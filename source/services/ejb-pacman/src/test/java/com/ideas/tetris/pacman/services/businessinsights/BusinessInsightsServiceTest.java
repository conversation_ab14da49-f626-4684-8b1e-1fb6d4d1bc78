package com.ideas.tetris.pacman.services.businessinsights;

import com.ideas.g3.test.AbstractG3JupiterTest;
import com.ideas.g3.test.category.SlowDBTest;
import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomClass;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomType;
import com.ideas.tetris.pacman.services.accommodation.entity.UniqueAccomClassCreator;
import com.ideas.tetris.pacman.services.accommodation.entity.UniqueAccomTypeCreator;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.IndividualTransactions;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.UniqueIndividualTransactionsCreator;
import com.ideas.tetris.pacman.services.businessgroup.service.UniqueMktSegCreator;
import com.ideas.tetris.pacman.services.businessinsights.criteria.BusinessInsightsCategory;
import com.ideas.tetris.pacman.services.businessinsights.criteria.BusinessInsightsCriteria;
import com.ideas.tetris.pacman.services.businessinsights.criteria.BusinessInsightsValue;
import com.ideas.tetris.pacman.services.businessinsights.entity.*;
import com.ideas.tetris.pacman.services.marketsegment.entity.MktSeg;
import com.ideas.tetris.pacman.services.marketsegment.entity.UniqueMktSegDetailsCreator;
import com.ideas.tetris.pacman.services.property.PropertyConfigParamService;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.http.ResponseEntity;

import javax.persistence.Tuple;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.text.ParseException;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;

import static java.lang.Boolean.FALSE;
import static java.lang.Boolean.TRUE;
import static java.time.temporal.ChronoUnit.DAYS;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@SlowDBTest
@MockitoSettings(strictness = Strictness.LENIENT)
public class BusinessInsightsServiceTest extends AbstractG3JupiterTest {

    private static final LocalDate NOW = LocalDate.now();

    private BusinessInsightsService service;

    private static final String INSERT_INTO_RESERVATION_NIGHT = "INSERT INTO [000005].[dbo].[Reservation_Night] (File_Metadata_ID,Property_ID,Reservation_Identifier," +
            "Individual_Status,Arrival_DT,Departure_DT,Booking_DT,Cancellation_DT,Booked_Accom_Type_Code,Accom_Type_ID,Mkt_Seg_ID,Room_Revenue,Food_Revenue,Beverage_Revenue,\" +\n" +
            "Telecom_Revenue,Other_Revenue,Total_Revenue,Source_Booking,Nationality,Rate_Code,Rate_Value,Room_Number," +
            "Booking_type,Number_Children,Number_Adults,CreateDate_DTTM,Confirmation_No,Channel,Booking_TM,Occupancy_DT, Persistent_Key, Analytics_Booking_Dt, Inv_Block_Code, Market_Code)";


    private LocalDate[] thisYear = {
            NOW.minusDays(45),
            NOW.minusDays(30),
            NOW.minusDays(20),
            NOW.minusDays(10),
            NOW.plusDays(5),
            NOW.plusDays(10)
    };

    private LocalDate[] lastYear = {
            NOW.minusWeeks(52).minusDays(45),
            NOW.minusWeeks(52).minusDays(30),
            NOW.minusWeeks(52).minusDays(20),
            NOW.minusWeeks(52).minusDays(10),
            NOW.minusWeeks(52).plusDays(5),
            NOW.minusWeeks(52).plusDays(10)
    };


    @Mock
    private PacmanConfigParamsService pacmanConfigParamsService;

    @Mock
    private PropertyConfigParamService propertyConfigParamService;

    @Mock
    private CrudService tenantCrudService;

    @BeforeEach
    public void setup() throws ParseException {
        service = new BusinessInsightsService();
        service.setEntityManager(tenantCrudService().getEntityManager());
        inject(service, "pacmanConfigParamsService", pacmanConfigParamsService);
        inject(service, "propertyConfigParamService", propertyConfigParamService);
        inject(service, "tenantCrudService", tenantCrudService);
        for (LocalDate date : thisYear) {
            UniqueIndividualTransactionsCreator.createIndividualTrans(DateUtil.convertJavaToJodaLocalDate(date), DateUtil.convertJavaToJodaLocalDate(date.minusDays(5)), "A", null);
        }
        DateUtil.convertJavaToJodaLocalDate(NOW);
        UniqueIndividualTransactionsCreator.createIndividualTrans(DateUtil.convertJavaToJodaLocalDate(NOW), DateUtil.convertJavaToJodaLocalDate(NOW).minusDays(5), "B", null);
        UniqueIndividualTransactionsCreator.createIndividualTrans(DateUtil.convertJavaToJodaLocalDate(NOW), DateUtil.convertJavaToJodaLocalDate(NOW).minusDays(5), "B", DateUtil.convertJavaToJodaLocalDate(NOW).minusDays(5), "XX");
        UniqueIndividualTransactionsCreator.createIndividualTrans(DateUtil.convertJavaToJodaLocalDate(NOW), DateUtil.convertJavaToJodaLocalDate(NOW).minusDays(5), "B", DateUtil.convertJavaToJodaLocalDate(NOW).minusDays(5), "NS");

        for (LocalDate date : lastYear) {
            UniqueIndividualTransactionsCreator.createIndividualTrans(DateUtil.convertJavaToJodaLocalDate(date), DateUtil.convertJavaToJodaLocalDate(date.minusDays(5)), "A", null);
        }
        UniqueIndividualTransactionsCreator.createIndividualTrans(DateUtil.convertJavaToJodaLocalDate(NOW).minusWeeks(52), DateUtil.convertJavaToJodaLocalDate(NOW).minusWeeks(52).minusDays(5), "B", null);
        UniqueIndividualTransactionsCreator.createIndividualTrans(DateUtil.convertJavaToJodaLocalDate(NOW).minusWeeks(52), DateUtil.convertJavaToJodaLocalDate(NOW).minusWeeks(52).minusDays(5), "B", DateUtil.convertJavaToJodaLocalDate(NOW).minusWeeks(52).minusDays(5), "XX");
        UniqueIndividualTransactionsCreator.createIndividualTrans(DateUtil.convertJavaToJodaLocalDate(NOW).minusWeeks(52), DateUtil.convertJavaToJodaLocalDate(NOW).minusWeeks(52).minusDays(5), "B", DateUtil.convertJavaToJodaLocalDate(NOW).minusWeeks(52).minusDays(5), "NS");
        tenantCrudService().findAll(IndividualTransactions.class);
    }

    @Test
    public void testGetTuples() {
        BusinessInsightsCriteria criteria = new BusinessInsightsCriteria();
        criteria.setCategory(BusinessInsightsCategory.OCCUPANCY_DATE);
        criteria.setStartDate(LocalDate.now().minusDays(90));
        criteria.setEndDate(LocalDate.now().plusDays(30));
        criteria.addValue(BusinessInsightsValue.ROOMS_SOLD);
        criteria.addValue(BusinessInsightsValue.ARRIVALS);
        criteria.addValue(BusinessInsightsValue.DEPARTURES);
        criteria.addValue(BusinessInsightsValue.CANCELLATIONS);
        criteria.addValue(BusinessInsightsValue.ROOM_REVENUE);

        List<Tuple> tuples = service.getTuples(criteria, false, service.getViewEntityClassBasedOnChannelCostVersion());

        assertInDateRange(criteria.getStartDate(), criteria.getEndDate(), BusinessInsightsValue.OCCUPANCY_DATE, tuples);
    }

    @Test
    public void testGetTuplesFilteredByRateCode() {
        BusinessInsightsCriteria criteria = new BusinessInsightsCriteria();
        criteria.setCategory(BusinessInsightsCategory.OCCUPANCY_DATE);
        criteria.setStartDate(LocalDate.now().minusDays(90));
        criteria.setEndDate(LocalDate.now().plusDays(30));
        criteria.addValue(BusinessInsightsValue.ROOMS_SOLD);
        criteria.addValue(BusinessInsightsValue.ARRIVALS);
        criteria.addValue(BusinessInsightsValue.DEPARTURES);
        criteria.addValue(BusinessInsightsValue.CANCELLATIONS);
        criteria.addValue(BusinessInsightsValue.ROOM_REVENUE);
        criteria.addValue(BusinessInsightsValue.RATE_CODE);
        criteria.addValue(BusinessInsightsValue.ACCOM_CLASS_CODE);
        criteria.addFilter(BusinessInsightsValue.RATE_CODE, Arrays.asList("A"));
        criteria.addFilter(BusinessInsightsValue.ACCOM_CLASS_CODE, Arrays.asList("DLX"));

        List<Tuple> tuples = service.getTuples(criteria, false,service.getViewEntityClassBasedOnChannelCostVersion());

        for (Tuple summary : tuples) {
            if (!summary.get(BusinessInsightsValue.RATE_CODE.getTableAlias()).equals("A")) {
                fail("Found wrong rate code: " + summary.get(BusinessInsightsValue.RATE_CODE.getField()));
            }
            if (!summary.get(BusinessInsightsValue.ACCOM_CLASS_CODE.getTableAlias()).equals("DLX")) {
                fail("Found wrong accom class code: " + summary.get(BusinessInsightsValue.ACCOM_CLASS_CODE.getField()));
            }
        }

        assertInDateRange(criteria.getStartDate(), criteria.getEndDate(), BusinessInsightsValue.OCCUPANCY_DATE, tuples);
    }

    @Test
    public void testGetTuplesHandleNullRateCodeAndValue() throws ParseException {
        buildIndividualTransactionDataForRateCodes();
        BusinessInsightsCriteria criteria = new BusinessInsightsCriteria();
        criteria.setCategory(BusinessInsightsCategory.RATE_CODE);
        criteria.setStartDate(NOW.minusDays(91));
        criteria.setEndDate(NOW.plusDays(31));
        criteria.addValue(BusinessInsightsValue.ROOMS_SOLD);
        BusinessInsightsData businessInsightsData = service.getBusinessInsightsData(criteria);
        List<String> resultRateCodes = businessInsightsData.getCategoryValues(false);
        List<String> expectedOrderOfRateCodes = Arrays.asList("", "A", "B", "TN", "TS", "VOR_FR", "VORAP_FR", "X1");
        assertEquals(expectedOrderOfRateCodes, resultRateCodes, "null Ratecode value should be handled and removed");
    }

    @Test
    void testGetTuplesForRateCode_WithUnderScoreCharacter() throws ParseException {
        createRateCodesTransaction("X1", "VOR_FR");
        createRateCodesTransaction("TS", "VORAP_FR");
        createRateCodesTransaction("TN", "VORAP_FR");
        createRateCodesTransaction("PROMO10_FR", "PROMO1_FR");
        BusinessInsightsCriteria criteria = new BusinessInsightsCriteria();
        criteria.setCategory(BusinessInsightsCategory.RATE_CODE);
        criteria.setStartDate(NOW.minusDays(91));
        criteria.setEndDate(NOW.plusDays(31));
        criteria.addFilter(BusinessInsightsValue.RATE_CODE, Arrays.asList("X1", "VOR_FR", "VORAP_FR", "TS", "TN", "PROMO10_FR", "PROMO1_FR"));
        criteria.addValue(BusinessInsightsValue.ROOMS_SOLD);
        BusinessInsightsData businessInsightsData = service.getBusinessInsightsData(criteria);
        List<String> expectedOrderOfRateCodes = Arrays.asList("PROMO1_FR", "PROMO10_FR", "TN", "TS", "VOR_FR", "VORAP_FR", "X1");
        assertEquals(expectedOrderOfRateCodes, businessInsightsData.getCategoryValues(false), "RateCode Order Should be right");
    }

    private void buildIndividualTransactionDataForRateCodes() throws ParseException {
        createRateCodesTransaction("X1", "VOR_FR");
        createRateCodesTransaction("TS", "VORAP_FR");
        createRateCodesTransaction("TN", "VORAP_FR");
    }

    private void createRateCodesTransaction(String rateCode, String rateCode1) throws ParseException {
        Random random = new Random(System.currentTimeMillis());

        LocalDate arrival = LocalDate.now().plusDays(random.nextInt(120) - 90);
        UniqueIndividualTransactionsCreator.createIndividualTrans(DateUtil.convertJavaToJodaLocalDate(arrival), DateUtil.convertJavaToJodaLocalDate(arrival.minusDays(random.nextInt(90) + 3)), rateCode, null);

        arrival = LocalDate.now().plusDays(random.nextInt(120) - 90);
        UniqueIndividualTransactionsCreator.createIndividualTrans(DateUtil.convertJavaToJodaLocalDate(arrival), DateUtil.convertJavaToJodaLocalDate(arrival.plusDays(1)), rateCode1, null);
    }

    @Test
    public void testGetTuplesForMarketSegments_WithUnderScoreCharacter() {
        buildIndividualTransactionDataForMarketSegments();
        BusinessInsightsCriteria criteria = new BusinessInsightsCriteria();
        criteria.setCategory(BusinessInsightsCategory.MARKET_SEGMENT_CODE);
        criteria.setStartDate(NOW.minusDays(91));
        criteria.setEndDate(NOW.plusDays(31));
        criteria.addFilter(BusinessInsightsValue.MARKET_SEGMENT_CODE, Arrays.asList("VOR_FR", "VORAP_FR", "VORAP", "none", "BAR", "bad"));
        criteria.addValue(BusinessInsightsValue.ROOMS_SOLD);
        BusinessInsightsData businessInsightsData = service.getBusinessInsightsData(criteria);
        List<String> marketSegmentsResultList = businessInsightsData.getCategoryValues(false);
        List<String> expectedOrderOfMarketSegments = Arrays.asList("bad", "BAR", "none", "VOR_FR", "VORAP", "VORAP_FR");
        List<Integer> expectedOrderOfRoomsSold = Arrays.asList(6, 5, 4, 1, 3, 2);
        assertEquals(expectedOrderOfMarketSegments, marketSegmentsResultList, "market Segments Order Should be right");
        assertEquals(expectedOrderOfRoomsSold.toString(), businessInsightsData.getValues().get(BusinessInsightsValue.ROOMS_SOLD).toString(), "corresponding rooms solds values for MS should also be same");
    }

    private void buildIndividualTransactionDataForMarketSegments() {
        AccomClass accomClass = UniqueAccomClassCreator.createUniqueAccomClassForPropertyID(5, 5);
        AccomType accomType = UniqueAccomTypeCreator.createUniqueAccomTypeForAccomClass(5, accomClass);
        createMarketSegmentsTransaction("VOR_FR", 5, 1, accomType);
        createMarketSegmentsTransaction("VORAP_FR", 5, 2, accomType);
        createMarketSegmentsTransaction("VORAP", 5, 3, accomType);
        createMarketSegmentsTransaction("none", 5, 4, accomType);
        createMarketSegmentsTransaction("BAR", 5, 5, accomType);
        createMarketSegmentsTransaction("bad", 5, 6, accomType);
    }

    private void createMarketSegmentsTransaction(String msName, int propertyId, int numberOfTransactions, AccomType accomType) {
        Random random = new Random(System.currentTimeMillis());
        LocalDate arrival = LocalDate.now().plusDays(random.nextInt(120) - 90);

        MktSeg mktSegBarAdr = UniqueMktSegCreator.createUniqueMktSegCreatorFor(propertyId, msName);
        UniqueMktSegDetailsCreator.createUniqueMktSegDetailsCreator(mktSegBarAdr, new Integer(1));
        for (int i = 0; i < numberOfTransactions; i++) {
            UniqueIndividualTransactionsCreator.buildIndividualTransactions(propertyId, BigDecimal.valueOf(10), mktSegBarAdr.getId(), DateUtil.convertLocalDateToJavaUtilDate(arrival),
                    DateUtil.convertLocalDateToJavaUtilDate(arrival.plusDays(1)), DateUtil.convertLocalDateToJavaUtilDate(arrival), null, "C", "SS", accomType.getId(), accomType.getAccomTypeCode());
        }
    }

    @Test
    public void testGetPossibleValuesForRateCode() {
        List<String> values = service.getPossibleValues(BusinessInsightsValue.RATE_CODE, String.class);

        assertNotNull(values);
        assertTrue(values.contains("A"));
        assertTrue(values.contains("B"));
    }

    @Test
    public void testGetPossibleValuesForRateCode_limitCriteriaByDates() {
        List<String> values = service.getPossibleValues(BusinessInsightsValue.RATE_CODE, String.class, NOW.minusDays(91), NOW.plusDays(31));

        assertTrue(values.contains("A"));
        assertTrue(values.contains("B"));
    }

    @Test
    public void testGetPossibleValuesForRateCode_withFilter() {
        BusinessInsightsCriteria criteria = new BusinessInsightsCriteria();
        criteria.setCategory(BusinessInsightsCategory.RATE_CODE);
        criteria.setStartDate(NOW.minusDays(91));
        criteria.setEndDate(NOW.plusDays(31));
        criteria.addFilter(BusinessInsightsValue.RATE_CODE, Collections.singletonList("A"));

        List<String> values = service.getPossibleValues(criteria, String.class);

        assertTrue(values.contains("A"));
        assertFalse(values.contains("B"));
    }

    @Test
    public void testGetPossibleValuesForDaysToArrival() {
        assertThrows(TetrisException.class, () -> {
            service.getPossibleValues(BusinessInsightsValue.DAYS_TO_ARRIVAL, Integer.class);
        });
    }

    @Test
    public void testGetArrivalsByOccupancyDate() {
        LocalDate startDate = LocalDate.now().minusDays(90);
        LocalDate endDate = LocalDate.now().plusDays(30);
        BusinessInsightsCriteria criteria = new BusinessInsightsCriteria();
        criteria.setCategory(BusinessInsightsCategory.OCCUPANCY_DATE);
        criteria.addValue(BusinessInsightsValue.ARRIVALS);
        criteria.addValue(BusinessInsightsValue.ARRIVALS_LAST_YEAR);
        criteria.addValue(BusinessInsightsValue.CANCELLATIONS);
        criteria.addValue(BusinessInsightsValue.CANCELLATIONS_LAST_YEAR);
        criteria.addValue(BusinessInsightsValue.ROOMS_SOLD);
        criteria.addValue(BusinessInsightsValue.ROOM_REVENUE);
        criteria.addValue(BusinessInsightsValue.TOTAL_REVENUE);
        criteria.addValue(BusinessInsightsValue.NET_REVENUE);
        criteria.addValue(BusinessInsightsValue.RATE_VALUE);
        criteria.addValue(BusinessInsightsValue.NET_RATE_VALUE);
        criteria.setStartDate(startDate);
        criteria.setEndDate(endDate);
        criteria.setAsOfDate(endDate);
        BusinessInsightsData data = service.getBusinessInsightsData(criteria);

        assertEquals(10, data.getValueTypes().size());

        int expectedDays = (int) (DAYS.between(startDate, endDate) + 1);

        List<Number> arrivals = data.getValueList(BusinessInsightsValue.ARRIVALS);
        assertEquals(expectedDays, arrivals.size());
        assertSumEquals(149, arrivals);

        List<Number> cancellations = data.getValueList(BusinessInsightsValue.CANCELLATIONS);
        assertEquals(expectedDays, cancellations.size());
        assertSumEquals(2, cancellations);

        List<Number> arrivalsLastYear = data.getValueList(BusinessInsightsValue.ARRIVALS_LAST_YEAR);
        assertEquals(expectedDays, arrivalsLastYear.size());
        assertSumEquals(7, arrivalsLastYear);

        List<Number> cancellationsLastYear = data.getValueList(BusinessInsightsValue.CANCELLATIONS_LAST_YEAR);
        assertEquals(expectedDays, cancellationsLastYear.size());
        assertSumEquals(2, cancellationsLastYear);

        List<Number> roomsSold = data.getValueList(BusinessInsightsValue.ROOMS_SOLD);
        assertEquals(expectedDays, roomsSold.size());
        assertSumEquals(149, roomsSold);

        List<Number> roomRevenue = data.getValueList(BusinessInsightsValue.ROOM_REVENUE);
        assertEquals(expectedDays, roomRevenue.size());
        assertSumEquals(10065, roomRevenue);

        List<Number> totalRevenue = data.getValueList(BusinessInsightsValue.TOTAL_REVENUE);
        assertEquals(expectedDays, totalRevenue.size());
        assertSumEquals(10765, totalRevenue);

        List<Number> netRevenue = data.getValueList(BusinessInsightsValue.NET_REVENUE);
        assertEquals(expectedDays, netRevenue.size());
        assertSumEquals(10065, netRevenue);

        List<Number> rateValue = data.getValueList(BusinessInsightsValue.RATE_VALUE);
        assertEquals(expectedDays, rateValue.size());
        assertSumEquals(2067, rateValue);

        List<Number> netRateValue = data.getValueList(BusinessInsightsValue.NET_RATE_VALUE);
        assertEquals(expectedDays, netRateValue.size());
        assertSumEquals(2067, netRateValue);

        List<Date> categoryList = (List<Date>) data.getCategoryValues(false);
        assertEquals(121, categoryList.size());
        LocalDate compareDate = startDate;
        for (Date occupancyDate : categoryList) {
            assertEquals(DateUtil.convertLocalDateToJavaUtilDate(compareDate), occupancyDate);
            compareDate = compareDate.plusDays(1);
        }
    }

    @Test
    public void testAllInclusiveValuesCalculationToggleOff() {
        BusinessInsightsValue.updateValuesForAllInclusive(0);

        LocalDate startDate = LocalDate.now().minusDays(90);
        LocalDate endDate = LocalDate.now().plusDays(30);
        BusinessInsightsCriteria criteria = new BusinessInsightsCriteria();
        criteria.setCategory(BusinessInsightsCategory.OCCUPANCY_DATE);
        criteria.addValue(BusinessInsightsValue.ROOMS_SOLD);
        criteria.addValue(BusinessInsightsValue.ROOM_REVENUE);
        criteria.addValue(BusinessInsightsValue.RATE_VALUE);
        criteria.addValue(BusinessInsightsValue.TOTAL_REVENUE);
        criteria.addValue(BusinessInsightsValue.ROOM_REVENUE_LAST_YEAR);
        criteria.addValue(BusinessInsightsValue.RATE_VALUE_LAST_YEAR);

        criteria.addValue(BusinessInsightsValue.AVG_DAILY_RATE);
        criteria.addValue(BusinessInsightsValue.AVG_DAILY_RATE_LAST_YEAR);
        criteria.addValue(BusinessInsightsValue.REVENUE_PER_AVAILABLE_ROOM);
        criteria.addValue(BusinessInsightsValue.REVENUE_PER_AVAILABLE_ROOM_LAST_YEAR);
        criteria.addValue(BusinessInsightsValue.OCCUPANCY_ON_BOOKS_LAST_YEAR);
        criteria.addValue(BusinessInsightsValue.CAPACITY);
        criteria.addValue(BusinessInsightsValue.CAPACITY_LAST_YEAR);

        criteria.setStartDate(startDate);
        criteria.setEndDate(endDate);
        criteria.setAsOfDate(endDate);
        BusinessInsightsData data = service.getBusinessInsightsData(criteria);

        assertEquals(13, data.getValueTypes().size());
        assertConstituentValue(data, BusinessInsightsValue.AVG_DAILY_RATE, BusinessInsightsValue.ROOM_REVENUE);
        assertConstituentValue(data, BusinessInsightsValue.AVG_DAILY_RATE_LAST_YEAR, BusinessInsightsValue.ROOM_REVENUE_LAST_YEAR);
        assertConstituentValue(data, BusinessInsightsValue.REVENUE_PER_AVAILABLE_ROOM, BusinessInsightsValue.ROOM_REVENUE);
        assertConstituentValue(data, BusinessInsightsValue.REVENUE_PER_AVAILABLE_ROOM_LAST_YEAR, BusinessInsightsValue.ROOM_REVENUE_LAST_YEAR);
    }

    @Test
    public void testAllInclusiveValuesCalculationToggleOn() {
        BusinessInsightsValue.updateValuesForAllInclusive(1);

        LocalDate startDate = LocalDate.now().minusDays(90);
        LocalDate endDate = LocalDate.now().plusDays(30);
        BusinessInsightsCriteria criteria = new BusinessInsightsCriteria();
        criteria.setCategory(BusinessInsightsCategory.OCCUPANCY_DATE);
        criteria.addValue(BusinessInsightsValue.ROOMS_SOLD);
        criteria.addValue(BusinessInsightsValue.ROOM_REVENUE);
        criteria.addValue(BusinessInsightsValue.RATE_VALUE);
        criteria.addValue(BusinessInsightsValue.TOTAL_REVENUE);
        criteria.addValue(BusinessInsightsValue.ROOM_REVENUE_LAST_YEAR);
        criteria.addValue(BusinessInsightsValue.RATE_VALUE_LAST_YEAR);

        criteria.addValue(BusinessInsightsValue.AVG_DAILY_RATE);
        criteria.addValue(BusinessInsightsValue.AVG_DAILY_RATE_LAST_YEAR);
        criteria.addValue(BusinessInsightsValue.REVENUE_PER_AVAILABLE_ROOM);
        criteria.addValue(BusinessInsightsValue.REVENUE_PER_AVAILABLE_ROOM_LAST_YEAR);
        criteria.addValue(BusinessInsightsValue.OCCUPANCY_ON_BOOKS_LAST_YEAR);
        criteria.addValue(BusinessInsightsValue.CAPACITY);
        criteria.addValue(BusinessInsightsValue.CAPACITY_LAST_YEAR);

        criteria.setStartDate(startDate);
        criteria.setEndDate(endDate);
        criteria.setAsOfDate(endDate);
        BusinessInsightsData data = service.getBusinessInsightsData(criteria);

        assertEquals(13, data.getValueTypes().size());
        assertConstituentValue(data, BusinessInsightsValue.AVG_DAILY_RATE, BusinessInsightsValue.RATE_VALUE);
        assertConstituentValue(data, BusinessInsightsValue.AVG_DAILY_RATE_LAST_YEAR, BusinessInsightsValue.RATE_VALUE_LAST_YEAR);
        assertConstituentValue(data, BusinessInsightsValue.REVENUE_PER_AVAILABLE_ROOM, BusinessInsightsValue.RATE_VALUE);
        assertConstituentValue(data, BusinessInsightsValue.REVENUE_PER_AVAILABLE_ROOM_LAST_YEAR, BusinessInsightsValue.RATE_VALUE_LAST_YEAR);
    }

    public void assertConstituentValue(BusinessInsightsData data, BusinessInsightsValue checkField, BusinessInsightsValue constituentValue1) {
        for (int i = 0; i < data.getCategoryValues(false).size(); i++) {
            if (data.getValueList(checkField).get(i) == null
                    || data.getValueList(constituentValue1).get(i) == null
                    || data.getValueList(checkField.getConstituentValue2()).get(i) == null) {
                continue;
            }
            double expected = data.getValueList(checkField).get(i).doubleValue();
            double constituentValueDouble = data.getValueList(constituentValue1).get(i).doubleValue();
            if (expected == 0) {
                assertEquals(0, constituentValueDouble);
            } else {
                Double actual = constituentValueDouble / data.getValueList(checkField.getConstituentValue2()).get(i).doubleValue();
                assertEquals(expected, new BigDecimal(actual.toString()).setScale(2, RoundingMode.HALF_UP).doubleValue());
            }
        }
        ;
    }

    @Test
    public void testGetArrivalsByOccupancyDateWithIncludeCancNoShowToggleON() {
        LocalDate startDate = LocalDate.now().minusDays(90);
        LocalDate endDate = LocalDate.now().plusDays(30);
        BusinessInsightsCriteria criteria = new BusinessInsightsCriteria();
        criteria.setCategory(BusinessInsightsCategory.OCCUPANCY_DATE);
        criteria.addValue(BusinessInsightsValue.ARRIVALS);
        criteria.addValue(BusinessInsightsValue.ARRIVALS_LAST_YEAR);
        criteria.addValue(BusinessInsightsValue.CANCELLATIONS);
        criteria.addValue(BusinessInsightsValue.CANCELLATIONS_LAST_YEAR);
        criteria.addValue(BusinessInsightsValue.ROOMS_SOLD);
        criteria.addValue(BusinessInsightsValue.ROOM_REVENUE);
        criteria.addValue(BusinessInsightsValue.TOTAL_REVENUE);
        criteria.addValue(BusinessInsightsValue.NET_REVENUE);
        criteria.addValue(BusinessInsightsValue.RATE_VALUE);
        criteria.addValue(BusinessInsightsValue.NET_RATE_VALUE);
        criteria.setStartDate(startDate);
        criteria.setEndDate(endDate);
        criteria.setAsOfDate(endDate);
        when(propertyConfigParamService.isIncludeCancellationNoShowRevenueEnabled()).thenReturn(TRUE);
        BusinessInsightsData data = service.getBusinessInsightsData(criteria);

        assertEquals(10, data.getValueTypes().size());

        int expectedDays = (int) (DAYS.between(startDate, endDate) + 1);

        List<Number> arrivals = data.getValueList(BusinessInsightsValue.ARRIVALS);
        assertEquals(expectedDays, arrivals.size());
        assertSumEquals(149, arrivals);

        List<Number> cancellations = data.getValueList(BusinessInsightsValue.CANCELLATIONS);
        assertEquals(expectedDays, cancellations.size());
        assertSumEquals(2, cancellations);

        List<Number> arrivalsLastYear = data.getValueList(BusinessInsightsValue.ARRIVALS_LAST_YEAR);
        assertEquals(expectedDays, arrivalsLastYear.size());
        assertSumEquals(7, arrivalsLastYear);

        List<Number> cancellationsLastYear = data.getValueList(BusinessInsightsValue.CANCELLATIONS_LAST_YEAR);
        assertEquals(expectedDays, cancellationsLastYear.size());
        assertSumEquals(2, cancellationsLastYear);

        List<Number> roomsSold = data.getValueList(BusinessInsightsValue.ROOMS_SOLD);
        assertEquals(expectedDays, roomsSold.size());
        assertSumEquals(149, roomsSold);

        List<Number> roomRevenue = data.getValueList(BusinessInsightsValue.ROOM_REVENUE);
        assertEquals(expectedDays, roomRevenue.size());
        assertSumEquals(10265, roomRevenue);

        List<Number> totalRevenue = data.getValueList(BusinessInsightsValue.TOTAL_REVENUE);
        assertEquals(expectedDays, totalRevenue.size());
        assertSumEquals(11165, totalRevenue);

        List<Number> netRevenue = data.getValueList(BusinessInsightsValue.NET_REVENUE);
        assertEquals(expectedDays, netRevenue.size());
        assertSumEquals(10265, netRevenue);

        List<Number> rateValue = data.getValueList(BusinessInsightsValue.RATE_VALUE);
        assertEquals(expectedDays, rateValue.size());
        assertSumEquals(2467, rateValue);

        List<Number> netRateValue = data.getValueList(BusinessInsightsValue.NET_RATE_VALUE);
        assertEquals(expectedDays, netRateValue.size());
        assertSumEquals(2467, netRateValue);

        List<Date> categoryList = (List<Date>) data.getCategoryValues(false);
        assertEquals(121, categoryList.size());
        LocalDate compareDate = startDate;
        for (Date occupancyDate : categoryList) {
            assertEquals(DateUtil.convertLocalDateToJavaUtilDate(compareDate), occupancyDate);
            compareDate = compareDate.plusDays(1);
        }
    }

    private void insertDayUseTransactions(Date date) {
        Map<String, Object> dateParameter = QueryParameter.with("arrivalDate", date).and("departureDate", date).parameters();
        tenantCrudService().executeUpdateByNativeQuery(INSERT_INTO_RESERVATION_NIGHT +
                "VALUES(1,5,7264078402,'CO',:arrivalDate,:departureDate,:arrivalDate,null,null,4,14,70,0,0,0,0,70,null,null,\n" +
                "'CHHUCG','30.00000',null,null,null,null,GETDATE(),null,'CHANNEL',null,:arrivalDate, null, null, null, null )", dateParameter);

        tenantCrudService().executeUpdateByNativeQuery(INSERT_INTO_RESERVATION_NIGHT +
                "VALUES(1,5,7264078403,'CO',:arrivalDate,:departureDate,:arrivalDate,:arrivalDate,null,4,14,70,0,0,0,0,70,null,null,\n" +
                "'CHHUCG','30.00000',null,null,null,null,GETDATE(),null,'CHANNEL',null,:arrivalDate, null, null, null, null )", dateParameter);
    }

    @Disabled
    @Test
    public void testWithDayUseReservations_WithCancelNoShowEnabled() {
        when(propertyConfigParamService.isIncludeCancellationNoShowRevenueEnabled()).thenReturn(TRUE);
        testDayUseWithOldChannelCost();
    }

    @Disabled
    @Test
    public void testWithDayUseReservations_WithCancelNoShowDisabled() {
        when(propertyConfigParamService.isIncludeCancellationNoShowRevenueEnabled()).thenReturn(FALSE);
        when(propertyConfigParamService.isDayUsePopulationEnabled()).thenReturn(TRUE);
        testDayUseWithOldChannelCost();
    }

    @Disabled
    @Test
    public void testWithDayUseReservationsWithSimplifiedCC_WithCancelNoShowEnabled() {
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.SIMPLIFIED_CHANNEL_COST_ENABLED)).thenReturn(TRUE);
        when(propertyConfigParamService.isIncludeCancellationNoShowRevenueEnabled()).thenReturn(TRUE);
        testWithSimplifiedCCEnabled();
    }

    @Disabled
    @Test
    public void testWithDayUseReservationsWithSimplifiedCC_WithCancelNoShowDisabled() {
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.SIMPLIFIED_CHANNEL_COST_ENABLED)).thenReturn(TRUE);
        when(propertyConfigParamService.isIncludeCancellationNoShowRevenueEnabled()).thenReturn(FALSE);
        when(propertyConfigParamService.isDayUsePopulationEnabled()).thenReturn(TRUE);
        testWithSimplifiedCCEnabled();
    }

    @Disabled
    @Test
    public void testWithDayUseReservationsWithChannelCost_WithCancelNoShowEnabled() {
        when(propertyConfigParamService.isIncludeCancellationNoShowRevenueEnabled()).thenReturn(TRUE);
        testDayUseWithChannelCost();
    }

    @Disabled
    @Test
    public void testWithDayUseReservationsWithChannelCost_WithCancelNoShowDisabledbled() {
        when(propertyConfigParamService.isIncludeCancellationNoShowRevenueEnabled()).thenReturn(FALSE);
        when(propertyConfigParamService.isDayUsePopulationEnabled()).thenReturn(TRUE);
        testDayUseWithChannelCost();
    }

    private void testDayUseWithOldChannelCost() {
        tenantCrudService().executeUpdateByNativeQuery("truncate table Reservation_Night ");
        LocalDate date = LocalDate.parse("2020-12-12");
        insertDayUseTransactions(DateUtil.convertLocalDateToJavaUtilDate(date));

        BusinessInsightsCriteria criteria = new BusinessInsightsCriteria();
        criteria.setCategory(BusinessInsightsCategory.OCCUPANCY_DATE);
        criteria.addValue(BusinessInsightsValue.ARRIVALS);
        criteria.addValue(BusinessInsightsValue.ARRIVALS_LAST_YEAR);
        criteria.addValue(BusinessInsightsValue.CANCELLATIONS);
        criteria.addValue(BusinessInsightsValue.CANCELLATIONS_LAST_YEAR);
        criteria.addValue(BusinessInsightsValue.ROOMS_SOLD);
        criteria.addValue(BusinessInsightsValue.ROOM_REVENUE);
        criteria.addValue(BusinessInsightsValue.TOTAL_REVENUE);
        criteria.addValue(BusinessInsightsValue.NET_REVENUE);
        criteria.addValue(BusinessInsightsValue.RATE_VALUE);
        criteria.addValue(BusinessInsightsValue.NET_RATE_VALUE);
        criteria.setStartDate(date);
        criteria.setEndDate(date);
        criteria.setAsOfDate(date);

        BusinessInsightsData data = service.getBusinessInsightsData(criteria);
        assertEquals(10, data.getValueTypes().size());
        int expectedDays = 1;

        List<Number> arrivals = data.getValueList(BusinessInsightsValue.ARRIVALS);
        assertEquals(expectedDays, arrivals.size());
        assertSumEquals(0, arrivals);

        List<Number> cancellations = data.getValueList(BusinessInsightsValue.CANCELLATIONS);
        assertEquals(expectedDays, cancellations.size());
        assertSumEquals(0, cancellations);

        List<Number> arrivalsLastYear = data.getValueList(BusinessInsightsValue.ARRIVALS_LAST_YEAR);
        assertEquals(expectedDays, arrivalsLastYear.size());
        assertSumEquals(0, arrivalsLastYear);

        List<Number> cancellationsLastYear = data.getValueList(BusinessInsightsValue.CANCELLATIONS_LAST_YEAR);
        assertEquals(expectedDays, cancellationsLastYear.size());
        assertSumEquals(0, cancellationsLastYear);

        List<Number> roomsSold = data.getValueList(BusinessInsightsValue.ROOMS_SOLD);
        assertEquals(expectedDays, roomsSold.size());
        assertSumEquals(0, roomsSold);

        List<Number> roomRevenue = data.getValueList(BusinessInsightsValue.ROOM_REVENUE);
        assertEquals(expectedDays, roomRevenue.size());
        assertSumEquals(140, roomRevenue);

        List<Number> totalRevenue = data.getValueList(BusinessInsightsValue.TOTAL_REVENUE);
        assertEquals(expectedDays, totalRevenue.size());
        assertSumEquals(140, totalRevenue);

        List<Number> netRevenue = data.getValueList(BusinessInsightsValue.NET_REVENUE);
        assertEquals(expectedDays, netRevenue.size());
        assertSumEquals(140, netRevenue);

        List<Number> rateValue = data.getValueList(BusinessInsightsValue.RATE_VALUE);
        assertEquals(expectedDays, rateValue.size());
        assertSumEquals(60, rateValue);

        List<Number> netRateValue = data.getValueList(BusinessInsightsValue.NET_RATE_VALUE);
        assertEquals(expectedDays, netRateValue.size());
        assertSumEquals(60, netRateValue);
    }


    private void testWithSimplifiedCCEnabled() {
        tenantCrudService().executeUpdateByNativeQuery("truncate table Reservation_Night ");
        LocalDate date = LocalDate.parse("2020-12-12");
        insertDayUseTransactions(DateUtil.convertLocalDateToJavaUtilDate(date));
        BusinessInsightsCriteria criteria = new BusinessInsightsCriteria();
        criteria.setCategory(BusinessInsightsCategory.OCCUPANCY_DATE);
        criteria.addValue(BusinessInsightsValue.ARRIVALS);
        criteria.addValue(BusinessInsightsValue.ARRIVALS_LAST_YEAR);
        criteria.addValue(BusinessInsightsValue.CANCELLATIONS);
        criteria.addValue(BusinessInsightsValue.CANCELLATIONS_LAST_YEAR);
        criteria.addValue(BusinessInsightsValue.ROOMS_SOLD);
        criteria.addValue(BusinessInsightsValue.ROOM_REVENUE);
        criteria.addValue(BusinessInsightsValue.TOTAL_REVENUE);
        criteria.addValue(BusinessInsightsValue.NET_REVENUE);
        criteria.addValue(BusinessInsightsValue.RATE_VALUE);
        criteria.addValue(BusinessInsightsValue.NET_RATE_VALUE);
        criteria.setStartDate(date);
        criteria.setEndDate(date);
        criteria.setAsOfDate(date);

        BusinessInsightsData data = service.getBusinessInsightsData(criteria);

        assertEquals(10, data.getValueTypes().size());

        int expectedDays = 1;

        List<Number> arrivals = data.getValueList(BusinessInsightsValue.ARRIVALS);
        assertEquals(expectedDays, arrivals.size());
        assertSumEquals(0, arrivals);

        List<Number> cancellations = data.getValueList(BusinessInsightsValue.CANCELLATIONS);
        assertEquals(expectedDays, cancellations.size());
        assertSumEquals(0, cancellations);

        List<Number> arrivalsLastYear = data.getValueList(BusinessInsightsValue.ARRIVALS_LAST_YEAR);
        assertEquals(expectedDays, arrivalsLastYear.size());
        assertSumEquals(0, arrivalsLastYear);

        List<Number> cancellationsLastYear = data.getValueList(BusinessInsightsValue.CANCELLATIONS_LAST_YEAR);
        assertEquals(expectedDays, cancellationsLastYear.size());
        assertSumEquals(0, cancellationsLastYear);

        List<Number> roomsSold = data.getValueList(BusinessInsightsValue.ROOMS_SOLD);
        assertEquals(expectedDays, roomsSold.size());
        assertSumEquals(0, roomsSold);

        List<Number> roomRevenue = data.getValueList(BusinessInsightsValue.ROOM_REVENUE);
        assertEquals(expectedDays, roomRevenue.size());
        assertSumEquals(140, roomRevenue);

        List<Number> totalRevenue = data.getValueList(BusinessInsightsValue.TOTAL_REVENUE);
        assertEquals(expectedDays, totalRevenue.size());
        assertSumEquals(140, totalRevenue);

        List<Number> netRevenue = data.getValueList(BusinessInsightsValue.NET_REVENUE);
        assertEquals(expectedDays, netRevenue.size());
        assertSumEquals(140, netRevenue);

        List<Number> rateValue = data.getValueList(BusinessInsightsValue.RATE_VALUE);
        assertEquals(expectedDays, rateValue.size());
        assertSumEquals(60, rateValue);

        List<Number> netRateValue = data.getValueList(BusinessInsightsValue.NET_RATE_VALUE);
        assertEquals(expectedDays, netRateValue.size());
        assertSumEquals(60, netRateValue);
    }


    private void testDayUseWithChannelCost() {
        tenantCrudService().executeUpdateByNativeQuery("truncate table Reservation_Night ");
        LocalDate date = LocalDate.parse("2020-12-12");
        insertDayUseTransactions(DateUtil.convertLocalDateToJavaUtilDate(date));
        addChannelCost();

        BusinessInsightsCriteria criteria = new BusinessInsightsCriteria();
        criteria.setCategory(BusinessInsightsCategory.OCCUPANCY_DATE);
        criteria.addValue(BusinessInsightsValue.ARRIVALS);
        criteria.addValue(BusinessInsightsValue.ARRIVALS_LAST_YEAR);
        criteria.addValue(BusinessInsightsValue.CANCELLATIONS);
        criteria.addValue(BusinessInsightsValue.CANCELLATIONS_LAST_YEAR);
        criteria.addValue(BusinessInsightsValue.ROOMS_SOLD);
        criteria.addValue(BusinessInsightsValue.ROOM_REVENUE);
        criteria.addValue(BusinessInsightsValue.TOTAL_REVENUE);
        criteria.addValue(BusinessInsightsValue.NET_REVENUE);
        criteria.addValue(BusinessInsightsValue.RATE_VALUE);
        criteria.addValue(BusinessInsightsValue.NET_RATE_VALUE);
        criteria.addValue(BusinessInsightsValue.TOTAL_ACQUISITION_COST);
        criteria.setStartDate(date);
        criteria.setEndDate(date);
        criteria.setAsOfDate(date);

        BusinessInsightsData data = service.getBusinessInsightsData(criteria);

        assertEquals(11, data.getValueTypes().size());

        int expectedDays = 1;

        List<Number> arrivals = data.getValueList(BusinessInsightsValue.ARRIVALS);
        assertEquals(expectedDays, arrivals.size());
        assertSumEquals(0, arrivals);

        List<Number> cancellations = data.getValueList(BusinessInsightsValue.CANCELLATIONS);
        assertEquals(expectedDays, cancellations.size());
        assertSumEquals(0, cancellations);

        List<Number> arrivalsLastYear = data.getValueList(BusinessInsightsValue.ARRIVALS_LAST_YEAR);
        assertEquals(expectedDays, arrivalsLastYear.size());
        assertSumEquals(0, arrivalsLastYear);

        List<Number> cancellationsLastYear = data.getValueList(BusinessInsightsValue.CANCELLATIONS_LAST_YEAR);
        assertEquals(expectedDays, cancellationsLastYear.size());
        assertSumEquals(0, cancellationsLastYear);

        List<Number> roomsSold = data.getValueList(BusinessInsightsValue.ROOMS_SOLD);
        assertEquals(expectedDays, roomsSold.size());
        assertSumEquals(0, roomsSold);

        List<Number> roomRevenue = data.getValueList(BusinessInsightsValue.ROOM_REVENUE);
        assertEquals(expectedDays, roomRevenue.size());
        assertSumEquals(140, roomRevenue);

        List<Number> totalRevenue = data.getValueList(BusinessInsightsValue.TOTAL_REVENUE);
        assertEquals(expectedDays, totalRevenue.size());
        assertSumEquals(140, totalRevenue);

        List<Number> netRevenue = data.getValueList(BusinessInsightsValue.NET_REVENUE);
        assertEquals(expectedDays, netRevenue.size());
        assertSumEquals(120, netRevenue);

        List<Number> rateValue = data.getValueList(BusinessInsightsValue.RATE_VALUE);
        assertEquals(expectedDays, rateValue.size());
        assertSumEquals(60, rateValue);

        List<Number> netRateValue = data.getValueList(BusinessInsightsValue.NET_RATE_VALUE);
        assertEquals(expectedDays, netRateValue.size());
        assertSumEquals(40, netRateValue);

        List<Number> totalAcquisitonCost = data.getValueList(BusinessInsightsValue.TOTAL_ACQUISITION_COST);
        assertEquals(expectedDays, totalAcquisitonCost.size());
        assertSumEquals(20, totalAcquisitonCost);
    }

    private void addChannelCost() {
        tenantCrudService().executeUpdateByNativeQuery("update property set channel_cost_source = 'CHANNEL' ");
        tenantCrudService().executeUpdateByNativeQuery("insert into channel_cost values ('CHANNEL', 10, 0, 'test', 11403, '2020-12-12', 11403, '2020-12-12' ) ");
    }


    @Test
    public void testGetArrivalsByOccupancyDateWithIncludeCancNoShowToggleONWhenArrivalDtNotEqToOccDt() {
        when(propertyConfigParamService.isIncludeCancellationNoShowRevenueEnabled()).thenReturn(TRUE);
        LocalDate date = LocalDate.now();
        LocalDate startDate = date.plusDays(2);
        LocalDate endDate = date.plusDays(2);
        tenantCrudService().executeUpdateByNativeQuery("update Reservation_Night set Occupancy_Dt = '" + date.plusDays(2) + "', Arrival_Dt = '" + date.plusDays(2) + "', Departure_Dt = '" + date.plusDays(4) + "' where individual_trans_id = 1");
        tenantCrudService().executeUpdateByNativeQuery("update Reservation_Night set Occupancy_Dt = '" + date.plusDays(3) + "', Arrival_Dt = '" + date.plusDays(2) + "', Departure_Dt = '" + date.plusDays(4) + "' where individual_trans_id = 2");
        BusinessInsightsCriteria criteria = new BusinessInsightsCriteria();
        criteria.setCategory(BusinessInsightsCategory.OCCUPANCY_DATE);
        criteria.addValue(BusinessInsightsValue.ARRIVALS);
        criteria.addValue(BusinessInsightsValue.CANCELLATIONS);
        criteria.addValue(BusinessInsightsValue.ROOMS_SOLD);
        criteria.addValue(BusinessInsightsValue.ROOM_REVENUE);
        criteria.addValue(BusinessInsightsValue.TOTAL_REVENUE);
        criteria.setStartDate(startDate);
        criteria.setEndDate(endDate);
        BusinessInsightsData day1Data = service.getBusinessInsightsData(criteria);
        assertEquals(5, day1Data.getValueTypes().size());
        int expectedDays1 = (int) (DAYS.between(startDate, endDate) + 1);
        List<Number> arrivals = day1Data.getValueList(BusinessInsightsValue.ARRIVALS);
        assertEquals(expectedDays1, arrivals.size());
        assertSumEquals(0, arrivals);
        List<Number> cancellations = day1Data.getValueList(BusinessInsightsValue.CANCELLATIONS);
        assertEquals(expectedDays1, cancellations.size());
        assertSumEquals(1, cancellations);
        List<Number> roomsSold = day1Data.getValueList(BusinessInsightsValue.ROOMS_SOLD);
        assertEquals(expectedDays1, roomsSold.size());
        assertSumEquals(0, roomsSold);
        List<Number> roomRevenue = day1Data.getValueList(BusinessInsightsValue.ROOM_REVENUE);
        assertEquals(expectedDays1, roomRevenue.size());
        assertSumEquals(50, roomRevenue);
        List<Number> totalRevenue = day1Data.getValueList(BusinessInsightsValue.TOTAL_REVENUE);
        assertEquals(expectedDays1, totalRevenue.size());
        assertSumEquals(100, totalRevenue);

        LocalDate startDate2 = date.plusDays(3);
        LocalDate endDate2 = date.plusDays(3);
        criteria.setStartDate(startDate2);
        criteria.setEndDate(endDate2);
        BusinessInsightsData day2Data = service.getBusinessInsightsData(criteria);
        assertEquals(5, day2Data.getValueTypes().size());
        int expectedDaysDay2 = (int) (DAYS.between(startDate2, endDate2) + 1);
        List<Number> arrivals2 = day2Data.getValueList(BusinessInsightsValue.ARRIVALS);
        assertEquals(expectedDaysDay2, arrivals2.size());
        assertSumEquals(0, arrivals2);
        List<Number> cancellations2 = day2Data.getValueList(BusinessInsightsValue.CANCELLATIONS);
        assertEquals(expectedDaysDay2, cancellations2.size());
        assertSumEquals(0, cancellations2);
        List<Number> roomsSold2 = day2Data.getValueList(BusinessInsightsValue.ROOMS_SOLD);
        assertEquals(expectedDaysDay2, roomsSold2.size());
        assertSumEquals(0, roomsSold2);
        List<Number> roomRevenue2 = day2Data.getValueList(BusinessInsightsValue.ROOM_REVENUE);
        assertEquals(expectedDaysDay2, roomRevenue2.size());
        assertSumEquals(50, roomRevenue2);
        List<Number> totalRevenue2 = day2Data.getValueList(BusinessInsightsValue.TOTAL_REVENUE);
        assertEquals(expectedDaysDay2, totalRevenue2.size());
        assertSumEquals(100, totalRevenue2);

        LocalDate startDate3 = date.plusDays(2);
        LocalDate endDate3 = date.plusDays(3);
        criteria.setStartDate(startDate3);
        criteria.setEndDate(endDate3);
        BusinessInsightsData day1and2Data = service.getBusinessInsightsData(criteria);
        assertEquals(5, day1and2Data.getValueTypes().size());
        int expectedDays = (int) (DAYS.between(startDate3, endDate3) + 1);
        List<Number> arrivals3 = day1and2Data.getValueList(BusinessInsightsValue.ARRIVALS);
        assertEquals(expectedDays, arrivals3.size());
        assertSumEquals(0, arrivals3);
        List<Number> cancellations3 = day1and2Data.getValueList(BusinessInsightsValue.CANCELLATIONS);
        assertEquals(expectedDays, cancellations3.size());
        assertSumEquals(1, cancellations3);
        List<Number> roomsSold3 = day1and2Data.getValueList(BusinessInsightsValue.ROOMS_SOLD);
        assertEquals(expectedDays, roomsSold3.size());
        assertSumEquals(0, roomsSold3);
        List<Number> roomRevenue3 = day1and2Data.getValueList(BusinessInsightsValue.ROOM_REVENUE);
        assertEquals(expectedDays, roomRevenue3.size());
        assertSumEquals(100, roomRevenue3);
        List<Number> totalRevenue3 = day1and2Data.getValueList(BusinessInsightsValue.TOTAL_REVENUE);
        assertEquals(expectedDays, totalRevenue3.size());
        assertSumEquals(200, totalRevenue3);
    }

    @Test
    public void testGetArrivalsByOccupancyDateWithIncludeCancNoShowToggleONAndSimplifiedCCIsONWhenArrivalDtNotEqToOccDt() {
        when(propertyConfigParamService.isIncludeCancellationNoShowRevenueEnabled()).thenReturn(TRUE);
        lenient().when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.SIMPLIFIED_CHANNEL_COST_ENABLED)).thenReturn(TRUE);
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.POST_DEPARTURE_REVENUE_ADJUSTMENT_ENABLED)).thenReturn(FALSE);
        LocalDate date = LocalDate.now();
        LocalDate startDate = date.plusDays(2);
        LocalDate endDate = date.plusDays(2);
        tenantCrudService().executeUpdateByNativeQuery("update Reservation_Night set Occupancy_Dt = '" + date.plusDays(2) + "', Arrival_Dt = '" + date.plusDays(2) + "', Departure_Dt = '" + date.plusDays(4) + "' where individual_trans_id = 1");
        tenantCrudService().executeUpdateByNativeQuery("update Reservation_Night set Occupancy_Dt = '" + date.plusDays(3) + "', Arrival_Dt = '" + date.plusDays(2) + "', Departure_Dt = '" + date.plusDays(4) + "' where individual_trans_id = 2");
        BusinessInsightsCriteria criteria = new BusinessInsightsCriteria();
        criteria.setCategory(BusinessInsightsCategory.OCCUPANCY_DATE);
        criteria.addValue(BusinessInsightsValue.ARRIVALS);
        criteria.addValue(BusinessInsightsValue.CANCELLATIONS);
        criteria.addValue(BusinessInsightsValue.ROOMS_SOLD);
        criteria.addValue(BusinessInsightsValue.ROOM_REVENUE);
        criteria.addValue(BusinessInsightsValue.TOTAL_REVENUE);
        criteria.setStartDate(startDate);
        criteria.setEndDate(endDate);
        BusinessInsightsData day1Data = service.getBusinessInsightsData(criteria);
        assertEquals(5, day1Data.getValueTypes().size());
        int expectedDays1 = (int) (DAYS.between(startDate, endDate) + 1);
        List<Number> arrivals = day1Data.getValueList(BusinessInsightsValue.ARRIVALS);
        assertEquals(expectedDays1, arrivals.size());
        assertSumEquals(0, arrivals);
        List<Number> cancellations = day1Data.getValueList(BusinessInsightsValue.CANCELLATIONS);
        assertEquals(expectedDays1, cancellations.size());
        assertSumEquals(1, cancellations);
        List<Number> roomsSold = day1Data.getValueList(BusinessInsightsValue.ROOMS_SOLD);
        assertEquals(expectedDays1, roomsSold.size());
        assertSumEquals(0, roomsSold);
        List<Number> roomRevenue = day1Data.getValueList(BusinessInsightsValue.ROOM_REVENUE);
        assertEquals(expectedDays1, roomRevenue.size());
        assertSumEquals(50, roomRevenue);
        List<Number> totalRevenue = day1Data.getValueList(BusinessInsightsValue.TOTAL_REVENUE);
        assertEquals(expectedDays1, totalRevenue.size());
        assertSumEquals(100, totalRevenue);

        LocalDate startDate2 = date.plusDays(3);
        LocalDate endDate2 = date.plusDays(3);
        criteria.setStartDate(startDate2);
        criteria.setEndDate(endDate2);
        BusinessInsightsData day2Data = service.getBusinessInsightsData(criteria);
        assertEquals(5, day2Data.getValueTypes().size());
        int expectedDaysDay2 = (int) (DAYS.between(startDate2, endDate2) + 1);
        List<Number> arrivals2 = day2Data.getValueList(BusinessInsightsValue.ARRIVALS);
        assertEquals(expectedDaysDay2, arrivals2.size());
        assertSumEquals(0, arrivals2);
        List<Number> cancellations2 = day2Data.getValueList(BusinessInsightsValue.CANCELLATIONS);
        assertEquals(expectedDaysDay2, cancellations2.size());
        assertSumEquals(0, cancellations2);
        List<Number> roomsSold2 = day2Data.getValueList(BusinessInsightsValue.ROOMS_SOLD);
        assertEquals(expectedDaysDay2, roomsSold2.size());
        assertSumEquals(0, roomsSold2);
        List<Number> roomRevenue2 = day2Data.getValueList(BusinessInsightsValue.ROOM_REVENUE);
        assertEquals(expectedDaysDay2, roomRevenue2.size());
        assertSumEquals(50, roomRevenue2);
        List<Number> totalRevenue2 = day2Data.getValueList(BusinessInsightsValue.TOTAL_REVENUE);
        assertEquals(expectedDaysDay2, totalRevenue2.size());
        assertSumEquals(100, totalRevenue2);

        LocalDate startDate3 = date.plusDays(2);
        LocalDate endDate3 = date.plusDays(3);
        criteria.setStartDate(startDate3);
        criteria.setEndDate(endDate3);
        BusinessInsightsData day1and2Data = service.getBusinessInsightsData(criteria);
        assertEquals(5, day1and2Data.getValueTypes().size());
        int expectedDays = (int) (DAYS.between(startDate3, endDate3) + 1);
        List<Number> arrivals3 = day1and2Data.getValueList(BusinessInsightsValue.ARRIVALS);
        assertEquals(expectedDays, arrivals3.size());
        assertSumEquals(0, arrivals3);
        List<Number> cancellations3 = day1and2Data.getValueList(BusinessInsightsValue.CANCELLATIONS);
        assertEquals(expectedDays, cancellations3.size());
        assertSumEquals(1, cancellations3);
        List<Number> roomsSold3 = day1and2Data.getValueList(BusinessInsightsValue.ROOMS_SOLD);
        assertEquals(expectedDays, roomsSold3.size());
        assertSumEquals(0, roomsSold3);
        List<Number> roomRevenue3 = day1and2Data.getValueList(BusinessInsightsValue.ROOM_REVENUE);
        assertEquals(expectedDays, roomRevenue3.size());
        assertSumEquals(100, roomRevenue3);
        List<Number> totalRevenue3 = day1and2Data.getValueList(BusinessInsightsValue.TOTAL_REVENUE);
        assertEquals(expectedDays, totalRevenue3.size());
        assertSumEquals(200, totalRevenue3);
    }

    @Test
    public void testGetArrivalsByOccupancyDateWithIncludeCancNoShowToggleIsOFFAndSimplifiedCCIsON() {
        LocalDate startDate = LocalDate.now().minusDays(90);
        LocalDate endDate = LocalDate.now().plusDays(30);
        BusinessInsightsCriteria criteria = new BusinessInsightsCriteria();
        criteria.setCategory(BusinessInsightsCategory.OCCUPANCY_DATE);
        criteria.addValue(BusinessInsightsValue.ARRIVALS);
        criteria.addValue(BusinessInsightsValue.ARRIVALS_LAST_YEAR);
        criteria.addValue(BusinessInsightsValue.CANCELLATIONS);
        criteria.addValue(BusinessInsightsValue.CANCELLATIONS_LAST_YEAR);
        criteria.addValue(BusinessInsightsValue.ROOMS_SOLD);
        criteria.addValue(BusinessInsightsValue.ROOM_REVENUE);
        criteria.addValue(BusinessInsightsValue.TOTAL_REVENUE);
        criteria.addValue(BusinessInsightsValue.NET_REVENUE);
        criteria.addValue(BusinessInsightsValue.RATE_VALUE);
        criteria.addValue(BusinessInsightsValue.NET_RATE_VALUE);
        criteria.setStartDate(startDate);
        criteria.setEndDate(endDate);
        criteria.setAsOfDate(endDate);
        lenient().when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.SIMPLIFIED_CHANNEL_COST_ENABLED)).thenReturn(TRUE);
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.POST_DEPARTURE_REVENUE_ADJUSTMENT_ENABLED)).thenReturn(FALSE);
        BusinessInsightsData data = service.getBusinessInsightsData(criteria);

        assertEquals(10, data.getValueTypes().size());

        int expectedDays = (int) (DAYS.between(startDate, endDate) + 1);

        List<Number> arrivals = data.getValueList(BusinessInsightsValue.ARRIVALS);
        assertEquals(expectedDays, arrivals.size());
        assertSumEquals(142, arrivals);

        List<Number> cancellations = data.getValueList(BusinessInsightsValue.CANCELLATIONS);
        assertEquals(expectedDays, cancellations.size());
        assertSumEquals(2, cancellations);

        List<Number> arrivalsLastYear = data.getValueList(BusinessInsightsValue.ARRIVALS_LAST_YEAR);
        assertEquals(expectedDays, arrivalsLastYear.size());
        assertSumEquals(0, arrivalsLastYear);

        List<Number> cancellationsLastYear = data.getValueList(BusinessInsightsValue.CANCELLATIONS_LAST_YEAR);
        assertEquals(expectedDays, cancellationsLastYear.size());
        assertSumEquals(2, cancellationsLastYear);

        List<Number> roomsSold = data.getValueList(BusinessInsightsValue.ROOMS_SOLD);
        assertEquals(expectedDays, roomsSold.size());
        assertSumEquals(142, roomsSold);

        List<Number> roomRevenue = data.getValueList(BusinessInsightsValue.ROOM_REVENUE);
        assertEquals(expectedDays, roomRevenue.size());
        assertSumEquals(9365, roomRevenue);

        List<Number> totalRevenue = data.getValueList(BusinessInsightsValue.TOTAL_REVENUE);
        assertEquals(expectedDays, totalRevenue.size());
        assertSumEquals(9365, totalRevenue);

        List<Number> netRevenue = data.getValueList(BusinessInsightsValue.NET_REVENUE);
        assertEquals(expectedDays, netRevenue.size());
        assertSumEquals(9365, netRevenue);

        List<Number> rateValue = data.getValueList(BusinessInsightsValue.RATE_VALUE);
        assertEquals(expectedDays, rateValue.size());
        assertSumEquals(667, rateValue);

        List<Number> netRateValue = data.getValueList(BusinessInsightsValue.NET_RATE_VALUE);
        assertEquals(expectedDays, netRateValue.size());
        assertSumEquals(667, netRateValue);

        List<Date> categoryList = (List<Date>) data.getCategoryValues(false);
        assertEquals(121, categoryList.size());
        LocalDate compareDate = startDate;
        for (Date occupancyDate : categoryList) {
            assertEquals(DateUtil.convertLocalDateToJavaUtilDate(compareDate), occupancyDate);
            compareDate = compareDate.plusDays(1);
        }
    }

    @Test
    public void testGetArrivalsByOccupancyDateWithIncludeCancNoShowToggleAndSimplifiedCCIsON() {
        LocalDate startDate = LocalDate.now().minusDays(90);
        LocalDate endDate = LocalDate.now().plusDays(30);
        BusinessInsightsCriteria criteria = new BusinessInsightsCriteria();
        criteria.setCategory(BusinessInsightsCategory.OCCUPANCY_DATE);
        criteria.addValue(BusinessInsightsValue.ARRIVALS);
        criteria.addValue(BusinessInsightsValue.ARRIVALS_LAST_YEAR);
        criteria.addValue(BusinessInsightsValue.CANCELLATIONS);
        criteria.addValue(BusinessInsightsValue.CANCELLATIONS_LAST_YEAR);
        criteria.addValue(BusinessInsightsValue.ROOMS_SOLD);
        criteria.addValue(BusinessInsightsValue.ROOM_REVENUE);
        criteria.addValue(BusinessInsightsValue.TOTAL_REVENUE);
        criteria.addValue(BusinessInsightsValue.NET_REVENUE);
        criteria.addValue(BusinessInsightsValue.RATE_VALUE);
        criteria.addValue(BusinessInsightsValue.NET_RATE_VALUE);
        criteria.setStartDate(startDate);
        criteria.setEndDate(endDate);
        criteria.setAsOfDate(endDate);
        lenient().when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.SIMPLIFIED_CHANNEL_COST_ENABLED)).thenReturn(TRUE);
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.POST_DEPARTURE_REVENUE_ADJUSTMENT_ENABLED)).thenReturn(FALSE);
        when(propertyConfigParamService.isIncludeCancellationNoShowRevenueEnabled()).thenReturn(TRUE);
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.POST_DEPARTURE_REVENUE_ADJUSTMENT_ENABLED)).thenReturn(FALSE);
        BusinessInsightsData data = service.getBusinessInsightsData(criteria);

        assertEquals(10, data.getValueTypes().size());

        int expectedDays = (int) (DAYS.between(startDate, endDate) + 1);

        List<Number> arrivals = data.getValueList(BusinessInsightsValue.ARRIVALS);
        assertEquals(expectedDays, arrivals.size());
        assertSumEquals(142, arrivals);

        List<Number> cancellations = data.getValueList(BusinessInsightsValue.CANCELLATIONS);
        assertEquals(expectedDays, cancellations.size());
        assertSumEquals(2, cancellations);

        List<Number> arrivalsLastYear = data.getValueList(BusinessInsightsValue.ARRIVALS_LAST_YEAR);
        assertEquals(expectedDays, arrivalsLastYear.size());
        assertSumEquals(0, arrivalsLastYear);

        List<Number> cancellationsLastYear = data.getValueList(BusinessInsightsValue.CANCELLATIONS_LAST_YEAR);
        assertEquals(expectedDays, cancellationsLastYear.size());
        assertSumEquals(2, cancellationsLastYear);

        List<Number> roomsSold = data.getValueList(BusinessInsightsValue.ROOMS_SOLD);
        assertEquals(expectedDays, roomsSold.size());
        assertSumEquals(142, roomsSold);

        List<Number> roomRevenue = data.getValueList(BusinessInsightsValue.ROOM_REVENUE);
        assertEquals(expectedDays, roomRevenue.size());
        assertSumEquals(9565, roomRevenue);

        List<Number> totalRevenue = data.getValueList(BusinessInsightsValue.TOTAL_REVENUE);
        assertEquals(expectedDays, totalRevenue.size());
        assertSumEquals(9765, totalRevenue);

        List<Number> netRevenue = data.getValueList(BusinessInsightsValue.NET_REVENUE);
        assertEquals(expectedDays, netRevenue.size());
        assertSumEquals(9565, netRevenue);

        List<Number> rateValue = data.getValueList(BusinessInsightsValue.RATE_VALUE);
        assertEquals(expectedDays, rateValue.size());
        assertSumEquals(1067, rateValue);

        List<Number> netRateValue = data.getValueList(BusinessInsightsValue.NET_RATE_VALUE);
        assertEquals(expectedDays, netRateValue.size());
        assertSumEquals(1067, netRateValue);

        List<Date> categoryList = (List<Date>) data.getCategoryValues(false);
        assertEquals(121, categoryList.size());
        LocalDate compareDate = startDate;
        for (Date occupancyDate : categoryList) {
            assertEquals(DateUtil.convertLocalDateToJavaUtilDate(compareDate), occupancyDate);
            compareDate = compareDate.plusDays(1);
        }
    }

    @Test
    public void testGetArrivalsByOccupancyDateWithBookingDate() throws ParseException {
        UniqueIndividualTransactionsCreator.createIndividualTrans(DateUtil.convertJavaToJodaLocalDate(NOW.minusDays(5)), DateUtil.convertJavaToJodaLocalDate(NOW.minusDays(25)), "B", DateUtil.convertJavaToJodaLocalDate(NOW.minusDays(15)), "XX");
        tenantCrudService().findAll(IndividualTransactions.class);
        LocalDate startDate = LocalDate.now().minusDays(90);
        LocalDate endDate = LocalDate.now();
        LocalDate bookingDate = LocalDate.now().minusDays(10);
        BusinessInsightsCriteria criteria = new BusinessInsightsCriteria();
        criteria.setCategory(BusinessInsightsCategory.OCCUPANCY_DATE);
        criteria.addValue(BusinessInsightsValue.ARRIVALS);
        criteria.addValue(BusinessInsightsValue.CANCELLATIONS);
        criteria.setStartDate(startDate);
        criteria.setEndDate(endDate);
        criteria.setAsOfDate(endDate);
        BusinessInsightsData data = service.getBusinessInsightsData(criteria);

        assertEquals(2, data.getValueTypes().size());

        int expectedDays = (int) (DAYS.between(startDate, endDate) + 1);

        List<Number> arrivals = data.getValueList(BusinessInsightsValue.ARRIVALS);
        assertEquals(expectedDays, arrivals.size());
        assertSumEquals(5, arrivals);

        List<Number> cancellation = data.getValueList(BusinessInsightsValue.CANCELLATIONS);
        assertSumEquals(3, cancellation);

        List<Date> categoryList = (List<Date>) data.getCategoryValues(false);
        assertEquals(91, categoryList.size());
        LocalDate compareDate = startDate;
        for (Date occupancyDate : categoryList) {
            assertEquals(DateUtil.convertLocalDateToJavaUtilDate(compareDate), occupancyDate);
            compareDate = compareDate.plusDays(1);
        }

        //applying booking date
        criteria.setAsOfDate(bookingDate);
        data = service.getBusinessInsightsData(criteria);

        arrivals = data.getValueList(BusinessInsightsValue.ARRIVALS);
        assertEquals(expectedDays, arrivals.size());
        assertSumEquals(4, arrivals);

        cancellation = data.getValueList(BusinessInsightsValue.CANCELLATIONS);
        assertSumEquals(1, cancellation);
    }

    @Test
    public void testGetRoomsSoldByDayOfWeek() {
        LocalDate startDate = LocalDate.now().minusDays(90);
        LocalDate endDate = LocalDate.now().plusDays(30);
        BusinessInsightsCriteria criteria = new BusinessInsightsCriteria();
        criteria.setCategory(BusinessInsightsCategory.DAY_OF_WEEK);
        criteria.addValue(BusinessInsightsValue.ROOMS_SOLD);
        criteria.addValue(BusinessInsightsValue.OCCUPANCY_ON_BOOKS_LAST_YEAR);
        criteria.setStartDate(startDate);
        criteria.setEndDate(endDate);
        criteria.setAsOfDate(endDate);
        BusinessInsightsData data = service.getBusinessInsightsData(criteria);

        assertEquals(2, data.getValueTypes().size());

        List<Number> roomsSold = data.getValueList(BusinessInsightsValue.ROOMS_SOLD);
        assertEquals(7, roomsSold.size());

        List<Number> roomsSoldLastYear = data.getValueList(BusinessInsightsValue.OCCUPANCY_ON_BOOKS_LAST_YEAR);
        assertEquals(7, roomsSoldLastYear.size());

        List<Number> categoryList = (List<Number>) data.getCategoryValues(false);
        assertEquals(7, categoryList.size());
        for (int i = 0, dow = 1; i < 7; i++) {
            assertEquals(dow++, categoryList.get(i));
        }
    }

    @Test
    public void testGetDataByDaysToArrival() {
        LocalDate startDate = LocalDate.now().minusDays(90);
        LocalDate endDate = LocalDate.now().plusDays(30);
        BusinessInsightsCriteria criteria = new BusinessInsightsCriteria();
        criteria.setCategory(BusinessInsightsCategory.DAYS_TO_ARRIVAL);
        criteria.addValue(BusinessInsightsValue.ARRIVALS);
        criteria.addValue(BusinessInsightsValue.ARRIVALS_LAST_YEAR);
        criteria.addValue(BusinessInsightsValue.ROOM_REVENUE);
        criteria.setStartDate(startDate);
        criteria.setEndDate(endDate);
        criteria.setDaysToArrival(90);

        BusinessInsightsData data = service.getBusinessInsightsData(criteria);

        assertEquals(3, data.getValueTypes().size());

        List<Number> arrivals = data.getValueList(BusinessInsightsValue.ARRIVALS);
        assertEquals(91, arrivals.size());

        List<Number> arrivalsLastYear = data.getValueList(BusinessInsightsValue.ARRIVALS_LAST_YEAR);
        assertEquals(91, arrivalsLastYear.size());

        List<Number> roomRevenue = data.getValueList(BusinessInsightsValue.ROOM_REVENUE);
        assertEquals(91, roomRevenue.size());

        List<Number> categoryList = (List<Number>) data.getCategoryValues(false);
        assertEquals(91, categoryList.size());
        for (int i = 0; i < 91; i++) {
            assertEquals(i, categoryList.get(i));
        }
    }

    @Test
    public void testGetDataByLengthOfStay() {
        LocalDate startDate = LocalDate.now().minusDays(90);
        LocalDate endDate = LocalDate.now().plusDays(30);
        BusinessInsightsCriteria criteria = new BusinessInsightsCriteria();
        criteria.setCategory(BusinessInsightsCategory.LENGTH_OF_STAY);
        criteria.addValue(BusinessInsightsValue.ARRIVALS);
        criteria.addValue(BusinessInsightsValue.ARRIVALS_LAST_YEAR);
        criteria.addValue(BusinessInsightsValue.ROOM_REVENUE);
        criteria.setStartDate(startDate);
        criteria.setEndDate(endDate);

        BusinessInsightsData data = service.getBusinessInsightsData(criteria);

        assertEquals(3, data.getValueTypes().size());

        List<Number> arrivals = data.getValueList(BusinessInsightsValue.ARRIVALS);
        assertEquals(1, arrivals.size());

        List<Number> arrivalsLastYear = data.getValueList(BusinessInsightsValue.ARRIVALS_LAST_YEAR);
        assertEquals(1, arrivalsLastYear.size());

        List<Number> roomRevenue = data.getValueList(BusinessInsightsValue.ROOM_REVENUE);
        assertEquals(1, roomRevenue.size());

        List<Number> categoryList = (List<Number>) data.getCategoryValues(false);
        assertEquals(1, categoryList.size());
        assertEquals(1, categoryList.get(0));
    }

    @Test
    public void testGetTransactionsFilteredByRateCode() {
        BusinessInsightsCriteria criteria = new BusinessInsightsCriteria();
        criteria.setStartDate(LocalDate.now().minusDays(90));
        criteria.setEndDate(LocalDate.now().plusDays(30));
        criteria.addFilter(BusinessInsightsValue.RATE_CODE, Arrays.asList("A"));
        criteria.addFilter(BusinessInsightsValue.ACCOM_CLASS_CODE, Arrays.asList("DLX"));

        List<BusinessInsightsTrans> transactions = service.getTransactions(criteria);
        assertTrue(transactions.size() > 0);

        for (BusinessInsightsTrans trans : transactions) {
            assertEquals("A", trans.getRateCode());
            assertEquals("DLX", trans.getAccomClassCode());
        }
    }

    @Test
    public void getTransactions_withoutDuplicateInstances() throws Exception {

        // Ensure records populated for requested date

        LocalDate arrival = NOW;
        for (int i = 0; i < 10; i++) {
            String rateCode = "C";
            UniqueIndividualTransactionsCreator.createIndividualTrans(DateUtil.convertJavaToJodaLocalDate(arrival), DateUtil.convertJavaToJodaLocalDate(arrival.minusDays(7)), rateCode, null);
        }

        //original search criteria

        BusinessInsightsCriteria original = new BusinessInsightsCriteria();
        original.setCategory(BusinessInsightsCategory.OCCUPANCY_DATE);
        original.setStartDate(arrival.minusDays(10));
        original.setEndDate(arrival.plusDays(10));
        original.addValue(BusinessInsightsValue.ARRIVALS);
        original.setDaysToArrival(90);

        //Simulate UI drilldown

        BusinessInsightsCriteria criteria = original.clone();
        BusinessInsightsValue selection = BusinessInsightsValue.ARRIVALS;

        //only select one value (series line)
        criteria.setValues(Arrays.asList(selection));

        //user clicked point for single date
        criteria.addFilter(BusinessInsightsValue.OCCUPANCY_DATE, Arrays.asList(DateUtil.convertLocalDateToJavaUtilDate(arrival)));

        List<BusinessInsightsTrans> transactions = service.getTransactions(criteria);

        ///

        assertThat(transactions.size(), greaterThan(1));

        assertThat(transactions.get(0), not(sameInstance(transactions.get(1))));
    }

    @Test
    public void testGetPossibleValuesWithNullValue() {
        LocalDate startDate = LocalDate.now().minusDays(90);
        LocalDate endDate = LocalDate.now().plusDays(30);
        List<Number> values = service.getPossibleValues(BusinessInsightsValue.LENGTH_OF_STAY, Number.class, startDate, endDate);

        assertNotNull(values);
        assertFalse(values.isEmpty());

        for (Number number : values) {
            assertNotNull(number);
        }
    }

    @Test
    public void testGetTuplesForAverages() {
        BusinessInsightsCriteria criteria = new BusinessInsightsCriteria();
        criteria.setCategory(BusinessInsightsCategory.OCCUPANCY_DATE);
        criteria.setStartDate(LocalDate.now().minusDays(90));
        criteria.setEndDate(LocalDate.now().plusDays(30));
        criteria.addValue(BusinessInsightsValue.AVG_DAILY_RATE);
        criteria.addValue(BusinessInsightsValue.AVG_DAILY_RATE_LAST_YEAR);

        List<Tuple> tuples = service.getTuples(criteria, false,service.getViewEntityClassBasedOnChannelCostVersion());
        assertTrue(tuples.size() > 0);
    }

    @Test
    public void testParseValueList() throws Exception {
        String input = "ARRIVALS,AVG_DAILY_RATE,ROOMS_SOLD";

        List<BusinessInsightsValue> values = service.parseValueList(input);

        assertNotNull(values);
        assertEquals(3, values.size());
        assertTrue(values.contains(BusinessInsightsValue.ARRIVALS));
        assertTrue(values.contains(BusinessInsightsValue.AVG_DAILY_RATE));
        assertTrue(values.contains(BusinessInsightsValue.ROOMS_SOLD));
    }

    @Test
    public void testParseFilterMap() throws Exception {
        String input = "DAY_OF_WEEK:3,4;LENGTH_OF_STAY:2,3,4;SOURCE_BOOKING:A,B,C;OCCUPANCY_DATE:2014-08-01,2014-08-02";

        Map<BusinessInsightsValue, List> parsed = service.parseFilterMap(input);

        assertNotNull(parsed);

        List daysOfWeek = parsed.get(BusinessInsightsValue.DAY_OF_WEEK);
        assertNotNull(daysOfWeek);
        assertEquals(2, daysOfWeek.size());
        assertTrue(daysOfWeek.contains(3));
        assertTrue(daysOfWeek.contains(4));

        List lengthOfStay = parsed.get(BusinessInsightsValue.LENGTH_OF_STAY);
        assertNotNull(lengthOfStay);
        assertEquals(3, lengthOfStay.size());
        assertTrue(lengthOfStay.contains(2));
        assertTrue(lengthOfStay.contains(3));
        assertTrue(lengthOfStay.contains(4));

        List sourceBooking = parsed.get(BusinessInsightsValue.SOURCE_BOOKING);
        assertNotNull(sourceBooking);
        assertEquals(3, sourceBooking.size());
        assertTrue(sourceBooking.contains("A"));
        assertTrue(sourceBooking.contains("B"));
        assertTrue(sourceBooking.contains("C"));

        List occupancyDate = parsed.get(BusinessInsightsValue.OCCUPANCY_DATE);
        assertNotNull(occupancyDate);
        assertEquals(2, occupancyDate.size());
        assertTrue(occupancyDate.contains(DateUtil.convertLocalDateToJavaUtilDate(LocalDate.parse("2014-08-01"))));
        assertTrue(occupancyDate.contains(DateUtil.convertLocalDateToJavaUtilDate(LocalDate.parse("2014-08-02"))));
    }

    @Test
    public void testGetDataREST() {
        String category = "OCCUPANCY_DATE";
        String startDate = "2013-08-08";
        String endDate = "2013-09-08";
        String values = "ARRIVALS,AVG_DAILY_RATE,ROOMS_SOLD";
        Integer daysToArrival = 90;
        String filters = "SOURCE_BOOKING:1A,1P";
        service = spy(service);
        BusinessInsightsCriteria criteria = new BusinessInsightsCriteria();
        criteria.setCategory(BusinessInsightsCategory.OCCUPANCY_DATE);
        criteria.setValues(Arrays.asList(BusinessInsightsValue.ARRIVALS,
                BusinessInsightsValue.AVG_DAILY_RATE, BusinessInsightsValue.ROOMS_SOLD));

        BusinessInsightsData data = new BusinessInsightsData(criteria);

        doReturn(data).when(service).getBusinessInsightsData(any(BusinessInsightsCriteria.class));

        ResponseEntity response = service.getData(category, startDate, endDate, values, daysToArrival, filters, null);
        assertEquals(200, response.getStatusCode().value());

        ArgumentCaptor<BusinessInsightsCriteria> criteriaCaptor = ArgumentCaptor.forClass(BusinessInsightsCriteria.class);
        verify(service).getBusinessInsightsData(criteriaCaptor.capture());
        BusinessInsightsCriteria capturedCriteria = criteriaCaptor.getValue();
        assertEquals(BusinessInsightsCategory.OCCUPANCY_DATE, capturedCriteria.getCategory());
        assertEquals(LocalDate.parse(startDate), capturedCriteria.getStartDate());
        assertEquals(LocalDate.parse(endDate), capturedCriteria.getEndDate());
        assertEquals(BusinessInsightsValue.ARRIVALS, capturedCriteria.getValues().get(0));
        assertEquals(BusinessInsightsValue.AVG_DAILY_RATE, capturedCriteria.getValues().get(1));
        assertEquals(BusinessInsightsValue.ROOMS_SOLD, capturedCriteria.getValues().get(2));
    }

    @Test
    public void testGetDataRESTWithNullFilters() {
        String category = "OCCUPANCY_DATE";
        String startDate = "2013-08-08";
        String endDate = "2013-09-08";
        String values = "ARRIVALS,AVG_DAILY_RATE,ROOMS_SOLD";
        Integer daysToArrival = 90;
        String filters = null;

        service = spy(service);

        BusinessInsightsCriteria criteria = new BusinessInsightsCriteria();
        criteria.setCategory(BusinessInsightsCategory.OCCUPANCY_DATE);
        criteria.setValues(Arrays.asList(BusinessInsightsValue.ARRIVALS,
                BusinessInsightsValue.AVG_DAILY_RATE, BusinessInsightsValue.ROOMS_SOLD));

        BusinessInsightsData data = new BusinessInsightsData(criteria);

        doReturn(data).when(service).getBusinessInsightsData(any(BusinessInsightsCriteria.class));

        ResponseEntity response = service.getData(category, startDate, endDate, values, daysToArrival, filters, null);
        assertEquals(200, response.getStatusCode().value());

        ArgumentCaptor<BusinessInsightsCriteria> criteriaCaptor = ArgumentCaptor.forClass(BusinessInsightsCriteria.class);
        verify(service).getBusinessInsightsData(criteriaCaptor.capture());
        BusinessInsightsCriteria capturedCriteria = criteriaCaptor.getValue();
        assertEquals(BusinessInsightsCategory.OCCUPANCY_DATE, capturedCriteria.getCategory());
        assertEquals(LocalDate.parse(startDate), capturedCriteria.getStartDate());
        assertEquals(LocalDate.parse(endDate), capturedCriteria.getEndDate());
        assertEquals(BusinessInsightsValue.ARRIVALS, capturedCriteria.getValues().get(0));
        assertEquals(BusinessInsightsValue.AVG_DAILY_RATE, capturedCriteria.getValues().get(1));
        assertEquals(BusinessInsightsValue.ROOMS_SOLD, capturedCriteria.getValues().get(2));
    }

    @Test
    public void dependentValuesAreNotDisplayedIfTheyWereNotSelected() {
        BusinessInsightsCriteria criteria = new BusinessInsightsCriteria();
        criteria.setStartDate(LocalDate.now().minusDays(90));
        criteria.setEndDate(LocalDate.now().plusDays(30));
        criteria.setCategory(BusinessInsightsCategory.OCCUPANCY_DATE);
        criteria.addValue(BusinessInsightsValue.REVENUE_PER_AVAILABLE_ROOM);
        criteria.addValue(BusinessInsightsValue.REVENUE_PER_AVAILABLE_ROOM_LAST_YEAR);

        BusinessInsightsData data = service.getBusinessInsightsData(criteria);
        assertTrue(data.getValueTypes().contains(BusinessInsightsValue.REVENUE_PER_AVAILABLE_ROOM), "RevPAR should be included in results");
        assertTrue(data.getValueTypes().contains(BusinessInsightsValue.REVENUE_PER_AVAILABLE_ROOM), "RevPARLastYear should be included in results");
        assertFalse(data.getValueTypes().contains(BusinessInsightsValue.CAPACITY), "Capacity should not be included in results");
        assertFalse(data.getValueTypes().contains(BusinessInsightsValue.CAPACITY_LAST_YEAR), "CapacityLastYear should not be included in results");
    }

    @Test
    public void testIgnoreCaseSorting() {

        BusinessInsightsCriteria criteria = new BusinessInsightsCriteria();
        criteria.setCategory(BusinessInsightsCategory.MARKET_SEGMENT_CODE);
        criteria.setValues(Arrays.asList(BusinessInsightsValue.ROOMS_SOLD));

        BusinessInsightsData data = new BusinessInsightsData(criteria);
        data.addValue(BusinessInsightsValue.ROOMS_SOLD, 498);
        data.addValue(BusinessInsightsValue.ROOMS_SOLD, 482);
        data.addValue(BusinessInsightsValue.ROOMS_SOLD, 1);
        data.addValue(BusinessInsightsValue.ROOMS_SOLD, 182);
        data.addValue(BusinessInsightsValue.ROOMS_SOLD, 197);

        data.addCategoryValue(false, "AIR");
        data.addCategoryValue(false, "ASC");
        data.addCategoryValue(false, "CPR/DIS");
        data.addCategoryValue(false, "Comp");
        data.addCategoryValue(false, "Abc");
        data.addCategoryValue(true, "Comp");
        data.addCategoryValue(true, "ABC");

        List<Object> expectedSorting = Arrays.asList("Abc", "ABC", "AIR", "ASC", "Comp", "CPR/DIS");

        List<Object> actualSorting = data.getCategoryValues(false);
        assertTrue(expectedSorting.equals(actualSorting), "Sorting should be case in-sensitive");
    }

    @Test
    public void testIgnoreCaseSortingOtherThenString() {

        BusinessInsightsCriteria criteria = new BusinessInsightsCriteria();
        criteria.setCategory(BusinessInsightsCategory.LENGTH_OF_STAY);
        criteria.setValues(Arrays.asList(BusinessInsightsValue.ROOMS_SOLD));

        BusinessInsightsData data = new BusinessInsightsData(criteria);
        data.addValue(BusinessInsightsValue.ROOMS_SOLD, 498);
        data.addValue(BusinessInsightsValue.ROOMS_SOLD, 482);
        data.addValue(BusinessInsightsValue.ROOMS_SOLD, 1);
        data.addValue(BusinessInsightsValue.ROOMS_SOLD, 182);
        data.addValue(BusinessInsightsValue.ROOMS_SOLD, 197);

        data.addCategoryValue(false, 1);
        data.addCategoryValue(false, 2);
        data.addCategoryValue(false, 3);
        data.addCategoryValue(false, 4);
        data.addCategoryValue(false, 5);
        data.addCategoryValue(true, 6);
        data.addCategoryValue(true, 7);

        List<Object> expectedSorting = Arrays.asList(1, 2, 3, 4, 5, 6, 7);

        List<Object> actualSorting = data.getCategoryValues(false);
        assertTrue(expectedSorting.equals(actualSorting), "Sorting should not be affected for other data types");
    }

    @Test
    public void shouldGetViewEntityClassBasedOnChannelCostVersionAndIncludeCancNoShowRevenueToggledFALSE() {
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.CHANNEL_COST_V3)).thenReturn(FALSE);
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.SIMPLIFIED_CHANNEL_COST_ENABLED)).thenReturn(TRUE);

        Class<? extends BusinessInsightsTrans> viewEntityClass = service.getViewEntityClassBasedOnChannelCostVersion();

        assertThat(viewEntityClass, is(equalTo(BusinessInsightsTransWithSimplifiedChannelCost.class)));
    }

    @Test
    public void shouldGetViewEntityClassBasedOnChannelCostVersionAndIncludeCancNoShowRevenueToggledTRUE() {
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.CHANNEL_COST_V3)).thenReturn(FALSE);
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.SIMPLIFIED_CHANNEL_COST_ENABLED)).thenReturn(TRUE);
        when(propertyConfigParamService.isIncludeCancellationNoShowRevenueEnabled()).thenReturn(TRUE);
        Class<? extends BusinessInsightsTrans> viewEntityClassSimplifiedCC = service.getViewEntityClassBasedOnChannelCostVersion();
        assertThat(viewEntityClassSimplifiedCC, is(equalTo(BusinessInsightsTransSimplifiedChannelCostWithCancNoShowRevenue.class)));

        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.SIMPLIFIED_CHANNEL_COST_ENABLED)).thenReturn(FALSE);
        Class<? extends BusinessInsightsTrans> viewEntityClass = service.getViewEntityClassBasedOnChannelCostVersion();
        assertThat(viewEntityClass, is(equalTo(BusinessInsightsTransOldChannelCostWithCancNoShowRevenue.class)));
    }

    @Test
    public void shouldGetViewEntityClassBasedOnChannelCostVersionAndDayUSeToggle() {
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.CHANNEL_COST_V3)).thenReturn(FALSE);
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.SIMPLIFIED_CHANNEL_COST_ENABLED)).thenReturn(FALSE);
        when(propertyConfigParamService.isIncludeCancellationNoShowRevenueEnabled()).thenReturn(FALSE);
        when(propertyConfigParamService.isDayUsePopulationEnabled()).thenReturn(FALSE);
        Class<? extends BusinessInsightsTrans> viewEntity1 = service.getViewEntityClassBasedOnChannelCostVersion();
        assertThat(viewEntity1, is(equalTo(BusinessInsightsTransWithOldChannelCost.class)));

        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.SIMPLIFIED_CHANNEL_COST_ENABLED)).thenReturn(FALSE);
        when(propertyConfigParamService.isIncludeCancellationNoShowRevenueEnabled()).thenReturn(FALSE);
        when(propertyConfigParamService.isDayUsePopulationEnabled()).thenReturn(TRUE);
        Class<? extends BusinessInsightsTrans> viewEntity2 = service.getViewEntityClassBasedOnChannelCostVersion();
        assertThat(viewEntity2, is(equalTo(BusinessInsightsTransWithOldChannelCostWithDayUse.class)));

        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.SIMPLIFIED_CHANNEL_COST_ENABLED)).thenReturn(TRUE);
        when(propertyConfigParamService.isIncludeCancellationNoShowRevenueEnabled()).thenReturn(FALSE);
        when(propertyConfigParamService.isDayUsePopulationEnabled()).thenReturn(FALSE);
        Class<? extends BusinessInsightsTrans> viewEntity3 = service.getViewEntityClassBasedOnChannelCostVersion();
        assertThat(viewEntity3, is(equalTo(BusinessInsightsTransWithSimplifiedChannelCost.class)));

        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.SIMPLIFIED_CHANNEL_COST_ENABLED)).thenReturn(TRUE);
        when(propertyConfigParamService.isIncludeCancellationNoShowRevenueEnabled()).thenReturn(FALSE);
        when(propertyConfigParamService.isDayUsePopulationEnabled()).thenReturn(TRUE);
        Class<? extends BusinessInsightsTrans> viewEntity4 = service.getViewEntityClassBasedOnChannelCostVersion();
        assertThat(viewEntity4, is(equalTo(BusinessInsightsTransWithSimplifiedChannelCostWithDayUseRevenue.class)));

        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.CHANNEL_COST_V3)).thenReturn(TRUE);
        when(propertyConfigParamService.isIncludeCancellationNoShowRevenueEnabled()).thenReturn(FALSE);
        when(propertyConfigParamService.isDayUsePopulationEnabled()).thenReturn(FALSE);
        Class<? extends BusinessInsightsTrans> viewEntity5 = service.getViewEntityClassBasedOnChannelCostVersion();
        assertThat(viewEntity5, is(equalTo(BusinessInsightsTransWithChannelCostV3.class)));

        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.CHANNEL_COST_V3)).thenReturn(TRUE);
        when(propertyConfigParamService.isIncludeCancellationNoShowRevenueEnabled()).thenReturn(FALSE);
        when(propertyConfigParamService.isDayUsePopulationEnabled()).thenReturn(TRUE);
        Class<? extends BusinessInsightsTrans> viewEntity6 = service.getViewEntityClassBasedOnChannelCostVersion();
        assertThat(viewEntity6, is(equalTo(BusinessInsightsTransWithChannelCostV3WithDayUseRevenue.class)));

        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.CHANNEL_COST_V3)).thenReturn(TRUE);
        when(propertyConfigParamService.isIncludeCancellationNoShowRevenueEnabled()).thenReturn(TRUE);
        Class<? extends BusinessInsightsTrans> viewEntity7 = service.getViewEntityClassBasedOnChannelCostVersion();
        assertThat(viewEntity7, is(equalTo(BusinessInsightsTransChannelCostV3WithCancNoShowRevenue.class)));
    }

    @Test
    public void testGetOccupancyOnBooksLYConsideringAsOfDateWithAddCancellationToggleOn() {
        tenantCrudService().executeUpdateByNativeQuery("truncate table Reservation_Night ");

        LocalDate date = LocalDate.parse("2022-12-12");
        LocalDate asOfDate = LocalDate.parse("2022-11-11");
        LocalDate arrivalDate = LocalDate.parse("2021-12-13");
        LocalDate departureDate = LocalDate.parse("2021-12-15");
        LocalDate bookingDate = LocalDate.parse("2021-11-05");
        LocalDate cancellationDate = LocalDate.parse("2021-12-11");

        insertDayUseTransactionsIntoReservationNight(DateUtil.convertLocalDateToJavaUtilDate(arrivalDate),
                DateUtil.convertLocalDateToJavaUtilDate(departureDate),
                DateUtil.convertLocalDateToJavaUtilDate(bookingDate),
                DateUtil.convertLocalDateToJavaUtilDate(cancellationDate));

        BusinessInsightsCriteria criteria = new BusinessInsightsCriteria();
        criteria.setCategory(BusinessInsightsCategory.OCCUPANCY_DATE);
        criteria.addValue(BusinessInsightsValue.OCCUPANCY_ON_BOOKS_LAST_YEAR);
        criteria.setStartDate(date);
        criteria.setEndDate(date);
        criteria.setAsOfDate(asOfDate);

        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.POST_DEPARTURE_REVENUE_ADJUSTMENT_ENABLED)).thenReturn(FALSE);
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.ADD_CANCELLATIONS_TO_OCCUPANCY_ON_BOOKS_STLY_FOR_BUSINESS_INSIGHT)).thenReturn(TRUE);
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.BUSINESS_INSIGHTS_REPORT_CANCELLATIONS_NO_SHOW_SEPARATION)).thenReturn(TRUE);

        BusinessInsightsData data = service.getBusinessInsightsData(criteria);
        assertEquals(1, data.getValueTypes().size());
        int expectedDays = 1;

        List<Number> occupancyOnBooksLastYear = data.getValueList(BusinessInsightsValue.OCCUPANCY_ON_BOOKS_LAST_YEAR);
        assertEquals(expectedDays, occupancyOnBooksLastYear.size());
        assertSumEquals(2, occupancyOnBooksLastYear);

        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.BUSINESS_INSIGHTS_REPORT_CANCELLATIONS_NO_SHOW_SEPARATION)).thenReturn(TRUE);
        criteria.setValues(Collections.singletonList(BusinessInsightsValue.ROOMS_SOLD));
        criteria.setStartDate(date.minusWeeks(52));
        criteria.setEndDate(date.minusWeeks(52));
        criteria.setAsOfDate(asOfDate.minusWeeks(52));
        BusinessInsightsData currentYearData = service.getBusinessInsightsData(criteria);
        assertEquals(1, currentYearData.getValueTypes().size());

        List<Number> occupancyOnBooks = currentYearData.getValueList(BusinessInsightsValue.ROOMS_SOLD);
        assertEquals(expectedDays, occupancyOnBooks.size());
        assertSumEquals(2, occupancyOnBooks);
    }

    @Test
    public void testGetCancellationsLYConsideringAsOfDateWithAddCancellationToggleOn() {
        tenantCrudService().executeUpdateByNativeQuery("truncate table Reservation_Night ");

        LocalDate date = LocalDate.parse("2022-12-12");
        LocalDate asOfDate = LocalDate.parse("2022-11-11");
        LocalDate arrivalDate = LocalDate.parse("2021-12-13");
        LocalDate departureDate = LocalDate.parse("2021-12-15");
        LocalDate bookingDate = LocalDate.parse("2021-11-05");
        LocalDate cancellationDate = LocalDate.parse("2021-11-09");

        insertDayUseTransactionsIntoReservationNight(DateUtil.convertLocalDateToJavaUtilDate(arrivalDate),
                DateUtil.convertLocalDateToJavaUtilDate(departureDate),
                DateUtil.convertLocalDateToJavaUtilDate(bookingDate),
                DateUtil.convertLocalDateToJavaUtilDate(cancellationDate));

        BusinessInsightsCriteria criteria = new BusinessInsightsCriteria();
        criteria.setCategory(BusinessInsightsCategory.OCCUPANCY_DATE);
        criteria.addValue(BusinessInsightsValue.CANCELLATIONS_LAST_YEAR);
        criteria.setStartDate(date);
        criteria.setEndDate(date);
        criteria.setAsOfDate(asOfDate);

        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.POST_DEPARTURE_REVENUE_ADJUSTMENT_ENABLED)).thenReturn(FALSE);
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.ADD_CANCELLATIONS_TO_OCCUPANCY_ON_BOOKS_STLY_FOR_BUSINESS_INSIGHT)).thenReturn(TRUE);
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.BUSINESS_INSIGHTS_REPORT_CANCELLATIONS_NO_SHOW_SEPARATION)).thenReturn(TRUE);
        BusinessInsightsData data = service.getBusinessInsightsData(criteria);
        assertEquals(1, data.getValueTypes().size());
        int expectedDays = 1;

        List<Number> cancellationsLastYear = data.getValueList(BusinessInsightsValue.CANCELLATIONS_LAST_YEAR);
        assertEquals(expectedDays, cancellationsLastYear.size());
        assertSumEquals(1, cancellationsLastYear);

        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.BUSINESS_INSIGHTS_REPORT_CANCELLATIONS_NO_SHOW_SEPARATION)).thenReturn(TRUE);
        criteria.setValues(Collections.singletonList(BusinessInsightsValue.CANCELLATIONS));
        criteria.setStartDate(date.minusWeeks(52));
        criteria.setEndDate(date.minusWeeks(52));
        criteria.setAsOfDate(asOfDate.minusWeeks(52));

        BusinessInsightsData currentYearData = service.getBusinessInsightsData(criteria);
        assertEquals(1, currentYearData.getValueTypes().size());

        List<Number> cancellations = currentYearData.getValueList(BusinessInsightsValue.CANCELLATIONS);
        assertEquals(expectedDays, cancellations.size());
        assertSumEquals(1, cancellations);
    }

    @Test
    void testGetRoomRevenueLYConsideringAsOfDateWithAddCancellationToggleOn() {
        tenantCrudService().executeUpdateByNativeQuery("truncate table Reservation_Night ");

        LocalDate date = LocalDate.parse("2022-12-12");
        LocalDate asOfDate = LocalDate.parse("2022-11-11");
        LocalDate arrivalDate = LocalDate.parse("2021-12-13");
        LocalDate departureDate = LocalDate.parse("2021-12-15");
        LocalDate bookingDate = LocalDate.parse("2021-11-05");
        LocalDate cancellationDate = LocalDate.parse("2021-12-11");

        insertDayUseTransactionsIntoReservationNight(DateUtil.convertLocalDateToJavaUtilDate(arrivalDate),
                DateUtil.convertLocalDateToJavaUtilDate(departureDate),
                DateUtil.convertLocalDateToJavaUtilDate(bookingDate),
                DateUtil.convertLocalDateToJavaUtilDate(cancellationDate));

        BusinessInsightsCriteria criteria = new BusinessInsightsCriteria();
        criteria.setCategory(BusinessInsightsCategory.OCCUPANCY_DATE);
        criteria.addValue(BusinessInsightsValue.ROOM_REVENUE_LAST_YEAR);
        criteria.setStartDate(date);
        criteria.setEndDate(date);
        criteria.setAsOfDate(asOfDate);

        lenient().when(propertyConfigParamService.isIncludeCancellationNoShowRevenueEnabled()).thenReturn(TRUE);
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.POST_DEPARTURE_REVENUE_ADJUSTMENT_ENABLED)).thenReturn(FALSE);
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.ADD_CANCELLATIONS_TO_OCCUPANCY_ON_BOOKS_STLY_FOR_BUSINESS_INSIGHT)).thenReturn(TRUE);
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.BUSINESS_INSIGHTS_REPORT_CANCELLATIONS_NO_SHOW_SEPARATION)).thenReturn(TRUE);
        BusinessInsightsData data = service.getBusinessInsightsData(criteria);
        assertEquals(1, data.getValueTypes().size());
        int expectedDays = 1;

        List<Number> roomRevenueLastYear = data.getValueList(BusinessInsightsValue.ROOM_REVENUE_LAST_YEAR);
        assertEquals(expectedDays, roomRevenueLastYear.size());
        assertSumEquals(100, roomRevenueLastYear);


        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.BUSINESS_INSIGHTS_REPORT_CANCELLATIONS_NO_SHOW_SEPARATION)).thenReturn(TRUE);
        criteria.setValues(Collections.singletonList(BusinessInsightsValue.ROOM_REVENUE));
        criteria.setStartDate(date.minusWeeks(52));
        criteria.setEndDate(date.minusWeeks(52));
        criteria.setAsOfDate(asOfDate.minusWeeks(52));

        BusinessInsightsData currentYear = service.getBusinessInsightsData(criteria);
        assertEquals(1, currentYear.getValueTypes().size());

        List<Number> roomRevenue = currentYear.getValueList(BusinessInsightsValue.ROOM_REVENUE);
        assertEquals(expectedDays, roomRevenue.size());
        assertSumEquals(100, roomRevenue);
    }

    @Test
    void testGetADRLYConsideringAsOfDateWithAddCancellationToggleOn() {
        tenantCrudService().executeUpdateByNativeQuery("truncate table Reservation_Night ");

        LocalDate date = LocalDate.parse("2022-12-12");
        LocalDate asOfDate = LocalDate.parse("2022-11-11");
        LocalDate arrivalDate = LocalDate.parse("2021-12-13");
        LocalDate departureDate = LocalDate.parse("2021-12-15");
        LocalDate bookingDate = LocalDate.parse("2021-11-05");
        LocalDate cancellationDate = LocalDate.parse("2021-12-11");

        insertDayUseTransactionsIntoReservationNight(DateUtil.convertLocalDateToJavaUtilDate(arrivalDate),
                DateUtil.convertLocalDateToJavaUtilDate(departureDate),
                DateUtil.convertLocalDateToJavaUtilDate(bookingDate),
                DateUtil.convertLocalDateToJavaUtilDate(cancellationDate));

        BusinessInsightsCriteria criteria = new BusinessInsightsCriteria();
        criteria.setCategory(BusinessInsightsCategory.OCCUPANCY_DATE);
        criteria.addValue(BusinessInsightsValue.AVG_DAILY_RATE_LAST_YEAR);
        criteria.setStartDate(date);
        criteria.setEndDate(date);
        criteria.setAsOfDate(asOfDate);

        lenient().when(propertyConfigParamService.isIncludeCancellationNoShowRevenueEnabled()).thenReturn(TRUE);
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.POST_DEPARTURE_REVENUE_ADJUSTMENT_ENABLED)).thenReturn(FALSE);
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.ADD_CANCELLATIONS_TO_OCCUPANCY_ON_BOOKS_STLY_FOR_BUSINESS_INSIGHT)).thenReturn(TRUE);
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.BUSINESS_INSIGHTS_REPORT_CANCELLATIONS_NO_SHOW_SEPARATION)).thenReturn(TRUE);
        BusinessInsightsData data = service.getBusinessInsightsData(criteria);
        assertEquals(1, data.getValueTypes().size());
        int expectedDays = 1;

        List<Number> adrLastYear = data.getValueList(BusinessInsightsValue.AVG_DAILY_RATE_LAST_YEAR);
        assertEquals(expectedDays, adrLastYear.size());
        assertSumEquals(0, adrLastYear);


        criteria.setValues(new ArrayList<>(List.of(BusinessInsightsValue.AVG_DAILY_RATE)));
        criteria.setStartDate(date.minusWeeks(52));
        criteria.setEndDate(date.minusWeeks(52));
        criteria.setAsOfDate(asOfDate.minusWeeks(52));

        BusinessInsightsData currentYear = service.getBusinessInsightsData(criteria);
        assertEquals(1, currentYear.getValueTypes().size());

        List<Number> adr = currentYear.getValueList(BusinessInsightsValue.AVG_DAILY_RATE);
        assertEquals(expectedDays, adr.size());
        assertSumEquals(0, adr);
    }

    @Test
    void testGetRevParLYConsideringAsOfDateWithAddCancellationToggleOn() {
        tenantCrudService().executeUpdateByNativeQuery("truncate table Reservation_Night ");
        tenantCrudService().executeUpdateByNativeQuery("truncate table Accom_Activity ");

        LocalDate date = LocalDate.parse("2022-12-12");
        LocalDate asOfDate = LocalDate.parse("2022-11-11");
        LocalDate arrivalDate = LocalDate.parse("2021-12-13");
        LocalDate departureDate = LocalDate.parse("2021-12-15");
        LocalDate bookingDate = LocalDate.parse("2021-11-05");
        LocalDate cancellationDate = LocalDate.parse("2021-12-11");

        String insertQuery = " insert into Accom_Activity(Property_ID,Occupancy_DT,SnapShot_DTTM,Accom_Type_ID,Accom_Capacity,Rooms_Sold,Rooms_Not_Avail_Maint,Rooms_Not_Avail_Other,Arrivals,Departures,Cancellations,No_Shows,Room_Revenue,Food_Revenue,Total_Revenue,File_Metadata_ID)" +
                " values(5,'2021-12-13','2021-12-15 00:00:00',4,7,5,1,1,5,6,0,0,250.00000,150.00000,400.00000,1);";
        tenantCrudService().executeUpdateByNativeQuery(insertQuery);

        insertDayUseTransactionsIntoReservationNight(DateUtil.convertLocalDateToJavaUtilDate(arrivalDate),
                DateUtil.convertLocalDateToJavaUtilDate(departureDate),
                DateUtil.convertLocalDateToJavaUtilDate(bookingDate),
                DateUtil.convertLocalDateToJavaUtilDate(cancellationDate));

        BusinessInsightsCriteria criteria = new BusinessInsightsCriteria();
        criteria.setCategory(BusinessInsightsCategory.OCCUPANCY_DATE);
        criteria.addValue(BusinessInsightsValue.REVENUE_PER_AVAILABLE_ROOM_LAST_YEAR);
        criteria.setStartDate(date);
        criteria.setEndDate(date);
        criteria.setAsOfDate(asOfDate);

        lenient().when(propertyConfigParamService.isIncludeCancellationNoShowRevenueEnabled()).thenReturn(TRUE);
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.POST_DEPARTURE_REVENUE_ADJUSTMENT_ENABLED)).thenReturn(FALSE);
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.ADD_CANCELLATIONS_TO_OCCUPANCY_ON_BOOKS_STLY_FOR_BUSINESS_INSIGHT)).thenReturn(TRUE);
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.BUSINESS_INSIGHTS_REPORT_CANCELLATIONS_NO_SHOW_SEPARATION)).thenReturn(TRUE);
        BusinessInsightsData data = service.getBusinessInsightsData(criteria);
        assertEquals(1, data.getValueTypes().size());
        int expectedDays = 1;

        List<Number> revParLastYear = data.getValueList(BusinessInsightsValue.REVENUE_PER_AVAILABLE_ROOM_LAST_YEAR);
        assertEquals(expectedDays, revParLastYear.size());
        assertSumEquals(20, revParLastYear);

        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.BUSINESS_INSIGHTS_REPORT_CANCELLATIONS_NO_SHOW_SEPARATION)).thenReturn(TRUE);
        criteria.setValues(new ArrayList<>(List.of(BusinessInsightsValue.REVENUE_PER_AVAILABLE_ROOM)));
        criteria.setStartDate(date.minusWeeks(52));
        criteria.setEndDate(date.minusWeeks(52));
        criteria.setAsOfDate(asOfDate.minusWeeks(52));

        BusinessInsightsData currentYear = service.getBusinessInsightsData(criteria);
        assertEquals(1, currentYear.getValueTypes().size());

        List<Number> revPar = currentYear.getValueList(BusinessInsightsValue.REVENUE_PER_AVAILABLE_ROOM);
        assertEquals(expectedDays, revPar.size());
        assertSumEquals(20, revPar);
    }

    @Test
    void testGetNetRoomRevenueLYConsideringAsOfDateWithAddCancellationToggleOn() {
        tenantCrudService().executeUpdateByNativeQuery("truncate table Reservation_Night ");

        LocalDate date = LocalDate.parse("2022-12-12");
        LocalDate asOfDate = LocalDate.parse("2022-11-11");
        LocalDate arrivalDate = LocalDate.parse("2021-12-13");
        LocalDate departureDate = LocalDate.parse("2021-12-15");
        LocalDate bookingDate = LocalDate.parse("2021-11-05");
        LocalDate cancellationDate = LocalDate.parse("2021-12-11");

        insertDayUseTransactionsIntoReservationNight(DateUtil.convertLocalDateToJavaUtilDate(arrivalDate),
                DateUtil.convertLocalDateToJavaUtilDate(departureDate),
                DateUtil.convertLocalDateToJavaUtilDate(bookingDate),
                DateUtil.convertLocalDateToJavaUtilDate(cancellationDate));

        BusinessInsightsCriteria criteria = new BusinessInsightsCriteria();
        criteria.setCategory(BusinessInsightsCategory.OCCUPANCY_DATE);
        criteria.addValue(BusinessInsightsValue.NET_REVENUE_LAST_YEAR);
        criteria.setStartDate(date);
        criteria.setEndDate(date);
        criteria.setAsOfDate(asOfDate);

        lenient().when(propertyConfigParamService.isIncludeCancellationNoShowRevenueEnabled()).thenReturn(TRUE);
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.POST_DEPARTURE_REVENUE_ADJUSTMENT_ENABLED)).thenReturn(FALSE);
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.ADD_CANCELLATIONS_TO_OCCUPANCY_ON_BOOKS_STLY_FOR_BUSINESS_INSIGHT)).thenReturn(TRUE);
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.BUSINESS_INSIGHTS_REPORT_CANCELLATIONS_NO_SHOW_SEPARATION)).thenReturn(TRUE);
        BusinessInsightsData data = service.getBusinessInsightsData(criteria);
        assertEquals(1, data.getValueTypes().size());
        int expectedDays = 1;

        List<Number> netRoomRevenueLastYear = data.getValueList(BusinessInsightsValue.NET_REVENUE_LAST_YEAR);
        assertEquals(expectedDays, netRoomRevenueLastYear.size());
        assertSumEquals(100, netRoomRevenueLastYear);


        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.BUSINESS_INSIGHTS_REPORT_CANCELLATIONS_NO_SHOW_SEPARATION)).thenReturn(TRUE);
        criteria.setValues(new ArrayList<>(List.of(BusinessInsightsValue.NET_REVENUE)));
        criteria.setStartDate(date.minusWeeks(52));
        criteria.setEndDate(date.minusWeeks(52));
        criteria.setAsOfDate(asOfDate.minusWeeks(52));

        BusinessInsightsData currentYear = service.getBusinessInsightsData(criteria);
        assertEquals(1, currentYear.getValueTypes().size());

        List<Number> netRoomRevenue = currentYear.getValueList(BusinessInsightsValue.NET_REVENUE);
        assertEquals(expectedDays, netRoomRevenue.size());
        assertSumEquals(100, netRoomRevenue);
    }

    @Test
    void testGetNetADRLYConsideringAsOfDateWithAddCancellationToggleOn() {
        tenantCrudService().executeUpdateByNativeQuery("truncate table Reservation_Night ");

        LocalDate date = LocalDate.parse("2022-12-12");
        LocalDate asOfDate = LocalDate.parse("2022-11-11");
        LocalDate arrivalDate = LocalDate.parse("2021-12-13");
        LocalDate departureDate = LocalDate.parse("2021-12-15");
        LocalDate bookingDate = LocalDate.parse("2021-11-05");
        LocalDate cancellationDate = LocalDate.parse("2021-12-11");

        insertDayUseTransactionsIntoReservationNight(DateUtil.convertLocalDateToJavaUtilDate(arrivalDate),
                DateUtil.convertLocalDateToJavaUtilDate(departureDate),
                DateUtil.convertLocalDateToJavaUtilDate(bookingDate),
                DateUtil.convertLocalDateToJavaUtilDate(cancellationDate));

        BusinessInsightsCriteria criteria = new BusinessInsightsCriteria();
        criteria.setCategory(BusinessInsightsCategory.OCCUPANCY_DATE);
        criteria.addValue(BusinessInsightsValue.NET_AVG_DAILY_RATE_LAST_YEAR);
        criteria.setStartDate(date);
        criteria.setEndDate(date);
        criteria.setAsOfDate(asOfDate);

        lenient().when(propertyConfigParamService.isIncludeCancellationNoShowRevenueEnabled()).thenReturn(TRUE);
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.POST_DEPARTURE_REVENUE_ADJUSTMENT_ENABLED)).thenReturn(FALSE);
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.BUSINESS_INSIGHTS_REPORT_CANCELLATIONS_NO_SHOW_SEPARATION)).thenReturn(TRUE);
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.ADD_CANCELLATIONS_TO_OCCUPANCY_ON_BOOKS_STLY_FOR_BUSINESS_INSIGHT)).thenReturn(TRUE);
        BusinessInsightsData data = service.getBusinessInsightsData(criteria);
        assertEquals(1, data.getValueTypes().size());
        int expectedDays = 1;

        List<Number> netADRLastYear = data.getValueList(BusinessInsightsValue.NET_AVG_DAILY_RATE_LAST_YEAR);
        assertEquals(expectedDays, netADRLastYear.size());
        assertSumEquals(50, netADRLastYear);

        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.BUSINESS_INSIGHTS_REPORT_CANCELLATIONS_NO_SHOW_SEPARATION)).thenReturn(TRUE);
        criteria.setValues(new ArrayList<>(List.of(BusinessInsightsValue.NET_AVG_DAILY_RATE)));
        criteria.setStartDate(date.minusWeeks(52));
        criteria.setEndDate(date.minusWeeks(52));
        criteria.setAsOfDate(asOfDate.minusWeeks(52));

        BusinessInsightsData currentYear = service.getBusinessInsightsData(criteria);
        assertEquals(1, currentYear.getValueTypes().size());

        List<Number> netADRLast = currentYear.getValueList(BusinessInsightsValue.NET_AVG_DAILY_RATE);
        assertEquals(expectedDays, netADRLast.size());
        assertSumEquals(50, netADRLast);
    }

    @Test
    void testGetNetRevParLYConsideringAsOfDateWithAddCancellationToggleOn() {
        tenantCrudService().executeUpdateByNativeQuery("truncate table Reservation_Night ");
        tenantCrudService().executeUpdateByNativeQuery("truncate table Accom_Activity ");

        LocalDate date = LocalDate.parse("2022-12-12");
        LocalDate asOfDate = LocalDate.parse("2022-11-11");
        LocalDate arrivalDate = LocalDate.parse("2021-12-13");
        LocalDate departureDate = LocalDate.parse("2021-12-15");
        LocalDate bookingDate = LocalDate.parse("2021-11-05");
        LocalDate cancellationDate = LocalDate.parse("2021-12-11");

        String insertQuery = " insert into Accom_Activity(Property_ID,Occupancy_DT,SnapShot_DTTM,Accom_Type_ID,Accom_Capacity,Rooms_Sold,Rooms_Not_Avail_Maint,Rooms_Not_Avail_Other,Arrivals,Departures,Cancellations,No_Shows,Room_Revenue,Food_Revenue,Total_Revenue,File_Metadata_ID)" +
                " values(5,'2021-12-13','2021-12-15 00:00:00',4,7,5,1,1,5,6,0,0,250.00000,150.00000,400.00000,1);";
        tenantCrudService().executeUpdateByNativeQuery(insertQuery);

        insertDayUseTransactionsIntoReservationNight(DateUtil.convertLocalDateToJavaUtilDate(arrivalDate),
                DateUtil.convertLocalDateToJavaUtilDate(departureDate),
                DateUtil.convertLocalDateToJavaUtilDate(bookingDate),
                DateUtil.convertLocalDateToJavaUtilDate(cancellationDate));

        BusinessInsightsCriteria criteria = new BusinessInsightsCriteria();
        criteria.setCategory(BusinessInsightsCategory.OCCUPANCY_DATE);
        criteria.addValue(BusinessInsightsValue.NET_REVENUE_PER_AVAILABLE_ROOM_LAST_YEAR);
        criteria.setStartDate(date);
        criteria.setEndDate(date);
        criteria.setAsOfDate(asOfDate);

        lenient().when(propertyConfigParamService.isIncludeCancellationNoShowRevenueEnabled()).thenReturn(TRUE);
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.POST_DEPARTURE_REVENUE_ADJUSTMENT_ENABLED)).thenReturn(FALSE);
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.ADD_CANCELLATIONS_TO_OCCUPANCY_ON_BOOKS_STLY_FOR_BUSINESS_INSIGHT)).thenReturn(TRUE);
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.BUSINESS_INSIGHTS_REPORT_CANCELLATIONS_NO_SHOW_SEPARATION)).thenReturn(TRUE);
        BusinessInsightsData data = service.getBusinessInsightsData(criteria);
        assertEquals(1, data.getValueTypes().size());
        int expectedDays = 1;

        List<Number> netRevParLastYear = data.getValueList(BusinessInsightsValue.NET_REVENUE_PER_AVAILABLE_ROOM_LAST_YEAR);
        assertEquals(expectedDays, netRevParLastYear.size());
        assertSumEquals(0, netRevParLastYear);

        criteria.setValues(new ArrayList<>(List.of(BusinessInsightsValue.NET_REVENUE_PER_AVAILABLE_ROOM)));
        criteria.setStartDate(date.minusWeeks(52));
        criteria.setEndDate(date.minusWeeks(52));
        criteria.setAsOfDate(asOfDate.minusWeeks(52));

        BusinessInsightsData currentYear = service.getBusinessInsightsData(criteria);
        assertEquals(1, currentYear.getValueTypes().size());

        List<Number> netRevPar = currentYear.getValueList(BusinessInsightsValue.NET_REVENUE_PER_AVAILABLE_ROOM);
        assertEquals(expectedDays, netRevPar.size());
        assertSumEquals(0, netRevPar);
    }

    @Test
    public void testGetLastYearDataConsideringAsOfDateWithAddCancellationToggleOn() {
        tenantCrudService().executeUpdateByNativeQuery("truncate table Reservation_Night ");
        tenantCrudService().executeUpdateByNativeQuery("truncate table Accom_Activity ");

        LocalDate date = LocalDate.parse("2022-12-12");
        LocalDate asOfDate = LocalDate.parse("2022-11-11");
        LocalDate arrivalDate = LocalDate.parse("2021-12-13");
        LocalDate departureDate = LocalDate.parse("2021-12-15");
        LocalDate bookingDate = LocalDate.parse("2021-11-05");
        LocalDate cancellationDate = LocalDate.parse("2021-12-11");

        String insertQuery = " insert into Accom_Activity(Property_ID,Occupancy_DT,SnapShot_DTTM,Accom_Type_ID,Accom_Capacity,Rooms_Sold,Rooms_Not_Avail_Maint,Rooms_Not_Avail_Other,Arrivals,Departures,Cancellations,No_Shows,Room_Revenue,Food_Revenue,Total_Revenue,File_Metadata_ID)" +
                " values(5,'2021-12-13','2021-12-15 00:00:00',4,7,5,1,1,5,6,0,0,250.00000,150.00000,400.00000,1);";
        tenantCrudService().executeUpdateByNativeQuery(insertQuery);

        insertDayUseTransactionsIntoReservationNight(DateUtil.convertLocalDateToJavaUtilDate(arrivalDate),
                DateUtil.convertLocalDateToJavaUtilDate(departureDate),
                DateUtil.convertLocalDateToJavaUtilDate(bookingDate),
                DateUtil.convertLocalDateToJavaUtilDate(cancellationDate));

        BusinessInsightsCriteria criteria = new BusinessInsightsCriteria();
        criteria.setCategory(BusinessInsightsCategory.OCCUPANCY_DATE);
        criteria.addValue(BusinessInsightsValue.OCCUPANCY_ON_BOOKS_LAST_YEAR);
        criteria.addValue(BusinessInsightsValue.CANCELLATIONS_LAST_YEAR);
        criteria.addValue(BusinessInsightsValue.ROOM_REVENUE_LAST_YEAR);
        criteria.addValue(BusinessInsightsValue.AVG_DAILY_RATE_LAST_YEAR);
        criteria.addValue(BusinessInsightsValue.REVENUE_PER_AVAILABLE_ROOM_LAST_YEAR);
        criteria.addValue(BusinessInsightsValue.NET_REVENUE_LAST_YEAR);
        criteria.addValue(BusinessInsightsValue.NET_AVG_DAILY_RATE_LAST_YEAR);
        criteria.addValue(BusinessInsightsValue.NET_REVENUE_PER_AVAILABLE_ROOM_LAST_YEAR);
        criteria.setStartDate(date);
        criteria.setEndDate(date);
        criteria.setAsOfDate(asOfDate);

        lenient().when(propertyConfigParamService.isIncludeCancellationNoShowRevenueEnabled()).thenReturn(TRUE);
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.POST_DEPARTURE_REVENUE_ADJUSTMENT_ENABLED)).thenReturn(FALSE);
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.ADD_CANCELLATIONS_TO_OCCUPANCY_ON_BOOKS_STLY_FOR_BUSINESS_INSIGHT)).thenReturn(TRUE);
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.BUSINESS_INSIGHTS_REPORT_CANCELLATIONS_NO_SHOW_SEPARATION)).thenReturn(TRUE);
        BusinessInsightsData data = service.getBusinessInsightsData(criteria);
        assertEquals(8, data.getValueTypes().size());

        List<Number> occupancyOnBooksLastYear = data.getValueList(BusinessInsightsValue.OCCUPANCY_ON_BOOKS_LAST_YEAR);
        assertSumEquals(2, occupancyOnBooksLastYear);

        List<Number> cancellationsLastYear = data.getValueList(BusinessInsightsValue.CANCELLATIONS_LAST_YEAR);
        assertSumEquals(0, cancellationsLastYear);

        List<Number> roomRevenueLastYear = data.getValueList(BusinessInsightsValue.ROOM_REVENUE_LAST_YEAR);
        assertSumEquals(100, roomRevenueLastYear);

        List<Number> adrLastYear = data.getValueList(BusinessInsightsValue.AVG_DAILY_RATE_LAST_YEAR);
        assertSumEquals(50, adrLastYear);

        List<Number> revParLastYear = data.getValueList(BusinessInsightsValue.REVENUE_PER_AVAILABLE_ROOM_LAST_YEAR);
        assertSumEquals(20, revParLastYear);

        List<Number> netRoomRevenueLastYear = data.getValueList(BusinessInsightsValue.NET_REVENUE_LAST_YEAR);
        assertSumEquals(100, netRoomRevenueLastYear);

        List<Number> netADRLastYear = data.getValueList(BusinessInsightsValue.NET_AVG_DAILY_RATE_LAST_YEAR);
        assertSumEquals(50, netADRLastYear);

        List<Number> netRevParLastYear = data.getValueList(BusinessInsightsValue.NET_REVENUE_PER_AVAILABLE_ROOM_LAST_YEAR);
        assertSumEquals(20, netRevParLastYear);
    }

    @Test
    void testGetLastYearDataConsideringAsOfDateWithAddCancellationToggleOnForPastDate() {
        tenantCrudService().executeUpdateByNativeQuery("truncate table Reservation_Night ");
        tenantCrudService().executeUpdateByNativeQuery("truncate table Accom_Activity ");

        LocalDate date = LocalDate.parse("2022-10-12");
        LocalDate asOfDate = LocalDate.parse("2022-11-11");
        LocalDate arrivalDate = LocalDate.parse("2021-10-13");
        LocalDate departureDate = LocalDate.parse("2021-10-15");
        LocalDate bookingDate = LocalDate.parse("2021-10-05");
        LocalDate cancellationDate = LocalDate.parse("2021-10-11");

        String insertQuery = " insert into Accom_Activity(Property_ID,Occupancy_DT,SnapShot_DTTM,Accom_Type_ID,Accom_Capacity,Rooms_Sold,Rooms_Not_Avail_Maint,Rooms_Not_Avail_Other,Arrivals,Departures,Cancellations,No_Shows,Room_Revenue,Food_Revenue,Total_Revenue,File_Metadata_ID)" +
                " values(5,'2021-10-13','2021-10-15 00:00:00',4,7,5,1,1,5,6,0,0,250.00000,150.00000,400.00000,1);";
        tenantCrudService().executeUpdateByNativeQuery(insertQuery);

        insertDayUseTransactionsIntoReservationNight(DateUtil.convertLocalDateToJavaUtilDate(arrivalDate),
                DateUtil.convertLocalDateToJavaUtilDate(departureDate),
                DateUtil.convertLocalDateToJavaUtilDate(bookingDate),
                DateUtil.convertLocalDateToJavaUtilDate(cancellationDate));

        BusinessInsightsCriteria criteria = new BusinessInsightsCriteria();
        criteria.setCategory(BusinessInsightsCategory.OCCUPANCY_DATE);
        criteria.addValue(BusinessInsightsValue.OCCUPANCY_ON_BOOKS_LAST_YEAR);
        criteria.addValue(BusinessInsightsValue.CANCELLATIONS_LAST_YEAR);
        criteria.addValue(BusinessInsightsValue.ROOM_REVENUE_LAST_YEAR);
        criteria.addValue(BusinessInsightsValue.AVG_DAILY_RATE_LAST_YEAR);
        criteria.addValue(BusinessInsightsValue.REVENUE_PER_AVAILABLE_ROOM_LAST_YEAR);
        criteria.addValue(BusinessInsightsValue.NET_REVENUE_LAST_YEAR);
        criteria.addValue(BusinessInsightsValue.NET_AVG_DAILY_RATE_LAST_YEAR);
        criteria.addValue(BusinessInsightsValue.NET_REVENUE_PER_AVAILABLE_ROOM_LAST_YEAR);
        criteria.setStartDate(date);
        criteria.setEndDate(date);
        criteria.setAsOfDate(asOfDate);

        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.POST_DEPARTURE_REVENUE_ADJUSTMENT_ENABLED)).thenReturn(FALSE);
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.ADD_CANCELLATIONS_TO_OCCUPANCY_ON_BOOKS_STLY_FOR_BUSINESS_INSIGHT)).thenReturn(TRUE);
        BusinessInsightsData data = service.getBusinessInsightsData(criteria);
        assertEquals(8, data.getValueTypes().size());

        List<Number> occupancyOnBooksLastYear = data.getValueList(BusinessInsightsValue.OCCUPANCY_ON_BOOKS_LAST_YEAR);
        assertSumEquals(1, occupancyOnBooksLastYear);

        List<Number> cancellationsLastYear = data.getValueList(BusinessInsightsValue.CANCELLATIONS_LAST_YEAR);
        assertSumEquals(1, cancellationsLastYear);

        List<Number> roomRevenueLastYear = data.getValueList(BusinessInsightsValue.ROOM_REVENUE_LAST_YEAR);
        assertSumEquals(50, roomRevenueLastYear);

        List<Number> adrLastYear = data.getValueList(BusinessInsightsValue.AVG_DAILY_RATE_LAST_YEAR);
        assertSumEquals(50, adrLastYear);

        List<Number> revParLastYear = data.getValueList(BusinessInsightsValue.REVENUE_PER_AVAILABLE_ROOM_LAST_YEAR);
        assertSumEquals(10, revParLastYear);

        List<Number> netRoomRevenueLastYear = data.getValueList(BusinessInsightsValue.NET_REVENUE_LAST_YEAR);
        assertSumEquals(50, netRoomRevenueLastYear);

        List<Number> netADRLastYear = data.getValueList(BusinessInsightsValue.NET_AVG_DAILY_RATE_LAST_YEAR);
        assertSumEquals(50, netADRLastYear);

        List<Number> netRevParLastYear = data.getValueList(BusinessInsightsValue.NET_REVENUE_PER_AVAILABLE_ROOM_LAST_YEAR);
        assertSumEquals(10, netRevParLastYear);
    }

    @Test
    void testGetLastYearDataConsideringAsOfDateWithAddCancellationToggleOnForPastFutureDateRange() {
        tenantCrudService().executeUpdateByNativeQuery("truncate table Reservation_Night ");
        tenantCrudService().executeUpdateByNativeQuery("truncate table Accom_Activity ");

        LocalDate startDate = LocalDate.parse("2022-11-10");
        LocalDate endDate = LocalDate.parse("2022-11-13");
        LocalDate asOfDate = LocalDate.parse("2022-11-11");

        tenantCrudService().executeUpdateByNativeQuery(" insert into Accom_Activity(Property_ID,Occupancy_DT,SnapShot_DTTM,Accom_Type_ID,Accom_Capacity,Rooms_Sold,Rooms_Not_Avail_Maint,Rooms_Not_Avail_Other,Arrivals,Departures,Cancellations,No_Shows,Room_Revenue,Food_Revenue,Total_Revenue,File_Metadata_ID)" +
                " values(5,'2021-11-11','2021-11-15 00:00:00',4,7,5,1,1,5,6,0,0,250.00000,150.00000,400.00000,1);");

        tenantCrudService().executeUpdateByNativeQuery(" insert into Accom_Activity(Property_ID,Occupancy_DT,SnapShot_DTTM,Accom_Type_ID,Accom_Capacity,Rooms_Sold,Rooms_Not_Avail_Maint,Rooms_Not_Avail_Other,Arrivals,Departures,Cancellations,No_Shows,Room_Revenue,Food_Revenue,Total_Revenue,File_Metadata_ID)" +
                " values(5,'2021-11-12','2021-11-15 00:00:00',4,7,5,1,1,5,6,0,0,250.00000,150.00000,400.00000,1);");

        tenantCrudService().executeUpdateByNativeQuery(" insert into Accom_Activity(Property_ID,Occupancy_DT,SnapShot_DTTM,Accom_Type_ID,Accom_Capacity,Rooms_Sold,Rooms_Not_Avail_Maint,Rooms_Not_Avail_Other,Arrivals,Departures,Cancellations,No_Shows,Room_Revenue,Food_Revenue,Total_Revenue,File_Metadata_ID)" +
                " values(5,'2021-11-13','2021-11-15 00:00:00',4,7,5,1,1,5,6,0,0,250.00000,150.00000,400.00000,1);");

        tenantCrudService().executeUpdateByNativeQuery(" insert into Accom_Activity(Property_ID,Occupancy_DT,SnapShot_DTTM,Accom_Type_ID,Accom_Capacity,Rooms_Sold,Rooms_Not_Avail_Maint,Rooms_Not_Avail_Other,Arrivals,Departures,Cancellations,No_Shows,Room_Revenue,Food_Revenue,Total_Revenue,File_Metadata_ID)" +
                " values(5,'2021-11-14','2021-11-15 00:00:00',4,7,5,1,1,5,6,0,0,250.00000,150.00000,400.00000,1);");

        tenantCrudService().executeUpdateByNativeQuery("insert into Reservation_Night([File_Metadata_ID],[Property_ID],[Reservation_Identifier],[Individual_Status],[Arrival_DT],[Departure_DT], \n" +
                "[Booking_DT],[Cancellation_DT],[Booked_Accom_Type_Code],[Accom_Type_ID],[Mkt_Seg_ID],[Room_Number],[Booking_type],[Room_Revenue],[Food_Revenue],[Total_Revenue],[Rate_Code], \n" +
                "[Rate_Value],[Number_Children],[Number_Adults],[Occupancy_DT])  \n" +
                "values(1, 000005, '***********','CO','2021-11-11','2021-11-15','2021-11-05',null,'SXBL', 4, 7, 1108, 'IN', 50.00, 0.00, 0.00,'SHHQO1',50.00,0,2, '2021-11-11');");

        tenantCrudService().executeUpdateByNativeQuery("insert into Reservation_Night([File_Metadata_ID],[Property_ID],[Reservation_Identifier],[Individual_Status],[Arrival_DT],[Departure_DT], \n" +
                "[Booking_DT],[Cancellation_DT],[Booked_Accom_Type_Code],[Accom_Type_ID],[Mkt_Seg_ID],[Room_Number],[Booking_type],[Room_Revenue],[Food_Revenue],[Total_Revenue],[Rate_Code], \n" +
                "[Rate_Value],[Number_Children],[Number_Adults],[Occupancy_DT])  \n" +
                "values(1, 000005, '12345678902','CO','2021-11-12','2021-11-15','2021-11-05',null,'SXBL', 4, 7, 1108, 'IN', 50.00, 0.00, 0.00,'SHHQO1',50.00,0,2, '2021-11-12');");

        tenantCrudService().executeUpdateByNativeQuery("insert into Reservation_Night([File_Metadata_ID],[Property_ID],[Reservation_Identifier],[Individual_Status],[Arrival_DT],[Departure_DT], \n" +
                "[Booking_DT],[Cancellation_DT],[Booked_Accom_Type_Code],[Accom_Type_ID],[Mkt_Seg_ID],[Room_Number],[Booking_type],[Room_Revenue],[Food_Revenue],[Total_Revenue],[Rate_Code], \n" +
                "[Rate_Value],[Number_Children],[Number_Adults],[Occupancy_DT])  \n" +
                "values(1, 000005, '***********','CO','2021-11-13','2021-11-15','2021-11-05',null,'SXBL', 4, 7, 1108, 'IN', 50.00, 0.00, 0.00,'SHHQO1',50.00,0,2, '2021-11-13');");

        tenantCrudService().executeUpdateByNativeQuery("insert into Reservation_Night([File_Metadata_ID],[Property_ID],[Reservation_Identifier],[Individual_Status],[Arrival_DT],[Departure_DT], \n" +
                "[Booking_DT],[Cancellation_DT],[Booked_Accom_Type_Code],[Accom_Type_ID],[Mkt_Seg_ID],[Room_Number],[Booking_type],[Room_Revenue],[Food_Revenue],[Total_Revenue],[Rate_Code], \n" +
                "[Rate_Value],[Number_Children],[Number_Adults],[Occupancy_DT])  \n" +
                "values(1, 000005, '12345678904','CO','2021-11-14','2021-11-15','2021-11-05',null,'SXBL', 4, 7, 1108, 'IN', 50.00, 0.00, 0.00,'SHHQO1',50.00,0,2, '2021-11-14');");

        tenantCrudService().executeUpdateByNativeQuery("insert into Reservation_Night([File_Metadata_ID],[Property_ID],[Reservation_Identifier],[Individual_Status],[Arrival_DT],[Departure_DT], \n" +
                "[Booking_DT],[Cancellation_DT],[Booked_Accom_Type_Code],[Accom_Type_ID],[Mkt_Seg_ID],[Room_Number],[Booking_type],[Room_Revenue],[Food_Revenue],[Total_Revenue],[Rate_Code], \n" +
                "[Rate_Value],[Number_Children],[Number_Adults],[Occupancy_DT])  \n" +
                "values(1, 000005, '***********','XX','2021-11-14','2021-11-15','2021-11-05','2021-11-13','SXBL', 4, 7, 1108, 'IN', 00.00, 0.00, 0.00,'SHHQO1',50.00,0,2, '2021-11-14');");

        BusinessInsightsCriteria criteria = new BusinessInsightsCriteria();
        criteria.setCategory(BusinessInsightsCategory.OCCUPANCY_DATE);
        criteria.addValue(BusinessInsightsValue.OCCUPANCY_ON_BOOKS_LAST_YEAR);
        criteria.addValue(BusinessInsightsValue.CANCELLATIONS_LAST_YEAR);
        criteria.addValue(BusinessInsightsValue.ROOM_REVENUE_LAST_YEAR);
        criteria.addValue(BusinessInsightsValue.AVG_DAILY_RATE_LAST_YEAR);
        criteria.addValue(BusinessInsightsValue.REVENUE_PER_AVAILABLE_ROOM_LAST_YEAR);
        criteria.addValue(BusinessInsightsValue.NET_REVENUE_LAST_YEAR);
        criteria.addValue(BusinessInsightsValue.NET_AVG_DAILY_RATE_LAST_YEAR);
        criteria.addValue(BusinessInsightsValue.NET_REVENUE_PER_AVAILABLE_ROOM_LAST_YEAR);
        criteria.setStartDate(startDate);
        criteria.setEndDate(endDate);
        criteria.setAsOfDate(asOfDate);

        lenient().when(propertyConfigParamService.isIncludeCancellationNoShowRevenueEnabled()).thenReturn(TRUE);
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.POST_DEPARTURE_REVENUE_ADJUSTMENT_ENABLED)).thenReturn(FALSE);
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.ADD_CANCELLATIONS_TO_OCCUPANCY_ON_BOOKS_STLY_FOR_BUSINESS_INSIGHT)).thenReturn(TRUE);
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.BUSINESS_INSIGHTS_REPORT_CANCELLATIONS_NO_SHOW_SEPARATION)).thenReturn(TRUE);
        BusinessInsightsData data = service.getBusinessInsightsData(criteria);
        assertEquals(8, data.getValueTypes().size());

        List<Number> occupancyOnBooksLastYear = data.getValueList(BusinessInsightsValue.OCCUPANCY_ON_BOOKS_LAST_YEAR);
        assertSumEquals(5, occupancyOnBooksLastYear);

        List<Number> cancellationsLastYear = data.getValueList(BusinessInsightsValue.CANCELLATIONS_LAST_YEAR);
        assertSumEquals(0, cancellationsLastYear);

        List<Number> roomRevenueLastYear = data.getValueList(BusinessInsightsValue.ROOM_REVENUE_LAST_YEAR);
        assertSumEquals(250, roomRevenueLastYear);

        List<Number> adrLastYear = data.getValueList(BusinessInsightsValue.AVG_DAILY_RATE_LAST_YEAR);
        assertSumEquals(200, adrLastYear);

        List<Number> revParLastYear = data.getValueList(BusinessInsightsValue.REVENUE_PER_AVAILABLE_ROOM_LAST_YEAR);
        assertSumEquals(50, revParLastYear);

        List<Number> netRoomRevenueLastYear = data.getValueList(BusinessInsightsValue.NET_REVENUE_LAST_YEAR);
        assertSumEquals(250, netRoomRevenueLastYear);

        List<Number> netADRLastYear = data.getValueList(BusinessInsightsValue.NET_AVG_DAILY_RATE_LAST_YEAR);
        assertSumEquals(200, netADRLastYear);

        List<Number> netRevParLastYear = data.getValueList(BusinessInsightsValue.NET_REVENUE_PER_AVAILABLE_ROOM_LAST_YEAR);
        assertSumEquals(50, netRevParLastYear);
    }

    @Test
    public void testGetLastYearDataWithMissingDatesConsideringAsOfDateWithAddCancellationToggleOn() {
        tenantCrudService().executeUpdateByNativeQuery("truncate table Reservation_Night ");
        tenantCrudService().executeUpdateByNativeQuery("truncate table Accom_Activity ");

        LocalDate startDate = LocalDate.parse("2022-12-12");
        LocalDate endDate = LocalDate.parse("2022-12-16");
        LocalDate asOfDate = LocalDate.parse("2022-11-11");

        tenantCrudService().executeUpdateByNativeQuery(" insert into Accom_Activity(Property_ID,Occupancy_DT,SnapShot_DTTM,Accom_Type_ID,Accom_Capacity,Rooms_Sold,Rooms_Not_Avail_Maint,Rooms_Not_Avail_Other,Arrivals,Departures,Cancellations,No_Shows,Room_Revenue,Food_Revenue,Total_Revenue,File_Metadata_ID)" +
                " values(5,'2021-12-13','2021-12-15 00:00:00',4,7,5,1,1,5,6,0,0,250.00000,150.00000,400.00000,1);");

        tenantCrudService().executeUpdateByNativeQuery(" insert into Accom_Activity(Property_ID,Occupancy_DT,SnapShot_DTTM,Accom_Type_ID,Accom_Capacity,Rooms_Sold,Rooms_Not_Avail_Maint,Rooms_Not_Avail_Other,Arrivals,Departures,Cancellations,No_Shows,Room_Revenue,Food_Revenue,Total_Revenue,File_Metadata_ID)" +
                " values(5,'2021-12-14','2021-12-15 00:00:00',4,7,5,1,1,5,6,0,0,250.00000,150.00000,400.00000,1);");

        tenantCrudService().executeUpdateByNativeQuery(" insert into Accom_Activity(Property_ID,Occupancy_DT,SnapShot_DTTM,Accom_Type_ID,Accom_Capacity,Rooms_Sold,Rooms_Not_Avail_Maint,Rooms_Not_Avail_Other,Arrivals,Departures,Cancellations,No_Shows,Room_Revenue,Food_Revenue,Total_Revenue,File_Metadata_ID)" +
                " values(5,'2021-12-15','2021-12-15 00:00:00',4,7,5,1,1,5,6,0,0,250.00000,150.00000,400.00000,1);");

        tenantCrudService().executeUpdateByNativeQuery(" insert into Accom_Activity(Property_ID,Occupancy_DT,SnapShot_DTTM,Accom_Type_ID,Accom_Capacity,Rooms_Sold,Rooms_Not_Avail_Maint,Rooms_Not_Avail_Other,Arrivals,Departures,Cancellations,No_Shows,Room_Revenue,Food_Revenue,Total_Revenue,File_Metadata_ID)" +
                " values(5,'2021-12-16','2021-12-16 00:00:00',4,7,5,1,1,0,0,0,0,0.00000,0.00000,0.00000,1);");

        tenantCrudService().executeUpdateByNativeQuery(" insert into Accom_Activity(Property_ID,Occupancy_DT,SnapShot_DTTM,Accom_Type_ID,Accom_Capacity,Rooms_Sold,Rooms_Not_Avail_Maint,Rooms_Not_Avail_Other,Arrivals,Departures,Cancellations,No_Shows,Room_Revenue,Food_Revenue,Total_Revenue,File_Metadata_ID)" +
                " values(5,'2021-12-17','2021-12-17 00:00:00',4,7,5,1,1,0,0,0,0,0.00000,0.00000,0.00000,1);");

        tenantCrudService().executeUpdateByNativeQuery("insert into Reservation_Night([File_Metadata_ID],[Property_ID],[Reservation_Identifier],[Individual_Status],[Arrival_DT],[Departure_DT], \n" +
                "[Booking_DT],[Cancellation_DT],[Booked_Accom_Type_Code],[Accom_Type_ID],[Mkt_Seg_ID],[Room_Number],[Booking_type],[Room_Revenue],[Food_Revenue],[Total_Revenue],[Rate_Code], \n" +
                "[Rate_Value],[Number_Children],[Number_Adults],[Occupancy_DT])  \n" +
                "values(1, 000005, '***********','CO','2021-12-13','2021-12-15','2021-11-05',null,'SXBL', 4, 7, 1108, 'IN', 50.00, 0.00, 0.00,'SHHQO1',50.00,0,2, '2021-12-13');");

        tenantCrudService().executeUpdateByNativeQuery("insert into Reservation_Night([File_Metadata_ID],[Property_ID],[Reservation_Identifier],[Individual_Status],[Arrival_DT],[Departure_DT], \n" +
                "[Booking_DT],[Cancellation_DT],[Booked_Accom_Type_Code],[Accom_Type_ID],[Mkt_Seg_ID],[Room_Number],[Booking_type],[Room_Revenue],[Food_Revenue],[Total_Revenue],[Rate_Code], \n" +
                "[Rate_Value],[Number_Children],[Number_Adults],[Occupancy_DT])  \n" +
                "values(1, 000005, '12345678904','CO','2021-12-14','2021-12-15','2021-11-05',null,'SXBL', 4, 7, 1108, 'IN', 50.00, 0.00, 0.00,'SHHQO1',50.00,0,2, '2021-12-14');");

        tenantCrudService().executeUpdateByNativeQuery("insert into Reservation_Night([File_Metadata_ID],[Property_ID],[Reservation_Identifier],[Individual_Status],[Arrival_DT],[Departure_DT], \n" +
                "[Booking_DT],[Cancellation_DT],[Booked_Accom_Type_Code],[Accom_Type_ID],[Mkt_Seg_ID],[Room_Number],[Booking_type],[Room_Revenue],[Food_Revenue],[Total_Revenue],[Rate_Code], \n" +
                "[Rate_Value],[Number_Children],[Number_Adults],[Occupancy_DT])  \n" +
                "values(1, 000005, '***********','XX','2021-12-14','2021-12-15','2021-11-05','2021-11-13','SXBL', 4, 7, 1108, 'IN', 00.00, 0.00, 0.00,'SHHQO1',50.00,0,2, '2021-12-14');");


        BusinessInsightsCriteria criteria = new BusinessInsightsCriteria();
        criteria.setCategory(BusinessInsightsCategory.OCCUPANCY_DATE);
        criteria.addValue(BusinessInsightsValue.OCCUPANCY_ON_BOOKS_LAST_YEAR);
        criteria.addValue(BusinessInsightsValue.CANCELLATIONS_LAST_YEAR);
        criteria.addValue(BusinessInsightsValue.ROOM_REVENUE_LAST_YEAR);
        criteria.addValue(BusinessInsightsValue.AVG_DAILY_RATE_LAST_YEAR);
        criteria.addValue(BusinessInsightsValue.REVENUE_PER_AVAILABLE_ROOM_LAST_YEAR);
        criteria.addValue(BusinessInsightsValue.NET_REVENUE_LAST_YEAR);
        criteria.addValue(BusinessInsightsValue.NET_AVG_DAILY_RATE_LAST_YEAR);
        criteria.addValue(BusinessInsightsValue.NET_REVENUE_PER_AVAILABLE_ROOM_LAST_YEAR);
        criteria.setStartDate(startDate);
        criteria.setEndDate(endDate);
        criteria.setAsOfDate(asOfDate);

        int expectedDays = (int) (DAYS.between(startDate, endDate) + 1);
        lenient().when(propertyConfigParamService.isIncludeCancellationNoShowRevenueEnabled()).thenReturn(TRUE);
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.POST_DEPARTURE_REVENUE_ADJUSTMENT_ENABLED)).thenReturn(FALSE);
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.ADD_CANCELLATIONS_TO_OCCUPANCY_ON_BOOKS_STLY_FOR_BUSINESS_INSIGHT)).thenReturn(TRUE);
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.BUSINESS_INSIGHTS_REPORT_CANCELLATIONS_NO_SHOW_SEPARATION)).thenReturn(TRUE);
        BusinessInsightsData data = service.getBusinessInsightsData(criteria);
        assertEquals(8, data.getValueTypes().size());

        List<Number> occupancyOnBooksLastYear = data.getValueList(BusinessInsightsValue.OCCUPANCY_ON_BOOKS_LAST_YEAR);
        assertEquals(expectedDays, occupancyOnBooksLastYear.size());
        assertSumEquals(3, occupancyOnBooksLastYear);

        List<Number> cancellationsLastYear = data.getValueList(BusinessInsightsValue.CANCELLATIONS_LAST_YEAR);
        assertEquals(expectedDays, cancellationsLastYear.size());
        assertSumEquals(0, cancellationsLastYear);

        List<Number> roomRevenueLastYear = data.getValueList(BusinessInsightsValue.ROOM_REVENUE_LAST_YEAR);
        assertEquals(expectedDays, roomRevenueLastYear.size());
        assertSumEquals(150, roomRevenueLastYear);

        List<Number> adrLastYear = data.getValueList(BusinessInsightsValue.AVG_DAILY_RATE_LAST_YEAR);
        assertEquals(expectedDays, adrLastYear.size());
        assertSumEquals(100, adrLastYear);

        List<Number> revParLastYear = data.getValueList(BusinessInsightsValue.REVENUE_PER_AVAILABLE_ROOM_LAST_YEAR);
        assertEquals(expectedDays, revParLastYear.size());
        assertSumEquals(30, revParLastYear);

        List<Number> netRoomRevenueLastYear = data.getValueList(BusinessInsightsValue.NET_REVENUE_LAST_YEAR);
        assertEquals(expectedDays, netRoomRevenueLastYear.size());
        assertSumEquals(150, netRoomRevenueLastYear);

        List<Number> netADRLastYear = data.getValueList(BusinessInsightsValue.NET_AVG_DAILY_RATE_LAST_YEAR);
        assertEquals(expectedDays, netADRLastYear.size());
        assertSumEquals(100, netADRLastYear);

        List<Number> netRevParLastYear = data.getValueList(BusinessInsightsValue.NET_REVENUE_PER_AVAILABLE_ROOM_LAST_YEAR);
        assertEquals(expectedDays, netRevParLastYear.size());
        assertSumEquals(30, netRevParLastYear);
    }

    @Test
    public void testGetTuplesAddedWhereClauseForCancellations() {
        lenient().when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.BUSINESS_INSIGHTS_REPORT_CANCELLATIONS_NO_SHOW_SEPARATION)).thenReturn(TRUE);
        BusinessInsightsCriteria criteria = new BusinessInsightsCriteria();
        criteria.setCategory(BusinessInsightsCategory.OCCUPANCY_DATE);
        criteria.setStartDate(LocalDate.now().minusDays(90));
        criteria.setEndDate(LocalDate.now().plusDays(30));
        criteria.addValue(BusinessInsightsValue.ROOMS_SOLD);
        criteria.addValue(BusinessInsightsValue.ARRIVALS);
        criteria.addValue(BusinessInsightsValue.DEPARTURES);
        criteria.addValue(BusinessInsightsValue.CANCELLATIONS);
        criteria.addValue(BusinessInsightsValue.ROOM_REVENUE);
        List<Tuple> tuples = service.getTuples(criteria, false, service.getViewEntityClassBasedOnChannelCostVersion());

        assertInDateRange(criteria.getStartDate(), criteria.getEndDate(), BusinessInsightsValue.OCCUPANCY_DATE, tuples);
    }

    @Test
    public void testGetNoShowAndCancellationWhenBusinessInsightsReportCancellationsNoShowSeparationEnabled() {
        lenient().when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.BUSINESS_INSIGHTS_REPORT_CANCELLATIONS_NO_SHOW_SEPARATION)).thenReturn(TRUE);
        LocalDate startDate = LocalDate.now().minusDays(90);
        LocalDate endDate = LocalDate.now().plusDays(30);
        BusinessInsightsCriteria criteria = getBusinessInsightCriteria(startDate, endDate);

        BusinessInsightsData data = service.getBusinessInsightsData(criteria);

        int expectedDays = (int) (DAYS.between(startDate, endDate) + 1);

        assertNoShowAndCancellationSum(data, expectedDays);
    }


    @Test
    public void testGetCancellationWhenBusinessInsightsReportCancellationsNoShowSeparationDisabled() {
        lenient().when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.BUSINESS_INSIGHTS_REPORT_CANCELLATIONS_NO_SHOW_SEPARATION)).thenReturn(FALSE);
        LocalDate startDate = LocalDate.now().minusDays(90);
        LocalDate endDate = LocalDate.now().plusDays(30);
        BusinessInsightsCriteria criteria = new BusinessInsightsCriteria();
        criteria.setCategory(BusinessInsightsCategory.OCCUPANCY_DATE);
        criteria.addValue(BusinessInsightsValue.CANCELLATIONS);
        criteria.addValue(BusinessInsightsValue.CANCELLATIONS_LAST_YEAR);
        criteria.setStartDate(startDate);
        criteria.setEndDate(endDate);
        criteria.setAsOfDate(endDate);
        BusinessInsightsData data = service.getBusinessInsightsData(criteria);

        assertEquals(2, data.getValueTypes().size());

        int expectedDays = (int) (DAYS.between(startDate, endDate) + 1);

        List<Number> cancellations = data.getValueList(BusinessInsightsValue.CANCELLATIONS);
        assertEquals(expectedDays, cancellations.size());
        assertSumEquals(2, cancellations);
    }

    @Test
    public void testGetNoShowAndCancellationWhenIncludeNoShowCancellationRevenueEnabled() {
        lenient().when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.BUSINESS_INSIGHTS_REPORT_CANCELLATIONS_NO_SHOW_SEPARATION)).thenReturn(TRUE);
        lenient().when(propertyConfigParamService.isIncludeCancellationNoShowRevenueEnabled()).thenReturn(TRUE);

        LocalDate startDate = LocalDate.now().minusDays(90);
        LocalDate endDate = LocalDate.now().plusDays(30);
        BusinessInsightsCriteria criteria = getBusinessInsightCriteria(startDate, endDate);

        BusinessInsightsData data = service.getBusinessInsightsData(criteria);

        int expectedDays = (int) (DAYS.between(startDate, endDate) + 1);

        assertNoShowAndCancellationSum(data, expectedDays);
    }

    @Test
    public void testGetNoShowAndCancellationWhenPopulateDayUseReservationEnabled() {
        lenient().when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.BUSINESS_INSIGHTS_REPORT_CANCELLATIONS_NO_SHOW_SEPARATION)).thenReturn(TRUE);
        lenient().when(propertyConfigParamService.isDayUsePopulationEnabled()).thenReturn(TRUE);
        LocalDate startDate = LocalDate.now().minusDays(90);
        LocalDate endDate = LocalDate.now().plusDays(30);
        BusinessInsightsCriteria criteria = getBusinessInsightCriteria(startDate, endDate);

        BusinessInsightsData data = service.getBusinessInsightsData(criteria);

        int expectedDays = (int) (DAYS.between(startDate, endDate) + 1);

        assertNoShowAndCancellationSum(data, expectedDays);
    }

    @Test
    public void testGetNoShowAndCancellationWhenIncludeNoShowCancellationRevenueEnabledAndChannelCostV3Enabled() {
        lenient().when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.BUSINESS_INSIGHTS_REPORT_CANCELLATIONS_NO_SHOW_SEPARATION)).thenReturn(TRUE);
        lenient().when(propertyConfigParamService.isIncludeCancellationNoShowRevenueEnabled()).thenReturn(TRUE);
        lenient().when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.CHANNEL_COST_V3)).thenReturn(true);
        LocalDate startDate = LocalDate.now().minusDays(90);
        LocalDate endDate = LocalDate.now().plusDays(30);
        BusinessInsightsCriteria criteria = getBusinessInsightCriteria(startDate, endDate);

        BusinessInsightsData data = service.getBusinessInsightsData(criteria);

        int expectedDays = (int) (DAYS.between(startDate, endDate) + 1);

        assertNoShowAndCancellationSum(data, expectedDays);
    }

    @Test
    public void testGetNoShowAndCancellationWhenPopulateDayUseReservationEnabledAndChannelCostV3Enabled() {
        lenient().when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.BUSINESS_INSIGHTS_REPORT_CANCELLATIONS_NO_SHOW_SEPARATION)).thenReturn(TRUE);
        lenient().when(propertyConfigParamService.isDayUsePopulationEnabled()).thenReturn(TRUE);
        lenient().when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.CHANNEL_COST_V3)).thenReturn(true);
        LocalDate startDate = LocalDate.now().minusDays(90);
        LocalDate endDate = LocalDate.now().plusDays(30);
        BusinessInsightsCriteria criteria = getBusinessInsightCriteria(startDate, endDate);

        BusinessInsightsData data = service.getBusinessInsightsData(criteria);

        int expectedDays = (int) (DAYS.between(startDate, endDate) + 1);

        assertNoShowAndCancellationSum(data, expectedDays);
    }
    @Test
    public void testGetNoShowAndCancellationWhenIncludeNoShowCancellationRevenueDisabledAndChannelCostV3Enabled() {
        lenient().when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.BUSINESS_INSIGHTS_REPORT_CANCELLATIONS_NO_SHOW_SEPARATION)).thenReturn(TRUE);
        lenient().when(propertyConfigParamService.isIncludeCancellationNoShowRevenueEnabled()).thenReturn(FALSE);
        lenient().when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.CHANNEL_COST_V3)).thenReturn(TRUE);
        LocalDate startDate = LocalDate.now().minusDays(90);
        LocalDate endDate = LocalDate.now().plusDays(30);
        BusinessInsightsCriteria criteria = getBusinessInsightCriteria(startDate, endDate);

        BusinessInsightsData data = service.getBusinessInsightsData(criteria);

        int expectedDays = (int) (DAYS.between(startDate, endDate) + 1);

        assertNoShowAndCancellationSum(data, expectedDays);
    }


    @Test
    public void testGetNoShowAndCancellationWhenIncludeNoShowCancellationRevenueEnabledAndSimplifiedChannelCostEnabled() {
        lenient().when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.BUSINESS_INSIGHTS_REPORT_CANCELLATIONS_NO_SHOW_SEPARATION)).thenReturn(TRUE);
        lenient().when(propertyConfigParamService.isIncludeCancellationNoShowRevenueEnabled()).thenReturn(TRUE);
        lenient().when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.SIMPLIFIED_CHANNEL_COST_ENABLED)).thenReturn(TRUE);
        LocalDate startDate = LocalDate.now().minusDays(90);
        LocalDate endDate = LocalDate.now().plusDays(30);
        BusinessInsightsCriteria criteria = getBusinessInsightCriteria(startDate, endDate);
        BusinessInsightsData data = service.getBusinessInsightsData(criteria);

        int expectedDays = (int) (DAYS.between(startDate, endDate) + 1);

        assertNoShowAndCancellationSum(data, expectedDays);
    }


    @Test
    public void testGetNoShowAndCancellationWhenIncludeNoShowCancellationRevenueDisabledAndSimplifiedChannelCostEnabled() {
        lenient().when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.BUSINESS_INSIGHTS_REPORT_CANCELLATIONS_NO_SHOW_SEPARATION)).thenReturn(TRUE);
        lenient().when(propertyConfigParamService.isIncludeCancellationNoShowRevenueEnabled()).thenReturn(FALSE);
        lenient().when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.SIMPLIFIED_CHANNEL_COST_ENABLED)).thenReturn(TRUE);

        LocalDate startDate = LocalDate.now().minusDays(90);
        LocalDate endDate = LocalDate.now().plusDays(30);
        BusinessInsightsCriteria criteria = getBusinessInsightCriteria(startDate, endDate);

        BusinessInsightsData data = service.getBusinessInsightsData(criteria);

        int expectedDays = (int) (DAYS.between(startDate, endDate) + 1);

        assertNoShowAndCancellationSum(data, expectedDays);
    }

    @Test
    public void testGetNoShowAndCancellationWhenPopulateDayUseReservationEnabledAndSimplifiedChannelCostEnabled() {
        lenient().when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.BUSINESS_INSIGHTS_REPORT_CANCELLATIONS_NO_SHOW_SEPARATION)).thenReturn(TRUE);
        lenient().when(propertyConfigParamService.isDayUsePopulationEnabled()).thenReturn(TRUE);
        lenient().when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.SIMPLIFIED_CHANNEL_COST_ENABLED)).thenReturn(TRUE);

        LocalDate startDate = LocalDate.now().minusDays(90);
        LocalDate endDate = LocalDate.now().plusDays(30);
        BusinessInsightsCriteria criteria = getBusinessInsightCriteria(startDate, endDate);

        BusinessInsightsData data = service.getBusinessInsightsData(criteria);

        int expectedDays = (int) (DAYS.between(startDate, endDate) + 1);

        assertNoShowAndCancellationSum(data, expectedDays);
    }

    @Test
    public void testGetNoShowAndCancellationWithAsOfDateWhenPopulateDayUseReservationEnabledAndSimplifiedChannelCostEnabled() {
        lenient().when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.ADD_CANCELLATIONS_TO_OCCUPANCY_ON_BOOKS_STLY_FOR_BUSINESS_INSIGHT)).thenReturn(TRUE);
        lenient().when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.BUSINESS_INSIGHTS_REPORT_CANCELLATIONS_NO_SHOW_SEPARATION)).thenReturn(TRUE);
        lenient().when(propertyConfigParamService.isDayUsePopulationEnabled()).thenReturn(TRUE);
        lenient().when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.SIMPLIFIED_CHANNEL_COST_ENABLED)).thenReturn(TRUE);

        LocalDate startDate = LocalDate.now().minusDays(90);
        LocalDate endDate = LocalDate.now().plusDays(30);
        BusinessInsightsCriteria criteria = getBusinessInsightCriteria(startDate, endDate);
        criteria.setAsOfDate(LocalDate.now());

        BusinessInsightsData data = service.getBusinessInsightsData(criteria);

        int expectedDays = (int) (DAYS.between(startDate, endDate) + 1);

        assertNoShowAndCancellationSum(data, expectedDays);
    }

    @Test
    public void testGetNoShowAndCancellationWhenAsOfDateLessThanOccupancyDatePopulateDayUseReservationEnabledAndSimplifiedChannelCostEnabled() {
        lenient().when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.BUSINESS_INSIGHTS_REPORT_CANCELLATIONS_NO_SHOW_SEPARATION)).thenReturn(TRUE);
        lenient().when(propertyConfigParamService.isDayUsePopulationEnabled()).thenReturn(TRUE);
        lenient().when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.SIMPLIFIED_CHANNEL_COST_ENABLED)).thenReturn(TRUE);

        LocalDate startDate = LocalDate.now().minusDays(90);
        LocalDate endDate = LocalDate.now().plusDays(30);
        BusinessInsightsCriteria criteria = getBusinessInsightCriteria(startDate, endDate);
        criteria.setAsOfDate(LocalDate.now().minusDays(10));

        BusinessInsightsData data = service.getBusinessInsightsData(criteria);

        int expectedDays = (int) (DAYS.between(startDate, endDate) + 1);

        List<Number> cancellations = data.getValueList(BusinessInsightsValue.CANCELLATIONS);
        assertEquals(expectedDays, cancellations.size());
        assertSumEquals(0, cancellations);

        List<Number> noShow = data.getValueList(BusinessInsightsValue.NO_SHOW);
        assertEquals(expectedDays, noShow.size());
        assertSumEquals(0, noShow);

        List<Number> noShowLastYear = data.getValueList(BusinessInsightsValue.NO_SHOW_LAST_YEAR);
        assertEquals(expectedDays, noShowLastYear.size());
        assertSumEquals(0, noShowLastYear);

        List<Number> cancellationsLastYear = data.getValueList(BusinessInsightsValue.CANCELLATIONS_LAST_YEAR);
        assertEquals(expectedDays, cancellationsLastYear.size());
        assertSumEquals(0, cancellationsLastYear);
    }

    @Test
    public void testGetCurrentYearDataConsideringAsOfDateWithAddCancellationToggleOn() {
        tenantCrudService().executeUpdateByNativeQuery("truncate table Reservation_Night ");
        tenantCrudService().executeUpdateByNativeQuery("truncate table Accom_Activity ");

        LocalDate date = LocalDate.parse("2021-12-13");
        LocalDate asOfDate = LocalDate.parse("2021-11-11");
        LocalDate arrivalDate = LocalDate.parse("2021-12-13");
        LocalDate departureDate = LocalDate.parse("2021-12-15");
        LocalDate bookingDate = LocalDate.parse("2021-11-05");
        LocalDate cancellationDate = LocalDate.parse("2021-12-11");


        String insertQuery = " insert into Accom_Activity(Property_ID,Occupancy_DT,SnapShot_DTTM,Accom_Type_ID,Accom_Capacity,Rooms_Sold,Rooms_Not_Avail_Maint,Rooms_Not_Avail_Other,Arrivals,Departures,Cancellations,No_Shows,Room_Revenue,Food_Revenue,Total_Revenue,File_Metadata_ID)" +
                " values(5,'2021-12-13','2021-12-15 00:00:00',4,7,5,1,1,5,6,0,0,250.00000,150.00000,400.00000,1);";
        tenantCrudService().executeUpdateByNativeQuery(insertQuery);

        insertDayUseTransactionsIntoReservationNight(DateUtil.convertLocalDateToJavaUtilDate(arrivalDate),
                DateUtil.convertLocalDateToJavaUtilDate(departureDate),
                DateUtil.convertLocalDateToJavaUtilDate(bookingDate),
                DateUtil.convertLocalDateToJavaUtilDate(cancellationDate));

        BusinessInsightsCriteria criteria = new BusinessInsightsCriteria();
        criteria.setCategory(BusinessInsightsCategory.OCCUPANCY_DATE);
        criteria.addValue(BusinessInsightsValue.ROOMS_SOLD);
        criteria.addValue(BusinessInsightsValue.CANCELLATIONS);
        criteria.addValue(BusinessInsightsValue.ROOM_REVENUE);
        criteria.addValue(BusinessInsightsValue.AVG_DAILY_RATE);
        criteria.addValue(BusinessInsightsValue.REVENUE_PER_AVAILABLE_ROOM);
        criteria.addValue(BusinessInsightsValue.NET_REVENUE);
        criteria.addValue(BusinessInsightsValue.NET_AVG_DAILY_RATE);
        criteria.addValue(BusinessInsightsValue.NET_REVENUE_PER_AVAILABLE_ROOM);
        criteria.setStartDate(date);
        criteria.setEndDate(date);
        criteria.setAsOfDate(asOfDate);

        lenient().when(propertyConfigParamService.isIncludeCancellationNoShowRevenueEnabled()).thenReturn(TRUE);
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.POST_DEPARTURE_REVENUE_ADJUSTMENT_ENABLED)).thenReturn(FALSE);
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.ADD_CANCELLATIONS_TO_OCCUPANCY_ON_BOOKS_STLY_FOR_BUSINESS_INSIGHT)).thenReturn(TRUE);
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.BUSINESS_INSIGHTS_REPORT_CANCELLATIONS_NO_SHOW_SEPARATION)).thenReturn(TRUE);

        BusinessInsightsData data = service.getBusinessInsightsData(criteria);
        assertEquals(8, data.getValueTypes().size());

        List<Number> occupancyOnBooksLastYear = data.getValueList(BusinessInsightsValue.ROOMS_SOLD);
        assertSumEquals(2, occupancyOnBooksLastYear);

        List<Number> cancellationsLastYear = data.getValueList(BusinessInsightsValue.CANCELLATIONS);
        assertSumEquals(0, cancellationsLastYear);

        List<Number> roomRevenueLastYear = data.getValueList(BusinessInsightsValue.ROOM_REVENUE);
        assertSumEquals(100, roomRevenueLastYear);

        List<Number> adrLastYear = data.getValueList(BusinessInsightsValue.AVG_DAILY_RATE);
        assertSumEquals(50, adrLastYear);

        List<Number> revParLastYear = data.getValueList(BusinessInsightsValue.REVENUE_PER_AVAILABLE_ROOM);
        assertSumEquals(20, revParLastYear);

        List<Number> netRoomRevenueLastYear = data.getValueList(BusinessInsightsValue.NET_REVENUE);
        assertSumEquals(100, netRoomRevenueLastYear);

        List<Number> netADRLastYear = data.getValueList(BusinessInsightsValue.NET_AVG_DAILY_RATE);
        assertSumEquals(50, netADRLastYear);

        List<Number> netRevParLastYear = data.getValueList(BusinessInsightsValue.NET_REVENUE_PER_AVAILABLE_ROOM);
        assertSumEquals(20, netRevParLastYear);
    }


    @Test
    void testGetCurrentYearDataConsideringAsOfDateWithAddCancellationToggleOnForPastDate() {
        tenantCrudService().executeUpdateByNativeQuery("truncate table Reservation_Night ");
        tenantCrudService().executeUpdateByNativeQuery("truncate table Accom_Activity ");

        LocalDate date = LocalDate.parse("2021-10-13");
        LocalDate asOfDate = LocalDate.parse("2023-11-12");
        LocalDate arrivalDate = LocalDate.parse("2021-10-13");
        LocalDate departureDate = LocalDate.parse("2021-10-15");
        LocalDate bookingDate = LocalDate.parse("2021-10-05");
        LocalDate cancellationDate = LocalDate.parse("2021-10-11");

        String insertQuery = " insert into Accom_Activity(Property_ID,Occupancy_DT,SnapShot_DTTM,Accom_Type_ID,Accom_Capacity,Rooms_Sold,Rooms_Not_Avail_Maint,Rooms_Not_Avail_Other,Arrivals,Departures,Cancellations,No_Shows,Room_Revenue,Food_Revenue,Total_Revenue,File_Metadata_ID)" +
                " values(5,'2021-10-13','2021-10-15 00:00:00',4,7,5,1,1,5,6,0,0,250.00000,150.00000,400.00000,1);";
        tenantCrudService().executeUpdateByNativeQuery(insertQuery);

        insertDayUseTransactionsIntoReservationNight(DateUtil.convertLocalDateToJavaUtilDate(arrivalDate),
                DateUtil.convertLocalDateToJavaUtilDate(departureDate),
                DateUtil.convertLocalDateToJavaUtilDate(bookingDate),
                DateUtil.convertLocalDateToJavaUtilDate(cancellationDate));

        BusinessInsightsCriteria criteria = new BusinessInsightsCriteria();
        criteria.setCategory(BusinessInsightsCategory.OCCUPANCY_DATE);
        criteria.addValue(BusinessInsightsValue.ROOMS_SOLD);
        criteria.addValue(BusinessInsightsValue.CANCELLATIONS);
        criteria.addValue(BusinessInsightsValue.ROOM_REVENUE);
        criteria.addValue(BusinessInsightsValue.AVG_DAILY_RATE);
        criteria.addValue(BusinessInsightsValue.REVENUE_PER_AVAILABLE_ROOM);
        criteria.addValue(BusinessInsightsValue.NET_REVENUE);
        criteria.addValue(BusinessInsightsValue.NET_AVG_DAILY_RATE);
        criteria.addValue(BusinessInsightsValue.NET_REVENUE_PER_AVAILABLE_ROOM);
        criteria.setStartDate(date);
        criteria.setEndDate(date);
        criteria.setAsOfDate(asOfDate);

        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.POST_DEPARTURE_REVENUE_ADJUSTMENT_ENABLED)).thenReturn(FALSE);
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.ADD_CANCELLATIONS_TO_OCCUPANCY_ON_BOOKS_STLY_FOR_BUSINESS_INSIGHT)).thenReturn(TRUE);
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.BUSINESS_INSIGHTS_REPORT_CANCELLATIONS_NO_SHOW_SEPARATION)).thenReturn(TRUE);
        BusinessInsightsData data = service.getBusinessInsightsData(criteria);
        assertEquals(8, data.getValueTypes().size());

        List<Number> occupancyOnBooksLastYear = data.getValueList(BusinessInsightsValue.ROOMS_SOLD);
        assertSumEquals(1, occupancyOnBooksLastYear);

        List<Number> cancellationsLastYear = data.getValueList(BusinessInsightsValue.CANCELLATIONS);
        assertSumEquals(1, cancellationsLastYear);

        List<Number> roomRevenueLastYear = data.getValueList(BusinessInsightsValue.ROOM_REVENUE);
        assertSumEquals(50, roomRevenueLastYear);

        List<Number> adrLastYear = data.getValueList(BusinessInsightsValue.AVG_DAILY_RATE);
        assertSumEquals(50, adrLastYear);

        List<Number> revParLastYear = data.getValueList(BusinessInsightsValue.REVENUE_PER_AVAILABLE_ROOM);
        assertSumEquals(10, revParLastYear);

        List<Number> netRoomRevenueLastYear = data.getValueList(BusinessInsightsValue.NET_REVENUE);
        assertSumEquals(50, netRoomRevenueLastYear);

        List<Number> netADRLastYear = data.getValueList(BusinessInsightsValue.NET_AVG_DAILY_RATE);
        assertSumEquals(50, netADRLastYear);

        List<Number> netRevParLastYear = data.getValueList(BusinessInsightsValue.NET_REVENUE_PER_AVAILABLE_ROOM);
        assertSumEquals(10, netRevParLastYear);
    }

    @Test
    void testGetCurrentYearDataConsideringAsOfDateWithAddCancellationToggleOnForPastFutureDateRange() {
        tenantCrudService().executeUpdateByNativeQuery("truncate table Reservation_Night ");
        tenantCrudService().executeUpdateByNativeQuery("truncate table Accom_Activity ");

        LocalDate startDate = LocalDate.parse("2021-11-11");
        LocalDate endDate = LocalDate.parse("2021-11-14");
        LocalDate asOfDate = LocalDate.parse("2021-11-12");

        tenantCrudService().executeUpdateByNativeQuery(" insert into Accom_Activity(Property_ID,Occupancy_DT,SnapShot_DTTM,Accom_Type_ID,Accom_Capacity,Rooms_Sold,Rooms_Not_Avail_Maint,Rooms_Not_Avail_Other,Arrivals,Departures,Cancellations,No_Shows,Room_Revenue,Food_Revenue,Total_Revenue,File_Metadata_ID)" +
                " values(5,'2021-11-11','2021-11-15 00:00:00',4,7,5,1,1,5,6,0,0,250.00000,150.00000,400.00000,1);");

        tenantCrudService().executeUpdateByNativeQuery(" insert into Accom_Activity(Property_ID,Occupancy_DT,SnapShot_DTTM,Accom_Type_ID,Accom_Capacity,Rooms_Sold,Rooms_Not_Avail_Maint,Rooms_Not_Avail_Other,Arrivals,Departures,Cancellations,No_Shows,Room_Revenue,Food_Revenue,Total_Revenue,File_Metadata_ID)" +
                " values(5,'2021-11-12','2021-11-15 00:00:00',4,7,5,1,1,5,6,0,0,250.00000,150.00000,400.00000,1);");

        tenantCrudService().executeUpdateByNativeQuery(" insert into Accom_Activity(Property_ID,Occupancy_DT,SnapShot_DTTM,Accom_Type_ID,Accom_Capacity,Rooms_Sold,Rooms_Not_Avail_Maint,Rooms_Not_Avail_Other,Arrivals,Departures,Cancellations,No_Shows,Room_Revenue,Food_Revenue,Total_Revenue,File_Metadata_ID)" +
                " values(5,'2021-11-13','2021-11-15 00:00:00',4,7,5,1,1,5,6,0,0,250.00000,150.00000,400.00000,1);");

        tenantCrudService().executeUpdateByNativeQuery(" insert into Accom_Activity(Property_ID,Occupancy_DT,SnapShot_DTTM,Accom_Type_ID,Accom_Capacity,Rooms_Sold,Rooms_Not_Avail_Maint,Rooms_Not_Avail_Other,Arrivals,Departures,Cancellations,No_Shows,Room_Revenue,Food_Revenue,Total_Revenue,File_Metadata_ID)" +
                " values(5,'2021-11-14','2021-11-15 00:00:00',4,7,5,1,1,5,6,0,0,250.00000,150.00000,400.00000,1);");

        tenantCrudService().executeUpdateByNativeQuery("insert into Reservation_Night([File_Metadata_ID],[Property_ID],[Reservation_Identifier],[Individual_Status],[Arrival_DT],[Departure_DT], \n" +
                "[Booking_DT],[Cancellation_DT],[Booked_Accom_Type_Code],[Accom_Type_ID],[Mkt_Seg_ID],[Room_Number],[Booking_type],[Room_Revenue],[Food_Revenue],[Total_Revenue],[Rate_Code], \n" +
                "[Rate_Value],[Number_Children],[Number_Adults],[Occupancy_DT])  \n" +
                "values(1, 000005, '***********','CO','2021-11-11','2021-11-15','2021-11-05',null,'SXBL', 4, 7, 1108, 'IN', 50.00, 0.00, 0.00,'SHHQO1',50.00,0,2, '2021-11-11');");

        tenantCrudService().executeUpdateByNativeQuery("insert into Reservation_Night([File_Metadata_ID],[Property_ID],[Reservation_Identifier],[Individual_Status],[Arrival_DT],[Departure_DT], \n" +
                "[Booking_DT],[Cancellation_DT],[Booked_Accom_Type_Code],[Accom_Type_ID],[Mkt_Seg_ID],[Room_Number],[Booking_type],[Room_Revenue],[Food_Revenue],[Total_Revenue],[Rate_Code], \n" +
                "[Rate_Value],[Number_Children],[Number_Adults],[Occupancy_DT])  \n" +
                "values(1, 000005, '12345678902','CO','2021-11-12','2021-11-15','2021-11-05',null,'SXBL', 4, 7, 1108, 'IN', 50.00, 0.00, 0.00,'SHHQO1',50.00,0,2, '2021-11-12');");

        tenantCrudService().executeUpdateByNativeQuery("insert into Reservation_Night([File_Metadata_ID],[Property_ID],[Reservation_Identifier],[Individual_Status],[Arrival_DT],[Departure_DT], \n" +
                "[Booking_DT],[Cancellation_DT],[Booked_Accom_Type_Code],[Accom_Type_ID],[Mkt_Seg_ID],[Room_Number],[Booking_type],[Room_Revenue],[Food_Revenue],[Total_Revenue],[Rate_Code], \n" +
                "[Rate_Value],[Number_Children],[Number_Adults],[Occupancy_DT])  \n" +
                "values(1, 000005, '***********','CO','2021-11-13','2021-11-15','2021-11-05',null,'SXBL', 4, 7, 1108, 'IN', 50.00, 0.00, 0.00,'SHHQO1',50.00,0,2, '2021-11-13');");

        tenantCrudService().executeUpdateByNativeQuery("insert into Reservation_Night([File_Metadata_ID],[Property_ID],[Reservation_Identifier],[Individual_Status],[Arrival_DT],[Departure_DT], \n" +
                "[Booking_DT],[Cancellation_DT],[Booked_Accom_Type_Code],[Accom_Type_ID],[Mkt_Seg_ID],[Room_Number],[Booking_type],[Room_Revenue],[Food_Revenue],[Total_Revenue],[Rate_Code], \n" +
                "[Rate_Value],[Number_Children],[Number_Adults],[Occupancy_DT])  \n" +
                "values(1, 000005, '12345678904','CO','2021-11-14','2021-11-15','2021-11-05',null,'SXBL', 4, 7, 1108, 'IN', 50.00, 0.00, 0.00,'SHHQO1',50.00,0,2, '2021-11-14');");

        tenantCrudService().executeUpdateByNativeQuery("insert into Reservation_Night([File_Metadata_ID],[Property_ID],[Reservation_Identifier],[Individual_Status],[Arrival_DT],[Departure_DT], \n" +
                "[Booking_DT],[Cancellation_DT],[Booked_Accom_Type_Code],[Accom_Type_ID],[Mkt_Seg_ID],[Room_Number],[Booking_type],[Room_Revenue],[Food_Revenue],[Total_Revenue],[Rate_Code], \n" +
                "[Rate_Value],[Number_Children],[Number_Adults],[Occupancy_DT])  \n" +
                "values(1, 000005, '***********','XX','2021-11-14','2021-11-15','2021-11-05','2021-11-13','SXBL', 4, 7, 1108, 'IN', 00.00, 0.00, 0.00,'SHHQO1',50.00,0,2, '2021-11-14');");

        BusinessInsightsCriteria criteria = new BusinessInsightsCriteria();
        criteria.setCategory(BusinessInsightsCategory.OCCUPANCY_DATE);
        criteria.addValue(BusinessInsightsValue.ROOMS_SOLD);
        criteria.addValue(BusinessInsightsValue.CANCELLATIONS);
        criteria.addValue(BusinessInsightsValue.ROOM_REVENUE);
        criteria.addValue(BusinessInsightsValue.AVG_DAILY_RATE);
        criteria.addValue(BusinessInsightsValue.REVENUE_PER_AVAILABLE_ROOM);
        criteria.addValue(BusinessInsightsValue.NET_REVENUE);
        criteria.addValue(BusinessInsightsValue.NET_AVG_DAILY_RATE);
        criteria.addValue(BusinessInsightsValue.NET_REVENUE_PER_AVAILABLE_ROOM);
        criteria.setStartDate(startDate);
        criteria.setEndDate(endDate);
        criteria.setAsOfDate(asOfDate);

        lenient().when(propertyConfigParamService.isIncludeCancellationNoShowRevenueEnabled()).thenReturn(TRUE);
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.POST_DEPARTURE_REVENUE_ADJUSTMENT_ENABLED)).thenReturn(FALSE);
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.ADD_CANCELLATIONS_TO_OCCUPANCY_ON_BOOKS_STLY_FOR_BUSINESS_INSIGHT)).thenReturn(TRUE);
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.BUSINESS_INSIGHTS_REPORT_CANCELLATIONS_NO_SHOW_SEPARATION)).thenReturn(TRUE);

        BusinessInsightsData data = service.getBusinessInsightsData(criteria);
        assertEquals(8, data.getValueTypes().size());

        List<Number> occupancyOnBooksLastYear = data.getValueList(BusinessInsightsValue.ROOMS_SOLD);
        assertSumEquals(5, occupancyOnBooksLastYear);

        List<Number> cancellationsLastYear = data.getValueList(BusinessInsightsValue.CANCELLATIONS);
        assertSumEquals(0, cancellationsLastYear);

        List<Number> roomRevenueLastYear = data.getValueList(BusinessInsightsValue.ROOM_REVENUE);
        assertSumEquals(250, roomRevenueLastYear);

        List<Number> adrLastYear = data.getValueList(BusinessInsightsValue.AVG_DAILY_RATE);
        assertSumEquals(200, adrLastYear);

        List<Number> revParLastYear = data.getValueList(BusinessInsightsValue.REVENUE_PER_AVAILABLE_ROOM);
        assertSumEquals(50, revParLastYear);

        List<Number> netRoomRevenueLastYear = data.getValueList(BusinessInsightsValue.NET_REVENUE);
        assertSumEquals(250, netRoomRevenueLastYear);

        List<Number> netADRLastYear = data.getValueList(BusinessInsightsValue.NET_AVG_DAILY_RATE);
        assertSumEquals(200, netADRLastYear);

        List<Number> netRevParLastYear = data.getValueList(BusinessInsightsValue.NET_REVENUE_PER_AVAILABLE_ROOM);
        assertSumEquals(50, netRevParLastYear);
    }


    @Test
    public void testGetCurrentYearDataWithMissingDatesConsideringAsOfDateWithAddCancellationToggleOn() {
        tenantCrudService().executeUpdateByNativeQuery("truncate table Reservation_Night ");
        tenantCrudService().executeUpdateByNativeQuery("truncate table Accom_Activity ");

        LocalDate startDate = LocalDate.parse("2021-12-13");
        LocalDate endDate = LocalDate.parse("2021-12-17");
        LocalDate asOfDate = LocalDate.parse("2021-11-12");

        tenantCrudService().executeUpdateByNativeQuery(" insert into Accom_Activity(Property_ID,Occupancy_DT,SnapShot_DTTM,Accom_Type_ID,Accom_Capacity,Rooms_Sold,Rooms_Not_Avail_Maint,Rooms_Not_Avail_Other,Arrivals,Departures,Cancellations,No_Shows,Room_Revenue,Food_Revenue,Total_Revenue,File_Metadata_ID)" +
                " values(5,'2021-12-13','2021-12-15 00:00:00',4,7,5,1,1,5,6,0,0,250.00000,150.00000,400.00000,1);");

        tenantCrudService().executeUpdateByNativeQuery(" insert into Accom_Activity(Property_ID,Occupancy_DT,SnapShot_DTTM,Accom_Type_ID,Accom_Capacity,Rooms_Sold,Rooms_Not_Avail_Maint,Rooms_Not_Avail_Other,Arrivals,Departures,Cancellations,No_Shows,Room_Revenue,Food_Revenue,Total_Revenue,File_Metadata_ID)" +
                " values(5,'2021-12-14','2021-12-15 00:00:00',4,7,5,1,1,5,6,0,0,250.00000,150.00000,400.00000,1);");

        tenantCrudService().executeUpdateByNativeQuery(" insert into Accom_Activity(Property_ID,Occupancy_DT,SnapShot_DTTM,Accom_Type_ID,Accom_Capacity,Rooms_Sold,Rooms_Not_Avail_Maint,Rooms_Not_Avail_Other,Arrivals,Departures,Cancellations,No_Shows,Room_Revenue,Food_Revenue,Total_Revenue,File_Metadata_ID)" +
                " values(5,'2021-12-15','2021-12-15 00:00:00',4,7,5,1,1,5,6,0,0,250.00000,150.00000,400.00000,1);");

        tenantCrudService().executeUpdateByNativeQuery(" insert into Accom_Activity(Property_ID,Occupancy_DT,SnapShot_DTTM,Accom_Type_ID,Accom_Capacity,Rooms_Sold,Rooms_Not_Avail_Maint,Rooms_Not_Avail_Other,Arrivals,Departures,Cancellations,No_Shows,Room_Revenue,Food_Revenue,Total_Revenue,File_Metadata_ID)" +
                " values(5,'2021-12-16','2021-12-16 00:00:00',4,7,5,1,1,0,0,0,0,0.00000,0.00000,0.00000,1);");

        tenantCrudService().executeUpdateByNativeQuery(" insert into Accom_Activity(Property_ID,Occupancy_DT,SnapShot_DTTM,Accom_Type_ID,Accom_Capacity,Rooms_Sold,Rooms_Not_Avail_Maint,Rooms_Not_Avail_Other,Arrivals,Departures,Cancellations,No_Shows,Room_Revenue,Food_Revenue,Total_Revenue,File_Metadata_ID)" +
                " values(5,'2021-12-17','2021-12-17 00:00:00',4,7,5,1,1,0,0,0,0,0.00000,0.00000,0.00000,1);");

        tenantCrudService().executeUpdateByNativeQuery("insert into Reservation_Night([File_Metadata_ID],[Property_ID],[Reservation_Identifier],[Individual_Status],[Arrival_DT],[Departure_DT], \n" +
                "[Booking_DT],[Cancellation_DT],[Booked_Accom_Type_Code],[Accom_Type_ID],[Mkt_Seg_ID],[Room_Number],[Booking_type],[Room_Revenue],[Food_Revenue],[Total_Revenue],[Rate_Code], \n" +
                "[Rate_Value],[Number_Children],[Number_Adults],[Occupancy_DT])  \n" +
                "values(1, 000005, '***********','CO','2021-12-13','2021-12-15','2021-11-05',null,'SXBL', 4, 7, 1108, 'IN', 50.00, 0.00, 0.00,'SHHQO1',50.00,0,2, '2021-12-13');");

        tenantCrudService().executeUpdateByNativeQuery("insert into Reservation_Night([File_Metadata_ID],[Property_ID],[Reservation_Identifier],[Individual_Status],[Arrival_DT],[Departure_DT], \n" +
                "[Booking_DT],[Cancellation_DT],[Booked_Accom_Type_Code],[Accom_Type_ID],[Mkt_Seg_ID],[Room_Number],[Booking_type],[Room_Revenue],[Food_Revenue],[Total_Revenue],[Rate_Code], \n" +
                "[Rate_Value],[Number_Children],[Number_Adults],[Occupancy_DT])  \n" +
                "values(1, 000005, '12345678904','CO','2021-12-14','2021-12-15','2021-11-05',null,'SXBL', 4, 7, 1108, 'IN', 50.00, 0.00, 0.00,'SHHQO1',50.00,0,2, '2021-12-14');");

        tenantCrudService().executeUpdateByNativeQuery("insert into Reservation_Night([File_Metadata_ID],[Property_ID],[Reservation_Identifier],[Individual_Status],[Arrival_DT],[Departure_DT], \n" +
                "[Booking_DT],[Cancellation_DT],[Booked_Accom_Type_Code],[Accom_Type_ID],[Mkt_Seg_ID],[Room_Number],[Booking_type],[Room_Revenue],[Food_Revenue],[Total_Revenue],[Rate_Code], \n" +
                "[Rate_Value],[Number_Children],[Number_Adults],[Occupancy_DT])  \n" +
                "values(1, 000005, '***********','XX','2021-12-14','2021-12-15','2021-11-05','2021-11-13','SXBL', 4, 7, 1108, 'IN', 00.00, 0.00, 0.00,'SHHQO1',50.00,0,2, '2021-12-14');");


        BusinessInsightsCriteria criteria = new BusinessInsightsCriteria();
        criteria.setCategory(BusinessInsightsCategory.OCCUPANCY_DATE);
        criteria.addValue(BusinessInsightsValue.ROOMS_SOLD);
        criteria.addValue(BusinessInsightsValue.CANCELLATIONS);
        criteria.addValue(BusinessInsightsValue.ROOM_REVENUE);
        criteria.addValue(BusinessInsightsValue.AVG_DAILY_RATE);
        criteria.addValue(BusinessInsightsValue.REVENUE_PER_AVAILABLE_ROOM);
        criteria.addValue(BusinessInsightsValue.NET_REVENUE);
        criteria.addValue(BusinessInsightsValue.NET_AVG_DAILY_RATE);
        criteria.addValue(BusinessInsightsValue.NET_REVENUE_PER_AVAILABLE_ROOM);
        criteria.setStartDate(startDate);
        criteria.setEndDate(endDate);
        criteria.setAsOfDate(asOfDate);

        int expectedDays = (int) (DAYS.between(startDate, endDate) + 1);
        lenient().when(propertyConfigParamService.isIncludeCancellationNoShowRevenueEnabled()).thenReturn(TRUE);
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.POST_DEPARTURE_REVENUE_ADJUSTMENT_ENABLED)).thenReturn(FALSE);
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.ADD_CANCELLATIONS_TO_OCCUPANCY_ON_BOOKS_STLY_FOR_BUSINESS_INSIGHT)).thenReturn(TRUE);
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.BUSINESS_INSIGHTS_REPORT_CANCELLATIONS_NO_SHOW_SEPARATION)).thenReturn(TRUE);

        BusinessInsightsData data = service.getBusinessInsightsData(criteria);
        assertEquals(8, data.getValueTypes().size());

        List<Number> occupancyOnBooksLastYear = data.getValueList(BusinessInsightsValue.ROOMS_SOLD);
        assertEquals(expectedDays, occupancyOnBooksLastYear.size());
        assertSumEquals(3, occupancyOnBooksLastYear);

        List<Number> cancellationsLastYear = data.getValueList(BusinessInsightsValue.CANCELLATIONS);
        assertEquals(expectedDays, cancellationsLastYear.size());
        assertSumEquals(0, cancellationsLastYear);

        List<Number> roomRevenueLastYear = data.getValueList(BusinessInsightsValue.ROOM_REVENUE);
        assertEquals(expectedDays, roomRevenueLastYear.size());
        assertSumEquals(150, roomRevenueLastYear);

        List<Number> adrLastYear = data.getValueList(BusinessInsightsValue.AVG_DAILY_RATE);
        assertEquals(expectedDays, adrLastYear.size());
        assertSumEquals(100, adrLastYear);

        List<Number> revParLastYear = data.getValueList(BusinessInsightsValue.REVENUE_PER_AVAILABLE_ROOM);
        assertEquals(expectedDays, revParLastYear.size());
        assertSumEquals(30, revParLastYear);

        List<Number> netRoomRevenueLastYear = data.getValueList(BusinessInsightsValue.NET_REVENUE);
        assertEquals(expectedDays, netRoomRevenueLastYear.size());
        assertSumEquals(150, netRoomRevenueLastYear);

        List<Number> netADRLastYear = data.getValueList(BusinessInsightsValue.NET_AVG_DAILY_RATE);
        assertEquals(expectedDays, netADRLastYear.size());
        assertSumEquals(100, netADRLastYear);

        List<Number> netRevParLastYear = data.getValueList(BusinessInsightsValue.NET_REVENUE_PER_AVAILABLE_ROOM);
        assertEquals(expectedDays, netRevParLastYear.size());
        assertSumEquals(30, netRevParLastYear);
    }

    @Test
    void testGetCurrentYearDataConsideringAsOfDateWithAddCancellationToggleOnForPastFutureDateRangeAndAsOfDateBetweenForChannel() {
        tenantCrudService().executeUpdateByNativeQuery("truncate table Reservation_Night ");
        tenantCrudService().executeUpdateByNativeQuery("truncate table Accom_Activity ");

        LocalDate startDate = LocalDate.parse("2021-11-11");
        LocalDate endDate = LocalDate.parse("2021-11-14");
        LocalDate asOfDate = LocalDate.parse("2021-11-12");

        tenantCrudService().executeUpdateByNativeQuery(" insert into Accom_Activity(Property_ID,Occupancy_DT,SnapShot_DTTM,Accom_Type_ID,Accom_Capacity,Rooms_Sold,Rooms_Not_Avail_Maint,Rooms_Not_Avail_Other,Arrivals,Departures,Cancellations,No_Shows,Room_Revenue,Food_Revenue,Total_Revenue,File_Metadata_ID)" +
                " values(5,'2021-11-11','2021-11-15 00:00:00',4,7,5,1,1,5,6,0,0,250.00000,150.00000,400.00000,1);");

        tenantCrudService().executeUpdateByNativeQuery(" insert into Accom_Activity(Property_ID,Occupancy_DT,SnapShot_DTTM,Accom_Type_ID,Accom_Capacity,Rooms_Sold,Rooms_Not_Avail_Maint,Rooms_Not_Avail_Other,Arrivals,Departures,Cancellations,No_Shows,Room_Revenue,Food_Revenue,Total_Revenue,File_Metadata_ID)" +
                " values(5,'2021-11-12','2021-11-15 00:00:00',4,7,5,1,1,5,6,0,0,250.00000,150.00000,400.00000,1);");

        tenantCrudService().executeUpdateByNativeQuery(" insert into Accom_Activity(Property_ID,Occupancy_DT,SnapShot_DTTM,Accom_Type_ID,Accom_Capacity,Rooms_Sold,Rooms_Not_Avail_Maint,Rooms_Not_Avail_Other,Arrivals,Departures,Cancellations,No_Shows,Room_Revenue,Food_Revenue,Total_Revenue,File_Metadata_ID)" +
                " values(5,'2021-11-13','2021-11-15 00:00:00',4,7,5,1,1,5,6,0,0,250.00000,150.00000,400.00000,1);");

        tenantCrudService().executeUpdateByNativeQuery(" insert into Accom_Activity(Property_ID,Occupancy_DT,SnapShot_DTTM,Accom_Type_ID,Accom_Capacity,Rooms_Sold,Rooms_Not_Avail_Maint,Rooms_Not_Avail_Other,Arrivals,Departures,Cancellations,No_Shows,Room_Revenue,Food_Revenue,Total_Revenue,File_Metadata_ID)" +
                " values(5,'2021-11-14','2021-11-15 00:00:00',4,7,5,1,1,5,6,0,0,250.00000,150.00000,400.00000,1);");

        tenantCrudService().executeUpdateByNativeQuery("insert into Reservation_Night([File_Metadata_ID],[Property_ID],[Reservation_Identifier],[Individual_Status],[Arrival_DT],[Departure_DT], \n" +
                "[Booking_DT],[Cancellation_DT],[Booked_Accom_Type_Code],[Accom_Type_ID],[Mkt_Seg_ID],[Room_Number],[Booking_type],[Room_Revenue],[Food_Revenue],[Total_Revenue],[Rate_Code], \n" +
                "[Rate_Value],[Number_Children],[Number_Adults],[Channel],[Occupancy_DT])  \n" +
                "values(1, 000005, '***********','CO','2021-11-11','2021-11-15','2021-11-05',null,'SXBL', 4, 7, 1108, 'IN', 50.00, 0.00, 0.00,'SHHQO1',50.00,0,2,'WEB', '2021-11-11');");

        tenantCrudService().executeUpdateByNativeQuery("insert into Reservation_Night([File_Metadata_ID],[Property_ID],[Reservation_Identifier],[Individual_Status],[Arrival_DT],[Departure_DT], \n" +
                "[Booking_DT],[Cancellation_DT],[Booked_Accom_Type_Code],[Accom_Type_ID],[Mkt_Seg_ID],[Room_Number],[Booking_type],[Room_Revenue],[Food_Revenue],[Total_Revenue],[Rate_Code], \n" +
                "[Rate_Value],[Number_Children],[Number_Adults],[Channel],[Occupancy_DT])  \n" +
                "values(1, 000005, '12345678902','CO','2021-11-12','2021-11-15','2021-11-05',null,'SXBL', 4, 7, 1108, 'IN', 50.00, 0.00, 0.00,'SHHQO1',50.00,0,2,'WEB', '2021-11-12');");

        tenantCrudService().executeUpdateByNativeQuery("insert into Reservation_Night([File_Metadata_ID],[Property_ID],[Reservation_Identifier],[Individual_Status],[Arrival_DT],[Departure_DT], \n" +
                "[Booking_DT],[Cancellation_DT],[Booked_Accom_Type_Code],[Accom_Type_ID],[Mkt_Seg_ID],[Room_Number],[Booking_type],[Room_Revenue],[Food_Revenue],[Total_Revenue],[Rate_Code], \n" +
                "[Rate_Value],[Number_Children],[Number_Adults],[Channel],[Occupancy_DT])  \n" +
                "values(1, 000005, '***********','CO','2021-11-13','2021-11-15','2021-11-05',null,'SXBL', 4, 7, 1108, 'IN', 50.00, 0.00, 0.00,'SHHQO1',50.00,0,2, 'WEB','2021-11-13');");

        tenantCrudService().executeUpdateByNativeQuery("insert into Reservation_Night([File_Metadata_ID],[Property_ID],[Reservation_Identifier],[Individual_Status],[Arrival_DT],[Departure_DT], \n" +
                "[Booking_DT],[Cancellation_DT],[Booked_Accom_Type_Code],[Accom_Type_ID],[Mkt_Seg_ID],[Room_Number],[Booking_type],[Room_Revenue],[Food_Revenue],[Total_Revenue],[Rate_Code], \n" +
                "[Rate_Value],[Number_Children],[Number_Adults],[Channel],[Occupancy_DT])  \n" +
                "values(1, 000005, '12345678904','CO','2021-11-14','2021-11-15','2021-11-05',null,'SXBL', 4, 7, 1108, 'IN', 50.00, 0.00, 0.00,'SHHQO1',50.00,0,2,'WEB', '2021-11-14');");

        tenantCrudService().executeUpdateByNativeQuery("insert into Reservation_Night([File_Metadata_ID],[Property_ID],[Reservation_Identifier],[Individual_Status],[Arrival_DT],[Departure_DT], \n" +
                "[Booking_DT],[Cancellation_DT],[Booked_Accom_Type_Code],[Accom_Type_ID],[Mkt_Seg_ID],[Room_Number],[Booking_type],[Room_Revenue],[Food_Revenue],[Total_Revenue],[Rate_Code], \n" +
                "[Rate_Value],[Number_Children],[Number_Adults],[Channel],[Occupancy_DT])  \n" +
                "values(1, 000005, '***********','XX','2021-11-14','2021-11-15','2021-11-05','2021-11-13','SXBL', 4, 7, 1108, 'IN', 00.00, 0.00, 0.00,'SHHQO1',50.00,0,2, 'WEB','2021-11-14');");

        BusinessInsightsCriteria criteria = new BusinessInsightsCriteria();
        criteria.setCategory(BusinessInsightsCategory.CHANNEL);
        criteria.addValue(BusinessInsightsValue.ROOMS_SOLD);
        criteria.addValue(BusinessInsightsValue.CANCELLATIONS);
        criteria.addValue(BusinessInsightsValue.ROOM_REVENUE);
        criteria.addValue(BusinessInsightsValue.AVG_DAILY_RATE);
        criteria.addValue(BusinessInsightsValue.REVENUE_PER_AVAILABLE_ROOM);
        criteria.addValue(BusinessInsightsValue.NET_REVENUE);
        criteria.addValue(BusinessInsightsValue.NET_AVG_DAILY_RATE);
        criteria.addValue(BusinessInsightsValue.NET_REVENUE_PER_AVAILABLE_ROOM);
        criteria.setStartDate(startDate);
        criteria.setEndDate(endDate);
        criteria.setAsOfDate(asOfDate);

        lenient().when(propertyConfigParamService.isIncludeCancellationNoShowRevenueEnabled()).thenReturn(TRUE);
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.POST_DEPARTURE_REVENUE_ADJUSTMENT_ENABLED)).thenReturn(FALSE);
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.ADD_CANCELLATIONS_TO_OCCUPANCY_ON_BOOKS_STLY_FOR_BUSINESS_INSIGHT)).thenReturn(TRUE);
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.BUSINESS_INSIGHTS_REPORT_CANCELLATIONS_NO_SHOW_SEPARATION)).thenReturn(TRUE);

        BusinessInsightsData data = service.getBusinessInsightsData(criteria);
        assertEquals(8, data.getValueTypes().size());

        List<Number> occupancyOnBooksLastYear = data.getValueList(BusinessInsightsValue.ROOMS_SOLD);
        assertSumEquals(5, occupancyOnBooksLastYear);

        List<Number> cancellationsLastYear = data.getValueList(BusinessInsightsValue.CANCELLATIONS);
        assertSumEquals(0, cancellationsLastYear);

        List<Number> roomRevenueLastYear = data.getValueList(BusinessInsightsValue.ROOM_REVENUE);
        assertSumEquals(250, roomRevenueLastYear);

        List<Number> adrLastYear = data.getValueList(BusinessInsightsValue.AVG_DAILY_RATE);
        assertSumEquals(50, adrLastYear);

        List<Number> revParLastYear = data.getValueList(BusinessInsightsValue.REVENUE_PER_AVAILABLE_ROOM);
        assertSumEquals(0, revParLastYear);

        List<Number> netRoomRevenueLastYear = data.getValueList(BusinessInsightsValue.NET_REVENUE);
        assertSumEquals(250, netRoomRevenueLastYear);

        List<Number> netADRLastYear = data.getValueList(BusinessInsightsValue.NET_AVG_DAILY_RATE);
        assertSumEquals(50, netADRLastYear);

        List<Number> netRevParLastYear = data.getValueList(BusinessInsightsValue.NET_REVENUE_PER_AVAILABLE_ROOM);
        assertSumEquals(0, netRevParLastYear);
    }

    private void assertNoShowAndCancellationSum(BusinessInsightsData data, int expectedDays) {
        assertEquals(4, data.getValueTypes().size());

        List<Number> cancellations = data.getValueList(BusinessInsightsValue.CANCELLATIONS);
        assertEquals(expectedDays, cancellations.size());
        assertSumEquals(1, cancellations);

        List<Number> noShow = data.getValueList(BusinessInsightsValue.NO_SHOW);
        assertEquals(expectedDays, noShow.size());
        assertSumEquals(1, noShow);

        List<Number> noShowLastYear = data.getValueList(BusinessInsightsValue.NO_SHOW_LAST_YEAR);
        assertEquals(expectedDays, noShowLastYear.size());
        assertSumEquals(1, noShowLastYear);

        List<Number> cancellationsLastYear = data.getValueList(BusinessInsightsValue.CANCELLATIONS_LAST_YEAR);
        assertEquals(expectedDays, cancellationsLastYear.size());
        assertSumEquals(1, cancellationsLastYear);
    }

    private BusinessInsightsCriteria getBusinessInsightCriteria(LocalDate startDate, LocalDate endDate) {
        BusinessInsightsCriteria criteria = new BusinessInsightsCriteria();
        criteria.setCategory(BusinessInsightsCategory.OCCUPANCY_DATE);
        criteria.addValue(BusinessInsightsValue.NO_SHOW);
        criteria.addValue(BusinessInsightsValue.NO_SHOW_LAST_YEAR);
        criteria.addValue(BusinessInsightsValue.CANCELLATIONS);
        criteria.addValue(BusinessInsightsValue.CANCELLATIONS_LAST_YEAR);
        criteria.setStartDate(startDate);
        criteria.setEndDate(endDate);
        criteria.setAsOfDate(endDate);
        return criteria;
    }


    private void insertDayUseTransactionsIntoReservationNight(Date arrivalDt, Date departureDt, Date bookingDt, Date cancellationDt) {
        tenantCrudService().executeUpdateByNativeQuery("insert into Reservation_Night([File_Metadata_ID],[Property_ID],[Reservation_Identifier],[Individual_Status],[Arrival_DT],[Departure_DT], \n" +
                        "[Booking_DT],[Cancellation_DT],[Booked_Accom_Type_Code],[Accom_Type_ID],[Mkt_Seg_ID],[Room_Number],[Booking_type],[Room_Revenue],[Food_Revenue],[Total_Revenue],[Rate_Code], \n" +
                        "[Rate_Value],[Number_Children],[Number_Adults],[Channel],[Occupancy_DT])  \n" +
                        "values(1, 000005, '***********','CO',:arrivalDT,:departureDate,:bookingDate,null,'SXBL', 4, 7, 1108, 'IN', 50.00, 0.00, 0.00,'SHHQO1',50.00,0,2, 'WEB',:occupancyDate);",
                QueryParameter.with("arrivalDT", arrivalDt).and("departureDate", departureDt).and("bookingDate", bookingDt).and("occupancyDate", arrivalDt).parameters());

        tenantCrudService().executeUpdateByNativeQuery("insert into Reservation_Night([File_Metadata_ID],[Property_ID],[Reservation_Identifier],[Individual_Status],[Arrival_DT],[Departure_DT], \n" +
                        "[Booking_DT],[Cancellation_DT],[Booked_Accom_Type_Code],[Accom_Type_ID],[Mkt_Seg_ID],[Room_Number],[Booking_type],[Room_Revenue],[Food_Revenue],[Total_Revenue],[Rate_Code], \n" +
                        "[Rate_Value],[Number_Children],[Number_Adults],[Channel], [Occupancy_DT])  \n" +
                        "values(1, 000005, '***********','XX',:arrivalDT,:departureDate,:bookingDate,:cancellationDate,'SXBL', 4, 7, 1108, 'IN', 0.00, 0.00, 0.00,'SHHQO1',50.00,0,2, 'WEB',:occupancyDate);",
                QueryParameter.with("arrivalDT", arrivalDt).and("departureDate", departureDt).and("bookingDate", bookingDt).and("cancellationDate", cancellationDt).and("occupancyDate", arrivalDt).parameters());
    }

    private void assertInDateRange(LocalDate start, LocalDate end, BusinessInsightsValue value, List<Tuple> tuples) {
        for (Tuple tuple : tuples) {
            Timestamp ts = (Timestamp) tuple.get(value.getTableAlias());
            LocalDate date = Instant.ofEpochMilli(ts.getTime())
                    .atZone(ZoneId.systemDefault()).toLocalDate();
            assertFalse(date.isBefore(start));
            assertFalse(date.isAfter(end));
        }
    }

    private void assertSumEquals(int expected, List<Number> arrivals) {
        int sum = arrivals.stream()
                .filter(Objects::nonNull)
                .mapToInt(Number::intValue)
                .sum();
        assertEquals(expected, sum);
    }
}
