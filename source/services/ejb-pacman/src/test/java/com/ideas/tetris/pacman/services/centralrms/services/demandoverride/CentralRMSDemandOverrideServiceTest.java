package com.ideas.tetris.pacman.services.centralrms.services.demandoverride;

import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomClass;
import com.ideas.tetris.pacman.services.accommodation.service.AccommodationService;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.OccupancyDemandForecast;
import com.ideas.tetris.pacman.services.centralrms.models.common.Action;
import com.ideas.tetris.pacman.services.centralrms.models.common.SpecialEventSettings;
import com.ideas.tetris.pacman.services.centralrms.models.common.Unit;
import com.ideas.tetris.pacman.services.centralrms.models.demandoverride.AdvancedDemandOverrideRequest;
import com.ideas.tetris.pacman.services.centralrms.models.demandoverride.AdvancedOccupancyDemandOverrideResults;
import com.ideas.tetris.pacman.services.centralrms.models.demandoverride.ApplicableOccupancyDemandForecastResults;
import com.ideas.tetris.pacman.services.centralrms.models.demandoverride.DemandOverrideRequest;
import com.ideas.tetris.pacman.services.centralrms.models.demandoverride.DemandOverrideSettings;
import com.ideas.tetris.pacman.services.centralrms.models.util.Tuple2;
import com.ideas.tetris.pacman.services.centralrms.models.util.Tuple3;
import com.ideas.tetris.pacman.services.centralrms.services.seasons.strategies.CentralRMSSpecialEventOverlapSplitter;
import com.ideas.tetris.pacman.services.demandoverride.DemandOverrideService;
import com.ideas.tetris.pacman.services.demandoverride.dto.ArrivalDemandOverride;
import com.ideas.tetris.pacman.services.demandoverride.entity.OccupancyDemandOverride;
import com.ideas.tetris.platform.common.time.LocalDateUtils;
import com.ideas.tetris.platform.common.xml.schema.workcontext.v1.WorkContextType;
import org.apache.commons.lang3.Range;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class CentralRMSDemandOverrideServiceTest {

    @Mock
    private DemandOverrideService demandOverrideService;

    @Mock
    private CentralRMSSpecialEventOverlapSplitter splitter;

    @Mock
    private AccommodationService accommodationService;

    @Mock
    private DemandOverrideForecastCalculatorFactory factory;

    @Mock
    private CentralRMSDemandOverrideGFFConflictFilter gffConflictFilter;

    @Mock
    private CentralRMSDemandOverrideArrivalLOSConflictFilter arrivalLOSConflictFilter;

    @InjectMocks
    private CentralRMSDemandOverrideService service;

    @BeforeEach
    void setup() {
        WorkContextType workContextType = new WorkContextType();
        workContextType.setClientCode("CLIENT");
        workContextType.setPropertyCode("PROPERTY");
        workContextType.setPropertyId(123);
        workContextType.setUserId("123");
        PacmanWorkContextHelper.setWorkContext(workContextType);
    }

    @Test
    void createDemandOverrides() {
        OccupancyDemandForecast forecastA = new OccupancyDemandForecast();
        OccupancyDemandForecast forecastB = new OccupancyDemandForecast();
        com.ideas.tetris.pacman.services.demandoverride.dto.OccupancyDemandOverride overrideA = new com.ideas.tetris.pacman.services.demandoverride.dto.OccupancyDemandOverride();
        com.ideas.tetris.pacman.services.demandoverride.dto.OccupancyDemandOverride overrideB = new com.ideas.tetris.pacman.services.demandoverride.dto.OccupancyDemandOverride();

        Function<OccupancyDemandForecast, com.ideas.tetris.pacman.services.demandoverride.dto.OccupancyDemandOverride> calculator = mock(Function.class);

        when(calculator.apply(forecastA)).thenReturn(overrideA);
        when(calculator.apply(forecastB)).thenReturn(overrideB);
        when(factory.build(Action.INCREASE, 2.0, Unit.PERCENTAGE)).thenReturn(calculator);

        List<com.ideas.tetris.pacman.services.demandoverride.dto.OccupancyDemandOverride> result = service.createDemandOverrides(
                List.of(forecastA, forecastB),
                Action.INCREASE,
                2.0,
                Unit.PERCENTAGE
        );

        assertEquals(List.of(overrideA, overrideB), result);
    }

    @Nested
    class GetApplicableOccupancyDemandForecastsSuite {

        @ParameterizedTest(name = "getApplicableOccupancyDemandForecastsEmpty - overlap special events: {0}")
        @ValueSource(booleans = {true, false})
        void getApplicableOccupancyDemandForecastsEmpty(boolean overlapsSpecialEvents) {
            DemandOverrideRequest request = DemandOverrideRequest.builder()
                    .startDate(LocalDate.of(2022, 1, 1))
                    .endDate(LocalDate.of(2022, 2, 1))
                    .daysOfWeek(Set.of(DayOfWeek.MONDAY, DayOfWeek.WEDNESDAY, DayOfWeek.FRIDAY))
                    .specialEventSettings(SpecialEventSettings.builder()
                            .specialEvents(Set.of("WINTER"))
                            .overlapsWithSpecialEvents(overlapsSpecialEvents)
                            .build())
                    .forecastGroupNames(Collections.emptySet())
                    .action(Action.INCREASE)
                    .quantity(2.0)
                    .unit(Unit.PERCENTAGE)
                    .build();

            List<Range<LocalDate>> splits = Collections.emptyList();

            if (overlapsSpecialEvents) {
                when(splitter.splitIncludeOverlapsWithSpecialEvents(request.getStartDate(), request.getEndDate(), request.getSpecialEventSettings().getSpecialEvents())).thenReturn(splits);
            } else {
                when(splitter.splitExcludeOverlapsWithSpecialEvents(request.getStartDate(), request.getEndDate(), request.getSpecialEventSettings().getSpecialEvents())).thenReturn(splits);
            }

            ApplicableOccupancyDemandForecastResults result = service.getApplicableOccupancyDemandForecasts(request);

            assertEquals(Collections.emptyList(), result.getSkippedForecastsBecauseOfArrivalLOSOverrides());
            assertEquals(Collections.emptyList(), result.getSkippedForecastsBecauseOfGffOverrides());
            assertEquals(Collections.emptyList(), result.getRemovableArrivalLOSOverrides());
            assertEquals(Collections.emptyList(), result.getOverridableForecasts());

            verifyNoInteractions(demandOverrideService, gffConflictFilter, arrivalLOSConflictFilter);
        }

        @ParameterizedTest(name = "getApplicableOccupancyDemandForecastsPropertyLevel - overlap special events: {0}")
        @ValueSource(booleans = {true, false})
        void getApplicableOccupancyDemandForecastsPropertyLevel(boolean overlapsSpecialEvents) {
            DemandOverrideRequest request = DemandOverrideRequest.builder()
                    .startDate(LocalDate.of(2022, 1, 1))
                    .endDate(LocalDate.of(2022, 2, 1))
                    .daysOfWeek(Set.of(DayOfWeek.MONDAY, DayOfWeek.WEDNESDAY, DayOfWeek.FRIDAY))
                    .specialEventSettings(SpecialEventSettings.builder()
                            .specialEvents(Set.of("WINTER"))
                            .overlapsWithSpecialEvents(overlapsSpecialEvents)
                            .build())
                    .forecastGroupNames(Collections.emptySet())
                    .action(Action.INCREASE)
                    .quantity(2.0)
                    .unit(Unit.PERCENTAGE)
                    .build();

            List<Range<LocalDate>> splits = List.of(
                    Range.between(LocalDate.of(2022, 1, 1), LocalDate.of(2022, 1, 7), LocalDate::compareTo),
                    Range.between(LocalDate.of(2022, 1, 14), LocalDate.of(2022, 1, 21), LocalDate::compareTo)
            );

            OccupancyDemandForecast forecastA = new OccupancyDemandForecast();
            forecastA.setOccupancyDate(LocalDateUtils.toDate(LocalDate.of(2022, 1, 5)));
            forecastA.setUserRemainingDemand(BigDecimal.TEN);

            OccupancyDemandForecast forecastB = new OccupancyDemandForecast();
            forecastB.setOccupancyDate(LocalDateUtils.toDate(LocalDate.of(2022, 1, 17)));
            forecastB.setUserRemainingDemand(BigDecimal.TEN);

            OccupancyDemandForecast skippedGFFConflict = new OccupancyDemandForecast();
            skippedGFFConflict.setOccupancyDate(LocalDateUtils.toDate(LocalDate.of(2022, 1, 7)));
            skippedGFFConflict.setUserRemainingDemand(BigDecimal.TEN);

            OccupancyDemandForecast skippedLOSConflict = new OccupancyDemandForecast();
            skippedLOSConflict.setOccupancyDate(LocalDateUtils.toDate(LocalDate.of(2022, 1, 19)));
            skippedLOSConflict.setUserRemainingDemand(BigDecimal.TEN);

            ArrivalDemandOverride removableLOSOverride = new ArrivalDemandOverride();

            List<OccupancyDemandForecast> occupancyDemandForecasts = List.of(skippedGFFConflict, skippedLOSConflict, forecastA, forecastB);

            if (overlapsSpecialEvents) {
                when(splitter.splitIncludeOverlapsWithSpecialEvents(request.getStartDate(), request.getEndDate(), request.getSpecialEventSettings().getSpecialEvents())).thenReturn(splits);
            } else {
                when(splitter.splitExcludeOverlapsWithSpecialEvents(request.getStartDate(), request.getEndDate(), request.getSpecialEventSettings().getSpecialEvents())).thenReturn(splits);
            }

            when(demandOverrideService.getOccupancyDemandForecastsBetweenDatesAndByProductId(
                    LocalDate.of(2022, 1, 3),
                    LocalDate.of(2022, 1, 21),
                    1
            )).thenReturn(occupancyDemandForecasts);

            when(gffConflictFilter.filterWithGFFConflicts(occupancyDemandForecasts)).thenReturn(Tuple2.of(List.of(skippedGFFConflict), List.of(skippedLOSConflict, forecastA, forecastB)));
            when(arrivalLOSConflictFilter.filterWithOverrideConflicts(List.of(skippedLOSConflict, forecastA, forecastB))).thenReturn(Tuple3.of(List.of(removableLOSOverride), List.of(skippedLOSConflict), List.of(forecastA, forecastB)));

            ApplicableOccupancyDemandForecastResults result = service.getApplicableOccupancyDemandForecasts(request);

            assertEquals(List.of(removableLOSOverride), result.getRemovableArrivalLOSOverrides());
            assertEquals(List.of(skippedGFFConflict), result.getSkippedForecastsBecauseOfGffOverrides());
            assertEquals(List.of(skippedLOSConflict), result.getSkippedForecastsBecauseOfArrivalLOSOverrides());
            assertEquals(List.of(forecastA, forecastB), result.getOverridableForecasts());

            if (overlapsSpecialEvents) {
                verify(splitter, never()).splitExcludeOverlapsWithSpecialEvents(any(), any(), any());
            } else {
                verify(splitter, never()).splitIncludeOverlapsWithSpecialEvents(any(), any(), any());
            }
            verify(gffConflictFilter).filterWithGFFConflicts(occupancyDemandForecasts);
            verify(arrivalLOSConflictFilter).filterWithOverrideConflicts(List.of(skippedLOSConflict, forecastA, forecastB));
        }

        @ParameterizedTest(name = "getApplicableOccupancyDemandForecastsForecastGroupLevelAllRoomClasses - overlap special events: {0}")
        @ValueSource(booleans = {true, false})
        void getApplicableOccupancyDemandForecastsForecastGroupLevelAllRoomClasses(boolean overlapsSpecialEvents) {
            DemandOverrideRequest request = DemandOverrideRequest.builder()
                    .startDate(LocalDate.of(2022, 1, 1))
                    .endDate(LocalDate.of(2022, 2, 1))
                    .daysOfWeek(Set.of(DayOfWeek.MONDAY, DayOfWeek.WEDNESDAY, DayOfWeek.FRIDAY))
                    .specialEventSettings(SpecialEventSettings.builder()
                            .specialEvents(Set.of("WINTER"))
                            .overlapsWithSpecialEvents(overlapsSpecialEvents)
                            .build())
                    .forecastGroupNames(Set.of("Group"))
                    .action(Action.INCREASE)
                    .quantity(2.0)
                    .unit(Unit.PERCENTAGE)
                    .build();

            List<Range<LocalDate>> splits = List.of(
                    Range.between(LocalDate.of(2022, 1, 1), LocalDate.of(2022, 1, 7), LocalDate::compareTo),
                    Range.between(LocalDate.of(2022, 1, 14), LocalDate.of(2022, 1, 21), LocalDate::compareTo)
            );

            OccupancyDemandForecast forecastA = new OccupancyDemandForecast();
            forecastA.setOccupancyDate(LocalDateUtils.toDate(LocalDate.of(2022, 1, 5)));
            forecastA.setUserRemainingDemand(BigDecimal.TEN);

            OccupancyDemandForecast forecastB = new OccupancyDemandForecast();
            forecastB.setOccupancyDate(LocalDateUtils.toDate(LocalDate.of(2022, 1, 17)));
            forecastB.setUserRemainingDemand(BigDecimal.TEN);

            OccupancyDemandForecast skippedGFFConflict = new OccupancyDemandForecast();
            skippedGFFConflict.setOccupancyDate(LocalDateUtils.toDate(LocalDate.of(2022, 1, 7)));
            skippedGFFConflict.setUserRemainingDemand(BigDecimal.TEN);

            OccupancyDemandForecast skippedLOSConflict = new OccupancyDemandForecast();
            skippedLOSConflict.setOccupancyDate(LocalDateUtils.toDate(LocalDate.of(2022, 1, 19)));
            skippedLOSConflict.setUserRemainingDemand(BigDecimal.TEN);

            ArrivalDemandOverride removableLOSOverride = new ArrivalDemandOverride();

            List<OccupancyDemandForecast> occupancyDemandForecasts = List.of(skippedGFFConflict, skippedLOSConflict, forecastA, forecastB);

            if (overlapsSpecialEvents) {
                when(splitter.splitIncludeOverlapsWithSpecialEvents(request.getStartDate(), request.getEndDate(), request.getSpecialEventSettings().getSpecialEvents())).thenReturn(splits);
            } else {
                when(splitter.splitExcludeOverlapsWithSpecialEvents(request.getStartDate(), request.getEndDate(), request.getSpecialEventSettings().getSpecialEvents())).thenReturn(splits);
            }

            when(demandOverrideService.getOccupancyDemandForecastsBetweenDatesAndForecastGroupNamesAndProductId(
                    LocalDate.of(2022, 1, 3),
                    LocalDate.of(2022, 1, 21),
                    Set.of("Group"),
                    1
            )).thenReturn(occupancyDemandForecasts);

            when(gffConflictFilter.filterWithGFFConflicts(occupancyDemandForecasts)).thenReturn(Tuple2.of(List.of(skippedGFFConflict), List.of(skippedLOSConflict, forecastA, forecastB)));
            when(arrivalLOSConflictFilter.filterWithOverrideConflicts(List.of(skippedLOSConflict, forecastA, forecastB))).thenReturn(Tuple3.of(List.of(removableLOSOverride), List.of(skippedLOSConflict), List.of(forecastA, forecastB)));

            ApplicableOccupancyDemandForecastResults result = service.getApplicableOccupancyDemandForecasts(request);

            assertEquals(List.of(removableLOSOverride), result.getRemovableArrivalLOSOverrides());
            assertEquals(List.of(skippedGFFConflict), result.getSkippedForecastsBecauseOfGffOverrides());
            assertEquals(List.of(skippedLOSConflict), result.getSkippedForecastsBecauseOfArrivalLOSOverrides());
            assertEquals(List.of(forecastA, forecastB), result.getOverridableForecasts());
            if (overlapsSpecialEvents) {
                verify(splitter, never()).splitExcludeOverlapsWithSpecialEvents(any(), any(), any());
            } else {
                verify(splitter, never()).splitIncludeOverlapsWithSpecialEvents(any(), any(), any());
            }
            verify(gffConflictFilter).filterWithGFFConflicts(occupancyDemandForecasts);
            verify(arrivalLOSConflictFilter).filterWithOverrideConflicts(List.of(skippedLOSConflict, forecastA, forecastB));
        }

        @ParameterizedTest(name = "getApplicableOccupancyDemandForecastsPercentageChangeSkipZeroUserRemainingDemand - overlap special events: {0}")
        @ValueSource(booleans = {true, false})
        void getApplicableOccupancyDemandForecastsPercentageChangeSkipZeroUserRemainingDemand(boolean overlapsSpecialEvents) {
            DemandOverrideRequest request = DemandOverrideRequest.builder()
                    .startDate(LocalDate.of(2022, 1, 1))
                    .endDate(LocalDate.of(2022, 2, 1))
                    .daysOfWeek(Set.of(DayOfWeek.MONDAY, DayOfWeek.WEDNESDAY, DayOfWeek.FRIDAY))
                    .specialEventSettings(SpecialEventSettings.builder()
                            .specialEvents(Set.of("WINTER"))
                            .overlapsWithSpecialEvents(overlapsSpecialEvents)
                            .build())
                    .forecastGroupNames(Set.of("Group"))
                    .action(Action.INCREASE)
                    .quantity(2.0)
                    .unit(Unit.PERCENTAGE)
                    .build();

            List<Range<LocalDate>> splits = List.of(
                    Range.between(LocalDate.of(2022, 1, 1), LocalDate.of(2022, 1, 7), LocalDate::compareTo),
                    Range.between(LocalDate.of(2022, 1, 14), LocalDate.of(2022, 1, 21), LocalDate::compareTo)
            );

            OccupancyDemandForecast forecastA = new OccupancyDemandForecast();
            forecastA.setOccupancyDate(LocalDateUtils.toDate(LocalDate.of(2022, 1, 5)));
            forecastA.setUserRemainingDemand(BigDecimal.TEN);

            OccupancyDemandForecast forecastB = new OccupancyDemandForecast();
            forecastB.setOccupancyDate(LocalDateUtils.toDate(LocalDate.of(2022, 1, 17)));
            forecastB.setUserRemainingDemand(BigDecimal.ZERO);

            OccupancyDemandForecast skippedGFFConflict = new OccupancyDemandForecast();
            skippedGFFConflict.setOccupancyDate(LocalDateUtils.toDate(LocalDate.of(2022, 1, 7)));
            skippedGFFConflict.setUserRemainingDemand(BigDecimal.TEN);

            OccupancyDemandForecast skippedLOSConflict = new OccupancyDemandForecast();
            skippedLOSConflict.setOccupancyDate(LocalDateUtils.toDate(LocalDate.of(2022, 1, 19)));
            skippedLOSConflict.setUserRemainingDemand(BigDecimal.TEN);

            ArrivalDemandOverride removableLOSOverride = new ArrivalDemandOverride();

            List<OccupancyDemandForecast> occupancyDemandForecasts = List.of(skippedGFFConflict, skippedLOSConflict, forecastA, forecastB);
            List<OccupancyDemandForecast> occupancyDemandForecastsWithNonZeroUserRemainingDemand = List.of(skippedGFFConflict, skippedLOSConflict, forecastA);

            if (overlapsSpecialEvents) {
                when(splitter.splitIncludeOverlapsWithSpecialEvents(request.getStartDate(), request.getEndDate(), request.getSpecialEventSettings().getSpecialEvents())).thenReturn(splits);
            } else {
                when(splitter.splitExcludeOverlapsWithSpecialEvents(request.getStartDate(), request.getEndDate(), request.getSpecialEventSettings().getSpecialEvents())).thenReturn(splits);
            }

            when(demandOverrideService.getOccupancyDemandForecastsBetweenDatesAndForecastGroupNamesAndProductId(
                    LocalDate.of(2022, 1, 3),
                    LocalDate.of(2022, 1, 21),
                    Set.of("Group"),
                    1
            )).thenReturn(occupancyDemandForecasts);

            when(gffConflictFilter.filterWithGFFConflicts(occupancyDemandForecastsWithNonZeroUserRemainingDemand)).thenReturn(Tuple2.of(List.of(skippedGFFConflict), List.of(skippedLOSConflict, forecastA)));
            when(arrivalLOSConflictFilter.filterWithOverrideConflicts(List.of(skippedLOSConflict, forecastA))).thenReturn(Tuple3.of(List.of(removableLOSOverride), List.of(skippedLOSConflict), List.of(forecastA)));

            ApplicableOccupancyDemandForecastResults result = service.getApplicableOccupancyDemandForecasts(request);

            assertEquals(List.of(removableLOSOverride), result.getRemovableArrivalLOSOverrides());
            assertEquals(List.of(skippedGFFConflict), result.getSkippedForecastsBecauseOfGffOverrides());
            assertEquals(List.of(skippedLOSConflict), result.getSkippedForecastsBecauseOfArrivalLOSOverrides());
            assertEquals(List.of(forecastA), result.getOverridableForecasts());
            if (overlapsSpecialEvents) {
                verify(splitter, never()).splitExcludeOverlapsWithSpecialEvents(any(), any(), any());
            } else {
                verify(splitter, never()).splitIncludeOverlapsWithSpecialEvents(any(), any(), any());
            }
            verify(gffConflictFilter).filterWithGFFConflicts(occupancyDemandForecastsWithNonZeroUserRemainingDemand);
            verify(arrivalLOSConflictFilter).filterWithOverrideConflicts(List.of(skippedLOSConflict, forecastA));
        }

        @ParameterizedTest(name = "getApplicableOccupancyDemandForecastsForecastGroupLevelOnlyMasterClass - overlap special events: {0}")
        @ValueSource(booleans = {true, false})
        void getApplicableOccupancyDemandForecastsForecastGroupLevelOnlyMasterClass(boolean overlapsSpecialEvents) {
            DemandOverrideRequest request = DemandOverrideRequest.builder()
                    .startDate(LocalDate.of(2022, 1, 1))
                    .endDate(LocalDate.of(2022, 2, 1))
                    .daysOfWeek(Set.of(DayOfWeek.MONDAY, DayOfWeek.WEDNESDAY, DayOfWeek.FRIDAY))
                    .specialEventSettings(SpecialEventSettings.builder()
                            .specialEvents(Set.of("WINTER"))
                            .overlapsWithSpecialEvents(overlapsSpecialEvents)
                            .build())
                    .forecastGroupNames(Set.of("Group"))
                    .action(Action.INCREASE)
                    .quantity(2.0)
                    .unit(Unit.VALUE)
                    .build();

            List<Range<LocalDate>> splits = List.of(
                    Range.between(LocalDate.of(2022, 1, 1), LocalDate.of(2022, 1, 7), LocalDate::compareTo),
                    Range.between(LocalDate.of(2022, 1, 14), LocalDate.of(2022, 1, 21), LocalDate::compareTo)
            );

            AccomClass masterClass = new AccomClass();
            masterClass.setId(1);

            OccupancyDemandForecast forecastA = new OccupancyDemandForecast();
            forecastA.setOccupancyDate(LocalDateUtils.toDate(LocalDate.of(2022, 1, 5)));
            forecastA.setAccomClassID(1);

            OccupancyDemandForecast forecastB = new OccupancyDemandForecast();
            forecastB.setOccupancyDate(LocalDateUtils.toDate(LocalDate.of(2022, 1, 17)));
            forecastB.setAccomClassID(2);

            OccupancyDemandForecast skippedGFFConflict = new OccupancyDemandForecast();
            skippedGFFConflict.setAccomClassID(1);
            skippedGFFConflict.setOccupancyDate(LocalDateUtils.toDate(LocalDate.of(2022, 1, 7)));

            OccupancyDemandForecast skippedLOSConflict = new OccupancyDemandForecast();
            skippedLOSConflict.setAccomClassID(1);
            skippedLOSConflict.setOccupancyDate(LocalDateUtils.toDate(LocalDate.of(2022, 1, 19)));

            ArrivalDemandOverride removableLOSOverride = new ArrivalDemandOverride();

            List<OccupancyDemandForecast> occupancyDemandForecasts = List.of(skippedGFFConflict, skippedLOSConflict, forecastA, forecastB);

            if (overlapsSpecialEvents) {
                when(splitter.splitIncludeOverlapsWithSpecialEvents(request.getStartDate(), request.getEndDate(), request.getSpecialEventSettings().getSpecialEvents())).thenReturn(splits);
            } else {
                when(splitter.splitExcludeOverlapsWithSpecialEvents(request.getStartDate(), request.getEndDate(), request.getSpecialEventSettings().getSpecialEvents())).thenReturn(splits);
            }

            when(demandOverrideService.getOccupancyDemandForecastsBetweenDatesAndForecastGroupNamesAndProductId(
                    LocalDate.of(2022, 1, 3),
                    LocalDate.of(2022, 1, 21),
                    Set.of("Group"),
                    1
            )).thenReturn(occupancyDemandForecasts);
            when(accommodationService.findMasterClass(123)).thenReturn(masterClass);

            when(gffConflictFilter.filterWithGFFConflicts(List.of(skippedGFFConflict, skippedLOSConflict, forecastA))).thenReturn(Tuple2.of(List.of(skippedGFFConflict), List.of(skippedLOSConflict, forecastA)));
            when(arrivalLOSConflictFilter.filterWithOverrideConflicts(List.of(skippedLOSConflict, forecastA))).thenReturn(Tuple3.of(List.of(removableLOSOverride), List.of(skippedLOSConflict), List.of(forecastA)));

            ApplicableOccupancyDemandForecastResults result = service.getApplicableOccupancyDemandForecasts(request);

            assertEquals(List.of(removableLOSOverride), result.getRemovableArrivalLOSOverrides());
            assertEquals(List.of(skippedGFFConflict), result.getSkippedForecastsBecauseOfGffOverrides());
            assertEquals(List.of(skippedLOSConflict), result.getSkippedForecastsBecauseOfArrivalLOSOverrides());
            assertEquals(List.of(forecastA), result.getOverridableForecasts());

            if (overlapsSpecialEvents) {
                verify(splitter, never()).splitExcludeOverlapsWithSpecialEvents(any(), any(), any());
            } else {
                verify(splitter, never()).splitIncludeOverlapsWithSpecialEvents(any(), any(), any());
            }
            verify(gffConflictFilter).filterWithGFFConflicts(List.of(skippedGFFConflict, skippedLOSConflict, forecastA));
            verify(arrivalLOSConflictFilter).filterWithOverrideConflicts(List.of(skippedLOSConflict, forecastA));
        }
    }

    @Nested
    class GetApplicableOccupancyDemandOverridesSuite {

        @ParameterizedTest(name = "getApplicableOccupancyDemandForecastsPropertyLevel - overlap special events: {0}")
        @ValueSource(booleans = {true, false})
        void getApplicableOccupancyDemandForecastsPropertyLevel(boolean overlapsSpecialEvents) {
            DemandOverrideRequest request = DemandOverrideRequest.builder()
                    .startDate(LocalDate.of(2022, 1, 1))
                    .endDate(LocalDate.of(2022, 2, 1))
                    .daysOfWeek(Set.of(DayOfWeek.MONDAY, DayOfWeek.WEDNESDAY, DayOfWeek.FRIDAY))
                    .specialEventSettings(SpecialEventSettings.builder()
                            .specialEvents(Set.of("WINTER"))
                            .overlapsWithSpecialEvents(overlapsSpecialEvents)
                            .build())
                    .forecastGroupNames(Collections.emptySet())
                    .action(Action.INCREASE)
                    .quantity(2.0)
                    .unit(Unit.PERCENTAGE)
                    .build();

            List<Range<LocalDate>> splits = List.of(
                    Range.between(LocalDate.of(2022, 1, 1), LocalDate.of(2022, 1, 7), LocalDate::compareTo),
                    Range.between(LocalDate.of(2022, 1, 14), LocalDate.of(2022, 1, 21), LocalDate::compareTo)
            );

            OccupancyDemandOverride overrideA = new OccupancyDemandOverride();
            overrideA.setOccupancyDate(LocalDateUtils.toDate(LocalDate.of(2022, 1, 5)));

            OccupancyDemandOverride overrideB = new OccupancyDemandOverride();
            overrideB.setOccupancyDate(LocalDateUtils.toDate(LocalDate.of(2022, 1, 17)));

            List<OccupancyDemandOverride> occupancyDemandOverrides = List.of(overrideA, overrideB);

            if (overlapsSpecialEvents) {
                when(splitter.splitIncludeOverlapsWithSpecialEvents(request.getStartDate(), request.getEndDate(), request.getSpecialEventSettings().getSpecialEvents())).thenReturn(splits);
            } else {
                when(splitter.splitExcludeOverlapsWithSpecialEvents(request.getStartDate(), request.getEndDate(), request.getSpecialEventSettings().getSpecialEvents())).thenReturn(splits);
            }

            when(demandOverrideService.getOccupancyDemandOverridesBetweenDatesAndByProductId(
                    LocalDate.of(2022, 1, 3),
                    LocalDate.of(2022, 1, 21),
                    1
            )).thenReturn(occupancyDemandOverrides);

            List<OccupancyDemandOverride> result = service.getApplicableOccupancyDemandOverrides(request);

            assertEquals(occupancyDemandOverrides, result);

            if (overlapsSpecialEvents) {
                verify(splitter, never()).splitExcludeOverlapsWithSpecialEvents(any(), any(), any());
            } else {
                verify(splitter, never()).splitIncludeOverlapsWithSpecialEvents(any(), any(), any());
            }
        }

        @ParameterizedTest(name = "getApplicableOccupancyDemandForecastsForecastGroupLevel - overlap special events: {0}")
        @ValueSource(booleans = {true, false})
        void getApplicableOccupancyDemandForecastsForecastGroupLevel(boolean overlapsSpecialEvents) {
            DemandOverrideRequest request = DemandOverrideRequest.builder()
                    .startDate(LocalDate.of(2022, 1, 1))
                    .endDate(LocalDate.of(2022, 2, 1))
                    .daysOfWeek(Set.of(DayOfWeek.MONDAY, DayOfWeek.WEDNESDAY, DayOfWeek.FRIDAY))
                    .specialEventSettings(SpecialEventSettings.builder()
                            .specialEvents(Set.of("WINTER"))
                            .overlapsWithSpecialEvents(overlapsSpecialEvents)
                            .build())
                    .forecastGroupNames(Set.of("Group"))
                    .action(Action.INCREASE)
                    .quantity(2.0)
                    .unit(Unit.PERCENTAGE)
                    .build();

            List<Range<LocalDate>> splits = List.of(
                    Range.between(LocalDate.of(2022, 1, 1), LocalDate.of(2022, 1, 7), LocalDate::compareTo),
                    Range.between(LocalDate.of(2022, 1, 14), LocalDate.of(2022, 1, 21), LocalDate::compareTo)
            );

            OccupancyDemandOverride overrideA = new OccupancyDemandOverride();
            overrideA.setOccupancyDate(LocalDateUtils.toDate(LocalDate.of(2022, 1, 5)));

            OccupancyDemandOverride overrideB = new OccupancyDemandOverride();
            overrideB.setOccupancyDate(LocalDateUtils.toDate(LocalDate.of(2022, 1, 17)));

            List<OccupancyDemandOverride> occupancyDemandOverrides = List.of(overrideA, overrideB);

            if (overlapsSpecialEvents) {
                when(splitter.splitIncludeOverlapsWithSpecialEvents(request.getStartDate(), request.getEndDate(), request.getSpecialEventSettings().getSpecialEvents())).thenReturn(splits);
            } else {
                when(splitter.splitExcludeOverlapsWithSpecialEvents(request.getStartDate(), request.getEndDate(), request.getSpecialEventSettings().getSpecialEvents())).thenReturn(splits);
            }

            when(demandOverrideService.getOccupancyDemandOverridesBetweenDatesAndForecastGroupNamesAndProductId(
                    LocalDate.of(2022, 1, 3),
                    LocalDate.of(2022, 1, 21),
                    Set.of("Group"),
                    1
            )).thenReturn(occupancyDemandOverrides);

            List<OccupancyDemandOverride> result = service.getApplicableOccupancyDemandOverrides(request);

            assertEquals(occupancyDemandOverrides, result);

            if (overlapsSpecialEvents) {
                verify(splitter, never()).splitExcludeOverlapsWithSpecialEvents(any(), any(), any());
            } else {
                verify(splitter, never()).splitIncludeOverlapsWithSpecialEvents(any(), any(), any());
            }
        }
    }

    @Nested
    class GetAdvancedDemandOverrideResultsSuite {

        @ParameterizedTest(name = "getApplicableOccupancyDemandForecastsEmpty - overlap special events: {0}")
        @ValueSource(booleans = {true, false})
        void getAdvancedDemandOverrideResultsEmpty(boolean overlapsSpecialEvents) {
            AdvancedDemandOverrideRequest request = AdvancedDemandOverrideRequest.builder()
                    .forecastGroupsToDemandOverrideSettings(Map.of(
                            "FG-1", List.of(
                                    DemandOverrideSettings.builder()
                                            .startDate(LocalDate.of(2022, 1, 1))
                                            .endDate(LocalDate.of(2022, 2, 1))
                                            .daysOfWeek(Set.of(DayOfWeek.MONDAY, DayOfWeek.WEDNESDAY, DayOfWeek.FRIDAY))
                                            .specialEventSettings(SpecialEventSettings.builder()
                                                    .specialEvents(Set.of("WINTER"))
                                                    .overlapsWithSpecialEvents(overlapsSpecialEvents)
                                                    .build())
                                            .action(Action.DECREASE)
                                            .build()
                            ),
                            "FG-2", List.of(
                                    DemandOverrideSettings.builder()
                                            .startDate(LocalDate.of(2023, 1, 1))
                                            .endDate(LocalDate.of(2023, 2, 1))
                                            .daysOfWeek(Set.of(DayOfWeek.MONDAY, DayOfWeek.WEDNESDAY, DayOfWeek.FRIDAY))
                                            .specialEventSettings(SpecialEventSettings.builder()
                                                    .specialEvents(Set.of("WINTER"))
                                                    .overlapsWithSpecialEvents(overlapsSpecialEvents)
                                                    .build())
                                            .action(Action.INCREASE)
                                            .build()
                            )
                    ))
                    .build();
            List<Range<LocalDate>> splits = Collections.emptyList();

            if (overlapsSpecialEvents) {
                when(splitter.splitIncludeOverlapsWithSpecialEvents(
                        request.getForecastGroupsToDemandOverrideSettings().get("FG-1").get(0).getStartDate(),
                        request.getForecastGroupsToDemandOverrideSettings().get("FG-1").get(0).getEndDate(),
                        request.getForecastGroupsToDemandOverrideSettings().get("FG-1").get(0).getSpecialEventSettings().getSpecialEvents())
                ).thenReturn(splits);

                when(splitter.splitIncludeOverlapsWithSpecialEvents(
                        request.getForecastGroupsToDemandOverrideSettings().get("FG-2").get(0).getStartDate(),
                        request.getForecastGroupsToDemandOverrideSettings().get("FG-2").get(0).getEndDate(),
                        request.getForecastGroupsToDemandOverrideSettings().get("FG-2").get(0).getSpecialEventSettings().getSpecialEvents())
                ).thenReturn(splits);
            } else {
                when(splitter.splitExcludeOverlapsWithSpecialEvents(
                        request.getForecastGroupsToDemandOverrideSettings().get("FG-1").get(0).getStartDate(),
                        request.getForecastGroupsToDemandOverrideSettings().get("FG-1").get(0).getEndDate(),
                        request.getForecastGroupsToDemandOverrideSettings().get("FG-1").get(0).getSpecialEventSettings().getSpecialEvents())
                ).thenReturn(splits);

                when(splitter.splitExcludeOverlapsWithSpecialEvents(
                        request.getForecastGroupsToDemandOverrideSettings().get("FG-2").get(0).getStartDate(),
                        request.getForecastGroupsToDemandOverrideSettings().get("FG-2").get(0).getEndDate(),
                        request.getForecastGroupsToDemandOverrideSettings().get("FG-2").get(0).getSpecialEventSettings().getSpecialEvents())
                ).thenReturn(splits);
            }

            AdvancedOccupancyDemandOverrideResults result = service.getAdvancedDemandOverrideResults(request);

            assertEquals(Collections.emptyList(), result.getSkippedForecastsBecauseOfArrivalLOSOverrides());
            assertEquals(Collections.emptyList(), result.getSkippedForecastsBecauseOfGffOverrides());
            assertEquals(Collections.emptyList(), result.getRemovableArrivalLOSOverrides());
            assertEquals(Collections.emptyList(), result.getOccupancyDemandOverridesToPersist());

            verifyNoInteractions(demandOverrideService, gffConflictFilter, arrivalLOSConflictFilter);
        }

        @ParameterizedTest(name = "getApplicableOccupancyDemandForecastsPropertyLevel - overlap special events: {0}")
        @ValueSource(booleans = {true, false})
        void getAdvancedDemandOverrideResultsPropertyLevel(boolean overlapsSpecialEvents) {
            Map<String, List<DemandOverrideSettings>> forecastGroupsToDemandOverrideSettings = new HashMap<>();
            forecastGroupsToDemandOverrideSettings.put(null, List.of(
                    DemandOverrideSettings.builder()
                            .startDate(LocalDate.of(2022, 1, 1))
                            .endDate(LocalDate.of(2022, 2, 1))
                            .daysOfWeek(Set.of(DayOfWeek.MONDAY, DayOfWeek.WEDNESDAY, DayOfWeek.FRIDAY))
                            .specialEventSettings(SpecialEventSettings.builder()
                                    .specialEvents(Set.of("WINTER"))
                                    .overlapsWithSpecialEvents(overlapsSpecialEvents)
                                    .build())
                            .action(Action.DECREASE)
                            .quantity(10.0)
                            .unit(Unit.PERCENTAGE)
                            .build()
            ));
            AdvancedDemandOverrideRequest request = AdvancedDemandOverrideRequest.builder()
                    .forecastGroupsToDemandOverrideSettings(forecastGroupsToDemandOverrideSettings)
                    .build();

            List<Range<LocalDate>> splits = List.of(
                    Range.between(LocalDate.of(2022, 1, 1), LocalDate.of(2022, 1, 7), LocalDate::compareTo),
                    Range.between(LocalDate.of(2022, 1, 14), LocalDate.of(2022, 1, 21), LocalDate::compareTo)
            );

            OccupancyDemandForecast forecastA = new OccupancyDemandForecast();
            com.ideas.tetris.pacman.services.demandoverride.dto.OccupancyDemandOverride overrideA = new com.ideas.tetris.pacman.services.demandoverride.dto.OccupancyDemandOverride();
            forecastA.setOccupancyDate(LocalDateUtils.toDate(LocalDate.of(2022, 1, 5)));
            forecastA.setUserRemainingDemand(BigDecimal.TEN);

            OccupancyDemandForecast forecastB = new OccupancyDemandForecast();
            com.ideas.tetris.pacman.services.demandoverride.dto.OccupancyDemandOverride overrideB = new com.ideas.tetris.pacman.services.demandoverride.dto.OccupancyDemandOverride();
            forecastB.setOccupancyDate(LocalDateUtils.toDate(LocalDate.of(2022, 1, 17)));
            forecastB.setUserRemainingDemand(BigDecimal.TEN);

            OccupancyDemandForecast skippedGFFConflict = new OccupancyDemandForecast();
            skippedGFFConflict.setOccupancyDate(LocalDateUtils.toDate(LocalDate.of(2022, 1, 7)));
            skippedGFFConflict.setUserRemainingDemand(BigDecimal.TEN);

            OccupancyDemandForecast skippedLOSConflict = new OccupancyDemandForecast();
            skippedLOSConflict.setOccupancyDate(LocalDateUtils.toDate(LocalDate.of(2022, 1, 19)));
            skippedLOSConflict.setUserRemainingDemand(BigDecimal.TEN);

            ArrivalDemandOverride removableLOSOverride = new ArrivalDemandOverride();

            List<OccupancyDemandForecast> occupancyDemandForecasts = List.of(skippedGFFConflict, skippedLOSConflict, forecastA, forecastB);

            Function<OccupancyDemandForecast, com.ideas.tetris.pacman.services.demandoverride.dto.OccupancyDemandOverride> simpleCalculator = (forecast) -> {
                if (forecast == forecastA) {
                    return overrideA;
                } else if (forecast == forecastB) {
                    return overrideB;
                } else {
                    return null;
                }
            };
            when(factory.build(Action.DECREASE, 10.0, Unit.PERCENTAGE)).thenReturn(simpleCalculator);

            if (overlapsSpecialEvents) {
                when(splitter.splitIncludeOverlapsWithSpecialEvents(
                        request.getForecastGroupsToDemandOverrideSettings().get(null).get(0).getStartDate(),
                        request.getForecastGroupsToDemandOverrideSettings().get(null).get(0).getEndDate(),
                        request.getForecastGroupsToDemandOverrideSettings().get(null).get(0).getSpecialEventSettings().getSpecialEvents())
                ).thenReturn(splits);
            } else {
                when(splitter.splitExcludeOverlapsWithSpecialEvents(
                        request.getForecastGroupsToDemandOverrideSettings().get(null).get(0).getStartDate(),
                        request.getForecastGroupsToDemandOverrideSettings().get(null).get(0).getEndDate(),
                        request.getForecastGroupsToDemandOverrideSettings().get(null).get(0).getSpecialEventSettings().getSpecialEvents())
                ).thenReturn(splits);
            }

            when(demandOverrideService.getOccupancyDemandForecastsBetweenDatesAndByProductId(
                    LocalDate.of(2022, 1, 3),
                    LocalDate.of(2022, 1, 21),
                    1
            )).thenReturn(occupancyDemandForecasts);

            when(gffConflictFilter.filterWithGFFConflicts(occupancyDemandForecasts)).thenReturn(Tuple2.of(List.of(skippedGFFConflict), List.of(skippedLOSConflict, forecastA, forecastB)));
            when(arrivalLOSConflictFilter.filterWithOverrideConflicts(List.of(skippedLOSConflict, forecastA, forecastB))).thenReturn(Tuple3.of(List.of(removableLOSOverride), List.of(skippedLOSConflict), List.of(forecastA, forecastB)));

            AdvancedOccupancyDemandOverrideResults result = service.getAdvancedDemandOverrideResults(request);

            assertEquals(List.of(removableLOSOverride), result.getRemovableArrivalLOSOverrides());
            assertEquals(List.of(skippedGFFConflict), result.getSkippedForecastsBecauseOfGffOverrides());
            assertEquals(List.of(skippedLOSConflict), result.getSkippedForecastsBecauseOfArrivalLOSOverrides());
            assertEquals(2, result.getOccupancyDemandOverridesToPersist().size());
            assertEquals(Set.of(overrideA, overrideB), new HashSet<>(result.getOccupancyDemandOverridesToPersist()));

            if (overlapsSpecialEvents) {
                verify(splitter, never()).splitExcludeOverlapsWithSpecialEvents(any(), any(), any());
            } else {
                verify(splitter, never()).splitIncludeOverlapsWithSpecialEvents(any(), any(), any());
            }
            verify(gffConflictFilter).filterWithGFFConflicts(occupancyDemandForecasts);
            verify(arrivalLOSConflictFilter).filterWithOverrideConflicts(List.of(skippedLOSConflict, forecastA, forecastB));
        }

        @ParameterizedTest(name = "getApplicableOccupancyDemandForecastsForecastGroupLevelAllRoomClasses - overlap special events: {0}")
        @ValueSource(booleans = {true, false})
        void getAdvancedDemandOverrideResultsForecastGroupLevelAllRoomClasses(boolean overlapsSpecialEvents) {
            Map<String, List<DemandOverrideSettings>> forecastGroupsToDemandOverrideSettings = new HashMap<>();
            forecastGroupsToDemandOverrideSettings.put("Group", List.of(
                    DemandOverrideSettings.builder()
                            .startDate(LocalDate.of(2022, 1, 1))
                            .endDate(LocalDate.of(2022, 2, 1))
                            .daysOfWeek(Set.of(DayOfWeek.MONDAY, DayOfWeek.WEDNESDAY, DayOfWeek.FRIDAY))
                            .specialEventSettings(SpecialEventSettings.builder()
                                    .specialEvents(Set.of("WINTER"))
                                    .overlapsWithSpecialEvents(overlapsSpecialEvents)
                                    .build())
                            .action(Action.INCREASE)
                            .quantity(2.0)
                            .unit(Unit.PERCENTAGE)
                            .build()
            ));
            AdvancedDemandOverrideRequest request = AdvancedDemandOverrideRequest.builder()
                    .forecastGroupsToDemandOverrideSettings(forecastGroupsToDemandOverrideSettings)
                    .build();

            List<Range<LocalDate>> splits = List.of(
                    Range.between(LocalDate.of(2022, 1, 1), LocalDate.of(2022, 1, 7), LocalDate::compareTo),
                    Range.between(LocalDate.of(2022, 1, 14), LocalDate.of(2022, 1, 21), LocalDate::compareTo)
            );

            OccupancyDemandForecast forecastA = new OccupancyDemandForecast();
            com.ideas.tetris.pacman.services.demandoverride.dto.OccupancyDemandOverride overrideA = new com.ideas.tetris.pacman.services.demandoverride.dto.OccupancyDemandOverride();
            forecastA.setOccupancyDate(LocalDateUtils.toDate(LocalDate.of(2022, 1, 5)));
            forecastA.setUserRemainingDemand(BigDecimal.TEN);

            OccupancyDemandForecast forecastB = new OccupancyDemandForecast();
            com.ideas.tetris.pacman.services.demandoverride.dto.OccupancyDemandOverride overrideB = new com.ideas.tetris.pacman.services.demandoverride.dto.OccupancyDemandOverride();
            forecastB.setOccupancyDate(LocalDateUtils.toDate(LocalDate.of(2022, 1, 17)));
            forecastB.setUserRemainingDemand(BigDecimal.TEN);

            OccupancyDemandForecast skippedGFFConflict = new OccupancyDemandForecast();
            skippedGFFConflict.setOccupancyDate(LocalDateUtils.toDate(LocalDate.of(2022, 1, 7)));
            skippedGFFConflict.setUserRemainingDemand(BigDecimal.TEN);

            OccupancyDemandForecast skippedLOSConflict = new OccupancyDemandForecast();
            skippedLOSConflict.setOccupancyDate(LocalDateUtils.toDate(LocalDate.of(2022, 1, 19)));
            skippedLOSConflict.setUserRemainingDemand(BigDecimal.TEN);

            ArrivalDemandOverride removableLOSOverride = new ArrivalDemandOverride();

            List<OccupancyDemandForecast> occupancyDemandForecasts = List.of(skippedGFFConflict, skippedLOSConflict, forecastA, forecastB);

            Function<OccupancyDemandForecast, com.ideas.tetris.pacman.services.demandoverride.dto.OccupancyDemandOverride> simpleCalculator = (forecast) -> {
                if (forecast == forecastA) {
                    return overrideA;
                } else if (forecast == forecastB) {
                    return overrideB;
                } else {
                    return null;
                }
            };
            when(factory.build(Action.INCREASE, 2.0, Unit.PERCENTAGE)).thenReturn(simpleCalculator);

            if (overlapsSpecialEvents) {
                when(splitter.splitIncludeOverlapsWithSpecialEvents(
                        request.getForecastGroupsToDemandOverrideSettings().get("Group").get(0).getStartDate(),
                        request.getForecastGroupsToDemandOverrideSettings().get("Group").get(0).getEndDate(),
                        request.getForecastGroupsToDemandOverrideSettings().get("Group").get(0).getSpecialEventSettings().getSpecialEvents())
                ).thenReturn(splits);
            } else {
                when(splitter.splitExcludeOverlapsWithSpecialEvents(
                        request.getForecastGroupsToDemandOverrideSettings().get("Group").get(0).getStartDate(),
                        request.getForecastGroupsToDemandOverrideSettings().get("Group").get(0).getEndDate(),
                        request.getForecastGroupsToDemandOverrideSettings().get("Group").get(0).getSpecialEventSettings().getSpecialEvents())
                ).thenReturn(splits);
            }

            when(demandOverrideService.getOccupancyDemandForecastsBetweenDatesAndForecastGroupNamesAndProductId(
                    LocalDate.of(2022, 1, 3),
                    LocalDate.of(2022, 1, 21),
                    Set.of("Group"),
                    1
            )).thenReturn(occupancyDemandForecasts);

            when(gffConflictFilter.filterWithGFFConflicts(occupancyDemandForecasts)).thenReturn(Tuple2.of(List.of(skippedGFFConflict), List.of(skippedLOSConflict, forecastA, forecastB)));
            when(arrivalLOSConflictFilter.filterWithOverrideConflicts(List.of(skippedLOSConflict, forecastA, forecastB))).thenReturn(Tuple3.of(List.of(removableLOSOverride), List.of(skippedLOSConflict), List.of(forecastA, forecastB)));

            AdvancedOccupancyDemandOverrideResults result = service.getAdvancedDemandOverrideResults(request);

            assertEquals(List.of(removableLOSOverride), result.getRemovableArrivalLOSOverrides());
            assertEquals(List.of(skippedGFFConflict), result.getSkippedForecastsBecauseOfGffOverrides());
            assertEquals(List.of(skippedLOSConflict), result.getSkippedForecastsBecauseOfArrivalLOSOverrides());
            assertEquals(2, result.getOccupancyDemandOverridesToPersist().size());
            assertEquals(Set.of(overrideA, overrideB), new HashSet<>(result.getOccupancyDemandOverridesToPersist()));
            if (overlapsSpecialEvents) {
                verify(splitter, never()).splitExcludeOverlapsWithSpecialEvents(any(), any(), any());
            } else {
                verify(splitter, never()).splitIncludeOverlapsWithSpecialEvents(any(), any(), any());
            }
            verify(gffConflictFilter).filterWithGFFConflicts(occupancyDemandForecasts);
            verify(arrivalLOSConflictFilter).filterWithOverrideConflicts(List.of(skippedLOSConflict, forecastA, forecastB));
        }

        @ParameterizedTest(name = "getApplicableOccupancyDemandForecastsPercentageChangeSkipZeroUserRemainingDemand - overlap special events: {0}")
        @ValueSource(booleans = {true, false})
        void getAdvancedDemandOverrideResultsForecastsPercentageChangeSkipZeroUserRemainingDemand(boolean overlapsSpecialEvents) {
            Map<String, List<DemandOverrideSettings>> forecastGroupsToDemandOverrideSettings = new HashMap<>();
            forecastGroupsToDemandOverrideSettings.put("Group", List.of(
                    DemandOverrideSettings.builder()
                            .startDate(LocalDate.of(2022, 1, 1))
                            .endDate(LocalDate.of(2022, 2, 1))
                            .daysOfWeek(Set.of(DayOfWeek.MONDAY, DayOfWeek.WEDNESDAY, DayOfWeek.FRIDAY))
                            .specialEventSettings(SpecialEventSettings.builder()
                                    .specialEvents(Set.of("WINTER"))
                                    .overlapsWithSpecialEvents(overlapsSpecialEvents)
                                    .build())
                            .action(Action.INCREASE)
                            .quantity(2.0)
                            .unit(Unit.PERCENTAGE)
                            .build()
            ));
            AdvancedDemandOverrideRequest request = AdvancedDemandOverrideRequest.builder()
                    .forecastGroupsToDemandOverrideSettings(forecastGroupsToDemandOverrideSettings)
                    .build();

            List<Range<LocalDate>> splits = List.of(
                    Range.between(LocalDate.of(2022, 1, 1), LocalDate.of(2022, 1, 7), LocalDate::compareTo),
                    Range.between(LocalDate.of(2022, 1, 14), LocalDate.of(2022, 1, 21), LocalDate::compareTo)
            );

            OccupancyDemandForecast forecastA = new OccupancyDemandForecast();
            com.ideas.tetris.pacman.services.demandoverride.dto.OccupancyDemandOverride overrideA = new com.ideas.tetris.pacman.services.demandoverride.dto.OccupancyDemandOverride();
            forecastA.setOccupancyDate(LocalDateUtils.toDate(LocalDate.of(2022, 1, 5)));
            forecastA.setUserRemainingDemand(BigDecimal.TEN);

            OccupancyDemandForecast forecastB = new OccupancyDemandForecast();
            forecastB.setOccupancyDate(LocalDateUtils.toDate(LocalDate.of(2022, 1, 17)));
            forecastB.setUserRemainingDemand(BigDecimal.ZERO);

            OccupancyDemandForecast skippedGFFConflict = new OccupancyDemandForecast();
            skippedGFFConflict.setOccupancyDate(LocalDateUtils.toDate(LocalDate.of(2022, 1, 7)));
            skippedGFFConflict.setUserRemainingDemand(BigDecimal.TEN);

            OccupancyDemandForecast skippedLOSConflict = new OccupancyDemandForecast();
            skippedLOSConflict.setOccupancyDate(LocalDateUtils.toDate(LocalDate.of(2022, 1, 19)));
            skippedLOSConflict.setUserRemainingDemand(BigDecimal.TEN);

            ArrivalDemandOverride removableLOSOverride = new ArrivalDemandOverride();

            List<OccupancyDemandForecast> occupancyDemandForecasts = List.of(skippedGFFConflict, skippedLOSConflict, forecastA, forecastB);
            List<OccupancyDemandForecast> occupancyDemandForecastsWithNonZeroUserRemainingDemand = List.of(skippedGFFConflict, skippedLOSConflict, forecastA);

            Function<OccupancyDemandForecast, com.ideas.tetris.pacman.services.demandoverride.dto.OccupancyDemandOverride> simpleCalculator = (forecast) -> {
                if (forecast == forecastA) {
                    return overrideA;
                } else {
                    return null;
                }
            };
            when(factory.build(Action.INCREASE, 2.0, Unit.PERCENTAGE)).thenReturn(simpleCalculator);

            if (overlapsSpecialEvents) {
                when(splitter.splitIncludeOverlapsWithSpecialEvents(
                        request.getForecastGroupsToDemandOverrideSettings().get("Group").get(0).getStartDate(),
                        request.getForecastGroupsToDemandOverrideSettings().get("Group").get(0).getEndDate(),
                        request.getForecastGroupsToDemandOverrideSettings().get("Group").get(0).getSpecialEventSettings().getSpecialEvents())
                ).thenReturn(splits);
            } else {
                when(splitter.splitExcludeOverlapsWithSpecialEvents(
                        request.getForecastGroupsToDemandOverrideSettings().get("Group").get(0).getStartDate(),
                        request.getForecastGroupsToDemandOverrideSettings().get("Group").get(0).getEndDate(),
                        request.getForecastGroupsToDemandOverrideSettings().get("Group").get(0).getSpecialEventSettings().getSpecialEvents())
                ).thenReturn(splits);
            }

            when(demandOverrideService.getOccupancyDemandForecastsBetweenDatesAndForecastGroupNamesAndProductId(
                    LocalDate.of(2022, 1, 3),
                    LocalDate.of(2022, 1, 21),
                    Set.of("Group"),
                    1
            )).thenReturn(occupancyDemandForecasts);

            when(gffConflictFilter.filterWithGFFConflicts(occupancyDemandForecastsWithNonZeroUserRemainingDemand)).thenReturn(Tuple2.of(List.of(skippedGFFConflict), List.of(skippedLOSConflict, forecastA)));
            when(arrivalLOSConflictFilter.filterWithOverrideConflicts(List.of(skippedLOSConflict, forecastA))).thenReturn(Tuple3.of(List.of(removableLOSOverride), List.of(skippedLOSConflict), List.of(forecastA)));

            AdvancedOccupancyDemandOverrideResults result = service.getAdvancedDemandOverrideResults(request);

            assertEquals(List.of(removableLOSOverride), result.getRemovableArrivalLOSOverrides());
            assertEquals(List.of(skippedGFFConflict), result.getSkippedForecastsBecauseOfGffOverrides());
            assertEquals(List.of(skippedLOSConflict), result.getSkippedForecastsBecauseOfArrivalLOSOverrides());
            assertEquals(List.of(overrideA), result.getOccupancyDemandOverridesToPersist());

            if (overlapsSpecialEvents) {
                verify(splitter, never()).splitExcludeOverlapsWithSpecialEvents(any(), any(), any());
            } else {
                verify(splitter, never()).splitIncludeOverlapsWithSpecialEvents(any(), any(), any());
            }
            verify(gffConflictFilter).filterWithGFFConflicts(occupancyDemandForecastsWithNonZeroUserRemainingDemand);
            verify(arrivalLOSConflictFilter).filterWithOverrideConflicts(List.of(skippedLOSConflict, forecastA));
        }

        @ParameterizedTest(name = "getApplicableOccupancyDemandForecastsForecastGroupLevelOnlyMasterClass - overlap special events: {0}")
        @ValueSource(booleans = {true, false})
        void getAdvancedDemandOverrideResultsForecastGroupLevelOnlyMasterClass(boolean overlapsSpecialEvents) {
            Map<String, List<DemandOverrideSettings>> forecastGroupsToDemandOverrideSettings = new HashMap<>();
            forecastGroupsToDemandOverrideSettings.put("Group", List.of(
                    DemandOverrideSettings.builder()
                            .startDate(LocalDate.of(2022, 1, 1))
                            .endDate(LocalDate.of(2022, 2, 1))
                            .daysOfWeek(Set.of(DayOfWeek.MONDAY, DayOfWeek.WEDNESDAY, DayOfWeek.FRIDAY))
                            .specialEventSettings(SpecialEventSettings.builder()
                                    .specialEvents(Set.of("WINTER"))
                                    .overlapsWithSpecialEvents(overlapsSpecialEvents)
                                    .build())
                            .action(Action.INCREASE)
                            .quantity(2.0)
                            .unit(Unit.VALUE)
                            .build()
            ));
            AdvancedDemandOverrideRequest request = AdvancedDemandOverrideRequest.builder()
                    .forecastGroupsToDemandOverrideSettings(forecastGroupsToDemandOverrideSettings)
                    .build();


            List<Range<LocalDate>> splits = List.of(
                    Range.between(LocalDate.of(2022, 1, 1), LocalDate.of(2022, 1, 7), LocalDate::compareTo),
                    Range.between(LocalDate.of(2022, 1, 14), LocalDate.of(2022, 1, 21), LocalDate::compareTo)
            );

            AccomClass masterClass = new AccomClass();
            masterClass.setId(1);

            OccupancyDemandForecast forecastA = new OccupancyDemandForecast();
            com.ideas.tetris.pacman.services.demandoverride.dto.OccupancyDemandOverride overrideA = new com.ideas.tetris.pacman.services.demandoverride.dto.OccupancyDemandOverride();
            forecastA.setOccupancyDate(LocalDateUtils.toDate(LocalDate.of(2022, 1, 5)));
            forecastA.setAccomClassID(1);

            OccupancyDemandForecast forecastB = new OccupancyDemandForecast();
            forecastB.setOccupancyDate(LocalDateUtils.toDate(LocalDate.of(2022, 1, 17)));
            forecastB.setAccomClassID(2);

            OccupancyDemandForecast skippedGFFConflict = new OccupancyDemandForecast();
            skippedGFFConflict.setAccomClassID(1);
            skippedGFFConflict.setOccupancyDate(LocalDateUtils.toDate(LocalDate.of(2022, 1, 7)));

            OccupancyDemandForecast skippedLOSConflict = new OccupancyDemandForecast();
            skippedLOSConflict.setAccomClassID(1);
            skippedLOSConflict.setOccupancyDate(LocalDateUtils.toDate(LocalDate.of(2022, 1, 19)));

            ArrivalDemandOverride removableLOSOverride = new ArrivalDemandOverride();

            List<OccupancyDemandForecast> occupancyDemandForecasts = List.of(skippedGFFConflict, skippedLOSConflict, forecastA, forecastB);

            Function<OccupancyDemandForecast, com.ideas.tetris.pacman.services.demandoverride.dto.OccupancyDemandOverride> simpleCalculator = (forecast) -> {
                if (forecast == forecastA) {
                    return overrideA;
                } else {
                    return null;
                }
            };
            when(factory.build(Action.INCREASE, 2.0, Unit.VALUE)).thenReturn(simpleCalculator);

            if (overlapsSpecialEvents) {
                when(splitter.splitIncludeOverlapsWithSpecialEvents(
                        request.getForecastGroupsToDemandOverrideSettings().get("Group").get(0).getStartDate(),
                        request.getForecastGroupsToDemandOverrideSettings().get("Group").get(0).getEndDate(),
                        request.getForecastGroupsToDemandOverrideSettings().get("Group").get(0).getSpecialEventSettings().getSpecialEvents())
                ).thenReturn(splits);
            } else {
                when(splitter.splitExcludeOverlapsWithSpecialEvents(
                        request.getForecastGroupsToDemandOverrideSettings().get("Group").get(0).getStartDate(),
                        request.getForecastGroupsToDemandOverrideSettings().get("Group").get(0).getEndDate(),
                        request.getForecastGroupsToDemandOverrideSettings().get("Group").get(0).getSpecialEventSettings().getSpecialEvents())
                ).thenReturn(splits);
            }

            when(demandOverrideService.getOccupancyDemandForecastsBetweenDatesAndForecastGroupNamesAndProductId(
                    LocalDate.of(2022, 1, 3),
                    LocalDate.of(2022, 1, 21),
                    Set.of("Group"),
                    1
            )).thenReturn(occupancyDemandForecasts);
            when(accommodationService.findMasterClass(123)).thenReturn(masterClass);

            when(gffConflictFilter.filterWithGFFConflicts(List.of(skippedGFFConflict, skippedLOSConflict, forecastA))).thenReturn(Tuple2.of(List.of(skippedGFFConflict), List.of(skippedLOSConflict, forecastA)));
            when(arrivalLOSConflictFilter.filterWithOverrideConflicts(List.of(skippedLOSConflict, forecastA))).thenReturn(Tuple3.of(List.of(removableLOSOverride), List.of(skippedLOSConflict), List.of(forecastA)));

            AdvancedOccupancyDemandOverrideResults result = service.getAdvancedDemandOverrideResults(request);

            assertEquals(List.of(removableLOSOverride), result.getRemovableArrivalLOSOverrides());
            assertEquals(List.of(skippedGFFConflict), result.getSkippedForecastsBecauseOfGffOverrides());
            assertEquals(List.of(skippedLOSConflict), result.getSkippedForecastsBecauseOfArrivalLOSOverrides());
            assertEquals(List.of(overrideA), result.getOccupancyDemandOverridesToPersist());

            if (overlapsSpecialEvents) {
                verify(splitter, never()).splitExcludeOverlapsWithSpecialEvents(any(), any(), any());
            } else {
                verify(splitter, never()).splitIncludeOverlapsWithSpecialEvents(any(), any(), any());
            }
            verify(gffConflictFilter).filterWithGFFConflicts(List.of(skippedGFFConflict, skippedLOSConflict, forecastA));
            verify(arrivalLOSConflictFilter).filterWithOverrideConflicts(List.of(skippedLOSConflict, forecastA));
        }
    }

    @Nested
    class GetAdvancedApplicableOccupancyDemandOverridesSuite {

        @ParameterizedTest(name = "getApplicableOccupancyDemandForecastsPropertyLevel - overlap special events: {0}")
        @ValueSource(booleans = {true, false})
        void getApplicableOccupancyDemandForecastsPropertyLevel(boolean overlapsSpecialEvents) {
            Map<String, List<DemandOverrideSettings>> forecastGroupsToDemandOverrideSettings = new HashMap<>();
            forecastGroupsToDemandOverrideSettings.put(null, List.of(
                    DemandOverrideSettings.builder()
                            .startDate(LocalDate.of(2022, 1, 1))
                            .endDate(LocalDate.of(2022, 2, 1))
                            .daysOfWeek(Set.of(DayOfWeek.MONDAY, DayOfWeek.WEDNESDAY, DayOfWeek.FRIDAY))
                            .specialEventSettings(SpecialEventSettings.builder()
                                    .specialEvents(Set.of("WINTER"))
                                    .overlapsWithSpecialEvents(overlapsSpecialEvents)
                                    .build())
                            .action(Action.INCREASE)
                            .quantity(2.0)
                            .unit(Unit.PERCENTAGE)
                            .build()
            ));
            AdvancedDemandOverrideRequest request = AdvancedDemandOverrideRequest.builder()
                    .forecastGroupsToDemandOverrideSettings(forecastGroupsToDemandOverrideSettings)
                    .build();

            List<Range<LocalDate>> splits = List.of(
                    Range.between(LocalDate.of(2022, 1, 1), LocalDate.of(2022, 1, 7), LocalDate::compareTo),
                    Range.between(LocalDate.of(2022, 1, 14), LocalDate.of(2022, 1, 21), LocalDate::compareTo)
            );

            OccupancyDemandOverride overrideA = new OccupancyDemandOverride();
            overrideA.setOccupancyDate(LocalDateUtils.toDate(LocalDate.of(2022, 1, 5)));

            OccupancyDemandOverride overrideB = new OccupancyDemandOverride();
            overrideB.setOccupancyDate(LocalDateUtils.toDate(LocalDate.of(2022, 1, 17)));

            List<OccupancyDemandOverride> occupancyDemandOverrides = List.of(overrideA, overrideB);

            if (overlapsSpecialEvents) {
                when(splitter.splitIncludeOverlapsWithSpecialEvents(
                        request.getForecastGroupsToDemandOverrideSettings().get(null).get(0).getStartDate(),
                        request.getForecastGroupsToDemandOverrideSettings().get(null).get(0).getEndDate(),
                        request.getForecastGroupsToDemandOverrideSettings().get(null).get(0).getSpecialEventSettings().getSpecialEvents())
                ).thenReturn(splits);
            } else {
                when(splitter.splitExcludeOverlapsWithSpecialEvents(
                        request.getForecastGroupsToDemandOverrideSettings().get(null).get(0).getStartDate(),
                        request.getForecastGroupsToDemandOverrideSettings().get(null).get(0).getEndDate(),
                        request.getForecastGroupsToDemandOverrideSettings().get(null).get(0).getSpecialEventSettings().getSpecialEvents())
                ).thenReturn(splits);
            }

            when(demandOverrideService.getOccupancyDemandOverridesBetweenDatesAndByProductId(
                    LocalDate.of(2022, 1, 3),
                    LocalDate.of(2022, 1, 21),
                    1
            )).thenReturn(occupancyDemandOverrides);

            List<OccupancyDemandOverride> result = service.getApplicableOccupancyDemandOverrides(request);

            assertEquals(occupancyDemandOverrides, result);

            if (overlapsSpecialEvents) {
                verify(splitter, never()).splitExcludeOverlapsWithSpecialEvents(any(), any(), any());
            } else {
                verify(splitter, never()).splitIncludeOverlapsWithSpecialEvents(any(), any(), any());
            }
        }

        @ParameterizedTest(name = "getApplicableOccupancyDemandForecastsForecastGroupLevel - overlap special events: {0}")
        @ValueSource(booleans = {true, false})
        void getApplicableOccupancyDemandForecastsForecastGroupLevel(boolean overlapsSpecialEvents) {
            Map<String, List<DemandOverrideSettings>> forecastGroupsToDemandOverrideSettings = new HashMap<>();
            forecastGroupsToDemandOverrideSettings.put("Group", List.of(
                    DemandOverrideSettings.builder()
                            .startDate(LocalDate.of(2022, 1, 1))
                            .endDate(LocalDate.of(2022, 2, 1))
                            .daysOfWeek(Set.of(DayOfWeek.MONDAY, DayOfWeek.WEDNESDAY, DayOfWeek.FRIDAY))
                            .specialEventSettings(SpecialEventSettings.builder()
                                    .specialEvents(Set.of("WINTER"))
                                    .overlapsWithSpecialEvents(overlapsSpecialEvents)
                                    .build())
                            .action(Action.INCREASE)
                            .quantity(2.0)
                            .unit(Unit.PERCENTAGE)
                            .build()
            ));
            AdvancedDemandOverrideRequest request = AdvancedDemandOverrideRequest.builder()
                    .forecastGroupsToDemandOverrideSettings(forecastGroupsToDemandOverrideSettings)
                    .build();

            List<Range<LocalDate>> splits = List.of(
                    Range.between(LocalDate.of(2022, 1, 1), LocalDate.of(2022, 1, 7), LocalDate::compareTo),
                    Range.between(LocalDate.of(2022, 1, 14), LocalDate.of(2022, 1, 21), LocalDate::compareTo)
            );

            OccupancyDemandOverride overrideA = new OccupancyDemandOverride();
            overrideA.setOccupancyDate(LocalDateUtils.toDate(LocalDate.of(2022, 1, 5)));

            OccupancyDemandOverride overrideB = new OccupancyDemandOverride();
            overrideB.setOccupancyDate(LocalDateUtils.toDate(LocalDate.of(2022, 1, 17)));

            List<OccupancyDemandOverride> occupancyDemandOverrides = List.of(overrideA, overrideB);

            if (overlapsSpecialEvents) {
                when(splitter.splitIncludeOverlapsWithSpecialEvents(
                        request.getForecastGroupsToDemandOverrideSettings().get("Group").get(0).getStartDate(),
                        request.getForecastGroupsToDemandOverrideSettings().get("Group").get(0).getEndDate(),
                        request.getForecastGroupsToDemandOverrideSettings().get("Group").get(0).getSpecialEventSettings().getSpecialEvents())
                ).thenReturn(splits);
            } else {
                when(splitter.splitExcludeOverlapsWithSpecialEvents(
                        request.getForecastGroupsToDemandOverrideSettings().get("Group").get(0).getStartDate(),
                        request.getForecastGroupsToDemandOverrideSettings().get("Group").get(0).getEndDate(),
                        request.getForecastGroupsToDemandOverrideSettings().get("Group").get(0).getSpecialEventSettings().getSpecialEvents())
                ).thenReturn(splits);
            }

            when(demandOverrideService.getOccupancyDemandOverridesBetweenDatesAndForecastGroupNamesAndProductId(
                    LocalDate.of(2022, 1, 3),
                    LocalDate.of(2022, 1, 21),
                    Set.of("Group"),
                    1
            )).thenReturn(occupancyDemandOverrides);

            List<OccupancyDemandOverride> result = service.getApplicableOccupancyDemandOverrides(request);

            assertEquals(occupancyDemandOverrides, result);

            if (overlapsSpecialEvents) {
                verify(splitter, never()).splitExcludeOverlapsWithSpecialEvents(any(), any(), any());
            } else {
                verify(splitter, never()).splitIncludeOverlapsWithSpecialEvents(any(), any(), any());
            }
        }
    }

}
