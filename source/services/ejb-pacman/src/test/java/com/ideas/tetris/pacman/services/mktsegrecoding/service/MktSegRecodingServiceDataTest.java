package com.ideas.tetris.pacman.services.mktsegrecoding.service;

import com.codepoetics.protonpack.StreamUtils;
import com.ideas.g3.data.TestClient;
import com.ideas.g3.data.TestProperty;
import com.ideas.g3.test.AbstractG3JupiterTest;
import com.ideas.g3.test.category.SlowDBTest;
import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.agilerates.configuration.entity.AgileRatesDecisionsSentBy;
import com.ideas.tetris.pacman.services.agilerates.configuration.entity.ProductRateCode;
import com.ideas.tetris.pacman.services.agilerates.configuration.service.AgileRatesConfigurationService;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.TenantStatusEnum;
import com.ideas.tetris.pacman.services.independentproducts.repository.IndependentProductsRepository;
import com.ideas.tetris.pacman.services.independentproducts.service.IndependentProductsService;
import com.ideas.tetris.pacman.services.marketsegment.entity.ForecastActivityType;
import com.ideas.tetris.pacman.services.marketsegment.entity.MarketSegmentMaster;
import com.ideas.tetris.pacman.services.marketsegment.entity.MarketSegmentProductMapping;
import com.ideas.tetris.pacman.services.marketsegment.entity.MktSeg;
import com.ideas.tetris.pacman.services.marketsegment.entity.MktSegDetailsProposed;
import com.ideas.tetris.pacman.services.marketsegment.entity.YieldCategoryRule;
import com.ideas.tetris.pacman.services.marketsegment.repository.MarketSegmentRepository;
import com.ideas.tetris.pacman.services.marketsegment.service.AnalyticalMarketSegment;
import com.ideas.tetris.pacman.services.marketsegment.service.AnalyticalMarketSegmentAttribute;
import com.ideas.tetris.pacman.services.marketsegment.service.AnalyticalMarketSegmentService;
import com.ideas.tetris.pacman.services.marketsegment.service.AnalyticalMarketSegmentSummary;
import com.ideas.tetris.pacman.services.marketsegment.service.MarketSegmentService;
import com.ideas.tetris.pacman.services.marketsegment.service.RateCodeTypeEnum;
import com.ideas.tetris.pacman.services.marketsegment.service.YieldCategoryRuleService;
import com.ideas.tetris.pacman.services.mktsegrecoding.dto.MktSegRecodingAMSSummary;
import com.ideas.tetris.pacman.services.mktsegrecoding.entity.MktSegRecodingBusinessTypeShift;
import com.ideas.tetris.pacman.services.mktsegrecoding.entity.MktSegRecodingConfig;
import com.ideas.tetris.pacman.services.mktsegrecoding.entity.PMSMigrationTemporaryMapping;
import com.ideas.tetris.pacman.services.mktsegrecoding.entity.RateCodeShiftAssociation;
import com.ideas.tetris.pacman.services.mktsegrecoding.entity.RateCodeShiftAssociationForTier;
import com.ideas.tetris.pacman.services.mktsegrecoding.entity.TierMarketSegmentMapping;
import com.ideas.tetris.pacman.services.mktsegrecoding.enums.MktSegRecodingState;
import com.ideas.tetris.pacman.services.pmsmigration.amsresolution.IgnoredRateCode;
import com.ideas.tetris.pacman.services.pmsmigration.amsresolution.NewAMSRule;
import com.ideas.tetris.pacman.services.pmsmigration.amsresolution.PMSRevampAMSDataService;
import com.ideas.tetris.pacman.services.pmsmigration.entity.PMSMigrationConfig;
import com.ideas.tetris.pacman.services.pmsmigration.entity.PMSMigrationMapping;
import com.ideas.tetris.pacman.services.pmsmigration.entity.PMSMigrationRateCodeMktSegMapping;
import com.ideas.tetris.pacman.services.pmsmigration.enums.PMSMigrationMappingType;
import com.ideas.tetris.pacman.services.pmsmigration.services.PMSMigrationMappingService;
import com.ideas.tetris.pacman.services.pmsmigration.services.PMSMigrationService;
import com.ideas.tetris.pacman.services.product.FloorType;
import com.ideas.tetris.pacman.services.product.OverridableProductEnum;
import com.ideas.tetris.pacman.services.product.Product;
import com.ideas.tetris.pacman.services.product.ProductCode;
import com.ideas.tetris.pacman.services.revenuestreams.RevenueStreamDetailCreator;
import com.ideas.tetris.pacman.services.revenuestreams.entity.RevenueStream;
import com.ideas.tetris.pacman.testdatabuilder.ProductBuilder;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.time.LocalDateUtils;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import com.ideas.tetris.platform.services.daoandentities.entity.Client;
import com.ideas.tetris.platform.services.daoandentities.entity.Property;
import com.ideas.tetris.platform.services.daoandentities.entity.Status;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import javax.persistence.NoResultException;
import java.math.BigDecimal;
import java.text.ParseException;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

import static com.ideas.tetris.pacman.services.mktsegrecoding.entity.RateCodeShiftAssociationForTier.INSERT_RATE_CODE_SHIFT_RULES_QUERY_PREFIX;
import static com.ideas.tetris.pacman.services.mktsegrecoding.service.MktSegRecodingTestHelper.MS1_MARKET_CODE;
import static com.ideas.tetris.pacman.services.mktsegrecoding.service.MktSegRecodingTestHelper.MS1_QYL_MARKET_CODE;
import static com.ideas.tetris.pacman.services.mktsegrecoding.service.MktSegRecodingTestHelper.MS1_STRAIGHT_MARKET_CODE;
import static com.ideas.tetris.pacman.services.mktsegrecoding.service.MktSegRecodingTestHelper.MS1_USB_MARKET_CODE;
import static com.ideas.tetris.pacman.services.mktsegrecoding.service.MktSegRecodingTestHelper.RANK_10;
import static com.ideas.tetris.pacman.services.mktsegrecoding.service.MktSegRecodingTestHelper.RC1_RATE_CODE;
import static com.ideas.tetris.pacman.services.mktsegrecoding.service.MktSegRecodingTestHelper.RC2_RATE_CODE;
import static com.ideas.tetris.pacman.services.mktsegrecoding.service.MktSegRecodingTestHelper.RC3_RATE_CODE;
import static com.ideas.tetris.pacman.services.mktsegrecoding.service.MktSegRecodingTestHelper.getAnalyticalMarketSegment;
import static com.ideas.tetris.pacman.services.mktsegrecoding.service.MktSegRecodingTestHelper.getAnalyticalMarketSegmentSummary;
import static com.ideas.tetris.pacman.services.mktsegrecoding.service.MktSegRecodingTestHelper.getNewAMSRule;
import static com.ideas.tetris.pacman.services.product.RoundingRule.PRICE_ROUNDING;
import static org.apache.commons.lang3.ObjectUtils.isNotEmpty;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNotSame;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.anyBoolean;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@SlowDBTest
@MockitoSettings(strictness = Strictness.LENIENT)
public class MktSegRecodingServiceDataTest extends AbstractG3JupiterTest {

    private static final String OLD_MS_1 = "MS1";
    private static final String OLD_MS_2 = "MS2";
    private static final String OLD_MS_3 = "MS3";
    private static final String NEW_MS_1 = "NEWMS1";
    private static final String NEW_MS_2 = "NEWMS2";
    private static final String NEW_MS_3 = "NEWMS3";

    private static final String OLD_RC_1 = "RC1";
    private static final String OLD_RC_2 = "RC6";
    private static final String OLD_RC_3 = "RC11";
    private static final String OLD_RC_1_ASSIGNMENT_RULE = "C";
    private static final String OLD_RC_2_ASSIGNMENT_RULE = "R";
    private static final String OLD_RC_3_ASSIGNMENT_RULE = "RC";
    private static final String NEW_RC_1 = "NEWRC1";
    private static final String NEW_RC_2 = "NEWRC6";
    private static final String NEW_RC_3 = "NEWRC11";

    private static final String OLD_MAPPED_MARKET_CODE_1 = "MS1_QYL";
    private static final String OLD_MAPPED_MARKET_CODE_1_DEF = "MS1_DEF";
    private static final String OLD_MAPPED_MARKET_CODE_2 = "MS2_QYL";
    private static final String OLD_MAPPED_MARKET_CODE_3 = "MS3_QYL";

    private static final String CODE_TYPE_MS = "MARKET_SEGMENT_NON_GROUP";
    private static final String CODE_TYPE_RC = "RATE_CODE";

    private static final String ATTRIBUTE = "EQUAL_TO_BAR";
    public static final String QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE = "QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE";
    public static final String QUALIFIED_NONBLOCK_LINKED_YIELDABLE = "QUALIFIED_NONBLOCK_LINKED_YIELDABLE";
    public static final String GROUP = "GROUP";
    public static final String EQUAL_TO_BAR = "EQUAL_TO_BAR";
    public static final String CODE_TYPE_MS_GROUP = "MARKET_SEGMENT";
    public static final String UNQUALIFIED_FENCED = "UNQUALIFIED_FENCED";
    public static final String FENCED = "FENCED";


    @Mock
    PMSRevampAMSDataService pmsRevampAMSDataService;
    @Mock
    PMSMigrationMappingService pmsMigrationMappingService;
    @Mock
    PMSMigrationService pmsMigrationService;
    @Mock
    private PacmanConfigParamsService pacmanConfigParamsService;
    @Mock
    private MarketSegmentRepository marketSegmentRepository;
    @Mock
    IndependentProductsRepository independentProductsRepository;

    @Spy
    @InjectMocks
    private MktSegRecodingService service;

    PMSMigrationMappingCreationHelper pmsMigrationMappingCreationHelper;
    AnalyticalMarketSegmentCreationHelper analyticalMarketSegmentCreationHelper;

    AnalyticalMarketSegmentService analyticalMarketSegmentService;
    public TierMarketSegmentMappingCreationHelper tierMarketSegmentMappingCreationHelper;
    public RevenueStreamDetailCreator revenueStreamDetailCreator;
    @Mock
    IndependentProductsService independentProductsService;

    @BeforeEach
    public void setUp() throws Exception {
        setWorkContextProperty(TestProperty.H2);
        analyticalMarketSegmentService = new AnalyticalMarketSegmentService();
        inject(service, "tenantCrudService", tenantCrudService());
        inject(analyticalMarketSegmentService, "crudService", tenantCrudService());
        inject(analyticalMarketSegmentService, "independentProductsService", independentProductsService);
        inject(analyticalMarketSegmentService, "configParamsServiceLocal", pacmanConfigParamsService);
        inject(service, "analyticalMarketSegmentService", analyticalMarketSegmentService);
        inject(service, "pacmanConfigParamsService", pacmanConfigParamsService);
        pmsMigrationMappingCreationHelper = new PMSMigrationMappingCreationHelper(tenantCrudService());
        analyticalMarketSegmentCreationHelper = new AnalyticalMarketSegmentCreationHelper(tenantCrudService());
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.CUSTOMIZED_MS_RECODING_FLOW_ENABELD))
                .thenReturn(false);
        tierMarketSegmentMappingCreationHelper = new TierMarketSegmentMappingCreationHelper(tenantCrudService());
        revenueStreamDetailCreator = new RevenueStreamDetailCreator(tenantCrudService());
    }

    @Test
    public void assignMktSegToNewAMSRules_MktSegGroup_MktSegRename() {
        setUpServiceForAssigningMSToNewAMSRulesWithNoAttributeChange();

        addPMSMigrationMappingWith("MARKET_SEGMENT", "FAM", "fam1", null);
        addPMSRevampNewAMSRule("fam1", null, QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");
        addAnalyticalMarketSegmentWith("FAM", null, "FAM", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "ALL", "1");
        addMktSegMaster("FAM", 1);
        Integer FAM_MKT_SEG_ID = addMarketSegment("FAM");

        service.assignMktSegToNewAMSRules();

        MktSeg mktSeg = tenantCrudService().find(MktSeg.class, FAM_MKT_SEG_ID);
        List<AnalyticalMarketSegment> analyticalMarketSegments = tenantCrudService().findAll(AnalyticalMarketSegment.class);
        List<MarketSegmentMaster> mktSegMasters = tenantCrudService().findAll(MarketSegmentMaster.class);
        assertEquals("fam1", mktSeg.getCode());
        assertEquals(2, analyticalMarketSegments.size());
        assertTrue(analyticalMarketSegments.stream().anyMatch(ams -> ams.getMarketCode().equalsIgnoreCase("fam1") && ams.getRateCode() == null &&
                ams.getMappedMarketCode().equalsIgnoreCase("fam1") && ams.getAttribute().equals(AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE) &&
                ams.getRateCodeType().equals(RateCodeTypeEnum.ALL)));
        assertTrue(analyticalMarketSegments.stream().anyMatch(ams -> ams.getMarketCode().equalsIgnoreCase("FAM") && ams.getRateCode() == null &&
                ams.getMappedMarketCode().equalsIgnoreCase("FAM") && ams.getAttribute().equals(AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE) &&
                ams.getRateCodeType().equals(RateCodeTypeEnum.ALL)));
        assertTrue(mktSegMasters.stream().anyMatch(mktSegMaster -> mktSegMaster.getCode().equals("fam1")));
    }

    @Test
    public void shouldRemoveProductMarketSegmentMappingForOldMktCodeWhenMktSegCodeIsRenamed() {
        setUpServiceForAssigningMSToNewAMSRulesWithNoAttributeChange();
        injectIPService();
        addPMSMigrationMappingWith("MARKET_SEGMENT", "FAM", "fam1", null);
        addPMSRevampNewAMSRule("fam1", null, QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");
        addAnalyticalMarketSegmentWith("FAM", null, "FAM", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "ALL", "1");
        addMktSegMaster("FAM", 1);
        Integer FAM_MKT_SEG_ID = addMarketSegment("FAM");
        addMarketSegmentProductMapping("FAM", addProduct("IND1"));
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_MS_RECODING_SUPPORT_FOR_INDEPENDENT_PRODUCT)).thenReturn(true);
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.INDEPENDENT_PRODUCTS_ENABLED)).thenReturn(true);

        service.assignMktSegToNewAMSRules();

        MktSeg mktSeg = tenantCrudService().find(MktSeg.class, FAM_MKT_SEG_ID);
        List<AnalyticalMarketSegment> analyticalMarketSegments = tenantCrudService().findAll(AnalyticalMarketSegment.class);
        List<MarketSegmentMaster> mktSegMasters = tenantCrudService().findAll(MarketSegmentMaster.class);
        assertEquals("fam1", mktSeg.getCode());
        assertEquals(2, analyticalMarketSegments.size());
        assertTrue(analyticalMarketSegments.stream().anyMatch(ams -> ams.getMarketCode().equalsIgnoreCase("fam1") && ams.getRateCode() == null &&
                ams.getMappedMarketCode().equalsIgnoreCase("fam1") && ams.getAttribute().equals(AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE) &&
                ams.getRateCodeType().equals(RateCodeTypeEnum.ALL)));
        assertTrue(analyticalMarketSegments.stream().anyMatch(ams -> ams.getMarketCode().equalsIgnoreCase("FAM") && ams.getRateCode() == null &&
                ams.getMappedMarketCode().equalsIgnoreCase("FAM") && ams.getAttribute().equals(AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE) &&
                ams.getRateCodeType().equals(RateCodeTypeEnum.ALL)));
        assertTrue(mktSegMasters.stream().anyMatch(mktSegMaster -> mktSegMaster.getCode().equals("fam1")));
        assertEquals(0, getMarketSegmentProductMappingCountFor("FAM"));
    }

    @Test
    public void shouldNotDeleteProductMSMappingForBusinessTypeShiftWithNoChangeInAttributeAndWithCaseSensitivity() {
        setUpServiceForAssigningMSToNewAMSRulesWithNoAttributeChange();
        addPMSMigrationMappingWith(CODE_TYPE_MS_GROUP, "GC-MEET", "GC-MeetNew", null);
        addPMSMigrationMappingWith(CODE_TYPE_RC, "CF77", "CF77", null);
        addAnalyticalMarketSegmentWith("GC-MEET", null, "GC-MEET", GROUP, "ALL", "1");
        addPMSRevampNewAMSRule("GC-MeetNew", "CF77", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");
        addPMSRevampNewAMSRule("GC-MeetNew", "DEFAULT", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");
        Integer GC_MEET_MKT_SEG_ID = addMarketSegment("GC-MEET");
        addMktSegMaster("GC-MEET", 1);
        tenantCrudService().executeUpdateByNativeQuery(MktSegRecodingBusinessTypeShift.CREATE_MKT_SEG_RECODING_BUSINESS_TYPE_SHIFT_TABLE_DDL);
        tenantCrudService().executeUpdateByNativeQuery(MktSegRecodingBusinessTypeShift.CLEAN_BUSINESS_TYPE_SHIFT_TABLE_QUERY);
        createMktSegRecodingBusinessTypeShiftAssociation("GC-MeetNew", "Group", "Transient");
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-03", "2019-01-01", "GC-MEET", "CF77", GC_MEET_MKT_SEG_ID);
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-03", "2019-01-02", "GC-MEET", "CF77", GC_MEET_MKT_SEG_ID);
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_MS_RECODING_SUPPORT_FOR_INDEPENDENT_PRODUCT)).thenReturn(true);
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.INDEPENDENT_PRODUCTS_ENABLED)).thenReturn(true);

        service.assignMktSegToNewAMSRules();

        tenantCrudService().flushAndClear();
        MktSeg GC_MeetNew_QYL_MktSeg = tenantCrudService().find(MktSeg.class, GC_MEET_MKT_SEG_ID);
        List<AnalyticalMarketSegment> analyticalMarketSegments = tenantCrudService().findAll(AnalyticalMarketSegment.class);
        List<MarketSegmentMaster> mktSegMasters = tenantCrudService().findAll(MarketSegmentMaster.class);
        assertEquals(1, GC_MeetNew_QYL_MktSeg.getPropertyId());
        assertTrue(analyticalMarketSegments.stream().anyMatch(ams -> ams.getMarketCode().equalsIgnoreCase("GC-MeetNew") && ams.getRateCode() != null && ams.getRateCode().equals("CF77") &&
                ams.getMappedMarketCode().equalsIgnoreCase("GC-MeetNew_QYL") && ams.getAttribute().equals(AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE) &&
                ams.getRateCodeType().equals(RateCodeTypeEnum.EQUALS)));
        assertTrue(analyticalMarketSegments.stream().anyMatch(ams -> ams.getMarketCode().equalsIgnoreCase("GC-MeetNew") && ams.getRateCode() == null &&
                ams.getMappedMarketCode().equalsIgnoreCase("GC-MeetNew_DEF") && ams.getAttribute().equals(AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE) &&
                ams.getRateCodeType().equals(RateCodeTypeEnum.DEFAULT)));
        assertTrue(analyticalMarketSegments.stream().anyMatch(ams -> ams.getMarketCode().equalsIgnoreCase("GC-MEET") && ams.getRateCode() != null && ams.getRateCode().equals("CF77") &&
                ams.getMappedMarketCode().equalsIgnoreCase("GC-MEET_G") && ams.getAttribute().equals(AnalyticalMarketSegmentAttribute.GROUP) &&
                ams.getRateCodeType().equals(RateCodeTypeEnum.EQUALS)));
        assertTrue(analyticalMarketSegments.stream().anyMatch(ams -> ams.getMarketCode().equalsIgnoreCase("GC-MEET") && ams.getRateCode() == null &&
                ams.getMappedMarketCode().equalsIgnoreCase("GC-MEET_DEF") && ams.getAttribute().equals(AnalyticalMarketSegmentAttribute.FENCED_AND_PACKAGED) &&
                ams.getRateCodeType().equals(RateCodeTypeEnum.DEFAULT)));
        assertTrue(mktSegMasters.stream().anyMatch(mktSegMaster -> mktSegMaster.getCode().equals("GC-MeetNew_QYL")));
        assertTrue(mktSegMasters.stream().anyMatch(mktSegMaster -> mktSegMaster.getCode().equals("GC-MeetNew_DEF")));
    }

    @Test
    public void assignMktSegToNewAMSRules_MktSegNonGroup_MktSegRenameRateCodeNoChange() {
        setUpServiceForAssigningMSToNewAMSRulesWithNoAttributeChange();

        addPMSMigrationMappingWith(CODE_TYPE_MS, "FAM", "FAM1", null);
        addPMSMigrationMappingWith(CODE_TYPE_RC, "RC1", "RC1", null);
        addPMSMigrationMappingWith(CODE_TYPE_RC, "RC2", "RC2", null);
        addAnalyticalMarketSegmentWith("FAM", "RC1", "FAM_QYL", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "EQUALS", "10");
        addAnalyticalMarketSegmentWith("FAM", "RC2", "FAM_QYL", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "EQUALS", "10");
        addAnalyticalMarketSegmentWith("FAM", null, "FAM_DEF", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "DEFAULT", "999");
        addPMSRevampNewAMSRule("FAM1", "RC1", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");
        addPMSRevampNewAMSRule("FAM1", "RC2", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");
        addPMSRevampNewAMSRule("FAM1", "DEFAULT", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");
        Integer FAM_QYL_MKT_SEG_ID = addMarketSegment("FAM_QYL");
        Integer FAM_DEF_MKT_SEG_ID = addMarketSegment("FAM_DEF");
        addMktSegMaster("FAM_QYL", 1);
        addMktSegMaster("FAM_DEF", 1);

        service.assignMktSegToNewAMSRules();

        tenantCrudService().flushAndClear();
        MktSeg FAM_QYL_MktSeg = tenantCrudService().find(MktSeg.class, FAM_QYL_MKT_SEG_ID);
        MktSeg FAM_DEF_MktSeg = tenantCrudService().find(MktSeg.class, FAM_DEF_MKT_SEG_ID);
        List<AnalyticalMarketSegment> analyticalMarketSegments = tenantCrudService().findAll(AnalyticalMarketSegment.class);
        List<MarketSegmentMaster> mktSegMasters = tenantCrudService().findAll(MarketSegmentMaster.class);
        assertEquals("FAM1_QYL", FAM_QYL_MktSeg.getCode());
        assertEquals("FAM1_DEF", FAM_DEF_MktSeg.getCode());
        assertTrue(analyticalMarketSegments.stream().anyMatch(ams -> ams.getMarketCode().equalsIgnoreCase("FAM1") && ams.getRateCode() != null && ams.getRateCode().equals("RC1") &&
                ams.getMappedMarketCode().equalsIgnoreCase("FAM1_QYL") && ams.getAttribute().equals(AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE) &&
                ams.getRateCodeType().equals(RateCodeTypeEnum.EQUALS)));
        assertTrue(analyticalMarketSegments.stream().anyMatch(ams -> ams.getMarketCode().equalsIgnoreCase("FAM1") && ams.getRateCode() != null && ams.getRateCode().equals("RC2") &&
                ams.getMappedMarketCode().equalsIgnoreCase("FAM1_QYL") && ams.getAttribute().equals(AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE) &&
                ams.getRateCodeType().equals(RateCodeTypeEnum.EQUALS)));
        assertTrue(analyticalMarketSegments.stream().anyMatch(ams -> ams.getMarketCode().equalsIgnoreCase("FAM") && ams.getRateCode() != null && ams.getRateCode().equals("RC1") &&
                ams.getMappedMarketCode().equalsIgnoreCase("FAM_QYL") && ams.getAttribute().equals(AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE) &&
                ams.getRateCodeType().equals(RateCodeTypeEnum.EQUALS)));
        assertTrue(analyticalMarketSegments.stream().anyMatch(ams -> ams.getMarketCode().equalsIgnoreCase("FAM") && ams.getRateCode() != null && ams.getRateCode().equals("RC2") &&
                ams.getMappedMarketCode().equalsIgnoreCase("FAM_QYL") && ams.getAttribute().equals(AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE) &&
                ams.getRateCodeType().equals(RateCodeTypeEnum.EQUALS)));
        assertTrue(mktSegMasters.stream().anyMatch(mktSegMaster -> mktSegMaster.getCode().equals("FAM1_QYL")));
        assertTrue(mktSegMasters.stream().anyMatch(mktSegMaster -> mktSegMaster.getCode().equals("FAM1_DEF")));
    }

    @Test
    public void assignMktSegToNewAMSRules_MktSegNonGroup_MktSegRenameRateCodeNoChange_WithCaseSensitivity() {
        setUpServiceForAssigningMSToNewAMSRulesWithNoAttributeChange();

        addPMSMigrationMappingWith(CODE_TYPE_MS, "FAM", "fam1", null);
        addPMSMigrationMappingWith(CODE_TYPE_RC, "RC1", "RC1", null);
        addPMSMigrationMappingWith(CODE_TYPE_RC, "RC2", "RC2", null);
        addAnalyticalMarketSegmentWith("FAM", "RC1", "FAM_QYL", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "EQUALS", "10");
        addAnalyticalMarketSegmentWith("FAM", "RC2", "FAM_QYL", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "EQUALS", "10");
        addAnalyticalMarketSegmentWith("FAM", null, "FAM_DEF", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "DEFAULT", "999");
        addPMSRevampNewAMSRule("fam1", "RC1", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");
        addPMSRevampNewAMSRule("fam1", "RC2", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");
        addPMSRevampNewAMSRule("fam1", "DEFAULT", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");
        Integer FAM_QYL_MKT_SEG_ID = addMarketSegment("FAM_QYL");
        Integer FAM_DEF_MKT_SEG_ID = addMarketSegment("FAM_DEF");
        addMktSegMaster("FAM_QYL", 1);
        addMktSegMaster("FAM_DEF", 1);

        service.assignMktSegToNewAMSRules();

        tenantCrudService().flushAndClear();
        MktSeg FAM_QYL_MktSeg = tenantCrudService().find(MktSeg.class, FAM_QYL_MKT_SEG_ID);
        MktSeg FAM_DEF_MktSeg = tenantCrudService().find(MktSeg.class, FAM_DEF_MKT_SEG_ID);
        List<AnalyticalMarketSegment> analyticalMarketSegments = tenantCrudService().findAll(AnalyticalMarketSegment.class);
        List<MarketSegmentMaster> mktSegMasters = tenantCrudService().findAll(MarketSegmentMaster.class);
        assertEquals("fam1_QYL", FAM_QYL_MktSeg.getCode());
        assertEquals("fam1_DEF", FAM_DEF_MktSeg.getCode());
        assertTrue(analyticalMarketSegments.stream().anyMatch(ams -> ams.getMarketCode().equalsIgnoreCase("fam1") && ams.getRateCode() != null && ams.getRateCode().equals("RC1") &&
                ams.getMappedMarketCode().equalsIgnoreCase("fam1_QYL") && ams.getAttribute().equals(AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE) &&
                ams.getRateCodeType().equals(RateCodeTypeEnum.EQUALS)));
        assertTrue(analyticalMarketSegments.stream().anyMatch(ams -> ams.getMarketCode().equalsIgnoreCase("fam1") && ams.getRateCode() != null && ams.getRateCode().equals("RC2") &&
                ams.getMappedMarketCode().equalsIgnoreCase("fam1_QYL") && ams.getAttribute().equals(AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE) &&
                ams.getRateCodeType().equals(RateCodeTypeEnum.EQUALS)));
        assertTrue(analyticalMarketSegments.stream().anyMatch(ams -> ams.getMarketCode().equalsIgnoreCase("FAM") && ams.getRateCode() != null && ams.getRateCode().equals("RC1") &&
                ams.getMappedMarketCode().equalsIgnoreCase("FAM_QYL") && ams.getAttribute().equals(AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE) &&
                ams.getRateCodeType().equals(RateCodeTypeEnum.EQUALS)));
        assertTrue(analyticalMarketSegments.stream().anyMatch(ams -> ams.getMarketCode().equalsIgnoreCase("FAM") && ams.getRateCode() != null && ams.getRateCode().equals("RC2") &&
                ams.getMappedMarketCode().equalsIgnoreCase("FAM_QYL") && ams.getAttribute().equals(AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE) &&
                ams.getRateCodeType().equals(RateCodeTypeEnum.EQUALS)));
        assertTrue(mktSegMasters.stream().anyMatch(mktSegMaster -> mktSegMaster.getCode().equals("fam1_QYL")));
        assertTrue(mktSegMasters.stream().anyMatch(mktSegMaster -> mktSegMaster.getCode().equals("fam1_DEF")));
    }

    @Test
    public void assignMktSegToNewAMSRules_ShouldFilterBusinessTypeShiftInNoChangeInAttribute_WithCaseSensitivity() {
        setUpServiceForAssigningMSToNewAMSRulesWithNoAttributeChange();

        addPMSMigrationMappingWith(CODE_TYPE_MS_GROUP, "GC-MEET", "GC-MeetNew", null);
        addPMSMigrationMappingWith(CODE_TYPE_RC, "CF77", "CF77", null);
        addAnalyticalMarketSegmentWith("GC-MEET", null, "GC-MEET", GROUP, "ALL", "1");
        addPMSRevampNewAMSRule("GC-MeetNew", "CF77", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");
        addPMSRevampNewAMSRule("GC-MeetNew", "DEFAULT", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");
        Integer GC_MEET_MKT_SEG_ID = addMarketSegment("GC-MEET");
        addMktSegMaster("GC-MEET", 1);
        tenantCrudService().executeUpdateByNativeQuery(MktSegRecodingBusinessTypeShift.CREATE_MKT_SEG_RECODING_BUSINESS_TYPE_SHIFT_TABLE_DDL);
        tenantCrudService().executeUpdateByNativeQuery(MktSegRecodingBusinessTypeShift.CLEAN_BUSINESS_TYPE_SHIFT_TABLE_QUERY);
        createMktSegRecodingBusinessTypeShiftAssociation("GC-MeetNew", "Group", "Transient");
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-03", "2019-01-01", "GC-MEET", "CF77", GC_MEET_MKT_SEG_ID);
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-03", "2019-01-02", "GC-MEET", "CF77", GC_MEET_MKT_SEG_ID);

        service.assignMktSegToNewAMSRules();

        tenantCrudService().flushAndClear();
        MktSeg GC_MeetNew_QYL_MktSeg = tenantCrudService().find(MktSeg.class, GC_MEET_MKT_SEG_ID);
        List<AnalyticalMarketSegment> analyticalMarketSegments = tenantCrudService().findAll(AnalyticalMarketSegment.class);
        List<MarketSegmentMaster> mktSegMasters = tenantCrudService().findAll(MarketSegmentMaster.class);
        assertEquals(1, GC_MeetNew_QYL_MktSeg.getPropertyId());
        assertTrue(analyticalMarketSegments.stream().anyMatch(ams -> ams.getMarketCode().equalsIgnoreCase("GC-MeetNew") && ams.getRateCode() != null && ams.getRateCode().equals("CF77") &&
                ams.getMappedMarketCode().equalsIgnoreCase("GC-MeetNew_QYL") && ams.getAttribute().equals(AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE) &&
                ams.getRateCodeType().equals(RateCodeTypeEnum.EQUALS)));
        assertTrue(analyticalMarketSegments.stream().anyMatch(ams -> ams.getMarketCode().equalsIgnoreCase("GC-MeetNew") && ams.getRateCode() == null &&
                ams.getMappedMarketCode().equalsIgnoreCase("GC-MeetNew_DEF") && ams.getAttribute().equals(AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE) &&
                ams.getRateCodeType().equals(RateCodeTypeEnum.DEFAULT)));
        assertTrue(analyticalMarketSegments.stream().anyMatch(ams -> ams.getMarketCode().equalsIgnoreCase("GC-MEET") && ams.getRateCode() != null && ams.getRateCode().equals("CF77") &&
                ams.getMappedMarketCode().equalsIgnoreCase("GC-MEET_G") && ams.getAttribute().equals(AnalyticalMarketSegmentAttribute.GROUP) &&
                ams.getRateCodeType().equals(RateCodeTypeEnum.EQUALS)));
        //NOTE: Here we are asserting mapped market code with GC-MEET_G as the expected mapped market code "GC-MeetNew_QYL" is updated in preservation step ahead in the job
        assertTrue(analyticalMarketSegments.stream().anyMatch(ams -> ams.getMarketCode().equalsIgnoreCase("GC-MEET") && ams.getRateCode() == null &&
                ams.getMappedMarketCode().equalsIgnoreCase("GC-MEET_DEF") && ams.getAttribute().equals(AnalyticalMarketSegmentAttribute.FENCED_AND_PACKAGED) &&
                ams.getRateCodeType().equals(RateCodeTypeEnum.DEFAULT)));
        assertTrue(mktSegMasters.stream().anyMatch(mktSegMaster -> mktSegMaster.getCode().equals("GC-MeetNew_QYL")));
        assertTrue(mktSegMasters.stream().anyMatch(mktSegMaster -> mktSegMaster.getCode().equals("GC-MeetNew_DEF")));
    }


    @Test
    public void assignMktSegToNewAMSRules_MktSegNonGroup_MktSegRenameRateCodeNoChange_SkipTierMktSegForMGM() {
        setUpServiceForAssigningMSToNewAMSRulesWithNoAttributeChange();

        addPMSMigrationMappingWith(CODE_TYPE_MS, "INCDNL", "INCDNL_New", null);
        addPMSMigrationMappingWith(CODE_TYPE_MS, "FAM", "FAM1", null);
        addPMSMigrationMappingWith(CODE_TYPE_MS, "CSLI", "CPERP", null);
        addPMSMigrationMappingWith(CODE_TYPE_MS, "CSLP", "CPERP", null);
        addPMSMigrationMappingWith(CODE_TYPE_RC, "RC1", "RC1", null);
        addPMSMigrationMappingWith(CODE_TYPE_RC, "RC2", "RC2", null);
        addPMSMigrationMappingWith(CODE_TYPE_RC, "RC3", "RC3", null);
        addPMSMigrationMappingWith(CODE_TYPE_RC, "RC4", "RC4", null);
        addAnalyticalMarketSegmentWith("CSLI", "CAS10", "CASINO_QYL", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "EQUALS", "10");
        addAnalyticalMarketSegmentWith("CSLI", "RC1", "CASINO_CASH_1_QYL", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "EQUALS", "10");
        addAnalyticalMarketSegmentWith("CSLP", "RC2", "CASINO_CASH_1_QYL", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "EQUALS", "10");
        addAnalyticalMarketSegmentWith("CSLI", null, "CASINO_DEF", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "DEFAULT", "999");
        addAnalyticalMarketSegmentWith("CSLP", null, "CASINO_DEF", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "DEFAULT", "999");
        addAnalyticalMarketSegmentWith("FAM", "RC3", "FAM_QYL", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "EQUALS", "10");
        addAnalyticalMarketSegmentWith("FAM", null, "FAM_DEF", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "DEFAULT", "999");
        addAnalyticalMarketSegmentWith("INCDNL", "RC4", "CASINO_CASH_2_QYL", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "EQUALS", "10");
        addAnalyticalMarketSegmentWith("INCDNL", null, "CASINO_DEF", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "DEFAULT", "999");
        addPMSRevampNewAMSRule("FAM1", "RC3", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");
        addPMSRevampNewAMSRule("FAM1", "DEFAULT", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");
        addPMSRevampNewAMSRule("CPERP", "CAS10", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");
        addPMSRevampNewAMSRule("CPERP", "DEFAULT", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");
        Integer CASIO_CASH_1_QYL_MKT_SEG_ID = addMarketSegment("CASINO_CASH_1_QYL");
        Integer CASIO_CASH_2_QYL_MKT_SEG_ID = addMarketSegment("CASINO_CASH_2_QYL");
        Integer CASINO_QYL_MKT_SEG_ID = addMarketSegment("CASINO_QYL");
        Integer CASINO_DEF_MKT_SEG_ID = addMarketSegment("CASINO_DEF");
        addMktSegMaster("CASINO_CASH_1_QYL", 1);
        addMktSegMaster("CASINO_CASH_2_QYL", 1);
        addMktSegMaster("CASINO_QYL", 1);
        addMktSegMaster("CASINO_DEF", 1);
        Integer FAM_QYL_MKT_SEG_ID = addMarketSegment("FAM_QYL");
        Integer FAM_DEF_MKT_SEG_ID = addMarketSegment("FAM_DEF");
        addMktSegMaster("FAM_QYL", 1);
        addMktSegMaster("FAM_DEF", 1);

        service.assignMktSegToNewAMSRules();

        tenantCrudService().flushAndClear();
        MktSeg CASIO_CASH_1_QYL_MktSeg = tenantCrudService().find(MktSeg.class, CASIO_CASH_1_QYL_MKT_SEG_ID);
        MktSeg CASINO_QYL_MktSeg = tenantCrudService().find(MktSeg.class, CASINO_QYL_MKT_SEG_ID);
        MktSeg CASINO_DEF_MktSeg = tenantCrudService().find(MktSeg.class, CASINO_DEF_MKT_SEG_ID);
        MktSeg FAM_QYL_MktSeg = tenantCrudService().find(MktSeg.class, FAM_QYL_MKT_SEG_ID);
        MktSeg FAM_DEF_MktSeg = tenantCrudService().find(MktSeg.class, FAM_DEF_MKT_SEG_ID);
        assertEquals("FAM1_QYL", FAM_QYL_MktSeg.getCode());
        assertEquals("FAM1_DEF", FAM_DEF_MktSeg.getCode());
        List<MarketSegmentMaster> mktSegMasters = tenantCrudService().findAll(MarketSegmentMaster.class);
        assertEquals("CASINO_CASH_1_QYL", CASIO_CASH_1_QYL_MktSeg.getCode());
        assertEquals("CASINO_QYL", CASINO_QYL_MktSeg.getCode());
        assertEquals("CASINO_DEF", CASINO_DEF_MktSeg.getCode());
        assertTrue(mktSegMasters.stream().anyMatch(mktSegMaster -> mktSegMaster.getCode().equals("CASINO_CASH_1_QYL")));
        assertTrue(mktSegMasters.stream().anyMatch(mktSegMaster -> mktSegMaster.getCode().equals("CASINO_QYL")));
        assertTrue(mktSegMasters.stream().anyMatch(mktSegMaster -> mktSegMaster.getCode().equals("CASINO_DEF")));
        assertTrue(mktSegMasters.stream().anyMatch(mktSegMaster -> mktSegMaster.getCode().equals("FAM1_QYL")));
        assertTrue(mktSegMasters.stream().anyMatch(mktSegMaster -> mktSegMaster.getCode().equals("FAM1_DEF")));
    }

    @Test
    public void assignMktSegToNewAMSRules_MktSegNonGroup_MktSegRenameRateCodeRename() {
        setUpServiceForAssigningMSToNewAMSRulesWithNoAttributeChange();

        addPMSMigrationMappingWith(CODE_TYPE_MS, "FAM", "FAM1", null);
        addPMSMigrationMappingWith(CODE_TYPE_RC, "RC1", "RC3", null);
        addPMSMigrationMappingWith(CODE_TYPE_RC, "RC2", "RC2", null);
        addAnalyticalMarketSegmentWith("FAM", "RC1", "FAM_QYL", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "EQUALS", "10");
        addAnalyticalMarketSegmentWith("FAM", "RC2", "FAM_QYL", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "EQUALS", "10");
        addAnalyticalMarketSegmentWith("FAM", null, "FAM_DEF", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "DEFAULT", "999");
        addPMSRevampNewAMSRule("FAM1", "RC3", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");
        addPMSRevampNewAMSRule("FAM1", "RC2", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");
        addPMSRevampNewAMSRule("FAM1", "DEFAULT", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");
        Integer FAM_QYL_MKT_SEG_ID = addMarketSegment("FAM_QYL");
        Integer FAM_DEF_MKT_SEG_ID = addMarketSegment("FAM_DEF");
        addMktSegMaster("FAM_QYL", 1);
        addMktSegMaster("FAM_DEF", 1);

        service.assignMktSegToNewAMSRules();

        tenantCrudService().flushAndClear();
        MktSeg FAM_QYL_MktSeg = tenantCrudService().find(MktSeg.class, FAM_QYL_MKT_SEG_ID);
        MktSeg FAM_DEF_MktSeg = tenantCrudService().find(MktSeg.class, FAM_DEF_MKT_SEG_ID);
        List<AnalyticalMarketSegment> analyticalMarketSegments = tenantCrudService().findAll(AnalyticalMarketSegment.class);
        List<MarketSegmentMaster> mktSegMasters = tenantCrudService().findAll(MarketSegmentMaster.class);
        assertEquals("FAM1_QYL", FAM_QYL_MktSeg.getCode());
        assertEquals("FAM1_DEF", FAM_DEF_MktSeg.getCode());
        assertTrue(analyticalMarketSegments.stream().anyMatch(ams -> ams.getMarketCode().equalsIgnoreCase("FAM1") && ams.getRateCode() != null && ams.getRateCode().equals("RC3") &&
                ams.getMappedMarketCode().equalsIgnoreCase("FAM1_QYL") && ams.getAttribute().equals(AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE) &&
                ams.getRateCodeType().equals(RateCodeTypeEnum.EQUALS)));
        assertTrue(analyticalMarketSegments.stream().anyMatch(ams -> ams.getMarketCode().equalsIgnoreCase("FAM1") && ams.getRateCode() != null && ams.getRateCode().equals("RC2") &&
                ams.getMappedMarketCode().equalsIgnoreCase("FAM1_QYL") && ams.getAttribute().equals(AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE) &&
                ams.getRateCodeType().equals(RateCodeTypeEnum.EQUALS)));
        assertTrue(analyticalMarketSegments.stream().anyMatch(ams -> ams.getMarketCode().equalsIgnoreCase("FAM") && ams.getRateCode() != null && ams.getRateCode().equals("RC1") &&
                ams.getMappedMarketCode().equalsIgnoreCase("FAM_QYL") && ams.getAttribute().equals(AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE) &&
                ams.getRateCodeType().equals(RateCodeTypeEnum.EQUALS)));
        assertTrue(analyticalMarketSegments.stream().anyMatch(ams -> ams.getMarketCode().equalsIgnoreCase("FAM") && ams.getRateCode() != null && ams.getRateCode().equals("RC2") &&
                ams.getMappedMarketCode().equalsIgnoreCase("FAM_QYL") && ams.getAttribute().equals(AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE) &&
                ams.getRateCodeType().equals(RateCodeTypeEnum.EQUALS)));
        assertTrue(mktSegMasters.stream().anyMatch(mktSegMaster -> mktSegMaster.getCode().equals("FAM1_QYL")));
        assertTrue(mktSegMasters.stream().anyMatch(mktSegMaster -> mktSegMaster.getCode().equals("FAM1_DEF")));
    }

    @Test
    public void assignMktSegToNewAMSRules_MktSegNonGroup_MktSegRenameRateCodeNoChangeAttributeNotCompletelyChanged() {
        setUpServiceForAssigningMSToNewAMSRulesWithNoAttributeChange();

        addPMSMigrationMappingWith(CODE_TYPE_MS, "FAM", "FAM1", null);
        addPMSMigrationMappingWith(CODE_TYPE_RC, "RC1", "RC1", null);
        addPMSMigrationMappingWith(CODE_TYPE_RC, "RC2", "RC2", null);
        addAnalyticalMarketSegmentWith("FAM", "RC1", "FAM_QYL", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "EQUALS", "10");
        addAnalyticalMarketSegmentWith("FAM", "RC2", "FAM_QYL", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "EQUALS", "10");
        addAnalyticalMarketSegmentWith("FAM", null, "FAM_DEF", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "DEFAULT", "999");
        addPMSRevampNewAMSRule("FAM1", "RC1", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");
        addPMSRevampNewAMSRule("FAM1", "RC2", EQUAL_TO_BAR, "1");
        addPMSRevampNewAMSRule("FAM1", "DEFAULT", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");
        Integer FAM_QYL_MKT_SEG_ID = addMarketSegment("FAM_QYL");
        Integer FAM_DEF_MKT_SEG_ID = addMarketSegment("FAM_DEF");
        addMktSegMaster("FAM_QYL", 1);
        addMktSegMaster("FAM_DEF", 1);

        service.assignMktSegToNewAMSRules();

        tenantCrudService().flushAndClear();
        MktSeg FAM_QYL_MktSeg = tenantCrudService().find(MktSeg.class, FAM_QYL_MKT_SEG_ID);
        MktSeg FAM_DEF_MktSeg = tenantCrudService().find(MktSeg.class, FAM_DEF_MKT_SEG_ID);
        List<AnalyticalMarketSegment> analyticalMarketSegments = tenantCrudService().findAll(AnalyticalMarketSegment.class);
        List<MarketSegmentMaster> mktSegMasters = tenantCrudService().findAll(MarketSegmentMaster.class);
        assertEquals("FAM_QYL", FAM_QYL_MktSeg.getCode());
        assertEquals("FAM1_DEF", FAM_DEF_MktSeg.getCode());
        assertTrue(analyticalMarketSegments.stream().anyMatch(ams -> ams.getMarketCode().equalsIgnoreCase("FAM1") && ams.getRateCode() != null && ams.getRateCode().equals("RC1") &&
                ams.getMappedMarketCode().equalsIgnoreCase("FAM1_QYL") && ams.getAttribute().equals(AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE) &&
                ams.getRateCodeType().equals(RateCodeTypeEnum.EQUALS)));
        assertTrue(analyticalMarketSegments.stream().anyMatch(ams -> ams.getMarketCode().equalsIgnoreCase("FAM1") && ams.getRateCode() != null && ams.getRateCode().equals("RC2") &&
                ams.getMappedMarketCode().equalsIgnoreCase("FAM1_USB") && ams.getAttribute().equals(AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR) &&
                ams.getRateCodeType().equals(RateCodeTypeEnum.EQUALS)));
        assertTrue(analyticalMarketSegments.stream().anyMatch(ams -> ams.getMarketCode().equalsIgnoreCase("FAM") && ams.getRateCode() != null && ams.getRateCode().equals("RC1") &&
                ams.getMappedMarketCode().equalsIgnoreCase("FAM_QYL") && ams.getAttribute().equals(AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE) &&
                ams.getRateCodeType().equals(RateCodeTypeEnum.EQUALS)));
        assertTrue(analyticalMarketSegments.stream().anyMatch(ams -> ams.getMarketCode().equalsIgnoreCase("FAM") && ams.getRateCode() != null && ams.getRateCode().equals("RC2") &&
                ams.getMappedMarketCode().equalsIgnoreCase("FAM_QYL") && ams.getAttribute().equals(AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE) &&
                ams.getRateCodeType().equals(RateCodeTypeEnum.EQUALS)));
        assertTrue(mktSegMasters.stream().anyMatch(mktSegMaster -> mktSegMaster.getCode().equals("FAM_QYL")));
        assertTrue(mktSegMasters.stream().anyMatch(mktSegMaster -> mktSegMaster.getCode().equals("FAM1_DEF")));
    }

    @Test
    public void assignMktSegToNewRules_RateCodeRenameWithNoAttributeChange() {
        setUpServiceForAssigningMSToNewAMSRulesWithNoAttributeChange();

        addAnalyticalMarketSegmentWith("FAM", "RC1", "FAM_QYL", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "EQUALS", "10");
        addAnalyticalMarketSegmentWith("FAM", "RC2", "FAM_USB", EQUAL_TO_BAR, "EQUALS", "10");
        addAnalyticalMarketSegmentWith("FAM", null, "FAM_DEF", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "DEFAULT", "999");
        addPMSMigrationMappingWith(CODE_TYPE_MS, "FAM", "FAM", null);
        addPMSMigrationMappingWith(CODE_TYPE_RC, "RC1", "RC1N", null);
        addPMSMigrationMappingWith(CODE_TYPE_RC, "RC2", "RC2", null);
        addPMSRevampNewAMSRule("FAM", "RC1N", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");
        addPMSRevampNewAMSRule("FAM", "DEFAULT", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");
        addMarketSegment("FAM_QYL");
        addMarketSegment("FAM_DEF");

        service.assignMktSegToNewAMSRules();

        tenantCrudService().flushAndClear();
        List<AnalyticalMarketSegment> amsList = tenantCrudService().findAll(AnalyticalMarketSegment.class);
        assertTrue(amsList.stream().anyMatch(ams -> ams.getRateCode() != null && ams.getRateCode().equalsIgnoreCase("RC1") && ams.getMarketCode().equals("FAM") && ams.isPreserved()));
        assertTrue(amsList.stream().anyMatch(ams -> ams.getRateCode() != null && ams.getRateCode().equalsIgnoreCase("RC1N") && ams.getMarketCode().equals("FAM") && !ams.isPreserved()));
        assertTrue(amsList.stream().anyMatch(ams -> ams.getRateCode() != null && ams.getRateCode().equalsIgnoreCase("RC2") && ams.getMarketCode().equals("FAM") && !ams.isPreserved()));
        assertTrue(amsList.stream().anyMatch(ams -> ams.getRateCode() != null && ams.getMarketCode().equals("FAM") && !ams.isPreserved()));
    }

    @Test
    public void assignMktSegToNewAMSRules_RateCodeRenameWithNoAttributeChange_SkipTierMktSegForMGM() {
        setUpServiceForAssigningMSToNewAMSRulesWithNoAttributeChange();

        addPMSMigrationMappingWith(CODE_TYPE_MS, "INCDNL", "INCDNL", null);
        addPMSMigrationMappingWith(CODE_TYPE_MS, "FAM", "FAM", null);
        addPMSMigrationMappingWith(CODE_TYPE_MS, "CSLI", "CPERP", null);
        addPMSMigrationMappingWith(CODE_TYPE_MS, "CSLP", "CPERP", null);
        addPMSMigrationMappingWith(CODE_TYPE_RC, "RC1", "RC1", null);
        addPMSMigrationMappingWith(CODE_TYPE_RC, "RC2", "RC2", null);
        addPMSMigrationMappingWith(CODE_TYPE_RC, "RC3", "RC3N", null);
        addPMSMigrationMappingWith(CODE_TYPE_RC, "RC4", "RC4N", null);
        addPMSMigrationMappingWith(CODE_TYPE_RC, "RC5", "RC5N", null);
        addAnalyticalMarketSegmentWith("CSLI", "CAS10", "CASINO_QYL", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "EQUALS", "10");
        addAnalyticalMarketSegmentWith("CSLI", "RC1", "CASINO_CASH_1_QYL", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "EQUALS", "10");
        addAnalyticalMarketSegmentWith("CSLP", "RC2", "CASINO_CASH_1_QYL", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "EQUALS", "10");
        addAnalyticalMarketSegmentWith("CSLI", null, "CASINO_DEF", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "DEFAULT", "999");
        addAnalyticalMarketSegmentWith("CSLP", null, "CASINO_DEF", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "DEFAULT", "999");
        addAnalyticalMarketSegmentWith("FAM", "RC3", "FAM_QYL", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "EQUALS", "10");
        addAnalyticalMarketSegmentWith("FAM", null, "FAM_DEF", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "DEFAULT", "999");
        addAnalyticalMarketSegmentWith("INCDNL", "RC4", "CASINO_CASH_2_QYL", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "EQUALS", "10");
        addAnalyticalMarketSegmentWith("INCDNL", "RC5", "CASINO_QYL", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "EQUALS", "10");
        addAnalyticalMarketSegmentWith("INCDNL", null, "CASINO_DEF", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "DEFAULT", "999");
        addPMSRevampNewAMSRule("FAM", "RC3N", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");
        addPMSRevampNewAMSRule("FAM", "DEFAULT", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");
        addPMSRevampNewAMSRule("CPERP", "CAS10", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");
        addPMSRevampNewAMSRule("CPERP", "DEFAULT", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");
        addPMSRevampNewAMSRule("INCDNL", "RC5N", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");
        addPMSRevampNewAMSRule("INCDNL", "DEFAULT", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");
        addMktSegMaster("CASINO_CASH_1_QYL", 1);
        addMktSegMaster("CASINO_CASH_2_QYL", 1);
        addMktSegMaster("CASINO_QYL", 1);
        addMktSegMaster("CASINO_DEF", 1);
        addMktSegMaster("FAM_QYL", 1);
        addMktSegMaster("FAM_DEF", 1);

        service.assignMktSegToNewAMSRules();

        tenantCrudService().flushAndClear();
        List<AnalyticalMarketSegment> amsList = tenantCrudService().findAll(AnalyticalMarketSegment.class);
        assertTrue(amsList.stream().anyMatch(ams -> ams.getRateCode() != null && ams.getRateCode().equalsIgnoreCase("RC3") && ams.getMarketCode().equals("FAM") && ams.isPreserved()));
        assertTrue(amsList.stream().anyMatch(ams -> ams.getRateCode() != null && ams.getRateCode().equalsIgnoreCase("RC3N") && ams.getMarketCode().equals("FAM") && !ams.isPreserved()));
        assertTrue(amsList.stream().anyMatch(ams -> ams.getRateCode() != null && ams.getRateCode().equalsIgnoreCase("RC5") && ams.getMarketCode().equals("INCDNL") && ams.isPreserved()));
        assertTrue(amsList.stream().anyMatch(ams -> ams.getRateCode() != null && ams.getRateCode().equalsIgnoreCase("RC5N") && ams.getMarketCode().equals("INCDNL") && !ams.isPreserved()));
        assertTrue(amsList.stream().anyMatch(ams -> ams.getRateCode() != null && ams.getMarketCode().equals("FAM") && !ams.isPreserved()));
    }

    @Test
    public void assignMktSegToNewRules_RateCodeRenameWithNoAttributeChange_WithSharedRateCodes() {
        setUpServiceForAssigningMSToNewAMSRulesWithNoAttributeChange();

        addAnalyticalMarketSegmentWith("FAM", "RC1", "FAM_QYL", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "EQUALS", "10");
        addAnalyticalMarketSegmentWith("FAM", "RC2", "FAM_USB", EQUAL_TO_BAR, "EQUALS", "10");
        addAnalyticalMarketSegmentWith("FAM", null, "FAM_DEF", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "DEFAULT", "999");
        addAnalyticalMarketSegmentWith("CORP", "RC1", "CORP_QYL", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "EQUALS", "10");
        addAnalyticalMarketSegmentWith("CORP", "RC3", "CORP_USB", EQUAL_TO_BAR, "EQUALS", "10");
        addAnalyticalMarketSegmentWith("CORP", null, "CORP_DEF", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "DEFAULT", "999");
        addPMSMigrationMappingWith(CODE_TYPE_MS, "FAM", "FAM", null);
        addPMSMigrationMappingWith(CODE_TYPE_MS, "CORP", "CORP", null);
        addPMSMigrationMappingWith(CODE_TYPE_RC, "RC1", "RC1N", null);
        addPMSMigrationMappingWith(CODE_TYPE_RC, "RC2", "RC2", null);
        addPMSMigrationMappingWith(CODE_TYPE_RC, "RC3", "RC3", null);
        addPMSRevampNewAMSRule("FAM", "RC1N", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");
        addPMSRevampNewAMSRule("FAM", "DEFAULT", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");
        addPMSRevampNewAMSRule("CORP", "RC1N", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");
        addPMSRevampNewAMSRule("CORP", "DEFAULT", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");

        service.assignMktSegToNewAMSRules();

        tenantCrudService().flushAndClear();
        List<AnalyticalMarketSegment> amsList = tenantCrudService().findAll(AnalyticalMarketSegment.class);
        assertTrue(amsList.stream().anyMatch(ams -> ams.getRateCode() != null && ams.getRateCode().equalsIgnoreCase("RC1") && ams.getMarketCode().equals("FAM") && ams.isPreserved()));
        assertTrue(amsList.stream().anyMatch(ams -> ams.getRateCode() != null && ams.getRateCode().equalsIgnoreCase("RC1N") && ams.getMarketCode().equals("FAM") && !ams.isPreserved()));
        assertTrue(amsList.stream().anyMatch(ams -> ams.getRateCode() != null && ams.getRateCode().equalsIgnoreCase("RC2") && ams.getMarketCode().equals("FAM") && !ams.isPreserved()));
        assertTrue(amsList.stream().anyMatch(ams -> ams.getRateCode() != null && ams.getMarketCode().equals("FAM") && !ams.isPreserved()));
        assertTrue(amsList.stream().anyMatch(ams -> ams.getRateCode() != null && ams.getRateCode().equalsIgnoreCase("RC1") && ams.getMarketCode().equals("CORP") && ams.isPreserved()));
        assertTrue(amsList.stream().anyMatch(ams -> ams.getRateCode() != null && ams.getRateCode().equalsIgnoreCase("RC1N") && ams.getMarketCode().equals("CORP") && !ams.isPreserved()));
        assertTrue(amsList.stream().anyMatch(ams -> ams.getRateCode() != null && ams.getRateCode().equalsIgnoreCase("RC3") && ams.getMarketCode().equals("CORP") && !ams.isPreserved()));
        assertTrue(amsList.stream().anyMatch(ams -> ams.getRateCode() != null && ams.getMarketCode().equals("CORP") && !ams.isPreserved()));
    }

    @Test
    public void assignMktSegToNewRules_RateCodeRename_WithSharedRateCodesAndOneRateCodeAttributeChanged() {
        setUpServiceForAssigningMSToNewAMSRulesWithNoAttributeChange();

        addAnalyticalMarketSegmentWith("FAM", "RC1", "FAM_QYL", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "EQUALS", "10");
        addAnalyticalMarketSegmentWith("FAM", "RC2", "FAM_USB", EQUAL_TO_BAR, "EQUALS", "10");
        addAnalyticalMarketSegmentWith("FAM", null, "FAM_DEF", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "DEFAULT", "999");
        addAnalyticalMarketSegmentWith("CORP", "RC1", "CORP_QYL", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "EQUALS", "10");
        addAnalyticalMarketSegmentWith("CORP", "RC3", "CORP_USB", EQUAL_TO_BAR, "EQUALS", "10");
        addAnalyticalMarketSegmentWith("CORP", null, "CORP_DEF", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "DEFAULT", "999");
        addPMSMigrationMappingWith(CODE_TYPE_MS, "FAM", "FAM", null);
        addPMSMigrationMappingWith(CODE_TYPE_MS, "CORP", "CORP", null);
        addPMSMigrationMappingWith(CODE_TYPE_RC, "RC1", "RC1N", null);
        addPMSMigrationMappingWith(CODE_TYPE_RC, "RC2", "RC2", null);
        addPMSMigrationMappingWith(CODE_TYPE_RC, "RC3", "RC3", null);
        addPMSRevampNewAMSRule("FAM", "RC1N", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");
        addPMSRevampNewAMSRule("FAM", "DEFAULT", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");
        addPMSRevampNewAMSRule("CORP", "RC1N", EQUAL_TO_BAR, "1");
        addPMSRevampNewAMSRule("CORP", "DEFAULT", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");

        service.assignMktSegToNewAMSRules();

        tenantCrudService().flushAndClear();
        List<AnalyticalMarketSegment> amsList = tenantCrudService().findAll(AnalyticalMarketSegment.class);
        assertTrue(amsList.stream().anyMatch(ams -> ams.getRateCode() != null && ams.getRateCode().equalsIgnoreCase("RC1") && ams.getMarketCode().equals("FAM") && ams.isPreserved()));
        assertTrue(amsList.stream().anyMatch(ams -> ams.getRateCode() != null && ams.getRateCode().equalsIgnoreCase("RC1N") && ams.getMarketCode().equals("FAM") && !ams.isPreserved()));
        assertTrue(amsList.stream().anyMatch(ams -> ams.getRateCode() != null && ams.getRateCode().equalsIgnoreCase("RC2") && ams.getMarketCode().equals("FAM") && !ams.isPreserved()));
        assertTrue(amsList.stream().anyMatch(ams -> ams.getRateCode() != null && ams.getMarketCode().equals("FAM") && !ams.isPreserved()));
        assertTrue(amsList.stream().anyMatch(ams -> ams.getRateCode() != null && ams.getRateCode().equalsIgnoreCase("RC1") && ams.getMarketCode().equals("CORP") && !ams.isPreserved()));
        assertTrue(amsList.stream().anyMatch(ams -> ams.getRateCode() != null && ams.getRateCode().equalsIgnoreCase("RC1N") && ams.getMarketCode().equals("CORP") && !ams.isPreserved()));
        assertTrue(amsList.stream().anyMatch(ams -> ams.getRateCode() != null && ams.getRateCode().equalsIgnoreCase("RC3") && ams.getMarketCode().equals("CORP") && !ams.isPreserved()));
        assertTrue(amsList.stream().anyMatch(ams -> ams.getRateCode() != null && ams.getMarketCode().equals("CORP") && !ams.isPreserved()));
    }

    @Test
    public void assignMktSegToNewRules_RateCodeRename_WithSharedRateCodesAndOneRateCodeMktCodeChanged() {
        setUpServiceForAssigningMSToNewAMSRulesWithNoAttributeChange();

        addAnalyticalMarketSegmentWith("FAM", "RC1", "FAM_QYL", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "EQUALS", "10");
        addAnalyticalMarketSegmentWith("FAM", "RC2", "FAM_USB", EQUAL_TO_BAR, "EQUALS", "10");
        addAnalyticalMarketSegmentWith("FAM", null, "FAM_DEF", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "DEFAULT", "999");
        addAnalyticalMarketSegmentWith("CORP", "RC1", "CORP_QYL", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "EQUALS", "10");
        addAnalyticalMarketSegmentWith("CORP", "RC3", "CORP_USB", EQUAL_TO_BAR, "EQUALS", "10");
        addAnalyticalMarketSegmentWith("CORP", null, "CORP_DEF", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "DEFAULT", "999");
        addPMSMigrationMappingWith(CODE_TYPE_MS, "FAM", "FAM", null);
        addPMSMigrationMappingWith(CODE_TYPE_MS, "CORP", "CORP1", null);
        addPMSMigrationMappingWith(CODE_TYPE_RC, "RC1", "RC1N", null);
        addPMSMigrationMappingWith(CODE_TYPE_RC, "RC2", "RC2", null);
        addPMSMigrationMappingWith(CODE_TYPE_RC, "RC3", "RC3", null);
        addPMSRevampNewAMSRule("FAM", "RC1N", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");
        addPMSRevampNewAMSRule("FAM", "DEFAULT", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");
        addPMSRevampNewAMSRule("CORP1", "RC1N", EQUAL_TO_BAR, "1");
        addPMSRevampNewAMSRule("CORP1", "DEFAULT", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");

        service.assignMktSegToNewAMSRules();

        tenantCrudService().flushAndClear();
        List<AnalyticalMarketSegment> amsList = tenantCrudService().findAll(AnalyticalMarketSegment.class);
        assertTrue(amsList.stream().anyMatch(ams -> ams.getRateCode() != null && ams.getRateCode().equalsIgnoreCase("RC1") && ams.getMarketCode().equals("FAM") && ams.isPreserved()));
        assertTrue(amsList.stream().anyMatch(ams -> ams.getRateCode() != null && ams.getRateCode().equalsIgnoreCase("RC1N") && ams.getMarketCode().equals("FAM") && !ams.isPreserved()));
        assertTrue(amsList.stream().anyMatch(ams -> ams.getRateCode() != null && ams.getRateCode().equalsIgnoreCase("RC2") && ams.getMarketCode().equals("FAM") && !ams.isPreserved()));
        assertTrue(amsList.stream().anyMatch(ams -> ams.getRateCode() != null && ams.getMarketCode().equals("FAM") && !ams.isPreserved()));
        assertTrue(amsList.stream().anyMatch(ams -> ams.getRateCode() != null && ams.getRateCode().equalsIgnoreCase("RC1") && ams.getMarketCode().equals("CORP") && !ams.isPreserved()));
        assertTrue(amsList.stream().anyMatch(ams -> ams.getRateCode() != null && ams.getRateCode().equalsIgnoreCase("RC1N") && ams.getMarketCode().equals("CORP1") && !ams.isPreserved()));
        assertTrue(amsList.stream().anyMatch(ams -> ams.getRateCode() != null && ams.getRateCode().equalsIgnoreCase("RC3") && ams.getMarketCode().equals("CORP") && !ams.isPreserved()));
        assertTrue(amsList.stream().anyMatch(ams -> ams.getRateCode() != null && ams.getMarketCode().equals("CORP") && !ams.isPreserved()));
    }

    @Test
    public void assignMktSegToNewAMSRules_MktSegNonGroup_MktSegRenameRateCodeNoChange_AttributeCompletelyChangedButOtherRateCodeAdded() {
        setUpServiceForAssigningMSToNewAMSRulesWithNoAttributeChange();

        addPMSMigrationMappingWith(CODE_TYPE_MS, "FAM", "FAM1", null);
        addPMSMigrationMappingWith(CODE_TYPE_RC, "RC1", "RC1", null);
        addPMSMigrationMappingWith(CODE_TYPE_RC, "RC2", "RC2", null);
        addAnalyticalMarketSegmentWith("FAM", "RC1", "FAM_QYL", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "EQUALS", "10");
        addAnalyticalMarketSegmentWith("FAM", "RC2", "FAM_USB", EQUAL_TO_BAR, "EQUALS", "10");
        addAnalyticalMarketSegmentWith("FAM", null, "FAM_DEF", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "DEFAULT", "999");
        addPMSRevampNewAMSRule("FAM1", "RC1", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");
        addPMSRevampNewAMSRule("FAM1", "RC2", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");
        addPMSRevampNewAMSRule("FAM1", "DEFAULT", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");
        Integer FAM_QYL_MKT_SEG_ID = addMarketSegment("FAM_QYL");
        Integer FAM_DEF_MKT_SEG_ID = addMarketSegment("FAM_DEF");
        addMktSegMaster("FAM_QYL", 1);
        addMktSegMaster("FAM_DEF", 1);

        service.assignMktSegToNewAMSRules();

        tenantCrudService().flushAndClear();
        MktSeg FAM_QYL_MktSeg = tenantCrudService().find(MktSeg.class, FAM_QYL_MKT_SEG_ID);
        MktSeg FAM_DEF_MktSeg = tenantCrudService().find(MktSeg.class, FAM_DEF_MKT_SEG_ID);
        List<AnalyticalMarketSegment> analyticalMarketSegments = tenantCrudService().findAll(AnalyticalMarketSegment.class);
        List<MarketSegmentMaster> mktSegMasters = tenantCrudService().findAll(MarketSegmentMaster.class);
        assertEquals("FAM_QYL", FAM_QYL_MktSeg.getCode());
        assertEquals("FAM1_DEF", FAM_DEF_MktSeg.getCode());
        assertTrue(analyticalMarketSegments.stream().anyMatch(ams -> ams.getMarketCode().equalsIgnoreCase("FAM1") && ams.getRateCode() != null && ams.getRateCode().equals("RC1") &&
                ams.getMappedMarketCode().equalsIgnoreCase("FAM1_QYL") && ams.getAttribute().equals(AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE) &&
                ams.getRateCodeType().equals(RateCodeTypeEnum.EQUALS)));
        assertTrue(analyticalMarketSegments.stream().anyMatch(ams -> ams.getMarketCode().equalsIgnoreCase("FAM1") && ams.getRateCode() != null && ams.getRateCode().equals("RC2") &&
                ams.getMappedMarketCode().equalsIgnoreCase("FAM1_QYL") && ams.getAttribute().equals(AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE) &&
                ams.getRateCodeType().equals(RateCodeTypeEnum.EQUALS)));
        assertTrue(analyticalMarketSegments.stream().anyMatch(ams -> ams.getMarketCode().equalsIgnoreCase("FAM") && ams.getRateCode() != null && ams.getRateCode().equals("RC1") &&
                ams.getMappedMarketCode().equalsIgnoreCase("FAM_QYL") && ams.getAttribute().equals(AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE) &&
                ams.getRateCodeType().equals(RateCodeTypeEnum.EQUALS)));
        assertTrue(analyticalMarketSegments.stream().anyMatch(ams -> ams.getMarketCode().equalsIgnoreCase("FAM") && ams.getRateCode() != null && ams.getRateCode().equals("RC2") &&
                ams.getMappedMarketCode().equalsIgnoreCase("FAM_USB") && ams.getAttribute().equals(AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR) &&
                ams.getRateCodeType().equals(RateCodeTypeEnum.EQUALS)));
        assertTrue(mktSegMasters.stream().anyMatch(mktSegMaster -> mktSegMaster.getCode().equals("FAM_QYL")));
        assertTrue(mktSegMasters.stream().anyMatch(mktSegMaster -> mktSegMaster.getCode().equals("FAM1_DEF")));
    }

    @Test
    public void assignMktSegToNewAMSRules_MktSegNonGroup_MktSegRenamedRateCodeMerged() {
        setUpServiceForAssigningMSToNewAMSRulesWithNoAttributeChange();

        addPMSMigrationMappingWith(CODE_TYPE_MS, "FAM", "FAM1", null);
        addPMSMigrationMappingWith(CODE_TYPE_RC, "RC1", "RC3", null);
        addPMSMigrationMappingWith(CODE_TYPE_RC, "RC2", "RC3", null);
        addAnalyticalMarketSegmentWith("FAM", "RC1", "FAM_QYL", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "EQUALS", "10");
        addAnalyticalMarketSegmentWith("FAM", "RC2", "FAM_QYL", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "EQUALS", "10");
        addAnalyticalMarketSegmentWith("FAM", null, "FAM_DEF", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "DEFAULT", "999");
        addPMSRevampNewAMSRule("FAM1", "RC3", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");
        addPMSRevampNewAMSRule("FAM1", "DEFAULT", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");
        Integer FAM_QYL_MKT_SEG_ID = addMarketSegment("FAM_QYL");
        Integer FAM_DEF_MKT_SEG_ID = addMarketSegment("FAM_DEF");
        addMktSegMaster("FAM_QYL", 1);
        addMktSegMaster("FAM_DEF", 1);

        service.assignMktSegToNewAMSRules();

        tenantCrudService().flushAndClear();
        MktSeg FAM_QYL_MktSeg = tenantCrudService().find(MktSeg.class, FAM_QYL_MKT_SEG_ID);
        MktSeg FAM_DEF_MktSeg = tenantCrudService().find(MktSeg.class, FAM_DEF_MKT_SEG_ID);
        List<AnalyticalMarketSegment> analyticalMarketSegments = tenantCrudService().findAll(AnalyticalMarketSegment.class);
        List<MarketSegmentMaster> mktSegMasters = tenantCrudService().findAll(MarketSegmentMaster.class);
        assertEquals("FAM1_QYL", FAM_QYL_MktSeg.getCode());
        assertEquals("FAM1_DEF", FAM_DEF_MktSeg.getCode());
        assertTrue(analyticalMarketSegments.stream().anyMatch(ams -> ams.getMarketCode().equalsIgnoreCase("FAM") && ams.getRateCode() != null && ams.getRateCode().equals("RC1") &&
                ams.getMappedMarketCode().equalsIgnoreCase("FAM_QYL") && ams.getAttribute().equals(AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE) &&
                ams.getRateCodeType().equals(RateCodeTypeEnum.EQUALS)));
        assertTrue(analyticalMarketSegments.stream().anyMatch(ams -> ams.getMarketCode().equalsIgnoreCase("FAM") && ams.getRateCode() != null && ams.getRateCode().equals("RC2") &&
                ams.getMappedMarketCode().equalsIgnoreCase("FAM_QYL") && ams.getAttribute().equals(AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE) &&
                ams.getRateCodeType().equals(RateCodeTypeEnum.EQUALS)));
        assertTrue(analyticalMarketSegments.stream().anyMatch(ams -> ams.getMarketCode().equalsIgnoreCase("FAM1") && ams.getRateCode() != null && ams.getRateCode().equals("RC3") &&
                ams.getMappedMarketCode().equalsIgnoreCase("FAM1_QYL") && ams.getAttribute().equals(AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE) &&
                ams.getRateCodeType().equals(RateCodeTypeEnum.EQUALS)));
        assertTrue(mktSegMasters.stream().anyMatch(mktSegMaster -> mktSegMaster.getCode().equals("FAM1_QYL")));
        assertTrue(mktSegMasters.stream().anyMatch(mktSegMaster -> mktSegMaster.getCode().equals("FAM1_DEF")));
    }

    @Test
    public void assignMktSegToNewAMSRules_MktSegNonGroup_MktSegNoChangeRateCodeRename() {
        setUpServiceForAssigningMSToNewAMSRulesWithNoAttributeChange();

        addPMSMigrationMappingWith(CODE_TYPE_MS, "FAM", "FAM", null);
        addPMSMigrationMappingWith(CODE_TYPE_RC, "RC1", "RC3", null);
        addPMSMigrationMappingWith(CODE_TYPE_RC, "RC2", "RC2", null);
        addAnalyticalMarketSegmentWith("FAM", "RC1", "FAM_QYL", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "EQUALS", "10");
        addAnalyticalMarketSegmentWith("FAM", "RC2", "FAM_QYL", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "EQUALS", "10");
        addAnalyticalMarketSegmentWith("FAM", null, "FAM_DEF", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "DEFAULT", "999");
        addPMSRevampNewAMSRule("FAM", "RC3", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");
        addPMSRevampNewAMSRule("FAM", "RC2", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");
        addPMSRevampNewAMSRule("FAM", "DEFAULT", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");
        Integer FAM_QYL_MKT_SEG_ID = addMarketSegment("FAM_QYL");
        Integer FAM_DEF_MKT_SEG_ID = addMarketSegment("FAM_DEF");
        addMktSegMaster("FAM_QYL", 1);
        addMktSegMaster("FAM_DEF", 1);

        service.assignMktSegToNewAMSRules();

        tenantCrudService().flushAndClear();
        MktSeg FAM_QYL_MktSeg = tenantCrudService().find(MktSeg.class, FAM_QYL_MKT_SEG_ID);
        MktSeg FAM_DEF_MktSeg = tenantCrudService().find(MktSeg.class, FAM_DEF_MKT_SEG_ID);
        List<AnalyticalMarketSegment> analyticalMarketSegments = tenantCrudService().findAll(AnalyticalMarketSegment.class);
        List<MarketSegmentMaster> mktSegMasters = tenantCrudService().findAll(MarketSegmentMaster.class);
        assertEquals("FAM_QYL", FAM_QYL_MktSeg.getCode());
        assertEquals("FAM_DEF", FAM_DEF_MktSeg.getCode());
        assertTrue(analyticalMarketSegments.stream().anyMatch(ams -> ams.getMarketCode().equalsIgnoreCase("FAM") && ams.getRateCode().equals("RC1") &&
                ams.getMappedMarketCode().equalsIgnoreCase("FAM_QYL") && ams.getAttribute().equals(AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE) &&
                ams.getRateCodeType().equals(RateCodeTypeEnum.EQUALS)));
        assertTrue(analyticalMarketSegments.stream().anyMatch(ams -> ams.getMarketCode().equalsIgnoreCase("FAM") && ams.getRateCode().equals("RC2") &&
                ams.getMappedMarketCode().equalsIgnoreCase("FAM_QYL") && ams.getAttribute().equals(AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE) &&
                ams.getRateCodeType().equals(RateCodeTypeEnum.EQUALS)));
        assertTrue(analyticalMarketSegments.stream().anyMatch(ams -> ams.getMarketCode().equalsIgnoreCase("FAM") && ams.getRateCode() != null && ams.getRateCode().equals("RC3") &&
                ams.getMappedMarketCode().equalsIgnoreCase("FAM_QYL") && ams.getAttribute().equals(AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE) &&
                ams.getRateCodeType().equals(RateCodeTypeEnum.EQUALS)));
        assertTrue(mktSegMasters.stream().anyMatch(mktSegMaster -> mktSegMaster.getCode().equals("FAM_QYL")));
        assertTrue(mktSegMasters.stream().anyMatch(mktSegMaster -> mktSegMaster.getCode().equals("FAM_DEF")));
    }

    @Test
    public void assignMktSegToNewAMSRules_MktSegGroupWithNonStraightAMSExisting_MktSegNoChangeRateCodeRename() {
        setUpServiceForAssigningMSToNewAMSRulesWithNoAttributeChange();

        addPMSMigrationMappingWith(CODE_TYPE_MS_GROUP, "FAM", "FAM1", null);
        addPMSMigrationMappingWith(CODE_TYPE_RC, "RC1", "RC3", null);
        addPMSMigrationMappingWith(CODE_TYPE_RC, "RC2", "RC2", null);
        addAnalyticalMarketSegmentWith("FAM", "RC1", "FAM_QYL", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "EQUALS", "10");
        addAnalyticalMarketSegmentWith("FAM", "RC2", "FAM_QYL", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "EQUALS", "10");
        addAnalyticalMarketSegmentWith("FAM", null, "FAM_DEF", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "DEFAULT", "999");
        addPMSRevampNewAMSRule("FAM1", null, QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");
        Integer FAM_QYL_MKT_SEG_ID = addMarketSegment("FAM_QYL");
        Integer FAM_DEF_MKT_SEG_ID = addMarketSegment("FAM_DEF");

        service.assignMktSegToNewAMSRules();

        tenantCrudService().flushAndClear();
        MktSeg FAM_QYL_MktSeg = tenantCrudService().find(MktSeg.class, FAM_QYL_MKT_SEG_ID);
        MktSeg FAM_DEF_MktSeg = tenantCrudService().find(MktSeg.class, FAM_DEF_MKT_SEG_ID);
        List<AnalyticalMarketSegment> analyticalMarketSegments = tenantCrudService().findAll(AnalyticalMarketSegment.class);
        assertEquals("FAM_QYL", FAM_QYL_MktSeg.getCode());
        assertEquals("FAM_DEF", FAM_DEF_MktSeg.getCode());
        assertTrue(analyticalMarketSegments.stream().anyMatch(ams -> ams.getMarketCode().equalsIgnoreCase("FAM") && ams.getRateCode().equals("RC1") &&
                ams.getMappedMarketCode().equalsIgnoreCase("FAM_QYL") && ams.getAttribute().equals(AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE) &&
                ams.getRateCodeType().equals(RateCodeTypeEnum.EQUALS)));
        assertTrue(analyticalMarketSegments.stream().anyMatch(ams -> ams.getMarketCode().equalsIgnoreCase("FAM") && ams.getRateCode().equals("RC2") &&
                ams.getMappedMarketCode().equalsIgnoreCase("FAM_QYL") && ams.getAttribute().equals(AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE) &&
                ams.getRateCodeType().equals(RateCodeTypeEnum.EQUALS)));
        assertTrue(analyticalMarketSegments.stream().anyMatch(ams -> ams.getMarketCode().equalsIgnoreCase("FAM1") && ams.getRateCode() == null &&
                ams.getMappedMarketCode().equalsIgnoreCase("FAM1") && ams.getAttribute().equals(AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE) &&
                ams.getRateCodeType().equals(RateCodeTypeEnum.ALL)));
    }

    @Test
    public void assignMktSegToNewAMSRules_MktSegNonGroupNonStraightToStraightRenameWithNoAttributeChange() {
        setUpServiceForAssigningMSToNewAMSRulesWithNoAttributeChange();

        addPMSMigrationMappingWith(CODE_TYPE_MS, "FAM", "FAM1", null);
        addPMSMigrationMappingWith(CODE_TYPE_RC, "RC1", "RC3", null);
        addPMSMigrationMappingWith(CODE_TYPE_RC, "RC2", "RC2", null);
        addAnalyticalMarketSegmentWith("FAM", "RC1", "FAM_QYL", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "EQUALS", "10");
        addAnalyticalMarketSegmentWith("FAM", "RC2", "FAM_QYL", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "EQUALS", "10");
        addAnalyticalMarketSegmentWith("FAM", null, "FAM_DEF", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "DEFAULT", "999");
        addPMSRevampNewAMSRule("FAM1", null, QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");
        Integer FAM__MKT_SEG_ID = addMarketSegment("FAM_QYL");
        Integer FAM_DEF_MKT_SEG_ID = addMarketSegment("FAM_DEF");

        service.assignMktSegToNewAMSRules();

        tenantCrudService().flushAndClear();
        MktSeg FAM_QYL_MktSeg = tenantCrudService().find(MktSeg.class, FAM__MKT_SEG_ID);
        MktSeg FAM_DEF_MktSeg = tenantCrudService().find(MktSeg.class, FAM_DEF_MKT_SEG_ID);
        List<AnalyticalMarketSegment> analyticalMarketSegments = tenantCrudService().findAll(AnalyticalMarketSegment.class);
        assertEquals("FAM_QYL", FAM_QYL_MktSeg.getCode());
        assertEquals("FAM_DEF", FAM_DEF_MktSeg.getCode());
        assertTrue(analyticalMarketSegments.stream().anyMatch(ams -> ams.getMarketCode().equalsIgnoreCase("FAM") && ams.getRateCode() == null &&
                ams.getMappedMarketCode().equalsIgnoreCase("FAM") && ams.getAttribute().equals(AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE) &&
                ams.getRateCodeType().equals(RateCodeTypeEnum.ALL)));
        assertTrue(analyticalMarketSegments.stream().anyMatch(ams -> ams.getMarketCode().equalsIgnoreCase("FAM1") && ams.getRateCode() == null &&
                ams.getMappedMarketCode().equalsIgnoreCase("FAM1") && ams.getAttribute().equals(AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE) &&
                ams.getRateCodeType().equals(RateCodeTypeEnum.ALL)));
    }

    @Test
    public void assignMktSegToNewAMSRules_MktSegNonGroupStraightToStraightRenameWithNoAttributeChange() {
        setUpServiceForAssigningMSToNewAMSRulesWithNoAttributeChange();

        addPMSMigrationMappingWith(CODE_TYPE_MS, "FAM", "FAM1", null);
        addPMSMigrationMappingWith(CODE_TYPE_RC, "RC1", "RC3", null);
        addPMSMigrationMappingWith(CODE_TYPE_RC, "RC2", "RC2", null);
        addAnalyticalMarketSegmentWith("FAM", null, "FAM", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "ALL", "1");
        addPMSRevampNewAMSRule("FAM1", null, QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");
        Integer FAM__MKT_SEG_ID = addMarketSegment("FAM");

        service.assignMktSegToNewAMSRules();

        tenantCrudService().flushAndClear();
        MktSeg FAM_QYL_MktSeg = tenantCrudService().find(MktSeg.class, FAM__MKT_SEG_ID);
        List<AnalyticalMarketSegment> analyticalMarketSegments = tenantCrudService().findAll(AnalyticalMarketSegment.class);
        assertEquals("FAM1", FAM_QYL_MktSeg.getCode());
        assertTrue(analyticalMarketSegments.stream().anyMatch(ams -> ams.getMarketCode().equalsIgnoreCase("FAM") && ams.getRateCode() == null &&
                ams.getMappedMarketCode().equalsIgnoreCase("FAM") && ams.getAttribute().equals(AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE) &&
                ams.getRateCodeType().equals(RateCodeTypeEnum.ALL)));
        assertTrue(analyticalMarketSegments.stream().anyMatch(ams -> ams.getMarketCode().equalsIgnoreCase("FAM1") && ams.getRateCode() == null &&
                ams.getMappedMarketCode().equalsIgnoreCase("FAM1") && ams.getAttribute().equals(AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE) &&
                ams.getRateCodeType().equals(RateCodeTypeEnum.ALL)));
    }

    @Test
    public void assignMktSegToNewAMSRules_MktSegNonGroupStraightToNonStraightRenameWithNoAttributeChange() {
        setUpServiceForAssigningMSToNewAMSRulesWithNoAttributeChange();

        addPMSMigrationMappingWith(CODE_TYPE_MS, "FAM", "FAM1", null);
        addPMSMigrationMappingWith(CODE_TYPE_RC, "RC1", "RC3", null);
        addPMSMigrationMappingWith(CODE_TYPE_RC, "RC2", "RC2", null);
        addAnalyticalMarketSegmentWith("FAM", null, "FAM", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "ALL", "1");
        addPMSRevampNewAMSRule("FAM1", "RC1", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");
        addPMSRevampNewAMSRule("FAM1", "RC2", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");
        addPMSRevampNewAMSRule("FAM1", "DEFAULT", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");
        Integer FAM__MKT_SEG_ID = addMarketSegment("FAM");

        service.assignMktSegToNewAMSRules();

        tenantCrudService().flushAndClear();
        MktSeg FAM_QYL_MktSeg = tenantCrudService().find(MktSeg.class, FAM__MKT_SEG_ID);
        List<AnalyticalMarketSegment> analyticalMarketSegments = tenantCrudService().findAll(AnalyticalMarketSegment.class);
        assertEquals("FAM", FAM_QYL_MktSeg.getCode());
        assertTrue(analyticalMarketSegments.stream().noneMatch(ams -> ams.getMarketCode().equalsIgnoreCase("FAM") && ams.getRateCode() == null &&
                ams.getMappedMarketCode().equalsIgnoreCase("FAM") && ams.getAttribute().equals(AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE) &&
                ams.getRateCodeType().equals(RateCodeTypeEnum.ALL)));
        assertTrue(analyticalMarketSegments.stream().noneMatch(ams -> ams.getMarketCode().equalsIgnoreCase("FAM") && ams.getRateCode() != null && ams.getRateCode().equals("RC1") &&
                ams.getMappedMarketCode().equalsIgnoreCase("FAM") && ams.getAttribute().equals(AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE) &&
                ams.getRateCodeType().equals(RateCodeTypeEnum.EQUALS)));
        assertTrue(analyticalMarketSegments.stream().anyMatch(ams -> ams.getMarketCode().equalsIgnoreCase("FAM1") && ams.getRateCode() != null && ams.getRateCode().equals("RC1") &&
                ams.getMappedMarketCode().equalsIgnoreCase("FAM1_QYL") && ams.getAttribute().equals(AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE) &&
                ams.getRateCodeType().equals(RateCodeTypeEnum.EQUALS)));
        assertTrue(analyticalMarketSegments.stream().anyMatch(ams -> ams.getMarketCode().equalsIgnoreCase("FAM1") && ams.getRateCode() != null && ams.getRateCode().equals("RC2") &&
                ams.getMappedMarketCode().equalsIgnoreCase("FAM1_QYL") && ams.getAttribute().equals(AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE) &&
                ams.getRateCodeType().equals(RateCodeTypeEnum.EQUALS)));
        assertTrue(analyticalMarketSegments.stream().anyMatch(ams -> ams.getMarketCode().equalsIgnoreCase("FAM1") && ams.getRateCode() == null &&
                ams.getMappedMarketCode().equalsIgnoreCase("FAM1_DEF") && ams.getAttribute().equals(AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE) &&
                ams.getRateCodeType().equals(RateCodeTypeEnum.DEFAULT)));
    }

    @Test
    public void assignMktSegToNewAMSRules_MktSegNonGroup_MktSegRenamedRateCodeNoChange_IgnoredRateCodesExist() {
        setUpServiceForAssigningMSToNewAMSRulesWithNoAttributeChange();

        PMSRevampAMSDataService pmsRevampAMSDataService = new PMSRevampAMSDataService();
        inject(pmsRevampAMSDataService, "tenantCrudService", tenantCrudService());
        List<IgnoredRateCode> ignoredRateCodes = new ArrayList<>();
        addPMSMigrationMappingWith(CODE_TYPE_MS, "FAM", "FAM1", null);
        addPMSMigrationMappingWith(CODE_TYPE_RC, "RC1", "RC1", null);
        addPMSMigrationMappingWith(CODE_TYPE_RC, "RC2", "RC2", null);
        addAnalyticalMarketSegmentWith("FAM", "RC1", "FAM_QYL", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "EQUALS", "10");
        addAnalyticalMarketSegmentWith("FAM", "RC2", "FAM_QYL", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "EQUALS", "10");
        addAnalyticalMarketSegmentWith("FAM", null, "FAM_DEF", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "DEFAULT", "999");
        addPMSRevampNewAMSRule("FAM1", "RC1", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");
        addPMSRevampNewAMSRule("FAM1", "DEFAULT", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");
        ignoredRateCodes.add(getIgnoredRateCode("FAM1", "RC2"));
        Integer FAM_QYL_MKT_SEG_ID = addMarketSegment("FAM_QYL");
        Integer FAM_DEF_MKT_SEG_ID = addMarketSegment("FAM_DEF");
        pmsRevampAMSDataService.ignoreMismatchedRateCodes(ignoredRateCodes);

        service.assignMktSegToNewAMSRules();

        tenantCrudService().flushAndClear();
        MktSeg FAM_QYL_MktSeg = tenantCrudService().find(MktSeg.class, FAM_QYL_MKT_SEG_ID);
        MktSeg FAM_DEF_MktSeg = tenantCrudService().find(MktSeg.class, FAM_DEF_MKT_SEG_ID);
        List<AnalyticalMarketSegment> analyticalMarketSegments = tenantCrudService().findAll(AnalyticalMarketSegment.class);
        assertEquals("FAM_QYL", FAM_QYL_MktSeg.getCode());
        assertEquals("FAM_DEF", FAM_DEF_MktSeg.getCode());
        assertTrue(analyticalMarketSegments.stream().anyMatch(ams -> ams.getMarketCode().equalsIgnoreCase("FAM") && ams.getRateCode() != null && ams.getRateCode().equals("RC1") &&
                ams.getMappedMarketCode().equalsIgnoreCase("FAM_QYL") && ams.getAttribute().equals(AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE) &&
                ams.getRateCodeType().equals(RateCodeTypeEnum.EQUALS)));
        assertTrue(analyticalMarketSegments.stream().anyMatch(ams -> ams.getMarketCode().equalsIgnoreCase("FAM") && ams.getRateCode() != null && ams.getRateCode().equals("RC2") &&
                ams.getMappedMarketCode().equalsIgnoreCase("FAM_QYL") && ams.getAttribute().equals(AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE) &&
                ams.getRateCodeType().equals(RateCodeTypeEnum.EQUALS)));
        assertTrue(analyticalMarketSegments.stream().anyMatch(ams -> ams.getMarketCode().equalsIgnoreCase("FAM1") && ams.getRateCode() != null && ams.getRateCode().equals("RC1") &&
                ams.getMappedMarketCode().equalsIgnoreCase("FAM1_QYL") && ams.getAttribute().equals(AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE) &&
                ams.getRateCodeType().equals(RateCodeTypeEnum.EQUALS)));
    }

    @Test
    public void assignMktSegToNewAMSRules_MktSegNonGroup_MktSegRenamedRateCodeNoChange_IgnoredRateCodesExistWithDefault() {
        setUpServiceForAssigningMSToNewAMSRulesWithNoAttributeChange();

        PMSRevampAMSDataService pmsRevampAMSDataService = new PMSRevampAMSDataService();
        inject(pmsRevampAMSDataService, "tenantCrudService", tenantCrudService());
        List<IgnoredRateCode> ignoredRateCodes = new ArrayList<>();
        addPMSMigrationMappingWith(CODE_TYPE_MS, "FAM", "FAM1", null);
        addPMSMigrationMappingWith(CODE_TYPE_RC, "RC1", "RC1", null);
        addPMSMigrationMappingWith(CODE_TYPE_RC, "RC2", "RC2", null);
        addAnalyticalMarketSegmentWith("FAM", "RC1", "FAM_QYL", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "EQUALS", "10");
        addAnalyticalMarketSegmentWith("FAM", "RC2", "FAM_DEF", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "EQUALS", "10");
        addAnalyticalMarketSegmentWith("FAM", null, "FAM_DEF", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "DEFAULT", "999");
        addPMSRevampNewAMSRule("FAM1", "RC1", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");
        addPMSRevampNewAMSRule("FAM1", "DEFAULT", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");
        ignoredRateCodes.add(getIgnoredRateCode("FAM1", "RC2"));
        pmsRevampAMSDataService.ignoreMismatchedRateCodes(ignoredRateCodes);
        addMktSegMaster("FAM_QYL", 1);
        addMktSegMaster("FAM_DEF", 1);
        Integer FAM_QYL_MKT_SEG_ID = addMarketSegment("FAM_QYL");
        Integer FAM_DEF_MKT_SEG_ID = addMarketSegment("FAM_DEF");

        service.assignMktSegToNewAMSRules();

        tenantCrudService().flushAndClear();
        MktSeg FAM_QYL_MktSeg = tenantCrudService().find(MktSeg.class, FAM_QYL_MKT_SEG_ID);
        MktSeg FAM_DEF_MktSeg = tenantCrudService().find(MktSeg.class, FAM_DEF_MKT_SEG_ID);
        List<AnalyticalMarketSegment> analyticalMarketSegments = tenantCrudService().findAll(AnalyticalMarketSegment.class);
        assertEquals("FAM1_QYL", FAM_QYL_MktSeg.getCode());
        assertEquals("FAM1_DEF", FAM_DEF_MktSeg.getCode());
        assertTrue(analyticalMarketSegments.stream().anyMatch(ams -> ams.getMarketCode().equalsIgnoreCase("FAM") && ams.getRateCode() != null && ams.getRateCode().equals("RC1") &&
                ams.getMappedMarketCode().equalsIgnoreCase("FAM_QYL") && ams.getAttribute().equals(AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE) &&
                ams.getRateCodeType().equals(RateCodeTypeEnum.EQUALS)));
        assertTrue(analyticalMarketSegments.stream().anyMatch(ams -> ams.getMarketCode().equalsIgnoreCase("FAM") && ams.getRateCode() != null && ams.getRateCode().equals("RC2") &&
                ams.getMappedMarketCode().equalsIgnoreCase("FAM_DEF") && ams.getAttribute().equals(AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE) &&
                ams.getRateCodeType().equals(RateCodeTypeEnum.EQUALS)));
        assertTrue(analyticalMarketSegments.stream().anyMatch(ams -> ams.getMarketCode().equalsIgnoreCase("FAM1") && ams.getRateCode() != null && ams.getRateCode().equals("RC1") &&
                ams.getMappedMarketCode().equalsIgnoreCase("FAM1_QYL") && ams.getAttribute().equals(AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE) &&
                ams.getRateCodeType().equals(RateCodeTypeEnum.EQUALS)));
        assertTrue(analyticalMarketSegments.stream().anyMatch(ams -> ams.getMarketCode().equalsIgnoreCase("FAM1") && ams.getRateCode() != null && ams.getRateCode().equals("RC2") &&
                ams.getMappedMarketCode().equalsIgnoreCase("FAM1_DEF") && ams.getAttribute().equals(AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE) &&
                ams.getRateCodeType().equals(RateCodeTypeEnum.EQUALS)));
    }

    @Test
    public void shouldRenameExistingMktSegNonGroupWhenMktSegRenameWithNoAttributionChangeHavingBlankRateCodeAMS() {
        setUpServiceForAssigningMSToNewAMSRulesWithNoAttributeChange();
        inject(new PMSRevampAMSDataService(), "tenantCrudService", tenantCrudService());
        addPMSMigrationMappingWith(CODE_TYPE_MS, "FAM", "FAM1", null);
        addPMSMigrationMappingWith(CODE_TYPE_RC, "RC1", "RC1", null);
        addPMSMigrationMappingWith(CODE_TYPE_RC, "RC2", "RC2", null);
        addAnalyticalMarketSegmentWith("FAM", "RC1", "FAM_QYL", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "EQUALS", "10");
        addAnalyticalMarketSegmentWith("FAM", "RC2", "FAM_QYL", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "EQUALS", "10");
        addAnalyticalMarketSegmentWith("FAM", null, "FAM_DEF", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "DEFAULT", "999");
        addAnalyticalMarketSegmentWith("FAM", "", "FAM_DEF", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "EQUALS", "10");
        addPMSRevampNewAMSRule("FAM1", "RC1", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");
        addPMSRevampNewAMSRule("FAM1", "RC2", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");
        addPMSRevampNewAMSRule("FAM1", "DEFAULT", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");
        addMktSegMaster("FAM_QYL", 1);
        addMktSegMaster("FAM_DEF", 1);
        Integer FAM_QYL_MKT_SEG_ID = addMarketSegment("FAM_QYL");
        Integer FAM_DEF_MKT_SEG_ID = addMarketSegment("FAM_DEF");

        service.assignMktSegToNewAMSRules();

        tenantCrudService().flushAndClear();
        MktSeg FAM_QYL_MktSeg = tenantCrudService().find(MktSeg.class, FAM_QYL_MKT_SEG_ID);
        MktSeg FAM_DEF_MktSeg = tenantCrudService().find(MktSeg.class, FAM_DEF_MKT_SEG_ID);
        assertEquals("FAM1_QYL", FAM_QYL_MktSeg.getCode());
        assertEquals("FAM1_DEF", FAM_DEF_MktSeg.getCode());
        List<AnalyticalMarketSegment> analyticalMarketSegments = tenantCrudService().findAll(AnalyticalMarketSegment.class);
        assertTrue(analyticalMarketSegments.stream().anyMatch(ams -> ams.getMarketCode().equalsIgnoreCase("FAM") && ams.getRateCode() != null && ams.getRateCode().equals("RC1") &&
                ams.getMappedMarketCode().equalsIgnoreCase("FAM_QYL") && ams.getAttribute().equals(AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE) &&
                ams.getRateCodeType().equals(RateCodeTypeEnum.EQUALS)));
        assertTrue(analyticalMarketSegments.stream().anyMatch(ams -> ams.getMarketCode().equalsIgnoreCase("FAM") && ams.getRateCode() != null && ams.getRateCode().equals("RC2") &&
                ams.getMappedMarketCode().equalsIgnoreCase("FAM_QYL") && ams.getAttribute().equals(AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE) &&
                ams.getRateCodeType().equals(RateCodeTypeEnum.EQUALS)));
        assertTrue(analyticalMarketSegments.stream().anyMatch(ams -> ams.getMarketCode().equalsIgnoreCase("FAM1") && ams.getRateCode() != null && ams.getRateCode().equals("RC1") &&
                ams.getMappedMarketCode().equalsIgnoreCase("FAM1_QYL") && ams.getAttribute().equals(AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE) &&
                ams.getRateCodeType().equals(RateCodeTypeEnum.EQUALS)));
        assertTrue(analyticalMarketSegments.stream().anyMatch(ams -> ams.getMarketCode().equalsIgnoreCase("FAM") && ams.getRateCode() != null && ams.getRateCode().equals("") &&
                ams.getMappedMarketCode().equalsIgnoreCase("FAM_DEF") && ams.getAttribute().equals(AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE) &&
                ams.getRateCodeType().equals(RateCodeTypeEnum.EQUALS)));
    }

    private MarketSegmentMaster addMktSegMaster(String mappedMarketCode, int forecastTypeId) {
        MarketSegmentMaster mktSegMaster = new MarketSegmentMaster();
        mktSegMaster.setCode(mappedMarketCode);
        mktSegMaster.setForecastActivityTypeId(forecastTypeId);
        mktSegMaster.setIsEditable(1);
        return tenantCrudService().save(mktSegMaster);
    }

    private void setUpServiceForAssigningMSToNewAMSRulesWithNoAttributeChange() {
        setWorkContextProperty(TestProperty.H1);
        tenantCrudService().executeUpdateByNativeQuery(NewAMSRule.CREATE_AMS_RULE_TABLE_DDL);
        PMSRevampAMSDataService pmsRevampAMSDataService = new PMSRevampAMSDataService();
        PMSMigrationMappingService pmsMappingService = new PMSMigrationMappingService();
        AnalyticalMarketSegmentService amsService = new AnalyticalMarketSegmentService();
        MarketSegmentService marketSegmentService = new MarketSegmentService();
        inject(pmsRevampAMSDataService, "tenantCrudService", tenantCrudService());
        inject(pmsRevampAMSDataService, "analyticalMarketSegmentService", analyticalMarketSegmentService);
        inject(pmsRevampAMSDataService, "pacmanConfigParamsService", pacmanConfigParamsService);
        inject(analyticalMarketSegmentService, "marketSegmentRepository", marketSegmentRepository);
        inject(analyticalMarketSegmentService, "independentProductsRepository", independentProductsRepository);
        inject(pmsMappingService, "tenantCrudService", tenantCrudService());
        inject(amsService, "crudService", tenantCrudService());
        inject(marketSegmentService, "crudService", tenantCrudService());
        inject(service, "pmsRevampAMSDataService", pmsRevampAMSDataService);
        inject(service, "pmsMigrationMappingService", pmsMappingService);
        inject(service, "analyticalMarketSegmentService", amsService);
        inject(service, "marketSegmentService", marketSegmentService);
    }

    @Test
    public void invalidatesDiscontinuedMktSegsAndDelegatesWithPMSAmsServiceToAssignMktSegsToNewAMSRules() {
        setUpRequiredData();

        addAnalyticalMarketSegmentWith("MS1", null, "MS1", "GROUP", "ALL", "1");
        addAnalyticalMarketSegmentWith("MS2", null, "MS2", "GROUP", "ALL", "1");
        addAnalyticalMarketSegmentWith("MS3", null, "MS3", "GROUP", "ALL", "1");
        addAnalyticalMarketSegmentWith("MS4", null, "MS4", "GROUP", "ALL", "1");

        addAnalyticalMarketSegmentWith("MS5", "1DAY", "MS5_UF", UNQUALIFIED_FENCED, "EQUALS", "10");
        addAnalyticalMarketSegmentWith("MS5", null, "MS5_DEF", UNQUALIFIED_FENCED, "DEFAULT", "999");
        addAnalyticalMarketSegmentWith("MS5_D", null, "MS5_D_DEF", UNQUALIFIED_FENCED, "DEFAULT", "999");
        addAnalyticalMarketSegmentWith("MS6", "3GTA", "MS6_UF", UNQUALIFIED_FENCED, "EQUALS", "10");
        addAnalyticalMarketSegmentWith("MS6", null, "MS6_DEF", UNQUALIFIED_FENCED, "DEFAULT", "999");

        service.assignMktSegToNewAMSRules();

        assertIfRequiredMktSegAreInvalidated(MktSegInvalidationFromMktSegMigrationMappingScenarios.INVALIDATE_DISCONTINUED_MKT_SEG_SCENARIO[2]);
        verify(pmsRevampAMSDataService).assignOrUpdateMarketSegmentsForNewAMSRules();
    }

    @Test
    public void shouldBeAbleToDetectAndHandleStraightMSToAMSWhenMarketCodeAndRateCodeAreNotChanged() {
        //GIVEN
        setWorkContextProperty(TestProperty.H1);
        tenantCrudService().executeUpdateByNativeQuery(NewAMSRule.CREATE_AMS_RULE_TABLE_DDL);
        PMSRevampAMSDataService pmsRevampAMSDataService = new PMSRevampAMSDataService();
        inject(pmsRevampAMSDataService, "tenantCrudService", tenantCrudService());
        inject(pmsRevampAMSDataService, "analyticalMarketSegmentService", analyticalMarketSegmentService);
        inject(pmsRevampAMSDataService, "pacmanConfigParamsService", pacmanConfigParamsService);
        inject(service, "pmsRevampAMSDataService", pmsRevampAMSDataService);
        //Reservations
        Integer FAM_MKT_SEG_ID = addMarketSegment("FAM");
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-01", "FAM", "COMP", FAM_MKT_SEG_ID);
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-02", "FAM", "COMP", FAM_MKT_SEG_ID);
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-03", "FAM", "COMP", FAM_MKT_SEG_ID);
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-04", "FAM", "COMP", FAM_MKT_SEG_ID);

        addPMSMigrationMappingWith("MARKET_SEGMENT_NON_GROUP", "FAM", "FAM", "null");
        addPMSRevampNewAMSRule("FAM", "COMP", "QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE", "1");
        addPMSRevampNewAMSRule("FAM", "DEFAULT", "QUALIFIED_NONBLOCK_LINKED_SEMIYIELDABLE", "1");
        addAnalyticalMarketSegmentWith("FAM", null, "FAM", "QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE", "ALL", "1");
        //WHEN
        service.assignMktSegToNewAMSRules();
        //THEN
        List<Object[]> rows = tenantCrudService().findByNativeQuery("select * from Analytical_Mkt_Seg");
        assertAnalyticalMktSeg(rows.get(0), "FAM", "COMP", "FAM_QY");
        assertAnalyticalMktSeg(rows.get(1), "FAM", null, "FAM_DEF");
        MktSeg mktSegFAM = tenantCrudService().find(MktSeg.class, FAM_MKT_SEG_ID);
        assertEquals(1, mktSegFAM.getPropertyId().intValue());
    }

    @Test
    public void noNeedToDeleteStraightRuleFromAMSWhenStraightMSToAMSRulesNotFound() {
        //GIVEN
        setWorkContextProperty(TestProperty.H1);
        tenantCrudService().executeUpdateByNativeQuery(NewAMSRule.CREATE_AMS_RULE_TABLE_DDL);
        PMSRevampAMSDataService pmsRevampAMSDataService = new PMSRevampAMSDataService();
        inject(pmsRevampAMSDataService, "tenantCrudService", tenantCrudService());
        inject(pmsRevampAMSDataService, "analyticalMarketSegmentService", analyticalMarketSegmentService);
        inject(pmsRevampAMSDataService, "pacmanConfigParamsService", pacmanConfigParamsService);
        inject(service, "pmsRevampAMSDataService", pmsRevampAMSDataService);
        inject(analyticalMarketSegmentService, "marketSegmentRepository", marketSegmentRepository);
        addPMSMigrationMappingWith("MARKET_SEGMENT_NON_GROUP", "FAM", "FAM", "null");
        addPMSRevampNewAMSRule("FAM", null, "QUALIFIED_NONBLOCK_LINKED_SEMIYIELDABLE", "1");
        addAnalyticalMarketSegmentWith("FAM", null, "FAM", "QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE", "ALL", "1");
        Integer FAM_MKT_SEG_ID = addMarketSegment("FAM");
        //WHEN
        service.assignMktSegToNewAMSRules();
        //THEN
        List<Object[]> rows = tenantCrudService().findByNativeQuery("select * from Analytical_Mkt_Seg");
        assertAnalyticalMktSeg(rows.get(0), "FAM", null, "FAM");
        MktSeg mktSegFAM = tenantCrudService().find(MktSeg.class, FAM_MKT_SEG_ID);
        assertEquals(5, mktSegFAM.getPropertyId().intValue());
    }

    @Test
    public void shouldBeAbleToDetectAndHandleStraightMSToAMSWhenMarketCodeIsChangedButRateCodeNot() {
        //GIVEN
        setWorkContextProperty(TestProperty.H1);
        tenantCrudService().executeUpdateByNativeQuery(NewAMSRule.CREATE_AMS_RULE_TABLE_DDL);
        PMSRevampAMSDataService pmsRevampAMSDataService = new PMSRevampAMSDataService();
        inject(pmsRevampAMSDataService, "tenantCrudService", tenantCrudService());
        inject(pmsRevampAMSDataService, "analyticalMarketSegmentService", analyticalMarketSegmentService);
        inject(pmsRevampAMSDataService, "pacmanConfigParamsService", pacmanConfigParamsService);
        inject(service, "pmsRevampAMSDataService", pmsRevampAMSDataService);
        //Reservations
        Integer FAM_MKT_SEG_ID = addMarketSegment("FAM");
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-01", "FAM", "COMP", FAM_MKT_SEG_ID);
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-02", "FAM", "COMP", FAM_MKT_SEG_ID);
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-03", "FAM", "COMP", FAM_MKT_SEG_ID);
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-04", "FAM", "COMP", FAM_MKT_SEG_ID);
        addPMSMigrationMappingWith("MARKET_SEGMENT_NON_GROUP", "FAM", "FAMNew", "null");
        addPMSRevampNewAMSRule("FAMNew", "COMP", "QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE", "1");
        addPMSRevampNewAMSRule("FAMNew", "DEFAULT", "QUALIFIED_NONBLOCK_LINKED_SEMIYIELDABLE", "1");
        addAnalyticalMarketSegmentWith("FAM", null, "FAM", "QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE", "ALL", "1");
        //WHEN
        service.assignMktSegToNewAMSRules();
        //THEN
        List<Object[]> rows = tenantCrudService().findByNativeQuery("select * from Analytical_Mkt_Seg");
        assertEquals(4, rows.size());
        assertAnalyticalMktSeg(rows.get(0), "FAMNew", "COMP", "FAMNew_QY");
        assertAnalyticalMktSeg(rows.get(1), "FAMNew", null, "FAMNew_DEF");
        assertAnalyticalMktSeg(rows.get(2), "FAM", "COMP", "FAM_QY");
        assertAnalyticalMktSeg(rows.get(3), "FAM", null, "FAM_DEF");
        MktSeg mktSegFAM = tenantCrudService().find(MktSeg.class, FAM_MKT_SEG_ID);
        assertEquals(1, mktSegFAM.getPropertyId().intValue());
    }

    @Test
    public void shouldBeAbleToDetectAndHandleStraightMSToAMS_WhenExistingStraightMSIsMergedAndConvertedToAMS() {
        //GIVEN
        setWorkContextProperty(TestProperty.H1);
        tenantCrudService().executeUpdateByNativeQuery("delete from Reservation_Night");
        tenantCrudService().executeUpdateByNativeQuery("delete from PMS_Migration_Mapping");
        tenantCrudService().executeUpdateByNativeQuery(NewAMSRule.CREATE_AMS_RULE_TABLE_DDL);
        PMSRevampAMSDataService pmsRevampAMSDataService = new PMSRevampAMSDataService();
        inject(pmsRevampAMSDataService, "tenantCrudService", tenantCrudService());
        inject(pmsRevampAMSDataService, "analyticalMarketSegmentService", analyticalMarketSegmentService);
        inject(pmsRevampAMSDataService, "pacmanConfigParamsService", pacmanConfigParamsService);
        inject(service, "pmsRevampAMSDataService", pmsRevampAMSDataService);
        Integer FAM_MKT_SEG_ID = addMarketSegment("FAM");
        Integer LNG_MKT_SEG_ID = addMarketSegment("LNG");
        //Reservations
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-01", "FAM", "COMP", FAM_MKT_SEG_ID);
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-02", "FAM", "COMP", FAM_MKT_SEG_ID);
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-03", "FAM", "COMP", FAM_MKT_SEG_ID);
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-04", "FAM", "COMP", FAM_MKT_SEG_ID);

        addReservationNightEntry("123456799", "2019-01-01", "2019-01-04", "2019-01-01", "LNG", "ROOM", LNG_MKT_SEG_ID);
        addReservationNightEntry("123456799", "2019-01-01", "2019-01-04", "2019-01-02", "LNG", "ROOM", LNG_MKT_SEG_ID);
        addReservationNightEntry("123456799", "2019-01-01", "2019-01-04", "2019-01-03", "LNG", "ROOM", LNG_MKT_SEG_ID);
        //Configurations
        addPMSMigrationMappingWith("MARKET_SEGMENT_NON_GROUP", "FAM", "FALNG", "null");
        addPMSMigrationMappingWith("MARKET_SEGMENT_NON_GROUP", "LNG", "FALNG", "null");
        addPMSRevampNewAMSRule("FALNG", "COMP", "QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE", "1");
        addPMSRevampNewAMSRule("FALNG", "ROOM", "QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE", "1");
        addPMSRevampNewAMSRule("FALNG", "DEFAULT", "QUALIFIED_NONBLOCK_LINKED_SEMIYIELDABLE", "1");
        addAnalyticalMarketSegmentWith("FAM", null, "FAM", "QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE", "ALL", "1");
        addAnalyticalMarketSegmentWith("LNG", null, "LNG", "QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE", "ALL", "1");
        //WHEN
        service.assignMktSegToNewAMSRules();
        //THEN
        List<Object[]> rows = tenantCrudService().findByNativeQuery("select * from Analytical_Mkt_Seg");
        assertEquals(7, rows.size());
        assertAnalyticalMktSeg(rows.get(0), "FALNG", "COMP", "FALNG_QY");
        assertAnalyticalMktSeg(rows.get(1), "FALNG", "ROOM", "FALNG_QY");
        assertAnalyticalMktSeg(rows.get(2), "FALNG", null, "FALNG_DEF");
        assertAnalyticalMktSeg(rows.get(3), "FAM", "COMP", "FAM_QY");
        assertAnalyticalMktSeg(rows.get(4), "LNG", "ROOM", "LNG_QY");
        assertAnalyticalMktSeg(rows.get(5), "FAM", null, "FAM_DEF");
        assertAnalyticalMktSeg(rows.get(6), "LNG", null, "LNG_DEF");
        MktSeg mktSegFAM = tenantCrudService().find(MktSeg.class, FAM_MKT_SEG_ID);
        assertEquals(1, mktSegFAM.getPropertyId().intValue());
        MktSeg mktSegLNG = tenantCrudService().find(MktSeg.class, LNG_MKT_SEG_ID);
        assertEquals(1, mktSegLNG.getPropertyId().intValue());
    }

    @Test
    public void shouldBeAbleToDetectAndHandleStraightMSToAMS_WhenExistingStraightMSIsSplitsIntoMultipleMSAndConvertedToAMS() {
        //GIVEN
        setWorkContextProperty(TestProperty.H1);
        tenantCrudService().executeUpdateByNativeQuery("delete from Reservation_Night");
        tenantCrudService().executeUpdateByNativeQuery("delete from PMS_Migration_Mapping");
        tenantCrudService().executeUpdateByNativeQuery(NewAMSRule.CREATE_AMS_RULE_TABLE_DDL);
        PMSRevampAMSDataService pmsRevampAMSDataService = new PMSRevampAMSDataService();
        inject(pmsRevampAMSDataService, "tenantCrudService", tenantCrudService());
        inject(pmsRevampAMSDataService, "analyticalMarketSegmentService", analyticalMarketSegmentService);
        inject(pmsRevampAMSDataService, "pacmanConfigParamsService", pacmanConfigParamsService);
        inject(service, "pmsRevampAMSDataService", pmsRevampAMSDataService);
        Integer FAM_MKT_SEG_ID = addMarketSegment("FAM");
        //Reservations
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-01", "FAM", "COMP", FAM_MKT_SEG_ID);
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-02", "FAM", "COMP", FAM_MKT_SEG_ID);
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-03", "FAM", "COMP", FAM_MKT_SEG_ID);
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-04", "FAM", "COMP", FAM_MKT_SEG_ID);

        addReservationNightEntry("123456799", "2019-01-01", "2019-01-04", "2019-01-01", "FAM", "ROOM", FAM_MKT_SEG_ID);
        addReservationNightEntry("123456799", "2019-01-01", "2019-01-04", "2019-01-02", "FAM", "ROOM", FAM_MKT_SEG_ID);
        addReservationNightEntry("123456799", "2019-01-01", "2019-01-04", "2019-01-03", "FAM", "ROOM", FAM_MKT_SEG_ID);
        //Configurations
        addPMSMigrationMappingWith("MARKET_SEGMENT_NON_GROUP", "FAM", "FAM1", "1");
        addPMSMigrationMappingWith("MARKET_SEGMENT_NON_GROUP", "FAM", "FAM2", "0");
        addPMSRevampNewAMSRule("FAM1", "COMP", "QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE", "1");
        addPMSRevampNewAMSRule("FAM2", "ROOM", "QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE", "1");
        addPMSRevampNewAMSRule("FAM1", "DEFAULT", "QUALIFIED_NONBLOCK_LINKED_SEMIYIELDABLE", "1");
        addPMSRevampNewAMSRule("FAM2", "DEFAULT", "QUALIFIED_NONBLOCK_LINKED_SEMIYIELDABLE", "1");
        addAnalyticalMarketSegmentWith("FAM", null, "FAM", "QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE", "ALL", "1");
        //WHEN
        service.assignMktSegToNewAMSRules();
        //THEN
        List<Object[]> rows = tenantCrudService().findByNativeQuery("select * from Analytical_Mkt_Seg");
        assertEquals(7, rows.size());
        assertAnalyticalMktSeg(rows.get(0), "FAM1", "COMP", "FAM1_QY");
        assertAnalyticalMktSeg(rows.get(1), "FAM2", "ROOM", "FAM2_QY");
        assertAnalyticalMktSeg(rows.get(2), "FAM1", null, "FAM1_DEF");
        assertAnalyticalMktSeg(rows.get(3), "FAM2", null, "FAM2_DEF");
        assertAnalyticalMktSeg(rows.get(4), "FAM", "COMP", "FAM_QY");
        assertAnalyticalMktSeg(rows.get(5), "FAM", "ROOM", "FAM_QY");
        assertAnalyticalMktSeg(rows.get(6), "FAM", null, "FAM_DEF");
        MktSeg mktSegFAM = tenantCrudService().find(MktSeg.class, FAM_MKT_SEG_ID);
        assertEquals(1, mktSegFAM.getPropertyId().intValue());
    }

    @Test
    public void shouldBeAbleToDetectAndHandleStraightMSToAMS_WhenRateCodeIsChange() {
        //GIVEN
        setWorkContextProperty(TestProperty.H1);
        tenantCrudService().executeUpdateByNativeQuery("delete from Reservation_Night");
        tenantCrudService().executeUpdateByNativeQuery("delete from PMS_Migration_Mapping");
        tenantCrudService().executeUpdateByNativeQuery(NewAMSRule.CREATE_AMS_RULE_TABLE_DDL);
        PMSRevampAMSDataService pmsRevampAMSDataService = new PMSRevampAMSDataService();
        inject(pmsRevampAMSDataService, "tenantCrudService", tenantCrudService());
        inject(pmsRevampAMSDataService, "analyticalMarketSegmentService", analyticalMarketSegmentService);
        inject(pmsRevampAMSDataService, "pacmanConfigParamsService", pacmanConfigParamsService);
        inject(service, "pmsRevampAMSDataService", pmsRevampAMSDataService);
        Integer FAM_MKT_SEG_ID = addMarketSegment("FAM");
        //Reservations
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-01", "FAM", "COMP", FAM_MKT_SEG_ID);
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-02", "FAM", "COMP", FAM_MKT_SEG_ID);
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-03", "FAM", "COMP", FAM_MKT_SEG_ID);
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-04", "FAM", "COMP", FAM_MKT_SEG_ID);

        addReservationNightEntry("123456799", "2019-01-01", "2019-01-04", "2019-01-01", "FAM", "ROOM", FAM_MKT_SEG_ID);
        addReservationNightEntry("123456799", "2019-01-01", "2019-01-04", "2019-01-02", "FAM", "ROOM", FAM_MKT_SEG_ID);
        addReservationNightEntry("123456799", "2019-01-01", "2019-01-04", "2019-01-03", "FAM", "ROOM", FAM_MKT_SEG_ID);
        //Configurations
        addPMSMigrationMappingWith("MARKET_SEGMENT_NON_GROUP", "FAM", "FAM", "null");
        addPMSMigrationMappingWith("RATE_CODE", "COMP", "COMPNew", "null");
        addPMSMigrationMappingWith("RATE_CODE", "ROOM", "ROOM", "null");
        addPMSRevampNewAMSRule("FAM", "COMPNew", "QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE", "1");
        addPMSRevampNewAMSRule("FAM", "ROOM", "QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE", "1");
        addPMSRevampNewAMSRule("FAM", "DEFAULT", "QUALIFIED_NONBLOCK_LINKED_SEMIYIELDABLE", "1");
        addAnalyticalMarketSegmentWith("FAM", null, "FAM", "QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE", "ALL", "1");
        //WHEN
        service.assignMktSegToNewAMSRules();
        //THEN
        List<Object[]> rows = tenantCrudService().findByNativeQuery("select * from Analytical_Mkt_Seg");
        assertEquals(4, rows.size());
        assertAnalyticalMktSeg(rows.get(0), "FAM", "COMPNew", "FAM_QY");
        assertAnalyticalMktSeg(rows.get(1), "FAM", "ROOM", "FAM_QY");
        assertAnalyticalMktSeg(rows.get(2), "FAM", null, "FAM_DEF");
        assertAnalyticalMktSegWithPreserved(rows.get(3), "FAM", "COMP", "FAM_QY", true);
        MktSeg mktSegFAM = tenantCrudService().find(MktSeg.class, FAM_MKT_SEG_ID);
        assertEquals(1, mktSegFAM.getPropertyId().intValue());
    }

    private void addReservationNightEntry(final String reservationId, final String arrivalDt, final String departureDt, final String occupancyDt,
                                          final String marketCode, final String rateCode, Integer mktSegId) {
        tenantCrudService().executeUpdateByNativeQuery("insert into Reservation_Night (File_Metadata_ID,Property_ID,Reservation_Identifier," +
                "Individual_Status,Arrival_DT,Departure_DT,Booking_DT,Cancellation_DT," +
                "Booked_Accom_Type_Code,Accom_Type_ID,Mkt_Seg_ID,Room_Revenue,Food_Revenue,Beverage_Revenue," +
                "Telecom_Revenue,Other_Revenue,Total_Revenue,Source_Booking,Nationality,Rate_Code,Rate_Value,Room_Number," +
                "Booking_type,Number_Children,Number_Adults,CreateDate_DTTM,Confirmation_No,Channel,Booking_TM," +
                "Occupancy_DT, Persistent_Key, Analytics_Booking_Dt, Inv_Block_Code, Market_Code) " +
                "values(1, 5, " + reservationId + ", 'SS', '" + arrivalDt + "', '" + departureDt + "', '2018-12-24', '1900-01-01', 'SXBL', 4, " + mktSegId
                + ", '100.00000', '50.00000', '10.00000', '3.00000', '0.00000', '163.00000', '', '', '" + rateCode + "', '30.00000', 127, 'IN', 0, 2, getdate(), null, null, null, '"
                + occupancyDt + "', '123456789-2019-01-01', null,null," + (null == marketCode ? "null" : "'" + marketCode + "'") + ")");
    }

    private Integer addMarketSegment(final String code) {
        tenantCrudService().executeUpdateByNativeQuery("insert into Mkt_Seg values(" + PacmanWorkContextHelper.getPropertyId() + ", '" + code + "', '" + code + "', '" + code + "', 1, getdate(), 1, 1, getdate(), 1, 0, 0)");
        return getMktSegIdByCode(code);
    }

    private void addMarketSegmentProductMapping(String code, Product product) {
        tenantCrudService().executeUpdateByNativeQuery("insert into mkt_seg_product_mapping values('" + code + "'," + product.getId() + ", 0, 1, 0, 1, GETDATE(), 1, GETDATE())");
    }

    private Product addProduct(String name) {
        Product product = ProductBuilder.createIndependentProductProduct(name);
        tenantCrudService().save(product);
        return product;
    }

    private void addProductRateCodeMapping(String code, Product product) {
        tenantCrudService().executeUpdateByNativeQuery("insert into Product_Rate_Code values(" + product.getId() + ", '" + code + "', 1, GETDATE(), 1, getdate(), null)");
    }

    private Integer getMktSegIdByCode(String code) {
        try {
            return tenantCrudService().findByNativeQuerySingleResult("select Mkt_Seg_ID from Mkt_Seg where Mkt_Seg_Code = :code",
                    QueryParameter.with("code", code).parameters());
        } catch (NoResultException e) {
            return null;
        }
    }

    private void assertAnalyticalMktSeg(Object[] row, String expectedMarketCode, String expectedRateCode, String expectedMappedMarketCode) {
        assertEquals(expectedMarketCode, row[1]);
        assertEquals(expectedRateCode, row[2]);
        assertEquals(expectedMappedMarketCode, row[3]);
    }

    private void assertAnalyticalMktSegWithPreserved(Object[] row, String expectedMarketCode,
                                                     String expectedRateCode, String expectedMappedMarketCode,
                                                     boolean expectedPreserved) {
        assertAnalyticalMktSeg(row, expectedMarketCode, expectedRateCode, expectedMappedMarketCode);
        assertEquals(expectedPreserved, row[12]);
    }

    private void addAnalyticalMarketSegmentWith(String marketCode, String rateCode, String mappedMarketCode, String attribute, String rateCodeType, String rank) {
        analyticalMarketSegmentCreationHelper.addAnalyticalMarketSegmentEntry(marketCode, rateCode, mappedMarketCode, attribute, rateCodeType, rank);
    }

    private void addPMSMigrationMappingWith(String codeType, String currentCode, String newCode, String isPrimary) {
        pmsMigrationMappingCreationHelper.insertIntoPMSMigrationMapping(codeType, currentCode, newCode, isPrimary, PacmanWorkContextHelper.getPropertyCode());
    }

    private void addPMSRevampNewAMSRule(final String marketCode, final String rateCode, final String attribute, final String forecastType) {
        tenantCrudService().executeUpdateByNativeQuery("insert into PMS_Revamp_New_AMS_Rule values('" + marketCode
                + "', " + (null == rateCode ? "null" : "'" + rateCode + "'") + ", '" + attribute + "', " + forecastType + ", null )");
    }

    private void addPMSRevampNewAMSRuleWithProduct(final String marketCode, final String rateCode, final String attribute, final String forecastType, final Integer productId) {
        tenantCrudService().executeUpdateByNativeQuery("insert into PMS_Revamp_New_AMS_Rule values('" + marketCode
                + "', " + (null == rateCode ? "null" : "'" + rateCode + "'") + ", '" + attribute + "', '" + forecastType + "', '" + productId + "' )");
    }

    private void addPMSRateCodeMktSegMapping(final String currentRateCode, final String newRateCode, final String mktCode) {
        tenantCrudService().executeUpdateByNativeQuery("insert into PMS_Migration_Rate_code_mkt_seg_Mapping values('" + currentRateCode
                + "', '" + newRateCode + "'" + ", '" + mktCode + "' )");
    }

    private void addPMSMigrationTempCodeMapping(final String codeType, final String currentCode, final String newOriginalCode, final String newTemporaryCode) {
        tenantCrudService().executeUpdateByNativeQuery("insert into PMS_Migration_Temporary_Mapping values('" + codeType
                + "', '" + currentCode + "', '" + newOriginalCode + "', '" + newTemporaryCode + "' )");
    }

    private IgnoredRateCode getIgnoredRateCode(String marketCode, String rateCode) {
        IgnoredRateCode ignoredRateCode = new IgnoredRateCode();
        ignoredRateCode.setMarketSegment(marketCode);
        ignoredRateCode.setRateCode(rateCode);
        ignoredRateCode.setMismatchType(IgnoredRateCode.MismatchType.MISSING);
        return ignoredRateCode;
    }

    @Test
    public void invalidatesRedundantMktSegs() {
        List<String> distinctNewEquivalentMktSegCodes = new ArrayList<>(Arrays.asList("MS6", "MS4", "MS8", "MS9", "MS7"));
        List<String> changedCurrentMktSegCodes = new ArrayList<>(Arrays.asList("MS2", "MS3", "MS4", "MS5"));
        when(pmsMigrationMappingService.getDistinctNewEquivalentMktSegCodes()).thenReturn(distinctNewEquivalentMktSegCodes);
        when(pmsMigrationMappingService.getChangedCurrentMktSegCodes()).thenReturn(changedCurrentMktSegCodes);
        setUpRequiredMktSegData(MktSegInvalidationFromMktSegMigrationMappingScenarios.INVALIDATE_REDUNDANT_MKT_SEG_SCENARIO[0]);

        addAnalyticalMarketSegmentWith("MS1", null, "MS1", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "ALL", "1");
        addAnalyticalMarketSegmentWith("MS2", null, "MS2", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "ALL", "1");
        addAnalyticalMarketSegmentWith("MS3", null, "MS3", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "ALL", "1");
        addAnalyticalMarketSegmentWith("MS4", null, "MS4", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "ALL", "1");
        addAnalyticalMarketSegmentWith("MS5", RC1_RATE_CODE, "MS5_UF", UNQUALIFIED_FENCED, "EQUALS", "10");
        addAnalyticalMarketSegmentWith("MS5", null, "MS5_DEF", UNQUALIFIED_FENCED, "DEFAULT", "999");
        addAnalyticalMarketSegmentWith("MS6", null, "MS6", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "ALL", "1");
        addAnalyticalMarketSegmentWith("MS7", RC2_RATE_CODE, "MS7_UF", UNQUALIFIED_FENCED, "EQUALS", "10");
        addAnalyticalMarketSegmentWith("MS7", null, "MS7_DEF", UNQUALIFIED_FENCED, "DEFAULT", "999");
        addAnalyticalMarketSegmentWith("MS8", null, "MS8", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "ALL", "1");
        addAnalyticalMarketSegmentWith("MS9", null, "MS9", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "ALL", "1");


        tenantCrudService().flushAndClear();
        service.invalidateRedundantMktSeg();

        assertIfRequiredMktSegAreInvalidated(MktSegInvalidationFromMktSegMigrationMappingScenarios.INVALIDATE_REDUNDANT_MKT_SEG_SCENARIO[1]);
    }

    @Test
    public void explodesAMSRulesWithAttributeAssignmentForMktCodesThatAreUndergoingRecoding() {
        setUpMSRecodingService();
        setUpYCBR();
        insertPMSMigrationMapping(CODE_TYPE_MS, OLD_MS_1, OLD_MS_1, "0");
        insertPMSMigrationMapping(CODE_TYPE_MS, OLD_MS_2, NEW_MS_2, "0");
        insertPMSMigrationMapping(CODE_TYPE_MS, OLD_MS_3, NEW_MS_3, "0");
        insertAnalyticalMktSeg(OLD_MS_1, OLD_RC_1_ASSIGNMENT_RULE, OLD_MAPPED_MARKET_CODE_1, RateCodeTypeEnum.STARTS_WITH, "0", ATTRIBUTE);
        insertAnalyticalMktSeg(OLD_MS_2, OLD_RC_2_ASSIGNMENT_RULE, OLD_MAPPED_MARKET_CODE_2, RateCodeTypeEnum.STARTS_WITH, "0", ATTRIBUTE);
        insertAnalyticalMktSeg(OLD_MS_3, OLD_RC_3, OLD_MAPPED_MARKET_CODE_3, RateCodeTypeEnum.EQUALS, "0", ATTRIBUTE);
        List<AnalyticalMarketSegment> allAMSRulesFromDbBefore = getAllAMSRules();

        service.ycbrToAMS();

        List<AnalyticalMarketSegment> allAMSRulesFromDbAfter = getAllAMSRules();
        assertNotSame(allAMSRulesFromDbBefore, allAMSRulesFromDbAfter);
        assertTrue(allAMSRulesFromDbAfter.stream().anyMatch(ams -> ams.getMarketCode().equalsIgnoreCase(OLD_MS_1) && ams.getRateCode().equalsIgnoreCase(OLD_RC_1_ASSIGNMENT_RULE)));
        assertTrue(allAMSRulesFromDbAfter.stream().noneMatch(ams -> ams.getMarketCode().equalsIgnoreCase(OLD_MS_2) && ams.getRateCode().equalsIgnoreCase(OLD_RC_2_ASSIGNMENT_RULE)));
        assertTrue(allAMSRulesFromDbAfter.stream().anyMatch(ams -> ams.getMarketCode().equalsIgnoreCase(OLD_MS_3) && ams.getRateCode().equalsIgnoreCase(OLD_RC_3)));
        assertTrue(allAMSRulesFromDbAfter.stream().noneMatch(ams -> ams.getMarketCode().equalsIgnoreCase(OLD_MS_3) && !ams.getRateCode().equalsIgnoreCase(OLD_RC_3)));
        assertTrue(allAMSRulesFromDbAfter.stream().filter(ams -> ams.getMarketCode().equalsIgnoreCase(OLD_MS_2))
                .allMatch(ams -> ams.getRateCodeType().equals(RateCodeTypeEnum.EQUALS) && ams.getRank() == RateCodeTypeEnum.EQUALS.getRank() && ams.getAttribute().equals(AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE)));
    }

    @Test
    public void explodesAMSRulesForRelatedMktCodesEvenWhenOnlyRateCodesChangedAndNoMktCodesAreChanged() {
        setUpMSRecodingService();
        setUpYCBR();
        insertPMSMigrationMapping(CODE_TYPE_RC, OLD_RC_1, NEW_RC_1, "0");
        insertPMSMigrationMapping(CODE_TYPE_RC, OLD_RC_2, NEW_RC_2, "0");
        insertPMSMigrationMapping(CODE_TYPE_RC, OLD_RC_3, OLD_RC_3, "0");
        insertAnalyticalMktSeg(OLD_MS_1, OLD_RC_1_ASSIGNMENT_RULE, OLD_MAPPED_MARKET_CODE_1, RateCodeTypeEnum.CONTAINS, "0", ATTRIBUTE);
        insertAnalyticalMktSeg(OLD_MS_1, null, OLD_MAPPED_MARKET_CODE_1_DEF, RateCodeTypeEnum.DEFAULT, "0", ATTRIBUTE);
        insertAnalyticalMktSeg(OLD_MS_2, OLD_RC_2_ASSIGNMENT_RULE, OLD_MAPPED_MARKET_CODE_2, RateCodeTypeEnum.STARTS_WITH, "0", ATTRIBUTE);
        insertAnalyticalMktSeg(OLD_MS_3, OLD_RC_3_ASSIGNMENT_RULE, OLD_MAPPED_MARKET_CODE_3, RateCodeTypeEnum.STARTS_WITH, "0", ATTRIBUTE);
        List<AnalyticalMarketSegment> allAMSRulesFromDbBefore = getAllAMSRules();

        service.ycbrToAMS();

        List<AnalyticalMarketSegment> allAMSRulesFromDbAfter = getAllAMSRules();
        assertNotSame(allAMSRulesFromDbBefore, allAMSRulesFromDbAfter);
        assertTrue(allAMSRulesFromDbAfter.stream().noneMatch(ams -> ams.getMarketCode().equalsIgnoreCase(OLD_MS_1) && OLD_RC_1_ASSIGNMENT_RULE.equalsIgnoreCase(ams.getRateCode())));
        assertTrue(allAMSRulesFromDbAfter.stream().noneMatch(ams -> ams.getMarketCode().equalsIgnoreCase(OLD_MS_2) && ams.getRateCode().equalsIgnoreCase(OLD_RC_2_ASSIGNMENT_RULE)));
        assertTrue(allAMSRulesFromDbAfter.stream().anyMatch(ams -> ams.getMarketCode().equalsIgnoreCase(OLD_MS_3) && ams.getRateCode().equalsIgnoreCase(OLD_RC_3_ASSIGNMENT_RULE)));
        assertTrue(allAMSRulesFromDbAfter.stream().noneMatch(ams -> ams.getMarketCode().equalsIgnoreCase(OLD_MS_3) && !ams.getRateCode().equalsIgnoreCase(OLD_RC_3_ASSIGNMENT_RULE)));
        assertTrue(allAMSRulesFromDbAfter.stream().filter(ams -> (ams.getMarketCode().equalsIgnoreCase(OLD_MS_1) || ams.getMarketCode().equalsIgnoreCase(OLD_MS_2)) && Objects.nonNull(ams.getRateCode()))
                .allMatch(ams -> ams.getRateCodeType().equals(RateCodeTypeEnum.EQUALS) && ams.getRank() == RateCodeTypeEnum.EQUALS.getRank() && ams.getAttribute().equals(AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE)));
        assertTrue(allAMSRulesFromDbAfter.stream().filter(ams -> (ams.getMappedMarketCode().equalsIgnoreCase(OLD_MAPPED_MARKET_CODE_1_DEF))).anyMatch(ams -> StringUtils.isEmpty(ams.getRateCode()) && ams.getRateCodeType().equals(RateCodeTypeEnum.DEFAULT) && ams.getRank() == RateCodeTypeEnum.DEFAULT.getRank() && ams.getAttribute().equals(AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR)));
    }

    @Test
    public void explodesAMSRulesWhichAreUndergoingRecodingEvenWhenOnlyRateCodesAreOnlyChanged() {
        setUpMSRecodingService();
        setUpYCBR();
        insertPMSMigrationMapping(CODE_TYPE_RC, OLD_RC_1, NEW_RC_1, "0");
        insertPMSMigrationMapping(CODE_TYPE_RC, OLD_RC_2, NEW_RC_2, "0");
        insertPMSMigrationMapping(CODE_TYPE_RC, OLD_RC_3, OLD_RC_3, "0");
        insertAnalyticalMktSeg(OLD_MS_1, OLD_RC_1, OLD_MAPPED_MARKET_CODE_1, RateCodeTypeEnum.EQUALS, "0", ATTRIBUTE);
        insertAnalyticalMktSeg(OLD_MS_2, OLD_RC_2_ASSIGNMENT_RULE, OLD_MAPPED_MARKET_CODE_2, RateCodeTypeEnum.STARTS_WITH, "0", ATTRIBUTE);
        insertAnalyticalMktSeg(OLD_MS_3, OLD_RC_3_ASSIGNMENT_RULE, OLD_MAPPED_MARKET_CODE_3, RateCodeTypeEnum.STARTS_WITH, "0", ATTRIBUTE);
        List<AnalyticalMarketSegment> allAMSRulesFromDbBefore = getAllAMSRules();

        service.ycbrToAMS();

        List<AnalyticalMarketSegment> allAMSRulesFromDbAfter = getAllAMSRules();
        assertNotSame(allAMSRulesFromDbBefore, allAMSRulesFromDbAfter);
        assertTrue(allAMSRulesFromDbAfter.stream().anyMatch(ams -> ams.getMarketCode().equalsIgnoreCase(OLD_MS_1) && ams.getRateCode().equalsIgnoreCase(OLD_RC_1)));
        assertTrue(allAMSRulesFromDbAfter.stream().noneMatch(ams -> ams.getMarketCode().equalsIgnoreCase(OLD_MS_1) && !ams.getRateCode().equalsIgnoreCase(OLD_RC_1)));
        assertTrue(allAMSRulesFromDbAfter.stream().noneMatch(ams -> ams.getMarketCode().equalsIgnoreCase(OLD_MS_2) && ams.getRateCode().equalsIgnoreCase(OLD_RC_2_ASSIGNMENT_RULE)));
        assertTrue(allAMSRulesFromDbAfter.stream().anyMatch(ams -> ams.getMarketCode().equalsIgnoreCase(OLD_MS_3) && ams.getRateCode().equalsIgnoreCase(OLD_RC_3_ASSIGNMENT_RULE)));
        assertTrue(allAMSRulesFromDbAfter.stream().noneMatch(ams -> ams.getMarketCode().equalsIgnoreCase(OLD_MS_3) && !ams.getRateCode().equalsIgnoreCase(OLD_RC_3_ASSIGNMENT_RULE)));
        assertTrue(allAMSRulesFromDbAfter.stream().filter(ams -> ams.getMarketCode().equalsIgnoreCase(OLD_MS_2))
                .allMatch(ams -> ams.getRateCodeType().equals(RateCodeTypeEnum.EQUALS) && ams.getRank() == RateCodeTypeEnum.EQUALS.getRank() && ams.getAttribute().equals(AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE)));
    }

    @Test
    public void explodedRulesDoNotContainDefaultRules() {
        setUpMSRecodingService();
        setUpYCBR();
        insertPMSMigrationMapping(CODE_TYPE_MS, OLD_MS_1, NEW_MS_1, "0");
        insertPMSMigrationMapping(CODE_TYPE_MS, OLD_MS_2, NEW_MS_2, "0");
        insertPMSMigrationMapping(CODE_TYPE_MS, OLD_MS_3, OLD_MS_3, "0");
        insertAnalyticalMktSeg(OLD_MS_1, OLD_RC_1, OLD_MAPPED_MARKET_CODE_1, RateCodeTypeEnum.EQUALS, "0", ATTRIBUTE);
        insertAnalyticalMktSeg(OLD_MS_2, OLD_RC_2_ASSIGNMENT_RULE, OLD_MAPPED_MARKET_CODE_2, RateCodeTypeEnum.STARTS_WITH, "0", ATTRIBUTE);
        insertAnalyticalMktSeg(OLD_MS_3, OLD_RC_3_ASSIGNMENT_RULE, OLD_MAPPED_MARKET_CODE_3, RateCodeTypeEnum.STARTS_WITH, "0", ATTRIBUTE);

        service.ycbrToAMS();

        List<AnalyticalMarketSegment> allAMSRulesFromDb = getAllAMSRules();
        assertTrue(allAMSRulesFromDb.stream().noneMatch(ams -> ams.getMarketCode().equalsIgnoreCase(OLD_MS_1) && ams.getRateCodeType() == RateCodeTypeEnum.DEFAULT));
        assertTrue(allAMSRulesFromDb.stream().noneMatch(ams -> ams.getMarketCode().equalsIgnoreCase(OLD_MS_2) && ams.getRateCodeType() == RateCodeTypeEnum.DEFAULT));
        assertTrue(allAMSRulesFromDb.stream().noneMatch(ams -> ams.getMarketCode().equalsIgnoreCase(OLD_MS_3) && ams.getRateCodeType() == RateCodeTypeEnum.DEFAULT));
    }

    @Test
    public void deletesExistingAttributeRulesAfterExploded() {
        setUpMSRecodingService();
        setUpYCBR();
        insertPMSMigrationMapping(CODE_TYPE_MS, OLD_MS_1, NEW_MS_1, "0");
        insertPMSMigrationMapping(CODE_TYPE_MS, OLD_MS_2, NEW_MS_2, "0");
        insertPMSMigrationMapping(CODE_TYPE_MS, OLD_MS_3, OLD_MS_3, "0");
        insertAnalyticalMktSeg(OLD_MS_1, OLD_RC_1, OLD_MAPPED_MARKET_CODE_1, RateCodeTypeEnum.EQUALS, "0", ATTRIBUTE);
        insertAnalyticalMktSeg(OLD_MS_2, OLD_RC_2_ASSIGNMENT_RULE, OLD_MAPPED_MARKET_CODE_2, RateCodeTypeEnum.STARTS_WITH, "0", ATTRIBUTE);
        insertAnalyticalMktSeg(OLD_MS_3, OLD_RC_3_ASSIGNMENT_RULE, OLD_MAPPED_MARKET_CODE_3, RateCodeTypeEnum.STARTS_WITH, "0", ATTRIBUTE);

        service.ycbrToAMS();

        List<AnalyticalMarketSegment> allAMSRulesFromDb = getAllAMSRules();
        assertTrue(allAMSRulesFromDb.stream().noneMatch(ams -> ams.getMarketCode().equalsIgnoreCase(OLD_MS_2) && ams.getRateCode().equalsIgnoreCase(OLD_RC_2_ASSIGNMENT_RULE)));
        assertTrue(allAMSRulesFromDb.stream().anyMatch(ams -> ams.getMarketCode().equalsIgnoreCase(OLD_MS_3) && ams.getRateCode().equalsIgnoreCase(OLD_RC_3_ASSIGNMENT_RULE)));
    }

    @Test
    public void deletesExistingAMSRulesThatAreNowConvertedToStraightRules_MSAndRCUnchanged() {
        setWorkContextProperty(TestProperty.H1);
        setUpMSRecodingService();
        setUpPMSRevampAMSServiceForRecoding();
        addPMSMigrationMappingWith(CODE_TYPE_MS, "FAM", "FAM", null);
        addPMSMigrationMappingWith(CODE_TYPE_RC, "RC1", "RC1", null);
        addPMSMigrationMappingWith(CODE_TYPE_RC, "RC2", "RC2", null);
        addPMSRevampNewAMSRule("FAM", null, "EQUAL_TO_BAR", "1");
        addAnalyticalMarketSegmentWith("FAM", "RC1", "FAM_USB", "EQUAL_TO_BAR", "EQUALS", "10");
        addAnalyticalMarketSegmentWith("FAM", "RC2", "FAM_USB", "EQUAL_TO_BAR", "EQUALS", "10");
        addAnalyticalMarketSegmentWith("FAM", null, "FAM_DEF", "EQUAL_TO_BAR", "DEFAULT", "999");
        addMarketSegment("FAM_USB");
        addMarketSegment("FAM_DEF");
        tenantCrudService().flushAndClear();
        service.assignMktSegToNewAMSRules();

        tenantCrudService().flushAndClear();
        List<AnalyticalMarketSegment> allAMSRulesFromDb = getAllAMSRules();
        List<MktSeg> allMktSegs = tenantCrudService().findAll(MktSeg.class);
        assertTrue(allMktSegs.stream().anyMatch(mktSeg -> mktSeg.getCode().equals("FAM_USB") && mktSeg.getPropertyId() == 1));
        assertTrue(allMktSegs.stream().anyMatch(mktSeg -> mktSeg.getCode().equals("FAM_DEF") && mktSeg.getPropertyId() == 1));
        assertTrue(allMktSegs.stream().anyMatch(mktSeg -> mktSeg.getCode().equals("FAM") && mktSeg.getPropertyId() == 5));
        assertTrue(allAMSRulesFromDb.stream().noneMatch(ams -> ams.getMarketCode().equals("FAM") && ams.getRateCode() != null && (ams.getRateCode().equals("RC1") || ams.getRateCode().equals("RC2"))));
        assertTrue(allAMSRulesFromDb.stream().noneMatch(ams -> ams.getMappedMarketCode().equals("FAM_DEF")));
        assertTrue(allAMSRulesFromDb.stream().anyMatch(ams -> ams.getMarketCode().equals("FAM") && ams.getRateCode() == null && ams.getMappedMarketCode().equals("FAM") &&
                ams.getRateCodeType().equals(RateCodeTypeEnum.ALL) && ams.getAttribute().equals(AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR)));
    }

    @Test
    public void deletesExistingAMSRulesThatAreNowConvertedToStraightRules_MSCUnchangedRCRenamed() {
        setWorkContextProperty(TestProperty.H1);
        setUpMSRecodingService();
        setUpPMSRevampAMSServiceForRecoding();
        addPMSMigrationMappingWith(CODE_TYPE_MS, "FAM", "FAM", null);
        addPMSMigrationMappingWith(CODE_TYPE_RC, "RC1", "RC3", null);
        addPMSMigrationMappingWith(CODE_TYPE_RC, "RC2", "RC2", null);
        addPMSRevampNewAMSRule("FAM", null, "EQUAL_TO_BAR", "1");
        addAnalyticalMarketSegmentWith("FAM", "RC1", "FAM_USB", "EQUAL_TO_BAR", "EQUALS", "10");
        addAnalyticalMarketSegmentWith("FAM", "RC2", "FAM_USB", "EQUAL_TO_BAR", "EQUALS", "10");
        addAnalyticalMarketSegmentWith("FAM", null, "FAM_DEF", "EQUAL_TO_BAR", "DEFAULT", "999");
        addMarketSegment("FAM_USB");
        addMarketSegment("FAM_DEF");

        service.assignMktSegToNewAMSRules();

        tenantCrudService().flushAndClear();
        List<AnalyticalMarketSegment> allAMSRulesFromDb = getAllAMSRules();
        List<MktSeg> allMktSegs = tenantCrudService().findAll(MktSeg.class);
        assertTrue(allMktSegs.stream().anyMatch(mktSeg -> mktSeg.getCode().equals("FAM_USB") && mktSeg.getPropertyId() == 1));
        assertTrue(allMktSegs.stream().anyMatch(mktSeg -> mktSeg.getCode().equals("FAM_DEF") && mktSeg.getPropertyId() == 1));
        assertTrue(allMktSegs.stream().anyMatch(mktSeg -> mktSeg.getCode().equals("FAM") && mktSeg.getPropertyId() == 5));
        assertTrue(allAMSRulesFromDb.stream().noneMatch(ams -> ams.getMarketCode().equals("FAM") && ams.getRateCode() != null && (ams.getRateCode().equals("RC1") || ams.getRateCode().equals("RC2"))));
        assertTrue(allAMSRulesFromDb.stream().noneMatch(ams -> ams.getMappedMarketCode().equals("FAM_DEF")));
        assertTrue(allAMSRulesFromDb.stream().anyMatch(ams -> ams.getMarketCode().equals("FAM") && ams.getRateCode() == null && ams.getMappedMarketCode().equals("FAM") &&
                ams.getRateCodeType().equals(RateCodeTypeEnum.ALL) && ams.getAttribute().equals(AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR)));
    }

    @Test
    public void deletesExistingAMSRulesThatAreNowConvertedToStraightRules_MSCUnchangedRCRenamed_StraightMSAlreadyPresent() {
        setWorkContextProperty(TestProperty.H1);
        setUpMSRecodingService();
        setUpPMSRevampAMSServiceForRecoding();
        addPMSMigrationMappingWith(CODE_TYPE_MS, "FAM", "FAM", null);
        addPMSMigrationMappingWith(CODE_TYPE_RC, "RC1", "RC3", null);
        addPMSMigrationMappingWith(CODE_TYPE_RC, "RC2", "RC2", null);
        addPMSRevampNewAMSRule("FAM", null, "EQUAL_TO_BAR", "1");
        addAnalyticalMarketSegmentWith("FAM", "RC1", "FAM_USB", "EQUAL_TO_BAR", "EQUALS", "10");
        addAnalyticalMarketSegmentWith("FAM", "RC2", "FAM_USB", "EQUAL_TO_BAR", "EQUALS", "10");
        addAnalyticalMarketSegmentWith("FAM", null, "FAM_DEF", "EQUAL_TO_BAR", "DEFAULT", "999");
        Integer stratightMktSegId = addMarketSegment("FAM");
        addMarketSegment("FAM_USB");
        addMarketSegment("FAM_DEF");

        service.assignMktSegToNewAMSRules();

        tenantCrudService().flushAndClear();
        List<AnalyticalMarketSegment> allAMSRulesFromDb = getAllAMSRules();
        List<MktSeg> allMktSegs = tenantCrudService().findAll(MktSeg.class);
        MktSeg straightMktSeg = tenantCrudService().find(MktSeg.class, stratightMktSegId);
        assertEquals(5, (int) straightMktSeg.getPropertyId());
        assertTrue(allMktSegs.stream().anyMatch(mktSeg -> mktSeg.getCode().equals("FAM_USB") && mktSeg.getPropertyId() == 1));
        assertTrue(allMktSegs.stream().anyMatch(mktSeg -> mktSeg.getCode().equals("FAM_DEF") && mktSeg.getPropertyId() == 1));
        assertTrue(allMktSegs.stream().anyMatch(mktSeg -> mktSeg.getCode().equals("FAM") && mktSeg.getPropertyId() == 5));
        assertTrue(allAMSRulesFromDb.stream().noneMatch(ams -> ams.getMarketCode().equals("FAM") && ams.getRateCode() != null && (ams.getRateCode().equals("RC1") || ams.getRateCode().equals("RC2"))));
        assertTrue(allAMSRulesFromDb.stream().noneMatch(ams -> ams.getMappedMarketCode().equals("FAM_DEF")));
        assertTrue(allAMSRulesFromDb.stream().anyMatch(ams -> ams.getMarketCode().equals("FAM") && ams.getRateCode() == null && ams.getMappedMarketCode().equals("FAM") &&
                ams.getRateCodeType().equals(RateCodeTypeEnum.ALL) && ams.getAttribute().equals(AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR)));
    }

    @Test
    public void deletesExistingAMSRulesThatAreNowConvertedToStraightRules_MSCRenamedRCRenamed_StraightMSAlreadyPresent() {
        setWorkContextProperty(TestProperty.H1);
        setUpMSRecodingService();
        setUpPMSRevampAMSServiceForRecoding();
        addPMSMigrationMappingWith(CODE_TYPE_MS, "FAM", "FAM1", null);
        addPMSMigrationMappingWith(CODE_TYPE_RC, "RC1", "RC3", null);
        addPMSMigrationMappingWith(CODE_TYPE_RC, "RC2", "RC2", null);
        addPMSRevampNewAMSRule("FAM1", null, "EQUAL_TO_BAR", "1");
        addAnalyticalMarketSegmentWith("FAM", "RC1", "FAM_USB", "EQUAL_TO_BAR", "EQUALS", "10");
        addAnalyticalMarketSegmentWith("FAM", "RC2", "FAM_USB", "EQUAL_TO_BAR", "EQUALS", "10");
        addAnalyticalMarketSegmentWith("FAM", null, "FAM_DEF", "EQUAL_TO_BAR", "DEFAULT", "999");
        Integer stratightMktSegId = addMarketSegment("FAM");
        addMarketSegment("FAM_USB");
        addMarketSegment("FAM_DEF");

        service.assignMktSegToNewAMSRules();

        tenantCrudService().flushAndClear();
        List<AnalyticalMarketSegment> allAMSRulesFromDb = getAllAMSRules();
        List<MktSeg> allMktSegs = tenantCrudService().findAll(MktSeg.class);
        MktSeg straightMktSeg = tenantCrudService().find(MktSeg.class, stratightMktSegId);
        assertEquals(5, (int) straightMktSeg.getPropertyId());
        assertTrue(allMktSegs.stream().anyMatch(mktSeg -> mktSeg.getCode().equals("FAM_USB") && mktSeg.getPropertyId() == 5));
        assertTrue(allMktSegs.stream().anyMatch(mktSeg -> mktSeg.getCode().equals("FAM_DEF") && mktSeg.getPropertyId() == 5));
        assertTrue(allAMSRulesFromDb.stream().noneMatch(ams -> ams.getMarketCode().equals("FAM") && ams.getRateCode() != null && (ams.getRateCode().equals("RC1") || ams.getRateCode().equals("RC2"))));
        assertTrue(allAMSRulesFromDb.stream().noneMatch(ams -> ams.getMappedMarketCode().equals("FAM_DEF")));
        assertTrue(allAMSRulesFromDb.stream().anyMatch(ams -> ams.getMarketCode().equals("FAM1") && ams.getRateCode() == null && ams.getMappedMarketCode().equals("FAM1") &&
                ams.getRateCodeType().equals(RateCodeTypeEnum.ALL) && ams.getAttribute().equals(AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR)));
        assertTrue(allAMSRulesFromDb.stream().anyMatch(ams -> ams.getMarketCode().equals("FAM") && ams.getRateCode() == null && ams.getMappedMarketCode().equals("FAM") &&
                ams.getRateCodeType().equals(RateCodeTypeEnum.ALL) && ams.getAttribute().equals(AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR)));
    }

    private PMSRevampAMSDataService setUpPMSRevampAMSServiceForRecoding() {
        tenantCrudService().executeUpdateByNativeQuery(NewAMSRule.CREATE_AMS_RULE_TABLE_DDL);
        PMSRevampAMSDataService pmsRevampAMSDataService = new PMSRevampAMSDataService();
        AnalyticalMarketSegmentService amsService = new AnalyticalMarketSegmentService();
        inject(amsService, "crudService", tenantCrudService());
        inject(amsService, "configParamsServiceLocal", pacmanConfigParamsService);
        inject(pmsRevampAMSDataService, "tenantCrudService", tenantCrudService());
        inject(pmsRevampAMSDataService, "analyticalMarketSegmentService", amsService);
        inject(amsService, "marketSegmentRepository", marketSegmentRepository);
        inject(amsService, "independentProductsService", independentProductsService);
        inject(pmsRevampAMSDataService, "pacmanConfigParamsService", pacmanConfigParamsService);
        inject(service, "pmsRevampAMSDataService", pmsRevampAMSDataService);
        return pmsRevampAMSDataService;
    }

    @Test
    public void deletesExistingAMSRulesThatAreNowConvertedToStraightRules_MSRenamedRCUnchanged() {
        setWorkContextProperty(TestProperty.H1);
        setUpMSRecodingService();
        setUpPMSRevampAMSServiceForRecoding();
        addPMSMigrationMappingWith(CODE_TYPE_MS, "FAM", "FAM1", null);
        addPMSMigrationMappingWith(CODE_TYPE_RC, "RC1", "RC1", null);
        addPMSMigrationMappingWith(CODE_TYPE_RC, "RC2", "RC2", null);
        addPMSRevampNewAMSRule("FAM1", null, "EQUAL_TO_BAR", "1");
        addAnalyticalMarketSegmentWith("FAM", "RC1", "FAM_USB", "EQUAL_TO_BAR", "EQUALS", "10");
        addAnalyticalMarketSegmentWith("FAM", "RC2", "FAM_USB", "EQUAL_TO_BAR", "EQUALS", "10");
        addAnalyticalMarketSegmentWith("FAM", null, "FAM_DEF", "EQUAL_TO_BAR", "DEFAULT", "999");
        addMarketSegment("FAM_USB");
        addMarketSegment("FAM_DEF");

        service.assignMktSegToNewAMSRules();

        tenantCrudService().flushAndClear();
        List<AnalyticalMarketSegment> allAMSRulesFromDb = getAllAMSRules();
        List<MktSeg> allMktSegs = tenantCrudService().findAll(MktSeg.class);
        assertTrue(allMktSegs.stream().anyMatch(mktSeg -> mktSeg.getCode().equals("FAM_USB") && mktSeg.getPropertyId() == 5));
        assertTrue(allMktSegs.stream().anyMatch(mktSeg -> mktSeg.getCode().equals("FAM_DEF") && mktSeg.getPropertyId() == 5));
        assertTrue(allMktSegs.stream().anyMatch(mktSeg -> mktSeg.getCode().equals("FAM1") && mktSeg.getPropertyId() == 5));
        assertTrue(allAMSRulesFromDb.stream().noneMatch(ams -> ams.getMarketCode().equals("FAM") && ams.getRateCode() != null && (ams.getRateCode().equals("RC1") || ams.getRateCode().equals("RC2"))));
        assertTrue(allAMSRulesFromDb.stream().noneMatch(ams -> ams.getMappedMarketCode().equals("FAM_DEF")));
        assertTrue(allAMSRulesFromDb.stream().anyMatch(ams -> ams.getMarketCode().equals("FAM1") && ams.getRateCode() == null && ams.getMappedMarketCode().equals("FAM1") &&
                ams.getRateCodeType().equals(RateCodeTypeEnum.ALL) && ams.getAttribute().equals(AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR)));
        assertTrue(allAMSRulesFromDb.stream().anyMatch(ams -> ams.getMarketCode().equals("FAM") && ams.getRateCode() == null && ams.getMappedMarketCode().equals("FAM") &&
                ams.getRateCodeType().equals(RateCodeTypeEnum.ALL) && ams.getAttribute().equals(AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR)));
    }

    @Test
    public void deletesExistingAMSRulesThatAreNowConvertedToStraightRules_MSAndRCRenamed() {
        setWorkContextProperty(TestProperty.H1);
        setUpMSRecodingService();
        setUpPMSRevampAMSServiceForRecoding();
        addPMSMigrationMappingWith(CODE_TYPE_MS, "FAM", "FAM1", null);
        addPMSMigrationMappingWith(CODE_TYPE_RC, "RC1", "RC3", null);
        addPMSMigrationMappingWith(CODE_TYPE_RC, "RC2", "RC2", null);
        addPMSRevampNewAMSRule("FAM1", null, "EQUAL_TO_BAR", "1");
        addAnalyticalMarketSegmentWith("FAM", "RC1", "FAM_USB", "EQUAL_TO_BAR", "EQUALS", "10");
        addAnalyticalMarketSegmentWith("FAM", "RC2", "FAM_USB", "EQUAL_TO_BAR", "EQUALS", "10");
        addAnalyticalMarketSegmentWith("FAM", null, "FAM_DEF", "EQUAL_TO_BAR", "DEFAULT", "999");
        addMarketSegment("FAM_USB");
        addMarketSegment("FAM_DEF");

        service.assignMktSegToNewAMSRules();

        tenantCrudService().flushAndClear();
        List<AnalyticalMarketSegment> allAMSRulesFromDb = getAllAMSRules();
        List<MktSeg> allMktSegs = tenantCrudService().findAll(MktSeg.class);
        assertTrue(allMktSegs.stream().anyMatch(mktSeg -> mktSeg.getCode().equals("FAM_USB") && mktSeg.getPropertyId() == 5));
        assertTrue(allMktSegs.stream().anyMatch(mktSeg -> mktSeg.getCode().equals("FAM_DEF") && mktSeg.getPropertyId() == 5));
        assertTrue(allMktSegs.stream().anyMatch(mktSeg -> mktSeg.getCode().equals("FAM1") && mktSeg.getPropertyId() == 5));
        assertTrue(allAMSRulesFromDb.stream().noneMatch(ams -> ams.getMarketCode().equals("FAM") && ams.getRateCode() != null && (ams.getRateCode().equals("RC1") || ams.getRateCode().equals("RC2"))));
        assertTrue(allAMSRulesFromDb.stream().noneMatch(ams -> ams.getMappedMarketCode().equals("FAM_DEF")));
        assertTrue(allAMSRulesFromDb.stream().anyMatch(ams -> ams.getMarketCode().equals("FAM1") && ams.getRateCode() == null && ams.getMappedMarketCode().equals("FAM1") &&
                ams.getRateCodeType().equals(RateCodeTypeEnum.ALL) && ams.getAttribute().equals(AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR)));
        assertTrue(allAMSRulesFromDb.stream().anyMatch(ams -> ams.getMarketCode().equals("FAM") && ams.getRateCode() == null && ams.getMappedMarketCode().equals("FAM") &&
                ams.getRateCodeType().equals(RateCodeTypeEnum.ALL) && ams.getAttribute().equals(AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR)));
    }


    @Test
    public void assignMktSegToNewAMSRules_shouldNotReturnAnyMktSegIdWithCompleteAttributionChange() {
        //Given
        setUpMSRecodingService();
        final PMSRevampAMSDataService testPMSRevampAMSDataService = setUpPMSRevampAMSServiceForRecoding();

        addMarketSegment(MS1_QYL_MARKET_CODE);
        addMarketSegment(MS1_STRAIGHT_MARKET_CODE);

        addPMSMigrationMappingWith(CODE_TYPE_MS, MS1_MARKET_CODE, MS1_MARKET_CODE, null);
        addPMSMigrationMappingWith(CODE_TYPE_MS, MS1_STRAIGHT_MARKET_CODE, MS1_STRAIGHT_MARKET_CODE, null);
        addPMSMigrationMappingWith(CODE_TYPE_RC, RC1_RATE_CODE, RC1_RATE_CODE, null);
        addPMSMigrationMappingWith(CODE_TYPE_RC, RC2_RATE_CODE, RC2_RATE_CODE, null);

        //AMS: non-default, non-straight
        final AnalyticalMarketSegment ms1rc1QYL = getAnalyticalMarketSegment(MS1_MARKET_CODE, RC1_RATE_CODE, MS1_QYL_MARKET_CODE, AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE, RateCodeTypeEnum.EQUALS, RANK_10, false);
        final AnalyticalMarketSegment ms1rc2QYL = getAnalyticalMarketSegment(MS1_MARKET_CODE, RC2_RATE_CODE, MS1_QYL_MARKET_CODE, AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE, RateCodeTypeEnum.EQUALS, RANK_10, false);

        //AMS: Straight
        final AnalyticalMarketSegment ms1StraightAMS = getAnalyticalMarketSegment(MS1_STRAIGHT_MARKET_CODE, null, MS1_STRAIGHT_MARKET_CODE, AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE, RateCodeTypeEnum.ALL, 1, false);

        tenantCrudService().save(Arrays.asList(ms1rc1QYL, ms1rc2QYL, ms1StraightAMS));

        final NewAMSRule ms1rc1USBNewRule = getNewAMSRule(MS1_MARKET_CODE, RC1_RATE_CODE, AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR, ForecastActivityType.DEMAND_AND_WASH);
        final NewAMSRule ms1rc2USBNewRule = getNewAMSRule(MS1_MARKET_CODE, RC2_RATE_CODE, AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR, ForecastActivityType.DEMAND_AND_WASH);

        final NewAMSRule ms1StraightNewRule = getNewAMSRule(MS1_STRAIGHT_MARKET_CODE, null, AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR, ForecastActivityType.DEMAND_AND_WASH);


        testPMSRevampAMSDataService.saveNewAMSRules(Arrays.asList(ms1rc1USBNewRule, ms1rc2USBNewRule, ms1StraightNewRule));

        //When
        List<Integer> mktSegIds = service.assignMktSegToNewAMSRules();

        //Then
        assertTrue(mktSegIds.isEmpty());

    }

    @Test
    public void assignMktSegToNewAMSRules_shouldNotReturnMktSegIdsWithOnlyForecastTypeChange() {
        //Given
        setUpMSRecodingService();
        final PMSRevampAMSDataService testPMSRevampAMSDataService = setUpPMSRevampAMSServiceForRecoding();

        addMarketSegment(MS1_QYL_MARKET_CODE);

        addPMSMigrationMappingWith(CODE_TYPE_MS, MS1_MARKET_CODE, MS1_MARKET_CODE, null);
        addPMSMigrationMappingWith(CODE_TYPE_RC, RC1_RATE_CODE, RC1_RATE_CODE, null);
        addPMSMigrationMappingWith(CODE_TYPE_RC, RC2_RATE_CODE, RC2_RATE_CODE, null);

        final AnalyticalMarketSegmentSummary ms1rc1QYL = getAnalyticalMarketSegmentSummary(MS1_MARKET_CODE, RC1_RATE_CODE, MS1_QYL_MARKET_CODE, RateCodeTypeEnum.EQUALS);

        final AnalyticalMarketSegmentSummary ms1rc2QYL = getAnalyticalMarketSegmentSummary(MS1_MARKET_CODE, RC2_RATE_CODE, MS1_QYL_MARKET_CODE, RateCodeTypeEnum.EQUALS);

        analyticalMarketSegmentService.assignMarketSegments(ms1rc1QYL, AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE, ForecastActivityType.DEMAND_AND_WASH, true, false, StringUtils.EMPTY);
        analyticalMarketSegmentService.assignMarketSegments(ms1rc2QYL, AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE, ForecastActivityType.DEMAND_AND_WASH, true, false, StringUtils.EMPTY);

        final NewAMSRule ms1rc1QYLNewRule = getNewAMSRule(MS1_MARKET_CODE, RC1_RATE_CODE, AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE, ForecastActivityType.WASH);
        final NewAMSRule ms1rc2QYLNewRule = getNewAMSRule(MS1_MARKET_CODE, RC2_RATE_CODE, AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE, ForecastActivityType.WASH);

        testPMSRevampAMSDataService.saveNewAMSRules(Arrays.asList(ms1rc1QYLNewRule, ms1rc2QYLNewRule));

        //When
        List<Integer> mktSegIds = service.assignMktSegToNewAMSRules();

        //Then
        assertTrue(mktSegIds.isEmpty());

    }

    @Test
    public void assignMktSegToNewAMSRules_shouldReturnMktSegIdsWithPartialAttributionToNewAMS() {
        //Given
        setUpMSRecodingService();
        final PMSRevampAMSDataService testPMSRevampAMSDataService = setUpPMSRevampAMSServiceForRecoding();

        final Integer ms1QYLId = addMarketSegment(MS1_QYL_MARKET_CODE);

        addPMSMigrationMappingWith(CODE_TYPE_MS, MS1_MARKET_CODE, MS1_MARKET_CODE, null);
        addPMSMigrationMappingWith(CODE_TYPE_RC, RC1_RATE_CODE, RC1_RATE_CODE, null);
        addPMSMigrationMappingWith(CODE_TYPE_RC, RC2_RATE_CODE, RC2_RATE_CODE, null);

        final AnalyticalMarketSegment ms1rc1QYL = getAnalyticalMarketSegment(MS1_MARKET_CODE, RC1_RATE_CODE, MS1_QYL_MARKET_CODE, AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE, RateCodeTypeEnum.EQUALS, RANK_10, false);
        final AnalyticalMarketSegment ms1rc2QYL = getAnalyticalMarketSegment(MS1_MARKET_CODE, RC2_RATE_CODE, MS1_QYL_MARKET_CODE, AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE, RateCodeTypeEnum.EQUALS, RANK_10, false);

        tenantCrudService().save(Arrays.asList(ms1rc1QYL, ms1rc2QYL));

        final NewAMSRule ms1rc1QYLNewRule = getNewAMSRule(MS1_MARKET_CODE, RC1_RATE_CODE, AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE, ForecastActivityType.DEMAND_AND_WASH);
        final NewAMSRule ms1rc2USBNewRule = getNewAMSRule(MS1_MARKET_CODE, RC2_RATE_CODE, AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR, ForecastActivityType.DEMAND_AND_WASH);

        testPMSRevampAMSDataService.saveNewAMSRules(Arrays.asList(ms1rc1QYLNewRule, ms1rc2USBNewRule));

        //When
        List<Integer> mktSegIds = service.assignMktSegToNewAMSRules();

        //Then
        assertFalse(mktSegIds.isEmpty());
        assertEquals(1, mktSegIds.size());
        assertTrue(mktSegIds.contains(ms1QYLId));

    }

    @Test
    public void assignMktSegToNewAMSRules_shouldReturnMktSegIdsWithPartialAttributionToExistingAMS() {
        //Given
        setUpMSRecodingService();
        final PMSRevampAMSDataService testPMSRevampAMSDataService = setUpPMSRevampAMSServiceForRecoding();
        final Integer ms1QYLId = addMarketSegment(MS1_QYL_MARKET_CODE);
        final Integer ms1USBId = addMarketSegment(MS1_USB_MARKET_CODE);

        addPMSMigrationMappingWith(CODE_TYPE_MS, MS1_MARKET_CODE, MS1_MARKET_CODE, null);
        addPMSMigrationMappingWith(CODE_TYPE_RC, RC1_RATE_CODE, RC1_RATE_CODE, null);
        addPMSMigrationMappingWith(CODE_TYPE_RC, RC2_RATE_CODE, RC2_RATE_CODE, null);

        final AnalyticalMarketSegment ms1rc1QYL = getAnalyticalMarketSegment(MS1_MARKET_CODE, RC1_RATE_CODE, MS1_QYL_MARKET_CODE, AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE, RateCodeTypeEnum.EQUALS, RANK_10, false);
        final AnalyticalMarketSegment ms1rc2QYL = getAnalyticalMarketSegment(MS1_MARKET_CODE, RC2_RATE_CODE, MS1_QYL_MARKET_CODE, AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE, RateCodeTypeEnum.EQUALS, RANK_10, false);
        final AnalyticalMarketSegment ms1rc3USB = getAnalyticalMarketSegment(MS1_MARKET_CODE, RC3_RATE_CODE, MS1_USB_MARKET_CODE, AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR, RateCodeTypeEnum.EQUALS, RANK_10, false);

        tenantCrudService().save(Arrays.asList(ms1rc1QYL, ms1rc2QYL, ms1rc3USB));

        final NewAMSRule ms1rc1QYLNewRule = getNewAMSRule(MS1_MARKET_CODE, RC1_RATE_CODE, AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE, ForecastActivityType.DEMAND_AND_WASH);
        final NewAMSRule ms1rc2USBNewRule = getNewAMSRule(MS1_MARKET_CODE, RC2_RATE_CODE, AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR, ForecastActivityType.DEMAND_AND_WASH);

        testPMSRevampAMSDataService.saveNewAMSRules(Arrays.asList(ms1rc1QYLNewRule, ms1rc2USBNewRule));

        //When
        List<Integer> mktSegIds = service.assignMktSegToNewAMSRules();

        //Then
        assertFalse(mktSegIds.isEmpty());
        assertEquals(2, mktSegIds.size());
        assertTrue(mktSegIds.containsAll(Arrays.asList(ms1QYLId, ms1USBId)));

    }

    @Test
    public void assignMktSegToNewAMSRules_shouldReturnMktSegIdsWhenLastRateCodeMovedToExistingAMS() {
        //Given
        setUpMSRecodingService();
        final PMSRevampAMSDataService testPMSRevampAMSDataService = setUpPMSRevampAMSServiceForRecoding();

        addMarketSegment(MS1_QYL_MARKET_CODE);
        final Integer ms1USBId = addMarketSegment(MS1_USB_MARKET_CODE);

        addPMSMigrationMappingWith(CODE_TYPE_MS, MS1_MARKET_CODE, MS1_MARKET_CODE, null);
        addPMSMigrationMappingWith(CODE_TYPE_RC, RC1_RATE_CODE, RC1_RATE_CODE, null);
        addPMSMigrationMappingWith(CODE_TYPE_RC, RC2_RATE_CODE, RC2_RATE_CODE, null);

        final AnalyticalMarketSegment ms1rc1QYL = getAnalyticalMarketSegment(MS1_MARKET_CODE, RC1_RATE_CODE, MS1_QYL_MARKET_CODE, AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE, RateCodeTypeEnum.EQUALS, RANK_10, false);
        final AnalyticalMarketSegment ms1rc2USB = getAnalyticalMarketSegment(MS1_MARKET_CODE, RC2_RATE_CODE, MS1_USB_MARKET_CODE, AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR, RateCodeTypeEnum.EQUALS, RANK_10, false);

        tenantCrudService().save(Arrays.asList(ms1rc1QYL, ms1rc2USB));

        final NewAMSRule ms1rc1USBNewRule = getNewAMSRule(MS1_MARKET_CODE, RC1_RATE_CODE, AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR, ForecastActivityType.DEMAND_AND_WASH);

        testPMSRevampAMSDataService.saveNewAMSRules(Collections.singletonList(ms1rc1USBNewRule));

        //When
        List<Integer> mktSegIds = service.assignMktSegToNewAMSRules();

        //Then
        assertFalse(mktSegIds.isEmpty());
        assertEquals(1, mktSegIds.size());
        assertTrue(mktSegIds.containsAll(Collections.singletonList(ms1USBId)));

    }

    @Test
    public void assignMktSegToNewAMSRules_shouldReturnMktSegIdsWhenNewRateCodeAddedToExistingAMS() {
        //Given
        setUpMSRecodingService();
        final PMSRevampAMSDataService testPMSRevampAMSDataService = setUpPMSRevampAMSServiceForRecoding();

        final Integer ms1QYLId = addMarketSegment(MS1_QYL_MARKET_CODE);

        addPMSMigrationMappingWith(CODE_TYPE_MS, MS1_MARKET_CODE, MS1_MARKET_CODE, null);
        addPMSMigrationMappingWith(CODE_TYPE_RC, RC1_RATE_CODE, RC1_RATE_CODE, null);
        addPMSMigrationMappingWith(CODE_TYPE_RC, RC2_RATE_CODE, RC2_RATE_CODE, null);

        final AnalyticalMarketSegment ms1rc1QYL = getAnalyticalMarketSegment(MS1_MARKET_CODE, RC1_RATE_CODE, MS1_QYL_MARKET_CODE, AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE, RateCodeTypeEnum.EQUALS, RANK_10, false);
        final AnalyticalMarketSegment ms1rc2QYL = getAnalyticalMarketSegment(MS1_MARKET_CODE, RC2_RATE_CODE, MS1_QYL_MARKET_CODE, AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE, RateCodeTypeEnum.EQUALS, RANK_10, false);

        tenantCrudService().save(Arrays.asList(ms1rc1QYL, ms1rc2QYL));

        final NewAMSRule ms1rc1QYLNewRule = getNewAMSRule(MS1_MARKET_CODE, RC1_RATE_CODE, AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE, ForecastActivityType.DEMAND_AND_WASH);
        final NewAMSRule ms1rc2QYLNewRule = getNewAMSRule(MS1_MARKET_CODE, RC2_RATE_CODE, AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE, ForecastActivityType.DEMAND_AND_WASH);
        final NewAMSRule ms1rc3QYLNewRule = getNewAMSRule(MS1_MARKET_CODE, RC3_RATE_CODE, AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE, ForecastActivityType.DEMAND_AND_WASH);

        testPMSRevampAMSDataService.saveNewAMSRules(Arrays.asList(ms1rc1QYLNewRule, ms1rc2QYLNewRule, ms1rc3QYLNewRule));

        //When
        List<Integer> mktSegIds = service.assignMktSegToNewAMSRules();

        //Then
        assertFalse(mktSegIds.isEmpty());
        assertEquals(1, mktSegIds.size());
        assertTrue(mktSegIds.containsAll(Collections.singletonList(ms1QYLId)));

    }

    @Test
    public void assignMktSegToNewAMSRules_MktSegRateCodeRename_ShouldNotReturnMktSegIdsForPaceBackFill() {
        setUpServiceForAssigningMSToNewAMSRulesWithNoAttributeChange();

        addPMSMigrationMappingWith(CODE_TYPE_MS, "CORP", "CORP-NEW", null);
        addPMSMigrationMappingWith(CODE_TYPE_RC, "RC4", "RC4", null);

        addPMSMigrationMappingWith(CODE_TYPE_MS, "FAM", "FAM", null);
        addPMSMigrationMappingWith(CODE_TYPE_RC, RC1_RATE_CODE, RC3_RATE_CODE, null);
        addPMSMigrationMappingWith(CODE_TYPE_RC, RC2_RATE_CODE, RC2_RATE_CODE, null);

        addAnalyticalMarketSegmentWith("CORP", "RC4", "CORP_QYL", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "EQUALS", "10");
        addAnalyticalMarketSegmentWith("CORP", null, "CORP_DEF", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "DEFAULT", "999");

        addAnalyticalMarketSegmentWith("FAM", RC1_RATE_CODE, "FAM_QYL", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "EQUALS", "10");
        addAnalyticalMarketSegmentWith("FAM", RC2_RATE_CODE, "FAM_QYL", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "EQUALS", "10");
        addAnalyticalMarketSegmentWith("FAM", null, "FAM_DEF", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "DEFAULT", "999");

        addPMSRevampNewAMSRule("CORP-NEW", "RC4", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");
        addPMSRevampNewAMSRule("CORP-NEW", "DEFAULT", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");

        addPMSRevampNewAMSRule("FAM", RC3_RATE_CODE, QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");
        addPMSRevampNewAMSRule("FAM", RC2_RATE_CODE, QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");
        addPMSRevampNewAMSRule("FAM", "DEFAULT", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");

        addMarketSegment("CORP_QYL");
        addMarketSegment("CORP_DEF");

        addMarketSegment("FAM_QYL");
        addMarketSegment("FAM_DEF");

        addMktSegMaster("CORP_QYL", 1);
        addMktSegMaster("CORP_DEF", 1);

        addMktSegMaster("FAM_QYL", 1);
        addMktSegMaster("FAM_DEF", 1);

        final List<Integer> mktSegIdsForPaceBackFill = service.assignMktSegToNewAMSRules();
        assertTrue(mktSegIdsForPaceBackFill.isEmpty());

    }


    @Test
    @Disabled
    public void assignMktSegToNewAMSRules_ShouldReturnMktSegIdsForPaceBackFill() {
        setUpServiceForAssigningMSToNewAMSRulesWithNoAttributeChange();

        addPMSMigrationMappingWith(CODE_TYPE_MS, "BAR 3-6 NIGHTS", "BAR 3-6 NIGHTS", null);
        addPMSMigrationMappingWith(CODE_TYPE_MS, "Longstay", "BAR 3-6 NIGHTS", null);

        addAnalyticalMarketSegmentWith("BAR 3-6 Nights", null, "BAR 3-6 Nights", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "ALL", "1");
        addAnalyticalMarketSegmentWith("Longstay", null, "BAR 3-6 NIGHTS", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "ALL", "1");

        addPMSRevampNewAMSRule("BAR 3-6 NIGHTS", null, QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");
        addPMSRevampNewAMSRule("Longstay", null, QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");

        Integer bar3_6Nights_MktSegId = addMarketSegment("BAR 3-6 Nights");
        addMarketSegment("Longstay");

        addMktSegMaster("BAR 3-6 Nights", 1);
        addMktSegMaster("Longstay", 1);

        final List<Integer> mktSegIdsForPaceBackFill = service.assignMktSegToNewAMSRules();
        assertTrue(isNotEmpty(mktSegIdsForPaceBackFill));
        assertTrue(mktSegIdsForPaceBackFill.contains(bar3_6Nights_MktSegId));

    }

    @Test
    public void assignMktSegToNewAMSRules_ShouldReturnMktSegIdsForPaceBackFillStraightMergeScenario() {
        setUpServiceForAssigningMSToNewAMSRulesWithNoAttributeChange();

        addPMSMigrationMappingWith(CODE_TYPE_MS, "BAR 3-6 NIGHTS", "BAR 3-6 NIGHTS", null);
        addPMSMigrationMappingWith(CODE_TYPE_MS, "Longstay", "BAR 3-6 NIGHTS", null);

        addAnalyticalMarketSegmentWith("BAR 3-6 NIGHTS", null, "BAR 3-6 NIGHTS", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "ALL", "1");
        addAnalyticalMarketSegmentWith("Longstay", null, "Longstay", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "ALL", "1");

        addPMSRevampNewAMSRule("BAR 3-6 NIGHTS", null, QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");
        addPMSRevampNewAMSRule("BAR 3-6 NIGHTS", null, QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");

        Integer bar3_6Nights_MktSegId = addMarketSegment("BAR 3-6 NIGHTS");
        addMarketSegment("Longstay");

        addMktSegMaster("BAR 3-6 NIGHTS", 1);
        addMktSegMaster("Longstay", 1);

        final List<Integer> mktSegIdsForPaceBackFill = service.getMktSegIdsEligibleForPaceBackFill();
        assertTrue(isNotEmpty(mktSegIdsForPaceBackFill));
        assertTrue(mktSegIdsForPaceBackFill.contains(bar3_6Nights_MktSegId));

    }

    @Test
    public void assignMktSegToNewAMSRules_ShouldReturnMktSegIdsForPaceBackFillSplitScenario() {
        setUpServiceForAssigningMSToNewAMSRulesWithNoAttributeChange();

        addPMSMigrationMappingWith(CODE_TYPE_MS, "CORP", "Corp", null);
        addPMSMigrationMappingWith(CODE_TYPE_RC, "RC4", "RC4", null);

        addPMSMigrationMappingWith(CODE_TYPE_MS, "FAM", "FAM", null);
        addPMSMigrationMappingWith(CODE_TYPE_RC, RC1_RATE_CODE, RC3_RATE_CODE, null);
        addPMSMigrationMappingWith(CODE_TYPE_RC, RC2_RATE_CODE, RC2_RATE_CODE, null);

        addAnalyticalMarketSegmentWith("CORP", "RC4", "CORP_QYL", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "EQUALS", "10");
        addAnalyticalMarketSegmentWith("CORP", null, "CORP_DEF", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "DEFAULT", "999");

        addAnalyticalMarketSegmentWith("FAM", RC1_RATE_CODE, "FAM_QYL", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "EQUALS", "10");
        addAnalyticalMarketSegmentWith("FAM", RC2_RATE_CODE, "FAM_QYL", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "EQUALS", "10");
        addAnalyticalMarketSegmentWith("FAM", null, "FAM_DEF", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "DEFAULT", "999");

        addPMSRevampNewAMSRule("Corp", "RC4", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");
        addPMSRevampNewAMSRule("Corp", "RC5", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");
        addPMSRevampNewAMSRule("Corp", "DEFAULT", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");

        addPMSRevampNewAMSRule("FAM", RC3_RATE_CODE, QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");
        addPMSRevampNewAMSRule("FAM", RC2_RATE_CODE, QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");
        addPMSRevampNewAMSRule("FAM", "DEFAULT", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");

        int corp_qyl_mktSegId = addMarketSegment("CORP_QYL");
        addMarketSegment("CORP_DEF");

        addMarketSegment("FAM_QYL");
        addMarketSegment("FAM_DEF");

        addMktSegMaster("CORP_QYL", 1);
        addMktSegMaster("CORP_DEF", 1);

        addMktSegMaster("FAM_QYL", 1);
        addMktSegMaster("FAM_DEF", 1);

        final List<Integer> mktSegIdsForPaceBackFill = service.getMktSegIdsEligibleForPaceBackFill();
        assertTrue(isNotEmpty(mktSegIdsForPaceBackFill));
        assertTrue(mktSegIdsForPaceBackFill.contains(corp_qyl_mktSegId));

    }

    @Test
    public void getMktSegRecodingAMSSummaryByMappedMarketCodeForNewAMSRulesTest() {
        setUpServiceForAssigningMSToNewAMSRulesWithNoAttributeChange();

        addPMSMigrationMappingWith(CODE_TYPE_MS, "CORP", "Corp", null);
        addPMSMigrationMappingWith(CODE_TYPE_RC, "RC4", "rc4", null);

        addPMSMigrationMappingWith(CODE_TYPE_MS, "FAM", "FAM", null);
        addPMSMigrationMappingWith(CODE_TYPE_RC, RC1_RATE_CODE, RC3_RATE_CODE, null);
        addPMSMigrationMappingWith(CODE_TYPE_RC, RC2_RATE_CODE, RC2_RATE_CODE, null);

        addAnalyticalMarketSegmentWith("CORP", "RC4", "CORP_QYL", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "EQUALS", "10");
        addAnalyticalMarketSegmentWith("CORP", null, "CORP_DEF", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "DEFAULT", "999");

        addAnalyticalMarketSegmentWith("FAM", RC1_RATE_CODE, "FAM_QYL", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "EQUALS", "10");
        addAnalyticalMarketSegmentWith("FAM", RC2_RATE_CODE, "FAM_QYL", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "EQUALS", "10");
        addAnalyticalMarketSegmentWith("FAM", null, "FAM_DEF", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "DEFAULT", "999");


        addPMSRevampNewAMSRule("Corp", "rc4", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");
        addPMSRevampNewAMSRule("Corp", "RC5", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");
        addPMSRevampNewAMSRule("Corp", "DEFAULT", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");

        addPMSRevampNewAMSRule("FAM", RC3_RATE_CODE, QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");
        addPMSRevampNewAMSRule("FAM", RC2_RATE_CODE, QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");
        addPMSRevampNewAMSRule("FAM", "DEFAULT", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");


        service.handleRenamedRateCodesWithNoMarketCodeOrAttributeChange();
        final List<Object[]> renamedRateCodesNoMarketCodesChange = tenantCrudService().findByNativeQuery("select market_code, mapped_market_code, rate_code " +
                "from Analytical_Mkt_Seg where preserved=1");
        assertEquals(1, renamedRateCodesNoMarketCodesChange.size());
        assertEquals("FAM", renamedRateCodesNoMarketCodesChange.get(0)[0]);
    }

    @Test
    public void shouldRenameProductRateCodeMappingsForOldRateCodesWhenNewRateCodesAreNotPresent() {
        setUpServiceForAssigningMSToNewAMSRulesWithNoAttributeChange();
        injectIPService();
        addPMSMigrationMappingWith(CODE_TYPE_MS, "CORP", "Corp", null);
        addPMSMigrationMappingWith(CODE_TYPE_RC, "RC4", "rc4-CS", null);
        addPMSMigrationMappingWith(CODE_TYPE_RC, "RC6", "rc6-CS", null);
        addPMSMigrationMappingWith(CODE_TYPE_MS, "FAM", "FAM", null);
        addPMSMigrationMappingWith(CODE_TYPE_RC, RC1_RATE_CODE, RC3_RATE_CODE, null);
        addPMSMigrationMappingWith(CODE_TYPE_RC, RC2_RATE_CODE, RC2_RATE_CODE, null);
        addAnalyticalMarketSegmentWith("CORP", "RC4", "CORP_QYL", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "EQUALS", "10");
        addAnalyticalMarketSegmentWith("CORP", "RC6", "CORP_QYL", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "EQUALS", "10");
        addAnalyticalMarketSegmentWith("CORP", null, "CORP_DEF", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "DEFAULT", "999");
        addAnalyticalMarketSegmentWith("FAM", RC1_RATE_CODE, "FAM_QYL", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "EQUALS", "10");
        addAnalyticalMarketSegmentWith("FAM", RC2_RATE_CODE, "FAM_QYL", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "EQUALS", "10");
        addAnalyticalMarketSegmentWith("FAM", null, "FAM_DEF", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "DEFAULT", "999");
        addPMSRevampNewAMSRule("Corp", "rc4-CS", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");
        addPMSRevampNewAMSRule("Corp", "rc6-CS", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");
        addPMSRevampNewAMSRule("Corp", "RC5", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");
        addPMSRevampNewAMSRule("Corp", "DEFAULT", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");
        addPMSRevampNewAMSRule("FAM", RC3_RATE_CODE, QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");
        addPMSRevampNewAMSRule("FAM", RC2_RATE_CODE, QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");
        addPMSRevampNewAMSRule("FAM", "DEFAULT", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");
        Product product1 = addProduct("IND1");
        Product product2 = addProduct("IND2");
        ProductRateCode prc1 = createProductRateCode(product1, "RC4");
        ProductRateCode prc2 = createProductRateCode(product2, "RC4");
        ProductRateCode prc3 = createProductRateCode(product1, "RC6");
        ProductRateCode prc4 = createProductRateCode(product2, "RC6");
        tenantCrudService().save(List.of(prc1, prc2, prc3, prc4));
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_MS_RECODING_SUPPORT_FOR_INDEPENDENT_PRODUCT)).thenReturn(true);
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.INDEPENDENT_PRODUCTS_ENABLED)).thenReturn(true);

        service.handleRenamedRateCodesWithNoMarketCodeOrAttributeChange();

        assertEquals("rc4-CS", prc1.getRateCode());
        assertEquals("rc4-CS", prc2.getRateCode());
        assertEquals("rc6-CS", prc3.getRateCode());
        assertEquals("rc6-CS", prc4.getRateCode());
        List<Object[]> renamedRateCodesNoMarketCodesChange = tenantCrudService().findByNativeQuery("select market_code, mapped_market_code, rate_code " +
                "from Analytical_Mkt_Seg where preserved=1");
        assertEquals(1, renamedRateCodesNoMarketCodesChange.size());
        assertEquals("FAM", renamedRateCodesNoMarketCodesChange.get(0)[0]);
    }

    private static ProductRateCode createProductRateCode(Product product1, String rc) {
        ProductRateCode prc = new ProductRateCode();
        prc.setProduct(product1);
        prc.setRateCode(rc);
        return prc;
    }

    @Test
    public void shouldRemoveProductRateCodeMappingsForOldRateCodesWhenNewRateCodesArePresent() {
        setUpServiceForAssigningMSToNewAMSRulesWithNoAttributeChange();
        injectIPService();
        addPMSMigrationMappingWith(CODE_TYPE_MS, "CORP", "Corp", null);
        addPMSMigrationMappingWith(CODE_TYPE_RC, "RC4", "rc4-CS", null);
        addPMSMigrationMappingWith(CODE_TYPE_RC, "RC6", "rc6-CS", null);
        addPMSMigrationMappingWith(CODE_TYPE_MS, "FAM", "FAM", null);
        addPMSMigrationMappingWith(CODE_TYPE_RC, RC1_RATE_CODE, RC3_RATE_CODE, null);
        addPMSMigrationMappingWith(CODE_TYPE_RC, RC2_RATE_CODE, RC2_RATE_CODE, null);
        addAnalyticalMarketSegmentWith("CORP", "RC4", "CORP_QYL", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "EQUALS", "10");
        addAnalyticalMarketSegmentWith("CORP", "RC6", "CORP_QYL", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "EQUALS", "10");
        addAnalyticalMarketSegmentWith("CORP", null, "CORP_DEF", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "DEFAULT", "999");
        addAnalyticalMarketSegmentWith("FAM", RC1_RATE_CODE, "FAM_QYL", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "EQUALS", "10");
        addAnalyticalMarketSegmentWith("FAM", RC2_RATE_CODE, "FAM_QYL", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "EQUALS", "10");
        addAnalyticalMarketSegmentWith("FAM", null, "FAM_DEF", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "DEFAULT", "999");
        addPMSRevampNewAMSRule("Corp", "rc4-CS", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");
        addPMSRevampNewAMSRule("Corp", "rc6-CS", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");
        addPMSRevampNewAMSRule("Corp", "RC5", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");
        addPMSRevampNewAMSRule("Corp", "DEFAULT", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");
        addPMSRevampNewAMSRule("FAM", RC3_RATE_CODE, QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");
        addPMSRevampNewAMSRule("FAM", RC2_RATE_CODE, QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");
        addPMSRevampNewAMSRule("FAM", "DEFAULT", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");
        Product product1 = addProduct("IND1");
        Product product2 = addProduct("IND2");
        ProductRateCode prc1 = createProductRateCode(product1, "RC4");
        ProductRateCode prc2 = createProductRateCode(product2, "RC4");
        ProductRateCode prc3 = createProductRateCode(product1, "RC6");
        ProductRateCode prc4 = createProductRateCode(product2, "RC6");
        ProductRateCode prc5 = createProductRateCode(product2, "rc6-CS");
        tenantCrudService().save(List.of(prc1, prc2, prc3, prc4, prc5));
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_MS_RECODING_SUPPORT_FOR_INDEPENDENT_PRODUCT)).thenReturn(true);
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.INDEPENDENT_PRODUCTS_ENABLED)).thenReturn(true);

        service.handleRenamedRateCodesWithNoMarketCodeOrAttributeChange();

        List<ProductRateCode> productRateCodes = tenantCrudService().findAll(ProductRateCode.class);
        productRateCodes.sort(Comparator.comparing(ProductRateCode::getRateCode));
        assertEquals(4, productRateCodes.size());
        assertEquals("rc4-CS", productRateCodes.get(0).getRateCode());
        assertEquals("rc4-CS", productRateCodes.get(1).getRateCode());
        assertEquals("rc6-CS", productRateCodes.get(2).getRateCode());
        assertEquals("rc6-CS", productRateCodes.get(3).getRateCode());
        assertEquals(prc5.getId(), productRateCodes.get(3).getId());
        List<Object[]> renamedRateCodesNoMarketCodesChange = tenantCrudService().findByNativeQuery("select market_code, mapped_market_code, rate_code " +
                "from Analytical_Mkt_Seg where preserved=1");
        assertEquals(1, renamedRateCodesNoMarketCodesChange.size());
        assertEquals("FAM", renamedRateCodesNoMarketCodesChange.get(0)[0]);
    }

    private void injectIPService() {
        AgileRatesConfigurationService agileRatesConfigurationService = mock(AgileRatesConfigurationService.class);
        when(agileRatesConfigurationService.findAllProducts()).thenReturn(new ArrayList<>());
        IndependentProductsService independentProductsService = new IndependentProductsService();
        IndependentProductsRepository independentProductsRepository = new IndependentProductsRepository();
        inject(independentProductsRepository, "tenantCrudService", tenantCrudService());
        inject(independentProductsService, "repository", independentProductsRepository);
        inject(independentProductsService, "agileRatesConfigurationService", agileRatesConfigurationService);
        inject(service, "independentProductsRepository", independentProductsRepository);
        inject(service, "independentProductsService", independentProductsService);
    }

    private ProductRateCode getProductRateCodeMappingCountFor(String rateCode) {
        return tenantCrudService().findByNativeQuerySingleResult("select top 1 * from Product_Rate_Code " +
                "where rate_code COLLATE SQL_Latin1_General_CP1_CS_AS = '" + rateCode + "' COLLATE SQL_Latin1_General_CP1_CS_AS", Map.of());
    }

    private Integer getMarketSegmentProductMappingCountFor(String mktSegCode) {
        return tenantCrudService().findByNativeQuerySingleResult("select count(*) from Mkt_Seg_Product_Mapping where Mkt_Seg_Code = '" + mktSegCode + "'", Map.of());
    }

    @Test
    public void getMktSegRecodingAMSSummaryByMappedMktCodeOfExistingAMSRulesTest() {
        setUpServiceForAssigningMSToNewAMSRulesWithNoAttributeChange();

        addAnalyticalMarketSegmentWith("Corp", "RC4", "Corp_QYL", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "EQUALS", "10");
        addAnalyticalMarketSegmentWith("Corp", null, "Corp_DEF", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "DEFAULT", "999");

        addAnalyticalMarketSegmentWith("FAM", RC1_RATE_CODE, "FAM_QYL", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "EQUALS", "10");
        addAnalyticalMarketSegmentWith("FAM", RC2_RATE_CODE, "FAM_QYL", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "EQUALS", "10");
        addAnalyticalMarketSegmentWith("FAM", null, "FAM_DEF", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "DEFAULT", "999");


        final List<AnalyticalMarketSegment> existingAnalyticalMktSeg = tenantCrudService().findByNamedQuery(AnalyticalMarketSegment.BY_MARKET_CODES,
                QueryParameter.with("marketCodes", List.of("Corp", "FAM")).parameters());

        final Map<String, MktSegRecodingAMSSummary> mappedMktCodeToMSRecodingSummaryMap =
                service.getMktSegRecodingAMSSummaryByMappedMktCodeOfExistingAMSRules(existingAnalyticalMktSeg);
        assertTrue(isNotEmpty(mappedMktCodeToMSRecodingSummaryMap));
        assertTrue(mappedMktCodeToMSRecodingSummaryMap.keySet()
                .containsAll(List.of("FAM_DEF", "FAM_QYL", "Corp_DEF", "Corp_QYL")));

    }

    @Test
    public void getExistingAMSRuleSummaryByMktCodeWhenCommonMktCodePresentTest() {
        setUpServiceForAssigningMSToNewAMSRulesWithNoAttributeChange();

        addPMSRevampNewAMSRule("Corp", "RC4", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");
        addPMSRevampNewAMSRule("Corp", "RC5", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");
        addPMSRevampNewAMSRule("Corp", "DEFAULT", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");

        addPMSRevampNewAMSRule("FAM", RC3_RATE_CODE, QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");
        addPMSRevampNewAMSRule("FAM", RC2_RATE_CODE, QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");
        addPMSRevampNewAMSRule("FAM", "DEFAULT", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");

        addAnalyticalMarketSegmentWith("CORP", "RC4", "CORP_QYL", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "EQUALS", "10");
        addAnalyticalMarketSegmentWith("CORP", null, "CORP_DEF", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "DEFAULT", "999");

        addAnalyticalMarketSegmentWith("FAM", RC1_RATE_CODE, "FAM_QYL", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "EQUALS", "10");
        addAnalyticalMarketSegmentWith("FAM", RC2_RATE_CODE, "FAM_QYL", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "EQUALS", "10");
        addAnalyticalMarketSegmentWith("FAM", null, "FAM_DEF", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "DEFAULT", "999");


        final List<NewAMSRule> newAMSRules =
                tenantCrudService().findByNativeQuery(NewAMSRule.MKT_CODE_EXISTS_IN_ANALYTICAL_MKT_SEG, Collections.emptyMap(), NewAMSRule.class);

        final Map<String, MktSegRecodingAMSSummary> mappedMktCodeToMSRecodingSummaryMap =
                service.getExistingAMSRuleSummaryByMktCode(newAMSRules);
        assertTrue(isNotEmpty(mappedMktCodeToMSRecodingSummaryMap));
        assertTrue(mappedMktCodeToMSRecodingSummaryMap.keySet()
                .containsAll(List.of("FAM_DEF", "FAM_QYL", "CORP_DEF", "CORP_QYL")));

    }

    @Test
    public void handleRenamedRateCodesWithNoMarketCodeOrAttributeChangeTest() {
        setUpServiceForAssigningMSToNewAMSRulesWithNoAttributeChange();

        addPMSRevampNewAMSRule("Corp", "RC4", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");
        addPMSRevampNewAMSRule("Corp", "RC5", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");
        addPMSRevampNewAMSRule("Corp", "DEFAULT", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");

        addPMSRevampNewAMSRule("FAM", RC3_RATE_CODE, QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");
        addPMSRevampNewAMSRule("FAM", RC2_RATE_CODE, QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");
        addPMSRevampNewAMSRule("FAM", "DEFAULT", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");

        addAnalyticalMarketSegmentWith("CORP", "RC4", "CORP_QYL", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "EQUALS", "10");
        addAnalyticalMarketSegmentWith("CORP", null, "CORP_DEF", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "DEFAULT", "999");

        addAnalyticalMarketSegmentWith("FAM", RC1_RATE_CODE, "FAM_QYL", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "EQUALS", "10");
        addAnalyticalMarketSegmentWith("FAM", RC2_RATE_CODE, "FAM_QYL", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "EQUALS", "10");
        addAnalyticalMarketSegmentWith("FAM", null, "FAM_DEF", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "DEFAULT", "999");


        final List<NewAMSRule> newAMSRules =
                tenantCrudService().findByNativeQuery(NewAMSRule.MKT_CODE_EXISTS_IN_ANALYTICAL_MKT_SEG, Collections.emptyMap(), NewAMSRule.class);

        final Map<String, MktSegRecodingAMSSummary> mappedMktCodeToMSRecodingSummaryMap =
                service.getExistingAMSRuleSummaryByMktCode(newAMSRules);
        assertTrue(isNotEmpty(mappedMktCodeToMSRecodingSummaryMap));
        assertTrue(mappedMktCodeToMSRecodingSummaryMap.keySet()
                .containsAll(List.of("FAM_DEF", "FAM_QYL", "CORP_DEF", "CORP_QYL")));

    }

    @Test
    public void assignMktSegToNewAMSRules_shouldReturnMktSegIdsWhoseBusinessTypeGetShifted() {
        //Given
        setUpMSRecodingService();
        final PMSRevampAMSDataService testPMSRevampAMSDataService = setUpPMSRevampAMSServiceForRecoding();

        final Integer corpMktSegId = addMarketSegment("CORP");
        final Integer ms1QYLId = addMarketSegment(MS1_QYL_MARKET_CODE);

        addPMSMigrationMappingWith(CODE_TYPE_MS_GROUP, "CORP", "CORP", null);
        addPMSMigrationMappingWith(CODE_TYPE_MS, MS1_MARKET_CODE, MS1_MARKET_CODE, null);
        addPMSMigrationMappingWith(CODE_TYPE_RC, RC1_RATE_CODE, RC1_RATE_CODE, null);

        final AnalyticalMarketSegment corpGroupAMS = getAnalyticalMarketSegment("CORP", null, "CORP", AnalyticalMarketSegmentAttribute.GROUP, RateCodeTypeEnum.ALL, 1, false);
        final AnalyticalMarketSegment ms1rc1QYL = getAnalyticalMarketSegment(MS1_MARKET_CODE, RC1_RATE_CODE, MS1_QYL_MARKET_CODE, AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE, RateCodeTypeEnum.EQUALS, RANK_10, false);
        tenantCrudService().save(Arrays.asList(corpGroupAMS, ms1rc1QYL));

        final NewAMSRule corpTransientAMSRule = getNewAMSRule("CORP", null, AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR, ForecastActivityType.DEMAND_AND_WASH);
        final NewAMSRule ms1rc1QYLNewRule = getNewAMSRule(MS1_MARKET_CODE, RC1_RATE_CODE, AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE, ForecastActivityType.DEMAND_AND_WASH);
        final NewAMSRule ms1rc2QYLNewRule = getNewAMSRule(MS1_MARKET_CODE, RC2_RATE_CODE, AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE, ForecastActivityType.DEMAND_AND_WASH);
        testPMSRevampAMSDataService.saveNewAMSRules(Arrays.asList(corpTransientAMSRule, ms1rc1QYLNewRule, ms1rc2QYLNewRule));

        final MktSegRecodingBusinessTypeShift corpBusinessTypeShift = new MktSegRecodingBusinessTypeShift("CORP",
                Constants.BUSINESS_TYPE.GROUP.getCode(),
                Constants.BUSINESS_TYPE.TRANSIENT.getCode());
        service.saveBusinessTypeShiftAssociations(Collections.singletonList(corpBusinessTypeShift));
        //When
        List<Integer> mktSegIds = service.assignMktSegToNewAMSRules();

        //Then
        assertFalse(mktSegIds.isEmpty());
        assertEquals(2, mktSegIds.size());
        assertTrue(mktSegIds.containsAll(Arrays.asList(corpMktSegId, ms1QYLId)));

    }

    @Test
    public void assignMktSegToNewAMSRules_shouldReturnMktSegIdsWhenStraightAMSMergedToExistingStraightAMS() {
        //Given
        setUpMSRecodingService();
        final PMSRevampAMSDataService testPMSRevampAMSDataService = setUpPMSRevampAMSServiceForRecoding();

        final String COLCL_MarketCode = "COLCL";
        final String CONEG_MarketCode = "CONEG";
        final Integer COLCL_MktSegId = addMarketSegment(COLCL_MarketCode);
        final Integer CONEG_MktSegId = addMarketSegment(CONEG_MarketCode);

        addPMSMigrationMappingWith(CODE_TYPE_MS, COLCL_MarketCode, CONEG_MarketCode, null);
        addPMSMigrationMappingWith(CODE_TYPE_MS, CONEG_MarketCode, CONEG_MarketCode, null);

        final AnalyticalMarketSegment COLCL_StraightAMS = getAnalyticalMarketSegment(COLCL_MarketCode, null, COLCL_MarketCode, AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE, RateCodeTypeEnum.ALL, 1, false);
        final AnalyticalMarketSegment CONEG_StraightAMS = getAnalyticalMarketSegment(CONEG_MarketCode, null, CONEG_MarketCode, AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE, RateCodeTypeEnum.ALL, 1, false);

        tenantCrudService().save(Arrays.asList(COLCL_StraightAMS, CONEG_StraightAMS));

        final NewAMSRule CONEG_StraightNewRule = getNewAMSRule(CONEG_MarketCode, null, AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE, ForecastActivityType.DEMAND_AND_WASH);

        testPMSRevampAMSDataService.saveNewAMSRules(Collections.singletonList(CONEG_StraightNewRule));

        //When
        List<Integer> mktSegIds = service.assignMktSegToNewAMSRules();

        //Then
        assertFalse(mktSegIds.isEmpty());
        assertEquals(1, mktSegIds.size());
        assertEquals(CONEG_MktSegId, mktSegIds.get(0));

    }

    @Test
    public void assignMktSegToNewAMSRules_shouldCreateMktSegDetailsProposedInCaseOfOnlyMarketCodeRenameAndMktSegNotExists() {
        //Given
        setUpMSRecodingService();
        final PMSRevampAMSDataService testPMSRevampAMSDataService = setUpPMSRevampAMSServiceForRecoding();

        final Integer PROJ_DEF_MktSegId = addMarketSegment("PROJ_DEF");

        addMktSegMaster("PROJ_USB", 1);
        final MarketSegmentMaster PROJ_DEF_MktSegMaster = addMktSegMaster("PROJ_DEF", 1);

        addPMSMigrationMappingWith(CODE_TYPE_MS, "PROJ", "PROJ_NEW", null);
        addPMSMigrationMappingWith(CODE_TYPE_RC, "R1", "R1", null);

        final AnalyticalMarketSegment R1_PROJ_USB = getAnalyticalMarketSegment("PROJ", "R1", "PROJ_USB", AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR, RateCodeTypeEnum.EQUALS, 10, false);
        final AnalyticalMarketSegment PROJ_DEF = getAnalyticalMarketSegment("PROJ", null, "PROJ_DEF", AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR, RateCodeTypeEnum.DEFAULT, 999, false);

        tenantCrudService().save(Arrays.asList(R1_PROJ_USB, PROJ_DEF));

        final NewAMSRule R1_PROJ_NEW_Rule = getNewAMSRule("PROJ_NEW", "R1", AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR, ForecastActivityType.DEMAND_AND_WASH);
        final NewAMSRule PROJ_NEW_DEF_Rule = getNewAMSRule("PROJ_NEW", "DEFAULT", AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR, ForecastActivityType.DEMAND_AND_WASH);

        testPMSRevampAMSDataService.saveNewAMSRules(Arrays.asList(R1_PROJ_NEW_Rule, PROJ_NEW_DEF_Rule));

        //When
        service.assignMktSegToNewAMSRules();
        //Then
        final Integer PROJ_NEW_USB_MktSegId = getMktSegIdByCode("PROJ_NEW_USB");
        assertNotNull(PROJ_NEW_USB_MktSegId);
        assertEquals(PROJ_DEF_MktSegId, getMktSegIdByCode("PROJ_NEW_DEF"));

        assertNotNull(getMktSegDetailsProposed(PROJ_NEW_USB_MktSegId));

        assertNotNull(getMarketSegmentMaster("PROJ_NEW_USB"));
        final MarketSegmentMaster PROJ_NEW_DEF_MktSegMaster = getMarketSegmentMaster("PROJ_NEW_DEF");
        assertEquals(PROJ_DEF_MktSegMaster.getId(), PROJ_NEW_DEF_MktSegMaster.getId());

    }

    @Test
    public void handleRenamedMappedMktCode_shouldUpdateMktSegAndMktSegMaster() {
        //Given
        final Integer PROJ_USB_MktSegId = addMarketSegment("PROJ_USB");
        final MarketSegmentMaster PROJ_USB_MktSegMaster = addMktSegMaster("PROJ_USB", 1);
        //When
        service.handleRenamedMappedMarketCode("PROJ_USB", "PROJ_NEW_USB");
        //Then
        final Integer PROJ_NEW_USB_MktSegId = getMktSegIdByCode("PROJ_NEW_USB");
        assertEquals(PROJ_USB_MktSegId, PROJ_NEW_USB_MktSegId);

        final MarketSegmentMaster PROJ_NEW_USB_MktSegMaster = getMarketSegmentMaster("PROJ_NEW_USB");
        assertEquals(PROJ_USB_MktSegMaster.getId(), PROJ_NEW_USB_MktSegMaster.getId());

    }

    @Test
    public void handleRenamedMappedMktCode_shouldDeleteMktSegMasterWhenMktSegNotExists() {
        //Given
        addMktSegMaster("PROJ_USB", 1);
        //When
        service.handleRenamedMappedMarketCode("PROJ_USB", "PROJ_NEW_USB");
        //Then
        assertNull(getMarketSegmentMaster("PROJ_USB"));

    }

    @Test
    public void shouldAssignTierMarketSegmentsToAMS() {
        //GIVEN
        insertAnalyticalMktSeg("CSLI", "CASH011", "CASINO_CASH_11_QSL", RateCodeTypeEnum.EQUALS, "0", ATTRIBUTE);
        insertAnalyticalMktSeg("CSLI", "CASH012", "CASINO_QSL", RateCodeTypeEnum.EQUALS, "0", ATTRIBUTE);
        insertAnalyticalMktSeg("CSLI", "", "CASINO_DEF", RateCodeTypeEnum.DEFAULT, "0", ATTRIBUTE);
        tierMarketSegmentMappingCreationHelper.insertIntoTierMarketSegmentMapping("MARKET_SEGMENT_NON_GROUP", "CSLI", "CPERP");
        //WHEN
        service.assignTierMarketSegmentsToAMS();
        //THEN
        List<Object[]> rowsCPERP = tenantCrudService().findByNativeQuery("select Market_Code, Rate_Code, Mapped_Market_Code, Preserved " +
                "from Analytical_Mkt_Seg where Market_Code = 'CPERP' order by Rate_Code ");
        assertAMS(rowsCPERP.get(0), "CPERP", null, "CASINO_DEF", 0);
        assertAMS(rowsCPERP.get(1), "CPERP", "CASH011", "CASINO_CASH_11_QSL", 0);
        assertAMS(rowsCPERP.get(2), "CPERP", "CASH012", "CASINO_QSL", 0);
        List<Object[]> rowsCSLI = tenantCrudService().findByNativeQuery("select Market_Code, Rate_Code, Mapped_Market_Code, Preserved " +
                "from Analytical_Mkt_Seg where Market_Code = 'CSLI' order by Rate_Code");
        assertAMS(rowsCSLI.get(0), "CSLI", null, "CASINO_DEF", 1);
        assertAMS(rowsCSLI.get(1), "CSLI", "CASH011", "CASINO_CASH_11_QSL", 1);
        assertAMS(rowsCSLI.get(2), "CSLI", "CASH012", "CASINO_QSL", 1);
    }

    @Test
    public void shouldFilterDuplicateEntriesWhileAssigningTierMarketSegmentsToAMS() {
        //GIVEN
        insertAnalyticalMktSeg("CSLI", "CASH011", "CASINO_CASH_11_QSL", RateCodeTypeEnum.EQUALS, "0", ATTRIBUTE);
        insertAnalyticalMktSeg("CSLI", "CASH012", "CASINO_QSL", RateCodeTypeEnum.EQUALS, "0", ATTRIBUTE);
        insertAnalyticalMktSeg("CSLI", "", "CASINO_DEF", RateCodeTypeEnum.DEFAULT, "0", ATTRIBUTE);
        insertAnalyticalMktSeg("CSTP", "CASH011", "CASINO_CASH_11_QSL", RateCodeTypeEnum.EQUALS, "0", ATTRIBUTE);
        insertAnalyticalMktSeg("CSTP", "", "CASINO_DEF", RateCodeTypeEnum.DEFAULT, "0", ATTRIBUTE);
        tierMarketSegmentMappingCreationHelper.insertIntoTierMarketSegmentMapping("MARKET_SEGMENT_NON_GROUP", "CSLI", "CPERP");
        tierMarketSegmentMappingCreationHelper.insertIntoTierMarketSegmentMapping("MARKET_SEGMENT_NON_GROUP", "CSTP", "CPERP");
        //WHEN
        service.assignTierMarketSegmentsToAMS();
        //THEN
        List<Object[]> rowsCPERP = tenantCrudService().findByNativeQuery("select Market_Code, Rate_Code, Mapped_Market_Code, Preserved " +
                "from Analytical_Mkt_Seg where Market_Code = 'CPERP' order by Rate_Code ");
        assertAMS(rowsCPERP.get(0), "CPERP", null, "CASINO_DEF", 0);
        assertAMS(rowsCPERP.get(1), "CPERP", "CASH011", "CASINO_CASH_11_QSL", 0);
        assertAMS(rowsCPERP.get(2), "CPERP", "CASH012", "CASINO_QSL", 0);
        List<Object[]> rowsCSLI = tenantCrudService().findByNativeQuery("select Market_Code, Rate_Code, Mapped_Market_Code, Preserved " +
                "from Analytical_Mkt_Seg where Market_Code = 'CSLI' order by Rate_Code");
        assertAMS(rowsCSLI.get(0), "CSLI", null, "CASINO_DEF", 1);
        assertAMS(rowsCSLI.get(1), "CSLI", "CASH011", "CASINO_CASH_11_QSL", 1);
        assertAMS(rowsCSLI.get(2), "CSLI", "CASH012", "CASINO_QSL", 1);
        List<Object[]> rowsCSTP = tenantCrudService().findByNativeQuery("select Market_Code, Rate_Code, Mapped_Market_Code, Preserved " +
                "from Analytical_Mkt_Seg where Market_Code = 'CSTP' order by Rate_Code");
        assertAMS(rowsCSTP.get(0), "CSTP", null, "CASINO_DEF", 1);
        assertAMS(rowsCSTP.get(1), "CSTP", "CASH011", "CASINO_CASH_11_QSL", 1);
    }

    @Test
    public void shouldAssignTierMarketSegmentsToAMSWhenRateCodeIsSharedBetweenDifferentTierMarketCodeWhichHasDifferentMappedMarketCode() {
        //GIVEN
        insertAnalyticalMktSeg("CSLI", "CASH011", "CASINO_CASH_11_QSL", RateCodeTypeEnum.EQUALS, "0", EQUAL_TO_BAR);
        insertAnalyticalMktSeg("CSLI", "CASH012", "CASINO_QSL", RateCodeTypeEnum.DEFAULT, "0", EQUAL_TO_BAR);
        insertAnalyticalMktSeg("CSLI", "", "CASINO_DEF", RateCodeTypeEnum.DEFAULT, "0", QUALIFIED_NONBLOCK_LINKED_YIELDABLE);
        insertAnalyticalMktSeg("CSTP", "CASH011", "CASINO_CASH_11_QSL", RateCodeTypeEnum.EQUALS, "0", EQUAL_TO_BAR);
        insertAnalyticalMktSeg("CSTP", "CASH012", "CASINO_CASH_12_QSL", RateCodeTypeEnum.EQUALS, "0", QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE);
        insertAnalyticalMktSeg("CSTP", "", "CASINO_DEF", RateCodeTypeEnum.DEFAULT, "0", QUALIFIED_NONBLOCK_LINKED_YIELDABLE);
        tierMarketSegmentMappingCreationHelper.insertIntoTierMarketSegmentMapping("MARKET_SEGMENT_NON_GROUP", "CSLI", "CPERP");
        tierMarketSegmentMappingCreationHelper.insertIntoTierMarketSegmentMapping("MARKET_SEGMENT_NON_GROUP", "CSTP", "CPERP");
        createRateCodeShiftAssociationForTierWith("CASH012", "CPERP", "CASINO_CASH_12_QSL");
        //WHEN
        service.assignTierMarketSegmentsToAMS();
        //THEN
        List<Object[]> rowsCPERP = tenantCrudService().findByNativeQuery("select Market_Code, Rate_Code, Mapped_Market_Code, Preserved, Attribute, Rate_Code_Type, Rank " +
                "from Analytical_Mkt_Seg where Market_Code = 'CPERP' order by Rate_Code ");
        assertEquals(3, rowsCPERP.size());
        assertAMSWithAttribute(rowsCPERP.get(0), "CPERP", null, "CASINO_DEF", 0, QUALIFIED_NONBLOCK_LINKED_YIELDABLE, RateCodeTypeEnum.DEFAULT, RateCodeTypeEnum.DEFAULT.getRank());
        assertAMSWithAttribute(rowsCPERP.get(1), "CPERP", "CASH011", "CASINO_CASH_11_QSL", 0, EQUAL_TO_BAR, RateCodeTypeEnum.EQUALS, RateCodeTypeEnum.EQUALS.getRank());
        assertAMSWithAttribute(rowsCPERP.get(2), "CPERP", "CASH012", "CASINO_CASH_12_QSL", 0, QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE, RateCodeTypeEnum.EQUALS, RateCodeTypeEnum.EQUALS.getRank());
        List<Object[]> rowsCSLI = tenantCrudService().findByNativeQuery("select Market_Code, Rate_Code, Mapped_Market_Code, Preserved, Attribute, Rate_Code_Type, Rank " +
                "from Analytical_Mkt_Seg where Market_Code = 'CSLI' order by Rate_Code");
        assertAMSWithAttribute(rowsCSLI.get(0), "CSLI", null, "CASINO_DEF", 1, QUALIFIED_NONBLOCK_LINKED_YIELDABLE, RateCodeTypeEnum.DEFAULT, RateCodeTypeEnum.DEFAULT.getRank());
        assertAMSWithAttribute(rowsCSLI.get(1), "CSLI", "CASH011", "CASINO_CASH_11_QSL", 1, EQUAL_TO_BAR, RateCodeTypeEnum.EQUALS, RateCodeTypeEnum.EQUALS.getRank());
        assertAMSWithAttribute(rowsCSLI.get(2), "CSLI", "CASH012", "CASINO_CASH_12_QSL", 1, QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE, RateCodeTypeEnum.EQUALS, RateCodeTypeEnum.EQUALS.getRank());
        List<Object[]> rowsCSTP = tenantCrudService().findByNativeQuery("select Market_Code, Rate_Code, Mapped_Market_Code, Preserved, Attribute, Rate_Code_Type, Rank " +
                "from Analytical_Mkt_Seg where Market_Code = 'CSTP' order by Rate_Code");
        assertAMSWithAttribute(rowsCSTP.get(0), "CSTP", null, "CASINO_DEF", 1, QUALIFIED_NONBLOCK_LINKED_YIELDABLE, RateCodeTypeEnum.DEFAULT, RateCodeTypeEnum.DEFAULT.getRank());
        assertAMSWithAttribute(rowsCSTP.get(1), "CSTP", "CASH011", "CASINO_CASH_11_QSL", 1, EQUAL_TO_BAR, RateCodeTypeEnum.EQUALS, RateCodeTypeEnum.EQUALS.getRank());
        assertAMSWithAttribute(rowsCSTP.get(2), "CSTP", "CASH012", "CASINO_CASH_12_QSL", 1, QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE, RateCodeTypeEnum.EQUALS, RateCodeTypeEnum.EQUALS.getRank());
    }

    @Test
    public void shouldUpdateTierMarketSegmentInMktSeg() {
        //GIVEN
        insertAnalyticalMktSeg("CPERP", "CASH011", "CASINO_CASH_11_QSL", RateCodeTypeEnum.EQUALS, "0", ATTRIBUTE);
        insertAnalyticalMktSeg("CPERP", "CASH012", "CASINO_QSL", RateCodeTypeEnum.EQUALS, "0", ATTRIBUTE);
        insertAnalyticalMktSeg("CPERP", "", "CASINO_DEF", RateCodeTypeEnum.DEFAULT, "0", ATTRIBUTE);
        insertAnalyticalMktSeg("IN-CDNL", "RC", "IN-CDNL_QSL", RateCodeTypeEnum.EQUALS, "0", ATTRIBUTE);
        insertAnalyticalMktSeg("IN-CDNL", "", "IN-CDNL_DEF", RateCodeTypeEnum.DEFAULT, "0", ATTRIBUTE);
        tierMarketSegmentMappingCreationHelper.insertIntoTierMarketSegmentMapping("MARKET_SEGMENT_NON_GROUP", "CSLI", "CPERP");
        Integer csliMktSegId = addMarketSegment("CSLI");
        addMarketSegment("IN-CDNL");
        //WHEN
        int updatedRows = service.updateTierMarketSegmentInMktSeg();
        //THEN
        assertEquals(1, updatedRows);
        List<Object[]> rows = tenantCrudService().findByNativeQuery("select Mkt_Seg_Code, Mkt_Seg_Name, Mkt_Seg_Description from " +
                "Mkt_Seg where Mkt_Seg_ID = " + csliMktSegId);
        assertEquals(1, rows.size());
        assertEquals("CPERP", rows.get(0)[0]);
        assertEquals("CPERP", rows.get(0)[1]);
        assertEquals("CPERP", rows.get(0)[2]);
    }

    @Test
    public void shouldUpdateMarketCodeOfTierMarketSegmentsInReservation() throws ExecutionException, InterruptedException, ParseException {
        setWorkContextProperty(TestProperty.H1);
        //Reservations
        Integer CASINO_CASH_MKT_SEG_ID = addMarketSegment("CASINO_CASH_11_QSL");
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-01", "CSLI", "CASH011", CASINO_CASH_MKT_SEG_ID);
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-02", "CSLI", "CASH011", CASINO_CASH_MKT_SEG_ID);
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-03", "CSLI", "CASH011", CASINO_CASH_MKT_SEG_ID);
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-05", "2019-01-04", "CSLI", "CASH011", CASINO_CASH_MKT_SEG_ID);
        //AMS
        insertAnalyticalMktSeg("CPERP", "CASH011", "CASINO_CASH_11_QSL", RateCodeTypeEnum.EQUALS, "0", ATTRIBUTE);
        insertAnalyticalMktSeg("CPERP", "CASH012", "CASINO_QSL", RateCodeTypeEnum.EQUALS, "0", ATTRIBUTE);
        insertAnalyticalMktSeg("CPERP", "", "CASINO_DEF", RateCodeTypeEnum.DEFAULT, "0", ATTRIBUTE);
        insertAnalyticalMktSeg("IN-CDNL", "RC", "IN-CDNL_QSL", RateCodeTypeEnum.EQUALS, "0", ATTRIBUTE);
        insertAnalyticalMktSeg("IN-CDNL", "", "IN-CDNL_DEF", RateCodeTypeEnum.DEFAULT, "0", ATTRIBUTE);
        tierMarketSegmentMappingCreationHelper.insertIntoTierMarketSegmentMapping("MARKET_SEGMENT_NON_GROUP", "CSLI", "CPERP");
        //WHEN
        Integer result = service.updateMarketCodeOfTierMarketSegmentsInReservation("Reservation_Night",
                DateUtil.getDateWithoutTime(1, 0, 2019),
                DateUtil.getDateWithoutTime(4, 0, 2019));
        assertEquals(4, result);
        List<Object[]> resNightResults = tenantCrudService().findByNativeQuery(
                "select Occupancy_DT, Market_Code from Reservation_Night order by Occupancy_DT");
        assertEquals(java.sql.Date.valueOf("2019-01-01"), resNightResults.get(4)[0]);
        assertEquals("CPERP", resNightResults.get(4)[1]);
        assertEquals(java.sql.Date.valueOf("2019-01-02"), resNightResults.get(5)[0]);
        assertEquals("CPERP", resNightResults.get(5)[1]);
        assertEquals(java.sql.Date.valueOf("2019-01-03"), resNightResults.get(6)[0]);
        assertEquals("CPERP", resNightResults.get(6)[1]);
        assertEquals(java.sql.Date.valueOf("2019-01-04"), resNightResults.get(7)[0]);
        assertEquals("CPERP", resNightResults.get(7)[1]);
    }

    @Test
    public void shouldUpdateMarketSegmentIdOfTierMarketSegmentsInReservation() {
        setWorkContextProperty(TestProperty.H1);
        //Reservations
        Integer CASINO_CASH_MKT_SEG_ID = addMarketSegment("CASINO_CASH_11_QSL");
        Integer CASINO_QYL_ID = addMarketSegment("CASINO_QYL");
        //NOTE Market_Code in the reservations has already updated to new in the earlier steps
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-03", "2019-01-01", "CPERP", "CASH011", CASINO_CASH_MKT_SEG_ID);
        addReservationNightEntry("123456789", "2019-01-01", "2019-01-03", "2019-01-02", "CPERP", "CASH011", CASINO_CASH_MKT_SEG_ID);
        addReservationNightEntry("123456799", "2019-01-04", "2019-01-06", "2019-01-04", "CPERP", "CASH011", CASINO_QYL_ID);
        addReservationNightEntry("123456799", "2019-01-04", "2019-01-06", "2019-01-05", "CPERP", "CASH011", CASINO_QYL_ID);
        createRateCodeShiftAssociationForTierWith("CASH011", "CPERP", "CASINO_QYL");
        tierMarketSegmentMappingCreationHelper.insertIntoTierMarketSegmentMapping("MARKET_SEGMENT_NON_GROUP", "CSLI", "CPERP");
        tierMarketSegmentMappingCreationHelper.insertIntoTierMarketSegmentMapping("MARKET_SEGMENT_NON_GROUP", "CSTP", "CPERP");
        //AMS
        insertAnalyticalMktSeg("CSLI", "CASH011", "CASINO_CASH_11_QSL", RateCodeTypeEnum.EQUALS, "1", ATTRIBUTE);
        insertAnalyticalMktSeg("CSLI", "", "CASINO_DEF", RateCodeTypeEnum.DEFAULT, "1", ATTRIBUTE);
        insertAnalyticalMktSeg("CSTP", "CASH012", "CASINO_CASH_12_QSL", RateCodeTypeEnum.EQUALS, "1", ATTRIBUTE);
        insertAnalyticalMktSeg("CSTP", "CASH011", "CASINO_QYL", RateCodeTypeEnum.EQUALS, "1", ATTRIBUTE);
        insertAnalyticalMktSeg("CSTP", "", "CASINO_DEF", RateCodeTypeEnum.DEFAULT, "1", ATTRIBUTE);
        insertAnalyticalMktSeg("CPERP", "CASH011", "CASINO_QYL", RateCodeTypeEnum.EQUALS, "0", ATTRIBUTE);
        insertAnalyticalMktSeg("CPERP", "CASH012", "CASINO_CASH_12_QSL", RateCodeTypeEnum.EQUALS, "0", ATTRIBUTE);
        insertAnalyticalMktSeg("CPERP", "", "CASINO_DEF", RateCodeTypeEnum.DEFAULT, "0", ATTRIBUTE);
        //WHEN
        Integer result = service.updateMarketSegmentIdOfTierMarketSegmentsInReservation("Reservation_Night",
                DateUtil.getDateWithoutTime(1, 0, 2019),
                DateUtil.getDateWithoutTime(7, 0, 2019));
        assertEquals(4, result);
        List<Object[]> resNightResults = tenantCrudService().findByNativeQuery(
                "select Occupancy_DT, Market_Code, Mkt_Seg_ID from Reservation_Night order by Occupancy_DT");
        assertEquals(java.sql.Date.valueOf("2019-01-01"), resNightResults.get(4)[0]);
        assertEquals("CPERP", resNightResults.get(4)[1]);
        assertEquals(CASINO_QYL_ID, resNightResults.get(4)[2]);
        assertEquals(java.sql.Date.valueOf("2019-01-02"), resNightResults.get(5)[0]);
        assertEquals("CPERP", resNightResults.get(5)[1]);
        assertEquals(CASINO_QYL_ID, resNightResults.get(5)[2]);
        assertEquals(java.sql.Date.valueOf("2019-01-04"), resNightResults.get(6)[0]);
        assertEquals("CPERP", resNightResults.get(6)[1]);
        assertEquals(CASINO_QYL_ID, resNightResults.get(6)[2]);
        assertEquals(java.sql.Date.valueOf("2019-01-05"), resNightResults.get(7)[0]);
        assertEquals("CPERP", resNightResults.get(7)[1]);
        assertEquals(CASINO_QYL_ID, resNightResults.get(7)[2]);
    }

    @Test
    public void shouldUpdateMarketSegmentIdOfTierMarketSegmentsInRevenueStreamDetails() {
        setWorkContextProperty(TestProperty.H1);
        Integer CASINO_CASH_MKT_SEG_ID = addMarketSegment("CASINO_CASH_11_QSL");
        Integer CASINO_QYL_ID = addMarketSegment("CASINO_QYL");
        //RevenueStreamDetails
        RevenueStream casinoRevenueStream = revenueStreamDetailCreator.createRevenueStream("100", "Casino", true);
        LocalDate today = LocalDate.now();
        LocalDate tomorrow = today.plusDays(1);
        createRevenueStreamDetail(casinoRevenueStream.getId(), today, CASINO_CASH_MKT_SEG_ID, "CASH011", null,
                "100.50", "105.50", "50.50", "55.00", 3,
                null, "CPERP", "STE", null);
        createRevenueStreamDetail(casinoRevenueStream.getId(), tomorrow, CASINO_QYL_ID, "CASH011", null,
                "110.50", "115.50", "52.50", "58.00", 7,
                null, "CPERP", "STE", null);
        createRateCodeShiftAssociationForTierWith("CASH011", "CPERP", "CASINO_QYL");
        tierMarketSegmentMappingCreationHelper.insertIntoTierMarketSegmentMapping("MARKET_SEGMENT_NON_GROUP", "CSLI", "CPERP");
        tierMarketSegmentMappingCreationHelper.insertIntoTierMarketSegmentMapping("MARKET_SEGMENT_NON_GROUP", "CSTP", "CPERP");
        //AMS
        insertAnalyticalMktSeg("CSLI", "CASH011", "CASINO_CASH_11_QSL", RateCodeTypeEnum.EQUALS, "1", ATTRIBUTE);
        insertAnalyticalMktSeg("CSLI", "", "CASINO_DEF", RateCodeTypeEnum.DEFAULT, "1", ATTRIBUTE);
        insertAnalyticalMktSeg("CSTP", "CASH012", "CASINO_CASH_12_QSL", RateCodeTypeEnum.EQUALS, "1", ATTRIBUTE);
        insertAnalyticalMktSeg("CSTP", "CASH011", "CASINO_QYL", RateCodeTypeEnum.EQUALS, "1", ATTRIBUTE);
        insertAnalyticalMktSeg("CSTP", "", "CASINO_DEF", RateCodeTypeEnum.DEFAULT, "1", ATTRIBUTE);
        insertAnalyticalMktSeg("CPERP", "CASH011", "CASINO_QYL", RateCodeTypeEnum.EQUALS, "0", ATTRIBUTE);
        insertAnalyticalMktSeg("CPERP", "CASH012", "CASINO_CASH_12_QSL", RateCodeTypeEnum.EQUALS, "0", ATTRIBUTE);
        insertAnalyticalMktSeg("CPERP", "", "CASINO_DEF", RateCodeTypeEnum.DEFAULT, "0", ATTRIBUTE);
        //WHEN
        Integer result = service.updateMarketSegmentIdOfTierMarketSegmentsInRevenueStreamDetails(
                LocalDateUtils.toDate(today),
                LocalDateUtils.toDate(tomorrow));
        assertEquals(2, result);
        List<Object[]> rows = tenantCrudService().findByNativeQuery("select * from Revenue_Stream_Detail order by Occupancy_DT");
        assertEquals(2, rows.size());
        assertRevenueStreamDetail(today, "100.50", "105.50",
                "50.50", "55.00", 3,
                "CPERP", CASINO_QYL_ID, rows.get(0));
        assertRevenueStreamDetail(tomorrow, "110.50", "115.50",
                "52.50", "58.00", 7,
                "CPERP", CASINO_QYL_ID, rows.get(1));
    }

    private void assertRevenueStreamDetail(LocalDate today, String expectedEstimateValue,
                                           String expectedActualValue, String expectedEstimateFixedCost,
                                           String expectedActualFixedCost, int expectedRoomNightCount,
                                           String expectedMktSegCode, int expectedMktSegId, Object[] row1) {
        assertEquals(today.toString(), ((java.sql.Date) row1[2]).toString());
        assertEquals(new BigDecimal(expectedEstimateValue), row1[8]);
        assertEquals(new BigDecimal(expectedActualValue), row1[9]);
        assertEquals(new BigDecimal(expectedEstimateFixedCost), row1[10]);
        assertEquals(new BigDecimal(expectedActualFixedCost), row1[11]);
        assertEquals(expectedRoomNightCount, row1[12]);
        assertEquals(expectedMktSegCode, row1[14]);
        assertEquals(expectedMktSegId, row1[3]);
    }

    private void createRevenueStreamDetail(Integer casinoRevenueStreamId,
                                           LocalDate occupancyDt, Integer casinoCash11Qyl,
                                           String rateCode, Integer tier, String estimateValue,
                                           String actualValue, String estimateFixedCost, String actualFixedCost,
                                           Integer roomNightCount, Integer LOS, String mktSegCode,
                                           String accomTypeCode, String blockCode) {
        revenueStreamDetailCreator.createRevenueStreamDetail(casinoRevenueStreamId, occupancyDt, casinoCash11Qyl, rateCode,
                tier, estimateValue, actualValue, estimateFixedCost, actualFixedCost,
                roomNightCount, LOS, mktSegCode, accomTypeCode, blockCode);
    }

    @Test
    public void rateCodeShiftAssociationIsNotAvailableWhenTableNotExist() {
        boolean rateCodeShiftAssociationForTierAvailable = service.isRateCodeShiftAssociationForTierAvailable();
        assertFalse(rateCodeShiftAssociationForTierAvailable);
    }

    @Test
    public void rateCodeShiftAssociationIsNotAvailableWhenTableExistButDataNotExists() {
        tenantCrudService().executeUpdateByNativeQuery(RateCodeShiftAssociationForTier.CREATE_RATE_CODE_SHIFT_TABLE_DDL);
        boolean rateCodeShiftAssociationForTierAvailable = service.isRateCodeShiftAssociationForTierAvailable();
        assertFalse(rateCodeShiftAssociationForTierAvailable);
    }

    @Test
    public void rateCodeShiftAssociationIsAvailableWhenTableExistWithData() {
        createRateCodeShiftAssociationForTierWith("RC", "CPERP", "CASINO_QYL");
        boolean rateCodeShiftAssociationForTierAvailable = service.isRateCodeShiftAssociationForTierAvailable();
        assertTrue(rateCodeShiftAssociationForTierAvailable);
    }

    private void createRateCodeShiftAssociationForTierWith(final String rateCode,
                                                           final String marketCode,
                                                           final String preferredMappedMarketCode) {
        tenantCrudService().executeUpdateByNativeQuery(RateCodeShiftAssociationForTier.CREATE_RATE_CODE_SHIFT_TABLE_DDL);
        tenantCrudService().executeUpdateByNativeQuery(INSERT_RATE_CODE_SHIFT_RULES_QUERY_PREFIX + "('" + rateCode
                + "', '" + marketCode + "', '" + preferredMappedMarketCode + "')");
    }

    protected void assertAMSWithAttribute(Object[] row, String expectedMarketCode,
                                          String expectedRateCode, String expectedMappedMarketCode,
                                          int isPreserved, String expectedAttribute,
                                          RateCodeTypeEnum expectedRateCodeType, int expectedRank) {
        assertAMS(row, expectedMarketCode, expectedRateCode, expectedMappedMarketCode, isPreserved);
        assertEquals(expectedAttribute, row[4]);
        assertEquals(expectedRateCodeType.name(), row[5]);
        assertEquals(expectedRank, row[6]);
    }

    protected void assertAMS(Object[] row, String expectedMarketCode,
                             String expectedRateCode, String expectedMappedMarketCode, int isPreserved) {
        assertEquals(expectedMarketCode, row[0]);
        assertEquals(expectedRateCode, row[1]);
        assertEquals(expectedMappedMarketCode, row[2]);
        assertEquals(isPreserved == 1, row[3]);
    }


    private MktSegDetailsProposed getMktSegDetailsProposed(Integer mktSegId) {
        return tenantCrudService().findByNamedQuerySingleResult(MktSegDetailsProposed.BY_MKT_SEG_ID, QueryParameter.with("id", mktSegId).and("propertyId", TestProperty.H2.getId()).parameters());
    }

    private MarketSegmentMaster getMarketSegmentMaster(String mktSegCode) {
        return tenantCrudService().findByNamedQuerySingleResult(MarketSegmentMaster.FIND_BY_CODE, QueryParameter.with("code", mktSegCode).parameters());
    }


    private void setUpMSRecodingService() {
        PMSMigrationMappingService pmsmService = new PMSMigrationMappingService();
        AnalyticalMarketSegmentService analyticalMarketSegmentService = new AnalyticalMarketSegmentService();
        YieldCategoryRuleService yieldCategoryRuleService = new YieldCategoryRuleService();
        MarketSegmentService marketSegmentService = new MarketSegmentService();
        inject(pmsmService, "tenantCrudService", tenantCrudService());
        inject(analyticalMarketSegmentService, "crudService", tenantCrudService());
        inject(analyticalMarketSegmentService, "independentProductsService", independentProductsService);
        inject(yieldCategoryRuleService, "crudService", tenantCrudService());
        inject(marketSegmentService, "crudService", tenantCrudService());
        inject(service, "analyticalMarketSegmentService", analyticalMarketSegmentService);
        inject(service, "pmsMigrationMappingService", pmsmService);
        inject(service, "yieldCategoryRuleService", yieldCategoryRuleService);
        inject(service, "marketSegmentService", marketSegmentService);
    }

    private List<AnalyticalMarketSegment> getAllAMSRules() {
        return tenantCrudService().findAll(AnalyticalMarketSegment.class);
    }

    private void insertAnalyticalMktSeg(String marketCode, String rateCode, String mappedMarketCode, RateCodeTypeEnum rateCodeType, final String preserved, String attribute) {
        String rateCodeString = StringUtils.isBlank(rateCode) ? null : ("'" + rateCode + "'");
        tenantCrudService().executeUpdateByNativeQuery("insert into Analytical_Mkt_Seg([Market_Code],[Rate_Code] ,[Mapped_Market_Code], [Attribute], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Rate_Code_Type], [Rank], [Complimentary]) values ('" +
                marketCode + "'," + rateCodeString + ", '" + mappedMarketCode + "', '" + attribute + "', 1, GETDATE(), 1, GETDATE(), '" + rateCodeType + "', " + rateCodeType.getRank() +
                ", " + preserved + ")");
    }

    private void insertPMSMigrationMapping(String codeType, String currentCode, String newCode, String discontinued) {
        tenantCrudService().executeUpdateByNativeQuery("insert into PMS_Migration_Mapping values" +
                "('H2', '" + codeType + "', '" + currentCode + "', '" + newCode + "', " + discontinued + ", NULL)");
    }

    private void insertOperaYCR(String marketCode, String rateCode, String mappedMarketCode, int rank) {
        tenantCrudService().executeUpdateByNativeQuery("insert into opera.Yield_Category_Rule(Market_Code, Rate_Code, Analytical_Market_Code, Rank) values" +
                "('" + marketCode + "', '" + rateCode + "',  '" + mappedMarketCode + "'," + rank + ")");
    }

    private void setUpYCBR() {
        tenantCrudService().executeUpdateByNativeQuery("insert into opera.Yield_Category_Rule(Market_Code, Rate_Code, Analytical_Market_Code, Rank) values" +
                "('MS1', 'RC1',  'MS1_QYL', 10)," +
                "('MS1', 'RC2',  'MS1_QYL', 10)," +
                "('MS1', 'RC3',  'MS1_QYL', 10)," +
                "('MS1', 'RC4',  'MS1_QYL', 10)," +
                "('MS1', 'RC5',  'MS1_QYL', 10)," +
                "('MS1',  NULL,  'MS1_DEF', 999)," +
                "('MS2', 'RC6',  'MS2_QYL', 10)," +
                "('MS2', 'RC7',  'MS2_QYL', 10)," +
                "('MS2', 'RC8',  'MS2_QYL', 10)," +
                "('MS2', 'RC9',  'MS2_QYL', 10)," +
                "('MS2', 'RC10', 'MS2_QYL', 10)," +
                "('MS2',  NULL,  'MS2_DEF', 999)," +
                "('MS3', 'RC11', 'MS3_QYL', 10)," +
                "('MS3',  NULL,  'MS3_DEF', 999)");
    }

    private void assertIfRequiredMktSegAreInvalidated(Object[][] expectedMktSegTable) {
        tenantCrudService().flushAndClear();

        List<MktSeg> actualMktSegs = tenantCrudService().findAll(MktSeg.class);
        List<MktSeg> expectMktSegs = Arrays.stream(ArrayUtils.subarray(expectedMktSegTable, 1, expectedMktSegTable.length)).map(mktSegRow -> {
            Object[] mktSegArr = (Object[]) mktSegRow;
            MktSeg mktSeg = new MktSeg();
            mktSeg.setCode((String) mktSegArr[1]);
            mktSeg.setPropertyId((Integer) mktSegArr[0]);
            mktSeg.setStatusId(1);
            return mktSeg;
        }).collect(Collectors.toList());

        assertConfiguration(expectMktSegs, actualMktSegs,
                Comparator.comparing(MktSeg::getCode).thenComparing(MktSeg::getPropertyId),
                MktSeg.class,
                "mktSegDetails", "mktSegDetailsProposed", "editable", "name", "description");
    }

    private <C> void assertConfiguration(List<C> expectedConfiguration, List<C> actualConfiguration, Comparator<C> configComparator, Class configurationType, String... additionalExcludes) {
        String[] commonExcludes = {"id", "createDate", "createdByUserId", "lastUpdatedDate", "lastUpdatedByUserId"};
        String[] excludedFields = (String[]) ArrayUtils.addAll(commonExcludes, additionalExcludes);

        expectedConfiguration.sort(configComparator);
        actualConfiguration.sort(configComparator);

        boolean configurationMisMatch = StreamUtils.zip(
                        expectedConfiguration.stream(), actualConfiguration.stream(), Pair::of)
                .anyMatch(p -> !EqualsBuilder.reflectionEquals(p.getLeft(), p.getRight(), false, configurationType, excludedFields));

        assertFalse(configurationMisMatch, configurationType.getSimpleName() + " configuration did not match");
    }

    @Test
    void shouldGetTierMarketSegmentMappingTableRecordCount() {
        service.createTierMarketSegmentMappingTable();
        List<TierMarketSegmentMapping> records = service.getTierMarketSegmentMappingRecordCount();
        assertEquals(0, records.size(), "records count should be 0");

        tenantCrudService().executeUpdateByNativeQuery(
                "insert into Tier_Market_Segment_Mapping (Code_Type, Current_Code, New_Equivalent_Code, Discontinued, Is_Primary_Code_For_One_To_Many_Splits) " +
                        "values ('" + PMSMigrationMappingType.MARKET_SEGMENT + "','ABC','XYZ'," + null + "," + null + ")");

        records = service.getTierMarketSegmentMappingRecordCount();
        assertEquals(1, records.size(), "records count should be 1");
    }

    @Test
    void shouldGetMarketSegmentIdsDiscontinuedByClient() {
        setUpMSRecodingService();
        insertPMSMigrationMapping(CODE_TYPE_MS, OLD_MS_1, OLD_MS_1, "1");
        insertPMSMigrationMapping(CODE_TYPE_MS, OLD_MS_2, NEW_MS_2, "1");
        insertPMSMigrationMapping(CODE_TYPE_MS, OLD_MS_3, NEW_MS_3, "0");
        insertAnalyticalMktSeg(OLD_MS_1, OLD_RC_1_ASSIGNMENT_RULE, OLD_MAPPED_MARKET_CODE_1, RateCodeTypeEnum.STARTS_WITH, "0", ATTRIBUTE);
        insertAnalyticalMktSeg(OLD_MS_2, OLD_RC_2_ASSIGNMENT_RULE, OLD_MAPPED_MARKET_CODE_2, RateCodeTypeEnum.STARTS_WITH, "0", ATTRIBUTE);
        insertAnalyticalMktSeg(OLD_MS_3, OLD_RC_3, OLD_MAPPED_MARKET_CODE_3, RateCodeTypeEnum.EQUALS, "0", ATTRIBUTE);
        List<Integer> marketSegmentIdsDiscontinuedByClient = service.getMarketSegmentIdsDiscontinuedByClient();
        assertEquals(2, marketSegmentIdsDiscontinuedByClient.size());
    }

    @Test
    public void shouldFilterTierMarketSegmentsFromPMSMigrationMapping() {
        //GIVEN
        addMarketSegment("CASINO_CASH_11_QYL");
        addMarketSegment("CASINO_DEF");

        addAnalyticalMarketSegmentWith("LMWW", "BKGDOD", "LMWW_QYL",
                QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "EQUALS", "10");
        addAnalyticalMarketSegmentWith("LMWW", null, "LMWW_DEF",
                QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "DEFAULT", "999");
        addAnalyticalMarketSegmentWith("CSLI", "BKGDOD", "CASINO_CASH_11_QYL",
                QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "EQUALS", "10");
        addAnalyticalMarketSegmentWith("CSLI", null, "CASINO_DEF",
                QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "DEFAULT", "999");
        addAnalyticalMarketSegmentWith("CSTP", "BKGDOD", "CASINO_CASH_11_QYL",
                QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "EQUALS", "10");
        addAnalyticalMarketSegmentWith("CSTP", null, "CASINO_DEF",
                QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "DEFAULT", "999");

        insertPMSMigrationMapping(CODE_TYPE_MS, "CSLI", "CPERP", "0");
        insertPMSMigrationMapping(CODE_TYPE_MS, "CSTP", "CPERP", "0");
        insertPMSMigrationMapping(CODE_TYPE_MS, "LMWW", "LMWW_NEW", "0");

        tierMarketSegmentMappingCreationHelper.insertIntoTierMarketSegmentMapping("MARKET_SEGMENT_NON_GROUP", "CSLI", "CPERP");
        tierMarketSegmentMappingCreationHelper.insertIntoTierMarketSegmentMapping("MARKET_SEGMENT_NON_GROUP", "CSTP", "CPERP");
        //WHEN
        service.filterTierMarketSegmentFromNonTierMSRecodingConfiguration();
        //THEN
        List<PMSMigrationMapping> pmsMigrationMappings =
                tenantCrudService().findByNamedQuery(PMSMigrationMapping.ALL);
        assertEquals(1, pmsMigrationMappings.size());
        assertEquals("LMWW", pmsMigrationMappings.get(0).getCurrentCode());
        assertEquals("LMWW_NEW", pmsMigrationMappings.get(0).getNewEquivalentCode());
    }

    @Test
    public void shouldFilterTierMarketSegmentFromNonTierRateCodeShiftTable() {
        //GIVEN
        tenantCrudService().executeUpdateByNativeQuery(RateCodeShiftAssociation.CREATE_RATE_CODE_SHIFT_TABLE_DDL);
        tenantCrudService().executeUpdateByNativeQuery(RateCodeShiftAssociation.CLEAN_RATE_CODE_SHIFT_TABLE_QUERY);
        createRateCodeShiftAssociationForNonTier("BKGDOD", "LMWW", "LMHI");
        createRateCodeShiftAssociationForNonTier("BKGDOD", "CSLI", "LMHI");
        createRateCodeShiftAssociationForNonTier("BKGDOD", "CSTP", "LMHI");
        tierMarketSegmentMappingCreationHelper.insertIntoTierMarketSegmentMapping("MARKET_SEGMENT_NON_GROUP", "CSLI", "CPERP");
        tierMarketSegmentMappingCreationHelper.insertIntoTierMarketSegmentMapping("MARKET_SEGMENT_NON_GROUP", "CSTP", "CPERP");
        addAnalyticalMarketSegmentWith("LMWW", "BKGDOD", "LMWW_QYL",
                QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "EQUALS", "10");
        addAnalyticalMarketSegmentWith("LMWW", null, "LMWW_DEF",
                QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "DEFAULT", "999");
        addAnalyticalMarketSegmentWith("CSLI", "BKGDOD", "CASINO_CASH_11_QYL",
                QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "EQUALS", "10");
        addAnalyticalMarketSegmentWith("CSLI", null, "CASINO_DEF",
                QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "DEFAULT", "999");
        addAnalyticalMarketSegmentWith("CSTP", "BKGDOD", "CASINO_CASH_11_QYL",
                QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "EQUALS", "10");
        addAnalyticalMarketSegmentWith("CSTP", null, "CASINO_DEF",
                QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "DEFAULT", "999");
        //WHEN
        service.filterTierMarketSegmentFromNonTierMSRecodingConfiguration();
        //THEN
        List<RateCodeShiftAssociation> rateCodeShiftAssociations =
                tenantCrudService().findByNamedQuery(RateCodeShiftAssociation.ALL);
        assertEquals(1, rateCodeShiftAssociations.size());
        assertEquals("BKGDOD", rateCodeShiftAssociations.get(0).getRateCode());
        assertEquals("LMWW", rateCodeShiftAssociations.get(0).getPreviousMarketSegment());
        assertEquals("LMHI", rateCodeShiftAssociations.get(0).getNewMarketSegment());
    }

    @Test
    public void shouldFilterTierMarketSegmentFromNonTierBusinessTypeShiftTable() {
        //GIVEN
        tenantCrudService().executeUpdateByNativeQuery(MktSegRecodingBusinessTypeShift.CREATE_MKT_SEG_RECODING_BUSINESS_TYPE_SHIFT_TABLE_DDL);
        tenantCrudService().executeUpdateByNativeQuery(MktSegRecodingBusinessTypeShift.CLEAN_BUSINESS_TYPE_SHIFT_TABLE_QUERY);
        createMktSegRecodingBusinessTypeShiftAssociation("LMWW", "Transient", "Group");
        createMktSegRecodingBusinessTypeShiftAssociation("CSLI", "Transient", "Group");
        createMktSegRecodingBusinessTypeShiftAssociation("CSTP", "Transient", "Group");
        tierMarketSegmentMappingCreationHelper.insertIntoTierMarketSegmentMapping("MARKET_SEGMENT_NON_GROUP", "CSLI", "CPERP");
        tierMarketSegmentMappingCreationHelper.insertIntoTierMarketSegmentMapping("MARKET_SEGMENT_NON_GROUP", "CSTP", "CPERP");
        addAnalyticalMarketSegmentWith("LMWW", "BKGDOD", "LMWW_QYL",
                QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "EQUALS", "10");
        addAnalyticalMarketSegmentWith("LMWW", null, "LMWW_DEF",
                QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "DEFAULT", "999");
        addAnalyticalMarketSegmentWith("CSLI", "BKGDOD", "CASINO_CASH_11_QYL",
                QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "EQUALS", "10");
        addAnalyticalMarketSegmentWith("CSLI", null, "CASINO_DEF",
                QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "DEFAULT", "999");
        addAnalyticalMarketSegmentWith("CSTP", "BKGDOD", "CASINO_CASH_11_QYL",
                QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "EQUALS", "10");
        addAnalyticalMarketSegmentWith("CSTP", null, "CASINO_DEF",
                QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "DEFAULT", "999");
        //WHEN
        service.filterTierMarketSegmentFromNonTierMSRecodingConfiguration();
        //THEN
        List<Object[]> businessTypeShiftRows =
                tenantCrudService().findByNativeQuery("select * from MS_Recoding_Business_Type_Shift");
        assertEquals(1, businessTypeShiftRows.size());
        assertEquals("LMWW", businessTypeShiftRows.get(0)[1]);
        assertEquals("Transient", businessTypeShiftRows.get(0)[2]);
        assertEquals("Group", businessTypeShiftRows.get(0)[3]);
    }

    @Test
    public void shouldFilterTierMarketSegmentFromNonTierIgnoreMissingRateCodesTable() {
        //GIVEN
        tenantCrudService().executeUpdateByNativeQuery(IgnoredRateCode.CREATE_IGNORED_RATE_CODE_TABLE_DDL);
        tenantCrudService().executeUpdateByNativeQuery(IgnoredRateCode.CLEAN_IGNORED_RATE_CODE_TABLE_QUERY);
        createIgnoreMissingRateCodes("LMWW", "RC2");
        createIgnoreMissingRateCodes("CSLI", "BKGDOD");
        createIgnoreMissingRateCodes("CSTP", "BKGDOD");
        tierMarketSegmentMappingCreationHelper.insertIntoTierMarketSegmentMapping("MARKET_SEGMENT_NON_GROUP", "CSLI", "CPERP");
        tierMarketSegmentMappingCreationHelper.insertIntoTierMarketSegmentMapping("MARKET_SEGMENT_NON_GROUP", "CSTP", "CPERP");
        addAnalyticalMarketSegmentWith("LMWW", "BKGDOD", "LMWW_QYL",
                QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "EQUALS", "10");
        addAnalyticalMarketSegmentWith("LMWW", null, "LMWW_DEF",
                QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "DEFAULT", "999");
        addAnalyticalMarketSegmentWith("CSLI", "BKGDOD", "CASINO_CASH_11_QYL",
                QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "EQUALS", "10");
        addAnalyticalMarketSegmentWith("CSLI", null, "CASINO_DEF",
                QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "DEFAULT", "999");
        addAnalyticalMarketSegmentWith("CSTP", "BKGDOD", "CASINO_CASH_11_QYL",
                QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "EQUALS", "10");
        addAnalyticalMarketSegmentWith("CSTP", null, "CASINO_DEF",
                QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "DEFAULT", "999");
        //WHEN
        service.filterTierMarketSegmentFromNonTierMSRecodingConfiguration();
        //THEN
        List<IgnoredRateCode> ignoredRateCodes =
                tenantCrudService().findByNamedQuery(IgnoredRateCode.ALL);
        assertEquals(1, ignoredRateCodes.size());
        assertEquals("LMWW", ignoredRateCodes.get(0).getMarketSegment());
        assertEquals("RC2", ignoredRateCodes.get(0).getRateCode());
        assertEquals(IgnoredRateCode.MismatchType.MISSING, ignoredRateCodes.get(0).getMismatchType());
    }

    private void createIgnoreMissingRateCodes(final String marketCode, final String rateCode) {
        tenantCrudService().executeUpdateByNativeQuery("insert into PMS_Revamp_Ignored_Rate_Code(Market_Code, Rate_Code, Mismatch_Type) " +
                "values ('" + marketCode + "', '" + rateCode + "', '" + IgnoredRateCode.MismatchType.MISSING + "')");
    }

    @Test
    public void shouldGetMarketSegmentIdsFromRateCodeShiftAssociation() {
        //GIVEN
        setWorkContextProperty(TestProperty.H1);
        tenantCrudService().executeUpdateByNativeQuery(RateCodeShiftAssociation.CREATE_RATE_CODE_SHIFT_TABLE_DDL);
        tenantCrudService().executeUpdateByNativeQuery(RateCodeShiftAssociation.CLEAN_RATE_CODE_SHIFT_TABLE_QUERY);
        Integer MS1_QYL = addMarketSegment("MS1_QYL");
        Integer MS1_DEF = addMarketSegment("MS1_DEF");
        Integer MS2_QYL = addMarketSegment("MS2_QYL");
        Integer MS2_DEF = addMarketSegment("MS2_DEF");
        addAnalyticalMarketSegmentWith("MS1", "RC1", "MS1_QYL",
                QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "EQUALS", "10");
        addAnalyticalMarketSegmentWith("MS1", "RC2", "MS1_QYL",
                QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "EQUALS", "10");
        addAnalyticalMarketSegmentWith("MS1", null, "MS1_DEF",
                QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "DEFAULT", "999");
        addAnalyticalMarketSegmentWith("MS2", "RC1", "MS2_QYL",
                QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "EQUALS", "10");
        addAnalyticalMarketSegmentWith("MS2", "RC3", "MS2_QYL",
                QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "EQUALS", "10");
        addAnalyticalMarketSegmentWith("MS2", null, "MS2_DEF",
                QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "DEFAULT", "999");
        addPMSMigrationMappingWith("MARKET_SEGMENT", "MS1", "MS1", null);
        addPMSMigrationMappingWith("MARKET_SEGMENT", "MS2", "MS2", null);
        addPMSMigrationMappingWith("RATE_CODE", "RC1", "RC1", null);
        addPMSMigrationMappingWith("RATE_CODE", "RC2", "RC2", null);
        addPMSMigrationMappingWith("RATE_CODE", "RC3", "RC3", null);
        createRateCodeShiftAssociationForNonTier("RC1", "MS1", "MS2");
        //WHEN
        List<Integer> mktSegIdsForPaceBackFill = service.getMarketSegmentIdsFromRateCodeShiftAssociation();
        //THEN
        assertTrue(mktSegIdsForPaceBackFill.contains(MS1_QYL));
        assertTrue(mktSegIdsForPaceBackFill.contains(MS2_QYL));
    }

    @Test
    public void shouldFetchAllMSRecodingConfigsForAllClients() {
        //Given
        inject(service, "globalCrudService", globalCrudService());

        Client clientBSTN = new Client();
        clientBSTN.setId(TestClient.BSTN.getId());
        clientBSTN.setCode("BSTN");

        Client clientHilton = new Client();
        clientHilton.setId(TestClient.HILTON.getId());
        clientHilton.setCode("Hilton");

        Property propertyH1 = globalCrudService().find(Property.class, 5);
        propertyH1.setStatus(Status.INACTIVE);
        globalCrudService().save(propertyH1);

        Property propertyH2 = new Property();
        propertyH2.setId(TestProperty.H2.getId());
        propertyH2.setCode("H2");
        propertyH2.setClient(clientBSTN);

        Property propertyDALMAC = new Property();
        propertyDALMAC.setId(19);


        MktSegRecodingConfig config1 = getMSRecodingConfig(propertyH1, MktSegRecodingState.MKT_SEG_RECODING_NOT_STARTED, clientBSTN);
        MktSegRecodingConfig config2 = getMSRecodingConfig(propertyH2, MktSegRecodingState.MKT_SEG_RECODING_NOT_STARTED, clientBSTN);
        MktSegRecodingConfig config3 = getMSRecodingConfig(propertyDALMAC, MktSegRecodingState.MKT_SEG_RECODING_NOT_STARTED, clientHilton);

        globalCrudService().save(Arrays.asList(config1, config2, config3));

        List<Integer> expectedConfigurationIds = Arrays.asList(config2.getId(), config3.getId());
        //When
        List<MktSegRecodingConfig> actualConfigurations = service.fetchAllMSRecodingConfigsForAllClients();
        //Then
        List<Integer> actualConfigurationIds = actualConfigurations.stream().map(MktSegRecodingConfig::getId).collect(Collectors.toList());

        assertEquals(expectedConfigurationIds.size(), actualConfigurationIds.size());
        assertTrue(expectedConfigurationIds.containsAll(actualConfigurationIds));
    }

    private MktSegRecodingConfig getMSRecodingConfig(Property property, MktSegRecodingState mktSegRecodingNotStarted, Client client) {
        MktSegRecodingConfig mktSegRecodingConfig = new MktSegRecodingConfig();
        mktSegRecodingConfig.setClient(client);
        mktSegRecodingConfig.setProperty(property);
        mktSegRecodingConfig.setMktSegRecodingState(mktSegRecodingNotStarted);
        mktSegRecodingConfig.setOldSysDate(DateUtil.getDate(5, 4, 2018));
        return mktSegRecodingConfig;

    }

    private void createRateCodeShiftAssociationForNonTier(final String rateCode, final String previousMarketSegment, final String nextMarketSegment) {
        tenantCrudService().executeUpdateByNativeQuery("Insert into MS_Recoding_Rate_Code_Shift " +
                "values('" + rateCode + "', '" + previousMarketSegment + "', '" + nextMarketSegment + "')");
    }

    private void createMktSegRecodingBusinessTypeShiftAssociation(final String mktSegName, final String previousBusinessType, final String newBusinessType) {
        tenantCrudService().executeUpdateByNativeQuery(MktSegRecodingBusinessTypeShift.INSERT_BUSINESS_TYPE_SHIFT_RULES_QUERY_PREFIX +
                "('" + mktSegName + "', '" + previousBusinessType + "', '" + newBusinessType + "')");
    }

    private void setUpRequiredData() {
        setUpPMSMigrationMappingData();
        setUpRequiredMktSegData(MktSegInvalidationFromMktSegMigrationMappingScenarios.INVALIDATE_DISCONTINUED_MKT_SEG_SCENARIO[1]);
        tenantCrudService().executeUpdateByNativeQuery(NewAMSRule.CREATE_AMS_RULE_TABLE_DDL);
    }

    private void setUpRequiredMktSegData(Object[][] mktSegData) {
        List<MktSeg> newMktSegs = Arrays.stream(ArrayUtils.subarray(mktSegData, 1, mktSegData.length)).map(mktSegRow -> {
            Object[] mktSegArr = (Object[]) mktSegRow;
            MktSeg newMktSeg = new MktSeg();
            newMktSeg.setPropertyId(MktSegInvalidationFromMktSegMigrationMappingScenarios.VALID_PROPERTY_ID);
            newMktSeg.setCode((String) mktSegArr[1]);
            newMktSeg.setStatusId(1);
            return newMktSeg;
        }).collect(Collectors.toList());
        tenantCrudService().save(newMktSegs);
    }

    private void setUpPMSMigrationMappingData() {
        Object[][] migrationMappingData = MktSegInvalidationFromMktSegMigrationMappingScenarios.INVALIDATE_DISCONTINUED_MKT_SEG_SCENARIO[0];
        List<PMSMigrationMapping> newPmsMigrationMappingData = Arrays.stream(ArrayUtils.subarray(migrationMappingData, 1, migrationMappingData.length)).map(pmsMigrationMappingRow -> {
            Object[] pmsMigrationMappingArr = (Object[]) pmsMigrationMappingRow;
            PMSMigrationMapping newPmsMigrationMapping = new PMSMigrationMapping();
            newPmsMigrationMapping.setPropertyCode((String) pmsMigrationMappingArr[0]);
            newPmsMigrationMapping.setCodeType((PMSMigrationMappingType) pmsMigrationMappingArr[1]);
            newPmsMigrationMapping.setCurrentCode((String) pmsMigrationMappingArr[2]);
            newPmsMigrationMapping.setNewEquivalentCode((String) pmsMigrationMappingArr[3]);
            newPmsMigrationMapping.setDiscontinued((boolean) pmsMigrationMappingArr[4]);
            newPmsMigrationMapping.setIsPrimaryCodeForOneToManySplits((boolean) pmsMigrationMappingArr[5]);
            return newPmsMigrationMapping;
        }).collect(Collectors.toList());
        tenantCrudService().save(newPmsMigrationMappingData);
    }

    @Test
    public void shouldValidateAllConflictingRateCodesAreConfiguredWithPreferredMappedMarketCode() {
        createRateCodeShiftAssociationForTierWith("RC", "CPERP", "CASINO_QYL");
        Integer CASINO_QYL_ID = addMarketSegment("CASINO_QYL");
        Integer CASINO_CASH_1_QYL_ID = addMarketSegment("CASINO_CASH_1_QYL");
        Integer CASINO_DEF_ID = addMarketSegment("CASINO_DEF");
        addAnalyticalMarketSegmentWith("CSLI", "RC", "CASINO_QYL", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "EQUALS", "10");
        addAnalyticalMarketSegmentWith("CSTP", "RC", "CASINO_CASH_1_QYL", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "EQUALS", "10");
        addAnalyticalMarketSegmentWith("CSLI", null, "CASINO_DEF", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "DEFAULT", "999");
        addAnalyticalMarketSegmentWith("CSTP", null, "CASINO_DEF", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "DEFAULT", "999");
        addAnalyticalMarketSegmentWith("CSLI", "RC1", "CASINO_QYL", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "EQUALS", "10");
        addAnalyticalMarketSegmentWith("CSTP", "RC1", "CASINO_CASH_1_QYL", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "EQUALS", "10");
        addAnalyticalMarketSegmentWith("CSLI", null, "CASINO_DEF", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "DEFAULT", "999");
        addAnalyticalMarketSegmentWith("CSTP", null, "CASINO_DEF", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "DEFAULT", "999");
        tierMarketSegmentMappingCreationHelper.insertIntoTierMarketSegmentMapping("MARKET_SEGMENT_NON_GROUP", "CSLI", "CPERP");
        tierMarketSegmentMappingCreationHelper.insertIntoTierMarketSegmentMapping("MARKET_SEGMENT_NON_GROUP", "CSTP", "CPERP");
        //WHEN
        List<String> rateCodesNotMentionedWithPreference = service.verifyAllConflictingRateCodesAreMentionedWithPreferredMappedMarketCode();
        //THEN
        assertEquals(List.of("RC1"), rateCodesNotMentionedWithPreference);

    }

    @Test
    void shouldBeAbleToReplaceOriginalCodeWithTemporaryCode() {
        //GIVEN
        setUpServiceForAssigningMSToNewAMSRulesWithNoAttributeChange();
        addPMSMigrationMappingWith("MARKET_SEGMENT_NON_GROUP", "FAM", "fam", null);
        addPMSMigrationMappingWith("MARKET_SEGMENT_NON_GROUP", "CORP", "CORP", null);
        addPMSMigrationMappingWith("MARKET_SEGMENT_NON_GROUP", "GOV", "gov_n", null);
        addPMSMigrationMappingWith("RATE_CODE", "RC", "rc", null);

        addPMSRevampNewAMSRule("CORP", "RC4", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");
        addPMSRevampNewAMSRule("gov_n", null, QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");

        addPMSRevampNewAMSRule("fam-CS", "rc-CS", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");
        addPMSRevampNewAMSRule("fam-CS", "RC2", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");
        addPMSRevampNewAMSRule("fam-CS", "DEFAULT", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");

        //WHEN
        PMSMigrationConfig pmsMigrationConfig = new PMSMigrationConfig();
        pmsMigrationConfig.setUseGenericTemplateForMigration(true);
        when(pmsMigrationService.fetchOngoingMigrationConfiguration()).thenReturn(pmsMigrationConfig);
        service.replaceOriginalCodeWithTemporaryCode();
        //THEN
        flushAndClear();
        List<PMSMigrationMapping> pmsMigrationMappings = tenantCrudService().findByNamedQuery(PMSMigrationMapping.ALL);
        assertEquals("MARKET_SEGMENT_NON_GROUP", pmsMigrationMappings.get(0).getCodeType().name());
        assertEquals("FAM", pmsMigrationMappings.get(0).getCurrentCode());
        assertEquals("fam-CS", pmsMigrationMappings.get(0).getNewEquivalentCode());
        assertEquals("MARKET_SEGMENT_NON_GROUP", pmsMigrationMappings.get(1).getCodeType().name());
        assertEquals("CORP", pmsMigrationMappings.get(1).getCurrentCode());
        assertEquals("CORP", pmsMigrationMappings.get(1).getNewEquivalentCode());
        assertEquals("MARKET_SEGMENT_NON_GROUP", pmsMigrationMappings.get(2).getCodeType().name());
        assertEquals("GOV", pmsMigrationMappings.get(2).getCurrentCode());
        assertEquals("gov_n", pmsMigrationMappings.get(2).getNewEquivalentCode());

        List<NewAMSRule> newAMSRules = tenantCrudService().findByNamedQuery(NewAMSRule.ALL);
        assertEquals("fam-CS", newAMSRules.get(2).getMarketSegment());
        assertEquals("rc-CS", newAMSRules.get(2).getRateCode());

    }

    @Test
    void shouldBeAbleToReplaceOriginalCodeWithTemporaryCodeClientMigration() {
        //GIVEN
        setUpServiceForAssigningMSToNewAMSRulesWithNoAttributeChange();
        addPMSMigrationMappingWith("MARKET_SEGMENT_AGAINST_BLANK_RATE_CODE", "FAM", "fam", null);
        addPMSMigrationMappingWith("MARKET_SEGMENT_AGAINST_BLANK_RATE_CODE", "CORP", "CORP", null);
        addPMSMigrationMappingWith("MARKET_SEGMENT_AGAINST_BLANK_RATE_CODE", "GOV", "gov_n", null);
        addPMSMigrationMappingWith("RATE_CODE", "RC", "rc", null);
        addPMSRateCodeMktSegMapping("RC", "rc", "fam");

        addPMSRevampNewAMSRule("CORP", "RC4", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");
        addPMSRevampNewAMSRule("gov_n", null, QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");

        addPMSRevampNewAMSRule("fam-CS", "rc", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");
        addPMSRevampNewAMSRule("fam-CS", "RC2", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");
        addPMSRevampNewAMSRule("fam-CS", "DEFAULT", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");

        //WHEN
        PMSMigrationConfig pmsMigrationConfig = new PMSMigrationConfig();
        pmsMigrationConfig.setUseGenericTemplateForMigration(false);
        when(pmsMigrationService.fetchOngoingMigrationConfiguration()).thenReturn(pmsMigrationConfig);
        service.replaceOriginalCodeWithTemporaryCode();
        //THEN
        flushAndClear();
        List<Object[]> pmsMigrationMappings = tenantCrudService().findByNativeQuery("SELECT Code_Type, Current_Code, New_Equivalent_Code FROM PMS_MIGRATION_MAPPING;");
        assertEquals("MARKET_SEGMENT_AGAINST_BLANK_RATE_CODE", pmsMigrationMappings.get(1)[0]);
        assertEquals("FAM", pmsMigrationMappings.get(1)[1]);
        assertEquals("fam-CS", pmsMigrationMappings.get(1)[2]);
        assertEquals("MARKET_SEGMENT_AGAINST_BLANK_RATE_CODE", pmsMigrationMappings.get(0)[0]);
        assertEquals("CORP", pmsMigrationMappings.get(0)[1]);
        assertEquals("CORP", pmsMigrationMappings.get(0)[2]);
        assertEquals("MARKET_SEGMENT_AGAINST_BLANK_RATE_CODE", pmsMigrationMappings.get(2)[0]);
        assertEquals("GOV", pmsMigrationMappings.get(2)[1]);
        assertEquals("gov_n", pmsMigrationMappings.get(2)[2]);

        List<NewAMSRule> newAMSRules = tenantCrudService().findByNamedQuery(NewAMSRule.ALL);
        assertEquals("fam-CS", newAMSRules.get(2).getMarketSegment());
        assertEquals("rc-CS", newAMSRules.get(2).getRateCode());

        List<PMSMigrationRateCodeMktSegMapping> pmsMigrationRateCodeMktSegMappings = tenantCrudService().findByNamedQuery(PMSMigrationRateCodeMktSegMapping.ALL);
        assertEquals("fam-CS", pmsMigrationRateCodeMktSegMappings.get(0).getNewMarketSegment());
        assertEquals("rc-CS", pmsMigrationRateCodeMktSegMappings.get(0).getNewRateCode());

    }

    @Test
    void shouldBeAbleToReplaceTempCodeWithOriginalCode() {
        //GIVEN
        setUpServiceForAssigningMSToNewAMSRulesWithNoAttributeChange();
        tenantCrudService().executeUpdateByNativeQuery(PMSMigrationTemporaryMapping.CREATE_PMS_MIGRATION_TEMPORARY_MAPPING_TABLE_DDL);
        addPMSMigrationTempCodeMapping("MARKET_SEGMENT_NON_GROUP", "FAM", "fam", "fam-CS");
        addPMSMigrationTempCodeMapping("RATE_CODE", "RC", "rc", "rc-CS");

        addPMSMigrationMappingWith("MARKET_SEGMENT_NON_GROUP", "FAM", "fam-CS", null);
        addPMSMigrationMappingWith("MARKET_SEGMENT_NON_GROUP", "CORP", "CORP", null);
        addPMSMigrationMappingWith("MARKET_SEGMENT_NON_GROUP", "GOV", "gov_n", null);
        addPMSMigrationMappingWith("RATE_CODE", "RC", "rc-CS", null);

        addAnalyticalMarketSegmentWith("fam-CS", "rc-CS", "fam-CS_QYL", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "EQUALS", "10");
        addAnalyticalMarketSegmentWith("fam-CS", "RC2", "fam-CS_DEF", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "EQUALS", "10");
        addAnalyticalMarketSegmentWith("fam-CS", "", "fam-CS_DEF", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "EQUALS", "10");
        addAnalyticalMarketSegmentWith("CORP", "RC4", "CORP", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "EQUALS", "10");
        addAnalyticalMarketSegmentWith("GOV", null, "GOV", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "DEFAULT", "999");

        addPMSRevampNewAMSRule("CORP", "RC4", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");
        addPMSRevampNewAMSRule("gov_n", null, QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");

        addPMSRevampNewAMSRule("fam-CS", "rc-CS", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");
        addPMSRevampNewAMSRule("fam-CS", "RC2", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");
        addPMSRevampNewAMSRule("fam-CS", "DEFAULT", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");

        addMktSegMaster("fam-CS_QYL", 1);
        addMarketSegment("fam-CS_QYL");

        //WHEN - to replace Temp code with Original Code
        service.replaceTempCodeWithOriginalCode();
        //THEN
        flushAndClear();
        List<PMSMigrationMapping> pmsMigrationMappings = tenantCrudService().findByNamedQuery(PMSMigrationMapping.ALL);
        assertEquals("MARKET_SEGMENT_NON_GROUP", pmsMigrationMappings.get(0).getCodeType().name());
        assertEquals("FAM", pmsMigrationMappings.get(0).getCurrentCode());
        assertEquals("fam", pmsMigrationMappings.get(0).getNewEquivalentCode());
        assertEquals("MARKET_SEGMENT_NON_GROUP", pmsMigrationMappings.get(1).getCodeType().name());
        assertEquals("CORP", pmsMigrationMappings.get(1).getCurrentCode());
        assertEquals("CORP", pmsMigrationMappings.get(1).getNewEquivalentCode());
        assertEquals("MARKET_SEGMENT_NON_GROUP", pmsMigrationMappings.get(2).getCodeType().name());
        assertEquals("GOV", pmsMigrationMappings.get(2).getCurrentCode());
        assertEquals("gov_n", pmsMigrationMappings.get(2).getNewEquivalentCode());

        List<NewAMSRule> newAMSRules = tenantCrudService().findByNamedQuery(NewAMSRule.ALL);
        assertEquals("fam", newAMSRules.get(2).getMarketSegment());
        assertEquals("rc", newAMSRules.get(2).getRateCode());

        List<Object[]> amsList = tenantCrudService().findByNativeQuery("select * from Analytical_Mkt_Seg");
        assertEquals("fam", amsList.get(0)[1]);
        assertEquals("rc", amsList.get(0)[2]);
        assertEquals("fam_QYL", amsList.get(0)[3]);

        List<MktSeg> msList = tenantCrudService().findByNamedQuery(MktSeg.BY_CODE, QueryParameter.with("code", "fam_QYL").parameters());
        assertEquals(1, msList.size());
        List<MarketSegmentMaster> msMasterList = tenantCrudService().findByNamedQuery(MarketSegmentMaster.FIND_BY_CODE, QueryParameter.with("code", "fam_QYL").parameters());
        assertEquals(1, msMasterList.size());

    }

    @Test
    void shouldReplaceTempCodeWithOriginalCodeForMarketSegmentProductTableWhenMSRecodingSupportForIPIsEnabled() {
        setUpServiceForAssigningMSToNewAMSRulesWithNoAttributeChange();
        tenantCrudService().executeUpdateByNativeQuery(PMSMigrationTemporaryMapping.CREATE_PMS_MIGRATION_TEMPORARY_MAPPING_TABLE_DDL);
        addPMSMigrationTempCodeMapping("MARKET_SEGMENT_NON_GROUP", "FAM", "fam", "fam-CS");
        addPMSMigrationTempCodeMapping("RATE_CODE", "RC", "rc", "rc-CS");
        addPMSRevampNewAMSRule("fam-CS", "rc-CS", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");
        addPMSRevampNewAMSRule("fam-CS", "RC2", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");
        addPMSRevampNewAMSRule("fam-CS", "DEFAULT", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");
        addMktSegMaster("fam-CS_QYL", 1);
        addMarketSegment("fam-CS_QYL");
        addMarketSegmentProductMapping("fam-CS-QYL", addProduct("IND1"));
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_MS_RECODING_SUPPORT_FOR_INDEPENDENT_PRODUCT)).thenReturn(true);
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.INDEPENDENT_PRODUCTS_ENABLED)).thenReturn(true);

        service.replaceTempCodeWithOriginalCode();

        flushAndClear();
        List<NewAMSRule> newAMSRules = tenantCrudService().findByNamedQuery(NewAMSRule.ALL);
        newAMSRules.sort(Comparator.comparing(NewAMSRule::getRateCode));
        assertEquals("fam", newAMSRules.get(2).getMarketSegment());
        assertEquals("rc", newAMSRules.get(2).getRateCode());
        List<MktSeg> msList = tenantCrudService().findByNamedQuery(MktSeg.BY_CODE, QueryParameter.with("code", "fam_QYL").parameters());
        assertEquals(1, msList.size());
        List<MarketSegmentMaster> msMasterList = tenantCrudService().findByNamedQuery(MarketSegmentMaster.FIND_BY_CODE, QueryParameter.with("code", "fam_QYL").parameters());
        assertEquals(1, msMasterList.size());
        List<MarketSegmentProductMapping> mktSegProductMappings = tenantCrudService().findByNativeQuery("select * from Mkt_Seg_Product_Mapping where Mkt_Seg_Code like 'fam%'",
                Map.of(), MarketSegmentProductMapping.class);
        assertEquals(1, mktSegProductMappings.size());
        assertEquals("fam-QYL", mktSegProductMappings.get(0).getMarketSegmentCode());
        verify(pacmanConfigParamsService).getBooleanParameterValue(PreProductionConfigParamName.ENABLE_MS_RECODING_SUPPORT_FOR_INDEPENDENT_PRODUCT);
        verify(pacmanConfigParamsService).getBooleanParameterValue(PreProductionConfigParamName.INDEPENDENT_PRODUCTS_ENABLED);
    }

    @Test
    void shouldNotReplaceTempCodeWithOriginalCodeForMarketSegmentProductTableWhenIndependentProductToggleIsDisabled() {
        setUpServiceForAssigningMSToNewAMSRulesWithNoAttributeChange();
        tenantCrudService().executeUpdateByNativeQuery(PMSMigrationTemporaryMapping.CREATE_PMS_MIGRATION_TEMPORARY_MAPPING_TABLE_DDL);
        addPMSMigrationTempCodeMapping("MARKET_SEGMENT_NON_GROUP", "FAM", "fam", "fam-CS");
        addPMSMigrationTempCodeMapping("RATE_CODE", "RC", "rc", "rc-CS");
        addPMSRevampNewAMSRule("fam-CS", "rc-CS", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");
        addPMSRevampNewAMSRule("fam-CS", "RC2", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");
        addPMSRevampNewAMSRule("fam-CS", "DEFAULT", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");
        addMktSegMaster("fam-CS_QYL", 1);
        addMarketSegment("fam-CS_QYL");
        addMarketSegmentProductMapping("fam-CS-QYL", addProduct("IND1"));
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_MS_RECODING_SUPPORT_FOR_INDEPENDENT_PRODUCT)).thenReturn(true);
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.INDEPENDENT_PRODUCTS_ENABLED)).thenReturn(false);

        service.replaceTempCodeWithOriginalCode();

        flushAndClear();
        List<NewAMSRule> newAMSRules = tenantCrudService().findByNamedQuery(NewAMSRule.ALL);
        newAMSRules.sort(Comparator.comparing(NewAMSRule::getRateCode));
        assertEquals("fam", newAMSRules.get(2).getMarketSegment());
        assertEquals("rc", newAMSRules.get(2).getRateCode());
        List<MktSeg> msList = tenantCrudService().findByNamedQuery(MktSeg.BY_CODE, QueryParameter.with("code", "fam_QYL").parameters());
        assertEquals(1, msList.size());
        List<MarketSegmentMaster> msMasterList = tenantCrudService().findByNamedQuery(MarketSegmentMaster.FIND_BY_CODE, QueryParameter.with("code", "fam_QYL").parameters());
        assertEquals(1, msMasterList.size());
        List<MarketSegmentProductMapping> mktSegProductMappings = tenantCrudService().findByNativeQuery("select * from Mkt_Seg_Product_Mapping where Mkt_Seg_Code like 'fam-CS%'",
                Map.of(), MarketSegmentProductMapping.class);
        assertEquals(1, mktSegProductMappings.size());
        assertEquals("fam-CS-QYL", mktSegProductMappings.get(0).getMarketSegmentCode());
        verify(pacmanConfigParamsService).getBooleanParameterValue(PreProductionConfigParamName.ENABLE_MS_RECODING_SUPPORT_FOR_INDEPENDENT_PRODUCT);
        verify(pacmanConfigParamsService).getBooleanParameterValue(PreProductionConfigParamName.INDEPENDENT_PRODUCTS_ENABLED);
    }

    @Test
    void shouldReplaceTempCodeWithOriginalCodeForProductRateCodeTableWhenMSRecodingSupportForIPIsEnabled() {
        setUpServiceForAssigningMSToNewAMSRulesWithNoAttributeChange();
        tenantCrudService().executeUpdateByNativeQuery(PMSMigrationTemporaryMapping.CREATE_PMS_MIGRATION_TEMPORARY_MAPPING_TABLE_DDL);
        addPMSMigrationTempCodeMapping("MARKET_SEGMENT_NON_GROUP", "FAM", "fam", "fam-CS");
        addPMSMigrationTempCodeMapping("RATE_CODE", "RC", "rc", "rc-CS");
        addPMSRevampNewAMSRule("fam-CS", "rc-CS", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");
        addPMSRevampNewAMSRule("fam-CS", "RC2", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");
        addPMSRevampNewAMSRule("fam-CS", "DEFAULT", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");
        addMktSegMaster("fam-CS_QYL", 1);
        addMarketSegment("fam-CS_QYL");
        addProductRateCodeMapping("rc-CS", addProduct("IND1"));
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_MS_RECODING_SUPPORT_FOR_INDEPENDENT_PRODUCT)).thenReturn(true);
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.INDEPENDENT_PRODUCTS_ENABLED)).thenReturn(true);

        service.replaceTempCodeWithOriginalCode();

        flushAndClear();
        List<NewAMSRule> newAMSRules = tenantCrudService().findByNamedQuery(NewAMSRule.ALL);
        newAMSRules.sort(Comparator.comparing(NewAMSRule::getRateCode));
        assertEquals("fam", newAMSRules.get(2).getMarketSegment());
        assertEquals("rc", newAMSRules.get(2).getRateCode());
        List<MktSeg> msList = tenantCrudService().findByNamedQuery(MktSeg.BY_CODE, QueryParameter.with("code", "fam_QYL").parameters());
        assertEquals(1, msList.size());
        List<MarketSegmentMaster> msMasterList = tenantCrudService().findByNamedQuery(MarketSegmentMaster.FIND_BY_CODE, QueryParameter.with("code", "fam_QYL").parameters());
        assertEquals(1, msMasterList.size());
        List<ProductRateCode> productRateCodes = tenantCrudService().findByNativeQuery("select * from Product_Rate_Code where Rate_Code = 'rc'",
                Map.of(), ProductRateCode.class);
        assertEquals(1, productRateCodes.size());
        assertEquals("rc", productRateCodes.get(0).getRateCode());
        verify(pacmanConfigParamsService).getBooleanParameterValue(PreProductionConfigParamName.ENABLE_MS_RECODING_SUPPORT_FOR_INDEPENDENT_PRODUCT);
        verify(pacmanConfigParamsService).getBooleanParameterValue(PreProductionConfigParamName.INDEPENDENT_PRODUCTS_ENABLED);
    }

    @Test
    void shouldNotReplaceTempCodeWithOriginalCodeForProductRateCodeTableWhenIndependentProductToggleIsDisabled() {
        setUpServiceForAssigningMSToNewAMSRulesWithNoAttributeChange();
        tenantCrudService().executeUpdateByNativeQuery(PMSMigrationTemporaryMapping.CREATE_PMS_MIGRATION_TEMPORARY_MAPPING_TABLE_DDL);
        addPMSMigrationTempCodeMapping("MARKET_SEGMENT_NON_GROUP", "FAM", "fam", "fam-CS");
        addPMSMigrationTempCodeMapping("RATE_CODE", "RC", "rc", "rc-CS");
        addPMSRevampNewAMSRule("fam-CS", "rc-CS", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");
        addPMSRevampNewAMSRule("fam-CS", "RC2", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");
        addPMSRevampNewAMSRule("fam-CS", "DEFAULT", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");
        addMktSegMaster("fam-CS_QYL", 1);
        addMarketSegment("fam-CS_QYL");
        addProductRateCodeMapping("rc-CS", addProduct("IND1"));
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_MS_RECODING_SUPPORT_FOR_INDEPENDENT_PRODUCT)).thenReturn(true);
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.INDEPENDENT_PRODUCTS_ENABLED)).thenReturn(false);

        service.replaceTempCodeWithOriginalCode();

        flushAndClear();
        List<NewAMSRule> newAMSRules = tenantCrudService().findByNamedQuery(NewAMSRule.ALL);
        newAMSRules.sort(Comparator.comparing(NewAMSRule::getRateCode));
        assertEquals("fam", newAMSRules.get(2).getMarketSegment());
        assertEquals("rc", newAMSRules.get(2).getRateCode());
        List<MktSeg> msList = tenantCrudService().findByNamedQuery(MktSeg.BY_CODE, QueryParameter.with("code", "fam_QYL").parameters());
        assertEquals(1, msList.size());
        List<MarketSegmentMaster> msMasterList = tenantCrudService().findByNamedQuery(MarketSegmentMaster.FIND_BY_CODE, QueryParameter.with("code", "fam_QYL").parameters());
        assertEquals(1, msMasterList.size());
        List<ProductRateCode> productRateCodes = tenantCrudService().findByNativeQuery("select * from Product_Rate_Code where Rate_Code = 'rc-CS'",
                Map.of(), ProductRateCode.class);
        assertEquals(1, productRateCodes.size());
        assertEquals("rc-CS", productRateCodes.get(0).getRateCode());
        verify(pacmanConfigParamsService).getBooleanParameterValue(PreProductionConfigParamName.ENABLE_MS_RECODING_SUPPORT_FOR_INDEPENDENT_PRODUCT);
        verify(pacmanConfigParamsService).getBooleanParameterValue(PreProductionConfigParamName.INDEPENDENT_PRODUCTS_ENABLED);
    }

    @Test
    void shouldBeAbleToReplaceTempCodeWithOriginalCodeIfTwoMSHaveSuffixSubstring() {
        //GIVEN
        setUpServiceForAssigningMSToNewAMSRulesWithNoAttributeChange();
        tenantCrudService().executeUpdateByNativeQuery(PMSMigrationTemporaryMapping.CREATE_PMS_MIGRATION_TEMPORARY_MAPPING_TABLE_DDL);
        addPMSMigrationTempCodeMapping("MARKET_SEGMENT_NON_GROUP", "FAM", "fam", "fam-CS");
        addPMSMigrationTempCodeMapping("MARKET_SEGMENT_NON_GROUP", "AM", "am", "am-CS");

        addPMSMigrationMappingWith("MARKET_SEGMENT_NON_GROUP", "FAM", "fam-CS", null);
        addPMSMigrationMappingWith("MARKET_SEGMENT_NON_GROUP", "AM", "am-CS", null);

        addAnalyticalMarketSegmentWith("fam-CS", "RC", "fam-CS_QYL", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "EQUALS", "10");
        addAnalyticalMarketSegmentWith("fam-CS", "RC2", "fam-CS_DEF", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "EQUALS", "10");
        addAnalyticalMarketSegmentWith("fam-CS", "", "fam-CS_DEF", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "EQUALS", "10");
        addAnalyticalMarketSegmentWith("am-CS", "RC3", "am-CS_QYL", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "EQUALS", "10");
        addAnalyticalMarketSegmentWith("am-CS", "", "am-CS_DEF", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "DEFAULT", "999");

        addPMSRevampNewAMSRule("fam-CS", "RC", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");
        addPMSRevampNewAMSRule("fam-CS", "RC2", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");
        addPMSRevampNewAMSRule("fam-CS", "DEFAULT", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");
        addPMSRevampNewAMSRule("am-CS", "DEFAULT", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");
        addPMSRevampNewAMSRule("am-CS", "RC3", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");

        addMktSegMaster("fam-CS_QYL", 1);
        addMarketSegment("fam-CS_QYL");
        addMktSegMaster("am-CS_QYL", 1);
        addMarketSegment("am-CS_QYL");

        //WHEN - to replace Temp code with Original Code
        service.replaceTempCodeWithOriginalCode();
        //THEN
        flushAndClear();
        List<PMSMigrationMapping> pmsMigrationMappings = tenantCrudService().findByNamedQuery(PMSMigrationMapping.ALL);
        assertEquals("MARKET_SEGMENT_NON_GROUP", pmsMigrationMappings.get(0).getCodeType().name());
        assertEquals("FAM", pmsMigrationMappings.get(0).getCurrentCode());
        assertEquals("fam", pmsMigrationMappings.get(0).getNewEquivalentCode());
        assertEquals("MARKET_SEGMENT_NON_GROUP", pmsMigrationMappings.get(1).getCodeType().name());
        assertEquals("AM", pmsMigrationMappings.get(1).getCurrentCode());
        assertEquals("am", pmsMigrationMappings.get(1).getNewEquivalentCode());

        List<NewAMSRule> newAMSRules = tenantCrudService().findByNamedQuery(NewAMSRule.BY_MARKET_CODE,
                QueryParameter.with("marketCodes", List.of("fam", "am")).parameters());
        assertEquals(5, newAMSRules.size());
        assertEquals("fam", newAMSRules.get(0).getMarketSegment());
        assertEquals("RC", newAMSRules.get(0).getRateCode());
        assertEquals("am", newAMSRules.get(4).getMarketSegment());
        assertEquals("RC3", newAMSRules.get(4).getRateCode());

        List<AnalyticalMarketSegment> amsList = tenantCrudService().findByNamedQuery(AnalyticalMarketSegment.BY_MARKET_CODES,
                QueryParameter.with("marketCodes", List.of("fam", "am")).parameters());
        assertEquals(5, newAMSRules.size());
        assertEquals("am", amsList.get(0).getMarketCode());
        assertEquals("", amsList.get(0).getRateCode());
        assertEquals("am_DEF", amsList.get(0).getMappedMarketCode());
        assertEquals("fam", amsList.get(2).getMarketCode());
        assertEquals("", amsList.get(2).getRateCode());
        assertEquals("fam_DEF", amsList.get(2).getMappedMarketCode());

        MktSeg ms = tenantCrudService().findByNamedQuerySingleResult(MktSeg.BY_CODE, QueryParameter.with("code", "fam_QYL").parameters());
        assertNotNull(ms);
        ms = tenantCrudService().findByNamedQuerySingleResult(MktSeg.BY_CODE, QueryParameter.with("code", "am_QYL").parameters());
        assertNotNull(ms);
        MarketSegmentMaster msMaster = tenantCrudService().findByNamedQuerySingleResult(MarketSegmentMaster.FIND_BY_CODE, QueryParameter.with("code", "fam_QYL").parameters());
        assertNotNull(msMaster);
        msMaster = tenantCrudService().findByNamedQuerySingleResult(MarketSegmentMaster.FIND_BY_CODE, QueryParameter.with("code", "am_QYL").parameters());
        assertNotNull(msMaster);

    }

    @Test
    void shouldBeAbleToReplaceTempCodeWithOriginalCodeIfTwoMSHavePrefixSubstring() {
        setUpServiceForAssigningMSToNewAMSRulesWithNoAttributeChange();
        tenantCrudService().executeUpdateByNativeQuery(PMSMigrationTemporaryMapping.CREATE_PMS_MIGRATION_TEMPORARY_MAPPING_TABLE_DDL);
        addPMSMigrationTempCodeMapping("MARKET_SEGMENT_NON_GROUP", "FAM", "fam", "fam-CS");
        addPMSMigrationTempCodeMapping("MARKET_SEGMENT_NON_GROUP", "FA", "fa", "fa-CS");

        addPMSMigrationMappingWith("MARKET_SEGMENT_NON_GROUP", "FAM", "fam-CS", null);
        addPMSMigrationMappingWith("MARKET_SEGMENT_NON_GROUP", "FA", "fa-CS", null);

        addAnalyticalMarketSegmentWith("fam-CS", "RC", "fam-CS_QYL", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "EQUALS", "10");
        addAnalyticalMarketSegmentWith("fam-CS", "RC2", "fam-CS_DEF", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "EQUALS", "10");
        addAnalyticalMarketSegmentWith("fam-CS", "", "fam-CS_DEF", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "EQUALS", "10");
        addAnalyticalMarketSegmentWith("fa-CS", "RC4", "fa-CS_QYL", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "EQUALS", "10");
        addAnalyticalMarketSegmentWith("fa-CS", "", "fa-CS_DEF", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "DEFAULT", "999");

        addPMSRevampNewAMSRule("fam-CS", "RC", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");
        addPMSRevampNewAMSRule("fam-CS", "RC2", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");
        addPMSRevampNewAMSRule("fam-CS", "DEFAULT", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");
        addPMSRevampNewAMSRule("fa-CS", "DEFAULT", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");
        addPMSRevampNewAMSRule("fa-CS", "RC4", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");

        addMktSegMaster("fam-CS_QYL", 1);
        addMktSegMaster("FAM_QYL", 1);
        addMarketSegment("fam-CS_QYL");
        addMktSegMaster("fa-CS_QYL", 1);
        addMktSegMaster("FA_QYL", 1);
        addMarketSegment("fa-CS_QYL");

        //WHEN - to replace Temp code with Original Code
        service.replaceTempCodeWithOriginalCode();
        //THEN
        flushAndClear();
        List<PMSMigrationMapping> pmsMigrationMappings = tenantCrudService().findByNamedQuery(PMSMigrationMapping.ALL);
        assertEquals("MARKET_SEGMENT_NON_GROUP", pmsMigrationMappings.get(0).getCodeType().name());
        assertEquals("FAM", pmsMigrationMappings.get(0).getCurrentCode());
        assertEquals("fam", pmsMigrationMappings.get(0).getNewEquivalentCode());
        assertEquals("MARKET_SEGMENT_NON_GROUP", pmsMigrationMappings.get(1).getCodeType().name());
        assertEquals("FA", pmsMigrationMappings.get(1).getCurrentCode());
        assertEquals("fa", pmsMigrationMappings.get(1).getNewEquivalentCode());

        List<NewAMSRule> newAMSRules = tenantCrudService().findByNamedQuery(NewAMSRule.BY_MARKET_CODE,
                QueryParameter.with("marketCodes", List.of("fam", "fa")).parameters());
        assertEquals(5, newAMSRules.size());
        assertEquals("fam", newAMSRules.get(0).getMarketSegment());
        assertEquals("RC", newAMSRules.get(0).getRateCode());
        assertEquals("fa", newAMSRules.get(4).getMarketSegment());
        assertEquals("RC4", newAMSRules.get(4).getRateCode());

        List<AnalyticalMarketSegment> amsList = tenantCrudService().findByNamedQuery(AnalyticalMarketSegment.BY_MARKET_CODES,
                QueryParameter.with("marketCodes", List.of("fam", "fa")).parameters());
        assertEquals(5, newAMSRules.size());
        assertEquals("fa", amsList.get(0).getMarketCode());
        assertEquals("", amsList.get(0).getRateCode());
        assertEquals("fa_DEF", amsList.get(0).getMappedMarketCode());
        assertEquals("fam", amsList.get(2).getMarketCode());
        assertEquals("", amsList.get(2).getRateCode());
        assertEquals("fam_DEF", amsList.get(2).getMappedMarketCode());

        MktSeg ms = tenantCrudService().findByNamedQuerySingleResult(MktSeg.BY_CODE, QueryParameter.with("code", "fam_QYL").parameters());
        assertNotNull(ms);
        ms = tenantCrudService().findByNamedQuerySingleResult(MktSeg.BY_CODE, QueryParameter.with("code", "fa_QYL").parameters());
        assertNotNull(ms);

        MarketSegmentMaster msMaster = tenantCrudService().findByNamedQuerySingleResult(MarketSegmentMaster.FIND_BY_CODE, QueryParameter.with("code", "fam_QYL").parameters());
        assertNotNull(msMaster);
        msMaster = tenantCrudService().findByNamedQuerySingleResult(MarketSegmentMaster.FIND_BY_CODE, QueryParameter.with("code", "fa_QYL").parameters());
        assertNotNull(msMaster);

    }

    @Test
    void shouldBeAbleToReplaceTempCodeWithOriginalCodeIfTwoMSHaveWholeSubstring() {
        //GIVEN
        setUpServiceForAssigningMSToNewAMSRulesWithNoAttributeChange();
        tenantCrudService().executeUpdateByNativeQuery(PMSMigrationTemporaryMapping.CREATE_PMS_MIGRATION_TEMPORARY_MAPPING_TABLE_DDL);
        addPMSMigrationTempCodeMapping("MARKET_SEGMENT_NON_GROUP", "FAM", "fam", "fam-CS");
        addPMSMigrationTempCodeMapping("MARKET_SEGMENT_NON_GROUP", "FAM_NEW", "fam_new", "fam_new-CS");

        addPMSMigrationMappingWith("MARKET_SEGMENT_NON_GROUP", "FAM", "fam-CS", null);
        addPMSMigrationMappingWith("MARKET_SEGMENT_NON_GROUP", "FAM_NEW", "fam_new-CS", null);

        addAnalyticalMarketSegmentWith("fam-CS", "rc-CS", "fam-CS_QYL", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "EQUALS", "10");
        addAnalyticalMarketSegmentWith("fam-CS", "RC2", "fam-CS_DEF", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "EQUALS", "10");
        addAnalyticalMarketSegmentWith("fam-CS", "", "fam-CS_DEF", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "EQUALS", "10");
        addAnalyticalMarketSegmentWith("fam_new-CS", "RC1", "fam_new-CS_QYL", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "EQUALS", "10");
        addAnalyticalMarketSegmentWith("fam_new-CS", "", "fam_new-CS_DEF", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "DEFAULT", "999");

        addPMSRevampNewAMSRule("fam-CS", "RC", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");
        addPMSRevampNewAMSRule("fam-CS", "RC2", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");
        addPMSRevampNewAMSRule("fam-CS", "DEFAULT", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");
        addPMSRevampNewAMSRule("fam_new-CS", "DEFAULT", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");
        addPMSRevampNewAMSRule("fam_new-CS", "RC1", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1");

        insertOperaYCR("fam-CS", "RC", "fam-CS_QYL", 10);
        insertOperaYCR("fam-CS", "RC2", "fam-CS_QYL", 10);
        insertOperaYCR("fam-CS", null, "fam-CS_DEF", 999);

        addMktSegMaster("fam-CS_QYL", 1);
        addMktSegMaster("FAM_QYL", 1);
        addMarketSegment("fam-CS_QYL");
        addMktSegMaster("fam_new-CS_QYL", 1);
        addMktSegMaster("FAM_NEW_QYL", 1);
        addMarketSegment("fam_new-CS_QYL");

        //WHEN - to replace Temp code with Original Code
        service.replaceTempCodeWithOriginalCode();
        //THEN
        flushAndClear();
        List<PMSMigrationMapping> pmsMigrationMappings = tenantCrudService().findByNamedQuery(PMSMigrationMapping.ALL);
        assertEquals("MARKET_SEGMENT_NON_GROUP", pmsMigrationMappings.get(0).getCodeType().name());
        assertEquals("FAM", pmsMigrationMappings.get(0).getCurrentCode());
        assertEquals("fam", pmsMigrationMappings.get(0).getNewEquivalentCode());
        assertEquals("MARKET_SEGMENT_NON_GROUP", pmsMigrationMappings.get(1).getCodeType().name());
        assertEquals("FAM_NEW", pmsMigrationMappings.get(1).getCurrentCode());
        assertEquals("fam_new", pmsMigrationMappings.get(1).getNewEquivalentCode());

        List<NewAMSRule> newAMSRules = tenantCrudService().findByNamedQuery(NewAMSRule.BY_MARKET_CODE,
                QueryParameter.with("marketCodes", List.of("fam", "fam_new")).parameters());
        assertEquals(5, newAMSRules.size());
        assertEquals("fam", newAMSRules.get(0).getMarketSegment());
        assertEquals("RC", newAMSRules.get(0).getRateCode());
        assertEquals("fam_new", newAMSRules.get(4).getMarketSegment());
        assertEquals("RC1", newAMSRules.get(4).getRateCode());

        List<AnalyticalMarketSegment> amsList = tenantCrudService().findByNamedQuery(AnalyticalMarketSegment.BY_MARKET_CODES,
                QueryParameter.with("marketCodes", List.of("fam", "fam_new")).parameters());
        assertEquals(5, newAMSRules.size());
        assertEquals("fam", amsList.get(1).getMarketCode());
        assertEquals("RC2", amsList.get(1).getRateCode());
        assertEquals("fam_DEF", amsList.get(1).getMappedMarketCode());
        assertEquals("fam_new", amsList.get(4).getMarketCode());
        assertEquals("RC1", amsList.get(4).getRateCode());
        assertEquals("fam_new_QYL", amsList.get(4).getMappedMarketCode());

        MktSeg ms = tenantCrudService().findByNamedQuerySingleResult(MktSeg.BY_CODE, QueryParameter.with("code", "fam_QYL").parameters());
        assertNotNull(ms);
        ms = tenantCrudService().findByNamedQuerySingleResult(MktSeg.BY_CODE, QueryParameter.with("code", "fam_new_QYL").parameters());
        assertNotNull(ms);

        MarketSegmentMaster msMaster = tenantCrudService().findByNamedQuerySingleResult(MarketSegmentMaster.FIND_BY_CODE, QueryParameter.with("code", "fam_QYL").parameters());
        assertNotNull(msMaster);
        msMaster = tenantCrudService().findByNamedQuerySingleResult(MarketSegmentMaster.FIND_BY_CODE, QueryParameter.with("code", "fam_new_QYL").parameters());
        assertNotNull(msMaster);

        List<YieldCategoryRule> yieldCategoryRules = tenantCrudService().findByNamedQuery(YieldCategoryRule.ALL);
        assertEquals(3, yieldCategoryRules.size());
        assertEquals("fam_QYL", yieldCategoryRules.get(0).getAnalyticalMarketCode());
        assertEquals("fam_DEF", yieldCategoryRules.get(2).getAnalyticalMarketCode());

    }

    @Test
    void shouldGetMarketSegIdsForPaceBackFillWithRenamedCodesUsedInSplitScenario() {
        //GIVEN
        setUpPMSRevampAMSServiceForRecoding();
        tenantCrudService().executeUpdateByNativeQuery(PMSMigrationTemporaryMapping.CREATE_PMS_MIGRATION_TEMPORARY_MAPPING_TABLE_DDL);

        addPMSMigrationMappingWith("MARKET_SEGMENT", "CORP", "corpnew", null);
        addPMSMigrationMappingWith("MARKET_SEGMENT", "COMP", "compnew", null);
        addPMSMigrationMappingWith("MARKET_SEGMENT", "GC-MEET", "corpnew", "1");
        addPMSMigrationMappingWith("MARKET_SEGMENT", "GC-MEET", "compnew", "0");

        addPMSRevampNewAMSRule("corpnew", null, GROUP, "1");
        addPMSRevampNewAMSRule("compnew", null, GROUP, "1");

        addAnalyticalMarketSegmentWith("CORP", null, "CORP", GROUP, "ALL", "1");
        addAnalyticalMarketSegmentWith("COMP", null, "COMP", GROUP, "ALL", "1");
        addAnalyticalMarketSegmentWith("GC-MEET", null, "GC-MEET", GROUP, "ALL", "1");

        Integer corpnewMktSegId = addMarketSegment("corpnew");
        Integer compnewMktSegId = addMarketSegment("compnew");

        //WHEN
        List<Integer> mktSegIdsForPaceBackFill = service.getMktSegIdsEligibleForPaceBackFill();

        //THEN
        assertNotNull(mktSegIdsForPaceBackFill);
        assertTrue(isNotEmpty(mktSegIdsForPaceBackFill));
        assertTrue(mktSegIdsForPaceBackFill.contains(corpnewMktSegId), "CORP renamed to corpnew Market Segment Id should be in pace back fill");
        assertTrue(mktSegIdsForPaceBackFill.contains(compnewMktSegId), "COMP renamed to compnew Market Segment Id should be in pace back fill");

    }

    @Test
    public void isThisFirstExtract() {
        assertTrue(service.isThisFirstExtract());
    }

    @Test
    public void assignMktSegToNewAMSRules_MktSegNonGroup_MktSegRenameNoChangeInAttribution_IPProductAssignedToRenamedMarketCode() {
        setUpServiceForAssigningMSToNewAMSRulesWithNoAttributeChange();
        setUpToggleValueForIndependentProductSupportForMSRecoding();
        Product product1 = buildProduct();
        tenantCrudService().save(product1);
        addPMSMigrationMappingWith(CODE_TYPE_MS, "PRESS", "PRESSNEW", null);
        addPMSMigrationMappingWith(CODE_TYPE_RC, "RC1", "RC1", null);
        addPMSMigrationMappingWith(CODE_TYPE_RC, "RC2", "RC2", null);

        addAnalyticalMarketSegmentWith("PRESS", "RC1", "PRESS_QYL", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "EQUALS", "10");
        addAnalyticalMarketSegmentWith("PRESS", "RC2", "PRESS_QYL", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "EQUALS", "10");
        addAnalyticalMarketSegmentWith("PRESS", null, "PRESS_DEF", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "DEFAULT", "999");

        addPMSRevampNewAMSRuleWithProduct("PRESSNEW", "RC1", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1", product1.getId());
        addPMSRevampNewAMSRuleWithProduct("PRESSNEW", "RC2", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1", product1.getId());
        addPMSRevampNewAMSRuleWithProduct("PRESSNEW", "DEFAULT", QUALIFIED_NONBLOCK_LINKED_YIELDABLE, "1", product1.getId());

        Integer PRESS_QYL_MKT_SEG_ID = addMarketSegment("PRESS_QYL");
        Integer PRESS_DEF_MKT_SEG_ID = addMarketSegment("PRESS_DEF");
        addMktSegMaster("PRESS_QYL", 1);
        addMktSegMaster("PRESS_DEF", 1);

        getProductAssociatedWithMarketCode(product1);
        when(independentProductsRepository.getProduct(product1.getName())).thenReturn(product1);
        when(independentProductsService.createNewMapping(anyString(), any(Product.class), anyBoolean())).thenReturn(new MarketSegmentProductMapping());

        service.assignMktSegToNewAMSRules();

        tenantCrudService().flushAndClear();
        List<AnalyticalMarketSegment> analyticalMarketSegments = tenantCrudService().findAll(AnalyticalMarketSegment.class);
        List<MarketSegmentMaster> mktSegMasters = tenantCrudService().findAll(MarketSegmentMaster.class);

        assertTrue(analyticalMarketSegments.stream().anyMatch(ams -> ams.getMarketCode().equalsIgnoreCase("PRESSNEW") && ams.getRateCode() != null && ams.getRateCode().equals("RC1") &&
                ams.getMappedMarketCode().equalsIgnoreCase("PRESSNEW_IP" + product1.getId() + "_QYL") && ams.getAttribute().equals(AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE) &&
                ams.getRateCodeType().equals(RateCodeTypeEnum.EQUALS)));
        assertTrue(analyticalMarketSegments.stream().anyMatch(ams -> ams.getMarketCode().equalsIgnoreCase("PRESSNEW") && ams.getRateCode() != null && ams.getRateCode().equals("RC2") &&
                ams.getMappedMarketCode().equalsIgnoreCase("PRESSNEW_IP" + product1.getId() + "_QYL") && ams.getAttribute().equals(AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE) &&
                ams.getRateCodeType().equals(RateCodeTypeEnum.EQUALS)));
        assertTrue(analyticalMarketSegments.stream().anyMatch(ams -> ams.getMarketCode().equalsIgnoreCase("PRESS") && ams.getRateCode() != null && ams.getRateCode().equals("RC1") &&
                ams.getMappedMarketCode().equalsIgnoreCase("PRESS_QYL") && ams.getAttribute().equals(AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE) &&
                ams.getRateCodeType().equals(RateCodeTypeEnum.EQUALS)));
        assertTrue(analyticalMarketSegments.stream().anyMatch(ams -> ams.getMarketCode().equalsIgnoreCase("PRESS") && ams.getRateCode() != null && ams.getRateCode().equals("RC2") &&
                ams.getMappedMarketCode().equalsIgnoreCase("PRESS_QYL") && ams.getAttribute().equals(AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE) &&
                ams.getRateCodeType().equals(RateCodeTypeEnum.EQUALS)));
        assertTrue(mktSegMasters.stream().anyMatch(mktSegMaster -> mktSegMaster.getCode().equals("PRESSNEW_IP" + product1.getId() + "_QYL")));
        assertTrue(mktSegMasters.stream().anyMatch(mktSegMaster -> mktSegMaster.getCode().equals("PRESSNEW_DEF")));
    }

    @Test
    public void assignMktSegToNewAMSRules_MktSegNonGroup_MktSegRenameNoChangeInAttribution_IPProductNotChangedForRenamedMarketCode() {
        setUpServiceForAssigningMSToNewAMSRulesWithNoAttributeChange();
        setUpToggleValueForIndependentProductSupportForMSRecoding();
        Product product1 = buildProduct();
        product1.setName("IP1");
        tenantCrudService().save(product1);
        int product1Id = product1.getId();

        addPMSMigrationMappingWith(CODE_TYPE_MS, "OTA", "OTANEW", null);
        addPMSMigrationMappingWith(CODE_TYPE_RC, "RC1", "RC1", null);
        addPMSMigrationMappingWith(CODE_TYPE_RC, "RC2", "RC2", null);

        addAnalyticalMarketSegmentWith("OTA", "RC1", "OTA_IP" + product1Id + "_UF", FENCED, "EQUALS", "10");
        addAnalyticalMarketSegmentWith("OTA", "RC2", "OTA_IP" + product1Id + "_UF", FENCED, "EQUALS", "10");
        addAnalyticalMarketSegmentWith("OTA", null, "OTA_DEF", FENCED, "DEFAULT", "999");

        addPMSRevampNewAMSRuleWithProduct("OTANEW", "RC1", FENCED, "1", product1.getId());
        addPMSRevampNewAMSRuleWithProduct("OTANEW", "RC2", FENCED, "1", product1.getId());
        addPMSRevampNewAMSRuleWithProduct("OTANEW", "DEFAULT", FENCED, "1", product1.getId());
        Integer OTA_UF_MKT_SEG_ID = addMarketSegment("OTA_IP" + product1Id + "_UF");

        addMarketSegmentProductMapping("OTA_IP" + product1Id + "_UF", product1);
        addMarketSegmentProductMapping("OTA_DEF", product1);

        Integer OTA_DEF_MKT_SEG_ID = addMarketSegment("OTA_DEF");
        addMktSegMaster("OTA" + "_IP" + product1Id + "_UF", 1);
        addMktSegMaster("OTA_DEF", 1);

        getProductAssociatedWithMarketCode(product1);
        when(independentProductsRepository.getProduct(product1.getName())).thenReturn(product1);
        when(independentProductsService.createNewMapping(anyString(), any(Product.class), anyBoolean())).thenReturn(new MarketSegmentProductMapping());
        service.assignMktSegToNewAMSRules();

        tenantCrudService().flushAndClear();
        MktSeg OTANEW_IP_QY_MktSeg = tenantCrudService().find(MktSeg.class, OTA_UF_MKT_SEG_ID);
        MktSeg OTANEW_DEF_MktSeg = tenantCrudService().find(MktSeg.class, OTA_DEF_MKT_SEG_ID);
        List<AnalyticalMarketSegment> analyticalMarketSegments = tenantCrudService().findAll(AnalyticalMarketSegment.class);
        List<MarketSegmentMaster> mktSegMasters = tenantCrudService().findAll(MarketSegmentMaster.class);
        assertEquals("OTANEW_IP" + product1Id + "_UF", OTANEW_IP_QY_MktSeg.getCode());
        assertEquals("OTANEW_DEF", OTANEW_DEF_MktSeg.getCode());
        assertTrue(analyticalMarketSegments.stream().anyMatch(ams -> ams.getMarketCode().equalsIgnoreCase("OTANEW") && ams.getRateCode() != null && ams.getRateCode().equals("RC1") &&
                ams.getMappedMarketCode().equalsIgnoreCase("OTANEW_IP" + product1Id + "_UF") && ams.getAttribute().equals(AnalyticalMarketSegmentAttribute.FENCED) &&
                ams.getRateCodeType().equals(RateCodeTypeEnum.EQUALS)));
        assertTrue(analyticalMarketSegments.stream().anyMatch(ams -> ams.getMarketCode().equalsIgnoreCase("OTANEW") && ams.getRateCode() != null && ams.getRateCode().equals("RC2") &&
                ams.getMappedMarketCode().equalsIgnoreCase("OTANEW_IP" + product1Id + "_UF") && ams.getAttribute().equals(AnalyticalMarketSegmentAttribute.FENCED) &&
                ams.getRateCodeType().equals(RateCodeTypeEnum.EQUALS)));
        assertTrue(analyticalMarketSegments.stream().anyMatch(ams -> ams.getMarketCode().equalsIgnoreCase("OTA") && ams.getRateCode() != null && ams.getRateCode().equals("RC1") &&
                ams.getMappedMarketCode().equalsIgnoreCase("OTA_IP" + product1Id + "_UF") && ams.getAttribute().equals(AnalyticalMarketSegmentAttribute.FENCED) &&
                ams.getRateCodeType().equals(RateCodeTypeEnum.EQUALS)));
        assertTrue(analyticalMarketSegments.stream().anyMatch(ams -> ams.getMarketCode().equalsIgnoreCase("OTA") && ams.getRateCode() != null && ams.getRateCode().equals("RC2") &&
                ams.getMappedMarketCode().equalsIgnoreCase("OTA_IP" + product1Id + "_UF") && ams.getAttribute().equals(AnalyticalMarketSegmentAttribute.FENCED) &&
                ams.getRateCodeType().equals(RateCodeTypeEnum.EQUALS)));
        assertTrue(analyticalMarketSegments.stream().anyMatch(ams -> ams.getMarketCode().equalsIgnoreCase("OTANEW") &&
                ams.getMappedMarketCode().equalsIgnoreCase("OTANEW_DEF") && ams.getAttribute().equals(AnalyticalMarketSegmentAttribute.FENCED)));

        assertTrue(mktSegMasters.stream().anyMatch(mktSegMaster -> mktSegMaster.getCode().equals("OTANEW_IP" + product1Id + "_UF")));
        assertTrue(mktSegMasters.stream().anyMatch(mktSegMaster -> mktSegMaster.getCode().equals("OTANEW_DEF")));
    }

    @Test
    public void shouldRenameExistingMktSegNonGroupWhenMktSegRenameWithNoAttributionChangeAndIPProductNotChangedHavingBlankRateCodeAMS() {
        setUpServiceForAssigningMSToNewAMSRulesWithNoAttributeChange();
        setUpToggleValueForIndependentProductSupportForMSRecoding();
        Product product1 = buildProduct();
        product1.setName("IP1");
        tenantCrudService().save(product1);
        int product1Id = product1.getId();
        addPMSMigrationMappingWith(CODE_TYPE_MS, "OTA", "OTANEW", null);
        addPMSMigrationMappingWith(CODE_TYPE_RC, "RC1", "RC1", null);
        addPMSMigrationMappingWith(CODE_TYPE_RC, "RC2", "RC2", null);
        addAnalyticalMarketSegmentWith("OTA", "RC1", "OTA_IP" + product1Id + "_UF", FENCED, "EQUALS", "10");
        addAnalyticalMarketSegmentWith("OTA", "RC2", "OTA_IP" + product1Id + "_UF", FENCED, "EQUALS", "10");
        addAnalyticalMarketSegmentWith("OTA", null, "OTA_DEF", FENCED, "DEFAULT", "999");
        addAnalyticalMarketSegmentWith("OTA", "", "OTA_DEF", FENCED, "EQUALS", "10");
        addPMSRevampNewAMSRuleWithProduct("OTANEW", "RC1", FENCED, "1", product1.getId());
        addPMSRevampNewAMSRuleWithProduct("OTANEW", "RC2", FENCED, "1", product1.getId());
        addPMSRevampNewAMSRuleWithProduct("OTANEW", "DEFAULT", FENCED, "1", product1.getId());
        Integer OTA_UF_MKT_SEG_ID = addMarketSegment("OTA_IP" + product1Id + "_UF");
        addMarketSegmentProductMapping("OTA_IP" + product1Id + "_UF", product1);
        addMarketSegmentProductMapping("OTA_DEF", product1);
        Integer OTA_DEF_MKT_SEG_ID = addMarketSegment("OTA_DEF");
        addMktSegMaster("OTA" + "_IP" + product1Id + "_UF", 1);
        addMktSegMaster("OTA_DEF", 1);
        getProductAssociatedWithMarketCode(product1);
        when(independentProductsRepository.getProduct(product1.getName())).thenReturn(product1);
        when(independentProductsService.createNewMapping(anyString(), any(Product.class), anyBoolean())).thenReturn(new MarketSegmentProductMapping());

        service.assignMktSegToNewAMSRules();

        tenantCrudService().flushAndClear();
        MktSeg OTANEW_IP_QY_MktSeg = tenantCrudService().find(MktSeg.class, OTA_UF_MKT_SEG_ID);
        MktSeg OTANEW_DEF_MktSeg = tenantCrudService().find(MktSeg.class, OTA_DEF_MKT_SEG_ID);
        List<AnalyticalMarketSegment> analyticalMarketSegments = tenantCrudService().findAll(AnalyticalMarketSegment.class);
        assertEquals("OTANEW_IP" + product1Id + "_UF", OTANEW_IP_QY_MktSeg.getCode());
        assertEquals("OTANEW_DEF", OTANEW_DEF_MktSeg.getCode());
        assertTrue(analyticalMarketSegments.stream().anyMatch(ams -> ams.getMarketCode().equalsIgnoreCase("OTA") && ams.getRateCode() != null && ams.getRateCode().equals("RC1") &&
                ams.getMappedMarketCode().equalsIgnoreCase("OTA_IP" + product1Id + "_UF") && ams.getAttribute().equals(AnalyticalMarketSegmentAttribute.FENCED) &&
                ams.getRateCodeType().equals(RateCodeTypeEnum.EQUALS)));
        assertTrue(analyticalMarketSegments.stream().anyMatch(ams -> ams.getMarketCode().equalsIgnoreCase("OTA") && ams.getRateCode() != null && ams.getRateCode().equals("RC2") &&
                ams.getMappedMarketCode().equalsIgnoreCase("OTA_IP" + product1Id + "_UF") && ams.getAttribute().equals(AnalyticalMarketSegmentAttribute.FENCED) &&
                ams.getRateCodeType().equals(RateCodeTypeEnum.EQUALS)));
        assertTrue(analyticalMarketSegments.stream().anyMatch(ams -> ams.getMarketCode().equalsIgnoreCase("OTA") && ams.getRateCode() == null &&
                ams.getMappedMarketCode().equalsIgnoreCase("OTA_DEF") && ams.getAttribute().equals(AnalyticalMarketSegmentAttribute.FENCED) &&
                ams.getRateCodeType().equals(RateCodeTypeEnum.DEFAULT)));
        assertTrue(analyticalMarketSegments.stream().anyMatch(ams -> ams.getMarketCode().equalsIgnoreCase("OTA") && ams.getRateCode() != null && ams.getRateCode().equals("") &&
                ams.getMappedMarketCode().equalsIgnoreCase("OTA_DEF") && ams.getAttribute().equals(AnalyticalMarketSegmentAttribute.FENCED) &&
                ams.getRateCodeType().equals(RateCodeTypeEnum.EQUALS)));
        assertTrue(analyticalMarketSegments.stream().anyMatch(ams -> ams.getMarketCode().equalsIgnoreCase("OTANEW") && ams.getRateCode() != null && ams.getRateCode().equals("RC1") &&
                ams.getMappedMarketCode().equalsIgnoreCase("OTANEW_IP" + product1Id + "_UF") && ams.getAttribute().equals(AnalyticalMarketSegmentAttribute.FENCED) &&
                ams.getRateCodeType().equals(RateCodeTypeEnum.EQUALS)));
        assertTrue(analyticalMarketSegments.stream().anyMatch(ams -> ams.getMarketCode().equalsIgnoreCase("OTANEW") && ams.getRateCode() != null && ams.getRateCode().equals("RC2") &&
                ams.getMappedMarketCode().equalsIgnoreCase("OTANEW_IP" + product1Id + "_UF") && ams.getAttribute().equals(AnalyticalMarketSegmentAttribute.FENCED) &&
                ams.getRateCodeType().equals(RateCodeTypeEnum.EQUALS)));
        assertTrue(analyticalMarketSegments.stream().anyMatch(ams -> ams.getMarketCode().equalsIgnoreCase("OTANEW") &&
                ams.getMappedMarketCode().equalsIgnoreCase("OTANEW_DEF") && ams.getAttribute().equals(AnalyticalMarketSegmentAttribute.FENCED)));

        List<MarketSegmentMaster> mktSegMasters = tenantCrudService().findAll(MarketSegmentMaster.class);
        assertTrue(mktSegMasters.stream().anyMatch(mktSegMaster -> mktSegMaster.getCode().equals("OTANEW_IP" + product1Id + "_UF")));
        assertTrue(mktSegMasters.stream().anyMatch(mktSegMaster -> mktSegMaster.getCode().equals("OTANEW_DEF")));
    }

    @Test
    public void assignMktSegToNewAMSRules_MktSegNonGroup_MktSegRenameNoChangeInAttribution_IPProductIsChangedForRenamedMarketCode() {
        setUpServiceForAssigningMSToNewAMSRulesWithNoAttributeChange();
        setUpToggleValueForIndependentProductSupportForMSRecoding();
        Product product1 = buildProduct();
        product1.setName("IP1");
        tenantCrudService().save(product1);
        int product1Id = product1.getId();

        Product product2 = buildProduct();
        product1.setName("IP2");
        tenantCrudService().save(product2);
        int product2Id = product2.getId();

        addPMSMigrationMappingWith(CODE_TYPE_MS, "OTA", "OTANEW", null);
        addPMSMigrationMappingWith(CODE_TYPE_RC, "RC1", "RC1", null);
        addPMSMigrationMappingWith(CODE_TYPE_RC, "RC2", "RC2", null);

        addAnalyticalMarketSegmentWith("OTA", "RC1", "OTA_IP" + product1Id + "_UF", FENCED, "EQUALS", "10");
        addAnalyticalMarketSegmentWith("OTA", "RC2", "OTA_IP" + product1Id + "_UF", FENCED, "EQUALS", "10");
        addAnalyticalMarketSegmentWith("OTA", null, "OTA_DEF", FENCED, "DEFAULT", "999");

        addPMSRevampNewAMSRuleWithProduct("OTANEW", "RC1", FENCED, "1", product2.getId());
        addPMSRevampNewAMSRuleWithProduct("OTANEW", "RC2", FENCED, "1", product2.getId());
        addPMSRevampNewAMSRuleWithProduct("OTANEW", "DEFAULT", FENCED, "1", product2.getId());
        Integer OTA_UF_MKT_SEG_ID = addMarketSegment("OTA_IP" + product1Id + "_UF");

        addMarketSegmentProductMapping("OTA_IP" + product1Id + "_UF", product1);
        addMarketSegmentProductMapping("OTA_DEF", product1);

        Integer OTA_DEF_MKT_SEG_ID = addMarketSegment("OTA_DEF");
        addMktSegMaster("OTA" + "_IP" + product1Id + "_UF", 1);
        addMktSegMaster("OTA_DEF", 1);

        getProductAssociatedWithMarketCode(product1);
        when(independentProductsRepository.getProduct(product2.getName())).thenReturn(product2);
        when(independentProductsService.createNewMapping(anyString(), any(Product.class), anyBoolean())).thenReturn(new MarketSegmentProductMapping());
        service.assignMktSegToNewAMSRules();

        tenantCrudService().flushAndClear();
        List<AnalyticalMarketSegment> analyticalMarketSegments = tenantCrudService().findAll(AnalyticalMarketSegment.class);
        List<MarketSegmentMaster> mktSegMasters = tenantCrudService().findAll(MarketSegmentMaster.class);
        assertTrue(analyticalMarketSegments.stream().anyMatch(ams -> ams.getMarketCode().equalsIgnoreCase("OTANEW") && ams.getRateCode() != null && ams.getRateCode().equals("RC1") &&
                ams.getMappedMarketCode().equalsIgnoreCase("OTANEW_IP" + product2Id + "_UF") && ams.getAttribute().equals(AnalyticalMarketSegmentAttribute.FENCED) &&
                ams.getRateCodeType().equals(RateCodeTypeEnum.EQUALS)));
        assertTrue(analyticalMarketSegments.stream().anyMatch(ams -> ams.getMarketCode().equalsIgnoreCase("OTANEW") && ams.getRateCode() != null && ams.getRateCode().equals("RC2") &&
                ams.getMappedMarketCode().equalsIgnoreCase("OTANEW_IP" + product2Id + "_UF") && ams.getAttribute().equals(AnalyticalMarketSegmentAttribute.FENCED) &&
                ams.getRateCodeType().equals(RateCodeTypeEnum.EQUALS)));
        assertTrue(analyticalMarketSegments.stream().anyMatch(ams -> ams.getMarketCode().equalsIgnoreCase("OTA") && ams.getRateCode() != null && ams.getRateCode().equals("RC1") &&
                ams.getMappedMarketCode().equalsIgnoreCase("OTA_IP" + product1Id + "_UF") && ams.getAttribute().equals(AnalyticalMarketSegmentAttribute.FENCED) &&
                ams.getRateCodeType().equals(RateCodeTypeEnum.EQUALS)));
        assertTrue(analyticalMarketSegments.stream().anyMatch(ams -> ams.getMarketCode().equalsIgnoreCase("OTA") && ams.getRateCode() != null && ams.getRateCode().equals("RC2") &&
                ams.getMappedMarketCode().equalsIgnoreCase("OTA_IP" + product1Id + "_UF") && ams.getAttribute().equals(AnalyticalMarketSegmentAttribute.FENCED) &&
                ams.getRateCodeType().equals(RateCodeTypeEnum.EQUALS)));
        assertTrue(analyticalMarketSegments.stream().anyMatch(ams -> ams.getMarketCode().equalsIgnoreCase("OTANEW") &&
                ams.getMappedMarketCode().equalsIgnoreCase("OTANEW_DEF") && ams.getAttribute().equals(AnalyticalMarketSegmentAttribute.FENCED)));

        assertTrue(mktSegMasters.stream().anyMatch(mktSegMaster -> mktSegMaster.getCode().equals("OTANEW_IP" + product2Id + "_UF")));
        assertTrue(mktSegMasters.stream().anyMatch(mktSegMaster -> mktSegMaster.getCode().equals("OTANEW_DEF")));
    }

    public static Product buildProduct() {
        Product product = new Product();
        ProductCode pc = new ProductCode(2);
        product.setName("BAR");
        product.setCode("INDEPENDENT");
        product.setType("DAILY");
        product.setDependentProductId(2);
        product.setDescription("Test");
        product.setMinDTA(5);
        product.setMaxDTA(14);
        product.setMinLOS(3);
        product.setMaxLOS(21);
        product.setOffsetForExtraAdult(true);
        product.setOffsetForExtraChild(true);
        product.setDowOffset(true);
        product.setRoomClassOffset(true);
        product.setDtaOffset(true);
        product.setUpload(true);
        product.setStatus(TenantStatusEnum.ACTIVE);
        product.setDefaultInactive(false);
        product.setOptimized(false);
        product.setFloor(BigDecimal.TEN);
        product.setRoundingRule(PRICE_ROUNDING);
        product.setPubliclyAvailable(true);
        product.setMinimumPriceChange(BigDecimal.ONE);
        product.setSystemDefault(false);
        product.setDecisionsSentBy(AgileRatesDecisionsSentBy.PRICE);
        product.setCentrallyManaged(false);
        product.setIsOverridable(OverridableProductEnum.ALLOW_OVERRIDES);
        product.setFloorType(FloorType.FIXED_RATE);
        product.setFloorPercentage(BigDecimal.ONE);
        product.setRateShoppingLOSMin(-1);
        product.setRateShoppingLOSMax(-1);
        product.setProductCode(pc);
        product.setMaxRooms(-1);
        product.setMinRooms(-1);
        product.setUseInSmallGroupEval(false);
        product.setFreeNightEnabled(false);
        product.setFreeUpgradeEnabled(false);

        return product;
    }

    private void setUpToggleValueForIndependentProductSupportForMSRecoding() {
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_MS_RECODING_SUPPORT_FOR_INDEPENDENT_PRODUCT))
                .thenReturn(true);
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.INDEPENDENT_PRODUCTS_ENABLED)).thenReturn(true);
    }

    private void getProductAssociatedWithMarketCode(Product product) {
        when(independentProductsService.getProductMappedToMarketSegment(anyString())).thenReturn(Optional.ofNullable(product));
    }
}
