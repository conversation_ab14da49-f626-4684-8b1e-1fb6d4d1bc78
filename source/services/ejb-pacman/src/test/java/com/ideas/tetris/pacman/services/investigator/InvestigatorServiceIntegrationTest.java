package com.ideas.tetris.pacman.services.investigator;


import com.ideas.g3.test.AbstractG3JupiterTest;
import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.configparams.IPConfigParamName;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomType;
import com.ideas.tetris.pacman.services.accommodation.entity.UniqueAccomTypeCreator;
import com.ideas.tetris.pacman.services.accommodation.entity.UniqueOccupancyDemandForecastCreator;
import com.ideas.tetris.pacman.services.accommodation.service.AccommodationService;
import com.ideas.tetris.pacman.services.bestavailablerate.DecisionOverrideType;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.AccomActivity;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.CPDecisionBAROutput;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.Decision;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.OptimalBarType;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.UniqueDecisionCreator;
import com.ideas.tetris.pacman.services.investigator.dto.InvestigatorCompetitorChartDto;
import com.ideas.tetris.pacman.services.investigator.dto.InvestigatorRoomsInfoDto;
import com.ideas.tetris.pacman.services.investigator.dto.PriceRankingChartDto;
import com.ideas.tetris.pacman.services.investigator.dto.RemainingDemandByForeCastTypeDto;
import com.ideas.tetris.pacman.services.overbooking.entity.DecisionOvrbkAccom;
import com.ideas.tetris.pacman.services.pricingconfiguration.service.PricingConfigurationService;
import com.ideas.tetris.pacman.services.product.Product;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.time.JavaLocalDateUtils;
import com.ideas.tetris.platform.common.time.LocalDateUtils;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.Mockito;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;
import java.util.List;
import static java.math.RoundingMode.HALF_UP;
import static org.junit.jupiter.api.Assertions.assertEquals;

public class InvestigatorServiceIntegrationTest extends AbstractG3JupiterTest {


    InvestigatorService investigatorService = new InvestigatorService();

    @Mock
    PacmanConfigParamsService configParamsService;

    @BeforeEach
    public void setup() {
        AccommodationService accommodationService = new AccommodationService();
        accommodationService.setTenantCrudService(tenantCrudService());
        investigatorService.setTenantCrudService(tenantCrudService());
        investigatorService.setAccommodationService(accommodationService);
        investigatorService.setConfigParamsService(configParamsService);
        Mockito.lenient().when(configParamsService.getBooleanParameterValue(IPConfigParamName.USE_COMPACT_WEBRATE_PACE)).thenReturn(false);
        Mockito.lenient().when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.IS_RDL_ENABLED)).thenReturn(false);
        PricingConfigurationService pricingConfigurationService = new PricingConfigurationService();
        pricingConfigurationService.setTenantCrudService(tenantCrudService());
        investigatorService.setPricingConfigurationService(pricingConfigurationService);
    }

    @Test
    public void testFetchAvarageofCompititorsRate() {
        preparedDataForFetchAverageOfCompititorsRate(1);
        List<InvestigatorCompetitorChartDto> dtos = investigatorService.fetchAverageOfCompetitorsRateByOccupancy(JavaLocalDateUtils.toDate(LocalDate.now()), JavaLocalDateUtils.toDate(LocalDate.now()), 3, 1);
        assertEquals(4, dtos.size());
        assertEquals(new BigDecimal(95).setScale(2), dtos.get(0).getRate());
        assertEquals(JavaLocalDateUtils.toDate(LocalDate.now()), dtos.get(0).getOccupancyDt());
        assertEquals(new BigDecimal(92).setScale(2), dtos.get(1).getRate());
        assertEquals(JavaLocalDateUtils.toDate(LocalDate.now().minusDays(1)), dtos.get(1).getOccupancyDt());
        assertEquals(new BigDecimal(110).setScale(2), dtos.get(2).getRate());
        assertEquals(JavaLocalDateUtils.toDate(LocalDate.now().minusDays(2)), dtos.get(2).getOccupancyDt());
        assertEquals(new BigDecimal(110).setScale(2), dtos.get(3).getRate());
        assertEquals(JavaLocalDateUtils.toDate(LocalDate.now().minusDays(3)), dtos.get(3).getOccupancyDt());
    }

    @Test
    public void testFetchAvarageofCompititorsRateByLos() {
        preparedDataForFetchAverageOfCompititorsRate(2);
        List<InvestigatorCompetitorChartDto> dtos = investigatorService.fetchAverageOfCompetitorsRateByOccupancy(JavaLocalDateUtils.toDate(LocalDate.now()), JavaLocalDateUtils.toDate(LocalDate.now()), 3, 2);
        assertEquals(4, dtos.size());
        assertEquals(new BigDecimal(95).setScale(2), dtos.get(0).getRate());
        assertEquals(JavaLocalDateUtils.toDate(LocalDate.now()), dtos.get(0).getOccupancyDt());
        assertEquals(new BigDecimal(92).setScale(2), dtos.get(1).getRate());
        assertEquals(JavaLocalDateUtils.toDate(LocalDate.now().minusDays(1)), dtos.get(1).getOccupancyDt());
        assertEquals(new BigDecimal(110).setScale(2), dtos.get(2).getRate());
        assertEquals(JavaLocalDateUtils.toDate(LocalDate.now().minusDays(2)), dtos.get(2).getOccupancyDt());
        assertEquals(new BigDecimal(110).setScale(2), dtos.get(3).getRate());
        assertEquals(JavaLocalDateUtils.toDate(LocalDate.now().minusDays(3)), dtos.get(3).getOccupancyDt());
    }

    @Test
    public void testFetchAvarageofCompititorsRate_Demand_Enable_False() {
        tenantCrudService().executeUpdateByNativeQuery("update Webrate_Competitors_Class set Demand_Enabled=0 where Accom_Class_ID = 3");
        preparedDataForFetchAverageOfCompititorsRate(1);
        List<InvestigatorCompetitorChartDto> dtos = investigatorService.fetchAverageOfCompetitorsRateByOccupancy(JavaLocalDateUtils.toDate(LocalDate.now()), JavaLocalDateUtils.toDate(LocalDate.now()), 3, 1);
        assertEquals(0, dtos.size());

    }

    @Test
    public void testFetchAvarageofCompititorsRate_Competitor_Not_Checked() {
        tenantCrudService().executeUpdateByNativeQuery("update Webrate_Competitors set Status_ID =2 where Webrate_Competitors_ID = 1");
        preparedDataForFetchAverageOfCompititorsRate(1);
        List<InvestigatorCompetitorChartDto> dtos = investigatorService.fetchAverageOfCompetitorsRateByOccupancy(JavaLocalDateUtils.toDate(LocalDate.now()), JavaLocalDateUtils.toDate(LocalDate.now()), 3, 1);
        assertEquals(new BigDecimal(75).setScale(2), dtos.get(0).getRate());
        assertEquals(JavaLocalDateUtils.toDate(LocalDate.now()), dtos.get(0).getOccupancyDt());
        assertEquals(new BigDecimal(97).setScale(2), dtos.get(1).getRate());
        assertEquals(JavaLocalDateUtils.toDate(LocalDate.now().minusDays(1)), dtos.get(1).getOccupancyDt());
        assertEquals(new BigDecimal(100).setScale(2), dtos.get(2).getRate());
        assertEquals(JavaLocalDateUtils.toDate(LocalDate.now().minusDays(2)), dtos.get(2).getOccupancyDt());
        assertEquals(new BigDecimal(100).setScale(2), dtos.get(3).getRate());
        assertEquals(JavaLocalDateUtils.toDate(LocalDate.now().minusDays(3)), dtos.get(3).getOccupancyDt());

    }


    @Test
    public void testFetchAverageOfCompetitorsRateForSeason() {
        addSeasonForRateShopping();
        preparedDataForFetchAverageOfCompititorsRate(1);
        List<InvestigatorCompetitorChartDto> dtos = investigatorService.fetchAverageOfCompetitorsRateByOccupancy(JavaLocalDateUtils.toDate(LocalDate.now()), JavaLocalDateUtils.toDate(LocalDate.now()), 3, 1);
        assertEquals(4, dtos.size());
        assertEquals(new BigDecimal(95).setScale(2), dtos.get(0).getRate());
        assertEquals(JavaLocalDateUtils.toDate(LocalDate.now()), dtos.get(0).getOccupancyDt());
        assertEquals(new BigDecimal(92).setScale(2), dtos.get(1).getRate());
        assertEquals(JavaLocalDateUtils.toDate(LocalDate.now().minusDays(1)), dtos.get(1).getOccupancyDt());
        assertEquals(new BigDecimal(110).setScale(2), dtos.get(2).getRate());
        assertEquals(JavaLocalDateUtils.toDate(LocalDate.now().minusDays(2)), dtos.get(2).getOccupancyDt());
        assertEquals(new BigDecimal(133).setScale(2), dtos.get(3).getRate());
        assertEquals(JavaLocalDateUtils.toDate(LocalDate.now().minusDays(3)), dtos.get(3).getOccupancyDt());
    }

    @Test
    public void testFetchAverageOfCompetitorsRateForMultipleSeason() {
        addSeasonForRateShopping();
        tenantCrudService().executeUpdateByNativeQuery("insert into  Webrate_Override_Channel values(5,'test2',DATEADD(DAY, -1, SYSDATETIME()),DATEADD(DAY, -1, SYSDATETIME()),1,1,1,1,1,1,1,1,SYSDATETIME(),1,SYSDATETIME(), 1)");
        preparedDataForFetchAverageOfCompititorsRate(1);
        List<InvestigatorCompetitorChartDto> dtos = investigatorService.fetchAverageOfCompetitorsRateByOccupancy(JavaLocalDateUtils.toDate(LocalDate.now()), JavaLocalDateUtils.toDate(LocalDate.now()), 3, 1);
        assertEquals(4, dtos.size());
        assertEquals(new BigDecimal(95).setScale(2), dtos.get(0).getRate());
        assertEquals(JavaLocalDateUtils.toDate(LocalDate.now()), dtos.get(0).getOccupancyDt());
        assertEquals(new BigDecimal(155).setScale(2), dtos.get(1).getRate());
        assertEquals(JavaLocalDateUtils.toDate(LocalDate.now().minusDays(1)), dtos.get(1).getOccupancyDt());
        assertEquals(new BigDecimal(110).setScale(2), dtos.get(2).getRate());
        assertEquals(JavaLocalDateUtils.toDate(LocalDate.now().minusDays(2)), dtos.get(2).getOccupancyDt());
        assertEquals(new BigDecimal(133).setScale(2), dtos.get(3).getRate());
        assertEquals(JavaLocalDateUtils.toDate(LocalDate.now().minusDays(3)), dtos.get(3).getOccupancyDt());
    }

    @Test
    public void testFetchAvarageofCompititorsRate_Differential() {
        Mockito.when(configParamsService.getBooleanParameterValue(IPConfigParamName.USE_COMPACT_WEBRATE_PACE)).thenReturn(true);
        preparedDifferentialDataForFetchAverageOfCompetitorsRate(1);
        List<InvestigatorCompetitorChartDto> dtos = investigatorService.fetchAverageOfCompetitorsRateByOccupancy(JavaLocalDateUtils.toDate(LocalDate.now()), JavaLocalDateUtils.toDate(LocalDate.now()), 3, 1);
        assertEquals(4, dtos.size());
        assertEquals(new BigDecimal(95).setScale(2), dtos.get(0).getRate());
        assertEquals(JavaLocalDateUtils.toDate(LocalDate.now()), dtos.get(0).getOccupancyDt());
        assertEquals(new BigDecimal(92).setScale(2), dtos.get(1).getRate());
        assertEquals(JavaLocalDateUtils.toDate(LocalDate.now().minusDays(1)), dtos.get(1).getOccupancyDt());
        assertEquals(new BigDecimal(110).setScale(2), dtos.get(2).getRate());
        assertEquals(JavaLocalDateUtils.toDate(LocalDate.now().minusDays(2)), dtos.get(2).getOccupancyDt());
        assertEquals(new BigDecimal(110).setScale(2), dtos.get(3).getRate());
        assertEquals(JavaLocalDateUtils.toDate(LocalDate.now().minusDays(3)), dtos.get(3).getOccupancyDt());
    }

    @Test
    public void testFetchAvarageofCompititorsRate_Differential_With_RateType() {
        Mockito.when(configParamsService.getBooleanParameterValue(IPConfigParamName.USE_COMPACT_WEBRATE_PACE)).thenReturn(true);
        Mockito.when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.IS_RDL_ENABLED)).thenReturn(true);
        preparedDifferentialDataForFetchAverageOfCompetitorsRate(1);
        createWebrateCompetitorsClass(1, 3, 1, 1);
        List<InvestigatorCompetitorChartDto> dtos = investigatorService.fetchAverageOfCompetitorsRateByOccupancy(JavaLocalDateUtils.toDate(LocalDate.now()), JavaLocalDateUtils.toDate(LocalDate.now()), 3, 1);
        assertEquals(0, dtos.size());
    }

    private void createWebrateCompetitorsClass(int competitorId, int accomClassId, int demandEnabled, int rankingEnabled) {
        int affectedRows = tenantCrudService().executeUpdateByNativeQuery("update Webrate_Competitors_Class set Demand_Enabled = :demandEnabled, Ranking_Enabled = :rankingEnabled  " +
                        "where Webrate_Competitors_ID = :competitorId and Accom_Class_ID = :accomClassId ",
                QueryParameter.with("competitorId", competitorId)
                        .and("accomClassId", accomClassId)
                        .and("demandEnabled", demandEnabled)
                        .and("rankingEnabled", rankingEnabled)
                        .parameters());
        if (affectedRows <= 0) {
            tenantCrudService().executeUpdateByNativeQuery("insert into Webrate_Competitors_Class values(" + competitorId + ", "
                    + accomClassId + ", " + demandEnabled + ", 1, 1, GETDATE(), 1, GETDATE(), 1, NULL)");
        }
    }

    @Test
    public void testFetchAvarageofCompititorsRateByLos_Differential() {
        Mockito.when(configParamsService.getBooleanParameterValue(IPConfigParamName.USE_COMPACT_WEBRATE_PACE)).thenReturn(true);
        preparedDifferentialDataForFetchAverageOfCompetitorsRate(2);
        List<InvestigatorCompetitorChartDto> dtos = investigatorService.fetchAverageOfCompetitorsRateByOccupancy(JavaLocalDateUtils.toDate(LocalDate.now()), JavaLocalDateUtils.toDate(LocalDate.now()), 3, 2);
        assertEquals(4, dtos.size());
        assertEquals(new BigDecimal(95).setScale(2), dtos.get(0).getRate());
        assertEquals(JavaLocalDateUtils.toDate(LocalDate.now()), dtos.get(0).getOccupancyDt());
        assertEquals(new BigDecimal(92).setScale(2), dtos.get(1).getRate());
        assertEquals(JavaLocalDateUtils.toDate(LocalDate.now().minusDays(1)), dtos.get(1).getOccupancyDt());
        assertEquals(new BigDecimal(110).setScale(2), dtos.get(2).getRate());
        assertEquals(JavaLocalDateUtils.toDate(LocalDate.now().minusDays(2)), dtos.get(2).getOccupancyDt());
        assertEquals(new BigDecimal(110).setScale(2), dtos.get(3).getRate());
        assertEquals(JavaLocalDateUtils.toDate(LocalDate.now().minusDays(3)), dtos.get(3).getOccupancyDt());
    }

    @Test
    public void testFetchAvarageofCompititorsRate_Demand_Enable_False_Differential() {
        Mockito.when(configParamsService.getBooleanParameterValue(IPConfigParamName.USE_COMPACT_WEBRATE_PACE)).thenReturn(true);
        tenantCrudService().executeUpdateByNativeQuery("update Webrate_Competitors_Class set Demand_Enabled=0 where Accom_Class_ID = 3");
        preparedDifferentialDataForFetchAverageOfCompetitorsRate(1);
        List<InvestigatorCompetitorChartDto> dtos = investigatorService.fetchAverageOfCompetitorsRateByOccupancy(JavaLocalDateUtils.toDate(LocalDate.now()), JavaLocalDateUtils.toDate(LocalDate.now()), 3, 1);
        assertEquals(0, dtos.size());
    }

    @Test
    public void testFetchAvarageofCompititorsRate_Competitor_Not_Checked_Differential() {
        Mockito.when(configParamsService.getBooleanParameterValue(IPConfigParamName.USE_COMPACT_WEBRATE_PACE)).thenReturn(true);
        tenantCrudService().executeUpdateByNativeQuery("update Webrate_Competitors set Status_ID =2 where Webrate_Competitors_ID = 1");
        preparedDifferentialDataForFetchAverageOfCompetitorsRate(1);
        List<InvestigatorCompetitorChartDto> dtos = investigatorService.fetchAverageOfCompetitorsRateByOccupancy(JavaLocalDateUtils.toDate(LocalDate.now()), JavaLocalDateUtils.toDate(LocalDate.now()), 3, 1);
        assertEquals(new BigDecimal(75).setScale(2), dtos.get(0).getRate());
        assertEquals(JavaLocalDateUtils.toDate(LocalDate.now()), dtos.get(0).getOccupancyDt());
        assertEquals(new BigDecimal(97).setScale(2), dtos.get(1).getRate());
        assertEquals(JavaLocalDateUtils.toDate(LocalDate.now().minusDays(1)), dtos.get(1).getOccupancyDt());
        assertEquals(new BigDecimal(100).setScale(2), dtos.get(2).getRate());
        assertEquals(JavaLocalDateUtils.toDate(LocalDate.now().minusDays(2)), dtos.get(2).getOccupancyDt());
        assertEquals(new BigDecimal(100).setScale(2), dtos.get(3).getRate());
        assertEquals(JavaLocalDateUtils.toDate(LocalDate.now().minusDays(3)), dtos.get(3).getOccupancyDt());
    }


    @Test
    public void testFetchAverageOfCompetitorsRateForSeason_Differential() {
        Mockito.when(configParamsService.getBooleanParameterValue(IPConfigParamName.USE_COMPACT_WEBRATE_PACE)).thenReturn(true);
        addSeasonForRateShopping();
        preparedDifferentialDataForFetchAverageOfCompetitorsRate(1);
        List<InvestigatorCompetitorChartDto> dtos = investigatorService.fetchAverageOfCompetitorsRateByOccupancy(JavaLocalDateUtils.toDate(LocalDate.now()), JavaLocalDateUtils.toDate(LocalDate.now()), 3, 1);
        assertEquals(4, dtos.size());
        assertEquals(new BigDecimal(95).setScale(2), dtos.get(0).getRate());
        assertEquals(JavaLocalDateUtils.toDate(LocalDate.now()), dtos.get(0).getOccupancyDt());
        assertEquals(new BigDecimal(92).setScale(2), dtos.get(1).getRate());
        assertEquals(JavaLocalDateUtils.toDate(LocalDate.now().minusDays(1)), dtos.get(1).getOccupancyDt());
        assertEquals(new BigDecimal(110).setScale(2), dtos.get(2).getRate());
        assertEquals(JavaLocalDateUtils.toDate(LocalDate.now().minusDays(2)), dtos.get(2).getOccupancyDt());
        assertEquals(new BigDecimal(133).setScale(2), dtos.get(3).getRate());
        assertEquals(JavaLocalDateUtils.toDate(LocalDate.now().minusDays(3)), dtos.get(3).getOccupancyDt());
    }

    @Test
    public void testFetchAverageOfCompetitorsRateForMultipleSeason_Differential() {
        Mockito.when(configParamsService.getBooleanParameterValue(IPConfigParamName.USE_COMPACT_WEBRATE_PACE)).thenReturn(true);
        addSeasonForRateShopping();
        tenantCrudService().executeUpdateByNativeQuery("insert into  Webrate_Override_Channel values(5,'test2',DATEADD(DAY, -1, SYSDATETIME()),DATEADD(DAY, -1, SYSDATETIME()),1,1,1,1,1,1,1,1,SYSDATETIME(),1,SYSDATETIME(),1)");
        preparedDifferentialDataForFetchAverageOfCompetitorsRate(1);
        List<InvestigatorCompetitorChartDto> dtos = investigatorService.fetchAverageOfCompetitorsRateByOccupancy(JavaLocalDateUtils.toDate(LocalDate.now()), JavaLocalDateUtils.toDate(LocalDate.now()), 3, 1);
        assertEquals(4, dtos.size());
        assertEquals(new BigDecimal(95).setScale(2), dtos.get(0).getRate());
        assertEquals(JavaLocalDateUtils.toDate(LocalDate.now()), dtos.get(0).getOccupancyDt());
        assertEquals(new BigDecimal(155).setScale(2), dtos.get(1).getRate());
        assertEquals(JavaLocalDateUtils.toDate(LocalDate.now().minusDays(1)), dtos.get(1).getOccupancyDt());
        assertEquals(new BigDecimal(110).setScale(2), dtos.get(2).getRate());
        assertEquals(JavaLocalDateUtils.toDate(LocalDate.now().minusDays(2)), dtos.get(2).getOccupancyDt());
        assertEquals(new BigDecimal(133).setScale(2), dtos.get(3).getRate());
        assertEquals(JavaLocalDateUtils.toDate(LocalDate.now().minusDays(3)), dtos.get(3).getOccupancyDt());
    }

    @Test
    public void testFetchCpBarRateFromDifferential() {
        Integer accomTypeId = 4;
        LocalDate now = LocalDate.now();
        preparedDataForFetchCp_BAR_RateDifferential(accomTypeId, now);
        List<InvestigatorCompetitorChartDto> dtos = investigatorService.fetchCpBarRatesByOccupancy(JavaLocalDateUtils.toDate(now), JavaLocalDateUtils.toDate(now), accomTypeId);
        assertEquals(6, dtos.size());
        assertEquals(new BigDecimal(130).setScale(2, HALF_UP), dtos.get(0).getRate());
        assertEquals(new BigDecimal(170).setScale(2, HALF_UP), dtos.get(1).getRate());
        assertEquals(new BigDecimal(115).setScale(2, HALF_UP), dtos.get(2).getRate());
        assertEquals(new BigDecimal(145).setScale(2, HALF_UP), dtos.get(3).getRate());
        assertEquals(new BigDecimal(140).setScale(2, HALF_UP), dtos.get(4).getRate());
        assertEquals(new BigDecimal(142).setScale(2, HALF_UP), dtos.get(5).getRate());
    }

    @Test
    public void testGetRemainingDemandByForeCastTypes() {
        LocalDate today = LocalDate.now();
        cookDataForRemainingDemand(today);
        List<RemainingDemandByForeCastTypeDto> remainingDemandByForeCastTypeDtos = investigatorService.getRemainingDemandByForeCastTypes(today, today.plusDays(1), 2);
        assertEquals("QUALIFIED_SEMIYLD", remainingDemandByForeCastTypeDtos.get(0).getForeCastType());
        assertEquals(new BigDecimal(10), remainingDemandByForeCastTypeDtos.get(0).getRemainingDemand());
        assertEquals(today, remainingDemandByForeCastTypeDtos.get(0).getOccupancyDate());

        assertEquals("QUALIFIED_YLD", remainingDemandByForeCastTypeDtos.get(1).getForeCastType());
        assertEquals(new BigDecimal(4), remainingDemandByForeCastTypeDtos.get(1).getRemainingDemand());
        assertEquals(today, remainingDemandByForeCastTypeDtos.get(1).getOccupancyDate());

        assertEquals("UNQUALIFIED", remainingDemandByForeCastTypeDtos.get(2).getForeCastType());
        assertEquals(new BigDecimal(7), remainingDemandByForeCastTypeDtos.get(2).getRemainingDemand());
        assertEquals(today, remainingDemandByForeCastTypeDtos.get(2).getOccupancyDate());

        assertEquals("QUALIFIED_SEMIYLD", remainingDemandByForeCastTypeDtos.get(3).getForeCastType());
        assertEquals(new BigDecimal(6), remainingDemandByForeCastTypeDtos.get(3).getRemainingDemand());
        assertEquals(today.plusDays(1), remainingDemandByForeCastTypeDtos.get(3).getOccupancyDate());

        assertEquals("QUALIFIED_YLD", remainingDemandByForeCastTypeDtos.get(4).getForeCastType());
        assertEquals(new BigDecimal(12), remainingDemandByForeCastTypeDtos.get(4).getRemainingDemand());
        assertEquals(today.plusDays(1), remainingDemandByForeCastTypeDtos.get(4).getOccupancyDate());

        assertEquals("UNQUALIFIED", remainingDemandByForeCastTypeDtos.get(5).getForeCastType());
        assertEquals(new BigDecimal(9), remainingDemandByForeCastTypeDtos.get(5).getRemainingDemand());
        assertEquals(today.plusDays(1), remainingDemandByForeCastTypeDtos.get(5).getOccupancyDate());
    }

    private void cookDataForRemainingDemand(LocalDate today) {
        tenantCrudService().executeUpdateByNativeQuery("delete from Occupancy_Demand_FCST where Occupancy_DT = :occupancyDate", QueryParameter.with("occupancyDate", JavaLocalDateUtils.toDate(today)).parameters());
        tenantCrudService().executeUpdateByNativeQuery("delete from Occupancy_Demand_FCST where Occupancy_DT = :occupancyDate", QueryParameter.with("occupancyDate", JavaLocalDateUtils.toDate(today.plusDays(1))).parameters());

        Decision decision1 = UniqueDecisionCreator.createDecisionFor(5, JavaLocalDateUtils.toDate(today), 1);
        UniqueOccupancyDemandForecastCreator.createOccupancyDemandForecast(5, 3, 2, decision1.getId(), JavaLocalDateUtils.toDate(today), BigDecimal.valueOf(7));
        UniqueOccupancyDemandForecastCreator.createOccupancyDemandForecast(5, 4, 2, decision1.getId(), JavaLocalDateUtils.toDate(today), BigDecimal.valueOf(4));
        UniqueOccupancyDemandForecastCreator.createOccupancyDemandForecast(5, 9, 2, decision1.getId(), JavaLocalDateUtils.toDate(today), BigDecimal.valueOf(10));

        Decision decision2 = UniqueDecisionCreator.createDecisionFor(5, JavaLocalDateUtils.toDate(today.plusDays(1)), 1);
        UniqueOccupancyDemandForecastCreator.createOccupancyDemandForecast(5, 3, 2, decision2.getId(), JavaLocalDateUtils.toDate(today.plusDays(1)), BigDecimal.valueOf(9));
        UniqueOccupancyDemandForecastCreator.createOccupancyDemandForecast(5, 4, 2, decision2.getId(), JavaLocalDateUtils.toDate(today.plusDays(1)), BigDecimal.valueOf(12));
        UniqueOccupancyDemandForecastCreator.createOccupancyDemandForecast(5, 9, 2, decision2.getId(), JavaLocalDateUtils.toDate(today.plusDays(1)), BigDecimal.valueOf(6));
    }

    @Test
    public void testGetRoomSoldsAndAvailableCapacityByOccupancyDate() {
        LocalDate today = LocalDate.now();
        cookDataForRoomSOldsAndCapacity(today);

        List<InvestigatorRoomsInfoDto> investigatorRoomsInfoDtos = investigatorService.getRoomSoldsAndAvailableCapacityByOccupancyDate(today, today.plusDays(1), 2);
        InvestigatorRoomsInfoDto investigatorRoomsInfoDto = investigatorRoomsInfoDtos.get(0);
        assertEquals(today, investigatorRoomsInfoDto.getOccupancyDate());
        assertEquals(new BigDecimal(7), investigatorRoomsInfoDto.getRoomsSolds());
        assertEquals(new BigDecimal(19), investigatorRoomsInfoDto.getAvailableCapacity());
        investigatorRoomsInfoDto = investigatorRoomsInfoDtos.get(1);
        assertEquals(today.plusDays(1), investigatorRoomsInfoDto.getOccupancyDate());
        assertEquals(new BigDecimal(6), investigatorRoomsInfoDto.getRoomsSolds());
        assertEquals(new BigDecimal(23), investigatorRoomsInfoDto.getAvailableCapacity());
    }

    @Test
    public void testGetPriceRankingData() {
        LocalDate today = LocalDate.now();
        cookDataForRoomSOldsAndCapacity(today);
        cookDataForRemainingDemand(today);
        List<PriceRankingChartDto> investigatorRoomsInfoDtos = investigatorService.getPriceRankingData(today, today.plusDays(1), 2);

        PriceRankingChartDto priceRankingChartDto = investigatorRoomsInfoDtos.get(0);
        assertEquals(today, priceRankingChartDto.getDate());
        assertEquals(new BigDecimal(19), priceRankingChartDto.getAvailableCapacity());
        assertEquals(new BigDecimal(7), priceRankingChartDto.getTotalRoomSolds());
        assertEquals(new BigDecimal(0), priceRankingChartDto.getLinnkedDemand());
        assertEquals(new BigDecimal(10), priceRankingChartDto.getLraDemand());
        assertEquals(new BigDecimal(7), priceRankingChartDto.getUnqualifiedDemand());

        priceRankingChartDto = investigatorRoomsInfoDtos.get(1);
        assertEquals(today.plusDays(1), priceRankingChartDto.getDate());
        assertEquals(new BigDecimal(23), priceRankingChartDto.getAvailableCapacity());
        assertEquals(new BigDecimal(6), priceRankingChartDto.getTotalRoomSolds());
        assertEquals(new BigDecimal(0), priceRankingChartDto.getLinnkedDemand());
        assertEquals(new BigDecimal(6), priceRankingChartDto.getLraDemand());
        assertEquals(new BigDecimal(9), priceRankingChartDto.getUnqualifiedDemand());
    }

    private void cookDataForRoomSOldsAndCapacity(LocalDate today) {
        tenantCrudService().executeUpdateByNativeQuery("delete from Accom_Activity where Occupancy_DT = :occupancyDate", QueryParameter.with("occupancyDate", JavaLocalDateUtils.toDate(today)).parameters());
        tenantCrudService().executeUpdateByNativeQuery("delete from Accom_Activity where Occupancy_DT = :occupancyDate", QueryParameter.with("occupancyDate", JavaLocalDateUtils.toDate(today.plusDays(1))).parameters());
        createDataForDate(JavaLocalDateUtils.toDate(today), 5, 6, 20, new BigDecimal(3), 5, 3);
        createDataForDate(JavaLocalDateUtils.toDate(today), 5, 7, 15, new BigDecimal(4), 5, 3);

        createDataForDate(JavaLocalDateUtils.toDate(today.plusDays(1)), 5, 6, 22, new BigDecimal(3), 5, 3);
        createDataForDate(JavaLocalDateUtils.toDate(today.plusDays(1)), 5, 8, 17, new BigDecimal(3), 5, 3);

    }

    private void createDataForDate(Date occupancyDate, int propertyId, int accomTypeId, int capacity, BigDecimal roomsSolds, int roomNotAvailableMaintenance, int roomsNotAvailableOther) {
        AccomActivity accomActivity = new AccomActivity();
        accomActivity.setOccupancyDate(occupancyDate);
        accomActivity.setAccomTypeId(accomTypeId);
        accomActivity.setAccomCapacity(BigDecimal.valueOf(capacity));
        accomActivity.setRoomsNotAvailableMaintenance(BigDecimal.valueOf(roomNotAvailableMaintenance));
        accomActivity.setRoomsNotAvailableOther(BigDecimal.valueOf(roomsNotAvailableOther));
        accomActivity.setPropertyId(propertyId);
        accomActivity.setFileMetadataId(1);
        accomActivity.setSnapShotDate(occupancyDate);
        accomActivity.setArrivals(BigDecimal.ONE);
        accomActivity.setCancellations(BigDecimal.ONE);
        accomActivity.setDepartures(BigDecimal.ONE);
        accomActivity.setFoodRevenue(BigDecimal.ZERO);
        accomActivity.setNoShows(BigDecimal.ZERO);
        accomActivity.setRoomRevenue(BigDecimal.ZERO);
        accomActivity.setRoomsSold(roomsSolds);
        accomActivity.setTotalRevenue(BigDecimal.ZERO);
        tenantCrudService().save(accomActivity);
    }

    private void addSeasonForRateShopping() {
        tenantCrudService().executeUpdateByNativeQuery("delete from Webrate_Override_Channel");
        tenantCrudService().executeUpdateByNativeQuery("insert into  Webrate_Override_Channel values(5,'test1',DATEADD(DAY, -3, SYSDATETIME()), DATEADD(DAY, -3, SYSDATETIME()),1,1,1,1,1,1,1,1,SYSDATETIME(),1,SYSDATETIME(),1);");
    }

    private void preparedDataForFetchAverageOfCompititorsRate(int los) {
        tenantCrudService().executeUpdateByNativeQuery("delete from PACE_Webrate");
        tenantCrudService().executeUpdateByNativeQuery("update  Webrate_Accom_Class_Mapping set Accom_Class_ID =3 where Webrate_Accom_Type_ID=2");
        tenantCrudService().executeUpdateByNativeQuery("insert into PACE_Webrate values(1,SYSDATETIME(), 1, 3, 3, 1, SYSDATETIME(), " + los + ",  'A', 'USD', 120.00000,'','lowest',SYSDATETIME())");
        tenantCrudService().executeUpdateByNativeQuery("insert into PACE_Webrate values(1,SYSDATETIME(), 2, 3, 3, 1, SYSDATETIME(), " + los + ",  'A', 'USD', 120.00000,'','lowest',SYSDATETIME())");
        tenantCrudService().executeUpdateByNativeQuery("insert into PACE_Webrate values(1,SYSDATETIME(), 1, 3, 2, 1, SYSDATETIME(), " + los + ",  'A', 'USD', 115.00000,'','lowest',SYSDATETIME())");
        tenantCrudService().executeUpdateByNativeQuery("insert into PACE_Webrate values(1,SYSDATETIME(), 2, 3, 2, 1, SYSDATETIME(), " + los + ",  'A', 'USD', 75.00000,'','lowest',SYSDATETIME())");
        tenantCrudService().executeUpdateByNativeQuery("insert into PACE_Webrate values(1,DATEADD(DAY, -1, SYSDATETIME()), 1, 3, 3, 1, SYSDATETIME(), " + los + ",  'A', 'USD', 87.00000,'','lowest',DATEADD(DAY, -1, SYSDATETIME()))");
        tenantCrudService().executeUpdateByNativeQuery("insert into PACE_Webrate values(1,DATEADD(DAY, -1, SYSDATETIME()), 2, 3, 3, 1, SYSDATETIME(), " + los + ",  'A', 'USD', 97.00000,'','lowest',DATEADD(DAY, -1, SYSDATETIME()))");
        tenantCrudService().executeUpdateByNativeQuery("insert into PACE_Webrate values(1,DATEADD(DAY, -2, SYSDATETIME()), 1, 3, 3, 1, SYSDATETIME(), " + los + ",  'A', 'USD', 120.00000,'','lowest',DATEADD(DAY, -2, SYSDATETIME()))");
        tenantCrudService().executeUpdateByNativeQuery("insert into PACE_Webrate values(1,DATEADD(DAY, -2, SYSDATETIME()), 2, 3, 3, 1, SYSDATETIME(), " + los + ",  'A', 'USD', 100.00000,'','lowest',DATEADD(DAY, -2, SYSDATETIME()))");
        tenantCrudService().executeUpdateByNativeQuery("insert into PACE_Webrate values(1,DATEADD(DAY, -3, SYSDATETIME()), 1, 3, 3, 1, SYSDATETIME(), " + los + ",  'A', 'USD', 120.00000,'','lowest',DATEADD(DAY, -3, SYSDATETIME()))");
        tenantCrudService().executeUpdateByNativeQuery("insert into PACE_Webrate values(1,DATEADD(DAY, -3, SYSDATETIME()), 2, 3, 3, 1, SYSDATETIME(), " + los + ",  'A', 'USD', 100.00000,'','lowest',DATEADD(DAY, -3, SYSDATETIME()))");


        tenantCrudService().executeUpdateByNativeQuery("insert into PACE_Webrate values(1,DATEADD(DAY, -1, SYSDATETIME()), 1, 1, 3, 1, SYSDATETIME(), " + los + ",  'A', 'USD', 160.00000,'','lowest',DATEADD(DAY, -1, SYSDATETIME()))");
        tenantCrudService().executeUpdateByNativeQuery("insert into PACE_Webrate values(1,DATEADD(DAY, -1, SYSDATETIME()), 2, 1, 3, 1, SYSDATETIME(), " + los + ",  'A', 'USD', 150.00000,'','lowest',DATEADD(DAY, -1, SYSDATETIME()))");
        tenantCrudService().executeUpdateByNativeQuery("insert into PACE_Webrate values(1,DATEADD(DAY, -2, SYSDATETIME()), 1, 1, 3, 1, SYSDATETIME(), " + los + ",  'A', 'USD', 120.00000,'','lowest',DATEADD(DAY, -2, SYSDATETIME()))");
        tenantCrudService().executeUpdateByNativeQuery("insert into PACE_Webrate values(1,DATEADD(DAY, -2, SYSDATETIME()), 2, 1, 3, 1, SYSDATETIME(), " + los + ",  'A', 'USD', 100.00000,'','lowest',DATEADD(DAY, -2, SYSDATETIME()))");
        tenantCrudService().executeUpdateByNativeQuery("insert into PACE_Webrate values(1,DATEADD(DAY, -3, SYSDATETIME()), 1, 1, 3, 1, SYSDATETIME(), " + los + ",  'A', 'USD', 126.00000,'','lowest',DATEADD(DAY, -3, SYSDATETIME()))");
        tenantCrudService().executeUpdateByNativeQuery("insert into PACE_Webrate values(1,DATEADD(DAY, -3, SYSDATETIME()), 2, 1, 3, 1, SYSDATETIME(), " + los + ",  'A', 'USD', 140.00000,'','lowest',DATEADD(DAY, -3, SYSDATETIME()))");
    }

    private void preparedDifferentialDataForFetchAverageOfCompetitorsRate(int los) {
        tenantCrudService().executeUpdateByNativeQuery("delete from PACE_Webrate");
        tenantCrudService().executeUpdateByNativeQuery("update  Webrate_Accom_Class_Mapping set Accom_Class_ID =3 where Webrate_Accom_Type_ID=2");
        tenantCrudService().executeUpdateByNativeQuery("insert into PACE_Webrate_Differential values(1,SYSDATETIME(),SYSDATETIME(),1, 1, 3, 3, 1, SYSDATETIME(), " + los + ",  'A', 'USD', 120.00000,'','lowest',SYSDATETIME(),120.00000)");
        tenantCrudService().executeUpdateByNativeQuery("insert into PACE_Webrate_Differential values(1,SYSDATETIME(),SYSDATETIME(),1, 2, 3, 3, 1, SYSDATETIME(), " + los + ",  'A', 'USD', 120.00000,'','lowest',SYSDATETIME(),120.00000)");
        tenantCrudService().executeUpdateByNativeQuery("insert into PACE_Webrate_Differential values(1,SYSDATETIME(),SYSDATETIME(),1, 1, 3, 2, 1, SYSDATETIME(), " + los + ",  'A', 'USD', 115.00000,'','lowest',SYSDATETIME(),115.00000)");
        tenantCrudService().executeUpdateByNativeQuery("insert into PACE_Webrate_Differential values(1,SYSDATETIME(),SYSDATETIME(),1, 2, 3, 2, 1, SYSDATETIME(), " + los + ",  'A', 'USD', 75.00000,'','lowest',SYSDATETIME(),75.00000)");
        tenantCrudService().executeUpdateByNativeQuery("insert into PACE_Webrate_Differential values(1,DATEADD(DAY, -1, SYSDATETIME()),DATEADD(DAY, -1, SYSDATETIME()),1, 1, 3, 3, 1, SYSDATETIME(), " + los + ",  'A', 'USD', 87.00000,'','lowest',DATEADD(DAY, -1, SYSDATETIME()),87.00000)");
        tenantCrudService().executeUpdateByNativeQuery("insert into PACE_Webrate_Differential values(1,DATEADD(DAY, -1, SYSDATETIME()),DATEADD(DAY, -1, SYSDATETIME()),1, 2, 3, 3, 1, SYSDATETIME(), " + los + ",  'A', 'USD', 97.00000,'','lowest',DATEADD(DAY, -1, SYSDATETIME()),97.00000)");
        tenantCrudService().executeUpdateByNativeQuery("insert into PACE_Webrate_Differential values(1,DATEADD(DAY, -2, SYSDATETIME()),DATEADD(DAY, -2, SYSDATETIME()),1, 1, 3, 3, 1, SYSDATETIME(), " + los + ",  'A', 'USD', 120.00000,'','lowest',DATEADD(DAY, -2, SYSDATETIME()),120.00000)");
        tenantCrudService().executeUpdateByNativeQuery("insert into PACE_Webrate_Differential values(1,DATEADD(DAY, -2, SYSDATETIME()),DATEADD(DAY, -2, SYSDATETIME()),1, 2, 3, 3, 1, SYSDATETIME(), " + los + ",  'A', 'USD', 100.00000,'','lowest',DATEADD(DAY, -2, SYSDATETIME()),100.00000)");
        tenantCrudService().executeUpdateByNativeQuery("insert into PACE_Webrate_Differential values(1,DATEADD(DAY, -3, SYSDATETIME()),DATEADD(DAY, -3, SYSDATETIME()),1, 1, 3, 3, 1, SYSDATETIME(), " + los + ",  'A', 'USD', 120.00000,'','lowest',DATEADD(DAY, -3, SYSDATETIME()),120.00000)");
        tenantCrudService().executeUpdateByNativeQuery("insert into PACE_Webrate_Differential values(1,DATEADD(DAY, -3, SYSDATETIME()),DATEADD(DAY, -3, SYSDATETIME()),1, 2, 3, 3, 1, SYSDATETIME(), " + los + ",  'A', 'USD', 100.00000,'','lowest',DATEADD(DAY, -3, SYSDATETIME()),100.00000)");


        tenantCrudService().executeUpdateByNativeQuery("insert into PACE_Webrate_Differential values(1,DATEADD(DAY, -1, SYSDATETIME()),DATEADD(DAY, -1, SYSDATETIME()),1, 1, 1, 3, 1, SYSDATETIME(), " + los + ",  'A', 'USD', 160.00000,'','lowest',DATEADD(DAY, -1, SYSDATETIME()),160.00000)");
        tenantCrudService().executeUpdateByNativeQuery("insert into PACE_Webrate_Differential values(1,DATEADD(DAY, -1, SYSDATETIME()),DATEADD(DAY, -1, SYSDATETIME()),1, 2, 1, 3, 1, SYSDATETIME(), " + los + ",  'A', 'USD', 150.00000,'','lowest',DATEADD(DAY, -1, SYSDATETIME()),150.00000)");
        tenantCrudService().executeUpdateByNativeQuery("insert into PACE_Webrate_Differential values(1,DATEADD(DAY, -2, SYSDATETIME()),DATEADD(DAY, -2, SYSDATETIME()),1, 1, 1, 3, 1, SYSDATETIME(), " + los + ",  'A', 'USD', 120.00000,'','lowest',DATEADD(DAY, -2, SYSDATETIME()),120.00000)");
        tenantCrudService().executeUpdateByNativeQuery("insert into PACE_Webrate_Differential values(1,DATEADD(DAY, -2, SYSDATETIME()),DATEADD(DAY, -2, SYSDATETIME()),1, 2, 1, 3, 1, SYSDATETIME(), " + los + ",  'A', 'USD', 100.00000,'','lowest',DATEADD(DAY, -2, SYSDATETIME()),100.00000)");
        tenantCrudService().executeUpdateByNativeQuery("insert into PACE_Webrate_Differential values(1,DATEADD(DAY, -3, SYSDATETIME()),DATEADD(DAY, -3, SYSDATETIME()),1, 1, 1, 3, 1, SYSDATETIME(), " + los + ",  'A', 'USD', 126.00000,'','lowest',DATEADD(DAY, -3, SYSDATETIME()),126.00000)");
        tenantCrudService().executeUpdateByNativeQuery("insert into PACE_Webrate_Differential values(1,DATEADD(DAY, -3, SYSDATETIME()),DATEADD(DAY, -3, SYSDATETIME()),1, 2, 1, 3, 1, SYSDATETIME(), " + los + ",  'A', 'USD', 140.00000,'','lowest',DATEADD(DAY, -3, SYSDATETIME()),140.00000)");
    }

    private void preparedDataForFetchCp_BAR_RateDifferential(Integer accomTypeId, LocalDate now) {
        tenantCrudService().executeUpdateByNativeQuery("delete  from CP_Pace_Decision_Bar_Output_Differential");
        List<Object> decisionIds = tenantCrudService().findByNativeQuery("select top 6 Decision_ID from Decision where Decision_Type_ID=1 and Business_DT  between DATEADD(DAY, -29, '"+now+"') and '"+now+"'  order by Business_DT desc");
        tenantCrudService().executeUpdateByNativeQuery("insert into CP_Pace_Decision_Bar_Output_Differential values(" + decisionIds.get(0).toString() + ", 1, 1, " + accomTypeId + ", SYSDATETIME(), 1, 120, 120, 'NONE', 10, 900, 120, SYSDATETIME(), 130, 120, 120, null, (select d.Business_Dt from Decision d where d.Decision_ID = " + decisionIds.get(0).toString() + "))");
        tenantCrudService().executeUpdateByNativeQuery("insert into CP_Pace_Decision_Bar_Output_Differential values(" + decisionIds.get(1).toString() + ", 1, 1, " + accomTypeId + ", SYSDATETIME(), 1, 120, 120, 'NONE', 10, 900, 120, SYSDATETIME(), 170, 120, 120, null, (select d.Business_Dt from Decision d where d.Decision_ID = " + decisionIds.get(1).toString() + "))");
        tenantCrudService().executeUpdateByNativeQuery("insert into CP_Pace_Decision_Bar_Output_Differential values(" + decisionIds.get(2).toString() + ", 1, 1, " + accomTypeId + ", SYSDATETIME(), 1, 120, 120, 'NONE', 10, 900, 120, SYSDATETIME(), 115, 120, 120, null, (select d.Business_Dt from Decision d where d.Decision_ID = " + decisionIds.get(2).toString() + "))");
        tenantCrudService().executeUpdateByNativeQuery("insert into CP_Pace_Decision_Bar_Output_Differential values(" + decisionIds.get(3).toString() + ", 1, 1, " + accomTypeId + ", SYSDATETIME(), 1, 120, 120, 'NONE', 10, 900, 120, SYSDATETIME(), 145, 120, 120, null, (select d.Business_Dt from Decision d where d.Decision_ID = " + decisionIds.get(3).toString() + "))");
        tenantCrudService().executeUpdateByNativeQuery("insert into CP_Pace_Decision_Bar_Output_Differential values(" + decisionIds.get(4).toString() + ", 1, 1, " + accomTypeId + ", SYSDATETIME(), 1, 120, 120, 'NONE', 10, 900, 120, SYSDATETIME(), 140, 120, 120, null, (select d.Business_Dt from Decision d where d.Decision_ID = " + decisionIds.get(4).toString() + "))");
        tenantCrudService().executeUpdateByNativeQuery("insert into CP_Pace_Decision_Bar_Output_Differential values(" + decisionIds.get(5).toString() + ", 1, 1, " + accomTypeId + ", SYSDATETIME(), 1, 120, 120, 'NONE', 10, 900, 120, SYSDATETIME(), 142, 120, 120, null, (select d.Business_Dt from Decision d where d.Decision_ID = " + decisionIds.get(5).toString() + "))");
    }

    @Test
    public void testCPDecisionBarOutput() {
        Date arrDt = new Date(117, 9, 10);
        AccomType accomType = tenantCrudService().find(AccomType.class, 8);
        tenantCrudService().executeUpdateByNativeQuery("delete  from CP_Decision_Bar_Output where Arrival_DT = '2017-10-10' and Accom_Type_ID = 8 ");
        tenantCrudService().save(createCpDecisionBarOutput());
        Product product = tenantCrudService().findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT);
        CPDecisionBAROutput cpDecisionBAROutput = tenantCrudService().findByNamedQuerySingleResult(CPDecisionBAROutput.GET_DECISION_BAR_OUTPUT, CPDecisionBAROutput.params(product, LocalDateUtils.fromDate(arrDt), accomType));
        BigDecimal finalBar = cpDecisionBAROutput.getFinalBAR().setScale(2, HALF_UP);
        BigDecimal optimalBar = cpDecisionBAROutput.getOptimalBAR().setScale(2, HALF_UP);
        BigDecimal previousBar = cpDecisionBAROutput.getPreviousBAR().setScale(2, BigDecimal.ROUND_HALF_UP);
        assertEquals(new BigDecimal(200).setScale(2, HALF_UP), finalBar);
        assertEquals(new BigDecimal(200).setScale(2, HALF_UP), optimalBar);
        assertEquals(new BigDecimal(175).setScale(2, HALF_UP), previousBar);
    }

    @Test
    public void shouldFetchAccomTypeFromDB() {
        AccomType accomType = UniqueAccomTypeCreator.createUniqueAccomType();
        AccomType atPersisted = investigatorService.getAccomTypeById(accomType.getId());
        assertEquals(accomType.getName(), atPersisted.getName());
        assertEquals(accomType.getId(), atPersisted.getId());
    }

    @Test
    public void testOverbookingByClass() {
        Date today = JavaLocalDateUtils.toDate(LocalDate.now());
        createOverBookingAccom(today, 5, 4);
        createOverBookingAccom(today, 6, 3);
        createOverBookingAccom(today, 7, 7);
        createOverBookingAccom(today, 8, 1);
        final Long overbookingByClass = investigatorService.getOverbookingByClass(2, today, today);
        assertEquals(11, overbookingByClass.intValue());
        final Long overbookingByClass1 = investigatorService.getOverbookingByClass(4, today, today);
        assertEquals(4, overbookingByClass1.intValue());

    }

    private void createOverBookingAccom(Date date, Integer accomTypeId, Integer overbookingDecision) {
        DecisionOvrbkAccom ovrBkAccom = tenantCrudService().findByNamedQuerySingleResult(DecisionOvrbkAccom.BY_PROPERTYID_ACCOMTYPEID_AND_OCCUPANCY_DATE,
                QueryParameter.with("accomTypeId", accomTypeId).and("occupancyDate", date).and("propertyId", 5).parameters());
        if (null == ovrBkAccom) {
            ovrBkAccom = new DecisionOvrbkAccom();
            ovrBkAccom.setAccomTypeId(4);
            ovrBkAccom.setCreateDate(new Date());
            ovrBkAccom.setDecisionId(1);
            ovrBkAccom.setOccupancyDate(date);
            ovrBkAccom.setPropertyId(5);
        }
        ovrBkAccom.setOverbookingDecision(overbookingDecision);
        tenantCrudService().save(ovrBkAccom);
    }

    private CPDecisionBAROutput createCpDecisionBarOutput() {
        CPDecisionBAROutput output = new CPDecisionBAROutput();
        output.setPropertyId(5);
        output.setDecisionId(249);
        output.setProduct(tenantCrudService().find(Product.class, 1));
        output.setDecisionReasonTypeId(1);
        output.setAccomType(tenantCrudService().find(AccomType.class, 8));
        output.setArrivalDate(JavaLocalDateUtils.toJodaLocalDate(LocalDate.parse("2017-10-10")));
        output.setLengthOfStay(-1);
        output.setOptimalBAR(BigDecimal.valueOf(200.00).setScale(2, HALF_UP));
        output.setPrettyBAR(BigDecimal.valueOf(200.00).setScale(2, HALF_UP));
        output.setOverrideType(DecisionOverrideType.NONE);
        output.setFloorOverride(BigDecimal.ZERO);
        output.setCeilingOverride(BigDecimal.ZERO);
        output.setSpecificOverride(BigDecimal.ZERO);
        output.setFinalBAR(BigDecimal.valueOf(200));
        output.setRoomsOnlyBAR(BigDecimal.valueOf(200));
        output.setPreviousBAR(BigDecimal.valueOf(175));
        output.setOptimalBarType(OptimalBarType.PRICE);
        return output;
    }
}
