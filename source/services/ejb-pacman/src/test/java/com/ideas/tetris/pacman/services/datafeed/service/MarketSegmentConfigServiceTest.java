package com.ideas.tetris.pacman.services.datafeed.service;

import com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName;
import com.ideas.tetris.pacman.services.businessgroup.service.BusinessGroupService;
import com.ideas.tetris.pacman.services.datafeed.dto.MarketSegmentConfig;
import com.ideas.tetris.pacman.services.independentproducts.service.IndependentProductsService;
import com.ideas.tetris.pacman.services.marketsegment.entity.BusinessGroup;
import com.ideas.tetris.pacman.services.marketsegment.entity.BusinessType;
import com.ideas.tetris.pacman.services.marketsegment.entity.ForecastActivityType;
import com.ideas.tetris.pacman.services.marketsegment.entity.ForecastGroup;
import com.ideas.tetris.pacman.services.marketsegment.entity.MktSeg;
import com.ideas.tetris.pacman.services.marketsegment.entity.MktSegBusinessGroup;
import com.ideas.tetris.pacman.services.marketsegment.entity.MktSegDetails;
import com.ideas.tetris.pacman.services.marketsegment.entity.MktSegDetailsProposed;
import com.ideas.tetris.pacman.services.marketsegment.entity.MktSegForecastGroup;
import com.ideas.tetris.pacman.services.marketsegment.entity.YieldType;
import com.ideas.tetris.pacman.services.marketsegment.service.MarketSegmentService;
import com.ideas.tetris.pacman.services.marketsegment.service.forecastgroup.service.ForecastGroupFinalService;
import com.ideas.tetris.pacman.services.product.Product;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;

import static com.ideas.tetris.pacman.common.constants.Constants.QUALIFIED;
import static com.ideas.tetris.pacman.common.constants.Constants.UNQUALIFIED;
import static com.ideas.tetris.pacman.services.datafeed.service.MarketSegmentConfigService.GROUP;
import static com.ideas.tetris.pacman.services.datafeed.service.MarketSegmentConfigService.SYSTEM_DECISION;
import static com.ideas.tetris.pacman.services.datafeed.service.MarketSegmentConfigService.TRANSIENT;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.reset;
import static org.mockito.Mockito.when;

public class MarketSegmentConfigServiceTest {

    @InjectMocks
    MarketSegmentConfigService marketSegmentConfigService;

    @Mock
    MarketSegmentService marketSegmentService;

    @Mock
    ForecastGroupFinalService forecastGroupService;

    @Mock
    BusinessGroupService businessGroupService;

    @Mock
    PacmanConfigParamsService configParamsService;

    @Mock
    IndependentProductsService independentProductsService;

    private List<MarketSegmentConfig> mktSegConfigs;
    private List<MktSeg> marketSegments;

    @BeforeEach
    public void setup() {
        MockitoAnnotations.initMocks(this);
        marketSegments = new ArrayList<>();
        setupWhenThen();
    }

    @Test
    public void testMarketSegmentConfigGeneration() {
        MktSeg mktSeg = getMktSegWithMinimalDetails();
        MktSegDetails mktSegDetails = getMktSegDetailsWithMockData();
        mktSeg.setMktSegDetails(mktSegDetails);
        marketSegments.add(mktSeg);

        List<ForecastGroup> forecastGroups = createForecastGroupWithMockData(mktSeg);
        List<BusinessGroup> businessGroups = createBusinessGroupsWithMockData();

        addMktSegBusinessGroupDetails(mktSeg, businessGroups);

        reset(forecastGroupService, businessGroupService);
        when(forecastGroupService.getForecastGroupByPropertyId()).thenReturn(forecastGroups);
        when(businessGroupService.getAllBusinessGroupsAssociatedWithMktSeg()).thenReturn(businessGroups);

        mktSegConfigs = marketSegmentConfigService.getMktSegConfigDetails();
        MarketSegmentConfig marketSegmentConfig = getActualMarketSegmentConfig();

        deepAssertMktSegConfigDetails(marketSegmentConfig);
        assertEquals("businessGroup", marketSegmentConfig.getBusinessViewName());
        assertEquals("businessGroupDescription", marketSegmentConfig.getBusinessViewDescription());
        assertEquals("forecastGrpCode", marketSegmentConfig.getForecastGroupCode());
        assertEquals("forecastGrpDescription", marketSegmentConfig.getForecastGroupDescription());
        assertEquals("forecastGrpName", marketSegmentConfig.getForecastGroupName());
    }

    @Test
    public void testMarketSegmentConfigWithBaseProductName() {
        MktSeg mktSeg = getMktSegWithMinimalDetails();
        MktSegDetails mktSegDetails = getMktSegDetailsWithMockData();
        mktSeg.setMktSegDetails(mktSegDetails);
        marketSegments.add(mktSeg);

        List<ForecastGroup> forecastGroups = createForecastGroupWithMockData(mktSeg);
        List<BusinessGroup> businessGroups = createBusinessGroupsWithMockData();

        addMktSegBusinessGroupDetails(mktSeg, businessGroups);

        Product indProduct = new Product();
        indProduct.setId(1);
        indProduct.setName("BAR");
        Optional<Product> product = Optional.of(indProduct);

        reset(forecastGroupService, businessGroupService);
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.HIERARCHY_INDEPENDENT_PRODUCT_COLUMN_ENABLED)).thenReturn(true);
        when(forecastGroupService.getForecastGroupByPropertyId()).thenReturn(forecastGroups);
        when(businessGroupService.getAllBusinessGroupsAssociatedWithMktSeg()).thenReturn(businessGroups);
        when(independentProductsService.getProductMappedToMarketSegment(any())).thenReturn(product);

        mktSegConfigs = marketSegmentConfigService.getMktSegConfigDetails();
        MarketSegmentConfig marketSegmentConfig = getActualMarketSegmentConfig();

        deepAssertMktSegConfigDetails(marketSegmentConfig);
        assertEquals("businessGroup", marketSegmentConfig.getBusinessViewName());
        assertEquals("businessGroupDescription", marketSegmentConfig.getBusinessViewDescription());
        assertEquals("forecastGrpCode", marketSegmentConfig.getForecastGroupCode());
        assertEquals("forecastGrpDescription", marketSegmentConfig.getForecastGroupDescription());
        assertEquals("forecastGrpName", marketSegmentConfig.getForecastGroupName());
        assertEquals("BAR", marketSegmentConfig.getBaseProduct());

    }

    @Test
    public void shouldNotAddMktSegDetailsIfUnavailable() {
        marketSegments.add(getMktSegWithMinimalDetails());
        mktSegConfigs = marketSegmentConfigService.getMktSegConfigDetails();
        assertMktSegConfigWithoutMktSegDetails(getActualMarketSegmentConfig());
    }

    @Test
    public void shouldWorkWithoutMktSegDetailsProposed() {
        MktSeg mktSeg = getMktSegWithMinimalDetails();
        MktSegDetails mktSegDetails = getMktSegDetailsWithMockData();
        mktSeg.setMktSegDetails(mktSegDetails);
        marketSegments.add(mktSeg);
        mktSegConfigs = marketSegmentConfigService.getMktSegConfigDetails();
        deepAssertMktSegConfigDetails(getActualMarketSegmentConfig());
    }

    @Test
    public void shouldWorkWithoutMktSegDetails() {
        MarketSegmentConfig mktSegConfig = generateMarketSegmentConfig();
        deepAssertMktSegConfigDetails(mktSegConfig);
    }

    @Test
    public void mktSegConfigWithoutBusinessGroups() {
        MarketSegmentConfig mktSegConfig = generateMarketSegmentConfig();
        assertEquals("Unassigned", mktSegConfig.getBusinessViewDescription());
        assertEquals("Unassigned", mktSegConfig.getBusinessViewName());
    }

    @Test
    public void shouldSortMktSegConfigs() {
        MktSeg mktSeg = getMktSegWithMinimalDetails();
        mktSeg.setCode("XYZ");
        marketSegments.add(mktSeg);
        mktSeg = getMktSegWithMinimalDetails();
        mktSeg.setCode("ABC");
        marketSegments.add(mktSeg);
        mktSegConfigs = marketSegmentConfigService.getMktSegConfigDetails();
        assertEquals(2, mktSegConfigs.size());
        assertEquals("ABC", mktSegConfigs.get(0).getMktSegCode());
        assertEquals("XYZ", mktSegConfigs.get(1).getMktSegCode());
    }

    @Test
    public void testGroupBusinessType() {
        marketSegments.add(getMktSegWithDetailsProposed());
        mktSegConfigs = marketSegmentConfigService.getMktSegConfigDetails();
        MarketSegmentConfig marketSegmentConfig = getActualMarketSegmentConfig();
        assertEquals(GROUP, marketSegmentConfig.getBusinessType());
        assertEquals(SYSTEM_DECISION, marketSegmentConfig.getContract());
        assertEquals(SYSTEM_DECISION, marketSegmentConfig.getBooking());
        assertEquals(SYSTEM_DECISION, marketSegmentConfig.getSelling());
        assertEquals(SYSTEM_DECISION, marketSegmentConfig.getLinked());
    }

    @Test
    public void testTransientUnqualifiedBusinessType() {
        MktSeg mktSeg = getMktSegWithMinimalDetails();
        MktSegDetails mktSegDetails = getMktSegDetailsWithMockData();
        mktSegDetails.getBusinessType().setName(TRANSIENT);
        mktSegDetails.setQualified(0);
        mktSeg.setMktSegDetails(mktSegDetails);
        marketSegments.add(mktSeg);

        mktSegConfigs = marketSegmentConfigService.getMktSegConfigDetails();

        MarketSegmentConfig marketSegmentConfig = getActualMarketSegmentConfig();
        assertEquals(TRANSIENT, marketSegmentConfig.getBusinessType());
        assertEquals(UNQUALIFIED, marketSegmentConfig.getContract());
        assertEquals(SYSTEM_DECISION, marketSegmentConfig.getBooking());
        assertEquals(SYSTEM_DECISION, marketSegmentConfig.getLinked());

    }

    @Test
    public void testTransientQualifiedBusinessType() {
        MktSeg mktSeg = getMktSegWithMinimalDetails();
        MktSegDetails mktSegDetails = getMktSegDetailsWithMockData();
        mktSegDetails.getBusinessType().setName(TRANSIENT);
        mktSeg.setMktSegDetails(mktSegDetails);
        marketSegments.add(mktSeg);
        mktSegConfigs = marketSegmentConfigService.getMktSegConfigDetails();
        MarketSegmentConfig marketSegmentConfig = getActualMarketSegmentConfig();
        assertEquals(TRANSIENT, marketSegmentConfig.getBusinessType());
        assertEquals(QUALIFIED, marketSegmentConfig.getContract());
        assertEquals(SYSTEM_DECISION, marketSegmentConfig.getSelling());
    }

    @Test
    public void shouldIncludeBusinessGrpWithoutMktSegment() {
        marketSegments = new ArrayList<>();
        List<BusinessGroup> businessGroups = createBusinessGroupsWithMockData();
        reset(businessGroupService);
        when(businessGroupService.getAllBusinessGroupsAssociatedWithMktSeg()).thenReturn(businessGroups);
        mktSegConfigs = marketSegmentConfigService.getMktSegConfigDetails();
        MarketSegmentConfig marketSegmentConfig = getActualMarketSegmentConfig();
        assertEquals("businessGroup", marketSegmentConfig.getBusinessViewName());
        assertEquals("businessGroupDescription", marketSegmentConfig.getBusinessViewDescription());
    }

    private void setupWhenThen() {
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.HIERARCHY_INDEPENDENT_PRODUCT_COLUMN_ENABLED)).thenReturn(false);
        when(marketSegmentService.getMktSegByPropertyId()).thenReturn(marketSegments);
        when(forecastGroupService.getForecastGroupByPropertyId()).thenReturn(new ArrayList<>());
        when(businessGroupService.getAllBusinessGroupDetails()).thenReturn(new ArrayList<>());
    }

    private MarketSegmentConfig generateMarketSegmentConfig() {
        marketSegments.add(getMktSegWithDetailsProposed());
        mktSegConfigs = marketSegmentConfigService.getMktSegConfigDetails();
        return getActualMarketSegmentConfig();
    }

    private MktSeg getMktSegWithDetailsProposed() {
        MktSeg mktSeg = getMktSegWithMinimalDetails();
        MktSegDetailsProposed mktSegDetailsProposed = getMktSegDetailsProposedWithMockData();
        mktSeg.setMktSegDetailsProposed(mktSegDetailsProposed);
        return mktSeg;
    }

    private MarketSegmentConfig getActualMarketSegmentConfig() {
        assertEquals(1, mktSegConfigs.size());
        return mktSegConfigs.get(0);
    }

    private MktSeg getMktSegWithMinimalDetails() {
        MktSeg mktSeg = new MktSeg();
        mktSeg.setCode("mktSegCode");
        mktSeg.setName("mktSegName");
        mktSeg.setDescription("mktSegDescription");

        return mktSeg;
    }

    private void assertMktSegConfigWithoutMktSegDetails(MarketSegmentConfig mktSegConfig) {
        assertMktSegConfigMinimal(mktSegConfig);
        assertNull(mktSegConfig.getContract());
        assertNull(mktSegConfig.getSelling());
    }

    private void assertMktSegConfigMinimal(MarketSegmentConfig mktSegConfig) {
        assertEquals("mktSegName", mktSegConfig.getMktSegName());
        assertEquals("mktSegCode", mktSegConfig.getMktSegCode());
        assertEquals("mktSegDescription", mktSegConfig.getMktSegDescription());
    }

    private void addMktSegBusinessGroupDetails(MktSeg mktSeg, List<BusinessGroup> businessGroups) {
        MktSegBusinessGroup mktSegBusinessGroup = new MktSegBusinessGroup();
        mktSegBusinessGroup.setBusinessGroup(businessGroups.get(0));
        mktSegBusinessGroup.setMktSeg(mktSeg);
        businessGroups.get(0).getMktSegBusinessGroups().add(mktSegBusinessGroup);
    }

    private void deepAssertMktSegConfigDetails(MarketSegmentConfig mktSegConfig) {
        assertMktSegConfigMinimal(mktSegConfig);
        assertEquals("Group", mktSegConfig.getBusinessType());
        assertEquals("System Decision", mktSegConfig.getContract());
        assertEquals("System Decision", mktSegConfig.getBooking());
        assertEquals("System Decision", mktSegConfig.getSelling());
        assertEquals("forecastActivityType", mktSegConfig.getForecastType());
        assertEquals("yieldType", mktSegConfig.getControl());
        assertEquals("System Decision", mktSegConfig.getLinked());
        assertEquals("Yes", mktSegConfig.getPricedByBar());
    }

    private List<ForecastGroup> createForecastGroupWithMockData(MktSeg mktSeg) {
        List<ForecastGroup> forecastGroups = new ArrayList<>();
        ForecastGroup forecastGroup = new ForecastGroup();
        forecastGroup.setCode("forecastGrpCode");
        forecastGroup.setDescription("forecastGrpDescription");
        forecastGroup.setName("forecastGrpName");
        MktSegForecastGroup mktSegForecastGroup = new MktSegForecastGroup();
        mktSegForecastGroup.setStatusId(1);
        mktSegForecastGroup.setMktSeg(mktSeg);
        mktSegForecastGroup.setForecastGroup(forecastGroup);
        Set<MktSegForecastGroup> mktSegForecastGroups = new HashSet<>();
        mktSegForecastGroups.add(mktSegForecastGroup);
        forecastGroup.setMktSegForecastGroup(mktSegForecastGroups);
        forecastGroups.add(forecastGroup);

        return forecastGroups;
    }

    private List<BusinessGroup> createBusinessGroupsWithMockData() {
        BusinessGroup businessGroup = new BusinessGroup();
        businessGroup.setName("businessGroup");
        businessGroup.setDescription("businessGroupDescription");
        Set<MktSegBusinessGroup> mktSegBusinessGroups = new HashSet<>();
        businessGroup.setMktSegBusinessGroups(mktSegBusinessGroups);
        List<BusinessGroup> businessGroups = new ArrayList<>();
        businessGroups.add(businessGroup);

        return businessGroups;
    }

    private MktSegDetailsProposed getMktSegDetailsProposedWithMockData() {
        MktSegDetailsProposed mktSegDetailsProposed = new MktSegDetailsProposed();
        ForecastActivityType forecastActivityType = new ForecastActivityType();
        forecastActivityType.setName("forecastActivityType");
        YieldType yieldType = new YieldType();
        yieldType.setName("yieldType");
        BusinessType businessType = new BusinessType();
        businessType.setName("Group");
        mktSegDetailsProposed.setBusinessType(businessType);
        mktSegDetailsProposed.setQualified(1);
        mktSegDetailsProposed.setBookingBlockPc(100);
        mktSegDetailsProposed.setFenced(0);
        mktSegDetailsProposed.setPackageValue(1);
        mktSegDetailsProposed.setForecastActivityType(forecastActivityType);
        mktSegDetailsProposed.setYieldType(yieldType);
        mktSegDetailsProposed.setLink(0);
        mktSegDetailsProposed.setPriceByBar(1);
        return mktSegDetailsProposed;
    }

    private MktSegDetails getMktSegDetailsWithMockData() {
        MktSegDetails mktSegDetails = new MktSegDetails();
        ForecastActivityType forecastActivityType = new ForecastActivityType();
        forecastActivityType.setName("forecastActivityType");
        YieldType yieldType = new YieldType();
        yieldType.setName("yieldType");
        BusinessType businessType = new BusinessType();
        businessType.setName("Group");
        mktSegDetails.setBusinessType(businessType);
        mktSegDetails.setQualified(1);
        mktSegDetails.setBookingBlockPc(100);
        mktSegDetails.setFenced(0);
        mktSegDetails.setPackageValue(1);
        mktSegDetails.setForecastActivityType(forecastActivityType);
        mktSegDetails.setYieldType(yieldType);
        mktSegDetails.setLink(0);
        mktSegDetails.setPriceByBar(1);
        return mktSegDetails;
    }
}