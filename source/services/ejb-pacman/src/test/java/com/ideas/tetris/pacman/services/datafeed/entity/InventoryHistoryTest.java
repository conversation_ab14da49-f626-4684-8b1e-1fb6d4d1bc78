package com.ideas.tetris.pacman.services.datafeed.entity;

import com.ideas.g3.test.AbstractG3JupiterTest;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomClass;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomType;
import com.ideas.tetris.pacman.services.accommodation.entity.UniqueAccomClassCreator;
import com.ideas.tetris.pacman.services.accommodation.entity.UniqueAccomTypeCreator;
import com.ideas.tetris.pacman.services.commondaoandenities.global.dao.UniquePropertyCreator;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.TenantProperty;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.fplos.entity.PaceDecisionQualifiedFPLOS;
import com.ideas.tetris.pacman.services.propertygroup.service.PropertyGroupService;
import com.ideas.tetris.pacman.services.qualifiedrate.entity.RateQualified;
import com.ideas.tetris.pacman.services.unqualifiedrate.entity.RateUnqualified;
import com.ideas.tetris.pacman.services.unqualifiedrate.entity.UniqueRateQualified;
import com.ideas.tetris.pacman.services.unqualifiedrate.entity.UniqueRateUnqualified;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import org.joda.time.LocalDate;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import javax.persistence.Query;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

public class InventoryHistoryTest extends AbstractG3JupiterTest {

    private DateService dateService;
    private TenantProperty testProperty;
    private AccomClass accomClass;
    private AccomType accomType1;

    private LocalDate systemToday;
    private LocalDate startDate;
    private LocalDate endDate;
    private AccomType accomType2;
    Date dateToday;
    RateUnqualified rateUnqualified1;
    RateQualified rateQualified1;

    @BeforeEach
    public void setUp() {
        dateService = DateService.createTestInstance();
        PropertyGroupService propertyGroupService = new PropertyGroupService();
        dateService.setPropertyGroupService(propertyGroupService);
        dateService.setMultiPropertyCrudService(multiPropertyCrudService());
        systemToday = dateService.getCaughtUpLocalDate();
        testProperty = UniquePropertyCreator.createUniqueTenantProperty(tenantCrudService());
        createTestData();
    }

    private void createTestData() {
        accomClass = UniqueAccomClassCreator.createUniqueAccomClassForPropertyID(testProperty.getId(), 1);
        accomType1 = UniqueAccomTypeCreator.createUniqueAccomTypeForAccomClass(testProperty.getId(), accomClass);
        accomType2 = UniqueAccomTypeCreator.createUniqueAccomTypeForAccomClass(testProperty.getId(), accomClass);
        rateQualified1 = UniqueRateQualified.createRateQualifiedByDate(systemToday.toDate(), systemToday.toDate(), testProperty.getId());
        rateQualified1.setName("LV0");
        RateQualified rateQualified2 = UniqueRateQualified.createRateQualifiedByDate(systemToday.toDate(), systemToday.toDate(), testProperty.getId());
        rateQualified2.setName("LV1");

        rateUnqualified1 = UniqueRateUnqualified.createRateUnqualifiedByDate(systemToday.toDate(), systemToday.toDate(), testProperty.getId());
        rateUnqualified1.setName("J0");
        RateUnqualified rateUnqualified2 = UniqueRateUnqualified.createRateUnqualifiedByDate(systemToday.toDate(), systemToday.toDate(), testProperty.getId());
        rateUnqualified2.setName("J1");

        Calendar cal = Calendar.getInstance();
        dateToday = cal.getTime();

        createDecisionAcknowledgementRecord(1, testProperty.getId(), "Test File", "TID000000002", systemToday.plusDays(1).toDate(),
                null, rateQualified1.getId(), accomType1.getId(), "FPLOS", "YYNNNNN", systemToday.minusDays(1).toDate(), systemToday.toDate(), "27", "class ErrorDetail { context: RATEPLAN code: 997 message: Bad Request notifications: [{code=506, fields=[inventoryRestrictions], message=Each inventory restriction must contain at least one restriction.}] }");
        createDecisionAcknowledgementRecord(2, testProperty.getId(), "Test File", "TID000000003", systemToday.plusDays(1).toDate(),
                null, rateQualified2.getId(), accomType1.getId(), "FPLOS", "YNYYYYY", systemToday.minusDays(2).toDate(), systemToday.minusDays(1).toDate(), "0", "Success");
        createDecisionAcknowledgementRecord(3, testProperty.getId(), "Test File", "TID000000004", systemToday.plusDays(2).toDate(),
                rateUnqualified1.getId(), null, accomType2.getId(), "Overbooking", "YNNNNNN", systemToday.minusDays(1).toDate(), systemToday.minusDays(1).toDate(), "27", "class ErrorDetail { context: RATEPLAN code: 997 message: Bad Request notifications: [{code=506, fields=[inventoryRestrictions], message=Each inventory restriction must contain at least one restriction.}] }");
        createDecisionAcknowledgementRecord(4, testProperty.getId(), "Test File", "TID000000005", systemToday.plusDays(2).toDate(),
                null, rateQualified1.getId(), accomType2.getId(), "Overbooking", "11", systemToday.minusDays(2).toDate(), systemToday.toDate(), "-1", "Acknowledgement not received");
        createDecisionAcknowledgementRecord(5, testProperty.getId(), "Test File", "TID000000005", systemToday.plusDays(3).toDate(),
                null, rateQualified2.getId(), accomType2.getId(), "FPLOS", "11", systemToday.minusDays(1).toDate(), systemToday.toDate(), "-1", "Acknowledgement not received");
        createDecisionAcknowledgementRecord(6, testProperty.getId(), "Test File", "TID000000006", systemToday.plusDays(3).toDate(),
                null, rateQualified1.getId(), null, "Overbooking", "11", systemToday.minusDays(2).toDate(), systemToday.minusDays(1).toDate(), "0", "Success");
        createDecisionAcknowledgementRecord(7, testProperty.getId(), "Test File", "TID000000007", systemToday.minusDays(1).toDate(),
                rateUnqualified2.getId(), null, accomType1.getId(), "Overbooking", "YYNNNNN", systemToday.minusDays(1).toDate(), systemToday.toDate(), "0", "Success");

        createPaceDecisionQualifiedFplosRecord(testProperty.getId(), systemToday.plusDays(1).toDate(), rateQualified1.getId(), accomType1.getId(), "YYNNNNN", systemToday.minusDays(1).toDate(), systemToday.toDate());
        createPaceDecisionQualifiedFplosRecord(testProperty.getId(), systemToday.plusDays(1).toDate(), rateQualified2.getId(), accomType1.getId(), "YNYYYYY", systemToday.minusDays(2).toDate(), systemToday.minusDays(1).toDate());
        createPaceDecisionQualifiedFplosRecord(testProperty.getId(), systemToday.plusDays(3).toDate(), rateQualified2.getId(), accomType2.getId(), "11", systemToday.minusDays(1).toDate(), systemToday.toDate());


        startDate = systemToday;
        endDate = systemToday.plusDays(5);
    }

    @Test
    public void shouldFetchDataInPaginatedFashion() throws Exception {
        List<InventoryHistory> inventoryHistoryList = tenantCrudService().findByNamedQuery(InventoryHistory.FIND_BY_DATES_BETWEEN, QueryParameter.with("startDate", startDate).and("endDate", endDate).parameters(), 1, 2);
        assertEquals(2, inventoryHistoryList.size());
    }

    @Test
    public void shouldBeConsistentInOrderDuringPagination() throws Exception {
        RateQualified rateQualified3 = UniqueRateQualified.createRateQualifiedByDate(systemToday.plusDays(6).toDate(), systemToday.plusDays(6).toDate(), testProperty.getId());
        rateQualified3.setName("CWMIS0");
        RateQualified rateQualified4 = UniqueRateQualified.createRateQualifiedByDate(systemToday.plusDays(6).toDate(), systemToday.plusDays(6).toDate(), testProperty.getId());
        rateQualified4.setName("CKAIS0");
        RateQualified rateQualified5 = UniqueRateQualified.createRateQualifiedByDate(systemToday.plusDays(6).toDate(), systemToday.plusDays(6).toDate(), testProperty.getId());
        rateQualified5.setName("CMDTS0");
        RateQualified rateQualified6 = UniqueRateQualified.createRateQualifiedByDate(systemToday.plusDays(6).toDate(), systemToday.plusDays(6).toDate(), testProperty.getId());
        rateQualified6.setName("CUNHS0");
        RateQualified rateQualified7 = UniqueRateQualified.createRateQualifiedByDate(systemToday.plusDays(6).toDate(), systemToday.plusDays(6).toDate(), testProperty.getId());
        rateQualified7.setName("CATOS0");


        createDecisionAcknowledgementRecord(8, testProperty.getId(), "Test File", "TID000000008", systemToday.plusDays(6).toDate(),
                null, rateQualified3.getId(), null, "FPLOS", "YYYYYYY", systemToday.plusDays(6).toDate(), systemToday.plusDays(7).toDate(), "0", "success");


        createDecisionAcknowledgementRecord(9, testProperty.getId(), "Test File", "TID000000009", systemToday.plusDays(7).toDate(),
                null, rateQualified3.getId(), null, "FPLOS", "YYYYYYY", systemToday.plusDays(7).toDate(), systemToday.plusDays(8).toDate(), "0", "success");


        createDecisionAcknowledgementRecord(10, testProperty.getId(), "Test File", "TID000000010", systemToday.plusDays(6).toDate(),
                null, rateQualified4.getId(), null, "FPLOS", "YYYYYYY", systemToday.plusDays(6).toDate(), systemToday.plusDays(7).toDate(), "0", "success");

        createDecisionAcknowledgementRecord(11, testProperty.getId(), "Test File", "TID000000011", systemToday.plusDays(7).toDate(),
                null, rateQualified4.getId(), null, "FPLOS", "YYYYYYY", systemToday.plusDays(7).toDate(), systemToday.plusDays(8).toDate(), "0", "success");

        createDecisionAcknowledgementRecord(12, testProperty.getId(), "Test File", "TID000000012", systemToday.plusDays(6).toDate(),
                null, rateQualified5.getId(), null, "FPLOS", "YYYYYYY", systemToday.plusDays(6).toDate(), systemToday.plusDays(7).toDate(), "0", "success");

        createDecisionAcknowledgementRecord(13, testProperty.getId(), "Test File", "TID000000013", systemToday.plusDays(7).toDate(),
                null, rateQualified5.getId(), null, "FPLOS", "YYYYYYY", systemToday.plusDays(7).toDate(), systemToday.plusDays(8).toDate(), "0", "success");


        createDecisionAcknowledgementRecord(14, testProperty.getId(), "Test File", "TID000000014", systemToday.plusDays(6).toDate(),
                null, rateQualified6.getId(), null, "FPLOS", "YYYYYYY", systemToday.plusDays(6).toDate(), systemToday.plusDays(7).toDate(), "0", "success");

        createDecisionAcknowledgementRecord(15, testProperty.getId(), "Test File", "TID000000015", systemToday.plusDays(7).toDate(),
                null, rateQualified6.getId(), null, "FPLOS", "YYYYYYY", systemToday.plusDays(7).toDate(), systemToday.plusDays(8).toDate(), "0", "success");

        createDecisionAcknowledgementRecord(16, testProperty.getId(), "Test File", "TID000000016", systemToday.plusDays(6).toDate(),
                null, rateQualified7.getId(), null, "FPLOS", "YYYYYYY", systemToday.plusDays(6).toDate(), systemToday.plusDays(7).toDate(), "0", "success");

        createDecisionAcknowledgementRecord(17, testProperty.getId(), "Test File", "TID000000017", systemToday.plusDays(7).toDate(),
                null, rateQualified7.getId(), null, "FPLOS", "YYYYYYY", systemToday.plusDays(7).toDate(), systemToday.plusDays(8).toDate(), "0", "success");

        List<InventoryHistory> firstPageInventoryHistoryList = tenantCrudService().findByNamedQuery(InventoryHistory.FIND_BY_DATES_BETWEEN, QueryParameter.with("startDate", systemToday.plusDays(5)).and("endDate", systemToday.plusDays(7)).parameters(), 0, 10);

        List<Integer> ids = firstPageInventoryHistoryList.stream().map(inventoryHistory -> inventoryHistory.getId()).collect(Collectors.toList());
        List<Integer> sortedIds = new ArrayList<>(ids);
        Collections.sort(sortedIds);
        assertTrue(sortedIds.equals(ids), "The records are not is ascending sorted order");
    }

    @Test
    public void testInventoryHistoryData() throws Exception {
        List<InventoryHistory> inventoryHistoryList = tenantCrudService().findByNamedQuery(InventoryHistory.FIND_BY_DATES_BETWEEN, QueryParameter.with("startDate", startDate).and("endDate", endDate).parameters(), 0, 10);
        final InventoryHistory inventoryHistory = inventoryHistoryList.stream().filter(inventoryHistoryObj -> inventoryHistoryObj.getOccupancyDate().equals(systemToday.plusDays(1).toDate()) || inventoryHistoryObj.getDecision().equals("YYNNNNN")).findFirst().get();
        assertNotNull(inventoryHistory);
        assertEquals("FPLOS", inventoryHistory.getDecisionType(), "DecisionType should be FPLOS");
        assertEquals("failure", inventoryHistory.getAckStatus(), "AckStatus should be Success");
        assertEquals("  [ context: RATEPLAN, code: 997 , message: Bad Request , notifications: [[code=506, fields=[inventoryRestrictions], message=Each inventory restriction must contain at least one restriction.]] ]", inventoryHistory.getErrorReason());
        assertEquals(DateUtil.formatDate(systemToday.minusDays(1).toDate(), DateUtil.DEFAULT_DATE_FORMAT), DateUtil.formatDate(inventoryHistory.getDecisionDateTime(), DateUtil.DEFAULT_DATE_FORMAT), "DecisionDateTime should be same");
        assertEquals(accomType1.getAccomTypeCode(), inventoryHistory.getRoomTypeCode(), "RoomTypeCode should be Same");
        assertEquals(DateUtil.formatDate(systemToday.toDate(), DateUtil.DEFAULT_DATE_FORMAT), DateUtil.formatDate(inventoryHistory.getAckDateTime(), DateUtil.DEFAULT_DATE_FORMAT), "AckDateTime should be Same");
        assertEquals("LV0", inventoryHistory.getRateLevel(), "Rate_Level should be LV0");
        assertNull(inventoryHistory.getSrp(), "SRP should be null");
    }

    @Test
    public void testInventoryHistoryDataWhenDecisionAckSuccessStatusSkippedForSRPFPLOS() throws Exception {
        List<InventoryHistory> inventoryHistoryList = tenantCrudService().findByNamedQuery(InventoryHistory.GET_INVENTORY_HISTORY_DATA_WHEN_DECISION_ACK_SUCCESS_STATUS_SKIPPED_FOR_SRPFPLOS, QueryParameter.with("startDate", startDate).and("endDate", endDate).and("startDecisionDate", systemToday.minusDays(2)).and("isSRPFPLOSAtTotalLevel", 0).and("propertyId", testProperty.getId()).and("pageOffset", 0).and("pageSize", 10).parameters());
        final InventoryHistory inventoryHistory = inventoryHistoryList.stream().filter(inventoryHistoryObj -> inventoryHistoryObj.getOccupancyDate().equals(systemToday.plusDays(1).toDate()) || inventoryHistoryObj.getDecision().equals("YYNNNNN")).findFirst().get();
        assertNotNull(inventoryHistory);
        assertEquals("FPLOS", inventoryHistory.getDecisionType(), "DecisionType should be FPLOS");
        assertEquals("success", inventoryHistory.getAckStatus(), "AckStatus should be Success");
        assertEquals(null, inventoryHistory.getErrorReason());
        assertEquals(DateUtil.formatDate(systemToday.minusDays(1).toDate(), DateUtil.DEFAULT_DATE_FORMAT), DateUtil.formatDate(inventoryHistory.getDecisionDateTime(), DateUtil.DEFAULT_DATE_FORMAT), "DecisionDateTime should be same");
        assertEquals(accomType1.getAccomTypeCode(), inventoryHistory.getRoomTypeCode(), "RoomTypeCode should be Same");
        assertEquals(DateUtil.formatDate(systemToday.minusDays(1).toDate(), DateUtil.DEFAULT_DATE_FORMAT), DateUtil.formatDate(inventoryHistory.getAckDateTime(), DateUtil.DEFAULT_DATE_FORMAT), "AckDateTime should be Same");
        assertEquals("LV0", inventoryHistory.getRateLevel(), "Rate_Level should be LV0");
        assertNull(inventoryHistory.getSrp(), "SRP should be null");
    }

    @Test
    public void testInventoryHistoryErrorReasonFormat() {
        List<InventoryHistory> inventoryHistoryList = tenantCrudService().findByNamedQuery(InventoryHistory.FIND_BY_DATES_BETWEEN, QueryParameter.with("startDate", startDate).and("endDate", endDate).parameters(), 0, 10);
        InventoryHistory inventoryHistoryFailure = inventoryHistoryList.stream().filter(inventoryHistoryObj -> inventoryHistoryObj.getOccupancyDate().equals(systemToday.plusDays(3).toDate()) || inventoryHistoryObj.getAckStatus().equals("failure")).findFirst().get();
        InventoryHistory inventoryHistoryNotReceived = inventoryHistoryList.stream().filter(inventoryHistoryObj -> inventoryHistoryObj.getOccupancyDate().equals(systemToday.plusDays(1).toDate()) || inventoryHistoryObj.getAckStatus().equals("not received")).findFirst().get();
        assertNotNull(inventoryHistoryNotReceived);
        assertNotNull(inventoryHistoryFailure);
        assertEquals("Acknowledgement not received", inventoryHistoryNotReceived.getErrorReason());
        assertEquals("  [ context: RATEPLAN, code: 997 , message: Bad Request , notifications: [[code=506, fields=[inventoryRestrictions], message=Each inventory restriction must contain at least one restriction.]] ]", inventoryHistoryFailure.getErrorReason());
    }

    @Test
    public void testInventoryHistoryErrorReasonFormatWhenDecisionAckSuccessStatusSkippedforSRPFPLOS() {
        List<InventoryHistory> inventoryHistoryList = tenantCrudService().findByNamedQuery(InventoryHistory.GET_INVENTORY_HISTORY_DATA_WHEN_DECISION_ACK_SUCCESS_STATUS_SKIPPED_FOR_SRPFPLOS, QueryParameter.with("startDate", startDate).and("endDate", endDate).and("startDecisionDate", systemToday.minusDays(2)).and("isSRPFPLOSAtTotalLevel", 0).and("propertyId", testProperty.getId()).and("pageOffset", 0).and("pageSize", 10).parameters());
        InventoryHistory inventoryHistoryFailure = inventoryHistoryList.stream().filter(inventoryHistoryObj -> inventoryHistoryObj.getOccupancyDate().equals(systemToday.plusDays(3).toDate()) || inventoryHistoryObj.getAckStatus().equals("failure")).findFirst().get();
        InventoryHistory inventoryHistoryNotReceived = inventoryHistoryList.stream().filter(inventoryHistoryObj -> inventoryHistoryObj.getOccupancyDate().equals(systemToday.plusDays(1).toDate()) || inventoryHistoryObj.getAckStatus().equals("not received")).findFirst().get();
        assertNotNull(inventoryHistoryNotReceived);
        assertNotNull(inventoryHistoryFailure);
        assertEquals("Acknowledgement not received", inventoryHistoryNotReceived.getErrorReason());
        assertEquals("  [ context: RATEPLAN, code: 997 , message: Bad Request , notifications: [[code=506, fields=[inventoryRestrictions], message=Each inventory restriction must contain at least one restriction.]] ]", inventoryHistoryFailure.getErrorReason());
    }

    @Test
    public void shouldNotContainDataForDayBeforeSystemToday() throws Exception {
        List<InventoryHistory> inventoryHistoryList = tenantCrudService().findByNamedQuery(InventoryHistory.FIND_BY_DATES_BETWEEN, QueryParameter.with("startDate", startDate).and("endDate", endDate).parameters(), 0, 10);
        assertTrue(inventoryHistoryList.size() > 0, "should return atleast one inventoryHistory record");
        final boolean foundDataForDayBeforeSystemToday = inventoryHistoryList.stream().anyMatch(inventoryHistoryObj -> LocalDate.fromDateFields(inventoryHistoryObj.getOccupancyDate()).isBefore(systemToday));
        assertFalse(foundDataForDayBeforeSystemToday, "should not contain data for day before systemToday");
    }

    @Test
    public void shouldFetchDataWithDecisionForMaxDecisionDateTimeAndDayBeforeMaxDecisionDateTime() throws Exception {
        List<InventoryHistory> inventoryHistoryList = tenantCrudService().findByNamedQuery(InventoryHistory.FIND_BY_DATES_BETWEEN, QueryParameter.with("startDate", startDate).and("endDate", endDate).parameters(), 0, 2);
        assertEquals(2, inventoryHistoryList.size());
        inventoryHistoryList.stream().forEach(inventoryHistory -> {
            assertTrue(isBetweenInclusive(startDate.minusDays(2), endDate, LocalDate.fromDateFields(inventoryHistory.getDecisionDateTime())), "DecissionDateTime should be between yesterday and today");
        });
    }

    @Test
    public void shouldNotFailForNullRateLevel() throws Exception {
        List<InventoryHistory> inventoryHistoryList = tenantCrudService().findByNamedQuery(InventoryHistory.FIND_BY_DATES_BETWEEN, QueryParameter.with("startDate", systemToday).and("endDate", systemToday.plusDays(5)).parameters(), 0, 6);
        assertTrue(inventoryHistoryList.size() > 0, "should return atleast one inventoryHistory record");
        final boolean foundInventoryHistoryWithNullRateLevel = inventoryHistoryList.stream().anyMatch(inventoryHistory -> inventoryHistory.getRateLevel() == null);
        assertTrue(foundInventoryHistoryWithNullRateLevel, "should contain atleast one inventoryHistory record with null rateLevel.");
    }

    @Test
    public void shouldHaveNullValueForSRPForFPLOSAsDecisionTypeForQualifiedRateAsLV0() throws Exception {
        List<InventoryHistory> inventoryHistoryList = tenantCrudService().findByNamedQuery(InventoryHistory.FIND_BY_DATES_BETWEEN, QueryParameter.with("startDate", systemToday).and("endDate", systemToday.plusDays(5)).parameters(), 0, 6);
        assertTrue(inventoryHistoryList.size() > 0, "should return atleast one inventoryHistory record");
        final InventoryHistory inventoryHistory = inventoryHistoryList.stream().filter(inventoryHistoryObj -> inventoryHistoryObj.getSrp() == null).findFirst().get();
        assertNotNull(inventoryHistory, "should contain atleast one inventoryHistory record with null SRP.");
        assertEquals("FPLOS", inventoryHistory.getDecisionType(), "DecisionType should be FPLOS when qualified rate is LV0");
        assertEquals("LV0", inventoryHistory.getRateLevel(), "Rate level should be LV0");
        assertEquals(null, inventoryHistory.getSrp(), "SRP should be null");
        assertEquals("failure", inventoryHistory.getAckStatus(), "Acknowledgement Status should be failure");
    }

    @Test
    public void shouldHaveNullErrorReasonForAcknowledgementStatusSuccessWhenDecisionAckSuccessStatusSkippedForSRPFPLOS() throws Exception {
        List<InventoryHistory> inventoryHistoryList = tenantCrudService().findByNamedQuery(InventoryHistory.GET_INVENTORY_HISTORY_DATA_WHEN_DECISION_ACK_SUCCESS_STATUS_SKIPPED_FOR_SRPFPLOS, QueryParameter.with("startDate", startDate).and("endDate", endDate).and("startDecisionDate", systemToday.minusDays(2)).and("isSRPFPLOSAtTotalLevel", 0).and("propertyId", testProperty.getId()).and("pageOffset", 0).and("pageSize", 10).parameters());
        assertTrue(inventoryHistoryList.size() > 0, "should return atleast one inventoryHistory record");
        final InventoryHistory inventoryHistory = inventoryHistoryList.stream().filter(inventoryHistoryObj -> inventoryHistoryObj.getAckStatus().equals("success")).findFirst().get();
        assertEquals("success", inventoryHistory.getAckStatus(), "Acknowledgement Status should be Success");
        assertEquals(null, inventoryHistory.getErrorReason(), "Error Reason must be null");
    }

    @Test
    public void shouldHaveNullErrorReasonForAcknowledgementStatusSuccess() throws Exception {
        List<InventoryHistory> inventoryHistoryList = tenantCrudService().findByNamedQuery(InventoryHistory.FIND_BY_DATES_BETWEEN, QueryParameter.with("startDate", startDate).and("endDate", endDate).parameters(), 1, 2);
        assertTrue(inventoryHistoryList.size() > 0, "should return atleast one inventoryHistory record");
        final InventoryHistory inventoryHistory = inventoryHistoryList.stream().filter(inventoryHistoryObj -> inventoryHistoryObj.getAckStatus().equals("success")).findFirst().get();
        assertEquals("success", inventoryHistory.getAckStatus(), "Acknowledgement Status should be Success");
        assertEquals(null, inventoryHistory.getErrorReason(), "Error Reason must be null");
    }

    @Test
    public void shouldNotFailForNullRoomTypeCode() throws Exception {
        List<InventoryHistory> inventoryHistoryList = tenantCrudService().findByNamedQuery(InventoryHistory.FIND_BY_DATES_BETWEEN, QueryParameter.with("startDate", systemToday).and("endDate", systemToday.plusDays(5)).parameters(), 0, 6);
        assertTrue(inventoryHistoryList.size() > 0, "should return atleast one inventoryHistory record");
        final boolean foundInventoryHistoryWithNullRoomTypeCode = inventoryHistoryList.stream().anyMatch(inventoryHistory -> inventoryHistory.getRoomTypeCode() == null);
        assertTrue(foundInventoryHistoryWithNullRoomTypeCode, "should contain atleast one inventoryHistory record with null RoomTypeCode.");
    }

    @Test
    public void shouldContainRateCodeNameAsHOMEWhenDecisionTypeIsOverbookingAndRoomTypeCodeIsNull() throws Exception {
        List<InventoryHistory> inventoryHistoryList = tenantCrudService().findByNamedQuery(InventoryHistory.FIND_BY_DATES_BETWEEN, QueryParameter.with("startDate", systemToday).and("endDate", systemToday.plusDays(5)).parameters(), 0, 6);
        assertTrue(inventoryHistoryList.size() > 0, "should return atleast one inventoryHistory record");
        final InventoryHistory inventoryHistory = inventoryHistoryList.stream()
                .filter(inventoryHistoryObj -> inventoryHistoryObj.getRoomTypeCode() == null && Constants.OVERBOOKING.equalsIgnoreCase(inventoryHistoryObj.getDecisionType()))
                .findFirst().get();
        assertNotNull(inventoryHistory);
        assertEquals("House", inventoryHistory.getRateLevel(), "RateLevel should be House for inventoryHistory with decisionType as Overbooking and no RoomTypeCode");
    }

    @Test
    public void shouldContainRateLevelAsHouseWhenDecisionTypeIsOverbookingAndRoomTypeCodeIsNullWhenDecisionAckSuccessStatusSkippedForSRPFPLOS() throws Exception {
        List<InventoryHistory> inventoryHistoryList = tenantCrudService().findByNamedQuery(InventoryHistory.GET_INVENTORY_HISTORY_DATA_WHEN_DECISION_ACK_SUCCESS_STATUS_SKIPPED_FOR_SRPFPLOS, QueryParameter.with("startDate", startDate).and("endDate", endDate).and("startDecisionDate", systemToday.minusDays(2)).and("isSRPFPLOSAtTotalLevel", 0).and("propertyId", testProperty.getId()).and("pageOffset", 0).and("pageSize", 10).parameters());
        assertTrue(inventoryHistoryList.size() > 0, "should return atleast one inventoryHistory record");
        final InventoryHistory inventoryHistory = inventoryHistoryList.stream()
                .filter(inventoryHistoryObj -> inventoryHistoryObj.getRoomTypeCode() == null && Constants.OVERBOOKING.equalsIgnoreCase(inventoryHistoryObj.getDecisionType()))
                .findFirst().get();
        assertNotNull(inventoryHistory);
        assertEquals("House", inventoryHistory.getRateLevel(), "RateLevel should be House for inventoryHistory with decisionType as Overbooking and no RoomTypeCode");
    }

    private void createDecisionAcknowledgementRecord(Integer decisionAckStatusID, Integer propertyID, String decisionFileName,
                                                     String transactionID, Date occupancyDate, Integer rateUnqualifiedID, Integer rateQualifiedID, Integer accomTypeID,
                                                     String decisionType, String decision, Date decision_Date_Time, Date acknowledgementDateTime, String errorCode, String errorDescription) {

        final String sqlCreate = "SET IDENTITY_INSERT [dbo].[Decision_Ack_Status] ON  "
                + "INSERT INTO [dbo].[Decision_Ack_Status](Decision_Ack_Status_ID, Property_ID, Decision_File_Name, Transaction_ID, Occupancy_Date, Rate_Unqualified_ID, "
                + "Rate_Qualified_ID, Accom_Type_ID, Decision_Type, Decision, Decision_Date_Time, Acknowledgement_Date_Time, Error_Code, Error_Description) "
                + "VALUES(:decisionAckStatusID,:propertyID,:decisionFileName,:transactionID,:occupancyDate,:rateUnqualifiedID,:rateQualifiedID,"
                + ":accomTypeID,:decisionType,:decision,:decision_Date_Time,:acknowledgementDateTime,:errorCode,:errorDescription)  SET IDENTITY_INSERT [dbo].[Decision_Ack_Status] OFF";
        Query query = tenantCrudService().getEntityManager().createNativeQuery(sqlCreate);
        query.setParameter("decisionAckStatusID", decisionAckStatusID);
        query.setParameter("propertyID", propertyID);
        query.setParameter("decisionFileName", decisionFileName);
        query.setParameter("transactionID", transactionID);
        query.setParameter("occupancyDate", occupancyDate);
        query.setParameter("rateUnqualifiedID", rateUnqualifiedID);
        query.setParameter("rateQualifiedID", rateQualifiedID);
        query.setParameter("accomTypeID", accomTypeID);
        query.setParameter("decisionType", decisionType);
        query.setParameter("decision", decision);
        query.setParameter("decision_Date_Time", decision_Date_Time);
        query.setParameter("acknowledgementDateTime", acknowledgementDateTime);
        query.setParameter("errorCode", errorCode);
        query.setParameter("errorDescription", errorDescription);
        if (query.executeUpdate() != 1) {
            throw new TetrisException("Decision_Ack_Status not inserted.");
        }
        tenantCrudService().getEntityManager().flush();
    }

    private void createPaceDecisionQualifiedFplosRecord(Integer propertyId, Date occupancyDate, Integer rateQualifiedId, Integer accomTypeId, String decision, Date decisionDateTime, Date acknowledgementDateTime) {
        PaceDecisionQualifiedFPLOS p = new PaceDecisionQualifiedFPLOS();
        p.setDecisionId(321);
        p.setAccomTypeId(accomTypeId);
        p.setArrivalDate(occupancyDate);
        p.setRateQualifiedId(rateQualifiedId);
        p.setCreateDate(decisionDateTime);
        p.setPropertyId(propertyId);
        p.setFplos(decision);
        tenantCrudService().save(p);
    }


    boolean isBetweenInclusive(LocalDate start, LocalDate end, LocalDate target) {
        return !target.isBefore(start) && !target.isAfter(end);
    }
}