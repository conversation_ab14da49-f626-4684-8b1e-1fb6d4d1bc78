package com.ideas.tetris.pacman.services.continuouspricing;

import com.ideas.g3.rule.ThreadLocalExtension;
import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.configparams.GUIConfigParamName;
import com.ideas.tetris.pacman.common.configparams.IPConfigParamName;
import com.ideas.tetris.pacman.common.configparams.IntegrationConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomClass;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomType;
import com.ideas.tetris.pacman.services.accommodation.service.AccommodationService;
import com.ideas.tetris.pacman.services.bestavailablerate.PriceService;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.*;
import com.ideas.tetris.pacman.services.configsparam.service.ConfigParameterNameService;
import com.ideas.tetris.pacman.services.cprecommendedfloorceiling.entity.CPRecommendedFloorCeilingEntity;
import com.ideas.tetris.pacman.services.cprecommendedfloorceiling.service.CPRecommendedFloorCeilingService;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.demandoverride.entity.ArrivalDemandOverride;
import com.ideas.tetris.pacman.services.demandoverride.entity.OccupancyDemandOverride;
import com.ideas.tetris.pacman.services.eventaggregator.SyncEvent;
import com.ideas.tetris.pacman.services.eventaggregator.SyncEventAggregatorService;
import com.ideas.tetris.pacman.services.informationmanager.entity.InfoMgrInstanceEntity;
import com.ideas.tetris.pacman.services.informationmanager.entity.InformationMgrAlertConfigEntity;
import com.ideas.tetris.pacman.services.opera.entity.DailyBarConfig;
import com.ideas.tetris.pacman.services.opera.entity.DailyBarRateChart;
import com.ideas.tetris.pacman.services.pricingconfiguration.entity.PricingAccomClass;
import com.ideas.tetris.pacman.services.pricingconfiguration.entity.TransientPricingBaseAccomType;
import com.ideas.tetris.pacman.services.product.Product;
import com.ideas.tetris.pacman.services.tax.entity.Tax;
import com.ideas.tetris.pacman.services.tax.service.TaxService;
import com.ideas.tetris.pacman.services.unqualifiedrate.entity.RateUnqualified;
import com.ideas.tetris.pacman.services.unqualifiedrate.entity.RateUnqualifiedClosed;
import com.ideas.tetris.pacman.services.unqualifiedrate.entity.RateUnqualifiedLOSOverride;
import com.ideas.tetris.pacman.services.unqualifiedrate.entity.RateUnqualifiedUserOverride;
import com.ideas.tetris.pacman.util.BigDecimalUtil;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.entity.DecisionUploadType;
import com.ideas.tetris.platform.common.errorhandling.ErrorCode;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.common.externalsystem.ExternalSystem;
import com.ideas.tetris.platform.common.externalsystem.ExternalSystemHelper;
import com.ideas.tetris.platform.services.clientservices.dto.pacman.PacmanSubscriberDto;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import com.ideas.tetris.platform.services.sas.log.SasDbToolService;
import org.joda.time.LocalDate;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.ideas.tetris.pacman.services.continuouspricing.ContinuousPricingMigrationService.*;
import static com.ideas.tetris.platform.common.crudservice.QueryParameter.with;
import static java.util.Arrays.asList;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class ContinuousPricingMigrationServiceTest {

    @RegisterExtension
    public ThreadLocalExtension threadLocalExtension = new ThreadLocalExtension();
    @Mock
    DateService dateService;
    @Mock
    SyncEventAggregatorService syncEventAggregatorService;
    @Mock
    TaxService taxService;
    @Mock
    PacmanConfigParamsService configParamsService;
    @Mock
    CrudService tenantCrudService;
    @Mock
    SasDbToolService sasDbToolService;
    @Mock
    AccommodationService accommodationService;
    @Mock
    PriceService priceService;
    @Mock
    CPRecommendedFloorCeilingService cpRecommendedFloorCeilingService;
    @InjectMocks
    ContinuousPricingMigrationService continuousPricingMigrationService;
    @Captor
    ArgumentCaptor<PricingAccomClass> pricingAccomClassArgumentCaptor;
    @Captor
    ArgumentCaptor<List<PricingAccomClass>> pricingAccomClassListArgumentCaptor;
    @Captor
    ArgumentCaptor<List<CPConfigOffsetAccomType>> configOffsetAccomTypeArgumentCaptor;
    @Captor
    ArgumentCaptor<Set<AccomTypeSupplement>> saveArgumentCaptor;
    LocalDate caughtUpDate = new LocalDate();
    @Mock
    private ExternalSystemHelper externalSystemHelperMock;
    @Mock
    private ConfigParameterNameService configParameterNameService;

    @Test
    public void migrateToTaxInclusiveWithNoApplyTax() {
        when(configParamsService.getParameterValue(IntegrationConfigParamName.CORE_PROPERTY_EXTERNAL_SYSTEM.value())).thenReturn(Constants.OPERA.toLowerCase());
        when(configParamsService.getBooleanParameterValue(Constants.PACMAN_INTEGRATION + Constants.OPERA.toLowerCase() + Constants.APPLY_TAX)).thenReturn(false);

        continuousPricingMigrationService.migrateToTaxInclusive();
    }

    @Test
    public void migrateToTaxInclusiveWithApplyTaxForOperaWithTaxAdjustmentAndNoHTNGOutboundConfigured() {
        setUpTaxParametersFor(Constants.OPERA, true, BigDecimal.TEN, Constants.NONE);

        continuousPricingMigrationService.migrateToTaxInclusive();

        verifyRoomTaxIsSetTo(BigDecimal.TEN.setScale(2));
        verifyApplyTaxIsDisabledFor(Constants.OPERA);
        verifyTaxAdjustmentIsSetAsNullFor(Constants.OPERA);
    }

    @Test
    public void migrateToTaxInclusiveWithApplyTaxForOperaAndHNTGOutboundWithMultipleDecisionsWithSameTax() {
        setUpTaxParametersFor(Constants.OPERA, true, BigDecimal.TEN, Constants.NONE);
        enableUploadTypeAndTaxAdjustmentFor(ExternalSystem.SITEMINDER, Constants.HTNG_DAILYBAR, BigDecimal.TEN);
        enableUploadTypeAndTaxAdjustmentFor(ExternalSystem.SITEMINDER, Constants.HTNG_LRV_AT_ROOM_CLASS, BigDecimal.TEN);

        continuousPricingMigrationService.migrateToTaxInclusive();

        verifyRoomTaxIsSetTo(BigDecimal.TEN.setScale(2));
        verifyApplyTaxIsDisabledFor(Constants.OPERA);
        verifyTaxAdjustmentIsSetAsNullFor(Constants.OPERA);
        verifyDecisionTypeTaxAdjustmentIsSetAsZeroFor(ExternalSystem.SITEMINDER, Constants.HTNG_DAILYBAR);
        verifyDecisionTypeTaxAdjustmentIsSetAsZeroFor(ExternalSystem.SITEMINDER, Constants.HTNG_LRV_AT_ROOM_CLASS);
    }

    private void verifyTaxAdjustmentIsSetAsNullFor(String externalSystem) {
        verify(configParamsService).updateParameterValue(Constants.PACMAN_INTEGRATION + externalSystem.toLowerCase() + Constants.TAX_ADJUSTMENT_VALUE, (BigDecimal) null);
    }

    @Test
    public void enableFullDecisionsForFullWindowPostCPMigrationStep() {
        continuousPricingMigrationService.updateEnableFullDecisionsForFullWindowPostCPMigrationStep(true);
        verify(configParamsService).updateParameterValue(FeatureTogglesConfigParamName.ENABLE_FULL_DECISIONS_FOR_HILTON_POST_CP_MIGRATION.value(), true);
    }

    @Test
    void updateGenerateRatchetRateExtractParam() {
        continuousPricingMigrationService.updateGenerateRatchetRateExtractParam();
        verify(configParamsService).addParameterValue(IntegrationConfigParamName.GENERATE_RATE_EXTRACT.value(Constants.RATCHET), "QRates");
    }

    @Test
    void updateIntegrationDecisionTypeParam() {
        String decisionTypes = "fplos,hoteloverbooking,roomtypeoverbooking";
        continuousPricingMigrationService.updateIntegrationDecisionTypeParam();
        verify(configParamsService).addParameterValue(IntegrationConfigParamName.DECISIONTYPES.value(Constants.PCRS), decisionTypes);
        verify(configParamsService).addParameterValue(IntegrationConfigParamName.DECISIONTYPES.value(Constants.HILSTAR), decisionTypes);
    }

    @Test
    void updateDailyBarUploadTypeParam() {
        continuousPricingMigrationService.updateDailyBarUploadTypeParam();
        verify(configParamsService).addParameterValue(IntegrationConfigParamName.DAILY_BAR_UPLOADTYPE.value(Constants.PCRS), DecisionUploadType.DIFFERENTIAL.getConfigParamValue());
        verify(configParamsService).addParameterValue(IntegrationConfigParamName.DAILY_BAR_UPLOADTYPE.value(Constants.HILSTAR), DecisionUploadType.DIFFERENTIAL.getConfigParamValue());
    }

    @Test
    void updateFplosUploadTypeParam() {
        continuousPricingMigrationService.updateHiltonCPDecisionUploadTypeParams();
        verify(configParamsService).addParameterValue(IntegrationConfigParamName.DAILY_BAR_UPLOADTYPE.value(Constants.PCRS), DecisionUploadType.DIFFERENTIAL.getConfigParamValue());
        verify(configParamsService).addParameterValue(IntegrationConfigParamName.DAILY_BAR_UPLOADTYPE.value(Constants.HILSTAR), DecisionUploadType.DIFFERENTIAL.getConfigParamValue());
        verify(configParamsService).addParameterValue(IntegrationConfigParamName.FPLOS_UPLOADTYPE.value(Constants.PCRS), DecisionUploadType.DIFFERENTIAL.getConfigParamValue());
        verify(configParamsService).addParameterValue(IntegrationConfigParamName.FPLOS_UPLOADTYPE.value(Constants.HILSTAR), DecisionUploadType.DIFFERENTIAL.getConfigParamValue());
        verify(configParamsService).addParameterValue(IntegrationConfigParamName.HOTEL_OVERBOOKING_UPLOADTYPE.value(Constants.PCRS), DecisionUploadType.DIFFERENTIAL.getConfigParamValue());
        verify(configParamsService).addParameterValue(IntegrationConfigParamName.HOTEL_OVERBOOKING_UPLOADTYPE.value(Constants.HILSTAR), DecisionUploadType.DIFFERENTIAL.getConfigParamValue());
        verify(configParamsService).addParameterValue(IntegrationConfigParamName.ROOM_TYPE_OVERBOOKING_UPLOAD_TYPE.value(Constants.PCRS), DecisionUploadType.DIFFERENTIAL.getConfigParamValue());
        verify(configParamsService).addParameterValue(IntegrationConfigParamName.ROOM_TYPE_OVERBOOKING_UPLOAD_TYPE.value(Constants.HILSTAR), DecisionUploadType.DIFFERENTIAL.getConfigParamValue());
    }

    @Test
    void updateBarFpLosByRankUploadTypeParam() {
        continuousPricingMigrationService.updateBarFpLosByRankUploadTypeParam();
        verify(configParamsService).addParameterValue(IntegrationConfigParamName.BARFPLOSBYRANK_UPLOADTYPE.value(Constants.PCRS), DecisionUploadType.NONE.getConfigParamValue());
        verify(configParamsService).addParameterValue(IntegrationConfigParamName.BARFPLOSBYRANK_UPLOADTYPE.value(Constants.HILSTAR), DecisionUploadType.NONE.getConfigParamValue());
    }

    @Test
    public void updateFcstingOptimizationUploadWindowDays() {
        continuousPricingMigrationService.updateFcstingOptimizationUploadWindowDays();
        verify(configParamsService).addParameterValue(IPConfigParamName.FORECASTING_FORECAST_WINDOW_BDE.value(), "372");
        verify(configParamsService).addParameterValue(IPConfigParamName.OPTIMIZATION_OPTIMIZATION_WINDOW_BDE.value(), "372");
        verify(configParamsService).addParameterValue(IntegrationConfigParamName.FUTURE_DAYS.value(), "372");
        verify(configParamsService).addParameterValue(IntegrationConfigParamName.UPLOAD_DECISION_UPLOAD_WINDOW_BDEDAYS.value(), "365");
    }

    @Test
    public void migrateToTaxInclusiveWithApplyTaxForOperaWithTaxAdjustmentAndHNTGOutboundWithTaxAdjustmentSetAsBlank() {
        setUpTaxParametersFor(Constants.OPERA, true, BigDecimal.TEN, Constants.NONE);
        enableUploadTypeAndTaxAdjustmentFor(ExternalSystem.SITEMINDER, Constants.HTNG_DAILYBAR, null);
        enableUploadTypeAndTaxAdjustmentFor(ExternalSystem.SITEMINDER, Constants.HTNG_LRV_AT_ROOM_TYPE, null);
        when(configParameterNameService.getIntegrationUploadTypeParameterName(ExternalSystem.SITEMINDER.getExternalSystemName().toLowerCase(), Constants.HTNG_LRV_AT_ROOM_CLASS, Constants.UPLOAD_TYPE)).thenReturn("pacman.integration.siteminder.LRVatRoomClass.uploadtype");

        continuousPricingMigrationService.migrateToTaxInclusive();

        verifyRoomTaxIsSetTo(BigDecimal.TEN.setScale(2));
        verifyApplyTaxIsDisabledFor(Constants.OPERA);
        verifyDecisionTypeTaxAdjustmentIsSetAsZeroFor(ExternalSystem.SITEMINDER, Constants.HTNG_DAILYBAR);
        verifyDecisionTypeTaxAdjustmentIsSetAsZeroFor(ExternalSystem.SITEMINDER, Constants.HTNG_LRV_AT_ROOM_TYPE);
        verifyDecisionTypeUploadTypesAreInvokedFor(ExternalSystem.SITEMINDER, 2);
    }

    @Test
    public void migrateToTaxInclusiveWithApplyTaxWithNullValue() {
        setUpTaxParametersFor(Constants.OPERA, true, null, Constants.NONE);
        enableUploadTypeAndTaxAdjustmentFor(ExternalSystem.SITEMINDER, Constants.HTNG_DAILYBAR, null);

        continuousPricingMigrationService.migrateToTaxInclusive();

        verifyRoomTaxIsSetTo(BigDecimal.ZERO);
        verifyApplyTaxIsDisabledFor(Constants.OPERA);
        verifyDecisionTypeTaxAdjustmentIsNotSetAsZeroFor(Constants.HTNG_DAILYBAR);
        verifyDecisionTypeTaxAdjustmentIsNotSetAsZeroFor(Constants.HTNG_LRV_AT_ROOM_CLASS);
    }

    @Test
    public void shouldFailMigrationToTaxInclusiveForOperaPropertyWhenTaxAdjustmentValueIsNullForOperaAndHTNGOutBoundIsNonZero() {
        setUpTaxParametersFor(Constants.OPERA, true, null, Constants.NONE);

        enableUploadTypeAndTaxAdjustmentFor(ExternalSystem.SITEMINDER, Constants.HTNG_DAILYBAR, new BigDecimal(11.0));

        disableUploadType(ExternalSystem.SITEMINDER, Constants.HTNG_LRV_AT_ROOM_TYPE);
        disableUploadType(ExternalSystem.SITEMINDER, Constants.HTNG_LRV_AT_ROOM_CLASS);

        try {
            continuousPricingMigrationService.migrateToTaxInclusive();
        } catch (TetrisException e) {
            assertTrue(e.getMessage().contains("Tax adjustment value for external system - [OPERA] is different than tax adjustment value of configured HTNG outbounds."));
            assertEquals(ErrorCode.VENDOR_CONFIG_ERROR, e.getErrorCode());
        }

        verifyThatTaxParameterUpdateIsSkippedFor(Constants.OPERA, ExternalSystem.SITEMINDER);
    }

    @Test
    public void migrateToTaxInclusiveForExternalSystemAsNGIAndTaxAdjustmentValueAsZero() {
        setUpTaxParametersFor(Constants.NGI, false, null, Constants.NONE);

        enableUploadTypeAndTaxAdjustmentFor(ExternalSystem.SITEMINDER, Constants.HTNG_DAILYBAR, new BigDecimal(0.0));
        disableUploadType(ExternalSystem.SITEMINDER, Constants.HTNG_LRV_AT_ROOM_CLASS);
        disableUploadType(ExternalSystem.SITEMINDER, Constants.HTNG_LRV_AT_ROOM_TYPE);

        continuousPricingMigrationService.migrateToTaxInclusive();

        verifyRoomTaxIsSetTo(BigDecimal.ZERO.setScale(2));

        verifySetUpForTaxParameters(Constants.NGI, 1);
        verifyDecisionTypeUploadTypesAreInvokedFor(ExternalSystem.SITEMINDER, 2);
        verifyDecisionTypeTaxAdjustmentIsInvoked(ExternalSystem.SITEMINDER, Constants.HTNG_DAILYBAR);
    }

    @Test
    public void migrateToTaxInclusiveForExternalSystemAsNGIAndTaxAdjustmentValueAsTen() {
        setUpTaxParametersFor(Constants.NGI, false, null, Constants.NONE);

        enableUploadTypeAndTaxAdjustmentFor(ExternalSystem.SITEMINDER, Constants.HTNG_DAILYBAR, new BigDecimal(10.0));
        enableUploadTypeAndTaxAdjustmentFor(ExternalSystem.SITEMINDER, Constants.HTNG_LRV_AT_ROOM_CLASS, new BigDecimal(10.0));

        disableUploadType(ExternalSystem.SITEMINDER, Constants.HTNG_LRV_AT_ROOM_TYPE);

        continuousPricingMigrationService.migrateToTaxInclusive();


        verifyRoomTaxIsSetTo(BigDecimal.TEN.setScale(2));
        verifyDecisionTypeTaxAdjustmentIsSetAsZeroFor(ExternalSystem.SITEMINDER, Constants.HTNG_DAILYBAR);
        verifyDecisionTypeTaxAdjustmentIsSetAsZeroFor(ExternalSystem.SITEMINDER, Constants.HTNG_LRV_AT_ROOM_CLASS);

        verifySetUpForTaxParameters(Constants.NGI, 1);
        verifyDecisionTypeUploadTypesAreInvokedFor(ExternalSystem.SITEMINDER, 2);
        verifyDecisionTypeTaxAdjustmentIsInvoked(ExternalSystem.SITEMINDER, Constants.HTNG_DAILYBAR);
        verifyDecisionTypeTaxAdjustmentIsInvoked(ExternalSystem.SITEMINDER, Constants.HTNG_LRV_AT_ROOM_CLASS);
    }

    @Test
    public void migrateToTaxInclusiveForExternalSystemAsNGIAndTaxAdjustmentValueAsNull() {
        setUpTaxParametersFor(Constants.NGI, false, null, Constants.NONE);

        enableUploadTypeAndTaxAdjustmentFor(ExternalSystem.SITEMINDER, Constants.HTNG_DAILYBAR, null);
        enableUploadTypeAndTaxAdjustmentFor(ExternalSystem.SITEMINDER, Constants.HTNG_LRV_AT_ROOM_CLASS, null);

        disableUploadType(ExternalSystem.SITEMINDER, Constants.HTNG_LRV_AT_ROOM_TYPE);

        continuousPricingMigrationService.migrateToTaxInclusive();

        verifyThatTaxParameterUpdateIsSkippedFor(Constants.NGI, ExternalSystem.SITEMINDER);
    }

    @Test
    public void migrateToTaxInclusiveForExternalSystemAsNGIAndTaxAdjustmentValueAsTenForTwoHTNGOutBounds() {
        setUpTaxParametersFor(Constants.NGI, false, null, Constants.NONE);

        enableUploadTypeAndTaxAdjustmentFor(ExternalSystem.SITEMINDER, Constants.HTNG_DAILYBAR, new BigDecimal(10.0));
        enableUploadTypeAndTaxAdjustmentFor(ExternalSystem.SITEMINDER, Constants.HTNG_LRV_AT_ROOM_CLASS, new BigDecimal(10.0));
        enableUploadTypeAndTaxAdjustmentFor(ExternalSystem.HBSI, Constants.HTNG_DAILYBAR, new BigDecimal(10.0));
        enableUploadTypeAndTaxAdjustmentFor(ExternalSystem.HBSI, Constants.HTNG_LRV_AT_ROOM_CLASS, new BigDecimal(10.0));

        disableUploadType(ExternalSystem.SITEMINDER, Constants.HTNG_LRV_AT_ROOM_TYPE);
        disableUploadType(ExternalSystem.HBSI, Constants.HTNG_LRV_AT_ROOM_TYPE);

        continuousPricingMigrationService.migrateToTaxInclusive();

        verifyRoomTaxIsSetTo(BigDecimal.TEN.setScale(2));
        verifyDecisionTypeTaxAdjustmentIsSetAsZeroFor(ExternalSystem.SITEMINDER, Constants.HTNG_DAILYBAR);
        verifyDecisionTypeTaxAdjustmentIsSetAsZeroFor(ExternalSystem.SITEMINDER, Constants.HTNG_LRV_AT_ROOM_CLASS);
        verifyDecisionTypeTaxAdjustmentIsSetAsZeroFor(ExternalSystem.HBSI, Constants.HTNG_DAILYBAR);
        verifyDecisionTypeTaxAdjustmentIsSetAsZeroFor(ExternalSystem.HBSI, Constants.HTNG_LRV_AT_ROOM_CLASS);

        verifySetUpForTaxParameters(Constants.NGI, 1);
        verifyDecisionTypeUploadTypesAreInvokedFor(ExternalSystem.SITEMINDER, 2);
        verifyDecisionTypeUploadTypesAreInvokedFor(ExternalSystem.HBSI, 2);
        verifyDecisionTypeTaxAdjustmentIsInvoked(ExternalSystem.SITEMINDER, Constants.HTNG_DAILYBAR);
        verifyDecisionTypeTaxAdjustmentIsInvoked(ExternalSystem.SITEMINDER, Constants.HTNG_LRV_AT_ROOM_CLASS);
        verifyDecisionTypeTaxAdjustmentIsInvoked(ExternalSystem.HBSI, Constants.HTNG_DAILYBAR);
        verifyDecisionTypeTaxAdjustmentIsInvoked(ExternalSystem.HBSI, Constants.HTNG_LRV_AT_ROOM_CLASS);
    }

    @Test
    public void migrateToTaxInclusiveForOXIPropertyAndTaxAdjustmentValueIsTenForOperaAndHTNGOutBound() {
        setUpTaxParametersFor(Constants.NGI, false, null, Constants.OXI);
        setUpTaxParametersFor(Constants.OPERA, true, new BigDecimal(10.0));
        enableUploadTypeAndTaxAdjustmentFor(ExternalSystem.SITEMINDER, Constants.HTNG_DAILYBAR, new BigDecimal(10.0));

        disableUploadType(ExternalSystem.SITEMINDER, Constants.HTNG_LRV_AT_ROOM_TYPE);
        disableUploadType(ExternalSystem.SITEMINDER, Constants.HTNG_LRV_AT_ROOM_CLASS);

        continuousPricingMigrationService.migrateToTaxInclusive();

        verifyApplyTaxIsDisabledFor(Constants.OPERA);
        verifyRoomTaxIsSetTo(BigDecimal.TEN.setScale(2));
        verifyDecisionTypeTaxAdjustmentIsSetAsZeroFor(ExternalSystem.SITEMINDER, Constants.HTNG_DAILYBAR);

        verifySetUpForTaxParameters(Constants.OPERA, 2);
        verifyDecisionTypeUploadTypesAreInvokedFor(ExternalSystem.SITEMINDER, 2);
        verifyDecisionTypeTaxAdjustmentIsInvoked(ExternalSystem.SITEMINDER, Constants.HTNG_DAILYBAR);
    }

    @Test
    public void shouldFailMigrationToTaxInclusiveForOXIPropertyWhenTaxAdjustmentValueIsDifferentForOperaAndHTNGOutBound() {
        setUpTaxParametersFor(Constants.NGI, false, null, Constants.OXI);
        setUpTaxParametersFor(Constants.OPERA, true, new BigDecimal(10.0));
        enableUploadTypeAndTaxAdjustmentFor(ExternalSystem.SITEMINDER, Constants.HTNG_DAILYBAR, new BigDecimal(11.0));

        disableUploadType(ExternalSystem.SITEMINDER, Constants.HTNG_LRV_AT_ROOM_TYPE);
        disableUploadType(ExternalSystem.SITEMINDER, Constants.HTNG_LRV_AT_ROOM_CLASS);

        try {
            continuousPricingMigrationService.migrateToTaxInclusive();
        } catch (TetrisException e) {
            assertTrue(e.getMessage().contains("Tax adjustment value for external system - [OPERA] is different than tax adjustment value of configured HTNG outbounds."));
            assertEquals(ErrorCode.VENDOR_CONFIG_ERROR, e.getErrorCode());
        }

        verifyThatTaxParameterUpdateIsSkippedFor(Constants.OPERA, ExternalSystem.SITEMINDER);
    }

    @Test
    public void shouldSkipMigrationToTaxInclusiveForOXIPropertyWhenApplyTaxIsFalseForOperaAndTaxAdjustmentValueIsAvailableForHTNGOutBound() {
        setUpTaxParametersFor(Constants.NGI, false, null, Constants.OXI);
        setUpTaxParametersFor(Constants.OPERA, false, null);
        enableUploadTypeAndTaxAdjustmentFor(ExternalSystem.SITEMINDER, Constants.HTNG_DAILYBAR, new BigDecimal(11.0));
        disableUploadType(ExternalSystem.SITEMINDER, Constants.HTNG_LRV_AT_ROOM_TYPE);
        disableUploadType(ExternalSystem.SITEMINDER, Constants.HTNG_LRV_AT_ROOM_CLASS);

        continuousPricingMigrationService.migrateToTaxInclusive();

        verifyThatTaxParameterUpdateIsSkippedFor(Constants.OPERA, ExternalSystem.SITEMINDER);
    }

    @Test
    public void shouldFailStepWhenMigrateToTaxInclusiveForExternalSystemAsNGIAndTaxAdjustmentValuesAreDifferent() {
        setUpTaxParametersFor(Constants.NGI, false, null, Constants.NONE);

        enableUploadTypeAndTaxAdjustmentFor(ExternalSystem.SITEMINDER, Constants.HTNG_DAILYBAR, new BigDecimal(10.0));
        enableUploadTypeAndTaxAdjustmentFor(ExternalSystem.SITEMINDER, Constants.HTNG_LRV_AT_ROOM_CLASS, new BigDecimal(0.0));

        disableUploadType(ExternalSystem.SITEMINDER, Constants.HTNG_LRV_AT_ROOM_TYPE);
        try {
            continuousPricingMigrationService.migrateToTaxInclusive();
        } catch (TetrisException e) {
            assertTrue(e.getMessage().contains("The Decision Type [.DailyBAR, .LRVatRoomClass, .LRVatRoomType] tax adjustment values are different for HTNG vendor -  SITEMINDER"));
            assertEquals(ErrorCode.VENDOR_CONFIG_ERROR, e.getErrorCode());
        }

        verifyThatTaxParameterUpdateIsSkippedFor(Constants.NGI, ExternalSystem.SITEMINDER);
    }


    @Test
    public void shouldFailStepWhenMigrateToTaxInclusiveForExternalSystemOperaAndThereIsDifferenceBetweenOperaTaxAdjustmentAndHTNGTaxAdjustment() {
        setUpTaxParametersFor(Constants.OPERA, true, new BigDecimal(10), Constants.NONE);

        enableUploadTypeAndTaxAdjustmentFor(ExternalSystem.SITEMINDER, Constants.HTNG_DAILYBAR, new BigDecimal(11.0));
        enableUploadTypeAndTaxAdjustmentFor(ExternalSystem.SITEMINDER, Constants.HTNG_LRV_AT_ROOM_CLASS, new BigDecimal(11.0));

        disableUploadType(ExternalSystem.SITEMINDER, Constants.HTNG_LRV_AT_ROOM_TYPE);

        try {
            continuousPricingMigrationService.migrateToTaxInclusive();
        } catch (TetrisException e) {
            assertEquals(ErrorCode.VENDOR_CONFIG_ERROR, e.getErrorCode());
        }

        verifyThatTaxParameterUpdateIsSkippedFor(Constants.OPERA, ExternalSystem.SITEMINDER);
    }

    private void verifyThatTaxParameterUpdateIsSkippedFor(String externalSystem, ExternalSystem htngExternalSystem) {
        verifyRoomTaxIsNotSet();
        verifyApplyTaxIsNotDisabledFor(externalSystem);
        verifyDecisionTypeTaxAdjustmentIsNotSetAsZeroFor(Constants.HTNG_DAILYBAR);
        verifyDecisionTypeTaxAdjustmentIsNotSetAsZeroFor(Constants.HTNG_LRV_AT_ROOM_CLASS);

        verifySetUpForTaxParameters(externalSystem, 1);
        verifyDecisionTypeUploadTypesAreInvokedFor(htngExternalSystem, 1);
    }

    @Test
    public void migrateToTaxInclusiveForExternalSystemAsOtherThanNGIAndApplyTaxIsSetAsFalse() {
        setUpTaxParametersFor(Constants.REZVIEW, false, null, Constants.NONE);

        enableUploadTypeAndTaxAdjustmentFor(ExternalSystem.SITEMINDER, Constants.HTNG_DAILYBAR, new BigDecimal(10.0));
        enableUploadTypeAndTaxAdjustmentFor(ExternalSystem.SITEMINDER, Constants.HTNG_LRV_AT_ROOM_CLASS, new BigDecimal(10.0));

        disableUploadType(ExternalSystem.SITEMINDER, Constants.HTNG_LRV_AT_ROOM_TYPE);
        continuousPricingMigrationService.migrateToTaxInclusive();

        verifyRoomTaxIsNotSet();
        verifyApplyTaxIsNotDisabledFor(Constants.REZVIEW);
        verifyDecisionTypeTaxAdjustmentIsNotSetAsZeroFor(Constants.HTNG_DAILYBAR);
        verifyDecisionTypeTaxAdjustmentIsNotSetAsZeroFor(Constants.HTNG_LRV_AT_ROOM_CLASS);

        verifySetUpForTaxParameters(Constants.REZVIEW, 1);
    }

    @Test
    public void shouldFailStepWhenMigrateToTaxInclusiveForExternalSystemAsNGIAndTaxAdjustmentValuesAreDifferentWithOneAsNull() {
        setUpTaxParametersFor(Constants.NGI, false, null, Constants.NONE);

        enableUploadTypeAndTaxAdjustmentFor(ExternalSystem.SITEMINDER, Constants.HTNG_DAILYBAR, new BigDecimal(10.0));
        enableUploadTypeAndTaxAdjustmentFor(ExternalSystem.SITEMINDER, Constants.HTNG_LRV_AT_ROOM_CLASS, null);

        disableUploadType(ExternalSystem.SITEMINDER, Constants.HTNG_LRV_AT_ROOM_TYPE);
        try {
            continuousPricingMigrationService.migrateToTaxInclusive();
        } catch (TetrisException e) {
            assertTrue(e.getMessage().contains("The Decision Type [.DailyBAR, .LRVatRoomClass, .LRVatRoomType] tax adjustment values are different for HTNG vendor -  SITEMINDER"));
            assertEquals(ErrorCode.VENDOR_CONFIG_ERROR, e.getErrorCode());
        }

        verifyThatTaxParameterUpdateIsSkippedFor(Constants.NGI, ExternalSystem.SITEMINDER);
    }

    @Test
    public void shouldFailStepWhenMigrateToTaxInclusiveForExternalSystemAsNGIAndTaxAdjustmentValuesAreDifferentBetweenOutbounds() {
        setUpTaxParametersFor(Constants.NGI, false, null, Constants.NONE);

        enableUploadTypeAndTaxAdjustmentFor(ExternalSystem.SITEMINDER, Constants.HTNG_DAILYBAR, new BigDecimal(10.0));
        enableUploadTypeAndTaxAdjustmentFor(ExternalSystem.SITEMINDER, Constants.HTNG_LRV_AT_ROOM_CLASS, new BigDecimal(10.0));
        enableUploadTypeAndTaxAdjustmentFor(ExternalSystem.HBSI, Constants.HTNG_DAILYBAR, new BigDecimal(15.0));
        enableUploadTypeAndTaxAdjustmentFor(ExternalSystem.HBSI, Constants.HTNG_LRV_AT_ROOM_CLASS, new BigDecimal(15.0));

        disableUploadType(ExternalSystem.SITEMINDER, Constants.HTNG_LRV_AT_ROOM_TYPE);
        disableUploadType(ExternalSystem.HBSI, Constants.HTNG_LRV_AT_ROOM_TYPE);

        try {
            continuousPricingMigrationService.migrateToTaxInclusive();
        } catch (TetrisException e) {
            assertTrue(e.getMessage().contains("The Decision Type [.DailyBAR, .LRVatRoomClass, .LRVatRoomType] tax adjustment values are different across following HTNG vendor"));
            assertEquals(ErrorCode.VENDOR_CONFIG_ERROR, e.getErrorCode());
        }

        verifyThatTaxParameterUpdateIsSkippedFor(Constants.NGI, ExternalSystem.SITEMINDER);
    }


    @Test
    public void migrateRoomTypeOffsetsToSupplementsWhenSupplementsExist() {
        List<AccomTypeSupplement> accomTypeSupplements = asList(new AccomTypeSupplement());
        when(tenantCrudService.findAll(AccomTypeSupplement.class)).thenReturn(accomTypeSupplements);

        continuousPricingMigrationService.migrateRoomTypeOffsetsToSupplements();

        verify(tenantCrudService, times(0)).findByNamedQuerySingleResult(Product.GET_BY_CODE, with(CODE, BAR).parameters());
    }

    @Test
    public void migrateRoomTypeOffsetsToSupplementsWhenNoDailybarConfig() {
        when(tenantCrudService.findAll(AccomTypeSupplement.class)).thenReturn(null);

        Product product = new Product();
        when(tenantCrudService.findByNamedQuerySingleResult(Product.GET_BY_CODE, with(CODE, BAR).parameters())).thenReturn(product);
        when(tenantCrudService.findByNamedQuery(DailyBarConfig.FIND_DEFAULTS_BY_PRODUCT_ID, with(PRODUCT_ID, product.getId()).parameters())).thenReturn(null);

        continuousPricingMigrationService.migrateRoomTypeOffsetsToSupplements();

        verify(configParamsService, times(0)).updateParameterValue(GUIConfigParamName.IS_SUPPLEMENTAL_ENABLED.value(), true);
    }

    @Test
    public void migrateRoomTypeOffsetsToSupplements() {
        when(tenantCrudService.findAll(AccomTypeSupplement.class)).thenReturn(null);

        Product product = new Product();
        when(tenantCrudService.findByNamedQuerySingleResult(Product.GET_BY_CODE, with(CODE, BAR).parameters())).thenReturn(product);

        when(dateService.getCaughtUpDate()).thenReturn(caughtUpDate.toDate());

        DailyBarConfig dailyBarConfig = new DailyBarConfig();
        dailyBarConfig.setAccomTypeId(1);
        dailyBarConfig.setStartDate(caughtUpDate.minusDays(10));
        dailyBarConfig.setEndDate(caughtUpDate.plusDays(10));
        dailyBarConfig.getSingleRateChart().setOffsetType(FIXED);
        dailyBarConfig.getSingleRateChart().setSundayRate(BigDecimal.ZERO);
        dailyBarConfig.getDoubleRateChart().setOffsetType(FIXED);
        dailyBarConfig.getDoubleRateChart().setSundayRate(BigDecimal.ONE);
        dailyBarConfig.getExtraAdultRateChart().setOffsetType(FIXED);
        dailyBarConfig.getExtraAdultRateChart().setSundayRate(BigDecimal.TEN);
        dailyBarConfig.getExtraChildRateChart().setOffsetType(FIXED);
        dailyBarConfig.getExtraChildRateChart().setSundayRate(BigDecimal.TEN);

        DailyBarConfig dailyBarConfig2 = new DailyBarConfig();
        dailyBarConfig2.setAccomTypeId(2);
        dailyBarConfig2.setStartDate(caughtUpDate.minusDays(100));
        dailyBarConfig2.setEndDate(caughtUpDate.minusDays(11));
        dailyBarConfig2.getSingleRateChart().setOffsetType(FIXED);
        dailyBarConfig2.getSingleRateChart().setSundayRate(BigDecimal.ZERO);
        dailyBarConfig2.getDoubleRateChart().setOffsetType(FIXED);
        dailyBarConfig2.getDoubleRateChart().setSundayRate(BigDecimal.ONE);
        dailyBarConfig2.getExtraAdultRateChart().setOffsetType(FIXED);
        dailyBarConfig2.getExtraAdultRateChart().setSundayRate(BigDecimal.TEN);
        dailyBarConfig2.getExtraChildRateChart().setOffsetType(FIXED);
        dailyBarConfig2.getExtraChildRateChart().setSundayRate(BigDecimal.TEN);

        DailyBarConfig dailyBarConfig3 = new DailyBarConfig();
        dailyBarConfig3.setAccomTypeId(3);
        dailyBarConfig3.setStartDate(caughtUpDate.plusDays(11));
        dailyBarConfig3.setEndDate(caughtUpDate.plusDays(100));
        dailyBarConfig3.getSingleRateChart().setOffsetType(FIXED);
        dailyBarConfig3.getSingleRateChart().setSundayRate(BigDecimal.ZERO);
        dailyBarConfig3.getDoubleRateChart().setOffsetType(FIXED);
        dailyBarConfig3.getDoubleRateChart().setSundayRate(BigDecimal.ONE);
        dailyBarConfig3.getExtraAdultRateChart().setOffsetType(FIXED);
        dailyBarConfig3.getExtraAdultRateChart().setSundayRate(BigDecimal.TEN);
        dailyBarConfig3.getExtraChildRateChart().setOffsetType(FIXED);
        dailyBarConfig3.getExtraChildRateChart().setSundayRate(BigDecimal.TEN);

        List<DailyBarConfig> dailyBarConfigs = asList(dailyBarConfig, dailyBarConfig2, dailyBarConfig3);
        when(tenantCrudService.<DailyBarConfig>findByNamedQuery(DailyBarConfig.FIND_DEFAULTS_BY_PRODUCT_ID, with(PRODUCT_ID, product.getId()).parameters())).thenReturn(dailyBarConfigs);

        CPConfiguration cpConfiguration = new CPConfiguration();
        when(tenantCrudService.findOne(CPConfiguration.class)).thenReturn(cpConfiguration);

        Tax tax = new Tax();
        tax.setRoomTaxRate(BigDecimal.TEN);
        when(taxService.findTax()).thenReturn(tax);

        AccomType accomType = new AccomType();
        accomType.setId(1);
        when(tenantCrudService.findAll(AccomType.class)).thenReturn(Arrays.asList(accomType));
        when(tenantCrudService.save(saveArgumentCaptor.capture())).thenReturn(null);

        continuousPricingMigrationService.migrateRoomTypeOffsetsToSupplements();

        List<AccomTypeSupplement> results = saveArgumentCaptor.getAllValues().get(0).stream().collect(Collectors.toList());
        assertEquals(4, results.size());
        assertEquals(accomType, results.get(0).getAccomType());
        assertEquals(OccupancyType.SINGLE, results.get(0).getOccupancyType());
        assertEquals(applyTaxAndRound(tax, BigDecimal.ZERO), results.get(0).getSundaySupplementValue());
        assertEquals(accomType, results.get(1).getAccomType());
        assertEquals(OccupancyType.DOUBLE, results.get(1).getOccupancyType());
        assertEquals(applyTaxAndRound(tax, BigDecimal.ONE), results.get(1).getSundaySupplementValue());
        assertEquals(accomType, results.get(2).getAccomType());
        assertEquals(OccupancyType.EXTRA_ADULT, results.get(2).getOccupancyType());
        assertEquals(applyTaxAndRound(tax, BigDecimal.TEN), results.get(2).getSundaySupplementValue());
        assertEquals(accomType, results.get(3).getAccomType());
        assertEquals(OccupancyType.EXTRA_CHILD, results.get(3).getOccupancyType());
        assertEquals(applyTaxAndRound(tax, BigDecimal.TEN), results.get(3).getSundaySupplementValue());

        assertTrue(cpConfiguration.isEnableSupplements());
        verify(configParamsService).updateParameterValue(GUIConfigParamName.IS_SUPPLEMENTAL_ENABLED.value(), true);
        verify(tenantCrudService).save(anySetOf(AccomTypeSupplement.class));
        verify(tenantCrudService).deleteAll(DailyBarConfig.class);
        verify(tenantCrudService).flush();
        verify(tenantCrudService).deleteAll(DailyBarRateChart.class);
    }

    private BigDecimal applyTaxAndRound(Tax tax, BigDecimal value) {
        return BigDecimalUtil.round(tax.applyRoomTaxRate(value), 2);
    }

    @Test
    public void configureBaseRoomTypes() {
        when(tenantCrudService.findAll(PricingAccomClass.class)).thenReturn(null);

        AccomClass stdClass = new AccomClass();
        stdClass.setId(1);

        AccomType doubleRoom = new AccomType();
        doubleRoom.setAccomClass(stdClass);
        doubleRoom.setAccomTypeCapacity(50);

        AccomType kingRoom = new AccomType();
        kingRoom.setAccomClass(stdClass);
        kingRoom.setAccomTypeCapacity(50);

        AccomClass deluxeClass = new AccomClass();
        deluxeClass.setId(2);

        AccomType suite = new AccomType();
        suite.setAccomClass(deluxeClass);

        List<AccomClass> accomClasses = asList(stdClass, deluxeClass);
        List<AccomType> accomTypes = asList(doubleRoom, kingRoom, suite);

        PricingAccomClass firstExpectedPricingAccomClass = new PricingAccomClass();
        firstExpectedPricingAccomClass.setPropertyId(PacmanWorkContextHelper.getPropertyId());
        firstExpectedPricingAccomClass.setAccomClass(stdClass);
        firstExpectedPricingAccomClass.setAccomType(doubleRoom);

        PricingAccomClass secondExpectedPricingAccomClass = new PricingAccomClass();
        secondExpectedPricingAccomClass.setPropertyId(PacmanWorkContextHelper.getPropertyId());
        secondExpectedPricingAccomClass.setAccomClass(deluxeClass);
        secondExpectedPricingAccomClass.setAccomType(suite);

        when(tenantCrudService.findByNamedQuerySingleResult(CPRecommendedFloorCeilingEntity.GET_COUNT)).thenReturn(0L);
        when(tenantCrudService.<AccomClass>findByNamedQuery(AccomClass.ALL_ACTIVE_NON_DEFAULT, with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).parameters())).thenReturn(accomClasses);
        when(tenantCrudService.<AccomType>findByNamedQuery(AccomType.ALL_ACTIVE, with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).parameters())).thenReturn(accomTypes);
        when(tenantCrudService.save(pricingAccomClassArgumentCaptor.capture())).thenReturn(null);

        continuousPricingMigrationService.configureBaseRTByMaxCapacity();

        List<PricingAccomClass> pricingAccomClasses = pricingAccomClassArgumentCaptor.getAllValues();
        assertEquals(stdClass, pricingAccomClasses.get(0).getAccomClass());
        assertEquals(doubleRoom, pricingAccomClasses.get(0).getAccomType());
        assertEquals(deluxeClass, pricingAccomClasses.get(1).getAccomClass());
        assertEquals(suite, pricingAccomClasses.get(1).getAccomType());
    }

    @Test
    public void testActivateSystemDefaultRateUnqualified() {
        continuousPricingMigrationService.activateSystemDefaultRateUnqualified();
        verify(tenantCrudService).executeUpdateByNamedQuery(RateUnqualified.ACTIVATE_SYSTEM_DEFAULT);
    }

    private PacmanSubscriberDto getPacmanSubscriberDto(int id, String code, String client) {
        PacmanSubscriberDto pacmanSubscriberDto = new PacmanSubscriberDto();
        pacmanSubscriberDto.setCode(code);
        pacmanSubscriberDto.setClient(client);
        pacmanSubscriberDto.setId(id);
        return pacmanSubscriberDto;
    }

    private void verifyDecisionTypeTaxAdjustmentIsInvoked(ExternalSystem externalSystem, String decisionType) {
        verify(configParamsService, times(1)).getBigDecimalParameterValue(Constants.PACMAN_INTEGRATION + externalSystem.getExternalSystemName().toLowerCase() + decisionType + Constants.TAX_ADJUSTMENT_VALUE);
    }

    private void verifyDecisionTypeUploadTypesAreInvokedFor(ExternalSystem externalSystem, int wantedNumberOfInvocations) {
        verify(configParamsService, times(wantedNumberOfInvocations)).getParameterValue(Constants.PACMAN_INTEGRATION + externalSystem.getExternalSystemName().toLowerCase() + Constants.HTNG_DAILYBAR + Constants.UPLOAD_TYPE);
        verify(configParamsService, times(wantedNumberOfInvocations)).getParameterValue(Constants.PACMAN_INTEGRATION + externalSystem.getExternalSystemName().toLowerCase() + Constants.HTNG_LRV_AT_ROOM_CLASS + Constants.UPLOAD_TYPE);
        verify(configParamsService, times(wantedNumberOfInvocations)).getParameterValue(Constants.PACMAN_INTEGRATION + externalSystem.getExternalSystemName().toLowerCase() + Constants.HTNG_LRV_AT_ROOM_TYPE + Constants.UPLOAD_TYPE);
    }

    private void verifyApplyTaxIsDisabledFor(String externalSystem) {
        verify(configParamsService).updateParameterValue(Constants.PACMAN_INTEGRATION + externalSystem.toLowerCase() + Constants.APPLY_TAX, false);
    }

    private void verifyApplyTaxIsNotDisabledFor(String externalSystem) {
        verify(configParamsService, never()).updateParameterValue(Constants.PACMAN_INTEGRATION + externalSystem.toLowerCase() + Constants.APPLY_TAX, false);
    }

    private void verifySetUpForTaxParameters(String externalSystem, int wantedNumberOfInvocations) {
        verify(configParamsService).getParameterValue(IntegrationConfigParamName.CORE_PROPERTY_EXTERNAL_SYSTEM.value());
        verify(configParamsService).getBooleanParameterValue(Constants.PACMAN_INTEGRATION + externalSystem.toLowerCase() + Constants.APPLY_TAX);
        verify(configParamsService, times(wantedNumberOfInvocations)).getBigDecimalParameterValue(Constants.PACMAN_INTEGRATION + externalSystem.toLowerCase() + Constants.TAX_ADJUSTMENT_VALUE);
    }

    private void verifyDecisionTypeTaxAdjustmentIsSetAsZeroFor(ExternalSystem externalSystem, String decisionType) {
        verify(configParamsService).updateParameterValue(Constants.PACMAN_INTEGRATION + externalSystem.getExternalSystemName().toLowerCase() + decisionType + Constants.TAX_ADJUSTMENT_VALUE, BigDecimalUtil.round(BigDecimal.ZERO, 0));
    }

    private void verifyDecisionTypeTaxAdjustmentIsNotSetAsZeroFor(String decisionType) {
        verify(configParamsService, never()).updateParameterValue(Constants.PACMAN_INTEGRATION + ExternalSystem.SITEMINDER.getExternalSystemName().toLowerCase() + decisionType + Constants.TAX_ADJUSTMENT_VALUE, BigDecimalUtil.round(BigDecimal.ZERO, 0));
    }

    private void verifyRoomTaxIsSetTo(BigDecimal roomTaxRate) {
        verify(taxService).updateRoomTaxRate(roomTaxRate);
    }

    private void verifyRoomTaxIsNotSet() {
        verify(taxService, never()).updateRoomTaxRate(any(BigDecimal.class));
    }

    private void setUpTaxParametersFor(String externalSystem, boolean isTaxApplicable, BigDecimal taxAdjustmentValue, String subsystem) {
        when(configParamsService.getParameterValue(IntegrationConfigParamName.CORE_PROPERTY_EXTERNAL_SYSTEM.value())).thenReturn(externalSystem.toLowerCase());
        when(configParamsService.getParameterValue(IntegrationConfigParamName.CORE_PROPERTY_EXTERNAL_SYSTEM_SUB_SYSTEM.value())).thenReturn(subsystem);
        when(externalSystemHelperMock.isOXI()).thenReturn(Constants.OXI.equalsIgnoreCase(subsystem));
        setUpTaxParametersFor(externalSystem, isTaxApplicable, taxAdjustmentValue);
    }

    private void setUpTaxParametersFor(String externalSystem, boolean isTaxApplicable, BigDecimal taxAdjustmentValue) {
        String parameterNameApplyTax = Constants.PACMAN_INTEGRATION + externalSystem.toLowerCase() + Constants.APPLY_TAX;
        String parameterNameTax = Constants.PACMAN_INTEGRATION + externalSystem.toLowerCase() + Constants.TAX_ADJUSTMENT_VALUE;
        when(configParamsService.getBooleanParameterValue(parameterNameApplyTax)).thenReturn(isTaxApplicable);
        when(configParamsService.getBigDecimalParameterValue(parameterNameTax)).thenReturn(taxAdjustmentValue);
        when(configParameterNameService.getIntegrationParameterName(externalSystem.toLowerCase(), Constants.APPLY_TAX)).thenReturn(parameterNameApplyTax);
        when(configParameterNameService.getIntegrationParameterName(externalSystem.toLowerCase(), Constants.TAX_ADJUSTMENT_VALUE)).thenReturn(parameterNameTax);
    }

    private void disableUploadType(ExternalSystem externalSystem, String decisionType) {
        String parameterNameUploadType = Constants.PACMAN_INTEGRATION + externalSystem.getExternalSystemName().toLowerCase() + decisionType + Constants.UPLOAD_TYPE;
        when(configParamsService.getParameterValue(parameterNameUploadType)).thenReturn(Constants.NONE);
        when(configParameterNameService.getIntegrationUploadTypeParameterName(externalSystem.getExternalSystemName().toLowerCase(), decisionType, Constants.UPLOAD_TYPE)).thenReturn(parameterNameUploadType);
    }

    private void enableUploadTypeAndTaxAdjustmentFor(ExternalSystem externalSystem, String decisionType, BigDecimal taxAdjustmentValue) {
        String parameterNameUploadType = Constants.PACMAN_INTEGRATION + externalSystem.getExternalSystemName().toLowerCase() + decisionType + Constants.UPLOAD_TYPE;
        String parameterNameTaxAdjustment = Constants.PACMAN_INTEGRATION + externalSystem.getExternalSystemName().toLowerCase() + decisionType + Constants.TAX_ADJUSTMENT_VALUE;
        when(configParamsService.getParameterValue(parameterNameUploadType)).thenReturn(Constants.FULL);
        when(configParamsService.getBigDecimalParameterValue(parameterNameTaxAdjustment)).thenReturn(taxAdjustmentValue);
        when(configParameterNameService.getIntegrationUploadTypeParameterName(externalSystem.getExternalSystemName().toLowerCase(), decisionType, Constants.UPLOAD_TYPE)).thenReturn(parameterNameUploadType);
        when(configParameterNameService.getIntegrationUploadTypeParameterName(externalSystem.getExternalSystemName().toLowerCase(), decisionType, Constants.TAX_ADJUSTMENT_VALUE)).thenReturn(parameterNameTaxAdjustment);
        when(configParameterNameService.getIntegrationParameterName(externalSystem.getExternalSystemName().toLowerCase(), decisionType + Constants.UPLOAD_TYPE)).thenReturn(parameterNameUploadType);
        when(configParameterNameService.getIntegrationParameterName(externalSystem.getExternalSystemName().toLowerCase(), decisionType + Constants.TAX_ADJUSTMENT_VALUE)).thenReturn(parameterNameTaxAdjustment);
    }

    @Test
    public void migrateRatePlansToDefaultTransientFloorAndCeilingRates() {
        when(tenantCrudService.findByNamedQuery(TransientPricingBaseAccomType.FIND_DEFAULT_BY_PROPERTY_ID, with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).parameters())).thenReturn(null);

        continuousPricingMigrationService.migrateRatePlansToDefaultTransientFloorAndCeilingRates();

        verify(tenantCrudService).executeUpdateByNativeQuery(MIGRATE_RATE_PLANS_TO_DEFAULT_TRANSIENT_FLOOR_AND_CEILING_RATES);
    }

    @Test
    public void deleteFutureArrivalDemandForecasts() {
        when(dateService.getCaughtUpDate()).thenReturn(caughtUpDate.toDate());

        Map<String, Object> arrivalDateParameter = with(ARRIVAL_DATE, caughtUpDate.toDate()).parameters();
        when(tenantCrudService.executeUpdateByNamedQuery(ArrivalDemandOverride.DELETE_FUTURE_ARRIVAL_DATES, arrivalDateParameter)).thenReturn(1);

        continuousPricingMigrationService.deleteFutureArrivalDemandForecasts();

        verify(tenantCrudService).executeUpdateByNamedQuery(ArrivalDemandOverride.DELETE_FUTURE_ARRIVAL_DATES, arrivalDateParameter);
    }

    @Test
    public void deleteFutureOccupancyDemandForecasts() {
        when(dateService.getCaughtUpDate()).thenReturn(caughtUpDate.toDate());

        Map<String, Object> occupancyDateParameter = with(OCCUPANCY_DATE, caughtUpDate.toDate()).parameters();
        when(tenantCrudService.executeUpdateByNamedQuery(OccupancyDemandOverride.DELETE_FUTURE_OCCUPANCY_DATES, occupancyDateParameter)).thenReturn(1);
        when(tenantCrudService.executeUpdateByNativeQuery("DELETE FROM [dbo].[FS_Fcst_Eval_Override] WHERE Occupancy_DT >= :occupancyDate", occupancyDateParameter)).thenReturn(1);

        continuousPricingMigrationService.deleteFutureOccupancyDemandForecasts();

        verify(tenantCrudService).executeUpdateByNamedQuery(OccupancyDemandOverride.DELETE_FUTURE_OCCUPANCY_DATES, occupancyDateParameter);
        verify(tenantCrudService).executeUpdateByNativeQuery("DELETE FROM [dbo].[FS_Fcst_Eval_Override] WHERE Occupancy_DT >= :occupancyDate", occupancyDateParameter);
    }

    @Test
    public void deleteFutureDecisions() {
        when(dateService.getCaughtUpDate()).thenReturn(caughtUpDate.toDate());

        Map<String, Object> arrivalDateParameter = with(ARRIVAL_DATE, caughtUpDate.toDate()).parameters();
        when(tenantCrudService.executeUpdateByNamedQuery(CloseHighestBarOverride.DELETE_FUTURE_ARRIVAL_DATES, arrivalDateParameter)).thenReturn(1);
        when(tenantCrudService.executeUpdateByNamedQuery(CloseHighestBar.DELETE_FUTURE_ARRIVAL_DATES, arrivalDateParameter)).thenReturn(1);
        when(tenantCrudService.executeUpdateByNativeQuery("DELETE FROM [dbo].[Unqualified_Demand_FCST_Price] WHERE Arrival_DT >= :arrivalDate", arrivalDateParameter)).thenReturn(1);
        when(tenantCrudService.executeUpdateByNativeQuery("DELETE FROM [dbo].[Decision_FPLOS_By_Hierarchy] WHERE Arrival_DT >= :arrivalDate", arrivalDateParameter)).thenReturn(1);
        when(tenantCrudService.executeUpdateByNativeQuery("DELETE FROM [dbo].[Decision_FPLOS_By_Rank] WHERE Arrival_DT >= :arrivalDate", arrivalDateParameter)).thenReturn(1);
        when(tenantCrudService.executeUpdateByNativeQuery("DELETE FROM [dbo].[Decision_FPLOS_By_RoomType] WHERE Arrival_DT >= :arrivalDate", arrivalDateParameter)).thenReturn(1);
        when(tenantCrudService.executeUpdateByNamedQuery(DecisionBAROutputOverrideDetails.DELETE_FUTURE_ARRIVAL_DATES, arrivalDateParameter)).thenReturn(1);
        when(tenantCrudService.executeUpdateByNamedQuery(DecisionBAROutputOverride.DELETE_FUTURE_ARRIVAL_DATES, arrivalDateParameter)).thenReturn(1);
        when(tenantCrudService.executeUpdateByNamedQuery(DecisionBAROutput.DELETE_FUTURE_ARRIVAL_DATES, arrivalDateParameter)).thenReturn(1);

        continuousPricingMigrationService.deleteFutureDecisions();

        verify(tenantCrudService).executeUpdateByNamedQuery(CloseHighestBarOverride.DELETE_FUTURE_ARRIVAL_DATES, arrivalDateParameter);
        verify(tenantCrudService).executeUpdateByNamedQuery(CloseHighestBar.DELETE_FUTURE_ARRIVAL_DATES, arrivalDateParameter);
        verify(tenantCrudService).executeUpdateByNativeQuery("DELETE FROM [dbo].[Unqualified_Demand_FCST_Price] WHERE Arrival_DT >= :arrivalDate", arrivalDateParameter);
        verify(tenantCrudService).executeUpdateByNativeQuery("DELETE FROM [dbo].[Decision_FPLOS_By_Hierarchy] WHERE Arrival_DT >= :arrivalDate", arrivalDateParameter);
        verify(tenantCrudService).executeUpdateByNativeQuery("DELETE FROM [dbo].[Decision_FPLOS_By_Rank] WHERE Arrival_DT >= :arrivalDate", arrivalDateParameter);
        verify(tenantCrudService).executeUpdateByNativeQuery("DELETE FROM [dbo].[Decision_FPLOS_By_RoomType] WHERE Arrival_DT >= :arrivalDate", arrivalDateParameter);
        verify(tenantCrudService).executeUpdateByNamedQuery(DecisionBAROutputOverrideDetails.DELETE_FUTURE_ARRIVAL_DATES, arrivalDateParameter);
        verify(tenantCrudService).executeUpdateByNamedQuery(DecisionBAROutputOverride.DELETE_FUTURE_ARRIVAL_DATES, arrivalDateParameter);
        verify(tenantCrudService).executeUpdateByNamedQuery(DecisionBAROutput.DELETE_FUTURE_ARRIVAL_DATES, arrivalDateParameter);
    }

    @Test
    public void truncateFutureDecisionPace() {

        when(tenantCrudService.executeUpdateByNativeQuery("TRUNCATE TABLE [dbo].[Pace_Decision_LRA_FPLOS]")).thenReturn(1);
        when(tenantCrudService.executeUpdateByNativeQuery("TRUNCATE TABLE [dbo].[Pace_Decision_LRA_minLOS]")).thenReturn(1);
        when(tenantCrudService.executeUpdateByNativeQuery("TRUNCATE TABLE [dbo].[PACE_FPLOS_By_Rank]")).thenReturn(1);
        when(tenantCrudService.executeUpdateByNativeQuery("TRUNCATE TABLE [dbo].[PACE_FPLOS_By_Hierarchy]")).thenReturn(1);
        when(tenantCrudService.executeUpdateByNativeQuery("TRUNCATE TABLE [dbo].[PACE_FPLOS_By_RoomType]")).thenReturn(1);
        when(tenantCrudService.executeUpdateByNativeQuery("TRUNCATE TABLE [dbo].[PACE_Bar_Output_NOTIFICATION]")).thenReturn(1);
        when(tenantCrudService.executeUpdateByNativeQuery("TRUNCATE TABLE [dbo].[PACE_Bar_Output_Upload]")).thenReturn(1);

        continuousPricingMigrationService.truncateFutureDecisionPace();

        verify(tenantCrudService).executeUpdateByNativeQuery("TRUNCATE TABLE [dbo].[Pace_Decision_LRA_FPLOS]");
        verify(tenantCrudService).executeUpdateByNativeQuery("TRUNCATE TABLE [dbo].[Pace_Decision_LRA_minLOS]");
        verify(tenantCrudService).executeUpdateByNativeQuery("TRUNCATE TABLE [dbo].[PACE_FPLOS_By_Rank]");
        verify(tenantCrudService).executeUpdateByNativeQuery("TRUNCATE TABLE [dbo].[PACE_FPLOS_By_Hierarchy]");
        verify(tenantCrudService).executeUpdateByNativeQuery("TRUNCATE TABLE [dbo].[PACE_FPLOS_By_RoomType]");
        verify(tenantCrudService).executeUpdateByNativeQuery("TRUNCATE TABLE [dbo].[PACE_Bar_Output_NOTIFICATION]");
        verify(tenantCrudService).executeUpdateByNativeQuery("TRUNCATE TABLE [dbo].[PACE_Bar_Output_Upload]");
    }

    @Test
    public void inactiveRateUnqualifieds() {
        when(tenantCrudService.deleteAll(RateUnqualifiedUserOverride.class)).thenReturn(1);
        when(tenantCrudService.deleteAll(RateUnqualifiedLOSOverride.class)).thenReturn(1);
        when(tenantCrudService.deleteAll(RateUnqualifiedClosed.class)).thenReturn(1);
        when(tenantCrudService.executeUpdateByNamedQuery(RateUnqualified.INACTIVE_ALL_NON_DEFAULTS)).thenReturn(1);

        continuousPricingMigrationService.inactiveRateUnqualifieds();

        verify(tenantCrudService).deleteAll(RateUnqualifiedUserOverride.class);
        verify(tenantCrudService).deleteAll(RateUnqualifiedLOSOverride.class);
        verify(tenantCrudService).deleteAll(RateUnqualifiedClosed.class);
        verify(tenantCrudService).executeUpdateByNamedQuery(RateUnqualified.INACTIVE_ALL_NON_DEFAULTS);
    }

    @Test
    public void inactivePricingInformationManagerAlertsAndNotifications() {
        continuousPricingMigrationService.inactivePricingInformationManagerAlertsAndNotifications();

        verify(tenantCrudService).executeUpdateByNamedQuery(InformationMgrAlertConfigEntity.INACTIVATE_BY_TYPE_IN, with(TYPES, PRICING_INFO_MGR_TYPES).parameters());
        List<String> alertTypes = new ArrayList<>(PRICING_INFO_MGR_TYPES);
        alertTypes.add("DecisionChangeEx");
        verify(tenantCrudService).executeUpdateByNamedQuery(InfoMgrInstanceEntity.INACTIVATE_BY_TYPE_IN, with(TYPES, alertTypes).parameters());
    }

    @Test
    public void enablePricingConfigurationSyncFlag() {
        continuousPricingMigrationService.enablePricingConfigurationSyncFlag();

        verify(syncEventAggregatorService).registerSyncEvent(SyncEvent.PRICING_CONFIG_CHANGED);
    }

    @Test
    public void disableContinuousPricingSASNewFeatureFlag() {
        String clientCode = PacmanWorkContextHelper.getClientCode();
        String propertyCode = PacmanWorkContextHelper.getPropertyCode();
        Integer propertyId = PacmanWorkContextHelper.getPropertyId();

        when(sasDbToolService.executeUpdate(clientCode, propertyId, propertyCode, DISABLE_CONTINUOUS_PRICING_SAS_NEW_FEATURE)).thenReturn(1);

        continuousPricingMigrationService.disableContinuousPricingSASNewFeatureFlag();

        verify(sasDbToolService).executeUpdate(clientCode, propertyId, propertyCode, DISABLE_CONTINUOUS_PRICING_SAS_NEW_FEATURE);
    }

    @Test
    public void enableContinuousPricing() {
        when(configParamsService.getBooleanParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED.value())).thenReturn(false);

        continuousPricingMigrationService.enableContinuousPricing();

        verify(configParamsService).updateParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED.value(), true);
    }

    @Test
    public void testEnableCentralRMSAvailable() {
        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.CENTRAL_RMS_AVAILABLE.value())).thenReturn(false);

        continuousPricingMigrationService.enableCentralRMSAvailable();

        verify(configParamsService).updateParameterValue(FeatureTogglesConfigParamName.CENTRAL_RMS_AVAILABLE.value(), true);
    }

    @Test
    public void enableContinuousPricingWhenAlreadyEnabled() {
        when(configParamsService.getBooleanParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED.value())).thenReturn(true);

        continuousPricingMigrationService.enableContinuousPricing();

        verify(configParamsService, times(0)).updateParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED.value(), true);
    }

    private AccomClass getAccomClass() {
        AccomClass accomClass = new AccomClass();
        accomClass.setId(1);
        return accomClass;
    }

    @Test
    public void deleteRoomClassWithNoRoomTypeIfNoSuchRoomClass() {
        when(tenantCrudService.findByNativeQuery("SELECT * FROM [dbo]. [Accom_class] as ac WHERE ac.Accom_Class_ID NOT IN(SELECT at.Accom_Class_ID FROM [dbo]. [Accom_Type] as at WHERE at.Status_ID = 1) AND ac.Accom_Class_Code != 'Unassigned'", new HashMap<>(), AccomClass.class)).thenReturn(null);

        continuousPricingMigrationService.deleteRoomClassWithNoRoomType();

        verify(tenantCrudService, never()).save(getAccomClass());
    }

    @Test
    public void deleteRoomClassWithNoRoomType() {
        AccomClass accomClass = getAccomClass();
        when(tenantCrudService.findByNativeQuery("SELECT * FROM [dbo]. [Accom_class] as ac WHERE ac.Accom_Class_ID NOT IN(SELECT at.Accom_Class_ID FROM [dbo]. [Accom_Type] as at WHERE at.Status_ID = 1) AND ac.Accom_Class_Code != 'Unassigned'", new HashMap<>(), AccomClass.class)).thenReturn(asList(accomClass));
        continuousPricingMigrationService.deleteRoomClassWithNoRoomType();
        verify(tenantCrudService).save(accomClass);
    }

    @Test
    public void updateBarDecisionToRateOfDay() {
        when(configParamsService.getParameterValue(IPConfigParamName.BAR_BAR_DECISION.value())).thenReturn(Constants.BAR_DECISION_VALUE_LOS);

        continuousPricingMigrationService.updateBarDecisionToRateOfDay();

        verify(configParamsService).updateParameterValue(IPConfigParamName.BAR_BAR_DECISION.value(), Constants.BAR_DECISION_VALUE_RATEOFDAY);
    }

    @Test
    public void updateBarDecisionToRateOfDayWhenParameterIsNull() {
        when(configParamsService.getParameterValue(IPConfigParamName.BAR_BAR_DECISION.value())).thenReturn(null);

        continuousPricingMigrationService.updateBarDecisionToRateOfDay();

        verify(configParamsService).updateParameterValue(IPConfigParamName.BAR_BAR_DECISION.value(), Constants.BAR_DECISION_VALUE_RATEOFDAY);
    }

    @Test
    public void updateBarDecisionToRateOfDayWhenAlreadyRateOfDay() {
        when(configParamsService.getParameterValue(IPConfigParamName.BAR_BAR_DECISION.value())).thenReturn(Constants.BAR_DECISION_VALUE_RATEOFDAY);

        continuousPricingMigrationService.updateBarDecisionToRateOfDay();

        verify(configParamsService, never()).updateParameterValue(IPConfigParamName.BAR_BAR_DECISION.value(), Constants.BAR_DECISION_VALUE_RATEOFDAY);
    }

    @Test
    public void disableSingleBarDecision() {
        when(configParamsService.getBooleanParameterValue(IPConfigParamName.BAR_ENABLE_SINGLE_BAR_DECISION.value())).thenReturn(true);

        continuousPricingMigrationService.disableSingleBarDecision();

        verify(configParamsService).updateParameterValue(IPConfigParamName.BAR_ENABLE_SINGLE_BAR_DECISION.value(), false);
    }

    @Test
    public void disableSingleBarDecisionExplicit() {
        continuousPricingMigrationService.disableSingleBarDecisionExplicit();
        verify(configParamsService).updateParameterValue(IPConfigParamName.BAR_ENABLE_SINGLE_BAR_DECISION.value(), false);
    }

    @Test
    public void disableSingleBarDecisionWhenAlreadyDisabled() {
        when(configParamsService.getBooleanParameterValue(IPConfigParamName.BAR_ENABLE_SINGLE_BAR_DECISION.value())).thenReturn(false);

        continuousPricingMigrationService.disableSingleBarDecision();

        verify(configParamsService, times(0)).updateParameterValue(IPConfigParamName.BAR_ENABLE_SINGLE_BAR_DECISION.value(), false);
    }

    @Test
    public void shouldConfigureFloorAndCeilingRatesTest() {
        BigDecimal groupRateAdjustment = BigDecimal.valueOf(25);
        when(externalSystemHelperMock.isRezview()).thenReturn(true);
        continuousPricingMigrationService.configureFloorAndCeilingRatesFromRecommendedFloorCeiling(groupRateAdjustment);
        verify(cpRecommendedFloorCeilingService).configureFloorAndCeilingRates(groupRateAdjustment, true);
    }

    @Test
    public void shouldConfigureTransientFloorAndCeilingRatesTest() {
        continuousPricingMigrationService.configureFloorAndCeilingRatesFromRecommendedFloorCeiling(null);
        verify(cpRecommendedFloorCeilingService).configureFloorAndCeilingRates(null, false);
    }


    @Test
    void enableAgileRates() {
        continuousPricingMigrationService.enableAgileRates();
        verify(configParamsService).updateParameterValue(FeatureTogglesConfigParamName.AGILE_RATES_ENABLED.value(), true);
    }

    @Test
    void enableConsiderRestrictionSeasonDatesForAgileQualifiedFPLOS() {
        continuousPricingMigrationService.enableConsiderRestrictionSeasonDatesForAgileQualifiedFPLOS();
        verify(configParamsService).updateParameterValue(FeatureTogglesConfigParamName.CONSIDER_RESTRICTION_SEASON_DATE_FOR_AGILE_QUALIFIED_FPLOS.value(), true);
    }
}