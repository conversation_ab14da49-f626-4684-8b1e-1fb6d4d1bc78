package com.ideas.tetris.pacman.services.extendedstay.ratemanagement.service;

import com.ideas.g3.data.TestProperty;
import com.ideas.g3.rule.CrudServiceBeanExtension;
import com.ideas.g3.test.AbstractG3JupiterTest;
import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.configparams.GUIConfigParamName;
import com.ideas.tetris.pacman.common.configparams.IPConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomType;
import com.ideas.tetris.pacman.services.bestavailablerate.BarDecisionService;
import com.ideas.tetris.pacman.services.bestavailablerate.DecisionOverrideType;
import com.ideas.tetris.pacman.services.bestavailablerate.DecisionReasonType;
import com.ideas.tetris.pacman.services.bestavailablerate.SQLHelper;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.CPDecisionBAROutput;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.Decision;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.UniqueDecisionBarOutputCreator;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.UniqueDecisionCreator;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.UniqueTotalActivityCreator;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.extendedstay.common.entity.ExtendedStayProductDefinition;
import com.ideas.tetris.pacman.services.extendedstay.ratemanagement.dto.ExtendedStayRateMappingData;
import com.ideas.tetris.pacman.services.extendedstay.ratemanagement.dto.ProductLevelDTO;
import com.ideas.tetris.pacman.services.extendedstay.unqualifiedrate.entity.ExtendedStayRateUnqualified;
import com.ideas.tetris.pacman.services.extendedstay.unqualifiedrate.entity.ExtendedStayRateUnqualifiedOverride;
import com.ideas.tetris.pacman.services.extendedstayrateshopping.entity.UniqueESProductDefinitionCreator;
import com.ideas.tetris.pacman.services.extendedstayrateshopping.entity.UniqueExtendedStayCompetitorMappingCreator;
import com.ideas.tetris.pacman.services.product.Product;
import com.ideas.tetris.pacman.services.specialevent.entity.PropertySpecialEventInstance;
import com.ideas.tetris.pacman.services.specialevent.entity.UniquePropertySpecialEventCreator;
import com.ideas.tetris.pacman.services.specialevent.entity.UniquePropertySpecialEventInstanceCreator;
import com.ideas.tetris.pacman.services.webrate.entity.UniqueWebRateCreator;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.entity.DayOfWeek;
import com.ideas.tetris.platform.common.time.LocalDateUtils;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.apache.log4j.Logger;
import org.joda.time.Days;
import org.joda.time.LocalDate;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.IntStream;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.when;

@MockitoSettings(strictness = Strictness.LENIENT)
public class ExtendedStayRateMappingServiceTest extends AbstractG3JupiterTest {

    private static final Logger LOGGER = Logger.getLogger(ExtendedStayRateMappingServiceTest.class);
    public static final String STE = "STE";
    public static final String DBL = "DBL";
    public static final String DLX = "DLX";
    public static final String Q = "Q";
    public static final String K = "K";
    private static final String UPLOAD_PENDING = "UPLOAD_PENDING";
    public static final String START_DATE = "2015-05-05";
    public static final String END_DATE = "2015-05-07";
    private static final int BAR_PRODUCT_ID = 1;
    private static final int BI_WEEKLY_PRODUCT_ID = 3;
    private CrudService crudService;
    ExtendedStayRateMappingService extendedStayRateMappingService;
    Integer PROPERTY_ID;
    private Map cacheAccomData;
    private static final String INSERT_INTO_ES_RATE_UNQUALIFIED_OVERRIDE = new StringBuilder().append("insert into ES_Rate_Unqualified_Override ")
            .append("  select :esRateUqId as  ES_Rate_Unqualified_ID ,:accomTypeID as Accom_Type_ID, cast(cal.calendar_date as date) as Occupancy_Date, ")
            .append("  :rateValue as Rate_Value, :overrideStatus as Override_status, GETDATE() as Created_DTTM,GETDATE() as Last_Updated_DTTM,1 as Created_By_User_ID, ")
            .append("  1 as Last_Updated_By_User_ID from calendar_dim as cal ").append(" where cast(cal.calendar_date as date) between :startDate and :endDate").toString();

    @InjectMocks
    private BarDecisionService barDecisionService;
    @InjectMocks
    private SQLHelper sqlHelper;

    @BeforeEach
    public void setUp() {
        crudService = tenantCrudService();
        cacheAccomData = new HashMap<String, Integer>();
        cacheAccomData.put(DLX, 4);
        cacheAccomData.put(STE, 5);
        cacheAccomData.put(DBL, 6);
        cacheAccomData.put(Q, 7);
        cacheAccomData.put(K, 8);
        extendedStayRateMappingService = new ExtendedStayRateMappingService();
        extendedStayRateMappingService.crudService = crudService;
        MockitoAnnotations.initMocks(this);
        setESProductDefinition();
        PROPERTY_ID = PacmanWorkContextHelper.getPropertyId();
    }

    private void setUpForExtendedStayPricingData(boolean isCpEnabled) {
        extendedStayRateMappingService.barDecisionService = barDecisionService;
        PacmanConfigParamsService configService = Mockito.mock(PacmanConfigParamsService.class);
        PacmanConfigParamsService pacmanConfigParamsService = Mockito.mock(PacmanConfigParamsService.class);
        DateService dateService = Mockito.mock(DateService.class);
        extendedStayRateMappingService.pacmanConfigParamsService = pacmanConfigParamsService;
        extendedStayRateMappingService.dateService = dateService;
        sqlHelper.setConfigService(configService);
        sqlHelper.setCrudService(crudService);
        sqlHelper.setDateService(dateService);
        barDecisionService.setSQLHelper(sqlHelper);
        barDecisionService.setConfigService(configService);
        when(dateService.getBusinessDate()).thenReturn(new Date());
        when(configService.getParameterValue(IPConfigParamName.BAR_BAR_OVRD_DISPLAY_COMPETITOR.value())).thenReturn("medianPrice");
        when(pacmanConfigParamsService.getBooleanParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED)).thenReturn(isCpEnabled);
        when(pacmanConfigParamsService.getParameterValue(IPConfigParamName.BAR_BAR_DECISION.value())).thenReturn("RateOfDay");
        when(pacmanConfigParamsService.getParameterValueByClientLevel(FeatureTogglesConfigParamName.ENABLE_PHYSICAL_CAPACITY_CONSIDERATION.value())).thenReturn("false");
        when(configService.getParameterValue(IPConfigParamName.BAR_WEB_RATE_SHOPPING_ENABLED.value())).thenReturn("false");
    }

    @Test
    public void deleteMultiDayOverridesTest() {
        Date startDate = DateUtil.toDate(START_DATE);
        Date endDate = DateUtil.toDate(END_DATE);
        Set<Product> products = new HashSet<>();
        Set<DayOfWeek> selectedDaysOfWeek = new HashSet<>();
        selectedDaysOfWeek.addAll(Arrays.asList(DayOfWeek.values()));
        List<Product> esProducts = getEsProducts();
        products.addAll(esProducts);
        Integer rateValue = 100;
        Date pastStartDate = DateUtil.addDaysToDate(startDate, -30);
        Date futureEndDate = DateUtil.addDaysToDate(endDate, 30);
        setupDataForEsRateOverrides(esProducts.get(0).getId(), "LV2", 7, rateValue, UPLOAD_PENDING, pastStartDate, futureEndDate);
        setupDataForEsRateOverrides(esProducts.get(1).getId(), "LV7", 15, rateValue, UPLOAD_PENDING, pastStartDate, futureEndDate);
        setupDataForEsRateOverrides(esProducts.get(2).getId(), "LV8", 30, rateValue, UPLOAD_PENDING, pastStartDate, futureEndDate);
        extendedStayRateMappingService.deleteMultiDayOverrides(startDate, endDate, products, selectedDaysOfWeek);
        flushAndClear();
        List<ExtendedStayRateUnqualifiedOverride> rateUnqualifiedOverrides = crudService.findByNamedQuery(ExtendedStayRateUnqualifiedOverride.GET_BY_DATE_RANGE,
                QueryParameter.with("startDate", startDate).and("endDate", endDate).parameters());
        assertEquals(0, rateUnqualifiedOverrides.size());
    }

    @Test
    public void testFetchExtendedStayPricingData() {
        setUpForExtendedStayPricingData(false);
        Integer accomTypeId = 6;
        Integer accomClassId = 2;

        Date startDate = DateUtil.toDate(START_DATE);
        Date endDate = DateUtil.toDate(END_DATE);

        prepareTotalActivityData(startDate, endDate);
        prepareDecisionBarOutputData(startDate, endDate);
        prepareOccupancyFCSTDate(accomTypeId, startDate, endDate);
        prepareDecisionLRVData(accomClassId, startDate, endDate);

        List<ExtendedStayRateMappingData> data = extendedStayRateMappingService.fetchExtendedStayPricingData(new LocalDate(START_DATE), new LocalDate(END_DATE));

        assertEquals(3, data.size());
        assertExtendedStayPricingData(data.get(0), new LocalDate(startDate), 1, 438, 97.99, 30, 199, 9);
        assertExtendedStayPricingData(data.get(1), new LocalDate(startDate).plusDays(1), 3, 438, 98.43, 31, 199, 7);
        assertExtendedStayPricingData(data.get(2), new LocalDate(startDate).plusDays(2), 5, 438, 98.87, 32, 199, 5);
    }

    @Test
    public void testShouldFetchExtendedStayPricingDataForCPOnlyForBARProductId() {
        setUpForExtendedStayPricingData(true);
        Integer accomTypeId = 6;
        Integer accomClassId = 2;

        Date startDate = DateUtil.toDate(START_DATE);
        Date endDate = DateUtil.toDate(END_DATE);

        prepareTotalActivityData(startDate, endDate);
        prepareCPDecisionBarOutputData(startDate, endDate, BAR_PRODUCT_ID);
        prepareCPDecisionBarOutputData(startDate, endDate, BI_WEEKLY_PRODUCT_ID);
        prepareOccupancyFCSTDate(accomTypeId, startDate, endDate);
        prepareDecisionLRVData(accomClassId, startDate, endDate);

        List<ExtendedStayRateMappingData> data = extendedStayRateMappingService.fetchExtendedStayPricingData(new LocalDate(START_DATE), new LocalDate(END_DATE));

        assertEquals(3, data.size());
        assertExtendedStayPricingData(data.get(0), new LocalDate(startDate), 1, 438, 97.99, 30, 99, 9);
        assertExtendedStayPricingData(data.get(1), new LocalDate(startDate).plusDays(1), 3, 438, 98.43, 31, 99, 7);
        assertExtendedStayPricingData(data.get(2), new LocalDate(startDate).plusDays(2), 5, 438, 98.87, 32, 99, 5);
    }

    @Test
    public void testFetchExtendedStayPricingDataWithCRandIsComponentRoom() {
        setUpForExtendedStayPricingData(false);
        Integer accomTypeId = 6;
        Integer accomClassId = 2;

        Date startDate = DateUtil.toDate(START_DATE);
        Date endDate = DateUtil.toDate(END_DATE);

        prepareTotalActivityData(startDate, endDate);
        prepareDecisionBarOutputData(startDate, endDate);
        prepareOccupancyFCSTDate(accomTypeId, startDate, endDate);
        prepareDecisionLRVData(accomClassId, startDate, endDate);
        updateAccomTypeIsComponentRoom(accomTypeId);

        List<ExtendedStayRateMappingData> data = extendedStayRateMappingService.fetchExtendedStayPricingData(new LocalDate(START_DATE), new LocalDate(END_DATE));
        assertEquals(3, data.size());
        data.forEach(obj -> {
            assertNull(obj.getHotelLevelDTO().getOccupancyForecastPercent());
        });
    }

    private void updateAccomTypeIsComponentRoom(int accomTypeId) {
        tenantCrudService().executeUpdateByNativeQuery(" update Accom_Type set isComponentRoom = 'Y' where Accom_Type_ID =" + accomTypeId);
    }

    private void assertExtendedStayPricingData(ExtendedStayRateMappingData extendedStayRateMappingData, LocalDate occupancyDate, Integer OOO, Integer roomsOnBooks,
                                               double occupancyForecatPercent, double lrv, double bar, Integer availableToSell) {
        assertEquals(occupancyDate, extendedStayRateMappingData.getDate());
        assertEquals(OOO, extendedStayRateMappingData.getHotelLevelDTO().getOutOfOrder());
        assertEquals(roomsOnBooks, extendedStayRateMappingData.getHotelLevelDTO().getRoomsOnBooks());
        assertEquals(0, BigDecimal.valueOf(occupancyForecatPercent).compareTo(extendedStayRateMappingData.getHotelLevelDTO().getOccupancyForecastPercent()));
        assertEquals(0, BigDecimal.valueOf(lrv).compareTo(extendedStayRateMappingData.getHotelLevelDTO().getLrv()));
        assertEquals(0, BigDecimal.valueOf(bar).compareTo(extendedStayRateMappingData.getHotelLevelDTO().getBar()));
        assertEquals(availableToSell, extendedStayRateMappingData.getHotelLevelDTO().getAvailableToSell());
    }

    @Test
    public void testFetchProductLevelData() {
        Integer accomTypeId = 6;
        LocalDate startDate = new LocalDate(START_DATE);
        LocalDate endDate = new LocalDate(END_DATE);
        List<Integer> ESRateUnqualifiedIds = prepareESRateUnqualifiedData();
        prepareESRateUnqualifiedDetailsData(accomTypeId, startDate, endDate, ESRateUnqualifiedIds);
        insertESRateUnqualifiedOverrides(ESRateUnqualifiedIds.get(0), accomTypeId, 100, UPLOAD_PENDING, endDate.toDate(), endDate.toDate());
        insertESRateUnqualifiedOverrides(ESRateUnqualifiedIds.get(1), accomTypeId, 100, UPLOAD_PENDING, endDate.toDate(), endDate.toDate());
        insertESRateUnqualifiedOverrides(ESRateUnqualifiedIds.get(2), accomTypeId, 100, UPLOAD_PENDING, endDate.toDate(), endDate.toDate());

        List<ProductLevelDTO> data = extendedStayRateMappingService.fetchProductLevelData(startDate, endDate);

        assertEquals(9, data.size());
        assertData(data.get(0), startDate, BigDecimal.valueOf(90), null, null, "Bi-Weekly");
        assertData(data.get(1), startDate, BigDecimal.valueOf(90), null, null, "Monthly");
        assertData(data.get(2), startDate, BigDecimal.valueOf(90), null, null, "Weekly");
        assertData(data.get(3), startDate.plusDays(1), BigDecimal.valueOf(90), null, null, "Bi-Weekly");
        assertData(data.get(4), startDate.plusDays(1), BigDecimal.valueOf(90), null, null, "Monthly");
        assertData(data.get(5), startDate.plusDays(1), BigDecimal.valueOf(90), null, null, "Weekly");
        assertData(data.get(6), startDate.plusDays(2), BigDecimal.valueOf(90), BigDecimal.valueOf(100), UPLOAD_PENDING, "Bi-Weekly");
        assertData(data.get(7), startDate.plusDays(2), BigDecimal.valueOf(90), BigDecimal.valueOf(100), UPLOAD_PENDING, "Monthly");
        assertData(data.get(8), startDate.plusDays(2), BigDecimal.valueOf(90), BigDecimal.valueOf(100), UPLOAD_PENDING, "Weekly");
    }

    @Test
    public void testGetExtendStayProducts() {
        Set<Product> products = extendedStayRateMappingService.getExtendStayProducts();
        assertEquals(3, products.size());
        assertTrue(products.stream().anyMatch(product -> product.getCode().equals("Weekly")));
        assertTrue(products.stream().anyMatch(product -> product.getCode().equals("Bi-Weekly")));
        assertTrue(products.stream().anyMatch(product -> product.getCode().equals("Monthly")));
    }

    @Test
    public void testGetExtendedStayProductDefinition() {
        Map<Integer, ExtendedStayProductDefinition> productDefinitionMap = extendedStayRateMappingService.getExtendedStayProductDefinition();
        assertEquals(3, productDefinitionMap.size());
        assertTrue(productDefinitionMap.values().stream().anyMatch(productDefinition -> productDefinition.getRateCode().equals("LV2")));
        assertTrue(productDefinitionMap.values().stream().anyMatch(productDefinition -> productDefinition.getRateCode().equals("LV7")));
        assertTrue(productDefinitionMap.values().stream().anyMatch(productDefinition -> productDefinition.getRateCode().equals("LV8")));
    }

    @Test
    public void getExtendedStayRatesTest() {
        prepareESRateUnqualifiedData();
        Map<String, Integer> ESRates = extendedStayRateMappingService.getExtendedStayRates();
        assertNotNull(ESRates);
        assertEquals(3, ESRates.size());
        assertTrue(ESRates.keySet().stream().anyMatch(ESRate -> ESRate.equals("LV2")));
        assertTrue(ESRates.keySet().stream().anyMatch(ESRate -> ESRate.equals("LV7")));
        assertTrue(ESRates.keySet().stream().anyMatch(ESRate -> ESRate.equals("LV8")));
    }

    @Test
    public void getOverridesTest() {
        Integer accomTypeId = 6;
        LocalDate startDate = new LocalDate(START_DATE);
        LocalDate endDate = new LocalDate(END_DATE);
        List<Integer> ESRateUnqualifiedIds = prepareESRateUnqualifiedData();
        prepareESRateUnqualifiedDetailsData(accomTypeId, startDate, endDate, ESRateUnqualifiedIds);
        insertESRateUnqualifiedOverrides(ESRateUnqualifiedIds.get(0), accomTypeId, 100, UPLOAD_PENDING, endDate.toDate(), endDate.toDate());
        insertESRateUnqualifiedOverrides(ESRateUnqualifiedIds.get(1), accomTypeId, 100, UPLOAD_PENDING, endDate.toDate(), endDate.toDate());
        Map<String, ExtendedStayRateUnqualifiedOverride> overrides = extendedStayRateMappingService.getOverrides(startDate.toDate(), endDate.toDate());
        assertEquals(2, overrides.size());
        assertTrue(overrides.values().stream().anyMatch(override -> override.getEsRateUnqualifiedID().equals(ESRateUnqualifiedIds.get(0))
                && override.getOccupancyDate().toString().contains(endDate.toString()) && override.getRateValue().compareTo(BigDecimal.valueOf(100)) == 0
                && override.getAccomTypeID().equals(6)));
        assertTrue(overrides.values().stream().anyMatch(override -> override.getEsRateUnqualifiedID().equals(ESRateUnqualifiedIds.get(1))
                && override.getOccupancyDate().toString().contains(endDate.toString()) && override.getRateValue().compareTo(BigDecimal.valueOf(100)) == 0
                && override.getAccomTypeID().equals(6)));
    }

    @Test
    public void getSpecialEventOccupancyDateSummariesTest() {
        LocalDate startDate = new LocalDate(START_DATE);
        LocalDate endDate = new LocalDate(END_DATE);
        UniquePropertySpecialEventCreator specialEventCreator = new UniquePropertySpecialEventCreator();
        specialEventCreator.createUniquePropertySpecialEventCreatorForDate(startDate.toDate(), endDate.toDate());
        UniquePropertySpecialEventInstanceCreator specialEventInstanceCreator = new UniquePropertySpecialEventInstanceCreator();
        specialEventInstanceCreator.createUniquePropertySpecialEventInstanceForDates(startDate.toDate(), endDate.toDate());
        List<SpecialEventOccupancyDetails> specialEventDetails = extendedStayRateMappingService.getSpecialEventOccupancyDateSummaries(startDate);
        assertNotNull(specialEventDetails);
        assertEquals(1, specialEventDetails.size());
        assertEquals("Property Special Event Name", specialEventDetails.get(0).getName());
        assertEquals(DateUtil.toDate(START_DATE).toString(), specialEventDetails.get(0).getStartDate().toString());
        assertEquals(DateUtil.toDate(END_DATE).toString(), specialEventDetails.get(0).getEndDate().toString());
    }

    @Test
    public void getSpecialEventOccupancyDateSummariesForSpecialEventNameWithInstanceNameTest() {
        LocalDate startDate = new LocalDate(START_DATE);
        LocalDate endDate = new LocalDate(END_DATE);

        PropertySpecialEventInstance propertySpecialEventInstance = UniquePropertySpecialEventInstanceCreator.createUniquePropertySpecialEventInstanceForDates(startDate.toDate(), endDate.toDate());
        propertySpecialEventInstance.setEventInstanceName("Instance Name");
        tenantCrudService().save(propertySpecialEventInstance);
        List<SpecialEventOccupancyDetails> specialEventDetails = extendedStayRateMappingService.getSpecialEventOccupancyDateSummaries(startDate);

        assertNotNull(specialEventDetails);
        assertEquals(1, specialEventDetails.size());
        assertEquals("Property Special Event Name - Instance Name", specialEventDetails.get(0).getName());
        assertEquals(DateUtil.toDate(START_DATE).toString(), specialEventDetails.get(0).getStartDate().toString());
        assertEquals(DateUtil.toDate(END_DATE).toString(), specialEventDetails.get(0).getEndDate().toString());
    }

    @Test
    public void competitorDetailsForADateTest() {
        extendedStayRateMappingService.barDecisionService = barDecisionService;
        PacmanConfigParamsService configService = Mockito.mock(PacmanConfigParamsService.class);
        barDecisionService.setConfigService(configService);
        when(configService.getParameterValue(IPConfigParamName.BAR_BAR_OVRD_DISPLAY_COMPETITOR.value())).thenReturn("medianPrice");
        when(configService.getParameterValue(IPConfigParamName.BAR_WEB_RATE_SHOPPING_ENABLED.value())).thenReturn("false");
        UniqueWebRateCreator webRateCreator = new UniqueWebRateCreator();
        webRateCreator.createWebrate(1, 3, 3, 2, 6, START_DATE, 100);

        List<CompetitorDetail> competitorDetails = extendedStayRateMappingService.competitorDetailsForADate(DateUtil.toDate(START_DATE));

        assertEquals(1, competitorDetails.size());
        CompetitorDetail competitorDetail = competitorDetails.get(0);
        assertEquals("Orbitz", competitorDetail.getChannelName());
        assertEquals("Luxor", competitorDetail.getCompetitorName());
        assertEquals("STD", competitorDetail.getAccomClassName());
        assertEquals("Standard 2", competitorDetail.getCompRccomType());
        assertEquals("100.00", competitorDetail.getBarValue());
        assertEquals(Integer.valueOf(6), competitorDetail.getLos());
    }

    @Test
    public void fetchProductLevelCompetitorsTest() {
        LocalDate startDate = new LocalDate(START_DATE);
        LocalDate endDate = new LocalDate(END_DATE);
        UniqueWebRateCreator webRateCreator = new UniqueWebRateCreator();
        webRateCreator.createWebrate(1, 3, 3, 2, 7, START_DATE, 100);
        UniqueExtendedStayCompetitorMappingCreator competitorMappingCreator = new UniqueExtendedStayCompetitorMappingCreator();
        competitorMappingCreator.create(3, 100);
        UniqueESProductDefinitionCreator productDefinitionCreator = new UniqueESProductDefinitionCreator();
        productDefinitionCreator.create(2, "LV2", 7);

        Map<Date, Map<String, BigDecimal>> competitorMap = extendedStayRateMappingService.fetchProductLevelCompetitors(startDate, endDate);

        assertEquals(1, competitorMap.size());
        assertTrue(competitorMap.containsKey(startDate.toDate()));
        assertTrue(competitorMap.get(startDate.toDate()).containsKey("Weekly"));
        assertEquals(0, competitorMap.get(startDate.toDate()).get("Weekly").compareTo(BigDecimal.valueOf(100)));
    }

    @Test
    public void mergeHotelAndProductLevelDataTest() {
        List<ExtendedStayRateMappingData> extendedStayRateMappingData = new ArrayList<>();
        Map<Date, Map<String, BigDecimal>> productLevelCompetitors = new HashMap<>();
        Map<Date, Map<String, BigDecimal>> productRecommendation = new HashMap<>();
        List<ProductLevelDTO> productLevelDTOs = new ArrayList<>();

        ProductLevelDTO productLevelDTO = new ProductLevelDTO();
        productLevelDTO.setOccupancyDate(new LocalDate(START_DATE));
        productLevelDTO.setProductCode("Weekly");
        productLevelDTOs.add(productLevelDTO);

        productLevelDTO = new ProductLevelDTO();
        productLevelDTO.setOccupancyDate(new LocalDate(START_DATE));
        productLevelDTO.setProductCode("Bi-Weekly");
        productLevelDTOs.add(productLevelDTO);

        productLevelDTO = new ProductLevelDTO();
        productLevelDTO.setOccupancyDate(new LocalDate(START_DATE));
        productLevelDTO.setProductCode("Monthly");
        productLevelDTOs.add(productLevelDTO);


        ExtendedStayRateMappingData extendedStayRateMappingData1 = new ExtendedStayRateMappingData();
        extendedStayRateMappingData1.setDate(new LocalDate(START_DATE));
        extendedStayRateMappingData.add(extendedStayRateMappingData1);

        Map<String, BigDecimal> competitorValueMap = new HashMap<>();
        competitorValueMap.put("Weekly", BigDecimal.valueOf(100));
        competitorValueMap.put("Bi-Weekly", BigDecimal.valueOf(200));
        competitorValueMap.put("Monthly", BigDecimal.valueOf(300));

        Map<String, BigDecimal> recommendedValueMap = new HashMap<>();
        recommendedValueMap.put("Weekly", BigDecimal.valueOf(100));
        recommendedValueMap.put("Bi-Weekly", null);
        recommendedValueMap.put("Monthly", BigDecimal.valueOf(-100));

        productLevelCompetitors.put(DateUtil.toDate(START_DATE), competitorValueMap);
        productRecommendation.put(DateUtil.toDate(START_DATE), recommendedValueMap);

        List<ExtendedStayRateMappingData> esRateMappingData = extendedStayRateMappingService.mergeHotelAndProductLevelData(extendedStayRateMappingData, productLevelDTOs, productLevelCompetitors, productRecommendation);

        assertTrue(0 < esRateMappingData.size());
        assertEquals(1, esRateMappingData.size());
        assertEquals(new LocalDate(START_DATE), esRateMappingData.get(0).getDate());
        assertEquals(new LocalDate(START_DATE), esRateMappingData.get(0).getProductslevelDtos().get(0).getOccupancyDate());
        assertEquals(new LocalDate(START_DATE), esRateMappingData.get(0).getProductslevelDtos().get(1).getOccupancyDate());
        assertEquals("Weekly", esRateMappingData.get(0).getProductslevelDtos().get(0).getProductCode());
        assertEquals("Bi-Weekly", esRateMappingData.get(0).getProductslevelDtos().get(1).getProductCode());
        assertEquals(0, esRateMappingData.get(0).getProductslevelDtos().get(0).getCompetitor().compareTo(BigDecimal.valueOf(100)));
        assertEquals(0, esRateMappingData.get(0).getProductslevelDtos().get(1).getCompetitor().compareTo(BigDecimal.valueOf(200)));
        assertEquals(0, esRateMappingData.get(0).getProductslevelDtos().get(2).getCompetitor().compareTo(BigDecimal.valueOf(300)));

        assertEquals(0, esRateMappingData.get(0).getProductslevelDtos().get(0).getRecommended().compareTo(BigDecimal.valueOf(100)));
        assertEquals(null, esRateMappingData.get(0).getProductslevelDtos().get(1).getRecommended());
        assertEquals(null, esRateMappingData.get(0).getProductslevelDtos().get(2).getRecommended());
    }

    private void assertData(ProductLevelDTO productLevelDTO, LocalDate occupancyDate, BigDecimal currentBar, BigDecimal override, String status, String productCode) {
        assertEquals(occupancyDate, productLevelDTO.getOccupancyDate());
        assertEquals(0, currentBar.compareTo(productLevelDTO.getCurrentBar()));
        if (override != null) {
            assertEquals(0, override.compareTo(productLevelDTO.getOverride()));
        }
        assertEquals(status, productLevelDTO.getStatus());
        assertEquals(productCode, productLevelDTO.getProductCode());
    }

    private List<Integer> prepareESRateUnqualifiedData() {
        List<Integer> ESRateUnqualifiedIDs = new ArrayList<>();
        ESRateUnqualifiedIDs.add(createExtendedStayRateUnqualifiedObject("LV2"));
        ESRateUnqualifiedIDs.add(createExtendedStayRateUnqualifiedObject("LV7"));
        ESRateUnqualifiedIDs.add(createExtendedStayRateUnqualifiedObject("LV8"));
        return ESRateUnqualifiedIDs;
    }

    private Integer createExtendedStayRateUnqualifiedObject(String rateCodeName) {
        ExtendedStayRateUnqualified esru = new ExtendedStayRateUnqualified();
        esru.setEsRateCodeName(rateCodeName);
        esru.setCreatedDTTM(LocalDateUtils.toJavaLocalDateTime(12345698));
        esru.setFileMetadataId(1);
        esru.setLastUpdatedDTTM(LocalDateUtils.toJavaLocalDateTime(12345698));
        esru.setPropertyId(PROPERTY_ID);
        tenantCrudService().save(esru);
        return esru.getId();
    }

    private void prepareESRateUnqualifiedDetailsData(Integer accomTypeId, LocalDate startDate, LocalDate endDate, List<Integer> ESRateUnqualifiedIds) {
        int days = Days.daysBetween(startDate, endDate).getDays();
        for (int i = 0; i <= days; i++) {
            insertESRateUnqualifiedDetails(ESRateUnqualifiedIds.get(0), accomTypeId, startDate.plusDays(i));
            insertESRateUnqualifiedDetails(ESRateUnqualifiedIds.get(1), accomTypeId, startDate.plusDays(i));
            insertESRateUnqualifiedDetails(ESRateUnqualifiedIds.get(2), accomTypeId, startDate.plusDays(i));
        }
    }

    private void insertESRateUnqualifiedDetails(Integer rateUnqualifiedId, Integer accomTypeId, LocalDate occupancyDate) {
        String insertRateUnqualifiedDetails = new StringBuilder("INSERT INTO [ES_Rate_Unqualified_Details]([ES_Rate_Unqualified_ID],[Accom_Type_ID] ,[Occupancy_Date]")
                .append(",[Rate_Value],[Created_DTTM],[Last_Updated_DTTM]) ")
                .append("VALUES (" + rateUnqualifiedId + ", " + accomTypeId + ", '" + occupancyDate + "', 90, getdate(), getdate()) ;")
                .toString();
        tenantCrudService().executeUpdateByNativeQuery(insertRateUnqualifiedDetails);
    }

    private void prepareDecisionLRVData(Integer accomClassId, Date startDate, Date endDate) {
        int days = getDifferenceInDates(startDate, endDate);
        final StringBuilder lrvDecisionQuery = new StringBuilder();
        lrvDecisionQuery.append("DECLARE @decisionID int\n");
        lrvDecisionQuery.append("select @decisionID = Max(Decision_ID) from Decision_LRV\n");
        lrvDecisionQuery.append("INSERT INTO Decision_LRV([Decision_ID],[Property_ID],[Accom_Class_ID],[Occupancy_DT],[LRV],[CreateDate_DTTM]) VALUES\n");
        IntStream.range(0, days + 1).forEach(i -> {
            lrvDecisionQuery.append(" (@decisionID," + PROPERTY_ID + "," + accomClassId + ",'" + DateUtil.formatDate(DateUtil.addDaysToDate(startDate, i), DateUtil.DEFAULT_DATE_FORMAT) + "'," + BigDecimal.valueOf(30 + i) + ",CURRENT_TIMESTAMP)\n");
            if (i < (days)) lrvDecisionQuery.append(", ");
        });
        lrvDecisionQuery.append(";");
        tenantCrudService().executeUpdateByNativeQuery(lrvDecisionQuery.toString());
    }

    private int getDifferenceInDates(Date startDate, Date endDate) {
        Integer start = DateUtil.getDateForDate(startDate);
        Integer end = DateUtil.getDateForDate(endDate);
        return (int) DateUtil.getDateDiff(end, start);
    }

    private void prepareOccupancyFCSTDate(Integer accomTypeId, Date startDate, Date endDate) {
        int days = getDifferenceInDates(startDate, endDate);
        int maxDecisionId = crudService.findByNamedQuerySingleResult(Decision.GET_MAX_ID, QueryParameter.with("propertyId", 5).parameters());
        Calendar cal = Calendar.getInstance();
        for (int i = 0; i <= days; i++) {
            Date occupancyDate = DateUtil.addDaysToDate(startDate, i);
            cal.setTime(occupancyDate);
            int monthID = cal.get(Calendar.MONTH);
            int year = cal.get(Calendar.YEAR);
            int yearID = Integer.parseInt(tenantCrudService().findByNativeQuery("Select Year_ID from Year where Year_Number = " + year).get(0).toString());
            final String query = "INSERT INTO Occupancy_FCST([Decision_ID],[Property_ID],[MKT_SEG_ID]" +
                    ",[Accom_Type_ID],[Occupancy_DT],[Occupancy_NBR],[Revenue],[Month_ID],[Year_ID],[CreateDate_DTTM]) values(" + maxDecisionId + ", " + PROPERTY_ID + ", 1, " + accomTypeId +
                    ",'" + DateUtil.formatDate(DateUtil.addDaysToDate(startDate, i), DateUtil.DEFAULT_DATE_FORMAT) + "', 10, 200, " + monthID + ", " + yearID + ", GETDATE())";

            tenantCrudService().executeUpdateByNativeQuery(query);
        }

    }

    private void prepareDecisionBarOutputData(Date startDate, Date endDate) {
        int days = getDifferenceInDates(startDate, endDate);
        for (int i = 0; i <= days; i++) {
            Date occupancyDate = DateUtil.addDaysToDate(startDate, i);
            UniqueDecisionBarOutputCreator.createDecisionBarOutput(occupancyDate, -1, 2, "None", "LV0", "LV2", "LV7");
        }
    }

    private void prepareCPDecisionBarOutputData(Date startDate, Date endDate, int productId) {
        int days = getDifferenceInDates(startDate, endDate);
        for (int i = 0; i <= days; i++) {
            Date occupancyDate = DateUtil.addDaysToDate(startDate, i);
            AccomType accomType = tenantCrudService().findByNamedQuerySingleResult(AccomType.BY_CODE, QueryParameter.with("code", DBL).parameters());
            Product barProduct = tenantCrudService().find(Product.class, productId);
            Integer decisionId = UniqueDecisionCreator.createDecision(occupancyDate, Constants.DECISION_TYPE_BDE).getId();
            saveCPDecisionBarOutput(accomType, barProduct, new LocalDate(occupancyDate), decisionId);
        }
    }

    private void saveCPDecisionBarOutput(AccomType accomType, Product product, LocalDate arrivalDate, Integer decisionId) {
        CPDecisionBAROutput cpDecisionBAROutput = new CPDecisionBAROutput();
        cpDecisionBAROutput.setArrivalDate(arrivalDate);
        cpDecisionBAROutput.setProduct(product);
        cpDecisionBAROutput.setAccomType(accomType);
        cpDecisionBAROutput.setDecisionId(decisionId);
        cpDecisionBAROutput.setDecisionReasonTypeId(DecisionReasonType.ALL_IS_WELL.getId());
        cpDecisionBAROutput.setPropertyId(TestProperty.H1.getId());
        cpDecisionBAROutput.setLengthOfStay(-1);
        cpDecisionBAROutput.setLrv(null);
        cpDecisionBAROutput.setCompetitorRate("534.34");
        cpDecisionBAROutput.setOverrideType(DecisionOverrideType.CEIL);
        cpDecisionBAROutput.setFinalBAR(new BigDecimal("99"));
        cpDecisionBAROutput.setOptimalBAR(new BigDecimal("99"));
        cpDecisionBAROutput.setSpecificOverride(null);
        cpDecisionBAROutput.setFloorOverride(null);
        cpDecisionBAROutput.setCeilingOverride(null);
        CrudService tenantCrudService = CrudServiceBeanExtension.getTenantCrudService();
        tenantCrudService.save(cpDecisionBAROutput);
    }

    private void prepareTotalActivityData(Date startDate, Date endDate) {
        int days = getDifferenceInDates(startDate, endDate);
        BigDecimal total = BigDecimal.valueOf(448);
        for (int i = 0; i <= days; i++) {
            Date currentDate = DateUtil.addDaysToDate(startDate, i);
            UniqueTotalActivityCreator.createTotalActivity(PROPERTY_ID, currentDate, BigDecimal.valueOf(i + 1), BigDecimal.valueOf(i), total, total.subtract(BigDecimal.TEN), total.subtract(BigDecimal.valueOf(10 * i + 15)), total.subtract(BigDecimal.valueOf(5 * i + 20)), total.subtract(BigDecimal.TEN).multiply(BigDecimal.valueOf(100)));
        }

    }

    private void setESProductDefinition() {
        createESProductDefinition(6, 1, 2, 7, "LV2");
        createESProductDefinition(6, 2, 3, 15, "LV7");
        createESProductDefinition(6, 3, 4, 30, "LV8");
    }

    private ExtendedStayProductDefinition createESProductDefinition(Integer accomTypeId, Integer id, Integer productId, Integer los, String rateCode) {
        ExtendedStayProductDefinition productDefinition = new ExtendedStayProductDefinition();
        productDefinition.setAccomTypeID(accomTypeId);
        productDefinition.setId(id);
        productDefinition.setProductId(productId);
        productDefinition.setLosBucket(los);
        productDefinition.setCreatedByUserId(11403);
        productDefinition.setCreatedDTTM(LocalDateTime.now());
        productDefinition.setLastUpdatedByUserId(11403);
        productDefinition.setLastUpdatedDTTM(LocalDateTime.now());
        productDefinition.setRateCode(rateCode);
        crudService.save(productDefinition);
        return productDefinition;
    }

    private List<Product> getEsProducts() {
        return crudService.findByNamedQuery(Product.GET_BY_TYPE, QueryParameter.with("type", Constants.EXTENDED_STAY).parameters());
    }

    private void setupDataForEsRateOverrides(Integer productId, String esRateCodeName, int los, Integer rateValue, String overrideStatus, Date startDate, Date endDate) {
        insertEsProductDef(productId, DBL, esRateCodeName, los);
        final Integer esRateUqId = saveExtendedStayRateUnqualified(esRateCodeName);
        insertESRateUnqualifiedOverrides(esRateUqId, (Integer) cacheAccomData.get(DBL), rateValue, overrideStatus, startDate, endDate);
        flushAndClear();
    }

    private Integer saveExtendedStayRateUnqualified(String esRateCodeName) {
        ExtendedStayRateUnqualified extendedStayRateUnqualified = new ExtendedStayRateUnqualified();
        extendedStayRateUnqualified.setEsRateCodeCurrency("USD");
        extendedStayRateUnqualified.setEsRateCodeName(esRateCodeName);
        extendedStayRateUnqualified.setFileMetadataId(2);
        extendedStayRateUnqualified.setPropertyId(PROPERTY_ID);
        extendedStayRateUnqualified.setCreatedDTTM(LocalDateTime.now());
        extendedStayRateUnqualified.setLastUpdatedDTTM(LocalDateTime.now());
        crudService.save(extendedStayRateUnqualified);
        return extendedStayRateUnqualified.getId();
    }

    private void insertESRateUnqualifiedOverrides(Integer esRateUqId, Integer accomTypeID, Integer rateValue, String overrideStatus, Date startDate, Date endDate) {
        crudService.executeUpdateByNativeQuery(INSERT_INTO_ES_RATE_UNQUALIFIED_OVERRIDE, QueryParameter.with("esRateUqId", esRateUqId)
                .and("accomTypeID", accomTypeID).and("rateValue", rateValue).and("overrideStatus", overrideStatus).and("startDate", startDate)
                .and("endDate", endDate).parameters());

    }

    private void insertEsProductDef(final int productId, final String accomType, final String rateCode, final int los) {
        String insertEsProductDefinition = new StringBuilder().append("INSERT INTO ES_Product_Definition ").append(" (Product_ID ")
                .append(", Accom_Type_ID ")
                .append(" ,ES_Rate_Code ")
                .append(" ,Los_Bucket ")
                .append(" ,Created_By_User_ID ")
                .append(" ,Created_DTTM ")
                .append(" ,Last_Updated_By_User_ID ")
                .append(" ,Last_Updated_DTTM) ")
                .append("     VALUES ").append(" (" + productId + ",'")
                .append(cacheAccomData.get(accomType)).append("','")
                .append(rateCode).append("',")
                .append(los).append(",1,getDate(),1,getDate())").toString();
        crudService.executeUpdateByNativeQuery(insertEsProductDefinition);
    }
}