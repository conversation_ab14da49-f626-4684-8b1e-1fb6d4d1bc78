package com.ideas.tetris.pacman.services.bestavailablerate;

import com.ideas.g3.test.category.SlowDBTest;
import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomType;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomTypeVendorMapping;
import com.ideas.tetris.pacman.services.accommodation.service.RoomTypeVendorMappingService;
import com.ideas.tetris.pacman.services.agilerates.configuration.entity.AgileRatesDecisionsSentBy;
import com.ideas.tetris.pacman.services.agilerates.configuration.entity.AgileRatesOffsetMethod;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.CPConfiguration;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.CPDecisionBAROutput;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.CPDecisionBAROutputOverride;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.CPPaceDecisionBAROutput;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.CPPaceDecisionBAROutputDifferential;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.DecisionDailybarOutput;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.DecisionDailybarOutputKey;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.OccupancyType;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.OffsetMethod;
import com.ideas.tetris.pacman.services.componentrooms.entity.CRAccomTypeMapping;
import com.ideas.tetris.pacman.services.pricingconfiguration.entity.MinimumIncrementMethod;
import com.ideas.tetris.pacman.services.pricingconfiguration.service.PricingConfigurationService;
import com.ideas.tetris.pacman.services.product.Product;
import com.ideas.tetris.pacman.services.product.RoundingRule;
import com.ideas.tetris.pacman.services.unqualifiedrate.entity.RateUnqualified;
import com.ideas.tetris.pacman.util.BigDecimalUtil;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.crudservice.TableBatch;
import com.ideas.tetris.platform.common.errorhandling.ErrorCode;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.common.externalsystem.ExternalSystem;
import com.ideas.tetris.platform.common.externalsystem.ExternalSystemHelper;
import com.ideas.tetris.platform.common.time.LocalDateUtils;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import org.joda.time.LocalDate;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.TreeMap;

import static com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName.COMPONENT_ROOMS_PRICE_AS_SUM_OF_PARTS;
import static com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName.CPBASE_ROOM_TYPE_ONLY_ENABLED;
import static com.ideas.tetris.pacman.services.bestavailablerate.CPOptimalBARService.BUSINESS_DATE;
import static com.ideas.tetris.pacman.services.bestavailablerate.CPOptimalBARService.CONSORTIA_FREE_UPGRADE_MISSING_DECISION_ACCOM_TYPE_DISCONTINUED;
import static com.ideas.tetris.pacman.services.bestavailablerate.CPOptimalBARService.CONSORTIA_FREE_UPGRADE_MISSING_DECISION_ACCOM_TYPE_NOT_FOUND;
import static com.ideas.tetris.pacman.services.bestavailablerate.CPOptimalBARService.CONSORTIA_FREE_UPGRADE_MISSING_DECISION_ACCOM_TYPE_ZERO_CAPACITY;
import static com.ideas.tetris.pacman.services.bestavailablerate.CPOptimalBARService.END_DATE;
import static com.ideas.tetris.pacman.services.bestavailablerate.CPOptimalBARService.PROPERTY_ID;
import static com.ideas.tetris.pacman.services.bestavailablerate.CPOptimalBARService.START_DATE;
import static com.ideas.tetris.pacman.services.bestavailablerate.entity.CPPaceDecisionBAROutputDifferential.INSERT_DECISIONS_INTO_CP_PACE_DIFFERENTIAL;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@SlowDBTest
@MockitoSettings(strictness = Strictness.LENIENT)
public class CPOptimalBARServiceTest extends AbstractCPServiceTest {
    private CPOptimalBARService service = new CPOptimalBARService();

    @Override
    CPBarDecisionService getCPBarDecisionService() {
        return service;
    }

    @Override
    protected boolean isAgileRatesEnabled() {
        return false;
    }

    @Override
    protected boolean isPerPersonEnabled() {
        return false;
    }

    @Override
    protected boolean isIndependentProductsEnabled() {
        return false;
    }

    @Mock(name = "tenantCrudService")
    CrudService crudService;

    @BeforeEach
    public void setUp() throws Exception {
        setTax(BigDecimal.ZERO);
    }

    @Test
    public void roundOptimalBARsNoRoundingRule() {
        // Add Floor/Ceiling
        addTransientPricingBaseAccomType(RT_DOUBLE, BigDecimal.TEN, new BigDecimal("100"), BigDecimal.ZERO);

        CPDecisionBAROutput cpDecisionBAROutput = addCPDecisionBarOutput(startDate, RT_DOUBLE, new BigDecimal("20.00"));

        service.roundOptimalBARs(startDate, endDate);

        cpDecisionBAROutput = getCPDecisionBarOutputFor(cpDecisionBAROutput.getId());
        assertBigDecimalEquals(new BigDecimal("20.00"), cpDecisionBAROutput.getPrettyBAR());
        assertBigDecimalEquals(new BigDecimal("20.00"), cpDecisionBAROutput.getRoomsOnlyBAR());
        assertBigDecimalEquals(new BigDecimal("20.00"), cpDecisionBAROutput.getFinalBAR());
    }

    @Test
    void populate_CP_Pace_Decision_Bar_Output_Differential_WhenExtendedWindowIsTrue() {
        // when extended window flag is true and then populate table only till extended window end date
        service.tenantCrudService = crudService;
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_UPLOAD_ON_OVERRIDE_FOR_EXTENDED_WINDOW)).thenReturn(true);
        Date optimizationEndDate = new Date();
        when(dateService.getOptimizationWindowEndDateBDE()).thenReturn(optimizationEndDate);
        java.time.LocalDate startDate = java.time.LocalDate.now();
        java.time.LocalDate endDate = startDate.plusDays(10);
        service.roundOptimalBARs(LocalDateUtils.toJodaLocalDate(startDate), LocalDateUtils.toJodaLocalDate(endDate));
        assertExpected(true, LocalDateUtils.toJodaLocalDate(startDate), LocalDateUtils.fromDate(optimizationEndDate));
    }

    @Test
    void populate_CP_Pace_Decision_Bar_Output_Differential_WhenEndDateIsBeforeOptimizationEndDate_WhenExtendedWindowIsTrue() {
        // when extended window flag is true and then populate table only till end date as optimization end date is greater than endDate,basically optimization cdp end date scenario
        service.tenantCrudService = crudService;
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_UPLOAD_ON_OVERRIDE_FOR_EXTENDED_WINDOW)).thenReturn(true);
        Date optimizationEndDate = DateUtil.addDaysToDate(new Date(), 5);
        when(dateService.getOptimizationWindowEndDateBDE()).thenReturn(optimizationEndDate);
        java.time.LocalDate startDate = java.time.LocalDate.now();
        java.time.LocalDate endDate = startDate.plusDays(1);
        service.roundOptimalBARs(LocalDateUtils.toJodaLocalDate(startDate), LocalDateUtils.toJodaLocalDate(endDate));
        assertExpected(true, LocalDateUtils.toJodaLocalDate(startDate), LocalDateUtils.toJodaLocalDate(endDate));
    }

    @Test
    void populate_CP_Pace_Decision_Bar_Output_Differential_WhenExtendedWindowIsFalse() {
        // when extended window is false
        service.tenantCrudService = crudService;
        endDate = endDate.plusDays(10);
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_UPLOAD_ON_OVERRIDE_FOR_EXTENDED_WINDOW)).thenReturn(false);
        Date optimizationEndDate = new Date();
        when(dateService.getOptimizationWindowEndDateBDE()).thenReturn(optimizationEndDate);
        service.roundOptimalBARs(startDate, endDate);
        assertExpected(true, startDate, endDate);
    }

    @Test
    void populate_CP_Pace_Decision_Bar_Output_Differential_WhenStartDateIsAfterOptimizationEndDate_WhenExtendedWindowIsTrue() {
        // when extended window flag is true and then populate table only till end date as optimization end date is greater than endDate,basically optimization cdp end date scenario
        service.tenantCrudService = crudService;
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_UPLOAD_ON_OVERRIDE_FOR_EXTENDED_WINDOW)).thenReturn(true);
        Date optimizationEndDate = DateUtil.addDaysToDate(new Date(), 5);
        when(dateService.getOptimizationWindowEndDateBDE()).thenReturn(optimizationEndDate);
        java.time.LocalDate startDate = java.time.LocalDate.now().plusDays(10);
        java.time.LocalDate endDate = startDate.plusDays(10);
        service.roundOptimalBARs(LocalDateUtils.toJodaLocalDate(startDate), LocalDateUtils.toJodaLocalDate(endDate));
        assertExpected(false, LocalDateUtils.toJodaLocalDate(startDate), LocalDateUtils.toJodaLocalDate(endDate));
    }

    private void assertExpected(boolean isRan, LocalDate startDate, LocalDate endDate) {
        Map<String, Object> parameters = QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId())
                .and(START_DATE, startDate)
                .and(END_DATE, endDate)
                .and(BUSINESS_DATE, dateService.getBusinessDate())
                .parameters();

        if (isRan) {
            verify(crudService).executeUpdateByNativeQuery(INSERT_DECISIONS_INTO_CP_PACE_DIFFERENTIAL, parameters);
        } else {
            verify(crudService, never()).executeUpdateByNativeQuery(INSERT_DECISIONS_INTO_CP_PACE_DIFFERENTIAL, parameters);
        }
    }

    @Test
    public void roundOptimalBARs() {
        // Set a Pricing Rule
        setRoundingRule(9, 8, 7);

        // Add Floor/Ceiling
        addTransientPricingBaseAccomType(RT_DOUBLE, BigDecimal.TEN, new BigDecimal("100"), BigDecimal.ZERO);

        CPDecisionBAROutput cpDecisionBAROutput = addCPDecisionBarOutput(startDate, RT_DOUBLE, new BigDecimal("20.00"));

        service.roundOptimalBARs(startDate, endDate);

        cpDecisionBAROutput = getCPDecisionBarOutputFor(cpDecisionBAROutput.getId());
        assertBigDecimalEquals(new BigDecimal("19.87"), cpDecisionBAROutput.getPrettyBAR());
        assertBigDecimalEquals(new BigDecimal("19.87"), cpDecisionBAROutput.getRoomsOnlyBAR());
        assertBigDecimalEquals(new BigDecimal("19.87"), cpDecisionBAROutput.getFinalBAR());
    }

    @Test
    public void roundOptimalBARsNonBaseRTUsesBaseRTFinalBar() {
        // Set the Base RT floor/ceiling
        addTransientPricingBaseAccomType(RT_DOUBLE, BigDecimal.TEN, new BigDecimal("1000"), BigDecimal.ZERO);

        // Add the QUEEN single offset
        addCPConfigOffsetAccomType(RT_QUEEN, OccupancyType.SINGLE, OffsetMethod.PERCENTAGE, new BigDecimal("25"));

        // Add supplements
        addAccomTypeSupplement(RT_DOUBLE, OccupancyType.SINGLE, BigDecimal.TEN);
        addAccomTypeSupplement(RT_QUEEN, OccupancyType.SINGLE, BigDecimal.TEN.add(BigDecimal.TEN));

        // Create the baseRT CPDecisionBAROutput (necessary for the non-base room type value to be applied
        CPDecisionBAROutput baseRoomType = addCPDecisionBarOutput(startDate, RT_DOUBLE, new BigDecimal("100"));

        // Create the non-BaseRT CPDecisionBAROutput (note that the optimal bar should be derived off the baseRT finalBar - supplement)
        CPDecisionBAROutput nonBaseRoomType = addCPDecisionBarOutput(startDate, RT_QUEEN, new BigDecimal("125"));

        service.roundOptimalBARs(startDate, endDate);

        baseRoomType = getCPDecisionBarOutputFor(baseRoomType.getId());
        nonBaseRoomType = getCPDecisionBarOutputFor(nonBaseRoomType.getId());
        assertBigDecimalEquals(new BigDecimal("100.00"), baseRoomType.getOptimalBAR());
        assertBigDecimalEquals(new BigDecimal("110.00"), baseRoomType.getPrettyBAR());
        assertBigDecimalEquals(new BigDecimal("100.00"), baseRoomType.getRoomsOnlyBAR());
        assertBigDecimalEquals(new BigDecimal("110.00"), baseRoomType.getFinalBAR());

        // Note - this looks odd but the non-base room type's optimalBAR remains what we receive from analytics
        assertBigDecimalEquals(new BigDecimal("125.00"), nonBaseRoomType.getOptimalBAR());
        assertBigDecimalEquals(new BigDecimal("145.00"), nonBaseRoomType.getPrettyBAR());
        assertBigDecimalEquals(new BigDecimal("125.00"), nonBaseRoomType.getRoomsOnlyBAR());
        assertBigDecimalEquals(new BigDecimal("145.00"), nonBaseRoomType.getFinalBAR());
    }

    @Test
    public void roundOptimalBARsNonBaseRTUsesBaseRTOptimalBar() {
        // Set the Base RT floor/ceiling
        addTransientPricingBaseAccomType(RT_DOUBLE, BigDecimal.TEN, new BigDecimal("1000"), BigDecimal.ZERO);

        // Add the QUEEN single offset
        addCPConfigOffsetAccomType(RT_QUEEN, OccupancyType.SINGLE, OffsetMethod.PERCENTAGE, new BigDecimal("25"));

        // Add supplements
        addAccomTypeSupplement(RT_DOUBLE, OccupancyType.SINGLE, BigDecimal.TEN);
        addAccomTypeSupplement(RT_QUEEN, OccupancyType.SINGLE, BigDecimal.TEN.add(BigDecimal.TEN));

        // Create the baseRT CPDecisionBAROutput (necessary for the non-base room type value to be applied
        CPDecisionBAROutput baseRoomType = addCPDecisionBarOutput(startDate, RT_DOUBLE, new BigDecimal("100"));

        // Create the non-BaseRT CPDecisionBAROutput (note that the optimal bar should be derived off the baseRT optimalBar)
        // The optimal bar is being initially set to 1 to prove its being calculated off of the base room type and offset
        CPDecisionBAROutput nonBaseRoomType = addCPDecisionBarOutput(startDate, RT_QUEEN, new BigDecimal("1"));

        service.roundOptimalBARs(startDate, endDate);

        baseRoomType = getCPDecisionBarOutputFor(baseRoomType.getId());
        nonBaseRoomType = getCPDecisionBarOutputFor(nonBaseRoomType.getId());
        assertBigDecimalEquals(new BigDecimal("100.00"), baseRoomType.getOptimalBAR());
        assertBigDecimalEquals(new BigDecimal("110.00"), baseRoomType.getPrettyBAR());
        assertBigDecimalEquals(new BigDecimal("100.00"), baseRoomType.getRoomsOnlyBAR());
        assertBigDecimalEquals(new BigDecimal("110.00"), baseRoomType.getFinalBAR());

        // Note - this looks odd but the non-base room type's optimalBAR remains what we receive from analytics
        assertBigDecimalEquals(new BigDecimal("125.00"), nonBaseRoomType.getOptimalBAR());
        assertBigDecimalEquals(new BigDecimal("145.00"), nonBaseRoomType.getPrettyBAR());
        assertBigDecimalEquals(new BigDecimal("125.00"), nonBaseRoomType.getRoomsOnlyBAR());
        assertBigDecimalEquals(new BigDecimal("145.00"), nonBaseRoomType.getFinalBAR());
    }

    @Test
    public void roundOptimalBARsDoesNotChangeFinalBARDueToMinimumIncrement() {
        // Set a minimum increment value that won't change the decision
        updatePricingAccomClassData(RT_DOUBLE, MinimumIncrementMethod.FIXED_OFFSET, new BigDecimal("20"), false);

        // Set a Pricing Rule
        setRoundingRule(9, 8, 7);

        // Add Floor/Ceiling
        addTransientPricingBaseAccomType(RT_DOUBLE, BigDecimal.TEN, new BigDecimal("100"), BigDecimal.ZERO);

        addDecisionDailybarOutput(RT_DOUBLE, startDate, new BigDecimal("30.00"), new BigDecimal("30.00"), BigDecimal.ZERO, BigDecimal.ZERO);

        CPDecisionBAROutput cpDecisionBAROutput = addCPDecisionBarOutput(startDate, RT_DOUBLE, new BigDecimal("20.00"));

        service.roundOptimalBARs(startDate, endDate);

        cpDecisionBAROutput = getCPDecisionBarOutputFor(cpDecisionBAROutput.getId());
        assertBigDecimalEquals(new BigDecimal("19.87"), cpDecisionBAROutput.getPrettyBAR());
        assertBigDecimalEquals(new BigDecimal("30.00"), BigDecimalUtil.round(cpDecisionBAROutput.getRoomsOnlyBAR(), 2));
        assertBigDecimalEquals(new BigDecimal("30.00"), BigDecimalUtil.round(cpDecisionBAROutput.getFinalBAR(), 2));
        assertBigDecimalEquals(new BigDecimal("30.00"), BigDecimalUtil.round(cpDecisionBAROutput.getPreviousBAR(), 2));
    }

    @Test
    public void roundOptimalBARsDoubleDoesChangeFinalBAR() {
        // Set a minimum increment value that won't change the decision
        updatePricingAccomClassData(RT_DOUBLE, MinimumIncrementMethod.FIXED_OFFSET, new BigDecimal("10"), false);

        // Set a Pricing Rule
        setRoundingRule(9, 8, 7);

        // Add Floor/Ceiling
        addTransientPricingBaseAccomType(RT_DOUBLE, BigDecimal.TEN, new BigDecimal("100"), BigDecimal.ZERO);

        addDecisionDailybarOutput(RT_DOUBLE, startDate, new BigDecimal("30.00"), new BigDecimal("5.00"), BigDecimal.ZERO, BigDecimal.ZERO);

        CPDecisionBAROutput cpDecisionBAROutput = addCPDecisionBarOutput(startDate, RT_DOUBLE, new BigDecimal("35.00"));

        service.roundOptimalBARs(startDate, endDate);

        cpDecisionBAROutput = getCPDecisionBarOutputFor(cpDecisionBAROutput.getId());
        assertBigDecimalEquals(new BigDecimal("39.87"), cpDecisionBAROutput.getPrettyBAR());
        assertBigDecimalEquals(new BigDecimal("39.87"), BigDecimalUtil.round(cpDecisionBAROutput.getRoomsOnlyBAR(), 2));
        assertBigDecimalEquals(new BigDecimal("39.87"), BigDecimalUtil.round(cpDecisionBAROutput.getFinalBAR(), 2));
        assertBigDecimalEquals(new BigDecimal("30.00"), BigDecimalUtil.round(cpDecisionBAROutput.getPreviousBAR(), 2));
    }

    @Test
    public void roundOptimalBARsNoRoundingRulesWithSupplement() {
        // Add Floor/Ceiling
        addTransientPricingBaseAccomType(RT_DOUBLE, BigDecimal.TEN, new BigDecimal("100"), BigDecimal.ZERO);

        // Add supplement
        addAccomTypeSupplement(RT_DOUBLE, OccupancyType.SINGLE, BigDecimal.TEN);

        CPDecisionBAROutput cpDecisionBAROutput = addCPDecisionBarOutput(startDate, RT_DOUBLE, new BigDecimal("20.00"));

        service.roundOptimalBARs(startDate, endDate);

        cpDecisionBAROutput = getCPDecisionBarOutputFor(cpDecisionBAROutput.getId());
        assertBigDecimalEquals(new BigDecimal("30.00"), cpDecisionBAROutput.getPrettyBAR());
        assertBigDecimalEquals(new BigDecimal("20.00"), cpDecisionBAROutput.getRoomsOnlyBAR());
        assertBigDecimalEquals(new BigDecimal("30.00"), cpDecisionBAROutput.getFinalBAR());
    }

    @Test
    public void roundOptimalBARsNoRoundingRulesWithSupplementsWithSupplementsDisabled() {
        CPConfiguration cpConfiguration = tenantCrudService().findOne(CPConfiguration.class);
        cpConfiguration.setEnableSupplements(false);
        tenantCrudService().save(cpConfiguration);

        // Add Floor/Ceiling
        addTransientPricingBaseAccomType(RT_DOUBLE, BigDecimal.TEN, new BigDecimal("100"), BigDecimal.ZERO);

        // Add supplement
        addAccomTypeSupplement(RT_DOUBLE, OccupancyType.SINGLE, BigDecimal.TEN);

        CPDecisionBAROutput cpDecisionBAROutput = addCPDecisionBarOutput(startDate, RT_DOUBLE, new BigDecimal("20.00"));

        service.roundOptimalBARs(startDate, endDate);

        cpDecisionBAROutput = getCPDecisionBarOutputFor(cpDecisionBAROutput.getId());
        assertBigDecimalEquals(new BigDecimal("20.00"), cpDecisionBAROutput.getPrettyBAR());
        assertBigDecimalEquals(new BigDecimal("20.00"), cpDecisionBAROutput.getRoomsOnlyBAR());
        assertBigDecimalEquals(new BigDecimal("20.00"), cpDecisionBAROutput.getFinalBAR());
    }

    @Test
    public void roundOptimalBARsWithRoundingRulesWithSupplement() {
        // Set a rounding rule
        setRoundingRule(9, 8, 7);

        // Sets floor/ceiling
        addTransientPricingBaseAccomType(RT_DOUBLE, BigDecimal.TEN, new BigDecimal("100"), BigDecimal.ZERO);

        // Add supplement
        addAccomTypeSupplement(RT_DOUBLE, OccupancyType.SINGLE, BigDecimal.TEN);

        CPDecisionBAROutput cpDecisionBAROutput = addCPDecisionBarOutput(startDate, RT_DOUBLE, new BigDecimal("20.00"));

        service.roundOptimalBARs(startDate, endDate);

        cpDecisionBAROutput = getCPDecisionBarOutputFor(cpDecisionBAROutput.getId());
        assertBigDecimalEquals(new BigDecimal("29.87"), cpDecisionBAROutput.getPrettyBAR());
        assertBigDecimalEquals(new BigDecimal("19.87"), cpDecisionBAROutput.getRoomsOnlyBAR());
        assertBigDecimalEquals(new BigDecimal("29.87"), cpDecisionBAROutput.getFinalBAR());
    }

    @Test
    public void roundOptimalBARsUsesFloorPrice() {
        // Sets floor/ceiling
        addTransientPricingBaseAccomType(RT_DOUBLE, BigDecimal.TEN, new BigDecimal("100"), BigDecimal.ZERO);

        CPDecisionBAROutput cpDecisionBAROutput = addCPDecisionBarOutput(startDate, RT_DOUBLE, BigDecimal.ONE);

        service.roundOptimalBARs(startDate, endDate);

        cpDecisionBAROutput = getCPDecisionBarOutputFor(cpDecisionBAROutput.getId());
        assertBigDecimalEquals(new BigDecimal("10.00"), cpDecisionBAROutput.getPrettyBAR());
        assertBigDecimalEquals(new BigDecimal("10.00"), cpDecisionBAROutput.getRoomsOnlyBAR());
        assertBigDecimalEquals(new BigDecimal("10.00"), cpDecisionBAROutput.getFinalBAR());
    }

    @Test
    public void roundOptimalBARsUsesFloorPriceAndAppliesSupplement() {
        // Sets floor/ceiling
        addTransientPricingBaseAccomType(RT_DOUBLE, BigDecimal.TEN, new BigDecimal("100"), BigDecimal.ZERO);

        // Add supplement
        addAccomTypeSupplement(RT_DOUBLE, OccupancyType.SINGLE, BigDecimal.TEN);

        CPDecisionBAROutput cpDecisionBAROutput = addCPDecisionBarOutput(startDate, RT_DOUBLE, BigDecimal.ONE);

        service.roundOptimalBARs(startDate, endDate);

        cpDecisionBAROutput = getCPDecisionBarOutputFor(cpDecisionBAROutput.getId());
        assertBigDecimalEquals(new BigDecimal("11.00"), cpDecisionBAROutput.getPrettyBAR());
        assertBigDecimalEquals(new BigDecimal("1.00"), cpDecisionBAROutput.getRoomsOnlyBAR());
        assertBigDecimalEquals(new BigDecimal("11.00"), cpDecisionBAROutput.getFinalBAR());
    }

    @Test
    public void roundOptimalBARsUsesCeilingPrice() {
        // Sets floor/ceiling
        addTransientPricingBaseAccomType(RT_DOUBLE, BigDecimal.TEN, new BigDecimal("100"), BigDecimal.ZERO);

        CPDecisionBAROutput cpDecisionBAROutput = addCPDecisionBarOutput(startDate, RT_DOUBLE, new BigDecimal("1000"));

        service.roundOptimalBARs(startDate, endDate);

        cpDecisionBAROutput = getCPDecisionBarOutputFor(cpDecisionBAROutput.getId());
        assertBigDecimalEquals(new BigDecimal("100.00"), cpDecisionBAROutput.getPrettyBAR());
        assertBigDecimalEquals(new BigDecimal("100.00"), cpDecisionBAROutput.getRoomsOnlyBAR());
        assertBigDecimalEquals(new BigDecimal("100.00"), cpDecisionBAROutput.getFinalBAR());
    }

    @Test
    public void roundOptimalBARsUsesCeilingPriceAndAppliesSupplement() {
        // Sets floor/ceiling
        addTransientPricingBaseAccomType(RT_DOUBLE, BigDecimal.TEN, new BigDecimal("100"), BigDecimal.ZERO);

        // Add supplement
        addAccomTypeSupplement(RT_DOUBLE, OccupancyType.SINGLE, BigDecimal.TEN);

        CPDecisionBAROutput cpDecisionBAROutput = addCPDecisionBarOutput(startDate, RT_DOUBLE, new BigDecimal("1000"));

        service.roundOptimalBARs(startDate, endDate);

        cpDecisionBAROutput = getCPDecisionBarOutputFor(cpDecisionBAROutput.getId());
        assertBigDecimalEquals(new BigDecimal("100.00"), cpDecisionBAROutput.getPrettyBAR());
        assertBigDecimalEquals(new BigDecimal("90.00"), cpDecisionBAROutput.getRoomsOnlyBAR());
        assertBigDecimalEquals(new BigDecimal("100.00"), cpDecisionBAROutput.getFinalBAR());
    }

    @Test
    public void roundOptimalBARsUserSpecificOverrideDoesNotRoundOrReapplySupplement() {
        // Set a Rounding Rule
        setRoundingRule(1, 1, 1);

        // Sets floor/ceiling
        addTransientPricingBaseAccomType(RT_DOUBLE, BigDecimal.TEN, new BigDecimal("100"), BigDecimal.ZERO);

        // Add supplement
        addAccomTypeSupplement(RT_DOUBLE, OccupancyType.SINGLE, BigDecimal.TEN);

        CPDecisionBAROutput cpDecisionBAROutput = addCPDecisionBarOutput(startDate, RT_DOUBLE, new BigDecimal("69.99"));
        // Since the roundOptimalBARs method gets called immediately after optimization, the specific override value
        // would not include the supplement here.
        cpDecisionBAROutput.setSpecificOverride(new BigDecimal("69.99"));
        cpDecisionBAROutput = tenantCrudService().save(cpDecisionBAROutput);
        tenantCrudService().flushAndClear();

        service.roundOptimalBARs(startDate, endDate);

        cpDecisionBAROutput = getCPDecisionBarOutputFor(cpDecisionBAROutput.getId());
        assertBigDecimalEquals(new BigDecimal("79.99"), cpDecisionBAROutput.getPrettyBAR());
        assertBigDecimalEquals(new BigDecimal("69.99"), cpDecisionBAROutput.getRoomsOnlyBAR());
        assertBigDecimalEquals(new BigDecimal("79.99"), cpDecisionBAROutput.getFinalBAR());

        assertEquals(new BigDecimal("79.99"), cpDecisionBAROutput.getSpecificOverride());
        assertNull(cpDecisionBAROutput.getFloorOverride());
        assertNull(cpDecisionBAROutput.getCeilingOverride());
    }

    @Test
    public void roundOptimalBARsOverrideFloorPriceDoesNotRoundOrReapplySupplement() {
        addTransientPricingBaseAccomType(RT_DOUBLE, BigDecimal.TEN, new BigDecimal("100"), BigDecimal.ZERO);

        addAccomTypeSupplement(RT_DOUBLE, OccupancyType.SINGLE, BigDecimal.TEN);

        CPDecisionBAROutput cpDecisionBAROutput = addCPDecisionBarOutput(startDate, RT_DOUBLE, new BigDecimal("190.00"));
        // Since the roundOptimalBARs method gets called immediately after optimization, the floor override value
        // would not include the supplement here.
        cpDecisionBAROutput.setFloorOverride(new BigDecimal("190.00"));
        cpDecisionBAROutput = tenantCrudService().save(cpDecisionBAROutput);

        service.roundOptimalBARs(startDate, endDate);

        cpDecisionBAROutput = getCPDecisionBarOutputFor(cpDecisionBAROutput.getId());
        assertBigDecimalEquals(new BigDecimal("200.00"), cpDecisionBAROutput.getPrettyBAR());
        assertBigDecimalEquals(new BigDecimal("190.00"), cpDecisionBAROutput.getRoomsOnlyBAR());
        assertBigDecimalEquals(new BigDecimal("200.00"), cpDecisionBAROutput.getFinalBAR());

        assertNull(cpDecisionBAROutput.getSpecificOverride());
        assertBigDecimalEquals(new BigDecimal("200.00"), cpDecisionBAROutput.getFloorOverride());
        assertNull(cpDecisionBAROutput.getCeilingOverride());
    }

    @Test
    public void roundOptimalBARsOverrideCeilingPriceDoesNotRoundOrReapplySupplement() {
        addTransientPricingBaseAccomType(RT_DOUBLE, new BigDecimal("20"), new BigDecimal("100"), BigDecimal.ZERO);

        addAccomTypeSupplement(RT_DOUBLE, OccupancyType.SINGLE, BigDecimal.TEN);

        CPDecisionBAROutput cpDecisionBAROutput = addCPDecisionBarOutput(startDate, RT_DOUBLE, new BigDecimal("50"));
        // Since the roundOptimalBARs method gets called immediately after optimization, the ceiling override value
        // would not include the supplement here.
        cpDecisionBAROutput.setCeilingOverride(new BigDecimal("5.00"));
        cpDecisionBAROutput = tenantCrudService().save(cpDecisionBAROutput);

        service.roundOptimalBARs(startDate, endDate);

        cpDecisionBAROutput = getCPDecisionBarOutputFor(cpDecisionBAROutput.getId());
        assertBigDecimalEquals(new BigDecimal("15.00"), cpDecisionBAROutput.getPrettyBAR());
        assertBigDecimalEquals(new BigDecimal("5.00"), cpDecisionBAROutput.getRoomsOnlyBAR());
        assertBigDecimalEquals(new BigDecimal("15.00"), cpDecisionBAROutput.getFinalBAR());

        assertNull(cpDecisionBAROutput.getSpecificOverride());
        assertNull(cpDecisionBAROutput.getFloorOverride());
        assertBigDecimalEquals(new BigDecimal("15.00"), cpDecisionBAROutput.getCeilingOverride());
    }

    @Test
    public void roundOptimalBARsCeilingPriceRoundingBringsBelowAndSupplementBringsItAbove() {
        setRoundingRule(9, 9, 9);

        addTransientPricingBaseAccomType(RT_DOUBLE, BigDecimal.TEN, new BigDecimal("100"), BigDecimal.ZERO);

        addAccomTypeSupplement(RT_DOUBLE, OccupancyType.SINGLE, BigDecimal.TEN);

        CPDecisionBAROutput cpDecisionBAROutput = addCPDecisionBarOutput(startDate, RT_DOUBLE, new BigDecimal("100.01"));

        service.roundOptimalBARs(startDate, endDate);

        cpDecisionBAROutput = getCPDecisionBarOutputFor(cpDecisionBAROutput.getId());
        assertBigDecimalEquals(new BigDecimal("99.99"), cpDecisionBAROutput.getPrettyBAR());
        assertBigDecimalEquals(new BigDecimal("89.99"), cpDecisionBAROutput.getRoomsOnlyBAR());
        assertBigDecimalEquals(new BigDecimal("99.99"), cpDecisionBAROutput.getFinalBAR());
    }

    @Test
    public void roundOptimalBARsCeilingPriceForNonBaseRT_CP_CALCULATE_BAR_NON_BASE_RT_OPTIMAL_BAR_DISABLED() {
        setRoundingRule(9, 8, 7);

        // Set the Base RT floor/ceiling
        addTransientPricingBaseAccomType(RT_DOUBLE, BigDecimal.TEN, new BigDecimal("100"), BigDecimal.ZERO);

        // Add the QUEEN single offset
        addCPConfigOffsetAccomType(RT_QUEEN, OccupancyType.SINGLE, OffsetMethod.PERCENTAGE, new BigDecimal("25"));

        // Create the baseRT CPDecisionBAROutput (necessary for the non-base room type value to be applied
        CPDecisionBAROutput baseRoomType = addCPDecisionBarOutput(startDate, RT_DOUBLE, new BigDecimal("100"));

        // Create the non-BaseRT CPDecisionBAROutput (note that the optimal bar is going to be replaced due to occupancy type rounding)
        CPDecisionBAROutput nonBaseRoomType = addCPDecisionBarOutput(startDate, RT_QUEEN, new BigDecimal("125"));

        service.roundOptimalBARs(startDate, endDate);

        baseRoomType = getCPDecisionBarOutputFor(baseRoomType.getId());
        nonBaseRoomType = getCPDecisionBarOutputFor(nonBaseRoomType.getId());

        verifyBarValuesFor(baseRoomType, "100.00000", "99.87");
        // OptimalBAR stays the same
        // Note - this looks odd but the non-base room type will always get rounded hence why it is so important for
        // users to do overrides that match their rounding rules.
        verifyBarValuesFor(nonBaseRoomType, "125.00000", "119.87");
    }

    @Test
    public void roundOptimalBARsCeilingPriceForNonBaseRT_CP_CALCULATE_BAR_NON_BASE_RT_OPTIMAL_BAR_ENABLED() {
        setRoundingRule(9, 8, 7);

        // Set the Base RT floor/ceiling
        addTransientPricingBaseAccomType(RT_DOUBLE, BigDecimal.TEN, new BigDecimal("100"), BigDecimal.ZERO);

        // Add the QUEEN single offset
        addCPConfigOffsetAccomType(RT_QUEEN, OccupancyType.SINGLE, OffsetMethod.PERCENTAGE, new BigDecimal("25"));

        // Create the baseRT CPDecisionBAROutput (necessary for the non-base room type value to be applied
        CPDecisionBAROutput baseRoomType = addCPDecisionBarOutput(startDate, RT_DOUBLE, new BigDecimal("100"));

        // Create the non-BaseRT CPDecisionBAROutput (note that the optimal bar is going to be replaced due to occupancy type rounding)
        // The optimal bar is being initially set to 1 to prove its being calculated off of the base room type and offset
        CPDecisionBAROutput nonBaseRoomType = addCPDecisionBarOutput(startDate, RT_QUEEN, new BigDecimal("1"));

        service.roundOptimalBARs(startDate, endDate);

        baseRoomType = getCPDecisionBarOutputFor(baseRoomType.getId());
        nonBaseRoomType = getCPDecisionBarOutputFor(nonBaseRoomType.getId());

        verifyBarValuesFor(baseRoomType, "100.00000", "99.87");
        // OptimalBAR changes
        // Note - this looks odd but the non-base room type will always get rounded hence why it is so important for
        // users to do overrides that match their rounding rules.
        verifyBarValuesFor(nonBaseRoomType, "125.00000", "119.87");
    }

    @Test
    public void roundOptimalBARsCeilingPriceForBaseRTRestricted() {
        setRoundingRule(null, null, null);

        // Set the Base RT floor/ceiling
        addTransientPricingBaseAccomType(RT_DOUBLE, BigDecimal.TEN, new BigDecimal("1000"), BigDecimal.ZERO);

        // Create the baseRT CPDecisionBAROutput (necessary for the non-base room type value to be applied
        CPDecisionBAROutput baseRoomType = addCPDecisionBarOutput(startDate, RT_DOUBLE, new BigDecimal("199.999999999997"));
        baseRoomType.setCeilingOverride(new BigDecimal("199.999999999997"));
        baseRoomType.setOverrideType(DecisionOverrideType.CEIL);
        tenantCrudService().save(baseRoomType);

        service.roundOptimalBARs(startDate, endDate);

        baseRoomType = getCPDecisionBarOutputFor(baseRoomType.getId());
        verifyBarValuesFor(baseRoomType, "200.00000", "200.00");
    }

    @Test
    public void roundOptimalBARsRoundsCeilingPriceForNonBaseRTAppliesSupplement_CP_CALCULATE_BAR_NON_BASE_RT_OPTIMAL_BAR_DISABLED() {
        setRoundingRule(9, 8, 7);

        // Set the Base RT floor/ceiling
        addTransientPricingBaseAccomType(RT_DOUBLE, BigDecimal.TEN, new BigDecimal("100"), BigDecimal.ZERO);

        // Add the QUEEN single offset
        addCPConfigOffsetAccomType(RT_QUEEN, OccupancyType.SINGLE, OffsetMethod.PERCENTAGE, new BigDecimal("25"));

        // Add a supplement for room type Queen
        addAccomTypeSupplement(RT_QUEEN, OccupancyType.SINGLE, BigDecimal.TEN);

        // Create the baseRT CPDecisionBAROutput (necessary for the non-base room type value to be applied
        CPDecisionBAROutput baseRoomType = addCPDecisionBarOutput(startDate, RT_DOUBLE, new BigDecimal("90.00"));

        // Create the non-BaseRT CPDecisionBAROutput (note that the optimal bar is going to be replaced due to occupancy type rounding)
        CPDecisionBAROutput nonBaseRoomType = addCPDecisionBarOutput(startDate, RT_QUEEN, new BigDecimal("112.50"));

        service.roundOptimalBARs(startDate, endDate);

        baseRoomType = getCPDecisionBarOutputFor(baseRoomType.getId());
        nonBaseRoomType = getCPDecisionBarOutputFor(nonBaseRoomType.getId());
        verifyBarValuesFor(baseRoomType, "90.00000", "89.87");

        // OptimalBAR stays the same
        // Note - this looks odd but the non-base room type will always get rounded hence why it is so important for
        // users to do overrides that match their rounding rules.
        assertBigDecimalEquals(new BigDecimal("112.50"), nonBaseRoomType.getOptimalBAR());
        assertBigDecimalEquals(new BigDecimal("119.87"), nonBaseRoomType.getPrettyBAR());
        assertBigDecimalEquals(new BigDecimal("109.87"), nonBaseRoomType.getRoomsOnlyBAR());
        assertBigDecimalEquals(new BigDecimal("119.87"), nonBaseRoomType.getFinalBAR());
    }

    @Test
    public void roundOptimalBARsRoundsCeilingPriceForNonBaseRTAppliesSupplement_CP_CALCULATE_BAR_NON_BASE_RT_OPTIMAL_BAR_ENABLED() {
        setRoundingRule(9, 8, 7);

        // Set the Base RT floor/ceiling
        addTransientPricingBaseAccomType(RT_DOUBLE, BigDecimal.TEN, new BigDecimal("100"), BigDecimal.ZERO);

        // Add the QUEEN single offset
        addCPConfigOffsetAccomType(RT_QUEEN, OccupancyType.SINGLE, OffsetMethod.PERCENTAGE, new BigDecimal("25"));

        // Add a supplement for room type Queen
        addAccomTypeSupplement(RT_QUEEN, OccupancyType.SINGLE, BigDecimal.TEN);

        // Create the baseRT CPDecisionBAROutput (necessary for the non-base room type value to be applied
        CPDecisionBAROutput baseRoomType = addCPDecisionBarOutput(startDate, RT_DOUBLE, new BigDecimal("90.00"));

        // Create the non-BaseRT CPDecisionBAROutput (note that the optimal bar is going to be replaced due to occupancy type rounding)
        // The optimal bar is being initially set to 1 to prove its being calculated off of the base room type and offset
        CPDecisionBAROutput nonBaseRoomType = addCPDecisionBarOutput(startDate, RT_QUEEN, new BigDecimal("1"));

        service.roundOptimalBARs(startDate, endDate);

        baseRoomType = getCPDecisionBarOutputFor(baseRoomType.getId());
        nonBaseRoomType = getCPDecisionBarOutputFor(nonBaseRoomType.getId());
        verifyBarValuesFor(baseRoomType, "90.00000", "89.87");

        // OptimalBAR changes
        // Note - this looks odd but the non-base room type will always get rounded hence why it is so important for
        // users to do overrides that match their rounding rules.
        assertBigDecimalEquals(new BigDecimal("112.50"), nonBaseRoomType.getOptimalBAR());
        assertBigDecimalEquals(new BigDecimal("119.87"), nonBaseRoomType.getPrettyBAR());
        assertBigDecimalEquals(new BigDecimal("109.87"), nonBaseRoomType.getRoomsOnlyBAR());
        assertBigDecimalEquals(new BigDecimal("119.87"), nonBaseRoomType.getFinalBAR());
    }

    @Test
    public void roundOptimalBARs_priceExcludedRoomClassWithSupplementIgnoresFloorCeilingAndRounding() {
        setPriceExcluded(RT_DOUBLE);

        addTransientPricingBaseAccomType(RT_DOUBLE, BigDecimal.TEN, new BigDecimal("100"), BigDecimal.ZERO);

        setRoundingRule(9, 9, 9);

        addAccomTypeSupplement(RT_DOUBLE, OccupancyType.SINGLE, BigDecimal.TEN);

        CPDecisionBAROutput cpDecisionBAROutput = addCPDecisionBarOutput(startDate, RT_DOUBLE, new BigDecimal("90.00"));

        service.roundOptimalBARs(startDate, endDate);

        cpDecisionBAROutput = getCPDecisionBarOutputFor(cpDecisionBAROutput.getId());
        assertBigDecimalEquals(new BigDecimal("90.00"), cpDecisionBAROutput.getOptimalBAR());
        assertBigDecimalEquals(new BigDecimal("100.00"), cpDecisionBAROutput.getPrettyBAR());
        assertBigDecimalEquals(new BigDecimal("90.00"), cpDecisionBAROutput.getRoomsOnlyBAR());
        assertBigDecimalEquals(new BigDecimal("100.00"), cpDecisionBAROutput.getFinalBAR());
    }

    @Test
    public void floorAndCeilingOverrideForNonBaseRTIncorrectlyIdentifyingValueChangeDueToSupplement() {
        AccomType tr = buildAccomType(6, 6, "TR");
        AccomType sp = buildAccomType(6, 4, "SP");
        AccomType sr = buildAccomType(6, 5, "SR");

        tenantCrudService().save(tr);
        tenantCrudService().save(sp);
        tenantCrudService().save(sr);
        tenantCrudService().flushAndClear();

        setTax(new BigDecimal("12.0"));
        setRoundingRule(null, 0, 0);
        updatePricingAccomClassData("TR", MinimumIncrementMethod.FIXED_OFFSET, new BigDecimal("20.00"), false);

        addTransientPricingBaseAccomType("TR", new BigDecimal("613.75"), new BigDecimal("1283.39"), BigDecimal.ZERO);

        addOffset("TR", null, null, OccupancyType.DOUBLE, OffsetMethod.FIXED_OFFSET, new BigDecimal("131.61"));
        addOffset("TR", null, null, OccupancyType.EXTRA_ADULT, OffsetMethod.FIXED_OFFSET, new BigDecimal("131.61"));
        addOffset("SP", null, null, OccupancyType.DOUBLE, OffsetMethod.FIXED_OFFSET, new BigDecimal("131.61"));
        addOffset("SP", null, null, OccupancyType.EXTRA_ADULT, OffsetMethod.FIXED_OFFSET, new BigDecimal("131.61"));

        addSupplement("TR", OccupancyType.SINGLE, new BigDecimal("52.60"));
        addSupplement("TR", OccupancyType.DOUBLE, new BigDecimal("105.20"));
        addSupplement("TR", OccupancyType.EXTRA_ADULT, new BigDecimal("52.60"));
        addSupplement("SP", OccupancyType.SINGLE, new BigDecimal("52.60"));
        addSupplement("SP", OccupancyType.DOUBLE, new BigDecimal("0.00"));
        addSupplement("SP", OccupancyType.EXTRA_ADULT, new BigDecimal("52.60"));
        addSupplement("SR", OccupancyType.SINGLE, new BigDecimal("52.60"));
        addSupplement("SR", OccupancyType.DOUBLE, new BigDecimal("105.20"));
        addSupplement("SR", OccupancyType.EXTRA_ADULT, new BigDecimal("52.60"));

        addDecisionDailybarOutput("TR", startDate, new BigDecimal("900.00"), new BigDecimal("1100.00"), new BigDecimal("200.00"), new BigDecimal("0.00"));
        addDecisionDailybarOutput("SP", startDate, new BigDecimal("900.00"), new BigDecimal("995.00"), new BigDecimal("200.00"), new BigDecimal("0.00"));
        addDecisionDailybarOutput("SR", startDate, new BigDecimal("900.00"), new BigDecimal("953.00"), new BigDecimal("52.60"), new BigDecimal("0.00"));

        addCPDecisionBAROutput("TR", startDate, new BigDecimal("850.27"), null, null, new BigDecimal("888.40"), new BigDecimal("847.40"));
        addCPDecisionBAROutput("SP", startDate, new BigDecimal("850.27"), null, null, null, null);
        addCPDecisionBAROutput("SR", startDate, new BigDecimal("850.27"), null, null, null, null);

        service.roundOptimalBARs(startDate, endDate);

        tenantCrudService().findAll(CPDecisionBAROutput.class).forEach(output -> {
            assertEquals(new BigDecimal("900.00"), output.getFinalBAR().setScale(2, RoundingMode.HALF_UP));
            assertEquals(new BigDecimal("900.00"), output.getPreviousBAR().setScale(2, RoundingMode.HALF_UP));
            tenantCrudService().findAll(CPPaceDecisionBAROutputDifferential.class).forEach(paceOutput -> {
                assertEquals(new BigDecimal("900.00"), paceOutput.getFinalBAR().setScale(2, RoundingMode.HALF_UP));
                // floor/ceiling override only present on the base rt
                if (paceOutput.getAccomType().equals(tr)) {
                    assertEquals(new BigDecimal("900.00"), paceOutput.getFloorOverride().setScale(2, RoundingMode.HALF_UP));
                    assertEquals(new BigDecimal("941.00"), paceOutput.getCeilingOverride().setScale(2, RoundingMode.HALF_UP));
                }
            });
        });
    }

    @Test
    public void roundOptimalBARs_priceExcludedBaseAndNonBaseRoomClass() {
        setTax(new BigDecimal("12.0"));
        setRoundingRule(null, 9, 9);

        setPriceExcluded(RT_DOUBLE);
        setPriceExcluded(RT_QUEEN);

        // Set the Base RT floor/ceiling
        addTransientPricingBaseAccomType(RT_DOUBLE, BigDecimal.TEN, BigDecimal.TEN, BigDecimal.ZERO);

        // Add the QUEEN single offset
        addCPConfigOffsetAccomType(RT_QUEEN, OccupancyType.SINGLE, OffsetMethod.FIXED_PRICE, new BigDecimal("11.00"));

        // Add supplements
        addAccomTypeSupplement(RT_DOUBLE, OccupancyType.SINGLE, BigDecimal.TEN);
        addAccomTypeSupplement(RT_QUEEN, OccupancyType.SINGLE, BigDecimal.TEN);

        // Create the baseRT CPDecisionBAROutput
        CPDecisionBAROutput baseRoomType = addCPDecisionBarOutput(startDate, RT_DOUBLE, new BigDecimal("10"));

        // Create the non-baseRT CPDecisionBAROutput - note that the optimalBAR doesn't matter
        CPDecisionBAROutput nonBaseRoomType = addCPDecisionBarOutput(startDate, RT_QUEEN, new BigDecimal("11"));

        service.roundOptimalBARs(startDate, endDate);

        baseRoomType = getCPDecisionBarOutputFor(baseRoomType.getId());
        nonBaseRoomType = getCPDecisionBarOutputFor(nonBaseRoomType.getId());

        assertBigDecimalEquals(new BigDecimal("10.00"), baseRoomType.getOptimalBAR());
        assertBigDecimalEquals(new BigDecimal("20.00"), baseRoomType.getPrettyBAR());
        assertBigDecimalEquals(new BigDecimal("10.00"), baseRoomType.getRoomsOnlyBAR());
        assertBigDecimalEquals(new BigDecimal("20.00"), baseRoomType.getFinalBAR());

        // Note - this looks odd but the non-base room type's optimalBAR remains what we receive from analytics
        assertBigDecimalEquals(new BigDecimal("11.00"), nonBaseRoomType.getOptimalBAR());
        assertBigDecimalEquals(new BigDecimal("12.32"), nonBaseRoomType.getPrettyBAR());
        assertBigDecimalEquals(new BigDecimal("2.32"), nonBaseRoomType.getRoomsOnlyBAR());
        assertBigDecimalEquals(new BigDecimal("12.32"), nonBaseRoomType.getFinalBAR());
    }

    @Test
    public void roundOptimalBARsRoundsFloorPriceEqualsNonBaseRT() {

        setRoundingRule(null, 0, 0);
        setTax(new BigDecimal("12.00"));

        // Set the Base RT floor/ceiling
        addTransientPricingBaseAccomType(RT_DOUBLE, new BigDecimal("89.30"), new BigDecimal("535.71"), BigDecimal.ZERO);

        // Create the baseRT CPDecisionBAROutput (necessary for the non-base room type value to be applied
        CPDecisionBAROutput baseRoomType = addCPDecisionBarOutput(startDate, RT_DOUBLE, new BigDecimal("110.0064"));

        service.roundOptimalBARs(startDate, endDate);

        baseRoomType = getCPDecisionBarOutputFor(baseRoomType.getId());

        verifyBarValuesFor(baseRoomType, "110.00640", "110.00");
    }

    @Test
    public void roundOptimalBARsCeilingPriceForNonBaseRTWithTaxWhenCeilingValueIsLessThanOptimalBar_CP_CALCULATE_BAR_NON_BASE_RT_OPTIMAL_BAR_DISABLED() {
        setTax(BigDecimal.TEN);
        addTransientPricingBaseAccomType(RT_DOUBLE, BigDecimal.TEN, new BigDecimal("100"), BigDecimal.ZERO);
        addCPConfigOffsetAccomType(RT_QUEEN, OccupancyType.SINGLE, OffsetMethod.FIXED_OFFSET, new BigDecimal("25"));
        CPDecisionBAROutput baseRoomType = addCPDecisionBarOutput(startDate, RT_DOUBLE, new BigDecimal("120"));
        CPDecisionBAROutput nonBaseRoomType = addCPDecisionBarOutput(startDate, RT_QUEEN, new BigDecimal("147.50"));

        service.roundOptimalBARs(startDate, endDate);

        baseRoomType = getCPDecisionBarOutputFor(baseRoomType.getId());
        nonBaseRoomType = getCPDecisionBarOutputFor(nonBaseRoomType.getId());
        verifyBarValuesFor(baseRoomType, "120.00000", "110.00");
        verifyBarValuesFor(nonBaseRoomType, "147.50000", "137.50");
    }

    @Test
    public void roundOptimalBARsCeilingPriceForNonBaseRTWithTaxWhenCeilingValueIsLessThanOptimalBar_CP_CALCULATE_BAR_NON_BASE_RT_OPTIMAL_BAR_ENABLED() {
        setTax(BigDecimal.TEN);
        addTransientPricingBaseAccomType(RT_DOUBLE, BigDecimal.TEN, new BigDecimal("100"), BigDecimal.ZERO);
        addCPConfigOffsetAccomType(RT_QUEEN, OccupancyType.SINGLE, OffsetMethod.FIXED_OFFSET, new BigDecimal("25"));
        CPDecisionBAROutput baseRoomType = addCPDecisionBarOutput(startDate, RT_DOUBLE, new BigDecimal("120"));
        //The optimal bar is being initially set to 1 to prove its being calculated off of the base room type and offset
        CPDecisionBAROutput nonBaseRoomType = addCPDecisionBarOutput(startDate, RT_QUEEN, new BigDecimal("1"));

        service.roundOptimalBARs(startDate, endDate);

        baseRoomType = getCPDecisionBarOutputFor(baseRoomType.getId());
        nonBaseRoomType = getCPDecisionBarOutputFor(nonBaseRoomType.getId());
        verifyBarValuesFor(baseRoomType, "120.00000", "110.00");
        verifyBarValuesFor(nonBaseRoomType, "147.50000", "137.50");
    }

    private void verifyBarValuesFor(CPDecisionBAROutput roomType, String optimalBar, String finaBar) {
        assertBigDecimalEquals(new BigDecimal(optimalBar), roomType.getOptimalBAR());
        assertBigDecimalEquals(new BigDecimal(finaBar), roomType.getPrettyBAR());
        assertBigDecimalEquals(new BigDecimal(finaBar), roomType.getRoomsOnlyBAR());
        assertBigDecimalEquals(new BigDecimal(finaBar), roomType.getFinalBAR());
    }

    @Test
    public void optimalBARsCeilingPriceForNonBaseRTWithTaxWhenOverridenCeilingValueIsLessThanOptimalBar_CP_CALCULATE_BAR_NON_BASE_RT_OPTIMAL_BAR_DISABLED() {
        setTax(BigDecimal.TEN);
        addTransientPricingBaseAccomType(RT_DOUBLE, BigDecimal.TEN, new BigDecimal("1000"), BigDecimal.ZERO);
        addCPConfigOffsetAccomType(RT_QUEEN, OccupancyType.SINGLE, OffsetMethod.FIXED_OFFSET, new BigDecimal("25"));
        CPDecisionBAROutput baseRoomType = addCPDecisionBarOutput(startDate, RT_DOUBLE, new BigDecimal("120"));
        CPDecisionBAROutput nonBaseRoomType = addCPDecisionBarOutput(startDate, RT_QUEEN, new BigDecimal("147.50"));
        setCeilingOverride(baseRoomType, new BigDecimal("100"));

        service.roundOptimalBARs(startDate, endDate);

        baseRoomType = getCPDecisionBarOutputFor(baseRoomType.getId());
        nonBaseRoomType = getCPDecisionBarOutputFor(nonBaseRoomType.getId());
        verifyBarValuesFor(baseRoomType, "120.00000", "100.00");
        verifyBarValuesFor(nonBaseRoomType, "147.50000", "127.50");
    }

    @Test
    public void optimalBARsCeilingPriceForNonBaseRTWithTaxWhenOverridenCeilingValueIsLessThanOptimalBar_CP_CALCULATE_BAR_NON_BASE_RT_OPTIMAL_BAR_ENABLED() {
        setTax(BigDecimal.TEN);
        addTransientPricingBaseAccomType(RT_DOUBLE, BigDecimal.TEN, new BigDecimal("1000"), BigDecimal.ZERO);
        addCPConfigOffsetAccomType(RT_QUEEN, OccupancyType.SINGLE, OffsetMethod.FIXED_OFFSET, new BigDecimal("25"));
        CPDecisionBAROutput baseRoomType = addCPDecisionBarOutput(startDate, RT_DOUBLE, new BigDecimal("120"));
        //The optimal bar is being initially set to 1 to prove its being calculated off of the base room type and offset
        CPDecisionBAROutput nonBaseRoomType = addCPDecisionBarOutput(startDate, RT_QUEEN, new BigDecimal("1"));
        setCeilingOverride(baseRoomType, new BigDecimal("100"));

        service.roundOptimalBARs(startDate, endDate);

        baseRoomType = getCPDecisionBarOutputFor(baseRoomType.getId());
        nonBaseRoomType = getCPDecisionBarOutputFor(nonBaseRoomType.getId());
        verifyBarValuesFor(baseRoomType, "120.00000", "100.00");
        verifyBarValuesFor(nonBaseRoomType, "147.50000", "127.50");
    }

    @Test
    public void roundOptimalBARsCeilingPriceForNonBaseRTWithTaxWhenOverridenCeilingValueIsLessThanOptimalBar_CP_CALCULATE_BAR_NON_BASE_RT_OPTIMAL_BAR_DISABLED() {
        setRoundingRule(9, 8, 7);
        setTax(BigDecimal.TEN);
        addTransientPricingBaseAccomType(RT_DOUBLE, BigDecimal.TEN, new BigDecimal("1000"), BigDecimal.ZERO);
        addCPConfigOffsetAccomType(RT_QUEEN, OccupancyType.SINGLE, OffsetMethod.FIXED_OFFSET, new BigDecimal("25"));
        CPDecisionBAROutput baseRoomType = addCPDecisionBarOutput(startDate, RT_DOUBLE, new BigDecimal("120"));
        CPDecisionBAROutput nonBaseRoomType = addCPDecisionBarOutput(startDate, RT_QUEEN, new BigDecimal("147.50"));
        setCeilingOverride(baseRoomType, new BigDecimal("99.87"));

        service.roundOptimalBARs(startDate, endDate);

        baseRoomType = getCPDecisionBarOutputFor(baseRoomType.getId());
        nonBaseRoomType = getCPDecisionBarOutputFor(nonBaseRoomType.getId());
        verifyBarValuesFor(baseRoomType, "120.00000", "99.87");
        //We cannot go above ceiling+offset for nonbase room type, so we round down
        verifyBarValuesFor(nonBaseRoomType, "147.50000", "119.87");
    }

    @Test
    public void roundOptimalBARsCeilingPriceForNonBaseRTWithTaxWhenOverridenCeilingValueIsLessThanOptimalBar_CP_CALCULATE_BAR_NON_BASE_RT_OPTIMAL_BAR_ENABLED() {
        setRoundingRule(9, 8, 7);
        setTax(BigDecimal.TEN);
        addTransientPricingBaseAccomType(RT_DOUBLE, BigDecimal.TEN, new BigDecimal("1000"), BigDecimal.ZERO);
        addCPConfigOffsetAccomType(RT_QUEEN, OccupancyType.SINGLE, OffsetMethod.FIXED_OFFSET, new BigDecimal("25"));
        CPDecisionBAROutput baseRoomType = addCPDecisionBarOutput(startDate, RT_DOUBLE, new BigDecimal("120"));
        //The optimal bar is being initially set to 1 to prove its being calculated off of the base room type and offset
        CPDecisionBAROutput nonBaseRoomType = addCPDecisionBarOutput(startDate, RT_QUEEN, new BigDecimal("1"));
        setCeilingOverride(baseRoomType, new BigDecimal("99.87"));

        service.roundOptimalBARs(startDate, endDate);

        baseRoomType = getCPDecisionBarOutputFor(baseRoomType.getId());
        nonBaseRoomType = getCPDecisionBarOutputFor(nonBaseRoomType.getId());
        verifyBarValuesFor(baseRoomType, "120.00000", "99.87");
        //We cannot go above ceiling+offset for nonbase room type, so we round down
        verifyBarValuesFor(nonBaseRoomType, "147.50000", "119.87");
    }

    private void setCeilingOverride(CPDecisionBAROutput decisionBAROutput, BigDecimal overrideValue) {
        Product product = new Product();
        product.setId(1);
        CPDecisionBAROutputOverride result = CPDecisionBAROutputOverride
                .ceilingOverride(6, decisionBAROutput.getDecisionId(), decisionBAROutput.getAccomType(), startDate, -1, 1, BigDecimal.ONE, overrideValue, product);
        result.setCreateDate(new Date());
        result.setOldOverrideType(DecisionOverrideType.NONE);
        tenantCrudService().save(result);
    }

    private void setFloorAndCeilingOverride(CPDecisionBAROutput decisionBAROutput, BigDecimal floorOverride, BigDecimal ceilingOverride) {
        Product product = new Product();
        product.setId(1);
        CPDecisionBAROutputOverride result = CPDecisionBAROutputOverride
                .ceilingOverride(6, decisionBAROutput.getDecisionId(), decisionBAROutput.getAccomType(), startDate, -1, 1, BigDecimal.ONE, floorOverride, product);
        result.setNewCeilingRate(ceilingOverride);
        result.setNewFloorRate(floorOverride);
        result.setNewOverrideType(DecisionOverrideType.FLOORANDCEIL);
        result.setCreateDate(new Date());
        result.setOldOverrideType(DecisionOverrideType.NONE);
        tenantCrudService().save(result);
    }

    private void setFloorOverride(CPDecisionBAROutput decisionBAROutput, BigDecimal overrideValue) {
        Product product = new Product();
        product.setId(1);
        CPDecisionBAROutputOverride result = CPDecisionBAROutputOverride
                .floorOverride(6, decisionBAROutput.getDecisionId(), decisionBAROutput.getAccomType(), startDate, -1, 1, BigDecimal.ONE, overrideValue, product);
        result.setCreateDate(new Date());
        result.setOldOverrideType(DecisionOverrideType.NONE);
        tenantCrudService().save(result);
    }

    private void setSpecificOverride(CPDecisionBAROutput decisionBAROutput, BigDecimal overrideValue) {
        Product product = new Product();
        product.setId(1);
        CPDecisionBAROutputOverride result = CPDecisionBAROutputOverride
                .specificOverride(6, decisionBAROutput.getDecisionId(), decisionBAROutput.getAccomType(), startDate, -1, 1, BigDecimal.ONE, overrideValue, product);
        result.setCreateDate(new Date());
        result.setOldOverrideType(DecisionOverrideType.NONE);
        tenantCrudService().save(result);
    }


    @Test
    public void roundOptimalBARsCeilingPriceForNonBaseRTWithTaxWhenOverridenFloorValueIsGreatorThanOptimalBar_CP_CALCULATE_BAR_NON_BASE_RT_OPTIMAL_BAR_ENABLED() {
        setTax(BigDecimal.TEN);
        addTransientPricingBaseAccomType(RT_DOUBLE, BigDecimal.ONE, new BigDecimal("1000"), BigDecimal.ZERO);
        addCPConfigOffsetAccomType(RT_QUEEN, OccupancyType.SINGLE, OffsetMethod.FIXED_OFFSET, new BigDecimal("25"));
        CPDecisionBAROutput baseRoomType = addCPDecisionBarOutput(startDate, RT_DOUBLE, BigDecimal.TEN);
        // The optimal bar is being initially set to 1 to prove its being calculated off of the base room type and offset
        CPDecisionBAROutput nonBaseRoomType = addCPDecisionBarOutput(startDate, RT_QUEEN, BigDecimal.valueOf(1));
        saveFloorOverrideFor(baseRoomType);

        service.roundOptimalBARs(startDate, endDate);

        baseRoomType = getCPDecisionBarOutputFor(baseRoomType.getId());
        nonBaseRoomType = getCPDecisionBarOutputFor(nonBaseRoomType.getId());
        verifyBarValuesFor(baseRoomType, "10.00000", "100.00");
        verifyBarValuesFor(nonBaseRoomType, "37.50000", "127.50");
    }

    private void saveFloorOverrideFor(CPDecisionBAROutput cpDecisionBAROutput) {
        Product product = new Product();
        product.setId(1);
        CPDecisionBAROutputOverride result = CPDecisionBAROutputOverride
                .floorOverride(6, cpDecisionBAROutput.getDecisionId(), cpDecisionBAROutput.getAccomType(), startDate, -1, 1, BigDecimal.ONE, new BigDecimal("100"), product);
        result.setCreateDate(new Date());
        result.setOldOverrideType(DecisionOverrideType.NONE);
        tenantCrudService().save(result);
    }

    @Test
    public void roundOptimalBARsCeilingPriceForNonBaseRTWithTaxWhenOverridenCeilingValueIsLessThanOptimalBarWithPercentageOffsetValue_CP_CALCULATE_BAR_NON_BASE_RT_OPTIMAL_BAR_ENABLED() {
        setTax(BigDecimal.TEN);
        addTransientPricingBaseAccomType(RT_DOUBLE, BigDecimal.TEN, new BigDecimal("1000"), BigDecimal.ZERO);
        addCPConfigOffsetAccomType(RT_QUEEN, OccupancyType.SINGLE, OffsetMethod.PERCENTAGE, new BigDecimal("25"));
        CPDecisionBAROutput baseRoomType = addCPDecisionBarOutput(startDate, RT_DOUBLE, new BigDecimal("1000"));
        // The optimal bar is being initially set to 1 to prove its being calculated off of the base room type and offset
        CPDecisionBAROutput nonBaseRoomType = addCPDecisionBarOutput(startDate, RT_QUEEN, new BigDecimal("1"));
        setCeilingOverride(baseRoomType, new BigDecimal("100"));

        service.roundOptimalBARs(startDate, endDate);

        baseRoomType = getCPDecisionBarOutputFor(baseRoomType.getId());
        nonBaseRoomType = getCPDecisionBarOutputFor(nonBaseRoomType.getId());
        verifyBarValuesFor(baseRoomType, "1000.00000", "100.00");
        verifyBarValuesFor(nonBaseRoomType, "1275.00000", "127.50");
    }

    @Test
    public void roundOptimalBARsNonBaseRTUsesBaseRTOptimalBarButNotWhenOverridenOnBaseOnly() {
        // Set the Base RT floor/ceiling
        addTransientPricingBaseAccomType(RT_DOUBLE, BigDecimal.TEN, new BigDecimal("1000"), BigDecimal.ZERO);

        // Add the QUEEN single offset
        addCPConfigOffsetAccomType(RT_QUEEN, OccupancyType.SINGLE, OffsetMethod.PERCENTAGE, new BigDecimal("25"));

        // Create the baseRT CPDecisionBAROutput (necessary for the non-base room type value to be applied
        CPDecisionBAROutput baseRoomType = addCPDecisionBarOutput(startDate, RT_DOUBLE, new BigDecimal("125"));
        baseRoomType.setSpecificOverride(new BigDecimal("125.00"));
        baseRoomType = tenantCrudService().save(baseRoomType);

        // Create the non-BaseRT CPDecisionBAROutput (note that the optimal bar should be derived off the baseRT optimalBar)
        CPDecisionBAROutput nonBaseRoomType = addCPDecisionBarOutput(startDate, RT_QUEEN, new BigDecimal("80"));

        service.roundOptimalBARs(startDate, endDate);

        baseRoomType = getCPDecisionBarOutputFor(baseRoomType.getId());
        nonBaseRoomType = getCPDecisionBarOutputFor(nonBaseRoomType.getId());
        assertBigDecimalEquals(new BigDecimal("125.00"), baseRoomType.getOptimalBAR());
        assertBigDecimalEquals(new BigDecimal("125.00"), baseRoomType.getPrettyBAR());
        assertBigDecimalEquals(new BigDecimal("125.00"), baseRoomType.getRoomsOnlyBAR());
        assertBigDecimalEquals(new BigDecimal("125.00"), baseRoomType.getFinalBAR());

        // Note - this looks odd but the non-base room type's optimalBAR remains what we receive from analytics
        assertBigDecimalEquals(new BigDecimal("80.00"), nonBaseRoomType.getOptimalBAR());
        assertBigDecimalEquals(new BigDecimal("80.00"), nonBaseRoomType.getPrettyBAR());
        assertBigDecimalEquals(new BigDecimal("80.00"), nonBaseRoomType.getRoomsOnlyBAR());
        assertBigDecimalEquals(new BigDecimal("80.00"), nonBaseRoomType.getFinalBAR());
    }

    @Test
    public void roundOptimalBARsTaxInclusiveOnAndCeilingFloorAreSetWithLessDifferenceAndSupplementIsMoreThanDifference() {
        setTax(new BigDecimal("10.0"));
        //eventually it will store wTaxCeiling = (78 * 1.10)  +10 = 95.8 and wTaxFloor = 92.5
        addTransientPricingBaseAccomType(RT_DOUBLE, new BigDecimal("75"), new BigDecimal("78"), BigDecimal.TEN);
        addSupplement(RT_DOUBLE, OccupancyType.SINGLE, BigDecimal.TEN);
        CPDecisionBAROutput cpDecisionBAROutput = addCPDecisionBarOutput(startDate, RT_DOUBLE, new BigDecimal("250.00"));

        service.roundOptimalBARs(startDate, endDate);

        cpDecisionBAROutput = getCPDecisionBarOutputFor(cpDecisionBAROutput.getId());
        assertBigDecimalEquals(new BigDecimal("95.8").setScale(2, RoundingMode.HALF_UP), cpDecisionBAROutput.getPrettyBAR());
        assertBigDecimalEquals(new BigDecimal("85.8").setScale(2, RoundingMode.HALF_UP), cpDecisionBAROutput.getRoomsOnlyBAR());
        assertBigDecimalEquals(new BigDecimal("95.8").setScale(2, RoundingMode.HALF_UP), cpDecisionBAROutput.getFinalBAR());
    }

    @Test
    public void roundOptimalBARsTaxInclusiveOnAndUsesFloorOverrideToCalculatePrettyBar() {
        setTax(new BigDecimal("10.0"));
        addTransientPricingBaseAccomType(RT_DOUBLE, new BigDecimal("30"), new BigDecimal("355"), BigDecimal.TEN);
        addSupplement(RT_DOUBLE, OccupancyType.SINGLE, BigDecimal.TEN);
        CPDecisionBAROutput cpDecisionBAROutput = addCPDecisionBarOutput(startDate, RT_DOUBLE, new BigDecimal("50.00"));
        cpDecisionBAROutput.setFloorOverride(BigDecimal.valueOf(89));//service adds the supplement
        cpDecisionBAROutput.setOverrideType(DecisionOverrideType.FLOOR);
        tenantCrudService().save(cpDecisionBAROutput);
        tenantCrudService().flushAndClear();
        setFloorOverride(cpDecisionBAROutput, BigDecimal.valueOf(89));


        service.roundOptimalBARs(startDate, endDate);

        cpDecisionBAROutput = getCPDecisionBarOutputFor(cpDecisionBAROutput.getId());
        assertBigDecimalEquals(new BigDecimal("99.00").setScale(2, RoundingMode.HALF_UP), cpDecisionBAROutput.getPrettyBAR().setScale(2, RoundingMode.HALF_UP));
        assertBigDecimalEquals(new BigDecimal("89.00").setScale(2, RoundingMode.HALF_UP), cpDecisionBAROutput.getRoomsOnlyBAR().setScale(2, RoundingMode.HALF_UP));
        assertBigDecimalEquals(new BigDecimal("99.00").setScale(2, RoundingMode.HALF_UP), cpDecisionBAROutput.getFinalBAR().setScale(2, RoundingMode.HALF_UP));
        assertBigDecimalEquals(new BigDecimal("99.00").setScale(2, RoundingMode.HALF_UP), cpDecisionBAROutput.getFloorOverride());
        assertBigDecimalEquals(null, cpDecisionBAROutput.getCeilingOverride());
        assertBigDecimalEquals(null, cpDecisionBAROutput.getSpecificOverride());

    }

    @Test
    public void roundOptimalBARsTaxInclusiveOnAndUsesCeilingOverrideToCalculatePrettyBar() {
        setTax(new BigDecimal("10.0"));
        addTransientPricingBaseAccomType(RT_DOUBLE, new BigDecimal("30"), new BigDecimal("355"), BigDecimal.TEN);
        addSupplement(RT_DOUBLE, OccupancyType.SINGLE, BigDecimal.TEN);
        CPDecisionBAROutput cpDecisionBAROutput = addCPDecisionBarOutput(startDate, RT_DOUBLE, new BigDecimal("150.00"));
        cpDecisionBAROutput.setCeilingOverride(BigDecimal.valueOf(89));//service adds the supplement
        cpDecisionBAROutput.setOverrideType(DecisionOverrideType.CEIL);
        tenantCrudService().save(cpDecisionBAROutput);
        tenantCrudService().flushAndClear();
        setCeilingOverride(cpDecisionBAROutput, BigDecimal.valueOf(89));


        service.roundOptimalBARs(startDate, endDate);

        cpDecisionBAROutput = getCPDecisionBarOutputFor(cpDecisionBAROutput.getId());
        assertBigDecimalEquals(new BigDecimal("99.00").setScale(2, RoundingMode.HALF_UP), cpDecisionBAROutput.getPrettyBAR().setScale(2, RoundingMode.HALF_UP));
        assertBigDecimalEquals(new BigDecimal("89.00").setScale(2, RoundingMode.HALF_UP), cpDecisionBAROutput.getRoomsOnlyBAR().setScale(2, RoundingMode.HALF_UP));
        assertBigDecimalEquals(new BigDecimal("99.00").setScale(2, RoundingMode.HALF_UP), cpDecisionBAROutput.getFinalBAR().setScale(2, RoundingMode.HALF_UP));
        assertBigDecimalEquals(new BigDecimal("99.00").setScale(2, RoundingMode.HALF_UP), cpDecisionBAROutput.getCeilingOverride());
        assertBigDecimalEquals(null, cpDecisionBAROutput.getSpecificOverride());
        assertBigDecimalEquals(null, cpDecisionBAROutput.getFloorOverride());

    }


    @Test
    public void roundOptimalBARsTaxInclusiveOnAndUsesSpecificOverrideToCalculatePrettyBar() {
        setTax(new BigDecimal("10.0"));
        addTransientPricingBaseAccomType(RT_DOUBLE, new BigDecimal("30"), new BigDecimal("355"), BigDecimal.TEN);
        addSupplement(RT_DOUBLE, OccupancyType.SINGLE, BigDecimal.TEN);
        CPDecisionBAROutput cpDecisionBAROutput = addCPDecisionBarOutput(startDate, RT_DOUBLE, new BigDecimal("150.00"));
        cpDecisionBAROutput.setSpecificOverride(BigDecimal.valueOf(89));//service adds the supplement
        cpDecisionBAROutput.setOverrideType(DecisionOverrideType.USER);
        tenantCrudService().save(cpDecisionBAROutput);
        tenantCrudService().flushAndClear();
        setSpecificOverride(cpDecisionBAROutput, BigDecimal.valueOf(89));


        service.roundOptimalBARs(startDate, endDate);

        cpDecisionBAROutput = getCPDecisionBarOutputFor(cpDecisionBAROutput.getId());
        assertBigDecimalEquals(new BigDecimal("99.00").setScale(2, RoundingMode.HALF_UP), cpDecisionBAROutput.getPrettyBAR().setScale(2, RoundingMode.HALF_UP));
        assertBigDecimalEquals(new BigDecimal("89.00").setScale(2, RoundingMode.HALF_UP), cpDecisionBAROutput.getRoomsOnlyBAR().setScale(2, RoundingMode.HALF_UP));
        assertBigDecimalEquals(new BigDecimal("99.00").setScale(2, RoundingMode.HALF_UP), cpDecisionBAROutput.getFinalBAR().setScale(2, RoundingMode.HALF_UP));
        assertBigDecimalEquals(new BigDecimal("99.00").setScale(2, RoundingMode.HALF_UP), cpDecisionBAROutput.getSpecificOverride());
        assertBigDecimalEquals(null, cpDecisionBAROutput.getCeilingOverride());
        assertBigDecimalEquals(null, cpDecisionBAROutput.getFloorOverride());
    }


    @Test
    public void roundOptimalBARsTaxInclusiveOnThenCalculatePrettyBarForPriceExcludedBaseAndNonBaseRoomTypes() {
        setPriceExcluded(RT_DOUBLE);
        setPriceExcluded(RT_QUEEN);
        setTax(BigDecimal.TEN);
        // Add the DOUBLE Set price
        addTransientPricingBaseAccomType(RT_DOUBLE, new BigDecimal("1115.00"), new BigDecimal("1115.00"), BigDecimal.TEN);
        // Add the QUEEN single offset as FixedPrice(Set Value)
        addSetOffsetForPriceExcluded(RT_QUEEN, OffsetMethod.FIXED_PRICE, new BigDecimal("1300.00"), BigDecimal.TEN);
        // Add supplements
        addAccomTypeSupplement(RT_DOUBLE, OccupancyType.SINGLE, BigDecimal.TEN);
        addAccomTypeSupplement(RT_QUEEN, OccupancyType.SINGLE, BigDecimal.TEN);
        // Create the baseRT CPDecisionBAROutput
        CPDecisionBAROutput baseRoomType = addCPDecisionBarOutput(startDate, RT_DOUBLE, new BigDecimal("1215.5"));
        CPDecisionBAROutput nonBaseRoomType = addCPDecisionBarOutput(startDate, RT_QUEEN, new BigDecimal("1300"));

        service.roundOptimalBARs(startDate, endDate);

        baseRoomType = getCPDecisionBarOutputFor(baseRoomType.getId());
        nonBaseRoomType = getCPDecisionBarOutputFor(nonBaseRoomType.getId());
        assertBigDecimalEquals(new BigDecimal("1215.50"), baseRoomType.getOptimalBAR());
        assertBigDecimalEquals(new BigDecimal("1225.50"), baseRoomType.getPrettyBAR());
        assertBigDecimalEquals(new BigDecimal("1215.50"), baseRoomType.getRoomsOnlyBAR());
        assertBigDecimalEquals(new BigDecimal("1225.50"), baseRoomType.getFinalBAR());

        // Note - this looks odd but the non-base room type's optimalBAR remains what we receive from analytics
        assertBigDecimalEquals(new BigDecimal("1300"), nonBaseRoomType.getOptimalBAR());
        assertBigDecimalEquals(new BigDecimal("1310"), nonBaseRoomType.getPrettyBAR());
        assertBigDecimalEquals(new BigDecimal("1300"), nonBaseRoomType.getRoomsOnlyBAR());
        assertBigDecimalEquals(new BigDecimal("1310"), nonBaseRoomType.getFinalBAR());
    }

    @Test
    public void roundOptimalBARsTaxInclusiveOffAndSupplementsAreDifferentForBaseAndNonBase() {
        setTax(new BigDecimal("10.0"));
        //eventually it will store wTaxCeiling = (78 * 1.10)  +10 = 86.8 and wTaxFloor = 83.5
        addTransientPricingBaseAccomType(RT_DOUBLE, new BigDecimal("75"), new BigDecimal("78"), BigDecimal.ONE);
        addSupplement(RT_DOUBLE, OccupancyType.SINGLE, BigDecimal.ONE);
        addSupplement(RT_QUEEN, OccupancyType.SINGLE, BigDecimal.TEN);
        CPDecisionBAROutput cpDecisionBAROutput = addCPDecisionBarOutput(startDate, RT_DOUBLE, new BigDecimal("250.00"));
        CPDecisionBAROutput cpDecisionBAROutput2 = addCPDecisionBarOutput(startDate, RT_QUEEN, new BigDecimal("255.00"));

        service.roundOptimalBARs(startDate, endDate);

        cpDecisionBAROutput = getCPDecisionBarOutputFor(cpDecisionBAROutput.getId());
        assertBigDecimalEquals(new BigDecimal("86.8").setScale(2, RoundingMode.HALF_UP), cpDecisionBAROutput.getPrettyBAR());
        assertBigDecimalEquals(new BigDecimal("85.8").setScale(2, RoundingMode.HALF_UP), cpDecisionBAROutput.getRoomsOnlyBAR());
        assertBigDecimalEquals(new BigDecimal("86.8").setScale(2, RoundingMode.HALF_UP), cpDecisionBAROutput.getFinalBAR());

        cpDecisionBAROutput2 = getCPDecisionBarOutputFor(cpDecisionBAROutput2.getId());
        assertBigDecimalEquals(new BigDecimal("95.8").setScale(2, RoundingMode.HALF_UP), cpDecisionBAROutput2.getPrettyBAR());
        assertBigDecimalEquals(new BigDecimal("85.8").setScale(2, RoundingMode.HALF_UP), cpDecisionBAROutput2.getRoomsOnlyBAR());
        assertBigDecimalEquals(new BigDecimal("95.8").setScale(2, RoundingMode.HALF_UP), cpDecisionBAROutput2.getFinalBAR());
    }

    @Test
    public void roundOptimalBARsTaxInclusiveOnAndSupplementsAreDifferentForBaseAndNonBase() {
        setTax(new BigDecimal("10.0"));
        addTransientPricingBaseAccomType(RT_DOUBLE, new BigDecimal("75"), new BigDecimal("78"), BigDecimal.TEN);
        addSupplement(RT_DOUBLE, OccupancyType.SINGLE, BigDecimal.TEN);
        addSupplement(RT_QUEEN, OccupancyType.SINGLE, BigDecimal.valueOf(18.0));
        CPDecisionBAROutput cpDecisionBAROutput = addCPDecisionBarOutput(startDate, RT_DOUBLE, new BigDecimal("250.00"));
        CPDecisionBAROutput cpDecisionBAROutput2 = addCPDecisionBarOutput(startDate, RT_QUEEN, new BigDecimal("255.00"));

        service.roundOptimalBARs(startDate, endDate);

        cpDecisionBAROutput = getCPDecisionBarOutputFor(cpDecisionBAROutput.getId());
        assertBigDecimalEquals(new BigDecimal("95.8").setScale(2, RoundingMode.HALF_UP), cpDecisionBAROutput.getPrettyBAR());
        assertBigDecimalEquals(new BigDecimal("85.8").setScale(2, RoundingMode.HALF_UP), cpDecisionBAROutput.getRoomsOnlyBAR());
        assertBigDecimalEquals(new BigDecimal("95.8").setScale(2, RoundingMode.HALF_UP), cpDecisionBAROutput.getFinalBAR());

        cpDecisionBAROutput2 = getCPDecisionBarOutputFor(cpDecisionBAROutput2.getId());
        assertBigDecimalEquals(new BigDecimal("103.8").setScale(2, RoundingMode.HALF_UP), cpDecisionBAROutput2.getPrettyBAR());
        assertBigDecimalEquals(new BigDecimal("85.8").setScale(2, RoundingMode.HALF_UP), cpDecisionBAROutput2.getRoomsOnlyBAR());
        assertBigDecimalEquals(new BigDecimal("103.8").setScale(2, RoundingMode.HALF_UP), cpDecisionBAROutput2.getFinalBAR());
    }

    @Test
    public void roundOptimalBARsTaxInclusiveOnAndSupplementsAreDifferentForBaseAndNonBaseAndOptimalBarIsWithinCeilingFloor() {
        verifyPrettyBarAndFinalBar(BigDecimal.ONE, BigDecimal.TEN, new BigDecimal("251"), new BigDecimal("260"));
    }

    @Test
    public void roundOptimalBARsTaxInclusiveOnAndSupplementsAreDifferentForBaseAndNonBaseAndOptimalBarIsWithinCeilingFloor_2() {
        verifyPrettyBarAndFinalBar(BigDecimal.ZERO, BigDecimal.TEN, new BigDecimal("250"), new BigDecimal("260"));
    }

    @Test
    public void roundOptimalBARsTaxInclusiveOnAndSupplementsAreDifferentForBaseAndNonBaseAndOptimalBarIsWithinCeilingFloor_3() {
        verifyPrettyBarAndFinalBar(BigDecimal.TEN, BigDecimal.ONE, new BigDecimal("260"), new BigDecimal("251"));
    }

    @Test
    public void roundOptimalBARsTaxInclusiveOnAndSupplementsAreDifferentForBaseAndNonBaseAndOptimalBarIsWithinCeilingFloor_4() {
        verifyPrettyBarAndFinalBar(null, BigDecimal.TEN, new BigDecimal("250"), new BigDecimal("260"));
    }

    private void verifyPrettyBarAndFinalBar(BigDecimal baseRTSupplementSingleOcc, BigDecimal nonBaseRTSingleOccSupplement, BigDecimal prettyBarForBaseRT, BigDecimal prettyBarForNonBaseRT) {
        setTax(new BigDecimal("10.0"));
        //eventually it will store wTaxCeiling = (780 * 1.10)  +1 = 859 and wTaxFloor = 166
        addTransientPricingBaseAccomType(RT_DOUBLE, new BigDecimal("150"), new BigDecimal("780"), baseRTSupplementSingleOcc);
        addSupplement(RT_DOUBLE, OccupancyType.SINGLE, baseRTSupplementSingleOcc);
        addSupplement(RT_QUEEN, OccupancyType.SINGLE, nonBaseRTSingleOccSupplement);
        CPDecisionBAROutput cpDecisionBAROutput = addCPDecisionBarOutput(startDate, RT_DOUBLE, new BigDecimal("250.00"));
        CPDecisionBAROutput cpDecisionBAROutput2 = addCPDecisionBarOutput(startDate, RT_QUEEN, new BigDecimal("260.00"));

        service.roundOptimalBARs(startDate, endDate);

        cpDecisionBAROutput = getCPDecisionBarOutputFor(cpDecisionBAROutput.getId());
        assertBigDecimalEquals(prettyBarForBaseRT.setScale(2, RoundingMode.HALF_UP), cpDecisionBAROutput.getPrettyBAR());
        assertBigDecimalEquals(BigDecimalUtil.subtract(prettyBarForBaseRT, baseRTSupplementSingleOcc).setScale(2, RoundingMode.HALF_UP), cpDecisionBAROutput.getRoomsOnlyBAR());
        assertBigDecimalEquals(prettyBarForBaseRT.setScale(2, RoundingMode.HALF_UP), cpDecisionBAROutput.getFinalBAR());

        cpDecisionBAROutput2 = getCPDecisionBarOutputFor(cpDecisionBAROutput2.getId());
        assertBigDecimalEquals(prettyBarForNonBaseRT.setScale(2, RoundingMode.HALF_UP), cpDecisionBAROutput2.getPrettyBAR());
        assertBigDecimalEquals(BigDecimalUtil.subtract(prettyBarForNonBaseRT, nonBaseRTSingleOccSupplement).setScale(2, RoundingMode.HALF_UP), cpDecisionBAROutput2.getRoomsOnlyBAR());
        assertBigDecimalEquals(prettyBarForNonBaseRT.setScale(2, RoundingMode.HALF_UP), cpDecisionBAROutput2.getFinalBAR());
    }

    @Test
    public void roundOptimalBARsTaxInclusiveOnAndSupplementsAreSameAndOffsetsAreConfiguredForNonBase() {
        setTax(new BigDecimal("10.0"));
        addTransientPricingBaseAccomType(RT_DOUBLE, new BigDecimal("75"), new BigDecimal("78"), BigDecimal.ONE);
        addSupplement(RT_DOUBLE, OccupancyType.SINGLE, BigDecimal.ONE);
        addSupplement(RT_QUEEN, OccupancyType.SINGLE, BigDecimal.ONE);

        //add 5 as fixed offset. since tax inclusive adding 4.545 after adding tax it would be 4.995
        addCPConfigOffsetAccomType(RT_QUEEN, OccupancyType.SINGLE, OffsetMethod.FIXED_OFFSET, BigDecimal.valueOf(4.545));

        CPDecisionBAROutput cpDecisionBAROutput = addCPDecisionBarOutput(startDate, RT_DOUBLE, new BigDecimal("82.5"));
        CPDecisionBAROutput cpDecisionBAROutput2 = addCPDecisionBarOutput(startDate, RT_QUEEN, new BigDecimal("87.495"));

        service.roundOptimalBARs(startDate, endDate);

        cpDecisionBAROutput = getCPDecisionBarOutputFor(cpDecisionBAROutput.getId());
        assertBigDecimalEquals(new BigDecimal("83.5").setScale(2, RoundingMode.HALF_UP), cpDecisionBAROutput.getPrettyBAR());
        assertBigDecimalEquals(new BigDecimal("82.5").setScale(2, RoundingMode.HALF_UP), cpDecisionBAROutput.getRoomsOnlyBAR());
        assertBigDecimalEquals(new BigDecimal("83.5").setScale(2, RoundingMode.HALF_UP), cpDecisionBAROutput.getFinalBAR());

        cpDecisionBAROutput2 = getCPDecisionBarOutputFor(cpDecisionBAROutput2.getId());
        assertBigDecimalEquals(new BigDecimal("88.495").setScale(2, RoundingMode.HALF_UP), cpDecisionBAROutput2.getPrettyBAR());
        assertBigDecimalEquals(new BigDecimal("87.495").setScale(2, RoundingMode.HALF_UP), cpDecisionBAROutput2.getRoomsOnlyBAR());
        assertBigDecimalEquals(new BigDecimal("88.495").setScale(2, RoundingMode.HALF_UP), cpDecisionBAROutput2.getFinalBAR());
    }

    @Test
    public void roundOptimalBARsTaxInclusiveOnAndSupplementsAreSameAndMoreThanDifferenceOfCeilingFloorAndOffsetsAreConfiguredForNonBase() {
        when(pacmanConfigParamsService.getParameterValue(FeatureTogglesConfigParamName.PER_PERSON_PRICING_ENABLED)).thenReturn(false);
        setTax(new BigDecimal("10.0"));
        addTransientPricingBaseAccomType(RT_DOUBLE, new BigDecimal("75"), new BigDecimal("78"), BigDecimal.TEN);
        addSupplement(RT_DOUBLE, OccupancyType.SINGLE, BigDecimal.TEN);
        addSupplement(RT_QUEEN, OccupancyType.SINGLE, BigDecimal.TEN);

        //add 5 as fixed offset. since tax inclusive adding 4.545 after adding tax it would be 4.995
        addCPConfigOffsetAccomType(RT_QUEEN, OccupancyType.SINGLE, OffsetMethod.FIXED_OFFSET, BigDecimal.valueOf(4.545));

        CPDecisionBAROutput cpDecisionBAROutput = addCPDecisionBarOutput(startDate, RT_DOUBLE, new BigDecimal("82.5"));
        CPDecisionBAROutput cpDecisionBAROutput2 = addCPDecisionBarOutput(startDate, RT_QUEEN, new BigDecimal("87.495"));

        service.roundOptimalBARs(startDate, endDate);

        cpDecisionBAROutput = getCPDecisionBarOutputFor(cpDecisionBAROutput.getId());
        assertBigDecimalEquals(new BigDecimal("92.5").setScale(2, RoundingMode.HALF_UP), cpDecisionBAROutput.getPrettyBAR());
        assertBigDecimalEquals(new BigDecimal("82.5").setScale(2, RoundingMode.HALF_UP), cpDecisionBAROutput.getRoomsOnlyBAR());
        assertBigDecimalEquals(new BigDecimal("92.5").setScale(2, RoundingMode.HALF_UP), cpDecisionBAROutput.getFinalBAR());

        cpDecisionBAROutput2 = getCPDecisionBarOutputFor(cpDecisionBAROutput2.getId());
        assertBigDecimalEquals(new BigDecimal("97.495").setScale(2, RoundingMode.HALF_UP), cpDecisionBAROutput2.getPrettyBAR());
        assertBigDecimalEquals(new BigDecimal("87.495").setScale(2, RoundingMode.HALF_UP), cpDecisionBAROutput2.getRoomsOnlyBAR());
        assertBigDecimalEquals(new BigDecimal("97.495").setScale(2, RoundingMode.HALF_UP), cpDecisionBAROutput2.getFinalBAR());
    }


    @Test
    public void roundOptimalBARsTaxInclusiveOnAndSupplementsAreSameAndOffsetsAreConfiguredForNonBaseAndOptimalBarIsWithinTheRange() {
        setTax(new BigDecimal("10.0"));
        addTransientPricingBaseAccomType(RT_DOUBLE, new BigDecimal("75"), new BigDecimal("780"), BigDecimal.TEN);
        addSupplement(RT_DOUBLE, OccupancyType.SINGLE, BigDecimal.TEN);
        addSupplement(RT_QUEEN, OccupancyType.SINGLE, BigDecimal.TEN);

        //add 5 as fixed offset. since tax inclusive adding 4.545 after adding tax it would be 4.995
        addCPConfigOffsetAccomType(RT_QUEEN, OccupancyType.SINGLE, OffsetMethod.FIXED_OFFSET, BigDecimal.valueOf(4.545));

        CPDecisionBAROutput cpDecisionBAROutput = addCPDecisionBarOutput(startDate, RT_DOUBLE, new BigDecimal("90"));
        CPDecisionBAROutput cpDecisionBAROutput2 = addCPDecisionBarOutput(startDate, RT_QUEEN, new BigDecimal("94.995"));

        service.roundOptimalBARs(startDate, endDate);

        cpDecisionBAROutput = getCPDecisionBarOutputFor(cpDecisionBAROutput.getId());
        assertBigDecimalEquals(new BigDecimal("100.00").setScale(2, RoundingMode.HALF_UP), cpDecisionBAROutput.getPrettyBAR());
        assertBigDecimalEquals(new BigDecimal("90.00").setScale(2, RoundingMode.HALF_UP), cpDecisionBAROutput.getRoomsOnlyBAR());
        assertBigDecimalEquals(new BigDecimal("100.00").setScale(2, RoundingMode.HALF_UP), cpDecisionBAROutput.getFinalBAR());

        cpDecisionBAROutput2 = getCPDecisionBarOutputFor(cpDecisionBAROutput2.getId());
        assertBigDecimalEquals(new BigDecimal("104.995").setScale(2, RoundingMode.HALF_UP), cpDecisionBAROutput2.getPrettyBAR());
        assertBigDecimalEquals(new BigDecimal("94.995").setScale(2, RoundingMode.HALF_UP), cpDecisionBAROutput2.getRoomsOnlyBAR());
        assertBigDecimalEquals(new BigDecimal("104.995").setScale(2, RoundingMode.HALF_UP), cpDecisionBAROutput2.getFinalBAR());
    }


    @Test
    public void roundOptimalBARsTaxInclusiveOnCeilingAndFloorOverrideIsDoneAndOptimalBarIsWithinTheRange() {
        setTax(new BigDecimal("10.0"));
        addTransientPricingBaseAccomType(RT_DOUBLE, new BigDecimal("55"), new BigDecimal("60"), BigDecimal.TEN);
        addSupplement(RT_DOUBLE, OccupancyType.SINGLE, BigDecimal.TEN);
        addSupplement(RT_QUEEN, OccupancyType.SINGLE, BigDecimal.TEN);
        //add 5 as fixed offset. since tax inclusive adding 4.545 after adding tax it would be 4.995
        addCPConfigOffsetAccomType(RT_QUEEN, OccupancyType.SINGLE, OffsetMethod.FIXED_OFFSET, BigDecimal.valueOf(4.545));
        CPDecisionBAROutput cpDecisionBAROutput = addCPDecisionBarOutput(startDate, RT_DOUBLE, new BigDecimal("90"));
        CPDecisionBAROutput cpDecisionBAROutput2 = addCPDecisionBarOutput(startDate, RT_QUEEN, new BigDecimal("94.995"));
        cpDecisionBAROutput.setCeilingOverride(BigDecimal.valueOf(100));
        cpDecisionBAROutput.setFloorOverride(BigDecimal.valueOf(79));
        cpDecisionBAROutput.setOverrideType(DecisionOverrideType.FLOORANDCEIL);
        setFloorAndCeilingOverride(cpDecisionBAROutput, BigDecimal.valueOf(79), BigDecimal.valueOf(100));
        tenantCrudService().save(cpDecisionBAROutput);
        tenantCrudService().flushAndClear();

        service.roundOptimalBARs(startDate, endDate);

        cpDecisionBAROutput = getCPDecisionBarOutputFor(cpDecisionBAROutput.getId());
        assertBigDecimalEquals(new BigDecimal("100.00").setScale(2, RoundingMode.HALF_UP), cpDecisionBAROutput.getPrettyBAR());
        assertBigDecimalEquals(new BigDecimal("90.00").setScale(2, RoundingMode.HALF_UP), cpDecisionBAROutput.getRoomsOnlyBAR());
        assertBigDecimalEquals(new BigDecimal("100.00").setScale(2, RoundingMode.HALF_UP), cpDecisionBAROutput.getFinalBAR());

        cpDecisionBAROutput2 = getCPDecisionBarOutputFor(cpDecisionBAROutput2.getId());
        assertBigDecimalEquals(new BigDecimal("104.995").setScale(2, RoundingMode.HALF_UP), cpDecisionBAROutput2.getPrettyBAR());
        assertBigDecimalEquals(new BigDecimal("94.995").setScale(2, RoundingMode.HALF_UP), cpDecisionBAROutput2.getRoomsOnlyBAR());
        assertBigDecimalEquals(new BigDecimal("104.995").setScale(2, RoundingMode.HALF_UP), cpDecisionBAROutput2.getFinalBAR());
    }

    @Test
    public void roundOptimalBARsTaxInclusiveOnCeilingAndFloorOverrideValuesAreVeryClose() {
        setTax(new BigDecimal("10.0"));
        addTransientPricingBaseAccomType(RT_DOUBLE, new BigDecimal("55"), new BigDecimal("60"), BigDecimal.TEN);
        addSupplement(RT_DOUBLE, OccupancyType.SINGLE, BigDecimal.TEN);
        addSupplement(RT_QUEEN, OccupancyType.SINGLE, BigDecimal.TEN);
        //add 5 as fixed offset. since tax inclusive adding 4.545 after adding tax it would be 4.995
        addCPConfigOffsetAccomType(RT_QUEEN, OccupancyType.SINGLE, OffsetMethod.FIXED_OFFSET, BigDecimal.valueOf(4.545));
        CPDecisionBAROutput cpDecisionBAROutput = addCPDecisionBarOutput(startDate, RT_DOUBLE, new BigDecimal("69"));
        CPDecisionBAROutput cpDecisionBAROutput2 = addCPDecisionBarOutput(startDate, RT_QUEEN, new BigDecimal("73.995"));
        cpDecisionBAROutput.setCeilingOverride(BigDecimal.valueOf(71));//service adds supplements to it
        cpDecisionBAROutput.setFloorOverride(BigDecimal.valueOf(69));//service adds supplements to it
        cpDecisionBAROutput.setOverrideType(DecisionOverrideType.FLOORANDCEIL);
        setFloorAndCeilingOverride(cpDecisionBAROutput, BigDecimal.valueOf(69), BigDecimal.valueOf(71));
        tenantCrudService().save(cpDecisionBAROutput);
        tenantCrudService().flushAndClear();

        service.roundOptimalBARs(startDate, endDate);

        cpDecisionBAROutput = getCPDecisionBarOutputFor(cpDecisionBAROutput.getId());
        assertBigDecimalEquals(new BigDecimal("79").setScale(2, RoundingMode.HALF_UP), cpDecisionBAROutput.getPrettyBAR());
        assertBigDecimalEquals(new BigDecimal("69").setScale(2, RoundingMode.HALF_UP), cpDecisionBAROutput.getRoomsOnlyBAR());
        assertBigDecimalEquals(new BigDecimal("79").setScale(2, RoundingMode.HALF_UP), cpDecisionBAROutput.getFinalBAR());

        cpDecisionBAROutput2 = getCPDecisionBarOutputFor(cpDecisionBAROutput2.getId());
        assertBigDecimalEquals(new BigDecimal("83.995").setScale(2, RoundingMode.HALF_UP), cpDecisionBAROutput2.getPrettyBAR());
        assertBigDecimalEquals(new BigDecimal("73.995").setScale(2, RoundingMode.HALF_UP), cpDecisionBAROutput2.getRoomsOnlyBAR());
        assertBigDecimalEquals(new BigDecimal("83.995").setScale(2, RoundingMode.HALF_UP), cpDecisionBAROutput2.getFinalBAR());
    }

    @Test
    public void roundOptimalBARsTaxInclusiveOnAndOnlyFloorOverrideIsSet() {
        setTax(new BigDecimal("10.0"));
        addTransientPricingBaseAccomType(RT_DOUBLE, new BigDecimal("55"), new BigDecimal("100"), BigDecimal.TEN);
        addSupplement(RT_DOUBLE, OccupancyType.SINGLE, BigDecimal.TEN);
        addSupplement(RT_QUEEN, OccupancyType.SINGLE, BigDecimal.TEN);
        //add 5 as fixed offset. since tax inclusive adding 4.545 after adding tax it would be 4.995
        addCPConfigOffsetAccomType(RT_QUEEN, OccupancyType.SINGLE, OffsetMethod.FIXED_OFFSET, BigDecimal.valueOf(4.545));
        CPDecisionBAROutput cpDecisionBAROutput = addCPDecisionBarOutput(startDate, RT_DOUBLE, new BigDecimal("150"));
        CPDecisionBAROutput cpDecisionBAROutput2 = addCPDecisionBarOutput(startDate, RT_QUEEN, new BigDecimal("155"));
        cpDecisionBAROutput.setFloorOverride(BigDecimal.valueOf(69));//service adds supplements to it
        cpDecisionBAROutput.setOverrideType(DecisionOverrideType.FLOOR);
        setFloorOverride(cpDecisionBAROutput, BigDecimal.valueOf(69));
        tenantCrudService().save(cpDecisionBAROutput);
        tenantCrudService().flushAndClear();

        service.roundOptimalBARs(startDate, endDate);

        cpDecisionBAROutput = getCPDecisionBarOutputFor(cpDecisionBAROutput.getId());
        assertBigDecimalEquals(new BigDecimal("120").setScale(2, RoundingMode.HALF_UP), cpDecisionBAROutput.getPrettyBAR());
        assertBigDecimalEquals(new BigDecimal("110").setScale(2, RoundingMode.HALF_UP), cpDecisionBAROutput.getRoomsOnlyBAR());
        assertBigDecimalEquals(new BigDecimal("120").setScale(2, RoundingMode.HALF_UP), cpDecisionBAROutput.getFinalBAR());

        cpDecisionBAROutput2 = getCPDecisionBarOutputFor(cpDecisionBAROutput2.getId());
        assertBigDecimalEquals(new BigDecimal("124.995").setScale(2, RoundingMode.HALF_UP), cpDecisionBAROutput2.getPrettyBAR());
        assertBigDecimalEquals(new BigDecimal("114.995").setScale(2, RoundingMode.HALF_UP), cpDecisionBAROutput2.getRoomsOnlyBAR());
        assertBigDecimalEquals(new BigDecimal("124.995").setScale(2, RoundingMode.HALF_UP), cpDecisionBAROutput2.getFinalBAR());
    }

    @Test
    public void roundOptimalBARsTaxInclusiveOnAndOnlyFloorOverrideIsSetAndOptimalBarIsLessThanFloorOverride() {
        setTax(new BigDecimal("10.0"));
        addTransientPricingBaseAccomType(RT_DOUBLE, new BigDecimal("10"), new BigDecimal("100"), BigDecimal.TEN);
        addSupplement(RT_DOUBLE, OccupancyType.SINGLE, BigDecimal.TEN);
        addSupplement(RT_QUEEN, OccupancyType.SINGLE, BigDecimal.TEN);
        //add 5 as fixed offset. since tax inclusive adding 4.545 after adding tax it would be 4.995
        addCPConfigOffsetAccomType(RT_QUEEN, OccupancyType.SINGLE, OffsetMethod.FIXED_OFFSET, BigDecimal.valueOf(4.545));
        CPDecisionBAROutput cpDecisionBAROutput = addCPDecisionBarOutput(startDate, RT_DOUBLE, new BigDecimal("50"));
        CPDecisionBAROutput cpDecisionBAROutput2 = addCPDecisionBarOutput(startDate, RT_QUEEN, new BigDecimal("55"));
        cpDecisionBAROutput.setFloorOverride(BigDecimal.valueOf(69));//service adds supplements to it
        cpDecisionBAROutput.setOverrideType(DecisionOverrideType.FLOOR);
        setFloorOverride(cpDecisionBAROutput, BigDecimal.valueOf(69));
        tenantCrudService().save(cpDecisionBAROutput);
        tenantCrudService().flushAndClear();

        service.roundOptimalBARs(startDate, endDate);

        cpDecisionBAROutput = getCPDecisionBarOutputFor(cpDecisionBAROutput.getId());
        assertBigDecimalEquals(new BigDecimal("79").setScale(2, RoundingMode.HALF_UP), cpDecisionBAROutput.getPrettyBAR());
        assertBigDecimalEquals(new BigDecimal("69").setScale(2, RoundingMode.HALF_UP), cpDecisionBAROutput.getRoomsOnlyBAR());
        assertBigDecimalEquals(new BigDecimal("79").setScale(2, RoundingMode.HALF_UP), cpDecisionBAROutput.getFinalBAR());

        cpDecisionBAROutput2 = getCPDecisionBarOutputFor(cpDecisionBAROutput2.getId());
        assertBigDecimalEquals(new BigDecimal("83.995").setScale(2, RoundingMode.HALF_UP), cpDecisionBAROutput2.getPrettyBAR());
        assertBigDecimalEquals(new BigDecimal("73.995").setScale(2, RoundingMode.HALF_UP), cpDecisionBAROutput2.getRoomsOnlyBAR());
        assertBigDecimalEquals(new BigDecimal("83.995").setScale(2, RoundingMode.HALF_UP), cpDecisionBAROutput2.getFinalBAR());
    }

    @Test
    public void roundOptimalBARsTaxInclusiveOnAndOnlyCeilingOverrideIsSet() {
        setTax(new BigDecimal("10.0"));
        addTransientPricingBaseAccomType(RT_DOUBLE, new BigDecimal("10"), new BigDecimal("300"), BigDecimal.TEN);
        addSupplement(RT_DOUBLE, OccupancyType.SINGLE, BigDecimal.TEN);
        addSupplement(RT_QUEEN, OccupancyType.SINGLE, BigDecimal.ONE);
        //add 5 as fixed offset. since tax inclusive adding 4.545 after adding tax it would be 4.995
        addCPConfigOffsetAccomType(RT_QUEEN, OccupancyType.SINGLE, OffsetMethod.FIXED_OFFSET, BigDecimal.valueOf(4.545));
        CPDecisionBAROutput cpDecisionBAROutput = addCPDecisionBarOutput(startDate, RT_DOUBLE, new BigDecimal("20"));
        CPDecisionBAROutput cpDecisionBAROutput2 = addCPDecisionBarOutput(startDate, RT_QUEEN, new BigDecimal("25"));
        cpDecisionBAROutput.setCeilingOverride(BigDecimal.valueOf(69));//service adds supplements to it
        cpDecisionBAROutput.setOverrideType(DecisionOverrideType.CEIL);
        setCeilingOverride(cpDecisionBAROutput, BigDecimal.valueOf(69));
        tenantCrudService().save(cpDecisionBAROutput);
        tenantCrudService().flushAndClear();

        service.roundOptimalBARs(startDate, endDate);

        cpDecisionBAROutput = getCPDecisionBarOutputFor(cpDecisionBAROutput.getId());
        assertBigDecimalEquals(new BigDecimal("30").setScale(2, RoundingMode.HALF_UP), cpDecisionBAROutput.getPrettyBAR());
        assertBigDecimalEquals(new BigDecimal("20").setScale(2, RoundingMode.HALF_UP), cpDecisionBAROutput.getRoomsOnlyBAR());
        assertBigDecimalEquals(new BigDecimal("30").setScale(2, RoundingMode.HALF_UP), cpDecisionBAROutput.getFinalBAR());

        cpDecisionBAROutput2 = getCPDecisionBarOutputFor(cpDecisionBAROutput2.getId());
        assertBigDecimalEquals(new BigDecimal("26").setScale(2, RoundingMode.HALF_UP), cpDecisionBAROutput2.getPrettyBAR());
        assertBigDecimalEquals(new BigDecimal("25").setScale(2, RoundingMode.HALF_UP), cpDecisionBAROutput2.getRoomsOnlyBAR());
        assertBigDecimalEquals(new BigDecimal("26").setScale(2, RoundingMode.HALF_UP), cpDecisionBAROutput2.getFinalBAR());
    }

    @Test
    public void roundOptimalBARsTaxInclusiveOnAndOnlyCeilingOverrideIsSetAndOptimalBarIsMoreThanCeiling() {
        setTax(new BigDecimal("10.0"));
        addTransientPricingBaseAccomType(RT_DOUBLE, new BigDecimal("40"), new BigDecimal("300"), BigDecimal.TEN);
        addSupplement(RT_DOUBLE, OccupancyType.SINGLE, BigDecimal.TEN);
        addSupplement(RT_QUEEN, OccupancyType.SINGLE, BigDecimal.ONE);
        //add 5 as fixed offset. since tax inclusive adding 4.545 after adding tax it would be 4.995
        addCPConfigOffsetAccomType(RT_QUEEN, OccupancyType.SINGLE, OffsetMethod.FIXED_OFFSET, BigDecimal.valueOf(4.545));
        CPDecisionBAROutput cpDecisionBAROutput = addCPDecisionBarOutput(startDate, RT_DOUBLE, new BigDecimal("90"));
        CPDecisionBAROutput cpDecisionBAROutput2 = addCPDecisionBarOutput(startDate, RT_QUEEN, new BigDecimal("95"));
        cpDecisionBAROutput.setCeilingOverride(BigDecimal.valueOf(69));//service adds supplements to it
        cpDecisionBAROutput.setOverrideType(DecisionOverrideType.CEIL);
        setCeilingOverride(cpDecisionBAROutput, BigDecimal.valueOf(69));
        tenantCrudService().save(cpDecisionBAROutput);
        tenantCrudService().flushAndClear();

        service.roundOptimalBARs(startDate, endDate);

        cpDecisionBAROutput = getCPDecisionBarOutputFor(cpDecisionBAROutput.getId());
        assertBigDecimalEquals(new BigDecimal("79").setScale(2, RoundingMode.HALF_UP), cpDecisionBAROutput.getPrettyBAR());
        assertBigDecimalEquals(new BigDecimal("69").setScale(2, RoundingMode.HALF_UP), cpDecisionBAROutput.getRoomsOnlyBAR());
        assertBigDecimalEquals(new BigDecimal("79").setScale(2, RoundingMode.HALF_UP), cpDecisionBAROutput.getFinalBAR());

        cpDecisionBAROutput2 = getCPDecisionBarOutputFor(cpDecisionBAROutput2.getId());
        assertBigDecimalEquals(new BigDecimal("74.995").setScale(2, RoundingMode.HALF_UP), cpDecisionBAROutput2.getPrettyBAR());
        assertBigDecimalEquals(new BigDecimal("73.995").setScale(2, RoundingMode.HALF_UP), cpDecisionBAROutput2.getRoomsOnlyBAR());
        assertBigDecimalEquals(new BigDecimal("74.995").setScale(2, RoundingMode.HALF_UP), cpDecisionBAROutput2.getFinalBAR());
    }

    @Test
    public void roundOptimalBARsTaxInclusiveOnSpecificOverrideIsDone() {
        setTax(new BigDecimal("10.0"));
        addTransientPricingBaseAccomType(RT_DOUBLE, new BigDecimal("50"), new BigDecimal("55"), BigDecimal.TEN);
        addSupplement(RT_DOUBLE, OccupancyType.SINGLE, BigDecimal.TEN);
        addSupplement(RT_QUEEN, OccupancyType.SINGLE, BigDecimal.TEN);
        //add 5 as fixed offset. since tax inclusive adding 4.545 after adding tax it would be 4.995
        addCPConfigOffsetAccomType(RT_QUEEN, OccupancyType.SINGLE, OffsetMethod.FIXED_OFFSET, BigDecimal.valueOf(4.545));
        CPDecisionBAROutput cpDecisionBAROutput = addCPDecisionBarOutput(startDate, RT_DOUBLE, new BigDecimal("115.00"));
        cpDecisionBAROutput.setSpecificOverride(BigDecimal.valueOf(115));//service adds supplements to it
        cpDecisionBAROutput.setOverrideType(DecisionOverrideType.USER);
        CPDecisionBAROutput cpDecisionBAROutput2 = addCPDecisionBarOutput(startDate, RT_QUEEN, new BigDecimal("119.995"));
        cpDecisionBAROutput2.setSpecificOverride(BigDecimal.valueOf(119.995));//service adds supplements to it
        cpDecisionBAROutput2.setOverrideType(DecisionOverrideType.USER);
        tenantCrudService().save(cpDecisionBAROutput);
        tenantCrudService().save(cpDecisionBAROutput2);
        setSpecificOverride(cpDecisionBAROutput, BigDecimal.valueOf(115.00));
        setSpecificOverride(cpDecisionBAROutput2, BigDecimal.valueOf(119.995));
        tenantCrudService().flushAndClear();

        service.roundOptimalBARs(startDate, endDate);

        cpDecisionBAROutput = getCPDecisionBarOutputFor(cpDecisionBAROutput.getId());
        assertBigDecimalEquals(new BigDecimal("125.00").setScale(2, RoundingMode.HALF_UP), cpDecisionBAROutput.getPrettyBAR());
        assertBigDecimalEquals(new BigDecimal("115.00").setScale(2, RoundingMode.HALF_UP), cpDecisionBAROutput.getRoomsOnlyBAR());
        assertBigDecimalEquals(new BigDecimal("125.00").setScale(2, RoundingMode.HALF_UP), cpDecisionBAROutput.getFinalBAR());

        cpDecisionBAROutput2 = getCPDecisionBarOutputFor(cpDecisionBAROutput2.getId());
        assertBigDecimalEquals(new BigDecimal("129.995").setScale(2, RoundingMode.HALF_UP), cpDecisionBAROutput2.getPrettyBAR());
        assertBigDecimalEquals(new BigDecimal("119.995").setScale(2, RoundingMode.HALF_UP), cpDecisionBAROutput2.getRoomsOnlyBAR());
        assertBigDecimalEquals(new BigDecimal("129.995").setScale(2, RoundingMode.HALF_UP), cpDecisionBAROutput2.getFinalBAR());
    }

    @Test
    public void roundOptimalBARsTaxInclusiveOnSpecificOverrideIsDoneAndSupplementsAreDifferentForBaseAndNonBase() {
        setTax(new BigDecimal("10.0"));
        addTransientPricingBaseAccomType(RT_DOUBLE, new BigDecimal("50"), new BigDecimal("55"), BigDecimal.ONE);
        addSupplement(RT_DOUBLE, OccupancyType.SINGLE, BigDecimal.ONE);
        addSupplement(RT_QUEEN, OccupancyType.SINGLE, BigDecimal.TEN);
        //add 5 as fixed offset. since tax inclusive adding 4.545 after adding tax it would be 4.995
        addCPConfigOffsetAccomType(RT_QUEEN, OccupancyType.SINGLE, OffsetMethod.FIXED_OFFSET, BigDecimal.valueOf(4.545));
        CPDecisionBAROutput cpDecisionBAROutput = addCPDecisionBarOutput(startDate, RT_DOUBLE, new BigDecimal("115.00"));
        cpDecisionBAROutput.setSpecificOverride(BigDecimal.valueOf(115));//service adds supplements to it
        cpDecisionBAROutput.setOverrideType(DecisionOverrideType.USER);
        CPDecisionBAROutput cpDecisionBAROutput2 = addCPDecisionBarOutput(startDate, RT_QUEEN, new BigDecimal("119.995"));
        cpDecisionBAROutput2.setSpecificOverride(BigDecimal.valueOf(119.995));//service adds supplements to it
        cpDecisionBAROutput2.setOverrideType(DecisionOverrideType.USER);
        tenantCrudService().save(cpDecisionBAROutput);
        tenantCrudService().save(cpDecisionBAROutput2);
        setSpecificOverride(cpDecisionBAROutput, BigDecimal.valueOf(115.00));
        setSpecificOverride(cpDecisionBAROutput2, BigDecimal.valueOf(119.995));
        tenantCrudService().flushAndClear();

        service.roundOptimalBARs(startDate, endDate);

        cpDecisionBAROutput = getCPDecisionBarOutputFor(cpDecisionBAROutput.getId());
        assertBigDecimalEquals(new BigDecimal("116.00").setScale(2, RoundingMode.HALF_UP), cpDecisionBAROutput.getPrettyBAR());
        assertBigDecimalEquals(new BigDecimal("115.00").setScale(2, RoundingMode.HALF_UP), cpDecisionBAROutput.getRoomsOnlyBAR());
        assertBigDecimalEquals(new BigDecimal("116.00").setScale(2, RoundingMode.HALF_UP), cpDecisionBAROutput.getFinalBAR());

        cpDecisionBAROutput2 = getCPDecisionBarOutputFor(cpDecisionBAROutput2.getId());
        assertBigDecimalEquals(new BigDecimal("129.995").setScale(2, RoundingMode.HALF_UP), cpDecisionBAROutput2.getPrettyBAR());
        assertBigDecimalEquals(new BigDecimal("119.995").setScale(2, RoundingMode.HALF_UP), cpDecisionBAROutput2.getRoomsOnlyBAR());
        assertBigDecimalEquals(new BigDecimal("129.995").setScale(2, RoundingMode.HALF_UP), cpDecisionBAROutput2.getFinalBAR());
    }

    @Test
    public void roundOptimalBARsTaxInclusiveOnSpecificOverrideIsDoneAndBaseRTSupplementsIsMoreThanNonBaseRT() {
        setTax(new BigDecimal("10.0"));
        addTransientPricingBaseAccomType(RT_DOUBLE, new BigDecimal("500"), new BigDecimal("1555"), BigDecimal.TEN);
        addSupplement(RT_DOUBLE, OccupancyType.SINGLE, BigDecimal.TEN);
        addSupplement(RT_QUEEN, OccupancyType.SINGLE, BigDecimal.ONE);
        //add 5 as fixed offset. since tax inclusive adding 4.545 after adding tax it would be 4.995
        addCPConfigOffsetAccomType(RT_QUEEN, OccupancyType.SINGLE, OffsetMethod.FIXED_OFFSET, BigDecimal.valueOf(4.545));
        CPDecisionBAROutput cpDecisionBAROutput = addCPDecisionBarOutput(startDate, RT_DOUBLE, new BigDecimal("115.00"));
        cpDecisionBAROutput.setSpecificOverride(BigDecimal.valueOf(115));//service adds supplements to it
        cpDecisionBAROutput.setOverrideType(DecisionOverrideType.USER);
        CPDecisionBAROutput cpDecisionBAROutput2 = addCPDecisionBarOutput(startDate, RT_QUEEN, new BigDecimal("119.995"));
        cpDecisionBAROutput2.setSpecificOverride(BigDecimal.valueOf(119.995));//service adds supplements to it
        cpDecisionBAROutput2.setOverrideType(DecisionOverrideType.USER);
        tenantCrudService().save(cpDecisionBAROutput);
        tenantCrudService().save(cpDecisionBAROutput2);
        setSpecificOverride(cpDecisionBAROutput, BigDecimal.valueOf(115.00));
        setSpecificOverride(cpDecisionBAROutput2, BigDecimal.valueOf(119.995));
        tenantCrudService().flushAndClear();

        service.roundOptimalBARs(startDate, endDate);

        cpDecisionBAROutput = getCPDecisionBarOutputFor(cpDecisionBAROutput.getId());
        assertBigDecimalEquals(new BigDecimal("125.00").setScale(2, RoundingMode.HALF_UP), cpDecisionBAROutput.getPrettyBAR());
        assertBigDecimalEquals(new BigDecimal("115.00").setScale(2, RoundingMode.HALF_UP), cpDecisionBAROutput.getRoomsOnlyBAR());
        assertBigDecimalEquals(new BigDecimal("125.00").setScale(2, RoundingMode.HALF_UP), cpDecisionBAROutput.getFinalBAR());

        cpDecisionBAROutput2 = getCPDecisionBarOutputFor(cpDecisionBAROutput2.getId());
        assertBigDecimalEquals(new BigDecimal("120.995").setScale(2, RoundingMode.HALF_UP), cpDecisionBAROutput2.getPrettyBAR());
        assertBigDecimalEquals(new BigDecimal("119.995").setScale(2, RoundingMode.HALF_UP), cpDecisionBAROutput2.getRoomsOnlyBAR());
        assertBigDecimalEquals(new BigDecimal("120.995").setScale(2, RoundingMode.HALF_UP), cpDecisionBAROutput2.getFinalBAR());
    }

    @Test
    public void roundOptimalBARsTaxInclusiveOnCeilingOverrideIsDoneAndBaseRTSupplementsIsMoreThanNonBaseRT() {
        setTax(new BigDecimal("10.0"));
        addTransientPricingBaseAccomType(RT_DOUBLE, new BigDecimal("15"), new BigDecimal("300"), BigDecimal.TEN);
        addSupplement(RT_DOUBLE, OccupancyType.SINGLE, BigDecimal.TEN);
        addSupplement(RT_QUEEN, OccupancyType.SINGLE, BigDecimal.ONE);
        //add 5 as fixed offset. since tax inclusive adding 4.545 after adding tax it would be 4.995
        addCPConfigOffsetAccomType(RT_QUEEN, OccupancyType.SINGLE, OffsetMethod.FIXED_OFFSET, BigDecimal.valueOf(4.545));
        CPDecisionBAROutput baseCPBarDecision = addCPDecisionBarOutput(startDate, RT_DOUBLE, new BigDecimal("115.00"));
        CPDecisionBAROutput nonBaseCPBarDecision = addCPDecisionBarOutput(startDate, RT_QUEEN, new BigDecimal("119.995"));
        baseCPBarDecision.setCeilingOverride(BigDecimal.valueOf(50));//service adds supplements to it
        baseCPBarDecision.setOverrideType(DecisionOverrideType.CEIL);
        tenantCrudService().save(baseCPBarDecision);
        setCeilingOverride(baseCPBarDecision, BigDecimal.valueOf(50));
        tenantCrudService().flushAndClear();

        service.roundOptimalBARs(startDate, endDate);

        baseCPBarDecision = getCPDecisionBarOutputFor(baseCPBarDecision.getId());
        assertBigDecimalEquals(new BigDecimal("60.00").setScale(2, RoundingMode.HALF_UP), baseCPBarDecision.getPrettyBAR());
        assertBigDecimalEquals(new BigDecimal("50.00").setScale(2, RoundingMode.HALF_UP), baseCPBarDecision.getRoomsOnlyBAR());
        assertBigDecimalEquals(new BigDecimal("60.00").setScale(2, RoundingMode.HALF_UP), baseCPBarDecision.getFinalBAR());

        nonBaseCPBarDecision = getCPDecisionBarOutputFor(nonBaseCPBarDecision.getId());
        assertBigDecimalEquals(new BigDecimal("56").setScale(2, RoundingMode.HALF_UP), nonBaseCPBarDecision.getPrettyBAR());
        assertBigDecimalEquals(new BigDecimal("55").setScale(2, RoundingMode.HALF_UP), nonBaseCPBarDecision.getRoomsOnlyBAR());
        assertBigDecimalEquals(new BigDecimal("56").setScale(2, RoundingMode.HALF_UP), nonBaseCPBarDecision.getFinalBAR());
    }

    @Test
    public void roundOptimalBARsTaxInclusiveOnFloorOverrideIsDoneAndBaseRTSupplementsIsMoreThanNonBaseRT() {
        setTax(new BigDecimal("10.0"));
        addTransientPricingBaseAccomType(RT_DOUBLE, new BigDecimal("15"), new BigDecimal("300"), BigDecimal.TEN);
        addSupplement(RT_DOUBLE, OccupancyType.SINGLE, BigDecimal.TEN);
        addSupplement(RT_QUEEN, OccupancyType.SINGLE, BigDecimal.ONE);
        //add 5 as fixed offset. since tax inclusive adding 4.545 after adding tax it would be 4.995
        addCPConfigOffsetAccomType(RT_QUEEN, OccupancyType.SINGLE, OffsetMethod.FIXED_OFFSET, BigDecimal.valueOf(4.545));
        CPDecisionBAROutput baseCPBarDecision = addCPDecisionBarOutput(startDate, RT_DOUBLE, new BigDecimal("115.00"));
        CPDecisionBAROutput nonBaseCPBarDecision = addCPDecisionBarOutput(startDate, RT_QUEEN, new BigDecimal("119.995"));
        baseCPBarDecision.setFloorOverride(BigDecimal.valueOf(150));//service adds supplements to it
        baseCPBarDecision.setOverrideType(DecisionOverrideType.FLOOR);
        tenantCrudService().save(baseCPBarDecision);
        setFloorOverride(baseCPBarDecision, BigDecimal.valueOf(150));
        tenantCrudService().flushAndClear();

        service.roundOptimalBARs(startDate, endDate);

        baseCPBarDecision = getCPDecisionBarOutputFor(baseCPBarDecision.getId());
        assertBigDecimalEquals(new BigDecimal("160.00").setScale(2, RoundingMode.HALF_UP), baseCPBarDecision.getPrettyBAR());
        assertBigDecimalEquals(new BigDecimal("150.00").setScale(2, RoundingMode.HALF_UP), baseCPBarDecision.getRoomsOnlyBAR());
        assertBigDecimalEquals(new BigDecimal("160.00").setScale(2, RoundingMode.HALF_UP), baseCPBarDecision.getFinalBAR());

        nonBaseCPBarDecision = getCPDecisionBarOutputFor(nonBaseCPBarDecision.getId());
        assertBigDecimalEquals(new BigDecimal("156").setScale(2, RoundingMode.HALF_UP), nonBaseCPBarDecision.getPrettyBAR());
        assertBigDecimalEquals(new BigDecimal("155").setScale(2, RoundingMode.HALF_UP), nonBaseCPBarDecision.getRoomsOnlyBAR());
        assertBigDecimalEquals(new BigDecimal("156").setScale(2, RoundingMode.HALF_UP), nonBaseCPBarDecision.getFinalBAR());
    }

    @Test
    public void roundOptimalBARsTaxInclusiveOnOptimalBarIsLessThanFloorValueAndBaseRTSupplementsIsMoreThanNonBaseRT() {
        addTransientPricingBaseAccomType(RT_DOUBLE, new BigDecimal("150"), new BigDecimal("300"), BigDecimal.TEN);
        addSupplement(RT_DOUBLE, OccupancyType.SINGLE, BigDecimal.TEN);
        addSupplement(RT_QUEEN, OccupancyType.SINGLE, BigDecimal.ONE);
        //add 5 as fixed offset. since tax inclusive adding 4.545 after adding tax it would be 4.995
        addCPConfigOffsetAccomType(RT_QUEEN, OccupancyType.SINGLE, OffsetMethod.FIXED_OFFSET, BigDecimal.valueOf(5.00));
        CPDecisionBAROutput baseCPBarDecision = addCPDecisionBarOutput(startDate, RT_DOUBLE, new BigDecimal("115.00"));
        CPDecisionBAROutput nonBaseCPBarDecision = addCPDecisionBarOutput(startDate, RT_QUEEN, new BigDecimal("119.995"));

        service.roundOptimalBARs(startDate, endDate);

        baseCPBarDecision = getCPDecisionBarOutputFor(baseCPBarDecision.getId());
        assertBigDecimalEquals(new BigDecimal("160.00").setScale(2, RoundingMode.HALF_UP), baseCPBarDecision.getPrettyBAR());
        assertBigDecimalEquals(new BigDecimal("150.00").setScale(2, RoundingMode.HALF_UP), baseCPBarDecision.getRoomsOnlyBAR());
        assertBigDecimalEquals(new BigDecimal("160.00").setScale(2, RoundingMode.HALF_UP), baseCPBarDecision.getFinalBAR());

        nonBaseCPBarDecision = getCPDecisionBarOutputFor(nonBaseCPBarDecision.getId());
        assertBigDecimalEquals(new BigDecimal("156").setScale(2, RoundingMode.HALF_UP), nonBaseCPBarDecision.getPrettyBAR());
        assertBigDecimalEquals(new BigDecimal("155").setScale(2, RoundingMode.HALF_UP), nonBaseCPBarDecision.getRoomsOnlyBAR());
        assertBigDecimalEquals(new BigDecimal("156").setScale(2, RoundingMode.HALF_UP), nonBaseCPBarDecision.getFinalBAR());
    }

    @Test
    public void roundOptimalBARsTaxInclusiveOnOptimalBarIsMoreThanCeilingValueAndBaseRTSupplementIsMoreThanNonBaseRT() {
        addTransientPricingBaseAccomType(RT_DOUBLE, new BigDecimal("150"), new BigDecimal("300"), BigDecimal.TEN);
        addSupplement(RT_DOUBLE, OccupancyType.SINGLE, BigDecimal.TEN);
        addSupplement(RT_QUEEN, OccupancyType.SINGLE, BigDecimal.ONE);
        //add 5 as fixed offset. since tax inclusive adding 4.545 after adding tax it would be 4.995
        addCPConfigOffsetAccomType(RT_QUEEN, OccupancyType.SINGLE, OffsetMethod.FIXED_OFFSET, BigDecimal.valueOf(5.00));
        CPDecisionBAROutput baseCPBarDecision = addCPDecisionBarOutput(startDate, RT_DOUBLE, new BigDecimal("400.00"));
        CPDecisionBAROutput nonBaseCPBarDecision = addCPDecisionBarOutput(startDate, RT_QUEEN, new BigDecimal("404.995"));

        service.roundOptimalBARs(startDate, endDate);

        baseCPBarDecision = getCPDecisionBarOutputFor(baseCPBarDecision.getId());
        assertBigDecimalEquals(new BigDecimal("310.00").setScale(2, RoundingMode.HALF_UP), baseCPBarDecision.getPrettyBAR());
        assertBigDecimalEquals(new BigDecimal("300.00").setScale(2, RoundingMode.HALF_UP), baseCPBarDecision.getRoomsOnlyBAR());
        assertBigDecimalEquals(new BigDecimal("310.00").setScale(2, RoundingMode.HALF_UP), baseCPBarDecision.getFinalBAR());

        nonBaseCPBarDecision = getCPDecisionBarOutputFor(nonBaseCPBarDecision.getId());
        assertBigDecimalEquals(new BigDecimal("306").setScale(2, RoundingMode.HALF_UP), nonBaseCPBarDecision.getPrettyBAR());
        assertBigDecimalEquals(new BigDecimal("305").setScale(2, RoundingMode.HALF_UP), nonBaseCPBarDecision.getRoomsOnlyBAR());
        assertBigDecimalEquals(new BigDecimal("306").setScale(2, RoundingMode.HALF_UP), nonBaseCPBarDecision.getFinalBAR());
    }

    @ParameterizedTest(name = "row: {index} ==> ComponentRoomsPriceAsSumOfParts={0}, expectedFinalBar={1}")
    @CsvSource({"false, 559.55", "true, 959.55"})
    public void roundOptimalBARsForComponentRooms_PriceAsSumOfPart(boolean sumOfPartToggleValue, double expectedFinalBar) {
        // Set a Pricing Rule
        setRoundingRule(9, 5, 5);
        when(pacmanConfigParamsService.getBooleanParameterValue(COMPONENT_ROOMS_PRICE_AS_SUM_OF_PARTS)).thenReturn(sumOfPartToggleValue);
        setupComponentRoomDataForPriceAsSumOfPart(STE, RT_DOUBLE, RT_QUEEN);

        addTransientPricingBaseAccomType(RT_DOUBLE, new BigDecimal("150"), new BigDecimal("300"), BigDecimal.TEN);
        addTransientPricingBaseAccomType(STE, new BigDecimal("500"), new BigDecimal("700"), BigDecimal.TEN);
        addSupplement(RT_DOUBLE, OccupancyType.SINGLE, BigDecimal.TEN);
        addSupplement(RT_QUEEN, OccupancyType.SINGLE, BigDecimal.TEN);
        addSupplement(STE, OccupancyType.SINGLE, BigDecimal.TEN);
        addCPConfigOffsetAccomType(RT_QUEEN, OccupancyType.SINGLE, OffsetMethod.FIXED_OFFSET, BigDecimal.valueOf(20.00));
        CPDecisionBAROutput baseCPBarDecision = addCPDecisionBarOutput(startDate, RT_DOUBLE, new BigDecimal("166.00"));
        CPDecisionBAROutput nonBaseCPBarDecision = addCPDecisionBarOutput(startDate, RT_QUEEN, new BigDecimal("186.00"));
        CPDecisionBAROutput baseCPBarDecisionForSTE = addCPDecisionBarOutput(startDate, STE, new BigDecimal("545.00"));

        service.roundOptimalBARs(startDate, endDate);

        baseCPBarDecision = getCPDecisionBarOutputFor(baseCPBarDecision.getId());
        assertBigDecimalEquals(new BigDecimal("179.55").setScale(2, RoundingMode.HALF_UP), baseCPBarDecision.getPrettyBAR());
        assertBigDecimalEquals(new BigDecimal("169.55").setScale(2, RoundingMode.HALF_UP), baseCPBarDecision.getRoomsOnlyBAR());
        assertBigDecimalEquals(new BigDecimal("179.55").setScale(2, RoundingMode.HALF_UP), baseCPBarDecision.getFinalBAR());

        nonBaseCPBarDecision = getCPDecisionBarOutputFor(nonBaseCPBarDecision.getId());
        assertBigDecimalEquals(new BigDecimal("199.55").setScale(2, RoundingMode.HALF_UP), nonBaseCPBarDecision.getPrettyBAR());
        assertBigDecimalEquals(new BigDecimal("189.55").setScale(2, RoundingMode.HALF_UP), nonBaseCPBarDecision.getRoomsOnlyBAR());
        assertBigDecimalEquals(new BigDecimal("199.55").setScale(2, RoundingMode.HALF_UP), nonBaseCPBarDecision.getFinalBAR());

        baseCPBarDecisionForSTE = getCPDecisionBarOutputFor(baseCPBarDecisionForSTE.getId());
        BigDecimal expectedFinalBarValue = new BigDecimal(expectedFinalBar);
        assertBigDecimalEquals(expectedFinalBarValue.setScale(2, RoundingMode.HALF_UP), baseCPBarDecisionForSTE.getPrettyBAR());
        assertBigDecimalEquals(expectedFinalBarValue.subtract(BigDecimal.TEN).setScale(2, RoundingMode.HALF_UP), baseCPBarDecisionForSTE.getRoomsOnlyBAR());
        assertBigDecimalEquals(expectedFinalBarValue.setScale(2, RoundingMode.HALF_UP), baseCPBarDecisionForSTE.getFinalBAR());
    }

    @ParameterizedTest(name = "row: {index} ==> ComponentRoomsPriceAsSumOfParts={0}, expectedFinalBarForDoubleRT={1} , expectedFinalBarForQueenRT={1}")
    @CsvSource({"false, 176.00, 176.00",
            "true, 1110.00, 1665.00"})
    public void roundOptimalBARsForComponentRoomsEqualsToPriceAsSumOfPartEvenWhenCPBaseRTOnlyEnabled(boolean isPriceAsSumOfParts, double expectedFinalBarForDoubleRT, double expectedFinalBarForQueenRT) {
        when(pacmanConfigParamsService.getBooleanParameterValue(COMPONENT_ROOMS_PRICE_AS_SUM_OF_PARTS)).thenReturn(isPriceAsSumOfParts);
        when(pacmanConfigParamsService.getBooleanParameterValue(CPBASE_ROOM_TYPE_ONLY_ENABLED.value())).thenReturn(true);
        AccomType doubleAccomType = findAccomType(RT_DOUBLE);
        doubleAccomType.setIsComponentRoom("Y");
        AccomType queenAccomType = findAccomType(RT_QUEEN);
        queenAccomType.setIsComponentRoom("Y");
        tenantCrudService().save(Arrays.asList(doubleAccomType, queenAccomType));

        AccomType steAccomType = findAccomType(STE);
        CRAccomTypeMapping crAccomTypeMapping1 = new CRAccomTypeMapping(doubleAccomType, steAccomType, 2);
        CRAccomTypeMapping crAccomTypeMapping2 = new CRAccomTypeMapping(queenAccomType, steAccomType, 3);
        tenantCrudService().save(Arrays.asList(crAccomTypeMapping1, crAccomTypeMapping2));
        updatePricingAccomClassData(RT_DOUBLE, null, null, true);

        addTransientPricingBaseAccomType(RT_DOUBLE, new BigDecimal("150"), new BigDecimal("300"), BigDecimal.TEN);
        addTransientPricingBaseAccomType(STE, new BigDecimal("500"), new BigDecimal("700"), BigDecimal.TEN);
        addSupplement(RT_DOUBLE, OccupancyType.SINGLE, BigDecimal.TEN);
        addSupplement(RT_QUEEN, OccupancyType.SINGLE, BigDecimal.TEN);
        addSupplement(STE, OccupancyType.SINGLE, BigDecimal.TEN);
        addCPConfigOffsetAccomType(RT_QUEEN, OccupancyType.SINGLE, OffsetMethod.FIXED_OFFSET, BigDecimal.valueOf(20.00));
        CPDecisionBAROutput baseCPBarDecision = addCPDecisionBarOutput(startDate, RT_DOUBLE, new BigDecimal("166.00"));
        CPDecisionBAROutput nonBaseCPBarDecision = addCPDecisionBarOutput(startDate, RT_QUEEN, new BigDecimal("186.00"));
        CPDecisionBAROutput baseCPBarDecisionForSTE = addCPDecisionBarOutput(startDate, STE, new BigDecimal("545.00"));

        service.roundOptimalBARs(startDate, endDate);

        baseCPBarDecisionForSTE = getCPDecisionBarOutputFor(baseCPBarDecisionForSTE.getId());
        assertBigDecimalEquals(new BigDecimal("555.00").setScale(2, RoundingMode.HALF_UP), baseCPBarDecisionForSTE.getPrettyBAR());
        assertBigDecimalEquals(new BigDecimal("545.00").setScale(2, RoundingMode.HALF_UP), baseCPBarDecisionForSTE.getRoomsOnlyBAR());
        assertBigDecimalEquals(new BigDecimal("555.00").setScale(2, RoundingMode.HALF_UP), baseCPBarDecisionForSTE.getFinalBAR());

        baseCPBarDecision = getCPDecisionBarOutputFor(baseCPBarDecision.getId());
        BigDecimal expectedDoubleFinalBar = new BigDecimal(expectedFinalBarForDoubleRT);
        assertBigDecimalEquals(expectedDoubleFinalBar.setScale(2, RoundingMode.HALF_UP), baseCPBarDecision.getPrettyBAR());
        assertBigDecimalEquals(expectedDoubleFinalBar.subtract(BigDecimal.TEN).setScale(2, RoundingMode.HALF_UP), baseCPBarDecision.getRoomsOnlyBAR());
        assertBigDecimalEquals(expectedDoubleFinalBar.setScale(2, RoundingMode.HALF_UP), baseCPBarDecision.getFinalBAR());

        BigDecimal expectedQueenFinalBar = new BigDecimal(expectedFinalBarForQueenRT);
        nonBaseCPBarDecision = getCPDecisionBarOutputFor(nonBaseCPBarDecision.getId());
        assertBigDecimalEquals(expectedQueenFinalBar.setScale(2, RoundingMode.HALF_UP), nonBaseCPBarDecision.getPrettyBAR());
        assertBigDecimalEquals(expectedQueenFinalBar.subtract(BigDecimal.TEN).setScale(2, RoundingMode.HALF_UP), nonBaseCPBarDecision.getRoomsOnlyBAR());
        assertBigDecimalEquals(expectedQueenFinalBar.setScale(2, RoundingMode.HALF_UP), nonBaseCPBarDecision.getFinalBAR());
    }

    @Test
    public void findCPDecisionBAROutputsForRoomTypes() {
        addCPDecisionBarOutput(startDate, RT_DOUBLE, new BigDecimal("166.00"));
        addCPDecisionBarOutput(startDate, RT_QUEEN, new BigDecimal("186.00"));
        CPDecisionBAROutput baseCPBarDecisionForSTE = addCPDecisionBarOutput(startDate, STE, new BigDecimal("545.00"));

        TreeMap<LocalDate, Map<Product, List<CPDecisionBAROutput>>> outputsForRoomTypes = service.findCPDecisionBAROutputsForRoomTypes(startDate, endDate, Collections.singleton(findAccomType(STE)));

        assertEquals(1, outputsForRoomTypes.size());
        Object barProduct = tenantCrudService().findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT);
        baseCPBarDecisionForSTE = getCPDecisionBarOutputFor(baseCPBarDecisionForSTE.getId());
        List<CPDecisionBAROutput> cpDecisionBAROutputs = outputsForRoomTypes.get(startDate).get(barProduct);
        assertEquals(1, cpDecisionBAROutputs.size());
        assertEquals(baseCPBarDecisionForSTE, cpDecisionBAROutputs.get(0));
    }

    @ParameterizedTest(name = "row: {index} ==> ComponentRoomsPriceAsSumOfParts={0}, expectedFinalBarForDoubleRT={1} , expectedFinalBarForQueenRT={1}")
    @CsvSource({"false, 176.00, 176.00",
            "true, 1080.00, 1620.00"})
    public void roundOptimalBARsForComponentRoomsEqualsToPriceAsSumOfPartMinimumPriceChangeIsNotSatisfied(boolean isPriceAsSumOfParts, double expectedFinalBarForDoubleRT, double expectedFinalBarForQueenRT) {
        when(pacmanConfigParamsService.getBooleanParameterValue(COMPONENT_ROOMS_PRICE_AS_SUM_OF_PARTS)).thenReturn(isPriceAsSumOfParts);
        when(pacmanConfigParamsService.getBooleanParameterValue(CPBASE_ROOM_TYPE_ONLY_ENABLED.value())).thenReturn(true);
        AccomType doubleAccomType = findAccomType(RT_DOUBLE);
        doubleAccomType.setIsComponentRoom("Y");
        AccomType queenAccomType = findAccomType(RT_QUEEN);
        queenAccomType.setIsComponentRoom("Y");
        tenantCrudService().save(Arrays.asList(doubleAccomType, queenAccomType));

        AccomType steAccomType = findAccomType(STE);
        CRAccomTypeMapping crAccomTypeMapping1 = new CRAccomTypeMapping(doubleAccomType, steAccomType, 2);
        CRAccomTypeMapping crAccomTypeMapping2 = new CRAccomTypeMapping(queenAccomType, steAccomType, 3);
        tenantCrudService().save(Arrays.asList(crAccomTypeMapping1, crAccomTypeMapping2));
        updatePricingAccomClassData(RT_DOUBLE, null, null, true);

        addTransientPricingBaseAccomType(RT_DOUBLE, new BigDecimal("150"), new BigDecimal("300"), BigDecimal.TEN);
        addTransientPricingBaseAccomType(STE, new BigDecimal("500"), new BigDecimal("700"), BigDecimal.TEN);
        addSupplement(RT_DOUBLE, OccupancyType.SINGLE, BigDecimal.TEN);
        addSupplement(RT_QUEEN, OccupancyType.SINGLE, BigDecimal.TEN);
        addSupplement(STE, OccupancyType.SINGLE, BigDecimal.TEN);
        addCPConfigOffsetAccomType(RT_QUEEN, OccupancyType.SINGLE, OffsetMethod.FIXED_OFFSET, BigDecimal.valueOf(20.00));
        CPDecisionBAROutput baseCPBarDecision = addCPDecisionBarOutput(startDate, RT_DOUBLE, new BigDecimal("166.00"));
        CPDecisionBAROutput nonBaseCPBarDecision = addCPDecisionBarOutput(startDate, RT_QUEEN, new BigDecimal("186.00"));
        CPDecisionBAROutput baseCPBarDecisionForSTE = addCPDecisionBarOutput(startDate, STE, new BigDecimal("545.00"));

        updatePricingAccomClassData(STE, MinimumIncrementMethod.FIXED_OFFSET, BigDecimal.valueOf(50), false);// so that minimum price change is not satisfied
        addDecisionDailybarOutput(STE, startDate, new BigDecimal("540.00"), new BigDecimal("540.00"), BigDecimal.ZERO, BigDecimal.ZERO);

        service.roundOptimalBARs(startDate, endDate);

        baseCPBarDecisionForSTE = getCPDecisionBarOutputFor(baseCPBarDecisionForSTE.getId());
        assertEquals(new BigDecimal("555.00"), baseCPBarDecisionForSTE.getPrettyBAR().setScale(2, RoundingMode.HALF_UP));
        assertEquals(new BigDecimal("530.00"), baseCPBarDecisionForSTE.getRoomsOnlyBAR().setScale(2, RoundingMode.HALF_UP));
        assertEquals(new BigDecimal("540.00"), baseCPBarDecisionForSTE.getFinalBAR().setScale(2, RoundingMode.HALF_UP));

        baseCPBarDecision = getCPDecisionBarOutputFor(baseCPBarDecision.getId());
        BigDecimal expectedDoubleFinalBar = new BigDecimal(expectedFinalBarForDoubleRT);
        assertBigDecimalEquals(expectedDoubleFinalBar.setScale(2, RoundingMode.HALF_UP), baseCPBarDecision.getPrettyBAR());
        assertBigDecimalEquals(expectedDoubleFinalBar.subtract(BigDecimal.TEN).setScale(2, RoundingMode.HALF_UP), baseCPBarDecision.getRoomsOnlyBAR());
        assertBigDecimalEquals(expectedDoubleFinalBar.setScale(2, RoundingMode.HALF_UP), baseCPBarDecision.getFinalBAR());

        BigDecimal expectedQueenFinalBar = new BigDecimal(expectedFinalBarForQueenRT);
        nonBaseCPBarDecision = getCPDecisionBarOutputFor(nonBaseCPBarDecision.getId());
        assertBigDecimalEquals(expectedQueenFinalBar.setScale(2, RoundingMode.HALF_UP), nonBaseCPBarDecision.getPrettyBAR());
        assertBigDecimalEquals(expectedQueenFinalBar.subtract(BigDecimal.TEN).setScale(2, RoundingMode.HALF_UP), nonBaseCPBarDecision.getRoomsOnlyBAR());
        assertBigDecimalEquals(expectedQueenFinalBar.setScale(2, RoundingMode.HALF_UP), nonBaseCPBarDecision.getFinalBAR());
    }


    @ParameterizedTest(name = "row: {index} ==> ComponentRoomsPriceAsSumOfParts={0}, expectedFinalBarForDoubleRT={1} , expectedFinalBarForQueenRT={1}")
    @CsvSource({"false, 176.00, 196.00",
            "true, 751.00, 196.00"})
    /**
     * final bar should be STE + Queen. Since as STE is not configured as sumOfIt's part so we should take final bar of STE and not (sum of parts of STE)
     */
    public void roundOptimalBARsForComponentRoomsEqualsToPriceAsSumOfPartWhereCRIsConfiguredAsPart(boolean isPriceAsSumOfParts, double expectedFinalBarForDoubleRT, double expectedFinalBarForQueenRT) {
        when(pacmanConfigParamsService.getBooleanParameterValue(COMPONENT_ROOMS_PRICE_AS_SUM_OF_PARTS)).thenReturn(isPriceAsSumOfParts);
        AccomType doubleAccomType = findAccomType(RT_DOUBLE);
        doubleAccomType.setIsComponentRoom("Y");
        AccomType queenAccomType = findAccomType(RT_QUEEN);
        tenantCrudService().save(Arrays.asList(doubleAccomType));

        AccomType steAccomType = findAccomType(STE);
        steAccomType.setIsComponentRoom("Y");
        CRAccomTypeMapping crAccomTypeMapping1 = new CRAccomTypeMapping(steAccomType, queenAccomType, 2);
        CRAccomTypeMapping crAccomTypeMapping2 = new CRAccomTypeMapping(doubleAccomType, steAccomType, 1);
        CRAccomTypeMapping crAccomTypeMapping3 = new CRAccomTypeMapping(doubleAccomType, queenAccomType, 1);
        tenantCrudService().save(Arrays.asList(crAccomTypeMapping1, crAccomTypeMapping2, crAccomTypeMapping3));
        updatePricingAccomClassData(RT_DOUBLE, null, null, true);

        addTransientPricingBaseAccomType(RT_DOUBLE, new BigDecimal("150"), new BigDecimal("300"), BigDecimal.TEN);
        addTransientPricingBaseAccomType(STE, new BigDecimal("500"), new BigDecimal("700"), BigDecimal.TEN);
        addSupplement(RT_DOUBLE, OccupancyType.SINGLE, BigDecimal.TEN);
        addSupplement(RT_QUEEN, OccupancyType.SINGLE, BigDecimal.TEN);
        addSupplement(STE, OccupancyType.SINGLE, BigDecimal.TEN);
        addCPConfigOffsetAccomType(RT_QUEEN, OccupancyType.SINGLE, OffsetMethod.FIXED_OFFSET, BigDecimal.valueOf(20.00));
        CPDecisionBAROutput baseCPBarDecision = addCPDecisionBarOutput(startDate, RT_DOUBLE, new BigDecimal("166.00"));
        CPDecisionBAROutput nonBaseCPBarDecision = addCPDecisionBarOutput(startDate, RT_QUEEN, new BigDecimal("186.00"));
        CPDecisionBAROutput baseCPBarDecisionForSTE = addCPDecisionBarOutput(startDate, STE, new BigDecimal("545.00"));

        updatePricingAccomClassData(STE, MinimumIncrementMethod.FIXED_OFFSET, BigDecimal.ZERO, false);
        addDecisionDailybarOutput(STE, startDate, new BigDecimal("540.00"), new BigDecimal("540.00"), BigDecimal.ZERO, BigDecimal.ZERO);

        service.roundOptimalBARs(startDate, endDate);

        baseCPBarDecisionForSTE = getCPDecisionBarOutputFor(baseCPBarDecisionForSTE.getId());
        assertEquals(new BigDecimal("555.00"), baseCPBarDecisionForSTE.getPrettyBAR().setScale(2, RoundingMode.HALF_UP));
        assertEquals(new BigDecimal("545.00"), baseCPBarDecisionForSTE.getRoomsOnlyBAR().setScale(2, RoundingMode.HALF_UP));
        assertEquals(new BigDecimal("555.00"), baseCPBarDecisionForSTE.getFinalBAR().setScale(2, RoundingMode.HALF_UP));

        baseCPBarDecision = getCPDecisionBarOutputFor(baseCPBarDecision.getId());
        BigDecimal expectedDoubleFinalBar = new BigDecimal(expectedFinalBarForDoubleRT);
        assertBigDecimalEquals(expectedDoubleFinalBar.setScale(2, RoundingMode.HALF_UP), baseCPBarDecision.getPrettyBAR());
        assertBigDecimalEquals(expectedDoubleFinalBar.subtract(BigDecimal.TEN).setScale(2, RoundingMode.HALF_UP), baseCPBarDecision.getRoomsOnlyBAR());
        assertBigDecimalEquals(expectedDoubleFinalBar.setScale(2, RoundingMode.HALF_UP), baseCPBarDecision.getFinalBAR());

        BigDecimal expectedQueenFinalBar = new BigDecimal(expectedFinalBarForQueenRT);
        nonBaseCPBarDecision = getCPDecisionBarOutputFor(nonBaseCPBarDecision.getId());
        assertBigDecimalEquals(expectedQueenFinalBar.setScale(2, RoundingMode.HALF_UP), nonBaseCPBarDecision.getPrettyBAR());
        assertBigDecimalEquals(expectedQueenFinalBar.subtract(BigDecimal.TEN).setScale(2, RoundingMode.HALF_UP), nonBaseCPBarDecision.getRoomsOnlyBAR());
        assertBigDecimalEquals(expectedQueenFinalBar.setScale(2, RoundingMode.HALF_UP), nonBaseCPBarDecision.getFinalBAR());
    }

    @Test
    public void handleCPBarDecisionChangeException() {
        assertThrows(Exception.class, () -> {
            CPDecisionBAROutput cpDecisionBAROutput = new CPDecisionBAROutput();
            Product product = new Product();
            product.setName("Test");
            product.setId(1);
            cpDecisionBAROutput.setProduct(product);
            AccomType accomType = new AccomType();
            accomType.setAccomTypeCode("CODE");
            cpDecisionBAROutput.setAccomType(accomType);
            cpDecisionBAROutput.setDecisionId(2);

            CPBarDecisionChange cpBarDecisionChange = new CPBarDecisionChange();
            cpBarDecisionChange.setBaseOccupancyRate(BigDecimal.ZERO);
            cpBarDecisionChange.setCpDecisionBAROutput(cpDecisionBAROutput);

            service.handleCPBarDecisionChange(1, null, null, null, null, cpBarDecisionChange, new ArrayList<>(), new ArrayList<>(), new HashMap<>(), null);
        });
    }

    @Test
    public void setFinalBarUsingPreviousFinalBarForHiltonSendAdjustment_fixedOffset() {
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.AGILE_RATES_ENABLED.value())).thenReturn(true);
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.HILTON_OPTION_TO_SEND_ADJUSTMENT_FOR_AGILE_ENABLED)).thenReturn(true);
        Product barProduct = tenantCrudService().findAll(Product.class).get(0);
        Product linkedProduct = addAgileRatesProduct("Test Product", 1);
        linkedProduct.setDecisionsSentBy(AgileRatesDecisionsSentBy.ADJUSTMENT);
        linkedProduct.setMinimumPriceChange(BigDecimal.valueOf(200));
        linkedProduct.setOffsetMethod(AgileRatesOffsetMethod.FIXED);
        addTransientPricingBaseAccomType(STE, BigDecimal.valueOf(10), BigDecimal.valueOf(200), BigDecimal.ZERO);
        updatePricingAccomClassData(STE, MinimumIncrementMethod.FIXED_OFFSET, BigDecimal.valueOf(200), false);
        CPDecisionBAROutput cpDecisionBAROutputForBar = addCPDecisionBarOutput(startDate, STE, new BigDecimal("123.00"), barProduct);
        CPDecisionBAROutput cpDecisionBAROutputForAgile = addCPDecisionBarOutput(startDate, STE, new BigDecimal("100.00"), linkedProduct);
        addDecisionDailybarOutput(STE, startDate, new BigDecimal("150.00"), new BigDecimal("150.00"), BigDecimal.ZERO, BigDecimal.ZERO, barProduct);
        addDecisionDailybarOutput(STE, startDate, new BigDecimal("-20.00"), new BigDecimal("-20.00"), BigDecimal.ZERO, BigDecimal.ZERO, linkedProduct);
        service.roundOptimalBARs(startDate, endDate);
        // this workaround to fetch CPDecisionBAROutput entity back from db is needed since, entity is getting detached in the flow.
        CPDecisionBAROutput cpDecisionBAROutputForAgileSaved = tenantCrudService().findAll(CPDecisionBAROutput.class)
                .stream().filter(cpDecisionBAROutput -> !cpDecisionBAROutput.getProduct().isSystemDefaultOrIndependentProduct()).findFirst().orElseThrow();
        assertTrue(BigDecimalUtil.equals(cpDecisionBAROutputForAgileSaved.getFinalBAR(), BigDecimal.valueOf(130.00)));
        assertTrue(BigDecimalUtil.equals(cpDecisionBAROutputForAgileSaved.getPreviousBAR(), BigDecimal.valueOf(130.00)));
    }

    @Test
    public void setFinalBarUsingPreviousFinalBarForHiltonSendAdjustment_fixedPercent() {
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.AGILE_RATES_ENABLED.value())).thenReturn(true);
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.HILTON_OPTION_TO_SEND_ADJUSTMENT_FOR_AGILE_ENABLED)).thenReturn(true);
        Product barProduct = tenantCrudService().findAll(Product.class).get(0);
        Product linkedProduct = addAgileRatesProduct("Test Product", 1);
        linkedProduct.setDecisionsSentBy(AgileRatesDecisionsSentBy.ADJUSTMENT);
        linkedProduct.setMinimumPriceChange(BigDecimal.valueOf(200));
        linkedProduct.setOffsetMethod(AgileRatesOffsetMethod.PERCENTAGE);
        addTransientPricingBaseAccomType(STE, BigDecimal.valueOf(10), BigDecimal.valueOf(200), BigDecimal.ZERO);
        updatePricingAccomClassData(STE, MinimumIncrementMethod.FIXED_OFFSET, BigDecimal.valueOf(200), false);
        CPDecisionBAROutput cpDecisionBAROutputForBar = addCPDecisionBarOutput(startDate, STE, new BigDecimal("123.00"), barProduct);
        CPDecisionBAROutput cpDecisionBAROutputForAgile = addCPDecisionBarOutput(startDate, STE, new BigDecimal("100.00"), linkedProduct);
        addDecisionDailybarOutput(STE, startDate, new BigDecimal("150.00"), new BigDecimal("150.00"), BigDecimal.ZERO, BigDecimal.ZERO, barProduct);
        addDecisionDailybarOutput(STE, startDate, new BigDecimal("-20.00"), new BigDecimal("-20.00"), BigDecimal.ZERO, BigDecimal.ZERO, linkedProduct);
        service.roundOptimalBARs(startDate, endDate);
        CPDecisionBAROutput cpDecisionBAROutputForAgileSaved = tenantCrudService().findAll(CPDecisionBAROutput.class)
                .stream().filter(cpDecisionBAROutput -> !cpDecisionBAROutput.getProduct().isSystemDefaultOrIndependentProduct()).findFirst().orElseThrow();
        assertTrue(BigDecimalUtil.equals(cpDecisionBAROutputForAgileSaved.getFinalBAR(), BigDecimal.valueOf(120.00)));
        assertTrue(BigDecimalUtil.equals(cpDecisionBAROutputForAgileSaved.getPreviousBAR(), BigDecimal.valueOf(120.00)));
    }

    @Test
    public void setFinalBarUsingSingleRateForHiltonSendAdjustmentNotEnabled() {
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.AGILE_RATES_ENABLED.value())).thenReturn(true);
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.HILTON_OPTION_TO_SEND_ADJUSTMENT_FOR_AGILE_ENABLED)).thenReturn(false);
        Product barProduct = tenantCrudService().findAll(Product.class).get(0);
        Product linkedProduct = addAgileRatesProduct("Test Product", 1);
        linkedProduct.setDecisionsSentBy(AgileRatesDecisionsSentBy.ADJUSTMENT);
        linkedProduct.setMinimumPriceChange(BigDecimal.valueOf(200));
        linkedProduct.setOffsetMethod(AgileRatesOffsetMethod.FIXED);
        addTransientPricingBaseAccomType(STE, BigDecimal.valueOf(10), BigDecimal.valueOf(200), BigDecimal.ZERO);
        updatePricingAccomClassData(STE, MinimumIncrementMethod.FIXED_OFFSET, BigDecimal.valueOf(200), false);
        CPDecisionBAROutput cpDecisionBAROutputForBar = addCPDecisionBarOutput(startDate, STE, new BigDecimal("123.00"), barProduct);
        CPDecisionBAROutput cpDecisionBAROutputForAgile = addCPDecisionBarOutput(startDate, STE, new BigDecimal("100.00"), linkedProduct);
        addDecisionDailybarOutput(STE, startDate, new BigDecimal("150.00"), new BigDecimal("150.00"), BigDecimal.ZERO, BigDecimal.ZERO, barProduct);
        addDecisionDailybarOutput(STE, startDate, new BigDecimal("140.00"), new BigDecimal("140.00"), BigDecimal.ZERO, BigDecimal.ZERO, linkedProduct);
        service.roundOptimalBARs(startDate, endDate);
        CPDecisionBAROutput cpDecisionBAROutputForAgileSaved = tenantCrudService().findAll(CPDecisionBAROutput.class)
                .stream().filter(cpDecisionBAROutput -> !cpDecisionBAROutput.getProduct().isSystemDefaultOrIndependentProduct()).findFirst().orElseThrow();
        assertTrue(BigDecimalUtil.equals(cpDecisionBAROutputForAgileSaved.getFinalBAR(), BigDecimal.valueOf(140.00)));
        assertTrue(BigDecimalUtil.equals(cpDecisionBAROutputForAgileSaved.getPreviousBAR(), BigDecimal.valueOf(140.00)));
    }

    @Test
    public void setFinalBarUsingSingleRateForHiltonSendDecisionEnabledByPrice() {
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.AGILE_RATES_ENABLED.value())).thenReturn(true);
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.HILTON_OPTION_TO_SEND_ADJUSTMENT_FOR_AGILE_ENABLED)).thenReturn(true);
        Product barProduct = tenantCrudService().findAll(Product.class).get(0);
        Product linkedProduct = addAgileRatesProduct("Test Product", 1);
        linkedProduct.setDecisionsSentBy(AgileRatesDecisionsSentBy.PRICE);
        linkedProduct.setMinimumPriceChange(BigDecimal.valueOf(200));
        linkedProduct.setOffsetMethod(AgileRatesOffsetMethod.FIXED);
        addTransientPricingBaseAccomType(STE, BigDecimal.valueOf(10), BigDecimal.valueOf(200), BigDecimal.ZERO);
        updatePricingAccomClassData(STE, MinimumIncrementMethod.FIXED_OFFSET, BigDecimal.valueOf(200), false);
        CPDecisionBAROutput cpDecisionBAROutputForBar = addCPDecisionBarOutput(startDate, STE, new BigDecimal("123.00"), barProduct);
        CPDecisionBAROutput cpDecisionBAROutputForAgile = addCPDecisionBarOutput(startDate, STE, new BigDecimal("100.00"), linkedProduct);
        addDecisionDailybarOutput(STE, startDate, new BigDecimal("150.00"), new BigDecimal("150.00"), BigDecimal.ZERO, BigDecimal.ZERO, barProduct);
        addDecisionDailybarOutput(STE, startDate, new BigDecimal("140.00"), new BigDecimal("140.00"), BigDecimal.ZERO, BigDecimal.ZERO, linkedProduct);
        service.roundOptimalBARs(startDate, endDate);
        CPDecisionBAROutput cpDecisionBAROutputForAgileSaved = tenantCrudService().findAll(CPDecisionBAROutput.class)
                .stream().filter(cpDecisionBAROutput -> !cpDecisionBAROutput.getProduct().isSystemDefaultOrIndependentProduct()).findFirst().orElseThrow();
        assertTrue(BigDecimalUtil.equals(cpDecisionBAROutputForAgileSaved.getFinalBAR(), BigDecimal.valueOf(140.00)));
        assertTrue(BigDecimalUtil.equals(cpDecisionBAROutputForAgileSaved.getPreviousBAR(), BigDecimal.valueOf(140.00)));
    }

    @Test
    public void setFinalBarUsingPreviousFinalBarForHiltonSendAdjustment_fixedOffset_RoundingRule_None() {
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.AGILE_RATES_ENABLED.value())).thenReturn(true);
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.HILTON_OPTION_TO_SEND_ADJUSTMENT_FOR_AGILE_ENABLED)).thenReturn(true);
        Product barProduct = tenantCrudService().findAll(Product.class).get(0);
        Product linkedProduct = addAgileRatesProduct("Test Product", 1);
        linkedProduct.setDecisionsSentBy(AgileRatesDecisionsSentBy.ADJUSTMENT);
        linkedProduct.setMinimumPriceChange(BigDecimal.valueOf(200));
        linkedProduct.setOffsetMethod(AgileRatesOffsetMethod.FIXED);
        linkedProduct.setRoundingRule(RoundingRule.NONE);
        addTransientPricingBaseAccomType(STE, BigDecimal.valueOf(10), BigDecimal.valueOf(200), BigDecimal.ZERO);
        updatePricingAccomClassData(STE, MinimumIncrementMethod.FIXED_OFFSET, BigDecimal.valueOf(200), false);
        CPDecisionBAROutput cpDecisionBAROutputForBar = addCPDecisionBarOutput(startDate, STE, new BigDecimal("123.00"), barProduct);
        CPDecisionBAROutput cpDecisionBAROutputForAgile = addCPDecisionBarOutput(startDate, STE, new BigDecimal("100.00"), linkedProduct);
        addDecisionDailybarOutput(STE, startDate, new BigDecimal("150.17"), new BigDecimal("150.17"), BigDecimal.ZERO, BigDecimal.ZERO, barProduct);
        addDecisionDailybarOutput(STE, startDate, new BigDecimal("-20.00"), new BigDecimal("-20.00"), BigDecimal.ZERO, BigDecimal.ZERO, linkedProduct);
        service.roundOptimalBARs(startDate, endDate);
        // this workaround to fetch CPDecisionBAROutput entity back from db is needed since, entity is getting detached in the flow.
        CPDecisionBAROutput cpDecisionBAROutputForAgileSaved = tenantCrudService().findAll(CPDecisionBAROutput.class)
                .stream().filter(cpDecisionBAROutput -> !cpDecisionBAROutput.getProduct().isSystemDefaultOrIndependentProduct()).findFirst().orElseThrow();
        assertTrue(BigDecimalUtil.equals(cpDecisionBAROutputForAgileSaved.getFinalBAR(), BigDecimal.valueOf(130.17)));
        assertTrue(BigDecimalUtil.equals(cpDecisionBAROutputForAgileSaved.getPreviousBAR(), BigDecimal.valueOf(130.17)));
    }

    @Test
    public void setFinalBarUsingPreviousFinalBarForHiltonSendAdjustment_fixedOffset_RoundingRule_Up() {
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.AGILE_RATES_ENABLED.value())).thenReturn(true);
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.HILTON_OPTION_TO_SEND_ADJUSTMENT_FOR_AGILE_ENABLED)).thenReturn(true);
        Product barProduct = tenantCrudService().findAll(Product.class).get(0);
        Product linkedProduct = addAgileRatesProduct("Test Product", 1);
        linkedProduct.setDecisionsSentBy(AgileRatesDecisionsSentBy.ADJUSTMENT);
        linkedProduct.setMinimumPriceChange(BigDecimal.valueOf(200));
        linkedProduct.setOffsetMethod(AgileRatesOffsetMethod.FIXED);
        linkedProduct.setRoundingRule(RoundingRule.UP);
        addTransientPricingBaseAccomType(STE, BigDecimal.valueOf(10), BigDecimal.valueOf(200), BigDecimal.ZERO);
        updatePricingAccomClassData(STE, MinimumIncrementMethod.FIXED_OFFSET, BigDecimal.valueOf(200), false);
        CPDecisionBAROutput cpDecisionBAROutputForBar = addCPDecisionBarOutput(startDate, STE, new BigDecimal("123.00"), barProduct);
        CPDecisionBAROutput cpDecisionBAROutputForAgile = addCPDecisionBarOutput(startDate, STE, new BigDecimal("100.00"), linkedProduct);
        addDecisionDailybarOutput(STE, startDate, new BigDecimal("150.17"), new BigDecimal("150.17"), BigDecimal.ZERO, BigDecimal.ZERO, barProduct);
        addDecisionDailybarOutput(STE, startDate, new BigDecimal("-20.00"), new BigDecimal("-20.00"), BigDecimal.ZERO, BigDecimal.ZERO, linkedProduct);
        service.roundOptimalBARs(startDate, endDate);
        // this workaround to fetch CPDecisionBAROutput entity back from db is needed since, entity is getting detached in the flow.
        CPDecisionBAROutput cpDecisionBAROutputForAgileSaved = tenantCrudService().findAll(CPDecisionBAROutput.class)
                .stream().filter(cpDecisionBAROutput -> !cpDecisionBAROutput.getProduct().isSystemDefaultOrIndependentProduct()).findFirst().orElseThrow();
        assertTrue(BigDecimalUtil.equals(cpDecisionBAROutputForAgileSaved.getFinalBAR(), BigDecimal.valueOf(131)));
        assertTrue(BigDecimalUtil.equals(cpDecisionBAROutputForAgileSaved.getPreviousBAR(), BigDecimal.valueOf(131)));
    }

    @Test
    public void setFinalBarUsingPreviousFinalBarForHiltonSendAdjustment_fixedOffset_RoundingRule_Down() {
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.AGILE_RATES_ENABLED.value())).thenReturn(true);
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.HILTON_OPTION_TO_SEND_ADJUSTMENT_FOR_AGILE_ENABLED)).thenReturn(true);
        Product barProduct = tenantCrudService().findAll(Product.class).get(0);
        Product linkedProduct = addAgileRatesProduct("Test Product", 1);
        linkedProduct.setDecisionsSentBy(AgileRatesDecisionsSentBy.ADJUSTMENT);
        linkedProduct.setMinimumPriceChange(BigDecimal.valueOf(200));
        linkedProduct.setOffsetMethod(AgileRatesOffsetMethod.FIXED);
        linkedProduct.setRoundingRule(RoundingRule.DOWN);
        addTransientPricingBaseAccomType(STE, BigDecimal.valueOf(10), BigDecimal.valueOf(200), BigDecimal.ZERO);
        updatePricingAccomClassData(STE, MinimumIncrementMethod.FIXED_OFFSET, BigDecimal.valueOf(200), false);
        CPDecisionBAROutput cpDecisionBAROutputForBar = addCPDecisionBarOutput(startDate, STE, new BigDecimal("123.00"), barProduct);
        CPDecisionBAROutput cpDecisionBAROutputForAgile = addCPDecisionBarOutput(startDate, STE, new BigDecimal("100.00"), linkedProduct);
        addDecisionDailybarOutput(STE, startDate, new BigDecimal("150.17"), new BigDecimal("150.17"), BigDecimal.ZERO, BigDecimal.ZERO, barProduct);
        addDecisionDailybarOutput(STE, startDate, new BigDecimal("-20.00"), new BigDecimal("-20.00"), BigDecimal.ZERO, BigDecimal.ZERO, linkedProduct);
        service.roundOptimalBARs(startDate, endDate);
        // this workaround to fetch CPDecisionBAROutput entity back from db is needed since, entity is getting detached in the flow.
        CPDecisionBAROutput cpDecisionBAROutputForAgileSaved = tenantCrudService().findAll(CPDecisionBAROutput.class)
                .stream().filter(cpDecisionBAROutput -> !cpDecisionBAROutput.getProduct().isSystemDefaultOrIndependentProduct()).findFirst().orElseThrow();
        assertTrue(BigDecimalUtil.equals(cpDecisionBAROutputForAgileSaved.getFinalBAR(), BigDecimal.valueOf(130)));
        assertTrue(BigDecimalUtil.equals(cpDecisionBAROutputForAgileSaved.getPreviousBAR(), BigDecimal.valueOf(130)));
    }

    @Test
    public void setFinalBarUsingPreviousFinalBarForHiltonSendAdjustment_fixedOffset_RoundingRule_Whole_floor() {
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.AGILE_RATES_ENABLED.value())).thenReturn(true);
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.HILTON_OPTION_TO_SEND_ADJUSTMENT_FOR_AGILE_ENABLED)).thenReturn(true);
        Product barProduct = tenantCrudService().findAll(Product.class).get(0);
        Product linkedProduct = addAgileRatesProduct("Test Product", 1);
        linkedProduct.setDecisionsSentBy(AgileRatesDecisionsSentBy.ADJUSTMENT);
        linkedProduct.setMinimumPriceChange(BigDecimal.valueOf(200));
        linkedProduct.setOffsetMethod(AgileRatesOffsetMethod.FIXED);
        linkedProduct.setRoundingRule(RoundingRule.WHOLE);
        addTransientPricingBaseAccomType(STE, BigDecimal.valueOf(10), BigDecimal.valueOf(200), BigDecimal.ZERO);
        updatePricingAccomClassData(STE, MinimumIncrementMethod.FIXED_OFFSET, BigDecimal.valueOf(200), false);
        CPDecisionBAROutput cpDecisionBAROutputForBar = addCPDecisionBarOutput(startDate, STE, new BigDecimal("123.00"), barProduct);
        CPDecisionBAROutput cpDecisionBAROutputForAgile = addCPDecisionBarOutput(startDate, STE, new BigDecimal("100.00"), linkedProduct);
        addDecisionDailybarOutput(STE, startDate, new BigDecimal("150.17"), new BigDecimal("150.17"), BigDecimal.ZERO, BigDecimal.ZERO, barProduct);
        addDecisionDailybarOutput(STE, startDate, new BigDecimal("-20.00"), new BigDecimal("-20.00"), BigDecimal.ZERO, BigDecimal.ZERO, linkedProduct);
        service.roundOptimalBARs(startDate, endDate);
        // this workaround to fetch CPDecisionBAROutput entity back from db is needed since, entity is getting detached in the flow.
        CPDecisionBAROutput cpDecisionBAROutputForAgileSaved = tenantCrudService().findAll(CPDecisionBAROutput.class)
                .stream().filter(cpDecisionBAROutput -> !cpDecisionBAROutput.getProduct().isSystemDefaultOrIndependentProduct()).findFirst().orElseThrow();
        assertTrue(BigDecimalUtil.equals(cpDecisionBAROutputForAgileSaved.getFinalBAR(), BigDecimal.valueOf(130)));
        assertTrue(BigDecimalUtil.equals(cpDecisionBAROutputForAgileSaved.getPreviousBAR(), BigDecimal.valueOf(130)));
    }

    @Test
    public void setFinalBarUsingPreviousFinalBarForHiltonSendAdjustment_fixedOffset_RoundingRule_Whole_ceil() {
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.AGILE_RATES_ENABLED.value())).thenReturn(true);
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.HILTON_OPTION_TO_SEND_ADJUSTMENT_FOR_AGILE_ENABLED)).thenReturn(true);
        Product barProduct = tenantCrudService().findAll(Product.class).get(0);
        Product linkedProduct = addAgileRatesProduct("Test Product", 1);
        linkedProduct.setDecisionsSentBy(AgileRatesDecisionsSentBy.ADJUSTMENT);
        linkedProduct.setMinimumPriceChange(BigDecimal.valueOf(200));
        linkedProduct.setOffsetMethod(AgileRatesOffsetMethod.FIXED);
        linkedProduct.setRoundingRule(RoundingRule.WHOLE);
        addTransientPricingBaseAccomType(STE, BigDecimal.valueOf(10), BigDecimal.valueOf(200), BigDecimal.ZERO);
        updatePricingAccomClassData(STE, MinimumIncrementMethod.FIXED_OFFSET, BigDecimal.valueOf(200), false);
        CPDecisionBAROutput cpDecisionBAROutputForBar = addCPDecisionBarOutput(startDate, STE, new BigDecimal("123.00"), barProduct);
        CPDecisionBAROutput cpDecisionBAROutputForAgile = addCPDecisionBarOutput(startDate, STE, new BigDecimal("100.00"), linkedProduct);
        addDecisionDailybarOutput(STE, startDate, new BigDecimal("150.97"), new BigDecimal("150.17"), BigDecimal.ZERO, BigDecimal.ZERO, barProduct);
        addDecisionDailybarOutput(STE, startDate, new BigDecimal("-20.00"), new BigDecimal("-20.00"), BigDecimal.ZERO, BigDecimal.ZERO, linkedProduct);
        service.roundOptimalBARs(startDate, endDate);
        // this workaround to fetch CPDecisionBAROutput entity back from db is needed since, entity is getting detached in the flow.
        CPDecisionBAROutput cpDecisionBAROutputForAgileSaved = tenantCrudService().findAll(CPDecisionBAROutput.class)
                .stream().filter(cpDecisionBAROutput -> !cpDecisionBAROutput.getProduct().isSystemDefaultOrIndependentProduct()).findFirst().orElseThrow();
        assertTrue(BigDecimalUtil.equals(cpDecisionBAROutputForAgileSaved.getFinalBAR(), BigDecimal.valueOf(131)));
        assertTrue(BigDecimalUtil.equals(cpDecisionBAROutputForAgileSaved.getPreviousBAR(), BigDecimal.valueOf(131)));
    }

    @Test
    public void setFinalBarUsingPreviousFinalBarForHiltonSendAdjustment_fixedOffset_RoundingRule_price_rounding() {
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.AGILE_RATES_ENABLED.value())).thenReturn(true);
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.HILTON_OPTION_TO_SEND_ADJUSTMENT_FOR_AGILE_ENABLED)).thenReturn(true);
        setRoundingRule(null, 8, 1);
        Product barProduct = tenantCrudService().findAll(Product.class).get(0);
        Product linkedProduct = addAgileRatesProduct("Test Product", 1);
        linkedProduct.setDecisionsSentBy(AgileRatesDecisionsSentBy.ADJUSTMENT);
        linkedProduct.setMinimumPriceChange(BigDecimal.valueOf(200));
        linkedProduct.setOffsetMethod(AgileRatesOffsetMethod.FIXED);
        linkedProduct.setRoundingRule(RoundingRule.PRICE_ROUNDING);
        addTransientPricingBaseAccomType(STE, BigDecimal.valueOf(10), BigDecimal.valueOf(200), BigDecimal.ZERO);
        updatePricingAccomClassData(STE, MinimumIncrementMethod.FIXED_OFFSET, BigDecimal.valueOf(200), false);
        CPDecisionBAROutput cpDecisionBAROutputForBar = addCPDecisionBarOutput(startDate, STE, new BigDecimal("123.00"), barProduct);
        CPDecisionBAROutput cpDecisionBAROutputForAgile = addCPDecisionBarOutput(startDate, STE, new BigDecimal("100.00"), linkedProduct);
        addDecisionDailybarOutput(STE, startDate, new BigDecimal("150.97"), new BigDecimal("150.17"), BigDecimal.ZERO, BigDecimal.ZERO, barProduct);
        addDecisionDailybarOutput(STE, startDate, new BigDecimal("-20.00"), new BigDecimal("-20.00"), BigDecimal.ZERO, BigDecimal.ZERO, linkedProduct);
        service.roundOptimalBARs(startDate, endDate);
        // this workaround to fetch CPDecisionBAROutput entity back from db is needed since, entity is getting detached in the flow.
        CPDecisionBAROutput cpDecisionBAROutputForAgileSaved = tenantCrudService().findAll(CPDecisionBAROutput.class)
                .stream().filter(cpDecisionBAROutput -> !cpDecisionBAROutput.getProduct().isSystemDefaultOrIndependentProduct()).findFirst().orElseThrow();
        assertTrue(BigDecimalUtil.equals(cpDecisionBAROutputForAgileSaved.getFinalBAR(), BigDecimal.valueOf(130.81)));
        assertTrue(BigDecimalUtil.equals(cpDecisionBAROutputForAgileSaved.getPreviousBAR(), BigDecimal.valueOf(130.81)));
    }

    @Test
    public void setFinalBarUsingPreviousFinalBarForHiltonSendAdjustment_fixedOffset_RoundingRule_price_rounding1() {
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.AGILE_RATES_ENABLED.value())).thenReturn(true);
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.HILTON_OPTION_TO_SEND_ADJUSTMENT_FOR_AGILE_ENABLED)).thenReturn(true);
        setRoundingRule(null, 8, null);
        Product barProduct = tenantCrudService().findAll(Product.class).get(0);
        Product linkedProduct = addAgileRatesProduct("Test Product", 1);
        linkedProduct.setDecisionsSentBy(AgileRatesDecisionsSentBy.ADJUSTMENT);
        linkedProduct.setMinimumPriceChange(BigDecimal.valueOf(200));
        linkedProduct.setOffsetMethod(AgileRatesOffsetMethod.FIXED);
        linkedProduct.setRoundingRule(RoundingRule.PRICE_ROUNDING);
        addTransientPricingBaseAccomType(STE, BigDecimal.valueOf(10), BigDecimal.valueOf(200), BigDecimal.ZERO);
        updatePricingAccomClassData(STE, MinimumIncrementMethod.FIXED_OFFSET, BigDecimal.valueOf(200), false);
        CPDecisionBAROutput cpDecisionBAROutputForBar = addCPDecisionBarOutput(startDate, STE, new BigDecimal("123.00"), barProduct);
        CPDecisionBAROutput cpDecisionBAROutputForAgile = addCPDecisionBarOutput(startDate, STE, new BigDecimal("100.00"), linkedProduct);
        addDecisionDailybarOutput(STE, startDate, new BigDecimal("150.97"), new BigDecimal("150.17"), BigDecimal.ZERO, BigDecimal.ZERO, barProduct);
        addDecisionDailybarOutput(STE, startDate, new BigDecimal("-20.00"), new BigDecimal("-20.00"), BigDecimal.ZERO, BigDecimal.ZERO, linkedProduct);
        service.roundOptimalBARs(startDate, endDate);
        // this workaround to fetch CPDecisionBAROutput entity back from db is needed since, entity is getting detached in the flow.
        CPDecisionBAROutput cpDecisionBAROutputForAgileSaved = tenantCrudService().findAll(CPDecisionBAROutput.class)
                .stream().filter(cpDecisionBAROutput -> !cpDecisionBAROutput.getProduct().isSystemDefaultOrIndependentProduct()).findFirst().orElseThrow();
        assertTrue(BigDecimalUtil.equals(cpDecisionBAROutputForAgileSaved.getFinalBAR(), BigDecimal.valueOf(130.89)));
        assertTrue(BigDecimalUtil.equals(cpDecisionBAROutputForAgileSaved.getPreviousBAR(), BigDecimal.valueOf(130.89)));
    }

    @Test
    public void setFinalBarUsingPreviousFinalBarForHiltonSendAdjustment_fixedOffset_RoundingRule_price_rounding2() {
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.AGILE_RATES_ENABLED.value())).thenReturn(true);
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.HILTON_OPTION_TO_SEND_ADJUSTMENT_FOR_AGILE_ENABLED)).thenReturn(true);
        setRoundingRule(null, 0, null);
        Product barProduct = tenantCrudService().findAll(Product.class).get(0);
        Product linkedProduct = addAgileRatesProduct("Test Product", 1);
        linkedProduct.setDecisionsSentBy(AgileRatesDecisionsSentBy.ADJUSTMENT);
        linkedProduct.setMinimumPriceChange(BigDecimal.valueOf(200));
        linkedProduct.setOffsetMethod(AgileRatesOffsetMethod.FIXED);
        linkedProduct.setRoundingRule(RoundingRule.PRICE_ROUNDING);
        addTransientPricingBaseAccomType(STE, BigDecimal.valueOf(10), BigDecimal.valueOf(200), BigDecimal.ZERO);
        updatePricingAccomClassData(STE, MinimumIncrementMethod.FIXED_OFFSET, BigDecimal.valueOf(200), false);
        CPDecisionBAROutput cpDecisionBAROutputForBar = addCPDecisionBarOutput(startDate, STE, new BigDecimal("123.00"), barProduct);
        CPDecisionBAROutput cpDecisionBAROutputForAgile = addCPDecisionBarOutput(startDate, STE, new BigDecimal("100.00"), linkedProduct);
        addDecisionDailybarOutput(STE, startDate, new BigDecimal("150.97"), new BigDecimal("150.17"), BigDecimal.ZERO, BigDecimal.ZERO, barProduct);
        addDecisionDailybarOutput(STE, startDate, new BigDecimal("-20.00"), new BigDecimal("-20.00"), BigDecimal.ZERO, BigDecimal.ZERO, linkedProduct);
        service.roundOptimalBARs(startDate, endDate);
        // this workaround to fetch CPDecisionBAROutput entity back from db is needed since, entity is getting detached in the flow.
        CPDecisionBAROutput cpDecisionBAROutputForAgileSaved = tenantCrudService().findAll(CPDecisionBAROutput.class)
                .stream().filter(cpDecisionBAROutput -> !cpDecisionBAROutput.getProduct().isSystemDefaultOrIndependentProduct()).findFirst().orElseThrow();
        assertTrue(BigDecimalUtil.equals(cpDecisionBAROutputForAgileSaved.getFinalBAR(), BigDecimal.valueOf(131.00)));
        assertTrue(BigDecimalUtil.equals(cpDecisionBAROutputForAgileSaved.getPreviousBAR(), BigDecimal.valueOf(131.00)));
    }

    @Test
    public void setFinalBarUsingPreviousFinalBarForHiltonSendAdjustment_fixedOffset_RoundingRule_price_rounding3() {
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.AGILE_RATES_ENABLED.value())).thenReturn(true);
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.HILTON_OPTION_TO_SEND_ADJUSTMENT_FOR_AGILE_ENABLED)).thenReturn(true);
        setRoundingRule(null, null, 0);
        Product barProduct = tenantCrudService().findAll(Product.class).get(0);
        Product linkedProduct = addAgileRatesProduct("Test Product", 1);
        linkedProduct.setDecisionsSentBy(AgileRatesDecisionsSentBy.ADJUSTMENT);
        linkedProduct.setMinimumPriceChange(BigDecimal.valueOf(200));
        linkedProduct.setOffsetMethod(AgileRatesOffsetMethod.FIXED);
        linkedProduct.setRoundingRule(RoundingRule.PRICE_ROUNDING);
        addTransientPricingBaseAccomType(STE, BigDecimal.valueOf(10), BigDecimal.valueOf(200), BigDecimal.ZERO);
        updatePricingAccomClassData(STE, MinimumIncrementMethod.FIXED_OFFSET, BigDecimal.valueOf(200), false);
        CPDecisionBAROutput cpDecisionBAROutputForBar = addCPDecisionBarOutput(startDate, STE, new BigDecimal("123.00"), barProduct);
        CPDecisionBAROutput cpDecisionBAROutputForAgile = addCPDecisionBarOutput(startDate, STE, new BigDecimal("100.00"), linkedProduct);
        addDecisionDailybarOutput(STE, startDate, new BigDecimal("150.97"), new BigDecimal("150.17"), BigDecimal.ZERO, BigDecimal.ZERO, barProduct);
        addDecisionDailybarOutput(STE, startDate, new BigDecimal("-20.00"), new BigDecimal("-20.00"), BigDecimal.ZERO, BigDecimal.ZERO, linkedProduct);
        service.roundOptimalBARs(startDate, endDate);
        // this workaround to fetch CPDecisionBAROutput entity back from db is needed since, entity is getting detached in the flow.
        CPDecisionBAROutput cpDecisionBAROutputForAgileSaved = tenantCrudService().findAll(CPDecisionBAROutput.class)
                .stream().filter(cpDecisionBAROutput -> !cpDecisionBAROutput.getProduct().isSystemDefaultOrIndependentProduct()).findFirst().orElseThrow();
        assertTrue(BigDecimalUtil.equals(cpDecisionBAROutputForAgileSaved.getFinalBAR(), BigDecimal.valueOf(131.00)));
        assertTrue(BigDecimalUtil.equals(cpDecisionBAROutputForAgileSaved.getPreviousBAR(), BigDecimal.valueOf(131.00)));
    }

    @Test
    public void setFinalBarUsingPreviousFinalBarForHiltonSendAdjustment_fixedOffset_RoundingRule_price_rounding4() {
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.AGILE_RATES_ENABLED.value())).thenReturn(true);
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.HILTON_OPTION_TO_SEND_ADJUSTMENT_FOR_AGILE_ENABLED)).thenReturn(true);
        setRoundingRule(null, null, 5);
        Product barProduct = tenantCrudService().findAll(Product.class).get(0);
        Product linkedProduct = addAgileRatesProduct("Test Product", 1);
        linkedProduct.setDecisionsSentBy(AgileRatesDecisionsSentBy.ADJUSTMENT);
        linkedProduct.setMinimumPriceChange(BigDecimal.valueOf(200));
        linkedProduct.setOffsetMethod(AgileRatesOffsetMethod.FIXED);
        linkedProduct.setRoundingRule(RoundingRule.PRICE_ROUNDING);
        addTransientPricingBaseAccomType(STE, BigDecimal.valueOf(10), BigDecimal.valueOf(200), BigDecimal.ZERO);
        updatePricingAccomClassData(STE, MinimumIncrementMethod.FIXED_OFFSET, BigDecimal.valueOf(200), false);
        CPDecisionBAROutput cpDecisionBAROutputForBar = addCPDecisionBarOutput(startDate, STE, new BigDecimal("123.00"), barProduct);
        CPDecisionBAROutput cpDecisionBAROutputForAgile = addCPDecisionBarOutput(startDate, STE, new BigDecimal("100.00"), linkedProduct);
        addDecisionDailybarOutput(STE, startDate, new BigDecimal("150.97"), new BigDecimal("150.17"), BigDecimal.ZERO, BigDecimal.ZERO, barProduct);
        addDecisionDailybarOutput(STE, startDate, new BigDecimal("-20.00"), new BigDecimal("-20.00"), BigDecimal.ZERO, BigDecimal.ZERO, linkedProduct);
        service.roundOptimalBARs(startDate, endDate);
        // this workaround to fetch CPDecisionBAROutput entity back from db is needed since, entity is getting detached in the flow.
        CPDecisionBAROutput cpDecisionBAROutputForAgileSaved = tenantCrudService().findAll(CPDecisionBAROutput.class)
                .stream().filter(cpDecisionBAROutput -> !cpDecisionBAROutput.getProduct().isSystemDefaultOrIndependentProduct()).findFirst().orElseThrow();
        assertTrue(BigDecimalUtil.equals(cpDecisionBAROutputForAgileSaved.getFinalBAR(), BigDecimal.valueOf(130.95))); // 130.95
        assertTrue(BigDecimalUtil.equals(cpDecisionBAROutputForAgileSaved.getPreviousBAR(), BigDecimal.valueOf(130.95)));
    }

    @Test
    public void testNegativeFinalBARNotAllowed() {
        Integer decisionId = 1;
        RateUnqualified defaultRateUnqualified = new RateUnqualified();
        CPDecisionContext cpDecisionContext = mock(CPDecisionContext.class);
        when(cpDecisionContext.isConsortiaFreeNightEnabled()).thenReturn(false);
        TableBatch tableBatch = null;
        TableBatch tableBatchNonHiltonCRS = null;
        CPBarDecisionChange cpBarDecisionChange = new CPBarDecisionChange();
        CPDecisionBAROutput cpDecisionBAROutput = new CPDecisionBAROutput();
        Product primaryProduct = new Product();
        primaryProduct.setName("BAR");
        AccomType accomType = new AccomType();
        accomType.setAccomTypeCode("STD");
        accomType.setName("STD");
        cpDecisionBAROutput.setAccomType(accomType);
        cpDecisionBAROutput.setProduct(primaryProduct);
        cpBarDecisionChange.setCpDecisionBAROutput(cpDecisionBAROutput);
        cpBarDecisionChange.setBaseOccupancyRate(BigDecimal.valueOf(-10.00));
        List<CPDecisionBAROutput> productOutputsForComparison = new ArrayList<>();
        List<DecisionDailybarOutput> barDecisionDailybarOutputs = new ArrayList<>();
        Map<DecisionDailybarOutputKey, DecisionDailybarOutput> previousDecisionDailybarOutputs = new HashMap<>();

        TetrisException exception = assertThrows(TetrisException.class,
                () -> service.handleCPBarDecisionChange(decisionId, defaultRateUnqualified, cpDecisionContext,
                        tableBatch, tableBatchNonHiltonCRS, cpBarDecisionChange, productOutputsForComparison,
                        barDecisionDailybarOutputs, previousDecisionDailybarOutputs, primaryProduct));
        assertTrue(exception.getMessage().contains("Rate Error:Final Bar is set to -10"));
    }

    @Test
    public void testNegativeFinalBARAfterAdjustmentNotAllowed() {
        CPBarDecisionChange cpBarDecisionChange = new CPBarDecisionChange();
        CPDecisionBAROutput cpDecisionBAROutput = new CPDecisionBAROutput();
        CPDecisionContext cpDecisionContext = null;
        Product primaryProduct = new Product();
        primaryProduct.setName("BAR");
        primaryProduct.setId(1);
        Product linkedProduct = new Product();
        linkedProduct.setName("Product A");
        linkedProduct.setId(5);
        linkedProduct.setOffsetMethod(AgileRatesOffsetMethod.FIXED);
        AccomType accomType = new AccomType();
        accomType.setAccomTypeCode("STD");
        accomType.setName("STD");
        accomType.setId(1);
        cpDecisionBAROutput.setAccomType(accomType);
        cpDecisionBAROutput.setProduct(linkedProduct);
        LocalDate arrivalDate = LocalDate.now();
        cpDecisionBAROutput.setArrivalDate(arrivalDate);
        cpBarDecisionChange.setCpDecisionBAROutput(cpDecisionBAROutput);
        Map<DecisionDailybarOutputKey, DecisionDailybarOutput> previousDecisionDailybarOutputs = new HashMap<>();
        BigDecimal previousDailybarOutputBaseOccupancyTypeRate = BigDecimal.valueOf(10);
        DecisionDailybarOutput decisionDailybarOutputForBar = new DecisionDailybarOutput();
        decisionDailybarOutputForBar.setDoubleRate(BigDecimal.valueOf(-15));
        DecisionDailybarOutputKey decisionDailybarOutputKeyForBarProduct =
                new DecisionDailybarOutputKey(primaryProduct.getId(), cpDecisionBAROutput.getArrivalDate(), cpDecisionBAROutput.getAccomType().getId());
        previousDecisionDailybarOutputs.put(decisionDailybarOutputKeyForBarProduct, decisionDailybarOutputForBar);
        TetrisException exception = assertThrows(TetrisException.class,
                () -> service.getActualFinalBarUsingAdjustment(cpDecisionBAROutput, cpBarDecisionChange,
                        previousDailybarOutputBaseOccupancyTypeRate, previousDecisionDailybarOutputs, primaryProduct, cpDecisionContext));
        assertTrue(exception.getMessage().contains("Rate Error:Final Bar should be positive. For send decision as adjustment flow, final bar computed to -5"));
    }

    @Test
    void test_differential_no_data_present_in_differential_table_hence_should_insert() {
        // given
        List<CPDecisionBAROutput> existing = insertData();
        int size = existing.size();
        System.setProperty("cp.differential.insert.use.new.procedure", "true");

        // when
        service.insertDecisionsIntoDifferentialTable(startDate, startDate.plusDays(1));
        List<CPPaceDecisionBAROutputDifferential> differentialList1 = tenantCrudService().findAll(CPPaceDecisionBAROutputDifferential.class);

        // then
        assertEquals(size, differentialList1.size());
    }

    @Test
    void test_differential_no_data_changed_hence_should_not_insert() {
        // given
        List<CPDecisionBAROutput> existing = insertData();
        int size = existing.size();
        System.setProperty("cp.differential.insert.use.new.procedure", "true");
        // when
        service.insertDecisionsIntoDifferentialTable(startDate, startDate.plusDays(1));
        List<CPPaceDecisionBAROutputDifferential> differentialList1 = tenantCrudService().findAll(CPPaceDecisionBAROutputDifferential.class);
        service.insertDecisionsIntoDifferentialTable(startDate, startDate.plusDays(1));
        List<CPPaceDecisionBAROutputDifferential> differentialList2 = tenantCrudService().findAll(CPPaceDecisionBAROutputDifferential.class);

        // then
        assertEquals(differentialList1.size(), differentialList2.size());
        assertEquals(new HashSet<>(differentialList1), new HashSet<>(differentialList2));
    }

    @Test
    void test_insert_differential_optimal_bar_changed() {
        // given
        List<CPDecisionBAROutput> existing = insertData();
        int size = existing.size();
        System.setProperty("cp.differential.insert.use.new.procedure", "true");

        // when
        service.insertDecisionsIntoDifferentialTable(startDate, startDate.plusDays(1));
        List<CPPaceDecisionBAROutputDifferential> differentialList1 = tenantCrudService().findAll(CPPaceDecisionBAROutputDifferential.class);
        CPDecisionBAROutput first = existing.get(0);
        BigDecimal newValue = BigDecimal.valueOf(123.45);
        first.setOptimalBAR(newValue);
        tenantCrudService().save(first);
        service.insertDecisionsIntoDifferentialTable(startDate, startDate.plusDays(1));
        List<CPPaceDecisionBAROutputDifferential> differentialList2 = tenantCrudService().findAll(CPPaceDecisionBAROutputDifferential.class);

        // then
        assertEquals(size, differentialList1.size());
        assertEquals(size + 1, differentialList2.size());
        differentialList2.removeAll(differentialList1);
        assertEquals(1, differentialList2.size());
        assertEquals(newValue.doubleValue(), differentialList2.get(0).getOptimalBAR().doubleValue());
    }

    @Test
    void test_insert_differential_pretty_bar_changed() {
        // given
        List<CPDecisionBAROutput> existing = insertData();
        int size = existing.size();
        System.setProperty("cp.differential.insert.use.new.procedure", "true");

        // when
        service.insertDecisionsIntoDifferentialTable(startDate, startDate.plusDays(1));
        List<CPPaceDecisionBAROutputDifferential> differentialList1 = tenantCrudService().findAll(CPPaceDecisionBAROutputDifferential.class);
        CPDecisionBAROutput first = existing.get(0);
        BigDecimal newValue = BigDecimal.valueOf(123.45);
        first.setPrettyBAR(newValue);
        tenantCrudService().save(first);
        service.insertDecisionsIntoDifferentialTable(startDate, startDate.plusDays(1));
        List<CPPaceDecisionBAROutputDifferential> differentialList2 = tenantCrudService().findAll(CPPaceDecisionBAROutputDifferential.class);

        // then
        assertEquals(size, differentialList1.size());
        assertEquals(size + 1, differentialList2.size());
        differentialList2.removeAll(differentialList1);
        assertEquals(1, differentialList2.size());
        assertEquals(newValue.doubleValue(), differentialList2.get(0).getPrettyBAR().doubleValue());
    }

    @Test
    void test_insert_differential_final_bar_changed() {
        // given
        List<CPDecisionBAROutput> existing = insertData();
        int size = existing.size();
        System.setProperty("cp.differential.insert.use.new.procedure", "true");

        // when
        service.insertDecisionsIntoDifferentialTable(startDate, startDate.plusDays(1));
        List<CPPaceDecisionBAROutputDifferential> differentialList1 = tenantCrudService().findAll(CPPaceDecisionBAROutputDifferential.class);
        CPDecisionBAROutput first = existing.get(0);
        BigDecimal newValue = BigDecimal.valueOf(123.45);
        first.setFinalBAR(newValue);
        tenantCrudService().save(first);
        service.insertDecisionsIntoDifferentialTable(startDate, startDate.plusDays(1));
        List<CPPaceDecisionBAROutputDifferential> differentialList2 = tenantCrudService().findAll(CPPaceDecisionBAROutputDifferential.class);

        // then
        assertEquals(size, differentialList1.size());
        assertEquals(size + 1, differentialList2.size());
        differentialList2.removeAll(differentialList1);
        assertEquals(1, differentialList2.size());
        assertEquals(newValue.doubleValue(), differentialList2.get(0).getFinalBAR().doubleValue());
    }

    @Test
    void test_insert_differential_rooms_only_bar_changed() {
        // given
        List<CPDecisionBAROutput> existing = insertData();
        int size = existing.size();
        System.setProperty("cp.differential.insert.use.new.procedure", "true");

        // when
        service.insertDecisionsIntoDifferentialTable(startDate, startDate.plusDays(1));
        List<CPPaceDecisionBAROutputDifferential> differentialList1 = tenantCrudService().findAll(CPPaceDecisionBAROutputDifferential.class);
        CPDecisionBAROutput first = existing.get(0);
        BigDecimal newValue = BigDecimal.valueOf(123.45);
        first.setRoomsOnlyBAR(newValue);
        tenantCrudService().save(first);
        service.insertDecisionsIntoDifferentialTable(startDate, startDate.plusDays(1));
        List<CPPaceDecisionBAROutputDifferential> differentialList2 = tenantCrudService().findAll(CPPaceDecisionBAROutputDifferential.class);

        // then
        assertEquals(size, differentialList1.size());
        assertEquals(size + 1, differentialList2.size());
        differentialList2.removeAll(differentialList1);
        assertEquals(1, differentialList2.size());
        assertEquals(newValue.doubleValue(), differentialList2.get(0).getRoomsOnlyBAR().doubleValue());
    }

    @Test
    void test_insert_differential_adjustment_changed() {
        // given
        List<CPDecisionBAROutput> existing = insertData();
        int size = existing.size();
        System.setProperty("cp.differential.insert.use.new.procedure", "true");

        // when
        service.insertDecisionsIntoDifferentialTable(startDate, startDate.plusDays(1));
        List<CPPaceDecisionBAROutputDifferential> differentialList1 = tenantCrudService().findAll(CPPaceDecisionBAROutputDifferential.class);
        CPDecisionBAROutput first = existing.get(0);
        BigDecimal newValue = BigDecimal.valueOf(123.45);
        first.setAdjustmentValue(newValue);
        tenantCrudService().save(first);
        service.insertDecisionsIntoDifferentialTable(startDate, startDate.plusDays(1));
        List<CPPaceDecisionBAROutputDifferential> differentialList2 = tenantCrudService().findAll(CPPaceDecisionBAROutputDifferential.class);

        // then
        assertEquals(size, differentialList1.size());
        assertEquals(size + 1, differentialList2.size());
        differentialList2.removeAll(differentialList1);
        assertEquals(1, differentialList2.size());
        assertEquals(newValue.doubleValue(), differentialList2.get(0).getAdjustmentValue().doubleValue());
    }

    @Test
    void test_insert_differential_specific_override_changed() {
        // given
        List<CPDecisionBAROutput> existing = insertData();
        int size = existing.size();
        System.setProperty("cp.differential.insert.use.new.procedure", "true");

        // when
        service.insertDecisionsIntoDifferentialTable(startDate, startDate.plusDays(1));
        List<CPPaceDecisionBAROutputDifferential> differentialList1 = tenantCrudService().findAll(CPPaceDecisionBAROutputDifferential.class);
        CPDecisionBAROutput first = existing.get(0);
        BigDecimal newValue = BigDecimal.valueOf(123.45);
        first.setSpecificOverride(newValue);
        tenantCrudService().save(first);
        service.insertDecisionsIntoDifferentialTable(startDate, startDate.plusDays(1));
        List<CPPaceDecisionBAROutputDifferential> differentialList2 = tenantCrudService().findAll(CPPaceDecisionBAROutputDifferential.class);

        // then
        assertEquals(size, differentialList1.size());
        assertEquals(size + 1, differentialList2.size());
        differentialList2.removeAll(differentialList1);
        assertEquals(1, differentialList2.size());
        assertEquals(newValue.doubleValue(), differentialList2.get(0).getSpecificOverride().doubleValue());
    }

    @Test
    void test_insert_differential_ceiling_override_changed() {
        // given
        List<CPDecisionBAROutput> existing = insertData();
        int size = existing.size();
        System.setProperty("cp.differential.insert.use.new.procedure", "true");

        // when
        service.insertDecisionsIntoDifferentialTable(startDate, startDate.plusDays(1));
        List<CPPaceDecisionBAROutputDifferential> differentialList1 = tenantCrudService().findAll(CPPaceDecisionBAROutputDifferential.class);
        CPDecisionBAROutput first = existing.get(0);
        BigDecimal newValue = BigDecimal.valueOf(123.45);
        first.setCeilingOverride(newValue);
        tenantCrudService().save(first);
        service.insertDecisionsIntoDifferentialTable(startDate, startDate.plusDays(1));
        List<CPPaceDecisionBAROutputDifferential> differentialList2 = tenantCrudService().findAll(CPPaceDecisionBAROutputDifferential.class);

        // then
        assertEquals(size, differentialList1.size());
        assertEquals(size + 1, differentialList2.size());
        differentialList2.removeAll(differentialList1);
        assertEquals(1, differentialList2.size());
        assertEquals(newValue.doubleValue(), differentialList2.get(0).getCeilingOverride().doubleValue());
    }

    @Test
    void test_insert_differential_floor_override_changed() {
        // given
        List<CPDecisionBAROutput> existing = insertData();
        int size = existing.size();
        System.setProperty("cp.differential.insert.use.new.procedure", "true");

        // when
        service.insertDecisionsIntoDifferentialTable(startDate, startDate.plusDays(1));
        List<CPPaceDecisionBAROutputDifferential> differentialList1 = tenantCrudService().findAll(CPPaceDecisionBAROutputDifferential.class);
        CPDecisionBAROutput first = existing.get(0);
        BigDecimal newValue = BigDecimal.valueOf(123.45);
        first.setFloorOverride(newValue);
        tenantCrudService().save(first);
        service.insertDecisionsIntoDifferentialTable(startDate, startDate.plusDays(1));
        List<CPPaceDecisionBAROutputDifferential> differentialList2 = tenantCrudService().findAll(CPPaceDecisionBAROutputDifferential.class);

        // then
        assertEquals(size, differentialList1.size());
        assertEquals(size + 1, differentialList2.size());
        differentialList2.removeAll(differentialList1);
        assertEquals(1, differentialList2.size());
        assertEquals(newValue.doubleValue(), differentialList2.get(0).getFloorOverride().doubleValue());
    }

    @Test
    void test_free_night_product_toggle_off() {
        CPDecisionBAROutput cpDecisionBAROutput = new CPDecisionBAROutput();
        CPDecisionContext cpDecisionContext = mock(CPDecisionContext.class);
        when(cpDecisionContext.isConsortiaFreeNightEnabled()).thenReturn(false);
        boolean result = service.isFreeNightProduct(cpDecisionBAROutput, cpDecisionContext);
        assertFalse(result);
    }

    @Test
    void test_free_night_product_toggle_true_product_is_not_free_night() {
        CPDecisionBAROutput cpDecisionBAROutput = new CPDecisionBAROutput();
        Product product = new Product();
        product.setFreeNightEnabled(false);
        cpDecisionBAROutput.setProduct(product);
        CPDecisionContext cpDecisionContext = mock(CPDecisionContext.class);
        when(cpDecisionContext.isConsortiaFreeNightEnabled()).thenReturn(true);
        boolean result = service.isFreeNightProduct(cpDecisionBAROutput, cpDecisionContext);
        assertFalse(result);
    }

    @Test
    void test_free_night_product_toggle_true_product_is_free_night() {
        CPDecisionBAROutput cpDecisionBAROutput = new CPDecisionBAROutput();
        Product product = new Product();
        product.setFreeNightEnabled(true);
        cpDecisionBAROutput.setProduct(product);
        CPDecisionContext cpDecisionContext = mock(CPDecisionContext.class);
        when(cpDecisionContext.isConsortiaFreeNightEnabled()).thenReturn(true);
        boolean result = service.isFreeNightProduct(cpDecisionBAROutput, cpDecisionContext);
        assertTrue(result);
    }

    @Test
    void test_free_night_eligibility_check() {
        CPDecisionBAROutput cpDecisionBAROutput = new CPDecisionBAROutput();
        cpDecisionBAROutput.setOptimalBAR(BigDecimal.ZERO);
        boolean result = service.isFreeNightEligibleForCurrentFreeNightDecision(cpDecisionBAROutput);
        assertFalse(result);
        cpDecisionBAROutput.setOptimalBAR(BigDecimal.valueOf(-100));
        result = service.isFreeNightEligibleForCurrentFreeNightDecision(cpDecisionBAROutput);
        assertTrue(result);
    }

    @Test
    void test_handleFreeNightProduct_non_free_night() {
        CPDecisionBAROutput cpDecisionBAROutput = new CPDecisionBAROutput();
        Product product = new Product();
        product.setFreeNightEnabled(true);
        cpDecisionBAROutput.setProduct(product);
        cpDecisionBAROutput.setAccomType(new AccomType());
        cpDecisionBAROutput.setOptimalBAR(BigDecimal.ZERO);
        CPDecisionBAROutput parentDecision = new CPDecisionBAROutput();
        parentDecision.setPrettyBAR(BigDecimal.valueOf(123.45));
        parentDecision.setFinalBAR(BigDecimal.valueOf(123.45));
        CPBarDecisionChange cpBarDecisionChange = mock(CPBarDecisionChange.class);
        CPDecisionBAROutputOccupancyTypeWrapper cpDecisionBAROutputOccupancyTypeWrapper = mock(CPDecisionBAROutputOccupancyTypeWrapper.class);
        when(cpBarDecisionChange.getOutputOccupancyTypeWrapper()).thenReturn(cpDecisionBAROutputOccupancyTypeWrapper);
        when(cpBarDecisionChange.getPreviousDailybarOutputBaseOccupancyTypeRate()).thenReturn(BigDecimal.valueOf(99.22));
        when(cpDecisionBAROutputOccupancyTypeWrapper.getDependentProductCpDecisionBarOutput()).thenReturn(parentDecision);
        TableBatch tableBatch = mock(TableBatch.class);
        service.handleFreeNightProduct(cpDecisionBAROutput, cpBarDecisionChange, tableBatch);
        assertEquals(BigDecimal.valueOf(123.45), cpDecisionBAROutput.getPrettyBAR());
        assertEquals(BigDecimal.valueOf(123.45), cpDecisionBAROutput.getFinalBAR());
        assertEquals(BigDecimal.valueOf(99.22), cpDecisionBAROutput.getPreviousBAR());
        assertEquals(BigDecimal.ZERO, cpDecisionBAROutput.getAdjustmentValue());
    }

    @Test
    void test_handleFreeNightProduct_free_night() {
        CPDecisionBAROutput cpDecisionBAROutput = new CPDecisionBAROutput();
        Product product = new Product();
        product.setFreeNightEnabled(true);
        cpDecisionBAROutput.setProduct(product);
        cpDecisionBAROutput.setAccomType(new AccomType());
        cpDecisionBAROutput.setOptimalBAR(BigDecimal.valueOf(-100));
        CPDecisionBAROutput parentDecision = new CPDecisionBAROutput();
        CPBarDecisionChange cpBarDecisionChange = mock(CPBarDecisionChange.class);
        CPDecisionBAROutputOccupancyTypeWrapper cpDecisionBAROutputOccupancyTypeWrapper = mock(CPDecisionBAROutputOccupancyTypeWrapper.class);
        when(cpBarDecisionChange.getOutputOccupancyTypeWrapper()).thenReturn(cpDecisionBAROutputOccupancyTypeWrapper);
        when(cpBarDecisionChange.getPreviousDailybarOutputBaseOccupancyTypeRate()).thenReturn(BigDecimal.ZERO);
        when(cpDecisionBAROutputOccupancyTypeWrapper.getDependentProductCpDecisionBarOutput()).thenReturn(parentDecision);
        TableBatch tableBatch = mock(TableBatch.class);
        service.handleFreeNightProduct(cpDecisionBAROutput, cpBarDecisionChange, tableBatch);
        assertEquals(BigDecimal.ZERO, cpDecisionBAROutput.getPrettyBAR());
        assertEquals(BigDecimal.ZERO, cpDecisionBAROutput.getFinalBAR());
        assertEquals(BigDecimal.ZERO, cpDecisionBAROutput.getPreviousBAR());
        assertEquals(BigDecimal.valueOf(-100), cpDecisionBAROutput.getAdjustmentValue());
    }

    @Test
    void test_free_upgrade_toggle_off() {
        // Given
        CPDecisionContext cpDecisionContext = mock(CPDecisionContext.class);
        PricingConfigurationService pricingConfigurationService = mock(PricingConfigurationService.class);
        RoomTypeVendorMappingService roomTypeVendorMappingService = mock(RoomTypeVendorMappingService.class);
        CrudService tenantCrudService = mock(CrudService.class);
        service.pricingConfigurationService = pricingConfigurationService;
        service.roomTypeVendorMappingService = roomTypeVendorMappingService;
        service.tenantCrudService = tenantCrudService;

        // When
        when(pricingConfigurationService.getCPDecisionContext(startDate, endDate)).thenReturn(cpDecisionContext);
        when(cpDecisionContext.isConsortiaFreeNightEnabled()).thenReturn(false);

        service.handleFreeUpgrade(startDate, endDate);

        // Then
        verify(roomTypeVendorMappingService, times(0)).getRoomTypeVendorMappings(Mockito.anyString());
        verify(tenantCrudService, times(0)).save(Mockito.anyList());
    }

    @Test
    void test_free_upgrade_toggle_on_no_free_upgrade_product() {
        // Given
        CPDecisionContext cpDecisionContext = mock(CPDecisionContext.class);
        PricingConfigurationService pricingConfigurationService = mock(PricingConfigurationService.class);
        RoomTypeVendorMappingService roomTypeVendorMappingService = mock(RoomTypeVendorMappingService.class);
        CrudService tenantCrudService = mock(CrudService.class);
        service.pricingConfigurationService = pricingConfigurationService;
        service.roomTypeVendorMappingService = roomTypeVendorMappingService;
        service.tenantCrudService = tenantCrudService;

        // When
        when(pricingConfigurationService.getCPDecisionContext(startDate, endDate)).thenReturn(cpDecisionContext);
        when(cpDecisionContext.isConsortiaFreeNightEnabled()).thenReturn(true);
        Map<Product, List<Product>> productListMap = getProductsInHierarchialOrder();
        when(cpDecisionContext.getProductsInHierarchicalOrder()).thenReturn(productListMap);

        service.handleFreeUpgrade(startDate, endDate);

        // Then
        verify(roomTypeVendorMappingService, times(0)).getRoomTypeVendorMappings(Mockito.anyString());
        verify(tenantCrudService, times(0)).save(Mockito.anyList());
    }

    @Test
    void test_free_upgrade_toggle_on_free_upgrade_product_present_ext_system_invalid() {
        // Given
        CPDecisionContext cpDecisionContext = mock(CPDecisionContext.class);
        PricingConfigurationService pricingConfigurationService = mock(PricingConfigurationService.class);
        RoomTypeVendorMappingService roomTypeVendorMappingService = mock(RoomTypeVendorMappingService.class);
        CrudService tenantCrudService = mock(CrudService.class);
        ExternalSystemHelper externalSystemHelper = mock(ExternalSystemHelper.class);
        service.pricingConfigurationService = pricingConfigurationService;
        service.roomTypeVendorMappingService = roomTypeVendorMappingService;
        service.tenantCrudService = tenantCrudService;
        service.externalSystemHelper = externalSystemHelper;

        // When
        when(pricingConfigurationService.getCPDecisionContext(startDate, endDate)).thenReturn(cpDecisionContext);
        when(cpDecisionContext.isConsortiaFreeNightEnabled()).thenReturn(true);
        Map<Product, List<Product>> productListMap = getProductsInHierarchialOrder();
        productListMap.get(createMockProduct(1, "bar", null)).get(2).setFreeUpgradeEnabled(true);
        when(cpDecisionContext.getProductsInHierarchicalOrder()).thenReturn(productListMap);
        Objects.requireNonNull(when(externalSystemHelper.isHilstar())).thenReturn(false);
        Objects.requireNonNull(when(externalSystemHelper.isPCRS())).thenReturn(false);

        // Then
        TetrisException e = assertThrows(TetrisException.class, () -> service.handleFreeUpgrade(startDate, endDate));
        assertEquals(ErrorCode.UNEXPECTED_ERROR, e.getErrorCode());
        verify(roomTypeVendorMappingService, times(0)).getRoomTypeVendorMappings(Mockito.anyString());
        verify(tenantCrudService, times(0)).save(Mockito.anyList());
    }

    @Test
    void test_free_upgrade_toggle_on_free_upgrade_product_present_ext_system_pcrs_no_mappings() {
        // Given
        CPDecisionContext cpDecisionContext = mock(CPDecisionContext.class);
        PricingConfigurationService pricingConfigurationService = mock(PricingConfigurationService.class);
        RoomTypeVendorMappingService roomTypeVendorMappingService = mock(RoomTypeVendorMappingService.class);
        CrudService tenantCrudService = mock(CrudService.class);
        ExternalSystemHelper externalSystemHelper = mock(ExternalSystemHelper.class);
        service.pricingConfigurationService = pricingConfigurationService;
        service.roomTypeVendorMappingService = roomTypeVendorMappingService;
        service.tenantCrudService = tenantCrudService;
        service.externalSystemHelper = externalSystemHelper;

        // When
        when(pricingConfigurationService.getCPDecisionContext(startDate, endDate)).thenReturn(cpDecisionContext);
        when(cpDecisionContext.isConsortiaFreeNightEnabled()).thenReturn(true);
        Map<Product, List<Product>> productListMap = getProductsInHierarchialOrder();
        productListMap.get(createMockProduct(1, "bar", null)).get(2).setFreeUpgradeEnabled(true);
        when(cpDecisionContext.getProductsInHierarchicalOrder()).thenReturn(productListMap);
        Objects.requireNonNull(when(externalSystemHelper.isHilstar())).thenReturn(false);
        Objects.requireNonNull(when(externalSystemHelper.isPCRS())).thenReturn(true);
        List<AccomTypeVendorMapping> accomTypeMappings = new ArrayList<>();
        when(roomTypeVendorMappingService.getRoomTypeVendorMappings(ExternalSystem.PCRS.getCode())).thenReturn(accomTypeMappings);

        // Then
        TetrisException e = assertThrows(TetrisException.class, () -> service.handleFreeUpgrade(startDate, endDate));
        assertEquals(ErrorCode.UNEXPECTED_ERROR, e.getErrorCode());
        verify(roomTypeVendorMappingService, times(1)).getRoomTypeVendorMappings(Mockito.anyString());
        verify(tenantCrudService, times(0)).save(Mockito.anyList());
    }

    @Test
    void test_free_upgrade_toggle_on_free_upgrade_product_present_ext_system_pcrs_ignore_mapping_with_same_source_and_target() {
        // Given
        CPDecisionContext cpDecisionContext = mock(CPDecisionContext.class);
        PricingConfigurationService pricingConfigurationService = mock(PricingConfigurationService.class);
        RoomTypeVendorMappingService roomTypeVendorMappingService = mock(RoomTypeVendorMappingService.class);
        CrudService tenantCrudService = mock(CrudService.class);
        ExternalSystemHelper externalSystemHelper = mock(ExternalSystemHelper.class);
        service.pricingConfigurationService = pricingConfigurationService;
        service.roomTypeVendorMappingService = roomTypeVendorMappingService;
        service.tenantCrudService = tenantCrudService;
        service.externalSystemHelper = externalSystemHelper;

        // When
        when(pricingConfigurationService.getCPDecisionContext(startDate, endDate)).thenReturn(cpDecisionContext);
        when(cpDecisionContext.isConsortiaFreeNightEnabled()).thenReturn(true);
        Map<Product, List<Product>> productListMap = getProductsInHierarchialOrder();
        productListMap.get(createMockProduct(1, "bar", null)).get(2).setFreeUpgradeEnabled(true);
        when(cpDecisionContext.getProductsInHierarchicalOrder()).thenReturn(productListMap);
        Objects.requireNonNull(when(externalSystemHelper.isHilstar())).thenReturn(false);
        Objects.requireNonNull(when(externalSystemHelper.isPCRS())).thenReturn(true);
        List<AccomTypeVendorMapping> accomTypeMappings = new ArrayList<>();
        accomTypeMappings.add(getAccomTypeVendorMapping("STD", "STD"));
        when(roomTypeVendorMappingService.getRoomTypeVendorMappings(ExternalSystem.PCRS.getCode())).thenReturn(accomTypeMappings);

        // Then
        TetrisException e = assertThrows(TetrisException.class, () -> service.handleFreeUpgrade(startDate, endDate));
        assertEquals(ErrorCode.UNEXPECTED_ERROR, e.getErrorCode());
        verify(roomTypeVendorMappingService, times(1)).getRoomTypeVendorMappings(Mockito.anyString());
        verify(tenantCrudService, times(0)).save(Mockito.anyList());
    }

    @Test
    void test_free_upgrade_toggle_on_free_upgrade_product_present_ext_system_pcrs_mappings_present_1() {
        // Given
        CPDecisionContext cpDecisionContext = mock(CPDecisionContext.class);
        PricingConfigurationService pricingConfigurationService = mock(PricingConfigurationService.class);
        RoomTypeVendorMappingService roomTypeVendorMappingService = mock(RoomTypeVendorMappingService.class);
        CrudService tenantCrudService = mock(CrudService.class);
        ExternalSystemHelper externalSystemHelper = mock(ExternalSystemHelper.class);
        CPManagementService cpManagementService = mock(CPManagementService.class);
        service.pricingConfigurationService = pricingConfigurationService;
        service.roomTypeVendorMappingService = roomTypeVendorMappingService;
        service.tenantCrudService = tenantCrudService;
        service.externalSystemHelper = externalSystemHelper;
        service.cpManagementService = cpManagementService;

        // When
        when(pricingConfigurationService.getCPDecisionContext(startDate, endDate)).thenReturn(cpDecisionContext);
        when(cpDecisionContext.isConsortiaFreeNightEnabled()).thenReturn(true);
        Map<Product, List<Product>> productListMap = getProductsInHierarchialOrder();
        productListMap.get(createMockProduct(1, "bar", null)).get(2).setFreeUpgradeEnabled(true);
        when(cpDecisionContext.getProductsInHierarchicalOrder()).thenReturn(productListMap);
        Objects.requireNonNull(when(externalSystemHelper.isHilstar())).thenReturn(false);
        Objects.requireNonNull(when(externalSystemHelper.isPCRS())).thenReturn(true);
        List<AccomTypeVendorMapping> accomTypeMappings = new ArrayList<>();
        accomTypeMappings.add(getAccomTypeVendorMapping("STD", "SUP"));
        when(roomTypeVendorMappingService.getRoomTypeVendorMappings(ExternalSystem.PCRS.getCode())).thenReturn(accomTypeMappings);
        AccomType std = getAccomType(1, "STD");
        AccomType sup = getAccomType(2, "SUP");
        AccomType jsui = getAccomType(3, "JSUI");
        Product fn1 = createMockProduct(3, "FN1", 1);
        CPDecisionBAROutput d1 = getDecision(fn1, BigDecimal.valueOf(0), BigDecimal.valueOf(123.45), std);
        CPDecisionBAROutput d2 = getDecision(fn1, BigDecimal.valueOf(0), BigDecimal.valueOf(160.78), sup);
        CPDecisionBAROutput d3 = getDecision(fn1, BigDecimal.valueOf(0), BigDecimal.valueOf(184.03), jsui);
        List<CPDecisionBAROutput> decisions = new ArrayList<>(List.of(d1, d2));
        when(cpManagementService.findCPDecisionsBetweenDatesWithCapacity(startDate, endDate)).thenReturn(decisions);

        service.handleFreeUpgrade(startDate, endDate);
        assertEquals(BigDecimal.valueOf(123.45), d1.getFinalBAR());
        assertEquals(BigDecimal.valueOf(123.45), d2.getFinalBAR());
        assertEquals(BigDecimal.valueOf(184.03), d3.getFinalBAR());
        // Then
        verify(roomTypeVendorMappingService, times(1)).getRoomTypeVendorMappings(Mockito.anyString());
        verify(tenantCrudService, times(1)).save(Mockito.anyList());
    }

    @Test
    void test_free_upgrade_toggle_on_free_upgrade_product_present_ext_system_pcrs_mappings_present_2() {
        // Given
        CPDecisionContext cpDecisionContext = mock(CPDecisionContext.class);
        PricingConfigurationService pricingConfigurationService = mock(PricingConfigurationService.class);
        RoomTypeVendorMappingService roomTypeVendorMappingService = mock(RoomTypeVendorMappingService.class);
        CrudService tenantCrudService = mock(CrudService.class);
        ExternalSystemHelper externalSystemHelper = mock(ExternalSystemHelper.class);
        CPManagementService cpManagementService = mock(CPManagementService.class);
        service.pricingConfigurationService = pricingConfigurationService;
        service.roomTypeVendorMappingService = roomTypeVendorMappingService;
        service.tenantCrudService = tenantCrudService;
        service.externalSystemHelper = externalSystemHelper;
        service.cpManagementService = cpManagementService;

        // When
        when(pricingConfigurationService.getCPDecisionContext(startDate, endDate)).thenReturn(cpDecisionContext);
        when(cpDecisionContext.isConsortiaFreeNightEnabled()).thenReturn(true);
        Map<Product, List<Product>> productListMap = getProductsInHierarchialOrder();
        productListMap.get(createMockProduct(1, "bar", null)).get(2).setFreeUpgradeEnabled(true);
        when(cpDecisionContext.getProductsInHierarchicalOrder()).thenReturn(productListMap);
        Objects.requireNonNull(when(externalSystemHelper.isHilstar())).thenReturn(false);
        Objects.requireNonNull(when(externalSystemHelper.isPCRS())).thenReturn(true);
        List<AccomTypeVendorMapping> accomTypeMappings = new ArrayList<>();
        accomTypeMappings.add(getAccomTypeVendorMapping("STD", "SUP"));
        accomTypeMappings.add(getAccomTypeVendorMapping("SUP", "JSUI"));
        when(roomTypeVendorMappingService.getRoomTypeVendorMappings(ExternalSystem.PCRS.getCode())).thenReturn(accomTypeMappings);
        AccomType std = getAccomType(1, "STD");
        AccomType sup = getAccomType(2, "SUP");
        AccomType jsui = getAccomType(3, "JSUI");
        Product fn1 = createMockProduct(3, "FN1", 1);
        CPDecisionBAROutput d1 = getDecision(fn1, BigDecimal.valueOf(0), BigDecimal.valueOf(123.45), std);
        CPDecisionBAROutput d2 = getDecision(fn1, BigDecimal.valueOf(0), BigDecimal.valueOf(160.78), sup);
        CPDecisionBAROutput d3 = getDecision(fn1, BigDecimal.valueOf(0), BigDecimal.valueOf(184.03), jsui);
        List<CPDecisionBAROutput> decisions = new ArrayList<>(List.of(d1, d2, d3));
        when(cpManagementService.findCPDecisionsBetweenDatesWithCapacity(startDate, endDate)).thenReturn(decisions);

        service.handleFreeUpgrade(startDate, endDate);
        assertEquals(BigDecimal.valueOf(123.45), d1.getFinalBAR());
        assertEquals(BigDecimal.valueOf(123.45), d2.getFinalBAR());
        assertEquals(BigDecimal.valueOf(160.78), d3.getFinalBAR());
        // Then
        verify(roomTypeVendorMappingService, times(1)).getRoomTypeVendorMappings(Mockito.anyString());
        verify(tenantCrudService, times(1)).save(Mockito.anyList());
    }

    @Test
    void test_free_upgrade_missing_accom_type() {
        // Given
        CPDecisionContext cpDecisionContext = mock(CPDecisionContext.class);
        PricingConfigurationService pricingConfigurationService = mock(PricingConfigurationService.class);
        RoomTypeVendorMappingService roomTypeVendorMappingService = mock(RoomTypeVendorMappingService.class);
        CrudService tenantCrudService = mock(CrudService.class);
        ExternalSystemHelper externalSystemHelper = mock(ExternalSystemHelper.class);
        CPManagementService cpManagementService = mock(CPManagementService.class);
        service.pricingConfigurationService = pricingConfigurationService;
        service.roomTypeVendorMappingService = roomTypeVendorMappingService;
        service.tenantCrudService = tenantCrudService;
        service.externalSystemHelper = externalSystemHelper;
        service.cpManagementService = cpManagementService;

        // When
        when(pricingConfigurationService.getCPDecisionContext(startDate, endDate)).thenReturn(cpDecisionContext);
        when(cpDecisionContext.isConsortiaFreeNightEnabled()).thenReturn(true);
        Map<Product, List<Product>> productListMap = getProductsInHierarchialOrder();
        productListMap.get(createMockProduct(1, "bar", null)).get(2).setFreeUpgradeEnabled(true);
        when(cpDecisionContext.getProductsInHierarchicalOrder()).thenReturn(productListMap);
        Objects.requireNonNull(when(externalSystemHelper.isHilstar())).thenReturn(false);
        Objects.requireNonNull(when(externalSystemHelper.isPCRS())).thenReturn(true);
        List<AccomTypeVendorMapping> accomTypeMappings = new ArrayList<>();
        accomTypeMappings.add(getAccomTypeVendorMapping("STD", "XYZ"));
        accomTypeMappings.add(getAccomTypeVendorMapping("SUP", "JSUI"));
        when(roomTypeVendorMappingService.getRoomTypeVendorMappings(ExternalSystem.PCRS.getCode())).thenReturn(accomTypeMappings);
        AccomType std = getAccomType(1, "STD");
        AccomType sup = getAccomType(2, "SUP");
        AccomType jsui = getAccomType(3, "JSUI");
        Product fn1 = createMockProduct(3, "FN1", 1);
        CPDecisionBAROutput d1 = getDecision(fn1, BigDecimal.valueOf(0), BigDecimal.valueOf(123.45), std);
        CPDecisionBAROutput d2 = getDecision(fn1, BigDecimal.valueOf(0), BigDecimal.valueOf(160.78), sup);
        CPDecisionBAROutput d3 = getDecision(fn1, BigDecimal.valueOf(0), BigDecimal.valueOf(184.03), jsui);
        List<CPDecisionBAROutput> decisions = new ArrayList<>(List.of(d1, d2, d3));
        when(cpManagementService.findCPDecisionsBetweenDatesWithCapacity(startDate, endDate)).thenReturn(decisions);
        when(service.accommodationService.getAllAccomTypeDetailsIncludingPseudoRooms()).thenReturn(List.of(std, sup, jsui));

        // Then
        TetrisException exception = assertThrows(TetrisException.class,
                () -> service.handleFreeUpgrade(startDate, endDate));
        assertTrue(exception.getMessage().contains("XYZ" + CONSORTIA_FREE_UPGRADE_MISSING_DECISION_ACCOM_TYPE_NOT_FOUND));
    }

    @Test
    void test_free_upgrade_discontinued_accom_type() {
        // Given
        CPDecisionContext cpDecisionContext = mock(CPDecisionContext.class);
        PricingConfigurationService pricingConfigurationService = mock(PricingConfigurationService.class);
        RoomTypeVendorMappingService roomTypeVendorMappingService = mock(RoomTypeVendorMappingService.class);
        CrudService tenantCrudService = mock(CrudService.class);
        ExternalSystemHelper externalSystemHelper = mock(ExternalSystemHelper.class);
        CPManagementService cpManagementService = mock(CPManagementService.class);
        service.pricingConfigurationService = pricingConfigurationService;
        service.roomTypeVendorMappingService = roomTypeVendorMappingService;
        service.tenantCrudService = tenantCrudService;
        service.externalSystemHelper = externalSystemHelper;
        service.cpManagementService = cpManagementService;

        // When
        when(pricingConfigurationService.getCPDecisionContext(startDate, endDate)).thenReturn(cpDecisionContext);
        when(cpDecisionContext.isConsortiaFreeNightEnabled()).thenReturn(true);
        Map<Product, List<Product>> productListMap = getProductsInHierarchialOrder();
        productListMap.get(createMockProduct(1, "bar", null)).get(2).setFreeUpgradeEnabled(true);
        when(cpDecisionContext.getProductsInHierarchicalOrder()).thenReturn(productListMap);
        Objects.requireNonNull(when(externalSystemHelper.isHilstar())).thenReturn(false);
        Objects.requireNonNull(when(externalSystemHelper.isPCRS())).thenReturn(true);
        List<AccomTypeVendorMapping> accomTypeMappings = new ArrayList<>();
        accomTypeMappings.add(getAccomTypeVendorMapping("STD", "SUP"));
        accomTypeMappings.add(getAccomTypeVendorMapping("SUP", "JSUI"));
        when(roomTypeVendorMappingService.getRoomTypeVendorMappings(ExternalSystem.PCRS.getCode())).thenReturn(accomTypeMappings);
        AccomType std = getAccomType(1, "STD");
        AccomType sup = getAccomType(2, "SUP");
        AccomType jsui = getAccomType(3, "JSUI");
        jsui.setDisplayStatusId(2);
        Product fn1 = createMockProduct(3, "FN1", 1);
        CPDecisionBAROutput d1 = getDecision(fn1, BigDecimal.valueOf(0), BigDecimal.valueOf(123.45), std);
        CPDecisionBAROutput d2 = getDecision(fn1, BigDecimal.valueOf(0), BigDecimal.valueOf(160.78), sup);
        CPDecisionBAROutput d3 = getDecision(fn1, BigDecimal.valueOf(0), BigDecimal.valueOf(184.03), jsui);
        List<CPDecisionBAROutput> decisions = new ArrayList<>(List.of(d1, d2));
        when(cpManagementService.findCPDecisionsBetweenDatesWithCapacity(startDate, endDate)).thenReturn(decisions);
        when(service.accommodationService.getAllAccomTypeDetailsIncludingPseudoRooms()).thenReturn(List.of(std, sup, jsui));

        // Then
        TetrisException exception = assertThrows(TetrisException.class,
                () -> service.handleFreeUpgrade(startDate, endDate));
        assertTrue(exception.getMessage().contains("JSUI" + CONSORTIA_FREE_UPGRADE_MISSING_DECISION_ACCOM_TYPE_DISCONTINUED));
    }

    @Test
    void test_free_upgrade_zero_capacity_accom_type() {
        // Given
        CPDecisionContext cpDecisionContext = mock(CPDecisionContext.class);
        PricingConfigurationService pricingConfigurationService = mock(PricingConfigurationService.class);
        RoomTypeVendorMappingService roomTypeVendorMappingService = mock(RoomTypeVendorMappingService.class);
        CrudService tenantCrudService = mock(CrudService.class);
        ExternalSystemHelper externalSystemHelper = mock(ExternalSystemHelper.class);
        CPManagementService cpManagementService = mock(CPManagementService.class);
        service.pricingConfigurationService = pricingConfigurationService;
        service.roomTypeVendorMappingService = roomTypeVendorMappingService;
        service.tenantCrudService = tenantCrudService;
        service.externalSystemHelper = externalSystemHelper;
        service.cpManagementService = cpManagementService;

        // When
        when(pricingConfigurationService.getCPDecisionContext(startDate, endDate)).thenReturn(cpDecisionContext);
        when(cpDecisionContext.isConsortiaFreeNightEnabled()).thenReturn(true);
        Map<Product, List<Product>> productListMap = getProductsInHierarchialOrder();
        productListMap.get(createMockProduct(1, "bar", null)).get(2).setFreeUpgradeEnabled(true);
        when(cpDecisionContext.getProductsInHierarchicalOrder()).thenReturn(productListMap);
        Objects.requireNonNull(when(externalSystemHelper.isHilstar())).thenReturn(false);
        Objects.requireNonNull(when(externalSystemHelper.isPCRS())).thenReturn(true);
        List<AccomTypeVendorMapping> accomTypeMappings = new ArrayList<>();
        accomTypeMappings.add(getAccomTypeVendorMapping("STD", "SUP"));
        accomTypeMappings.add(getAccomTypeVendorMapping("SUP", "JSUI"));
        when(roomTypeVendorMappingService.getRoomTypeVendorMappings(ExternalSystem.PCRS.getCode())).thenReturn(accomTypeMappings);
        AccomType std = getAccomType(1, "STD");
        AccomType sup = getAccomType(2, "SUP");
        AccomType jsui = getAccomType(3, "JSUI");
        jsui.setAccomTypeCapacity(0);
        Product fn1 = createMockProduct(3, "FN1", 1);
        CPDecisionBAROutput d1 = getDecision(fn1, BigDecimal.valueOf(0), BigDecimal.valueOf(123.45), std);
        CPDecisionBAROutput d2 = getDecision(fn1, BigDecimal.valueOf(0), BigDecimal.valueOf(160.78), sup);
        CPDecisionBAROutput d3 = getDecision(fn1, BigDecimal.valueOf(0), BigDecimal.valueOf(184.03), jsui);
        List<CPDecisionBAROutput> decisions = new ArrayList<>(List.of(d1, d2));
        when(cpManagementService.findCPDecisionsBetweenDatesWithCapacity(startDate, endDate)).thenReturn(decisions);
        when(service.accommodationService.getAllAccomTypeDetailsIncludingPseudoRooms()).thenReturn(List.of(std, sup, jsui));

        // Then
        TetrisException exception = assertThrows(TetrisException.class,
                () -> service.handleFreeUpgrade(startDate, endDate));
        assertTrue(exception.getMessage().contains("JSUI" + CONSORTIA_FREE_UPGRADE_MISSING_DECISION_ACCOM_TYPE_ZERO_CAPACITY));
    }

    private List<CPDecisionBAROutput> getDecisions() {
        AccomType std = getAccomType(1, "STD");
        AccomType sup = getAccomType(2, "SUP");
        Product fn1 = createMockProduct(3, "FN1", 1);
        CPDecisionBAROutput d1 = getDecision(fn1, BigDecimal.valueOf(0), BigDecimal.valueOf(123.45), std);
        CPDecisionBAROutput d2 = getDecision(fn1, BigDecimal.valueOf(0), BigDecimal.valueOf(160.78), sup);
        return List.of(d1, d2);
    }

    private AccomType getAccomType(int id, String code) {
        AccomType accomType = new AccomType();
        accomType.setId(id);
        accomType.setAccomTypeCode(code);
        return accomType;
    }

    private CPDecisionBAROutput getDecision(Product product, BigDecimal optimalBar, BigDecimal finalBar, AccomType accomType) {
        CPDecisionBAROutput d = new CPDecisionBAROutput();
        d.setProduct(product);
        d.setArrivalDate(startDate);
        d.setOptimalBAR(optimalBar);
        d.setAccomType(accomType);
        d.setFinalBAR(finalBar);
        return d;
    }

    private AccomTypeVendorMapping getAccomTypeVendorMapping(String source, String destination) {
        AccomTypeVendorMapping accomTypeVendorMapping = new AccomTypeVendorMapping();
        accomTypeVendorMapping.setAccomTypeCode(source);
        accomTypeVendorMapping.setVendorAccomTypeCode(destination);
        accomTypeVendorMapping.setVendor("PCRS");
        return accomTypeVendorMapping;
    }

    private Map<Product, List<Product>> getProductsInHierarchialOrder() {
        Product bar = createMockProduct(1, "bar", null);
        Product lp1 = createMockProduct(2, "LP1", 1);
        Product fn1 = createMockProduct(3, "FN1", 1);
        Map<Product, List<Product>> products = new HashMap<>();
        products.put(bar, List.of(bar, lp1, fn1));
        return products;
    }

    private Product createMockProduct(int id, String name, Integer dependentProductId) {
        Product product = new Product();
        product.setId(id);
        product.setName(name);
        product.setDependentProductId(dependentProductId);
        return product;
    }

    private List<CPDecisionBAROutput> insertData() {
        Product barProduct = tenantCrudService().findAll(Product.class).get(0);
        Product linkedProduct = addAgileRatesProduct("Test Product", 1);
        CPDecisionBAROutput cpDecisionBarOutput1 = addCPDecisionBarOutput(startDate, RT_QUEEN, new BigDecimal("101.12"), barProduct);
        CPDecisionBAROutput cpDecisionBarOutput2 = addCPDecisionBarOutput(startDate, RT_KING, new BigDecimal("202.12"), barProduct);
        CPDecisionBAROutput cpDecisionBarOutput3 = addCPDecisionBarOutput(startDate.plusDays(1), RT_QUEEN, new BigDecimal("301.56"), barProduct);
        CPDecisionBAROutput cpDecisionBarOutput4 = addCPDecisionBarOutput(startDate.plusDays(1), RT_KING, new BigDecimal("409.70"), barProduct);
        CPDecisionBAROutput cpDecisionBarOutput5 = addCPDecisionBarOutput(startDate, RT_QUEEN, new BigDecimal("123.00"), linkedProduct);
        CPDecisionBAROutput cpDecisionBarOutput6 = addCPDecisionBarOutput(startDate, RT_KING, new BigDecimal("234.50"), linkedProduct);
        CPDecisionBAROutput cpDecisionBarOutput7 = addCPDecisionBarOutput(startDate.plusDays(1), RT_QUEEN, new BigDecimal("512.67"), linkedProduct);
        CPDecisionBAROutput cpDecisionBarOutput8 = addCPDecisionBarOutput(startDate.plusDays(1), RT_KING, new BigDecimal("234.56"), linkedProduct);
        tenantCrudService().flushAndClear();
        return List.of(cpDecisionBarOutput1, cpDecisionBarOutput2, cpDecisionBarOutput3, cpDecisionBarOutput4,
                cpDecisionBarOutput5, cpDecisionBarOutput6, cpDecisionBarOutput7, cpDecisionBarOutput8);
    }

}