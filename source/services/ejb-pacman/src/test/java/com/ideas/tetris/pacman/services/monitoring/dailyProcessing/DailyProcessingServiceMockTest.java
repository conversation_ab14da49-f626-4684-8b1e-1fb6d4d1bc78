package com.ideas.tetris.pacman.services.monitoring.dailyProcessing;

import com.ideas.g3.rule.ThreadLocalExtension;
import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.configparams.GUIConfigParamName;
import com.ideas.tetris.pacman.common.configparams.IntegrationConfigParamName;
import com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.common.fds.G3SNSService;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.commondaoandenities.global.entity.ClientPropertyView;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.CdpSchedule;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.externalSystemDetail.ExternalSystemDetailService;
import com.ideas.tetris.pacman.services.forcefulldecisions.ForceFullDecisionsUpdateReasons;
import com.ideas.tetris.pacman.services.forcefulldecisions.service.ForceFullDecisionsService;
import com.ideas.tetris.pacman.services.internalalert.InternalAlertService;
import com.ideas.tetris.pacman.services.monitoring.dailyProcessing.dto.InputProcessingDto;
import com.ideas.tetris.pacman.services.monitoring.dailyProcessing.entity.DailyProcessingStatus;
import com.ideas.tetris.pacman.services.monitoring.dailyProcessing.entity.DecisionDelivery;
import com.ideas.tetris.pacman.services.monitoring.dailyProcessing.entity.DecisionDeliveryByType;
import com.ideas.tetris.pacman.services.monitoring.dailyProcessing.entity.DecisionDeliveryByTypeStatus;
import com.ideas.tetris.pacman.services.monitoring.dailyProcessing.entity.DecisionDeliveryCriteria;
import com.ideas.tetris.pacman.services.monitoring.dailyProcessing.entity.ExtractStatus;
import com.ideas.tetris.pacman.services.monitoring.dailyProcessing.entity.InputProcessing;
import com.ideas.tetris.pacman.services.monitoring.dailyProcessing.entity.InputProcessingCriteria;
import com.ideas.tetris.pacman.services.monitoring.dailyProcessing.entity.InputProcessingJob;
import com.ideas.tetris.pacman.services.monitoring.dailyProcessing.entity.InputProcessingStatus;
import com.ideas.tetris.pacman.services.monitoring.dailyProcessing.entity.InputType;
import com.ideas.tetris.pacman.services.monitoring.dailyProcessing.entity.PropertyDailyProcessing;
import com.ideas.tetris.pacman.services.monitoring.dailyProcessing.entity.PropertyDailyProcessingCriteria;
import com.ideas.tetris.pacman.services.ngi.extractstatus.dto.NgiExtractReceivedTracker;
import com.ideas.tetris.pacman.services.property.PropertyService;
import com.ideas.tetris.platform.common.contextholder.PlatformThreadLocalContextHolder;
import com.ideas.tetris.platform.common.crudservice.AbstractMultiPropertyCrudService;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.entity.DecisionDestination;
import com.ideas.tetris.platform.common.entity.IdAware;
import com.ideas.tetris.platform.common.entity.IdAwareEntity;
import com.ideas.tetris.platform.common.externalsystem.ExternalSubSystem;
import com.ideas.tetris.platform.common.externalsystem.ExternalSystem;
import com.ideas.tetris.platform.common.job.JobName;
import com.ideas.tetris.platform.common.job.JobStepContext;
import com.ideas.tetris.platform.common.rest.mapper.RestClient;
import com.ideas.tetris.platform.common.xml.schema.workcontext.v1.WorkContextType;
import com.ideas.tetris.platform.services.Stage;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import com.ideas.tetris.platform.services.daoandentities.entity.Client;
import com.ideas.tetris.platform.services.daoandentities.entity.Property;
import com.ideas.tetris.platform.services.globalproperty.service.GlobalPropertyService;
import com.ideas.tetris.platform.services.job.JobService;
import org.joda.time.DateTimeZone;
import org.joda.time.LocalDate;
import org.joda.time.LocalDateTime;
import org.joda.time.LocalTime;
import org.json.JSONException;
import org.json.JSONObject;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.Spy;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.TimeZone;

import static com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName.NOTIFY_AWS_POST_PROCESSING_ENABLED;
import static com.ideas.tetris.pacman.common.constants.Constants.FALSE;
import static com.ideas.tetris.pacman.common.constants.Constants.TARS_DESTINATION;
import static com.ideas.tetris.pacman.common.constants.Constants.TRUE;
import static com.ideas.tetris.pacman.services.monitoring.dailyProcessing.entity.DecisionDeliveryByTypeStatus.ACKNOWLEDGED;
import static com.ideas.tetris.pacman.services.monitoring.dailyProcessing.entity.DecisionDeliveryByTypeStatus.DELIVERED;
import static com.ideas.tetris.pacman.services.monitoring.dailyProcessing.entity.DecisionDeliveryByTypeStatus.IN_PROGRESS;
import static org.hamcrest.CoreMatchers.notNullValue;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.is;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.anyMap;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.atLeastOnce;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.eq;
import static org.mockito.Mockito.ignoreStubs;
import static org.mockito.Mockito.isA;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.nullable;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoMoreInteractions;
import static org.mockito.Mockito.when;

public class DailyProcessingServiceMockTest {

    public static final String DECISION_ID = "123::DecisionType";

    public static final String DECISION_ID_DEFAULT = "123";

    private static final String CLIENT_CODE = "TESTCLIENT";

    private static final String PROPERTY_CODE = "TESTPROPERTY";

    private static final Integer PROPERTY_ID = 913;

    private static final String INPUT_FILE_NAME = "BEPOP.20140913.0526.tar.Z";

    private static final String DESTINATION_ID = "HILSTAR";

    private static final int INPUT_PROCESSING_ID = 913;

    private static final String CORRELATION_ID = "myUniqueId";

    private static final Integer OVERDUE_1_ID = 1211;

    private static final Integer OVERDUE_2_ID = 526;

    private static final LocalDate EARLY_DATE = new LocalDate(2015, 1, 1);

    private static final LocalDate MIDDLE_DATE = new LocalDate(2015, 1, 2);

    private static final LocalDate LATE_DATE = new LocalDate(2015, 1, 3);

    private static final Integer SCHEDULE_ID = 1211;

    private static final Boolean CLIENT_ESA = Boolean.FALSE;

    private static final Boolean CLIENT_HILTON = Boolean.TRUE;

    private static final Integer CLIENT_ID = 23;

    private static final String TIME_ZONE = "America/Chicago";

    private static final String DECISION_TYPE_1 = "someDecisionType";

    private static final String DECISION_TYPE_2 = "someOtherDecisionType";

    @RegisterExtension
    public ThreadLocalExtension threadLocalExtension = new ThreadLocalExtension();

    @Mock
    private ForceFullDecisionsService forceFullDecisionsService;

    @Spy
    @InjectMocks
    private DailyProcessingService dailyProcessingService;

    @Mock
    private CrudService crudService;

    @Mock
    private AbstractMultiPropertyCrudService multiPropertyCrudService;

    @Mock
    private PacmanConfigParamsService pacmanConfigParamsService;

    @Mock
    private PropertyService propertyService;
    @Mock
    private ExternalSystemDetailService externalSystemDetailService;

    @Mock
    private InternalAlertService internalAlertService;

    @Mock
    private JobService jobService;
    @Mock
    private G3SNSService g3snsService;


    @Mock
    private GlobalPropertyService globalPropertyService;

    @Mock
    private RestClient restClient;

    @Mock
    private DateService dateService;

    @Captor
    private ArgumentCaptor<Map<String, Object>> jobParametersCaptor;

    @Captor
    private ArgumentCaptor<InputProcessing> inputProcessingCaptor;

    @Captor
    private ArgumentCaptor<List<InputProcessing>> inputProcessingListCaptor;

    private InputProcessing inputProcessing;

    private PropertyDailyProcessing propertyDailyProcessing;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        WorkContextType workContext = new WorkContextType();
        workContext.setPropertyId(PROPERTY_ID);
        workContext.setClientCode(CLIENT_CODE);
        workContext.setPropertyCode(PROPERTY_CODE);
        PacmanWorkContextHelper.setWorkContext(workContext);
    }

    @Disabled("Failing on PR builds")
    @Test
    public void testFindInputProcessingRecordsSetsExternalSystem() {
        InputProcessingCriteria inputProcessingCriteria = getInputProcessingDtos(ExternalSubSystem.OXI);
        List<InputProcessingDto> results = dailyProcessingService.findInputProcessingRecords(inputProcessingCriteria);
        assertEquals(1, results.size());
        assertEquals(results.get(0).getExternalSystem(), "Hilstar / OXI");
    }

    private ClientPropertyView createClientPropertyView(Integer propertyId, String clientCode, String propertyCode, String timeZone) {
        ClientPropertyView property = new ClientPropertyView();
        property.setPropertyId(propertyId);
        property.setClientCode(clientCode);
        property.setPropertyCode(propertyCode);
        property.setTimeZone(timeZone);
        property.setStage(Stage.POPULATION);
        return property;
    }

    @Test
    public void verifyInputType() {
        PropertyDailyProcessing pdp = new PropertyDailyProcessing();
        pdp.setProcessingDate(LocalDate.now());
        InputProcessing inputProcessing = new InputProcessing();
        inputProcessing.setInputType(InputType.CDP.toString());
        inputProcessing.setInputId(INPUT_FILE_NAME);
        inputProcessing.setPropertyDailyProcessing(pdp);
        pdp.addInputProcessing(inputProcessing);

        when(crudService.findByCriteria(any(PropertyDailyProcessingCriteria.class))).thenReturn(List.of(pdp));
        when(crudService.findByCriteria(any(InputProcessingCriteria.class))).thenReturn(List.of(inputProcessing));
        when(crudService.save(inputProcessing)).thenReturn(inputProcessing);
        when(crudService.save(pdp)).thenReturn(pdp);
        dailyProcessingService.verifyInputType(PROPERTY_CODE, CLIENT_CODE, INPUT_FILE_NAME, InputType.BDE);
        verify(crudService).save(inputProcessing);
        verify(crudService).save(pdp);
    }

    @Test
    public void updatePropertyDailyProcessing() {
        InputProcessing inputProcessing = new InputProcessing();
        inputProcessing.setInputType("BDE");
        PropertyDailyProcessing pdp = new PropertyDailyProcessing();
        pdp.setPropertyTimeZone("America/New_York");
        pdp.setClientCode(CLIENT_CODE);
        pdp.setPropertyCode(PROPERTY_CODE);
        inputProcessing.setPropertyDailyProcessing(pdp);
        when(crudService.save(inputProcessing)).thenReturn(inputProcessing);
        when(crudService.find(InputProcessing.class, INPUT_PROCESSING_ID)).thenReturn(inputProcessing);
        String context = "pacman." + CLIENT_CODE + "." + PROPERTY_CODE;
        when(pacmanConfigParamsService.getValue(context, GUIConfigParamName.BDE_COMPLETED_OVERDUE.value())).thenReturn("180");
        InputProcessing result = dailyProcessingService.updatePropertyDailyProcessing(INPUT_PROCESSING_ID, CORRELATION_ID, null);
        assertEquals(inputProcessing, result);
        ArgumentCaptor<IdAware> saveCaptor = ArgumentCaptor.forClass(IdAware.class);
        verify(crudService, times(2)).save(saveCaptor.capture());
        List<IdAware> values = saveCaptor.getAllValues();
        InputProcessing savedIp = (InputProcessing) values.get(0);
        assertEquals(CORRELATION_ID, savedIp.getInputId());
        assertEquals(InputProcessingStatus.IN_PROGRESS.toString(), savedIp.getStatus());
        assertNotNull(savedIp.getReceivedDate());
        assertNotNull(savedIp.getOverdueDate());
        PropertyDailyProcessing savedPdp = (PropertyDailyProcessing) values.get(1);
        assertEquals(DailyProcessingStatus.BDE_IN_PROGRESS.toString(), savedPdp.getStatus());
        assertEquals(ExtractStatus.COMPLETE, savedIp.getExtractStatus());
    }

    @Test
    public void test_inputReceived_withType() {
        String context = "pacman." + CLIENT_CODE + "." + PROPERTY_CODE;
        when(pacmanConfigParamsService.getValue(context, GUIConfigParamName.BDE_COMPLETED_OVERDUE.value())).thenReturn("180");
        ClientPropertyView cpv = new ClientPropertyView();
        cpv.setTimeZone(TIME_ZONE);
        when(crudService.findByNamedQuerySingleResult(ClientPropertyView.BY_CLIENT_CODE_PROPERTY_CODE, QueryParameter.with(ClientPropertyView.CLIENT_CODE, CLIENT_CODE).and(ClientPropertyView.PROPERTY_CODE, PROPERTY_CODE).parameters())).thenReturn(cpv);
        PropertyDailyProcessing pdp = new PropertyDailyProcessing();
        pdp.setPropertyTimeZone(TIME_ZONE);
        pdp.setPropertyCode(PROPERTY_CODE);
        pdp.setClientCode(CLIENT_CODE);
        List<PropertyDailyProcessing> pdps = List.of(pdp);
        when(crudService.findByCriteria(any(PropertyDailyProcessingCriteria.class))).thenReturn(pdps);
        dailyProcessingService.inputReceived(PROPERTY_CODE, CLIENT_CODE, CORRELATION_ID, InputType.BDE);
    }

    @Test
    public void test_inputReceived_withoutType() {
        String context = "pacman." + CLIENT_CODE + "." + PROPERTY_CODE;
        when(pacmanConfigParamsService.getValue(context, GUIConfigParamName.BDE_COMPLETED_OVERDUE.value())).thenReturn("180");
        ClientPropertyView cpv = new ClientPropertyView();
        cpv.setTimeZone(TIME_ZONE);
        when(crudService.findByNamedQuerySingleResult(ClientPropertyView.BY_CLIENT_CODE_PROPERTY_CODE, QueryParameter.with(ClientPropertyView.CLIENT_CODE, CLIENT_CODE).and(ClientPropertyView.PROPERTY_CODE, PROPERTY_CODE).parameters())).thenReturn(cpv);
        PropertyDailyProcessing pdp = new PropertyDailyProcessing();
        pdp.setPropertyTimeZone(TIME_ZONE);
        pdp.setPropertyCode(PROPERTY_CODE);
        pdp.setClientCode(CLIENT_CODE);
        pdp.setStatus(DailyProcessingStatus.BDE_NOT_RECEIVED.toString());
        List<PropertyDailyProcessing> pdps = List.of(pdp);
        when(crudService.findByCriteria(any(PropertyDailyProcessingCriteria.class))).thenReturn(pdps);
        dailyProcessingService.inputReceived(PROPERTY_CODE, CLIENT_CODE, CORRELATION_ID, InputType.BDE);
    }

    @Test
    public void testCreateOrUpdateInputProcessingForJob() {
        JobStepContext jobStepContext = new JobStepContext();
        jobStepContext.setJobInstanceId(123L);
        PlatformThreadLocalContextHolder.setJobStepContext(jobStepContext);
        PropertyDailyProcessing pdp = new PropertyDailyProcessing();
        pdp.setClientCode(CLIENT_CODE);
        pdp.setPropertyCode(PROPERTY_CODE);
        InputProcessing inputProcessing = new InputProcessing();
        inputProcessing.setInputType(InputType.BDE.toString());
        inputProcessing.setInputId(INPUT_FILE_NAME);
        pdp.setPropertyTimeZone(TIME_ZONE);
        when(pacmanConfigParamsService.getValue("pacman." + CLIENT_CODE + "." + PROPERTY_CODE, GUIConfigParamName.BDE_COMPLETED_OVERDUE.value())).thenReturn("1");
        when(pacmanConfigParamsService.getValue("pacman." + CLIENT_CODE + "." + PROPERTY_CODE, IntegrationConfigParamName.CORE_PROPERTY_TIME_ZONE.value())).thenReturn(TIME_ZONE);
        when(crudService.findByNamedQuerySingleResult(PropertyDailyProcessing.BY_CLIENT_CODE_PROPERTY_CODE_AND_DATE_WITH_INPUTS, QueryParameter.with(Constants.CLIENT_CODE, CLIENT_CODE).and(Constants.PROPERTY_CODE, PROPERTY_CODE).and("processingDate", new LocalDate(DateTimeZone.forTimeZone(TimeZone.getTimeZone(TIME_ZONE)))).parameters())).thenReturn(pdp);
        when(crudService.save(isA(InputProcessing.class))).thenReturn(inputProcessing);
        InputProcessing result = dailyProcessingService.createOrUpdateInputProcessingForJob(PROPERTY_CODE, CLIENT_CODE, INPUT_FILE_NAME, InputType.BDE);
        assertEquals(result, inputProcessing);
        ArgumentCaptor<InputProcessing> inputProcessingArgumentCaptor = ArgumentCaptor.forClass(InputProcessing.class);
        verify(crudService).save(inputProcessingArgumentCaptor.capture());
        assertEquals(inputProcessingArgumentCaptor.getValue().getInputId(), INPUT_FILE_NAME);
        assertEquals(inputProcessingArgumentCaptor.getValue().getInputType(), InputType.BDE.toString());
        assertEquals(inputProcessingArgumentCaptor.getValue().getJobs().get(0).getJobInstanceId().longValue(), 123L);
        verify(pacmanConfigParamsService).getValue("pacman." + CLIENT_CODE + "." + PROPERTY_CODE, GUIConfigParamName.BDE_COMPLETED_OVERDUE.value());
        verify(pacmanConfigParamsService).getValue("pacman." + CLIENT_CODE + "." + PROPERTY_CODE, IntegrationConfigParamName.CORE_PROPERTY_TIME_ZONE.value());
        verify(crudService).findByNamedQuerySingleResult(PropertyDailyProcessing.BY_CLIENT_CODE_PROPERTY_CODE_AND_DATE_WITH_INPUTS, QueryParameter.with(Constants.CLIENT_CODE, CLIENT_CODE).and(Constants.PROPERTY_CODE, PROPERTY_CODE).and("processingDate", new LocalDate(DateTimeZone.forTimeZone(TimeZone.getTimeZone(TIME_ZONE)))).parameters());
    }

    @Test
    public void inputReceivedIsDuplicateInput() {
        PropertyDailyProcessing pdp = new PropertyDailyProcessing();
        InputProcessing inputProcessing = new InputProcessing();
        inputProcessing.setInputType(InputType.BDE.toString());
        inputProcessing.setInputId(INPUT_FILE_NAME);
        inputProcessing.setStatus(InputProcessingStatus.COMPLETED.toString());
        pdp.setPropertyTimeZone(TIME_ZONE);
        pdp.addInputProcessing(inputProcessing);
        when(pacmanConfigParamsService.getValue("pacman." + CLIENT_CODE + "." + PROPERTY_CODE, IntegrationConfigParamName.CORE_PROPERTY_TIME_ZONE.value())).thenReturn(TIME_ZONE);
        when(crudService.findByNamedQuerySingleResult(PropertyDailyProcessing.BY_CLIENT_CODE_PROPERTY_CODE_AND_DATE_WITH_INPUTS, QueryParameter.with(Constants.CLIENT_CODE, CLIENT_CODE).and(Constants.PROPERTY_CODE, PROPERTY_CODE).and("processingDate", new LocalDate(DateTimeZone.forTimeZone(TimeZone.getTimeZone(TIME_ZONE)))).parameters())).thenReturn(pdp);
        when(crudService.save(inputProcessing)).thenReturn(inputProcessing);
        List<InputProcessing> inputProcessings = new ArrayList<>();
        inputProcessings.add(inputProcessing);
        inputProcessing.setPropertyDailyProcessing(pdp);
        when(crudService.findByCriteria(isA(InputProcessingCriteria.class))).thenReturn(inputProcessings);
        InputProcessing result = dailyProcessingService.inputReceived(PROPERTY_CODE, CLIENT_CODE, INPUT_FILE_NAME, InputType.CDP);
        assertEquals(inputProcessing, result);
        assertEquals(InputProcessingStatus.COMPLETED.toString(), result.getStatus());
        verify(pacmanConfigParamsService).getValue("pacman." + CLIENT_CODE + "." + PROPERTY_CODE, IntegrationConfigParamName.CORE_PROPERTY_TIME_ZONE.value());
        verify(crudService).findByNamedQuerySingleResult(PropertyDailyProcessing.BY_CLIENT_CODE_PROPERTY_CODE_AND_DATE_WITH_INPUTS, QueryParameter.with(Constants.CLIENT_CODE, CLIENT_CODE).and(Constants.PROPERTY_CODE, PROPERTY_CODE).and("processingDate", new LocalDate(DateTimeZone.forTimeZone(TimeZone.getTimeZone(TIME_ZONE)))).parameters());
    }

    @Test
    public void inputReceivedIsNewInput() {
        PropertyDailyProcessing pdp = new PropertyDailyProcessing();
        InputProcessing inputProcessing = new InputProcessing();
        inputProcessing.setInputType(InputType.CDP.toString());
        inputProcessing.setInputId(INPUT_FILE_NAME + "foo");
        pdp.addInputProcessing(inputProcessing);
        pdp.setPropertyTimeZone(TIME_ZONE);
        pdp.setClientCode(CLIENT_CODE);
        pdp.setPropertyCode(PROPERTY_CODE);
        when(pacmanConfigParamsService.getValue("pacman." + CLIENT_CODE + "." + PROPERTY_CODE, GUIConfigParamName.BDE_COMPLETED_OVERDUE.value())).thenReturn("1");
        when(pacmanConfigParamsService.getValue("pacman." + CLIENT_CODE + "." + PROPERTY_CODE, IntegrationConfigParamName.CORE_PROPERTY_TIME_ZONE.value())).thenReturn(TIME_ZONE);
        when(crudService.findByNamedQuerySingleResult(PropertyDailyProcessing.BY_CLIENT_CODE_PROPERTY_CODE_AND_DATE_WITH_INPUTS, QueryParameter.with(Constants.CLIENT_CODE, CLIENT_CODE).and(Constants.PROPERTY_CODE, PROPERTY_CODE).and("processingDate", new LocalDate(DateTimeZone.forTimeZone(TimeZone.getTimeZone(TIME_ZONE)))).parameters())).thenReturn(pdp);
        when(crudService.findByCriteria(any(InputProcessingCriteria.class))).thenReturn(new ArrayList<>());
        InputProcessing result = dailyProcessingService.inputReceived(PROPERTY_CODE, CLIENT_CODE, INPUT_FILE_NAME, InputType.BDE);
        assert (inputProcessing != result);
        verify(pacmanConfigParamsService).getValue("pacman." + CLIENT_CODE + "." + PROPERTY_CODE, IntegrationConfigParamName.CORE_PROPERTY_TIME_ZONE.value());
        verify(crudService).findByNamedQuerySingleResult(PropertyDailyProcessing.BY_CLIENT_CODE_PROPERTY_CODE_AND_DATE_WITH_INPUTS, QueryParameter.with(Constants.CLIENT_CODE, CLIENT_CODE).and(Constants.PROPERTY_CODE, PROPERTY_CODE).and("processingDate", new LocalDate(DateTimeZone.forTimeZone(TimeZone.getTimeZone(TIME_ZONE)))).parameters());
        verify(pacmanConfigParamsService).getValue("pacman." + CLIENT_CODE + "." + PROPERTY_CODE, GUIConfigParamName.BDE_COMPLETED_OVERDUE.value());
    }

    @Test
    public void test_inputReceivedCdp() {
        PropertyDailyProcessing pdp = new PropertyDailyProcessing();
        InputProcessing inputProcessing1 = new InputProcessing();
        inputProcessing1.setInputType(InputType.CDP.toString());
        inputProcessing1.setInputId("N/A");
        inputProcessing1.setCdpScheduleId(SCHEDULE_ID);
        pdp.addInputProcessing(inputProcessing1);
        InputProcessing inputProcessing2 = new InputProcessing();
        inputProcessing2.setInputType(InputType.CDP.toString());
        inputProcessing2.setInputId("N/A");
        pdp.addInputProcessing(inputProcessing2);
        pdp.setPropertyTimeZone(TIME_ZONE);
        pdp.setClientCode(CLIENT_CODE);
        pdp.setPropertyCode(PROPERTY_CODE);
        Property property = new Property();
        property.setId(PROPERTY_ID);
        WorkContextType workContext = new WorkContextType();
        workContext.setPropertyId(PROPERTY_ID + 1);
        PacmanWorkContextHelper.setWorkContext(workContext);
        List<CdpSchedule> cdpSchedules = new ArrayList<>();
        CdpSchedule schedule = new CdpSchedule();
        schedule.setId(SCHEDULE_ID);
        cdpSchedules.add(schedule);
        String context = "pacman." + CLIENT_CODE + "." + PROPERTY_CODE;
        when(multiPropertyCrudService.findByNamedQueryForSingleProperty(PROPERTY_ID, CdpSchedule.ALL_BY_TIME, new HashMap<>())).thenReturn(cdpSchedules);
        when(pacmanConfigParamsService.getValue(context, GUIConfigParamName.CDP_ARRIVED_OVERDUE.value())).thenReturn("240");
        when(pacmanConfigParamsService.getValue(context, GUIConfigParamName.CDP_COMPLETED_OVERDUE.value())).thenReturn("240");
        when(propertyService.getPropertyByCode(CLIENT_CODE, PROPERTY_CODE)).thenReturn(property);
        when(crudService.save(inputProcessing1)).thenReturn(inputProcessing1);
        when(pacmanConfigParamsService.getValue("pacman." + CLIENT_CODE + "." + PROPERTY_CODE, IntegrationConfigParamName.CORE_PROPERTY_TIME_ZONE.value())).thenReturn(TIME_ZONE);
        when(crudService.findByNamedQuerySingleResult(PropertyDailyProcessing.BY_CLIENT_CODE_PROPERTY_CODE_AND_DATE_WITH_INPUTS, QueryParameter.with(Constants.CLIENT_CODE, CLIENT_CODE).and(Constants.PROPERTY_CODE, PROPERTY_CODE).and("processingDate", new LocalDate(DateTimeZone.forTimeZone(TimeZone.getTimeZone(TIME_ZONE)))).parameters())).thenReturn(pdp);
        InputProcessing result = dailyProcessingService.inputReceived(PROPERTY_CODE, CLIENT_CODE, INPUT_FILE_NAME, InputType.CDP);
        assertEquals(inputProcessing1, result);
        assertNotNull(result.getReceivedDate());
        assertEquals(INPUT_FILE_NAME, result.getInputId());
        assertNotNull(result.getOverdueDate());
        assertEquals(InputProcessingStatus.IN_PROGRESS.toString(), result.getStatus());
        verify(multiPropertyCrudService).findByNamedQueryForSingleProperty(PROPERTY_ID, CdpSchedule.ALL_BY_TIME, new HashMap<>());
        assertEquals(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId());
        verify(pacmanConfigParamsService).getValue("pacman." + CLIENT_CODE + "." + PROPERTY_CODE, IntegrationConfigParamName.CORE_PROPERTY_TIME_ZONE.value());
        verify(crudService).findByNamedQuerySingleResult(PropertyDailyProcessing.BY_CLIENT_CODE_PROPERTY_CODE_AND_DATE_WITH_INPUTS, QueryParameter.with(Constants.CLIENT_CODE, CLIENT_CODE).and(Constants.PROPERTY_CODE, PROPERTY_CODE).and("processingDate", new LocalDate(DateTimeZone.forTimeZone(TimeZone.getTimeZone(TIME_ZONE)))).parameters());
    }

    @SuppressWarnings("unchecked")
    @Test
    public void test_inputReceivedMultipleCdps() {
        PropertyDailyProcessing pdp = new PropertyDailyProcessing();
        InputProcessing inputProcessing1 = new InputProcessing();
        inputProcessing1.setInputType(InputType.CDP.toString());
        inputProcessing1.setInputId("N/A");
        inputProcessing1.setCdpScheduleId(1);
        pdp.addInputProcessing(inputProcessing1);
        InputProcessing inputProcessing2 = new InputProcessing();
        inputProcessing2.setInputType(InputType.CDP.toString());
        inputProcessing2.setInputId("N/A");
        inputProcessing2.setCdpScheduleId(2);
        pdp.addInputProcessing(inputProcessing2);
        pdp.setPropertyTimeZone(TIME_ZONE);
        pdp.setClientCode(CLIENT_CODE);
        pdp.setPropertyCode(PROPERTY_CODE);
        Property property = new Property();
        property.setId(PROPERTY_ID);
        List<CdpSchedule> schedules = new ArrayList<>();
        CdpSchedule schedule1 = new CdpSchedule();
        schedule1.setId(1);
        CdpSchedule schedule2 = new CdpSchedule();
        schedule2.setId(2);
        schedules.add(schedule1);
        schedules.add(schedule2);
        when(propertyService.getPropertyByCode(CLIENT_CODE, PROPERTY_CODE)).thenReturn(property);
        when(crudService.save(inputProcessing1)).thenReturn(inputProcessing1);
        String context = "pacman." + CLIENT_CODE + "." + PROPERTY_CODE;
        when(pacmanConfigParamsService.getValue(context, GUIConfigParamName.CDP_ARRIVED_OVERDUE.value())).thenReturn("240");
        when(pacmanConfigParamsService.getValue(context, GUIConfigParamName.CDP_COMPLETED_OVERDUE.value())).thenReturn("240");
        when(multiPropertyCrudService.findByNamedQueryForSingleProperty(eq(PROPERTY_ID), eq(CdpSchedule.ALL_BY_TIME), anyMap())).thenReturn(schedules);
        when(pacmanConfigParamsService.getValue("pacman." + CLIENT_CODE + "." + PROPERTY_CODE, IntegrationConfigParamName.CORE_PROPERTY_TIME_ZONE.value())).thenReturn(TIME_ZONE);
        when(crudService.findByNamedQuerySingleResult(PropertyDailyProcessing.BY_CLIENT_CODE_PROPERTY_CODE_AND_DATE_WITH_INPUTS, QueryParameter.with(Constants.CLIENT_CODE, CLIENT_CODE).and(Constants.PROPERTY_CODE, PROPERTY_CODE).and("processingDate", new LocalDate(DateTimeZone.forTimeZone(TimeZone.getTimeZone(TIME_ZONE)))).parameters())).thenReturn(pdp);
        InputProcessing result = dailyProcessingService.inputReceived(PROPERTY_CODE, CLIENT_CODE, INPUT_FILE_NAME, InputType.CDP);
        assertEquals(inputProcessing1, result);
        assertNotNull(result.getReceivedDate());
        assertEquals(INPUT_FILE_NAME, result.getInputId());
        assertEquals(InputProcessingStatus.IN_PROGRESS.toString(), result.getStatus());
        assertNotNull(result.getOverdueDate());
        verify(pacmanConfigParamsService).getValue("pacman." + CLIENT_CODE + "." + PROPERTY_CODE, IntegrationConfigParamName.CORE_PROPERTY_TIME_ZONE.value());
        verify(crudService).findByNamedQuerySingleResult(PropertyDailyProcessing.BY_CLIENT_CODE_PROPERTY_CODE_AND_DATE_WITH_INPUTS, QueryParameter.with(Constants.CLIENT_CODE, CLIENT_CODE).and(Constants.PROPERTY_CODE, PROPERTY_CODE).and("processingDate", new LocalDate(DateTimeZone.forTimeZone(TimeZone.getTimeZone(TIME_ZONE)))).parameters());
    }

    @SuppressWarnings("unchecked")
    @Test
    public void test_inputReceivedMultipleCdps_someSkipped() {
        PropertyDailyProcessing pdp = new PropertyDailyProcessing();
        InputProcessing inputProcessing1 = new InputProcessing();
        inputProcessing1.setInputType(InputType.CDP.toString());
        inputProcessing1.setInputId("N/A");
        inputProcessing1.setCdpScheduleId(1);
        inputProcessing1.setStatus(InputProcessingStatus.SKIPPED.toString());
        pdp.addInputProcessing(inputProcessing1);
        InputProcessing inputProcessing2 = new InputProcessing();
        inputProcessing2.setInputType(InputType.CDP.toString());
        inputProcessing2.setInputId("N/A");
        inputProcessing2.setCdpScheduleId(2);
        pdp.addInputProcessing(inputProcessing2);
        pdp.setPropertyTimeZone(TIME_ZONE);
        pdp.setClientCode(CLIENT_CODE);
        pdp.setPropertyCode(PROPERTY_CODE);
        Property property = new Property();
        property.setId(PROPERTY_ID);
        List<CdpSchedule> schedules = new ArrayList<CdpSchedule>();
        CdpSchedule schedule1 = new CdpSchedule();
        schedule1.setId(1);
        CdpSchedule schedule2 = new CdpSchedule();
        schedule2.setId(2);
        schedules.add(schedule1);
        schedules.add(schedule2);
        when(propertyService.getPropertyByCode(CLIENT_CODE, PROPERTY_CODE)).thenReturn(property);
        when(crudService.save(inputProcessing2)).thenReturn(inputProcessing2);
        String context = "pacman." + CLIENT_CODE + "." + PROPERTY_CODE;
        when(pacmanConfigParamsService.getValue(context, GUIConfigParamName.CDP_ARRIVED_OVERDUE.value())).thenReturn("240");
        when(pacmanConfigParamsService.getValue(context, GUIConfigParamName.CDP_COMPLETED_OVERDUE.value())).thenReturn("240");
        when(multiPropertyCrudService.findByNamedQueryForSingleProperty(eq(PROPERTY_ID), eq(CdpSchedule.ALL_BY_TIME), anyMap())).thenReturn(schedules);
        when(pacmanConfigParamsService.getValue("pacman." + CLIENT_CODE + "." + PROPERTY_CODE, IntegrationConfigParamName.CORE_PROPERTY_TIME_ZONE.value())).thenReturn(TIME_ZONE);
        when(crudService.findByNamedQuerySingleResult(PropertyDailyProcessing.BY_CLIENT_CODE_PROPERTY_CODE_AND_DATE_WITH_INPUTS, QueryParameter.with(Constants.CLIENT_CODE, CLIENT_CODE).and(Constants.PROPERTY_CODE, PROPERTY_CODE).and("processingDate", new LocalDate(DateTimeZone.forTimeZone(TimeZone.getTimeZone(TIME_ZONE)))).parameters())).thenReturn(pdp);
        InputProcessing result = dailyProcessingService.inputReceived(PROPERTY_CODE, CLIENT_CODE, INPUT_FILE_NAME, InputType.CDP);
        assertEquals(inputProcessing2, result);
        assertNotNull(result.getReceivedDate());
        assertEquals(INPUT_FILE_NAME, result.getInputId());
        assertEquals(InputProcessingStatus.IN_PROGRESS.toString(), result.getStatus());
        assertNotNull(result.getOverdueDate());
        verify(pacmanConfigParamsService).getValue("pacman." + CLIENT_CODE + "." + PROPERTY_CODE, IntegrationConfigParamName.CORE_PROPERTY_TIME_ZONE.value());
        verify(crudService).findByNamedQuerySingleResult(PropertyDailyProcessing.BY_CLIENT_CODE_PROPERTY_CODE_AND_DATE_WITH_INPUTS, QueryParameter.with(Constants.CLIENT_CODE, CLIENT_CODE).and(Constants.PROPERTY_CODE, PROPERTY_CODE).and("processingDate", new LocalDate(DateTimeZone.forTimeZone(TimeZone.getTimeZone(TIME_ZONE)))).parameters());
    }

    @Test
    public void test_inputSkipped() {
        PropertyDailyProcessing pdp = new PropertyDailyProcessing();
        pdp.setExpectedCdpCount(2);
        pdp.setCompletedCdpCount(1);
        InputProcessing inputProcessing = new InputProcessing();
        inputProcessing.setInputType(InputType.CDP.toString());
        inputProcessing.setInputId(INPUT_FILE_NAME);
        inputProcessing.setStatus(InputProcessingStatus.IN_PROGRESS.toString());
        inputProcessing.setOverdueDate(LocalDateTime.now());
        inputProcessing.setPropertyDailyProcessing(pdp);
        inputProcessing.setInputProcessingId(INPUT_PROCESSING_ID);
        List<InputProcessing> inputProcessingList = new ArrayList<>();
        inputProcessingList.add(inputProcessing);
        when(crudService.save(inputProcessing)).thenReturn(inputProcessing);
        when(crudService.findByCriteria(any(InputProcessingCriteria.class))).thenReturn(inputProcessingList);
        when(crudService.find(InputProcessing.class, INPUT_PROCESSING_ID)).thenReturn(inputProcessing);
        InputProcessing result = dailyProcessingService.inputSkipped(inputProcessing);
        assertEquals(2, pdp.getCompletedCdpCount().intValue());
        assertEquals(DailyProcessingStatus.DAILY_PROCESSING_COMPLETED.toString(), pdp.getStatus());
        assertEquals(InputProcessingStatus.SKIPPED.toString(), inputProcessing.getStatus());
        assertNull(inputProcessing.getOverdueDate());
        assertEquals(inputProcessing, result);
    }


    @Test
    public void test_getJobInstanceIds() {
        Long JOB_INSTANCE_ID = 516L;
        InputProcessing inputProcessing = new InputProcessing();
        InputProcessingJob ipj = new InputProcessingJob();
        ipj.setJobInstanceId(JOB_INSTANCE_ID);
        inputProcessing.addJob(ipj);
        when(crudService.find(InputProcessing.class, INPUT_PROCESSING_ID)).thenReturn(inputProcessing);
        List<Long> results = dailyProcessingService.getJobInstanceIds(INPUT_PROCESSING_ID);
        assertEquals(1, results.size());
        assertEquals(JOB_INSTANCE_ID, results.get(0));
    }

    @SuppressWarnings("unchecked")
    @Test
    public void test_deleteAllProcessingRecords() {
        String expectedPropertyCode = "WHATEVS";
        String expectedClientCode = "WHATEVSCLI";
        @SuppressWarnings("rawtypes")
        ArgumentCaptor<Map> queryParameter = ArgumentCaptor.forClass(Map.class);
        when(crudService.findByNamedQuery(eq(InputProcessing.IDS_BY_PROPERTY_CODE), anyMap())).thenReturn(List.of(1));
        dailyProcessingService.deleteAllProcessingRecords(expectedPropertyCode, expectedClientCode);
        verify(crudService).findByNamedQuery(eq(InputProcessing.IDS_BY_PROPERTY_CODE), queryParameter.capture());
        verify(crudService).executeUpdateByNamedQuery(eq(DecisionDelivery.DELETE_BY_IP_IDS), anyMap());
        verify(crudService).executeUpdateByNamedQuery(eq(InputProcessingJob.DELETE_BY_IP_IDS), anyMap());
        verify(crudService).executeUpdateByNamedQuery(eq(InputProcessing.DELETE_BY_IDS), anyMap());
        verify(crudService).executeUpdateByNamedQuery(eq(PropertyDailyProcessing.DELETE_BY_PROPERTY_CODE), anyMap());
        assertEquals(queryParameter.getValue().get("propertyCode"), expectedPropertyCode);
    }

    @Test
    public void test_deleteAllProcessingRecordsGreaterThan2000() {
        List<Integer> ipIDs = new ArrayList<>();
        for (int i = 0; i < 2100; i++) {
            ipIDs.add(i);
        }
        String expectedPropertyCode = "WHATEVS";
        String expectedClientCode = "WHATEVSCLI";
        doReturn(ipIDs).when(crudService).findByNamedQuery(eq(InputProcessing.IDS_BY_PROPERTY_CODE), anyMap());
        dailyProcessingService.deleteAllProcessingRecords(expectedPropertyCode, expectedClientCode);
        verify(crudService, times(5)).executeUpdateByNamedQuery(eq(InputProcessingJob.DELETE_BY_IP_IDS), anyMap());
        verify(crudService, times(5)).executeUpdateByNamedQuery(eq(DecisionDelivery.DELETE_BY_IP_IDS), anyMap());
        verify(crudService, times(5)).executeUpdateByNamedQuery(eq(InputProcessing.DELETE_BY_IDS), anyMap());
        verify(crudService, times(1)).executeUpdateByNamedQuery(eq(PropertyDailyProcessing.DELETE_BY_PROPERTY_CODE), anyMap());
    }

    @Test
    public void test_deleteAllProcessingRecordsByClientCode() {
        String expectedClientCode = "BSTN";
        ArgumentCaptor<Map> queryParameter = ArgumentCaptor.forClass(Map.class);
        when(crudService.findByNamedQuery(eq(InputProcessing.IDS_BY_CLIENT_CODE), anyMap())).thenReturn(List.of(1));
        dailyProcessingService.deleteAllProcessingRecordsByClientCode(expectedClientCode);
        verify(crudService).findByNamedQuery(eq(InputProcessing.IDS_BY_CLIENT_CODE), queryParameter.capture());
        verify(crudService).executeUpdateByNamedQuery(eq(DecisionDelivery.DELETE_BY_IP_IDS), anyMap());
        verify(crudService).executeUpdateByNamedQuery(eq(InputProcessingJob.DELETE_BY_IP_IDS), anyMap());
        verify(crudService).executeUpdateByNamedQuery(eq(InputProcessing.DELETE_BY_IDS), anyMap());
        verify(crudService).executeUpdateByNamedQuery(eq(PropertyDailyProcessing.DELETE_BY_CLIENT_CODE), anyMap());
    }

    @Test
    public void test_deleteAllProcessingRecordsByClientCodeGreaterThan2000() {
        List<Integer> ipIDs = new ArrayList<>();
        for (int i = 0; i < 2100; i++) {
            ipIDs.add(i);
        }
        doReturn(ipIDs).when(crudService).findByNamedQuery(eq(InputProcessing.IDS_BY_CLIENT_CODE), anyMap());
        dailyProcessingService.deleteAllProcessingRecordsByClientCode("BSTN");
        verify(crudService, times(5)).executeUpdateByNamedQuery(eq(InputProcessingJob.DELETE_BY_IP_IDS), anyMap());
        verify(crudService, times(5)).executeUpdateByNamedQuery(eq(DecisionDelivery.DELETE_BY_IP_IDS), anyMap());
        verify(crudService, times(5)).executeUpdateByNamedQuery(eq(InputProcessing.DELETE_BY_IDS), anyMap());
        verify(crudService, times(1)).executeUpdateByNamedQuery(eq(PropertyDailyProcessing.DELETE_BY_CLIENT_CODE), anyMap());
    }

    @Test
    public void test_deleteAllProcessingRecordsByClientCodeNoInputProcessing() {
        dailyProcessingService.deleteAllProcessingRecordsByClientCode("BSTN");
        verify(crudService, times(0)).executeUpdateByNamedQuery(eq(InputProcessingJob.DELETE_BY_IP_IDS), anyMap());
        verify(crudService, times(0)).executeUpdateByNamedQuery(eq(DecisionDelivery.DELETE_BY_IP_IDS), anyMap());
        verify(crudService, times(0)).executeUpdateByNamedQuery(eq(InputProcessing.DELETE_BY_IDS), anyMap());
        verify(crudService, times(1)).executeUpdateByNamedQuery(eq(PropertyDailyProcessing.DELETE_BY_CLIENT_CODE), anyMap());
    }

    @SuppressWarnings("unchecked")
    @Test
    public void test_deleteAllProcessingRecords_noInputProcessing() {
        String expectedPropertyCode = "WHATEVS";
        String expectedClientCode = "WHATEVSCLI";
        @SuppressWarnings("rawtypes")
        ArgumentCaptor<Map> queryParameter = ArgumentCaptor.forClass(Map.class);
        dailyProcessingService.deleteAllProcessingRecords(expectedPropertyCode, expectedClientCode);
        verify(crudService).findByNamedQuery(eq(InputProcessing.IDS_BY_PROPERTY_CODE), queryParameter.capture());
        verify(crudService).executeUpdateByNamedQuery(eq(PropertyDailyProcessing.DELETE_BY_PROPERTY_CODE), anyMap());
        assertEquals(queryParameter.getValue().get("propertyCode"), expectedPropertyCode);
    }

    @Test
    public void getInputProcessingForDateAndType() {
        List<InputProcessing> inputProcessings = new ArrayList<>();
        InputProcessing expectedInputProcessing = new InputProcessing();
        inputProcessings.add(expectedInputProcessing);
        LocalDate processingDate = LocalDate.now();
        when(crudService.<InputProcessing>findByNamedQuery(InputProcessing.BY_PROPERTY_DATE_TYPE, QueryParameter.with("clientCode", "CL123").and("propertyCode", "PR123").and("processingDate", processingDate).and("inputType", InputType.CDP.toString()).parameters())).thenReturn(inputProcessings);
        List<InputProcessing> foundInputProcessings = dailyProcessingService.getInputProcessings("CL123", "PR123", processingDate, InputType.CDP);
        Assertions.assertNotNull(foundInputProcessings);
        assertEquals(expectedInputProcessing, foundInputProcessings.get(0));
    }

    @Test
    public void getBDEForToday() {
        InputProcessing inputProcessing = new InputProcessing();
        LocalDateTime localDateTime = new LocalDateTime();
        when(propertyService.getPropertyById(5)).thenReturn(getPropertyWithClient());
        when(propertyService.getLocalDateTime(5)).thenReturn(localDateTime);
        when(crudService.findByNamedQuery(eq(InputProcessing.BY_PROPERTY_DATE_TYPE), anyMap())).thenReturn(Collections.singletonList(inputProcessing));
        assertEquals(inputProcessing, dailyProcessingService.getBDEForToday(5));
        verify(propertyService).getLocalDateTime(5);
        verify(crudService).findByNamedQuery(eq(InputProcessing.BY_PROPERTY_DATE_TYPE), anyMap());
    }

    @Test
    public void getBDEForToday_NoResult() {
        LocalDateTime localDateTime = new LocalDateTime();
        when(propertyService.getPropertyById(5)).thenReturn(getPropertyWithClient());
        when(propertyService.getLocalDateTime(5)).thenReturn(localDateTime);
        when(crudService.findByNamedQuery(eq(InputProcessing.BY_PROPERTY_DATE_TYPE), anyMap())).thenReturn(new ArrayList<>());
        assertNull(dailyProcessingService.getBDEForToday(5));
        verify(propertyService).getLocalDateTime(5);
        verify(crudService).findByNamedQuery(eq(InputProcessing.BY_PROPERTY_DATE_TYPE), anyMap());
    }

    @Test
    public void findInputProcessingSingleResult() {
        InputProcessing inputProcessing = new InputProcessing();
        when(crudService.find(InputProcessing.class, 5)).thenReturn(inputProcessing);
        dailyProcessingService.findInputProcessingSingleResult(5);
        verify(crudService).find(InputProcessing.class, 5);
    }

    @Test
    public void saveInputProcessing() {
        InputProcessing inputProcessing = new InputProcessing();
        dailyProcessingService.saveInputProcessing(inputProcessing);
        verify(crudService).save(inputProcessing);
    }

    @Test
    public void getBDEByDate() {
        LocalDate localDate = new LocalDate();
        when(propertyService.getPropertyById(5)).thenReturn(getPropertyWithClient());
        when(crudService.findByNamedQuerySingleResult(eq(InputProcessing.BY_PROPERTY_DATE_TYPE), anyMap())).thenReturn(new InputProcessing());
        dailyProcessingService.getBDEByDate(5, localDate);
        verify(propertyService).getPropertyById(5);
        verify(crudService).findByNamedQuerySingleResult(eq(InputProcessing.BY_PROPERTY_DATE_TYPE), anyMap());
    }

    @Test
    public void purgeDailyProcessingRecords() {
        when(pacmanConfigParamsService.getValue("pacman", GUIConfigParamName.CORE_INTERNAL_HISTORY_DAYS_TO_RETAIN.value())).thenReturn("5");
        when(crudService.executeUpdateByNamedQuery(eq(PropertyDailyProcessing.DELETE_BY_DATE), anyMap())).thenReturn(1);
        dailyProcessingService.purgeDailyProcessingRecords();
        verify(pacmanConfigParamsService).getValue("pacman", GUIConfigParamName.CORE_INTERNAL_HISTORY_DAYS_TO_RETAIN.value());
        verify(crudService).executeUpdateByNamedQuery(eq(PropertyDailyProcessing.DELETE_BY_DATE), anyMap());
    }

    @Test
    public void test_cancelPreviousOverdueInputs() {
        PropertyDailyProcessing pdp = new PropertyDailyProcessing();
        pdp.setClientCode(CLIENT_CODE);
        pdp.setPropertyCode(PROPERTY_CODE);
        pdp.setCompletedCdpCount(0);
        pdp.setExpectedCdpCount(1);
        pdp.setProcessingDate(MIDDLE_DATE);
        InputProcessing inputProcessing = new InputProcessing();
        inputProcessing.setInputType(InputType.BDE.toString());
        inputProcessing.setInputProcessingId(INPUT_PROCESSING_ID);
        Set<String> statuses = new HashSet<>();
        statuses.add(InputProcessingStatus.NOT_RECEIVED.toString());
        inputProcessing.setPropertyDailyProcessing(pdp);
        List<InputProcessing> inputProcessings = new ArrayList<>();
        PropertyDailyProcessing pdp1 = new PropertyDailyProcessing();
        pdp1.setClientCode(CLIENT_CODE);
        pdp1.setPropertyCode(PROPERTY_CODE);
        pdp1.setCompletedCdpCount(0);
        pdp1.setExpectedCdpCount(1);
        pdp1.setProcessingDate(EARLY_DATE);
        InputProcessing overdueInput1 = new InputProcessing();
        overdueInput1.setInputProcessingId(OVERDUE_1_ID);
        overdueInput1.setInputType(InputType.CDP.toString());
        overdueInput1.setOverdueDate(LocalDateTime.now());
        overdueInput1.setPropertyDailyProcessing(pdp1);
        overdueInput1.setStatus(InputProcessingStatus.NOT_RECEIVED.toString());
        InputProcessing overdueInput2 = new InputProcessing();
        overdueInput2.setInputProcessingId(OVERDUE_2_ID);
        overdueInput2.setInputType(InputType.BDE.toString());
        overdueInput2.setOverdueDate(LocalDateTime.now());
        overdueInput2.setPropertyDailyProcessing(pdp1);
        overdueInput2.setStatus(InputProcessingStatus.IN_PROGRESS.toString());
        inputProcessings.add(overdueInput1);
        inputProcessings.add(overdueInput2);
        when(crudService.find(InputProcessing.class, OVERDUE_1_ID)).thenReturn(overdueInput1);
        when(crudService.find(InputProcessing.class, OVERDUE_2_ID)).thenReturn(overdueInput2);
        when(crudService.findByCriteria(any(InputProcessingCriteria.class))).thenReturn(inputProcessings);
        dailyProcessingService.cancelPreviousOverdueInputs(inputProcessing, statuses);
        ArgumentCaptor<InputProcessingCriteria> queryCaptor = ArgumentCaptor.forClass(InputProcessingCriteria.class);
        verify(crudService).findByCriteria(queryCaptor.capture());
        InputProcessingCriteria criteria = queryCaptor.getValue();
        assertEquals(true, criteria.getOverdue());
        assertEquals(1, criteria.getClientCodes().size());
        assertTrue(criteria.getClientCodes().contains(CLIENT_CODE));
        assertEquals(1, criteria.getPropertyCodes().size());
        assertTrue(criteria.getPropertyCodes().contains(PROPERTY_CODE));
        assertTrue(criteria.getInputTypes().contains(InputType.BDE.toString()));
        assertTrue(criteria.getInputTypes().contains(InputType.CDP.toString()));
        assertEquals(2, criteria.getInputTypes().size());
        assertTrue(criteria.getStatuses().contains(InputProcessingStatus.NOT_RECEIVED.toString()));
        assertEquals(1, criteria.getStatuses().size());
        assertEquals(true, criteria.getOverdue());
        assertNull(overdueInput1.getOverdueDate());
        assertNull(overdueInput2.getOverdueDate());
        assertEquals(InputProcessingStatus.SKIPPED.toString(), overdueInput1.getStatus());
        assertEquals(InputProcessingStatus.SKIPPED.toString(), overdueInput2.getStatus());
        assertEquals(1, pdp1.getCompletedCdpCount().intValue());
        assertEquals(DailyProcessingStatus.DAILY_PROCESSING_COMPLETED.toString(), pdp1.getStatus());
    }

    @Test
    public void test_cancelPreviousOverdueInputsAlreadyReceived() {
        PropertyDailyProcessing pdp = new PropertyDailyProcessing();
        pdp.setClientCode(CLIENT_CODE);
        pdp.setPropertyCode(PROPERTY_CODE);
        pdp.setCompletedCdpCount(0);
        pdp.setExpectedCdpCount(1);
        pdp.setProcessingDate(MIDDLE_DATE);
        InputProcessing inputProcessing = new InputProcessing();
        inputProcessing.setInputType(InputType.BDE.toString());
        inputProcessing.setInputProcessingId(INPUT_PROCESSING_ID);
        Set<String> statuses = new HashSet<>();
        statuses.add(InputProcessingStatus.NOT_RECEIVED.toString());
        inputProcessing.setPropertyDailyProcessing(pdp);
        List<InputProcessing> inputProcessings = new ArrayList<>();
        PropertyDailyProcessing pdp1 = new PropertyDailyProcessing();
        pdp1.setClientCode(CLIENT_CODE);
        pdp1.setPropertyCode(PROPERTY_CODE);
        pdp1.setCompletedCdpCount(0);
        pdp1.setExpectedCdpCount(0);
        pdp1.setProcessingDate(EARLY_DATE);
        InputProcessing overdueInput1 = new InputProcessing();
        overdueInput1.setInputProcessingId(OVERDUE_1_ID);
        overdueInput1.setInputType(InputType.CDP.toString());
        overdueInput1.setInputId("1234-4321");
        overdueInput1.setOverdueDate(LocalDateTime.now());
        overdueInput1.setStatus(InputProcessingStatus.NOT_RECEIVED.toString());
        overdueInput1.setPropertyDailyProcessing(pdp);
        InputProcessing overdueInput2 = new InputProcessing();
        overdueInput2.setInputProcessingId(OVERDUE_2_ID);
        overdueInput2.setInputType(InputType.BDE.toString());
        overdueInput2.setOverdueDate(LocalDateTime.now());
        overdueInput2.setPropertyDailyProcessing(pdp1);
        overdueInput2.setStatus(InputProcessingStatus.NOT_RECEIVED.toString());
        inputProcessings.add(overdueInput1);
        inputProcessings.add(overdueInput2);
        when(crudService.find(InputProcessing.class, OVERDUE_1_ID)).thenReturn(overdueInput1);
        when(crudService.find(InputProcessing.class, OVERDUE_2_ID)).thenReturn(overdueInput2);
        when(crudService.findByCriteria(any(InputProcessingCriteria.class))).thenReturn(inputProcessings);
        dailyProcessingService.cancelPreviousOverdueInputs(inputProcessing, statuses);
        ArgumentCaptor<InputProcessingCriteria> queryCaptor = ArgumentCaptor.forClass(InputProcessingCriteria.class);
        verify(crudService).findByCriteria(queryCaptor.capture());
        InputProcessingCriteria criteria = queryCaptor.getValue();
        assertEquals(true, criteria.getOverdue());
        assertEquals(1, criteria.getClientCodes().size());
        assertTrue(criteria.getClientCodes().contains(CLIENT_CODE));
        assertEquals(1, criteria.getPropertyCodes().size());
        assertTrue(criteria.getPropertyCodes().contains(PROPERTY_CODE));
        assertTrue(criteria.getInputTypes().contains(InputType.BDE.toString()));
        assertTrue(criteria.getInputTypes().contains(InputType.CDP.toString()));
        assertEquals(2, criteria.getInputTypes().size());
        assertTrue(criteria.getStatuses().contains(InputProcessingStatus.NOT_RECEIVED.toString()));
        assertEquals(1, criteria.getStatuses().size());
        assertEquals(true, criteria.getOverdue());
        assertNotNull(overdueInput1.getOverdueDate());
        assertNull(overdueInput2.getOverdueDate());
        assertEquals(InputProcessingStatus.NOT_RECEIVED.toString(), overdueInput1.getStatus());
        assertEquals(InputProcessingStatus.SKIPPED.toString(), overdueInput2.getStatus());
        assertEquals(0, pdp1.getCompletedCdpCount().intValue());
    }

    @Test
    public void test_cancelPreviousOverdueInputsInPast() {
        PropertyDailyProcessing pdp = new PropertyDailyProcessing();
        pdp.setClientCode(CLIENT_CODE);
        pdp.setPropertyCode(PROPERTY_CODE);
        pdp.setCompletedCdpCount(0);
        pdp.setExpectedCdpCount(1);
        pdp.setProcessingDate(MIDDLE_DATE);
        InputProcessing inputProcessing = new InputProcessing();
        inputProcessing.setInputType(InputType.BDE.toString());
        inputProcessing.setInputProcessingId(INPUT_PROCESSING_ID);
        Set<String> statuses = new HashSet<>();
        statuses.add(InputProcessingStatus.NOT_RECEIVED.toString());
        inputProcessing.setPropertyDailyProcessing(pdp);
        List<InputProcessing> inputProcessings = new ArrayList<>();
        PropertyDailyProcessing pdp1 = new PropertyDailyProcessing();
        pdp1.setClientCode(CLIENT_CODE);
        pdp1.setPropertyCode(PROPERTY_CODE);
        pdp1.setCompletedCdpCount(0);
        pdp1.setExpectedCdpCount(1);
        pdp1.setProcessingDate(EARLY_DATE);
        InputProcessing overdueInput1 = new InputProcessing();
        overdueInput1.setInputProcessingId(OVERDUE_1_ID);
        overdueInput1.setInputType(InputType.CDP.toString());
        overdueInput1.setOverdueDate(LocalDateTime.now());
        overdueInput1.setPropertyDailyProcessing(pdp1);
        overdueInput1.setStatus(InputProcessingStatus.NOT_RECEIVED.toString());
        InputProcessing overdueInput2 = new InputProcessing();
        PropertyDailyProcessing pdp2 = new PropertyDailyProcessing();
        pdp2.setClientCode(CLIENT_CODE);
        pdp2.setPropertyCode(PROPERTY_CODE);
        pdp2.setCompletedCdpCount(0);
        pdp2.setExpectedCdpCount(1);
        pdp2.setProcessingDate(LATE_DATE);
        overdueInput2.setInputProcessingId(OVERDUE_2_ID);
        overdueInput2.setInputType(InputType.BDE.toString());
        overdueInput2.setOverdueDate(LocalDateTime.now());
        overdueInput2.setPropertyDailyProcessing(pdp2);
        overdueInput2.setStatus(InputProcessingStatus.IN_PROGRESS.toString());
        inputProcessings.add(overdueInput1);
        inputProcessings.add(overdueInput2);
        when(crudService.find(InputProcessing.class, OVERDUE_1_ID)).thenReturn(overdueInput1);
        when(crudService.find(InputProcessing.class, OVERDUE_2_ID)).thenReturn(overdueInput2);
        when(crudService.findByCriteria(any(InputProcessingCriteria.class))).thenReturn(inputProcessings);
        dailyProcessingService.cancelPreviousOverdueInputs(inputProcessing, statuses);
        ArgumentCaptor<InputProcessingCriteria> queryCaptor = ArgumentCaptor.forClass(InputProcessingCriteria.class);
        verify(crudService).findByCriteria(queryCaptor.capture());
        InputProcessingCriteria criteria = queryCaptor.getValue();
        assertEquals(true, criteria.getOverdue());
        assertEquals(1, criteria.getClientCodes().size());
        assertTrue(criteria.getClientCodes().contains(CLIENT_CODE));
        assertEquals(1, criteria.getPropertyCodes().size());
        assertTrue(criteria.getPropertyCodes().contains(PROPERTY_CODE));
        assertTrue(criteria.getInputTypes().contains(InputType.BDE.toString()));
        assertTrue(criteria.getInputTypes().contains(InputType.CDP.toString()));
        assertEquals(2, criteria.getInputTypes().size());
        assertTrue(criteria.getStatuses().contains(InputProcessingStatus.NOT_RECEIVED.toString()));
        assertEquals(1, criteria.getStatuses().size());
        assertEquals(true, criteria.getOverdue());
        assertNull(overdueInput1.getOverdueDate());
        assertNotNull(overdueInput2.getOverdueDate());
        assertEquals(InputProcessingStatus.SKIPPED.toString(), overdueInput1.getStatus());
        assertEquals(InputProcessingStatus.IN_PROGRESS.toString(), overdueInput2.getStatus());
        assertEquals(1, pdp1.getCompletedCdpCount().intValue());
        assertEquals(DailyProcessingStatus.DAILY_PROCESSING_COMPLETED.toString(), pdp1.getStatus());
    }

    @SuppressWarnings("unchecked")
    @Test
    public void test_forceCompleteInputProcessingByClient() {
        when(crudService.findByCriteria(any(InputProcessingCriteria.class))).thenReturn(createInputProcessingList());
        String response = dailyProcessingService.forceCompleteInputProcessingByClient(CLIENT_CODE, EARLY_DATE);
        assertEquals(response, "Marked 2 Input_Processing records as COMPLETED");
        ArgumentCaptor<List> saveCaptor = ArgumentCaptor.forClass(List.class);
        verify(crudService).save(saveCaptor.capture());
        List<InputProcessing> savedEntities = saveCaptor.getValue();
        assertEquals(2, savedEntities.size());
        for (InputProcessing entity : savedEntities) {
            assertEquals(InputProcessingStatus.COMPLETED.toString(), entity.getStatus());
            assertNotNull(entity.getCompletedDate());
        }
        ArgumentCaptor<InputProcessingCriteria> queryCaptor = ArgumentCaptor.forClass(InputProcessingCriteria.class);
        verify(crudService).findByCriteria(queryCaptor.capture());
        InputProcessingCriteria criteria = queryCaptor.getValue();
        assertEquals(1, criteria.getClientCodes().size());
        assertTrue(criteria.getClientCodes().contains(CLIENT_CODE));
        assertTrue(criteria.getStatuses().contains(InputProcessingStatus.IN_PROGRESS.toString()));
        assertTrue(criteria.getStatuses().contains(InputProcessingStatus.DECISIONS_IN_PROGRESS.toString()));
        assertEquals(3, criteria.getStatuses().size());
        assertEquals(EARLY_DATE, criteria.getProcessingDateRangeEnd());
    }

    private List<InputProcessing> createInputProcessingList() {
        List<InputProcessing> entities = new ArrayList<>();
        InputProcessing entity1 = new InputProcessing();
        entity1.setReceivedDate(LocalDateTime.now());
        entities.add(entity1);
        InputProcessing entity2 = new InputProcessing();
        entity2.setReceivedDate(LocalDateTime.now());
        entities.add(entity2);
        return entities;
    }

    @Test
    public void savingInputProcessingWithStatusNotReceivedShouldSetCompleteTimeToNow() {
        List<InputProcessing> inputProcessings = createInputProcessingList();
        inputProcessings.forEach(inputProcessing -> {
            inputProcessing.setReceivedDate(null);
            inputProcessing.setStatus(InputProcessingStatus.NOT_RECEIVED.toString());
        });
        when(crudService.findByCriteria(any(InputProcessingCriteria.class))).thenReturn(inputProcessings);
        dailyProcessingService.forceCompleteInputProcessingByClient(CLIENT_CODE, EARLY_DATE);
        ArgumentCaptor<List> saveCaptor = ArgumentCaptor.forClass(List.class);
        verify(crudService).save(saveCaptor.capture());
        List<InputProcessing> savedEntities = saveCaptor.getValue();
        savedEntities.forEach(inputProcessing -> {
            assertNotNull(inputProcessing.getCompletedDate());
            assertEquals(InputProcessingStatus.COMPLETED.toString(), inputProcessing.getStatus());
        });
    }

    @Test
    public void startPostBDEIndexRebuild() {
        PropertyDailyProcessing propertyDailyProcessing = new PropertyDailyProcessing();
        propertyDailyProcessing.setClientCode("clientCode");
        propertyDailyProcessing.setPropertyCode("propertyCode");
        when(jobService.startGuaranteedNewInstance(eq(JobName.BDEPostProcessingJob), jobParametersCaptor.capture())).thenReturn(1L);
        dailyProcessingService.startPostBDEIndexRebuild(PROPERTY_ID);
        verify(jobService).startGuaranteedNewInstance(eq(JobName.BDEPostProcessingIndexRebuildJob), jobParametersCaptor.capture());
    }

    @Test
    public void findOldestInProgressInputProcessingSinceSnapshot() {
        LocalDateTime latestCompletedPreparedDate = LocalDateTime.now();
        InputProcessing inputProcessing = new InputProcessing();
        when(dailyProcessingService.findOldestInProgressInputProcessingSinceSnapshot(CLIENT_CODE, PROPERTY_CODE, CORRELATION_ID, latestCompletedPreparedDate)).thenReturn(inputProcessing);
        InputProcessing result = dailyProcessingService.findOldestInProgressInputProcessingSinceSnapshot(CLIENT_CODE, PROPERTY_CODE, CORRELATION_ID, latestCompletedPreparedDate);
        verify(crudService).findByNamedQuerySingleResult(InputProcessing.OLDEST_IN_PROGRESS_SINCE_LATEST_COMPLETED, QueryParameter.with("clientCode", CLIENT_CODE).and("propertyCode", PROPERTY_CODE).and("correlationId", CORRELATION_ID).and("latestCompletedPreparedDate", latestCompletedPreparedDate).parameters());
        assertEquals(inputProcessing, result);
    }

    @Test
    public void shouldNotResetForcefullIfForceFullDecisionToggleIsFalseAndClientNotHilton() {
        setUpMockDataForDecisionDelivered();
        setUpConfigValues(FALSE, false);
        dailyProcessingService.decisionsDelivered(PROPERTY_CODE, CLIENT_CODE, "1", DecisionDestination.RESERVE.toString(), CLIENT_ESA);
        verify(pacmanConfigParamsService).getBooleanParameterValue(FeatureTogglesConfigParamName.ENABLE_FORCE_FULL_DECISIONS.value(), CLIENT_CODE, PROPERTY_CODE);
        verify(propertyService, never()).setForceFullDecisions(anyInt(), eq(Boolean.FALSE));
    }

    @Test
    public void shouldNotResetForcefullIfClientCodeAndPropertyCodeAreNull() {
        setUpMockDataForDecisionDelivered();
        setUpConfigValues(FALSE, false);
        dailyProcessingService.decisionsDelivered(null, null, "1", DecisionDestination.RESERVE.toString(), CLIENT_ESA);
        verify(propertyService, never()).setForceFullDecisions(anyInt(), eq(Boolean.FALSE));
    }

    @Test
    public void shouldNotResetForcefullIfForcefullNotSetAndClientNotHilton() {
        setUpMockDataForDecisionDelivered();
        setUpConfigValues(TRUE, false);
        dailyProcessingService.decisionsDelivered(PROPERTY_CODE, CLIENT_CODE, "1", DecisionDestination.RESERVE.toString(), CLIENT_ESA);
        verify(pacmanConfigParamsService).getBooleanParameterValue(FeatureTogglesConfigParamName.ENABLE_FORCE_FULL_DECISIONS.value(), CLIENT_CODE, PROPERTY_CODE);
        verify(propertyService, never()).setForceFullDecisions(anyInt(), eq(Boolean.FALSE));
    }

    @Test
    public void shouldResetForcefullIfPropertyIsforcefullAndClientNotHilton() {
        setUpMockDataForDecisionDelivered();
        setUpConfigValues(TRUE, true);
        dailyProcessingService.decisionsDelivered(PROPERTY_CODE, CLIENT_CODE, "1", DecisionDestination.RESERVE.toString(), CLIENT_ESA);
        verify(pacmanConfigParamsService).getBooleanParameterValue(FeatureTogglesConfigParamName.ENABLE_FORCE_FULL_DECISIONS.value(), CLIENT_CODE, PROPERTY_CODE);
        verify(propertyService).setForceFullDecisions(anyInt(), eq(Boolean.FALSE));
    }

    @Test
    public void shouldnotResetForcefullIfClientHilton() {
        setUpMockDataForDecisionDelivered();
        setUpConfigValues(TRUE, true);
        dailyProcessingService.decisionsDelivered(PROPERTY_CODE, CLIENT_CODE, "1", DecisionDestination.RESERVE.toString(), CLIENT_HILTON);
        verify(propertyService, never()).setForceFullDecisions(anyInt(), eq(Boolean.FALSE));
    }

    @Test
    public void getLatestBDEByDateShouldReturnLatestBDERecord() {
        LocalDateTime receivedDate = new LocalDateTime();
        List<InputProcessing> inputProcessings = new ArrayList<>();
        inputProcessings.add(getBDEInputProcessInstance(receivedDate.plusMinutes(2), 434));
        inputProcessings.add(getBDEInputProcessInstance(receivedDate, 123));
        inputProcessings.add(getBDEInputProcessInstance(receivedDate.plusHours(2), 234));
        doReturn(inputProcessings).when(crudService).findByNamedQuery(eq(InputProcessing.BY_PROPERTY_DATE_TYPE), anyMap());
        Optional<InputProcessing> latestBDEByDate = dailyProcessingService.getLatestBDEByDate(CLIENT_CODE, PROPERTY_CODE, new LocalDate());
        assertEquals(Optional.of(434).get(), latestBDEByDate.get().getId());
    }

    private InputProcessing getBDEInputProcessInstance(LocalDateTime receivedDate, Integer inputID) {
        InputProcessing inputProcessing = new InputProcessing();
        inputProcessing.setInputType("BDE");
        inputProcessing.setId(inputID);
        inputProcessing.setReceivedDate(receivedDate);
        return inputProcessing;
    }

    @Test
    public void getLatestBDEByDateShouldReturnNULLIfNOBDERecordsAreFound() {
        doReturn(null).when(crudService).findByNamedQuery(eq(InputProcessing.BY_PROPERTY_DATE_TYPE), anyMap());
        assertFalse(dailyProcessingService.getLatestBDEByDate(CLIENT_CODE, PROPERTY_CODE, new LocalDate()).isPresent());
    }

    private List<CdpSchedule> setExpectations(List<ClientPropertyView> properties) {
        when(crudService.findAll(ClientPropertyView.class)).thenReturn(properties);
        // when(multiPropertyCrudService.findByNamedQueryForSingleProperty(1, CdpSchedule.LATEST_CDP_RUN, new HashMap<String, Object>())).thenReturn(new ArrayList<CdpSchedule>());
        ArrayList<CdpSchedule> cdpSchedules = new ArrayList<>();
        cdpSchedules.add(getCdpSchedule(1, LocalTime.now(DateTimeZone.forID(properties.get(0).getTimeZone())).plusHours(2)));
        cdpSchedules.add(getCdpSchedule(2, LocalTime.now(DateTimeZone.forID(properties.get(0).getTimeZone())).plusHours(5)));
        when(multiPropertyCrudService.findByNamedQueryForSingleProperty(1, CdpSchedule.ALL_BY_TIME, new HashMap<>())).thenReturn(cdpSchedules);
        when(pacmanConfigParamsService.getValue("pacman.Client1.Property1", GUIConfigParamName.BDE_ARRIVED_OVERDUE.value())).thenReturn("240");
        when(pacmanConfigParamsService.getValue("pacman.Client1.Property1", GUIConfigParamName.CDP_ARRIVED_OVERDUE.value())).thenReturn("60");
        return cdpSchedules;
    }

    private CdpSchedule getCdpSchedule(Integer id, LocalTime cdpTime) {
        CdpSchedule cdpSchedule = new CdpSchedule();
        cdpSchedule.setId(id);
        cdpSchedule.setCdpTime(cdpTime);
        return cdpSchedule;
    }

    private InputProcessing createInputProcessing(String inputType, String status, LocalDateTime overdueDateTime, Integer cdpScheduleId) {
        InputProcessing inputProcessing = new InputProcessing();
        inputProcessing.setInputType(inputType);
        inputProcessing.setStatus(status);
        inputProcessing.setOverdueDate(overdueDateTime);
        inputProcessing.setCdpScheduleId(cdpScheduleId);
        return inputProcessing;
    }

    private List<PropertyDailyProcessing> getExistingPropertyDailyProcessings() {
        PropertyDailyProcessing pdp1 = createPropertyDailyProcessing(LocalDate.now());
        pdp1.setId(1);
        PropertyDailyProcessing pdp2 = createPropertyDailyProcessing(LocalDate.now().plusDays(1));
        pdp2.setId(2);
        return Arrays.asList(pdp1, pdp2);
    }

    private PropertyDailyProcessing createPropertyDailyProcessing(LocalDate date) {
        PropertyDailyProcessing pdp = new PropertyDailyProcessing();
        pdp.setClientCode("Client1");
        pdp.setPropertyCode("Property1");
        pdp.setProcessingDate(date);
        pdp.setStage(Stage.POPULATION.getCode());
        return pdp;
    }

    private void setUpConfigValues(String enableForceFullDecisions, boolean isPropertyForceFull) {
        when(propertyService.getPropertyId(CLIENT_CODE, PROPERTY_CODE)).thenReturn(PROPERTY_ID);
        when(pacmanConfigParamsService.getBooleanParameterValue(
                FeatureTogglesConfigParamName.ENABLE_FORCE_FULL_DECISIONS.value(), CLIENT_CODE,
                PROPERTY_CODE)).thenReturn(Boolean.valueOf(enableForceFullDecisions));
        when(propertyService.isForceFullDecisions(anyInt())).thenReturn(isPropertyForceFull);
        when(pacmanConfigParamsService.getParameterValue(any(), any(),
                eq(NOTIFY_AWS_POST_PROCESSING_ENABLED))).thenReturn(false);
    }

    private void setUpMockDataForDecisionDelivered() {
        setUpMockDataForDecisionDelivered(DecisionDestination.RESERVE);
    }

    private void setUpMockDataForDecisionDelivered(DecisionDestination destination) {
        InputProcessing inputProcessing = new InputProcessing();
        inputProcessing.setStatus(InputProcessingStatus.IN_PROGRESS.toString());
        inputProcessing.setInputType(InputType.BDE.toString());
        PropertyDailyProcessing pdp = new PropertyDailyProcessing();
        pdp.setExpectedCdpCount(0);
        pdp.setStage(Stage.TWO_WAY.getCode());
        pdp.setPropertyTimeZone(TIME_ZONE);
        inputProcessing.setPropertyDailyProcessing(pdp);
        DecisionDelivery decisionDelivery = new DecisionDelivery();
        decisionDelivery.setInputProcessing(inputProcessing);
        decisionDelivery.setDestinationId(destination.toString());
        decisionDelivery.setResult("COMPLETED");
        decisionDelivery.setUploadedDate(LocalDateTime.now());
        inputProcessing.setDecisionDeliveries(Arrays.asList(decisionDelivery));
        when(crudService.findByCriteria(any(DecisionDeliveryCriteria.class))).thenReturn(Arrays.asList(decisionDelivery));
    }

    private InputProcessingCriteria getInputProcessingDtos(ExternalSubSystem externalSubSystem) {
        List<InputProcessing> entities = new ArrayList<>();
        InputProcessing entity = new InputProcessing();
        PropertyDailyProcessing propertyDailyProcessing = new PropertyDailyProcessing();
        entity.setPropertyDailyProcessing(propertyDailyProcessing);
        entities.add(entity);
        InputProcessingCriteria inputProcessingCriteria = new InputProcessingCriteria();
        when(crudService.findByCriteria(any(InputProcessingCriteria.class))).thenReturn(entities);
        when(externalSystemDetailService.getExternalSystemWithSubTypeAsString(any(),any())).thenReturn("Hilstar / " + externalSubSystem.getConfigParameterValue());
        return inputProcessingCriteria;
    }

    private Property getPropertyWithClient() {
        Client client = new Client();
        client.setCode(CLIENT_CODE);
        Property property = new Property();
        property.setId(5);
        property.setCode(PROPERTY_CODE);
        property.setClient(client);
        return property;
    }

    @Test
    public void testUpdateInputProcessingRecordWithStartTimeWhenInputProcessingIdNotNullAndStartDateNull() {
        InputProcessing inputProcessing = getInputProcessing();
        when(crudService.find(InputProcessing.class, INPUT_PROCESSING_ID)).thenReturn(inputProcessing);
        String result = dailyProcessingService.updateInputProcessingRecordWithStartTime(INPUT_PROCESSING_ID, INPUT_FILE_NAME, PROPERTY_CODE, CLIENT_CODE);
        verify(crudService).find(InputProcessing.class, INPUT_PROCESSING_ID);
        verify(crudService).save(inputProcessing);
        assertThat(result, is("Started_DTTM time is updated for inputProcessingId: " + INPUT_PROCESSING_ID));
    }

    @Test
    public void testUpdateInputProcessingRecordWithStartTimeWhenInputProcessingIdNotNullAndStartDateNotNull() {
        InputProcessing inputProcessing = getInputProcessing();
        inputProcessing.setStartedDate(LocalDateTime.now());
        when(crudService.find(InputProcessing.class, INPUT_PROCESSING_ID)).thenReturn(inputProcessing);
        String result = dailyProcessingService.updateInputProcessingRecordWithStartTime(INPUT_PROCESSING_ID, INPUT_FILE_NAME, PROPERTY_CODE, CLIENT_CODE);
        verify(crudService).find(InputProcessing.class, INPUT_PROCESSING_ID);
        verify(crudService, never()).save(inputProcessing);
        assertThat(result, is("Started_DTTM were already set."));
    }

    @Test
    public void testUpdateInputProcessingRecordWithStartTimeWhenInputProcessingIdNull() {
        InputProcessing inputProcessing = getInputProcessing();
        List<InputProcessing> entities = Collections.singletonList(inputProcessing);
        when(crudService.findByCriteria(any(InputProcessingCriteria.class))).thenReturn(entities);
        String result = dailyProcessingService.updateInputProcessingRecordWithStartTime(null, INPUT_FILE_NAME, PROPERTY_CODE, CLIENT_CODE);
        verify(crudService).findByCriteria(any(InputProcessingCriteria.class));
        verify(crudService).save(inputProcessing);
        assertThat(result, is("Started_DTTM time is updated for inputProcessingId: " + INPUT_PROCESSING_ID));
    }

    @Test
    public void testUpdateInputProcessingWhenStartedDateAlreadySet() {
        InputProcessing inputProcessing = getInputProcessing();
        when(crudService.find(InputProcessing.class, 555)).thenReturn(null);
        String result = dailyProcessingService.updateInputProcessingRecordWithStartTime(555, INPUT_FILE_NAME, PROPERTY_CODE, CLIENT_CODE);
        verify(crudService, never()).save(inputProcessing);
        assertThat(result, is("InputProcessing record not found."));
    }

    private InputProcessing getInputProcessing() {
        JobStepContext jobStepContext = new JobStepContext();
        jobStepContext.setJobInstanceId(123L);
        PlatformThreadLocalContextHolder.setJobStepContext(jobStepContext);
        PropertyDailyProcessing pdp = new PropertyDailyProcessing();
        pdp.setPropertyTimeZone(TIME_ZONE);
        pdp.setProcessingDate(LocalDate.now());
        InputProcessing inputProcessing = new InputProcessing();
        inputProcessing.setId(INPUT_PROCESSING_ID);
        inputProcessing.setPropertyDailyProcessing(pdp);
        return inputProcessing;
    }

    @Test
    public void testDecisionsGeneratedWhenInputProcessingIsNull() {
        InputProcessing inputProcessing = getInputProcessing();
        dailyProcessingService.decisionsGenerated(PROPERTY_CODE, CLIENT_CODE, CORRELATION_ID, INPUT_FILE_NAME, DESTINATION_ID);
        verify(crudService, never()).save(inputProcessing);
    }

    @Test
    public void testDecisionsGeneratedWhenInputProcessingIsCompleted() {
        InputProcessing inputProcessing = getInputProcessing();
        inputProcessing.setStatus(InputProcessingStatus.COMPLETED.toString());
        List<InputProcessing> entities = Collections.singletonList(inputProcessing);
        when(crudService.findByCriteria(any(InputProcessingCriteria.class))).thenReturn(entities);
        dailyProcessingService.decisionsGenerated(PROPERTY_CODE, CLIENT_CODE, CORRELATION_ID, INPUT_FILE_NAME, DESTINATION_ID);
        verify(crudService, never()).save(inputProcessing);
    }

    @Test
    public void testDecisionsGeneratedWhenBDEIsInProgress() {
        InputProcessing inputProcessing = getInputProcessing();
        inputProcessing.setStatus(InputProcessingStatus.IN_PROGRESS.toString());
        inputProcessing.getPropertyDailyProcessing().setStatus(DailyProcessingStatus.BDE_IN_PROGRESS.toString());
        List<InputProcessing> entities = Collections.singletonList(inputProcessing);
        when(crudService.findByCriteria(any(InputProcessingCriteria.class))).thenReturn(entities);
        dailyProcessingService.decisionsGenerated(PROPERTY_CODE, CLIENT_CODE, CORRELATION_ID, INPUT_FILE_NAME, DESTINATION_ID);
        assertThat(inputProcessing.getPropertyDailyProcessing().getStatus(), is(DailyProcessingStatus.BDE_DECISIONS_IN_PROGRESS.toString()));
        verify(crudService).save(inputProcessing);
        verify(crudService).save(inputProcessing.getPropertyDailyProcessing());
    }

    @Test
    public void testDecisionsGeneratedWhenCDPIsInProgress() {
        InputProcessing inputProcessing = getInputProcessing();
        inputProcessing.setStatus(InputProcessingStatus.IN_PROGRESS.toString());
        inputProcessing.getPropertyDailyProcessing().setStatus(DailyProcessingStatus.CDP_IN_PROGRESS.toString());
        List<InputProcessing> entities = Collections.singletonList(inputProcessing);
        when(crudService.findByCriteria(any(InputProcessingCriteria.class))).thenReturn(entities);
        dailyProcessingService.decisionsGenerated(PROPERTY_CODE, CLIENT_CODE, CORRELATION_ID, INPUT_FILE_NAME, DESTINATION_ID);
        assertThat(inputProcessing.getPropertyDailyProcessing().getStatus(), is(DailyProcessingStatus.CDP_DECISIONS_IN_PROGRESS.toString()));
        verify(crudService).save(inputProcessing);
        verify(crudService).save(inputProcessing.getPropertyDailyProcessing());
    }

    @Test
    public void testOperaDecisionsGeneratedWhenInputProcessingIsNull() {
        InputProcessing inputProcessing = getInputProcessing();
        dailyProcessingService.operaDecisionsGenerated(INPUT_PROCESSING_ID, DESTINATION_ID);
        verify(crudService, never()).save(inputProcessing);
    }

    @Test
    public void testDecisionsGeneratedForDecisionIdDestination_UsingDecisionIdForLookup() {
        InputProcessing inputProcessing = getInputProcessing();
        inputProcessing.setStatus(InputProcessingStatus.IN_PROGRESS.toString());
        inputProcessing.getPropertyDailyProcessing().setStatus(DailyProcessingStatus.BDE_IN_PROGRESS.toString());
        when(crudService.find(InputProcessing.class, INPUT_PROCESSING_ID)).thenReturn(inputProcessing);
        inputProcessing.addDecisionDelivery(getDecisionDelivery(DECISION_ID_DEFAULT));
        dailyProcessingService.decisionsGeneratedForDecisionIdDestination(INPUT_PROCESSING_ID, DESTINATION_ID, inp -> DECISION_ID);
        ArgumentCaptor<IdAware> savedEntities = ArgumentCaptor.forClass(IdAware.class);
        verify(crudService, atLeastOnce()).save(savedEntities.capture());
        DecisionDelivery newDecisionDelivery = savedEntities.getAllValues().stream().filter(ent -> ent instanceof DecisionDelivery).map(ent -> (DecisionDelivery) ent).findFirst().orElse(null);
        assertEquals(inputProcessing, newDecisionDelivery.getInputProcessing());
        assertEquals(DESTINATION_ID, newDecisionDelivery.getDestinationId());
        assertEquals(DECISION_ID, newDecisionDelivery.getDecisionId());
    }

    @Test
    public void testOperaDecisionsGeneratedWhenBDEIsInProgress() {
        InputProcessing inputProcessing = getInputProcessing();
        inputProcessing.getPropertyDailyProcessing().setStatus(DailyProcessingStatus.BDE_IN_PROGRESS.toString());
        when(crudService.find(InputProcessing.class, INPUT_PROCESSING_ID)).thenReturn(inputProcessing);
        dailyProcessingService.operaDecisionsGenerated(INPUT_PROCESSING_ID, DESTINATION_ID);
        assertThat(inputProcessing.getPropertyDailyProcessing().getStatus(), is(DailyProcessingStatus.BDE_DECISIONS_IN_PROGRESS.toString()));
        verify(crudService).save(inputProcessing.getPropertyDailyProcessing());
    }

    @Test
    public void testOperaDecisionsGeneratedWhenCDPIsInProgress() {
        InputProcessing inputProcessing = getInputProcessing();
        inputProcessing.getPropertyDailyProcessing().setStatus(DailyProcessingStatus.CDP_IN_PROGRESS.toString());
        when(crudService.find(InputProcessing.class, INPUT_PROCESSING_ID)).thenReturn(inputProcessing);
        dailyProcessingService.operaDecisionsGenerated(INPUT_PROCESSING_ID, DESTINATION_ID);
        assertThat(inputProcessing.getPropertyDailyProcessing().getStatus(), is(DailyProcessingStatus.CDP_DECISIONS_IN_PROGRESS.toString()));
        verify(crudService).save(inputProcessing.getPropertyDailyProcessing());
    }

    @Test
    public void testOperaDecisionsGeneratedWithDecisionsByType() {
        InputProcessing inputProcessing = getInputProcessing();
        inputProcessing.setInputId("theInputId");
        inputProcessing.getPropertyDailyProcessing().setStatus(DailyProcessingStatus.BDE_IN_PROGRESS.toString());
        when(crudService.find(InputProcessing.class, INPUT_PROCESSING_ID)).thenReturn(inputProcessing);
        dailyProcessingService.operaDecisionsGenerated(INPUT_PROCESSING_ID, DESTINATION_ID, Arrays.asList("type1", "type2"));
        assertThat(inputProcessing.getPropertyDailyProcessing().getStatus(), is(DailyProcessingStatus.BDE_DECISIONS_IN_PROGRESS.toString()));
        assertNotNull(inputProcessing.getDecisionsGeneratedDate());
        assertThat(inputProcessing.getDecisionDeliveries().size(), is(1));
        DecisionDelivery decisionDelivery = inputProcessing.getDecisionDeliveries().get(0);
        assertThat(decisionDelivery.getInputProcessing(), is(inputProcessing));
        assertThat(decisionDelivery.getDecisionId(), is(inputProcessing.getInputId()));
        assertThat(decisionDelivery.getDestinationId(), is(DESTINATION_ID));
        assertThat(decisionDelivery.getResult(), is("IN_PROGRESS"));
        assertNull(decisionDelivery.getStartedDate());
        assertNull(decisionDelivery.getUploadedDate());
        assertNull(decisionDelivery.getAckReceivedDate());
        assertThat(decisionDelivery.getDecisionDeliveryByTypes().size(), is(2));
        DecisionDeliveryByType type1 = decisionDelivery.getDecisionDeliveryByType("type1");
        assertNotNull(type1);
        assertThat(type1.getDecisionType(), is("type1"));
        assertThat(type1.getDecisionDelivery(), is(decisionDelivery));
        assertThat(type1.getStatus(), is(DecisionDeliveryByTypeStatus.IN_PROGRESS));
        assertNull(type1.getStartedDateTime());
        assertNull(type1.getDeliveredDateTime());
        assertNull(type1.getAcknowledgedDateTime());
        verify(crudService).save(inputProcessing.getPropertyDailyProcessing());
    }

    @Test
    public void updateAckReceivedTimeForDecisionDelivery() {
        String decisionFileName = "test.recom";
        DecisionDelivery decisionDelivery = new DecisionDelivery();
        InputProcessing inputProcessing = new InputProcessing();
        PropertyDailyProcessing pdp = new PropertyDailyProcessing();
        pdp.setPropertyTimeZone(TIME_ZONE);
        inputProcessing.setPropertyDailyProcessing(pdp);
        decisionDelivery.setInputProcessing(inputProcessing);
        decisionDelivery.setDecisionId("test.recom.Z");
        when(crudService.findByNamedQuerySingleResult(DecisionDelivery.GET_BY_DECISION_ID, QueryParameter.with("decisionId", decisionFileName + ".Z").parameters())).thenReturn(decisionDelivery);
        dailyProcessingService.updateAckReceivedTimeForDecisionDelivery(decisionFileName);
        verify(crudService).save(decisionDelivery);
    }

    @Test
    public void shouldNotUpdateAckReceivedTimeForDecisionDelivery() {
        String decisionFileName = "test.recom";
        when(crudService.findByNamedQuerySingleResult(DecisionDelivery.GET_BY_DECISION_ID, QueryParameter.with("decisionId", decisionFileName + ".Z").parameters())).thenReturn(null);
        dailyProcessingService.updateAckReceivedTimeForDecisionDelivery(decisionFileName);
        verify(crudService, never()).save(any(DecisionDelivery.class));
    }

    @Test
    public void updateAckReceivedTime() {
        DecisionDelivery decisionDelivery = new DecisionDelivery();
        decisionDelivery.setDestinationId(TARS_DESTINATION);
        decisionDelivery.setResult("COMPLETED");
        InputProcessing inputProcessing = new InputProcessing();
        PropertyDailyProcessing pdp = new PropertyDailyProcessing();
        pdp.setPropertyTimeZone(TIME_ZONE);
        inputProcessing.setPropertyDailyProcessing(pdp);
        inputProcessing.setDecisionDeliveries(Arrays.asList(decisionDelivery));
        int inputProcessingId = 1;
        when(crudService.find(InputProcessing.class, inputProcessingId)).thenReturn(inputProcessing);
        assertThat("Decision Delivery acknowledgement received time updated", is(dailyProcessingService.updateAckReceivedTime(inputProcessingId, TARS_DESTINATION)));
        verify(crudService).save(decisionDelivery);
    }

    @Test
    public void failToUpdateAckReceivedTimeAsDecisionDeliveryRecordNotPresent() {
        DecisionDelivery decisionDelivery = new DecisionDelivery();
        InputProcessing inputProcessing = new InputProcessing();
        PropertyDailyProcessing pdp = new PropertyDailyProcessing();
        pdp.setPropertyTimeZone(TIME_ZONE);
        inputProcessing.setPropertyDailyProcessing(pdp);
        inputProcessing.setDecisionDeliveries(Arrays.asList(decisionDelivery));
        int inputProcessingId = 1;
        when(crudService.find(InputProcessing.class, inputProcessingId)).thenReturn(inputProcessing);
        assertThat("no Decision_Delivery record found for " + inputProcessingId + " /  " + TARS_DESTINATION, is(dailyProcessingService.updateAckReceivedTime(inputProcessingId, TARS_DESTINATION)));
        verify(crudService, never()).save(decisionDelivery);
    }

    @Test
    public void failToUpdateAckReceivedTimeAsInputReceivedRecordNotPresent() {
        DecisionDelivery decisionDelivery = new DecisionDelivery();
        int inputProcessingId = 1;
        when(crudService.find(InputProcessing.class, inputProcessingId)).thenReturn(null);
        assertThat("no inputProcessing record not found for inputProcessingId: " + inputProcessingId, is(dailyProcessingService.updateAckReceivedTime(inputProcessingId, TARS_DESTINATION)));
        verify(crudService, never()).save(decisionDelivery);
    }

    @Test
    public void updateAckReceivedTime_withDeliveryByTypes_allAcknowledged() {
        DecisionDelivery decisionDelivery = createDecisionDelivery("COMPLETED", ACKNOWLEDGED, ACKNOWLEDGED);
        assertThat("Decision Delivery acknowledgement received time updated", is(dailyProcessingService.updateAckReceivedTime(INPUT_PROCESSING_ID, DESTINATION_ID)));
        verify(crudService).save(decisionDelivery);
        assertThat(decisionDelivery.getAckReceivedDate(), is(notNullValue()));
    }

    @Test
    public void updateAckReceivedTime_withDeliveryByTypes_notAllAcknowledged() {
        DecisionDelivery decisionDelivery = createDecisionDelivery("COMPLETED", DELIVERED, ACKNOWLEDGED);
        assertThat(dailyProcessingService.updateAckReceivedTime(INPUT_PROCESSING_ID, DESTINATION_ID), is("Not all decision deliveries by type are acknowledged"));
        verify(crudService, never()).save(decisionDelivery);
    }

    private DecisionDelivery createDecisionDelivery(String decisionDeliveryStatus, DecisionDeliveryByTypeStatus status1, DecisionDeliveryByTypeStatus status2) {
        DecisionDelivery decisionDelivery = new DecisionDelivery();
        decisionDelivery.setDestinationId(DESTINATION_ID);
        decisionDelivery.setResult(decisionDeliveryStatus);
        decisionDelivery.addDecisionDeliveryByType(createDecisionDeliveryByType(DECISION_TYPE_1, status1));
        decisionDelivery.addDecisionDeliveryByType(createDecisionDeliveryByType(DECISION_TYPE_2, status2));
        InputProcessing inputProcessing = new InputProcessing();
        inputProcessing.setId(INPUT_PROCESSING_ID);
        inputProcessing.addDecisionDelivery(decisionDelivery);
        PropertyDailyProcessing pdp = new PropertyDailyProcessing();
        pdp.setPropertyTimeZone(TIME_ZONE);
        pdp.addInputProcessing(inputProcessing);
        when(crudService.find(InputProcessing.class, INPUT_PROCESSING_ID)).thenReturn(inputProcessing);
        return decisionDelivery;
    }

    private DecisionDeliveryByType createDecisionDeliveryByType(String decisionType, DecisionDeliveryByTypeStatus status) {
        DecisionDeliveryByType decisionDeliveryByType = new DecisionDeliveryByType();
        decisionDeliveryByType.setDecisionType(decisionType);
        decisionDeliveryByType.setStatus(status);
        return decisionDeliveryByType;
    }

    @Test
    public void updateAckReceivedTime_forDecisionType() {
        DecisionDelivery decisionDelivery = createDecisionDelivery("COMPLETED", DELIVERED, DELIVERED);
        DecisionDeliveryByType decisionDeliveryByType1 = decisionDelivery.getDecisionDeliveryByType(DECISION_TYPE_1);
        DecisionDeliveryByType decisionDeliveryByType2 = decisionDelivery.getDecisionDeliveryByType(DECISION_TYPE_2);
        dailyProcessingService.updateAckReceivedTime(INPUT_PROCESSING_ID, DESTINATION_ID, DECISION_TYPE_1);
        verify(crudService).save(decisionDeliveryByType1);
        assertThat(decisionDeliveryByType1.getAcknowledgedDateTime(), is(notNullValue()));
        assertThat(decisionDeliveryByType1.getStatus(), is(ACKNOWLEDGED));
        assertNull(decisionDeliveryByType2.getAcknowledgedDateTime());
        assertThat(decisionDeliveryByType2.getStatus(), is(DELIVERED));
        assertNull(decisionDelivery.getAckReceivedDate());
    }

    @Test
    public void updateAckReceivedTime_forDecisionType_inputNotFound() {
        InputProcessing inputProcessing = new InputProcessing();
        inputProcessing.setId(INPUT_PROCESSING_ID);
        when(crudService.find(InputProcessing.class, INPUT_PROCESSING_ID)).thenReturn(null);
        dailyProcessingService.updateAckReceivedTime(INPUT_PROCESSING_ID, DESTINATION_ID, DECISION_TYPE_1);
        verify(crudService, never()).save(any(DecisionDeliveryByType.class));
    }

    @Test
    public void updateAckReceivedTime_forDecisionType_decisionDeliveryNotCompleted() {
        createDecisionDelivery("IN_PROGRESS", DELIVERED, DELIVERED);
        dailyProcessingService.updateAckReceivedTime(INPUT_PROCESSING_ID, DESTINATION_ID, DECISION_TYPE_1);
        verify(crudService, never()).save(any(DecisionDeliveryByType.class));
    }

    @Test
    public void updateAckReceivedTime_forDecisionType_decisionDeliveryByTypeNotDelivered() {
        createDecisionDelivery("COMPLETED", DELIVERED, IN_PROGRESS);
        dailyProcessingService.updateAckReceivedTime(INPUT_PROCESSING_ID, DESTINATION_ID, DECISION_TYPE_2);
        verify(crudService, never()).save(any(DecisionDeliveryByType.class));
    }

    @Test
    public void testUpdateDecisionDeliveryUploadStartedDateWhenInputProcessingNotFound() {
        DecisionDelivery decisionDelivery = new DecisionDelivery();
        dailyProcessingService.updateDecisionDeliveryUploadStartedDate(INPUT_PROCESSING_ID, DESTINATION_ID);
        verify(crudService).find(InputProcessing.class, INPUT_PROCESSING_ID);
        verify(crudService, never()).save(decisionDelivery);
    }

    @Test
    public void testUpdateDecisionDeliveryUploadStartedDateWhenInprogressDecisionDeliveryNotFound() {
        DecisionDelivery decisionDelivery = new DecisionDelivery();
        decisionDelivery.setResult("COMPLETED");
        InputProcessing inputProcessing = getInputProcessing();
        inputProcessing.setDecisionDeliveries(Collections.singletonList(decisionDelivery));
        when(crudService.find(InputProcessing.class, INPUT_PROCESSING_ID)).thenReturn(inputProcessing);
        dailyProcessingService.updateDecisionDeliveryUploadStartedDate(INPUT_PROCESSING_ID, DESTINATION_ID);
        verify(crudService).find(InputProcessing.class, INPUT_PROCESSING_ID);
        verify(crudService, never()).save(decisionDelivery);
    }

    @Test
    public void testUpdateDecisionDeliveryUploadStartedDateWhenValidRecordPresentOG() {
        DecisionDelivery decisionDelivery = new DecisionDelivery();
        decisionDelivery.setResult("IN_PROGRESS");
        decisionDelivery.setDestinationId(DESTINATION_ID);
        InputProcessing inputProcessing = getInputProcessing();
        inputProcessing.setDecisionDeliveries(Collections.singletonList(decisionDelivery));
        when(crudService.find(InputProcessing.class, INPUT_PROCESSING_ID)).thenReturn(inputProcessing);
        dailyProcessingService.updateDecisionDeliveryUploadStartedDate(INPUT_PROCESSING_ID, DESTINATION_ID);
        verify(crudService).find(InputProcessing.class, INPUT_PROCESSING_ID);
        verify(crudService).save(decisionDelivery);
        assertThat(decisionDelivery.getStartedDate(), is(notNullValue()));
    }

    @Test
    public void testUpdateDecisionDeliveryUploadStartedDate_withDecisionType() {
        DecisionDelivery decisionDelivery = createDecisionDelivery("IN_PROGRESS", IN_PROGRESS, IN_PROGRESS);
        DecisionDeliveryByType decisionDeliveryByType1 = decisionDelivery.getDecisionDeliveryByType(DECISION_TYPE_1);
        DecisionDeliveryByType decisionDeliveryByType2 = decisionDelivery.getDecisionDeliveryByType(DECISION_TYPE_2);
        dailyProcessingService.updateDecisionDeliveryUploadStartedDate(INPUT_PROCESSING_ID, DESTINATION_ID, DECISION_TYPE_2);
        verify(crudService).save(decisionDelivery);
        assertThat(decisionDelivery.getStartedDate(), is(notNullValue()));
        assertThat(decisionDeliveryByType2.getStartedDateTime(), is(notNullValue()));
        assertNull(decisionDeliveryByType1.getStartedDateTime());
    }

    @Test
    public void testUpdateDecisionDeliveryUploadStartedDate_decisionTypeNotFound() {
        DecisionDelivery decisionDelivery = createDecisionDelivery("IN_PROGRESS", IN_PROGRESS, IN_PROGRESS);
        DecisionDeliveryByType decisionDeliveryByType1 = decisionDelivery.getDecisionDeliveryByType(DECISION_TYPE_1);
        DecisionDeliveryByType decisionDeliveryByType2 = decisionDelivery.getDecisionDeliveryByType(DECISION_TYPE_2);
        dailyProcessingService.updateDecisionDeliveryUploadStartedDate(INPUT_PROCESSING_ID, DESTINATION_ID, "aDifferentDecisionType");
        verify(crudService).save(decisionDelivery);
        assertThat(decisionDelivery.getStartedDate(), is(notNullValue()));
        assertNull(decisionDeliveryByType1.getStartedDateTime());
        assertNull(decisionDeliveryByType2.getStartedDateTime());
    }

    @Test
    public void testUpdateDecisionDeliveryUploadStartedDate_decisionTypeNotInProgress() {
        DecisionDelivery decisionDelivery = createDecisionDelivery("IN_PROGRESS", DELIVERED, IN_PROGRESS);
        DecisionDeliveryByType decisionDeliveryByType1 = decisionDelivery.getDecisionDeliveryByType(DECISION_TYPE_1);
        DecisionDeliveryByType decisionDeliveryByType2 = decisionDelivery.getDecisionDeliveryByType(DECISION_TYPE_2);
        dailyProcessingService.updateDecisionDeliveryUploadStartedDate(INPUT_PROCESSING_ID, DESTINATION_ID, DECISION_TYPE_1);
        assertThat(decisionDelivery.getStartedDate(), is(notNullValue()));
        assertNull(decisionDeliveryByType1.getStartedDateTime());
        assertNull(decisionDeliveryByType2.getStartedDateTime());
    }

    @Test
    public void testUpdateDecisionDeliveryUploadStartedDate_alreadySet() {
        DecisionDelivery decisionDelivery = new DecisionDelivery();
        decisionDelivery.setResult("IN_PROGRESS");
        decisionDelivery.setDestinationId(DESTINATION_ID);
        LocalDateTime startedDateTime = LocalDateTime.now().minusMinutes(5);
        decisionDelivery.setStartedDate(startedDateTime);
        InputProcessing inputProcessing = getInputProcessing();
        inputProcessing.addDecisionDelivery(decisionDelivery);
        when(crudService.find(InputProcessing.class, INPUT_PROCESSING_ID)).thenReturn(inputProcessing);
        dailyProcessingService.updateDecisionDeliveryUploadStartedDate(INPUT_PROCESSING_ID, DESTINATION_ID);
        verify(crudService, never()).save(decisionDelivery);
        assertThat(decisionDelivery.getStartedDate(), is(startedDateTime));
    }

    @Test
    public void testUpdateDecisionDeliveryUploadStartedDate_alreadySet_withDecisionType() {
        DecisionDelivery decisionDelivery = createDecisionDelivery("IN_PROGRESS", IN_PROGRESS, IN_PROGRESS);
        LocalDateTime startedDateTime = LocalDateTime.now().minusMinutes(5);
        decisionDelivery.setStartedDate(startedDateTime);
        DecisionDeliveryByType decisionDeliveryByType1 = decisionDelivery.getDecisionDeliveryByType(DECISION_TYPE_1);
        DecisionDeliveryByType decisionDeliveryByType2 = decisionDelivery.getDecisionDeliveryByType(DECISION_TYPE_2);
        dailyProcessingService.updateDecisionDeliveryUploadStartedDate(INPUT_PROCESSING_ID, DESTINATION_ID, DECISION_TYPE_1);
        verify(crudService).save(decisionDelivery);
        assertThat(decisionDelivery.getStartedDate(), is(startedDateTime));
        assertThat(decisionDeliveryByType1.getStartedDateTime(), is(notNullValue()));
        assertNull(decisionDeliveryByType2.getStartedDateTime());
    }

    @Test
    public void testUpdateDecisionDeliveryUploadStartedDate_withDecisionId() {
        InputProcessing inputProcessing = getInputProcessing();
        DecisionDelivery relevantDecisionDelivery = getDecisionDelivery(DECISION_ID);
        inputProcessing.addDecisionDelivery(relevantDecisionDelivery);
        inputProcessing.addDecisionDelivery(getDecisionDelivery(DECISION_ID_DEFAULT));
        when(crudService.find(InputProcessing.class, INPUT_PROCESSING_ID)).thenReturn(inputProcessing);
        dailyProcessingService.updateDecisionDeliveryUploadStartedDate(INPUT_PROCESSING_ID, DESTINATION_ID, null, inp -> DECISION_ID);
        verify(crudService).find(InputProcessing.class, INPUT_PROCESSING_ID);
        verify(crudService).save(relevantDecisionDelivery);
        assertThat(relevantDecisionDelivery.getStartedDate(), is(notNullValue()));
    }

    @Test
    public void createRequiredAsNothingIsModified() {
        String clientCode = "Client1";
        String propertyCode = "Property1";
        List<ClientPropertyView> properties = Arrays.asList(createClientPropertyView(1, clientCode, propertyCode, TIME_ZONE));
        Property property = new Property();
        property.setClient(new Client());
        property.getClient().setCode(clientCode);
        property.setCode(propertyCode);
        property.setStage(properties.get(0).getStage());
        when(propertyService.getPropertyById(1)).thenReturn(property);
        when(pacmanConfigParamsService.getParameterValue(clientCode, propertyCode, IntegrationConfigParamName.CORE_PROPERTY_TIME_ZONE)).thenReturn(properties.get(0).getTimeZone());

        List<CdpSchedule> cdpSchedules = setExpectations(properties);
        List<PropertyDailyProcessing> pdp = new ArrayList<>();
        Map<LocalDate, Set<String>> pdpMap = dailyProcessingService.buildPDPMap(pdp);
        TimeZone propertyTimeZone = TimeZone.getTimeZone(properties.get(0).getTimeZone());
        LocalDate today = new LocalDate(DateTimeZone.forTimeZone(propertyTimeZone));
        LocalDate tomorrow = today.plusDays(1);
        List<String> response = new ArrayList<>();
        dailyProcessingService.createOrModifyDailyProcessingRecordsForOneDay(properties.get(0), pdp, response, dailyProcessingService.buildPseudoPropertyId(clientCode, propertyCode), today, pdpMap.get(today));
        dailyProcessingService.createOrModifyDailyProcessingRecordsForOneDay(properties.get(0), pdp, response, dailyProcessingService.buildPseudoPropertyId(clientCode, propertyCode), tomorrow, pdpMap.get(tomorrow));
        assertEquals(2, response.size());
    }

    @Test
    public void updateInputProcessingRecordWithSummaryStartedAndCompletedTimes() {
        InputProcessing inputProcessing = getInputProcessing();
        PropertyDailyProcessing pdp = new PropertyDailyProcessing();
        pdp.setPropertyTimeZone(TIME_ZONE);
        inputProcessing.setPropertyDailyProcessing(pdp);
        when(crudService.find(InputProcessing.class, INPUT_PROCESSING_ID)).thenReturn(inputProcessing);
        String result = dailyProcessingService.updateInputProcessingRecordWithSummaryStartedAndCompletedTimes(INPUT_PROCESSING_ID, INPUT_FILE_NAME, PROPERTY_CODE, CLIENT_CODE, new Date(), new Date());
        verify(crudService).find(InputProcessing.class, INPUT_PROCESSING_ID);
        verify(crudService).save(inputProcessing);
        assertThat(result, is("Summary_Started_DTTM and Summary_Completed_DTTM is updated for inputProcessingId: " + inputProcessing.getInputProcessingId()));
    }

    @Test
    public void updateInputProcessingRecordWithSummaryStartedAndCompletedTimesWhenInputProcessingIDIsNull() {
        InputProcessing inputProcessing = getInputProcessing();
        PropertyDailyProcessing pdp = new PropertyDailyProcessing();
        pdp.setPropertyTimeZone(TIME_ZONE);
        pdp.setProcessingDate(LocalDate.now());
        inputProcessing.setPropertyDailyProcessing(pdp);
        when(crudService.findByCriteria(any(InputProcessingCriteria.class))).thenReturn(Arrays.asList(inputProcessing));
        String result = dailyProcessingService.updateInputProcessingRecordWithSummaryStartedAndCompletedTimes(null, INPUT_FILE_NAME, PROPERTY_CODE, CLIENT_CODE, new Date(), new Date());
        verify(crudService).findByCriteria(any(InputProcessingCriteria.class));
        verify(crudService).save(inputProcessing);
        assertThat(result, is("Summary_Started_DTTM and Summary_Completed_DTTM is updated for inputProcessingId: " + inputProcessing.getInputProcessingId()));
    }

    @Test
    public void doNotUpdateInputProcessingRecordWithSummaryStartedAndCompletedTimesWhenInputProcessingIsNull() {
        InputProcessing inputProcessing = getInputProcessing();
        PropertyDailyProcessing pdp = new PropertyDailyProcessing();
        pdp.setPropertyTimeZone(TIME_ZONE);
        inputProcessing.setPropertyDailyProcessing(pdp);
        when(crudService.findByCriteria(any(InputProcessingCriteria.class))).thenReturn(new ArrayList<>());
        String result = dailyProcessingService.updateInputProcessingRecordWithSummaryStartedAndCompletedTimes(null, INPUT_FILE_NAME, PROPERTY_CODE, CLIENT_CODE, new Date(), new Date());
        verify(crudService).findByCriteria(any(InputProcessingCriteria.class));
        verify(crudService, never()).save(inputProcessing);
        assertThat(result, is("InputProcessing record not found"));
    }

    @Test
    public void doNotUpdateInputProcessingRecordWithSummaryStartedAndCompletedTimesWhenTimingsNotAvailable() {
        InputProcessing inputProcessing = getInputProcessing();
        PropertyDailyProcessing pdp = new PropertyDailyProcessing();
        pdp.setPropertyTimeZone(TIME_ZONE);
        inputProcessing.setPropertyDailyProcessing(pdp);
        when(crudService.findByCriteria(any(InputProcessingCriteria.class))).thenReturn(Arrays.asList(inputProcessing));
        String result = dailyProcessingService.updateInputProcessingRecordWithSummaryStartedAndCompletedTimes(null, INPUT_FILE_NAME, PROPERTY_CODE, CLIENT_CODE, null, null);
        verify(crudService, never()).save(inputProcessing);
        assertThat(result, is("Summary_Started_DTTM and Summary_Completed_DTTM not available"));
    }

    @Test
    public void shouldResetForcefullIfPropertyIsforcefullAndClientNotHilton_With_OXI_System() {
        setUpMockDataForDecisionDelivered(DecisionDestination.OPERA);
        when(pacmanConfigParamsService.getParameterValue(PreProductionConfigParamName.FORCEFULLDECISIONS_OUTBOUND_ENABLED)).thenReturn(true);
        PacmanWorkContextHelper.getWorkContext().setClientId(23);
        PacmanWorkContextHelper.getWorkContext().setExternalSystem(ExternalSystem.OXI);
        doNothing().when(forceFullDecisionsService)
                .deleteOutBoundEntryByClientIdAndPropertyIdAndOutboundName(CLIENT_ID, PROPERTY_ID,
                        ExternalSystem.OPERA_AGENT.getCode(),
                        ForceFullDecisionsUpdateReasons.FFD_UPLOADED.getReasonID());
        when(pacmanConfigParamsService.getParameterValue(nullable(String.class), nullable(String.class),
                eq(NOTIFY_AWS_POST_PROCESSING_ENABLED))).thenReturn(false);
        dailyProcessingService.decisionsDelivered(PROPERTY_CODE, CLIENT_CODE, "1", DecisionDestination.OPERA.toString(),
                CLIENT_ESA);
        verify(forceFullDecisionsService).deleteOutBoundEntryByClientIdAndPropertyIdAndOutboundName(CLIENT_ID, PROPERTY_ID, ExternalSystem.OPERA_AGENT.getCode(), ForceFullDecisionsUpdateReasons.FFD_UPLOADED.getReasonID());
    }

    @Test
    public void shouldResetForcefullIfPropertyIsforcefullAndClientNotHilton_With_Other_Than_OXI_System() {
        setUpMockDataForDecisionDelivered(DecisionDestination.TARS);
        when(pacmanConfigParamsService.getParameterValue(
                PreProductionConfigParamName.FORCEFULLDECISIONS_OUTBOUND_ENABLED)).thenReturn(true);
        when(pacmanConfigParamsService.getParameterValue(nullable(String.class), nullable(String.class),
                eq(NOTIFY_AWS_POST_PROCESSING_ENABLED))).thenReturn(false);
        PacmanWorkContextHelper.getWorkContext().setClientId(23);
        PacmanWorkContextHelper.getWorkContext().setExternalSystem(ExternalSystem.TARS);
        doNothing().when(forceFullDecisionsService)
                .deleteOutBoundEntryByClientIdAndPropertyIdAndOutboundName(CLIENT_ID, PROPERTY_ID,
                        ExternalSystem.TARS.getCode(), ForceFullDecisionsUpdateReasons.FFD_UPLOADED.getReasonID());
        dailyProcessingService.decisionsDelivered(PROPERTY_CODE, CLIENT_CODE, "1", DecisionDestination.TARS.toString(),
                CLIENT_ESA);
        verify(forceFullDecisionsService).deleteOutBoundEntryByClientIdAndPropertyIdAndOutboundName(CLIENT_ID,
                PROPERTY_ID, ExternalSystem.TARS.getCode(), ForceFullDecisionsUpdateReasons.FFD_UPLOADED.getReasonID());
    }

    @Test
    public void test_completeDestination() {
        DecisionDelivery decisionDelivery = new DecisionDelivery();
        decisionDelivery.setResult("IN_PROGRESS");
        decisionDelivery.setDestinationId(DESTINATION_ID);
        InputProcessing inputProcessing = new InputProcessing();
        PropertyDailyProcessing pdp = new PropertyDailyProcessing();
        pdp.setPropertyTimeZone(TIME_ZONE);
        inputProcessing.setPropertyDailyProcessing(pdp);
        inputProcessing.addDecisionDelivery(decisionDelivery);
        when(crudService.find(InputProcessing.class, INPUT_PROCESSING_ID)).thenReturn(inputProcessing);
        dailyProcessingService.completeDestination(INPUT_PROCESSING_ID, DESTINATION_ID);
        verify(crudService).find(InputProcessing.class, INPUT_PROCESSING_ID);
        ArgumentCaptor<DecisionDelivery> saveCaptor = ArgumentCaptor.forClass(DecisionDelivery.class);
        verify(crudService).save(saveCaptor.capture());
        DecisionDelivery entity = saveCaptor.getValue();
        assertEquals(entity.getResult(), "COMPLETED");
        assertNotNull(entity.getUploadedDate());
    }

    @Test
    public void test_completeDestinationByDecisionId() {
        InputProcessing inputProcessing = new InputProcessing();
        PropertyDailyProcessing pdp = new PropertyDailyProcessing();
        pdp.setPropertyTimeZone(TIME_ZONE);
        inputProcessing.setPropertyDailyProcessing(pdp);
        when(crudService.find(InputProcessing.class, INPUT_PROCESSING_ID)).thenReturn(inputProcessing);
        DecisionDelivery decisionDelivery = getDecisionDelivery(DECISION_ID);
        inputProcessing.addDecisionDelivery(decisionDelivery);
        inputProcessing.addDecisionDelivery(getDecisionDelivery(DECISION_ID_DEFAULT));
        dailyProcessingService.completeDestinationByDecisionId(INPUT_PROCESSING_ID, DESTINATION_ID, inp -> DECISION_ID);
        verify(crudService).find(InputProcessing.class, INPUT_PROCESSING_ID);
        ArgumentCaptor<DecisionDelivery> saveCaptor = ArgumentCaptor.forClass(DecisionDelivery.class);
        verify(crudService).save(saveCaptor.capture());
        DecisionDelivery entity = saveCaptor.getValue();
        assertEquals(entity.getResult(), "COMPLETED");
        assertNotNull(entity.getUploadedDate());
    }

    private DecisionDelivery getDecisionDelivery(String decisionId) {
        DecisionDelivery decisionDelivery = new DecisionDelivery();
        decisionDelivery.setResult("IN_PROGRESS");
        decisionDelivery.setDestinationId(DESTINATION_ID);
        decisionDelivery.setDecisionId(decisionId);
        return decisionDelivery;
    }

    @Test
    public void test_completeDestinationByType() {
        PropertyDailyProcessing pdp = new PropertyDailyProcessing();
        pdp.setPropertyTimeZone(TIME_ZONE);
        InputProcessing inputProcessing = new InputProcessing();
        pdp.addInputProcessing(inputProcessing);
        DecisionDelivery decisionDelivery = new DecisionDelivery();
        decisionDelivery.setResult("IN_PROGRESS");
        decisionDelivery.setDestinationId(DESTINATION_ID);
        inputProcessing.addDecisionDelivery(decisionDelivery);
        DecisionDeliveryByType decisionDeliveryByType1 = new DecisionDeliveryByType();
        decisionDeliveryByType1.setStatus(DELIVERED);
        DecisionDeliveryByType decisionDeliveryByType2 = new DecisionDeliveryByType();
        decisionDeliveryByType2.setStatus(DELIVERED);
        decisionDelivery.addDecisionDeliveryByType(decisionDeliveryByType1);
        decisionDelivery.addDecisionDeliveryByType(decisionDeliveryByType2);
        when(crudService.find(InputProcessing.class, INPUT_PROCESSING_ID)).thenReturn(inputProcessing);
        dailyProcessingService.completeDestinationByDecisionType(INPUT_PROCESSING_ID, DESTINATION_ID);
        ArgumentCaptor<DecisionDelivery> saveCaptor = ArgumentCaptor.forClass(DecisionDelivery.class);
        verify(crudService).save(saveCaptor.capture());
        DecisionDelivery entity = saveCaptor.getValue();
        assertEquals(entity.getResult(), "COMPLETED");
        assertNotNull(entity.getUploadedDate());
    }

    @Test
    public void decisionsDeliveredByType() {
        PropertyDailyProcessing pdp = new PropertyDailyProcessing();
        pdp.setPropertyTimeZone(TIME_ZONE);
        InputProcessing inputProcessing = new InputProcessing();
        pdp.addInputProcessing(inputProcessing);
        DecisionDelivery decisionDelivery = new DecisionDelivery();
        decisionDelivery.setResult("IN_PROGRESS");
        decisionDelivery.setDestinationId(DESTINATION_ID);
        inputProcessing.addDecisionDelivery(decisionDelivery);
        DecisionDeliveryByType decisionDeliveryByType1 = new DecisionDeliveryByType();
        decisionDeliveryByType1.setDecisionType("decisionType1");
        decisionDeliveryByType1.setStatus(DecisionDeliveryByTypeStatus.IN_PROGRESS);
        DecisionDeliveryByType decisionDeliveryByType2 = new DecisionDeliveryByType();
        decisionDeliveryByType2.setDecisionType("decisionType2");
        decisionDeliveryByType2.setStatus(DELIVERED);
        decisionDelivery.addDecisionDeliveryByType(decisionDeliveryByType1);
        decisionDelivery.addDecisionDeliveryByType(decisionDeliveryByType2);
        when(crudService.find(InputProcessing.class, INPUT_PROCESSING_ID)).thenReturn(inputProcessing);
        dailyProcessingService.decisionsDeliveredByType(INPUT_PROCESSING_ID, DESTINATION_ID, "decisionType1");
        ArgumentCaptor<DecisionDeliveryByType> captor = ArgumentCaptor.forClass(DecisionDeliveryByType.class);
        verify(crudService).save(captor.capture());
        DecisionDeliveryByType savedDecisionDeliveryByType = captor.getValue();
        assertEquals(savedDecisionDeliveryByType.getDecisionType(), "decisionType1");
        assertEquals(DELIVERED, savedDecisionDeliveryByType.getStatus());
        verifyNoMoreInteractions(ignoreStubs(crudService));
    }

    @Test
    public void decisionsDeliveredByType_notInProgress() {
        PropertyDailyProcessing pdp = new PropertyDailyProcessing();
        pdp.setPropertyTimeZone(TIME_ZONE);
        InputProcessing inputProcessing = new InputProcessing();
        pdp.addInputProcessing(inputProcessing);
        DecisionDelivery decisionDelivery = new DecisionDelivery();
        decisionDelivery.setResult("IN_PROGRESS");
        decisionDelivery.setDestinationId(DESTINATION_ID);
        inputProcessing.addDecisionDelivery(decisionDelivery);
        DecisionDeliveryByType decisionDeliveryByType1 = new DecisionDeliveryByType();
        decisionDeliveryByType1.setDecisionType("decisionType1");
        decisionDeliveryByType1.setStatus(DecisionDeliveryByTypeStatus.IN_PROGRESS);
        DecisionDeliveryByType decisionDeliveryByType2 = new DecisionDeliveryByType();
        decisionDeliveryByType2.setDecisionType("decisionType2");
        decisionDeliveryByType2.setStatus(DELIVERED);
        decisionDelivery.addDecisionDeliveryByType(decisionDeliveryByType1);
        decisionDelivery.addDecisionDeliveryByType(decisionDeliveryByType2);
        when(crudService.find(InputProcessing.class, INPUT_PROCESSING_ID)).thenReturn(inputProcessing);
        dailyProcessingService.decisionsDeliveredByType(INPUT_PROCESSING_ID, DESTINATION_ID, "decisionType2");
        verifyNoMoreInteractions(ignoreStubs(crudService));
    }

    @Test
    public void test_completeDestinationByType_notComplete() {
        PropertyDailyProcessing pdp = new PropertyDailyProcessing();
        pdp.setPropertyTimeZone(TIME_ZONE);
        InputProcessing inputProcessing = new InputProcessing();
        pdp.addInputProcessing(inputProcessing);
        DecisionDelivery decisionDelivery = new DecisionDelivery();
        decisionDelivery.setResult("IN_PROGRESS");
        decisionDelivery.setDestinationId(DESTINATION_ID);
        inputProcessing.addDecisionDelivery(decisionDelivery);
        DecisionDeliveryByType decisionDeliveryByType1 = new DecisionDeliveryByType();
        decisionDeliveryByType1.setStatus(DELIVERED);
        DecisionDeliveryByType decisionDeliveryByType2 = new DecisionDeliveryByType();
        decisionDeliveryByType2.setStatus(DecisionDeliveryByTypeStatus.IN_PROGRESS);
        decisionDelivery.addDecisionDeliveryByType(decisionDeliveryByType1);
        decisionDelivery.addDecisionDeliveryByType(decisionDeliveryByType2);
        when(crudService.find(InputProcessing.class, INPUT_PROCESSING_ID)).thenReturn(inputProcessing);
        dailyProcessingService.completeDestinationByDecisionType(INPUT_PROCESSING_ID, DESTINATION_ID);
        assertEquals(decisionDelivery.getResult(), "IN_PROGRESS");
        assertNull(decisionDelivery.getUploadedDate());
        verifyNoMoreInteractions(ignoreStubs(crudService));
    }

    @Test
    public void test_operaDecisionsDelivered_BDE_allCompleted() {
        DecisionDelivery decisionDelivery = new DecisionDelivery();
        decisionDelivery.setResult("COMPLETED");
        decisionDelivery.setDestinationId(DESTINATION_ID);
        decisionDelivery.setUploadedDate(LocalDateTime.now());
        InputProcessing inputProcessing = new InputProcessing();
        PropertyDailyProcessing pdp = new PropertyDailyProcessing();
        pdp.setPropertyTimeZone(TIME_ZONE);
        pdp.setPropertyCode(PROPERTY_CODE);
        pdp.setClientCode(CLIENT_CODE);
        pdp.setExpectedCdpCount(1);
        pdp.setStage(Stage.ONE_WAY.getCode());
        inputProcessing.setPropertyDailyProcessing(pdp);
        inputProcessing.setInputType(InputType.BDE.toString());
        inputProcessing.addDecisionDelivery(decisionDelivery);
        when(crudService.find(InputProcessing.class, INPUT_PROCESSING_ID)).thenReturn(inputProcessing);
        when(pacmanConfigParamsService.getParameterValue(anyString(), anyString(),
                eq(NOTIFY_AWS_POST_PROCESSING_ENABLED))).thenReturn(false);
        dailyProcessingService.operaDecisionsDelivered(INPUT_PROCESSING_ID, DESTINATION_ID);
        verify(crudService).find(InputProcessing.class, INPUT_PROCESSING_ID);
        ArgumentCaptor<IdAwareEntity> saveCaptor = ArgumentCaptor.forClass(IdAwareEntity.class);
        verify(crudService, times(2)).save(saveCaptor.capture());
        List<IdAwareEntity> entities = saveCaptor.getAllValues();
        assertEquals(2, entities.size());
        InputProcessing entity = (InputProcessing) entities.get(0);
        assertEquals(InputProcessingStatus.COMPLETED.toString(), entity.getStatus());
        assertNotNull(entity.getCompletedDate());
        PropertyDailyProcessing entity2 = (PropertyDailyProcessing) entities.get(1);
        assertEquals(DailyProcessingStatus.BDE_COMPLETED.toString(), entity2.getStatus());
    }

    @Test
    public void test_operaDecisionsDelivered_CDP_allCompleted() {
        DecisionDelivery decisionDelivery = new DecisionDelivery();
        decisionDelivery.setResult("COMPLETED");
        decisionDelivery.setDestinationId(DESTINATION_ID);
        decisionDelivery.setUploadedDate(LocalDateTime.now());
        InputProcessing inputProcessing = new InputProcessing();
        PropertyDailyProcessing pdp = new PropertyDailyProcessing();
        pdp.setPropertyTimeZone(TIME_ZONE);
        pdp.setPropertyCode(PROPERTY_CODE);
        pdp.setClientCode(CLIENT_CODE);
        pdp.setExpectedCdpCount(1);
        pdp.setStage(Stage.ONE_WAY.getCode());
        pdp.setCompletedCdpCount(0);
        inputProcessing.setPropertyDailyProcessing(pdp);
        inputProcessing.setInputType(InputType.CDP.toString());
        inputProcessing.addDecisionDelivery(decisionDelivery);
        when(crudService.find(InputProcessing.class, INPUT_PROCESSING_ID)).thenReturn(inputProcessing);
        dailyProcessingService.operaDecisionsDelivered(INPUT_PROCESSING_ID, DESTINATION_ID);
        verify(crudService).find(InputProcessing.class, INPUT_PROCESSING_ID);
        ArgumentCaptor<IdAwareEntity> saveCaptor = ArgumentCaptor.forClass(IdAwareEntity.class);
        verify(crudService, times(2)).save(saveCaptor.capture());
        List<IdAwareEntity> entities = saveCaptor.getAllValues();
        assertEquals(2, entities.size());
        InputProcessing entity = (InputProcessing) entities.get(0);
        assertEquals(InputProcessingStatus.COMPLETED.toString(), entity.getStatus());
        assertNotNull(entity.getCompletedDate());
        PropertyDailyProcessing entity2 = (PropertyDailyProcessing) entities.get(1);
        assertEquals(DailyProcessingStatus.DAILY_PROCESSING_COMPLETED.toString(), entity2.getStatus());
    }

    @Test
    public void test_operaDecisionsDelivered_BDE_notAllCompleted() {
        DecisionDelivery decisionDelivery = new DecisionDelivery();
        decisionDelivery.setResult("IN_PROGRESS");
        decisionDelivery.setDestinationId(DESTINATION_ID);
        InputProcessing inputProcessing = new InputProcessing();
        PropertyDailyProcessing pdp = new PropertyDailyProcessing();
        pdp.setPropertyTimeZone(TIME_ZONE);
        pdp.setPropertyCode(PROPERTY_CODE);
        pdp.setClientCode(CLIENT_CODE);
        pdp.setExpectedCdpCount(1);
        pdp.setStage(Stage.ONE_WAY.getCode());
        pdp.setStatus(DailyProcessingStatus.BDE_DECISIONS_IN_PROGRESS.toString());
        inputProcessing.setPropertyDailyProcessing(pdp);
        inputProcessing.setInputType(InputType.BDE.toString());
        inputProcessing.addDecisionDelivery(decisionDelivery);
        inputProcessing.setStatus(InputProcessingStatus.DECISIONS_IN_PROGRESS.toString());
        when(crudService.find(InputProcessing.class, INPUT_PROCESSING_ID)).thenReturn(inputProcessing);
        dailyProcessingService.operaDecisionsDelivered(INPUT_PROCESSING_ID, DESTINATION_ID);
        verify(crudService).find(InputProcessing.class, INPUT_PROCESSING_ID);
        ArgumentCaptor<IdAwareEntity> saveCaptor = ArgumentCaptor.forClass(IdAwareEntity.class);
        verify(crudService, times(2)).save(saveCaptor.capture());
        List<IdAwareEntity> entities = saveCaptor.getAllValues();
        assertEquals(2, entities.size());
        InputProcessing entity = (InputProcessing) entities.get(0);
        assertEquals(InputProcessingStatus.DECISIONS_IN_PROGRESS.toString(), entity.getStatus());
        assertNull(entity.getCompletedDate());
        PropertyDailyProcessing entity2 = (PropertyDailyProcessing) entities.get(1);
        assertEquals(DailyProcessingStatus.BDE_DECISIONS_IN_PROGRESS.toString(), entity2.getStatus());
    }

    @Test
    public void test_operaDecisionsDelivered_BDE_notlast() {
        LocalDateTime NOW = LocalDateTime.now();
        DecisionDelivery decisionDelivery1 = new DecisionDelivery();
        decisionDelivery1.setResult("COMPLETED");
        decisionDelivery1.setDestinationId(DESTINATION_ID);
        decisionDelivery1.setUploadedDate(NOW);
        DecisionDelivery decisionDelivery2 = new DecisionDelivery();
        decisionDelivery2.setResult("COMPLETED");
        decisionDelivery2.setDestinationId(DESTINATION_ID + "foo");
        decisionDelivery2.setUploadedDate(NOW.plusMillis(1));
        InputProcessing inputProcessing = new InputProcessing();
        PropertyDailyProcessing pdp = new PropertyDailyProcessing();
        pdp.setPropertyTimeZone(TIME_ZONE);
        pdp.setPropertyCode(PROPERTY_CODE);
        pdp.setClientCode(CLIENT_CODE);
        pdp.setExpectedCdpCount(1);
        pdp.setStage(Stage.ONE_WAY.getCode());
        pdp.setStatus(DailyProcessingStatus.BDE_DECISIONS_IN_PROGRESS.toString());
        inputProcessing.setPropertyDailyProcessing(pdp);
        inputProcessing.setInputType(InputType.BDE.toString());
        inputProcessing.addDecisionDelivery(decisionDelivery1);
        inputProcessing.addDecisionDelivery(decisionDelivery2);
        inputProcessing.setStatus(InputProcessingStatus.DECISIONS_IN_PROGRESS.toString());
        when(crudService.find(InputProcessing.class, INPUT_PROCESSING_ID)).thenReturn(inputProcessing);
        dailyProcessingService.operaDecisionsDelivered(INPUT_PROCESSING_ID, DESTINATION_ID);
        verify(crudService).find(InputProcessing.class, INPUT_PROCESSING_ID);
        ArgumentCaptor<IdAwareEntity> saveCaptor = ArgumentCaptor.forClass(IdAwareEntity.class);
        verify(crudService, times(2)).save(saveCaptor.capture());
        List<IdAwareEntity> entities = saveCaptor.getAllValues();
        assertEquals(2, entities.size());
        InputProcessing entity = (InputProcessing) entities.get(0);
        assertEquals(InputProcessingStatus.DECISIONS_IN_PROGRESS.toString(), entity.getStatus());
        assertNull(entity.getCompletedDate());
        PropertyDailyProcessing entity2 = (PropertyDailyProcessing) entities.get(1);
        assertEquals(DailyProcessingStatus.BDE_DECISIONS_IN_PROGRESS.toString(), entity2.getStatus());
    }

    @Test
    public void test_operaDecisionsDelivered_BDE_last() {
        LocalDateTime NOW = LocalDateTime.now();
        DecisionDelivery decisionDelivery1 = new DecisionDelivery();
        decisionDelivery1.setResult("COMPLETED");
        decisionDelivery1.setDestinationId(DESTINATION_ID);
        decisionDelivery1.setUploadedDate(NOW);
        DecisionDelivery decisionDelivery2 = new DecisionDelivery();
        decisionDelivery2.setResult("COMPLETED");
        decisionDelivery2.setDestinationId(DESTINATION_ID + "foo");
        decisionDelivery2.setUploadedDate(NOW.plusMillis(-1));
        InputProcessing inputProcessing = new InputProcessing();
        PropertyDailyProcessing pdp = new PropertyDailyProcessing();
        pdp.setPropertyTimeZone(TIME_ZONE);
        pdp.setPropertyCode(PROPERTY_CODE);
        pdp.setClientCode(CLIENT_CODE);
        pdp.setExpectedCdpCount(1);
        pdp.setStage(Stage.ONE_WAY.getCode());
        pdp.setStatus(DailyProcessingStatus.BDE_DECISIONS_IN_PROGRESS.toString());
        inputProcessing.setPropertyDailyProcessing(pdp);
        inputProcessing.setInputType(InputType.BDE.toString());
        inputProcessing.addDecisionDelivery(decisionDelivery1);
        inputProcessing.addDecisionDelivery(decisionDelivery2);
        inputProcessing.setStatus(InputProcessingStatus.DECISIONS_IN_PROGRESS.toString());
        when(crudService.find(InputProcessing.class, INPUT_PROCESSING_ID)).thenReturn(inputProcessing);
        when(pacmanConfigParamsService.getParameterValue(anyString(), anyString(),
                eq(NOTIFY_AWS_POST_PROCESSING_ENABLED))).thenReturn(false);
        dailyProcessingService.operaDecisionsDelivered(INPUT_PROCESSING_ID, DESTINATION_ID);
        verify(crudService).find(InputProcessing.class, INPUT_PROCESSING_ID);
        ArgumentCaptor<IdAwareEntity> saveCaptor = ArgumentCaptor.forClass(IdAwareEntity.class);
        verify(crudService, times(2)).save(saveCaptor.capture());
        List<IdAwareEntity> entities = saveCaptor.getAllValues();
        assertEquals(2, entities.size());
        InputProcessing entity = (InputProcessing) entities.get(0);
        assertEquals(InputProcessingStatus.COMPLETED.toString(), entity.getStatus());
        assertNotNull(entity.getCompletedDate());
        PropertyDailyProcessing entity2 = (PropertyDailyProcessing) entities.get(1);
        assertEquals(DailyProcessingStatus.BDE_COMPLETED.toString(), entity2.getStatus());
    }

    @Test
    public void test_operaDecisionsDelivered_BDE_sameTime_last_DecisionIdMapper() {
        LocalDateTime NOW = LocalDateTime.now();
        DecisionDelivery decisionDelivery1 = new DecisionDelivery();
        decisionDelivery1.setResult("COMPLETED");
        decisionDelivery1.setDestinationId(DESTINATION_ID);
        decisionDelivery1.setDecisionId("correlation-id");
        decisionDelivery1.setUploadedDate(NOW);
        DecisionDelivery decisionDelivery2 = new DecisionDelivery();
        decisionDelivery2.setResult("COMPLETED");
        decisionDelivery2.setDestinationId("ZZZ" + DESTINATION_ID);
        decisionDelivery2.setUploadedDate(NOW.plusSeconds(-10));
        InputProcessing inputProcessing = new InputProcessing();
        PropertyDailyProcessing pdp = new PropertyDailyProcessing();
        pdp.setPropertyTimeZone(TIME_ZONE);
        pdp.setPropertyCode(PROPERTY_CODE);
        pdp.setClientCode(CLIENT_CODE);
        pdp.setExpectedCdpCount(1);
        pdp.setStage(Stage.ONE_WAY.getCode());
        pdp.setStatus(DailyProcessingStatus.BDE_DECISIONS_IN_PROGRESS.toString());
        inputProcessing.setPropertyDailyProcessing(pdp);
        inputProcessing.setInputId("correlation-id");
        inputProcessing.setInputType(InputType.BDE.toString());
        inputProcessing.addDecisionDelivery(decisionDelivery1);
        inputProcessing.addDecisionDelivery(decisionDelivery2);
        inputProcessing.setStatus(InputProcessingStatus.DECISIONS_IN_PROGRESS.toString());
        when(crudService.find(InputProcessing.class, INPUT_PROCESSING_ID)).thenReturn(inputProcessing);
        when(pacmanConfigParamsService.getParameterValue(anyString(), anyString(),
                eq(NOTIFY_AWS_POST_PROCESSING_ENABLED))).thenReturn(false);
        dailyProcessingService.operaDecisionsDelivered(INPUT_PROCESSING_ID, DESTINATION_ID,
                InputProcessing::getInputId);
        verify(crudService).find(InputProcessing.class, INPUT_PROCESSING_ID);
        ArgumentCaptor<IdAwareEntity> saveCaptor = ArgumentCaptor.forClass(IdAwareEntity.class);
        verify(crudService, times(2)).save(saveCaptor.capture());
        List<IdAwareEntity> entities = saveCaptor.getAllValues();
        assertEquals(2, entities.size());
        InputProcessing entity = (InputProcessing) entities.get(0);
        assertEquals(InputProcessingStatus.COMPLETED.toString(), entity.getStatus());
        assertNotNull(entity.getCompletedDate());
        PropertyDailyProcessing entity2 = (PropertyDailyProcessing) entities.get(1);
        assertEquals(DailyProcessingStatus.BDE_COMPLETED.toString(), entity2.getStatus());
    }

    @Test
    public void test_operaDecisionsDelivered_BDE_sameTime_last() {
        LocalDateTime NOW = LocalDateTime.now();
        DecisionDelivery decisionDelivery1 = new DecisionDelivery();
        decisionDelivery1.setResult("COMPLETED");
        decisionDelivery1.setDestinationId(DESTINATION_ID);
        decisionDelivery1.setUploadedDate(NOW);
        DecisionDelivery decisionDelivery2 = new DecisionDelivery();
        decisionDelivery2.setResult("COMPLETED");
        decisionDelivery2.setDestinationId("ZZZ" + DESTINATION_ID);
        decisionDelivery2.setUploadedDate(NOW);
        InputProcessing inputProcessing = new InputProcessing();
        PropertyDailyProcessing pdp = new PropertyDailyProcessing();
        pdp.setPropertyTimeZone(TIME_ZONE);
        pdp.setPropertyCode(PROPERTY_CODE);
        pdp.setClientCode(CLIENT_CODE);
        pdp.setExpectedCdpCount(1);
        pdp.setStage(Stage.ONE_WAY.getCode());
        pdp.setStatus(DailyProcessingStatus.BDE_DECISIONS_IN_PROGRESS.toString());
        inputProcessing.setPropertyDailyProcessing(pdp);
        inputProcessing.setInputType(InputType.BDE.toString());
        inputProcessing.addDecisionDelivery(decisionDelivery1);
        inputProcessing.addDecisionDelivery(decisionDelivery2);
        inputProcessing.setStatus(InputProcessingStatus.DECISIONS_IN_PROGRESS.toString());
        when(crudService.find(InputProcessing.class, INPUT_PROCESSING_ID)).thenReturn(inputProcessing);
        when(pacmanConfigParamsService.getParameterValue(any(), any(),
                eq(NOTIFY_AWS_POST_PROCESSING_ENABLED))).thenReturn(false);
        dailyProcessingService.operaDecisionsDelivered(INPUT_PROCESSING_ID, DESTINATION_ID);
        verify(crudService).find(InputProcessing.class, INPUT_PROCESSING_ID);
        ArgumentCaptor<IdAwareEntity> saveCaptor = ArgumentCaptor.forClass(IdAwareEntity.class);
        verify(crudService, times(2)).save(saveCaptor.capture());
        List<IdAwareEntity> entities = saveCaptor.getAllValues();
        assertEquals(2, entities.size());
        InputProcessing entity = (InputProcessing) entities.get(0);
        assertEquals(InputProcessingStatus.COMPLETED.toString(), entity.getStatus());
        assertNotNull(entity.getCompletedDate());
        PropertyDailyProcessing entity2 = (PropertyDailyProcessing) entities.get(1);
        assertEquals(DailyProcessingStatus.BDE_COMPLETED.toString(), entity2.getStatus());
    }

    @Test
    public void test_operaDecisionsDelivered_BDE_sameTime_notLast() {
        LocalDateTime NOW = LocalDateTime.now();
        DecisionDelivery decisionDelivery1 = new DecisionDelivery();
        decisionDelivery1.setResult("COMPLETED");
        decisionDelivery1.setDestinationId(DESTINATION_ID);
        decisionDelivery1.setUploadedDate(NOW);
        DecisionDelivery decisionDelivery2 = new DecisionDelivery();
        decisionDelivery2.setResult("COMPLETED");
        decisionDelivery2.setDestinationId("AAA" + DESTINATION_ID);
        decisionDelivery2.setUploadedDate(NOW);
        InputProcessing inputProcessing = new InputProcessing();
        PropertyDailyProcessing pdp = new PropertyDailyProcessing();
        pdp.setPropertyTimeZone(TIME_ZONE);
        pdp.setPropertyCode(PROPERTY_CODE);
        pdp.setClientCode(CLIENT_CODE);
        pdp.setExpectedCdpCount(1);
        pdp.setStage(Stage.ONE_WAY.getCode());
        pdp.setStatus(DailyProcessingStatus.BDE_DECISIONS_IN_PROGRESS.toString());
        inputProcessing.setPropertyDailyProcessing(pdp);
        inputProcessing.setInputType(InputType.BDE.toString());
        inputProcessing.addDecisionDelivery(decisionDelivery1);
        inputProcessing.addDecisionDelivery(decisionDelivery2);
        inputProcessing.setStatus(InputProcessingStatus.DECISIONS_IN_PROGRESS.toString());
        when(crudService.find(InputProcessing.class, INPUT_PROCESSING_ID)).thenReturn(inputProcessing);
        dailyProcessingService.operaDecisionsDelivered(INPUT_PROCESSING_ID, DESTINATION_ID);
        verify(crudService).find(InputProcessing.class, INPUT_PROCESSING_ID);
        ArgumentCaptor<IdAwareEntity> saveCaptor = ArgumentCaptor.forClass(IdAwareEntity.class);
        verify(crudService, times(2)).save(saveCaptor.capture());
        List<IdAwareEntity> entities = saveCaptor.getAllValues();
        assertEquals(2, entities.size());
        InputProcessing entity = (InputProcessing) entities.get(0);
        assertEquals(InputProcessingStatus.DECISIONS_IN_PROGRESS.toString(), entity.getStatus());
        assertNull(entity.getCompletedDate());
        PropertyDailyProcessing entity2 = (PropertyDailyProcessing) entities.get(1);
        assertEquals(DailyProcessingStatus.BDE_DECISIONS_IN_PROGRESS.toString(), entity2.getStatus());
    }

    @Test
    public void shouldSetExtractStatusInForcefulCompleteBDE() {
        buildInputProcessing("BDE", null).whenForcefulCompleteMethodCalled(1).assertExtractStatusWasSetTo(ExtractStatus.COMPLETE, captureSingleInputProcessingArgument());
    }

    @Test
    public void shouldNOTSetExtractStatusInForcefulCompleteCDP() {
        buildInputProcessing("CDP", null).whenForcefulCompleteMethodCalled(1).assertExtractStatusWasSetTo(null, captureSingleInputProcessingArgument());
    }

    @Test
    public void shouldSetExtractStatusInForcefulCompleteClientBDE() {
        buildInputProcessing("BDE", null).whenForcefulCompleteClientMethodCalled(CLIENT_CODE, LocalDate.now()).assertExtractStatusWasSetTo(ExtractStatus.COMPLETE, captureListInputProcessingArgument());
    }

    @Test
    public void shouldNOTSetExtractStatusInForcefulCompleteClientCDP() {
        buildInputProcessing("CDP", null).whenForcefulCompleteClientMethodCalled(CLIENT_CODE, LocalDate.now()).assertExtractStatusWasSetTo(null, captureListInputProcessingArgument());
    }

    @Test
    public void shouldSetExtractStatusWhenCurrentValueIsNull() {
        buildInputProcessing("BDE", null).whenUpdateExtractStatusMethodCalled().assertExtractStatus(inputProcessing);
    }

    @Test
    public void shouldSetExtractStatusWhenCurrentValueIsNotReceived() {
        buildInputProcessing("BDE", ExtractStatus.NOT_RECEIVED).whenUpdateExtractStatusMethodCalled().assertExtractStatus(inputProcessing);
    }

    @Test
    public void shouldSetExtractStatusWhenCurrentValueIsInComplete() {
        buildInputProcessing("BDE", ExtractStatus.INCOMPLETE).whenUpdateExtractStatusMethodCalled().assertExtractStatus(inputProcessing);
    }

    @Test
    public void shouldNotUpdateExtractStatusWhenCurrentValueIsComplete() {
        buildInputProcessing("BDE", ExtractStatus.COMPLETE).whenUpdateExtractStatusMethodCalled().assertExtractStatus(inputProcessing);
    }

    @Test
    public void test_decisionsGenerated_existingDecisionDelivery() {
        InputProcessing inputProcessing = getInputProcessing();
        inputProcessing.setStatus(InputProcessingStatus.IN_PROGRESS.toString());
        inputProcessing.getPropertyDailyProcessing().setStatus(DailyProcessingStatus.BDE_IN_PROGRESS.toString());
        List<InputProcessing> entities = Collections.singletonList(inputProcessing);
        DecisionDelivery decisionDelivery = new DecisionDelivery();
        decisionDelivery.setDestinationId(DESTINATION_ID);
        decisionDelivery.setResult("COMPLETED");
        inputProcessing.addDecisionDelivery(decisionDelivery);
        when(crudService.findByCriteria(any(InputProcessingCriteria.class))).thenReturn(entities);
        dailyProcessingService.decisionsGenerated(PROPERTY_CODE, CLIENT_CODE, CORRELATION_ID, INPUT_FILE_NAME, DESTINATION_ID);
        assertThat(inputProcessing.getPropertyDailyProcessing().getStatus(), is(DailyProcessingStatus.BDE_DECISIONS_IN_PROGRESS.toString()));
        verify(crudService).save(inputProcessing);
        verify(crudService).save(inputProcessing.getPropertyDailyProcessing());
        verify(crudService).findByCriteria(any(InputProcessingCriteria.class));
        verifyNoMoreInteractions(crudService);
    }

    @Test
    public void test_decisionsGenerated_noExistingDecisionDelivery() {
        InputProcessing inputProcessing = getInputProcessing();
        inputProcessing.setStatus(InputProcessingStatus.IN_PROGRESS.toString());
        inputProcessing.getPropertyDailyProcessing().setStatus(DailyProcessingStatus.BDE_IN_PROGRESS.toString());
        List<InputProcessing> entities = Collections.singletonList(inputProcessing);
        DecisionDelivery decisionDelivery = new DecisionDelivery();
        decisionDelivery.setDestinationId(DESTINATION_ID + "foo");
        decisionDelivery.setResult("COMPLETED");
        inputProcessing.addDecisionDelivery(decisionDelivery);
        when(crudService.findByCriteria(any(InputProcessingCriteria.class))).thenReturn(entities);
        dailyProcessingService.decisionsGenerated(PROPERTY_CODE, CLIENT_CODE, CORRELATION_ID, INPUT_FILE_NAME, DESTINATION_ID);
        assertThat(inputProcessing.getPropertyDailyProcessing().getStatus(), is(DailyProcessingStatus.BDE_DECISIONS_IN_PROGRESS.toString()));
        verify(crudService, atLeastOnce()).save(any(DecisionDelivery.class));
        verify(crudService).save(inputProcessing);
        verify(crudService).save(inputProcessing.getPropertyDailyProcessing());
        verify(crudService).findByCriteria(any(InputProcessingCriteria.class));
        verifyNoMoreInteractions(crudService);
    }

    @Test
    public void test_operaDecisionsGenerated_existingDecisionDelivery() {
        InputProcessing inputProcessing = getInputProcessing();
        inputProcessing.setStatus(InputProcessingStatus.IN_PROGRESS.toString());
        inputProcessing.getPropertyDailyProcessing().setStatus(DailyProcessingStatus.BDE_IN_PROGRESS.toString());
        DecisionDelivery decisionDelivery = new DecisionDelivery();
        decisionDelivery.setDestinationId(DESTINATION_ID);
        decisionDelivery.setResult("COMPLETED");
        inputProcessing.addDecisionDelivery(decisionDelivery);
        when(crudService.find(InputProcessing.class, INPUT_PROCESSING_ID)).thenReturn(inputProcessing);
        dailyProcessingService.operaDecisionsGenerated(INPUT_PROCESSING_ID, DESTINATION_ID);
        assertThat(inputProcessing.getPropertyDailyProcessing().getStatus(), is(DailyProcessingStatus.BDE_DECISIONS_IN_PROGRESS.toString()));
        assertEquals(1, inputProcessing.getDecisionDeliveries().size());
        verify(crudService).save(inputProcessing.getPropertyDailyProcessing());
        verify(crudService).find(InputProcessing.class, INPUT_PROCESSING_ID);
        verifyNoMoreInteractions(crudService);
    }

    @Test
    public void test_operaDecisionsGenerated_noExistingDecisionDelivery() {
        InputProcessing inputProcessing = getInputProcessing();
        inputProcessing.setStatus(InputProcessingStatus.IN_PROGRESS.toString());
        inputProcessing.getPropertyDailyProcessing().setStatus(DailyProcessingStatus.BDE_IN_PROGRESS.toString());
        DecisionDelivery decisionDelivery = new DecisionDelivery();
        decisionDelivery.setDestinationId(DESTINATION_ID + "foo");
        decisionDelivery.setResult("COMPLETED");
        inputProcessing.addDecisionDelivery(decisionDelivery);
        when(crudService.find(InputProcessing.class, INPUT_PROCESSING_ID)).thenReturn(inputProcessing);
        dailyProcessingService.operaDecisionsGenerated(INPUT_PROCESSING_ID, DESTINATION_ID);
        assertThat(inputProcessing.getPropertyDailyProcessing().getStatus(), is(DailyProcessingStatus.BDE_DECISIONS_IN_PROGRESS.toString()));
        assertEquals(2, inputProcessing.getDecisionDeliveries().size());
        verify(crudService).save(inputProcessing.getPropertyDailyProcessing());
        verify(crudService).find(InputProcessing.class, INPUT_PROCESSING_ID);
        verifyNoMoreInteractions(crudService);
    }

    @Test
    public void test_findCurrentPropertyDailyProcessing() {
        PropertyDailyProcessing pdp = new PropertyDailyProcessing();
        when(pacmanConfigParamsService.getValue("pacman." + CLIENT_CODE + "." + PROPERTY_CODE, IntegrationConfigParamName.CORE_PROPERTY_TIME_ZONE.value())).thenReturn(TIME_ZONE);
        when(crudService.findByNamedQuerySingleResult(PropertyDailyProcessing.BY_CLIENT_CODE_PROPERTY_CODE_AND_DATE_WITH_INPUTS, QueryParameter.with(Constants.CLIENT_CODE, CLIENT_CODE).and(Constants.PROPERTY_CODE, PROPERTY_CODE).and("processingDate", new LocalDate(DateTimeZone.forTimeZone(TimeZone.getTimeZone(TIME_ZONE)))).parameters())).thenReturn(pdp);
        PropertyDailyProcessing result = dailyProcessingService.findCurrentPropertyDailyProcessing(CLIENT_CODE, PROPERTY_CODE);
        assertEquals(pdp, result);
        verify(pacmanConfigParamsService).getValue("pacman." + CLIENT_CODE + "." + PROPERTY_CODE, IntegrationConfigParamName.CORE_PROPERTY_TIME_ZONE.value());
        verify(crudService).findByNamedQuerySingleResult(PropertyDailyProcessing.BY_CLIENT_CODE_PROPERTY_CODE_AND_DATE_WITH_INPUTS, QueryParameter.with(Constants.CLIENT_CODE, CLIENT_CODE).and(Constants.PROPERTY_CODE, PROPERTY_CODE).and("processingDate", new LocalDate(DateTimeZone.forTimeZone(TimeZone.getTimeZone(TIME_ZONE)))).parameters());
    }

    @Test
    public void shouldSetValueToExtractStatusColumnAsNotReceived() {
        when(pacmanConfigParamsService.getValue(any(), any())).thenReturn("90");
        Property property = new Property();
        property.setClient(new Client());
        property.getClient().setCode(CLIENT_CODE);
        property.setCode(PROPERTY_CODE);
        property.setStage(Stage.TWO_WAY);
        when(pacmanConfigParamsService.getParameterValue(CLIENT_CODE, PROPERTY_CODE, IntegrationConfigParamName.CORE_PROPERTY_TIME_ZONE)).thenReturn(TIME_ZONE);

        dailyProcessingService.createDailyProcessingRecord(property, LocalDate.now());
        ArgumentCaptor<PropertyDailyProcessing> saveCaptor = ArgumentCaptor.forClass(PropertyDailyProcessing.class);
        verify(crudService).save(saveCaptor.capture());
        PropertyDailyProcessing dailyProcessingRecord = saveCaptor.getValue();
        assertEquals(ExtractStatus.NOT_RECEIVED, dailyProcessingRecord.getInputProcessings().stream().filter(inputProcessing -> "BDE".equals(inputProcessing.getInputType())).findFirst().get().getExtractStatus());
    }

    private DailyProcessingServiceMockTest assertExtractDTTM() {
        InputProcessing savedIp = getInputProcessingSaved();
        assertNotNull(savedIp.getExtractStartedDateTime());
        assertNotNull(savedIp.getExtractCompletedDateTime());
        return null;
    }

    private InputProcessing captureSingleInputProcessingArgument() {
        verify(crudService).save(inputProcessingCaptor.capture());
        return inputProcessingCaptor.getValue();
    }

    private InputProcessing captureListInputProcessingArgument() {
        verify(crudService).save(inputProcessingListCaptor.capture());
        return inputProcessingListCaptor.getValue().get(0);
    }

    private DailyProcessingServiceMockTest assertExtractStatusWasSetTo(ExtractStatus expectedStatus, InputProcessing inputProcessing) {
        assertEquals(expectedStatus, inputProcessing.getExtractStatus());
        return this;
    }

    private DailyProcessingServiceMockTest whenUpdateExtractStatusMethodCalled() {
        dailyProcessingService.updateExtractStatusIfNotSet(inputProcessing, null);
        return this;
    }

    private DailyProcessingServiceMockTest whenForcefulCompleteMethodCalled(Integer inputProcessingId) {
        when(crudService.find(InputProcessing.class, inputProcessingId)).thenReturn(inputProcessing);
        dailyProcessingService.forceCompleteInputProcessing(inputProcessingId);
        return this;
    }

    private DailyProcessingServiceMockTest whenForcefulCompleteClientMethodCalled(String clientCode, LocalDate processingDate) {
        when(crudService.findByCriteria(any())).thenReturn(Arrays.asList(inputProcessing));
        dailyProcessingService.forceCompleteInputProcessingByClient(clientCode, processingDate);
        return this;
    }

    private NgiExtractReceivedTracker getNgiExtractReceivedTracker() {
        NgiExtractReceivedTracker tracker = new NgiExtractReceivedTracker();
        tracker.setExtractCompletedDTTM(java.time.LocalDateTime.now());
        tracker.setExtractStartedDTTM(java.time.LocalDateTime.now());
        return tracker;
    }

    private List<JSONObject> getJsonList() throws JSONException {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("extractStartedDTTM", java.time.LocalDateTime.now());
        jsonObject.put("extractCompletedDTTM", java.time.LocalDateTime.now());
        return Collections.singletonList(jsonObject);
    }

    private DailyProcessingServiceMockTest assertExtractStatus() {
        InputProcessing savedIp = getInputProcessingSaved();
        assertEquals(ExtractStatus.COMPLETE, savedIp.getExtractStatus());
        return this;
    }

    private InputProcessing getInputProcessingSaved() {
        ArgumentCaptor<IdAware> saveCaptor = ArgumentCaptor.forClass(IdAware.class);
        verify(crudService, times(2)).save(saveCaptor.capture());
        List<IdAware> values = saveCaptor.getAllValues();
        return (InputProcessing) values.get(0);
    }

    private DailyProcessingServiceMockTest inputProcessingIsSetToDailProcessing() {
        propertyDailyProcessing.setInputProcessings(Arrays.asList(inputProcessing));
        return this;
    }

    private DailyProcessingServiceMockTest buildInputProcessing(String inputType, ExtractStatus extractStatus) {
        inputProcessing = new InputProcessing();
        inputProcessing.setExtractStatus(extractStatus);
        inputProcessing.setInputType(inputType);
        inputProcessing.setPropertyDailyProcessing(propertyDailyProcessing);
        inputProcessing.setStatus(InputProcessingStatus.IN_PROGRESS.name());
        inputProcessing.setReceivedDate(LocalDateTime.now());
        return this;
    }

    private DailyProcessingServiceMockTest buildDailyProcessing(String clientCode, String propertyCode, String propertyTimeZone) {
        propertyDailyProcessing = new PropertyDailyProcessing();
        propertyDailyProcessing.setClientCode(clientCode);
        propertyDailyProcessing.setPropertyCode(propertyCode);
        propertyDailyProcessing.setPropertyTimeZone(propertyTimeZone);
        return this;
    }

    private DailyProcessingServiceMockTest assertExtractStatus(InputProcessing inputProcessing) {
        assertEquals(ExtractStatus.COMPLETE, inputProcessing.getExtractStatus());
        return this;
    }

    @Test
    public void testUpdateDecisionDeliveryByTypeUploadStartedDate_alreadySet_withDecisionType() {
        DecisionDelivery decisionDelivery = createDecisionDelivery("IN_PROGRESS", IN_PROGRESS, IN_PROGRESS);
        LocalDateTime startedDateTime = LocalDateTime.now().minusMinutes(5);
        decisionDelivery.setStartedDate(startedDateTime);
        DecisionDeliveryByType decisionDeliveryByType1 = decisionDelivery.getDecisionDeliveryByType(DECISION_TYPE_1);
        decisionDeliveryByType1.setStartedDateTime(startedDateTime);
        dailyProcessingService.updateDecisionDeliveryUploadStartedDate(INPUT_PROCESSING_ID, DESTINATION_ID, DECISION_TYPE_1);
        assertThat(decisionDelivery.getStartedDate(), is(startedDateTime));
        assertThat(decisionDeliveryByType1.getStartedDateTime(), is(startedDateTime));
    }

    @Test
    public void isAllDecisionDeliveryByTypeComplete_InProgress() {
        createDecisionDelivery("IN_PROGRESS", IN_PROGRESS, DELIVERED);
        assertFalse(dailyProcessingService.isAllDecisionDeliveryByTypeComplete(INPUT_PROCESSING_ID, DESTINATION_ID, null));
        verify(crudService).find(InputProcessing.class, INPUT_PROCESSING_ID);
    }

    @Test
    void isAllDecisionDeliveryByTypeComplete_AllDelivered() {
        createDecisionDelivery("IN_PROGRESS", DELIVERED, DELIVERED);
        assertTrue(dailyProcessingService.isAllDecisionDeliveryByTypeComplete(INPUT_PROCESSING_ID, DESTINATION_ID, null));
        verify(crudService).find(InputProcessing.class, INPUT_PROCESSING_ID);
    }

    @Test
    void isAllDecisionDeliveryByTypeComplete_InputProcessingIdNull() {
        assertFalse(dailyProcessingService.isAllDecisionDeliveryByTypeComplete(null, "PCRS", "123"));
    }

    @Test
    void updateDecisionDeliveryByTypeUploadStartDate() {
        DecisionDelivery decisionDelivery = createDecisionDelivery("IN_PROGRESS", IN_PROGRESS, IN_PROGRESS);
        LocalDateTime dateTime = LocalDateTime.now();
        decisionDelivery.getDecisionDeliveryByType(DECISION_TYPE_2).setStartedDateTime(dateTime);
        dailyProcessingService.updateDecisionDeliveryByTypeUploadStartDate(DECISION_TYPE_2, INPUT_PROCESSING_ID, DESTINATION_ID);
        assertEquals(dateTime, decisionDelivery.getDecisionDeliveryByType(DECISION_TYPE_2).getStartedDateTime());
    }

    @Test
    void addDecisionDeliveryByType() {
        DecisionDelivery decisionDelivery = new DecisionDelivery();
        decisionDelivery.setDestinationId(DESTINATION_ID);
        decisionDelivery.setResult("IN_PROGRESS");
        InputProcessing inputProcessing = new InputProcessing();
        inputProcessing.setId(INPUT_PROCESSING_ID);
        inputProcessing.addDecisionDelivery(decisionDelivery);
        PropertyDailyProcessing pdp = new PropertyDailyProcessing();
        pdp.setPropertyTimeZone(TIME_ZONE);
        pdp.addInputProcessing(inputProcessing);
        when(crudService.find(InputProcessing.class, INPUT_PROCESSING_ID)).thenReturn(inputProcessing);
        dailyProcessingService.addDecisionDeliveryByType(INPUT_PROCESSING_ID, DESTINATION_ID, null, List.of(DECISION_TYPE_1, DECISION_TYPE_2));
        assertEquals(0, decisionDelivery.getDecisionDeliveryByType(DECISION_TYPE_1).getExtraDeliveryCount());
        assertEquals(0, decisionDelivery.getDecisionDeliveryByType(DECISION_TYPE_2).getExtraDeliveryCount());
        assertEquals(IN_PROGRESS, decisionDelivery.getDecisionDeliveryByType(DECISION_TYPE_1).getStatus());
        assertEquals(IN_PROGRESS, decisionDelivery.getDecisionDeliveryByType(DECISION_TYPE_2).getStatus());
    }

    @Test
    void addDecisionDeliveryByType_AlreadyExists() {
        DecisionDelivery decisionDelivery = createDecisionDelivery("IN_PROGRESS", IN_PROGRESS, DELIVERED);
        dailyProcessingService.addDecisionDeliveryByType(INPUT_PROCESSING_ID, DESTINATION_ID, null, List.of(DECISION_TYPE_1, DECISION_TYPE_2));
        assertEquals(0, decisionDelivery.getDecisionDeliveryByType(DECISION_TYPE_1).getExtraDeliveryCount());
        assertEquals(1, decisionDelivery.getDecisionDeliveryByType(DECISION_TYPE_2).getExtraDeliveryCount());
    }
}
