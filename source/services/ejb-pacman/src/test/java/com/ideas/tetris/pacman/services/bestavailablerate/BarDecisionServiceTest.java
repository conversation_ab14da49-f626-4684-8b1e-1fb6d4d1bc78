package com.ideas.tetris.pacman.services.bestavailablerate;

import com.ideas.g3.data.TestProperty;
import com.ideas.g3.test.AbstractG3JupiterTest;
import com.ideas.tetris.pacman.common.configparams.ConfigParamName;
import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.configparams.IPConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.accommodation.dto.AccomTypeSummary;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomClass;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomType;
import com.ideas.tetris.pacman.services.accommodation.entity.UniqueAccomClassCreator;
import com.ideas.tetris.pacman.services.accommodation.entity.UniqueAccomTypeCreator;
import com.ideas.tetris.pacman.services.bestavailablerate.dto.BARDecisionInfo;
import com.ideas.tetris.pacman.services.bestavailablerate.dto.BARDetails;
import com.ideas.tetris.pacman.services.bestavailablerate.dto.BARFilterCriteria;
import com.ideas.tetris.pacman.services.bestavailablerate.dto.BAROvrUnavailableDtRangeAndPriceDetails;
import com.ideas.tetris.pacman.services.bestavailablerate.dto.CompetitorInfo;
import com.ideas.tetris.pacman.services.bestavailablerate.dto.LRAAffectedDateRange;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.Decision;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.DecisionBAROutput;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.DecisionBAROutputOverride;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.dateservice.dto.DateParameter;
import com.ideas.tetris.pacman.services.demandoverride.entity.OccupancyDemandOverride;
import com.ideas.tetris.pacman.services.product.Product;
import com.ideas.tetris.pacman.services.tax.entity.Tax;
import com.ideas.tetris.pacman.services.unqualifiedrate.entity.RateUnqualified;
import com.ideas.tetris.pacman.services.unqualifiedrate.entity.RateUnqualifiedDetails;
import com.ideas.tetris.pacman.services.unqualifiedrate.entity.UniqueRateUnqualified;
import com.ideas.tetris.pacman.services.unqualifiedrate.entity.UniqueRateUnqualifiedDetails;
import com.ideas.tetris.pacman.services.unqualifiedrate.entity.UniqueUnqualifiedDemandForecastPrice;
import com.ideas.tetris.pacman.services.webrate.entity.UniqueWebRateCreator;
import com.ideas.tetris.pacman.services.webrate.entity.UniqueWebrateCompetitorsAccomClassCreator;
import com.ideas.tetris.pacman.services.webrate.entity.UniqueWebrateCompetitorsCreator;
import com.ideas.tetris.pacman.services.webrate.entity.Webrate;
import com.ideas.tetris.pacman.services.webrate.entity.WebrateCompetitors;
import com.ideas.tetris.pacman.services.webrate.entity.WebrateCompetitorsAccomClass;
import com.ideas.tetris.pacman.testdatabuilder.ProductBuilder;
import com.ideas.tetris.platform.common.contextholder.PacmanThreadLocalContextHolder;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.CrudServiceBean;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.common.xml.schema.workcontext.v1.WorkContextType;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.joda.time.LocalDate;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.testng.Assert;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.Set;
import java.util.TimeZone;

import static com.ideas.tetris.pacman.common.constants.Constants.BARDECISIONOVERRIDE_CEILING;
import static com.ideas.tetris.pacman.common.constants.Constants.BARDECISIONOVERRIDE_PENDING;
import static com.ideas.tetris.pacman.common.constants.Constants.BARDECISIONOVERRIDE_USER;
import static com.ideas.tetris.pacman.common.constants.Constants.BAR_DECISION_VALUE_LOS;
import static com.ideas.tetris.pacman.common.constants.Constants.BAR_DECISION_VALUE_RATEOFDAY;
import static com.ideas.tetris.pacman.common.constants.Constants.BAR_OVRD_DISPLAY_COMPETITOR_VALUE_ABSOLUTE;
import static com.ideas.tetris.pacman.common.constants.Constants.BAR_OVRD_DISPLAY_COMPETITOR_VALUE_HIGH;
import static com.ideas.tetris.pacman.common.constants.Constants.BAR_OVRD_DISPLAY_COMPETITOR_VALUE_LOW;
import static com.ideas.tetris.pacman.common.constants.Constants.BAR_OVRD_DISPLAY_COMPETITOR_VALUE_MEDIAN;
import static com.ideas.tetris.pacman.common.constants.Constants.CONTEXT_KEY;
import static com.ideas.tetris.pacman.common.constants.Constants.END_DATE;
import static com.ideas.tetris.pacman.common.constants.Constants.START_DATE;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNotSame;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@MockitoSettings(strictness = Strictness.LENIENT)
public class BarDecisionServiceTest extends AbstractG3JupiterTest {
    private static final String CLIENT_CODE = "BSTN";
    private static final int PROPERTY_ID_RATEOFDAY = 5;
    private static final int PROPERTY_ID_BAR_BY_LOS = 6;
    private static final int BAR_PRODUCT_ID = 1;
    private static final int ACCOM_CLASS_ID_RATE_OF_DAY = 2;
    private static final int ACCOM_CLASS_ID_BAR_BY_LOS = 6;

    private BarDecisionService service = new BarDecisionService();
    private DateService dateService;
    private SQLHelper sqlHelper;
    private RateDeterminator rateDeterminator;
    private PriceService priceService;
    private PacmanConfigParamsService configService;
    private CloseHighestBarService mockedCloseHighestBarService;

    @BeforeEach
    public void setUp() {
        dateService = new DateService() {
            @Override
            public Date getCaughtUpDate() {
                return DateUtil.getCurrentDate();
            }

            @Override
            public Date getBusinessDate() {
                return DateUtil.getFirstDayOfCurrentMonth();
            }

            @Override
            public Date getWebRateShoppingDate() {
                return DateUtil.getFirstDayOfCurrentMonth();
            }

            @Override
            public Date getUnqualifiedRateCaughtUpDate() {
                return DateUtil.getFirstDayOfCurrentMonth();
            }

            @Override
            public Date getBARDisplayWindowEndDate() {
                return DateUtil.getLastDayOfNextMonth();
            }

            @Override
            public Date getBARDisplayWindowStartDate() {
                return DateUtil.getFirstDayOfCurrentMonth();
            }
        };

        configService = spy(new PacmanConfigParamsService());
        doReturn(BAR_DECISION_VALUE_RATEOFDAY).when(configService).getParameterValue(IPConfigParamName.BAR_BAR_DECISION.value());
        doReturn("false").when(configService).getParameterValue(IPConfigParamName.BAR_ENABLE_SINGLE_BAR_DECISION.value());
        doReturn(false).when(configService).getBooleanParameterValue(FeatureTogglesConfigParamName.CEILING_OVERRIDE_ENABLED.value());

        sqlHelper = new SQLHelper();
        sqlHelper.setDateService(dateService);
        sqlHelper.setCrudService(tenantCrudService());
        sqlHelper.setConfigService(configService);

        rateDeterminator = new RateDeterminator();
        rateDeterminator.setCrudService(tenantCrudService());

        priceService = new PriceService();
        priceService.setCrudService(tenantCrudService());
        priceService.setConfigService(configService);
        priceService.setDateService(dateService);
        priceService.setRateDeterminator(rateDeterminator);

        service.setCrudService(tenantCrudService());
        service.setConfigService(configService);
        service.setSQLHelper(sqlHelper);
        service.setPriceService(priceService);
        service.setDateService(dateService);
        WorkContextType wc = new WorkContextType();
        wc.setUserId("1");
        wc.setPropertyId(PROPERTY_ID_RATEOFDAY);
        wc.setClientCode(CLIENT_CODE);
        wc.setClientId(2);
        PacmanThreadLocalContextHolder.put(CONTEXT_KEY, wc);

        doReturn(true).when(configService).getBooleanParameterValue(FeatureTogglesConfigParamName.HIGHEST_BAR_RESTRICTED_ENABLED.value());
        mockedCloseHighestBarService = mock(CloseHighestBarService.class);
        inject(service, "closeHighestBarService", mockedCloseHighestBarService);
    }

    private void switchTenant() {
        setWorkContextProperty(TestProperty.H2);
        sqlHelper.setCrudService(tenantCrudService());
        service.setCrudService(tenantCrudService());
        rateDeterminator.setCrudService(tenantCrudService());
        priceService.setCrudService(tenantCrudService());
    }

    @Test
    public void testGetBARDetails() throws Exception {
        BARDetails result = service.getBARDetails(ACCOM_CLASS_ID_RATE_OF_DAY,
                DateUtil.getFirstDayOfCurrentMonth());
        assertNotNull(result);

        Map<String, Map<String, BigDecimal>> priceMapByRateUnqualifiedName = result.getPriceMapByRateUnqualifiedName();

        assertEquals(9, priceMapByRateUnqualifiedName.size());
        Map<String, BigDecimal> remainingDemandByRateUnqualifiedName = result.getRemainingDemandByRateUnqualifiedName();
        assertEquals(9, remainingDemandByRateUnqualifiedName.size());

        assertEquals("{DOUBLE=199.00000}", priceMapByRateUnqualifiedName.get("LV0").toString());
        assertEquals("{DOUBLE=189.00000}", priceMapByRateUnqualifiedName.get("LV1").toString());
        assertEquals("{DOUBLE=179.00000}", priceMapByRateUnqualifiedName.get("LV2").toString());
        assertEquals("{DOUBLE=169.00000}", priceMapByRateUnqualifiedName.get("LV3").toString());
        assertEquals("{DOUBLE=159.00000}", priceMapByRateUnqualifiedName.get("LV4").toString());
        assertEquals("{DOUBLE=149.00000}", priceMapByRateUnqualifiedName.get("LV5").toString());
        assertEquals("{DOUBLE=139.00000}", priceMapByRateUnqualifiedName.get("LV6").toString());
        assertEquals("{DOUBLE=129.00000}", priceMapByRateUnqualifiedName.get("LV7").toString());
        assertEquals("{DOUBLE=119.00000}", priceMapByRateUnqualifiedName.get("LV8").toString());

        assertEquals("109.00", remainingDemandByRateUnqualifiedName.get("LV0").toString());
        assertEquals("111.00", remainingDemandByRateUnqualifiedName.get("LV1").toString());
        assertEquals("113.00", remainingDemandByRateUnqualifiedName.get("LV2").toString());
        assertEquals("115.00", remainingDemandByRateUnqualifiedName.get("LV3").toString());
        assertEquals("117.00", remainingDemandByRateUnqualifiedName.get("LV4").toString());
        assertEquals("119.00", remainingDemandByRateUnqualifiedName.get("LV5").toString());
        assertEquals("121.00", remainingDemandByRateUnqualifiedName.get("LV6").toString());
        assertEquals("123.00", remainingDemandByRateUnqualifiedName.get("LV7").toString());
        assertEquals("125.00", remainingDemandByRateUnqualifiedName.get("LV8").toString());

    }

    @Test
    public void testGetBARDetailsWhenCloseLV0EnabledButNotSet() throws Exception {
        switchTenant();
        when(mockedCloseHighestBarService.isClosedForHighestBar(ACCOM_CLASS_ID_BAR_BY_LOS, DateUtil.getFirstDayOfCurrentMonth(), DateUtil.getFirstDayOfCurrentMonth(), 1)).thenReturn(false);
        BARDetails result = service.getBARDetails(ACCOM_CLASS_ID_BAR_BY_LOS,
                DateUtil.getFirstDayOfCurrentMonth(), 1);
        assertNotNull(result);
        assertFalse(result.getRestrictHighestBARSelected());
    }

    @Test
    public void testGetBARDetailsWhenCloseLV0EnabledAndSet() throws Exception {
        switchTenant();
        when(mockedCloseHighestBarService.isClosedForHighestBar(ACCOM_CLASS_ID_BAR_BY_LOS, DateUtil.getFirstDayOfCurrentMonth(), DateUtil.getFirstDayOfCurrentMonth(), 1)).thenReturn(true);
        BARDetails result = service.getBARDetails(ACCOM_CLASS_ID_BAR_BY_LOS,
                DateUtil.getFirstDayOfCurrentMonth(), 1);
        assertNotNull(result);
        assertTrue(result.getRestrictHighestBARSelected());
    }

    @Test
    public void testGetBarDecisionsByArrivalDateAndLengthOfStayUsingMaxLOS_WhenCloseLV0EnabledButNotSet() {
        switchTenant();

        PacmanConfigParamsService mockConfigService = mock(PacmanConfigParamsService.class);
        when(mockConfigService.getBooleanParameterValue(FeatureTogglesConfigParamName.CEILING_OVERRIDE_ENABLED.value())).thenReturn(false);

        service.setConfigService(mockConfigService);

        when(mockedCloseHighestBarService.isClosedForHighestBar(ACCOM_CLASS_ID_BAR_BY_LOS, DateUtil.getFirstDayOfCurrentMonth(), DateUtil.getFirstDayOfCurrentMonth(), 1)).thenReturn(false);
        Map<Date, Map<Integer, BARDecisionInfo>> result = service.getBarDecisionsByArrivalDateAndLengthOfStayUsingMaxLOS(ACCOM_CLASS_ID_BAR_BY_LOS,
                1, DateUtil.getFirstDayOfCurrentMonth(), DateUtil.getFirstDayOfCurrentMonth());
        assertNotNull(result);
        assertFalse((result.get(DateUtil.getFirstDayOfCurrentMonth()).get(1)).isRestrictHighestBarOverride());
    }

    @Test
    public void testGetBarDecisionsByArrivalDateAndLengthOfStayUsingMaxLOS_WhenCloseLV0EnabledAndSet() {
        switchTenant();

        PacmanConfigParamsService mockConfigService = mock(PacmanConfigParamsService.class);
        when(mockConfigService.getBooleanParameterValue(FeatureTogglesConfigParamName.CEILING_OVERRIDE_ENABLED.value())).thenReturn(false);
        when(mockConfigService.getBooleanParameterValue(FeatureTogglesConfigParamName.CEILING_OVERRIDE_ENABLED)).thenReturn(false);
        when(mockConfigService.getBooleanParameterValue(FeatureTogglesConfigParamName.HIGHEST_BAR_RESTRICTED_ENABLED.value())).thenReturn(Boolean.TRUE);

        service.setConfigService(mockConfigService);

        final Date arrivalDate = DateUtil.getFirstDayOfCurrentMonth();
        when(mockedCloseHighestBarService.isClosedForHighestBar(ACCOM_CLASS_ID_BAR_BY_LOS, arrivalDate, arrivalDate, 1)).thenReturn(true);

        Map<Integer, List<AccomTypeSummary>> valueMap = new HashMap<>();
        valueMap.put(1, Arrays.asList(new AccomTypeSummary(1, "DLX", true)));
        Map<Date, Map<Integer, List<AccomTypeSummary>>> closedRoomTypes = new HashMap<>();
        closedRoomTypes.put(arrivalDate, valueMap);

        when(mockedCloseHighestBarService.getRoomTypesForMaxLOS(ACCOM_CLASS_ID_BAR_BY_LOS, 1, arrivalDate, arrivalDate)).thenReturn(closedRoomTypes);
        Map<Date, Map<Integer, BARDecisionInfo>> result = service.getBarDecisionsByArrivalDateAndLengthOfStayUsingMaxLOS(ACCOM_CLASS_ID_BAR_BY_LOS,
                1, arrivalDate, arrivalDate);

        assertNotNull(result);
        assertTrue((result.get(DateUtil.getFirstDayOfCurrentMonth()).get(1)).isRestrictHighestBarOverride());

    }

    @Test
    public void testGetBarDecisionsCalendarView() throws Exception {
        Date startDate = dateService.getBARDisplayWindowStartDate();
        Date endDate = dateService.getBARDisplayWindowEndDate();

        PacmanConfigParamsService mockConfigService = mock(PacmanConfigParamsService.class);
        when(mockConfigService.getBooleanParameterValue(FeatureTogglesConfigParamName.CEILING_OVERRIDE_ENABLED.value())).thenReturn(false);

        service.setConfigService(mockConfigService);

        Map<Date, BARDecisionInfo> result = service.getBarDecisions(
                ACCOM_CLASS_ID_RATE_OF_DAY, -1, startDate, endDate);
        assertNotNull(result);

        assertEquals(DateUtil.getNumberOfDaysInThisMonthAndNext(), result.size());

        List<Date> keys = new ArrayList<Date>(result.keySet());
        Collections.sort(keys);

        // Check the first few results to see if they match known expected
        // values
        assertEquals("LV2", result.get(keys.get(0)).getRatePlanName());
        assertEquals("LV3", result.get(keys.get(1)).getRatePlanName());
        assertEquals("LV5", result.get(keys.get(2)).getRatePlanName());
        assertEquals("LV6", result.get(keys.get(3)).getRatePlanName());
    }

    @Test
    public void testGetBarDecisionsWithCloseHighestBarForCalendarView() throws Exception {
        PacmanConfigParamsService mockConfigService = mock(PacmanConfigParamsService.class);
        when(mockConfigService.getBooleanParameterValue(FeatureTogglesConfigParamName.HIGHEST_BAR_RESTRICTED_ENABLED.value())).thenReturn(true);

        service.setConfigService(mockConfigService);

        Date startDate = dateService.getBARDisplayWindowStartDate();
        Date endDate = dateService.getBARDisplayWindowEndDate();

        final Date date = LocalDate.fromDateFields(startDate).plusDays(1).toDate();
        Map<Date, List<AccomTypeSummary>> roomTypesByArrivalDate = new HashMap<>();
        roomTypesByArrivalDate.put(date, new ArrayList<>());
        when(mockedCloseHighestBarService.getSelectedAccomTypes(ACCOM_CLASS_ID_RATE_OF_DAY, -1,
                startDate, endDate)).thenReturn(roomTypesByArrivalDate);

        Map<Date, BARDecisionInfo> result = service.getBarDecisions(
                ACCOM_CLASS_ID_RATE_OF_DAY, -1, startDate, endDate);
        assertNotNull(result);

        assertEquals(DateUtil.getNumberOfDaysInThisMonthAndNext(), result.size());
        assertNotNull(result.get(date));
        assertTrue(result.get(date).isRestrictHighestBarOverride(), "Restrict Highest BAR should be enabled");
    }

    @Test
    public void testGetBarDecisionsTabularView() throws Exception {
        switchTenant();

        PacmanConfigParamsService mockConfigService = Mockito.mock(PacmanConfigParamsService.class);
        when(mockConfigService.isEnablePhysicalCapacityConsideration()).thenReturn(false);
        when(mockConfigService.getParameterValue(IPConfigParamName.BAR_BAR_DECISION.value())).thenReturn(BAR_DECISION_VALUE_LOS);

        service.setConfigService(mockConfigService);
        PacmanWorkContextHelper.setPropertyId(PROPERTY_ID_BAR_BY_LOS);

        BARFilterCriteria criteria = new BARFilterCriteria();
        criteria.setAccomClassId(ACCOM_CLASS_ID_BAR_BY_LOS);
        criteria.setIncludedDaysOfWeek(Arrays.asList(new Integer[]{1, 2, 3,
                4, 5, 6, 7}));
        criteria.setIncludedLengthsOfStay(Arrays.asList(new Integer[]{1, 2,
                3, 4, 5, 6, 7, 8}));

        criteria.setStartDate(dateService.getBARDisplayWindowStartDate());
        criteria.setEndDate(dateService.getBARDisplayWindowEndDate());

        Map<Date, Map<Integer, BARDecisionInfo>> result = service
                .getBarDecisions(criteria);
        assertNotNull(result);

        assertEquals(DateUtil.getNumberOfDaysInThisMonthAndNext(), result.size());

        List<Date> keys = new ArrayList<Date>(result.keySet());
        Collections.sort(keys);

        assertEquals("BAR5", result.get(keys.get(0)).get(1).getRatePlanName());
        assertEquals("BAR1", result.get(keys.get(0)).get(2).getRatePlanName());
        assertEquals("BAR1", result.get(keys.get(0)).get(3).getRatePlanName());
        assertEquals("BAR2", result.get(keys.get(0)).get(4).getRatePlanName());

        assertEquals("BAR1", result.get(keys.get(1)).get(1).getRatePlanName());
        assertEquals("BAR2", result.get(keys.get(1)).get(2).getRatePlanName());
        assertEquals("BAR3", result.get(keys.get(1)).get(3).getRatePlanName());
        assertEquals("BAR4", result.get(keys.get(1)).get(4).getRatePlanName());

    }

    @Test
    public void testGetBarDecisionsWithCloseHighestBarForTabularView() throws Exception {
        switchTenant();

        PacmanConfigParamsService mockConfigService = Mockito.mock(PacmanConfigParamsService.class);
        when(mockConfigService.isEnablePhysicalCapacityConsideration()).thenReturn(false);
        when(mockConfigService.getParameterValue(IPConfigParamName.BAR_BAR_DECISION.value())).thenReturn(BAR_DECISION_VALUE_LOS);
        when(mockConfigService.getBooleanParameterValue(FeatureTogglesConfigParamName.HIGHEST_BAR_RESTRICTED_ENABLED.value())).thenReturn(true);

        service.setConfigService(mockConfigService);
        PacmanWorkContextHelper.setPropertyId(PROPERTY_ID_BAR_BY_LOS);

        BARFilterCriteria criteria = new BARFilterCriteria();
        criteria.setAccomClassId(ACCOM_CLASS_ID_BAR_BY_LOS);
        criteria.setIncludedDaysOfWeek(Arrays.asList(new Integer[]{1, 2, 3,
                4, 5, 6, 7}));
        criteria.setIncludedLengthsOfStay(Arrays.asList(new Integer[]{1, 2,
                3, 4, 5, 6, 7, 8}));

        criteria.setStartDate(dateService.getBARDisplayWindowStartDate());
        criteria.setEndDate(dateService.getBARDisplayWindowEndDate());

        final Date date = LocalDate.fromDateFields(criteria.getStartDate()).plusDays(1).toDate();
        when(mockedCloseHighestBarService.isClosedForHighestBar(ACCOM_CLASS_ID_BAR_BY_LOS, date, date, 1)).thenReturn(true);

        Map<Date, Map<Integer, BARDecisionInfo>> result = service
                .getBarDecisions(criteria);
        assertNotNull(result);

        assertEquals(DateUtil.getNumberOfDaysInThisMonthAndNext(), result.size());

        assertNotNull(result.get(date).get(1));
        assertTrue(result.get(date).get(1).isRestrictHighestBarOverride(), "Restrict Highest BAR should be enabled");
    }

    @Test
    public void testGetEarlierBarDecisionsCalendarView() throws Exception {
        Date paceDate = DateUtil.getDateForCurrentMonth(25);
        Map<Date, BARDecisionInfo> result = service.getEarlierBarDecisions(
                paceDate, ACCOM_CLASS_ID_RATE_OF_DAY, -1, dateService
                        .getBARDisplayWindowStartDate(), dateService
                        .getBARDisplayWindowEndDate());
        assertNotNull(result);

        assertEquals(DateUtil.getNumberOfDaysInThisMonthAndNext(), result.size());

        List<Date> keys = new ArrayList<Date>(result.keySet());
        Collections.sort(keys);

        // Check the first few results to see if they match known expected
        // values
        assertEquals("LV3", result.get(keys.get(1)).getRatePlanName());
        assertEquals("LV5", result.get(keys.get(2)).getRatePlanName());
        assertEquals("LV6", result.get(keys.get(3)).getRatePlanName());
    }

    @Test
    public void testGetEarlierBarDecisionsTabularView() throws Exception {
        switchTenant();

        PacmanConfigParamsService mockConfigService = Mockito.mock(PacmanConfigParamsService.class);
        when(mockConfigService.isEnablePhysicalCapacityConsideration()).thenReturn(false);
        when(mockConfigService.getParameterValue(IPConfigParamName.BAR_BAR_DECISION.value())).thenReturn(BAR_DECISION_VALUE_LOS);
        when(mockConfigService.getParameterValue(IPConfigParamName.BAR_WEB_RATE_SHOPPING_ENABLED.value())).thenReturn("true");
        when(mockConfigService.getParameterValue(IPConfigParamName.BAR_WEB_RATE_STALENESS_THRESHOLD.value())).thenReturn("365");
        when(mockConfigService.getParameterValue(IPConfigParamName.BAR_BAR_OVRD_DISPLAY_COMPETITOR.value())).thenReturn(BAR_OVRD_DISPLAY_COMPETITOR_VALUE_ABSOLUTE);

        service.setConfigService(mockConfigService);
        PacmanWorkContextHelper.setPropertyId(PROPERTY_ID_BAR_BY_LOS);

        BARFilterCriteria criteria = new BARFilterCriteria();
        criteria.setAccomClassId(ACCOM_CLASS_ID_BAR_BY_LOS);
        criteria.setIncludedDaysOfWeek(Arrays.asList(1, 2, 3, 4, 5, 6, 7));
        criteria.setIncludedLengthsOfStay(Arrays.asList(1, 2, 3, 4, 5, 6, 7, 8));

        criteria.setStartDate(dateService.getBARDisplayWindowStartDate());
        criteria.setEndDate(dateService.getBARDisplayWindowEndDate());
        Date paceDate = DateUtil.getFirstDayOfCurrentMonth();
        criteria.setLastViewedTime(paceDate);

        Map<Date, Map<Integer, BARDecisionInfo>> result = service
                .getEarlierBarDecisions(criteria);
        assertNotNull(result);

        assertEquals(DateUtil.getNumberOfDaysInThisMonthAndNext(), result.size());

        List<Date> keys = new ArrayList<Date>(result.keySet());
        Collections.sort(keys);

        // Check the first few results to see if they match known expected
        // values
        assertTrue((result.get(keys.get(0)).get(1).getRatePlanName()).startsWith("BAR"));
        assertTrue((result.get(keys.get(0)).get(2).getRatePlanName()).startsWith("BAR"));
        assertTrue((result.get(keys.get(0)).get(3).getRatePlanName()).startsWith("BAR"));
        assertTrue((result.get(keys.get(0)).get(4).getRatePlanName()).startsWith("BAR"));

        assertTrue((result.get(keys.get(1)).get(1).getRatePlanName()).startsWith("BAR"));
        assertTrue((result.get(keys.get(1)).get(2).getRatePlanName()).startsWith("BAR"));
        assertTrue((result.get(keys.get(1)).get(3).getRatePlanName()).startsWith("BAR"));
        assertTrue((result.get(keys.get(1)).get(4).getRatePlanName()).startsWith("BAR"));
    }

    @Test
    public void testGetCompetitorInfoCalendarView_MaxPrice() throws Exception {
        PacmanConfigParamsService mockConfigService = Mockito.mock(PacmanConfigParamsService.class);
        when(mockConfigService.isEnablePhysicalCapacityConsideration()).thenReturn(false);
        when(mockConfigService.getParameterValue(IPConfigParamName.BAR_WEB_RATE_SHOPPING_ENABLED.value())).thenReturn("true");
        when(mockConfigService.getParameterValue(IPConfigParamName.BAR_BAR_OVRD_DISPLAY_COMPETITOR.value())).thenReturn(BAR_OVRD_DISPLAY_COMPETITOR_VALUE_HIGH);

        service.setConfigService(mockConfigService);
        sqlHelper.setConfigService(mockConfigService);

        Map<Date, CompetitorInfo> result = service.getCompetitorInfo(
                ACCOM_CLASS_ID_RATE_OF_DAY, -1, dateService
                        .getBARDisplayWindowStartDate(), dateService
                        .getBARDisplayWindowEndDate());
        assertNotNull(result);

        assertEquals(DateUtil.getNumberOfDaysInThisMonthAndNext(), result.size());

        List<Date> keys = new ArrayList<Date>(result.keySet());
        Collections.sort(keys);

        // Check the first few results to see if they match known expected
        // values
        assertTrue(result.get(keys.get(0)).getCompetitorNames().contains(
                "Luxor"));
        assertTrue(result.get(keys.get(1)).getCompetitorNames().contains(
                "Santa Barbara Inn"));
        assertTrue(result.get(keys.get(2)).getCompetitorNames().contains(
                "Santa Barbara Inn"));
        assertEquals("190.00", result.get(keys.get(0)).getCompetitorPrice());
        assertEquals("160.00", result.get(keys.get(1)).getCompetitorPrice());
        assertEquals("190.00", result.get(keys.get(2)).getCompetitorPrice());
    }

    @Test
    public void testGetCompetitorInfoMapCalendarView_MaxPrice() throws Exception {
        PacmanConfigParamsService mockConfigService = Mockito.mock(PacmanConfigParamsService.class);
        when(mockConfigService.isEnablePhysicalCapacityConsideration()).thenReturn(false);
        when(mockConfigService.getParameterValue(IPConfigParamName.BAR_WEB_RATE_SHOPPING_ENABLED.value())).thenReturn("true");
        when(mockConfigService.getParameterValue(IPConfigParamName.BAR_BAR_OVRD_DISPLAY_COMPETITOR.value())).thenReturn(BAR_OVRD_DISPLAY_COMPETITOR_VALUE_HIGH);

        service.setConfigService(mockConfigService);
        sqlHelper.setConfigService(mockConfigService);

        Map<Integer, Map<Date, CompetitorInfo>> result = service.getCompetitorInfoMap(-1, dateService
                .getBARDisplayWindowStartDate(), dateService
                .getBARDisplayWindowEndDate());
        assertNotNull(result);

        Map<Date, CompetitorInfo> dateCompetitorInfoMap = result.get(ACCOM_CLASS_ID_RATE_OF_DAY);
        assertEquals(DateUtil.getNumberOfDaysInThisMonthAndNext(), dateCompetitorInfoMap.size());

        List<Date> keys = new ArrayList<Date>(dateCompetitorInfoMap.keySet());
        Collections.sort(keys);

        // Check the first few results to see if they match known expected
        // values
        assertTrue(dateCompetitorInfoMap.get(keys.get(0)).getCompetitorNames().contains(
                "Luxor"));
        assertTrue(dateCompetitorInfoMap.get(keys.get(1)).getCompetitorNames().contains(
                "Santa Barbara Inn"));
        assertTrue(dateCompetitorInfoMap.get(keys.get(2)).getCompetitorNames().contains(
                "Santa Barbara Inn"));
        assertEquals("190.00", dateCompetitorInfoMap.get(keys.get(0)).getCompetitorPrice());
        assertEquals("160.00", dateCompetitorInfoMap.get(keys.get(1)).getCompetitorPrice());
        assertEquals("190.00", dateCompetitorInfoMap.get(keys.get(2)).getCompetitorPrice());
    }

    @Test
    public void testGetCompetitorInfoMapDayCardLayout() throws Exception {
        //Basic BAR Test for competitors as we do not have any seeded data for Independent Product Competitors yet
        PacmanConfigParamsService mockConfigService = Mockito.mock(PacmanConfigParamsService.class);
        when(mockConfigService.isEnablePhysicalCapacityConsideration()).thenReturn(false);
        when(mockConfigService.getParameterValue(IPConfigParamName.BAR_WEB_RATE_SHOPPING_ENABLED.value())).thenReturn("true");
        when(mockConfigService.getParameterValue(IPConfigParamName.BAR_BAR_OVRD_DISPLAY_COMPETITOR.value())).thenReturn(BAR_OVRD_DISPLAY_COMPETITOR_VALUE_HIGH);

        service.setConfigService(mockConfigService);
        sqlHelper.setConfigService(mockConfigService);

        Map<Integer, Map<Date, Map<Integer, CompetitorInfo>>> result = service.getCompetitorInfoMapDayCardLayout(-1,
                dateService.getBARDisplayWindowStartDate(),
                dateService.getBARDisplayWindowEndDate());
        assertNotNull(result);

        Map<Date, Map<Integer, CompetitorInfo>> accomClassMap = result.get(ACCOM_CLASS_ID_RATE_OF_DAY);

        assertEquals(DateUtil.getNumberOfDaysInThisMonthAndNext(), accomClassMap.size());

        List<Date> keys = new ArrayList<Date>(accomClassMap.keySet());
        Collections.sort(keys);

        // Check the first few results to see if they match known expected
        // values
        assertTrue(accomClassMap.get(keys.get(0)).get(BAR_PRODUCT_ID).getCompetitorNames().contains(
                "Luxor"));
        assertTrue(accomClassMap.get(keys.get(1)).get(BAR_PRODUCT_ID).getCompetitorNames().contains(
                "Santa Barbara Inn"));
        assertTrue(accomClassMap.get(keys.get(2)).get(BAR_PRODUCT_ID).getCompetitorNames().contains(
                "Santa Barbara Inn"));
        assertEquals("190.00", accomClassMap.get(keys.get(0)).get(BAR_PRODUCT_ID).getCompetitorPrice());
        assertEquals("160.00", accomClassMap.get(keys.get(1)).get(BAR_PRODUCT_ID).getCompetitorPrice());
        assertEquals("190.00", accomClassMap.get(keys.get(2)).get(BAR_PRODUCT_ID).getCompetitorPrice());
    }

    @Test
    public void testGetCompetitorInfoMapDayCardLayout_IndependentProducts() throws Exception {
        //Basic BAR Test for competitors as we do not have any seeded data for Independent Product Competitors yet
        PacmanConfigParamsService mockConfigService = Mockito.mock(PacmanConfigParamsService.class);
        when(mockConfigService.isEnablePhysicalCapacityConsideration()).thenReturn(false);
        when(mockConfigService.getParameterValue(IPConfigParamName.BAR_WEB_RATE_SHOPPING_ENABLED.value())).thenReturn("true");
        when(mockConfigService.getParameterValue(IPConfigParamName.BAR_BAR_OVRD_DISPLAY_COMPETITOR.value())).thenReturn(BAR_OVRD_DISPLAY_COMPETITOR_VALUE_HIGH);

        service.setConfigService(mockConfigService);
        sqlHelper.setConfigService(mockConfigService);

        Set<Integer> lengthsOfStay = new HashSet<>();
        lengthsOfStay.add(1);

        Map<Integer, Map<Date, Map<Integer, CompetitorInfo>>> result = service.getCompetitorInfoMapDayCardLayout(lengthsOfStay,
                dateService.getBARDisplayWindowStartDate(),
                dateService.getBARDisplayWindowEndDate());
        assertNotNull(result);

        Map<Date, Map<Integer, CompetitorInfo>> accomClassMap = result.get(ACCOM_CLASS_ID_RATE_OF_DAY);

        assertEquals(DateUtil.getNumberOfDaysInThisMonthAndNext(), accomClassMap.size());

        List<Date> keys = new ArrayList<Date>(accomClassMap.keySet());
        Collections.sort(keys);

        // Check the first few results to see if they match known expected
        // values
        assertTrue(accomClassMap.get(keys.get(0)).get(BAR_PRODUCT_ID).getCompetitorNames().contains(
                "Luxor"));
        assertTrue(accomClassMap.get(keys.get(1)).get(BAR_PRODUCT_ID).getCompetitorNames().contains(
                "Santa Barbara Inn"));
        assertTrue(accomClassMap.get(keys.get(2)).get(BAR_PRODUCT_ID).getCompetitorNames().contains(
                "Santa Barbara Inn"));
        assertEquals("190.00", accomClassMap.get(keys.get(0)).get(BAR_PRODUCT_ID).getCompetitorPrice());
        assertEquals("160.00", accomClassMap.get(keys.get(1)).get(BAR_PRODUCT_ID).getCompetitorPrice());
        assertEquals("190.00", accomClassMap.get(keys.get(2)).get(BAR_PRODUCT_ID).getCompetitorPrice());
    }

    @Test
    public void testGetCompetitorInfoCalendarView_MinPrice() {
        PacmanConfigParamsService mockConfigService = Mockito.mock(PacmanConfigParamsService.class);
        when(mockConfigService.isEnablePhysicalCapacityConsideration()).thenReturn(false);
        when(mockConfigService.getParameterValue(IPConfigParamName.BAR_WEB_RATE_SHOPPING_ENABLED.value())).thenReturn("true");
        when(mockConfigService.getParameterValue(IPConfigParamName.BAR_BAR_OVRD_DISPLAY_COMPETITOR.value())).thenReturn(BAR_OVRD_DISPLAY_COMPETITOR_VALUE_LOW);

        service.setConfigService(mockConfigService);
        sqlHelper.setConfigService(mockConfigService);

        Map<Date, CompetitorInfo> result = service.getCompetitorInfo(
                ACCOM_CLASS_ID_RATE_OF_DAY, -1, dateService
                        .getBARDisplayWindowStartDate(), dateService
                        .getBARDisplayWindowEndDate());
        assertNotNull(result);

        assertEquals(DateUtil.getNumberOfDaysInThisMonthAndNext(), result.size());

        List<Date> keys = new ArrayList<Date>(result.keySet());
        Collections.sort(keys);

        // Check the first few results to see if they match known expected
        // values
        assertTrue(result.get(keys.get(0)).getCompetitorNames().contains(
                "West Beach Inn Santa Barbara"));
        assertTrue(result.get(keys.get(1)).getCompetitorNames().contains(
                "Santa Barbara Inn"));
        assertTrue(result.get(keys.get(1)).getCompetitorNames().contains(
                "West Beach Inn Santa Barbara"));
        assertTrue(result.get(keys.get(2)).getCompetitorNames().contains(
                "Santa Barbara Inn"));
        assertTrue(result.get(keys.get(2)).getCompetitorNames().contains(
                "West Beach Inn Santa Barbara"));
        assertEquals("112.00", result.get(keys.get(0)).getCompetitorPrice());
        assertEquals("112.00", result.get(keys.get(1)).getCompetitorPrice());
        assertEquals("118.00", result.get(keys.get(2)).getCompetitorPrice());
    }


    @Test
    public void testGetCompetitorInfoMapCalendarView_MinPrice() {
        PacmanConfigParamsService mockConfigService = Mockito.mock(PacmanConfigParamsService.class);
        when(mockConfigService.isEnablePhysicalCapacityConsideration()).thenReturn(false);
        when(mockConfigService.getParameterValue(IPConfigParamName.BAR_WEB_RATE_SHOPPING_ENABLED.value())).thenReturn("true");
        when(mockConfigService.getParameterValue(IPConfigParamName.BAR_BAR_OVRD_DISPLAY_COMPETITOR.value())).thenReturn(BAR_OVRD_DISPLAY_COMPETITOR_VALUE_LOW);

        service.setConfigService(mockConfigService);
        sqlHelper.setConfigService(mockConfigService);

        Map<Integer, Map<Date, CompetitorInfo>> resultMap = service.getCompetitorInfoMap(-1, dateService
                .getBARDisplayWindowStartDate(), dateService
                .getBARDisplayWindowEndDate());
        Map<Date, CompetitorInfo> result = resultMap.get(ACCOM_CLASS_ID_RATE_OF_DAY);
        assertNotNull(result);

        assertEquals(DateUtil.getNumberOfDaysInThisMonthAndNext(), result.size());

        List<Date> keys = new ArrayList<Date>(result.keySet());
        Collections.sort(keys);

        // Check the first few results to see if they match known expected
        // values
        assertTrue(result.get(keys.get(0)).getCompetitorNames().contains(
                "West Beach Inn Santa Barbara"));
        assertTrue(result.get(keys.get(1)).getCompetitorNames().contains(
                "Santa Barbara Inn"));
        assertTrue(result.get(keys.get(1)).getCompetitorNames().contains(
                "West Beach Inn Santa Barbara"));
        assertTrue(result.get(keys.get(2)).getCompetitorNames().contains(
                "Santa Barbara Inn"));
        assertTrue(result.get(keys.get(2)).getCompetitorNames().contains(
                "West Beach Inn Santa Barbara"));
        assertEquals("112.00", result.get(keys.get(0)).getCompetitorPrice());
        assertEquals("112.00", result.get(keys.get(1)).getCompetitorPrice());
        assertEquals("118.00", result.get(keys.get(2)).getCompetitorPrice());
    }

    @Test
    public void testGetCompetitorInfoCalendarView_MedianPrice() {

        PacmanConfigParamsService mockConfigService = Mockito.mock(PacmanConfigParamsService.class);
        when(mockConfigService.isEnablePhysicalCapacityConsideration()).thenReturn(false);
        when(mockConfigService.getParameterValue(IPConfigParamName.BAR_WEB_RATE_SHOPPING_ENABLED.value())).thenReturn("true");
        when(mockConfigService.getParameterValue(IPConfigParamName.BAR_BAR_OVRD_DISPLAY_COMPETITOR.value())).thenReturn(BAR_OVRD_DISPLAY_COMPETITOR_VALUE_MEDIAN);

        service.setConfigService(mockConfigService);
        sqlHelper.setConfigService(mockConfigService);

        Map<Date, CompetitorInfo> result = service.getCompetitorInfo(
                ACCOM_CLASS_ID_RATE_OF_DAY, -1, dateService
                        .getBARDisplayWindowStartDate(), dateService
                        .getBARDisplayWindowEndDate());
        assertNotNull(result);

        assertEquals(DateUtil.getNumberOfDaysInThisMonthAndNext(), result.size());

        List<Date> keys = new ArrayList<Date>(result.keySet());
        Collections.sort(keys);

        // Check the first few results to see if they match known expected
        // values

        assertTrue(result.get(keys.get(1)).getCompetitorNames().contains(
                "Luxor"));
        assertTrue(result.get(keys.get(0)).getCompetitorNames().contains(
                "Santa Barbara Inn"));
        assertTrue(result.get(keys.get(4)).getCompetitorNames().contains(
                "West Beach Inn Santa Barbara"));
        assertEquals("118.00", result.get(keys.get(1)).getCompetitorPrice());
        assertEquals("130.00", result.get(keys.get(0)).getCompetitorPrice());
        assertEquals("130.00", result.get(keys.get(4)).getCompetitorPrice());
    }


    @Test
    public void testGetCompetitorInfoMapCalendarView_MedianPrice() {

        PacmanConfigParamsService mockConfigService = Mockito.mock(PacmanConfigParamsService.class);
        when(mockConfigService.isEnablePhysicalCapacityConsideration()).thenReturn(false);
        when(mockConfigService.getParameterValue(IPConfigParamName.BAR_WEB_RATE_SHOPPING_ENABLED.value())).thenReturn("true");
        when(mockConfigService.getParameterValue(IPConfigParamName.BAR_BAR_OVRD_DISPLAY_COMPETITOR.value())).thenReturn(BAR_OVRD_DISPLAY_COMPETITOR_VALUE_MEDIAN);

        service.setConfigService(mockConfigService);
        sqlHelper.setConfigService(mockConfigService);

        Map<Integer, Map<Date, CompetitorInfo>> resultMap = service.getCompetitorInfoMap(-1, dateService
                .getBARDisplayWindowStartDate(), dateService
                .getBARDisplayWindowEndDate());
        Map<Date, CompetitorInfo> result = resultMap.get(ACCOM_CLASS_ID_RATE_OF_DAY);
        assertNotNull(result);

        assertEquals(DateUtil.getNumberOfDaysInThisMonthAndNext(), result.size());

        List<Date> keys = new ArrayList<Date>(result.keySet());
        Collections.sort(keys);

        // Check the first few results to see if they match known expected
        // values

        assertTrue(result.get(keys.get(1)).getCompetitorNames().contains(
                "Luxor"));
        assertTrue(result.get(keys.get(0)).getCompetitorNames().contains(
                "Santa Barbara Inn"));
        assertTrue(result.get(keys.get(4)).getCompetitorNames().contains(
                "West Beach Inn Santa Barbara"));
        assertEquals("118.00", result.get(keys.get(1)).getCompetitorPrice());
        assertEquals("130.00", result.get(keys.get(0)).getCompetitorPrice());
        assertEquals("130.00", result.get(keys.get(4)).getCompetitorPrice());
    }

    @Test
    public void testGetCompetitorInfoCalendarView_AbsoluteCompetitorPrice() {
        switchTenant();

        PacmanConfigParamsService mockConfigService = Mockito.mock(PacmanConfigParamsService.class);
        when(mockConfigService.isEnablePhysicalCapacityConsideration()).thenReturn(false);
        when(mockConfigService.getParameterValue(IPConfigParamName.BAR_WEB_RATE_SHOPPING_ENABLED.value())).thenReturn("true");
        when(mockConfigService.getParameterValue(IPConfigParamName.BAR_BAR_OVRD_DISPLAY_COMPETITOR.value())).thenReturn(BAR_OVRD_DISPLAY_COMPETITOR_VALUE_ABSOLUTE);
        when(mockConfigService.getParameterValue(IPConfigParamName.BAR_BAR_OVRD_ABSOLUTE_COMPETITOR.value())).thenReturn("Holiday Inn");

        service.setConfigService(mockConfigService);
        sqlHelper.setConfigService(mockConfigService);

        Map<Date, CompetitorInfo> result = service.getCompetitorInfo(
                ACCOM_CLASS_ID_BAR_BY_LOS, -1, dateService
                        .getBARDisplayWindowStartDate(), dateService
                        .getBARDisplayWindowEndDate());
        assertNotNull(result);

        assertEquals(DateUtil.getNumberOfDaysInThisMonthAndNext(), result.size());

        List<Date> keys = new ArrayList<Date>(result.keySet());
        Collections.sort(keys);

        // Check the first few results to see if they match known expected
        // values
        assertTrue(result.get(keys.get(0)).getCompetitorNames().contains(
                "Holiday Inn"));
        assertTrue(result.get(keys.get(1)).getCompetitorNames().contains(
                "Holiday Inn"));
        assertTrue(result.get(keys.get(2)).getCompetitorNames().contains(
                "Holiday Inn"));
        assertEquals("114.00", result.get(keys.get(0)).getCompetitorPrice());
        assertEquals("128.00", result.get(keys.get(1)).getCompetitorPrice());
        assertEquals("142.00", result.get(keys.get(2)).getCompetitorPrice());
    }


    @Test
    public void testGetCompetitorInfoMapCalendarView_AbsoluteCompetitorPrice() {
        switchTenant();

        PacmanConfigParamsService mockConfigService = Mockito.mock(PacmanConfigParamsService.class);
        when(mockConfigService.isEnablePhysicalCapacityConsideration()).thenReturn(false);
        when(mockConfigService.getParameterValue(IPConfigParamName.BAR_WEB_RATE_SHOPPING_ENABLED.value())).thenReturn("true");
        when(mockConfigService.getParameterValue(IPConfigParamName.BAR_BAR_OVRD_DISPLAY_COMPETITOR.value())).thenReturn(BAR_OVRD_DISPLAY_COMPETITOR_VALUE_ABSOLUTE);
        when(mockConfigService.getParameterValue(IPConfigParamName.BAR_BAR_OVRD_ABSOLUTE_COMPETITOR.value())).thenReturn("Holiday Inn");

        service.setConfigService(mockConfigService);
        sqlHelper.setConfigService(mockConfigService);

        Map<Integer, Map<Date, CompetitorInfo>> resultMap = service.getCompetitorInfoMap(-1, dateService
                .getBARDisplayWindowStartDate(), dateService
                .getBARDisplayWindowEndDate());
        Map<Date, CompetitorInfo> result = resultMap.get(ACCOM_CLASS_ID_BAR_BY_LOS);
        assertNotNull(result);

        assertEquals(DateUtil.getNumberOfDaysInThisMonthAndNext(), result.size());

        List<Date> keys = new ArrayList<Date>(result.keySet());
        Collections.sort(keys);

        // Check the first few results to see if they match known expected
        // values
        assertTrue(result.get(keys.get(0)).getCompetitorNames().contains(
                "Holiday Inn"));
        assertTrue(result.get(keys.get(1)).getCompetitorNames().contains(
                "Holiday Inn"));
        assertTrue(result.get(keys.get(2)).getCompetitorNames().contains(
                "Holiday Inn"));
        assertEquals("114.00", result.get(keys.get(0)).getCompetitorPrice());
        assertEquals("128.00", result.get(keys.get(1)).getCompetitorPrice());
        assertEquals("142.00", result.get(keys.get(2)).getCompetitorPrice());
    }

    @Test
    public void testGetCompetitorInfoTabularView() throws Exception {
        switchTenant();

        PacmanConfigParamsService mockConfigService = Mockito.mock(PacmanConfigParamsService.class);
        when(mockConfigService.isEnablePhysicalCapacityConsideration()).thenReturn(false);
        when(mockConfigService.getParameterValue(IPConfigParamName.BAR_WEB_RATE_SHOPPING_ENABLED.value())).thenReturn("true");
        when(mockConfigService.getParameterValue(IPConfigParamName.BAR_BAR_OVRD_DISPLAY_COMPETITOR.value())).thenReturn(BAR_OVRD_DISPLAY_COMPETITOR_VALUE_HIGH);

        service.setConfigService(mockConfigService);
        sqlHelper.setConfigService(mockConfigService);

        PacmanWorkContextHelper.setPropertyId(PROPERTY_ID_BAR_BY_LOS);

        BARFilterCriteria criteria = new BARFilterCriteria();
        criteria.setAccomClassId(ACCOM_CLASS_ID_BAR_BY_LOS);
        criteria.setIncludedDaysOfWeek(Arrays.asList(new Integer[]{1, 2, 3,
                4, 5, 6, 7}));
        criteria.setIncludedLengthsOfStay(Arrays.asList(new Integer[]{1, 2,
                3, 4, 5, 6, 7, 8}));
        criteria.setStartDate(dateService.getBARDisplayWindowStartDate());
        criteria.setEndDate(dateService.getBARDisplayWindowEndDate());

        Map<Date, Map<Integer, CompetitorInfo>> result = service.getCompetitorInfo(criteria);
        assertNotNull(result);
        assertEquals(DateUtil.getNumberOfDaysInThisMonthAndNext(), result.size());

        List<Date> keys = new ArrayList<Date>(result.keySet());
        Collections.sort(keys);

        assertTrue(result.get(keys.get(0)).get(1).getCompetitorNames().contains("Holiday Inn"));
        assertEquals(new BigDecimal(114), new BigDecimal(result.get(keys.get(0)).get(1).getCompetitorPrice()).setScale(0, RoundingMode.HALF_UP));

        assertTrue(result.get(keys.get(1)).get(1).getCompetitorNames().contains("Holiday Inn"));
        assertEquals(new BigDecimal(128), new BigDecimal(result.get(keys.get(1)).get(1).getCompetitorPrice()).setScale(0, RoundingMode.HALF_UP));

        assertTrue(result.get(keys.get(2)).get(1).getCompetitorNames().contains("Holiday Inn"));
        assertEquals(new BigDecimal(142), new BigDecimal(result.get(keys.get(2)).get(1).getCompetitorPrice()).setScale(0, RoundingMode.HALF_UP));
    }

    @Test
    public void testGetCompetitorInfoForIndependentProducts() throws Exception {
        PacmanConfigParamsService mockConfigService = Mockito.mock(PacmanConfigParamsService.class);
        when(mockConfigService.isEnablePhysicalCapacityConsideration()).thenReturn(false);
        when(mockConfigService.getParameterValue(IPConfigParamName.BAR_WEB_RATE_SHOPPING_ENABLED.value())).thenReturn("true");
        when(mockConfigService.getParameterValue(IPConfigParamName.BAR_BAR_OVRD_DISPLAY_COMPETITOR.value())).thenReturn(BAR_OVRD_DISPLAY_COMPETITOR_VALUE_HIGH);

        service.setConfigService(mockConfigService);
        sqlHelper.setConfigService(mockConfigService);

        BARFilterCriteria criteria = new BARFilterCriteria();
        criteria.setAccomClassId(2);
        criteria.setIncludedDaysOfWeek(Arrays.asList(new Integer[]{1, 2, 3, 4, 5, 6, 7}));
        criteria.setIncludedLengthsOfStay(Arrays.asList(new Integer[]{1, 2, 3, 4, 5, 6, 7}));
        criteria.setStartDate(LocalDate.now().toDate());
        criteria.setEndDate(LocalDate.now().toDate());
        criteria.setSelectedProductId(createTestDataForProductAndWebrate().getId());

        Map<Date, Map<Integer, CompetitorInfo>> result = service.getCompetitorInfo(criteria);
        assertNotNull(result);
        assertEquals(1, result.size());
    }

    private Product createTestDataForProductAndWebrate() {
        Product product = tenantCrudService().save(ProductBuilder.createIndependentProductProduct("IND1"));

        AccomClass accomClass = new AccomClass();
        accomClass.setId(2);

        WebrateCompetitors webrateCompetitors = new WebrateCompetitors();
        webrateCompetitors.setId(4);

        WebrateCompetitorsAccomClass webrateCompetitorsAccomClass = new WebrateCompetitorsAccomClass();
        webrateCompetitorsAccomClass.setAccomClass(accomClass);
        webrateCompetitorsAccomClass.setWebrateCompetitor(webrateCompetitors);
        webrateCompetitorsAccomClass.setProductID(product.getId());
        webrateCompetitorsAccomClass.setDemandEnabled(1);
        webrateCompetitorsAccomClass.setRankingEnabled(1);
        tenantCrudService().save(webrateCompetitorsAccomClass);

        return product;
    }

    @Test
    void testGetCompetitorInfoToExcludeSelfCompetitor_MinPrice_WhenDemandEnabled() {
        PacmanWorkContextHelper.setPropertyId(PROPERTY_ID_RATEOFDAY);
        System.setProperty("use.demand.enabled.in.sql.helper.competitor.query", "true");

        int selfCompetitorHotelId = 1863;
        PacmanConfigParamsService mockConfigService = mockData(selfCompetitorHotelId, BAR_OVRD_DISPLAY_COMPETITOR_VALUE_LOW);
        setConfigServices(mockConfigService);

        List<AccomClass> accomClasses = tenantCrudService().findAll(AccomClass.class);
        WebrateCompetitors selfCompetitor = UniqueWebrateCompetitorsCreator.createWebrateCompetitorsByName("Hotel Pune" + new Random().nextInt(), String.valueOf(selfCompetitorHotelId));
        Webrate selfCompetitorWebRate = createWebrateData(selfCompetitor, 10);
        AccomClass selfCompetitorWebRateAccomClass = getWebRateAccomClass(accomClasses, selfCompetitorWebRate);
        WebrateCompetitorsAccomClass selfCompetitorWebrateCompetitorsAccomClass = UniqueWebrateCompetitorsAccomClassCreator.createWebrateCompetitorsAccomClass(1, 0, selfCompetitor, selfCompetitorWebRateAccomClass);
        selfCompetitor.setWebrateCompetitorsAccomClasses(new HashSet<>(Collections.singletonList(selfCompetitorWebrateCompetitorsAccomClass)));

        WebrateCompetitors competitorWithMinWebRate = UniqueWebrateCompetitorsCreator.createWebrateCompetitorsByName("Hotel Mumbai" + new Random().nextInt(), String.valueOf(selfCompetitorHotelId + 1));
        Webrate webrateData = createWebrateData(competitorWithMinWebRate, 20);
        AccomClass webRateDataAccomClass = getWebRateAccomClass(accomClasses, webrateData);
        WebrateCompetitorsAccomClass competitorWithMinWebRateWebrateCompetitorsAccomClass = UniqueWebrateCompetitorsAccomClassCreator.createWebrateCompetitorsAccomClass(0, 0, competitorWithMinWebRate, webRateDataAccomClass);
        competitorWithMinWebRate.setWebrateCompetitorsAccomClasses(new HashSet<>(Collections.singletonList(competitorWithMinWebRateWebrateCompetitorsAccomClass)));

        WebrateCompetitors competitorWithMaxWebRate = createWebRateCompetitor(selfCompetitorHotelId + 2, "Hotel USA", 50, webRateDataAccomClass);
        competitorWithMaxWebRate.setWebrateCompetitorsAccomClasses(new HashSet<>(Collections.singletonList(competitorWithMinWebRateWebrateCompetitorsAccomClass)));

        WebrateCompetitors competitorExpectedInOutput = createWebRateCompetitor(selfCompetitorHotelId + 3, "Hotel UK", 30, webRateDataAccomClass);
        competitorExpectedInOutput.setWebrateCompetitorsAccomClasses(new HashSet<>(Collections.singletonList(competitorWithMinWebRateWebrateCompetitorsAccomClass)));

        BARFilterCriteria criteria = getBarFilterCriteria(selfCompetitorWebRate);
        Map<Date, Map<Integer, CompetitorInfo>> result = service.getCompetitorInfo(criteria);

        verifyCompetitorResult(selfCompetitor, selfCompetitorWebRate, competitorWithMinWebRate, competitorWithMaxWebRate, competitorExpectedInOutput, result);
    }

    @Test
    void testGetCompetitorInfoToExcludeSelfCompetitor_MaxPrice_WhenDemandEnabled() {
        PacmanWorkContextHelper.setPropertyId(PROPERTY_ID_RATEOFDAY);
        System.setProperty("use.demand.enabled.in.sql.helper.competitor.query", "true");

        int selfCompetitorHotelId = 1863;
        PacmanConfigParamsService mockConfigService = mockData(selfCompetitorHotelId, BAR_OVRD_DISPLAY_COMPETITOR_VALUE_HIGH);
        setConfigServices(mockConfigService);

        List<AccomClass> accomClasses = tenantCrudService().findAll(AccomClass.class);
        WebrateCompetitors selfCompetitor = UniqueWebrateCompetitorsCreator.createWebrateCompetitorsByName("Hotel Pune" + new Random().nextInt(), String.valueOf(selfCompetitorHotelId));
        Webrate selfCompetitorWebRate = createWebrateData(selfCompetitor, 10);
        AccomClass selfCompetitorWebRateAccomClass = getWebRateAccomClass(accomClasses, selfCompetitorWebRate);
        WebrateCompetitorsAccomClass selfCompetitorWebrateCompetitorsAccomClass = UniqueWebrateCompetitorsAccomClassCreator.createWebrateCompetitorsAccomClass(1, 0, selfCompetitor, selfCompetitorWebRateAccomClass);
        selfCompetitor.setWebrateCompetitorsAccomClasses(new HashSet<>(Collections.singletonList(selfCompetitorWebrateCompetitorsAccomClass)));

        WebrateCompetitors competitorWithMaxWebRate = UniqueWebrateCompetitorsCreator.createWebrateCompetitorsByName("Hotel Mumbai" + new Random().nextInt(), String.valueOf(selfCompetitorHotelId + 1));
        Webrate webrateData = createWebrateData(competitorWithMaxWebRate, 200);
        AccomClass webRateDataAccomClass = getWebRateAccomClass(accomClasses, webrateData);
        WebrateCompetitorsAccomClass competitorWithMinWebRateWebrateCompetitorsAccomClass = UniqueWebrateCompetitorsAccomClassCreator.createWebrateCompetitorsAccomClass(0, 0, competitorWithMaxWebRate, webRateDataAccomClass);
        competitorWithMaxWebRate.setWebrateCompetitorsAccomClasses(new HashSet<>(Collections.singletonList(competitorWithMinWebRateWebrateCompetitorsAccomClass)));

        WebrateCompetitors competitorWithMinWebRate = createWebRateCompetitor(selfCompetitorHotelId + 2, "Hotel USA", 50, webRateDataAccomClass);
        competitorWithMinWebRate.setWebrateCompetitorsAccomClasses(new HashSet<>(Collections.singletonList(competitorWithMinWebRateWebrateCompetitorsAccomClass)));

        WebrateCompetitors competitorExpectedInOutput = createWebRateCompetitor(selfCompetitorHotelId + 3, "Hotel UK", 150, webRateDataAccomClass);
        competitorExpectedInOutput.setWebrateCompetitorsAccomClasses(new HashSet<>(Collections.singletonList(competitorWithMinWebRateWebrateCompetitorsAccomClass)));

        BARFilterCriteria criteria = getBarFilterCriteria(selfCompetitorWebRate);
        Map<Date, Map<Integer, CompetitorInfo>> result = service.getCompetitorInfo(criteria);

        verifyCompetitorResult(selfCompetitor, selfCompetitorWebRate, competitorWithMinWebRate, competitorWithMaxWebRate, competitorExpectedInOutput, result);
    }

    @Test
    void testGetCompetitorInfoToExcludeSelfCompetitor_MedianPrice_WhenDemandEnabled() {
        PacmanWorkContextHelper.setPropertyId(PROPERTY_ID_RATEOFDAY);
        System.setProperty("use.demand.enabled.in.sql.helper.competitor.query", "true");

        int selfCompetitorHotelId = 1863;
        PacmanConfigParamsService mockConfigService = mockData(selfCompetitorHotelId, BAR_OVRD_DISPLAY_COMPETITOR_VALUE_MEDIAN);
        setConfigServices(mockConfigService);

        List<AccomClass> accomClasses = tenantCrudService().findAll(AccomClass.class);
        WebrateCompetitors selfCompetitor = UniqueWebrateCompetitorsCreator.createWebrateCompetitorsByName("Hotel Pune" + new Random().nextInt(), String.valueOf(selfCompetitorHotelId));
        Webrate selfCompetitorWebRate = createWebrateData(selfCompetitor, 10);
        AccomClass selfCompetitorWebRateAccomClass = getWebRateAccomClass(accomClasses, selfCompetitorWebRate);
        WebrateCompetitorsAccomClass selfCompetitorWebrateCompetitorsAccomClass = UniqueWebrateCompetitorsAccomClassCreator.createWebrateCompetitorsAccomClass(1, 0, selfCompetitor, selfCompetitorWebRateAccomClass);
        selfCompetitor.setWebrateCompetitorsAccomClasses(new HashSet<>(Collections.singletonList(selfCompetitorWebrateCompetitorsAccomClass)));

        WebrateCompetitors competitorWithMinWebRate = UniqueWebrateCompetitorsCreator.createWebrateCompetitorsByName("Hotel Mumbai" + new Random().nextInt(), String.valueOf(selfCompetitorHotelId + 1));
        Webrate webrateData = createWebrateData(competitorWithMinWebRate, 200);
        AccomClass webRateDataAccomClass = getWebRateAccomClass(accomClasses, webrateData);
        WebrateCompetitorsAccomClass competitorWithMinWebRateWebrateCompetitorsAccomClass = UniqueWebrateCompetitorsAccomClassCreator.createWebrateCompetitorsAccomClass(0, 0, competitorWithMinWebRate, webRateDataAccomClass);
        competitorWithMinWebRate.setWebrateCompetitorsAccomClasses(new HashSet<>(Collections.singletonList(competitorWithMinWebRateWebrateCompetitorsAccomClass)));

        WebrateCompetitors competitorWithMaxWebRate = createWebRateCompetitor(selfCompetitorHotelId + 2, "Hotel USA", 250, webRateDataAccomClass);
        competitorWithMaxWebRate.setWebrateCompetitorsAccomClasses(new HashSet<>(Collections.singletonList(competitorWithMinWebRateWebrateCompetitorsAccomClass)));

        WebrateCompetitors competitorExpectedInOutput = createWebRateCompetitor(selfCompetitorHotelId + 3, "Hotel UK", 150, webRateDataAccomClass);
        competitorExpectedInOutput.setWebrateCompetitorsAccomClasses(new HashSet<>(Collections.singletonList(competitorWithMinWebRateWebrateCompetitorsAccomClass)));

        BARFilterCriteria criteria = getBarFilterCriteria(selfCompetitorWebRate);
        Map<Date, Map<Integer, CompetitorInfo>> result = service.getCompetitorInfo(criteria);

        verifyCompetitorResult(selfCompetitor, selfCompetitorWebRate, competitorWithMinWebRate, competitorWithMaxWebRate, competitorExpectedInOutput, result);
    }

    private void verifyCompetitorResult(WebrateCompetitors selfCompetitor, Webrate selfCompetitorWebRate, WebrateCompetitors competitorWithMinWebRate, WebrateCompetitors competitorWithMaxWebRate, WebrateCompetitors competitorExpectedInOutput, Map<Date, Map<Integer, CompetitorInfo>> result) {
        assertNotNull(result);
        assertEquals(1, result.size());
        List<Date> keys = new ArrayList<Date>(result.keySet());
        keys.forEach(key -> {
            Map<Integer, CompetitorInfo> resultMap = result.get(key);
            CompetitorInfo competitorInfo = resultMap.get(selfCompetitorWebRate.getLos());
            assertFalse(competitorInfo.getCompetitorIds().contains(selfCompetitor.getId()));
            assertFalse(competitorInfo.getCompetitorIds().contains(competitorWithMinWebRate.getId()));
            assertFalse(competitorInfo.getCompetitorIds().contains(competitorWithMaxWebRate.getId()));
            assertTrue(competitorInfo.getCompetitorIds().contains(competitorExpectedInOutput.getId()));
        });
    }


    @Test
    void testGetCompetitorInfoForPrimaryProduct() {
        Product product = tenantCrudService().save(ProductBuilder.createIndependentProductProduct("IND1"));
        Integer indProductId = product.getId();
        PacmanWorkContextHelper.setPropertyId(PROPERTY_ID_RATEOFDAY);
        int selfCompetitorHotelId = 1863;
        PacmanConfigParamsService mockConfigService = mockData(selfCompetitorHotelId, BAR_OVRD_DISPLAY_COMPETITOR_VALUE_LOW);
        setConfigServices(mockConfigService);
        Integer primaryProductId = 1;

        List<AccomClass> accomClasses = tenantCrudService().findAll(AccomClass.class);
        WebrateCompetitors selfCompetitor = UniqueWebrateCompetitorsCreator.createWebrateCompetitorsByName("Hotel Pune" + new Random().nextInt(), String.valueOf(selfCompetitorHotelId));
        Webrate selfCompetitorWebRate = createWebrateData(selfCompetitor, 10, 1);
        AccomClass webRateAccomClass = getWebRateAccomClass(accomClasses, selfCompetitorWebRate);
        createWebrateCompetitorsAccomClass(primaryProductId, selfCompetitor, webRateAccomClass);

        WebrateCompetitors competitorWithMinWebRate = createWebRateCompetitor(selfCompetitorHotelId + 1, "Hotel Mumbai", 20, webRateAccomClass, primaryProductId, 1);
        WebrateCompetitors competitorWithMedianWebRate = createWebRateCompetitor(selfCompetitorHotelId + 2, "Hotel UK", 30, webRateAccomClass, primaryProductId, 1);
        WebrateCompetitors competitorWithMaxWebRate = createWebRateCompetitor(selfCompetitorHotelId + 3, "Hotel USA", 50, webRateAccomClass, primaryProductId, 1);
        WebrateCompetitors hotel_ind = createWebRateCompetitor(selfCompetitorHotelId + 4, "Hotel IND", 15, webRateAccomClass, indProductId, 1);

        BARFilterCriteria criteria = getBarFilterCriteria(selfCompetitorWebRate);
        Map<Date, Map<Integer, CompetitorInfo>> result = service.getCompetitorInfo(criteria);

        assertNotNull(result);
        assertEquals(1, result.size());
        List<Date> keys = new ArrayList<Date>(result.keySet());
        keys.forEach(key -> {
            Map<Integer, CompetitorInfo> resultMap = result.get(key);
            CompetitorInfo competitorInfo = resultMap.get(selfCompetitorWebRate.getLos());
            assertFalse(competitorInfo.getCompetitorIds().contains(selfCompetitor.getId()));
            assertTrue(competitorInfo.getCompetitorIds().contains(competitorWithMinWebRate.getId()));
            assertFalse(competitorInfo.getCompetitorIds().contains(competitorWithMedianWebRate.getId()));
            assertFalse(competitorInfo.getCompetitorIds().contains(competitorWithMaxWebRate.getId()));
            assertFalse(competitorInfo.getCompetitorIds().contains(hotel_ind.getId()));
            assertEquals("20.00", competitorInfo.getCompetitorPrice());
        });
    }

    @Test
    void testGetCompetitorInfoForNonPrimaryProduct() {
        Product product = tenantCrudService().save(ProductBuilder.createIndependentProductProduct("IND1"));
        Integer indProductId = product.getId();
        PacmanWorkContextHelper.setPropertyId(PROPERTY_ID_RATEOFDAY);
        int selfCompetitorHotelId = 1863;
        PacmanConfigParamsService mockConfigService = mockData(selfCompetitorHotelId, BAR_OVRD_DISPLAY_COMPETITOR_VALUE_HIGH);
        setConfigServices(mockConfigService);
        Integer primaryProductId = 1;

        List<AccomClass> accomClasses = tenantCrudService().findAll(AccomClass.class);
        WebrateCompetitors selfCompetitor = UniqueWebrateCompetitorsCreator.createWebrateCompetitorsByName("Hotel Pune" + new Random().nextInt(), String.valueOf(selfCompetitorHotelId));
        Webrate selfCompetitorWebRate = createWebrateData(selfCompetitor, 10, 1);
        AccomClass webRateAccomClass = getWebRateAccomClass(accomClasses, selfCompetitorWebRate);
        createWebrateCompetitorsAccomClass(indProductId, selfCompetitor, webRateAccomClass);

        WebrateCompetitors competitorWithMinWebRate = createWebRateCompetitor(selfCompetitorHotelId + 1, "Hotel Mumbai", 20, webRateAccomClass, indProductId, 1);
        WebrateCompetitors competitorWithMedianWebRate = createWebRateCompetitor(selfCompetitorHotelId + 2, "Hotel UK", 30, webRateAccomClass, indProductId, 1);
        WebrateCompetitors competitorWithMaxWebRate = createWebRateCompetitor(selfCompetitorHotelId + 3, "Hotel USA", 50, webRateAccomClass, indProductId, 1);
        WebrateCompetitors hotel_ind = createWebRateCompetitor(selfCompetitorHotelId + 4, "Hotel IND", 65, webRateAccomClass, primaryProductId, 1);

        BARFilterCriteria criteria = getBarFilterCriteria(selfCompetitorWebRate);
        criteria.setSelectedProductId(indProductId);
        Map<Date, Map<Integer, CompetitorInfo>> result = service.getCompetitorInfo(criteria);

        assertNotNull(result);
        assertEquals(1, result.size());
        List<Date> keys = new ArrayList<Date>(result.keySet());
        keys.forEach(key -> {
            Map<Integer, CompetitorInfo> resultMap = result.get(key);
            CompetitorInfo competitorInfo = resultMap.get(selfCompetitorWebRate.getLos());
            assertFalse(competitorInfo.getCompetitorIds().contains(selfCompetitor.getId()));
            assertFalse(competitorInfo.getCompetitorIds().contains(competitorWithMinWebRate.getId()));
            assertFalse(competitorInfo.getCompetitorIds().contains(competitorWithMedianWebRate.getId()));
            assertTrue(competitorInfo.getCompetitorIds().contains(competitorWithMaxWebRate.getId()));
            assertFalse(competitorInfo.getCompetitorIds().contains(hotel_ind.getId()));
            assertEquals("50.00", competitorInfo.getCompetitorPrice());
        });
    }


    private Webrate createWebrateData(WebrateCompetitors selfCompetitor, int webRateValue, int webRateTypeId) {
        return UniqueWebRateCreator.createWebrate(1, selfCompetitor.getId(), 3, 3, 1, DateUtil.sqlDate(getDateAfterTenYears()), webRateValue, webRateTypeId);
    }

    private WebrateCompetitors createWebRateCompetitor(int selfCompetitorHotelId, String competitorName, int webRateValue, AccomClass accomClass, Integer productID, int webRateTypeId) {
        WebrateCompetitors competitor = UniqueWebrateCompetitorsCreator.createWebrateCompetitorsByName(competitorName + new Random().nextInt(), String.valueOf(selfCompetitorHotelId));
        createWebrateData(competitor, webRateValue, webRateTypeId);
        createWebrateCompetitorsAccomClass(productID, competitor, accomClass);
        return competitor;
    }

    private WebrateCompetitorsAccomClass createWebrateCompetitorsAccomClass(Integer productID, WebrateCompetitors competitor, AccomClass accomClass) {
        return UniqueWebrateCompetitorsAccomClassCreator.createWebrateCompetitorsAccomClass(productID, 1, 1, competitor, accomClass);
    }

    @Test
    public void testGetOccupancyForecastWhenPhysicalCapacityFalse() throws Exception {
        switchTenant();
        PacmanWorkContextHelper.setPropertyId(PROPERTY_ID_BAR_BY_LOS);
        PacmanConfigParamsService mockConfigService = Mockito.mock(PacmanConfigParamsService.class);
        when(mockConfigService.isEnablePhysicalCapacityConsideration()).thenReturn(false);
        service.setConfigService(mockConfigService);
        Map<Date, Float> result = service.getOccupancyForecast(dateService
                .getBARDisplayWindowStartDate(), dateService
                .getBARDisplayWindowEndDate());
        verifyConfigServiceCallForEnablePhysicalCapacity(mockConfigService);
        assertNotNull(result);
        assertEquals(Float.valueOf(45.0847f), result.get(DateUtil.getDateForNextMonth(1)));
        assertEquals(Float.valueOf(47.1186f), result.get(DateUtil.getDateForNextMonth(2)));
        assertEquals(Float.valueOf(48.8135f), result.get(DateUtil.getDateForNextMonth(3)));
        assertEquals(Float.valueOf(50.5084f), result.get(DateUtil.getDateForNextMonth(4)));
    }

    @Test
    public void testGetOccupancyForecastWhenPhysicalCapacityTrue() throws Exception {
        switchTenant();
        PacmanWorkContextHelper.setPropertyId(PROPERTY_ID_BAR_BY_LOS);
        PacmanConfigParamsService mockConfigService = Mockito.mock(PacmanConfigParamsService.class);
        when(mockConfigService.isEnablePhysicalCapacityConsideration()).thenReturn(true);
        service.setConfigService(mockConfigService);
        Map<Date, Float> result = service.getOccupancyForecast(dateService
                .getBARDisplayWindowStartDate(), dateService
                .getBARDisplayWindowEndDate());
        verifyConfigServiceCallForEnablePhysicalCapacity(mockConfigService);
        assertNotNull(result);
        assertEquals(Float.valueOf(45.08f), result.get(DateUtil.getDateForNextMonth(1)));
        assertEquals(Float.valueOf(47.12f), result.get(DateUtil.getDateForNextMonth(2)));
        assertEquals(Float.valueOf(48.81f), result.get(DateUtil.getDateForNextMonth(3)));
        assertEquals(Float.valueOf(50.51f), result.get(DateUtil.getDateForNextMonth(4)));
    }

    private void verifyConfigServiceCallForEnablePhysicalCapacity(PacmanConfigParamsService mockConfigService) {
        verify(mockConfigService, times(1)).isEnablePhysicalCapacityConsideration();
    }

    @Test
    public void testGetRoomsOutOfOrder() throws Exception {
        switchTenant();

        PacmanWorkContextHelper.setPropertyId(PROPERTY_ID_BAR_BY_LOS);

        Map<Date, Integer> result = service.getRoomsOutOfOrder(dateService
                .getBARDisplayWindowStartDate(), dateService
                .getBARDisplayWindowEndDate());
        assertNotNull(result);

        List<Date> keys = new ArrayList<Date>(result.keySet());
        Collections.sort(keys);

        assertEquals(Integer.valueOf(0), result.get(keys.get(0)));
        assertEquals(Integer.valueOf(4), result.get(keys.get(14)));
        assertEquals(Integer.valueOf(8), result.get(keys.get(16)));
    }

    @Test
    public void testGetLastRoomValue() throws Exception {
        PacmanWorkContextHelper.setPropertyId(PROPERTY_ID_RATEOFDAY);

        Map<Date, BigDecimal> result = service.getLastRoomValue(ACCOM_CLASS_ID_RATE_OF_DAY, dateService
                .getBARDisplayWindowStartDate(), dateService
                .getBARDisplayWindowEndDate());
        assertNotNull(result);

        List<Date> keys = new ArrayList<Date>(result.keySet());
        Collections.sort(keys);

        assertEquals(new BigDecimal("30.00000"), result.get(keys.get(0)));
        assertEquals(new BigDecimal("44.00000"), result.get(keys.get(14)));
        assertEquals(new BigDecimal("46.00000"), result.get(keys.get(16)));
    }

    @Test
    public void testGetLastRoomValueWithLrv() throws Exception {
        PacmanWorkContextHelper.setPropertyId(PROPERTY_ID_RATEOFDAY);

        int lengthOfStay = 3;
        Map<Date, BigDecimal> result = service.getLastRoomValue(ACCOM_CLASS_ID_RATE_OF_DAY, dateService
                .getBARDisplayWindowStartDate(), dateService
                .getBARDisplayWindowEndDate(), lengthOfStay);
        assertNotNull(result);

        List<Date> keys = new ArrayList<Date>(result.keySet());
        Collections.sort(keys);
        assertEquals(31.00000, result.get(keys.get(0)).doubleValue(), 0.0001);
        assertEquals(45.00000, result.get(keys.get(14)).doubleValue(), 0.0001);
        assertEquals(47.00000, result.get(keys.get(16)).doubleValue(), 0.0001);
    }

    @Test
    public void testGetLastRoomValueForADate() throws Exception {
        int lengthOfStay = 3;
        BigDecimal lastRoomValue = service.getLastRoomValue(ACCOM_CLASS_ID_RATE_OF_DAY, dateService
                .getBARDisplayWindowStartDate(), lengthOfStay);
        assertEquals(31.00, lastRoomValue.doubleValue(), 0.001);
    }

    @Test
    public void testGetLastRoomValueForADateNoValue() throws Exception {
        int lengthOfStay = 3;
        int nonExistantAccomClassId = 9999;
        BigDecimal lastRoomValue = service.getLastRoomValue(nonExistantAccomClassId, dateService
                .getBARDisplayWindowStartDate(), lengthOfStay);
        assertNull(lastRoomValue);
    }

    @Test
    public void testFilterByPrice() throws Exception {
        switchTenant();

        PacmanConfigParamsService mockConfigService = Mockito.mock(PacmanConfigParamsService.class);
        when(mockConfigService.isEnablePhysicalCapacityConsideration()).thenReturn(false);
        when(mockConfigService.getParameterValue(IPConfigParamName.BAR_BAR_DECISION.value())).thenReturn(BAR_DECISION_VALUE_LOS);
        when(mockConfigService.getParameterValue(IPConfigParamName.BAR_WEB_RATE_SHOPPING_ENABLED.value())).thenReturn("true");
        when(mockConfigService.getParameterValue(IPConfigParamName.BAR_WEB_RATE_STALENESS_THRESHOLD.value())).thenReturn("365");
        when(mockConfigService.getParameterValue(IPConfigParamName.BAR_BAR_OVRD_DISPLAY_COMPETITOR.value())).thenReturn(BAR_OVRD_DISPLAY_COMPETITOR_VALUE_HIGH);

        service.setConfigService(mockConfigService);
        sqlHelper.setConfigService(mockConfigService);
        PacmanWorkContextHelper.setPropertyId(PROPERTY_ID_BAR_BY_LOS);

        BARFilterCriteria criteria = new BARFilterCriteria();
        criteria.setAccomClassId(ACCOM_CLASS_ID_BAR_BY_LOS);
        criteria.setIncludedDaysOfWeek(Arrays.asList(new Integer[]{1, 2, 3, 4, 5, 6, 7}));
        criteria.setIncludedLengthsOfStay(Arrays.asList(new Integer[]{1, 2, 3, 4, 5, 6, 7, 8}));

        criteria.setStartDate(dateService.getBARDisplayWindowStartDate());
        criteria.setEndDate(dateService.getBARDisplayWindowEndDate());

        criteria.setPriceLessThan(1450f);
        criteria.setLastViewedTime(DateUtil.getFirstDayOfCurrentMonth());

        List<Date> dates = new ArrayList<Date>(service.getBarDecisions(criteria).keySet());
        assertTrue(dates.size() >= 4, "Expected at least four dates");

        List<Date> dates2 = new ArrayList<Date>(service.getEarlierBarDecisions(criteria).keySet());
        assertEquals(dates.size(), dates2.size());
        for (Date d : dates) {
            assertTrue(dates2.contains(d));
        }

        List<Date> dates3 = new ArrayList<>(service.getOccupancyForecast(criteria).keySet());
        assertEquals(dates.size(), dates3.size());
        for (Date d : dates) {
            assertTrue(dates3.contains(d));
        }

        List<Date> dates4 = new
                ArrayList<Date>(service.getRoomsOutOfOrder(criteria).keySet());
        assertEquals(dates.size(), dates4.size());
        for (Date d : dates) {
            assertTrue(dates4.contains(d));
        }

        List<Date> dates5 = new
                ArrayList<Date>(service.getCompetitorInfo(criteria).keySet());
        assertEquals(dates.size(), dates5.size());
        for (Date d : dates) {
            assertTrue(dates5.contains(d));
        }

        List<Date> dates6 = new
                ArrayList<Date>(service.getLastRoomValue(criteria).keySet());
        assertEquals(dates.size(), dates6.size());
        for (Date d : dates) {
            assertTrue(dates6.contains(d));
        }

    }

    @Test
    public void testFilterByBARRate() throws Exception {
        switchTenant();

        PacmanConfigParamsService mockConfigService = Mockito.mock(PacmanConfigParamsService.class);
        when(mockConfigService.isEnablePhysicalCapacityConsideration()).thenReturn(false);
        when(mockConfigService.getParameterValue(IPConfigParamName.BAR_BAR_DECISION.value())).thenReturn(BAR_DECISION_VALUE_LOS);
        when(mockConfigService.getParameterValue(IPConfigParamName.BAR_WEB_RATE_SHOPPING_ENABLED.value())).thenReturn("true");
        when(mockConfigService.getParameterValue(IPConfigParamName.BAR_WEB_RATE_STALENESS_THRESHOLD.value())).thenReturn("365");
        when(mockConfigService.getParameterValue(IPConfigParamName.BAR_BAR_OVRD_DISPLAY_COMPETITOR.value())).thenReturn(BAR_OVRD_DISPLAY_COMPETITOR_VALUE_HIGH);

        service.setConfigService(mockConfigService);
        sqlHelper.setConfigService(mockConfigService);
        PacmanWorkContextHelper.setPropertyId(PROPERTY_ID_BAR_BY_LOS);

        BARFilterCriteria criteria = new BARFilterCriteria();
        criteria.setAccomClassId(ACCOM_CLASS_ID_BAR_BY_LOS);
        criteria.setIncludedDaysOfWeek(Arrays.asList(new Integer[]{1, 2, 3, 4, 5, 6, 7}));
        criteria.setIncludedLengthsOfStay(Arrays.asList(new Integer[]{1}));

        criteria.setStartDate(dateService.getBARDisplayWindowStartDate());
        criteria.setEndDate(dateService.getBARDisplayWindowEndDate());

        criteria.setRateUnqualifiedID(14);
        criteria.setLastViewedTime(DateUtil.getFirstDayOfCurrentMonth());

        List<Date> dates = new ArrayList<Date>(service.getBarDecisions(criteria).keySet());
        assertTrue(dates.size() >= 14, "Expected at least four dates");
        assertTrue(dates.contains(DateUtil.getDateForCurrentMonth(2)));
        assertTrue(dates.contains(DateUtil.getDateForCurrentMonth(3)));
        assertTrue(dates.contains(DateUtil.getDateForCurrentMonth(17)));

        List<Date> dates2 = new ArrayList<Date>(service.getEarlierBarDecisions(criteria).keySet());
        assertEquals(dates.size(), dates2.size());
        for (Date d : dates) {
            assertTrue(dates2.contains(d));
        }

        List<Date> dates3 = new ArrayList<Date>(service.getOccupancyForecast(criteria).keySet());
        assertEquals(dates.size(), dates3.size());
        for (Date d : dates) {
            assertTrue(dates3.contains(d));
        }

        List<Date> dates4 = new
                ArrayList<Date>(service.getRoomsOutOfOrder(criteria).keySet());
        assertEquals(dates.size(), dates4.size());
        for (Date d : dates) {
            assertTrue(dates4.contains(d));
        }

        List<Date> dates5 = new
                ArrayList<Date>(service.getCompetitorInfo(criteria).keySet());
        assertEquals(dates.size(), dates5.size());
        for (Date d : dates) {
            assertTrue(dates5.contains(d));
        }
    }

    @Test
    public void testFilterByOccupancyForecast() throws Exception {
        switchTenant();

        PacmanConfigParamsService mockConfigService = Mockito.mock(PacmanConfigParamsService.class);
        when(mockConfigService.isEnablePhysicalCapacityConsideration()).thenReturn(false);
        when(mockConfigService.getParameterValue(IPConfigParamName.BAR_BAR_DECISION.value())).thenReturn(BAR_DECISION_VALUE_LOS);
        when(mockConfigService.getParameterValue(IPConfigParamName.BAR_WEB_RATE_SHOPPING_ENABLED.value())).thenReturn("true");
        when(mockConfigService.getParameterValue(IPConfigParamName.BAR_WEB_RATE_STALENESS_THRESHOLD.value())).thenReturn("365");
        when(mockConfigService.getParameterValue(IPConfigParamName.BAR_BAR_OVRD_DISPLAY_COMPETITOR.value())).thenReturn(BAR_OVRD_DISPLAY_COMPETITOR_VALUE_HIGH);

        service.setConfigService(mockConfigService);
        sqlHelper.setConfigService(mockConfigService);
        PacmanWorkContextHelper.setPropertyId(PROPERTY_ID_BAR_BY_LOS);

        BARFilterCriteria criteria = new BARFilterCriteria();
        criteria.setAccomClassId(ACCOM_CLASS_ID_BAR_BY_LOS);
        criteria.setIncludedDaysOfWeek(Arrays.asList(new Integer[]{1, 2, 3, 4, 5, 6, 7}));
        criteria.setIncludedLengthsOfStay(Arrays.asList(new Integer[]{1}));

        criteria.setStartDate(dateService.getBARDisplayWindowStartDate());
        criteria.setEndDate(dateService.getBARDisplayWindowEndDate());

        criteria.setOccupancyForecastGreaterThan(90);
        criteria.setLastViewedTime(DateUtil.getFirstDayOfCurrentMonth());

        List<Date> dates = new ArrayList<Date>(service.getBarDecisions(criteria).keySet());
        assertTrue(dates.size() >= 4, "Expected at least four dates");

        List<Date> dates2 = new ArrayList<Date>(service.getEarlierBarDecisions(criteria).keySet());
        assertEquals(dates.size(), dates2.size());
        for (Date d : dates) {
            assertTrue(dates2.contains(d));
        }

        List<Date> dates3 = new ArrayList<Date>(service.getOccupancyForecast(criteria).keySet());
        assertEquals(dates.size(), dates3.size());
        for (Date d : dates) {
            assertTrue(dates3.contains(d));
        }

        List<Date> dates4 = new
                ArrayList<Date>(service.getRoomsOutOfOrder(criteria).keySet());
        assertEquals(dates.size(), dates4.size());
        for (Date d : dates) {
            assertTrue(dates4.contains(d));
        }

        List<Date> dates5 = new
                ArrayList<Date>(service.getCompetitorInfo(criteria).keySet());
        assertEquals(dates.size(), dates5.size());
        for (Date d : dates) {
            assertTrue(dates5.contains(d));
        }
    }

    @Test
    public void testFilterByChangedSince() throws Exception {

        PacmanConfigParamsService mockConfigService = Mockito.mock(PacmanConfigParamsService.class);
        when(mockConfigService.isEnablePhysicalCapacityConsideration()).thenReturn(false);
        when(mockConfigService.getParameterValue(IPConfigParamName.BAR_BAR_DECISION.value())).thenReturn(BAR_DECISION_VALUE_LOS);
        when(mockConfigService.getParameterValue(IPConfigParamName.BAR_WEB_RATE_SHOPPING_ENABLED.value())).thenReturn("true");
        when(mockConfigService.getParameterValue(IPConfigParamName.BAR_WEB_RATE_STALENESS_THRESHOLD.value())).thenReturn("365");
        when(mockConfigService.getParameterValue(IPConfigParamName.BAR_BAR_OVRD_DISPLAY_COMPETITOR.value())).thenReturn(BAR_OVRD_DISPLAY_COMPETITOR_VALUE_HIGH);

        service.setConfigService(mockConfigService);
        sqlHelper.setConfigService(mockConfigService);
        PacmanWorkContextHelper.setPropertyId(PROPERTY_ID_BAR_BY_LOS);

        BARFilterCriteria criteria = new BARFilterCriteria();
        criteria.setAccomClassId(ACCOM_CLASS_ID_BAR_BY_LOS);
        criteria.setIncludedDaysOfWeek(Arrays.asList(new Integer[]{1, 2, 3, 4, 5, 6, 7}));
        criteria.setIncludedLengthsOfStay(Arrays.asList(new Integer[]{1}));

        criteria.setStartDate(dateService.getBARDisplayWindowStartDate());
        criteria.setEndDate(dateService.getBARDisplayWindowEndDate());

        criteria.setIncludeChangedOnly(true);
        Date lastViewed = new Date(DateUtil.getCurrentDateWithoutTime().getTime() - (86400000 * 1));
        criteria.setLastViewedTime(lastViewed);

        List<Date> dates = new ArrayList<Date>(service.getBarDecisions(criteria).keySet());
        assertTrue(dates.size() >= 0, "Expected at least one date");

        List<Date> dates2 = new ArrayList<Date>(service.getEarlierBarDecisions(criteria).keySet());
        assertEquals(dates.size(), dates2.size());
        for (Date d : dates) {
            assertTrue(dates2.contains(d));
        }

        List<Date> dates3 = new ArrayList<Date>(service.getOccupancyForecast(criteria).keySet());
        assertEquals(dates.size(), dates3.size());
        for (Date d : dates) {
            assertTrue(dates3.contains(d));
        }

        List<Date> dates4 = new
                ArrayList<Date>(service.getRoomsOutOfOrder(criteria).keySet());
        assertEquals(dates.size(), dates4.size());
        for (Date d : dates) {
            assertTrue(dates4.contains(d));
        }

        List<Date> dates5 = new
                ArrayList<Date>(service.getCompetitorInfo(criteria).keySet());
        assertEquals(dates.size(), dates5.size());
        for (Date d : dates) {
            assertTrue(dates5.contains(d));
        }

    }

    @Test
    public void testFilterByConflictingOverrides() throws Exception {

        PacmanConfigParamsService mockConfigService = Mockito.mock(PacmanConfigParamsService.class);
        when(mockConfigService.isEnablePhysicalCapacityConsideration()).thenReturn(false);
        when(mockConfigService.getParameterValue(IPConfigParamName.BAR_BAR_DECISION.value())).thenReturn(BAR_DECISION_VALUE_LOS);
        when(mockConfigService.getParameterValue(IPConfigParamName.BAR_WEB_RATE_SHOPPING_ENABLED.value())).thenReturn("true");
        when(mockConfigService.getParameterValue(IPConfigParamName.BAR_WEB_RATE_STALENESS_THRESHOLD.value())).thenReturn("365");
        when(mockConfigService.getParameterValue(IPConfigParamName.BAR_BAR_OVRD_DISPLAY_COMPETITOR.value())).thenReturn(BAR_OVRD_DISPLAY_COMPETITOR_VALUE_HIGH);

        service.setConfigService(mockConfigService);
        sqlHelper.setConfigService(mockConfigService);
        PacmanWorkContextHelper.setPropertyId(PROPERTY_ID_BAR_BY_LOS);

        BARFilterCriteria criteria = new BARFilterCriteria();
        criteria.setAccomClassId(ACCOM_CLASS_ID_BAR_BY_LOS);
        criteria.setIncludedDaysOfWeek(Arrays.asList(new Integer[]{1, 2, 3, 4, 5, 6, 7}));
        criteria.setIncludedLengthsOfStay(Arrays.asList(new Integer[]{1}));

        criteria.setStartDate(dateService.getBARDisplayWindowStartDate());
        criteria.setEndDate(dateService.getBARDisplayWindowEndDate());

        criteria.setOnlyShowDatesWithOverridesConflictingRateStrategy(true);

        List<Date> dates = new ArrayList<Date>(service.getBarDecisions(criteria).keySet());
        // TODO: Assert against known data when there is relevant sample data

        List<Date> dates2 = new ArrayList<Date>(service.getEarlierBarDecisions(criteria).keySet());
        assertEquals(dates.size(), dates2.size());
        for (Date d : dates) {
            assertTrue(dates2.contains(d));
        }

        List<Date> dates3 = new ArrayList<Date>(service.getOccupancyForecast(criteria).keySet());
        assertEquals(dates.size(), dates3.size());
        for (Date d : dates) {
            assertTrue(dates3.contains(d));
        }

        List<Date> dates4 = new
                ArrayList<Date>(service.getRoomsOutOfOrder(criteria).keySet());
        assertEquals(dates.size(), dates4.size());
        for (Date d : dates) {
            assertTrue(dates4.contains(d));
        }

        List<Date> dates5 = new
                ArrayList<Date>(service.getCompetitorInfo(criteria).keySet());
        assertEquals(dates.size(), dates5.size());
        for (Date d : dates) {
            assertTrue(dates5.contains(d));
        }

    }

    @Test
    public void testGetRatePlanIds() {
        Map<String, Integer> obj = service.getRatePlanIds();
        assertNotNull(obj);
        List<RateUnqualified> headers = tenantCrudService().findAll(RateUnqualified.class);
        assertEquals(headers.size() - 1, obj.size());
    }

    @Test
    public void testGetNonDecisions() throws Exception {
        Date startDate = dateService.getBARDisplayWindowStartDate();
        Date endDate = dateService.getBARDisplayWindowEndDate();

        PacmanConfigParamsService mockConfigService = mock(PacmanConfigParamsService.class);
        when(mockConfigService.getBooleanParameterValue(FeatureTogglesConfigParamName.CEILING_OVERRIDE_ENABLED.value())).thenReturn(false);

        service.setConfigService(mockConfigService);

        List<BARDecisionInfo> result = service.getNonDecisions(startDate, endDate);
        assertNotNull(result);

        assertEquals(0, result.size());
    }

    @Test
    public void testGetArrivalOrOccupancyDemandOverride() {
        Date startDate = dateService.getBARDisplayWindowStartDate();
        Date endDate = dateService.getBARDisplayWindowEndDate();
        WorkContextType wc = new WorkContextType();
        wc.setPropertyId(5);
        PacmanThreadLocalContextHolder.put(CONTEXT_KEY, wc);

        com.ideas.tetris.pacman.services.demandoverride.entity.ArrivalDemandOverride obj = new com.ideas.tetris.pacman.services.demandoverride.entity.ArrivalDemandOverride();
        obj.setAccomClassID(4);
        obj.setDecisionID(1);
        obj.setArrivalDate(startDate);
        obj.setForecastGroupId(1);
        obj.setLengthOfStay(-1);
        obj.setOverrideValue(new BigDecimal("34"));
        obj.setPropertyID(5);
        obj.setRateUnqualified(tenantCrudService().find(RateUnqualified.class, tenantCrudService().findByNamedQuerySingleResult(RateUnqualified.GET_ACTIVE_RATE_UNQUALIFIED_ID_BY_NAME, QueryParameter.with("name", "LV0").parameters())));
        obj.setRemainingDemand(new BigDecimal("36"));
        obj.setStatusId(1);
        obj.setCreateDate(startDate);
        obj.setCreatedByUserId(16547); // BLKSTN User
        obj = tenantCrudService().save(obj);

        OccupancyDemandOverride objOccupancyOverride = new OccupancyDemandOverride();
        objOccupancyOverride.setAccomClassID(4);
        objOccupancyOverride.setCreateDate(startDate);
        objOccupancyOverride.setDecisionID(1);
        objOccupancyOverride.setForecastGroupId(1);
        objOccupancyOverride.setOccupancyDate(startDate);
        objOccupancyOverride.setOverrideValue(new BigDecimal("34"));
        objOccupancyOverride.setPropertyID(5);
        objOccupancyOverride.setRateUnqualified(tenantCrudService().find(RateUnqualified.class, tenantCrudService().findByNamedQuerySingleResult(RateUnqualified.GET_ACTIVE_RATE_UNQUALIFIED_ID_BY_NAME, QueryParameter.with("name", "LV0").parameters())));
        objOccupancyOverride.setRemainingDemand(new BigDecimal("36"));
        objOccupancyOverride.setRoomsSold(new BigDecimal("11"));
        objOccupancyOverride.setStatusId(1);
        objOccupancyOverride.setCreatedByUserId(new Integer(16547)); // BLKSTN User
        objOccupancyOverride = tenantCrudService().save(objOccupancyOverride);

        Map<Date, BARDecisionInfo> map = service.getArrivalOrOccupancyDemandOverride(startDate, endDate);
        Map<Date, BARDecisionInfo> newMap = service.getArrivalOrOccupancyDemandOverride(startDate, endDate);
        assertNotNull(map);

    }

    @Test
    public void testGetBarDecisionWithConflictingOverrides() {
        Date startDate = dateService.getBARDisplayWindowStartDate();
        Date endDate = dateService.getBARDisplayWindowEndDate();
        WorkContextType wc = new WorkContextType();
        wc.setPropertyId(5);
        PacmanThreadLocalContextHolder.put(CONTEXT_KEY, wc);
        Map<Date, BARDecisionInfo> map = service.getBarDecisionWithConflictingOverrides(4, -1, startDate, endDate);
        assertNotNull(map);
    }

    @Test
    public void testGetAbsoluteCompetitorAlias() {
        WorkContextType wc = new WorkContextType();
        wc.setPropertyId(5);
        PacmanThreadLocalContextHolder.put(CONTEXT_KEY, wc);

        PacmanConfigParamsService mockConfigService = Mockito.mock(PacmanConfigParamsService.class);
        when(mockConfigService.isEnablePhysicalCapacityConsideration()).thenReturn(false);
        when(mockConfigService.getParameterValue(IPConfigParamName.BAR_BAR_DECISION.value())).thenReturn(BAR_DECISION_VALUE_LOS);
        when(mockConfigService.getParameterValue(IPConfigParamName.BAR_WEB_RATE_SHOPPING_ENABLED.value())).thenReturn("true");
        when(mockConfigService.getParameterValue(IPConfigParamName.BAR_WEB_RATE_STALENESS_THRESHOLD.value())).thenReturn("365");
        when(mockConfigService.getParameterValue(IPConfigParamName.BAR_BAR_OVRD_DISPLAY_COMPETITOR.value())).thenReturn(BAR_OVRD_DISPLAY_COMPETITOR_VALUE_ABSOLUTE);
        when(mockConfigService.getParameterValue(IPConfigParamName.BAR_BAR_OVRD_ABSOLUTE_COMPETITOR.value())).thenReturn("Luxor");

        service.setConfigService(mockConfigService);
        String ObjAbsoluteCompAlias = service.getAbsoluteCompetitorAlias();
        assertEquals(mockConfigService.getParameterValue(IPConfigParamName.BAR_BAR_OVRD_ABSOLUTE_COMPETITOR.value()), ObjAbsoluteCompAlias);
    }


    @Test
    public void testGetWashOverrides() {
        Date startDate = dateService.getBARDisplayWindowStartDate();
        Date endDate = dateService.getBARDisplayWindowEndDate();
        WorkContextType wc = new WorkContextType();
        wc.setPropertyId(5);
        PacmanThreadLocalContextHolder.put(CONTEXT_KEY, wc);
        Map<Date, BARDecisionInfo> resultMap = service.getWashOverrides(startDate, endDate);
        assertNotNull(resultMap);
    }

    @Test
    public void testNonDecisionPresent() {
        Date startDate = dateService.getBARDisplayWindowStartDate();
        Date endDate = dateService.getBARDisplayWindowEndDate();
        boolean isPresent = service.nonDecisionsPresent(startDate, endDate);
        assertNotNull(isPresent);
    }

    @Test
    public void testGetRoomsOnBooks() {
        BARFilterCriteria criteria = new BARFilterCriteria();
        criteria.setAccomClassId(3);
        criteria.setIncludedDaysOfWeek(Arrays.asList(new Integer[]{1, 2, 3, 4, 5, 6, 7}));
        criteria.setIncludedLengthsOfStay(Arrays.asList(new Integer[]{1}));

        criteria.setStartDate(dateService.getBARDisplayWindowStartDate());
        criteria.setEndDate(dateService.getBARDisplayWindowEndDate());

        criteria.setIncludeChangedOnly(true);
        Date lastViewed = new Date(DateUtil.getCurrentDateWithoutTime().getTime() - (86400000 * 1));
        criteria.setLastViewedTime(lastViewed);
        Map<Date, BigDecimal> map = service.getRoomsOnBooks(criteria);
        assertNotNull(map);
    }

    @Test
    public void testGetRoomsOnBooks_CR() {
        BARFilterCriteria criteria = new BARFilterCriteria();
        criteria.setAccomClassId(3);
        Date startDate = DateUtil.getFirstDayOfCurrentMonth();
        criteria.setStartDate(startDate);
        criteria.setEndDate(startDate);
        assertOnBooks(criteria, startDate, 186);
        tenantCrudService().executeUpdateByNativeQuery("update Accom_Type set isComponentRoom='Y' where Accom_Type_ID=7");
        assertOnBooks(criteria, startDate, 146);
    }

    private void assertOnBooks(BARFilterCriteria criteria, Date startDate, int value) {
        Map<Date, BigDecimal> map = service.getRoomsOnBooks(criteria);
        BigDecimal onBooks = map.get(startDate);
        assertEquals(value, onBooks.intValue());
    }

    @Test
    public void testGetCompetitorInfoWithCompetitorIdParam() throws Exception {
        PacmanConfigParamsService configService = new PacmanConfigParamsService() {
            @Override
            public String getParameterValue(String parameterName) {
                if (parameterName.equals(IPConfigParamName.BAR_WEB_RATE_SHOPPING_ENABLED.value())) {
                    return "true";
                }
                if (parameterName.equals(IPConfigParamName.BAR_BAR_OVRD_DISPLAY_COMPETITOR.value())) {
                    return BAR_OVRD_DISPLAY_COMPETITOR_VALUE_HIGH;
                }
                return null;
            }

            @Override
            public <T> T getParameterValue(ConfigParamName configParamName, String... params) {
                if (configParamName.equals(IPConfigParamName.BAR_WEB_RATE_ALIAS)) {
                    return null;
                }
                return null;
            }
        };
        service.setConfigService(configService);
        sqlHelper.setConfigService(configService);

        Map<Date, CompetitorInfo> result = service.getCompetitorInfo(
                ACCOM_CLASS_ID_RATE_OF_DAY, -1, dateService
                        .getBARDisplayWindowStartDate(), dateService
                        .getBARDisplayWindowEndDate());
        assertNotNull(result);

        assertEquals(DateUtil.getNumberOfDaysInThisMonthAndNext(), result.size());

        List<Date> keys = new ArrayList<Date>(result.keySet());
        Collections.sort(keys);

        // Check the first few results to see if they match known expected
        // values
        assertTrue(result.get(keys.get(0)).getCompetitorNames().contains("Luxor"));
        assertTrue(result.get(keys.get(0)).getCompetitorIds().contains(3));
        assertEquals("190.00", result.get(keys.get(0)).getCompetitorPrice());

        Tax tax = tenantCrudService().findOne(Tax.class);
        tax.setRoomTaxRate(BigDecimal.TEN);
        tenantCrudService().flushAndClear();

        result = service.getCompetitorInfo(
                ACCOM_CLASS_ID_RATE_OF_DAY, -1, dateService
                        .getBARDisplayWindowStartDate(), dateService
                        .getBARDisplayWindowEndDate());
        assertEquals("190.00", result.get(keys.get(0)).getCompetitorPrice());
    }

    @Test
    public void testGetCompetitorInfoMapWithCompetitorIdParam() throws Exception {
        PacmanConfigParamsService configService = new PacmanConfigParamsService() {
            @Override
            public String getParameterValue(String parameterName) {
                if (parameterName.equals(IPConfigParamName.BAR_WEB_RATE_SHOPPING_ENABLED.value())) {
                    return "true";
                }
                if (parameterName.equals(IPConfigParamName.BAR_BAR_OVRD_DISPLAY_COMPETITOR.value())) {
                    return BAR_OVRD_DISPLAY_COMPETITOR_VALUE_HIGH;
                }
                return null;
            }

            @Override
            public <T> T getParameterValue(ConfigParamName configParamName, String... params) {
                if (configParamName.equals(IPConfigParamName.BAR_WEB_RATE_ALIAS)) {
                    return null;
                }
                return null;
            }
        };
        service.setConfigService(configService);
        sqlHelper.setConfigService(configService);

        Map<Integer, Map<Date, CompetitorInfo>> resultMap = service.getCompetitorInfoMap(-1, dateService
                .getBARDisplayWindowStartDate(), dateService
                .getBARDisplayWindowEndDate());
        Map<Date, CompetitorInfo> result = resultMap.get(ACCOM_CLASS_ID_RATE_OF_DAY);
        assertNotNull(result);

        assertEquals(DateUtil.getNumberOfDaysInThisMonthAndNext(), result.size());

        List<Date> keys = new ArrayList<Date>(result.keySet());
        Collections.sort(keys);

        // Check the first few results to see if they match known expected
        // values
        assertTrue(result.get(keys.get(0)).getCompetitorNames().contains("Luxor"));
        assertTrue(result.get(keys.get(0)).getCompetitorIds().contains(3));
        assertEquals("190.00", result.get(keys.get(0)).getCompetitorPrice());

        Tax tax = tenantCrudService().findOne(Tax.class);
        tax.setRoomTaxRate(BigDecimal.TEN);
        tenantCrudService().flushAndClear();

        resultMap = service.getCompetitorInfoMap(-1, dateService
                .getBARDisplayWindowStartDate(), dateService
                .getBARDisplayWindowEndDate());
        result = resultMap.get(ACCOM_CLASS_ID_RATE_OF_DAY);
        assertEquals("190.00", result.get(keys.get(0)).getCompetitorPrice());
    }

    @Test
    public void testGetCompetitorInfoWithZeroStaleness() throws Exception {
        PacmanConfigParamsService configService = new PacmanConfigParamsService() {
            @Override
            public String getParameterValue(String parameterName) {
                if (parameterName.equals(IPConfigParamName.BAR_WEB_RATE_SHOPPING_ENABLED.value())) {
                    return "true";
                }
                if (parameterName.equals(IPConfigParamName.BAR_WEB_RATE_STALENESS_THRESHOLD.value())) {
                    return "0";
                }
                if (parameterName.equals(IPConfigParamName.BAR_BAR_OVRD_DISPLAY_COMPETITOR.value())) {
                    return BAR_OVRD_DISPLAY_COMPETITOR_VALUE_HIGH;
                }

                return null;
            }

            @Override
            public <T> T getParameterValue(ConfigParamName configParamName, String... params) {
                if (configParamName.equals(IPConfigParamName.BAR_WEB_RATE_ALIAS)) {
                    return null;
                }
                return null;
            }

        };
        service.setConfigService(configService);
        sqlHelper.setConfigService(configService);

        Map<Date, CompetitorInfo> result = service.getCompetitorInfo(
                ACCOM_CLASS_ID_RATE_OF_DAY, -1, dateService
                        .getBARDisplayWindowStartDate(), dateService
                        .getBARDisplayWindowEndDate());
        assertNotNull(result);
    }


    @Test
    public void testGetCompetitorInfoMapWithZeroStaleness() throws Exception {
        PacmanConfigParamsService configService = new PacmanConfigParamsService() {
            @Override
            public String getParameterValue(String parameterName) {
                if (parameterName.equals(IPConfigParamName.BAR_WEB_RATE_SHOPPING_ENABLED.value())) {
                    return "true";
                }
                if (parameterName.equals(IPConfigParamName.BAR_WEB_RATE_STALENESS_THRESHOLD.value())) {
                    return "0";
                }
                if (parameterName.equals(IPConfigParamName.BAR_BAR_OVRD_DISPLAY_COMPETITOR.value())) {
                    return BAR_OVRD_DISPLAY_COMPETITOR_VALUE_HIGH;
                }

                return null;
            }

            @Override
            public <T> T getParameterValue(ConfigParamName configParamName, String... params) {
                if (configParamName.equals(IPConfigParamName.BAR_WEB_RATE_ALIAS)) {
                    return null;
                }
                return null;
            }

        };
        service.setConfigService(configService);
        sqlHelper.setConfigService(configService);

        Map<Integer, Map<Date, CompetitorInfo>> resultMap = service.getCompetitorInfoMap(-1, dateService
                .getBARDisplayWindowStartDate(), dateService
                .getBARDisplayWindowEndDate());
        Map<Date, CompetitorInfo> result = resultMap.get(ACCOM_CLASS_ID_RATE_OF_DAY);
        assertNotNull(result);
    }


    @Test
    public void testGetBarDecisionWithOverrideConflictsForAllLos() {

        WorkContextType wc = new WorkContextType();
        wc.setPropertyId(5);
        PacmanThreadLocalContextHolder.put(CONTEXT_KEY, wc);
        BARFilterCriteria criteria = new BARFilterCriteria();
        criteria.setAccomClassId(ACCOM_CLASS_ID_BAR_BY_LOS);
        criteria.setIncludedDaysOfWeek(Arrays.asList(new Integer[]{1, 2, 3,
                4, 5, 6, 7}));
        criteria.setIncludedLengthsOfStay(Arrays.asList(new Integer[]{1, 2,
                3, 4, 5, 6, 7, 8}));

        criteria.setStartDate(dateService.getBARDisplayWindowStartDate());
        criteria.setEndDate(dateService.getBARDisplayWindowEndDate());
        criteria.setOnlyShowDatesWithOverridesConflictingRateStrategy(true);


        Map<Date, List<Integer>> map = service.getBarDecisionsWithOverrideConflicts(criteria);
        assertNotNull(map);
    }

    @Test
    public void testGetBARDetailsByDateRange() throws Exception {
        tenantCrudService().getEntityManager().createNativeQuery("update Rate_unqualified set status_id = 2").executeUpdate();
        AccomClass accomClass = UniqueAccomClassCreator.createUniqueAccomClassForPropertyID(PROPERTY_ID_RATEOFDAY, 0);
        AccomType accomType = UniqueAccomTypeCreator.createUniqueAccomTypeForAccomClass(PROPERTY_ID_RATEOFDAY, accomClass);
        Date currentDate = DateUtil.getCurrentDate();
        RateUnqualified rateUnqualified = UniqueRateUnqualified.createRateUnqualifiedByDate(currentDate, DateUtil.addDaysToDate(currentDate, 20));
        UniqueRateUnqualifiedDetails.createRateUnqualifiedDetailsForRateUnqualified(accomType.getId(), rateUnqualified.getId(), currentDate, DateUtil.addDaysToDate(currentDate, 5), 10f);
        UniqueRateUnqualifiedDetails.createRateUnqualifiedDetailsForRateUnqualified(accomType.getId(), rateUnqualified.getId(), DateUtil.addDaysToDate(currentDate, 7), DateUtil.addDaysToDate(currentDate, 10), 10f);
        RateUnqualified rateUnqualified1 = UniqueRateUnqualified.createRateUnqualifiedByDate(currentDate, DateUtil.addDaysToDate(currentDate, 20));
        UniqueRateUnqualifiedDetails.createRateUnqualifiedDetailsForRateUnqualified(accomType.getId(), rateUnqualified1.getId(), currentDate, DateUtil.addDaysToDate(currentDate, 5), 10f);
        UniqueRateUnqualifiedDetails.createRateUnqualifiedDetailsForRateUnqualified(accomType.getId(), rateUnqualified1.getId(), DateUtil.addDaysToDate(currentDate, 7), DateUtil.addDaysToDate(currentDate, 10), 10f);
        RateUnqualified rateUnqualified3 = UniqueRateUnqualified.createRateUnqualifiedByDate(currentDate, DateUtil.addDaysToDate(currentDate, 20));
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        tenantCrudService().getEntityManager().flush();
        createUnqualifiedDemandFcstPriceData(rateUnqualified.getId(), accomClass.getId(), dateFormat.format(rateUnqualified.getStartDate()));
        createUnqualifiedDemandFcstPriceData(rateUnqualified1.getId(), accomClass.getId(), dateFormat.format(rateUnqualified.getStartDate()));
        when(mockedCloseHighestBarService.isClosedForHighestBar(accomClass.getId(),
                DateUtil.getFirstDayOfCurrentMonth(), DateUtil.getLastDayOfCurrentMonth(), -1)).thenReturn(true);

        BARDetails result = service.getBARDetailsByDateRange(accomClass.getId(),
                DateUtil.getFirstDayOfCurrentMonth(), DateUtil.getLastDayOfCurrentMonth());
        assertNotNull(result);

        assertEquals(2, result.getPriceMapByRateUnqualifiedName().size());
        assertEquals(2, result.getRemainingDemandByRateUnqualifiedName().size());
        assertTrue(result.getRestrictHighestBARSelected());
    }

    private void createUnqualifiedDemandFcstPriceData(Integer rateUnqualifiedId, Integer accomClassId,
                                                      String arrivalDateString) {
        String query = "INSERT INTO [Unqualified_Demand_FCST_Price]" +
                " ([Decision_ID]  ,[Property_ID] ,[Arrival_DT] ,[Accom_Class_ID]  ,[Rate_Unqualified_ID] ,[LOS] ,[Remaining_Demand] ,[CreateDate_DTTM])" +
                " select max(decision_id)," + PROPERTY_ID_RATEOFDAY + " ,'" + arrivalDateString + "'," + accomClassId + "," + rateUnqualifiedId + ", -1 , 1.0, getDate() from decision ";
        tenantCrudService().getEntityManager().createNativeQuery(query).executeUpdate();
    }

    @SuppressWarnings({"rawtypes", "unchecked"})
    @Test
    public void testGetUnavailableBarDecisionDates() {
        System.setProperty("pacman.pricingmanagement.isOverrideForValidRateEnabled.enabled", String.valueOf("false"));

        AccomClass accomClass = UniqueAccomClassCreator.createUniqueAccomClassForPropertyID(PROPERTY_ID_RATEOFDAY, 0);
        AccomType accomType = UniqueAccomTypeCreator.createUniqueAccomTypeForAccomClass(PROPERTY_ID_RATEOFDAY, accomClass);
        Date currentDate = DateUtil.getCurrentDate();
        RateUnqualified rateUnqualified = UniqueRateUnqualified.createRateUnqualifiedByDate(currentDate, DateUtil.addDaysToDate(currentDate, 20));
        UniqueRateUnqualifiedDetails.createRateUnqualifiedDetailsForRateUnqualified(accomType.getId(), rateUnqualified.getId(), currentDate, DateUtil.addDaysToDate(currentDate, 5), 10f);
        UniqueRateUnqualifiedDetails.createRateUnqualifiedDetailsForRateUnqualified(accomType.getId(), rateUnqualified.getId(), DateUtil.addDaysToDate(currentDate, 8), DateUtil.addDaysToDate(currentDate, 10), 10f);

        //insert data for los -1
        UniqueUnqualifiedDemandForecastPrice.createUnqualifiedDemandForecast(accomClass.getId(), currentDate, -1, 5, rateUnqualified, new BigDecimal(100.12));

        BAROvrUnavailableDtRangeAndPriceDetails availableRates = service.getUnavailableBarDecisionDates(accomClass.getId(), rateUnqualified.getId(), null, currentDate, DateUtil.addDaysToDate(currentDate, 30), Arrays.asList(-1));
        assertNotNull(availableRates);
        assertEquals(2, availableRates.getUnavailableDateRanges().size());

        Map resultArr = availableRates.getUnavailableDateRanges().get(0);

        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        Date startDate = null;
        Date endDate = null;
        startDate = (Date) resultArr.get("startDate");
        endDate = (Date) resultArr.get("endDate");

        assertEquals(1, DateUtil.getDateDiffDays(DateUtil.getDateTimeByTimeZone(endDate, TimeZone.getDefault(), TimeZone.getTimeZone("UTC")).getTime(), DateUtil.getDateTimeByTimeZone(startDate, TimeZone.getDefault(), TimeZone.getTimeZone("UTC")).getTime()));
    }

    @SuppressWarnings({"rawtypes", "unchecked"})
    @Test
    public void testGetUnavailableBarDecisionDatesNew() throws ParseException {
        System.setProperty("pacman.pricingmanagement.isOverrideForValidRateEnabled.enabled", String.valueOf("true"));
        AccomClass accomClass = UniqueAccomClassCreator.createUniqueAccomClassForPropertyID(PROPERTY_ID_RATEOFDAY, 0);
        AccomType accomType = UniqueAccomTypeCreator.createUniqueAccomTypeForAccomClass(PROPERTY_ID_RATEOFDAY, accomClass);
        Date currentDate = DateUtil.getCurrentDateWithoutTime();
        RateUnqualified rateUnqualified = UniqueRateUnqualified.createRateUnqualifiedByDate(currentDate, DateUtil.addDaysToDate(currentDate, 20));
        UniqueRateUnqualifiedDetails.createRateUnqualifiedDetailsForRateUnqualified(accomType.getId(), rateUnqualified.getId(), currentDate, DateUtil.addDaysToDate(currentDate, 5), 10f);
        UniqueRateUnqualifiedDetails.createRateUnqualifiedDetailsForRateUnqualified(accomType.getId(), rateUnqualified.getId(), DateUtil.addDaysToDate(currentDate, 8), DateUtil.addDaysToDate(currentDate, 10), 10f);

        // insert data for los -1
        UniqueUnqualifiedDemandForecastPrice.createUnqualifiedDemandForecast(accomClass.getId(), DateUtil.addDaysToDate(currentDate, 2), -1, PROPERTY_ID_RATEOFDAY, rateUnqualified, new BigDecimal(100.12));

        BAROvrUnavailableDtRangeAndPriceDetails availableRates = service.getUnavailableBarDecisionDates(accomClass.getId(), rateUnqualified.getId(), null, currentDate, DateUtil.addDaysToDate(currentDate, 30), Arrays.asList(-1));
        assertNotNull(availableRates);
        assertEquals(2, availableRates.getUnavailableRateListByDateRangeAndLOS().size());

        Map resultArr = availableRates.getUnavailableRateListByDateRangeAndLOS().get(0);
        SimpleDateFormat simpleDateFormat2 = new SimpleDateFormat("MM/dd/yyyy");
        List<String> los = (List<String>) resultArr.get(simpleDateFormat2.format(currentDate) + " - " + simpleDateFormat2.format(DateUtil.addDaysToDate(currentDate, 1)));
        assertEquals(-1, los.get(0));
        Map resultArr1 = availableRates.getUnavailableRateListByDateRangeAndLOS().get(1);
        List<String> los1 = (List<String>) resultArr1.get(simpleDateFormat2.format(DateUtil.addDaysToDate(currentDate, 3)) + " - " + simpleDateFormat2.format(DateUtil.addDaysToDate(currentDate, 30)));
        assertEquals(-1, los1.get(0));
    }

    @Test
    public void shouldGetLraAffectedDateRange() {
        List<Integer> los = new ArrayList<Integer>();
        los.add(-1);
        Date currentDate = DateUtil.getCurrentDateWithoutTime();
        Decision decision = new Decision();
        decision.setBusinessDate(DateUtil.addDaysToDate(currentDate, 1));
        decision.setCaughtUpDate(DateUtil.addDaysToDate(currentDate, 1));
        decision.setRateUnqualifiedDate(currentDate);
        decision.setWebRateDate(currentDate);
        decision.setDecisionTypeId(1);
        decision.setStartDate(DateUtil.addDaysToDate(currentDate, 1));
        decision.setEndDate(DateUtil.addDaysToDate(currentDate, 1));
        decision.setProcessStatusId(2);
        decision.setPropertyID(PROPERTY_ID_RATEOFDAY);
        decision = tenantCrudService().save(decision);

        RateUnqualified rateUnqualified = UniqueRateUnqualified.createRateUnqualifiedByDate(currentDate, DateUtil.addDaysToDate(currentDate, 20));

        DecisionBAROutput decisionBarOutput = new DecisionBAROutput();
        decisionBarOutput.setAccomClassId(2);
        decisionBarOutput.setArrivalDate(currentDate);
        decisionBarOutput.setDecision(decision);
        decisionBarOutput.setPropertyID(PROPERTY_ID_RATEOFDAY);
        decisionBarOutput.setRateUnqualified(rateUnqualified);
        decisionBarOutput.setLengthOfStay(-1);
        decisionBarOutput.setOverride("None");
        decisionBarOutput.setReasonTypeId(6);
        decisionBarOutput.setCreateDate(DateUtil.getCurrentDate());
        decisionBarOutput = tenantCrudService().save(decisionBarOutput);

        List<LRAAffectedDateRange> lraAffectedDateRanges = service.getLraAffectedDateRange(2, DateParameter.fromDate(currentDate), DateParameter.fromDate(currentDate), los);
        assertNotNull(lraAffectedDateRanges);
        for (LRAAffectedDateRange lraAffectedDateRange : lraAffectedDateRanges) {
            assertTrue(-1 == lraAffectedDateRange.getLos());
            assertTrue(lraAffectedDateRange.getStartDate().compareTo(currentDate) == 0);

            assertTrue(lraAffectedDateRange.getEndDate().compareTo(currentDate) == 0);
        }
    }


    @Test
    public void shouldGetLraAffectedDateRangeForBARByLos() {

        setWorkContextProperty(TestProperty.H2);
        List<Integer> los = new ArrayList<Integer>();
        los.add(4);
        Date currentDate = DateUtil.getCurrentDateWithoutTime();
        Decision decision = new Decision();
        decision.setBusinessDate(DateUtil.addDaysToDate(currentDate, 1));
        decision.setCaughtUpDate(DateUtil.addDaysToDate(currentDate, 1));
        decision.setRateUnqualifiedDate(currentDate);
        decision.setWebRateDate(currentDate);
        decision.setDecisionTypeId(1);
        decision.setStartDate(DateUtil.addDaysToDate(currentDate, 1));
        decision.setEndDate(DateUtil.addDaysToDate(currentDate, 1));
        decision.setProcessStatusId(2);
        decision.setPropertyID(PROPERTY_ID_BAR_BY_LOS);
        decision = tenantCrudService().save(decision);

        RateUnqualified rateUnqualified = UniqueRateUnqualified.createRateUnqualifiedByDate(currentDate, DateUtil.addDaysToDate(currentDate, 20), PROPERTY_ID_BAR_BY_LOS);


        DecisionBAROutput decisionBarOutput = new DecisionBAROutput();
        decisionBarOutput.setAccomClassId(7);
        decisionBarOutput.setArrivalDate(currentDate);
        decisionBarOutput.setDecision(decision);
        decisionBarOutput.setPropertyID(PROPERTY_ID_BAR_BY_LOS);
        decisionBarOutput.setRateUnqualified(rateUnqualified);
        decisionBarOutput.setLengthOfStay(4);
        decisionBarOutput.setOverride("None");
        decisionBarOutput.setReasonTypeId(6);
        decisionBarOutput.setCreateDate(DateUtil.getCurrentDate());
        decisionBarOutput = tenantCrudService().save(decisionBarOutput);

        List<LRAAffectedDateRange> lraAffectedDateRanges = service.getLraAffectedDateRange(7, DateParameter.fromDate(currentDate), DateParameter.fromDate(currentDate), los);
        assertNotNull(lraAffectedDateRanges);
        for (LRAAffectedDateRange lraAffectedDateRange : lraAffectedDateRanges) {
            assertTrue(4 == lraAffectedDateRange.getLos());
            assertTrue(lraAffectedDateRange.getStartDate().compareTo(currentDate) == 0);

            assertTrue(lraAffectedDateRange.getEndDate().compareTo(currentDate) == 0);
        }
    }

    @Test
    public void getBarDecisionsIncludesCeiling() throws Exception {
        int lengthOfStay = -1;
        int accomClassId = 2;
        doReturn(false).when(configService).getBooleanParameterValue(FeatureTogglesConfigParamName.HIGHEST_BAR_RESTRICTED_ENABLED.value());

        PacmanConfigParamsService mockConfigService = mock(PacmanConfigParamsService.class);
        when(mockConfigService.getBooleanParameterValue(FeatureTogglesConfigParamName.CEILING_OVERRIDE_ENABLED.value())).thenReturn(false);

        service.setConfigService(mockConfigService);

        Date arrivalDate = DateUtil.getCurrentDateWithoutTime();
        DateParameter startDate = new DateParameter(DateUtil.addDaysToDate(arrivalDate, -1));
        DateParameter endDate = new DateParameter(DateUtil.addDaysToDate(arrivalDate, 1));

        Integer ceilingId = tenantCrudService().findByNamedQuerySingleResult(
                RateUnqualified.GET_ACTIVE_RATE_UNQUALIFIED_ID_BY_NAME,
                QueryParameter.with("name", "LV1").parameters());
        RateUnqualified ceiling = tenantCrudService().find(RateUnqualified.class, ceilingId);

        QueryParameter params = QueryParameter.with("arrivalDate", arrivalDate)
                .and("accomClassId", accomClassId)
                .and("lengthOfStay", lengthOfStay);
        DecisionBAROutput barOutput = tenantCrudService().findByNamedQuerySingleResult(
                DecisionBAROutput.BY_ARRIVALDATE_AND_ACCOMCLASSID_AND_LOS, params.parameters());
        barOutput.setFloorRateUnqualified(null);
        barOutput.setOverride(BARDECISIONOVERRIDE_CEILING);
        barOutput.setCeilingRateUnqualified(ceiling);
        tenantCrudService().save(barOutput);

        DecisionBAROutputOverride barOutputOverride = new DecisionBAROutputOverride();
        barOutputOverride.setDecision(barOutput.getDecision());
        barOutputOverride.setPropertyId(barOutput.getPropertyID());
        barOutputOverride.setAccomClassId(barOutput.getAccomClassId());
        barOutputOverride.setArrivalDate(barOutput.getArrivalDate());
        barOutputOverride.setUserId(1);
        barOutputOverride.setLengthOfStay(barOutput.getLengthOfStay());

        barOutputOverride.setOldOverride(Constants.BARDECISIONOVERRIDE_NONE);
        barOutputOverride.setOldRateUnqualified(barOutput.getRateUnqualified());

        barOutputOverride.setNewOverride(Constants.BARDECISIONOVERRIDE_CEILING);
        barOutputOverride.setNewRateUnqualified(barOutput.getRateUnqualified());
        barOutputOverride.setNewFloorRateUnqualified(null);
        barOutputOverride.setNewCeilingRateUnqualified(ceiling);
        barOutputOverride.setCreateDate(new Date());

        tenantCrudService().save(barOutputOverride);
        tenantCrudService().flushAndClear();

        Map<Date, BARDecisionInfo> barDecisions = service.getBarDecisions(accomClassId,
                lengthOfStay, startDate, endDate);

        BARDecisionInfo barDecisionInfo = barDecisions.get(arrivalDate);
        assertEquals(ceiling.getId(), barDecisionInfo.getCeilingRatePlanId());
        assertEquals(ceiling.getName(), barDecisionInfo.getCeilingRatePlanName());
    }

    @Test
    public void getBarDecisionsIncludesSpecific() throws Exception {
        int lengthOfStay = -1;
        int accomClassId = 2;
        doReturn(false).when(configService).getBooleanParameterValue(FeatureTogglesConfigParamName.HIGHEST_BAR_RESTRICTED_ENABLED.value());

        PacmanConfigParamsService mockConfigService = mock(PacmanConfigParamsService.class);
        when(mockConfigService.getBooleanParameterValue(FeatureTogglesConfigParamName.CEILING_OVERRIDE_ENABLED.value())).thenReturn(false);

        service.setConfigService(mockConfigService);

        Date arrivalDate = DateUtil.getCurrentDateWithoutTime();
        DateParameter startDate = new DateParameter(DateUtil.addDaysToDate(arrivalDate, -1));
        DateParameter endDate = new DateParameter(DateUtil.addDaysToDate(arrivalDate, 1));

        Integer ceilingId = tenantCrudService().findByNamedQuerySingleResult(
                RateUnqualified.GET_ACTIVE_RATE_UNQUALIFIED_ID_BY_NAME,
                QueryParameter.with("name", "LV1").parameters());
        RateUnqualified specific = tenantCrudService().find(RateUnqualified.class, ceilingId);

        QueryParameter params = QueryParameter.with("arrivalDate", arrivalDate)
                .and("accomClassId", accomClassId)
                .and("lengthOfStay", lengthOfStay);
        DecisionBAROutput barOutput = tenantCrudService().findByNamedQuerySingleResult(
                DecisionBAROutput.BY_ARRIVALDATE_AND_ACCOMCLASSID_AND_LOS, params.parameters());
        barOutput.setRateUnqualified(specific);
        barOutput.setOverride(BARDECISIONOVERRIDE_USER);
        barOutput.setFloorRateUnqualified(null);
        barOutput.setCeilingRateUnqualified(null);
        tenantCrudService().save(barOutput);

        DecisionBAROutputOverride barOutputOverride = new DecisionBAROutputOverride();
        barOutputOverride.setDecision(barOutput.getDecision());
        barOutputOverride.setPropertyId(barOutput.getPropertyID());
        barOutputOverride.setAccomClassId(barOutput.getAccomClassId());
        barOutputOverride.setArrivalDate(barOutput.getArrivalDate());
        barOutputOverride.setUserId(1);
        barOutputOverride.setLengthOfStay(barOutput.getLengthOfStay());

        barOutputOverride.setOldOverride(Constants.BARDECISIONOVERRIDE_NONE);
        barOutputOverride.setOldRateUnqualified(barOutput.getRateUnqualified());

        barOutputOverride.setNewOverride(Constants.BARDECISIONOVERRIDE_USER);
        barOutputOverride.setNewRateUnqualified(specific);
        barOutputOverride.setNewFloorRateUnqualified(null);
        barOutputOverride.setNewCeilingRateUnqualified(null);
        barOutputOverride.setCreateDate(new Date());

        tenantCrudService().save(barOutputOverride);
        tenantCrudService().flushAndClear();

        Map<Date, BARDecisionInfo> barDecisions = service.getBarDecisions(accomClassId,
                lengthOfStay, startDate, endDate);

        BARDecisionInfo barDecisionInfo = barDecisions.get(arrivalDate);
        assertEquals(specific.getId(), barDecisionInfo.getSpecificRatePlanId());
        assertEquals(specific.getName(), barDecisionInfo.getSpecificRatePlanName());
    }

    @Test
    public void getBarDecisionsConvertsNoneRatesToNullRates() throws Exception {
        PacmanConfigParamsService mockConfigService = mock(PacmanConfigParamsService.class);
        when(mockConfigService.getBooleanParameterValue(FeatureTogglesConfigParamName.CEILING_OVERRIDE_ENABLED.value())).thenReturn(false);

        service.setConfigService(mockConfigService);

        // should convert if this is false
        doReturn(true).when(configService).getBooleanParameterValue(FeatureTogglesConfigParamName.CEILING_OVERRIDE_ENABLED.value());
        doReturn(false).when(configService).getBooleanParameterValue(FeatureTogglesConfigParamName.HIGHEST_BAR_RESTRICTED_ENABLED.value());

        int lengthOfStay = -1;
        int accomClassId = 2;
        Date arrivalDate = DateUtil.getCurrentDateWithoutTime();
        DateParameter startDate = new DateParameter(DateUtil.addDaysToDate(arrivalDate, -1));
        DateParameter endDate = new DateParameter(DateUtil.addDaysToDate(arrivalDate, 1));

        Integer noRateId = tenantCrudService().findByNamedQuerySingleResult(
                RateUnqualified.GET_ACTIVE_RATE_UNQUALIFIED_ID_BY_NAME,
                QueryParameter.with("name", "None").parameters());
        RateUnqualified none = tenantCrudService().find(RateUnqualified.class, noRateId);

        QueryParameter params = QueryParameter.with("arrivalDate", arrivalDate)
                .and("accomClassId", accomClassId)
                .and("lengthOfStay", lengthOfStay);
        DecisionBAROutput barOutput = tenantCrudService().findByNamedQuerySingleResult(
                DecisionBAROutput.BY_ARRIVALDATE_AND_ACCOMCLASSID_AND_LOS, params.parameters());
        barOutput.setOverride(BARDECISIONOVERRIDE_PENDING);
        barOutput.setFloorRateUnqualified(none);
        barOutput.setCeilingRateUnqualified(none);
        tenantCrudService().save(barOutput);

        DecisionBAROutputOverride barOutputOverride = new DecisionBAROutputOverride();
        barOutputOverride.setDecision(barOutput.getDecision());
        barOutputOverride.setPropertyId(barOutput.getPropertyID());
        barOutputOverride.setAccomClassId(barOutput.getAccomClassId());
        barOutputOverride.setArrivalDate(barOutput.getArrivalDate());
        barOutputOverride.setUserId(1);
        barOutputOverride.setLengthOfStay(barOutput.getLengthOfStay());

        barOutputOverride.setOldOverride(Constants.BARDECISIONOVERRIDE_NONE);
        barOutputOverride.setOldRateUnqualified(barOutput.getRateUnqualified());

        barOutputOverride.setNewOverride(Constants.BARDECISIONOVERRIDE_PENDING);
        barOutputOverride.setNewRateUnqualified(barOutput.getRateUnqualified());
        barOutputOverride.setNewFloorRateUnqualified(none);
        barOutputOverride.setNewCeilingRateUnqualified(none);
        barOutputOverride.setCreateDate(new Date());

        tenantCrudService().save(barOutputOverride);
        tenantCrudService().flushAndClear();

        Map<Date, BARDecisionInfo> barDecisions = service.getBarDecisions(accomClassId,
                lengthOfStay, startDate, endDate);

        BARDecisionInfo barDecisionInfo = barDecisions.get(arrivalDate);
        assertNull(barDecisionInfo.getFloorRatePlanId());
        assertNull(barDecisionInfo.getFloorRatePlanName());
        assertNull(barDecisionInfo.getCeilingRatePlanId());
        assertNull(barDecisionInfo.getCeilingRatePlanName());
    }

    @Test
    public void getBarDecisionsDoesntConvertsNoneRatesToNullRates() throws Exception {
        PacmanConfigParamsService mockConfigService = mock(PacmanConfigParamsService.class);
        when(mockConfigService.getBooleanParameterValue(FeatureTogglesConfigParamName.CEILING_OVERRIDE_ENABLED.value())).thenReturn(false);

        service.setConfigService(mockConfigService);

        // shouldn't convert if this is false
        doReturn(false).when(configService).getBooleanParameterValue(FeatureTogglesConfigParamName.CEILING_OVERRIDE_ENABLED.value());
        doReturn(false).when(configService).getBooleanParameterValue(FeatureTogglesConfigParamName.HIGHEST_BAR_RESTRICTED_ENABLED.value());

        int lengthOfStay = -1;
        int accomClassId = 2;
        Date arrivalDate = DateUtil.getCurrentDateWithoutTime();
        DateParameter startDate = new DateParameter(DateUtil.addDaysToDate(arrivalDate, -1));
        DateParameter endDate = new DateParameter(DateUtil.addDaysToDate(arrivalDate, 1));

        Integer noRateId = ((RateUnqualified) tenantCrudService().findByNamedQuerySingleResult(
                RateUnqualified.GET_RATE_UNQUALIFIED_BY_NAMES,
                QueryParameter.with("names", "None").parameters())).getId();
        RateUnqualified none = tenantCrudService().find(RateUnqualified.class, noRateId);

        QueryParameter params = QueryParameter.with("arrivalDate", arrivalDate)
                .and("accomClassId", accomClassId)
                .and("lengthOfStay", lengthOfStay);
        DecisionBAROutput barOutput = tenantCrudService().findByNamedQuerySingleResult(
                DecisionBAROutput.BY_ARRIVALDATE_AND_ACCOMCLASSID_AND_LOS, params.parameters());
        barOutput.setOverride(BARDECISIONOVERRIDE_PENDING);
        barOutput.setFloorRateUnqualified(none);
        barOutput.setCeilingRateUnqualified(none);
        tenantCrudService().save(barOutput);

        DecisionBAROutputOverride barOutputOverride = new DecisionBAROutputOverride();
        barOutputOverride.setDecision(barOutput.getDecision());
        barOutputOverride.setPropertyId(barOutput.getPropertyID());
        barOutputOverride.setAccomClassId(barOutput.getAccomClassId());
        barOutputOverride.setArrivalDate(barOutput.getArrivalDate());
        barOutputOverride.setUserId(1);
        barOutputOverride.setLengthOfStay(barOutput.getLengthOfStay());

        barOutputOverride.setOldOverride(Constants.BARDECISIONOVERRIDE_NONE);
        barOutputOverride.setOldRateUnqualified(barOutput.getRateUnqualified());

        barOutputOverride.setNewOverride(Constants.BARDECISIONOVERRIDE_PENDING);
        barOutputOverride.setNewRateUnqualified(barOutput.getRateUnqualified());
        barOutputOverride.setNewFloorRateUnqualified(none);
        barOutputOverride.setNewCeilingRateUnqualified(none);
        barOutputOverride.setCreateDate(new Date());

        tenantCrudService().save(barOutputOverride);
        tenantCrudService().flushAndClear();

        Map<Date, BARDecisionInfo> barDecisions = service.getBarDecisions(accomClassId,
                lengthOfStay, startDate, endDate);

        BARDecisionInfo barDecisionInfo = barDecisions.get(arrivalDate);
        assertEquals(none.getId(), barDecisionInfo.getFloorRatePlanId());
        assertEquals(none.getName(), barDecisionInfo.getFloorRatePlanName());
        assertEquals(none.getId(), barDecisionInfo.getCeilingRatePlanId());
        assertEquals(none.getName(), barDecisionInfo.getCeilingRatePlanName());
    }

    @Test
    public void getBARDetailsByDateRange() throws Exception {
        BarDecisionService spy = spy(service);
        PriceService mockPriceService = mock(PriceService.class);
        CrudServiceBean mockCrudService = mock(CrudServiceBean.class);
        PacmanConfigParamsService mockConfigurationService = mock(PacmanConfigParamsService.class);

        spy.setCrudService(mockCrudService);
        spy.setPriceService(mockPriceService);

        int accomClassId = 2;
        int accomTypeId = 1;
        int los = 1;
        Date startDate = new LocalDate().toDate();
        Date endDate = new LocalDate().plusDays(3).toDate();
        List<Object[]> returnValues = new ArrayList<>();
        returnValues.add(new Object[]{10, "test", BigDecimal.TEN, startDate});

        List<RateUnqualifiedDetails> rateUnqualifiedDetailsList = new ArrayList<>();
        RateUnqualifiedDetails rateUnqualifiedDetails = new RateUnqualifiedDetails();
        rateUnqualifiedDetails.setRateUnqualifiedId(10);
        rateUnqualifiedDetails.setAccomTypeId(accomTypeId);
        rateUnqualifiedDetails.setStartDate(startDate);
        rateUnqualifiedDetails.setEndDate(endDate);
        rateUnqualifiedDetailsList.add(rateUnqualifiedDetails);

        when(mockCrudService.findByNamedQuery(AccomType.ID_BY_ACCOM_CLASS_ID, QueryParameter.with("accomClassId", accomClassId).parameters())).thenReturn(Collections.singletonList(1));
        doReturn(1).when(spy).getMasterClassId();
        doReturn(returnValues).when(spy).getAvailableRates(accomClassId, startDate, endDate, los);
        when(mockConfigurationService.getBooleanParameterValue(FeatureTogglesConfigParamName.HIGHEST_BAR_RESTRICTED_ENABLED.value())).thenReturn(false);

        when(mockPriceService.getRateUnqualifiedDetails(Arrays.asList(10), startDate, endDate, Arrays.asList(accomTypeId))).thenReturn(Collections.singletonList(rateUnqualifiedDetails));

        BARDetails barDetails = spy.getBARDetailsByDateRange(accomClassId, startDate, endDate, los);

        assertNotNull(barDetails);
        verify(mockPriceService).getPriceMap(startDate, Collections.singletonList(1), Collections.singletonList(rateUnqualifiedDetails));
    }

    @Test
    public void shouldGetBarDecisionsForDateRangeAndLengthOfStayUsingMaxLOSWhenCloseHighestBARIsDisabled() throws Exception {
        Date today = DateUtil.getCurrentDateWithoutTime();
        Date arrivalDate1 = DateUtil.addYearsToDate(today, 5);
        Date arrivalDate2 = DateUtil.addYearsToDate(today, 6);

        BarDecisionService barDecisionService = Mockito.spy(service);
        PacmanConfigParamsService configService = Mockito.mock(PacmanConfigParamsService.class);
        CrudService crudService = Mockito.mock(TenantCrudServiceBean.class);
        barDecisionService.setConfigService(configService);
        barDecisionService.setCrudService(crudService);

        Mockito.when(configService.getBooleanParameterValue(FeatureTogglesConfigParamName.HIGHEST_BAR_RESTRICTED_ENABLED.value())).thenReturn(false);

        DecisionBAROutput decisionBAROutput1 = getDecisionBAROutput(arrivalDate1, 1, 5);
        DecisionBAROutput decisionBAROutput2 = getDecisionBAROutput(arrivalDate1, 1, 6);
        DecisionBAROutput decisionBAROutput3 = getDecisionBAROutput(arrivalDate1, 2, 6);
        DecisionBAROutput decisionBAROutput4 = getDecisionBAROutput(arrivalDate2, 3, 9);
        DecisionBAROutput decisionBAROutput5 = getDecisionBAROutput(arrivalDate2, 3, 2);

        Map<String, Object> parameters = QueryParameter.with(START_DATE, arrivalDate1).and(END_DATE, arrivalDate2).and("maxLengthOfStay", 10).parameters();
        Mockito.when(crudService.findByNamedQuery(DecisionBAROutput.BY_ARRIVALDATERANGE_AND_All_ACCOMCLASS_AND_LESS_THAN_OR_EQUAL_LOS, parameters))
                .thenReturn(Arrays.asList(decisionBAROutput1, decisionBAROutput2, decisionBAROutput3, decisionBAROutput4, decisionBAROutput5));
        Mockito.when(barDecisionService.getBarDecisionsForDateRangeAndLengthOfStayUsingMaxLOS(10, arrivalDate1, arrivalDate2)).thenCallRealMethod();

        Map<Date, Map<Integer, Map<Integer, BARDecisionInfo>>> result = barDecisionService.getBarDecisionsForDateRangeAndLengthOfStayUsingMaxLOS(10, arrivalDate1, arrivalDate2);
        Assert.assertEquals(2, result.size());
        verifyBARDecisionInfo(decisionBAROutput1, result, new ArrayList<>(), false);
        verifyBARDecisionInfo(decisionBAROutput2, result, new ArrayList<>(), false);
        verifyBARDecisionInfo(decisionBAROutput3, result, new ArrayList<>(), false);
        verifyBARDecisionInfo(decisionBAROutput4, result, new ArrayList<>(), false);
        verifyBARDecisionInfo(decisionBAROutput5, result, new ArrayList<>(), false);
    }

    @Test
    public void shouldGetBarDecisionsForDateRangeAndLengthOfStayUsingMaxLOSWhenCloseHighestBARIsEnabled() throws Exception {
        Date today = DateUtil.getCurrentDateWithoutTime();
        Date arrivalDate1 = DateUtil.addYearsToDate(today, 5);
        Date arrivalDate2 = DateUtil.addYearsToDate(today, 6);
        int maxLenghtOfStay = 10;

        BarDecisionService barDecisionService = Mockito.spy(service);
        PacmanConfigParamsService configService = Mockito.mock(PacmanConfigParamsService.class);
        CrudService crudService = Mockito.mock(TenantCrudServiceBean.class);
        CloseHighestBarService closeHighestBarService = Mockito.mock(CloseHighestBarService.class);

        barDecisionService.setConfigService(configService);
        barDecisionService.setCrudService(crudService);
        barDecisionService.setCloseHighestBarService(closeHighestBarService);

        Mockito.when(configService.getBooleanParameterValue(FeatureTogglesConfigParamName.HIGHEST_BAR_RESTRICTED_ENABLED.value())).thenReturn(true);

        DecisionBAROutput decisionBAROutput1 = getDecisionBAROutput(arrivalDate1, 1, 5);
        DecisionBAROutput decisionBAROutput2 = getDecisionBAROutput(arrivalDate1, 1, 6);
        DecisionBAROutput decisionBAROutput3 = getDecisionBAROutput(arrivalDate1, 2, 6);
        DecisionBAROutput decisionBAROutput4 = getDecisionBAROutput(arrivalDate2, 3, 9);
        DecisionBAROutput decisionBAROutput5 = getDecisionBAROutput(arrivalDate2, 3, 2);

        Map<String, Object> parameters = QueryParameter.with(START_DATE, arrivalDate1).and(END_DATE, arrivalDate2).and("maxLengthOfStay", 10).parameters();
        Mockito.when(crudService.findByNamedQuery(DecisionBAROutput.BY_ARRIVALDATERANGE_AND_All_ACCOMCLASS_AND_LESS_THAN_OR_EQUAL_LOS, parameters))
                .thenReturn(Arrays.asList(decisionBAROutput1, decisionBAROutput2, decisionBAROutput3, decisionBAROutput4, decisionBAROutput5));

        AccomTypeSummary accomTypeSummary1 = new AccomTypeSummary(12, "AccomType1");
        AccomTypeSummary accomTypeSummary2 = new AccomTypeSummary(22, "AccomType2");

        mockHighestBARService(arrivalDate1, arrivalDate2, maxLenghtOfStay, closeHighestBarService, accomTypeSummary1, accomTypeSummary2, 1, 5);

        Map<Date, Map<Integer, Map<Integer, BARDecisionInfo>>> result = barDecisionService.getBarDecisionsForDateRangeAndLengthOfStayUsingMaxLOS(maxLenghtOfStay, arrivalDate1, arrivalDate2);
        Assert.assertEquals(2, result.size());
        verifyBARDecisionInfo(decisionBAROutput1, result, Arrays.asList(accomTypeSummary1, accomTypeSummary2), true);
        verifyBARDecisionInfo(decisionBAROutput2, result, new ArrayList<>(), false);
        verifyBARDecisionInfo(decisionBAROutput3, result, new ArrayList<>(), false);
        verifyBARDecisionInfo(decisionBAROutput4, result, new ArrayList<>(), false);
        verifyBARDecisionInfo(decisionBAROutput5, result, new ArrayList<>(), false);
    }

    @Test
    public void truncateDerivedPaceTableDecisionsTest() {
        BarDecisionService spyBarDecisionService = spy(service);
        CrudService spyCrudService = Mockito.mock(TenantCrudServiceBean.class);
        spyBarDecisionService.setCrudService(spyCrudService);

        spyBarDecisionService.truncateDerivedPaceTableDecisions();

        verify(spyCrudService).executeUpdateByNativeQuery("TRUNCATE TABLE [dbo].[PACE_FPLOS_By_Hierarchy]");
        verify(spyCrudService).executeUpdateByNativeQuery("TRUNCATE TABLE [dbo].[PACE_FPLOS_By_RoomType]");
        verify(spyCrudService).executeUpdateByNativeQuery("TRUNCATE TABLE [dbo].[Pace_Decision_LRA_FPLOS]");
        verify(spyCrudService).executeUpdateByNativeQuery("TRUNCATE TABLE [dbo].[Pace_Decision_LRA_minLOS]");
        verify(spyCrudService).executeUpdateByNativeQuery("TRUNCATE TABLE [dbo].[PACE_FPLOS_By_Rank]");
        verify(spyCrudService).executeUpdateByNativeQuery("TRUNCATE TABLE [dbo].[PACE_Bar_Output_NOTIFICATION]");
        verify(spyCrudService).executeUpdateByNativeQuery("TRUNCATE TABLE [dbo].[PACE_Bar_Output_Upload]");
    }

    @Test
    public void deleteFutureBarDecisionTest() {
        BarDecisionService spyBarDecisionService = spy(service);
        Date caughtUpDate = LocalDate.fromDateFields(dateService.getCaughtUpDate()).toDate();

        when(spyBarDecisionService.deleteDecisionBarOutputRecords(caughtUpDate)).thenReturn(100);
        when(spyBarDecisionService.deleteDecisionBarOutputOverrideDetailsRecords(caughtUpDate)).thenReturn(100);
        when(spyBarDecisionService.deleteDecisionBarOutputOverrideRecords(caughtUpDate)).thenReturn(100);
        when(spyBarDecisionService.deleteDecisionDailyBarOutputRecords(caughtUpDate)).thenReturn(100);

        spyBarDecisionService.deleteFutureBarDecision();

        verify(spyBarDecisionService).deleteDecisionBarOutputRecords(caughtUpDate);
        verify(spyBarDecisionService).deleteDecisionBarOutputOverrideDetailsRecords(caughtUpDate);
        verify(spyBarDecisionService).deleteDecisionBarOutputOverrideRecords(caughtUpDate);
        verify(spyBarDecisionService).deleteDecisionDailyBarOutputRecords(caughtUpDate);
    }

    @Test
    public void deleteFutureBarByLOSDecisionTest() {
        BarDecisionService spyBarDecisionService = spy(service);
        Date caughtUpDate = LocalDate.fromDateFields(dateService.getCaughtUpDate()).toDate();

        when(spyBarDecisionService.deleteDecisionFplosByHierarchyRecords(caughtUpDate)).thenReturn(100);
        when(spyBarDecisionService.deleteDecisionFplosByRoomTypeRecords(caughtUpDate)).thenReturn(100);
        when(spyBarDecisionService.deleteDecisionLraFplosRecords(caughtUpDate)).thenReturn(100);
        when(spyBarDecisionService.deleteDecisionLraMinLOSRecords(caughtUpDate)).thenReturn(100);

        spyBarDecisionService.deleteFutureBarByLOSDecision();

        verify(spyBarDecisionService).deleteDecisionFplosByHierarchyRecords(caughtUpDate);
        verify(spyBarDecisionService).deleteDecisionFplosByRoomTypeRecords(caughtUpDate);
        verify(spyBarDecisionService).deleteDecisionLraFplosRecords(caughtUpDate);
        verify(spyBarDecisionService).deleteDecisionLraMinLOSRecords(caughtUpDate);
    }

    @Test
    public void deleteFutureCPBarDecisionTest() {
        BarDecisionService spyBarDecisionService = spy(service);
        Date caughtUpDate = LocalDate.fromDateFields(dateService.getCaughtUpDate()).toDate();

        when(spyBarDecisionService.deleteCPDecisionBarOutputRecords(caughtUpDate)).thenReturn(100);
        when(spyBarDecisionService.deleteCPDecisionBarOutputOverrideRecords(caughtUpDate)).thenReturn(100);

        spyBarDecisionService.deleteFutureCPBarDecision();

        verify(spyBarDecisionService).deleteCPDecisionBarOutputRecords(caughtUpDate);
        verify(spyBarDecisionService).deleteCPDecisionBarOutputOverrideRecords(caughtUpDate);
    }


    private void mockHighestBARService(Date arrivalDate1, Date arrivalDate2, int maxLenghtOfStay, CloseHighestBarService closeHighestBarService, AccomTypeSummary accomTypeSummary1, AccomTypeSummary accomTypeSummary2, int accomClassId, int los) {
        Map<Integer, List<AccomTypeSummary>> losToAccomTypeSummary = new HashMap<>();
        losToAccomTypeSummary.put(los, Arrays.asList(accomTypeSummary1, accomTypeSummary2));

        Map<Integer, Map<Integer, List<AccomTypeSummary>>> accomClassToAccomTypeSummary = new HashMap<>();
        accomClassToAccomTypeSummary.put(accomClassId, losToAccomTypeSummary);

        Map<Date, Map<Integer, Map<Integer, List<AccomTypeSummary>>>> closedRoomTypes = new HashMap<>();
        closedRoomTypes.put(arrivalDate1, accomClassToAccomTypeSummary);

        Mockito.when(closeHighestBarService.getRoomTypesForMaxLOSForAllAccomClasses(maxLenghtOfStay, arrivalDate1, arrivalDate2)).thenReturn(closedRoomTypes);
    }

    private void verifyBARDecisionInfo(DecisionBAROutput decisionBAROutput, Map<Date, Map<Integer, Map<Integer, BARDecisionInfo>>> result, List<AccomTypeSummary> accomTypeSummaries, boolean restrictHighestBarOverride) {
        Map<Integer, Map<Integer, BARDecisionInfo>> accomClassToDecision = result.get(decisionBAROutput.getArrivalDate());
        Assert.assertNotNull(accomClassToDecision);
        Map<Integer, BARDecisionInfo> losToDecision = accomClassToDecision.get(decisionBAROutput.getAccomClassId());
        Assert.assertNotNull(losToDecision);
        BARDecisionInfo barDecisionInfo = losToDecision.get(decisionBAROutput.getLengthOfStay());
        Assert.assertNotNull(barDecisionInfo);
        Assert.assertEquals(accomTypeSummaries, barDecisionInfo.getClosedRoomTypes());
        Assert.assertEquals(restrictHighestBarOverride, barDecisionInfo.isRestrictHighestBarOverride());
    }

    private DecisionBAROutput getDecisionBAROutput(Date arrivalDate, int accomClassId, int los) {
        DecisionBAROutput decisionBAROutput = new DecisionBAROutput();
        decisionBAROutput.setArrivalDate(arrivalDate);
        decisionBAROutput.setAccomClassId(accomClassId);
        decisionBAROutput.setLengthOfStay(los);
        decisionBAROutput.setOverride("User");

        RateUnqualified rateUnqualified = new RateUnqualified();
        rateUnqualified.setId(1);
        rateUnqualified.setName("Test");
        rateUnqualified.setStatusId(2);

        decisionBAROutput.setRateUnqualified(rateUnqualified);
        return decisionBAROutput;
    }

    @Test
    public void testGetCompetitorInfoToExcludeSelfCompetitor_MinPrice() throws Exception {
        int selfCompetitorHotelId = 8761;
        PacmanConfigParamsService mockConfigService = mockData(selfCompetitorHotelId, BAR_OVRD_DISPLAY_COMPETITOR_VALUE_LOW);
        setConfigServices(mockConfigService);
        tenantCrudService().findByNamedQuerySingleResult(WebrateCompetitors.IDS_PROPERTY_ID_AND_WRHOTEL_ID, QueryParameter.with("propertyId", 5)
                .and("webrateHotelID", String.valueOf(selfCompetitorHotelId)).parameters());
        List<AccomClass> accomClasses = tenantCrudService().findAll(AccomClass.class);
        WebrateCompetitors selfCompetitor = UniqueWebrateCompetitorsCreator.createWebrateCompetitorsByName("Hotel Pune" + new Random().nextInt(), String.valueOf(selfCompetitorHotelId));
        Webrate selfCompetitorWebRate = createWebrateData(selfCompetitor, 10);
        AccomClass selfCompetitorWebRateAccomClass = getWebRateAccomClass(accomClasses, selfCompetitorWebRate);
        WebrateCompetitorsAccomClass selfCompetitorWebrateCompetitorsAccomClass = createWebrateCompetitorsAccomClass(selfCompetitor, selfCompetitorWebRateAccomClass);
        selfCompetitor.setWebrateCompetitorsAccomClasses(new HashSet<>(Arrays.asList(selfCompetitorWebrateCompetitorsAccomClass)));

        WebrateCompetitors competitorWithMinWebRate = UniqueWebrateCompetitorsCreator.createWebrateCompetitorsByName("Hotel Mumbai" + new Random().nextInt(), String.valueOf(selfCompetitorHotelId + 1));
        Webrate webrateData = createWebrateData(competitorWithMinWebRate, 20);
        AccomClass webRateDataAccomClass = getWebRateAccomClass(accomClasses, webrateData);
        WebrateCompetitorsAccomClass competitorWithMinWebRateWebrateCompetitorsAccomClass = createWebrateCompetitorsAccomClass(competitorWithMinWebRate, webRateDataAccomClass);
        competitorWithMinWebRate.setWebrateCompetitorsAccomClasses(new HashSet<>(Arrays.asList(competitorWithMinWebRateWebrateCompetitorsAccomClass)));

        WebrateCompetitors hotel_uk = createWebRateCompetitor(selfCompetitorHotelId + 2, "Hotel Uk", 30, webRateDataAccomClass);
        hotel_uk.setWebrateCompetitorsAccomClasses(new HashSet<>(Arrays.asList(competitorWithMinWebRateWebrateCompetitorsAccomClass)));

        PacmanWorkContextHelper.setPropertyId(PROPERTY_ID_RATEOFDAY);
        BARFilterCriteria criteria = getBarFilterCriteria(selfCompetitorWebRate);
        Map<Date, Map<Integer, CompetitorInfo>> result = service.getCompetitorInfo(criteria);
        verifyResult(selfCompetitor, selfCompetitorWebRate, result, competitorWithMinWebRate.getId());
    }

    private AccomClass getWebRateAccomClass(List<AccomClass> accomClasses, Webrate webrate) {
        return accomClasses.stream().filter(accomClass -> accomClass.getId().equals(webrate.getWebrateAccomType().getId())).findFirst().orElse(null);
    }

    @Test
    public void testGetCompetitorInfoToExcludeSelfCompetitor_MaxPrice() throws Exception {
        int selfCompetitorHotelId = 8764;
        PacmanConfigParamsService mockConfigService = mockData(selfCompetitorHotelId, BAR_OVRD_DISPLAY_COMPETITOR_VALUE_HIGH);
        setConfigServices(mockConfigService);
        tenantCrudService().findByNamedQuerySingleResult(WebrateCompetitors.IDS_PROPERTY_ID_AND_WRHOTEL_ID, QueryParameter.with("propertyId", 5)
                .and("webrateHotelID", String.valueOf(selfCompetitorHotelId)).parameters());
        List<AccomClass> accomClasses = tenantCrudService().findAll(AccomClass.class);
        WebrateCompetitors selfCompetitor = UniqueWebrateCompetitorsCreator.createWebrateCompetitorsByName("Hotel Delhi" + new Random().nextInt(), String.valueOf(selfCompetitorHotelId));
        Webrate selfCompetitorWebRateData = createWebrateData(selfCompetitor, 100);
        AccomClass selfCompetitorWebRateAccomClass = getWebRateAccomClass(accomClasses, selfCompetitorWebRateData);
        WebrateCompetitorsAccomClass selfCompetitorWebrateCompetitorsAccomClass = createWebrateCompetitorsAccomClass(selfCompetitor, selfCompetitorWebRateAccomClass);
        selfCompetitor.setWebrateCompetitorsAccomClasses(new HashSet<>(Arrays.asList(selfCompetitorWebrateCompetitorsAccomClass)));

        WebrateCompetitors hotel_usa = createWebRateCompetitor(selfCompetitorHotelId + 1, "Hotel USA", 190, selfCompetitorWebRateAccomClass);
        hotel_usa.setWebrateCompetitorsAccomClasses(new HashSet<>(Arrays.asList(selfCompetitorWebrateCompetitorsAccomClass)));

        WebrateCompetitors competitorWithMaxWebRate = UniqueWebrateCompetitorsCreator.createWebrateCompetitorsByName("Hotel Abc" + new Random().nextInt(), String.valueOf(selfCompetitorHotelId + 2));
        Webrate competitorWithMaxWebRateData = createWebrateData(competitorWithMaxWebRate, 700);
        AccomClass competitorWithMaxWebRateDataWebRateAccomClass = getWebRateAccomClass(accomClasses, competitorWithMaxWebRateData);
        WebrateCompetitorsAccomClass competitorWithMaxWebRateDataWebrateCompetitorsAccomClass = createWebrateCompetitorsAccomClass(competitorWithMaxWebRate, competitorWithMaxWebRateDataWebRateAccomClass);
        competitorWithMaxWebRate.setWebrateCompetitorsAccomClasses(new HashSet<>(Arrays.asList(competitorWithMaxWebRateDataWebrateCompetitorsAccomClass)));

        PacmanWorkContextHelper.setPropertyId(PROPERTY_ID_RATEOFDAY);
        Date date = getDateAfterTenYears();
        Map<Date, CompetitorInfo> result = service.getCompetitorInfo(
                3, 1, date, DateUtil.addDaysToDate(date, 1));
        verifyResult(selfCompetitor, result, competitorWithMaxWebRate.getId());
    }

    @Test
    public void testGetCompetitorInfoMapToExcludeSelfCompetitor_MaxPrice() throws Exception {
        int selfCompetitorHotelId = 8764;
        PacmanConfigParamsService mockConfigService = mockData(selfCompetitorHotelId, BAR_OVRD_DISPLAY_COMPETITOR_VALUE_HIGH);
        setConfigServices(mockConfigService);
        tenantCrudService().findByNamedQuerySingleResult(WebrateCompetitors.IDS_PROPERTY_ID_AND_WRHOTEL_ID, QueryParameter.with("propertyId", 5)
                .and("webrateHotelID", String.valueOf(selfCompetitorHotelId)).parameters());
        List<AccomClass> accomClasses = tenantCrudService().findAll(AccomClass.class);

        WebrateCompetitors selfCompetitor = UniqueWebrateCompetitorsCreator.createWebrateCompetitorsByName("Hotel Delhi" + new Random().nextInt(), String.valueOf(selfCompetitorHotelId));
        Webrate selfCompetitorWebrateData = createWebrateData(selfCompetitor, 100);
        AccomClass selfCompetitorWebRateAccomClass = getWebRateAccomClass(accomClasses, selfCompetitorWebrateData);
        createWebRateCompetitor(selfCompetitorHotelId + 1, "Hotel USA", 190, selfCompetitorWebRateAccomClass);
        WebrateCompetitorsAccomClass selfCompetitorWebrateCompetitorsAccomClass = createWebrateCompetitorsAccomClass(selfCompetitor, selfCompetitorWebRateAccomClass);
        selfCompetitor.setWebrateCompetitorsAccomClasses(new HashSet<>(Arrays.asList(selfCompetitorWebrateCompetitorsAccomClass)));

        WebrateCompetitors competitorWithMaxWebRate = UniqueWebrateCompetitorsCreator.createWebrateCompetitorsByName("Hotel Abc" + new Random().nextInt(), String.valueOf(selfCompetitorHotelId + 2));
        Webrate competitorWithMaxWebrateData = createWebrateData(competitorWithMaxWebRate, 700);
        AccomClass competitorWithMaxWebRateAccomClass = getWebRateAccomClass(accomClasses, competitorWithMaxWebrateData);
        WebrateCompetitorsAccomClass competitorWithMaxWebRateWebrateCompetitorsAccomClass = createWebrateCompetitorsAccomClass(competitorWithMaxWebRate, competitorWithMaxWebRateAccomClass);
        competitorWithMaxWebRate.setWebrateCompetitorsAccomClasses(new HashSet<>(Arrays.asList(competitorWithMaxWebRateWebrateCompetitorsAccomClass)));

        PacmanWorkContextHelper.setPropertyId(PROPERTY_ID_RATEOFDAY);
        Date date = getDateAfterTenYears();
        Map<Integer, Map<Date, CompetitorInfo>> resultMap = service.getCompetitorInfoMap(1, date, DateUtil.addDaysToDate(date, 1));
        Map<Date, CompetitorInfo> result = resultMap.get(3);
        verifyResult(selfCompetitor, result, competitorWithMaxWebRate.getId());
    }

    @Test
    public void testGetCompetitorInfoToExcludeSelfCompetitor_MedianPrice() throws Exception {
        int selfCompetitorHotelId = 8767;
        PacmanConfigParamsService mockConfigService = mockData(selfCompetitorHotelId, BAR_OVRD_DISPLAY_COMPETITOR_VALUE_MEDIAN);
        setConfigServices(mockConfigService);
        tenantCrudService().findByNamedQuerySingleResult(WebrateCompetitors.IDS_PROPERTY_ID_AND_WRHOTEL_ID, QueryParameter.with("propertyId", 5)
                .and("webrateHotelID", String.valueOf(selfCompetitorHotelId)).parameters());
        List<AccomClass> accomClasses = tenantCrudService().findAll(AccomClass.class);

        WebrateCompetitors selfCompetitor = UniqueWebrateCompetitorsCreator.createWebrateCompetitorsByName("Hotel A" + new Random().nextInt(), String.valueOf(selfCompetitorHotelId));
        Webrate selfCompetitorWebRate = createWebrateData(selfCompetitor, 100);
        AccomClass selfCompetitorWebRateAccomClass = getWebRateAccomClass(accomClasses, selfCompetitorWebRate);
        WebrateCompetitorsAccomClass selfCompetitorWebrateCompetitorsAccomClass = createWebrateCompetitorsAccomClass(selfCompetitor, selfCompetitorWebRateAccomClass);
        selfCompetitor.setWebrateCompetitorsAccomClasses(new HashSet<>(Arrays.asList(selfCompetitorWebrateCompetitorsAccomClass)));
        createWebRateCompetitor(selfCompetitorHotelId + 1, "Hotel B", 630, selfCompetitorWebRateAccomClass);

        WebrateCompetitors competitorExpectedInOutput = UniqueWebrateCompetitorsCreator.createWebrateCompetitorsByName("Hotel C" + new Random().nextInt(), String.valueOf(selfCompetitorHotelId + 2));
        Webrate competitorExpectedInOutputWebrateData = createWebrateData(competitorExpectedInOutput, 150);
        AccomClass competitorExpectedInOutputAccomClass = getWebRateAccomClass(accomClasses, competitorExpectedInOutputWebrateData);
        WebrateCompetitorsAccomClass competitorExpectedInOutputWebrateCompetitorsAccomClass = createWebrateCompetitorsAccomClass(competitorExpectedInOutput, competitorExpectedInOutputAccomClass);
        competitorExpectedInOutput.setWebrateCompetitorsAccomClasses(new HashSet<>(Arrays.asList(competitorExpectedInOutputWebrateCompetitorsAccomClass)));

        PacmanWorkContextHelper.setPropertyId(PROPERTY_ID_RATEOFDAY);
        Date date = getDateAfterTenYears();
        Map<Date, CompetitorInfo> result = service.getCompetitorInfo(
                3, 1, date, DateUtil.addDaysToDate(date, 1));
        verifyResult(selfCompetitor, result, competitorExpectedInOutput.getId());
    }

    @Test
    public void testGetCompetitorInfoMapToExcludeSelfCompetitor_MedianPrice() throws Exception {
        int selfCompetitorHotelId = 8767;
        PacmanConfigParamsService mockConfigService = mockData(selfCompetitorHotelId, BAR_OVRD_DISPLAY_COMPETITOR_VALUE_MEDIAN);
        setConfigServices(mockConfigService);
        tenantCrudService().findByNamedQuerySingleResult(WebrateCompetitors.IDS_PROPERTY_ID_AND_WRHOTEL_ID, QueryParameter.with("propertyId", 5)
                .and("webrateHotelID", String.valueOf(selfCompetitorHotelId)).parameters());
        List<AccomClass> accomClasses = tenantCrudService().findAll(AccomClass.class);

        WebrateCompetitors selfCompetitor = UniqueWebrateCompetitorsCreator.createWebrateCompetitorsByName("Hotel A" + new Random().nextInt(), String.valueOf(selfCompetitorHotelId));
        Webrate selfCompetitorWebrateData = createWebrateData(selfCompetitor, 100);
        AccomClass selfCompetitorWebRateAccomClass = getWebRateAccomClass(accomClasses, selfCompetitorWebrateData);
        WebrateCompetitorsAccomClass selfCompetitorWebrateCompetitorsAccomClass = createWebrateCompetitorsAccomClass(selfCompetitor, selfCompetitorWebRateAccomClass);
        selfCompetitor.setWebrateCompetitorsAccomClasses(new HashSet<>(Arrays.asList(selfCompetitorWebrateCompetitorsAccomClass)));
        createWebRateCompetitor(selfCompetitorHotelId + 1, "Hotel B", 630, selfCompetitorWebRateAccomClass);

        WebrateCompetitors competitorExpectedInOutput = UniqueWebrateCompetitorsCreator.createWebrateCompetitorsByName("Hotel C" + new Random().nextInt(), String.valueOf(selfCompetitorHotelId + 2));
        Webrate competitorExpectedInOutputWebrateData = createWebrateData(competitorExpectedInOutput, 150);
        AccomClass competitorExpectedInOutputAccomClass = getWebRateAccomClass(accomClasses, competitorExpectedInOutputWebrateData);
        WebrateCompetitorsAccomClass competitorExpectedInOutputWebrateCompetitorsAccomClass = createWebrateCompetitorsAccomClass(competitorExpectedInOutput, competitorExpectedInOutputAccomClass);
        competitorExpectedInOutput.setWebrateCompetitorsAccomClasses(new HashSet<>(Arrays.asList(competitorExpectedInOutputWebrateCompetitorsAccomClass)));

        PacmanWorkContextHelper.setPropertyId(PROPERTY_ID_RATEOFDAY);
        Date date = getDateAfterTenYears();
        Map<Integer, Map<Date, CompetitorInfo>> resultMap = service.getCompetitorInfoMap(1, date, DateUtil.addDaysToDate(date, 1));
        Map<Date, CompetitorInfo> result = resultMap.get(3);
        verifyResult(selfCompetitor, result, competitorExpectedInOutput.getId());
    }

    private Date getDateAfterTenYears() {
        return DateUtil.addYearsToDate(new Date(new LocalDate().toDateTimeAtStartOfDay().getMillis()), 10);
    }

    private void verifyResult(WebrateCompetitors selfCompetitor, Map<Date, CompetitorInfo> result, Integer competitorChannelId) {
        assertNotNull(result);
        assertTrue(result.size() == 1);
        List<Date> keys = new ArrayList<Date>(result.keySet());
        keys.forEach(key -> {
            CompetitorInfo competitorInfo = result.get(key);
            assertNotSame(competitorInfo.getCompetitorIds(), selfCompetitor.getId());
            assertTrue(competitorInfo.getCompetitorIds().contains(competitorChannelId));
        });
    }

    private void verifyResult(WebrateCompetitors selfCompetitor, Webrate selfCompetitorWebRate, Map<Date, Map<Integer, CompetitorInfo>> result, Integer competitorWithMinimumWebrate) {
        assertNotNull(result);
        assertTrue(result.size() == 1);
        List<Date> keys = new ArrayList<Date>(result.keySet());
        keys.forEach(key -> {
            Map<Integer, CompetitorInfo> resultMap = result.get(key);
            CompetitorInfo competitorInfo = resultMap.get(selfCompetitorWebRate.getLos());
            assertNotSame(competitorInfo.getCompetitorIds(), selfCompetitor.getId());
            assertTrue(competitorInfo.getCompetitorIds().contains(competitorWithMinimumWebrate));
        });
    }

    private void setConfigServices(PacmanConfigParamsService mockConfigService) {
        service.setConfigService(mockConfigService);
        sqlHelper.setConfigService(mockConfigService);
    }

    private Webrate createWebrateData(WebrateCompetitors selfCompetitor, int webRateValue) {
        return UniqueWebRateCreator.createWebrate(1, selfCompetitor.getId(), 3, 3, 1, DateUtil.sqlDate(getDateAfterTenYears()), webRateValue);
    }

    private WebrateCompetitorsAccomClass createWebrateCompetitorsAccomClass(WebrateCompetitors competitor, AccomClass accomClass) {
        return UniqueWebrateCompetitorsAccomClassCreator.createWebrateCompetitorsAccomClass(1, 1, competitor, accomClass);
    }

    private WebrateCompetitors createWebRateCompetitor(int selfCompetitorHotelId, String competitorName, int webRateValue, AccomClass accomClass) {
        WebrateCompetitors competitor = UniqueWebrateCompetitorsCreator.createWebrateCompetitorsByName(competitorName + new Random().nextInt(), String.valueOf(selfCompetitorHotelId));
        createWebrateData(competitor, webRateValue);
        createWebrateCompetitorsAccomClass(competitor, accomClass);
        return competitor;
    }

    private PacmanConfigParamsService mockData(int selfCompetitorHotelId, String barOverrideDisplayValue) {
        PacmanConfigParamsService mockConfigService = Mockito.mock(PacmanConfigParamsService.class);
        when(mockConfigService.getParameterValue(IPConfigParamName.BAR_WEB_RATE_SHOPPING_ENABLED.value())).thenReturn("true");
        when(mockConfigService.getParameterValue(IPConfigParamName.BAR_BAR_OVRD_DISPLAY_COMPETITOR.value())).thenReturn(barOverrideDisplayValue);
        when(mockConfigService.getParameterValue(IPConfigParamName.BAR_WEB_RATE_ALIAS)).thenReturn(String.valueOf(selfCompetitorHotelId));
        return mockConfigService;
    }

    private BARFilterCriteria getBarFilterCriteria(Webrate webrate) {
        BARFilterCriteria criteria = new BARFilterCriteria();
        criteria.setAccomClassId(webrate.getWebrateAccomType().getId());
        criteria.setIncludedDaysOfWeek(Arrays.asList(new Integer[]{1, 2, 3,
                4, 5, 6, 7}));
        criteria.setIncludedLengthsOfStay(Arrays.asList(new Integer[]{1, 2}));
        Date date = getDateAfterTenYears();
        criteria.setStartDate(date);
        criteria.setEndDate(DateUtil.addDaysToDate(date, 1));
        return criteria;
    }
}