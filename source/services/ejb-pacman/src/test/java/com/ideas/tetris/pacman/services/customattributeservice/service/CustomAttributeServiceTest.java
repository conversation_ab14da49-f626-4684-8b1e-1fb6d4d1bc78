package com.ideas.tetris.pacman.services.customattributeservice.service;

import com.ideas.cache.redis.configuration.IdeasRedisCacheManager;
import com.ideas.g3.test.AbstractG3JupiterTest;
import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.common.utils.pojo.Pair;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.client.service.ClientService;
import com.ideas.tetris.pacman.services.commondaoandenities.global.entity.*;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.dao.UniqueClientAttributeCreator;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.dao.UniqueClientAttributeValueCreator;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.dao.UniqueClientPropertyAttributePairingCreator;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.dao.UniquePropertyGroupCreator;
import com.ideas.tetris.pacman.services.fds.ups.UPSService;
import com.ideas.tetris.pacman.services.fds.ups.model.UPSCustomAttribute;
import com.ideas.tetris.pacman.services.reports.dto.PropertyAttributeAssignmentDTO;
import com.ideas.tetris.pacman.services.rules.*;
import com.ideas.tetris.pacman.services.security.GlobalUser;
import com.ideas.tetris.pacman.services.security.UniqueAuthorizationGroupCreator;
import com.ideas.tetris.pacman.services.useractivity.ActivityType;
import com.ideas.tetris.pacman.services.useractivity.UserActivityService;
import com.ideas.tetris.pacman.services.useractivity.entity.UserActivity;
import com.ideas.tetris.pacman.services.useractivity.entity.UserActivityPageCodeEnum;
import com.ideas.tetris.platform.common.contextholder.PacmanWorkContextTestHelper;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.errorhandling.ErrorCode;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import com.ideas.tetris.platform.services.daoandentities.entity.AuthorizationGroup;
import com.ideas.tetris.platform.services.daoandentities.entity.Client;
import com.ideas.tetris.platform.services.daoandentities.entity.Property;
import com.ideas.tetris.platform.services.daoandentities.entity.Status;
import com.ideas.tetris.platform.services.globalproperty.service.ClientPropertyCacheService;
import com.ideas.tetris.platform.services.globalproperty.service.PropertyCache;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import javax.persistence.EntityManager;
import javax.persistence.Query;
import java.util.*;
import java.util.stream.Collectors;

import static com.ideas.tetris.pacman.services.customattributeservice.service.CustomAttributeService.BUSINESS_PRACTICE;
import static com.ideas.tetris.pacman.services.customattributeservice.service.CustomAttributeService.REGION;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.core.Is.is;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@MockitoSettings(strictness = Strictness.LENIENT)
public class CustomAttributeServiceTest extends AbstractG3JupiterTest {

    @InjectMocks
    @Spy
    private CustomAttributeService service;

    CustomAttributeSearchBean search = new CustomAttributeSearchBean();
    RulesService rulesService = new RulesService();
    PacmanConfigParamsService configParamsService = new PacmanConfigParamsService();
    ClientService clientService = new ClientService();
    ClientPropertyCacheService clientPropertyCacheService = new ClientPropertyCacheService();
    PropertyCache propertyCache = new PropertyCache();
    private AttributeDisplayType charAttributeDisplayType;
    private AttributeDisplayType booleanAttributeDisplayType;
    private AttributeDisplayType numbericAttributeDisplayType;

    @BeforeEach
    public void setUp() {
        service.setGlobalCrudService(globalCrudService());
        rulesService.setGlobalCrudService(globalCrudService());
        search.setCrudService(globalCrudService());
        rulesService.setSearcher(search);
        service.setRulesService(rulesService);
        service.setCustomAttributeSearch(search);
        service.setConfigParamsService(configParamsService);
        service.setClientService(clientService);
        clientService.setClientPropertyCacheService(clientPropertyCacheService);
        clientPropertyCacheService.setPropertyCache(propertyCache);

        charAttributeDisplayType = new AttributeDisplayType();
        charAttributeDisplayType.setId(1);
        charAttributeDisplayType.setDisplayTypeName("Char");
        booleanAttributeDisplayType = new AttributeDisplayType();
        booleanAttributeDisplayType.setId(2);
        booleanAttributeDisplayType.setDisplayTypeName("Boolean");
        numbericAttributeDisplayType = new AttributeDisplayType();
        numbericAttributeDisplayType.setId(3);
        numbericAttributeDisplayType.setDisplayTypeName("Numeric");
    }

    @Test
    public void testCreateAttributeValueCollectionFromParing() {
        List<Integer> clientAttributeValueIds = new ArrayList<Integer>();
        service.createAttributeValueCollectionFromParing(clientAttributeValueIds);
        assertThat("There should be some client attributes pairing records in sample data",
                clientAttributeValueIds.toString(), is("[1, 5, 7, 8, 10, 11]"));
    }

    @Test
    public void testCreateAttributeValueCollectionFromRuleMapping() {
        List<Integer> ruleAttributeMappingValueIds = new ArrayList<Integer>();
        service.createAttributeValueCollectionFromRuleMapping(ruleAttributeMappingValueIds);
        assertTrue(ruleAttributeMappingValueIds.size() > 0, "There should be some client attributes mapping entries with rules records in sample data");
    }

    @Test
    public void shouldRemoveClientPropertyAttributePairingForClientAttribute() {
        Integer propertyId = PacmanWorkContextTestHelper.WC_PROPERTY_ID_PUNE;
        ClientPropertyAttributePairing cpap = UniqueClientPropertyAttributePairingCreator.createUniqueClientPropertyAttributePairing(globalCrudService(), 2, propertyId, "Location");
        service.removeClientAttributeValuesForPropertyIds(cpap.getClientAttributeValue().getClientAttribute().getId(), Arrays.asList(propertyId));
    }

    @Test
    public void shouldReturnAllActiveExternalUserRulesUsedInPG() {
        Integer clientId = PacmanWorkContextHelper.getClientId();
        ClientAttributeValue attriButeValue = UniqueClientAttributeValueCreator.createUniqueClientAttributeValue(globalCrudService(), clientId);
        int userID = Integer.parseInt(PacmanWorkContextHelper.getUserId());
        Rule rule = UniqueRuleCreator.createRule(globalCrudService(), clientId, userID);
        UniqueRuleAttributeValueMappingCreator.createRuleAttributeMapping(globalCrudService(), attriButeValue.getId(), rule.getId());
        PropertyGroup pg = UniquePropertyGroupCreator.createPropertyGroup(globalCrudService(), clientId);
        pg.setRuleId(rule.getId());
        globalCrudService().save(pg);
        GlobalUser externalUser = globalCrudService().find(GlobalUser.class, userID);
        externalUser.setInternal(false);
        globalCrudService().save(externalUser);

        globalCrudService().getEntityManager().flush();

        List<Integer> ruleAttributeMapping = service.getRuleAttributeMapping();
        assertNotNull(ruleAttributeMapping);
        assertTrue(ruleAttributeMapping.contains(attriButeValue.getId()));
    }

    @Test
    public void shouldNotReturnRulesUsedInPGForInternalUser() {
        Integer clientId = PacmanWorkContextHelper.getClientId();
        ClientAttributeValue attriButeValue = UniqueClientAttributeValueCreator.createUniqueClientAttributeValue(globalCrudService(), clientId);
        int userID = Integer.parseInt(PacmanWorkContextHelper.getUserId());
        Rule rule = UniqueRuleCreator.createRule(globalCrudService(), clientId, userID);
        UniqueRuleAttributeValueMappingCreator.createRuleAttributeMapping(globalCrudService(), attriButeValue.getId(), rule.getId());
        PropertyGroup pg = UniquePropertyGroupCreator.createPropertyGroup(globalCrudService(), clientId);
        pg.setRuleId(rule.getId());
        globalCrudService().save(pg);

        GlobalUser user = globalCrudService().findByNamedQuerySingleResult(GlobalUser.BY_USERID,
                QueryParameter.with("id", Integer.valueOf(userID)).parameters());
        user.setInternal(true);
        globalCrudService().save(user);

        List<Integer> ruleAttributeMapping = service.getRuleAttributeMapping();
        assertNotNull(ruleAttributeMapping);
        assertFalse(ruleAttributeMapping.contains(attriButeValue.getId()));
    }

    @Test
    public void shouldReturnAllActiveRulesUsedInAG() {
        Integer clientId = PacmanWorkContextHelper.getClientId();
        ClientAttributeValue attriButeValue = UniqueClientAttributeValueCreator.createUniqueClientAttributeValue(globalCrudService(), clientId);
        Rule rule = UniqueRuleCreator.createRule(globalCrudService(), clientId, Integer.parseInt(PacmanWorkContextHelper.getUserId()));
        UniqueRuleAttributeValueMappingCreator.createRuleAttributeMapping(globalCrudService(), attriButeValue.getId(), rule.getId());
        AuthorizationGroup ag = UniqueAuthorizationGroupCreator.createAuthorizationGroup(globalCrudService(), clientId, Integer.parseInt(PacmanWorkContextHelper.getUserId()));
        ag.setRuleId(rule.getId());
        globalCrudService().save(ag);
        globalCrudService().getEntityManager().flush();

        List<Integer> ruleAttributeMapping = service.getRuleAttributeMapping();
        assertNotNull(ruleAttributeMapping);
        assertTrue(ruleAttributeMapping.contains(attriButeValue.getId()));
    }

    @Test
    public void shouldReturnAllActiveRulesUsedInAGAndPG() {
        Integer clientId = PacmanWorkContextHelper.getClientId();
        ClientAttributeValue attriButeValuePG = UniqueClientAttributeValueCreator.createUniqueClientAttributeValue(globalCrudService(), clientId);
        ClientAttributeValue attriButeValueAG = UniqueClientAttributeValueCreator.createUniqueClientAttributeValue(globalCrudService(), clientId);

        int userId = Integer.parseInt(PacmanWorkContextHelper.getUserId());
        Rule ruleForAg = UniqueRuleCreator.createRule(globalCrudService(), clientId, userId);
        UniqueRuleAttributeValueMappingCreator.createRuleAttributeMapping(globalCrudService(), attriButeValueAG.getId(), ruleForAg.getId());
        AuthorizationGroup ag = UniqueAuthorizationGroupCreator.createAuthorizationGroup(globalCrudService(), clientId, userId);
        ag.setRuleId(ruleForAg.getId());
        globalCrudService().save(ag);
        Rule ruleForPG = UniqueRuleCreator.createRule(globalCrudService(), clientId, userId);
        UniqueRuleAttributeValueMappingCreator.createRuleAttributeMapping(globalCrudService(), attriButeValuePG.getId(), ruleForPG.getId());

        PropertyGroup pg = UniquePropertyGroupCreator.createPropertyGroup(globalCrudService(), clientId);
        pg.setRuleId(ruleForPG.getId());
        GlobalUser externalUser = globalCrudService().find(GlobalUser.class, userId);
        externalUser.setInternal(false);
        globalCrudService().save(externalUser);
        globalCrudService().save(pg);
        globalCrudService().getEntityManager().flush();

        List<Integer> ruleAttributeMapping = service.getRuleAttributeMapping();
        assertNotNull(ruleAttributeMapping);
        assertTrue(ruleAttributeMapping.contains(attriButeValueAG.getId()));
        assertTrue(ruleAttributeMapping.contains(attriButeValuePG.getId()));
    }

    @Test
    public void shouldNotReturnInActiveRulesUsedInPG() {
        Integer clientId = PacmanWorkContextHelper.getClientId();
        ClientAttributeValue attriButeValuePG = UniqueClientAttributeValueCreator.createUniqueClientAttributeValue(globalCrudService(), clientId);
        ClientAttributeValue attriButeValueAG = UniqueClientAttributeValueCreator.createUniqueClientAttributeValue(globalCrudService(), clientId);

        Rule ruleForAg = UniqueRuleCreator.createRule(globalCrudService(), clientId, Integer.parseInt(PacmanWorkContextHelper.getUserId()));
        UniqueRuleAttributeValueMappingCreator.createRuleAttributeMapping(globalCrudService(), attriButeValueAG.getId(), ruleForAg.getId());
        AuthorizationGroup ag = UniqueAuthorizationGroupCreator.createAuthorizationGroup(globalCrudService(), clientId, Integer.parseInt(PacmanWorkContextHelper.getUserId()));
        ag.setRuleId(ruleForAg.getId());
        globalCrudService().save(ag);
        GlobalUser inactiveuser = globalCrudService().find(GlobalUser.class, 1);
        Rule ruleForPG = UniqueRuleCreator.createRule(globalCrudService(), clientId, inactiveuser.getId());
        UniqueRuleAttributeValueMappingCreator.createRuleAttributeMapping(globalCrudService(), attriButeValuePG.getId(), ruleForPG.getId());

        PropertyGroup pg = UniquePropertyGroupCreator.createPropertyGroup(globalCrudService(), clientId);
        pg.setRuleId(ruleForPG.getId());
        globalCrudService().save(pg);

        inactiveuser.setActive(false);
        globalCrudService().save(inactiveuser);
        globalCrudService().getEntityManager().flush();

        List<Integer> ruleAttributeMapping = service.getRuleAttributeMapping();
        assertNotNull(ruleAttributeMapping);
        assertTrue(ruleAttributeMapping.contains(attriButeValueAG.getId()));
        assertFalse(ruleAttributeMapping.contains(attriButeValuePG.getId()));
    }

    @Test
    public void shouldDeleteRules() {
        Rule rule = UniqueRuleCreator.createRule(globalCrudService(), PacmanWorkContextHelper.getClientId(), Integer.parseInt(PacmanWorkContextHelper.getUserId()));
        Integer ruleID = rule.getId();
        List<Integer> rules = Arrays.asList(rule.getId());
        service.deleteRules(rules);
        Query qry = globalCrudService().getEntityManager().createNativeQuery("select rules.rule_id from rules where rule_id = :ruleId").setParameter("ruleId", ruleID);
        assertTrue(qry.getResultList().isEmpty());
    }

    @Test
    public void shouldDeleteRuleMappings() {
        Integer clientId = PacmanWorkContextHelper.getClientId();
        ClientAttributeValue attriButeValue = UniqueClientAttributeValueCreator.createUniqueClientAttributeValue(globalCrudService(), clientId);
        Rule rule = UniqueRuleCreator.createRule(globalCrudService(), clientId, Integer.parseInt(PacmanWorkContextHelper.getUserId()));
        UniqueRuleAttributeValueMappingCreator.createRuleAttributeMapping(globalCrudService(), attriButeValue.getId(), rule.getId());
        Integer ruleID = rule.getId();
        List<Integer> rules = Arrays.asList(rule.getId());
        service.deleteRuleValueMappings(rules);
        Query qry = globalCrudService().getEntityManager().createNativeQuery("select * from rule_attribute_Value where rule_id = :ruleId").setParameter("ruleId", ruleID);
        assertTrue(qry.getResultList().isEmpty());
    }

    @Test
    public void shouldRemovePropertyGroupsAndItsAssociations() {
        Integer clientId = PacmanWorkContextHelper.getClientId();
        ClientAttributeValue attriButeValue = UniqueClientAttributeValueCreator.createUniqueClientAttributeValue(globalCrudService(), clientId);
        Rule rule = UniqueRuleCreator.createRule(globalCrudService(), clientId, Integer.parseInt(PacmanWorkContextHelper.getUserId()));
        UniqueRuleAttributeValueMappingCreator.createRuleAttributeMapping(globalCrudService(), attriButeValue.getId(), rule.getId());
        PropertyGroup pg = UniquePropertyGroupCreator.createPropertyGroup(globalCrudService(), clientId);
        pg.setRuleId(rule.getId());
        globalCrudService().save(pg);
        UserActivityService activityservice = new UserActivityService();
        activityservice.setCrudService(globalCrudService());
        Integer id = activityservice.startActivity(UserActivityPageCodeEnum.OVERBOOKING.getPageCode(),
                ActivityType.VIEW_PAGE, null, null, "true");
        assertNotNull(id);
        UserActivity activity = globalCrudService().find(UserActivity.class, id);
        activity.setPropertyGroupId(pg.getId());
        globalCrudService().save(activity);

        Integer ruleID = rule.getId();
        List<Integer> rules = Arrays.asList(rule.getId());
        service.removeRulesFromPropertyGroups(rules);
        globalCrudService().getEntityManager().flush();

        Query qry = globalCrudService().getEntityManager().createNativeQuery("select * from property_Group where rule_id = :ruleId").setParameter("ruleId", ruleID);
        assertTrue(qry.getResultList().isEmpty());
        qry = globalCrudService().getEntityManager().createNativeQuery("select * from system_usage where property_group_id = :propertyGroupId").setParameter("propertyGroupId", pg.getId());
        assertTrue(qry.getResultList().isEmpty());
    }

    @Test
    public void shouldGetRulesUsedInPropertyGroupsForClientAttributeWithOneValue() {
        Integer clientId = PacmanWorkContextHelper.getClientId();
        ClientAttributeValue attriButeValue1 = UniqueClientAttributeValueCreator.createUniqueClientAttributeValue(globalCrudService(), clientId);
        ClientAttributeValue attriButeValue2 = UniqueClientAttributeValueCreator.createUniqueClientAttributeValue(globalCrudService(), clientId);
        int userId = Integer.parseInt(PacmanWorkContextHelper.getUserId());
        Rule rule1 = UniqueRuleCreator.createRule(globalCrudService(), clientId, userId);
        UniqueRuleAttributeValueMappingCreator.createRuleAttributeMapping(globalCrudService(), attriButeValue1.getId(), rule1.getId());
        PropertyGroup pg = UniquePropertyGroupCreator.createPropertyGroup(globalCrudService(), clientId);
        pg.setRuleId(rule1.getId());
        globalCrudService().save(pg);

        Rule rule2 = UniqueRuleCreator.createRule(globalCrudService(), clientId, userId);
        UniqueRuleAttributeValueMappingCreator.createRuleAttributeMapping(globalCrudService(), attriButeValue2.getId(), rule2.getId());
        AuthorizationGroup ag = UniqueAuthorizationGroupCreator.createAuthorizationGroup(globalCrudService(), clientId, userId);
        ag.setRuleId(rule2.getId());
        globalCrudService().save(ag);
        globalCrudService().getEntityManager().flush();

        Map<Integer, Set<Integer>> rules = service.getRulesGettingUsedInPG(attriButeValue1.getClientAttribute().getId(), null);
        List<Integer> ruleIds = rules.values().stream().flatMap(Set::stream).collect(Collectors.toList());
        assertTrue(ruleIds.size() == 1);
        assertTrue(ruleIds.get(0).intValue() == rule1.getId().intValue());
    }

    @Test
    public void shouldGetRulesUsedInPropertyGroupsForClientAttributeWithMultipleValue() {
        Integer clientId = PacmanWorkContextHelper.getClientId();
        ClientAttribute attriBute1 = UniqueClientAttributeCreator.createUniqueClientAttribute(globalCrudService(), clientId);
        ClientAttributeValue attriButeValue1 = UniqueClientAttributeValueCreator.createUniqueClientAttributeValue(globalCrudService(), clientId, attriBute1.getId());
        UniqueClientAttributeValueCreator.createUniqueClientAttributeValue(globalCrudService(), clientId);
        ClientAttributeValue attriButeValue3 = UniqueClientAttributeValueCreator.createUniqueClientAttributeValue(globalCrudService(), clientId, attriBute1.getId());
        Rule rule1 = UniqueRuleCreator.createRule(globalCrudService(), clientId, Integer.parseInt(PacmanWorkContextHelper.getUserId()));
        UniqueRuleAttributeValueMappingCreator.createRuleAttributeMapping(globalCrudService(), attriButeValue1.getId(), rule1.getId());
        PropertyGroup pg1 = UniquePropertyGroupCreator.createPropertyGroup(globalCrudService(), clientId);
        pg1.setRuleId(rule1.getId());
        globalCrudService().save(pg1);
        Rule rule2 = UniqueRuleCreator.createRule(globalCrudService(), clientId, Integer.parseInt(PacmanWorkContextHelper.getUserId()));
        UniqueRuleAttributeValueMappingCreator.createRuleAttributeMapping(globalCrudService(), attriButeValue3.getId(), rule2.getId());
        PropertyGroup pg2 = UniquePropertyGroupCreator.createPropertyGroup(globalCrudService(), clientId);
        pg2.setRuleId(rule2.getId());
        globalCrudService().save(pg2);
        globalCrudService().getEntityManager().flush();

        Map<Integer, Set<Integer>> rules = service.getRulesGettingUsedInPG(attriBute1.getId(), null);
        List<Integer> ruleIds = rules.values().stream().flatMap(Set::stream).collect(Collectors.toList());
        assertTrue(ruleIds.size() == 2);
        assertTrue(ruleIds.contains(rule1.getId()));
        assertTrue(ruleIds.contains(rule2.getId()));
    }

    @Test
    public void shouldGetRulesUsedInPropertyGroupsForClientAttributeValue() {
        Integer clientId = PacmanWorkContextHelper.getClientId();
        ClientAttribute attriBute1 = UniqueClientAttributeCreator.createUniqueClientAttribute(globalCrudService(), clientId);
        ClientAttributeValue attriButeValue1 = UniqueClientAttributeValueCreator.createUniqueClientAttributeValue(globalCrudService(), clientId, attriBute1.getId());
        UniqueClientAttributeValueCreator.createUniqueClientAttributeValue(globalCrudService(), clientId);
        ClientAttributeValue attriButeValue3 = UniqueClientAttributeValueCreator.createUniqueClientAttributeValue(globalCrudService(), clientId, attriBute1.getId());
        Rule rule1 = UniqueRuleCreator.createRule(globalCrudService(), clientId, Integer.parseInt(PacmanWorkContextHelper.getUserId()));
        UniqueRuleAttributeValueMappingCreator.createRuleAttributeMapping(globalCrudService(), attriButeValue1.getId(), rule1.getId());
        PropertyGroup pg1 = UniquePropertyGroupCreator.createPropertyGroup(globalCrudService(), clientId);
        pg1.setRuleId(rule1.getId());
        globalCrudService().save(pg1);
        Rule rule2 = UniqueRuleCreator.createRule(globalCrudService(), clientId, Integer.parseInt(PacmanWorkContextHelper.getUserId()));
        UniqueRuleAttributeValueMappingCreator.createRuleAttributeMapping(globalCrudService(), attriButeValue3.getId(), rule2.getId());
        PropertyGroup pg2 = UniquePropertyGroupCreator.createPropertyGroup(globalCrudService(), clientId);
        pg2.setRuleId(rule2.getId());
        globalCrudService().save(pg2);
        globalCrudService().getEntityManager().flush();

        Map<Integer, Set<Integer>> rules = service.getRulesGettingUsedInPG(attriBute1.getId(), attriButeValue1.getId());
        List<Integer> ruleIds = rules.values().stream().flatMap(Set::stream).collect(Collectors.toList());
        assertTrue(ruleIds.size() == 1);
        assertTrue(ruleIds.get(0).intValue() == rule1.getId().intValue());
    }

    @Test
    public void testHandleRulesAndMappings() {
        CrudService mockGlobalCrudService = mock(CrudService.class);
        service.setGlobalCrudService(mockGlobalCrudService);
        List<Object[]> rules = new ArrayList<>();
        rules.add(new Object[]{1, 1});
        List<Integer> rules1 = List.of(1);
        EntityManager mockEntityManager = mock(EntityManager.class);
        Query mockQuery1 = mock(Query.class);
        Query mockQuery2 = mock(Query.class);
        when(mockGlobalCrudService.getEntityManager()).thenReturn(mockEntityManager);
        when(mockEntityManager.createNamedQuery(any(String.class))).thenReturn(mockQuery1);
        when(mockEntityManager.createNativeQuery(any(String.class))).thenReturn(mockQuery2);
        when(mockQuery1.setParameter("ruleIds", rules1)).thenReturn(mockQuery1);
        when(mockQuery1.executeUpdate()).thenReturn(1);
        when(mockQuery2.getResultList()).thenReturn(rules);

        service.handleRulesAndMappings(1, null);

        verify(mockQuery2).getResultList();
    }

    @Test
    public void testhandleRulesAndMappingsWithNoRules() {
        CrudService mockGlobalCrudService = mock(CrudService.class);
        service.setGlobalCrudService(mockGlobalCrudService);
        List<Integer> rules = new ArrayList<>();

        EntityManager mockEntityManager = mock(EntityManager.class);
        Query mockQuery1 = mock(Query.class);
        when(mockGlobalCrudService.getEntityManager()).thenReturn(mockEntityManager);
        when(mockEntityManager.createNativeQuery(any(String.class))).thenReturn(mockQuery1);
        when(mockQuery1.setParameter("ruleIds", rules)).thenReturn(mockQuery1);
        when(mockQuery1.getResultList()).thenReturn(rules);

        service.handleRulesAndMappings(1, null);

        verify(mockQuery1).getResultList();
        verify(mockGlobalCrudService).getEntityManager();
        verify(mockEntityManager).createNativeQuery(any(String.class));
    }

    @SuppressWarnings("unchecked")
    @Test
    public void testdeleteClientAttributeWithNoRules() {
        CrudService mockGlobalCrudService = mock(CrudService.class);
        service.setGlobalCrudService(mockGlobalCrudService);
        PacmanConfigParamsService mockPacmanConfigParamsService = mock(PacmanConfigParamsService.class);
        service.setConfigParamsService(mockPacmanConfigParamsService);
        UPSService mockUpsService = mock(UPSService.class);
        service.setUpsService(mockUpsService);

        List<Integer> rules = new ArrayList<>();
        ClientAttribute clientAttribute = new ClientAttribute();
        clientAttribute.setId(1);

        EntityManager mockEntityManager = mock(EntityManager.class);
        Query mockQuery1 = mock(Query.class);
        when(mockGlobalCrudService.getEntityManager()).thenReturn(mockEntityManager);
        when(mockEntityManager.createNativeQuery(any(String.class))).thenReturn(mockQuery1);
        when(mockQuery1.setParameter("ruleIds", rules)).thenReturn(mockQuery1);
        when(mockQuery1.getResultList()).thenReturn(rules);
        when(mockGlobalCrudService.delete(any(Class.class), any(Object.class))).thenReturn(true);
        when(mockPacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.FDS_CLIENT_ATTRIBUTES_ENABLED)).thenReturn(false);

        service.deleteClientAttribute(clientAttribute);

        verify(mockQuery1).getResultList();
        verify(mockEntityManager).createNativeQuery(any(String.class));
        verify(mockUpsService, never()).deleteCustomAttributeValues(any(ClientAttribute.class));
    }

    @Test
    public void testdeleteClientAttributeWithNoRules_FDSEnabled() {
        CrudService mockGlobalCrudService = mock(CrudService.class);
        service.setGlobalCrudService(mockGlobalCrudService);
        PacmanConfigParamsService mockPacmanConfigParamsService = mock(PacmanConfigParamsService.class);
        service.setConfigParamsService(mockPacmanConfigParamsService);
        UPSService mockUpsService = mock(UPSService.class);
        service.setUpsService(mockUpsService);

        List<Integer> rules = new ArrayList<>();
        ClientAttribute clientAttribute = new ClientAttribute();
        clientAttribute.setId(1);

        EntityManager mockEntityManager = mock(EntityManager.class);
        Query mockQuery1 = mock(Query.class);
        when(mockGlobalCrudService.getEntityManager()).thenReturn(mockEntityManager);
        when(mockEntityManager.createNativeQuery(any(String.class))).thenReturn(mockQuery1);
        when(mockQuery1.setParameter("ruleIds", rules)).thenReturn(mockQuery1);
        when(mockQuery1.getResultList()).thenReturn(rules);
        when(mockGlobalCrudService.delete(any(Class.class), any(Object.class))).thenReturn(true);
        when(mockPacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.FDS_CLIENT_ATTRIBUTES_ENABLED)).thenReturn(true);

        service.deleteClientAttribute(clientAttribute);

        verify(mockQuery1).getResultList();
        verify(mockEntityManager).createNativeQuery(any(String.class));
        verify(mockUpsService).deleteCustomAttributeValues(any(ClientAttribute.class));
    }

    @SuppressWarnings("unchecked")
    @Test
    public void testdeleteClientAttributeValueWithNoRules() {
        CrudService mockGlobalCrudService = mock(CrudService.class);
        service.setGlobalCrudService(mockGlobalCrudService);
        List<Integer> rules = new ArrayList<>();
        ClientAttribute clientAttribute = new ClientAttribute();
        clientAttribute.setId(1);
        ClientAttributeValue clientAttributeValue = new ClientAttributeValue();
        clientAttributeValue.setId(1);
        clientAttributeValue.setClientAttribute(clientAttribute);
        List<ClientAttributeValue> valueList = new ArrayList<ClientAttributeValue>();
        valueList.add(clientAttributeValue);

        EntityManager mockEntityManager = mock(EntityManager.class);
        Query mockQuery1 = mock(Query.class);
        when(mockGlobalCrudService.getEntityManager()).thenReturn(mockEntityManager);
        when(mockEntityManager.createNativeQuery(any(String.class))).thenReturn(mockQuery1);
        when(mockQuery1.setParameter("ruleIds", rules)).thenReturn(mockQuery1);
        when(mockQuery1.getResultList()).thenReturn(rules);
        when(mockGlobalCrudService.delete(any(Class.class), any(Object.class))).thenReturn(true);


        service.deleteClientAttributeValue(valueList);

        verify(mockQuery1).getResultList();
        verify(mockGlobalCrudService).delete(any(Class.class), any(Object.class));
        verify(mockEntityManager).createNativeQuery(any(String.class));
    }

    @Test
    public void testGetAllAttributesForClient() {
        List<PropertyAttributeAssignmentDTO> reportData = service.getAllPropetyAttributesForClient();
        assertNotNull(service.getAllPropetyAttributesForClient());

        validatePropertyAttributeAssignmentReportData("Validate Property Attribute Assignment Report Data for FAYNC property for First Attribute ", reportData, 0, "1", "FAYNC", "Hampton Inn Fayetteville", "Brand", "--", "1");
        validatePropertyAttributeAssignmentReportData("Validate Property Attribute Assignment Report Data for FAYNC property for Second Attribute ", reportData, 0, "2", "FAYNC", "Hampton Inn Fayetteville", "Region", "--", "1");
        validatePropertyAttributeAssignmentReportData("Validate Property Attribute Assignment Report Data for FAYNC property for Third Attribute ", reportData, 0, "3", "FAYNC", "Hampton Inn Fayetteville", "Airport", "--", "2");
        validatePropertyAttributeAssignmentReportData("Validate Property Attribute Assignment Report Data for FAYNC property for Fourth Attribute ", reportData, 0, "4", "FAYNC", "Hampton Inn Fayetteville", "No of Rooms", "--", "3");

        validatePropertyAttributeAssignmentReportData("Validate Property Attribute Assignment Report Data for H1 property for First Attribute ", reportData, 1, "1", "H1", "Hilton - Pune", "Brand", "Hilton", "1");
        validatePropertyAttributeAssignmentReportData("Validate Property Attribute Assignment Report Data for H1 property for Second Attribute ", reportData, 1, "2", "H1", "Hilton - Pune", "Region", "Asia", "1");
        validatePropertyAttributeAssignmentReportData("Validate Property Attribute Assignment Report Data for H1 property for Third Attribute ", reportData, 1, "3", "H1", "Hilton - Pune", "Airport", "Yes", "2");
        validatePropertyAttributeAssignmentReportData("Validate Property Attribute Assignment Report Data for H1 property for Fourth Attribute ", reportData, 1, "4", "H1", "Hilton - Pune", "No of Rooms", "300", "3");

        validatePropertyAttributeAssignmentReportData("Validate Property Attribute Assignment Report Data for H2 property for First Attribute ", reportData, 2, "1", "H2", "Hilton - Paris", "Brand", "Hilton", "1");
        validatePropertyAttributeAssignmentReportData("Validate Property Attribute Assignment Report Data for H2 property for Second Attribute ", reportData, 2, "2", "H2", "Hilton - Paris", "Region", "Europe", "1");
        validatePropertyAttributeAssignmentReportData("Validate Property Attribute Assignment Report Data for H2 property for Third Attribute ", reportData, 2, "3", "H2", "Hilton - Paris", "Airport", "--", "2");
        validatePropertyAttributeAssignmentReportData("Validate Property Attribute Assignment Report Data for H2 property for Fourth Attribute ", reportData, 2, "4", "H2", "Hilton - Paris", "No of Rooms", "250", "3");

    }

    private void validatePropertyAttributeAssignmentReportData(String Level, List<PropertyAttributeAssignmentDTO> reportData, int recordNumber, String firstAttribute, String propertyCode, String propertyName, String attributeName, String attributeValue, String attributeDisplayTypeId) {
        assertEquals(5, reportData.size(), "Number of records retrieved for Property Attribute Assignment Report ");
        assertEquals(propertyCode, (reportData.get(recordNumber).getPropertyCode()), Level);
        assertEquals(propertyName, (reportData.get(recordNumber).getPropertyName()), Level);
        assertEquals(attributeName, reportData.get(recordNumber).getAttributeDetailsMap().get(firstAttribute).getAttributeName(), Level);
        assertEquals(attributeValue, reportData.get(recordNumber).getAttributeDetailsMap().get(firstAttribute).getAttributeValue(), Level);
        assertEquals(attributeDisplayTypeId, reportData.get(recordNumber).getAttributeDetailsMap().get(firstAttribute).getAttributeDisplayTypeId().toString(), Level);
    }

    @SuppressWarnings("unchecked")
    @Test
    public void shouldRemoveClientPropertyAttributePairingForClientAttributeByPagination() {
        ClientPropertyAttributePairing cpap = createClientAttributeItsValueAndMappingWithProperty(null);
        Integer clientAttributeValueId = cpap.getClientAttributeValue().getId();
        List<Integer> propertyIds = generatePropertyIds();
        List<ClientPropertyAttributePairing> cpapListBeforeRemoval = globalCrudService().getEntityManager().createQuery("SELECT cpap FROM ClientPropertyAttributePairing as cpap WHERE cpap.clientAttributeValue.id = :clientAttributeValueId AND cpap.status = 1")
                .setParameter("clientAttributeValueId", clientAttributeValueId).getResultList();
        assertNotNull(cpapListBeforeRemoval);
        assertTrue(cpapListBeforeRemoval.get(0).getClientAttributeValue().getId() == clientAttributeValueId);
        service.removeClientAttributeValuesForPropertiesByPagination(cpap.getClientAttributeValue().getClientAttribute().getId(), propertyIds, 2);
        List<ClientPropertyAttributePairing> cpapListAfterRemoval = globalCrudService().getEntityManager().createQuery("SELECT cpap FROM ClientPropertyAttributePairing as cpap WHERE cpap.clientAttributeValue.id = :clientAttributeValueId AND cpap.status = 1")
                .setParameter("clientAttributeValueId", clientAttributeValueId).getResultList();
        assertTrue(cpapListAfterRemoval.isEmpty());
    }

    @Test
    public void shouldFetchClientAttributeValuesForPropertyIdsAndClientAttribute() {
        List<ClientPropertyAttributePairing> clientPropertyAttributePairingList = new ArrayList<ClientPropertyAttributePairing>();
        List<Integer> propertyIds = generatePropertyIds();

        ClientPropertyAttributePairing cpap = createClientAttributeItsValueAndMappingWithProperty(null);
        Integer clientAttributeValueId = cpap.getClientAttributeValue().getId();
        clientPropertyAttributePairingList = service.getClientPropertyAttributePairing(propertyIds, cpap.getClientAttributeValue().getClientAttribute().getId());

        assertNotNull(clientPropertyAttributePairingList);
        assertFalse(clientPropertyAttributePairingList.isEmpty());
        assertEquals(4, clientPropertyAttributePairingList.size());
        assertEquals(clientAttributeValueId, clientPropertyAttributePairingList.get(0).getClientAttributeValue().getId());
        assertEquals(PacmanWorkContextTestHelper.WC_PROPERTY_ID_PUNE, clientPropertyAttributePairingList.get(0).getPropertyID());
        assertEquals(PacmanWorkContextTestHelper.WC_PROPERTY_ID_PARIS, clientPropertyAttributePairingList.get(1).getPropertyID());
        assertEquals(PacmanWorkContextTestHelper.WC_PROPERTY_ID_SHERATON_CARY, clientPropertyAttributePairingList.get(2).getPropertyID());
        assertEquals(PacmanWorkContextTestHelper.WC_PROPERTY_ID_HILTON_BOSCO, clientPropertyAttributePairingList.get(3).getPropertyID());
    }

    @Test
    public void shouldFetchClientAttributeValuesForPropertyIdsAndClientAttributeForPageWithPageSize() {
        List<ClientPropertyAttributePairing> clientPropertyAttributePairingList = new ArrayList<ClientPropertyAttributePairing>();
        List<Integer> propertyIds = generatePropertyIds();

        ClientPropertyAttributePairing cpap = createClientAttributeItsValueAndMappingWithProperty(null);
        Integer clientAttributeValueId = cpap.getClientAttributeValue().getId();
        int pageNo = 1;
        int pageSize = 2;
        //for page 1 with size 2
        clientPropertyAttributePairingList = service.getClientPropertyAttributePairingListForPageWithPageSize(pageNo, pageSize, propertyIds, cpap.getClientAttributeValue().getClientAttribute().getId());

        assertNotNull(clientPropertyAttributePairingList);
        assertFalse(clientPropertyAttributePairingList.isEmpty());
        assertEquals(pageSize, clientPropertyAttributePairingList.size());
        assertEquals(clientAttributeValueId, clientPropertyAttributePairingList.get(0).getClientAttributeValue().getId());
        assertEquals(PacmanWorkContextTestHelper.WC_PROPERTY_ID_PUNE, clientPropertyAttributePairingList.get(0).getPropertyID());
        assertEquals(PacmanWorkContextTestHelper.WC_PROPERTY_ID_PARIS, clientPropertyAttributePairingList.get(1).getPropertyID());

        //for page 2 with size 2
        pageNo = 2;
        clientPropertyAttributePairingList = service.getClientPropertyAttributePairingListForPageWithPageSize(pageNo, pageSize, propertyIds, cpap.getClientAttributeValue().getClientAttribute().getId());

        assertNotNull(clientPropertyAttributePairingList);
        assertFalse(clientPropertyAttributePairingList.isEmpty());
        assertEquals(pageSize, clientPropertyAttributePairingList.size());
        assertEquals(clientAttributeValueId, clientPropertyAttributePairingList.get(0).getClientAttributeValue().getId());
        assertEquals(PacmanWorkContextTestHelper.WC_PROPERTY_ID_SHERATON_CARY, clientPropertyAttributePairingList.get(0).getPropertyID());
        assertEquals(PacmanWorkContextTestHelper.WC_PROPERTY_ID_HILTON_BOSCO, clientPropertyAttributePairingList.get(1).getPropertyID());
    }

    private List<Integer> generatePropertyIds() {
        List<Integer> propertyIds = new ArrayList<Integer>();
        propertyIds.add(PacmanWorkContextTestHelper.WC_PROPERTY_ID_PUNE);
        propertyIds.add(PacmanWorkContextTestHelper.WC_PROPERTY_ID_PARIS);
        propertyIds.add(PacmanWorkContextTestHelper.WC_PROPERTY_ID_SHERATON_CARY);
        propertyIds.add(PacmanWorkContextTestHelper.WC_PROPERTY_ID_HILTON_BOSCO);
        return propertyIds;
    }

    @Test
    public void shouldFetchClientAttributeValuesForPropertyIdsAndClientAttributeByPagination() {
        List<Integer> propertyIds = generatePropertyIds();

        List<Integer> propertyIdsForFirstPage = new ArrayList<Integer>();
        propertyIdsForFirstPage.add(PacmanWorkContextTestHelper.WC_PROPERTY_ID_PUNE);
        propertyIdsForFirstPage.add(PacmanWorkContextTestHelper.WC_PROPERTY_ID_PARIS);

        List<Integer> propertyIdsForSecondPage = new ArrayList<Integer>();
        propertyIdsForSecondPage.add(PacmanWorkContextTestHelper.WC_PROPERTY_ID_SHERATON_CARY);
        propertyIdsForSecondPage.add(PacmanWorkContextTestHelper.WC_PROPERTY_ID_HILTON_BOSCO);

        int clientAttributeId = 2;
        int pageSize = 2;

        CrudService mockGlobalCrudService = mock(CrudService.class);
        service.setGlobalCrudService(mockGlobalCrudService);
        when(mockGlobalCrudService.<ClientPropertyAttributePairing>findByNamedQuery(ClientPropertyAttributePairing.RECORDS_BYPROPERTYID_AND_ATTRIBUTE_ID, QueryParameter
                .with("clientAttributeId", clientAttributeId).and("propertyIdList", propertyIdsForFirstPage).parameters())).thenReturn(new ArrayList<ClientPropertyAttributePairing>());
        when(mockGlobalCrudService.<ClientPropertyAttributePairing>findByNamedQuery(ClientPropertyAttributePairing.RECORDS_BYPROPERTYID_AND_ATTRIBUTE_ID, QueryParameter
                .with("clientAttributeId", clientAttributeId).and("propertyIdList", propertyIdsForSecondPage).parameters())).thenReturn(new ArrayList<ClientPropertyAttributePairing>());

        service.getClientPropertyAttributePairingListByPagination(propertyIds, clientAttributeId, pageSize);

        verify(mockGlobalCrudService).findByNamedQuery(ClientPropertyAttributePairing.RECORDS_BYPROPERTYID_AND_ATTRIBUTE_ID, QueryParameter
                .with("clientAttributeId", clientAttributeId).and("propertyIdList", propertyIdsForFirstPage).parameters());
        verify(mockGlobalCrudService).findByNamedQuery(ClientPropertyAttributePairing.RECORDS_BYPROPERTYID_AND_ATTRIBUTE_ID, QueryParameter
                .with("clientAttributeId", clientAttributeId).and("propertyIdList", propertyIdsForSecondPage).parameters());
    }

    @SuppressWarnings("unchecked")
    @Test
    public void shouldUpdateAttributeValueForPairingIds() {
        List<Integer> clientPropertyAttributePairings = new ArrayList<Integer>();
        ClientPropertyAttributePairing cpap = createClientAttributeItsValueAndMappingWithProperty(clientPropertyAttributePairings);

        ClientAttributeValue clientAttributeValueForUpdate = UniqueClientAttributeValueCreator.createUniqueClientAttributeValue(globalCrudService(), 2, cpap.getClientAttributeValue().getClientAttribute().getId());

        Integer updatedRows = service.updateAttributeValueForPairingIds(clientPropertyAttributePairings, clientAttributeValueForUpdate.getId());
        assertNotNull(updatedRows);
        assertTrue(4 == updatedRows);
        globalCrudService().getEntityManager().clear();
        List<ClientPropertyAttributePairing> cpapListAfterUpdation = globalCrudService().getEntityManager().createQuery("select cpap from ClientPropertyAttributePairing as cpap where cpap.status = 1 and cpap.id IN (:pairingIds)")
                .setParameter("pairingIds", clientPropertyAttributePairings).getResultList();

        assertNotNull(cpapListAfterUpdation);
        assertFalse(cpapListAfterUpdation.isEmpty());
        assertTrue(4 == cpapListAfterUpdation.size());
        assertEquals(clientAttributeValueForUpdate.getId(), cpapListAfterUpdation.get(0).getClientAttributeValue().getId());
        assertEquals(clientAttributeValueForUpdate.getId(), cpapListAfterUpdation.get(1).getClientAttributeValue().getId());
        assertEquals(clientAttributeValueForUpdate.getId(), cpapListAfterUpdation.get(2).getClientAttributeValue().getId());
        assertEquals(clientAttributeValueForUpdate.getId(), cpapListAfterUpdation.get(3).getClientAttributeValue().getId());
    }

    private ClientPropertyAttributePairing createClientAttributeItsValueAndMappingWithProperty(
            List<Integer> clientPropertyAttributePairings) {
        ClientPropertyAttributePairing cpap = UniqueClientPropertyAttributePairingCreator.createUniqueClientPropertyAttributePairing(globalCrudService(), 2, PacmanWorkContextTestHelper.WC_PROPERTY_ID_PUNE, "Location");
        Integer clientAttributeValueId = cpap.getClientAttributeValue().getId();

        ClientPropertyAttributePairing cpap2 = UniqueClientPropertyAttributePairingCreator.createUniqueClientPropertyAttributePairingByClientAttributeValue(globalCrudService(), 2, PacmanWorkContextTestHelper.WC_PROPERTY_ID_PARIS, clientAttributeValueId);
        ClientPropertyAttributePairing cpap3 = UniqueClientPropertyAttributePairingCreator.createUniqueClientPropertyAttributePairingByClientAttributeValue(globalCrudService(), 2, PacmanWorkContextTestHelper.WC_PROPERTY_ID_SHERATON_CARY, clientAttributeValueId);
        ClientPropertyAttributePairing cpap4 = UniqueClientPropertyAttributePairingCreator.createUniqueClientPropertyAttributePairingByClientAttributeValue(globalCrudService(), 2, PacmanWorkContextTestHelper.WC_PROPERTY_ID_HILTON_BOSCO, clientAttributeValueId);
        if (clientPropertyAttributePairings != null) {
            clientPropertyAttributePairings.add(cpap.getId());
            clientPropertyAttributePairings.add(cpap2.getId());
            clientPropertyAttributePairings.add(cpap3.getId());
            clientPropertyAttributePairings.add(cpap4.getId());
        }
        return cpap;
    }

    @SuppressWarnings("unchecked")
    @Test
    public void shouldUpdateAttributeValueForPairingIdsForPageWithPageSize() {
        List<Integer> clientPropertyAttributePairings = new ArrayList<Integer>();
        ClientPropertyAttributePairing cpap = createClientAttributeItsValueAndMappingWithProperty(clientPropertyAttributePairings);
        Integer clientAttributeValueIdBeforeUpdate = cpap.getClientAttributeValue().getId();

        ClientAttributeValue clientAttributeValueForUpdate = UniqueClientAttributeValueCreator.createUniqueClientAttributeValue(globalCrudService(), 2, cpap.getClientAttributeValue().getClientAttribute().getId());

        int pageNo = 1;
        int pageSize = 2;

        //for page 1 with size 2
        Integer updatedRows = service.updateAttributeValueForPairingIdsForPageWithPageSize(pageNo, pageSize, clientPropertyAttributePairings, clientAttributeValueForUpdate.getId());
        assertNotNull(updatedRows);
        assertTrue(2 == updatedRows);
        globalCrudService().getEntityManager().clear();
        List<ClientPropertyAttributePairing> cpapListAfterUpdation = globalCrudService().getEntityManager().createQuery("select cpap from ClientPropertyAttributePairing as cpap where cpap.status = 1 and cpap.id IN (:pairingIds)")
                .setParameter("pairingIds", clientPropertyAttributePairings)
                .getResultList();
        assertNotNull(cpapListAfterUpdation);
        assertFalse(cpapListAfterUpdation.isEmpty());
        assertTrue(4 == cpapListAfterUpdation.size());
        assertEquals(clientAttributeValueForUpdate.getId(), cpapListAfterUpdation.get(0).getClientAttributeValue().getId());
        assertEquals(clientAttributeValueForUpdate.getId(), cpapListAfterUpdation.get(1).getClientAttributeValue().getId());
        assertEquals(clientAttributeValueIdBeforeUpdate, cpapListAfterUpdation.get(2).getClientAttributeValue().getId());
        assertEquals(clientAttributeValueIdBeforeUpdate, cpapListAfterUpdation.get(3).getClientAttributeValue().getId());

        //for page 2 with size 2
        pageNo++;
        updatedRows = service.updateAttributeValueForPairingIdsForPageWithPageSize(pageNo, pageSize, clientPropertyAttributePairings, clientAttributeValueForUpdate.getId());
        assertNotNull(updatedRows);
        assertTrue(2 == updatedRows);
        globalCrudService().getEntityManager().clear();
        cpapListAfterUpdation = globalCrudService().getEntityManager().createQuery("select cpap from ClientPropertyAttributePairing as cpap where cpap.status = 1 and cpap.id IN (:pairingIds)")
                .setParameter("pairingIds", clientPropertyAttributePairings).getResultList();
        assertNotNull(cpapListAfterUpdation);
        assertFalse(cpapListAfterUpdation.isEmpty());
        assertTrue(4 == cpapListAfterUpdation.size());
        assertEquals(clientAttributeValueForUpdate.getId(), cpapListAfterUpdation.get(0).getClientAttributeValue().getId());
        assertEquals(clientAttributeValueForUpdate.getId(), cpapListAfterUpdation.get(1).getClientAttributeValue().getId());
        assertEquals(clientAttributeValueForUpdate.getId(), cpapListAfterUpdation.get(2).getClientAttributeValue().getId());
        assertEquals(clientAttributeValueForUpdate.getId(), cpapListAfterUpdation.get(3).getClientAttributeValue().getId());
    }

    @Test
    public void shouldUpdateAttributeValueForPairingIdsByPagination() {
        List<Integer> clientPropertyAttributePairings = new ArrayList<Integer>();
        clientPropertyAttributePairings.add(10);
        clientPropertyAttributePairings.add(11);
        clientPropertyAttributePairings.add(12);
        clientPropertyAttributePairings.add(13);

        Integer attributeValueIdForUpdate = 20;
        Integer pageSize = 2;

        List<Integer> clientPropertyAttributePairingsForFirstPage = new ArrayList<Integer>();
        clientPropertyAttributePairingsForFirstPage.add(10);
        clientPropertyAttributePairingsForFirstPage.add(11);
        List<Integer> clientPropertyAttributePairingsForSecondPage = new ArrayList<Integer>();
        clientPropertyAttributePairingsForSecondPage.add(12);
        clientPropertyAttributePairingsForSecondPage.add(13);

        CrudService mockGlobalCrudService = mock(CrudService.class);
        service.setGlobalCrudService(mockGlobalCrudService);
        when(mockGlobalCrudService.executeUpdateByNamedQuery(ClientPropertyAttributePairing.UPDATE_PAIRING_ATTRIBUTE_VALUE_BY_ID, QueryParameter.with("clientAttributeValueId", attributeValueIdForUpdate).and("id", clientPropertyAttributePairingsForFirstPage).parameters())).thenReturn(2);
        when(mockGlobalCrudService.executeUpdateByNamedQuery(ClientPropertyAttributePairing.UPDATE_PAIRING_ATTRIBUTE_VALUE_BY_ID, QueryParameter.with("clientAttributeValueId", attributeValueIdForUpdate).and("id", clientPropertyAttributePairingsForSecondPage).parameters())).thenReturn(2);
        service.updateAttributeValueForPairingIdsByPagination(attributeValueIdForUpdate, clientPropertyAttributePairings, pageSize);
        verify(mockGlobalCrudService).executeUpdateByNamedQuery(ClientPropertyAttributePairing.UPDATE_PAIRING_ATTRIBUTE_VALUE_BY_ID,
                QueryParameter.with("clientAttributeValueId", attributeValueIdForUpdate).and("id", clientPropertyAttributePairingsForFirstPage).parameters());
        verify(mockGlobalCrudService).executeUpdateByNamedQuery(ClientPropertyAttributePairing.UPDATE_PAIRING_ATTRIBUTE_VALUE_BY_ID,
                QueryParameter.with("clientAttributeValueId", attributeValueIdForUpdate).and("id", clientPropertyAttributePairingsForSecondPage).parameters());
    }

    @Test
    public void shouldReturnClientAttributePropertyAttributePairingAsMap() throws Exception {
        Map<ClientAttribute, ClientPropertyAttributePairing> clientPropertyAttributePairingMap
                = service.populateAttributesAndPairingMap(PacmanWorkContextTestHelper.WC_PROPERTY_ID_PUNE);
        assertEquals(4, clientPropertyAttributePairingMap.size());

        final List<ClientPropertyAttributePairing> clientPropertyAttributePairings = service.getAllClientPropertyAttributePairing(PacmanWorkContextTestHelper.WC_PROPERTY_ID_PUNE);
        assertEquals(4, clientPropertyAttributePairings.size());

        for (ClientPropertyAttributePairing clientPropertyAttributePairing : clientPropertyAttributePairings) {
            clientPropertyAttributePairingMap.containsKey(clientPropertyAttributePairing.getClientAttributeValue().getClientAttribute());
            clientPropertyAttributePairingMap.containsValue(clientPropertyAttributePairing.getClientAttributeValue().getClientAttributeValue());
        }
    }

    @Test
    public void testGetAllAttributesForClient_withVirtualPropertyDisplayCodeAsPropertyCode() {
        globalCrudService().executeUpdateByNativeQuery("update Property " +
                "set Virtual_Property_Display_Code= 'VP_CODE_FAYNC', Is_Virtual_Property = 1 " +
                "where Property_Code = 'FAYNC'");
        List<PropertyAttributeAssignmentDTO> reportData = service.getAllPropetyAttributesForClient();
        assertNotNull(reportData);
        reportData = reportData.stream().sorted(Comparator.comparing(PropertyAttributeAssignmentDTO::getPropertyCode)).collect(Collectors.toList());

        validatePropertyAttributeAssignmentReportData("Validate Property Attribute Assignment Report Data for H1 property for First Attribute ", reportData, 0, "1", "H1", "Hilton - Pune", "Brand", "Hilton", "1");
        validatePropertyAttributeAssignmentReportData("Validate Property Attribute Assignment Report Data for H1 property for Second Attribute ", reportData, 0, "2", "H1", "Hilton - Pune", "Region", "Asia", "1");
        validatePropertyAttributeAssignmentReportData("Validate Property Attribute Assignment Report Data for H1 property for Third Attribute ", reportData, 0, "3", "H1", "Hilton - Pune", "Airport", "Yes", "2");
        validatePropertyAttributeAssignmentReportData("Validate Property Attribute Assignment Report Data for H1 property for Fourth Attribute ", reportData, 0, "4", "H1", "Hilton - Pune", "No of Rooms", "300", "3");

        validatePropertyAttributeAssignmentReportData("Validate Property Attribute Assignment Report Data for H2 property for First Attribute ", reportData, 1, "1", "H2", "Hilton - Paris", "Brand", "Hilton", "1");
        validatePropertyAttributeAssignmentReportData("Validate Property Attribute Assignment Report Data for H2 property for Second Attribute ", reportData, 1, "2", "H2", "Hilton - Paris", "Region", "Europe", "1");
        validatePropertyAttributeAssignmentReportData("Validate Property Attribute Assignment Report Data for H2 property for Third Attribute ", reportData, 1, "3", "H2", "Hilton - Paris", "Airport", "--", "2");
        validatePropertyAttributeAssignmentReportData("Validate Property Attribute Assignment Report Data for H2 property for Fourth Attribute ", reportData, 1, "4", "H2", "Hilton - Paris", "No of Rooms", "250", "3");

        validatePropertyAttributeAssignmentReportData("Validate Property Attribute Assignment Report Data for FAYNC property for First Attribute ", reportData, 4, "1", "VP_CODE_FAYNC", "Hampton Inn Fayetteville", "Brand", "--", "1");
        validatePropertyAttributeAssignmentReportData("Validate Property Attribute Assignment Report Data for FAYNC property for Second Attribute ", reportData, 4, "2", "VP_CODE_FAYNC", "Hampton Inn Fayetteville", "Region", "--", "1");
        validatePropertyAttributeAssignmentReportData("Validate Property Attribute Assignment Report Data for FAYNC property for Third Attribute ", reportData, 4, "3", "VP_CODE_FAYNC", "Hampton Inn Fayetteville", "Airport", "--", "2");
        validatePropertyAttributeAssignmentReportData("Validate Property Attribute Assignment Report Data for FAYNC property for Fourth Attribute ", reportData, 4, "4", "VP_CODE_FAYNC", "Hampton Inn Fayetteville", "No of Rooms", "--", "3");
    }

    @Test
    public void getClientAttributeList() {
        CrudService mockGlobalCrudService = mock(CrudService.class);
        service.setGlobalCrudService(mockGlobalCrudService);

        service.getClientAttributeList();
        verify(mockGlobalCrudService).findByNamedQuery(ClientAttribute.ALL, QueryParameter.with("clientId", PacmanWorkContextHelper.getClientId()).parameters());
    }

    @Test
    public void getClientAttributeListByClientId() {
        CrudService mockGlobalCrudService = mock(CrudService.class);
        service.setGlobalCrudService(mockGlobalCrudService);

        service.getClientAttributeListByClientId(100);
        verify(mockGlobalCrudService).findByNamedQuery(ClientAttribute.ALL, QueryParameter.with("clientId", 100).parameters());
    }

    @Test
    public void getClientAttributeValuesListByClientId() {
        CrudService mockGlobalCrudService = mock(CrudService.class);
        service.setGlobalCrudService(mockGlobalCrudService);

        service.getClientAttributeValuesListByClientId(100);
        verify(mockGlobalCrudService).findByNamedQuery(ClientAttributeValue.BY_CLIENT_ID, QueryParameter.with("clientId", 100).parameters());
    }

    @Test
    public void saveClientAttributes() {
        CrudService mockGlobalCrudService = mock(CrudService.class);
        service.setGlobalCrudService(mockGlobalCrudService);

        List<ClientAttribute> clientAttributes = new ArrayList<>();
        ClientAttribute clientAttribute = new ClientAttribute();
        clientAttribute.setUpsCustomAttributeUuid("test");

        service.saveClientAttributes(clientAttributes);
        verify(mockGlobalCrudService).save(clientAttributes);
    }

    @Test
    public void clearClientAttributesUpsUuid() {
        CrudService mockGlobalCrudService = mock(CrudService.class);
        service.setGlobalCrudService(mockGlobalCrudService);

        ClientAttribute clientAttribute = new ClientAttribute();
        clientAttribute.setUpsCustomAttributeUuid(UUID.randomUUID().toString());
        ClientAttribute clientAttribute2 = new ClientAttribute();
        clientAttribute2.setUpsCustomAttributeUuid(UUID.randomUUID().toString());

        when(service.getClientAttributeListByClientId(100)).thenReturn(Arrays.asList(clientAttribute, clientAttribute2));

        service.clearClientAttributesUpsUuid(100);
        assertNull(clientAttribute.getUpsCustomAttributeUuid());
        assertNull(clientAttribute2.getUpsCustomAttributeUuid());

        verify(mockGlobalCrudService).save(Arrays.asList(clientAttribute, clientAttribute2));
    }

    @Test
    public void clearClientAttributesUpsUuid_NoAttributes() {
        CrudService mockGlobalCrudService = mock(CrudService.class);
        service.setGlobalCrudService(mockGlobalCrudService);

        when(service.getClientAttributeListByClientId(100)).thenReturn(null);

        service.clearClientAttributesUpsUuid(100);

        verify(mockGlobalCrudService, never()).save(anyList());
    }

    @Test
    public void migrateCustomAttributesForClient() {
        UPSService mockUpsService = mock(UPSService.class);
        service.setUpsService(mockUpsService);

        String clientCode = "clientCode";

        service.migrateCustomAttributesForClientToFDS(clientCode);
        verify(mockUpsService).migrateCustomAttributesForClient(clientCode);
    }

    @Test
    public void clearCustomAttributesForClientInFDS() {
        UPSService mockUpsService = mock(UPSService.class);
        service.setUpsService(mockUpsService);

        String clientCode = "clientCode";

        service.clearCustomAttributesForClientInFDS(clientCode);
        verify(mockUpsService).clearCustomAttributesForClient(clientCode);
    }

    @Test
    public void updateCustomAttributeAssignmentsForPropertyInFDS() {
        UPSService mockUpsService = mock(UPSService.class);
        service.setUpsService(mockUpsService);

        service.updateCustomAttributeAssignmentsForPropertyInFDS(1);
        verify(mockUpsService).updateCustomAttributeAssignmentsForProperties(Arrays.asList(1));
    }

    @Test
    public void updateG3CustomAttributeAssignmentsFromFDS() {
        UPSService mockUpsService = mock(UPSService.class);
        service.setUpsService(mockUpsService);

        service.updateG3CustomAttributeAssignmentsFromFDS(1);
        verify(mockUpsService).updateG3CustomAttributeAssignmentsFromFDS(1);
    }

    @Test
    public void updateG3CustomAttributesFromFDS() {
        UPSService mockUpsService = mock(UPSService.class);
        service.setUpsService(mockUpsService);

        service.updateG3CustomAttributesFromFDS(1);
        verify(mockUpsService).updateG3CustomAttributesFromFDS(1);
    }

    @Test
    public void propertyGroupWithMultipleAttributesButDeleteSingleAttributeOfIt() {
        PacmanConfigParamsService mockPacmanConfigParamsService = mock(PacmanConfigParamsService.class);
        service.setConfigParamsService(mockPacmanConfigParamsService);
        UPSService mockUpsService = mock(UPSService.class);
        service.setUpsService(mockUpsService);

        Integer clientId = PacmanWorkContextHelper.getClientId();
        ClientAttribute attribute1 = UniqueClientAttributeCreator.createUniqueClientAttribute(globalCrudService(), clientId);
        ClientAttributeValue attributeValue1 = UniqueClientAttributeValueCreator.createUniqueClientAttributeValue(globalCrudService(), clientId, attribute1.getId());

        ClientAttribute attribute2 = UniqueClientAttributeCreator.createUniqueClientAttribute(globalCrudService(), clientId);
        ClientAttributeValue attributeValue2 = UniqueClientAttributeValueCreator.createUniqueClientAttributeValue(globalCrudService(), clientId, attribute2.getId());

        Rule rule1 = UniqueRuleCreator.createRule(globalCrudService(), clientId, Integer.parseInt(PacmanWorkContextHelper.getUserId()));
        UniqueRuleAttributeValueMappingCreator.createRuleAttributeMapping(globalCrudService(), attributeValue1.getId(), rule1.getId());
        UniqueRuleAttributeValueMappingCreator.createRuleAttributeMapping(globalCrudService(), attributeValue2.getId(), rule1.getId());

        PropertyGroup pg = UniquePropertyGroupCreator.createPropertyGroup(globalCrudService(), clientId);
        pg.setRuleId(rule1.getId());
        globalCrudService().save(pg);

        when(mockPacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.FDS_CLIENT_ATTRIBUTES_ENABLED)).thenReturn(false);

        globalCrudService().getEntityManager().flush();
        service.deleteClientAttribute(attribute1);
        //verify database for attribute and its value to confirm deletion.
        long attributeValueCount1 = globalCrudService().findByNamedQuerySingleResult(RuleAttributeValueMapping.COUNT_BY_CLIENT_ATTRIBUTE_VALUE,
                QueryParameter.with("clientAttributeValue", attributeValue1).parameters());
        long attributeValueCount2 = globalCrudService().findByNamedQuerySingleResult(RuleAttributeValueMapping.COUNT_BY_CLIENT_ATTRIBUTE_VALUE,
                QueryParameter.with("clientAttributeValue", attributeValue2).parameters());
        List<PropertyGroup> propertyGroups = globalCrudService()
                .findByNamedQuery(PropertyGroup.RULE_BASED,
                        QueryParameter.with("clientId", clientId).parameters());
        List<Integer> propertyGroupIds = propertyGroups.stream().map(PropertyGroup::getId).collect(Collectors.toList());
        //attribute value 1 deleted but not attribute value 2
        assertThat(attributeValueCount1, is(0L));
        assertThat(attributeValueCount2, is(1L));
        // property group not deleted as attribute 2 ( attribute value 2) is not deleted, it's still associated with PG.
        assertTrue(propertyGroupIds.contains(pg.getId()));
        verify(mockUpsService, never()).deleteCustomAttributeValues(any(ClientAttribute.class));
    }

    @Test
    public void propertyGroupWithMultipleAttributesButDeleteSingleAttributeOfIt_FDSEnabled() {
        PacmanConfigParamsService mockPacmanConfigParamsService = mock(PacmanConfigParamsService.class);
        service.setConfigParamsService(mockPacmanConfigParamsService);
        UPSService mockUpsService = mock(UPSService.class);
        service.setUpsService(mockUpsService);

        Integer clientId = PacmanWorkContextHelper.getClientId();
        ClientAttribute attribute1 = UniqueClientAttributeCreator.createUniqueClientAttribute(globalCrudService(), clientId);
        ClientAttributeValue attributeValue1 = UniqueClientAttributeValueCreator.createUniqueClientAttributeValue(globalCrudService(), clientId, attribute1.getId());

        ClientAttribute attribute2 = UniqueClientAttributeCreator.createUniqueClientAttribute(globalCrudService(), clientId);
        ClientAttributeValue attributeValue2 = UniqueClientAttributeValueCreator.createUniqueClientAttributeValue(globalCrudService(), clientId, attribute2.getId());

        Rule rule1 = UniqueRuleCreator.createRule(globalCrudService(), clientId, Integer.parseInt(PacmanWorkContextHelper.getUserId()));
        UniqueRuleAttributeValueMappingCreator.createRuleAttributeMapping(globalCrudService(), attributeValue1.getId(), rule1.getId());
        UniqueRuleAttributeValueMappingCreator.createRuleAttributeMapping(globalCrudService(), attributeValue2.getId(), rule1.getId());

        PropertyGroup pg = UniquePropertyGroupCreator.createPropertyGroup(globalCrudService(), clientId);
        pg.setRuleId(rule1.getId());
        globalCrudService().save(pg);

        when(mockPacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.FDS_CLIENT_ATTRIBUTES_ENABLED)).thenReturn(true);

        globalCrudService().getEntityManager().flush();
        service.deleteClientAttribute(attribute1);
        //verify database for attribute and its value to confirm deletion.
        long attributeValueCount1 = globalCrudService().findByNamedQuerySingleResult(RuleAttributeValueMapping.COUNT_BY_CLIENT_ATTRIBUTE_VALUE,
                QueryParameter.with("clientAttributeValue", attributeValue1).parameters());
        long attributeValueCount2 = globalCrudService().findByNamedQuerySingleResult(RuleAttributeValueMapping.COUNT_BY_CLIENT_ATTRIBUTE_VALUE,
                QueryParameter.with("clientAttributeValue", attributeValue2).parameters());
        List<PropertyGroup> propertyGroups = globalCrudService()
                .findByNamedQuery(PropertyGroup.RULE_BASED,
                        QueryParameter.with("clientId", clientId).parameters());
        List<Integer> propertyGroupIds = propertyGroups.stream().map(PropertyGroup::getId).collect(Collectors.toList());
        //attribute value 1 deleted but not attribute value 2
        assertThat(attributeValueCount1, is(0L));
        assertThat(attributeValueCount2, is(1L));
        // property group not deleted as attribute 2 ( attribute value 2) is not deleted, it's still associated with PG.
        assertTrue(propertyGroupIds.contains(pg.getId()));
        verify(mockUpsService).deleteCustomAttributeValues(any(ClientAttribute.class));
    }

    @Test
    public void propertyGroupDeletedWhenDeleteAttributeOfIt() {
        PacmanConfigParamsService mockPacmanConfigParamsService = mock(PacmanConfigParamsService.class);
        service.setConfigParamsService(mockPacmanConfigParamsService);
        UPSService mockUpsService = mock(UPSService.class);
        service.setUpsService(mockUpsService);

        Integer clientId = PacmanWorkContextHelper.getClientId();
        ClientAttribute attribute1 = UniqueClientAttributeCreator.createUniqueClientAttribute(globalCrudService(), clientId);
        ClientAttributeValue attributeValue1 = UniqueClientAttributeValueCreator.createUniqueClientAttributeValue(globalCrudService(), clientId, attribute1.getId());

        Rule rule1 = UniqueRuleCreator.createRule(globalCrudService(), clientId, Integer.parseInt(PacmanWorkContextHelper.getUserId()));
        UniqueRuleAttributeValueMappingCreator.createRuleAttributeMapping(globalCrudService(), attributeValue1.getId(), rule1.getId());

        PropertyGroup pg = UniquePropertyGroupCreator.createPropertyGroup(globalCrudService(), clientId);
        pg.setRuleId(rule1.getId());
        globalCrudService().save(pg);

        when(mockPacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.FDS_CLIENT_ATTRIBUTES_ENABLED)).thenReturn(false);

        globalCrudService().getEntityManager().flush();
        service.deleteClientAttribute(attribute1);
        //verify database for attribute and its value to confirm deletion.
        long attributeValueCount1 = globalCrudService().findByNamedQuerySingleResult(RuleAttributeValueMapping.COUNT_BY_CLIENT_ATTRIBUTE_VALUE,
                QueryParameter.with("clientAttributeValue", attributeValue1).parameters());

        List<PropertyGroup> propertyGroups = globalCrudService()
                .findByNamedQuery(PropertyGroup.RULE_BASED,
                        QueryParameter.with("clientId", clientId).parameters());
        List<Integer> propertyGroupIds = propertyGroups.stream().map(PropertyGroup::getId).collect(Collectors.toList());

        assertThat(attributeValueCount1, is(0L));
        assertFalse(propertyGroupIds.contains(pg.getId()));
        verify(mockUpsService, never()).deleteCustomAttributeValues(any(ClientAttribute.class));
    }

    @Test
    public void propertyGroupDeletedWhenDeleteAttributeOfIt_FDSEnabled() {
        PacmanConfigParamsService mockPacmanConfigParamsService = mock(PacmanConfigParamsService.class);
        service.setConfigParamsService(mockPacmanConfigParamsService);
        UPSService mockUpsService = mock(UPSService.class);
        service.setUpsService(mockUpsService);

        Integer clientId = PacmanWorkContextHelper.getClientId();
        ClientAttribute attribute1 = UniqueClientAttributeCreator.createUniqueClientAttribute(globalCrudService(), clientId);
        ClientAttributeValue attributeValue1 = UniqueClientAttributeValueCreator.createUniqueClientAttributeValue(globalCrudService(), clientId, attribute1.getId());

        Rule rule1 = UniqueRuleCreator.createRule(globalCrudService(), clientId, Integer.parseInt(PacmanWorkContextHelper.getUserId()));
        UniqueRuleAttributeValueMappingCreator.createRuleAttributeMapping(globalCrudService(), attributeValue1.getId(), rule1.getId());

        PropertyGroup pg = UniquePropertyGroupCreator.createPropertyGroup(globalCrudService(), clientId);
        pg.setRuleId(rule1.getId());
        globalCrudService().save(pg);

        when(mockPacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.FDS_CLIENT_ATTRIBUTES_ENABLED)).thenReturn(true);

        globalCrudService().getEntityManager().flush();
        service.deleteClientAttribute(attribute1);
        //verify database for attribute and its value to confirm deletion.
        long attributeValueCount1 = globalCrudService().findByNamedQuerySingleResult(RuleAttributeValueMapping.COUNT_BY_CLIENT_ATTRIBUTE_VALUE,
                QueryParameter.with("clientAttributeValue", attributeValue1).parameters());

        List<PropertyGroup> propertyGroups = globalCrudService()
                .findByNamedQuery(PropertyGroup.RULE_BASED,
                        QueryParameter.with("clientId", clientId).parameters());
        List<Integer> propertyGroupIds = propertyGroups.stream().map(PropertyGroup::getId).collect(Collectors.toList());

        assertThat(attributeValueCount1, is(0L));
        assertFalse(propertyGroupIds.contains(pg.getId()));
        verify(mockUpsService).deleteCustomAttributeValues(any(ClientAttribute.class));
    }

    @Test
    public void saveClientAttributeValue() {
        CrudService mockGlobalCrudService = mock(CrudService.class);
        service.setGlobalCrudService(mockGlobalCrudService);

        ClientAttributeValue clientAttributeValue = new ClientAttributeValue();
        clientAttributeValue.setClientAttributeValue("test");

        service.saveClientAttributeValue(clientAttributeValue);
        verify(mockGlobalCrudService).save(clientAttributeValue);
    }

    @Test
    public void saveClientAttributePairing() {
        CrudService mockGlobalCrudService = mock(CrudService.class);
        service.setGlobalCrudService(mockGlobalCrudService);

        ClientAttributeValue clientAttributeValue = new ClientAttributeValue();
        clientAttributeValue.setClientAttributeValue("test");
        ClientPropertyAttributePairing clientPropertyAttributePairing = new ClientPropertyAttributePairing();
        clientPropertyAttributePairing.setClientAttributeValue(clientAttributeValue);

        service.saveClientAttributePairing(clientPropertyAttributePairing);
        verify(mockGlobalCrudService).save(clientPropertyAttributePairing);
    }

    @Test
    public void saveCustomAttributePairingsForProperty_ExistingAttributeAndPairingWithSameValue() {
        CrudService mockGlobalCrudService = mock(CrudService.class);
        service.setGlobalCrudService(mockGlobalCrudService);
        Integer propertyId = 5;
        Map<String, String> map = new HashMap();
        //Existing attribute; existing pairing for property has same value (same case)
        map.put("InnCode", "ATLCV");

        Client client = new Client();
        Integer clientId = 1;
        client.setId(clientId);

        Property property = new Property();
        property.setId(5);
        property.setClient(client);
        globalCrudService().save(property);

        propertyCache.setIdeasRedisCacheManager(new IdeasRedisCacheManager());
        propertyCache.put(5, property);
        clientPropertyCacheService.setPropertyCache(propertyCache);

        ClientAttribute clientAttribute1 = new ClientAttribute();
        clientAttribute1.setId(1);
        clientAttribute1.setClientId(clientId);
        clientAttribute1.setClientAttributeName("InnCode");
        clientAttribute1.setAttributeDisplayType(charAttributeDisplayType);

        ClientAttribute clientAttribute6 = new ClientAttribute();
        clientAttribute6.setId(6);
        clientAttribute6.setClientId(clientId);
        clientAttribute6.setClientAttributeName("UnusedAttribute"); //nothing should be set for this
        clientAttribute6.setAttributeDisplayType(charAttributeDisplayType);

        when(mockGlobalCrudService.findByNamedQuery(ClientAttribute.ALL, QueryParameter.with("clientId", clientId).parameters())).thenReturn(Arrays.asList(clientAttribute1, clientAttribute6));

        ClientAttributeValue clientAttributeValue1 = new ClientAttributeValue();
        clientAttributeValue1.setId(1);
        clientAttributeValue1.setClientAttribute(clientAttribute1);
        clientAttributeValue1.setClientAttributeDefault(0);
        clientAttributeValue1.setStatus(Status.ACTIVE);
        clientAttributeValue1.setClientAttributeValue("ATLCV");

        when(mockGlobalCrudService.findByNamedQuery(ClientAttributeValue.BY_CLIENT_ID, QueryParameter.with("clientId", clientId).parameters())).thenReturn(Arrays.asList(clientAttributeValue1));

        ClientPropertyAttributePairing clientPropertyAttributePairing1 = new ClientPropertyAttributePairing();
        clientPropertyAttributePairing1.setId(1);
        clientPropertyAttributePairing1.setPropertyID(propertyId);
        clientPropertyAttributePairing1.setClientAttributeValue(clientAttributeValue1);

        when(mockGlobalCrudService.findByNamedQuery(ClientPropertyAttributePairing.BYPROPERTYID, QueryParameter.with("propertyID", propertyId).parameters())).thenReturn(Arrays.asList(clientPropertyAttributePairing1));

        service.saveCustomAttributePairingsForProperty(propertyId, map, true);
        verify(service, never()).saveClientAttributeValue(any());
        verify(service, never()).saveClientAttributePairing(any());
        verify(service, never()).updateAttributeValueForPairingIds(anyList(), any());
        verify(service, never()).savePropertyClientAttributeValuePairing(anyList(), any());
    }

    @Test
    public void saveCustomAttributePairingsForProperty_ExistingAttributeAndPairingWithSameValue_Truncated() {
        CrudService mockGlobalCrudService = mock(CrudService.class);
        service.setGlobalCrudService(mockGlobalCrudService);
        Integer propertyId = 5;
        Map<String, String> map = new HashMap();
        //Existing attribute; existing pairing for property has same value (same case) but the saved value has been truncated to 150
        map.put("InnCode", "1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901");

        Client client = new Client();
        Integer clientId = 1;
        client.setId(clientId);

        Property property = new Property();
        property.setId(5);
        property.setClient(client);
        globalCrudService().save(property);

        propertyCache.setIdeasRedisCacheManager(new IdeasRedisCacheManager());
        propertyCache.put(5, property);
        clientPropertyCacheService.setPropertyCache(propertyCache);

        ClientAttribute clientAttribute1 = new ClientAttribute();
        clientAttribute1.setId(1);
        clientAttribute1.setClientId(clientId);
        clientAttribute1.setClientAttributeName("InnCode");
        clientAttribute1.setAttributeDisplayType(charAttributeDisplayType);

        ClientAttribute clientAttribute6 = new ClientAttribute();
        clientAttribute6.setId(6);
        clientAttribute6.setClientId(clientId);
        clientAttribute6.setClientAttributeName("UnusedAttribute"); //nothing should be set for this
        clientAttribute6.setAttributeDisplayType(charAttributeDisplayType);

        when(mockGlobalCrudService.findByNamedQuery(ClientAttribute.ALL, QueryParameter.with("clientId", clientId).parameters())).thenReturn(Arrays.asList(clientAttribute1, clientAttribute6));

        ClientAttributeValue clientAttributeValue1 = new ClientAttributeValue();
        clientAttributeValue1.setId(1);
        clientAttributeValue1.setClientAttribute(clientAttribute1);
        clientAttributeValue1.setClientAttributeDefault(0);
        clientAttributeValue1.setStatus(Status.ACTIVE);
        clientAttributeValue1.setClientAttributeValue("123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890");

        when(mockGlobalCrudService.findByNamedQuery(ClientAttributeValue.BY_CLIENT_ID, QueryParameter.with("clientId", clientId).parameters())).thenReturn(Arrays.asList(clientAttributeValue1));

        ClientPropertyAttributePairing clientPropertyAttributePairing1 = new ClientPropertyAttributePairing();
        clientPropertyAttributePairing1.setId(1);
        clientPropertyAttributePairing1.setPropertyID(propertyId);
        clientPropertyAttributePairing1.setClientAttributeValue(clientAttributeValue1);

        when(mockGlobalCrudService.findByNamedQuery(ClientPropertyAttributePairing.BYPROPERTYID, QueryParameter.with("propertyID", propertyId).parameters())).thenReturn(Arrays.asList(clientPropertyAttributePairing1));

        service.saveCustomAttributePairingsForProperty(propertyId, map, true);
        verify(service, never()).saveClientAttributeValue(any());
        verify(service, never()).saveClientAttributePairing(any());
        verify(service, never()).updateAttributeValueForPairingIds(anyList(), any());
        verify(service, never()).savePropertyClientAttributeValuePairing(anyList(), any());
    }

    @Test
    public void saveCustomAttributePairingsForProperty_ExistingPairingWithNewValue() {
        CrudService mockGlobalCrudService = mock(CrudService.class);
        service.setGlobalCrudService(mockGlobalCrudService);
        Integer propertyId = 5;
        Map<String, String> map = new HashMap();
        //Existing attribute, update existing pairing for property to new value
        map.put("UseName", "Hampton Inn Covington");

        Client client = new Client();
        Integer clientId = 1;
        client.setId(clientId);

        Property property = new Property();
        property.setId(5);
        property.setClient(client);
        globalCrudService().save(property);

        propertyCache.setIdeasRedisCacheManager(new IdeasRedisCacheManager());
        propertyCache.put(5, property);
        clientPropertyCacheService.setPropertyCache(propertyCache);

        ClientAttribute clientAttribute2 = new ClientAttribute();
        clientAttribute2.setId(2);
        clientAttribute2.setClientId(clientId);
        clientAttribute2.setClientAttributeName("UseName");
        clientAttribute2.setAttributeDisplayType(charAttributeDisplayType);

        when(mockGlobalCrudService.findByNamedQuery(ClientAttribute.ALL, QueryParameter.with("clientId", clientId).parameters())).thenReturn(Arrays.asList(clientAttribute2));

        ClientAttributeValue clientAttributeValue2existing = new ClientAttributeValue();
        clientAttributeValue2existing.setId(2);
        clientAttributeValue2existing.setClientAttribute(clientAttribute2);
        clientAttributeValue2existing.setClientAttributeDefault(0);
        clientAttributeValue2existing.setStatus(Status.ACTIVE);
        clientAttributeValue2existing.setClientAttributeValue("OLD VALUE THAT WILL BE UPDATED");

        ClientAttributeValue clientAttributeValue2new = new ClientAttributeValue();
        clientAttributeValue2new.setId(2);
        clientAttributeValue2new.setClientAttribute(clientAttribute2);
        clientAttributeValue2new.setClientAttributeDefault(0);
        clientAttributeValue2new.setStatus(Status.ACTIVE);
        clientAttributeValue2new.setClientAttributeValue("Hampton Inn Covington");

        when(mockGlobalCrudService.findByNamedQuery(ClientAttributeValue.BY_CLIENT_ID, QueryParameter.with("clientId", clientId).parameters())).thenReturn(Arrays.asList(clientAttributeValue2existing));
        when(mockGlobalCrudService.save(any(ClientAttributeValue.class))).thenReturn(clientAttributeValue2new);

        ClientPropertyAttributePairing clientPropertyAttributePairing2 = new ClientPropertyAttributePairing();
        clientPropertyAttributePairing2.setId(2);
        clientPropertyAttributePairing2.setPropertyID(propertyId);
        clientPropertyAttributePairing2.setClientAttributeValue(clientAttributeValue2existing);

        when(mockGlobalCrudService.findByNamedQuery(ClientPropertyAttributePairing.BYPROPERTYID, QueryParameter.with("propertyID", propertyId).parameters())).thenReturn(Arrays.asList(clientPropertyAttributePairing2));

        service.saveCustomAttributePairingsForProperty(propertyId, map, true);
        verify(service, never()).saveClientAttributePairing(any());
        verify(service, never()).savePropertyClientAttributeValuePairing(anyList(), any());

        ArgumentCaptor<ClientAttributeValue> clientAttributeValueParameterCaptor = ArgumentCaptor.forClass(ClientAttributeValue.class);
        verify(service).saveClientAttributeValue(clientAttributeValueParameterCaptor.capture());
        assertEquals(clientAttribute2, clientAttributeValueParameterCaptor.getValue().getClientAttribute());
        assertEquals("Hampton Inn Covington", clientAttributeValueParameterCaptor.getValue().getClientAttributeValue());
        assertEquals(0, clientAttributeValueParameterCaptor.getValue().getClientAttributeDefault());
        assertEquals(Status.ACTIVE, clientAttributeValueParameterCaptor.getValue().getStatus());

        ArgumentCaptor<List<Integer>> clientPropertyAttributePairingIdParameterCapture = ArgumentCaptor.forClass(ArrayList.class);
        ArgumentCaptor<Integer> clientAttributeValueIdParameterCaptor1 = ArgumentCaptor.forClass(Integer.class);
        verify(service).updateAttributeValueForPairingIds(clientPropertyAttributePairingIdParameterCapture.capture(), clientAttributeValueIdParameterCaptor1.capture());
        assertEquals(1, clientPropertyAttributePairingIdParameterCapture.getValue().size());
        assertEquals(clientPropertyAttributePairing2.getId(), clientPropertyAttributePairingIdParameterCapture.getValue().get(0));
        assertEquals(clientAttributeValue2new.getId(), clientAttributeValueIdParameterCaptor1.getValue());
    }

    @Test
    public void saveCustomAttributePairingsForProperty_ExistingPairingWithNewValue_Truncated() {
        CrudService mockGlobalCrudService = mock(CrudService.class);
        service.setGlobalCrudService(mockGlobalCrudService);
        Integer propertyId = 5;
        Map<String, String> map = new HashMap();
        //Existing attribute, update existing pairing for property to new value
        map.put("UseName", "1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901");

        Client client = new Client();
        Integer clientId = 1;
        client.setId(clientId);

        Property property = new Property();
        property.setId(5);
        property.setClient(client);
        globalCrudService().save(property);

        propertyCache.setIdeasRedisCacheManager(new IdeasRedisCacheManager());
        propertyCache.put(5, property);
        clientPropertyCacheService.setPropertyCache(propertyCache);

        ClientAttribute clientAttribute2 = new ClientAttribute();
        clientAttribute2.setId(2);
        clientAttribute2.setClientId(clientId);
        clientAttribute2.setClientAttributeName("UseName");
        clientAttribute2.setAttributeDisplayType(charAttributeDisplayType);

        when(mockGlobalCrudService.findByNamedQuery(ClientAttribute.ALL, QueryParameter.with("clientId", clientId).parameters())).thenReturn(Arrays.asList(clientAttribute2));

        ClientAttributeValue clientAttributeValue2existing = new ClientAttributeValue();
        clientAttributeValue2existing.setId(2);
        clientAttributeValue2existing.setClientAttribute(clientAttribute2);
        clientAttributeValue2existing.setClientAttributeDefault(0);
        clientAttributeValue2existing.setStatus(Status.ACTIVE);
        clientAttributeValue2existing.setClientAttributeValue("OLD VALUE THAT WILL BE UPDATED");

        ClientAttributeValue clientAttributeValue2new = new ClientAttributeValue();
        clientAttributeValue2new.setId(2);
        clientAttributeValue2new.setClientAttribute(clientAttribute2);
        clientAttributeValue2new.setClientAttributeDefault(0);
        clientAttributeValue2new.setStatus(Status.ACTIVE);
        clientAttributeValue2new.setClientAttributeValue("123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890");

        when(mockGlobalCrudService.findByNamedQuery(ClientAttributeValue.BY_CLIENT_ID, QueryParameter.with("clientId", clientId).parameters())).thenReturn(Arrays.asList(clientAttributeValue2existing));
        when(mockGlobalCrudService.save(any(ClientAttributeValue.class))).thenReturn(clientAttributeValue2new);

        ClientPropertyAttributePairing clientPropertyAttributePairing2 = new ClientPropertyAttributePairing();
        clientPropertyAttributePairing2.setId(2);
        clientPropertyAttributePairing2.setPropertyID(propertyId);
        clientPropertyAttributePairing2.setClientAttributeValue(clientAttributeValue2existing);

        when(mockGlobalCrudService.findByNamedQuery(ClientPropertyAttributePairing.BYPROPERTYID, QueryParameter.with("propertyID", propertyId).parameters())).thenReturn(Arrays.asList(clientPropertyAttributePairing2));

        service.saveCustomAttributePairingsForProperty(propertyId, map, true);
        verify(service, never()).saveClientAttributePairing(any());
        verify(service, never()).savePropertyClientAttributeValuePairing(anyList(), any());

        ArgumentCaptor<ClientAttributeValue> clientAttributeValueParameterCaptor = ArgumentCaptor.forClass(ClientAttributeValue.class);
        verify(service).saveClientAttributeValue(clientAttributeValueParameterCaptor.capture());
        assertEquals(clientAttribute2, clientAttributeValueParameterCaptor.getValue().getClientAttribute());
        assertEquals("123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890", clientAttributeValueParameterCaptor.getValue().getClientAttributeValue());
        assertEquals(0, clientAttributeValueParameterCaptor.getValue().getClientAttributeDefault());
        assertEquals(Status.ACTIVE, clientAttributeValueParameterCaptor.getValue().getStatus());

        ArgumentCaptor<List<Integer>> clientPropertyAttributePairingIdParameterCapture = ArgumentCaptor.forClass(ArrayList.class);
        ArgumentCaptor<Integer> clientAttributeValueIdParameterCaptor1 = ArgumentCaptor.forClass(Integer.class);
        verify(service).updateAttributeValueForPairingIds(clientPropertyAttributePairingIdParameterCapture.capture(), clientAttributeValueIdParameterCaptor1.capture());
        assertEquals(1, clientPropertyAttributePairingIdParameterCapture.getValue().size());
        assertEquals(clientPropertyAttributePairing2.getId(), clientPropertyAttributePairingIdParameterCapture.getValue().get(0));
        assertEquals(clientAttributeValue2new.getId(), clientAttributeValueIdParameterCaptor1.getValue());
    }

    @Test
    public void saveCustomAttributePairingsForProperty_ExistingValueAndNewPairing() {
        CrudService mockGlobalCrudService = mock(CrudService.class);
        service.setGlobalCrudService(mockGlobalCrudService);
        Integer propertyId = 5;
        Map<String, String> map = new HashMap();
        //Existing attribute, existing value and new pairing for property
        map.put("Brand", "Hampton Inn by Hilton");

        Client client = new Client();
        Integer clientId = 1;
        client.setId(clientId);

        Property property = new Property();
        property.setId(5);
        property.setClient(client);
        globalCrudService().save(property);

        propertyCache.setIdeasRedisCacheManager(new IdeasRedisCacheManager());
        propertyCache.put(5, property);
        clientPropertyCacheService.setPropertyCache(propertyCache);

        ClientAttribute clientAttribute3 = new ClientAttribute();
        clientAttribute3.setId(3);
        clientAttribute3.setClientId(clientId);
        clientAttribute3.setClientAttributeName("Brand");
        clientAttribute3.setAttributeDisplayType(charAttributeDisplayType);

        when(mockGlobalCrudService.findByNamedQuery(ClientAttribute.ALL, QueryParameter.with("clientId", clientId).parameters())).thenReturn(Arrays.asList(clientAttribute3));

        ClientAttributeValue clientAttributeValue3 = new ClientAttributeValue();
        clientAttributeValue3.setId(3);
        clientAttributeValue3.setClientAttribute(clientAttribute3);
        clientAttributeValue3.setClientAttributeDefault(0);
        clientAttributeValue3.setStatus(Status.ACTIVE);
        clientAttributeValue3.setClientAttributeValue("Hampton Inn by Hilton");
        when(mockGlobalCrudService.find(ClientAttributeValue.class, 3)).thenReturn(clientAttributeValue3);

        when(mockGlobalCrudService.findByNamedQuery(ClientAttributeValue.BY_CLIENT_ID, QueryParameter.with("clientId", clientId).parameters())).thenReturn(Arrays.asList(clientAttributeValue3));

        when(mockGlobalCrudService.findByNamedQuery(ClientPropertyAttributePairing.BYPROPERTYID, QueryParameter.with("propertyID", propertyId).parameters())).thenReturn(new ArrayList<>());

        service.saveCustomAttributePairingsForProperty(propertyId, map, true);

        verify(service, never()).saveClientAttributeValue(any());
        verify(service, never()).saveClientAttributePairing(any());
        verify(service, never()).updateAttributeValueForPairingIds(anyList(), any());

        ArgumentCaptor<List<Integer>> propertyIdListParameterCapture2 = ArgumentCaptor.forClass(ArrayList.class);
        ArgumentCaptor<ClientAttributeValue> clientAttributeValueParameterCaptor2 = ArgumentCaptor.forClass(ClientAttributeValue.class);
        verify(service).savePropertyClientAttributeValuePairing(propertyIdListParameterCapture2.capture(), clientAttributeValueParameterCaptor2.capture());
        assertEquals(1, propertyIdListParameterCapture2.getValue().size());
        assertEquals(propertyId, propertyIdListParameterCapture2.getValue().get(0));
        assertEquals(clientAttributeValue3.getClientAttributeValue(), clientAttributeValueParameterCaptor2.getValue().getClientAttributeValue());
        assertEquals(Status.ACTIVE, clientAttributeValueParameterCaptor2.getValue().getStatus());
    }

    @Test
    public void saveCustomAttributePairingsForProperty_ExistingValueAndNewPairing_Truncated() {
        CrudService mockGlobalCrudService = mock(CrudService.class);
        service.setGlobalCrudService(mockGlobalCrudService);
        Integer propertyId = 5;
        Map<String, String> map = new HashMap();
        //Existing attribute, existing value and new pairing for property
        map.put("Brand", "1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901");

        Client client = new Client();
        Integer clientId = 1;
        client.setId(clientId);

        Property property = new Property();
        property.setId(5);
        property.setClient(client);
        globalCrudService().save(property);

        propertyCache.setIdeasRedisCacheManager(new IdeasRedisCacheManager());
        propertyCache.put(5, property);
        clientPropertyCacheService.setPropertyCache(propertyCache);

        ClientAttribute clientAttribute3 = new ClientAttribute();
        clientAttribute3.setId(3);
        clientAttribute3.setClientId(clientId);
        clientAttribute3.setClientAttributeName("Brand");
        clientAttribute3.setAttributeDisplayType(charAttributeDisplayType);

        when(mockGlobalCrudService.findByNamedQuery(ClientAttribute.ALL, QueryParameter.with("clientId", clientId).parameters())).thenReturn(Arrays.asList(clientAttribute3));

        ClientAttributeValue clientAttributeValue3 = new ClientAttributeValue();
        clientAttributeValue3.setId(3);
        clientAttributeValue3.setClientAttribute(clientAttribute3);
        clientAttributeValue3.setClientAttributeDefault(0);
        clientAttributeValue3.setStatus(Status.ACTIVE);
        clientAttributeValue3.setClientAttributeValue("123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890");
        when(mockGlobalCrudService.find(ClientAttributeValue.class, 3)).thenReturn(clientAttributeValue3);

        when(mockGlobalCrudService.findByNamedQuery(ClientAttributeValue.BY_CLIENT_ID, QueryParameter.with("clientId", clientId).parameters())).thenReturn(Arrays.asList(clientAttributeValue3));

        when(mockGlobalCrudService.findByNamedQuery(ClientPropertyAttributePairing.BYPROPERTYID, QueryParameter.with("propertyID", propertyId).parameters())).thenReturn(new ArrayList<>());

        service.saveCustomAttributePairingsForProperty(propertyId, map, true);

        verify(service, never()).saveClientAttributeValue(any());
        verify(service, never()).saveClientAttributePairing(any());
        verify(service, never()).updateAttributeValueForPairingIds(anyList(), any());

        ArgumentCaptor<List<Integer>> propertyIdListParameterCapture2 = ArgumentCaptor.forClass(ArrayList.class);
        ArgumentCaptor<ClientAttributeValue> clientAttributeValueParameterCaptor2 = ArgumentCaptor.forClass(ClientAttributeValue.class);
        verify(service).savePropertyClientAttributeValuePairing(propertyIdListParameterCapture2.capture(), clientAttributeValueParameterCaptor2.capture());
        assertEquals(1, propertyIdListParameterCapture2.getValue().size());
        assertEquals(propertyId, propertyIdListParameterCapture2.getValue().get(0));
        assertEquals(clientAttributeValue3.getClientAttributeValue(), clientAttributeValueParameterCaptor2.getValue().getClientAttributeValue());
        assertEquals(Status.ACTIVE, clientAttributeValueParameterCaptor2.getValue().getStatus());
    }

    @Test
    public void saveCustomAttributePairingsForProperty_NewValueAndNewPairing() {
        CrudService mockGlobalCrudService = mock(CrudService.class);
        service.setGlobalCrudService(mockGlobalCrudService);
        Integer propertyId = 5;
        Map<String, String> map = new HashMap();
        //Existing attribute, new value and pairing for property
        map.put("Brand Code", "HX");

        Client client = new Client();
        Integer clientId = 1;
        client.setId(clientId);

        Property property = new Property();
        property.setId(5);
        property.setClient(client);
        globalCrudService().save(property);

        propertyCache.setIdeasRedisCacheManager(new IdeasRedisCacheManager());
        propertyCache.put(5, property);
        clientPropertyCacheService.setPropertyCache(propertyCache);

        ClientAttribute clientAttribute4 = new ClientAttribute();
        clientAttribute4.setId(4);
        clientAttribute4.setClientId(clientId);
        clientAttribute4.setClientAttributeName("Brand Code");
        clientAttribute4.setAttributeDisplayType(charAttributeDisplayType);

        when(mockGlobalCrudService.findByNamedQuery(ClientAttribute.ALL, QueryParameter.with("clientId", clientId).parameters())).thenReturn(Arrays.asList(clientAttribute4));

        when(mockGlobalCrudService.findByNamedQuery(ClientAttributeValue.BY_CLIENT_ID, QueryParameter.with("clientId", clientId).parameters())).thenReturn(new ArrayList<>());

        ClientAttributeValue savedClientAttributeValue = new ClientAttributeValue();
        savedClientAttributeValue.setId(10);
        savedClientAttributeValue.setClientAttribute(clientAttribute4);
        savedClientAttributeValue.setClientAttributeValue("HX");
        savedClientAttributeValue.setClientAttributeDefault(0);
        savedClientAttributeValue.setStatus(Status.ACTIVE);
        when(mockGlobalCrudService.save(any(ClientAttributeValue.class))).thenReturn(savedClientAttributeValue);
        when(mockGlobalCrudService.find(ClientAttributeValue.class, 10)).thenReturn(savedClientAttributeValue);

        when(mockGlobalCrudService.findByNamedQuery(ClientPropertyAttributePairing.BYPROPERTYID, QueryParameter.with("propertyID", propertyId).parameters())).thenReturn(new ArrayList<>());

        service.saveCustomAttributePairingsForProperty(propertyId, map, true);

        verify(mockGlobalCrudService, never()).executeUpdateByNamedQuery(ClientPropertyAttributePairing.UPDATE_PAIRING_ATTRIBUTE_VALUE_BY_ID,
                QueryParameter.with("clientAttributeValueId", anyInt()).and("id", anyList()).parameters());

        verify(mockGlobalCrudService).save(any(ClientAttributeValue.class));
        ArgumentCaptor<ClientAttributeValue> clientAttributeValueParameterCaptor = ArgumentCaptor.forClass(ClientAttributeValue.class);
        verify(service).saveClientAttributeValue(clientAttributeValueParameterCaptor.capture());
        assertEquals(clientAttribute4, clientAttributeValueParameterCaptor.getValue().getClientAttribute());
        assertEquals("HX", clientAttributeValueParameterCaptor.getValue().getClientAttributeValue());
        assertEquals(0, clientAttributeValueParameterCaptor.getValue().getClientAttributeDefault());
        assertEquals(Status.ACTIVE, clientAttributeValueParameterCaptor.getValue().getStatus());

        verify(mockGlobalCrudService).save(any(ClientPropertyAttributePairing.class));
        ArgumentCaptor<List<Integer>> propertyIdListParameterCapture = ArgumentCaptor.forClass(ArrayList.class);
        ArgumentCaptor<ClientAttributeValue> clientAttributeValueParameterCaptor2 = ArgumentCaptor.forClass(ClientAttributeValue.class);
        verify(service).savePropertyClientAttributeValuePairing(propertyIdListParameterCapture.capture(), clientAttributeValueParameterCaptor2.capture());
        assertEquals(1, propertyIdListParameterCapture.getValue().size());
        assertEquals(propertyId, propertyIdListParameterCapture.getValue().get(0));
        assertEquals("HX", clientAttributeValueParameterCaptor2.getValue().getClientAttributeValue());
        assertEquals(Status.ACTIVE, clientAttributeValueParameterCaptor2.getValue().getStatus());
    }

    @Test
    public void saveCustomAttributePairingsForProperty_NewValueAndNewPairing_Truncated() {
        CrudService mockGlobalCrudService = mock(CrudService.class);
        service.setGlobalCrudService(mockGlobalCrudService);
        Integer propertyId = 5;
        Map<String, String> map = new HashMap();
        //Existing attribute, new value and pairing for property
        map.put("Brand Code", "1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901");

        Client client = new Client();
        Integer clientId = 1;
        client.setId(clientId);

        Property property = new Property();
        property.setId(5);
        property.setClient(client);
        globalCrudService().save(property);

        propertyCache.setIdeasRedisCacheManager(new IdeasRedisCacheManager());
        propertyCache.put(5, property);
        clientPropertyCacheService.setPropertyCache(propertyCache);

        ClientAttribute clientAttribute4 = new ClientAttribute();
        clientAttribute4.setId(4);
        clientAttribute4.setClientId(clientId);
        clientAttribute4.setClientAttributeName("Brand Code");
        clientAttribute4.setAttributeDisplayType(charAttributeDisplayType);

        when(mockGlobalCrudService.findByNamedQuery(ClientAttribute.ALL, QueryParameter.with("clientId", clientId).parameters())).thenReturn(Arrays.asList(clientAttribute4));

        when(mockGlobalCrudService.findByNamedQuery(ClientAttributeValue.BY_CLIENT_ID, QueryParameter.with("clientId", clientId).parameters())).thenReturn(new ArrayList<>());

        ClientAttributeValue savedClientAttributeValue = new ClientAttributeValue();
        savedClientAttributeValue.setId(10);
        savedClientAttributeValue.setClientAttribute(clientAttribute4);
        savedClientAttributeValue.setClientAttributeValue("123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890");
        savedClientAttributeValue.setClientAttributeDefault(0);
        savedClientAttributeValue.setStatus(Status.ACTIVE);
        when(mockGlobalCrudService.save(any(ClientAttributeValue.class))).thenReturn(savedClientAttributeValue);
        when(mockGlobalCrudService.find(ClientAttributeValue.class, 10)).thenReturn(savedClientAttributeValue);

        when(mockGlobalCrudService.findByNamedQuery(ClientPropertyAttributePairing.BYPROPERTYID, QueryParameter.with("propertyID", propertyId).parameters())).thenReturn(new ArrayList<>());

        service.saveCustomAttributePairingsForProperty(propertyId, map, true);

        verify(mockGlobalCrudService, never()).executeUpdateByNamedQuery(ClientPropertyAttributePairing.UPDATE_PAIRING_ATTRIBUTE_VALUE_BY_ID,
                QueryParameter.with("clientAttributeValueId", anyInt()).and("id", anyList()).parameters());

        verify(mockGlobalCrudService).save(any(ClientAttributeValue.class));
        ArgumentCaptor<ClientAttributeValue> clientAttributeValueParameterCaptor = ArgumentCaptor.forClass(ClientAttributeValue.class);
        verify(service).saveClientAttributeValue(clientAttributeValueParameterCaptor.capture());
        assertEquals(clientAttribute4, clientAttributeValueParameterCaptor.getValue().getClientAttribute());
        assertEquals("123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890", clientAttributeValueParameterCaptor.getValue().getClientAttributeValue());
        assertEquals(0, clientAttributeValueParameterCaptor.getValue().getClientAttributeDefault());
        assertEquals(Status.ACTIVE, clientAttributeValueParameterCaptor.getValue().getStatus());

        verify(mockGlobalCrudService).save(any(ClientPropertyAttributePairing.class));
        ArgumentCaptor<List<Integer>> propertyIdListParameterCapture = ArgumentCaptor.forClass(ArrayList.class);
        ArgumentCaptor<ClientAttributeValue> clientAttributeValueParameterCaptor2 = ArgumentCaptor.forClass(ClientAttributeValue.class);
        verify(service).savePropertyClientAttributeValuePairing(propertyIdListParameterCapture.capture(), clientAttributeValueParameterCaptor2.capture());
        assertEquals(1, propertyIdListParameterCapture.getValue().size());
        assertEquals(propertyId, propertyIdListParameterCapture.getValue().get(0));
        assertEquals("123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890", clientAttributeValueParameterCaptor2.getValue().getClientAttributeValue());
        assertEquals(Status.ACTIVE, clientAttributeValueParameterCaptor2.getValue().getStatus());
    }

    @Test
    public void saveCustomAttributePairingsForProperty_ExistingPairingWithSameValueButDifferentCase() {
        CrudService mockGlobalCrudService = mock(CrudService.class);
        service.setGlobalCrudService(mockGlobalCrudService);
        Integer propertyId = 5;
        Map<String, String> map = new HashMap();
        //Existing attribute; existing pairing for property has same value (different case)
        map.put("CurrencyCode", "USD");

        Client client = new Client();
        Integer clientId = 1;
        client.setId(clientId);

        Property property = new Property();
        property.setId(5);
        property.setClient(client);
        globalCrudService().save(property);

        propertyCache.setIdeasRedisCacheManager(new IdeasRedisCacheManager());
        propertyCache.put(5, property);
        clientPropertyCacheService.setPropertyCache(propertyCache);

        ClientAttribute clientAttribute5 = new ClientAttribute();
        clientAttribute5.setId(5);
        clientAttribute5.setClientId(clientId);
        clientAttribute5.setClientAttributeName("CurrencyCode");
        clientAttribute5.setAttributeDisplayType(charAttributeDisplayType);

        when(mockGlobalCrudService.findByNamedQuery(ClientAttribute.ALL, QueryParameter.with("clientId", clientId).parameters())).thenReturn(Arrays.asList(clientAttribute5));

        ClientAttributeValue clientAttributeValue5 = new ClientAttributeValue();
        clientAttributeValue5.setId(5);
        clientAttributeValue5.setClientAttribute(clientAttribute5);
        clientAttributeValue5.setClientAttributeDefault(0);
        clientAttributeValue5.setStatus(Status.ACTIVE);
        clientAttributeValue5.setClientAttributeValue("usd");

        when(mockGlobalCrudService.findByNamedQuery(ClientAttributeValue.BY_CLIENT_ID, QueryParameter.with("clientId", clientId).parameters())).thenReturn(Arrays.asList(clientAttributeValue5));

        ClientPropertyAttributePairing clientPropertyAttributePairing5 = new ClientPropertyAttributePairing();
        clientPropertyAttributePairing5.setId(5);
        clientPropertyAttributePairing5.setPropertyID(propertyId);
        clientPropertyAttributePairing5.setClientAttributeValue(clientAttributeValue5);

        when(mockGlobalCrudService.findByNamedQuery(ClientPropertyAttributePairing.BYPROPERTYID, QueryParameter.with("propertyID", propertyId).parameters())).thenReturn(Arrays.asList(clientPropertyAttributePairing5));

        service.saveCustomAttributePairingsForProperty(propertyId, map, true);
        verify(service, never()).saveClientAttributeValue(any());
        verify(service, never()).saveClientAttributePairing(any());
        verify(service, never()).updateAttributeValueForPairingIds(anyList(), any());
        verify(service, never()).savePropertyClientAttributeValuePairing(anyList(), any());
    }

    @Test
    public void saveCustomAttributePairingsForProperty_ExistingPairingWithSameValueButDifferentCase_Truncated() {
        CrudService mockGlobalCrudService = mock(CrudService.class);
        service.setGlobalCrudService(mockGlobalCrudService);
        Integer propertyId = 5;
        Map<String, String> map = new HashMap();
        //Existing attribute; existing pairing for property has same value (different case)
        map.put("CurrencyCode", "12345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789A1");

        Client client = new Client();
        Integer clientId = 1;
        client.setId(clientId);

        Property property = new Property();
        property.setId(5);
        property.setClient(client);
        globalCrudService().save(property);

        propertyCache.setIdeasRedisCacheManager(new IdeasRedisCacheManager());
        propertyCache.put(5, property);
        clientPropertyCacheService.setPropertyCache(propertyCache);

        ClientAttribute clientAttribute5 = new ClientAttribute();
        clientAttribute5.setId(5);
        clientAttribute5.setClientId(clientId);
        clientAttribute5.setClientAttributeName("CurrencyCode");
        clientAttribute5.setAttributeDisplayType(charAttributeDisplayType);

        when(mockGlobalCrudService.findByNamedQuery(ClientAttribute.ALL, QueryParameter.with("clientId", clientId).parameters())).thenReturn(Arrays.asList(clientAttribute5));

        ClientAttributeValue clientAttributeValue5 = new ClientAttributeValue();
        clientAttributeValue5.setId(5);
        clientAttributeValue5.setClientAttribute(clientAttribute5);
        clientAttributeValue5.setClientAttributeDefault(0);
        clientAttributeValue5.setStatus(Status.ACTIVE);
        clientAttributeValue5.setClientAttributeValue("12345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789a");

        when(mockGlobalCrudService.findByNamedQuery(ClientAttributeValue.BY_CLIENT_ID, QueryParameter.with("clientId", clientId).parameters())).thenReturn(Arrays.asList(clientAttributeValue5));

        ClientPropertyAttributePairing clientPropertyAttributePairing5 = new ClientPropertyAttributePairing();
        clientPropertyAttributePairing5.setId(5);
        clientPropertyAttributePairing5.setPropertyID(propertyId);
        clientPropertyAttributePairing5.setClientAttributeValue(clientAttributeValue5);

        when(mockGlobalCrudService.findByNamedQuery(ClientPropertyAttributePairing.BYPROPERTYID, QueryParameter.with("propertyID", propertyId).parameters())).thenReturn(Arrays.asList(clientPropertyAttributePairing5));

        service.saveCustomAttributePairingsForProperty(propertyId, map, true);
        verify(service, never()).saveClientAttributeValue(any());
        verify(service, never()).saveClientAttributePairing(any());
        verify(service, never()).updateAttributeValueForPairingIds(anyList(), any());
        verify(service, never()).savePropertyClientAttributeValuePairing(anyList(), any());
    }

    @Test
    public void saveCustomAttributePairingsForProperty_ExistingPairingWithDifferentExistingValue() {
        CrudService mockGlobalCrudService = mock(CrudService.class);
        service.setGlobalCrudService(mockGlobalCrudService);
        Integer propertyId = 5;
        Map<String, String> map = new HashMap();
        //Existing attribute, update existing pairing for property to new value
        map.put("UseName", "Hampton Inn Covington");

        Client client = new Client();
        Integer clientId = 1;
        client.setId(clientId);

        Property property = new Property();
        property.setId(5);
        property.setClient(client);
        globalCrudService().save(property);

        propertyCache.setIdeasRedisCacheManager(new IdeasRedisCacheManager());
        propertyCache.put(5, property);
        clientPropertyCacheService.setPropertyCache(propertyCache);

        ClientAttribute clientAttribute2 = new ClientAttribute();
        clientAttribute2.setId(2);
        clientAttribute2.setClientId(clientId);
        clientAttribute2.setClientAttributeName("UseName");
        clientAttribute2.setAttributeDisplayType(charAttributeDisplayType);

        when(mockGlobalCrudService.findByNamedQuery(ClientAttribute.ALL, QueryParameter.with("clientId", clientId).parameters())).thenReturn(Arrays.asList(clientAttribute2));

        ClientAttributeValue clientAttributeValue2 = new ClientAttributeValue();
        clientAttributeValue2.setId(2);
        clientAttributeValue2.setClientAttribute(clientAttribute2);
        clientAttributeValue2.setClientAttributeDefault(0);
        clientAttributeValue2.setStatus(Status.ACTIVE);
        clientAttributeValue2.setClientAttributeValue("OLD VALUE THAT WILL BE UPDATED");

        ClientAttributeValue clientAttributeValue3 = new ClientAttributeValue();
        clientAttributeValue3.setId(2);
        clientAttributeValue3.setClientAttribute(clientAttribute2);
        clientAttributeValue3.setClientAttributeDefault(0);
        clientAttributeValue3.setStatus(Status.ACTIVE);
        clientAttributeValue3.setClientAttributeValue("Hampton Inn Covington");

        when(mockGlobalCrudService.findByNamedQuery(ClientAttributeValue.BY_CLIENT_ID, QueryParameter.with("clientId", clientId).parameters())).thenReturn(Arrays.asList(clientAttributeValue2, clientAttributeValue3));

        ClientPropertyAttributePairing clientPropertyAttributePairing2 = new ClientPropertyAttributePairing();
        clientPropertyAttributePairing2.setId(2);
        clientPropertyAttributePairing2.setPropertyID(propertyId);
        clientPropertyAttributePairing2.setClientAttributeValue(clientAttributeValue2);

        when(mockGlobalCrudService.findByNamedQuery(ClientPropertyAttributePairing.BYPROPERTYID, QueryParameter.with("propertyID", propertyId).parameters())).thenReturn(Arrays.asList(clientPropertyAttributePairing2));

        service.saveCustomAttributePairingsForProperty(propertyId, map, true);
        verify(service, never()).saveClientAttributeValue(any());
        verify(service, never()).updateAttributeValueForPairingIds(anyList(), any());
        verify(service, never()).savePropertyClientAttributeValuePairing(anyList(), any());

        ArgumentCaptor<ClientPropertyAttributePairing> clientPropertyAttributePairingParameterCaptor = ArgumentCaptor.forClass(ClientPropertyAttributePairing.class);
        verify(service).saveClientAttributePairing(clientPropertyAttributePairingParameterCaptor.capture());
        assertEquals(propertyId, clientPropertyAttributePairingParameterCaptor.getValue().getPropertyID());
        assertEquals("Hampton Inn Covington", clientPropertyAttributePairingParameterCaptor.getValue().getClientAttributeValue().getClientAttributeValue());
        assertEquals(Status.ACTIVE, clientPropertyAttributePairingParameterCaptor.getValue().getStatus());
    }

    @Test
    public void saveCustomAttributePairingsForProperty_ExistingPairingWithDifferentExistingValue_Truncated() {
        CrudService mockGlobalCrudService = mock(CrudService.class);
        service.setGlobalCrudService(mockGlobalCrudService);
        Integer propertyId = 5;
        Map<String, String> map = new HashMap();
        //Existing attribute, update existing pairing for property to new value
        map.put("UseName", "1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901");

        Client client = new Client();
        Integer clientId = 1;
        client.setId(clientId);

        Property property = new Property();
        property.setId(5);
        property.setClient(client);
        globalCrudService().save(property);

        propertyCache.setIdeasRedisCacheManager(new IdeasRedisCacheManager());
        propertyCache.put(5, property);
        clientPropertyCacheService.setPropertyCache(propertyCache);

        ClientAttribute clientAttribute2 = new ClientAttribute();
        clientAttribute2.setId(2);
        clientAttribute2.setClientId(clientId);
        clientAttribute2.setClientAttributeName("UseName");
        clientAttribute2.setAttributeDisplayType(charAttributeDisplayType);

        when(mockGlobalCrudService.findByNamedQuery(ClientAttribute.ALL, QueryParameter.with("clientId", clientId).parameters())).thenReturn(Arrays.asList(clientAttribute2));

        ClientAttributeValue clientAttributeValue2 = new ClientAttributeValue();
        clientAttributeValue2.setId(2);
        clientAttributeValue2.setClientAttribute(clientAttribute2);
        clientAttributeValue2.setClientAttributeDefault(0);
        clientAttributeValue2.setStatus(Status.ACTIVE);
        clientAttributeValue2.setClientAttributeValue("OLD VALUE THAT WILL BE UPDATED");

        ClientAttributeValue clientAttributeValue3 = new ClientAttributeValue();
        clientAttributeValue3.setId(2);
        clientAttributeValue3.setClientAttribute(clientAttribute2);
        clientAttributeValue3.setClientAttributeDefault(0);
        clientAttributeValue3.setStatus(Status.ACTIVE);
        clientAttributeValue3.setClientAttributeValue("123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890");

        when(mockGlobalCrudService.findByNamedQuery(ClientAttributeValue.BY_CLIENT_ID, QueryParameter.with("clientId", clientId).parameters())).thenReturn(Arrays.asList(clientAttributeValue2, clientAttributeValue3));

        ClientPropertyAttributePairing clientPropertyAttributePairing2 = new ClientPropertyAttributePairing();
        clientPropertyAttributePairing2.setId(2);
        clientPropertyAttributePairing2.setPropertyID(propertyId);
        clientPropertyAttributePairing2.setClientAttributeValue(clientAttributeValue2);

        when(mockGlobalCrudService.findByNamedQuery(ClientPropertyAttributePairing.BYPROPERTYID, QueryParameter.with("propertyID", propertyId).parameters())).thenReturn(Arrays.asList(clientPropertyAttributePairing2));

        service.saveCustomAttributePairingsForProperty(propertyId, map, true);
        verify(service, never()).saveClientAttributeValue(any());
        verify(service, never()).updateAttributeValueForPairingIds(anyList(), any());
        verify(service, never()).savePropertyClientAttributeValuePairing(anyList(), any());

        ArgumentCaptor<ClientPropertyAttributePairing> clientPropertyAttributePairingParameterCaptor = ArgumentCaptor.forClass(ClientPropertyAttributePairing.class);
        verify(service).saveClientAttributePairing(clientPropertyAttributePairingParameterCaptor.capture());
        assertEquals(propertyId, clientPropertyAttributePairingParameterCaptor.getValue().getPropertyID());
        assertEquals("123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890", clientPropertyAttributePairingParameterCaptor.getValue().getClientAttributeValue().getClientAttributeValue());
        assertEquals(Status.ACTIVE, clientPropertyAttributePairingParameterCaptor.getValue().getStatus());
    }

    @Test
    public void saveCustomAttributePairingsForProperty_MultipleScenarios() {
        CrudService mockGlobalCrudService = mock(CrudService.class);
        service.setGlobalCrudService(mockGlobalCrudService);
        Integer propertyId = 5;
        Map<String, String> map = new HashMap();
        //Existing attribute; existing pairing for property has same value (same case)
        map.put("InnCode", "ATLCV");
        //Existing attribute, update existing pairing for property to new value
        map.put("UseName", "Hampton Inn Covington");
        //Existing attribute, existing value and new pairing for property
        map.put("Brand", "Hampton Inn by Hilton");
        //Existing attribute, new value and pairing for property
        map.put("Brand Code", "HX");
        //Existing attribute; existing pairing for property has same value (different case)
        map.put("CurrencyCode", "USD");
        //Unknown attribute, should be skipped
        map.put("Unknown", "Test");
        map.put("isActive", "No");
        map.put("roomCount", "124");

        Client client = new Client();
        Integer clientId = 1;
        client.setId(clientId);

        Property property = new Property();
        property.setId(5);
        property.setClient(client);
        globalCrudService().save(property);

        propertyCache.setIdeasRedisCacheManager(new IdeasRedisCacheManager());
        propertyCache.put(5, property);
        clientPropertyCacheService.setPropertyCache(propertyCache);

        ClientAttribute clientAttribute1 = new ClientAttribute();
        clientAttribute1.setId(1);
        clientAttribute1.setClientId(clientId);
        clientAttribute1.setClientAttributeName("InnCode");
        clientAttribute1.setAttributeDisplayType(charAttributeDisplayType);

        ClientAttribute clientAttribute2 = new ClientAttribute();
        clientAttribute2.setId(2);
        clientAttribute2.setClientId(clientId);
        clientAttribute2.setClientAttributeName("UseName");
        clientAttribute2.setAttributeDisplayType(charAttributeDisplayType);

        ClientAttribute clientAttribute3 = new ClientAttribute();
        clientAttribute3.setId(3);
        clientAttribute3.setClientId(clientId);
        clientAttribute3.setClientAttributeName("Brand");
        clientAttribute3.setAttributeDisplayType(charAttributeDisplayType);

        ClientAttribute clientAttribute4 = new ClientAttribute();
        clientAttribute4.setId(4);
        clientAttribute4.setClientId(clientId);
        clientAttribute4.setClientAttributeName("Brand Code");
        clientAttribute4.setAttributeDisplayType(charAttributeDisplayType);

        ClientAttribute clientAttribute5 = new ClientAttribute();
        clientAttribute5.setId(5);
        clientAttribute5.setClientId(clientId);
        clientAttribute5.setClientAttributeName("CurrencyCode");
        clientAttribute5.setAttributeDisplayType(charAttributeDisplayType);

        ClientAttribute clientAttribute6 = new ClientAttribute();
        clientAttribute6.setId(6);
        clientAttribute6.setClientId(clientId);
        clientAttribute6.setClientAttributeName("UnusedAttribute"); //nothing should be set for this
        clientAttribute6.setAttributeDisplayType(charAttributeDisplayType);

        ClientAttribute clientAttribute7 = new ClientAttribute();
        clientAttribute7.setId(7);
        clientAttribute7.setClientId(clientId);
        clientAttribute7.setClientAttributeName("isActive");
        clientAttribute7.setAttributeDisplayType(booleanAttributeDisplayType);

        ClientAttribute clientAttribute8 = new ClientAttribute();
        clientAttribute8.setId(8);
        clientAttribute8.setClientId(clientId);
        clientAttribute8.setClientAttributeName("roomCount");
        clientAttribute8.setAttributeDisplayType(numbericAttributeDisplayType);

        when(mockGlobalCrudService.findByNamedQuery(ClientAttribute.ALL, QueryParameter.with("clientId", clientId).parameters()))
                .thenReturn(Arrays.asList(clientAttribute1, clientAttribute2, clientAttribute3, clientAttribute4, clientAttribute5, clientAttribute6, clientAttribute7, clientAttribute8));

        ClientAttributeValue clientAttributeValue1 = new ClientAttributeValue();
        clientAttributeValue1.setId(1);
        clientAttributeValue1.setClientAttribute(clientAttribute1);
        clientAttributeValue1.setClientAttributeDefault(0);
        clientAttributeValue1.setStatus(Status.ACTIVE);
        clientAttributeValue1.setClientAttributeValue("ATLCV");

        ClientAttributeValue clientAttributeValue2existing = new ClientAttributeValue();
        clientAttributeValue2existing.setId(2);
        clientAttributeValue2existing.setClientAttribute(clientAttribute2);
        clientAttributeValue2existing.setClientAttributeDefault(0);
        clientAttributeValue2existing.setStatus(Status.ACTIVE);
        clientAttributeValue2existing.setClientAttributeValue("OLD VALUE THAT WILL BE UPDATED");

        ClientAttributeValue clientAttributeValue2new = new ClientAttributeValue();
        clientAttributeValue2new.setId(2);
        clientAttributeValue2new.setClientAttribute(clientAttribute2);
        clientAttributeValue2new.setClientAttributeDefault(0);
        clientAttributeValue2new.setStatus(Status.ACTIVE);
        clientAttributeValue2new.setClientAttributeValue("Hampton Inn Covington");
        when(mockGlobalCrudService.find(ClientAttributeValue.class, 2)).thenReturn(clientAttributeValue2new);

        ClientAttributeValue clientAttributeValue3 = new ClientAttributeValue();
        clientAttributeValue3.setId(3);
        clientAttributeValue3.setClientAttribute(clientAttribute3);
        clientAttributeValue3.setClientAttributeDefault(0);
        clientAttributeValue3.setStatus(Status.ACTIVE);
        clientAttributeValue3.setClientAttributeValue("Hampton Inn by Hilton");
        when(mockGlobalCrudService.find(ClientAttributeValue.class, 3)).thenReturn(clientAttributeValue3);

        ClientAttributeValue clientAttributeValue5 = new ClientAttributeValue();
        clientAttributeValue5.setId(5);
        clientAttributeValue5.setClientAttribute(clientAttribute5);
        clientAttributeValue5.setClientAttributeDefault(0);
        clientAttributeValue5.setStatus(Status.ACTIVE);
        clientAttributeValue5.setClientAttributeValue("usd");
        when(mockGlobalCrudService.find(ClientAttributeValue.class, 5)).thenReturn(clientAttributeValue5);

        ClientAttributeValue clientAttributeValue7 = new ClientAttributeValue();
        clientAttributeValue7.setId(7);
        clientAttributeValue7.setClientAttribute(clientAttribute7);
        clientAttributeValue7.setClientAttributeDefault(0);
        clientAttributeValue7.setStatus(Status.ACTIVE);
        clientAttributeValue7.setClientAttributeValue("Yes");
        when(mockGlobalCrudService.find(ClientAttributeValue.class, 7)).thenReturn(clientAttributeValue7);

        ClientAttributeValue clientAttributeValue8 = new ClientAttributeValue();
        clientAttributeValue8.setId(8);
        clientAttributeValue8.setClientAttribute(clientAttribute8);
        clientAttributeValue8.setClientAttributeDefault(0);
        clientAttributeValue8.setStatus(Status.ACTIVE);
        clientAttributeValue8.setClientAttributeValue("123");
        when(mockGlobalCrudService.find(ClientAttributeValue.class, 5)).thenReturn(clientAttributeValue8);

        when(mockGlobalCrudService.findByNamedQuery(ClientAttributeValue.BY_CLIENT_ID, QueryParameter.with("clientId", clientId).parameters())).thenReturn(Arrays.asList(clientAttributeValue1, clientAttributeValue2existing, clientAttributeValue3, clientAttributeValue5, clientAttributeValue7, clientAttributeValue8));
        when(mockGlobalCrudService.save(any(ClientAttributeValue.class))).thenReturn(clientAttributeValue2new);

        ClientPropertyAttributePairing clientPropertyAttributePairing1 = new ClientPropertyAttributePairing();
        clientPropertyAttributePairing1.setId(1);
        clientPropertyAttributePairing1.setPropertyID(propertyId);
        clientPropertyAttributePairing1.setClientAttributeValue(clientAttributeValue1);

        ClientPropertyAttributePairing clientPropertyAttributePairing2 = new ClientPropertyAttributePairing();
        clientPropertyAttributePairing2.setId(2);
        clientPropertyAttributePairing2.setPropertyID(propertyId);
        clientPropertyAttributePairing2.setClientAttributeValue(clientAttributeValue2existing);

        ClientPropertyAttributePairing clientPropertyAttributePairing5 = new ClientPropertyAttributePairing();
        clientPropertyAttributePairing5.setId(5);
        clientPropertyAttributePairing5.setPropertyID(propertyId);
        clientPropertyAttributePairing5.setClientAttributeValue(clientAttributeValue5);

        when(mockGlobalCrudService.findByNamedQuery(ClientPropertyAttributePairing.BYPROPERTYID, QueryParameter.with("propertyID", propertyId).parameters())).thenReturn(Arrays.asList(clientPropertyAttributePairing1, clientPropertyAttributePairing2, clientPropertyAttributePairing5));

        service.saveCustomAttributePairingsForProperty(propertyId, map, true);
        verify(service, times(4)).saveClientAttributeValue(any());
        verify(service, times(1)).updateAttributeValueForPairingIds(anyList(), any());
        verify(service, times(4)).savePropertyClientAttributeValuePairing(anyList(), any());
    }

    @Test
    public void validateClientAttributeValue() {
        ClientAttribute clientAttribute1 = new ClientAttribute();
        clientAttribute1.setClientAttributeName("TestChar");
        clientAttribute1.setAttributeDisplayType(charAttributeDisplayType);

        ClientAttribute clientAttribute2 = new ClientAttribute();
        clientAttribute2.setClientAttributeName("TestBoolean");
        clientAttribute2.setAttributeDisplayType(booleanAttributeDisplayType);

        ClientAttribute clientAttribute3 = new ClientAttribute();
        clientAttribute3.setClientAttributeName("TestNumeric");
        clientAttribute3.setAttributeDisplayType(numbericAttributeDisplayType);

        //Char
        assertDoesNotThrow(() -> service.validateClientAttributeValue(clientAttribute1, null));
        assertDoesNotThrow(() -> service.validateClientAttributeValue(clientAttribute1, "TEST"));
        assertDoesNotThrow(() -> service.validateClientAttributeValue(clientAttribute1, "Yes"));
        assertDoesNotThrow(() -> service.validateClientAttributeValue(clientAttribute1, "123"));

        //Boolean
        assertDoesNotThrow(() -> service.validateClientAttributeValue(clientAttribute2, null));
        TetrisException exception1 = assertThrows(TetrisException.class, () -> service.validateClientAttributeValue(clientAttribute2, "TEST"));
        assertEquals(ErrorCode.UNEXPECTED_ERROR, exception1.getErrorCode());
        assertEquals("Invalid value for Boolean client attribute " + clientAttribute2.getClientAttributeName() + ", value: TEST", exception1.getBaseMessage());
        assertDoesNotThrow(() -> service.validateClientAttributeValue(clientAttribute2, "Yes"));
        TetrisException exception2 = assertThrows(TetrisException.class, () -> service.validateClientAttributeValue(clientAttribute2, "123"));
        assertEquals(ErrorCode.UNEXPECTED_ERROR, exception2.getErrorCode());
        assertEquals("Invalid value for Boolean client attribute " + clientAttribute2.getClientAttributeName() + ", value: 123", exception2.getBaseMessage());
        assertDoesNotThrow(() -> service.validateClientAttributeValue(clientAttribute2, "NO"));

        //Numeric
        assertDoesNotThrow(() -> service.validateClientAttributeValue(clientAttribute3, null));
        TetrisException exception3 = assertThrows(TetrisException.class, () -> service.validateClientAttributeValue(clientAttribute3, "TEST"));
        assertEquals(ErrorCode.UNEXPECTED_ERROR, exception3.getErrorCode());
        assertEquals("Invalid value for Numeric client attribute " + clientAttribute3.getClientAttributeName() + ", value: TEST", exception3.getBaseMessage());
        TetrisException exception4 = assertThrows(TetrisException.class, () -> service.validateClientAttributeValue(clientAttribute3, "Yes"));
        assertEquals(ErrorCode.UNEXPECTED_ERROR, exception4.getErrorCode());
        assertEquals("Invalid value for Numeric client attribute " + clientAttribute3.getClientAttributeName() + ", value: Yes", exception4.getBaseMessage());
        assertDoesNotThrow(() -> service.validateClientAttributeValue(clientAttribute3, "123"));
        assertDoesNotThrow(() -> service.validateClientAttributeValue(clientAttribute3, "1.23"));
    }

    @Test
    public void getAttributeDisplayType() {
        CrudService mockGlobalCrudService = mock(CrudService.class);
        service.setGlobalCrudService(mockGlobalCrudService);

        service.getAttributeDisplayType(charAttributeDisplayType.getDisplayTypeName());
        verify(mockGlobalCrudService).findByNamedQuerySingleResult(AttributeDisplayType.BY_NAME,
                QueryParameter.with("name", charAttributeDisplayType.getDisplayTypeName()).parameters());
    }

    @Test
    public void processCustomAttributeCreateUpdateFromFDS_Create() {
        CrudService mockGlobalCrudService = mock(CrudService.class);
        service.setGlobalCrudService(mockGlobalCrudService);
        UPSService mockUpsService = mock(UPSService.class);
        service.setUpsService(mockUpsService);

        Client client = new Client();
        Integer clientId = 1;
        client.setId(clientId);
        UUID clientUUID = UUID.randomUUID();
        client.setUpsClientUuid(clientUUID.toString());

        UUID customAttributeId = UUID.randomUUID();
        UPSCustomAttribute upsCustomAttribute = new UPSCustomAttribute();
        upsCustomAttribute.setCustomAttributeId(customAttributeId);
        String name = "TestChar";
        upsCustomAttribute.setAttributeName(name);
        upsCustomAttribute.setDataType(charAttributeDisplayType.getDisplayTypeName());
        upsCustomAttribute.setClientId(clientUUID);
        upsCustomAttribute.setDescription("TestDescription12345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890");
        upsCustomAttribute.setMaxCharacterLength("100");
        upsCustomAttribute.setPredefined(Boolean.FALSE);
        upsCustomAttribute.setDefaultValue("n/a");
        upsCustomAttribute.setValues(Arrays.asList("TestValue", "TestValue2"));

        ClientAttribute clientAttribute = new ClientAttribute();
        clientAttribute.setClientAttributeName(name);
        clientAttribute.setAttributeDisplayType(charAttributeDisplayType);
        clientAttribute.setClientId(client.getId());
        clientAttribute.setClientAttributeDescription("TestDescription123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345");
        clientAttribute.setAttributeDisplayLength(100);
        clientAttribute.setUserLengthEntry(1);
        clientAttribute.setUpsCustomAttributeUuid(customAttributeId.toString());
        ClientAttributeValue clientAttributeValue = new ClientAttributeValue();
        clientAttributeValue.setStatus(Status.ACTIVE);
        clientAttributeValue.setClientAttributeValue("TestValue");
        clientAttributeValue.setClientAttribute(clientAttribute);
        clientAttributeValue.setClientAttributeDefault(0);
        ClientAttributeValue clientAttributeValue2 = new ClientAttributeValue();
        clientAttributeValue2.setStatus(Status.ACTIVE);
        clientAttributeValue2.setClientAttributeValue("TestValue2");
        clientAttributeValue2.setClientAttribute(clientAttribute);
        clientAttributeValue2.setClientAttributeDefault(0);
        clientAttribute.setClientAttributeValues(new HashSet<>(Arrays.asList(clientAttributeValue, clientAttributeValue2)));
        ClientAttributeValue clientAttributeValue3 = new ClientAttributeValue();
        clientAttributeValue3.setStatus(Status.ACTIVE);
        clientAttributeValue3.setClientAttributeValue("n/a");
        clientAttributeValue3.setClientAttribute(clientAttribute);
        clientAttributeValue3.setClientAttributeDefault(1);

        when(mockGlobalCrudService.findByNamedQuerySingleResult(ClientAttribute.BY_UPS_UUID,
                QueryParameter.with("customAttributeId", customAttributeId.toString())
                        .and("clientId", clientId).parameters())).thenReturn(null);

        when(mockUpsService.getCustomAttributeFromFDS(customAttributeId.toString())).thenReturn(upsCustomAttribute);
        when(mockGlobalCrudService.findByNamedQuerySingleResult(AttributeDisplayType.BY_NAME,
                QueryParameter.with("name", charAttributeDisplayType.getDisplayTypeName()).parameters())).thenReturn(charAttributeDisplayType);

        service.processCustomAttributeCreateUpdateFromFDS(client, customAttributeId.toString());
        ArgumentCaptor<List<ClientAttribute>> clientAttributeParameterCaptor = ArgumentCaptor.forClass(ArrayList.class);
        verify(mockGlobalCrudService).save(clientAttributeParameterCaptor.capture());
        assertEquals(1, clientAttributeParameterCaptor.getValue().size());
        ClientAttribute result = clientAttributeParameterCaptor.getValue().get(0);
        assertEquals(clientAttribute.getClientAttributeName(), result.getClientAttributeName());
        assertEquals(clientAttribute.getAttributeDisplayType(), result.getAttributeDisplayType());
        assertEquals(clientAttribute.getClientId(), result.getClientId());
        assertEquals(clientAttribute.getClientAttributeDescription(), result.getClientAttributeDescription());
        assertEquals(clientAttribute.getAttributeDisplayLength(), result.getAttributeDisplayLength());
        assertEquals(clientAttribute.getUserLengthEntry(), result.getUserLengthEntry());
        assertEquals(clientAttribute.getUpsCustomAttributeUuid(), result.getUpsCustomAttributeUuid());
        assertEquals(3, result.getClientAttributeValues().size());
        List<ClientAttributeValue> clientAttributeValues = new ArrayList<>(result.getClientAttributeValues());
        ClientAttributeValue resultValue1 = clientAttributeValues.get(1);
        assertEquals(clientAttributeValue.getStatus(), resultValue1.getStatus());
        assertEquals(clientAttributeValue.getClientAttributeValue(), resultValue1.getClientAttributeValue());
        assertEquals(clientAttributeValue.getClientAttributeDefault(), resultValue1.getClientAttributeDefault());
        ClientAttributeValue resultValue2 = clientAttributeValues.get(2);
        assertEquals(clientAttributeValue2.getStatus(), resultValue2.getStatus());
        assertEquals(clientAttributeValue2.getClientAttributeValue(), resultValue2.getClientAttributeValue());
        assertEquals(clientAttributeValue2.getClientAttributeDefault(), resultValue2.getClientAttributeDefault());
        ClientAttributeValue resultValue3 = clientAttributeValues.get(0);
        assertEquals(clientAttributeValue3.getStatus(), resultValue3.getStatus());
        assertEquals(clientAttributeValue3.getClientAttributeValue(), resultValue3.getClientAttributeValue());
        assertEquals(clientAttributeValue3.getClientAttributeDefault(), resultValue3.getClientAttributeDefault());
    }

    @Test
    public void processCustomAttributeCreateUpdateFromFDS_Create_NullValues() {
        CrudService mockGlobalCrudService = mock(CrudService.class);
        service.setGlobalCrudService(mockGlobalCrudService);
        UPSService mockUpsService = mock(UPSService.class);
        service.setUpsService(mockUpsService);

        Client client = new Client();
        Integer clientId = 1;
        client.setId(clientId);
        UUID clientUUID = UUID.randomUUID();
        client.setUpsClientUuid(clientUUID.toString());

        UUID customAttributeId = UUID.randomUUID();
        UPSCustomAttribute upsCustomAttribute = new UPSCustomAttribute();
        upsCustomAttribute.setCustomAttributeId(customAttributeId);
        String name = "TestChar";
        upsCustomAttribute.setAttributeName(name);
        upsCustomAttribute.setDataType(charAttributeDisplayType.getDisplayTypeName());
        upsCustomAttribute.setClientId(clientUUID);
        upsCustomAttribute.setDescription(null);
        upsCustomAttribute.setMaxCharacterLength(null);
        upsCustomAttribute.setPredefined(Boolean.TRUE);
        upsCustomAttribute.setDefaultValue(null);
        upsCustomAttribute.setValues(Arrays.asList("TestValue", "TestValue2"));

        ClientAttribute clientAttribute = new ClientAttribute();
        clientAttribute.setClientAttributeName(name);
        clientAttribute.setAttributeDisplayType(charAttributeDisplayType);
        clientAttribute.setClientId(client.getId());
        clientAttribute.setClientAttributeDescription(null);
        clientAttribute.setAttributeDisplayLength(null);
        clientAttribute.setUserLengthEntry(0);
        clientAttribute.setUpsCustomAttributeUuid(customAttributeId.toString());
        ClientAttributeValue clientAttributeValue = new ClientAttributeValue();
        clientAttributeValue.setStatus(Status.ACTIVE);
        clientAttributeValue.setClientAttributeValue("TestValue");
        clientAttributeValue.setClientAttribute(clientAttribute);
        clientAttributeValue.setClientAttributeDefault(0);
        ClientAttributeValue clientAttributeValue2 = new ClientAttributeValue();
        clientAttributeValue2.setStatus(Status.ACTIVE);
        clientAttributeValue2.setClientAttributeValue("TestValue2");
        clientAttributeValue2.setClientAttribute(clientAttribute);
        clientAttributeValue2.setClientAttributeDefault(0);
        clientAttribute.setClientAttributeValues(new HashSet<>(Arrays.asList(clientAttributeValue, clientAttributeValue2)));

        when(mockGlobalCrudService.findByNamedQuerySingleResult(ClientAttribute.BY_UPS_UUID,
                QueryParameter.with("customAttributeId", customAttributeId.toString())
                        .and("clientId", clientId).parameters())).thenReturn(null);

        when(mockUpsService.getCustomAttributeFromFDS(customAttributeId.toString())).thenReturn(upsCustomAttribute);
        when(mockGlobalCrudService.findByNamedQuerySingleResult(AttributeDisplayType.BY_NAME,
                QueryParameter.with("name", charAttributeDisplayType.getDisplayTypeName()).parameters())).thenReturn(charAttributeDisplayType);

        service.processCustomAttributeCreateUpdateFromFDS(client, customAttributeId.toString());
        ArgumentCaptor<List<ClientAttribute>> clientAttributeParameterCaptor = ArgumentCaptor.forClass(ArrayList.class);
        verify(mockGlobalCrudService).save(clientAttributeParameterCaptor.capture());
        assertEquals(1, clientAttributeParameterCaptor.getValue().size());
        ClientAttribute result = clientAttributeParameterCaptor.getValue().get(0);
        assertEquals(clientAttribute.getClientAttributeName(), result.getClientAttributeName());
        assertEquals(clientAttribute.getAttributeDisplayType(), result.getAttributeDisplayType());
        assertEquals(clientAttribute.getClientId(), result.getClientId());
        assertEquals(clientAttribute.getClientAttributeDescription(), result.getClientAttributeDescription());
        assertEquals(clientAttribute.getAttributeDisplayLength(), result.getAttributeDisplayLength());
        assertEquals(clientAttribute.getUserLengthEntry(), result.getUserLengthEntry());
        assertEquals(clientAttribute.getUpsCustomAttributeUuid(), result.getUpsCustomAttributeUuid());
        assertEquals(2, result.getClientAttributeValues().size());
        List<ClientAttributeValue> clientAttributeValues = new ArrayList<>(result.getClientAttributeValues());
        ClientAttributeValue resultValue1 = clientAttributeValues.get(0);
        assertEquals(clientAttributeValue.getStatus(), resultValue1.getStatus());
        assertEquals(clientAttributeValue.getClientAttributeValue(), resultValue1.getClientAttributeValue());
        assertEquals(clientAttributeValue.getClientAttributeDefault(), resultValue1.getClientAttributeDefault());
        ClientAttributeValue resultValue2 = clientAttributeValues.get(1);
        assertEquals(clientAttributeValue2.getStatus(), resultValue2.getStatus());
        assertEquals(clientAttributeValue2.getClientAttributeValue(), resultValue2.getClientAttributeValue());
        assertEquals(clientAttributeValue2.getClientAttributeDefault(), resultValue2.getClientAttributeDefault());
    }

    @Test
    public void processCustomAttributeCreateUpdateFromFDS_Create_DoesNotExist() {
        CrudService mockGlobalCrudService = mock(CrudService.class);
        service.setGlobalCrudService(mockGlobalCrudService);
        UPSService mockUpsService = mock(UPSService.class);
        service.setUpsService(mockUpsService);

        Client client = new Client();
        Integer clientId = 1;
        client.setId(clientId);
        UUID clientUUID = UUID.randomUUID();
        client.setUpsClientUuid(clientUUID.toString());

        UUID customAttributeId = UUID.randomUUID();

        when(mockGlobalCrudService.findByNamedQuerySingleResult(ClientAttribute.BY_UPS_UUID,
                QueryParameter.with("customAttributeId", customAttributeId.toString())
                        .and("clientId", clientId).parameters())).thenReturn(null);

        when(mockUpsService.getCustomAttributeFromFDS(customAttributeId.toString())).thenReturn(null);

        TetrisException exception = assertThrows(TetrisException.class, () -> service.processCustomAttributeCreateUpdateFromFDS(client, customAttributeId.toString()));
        assertEquals(ErrorCode.UNEXPECTED_ERROR, exception.getErrorCode());
        assertEquals("Error creating customAttributeId: " + customAttributeId + " for clientId: " + client.getUpsClientUuid() + ", as the custom attribute doesn't exist in FDS", exception.getBaseMessage());
    }

    @Test
    public void processCustomAttributeCreateUpdateFromFDS_Update() {
        CrudService mockGlobalCrudService = mock(CrudService.class);
        service.setGlobalCrudService(mockGlobalCrudService);
        UPSService mockUpsService = mock(UPSService.class);
        service.setUpsService(mockUpsService);

        Client client = new Client();
        Integer clientId = 1;
        client.setId(clientId);
        UUID clientUUID = UUID.randomUUID();
        client.setUpsClientUuid(clientUUID.toString());

        UUID customAttributeId = UUID.randomUUID();
        UPSCustomAttribute upsCustomAttribute = new UPSCustomAttribute();
        upsCustomAttribute.setCustomAttributeId(customAttributeId);
        String name = "TestChar";
        upsCustomAttribute.setAttributeName(name);
        upsCustomAttribute.setDataType(charAttributeDisplayType.getDisplayTypeName());
        upsCustomAttribute.setClientId(clientUUID);
        upsCustomAttribute.setDescription("TestDescription12345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890");
        upsCustomAttribute.setMaxCharacterLength("100");
        upsCustomAttribute.setPredefined(Boolean.FALSE);
        upsCustomAttribute.setDefaultValue("n/a");
        upsCustomAttribute.setValues(Arrays.asList("TestValue", "TestValue2", "TestValue3"));

        ClientAttribute clientAttribute = new ClientAttribute();
        clientAttribute.setClientAttributeName(name);
        clientAttribute.setAttributeDisplayType(charAttributeDisplayType);
        clientAttribute.setClientId(client.getId());
        clientAttribute.setClientAttributeDescription("TestDescription123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345");
        clientAttribute.setAttributeDisplayLength(100);
        clientAttribute.setUserLengthEntry(1);
        clientAttribute.setUpsCustomAttributeUuid(customAttributeId.toString());
        ClientAttributeValue clientAttributeValue = new ClientAttributeValue();
        clientAttributeValue.setStatus(Status.ACTIVE);
        clientAttributeValue.setClientAttributeValue("TestValue");
        clientAttributeValue.setClientAttribute(clientAttribute);
        clientAttributeValue.setClientAttributeDefault(0);
        ClientAttributeValue clientAttributeValue2 = new ClientAttributeValue();
        clientAttributeValue2.setStatus(Status.ACTIVE);
        clientAttributeValue2.setClientAttributeValue("TestValue2");
        clientAttributeValue2.setClientAttribute(clientAttribute);
        clientAttributeValue2.setClientAttributeDefault(0);
        clientAttribute.setClientAttributeValues(new HashSet<>(Arrays.asList(clientAttributeValue, clientAttributeValue2)));
        ClientAttributeValue clientAttributeValue3 = new ClientAttributeValue();
        clientAttributeValue3.setStatus(Status.ACTIVE);
        clientAttributeValue3.setClientAttributeValue("n/a");
        clientAttributeValue3.setClientAttribute(clientAttribute);
        clientAttributeValue3.setClientAttributeDefault(1);

        when(mockGlobalCrudService.findByNamedQuerySingleResult(ClientAttribute.BY_UPS_UUID,
                QueryParameter.with("customAttributeId", customAttributeId.toString())
                        .and("clientId", clientId).parameters())).thenReturn(clientAttribute);

        when(mockUpsService.getCustomAttributeFromFDS(customAttributeId.toString())).thenReturn(upsCustomAttribute);
        when(mockGlobalCrudService.findByNamedQuerySingleResult(AttributeDisplayType.BY_NAME,
                QueryParameter.with("name", charAttributeDisplayType.getDisplayTypeName()).parameters())).thenReturn(charAttributeDisplayType);

        service.processCustomAttributeCreateUpdateFromFDS(client, customAttributeId.toString());
        ArgumentCaptor<List<ClientAttribute>> clientAttributeParameterCaptor = ArgumentCaptor.forClass(ArrayList.class);
        verify(mockGlobalCrudService).save(clientAttributeParameterCaptor.capture());
        assertEquals(1, clientAttributeParameterCaptor.getValue().size());
        ClientAttribute result = clientAttributeParameterCaptor.getValue().get(0);
        assertEquals(clientAttribute, result);
        assertEquals(4, result.getClientAttributeValues().size());
        List<ClientAttributeValue> clientAttributeValues = new ArrayList<>(result.getClientAttributeValues());
        assertEquals(clientAttributeValue, clientAttributeValues.get(0));
        assertEquals(clientAttributeValue2, clientAttributeValues.get(1));
        ClientAttributeValue defaultResult = clientAttributeValues.get(2);
        assertEquals(clientAttributeValue3.getClientAttributeValue(), defaultResult.getClientAttributeValue());
        assertEquals(clientAttributeValue3.getClientAttributeDefault(), defaultResult.getClientAttributeDefault());
        assertEquals(clientAttributeValue3.getStatus(), defaultResult.getStatus());
        assertEquals(clientAttributeValue3.getClientAttribute(), defaultResult.getClientAttribute());
        ClientAttributeValue clientAttributeValue4 = clientAttributeValues.get(3);
        assertEquals(Status.ACTIVE, clientAttributeValue4.getStatus());
        assertEquals(0, clientAttributeValue4.getClientAttributeDefault());
        assertEquals("TestValue3", clientAttributeValue4.getClientAttributeValue());
        assertEquals(clientAttribute, clientAttributeValue4.getClientAttribute());
    }

    @Test
    public void processCustomAttributeCreateUpdateFromFDS_Update_NullValues() {
        CrudService mockGlobalCrudService = mock(CrudService.class);
        service.setGlobalCrudService(mockGlobalCrudService);
        UPSService mockUpsService = mock(UPSService.class);
        service.setUpsService(mockUpsService);

        Client client = new Client();
        Integer clientId = 1;
        client.setId(clientId);
        UUID clientUUID = UUID.randomUUID();
        client.setUpsClientUuid(clientUUID.toString());

        UUID customAttributeId = UUID.randomUUID();
        UPSCustomAttribute upsCustomAttribute = new UPSCustomAttribute();
        upsCustomAttribute.setCustomAttributeId(customAttributeId);
        String name = "TestChar";
        upsCustomAttribute.setAttributeName(name);
        upsCustomAttribute.setDataType(charAttributeDisplayType.getDisplayTypeName());
        upsCustomAttribute.setClientId(clientUUID);
        upsCustomAttribute.setDescription(null);
        upsCustomAttribute.setMaxCharacterLength(null);
        upsCustomAttribute.setPredefined(Boolean.FALSE);
        upsCustomAttribute.setDefaultValue(null);
        upsCustomAttribute.setValues(Arrays.asList("TestValue", "TestValue2", "TestValue3"));

        ClientAttribute clientAttribute = new ClientAttribute();
        clientAttribute.setClientAttributeName(name);
        clientAttribute.setAttributeDisplayType(charAttributeDisplayType);
        clientAttribute.setClientId(client.getId());
        clientAttribute.setClientAttributeDescription(null);
        clientAttribute.setAttributeDisplayLength(100);
        clientAttribute.setUserLengthEntry(1);
        clientAttribute.setUpsCustomAttributeUuid(customAttributeId.toString());
        ClientAttributeValue clientAttributeValue = new ClientAttributeValue();
        clientAttributeValue.setStatus(Status.ACTIVE);
        clientAttributeValue.setClientAttributeValue("TestValue");
        clientAttributeValue.setClientAttribute(clientAttribute);
        clientAttributeValue.setClientAttributeDefault(0);
        ClientAttributeValue clientAttributeValue2 = new ClientAttributeValue();
        clientAttributeValue2.setStatus(Status.ACTIVE);
        clientAttributeValue2.setClientAttributeValue("TestValue2");
        clientAttributeValue2.setClientAttribute(clientAttribute);
        clientAttributeValue2.setClientAttributeDefault(0);
        clientAttribute.setClientAttributeValues(new HashSet<>(Arrays.asList(clientAttributeValue, clientAttributeValue2)));

        when(mockGlobalCrudService.findByNamedQuerySingleResult(ClientAttribute.BY_UPS_UUID,
                QueryParameter.with("customAttributeId", customAttributeId.toString())
                        .and("clientId", clientId).parameters())).thenReturn(clientAttribute);

        when(mockUpsService.getCustomAttributeFromFDS(customAttributeId.toString())).thenReturn(upsCustomAttribute);
        when(mockGlobalCrudService.findByNamedQuerySingleResult(AttributeDisplayType.BY_NAME,
                QueryParameter.with("name", charAttributeDisplayType.getDisplayTypeName()).parameters())).thenReturn(charAttributeDisplayType);
        service.processCustomAttributeCreateUpdateFromFDS(client, customAttributeId.toString());
        ArgumentCaptor<List<ClientAttribute>> clientAttributeParameterCaptor = ArgumentCaptor.forClass(ArrayList.class);
        verify(mockGlobalCrudService).save(clientAttributeParameterCaptor.capture());
        assertEquals(1, clientAttributeParameterCaptor.getValue().size());
        ClientAttribute result = clientAttributeParameterCaptor.getValue().get(0);
        assertEquals(clientAttribute, result);
        assertEquals(3, result.getClientAttributeValues().size());
        List<ClientAttributeValue> clientAttributeValues = new ArrayList<>(result.getClientAttributeValues());
        assertEquals(clientAttributeValue, clientAttributeValues.get(0));
        assertEquals(clientAttributeValue2, clientAttributeValues.get(1));
        ClientAttributeValue clientAttributeValue3 = clientAttributeValues.get(2);
        assertEquals(Status.ACTIVE, clientAttributeValue3.getStatus());
        assertEquals(0, clientAttributeValue3.getClientAttributeDefault());
        assertEquals("TestValue3", clientAttributeValue3.getClientAttributeValue());
        assertEquals(clientAttribute, clientAttributeValue3.getClientAttribute());
    }

    @Test
    public void processCustomAttributeCreateUpdateFromFDS_Update_DoesNotExist() {
        CrudService mockGlobalCrudService = mock(CrudService.class);
        service.setGlobalCrudService(mockGlobalCrudService);
        UPSService mockUpsService = mock(UPSService.class);
        service.setUpsService(mockUpsService);

        Client client = new Client();
        Integer clientId = 1;
        client.setId(clientId);
        UUID clientUUID = UUID.randomUUID();
        client.setUpsClientUuid(clientUUID.toString());

        UUID customAttributeId = UUID.randomUUID();
        ClientAttribute clientAttribute = new ClientAttribute();
        clientAttribute.setUpsCustomAttributeUuid(customAttributeId.toString());

        when(mockGlobalCrudService.findByNamedQuerySingleResult(ClientAttribute.BY_UPS_UUID,
                QueryParameter.with("customAttributeId", customAttributeId.toString())
                        .and("clientId", clientId).parameters())).thenReturn(clientAttribute);

        when(mockUpsService.getCustomAttributeFromFDS(customAttributeId.toString())).thenReturn(null);

        TetrisException exception = assertThrows(TetrisException.class, () -> service.processCustomAttributeCreateUpdateFromFDS(client, customAttributeId.toString()));
        assertEquals(ErrorCode.UNEXPECTED_ERROR, exception.getErrorCode());
        assertEquals("Error updating customAttributeId: " + customAttributeId + " for clientId: " + client.getUpsClientUuid() + ", as the custom attribute doesn't exist in FDS", exception.getBaseMessage());
    }

    @Test
    public void processCustomAttributeDeleteFromFDS() {
        CrudService mockGlobalCrudService = mock(CrudService.class);
        service.setGlobalCrudService(mockGlobalCrudService);
        UPSService mockUpsService = mock(UPSService.class);
        service.setUpsService(mockUpsService);

        List<Integer> rules = new ArrayList<>();
        EntityManager mockEntityManager = mock(EntityManager.class);
        Query mockQuery1 = mock(Query.class);
        when(mockGlobalCrudService.getEntityManager()).thenReturn(mockEntityManager);
        when(mockEntityManager.createNativeQuery(any(String.class))).thenReturn(mockQuery1);
        when(mockQuery1.setParameter("ruleIds", rules)).thenReturn(mockQuery1);
        when(mockQuery1.getResultList()).thenReturn(rules);

        Client client = new Client();
        Integer clientId = 1;
        client.setId(clientId);
        UUID clientUUID = UUID.randomUUID();
        client.setUpsClientUuid(clientUUID.toString());

        UUID customAttributeId = UUID.randomUUID();
        UPSCustomAttribute upsCustomAttribute = new UPSCustomAttribute();
        upsCustomAttribute.setCustomAttributeId(customAttributeId);
        String name = "TestChar";
        upsCustomAttribute.setAttributeName(name);
        upsCustomAttribute.setDataType(charAttributeDisplayType.getDisplayTypeName());
        upsCustomAttribute.setClientId(clientUUID);
        String description = "TestDescription";
        upsCustomAttribute.setDescription(description);
        upsCustomAttribute.setMaxCharacterLength("100");
        upsCustomAttribute.setPredefined(Boolean.FALSE);
        upsCustomAttribute.setValues(Arrays.asList("TestValue", "TestValue2"));

        ClientAttribute clientAttribute = new ClientAttribute();
        clientAttribute.setClientAttributeName(name);
        clientAttribute.setAttributeDisplayType(charAttributeDisplayType);
        clientAttribute.setClientId(client.getId());
        clientAttribute.setClientAttributeDescription(description);
        clientAttribute.setAttributeDisplayLength(100);
        clientAttribute.setUserLengthEntry(1);
        clientAttribute.setUpsCustomAttributeUuid(customAttributeId.toString());
        ClientAttributeValue clientAttributeValue = new ClientAttributeValue();
        clientAttributeValue.setStatus(Status.ACTIVE);
        clientAttributeValue.setClientAttributeValue("TestValue");
        clientAttributeValue.setClientAttribute(clientAttribute);
        clientAttributeValue.setClientAttributeDefault(0);
        ClientAttributeValue clientAttributeValue2 = new ClientAttributeValue();
        clientAttributeValue2.setStatus(Status.ACTIVE);
        clientAttributeValue2.setClientAttributeValue("TestValue2");
        clientAttributeValue2.setClientAttribute(clientAttribute);
        clientAttributeValue2.setClientAttributeDefault(0);
        clientAttribute.setClientAttributeValues(new HashSet<>(Arrays.asList(clientAttributeValue, clientAttributeValue2)));

        when(mockGlobalCrudService.findByNamedQuerySingleResult(ClientAttribute.BY_UPS_UUID,
                QueryParameter.with("customAttributeId", customAttributeId.toString())
                        .and("clientId", clientId).parameters())).thenReturn(clientAttribute);

        when(mockUpsService.getCustomAttributeFromFDS(customAttributeId.toString())).thenReturn(upsCustomAttribute);
        when(mockGlobalCrudService.findByNamedQuerySingleResult(AttributeDisplayType.BY_NAME,
                QueryParameter.with("name", charAttributeDisplayType.getDisplayTypeName()).parameters())).thenReturn(charAttributeDisplayType);

        service.processCustomAttributeDeleteFromFDS(client, customAttributeId.toString());
        verify(mockGlobalCrudService).delete(ClientAttribute.class, clientAttribute.getId());
        verify(mockUpsService, never()).deleteCustomAttributeValues(any(ClientAttribute.class));
    }

    @Test
    public void processCustomAttributeDeleteFromFDS_DoesNotExist() {
        CrudService mockGlobalCrudService = mock(CrudService.class);
        service.setGlobalCrudService(mockGlobalCrudService);
        UPSService mockUpsService = mock(UPSService.class);
        service.setUpsService(mockUpsService);

        Client client = new Client();
        Integer clientId = 1;
        client.setId(clientId);
        UUID clientUUID = UUID.randomUUID();
        client.setUpsClientUuid(clientUUID.toString());

        UUID customAttributeId = UUID.randomUUID();

        when(mockGlobalCrudService.findByNamedQuerySingleResult(ClientAttribute.BY_UPS_UUID,
                QueryParameter.with("customAttributeId", customAttributeId.toString())
                        .and("clientId", clientId).parameters())).thenReturn(null);

        service.processCustomAttributeDeleteFromFDS(client, customAttributeId.toString());
        verify(mockUpsService, never()).getCustomAttributeFromFDS(customAttributeId.toString());
        verify(mockGlobalCrudService, never()).findByNamedQuerySingleResult(eq(AttributeDisplayType.BY_NAME), any());
        verify(mockGlobalCrudService, never()).delete(eq(ClientAttribute.class), any());
        verify(mockUpsService, never()).deleteCustomAttributeValues(any(ClientAttribute.class));
    }

    @Test
    public void getClientAttributeByUpsCustomAttributeUuid() {
        CrudService mockGlobalCrudService = mock(CrudService.class);
        service.setGlobalCrudService(mockGlobalCrudService);

        Client client = new Client();
        Integer clientId = 1;
        client.setId(clientId);
        UUID clientUUID = UUID.randomUUID();
        client.setUpsClientUuid(clientUUID.toString());

        UUID customAttributeId = UUID.randomUUID();

        service.getClientAttributeByUpsCustomAttributeUuid(customAttributeId.toString(), clientId);
        verify(mockGlobalCrudService).findByNamedQuerySingleResult(ClientAttribute.BY_UPS_UUID,
                QueryParameter.with("customAttributeId", customAttributeId.toString())
                        .and("clientId", clientId).parameters());
    }

    @Test
    public void getAttributeValueForAutoMSAttribution(){
        globalCrudService().executeUpdateByNamedQuery(ClientPropertyAttributePairing.DELETE_BY_PROPERTY_ID,
                QueryParameter.with(Constants.PROPERTY_ID, PacmanWorkContextHelper.getPropertyId())
                        .parameters());

        UniqueClientPropertyAttributePairingCreator.createUniqueClientPropertyAttributePairing(globalCrudService(),
                2, 5, BUSINESS_PRACTICE);
        ClientAttributeValue attributeValue = UniqueClientAttributeValueCreator.createUniqueClientAttributeValue(
                globalCrudService(), 2, 2);

        UniqueClientPropertyAttributePairingCreator.createUniqueClientPropertyAttributePairingByClientAttributeValue(
                globalCrudService(), 2, 5, attributeValue.getId());

        Map<String, Pair<String, Integer>> msAttribution = service.getAttributeValueForAutoMSAttribution();
        assertNotNull(msAttribution);
        assertThat(msAttribution.size(), is(2));
        assertTrue(msAttribution.containsKey(REGION));
        assertTrue(msAttribution.containsKey(BUSINESS_PRACTICE));
    }
    @Test
    public void saveClientAttributesForClient() {
        CrudService mockGlobalCrudService = mock(CrudService.class);
        service.setGlobalCrudService(mockGlobalCrudService);

        Client client = new Client();
        Integer clientId = 1;
        client.setId(clientId);
        UUID clientUUID = UUID.randomUUID();
        client.setUpsClientUuid(clientUUID.toString());

        //existing attribute
        UPSCustomAttribute upsCustomAttribute = new UPSCustomAttribute();
        UUID customAttributeId = UUID.randomUUID();
        upsCustomAttribute.setCustomAttributeId(customAttributeId);
        upsCustomAttribute.setClientId(clientUUID);
        upsCustomAttribute.setPredefined(false);

        ClientAttribute clientAttribute1 = new ClientAttribute();
        clientAttribute1.setClientId(clientId);
        clientAttribute1.setUpsCustomAttributeUuid(customAttributeId.toString());

        //new attribute
        UPSCustomAttribute upsCustomAttribute2 = new UPSCustomAttribute();
        UUID customAttributeId2 = UUID.randomUUID();
        upsCustomAttribute2.setCustomAttributeId(customAttributeId2);
        upsCustomAttribute2.setClientId(clientUUID);
        upsCustomAttribute2.setPredefined(true);

        ClientAttribute clientAttribute2 = new ClientAttribute();
        clientAttribute2.setClientId(clientId);
        clientAttribute2.setUpsCustomAttributeUuid(customAttributeId2.toString());

        when(mockGlobalCrudService.findByNamedQuery(ClientAttribute.ALL, QueryParameter.with("clientId", clientId).parameters())).thenReturn(Arrays.asList(clientAttribute1));

        service.saveClientAttributesForClient(client, Arrays.asList(upsCustomAttribute, upsCustomAttribute2));
        verify(mockGlobalCrudService, times(2)).save(anyList());
        verify(mockGlobalCrudService).save(Arrays.asList(clientAttribute1));
    }
}
