package com.ideas.tetris.pacman.services.reports.rateplan;

import com.ideas.g3.data.TestClient;
import com.ideas.g3.data.TestProperty;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.reports.rateplan.dto.AllSrpDTO;
import com.ideas.tetris.platform.common.crudservice.CrudServiceBean;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.xml.schema.workcontext.v1.WorkContextType;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.anyMap;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class RatePlanServiceTest {

    @InjectMocks
    RatePlanService ratePlanService;

    @Mock
    @TenantCrudServiceBean.Qualifier
    CrudServiceBean crudServiceBean;
    private ArrayList<Object[]> resultList;
    private final LocalDate startDate = LocalDate.parse("2016-01-01");
    private final LocalDate endDate = LocalDate.parse("2016-12-01");

    @BeforeEach
    public void setUp() throws Exception {
        WorkContextType wc = new WorkContextType();
        wc.setClientId(TestClient.BSTN.getId());
        wc.setClientCode(TestClient.BSTN.name());
        wc.setPropertyId(TestProperty.H1.getId());
        wc.setPropertyCode(TestProperty.H1.name());
        wc.setUserId("99");
        wc.setUserRole("role");
        PacmanWorkContextHelper.setWorkContext(wc);
        resultList = new ArrayList<>();
    }

    @Test
    public void testGetTotalSRPDetails() {
        getResultList(resultList, new BigDecimal("13620.00000"));
        QueryParameter queryParameters = QueryParameter.with("property_id", TestProperty.H1.getId())
                .and("start_date", new java.sql.Date(startDate.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli()))
                .and("end_date", new java.sql.Date(endDate.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli()));
        when(crudServiceBean.<Object[]>findByNativeQuery("select * from dbo.ufn_get_all_srp_by_ms_and_fg_for_date_range(:property_id, :start_date, :end_date) order by srp, Forecast_Group_Name, Mkt_Seg_Name",
                queryParameters.parameters())).thenReturn(resultList);

        List<AllSrpDTO> totalSRPDetails = ratePlanService.getTotalSRPDetails(startDate, endDate);
        assertFalse(totalSRPDetails.isEmpty());
        Mockito.verify(crudServiceBean).findByNativeQuery(anyString(), anyMap());
    }

    @Test
    public void testGetTotalSRPDetailsErrorWithEmptyList() {
        getResultList(resultList, 13620.00000);
        QueryParameter queryParameters = QueryParameter.with("property_id", TestProperty.H1.getId())
                .and("start_date", new java.sql.Date(startDate.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli()))
                .and("end_date", new java.sql.Date(endDate.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli()));
        when(crudServiceBean.<Object[]>findByNativeQuery("select * from dbo.ufn_get_all_srp_by_ms_and_fg_for_date_range(:property_id, :start_date, :end_date) order by srp, Forecast_Group_Name, Mkt_Seg_Name",
                queryParameters.parameters())).thenReturn(resultList);

        List<AllSrpDTO> totalSRPDetails = ratePlanService.getTotalSRPDetails(startDate, endDate);
        assertTrue(totalSRPDetails.isEmpty());
        Mockito.verify(crudServiceBean).findByNativeQuery(anyString(), anyMap());
    }

    private void getResultList(List<Object[]> resultList, Object revenue) {
        Object[] rows = {"CHHCCE", 20, 213, revenue, 63.94366, 22, "GT", "FG_NOFCST_WASH_NSB_1", "Group"};
        resultList.add(rows);
    }
}