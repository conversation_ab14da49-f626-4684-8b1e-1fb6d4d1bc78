package com.ideas.tetris.pacman.services.functionspace.configuration.service;

import com.ideas.g3.test.AbstractG3JupiterTest;
import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.configparams.IntegrationConfigParamName;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomType;
import com.ideas.tetris.pacman.services.accommodation.service.AccommodationService;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.TenantStatusEnum;
import com.ideas.tetris.pacman.services.configautomation.dto.GuestRoomTypeToRMSRoomTypeDTO;
import com.ideas.tetris.pacman.services.configautomation.dto.SalesAndCateringMSToRMSMarketSegmentDTO;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.eventaggregator.SyncEvent;
import com.ideas.tetris.pacman.services.eventaggregator.SyncEventAggregatorService;
import com.ideas.tetris.pacman.services.functionspace.activity.entity.FunctionSpaceForecastOverride;
import com.ideas.tetris.pacman.services.functionspace.configuration.entity.*;
import com.ideas.tetris.pacman.services.functionspace.dto.FunctionSpaceLimitsDto;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.entity.DayOfWeek;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import com.ideas.tetris.platform.services.daoandentities.entity.Status;
import org.fest.assertions.api.Assertions;
import org.joda.time.LocalDate;
import org.joda.time.LocalTime;
import org.junit.jupiter.api.Test;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.math.BigDecimal;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@MockitoSettings(strictness = Strictness.LENIENT)
public class FunctionSpaceConfigurationServiceTest extends AbstractG3JupiterTest {
    @Mock
    private CrudService tenantCrudService;
    @Mock
    private SyncEventAggregatorService syncService;
    @Mock
    private DateService dateService;
    @Mock
    private AccommodationService accommodationService;
    @Mock
    private PacmanConfigParamsService pacmanConfigParamsService;
    @InjectMocks
    private FunctionSpaceConfigurationService service;
    @Captor
    private ArgumentCaptor<List<FunctionSpaceGuestRoomCategory>> guestRoomCategoryListCaptor;

    @Test
    public void saveDayPart() throws Exception {
        List<FunctionSpaceDayPart> dayParts = new ArrayList<>();
        FunctionSpaceDayPart dayPart = FunctionSpaceObjectMother.buildDayPart();
        dayParts.add(dayPart);

        Mockito.when(tenantCrudService.save(Mockito.eq(dayPart))).thenReturn(new FunctionSpaceDayPart());

        service.saveDayParts(dayParts);

        Mockito.verify(tenantCrudService).save(Mockito.eq(dayPart));
        Mockito.verify(syncService).registerSyncEvent(Mockito.eq(SyncEvent.FUNCTION_SPACE_CONFIG_CHANGED));
    }

    @Test
    public void saveDayPart_Delete() throws Exception {
        FunctionSpaceConfigurationService spyService = Mockito.spy(service);

        List<FunctionSpaceDayPart> dayParts = new ArrayList<>();
        FunctionSpaceDayPart dayPart = FunctionSpaceObjectMother.buildDayPart();
        dayPart.setDelete(true);
        dayPart.setStatus(Status.INACTIVE);
        dayParts.add(dayPart);

        Mockito.when(dateService.getCaughtUpDate()).thenReturn(new LocalDate(2015, 1, 1).toDate());
        Mockito.when(tenantCrudService.save(Mockito.eq(dayPart))).thenReturn(new FunctionSpaceDayPart());

        spyService.saveDayParts(dayParts);

        Mockito.verify(tenantCrudService).save(Mockito.eq(dayPart));
        Mockito.verify(spyService).deleteForecastOverrideWhenDayPartHasBeenDeleted(Mockito.eq(dayPart));
        Mockito.verify(syncService).registerSyncEvent(Mockito.eq(SyncEvent.FUNCTION_SPACE_CONFIG_CHANGED));
    }

    @Test
    public void saveDayPart_ForExcludedDayPart() throws Exception {
        FunctionSpaceConfigurationService spyService = Mockito.spy(service);

        List<FunctionSpaceDayPart> dayParts = new ArrayList<>();
        FunctionSpaceDayPart dayPart = FunctionSpaceObjectMother.buildDayPart();
        dayPart.setDelete(false);
        dayPart.setHasBeenExcluded(true);
        dayParts.add(dayPart);

        Mockito.when(dateService.getCaughtUpDate()).thenReturn(new LocalDate(2015, 1, 1).toDate());
        Mockito.when(tenantCrudService.save(Mockito.eq(dayPart))).thenReturn(new FunctionSpaceDayPart());

        spyService.saveDayParts(dayParts);

        Mockito.verify(tenantCrudService).save(Mockito.eq(dayPart));
        Mockito.verify(spyService).deleteForecastOverrideWhenDayPartHasBeenDeleted(Mockito.eq(dayPart));
        Mockito.verify(syncService).registerSyncEvent(Mockito.eq(SyncEvent.FUNCTION_SPACE_CONFIG_CHANGED));
    }

    @Test
    public void deleteForecastOverrideWhenDayPartHasBeenDeleted() throws Exception {
        FunctionSpaceConfigurationService spyService = Mockito.spy(service);

        FunctionSpaceDayPart dayPart = FunctionSpaceObjectMother.buildDayPart();
        dayPart.setId(5);
        dayPart.setStatus(Status.INACTIVE);

        FunctionSpaceForecastOverride forecastOverride = FunctionSpaceObjectMother.buildFunctionSpaceForecastOverride();
        forecastOverride.setPropertyId(5);

        Mockito.doReturn(Collections.singletonList(forecastOverride)).when(spyService).findAllForecastOverridesForDayPart(dayPart);
        Mockito.when(tenantCrudService.save(forecastOverride)).thenReturn(forecastOverride);

        spyService.deleteForecastOverrideWhenDayPartHasBeenDeleted(dayPart);

        Mockito.verify(spyService).findAllForecastOverridesForDayPart(Mockito.eq(dayPart));
        Mockito.verify(tenantCrudService).save(Mockito.eq(forecastOverride));
    }

    @Test
    public void findAllForecastOverridesForDayPart() throws Exception {
        FunctionSpaceDayPart dayPart = FunctionSpaceObjectMother.buildDayPart();
        dayPart.setId(5);
        dayPart.setStatus(Status.INACTIVE);

        FunctionSpaceForecastOverride forecastOverride = FunctionSpaceObjectMother.buildFunctionSpaceForecastOverride();
        forecastOverride.setPropertyId(5);

        Map<String, Object> overrideParams = new HashMap<>();
        overrideParams.put("propertyId", PacmanWorkContextHelper.getPropertyId());
        overrideParams.put("occupancyDate", new LocalDate(2015, 1, 1));
        overrideParams.put("dayPart", dayPart);

        Mockito.when(dateService.getCaughtUpDate()).thenReturn(new LocalDate(2015, 1, 1).toDate());
        Mockito.when(tenantCrudService.findByNamedQuery(FunctionSpaceForecastOverride.FIND_ACTIVE_OVERRIDES_BY_DAY_PART,
                overrideParams)).thenReturn(Collections.singletonList(forecastOverride));

        service.findAllForecastOverridesForDayPart(dayPart);

        Mockito.verify(tenantCrudService).findByNamedQuery(
                FunctionSpaceForecastOverride.FIND_ACTIVE_OVERRIDES_BY_DAY_PART, overrideParams);
    }

    @Test
    public void saveDayPart_updateAssociationPartPartToNegativeOne() throws Exception {
        List<FunctionSpaceDayPart> dayParts = new ArrayList<>();

        LocalTime beginTime = new LocalTime(8, 0, 0);
        LocalTime endTime = new LocalTime(12, 0, 0);

        FunctionSpaceDayPart dayPart = FunctionSpaceObjectMother.buildDayPart(beginTime, endTime);
        dayPart.setAssociation(0);

        dayParts.add(dayPart);

        Mockito.when(tenantCrudService.save(Mockito.eq(dayPart))).thenReturn(dayPart);

        service.saveDayParts(dayParts);

        assertTrue(dayPart.getAssociation() == -1);
        Mockito.verify(tenantCrudService).save(Mockito.eq(dayPart));
        Mockito.verify(syncService).registerSyncEvent(Mockito.eq(SyncEvent.FUNCTION_SPACE_CONFIG_CHANGED));
    }

    @Test
    public void saveDayPart_updateAssociationPartPartToZero() throws Exception {
        List<FunctionSpaceDayPart> dayParts = new ArrayList<>();

        LocalTime beginTime = new LocalTime(18, 0, 0);
        LocalTime endTime = new LocalTime(0, 0, 0);

        FunctionSpaceDayPart dayPart = FunctionSpaceObjectMother.buildDayPart(beginTime, endTime);
        dayPart.setAssociation(-1);

        dayParts.add(dayPart);

        Mockito.when(tenantCrudService.save(Mockito.eq(dayPart))).thenReturn(dayPart);

        service.saveDayParts(dayParts);

        assertTrue(dayPart.getAssociation() == 0);
        Mockito.verify(tenantCrudService).save(Mockito.eq(dayPart));
        Mockito.verify(syncService).registerSyncEvent(Mockito.eq(SyncEvent.FUNCTION_SPACE_CONFIG_CHANGED));
    }

    @Test
    public void saveDayPart_updateAssociationPartToZeroSpans4pmThreshold() throws Exception {
        List<FunctionSpaceDayPart> dayParts = new ArrayList<>();

        LocalTime beginTime = new LocalTime(15, 0, 0);
        LocalTime endTime = new LocalTime(20, 0, 0);

        FunctionSpaceDayPart dayPart = FunctionSpaceObjectMother.buildDayPart(beginTime, endTime);
        dayPart.setAssociation(0);

        dayParts.add(dayPart);

        Mockito.when(tenantCrudService.save(Mockito.eq(dayPart))).thenReturn(dayPart);

        service.saveDayParts(dayParts);

        assertTrue(dayPart.getAssociation() == -1);
        Mockito.verify(tenantCrudService).save(Mockito.eq(dayPart));
        Mockito.verify(syncService).registerSyncEvent(Mockito.eq(SyncEvent.FUNCTION_SPACE_CONFIG_CHANGED));
    }

    @Test
    public void saveDayPart_updateAssociationPartToZeroWithBeginTimeOf4pm() throws Exception {
        List<FunctionSpaceDayPart> dayParts = new ArrayList<>();

        LocalTime beginTime = new LocalTime(16, 0, 0);
        LocalTime endTime = new LocalTime(22, 0, 0);

        FunctionSpaceDayPart dayPart = FunctionSpaceObjectMother.buildDayPart(beginTime, endTime);
        dayPart.setAssociation(-1);

        dayParts.add(dayPart);

        Mockito.when(tenantCrudService.save(Mockito.eq(dayPart))).thenReturn(dayPart);

        service.saveDayParts(dayParts);

        assertTrue(dayPart.getAssociation() == 0);
        Mockito.verify(tenantCrudService).save(Mockito.eq(dayPart));
        Mockito.verify(syncService).registerSyncEvent(Mockito.eq(SyncEvent.FUNCTION_SPACE_CONFIG_CHANGED));
    }

    @SuppressWarnings({"rawtypes", "unchecked"})
    @Test
    public void getDayPart() throws Exception {
        List<FunctionSpaceDayPart> dayParts = new ArrayList<>();

        FunctionSpaceDayPart dayPart = FunctionSpaceObjectMother.buildDayPart();
        Map params = new HashMap();
        params.put(FunctionSpaceDayPart.FIND_BY_PROPERTY_ID, dayPart.getPropertyId());
        dayParts.add(dayPart);

        Mockito.when(tenantCrudService.findByNamedQuery(FunctionSpaceDayPart.FIND_BY_PROPERTY_ID, params)).thenReturn(dayParts);

        service.getDayParts();

        Mockito.verify(tenantCrudService).findByNamedQuery(Mockito.eq(FunctionSpaceDayPart.FIND_BY_PROPERTY_ID), Mockito.anyMap());
    }

    @Test
    public void getFunctionSpaceDefaultTimeTest() {
        FunctionSpaceFunctionRoomDefaultTime defaultTime = new FunctionSpaceFunctionRoomDefaultTime();
        defaultTime.setStartTime(java.time.LocalTime.of(8, 00));
        defaultTime.setEndTime(java.time.LocalTime.of(17, 00));
        when(tenantCrudService.findByNamedQuery(FunctionSpaceFunctionRoomDefaultTime.FIND_DEFAULT_TIME)).thenReturn(Arrays.asList(defaultTime));
        FunctionSpaceFunctionRoomDefaultTime result = service.getFunctionSpaceDefaultTime();
        assertEquals(java.time.LocalTime.of(8, 00), result.getStartTime());
        assertEquals(java.time.LocalTime.of(17, 00), result.getEndTime());
    }

    @Test
    public void getCombinationFunctionRooms() throws Exception {
        List<FunctionSpaceCombinationFunctionRoom> combinationRooms = Collections.singletonList(FunctionSpaceObjectMother.buildFunctionSpaceCombinationFunctionRoom("CombinationRoom"));

        Mockito.when(tenantCrudService.<FunctionSpaceCombinationFunctionRoom>findByNamedQuery(FunctionSpaceCombinationFunctionRoom.FIND_COMBINATION_ROOMS_BY_PROPERTY,
                        QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters()))
                .thenReturn(combinationRooms);

        List<FunctionSpaceCombinationFunctionRoom> retVal = service.getCombinationFunctionRooms();
        assertEquals(combinationRooms, retVal);

        Mockito.verify(tenantCrudService).findByNamedQuery(FunctionSpaceCombinationFunctionRoom.FIND_COMBINATION_ROOMS_BY_PROPERTY,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters());
    }

    @Test
    public void getIndivisibleFunctionRooms() throws Exception {
        List<FunctionSpaceFunctionRoom> functionRooms = Collections.singletonList(FunctionSpaceObjectMother
                .buildFunctionSpaceFunctionRoom());

        Mockito.when(tenantCrudService.<FunctionSpaceFunctionRoom>findByNamedQuery(FunctionSpaceFunctionRoom.FIND_NON_COMBINATION_ROOMS_BY_PROPERTY,
                        QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters()))
                .thenReturn(functionRooms);

        List<FunctionSpaceFunctionRoom> retVal = service.getIndivisibleFunctionRooms();
        assertEquals(functionRooms, retVal);

        Mockito.verify(tenantCrudService).findByNamedQuery(FunctionSpaceFunctionRoom.FIND_NON_COMBINATION_ROOMS_BY_PROPERTY,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters());
    }

    @Test
    public void saveFunctionRooms() throws Exception {
        List<FunctionSpaceFunctionRoom> functionRooms = Collections.singletonList(FunctionSpaceObjectMother.buildFunctionSpaceFunctionRoom());

        Mockito.when(tenantCrudService.<FunctionSpaceCombinationFunctionRoom>findByNamedQuery(FunctionSpaceCombinationFunctionRoom.FIND_COMBINATION_ROOMS_BY_PROPERTY,
                        QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters()))
                .thenReturn(new ArrayList<>());
        Mockito.when(tenantCrudService.save(functionRooms)).thenReturn(functionRooms);

        service.saveFunctionRooms(functionRooms);

        Mockito.verify(tenantCrudService).save(functionRooms);
        Mockito.verify(syncService).registerSyncEvent(Mockito.eq(SyncEvent.FUNCTION_SPACE_CONFIG_CHANGED));
    }

    @Test
    public void saveFunctionRooms_UpdateCombinationRoomPricingForTrue() throws Exception {
        FunctionSpaceCombinationFunctionRoom comboRoom = FunctionSpaceObjectMother.buildFunctionSpaceCombinationFunctionRoom("Combo Room");
        comboRoom.setIncludeForPricing(false);
        List<FunctionSpaceFunctionRoom> indivisibleRooms = comboRoom.getIndivisibleFunctionRoomParts();
        indivisibleRooms.get(0).setIncludeForPricing(false);
        indivisibleRooms.get(1).setIncludeForPricing(true);

        Mockito.when(tenantCrudService.findByNamedQuery(FunctionSpaceCombinationFunctionRoom.FIND_COMBINATION_ROOMS_BY_PROPERTY,
                        QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters()))
                .thenReturn(Collections.singletonList(comboRoom));

        Mockito.when(tenantCrudService.save(indivisibleRooms)).thenReturn(indivisibleRooms);

        service.saveFunctionRooms(indivisibleRooms);

        assertTrue(comboRoom.isIncludeForPricing());

        Mockito.verify(tenantCrudService).save(Collections.singletonList(comboRoom));
        Mockito.verify(tenantCrudService).save(indivisibleRooms);
        Mockito.verify(syncService).registerSyncEvent(Mockito.eq(SyncEvent.FUNCTION_SPACE_CONFIG_CHANGED));
    }

    @Test
    public void saveFunctionRooms_UpdateSingleComboRoomForPricingWhenThereAreMultipleComboRooms() throws Exception {
        List<FunctionSpaceCombinationFunctionRoom> allComboRooms = new ArrayList<>();
        FunctionSpaceCombinationFunctionRoom comboRoom1 = FunctionSpaceObjectMother.buildFunctionSpaceCombinationFunctionRoom("Red Combo Room");
        FunctionSpaceCombinationFunctionRoom comboRoom2 = FunctionSpaceObjectMother.buildFunctionSpaceCombinationFunctionRoom("Blue Combo Room");
        comboRoom1.setIncludeForPricing(false);
        comboRoom2.setIncludeForPricing(false);

        allComboRooms.add(comboRoom1);
        allComboRooms.add(comboRoom2);

        List<FunctionSpaceFunctionRoom> allIndivisibleRooms = new ArrayList<>();
        List<FunctionSpaceFunctionRoom> indivisibleRoomsForCombo1 = comboRoom1.getIndivisibleFunctionRoomParts();
        indivisibleRoomsForCombo1.get(0).setIncludeForPricing(false);
        indivisibleRoomsForCombo1.get(1).setIncludeForPricing(true);

        List<FunctionSpaceFunctionRoom> indivisableRoomsForCombo2 = comboRoom2.getIndivisibleFunctionRoomParts();
        indivisableRoomsForCombo2.get(0).setIncludeForPricing(false);
        indivisableRoomsForCombo2.get(1).setIncludeForPricing(false);

        allIndivisibleRooms.addAll(indivisibleRoomsForCombo1);
        allIndivisibleRooms.addAll(indivisableRoomsForCombo2);

        Mockito.when(tenantCrudService.<FunctionSpaceCombinationFunctionRoom>findByNamedQuery(FunctionSpaceCombinationFunctionRoom.FIND_COMBINATION_ROOMS_BY_PROPERTY,
                        QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters()))
                .thenReturn(allComboRooms);

        Mockito.when(tenantCrudService.save(indivisibleRoomsForCombo1)).thenReturn(indivisibleRoomsForCombo1);

        service.saveFunctionRooms(allIndivisibleRooms);

        assertTrue(comboRoom1.isIncludeForPricing());
        assertFalse(comboRoom2.isIncludeForPricing());

        Mockito.verify(tenantCrudService).save(allComboRooms);
        Mockito.verify(tenantCrudService).save(allIndivisibleRooms);
        Mockito.verify(syncService).registerSyncEvent(Mockito.eq(SyncEvent.FUNCTION_SPACE_CONFIG_CHANGED));
    }

    @Test
    public void saveFunctionRooms_ExistingComboRoomIsNotUpdatedWhenNonComboRoomPartIsSetToIncludePricing() {
        // setup combo room with parts that are included for pricing
        FunctionSpaceCombinationFunctionRoom comboRoom = FunctionSpaceObjectMother
                .buildFunctionSpaceCombinationFunctionRoom("Combo Room");
        comboRoom.setIncludeForPricing(true);
        List<FunctionSpaceFunctionRoom> indivisableRooms = comboRoom.getIndivisibleFunctionRoomParts();
        indivisableRooms.get(0).setIncludeForPricing(true);
        indivisableRooms.get(1).setIncludeForPricing(true);

        // simulate a different room being updated to include pricing that is not part of the combo room
        FunctionSpaceFunctionRoom nonComboRoomPart = FunctionSpaceObjectMother.buildFunctionSpaceFunctionRoom("Red");
        nonComboRoomPart.setIncludeForPricing(true);

        Mockito.when(tenantCrudService.findByNamedQuery(FunctionSpaceCombinationFunctionRoom.FIND_COMBINATION_ROOMS_BY_PROPERTY,
                        QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters()))
                .thenReturn(Collections.singletonList(comboRoom));

        Mockito.when(tenantCrudService.save(Collections.singletonList(nonComboRoomPart))).thenReturn(Collections.singletonList(nonComboRoomPart));

        // save the non combo room part
        service.saveFunctionRooms(Collections.singletonList(nonComboRoomPart));

        // the existing combo room should still be true since the indivisible room being updated is not one of its parts
        assertTrue(comboRoom.isIncludeForPricing());

        Mockito.verify(tenantCrudService).save(Collections.singletonList(nonComboRoomPart));
        Mockito.verify(syncService).registerSyncEvent(Mockito.eq(SyncEvent.FUNCTION_SPACE_CONFIG_CHANGED));
    }

    @Test
    public void saveFunctionRooms_ExistingComboRoomIsUpdatedWhenNonComboRoomPartAreSetToIncludePricing() {
        // setup combo room with parts that are included for pricing
        FunctionSpaceCombinationFunctionRoom comboRoom = FunctionSpaceObjectMother
                .buildFunctionSpaceCombinationFunctionRoom("Combo Room");
        comboRoom.setIncludeForPricing(true);
        List<FunctionSpaceFunctionRoom> indivisibleRooms = comboRoom.getIndivisibleFunctionRoomParts();
        // set all combo room parts include for pricing to false
        indivisibleRooms.get(0).setIncludeForPricing(false);
        indivisibleRooms.get(1).setIncludeForPricing(false);

        Mockito.when(tenantCrudService.findByNamedQuery(FunctionSpaceCombinationFunctionRoom.FIND_COMBINATION_ROOMS_BY_PROPERTY,
                        QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters()))
                .thenReturn(Collections.singletonList(comboRoom));

        Mockito.when(tenantCrudService.save(indivisibleRooms)).thenReturn(indivisibleRooms);

        // save the combo room parts
        service.saveFunctionRooms(indivisibleRooms);

        // the existing combo room should no longer be included for pricing, since none of its parts are
        assertFalse(comboRoom.isIncludeForPricing());

        Mockito.verify(tenantCrudService).save(indivisibleRooms);
        Mockito.verify(syncService).registerSyncEvent(Mockito.eq(SyncEvent.FUNCTION_SPACE_CONFIG_CHANGED));
    }

    @Test
    public void getFunctionSpaceEventTypes() throws Exception {
        List<FunctionSpaceEventType> functionSpaceEventTypes = Collections.singletonList(FunctionSpaceObjectMother.buildFunctionSpaceEventType("OutOfService", "Out of Service"));

        Mockito.when(tenantCrudService.<FunctionSpaceEventType>findByNamedQuery(FunctionSpaceEventType.FIND_BY_PROPERTY,
                        QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters()))
                .thenReturn(functionSpaceEventTypes);

        List<FunctionSpaceEventType> retVal = service.getFunctionSpaceOutOfServiceEventTypes();
        assertEquals(functionSpaceEventTypes, retVal);

        Mockito.verify(tenantCrudService).findByNamedQuery(FunctionSpaceEventType.FIND_BY_PROPERTY,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters());
    }

    @Test
    public void saveEventTypes() throws Exception {
        List<FunctionSpaceEventType> functionSpaceEventTypes = Collections.singletonList(FunctionSpaceObjectMother.buildFunctionSpaceEventType("OutOfService", "Out of Service"));

        Mockito.when(tenantCrudService.save(functionSpaceEventTypes)).thenReturn(functionSpaceEventTypes);

        service.saveOutOfServiceEventTypes(functionSpaceEventTypes);

        Mockito.verify(tenantCrudService).save(functionSpaceEventTypes);
        Mockito.verify(syncService).registerSyncEvent(Mockito.eq(SyncEvent.FUNCTION_SPACE_CONFIG_CHANGED));
    }

    @Test
    public void saveForecastLevel() throws Exception {
        FunctionSpaceForecastLevel forecastLevel = FunctionSpaceObjectMother.buildFunctionSpaceForecastLevel();

        Mockito.when(tenantCrudService.save(forecastLevel)).thenReturn(forecastLevel);

        service.saveForecestLevel(forecastLevel);

        Mockito.verify(tenantCrudService).save(forecastLevel);
    }

    @Test
    public void getForecastLevel() throws Exception {
        FunctionSpaceForecastLevel forecastLevel = FunctionSpaceObjectMother.buildFunctionSpaceForecastLevel();

        Mockito.when(
                        tenantCrudService.findByNamedQuerySingleResult(FunctionSpaceForecastLevel.FIND_BY_PROPERTY_ID,
                                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters()))
                .thenReturn(forecastLevel);

        FunctionSpaceForecastLevel retVal = service.getForecastLevel();

        assertEquals(forecastLevel, retVal);

        Mockito.verify(tenantCrudService).findByNamedQuerySingleResult(FunctionSpaceForecastLevel.FIND_BY_PROPERTY_ID,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters());
    }

    @Test
    public void saveMarketSegment() throws Exception {
        FunctionSpaceMarketSegment marketSegment = FunctionSpaceObjectMother.buildFunctionSpaceMarketSegment("New Market Segment", 1, FunctionSpaceObjectMother.DEFAULT_PROPERTY_ID);

        Mockito.when(tenantCrudService.save(marketSegment)).thenReturn(marketSegment);

        service.saveMarketSegment(marketSegment);

        Mockito.verify(tenantCrudService).save(marketSegment);

    }

    @Test
    public void getMarketSegments() throws Exception {
        List<FunctionSpaceMarketSegment> marketSegments = Collections.singletonList(FunctionSpaceObjectMother.buildFunctionSpaceMarketSegment("New Market Segment", 1, FunctionSpaceObjectMother.DEFAULT_PROPERTY_ID));

        Mockito.when(tenantCrudService.<FunctionSpaceMarketSegment>findByNamedQuery(FunctionSpaceMarketSegment.FIND_BY_PROPERTY_ID,
                        QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters()))
                .thenReturn(marketSegments);

        List<FunctionSpaceMarketSegment> retVal = service.getMarketSegments();

        assertEquals(marketSegments, retVal);

        Mockito.verify(tenantCrudService).findByNamedQuery(FunctionSpaceMarketSegment.FIND_BY_PROPERTY_ID,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters());
    }

    @Test
    public void testClearMapping() {
        FunctionSpaceMarketSegment functionSpaceMarketSegment = FunctionSpaceObjectMother
                .buildFunctionSpaceMarketSegment("New Market Segment", 1, FunctionSpaceObjectMother.DEFAULT_PROPERTY_ID);
        functionSpaceMarketSegment.setMarketSegmentId(25);
        List<FunctionSpaceMarketSegment> marketSegments = Collections.singletonList(functionSpaceMarketSegment);

        Mockito.when(tenantCrudService.<FunctionSpaceMarketSegment>findByNamedQuery(FunctionSpaceMarketSegment.FIND_BY_PROPERTY_ID,
                        QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters()))
                .thenReturn(marketSegments);

        Mockito.when(tenantCrudService.save(functionSpaceMarketSegment)).thenReturn(functionSpaceMarketSegment);

        service.clearMarketSegmentMapping();

        assertNull(functionSpaceMarketSegment.getMarketSegmentId());
        Mockito.verify(tenantCrudService).save(functionSpaceMarketSegment);
    }

    @Test
    public void getGuestRoomCategories() throws Exception {
        List<FunctionSpaceGuestRoomCategory> guestRoomCategories = Collections.singletonList(FunctionSpaceObjectMother.buildFunctioSpaceGuestRoomCategory());

        Mockito.when(tenantCrudService.<FunctionSpaceGuestRoomCategory>findByNamedQuery(FunctionSpaceGuestRoomCategory.FIND_BY_PROPERTY_ID, QueryParameter
                .with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters())).thenReturn(
                guestRoomCategories);

        List<FunctionSpaceGuestRoomCategory> retVal = service.getGuestRoomCategories();

        assertEquals(guestRoomCategories, retVal);

        Mockito.verify(tenantCrudService).findByNamedQuery(FunctionSpaceGuestRoomCategory.FIND_BY_PROPERTY_ID,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters());
    }

    @Test
    public void getFunctionSpaceData() throws Exception {
        List<FunctionSpaceStatus> functionSpaceStatuses = Collections.singletonList(new FunctionSpaceStatus());

        Mockito.when(tenantCrudService.<FunctionSpaceStatus>findByNamedQuery(FunctionSpaceStatus.FIND_BY_PROPERTY_AND_ORDER_BY_STATUS, QueryParameter
                .with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters())).thenReturn(
                functionSpaceStatuses);

        List<FunctionSpaceStatus> retVal = service.getFunctionSpaceStatuses();

        assertEquals(functionSpaceStatuses, retVal);

        Mockito.verify(tenantCrudService).findByNamedQuery(FunctionSpaceStatus.FIND_BY_PROPERTY_AND_ORDER_BY_STATUS,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters());
    }

    @Test
    public void saveFunctionSpaceStatuses() throws Exception {
        List<FunctionSpaceStatus> functionSpaceStatuses = Collections.singletonList(new FunctionSpaceStatus());

        Mockito.when(tenantCrudService.save(functionSpaceStatuses)).thenReturn(functionSpaceStatuses);

        service.saveFunctionSpaceStatuses(functionSpaceStatuses);

        Mockito.verify(tenantCrudService).save(functionSpaceStatuses);
    }

    @Test
    public void saveGuestRoomCategory() throws Exception {
        FunctionSpaceGuestRoomCategory guestRoomCategory = FunctionSpaceObjectMother
                .buildFunctioSpaceGuestRoomCategory();

        Mockito.when(tenantCrudService.save(guestRoomCategory)).thenReturn(guestRoomCategory);

        service.saveGuestRoomCategory(guestRoomCategory);

        Mockito.verify(tenantCrudService).save(guestRoomCategory);
    }

    @Test
    public void saveGuestRoomCategories() throws Exception {
        List<FunctionSpaceGuestRoomCategory> guestRoomCategories = Collections.singletonList(FunctionSpaceObjectMother
                .buildFunctioSpaceGuestRoomCategory());

        Mockito.when(tenantCrudService.save(guestRoomCategories.get(0))).thenReturn(guestRoomCategories.get(0));

        service.saveGuestRoomCategories(guestRoomCategories);

        Mockito.verify(tenantCrudService).save(guestRoomCategories.get(0));
        Mockito.verify(syncService).registerSyncEvent(Mockito.eq(SyncEvent.FUNCTION_SPACE_CONFIG_CHANGED));
    }

    @Test
    public void syncGuestRoomTypeMapping_noAssignedAccomTypesInRMS() {
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.ROOM_TYPE_RECODING_UI_ENABLED)).thenReturn(false);
        when(accommodationService.getAllAssignedAccomTypes()).thenReturn(Collections.EMPTY_LIST);

        when(service.getGuestRoomCategories()).thenReturn(Arrays.asList(FunctionSpaceObjectMother.buildFunctioSpaceGuestRoomCategory()));

        service.syncGuestRoomTypeMapping();

        verify(tenantCrudService, never()).save(anyList());
        verify(syncService, never()).registerSyncEvent(SyncEvent.FUNCTION_SPACE_CONFIG_CHANGED);
    }

    @Test
    public void getAssignedAccomTypes_filterDiscontinuedRoomTypes() {
        //Given
        AccomType accomType = getTestAccomType(1, "Quad");
        AccomType accomTypeDiscontinued = getTestAccomType(2, "Quad_Dis");
        accomTypeDiscontinued.setDisplayStatusId(2);
        when(accommodationService.getAllAssignedAccomTypes()).thenReturn(Arrays.asList(accomType, accomTypeDiscontinued));
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.ROOM_TYPE_RECODING_UI_ENABLED)).thenReturn(true);
        //When
        final List<AccomType> assignedAccomTypes = service.getAssignedAccomTypes();
        //Then
        assertEquals(1, assignedAccomTypes.size());
        assertEquals(accomType, assignedAccomTypes.get(0));
    }

    private AccomType getTestAccomType(int id, String name) {
        AccomType accomTypeDiscontinued = new AccomType();
        accomTypeDiscontinued.setId(id);
        accomTypeDiscontinued.setName(name);
        return accomTypeDiscontinued;
    }


    @Test
    public void getRevenueGroups() throws Exception {
        List<FunctionSpaceRevenueGroup> revenueGroups = Collections.singletonList(FunctionSpaceObjectMother.buildFunctionSpaceRevenueGroup("GR", 1));

        Mockito.when(tenantCrudService.<FunctionSpaceRevenueGroup>findByNamedQuery(FunctionSpaceRevenueGroup.FIND_ACTIVE_REVENUE_GROUPS_BY_PROPERTY_ID,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters()))
                .thenReturn(revenueGroups);

        List<FunctionSpaceRevenueGroup> retVal = service.getActiveRevenueGroups();

        assertEquals(revenueGroups, retVal);

        Mockito.verify(tenantCrudService).findByNamedQuery(FunctionSpaceRevenueGroup.FIND_ACTIVE_REVENUE_GROUPS_BY_PROPERTY_ID,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters());
    }

    @Test
    public void saveRevenueGroup() throws Exception {
        FunctionSpaceRevenueGroup revenueGroup = FunctionSpaceObjectMother.buildFunctionSpaceRevenueGroup("GR", 1);

        Mockito.when(tenantCrudService.save(revenueGroup)).thenReturn(revenueGroup);

        service.saveRevenueGroup(revenueGroup);

        Mockito.verify(tenantCrudService).save(revenueGroup);
    }

    @Test
    public void getTotalSquareFeetOfAllIndivisibleRooms_DifferentPriceTiers() throws Exception {
        FunctionSpaceConfigurationService spy = Mockito.spy(service);
        List<FunctionSpaceFunctionRoom> indivisibleRooms = new ArrayList<>();

        FunctionSpaceFunctionRoom blueRoom = FunctionSpaceObjectMother.buildFunctionSpaceFunctionRoom("Blue");
        FunctionSpaceFunctionRoom redRoom = FunctionSpaceObjectMother.buildFunctionSpaceFunctionRoom("Red");
        // set this room to price tier 3
        redRoom.setFunctionSpaceFunctionRoomPriceTier(FunctionSpaceFunctionRoomPriceTier.TIER_3);

        indivisibleRooms.add(blueRoom);
        indivisibleRooms.add(redRoom);

        BigDecimal expectedTotalPriceTier1 = blueRoom.getAreaSqFeet();
        BigDecimal expectedTotalPriceTier3 = redRoom.getAreaSqFeet();

        Mockito.doReturn(indivisibleRooms).when(spy).getAllActiveRoomsIncludedForPricing();

        Map<FunctionSpaceFunctionRoomPriceTier, BigDecimal> priceTierSqFeet = spy.getTotalSqFeetForFunctionRoomsPerPriceTier();

        assertTrue(priceTierSqFeet.size() == 2);

        assertEquals(expectedTotalPriceTier1, priceTierSqFeet.get(FunctionSpaceFunctionRoomPriceTier.TIER_1));
        assertEquals(expectedTotalPriceTier3, priceTierSqFeet.get(FunctionSpaceFunctionRoomPriceTier.TIER_3));
    }

    @Test
    public void getAllActiveFunctionRoomsIncludedForPricing() throws Exception {
        List<FunctionSpaceFunctionRoom> allRooms = new ArrayList<>();

        FunctionSpaceFunctionRoom indivisibleRoom = FunctionSpaceObjectMother.buildFunctionSpaceFunctionRoom("Blue");
        FunctionSpaceFunctionRoom comboRoom = FunctionSpaceObjectMother
                .buildFunctionSpaceCombinationFunctionRoom("Red");

        allRooms.add(indivisibleRoom);
        allRooms.add(comboRoom);

        Mockito.when(tenantCrudService.<FunctionSpaceFunctionRoom>findByNamedQuery(FunctionSpaceCombinationFunctionRoom.FIND_ALL_ACTIVE_ROOMS_INCLUDED_FOR_PRICING, QueryParameter
                .with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters())).thenReturn(
                allRooms);

        allRooms = service.getAllActiveRoomsIncludedForPricing();

        assertTrue(allRooms.size() == 2);

        Mockito.verify(tenantCrudService).findByNamedQuery(
                FunctionSpaceCombinationFunctionRoom.FIND_ALL_ACTIVE_ROOMS_INCLUDED_FOR_PRICING,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters());
    }

    @Test
    public void getAllTbaAndNonActiveFunctionRoomsIncludedForPricing() throws Exception {
        List<FunctionSpaceFunctionRoom> allRooms = new ArrayList<>();

        FunctionSpaceFunctionRoom tbaRoom1 = FunctionSpaceObjectMother.buildFunctionSpaceFunctionRoom("Blue");
        tbaRoom1.setIncludeForPricing(false);
        tbaRoom1.setTba(true);

        FunctionSpaceFunctionRoom tbaRoom2 = FunctionSpaceObjectMother.buildFunctionSpaceFunctionRoom("red");
        tbaRoom2.setIncludeForPricing(false);
        tbaRoom2.setTba(true);

        allRooms.add(tbaRoom1);
        allRooms.add(tbaRoom2);

        Mockito.when(tenantCrudService.<FunctionSpaceFunctionRoom>findByNamedQuery(FunctionSpaceCombinationFunctionRoom.FIND_ALL_TBA_AND_NOT_INCLUDED_ROOMS_FOR_PRICING, QueryParameter
                .with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters())).thenReturn(
                allRooms);

        allRooms = service.getAllTbaAndNonActiveRoomsIncludedForPricing();

        assertTrue(allRooms.size() == 2);

        Mockito.verify(tenantCrudService).findByNamedQuery(
                FunctionSpaceCombinationFunctionRoom.FIND_ALL_TBA_AND_NOT_INCLUDED_ROOMS_FOR_PRICING,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters());
    }

    @Test
    public void findDayPartById() throws Exception {
        FunctionSpaceDayPart dayPart = FunctionSpaceObjectMother.buildDayPart();
        dayPart.setId(1);

        Mockito.when(tenantCrudService.find(FunctionSpaceDayPart.class, 1)).thenReturn(dayPart);

        service.findDayPartById(1);

        Mockito.verify(tenantCrudService).find(Mockito.same(FunctionSpaceDayPart.class), Mockito.eq(1));
    }

    @Test
    public void findFunctionRoomById() throws Exception {
        FunctionSpaceFunctionRoom room = FunctionSpaceObjectMother.buildFunctionSpaceFunctionRoom();
        room.setId(1);

        Mockito.when(tenantCrudService.find(FunctionSpaceFunctionRoom.class, 1)).thenReturn(room);

        service.findFunctionRoomById(1);

        Mockito.verify(tenantCrudService).find(Mockito.same(FunctionSpaceFunctionRoom.class), Mockito.eq(1));
    }

    @Test
    public void getTotalDayPartHoursAvailablePerDay() throws Exception {
        FunctionSpaceConfigurationService spy = Mockito.spy(service);

        List<FunctionSpaceDayPart> dayParts = new ArrayList<>();
        FunctionSpaceDayPart dayPart = FunctionSpaceObjectMother.buildDayPart();
        dayParts.add(dayPart);
        Mockito.when(spy.getDayParts()).thenReturn(dayParts);

        BigDecimal totalHours = spy.getTotalDayPartHoursAvailablePerDay();

        assertEquals(4, totalHours.intValue());
    }

    @Test
    public void getTotalDayPartHoursAvailablePerDay_MultipleDayParts() throws Exception {
        FunctionSpaceConfigurationService spy = Mockito.spy(service);

        List<FunctionSpaceDayPart> dayParts = new ArrayList<>();
        FunctionSpaceDayPart dayPart1 = FunctionSpaceObjectMother.buildDayPart(new LocalTime(8, 0), new LocalTime(12, 0));
        FunctionSpaceDayPart dayPart2 = FunctionSpaceObjectMother.buildDayPart(new LocalTime(12, 0), new LocalTime(17, 0));
        dayParts.add(dayPart1);
        dayParts.add(dayPart2);

        Mockito.when(spy.getDayParts()).thenReturn(dayParts);

        BigDecimal totalHours = spy.getTotalDayPartHoursAvailablePerDay();

        assertEquals(9, totalHours.intValue());
    }

    @Test
    public void getAllIncludedDayParts() throws Exception {
        FunctionSpaceConfigurationService spy = Mockito.spy(service);

        List<FunctionSpaceDayPart> dayParts = new ArrayList<>();
        FunctionSpaceDayPart dayPart1 = FunctionSpaceObjectMother.buildDayPart(new LocalTime(8, 0), new LocalTime(12, 0));
        FunctionSpaceDayPart dayPart2 = FunctionSpaceObjectMother.buildDayPart(new LocalTime(12, 0), new LocalTime(17, 0));
        dayParts.add(dayPart1);
        dayParts.add(dayPart2);

        Mockito.when(spy.getDayParts()).thenReturn(dayParts);

        assertEquals(2, spy.getAllIncludedDayParts().size());
    }

    @Test
    public void getAllIncludedDayParts_WithOneExcludedDayPart() throws Exception {
        FunctionSpaceConfigurationService spy = Mockito.spy(service);

        List<FunctionSpaceDayPart> dayParts = new ArrayList<>();
        FunctionSpaceDayPart dayPart1 = FunctionSpaceObjectMother.buildDayPart(new LocalTime(8, 0),
                new LocalTime(12, 0));
        FunctionSpaceDayPart dayPart2 = FunctionSpaceObjectMother.buildDayPart(new LocalTime(12, 0), new LocalTime(17,
                0));
        dayPart2.setIncluded(false);
        dayParts.add(dayPart1);
        dayParts.add(dayPart2);

        Mockito.when(spy.getDayParts()).thenReturn(dayParts);

        dayParts = spy.getAllIncludedDayParts();

        assertEquals(1, dayParts.size());
        assertTrue(dayParts.get(0).isIncluded());
    }

    @Test
    public void clearOutOfRangeDayOfWeekValues_saturdaySunday() {
        FunctionSpaceFunctionRoomMARSeason season = new FunctionSpaceFunctionRoomMARSeason();

        // sat-sun
        LocalDate expectedStartDate = new LocalDate(2013, 1, 5);
        LocalDate expectedEndDate = new LocalDate(2013, 1, 6);
        season.setStartDate(expectedStartDate);
        season.setEndDate(expectedEndDate);

        BigDecimal expectedMarValue = BigDecimal.ONE;
        season.setSundayMAR(expectedMarValue);
        season.setMondayMAR(expectedMarValue);
        season.setTuesdayMAR(expectedMarValue);
        season.setWednesdayMAR(expectedMarValue);
        season.setThursdayMAR(expectedMarValue);
        season.setFridayMAR(expectedMarValue);
        season.setSaturdayMAR(expectedMarValue);

        service.clearOutOfRangeDayOfWeekValues(season);

        assertEquals(expectedStartDate, season.getStartDate());
        assertEquals(expectedEndDate, season.getEndDate());

        assertEquals(expectedMarValue, season.getSaturdayMAR());
        assertEquals(expectedMarValue, season.getSundayMAR());
        assertEquals(null, season.getMondayMAR());
        assertEquals(null, season.getTuesdayMAR());
        assertEquals(null, season.getWednesdayMAR());
        assertEquals(null, season.getThursdayMAR());
        assertEquals(null, season.getFridayMAR());
    }

    @Test
    public void clearOutOfRangeDayOfWeekValues_mondayToFriday() {
        FunctionSpaceFunctionRoomMARSeason season = new FunctionSpaceFunctionRoomMARSeason();

        // sat-sun
        LocalDate expectedStartDate = new LocalDate(2013, 1, 7);
        LocalDate expectedEndDate = new LocalDate(2013, 1, 11);
        season.setStartDate(expectedStartDate);
        season.setEndDate(expectedEndDate);

        BigDecimal expectedMarValue = BigDecimal.ONE;
        season.setSundayMAR(expectedMarValue);
        season.setMondayMAR(expectedMarValue);
        season.setTuesdayMAR(expectedMarValue);
        season.setWednesdayMAR(expectedMarValue);
        season.setThursdayMAR(expectedMarValue);
        season.setFridayMAR(expectedMarValue);
        season.setSaturdayMAR(expectedMarValue);

        service.clearOutOfRangeDayOfWeekValues(season);

        assertEquals(expectedStartDate, season.getStartDate());
        assertEquals(expectedEndDate, season.getEndDate());

        assertEquals(null, season.getSaturdayMAR());
        assertEquals(null, season.getSundayMAR());
        assertEquals(expectedMarValue, season.getMondayMAR());
        assertEquals(expectedMarValue, season.getTuesdayMAR());
        assertEquals(expectedMarValue, season.getWednesdayMAR());
        assertEquals(expectedMarValue, season.getThursdayMAR());
        assertEquals(expectedMarValue, season.getFridayMAR());
    }

    @Test
    public void clearOutOfRangeDayOfWeekRoomMARValues_mondayToFriday() {
        FunctionSpaceFunctionRoomMARSeason season = new FunctionSpaceFunctionRoomMARSeason();

        // sat-sun
        java.time.LocalDate expectedStartDate = java.time.LocalDate.of(2013, 1, 7);
        java.time.LocalDate expectedEndDate = java.time.LocalDate.of(2013, 1, 11);
        season.setStartDate(DateUtil.convertJavaToJodaLocalDate(expectedStartDate));
        season.setEndDate(DateUtil.convertJavaToJodaLocalDate(expectedEndDate));

        FunctionSpaceFunctionRoomRentalMARSeasonDetails roomRentalMAR = new FunctionSpaceFunctionRoomRentalMARSeasonDetails();
        roomRentalMAR.setConfigureRentalMAR(true);
        BigDecimal expectedMarValue = BigDecimal.ONE;
        roomRentalMAR.setSundayRoomRentalMAR(expectedMarValue);
        roomRentalMAR.setMondayRoomRentalMAR(expectedMarValue);
        roomRentalMAR.setTuesdayRoomRentalMAR(expectedMarValue);
        roomRentalMAR.setWednesdayRoomRentalMAR(expectedMarValue);
        roomRentalMAR.setThursdayRoomRentalMAR(expectedMarValue);
        roomRentalMAR.setFridayRoomRentalMAR(expectedMarValue);
        roomRentalMAR.setSaturdayRoomRentalMAR(expectedMarValue);
        season.setFunctionSpaceFunctionRoomRentalMARSeasonDetails(roomRentalMAR);

        service.clearOutOfRangeDayOfWeekValuesForRoomRentalMAR(season);

        assertEquals(expectedStartDate, DateUtil.convertJodaToJavaLocalDate(season.getStartDate()));
        assertEquals(expectedEndDate, DateUtil.convertJodaToJavaLocalDate(season.getEndDate()));
        assertTrue(roomRentalMAR.isConfigureRentalMAR());
        assertEquals(null, roomRentalMAR.getSaturdayRoomRentalMAR());
        assertEquals(null, roomRentalMAR.getSundayRoomRentalMAR());
        assertEquals(expectedMarValue, roomRentalMAR.getMondayRoomRentalMAR());
        assertEquals(expectedMarValue, roomRentalMAR.getTuesdayRoomRentalMAR());
        assertEquals(expectedMarValue, roomRentalMAR.getWednesdayRoomRentalMAR());
        assertEquals(expectedMarValue, roomRentalMAR.getThursdayRoomRentalMAR());
        assertEquals(expectedMarValue, roomRentalMAR.getFridayRoomRentalMAR());
    }

    @Test
    public void clearOutOfRangeDayOfWeekValues_fullRange() {
        FunctionSpaceFunctionRoomMARSeason season = new FunctionSpaceFunctionRoomMARSeason();

        // sat-sun
        LocalDate expectedStartDate = new LocalDate(2013, 1, 1);
        LocalDate expectedEndDate = new LocalDate(2013, 1, 11);
        season.setStartDate(expectedStartDate);
        season.setEndDate(expectedEndDate);

        BigDecimal expectedMarValue = BigDecimal.ONE;
        season.setSundayMAR(expectedMarValue);
        season.setMondayMAR(expectedMarValue);
        season.setTuesdayMAR(expectedMarValue);
        season.setWednesdayMAR(expectedMarValue);
        season.setThursdayMAR(expectedMarValue);
        season.setFridayMAR(expectedMarValue);
        season.setSaturdayMAR(expectedMarValue);

        service.clearOutOfRangeDayOfWeekValues(season);

        assertEquals(expectedStartDate, season.getStartDate());
        assertEquals(expectedEndDate, season.getEndDate());

        assertEquals(expectedMarValue, season.getSaturdayMAR());
        assertEquals(expectedMarValue, season.getSundayMAR());
        assertEquals(expectedMarValue, season.getMondayMAR());
        assertEquals(expectedMarValue, season.getTuesdayMAR());
        assertEquals(expectedMarValue, season.getWednesdayMAR());
        assertEquals(expectedMarValue, season.getThursdayMAR());
        assertEquals(expectedMarValue, season.getFridayMAR());
    }

    @Test
    public void nullOutUnusedDayOfWeekValuesForDateRange() {
        FunctionSpaceFunctionRoomMARSeason season = new FunctionSpaceFunctionRoomMARSeason();
        season.setSundayMAR(BigDecimal.ONE);
        season.setMondayMAR(BigDecimal.ONE);
        season.setTuesdayMAR(BigDecimal.ONE);
        season.setWednesdayMAR(BigDecimal.ONE);
        season.setThursdayMAR(BigDecimal.ONE);
        season.setFridayMAR(BigDecimal.ONE);
        season.setSaturdayMAR(BigDecimal.ONE);
        season.setStartDate(new LocalDate(2013, 9, 23));
        season.setEndDate(new LocalDate(2013, 9, 24));

        service.clearOutOfRangeDayOfWeekValues(season);
        Assertions.assertThat(season.getSundayMAR()).isNull();
        Assertions.assertThat(season.getMondayMAR()).isEqualTo(BigDecimal.ONE);
        Assertions.assertThat(season.getTuesdayMAR()).isEqualTo(BigDecimal.ONE);
        Assertions.assertThat(season.getWednesdayMAR()).isNull();
        Assertions.assertThat(season.getThursdayMAR()).isNull();
        Assertions.assertThat(season.getFridayMAR()).isNull();
        Assertions.assertThat(season.getSaturdayMAR()).isNull();
    }

    @Test
    public void getMARByPriceTierMap() throws Exception {
        FunctionSpaceConfigurationService spy = Mockito.spy(service);

        List<FunctionSpaceFunctionRoom> allRooms = new ArrayList<>();

        FunctionSpaceFunctionRoom blueRoom = FunctionSpaceObjectMother.buildFunctionSpaceFunctionRoom("Blue");
        blueRoom.setFunctionSpaceFunctionRoomLimit(FunctionSpaceObjectMother
                .buildFunctionSpaceFunctionRoomLimit(blueRoom));
        // change low/high values for Saturday
        blueRoom.getFunctionSpaceFunctionRoomLimit().setSaturdayDefaultMAR(new BigDecimal(50));
        blueRoom.getFunctionSpaceFunctionRoomLimit().setSaturdayUpperLimit(new BigDecimal(3000));

        allRooms.add(blueRoom);

        Mockito.when(tenantCrudService.<FunctionSpaceFunctionRoom>findByNamedQuery(FunctionSpaceCombinationFunctionRoom.FIND_ALL_ACTIVE_ROOMS_INCLUDED_FOR_PRICING, QueryParameter
                .with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters())).thenReturn(
                allRooms);

        Map<FunctionSpaceFunctionRoomPriceTier, Map<DayOfWeek, FunctionSpaceLimitsDto>> retMap = spy
                .getMARByPriceTierMap(allRooms, new LocalDate(2014, 1, 1));

        assertNotNull(retMap);
        // should be 3 price tiers
        assertTrue(retMap.size() == 3);
        // each price tier should have 7 day of week values
        assertTrue(retMap.get(FunctionSpaceFunctionRoomPriceTier.TIER_1).size() == 7);
        assertTrue(retMap.get(FunctionSpaceFunctionRoomPriceTier.TIER_2).size() == 7);
        assertTrue(retMap.get(FunctionSpaceFunctionRoomPriceTier.TIER_3).size() == 7);

        // validate min/max changes for tier 1 on Saturday
        FunctionSpaceLimitsDto dto = retMap.get(FunctionSpaceFunctionRoomPriceTier.TIER_1).get(DayOfWeek.SATURDAY);

        assertNotNull(dto);
        assertEquals(new BigDecimal(50), dto.getMinValue());
        assertEquals(new BigDecimal(3000), dto.getMaxValue());
    }

    @Test
    public void getMARByPriceTierMap_WithSeasonalMAR() throws Exception {
        FunctionSpaceConfigurationService spy = Mockito.spy(service);

        List<FunctionSpaceFunctionRoom> allRooms = new ArrayList<>();

        FunctionSpaceFunctionRoom blueRoom = FunctionSpaceObjectMother.buildFunctionSpaceFunctionRoom("Blue");
        blueRoom.setFunctionSpaceFunctionRoomLimit(FunctionSpaceObjectMother
                .buildFunctionSpaceFunctionRoomLimit(blueRoom));
        // change low/high values for Saturday
        blueRoom.getFunctionSpaceFunctionRoomLimit().setSaturdayDefaultMAR(new BigDecimal(50));
        blueRoom.getFunctionSpaceFunctionRoomLimit().setSaturdayUpperLimit(new BigDecimal(3000));

        allRooms.add(blueRoom);

        Mockito.when(
                tenantCrudService.<FunctionSpaceFunctionRoom>findByNamedQuery(
                        FunctionSpaceCombinationFunctionRoom.FIND_ALL_ACTIVE_ROOMS_INCLUDED_FOR_PRICING, QueryParameter
                                .with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters())).thenReturn(
                allRooms);
        // 2015-1-1 is the start of the seasonal MAR, so it should use the seasonal MAR value 0f 250
        Map<FunctionSpaceFunctionRoomPriceTier, Map<DayOfWeek, FunctionSpaceLimitsDto>> retMap = spy
                .getMARByPriceTierMap(allRooms, new LocalDate(2015, 1, 1));

        assertNotNull(retMap);
        // should be 3 price tiers
        assertTrue(retMap.size() == 3);
        // each price tier should have 7 day of week values
        assertTrue(retMap.get(FunctionSpaceFunctionRoomPriceTier.TIER_1).size() == 7);
        assertTrue(retMap.get(FunctionSpaceFunctionRoomPriceTier.TIER_2).size() == 7);
        assertTrue(retMap.get(FunctionSpaceFunctionRoomPriceTier.TIER_3).size() == 7);

        // validate min/max changes for tier 1 on Saturday
        FunctionSpaceLimitsDto dto = retMap.get(FunctionSpaceFunctionRoomPriceTier.TIER_1).get(DayOfWeek.SATURDAY);

        assertNotNull(dto);
        assertEquals(new BigDecimal(250), dto.getMinValue());
        assertEquals(new BigDecimal(3000), dto.getMaxValue());
    }

    @Test
    public void findResourceType() {
        Mockito.when(
                tenantCrudService.findByNamedQuerySingleResult(
                        FunctionSpaceResourceType.FIND_BY_NAME,
                        QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId())
                                .and("name", FunctionSpaceResourceType.OTHER_RESOURCE_TYPE).parameters())).thenReturn(
                new FunctionSpaceResourceType());

        assertNotNull(service.findFunctionSpaceResourceType(FunctionSpaceResourceType.OTHER_RESOURCE_TYPE));

        Mockito.verify(tenantCrudService).findByNamedQuerySingleResult(
                FunctionSpaceResourceType.FIND_BY_NAME,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId())
                        .and("name", FunctionSpaceResourceType.OTHER_RESOURCE_TYPE).parameters());
    }

    @Test
    public void buildMapOfMAR_DifferentLimitsForTwoDays() throws Exception {
        List<FunctionSpaceFunctionRoom> allRooms = new ArrayList<>();

        FunctionSpaceFunctionRoom blueRoom = FunctionSpaceObjectMother.buildFunctionSpaceFunctionRoom("Blue");
        blueRoom.setFunctionSpaceFunctionRoomLimit(FunctionSpaceObjectMother
                .buildFunctionSpaceFunctionRoomLimit(blueRoom));
        // change low/high values for Friday
        blueRoom.getFunctionSpaceFunctionRoomLimit().setFridayDefaultMAR(new BigDecimal(100));
        blueRoom.getFunctionSpaceFunctionRoomLimit().setFridayUpperLimit(new BigDecimal(1000));
        // change low/high values for Saturday
        blueRoom.getFunctionSpaceFunctionRoomLimit().setSaturdayDefaultMAR(new BigDecimal(100));
        blueRoom.getFunctionSpaceFunctionRoomLimit().setSaturdayUpperLimit(new BigDecimal(150));

        FunctionSpaceFunctionRoom redRoom = FunctionSpaceObjectMother.buildFunctionSpaceFunctionRoom("Red");
        redRoom.setFunctionSpaceFunctionRoomLimit(FunctionSpaceObjectMother
                .buildFunctionSpaceFunctionRoomLimit(redRoom));
        // change low/high values for Friday
        redRoom.getFunctionSpaceFunctionRoomLimit().setFridayDefaultMAR(new BigDecimal(1800));
        redRoom.getFunctionSpaceFunctionRoomLimit().setFridayUpperLimit(new BigDecimal(2000));
        // change low/high values for Saturday
        redRoom.getFunctionSpaceFunctionRoomLimit().setSaturdayDefaultMAR(new BigDecimal(10));
        redRoom.getFunctionSpaceFunctionRoomLimit().setSaturdayUpperLimit(new BigDecimal(50));

        allRooms.add(blueRoom);
        allRooms.add(redRoom);

        LocalDate startDate = new LocalDate(2015, 3, 7);

        Map<DayOfWeek, FunctionSpaceLimitsDto> retMap = service.buildMapOfMAR(allRooms, startDate);

        // 100 should be the low mar for these two rooms on Friday
        assertEquals(new BigDecimal(100), retMap.get(DayOfWeek.FRIDAY).getMinValue());
        // 2000 should be the upper limit for these two rooms on Friday
        assertEquals(new BigDecimal(2000), retMap.get(DayOfWeek.FRIDAY).getMaxValue());
        // 10 should be the low mar for these two rooms on Saturday
        assertEquals(new BigDecimal(10), retMap.get(DayOfWeek.SATURDAY).getMinValue());
        // 150 should be the upper limit for these two rooms on FridayR
        assertEquals(new BigDecimal(150), retMap.get(DayOfWeek.SATURDAY).getMaxValue());
    }

    @Test
    public void saveRevenueGroups() throws Exception {
        List<FunctionSpaceRevenueGroup> revenueGroups = Collections.singletonList(FunctionSpaceObjectMother
                .buildFunctionSpaceRevenueGroup("GR", 1));

        Mockito.when(tenantCrudService.save(revenueGroups.get(0))).thenReturn(revenueGroups.get(0));

        service.saveRevenueGroups(revenueGroups);

        Mockito.verify(tenantCrudService).save(revenueGroups.get(0));
        Mockito.verify(syncService).registerSyncEvent(Mockito.eq(SyncEvent.FUNCTION_SPACE_CONFIG_CHANGED));
    }

    @Test
    public void ifARoomIsMarkedIncludedForPricingFalseThenDisplayAsRevenueOfAllPreFunctionRoomMappedToThisRoomIsSetNull() {
        FunctionSpaceFunctionRoom blue = FunctionSpaceObjectMother.buildFunctionSpaceFunctionRoom("Blue");
        blue.setId(1);
        FunctionSpaceFunctionRoom blue_preFunction = FunctionSpaceObjectMother.buildFunctionSpaceFunctionRoom("Blue PreFunction");
        blue_preFunction.setDisplayRevenueAs(blue.getId());
        when(tenantCrudService.findAll(FunctionSpaceFunctionRoom.class)).thenReturn(Collections.singletonList(blue_preFunction));

        int noOfUpdatedRooms = service.removeDisplayRevenueAsMappingFor(blue);

        assertEquals(1, noOfUpdatedRooms);
    }

    @Test
    void testGetSalesAndCateringMSToRMSMarketSegmentMapping() {
        Map<Integer, String> mktSegmentsById = new HashMap<>();
        mktSegmentsById.put(1, "A");
        FunctionSpaceMarketSegment spaceMarketSegment = new FunctionSpaceMarketSegment();
        spaceMarketSegment.setName("SC1");
        spaceMarketSegment.setId(1);
        spaceMarketSegment.setMarketSegmentId(1);
        when(tenantCrudService.findByNamedQuery(FunctionSpaceMarketSegment.FIND_BY_PROPERTY_ID, QueryParameter
                .with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters()))
                .thenReturn(List.of(spaceMarketSegment));
        List<SalesAndCateringMSToRMSMarketSegmentDTO> response = service.getSalesAndCateringMSToRMSMarketSegmentMapping(mktSegmentsById);
        assertEquals(1, response.size());
        assertEquals("A", response.get(0).getRmsMarketSegment());
        verify(tenantCrudService).findByNamedQuery(FunctionSpaceMarketSegment.FIND_BY_PROPERTY_ID, QueryParameter
                .with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters());
    }

    @Test
    void testAddSalesCateringToRMSMarketSegmentMapping() {
        List<SalesAndCateringMSToRMSMarketSegmentDTO> salesAndCateringMSToRMSMarketSegments =
                List.of(mockSalesAndCateringMSToRMSMarketSegmentDTO("SC1", "A"),
                        mockSalesAndCateringMSToRMSMarketSegmentDTO("SC2", "B"));
        Map<String, Integer> mktSegmentIdsByCode = new HashMap<>();
        mktSegmentIdsByCode.put("A", 1);
        mktSegmentIdsByCode.put("B", 2);
        when(tenantCrudService.executeUpdateByNamedQuery(FunctionSpaceMarketSegment.DELETE_BY_PROPERTY_ID, QueryParameter
                .with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters())).thenReturn(0);
        when(tenantCrudService.save(anyList())).thenReturn(new ArrayList<>());
        service.addSalesCateringToRMSMarketSegmentMapping(salesAndCateringMSToRMSMarketSegments, mktSegmentIdsByCode, false);
        verify(tenantCrudService).executeUpdateByNamedQuery(FunctionSpaceMarketSegment.DELETE_BY_PROPERTY_ID, QueryParameter
                .with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters());
        verify(tenantCrudService).save(anyList());
    }

    @Test
    void testGetGuestRoomTypeToRMSRoomTypeMapping() {
        FunctionSpaceGuestRoomCategory functionSpaceGuestRoomCategory = new FunctionSpaceGuestRoomCategory();
        functionSpaceGuestRoomCategory.setRoomCategory("RC1");
        functionSpaceGuestRoomCategory.setAccomTypeId(1);
        functionSpaceGuestRoomCategory.setId(1);
        when(tenantCrudService.findByNamedQuery(FunctionSpaceGuestRoomCategory.FIND_BY_PROPERTY_ID, QueryParameter
                .with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters()))
                .thenReturn(List.of(functionSpaceGuestRoomCategory));
        when(accommodationService.getAllAssignedAccomTypes()).thenReturn(List.of(mockAccommodationType("AT1", 1),
                mockAccommodationType("AT2", 2)));
        List<GuestRoomTypeToRMSRoomTypeDTO> response = service.getGuestRoomTypeToRMSRoomTypeMapping();
        assertEquals(1, response.size());
        assertEquals("RC1", response.get(0).getGuestRoomType());
        assertEquals("AT1", response.get(0).getRmsRoomType());
        verify(tenantCrudService).findByNamedQuery(FunctionSpaceGuestRoomCategory.FIND_BY_PROPERTY_ID, QueryParameter
                .with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters());
        verify(accommodationService).getAllAssignedAccomTypes();
    }

    @Test
    void testAddGuestRoomTypeToRMSRoomTypeMapping_ToggleDisable() {
        List<GuestRoomTypeToRMSRoomTypeDTO> guestRoomTypeToRMSRoomTypeDTOS =
                List.of(mockGuestRoomTypeToRMSRoomTypeDTO("GRT1", "RMS1"),
                        mockGuestRoomTypeToRMSRoomTypeDTO("GRT2", "RMS2"));
        when(accommodationService.getAllAssignedAccomTypes()).thenReturn(List.of(mockAccommodationType("RMS1", 1),
                mockAccommodationType("RMS2", 2)));
        when(tenantCrudService.executeUpdateByNamedQuery(FunctionSpaceGuestRoomCategory.DELETE_BY_PROPERTY_ID, QueryParameter
                .with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters())).thenReturn(0);
        when(tenantCrudService.save(anyList())).thenReturn(new ArrayList<>());
        when(pacmanConfigParamsService.getBooleanParameterValue(IntegrationConfigParamName.SALES_AND_CATERING_EVALUATION_ENABLED))
                .thenReturn(false);
        final TetrisException tetrisException = assertThrows(TetrisException.class, () -> service.addGuestRoomTypeToRMSRoomTypeMapping(guestRoomTypeToRMSRoomTypeDTOS, false));
        assertEquals("pacman.integration.SalesAndCateringEvaluationEnabled toggle need to be enabled to use this api.", tetrisException.getBaseMessage());
        verify(tenantCrudService, times(0)).executeUpdateByNamedQuery(FunctionSpaceGuestRoomCategory.DELETE_BY_PROPERTY_ID, QueryParameter
                .with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters());
        verify(tenantCrudService, times(0)).save(anyList());
        verify(accommodationService).getAllAssignedAccomTypes();
        verify(pacmanConfigParamsService).getBooleanParameterValue(IntegrationConfigParamName.SALES_AND_CATERING_EVALUATION_ENABLED);
    }

    @Test
    void testAddGuestRoomTypeToRMSRoomTypeMapping_ToggleEnable() {
        List<GuestRoomTypeToRMSRoomTypeDTO> guestRoomTypeToRMSRoomTypeDTOS =
                List.of(mockGuestRoomTypeToRMSRoomTypeDTO("GRT1", "RMS1"),
                        mockGuestRoomTypeToRMSRoomTypeDTO("GRT2", "RMS2"));
        when(accommodationService.getAllAssignedAccomTypes()).thenReturn(List.of(mockAccommodationType("RMS1", 1),
                mockAccommodationType("RMS2", 2)));
        when(tenantCrudService.executeUpdateByNamedQuery(FunctionSpaceGuestRoomCategory.DELETE_BY_PROPERTY_ID, QueryParameter
                .with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters())).thenReturn(0);
        when(tenantCrudService.save(anyList())).thenReturn(new ArrayList<>());
        when(pacmanConfigParamsService.getBooleanParameterValue(IntegrationConfigParamName.SALES_AND_CATERING_EVALUATION_ENABLED))
                .thenReturn(true);
        service.addGuestRoomTypeToRMSRoomTypeMapping(guestRoomTypeToRMSRoomTypeDTOS, false);
        verify(tenantCrudService).executeUpdateByNamedQuery(FunctionSpaceGuestRoomCategory.DELETE_BY_PROPERTY_ID, QueryParameter
                .with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters());
        verify(tenantCrudService).save(anyList());
        verify(accommodationService).getAllAssignedAccomTypes();
        verify(pacmanConfigParamsService).getBooleanParameterValue(IntegrationConfigParamName.SALES_AND_CATERING_EVALUATION_ENABLED);
    }

    @Test
    void testAddGuestRoomTypeToRMSRoomTypeMapping_ToggleEnable_RequestContainsEmptyRMSValues() {
        List<GuestRoomTypeToRMSRoomTypeDTO> guestRoomTypeToRMSRoomTypeDTOS =
                List.of(mockGuestRoomTypeToRMSRoomTypeDTO("", "RMS1"),
                        mockGuestRoomTypeToRMSRoomTypeDTO("", "RMS2"));
        when(accommodationService.getAllAssignedAccomTypes()).thenReturn(List.of(mockAccommodationType("RMS1", 1),
                mockAccommodationType("RMS2", 2)));
        when(pacmanConfigParamsService.getBooleanParameterValue(IntegrationConfigParamName.SALES_AND_CATERING_EVALUATION_ENABLED))
                .thenReturn(true);
        boolean response = service.addGuestRoomTypeToRMSRoomTypeMapping(guestRoomTypeToRMSRoomTypeDTOS, false);
        assertFalse(response);
        verify(accommodationService).getAllAssignedAccomTypes();
        verify(pacmanConfigParamsService).getBooleanParameterValue(IntegrationConfigParamName.SALES_AND_CATERING_EVALUATION_ENABLED);
    }

    @Test
    void testAddGuestRoomTypeToRMSRoomTypeMapping_ToggleEnable_RequestContainsNullRMSValues() {
        List<GuestRoomTypeToRMSRoomTypeDTO> guestRoomTypeToRMSRoomTypeDTOS =
                List.of(mockGuestRoomTypeToRMSRoomTypeDTO(null, "RMS1"),
                        mockGuestRoomTypeToRMSRoomTypeDTO(null, "RMS2"));
        when(accommodationService.getAllAssignedAccomTypes()).thenReturn(List.of(mockAccommodationType("RMS1", 1),
                mockAccommodationType("RMS2", 2)));
        when(pacmanConfigParamsService.getBooleanParameterValue(IntegrationConfigParamName.SALES_AND_CATERING_EVALUATION_ENABLED))
                .thenReturn(true);
        boolean response = service.addGuestRoomTypeToRMSRoomTypeMapping(guestRoomTypeToRMSRoomTypeDTOS, false);
        assertFalse(response);
        verify(accommodationService).getAllAssignedAccomTypes();
        verify(pacmanConfigParamsService).getBooleanParameterValue(IntegrationConfigParamName.SALES_AND_CATERING_EVALUATION_ENABLED);
    }

    @Test
    void testAddGuestRoomTypeToRMSRoomTypeMapping_ToggleEnable_RequestContainsNullGuestRoomValues() {
        List<GuestRoomTypeToRMSRoomTypeDTO> guestRoomTypeToRMSRoomTypeDTOS =
                List.of(mockGuestRoomTypeToRMSRoomTypeDTO("GRT1", null),
                        mockGuestRoomTypeToRMSRoomTypeDTO("GRT2", null));
        when(accommodationService.getAllAssignedAccomTypes()).thenReturn(List.of(mockAccommodationType("RMS1", 1),
                mockAccommodationType("RMS2", 2)));
        when(pacmanConfigParamsService.getBooleanParameterValue(IntegrationConfigParamName.SALES_AND_CATERING_EVALUATION_ENABLED))
                .thenReturn(true);
        boolean response = service.addGuestRoomTypeToRMSRoomTypeMapping(guestRoomTypeToRMSRoomTypeDTOS, false);
        assertTrue(response);
        verify(accommodationService).getAllAssignedAccomTypes();
        verify(pacmanConfigParamsService).getBooleanParameterValue(IntegrationConfigParamName.SALES_AND_CATERING_EVALUATION_ENABLED);
    }

    @Test
    void testAddGuestRoomTypeToRMSRoomTypeMapping_ToggleEnable_RequestContainsEmptyGuestRoomValues() {
        List<GuestRoomTypeToRMSRoomTypeDTO> guestRoomTypeToRMSRoomTypeDTOS =
                List.of(mockGuestRoomTypeToRMSRoomTypeDTO("GRT1", ""),
                        mockGuestRoomTypeToRMSRoomTypeDTO("GRT2", ""));
        when(accommodationService.getAllAssignedAccomTypes()).thenReturn(List.of(mockAccommodationType("RMS1", 1),
                mockAccommodationType("RMS2", 2)));
        when(pacmanConfigParamsService.getBooleanParameterValue(IntegrationConfigParamName.SALES_AND_CATERING_EVALUATION_ENABLED))
                .thenReturn(true);
        boolean response = service.addGuestRoomTypeToRMSRoomTypeMapping(guestRoomTypeToRMSRoomTypeDTOS, false);
        assertTrue(response);
        verify(accommodationService).getAllAssignedAccomTypes();
        verify(pacmanConfigParamsService).getBooleanParameterValue(IntegrationConfigParamName.SALES_AND_CATERING_EVALUATION_ENABLED);
    }

    @Test
    void testAddGuestRoomTypeToRMSRoomTypeMapping_ToggleEnable_DuplicateEntriesInRequest() {
        List<GuestRoomTypeToRMSRoomTypeDTO> guestRoomTypeToRMSRoomTypeDTOS =
                List.of(mockGuestRoomTypeToRMSRoomTypeDTO("GRT1", "RMS1"),
                        mockGuestRoomTypeToRMSRoomTypeDTO("GRT1", "RMS2"));
        when(accommodationService.getAllAssignedAccomTypes()).thenReturn(List.of(mockAccommodationType("RMS1", 1),
                mockAccommodationType("RMS2", 2)));
        when(pacmanConfigParamsService.getBooleanParameterValue(IntegrationConfigParamName.SALES_AND_CATERING_EVALUATION_ENABLED))
                .thenReturn(true);
        boolean response = service.addGuestRoomTypeToRMSRoomTypeMapping(guestRoomTypeToRMSRoomTypeDTOS, false);
        assertFalse(response);
        verify(accommodationService).getAllAssignedAccomTypes();
        verify(pacmanConfigParamsService).getBooleanParameterValue(IntegrationConfigParamName.SALES_AND_CATERING_EVALUATION_ENABLED);
    }

    @Test
    void testValidateSaleAndCateringToRmsMsRequest_ToggleDisable() {
        List<SalesAndCateringMSToRMSMarketSegmentDTO> salesAndCateringMSToRMSMarketSegmentDTOS =
                List.of(mockSalesAndCateringMSToRMSMarketSegmentDTO("Sale1", "RMS1"),
                        mockSalesAndCateringMSToRMSMarketSegmentDTO("Sale2", "RMS2"));
        when(pacmanConfigParamsService.getBooleanParameterValue(IntegrationConfigParamName.SALES_AND_CATERING_EVALUATION_ENABLED))
                .thenReturn(false);
        final TetrisException tetrisException = assertThrows(TetrisException.class, () ->
                service.validateSaleAndCateringToRmsMsRequest(salesAndCateringMSToRMSMarketSegmentDTOS, new HashMap<>()));
        assertEquals("pacman.integration.SalesAndCateringEvaluationEnabled toggle need to be enabled to use this api.", tetrisException.getBaseMessage());
        verify(pacmanConfigParamsService).getBooleanParameterValue(IntegrationConfigParamName.SALES_AND_CATERING_EVALUATION_ENABLED);
    }

    @Test
    void testValidateSaleAndCateringToRmsMsRequest_EmptySalesMS() {
        List<SalesAndCateringMSToRMSMarketSegmentDTO> salesAndCateringMSToRMSMarketSegmentDTOS =
                List.of(mockSalesAndCateringMSToRMSMarketSegmentDTO("", "RMS1"),
                        mockSalesAndCateringMSToRMSMarketSegmentDTO("", "RMS2"));
        when(pacmanConfigParamsService.getBooleanParameterValue(IntegrationConfigParamName.SALES_AND_CATERING_EVALUATION_ENABLED))
                .thenReturn(true);
        boolean response = service.validateSaleAndCateringToRmsMsRequest(salesAndCateringMSToRMSMarketSegmentDTOS, new HashMap<>());
        assertFalse(response);
        verify(pacmanConfigParamsService).getBooleanParameterValue(IntegrationConfigParamName.SALES_AND_CATERING_EVALUATION_ENABLED);
    }

    @Test
    void testValidateSaleAndCateringToRmsMsRequest_NullSalesMS() {
        List<SalesAndCateringMSToRMSMarketSegmentDTO> salesAndCateringMSToRMSMarketSegmentDTOS =
                List.of(mockSalesAndCateringMSToRMSMarketSegmentDTO(null, "RMS1"),
                        mockSalesAndCateringMSToRMSMarketSegmentDTO(null, "RMS2"));
        when(pacmanConfigParamsService.getBooleanParameterValue(IntegrationConfigParamName.SALES_AND_CATERING_EVALUATION_ENABLED))
                .thenReturn(true);
        boolean response = service.validateSaleAndCateringToRmsMsRequest(salesAndCateringMSToRMSMarketSegmentDTOS, new HashMap<>());
        assertFalse(response);
        verify(pacmanConfigParamsService).getBooleanParameterValue(IntegrationConfigParamName.SALES_AND_CATERING_EVALUATION_ENABLED);
    }

    @Test
    void testValidateSaleAndCateringToRmsMsRequest_EmptyRMSMS() {
        List<SalesAndCateringMSToRMSMarketSegmentDTO> salesAndCateringMSToRMSMarketSegmentDTOS =
                List.of(mockSalesAndCateringMSToRMSMarketSegmentDTO("Sales1", ""),
                        mockSalesAndCateringMSToRMSMarketSegmentDTO("Sales2", ""));
        when(pacmanConfigParamsService.getBooleanParameterValue(IntegrationConfigParamName.SALES_AND_CATERING_EVALUATION_ENABLED))
                .thenReturn(true);
        boolean response = service.validateSaleAndCateringToRmsMsRequest(salesAndCateringMSToRMSMarketSegmentDTOS, new HashMap<>());
        assertFalse(response);
        verify(pacmanConfigParamsService).getBooleanParameterValue(IntegrationConfigParamName.SALES_AND_CATERING_EVALUATION_ENABLED);
    }

    @Test
    void testValidateSaleAndCateringToRmsMsRequest_NullRMSMS() {
        List<SalesAndCateringMSToRMSMarketSegmentDTO> salesAndCateringMSToRMSMarketSegmentDTOS =
                List.of(mockSalesAndCateringMSToRMSMarketSegmentDTO("Sales1", null),
                        mockSalesAndCateringMSToRMSMarketSegmentDTO("Sales2", null));
        when(pacmanConfigParamsService.getBooleanParameterValue(IntegrationConfigParamName.SALES_AND_CATERING_EVALUATION_ENABLED))
                .thenReturn(true);
        boolean response = service.validateSaleAndCateringToRmsMsRequest(salesAndCateringMSToRMSMarketSegmentDTOS, new HashMap<>());
        assertFalse(response);
        verify(pacmanConfigParamsService).getBooleanParameterValue(IntegrationConfigParamName.SALES_AND_CATERING_EVALUATION_ENABLED);
    }

    @Test
    void testValidateSaleAndCateringToRmsMsRequest_DuplicateEntriesInRequest() {
        List<SalesAndCateringMSToRMSMarketSegmentDTO> salesAndCateringMSToRMSMarketSegmentDTOS =
                List.of(mockSalesAndCateringMSToRMSMarketSegmentDTO("Sales1", "RMS1"),
                        mockSalesAndCateringMSToRMSMarketSegmentDTO("Sales1", "RMS2"));
        when(pacmanConfigParamsService.getBooleanParameterValue(IntegrationConfigParamName.SALES_AND_CATERING_EVALUATION_ENABLED))
                .thenReturn(true);
        boolean response = service.validateSaleAndCateringToRmsMsRequest(salesAndCateringMSToRMSMarketSegmentDTOS, new HashMap<>());
        assertFalse(response);
        verify(pacmanConfigParamsService).getBooleanParameterValue(IntegrationConfigParamName.SALES_AND_CATERING_EVALUATION_ENABLED);
    }

    @Test
    void testAutoConfigureGuestRoomTypesToAccomTypes_OneToOneMappingNotPossible() {
        when(accommodationService.getAllAssignedAccomTypes()).thenReturn(List.of(mockAccommodationType("RMS1", 1),
                mockAccommodationType("RMS2", 2)));
        when(tenantCrudService.findByNamedQuery(FunctionSpaceGuestRoomCategory.FIND_BY_PROPERTY_ID,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters()))
                .thenReturn(List.of(mockFunctionSpaceGuestRoomCategory(1, "Guest-1"),
                        mockFunctionSpaceGuestRoomCategory(2, "Guest-2")));
        service.autoConfigureGuestRoomTypesToAccomTypes();
        verify(tenantCrudService).findByNamedQuery(FunctionSpaceGuestRoomCategory.FIND_BY_PROPERTY_ID,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters());
        verify(tenantCrudService).save(guestRoomCategoryListCaptor.capture());
        FunctionSpaceGuestRoomCategory firstEntry = guestRoomCategoryListCaptor.getValue().get(0);
        FunctionSpaceGuestRoomCategory secondEntry = guestRoomCategoryListCaptor.getValue().get(1);
        assertNull(firstEntry.getAccomTypeId());
        assertNull(secondEntry.getAccomTypeId());
        verify(accommodationService).getAllAssignedAccomTypes();
    }

    @Test
    void testAutoConfigureGuestRoomTypesToAccomTypes_DoOneToOneMapping() {
        when(accommodationService.getAllAssignedAccomTypes()).thenReturn(List.of(mockAccommodationType("Guest-1", 1), mockAccommodationType("Guest-2", 2)));
        when(tenantCrudService.findByNamedQuery(FunctionSpaceGuestRoomCategory.FIND_BY_PROPERTY_ID,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters()))
                .thenReturn(List.of(mockFunctionSpaceGuestRoomCategory(1, "Guest-1"),
                        mockFunctionSpaceGuestRoomCategory(2, "Guest-2")));
        service.autoConfigureGuestRoomTypesToAccomTypes();
        verify(tenantCrudService).findByNamedQuery(FunctionSpaceGuestRoomCategory.FIND_BY_PROPERTY_ID,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters());
        verify(tenantCrudService).save(guestRoomCategoryListCaptor.capture());
        FunctionSpaceGuestRoomCategory firstEntry = guestRoomCategoryListCaptor.getValue().get(0);
        FunctionSpaceGuestRoomCategory secondEntry = guestRoomCategoryListCaptor.getValue().get(1);
        assertEquals(1, firstEntry.getAccomTypeId());
        assertEquals(2, secondEntry.getAccomTypeId());
        verify(accommodationService).getAllAssignedAccomTypes();
    }

    @Test
    public void shouldReturnFunctionSpaceRevenueGroupByAbbreviation() {
        FunctionSpaceRevenueGroup expectedRevenueGroup = FunctionSpaceObjectMother.buildFunctionSpaceRevenueGroup("F&B", 1);
        when(tenantCrudService.findByNamedQuerySingleResult(FunctionSpaceRevenueGroup.FIND_ACTIVE_REVENUE_GROUP_BY_ABBREVIATION_WITH_RESOURCE_TYPE,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId())
                        .and("abbreviation", "F&B").parameters())).thenReturn(expectedRevenueGroup);

        FunctionSpaceRevenueGroup actualRevenueGroup = service.findRevenueGroupByAbbreviation("F&B");

        assertEquals(expectedRevenueGroup, actualRevenueGroup);
        assertEquals(1, actualRevenueGroup.getId());
        assertEquals("F&B", actualRevenueGroup.getName());
        verify(tenantCrudService).findByNamedQuerySingleResult(FunctionSpaceRevenueGroup.FIND_ACTIVE_REVENUE_GROUP_BY_ABBREVIATION_WITH_RESOURCE_TYPE,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId())
                        .and("abbreviation", "F&B").parameters());
    }

    @Test
    void shouldReturnFunctionSpaceRevenueGroupById() {
        when(tenantCrudService.findByNamedQuerySingleResult(eq(FunctionSpaceRevenueGroup.FIND_BY_ID), anyMap())).thenReturn(new FunctionSpaceRevenueGroup());

        FunctionSpaceRevenueGroup functionSpaceRevenueGroup = service.getFunctionSpaceRevenueGroupById(1);

        assertNotNull(functionSpaceRevenueGroup);
        verify(tenantCrudService).findByNamedQuerySingleResult(eq(FunctionSpaceRevenueGroup.FIND_BY_ID), anyMap());
    }

    @Test
    void shouldReturnFunctionSpaceRevenueGroupsByPropertyId() {
        when(tenantCrudService.findByNamedQuery(eq(FunctionSpaceRevenueGroup.FIND_ACTIVE_REVENUE_GROUPS_BY_PROPERTY_ID), anyMap())).thenReturn(List.of(new FunctionSpaceRevenueGroup()));

        List<FunctionSpaceRevenueGroup> functionSpaceRevenueGroups = service.findActiveFunctionSpaceRevenueGroupsByPropertyId(1);

        assertNotNull(functionSpaceRevenueGroups);
        assertEquals(1, functionSpaceRevenueGroups.size());
        assertEquals(TenantStatusEnum.ACTIVE,functionSpaceRevenueGroups.get(0).getStatus());
        verify(tenantCrudService).findByNamedQuery(eq(FunctionSpaceRevenueGroup.FIND_ACTIVE_REVENUE_GROUPS_BY_PROPERTY_ID), anyMap());
    }

    @Test
    void shouldSoftDeleteFunctionSpaceRevenueGroup() {
        FunctionSpaceRevenueGroup expectedRevenueGroup = FunctionSpaceObjectMother.buildFunctionSpaceRevenueGroup("F&B", 1);

        service.softDeleteFunctionSpaceRevenueGroup(expectedRevenueGroup);

        ArgumentCaptor<FunctionSpaceRevenueGroup> revenueGroupArgumentCaptor = ArgumentCaptor.forClass(FunctionSpaceRevenueGroup.class);
        verify(tenantCrudService).save(revenueGroupArgumentCaptor.capture());
        assertEquals(TenantStatusEnum.DELETED, revenueGroupArgumentCaptor.getValue().getStatus());
    }

    @Test
    void shouldNotSaveEmptyOrNullFunctionSpaceStatus() {
        List<FunctionSpaceStatus> functionSpaceStatusList = mockFunctionSpaceStatusObject();
        boolean response = service.saveFunctionSpaceStatus(functionSpaceStatusList);
        assertFalse(response);
    }

    private List<FunctionSpaceStatus> mockFunctionSpaceStatusObject() {
        List<FunctionSpaceStatus> functionSpaceStatusList = new ArrayList<>();
        FunctionSpaceStatus functionSpaceStatus = new FunctionSpaceStatus();
        functionSpaceStatus.setPropertyId(PacmanWorkContextHelper.getPropertyId());
        functionSpaceStatus.setStatusCode("");
        functionSpaceStatus.setFinal(true);
        functionSpaceStatus.setTentative(true);
        functionSpaceStatus.setProspect(true);
        functionSpaceStatus.setInventoryDeducted(true);
        functionSpaceStatusList.add(functionSpaceStatus);

        return functionSpaceStatusList;
    }

    private GuestRoomTypeToRMSRoomTypeDTO mockGuestRoomTypeToRMSRoomTypeDTO(String guestRoomType, String rmsRoomType) {
        GuestRoomTypeToRMSRoomTypeDTO guestRoomTypeToRMSRoomTypeDTO = new GuestRoomTypeToRMSRoomTypeDTO();
        guestRoomTypeToRMSRoomTypeDTO.setGuestRoomType(guestRoomType);
        guestRoomTypeToRMSRoomTypeDTO.setRmsRoomType(rmsRoomType);
        return guestRoomTypeToRMSRoomTypeDTO;
    }

    private AccomType mockAccommodationType(String name, int id) {
        AccomType accomType = new AccomType();
        accomType.setId(id);
        accomType.setName(name);
        accomType.setAccomTypeCode(name);
        accomType.setStatusId(1);
        return accomType;
    }

    private SalesAndCateringMSToRMSMarketSegmentDTO mockSalesAndCateringMSToRMSMarketSegmentDTO(String salesMS, String rmsMS) {
        SalesAndCateringMSToRMSMarketSegmentDTO salesAndCateringMSToRMSMarketSegmentDTO = new SalesAndCateringMSToRMSMarketSegmentDTO();
        salesAndCateringMSToRMSMarketSegmentDTO.setSaleAndCateringMS(salesMS);
        salesAndCateringMSToRMSMarketSegmentDTO.setRmsMarketSegment(rmsMS);
        return salesAndCateringMSToRMSMarketSegmentDTO;
    }

    private FunctionSpaceGuestRoomCategory mockFunctionSpaceGuestRoomCategory(int id, String guestCategory) {
        FunctionSpaceGuestRoomCategory guestRoomCategory = new FunctionSpaceGuestRoomCategory();
        guestRoomCategory.setId(id);
        guestRoomCategory.setPropertyId(5);
        guestRoomCategory.setRoomCategory(guestCategory);
        guestRoomCategory.setAccomTypeId(null);
        return guestRoomCategory;
    }
}
