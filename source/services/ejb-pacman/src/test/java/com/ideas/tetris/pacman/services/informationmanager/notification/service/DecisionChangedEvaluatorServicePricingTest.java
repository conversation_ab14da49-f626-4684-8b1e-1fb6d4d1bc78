package com.ideas.tetris.pacman.services.informationmanager.notification.service;

import com.google.common.collect.Sets;
import com.ideas.cache.redis.configuration.IdeasRedisCacheManager;
import com.ideas.g3.test.AbstractG3JupiterTest;
import com.ideas.infra.tetris.security.domain.LDAPUser;
import com.ideas.tetris.pacman.common.configparams.AlertConfigParamName;
import com.ideas.tetris.pacman.common.configparams.GUIConfigParamName;
import com.ideas.tetris.pacman.common.configparams.IPConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.dashboard.DashboardService;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.informationmanager.dto.AlertType;
import com.ideas.tetris.pacman.services.informationmanager.dto.ExceptionAlert;
import com.ideas.tetris.pacman.services.informationmanager.dto.ExceptionConfigDTO;
import com.ideas.tetris.pacman.services.informationmanager.entity.InfoMgrExcepNotifEntity;
import com.ideas.tetris.pacman.services.informationmanager.entity.InfoMgrTypeEntity;
import com.ideas.tetris.pacman.services.informationmanager.entity.InformationMgrAlertConfigEntity;
import com.ideas.tetris.pacman.services.informationmanager.entity.InformationMgrLevelEntity;
import com.ideas.tetris.pacman.services.informationmanager.entity.InformationMgrSubTypeEntity;
import com.ideas.tetris.pacman.services.informationmanager.enums.ExceptionSubType;
import com.ideas.tetris.pacman.services.informationmanager.enums.LevelType;
import com.ideas.tetris.pacman.services.informationmanager.enums.MetricType;
import com.ideas.tetris.pacman.services.informationmanager.enums.SubLevelType;
import com.ideas.tetris.pacman.services.informationmanager.exception.service.ExceptionAlertService;
import com.ideas.tetris.pacman.services.informationmanager.exception.service.ExceptionConfigService;
import com.ideas.tetris.pacman.services.informationmanager.exception.service.SystemExceptionEvaluatorService;
import com.ideas.tetris.pacman.services.informationmanager.exception.types.InfoMgrExcepNotifSnoozerFactory;
import com.ideas.tetris.pacman.services.informationmanager.notification.interfaces.INotificationEvaluator;
import com.ideas.tetris.pacman.services.informationmanager.notification.querybuilder.DecisionAsOfLastNightlyOptimization;
import com.ideas.tetris.pacman.services.informationmanager.notification.querybuilder.DecisionAsOfLastOptimization;
import com.ideas.tetris.pacman.services.informationmanager.notification.querybuilder.DecisionChangeFromLastNightlyOptimization;
import com.ideas.tetris.pacman.services.propertygroup.service.PropertyGroupService;
import com.ideas.tetris.pacman.services.security.AuthorizationService;
import com.ideas.tetris.pacman.services.security.RoleService;
import com.ideas.tetris.pacman.services.security.UserAuthorizedPropertyCache;
import com.ideas.tetris.pacman.services.security.UserService;
import com.ideas.tetris.pacman.services.specialevent.service.SpecialEventService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.xml.schema.workcontext.v1.WorkContextType;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import com.ideas.tetris.platform.services.globalproperty.service.ClientCache;
import com.ideas.tetris.platform.services.globalproperty.service.ClientPropertyCacheService;
import com.ideas.tetris.platform.services.globalproperty.service.ClientPropertyRelationshipCache;
import com.ideas.tetris.platform.services.globalproperty.service.PropertyCache;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import javax.persistence.Query;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.ideas.tetris.pacman.services.informationmanager.notification.service.DecisionChangeEvaluatorForOverbookingTest.getNotificationEvaluator;
import static com.ideas.tetris.platform.common.utils.dateutil.DateUtil.addDaysToDate;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.anyObject;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.nullable;
import static org.mockito.Mockito.when;

@MockitoSettings(strictness = Strictness.LENIENT)
public class DecisionChangedEvaluatorServicePricingTest extends AbstractG3JupiterTest {

    public static final int PROPERTY_ID5 = 5;

    public static final String SUCCESS = "SUCCESS";

    public static final String TODAY = "Today";

    public static final String TODAY7 = "Today+7";

    public static final String FREQUENCY = "1";

    DashboardService mockDashboardService = null;

    public INotificationEvaluator instance = null;

    SystemExceptionEvaluatorService exceptionEvaluator = null;

    DateService mockDateService;

    ExceptionConfigService alertExceptionService;

    PacmanConfigParamsService mockConfigParamsService = Mockito.mock(PacmanConfigParamsService.class);

    ExceptionAlertService exceptionAlertService;

    DateService dateService;

    PropertyGroupService propertyGroupService;

    private PacmanConfigParamsService mockConfigService;

    private AuthorizationService authService;

    private final Date businessDate = new GregorianCalendar(2010, Calendar.JANUARY, 02).getTime();

    private InfoMgrExcepNotifSnoozerFactory infoMgrExcepNotifSnoozerFactory;

    @Mock
    SpecialEventService specialEventService;

    @BeforeEach
    public void setUp() {
        setUpWorkContext();
        alertExceptionService = new ExceptionConfigService();
        alertExceptionService.setGlobalCrudService(globalCrudService());
        authService = new AuthorizationService();
        authService.setCrudService(tenantCrudService());
        authService.setGlobalCrudService(globalCrudService());
        dateService = Mockito.mock(DateService.class);
        when(dateService.getBusinessDate()).thenReturn(businessDate);
        exceptionEvaluator = new SystemExceptionEvaluatorService();
        propertyGroupService = new PropertyGroupService();
        mockDashboardService = Mockito.mock(DashboardService.class);
        alertExceptionService.crudService = tenantCrudService();
        exceptionAlertService = new ExceptionAlertService();
        exceptionAlertService.setMultiPropertyCrudService(multiPropertyCrudService());
        exceptionAlertService.setAuthorizationService(authService);
        exceptionEvaluator.setDateService(mockDateService);
        UserService userService = Mockito.mock(UserService.class);
        inject(authService, "userService", userService);
        RoleService roleService = Mockito.mock(RoleService.class);
        inject(authService, "roleService", roleService);
        LDAPUser u1 = new LDAPUser();
        u1.setUserId(11403);
        ArrayList<String> roleList = new ArrayList<>();
        roleList.add("roleId=-666 ON GROUP groupId=-666");
        u1.setRoles(roleList);
        when(userService.getById("11403")).thenReturn(u1);
        when(roleService.getPropertiesForUser(anyString(), anyObject())).thenReturn(Sets.newHashSet("5"));
        when(roleService.getPermsForUser(anyString(), anyString(), anyObject())).thenReturn(Sets.newHashSet("-666"));
        authService.setPacmanConfigParamsService(mockConfigParamsService);
        exceptionEvaluator.setAuthorizationService(authService);
        alertExceptionService.setAuthorizationService(authService);
        alertExceptionService.setMultiPropertyCrudService(multiPropertyCrudService());
        alertExceptionService.setConfigParamService(mockConfigParamsService);
        infoMgrExcepNotifSnoozerFactory = Mockito.mock(InfoMgrExcepNotifSnoozerFactory.class);
        instance = getNotificationEvaluator(AlertType.DecisionChangeEx, mockConfigParamsService, multiPropertyCrudService(), globalCrudService(), dateService, specialEventService, tenantCrudService(), infoMgrExcepNotifSnoozerFactory);
        MockitoAnnotations.initMocks(this);
        ClientPropertyCacheService clientPropertyCacheService = new ClientPropertyCacheService();
        authService.setClientPropertyCacheService(clientPropertyCacheService);

        IdeasRedisCacheManager ideasRedisCacheManager = ideasRedisCacheManager();

        ClientCache clientCache = new ClientCache();
        clientCache.setIdeasRedisCacheManager(ideasRedisCacheManager);
        inject(clientCache, "crudService", globalCrudService());
        clientPropertyCacheService.setClientCache(clientCache);

        PropertyCache propertyCache = new PropertyCache();
        propertyCache.setIdeasRedisCacheManager(ideasRedisCacheManager);
        inject(propertyCache, "crudService", globalCrudService());
        clientPropertyCacheService.setPropertyCache(propertyCache);

        ClientPropertyRelationshipCache clientPropertyRelationshipCache = new ClientPropertyRelationshipCache();
        clientPropertyRelationshipCache.setIdeasRedisCacheManager(ideasRedisCacheManager);
        inject(clientPropertyRelationshipCache, "crudService", globalCrudService());
        clientPropertyCacheService.setClientPropertyRelationshipCache(clientPropertyRelationshipCache);

        UserAuthorizedPropertyCache userAuthorizedPropertyCache = new UserAuthorizedPropertyCache();
        userAuthorizedPropertyCache.setIdeasRedisCacheManager(new IdeasRedisCacheManager());
        authService.setUserAuthorizedPropertyCache(userAuthorizedPropertyCache);
    }

    public void setUpWorkContext() {
        WorkContextType wc = new WorkContextType();
        wc.setClientCode("BSTN");
        wc.setClientId(2);
        wc.setPropertyCode("H1");
        wc.setPropertyId(5);
        wc.setUserId("1");
        PacmanWorkContextHelper.setWorkContext(wc);
    }

    @Test
    public void testDecisionChangeForPricingNewException_RateOFDay() {
        when(mockConfigParamsService.getParameterValue(AlertConfigParamName.USER_EXCEPTIONS_ENABLED)).thenReturn(true);
        when(mockConfigParamsService.getParameterValue(IPConfigParamName.BAR_BAR_DECISION.value())).thenReturn(Constants.BAR_DECISION_VALUE_RATEOFDAY);
        when(mockConfigParamsService.getValue("pacman.BSTN", GUIConfigParamName.CORE_DISPLAY_PROPERTY_CODE_OR_NAME.value())).thenReturn("Code");
        List<InformationMgrAlertConfigEntity> listOfExcep = alertExceptionService.findAllExceptions();
        Map<String, List<InformationMgrAlertConfigEntity>> resultList = alertExceptionService.persistExceptionConfiguration(buildExceptionConfiguration(ExceptionSubType.PRICING.getCode(), LevelType.ROOM_CLASS.getCode(), SubLevelType.ALL_ROOM_CLASSES.getCode(), Arrays.asList(PROPERTY_ID5), true, ">=", 2.0, AlertType.DecisionChangeEx));
        tenantCrudService().flush();
        listOfExcep = resultList.get(SUCCESS);
        setPricingData(businessDate);
        instance.evaluateByType(listOfExcep, AlertType.DecisionChangeEx, new DecisionChangeFromLastNightlyOptimization(), false);
        List list = multiPropertyCrudService().findByNamedQueryForSingleProperty(5, InfoMgrExcepNotifEntity.ALL, new HashMap<>());
        assertionsForRateOfDay(list, 40);
        // Check if score increases after executing service again
        instance.evaluateByType(listOfExcep, AlertType.DecisionChangeEx, new DecisionChangeFromLastNightlyOptimization(), false);
        assertionsForRateOfDay(fetchAllNotifications(), 60);
        // Check if score decreases after executing service again with changes increase in threshold
        for (InformationMgrAlertConfigEntity configEntity : listOfExcep) {
            configEntity.setThresholdValue(new BigDecimal(8));
        }
        instance.evaluateByType(listOfExcep, AlertType.DecisionChangeEx, new DecisionChangeFromLastNightlyOptimization(), false);
        assertionsForRateOfDay(fetchAllNotifications(), 30);
    }

    @Test
    public void testDecisionChangeForPricingNewException_BarByLOS() {
        when(mockConfigParamsService.getValue("pacman.BSTN", GUIConfigParamName.CORE_DISPLAY_PROPERTY_CODE_OR_NAME.value())).thenReturn("Code");
        List<InformationMgrAlertConfigEntity> listOfExcep = alertExceptionService.findAllExceptions();
        Map<String, List<InformationMgrAlertConfigEntity>> resultList = alertExceptionService.persistExceptionConfiguration(buildExceptionConfiguration(ExceptionSubType.PRICING.getCode(), LevelType.ROOM_CLASS.getCode(), SubLevelType.ALL_ROOM_CLASSES.getCode(), Arrays.asList(PROPERTY_ID5), true, "<=", 2.0, AlertType.DecisionChangeEx));
        tenantCrudService().flush();
        listOfExcep = resultList.get(SUCCESS);
        when(mockConfigParamsService.getParameterValue(AlertConfigParamName.USER_EXCEPTIONS_ENABLED)).thenReturn(true);
        when(mockConfigParamsService.getParameterValue(IPConfigParamName.BAR_BAR_DECISION.value())).thenReturn(Constants.BAR_DECISION_VALUE_LOS);
        setExistingDataForTest(businessDate);
        instance.evaluateByType(listOfExcep, AlertType.DecisionChangeEx, new DecisionChangeFromLastNightlyOptimization(), false);
        List<InfoMgrExcepNotifEntity> list = fetchAllNotifications();
        assertionsForBarByLOS(list, 40);
        // Check if score increases after executing service again
        instance.evaluateByType(listOfExcep, AlertType.DecisionChangeEx, new DecisionChangeFromLastNightlyOptimization(), false);
        list = fetchAllNotifications();
        assertionsForBarByLOS(list, 60);
        // Check if score decreases after executing service again with changes increase in threshold
        for (InformationMgrAlertConfigEntity configEntity : listOfExcep) {
            configEntity.setThresholdValue(new BigDecimal(8));
        }
        instance.evaluateByType(listOfExcep, AlertType.DecisionChangeEx, new DecisionChangeFromLastNightlyOptimization(), false);
        list = fetchAllNotifications();
        assertionsForBarByLOS(list, 30);
    }

    @Test
    public void shouldCreateMultipleNotificationsForDecisionChangeForPricing_BarByLOS() {
        when(mockConfigParamsService.getValue("pacman.BSTN", GUIConfigParamName.CORE_DISPLAY_PROPERTY_CODE_OR_NAME.value())).thenReturn("Code");
        List<InformationMgrAlertConfigEntity> listOfExcep = alertExceptionService.findAllExceptions();
        Map<String, List<InformationMgrAlertConfigEntity>> resultList = alertExceptionService.persistExceptionConfiguration(buildExceptionConfiguration(ExceptionSubType.PRICING.getCode(), LevelType.ROOM_CLASS.getCode(), SubLevelType.ALL_ROOM_CLASSES.getCode(), Arrays.asList(PROPERTY_ID5), true, "<=", 2.0, AlertType.DecisionChangeEx));
        tenantCrudService().flush();
        listOfExcep = resultList.get(SUCCESS);
        when(mockConfigParamsService.getParameterValue(AlertConfigParamName.USER_EXCEPTIONS_ENABLED)).thenReturn(true);
        when(mockConfigParamsService.getParameterValue(IPConfigParamName.BAR_BAR_DECISION.value())).thenReturn(Constants.BAR_DECISION_VALUE_LOS);
        when(mockConfigParamsService.getParameterValue(IPConfigParamName.BAR_MAX_LOS.value())).thenReturn("2");
        setExistingDataForMultipleLOSForDecisionChange(businessDate);
        instance.evaluateByType(listOfExcep, AlertType.DecisionChangeEx, new DecisionChangeFromLastNightlyOptimization(), false);
        List<InfoMgrExcepNotifEntity> list = fetchAllNotifications();
        thenAssertForMultipleNotifications(list);
        thenAssertForNotificationsContainsLOS(list);
    }

    @Test
    public void shouldIncreaseScoreOfMultipleNotificationsForDecisionChangeForPricing_BarByLOS() {
        when(mockConfigParamsService.getValue("pacman.BSTN", GUIConfigParamName.CORE_DISPLAY_PROPERTY_CODE_OR_NAME.value())).thenReturn("Code");
        List<InformationMgrAlertConfigEntity> listOfExcep = alertExceptionService.findAllExceptions();
        Map<String, List<InformationMgrAlertConfigEntity>> resultList = alertExceptionService.persistExceptionConfiguration(buildExceptionConfiguration(ExceptionSubType.PRICING.getCode(), LevelType.ROOM_CLASS.getCode(), SubLevelType.ALL_ROOM_CLASSES.getCode(), Arrays.asList(PROPERTY_ID5), true, "<=", 2.0, AlertType.DecisionChangeEx));
        tenantCrudService().flush();
        listOfExcep = resultList.get(SUCCESS);
        when(mockConfigParamsService.getParameterValue(AlertConfigParamName.USER_EXCEPTIONS_ENABLED)).thenReturn(true);
        when(mockConfigParamsService.getParameterValue(IPConfigParamName.BAR_BAR_DECISION.value())).thenReturn(Constants.BAR_DECISION_VALUE_LOS);
        when(mockConfigParamsService.getParameterValue(IPConfigParamName.BAR_MAX_LOS.value())).thenReturn("2");
        setExistingDataForMultipleLOSForDecisionChange(businessDate);
        instance.evaluateByType(listOfExcep, AlertType.DecisionChangeEx, new DecisionChangeFromLastNightlyOptimization(), false);
        List<InfoMgrExcepNotifEntity> listFromEvaluation = fetchAllNotifications();
        thenAssertForMultipleNotifications(listFromEvaluation);
        thenAssertForNotificationsContainsLOS(listFromEvaluation);
        thenAssertForNotificationsScore(40, listFromEvaluation);
        instance.evaluateByType(listOfExcep, AlertType.DecisionChangeEx, new DecisionChangeFromLastNightlyOptimization(), false);
        listFromEvaluation = fetchAllNotifications();
        thenAssertForMultipleNotifications(listFromEvaluation);
        thenAssertForNotificationsContainsLOS(listFromEvaluation);
        thenAssertForNotificationsScore(60, listFromEvaluation);
    }

    @Test
    public void shouldDecreaseScoreOfMultipleNotificationsForDecisionChangeForPricing_BarByLOS() {
        when(mockConfigParamsService.getValue("pacman.BSTN", GUIConfigParamName.CORE_DISPLAY_PROPERTY_CODE_OR_NAME.value())).thenReturn("Code");
        List<InformationMgrAlertConfigEntity> listOfExcep = alertExceptionService.findAllExceptions();
        Map<String, List<InformationMgrAlertConfigEntity>> resultList = alertExceptionService.persistExceptionConfiguration(buildExceptionConfiguration(ExceptionSubType.PRICING.getCode(), LevelType.ROOM_CLASS.getCode(), SubLevelType.ALL_ROOM_CLASSES.getCode(), Arrays.asList(PROPERTY_ID5), true, "<=", 2.0, AlertType.DecisionChangeEx));
        tenantCrudService().flush();
        listOfExcep = resultList.get(SUCCESS);
        when(mockConfigParamsService.getParameterValue(AlertConfigParamName.USER_EXCEPTIONS_ENABLED)).thenReturn(true);
        when(mockConfigParamsService.getParameterValue(IPConfigParamName.BAR_BAR_DECISION.value())).thenReturn(Constants.BAR_DECISION_VALUE_LOS);
        when(mockConfigParamsService.getParameterValue(IPConfigParamName.BAR_MAX_LOS.value())).thenReturn("2");
        setExistingDataForMultipleLOSForDecisionChange(businessDate);
        instance.evaluateByType(listOfExcep, AlertType.DecisionChangeEx, new DecisionChangeFromLastNightlyOptimization(), false);
        List<InfoMgrExcepNotifEntity> listFromEvaluation = fetchAllNotifications();
        thenAssertForMultipleNotifications(listFromEvaluation);
        thenAssertForNotificationsContainsLOS(listFromEvaluation);
        thenAssertForNotificationsScore(40, listFromEvaluation);
        // Check if score decreases after executing service again with changes increase in threshold.
        for (InformationMgrAlertConfigEntity configEntity : listOfExcep) {
            configEntity.setThresholdValue(new BigDecimal(8));
        }
        instance.evaluateByType(listOfExcep, AlertType.DecisionChangeEx, new DecisionChangeFromLastNightlyOptimization(), false);
        listFromEvaluation = fetchAllNotifications();
        thenAssertForMultipleNotifications(listFromEvaluation);
        thenAssertForNotificationsContainsLOS(listFromEvaluation);
        thenAssertForNotificationsScore(20, listFromEvaluation);
    }

    @Test
    public void shouldCreatePricingByValueNotificationWhenDecreasedOrIncreasedByThreasoldForBARByDAY() {
        Date lastBusinessDate = new GregorianCalendar(2010, Calendar.JANUARY, 01).getTime();
        when(mockConfigParamsService.getValue("pacman.BSTN", GUIConfigParamName.CORE_DISPLAY_PROPERTY_CODE_OR_NAME.value())).thenReturn("Code");
        List<InformationMgrAlertConfigEntity> listOfExcep = alertExceptionService.findAllExceptions();
        Map<String, List<InformationMgrAlertConfigEntity>> resultList = alertExceptionService.persistExceptionConfiguration(buildExceptionConfiguration(ExceptionSubType.PRICING_BY_VALUE.getCode(), LevelType.ROOM_CLASS.getCode(), SubLevelType.ALL_ROOM_CLASSES.getCode(), Arrays.asList(PROPERTY_ID5), true, "<>=", 1500.0, AlertType.DecisionChangeEx));
        listOfExcep = resultList.get(SUCCESS);
        when(mockConfigParamsService.getParameterValue(AlertConfigParamName.USER_EXCEPTIONS_ENABLED)).thenReturn(true);
        List<InfoMgrExcepNotifEntity> list = null;
        Date currentBusinessDate = new GregorianCalendar(2010, Calendar.JANUARY, 02).getTime();
        setPricingDataForPricingByValue(lastBusinessDate, 1500.0, -1);
        // diff exactly 1500
        setPricingDataForPricingByValue(currentBusinessDate, 3000.0, -1);
        setPricingDataForPricingByValueForNotification(lastBusinessDate, 1500.0, -1);
        // diff exactly 1500
        setPricingDataForPricingByValueForNotification(currentBusinessDate, 3000.0, -1);
        instance.evaluateByType(listOfExcep, AlertType.DecisionChangeEx, new DecisionChangeFromLastNightlyOptimization(), false);
        // evaluated first time
        list = fetchAllNotifications();
        assertionsForPricingByValue(list, 1);
        InfoMgrExcepNotifEntity infoMgrExcepNotifEntity = list.get(0);
        assertFalse(infoMgrExcepNotifEntity.getDetails().contains("LOS:"));
        assertTrue(infoMgrExcepNotifEntity.getScore() == 20);
        // evaluated again - it should increment score
        instance.evaluateByType(listOfExcep, AlertType.DecisionChangeEx, new DecisionChangeFromLastNightlyOptimization(), false);
        list = fetchAllNotifications();
        assertionsForPricingByValue(list, 1);
        infoMgrExcepNotifEntity = list.get(0);
        assertFalse(infoMgrExcepNotifEntity.getDetails().contains("LOS:"));
        // score incremented and not generated new notification after reevaluations
        assertEquals(30, infoMgrExcepNotifEntity.getScore());
    }

    @Test
    public void shouldCreatePricingByValueNotificationWhenDecreasedOrIncreasedByThreasoldForBARByLOS() {
        Date lastBusinessDate = new GregorianCalendar(2010, Calendar.JANUARY, 01).getTime();
        when(mockConfigParamsService.getValue("pacman.BSTN", GUIConfigParamName.CORE_DISPLAY_PROPERTY_CODE_OR_NAME.value())).thenReturn("Code");
        List<InformationMgrAlertConfigEntity> listOfExcep = alertExceptionService.findAllExceptions();
        Map<String, List<InformationMgrAlertConfigEntity>> resultList = alertExceptionService.persistExceptionConfiguration(buildExceptionConfiguration(ExceptionSubType.PRICING_BY_VALUE.getCode(), LevelType.ROOM_CLASS.getCode(), SubLevelType.ALL_ROOM_CLASSES.getCode(), Arrays.asList(PROPERTY_ID5), true, "<>=", 1500.0, AlertType.DecisionChangeEx));
        listOfExcep = resultList.get(SUCCESS);
        when(mockConfigParamsService.getParameterValue(AlertConfigParamName.USER_EXCEPTIONS_ENABLED)).thenReturn(true);
        List<InfoMgrExcepNotifEntity> list = null;
        Date currentBusinessDate = new GregorianCalendar(2010, Calendar.JANUARY, 02).getTime();
        setPricingDataForPricingByValue(lastBusinessDate, 1500.0, 5);
        // diff exactly 1500
        setPricingDataForPricingByValue(currentBusinessDate, 3000.0, 5);
        setExistingDataForTest(businessDate);
        setPricingDataForPricingByValueForNotification(lastBusinessDate, 1500.0, 5);
        // diff exactly 1500
        setPricingDataForPricingByValueForNotification(currentBusinessDate, 3000.0, 5);
        instance.evaluateByType(listOfExcep, AlertType.DecisionChangeEx, new DecisionChangeFromLastNightlyOptimization(), false);
        // evaluated first time
        list = fetchAllNotifications();
        assertionsForPricingByValue(list, 1);
        InfoMgrExcepNotifEntity infoMgrExcepNotifEntity = list.get(0);
        assertTrue(infoMgrExcepNotifEntity.getDetails().contains(Constants.IM_LABEL_LOS_ABBREVIATED + ":5"));
        assertEquals(20, infoMgrExcepNotifEntity.getScore());
        // evaluated again - it should increment score
        instance.evaluateByType(listOfExcep, AlertType.DecisionChangeEx, new DecisionChangeFromLastNightlyOptimization(), false);
        list = fetchAllNotifications();
        infoMgrExcepNotifEntity = list.get(0);
        assertTrue(infoMgrExcepNotifEntity.getDetails().contains(Constants.IM_LABEL_LOS_ABBREVIATED + ":5"));
        // score incremented
        assertEquals(30, infoMgrExcepNotifEntity.getScore());
        setPricingDataForPricingByValueForNotification(lastBusinessDate, 2000.0, 1);
        // increase by 1500+
        setPricingDataForPricingByValueForNotification(currentBusinessDate, 3600.0, 1);
        setPricingDataForPricingByValueForNotification(lastBusinessDate, 3500.0, 3);
        // decreased by 1500-
        setPricingDataForPricingByValueForNotification(currentBusinessDate, 1500.0, 3);
        instance.evaluateByType(listOfExcep, AlertType.DecisionChangeEx, new DecisionChangeFromLastNightlyOptimization(), false);
        list = fetchAllNotifications();
        // evaluated again and it should have 3 notifications generated for 5,1,3 LOSs
        assertionsForPricingByValue(list, 3);
        for (InfoMgrExcepNotifEntity notifEntity : list) {
            assertTrue(notifEntity.getDetails().contains(Constants.IM_LABEL_LOS_ABBREVIATED + ":"));
            assertFalse(notifEntity.getDetails().contains(Constants.IM_LABEL_LOS_ABBREVIATED + ":-1"));
        }
        // created data condition for los=-1 it should generate 4 notifications after evaluation
        // but should not contain LOS=-1 in details
        setPricingDataForPricingByValueForNotification(lastBusinessDate, 1500.0, -1);
        // diff exactly 1500
        setPricingDataForPricingByValueForNotification(currentBusinessDate, 3000.0, -1);
        instance.evaluateByType(listOfExcep, AlertType.DecisionChangeEx, new DecisionChangeFromLastNightlyOptimization(), false);
        list = fetchAllNotifications();
        assertionsForPricingByValue(list, 4);
        for (InfoMgrExcepNotifEntity notifEntity : list) {
            assertFalse(notifEntity.getDetails().contains(Constants.IM_LABEL_LOS_ABBREVIATED + ":-1"));
        }
    }

    @Test
    public void shouldCreateMultipleNotificationsForDecisionAsOfLastOptimizationForPricingByRank_BarByLOS() {
        List<InformationMgrAlertConfigEntity> givenListOfExcep = setupForDecisionAsOfLastOptimizationForPricingByRank_BarByLOS();
        whenCall_evaluateByType_ForDecisionAsOfLastOptimization(givenListOfExcep);
        List<InfoMgrExcepNotifEntity> listFromEvaluation = fetchAllNotifications();
        thenAssertForMultipleNotifications(listFromEvaluation);
        thenAssertForNotificationsContainsLOS(listFromEvaluation);
    }

    @Test
    public void shouldIncreaseScoreOfMultipleNotificationsForDecisionAsOfLastOptimizationForPricingByRank_BarByLOS() {
        List<InformationMgrAlertConfigEntity> givenListOfExcep = setupForDecisionAsOfLastOptimizationForPricingByRank_BarByLOS();
        whenCall_evaluateByType_ForDecisionAsOfLastOptimization(givenListOfExcep);
        List<InfoMgrExcepNotifEntity> listFromEvaluation = fetchAllNotifications();
        thenAssertForMultipleNotifications(listFromEvaluation);
        thenAssertForNotificationsContainsLOS(listFromEvaluation);
        thenAssertForNotificationsScore(30, listFromEvaluation);
        whenCall_evaluateByType_ForDecisionAsOfLastOptimization(givenListOfExcep);
        listFromEvaluation = fetchAllNotifications();
        thenAssertForMultipleNotifications(listFromEvaluation);
        thenAssertForNotificationsContainsLOS(listFromEvaluation);
        thenAssertForNotificationsScore(45, listFromEvaluation);
    }

    @Test
    public void shouldCreateMultipleNotificationsForDecisionAsOfLastNightlyOptimizationForPricingByRank_BarByLOS() {
        List<InformationMgrAlertConfigEntity> givenListOfExcep = setupForDecisionAsOfLastNightlyOptimizationForPricingByRank_BarByLOS();
        whenCall_evaluateByType_ForDecisionAsOfLastNightlyOptimization(givenListOfExcep);
        List<InfoMgrExcepNotifEntity> listFromEvaluation = fetchAllNotifications();
        thenAssertForMultipleNotifications(listFromEvaluation);
        thenAssertForNotificationsContainsLOS(listFromEvaluation);
    }

    @Test
    public void shouldIncreaseScoreOfMultipleNotificationsForDecisionAsOfLastNightlyOptimizationForPricingByRank_BarByLOS() {
        List<InformationMgrAlertConfigEntity> givenListOfExcep = setupForDecisionAsOfLastNightlyOptimizationForPricingByRank_BarByLOS();
        whenCall_evaluateByType_ForDecisionAsOfLastNightlyOptimization(givenListOfExcep);
        List<InfoMgrExcepNotifEntity> listFromEvaluation = fetchAllNotifications();
        thenAssertForMultipleNotifications(listFromEvaluation);
        thenAssertForNotificationsContainsLOS(listFromEvaluation);
        thenAssertForNotificationsScore(50, listFromEvaluation);
        whenCall_evaluateByType_ForDecisionAsOfLastNightlyOptimization(givenListOfExcep);
        listFromEvaluation = fetchAllNotifications();
        thenAssertForMultipleNotifications(listFromEvaluation);
        thenAssertForNotificationsContainsLOS(listFromEvaluation);
        thenAssertForNotificationsScore(75, listFromEvaluation);
    }

    private void assertionsForPricingByValue(List<InfoMgrExcepNotifEntity> list, int expectedGeneratedNotificationCount) {
        assertNotNull(list, "Notification list is empty ");
        assertTrue(list.size() == expectedGeneratedNotificationCount, "Notification list should have " + expectedGeneratedNotificationCount + " entries but has " + list.size());
    }

    private void setPricingDataForPricingByValue(Date businessDate, Double price, int los) {
        String businessDateStr = new SimpleDateFormat("yyyy-MM-dd").format(businessDate).toString().trim();
        String dateInBetweenRangeStr = "2010-11-10";
        insertPaceBarOutputRecordForPricingByValue(dateInBetweenRangeStr, getMaxDecisionForGivenBusinessDate(businessDateStr), 4, price, los);
    }

    private void setPricingDataForPricingByValueForNotification(Date businessDate, Double price, int los) {
        String businessDateStr = new SimpleDateFormat("yyyy-MM-dd").format(businessDate).toString().trim();
        String dateInBetweenRangeStr = "2010-11-10";
        insertPaceBarOutputNotificationRecordForPricingByValue(dateInBetweenRangeStr, getMaxDecisionForGivenBusinessDate(businessDateStr), 4, price, los);
    }

    private void insertPaceBarOutputRecordForPricingByValue(String dateInBetweenRangeStr, BigInteger decisionId, Integer rateUnqualifiedId, Double derivedUnqualifiedValue, int los) {
        Query qryAccom = tenantCrudService().getEntityManager().createNativeQuery("INSERT INTO PACE_BAR_OUTPUT(Decision_ID, Property_ID, ACCOM_CLASS_ID " + " ,Arrival_DT, RATE_UNQUALIFIED_ID, Derived_Unqualified_Value, LOS,OVERRIDE,DECISION_REASON_TYPE_ID,month_ID,year_id,CreateDate)VALUES(:decisionId,5,2,:dateInBetween,:rateUnqualifiedId" + ",:derivedUnqualifiedValue,:los,'NONE',1,1,1,GETDATE())");
        qryAccom.setParameter("decisionId", decisionId);
        qryAccom.setParameter("dateInBetween", dateInBetweenRangeStr);
        qryAccom.setParameter("rateUnqualifiedId", rateUnqualifiedId);
        qryAccom.setParameter("derivedUnqualifiedValue", derivedUnqualifiedValue);
        qryAccom.setParameter("los", los);
        qryAccom.executeUpdate();
    }

    private void insertPaceBarOutputNotificationRecordForPricingByValue(String dateInBetweenRangeStr, BigInteger decisionId, Integer rateUnqualifiedId, Double derivedUnqualifiedValue, int los) {
        Query qryAccom = tenantCrudService().getEntityManager().createNativeQuery("INSERT INTO PACE_Bar_Output_NOTIFICATION(Decision_ID, Property_ID, ACCOM_CLASS_ID " + " ,Arrival_DT, RATE_UNQUALIFIED_ID, Derived_Unqualified_Value, LOS,OVERRIDE,DECISION_REASON_TYPE_ID,month_ID,year_id,CreateDate)VALUES(:decisionId,5,2,:dateInBetween,:rateUnqualifiedId" + ",:derivedUnqualifiedValue,:los,'NONE',1,1,1,GETDATE())");
        qryAccom.setParameter("decisionId", decisionId);
        qryAccom.setParameter("dateInBetween", dateInBetweenRangeStr);
        qryAccom.setParameter("rateUnqualifiedId", rateUnqualifiedId);
        qryAccom.setParameter("derivedUnqualifiedValue", derivedUnqualifiedValue);
        qryAccom.setParameter("los", los);
        qryAccom.executeUpdate();
    }

    private void assertionsForRateOfDay(List<InfoMgrExcepNotifEntity> list, int score) {
        assertNotNull(list);
        assertTrue(list.size() == 2);
        assertTrue(list.get(0).getCurrentOptimizationValue().equals("LV4"));
        assertTrue(list.get(0).getLastOptimizationValue().equals("LV8"));
        assertEquals(score, list.get(0).getScore());
    }

    private void setExistingDataForTest(Date businessDate) {
        String businessDateStr = new SimpleDateFormat("yyyy-MM-dd").format(businessDate).toString().trim();
        String previousBusinessDateStr = new SimpleDateFormat("yyyy-MM-dd").format(addDaysToDate(businessDate, -1)).toString().trim();
        String dateForPreviousDay = "2010-11-01";
        insertPaceBarNotificationOutputRecord(dateForPreviousDay, 2, -1, getMaxDecisionForGivenBusinessDate(businessDateStr).subtract(BigInteger.ONE), 8);
        insertPaceBarNotificationOutputRecord(dateForPreviousDay, 2, -1, getMaxDecisionForGivenBusinessDate(businessDateStr), 8);
        insertPaceBarNotificationOutputRecord(dateForPreviousDay, 2, -1, getMaxDecisionForGivenBusinessDate(previousBusinessDateStr), 12);
        insertPaceBarNotificationOutputRecord(dateForPreviousDay, 2, 1, getMaxDecisionForGivenBusinessDate(businessDateStr).subtract(BigInteger.ONE), 8);
        insertPaceBarNotificationOutputRecord(dateForPreviousDay, 2, 1, getMaxDecisionForGivenBusinessDate(businessDateStr), 8);
        insertPaceBarNotificationOutputRecord(dateForPreviousDay, 2, 1, getMaxDecisionForGivenBusinessDate(previousBusinessDateStr), 4);
    }

    private void setPricingData(Date businessDate) {
        String businessDateStr = new SimpleDateFormat("yyyy-MM-dd").format(businessDate).toString().trim();
        String previousBusinessDateStr = new SimpleDateFormat("yyyy-MM-dd").format(addDaysToDate(businessDate, -1)).toString().trim();
        String dateForPreviousDay = "2010-11-01";
        String dateInBetweenRangeStr = "2010-11-02";
        insertPaceBarOutputRecord(dateInBetweenRangeStr, 4, getMaxDecisionForGivenBusinessDate(businessDateStr), 4);
        insertPaceBarOutputRecord(dateInBetweenRangeStr, 4, getMaxDecisionForGivenBusinessDate(previousBusinessDateStr), 8);
        insertPaceBarNotificationOutputRecord(dateInBetweenRangeStr, 4, -1, getMaxDecisionForGivenBusinessDate(businessDateStr), 4);
        insertPaceBarNotificationOutputRecord(dateInBetweenRangeStr, 4, -1, getMaxDecisionForGivenBusinessDate(previousBusinessDateStr), 8);
        setExistingDataForTest(businessDate);
    }

    private BigInteger getMaxDecisionForGivenBusinessDate(String businessDateStr) {
        // get the Max of Decision for business  date
        Query qry = tenantCrudService().getEntityManager().createNativeQuery("select MAX(Decision_ID) from Decision where Decision_Type_ID =1 and Business_DT=:date and property_id = 5");
        qry.setParameter("date", businessDateStr);
        return (BigInteger) qry.getSingleResult();
    }

    private void insertPaceBarOutputRecord(String dateInBetweenRangeStr, int accomClassId, BigInteger decisionId, Integer rateUnqualifiedId) {
        // insert the records into [PACE_BAR_OUTPUT] table
        Map<String, Object> parameter = new HashMap<>();
        parameter.put("accomClassId", accomClassId);
        parameter.put("decisionId", decisionId);
        parameter.put("dateInBetween", dateInBetweenRangeStr);
        parameter.put("rateUnqualifiedId", rateUnqualifiedId);
        tenantCrudService().executeUpdateByNativeQuery("INSERT INTO PACE_BAR_OUTPUT(Decision_ID, Property_ID, ACCOM_CLASS_ID " + " ,Arrival_DT, RATE_UNQUALIFIED_ID, LOS,OVERRIDE,DECISION_REASON_TYPE_ID,month_ID,year_id,CreateDate)VALUES(:decisionId,5,:accomClassId,:dateInBetween,:rateUnqualifiedId" + ",-1,'NONE',1,1,1,GETDATE())", parameter);
    }

    private void insertPaceBarNotificationOutputRecord(String dateInBetweenRangeStr, int accomClassId, int los, BigInteger decisionId, Integer rateUnqualifiedId) {
        // insert the records into [PACE_Bar_Output_NOTIFICATION] table
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("los", los);
        parameters.put("accomClassId", accomClassId);
        parameters.put("decisionId", decisionId);
        parameters.put("dateInBetween", dateInBetweenRangeStr);
        parameters.put("rateUnqualifiedId", rateUnqualifiedId);
        tenantCrudService().executeUpdateByNativeQuery("INSERT INTO PACE_Bar_Output_NOTIFICATION(Decision_ID, Property_ID, ACCOM_CLASS_ID " + " ,Arrival_DT, RATE_UNQUALIFIED_ID, LOS,OVERRIDE,DECISION_REASON_TYPE_ID,month_ID,year_id,CreateDate)VALUES(:decisionId,5,:accomClassId,:dateInBetween,:rateUnqualifiedId" + ",:los,'NONE',1,1,1,GETDATE())", parameters);
    }

    private List<InfoMgrExcepNotifEntity> fetchAllNotifications() {
        return tenantCrudService().findAll(InfoMgrExcepNotifEntity.class);
    }

    private void assertionsForBarByLOS(List<InfoMgrExcepNotifEntity> list, int score) {
        assertNotNull(list);
        assertTrue(list.size() == 1);
        assertTrue(list.get(0).getCurrentOptimizationValue().equals("LV4"));
        assertTrue(list.get(0).getLastOptimizationValue().equals("LV0"));
        assertEquals(score, list.get(0).getScore());
    }

    private ExceptionConfigDTO buildExceptionConfiguration(String subType, String levelType, String sublevelType, List<Integer> listPropertyIds, boolean subLevelHasKeyword, String thresholdConstraint, double thresholdValue, AlertType alertType) {
        ExceptionConfigDTO objExceptionConfigDTO = new ExceptionConfigDTO();
        InfoMgrTypeEntity type = tenantCrudService().findByNamedQuerySingleResult(InfoMgrTypeEntity.BY_NAME, QueryParameter.with("name", alertType.toString()).parameters());
        InformationMgrLevelEntity objExceptionLevelEntity = tenantCrudService().findByNamedQuerySingleResult(InformationMgrLevelEntity.BY_NAME, QueryParameter.with("name", levelType).parameters());
        InformationMgrSubTypeEntity objExceptionSubTypeEntity = tenantCrudService().findByNamedQuerySingleResult(InformationMgrSubTypeEntity.BY_NAME, QueryParameter.with("name", subType).parameters());
        objExceptionConfigDTO.setAlertTypeEntity(type);
        objExceptionConfigDTO.setDisabled(false);
        objExceptionConfigDTO.setEndDate("11/10/2010");
        objExceptionConfigDTO.setExceptionLevel(objExceptionLevelEntity);
        objExceptionConfigDTO.setExceptionSubLevel(sublevelType);
        objExceptionConfigDTO.setExceptionSubType(objExceptionSubTypeEntity);
        objExceptionConfigDTO.setFrequency(FREQUENCY);
        objExceptionConfigDTO.setPropertyIds(listPropertyIds);
        objExceptionConfigDTO.setStartDate("11/01/2010");
        objExceptionConfigDTO.setThresholdConstraint(thresholdConstraint);
        objExceptionConfigDTO.setThresholdValue(new BigDecimal(thresholdValue));
        objExceptionConfigDTO.setStatusId(1);
        if ("PRICING_BY_VALUE".equals(subType)) {
            objExceptionConfigDTO.setMetricType(MetricType.CURRENCY);
        } else {
            objExceptionConfigDTO.setMetricType(MetricType.PRICE_POINT);
        }
        objExceptionConfigDTO.setSubLevelHasKeyword(subLevelHasKeyword);
        return objExceptionConfigDTO;
    }

    @Test
    public void testDecisionChangeForPricingByRankImpl() {
        List<Integer> listPropertyIds = new ArrayList<>();
        listPropertyIds.add(PROPERTY_ID5);
        UserService userService = Mockito.mock(UserService.class);
        inject(authService, "userService", userService);
        RoleService roleService = Mockito.mock(RoleService.class);
        inject(authService, "roleService", roleService);
        LDAPUser u1 = new LDAPUser();
        u1.setUserId(1);
        ArrayList<String> roleList = new ArrayList<>();
        roleList.add("roleId=-666 ON GROUP groupId=-666");
        u1.setRoles(roleList);
        when(userService.getById("1")).thenReturn(u1);
        when(roleService.getPropertiesForUser(nullable(String.class), anyObject())).thenReturn(Sets.newHashSet("5"));
        when(roleService.getPermsForUser(anyString(), anyString(), anyObject())).thenReturn(Sets.newHashSet("-666"));
        when(mockConfigParamsService.getValue("pacman.BSTN", GUIConfigParamName.CORE_DISPLAY_PROPERTY_CODE_OR_NAME.value())).thenReturn("Code");
        List<InformationMgrAlertConfigEntity> listOfExcep = alertExceptionService.findAllExceptions();
        Map<String, List<InformationMgrAlertConfigEntity>> resultList = alertExceptionService.persistExceptionConfiguration(buildExceptionConfiguration(ExceptionSubType.PRICING.getCode(), LevelType.ROOM_CLASS.getCode(), SubLevelType.MASTER_CLASS.getCode(), listPropertyIds, true, "<>", 50.0, AlertType.DecisionChangeEx));
        tenantCrudService().flush();
        listOfExcep = resultList.get(SUCCESS);
        Integer infoMgrAlertConfigEntityId = listOfExcep.get(0).getId();
        addDummyRowForTesting(infoMgrAlertConfigEntityId);
        List<ExceptionAlert> listOfExcepAlerts = exceptionAlertService.getNewExceptionAlerts();
        ExceptionAlert enhancedExceptionDtoForUI = exceptionAlertService.addExceptionConfigurationDetailsToAlert(listOfExcepAlerts.get(0));
        assertNotNull(listOfExcepAlerts);
        assertEquals(1, listOfExcepAlerts.size());
        assertEquals("LV5", enhancedExceptionDtoForUI.getCurrentOptimizationValue());
        assertEquals("LV4", enhancedExceptionDtoForUI.getLastOptVal());
        assertEquals("9", enhancedExceptionDtoForUI.getMaxRankForPricing());
        assertEquals("1", enhancedExceptionDtoForUI.getMinRankForPricing());
    }

    @Test
    public void testDecisionChangeForPricingByRankImplUsingNewCache() {
        List<Integer> listPropertyIds = new ArrayList<>();
        listPropertyIds.add(PROPERTY_ID5);
        UserService userService = Mockito.mock(UserService.class);
        inject(authService, "userService", userService);
        RoleService roleService = Mockito.mock(RoleService.class);
        inject(authService, "roleService", roleService);
        LDAPUser u1 = new LDAPUser();
        u1.setUserId(1);
        ArrayList<String> roleList = new ArrayList<>();
        roleList.add("roleId=-666 ON GROUP groupId=-666");
        u1.setRoles(roleList);
        when(userService.getById("1")).thenReturn(u1);
        when(roleService.getPropertiesForUser(nullable(String.class), anyObject())).thenReturn(Sets.newHashSet("5"));
        when(roleService.getPermsForUser(anyString(), anyString(), anyObject())).thenReturn(Sets.newHashSet("-666"));
        when(mockConfigParamsService.getValue("pacman.BSTN", GUIConfigParamName.CORE_DISPLAY_PROPERTY_CODE_OR_NAME.value())).thenReturn("Code");
        List<InformationMgrAlertConfigEntity> listOfExcep = alertExceptionService.findAllExceptions();
        Map<String, List<InformationMgrAlertConfigEntity>> resultList = alertExceptionService.persistExceptionConfiguration(buildExceptionConfiguration(ExceptionSubType.PRICING.getCode(), LevelType.ROOM_CLASS.getCode(), SubLevelType.MASTER_CLASS.getCode(), listPropertyIds, true, "<>", 50.0, AlertType.DecisionChangeEx));
        tenantCrudService().flush();
        listOfExcep = resultList.get(SUCCESS);
        Integer infoMgrAlertConfigEntityId = listOfExcep.get(0).getId();
        addDummyRowForTesting(infoMgrAlertConfigEntityId);
        List<ExceptionAlert> listOfExcepAlerts = exceptionAlertService.getNewExceptionAlerts();
        ExceptionAlert enhancedExceptionDtoForUI = exceptionAlertService.addExceptionConfigurationDetailsToAlert(listOfExcepAlerts.get(0));
        assertNotNull(listOfExcepAlerts);
        assertEquals(1, listOfExcepAlerts.size());
        assertEquals("LV5", enhancedExceptionDtoForUI.getCurrentOptimizationValue());
        assertEquals("LV4", enhancedExceptionDtoForUI.getLastOptVal());
        assertEquals("9", enhancedExceptionDtoForUI.getMaxRankForPricing());
        assertEquals("1", enhancedExceptionDtoForUI.getMinRankForPricing());
    }

    private void addDummyRowForTesting(Integer infoMgrAlertConfigEntityId) {
        Query qryInfoMgrInstance = tenantCrudService().getEntityManager().createNativeQuery("INSERT INTO Info_Mgr_Instance (Info_Mgr_Type_ID, Alert_Description, Alert_Detail" + ", Property_ID, Status_ID, Created_By, Created_Date, Score, Last_Modification_Date, Info_Mgr_Status_ID, Discriminator" + ", Occupancy_Date, Info_Mgr_Excep_Notif_Sub_Type_ID, Info_Mgr_Excep_Notif_Config_Id, Skip_Occupancy_Date, Current_Optimization_Value" + ", Last_Optimization_Value)" + "VALUES(17, 'Decision Change From Last Optimization'" + ", 'Metric:Pricing,Occupancy Date:17-Aug-2011,Room Class:Master Class (STD),Current Price:LV5,Pricing Change:1.00,Defined Threshold:<> 1.00'" + ", 5, 1, 'SSO User', '2012-10-25 10:22:12.053', 18, '2013-01-03 17:22:39.457', 1, 'Exception', '2011-08-17 00:00:00.000',3" + ", :exceptionNotifId, 0, 'LV5', 'LV4')");
        qryInfoMgrInstance.setParameter("exceptionNotifId", infoMgrAlertConfigEntityId);
        qryInfoMgrInstance.executeUpdate();
    }

    private void setExistingDataForMultipleLOSForDecisionChange(Date businessDate) {
        setExistingDataForTest(businessDate);
        setExistingDataForMultipleLOSTest(businessDate);
    }

    private void setExistingDataForMultipleLOSTestForDecisionAsOfLastOptimizationByRank(Date businessDate) {
        setExistingDataForTest(businessDate);
        setExistingDataForMultipleLOSTest(businessDate);
    }

    private void setExistingDataForMultipleLOSTest(Date businessDate) {
        String businessDateStr = new SimpleDateFormat("yyyy-MM-dd").format(businessDate).toString().trim();
        String previousBusinessDateStr = new SimpleDateFormat("yyyy-MM-dd").format(addDaysToDate(businessDate, -1)).toString().trim();
        String dateForPreviousDay = "2010-11-01";
        insertPaceBarNotificationOutputRecord(dateForPreviousDay, 2, 2, getMaxDecisionForGivenBusinessDate(businessDateStr).subtract(BigInteger.ONE), 8);
        insertPaceBarNotificationOutputRecord(dateForPreviousDay, 2, 2, getMaxDecisionForGivenBusinessDate(businessDateStr), 8);
        insertPaceBarNotificationOutputRecord(dateForPreviousDay, 2, 2, getMaxDecisionForGivenBusinessDate(previousBusinessDateStr), 4);
    }

    private void setExistingDataForMultipleLOSTestForDecisionAsOfLastNightlyOptimizationByRank(Date businessDate) {
        String businessDateStr = new SimpleDateFormat("yyyy-MM-dd").format(businessDate).toString().trim();
        String previousBusinessDateStr = new SimpleDateFormat("yyyy-MM-dd").format(addDaysToDate(businessDate, -1)).toString().trim();
        String dateForPreviousDay = "2010-11-01";
        insertPaceBarNotificationOutputRecord(dateForPreviousDay, 2, 1, getMaxDecisionForGivenBusinessDate(businessDateStr).subtract(BigInteger.ONE), 8);
        insertPaceBarNotificationOutputRecord(dateForPreviousDay, 2, 1, getMaxDecisionForGivenBusinessDate(businessDateStr), 8);
        insertPaceBarNotificationOutputRecord(dateForPreviousDay, 2, 1, getMaxDecisionForGivenBusinessDate(previousBusinessDateStr), 12);
        insertPaceBarNotificationOutputRecord(dateForPreviousDay, 2, 2, getMaxDecisionForGivenBusinessDate(businessDateStr).subtract(BigInteger.ONE), 8);
        insertPaceBarNotificationOutputRecord(dateForPreviousDay, 2, 2, getMaxDecisionForGivenBusinessDate(businessDateStr), 8);
        insertPaceBarNotificationOutputRecord(dateForPreviousDay, 2, 2, getMaxDecisionForGivenBusinessDate(previousBusinessDateStr), 12);
    }

    private void thenAssertForMultipleNotifications(List<InfoMgrExcepNotifEntity> listFromEvaluation) {
        assertNotNull(listFromEvaluation);
        assertTrue(listFromEvaluation.size() == 2);
    }

    private void thenAssertForNotificationsScore(int expectedScore, List<InfoMgrExcepNotifEntity> listFromEvaluation) {
        assertTrue(listFromEvaluation.get(0).getScore() == expectedScore);
        assertTrue(listFromEvaluation.get(1).getScore() == expectedScore);
    }

    private void thenAssertForNotificationsContainsLOS(List<InfoMgrExcepNotifEntity> listFromEvaluation) {
        assertTrue(listFromEvaluation.get(0).getDetails().contains("LOS:1"));
        assertTrue(listFromEvaluation.get(1).getDetails().contains("LOS:2"));
    }

    private List<InformationMgrAlertConfigEntity> setupForDecisionAsOfLastOptimizationForPricingByRank_BarByLOS() {
        when(mockConfigParamsService.getValue("pacman.BSTN", GUIConfigParamName.CORE_DISPLAY_PROPERTY_CODE_OR_NAME.value())).thenReturn("Code");
        List<InformationMgrAlertConfigEntity> listOfExcep = alertExceptionService.findAllExceptions();
        Map<String, List<InformationMgrAlertConfigEntity>> resultList = alertExceptionService.persistExceptionConfiguration(buildExceptionConfiguration(ExceptionSubType.PRICING.getCode(), LevelType.ROOM_CLASS.getCode(), SubLevelType.ALL_ROOM_CLASSES.getCode(), Arrays.asList(PROPERTY_ID5), true, ">=", 2.0, AlertType.DecisionAsOfLastOptimization));
        tenantCrudService().flush();
        listOfExcep = resultList.get(SUCCESS);
        when(mockConfigParamsService.getParameterValue(AlertConfigParamName.USER_EXCEPTIONS_ENABLED)).thenReturn(true);
        when(mockConfigParamsService.getParameterValue(IPConfigParamName.BAR_BAR_DECISION.value())).thenReturn(Constants.BAR_DECISION_VALUE_LOS);
        when(mockConfigParamsService.getParameterValue(IPConfigParamName.BAR_MAX_LOS.value())).thenReturn("2");
        setExistingDataForMultipleLOSTestForDecisionAsOfLastOptimizationByRank(businessDate);
        return listOfExcep;
    }

    private List<InformationMgrAlertConfigEntity> setupForDecisionAsOfLastNightlyOptimizationForPricingByRank_BarByLOS() {
        when(mockConfigParamsService.getValue("pacman.BSTN", GUIConfigParamName.CORE_DISPLAY_PROPERTY_CODE_OR_NAME.value())).thenReturn("Code");
        List<InformationMgrAlertConfigEntity> listOfExcep = alertExceptionService.findAllExceptions();
        Map<String, List<InformationMgrAlertConfigEntity>> resultList = alertExceptionService.persistExceptionConfiguration(buildExceptionConfiguration(ExceptionSubType.PRICING.getCode(), LevelType.ROOM_CLASS.getCode(), SubLevelType.ALL_ROOM_CLASSES.getCode(), Arrays.asList(PROPERTY_ID5), true, "<=", 2.0, AlertType.DecisionAsOfLastNightlyOptimization));
        tenantCrudService().flush();
        listOfExcep = resultList.get(SUCCESS);
        when(mockConfigParamsService.getParameterValue(AlertConfigParamName.USER_EXCEPTIONS_ENABLED)).thenReturn(true);
        when(mockConfigParamsService.getParameterValue(IPConfigParamName.BAR_BAR_DECISION.value())).thenReturn(Constants.BAR_DECISION_VALUE_LOS);
        when(mockConfigParamsService.getParameterValue(IPConfigParamName.BAR_MAX_LOS.value())).thenReturn("2");
        setExistingDataForMultipleLOSTestForDecisionAsOfLastNightlyOptimizationByRank(businessDate);
        return listOfExcep;
    }

    private void whenCall_evaluateByType_ForDecisionAsOfLastOptimization(List<InformationMgrAlertConfigEntity> givenListOfExcep) {
        instance.evaluateByType(givenListOfExcep, AlertType.DecisionAsOfLastOptimization, new DecisionAsOfLastOptimization(), false);
    }

    private void whenCall_evaluateByType_ForDecisionAsOfLastNightlyOptimization(List<InformationMgrAlertConfigEntity> givenListOfExcep) {
        instance.evaluateByType(givenListOfExcep, AlertType.DecisionAsOfLastNightlyOptimization, new DecisionAsOfLastNightlyOptimization(), false);
    }

    private void whenChangeThresholdValue(List<InformationMgrAlertConfigEntity> listOfExcep, BigDecimal changeBy) {
        for (InformationMgrAlertConfigEntity configEntity : listOfExcep) {
            configEntity.setThresholdValue(changeBy);
        }
    }
}
