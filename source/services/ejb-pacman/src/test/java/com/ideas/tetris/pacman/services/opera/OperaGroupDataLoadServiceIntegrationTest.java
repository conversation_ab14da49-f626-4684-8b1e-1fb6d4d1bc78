package com.ideas.tetris.pacman.services.opera;

import com.ideas.g3.rule.CrudServiceBeanExtension;
import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.configparams.IntegrationConfigParamName;
import com.ideas.tetris.pacman.common.configparams.RemoteAgentConfigParamName;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomType;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.FileMetadata;
import com.ideas.tetris.pacman.services.groupblock.GroupBlockDetail;
import com.ideas.tetris.pacman.services.groupblock.GroupBlockMaster;
import com.ideas.tetris.pacman.services.groupblock.OperaGroupBlockCode;
import com.ideas.tetris.pacman.services.groupblock.PaceGroupBlock;
import com.ideas.tetris.pacman.services.groupblock.PaceGroupMaster;
import com.ideas.tetris.pacman.services.marketsegment.entity.MarketSegmentSummary;
import com.ideas.tetris.pacman.services.marketsegment.entity.YieldCategoryRule;
import com.ideas.tetris.pacman.services.opera.entity.DataLoadMetadata;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.log4j.Logger;
import org.joda.time.LocalDate;
import org.joda.time.LocalDateTime;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.math.BigDecimal;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.ideas.tetris.pacman.common.configparams.RemoteAgentConfigParamName.EXCLUDE_ZERO_GROUP_BLOCK_PICKUP;
import static com.ideas.tetris.platform.common.externalsystem.ExternalSystem.OPERA_AGENT;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.when;

@Disabled("slow")
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class OperaGroupDataLoadServiceIntegrationTest extends OperaDataLoadServiceIntegrationTest {
    private static final Logger LOGGER = Logger.getLogger(OperaGroupDataLoadServiceIntegrationTest.class.getName());

    private static boolean dataLoadComplete = false;
    @Mock
    private PacmanConfigParamsService mockConfigParamsService;


    @AfterAll
    public static void afterclass() {
        CrudServiceBeanExtension.getTenantCrudService().getEntityManager().getTransaction().begin();
        deleteExistingData();
        CrudServiceBeanExtension.getTenantCrudService().getEntityManager().getTransaction().commit();
    }

    @BeforeEach
    public void beforeTests() throws Exception {
        super.setUp();
        operaGroupDataLoadService.pacmanConfigParamsService = this.pacmanConfigParamsService;
        if (!dataLoadComplete) {
            dataLoadComplete = true;
            loadStagingTables();
            commitTransaction(tenantCrudService());
        }
        cleanUp();
        when(mockConfigParamsService.getParameterValue(FeatureTogglesConfigParamName.OPERA_GROUP_RATE_CALCULATION_PER_DOW_ENABLED)).thenReturn(false);
        when(pacmanConfigParamsService.getParameterValue(IntegrationConfigParamName.GROUP_PAST_DAYS, OPERA_AGENT)).thenReturn(735);
        when(pacmanConfigParamsService.getParameterValue(RemoteAgentConfigParamName.EXCLUDE_ZERO_GROUP_BLOCK_PICKUP, OPERA_AGENT)).thenReturn(false);
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.DELTA_PGB_INSERTS)).thenReturn(false);
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.GROUP_MASTER_PACE_ENABLED)).thenReturn(false);
        System.setProperty("enable.opera.group.code.historical.data.population", "false");
    }

    @Test
    public void test_preprocessStageData() {
        List<?> results = tenantCrudService().findByNativeQuery("select * from dbo.Accom_Type where len(accom_type_code) = 0");
        assertEquals(0, results.size());

        List<OperaGroupBlock> groupBlocks = tenantCrudService().findAll(OperaGroupBlock.class);
        int actualGBCount = 0;
        for (OperaGroupBlock operaGroupBlock : groupBlocks) {
            if (operaGroupBlock.getGroupId() == 4696344) {
                assertEquals("SK", operaGroupBlock.getRoomType());
                actualGBCount++;
            }
            assertFalse(operaGroupBlock.getGroupId() == 22202);
        }
        assertEquals(1, actualGBCount);
        String sql = "select * from opera.Stage_Group_Master where group_id= 4696344";
        results = tenantCrudService().findByNativeQuery(sql);
        assertEquals(1, results.size());
        sql = "select * from opera.Stage_Group_Master where group_id= 22202";
        results = tenantCrudService().findByNativeQuery(sql);
        assertEquals(0, results.size());
    }

    @Test
    public void test_transform() {
        operaGroupDataLoadService.pacmanConfigParamsService = mockConfigParamsService;
        List<OperaGroupBlock> groupBlocks = tenantCrudService().findAll(OperaGroupBlock.class);
        for (OperaGroupBlock operaGroupBlock : groupBlocks) {
            assertEquals(calculateGroupRateValue(operaGroupBlock).doubleValue(), operaGroupBlock.getG3RateValue().doubleValue(), 2);
        }
        long start = System.currentTimeMillis();
        assertGroupBlockEquals(4696343, "2008-09-04", 0, "SK", 15, 5);
        assertGroupBlockEquals(4696343, "2008-09-05", 0, "SK", 15, 0);

        System.out.println("new assertions taking " + (System.currentTimeMillis() - start) + "ms");
    }

    @Test
    public void test_transformRecordsInGmNotInGb() {
        String sql = "select * from opera.Stage_Group_Master where group_id = 22201";
        List<?> results = tenantCrudService().findByNativeQuery(sql);
        assertEquals(0, results.size());
    }

    @Test
    public void test_transformRecordsInGbNotInGm() {
        String sql = "select * from opera.Stage_Group_Block where group_id in(44444)";
        List<?> results = tenantCrudService().findByNativeQuery(sql);
        assertEquals(0, results.size());
    }

    @Test
    public void test_transformRecordsDeleteOutsideFutureWindow() {
        String sql = "select max(Block_DT) from opera.Stage_Group_Block";
        List<?> results = tenantCrudService().findByNativeQuery(sql);
        LocalDate maxDate = new LocalDate(results.get(0));
        assertTrue(new LocalDate("2008-09-18").toDate().after(maxDate.toDate()));
        assertTrue(!results.get(0).toString().equalsIgnoreCase("2008-09-18"));
    }

    @Test
    public void test_loadFinal() {
        FileMetadata fileMetadata = new FileMetadata();
        LocalDate businessDate = new LocalDate("2008-09-06");
        fileMetadata.setSnapshotDt(businessDate.toDate());
        fileMetadata.setFutureWindowSize(11);
        int numRecords = operaGroupDataLoadService.loadTenantDatabase(fileMetadata);
        LOGGER.debug("Number of loaded records = " + numRecords);
        assertEquals(47, (Object) numRecords, "Did not load the expected number of rows based on existing test data.");

        long start = System.currentTimeMillis();
        assertPaceGroupBlockEquals(4696343, new LocalDate("2008-09-04"), new LocalDate("2008-09-04"), "SK", 15, 5, 0, new BigDecimal(120));
        assertPaceGroupBlockEquals(4696343, new LocalDate("2008-09-05"), new LocalDate("2008-09-05"), "SK", 15, 0, 0, BigDecimal.ZERO);
        LOGGER.debug("new assertions taking " + (System.currentTimeMillis() - start) + "ms");
        List<PaceGroupBlock> paceRecords = tenantCrudService().findAll(PaceGroupBlock.class);
        assertEquals(33, (Object) paceRecords.size(), "Expected number of group block pace records did not match ");
        ;
        for (PaceGroupBlock pace : paceRecords) {
            assertTrue(businessDate.toDate().after(pace.getBusinessDayEndDate()), "The pace record has a BusinessDayEndDate (" +
                    DateUtil.formatDate(pace.getBusinessDayEndDate(), DateUtil.DEFAULT_DATE_FORMAT) +
                    ") later than or equal the snapshot date -1 (" +
                    DateUtil.formatDate(businessDate.toDate(), DateUtil.DEFAULT_DATE_FORMAT) +
                    ")");
        }
    }

    @Test
    public void test_loadTenantDatabase_PaceGroupMaster() {
        // given
        String groupCode = "1";
        String newGroupCode = "2";
        String groupName = "test_name";
        String newGroupName = "new_test_name";
        String groupStatusCode = "test_status";
        String groupTypeCode = "gtype";
        int mktSegId = 7;
        String startDt = "2019-12-13";
        String endDt = "2019-12-14";
        String businessDateFirst = "2019-12-15";
        String businessDateSecond = "2019-12-16";
        FileMetadata fileMetadata = createTestFileMetadata();
        fileMetadata.setSnapshotDt(LocalDate.parse(businessDateFirst).plusDays(1).toDate());
        fileMetadata.setFutureWindowSize(11);
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.GROUP_MASTER_PACE_ENABLED)).thenReturn(true);

        PaceGroupMaster expectedPace = new PaceGroupMaster();
        expectedPace.setBusinessDayEndDt(LocalDate.parse(businessDateFirst).toDate());
        expectedPace.setGroupCode(groupCode);
        expectedPace.setGroupName(groupName);
        expectedPace.setGroupStatusCode(groupStatusCode);
        expectedPace.setGroupTypeCode(groupTypeCode);
        MarketSegmentSummary ms = new MarketSegmentSummary();
        ms.setId(mktSegId);
        expectedPace.setMarketSegment(ms);
        expectedPace.setStartDate(LocalDate.parse(startDt).toDate());
        expectedPace.setEndDate(LocalDate.parse(endDt).toDate());
        expectedPace.setFileMetadata(fileMetadata);

        tenantCrudService().executeUpdateByNativeQuery("delete from opera.Stage_Group_Block");
        tenantCrudService().executeUpdateByNativeQuery("delete from opera.Stage_Group_Master");

        // TEST 1 : New Pace_Group_Master record should be inserted for a new Group_Master record
        tenantCrudService().executeUpdateByNativeQuery("INSERT INTO Group_Master ( " +
                        "Property_ID, Group_Code, Group_Name, Group_Status_Code, Group_Type_Code, Mkt_Seg_ID, Start_DT, End_DT) " +
                        "VALUES (5, :code, :name, :status, :type, :mktseg, :start, :end) ",
                QueryParameter.with("code", groupCode).and("name", groupName).and("status", groupStatusCode)
                        .and("type", groupTypeCode).and("mktseg", mktSegId).and("start", startDt).and("end", endDt).parameters());
        assertTableSize(1, "Group_Master");
        assertTableSize(0, "Pace_Group_Master");
        operaGroupDataLoadService.loadTenantDatabase(fileMetadata);
        assertTableSize(1, "Group_Master");
        assertTableSize(1, "Pace_Group_Master");
        assertPaceGroupMasterRecord(expectedPace, 0);

        // TEST 2 : Pace_Group_Master should not change if Group_Master does not change
        operaGroupDataLoadService.loadTenantDatabase(fileMetadata);
        assertTableSize(1, "Group_Master");
        assertTableSize(1, "Pace_Group_Master");
        assertPaceGroupMasterRecord(expectedPace, 0);

        // TEST 3 : Pace_Group_Master record should be updated if Group_Master changes again during the same business date
        tenantCrudService().executeUpdateByNativeQuery("UPDATE Group_Master SET Group_Code = :code",
                QueryParameter.with("code", newGroupCode).parameters());
        operaGroupDataLoadService.loadTenantDatabase(fileMetadata);
        assertTableSize(1, "Group_Master");
        assertTableSize(1, "Pace_Group_Master");
        expectedPace.setGroupCode(newGroupCode);
        assertPaceGroupMasterRecord(expectedPace, 0);

        // TEST 4 : New Pace_Group_Master record should be inserted for a change in Group_Master after business date advances
        fileMetadata.setSnapshotDt(LocalDate.parse(businessDateSecond).plusDays(1).toDate());
        tenantCrudService().executeUpdateByNativeQuery("UPDATE Group_Master SET Group_Name = :name",
                QueryParameter.with("name", newGroupName).parameters());
        operaGroupDataLoadService.loadTenantDatabase(fileMetadata);
        assertTableSize(1, "Group_Master");
        assertTableSize(2, "Pace_Group_Master");
        assertPaceGroupMasterRecord(expectedPace, 0);
        expectedPace.setBusinessDayEndDt(LocalDate.parse(businessDateSecond).toDate());
        expectedPace.setGroupName(newGroupName);
        assertPaceGroupMasterRecord(expectedPace, 1);
    }

    @Test
    public void test_loadTenantDatabase_OperaGroupBLockCode() {
        // given
        FileMetadata fileMetadata = new FileMetadata();
        LocalDate businessDate = new LocalDate("2008-09-06");
        fileMetadata.setSnapshotDt(businessDate.toDate());
        fileMetadata.setFutureWindowSize(11);

        operaGroupDataLoadService.loadTenantDatabase(fileMetadata);
        assertTableSize(14, "Group_Master");
        assertTableSize(14, "Opera_Group_Block_Code");
        assertEquals(1, tenantCrudService().findByNativeQuerySingleResult("SELECT COUNT(*) FROM Opera_Group_Block_Code where group_code = '0810VAIBHA'",
                null, dbCountRowMapper).intValue());
    }

    @Test
    public void test_loadTenantDatabase_OperaGroupBLockCode_WhenItContainsStringGroupCodeEntries() {
        // given
        FileMetadata fileMetadata = new FileMetadata();
        LocalDate businessDate = new LocalDate("2008-09-06");
        fileMetadata.setSnapshotDt(businessDate.toDate());
        fileMetadata.setFutureWindowSize(11);
        tenantCrudService().executeUpdateByNativeQuery("insert into group_master (property_id,Group_Code,Group_Name,Group_Description,mkt_seg_id,Group_Status_Code,Group_Type_Code,Start_DT,End_DT,Booking_DT) values(5,'ABCDE', '88881Test_Group with Partial Pickups', 'Group with Partial Pickups', 7,'DEFINITE','TRANS','2008-09-07','2008-09-10','2008-09-07')");

        operaGroupDataLoadService.loadTenantDatabase(fileMetadata);
        assertTableSize(15, "Group_Master");
        assertTableSize(14, "Opera_Group_Block_Code");
        assertEquals(1, tenantCrudService().findByNativeQuerySingleResult("SELECT COUNT(*) FROM Opera_Group_Block_Code where group_code = '0810VAIBHA'",
                null, dbCountRowMapper).intValue());
    }

    private void assertPaceGroupMasterRecord(PaceGroupMaster expectedPace, int sqlRowIndex) {
        Object[] row = (Object[]) tenantCrudService()
                .findByNativeQuery("SELECT * FROM Pace_Group_Master ORDER BY Business_Day_End_DT")
                .get(sqlRowIndex);
        assertTrue(isDateMatch(expectedPace.getBusinessDayEndDt(), row[1]));
        assertEquals(String.valueOf(expectedPace.getGroupCode()), String.valueOf(row[2]));
        assertEquals(String.valueOf(expectedPace.getGroupName()), String.valueOf(row[3]));
        assertEquals(String.valueOf(expectedPace.getGroupDescription()), String.valueOf(row[4]));
        assertEquals(String.valueOf(expectedPace.getMasterGroupId()), String.valueOf(row[5]));
        assertEquals(String.valueOf(expectedPace.getMasterGroupCode()), String.valueOf(row[6]));
        assertEquals(String.valueOf(expectedPace.getGroupStatusCode()), String.valueOf(row[7]));
        assertEquals(String.valueOf(expectedPace.getGroupTypeCode()), String.valueOf(row[8]));
        assertEquals(expectedPace.getMarketSegment().getId(), row[9]);
        assertTrue(isDateMatch(expectedPace.getStartDate(), row[10]));
        assertTrue(isDateMatch(expectedPace.getEndDate(), row[11]));
        assertTrue(isDateMatch(expectedPace.getBookingDate(), row[12]));
        assertEquals(String.valueOf(expectedPace.getPickupTypeCode()), String.valueOf(row[13]));
        assertTrue(isDateMatch(expectedPace.getCancelDate(), row[14]));
        assertEquals(String.valueOf(expectedPace.getBookingType()), String.valueOf(row[15]));
        assertEquals(String.valueOf(expectedPace.getSalesPerson()), String.valueOf(row[16]));
        assertTrue(isDateMatch(expectedPace.getCutOffDate(), row[17]));
        assertEquals(String.valueOf(expectedPace.getCutOffDays()), String.valueOf(row[18]));
        assertEquals(expectedPace.getFileMetadata().getId(), row[19]);
    }

    private boolean isDateMatch(Date expected, Object actual) {
        if (expected == null && actual == null) {
            return true;
        } else if (expected == null || actual == null) {
            return false;
        }
        return new java.sql.Date(expected.getTime()).equals(actual);
    }

    private void assertTableSize(int expectedSize, String tableName) {
        assertEquals(expectedSize, tenantCrudService().findByNativeQuerySingleResult("SELECT COUNT(*) FROM " + tableName,
                null, dbCountRowMapper).intValue());
    }

    @Test
    public void doNotInsertPaceGroupBlockRecordsIfValuesHaveNotChanged() {
        // given
        FileMetadata fileMetadata = new FileMetadata();
        fileMetadata.setSnapshotDt(new LocalDate("2222-12-31").toDate());  // use the S_G_B record BDE date
        fileMetadata.setFutureWindowSize(11);
        String selectPgb = "SELECT Group_ID, Occupancy_DT, Accom_Type_ID, Business_Day_End_DT, Blocks, Pickup, Original_Blocks, rate " +
                "FROM Pace_Group_Block order by Group_ID, Occupancy_DT, Accom_Type_ID";
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.DELTA_PGB_INSERTS)).thenReturn(true);

        // when:
        operaGroupDataLoadService.loadTenantDatabase(fileMetadata);
        List rows = tenantCrudService().findByNativeQuery(selectPgb);  // a list of array
        assertEquals(33, rows.size());

        // Change S_G_B records to next business day (9/7/2008), without changing any other values
        tenantCrudService().executeUpdateByNativeQuery(
                "delete opera.stage_group_block where business_day_end_dt != '2008-09-06'");
        // when: the next business day, has the same same values as the prior day, then a new row should NOT be inserted into P_G_B
        tenantCrudService().executeUpdateByNativeQuery(
                "update opera.stage_group_block set business_day_end_dt = '2008-09-07' where business_day_end_dt = '2008-09-06'");
        // when: we add a new BDE record for existing (group/block_dt/accom_type_id) that has different (block,pickup,forecast) values than the prior day, then a new row should be added
        tenantCrudService().executeUpdateByNativeQuery(
                "insert into opera.stage_group_block " +
                        "(group_id, block_dt, accom_type_id, business_day_end_dt, Block, Pickup, Forecast, single_rate, data_load_metadata_id, g3_rate_value) " +
                        "values (23450,'2008-09-14', (select min(accom_type_id) from opera.stage_group_block where group_id=23450), '2008-09-07', 99, 98, 97, 88.00, (select max(data_load_metadata_id) from opera.stage_group_block), 10)");
        operaGroupDataLoadService.loadTenantDatabase(fileMetadata);
        rows = tenantCrudService().findByNativeQuery(selectPgb);  // a list of array
        assertEquals(34, rows.size());
        Object[] row = (Object[]) rows.get(7); // should have been newly added
        assertEquals(99, ((BigDecimal) row[4]).intValue());
        assertEquals(98, ((BigDecimal) row[5]).intValue());
        assertEquals(97, ((BigDecimal) row[6]).intValue());
        assertEquals(88, ((BigDecimal) row[7]).intValue());
    }


    @Test
    public void reconstructStageGroupBlockZeroRecordsFromGroupBlock() throws Exception {
        // given
        LocalDate bdeLocalDate = new LocalDate("2008-09-11");
        int groupPastDays = 10;
        FileMetadata fileMetadata = new FileMetadata();
        fileMetadata.setSnapshotDt(bdeLocalDate.toDate());
        fileMetadata.setFutureWindowSize(11);
        when(pacmanConfigParamsService.getParameterValue(EXCLUDE_ZERO_GROUP_BLOCK_PICKUP, OPERA_AGENT)).thenReturn(true);
        when(pacmanConfigParamsService.getParameterValue(IntegrationConfigParamName.GROUP_PAST_DAYS, OPERA_AGENT)).thenReturn(groupPastDays);
        // BDE Date = 2008-09-11, and group past days is 10 days prior, thus the snapshot start date is 2008-09-01

        Object[] row;
        int group_id = 0;
        int block_dt = 1;
        int block = 2;
        int pickup = 3;
        int accom_type_id = 4;
        int businessDayEndDt = 5;
        String selectSgb = "select group_id, block_dt, block, pickup, accom_type_id, Business_Day_End_DT from opera.stage_group_block order by group_id, block_dt, accom_type_id";
        // Test setup for Group_Master, Stage_Group_Block, Group_Block
        URL filePathUrl = this.getClass().getResource("/sql/reconstruct-sgb.sql");
        String testDataSql = new String(Files.readAllBytes(Paths.get(filePathUrl.toURI())));
        tenantCrudService().executeUpdateByNativeQuery(testDataSql);
        List beforeRows = tenantCrudService().findByNativeQuery(selectSgb);

        // when
        operaGroupDataLoadService.loadTenantDatabase(fileMetadata);
        List afterRows = tenantCrudService().findByNativeQuery(selectSgb);

        // then
        assertEquals(37, beforeRows.size());
        // added (99934[2,4,6]/[4,5]/2008-09-0[2,4,6]) (group_id, accom_type_id, block_dt) to Stage_Group_Block
        assertEquals(45, afterRows.size()); // two new rows added to Stage_Group_Block

        // Note that dates are 9/1/2008 and 9/16/2008 which bounds the inserted records to be the 9/8/2008 records
        row = (Object[]) afterRows.get(27);
        assertEquals(888777, row[group_id]);
        assertEquals(java.sql.Date.valueOf(java.time.LocalDate.of(2008, 9, 8)), row[block_dt]);
        assertNotNull(row[accom_type_id]);

        // 341
        row = (Object[]) afterRows.get(28);
        assertEquals(999341, row[group_id]);
        assertEquals(java.sql.Date.valueOf(java.time.LocalDate.of(2008, 9, 1)), row[block_dt]);
        assertEquals(4, row[accom_type_id]);
        row = (Object[]) afterRows.get(29);
        assertEquals(999341, row[group_id]);
        assertEquals(java.sql.Date.valueOf(java.time.LocalDate.of(2008, 9, 1)), row[block_dt]);
        assertEquals(5, row[accom_type_id]);

        // 342 - added
        row = (Object[]) afterRows.get(30);
        assertEquals(999342, row[group_id]);
        assertEquals(java.sql.Date.valueOf(java.time.LocalDate.of(2008, 9, 2)), row[block_dt]);
        assertEquals(0, row[block]);
        assertEquals(0, row[pickup]);
        assertEquals(4, row[accom_type_id]);
        row = (Object[]) afterRows.get(31);
        assertEquals(999342, row[group_id]);
        assertEquals(java.sql.Date.valueOf(java.time.LocalDate.of(2008, 9, 2)), row[block_dt]);
        assertEquals(0, row[block]);
        assertEquals(0, row[pickup]);
        assertEquals(5, row[accom_type_id]);

        // 343
        row = (Object[]) afterRows.get(32);
        assertEquals(999343, row[group_id]);
        assertEquals(java.sql.Date.valueOf(java.time.LocalDate.of(2008, 9, 3)), row[block_dt]);
        assertEquals(4, row[accom_type_id]);
        row = (Object[]) afterRows.get(33);
        assertEquals(999343, row[group_id]);
        assertEquals(java.sql.Date.valueOf(java.time.LocalDate.of(2008, 9, 3)), row[block_dt]);
        assertEquals(5, row[accom_type_id]);

        // 344 - added
        row = (Object[]) afterRows.get(34);
        assertEquals(999344, row[group_id]);
        assertEquals(java.sql.Date.valueOf(java.time.LocalDate.of(2008, 9, 4)), row[block_dt]);
        assertEquals(4, row[accom_type_id]);
        row = (Object[]) afterRows.get(35);
        assertEquals(999344, row[group_id]);
        assertEquals(java.sql.Date.valueOf(java.time.LocalDate.of(2008, 9, 4)), row[block_dt]);
        assertEquals(5, row[accom_type_id]);

        // 346 - added
        row = (Object[]) afterRows.get(36);
        assertEquals(999346, row[group_id]);
        assertEquals(java.sql.Date.valueOf(java.time.LocalDate.of(2008, 9, 6)), row[block_dt]);
        assertEquals(4, row[accom_type_id]);
        row = (Object[]) afterRows.get(37);
        assertEquals(999346, row[group_id]);
        assertEquals(java.sql.Date.valueOf(java.time.LocalDate.of(2008, 9, 6)), row[block_dt]);
        assertEquals(java.sql.Date.valueOf(java.time.LocalDate.of(2008, 9, 6)), row[businessDayEndDt]);
        assertEquals(5, row[accom_type_id]);
        row = (Object[]) afterRows.get(38);
        assertEquals(999346, row[group_id]);
        assertEquals(java.sql.Date.valueOf(java.time.LocalDate.of(2008, 9, 12)), row[block_dt]);
        assertEquals(java.sql.Date.valueOf(java.time.LocalDate.of(2008, 9, 10)), row[businessDayEndDt]);
        assertEquals(4, row[accom_type_id]);

        row = (Object[]) afterRows.get(40);
        assertEquals(999777, row[group_id]);
        assertEquals(java.sql.Date.valueOf(java.time.LocalDate.of(2008, 9, 6)), row[block_dt]);
        assertEquals(java.sql.Date.valueOf(java.time.LocalDate.of(2008, 9, 6)), row[businessDayEndDt]);
        assertNotNull(row[accom_type_id]);

    }

    @Test
    public void confirmFutureRecordsThatExistAreNotoverwritten() {
        FileMetadata fileMetadata = createTestFileMetadata();

        AccomType sk = (AccomType) tenantCrudService().findByNamedQuery(AccomType.BY_CODE,
                QueryParameter.with("code", "SK").parameters()).get(0);

        assertEquals(47, operaGroupDataLoadService.loadTenantDatabase(fileMetadata));
        tenantCrudService().flush();
        GroupBlockMaster associatedGroupMaster = (GroupBlockMaster) tenantCrudService().findByNamedQuery(GroupBlockMaster.BY_CODE,
                QueryParameter.with("code", "4696343").parameters()).get(0);
        PaceGroupBlock paceGroupBlock = new PaceGroupBlock();
        paceGroupBlock.setAccomTypeId(sk.getId());
        paceGroupBlock.setBlocks(15);
        paceGroupBlock.setPickup(5);
        paceGroupBlock.setOriginalBlocks(15);
        paceGroupBlock.setRate(BigDecimal.TEN);
        paceGroupBlock.setBusinessDayEndDate(fileMetadata.getSnapshotDt());// the businessdate
        paceGroupBlock.setGroupId(associatedGroupMaster.getId());
        paceGroupBlock.setOccupancyDate(DateUtil.getDate(4, 9, 2008));
        tenantCrudService().save(paceGroupBlock);
        tenantCrudService().flush();
        List<GroupBlockMaster> groupMastersAfter = tenantCrudService().findAll(GroupBlockMaster.class);

        List<PaceGroupBlock> paceGroupBlocks = tenantCrudService().findAll(PaceGroupBlock.class);
        fileMetadata.setSnapshotDt(DateUtil.addDaysToDate(fileMetadata.getSnapshotDt(), 1));
        assertEquals(28, operaGroupDataLoadService.loadTenantDatabase(fileMetadata));
        tenantCrudService().flush();

        paceGroupBlocks = tenantCrudService().findAll(PaceGroupBlock.class);
        assertEquals(62, paceGroupBlocks.size());
        List<PaceGroupBlock> filteredList = paceGroupBlocks.stream()
                .filter(o -> o.getBusinessDayEndDate().equals(DateUtil.addDaysToDate(fileMetadata.getSnapshotDt(), -1)))
                .collect(Collectors.toList());
        assertEquals(29, filteredList.size());
        assertEquals(4, filteredList.stream()
                .filter(o -> o.getBlocks() == 0
                        && o.getPickup() == 0
                        && o.getRate().compareTo(BigDecimal.ZERO) == 0
                ).collect(Collectors.toList()).size());
    }

    @Test
    public void test_historicGroupMasterEntry_doNotLoadCancellationdt() {
        int pastGroupId = 88883;
        FileMetadata fileMetadata = createTestFileMetadata();
        operaGroupDataLoadService.loadTenantDatabase(fileMetadata);

        LocalDateTime nextBusinessDate = new LocalDateTime(fileMetadata.getSnapshotDt()).plusDays(1);
        DataLoadMetadata dataLoadMetadata = createNewFileMetadataAndDataLoadMetadataEntryForGroupData(fileMetadata, nextBusinessDate);
        int latestDataLoadMetadataId = dataLoadMetadata.getId();

        insertNewHistoryGMCancelStatusEntry(latestDataLoadMetadataId, pastGroupId);
        updateStageGMWithCancelStatusEntry(latestDataLoadMetadataId, pastGroupId);

        operaGroupDataLoadService.loadTenantDatabase(fileMetadata);
        String getNonNullCancelDtCountSql = "select count(*) from group_master where group_code = :groupId and cancel_dt is not null";
        Map<String, Object> params = QueryParameter.with("groupId", pastGroupId).parameters();
        assertEquals(0, tenantCrudService().findByNativeQuerySingleResult(getNonNullCancelDtCountSql, params, dbCountRowMapper).intValue());
    }

    @Test
    public void test_loadCancellation_dt_existingGroupGetsCancelled() {
        int groupId = 33334;
        FileMetadata fileMetadata = createTestFileMetadata();
        operaGroupDataLoadService.loadTenantDatabase(fileMetadata);

        LocalDateTime nextBusinessDate = new LocalDateTime(fileMetadata.getSnapshotDt()).plusDays(1);
        DataLoadMetadata dataLoadMetadata = createNewFileMetadataAndDataLoadMetadataEntryForGroupData(fileMetadata, nextBusinessDate);

        int latestDataLoadMetadataId = dataLoadMetadata.getId();
        insertNewHistoryGMCancelStatusEntry(latestDataLoadMetadataId, groupId);
        updateStageGMWithCancelStatusEntry(latestDataLoadMetadataId, groupId);

        operaGroupDataLoadService.loadTenantDatabase(fileMetadata);
        String getNonNullCancelDtCountSql = "select count(*) from group_master where group_code = :groupId and cancel_dt= :cancelDt";
        Map<String, Object> params = QueryParameter.with("groupId", groupId).and("cancelDt", nextBusinessDate.toLocalDate()).parameters();
        assertEquals(1, tenantCrudService().findByNativeQuerySingleResult(getNonNullCancelDtCountSql, params, dbCountRowMapper).intValue());
    }

    @Test
    public void test_loadCancellation_dt_withRestCall() {
        int groupId = 33334;
        FileMetadata fileMetadata = createTestFileMetadata();
        operaGroupDataLoadService.loadTenantDatabase(fileMetadata);

        LocalDateTime nextBusinessDate = new LocalDateTime(fileMetadata.getSnapshotDt()).plusDays(1);
        DataLoadMetadata dataLoadMetadata = createNewFileMetadataAndDataLoadMetadataEntryForGroupData(fileMetadata, nextBusinessDate);

        int latestDataLoadMetadataId = dataLoadMetadata.getId();
        insertNewHistoryGMCancelStatusEntry(latestDataLoadMetadataId, groupId);
        updateStageGMWithCancelStatusEntry(latestDataLoadMetadataId, groupId);

        operaGroupDataLoadService.loadTenantDatabase(fileMetadata);
        String nullifyCancelDateValues = "UPDATE group_master set cancel_dt = null where cancel_dt is not null";
        tenantCrudService().executeUpdateByNativeQuery(nullifyCancelDateValues);
        operaGroupDataLoadService.updateCancelDate(PacmanWorkContextHelper.getPropertyId());
        String getNonNullCancelDtCountSql = "select count(*) from group_master where group_code = :groupId and cancel_dt is not null";
        Map<String, Object> params = QueryParameter.with("groupId", groupId).parameters();
        assertEquals(1, tenantCrudService().findByNativeQuerySingleResult(getNonNullCancelDtCountSql, params, dbCountRowMapper).intValue());
    }

    @Test
    public void test_populateHistoricalOperaGroupBlockCodeData() {
        System.setProperty("enable.opera.group.code.historical.data.population", "true");
        tenantCrudService().executeUpdateByNativeQuery("insert into group_master (property_id,Group_Code,Group_Name,Group_Description,mkt_seg_id,Group_Status_Code,Group_Type_Code,Start_DT,End_DT,Booking_DT) values(5, 1234 , '88881Test_Group with Partial Pickups', 'Group with Partial Pickups', 7,'DEFINITE','TRANS','2008-09-07','2008-09-10','2008-09-07')");

        operaGroupDataLoadService.populateHistoricalOperaGroupBlockCodeData(true);
        List<OperaGroupBlockCode> groupBlocks = tenantCrudService().findAll(OperaGroupBlockCode.class);
        assertTrue(!groupBlocks.isEmpty());
    }

    @Test
    public void test_populateHistoricalOperaGroupBlockCodeDataCalledFromPms() {
        System.setProperty("enable.opera.group.code.historical.data.population", "true");
        tenantCrudService().executeUpdateByNativeQuery("insert into group_master (property_id,Group_Code,Group_Name,Group_Description,mkt_seg_id,Group_Status_Code,Group_Type_Code,Start_DT,End_DT,Booking_DT) values(5, 1234 , '88881Test_Group with Partial Pickups', 'Group with Partial Pickups', 7,'DEFINITE','TRANS','2008-09-07','2008-09-10','2008-09-07')");

        operaGroupDataLoadService.populateHistoricalOperaGroupBlockCodeData(false);
        List<OperaGroupBlockCode> groupBlocks = tenantCrudService().findAll(OperaGroupBlockCode.class);
        assertTrue(!groupBlocks.isEmpty());
    }


    @Test
    public void test_loadCancellation_dt_newGroupGetsCancelled() {
        int groupId = 33335;
        String groupCode = groupId + "_Test";
        String groupDesc = "TestNewGrpVerifyCancelDate";
        FileMetadata fileMetadata = createTestFileMetadata();
        operaGroupDataLoadService.loadTenantDatabase(fileMetadata);

        LocalDateTime nextBusinessDate = new LocalDateTime(fileMetadata.getSnapshotDt()).plusDays(1);
        DataLoadMetadata dataLoadMetadata = createNewFileMetadataAndDataLoadMetadataEntryForGroupData(fileMetadata, nextBusinessDate);
        int latestDataLoadMetadataId = dataLoadMetadata.getId();

        String insertCancelledGroupEntry = " INSERT INTO opera.History_Group_Master VALUES " +
                "('FSDH',:groupId,:groupCode,:groupDesc,NULL,'CAN','BR','2011-09-06','2011-09-08','2011-02-15 11:03:09.0','G',:latestDlmId)";
        Map<String, Object> params = QueryParameter.with("groupId", groupId).and("groupCode", groupCode).and("groupDesc", groupDesc).and("latestDlmId", latestDataLoadMetadataId).parameters();
        tenantCrudService().executeUpdateByNativeQuery(insertCancelledGroupEntry, params);

        String updateStageSql = " insert INTO OPERA.stage_group_master " +
                " (Hotel_Code,Group_ID,Block_Code,Group_Name,Master_Group_ID,Status,Market_Segment,Arrival_DT,Departure_DT,Insert_DT,Group_Type,Data_Load_Metadata_ID,Mkt_Seg_ID,G3_Group_Status_Code) values " +
                " ('FSDH',:groupId,:groupCode,:groupDesc,NULL,'CAN','BR','2011-09-06','2011-09-08','2011-02-15 11:03:09.0','G', :latestDlmId" +
                " ,(select top 1 mkt_seg_id from mkt_seg where mkt_seg_code='BR'),'CANCELLED') ";
        tenantCrudService().executeUpdateByNativeQuery(updateStageSql, params);

        operaGroupDataLoadService.loadTenantDatabase(fileMetadata);

        String getNonNullCancelDtCountSql = "select count(*) from group_master where group_code = :groupId and cancel_dt is not null";
        Map<String, Object> parameters = QueryParameter.with("groupId", groupId).parameters();
        assertEquals(1, tenantCrudService().findByNativeQuerySingleResult(getNonNullCancelDtCountSql, parameters, dbCountRowMapper).intValue());
    }

    private void cleanUp() {
        tenantCrudService().executeUpdateByNativeQuery("delete from Pace_Group_Master");
        tenantCrudService().executeUpdateByNativeQuery("DELETE FROM Wash_Ind_Group_Fcst");
        tenantCrudService().executeUpdateByNativeQuery("delete from Wash_Ind_Group_Fcst_OVR");
        tenantCrudService().executeUpdateByNativeQuery("delete from Group_Block");
        tenantCrudService().executeUpdateByNativeQuery("delete from Group_Master");
        tenantCrudService().executeUpdateByNativeQuery("delete from Opera_Group_Block_Code");
    }

    @Test
    public void test_loadTenant_GroupMaster_Records() {
        // insert same record
        tenantCrudService().executeUpdateByNativeQuery("insert into group_master (property_id,Group_Code,Group_Name,Group_Description,mkt_seg_id,Group_Status_Code,Group_Type_Code,Start_DT,End_DT,Booking_DT) values(5,'88881', '88881Test_Group with Partial Pickups', 'Group with Partial Pickups', 7,'DEFINITE','TRANS','2008-09-07','2008-09-10','2008-09-07')");
        tenantCrudService().executeUpdateByNativeQuery
                ("INSERT INTO opera.G3_Group_Master_Link (Opera_Group_ID, G3_Group_ID) values ('88881',(select group_id from group_master where group_code = '88881'))");
        FileMetadata fileMetadata = new FileMetadata();
        LocalDate businessDate = new LocalDate("2008-09-06");
        fileMetadata.setSnapshotDt(businessDate.toDate());
        fileMetadata.setFutureWindowSize(11);
        int numRecords = operaGroupDataLoadService.loadTenantDatabase(fileMetadata);
        List byNativeQuery2 = tenantCrudService().findByNativeQuery("select mkt_seg_id from group_master where Group_Code = '88881'");
        assertGroupMasterMSEquals("88881", "88881Test_Group with Partial Pickups", "Group with Partial Pickups");
    }

    @Test
    public void test_loadTenant_GroupMaster_Records_WhenSomeLinkRecordsAreAbsent_And_SomeGroupCodesInGroupMasterAreInNGIStyleVarCharFormat() {
        FileMetadata fileMetadata = new FileMetadata();
        LocalDate businessDate = new LocalDate("2008-09-06");
        fileMetadata.setSnapshotDt(businessDate.toDate());
        fileMetadata.setFutureWindowSize(11);
        // Group with varchar group-code
        tenantCrudService().executeUpdateByNativeQuery("insert into group_master (property_id,Group_Code,Group_Name,Group_Description,mkt_seg_id,Group_Status_Code,Group_Type_Code,Start_DT,End_DT,Booking_DT) values(5,'VARCHAR_GROUP_CODE', 'Test_Group', 'Group with Partial Pickups', 7,'DEFINITE','TRANS','2008-09-07','2008-09-10','2008-09-07')");
        // Group having corresponding link record present
        tenantCrudService().executeUpdateByNativeQuery("insert into group_master (property_id,Group_Code,Group_Name,Group_Description,mkt_seg_id,Group_Status_Code,Group_Type_Code,Start_DT,End_DT,Booking_DT) values(5,'88881', '88881Test_Group with Partial Pickups', 'Group with Partial Pickups', 7,'DEFINITE','TRANS','2008-09-07','2008-09-10','2008-09-07')");
        tenantCrudService().executeUpdateByNativeQuery
                ("INSERT INTO opera.G3_Group_Master_Link (Opera_Group_ID, G3_Group_ID) values ('88881',(select group_id from group_master where group_code = '88881'))");

        operaGroupDataLoadService.loadTenantDatabase(fileMetadata);

        // assert that loading is successful and column-cast exception is not thrown
        assertGroupMasterMSEquals("88881", "88881Test_Group with Partial Pickups", "Group with Partial Pickups");
    }

    @Test
    public void test_GroupMaster_Delete() {
        FileMetadata fileMetadata = createTestFileMetadata();
        operaGroupDataLoadService.loadTenantDatabase(fileMetadata);
        LocalDateTime nextBusinessDate = createAnotherLoadForNextBusinessDate(fileMetadata);
        changeGroupCodeGroupMaster();
        assertGroupMasterEquals("499999", "0810VAIBHA_VaibhavDataExtractSanityTest", "VaibhavDataExtractSanityTest");
        operaGroupDataLoadService.loadTenantDatabase(fileMetadata);
        assertGroupMasterEquals("4696343", "0810VAIBHA_VaibhavDataExtractSanityTest", "VaibhavDataExtractSanityTest");
        assertGroupMasterEquals("499999", "0810VAIBHA_VaibhavDataExtractSanityTest", "VaibhavDataExtractSanityTest");
    }

    @Test
    public void test_load_NonPace() {
        FileMetadata fileMetadata = new FileMetadata();
        LocalDate businessDate = new LocalDate("2008-09-06");
        fileMetadata.setSnapshotDt(businessDate.toDate());
        fileMetadata.setFutureWindowSize(11);
        int numRecords = operaGroupDataLoadService.loadTenantDatabase(fileMetadata);
        LOGGER.debug("Number of loaded records = " + numRecords);
        assertEquals(47, (Object) numRecords, "Did not load the expected number of rows based on existing test data.");
        List<GroupBlockDetail> groupBlockDetails = tenantCrudService().findAll(GroupBlockDetail.class);
        assertNonPaceGroupBlockEquals(4696343, new LocalDate("2008-09-04"), new LocalDate("2008-09-04"), "SK", 15, 5, 0, new BigDecimal(120));
        assertNonPaceGroupBlockEquals(4696343, new LocalDate("2008-09-05"), new LocalDate("2008-09-05"), "SK", 15, 0, 0, BigDecimal.ZERO);
    }

    @Test
    public void test_load_NonPace_Count() {
        FileMetadata fileMetadata = new FileMetadata();
        LocalDate businessDate = new LocalDate("2008-09-06");
        fileMetadata.setSnapshotDt(businessDate.toDate());
        String sql = "select count(*) from dbo.Group_Block";
        fileMetadata.setFutureWindowSize(11);
        int nonpacerowsbefore = tenantCrudService().findByNativeQuerySingleResult(sql, null, dbCountRowMapper);
        operaGroupDataLoadService.loadTenantDatabase(fileMetadata);
        sql = "select count(*) from opera.Stage_Group_Block";
        int stagerows = tenantCrudService().findByNativeQuerySingleResult(sql, null, dbCountRowMapper);
        sql = "select count(*) from dbo.Group_Block";
        int nonpacerows = tenantCrudService().findByNativeQuerySingleResult(sql, null, dbCountRowMapper);
        assertEquals(stagerows, nonpacerows - nonpacerowsbefore);
    }


    @Test
    // To test zero fill, we first process one extract.  We then load a second
    // extract.  In this case, we use the exact same extract for both loads, but
    // we update the snapshot date on the second one to make it come through as the
    // next business day.  We then delete a group block record, process the extract,
    // and then verify that a zero fill was created in the pace table.
    public void test_zeroFillPace() {
        int operaGroupId = 88881;
        LocalDate blockDate = new LocalDate(2008, 9, 7);
        String roomTypeCode = "SK6";

        // Perform first load
        FileMetadata fileMetadata = createTestFileMetadata();
        operaGroupDataLoadService.loadTenantDatabase(fileMetadata);

        // Update business day for the second load.
        LocalDateTime nextBusinessDate = createAnotherLoadForNextBusinessDate(fileMetadata);

        // Remove existing block that should then be zero-filled.
        removeGroupBlockFromStage(operaGroupId, blockDate, roomTypeCode);

        // Now process the second extract
        operaGroupDataLoadService.loadTenantDatabase(fileMetadata);
        // And verify that a zero filled pace record was created for the deleted block.
        final LocalDate businessDayEndDate = LocalDate.fromDateFields(fileMetadata.getSnapshotDt()).minusDays(1);
        assertPaceGroupBlockEquals(operaGroupId, blockDate, businessDayEndDate, roomTypeCode, 0, 0, 0, BigDecimal.ZERO);
    }


    @Test
    public void test_zeroFillPace_RoomtypeChange() {
        int operaGroupId = 4696343;
        LocalDate blockDate = new LocalDate(2008, 9, 4);
        String roomTypeCode = "SK";

        // Perform first load
        FileMetadata fileMetadata = createTestFileMetadata();
        operaGroupDataLoadService.loadTenantDatabase(fileMetadata);

        // Update business day for the second load.
        LocalDateTime nextBusinessDate = createAnotherLoadForNextBusinessDate(fileMetadata);

        // Remove existing block that should then be zero-filled.
        changeRoomTypeGroupBlock(operaGroupId, blockDate, roomTypeCode);

        // Now process the second extract
        operaGroupDataLoadService.loadTenantDatabase(fileMetadata);

        // And verify that a zero filled pace record was created for the deleted block.
        assertPaceGroupBlockEquals(operaGroupId, blockDate, new LocalDate(blockDate), roomTypeCode, 0, 0, 0, BigDecimal.ZERO);
    }


    @Test
    // Test to make sure that only one zero fill record is created upon processing a third extract
    public void test_zeroFillPace_onlyOneZeroFillRecordShouldBeCreated() {
        int operaGroupId = 88881;
        LocalDate blockDate = new LocalDate(2008, 9, 7);
        String roomTypeCode = "SK6";

        FileMetadata fileMetadata = createTestFileMetadata();
        operaGroupDataLoadService.loadTenantDatabase(fileMetadata);
        LocalDateTime nextBusinessDate = createAnotherLoadForNextBusinessDate(fileMetadata);
        removeGroupBlockFromStage(operaGroupId, blockDate, roomTypeCode);
        operaGroupDataLoadService.loadTenantDatabase(fileMetadata);
        final LocalDate businessDayEndDt = LocalDate.fromDateFields(fileMetadata.getSnapshotDt()).minusDays(1);
        assertPaceGroupBlockEquals(operaGroupId, blockDate, businessDayEndDt, roomTypeCode, 0, 0, 0, BigDecimal.ZERO);

        // Now process the third extract
        LocalDateTime thirdBusinessDate = createAnotherLoadForNextBusinessDate(fileMetadata);
        List<?> paceGroupBlocks = findPaceGroupBlocks(operaGroupId, blockDate, new LocalDate(thirdBusinessDate), roomTypeCode);
        assertTrue(CollectionUtils.isEmpty(paceGroupBlocks),
                "Should not find a zero filled record for the third extract as one was already created in the second extract.");
    }

    @Test
    public void test_yieldCategoryByRule_GroupData() {

        tenantCrudService().save(new YieldCategoryRule("BR", "RACK", "BR_RACK_1", new LocalDate(2000, 3, 5), new LocalDate(2007, 3, 5), 1));
        tenantCrudService().save(new YieldCategoryRule("BR", "RACK", "BR_RACK_2", new LocalDate(2008, 5, 30), new LocalDate(2027, 3, 5), 2));
        tenantCrudService().save(new YieldCategoryRule("BR", null, "BR_DEF", null, null, 3));

        operaPreProcessTransactionDataService.updateAnalyticalMarketSegments(DEFAULT_CORRELATION_ID);
        operaTransformGroupDataService.updateAnalyticalMarketSegments(DEFAULT_CORRELATION_ID);
        List<Object[]> results = tenantCrudService().findByNativeQuery("SELECT DISTINCT Market_Segment from opera.Stage_Group_Master as sgm " +
                " WHERE sgm.market_segment like ('BR%') and sgm.Data_Load_Metadata_ID IN (select Data_Load_Metadata_ID from opera.Data_Load_Metadata where correlation_ID = " +
                ":correlationId)", QueryParameter.with("correlationId", DEFAULT_CORRELATION_ID).parameters());
        assertEquals(results.size(), 1);
        assertEquals("BR_DEF", results.get(0));
        results = tenantCrudService().findByNativeQuery("SELECT DISTINCT Market_Segment from opera.Stage_Group_Master as sgm " +
                "WHERE sgm.market_segment like ('SKG%') and sgm.Data_Load_Metadata_ID IN (select Data_Load_Metadata_ID from opera.Data_Load_Metadata where correlation_ID = :correlationId)", QueryParameter.with("correlationId", DEFAULT_CORRELATION_ID).parameters());
        assertEquals(results.size(), 1);
        assertEquals("SKG", results.get(0));
    }

    private void removeGroupBlockFromStage(int operaGroupId, LocalDate blockDate, String roomTypeCode) {
        String sql = "DELETE FROM opera.Stage_Group_Block " +
                " WHERE Room_Type = :roomType AND Block_DT = :blockDate AND Group_ID = :groupId";
        int numRows = updateStageGroupBlock(operaGroupId, blockDate, roomTypeCode, sql);
        assertEquals(1, (Object) numRows, "Could not delete existing block to check for zero fill.");
    }

    private void changeRoomTypeGroupBlock(int operaGroupId, LocalDate blockDate, String roomTypeCode) {
        String sql = "Update opera.Stage_Group_Block set room_type ='K', accom_type_id=8  WHERE Room_Type = :roomType AND Block_DT = :blockDate AND Group_ID = :groupId";
        int numRows = updateStageGroupBlock(operaGroupId, blockDate, roomTypeCode, sql);
        assertEquals(1, (Object) numRows, "Could not update roomtype for existing block to check for zero fill.");
    }

    private int updateStageGroupBlock(int operaGroupId, LocalDate blockDate, String roomTypeCode, String sql) {
        return tenantCrudService().executeUpdateByNativeQuery(sql, QueryParameter.with("groupId", operaGroupId).and("roomType", roomTypeCode)
                .and("blockDate", blockDate.toDate()).parameters());
    }

    private void changeGroupCodeGroupMaster() {

        int numRows = tenantCrudService().executeUpdateByNativeQuery("Update Group_Master set group_code ='499999' where group_code ='4696343'");
        numRows += tenantCrudService().executeUpdateByNativeQuery(
                "Update opera.G3_Group_Master_Link set Opera_Group_ID='499999' where Opera_Group_ID='4696343'");
        assertEquals(2, (Object) numRows, "Could not update group_code for existing master to check for deletion of group_master.");
    }


    private LocalDateTime createAnotherLoadForNextBusinessDate(FileMetadata fileMetadata) {
        LocalDateTime nextBusinessDate = new LocalDateTime(fileMetadata.getSnapshotDt()).plusDays(1);
        createAnotherGroupDataLoadForBusinessDateTime(fileMetadata, nextBusinessDate);
        return nextBusinessDate;
    }

    private void assertNonPaceGroupBlockEquals(int operaGroupId, LocalDate occupancyDate, LocalDate businessDate, String roomType,
                                               Integer block, Integer pickup, Integer originalBlocks, BigDecimal rate) {
        String sql = "select Blocks, Pickup, Original_Blocks, rate " +
                "from Group_Block pgb " + "join Accom_Type at on at.Accom_Type_ID = pgb.Accom_Type_ID " +
                "join opera.G3_Group_Master_Link gml on gml.G3_Group_ID = pgb.Group_ID " +
                "where Occupancy_DT = :occupancyDate " + "and gml.Opera_Group_ID = :operaGroupId " +
                "and at.Accom_Type_Code = :roomType";

        Map<String, Object> params = new HashMap<>();
        params.put("operaGroupId", operaGroupId);
        params.put("occupancyDate", occupancyDate.toDate());
        params.put("roomType", roomType);
        List<?> results = tenantCrudService().findByNativeQuery(sql, params);
        assertNotNull(results);
        assertEquals(1, results.size());
        Object[] row = (Object[]) results.get(0);
        assertEquals(new BigDecimal(block), row[0]);
        assertEquals(new BigDecimal(pickup), row[1]);
        assertEquals(new BigDecimal(originalBlocks), row[2]);
        assertTrue(rate.compareTo((BigDecimal) row[3]) == 0);
    }

    private void assertGroupBlockEquals(int operaGroupId, String occupancyDate, int forecast, String roomType, int block,
                                        int pickup) {
        String sql = "select Forecast, Block, Pickup " + "from opera.Stage_Group_Block " +
                "where Group_ID = :operaGroupId " + "and Room_Type = :roomType " +
                "and Block_DT = :occupancyDate";

        Map<String, Object> params = new HashMap<>();
        params.put("operaGroupId", operaGroupId);
        params.put("occupancyDate", occupancyDate);
        params.put("roomType", roomType);
        List<?> results = tenantCrudService().findByNativeQuery(sql, params);
        assertNotNull(results);
        assertEquals(1, (Object) results.size(), "Expected GroupBlock not found for: " + occupancyDate + " " + roomType);
        Object[] row = (Object[]) results.get(0);
        assertEquals(forecast, row[0]);
        assertEquals(block, row[1]);
        assertEquals(pickup, row[2]);
    }


    private void assertPaceGroupBlockEquals(int operaGroupId, LocalDate occupancyDate, LocalDate businessDate, String roomType,
                                            Integer block, Integer pickup, Integer originalBlocks, BigDecimal rate) {
        List<?> results = findPaceGroupBlocks(operaGroupId, occupancyDate, businessDate, roomType);
        assertNotNull(results);
        assertEquals(1, results.size());
        Object[] row = (Object[]) results.get(0);
        assertEquals(new BigDecimal(block), row[0]);
        assertEquals(new BigDecimal(pickup), row[1]);
        assertEquals(new BigDecimal(originalBlocks), row[2]);
        assertTrue(rate.compareTo((BigDecimal) row[3]) == 0);
    }

    private Object[] assertGroupMasterEquals(String operaGroupId, String groupName, String groupDesc) {
        List<?> results = findGroupMaster(operaGroupId);
        assertNotNull(results);
        assertEquals(1, results.size());
        Object[] row = (Object[]) results.get(0);
        assertEquals((operaGroupId), row[0]);
        assertEquals(groupName, row[1]);
        assertEquals(groupDesc, row[2]);
        return row;
    }

    private void assertGroupMasterMSEquals(String operaGroupId, String groupName, String groupDesc) {
        Object[] row = assertGroupMasterEquals(operaGroupId, groupName, groupDesc);
        assertFalse(row[3] == new Integer(7));
    }

    private List<?> findGroupMaster(String operaGroupId) {
        String sql = "select Group_Code,Group_Name,Group_Description,mkt_seg_id  from group_master where Group_Code = :operaGroupId ";

        Map<String, Object> params = new HashMap<>();
        params.put("operaGroupId", operaGroupId);
        return tenantCrudService().findByNativeQuery(sql, params);
    }


    private List<?> findPaceGroupBlocks(int operaGroupId, LocalDate occupancyDate, LocalDate businessDate, String roomType) {
        String sql = "select Blocks, Pickup, Original_Blocks, rate " +
                "from Pace_Group_Block pgb " +
                "join Accom_Type at on at.Accom_Type_ID = pgb.Accom_Type_ID " +
                "join opera.G3_Group_Master_Link gml on gml.G3_Group_ID = pgb.Group_ID " +
                "where Occupancy_DT = :occupancyDate " +
                "and Business_Day_End_DT = :businessDate " +
                "and gml.Opera_Group_ID = :operaGroupId " +
                "and at.Accom_Type_Code = :roomType";

        Map<String, Object> params = new HashMap<>();
        params.put("operaGroupId", operaGroupId);
        params.put("occupancyDate", occupancyDate.toDate());
        params.put("businessDate", businessDate.toDate());
        params.put("roomType", roomType);
        List<?> results = tenantCrudService().findByNativeQuery(sql, params);
        return results;
    }

    private BigDecimal calculateGroupRateValue(OperaGroupBlock operaGroupBlock) {
        if (operaGroupBlock.getForecast() == 0 && operaGroupBlock.getBlock() == 0 && operaGroupBlock.getPickup() == 0) {
            return BigDecimal.ZERO;
        }

        BigDecimal sum = BigDecimal.ZERO;
        int count = 0;

        if (operaGroupBlock.getSingleRate().intValue() != 0) {
            sum = sum.add(operaGroupBlock.getSingleRate());
            count++;
        }

        if (operaGroupBlock.getDoubleRate().intValue() != 0) {
            sum = sum.add(operaGroupBlock.getDoubleRate());
            count++;
        }

        if (operaGroupBlock.getTripleRate().intValue() != 0) {
            sum = sum.add(operaGroupBlock.getTripleRate());
            count++;
        }

        if (operaGroupBlock.getQuadrupleRate().intValue() != 0) {
            sum = sum.add(operaGroupBlock.getQuadrupleRate());
            count++;
        }

        if (count == 0) {
            return BigDecimal.ZERO;
        }

        return sum.divide(new BigDecimal(count));
    }


    @Test
    public void testAdjustGroupStatusTypeFilter() {
        String sql = "select * from opera.Stage_Group_Master where group_id = 88885";
        List<?> results = tenantCrudService().findByNativeQuery(sql);
        assertEquals(1, results.size());
    }

}
