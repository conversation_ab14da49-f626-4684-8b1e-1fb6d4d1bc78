package com.ideas.tetris.pacman.services.independentproducts.repository;

import com.ideas.g3.test.AbstractG3JupiterTest;
import com.ideas.tetris.pacman.services.marketsegment.entity.BusinessType;
import com.ideas.tetris.pacman.services.marketsegment.entity.ForecastActivityType;
import com.ideas.tetris.pacman.services.marketsegment.entity.MarketSegmentProductMapping;
import com.ideas.tetris.pacman.services.marketsegment.entity.MktSeg;
import com.ideas.tetris.pacman.services.marketsegment.entity.MktSegDetails;
import com.ideas.tetris.pacman.services.marketsegment.entity.MktSegDetailsProposed;
import com.ideas.tetris.pacman.services.marketsegment.entity.ProcessStatus;
import com.ideas.tetris.pacman.services.marketsegment.entity.UniqueMktSegCreator;
import com.ideas.tetris.pacman.services.marketsegment.entity.YieldType;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;

import java.util.List;
import java.util.Map;

import static com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper.getPropertyId;
import static com.ideas.tetris.pacman.util.Runner.collectToMap;
import static org.apache.commons.collections.CollectionUtils.isEmpty;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

public class IndependentProductMigrationRepositoryMarketSegmentMigrationESATest extends AbstractG3JupiterTest {

    private static final Integer GROUP = 1;
    private static final Integer TRANSIENT = 2;
    private static final Integer DEMAND_AND_WASH = 1;
    private static final Integer WASH = 2;
    private static final Integer NONE = 3;
    private static final Integer YIELDABLE = 1;
    private static final Integer SEMIYIELDABLE = 2;
    private static final Integer NONYIELDABLE = 3;
    private static final Integer QUALIFIED = 1;
    private static final Integer UNQUALIFIED = 0;

    @InjectMocks
    IndependentProductMigrationRepository repository;
    static private Map<Integer, BusinessType> businessTypes;
    static private Map<Integer, ForecastActivityType> forecastActivityTypes;
    static private Map<Integer, YieldType> yieldTypes;
    static private ProcessStatus processStatus;

    @BeforeEach
    public void setUp() {
        if (businessTypes == null) {
            businessTypes = collectToMap(tenantCrudService().findAll(BusinessType.class), BusinessType::getId, b -> b);
            forecastActivityTypes = collectToMap(tenantCrudService().findAll(ForecastActivityType.class), ForecastActivityType::getId, b -> b);
            yieldTypes = collectToMap(tenantCrudService().findAll(YieldType.class), YieldType::getId, b -> b);
            processStatus = tenantCrudService().findByNamedQuerySingleResult(ProcessStatus.BY_NAME, QueryParameter.with("name", "Successful").parameters());
        }
        repository.tenantCrudService = tenantCrudService();
        tenantCrudService().executeUpdateByNativeQuery("update Mkt_Seg set Status_ID = 2;");
    }

    @Test
    void getMarketSegmentsAssociatedWithBAR_Group_Demand_And_Wash() {
        lets()
                .start()
                .withBusinessType(GROUP)
                .withYieldType(YIELDABLE)
                .withQualified(QUALIFIED)
                .withForecastActivityType(DEMAND_AND_WASH)
                .withFenced(false)
                .withPackaged(false)
                .withLinked(false)
                .saveMS()
                .execute()
                .verifyAbsent();
    }

    @Test
    void getMarketSegmentsAssociatedWithBAR_Group_Wash() {
        lets()
                .start()
                .withBusinessType(GROUP)
                .withYieldType(YIELDABLE)
                .withQualified(QUALIFIED)
                .withForecastActivityType(WASH)
                .withLinked(false)
                .withFenced(false)
                .withPackaged(false)
                .saveMS()
                .execute()
                .verifyAbsent();
    }

    @Test
    void getMarketSegmentsAssociatedWithBAR_Group_None() {
        lets()
                .start()
                .withBusinessType(GROUP)
                .withYieldType(YIELDABLE)
                .withQualified(QUALIFIED)
                .withForecastActivityType(NONE)
                .withFenced(false)
                .withPackaged(false)
                .withLinked(false)
                .saveMS()
                .execute()
                .verifyAbsent();
    }

    @Test
    void getMarketSegmentsAssociatedWithBAR_TransientBlock_Demand_And_Wash() {
        lets()
                .start()
                .withBusinessType(TRANSIENT)
                .withYieldType(YIELDABLE)
                .withForecastActivityType(DEMAND_AND_WASH)
                .withQualified(QUALIFIED)
                .withFenced(false)
                .withPackaged(false)
                .withLinked(false)
                .saveMS()
                .execute()
                .verifyAbsent();
    }

    @Test
    void getMarketSegmentsAssociatedWithBAR_TransientBlock_Wash() {
        lets()
                .start()
                .withBusinessType(TRANSIENT)
                .withYieldType(YIELDABLE)
                .withForecastActivityType(WASH)
                .withQualified(QUALIFIED)
                .withFenced(false)
                .withPackaged(false)
                .withLinked(false)
                .saveMS()
                .execute()
                .verifyAbsent();
    }

    @Test
    void getMarketSegmentsAssociatedWithBAR_TransientBlock_None() {
        lets()
                .start()
                .withBusinessType(TRANSIENT)
                .withYieldType(YIELDABLE)
                .withForecastActivityType(NONE)
                .withQualified(QUALIFIED)
                .withFenced(false)
                .withPackaged(false)
                .withLinked(false)
                .saveMS()
                .execute()
                .verifyAbsent();
    }

    @Test
    void getMarketSegmentsAssociatedWithBAR_Unqualified_PricedByBar() {
        lets()
                .start()
                .withBusinessType(TRANSIENT)
                .withQualified(UNQUALIFIED)
                .withYieldType(YIELDABLE)
                .withForecastActivityType(DEMAND_AND_WASH)
                .withPricedByBar(true)
                .withFenced(false)
                .withPackaged(false)
                .withLinked(false)
                .saveMS()
                .execute()
                .verifyPresentIPAndProductID();
    }

    @Test
    void getMarketSegmentsAssociatedWithBAR_Unqualified_Fenced() {
        lets()
                .start()
                .withBusinessType(TRANSIENT)
                .withQualified(UNQUALIFIED)
                .withYieldType(YIELDABLE)
                .withForecastActivityType(DEMAND_AND_WASH)
                .withPricedByBar(false)
                .withFenced(true)
                .withPackaged(false)
                .withLinked(false)
                .saveMS()
                .execute()
                .verifyPresentIPAndProductID();
    }

    @Test
    void getMarketSegmentsAssociatedWithBAR_Unqualified_Packaged() {
        lets()
                .start()
                .withBusinessType(TRANSIENT)
                .withQualified(UNQUALIFIED)
                .withYieldType(YIELDABLE)
                .withForecastActivityType(DEMAND_AND_WASH)
                .withPricedByBar(false)
                .withFenced(false)
                .withPackaged(true)
                .withLinked(false)
                .saveMS()
                .execute()
                .verifyPresentIPAndProductID();
    }

    @Test
    void getMarketSegmentsAssociatedWithBAR_Unqualified_FencedPackaged() {
        lets()
                .start()
                .withBusinessType(TRANSIENT)
                .withQualified(UNQUALIFIED)
                .withYieldType(YIELDABLE)
                .withForecastActivityType(DEMAND_AND_WASH)
                .withPricedByBar(false)
                .withFenced(true)
                .withPackaged(true)
                .withLinked(false)
                .saveMS()
                .execute()
                .verifyPresentIPAndProductID();
    }

    @Test
    void getMarketSegmentsAssociatedWithBAR_Unqualified_UnFencedNonPackaged() {
        lets()
                .start()
                .withBusinessType(TRANSIENT)
                .withQualified(UNQUALIFIED)
                .withYieldType(YIELDABLE)
                .withForecastActivityType(DEMAND_AND_WASH)
                .withPricedByBar(false)
                .withFenced(false)
                .withPackaged(false)
                .withLinked(false)
                .saveMS()
                .execute()
                .verifyPresentIPAndProductID();
    }

    @Test
    void getMarketSegmentsAssociatedWithBAR_Qualified_Linked_Yieldable_DemandAndWash() {
        lets()
                .start()
                .withBusinessType(TRANSIENT)
                .withQualified(QUALIFIED)
                .withYieldType(YIELDABLE)
                .withForecastActivityType(DEMAND_AND_WASH)
                .withPricedByBar(false)
                .withFenced(false)
                .withPackaged(false)
                .withLinked(true)
                .saveMS()
                .execute()
                .verifyPresentIPAndProductID();
    }

    @Test
    void getMarketSegmentsAssociatedWithBAR_Qualified_Linked_Yieldable_Wash() {
        lets()
                .start()
                .withBusinessType(TRANSIENT)
                .withQualified(QUALIFIED)
                .withYieldType(YIELDABLE)
                .withForecastActivityType(WASH)
                .withPricedByBar(false)
                .withFenced(false)
                .withPackaged(false)
                .withLinked(true)
                .saveMS()
                .execute()
                .verifyAbsent();
    }

    @Test
    void getMarketSegmentsAssociatedWithBAR_Qualified_Linked_Yieldable_None() {
        lets()
                .start()
                .withBusinessType(TRANSIENT)
                .withQualified(QUALIFIED)
                .withYieldType(YIELDABLE)
                .withForecastActivityType(NONE)
                .withPricedByBar(false)
                .withFenced(false)
                .withPackaged(false)
                .withLinked(true)
                .saveMS()
                .execute()
                .verifyAbsent();
    }

    @Test
    void getMarketSegmentsAssociatedWithBAR_Qualified_Linked_SemiYieldable_DemandAndWash() {
        lets()
                .start()
                .withBusinessType(TRANSIENT)
                .withQualified(QUALIFIED)
                .withYieldType(SEMIYIELDABLE)
                .withForecastActivityType(DEMAND_AND_WASH)
                .withPricedByBar(false)
                .withFenced(false)
                .withPackaged(false)
                .withLinked(true)
                .saveMS()
                .execute()
                .verifyPresentIPAndProductID();
    }

    @Test
    void getMarketSegmentsAssociatedWithBAR_Qualified_Linked_SemiYieldable_Wash() {
        lets()
                .start()
                .withBusinessType(TRANSIENT)
                .withQualified(QUALIFIED)
                .withYieldType(SEMIYIELDABLE)
                .withForecastActivityType(WASH)
                .withPricedByBar(false)
                .withFenced(false)
                .withPackaged(false)
                .withLinked(true)
                .saveMS()
                .execute()
                .verifyAbsent();
    }

    @Test
    void getMarketSegmentsAssociatedWithBAR_Qualified_Linked_SemiYieldable_None() {
        lets()
                .start()
                .withBusinessType(TRANSIENT)
                .withQualified(QUALIFIED)
                .withYieldType(SEMIYIELDABLE)
                .withForecastActivityType(NONE)
                .withPricedByBar(false)
                .withFenced(false)
                .withPackaged(false)
                .withLinked(true)
                .saveMS()
                .execute()
                .verifyAbsent();
    }

    @Test
    void getMarketSegmentsAssociatedWithBAR_Qualified_Linked_NonYieldable_DemandAndWash() {
        lets()
                .start()
                .withBusinessType(TRANSIENT)
                .withQualified(QUALIFIED)
                .withYieldType(NONYIELDABLE)
                .withForecastActivityType(DEMAND_AND_WASH)
                .withPricedByBar(false)
                .withFenced(false)
                .withPackaged(false)
                .withLinked(true)
                .saveMS()
                .execute()
                .verifyPresentIPAndProductID();
    }

    @Test
    void getMarketSegmentsAssociatedWithBAR_Qualified_Linked_NonYieldable_Wash() {
        lets()
                .start()
                .withBusinessType(TRANSIENT)
                .withQualified(QUALIFIED)
                .withYieldType(NONYIELDABLE)
                .withForecastActivityType(WASH)
                .withPricedByBar(false)
                .withFenced(false)
                .withPackaged(false)
                .withLinked(true)
                .saveMS()
                .execute()
                .verifyAbsent();
    }

    @Test
    void getMarketSegmentsAssociatedWithBAR_Qualified_Linked_NonYieldable_None() {
        lets()
                .start()
                .withBusinessType(TRANSIENT)
                .withQualified(QUALIFIED)
                .withYieldType(NONYIELDABLE)
                .withForecastActivityType(NONE)
                .withPricedByBar(false)
                .withFenced(false)
                .withPackaged(false)
                .withLinked(true)
                .saveMS()
                .execute()
                .verifyAbsent();
    }

    @Test
    void getMarketSegmentsAssociatedWithBAR_Qualified_NonLinked_Yieldable_DemandAndWash() {
        lets()
                .start()
                .withBusinessType(TRANSIENT)
                .withQualified(QUALIFIED)
                .withYieldType(YIELDABLE)
                .withForecastActivityType(DEMAND_AND_WASH)
                .withPricedByBar(false)
                .withFenced(false)
                .withPackaged(false)
                .withLinked(false)
                .saveMS()
                .execute()
                .verifyAbsent();
    }

    @Test
    void getMarketSegmentsAssociatedWithBAR_Qualified_NonLinked_Yieldable_Wash() {
        lets()
                .start()
                .withBusinessType(TRANSIENT)
                .withQualified(QUALIFIED)
                .withYieldType(YIELDABLE)
                .withForecastActivityType(WASH)
                .withPricedByBar(false)
                .withFenced(false)
                .withPackaged(false)
                .withLinked(false)
                .saveMS()
                .execute()
                .verifyAbsent();
    }

    @Test
    void getMarketSegmentsAssociatedWithBAR_Qualified_NonLinked_Yieldable_None() {
        lets()
                .start()
                .withBusinessType(TRANSIENT)
                .withQualified(QUALIFIED)
                .withYieldType(YIELDABLE)
                .withForecastActivityType(NONE)
                .withPricedByBar(false)
                .withFenced(false)
                .withPackaged(false)
                .withLinked(false)
                .saveMS()
                .execute()
                .verifyAbsent();
    }

    @Test
    void getMarketSegmentsAssociatedWithBAR_Qualified_NonLinked_SemiYieldable_DemandAndWash() {
        lets()
                .start()
                .withBusinessType(TRANSIENT)
                .withQualified(QUALIFIED)
                .withYieldType(SEMIYIELDABLE)
                .withForecastActivityType(DEMAND_AND_WASH)
                .withPricedByBar(false)
                .withFenced(false)
                .withPackaged(false)
                .withLinked(false)
                .saveMS()
                .execute()
                .verifyPresentIPAndProductID();
    }

    @Test
    void getMarketSegmentsAssociatedWithBAR_Qualified_NonLinked_SemiYieldable_Wash() {
        lets()
                .start()
                .withBusinessType(TRANSIENT)
                .withQualified(QUALIFIED)
                .withYieldType(SEMIYIELDABLE)
                .withForecastActivityType(WASH)
                .withPricedByBar(false)
                .withFenced(false)
                .withPackaged(false)
                .withLinked(false)
                .saveMS()
                .execute()
                .verifyAbsent();
    }

    @Test
    void getMarketSegmentsAssociatedWithBAR_Qualified_NonLinked_SemiYieldable_None() {
        lets()
                .start()
                .withBusinessType(TRANSIENT)
                .withQualified(QUALIFIED)
                .withYieldType(SEMIYIELDABLE)
                .withForecastActivityType(NONE)
                .withPricedByBar(false)
                .withFenced(false)
                .withPackaged(false)
                .withLinked(false)
                .saveMS()
                .execute()
                .verifyAbsent();
    }

    @Test
    void getMarketSegmentsAssociatedWithBAR_Qualified_NonLinked_NonYieldable_DemandAndWash() {
        lets()
                .start()
                .withBusinessType(TRANSIENT)
                .withQualified(QUALIFIED)
                .withYieldType(NONYIELDABLE)
                .withForecastActivityType(DEMAND_AND_WASH)
                .withPricedByBar(false)
                .withFenced(false)
                .withPackaged(false)
                .withLinked(false)
                .saveMS()
                .execute()
                .verifyPresentIPAndProductID();
    }

    @Test
    void getMarketSegmentsAssociatedWithBAR_Qualified_NonLinked_NonYieldable_Wash() {
        lets()
                .start()
                .withBusinessType(TRANSIENT)
                .withQualified(QUALIFIED)
                .withYieldType(NONYIELDABLE)
                .withForecastActivityType(WASH)
                .withPricedByBar(false)
                .withFenced(false)
                .withPackaged(false)
                .withLinked(false)
                .saveMS()
                .execute()
                .verifyAbsent();
    }

    @Test
    void getMarketSegmentsAssociatedWithBAR_Qualified_NonLinked_NonYieldable_None() {
        lets()
                .start()
                .withBusinessType(TRANSIENT)
                .withQualified(QUALIFIED)
                .withYieldType(NONYIELDABLE)
                .withForecastActivityType(NONE)
                .withPricedByBar(false)
                .withFenced(false)
                .withPackaged(false)
                .withLinked(false)
                .saveMS()
                .execute()
                .verifyAbsent();
    }


    Context lets() {
        return new Context();
    }

    class Context {
        private MktSeg mktSeg = new MktSeg();
        private MktSegDetails details = new MktSegDetails();
        private MktSegDetailsProposed detailsProposed = null;
        private List<MarketSegmentProductMapping> result;
        private String code = "MS";

        public Context start() {
            mktSeg = UniqueMktSegCreator.createUniqueMktSeg(code, getPropertyId());
            details.setBookingBlockPc(0);
            details.setProcessStatus(processStatus);
            details.setStatusId(1);
            details.setTemplateDefault(0);
            return this;
        }

        public Context saveMS() {
            mktSeg.setMktSegDetails(details);
            details.setMktSeg(mktSeg);
            tenantCrudService().save(details);
            if (detailsProposed != null) {
                detailsProposed.setMktSeg(mktSeg);
                mktSeg.setMktSegDetailsProposed(detailsProposed);
                tenantCrudService().save(detailsProposed);
            }
            tenantCrudService().save(mktSeg);
            return this;
        }

        public Context withBusinessType(Integer businessType) {
            details.setBusinessType(businessTypes.get(businessType));
            return this;
        }

        public Context withForecastActivityType(Integer forecastActivityType) {
            details.setForecastActivityType(forecastActivityTypes.get(forecastActivityType));
            return this;
        }

        public Context withYieldType(Integer yieldType) {
            details.setYieldType(yieldTypes.get(yieldType));
            return this;
        }

        public Context withQualified(Integer qualified) {
            details.setQualified(qualified);
            return this;
        }

        public Context withPricedByBar(boolean pbb) {
            details.setPriceByBar(pbb ? 1 : 0);
            return this;
        }

        public Context withFenced(boolean fenced) {
            details.setFenced(fenced ? 1 : 0);
            return this;
        }

        public Context withPackaged(boolean packaged) {
            details.setPackageValue(packaged ? 1 : 0);
            return this;
        }

        public Context withLinked(boolean linked) {
            details.setLink(linked ? 1 : 0);
            return this;
        }

        public Context execute() {
            repository.createMarketSegmentProductMappingsForExistingMarketSegmentsBasedOnMktSegDetails();
            result = tenantCrudService().findAll(MarketSegmentProductMapping.class);
            return this;
        }

        public void verifyAbsent() {
            assertTrue(isEmpty(result));
        }

        public Context verifyPresentIPAndProductID() {
            assertFalse(isEmpty(result));
            assertEquals(1, result.size());
            assertEquals(code, result.get(0).getMarketSegmentCode());
            assertEquals(1, result.get(0).getProduct().getId());
            assertEquals(details.getPriceByBar() == 1, result.get(0).isIp());
            return this;
        }

    }
}
