package com.ideas.tetris.pacman.services.limiteddatabuild;

import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.IndividualTransactions;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.TotalActivity;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.FileMetadata;
import com.ideas.tetris.pacman.services.limiteddatabuild.dto.BookingWindow;
import com.ideas.tetris.pacman.services.limiteddatabuild.dto.ClonePatternSource;
import com.ideas.tetris.pacman.services.limiteddatabuild.dto.GenericPatternData;
import com.ideas.tetris.pacman.services.limiteddatabuild.entity.LDBBuildHistory;
import com.ideas.tetris.pacman.services.limiteddatabuild.entity.LDBClonePatternSource;
import com.ideas.tetris.pacman.services.limiteddatabuild.entity.LDBConfig;
import com.ideas.tetris.pacman.services.limiteddatabuild.entity.LDBGenericBookingCurve;
import com.ideas.tetris.pacman.services.limiteddatabuild.entity.LDBGenericBookingCurvePoint;
import com.ideas.tetris.pacman.services.limiteddatabuild.entity.LDBGenericLOSDistribution;
import com.ideas.tetris.pacman.services.limiteddatabuild.entity.LDBGenericPatternData;
import com.ideas.tetris.pacman.services.limiteddatabuild.entity.LDBGlobalGenericBookingCurve;
import com.ideas.tetris.pacman.services.limiteddatabuild.entity.LDBGlobalGenericBookingCurvePoint;
import com.ideas.tetris.pacman.services.limiteddatabuild.entity.LDBHotelProfile;
import com.ideas.tetris.pacman.services.limiteddatabuild.entity.PatternSource;
import com.ideas.tetris.pacman.services.marketsegment.component.MarketSegmentComponent;
import com.ideas.tetris.pacman.services.marketsegment.entity.BusinessType;
import com.ideas.tetris.pacman.services.marketsegment.entity.MktSeg;
import com.ideas.tetris.pacman.services.marketsegment.entity.MktSegDetails;
import com.ideas.tetris.pacman.services.marketsegment.entity.MktSegDetailsProposed;
import com.ideas.tetris.pacman.services.property.PropertyService;
import com.ideas.tetris.platform.common.crudservice.AbstractMultiPropertyCrudService;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.xml.schema.workcontext.v1.WorkContextType;
import com.ideas.tetris.platform.services.daoandentities.entity.Property;
import org.apache.commons.io.FileUtils;
import org.joda.time.LocalDate;
import org.joda.time.LocalDateTime;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Scanner;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

class LDBPatternServiceTest {

    private static final int PROPERTY_ID = 913;

    private static final int CLIENT_ID = 526;

    private static final String PROPERTY_CODE = "BOSCO";

    private static final String CLIENT_CODE = "Hilton";

    private static final Integer MARKET_SEGMENT_ID_1 = 1211;

    private static final Integer MARKET_SEGMENT_ID_2 = 1212;

    private static final Integer BOOKING_CURVE_ID = 113;

    private static final BigDecimal AVERAGE_LOS = new BigDecimal("1.913");

    private static final BigDecimal NO_SHOW_PROBABLITY = new BigDecimal("0.12");

    private static final String MARKET_SEGMENT_CODE = "CONV";

    private static final String LABEL = "myLabel";

    public static final String NEWMKTSEG = "NEWMKTSEG";

    public static final LDBGenericBookingCurve BOOKING_CURVE = new LDBGenericBookingCurve();

    private static final String TEST_BOOKING_CURVE_345_POINTS = "1.00000, 0.90914, 0.83060, 0.78442, 0.74295, 0.70727, 0.66960, 0.63372, 0.60514, 0.58511, 0.55972, 0.53704, 0.52494, 0.49423, 0.47795, 0.45977, 0.44488, 0.43320, 0.41853, 0.40464, 0.39311, 0.37957, 0.36968, 0.35221, 0.34027, 0.32608, 0.31661, 0.30857, 0.30387, 0.29855, 0.28743, 0.28014, 0.27083, 0.26138, 0.25259, 0.24816, 0.24228, 0.23431, 0.22404, 0.22079, 0.20997, 0.20440, 0.19991, 0.19873, 0.19469, 0.18955, 0.18242, 0.17744, 0.16886, 0.16441, 0.16118, 0.15845, 0.15471, 0.15049, 0.14841, 0.14685, 0.14478, 0.14385, 0.14236, 0.14046, 0.13669, 0.13540, 0.13164, 0.12548, 0.12379, 0.12293, 0.12109, 0.11954, 0.11816, 0.11642, 0.11460, 0.11344, 0.11153, 0.10949, 0.10731, 0.10641, 0.10557, 0.10354, 0.10336, 0.10257, 0.10085, 0.10030, 0.09942, 0.09528, 0.09450, 0.09282, 0.09168, 0.09031, 0.08831, 0.08564, 0.08385, 0.08375, 0.08365, 0.08355, 0.08335, 0.08325, 0.08305, 0.08295, 0.08275, 0.08265, 0.08245, 0.08235, 0.07295, 0.06346, 0.06186, 0.06016, 0.05856, 0.05686, 0.05526, 0.05356, 0.05196, 0.05026, 0.04866, 0.04696, 0.04536, 0.04366, 0.04204, 0.04034, 0.03864, 0.03704, 0.03534, 0.03374, 0.03204, 0.03044, 0.02874, 0.02714, 0.02544, 0.02384, 0.02214, 0.02054, 0.01884, 0.01722, 0.01612, 0.01492, 0.01382, 0.01262, 0.01152, 0.01032, 0.00922, 0.00802, 0.00692, 0.00572, 0.00462, 0.00342, 0.00232, 0.00112, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000";

    private static String ldbFolder;

    private static final String[] HILTON_LDB_MARKET_SEGMENTS = {"BAR", "CONS", "CMP", "HOU", "GT", "CMTG", "CONV", "SMRF", "GOV", "PERM", "DISC", "MKT", "IT", "LNR", "CNR"};

    @Mock
    ProjectionDataService projectionDataService;

    @Mock
    PropertyService propertyService;

    @Mock
    MarketSegmentComponent marketSegmentComponent;

    @Mock
    private CrudService tenantCrudService;

    @Mock
    private CrudService globalCrudService;

    @Mock
    private AbstractMultiPropertyCrudService multiPropertyCrudService;

    @InjectMocks
    private LDBPatternService ldbPatternService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.initMocks(this);
        WorkContextType workContext = new WorkContextType();
        workContext.setPropertyId(PROPERTY_ID);
        workContext.setPropertyCode(PROPERTY_CODE);
        workContext.setClientId(CLIENT_ID);
        workContext.setClientCode(CLIENT_CODE);
        PacmanWorkContextHelper.setWorkContext(workContext);
        ldbFolder = this.getClass().getResource("/").getFile();
    }

    @Test
    void test_getClonePatternsLastUpdate() {
        LDBClonePatternSource source = new LDBClonePatternSource();
        List<LDBClonePatternSource> sources = Arrays.asList(source);
        when(multiPropertyCrudService.findByNamedQueryForSingleProperty(PacmanWorkContextHelper.getPropertyId(), LDBClonePatternSource.GET_LAST_UPDATE, new HashMap<String, Object>())).thenReturn(sources);
        LDBClonePatternSource results = ldbPatternService.getClonePatternsLastUpdate();
        assertEquals(source, results);
    }

    @Test
    void test_getGenericPatternsLastUpdate() {
        LDBGenericPatternData source = new LDBGenericPatternData();
        List<LDBGenericPatternData> sources = Arrays.asList(source);
        when(multiPropertyCrudService.findByNamedQueryForSingleProperty(PacmanWorkContextHelper.getPropertyId(), LDBGenericPatternData.GET_LAST_UPDATE, new HashMap<String, Object>())).thenReturn(sources);
        LDBGenericPatternData results = ldbPatternService.getGenericPatternsLastUpdate();
        assertEquals(source, results);
    }

    @Test
    void test_getHotelProfile() {
        String PROFILE = "myProfile";
        LDBHotelProfile profile = new LDBHotelProfile();
        when(globalCrudService.findByNamedQuerySingleResult(LDBHotelProfile.BY_LABEL, QueryParameter.with("label", PROFILE).parameters())).thenReturn(profile);
        LDBHotelProfile results = ldbPatternService.getHotelProfile(PROFILE);
        assertEquals(profile, results);
    }

    @Test
    void test_createHotelProfile() {
        String PROFILE = "myProfile";
        LDBHotelProfile profile = new LDBHotelProfile();
        when(globalCrudService.save(Mockito.any(LDBHotelProfile.class))).thenReturn(profile);
        LDBHotelProfile results = ldbPatternService.createHotelProfile(PROFILE);
        assertEquals(profile, results);
        ArgumentCaptor<LDBHotelProfile> saveCaptor = ArgumentCaptor.forClass(LDBHotelProfile.class);
        verify(globalCrudService).save(saveCaptor.capture());
        LDBHotelProfile entity = saveCaptor.getValue();
        assertEquals(PROFILE, entity.getLabel());
        verify(globalCrudService).save(Mockito.any(LDBHotelProfile.class));
    }

    @Test
    void test_createGlobalGenericDataForTesting() {
        LDBHotelProfile profile = new LDBHotelProfile();
        profile.setLabel(LDBPatternService.TEST_PROFILE_LABEL);
        when(globalCrudService.save(Mockito.any(LDBHotelProfile.class))).thenReturn(profile, null, null, null, null);
        when(globalCrudService.findByNamedQuerySingleResult(LDBHotelProfile.BY_LABEL, QueryParameter.with("label", LDBPatternService.TEST_PROFILE_LABEL).parameters())).thenReturn(null);
        ldbPatternService.createGlobalGenericDataForTesting();
        ArgumentCaptor<LDBGlobalGenericBookingCurve> saveCaptor = ArgumentCaptor.forClass(LDBGlobalGenericBookingCurve.class);
        verify(globalCrudService, times(5)).save(saveCaptor.capture());
        List<LDBGlobalGenericBookingCurve> entities = saveCaptor.getAllValues();
        assertEquals(5, entities.size());
        LDBGlobalGenericBookingCurve entity = entities.get(1);
        assertEquals(profile, entity.getHotelProfile());
        assertEquals(BookingWindow.LONG.toString(), entity.getBookingWindow());
        assertEquals(Constants.BUSINESS_TYPE_TRANSIENT, entity.getBusinessTypeId());
        assertEquals(347, entity.getPoints().size());
    }

    @Test
    void test_getGlobalBookingCurves() {
        LDBGlobalGenericBookingCurve curve = new LDBGlobalGenericBookingCurve();
        LDBGlobalGenericBookingCurvePoint point = new LDBGlobalGenericBookingCurvePoint();
        curve.setPoints(Arrays.asList(point));
        List<LDBGlobalGenericBookingCurve> curves = Arrays.asList(curve);
        when(globalCrudService.findAll(LDBGlobalGenericBookingCurve.class)).thenReturn(curves);
        List<LDBGlobalGenericBookingCurve> results = ldbPatternService.getGlobalBookingCurves();
        assertEquals(curves, results);
    }

    @Test
    void test_getGlobalGenericBookingCurve() {
        String LABEL = "myLabel";
        LDBGlobalGenericBookingCurve entity = new LDBGlobalGenericBookingCurve();
        when(globalCrudService.findByNamedQuerySingleResult(LDBGlobalGenericBookingCurve.GET_BY_LABEL, QueryParameter.with("label", LABEL).parameters())).thenReturn(entity);
        LDBGlobalGenericBookingCurve results = ldbPatternService.getGlobalGenericBookingCurve(LABEL);
        assertEquals(entity, results);
    }

    @Test
    void test_isGenericPatternSourceConfigurationComplete_true() throws IOException {
        LDBGenericPatternData sundayData = buildGenericData(Calendar.SUNDAY);
        LDBGenericPatternData mondayData = buildGenericData(Calendar.MONDAY);
        LDBGenericPatternData tuesdayData = buildGenericData(Calendar.TUESDAY);
        LDBGenericPatternData wednesdayData = buildGenericData(Calendar.WEDNESDAY);
        LDBGenericPatternData thursdayData = buildGenericData(Calendar.THURSDAY);
        LDBGenericPatternData fridayData = buildGenericData(Calendar.FRIDAY);
        LDBGenericPatternData saturdayData = buildGenericData(Calendar.SATURDAY);
        List<LDBGenericPatternData> genericData = Arrays.asList(sundayData, mondayData, tuesdayData, wednesdayData, thursdayData, fridayData, saturdayData);
        when(tenantCrudService.findAll(LDBGenericPatternData.class)).thenReturn(genericData);
        MktSeg marketSegment = new MktSeg();
        marketSegment.setId(MARKET_SEGMENT_ID_1);
        marketSegment.setCode(MARKET_SEGMENT_CODE);
        List<MktSeg> marketSegments = Arrays.asList(marketSegment);
        when(tenantCrudService.<MktSeg>findByNamedQuery(MktSeg.BY_PROPERTY_ID, QueryParameter.with("propertyId", PROPERTY_ID).parameters())).thenReturn(marketSegments);
        assertTrue(ldbPatternService.isGenericPatternSourceConfigurationComplete());
    }

    @Test
    void test_isGenericPatternSourceConfigurationComplete_missingBookingCurve() throws IOException {
        LDBGenericPatternData sundayData = buildGenericData(Calendar.SUNDAY);
        LDBGenericPatternData mondayData = buildGenericData(Calendar.MONDAY);
        LDBGenericPatternData tuesdayData = buildGenericData(Calendar.TUESDAY);
        LDBGenericPatternData wednesdayData = buildGenericData(Calendar.WEDNESDAY);
        LDBGenericPatternData thursdayData = buildGenericData(Calendar.THURSDAY);
        LDBGenericPatternData fridayData = buildGenericData(Calendar.FRIDAY);
        LDBGenericPatternData saturdayData = buildGenericData(Calendar.SATURDAY);
        saturdayData.setBookingCurve(null);
        List<LDBGenericPatternData> genericData = Arrays.asList(sundayData, mondayData, tuesdayData, wednesdayData, thursdayData, fridayData, saturdayData);
        when(tenantCrudService.findAll(LDBGenericPatternData.class)).thenReturn(genericData);
        MktSeg marketSegment = new MktSeg();
        marketSegment.setId(MARKET_SEGMENT_ID_1);
        marketSegment.setCode(MARKET_SEGMENT_CODE);
        List<MktSeg> marketSegments = Arrays.asList(marketSegment);
        when(tenantCrudService.<MktSeg>findByNamedQuery(MktSeg.BY_PROPERTY_ID, QueryParameter.with("propertyId", PROPERTY_ID).parameters())).thenReturn(marketSegments);
        assertFalse(ldbPatternService.isGenericPatternSourceConfigurationComplete());
    }

    @Test
    void test_isGenericPatternSourceConfigurationComplete_missingNoShowProbability() throws IOException {
        LDBGenericPatternData sundayData = buildGenericData(Calendar.SUNDAY);
        LDBGenericPatternData mondayData = buildGenericData(Calendar.MONDAY);
        LDBGenericPatternData tuesdayData = buildGenericData(Calendar.TUESDAY);
        LDBGenericPatternData wednesdayData = buildGenericData(Calendar.WEDNESDAY);
        LDBGenericPatternData thursdayData = buildGenericData(Calendar.THURSDAY);
        LDBGenericPatternData fridayData = buildGenericData(Calendar.FRIDAY);
        LDBGenericPatternData saturdayData = buildGenericData(Calendar.SATURDAY);
        saturdayData.setNoShowProbability(null);
        List<LDBGenericPatternData> genericData = Arrays.asList(sundayData, mondayData, tuesdayData, wednesdayData, thursdayData, fridayData, saturdayData);
        when(tenantCrudService.findAll(LDBGenericPatternData.class)).thenReturn(genericData);
        MktSeg marketSegment = new MktSeg();
        marketSegment.setId(MARKET_SEGMENT_ID_1);
        marketSegment.setCode(MARKET_SEGMENT_CODE);
        List<MktSeg> marketSegments = Arrays.asList(marketSegment);
        when(tenantCrudService.<MktSeg>findByNamedQuery(MktSeg.BY_PROPERTY_ID, QueryParameter.with("propertyId", PROPERTY_ID).parameters())).thenReturn(marketSegments);
        assertFalse(ldbPatternService.isGenericPatternSourceConfigurationComplete());
    }

    @Test
    void test_isGenericPatternSourceConfigurationComplete_missingDate() throws IOException {
        LDBGenericPatternData sundayData = buildGenericData(Calendar.SUNDAY);
        LDBGenericPatternData mondayData = buildGenericData(Calendar.MONDAY);
        LDBGenericPatternData tuesdayData = buildGenericData(Calendar.TUESDAY);
        LDBGenericPatternData wednesdayData = buildGenericData(Calendar.WEDNESDAY);
        LDBGenericPatternData thursdayData = buildGenericData(Calendar.THURSDAY);
        LDBGenericPatternData fridayData = buildGenericData(Calendar.FRIDAY);
        LDBGenericPatternData saturdayData = buildGenericData(Calendar.SATURDAY);
        saturdayData.setNoShowProbability(null);
        List<LDBGenericPatternData> genericData = Arrays.asList(sundayData, mondayData, tuesdayData, wednesdayData, thursdayData, fridayData);
        when(tenantCrudService.findAll(LDBGenericPatternData.class)).thenReturn(genericData);
        MktSeg marketSegment = new MktSeg();
        marketSegment.setId(MARKET_SEGMENT_ID_1);
        marketSegment.setCode(MARKET_SEGMENT_CODE);
        List<MktSeg> marketSegments = Arrays.asList(marketSegment);
        when(tenantCrudService.<MktSeg>findByNamedQuery(MktSeg.BY_PROPERTY_ID, QueryParameter.with("propertyId", PROPERTY_ID).parameters())).thenReturn(marketSegments);
        assertFalse(ldbPatternService.isGenericPatternSourceConfigurationComplete());
    }

    private LDBGenericPatternData buildGenericData(int dow) {
        LDBGenericPatternData data = new LDBGenericPatternData();
        data.setAverageLOS(AVERAGE_LOS);
        data.setNoShowProbability(new BigDecimal("10"));
        data.setDayOfWeek(dow);
        data.setMarketSegmentId(MARKET_SEGMENT_ID_1);
        LDBGenericBookingCurve bookingCurve = new LDBGenericBookingCurve();
        data.setBookingCurve(bookingCurve);
        return data;
    }

    @Test
    void test_createLosCsvFileFromGenericData() throws IOException {
        BigDecimal AVERAGE_LOS = new BigDecimal("2.1");
        BigDecimal LOS_PROBABILITY = new BigDecimal("0.1");
        LDBGenericPatternData data = new LDBGenericPatternData();
        data.setAverageLOS(AVERAGE_LOS);
        data.setDayOfWeek(Calendar.SUNDAY);
        data.setMarketSegmentId(MARKET_SEGMENT_ID_1);
        LDBGenericPatternData groupData = new LDBGenericPatternData();
        groupData.setAverageLOS(null);
        groupData.setDayOfWeek(Calendar.SUNDAY);
        groupData.setMarketSegmentId(MARKET_SEGMENT_ID_2);
        List<LDBGenericPatternData> genericData = Arrays.asList(data, groupData);
        LDBGenericLOSDistribution distribution = new LDBGenericLOSDistribution();
        distribution.setMinAverageLOS(AVERAGE_LOS.subtract(new BigDecimal("0.5")));
        distribution.setMaxAverageLOS(AVERAGE_LOS.add(new BigDecimal("0.5")));
        distribution.setLos1Probability(LOS_PROBABILITY);
        distribution.setLos2Probability(LOS_PROBABILITY);
        distribution.setLos3Probability(LOS_PROBABILITY);
        distribution.setLos4Probability(LOS_PROBABILITY);
        distribution.setLos5Probability(LOS_PROBABILITY);
        distribution.setLos6Probability(LOS_PROBABILITY);
        distribution.setLos7Probability(LOS_PROBABILITY);
        distribution.setLos8Probability(LOS_PROBABILITY);
        distribution.setLos14Probability(LOS_PROBABILITY);
        distribution.setLos21Probability(LOS_PROBABILITY);
        distribution.setLos28Probability(LOS_PROBABILITY);
        distribution.setLos35Probability(LOS_PROBABILITY);
        distribution.setLos63Probability(LOS_PROBABILITY);
        List<LDBGenericLOSDistribution> distributions = Arrays.asList(distribution);
        when(globalCrudService.<LDBGenericLOSDistribution>findByNamedQuery(Mockito.eq(LDBGenericLOSDistribution.GET_BY_AVERAGE_LOS), Mockito.anyMap())).thenReturn(distributions);
        when(tenantCrudService.findAll(LDBGenericPatternData.class)).thenReturn(genericData);
        String response = ldbPatternService.createLosCsvFileFromGenericData(ldbFolder);
        assertTrue(response.contains(LDBPatternService.LOS_DIST_FILE_NAME + " with 26 entries"));
        File file = new File(ldbFolder, LDBPatternService.LOS_DIST_FILE_NAME);
        assertTrue(file.exists());
        List<String> lines = readFile(file);
        assertEquals(27, lines.size());
        file.delete();
    }

    @Test
    void test_createLosCsvFileFromGenericData_avgLosGT90() throws IOException {
        BigDecimal AVERAGE_LOS = new BigDecimal("91.0");
        BigDecimal LOS_PROBABILITY = new BigDecimal("0.1");
        LDBGenericPatternData data = new LDBGenericPatternData();
        data.setAverageLOS(AVERAGE_LOS);
        data.setDayOfWeek(Calendar.SUNDAY);
        data.setMarketSegmentId(MARKET_SEGMENT_ID_1);
        List<LDBGenericPatternData> genericData = Arrays.asList(data);
        LDBGenericLOSDistribution distribution = new LDBGenericLOSDistribution();
        distribution.setMinAverageLOS(AVERAGE_LOS.subtract(new BigDecimal("0.5")));
        distribution.setMaxAverageLOS(AVERAGE_LOS.add(new BigDecimal("90.0")));
        distribution.setLos1Probability(LOS_PROBABILITY);
        distribution.setLos2Probability(LOS_PROBABILITY);
        distribution.setLos3Probability(LOS_PROBABILITY);
        distribution.setLos4Probability(LOS_PROBABILITY);
        distribution.setLos5Probability(LOS_PROBABILITY);
        distribution.setLos6Probability(LOS_PROBABILITY);
        distribution.setLos7Probability(LOS_PROBABILITY);
        distribution.setLos8Probability(LOS_PROBABILITY);
        distribution.setLos14Probability(LOS_PROBABILITY);
        distribution.setLos21Probability(LOS_PROBABILITY);
        distribution.setLos28Probability(LOS_PROBABILITY);
        distribution.setLos35Probability(LOS_PROBABILITY);
        distribution.setLos63Probability(LOS_PROBABILITY);
        List<LDBGenericLOSDistribution> distributions = Arrays.asList(distribution);
        when(globalCrudService.<LDBGenericLOSDistribution>findByNamedQuery(Mockito.eq(LDBGenericLOSDistribution.GET_BY_AVERAGE_LOS), Mockito.anyMap())).thenReturn(distributions);
        when(tenantCrudService.findAll(LDBGenericPatternData.class)).thenReturn(genericData);
        String response = ldbPatternService.createLosCsvFileFromGenericData(ldbFolder);
        assertTrue(response.contains(LDBPatternService.LOS_DIST_FILE_NAME + " with 13 entries"));
        File file = new File(ldbFolder, LDBPatternService.LOS_DIST_FILE_NAME);
        assertTrue(file.exists());
        List<String> lines = readFile(file);
        assertEquals(14, lines.size());
        file.delete();
        ArgumentCaptor<Map> parameterCaptor = ArgumentCaptor.forClass(Map.class);
        verify(globalCrudService).findByNamedQuery(Mockito.eq(LDBGenericLOSDistribution.GET_BY_AVERAGE_LOS), parameterCaptor.capture());
        Map parameters = parameterCaptor.getValue();
        assertEquals(1, parameters.size());
        BigDecimal averageLos = (BigDecimal) parameters.get("averageLOS");
        assertEquals(LDBPatternService.MAXIMUM_AVERAGE_LOS, averageLos);
    }

    @Test
    void test_createNoShowCsvFileFromGenericData() throws IOException {
        BigDecimal NO_SHOW_PROBABILITY = new BigDecimal("0.1");
        LDBGenericPatternData data = new LDBGenericPatternData();
        data.setNoShowProbability(NO_SHOW_PROBABILITY);
        data.setDayOfWeek(Calendar.SUNDAY);
        data.setMarketSegmentId(MARKET_SEGMENT_ID_1);
        LDBGenericPatternData groupData = new LDBGenericPatternData();
        groupData.setNoShowProbability(null);
        groupData.setDayOfWeek(Calendar.SUNDAY);
        groupData.setMarketSegmentId(MARKET_SEGMENT_ID_2);
        List<LDBGenericPatternData> genericData = Arrays.asList(data, groupData);
        when(tenantCrudService.findAll(LDBGenericPatternData.class)).thenReturn(genericData);
        String response = ldbPatternService.createNoShowCsvFileFromGenericData(ldbFolder);
        assertTrue(response.contains(LDBPatternService.NO_SHOW_FILE_NAME + " with 26 entries"));
        File file = new File(ldbFolder, LDBPatternService.NO_SHOW_FILE_NAME);
        assertTrue(file.exists());
        List<String> lines = readFile(file);
        assertEquals(27, lines.size());
        file.delete();
    }

    @Test
    void test_createBookingCurvePoints() {
        LDBGlobalGenericBookingCurve bookingCurve = new LDBGlobalGenericBookingCurve();
        bookingCurve.setId(BOOKING_CURVE_ID);
        String pointsString = "1.0, 0.9, 0.8";
        when(globalCrudService.find(LDBGlobalGenericBookingCurve.class, BOOKING_CURVE_ID)).thenReturn(bookingCurve);
        List<LDBGlobalGenericBookingCurvePoint> results = ldbPatternService.createBookingCurvePoints(pointsString, bookingCurve);
        assertEquals(3, results.size());
        LDBGlobalGenericBookingCurvePoint point = results.get(0);
        assertEquals(new BigDecimal("1.0"), point.getData());
        assertEquals(0, point.getDaysToArrival().intValue());
        point = results.get(1);
        assertEquals(new BigDecimal("0.9"), point.getData());
        assertEquals(1, point.getDaysToArrival().intValue());
        point = results.get(2);
        assertEquals(new BigDecimal("0.8"), point.getData());
        assertEquals(2, point.getDaysToArrival().intValue());
        assertEquals(bookingCurve, point.getBookingCurve());
    }

    @Test
    void test_createBookingCurvefor345Points() {
        LDBGlobalGenericBookingCurve bookingCurve = new LDBGlobalGenericBookingCurve();
        bookingCurve.setId(BOOKING_CURVE_ID);
        when(globalCrudService.find(LDBGlobalGenericBookingCurve.class, BOOKING_CURVE_ID)).thenReturn(bookingCurve);
        List<LDBGlobalGenericBookingCurvePoint> results = ldbPatternService.createBookingCurvePoints(TEST_BOOKING_CURVE_345_POINTS, bookingCurve);
        assertEquals(347, results.size());
        LDBGlobalGenericBookingCurvePoint point = results.get(0);
        assertEquals(new BigDecimal("1.00000"), point.getData());
        assertEquals(0, point.getDaysToArrival().intValue());
        point = results.get(1);
        assertEquals(new BigDecimal("0.90914"), point.getData());
        assertEquals(1, point.getDaysToArrival().intValue());
        point = results.get(2);
        assertEquals(new BigDecimal("0.83060"), point.getData());
        assertEquals(2, point.getDaysToArrival().intValue());
        assertEquals(bookingCurve, point.getBookingCurve());
    }

    @Test
    void test_createBookingCurveCsvFileFromGenericData() throws IOException {
        LDBGenericBookingCurve bookingCurve = new LDBGenericBookingCurve();
        List<LDBGenericBookingCurvePoint> points = new ArrayList<>();
        for (int daysToArrival = 0; daysToArrival <= 346; daysToArrival++) {
            LDBGenericBookingCurvePoint point = new LDBGenericBookingCurvePoint();
            point.setDaysToArrival(daysToArrival);
            point.setData(BigDecimal.ONE.subtract(new BigDecimal(daysToArrival).divide(new BigDecimal("1000"), 4, RoundingMode.HALF_EVEN)));
            points.add(point);
        }
        bookingCurve.setPoints(points);
        LDBGenericPatternData data1 = new LDBGenericPatternData();
        data1.setBookingCurve(bookingCurve);
        data1.setDayOfWeek(Calendar.SUNDAY);
        data1.setMarketSegmentId(MARKET_SEGMENT_ID_1);
        LDBGenericPatternData data2 = new LDBGenericPatternData();
        data2.setBookingCurve(bookingCurve);
        data2.setDayOfWeek(Calendar.MONDAY);
        data2.setMarketSegmentId(MARKET_SEGMENT_ID_2);
        List<LDBGenericPatternData> genericData = Arrays.asList(data1, data2);
        when(tenantCrudService.findAll(LDBGenericPatternData.class)).thenReturn(genericData);
        String response = ldbPatternService.createBookingCurveCsvFileFromGenericData(ldbFolder);
        // 2 market segments * 1 DOW * 13 LOS * 103 DTA
        int expectedRows = 2 * 1 * 13 * 103;
        assertTrue(response.startsWith("created file "));
        assertTrue(response.contains("NHS_BCRV.csv with 2678 entries"));
        assertTrue(response.contains("Validated BCRV for generic pattern"));
        File file = new File(ldbFolder, LDBPatternService.BOOKING_CURVE_FILE_NAME);
        assertTrue(file.exists());
        List<String> lines = readFile(file);
        assertEquals(expectedRows + 1, lines.size());
        file.delete();
    }

    private List<String> readFile(File file) throws IOException {
        Scanner sc = new Scanner(file);
        List<String> lines = new ArrayList<String>();
        while (sc.hasNextLine()) {
            lines.add(sc.nextLine());
        }
        return lines;
    }

    @Test
    void test_saveHotelProfiles() {
        List<LDBHotelProfile> profileList = new ArrayList<>();
        when(globalCrudService.save(profileList)).thenReturn(profileList);
        List<LDBHotelProfile> results = ldbPatternService.saveHotelProfiles(profileList);
        assertEquals(profileList, results);
    }

    @Test
    void test_saveHotelProfiles_deletedProfile() {
        Integer PROFILE_ID1 = 913;
        Integer PROFILE_ID2 = 1211;
        LDBHotelProfile existingProfile1 = new LDBHotelProfile();
        existingProfile1.setId(PROFILE_ID1);
        existingProfile1.setBookingCurves(new ArrayList<LDBGlobalGenericBookingCurve>());
        LDBHotelProfile existingProfile2 = new LDBHotelProfile();
        existingProfile2.setId(PROFILE_ID2);
        existingProfile2.setBookingCurves(new ArrayList<LDBGlobalGenericBookingCurve>());
        List<LDBHotelProfile> existingProfiles = Arrays.asList(existingProfile1, existingProfile2);
        when(globalCrudService.findAll(LDBHotelProfile.class)).thenReturn(existingProfiles);
        LDBHotelProfile newProfile = new LDBHotelProfile();
        newProfile.setId(PROFILE_ID1);
        newProfile.setBookingCurves(new ArrayList<LDBGlobalGenericBookingCurve>());
        List<LDBHotelProfile> profileList = Arrays.asList(newProfile);
        when(globalCrudService.save(profileList)).thenReturn(profileList);
        List<LDBHotelProfile> results = ldbPatternService.saveHotelProfiles(profileList);
        assertEquals(profileList, results);
        verify(globalCrudService).delete(Mockito.anyList());
    }

    @Test
    void test_saveHotelProfiles_labelChange() {
        Integer PROFILE_ID1 = 913;
        LDBGlobalGenericBookingCurve curve = new LDBGlobalGenericBookingCurve();
        curve.setBookingWindow(BookingWindow.LONG.toString());
        curve.setBusinessTypeId(Constants.BUSINESS_TYPE_GROUP);
        List<LDBGlobalGenericBookingCurve> curves = Arrays.asList(curve);
        LDBHotelProfile existingProfile1 = new LDBHotelProfile();
        existingProfile1.setId(PROFILE_ID1);
        existingProfile1.setLabel(LABEL);
        existingProfile1.setBookingCurves(curves);
        when(globalCrudService.find(LDBHotelProfile.class, PROFILE_ID1)).thenReturn(existingProfile1);
        LDBHotelProfile newProfile = new LDBHotelProfile();
        newProfile.setId(PROFILE_ID1);
        newProfile.setLabel(LABEL + "changed");
        newProfile.setBookingCurves(curves);
        List<LDBHotelProfile> profileList = Arrays.asList(newProfile);
        when(globalCrudService.save(profileList)).thenReturn(profileList);
        List<LDBHotelProfile> results = ldbPatternService.saveHotelProfiles(profileList);
        assertEquals(profileList, results);
        verify(globalCrudService, times(2)).save(Mockito.anyList());
    }

    @Test
    void test_createGlobalGenericBookingCurve() {
        String PROFILE_LABEL = "Airport";
        BookingWindow BOOKING_WINDOW = BookingWindow.SHORT;
        Constants.BUSINESS_TYPE BUSINESS_TYPE = Constants.BUSINESS_TYPE.TRANSIENT;
        String POINTS = "1, 0.9, 0.8";
        LDBHotelProfile profile = new LDBHotelProfile();
        profile.setLabel(PROFILE_LABEL);
        LDBGlobalGenericBookingCurve savedEntity = new LDBGlobalGenericBookingCurve();
        String labelBase = PROFILE_LABEL + "_Transient_Short_";
        when(globalCrudService.save(Mockito.any(LDBGlobalGenericBookingCurve.class))).thenReturn(savedEntity);
        List<LDBGlobalGenericBookingCurve> existingCurves = new ArrayList<LDBGlobalGenericBookingCurve>();
        LDBGlobalGenericBookingCurve existingCurve = new LDBGlobalGenericBookingCurve();
        existingCurve.setLabel(labelBase + "01");
        existingCurves.add(existingCurve);
        when(globalCrudService.<LDBGlobalGenericBookingCurve>findByNamedQuery(LDBGlobalGenericBookingCurve.GET_BY_PARTIAL_LABEL, QueryParameter.with("label", labelBase + "%").parameters())).thenReturn(existingCurves);
        LDBGlobalGenericBookingCurve results = ldbPatternService.createGlobalGenericBookingCurve(profile, POINTS, BUSINESS_TYPE, BOOKING_WINDOW);
        assertEquals(savedEntity, results);
        ArgumentCaptor<LDBGlobalGenericBookingCurve> saveCaptor = ArgumentCaptor.forClass(LDBGlobalGenericBookingCurve.class);
        verify(globalCrudService).save(saveCaptor.capture());
        LDBGlobalGenericBookingCurve entity = saveCaptor.getValue();
        assertEquals(Constants.BUSINESS_TYPE_TRANSIENT, entity.getBusinessTypeId());
        assertEquals(BOOKING_WINDOW.toString(), entity.getBookingWindow());
        assertEquals(labelBase + "02", entity.getLabel());
        LDBHotelProfile hotelProfile = entity.getHotelProfile();
        assertEquals(profile, hotelProfile);
        List<LDBGlobalGenericBookingCurvePoint> points = entity.getPoints();
        assertEquals(3, points.size());
        assertEquals(0, points.get(0).getDaysToArrival().intValue());
        assertEquals(new BigDecimal("1"), points.get(0).getData());
        assertEquals(1, points.get(1).getDaysToArrival().intValue());
        assertEquals(new BigDecimal("0.9"), points.get(1).getData());
        assertEquals(2, points.get(2).getDaysToArrival().intValue());
        assertEquals(new BigDecimal("0.8"), points.get(2).getData());
    }

    @Test
    void test_getGenericPatternData_noExisting() {
        List<LDBGenericPatternData> entities = new ArrayList<LDBGenericPatternData>();
        when(multiPropertyCrudService.findByNamedQueryForSingleProperty(PROPERTY_ID, LDBGenericPatternData.BY_DAY_OF_WEEK, QueryParameter.with("dayOfWeek", Calendar.SUNDAY).parameters())).thenReturn(entities);
        when(projectionDataService.getMarketSegments()).thenReturn(Arrays.asList(HILTON_LDB_MARKET_SEGMENTS));
        List<GenericPatternData> results = ldbPatternService.getGenericPatternData();
        assertEquals(HILTON_LDB_MARKET_SEGMENTS.length, results.size());
        for (GenericPatternData dto : results) {
            assertNotNull(dto.getMarketSegment());
            assertNull(dto.getAverageLOS());
            assertNull(dto.getNoShowProbability());
            assertNull(dto.getBookingCurve());
        }
    }

    @Test
    void test_getGenericPatternData_existingData() {
        List<LDBGenericPatternData> entities = new ArrayList<LDBGenericPatternData>();
        LDBGenericPatternData entity = new LDBGenericPatternData();
        entity.setMarketSegmentId(MARKET_SEGMENT_ID_1);
        entity.setNoShowProbability(NO_SHOW_PROBABLITY);
        entity.setAverageLOS(AVERAGE_LOS);
        entity.setBookingCurve(BOOKING_CURVE);
        entities.add(entity);
        when(multiPropertyCrudService.findByNamedQueryForSingleProperty(PROPERTY_ID, LDBGenericPatternData.BY_DAY_OF_WEEK, QueryParameter.with("dayOfWeek", Calendar.SUNDAY).parameters())).thenReturn(entities);
        when(projectionDataService.getMarketSegments()).thenReturn(Arrays.asList(HILTON_LDB_MARKET_SEGMENTS));
        MktSeg marketSegment = createMarketSegmentEntityBy(MARKET_SEGMENT_CODE);
        when(multiPropertyCrudService.find(Mockito.eq(PROPERTY_ID), Mockito.eq(MktSeg.class), Mockito.eq(new Integer(MARKET_SEGMENT_ID_1)))).thenReturn(marketSegment);
        MktSegDetails mktSegDetail = new MktSegDetails();
        BusinessType transientBusinessType = createBusinessTypeWith("Transient", 2);
        mktSegDetail.setBusinessType(transientBusinessType);
        when(tenantCrudService.findByNamedQuerySingleResult(MktSegDetails.GET_MARKET_SEGMENTS_BY_NAME, QueryParameter.with("mktSegName", marketSegment.getCode()).parameters())).thenReturn(mktSegDetail);
        List<GenericPatternData> results = ldbPatternService.getGenericPatternData();
        assertEquals(HILTON_LDB_MARKET_SEGMENTS.length, results.size());
        for (GenericPatternData dto : results) {
            assertNotNull(dto.getMarketSegment());
            if (MARKET_SEGMENT_CODE.equals(dto.getMarketSegment())) {
                assertEquals(AVERAGE_LOS, dto.getAverageLOS());
                assertEquals(NO_SHOW_PROBABLITY, dto.getNoShowProbability());
                assertEquals(BOOKING_CURVE, dto.getBookingCurve());
                assertEquals(false, dto.isGroupMarketSegment());
            } else {
                assertNull(dto.getAverageLOS());
                assertNull(dto.getNoShowProbability());
            }
        }
    }

    @Test
    void test_saveGlobalBookingCurves() {
        LDBHotelProfile profile = new LDBHotelProfile();
        profile.setLabel(LABEL);
        LDBGlobalGenericBookingCurve curve = new LDBGlobalGenericBookingCurve();
        curve.setBusinessTypeId(Constants.BUSINESS_TYPE_GROUP);
        curve.setBookingWindow(BookingWindow.LONG.toString());
        curve.setHotelProfile(profile);
        List<LDBGlobalGenericBookingCurve> curves = Arrays.asList(curve);
        when(globalCrudService.save(curves)).thenReturn(curves);
        List<LDBGlobalGenericBookingCurve> results = ldbPatternService.saveGlobalBookingCurves(curves);
        assertEquals(curves, results);
        verify(globalCrudService).save(curves);
    }

    @Test
    void test_saveGlobalBookingCurves_labelChange() {
        LDBHotelProfile profile = new LDBHotelProfile();
        profile.setLabel(LABEL);
        LDBGlobalGenericBookingCurve existingCurve = new LDBGlobalGenericBookingCurve();
        existingCurve.setBusinessTypeId(Constants.BUSINESS_TYPE_TRANSIENT);
        existingCurve.setId(BOOKING_CURVE_ID);
        existingCurve.setBookingWindow(BookingWindow.LONG.toString());
        existingCurve.setHotelProfile(profile);
        when(globalCrudService.find(LDBGlobalGenericBookingCurve.class, BOOKING_CURVE_ID)).thenReturn(existingCurve);
        LDBGlobalGenericBookingCurve newCurve = new LDBGlobalGenericBookingCurve();
        newCurve.setBusinessTypeId(Constants.BUSINESS_TYPE_GROUP);
        newCurve.setBookingWindow(BookingWindow.LONG.toString());
        newCurve.setHotelProfile(profile);
        newCurve.setId(BOOKING_CURVE_ID);
        List<LDBGlobalGenericBookingCurve> curves = Arrays.asList(newCurve);
        when(globalCrudService.save(curves)).thenReturn(curves);
        List<LDBGlobalGenericBookingCurve> results = ldbPatternService.saveGlobalBookingCurves(curves);
        assertEquals(curves, results);
        verify(globalCrudService).save(curves);
        assertTrue(curves.get(0).getLabel().contains("Group"));
    }

    @Test
    void test_saveGlobalBookingCurves_duplicateLabels() {
        LDBHotelProfile profile = new LDBHotelProfile();
        profile.setLabel(LABEL);
        LDBGlobalGenericBookingCurve newCurve1 = new LDBGlobalGenericBookingCurve();
        newCurve1.setBusinessTypeId(Constants.BUSINESS_TYPE_GROUP);
        newCurve1.setBookingWindow(BookingWindow.LONG.toString());
        newCurve1.setHotelProfile(profile);
        LDBGlobalGenericBookingCurve newCurve2 = new LDBGlobalGenericBookingCurve();
        newCurve2.setBusinessTypeId(Constants.BUSINESS_TYPE_GROUP);
        newCurve2.setBookingWindow(BookingWindow.LONG.toString());
        newCurve2.setHotelProfile(profile);
        List<LDBGlobalGenericBookingCurve> curves = Arrays.asList(newCurve1, newCurve2);
        when(globalCrudService.save(curves)).thenReturn(curves);
        List<LDBGlobalGenericBookingCurve> results = ldbPatternService.saveGlobalBookingCurves(curves);
        assertEquals(curves, results);
        verify(globalCrudService).save(curves);
        assertEquals(LABEL + "_Group_Long_01", curves.get(0).getLabel());
        assertEquals(LABEL + "_Group_Long_02", curves.get(1).getLabel());
    }

    @Test
    void test_saveGenericPatternData() {
        // GIVEN
        MktSeg marketSegment = new MktSeg();
        marketSegment.setCode(MARKET_SEGMENT_CODE);
        marketSegment.setId(MARKET_SEGMENT_ID_1);
        when(multiPropertyCrudService.findByNamedQuerySingleResultForSingleProperty(Mockito.eq(PROPERTY_ID), Mockito.eq(MktSeg.BY_CODE), Mockito.anyMap())).thenReturn(marketSegment);
        List<GenericPatternData> input = new ArrayList<GenericPatternData>();
        GenericPatternData dto = getGenericPatternData(MARKET_SEGMENT_CODE);
        input.add(dto);
        when(tenantCrudService.save(any(LDBGenericBookingCurve.class))).thenReturn(BOOKING_CURVE);
        LDBGlobalGenericBookingCurve bookingCurveFromGlobal = new LDBGlobalGenericBookingCurve();
        bookingCurveFromGlobal.setLabel("Airport_Transient_Long_01");
        List<LDBGlobalGenericBookingCurvePoint> points = new ArrayList<>();
        points.add(createLDBGlobalGenericBookingCurvePoint(1, new BigDecimal("95.00")));
        points.add(createLDBGlobalGenericBookingCurvePoint(2, new BigDecimal("90.00")));
        bookingCurveFromGlobal.setPoints(points);
        when(globalCrudService.findByNamedQuerySingleResult(LDBGlobalGenericBookingCurve.GET_BY_LABEL, QueryParameter.with("label", "Airport_Transient_Long_01").parameters())).thenReturn(bookingCurveFromGlobal);
        // WHEN
        ldbPatternService.saveGenericPatternData(input);
        // THEN
        verify(tenantCrudService).deleteAll(LDBGenericPatternData.class);
        ArgumentCaptor<Collection> saveCaptor = ArgumentCaptor.forClass(Collection.class);
        verify(tenantCrudService, times(2)).save(saveCaptor.capture());
        List<Collection> allValues = saveCaptor.getAllValues();
        List<LDBGenericPatternData> entities = (List<LDBGenericPatternData>) allValues.get(1);
        List<LDBGenericBookingCurvePoint> bookingCurvePoints = (List<LDBGenericBookingCurvePoint>) allValues.get(0);
        assertEquals(7, entities.size());
        LDBGenericPatternData entity = entities.get(0);
        assertEquals(AVERAGE_LOS, entity.getAverageLOS());
        assertEquals(NO_SHOW_PROBABLITY, entity.getNoShowProbability());
        assertEquals(MARKET_SEGMENT_ID_1, entity.getMarketSegmentId());
        assertEquals(BOOKING_CURVE, entity.getBookingCurve());
        assertEquals("Airport_Transient_Long_01", entity.getBookingCurve().getLabel());
        assertEquals(1, entity.getBookingCurve().getId().intValue());
        assertEquals(1, bookingCurvePoints.get(0).getDaysToArrival().intValue());
        assertEquals(new BigDecimal("95.00"), bookingCurvePoints.get(0).getData());
        assertEquals(2, bookingCurvePoints.get(1).getDaysToArrival().intValue());
        assertEquals(new BigDecimal("90.00"), bookingCurvePoints.get(1).getData());
        assertEquals(BOOKING_CURVE, bookingCurvePoints.get(0).getBookingCurve());
        assertEquals(BOOKING_CURVE, bookingCurvePoints.get(1).getBookingCurve());
    }

    private LDBGlobalGenericBookingCurvePoint createLDBGlobalGenericBookingCurvePoint(int daysToArrival, BigDecimal data) {
        LDBGlobalGenericBookingCurvePoint point = new LDBGlobalGenericBookingCurvePoint();
        point.setDaysToArrival(daysToArrival);
        point.setData(data);
        return point;
    }

    @Test
    void test_saveGenericPatternData_WhenBookingCurveExistInTenant() {
        // GIVEN
        MktSeg marketSegment = new MktSeg();
        marketSegment.setCode(MARKET_SEGMENT_CODE);
        marketSegment.setId(MARKET_SEGMENT_ID_1);
        when(multiPropertyCrudService.findByNamedQuerySingleResultForSingleProperty(Mockito.eq(PROPERTY_ID), Mockito.eq(MktSeg.BY_CODE), Mockito.anyMap())).thenReturn(marketSegment);
        List<GenericPatternData> input = new ArrayList<GenericPatternData>();
        GenericPatternData dto = getGenericPatternData(MARKET_SEGMENT_CODE);
        input.add(dto);
        BOOKING_CURVE.setId(2);
        when(tenantCrudService.findByNamedQuerySingleResult(LDBGenericBookingCurve.GET_BY_LABEL, QueryParameter.with("label", "Airport_Transient_Long_01").parameters())).thenReturn(BOOKING_CURVE);
        // WHEN
        ldbPatternService.saveGenericPatternData(input);
        // THEN
        verify(tenantCrudService).deleteAll(LDBGenericPatternData.class);
        verify(tenantCrudService, never()).save(any(LDBGenericBookingCurve.class));
        ArgumentCaptor<Collection> saveCaptor = ArgumentCaptor.forClass(Collection.class);
        verify(tenantCrudService).save(saveCaptor.capture());
        List<LDBGenericPatternData> entities = (List<LDBGenericPatternData>) saveCaptor.getValue();
        assertEquals(7, entities.size());
        LDBGenericPatternData entity = entities.get(0);
        assertEquals(AVERAGE_LOS, entity.getAverageLOS());
        assertEquals(NO_SHOW_PROBABLITY, entity.getNoShowProbability());
        assertEquals(MARKET_SEGMENT_ID_1, entity.getMarketSegmentId());
        assertEquals(BOOKING_CURVE, entity.getBookingCurve());
        assertEquals("Airport_Transient_Long_01", entity.getBookingCurve().getLabel());
        assertEquals(2, entity.getBookingCurve().getId().intValue());
    }

    @Test
    void test_saveGenericPatternData_newMarketSegment() {
        MktSeg marketSegment = new MktSeg();
        marketSegment.setCode(MARKET_SEGMENT_CODE);
        marketSegment.setId(MARKET_SEGMENT_ID_1);
        when(multiPropertyCrudService.findByNamedQuerySingleResultForSingleProperty(Mockito.eq(PROPERTY_ID), Mockito.eq(MktSeg.BY_CODE), Mockito.anyMap())).thenReturn(marketSegment);
        MktSeg marketSegment1 = new MktSeg();
        marketSegment1.setId(MARKET_SEGMENT_ID_2);
        when(multiPropertyCrudService.findByNamedQuerySingleResultForSingleProperty(PacmanWorkContextHelper.getPropertyId(), MktSeg.BY_CODE, QueryParameter.with("code", NEWMKTSEG).parameters())).thenReturn(null, marketSegment1);
        List<GenericPatternData> input = new ArrayList<GenericPatternData>();
        GenericPatternData dto = getGenericPatternData(MARKET_SEGMENT_CODE);
        input.add(dto);
        GenericPatternData genericPatternData = getGenericPatternData(NEWMKTSEG);
        input.add(genericPatternData);
        when(tenantCrudService.findByNamedQuerySingleResult(LDBGenericBookingCurve.GET_BY_LABEL, QueryParameter.with("label", genericPatternData.getBookingCurve().getLabel()).parameters())).thenReturn(BOOKING_CURVE);
        ldbPatternService.saveGenericPatternData(input);
        verify(tenantCrudService).deleteAll(LDBGenericPatternData.class);
        Map<String, String> missingMarketSegments = new HashMap<String, String>();
        missingMarketSegments.put(NEWMKTSEG, NEWMKTSEG);
        verify(marketSegmentComponent).saveMarketSegment(missingMarketSegments);
        ArgumentCaptor<Collection> saveCaptor = ArgumentCaptor.forClass(Collection.class);
        verify(tenantCrudService).save(saveCaptor.capture());
        List<LDBGenericPatternData> entities = (List<LDBGenericPatternData>) saveCaptor.getValue();
        assertEquals(14, entities.size());
        LDBGenericPatternData entity = entities.get(0);
        assertEquals(AVERAGE_LOS, entity.getAverageLOS());
        assertEquals(NO_SHOW_PROBABLITY, entity.getNoShowProbability());
        assertEquals(MARKET_SEGMENT_ID_1, entity.getMarketSegmentId());
    }

    @Test
    void shouldIgnoreSavingNoShowAndAvgLOSForGroupMarketSegment() {
        MktSeg marketSegment = new MktSeg();
        marketSegment.setCode(MARKET_SEGMENT_CODE);
        marketSegment.setId(MARKET_SEGMENT_ID_1);
        when(multiPropertyCrudService.findByNamedQuerySingleResultForSingleProperty(Mockito.eq(PROPERTY_ID), Mockito.eq(MktSeg.BY_CODE), Mockito.anyMap())).thenReturn(marketSegment);
        when(projectionDataService.isGroupMarketSegment(MARKET_SEGMENT_CODE)).thenReturn(true);
        List<GenericPatternData> input = new ArrayList<GenericPatternData>();
        GenericPatternData dto = getGenericPatternData(MARKET_SEGMENT_CODE);
        input.add(dto);
        when(tenantCrudService.findByNamedQuerySingleResult(LDBGenericBookingCurve.GET_BY_LABEL, QueryParameter.with("label", dto.getBookingCurve().getLabel()).parameters())).thenReturn(BOOKING_CURVE);
        ldbPatternService.saveGenericPatternData(input);
        verify(tenantCrudService).deleteAll(LDBGenericPatternData.class);
        ArgumentCaptor<Collection> saveCaptor = ArgumentCaptor.forClass(Collection.class);
        verify(tenantCrudService).save(saveCaptor.capture());
        List<LDBGenericPatternData> entities = (List<LDBGenericPatternData>) saveCaptor.getValue();
        assertEquals(7, entities.size());
        LDBGenericPatternData entity = entities.get(0);
        assertNull(entity.getAverageLOS());
        assertNull(entity.getNoShowProbability());
        assertEquals(MARKET_SEGMENT_ID_1, entity.getMarketSegmentId());
    }

    private GenericPatternData getGenericPatternData(String marketSegmentCode) {
        GenericPatternData dto = new GenericPatternData();
        dto.setNoShowProbability(NO_SHOW_PROBABLITY);
        dto.setAverageLOS(AVERAGE_LOS);
        dto.setMarketSegment(marketSegmentCode);
        BOOKING_CURVE.setId(1);
        BOOKING_CURVE.setLabel("Airport_Transient_Long_01");
        dto.setBookingCurve(BOOKING_CURVE);
        return dto;
    }

    @Test
    void test_getBookingCurves() {
        LDBHotelProfile HOTEL_PROFILE = new LDBHotelProfile();
        Constants.BUSINESS_TYPE BUSINESS_TYPE = Constants.BUSINESS_TYPE.TRANSIENT;
        List<BookingWindow> BOOKING_WINDOWS = Arrays.asList(BookingWindow.LONG, BookingWindow.SHORT);
        List<LDBGlobalGenericBookingCurve> entities = new ArrayList<>();
        LDBGlobalGenericBookingCurve entity = new LDBGlobalGenericBookingCurve();
        LDBGlobalGenericBookingCurvePoint point = Mockito.mock(LDBGlobalGenericBookingCurvePoint.class);
        entity.setPoints(Arrays.asList(point));
        entities.add(entity);
        when(globalCrudService.<LDBGlobalGenericBookingCurve>findByNamedQuery(Mockito.eq(LDBGlobalGenericBookingCurve.GET_BY_PROFILE_TYPE_AND_WINDOWS), Mockito.anyMap())).thenReturn(entities);
        List<LDBGlobalGenericBookingCurve> results = ldbPatternService.getBookingCurves(HOTEL_PROFILE, BUSINESS_TYPE, BOOKING_WINDOWS);
        assertEquals(entities, results);
        ArgumentCaptor<Map> parameterCaptor = ArgumentCaptor.forClass(Map.class);
        verify(globalCrudService).findByNamedQuery(Mockito.eq(LDBGlobalGenericBookingCurve.GET_BY_PROFILE_TYPE_AND_WINDOWS), parameterCaptor.capture());
        Map parameters = parameterCaptor.getValue();
        assertEquals(HOTEL_PROFILE, parameters.get("profile"));
        assertEquals(Constants.BUSINESS_TYPE_TRANSIENT, parameters.get("businessTypeId"));
        List<String> bookingWindows = (List<String>) parameters.get("bookingWindows");
        assertEquals(2, bookingWindows.size());
        assertTrue(bookingWindows.contains(BookingWindow.LONG.toString()));
        assertTrue(bookingWindows.contains(BookingWindow.SHORT.toString()));
        // confirm that curve is hydrated
        verify(point).getData();
    }

    @Test
    void shouldNotGetAnyBookingCurveWhenBookingWindowsAreNotGiven() {
        LDBHotelProfile HOTEL_PROFILE = new LDBHotelProfile();
        Constants.BUSINESS_TYPE BUSINESS_TYPE = Constants.BUSINESS_TYPE.TRANSIENT;
        List<BookingWindow> BOOKING_WINDOWS = new ArrayList<>();
        List<LDBGlobalGenericBookingCurve> results = ldbPatternService.getBookingCurves(HOTEL_PROFILE, BUSINESS_TYPE, BOOKING_WINDOWS);
        assertTrue(results.isEmpty());
        verify(globalCrudService, never()).findByNamedQuery(Mockito.eq(LDBGlobalGenericBookingCurve.GET_BY_PROFILE_TYPE_AND_WINDOWS), Mockito.anyMap());
    }

    @Test
    void test_getHotelProfiles() {
        List<LDBHotelProfile> profiles = new ArrayList<LDBHotelProfile>();
        when(globalCrudService.findAll(LDBHotelProfile.class)).thenReturn(profiles);
        List<LDBHotelProfile> results = ldbPatternService.getHotelProfiles();
        assertEquals(profiles, results);
    }

    @Test
    void test_addAverageLOS() {
        Property sourceProperty = new Property();
        sourceProperty.setId(PROPERTY_ID);
        List<String> marketSegmentCodes = new ArrayList<String>();
        marketSegmentCodes.add("BAR");
        marketSegmentCodes.add("Tier1");
        MktSeg marketSegment = new MktSeg();
        marketSegment.setId(MARKET_SEGMENT_ID_1);
        when(multiPropertyCrudService.findByNamedQuerySingleResultForSingleProperty(Mockito.eq(PROPERTY_ID), Mockito.eq(MktSeg.BY_CODE), Mockito.anyMap())).thenReturn(marketSegment);
        List<IndividualTransactions> transactions = new ArrayList<IndividualTransactions>();
        IndividualTransactions transaction = new IndividualTransactions();
        transaction.setDepartureDate(LocalDate.now().toDate());
        transaction.setArrivalDate(LocalDate.now().minusDays(2).toDate());
        transactions.add(transaction);
        when(multiPropertyCrudService.findByNamedQueryForSingleProperty(Mockito.eq(PROPERTY_ID), Mockito.eq(IndividualTransactions.BY_OCCUPANCY_DATERANGE_AND_MARKET_SEGMENT), Mockito.anyMap())).thenReturn(transactions);
        List<String> results = ldbPatternService.addAverageLOS(sourceProperty, marketSegmentCodes);
        assertEquals(2, results.size());
        assertEquals("BAR", results.get(0));
        assertEquals("Tier1 (Avg. LOS: 2)", results.get(1));
    }

    @SuppressWarnings("unchecked")
    @Test
    void test_isClonePatternSourceConfigurationComplete_true() throws IOException {
        Integer MARKET_SEGMENT_ID = 1211;
        Integer SOURCE_PROPERTY_ID = 913;
        String SOURCE_MARKET_SEGEMENT_IDS = "526";
        MktSeg marketSegment = new MktSeg();
        marketSegment.setCode(MARKET_SEGMENT_CODE);
        Property sourceProperty = new Property();
        List<LDBClonePatternSource> entities = new ArrayList<LDBClonePatternSource>();
        LDBClonePatternSource entity = new LDBClonePatternSource();
        entity.setMarketSegmentId(MARKET_SEGMENT_ID);
        entity.setSourcePropertyId(SOURCE_PROPERTY_ID);
        entity.setSourceMarketSegmentIds(SOURCE_MARKET_SEGEMENT_IDS);
        entities.add(entity);
        when(multiPropertyCrudService.find(Mockito.eq(SOURCE_PROPERTY_ID), Mockito.eq(MktSeg.class), Mockito.eq(new Integer(SOURCE_MARKET_SEGEMENT_IDS)))).thenReturn(marketSegment);
        when(multiPropertyCrudService.findByNamedQueryForSingleProperty(Mockito.eq(PROPERTY_ID), Mockito.eq(LDBClonePatternSource.GET_ALL), Mockito.anyMap())).thenReturn(entities);
        List<String> marketSegments = new ArrayList<String>();
        marketSegments.add(MARKET_SEGMENT_CODE);
        when(multiPropertyCrudService.find(PROPERTY_ID, MktSeg.class, MARKET_SEGMENT_ID)).thenReturn(marketSegment);
        when(propertyService.getPropertyById(SOURCE_PROPERTY_ID)).thenReturn(sourceProperty);
        when(projectionDataService.getMarketSegments()).thenReturn(marketSegments);
        assertTrue(ldbPatternService.isClonePatternSourceConfigurationComplete());
    }

    @Test
    void test_isClonePatternSourceConfigurationComplete_false() throws IOException {
        Integer MARKET_SEGMENT_ID = 1211;
        Integer SOURCE_PROPERTY_ID = 913;
        String SOURCE_MARKET_SEGEMENT_IDS = "526";
        MktSeg marketSegment = new MktSeg();
        marketSegment.setCode(MARKET_SEGMENT_CODE);
        Property sourceProperty = new Property();
        List<LDBClonePatternSource> entities = new ArrayList<LDBClonePatternSource>();
        when(multiPropertyCrudService.find(Mockito.eq(SOURCE_PROPERTY_ID), Mockito.eq(MktSeg.class), Mockito.eq(new Integer(SOURCE_MARKET_SEGEMENT_IDS)))).thenReturn(marketSegment);
        when(tenantCrudService.findAll(LDBClonePatternSource.class)).thenReturn(entities);
        List<String> marketSegments = new ArrayList<String>();
        marketSegments.add(MARKET_SEGMENT_CODE);
        when(tenantCrudService.find(MktSeg.class, MARKET_SEGMENT_ID)).thenReturn(marketSegment);
        when(propertyService.getPropertyById(SOURCE_PROPERTY_ID)).thenReturn(sourceProperty);
        when(projectionDataService.getMarketSegments()).thenReturn(marketSegments);
        assertFalse(ldbPatternService.isClonePatternSourceConfigurationComplete());
    }

    @Test
    void test_isEligibleForSelfCloning_hotelNotOpen() throws IOException {
        Mockito.when(tenantCrudService.findByNamedQuerySingleResult(TotalActivity.GET_HOTEL_OPENING_DATE)).thenReturn(null);
        assertFalse(ldbPatternService.isSufficientPaceAvailablePostHotelOpening());
        Mockito.verify(tenantCrudService).findByNamedQuerySingleResult(TotalActivity.GET_HOTEL_OPENING_DATE);
    }

    @Test
    void test_isEligibleForSelfCloning_insufficientPace() throws IOException {
        Date HOTEL_OPENING_DATE = new Date();
        Mockito.when(tenantCrudService.findByNamedQuerySingleResult(TotalActivity.GET_HOTEL_OPENING_DATE)).thenReturn(HOTEL_OPENING_DATE);
        Mockito.when(tenantCrudService.findByNamedQuerySingleResult(FileMetadata.COUNT_BDE_SINCE_DATE, QueryParameter.with("snapshotDate", HOTEL_OPENING_DATE).parameters())).thenReturn(LDBPatternService.PACE_DAYS_REQUIRED_FOR_SELF_CLONING - 1);
        assertFalse(ldbPatternService.isSufficientPaceAvailablePostHotelOpening());
        Mockito.verify(tenantCrudService).findByNamedQuerySingleResult(TotalActivity.GET_HOTEL_OPENING_DATE);
        Mockito.verify(tenantCrudService).findByNamedQuerySingleResult(FileMetadata.COUNT_BDE_SINCE_DATE, QueryParameter.with("snapshotDate", HOTEL_OPENING_DATE).parameters());
    }

    @Test
    void test_isEligibleForSelfCloning_sufficientPace() throws IOException {
        Date HOTEL_OPENING_DATE = new Date();
        Mockito.when(tenantCrudService.findByNamedQuerySingleResult(TotalActivity.GET_HOTEL_OPENING_DATE)).thenReturn(HOTEL_OPENING_DATE);
        Mockito.when(tenantCrudService.findByNamedQuerySingleResult(FileMetadata.COUNT_BDE_SINCE_DATE, QueryParameter.with("snapshotDate", HOTEL_OPENING_DATE).parameters())).thenReturn(LDBPatternService.PACE_DAYS_REQUIRED_FOR_SELF_CLONING + 1);
        assertTrue(ldbPatternService.isSufficientPaceAvailablePostHotelOpening());
        Mockito.verify(tenantCrudService).findByNamedQuerySingleResult(TotalActivity.GET_HOTEL_OPENING_DATE);
        Mockito.verify(tenantCrudService).findByNamedQuerySingleResult(FileMetadata.COUNT_BDE_SINCE_DATE, QueryParameter.with("snapshotDate", HOTEL_OPENING_DATE).parameters());
    }

    @Test
    void test_getSelfClonePatternSources() throws IOException {
        List<MktSeg> marketSegments = new ArrayList<MktSeg>();
        MktSeg ms1 = new MktSeg();
        ms1.setId(MARKET_SEGMENT_ID_1);
        ms1.setPropertyId(PROPERTY_ID);
        marketSegments.add(ms1);
        MktSeg ms2 = new MktSeg();
        ms2.setId(MARKET_SEGMENT_ID_2);
        ms2.setPropertyId(PROPERTY_ID);
        marketSegments.add(ms2);
        Mockito.when(tenantCrudService.<MktSeg>findByNamedQuery(Mockito.eq(MktSeg.BY_PROPERTY_ID), Mockito.anyMap())).thenReturn(marketSegments);
        List<LDBClonePatternSource> results = ldbPatternService.getSelfClonePatternSources();
        assertEquals(2, results.size());
        LDBClonePatternSource source = results.get(0);
        assertEquals(PROPERTY_ID, source.getSourcePropertyId().intValue());
        assertEquals(MARKET_SEGMENT_ID_1, source.getMarketSegmentId());
        assertEquals(MARKET_SEGMENT_ID_1.toString(), source.getSourceMarketSegmentIds());
        source = results.get(1);
        assertEquals(PROPERTY_ID, source.getSourcePropertyId().intValue());
        assertEquals(MARKET_SEGMENT_ID_2, source.getMarketSegmentId());
        assertEquals(MARKET_SEGMENT_ID_2.toString(), source.getSourceMarketSegmentIds());
    }

    @Test
    void test_concatenatePatternCsvFiles() throws IOException {
        File directory = new File(ldbFolder, "test" + String.valueOf(LocalDateTime.now().getMillisOfDay()));
        directory.mkdirs();
        createMktSegPatternFiles(directory, "nhs_los_dist_*.csv");
        createMktSegPatternFiles(directory, "nhs_noshow_*.csv");
        createMktSegBCRVPatternFiles(directory, "nhs_bcrv_*.csv");
        String response = ldbPatternService.concatenatePatternCsvFiles(directory);
        assertTrue(response.contains("concatenated 2 files to produce NHS_LOS_DIST.csv"));
        assertTrue(response.contains("concatenated 2 files to produce NHS_NOSHOW.csv"));
        assertTrue(response.contains("concatenated 2 files to produce NHS_BCRV.csv"));
        assertTrue((new File(directory, "NHS_LOS_DIST.csv")).exists());
        assertTrue((new File(directory, "NHS_NOSHOW.csv")).exists());
        assertTrue((new File(directory, "NHS_BCRV.csv")).exists());
        String contents = FileUtils.readFileToString(new File(directory, "NHS_LOS_DIST.csv"));
        assertEquals("header,header,header" + System.lineSeparator() + "data,data,data" + System.lineSeparator() + "data,data,data" + System.lineSeparator() + "", contents);
        contents = FileUtils.readFileToString(new File(directory, "NHS_NOSHOW.csv"));
        assertEquals("header,header,header" + System.lineSeparator() + "data,data,data" + System.lineSeparator() + "data,data,data" + System.lineSeparator() + "", contents);
        contents = FileUtils.readFileToString(new File(directory, "NHS_BCRV.csv"));
        assertEquals("MKT_SEG_GRP_ID,DOW,LOS,DTA,BCRV" + System.lineSeparator() + "1.0,1.0,1.0,1.0,1.0" + System.lineSeparator() + "1.0,1.0,1.0,1.0,1.0" + System.lineSeparator() + "", contents);
        FileUtils.cleanDirectory(directory);
        FileUtils.forceDelete(directory);
    }

    private void createMktSegBCRVPatternFiles(File directory, String fileNamePattern) throws IOException {
        for (int marketSegment = 1; marketSegment < 3; marketSegment++) {
            String fileName = fileNamePattern.replace("*", String.valueOf(marketSegment));
            FileUtils.writeStringToFile(new File(directory, fileName), "MKT_SEG_GRP_ID,DOW,LOS,DTA,BCRV" + System.lineSeparator() + "1.0,1.0,1.0,1.0,1.0" + System.lineSeparator() + "");
        }
    }

    private void createMktSegPatternFiles(File directory, String fileNamePattern) throws IOException {
        for (int marketSegment = 1; marketSegment < 3; marketSegment++) {
            String fileName = fileNamePattern.replace("*", String.valueOf(marketSegment));
            FileUtils.writeStringToFile(new File(directory, fileName), "header,header,header" + System.lineSeparator() + "data,data,data" + System.lineSeparator() + "");
        }
    }

    @Test
    void shouldChangeBCRVValueForDTA0To1IfZero() throws IOException {
        String expectedFileContents = "MKT_SEG_GRP_ID,DOW,LOS,DTA,BCRV" + System.lineSeparator() + "1,1,1,0,1" + System.lineSeparator() + "1,1,1,1,0" + System.lineSeparator() + "1,1,1,2,0.2" + System.lineSeparator() + "1,2,1,0,0.1" + System.lineSeparator() + "1,2,1,1,1" + System.lineSeparator() + "2,1,1,0,1" + "" + System.lineSeparator() + "2,1,1,1,0" + System.lineSeparator() + "2,1,2,0,1" + System.lineSeparator() + "2,1,3,1,1" + System.lineSeparator() + "2,1,3,2,0.2" + System.lineSeparator() + "3,1,1,0,0.1" + System.lineSeparator() + "3,1,1,1,1" + System.lineSeparator() + "3,1,2,2,1" + System.lineSeparator() + "";
        expectedFileContents = expectedFileContents;
        File directory = new File("src/test/resources/limitedDataBuild");
        String response = ldbPatternService.concatenatePatternCsvFiles(directory);
        assertTrue(response.contains("concatenated 0 files to produce NHS_LOS_DIST.csv"));
        assertTrue(response.contains("concatenated 0 files to produce NHS_NOSHOW.csv"));
        assertTrue(response.contains("concatenated 3 files to produce NHS_BCRV.csv"));
        assertTrue((new File(directory, "NHS_LOS_DIST.csv")).exists());
        assertTrue((new File(directory, "NHS_NOSHOW.csv")).exists());
        assertTrue((new File(directory, "NHS_BCRV.csv")).exists());
        String actualContents = FileUtils.readFileToString(new File(directory, "NHS_BCRV.csv"));
        FileUtils.forceDelete(new File("src/test/resources/limitedDataBuild/NHS_LOS_DIST.csv"));
        FileUtils.forceDelete(new File("src/test/resources/limitedDataBuild/NHS_NOSHOW.csv"));
        FileUtils.forceDelete(new File("src/test/resources/limitedDataBuild/NHS_BCRV.csv"));
        assertEquals(expectedFileContents, actualContents);
    }

    @Test
    void test_getMarketSegmentsForSourceProperty() {
        String SOURCE_MS_CODE1 = "CONV_QY";
        String SOURCE_MS_CODE2 = "CONV_UN";
        String SOURCE_MS_CODE3 = "CONV_DEF_HIST";
        Property sourceProperty = new Property();
        List<MktSeg> existingEntities = createExistingEntitiesBy(SOURCE_MS_CODE1, SOURCE_MS_CODE2, SOURCE_MS_CODE3);
        when(tenantCrudService.findAll(MktSeg.class)).thenReturn(existingEntities);
        List<String> results = ldbPatternService.getMarketSegmentsForSourceProperty(sourceProperty, MARKET_SEGMENT_CODE);
        assertEquals(2, results.size());
        assertEquals(SOURCE_MS_CODE1, results.get(0));
        assertEquals(SOURCE_MS_CODE2, results.get(1));
        verify(tenantCrudService).findAll(MktSeg.class);
    }

    @Test
    void shouldGetAllSourceMarketSegmentsForTierMarketSegments() {
        String MARKET_SEGMENT = "TIER1";
        String SOURCE_MS_CODE1 = "CONV_QY";
        String SOURCE_MS_CODE2 = "CONV_UN";
        String SOURCE_MS_CODE3 = "BART";
        Property sourceProperty = new Property();
        List<MktSeg> existingEntities = createExistingEntitiesBy(SOURCE_MS_CODE1, SOURCE_MS_CODE2, SOURCE_MS_CODE3);
        when(tenantCrudService.findAll(MktSeg.class)).thenReturn(existingEntities);
        List<String> results = ldbPatternService.getMarketSegmentsForSourceProperty(sourceProperty, MARKET_SEGMENT);
        assertEquals(3, results.size());
        assertEquals(SOURCE_MS_CODE1, results.get(0));
        assertEquals(SOURCE_MS_CODE2, results.get(1));
        assertEquals(SOURCE_MS_CODE3, results.get(2));
        verify(tenantCrudService).findAll(MktSeg.class);
    }

    @Test
    void ldbMarketSegmentCheckShouldBeCaseInsensitiveWhileGettingSourceMarketSegments() {
        String SOURCE_MS_CODE1 = "CONV_QY";
        String SOURCE_MS_CODE2 = "CONV_UN";
        String SOURCE_MS_CODE3 = "CONV_FU";
        Property sourceProperty = new Property();
        List<MktSeg> existingEntities = createExistingEntitiesBy(SOURCE_MS_CODE1, SOURCE_MS_CODE2, SOURCE_MS_CODE3);
        when(tenantCrudService.findAll(MktSeg.class)).thenReturn(existingEntities);
        List<String> results = ldbPatternService.getMarketSegmentsForSourceProperty(sourceProperty, MARKET_SEGMENT_CODE.toLowerCase());
        assertEquals(3, results.size());
        assertEquals(SOURCE_MS_CODE1, results.get(0));
        assertEquals(SOURCE_MS_CODE2, results.get(1));
        assertEquals(SOURCE_MS_CODE3, results.get(2));
        verify(tenantCrudService).findAll(MktSeg.class);
    }

    @Test
    void ldbTierMarketSegmentCheckShouldBeCaseInsensitiveWhileGettingSourceMarketSegments() {
        String MARKET_SEGMENT = "Tier1";
        String SOURCE_MS_CODE1 = "CONV_QY";
        String SOURCE_MS_CODE2 = "CONV_UN";
        String SOURCE_MS_CODE3 = "BART";
        Property sourceProperty = new Property();
        List<MktSeg> existingEntities = createExistingEntitiesBy(SOURCE_MS_CODE1, SOURCE_MS_CODE2, SOURCE_MS_CODE3);
        when(tenantCrudService.findAll(MktSeg.class)).thenReturn(existingEntities);
        List<String> results = ldbPatternService.getMarketSegmentsForSourceProperty(sourceProperty, MARKET_SEGMENT);
        assertEquals(3, results.size());
        assertEquals(SOURCE_MS_CODE1, results.get(0));
        assertEquals(SOURCE_MS_CODE2, results.get(1));
        assertEquals(SOURCE_MS_CODE3, results.get(2));
        verify(tenantCrudService).findAll(MktSeg.class);
    }

    @Test
    void shouldIgnoreDummyMarketSegmentsWhileGettingMarketSegmentsForSourceProperty() {
        String MARKET_SEGMENT = "TIER1";
        Property sourceProperty = new Property();
        String DUMMY_MS_CODE = "-1";
        String SOURCE_MS_CODE1 = "CONV_UN";
        String SOURCE_MS_CODE2 = "BART";
        List<MktSeg> existingEntities = createExistingEntitiesBy(DUMMY_MS_CODE, SOURCE_MS_CODE1, SOURCE_MS_CODE2);
        when(tenantCrudService.findAll(MktSeg.class)).thenReturn(existingEntities);
        List<String> results = ldbPatternService.getMarketSegmentsForSourceProperty(sourceProperty, MARKET_SEGMENT);
        assertEquals(2, results.size());
        assertEquals(SOURCE_MS_CODE1, results.get(0));
        assertEquals(SOURCE_MS_CODE2, results.get(1));
        verify(tenantCrudService).findAll(MktSeg.class);
    }

    private List<MktSeg> createExistingEntitiesBy(String SOURCE_MS_CODE1, String SOURCE_MS_CODE2, String SOURCE_MS_CODE3) {
        List<MktSeg> existingEntities = new ArrayList<MktSeg>();
        existingEntities.add(createMarketSegmentEntityBy(SOURCE_MS_CODE1));
        existingEntities.add(createMarketSegmentEntityBy(SOURCE_MS_CODE2));
        existingEntities.add(createMarketSegmentEntityBy(SOURCE_MS_CODE3));
        return existingEntities;
    }

    private MktSeg createMarketSegmentEntityBy(String SOURCE_MS_CODE1) {
        MktSeg entity1 = new MktSeg();
        entity1.setCode(SOURCE_MS_CODE1);
        return entity1;
    }

    @SuppressWarnings({"rawtypes", "unchecked"})
    @Test
    void test_saveClonePatternSources() {
        Integer SOURCE_PROPERTY_ID = 1211;
        Integer MARKET_SEGMENT_ID = 1212;
        String SOURCE_MS_CODE1 = "CONV_QY";
        String SOURCE_MS_CODE2 = "CONV_UN";
        Integer SOURCE_MS_ID1 = 913;
        Integer SOURCE_MS_ID2 = 914;
        Property sourceProperty = new Property();
        sourceProperty.setId(SOURCE_PROPERTY_ID);
        MktSeg marketSegment = new MktSeg();
        marketSegment.setId(MARKET_SEGMENT_ID);
        when(multiPropertyCrudService.findByNamedQuerySingleResultForSingleProperty(PacmanWorkContextHelper.getPropertyId(), MktSeg.BY_CODE, QueryParameter.with("code", MARKET_SEGMENT_CODE).parameters())).thenReturn(marketSegment);
        MktSeg sourceMs1 = new MktSeg();
        sourceMs1.setId(SOURCE_MS_ID1);
        MktSeg sourceMs2 = new MktSeg();
        sourceMs2.setId(SOURCE_MS_ID2);
        when(multiPropertyCrudService.findByNamedQuerySingleResultForSingleProperty(SOURCE_PROPERTY_ID, MktSeg.BY_CODE, QueryParameter.with("code", SOURCE_MS_CODE1).parameters())).thenReturn(sourceMs1);
        when(multiPropertyCrudService.findByNamedQuerySingleResultForSingleProperty(SOURCE_PROPERTY_ID, MktSeg.BY_CODE, QueryParameter.with("code", SOURCE_MS_CODE2).parameters())).thenReturn(sourceMs2);
        List<ClonePatternSource> dtos = new ArrayList<ClonePatternSource>();
        ClonePatternSource dto = new ClonePatternSource();
        dto.setMarketSegment(MARKET_SEGMENT_CODE);
        dto.setSourceProperty(sourceProperty);
        List<String> sourceMarketSegments = new ArrayList<String>();
        sourceMarketSegments.add(SOURCE_MS_CODE1);
        sourceMarketSegments.add(SOURCE_MS_CODE2);
        dto.setSourceMarketSegments(sourceMarketSegments);
        dtos.add(dto);
        ldbPatternService.saveClonePatternSources(dtos);
        verify(tenantCrudService).deleteAll(LDBClonePatternSource.class);
        ArgumentCaptor<Collection> captor = ArgumentCaptor.forClass(Collection.class);
        verify(tenantCrudService).save(captor.capture());
        List<LDBClonePatternSource> entities = (List<LDBClonePatternSource>) captor.getValue();
        assertEquals(1, entities.size());
        LDBClonePatternSource entity = entities.get(0);
        assertEquals(MARKET_SEGMENT_ID, entity.getMarketSegmentId());
        assertEquals(SOURCE_PROPERTY_ID, entity.getSourcePropertyId());
        assertEquals(SOURCE_MS_ID1.toString() + ',' + SOURCE_MS_ID2.toString(), entity.getSourceMarketSegmentIds());
    }

    @SuppressWarnings({"rawtypes", "unchecked"})
    @Test
    void test_saveClonePatternSources_newMarketSegment() {
        Integer SOURCE_PROPERTY_ID = 1211;
        Integer MARKET_SEGMENT_ID = 1212;
        String SOURCE_MS_CODE1 = "CONV_QY";
        String SOURCE_MS_CODE2 = "CONV_UN";
        Integer SOURCE_MS_ID1 = 913;
        Integer SOURCE_MS_ID2 = 914;
        Property sourceProperty = new Property();
        sourceProperty.setId(SOURCE_PROPERTY_ID);
        MktSeg marketSegment = new MktSeg();
        marketSegment.setId(MARKET_SEGMENT_ID);
        when(multiPropertyCrudService.findByNamedQuerySingleResultForSingleProperty(PacmanWorkContextHelper.getPropertyId(), MktSeg.BY_CODE, QueryParameter.with("code", MARKET_SEGMENT_CODE).parameters())).thenReturn(null, marketSegment);
        MktSeg sourceMs1 = new MktSeg();
        sourceMs1.setId(SOURCE_MS_ID1);
        MktSeg sourceMs2 = new MktSeg();
        sourceMs2.setId(SOURCE_MS_ID2);
        when(multiPropertyCrudService.findByNamedQuerySingleResultForSingleProperty(SOURCE_PROPERTY_ID, MktSeg.BY_CODE, QueryParameter.with("code", SOURCE_MS_CODE1).parameters())).thenReturn(sourceMs1);
        when(multiPropertyCrudService.findByNamedQuerySingleResultForSingleProperty(SOURCE_PROPERTY_ID, MktSeg.BY_CODE, QueryParameter.with("code", SOURCE_MS_CODE2).parameters())).thenReturn(sourceMs2);
        List<ClonePatternSource> dtos = new ArrayList<ClonePatternSource>();
        ClonePatternSource dto = new ClonePatternSource();
        dto.setMarketSegment(MARKET_SEGMENT_CODE);
        dto.setSourceProperty(sourceProperty);
        List<String> sourceMarketSegments = new ArrayList<String>();
        sourceMarketSegments.add(SOURCE_MS_CODE1);
        sourceMarketSegments.add(SOURCE_MS_CODE2);
        dto.setSourceMarketSegments(sourceMarketSegments);
        dtos.add(dto);
        ldbPatternService.saveClonePatternSources(dtos);
        Map<String, String> missingMarketSegments = new HashMap<String, String>();
        missingMarketSegments.put(MARKET_SEGMENT_CODE, MARKET_SEGMENT_CODE);
        verify(marketSegmentComponent).saveMarketSegment(missingMarketSegments);
        verify(tenantCrudService).deleteAll(LDBClonePatternSource.class);
        ArgumentCaptor<Collection> captor = ArgumentCaptor.forClass(Collection.class);
        verify(tenantCrudService).save(captor.capture());
        List<LDBClonePatternSource> entities = (List<LDBClonePatternSource>) captor.getValue();
        assertEquals(1, entities.size());
        LDBClonePatternSource entity = entities.get(0);
        assertEquals(MARKET_SEGMENT_ID, entity.getMarketSegmentId());
        assertEquals(SOURCE_PROPERTY_ID, entity.getSourcePropertyId());
        assertEquals(SOURCE_MS_ID1.toString() + ',' + SOURCE_MS_ID2.toString(), entity.getSourceMarketSegmentIds());
    }

    @SuppressWarnings("unchecked")
    @Test
    void test_getClonePatternSources_noExisting() {
        PacmanWorkContextHelper.setClientCode("notHilton");
        List<LDBClonePatternSource> entities = new ArrayList<LDBClonePatternSource>();
        when(multiPropertyCrudService.findByNamedQueryForSingleProperty(Mockito.eq(PROPERTY_ID), Mockito.eq(LDBClonePatternSource.GET_ALL), Mockito.anyMap())).thenReturn(entities);
        when(projectionDataService.getMarketSegments()).thenReturn(Arrays.asList(HILTON_LDB_MARKET_SEGMENTS));
        when(multiPropertyCrudService.findByNamedQuerySingleResultForSingleProperty(PROPERTY_ID, MktSegDetailsProposed.GET_MARKET_SEGMENTS_BY_NAME, QueryParameter.with("mktSegName", "CONS").parameters())).thenReturn(new MktSegDetailsProposed());
        when(multiPropertyCrudService.findByNamedQuerySingleResultForSingleProperty(PROPERTY_ID, MktSegDetails.GET_MARKET_SEGMENTS_BY_NAME, QueryParameter.with("mktSegName", "CONS").parameters())).thenReturn(null);
        List<ClonePatternSource> results = ldbPatternService.getClonePatternSources();
        assertEquals(HILTON_LDB_MARKET_SEGMENTS.length, results.size());
        for (ClonePatternSource dto : results) {
            assertNotNull(dto.getMarketSegment());
            assertNull(dto.getSourceProperty());
            assertNull(dto.getSourceMarketSegments());
            if (dto.getMarketSegment().equals("CONS")) {
                assertTrue(dto.isAttributed());
            } else {
                assertFalse(dto.isAttributed());
            }
            assertFalse(dto.isSourceError());
        }
        verify(multiPropertyCrudService).findByNamedQuerySingleResultForSingleProperty(PROPERTY_ID, MktSegDetails.GET_MARKET_SEGMENTS_BY_NAME, QueryParameter.with("mktSegName", "CONS").parameters());
        verify(multiPropertyCrudService).findByNamedQuerySingleResultForSingleProperty(PROPERTY_ID, MktSegDetailsProposed.GET_MARKET_SEGMENTS_BY_NAME, QueryParameter.with("mktSegName", "CONS").parameters());
    }

    @SuppressWarnings("unchecked")
    @Test
    void test_getClonePatternSources_existingData() {
        Integer SOURCE_PROPERTY_ID = 1211;
        Integer MARKET_SEGMENT_ID = 1212;
        Integer SOURCE_MS_ID1 = 913;
        Integer SOURCE_MS_ID2 = 914;
        Integer SOURCE_MS_ID3 = 915;
        Integer SOURCE_MS_ID4 = 916;
        String SOURCE_MS_CODE1 = "CONV_QY";
        String SOURCE_MS_CODE2 = "CONV_UN";
        String SOURCE_MS_CODE3 = "CONV_US";
        PacmanWorkContextHelper.setClientCode("notHilton");
        MktSeg marketSegment = createMarketSegmentEntityBy(MARKET_SEGMENT_CODE);
        // when(tenantCrudService.find(MktSeg.class, MARKET_SEGMENT_ID)).thenReturn(marketSegment);
        when(multiPropertyCrudService.find(Mockito.eq(PROPERTY_ID), Mockito.eq(MktSeg.class), Mockito.eq(new Integer(MARKET_SEGMENT_ID)))).thenReturn(marketSegment);
        MktSeg sourceMs1 = createMarketSegmentEntityBy(SOURCE_MS_CODE1);
        MktSeg sourceMs2 = createMarketSegmentEntityBy(SOURCE_MS_CODE2);
        MktSeg sourceMs3 = createMarketSegmentEntityBy(SOURCE_MS_CODE3);
        sourceMs3.setCode(null);
        when(multiPropertyCrudService.find(SOURCE_PROPERTY_ID, MktSeg.class, SOURCE_MS_ID1)).thenReturn(sourceMs1);
        when(multiPropertyCrudService.find(SOURCE_PROPERTY_ID, MktSeg.class, SOURCE_MS_ID2)).thenReturn(sourceMs2);
        when(multiPropertyCrudService.find(SOURCE_PROPERTY_ID, MktSeg.class, SOURCE_MS_ID3)).thenReturn(sourceMs3);
        when(multiPropertyCrudService.find(SOURCE_PROPERTY_ID, MktSeg.class, SOURCE_MS_ID4)).thenReturn(null);
        List<LDBClonePatternSource> entities = new ArrayList<LDBClonePatternSource>();
        LDBClonePatternSource entity = new LDBClonePatternSource();
        entity.setSourcePropertyId(SOURCE_PROPERTY_ID);
        entity.setMarketSegmentId(MARKET_SEGMENT_ID);
        entity.setSourceMarketSegmentIds(SOURCE_MS_ID1.toString() + ',' + SOURCE_MS_ID2.toString() + ',' + SOURCE_MS_ID3.toString() + ',' + SOURCE_MS_ID4.toString());
        entities.add(entity);
        when(multiPropertyCrudService.findByNamedQueryForSingleProperty(Mockito.eq(PROPERTY_ID), Mockito.eq(LDBClonePatternSource.GET_ALL), Mockito.anyMap())).thenReturn(entities);
        when(projectionDataService.getMarketSegments()).thenReturn(Arrays.asList(HILTON_LDB_MARKET_SEGMENTS));
        Property sourceProperty = new Property();
        sourceProperty.setId(SOURCE_PROPERTY_ID);
        when(propertyService.getPropertyById(SOURCE_PROPERTY_ID)).thenReturn(sourceProperty);
        when(multiPropertyCrudService.findByNamedQuerySingleResultForSingleProperty(PROPERTY_ID, MktSegDetails.GET_MARKET_SEGMENTS_BY_NAME, QueryParameter.with("mktSegName", MARKET_SEGMENT_CODE).parameters())).thenReturn(new MktSegDetails());
        List<ClonePatternSource> results = ldbPatternService.getClonePatternSources();
        assertEquals(HILTON_LDB_MARKET_SEGMENTS.length, results.size());
        for (ClonePatternSource dto : results) {
            assertNotNull(dto.getMarketSegment());
            if (dto.getMarketSegment().equals(MARKET_SEGMENT_CODE)) {
                assertEquals(sourceProperty, dto.getSourceProperty());
                assertEquals(SOURCE_MS_CODE1, dto.getSourceMarketSegments().get(0));
                assertEquals(SOURCE_MS_CODE2, dto.getSourceMarketSegments().get(1));
                assertTrue(dto.isAttributed());
            } else {
                assertNull(dto.getSourceProperty());
                assertNull(dto.getSourceMarketSegments());
            }
            assertFalse(dto.isSourceError());
        }
        verify(multiPropertyCrudService).findByNamedQuerySingleResultForSingleProperty(PROPERTY_ID, MktSegDetails.GET_MARKET_SEGMENTS_BY_NAME, QueryParameter.with("mktSegName", MARKET_SEGMENT_CODE).parameters());
    }

    @Test
    void shouldBeAbleToGetMarketSegmentsForSourcePropertyByBusinessType() {
        // GIVEN
        PacmanWorkContextHelper.setClientCode("notHilton");
        Property sourceProperty = new Property();
        sourceProperty.setId(6);
        sourceProperty.setCode("H2");
        MktSegDetails mktSegDetail = new MktSegDetails();
        BusinessType transientBusinessType = createBusinessTypeWith("Transient", 2);
        mktSegDetail.setBusinessType(transientBusinessType);
        when(multiPropertyCrudService.findByNamedQuerySingleResultForSingleProperty(PacmanWorkContextHelper.getPropertyId(), MktSegDetails.GET_MARKET_SEGMENTS_BY_NAME, QueryParameter.with("mktSegName", "COMP").parameters())).thenReturn(mktSegDetail);
        when(multiPropertyCrudService.findByNamedQueryForSingleProperty((sourceProperty.getId()), MktSegDetails.GET_MARKET_SEGMENTS_CODE_BY_BUSINESS_TYPE, QueryParameter.with("businessTypeId", mktSegDetail.getBusinessType().getId()).parameters())).thenReturn(Arrays.asList("BART"));
        // WHEN
        List<String> marketSegments = ldbPatternService.getMarketSegmentsForSourcePropertyByBusinessType(sourceProperty, "COMP");
        // THEN
        assertEquals("BART", marketSegments.get(0));
        verify(multiPropertyCrudService).findByNamedQuerySingleResultForSingleProperty(PacmanWorkContextHelper.getPropertyId(), MktSegDetails.GET_MARKET_SEGMENTS_BY_NAME, QueryParameter.with("mktSegName", "COMP").parameters());
        verify(multiPropertyCrudService).findByNamedQueryForSingleProperty((sourceProperty.getId()), MktSegDetails.GET_MARKET_SEGMENTS_CODE_BY_BUSINESS_TYPE, QueryParameter.with("businessTypeId", mktSegDetail.getBusinessType().getId()).parameters());
    }

    @Test
    void shouldAcquireBusinessTypeFromProposedMSIfNotAvailableInMktSegDetails() {
        // GIVEN
        PacmanWorkContextHelper.setClientCode("notHilton");
        Property sourceProperty = new Property();
        sourceProperty.setId(6);
        sourceProperty.setCode("H2");
        when(multiPropertyCrudService.findByNamedQuerySingleResultForSingleProperty(PacmanWorkContextHelper.getPropertyId(), MktSegDetails.GET_MARKET_SEGMENTS_BY_NAME, QueryParameter.with("mktSegName", "COMP").parameters())).thenReturn(null);
        MktSegDetailsProposed mktSegDetailProposed = new MktSegDetailsProposed();
        BusinessType transientBusinessType = createBusinessTypeWith("Transient", 2);
        mktSegDetailProposed.setBusinessType(transientBusinessType);
        when(multiPropertyCrudService.findByNamedQuerySingleResultForSingleProperty(PacmanWorkContextHelper.getPropertyId(), MktSegDetailsProposed.GET_MARKET_SEGMENTS_BY_NAME, QueryParameter.with("mktSegName", "COMP").parameters())).thenReturn(mktSegDetailProposed);
        when(multiPropertyCrudService.findByNamedQueryForSingleProperty((sourceProperty.getId()), MktSegDetails.GET_MARKET_SEGMENTS_CODE_BY_BUSINESS_TYPE, QueryParameter.with("businessTypeId", 2).parameters())).thenReturn(Arrays.asList("BART"));
        // WHEN
        List<String> marketSegments = ldbPatternService.getMarketSegmentsForSourcePropertyByBusinessType(sourceProperty, "COMP");
        // THEN
        assertEquals("BART", marketSegments.get(0));
        verify(multiPropertyCrudService).findByNamedQuerySingleResultForSingleProperty(PacmanWorkContextHelper.getPropertyId(), MktSegDetails.GET_MARKET_SEGMENTS_BY_NAME, QueryParameter.with("mktSegName", "COMP").parameters());
        verify(multiPropertyCrudService).findByNamedQuerySingleResultForSingleProperty(PacmanWorkContextHelper.getPropertyId(), MktSegDetailsProposed.GET_MARKET_SEGMENTS_BY_NAME, QueryParameter.with("mktSegName", "COMP").parameters());
        verify(multiPropertyCrudService).findByNamedQueryForSingleProperty((sourceProperty.getId()), MktSegDetails.GET_MARKET_SEGMENTS_CODE_BY_BUSINESS_TYPE, QueryParameter.with("businessTypeId", 2).parameters());
    }

    @Test
    void shouldNotGetMSCodesWhenBusinessTypeForLDBMSNotFound() {
        // GIVEN
        PacmanWorkContextHelper.setClientCode("notHilton");
        Property sourceProperty = new Property();
        sourceProperty.setId(6);
        sourceProperty.setCode("H2");
        when(multiPropertyCrudService.findByNamedQuerySingleResultForSingleProperty(PacmanWorkContextHelper.getPropertyId(), MktSegDetails.GET_MARKET_SEGMENTS_BY_NAME, QueryParameter.with("mktSegName", "COMP").parameters())).thenReturn(null);
        when(multiPropertyCrudService.findByNamedQuerySingleResultForSingleProperty(PacmanWorkContextHelper.getPropertyId(), MktSegDetailsProposed.GET_MARKET_SEGMENTS_BY_NAME, QueryParameter.with("mktSegName", "COMP").parameters())).thenReturn(null);
        // WHEN
        List<String> marketSegments = ldbPatternService.getMarketSegmentsForSourcePropertyByBusinessType(sourceProperty, "COMP");
        // THEN
        assertEquals(0, marketSegments.size());
        verify(multiPropertyCrudService).findByNamedQuerySingleResultForSingleProperty(PacmanWorkContextHelper.getPropertyId(), MktSegDetails.GET_MARKET_SEGMENTS_BY_NAME, QueryParameter.with("mktSegName", "COMP").parameters());
        verify(multiPropertyCrudService).findByNamedQuerySingleResultForSingleProperty(PacmanWorkContextHelper.getPropertyId(), MktSegDetailsProposed.GET_MARKET_SEGMENTS_BY_NAME, QueryParameter.with("mktSegName", "COMP").parameters());
    }

    @Test
    void shouldNotGetMSCodesIfNotFoundInMktSegDetailsForSourceProperty() {
        // GIVEN
        PacmanWorkContextHelper.setClientCode("notHilton");
        Property sourceProperty = new Property();
        sourceProperty.setId(6);
        sourceProperty.setCode("H2");
        MktSegDetails mktSegDetail = new MktSegDetails();
        BusinessType transientBusinessType = createBusinessTypeWith("Transient", 2);
        mktSegDetail.setBusinessType(transientBusinessType);
        when(multiPropertyCrudService.findByNamedQuerySingleResultForSingleProperty(PacmanWorkContextHelper.getPropertyId(), MktSegDetails.GET_MARKET_SEGMENTS_BY_NAME, QueryParameter.with("mktSegName", "COMP").parameters())).thenReturn(mktSegDetail);
        when(multiPropertyCrudService.findByNamedQueryForSingleProperty((sourceProperty.getId()), MktSegDetails.GET_MARKET_SEGMENTS_CODE_BY_BUSINESS_TYPE, QueryParameter.with("businessTypeId", mktSegDetail.getBusinessType().getId()).parameters())).thenReturn(null);
        when(multiPropertyCrudService.findByNamedQueryForSingleProperty((sourceProperty.getId()), MktSegDetails.GET_MARKET_SEGMENTS_CODE_BY_BUSINESS_TYPE, QueryParameter.with("businessTypeId", mktSegDetail.getBusinessType().getId()).parameters())).thenReturn(null);
        // WHEN
        List<String> marketSegments = ldbPatternService.getMarketSegmentsForSourcePropertyByBusinessType(sourceProperty, "COMP");
        // THEN
        assertEquals(0, marketSegments.size());
        verify(multiPropertyCrudService).findByNamedQuerySingleResultForSingleProperty(PacmanWorkContextHelper.getPropertyId(), MktSegDetails.GET_MARKET_SEGMENTS_BY_NAME, QueryParameter.with("mktSegName", "COMP").parameters());
        verify(multiPropertyCrudService).findByNamedQueryForSingleProperty((sourceProperty.getId()), MktSegDetails.GET_MARKET_SEGMENTS_CODE_BY_BUSINESS_TYPE, QueryParameter.with("businessTypeId", mktSegDetail.getBusinessType().getId()).parameters());
        verify(multiPropertyCrudService).findByNamedQueryForSingleProperty((sourceProperty.getId()), MktSegDetails.GET_MARKET_SEGMENTS_CODE_BY_BUSINESS_TYPE, QueryParameter.with("businessTypeId", mktSegDetail.getBusinessType().getId()).parameters());
    }

    private BusinessType createBusinessTypeWith(String type, int id) {
        BusinessType businessType = new BusinessType();
        businessType.setId(id);
        businessType.setName(type);
        return businessType;
    }

    @Test
    void acquireBusinessTypeFromBookingCurve() {
        LDBGlobalGenericBookingCurve globalGenericBookingCurve = new LDBGlobalGenericBookingCurve();
        globalGenericBookingCurve.setBusinessTypeId(1);
        when(globalCrudService.findByNamedQuerySingleResult(LDBGlobalGenericBookingCurve.GET_BY_LABEL, QueryParameter.with("label", "Airport_Transient_Short_01").parameters())).thenReturn(globalGenericBookingCurve);
        Integer businessTypeId = ldbPatternService.acquireBusinessTypeFromBookingCurve("Airport_Transient_Short_01");
        assertEquals(1, businessTypeId.intValue());
        verify(globalCrudService).findByNamedQuerySingleResult(LDBGlobalGenericBookingCurve.GET_BY_LABEL, QueryParameter.with("label", "Airport_Transient_Short_01").parameters());
    }

    @Test
    void returnMinusOneWhenBusinessTypeNotFoundForBookingCurve() {
        when(globalCrudService.findByNamedQuerySingleResult(LDBGlobalGenericBookingCurve.GET_BY_LABEL, QueryParameter.with("label", "Airport_Transient_Short_01").parameters())).thenReturn(null);
        Integer businessTypeId = ldbPatternService.acquireBusinessTypeFromBookingCurve("Airport_Transient_Short_01");
        assertEquals(-1, businessTypeId.intValue());
        verify(globalCrudService).findByNamedQuerySingleResult(LDBGlobalGenericBookingCurve.GET_BY_LABEL, QueryParameter.with("label", "Airport_Transient_Short_01").parameters());
    }

    @Test
    void returnFalseWhenLDBConfigNotAvailable() {
        when(tenantCrudService.findByNamedQuery(LDBConfig.GET_ALL)).thenReturn(null);
        boolean marketSegmentUsedInLDBConfiguration = ldbPatternService.isMarketSegmentUsedInLDBConfiguration(new ArrayList<>());
        assertFalse(marketSegmentUsedInLDBConfiguration);
        verify(tenantCrudService).findByNamedQuery(LDBConfig.GET_ALL);
    }

    @Test
    void returnTrueWhenMarketSegmentUsedInLDBClonePattern() {
        // GIVEN
        List ldbConfigs = new ArrayList();
        LDBConfig ldbConfig = new LDBConfig();
        ldbConfig.setPatternSource(PatternSource.CLONE);
        ldbConfigs.add(ldbConfig);
        when(tenantCrudService.findByNamedQuery(LDBConfig.GET_ALL)).thenReturn(ldbConfigs);
        List ldbClonePatternSources = new ArrayList();
        ldbClonePatternSources.add(createLdbPatternSource(MARKET_SEGMENT_ID_1, MARKET_SEGMENT_ID_2.toString()));
        when(tenantCrudService.findByNamedQuery(LDBClonePatternSource.GET_ALL)).thenReturn(ldbClonePatternSources);
        List<MktSeg> marketSegments = new ArrayList<>();
        MktSeg mktSeg = createMktSeg(MARKET_SEGMENT_ID_1);
        marketSegments.add(mktSeg);
        // WHEN
        boolean marketSegmentUsedInLDBConfiguration = ldbPatternService.isMarketSegmentUsedInLDBConfiguration(marketSegments);
        // THEN
        assertTrue(marketSegmentUsedInLDBConfiguration);
        verify(tenantCrudService).findByNamedQuery(LDBConfig.GET_ALL);
        verify(tenantCrudService).findByNamedQuery(LDBClonePatternSource.GET_ALL);
    }

    private MktSeg createMktSeg(Integer marketSegmentId1) {
        MktSeg mktSeg = new MktSeg();
        mktSeg.setId(marketSegmentId1);
        return mktSeg;
    }

    @Test
    void returnTrueWhenMarketSegmentUsedInGenericPattern() {
        // GIVEN
        List ldbConfigs = new ArrayList();
        LDBConfig ldbConfig = new LDBConfig();
        ldbConfig.setPatternSource(PatternSource.GENERIC);
        ldbConfigs.add(ldbConfig);
        when(tenantCrudService.findByNamedQuery(LDBConfig.GET_ALL)).thenReturn(ldbConfigs);
        List genericPatterns = new ArrayList<>();
        LDBGenericPatternData genericPatternData = new LDBGenericPatternData();
        genericPatternData.setMarketSegmentId(MARKET_SEGMENT_ID_1);
        genericPatterns.add(genericPatternData);
        when(tenantCrudService.findByNamedQuery(LDBGenericPatternData.GET_ALL)).thenReturn(genericPatterns);
        List<MktSeg> marketSegments = new ArrayList<>();
        MktSeg mktSeg = createMktSeg(MARKET_SEGMENT_ID_1);
        marketSegments.add(mktSeg);
        // WHEN
        boolean marketSegmentUsedInLDBConfiguration = ldbPatternService.isMarketSegmentUsedInLDBConfiguration(marketSegments);
        // THEN
        assertTrue(marketSegmentUsedInLDBConfiguration);
        verify(tenantCrudService).findByNamedQuery(LDBConfig.GET_ALL);
        verify(tenantCrudService).findByNamedQuery(LDBGenericPatternData.GET_ALL);
    }

    private LDBClonePatternSource createLdbPatternSource(Integer marketSegmentId1, String sourceMarketSegmentIds) {
        LDBClonePatternSource ldbClonePatternSource = new LDBClonePatternSource();
        ldbClonePatternSource.setMarketSegmentId(marketSegmentId1);
        ldbClonePatternSource.setSourceMarketSegmentIds(sourceMarketSegmentIds);
        return ldbClonePatternSource;
    }

    @Test
    void shouldRemoveLdbConfigForMarketSegments() {
        List<MktSeg> marketSegments = new ArrayList<>();
        marketSegments.add(createMktSeg(MARKET_SEGMENT_ID_1));
        marketSegments.add(createMktSeg(MARKET_SEGMENT_ID_2));
        when(tenantCrudService.executeUpdateByNamedQuery(LDBClonePatternSource.DELETE_BY_MARKET_SEGMENTS, QueryParameter.with("marketSegmentIds", Arrays.asList(MARKET_SEGMENT_ID_1, MARKET_SEGMENT_ID_2)).parameters())).thenReturn(2);
        when(tenantCrudService.executeUpdateByNamedQuery(LDBGenericPatternData.DELETE_BY_MARKET_SEGMENTS, QueryParameter.with("marketSegmentIds", Arrays.asList(MARKET_SEGMENT_ID_1, MARKET_SEGMENT_ID_2)).parameters())).thenReturn(2);
        ldbPatternService.removeLDBConfigForMarketSegments(marketSegments);
        verify(tenantCrudService).executeUpdateByNamedQuery(LDBClonePatternSource.DELETE_BY_MARKET_SEGMENTS, QueryParameter.with("marketSegmentIds", Arrays.asList(MARKET_SEGMENT_ID_1, MARKET_SEGMENT_ID_2)).parameters());
        verify(tenantCrudService).executeUpdateByNamedQuery(LDBGenericPatternData.DELETE_BY_MARKET_SEGMENTS, QueryParameter.with("marketSegmentIds", Arrays.asList(MARKET_SEGMENT_ID_1, MARKET_SEGMENT_ID_2)).parameters());
    }

    @Test
    void isPropertyBuildForLDB() {
        // GIVEN
        List<LDBBuildHistory> buildHistory = new ArrayList<>();
        LDBBuildHistory history = new LDBBuildHistory();
        history.setId(1);
        buildHistory.add(history);
        when(tenantCrudService.findAll(LDBBuildHistory.class)).thenReturn(buildHistory);
        // WHEN
        boolean propertyBuildForLDB = ldbPatternService.isPropertyBuildForLDB();
        // THEN
        assertTrue(propertyBuildForLDB);
        verify(tenantCrudService).findAll(LDBBuildHistory.class);
    }

    @Test
    void propertyIsNotBuildForLDBWhenBuildHistoryIsNull() {
        when(tenantCrudService.findAll(LDBBuildHistory.class)).thenReturn(null);
        boolean propertyBuildForLDB = ldbPatternService.isPropertyBuildForLDB();
        assertFalse(propertyBuildForLDB);
        verify(tenantCrudService).findAll(LDBBuildHistory.class);
    }

    @Test
    void propertyIsNotBuildForLDBWhenBuildHistoryIsEmpty() {
        when(tenantCrudService.findAll(LDBBuildHistory.class)).thenReturn(new ArrayList<LDBBuildHistory>());
        boolean propertyBuildForLDB = ldbPatternService.isPropertyBuildForLDB();
        assertFalse(propertyBuildForLDB);
        verify(tenantCrudService).findAll(LDBBuildHistory.class);
    }

    @Test
    void CCFGShouldBeAsumedAsCompletedWhenThereAreEntriesInMktSegDetails() {
        ArrayList<MktSegDetails> mktSegDetails = new ArrayList<>();
        mktSegDetails.add(new MktSegDetails());
        when(tenantCrudService.findAll(MktSegDetails.class)).thenReturn(mktSegDetails);
        boolean hasCCFGCompletedOnce = ldbPatternService.hasCCFGCompletedOnce();
        assertTrue(hasCCFGCompletedOnce);
        verify(tenantCrudService).findAll(MktSegDetails.class);
    }

    @Test
    void CCFGShouldBeAsumedAsNotCompletedWhenThereAreNoEntriesInMktSegDetails() {
        when(tenantCrudService.findAll(MktSegDetails.class)).thenReturn(new ArrayList<>());
        boolean hasCCFGCompletedOnce = ldbPatternService.hasCCFGCompletedOnce();
        assertFalse(hasCCFGCompletedOnce);
        verify(tenantCrudService).findAll(MktSegDetails.class);
    }
}
