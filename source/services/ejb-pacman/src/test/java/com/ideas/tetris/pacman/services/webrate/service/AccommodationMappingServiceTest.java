package com.ideas.tetris.pacman.services.webrate.service;


import com.fasterxml.jackson.core.JsonProcessingException;
import com.ideas.g3.data.TestProperty;
import com.ideas.g3.rule.CrudServiceBeanExtension;
import com.ideas.g3.test.AbstractG3JupiterTest;
import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.configparams.GUIConfigParamName;
import com.ideas.tetris.pacman.common.configparams.IPConfigParamName;
import com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.common.fds.G3SNSService;
import com.ideas.tetris.pacman.common.fds.dto.EventType;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomClass;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomType;
import com.ideas.tetris.pacman.services.accommodation.entity.UniqueAccomClassCreator;
import com.ideas.tetris.pacman.services.accommodation.service.AccommodationService;
import com.ideas.tetris.pacman.services.agilerates.configuration.entity.ProductAccomType;
import com.ideas.tetris.pacman.services.agilerates.configuration.service.AgileRatesConfigurationService;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.MktSegAccomActivity;
import com.ideas.tetris.pacman.services.businessgroup.service.UniqueMktSegCreator;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.dao.UniqueTenantPropertyCreator;
import com.ideas.tetris.pacman.services.configautomation.dto.RoomClassMappingDTO;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.eventaggregator.SyncEvent;
import com.ideas.tetris.pacman.services.eventaggregator.SyncEventAggregatorService;
import com.ideas.tetris.pacman.services.eventaggregator.SystemComponent;
import com.ideas.tetris.pacman.services.informationmanager.alert.service.AlertService;
import com.ideas.tetris.pacman.services.informationmanager.dto.Alert;
import com.ideas.tetris.pacman.services.informationmanager.dto.AlertType;
import com.ideas.tetris.pacman.services.marketsegment.entity.MktSegDetails;
import com.ideas.tetris.pacman.services.marketsegment.entity.UniqueMktSegDetailsCreator;
import com.ideas.tetris.pacman.services.product.Product;
import com.ideas.tetris.pacman.services.property.PropertyService;
import com.ideas.tetris.pacman.services.rateshoppingadjustment.entity.RateShoppingAdjustment;
import com.ideas.tetris.pacman.services.tax.entity.Tax;
import com.ideas.tetris.pacman.services.tax.service.TaxService;
import com.ideas.tetris.pacman.services.webrate.dto.WebrateAccomClassMappingDTO;
import com.ideas.tetris.pacman.services.webrate.dto.WebrateAccomTypeAverageRateDTO;
import com.ideas.tetris.pacman.services.webrate.entity.*;
import com.ideas.tetris.pacman.services.webrate.enums.CompetitiveMarketPositionConstraintEnum;
import com.ideas.tetris.platform.common.crudservice.AbstractMultiPropertyCrudService;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.job.JobName;
import com.ideas.tetris.platform.common.time.LocalDateUtils;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import com.ideas.tetris.platform.services.daoandentities.entity.Client;
import com.ideas.tetris.platform.services.daoandentities.entity.Property;
import org.apache.commons.collections.CollectionUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.ideas.tetris.pacman.common.constants.Constants.*;
import static com.ideas.tetris.pacman.services.webrate.entity.WebrateAccomType.PARAM_ACCOM_NAMES;
import static com.ideas.tetris.pacman.services.webrate.entity.WebrateAccomType.PARAM_PROPERTY_ID;
import static com.ideas.tetris.platform.common.crudservice.QueryParameter.with;
import static java.util.Collections.emptyList;
import static java.util.Collections.singletonList;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.core.Is.is;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
@MockitoSettings(strictness = Strictness.LENIENT)
@DisplayName("Accommodation Mapping Service Test")
class AccommodationMappingServiceTest extends AbstractG3JupiterTest {

    public static final String WEB_RATE_HOTEL_ID = "webRateHotelId";
    public static final String TEST = "Test";
    private static final LocalDate CAUGHTUP_DATE = LocalDate.of(2021, 12, 10);
    private static final Integer TEST_PROPERTY_ID = 5;
    private static final String TEST_UPS_ID = UUID.randomUUID().toString();
    private static final String NAME = "name";
    private static final String ALIAS = "alias";
    private static final String ACCOM_CODE = "accomCode";

    @Spy
    @InjectMocks
    protected static AccommodationMappingService accomMappingService = new AccommodationMappingService();
    @Mock
    protected static AccommodationService accommodation2Service;

    @Mock
    private CompetitorDataFilterService competitorDataFilterService;

    @Mock
    private WebrateShoppingDataService webrateShoppingDataService;

    @Mock
    private DefaultReferenceChannelService defaultReferenceChannelService;

    @Mock
    private AgileRatesConfigurationService agileRatesConfigurationService;

    @Mock
    private TaxService taxService;

    @Mock
    private PacmanConfigParamsService configParamsService;
    @Mock
    private DateService dateService;
    @Mock
    private AlertService alertService;
    private SyncEventAggregatorService syncEventAggregatorService;

    private WebrateShoppingCleanUpService webrateShoppingCleanUpService;

    private WebrateCompetitors competitor;

    @Mock
    private PropertyService propertyService;

    @Mock
    private G3SNSService g3SNSService;

    SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd", Locale.ENGLISH);

    @BeforeEach
    void setUp() {
        accomMappingService.setCrudService(tenantCrudService());

        syncEventAggregatorService = mock(SyncEventAggregatorService.class);
        accomMappingService.setSyncEventAggregatorService(syncEventAggregatorService);

        webrateShoppingCleanUpService = mock(WebrateShoppingCleanUpService.class);
        accomMappingService.setWebrateShoppingCleanUpService(webrateShoppingCleanUpService);

        MockitoAnnotations.initMocks(this);
        when(configParamsService.getParameterValue(IPConfigParamName.BAR_WEB_RATE_ALIAS.value())).thenReturn(WEB_RATE_HOTEL_ID);
        when(configParamsService.getParameterValue(FeatureTogglesConfigParamName.RDL_CLONED_FROM_PROPERTY_UPS_ID)).thenReturn(null);
    }

    @Test
    @DisplayName("Test Get Accomodation Mapping By Property")
    void testGetAccomodationMappingByProperty() {
        WebrateAccomType dbWebrateAccomType = UniqueWebrateAccomTypeCreator.createWebrateAccomType();
        List<WebrateAccomType> webrateAccomTypeList = accomMappingService.getAccomodationMappingByProperty(dbWebrateAccomType.getPropertyId());
        assertFalse(webrateAccomTypeList.isEmpty());
    }

    @Test
    @DisplayName("Get Accomodation Mapping By Property")
    void getAccomodationMappingByProperty() {
        final List<WebrateAccomType> webrateAccomTypes = accomMappingService.getAccomodationMappingByProperty();
        Assertions.assertNotNull(webrateAccomTypes);
        Assertions.assertFalse(webrateAccomTypes.isEmpty());
    }

    @Test
    @DisplayName("Test Get All Webrate Ranking")
    void testGetAllWebrateRanking() {
        UniqueWebrateRankingCreator.createWebrateRanking();
        List<WebrateRanking> webrateRankingList = accomMappingService.getAllWebrateRanking();
        assertFalse(webrateRankingList.isEmpty());

    }

    @Test
    @DisplayName("Test Save Accom Mapping Webrate Accom Type")
    void testSaveAccomMappingWebrateAccomType() {
        Product barProduct = createBarProduct();
        // US3722
        AccommodationClassMappingTestData.createTestData();

        List<WebrateAccomType> webrateAccomTypeList = accomMappingService.getAccomodationMappingByProperty(5);

        when(agileRatesConfigurationService.findAgileAndSystemDefaultProductsAndIndependentProducts()).thenReturn(List.of(barProduct));
        boolean value = accomMappingService.saveAccomMapping(webrateAccomTypeList, null, 5, "1");

        Assertions.assertTrue(value);
    }

    @Test
    @DisplayName("Test Save Accom Mapping Web Ranking One Without Sync")
    void testSaveAccomMappingWebRankingOneWithoutSync() {
        WebrateRankingAccomClass rankingAccomClass = UniqueWebrateRankingAccomClassCreator.createWebrateRankingAccomClass();

        tenantCrudService().getEntityManager().flush();
        tenantCrudService().getEntityManager().clear();

        List<WebrateRankingAccomClass> rankingAccomList = new ArrayList<>();
        rankingAccomList.add(rankingAccomClass);

        boolean value = accomMappingService.saveAccomMapping(null, rankingAccomList, 100, "1");
        Assertions.assertTrue(value);
    }

    @Test
    @DisplayName("Test Save Accom Mapping Web Ranking Two")
    void testSaveAccomMappingWebRankingTwo() {
        WebrateRankingAccomClass rankingAccomClass = UniqueWebrateRankingAccomClassCreator.createWebrateRankingAccomClass();

        tenantCrudService().getEntityManager().flush();
        tenantCrudService().getEntityManager().clear();

        rankingAccomClass.setIsDeleted(1);

        List<WebrateRankingAccomClass> rankingAccomList = new ArrayList<>();
        rankingAccomList.add(rankingAccomClass);

        syncEventAggregatorService.clearSyncEvent(SyncEvent.WEBRATE_CONFIG_CHANGED_NO_SYNC_REQUIRED);

        syncEventAggregatorService.registerSyncEvent(SyncEvent.WEBRATE_CONFIG_CHANGED);

        boolean value = accomMappingService.saveAccomMapping(null, rankingAccomList, 100, "1");
        Assertions.assertTrue(value);

        verify(syncEventAggregatorService, times(2)).clearSyncEvent(SyncEvent.WEBRATE_CONFIG_CHANGED_NO_SYNC_REQUIRED);
        verify(syncEventAggregatorService, times(2)).registerSyncEvent(SyncEvent.WEBRATE_CONFIG_CHANGED);
    }

    @Test
    @DisplayName("Test Save Accom Mapping With Toggle On")
    void testSaveAccomMappingWithToggleOn() {
        // Covers Entity mapping changes for US5805
        WebrateRankingAccomClass rankingAccomClass = UniqueWebrateRankingAccomClassCreator.createWebrateRankingAccomClassWithOvr();
        WebrateRanking rank1 = UniqueWebrateRankingCreator.createWebrateRanking();
        WebrateRanking rank2 = UniqueWebrateRankingCreator.createWebrateRanking();

        rankingAccomClass.setWebrateRankingSaturday(rank1);
        rankingAccomClass.setWebrateRankingSunday(rank2);

        List<WebrateRankingAccomClass> webrateRankingAccomClassList = new ArrayList<>();
        webrateRankingAccomClassList.add(rankingAccomClass);

        List<WebrateAccomType> webrateAccomTypeList = accomMappingService.getAccomodationMappingByProperty(5);
        System.setProperty("pacman.webrateshopping.marketpostionconstraint.dow.enabled", "true");
        boolean value = accomMappingService.saveAccomMapping(webrateAccomTypeList, webrateRankingAccomClassList, 5, "1");
        Assertions.assertTrue(value);

        WebrateRankingAccomClass dbWebRateAccomClass = tenantCrudService().find(WebrateRankingAccomClass.class, rankingAccomClass.getId());

        Assertions.assertFalse((dbWebRateAccomClass.getWebrateRankingSaturday().getWebrateRankingName().equalsIgnoreCase(rankingAccomClass.getWebrateRanking().getWebrateRankingName())));
        Assertions.assertFalse((dbWebRateAccomClass.getWebrateRankingSunday().getWebrateRankingName().equalsIgnoreCase(rankingAccomClass.getWebrateRanking().getWebrateRankingName())));

    }

    @Test
    @DisplayName("Test Save Accom Mapping With Toggle Off")
    void testSaveAccomMappingWithToggleOff() {
        WebrateRankingAccomClass rankingAccomClass = UniqueWebrateRankingAccomClassCreator.createWebrateRankingAccomClassWithOvr();
        WebrateRanking rank1 = UniqueWebrateRankingCreator.createWebrateRanking();
        WebrateRanking rank2 = UniqueWebrateRankingCreator.createWebrateRanking();

        rankingAccomClass.setWebrateRankingSaturday(rank1);
        rankingAccomClass.setWebrateRankingSunday(rank2);

        List<WebrateRankingAccomClass> webrateRankingAccomClassList = new ArrayList<>();
        webrateRankingAccomClassList.add(rankingAccomClass);

        List<WebrateAccomType> webrateAccomTypeList = accomMappingService.getAccomodationMappingByProperty(5);
        System.setProperty("pacman.webrateshopping.marketpostionconstraint.dow.enabled", "false");
        boolean value = accomMappingService.saveAccomMapping(webrateAccomTypeList, webrateRankingAccomClassList, 5, "1");
        Assertions.assertTrue(value);

        WebrateRankingAccomClass dbWebRateAccomClass = tenantCrudService().find(WebrateRankingAccomClass.class, rankingAccomClass.getId());

        Assertions.assertTrue((dbWebRateAccomClass.getWebrateRankingSaturday().getWebrateRankingName().equalsIgnoreCase(rankingAccomClass.getWebrateRanking().getWebrateRankingName())));
        Assertions.assertTrue((dbWebRateAccomClass.getWebrateRankingSunday().getWebrateRankingName().equalsIgnoreCase(rankingAccomClass.getWebrateRanking().getWebrateRankingName())));

    }

    @Test
    @DisplayName("Test Save Accom Mapping")
    void testSaveAccomMapping() {
        WebrateRankingAccomClass rankingAccomClass = UniqueWebrateRankingAccomClassCreator.createWebrateRankingAccomClassWithOvr();

        List<WebrateRankingAccomClass> webrateRankingAccomClassList = new ArrayList<>();
        webrateRankingAccomClassList.add(rankingAccomClass);

        List<WebrateAccomType> webrateAccomTypeList = accomMappingService.getAccomodationMappingByProperty(5);

        boolean value = accomMappingService.saveAccomMapping(webrateAccomTypeList, webrateRankingAccomClassList, 5, "1");
        Assertions.assertTrue(value);
    }

    @Test
    @DisplayName("Test Remove Season Override")
    void testRemoveSeasonOverride() {
        // Handled Delete season scenario
        WebrateRankingAccomClassOverride ovrInstance = UniqueWebrateRankingAccomClassCreator.createWebrateRankingAccomClassOverride();
        ovrInstance.setIsDeleted(1);

        List<WebrateRankingAccomClassOverride> webrateRankingACOverridesList = new ArrayList<>();
        webrateRankingACOverridesList.add(ovrInstance);

        when(syncEventAggregatorService.isSystemComponentDirty(SystemComponent.WEBRATE)).thenReturn(false);

        syncEventAggregatorService.registerSyncEvent(SyncEvent.WEBRATE_CONFIG_CHANGED_NO_SYNC_REQUIRED);

        accomMappingService.removeSeasonOverride(webrateRankingACOverridesList);

        WebrateRankingAccomClassOverride objDelOvr = tenantCrudService().find(WebrateRankingAccomClassOverride.class, ovrInstance.getId());
        Assertions.assertNull(objDelOvr);

        verify(syncEventAggregatorService, times(2)).registerSyncEvent(SyncEvent.WEBRATE_CONFIG_CHANGED_NO_SYNC_REQUIRED);
    }

    @Test
    @DisplayName("Test Sync Flag _ Turned On _ With Update To Default Config _ Webrate Ranking For Sunday")
    void testSyncFlag_TurnedOn_WithUpdateToDefaultConfig_WebrateRankingForSunday() {
        WebrateRankingAccomClass rankingAccomClass = UniqueWebrateRankingAccomClassCreator.createWebrateRankingAccomClass();

        tenantCrudService().getEntityManager().flush();
        tenantCrudService().getEntityManager().clear();

        WebrateRanking rankingForSunday = UniqueWebrateRankingCreator.createWebrateRanking();
        rankingAccomClass.setWebrateRankingSunday(rankingForSunday);

        boolean value = accomMappingService.isSyncRequiredForDefaultConfigChanged(rankingAccomClass);
        Assertions.assertTrue(value);
    }

    @Test
    @DisplayName("Test Sync Flag _ Turned On _ With Update To Default Config _ Webrate Ranking For Monday")
    void testSyncFlag_TurnedOn_WithUpdateToDefaultConfig_WebrateRankingForMonday() {
        WebrateRankingAccomClass rankingAccomClass = UniqueWebrateRankingAccomClassCreator.createWebrateRankingAccomClass();

        tenantCrudService().getEntityManager().flush();
        tenantCrudService().getEntityManager().clear();

        WebrateRanking rankingForMonday = UniqueWebrateRankingCreator.createWebrateRanking();
        rankingAccomClass.setWebrateRankingMonday(rankingForMonday);

        boolean value = accomMappingService.isSyncRequiredForDefaultConfigChanged(rankingAccomClass);
        Assertions.assertTrue(value);
    }

    @Test
    @DisplayName("Test Sync Flag _ Turned On _ With Update To Default Config _ Webrate Ranking For Tuesday")
    void testSyncFlag_TurnedOn_WithUpdateToDefaultConfig_WebrateRankingForTuesday() {
        WebrateRankingAccomClass rankingAccomClass = UniqueWebrateRankingAccomClassCreator.createWebrateRankingAccomClass();

        tenantCrudService().getEntityManager().flush();
        tenantCrudService().getEntityManager().clear();

        WebrateRanking rankingForTuesday = UniqueWebrateRankingCreator.createWebrateRanking();
        rankingAccomClass.setWebrateRankingTuesday(rankingForTuesday);

        boolean value = accomMappingService.isSyncRequiredForDefaultConfigChanged(rankingAccomClass);
        Assertions.assertTrue(value);
    }

    @Test
    @DisplayName("Test Sync Flag _ Turned On _ With Update To Default Config _ Webrate Ranking For Wednesday")
    void testSyncFlag_TurnedOn_WithUpdateToDefaultConfig_WebrateRankingForWednesday() {
        WebrateRankingAccomClass rankingAccomClass = UniqueWebrateRankingAccomClassCreator.createWebrateRankingAccomClass();

        tenantCrudService().getEntityManager().flush();
        tenantCrudService().getEntityManager().clear();

        WebrateRanking rankingForWednesday = UniqueWebrateRankingCreator.createWebrateRanking();
        rankingAccomClass.setWebrateRankingWednesday(rankingForWednesday);

        boolean value = accomMappingService.isSyncRequiredForDefaultConfigChanged(rankingAccomClass);
        Assertions.assertTrue(value);
    }

    @Test
    @DisplayName("Test Sync Flag _ Turned On _ With Update To Default Config _ Webrate Ranking For Thursday")
    void testSyncFlag_TurnedOn_WithUpdateToDefaultConfig_WebrateRankingForThursday() {
        WebrateRankingAccomClass rankingAccomClass = UniqueWebrateRankingAccomClassCreator.createWebrateRankingAccomClass();

        tenantCrudService().getEntityManager().flush();
        tenantCrudService().getEntityManager().clear();

        WebrateRanking rankingForThursday = UniqueWebrateRankingCreator.createWebrateRanking();
        rankingAccomClass.setWebrateRankingThursday(rankingForThursday);

        boolean value = accomMappingService.isSyncRequiredForDefaultConfigChanged(rankingAccomClass);
        Assertions.assertTrue(value);
    }

    @Test
    @DisplayName("Test Sync Flag _ Turned On _ With Update To Default Config _ Webrate Ranking For Friday")
    void testSyncFlag_TurnedOn_WithUpdateToDefaultConfig_WebrateRankingForFriday() {
        WebrateRankingAccomClass rankingAccomClass = UniqueWebrateRankingAccomClassCreator.createWebrateRankingAccomClass();

        tenantCrudService().getEntityManager().flush();
        tenantCrudService().getEntityManager().clear();

        WebrateRanking rankingForFriday = UniqueWebrateRankingCreator.createWebrateRanking();
        rankingAccomClass.setWebrateRankingFriday(rankingForFriday);

        boolean value = accomMappingService.isSyncRequiredForDefaultConfigChanged(rankingAccomClass);
        Assertions.assertTrue(value);
    }

    @Test
    @DisplayName("Test Sync Flag _ Turned On _ With Update To Default Config _ Webrate Ranking For Saturday")
    void testSyncFlag_TurnedOn_WithUpdateToDefaultConfig_WebrateRankingForSaturday() {
        WebrateRankingAccomClass rankingAccomClass = UniqueWebrateRankingAccomClassCreator.createWebrateRankingAccomClass();

        tenantCrudService().getEntityManager().flush();
        tenantCrudService().getEntityManager().clear();

        WebrateRanking rankingForSaturday = UniqueWebrateRankingCreator.createWebrateRanking();
        rankingAccomClass.setWebrateRankingSaturday(rankingForSaturday);

        boolean value = accomMappingService.isSyncRequiredForDefaultConfigChanged(rankingAccomClass);
        Assertions.assertTrue(value);
    }

    @Test
    @DisplayName("Should Clean Up Webrate Accomtypes")
    void shouldCleanUpWebrateAccomtypes() {
        List<WebrateAccomType> webrateAccomTypeList = new ArrayList<>();
        WebrateAccomType webrateAccomType = new WebrateAccomType();
        WebrateAccomClassMapping webrateAccomClassMapping = new WebrateAccomClassMapping();
        Set<WebrateAccomClassMapping> webrateAccomClassMappings = new HashSet<>();
        webrateAccomClassMappings.add(webrateAccomClassMapping);
        webrateAccomType.setWebrateAccomClassMappings(webrateAccomClassMappings);
        webrateAccomType.setShouldDelete(true);
        webrateAccomTypeList.add(webrateAccomType);

        boolean value = accomMappingService.saveAccomMapping(webrateAccomTypeList, new ArrayList<>(), 5, "1");
        Assertions.assertTrue(value);
        verify(webrateShoppingCleanUpService).cleanUpWebrateAccomTypes(webrateAccomTypeList);
    }

    @Test
    @DisplayName("Save Accom Mapping _ set Default Webrate Accom Alias")
    void saveAccomMapping_setDefaultWebrateAccomAlias() {
        List<WebrateAccomType> webrateAccomTypeList = new ArrayList<>();
        WebrateAccomType webrateAccomType = UniqueWebrateAccomTypeCreator.createWebrateAccomType();
        webrateAccomType.setWebrateAccomAlias("");
        webrateAccomTypeList.add(webrateAccomType);

        boolean value = accomMappingService.saveAccomMapping(webrateAccomTypeList, new ArrayList<>(), 5, "1");
        Assertions.assertTrue(value);
        WebrateAccomType savedWebrateAccomType = tenantCrudService().find(WebrateAccomType.class, webrateAccomType.getId());
        assertThat(savedWebrateAccomType.getWebrateAccomAlias(), is("Display Name"));
    }

    @Test
    @DisplayName("Save Webrate Ranking Accom Class Override _ Without Any Change _ Sync Not Required")
    void saveWebrateRankingAccomClassOverride_WithoutAnyChange_SyncNotRequired() {
        WebrateRankingAccomClassOverride webrateRankingAccomClassOverride = UniqueWebrateRankingAccomClassCreator.createWebrateRankingAccomClassOverride();
        WebrateRanking webrateRanking = UniqueWebrateRankingCreator.createWebrateRanking();
        String webrateRankingNameAfter = webrateRanking.getWebrateRankingName();
        webrateRankingAccomClassOverride.setWebrateRankingOvrSunday(webrateRanking);

        WebrateRankingAccomClassOverride retValue = accomMappingService.saveWebrateRankingAccomClassOverride(webrateRankingAccomClassOverride);
        Assertions.assertEquals(webrateRankingNameAfter, retValue.getWebrateRankingOvrSunday().getWebrateRankingName());
    }

    @Test
    @DisplayName("Save Webrate Ranking Accom Class Override _ New _ Sync Required")
    void saveWebrateRankingAccomClassOverride_New_SyncRequired() {
        WebrateRankingAccomClassOverride rankingAccomClassOvr = new WebrateRankingAccomClassOverride();
        int uniqueInt = new Random().nextInt();
        AccomClass accomClass = new AccomClass();
        accomClass.setCode("Accom Code" + uniqueInt);
        accomClass.setDescription("Accom Description");
        accomClass.setName("Accom Name" + uniqueInt);
        accomClass.setPropertyId(UniqueTenantPropertyCreator.getTenantProperty().getId());
        accomClass.setSystemDefault(0);
        accomClass.setStatusId(ACTIVE_STATUS_ID);
        AccomClass persistedAccomClass = CrudServiceBeanExtension.getTenantCrudService().save(accomClass);

        rankingAccomClassOvr.setAccomClass(persistedAccomClass);
        rankingAccomClassOvr.setProductID(1);
        rankingAccomClassOvr.setWebrateRankingStartDT(new Date());
        rankingAccomClassOvr.setWebrateRankingEndDT(new Date());
        rankingAccomClassOvr.setWebrateRankingOvrMonday(UniqueWebrateRankingCreator.createWebrateRanking());
        rankingAccomClassOvr.setWebrateRankingOvrTuesday(UniqueWebrateRankingCreator.createWebrateRanking());
        rankingAccomClassOvr.setWebrateRankingOvrWednesday(UniqueWebrateRankingCreator.createWebrateRanking());
        rankingAccomClassOvr.setWebrateRankingOvrThursday(UniqueWebrateRankingCreator.createWebrateRanking());
        rankingAccomClassOvr.setWebrateRankingOvrFriday(UniqueWebrateRankingCreator.createWebrateRanking());
        rankingAccomClassOvr.setWebrateRankingOvrSaturday(UniqueWebrateRankingCreator.createWebrateRanking());
        rankingAccomClassOvr.setWebrateRankingOvrSunday(UniqueWebrateRankingCreator.createWebrateRanking());

        WebrateRankingAccomClassOverride retValue = accomMappingService.saveWebrateRankingAccomClassOverride(rankingAccomClassOvr);
        Assertions.assertNotNull(retValue.getId());
    }

    @Test
    @DisplayName("Get Webrate Ranking Accom Class For Property")
    void getWebrateRankingAccomClassForProperty() {
        final ArrayList<AccomClass> accomClasses = new ArrayList<>();
        final WebrateRankingAccomClass webrateRankingAccomClass = UniqueWebrateRankingAccomClassCreator.createWebrateRankingAccomClass();
        accomClasses.add(webrateRankingAccomClass.getAccomClass());
        when(accommodation2Service.getAccomClassesByViewOrder()).thenReturn(accomClasses);
        accomMappingService.getWebrateRankingAccomClassForProperty(5);
    }

    @Test
    @DisplayName("Test Update New Competitor Status _ When Demand Enabled")
    void testUpdateNewCompetitorStatus_WhenDemandEnabled() {
        setUpDataForNewCompetitorWithRoomClasses(1, 0).runActualServiceToUpdateCompetitor().assertWithExpectedStatusOfCompetitor(NEW_STATUS_ID);
    }

    @Test
    @DisplayName("Test Update New Competitor Status _ When Ranking Enabled")
    void testUpdateNewCompetitorStatus_WhenRankingEnabled() {
        setUpDataForNewCompetitorWithRoomClasses(0, 1).runActualServiceToUpdateCompetitor().assertWithExpectedStatusOfCompetitor(NEW_STATUS_ID);
    }

    @Test
    @DisplayName("Test Update New Competitor Status _ When No Childs Enabled")
    void testUpdateNewCompetitorStatus_WhenNoChildsEnabled() {
        setUpDataForNewCompetitorWithRoomClasses(0, 0).runActualServiceToUpdateCompetitor().assertWithExpectedStatusOfCompetitor(NEW_STATUS_ID);
    }

    @Test
    @DisplayName("Test Update Competitor Status _ When Demand Enabled")
    void testUpdateCompetitorStatus_WhenDemandEnabled() {
        setUpDataForCompetitorWithRoomClasses(1, 0).runActualServiceToUpdateCompetitor().assertWithExpectedStatusOfCompetitor(ACTIVE_STATUS_ID);
    }

    @Test
    @DisplayName("Test Update Competitor Status _ When Ranking Enabled")
    void testUpdateCompetitorStatus_WhenRankingEnabled() {
        setUpDataForCompetitorWithRoomClasses(0, 1).runActualServiceToUpdateCompetitor().assertWithExpectedStatusOfCompetitor(ACTIVE_STATUS_ID);
    }

    @Test
    @DisplayName("Test Update Competitor Status _ When No Childs Enabled")
    void testUpdateCompetitorStatus_WhenNoChildsEnabled() {
        setUpDataForCompetitorWithRoomClasses(0, 0).runActualServiceToUpdateCompetitor().assertWithExpectedStatusOfCompetitor(INACTIVE_STATUS_ID);
    }

    @Test
    @DisplayName("Test Update Competitor Status _ When No Child Associated")
    void testUpdateCompetitorStatus_WhenNoChildAssociated() {
        setupDataForHotelIdAndWebrateCompetitor(WEB_RATE_HOTEL_ID).runActualServiceToUpdateCompetitor().assertWithExpectedStatusOfCompetitor(INACTIVE_STATUS_ID);
    }

    @Test
    @DisplayName("Test Update Competitor With New Accom Class _ When Self Competitor")
    void testUpdateCompetitorWithNewAccomClass_WhenSelfCompetitor() {
        setupDataForHotelIdAndWebrateCompetitor(WEB_RATE_HOTEL_ID).runActualServiceToUpdateCompetitorAndRoomClasses().assertWithExpectedStatusOfDemandAndRanking(ACTIVE_STATUS_ID);
    }

    @Test
    @DisplayName("Test Update Competitor With New Accom Class _ When No Self Competitor")
    void testUpdateCompetitorWithNewAccomClass_WhenNoSelfCompetitor() {
        setupDataForHotelIdAndWebrateCompetitor(TEST).runActualServiceToUpdateCompetitorAndRoomClasses().assertWithExpectedStatusOfDemandAndRanking(ZERO);
    }

    @Test
    @DisplayName("Test Update Competitor With New Accom Class _ When RDL Self Competitor")
    void testUpdateCompetitorWithNewAccomClass_WhenRDLSelfCompetitor() {
        setupProperty(TEST_UPS_ID).setupDataForRDLUpsIdAndWebrateCompetitor(TEST_UPS_ID).runActualServiceToUpdateCompetitorAndRoomClasses().assertWithExpectedStatusOfDemandAndRanking(ACTIVE_STATUS_ID);
    }

    @Test
    @DisplayName("Test Update Competitor With New Accom Class _ When No RDL Self Competitor")
    void testUpdateCompetitorWithNewAccomClass_WhenNoRDLSelfCompetitor() {
        setupProperty(TEST_UPS_ID).setupDataForRDLUpsIdAndWebrateCompetitor(TEST).runActualServiceToUpdateCompetitorAndRoomClasses().assertWithExpectedStatusOfDemandAndRanking(ZERO);
    }

    @Test
    @DisplayName("Test Update Competitor With New Accom Class _ When No RDL Self Competitor _ with Missing Ups Id")
    void testUpdateCompetitorWithNewAccomClass_WhenNoRDLSelfCompetitor_withMissingUpsId() {
        setupProperty(null).setupDataForRDLUpsIdAndWebrateCompetitor(TEST).runActualServiceToUpdateCompetitorAndRoomClasses().assertWithExpectedStatusOfDemandAndRanking(ZERO);
    }

    @Test
    @DisplayName("Get Average Rates For Webrate Accom Types")
    void getAverageRatesForWebrateAccomTypes() {
        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(CAUGHTUP_DATE);
        tenantCrudService().executeUpdateByNativeQuery("update Webrate set Webrate_Status = 'C'");
        tenantCrudService().save(createWebrateData());

        List<WebrateAccomTypeAverageRateDTO> results = accomMappingService.getAverageRatesForWebrateAccomTypes();
        Assertions.assertEquals(3, results.size());
        Assertions.assertEquals(1, results.get(0).getWebrateAccomTypeId());
        Assertions.assertEquals(2, results.get(1).getWebrateAccomTypeId());
        Assertions.assertEquals(3, results.get(2).getWebrateAccomTypeId());
        Assertions.assertEquals(new BigDecimal("130.00"), results.get(0).getAverageRate());
        Assertions.assertEquals(new BigDecimal("160.00"), results.get(1).getAverageRate());
        Assertions.assertEquals(new BigDecimal("115.00"), results.get(2).getAverageRate());
    }

    private List<Webrate> createWebrateData() {
        ArrayList<Webrate> webrates = new ArrayList<>();
        for (int i = 1; i <= 5; i++) {
            webrates.add(UniqueWebRateCreator.createWebrate(UniqueWebrateSourcePropertyCreator.createWebrateSourceProperty().getId(), 1, 1, 1, 1, AccommodationMappingServiceTest.CAUGHTUP_DATE.minusDays(i - 1).toString(), 100.00 + 10 * i));
            webrates.add(UniqueWebRateCreator.createWebrate(UniqueWebrateSourcePropertyCreator.createWebrateSourceProperty().getId(), 1, 1, 2, 1, AccommodationMappingServiceTest.CAUGHTUP_DATE.minusDays(i - 1).toString(), 100.00 + 20 * i));
            webrates.add(UniqueWebRateCreator.createWebrate(UniqueWebrateSourcePropertyCreator.createWebrateSourceProperty().getId(), 1, 1, 3, 1, AccommodationMappingServiceTest.CAUGHTUP_DATE.minusDays(i - 1).toString(), 100.00 + 5 * i));
        }
        return webrates;
    }

    @Test
    @DisplayName("Get ADR By Accom Classes")
    void getADRByAccomClasses() {
        tenantCrudService().executeUpdateByNativeQuery("UPDATE Mkt_Seg_Details SET Priced_By_BAR = 1 WHERE Mkt_Seg_ID = 12");
        tenantCrudService().executeUpdateByNativeQuery("UPDATE Mkt_Accom_Activity SET Room_Revenue = 500, Rooms_Sold = 20 WHERE Accom_Type_ID = 4");
        tenantCrudService().executeUpdateByNativeQuery("UPDATE Mkt_Accom_Activity SET Room_Revenue = 400, Rooms_Sold = 20 WHERE Accom_Type_ID = 5");
        tenantCrudService().executeUpdateByNativeQuery("UPDATE Mkt_Accom_Activity SET Room_Revenue = 300, Rooms_Sold = 20 WHERE Accom_Type_ID IN (6, 7, 8)");
        LocalDate maxOccupancyDate = DateUtil.convertJavaUtilDateToLocalDate(tenantCrudService().findByNamedQuerySingleResult(MktSegAccomActivity.MAX_OCCUPANCY_DATE));
        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(maxOccupancyDate);
        List<WebrateAccomTypeAverageRateDTO> results = accomMappingService.getADRByAccomClasses();
        Assertions.assertFalse(results.isEmpty());
        Assertions.assertEquals(2, results.get(0).getWebrateAccomTypeId());
        Assertions.assertEquals(4, results.get(1).getWebrateAccomTypeId());
        Assertions.assertEquals(3, results.get(2).getWebrateAccomTypeId());
        Assertions.assertEquals(new BigDecimal("15.00"), results.get(0).getAverageRate());
        Assertions.assertEquals(new BigDecimal("20.00"), results.get(1).getAverageRate());
        Assertions.assertEquals(new BigDecimal("25.00"), results.get(2).getAverageRate());
    }

    @Test
    @DisplayName("Straight Bar Market Segments Are Not Present")
    void straightBarMarketSegmentsAreNotPresent() {
        tenantCrudService().executeUpdateByNativeQuery("UPDATE Mkt_Seg_Details SET Priced_By_BAR = 0");
        List<Integer> straightBarMarketSegmentIds = accomMappingService.getStraightBarMarketSegments();
        assertTrue(straightBarMarketSegmentIds.isEmpty());
        assertFalse(accomMappingService.straightBarMarketSegmentsArePresent());
    }

    @Test
    @DisplayName("Straight Bar Market Segments Are Present In Details")
    void straightBarMarketSegmentsArePresentInDetails() {
        tenantCrudService().executeUpdateByNativeQuery("UPDATE Mkt_Seg_Details SET Priced_By_BAR = 1 WHERE Mkt_Seg_ID = 12");
        List<Integer> straightBarMarketSegmentIds = accomMappingService.getStraightBarMarketSegments();
        assertEquals(12, straightBarMarketSegmentIds.get(0));
        assertTrue(accomMappingService.straightBarMarketSegmentsArePresent());
    }

    @Test
    @DisplayName("Property Is Not Limited Data Build")
    void propertyIsNotLimitedDataBuild() {
        when(configParamsService.getBooleanParameterValue(IPConfigParamName.CORE_LIMITED_DATA_BUILD_ENABLED)).thenReturn(false);
        assertTrue(accomMappingService.propertyIsNotLimitedDataBuild());
        when(configParamsService.getBooleanParameterValue(IPConfigParamName.CORE_LIMITED_DATA_BUILD_ENABLED)).thenReturn(true);
        assertFalse(accomMappingService.propertyIsNotLimitedDataBuild());
    }

    @Test
    @DisplayName("Get Active Non Default Accom Classes")
    void getActiveNonDefaultAccomClasses() {
        List<AccomClass> accomClasses = createAccomClasses();
        when(accommodation2Service.getActiveNonDefaultAccomClasses()).thenReturn(accomClasses);
        List<AccomClass> actual = accomMappingService.getActiveNonDefaultAccomClasses();
        assertEquals(5, actual.size());
    }

    @Test
    @DisplayName("is WebrateAccomType Cleanup Job Running")
    void isWebrateAccomTypeCleanupJobRunning() {
        accomMappingService.isWebrateAccomTypeCleanupJobRunning();
        verify(webrateShoppingDataService).isWebrateCleanupJobRunningOrWebrateSourceUpdateJobRunning(JobName.WebrateAccomTypeCleanupJob);
    }

    @Test
    @DisplayName("Is Rate Shopping Auto Configuration Allowed _ RDL is enabled")
    void isRateShoppingAutoConfigurationAllowed_RDLToggelIsEnabled() {
        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.IS_RDL_ENABLED)).thenReturn(true);
        assertFalse(accomMappingService.isRateShoppingAutoConfigurationAllowed());
    }

    @Test
    @DisplayName("Is Rate Shopping Auto Configuration Allowed _ all Accom Types Are Not Mapped")
    void isRateShoppingAutoConfigurationAllowed_allAccomTypesAreNotMapped() {
        CrudService crudService = mock(CrudService.class);
        inject(accomMappingService, "crudService", crudService);
        when(crudService.findByNamedQuerySingleResult(WebrateDefaultChannel.BY_PROPERTY_ID_FOR_BAR_ONLY, QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters())).thenReturn(null);
        MktSegDetails ms1 = UniqueMktSegDetailsCreator.createUniqueMktSegDetailsCreator(UniqueMktSegCreator.createUniqueMktSegCreatorFor(PacmanWorkContextHelper.getPropertyId(), "dummy1"), 1);
        when(crudService.findByNamedQuery(MktSegDetails.GET_STRAIGHT_BAR_MS_IDS)).thenReturn(Collections.singletonList(ms1));
        AccomType stdAccomType = new AccomType();
        stdAccomType.setAccomTypeCode("STD");
        AccomType dlxAccomType = new AccomType();
        dlxAccomType.setAccomTypeCode("DLX");
        AccomType steAccomType = new AccomType();
        steAccomType.setAccomTypeCode("STE");
        List<AccomType> accomTypes = Arrays.asList(stdAccomType, dlxAccomType, steAccomType);
        when(crudService.<AccomType>findByNamedQuery(AccomType.ALL_UNASSIGNED_BY_PROPERTY_ID, QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters())).thenReturn(accomTypes);
        assertFalse(accomMappingService.isRateShoppingAutoConfigurationAllowed());
    }

    @Test
    @DisplayName("Is Rate Shopping Auto Configuration Allowed _ property Is Limited Data Build")
    void isRateShoppingAutoConfigurationAllowed_propertyIsLimitedDataBuild() {
        CrudService crudService = mock(CrudService.class);
        inject(accomMappingService, "crudService", crudService);
        when(crudService.findByNamedQuerySingleResult(WebrateDefaultChannel.BY_PROPERTY_ID_FOR_BAR_ONLY, QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters())).thenReturn(null);
        MktSegDetails ms1 = UniqueMktSegDetailsCreator.createUniqueMktSegDetailsCreator(UniqueMktSegCreator.createUniqueMktSegCreatorFor(PacmanWorkContextHelper.getPropertyId(), "dummy1"), 1);
        when(crudService.findByNamedQuery(MktSegDetails.GET_STRAIGHT_BAR_MS_IDS)).thenReturn(Collections.singletonList(ms1));
        when(crudService.<AccomType>findByNamedQuery(AccomType.ALL_UNASSIGNED_BY_PROPERTY_ID, QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters())).thenReturn(null);
        when(configParamsService.getBooleanParameterValue(IPConfigParamName.CORE_LIMITED_DATA_BUILD_ENABLED)).thenReturn(true);
        assertFalse(accomMappingService.isRateShoppingAutoConfigurationAllowed());
    }

    @Test
    @DisplayName("Is Rate Shopping Auto Configuration Allowed")
    void isRateShoppingAutoConfigurationAllowed() {
        CrudService crudService = mock(CrudService.class);
        inject(accomMappingService, "crudService", crudService);
        when(crudService.findByNamedQuerySingleResult(WebrateDefaultChannel.BY_PROPERTY_ID_FOR_BAR_ONLY, QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters())).thenReturn(null);
        MktSegDetails ms1 = UniqueMktSegDetailsCreator.createUniqueMktSegDetailsCreator(UniqueMktSegCreator.createUniqueMktSegCreatorFor(PacmanWorkContextHelper.getPropertyId(), "dummy1"), 1);
        when(crudService.findByNamedQuery(MktSegDetails.GET_STRAIGHT_BAR_MS_IDS)).thenReturn(Collections.singletonList(ms1));
        when(crudService.<AccomType>findByNamedQuery(AccomType.ALL_UNASSIGNED_BY_PROPERTY_ID, QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters())).thenReturn(null);
        when(configParamsService.getBooleanParameterValue(IPConfigParamName.CORE_LIMITED_DATA_BUILD_ENABLED)).thenReturn(false);
        assertTrue(accomMappingService.isRateShoppingAutoConfigurationAllowed());
    }

    @Test
    @DisplayName("Update Or Delete Accomodation Mapping")
    void updateOrDeleteAccomodationMapping() {
        CrudService crudService = mock(CrudService.class);
        inject(accomMappingService, "crudService", crudService);
        WebrateAccomClassMappingDTO webrateAccomClassMappingDTO = new WebrateAccomClassMappingDTO();
        AccomClass accomClass = new AccomClass();
        accomClass.setId(1);
        accomClass.setCode("accomClass");
        accomClass.setName("accomClass");
        webrateAccomClassMappingDTO.setAccomClass(accomClass);
        webrateAccomClassMappingDTO.setShouldDelete(true);
        webrateAccomClassMappingDTO.setWebrateAccomTypeId(1);
        WebrateAccomClassMapping webrateAccomClassMapping = getWebrateAccomClassMapping();
        when(crudService.findByNamedQuery(WebrateAccomClassMapping.BY_ACCOMTYPE_ID, QueryParameter.with("accomTypeId", 1).parameters())).thenReturn(Collections.singletonList(webrateAccomClassMapping));
        List<WebrateAccomClassMappingDTO> accomMappings = Collections.singletonList(webrateAccomClassMappingDTO);

        WebrateAccomType webrateAccomType = new WebrateAccomType();
        webrateAccomType.setId(1);
        webrateAccomType.setPropertyId(PacmanWorkContextHelper.getPropertyId());
        webrateAccomType.setWebrateAccomAlias("Accom Type Alias");
        webrateAccomType.setWebrateAccomName("test");
        webrateAccomType.setWebrateAccomClassMappings(Set.of(webrateAccomClassMapping));
        when(crudService.findByNamedQuerySingleResult(WebrateAccomType.BY_ID, QueryParameter.with("id", 1).parameters())).thenReturn(webrateAccomType);
        when(crudService.findByNamedQuery(WebrateAccomType.BY_PROPERTY_ID, QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters())).thenReturn(Collections.singletonList(webrateAccomType));
        accomMappingService.updateOrDeleteAccomodationMapping(accomMappings);
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_CPC_NOTIFICATION_BY_ROOM_CLASS)).thenReturn(true);
        verify(webrateShoppingCleanUpService).cleanUpWebrateAccomTypes(Collections.singletonList(webrateAccomType));

        verify(crudService, never()).executeUpdateByNamedQuery(any(String.class), anyMap());
        verify(crudService, never()).delete(any());
    }

    @Test
    @DisplayName("Update Or Delete Accommodation Mapping")
    void updateOrDeleteMultipleAccommodationMapping() {
        CrudService crudService = mock(CrudService.class);
        inject(accomMappingService, "crudService", crudService);
        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.IS_MAP_ACCOM_TYPE_TO_MULTIPLE_ACCOM_CLASSES_ENABLED)).thenReturn(true);
        WebrateAccomClassMappingDTO webrateAccomClassMappingDTO = new WebrateAccomClassMappingDTO();
        AccomClass accomClass = new AccomClass();
        accomClass.setId(1);
        accomClass.setCode("STD");
        accomClass.setName("STD");
        AccomClass dlxAccomClass = new AccomClass();
        dlxAccomClass.setId(2);
        dlxAccomClass.setCode("DLX");
        dlxAccomClass.setName("DLX");
        webrateAccomClassMappingDTO.setAccomClasses(Set.of(accomClass, dlxAccomClass));
        webrateAccomClassMappingDTO.setShouldDelete(false);
        webrateAccomClassMappingDTO.setWebrateAccomTypeId(1);
        WebrateAccomClassMapping webrateAccomClassMapping = getWebrateAccomClassMapping();
        when(crudService.findByNamedQuery(WebrateAccomClassMapping.BY_ACCOMTYPE_ID, QueryParameter.with("accomTypeId", 1).parameters())).thenReturn(Collections.singletonList(webrateAccomClassMapping));
        List<WebrateAccomClassMappingDTO> accomMappings = Collections.singletonList(webrateAccomClassMappingDTO);

        WebrateAccomType webrateAccomType = new WebrateAccomType();
        webrateAccomType.setId(1);
        webrateAccomType.setPropertyId(PacmanWorkContextHelper.getPropertyId());
        webrateAccomType.setWebrateAccomAlias("Accom Type Alias");
        webrateAccomType.setWebrateAccomName("test");
        webrateAccomType.setWebrateAccomClassMappings(Set.of(webrateAccomClassMapping));
        webrateAccomType.setShouldDelete(false);
        when(crudService.findByNamedQuerySingleResult(WebrateAccomType.BY_ID, QueryParameter.with("id", 1).parameters())).thenReturn(webrateAccomType);
        when(crudService.findByNamedQuery(WebrateAccomType.BY_PROPERTY_ID, QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters())).thenReturn(Collections.singletonList(webrateAccomType));
        accomMappingService.updateOrDeleteAccomodationMapping(accomMappings);
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_CPC_NOTIFICATION_BY_ROOM_CLASS)).thenReturn(true);

        verify(crudService, times(1)).save(any(WebrateAccomClassMapping.class));
        verify(crudService, never()).delete(any());
    }

    @Test
    @DisplayName("Test Delete webrate Accom Types")
    void TestDeleteWebrateAccomTypes() {
        CrudService crudService = mock(CrudService.class);
        inject(accomMappingService, "crudService", crudService);
        List<String> accomTypesToBeDeleted = Arrays.asList("STD", " STD", "STD ", " STD ", null);
        WebrateAccomClassMapping webrateAccomClassMapping = getWebrateAccomClassMapping();
        WebrateAccomType webrateAccomType = new WebrateAccomType();
        webrateAccomType.setId(1);
        webrateAccomType.setPropertyId(PacmanWorkContextHelper.getPropertyId());
        webrateAccomType.setWebrateAccomAlias("Accom Type Alias");
        webrateAccomType.setWebrateAccomName("STD");
        webrateAccomType.setWebrateAccomClassMappings(Set.of(webrateAccomClassMapping));
        when(crudService.findByNamedQuery(WebrateAccomType.BY_PROPERTY_ID_AND_ACCOM_NAMES,
                QueryParameter.with(PARAM_PROPERTY_ID, 5)
                        .and(PARAM_ACCOM_NAMES, Set.of("STD"))
                        .parameters()))
                .thenReturn(Collections.singletonList(webrateAccomType));
        accomMappingService.deleteWebrateAccomTypes(accomTypesToBeDeleted);
        verify(crudService, times(1)).delete(any());
        verify(webrateShoppingCleanUpService).cleanUpWebrateAccomTypes(Collections.singletonList(webrateAccomType));
    }

    private WebrateAccomClassMapping getWebrateAccomClassMapping() {
        WebrateAccomClassMapping webrateAccomClassMapping = new WebrateAccomClassMapping();
        webrateAccomClassMapping.setAccomClass(tenantCrudService().find(AccomClass.class, 1));
        webrateAccomClassMapping.setWebrateAccomType(tenantCrudService().find(WebrateAccomType.class, 1));
        webrateAccomClassMapping.setCreatedByUserId(SYSTEM_USER_ID);
        webrateAccomClassMapping.setLastUpdatedByUserId(SYSTEM_USER_ID);
        return webrateAccomClassMapping;
    }

    private WebrateDefaultChannel getDeafultWebrateChannel() {
        WebrateDefaultChannel webrateDefaultChannel = new WebrateDefaultChannel();
        webrateDefaultChannel.setPropertyId(TestProperty.H1.getId());
        return webrateDefaultChannel;
    }

    @Test
    @DisplayName("Straight Bar Market Segments Are Present In Proposed")
    void straightBarMarketSegmentsArePresentInProposed() {
        tenantCrudService().executeUpdateByNativeQuery("INSERT INTO Mkt_Seg_Details_Proposed " + "(Mkt_Seg_ID,Business_Type_ID,Yield_Type_ID,Forecast_Activity_Type_ID,Qualified,Booking_Block_Pc,Fenced,Package,Link,Template_ID,Template_Default,Process_Status_ID,Offset_Type_ID,Offset_Value,Status_ID,Last_Updated_DTTM,Priced_By_BAR,Created_By_User_ID,Created_DTTM,Last_Updated_By_User_ID) VALUES " + "(12,2,1,1,0,0,1,0,0,0,0,10,NULL,NULL,1,getdate(),1,11403,getdate(),11403)");
        List<Integer> straightBarMarketSegmentIds = accomMappingService.getStraightBarMarketSegments();
        assertEquals(12, straightBarMarketSegmentIds.get(0));
    }

    @Test
    @DisplayName("Auto Configure Default Channel _ Independent Product Disabled")
    void autoConfigureDefaultChannel_IndependentProductDisabled() {
        Product barProduct = createBarProduct();
        Product independentProduct = createIndependentProduct();
        doNothing().when(webrateShoppingDataService).updateAllNewStatusToActive(WebrateChannel.UPDATE_ALL_NEW_TO_ACTIVE_BY_PROPERTY);
        doReturn(true).when(defaultReferenceChannelService).saveDefaultChannel(any(WebrateDefaultChannel.class));
        when(webrateShoppingDataService.getAllChannelsByProperty()).thenReturn(getWebrateChannels());
        when(agileRatesConfigurationService.findAgileAndSystemDefaultProductsAndIndependentProducts()).thenReturn(Arrays.asList(barProduct, independentProduct));
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.INDEPENDENT_PRODUCTS_ENABLED)).thenReturn(false);
        accomMappingService.autoConfigureDefaultChannel();

        ArgumentCaptor<List> defaultChannelCaptor = ArgumentCaptor.forClass(List.class);
        verify(defaultReferenceChannelService).saveDefaultChannels(defaultChannelCaptor.capture());
        assertEquals(1, defaultChannelCaptor.getValue().size());
        WebrateDefaultChannel result = (WebrateDefaultChannel) defaultChannelCaptor.getValue().get(0);
        assertEquals(barProduct.getId(), result.getProductID());
        assertEquals(5, result.getWebrateChannelMon().getId());
        assertEquals(5, result.getWebrateChannelTues().getId());
        assertEquals(5, result.getWebrateChannelWed().getId());
        assertEquals(5, result.getWebrateChannelThurs().getId());
        assertEquals(5, result.getWebrateChannelFri().getId());
        assertEquals(5, result.getWebrateChannelSat().getId());
        assertEquals(5, result.getWebrateChannelSun().getId());
    }

    @Test
    @DisplayName("Auto Configure Default Channel _ Independent Product Enabled")
    void autoConfigureDefaultChannel_IndependentProductEnabled() {
        Product barProduct = createBarProduct();
        Product independentProduct = createIndependentProduct();
        doNothing().when(webrateShoppingDataService).updateAllNewStatusToActive(WebrateChannel.UPDATE_ALL_NEW_TO_ACTIVE_BY_PROPERTY);
        doReturn(true).when(defaultReferenceChannelService).saveDefaultChannel(any(WebrateDefaultChannel.class));
        when(webrateShoppingDataService.getAllChannelsByProperty()).thenReturn(getWebrateChannels());
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.INDEPENDENT_PRODUCTS_ENABLED)).thenReturn(true);
        when(agileRatesConfigurationService.findAgileAndSystemDefaultProductsAndIndependentProducts()).thenReturn(Arrays.asList(barProduct, independentProduct));
        accomMappingService.autoConfigureDefaultChannel();

        ArgumentCaptor<List> defaultChannelCaptor = ArgumentCaptor.forClass(List.class);
        verify(defaultReferenceChannelService).saveDefaultChannels(defaultChannelCaptor.capture());
        WebrateDefaultChannel result1 = (WebrateDefaultChannel) defaultChannelCaptor.getValue().get(0);
        assertEquals(2, defaultChannelCaptor.getValue().size());
        assertEquals(barProduct.getId(), result1.getProductID());
        assertEquals(5, result1.getWebrateChannelMon().getId());
        assertEquals(5, result1.getWebrateChannelTues().getId());
        assertEquals(5, result1.getWebrateChannelWed().getId());
        assertEquals(5, result1.getWebrateChannelThurs().getId());
        assertEquals(5, result1.getWebrateChannelFri().getId());
        assertEquals(5, result1.getWebrateChannelSat().getId());
        assertEquals(5, result1.getWebrateChannelSun().getId());

        WebrateDefaultChannel result2 = (WebrateDefaultChannel) defaultChannelCaptor.getValue().get(1);
        assertEquals(independentProduct.getId(), result2.getProductID());
        assertEquals(5, result2.getWebrateChannelMon().getId());
        assertEquals(5, result2.getWebrateChannelTues().getId());
        assertEquals(5, result2.getWebrateChannelWed().getId());
        assertEquals(5, result2.getWebrateChannelThurs().getId());
        assertEquals(5, result2.getWebrateChannelFri().getId());
        assertEquals(5, result2.getWebrateChannelSat().getId());
        assertEquals(5, result2.getWebrateChannelSun().getId());
    }

    @Test
    @DisplayName("Create Default Channel For Products _ Missing Independent Product")
    void createDefaultChannelForProducts_MissingIndependentProduct() {
        CrudService crudService = mock(CrudService.class);
        inject(accomMappingService, "crudService", crudService);

        Product barProduct = createBarProduct();
        Product independentProduct = createIndependentProduct();

        WebrateChannel webrateChannel = new WebrateChannel();
        webrateChannel.setId(5);
        webrateChannel.setWebrateChannelName("TEST");

        final WebrateDefaultChannel webrateDefaultChannel = getWebrateDefaultChannel(barProduct, webrateChannel);
        when(crudService.findByNamedQuery(WebrateDefaultChannel.ALL_CHANNELS_BY_PROPERTY_ID, with("propertyId", TEST_PROPERTY_ID).parameters())).thenReturn(List.of(webrateDefaultChannel));
        when(crudService.findByNamedQuery(WebrateChannel.BY_PROPERTY_ID, with("propertyId", TEST_PROPERTY_ID).parameters())).thenReturn(List.of(webrateChannel));
        accomMappingService.createDefaultChannelForProducts(Arrays.asList(barProduct, independentProduct));

        ArgumentCaptor<List> defaultChannelCaptor = ArgumentCaptor.forClass(List.class);
        verify(defaultReferenceChannelService).saveDefaultChannels(defaultChannelCaptor.capture());
        WebrateDefaultChannel result1 = (WebrateDefaultChannel) defaultChannelCaptor.getValue().get(0);
        assertEquals(1, defaultChannelCaptor.getValue().size());
        assertEquals(independentProduct.getId(), result1.getProductID());
        assertEquals(5, result1.getWebrateChannelMon().getId());
        assertEquals(5, result1.getWebrateChannelTues().getId());
        assertEquals(5, result1.getWebrateChannelWed().getId());
        assertEquals(5, result1.getWebrateChannelThurs().getId());
        assertEquals(5, result1.getWebrateChannelFri().getId());
        assertEquals(5, result1.getWebrateChannelSat().getId());
        assertEquals(5, result1.getWebrateChannelSun().getId());
    }

    private static WebrateDefaultChannel getWebrateDefaultChannel(Product barProduct, WebrateChannel webrateChannel) {
        WebrateDefaultChannel webrateDefaultChannel = new WebrateDefaultChannel();
        webrateDefaultChannel.setPropertyId(TEST_PROPERTY_ID);
        webrateDefaultChannel.setProductID(barProduct.getId());
        webrateDefaultChannel.setWebrateChannelMon(webrateChannel);
        webrateDefaultChannel.setWebrateChannelTues(webrateChannel);
        webrateDefaultChannel.setWebrateChannelWed(webrateChannel);
        webrateDefaultChannel.setWebrateChannelThurs(webrateChannel);
        webrateDefaultChannel.setWebrateChannelFri(webrateChannel);
        webrateDefaultChannel.setWebrateChannelSat(webrateChannel);
        webrateDefaultChannel.setWebrateChannelSun(webrateChannel);
        return webrateDefaultChannel;
    }

    @Test
    @DisplayName("Create Webrate Comp Accom Mappings _ Existing No Changes _ Independent Products Disabled")
    void createWebrateCompAccomMappings_ExistingNoChanges_IndependentProductsDisabled() {
        CrudService crudService = mock(CrudService.class);
        inject(accomMappingService, "crudService", crudService);

        Product barProduct = createBarProduct();
        Product independentProduct = createIndependentProduct();
        createWebrateCompetitor();

        List<AccomClass> accomClasses = createAccomClasses();
        List<WebrateCompetitorsAccomClass> allWebrateCompetitorsAccomClass = new ArrayList<>();
        WebrateCompetitorsAccomClass webrateCompetitorsAccomClass1 = new WebrateCompetitorsAccomClass();
        webrateCompetitorsAccomClass1.setId(20);
        webrateCompetitorsAccomClass1.setWebrateCompetitor(competitor);
        webrateCompetitorsAccomClass1.setProductID(barProduct.getId());
        webrateCompetitorsAccomClass1.setAccomClass(accomClasses.get(0));
        WebrateCompetitorsAccomClass webrateCompetitorsAccomClass2 = new WebrateCompetitorsAccomClass();
        webrateCompetitorsAccomClass2.setId(21);
        webrateCompetitorsAccomClass2.setWebrateCompetitor(competitor);
        webrateCompetitorsAccomClass2.setProductID(barProduct.getId());
        webrateCompetitorsAccomClass2.setAccomClass(accomClasses.get(1));
        allWebrateCompetitorsAccomClass.add(webrateCompetitorsAccomClass1);
        allWebrateCompetitorsAccomClass.add(webrateCompetitorsAccomClass2);

        competitor.setWebrateCompetitorsAccomClasses(new HashSet<>(allWebrateCompetitorsAccomClass));

        when(configParamsService.getParameterValue(IPConfigParamName.BAR_WEB_RATE_ALIAS.value())).thenReturn(WEB_RATE_HOTEL_ID);
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.INDEPENDENT_PRODUCTS_ENABLED)).thenReturn(false);
        when(agileRatesConfigurationService.findAgileAndSystemDefaultProductsAndIndependentProducts()).thenReturn(Arrays.asList(barProduct, independentProduct));

        accomMappingService.createWebrateCompAccomMappings(allWebrateCompetitorsAccomClass, competitor, Arrays.asList(accomClasses.get(0), accomClasses.get(1)));
        verify(crudService, times(1)).save(any(WebrateCompetitors.class));
        ArrayList<WebrateCompetitorsAccomClass> result = new ArrayList<>(competitor.getWebrateCompetitorsAccomClasses());
        assertEquals(2, result.size());
        assertEquals(webrateCompetitorsAccomClass1, result.get(0));
        assertEquals(webrateCompetitorsAccomClass2, result.get(1));
    }

    @Test
    @DisplayName("Create Webrate Comp Accom Mappings _ No Existing Changes _ Independent Products Disabled")
    void createWebrateCompAccomMappings_NoExistingChanges_IndependentProductsDisabled() {
        CrudService crudService = mock(CrudService.class);
        inject(accomMappingService, "crudService", crudService);

        Product barProduct = createBarProduct();
        Product independentProduct = createIndependentProduct();
        createWebrateCompetitor();

        List<AccomClass> accomClasses = createAccomClasses();
        List<WebrateCompetitorsAccomClass> allWebrateCompetitorsAccomClass = new ArrayList<>();
        WebrateCompetitorsAccomClass webrateCompetitorsAccomClass1 = new WebrateCompetitorsAccomClass();
        webrateCompetitorsAccomClass1.setId(20);
        webrateCompetitorsAccomClass1.setWebrateCompetitor(competitor);
        webrateCompetitorsAccomClass1.setProductID(barProduct.getId());
        webrateCompetitorsAccomClass1.setAccomClass(accomClasses.get(0));
        WebrateCompetitorsAccomClass webrateCompetitorsAccomClass2 = new WebrateCompetitorsAccomClass();
        webrateCompetitorsAccomClass2.setId(21);
        webrateCompetitorsAccomClass2.setWebrateCompetitor(competitor);
        webrateCompetitorsAccomClass2.setProductID(barProduct.getId());
        webrateCompetitorsAccomClass2.setAccomClass(accomClasses.get(1));
        allWebrateCompetitorsAccomClass.add(webrateCompetitorsAccomClass1);
        allWebrateCompetitorsAccomClass.add(webrateCompetitorsAccomClass2);

        competitor.setWebrateCompetitorsAccomClasses(new HashSet<>(allWebrateCompetitorsAccomClass));

        when(configParamsService.getParameterValue(IPConfigParamName.BAR_WEB_RATE_ALIAS.value())).thenReturn(WEB_RATE_HOTEL_ID);
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.INDEPENDENT_PRODUCTS_ENABLED)).thenReturn(false);
        when(agileRatesConfigurationService.findAgileAndSystemDefaultProductsAndIndependentProducts()).thenReturn(Arrays.asList(barProduct, independentProduct));

        accomMappingService.createWebrateCompAccomMappings(allWebrateCompetitorsAccomClass, competitor, Arrays.asList(accomClasses.get(0), accomClasses.get(1), accomClasses.get(2)));
        verify(crudService).save(competitor);
        ArrayList<WebrateCompetitorsAccomClass> result = new ArrayList<>(competitor.getWebrateCompetitorsAccomClasses());
        assertEquals(3, result.size());
        assertEquals(accomClasses.get(2), result.get(0).getAccomClass());
        assertEquals(barProduct.getId(), result.get(0).getProductID());
        assertEquals(competitor, result.get(0).getWebrateCompetitor());
        assertEquals(webrateCompetitorsAccomClass1, result.get(1));
        assertEquals(webrateCompetitorsAccomClass2, result.get(2));

    }

    @Test
    @DisplayName("Create Webrate Comp Accom Mappings _ Existing No Changes _ Independent Products Enabled")
    void createWebrateCompAccomMappings_ExistingNoChanges_IndependentProductsEnabled() {
        CrudService crudService = mock(CrudService.class);
        inject(accomMappingService, "crudService", crudService);

        Product barProduct = createBarProduct();
        Product independentProduct = createIndependentProduct();
        createWebrateCompetitor();

        List<AccomClass> accomClasses = createAccomClasses();
        List<WebrateCompetitorsAccomClass> allWebrateCompetitorsAccomClass = new ArrayList<>();
        WebrateCompetitorsAccomClass webrateCompetitorsAccomClass1 = new WebrateCompetitorsAccomClass();
        webrateCompetitorsAccomClass1.setId(20);
        webrateCompetitorsAccomClass1.setWebrateCompetitor(competitor);
        webrateCompetitorsAccomClass1.setProductID(barProduct.getId());
        webrateCompetitorsAccomClass1.setAccomClass(accomClasses.get(0));
        WebrateCompetitorsAccomClass webrateCompetitorsAccomClass2 = new WebrateCompetitorsAccomClass();
        webrateCompetitorsAccomClass2.setId(21);
        webrateCompetitorsAccomClass2.setWebrateCompetitor(competitor);
        webrateCompetitorsAccomClass2.setProductID(barProduct.getId());
        webrateCompetitorsAccomClass2.setAccomClass(accomClasses.get(1));
        WebrateCompetitorsAccomClass webrateCompetitorsAccomClass3 = new WebrateCompetitorsAccomClass();
        webrateCompetitorsAccomClass3.setId(22);
        webrateCompetitorsAccomClass3.setWebrateCompetitor(competitor);
        webrateCompetitorsAccomClass3.setProductID(independentProduct.getId());
        webrateCompetitorsAccomClass3.setAccomClass(accomClasses.get(0));
        WebrateCompetitorsAccomClass webrateCompetitorsAccomClass4 = new WebrateCompetitorsAccomClass();
        webrateCompetitorsAccomClass4.setId(23);
        webrateCompetitorsAccomClass4.setWebrateCompetitor(competitor);
        webrateCompetitorsAccomClass4.setProductID(independentProduct.getId());
        webrateCompetitorsAccomClass4.setAccomClass(accomClasses.get(1));
        allWebrateCompetitorsAccomClass.add(webrateCompetitorsAccomClass1);
        allWebrateCompetitorsAccomClass.add(webrateCompetitorsAccomClass2);
        allWebrateCompetitorsAccomClass.add(webrateCompetitorsAccomClass3);
        allWebrateCompetitorsAccomClass.add(webrateCompetitorsAccomClass4);
        competitor.setWebrateCompetitorsAccomClasses(new HashSet<>(allWebrateCompetitorsAccomClass));

        ProductAccomType productAccomType = new ProductAccomType();
        productAccomType.setProduct(independentProduct);
        productAccomType.setAccomType(accomClasses.get(0).getAccomTypes().iterator().next());
        ProductAccomType productAccomType1 = new ProductAccomType();
        productAccomType1.setProduct(independentProduct);
        productAccomType1.setAccomType(accomClasses.get(1).getAccomTypes().iterator().next());
        when(crudService.findAll(ProductAccomType.class)).thenReturn(Arrays.asList(productAccomType, productAccomType1));

        when(configParamsService.getParameterValue(IPConfigParamName.BAR_WEB_RATE_ALIAS.value())).thenReturn(WEB_RATE_HOTEL_ID);
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.INDEPENDENT_PRODUCTS_ENABLED)).thenReturn(true);
        when(agileRatesConfigurationService.findAgileAndSystemDefaultProductsAndIndependentProducts()).thenReturn(Arrays.asList(barProduct, independentProduct));

        accomMappingService.createWebrateCompAccomMappings(allWebrateCompetitorsAccomClass, competitor, Arrays.asList(accomClasses.get(0), accomClasses.get(1)));
        verify(crudService, times(1)).save(any(WebrateCompetitors.class));
        ArrayList<WebrateCompetitorsAccomClass> result = new ArrayList<>(competitor.getWebrateCompetitorsAccomClasses());
        assertEquals(4, result.size());
        assertEquals(webrateCompetitorsAccomClass1, result.get(0));
        assertEquals(webrateCompetitorsAccomClass2, result.get(1));
        assertEquals(webrateCompetitorsAccomClass3, result.get(2));
        assertEquals(webrateCompetitorsAccomClass4, result.get(3));
    }

    @Test
    @DisplayName("Create Webrate Comp Accom class _ No duplicate key _ Independent Products Enabled")
    void createWebrateCompAccomMappings_NoDuplicateKey_IndependentProductsEnabled() {
        CrudService crudService = mock(CrudService.class);
        inject(accomMappingService, "crudService", crudService);

        Product barProduct = createBarProduct();
        Product independentProduct = createIndependentProduct();
        createWebrateCompetitor();

        List<AccomClass> accomClasses = createAccomClasses();
        ProductAccomType productAccomType = new ProductAccomType();
        productAccomType.setProduct(independentProduct);
        productAccomType.setAccomType(accomClasses.get(0).getAccomTypes().iterator().next());
        ProductAccomType productAccomType1 = new ProductAccomType();
        productAccomType1.setProduct(independentProduct);
        productAccomType1.setAccomType(accomClasses.get(1).getAccomTypes().iterator().next());
        when(crudService.findAll(ProductAccomType.class)).thenReturn(Arrays.asList(productAccomType, productAccomType1));

        when(configParamsService.getParameterValue(IPConfigParamName.BAR_WEB_RATE_ALIAS.value())).thenReturn(WEB_RATE_HOTEL_ID);
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.INDEPENDENT_PRODUCTS_ENABLED)).thenReturn(true);
        when(agileRatesConfigurationService.findAgileAndSystemDefaultProductsAndIndependentProducts()).thenReturn(Arrays.asList(barProduct, independentProduct));

        accomMappingService.createWebrateCompAccomMappings(new ArrayList<>(), competitor, Arrays.asList(accomClasses.get(0), accomClasses.get(1)));
        verify(crudService, times(1)).save(any(WebrateCompetitors.class));
        ArrayList<WebrateCompetitorsAccomClass> result = new ArrayList<>(competitor.getWebrateCompetitorsAccomClasses());
        assertEquals(4, result.size());
    }

    @Test
    @DisplayName("Create Webrate Comp Accom Mappings _ BAR Changes _ Independent Products Enabled _ No Changes For Independent Products")
    void createWebrateCompAccomMappings_BARChanges_IndependentProductsEnabled_NoChangesForIndependentProducts() {
        CrudService crudService = mock(CrudService.class);
        inject(accomMappingService, "crudService", crudService);

        Product barProduct = createBarProduct();
        Product independentProduct = createIndependentProduct();
        createWebrateCompetitor();

        List<AccomClass> accomClasses = createAccomClasses();
        List<WebrateCompetitorsAccomClass> allWebrateCompetitorsAccomClass = new ArrayList<>();
        WebrateCompetitorsAccomClass webrateCompetitorsAccomClass1 = new WebrateCompetitorsAccomClass();
        webrateCompetitorsAccomClass1.setId(20);
        webrateCompetitorsAccomClass1.setWebrateCompetitor(competitor);
        webrateCompetitorsAccomClass1.setProductID(barProduct.getId());
        webrateCompetitorsAccomClass1.setAccomClass(accomClasses.get(0));
        WebrateCompetitorsAccomClass webrateCompetitorsAccomClass2 = new WebrateCompetitorsAccomClass();
        webrateCompetitorsAccomClass2.setId(21);
        webrateCompetitorsAccomClass2.setWebrateCompetitor(competitor);
        webrateCompetitorsAccomClass2.setProductID(barProduct.getId());
        webrateCompetitorsAccomClass2.setAccomClass(accomClasses.get(1));
        WebrateCompetitorsAccomClass webrateCompetitorsAccomClass3 = new WebrateCompetitorsAccomClass();
        webrateCompetitorsAccomClass3.setId(22);
        webrateCompetitorsAccomClass3.setWebrateCompetitor(competitor);
        webrateCompetitorsAccomClass3.setProductID(independentProduct.getId());
        webrateCompetitorsAccomClass3.setAccomClass(accomClasses.get(0));
        WebrateCompetitorsAccomClass webrateCompetitorsAccomClass4 = new WebrateCompetitorsAccomClass();
        webrateCompetitorsAccomClass4.setId(23);
        webrateCompetitorsAccomClass4.setWebrateCompetitor(competitor);
        webrateCompetitorsAccomClass4.setProductID(independentProduct.getId());
        webrateCompetitorsAccomClass4.setAccomClass(accomClasses.get(1));
        allWebrateCompetitorsAccomClass.add(webrateCompetitorsAccomClass1);
        allWebrateCompetitorsAccomClass.add(webrateCompetitorsAccomClass2);
        allWebrateCompetitorsAccomClass.add(webrateCompetitorsAccomClass3);
        allWebrateCompetitorsAccomClass.add(webrateCompetitorsAccomClass4);
        competitor.setWebrateCompetitorsAccomClasses(new HashSet<>(allWebrateCompetitorsAccomClass));

        ProductAccomType productAccomType = new ProductAccomType();
        productAccomType.setProduct(independentProduct);
        productAccomType.setAccomType(accomClasses.get(0).getAccomTypes().iterator().next());
        ProductAccomType productAccomType1 = new ProductAccomType();
        productAccomType1.setProduct(independentProduct);
        productAccomType1.setAccomType(accomClasses.get(1).getAccomTypes().iterator().next());
        when(crudService.findAll(ProductAccomType.class)).thenReturn(Arrays.asList(productAccomType, productAccomType1));

        when(configParamsService.getParameterValue(IPConfigParamName.BAR_WEB_RATE_ALIAS.value())).thenReturn(WEB_RATE_HOTEL_ID);
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.INDEPENDENT_PRODUCTS_ENABLED)).thenReturn(true);
        when(agileRatesConfigurationService.findAgileAndSystemDefaultProductsAndIndependentProducts()).thenReturn(Arrays.asList(barProduct, independentProduct));

        accomMappingService.createWebrateCompAccomMappings(allWebrateCompetitorsAccomClass, competitor, Arrays.asList(accomClasses.get(0), accomClasses.get(1), accomClasses.get(2)));
        verify(crudService).save(any(WebrateCompetitors.class));
        ArrayList<WebrateCompetitorsAccomClass> result = new ArrayList<>(competitor.getWebrateCompetitorsAccomClasses());
        assertEquals(5, result.size());
        assertEquals(accomClasses.get(2), result.get(0).getAccomClass());
        assertEquals(barProduct.getId(), result.get(0).getProductID());
        assertEquals(competitor, result.get(0).getWebrateCompetitor());
        assertEquals(webrateCompetitorsAccomClass1, result.get(1));
        assertEquals(webrateCompetitorsAccomClass2, result.get(2));
        assertEquals(webrateCompetitorsAccomClass3, result.get(3));
        assertEquals(webrateCompetitorsAccomClass4, result.get(4));
    }

    @Test
    @DisplayName("Create Webrate Comp Accom Mappings _ BA Rand Independent Changes _ Independent Products Enabled")
    void createWebrateCompAccomMappings_BARandIndependentChanges_IndependentProductsEnabled() {
        CrudService crudService = mock(CrudService.class);
        inject(accomMappingService, "crudService", crudService);

        Product barProduct = createBarProduct();
        Product independentProduct = createIndependentProduct();
        createWebrateCompetitor();

        List<AccomClass> accomClasses = createAccomClasses();
        List<WebrateCompetitorsAccomClass> allWebrateCompetitorsAccomClass = new ArrayList<>();
        WebrateCompetitorsAccomClass webrateCompetitorsAccomClass1 = new WebrateCompetitorsAccomClass();
        webrateCompetitorsAccomClass1.setId(20);
        webrateCompetitorsAccomClass1.setWebrateCompetitor(competitor);
        webrateCompetitorsAccomClass1.setProductID(barProduct.getId());
        webrateCompetitorsAccomClass1.setAccomClass(accomClasses.get(0));
        WebrateCompetitorsAccomClass webrateCompetitorsAccomClass2 = new WebrateCompetitorsAccomClass();
        webrateCompetitorsAccomClass2.setId(21);
        webrateCompetitorsAccomClass2.setWebrateCompetitor(competitor);
        webrateCompetitorsAccomClass2.setProductID(barProduct.getId());
        webrateCompetitorsAccomClass2.setAccomClass(accomClasses.get(1));
        WebrateCompetitorsAccomClass webrateCompetitorsAccomClass3 = new WebrateCompetitorsAccomClass();
        webrateCompetitorsAccomClass3.setId(22);
        webrateCompetitorsAccomClass3.setWebrateCompetitor(competitor);
        webrateCompetitorsAccomClass3.setProductID(independentProduct.getId());
        webrateCompetitorsAccomClass3.setAccomClass(accomClasses.get(0));
        WebrateCompetitorsAccomClass webrateCompetitorsAccomClass4 = new WebrateCompetitorsAccomClass();
        webrateCompetitorsAccomClass4.setId(23);
        webrateCompetitorsAccomClass4.setWebrateCompetitor(competitor);
        webrateCompetitorsAccomClass4.setProductID(independentProduct.getId());
        webrateCompetitorsAccomClass4.setAccomClass(accomClasses.get(1));
        allWebrateCompetitorsAccomClass.add(webrateCompetitorsAccomClass1);
        allWebrateCompetitorsAccomClass.add(webrateCompetitorsAccomClass2);
        allWebrateCompetitorsAccomClass.add(webrateCompetitorsAccomClass3);
        allWebrateCompetitorsAccomClass.add(webrateCompetitorsAccomClass4);
        competitor.setWebrateCompetitorsAccomClasses(new HashSet<>(allWebrateCompetitorsAccomClass));

        ProductAccomType productAccomType = new ProductAccomType();
        productAccomType.setProduct(independentProduct);
        productAccomType.setAccomType(accomClasses.get(0).getAccomTypes().iterator().next());
        ProductAccomType productAccomType1 = new ProductAccomType();
        productAccomType1.setProduct(independentProduct);
        productAccomType1.setAccomType(accomClasses.get(1).getAccomTypes().iterator().next());
        ProductAccomType productAccomType2 = new ProductAccomType();
        productAccomType2.setProduct(independentProduct);
        productAccomType2.setAccomType(accomClasses.get(2).getAccomTypes().iterator().next());
        when(crudService.findAll(ProductAccomType.class)).thenReturn(Arrays.asList(productAccomType, productAccomType1, productAccomType2));

        when(configParamsService.getParameterValue(IPConfigParamName.BAR_WEB_RATE_ALIAS.value())).thenReturn(WEB_RATE_HOTEL_ID);
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.INDEPENDENT_PRODUCTS_ENABLED)).thenReturn(true);
        when(agileRatesConfigurationService.findAgileAndSystemDefaultProductsAndIndependentProducts()).thenReturn(Arrays.asList(barProduct, independentProduct));

        accomMappingService.createWebrateCompAccomMappings(allWebrateCompetitorsAccomClass, competitor, Arrays.asList(accomClasses.get(0), accomClasses.get(1), accomClasses.get(2)));
        verify(crudService, times(1)).save(any(WebrateCompetitors.class));
        ArrayList<WebrateCompetitorsAccomClass> result = new ArrayList<>(competitor.getWebrateCompetitorsAccomClasses());
        assertEquals(6, result.size());
        assertEquals(accomClasses.get(2), result.get(0).getAccomClass());
        assertEquals(barProduct.getId(), result.get(0).getProductID());
        assertEquals(competitor, result.get(0).getWebrateCompetitor());
        assertEquals(accomClasses.get(2), result.get(1).getAccomClass());
        assertEquals(independentProduct.getId(), result.get(1).getProductID());
        assertEquals(competitor, result.get(1).getWebrateCompetitor());
        assertEquals(webrateCompetitorsAccomClass1, result.get(2));
        assertEquals(webrateCompetitorsAccomClass2, result.get(3));
        assertEquals(webrateCompetitorsAccomClass3, result.get(4));
        assertEquals(webrateCompetitorsAccomClass4, result.get(5));
    }

    @Test
    @DisplayName("Create Webrate Comp Accom Mappings _ BA Rand Independent Changes _ Independent Products Enabled")
    void createWebrateCompAccomMappings_BARandIndependentChanges_IndependentProductsEnabled_ForRDLFlow() {
        CrudService crudService = mock(CrudService.class);
        inject(accomMappingService, "crudService", crudService);

        Product barProduct = createBarProduct();
        Product independentProduct = createIndependentProduct();
        createWebrateCompetitor();

        List<AccomClass> accomClasses = createAccomClasses();
        List<WebrateCompetitorsAccomClass> allWebrateCompetitorsAccomClass = new ArrayList<>();
        WebrateCompetitorsAccomClass webrateCompetitorsAccomClass1 = new WebrateCompetitorsAccomClass();
        webrateCompetitorsAccomClass1.setId(20);
        webrateCompetitorsAccomClass1.setWebrateCompetitor(competitor);
        webrateCompetitorsAccomClass1.setProductID(barProduct.getId());
        webrateCompetitorsAccomClass1.setAccomClass(accomClasses.get(0));
        WebrateCompetitorsAccomClass webrateCompetitorsAccomClass2 = new WebrateCompetitorsAccomClass();
        webrateCompetitorsAccomClass2.setId(21);
        webrateCompetitorsAccomClass2.setWebrateCompetitor(competitor);
        webrateCompetitorsAccomClass2.setProductID(barProduct.getId());
        webrateCompetitorsAccomClass2.setAccomClass(accomClasses.get(1));
        WebrateCompetitorsAccomClass webrateCompetitorsAccomClass3 = new WebrateCompetitorsAccomClass();
        webrateCompetitorsAccomClass3.setId(22);
        webrateCompetitorsAccomClass3.setWebrateCompetitor(competitor);
        webrateCompetitorsAccomClass3.setProductID(independentProduct.getId());
        webrateCompetitorsAccomClass3.setAccomClass(accomClasses.get(0));
        WebrateCompetitorsAccomClass webrateCompetitorsAccomClass4 = new WebrateCompetitorsAccomClass();
        webrateCompetitorsAccomClass4.setId(23);
        webrateCompetitorsAccomClass4.setWebrateCompetitor(competitor);
        webrateCompetitorsAccomClass4.setProductID(independentProduct.getId());
        webrateCompetitorsAccomClass4.setAccomClass(accomClasses.get(1));
        allWebrateCompetitorsAccomClass.add(webrateCompetitorsAccomClass1);
        allWebrateCompetitorsAccomClass.add(webrateCompetitorsAccomClass2);
        allWebrateCompetitorsAccomClass.add(webrateCompetitorsAccomClass3);
        allWebrateCompetitorsAccomClass.add(webrateCompetitorsAccomClass4);
        competitor.setWebrateCompetitorsAccomClasses(new HashSet<>(allWebrateCompetitorsAccomClass));

        ProductAccomType productAccomType = new ProductAccomType();
        productAccomType.setProduct(independentProduct);
        productAccomType.setAccomType(accomClasses.get(0).getAccomTypes().iterator().next());
        ProductAccomType productAccomType1 = new ProductAccomType();
        productAccomType1.setProduct(independentProduct);
        productAccomType1.setAccomType(accomClasses.get(1).getAccomTypes().iterator().next());
        ProductAccomType productAccomType2 = new ProductAccomType();
        productAccomType2.setProduct(independentProduct);
        productAccomType2.setAccomType(accomClasses.get(2).getAccomTypes().iterator().next());
        when(crudService.findAll(ProductAccomType.class)).thenReturn(Arrays.asList(productAccomType, productAccomType1, productAccomType2));

        when(configParamsService.getParameterValue(IPConfigParamName.BAR_WEB_RATE_ALIAS.value())).thenReturn(WEB_RATE_HOTEL_ID);
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.INDEPENDENT_PRODUCTS_ENABLED)).thenReturn(true);
        when(agileRatesConfigurationService.findAgileAndSystemDefaultProductsAndIndependentProducts()).thenReturn(Arrays.asList(barProduct, independentProduct));

        accomMappingService.createWebrateCompAccomMappingsForRDL(allWebrateCompetitorsAccomClass, competitor, Arrays.asList(accomClasses.get(0), accomClasses.get(1), accomClasses.get(2)));
        verify(crudService, times(1)).save(any(WebrateCompetitors.class));
        ArrayList<WebrateCompetitorsAccomClass> result = new ArrayList<>(competitor.getWebrateCompetitorsAccomClasses());
        assertEquals(6, result.size());
        assertEquals(accomClasses.get(2), result.get(0).getAccomClass());
        assertEquals(barProduct.getId(), result.get(0).getProductID());
        assertEquals(competitor, result.get(0).getWebrateCompetitor());
        assertEquals(accomClasses.get(2), result.get(1).getAccomClass());
        assertEquals(independentProduct.getId(), result.get(1).getProductID());
        assertEquals(competitor, result.get(1).getWebrateCompetitor());
        assertEquals(webrateCompetitorsAccomClass1, result.get(2));
        assertEquals(webrateCompetitorsAccomClass2, result.get(3));
        assertEquals(webrateCompetitorsAccomClass3, result.get(4));
        assertEquals(webrateCompetitorsAccomClass4, result.get(5));
    }

    @Test
    @DisplayName("Test Empty Mappings")
    void testEmptyMappings() {
        when(accomMappingService.getAccomodationMappingByProperty()).thenReturn(Collections.emptyList());
        WebrateCompetitors competitor = UniqueWebrateCompetitorsCreator.createWebrateCompetitors();
        when(competitorDataFilterService.getAllCompetitorsByProperty(5)).thenReturn(Collections.singletonList(competitor));
        List<WebrateAccomClassMappingDTO> accomMappings = accomMappingService.getAccomMappingDetails(5);
        assertEquals(0, accomMappings.size());
    }

    @Test
    @DisplayName("Test Accom Class Mappings _ Display Name Enabled")
    void testAccomClassMappings_DisplayNameEnabled() {
        when(accomMappingService.getAccomodationMappingByProperty()).thenReturn(getWebrateAccomTypes());
        WebrateCompetitors competitor = UniqueWebrateCompetitorsCreator.createWebrateCompetitors();
        when(competitorDataFilterService.getAllCompetitorsByProperty(5)).thenReturn(Collections.singletonList(competitor));
        when(configParamsService.getBooleanParameterValue(GUIConfigParamName.DISPLAY_NAME_DISPLAY_NAME_ENABLED)).thenReturn(true);
        List<WebrateAccomClassMappingDTO> accomMappings = accomMappingService.getAccomMappingDetails(5);
        assertData(accomMappings);
    }

    @Test
    @DisplayName("Test Accom Class Mappings _ Display Name Disabled")
    void testAccomClassMappings_DisplayNameDisabled() {
        when(accomMappingService.getAccomodationMappingByProperty()).thenReturn(getWebrateAccomTypes());
        WebrateCompetitors competitor = UniqueWebrateCompetitorsCreator.createWebrateCompetitors();
        when(competitorDataFilterService.getAllCompetitorsByProperty(5)).thenReturn(Collections.singletonList(competitor));
        when(configParamsService.getBooleanParameterValue(GUIConfigParamName.DISPLAY_NAME_DISPLAY_NAME_ENABLED)).thenReturn(false);
        List<WebrateAccomClassMappingDTO> accomMappings = accomMappingService.getAccomMappingDetails(5);
        assertData(accomMappings);
    }

    @Test
    @DisplayName("Map Webrate Accom Types To Accom Classes")
    void mapWebrateAccomTypesToAccomClasses() {
        doReturn(createWebrateAccomTypeAverageRatesDTOs()).when(accomMappingService).getAverageRatesForWebrateAccomTypes();
        doReturn(createAccomClassADRList()).when(accomMappingService).getADRByAccomClasses();
        List<WebrateAccomType> webrateAccomTypes = createWebrateAccomTypes();
        accomMappingService.mapWebrateAccomTypesToAccomClasses(webrateAccomTypes, createAccomClasses());
        webrateAccomTypes.forEach(webrateAccomType -> {
            assertEquals(1, webrateAccomType.getWebrateAccomClassMappings().size());
            assertEquals(1, webrateAccomType.getWebrateAccomClassMappings().iterator().next().getAccomClass().getId());
        });
    }

    @Test
    @DisplayName("Map Webrate Accom Types To Accom Classes With No Webrate Data")
    void mapWebrateAccomTypesToAccomClassesWithNoWebrateData() {
        doReturn(emptyList()).when(accomMappingService).getAverageRatesForWebrateAccomTypes();
        doReturn(createAccomClassADRList()).when(accomMappingService).getADRByAccomClasses();
        List<WebrateAccomType> webrateAccomTypes = createWebrateAccomTypes();
        accomMappingService.mapWebrateAccomTypesToAccomClasses(webrateAccomTypes, createAccomClasses());
        webrateAccomTypes.forEach(webrateAccomType -> assertNull(webrateAccomType.getWebrateAccomClassMappings()));
    }

    @Test
    @DisplayName("Map Webrate Accom Types To Accom Classes With No Matches")
    void mapWebrateAccomTypesToAccomClassesWithNoMatches() {
        doReturn(createWebrateAccomTypesDTOsWithNoMatches()).when(accomMappingService).getAverageRatesForWebrateAccomTypes();
        doReturn(createAccomClassADRList()).when(accomMappingService).getADRByAccomClasses();
        List<WebrateAccomType> webrateAccomTypes = createWebrateAccomTypes();
        accomMappingService.mapWebrateAccomTypesToAccomClasses(webrateAccomTypes, createAccomClasses());
        webrateAccomTypes.forEach(webrateAccomType -> assertNull(webrateAccomType.getWebrateAccomClassMappings()));
    }

    @Test
    @DisplayName("Average Rate Is Within Range")
    void averageRateIsWithinRange() {
        assertTrue(accomMappingService.averageRateIsWithinRange(BigDecimal.valueOf(50), BigDecimal.valueOf(20), BigDecimal.valueOf(100)));
        assertTrue(accomMappingService.averageRateIsWithinRange(BigDecimal.valueOf(50), BigDecimal.valueOf(50), BigDecimal.valueOf(100)));
        assertTrue(accomMappingService.averageRateIsWithinRange(BigDecimal.valueOf(50), BigDecimal.valueOf(20), BigDecimal.valueOf(50)));
    }

    @Test
    @DisplayName("Map Webrate Accom Types To Accom Classes Wit Multiple Matches")
    void mapWebrateAccomTypesToAccomClassesWitMultipleMatches() {
        doReturn(createWebrateAccomTypesDTOsWithMultipleMatches()).when(accomMappingService).getAverageRatesForWebrateAccomTypes();
        doReturn(createAccomClassADRList()).when(accomMappingService).getADRByAccomClasses();
        List<WebrateAccomType> webrateAccomTypes = createWebrateAccomTypes();
        accomMappingService.mapWebrateAccomTypesToAccomClasses(webrateAccomTypes, createAccomClasses());
        webrateAccomTypes.forEach(webrateAccomType -> {
            assertEquals(1, webrateAccomType.getWebrateAccomClassMappings().size());
            assertEquals(3, webrateAccomType.getWebrateAccomClassMappings().iterator().next().getAccomClass().getId());
        });
    }

    @Test
    @DisplayName("Map Webrate Accom Types To Single Accom Class")
    void mapWebrateAccomTypesToSingleAccomClass() {
        doReturn(createWebrateAccomTypeAverageRatesDTOs()).when(accomMappingService).getAverageRatesForWebrateAccomTypes();
        doReturn(createAccomClassADRSingleton()).when(accomMappingService).getADRByAccomClasses();
        List<WebrateAccomType> webrateAccomTypes = createWebrateAccomTypes();
        accomMappingService.mapWebrateAccomTypesToAccomClasses(webrateAccomTypes, createAccomClasses());
        assertEquals(1, webrateAccomTypes.get(0).getWebrateAccomClassMappings().iterator().next().getAccomClass().getId());
        assertEquals(1, webrateAccomTypes.get(1).getWebrateAccomClassMappings().iterator().next().getAccomClass().getId());
        assertEquals(1, webrateAccomTypes.get(2).getWebrateAccomClassMappings().iterator().next().getAccomClass().getId());
        assertNull(webrateAccomTypes.get(3).getWebrateAccomClassMappings());
    }

    @Test
    @DisplayName("Get Minimum Average Rate")
    void getMinimumAverageRate() {
        Optional<BigDecimal> minimumAverageRate = accomMappingService.getMinimumAverageRate(createAccomClassADRList());
        assertTrue(minimumAverageRate.isPresent());
        assertEquals(new BigDecimal("70.00"), minimumAverageRate.get());
    }

    @Test
    @DisplayName("Get Minimum Average Rate Empty List")
    void getMinimumAverageRateEmptyList() {
        Optional<BigDecimal> minimumAverageRate = accomMappingService.getMinimumAverageRate(emptyList());
        assertFalse(minimumAverageRate.isPresent());
    }

    @Test
    @DisplayName("Get Maximum Average Rate")
    void getMaximumAverageRate() {
        Optional<BigDecimal> maximumAverageRate = accomMappingService.getMaximumAverageRate(createAccomClassADRList());
        assertTrue(maximumAverageRate.isPresent());
        assertEquals(new BigDecimal("169.00"), maximumAverageRate.get());
    }

    @Test
    @DisplayName("Get Maximum Average Rate Empty List")
    void getMaximumAverageRateEmptyList() {
        Optional<BigDecimal> maximumAverageRate = accomMappingService.getMaximumAverageRate(emptyList());
        assertFalse(maximumAverageRate.isPresent());
    }

    private List<WebrateAccomTypeAverageRateDTO> createWebrateAccomTypeAverageRatesDTOs() {
        List<WebrateAccomTypeAverageRateDTO> results = new ArrayList<>();
        for (int i = 1; i <= 4; i++) {
            WebrateAccomTypeAverageRateDTO webrateAccomTypeAverageRateDTO = new WebrateAccomTypeAverageRateDTO(new Object[]{i, BigDecimal.valueOf(80 + 5 * i)});
            results.add(webrateAccomTypeAverageRateDTO);
        }
        return results;
    }

    private List<WebrateAccomTypeAverageRateDTO> createWebrateAccomTypesDTOsWithNoMatches() {
        List<WebrateAccomTypeAverageRateDTO> results = new ArrayList<>();
        for (int i = 1; i <= 4; i++) {
            WebrateAccomTypeAverageRateDTO webrateAccomTypeAverageRateDTO = new WebrateAccomTypeAverageRateDTO(new Object[]{i, BigDecimal.valueOf(200)});
            results.add(webrateAccomTypeAverageRateDTO);
        }
        return results;
    }

    private List<WebrateAccomTypeAverageRateDTO> createWebrateAccomTypesDTOsWithMultipleMatches() {
        List<WebrateAccomTypeAverageRateDTO> results = new ArrayList<>();
        for (int i = 1; i <= 4; i++) {
            WebrateAccomTypeAverageRateDTO webrateAccomTypeAverageRateDTO = new WebrateAccomTypeAverageRateDTO(new Object[]{i, BigDecimal.valueOf(125)});
            results.add(webrateAccomTypeAverageRateDTO);
        }
        return results;
    }

    private List<WebrateAccomTypeAverageRateDTO> createAccomClassADRList() {
        ArrayList<WebrateAccomTypeAverageRateDTO> accomClassADRDTOs = new ArrayList<>();
        for (int i = 1; i <= 4; i++) {
            WebrateAccomTypeAverageRateDTO webrateAccomTypeAverageRateDTO = new WebrateAccomTypeAverageRateDTO(new Object[]{i, BigDecimal.valueOf(100 + 10 * (i - 1))});
            accomClassADRDTOs.add(webrateAccomTypeAverageRateDTO);
        }
        return accomClassADRDTOs;
    }

    private List<WebrateAccomTypeAverageRateDTO> createAccomClassADRSingleton() {
        WebrateAccomTypeAverageRateDTO webrateAccomTypeAverageRateDTO = new WebrateAccomTypeAverageRateDTO(new Object[]{1, BigDecimal.valueOf(75)});
        return Collections.singletonList(webrateAccomTypeAverageRateDTO);
    }

    private List<WebrateAccomType> createWebrateAccomTypes() {
        ArrayList<WebrateAccomType> webrateAccomTypeList = new ArrayList<>();
        for (int i = 1; i <= 4; i++) {
            WebrateAccomType webrateAccomType = new WebrateAccomType();
            webrateAccomType.setId(i);
            webrateAccomTypeList.add(webrateAccomType);
        }
        return webrateAccomTypeList;
    }

    private List<AccomClass> createAccomClasses() {
        ArrayList<AccomClass> accomClasses = new ArrayList<>();
        for (int i = 1; i <= 4; i++) {
            AccomType accomType = new AccomType();
            accomType.setStatusId(1);
            accomType.setAccomTypeCapacity(100);
            AccomClass accomClass = new AccomClass();
            accomClass.setSystemDefault(0);
            accomClass.setAccomTypes(Collections.singleton(accomType));
            accomClass.setId(i);
            accomClass.setName("accomClass" + i);
            accomClasses.add(accomClass);
        }
        return accomClasses;
    }

    private List<WebrateChannel> getWebrateChannels() {
        WebrateChannel webrateChannel = new WebrateChannel();
        webrateChannel.setId(5);
        webrateChannel.setWebrateChannelName("WebrateChannel");
        return Collections.singletonList(webrateChannel);
    }

    @Test
    @DisplayName("Update New Competitor Status To Active")
    void updateNewCompetitorStatusToActive() {
        PacmanWorkContextHelper.setWorkContext("BLKSTN", 5);
        tenantCrudService().executeUpdateByNativeQuery("update Webrate_Competitors set Status_ID = 3");
        accomMappingService.updateNewCompetitorStatusToActive();
        List<WebrateCompetitors> webrateCompetitors = tenantCrudService().findByNamedQuery(WebrateCompetitors.BY_PROPERTY_ID, with("propertyId", 5).parameters());
        webrateCompetitors.forEach(webrateCompetitor -> {
            assertEquals(1, webrateCompetitor.getStatusId());
            assertTrue(webrateCompetitor.getWebrateCompetitorsAccomClasses().stream().allMatch(accomClass -> accomClass.getDemandEnabled().equals(1)));
        });
    }

    @Test
    @DisplayName("Set Rate Shopping Adjustment For Default Tax")
    void setRateShoppingAdjustmentForDefaultTax() {
        when(configParamsService.getBooleanParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED)).thenReturn(true);
        Tax defaultTax = new Tax();
        defaultTax.setRoomTaxRate(BigDecimal.TEN);
        when(taxService.findTax()).thenReturn(defaultTax);
        accomMappingService.setRateShoppingAdjustmentForDefaultTax();
        List<RateShoppingAdjustment> taxAdjustments = tenantCrudService().findByNamedQuery(RateShoppingAdjustment.FETCH_DEFAULT);
        assertFalse(taxAdjustments.isEmpty());
        assertEquals(BigDecimal.TEN.negate(), taxAdjustments.get(0).getTaxOffsetValue());
    }

    @Test
    @DisplayName("Set Rate Shopping Adjustment For Zero Tax")
    void setRateShoppingAdjustmentForZeroTax() {
        when(configParamsService.getBooleanParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED)).thenReturn(true);
        Tax defaultTax = new Tax();
        defaultTax.setRoomTaxRate(BigDecimal.ZERO);
        when(taxService.findTax()).thenReturn(defaultTax);
        accomMappingService.setRateShoppingAdjustmentForDefaultTax();
        List<RateShoppingAdjustment> taxAdjustments = tenantCrudService().findByNamedQuery(RateShoppingAdjustment.FETCH_DEFAULT);
        assertTrue(taxAdjustments.isEmpty());
    }

    @Test
    @DisplayName("Set Rate Shopping Adjustment For No Tax")
    void setRateShoppingAdjustmentForNoTax() {
        when(configParamsService.getBooleanParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED)).thenReturn(true);
        when(taxService.findTax()).thenReturn(null);
        accomMappingService.setRateShoppingAdjustmentForDefaultTax();
        List<RateShoppingAdjustment> taxAdjustments = tenantCrudService().findByNamedQuery(RateShoppingAdjustment.FETCH_DEFAULT);
        assertTrue(taxAdjustments.isEmpty());
    }

    @Test
    @DisplayName("Get Accom Classes")
    void getAccomClasses() {
        List<AccomClass> accomClasses = createAccomClasses();
        accomClasses.stream().filter(accomClass -> accomClass.getId().equals(1)).findFirst().ifPresent(accomClass -> accomClass.getAccomTypes().forEach(accomType -> accomType.setAccomTypeCapacity(0)));
        when(accommodation2Service.getActiveNonDefaultAccomClasses()).thenReturn(accomClasses);
        List<AccomClass> actual = accomMappingService.getAccomClasses();
        assertEquals(4, actual.size());
    }

    @Test
    @DisplayName("Rate Shopping Configuration Prerequisite Data Is Not Present")
    void rateShoppingConfigurationPrerequisiteDataIsNotPresent() {
        when(webrateShoppingDataService.getAllChannelsByProperty()).thenReturn(singletonList(new WebrateChannel()));
        assertFalse(accomMappingService.rateShoppingConfigurationPrerequisiteDataIsNotPresent());
    }

    @Test
    @DisplayName("Rate Shopping Configuration Prerequisite Data Is Not Present _ Missing Webrate")
    void rateShoppingConfigurationPrerequisiteDataIsNotPresent_MissingWebrate() {
        tenantCrudService().deleteAll(Webrate.class);
        tenantCrudService().deleteAll(WebrateAccomClassMapping.class);
        tenantCrudService().deleteAll(WebrateAccomType.class);
        tenantCrudService().flushAndClear();
        assertTrue(accomMappingService.rateShoppingConfigurationPrerequisiteDataIsNotPresent());
    }

    @Test
    @DisplayName("Rate Shopping Configuration Prerequisite Data Is Not Present _ Missing Channels")
    void rateShoppingConfigurationPrerequisiteDataIsNotPresent_MissingChannels() {
        when(webrateShoppingDataService.getAllChannelsByProperty()).thenReturn(emptyList());
        assertTrue(accomMappingService.rateShoppingConfigurationPrerequisiteDataIsNotPresent());
    }

    @Test
    @DisplayName("Rate Shopping Configuration Prerequisite Data Is Not Present _ Missing Competitors")
    void rateShoppingConfigurationPrerequisiteDataIsNotPresent_MissingCompetitors() {
        tenantCrudService().executeUpdateByNativeQuery("delete from Webrate");
        tenantCrudService().executeUpdateByNativeQuery("delete from Pace_Webrate");
        tenantCrudService().executeUpdateByNativeQuery("delete from Pace_Webrate_Differential");
        tenantCrudService().executeUpdateByNativeQuery("delete from Rate_Shopping_Adjustment");
        tenantCrudService().executeUpdateByNativeQuery("delete from RRA_Competitor");
        tenantCrudService().executeUpdateByNativeQuery("delete from Webrate_OVR_Competitor_DTLS");
        tenantCrudService().executeUpdateByNativeQuery("delete from Webrate_Competitors_Class");
        tenantCrudService().executeUpdateByNativeQuery("delete from RDL_Shop_Attribute");
        tenantCrudService().executeUpdateByNativeQuery("delete from Webrate_Competitors");
        tenantCrudService().flushAndClear();
        assertTrue(accomMappingService.rateShoppingConfigurationPrerequisiteDataIsNotPresent());
    }

    private void assertWithExpectedStatusOfCompetitor(Integer status) {
        assertThat(competitor.getStatusId(), is(status));
    }

    private void assertWithExpectedStatusOfDemandAndRanking(Integer status) {
        competitor.getWebrateCompetitorsAccomClasses().forEach(webrateCompetitorsAccomClass -> {
            assertThat(webrateCompetitorsAccomClass.getDemandEnabled(), is(status));
            assertThat(webrateCompetitorsAccomClass.getRankingEnabled(), is(status));
        });
    }

    private AccommodationMappingServiceTest runActualServiceToUpdateCompetitor() {
        accomMappingService.updateCompetitorStatus(competitor);
        return this;
    }

    private AccommodationMappingServiceTest setupDataForHotelIdAndWebrateCompetitor(final String hotelId) {
        createWebrateCompetitor();
        competitor.setWebrateHotelID(hotelId);
        return this;

    }

    private AccommodationMappingServiceTest setupDataForRDLUpsIdAndWebrateCompetitor(final String upsID) {
        createWebrateCompetitor();
        competitor.setUpsId(upsID);

        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.IS_RDL_ENABLED)).thenReturn(true);
        return this;
    }

    private AccommodationMappingServiceTest setupProperty(String upsId) {
        when(propertyService.getPropertyById(anyInt())).thenReturn(getProperty(upsId));
        return this;
    }

    private Property getProperty(String upsId) {
        Property property = new Property();
        property.setCode("H1");
        property.setUpsId(upsId);
        return property;
    }

    private AccommodationMappingServiceTest runActualServiceToUpdateCompetitorAndRoomClasses() {
        Product barProduct = createBarProduct();
        AccomClass uniqueAccomClass = UniqueAccomClassCreator.createUniqueAccomClass();
        accomMappingService.updateCompetitorWithNewAccomClasses(WEB_RATE_HOTEL_ID, List.of(uniqueAccomClass), competitor, barProduct);
        return this;
    }

    private AccommodationMappingServiceTest setUpDataForNewCompetitorWithRoomClasses(final Integer demandEnabled, final Integer rankingEnabled) {
        createWebrateCompetitor();
        WebrateCompetitorsAccomClass webrateCompetitorsAccomClass = UniqueWebrateCompetitorsAccomClassCreator.createWebrateCompetitorsAccomClass(demandEnabled | rankingEnabled, rankingEnabled, competitor);
        WebrateCompetitorsAccomClass webrateCompetitorsAccomClass1 = UniqueWebrateCompetitorsAccomClassCreator.createWebrateCompetitorsAccomClass(0, 0, competitor);
        WebrateCompetitorsAccomClass webrateCompetitorsAccomClass2 = UniqueWebrateCompetitorsAccomClassCreator.createWebrateCompetitorsAccomClass(0, 0, competitor);
        competitor.setWebrateCompetitorsAccomClasses(Stream.of(webrateCompetitorsAccomClass, webrateCompetitorsAccomClass1, webrateCompetitorsAccomClass2).collect(Collectors.toSet()));
        competitor.setStatusId(NEW_STATUS_ID);
        return this;
    }

    private AccommodationMappingServiceTest setUpDataForCompetitorWithRoomClasses(final Integer demandEnabled, final Integer rankingEnabled) {
        createWebrateCompetitor();
        WebrateCompetitorsAccomClass webrateCompetitorsAccomClass = UniqueWebrateCompetitorsAccomClassCreator.createWebrateCompetitorsAccomClass(demandEnabled | rankingEnabled, rankingEnabled, competitor);
        WebrateCompetitorsAccomClass webrateCompetitorsAccomClass1 = UniqueWebrateCompetitorsAccomClassCreator.createWebrateCompetitorsAccomClass(0, 0, competitor);
        WebrateCompetitorsAccomClass webrateCompetitorsAccomClass2 = UniqueWebrateCompetitorsAccomClassCreator.createWebrateCompetitorsAccomClass(0, 0, competitor);
        competitor.setWebrateCompetitorsAccomClasses(Stream.of(webrateCompetitorsAccomClass, webrateCompetitorsAccomClass1, webrateCompetitorsAccomClass2).collect(Collectors.toSet()));
        competitor.setStatusId(INACTIVE_STATUS_ID);
        return this;
    }

    private void createWebrateCompetitor() {
        competitor = UniqueWebrateCompetitorsCreator.createWebrateCompetitorsByName(TEST, TEST);
    }

    @Test
    @DisplayName("Get Mapped Accom Classes Test")
    void getMappedAccomClassesTest() {
        Integer propertyId = 5;
        PacmanWorkContextHelper.setWorkContext("BSTN", propertyId);
        List<AccomClass> accomClassList = tenantCrudService().findByNamedQuery(WebrateAccomClassMapping.BY_PROPERTY_ID, with("propertyId", propertyId).parameters());
        AbstractMultiPropertyCrudService multiPropertyCrudService = multiPropertyCrudService();
        inject(accomMappingService, "multiPropertyCrudService", multiPropertyCrudService);

        List<AccomClass> actual = accomMappingService.getMappedAccomClasses(propertyId);

        assertNotNull(actual);
        assertEquals(actual.size(), accomClassList.size());
        assertTrue(CollectionUtils.isEqualCollection(actual, accomClassList));
    }

    @Test
    @DisplayName("Get Accom Class By Id Test")
    void getAccomClassByIdTest() {
        Integer propertyId = 5;
        PacmanWorkContextHelper.setWorkContext("BSTN", propertyId);
        List<AccomClass> accomClassList = tenantCrudService().findAll(AccomClass.class);
        AbstractMultiPropertyCrudService multiPropertyCrudService = multiPropertyCrudService();
        inject(accomMappingService, "multiPropertyCrudService", multiPropertyCrudService);
        accomClassList.forEach(accomClass -> assertEquals(accomClass.getName(), (accomMappingService.getAccomClassById(propertyId, accomClass.getId())).getName()));
    }

    @Test
    @DisplayName("Test Save Accom Mapping Webrate Accom Type With CPC Notification Cleanup For Room Classes Not Usedin Mapping")
    void testSaveAccomMappingWebrateAccomTypeWithCPCNotificationCleanupForRoomClassesNotUsedinMapping() {
        Product barProduct = createBarProduct();

        AccommodationClassMappingTestData.createTestData();

        List<WebrateAccomType> webrateAccomTypeList = accomMappingService.getAccomodationMappingByProperty(5);
        WebrateAccomClassMapping mapping = webrateAccomTypeList.get(0).getWebrateAccomClassMappings().iterator().next();
        AccomClass oldAccomClass = mapping.getAccomClass();
        mapping.setAccomClass(getNewAccomClass());
        when(agileRatesConfigurationService.findAgileAndSystemDefaultProductsAndIndependentProducts()).thenReturn(List.of(barProduct));
        boolean value = accomMappingService.saveAccomMapping(webrateAccomTypeList, null, 5, "1", Collections.singletonList(oldAccomClass));
        Assertions.assertTrue(value);
        verify(webrateShoppingCleanUpService, times(1)).cleanUpCPCByRoomClassNotificationsConfig(oldAccomClass);
    }

    @Test
    @DisplayName("Create Default Webrate Ranking Accom Classes _ No Existing Webrate Ranking Accom Classes")
    void createDefaultWebrateRankingAccomClasses_NoExistingWebrateRankingAccomClasses() {
        Product barProduct = createBarProduct();
        Product independentProduct = createIndependentProduct();

        doReturn(createWebrateAccomTypeAverageRatesDTOs()).when(accomMappingService).getAverageRatesForWebrateAccomTypes();
        doReturn(createAccomClassADRSingleton()).when(accomMappingService).getADRByAccomClasses();
        List<WebrateAccomType> webrateAccomTypes = createWebrateAccomTypes();
        List<AccomClass> accomClasses = createAccomClasses();
        accomMappingService.mapWebrateAccomTypesToAccomClasses(webrateAccomTypes, accomClasses);

        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.INDEPENDENT_PRODUCTS_ENABLED)).thenReturn(false);
        when(agileRatesConfigurationService.findSystemDefaultProductAndIndependentProducts()).thenReturn(Arrays.asList(barProduct, independentProduct));

        List<WebrateRankingAccomClass> results = accomMappingService.createDefaultWebrateRankingAccomClassesForSystemDefaultAndIndependentProducts(TEST_PROPERTY_ID, "1");
        assertEquals(2, results.size());
        assertFalse(results.get(0).isPersisted());
        assertEquals(barProduct.getId(), results.get(0).getProductID());
        assertFalse(results.get(1).isPersisted());
        assertEquals(barProduct.getId(), results.get(1).getProductID());
    }

    @Test
    @DisplayName("Update Competitor Accom Class Reference _ Independent Products Disabled _ No Existing Webrate Competitors Accom Class")
    void updateCompetitorAccomClassReference_IndependentProductsDisabled_NoExistingWebrateCompetitorsAccomClass() {
        CrudService crudService = mock(CrudService.class);
        inject(accomMappingService, "crudService", crudService);

        Product barProduct = createBarProduct();
        Product independentProduct = createIndependentProduct();

        doReturn(createWebrateAccomTypeAverageRatesDTOs()).when(accomMappingService).getAverageRatesForWebrateAccomTypes();
        doReturn(createAccomClassADRSingleton()).when(accomMappingService).getADRByAccomClasses();
        List<WebrateAccomType> webrateAccomTypes = createWebrateAccomTypes();
        List<AccomClass> accomClasses = createAccomClasses();
        accomMappingService.mapWebrateAccomTypesToAccomClasses(webrateAccomTypes, accomClasses);

        createWebrateCompetitor();
        competitor.setStatusId(INACTIVE_STATUS_ID);

        when(crudService.findByNamedQuery(WebrateAccomClassMapping.BY_PROPERTY_ID, with("propertyId", TEST_PROPERTY_ID).parameters())).thenReturn(Arrays.asList(accomClasses.get(0), accomClasses.get(1), accomClasses.get(2)));
        when(crudService.findAll(WebrateCompetitorsAccomClass.class)).thenReturn(List.of());
        when(crudService.findByNamedQuery(WebrateCompetitors.BY_PROPERTY_ID, with("propertyId", TEST_PROPERTY_ID).parameters())).thenReturn(List.of(competitor));
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.INDEPENDENT_PRODUCTS_ENABLED)).thenReturn(false);

        accomMappingService.updateCompetitorAccomClassReference(TEST_PROPERTY_ID, Arrays.asList(barProduct, independentProduct));
        assertEquals(3, competitor.getWebrateCompetitorsAccomClasses().size());
        List<WebrateCompetitorsAccomClass> resultWebrateCompetitorsAccomClasses = new ArrayList<>(competitor.getWebrateCompetitorsAccomClasses());
        assertEquals(accomClasses.get(0), resultWebrateCompetitorsAccomClasses.get(0).getAccomClass());
        assertEquals(barProduct.getId(), resultWebrateCompetitorsAccomClasses.get(0).getProductID());
        assertEquals(competitor, resultWebrateCompetitorsAccomClasses.get(0).getWebrateCompetitor());
        assertEquals(accomClasses.get(1), resultWebrateCompetitorsAccomClasses.get(1).getAccomClass());
        assertEquals(competitor, resultWebrateCompetitorsAccomClasses.get(1).getWebrateCompetitor());
        assertEquals(barProduct.getId(), resultWebrateCompetitorsAccomClasses.get(1).getProductID());
        assertEquals(accomClasses.get(2), resultWebrateCompetitorsAccomClasses.get(2).getAccomClass());
        assertEquals(barProduct.getId(), resultWebrateCompetitorsAccomClasses.get(2).getProductID());
        assertEquals(competitor, resultWebrateCompetitorsAccomClasses.get(2).getWebrateCompetitor());

        verify(crudService, never()).executeUpdateByNamedQuery(any(String.class), anyMap());
        verify(crudService, never()).delete(any());
    }

    @Test
    @DisplayName("Update Competitor Accom Class Reference _ Independent Products Disabled and RDL Disabled _ No Existing Webrate Competitors Accom Class")
    void updateCompetitorAccomClassReference_IndependentProductsDisabledAndRDLIsDisabled_NoExistingWebrateCompetitorsAccomClass() {
        CrudService crudService = mock(CrudService.class);
        inject(accomMappingService, "crudService", crudService);

        Product linkedProduct = createdLinkedProduct();

        doReturn(createWebrateAccomTypeAverageRatesDTOs()).when(accomMappingService).getAverageRatesForWebrateAccomTypes();
        doReturn(createAccomClassADRSingleton()).when(accomMappingService).getADRByAccomClasses();
        List<WebrateAccomType> webrateAccomTypes = createWebrateAccomTypes();
        List<AccomClass> accomClasses = createAccomClasses();
        accomMappingService.mapWebrateAccomTypesToAccomClasses(webrateAccomTypes, accomClasses);

        ProductAccomType productAccomType = new ProductAccomType();
        productAccomType.setProduct(linkedProduct);
        productAccomType.setAccomType(accomClasses.get(0).getAccomTypes().iterator().next());
        ProductAccomType productAccomType1 = new ProductAccomType();
        productAccomType1.setProduct(linkedProduct);
        productAccomType1.setAccomType(accomClasses.get(1).getAccomTypes().iterator().next());
        ProductAccomType productAccomType2 = new ProductAccomType();
        productAccomType2.setProduct(linkedProduct);
        productAccomType2.setAccomType(accomClasses.get(2).getAccomTypes().iterator().next());

        when(crudService.findAll(ProductAccomType.class)).thenReturn(Arrays.asList(productAccomType, productAccomType1, productAccomType2));

        createWebrateCompetitor();
        competitor.setStatusId(INACTIVE_STATUS_ID);

        when(crudService.findByNamedQuery(WebrateAccomClassMapping.BY_PROPERTY_ID, with("propertyId", TEST_PROPERTY_ID).parameters())).thenReturn(Arrays.asList(accomClasses.get(0), accomClasses.get(1), accomClasses.get(2)));
        when(crudService.findAll(WebrateCompetitorsAccomClass.class)).thenReturn(List.of());
        when(crudService.findByNamedQuery(WebrateCompetitors.BY_PROPERTY_ID, with("propertyId", TEST_PROPERTY_ID).parameters())).thenReturn(List.of(competitor));
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.INDEPENDENT_PRODUCTS_ENABLED)).thenReturn(false);
        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.IS_RDL_ENABLED)).thenReturn(false);
        when(propertyService.getPropertyById(TEST_PROPERTY_ID)).thenReturn(getProperty(TEST_UPS_ID));

        accomMappingService.updateCompetitorAccomClassReference(TEST_PROPERTY_ID, Arrays.asList(linkedProduct));

        assertEquals(0, competitor.getWebrateCompetitorsAccomClasses().size());

        verify(crudService, never()).executeUpdateByNamedQuery(any(String.class), anyMap());
        verify(crudService, never()).delete(any());

    }

    @Test
    @DisplayName("Update Competitor Accom Class Reference _ Independent Products Disabled RDL Enabled _ No Existing Webrate Competitors Accom Class")
    void updateCompetitorAccomClassReference_IndependentProductsDisabledAndRDLEnabled_NoExistingWebrateCompetitorsAccomClass() {
        CrudService crudService = mock(CrudService.class);
        inject(accomMappingService, "crudService", crudService);

        Product linkedProduct = createdLinkedProduct();

        doReturn(createWebrateAccomTypeAverageRatesDTOs()).when(accomMappingService).getAverageRatesForWebrateAccomTypes();
        doReturn(createAccomClassADRSingleton()).when(accomMappingService).getADRByAccomClasses();
        List<WebrateAccomType> webrateAccomTypes = createWebrateAccomTypes();
        List<AccomClass> accomClasses = createAccomClasses();
        accomMappingService.mapWebrateAccomTypesToAccomClasses(webrateAccomTypes, accomClasses);

        ProductAccomType productAccomType = new ProductAccomType();
        productAccomType.setProduct(linkedProduct);
        productAccomType.setAccomType(accomClasses.get(0).getAccomTypes().iterator().next());
        ProductAccomType productAccomType1 = new ProductAccomType();
        productAccomType1.setProduct(linkedProduct);
        productAccomType1.setAccomType(accomClasses.get(1).getAccomTypes().iterator().next());
        ProductAccomType productAccomType2 = new ProductAccomType();
        productAccomType2.setProduct(linkedProduct);
        productAccomType2.setAccomType(accomClasses.get(2).getAccomTypes().iterator().next());

        when(crudService.findAll(ProductAccomType.class)).thenReturn(Arrays.asList(productAccomType, productAccomType1, productAccomType2));

        createWebrateCompetitor();
        competitor.setStatusId(INACTIVE_STATUS_ID);

        when(crudService.findByNamedQuery(WebrateAccomClassMapping.BY_PROPERTY_ID, with("propertyId", TEST_PROPERTY_ID).parameters())).thenReturn(Arrays.asList(accomClasses.get(0), accomClasses.get(1), accomClasses.get(2)));
        when(crudService.findAll(WebrateCompetitorsAccomClass.class)).thenReturn(List.of());
        when(crudService.findByNamedQuery(WebrateCompetitors.BY_PROPERTY_ID, with("propertyId", TEST_PROPERTY_ID).parameters())).thenReturn(List.of(competitor));
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.INDEPENDENT_PRODUCTS_ENABLED)).thenReturn(false);
        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.IS_RDL_ENABLED)).thenReturn(true);
        when(propertyService.getPropertyById(TEST_PROPERTY_ID)).thenReturn(getProperty(TEST_UPS_ID));

        accomMappingService.updateCompetitorAccomClassReference(TEST_PROPERTY_ID, Arrays.asList(linkedProduct));

        assertEquals(3, competitor.getWebrateCompetitorsAccomClasses().size());
        List<WebrateCompetitorsAccomClass> resultWebrateCompetitorsAccomClasses = new ArrayList<>(competitor.getWebrateCompetitorsAccomClasses());
        assertEquals(accomClasses.get(0), resultWebrateCompetitorsAccomClasses.get(0).getAccomClass());
        assertEquals(linkedProduct.getId(), resultWebrateCompetitorsAccomClasses.get(0).getProductID());
        assertEquals(competitor, resultWebrateCompetitorsAccomClasses.get(0).getWebrateCompetitor());
        assertEquals(accomClasses.get(1), resultWebrateCompetitorsAccomClasses.get(1).getAccomClass());
        assertEquals(competitor, resultWebrateCompetitorsAccomClasses.get(1).getWebrateCompetitor());
        assertEquals(linkedProduct.getId(), resultWebrateCompetitorsAccomClasses.get(1).getProductID());
        assertEquals(accomClasses.get(2), resultWebrateCompetitorsAccomClasses.get(2).getAccomClass());
        assertEquals(linkedProduct.getId(), resultWebrateCompetitorsAccomClasses.get(2).getProductID());
        assertEquals(competitor, resultWebrateCompetitorsAccomClasses.get(2).getWebrateCompetitor());

        verify(crudService, never()).executeUpdateByNamedQuery(any(String.class), anyMap());
        verify(crudService, never()).delete(any());
    }

    @Test
    @DisplayName("Update Competitor Accom Class Reference _ Independent Products Disabled _ Existing Webrate Competitors Accom Class")
    void updateCompetitorAccomClassReference_IndependentProductsDisabled_ExistingWebrateCompetitorsAccomClass() {
        CrudService crudService = mock(CrudService.class);
        inject(accomMappingService, "crudService", crudService);

        Product barProduct = createBarProduct();
        Product independentProduct = createIndependentProduct();

        doReturn(createWebrateAccomTypeAverageRatesDTOs()).when(accomMappingService).getAverageRatesForWebrateAccomTypes();
        doReturn(createAccomClassADRSingleton()).when(accomMappingService).getADRByAccomClasses();
        List<WebrateAccomType> webrateAccomTypes = createWebrateAccomTypes();
        List<AccomClass> accomClasses = createAccomClasses();
        accomMappingService.mapWebrateAccomTypesToAccomClasses(webrateAccomTypes, accomClasses);

        createWebrateCompetitor();
        WebrateCompetitorsAccomClass webrateCompetitorsAccomClass = UniqueWebrateCompetitorsAccomClassCreator.createWebrateCompetitorsAccomClass(0, 0, competitor);
        webrateCompetitorsAccomClass.setAccomClass(accomClasses.get(0));
        webrateCompetitorsAccomClass.setId(100);
        webrateCompetitorsAccomClass.setProductID(barProduct.getId());
        competitor.setStatusId(INACTIVE_STATUS_ID);
        competitor.setWebrateCompetitorsAccomClasses(new HashSet<>(List.of(webrateCompetitorsAccomClass)));

        when(crudService.findByNamedQuery(WebrateAccomClassMapping.BY_PROPERTY_ID, with("propertyId", TEST_PROPERTY_ID).parameters())).thenReturn(Arrays.asList(accomClasses.get(0), accomClasses.get(1), accomClasses.get(2)));
        when(crudService.findAll(WebrateCompetitorsAccomClass.class)).thenReturn(List.of(webrateCompetitorsAccomClass));
        when(crudService.findByNamedQuery(WebrateCompetitors.BY_PROPERTY_ID, with("propertyId", TEST_PROPERTY_ID).parameters())).thenReturn(List.of(competitor));
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.INDEPENDENT_PRODUCTS_ENABLED)).thenReturn(false);

        accomMappingService.updateCompetitorAccomClassReference(TEST_PROPERTY_ID, Arrays.asList(barProduct, independentProduct));
        assertEquals(3, competitor.getWebrateCompetitorsAccomClasses().size());
        List<WebrateCompetitorsAccomClass> resultWebrateCompetitorsAccomClasses = new ArrayList<>(competitor.getWebrateCompetitorsAccomClasses());
        assertEquals(accomClasses.get(1), resultWebrateCompetitorsAccomClasses.get(0).getAccomClass());
        assertEquals(competitor, resultWebrateCompetitorsAccomClasses.get(0).getWebrateCompetitor());
        assertEquals(barProduct.getId(), resultWebrateCompetitorsAccomClasses.get(0).getProductID());
        assertEquals(accomClasses.get(2), resultWebrateCompetitorsAccomClasses.get(1).getAccomClass());
        assertEquals(barProduct.getId(), resultWebrateCompetitorsAccomClasses.get(1).getProductID());
        assertEquals(competitor, resultWebrateCompetitorsAccomClasses.get(1).getWebrateCompetitor());
        assertEquals(accomClasses.get(0), resultWebrateCompetitorsAccomClasses.get(2).getAccomClass());
        assertEquals(barProduct.getId(), resultWebrateCompetitorsAccomClasses.get(2).getProductID());
        assertEquals(100, resultWebrateCompetitorsAccomClasses.get(2).getId());
        assertEquals(competitor, resultWebrateCompetitorsAccomClasses.get(2).getWebrateCompetitor());

        verify(crudService, never()).executeUpdateByNamedQuery(any(String.class), anyMap());
        verify(crudService, never()).delete(any());
    }

    @Test
    @DisplayName("Update Competitor Accom Class Reference _ Independent Products Disabled And RDL Enabled _ Existing Webrate Competitors Accom Class")
    void updateCompetitorAccomClassReference_IndependentProductsDisabledAndRDLInsEnabled_ExistingWebrateCompetitorsAccomClassIsPresent() {

        CrudService crudService = mock(CrudService.class);
        inject(accomMappingService, "crudService", crudService);

        Product linkedProduct = createdLinkedProduct();

        doReturn(createWebrateAccomTypeAverageRatesDTOs()).when(accomMappingService).getAverageRatesForWebrateAccomTypes();
        doReturn(createAccomClassADRSingleton()).when(accomMappingService).getADRByAccomClasses();
        List<WebrateAccomType> webrateAccomTypes = createWebrateAccomTypes();
        List<AccomClass> accomClasses = createAccomClasses();
        accomMappingService.mapWebrateAccomTypesToAccomClasses(webrateAccomTypes, accomClasses);

        createWebrateCompetitor();
        WebrateCompetitorsAccomClass webrateCompetitorsAccomClass = UniqueWebrateCompetitorsAccomClassCreator.createWebrateCompetitorsAccomClass(0, 0, competitor);
        webrateCompetitorsAccomClass.setAccomClass(accomClasses.get(0));
        webrateCompetitorsAccomClass.setId(100);
        webrateCompetitorsAccomClass.setProductID(linkedProduct.getId());
        competitor.setStatusId(INACTIVE_STATUS_ID);
        competitor.setWebrateCompetitorsAccomClasses(new HashSet<>(List.of(webrateCompetitorsAccomClass)));

        ProductAccomType productAccomType = new ProductAccomType();
        productAccomType.setProduct(linkedProduct);
        productAccomType.setAccomType(accomClasses.get(0).getAccomTypes().iterator().next());
        ProductAccomType productAccomType1 = new ProductAccomType();
        productAccomType1.setProduct(linkedProduct);
        productAccomType1.setAccomType(accomClasses.get(1).getAccomTypes().iterator().next());
        ProductAccomType productAccomType2 = new ProductAccomType();
        productAccomType2.setProduct(linkedProduct);
        productAccomType2.setAccomType(accomClasses.get(2).getAccomTypes().iterator().next());

        when(crudService.findAll(ProductAccomType.class)).thenReturn(Arrays.asList(productAccomType, productAccomType1, productAccomType2));


        when(crudService.findByNamedQuery(WebrateAccomClassMapping.BY_PROPERTY_ID, with("propertyId", TEST_PROPERTY_ID).parameters())).thenReturn(Arrays.asList(accomClasses.get(0), accomClasses.get(1), accomClasses.get(2)));
        when(crudService.findAll(WebrateCompetitorsAccomClass.class)).thenReturn(List.of(webrateCompetitorsAccomClass));
        when(crudService.findByNamedQuery(WebrateCompetitors.BY_PROPERTY_ID, with("propertyId", TEST_PROPERTY_ID).parameters())).thenReturn(List.of(competitor));
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.INDEPENDENT_PRODUCTS_ENABLED)).thenReturn(false);
        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.IS_RDL_ENABLED)).thenReturn(true);
        when(propertyService.getPropertyById(TEST_PROPERTY_ID)).thenReturn(getProperty(TEST_UPS_ID));


        accomMappingService.updateCompetitorAccomClassReference(TEST_PROPERTY_ID, Arrays.asList(linkedProduct));
        assertEquals(3, competitor.getWebrateCompetitorsAccomClasses().size());
        List<WebrateCompetitorsAccomClass> resultWebrateCompetitorsAccomClasses = new ArrayList<>(competitor.getWebrateCompetitorsAccomClasses());
        assertEquals(accomClasses.get(1), resultWebrateCompetitorsAccomClasses.get(0).getAccomClass());
        assertEquals(competitor, resultWebrateCompetitorsAccomClasses.get(0).getWebrateCompetitor());
        assertEquals(linkedProduct.getId(), resultWebrateCompetitorsAccomClasses.get(0).getProductID());
        assertEquals(accomClasses.get(2), resultWebrateCompetitorsAccomClasses.get(1).getAccomClass());
        assertEquals(linkedProduct.getId(), resultWebrateCompetitorsAccomClasses.get(1).getProductID());
        assertEquals(competitor, resultWebrateCompetitorsAccomClasses.get(1).getWebrateCompetitor());
        assertEquals(accomClasses.get(0), resultWebrateCompetitorsAccomClasses.get(2).getAccomClass());
        assertEquals(linkedProduct.getId(), resultWebrateCompetitorsAccomClasses.get(2).getProductID());
        assertEquals(100, resultWebrateCompetitorsAccomClasses.get(2).getId());
        assertEquals(competitor, resultWebrateCompetitorsAccomClasses.get(2).getWebrateCompetitor());

        verify(crudService, never()).executeUpdateByNamedQuery(any(String.class), anyMap());
        verify(crudService, never()).delete(any());
    }

    @Test
    @DisplayName("Update Competitor Accom Class Reference _ Independent Products Enabled _ No Existing Webrate Competitors Accom Class")
    void updateCompetitorAccomClassReference_IndependentProductsEnabled_NoExistingWebrateCompetitorsAccomClass() {
        CrudService crudService = mock(CrudService.class);
        inject(accomMappingService, "crudService", crudService);

        Product barProduct = createBarProduct();
        Product independentProduct = createIndependentProduct();

        doReturn(createWebrateAccomTypeAverageRatesDTOs()).when(accomMappingService).getAverageRatesForWebrateAccomTypes();
        doReturn(createAccomClassADRSingleton()).when(accomMappingService).getADRByAccomClasses();
        List<WebrateAccomType> webrateAccomTypes = createWebrateAccomTypes();
        List<AccomClass> accomClasses = createAccomClasses();
        accomMappingService.mapWebrateAccomTypesToAccomClasses(webrateAccomTypes, accomClasses);

        ProductAccomType productAccomType = new ProductAccomType();
        productAccomType.setProduct(independentProduct);
        productAccomType.setAccomType(accomClasses.get(0).getAccomTypes().iterator().next());
        ProductAccomType productAccomType1 = new ProductAccomType();
        productAccomType1.setProduct(independentProduct);
        productAccomType1.setAccomType(accomClasses.get(1).getAccomTypes().iterator().next());
        ProductAccomType productAccomType2 = new ProductAccomType();
        productAccomType2.setProduct(independentProduct);
        productAccomType2.setAccomType(accomClasses.get(2).getAccomTypes().iterator().next());

        when(crudService.findAll(ProductAccomType.class)).thenReturn(Arrays.asList(productAccomType, productAccomType1, productAccomType2));

        createWebrateCompetitor();
        competitor.setStatusId(INACTIVE_STATUS_ID);

        when(crudService.findByNamedQuery(WebrateAccomClassMapping.BY_PROPERTY_ID, with("propertyId", TEST_PROPERTY_ID).parameters())).thenReturn(Arrays.asList(accomClasses.get(0), accomClasses.get(1), accomClasses.get(2)));
        when(crudService.findAll(WebrateCompetitorsAccomClass.class)).thenReturn(List.of());
        when(crudService.findByNamedQuery(WebrateCompetitors.BY_PROPERTY_ID, with("propertyId", TEST_PROPERTY_ID).parameters())).thenReturn(List.of(competitor));
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.INDEPENDENT_PRODUCTS_ENABLED)).thenReturn(true);

        accomMappingService.updateCompetitorAccomClassReference(TEST_PROPERTY_ID, Arrays.asList(barProduct, independentProduct));
        assertEquals(6, competitor.getWebrateCompetitorsAccomClasses().size());
        List<WebrateCompetitorsAccomClass> resultWebrateCompetitorsAccomClasses = new ArrayList<>(competitor.getWebrateCompetitorsAccomClasses());
        assertEquals(accomClasses.get(0), resultWebrateCompetitorsAccomClasses.get(0).getAccomClass());
        assertEquals(barProduct.getId(), resultWebrateCompetitorsAccomClasses.get(0).getProductID());
        assertEquals(competitor, resultWebrateCompetitorsAccomClasses.get(0).getWebrateCompetitor());
        assertEquals(accomClasses.get(1), resultWebrateCompetitorsAccomClasses.get(1).getAccomClass());
        assertEquals(competitor, resultWebrateCompetitorsAccomClasses.get(1).getWebrateCompetitor());
        assertEquals(barProduct.getId(), resultWebrateCompetitorsAccomClasses.get(1).getProductID());
        assertEquals(accomClasses.get(2), resultWebrateCompetitorsAccomClasses.get(2).getAccomClass());
        assertEquals(barProduct.getId(), resultWebrateCompetitorsAccomClasses.get(2).getProductID());
        assertEquals(competitor, resultWebrateCompetitorsAccomClasses.get(2).getWebrateCompetitor());
        assertEquals(accomClasses.get(0), resultWebrateCompetitorsAccomClasses.get(3).getAccomClass());
        assertEquals(independentProduct.getId(), resultWebrateCompetitorsAccomClasses.get(3).getProductID());
        assertEquals(competitor, resultWebrateCompetitorsAccomClasses.get(3).getWebrateCompetitor());
        assertEquals(accomClasses.get(1), resultWebrateCompetitorsAccomClasses.get(4).getAccomClass());
        assertEquals(competitor, resultWebrateCompetitorsAccomClasses.get(4).getWebrateCompetitor());
        assertEquals(independentProduct.getId(), resultWebrateCompetitorsAccomClasses.get(4).getProductID());
        assertEquals(accomClasses.get(2), resultWebrateCompetitorsAccomClasses.get(5).getAccomClass());
        assertEquals(independentProduct.getId(), resultWebrateCompetitorsAccomClasses.get(5).getProductID());
        assertEquals(competitor, resultWebrateCompetitorsAccomClasses.get(5).getWebrateCompetitor());

        verify(crudService, never()).executeUpdateByNamedQuery(any(String.class), anyMap());
        verify(crudService, never()).delete(any());
    }

    @Test
    @DisplayName("Update Competitor Accom Class Reference _ Independent Products Enabled And RDL Enabled_ No Existing Webrate Competitors Accom Class")
    void updateCompetitorAccomClassReference_IndependentProductsEnabledAndRDLIsEnabled_NoExistingWebrateCompetitorsAccomClass() {
        CrudService crudService = mock(CrudService.class);
        inject(accomMappingService, "crudService", crudService);

        Product barProduct = createBarProduct();
        Product independentProduct = createIndependentProduct();

        doReturn(createWebrateAccomTypeAverageRatesDTOs()).when(accomMappingService).getAverageRatesForWebrateAccomTypes();
        doReturn(createAccomClassADRSingleton()).when(accomMappingService).getADRByAccomClasses();
        List<WebrateAccomType> webrateAccomTypes = createWebrateAccomTypes();
        List<AccomClass> accomClasses = createAccomClasses();
        accomMappingService.mapWebrateAccomTypesToAccomClasses(webrateAccomTypes, accomClasses);

        ProductAccomType productAccomType = new ProductAccomType();
        productAccomType.setProduct(independentProduct);
        productAccomType.setAccomType(accomClasses.get(0).getAccomTypes().iterator().next());
        ProductAccomType productAccomType1 = new ProductAccomType();
        productAccomType1.setProduct(independentProduct);
        productAccomType1.setAccomType(accomClasses.get(1).getAccomTypes().iterator().next());
        ProductAccomType productAccomType2 = new ProductAccomType();
        productAccomType2.setProduct(independentProduct);
        productAccomType2.setAccomType(accomClasses.get(2).getAccomTypes().iterator().next());

        when(crudService.findAll(ProductAccomType.class)).thenReturn(Arrays.asList(productAccomType, productAccomType1, productAccomType2));

        createWebrateCompetitor();
        competitor.setStatusId(INACTIVE_STATUS_ID);

        when(crudService.findByNamedQuery(WebrateAccomClassMapping.BY_PROPERTY_ID, with("propertyId", TEST_PROPERTY_ID).parameters())).thenReturn(Arrays.asList(accomClasses.get(0), accomClasses.get(1), accomClasses.get(2)));
        when(crudService.findAll(WebrateCompetitorsAccomClass.class)).thenReturn(List.of());
        when(crudService.findByNamedQuery(WebrateCompetitors.BY_PROPERTY_ID, with("propertyId", TEST_PROPERTY_ID).parameters())).thenReturn(List.of(competitor));
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.INDEPENDENT_PRODUCTS_ENABLED)).thenReturn(true);
        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.IS_RDL_ENABLED)).thenReturn(true);
        when(propertyService.getPropertyById(TEST_PROPERTY_ID)).thenReturn(getProperty(TEST_UPS_ID));

        accomMappingService.updateCompetitorAccomClassReference(TEST_PROPERTY_ID, Arrays.asList(barProduct, independentProduct));
        assertEquals(6, competitor.getWebrateCompetitorsAccomClasses().size());
        List<WebrateCompetitorsAccomClass> resultWebrateCompetitorsAccomClasses = new ArrayList<>(competitor.getWebrateCompetitorsAccomClasses());
        assertEquals(accomClasses.get(0), resultWebrateCompetitorsAccomClasses.get(0).getAccomClass());
        assertEquals(barProduct.getId(), resultWebrateCompetitorsAccomClasses.get(0).getProductID());
        assertEquals(competitor, resultWebrateCompetitorsAccomClasses.get(0).getWebrateCompetitor());
        assertEquals(accomClasses.get(1), resultWebrateCompetitorsAccomClasses.get(1).getAccomClass());
        assertEquals(competitor, resultWebrateCompetitorsAccomClasses.get(1).getWebrateCompetitor());
        assertEquals(barProduct.getId(), resultWebrateCompetitorsAccomClasses.get(1).getProductID());
        assertEquals(accomClasses.get(2), resultWebrateCompetitorsAccomClasses.get(2).getAccomClass());
        assertEquals(barProduct.getId(), resultWebrateCompetitorsAccomClasses.get(2).getProductID());
        assertEquals(competitor, resultWebrateCompetitorsAccomClasses.get(2).getWebrateCompetitor());
        assertEquals(accomClasses.get(0), resultWebrateCompetitorsAccomClasses.get(3).getAccomClass());
        assertEquals(independentProduct.getId(), resultWebrateCompetitorsAccomClasses.get(3).getProductID());
        assertEquals(competitor, resultWebrateCompetitorsAccomClasses.get(3).getWebrateCompetitor());
        assertEquals(accomClasses.get(1), resultWebrateCompetitorsAccomClasses.get(4).getAccomClass());
        assertEquals(competitor, resultWebrateCompetitorsAccomClasses.get(4).getWebrateCompetitor());
        assertEquals(independentProduct.getId(), resultWebrateCompetitorsAccomClasses.get(4).getProductID());
        assertEquals(accomClasses.get(2), resultWebrateCompetitorsAccomClasses.get(5).getAccomClass());
        assertEquals(independentProduct.getId(), resultWebrateCompetitorsAccomClasses.get(5).getProductID());
        assertEquals(competitor, resultWebrateCompetitorsAccomClasses.get(5).getWebrateCompetitor());

        verify(crudService, never()).executeUpdateByNamedQuery(any(String.class), anyMap());
        verify(crudService, never()).delete(any());
    }

    @Test
    @DisplayName("Update Competitor Accom Class Reference _ Independent Products Enabled _ Existing Webrate Competitors Accom Class")
    void updateCompetitorAccomClassReference_IndependentProductsEnabled_ExistingWebrateCompetitorsAccomClass() {
        CrudService crudService = mock(CrudService.class);
        inject(accomMappingService, "crudService", crudService);

        Product barProduct = createBarProduct();
        Product independentProduct = createIndependentProduct();

        doReturn(createWebrateAccomTypeAverageRatesDTOs()).when(accomMappingService).getAverageRatesForWebrateAccomTypes();
        doReturn(createAccomClassADRSingleton()).when(accomMappingService).getADRByAccomClasses();
        List<WebrateAccomType> webrateAccomTypes = createWebrateAccomTypes();
        List<AccomClass> accomClasses = createAccomClasses();
        accomMappingService.mapWebrateAccomTypesToAccomClasses(webrateAccomTypes, accomClasses);

        ProductAccomType productAccomType = new ProductAccomType();
        productAccomType.setProduct(independentProduct);
        productAccomType.setAccomType(accomClasses.get(0).getAccomTypes().iterator().next());
        ProductAccomType productAccomType1 = new ProductAccomType();
        productAccomType1.setProduct(independentProduct);
        productAccomType1.setAccomType(accomClasses.get(1).getAccomTypes().iterator().next());
        ProductAccomType productAccomType2 = new ProductAccomType();
        productAccomType2.setProduct(independentProduct);
        productAccomType2.setAccomType(accomClasses.get(2).getAccomTypes().iterator().next());

        when(crudService.findAll(ProductAccomType.class)).thenReturn(Arrays.asList(productAccomType, productAccomType1, productAccomType2));

        createWebrateCompetitor();
        WebrateCompetitorsAccomClass webrateCompetitorsAccomClass = UniqueWebrateCompetitorsAccomClassCreator.createWebrateCompetitorsAccomClass(0, 0, competitor);
        webrateCompetitorsAccomClass.setAccomClass(accomClasses.get(0));
        webrateCompetitorsAccomClass.setId(100);
        webrateCompetitorsAccomClass.setProductID(barProduct.getId());
        WebrateCompetitorsAccomClass webrateCompetitorsAccomClass1 = UniqueWebrateCompetitorsAccomClassCreator.createWebrateCompetitorsAccomClass(0, 0, competitor);
        webrateCompetitorsAccomClass1.setAccomClass(accomClasses.get(0));
        webrateCompetitorsAccomClass1.setId(101);
        webrateCompetitorsAccomClass1.setProductID(independentProduct.getId());
        competitor.setStatusId(INACTIVE_STATUS_ID);
        competitor.setWebrateCompetitorsAccomClasses(new HashSet<>(Arrays.asList(webrateCompetitorsAccomClass, webrateCompetitorsAccomClass1)));

        when(crudService.findByNamedQuery(WebrateAccomClassMapping.BY_PROPERTY_ID, with("propertyId", TEST_PROPERTY_ID).parameters())).thenReturn(Arrays.asList(accomClasses.get(0), accomClasses.get(1), accomClasses.get(2)));
        when(crudService.findAll(WebrateCompetitorsAccomClass.class)).thenReturn(Arrays.asList(webrateCompetitorsAccomClass, webrateCompetitorsAccomClass1));
        when(crudService.findByNamedQuery(WebrateCompetitors.BY_PROPERTY_ID, with("propertyId", TEST_PROPERTY_ID).parameters())).thenReturn(List.of(competitor));
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.INDEPENDENT_PRODUCTS_ENABLED)).thenReturn(true);

        accomMappingService.updateCompetitorAccomClassReference(TEST_PROPERTY_ID, Arrays.asList(barProduct, independentProduct));
        assertEquals(6, competitor.getWebrateCompetitorsAccomClasses().size());
        List<WebrateCompetitorsAccomClass> resultWebrateCompetitorsAccomClasses = new ArrayList<>(competitor.getWebrateCompetitorsAccomClasses());
        assertEquals(accomClasses.get(1), resultWebrateCompetitorsAccomClasses.get(0).getAccomClass());
        assertEquals(competitor, resultWebrateCompetitorsAccomClasses.get(0).getWebrateCompetitor());
        assertEquals(barProduct.getId(), resultWebrateCompetitorsAccomClasses.get(0).getProductID());
        assertEquals(accomClasses.get(2), resultWebrateCompetitorsAccomClasses.get(1).getAccomClass());
        assertEquals(barProduct.getId(), resultWebrateCompetitorsAccomClasses.get(1).getProductID());
        assertEquals(competitor, resultWebrateCompetitorsAccomClasses.get(1).getWebrateCompetitor());
        assertEquals(accomClasses.get(1), resultWebrateCompetitorsAccomClasses.get(2).getAccomClass());
        assertEquals(competitor, resultWebrateCompetitorsAccomClasses.get(2).getWebrateCompetitor());
        assertEquals(independentProduct.getId(), resultWebrateCompetitorsAccomClasses.get(2).getProductID());
        assertEquals(accomClasses.get(2), resultWebrateCompetitorsAccomClasses.get(3).getAccomClass());
        assertEquals(independentProduct.getId(), resultWebrateCompetitorsAccomClasses.get(3).getProductID());
        assertEquals(competitor, resultWebrateCompetitorsAccomClasses.get(3).getWebrateCompetitor());
        assertEquals(accomClasses.get(0), resultWebrateCompetitorsAccomClasses.get(4).getAccomClass());
        assertEquals(barProduct.getId(), resultWebrateCompetitorsAccomClasses.get(4).getProductID());
        assertEquals(100, resultWebrateCompetitorsAccomClasses.get(4).getId());
        assertEquals(competitor, resultWebrateCompetitorsAccomClasses.get(4).getWebrateCompetitor());
        assertEquals(accomClasses.get(0), resultWebrateCompetitorsAccomClasses.get(5).getAccomClass());
        assertEquals(independentProduct.getId(), resultWebrateCompetitorsAccomClasses.get(5).getProductID());
        assertEquals(101, resultWebrateCompetitorsAccomClasses.get(5).getId());
        assertEquals(competitor, resultWebrateCompetitorsAccomClasses.get(5).getWebrateCompetitor());

        verify(crudService, never()).executeUpdateByNamedQuery(any(String.class), anyMap());
        verify(crudService, never()).delete(any());
    }

    @Test
    void testIsPropertyCMPCActiveWithZeroCount() {
        CrudService crudService = mock(CrudService.class);
        when(crudService.findByNamedQuerySingleResult(WebrateRankingAccomClass.COUNT_OF_NON_NONE_CONSTRAINTS)).thenReturn(0);
        when(crudService.findByNamedQuerySingleResult(WebrateRankingAccomClassOverride.COUNT_ALL)).thenReturn(0);

        assertFalse(accomMappingService.isPropertyCompetitiveMarketPositionConstraintsActive());
    }

    @Test
    @DisplayName("Update Competitor Accom Class Reference _ Independent Products Enabled And RDL Enabled_ Existing Webrate Competitors Accom Class")
    void updateCompetitorAccomClassReference_IndependentProductsEnabledAndRDLIsEnabled_ExistingWebrateCompetitorsAccomClass() {
        CrudService crudService = mock(CrudService.class);
        inject(accomMappingService, "crudService", crudService);

        Product barProduct = createBarProduct();
        Product independentProduct = createIndependentProduct();

        doReturn(createWebrateAccomTypeAverageRatesDTOs()).when(accomMappingService).getAverageRatesForWebrateAccomTypes();
        doReturn(createAccomClassADRSingleton()).when(accomMappingService).getADRByAccomClasses();
        List<WebrateAccomType> webrateAccomTypes = createWebrateAccomTypes();
        List<AccomClass> accomClasses = createAccomClasses();
        accomMappingService.mapWebrateAccomTypesToAccomClasses(webrateAccomTypes, accomClasses);

        ProductAccomType productAccomType = new ProductAccomType();
        productAccomType.setProduct(independentProduct);
        productAccomType.setAccomType(accomClasses.get(0).getAccomTypes().iterator().next());
        ProductAccomType productAccomType1 = new ProductAccomType();
        productAccomType1.setProduct(independentProduct);
        productAccomType1.setAccomType(accomClasses.get(1).getAccomTypes().iterator().next());
        ProductAccomType productAccomType2 = new ProductAccomType();
        productAccomType2.setProduct(independentProduct);
        productAccomType2.setAccomType(accomClasses.get(2).getAccomTypes().iterator().next());

        when(crudService.findAll(ProductAccomType.class)).thenReturn(Arrays.asList(productAccomType, productAccomType1, productAccomType2));

        createWebrateCompetitor();
        WebrateCompetitorsAccomClass webrateCompetitorsAccomClass = UniqueWebrateCompetitorsAccomClassCreator.createWebrateCompetitorsAccomClass(0, 0, competitor);
        webrateCompetitorsAccomClass.setAccomClass(accomClasses.get(0));
        webrateCompetitorsAccomClass.setId(100);
        webrateCompetitorsAccomClass.setProductID(barProduct.getId());
        WebrateCompetitorsAccomClass webrateCompetitorsAccomClass1 = UniqueWebrateCompetitorsAccomClassCreator.createWebrateCompetitorsAccomClass(0, 0, competitor);
        webrateCompetitorsAccomClass1.setAccomClass(accomClasses.get(0));
        webrateCompetitorsAccomClass1.setId(101);
        webrateCompetitorsAccomClass1.setProductID(independentProduct.getId());
        competitor.setStatusId(INACTIVE_STATUS_ID);
        competitor.setWebrateCompetitorsAccomClasses(new HashSet<>(Arrays.asList(webrateCompetitorsAccomClass, webrateCompetitorsAccomClass1)));

        when(crudService.findByNamedQuery(WebrateAccomClassMapping.BY_PROPERTY_ID, with("propertyId", TEST_PROPERTY_ID).parameters())).thenReturn(Arrays.asList(accomClasses.get(0), accomClasses.get(1), accomClasses.get(2)));
        when(crudService.findAll(WebrateCompetitorsAccomClass.class)).thenReturn(Arrays.asList(webrateCompetitorsAccomClass, webrateCompetitorsAccomClass1));
        when(crudService.findByNamedQuery(WebrateCompetitors.BY_PROPERTY_ID, with("propertyId", TEST_PROPERTY_ID).parameters())).thenReturn(List.of(competitor));
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.INDEPENDENT_PRODUCTS_ENABLED)).thenReturn(true);
        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.IS_RDL_ENABLED)).thenReturn(true);
        when(propertyService.getPropertyById(TEST_PROPERTY_ID)).thenReturn(getProperty(TEST_UPS_ID));

        accomMappingService.updateCompetitorAccomClassReference(TEST_PROPERTY_ID, Arrays.asList(barProduct, independentProduct));
        assertEquals(6, competitor.getWebrateCompetitorsAccomClasses().size());
        List<WebrateCompetitorsAccomClass> resultWebrateCompetitorsAccomClasses = new ArrayList<>(competitor.getWebrateCompetitorsAccomClasses());
        assertEquals(accomClasses.get(1), resultWebrateCompetitorsAccomClasses.get(0).getAccomClass());
        assertEquals(competitor, resultWebrateCompetitorsAccomClasses.get(0).getWebrateCompetitor());
        assertEquals(barProduct.getId(), resultWebrateCompetitorsAccomClasses.get(0).getProductID());
        assertEquals(accomClasses.get(2), resultWebrateCompetitorsAccomClasses.get(1).getAccomClass());
        assertEquals(barProduct.getId(), resultWebrateCompetitorsAccomClasses.get(1).getProductID());
        assertEquals(competitor, resultWebrateCompetitorsAccomClasses.get(1).getWebrateCompetitor());
        assertEquals(accomClasses.get(1), resultWebrateCompetitorsAccomClasses.get(2).getAccomClass());
        assertEquals(competitor, resultWebrateCompetitorsAccomClasses.get(2).getWebrateCompetitor());
        assertEquals(independentProduct.getId(), resultWebrateCompetitorsAccomClasses.get(2).getProductID());
        assertEquals(accomClasses.get(2), resultWebrateCompetitorsAccomClasses.get(3).getAccomClass());
        assertEquals(independentProduct.getId(), resultWebrateCompetitorsAccomClasses.get(3).getProductID());
        assertEquals(competitor, resultWebrateCompetitorsAccomClasses.get(3).getWebrateCompetitor());
        assertEquals(accomClasses.get(0), resultWebrateCompetitorsAccomClasses.get(4).getAccomClass());
        assertEquals(barProduct.getId(), resultWebrateCompetitorsAccomClasses.get(4).getProductID());
        assertEquals(100, resultWebrateCompetitorsAccomClasses.get(4).getId());
        assertEquals(competitor, resultWebrateCompetitorsAccomClasses.get(4).getWebrateCompetitor());
        assertEquals(accomClasses.get(0), resultWebrateCompetitorsAccomClasses.get(5).getAccomClass());
        assertEquals(independentProduct.getId(), resultWebrateCompetitorsAccomClasses.get(5).getProductID());
        assertEquals(101, resultWebrateCompetitorsAccomClasses.get(5).getId());
        assertEquals(competitor, resultWebrateCompetitorsAccomClasses.get(5).getWebrateCompetitor());

        verify(crudService, never()).executeUpdateByNamedQuery(any(String.class), anyMap());
        verify(crudService, never()).delete(any());
    }

    @Test
    @DisplayName("Update Competitor Accom Class Reference _ Independent Products Enabled _ Existing Webrate Competitors Accom Class _ Delete")
    void updateCompetitorAccomClassReference_IndependentProductsEnabled_ExistingWebrateCompetitorsAccomClass_Delete() {
        CrudService crudService = mock(CrudService.class);
        inject(accomMappingService, "crudService", crudService);

        Product barProduct = createBarProduct();
        Product independentProduct = createIndependentProduct();

        doReturn(createWebrateAccomTypeAverageRatesDTOs()).when(accomMappingService).getAverageRatesForWebrateAccomTypes();
        doReturn(createAccomClassADRSingleton()).when(accomMappingService).getADRByAccomClasses();
        List<WebrateAccomType> webrateAccomTypes = createWebrateAccomTypes();
        List<AccomClass> accomClasses = createAccomClasses();
        accomMappingService.mapWebrateAccomTypesToAccomClasses(webrateAccomTypes, accomClasses);

        ProductAccomType productAccomType = new ProductAccomType();
        productAccomType.setProduct(independentProduct);
        productAccomType.setAccomType(accomClasses.get(0).getAccomTypes().iterator().next());
        ProductAccomType productAccomType1 = new ProductAccomType();
        productAccomType1.setProduct(independentProduct);
        productAccomType1.setAccomType(accomClasses.get(1).getAccomTypes().iterator().next());
        when(crudService.findAll(ProductAccomType.class)).thenReturn(Arrays.asList(productAccomType, productAccomType1));

        createWebrateCompetitor();
        WebrateCompetitorsAccomClass webrateCompetitorsAccomClass = UniqueWebrateCompetitorsAccomClassCreator.createWebrateCompetitorsAccomClass(0, 0, competitor);
        webrateCompetitorsAccomClass.setAccomClass(accomClasses.get(0));
        webrateCompetitorsAccomClass.setId(100);
        webrateCompetitorsAccomClass.setProductID(barProduct.getId());
        WebrateCompetitorsAccomClass webrateCompetitorsAccomClass1 = UniqueWebrateCompetitorsAccomClassCreator.createWebrateCompetitorsAccomClass(0, 0, competitor);
        webrateCompetitorsAccomClass1.setAccomClass(accomClasses.get(0));
        webrateCompetitorsAccomClass1.setId(101);
        webrateCompetitorsAccomClass1.setProductID(independentProduct.getId());
        WebrateCompetitorsAccomClass webrateCompetitorsAccomClassToDelete = UniqueWebrateCompetitorsAccomClassCreator.createWebrateCompetitorsAccomClass(0, 0, competitor);
        webrateCompetitorsAccomClassToDelete.setAccomClass(accomClasses.get(2));
        webrateCompetitorsAccomClassToDelete.setId(102);
        webrateCompetitorsAccomClassToDelete.setProductID(independentProduct.getId());
        WebrateOverrideCompetitor webrateOverrideCompetitor = new WebrateOverrideCompetitor();
        webrateOverrideCompetitor.setId(222);
        WebrateOverrideCompetitorDetails webrateOverrideCompetitorDetails = new WebrateOverrideCompetitorDetails();
        webrateOverrideCompetitorDetails.setId(333);
        webrateOverrideCompetitorDetails.setWebrateCompetitorsAccomClass(webrateCompetitorsAccomClassToDelete);
        webrateOverrideCompetitorDetails.setWebrateOverrideCompetitor(webrateOverrideCompetitor);
        webrateOverrideCompetitor.setWebrateOverrideCompetitorDetails(new HashSet<>(List.of(webrateOverrideCompetitorDetails)));
        competitor.setStatusId(INACTIVE_STATUS_ID);
        competitor.setWebrateCompetitorsAccomClasses(new HashSet<>(Arrays.asList(webrateCompetitorsAccomClass, webrateCompetitorsAccomClass1, webrateCompetitorsAccomClassToDelete)));

        when(crudService.findByNamedQuery(WebrateAccomClassMapping.BY_PROPERTY_ID, with("propertyId", TEST_PROPERTY_ID).parameters())).thenReturn(Arrays.asList(accomClasses.get(0), accomClasses.get(1), accomClasses.get(2)));
        when(crudService.findAll(WebrateCompetitorsAccomClass.class)).thenReturn(Arrays.asList(webrateCompetitorsAccomClass, webrateCompetitorsAccomClass1, webrateCompetitorsAccomClassToDelete));
        when(crudService.findByNamedQuery(WebrateCompetitors.BY_PROPERTY_ID, with("propertyId", TEST_PROPERTY_ID).parameters())).thenReturn(List.of(competitor));
        when(crudService.findByNamedQuery(WebrateOverrideCompetitorDetails.BY_ACCOMMAPPING_IDS, with("compAccomClassIds", List.of(102)).parameters())).thenReturn(List.of(webrateOverrideCompetitorDetails));
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.INDEPENDENT_PRODUCTS_ENABLED)).thenReturn(true);

        accomMappingService.updateCompetitorAccomClassReference(TEST_PROPERTY_ID, Arrays.asList(barProduct, independentProduct));
        assertEquals(5, competitor.getWebrateCompetitorsAccomClasses().size());
        List<WebrateCompetitorsAccomClass> resultWebrateCompetitorsAccomClasses = new ArrayList<>(competitor.getWebrateCompetitorsAccomClasses());
        assertEquals(accomClasses.get(1), resultWebrateCompetitorsAccomClasses.get(0).getAccomClass());
        assertEquals(competitor, resultWebrateCompetitorsAccomClasses.get(0).getWebrateCompetitor());
        assertEquals(barProduct.getId(), resultWebrateCompetitorsAccomClasses.get(0).getProductID());
        assertEquals(accomClasses.get(2), resultWebrateCompetitorsAccomClasses.get(1).getAccomClass());
        assertEquals(barProduct.getId(), resultWebrateCompetitorsAccomClasses.get(1).getProductID());
        assertEquals(competitor, resultWebrateCompetitorsAccomClasses.get(1).getWebrateCompetitor());
        assertEquals(accomClasses.get(1), resultWebrateCompetitorsAccomClasses.get(2).getAccomClass());
        assertEquals(competitor, resultWebrateCompetitorsAccomClasses.get(2).getWebrateCompetitor());
        assertEquals(independentProduct.getId(), resultWebrateCompetitorsAccomClasses.get(2).getProductID());
        assertEquals(accomClasses.get(0), resultWebrateCompetitorsAccomClasses.get(3).getAccomClass());
        assertEquals(barProduct.getId(), resultWebrateCompetitorsAccomClasses.get(3).getProductID());
        assertEquals(100, resultWebrateCompetitorsAccomClasses.get(3).getId());
        assertEquals(competitor, resultWebrateCompetitorsAccomClasses.get(3).getWebrateCompetitor());
        assertEquals(accomClasses.get(0), resultWebrateCompetitorsAccomClasses.get(4).getAccomClass());
        assertEquals(independentProduct.getId(), resultWebrateCompetitorsAccomClasses.get(4).getProductID());
        assertEquals(101, resultWebrateCompetitorsAccomClasses.get(4).getId());
        assertEquals(competitor, resultWebrateCompetitorsAccomClasses.get(4).getWebrateCompetitor());

        verify(crudService).findByNamedQuery(WebrateOverrideCompetitorDetails.BY_ACCOMMAPPING_IDS,
                QueryParameter.with("compAccomClassIds", List.of(102)).parameters());
        verify(crudService).delete(List.of(webrateOverrideCompetitorDetails));
        verify(crudService).delete(List.of(webrateOverrideCompetitor));
        verify(crudService).delete(List.of(webrateCompetitorsAccomClassToDelete));
    }

    @Test
    @DisplayName("Update Competitor Accom Class Reference _ Independent Products Enabled AND RDL Enabled_ Existing Webrate Competitors Accom Class _ Delete")
    void updateCompetitorAccomClassReference_IndependentProductsDisabledAndRDLISEnabled_ExistingWebrateCompetitorsAccomClass_Delete() {
        CrudService crudService = mock(CrudService.class);
        inject(accomMappingService, "crudService", crudService);

        Product linkedProduct = createdLinkedProduct();

        doReturn(createWebrateAccomTypeAverageRatesDTOs()).when(accomMappingService).getAverageRatesForWebrateAccomTypes();
        doReturn(createAccomClassADRSingleton()).when(accomMappingService).getADRByAccomClasses();
        List<WebrateAccomType> webrateAccomTypes = createWebrateAccomTypes();
        List<AccomClass> accomClasses = createAccomClasses();
        accomMappingService.mapWebrateAccomTypesToAccomClasses(webrateAccomTypes, accomClasses);

        ProductAccomType productAccomType = new ProductAccomType();
        productAccomType.setProduct(linkedProduct);
        productAccomType.setAccomType(accomClasses.get(0).getAccomTypes().iterator().next());
        ProductAccomType productAccomType1 = new ProductAccomType();
        productAccomType1.setProduct(linkedProduct);
        productAccomType1.setAccomType(accomClasses.get(1).getAccomTypes().iterator().next());
        when(crudService.findAll(ProductAccomType.class)).thenReturn(Arrays.asList(productAccomType, productAccomType1));

        createWebrateCompetitor();
        WebrateCompetitorsAccomClass webrateCompetitorsAccomClass = UniqueWebrateCompetitorsAccomClassCreator.createWebrateCompetitorsAccomClass(0, 0, competitor);
        webrateCompetitorsAccomClass.setAccomClass(accomClasses.get(0));
        webrateCompetitorsAccomClass.setId(100);
        webrateCompetitorsAccomClass.setProductID(linkedProduct.getId());
        WebrateCompetitorsAccomClass webrateCompetitorsAccomClass1 = UniqueWebrateCompetitorsAccomClassCreator.createWebrateCompetitorsAccomClass(0, 0, competitor);
        webrateCompetitorsAccomClass1.setAccomClass(accomClasses.get(0));
        webrateCompetitorsAccomClass1.setId(101);
        webrateCompetitorsAccomClass1.setProductID(linkedProduct.getId());
        WebrateCompetitorsAccomClass webrateCompetitorsAccomClassToDelete = UniqueWebrateCompetitorsAccomClassCreator.createWebrateCompetitorsAccomClass(0, 0, competitor);
        webrateCompetitorsAccomClassToDelete.setAccomClass(accomClasses.get(2));
        webrateCompetitorsAccomClassToDelete.setId(102);
        webrateCompetitorsAccomClassToDelete.setProductID(linkedProduct.getId());
        WebrateOverrideCompetitor webrateOverrideCompetitor = new WebrateOverrideCompetitor();
        webrateOverrideCompetitor.setId(222);
        WebrateOverrideCompetitorDetails webrateOverrideCompetitorDetails = new WebrateOverrideCompetitorDetails();
        webrateOverrideCompetitorDetails.setId(333);
        webrateOverrideCompetitorDetails.setWebrateCompetitorsAccomClass(webrateCompetitorsAccomClassToDelete);
        webrateOverrideCompetitorDetails.setWebrateOverrideCompetitor(webrateOverrideCompetitor);
        webrateOverrideCompetitor.setWebrateOverrideCompetitorDetails(new HashSet<>(List.of(webrateOverrideCompetitorDetails)));
        competitor.setStatusId(INACTIVE_STATUS_ID);
        competitor.setWebrateCompetitorsAccomClasses(new HashSet<>(Arrays.asList(webrateCompetitorsAccomClass, webrateCompetitorsAccomClass1, webrateCompetitorsAccomClassToDelete)));

        when(crudService.findByNamedQuery(WebrateAccomClassMapping.BY_PROPERTY_ID, with("propertyId", TEST_PROPERTY_ID).parameters())).thenReturn(Arrays.asList(accomClasses.get(0), accomClasses.get(1), accomClasses.get(2)));
        when(crudService.findAll(WebrateCompetitorsAccomClass.class)).thenReturn(Arrays.asList(webrateCompetitorsAccomClass, webrateCompetitorsAccomClass1, webrateCompetitorsAccomClassToDelete));
        when(crudService.findByNamedQuery(WebrateCompetitors.BY_PROPERTY_ID, with("propertyId", TEST_PROPERTY_ID).parameters())).thenReturn(List.of(competitor));
        when(crudService.findByNamedQuery(WebrateOverrideCompetitorDetails.BY_ACCOMMAPPING_IDS, with("compAccomClassIds", List.of(102)).parameters())).thenReturn(List.of(webrateOverrideCompetitorDetails));
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.INDEPENDENT_PRODUCTS_ENABLED)).thenReturn(false);
        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.IS_RDL_ENABLED)).thenReturn(true);
        when(propertyService.getPropertyById(TEST_PROPERTY_ID)).thenReturn(getProperty(TEST_UPS_ID));

        accomMappingService.updateCompetitorAccomClassReference(TEST_PROPERTY_ID, Arrays.asList(linkedProduct));

        assertEquals(3, competitor.getWebrateCompetitorsAccomClasses().size());
        List<WebrateCompetitorsAccomClass> resultWebrateCompetitorsAccomClasses = new ArrayList<>(competitor.getWebrateCompetitorsAccomClasses());
        assertEquals(accomClasses.get(1), resultWebrateCompetitorsAccomClasses.get(0).getAccomClass());
        assertEquals(competitor, resultWebrateCompetitorsAccomClasses.get(0).getWebrateCompetitor());
        assertEquals(linkedProduct.getId(), resultWebrateCompetitorsAccomClasses.get(0).getProductID());
        assertEquals(accomClasses.get(0), resultWebrateCompetitorsAccomClasses.get(1).getAccomClass());
        assertEquals(linkedProduct.getId(), resultWebrateCompetitorsAccomClasses.get(1).getProductID());
        assertEquals(100, resultWebrateCompetitorsAccomClasses.get(1).getId());
        assertEquals(competitor, resultWebrateCompetitorsAccomClasses.get(1).getWebrateCompetitor());
        assertEquals(accomClasses.get(0), resultWebrateCompetitorsAccomClasses.get(2).getAccomClass());
        assertEquals(linkedProduct.getId(), resultWebrateCompetitorsAccomClasses.get(2).getProductID());
        assertEquals(101, resultWebrateCompetitorsAccomClasses.get(2).getId());
        assertEquals(competitor, resultWebrateCompetitorsAccomClasses.get(2).getWebrateCompetitor());

        verify(crudService).findByNamedQuery(WebrateOverrideCompetitorDetails.BY_ACCOMMAPPING_IDS,
                QueryParameter.with("compAccomClassIds", List.of(102)).parameters());
        verify(crudService).delete(List.of(webrateOverrideCompetitorDetails));
        verify(crudService).delete(List.of(webrateOverrideCompetitor));
        verify(crudService).delete(List.of(webrateCompetitorsAccomClassToDelete));
    }

    @Test
    public void deleteWebrateOverrideCompetitors() {
        CrudService crudService = mock(CrudService.class);
        inject(accomMappingService, "crudService", crudService);

        List<AccomClass> accomClasses = createAccomClasses();

        createWebrateCompetitor();
        WebrateCompetitorsAccomClass webrateCompetitorsAccomClassToDelete = UniqueWebrateCompetitorsAccomClassCreator.createWebrateCompetitorsAccomClass(0, 0, competitor);
        webrateCompetitorsAccomClassToDelete.setAccomClass(accomClasses.get(2));
        webrateCompetitorsAccomClassToDelete.setId(102);
        WebrateOverrideCompetitor webrateOverrideCompetitor = new WebrateOverrideCompetitor();
        webrateOverrideCompetitor.setId(222);
        WebrateOverrideCompetitorDetails webrateOverrideCompetitorDetails = new WebrateOverrideCompetitorDetails();
        webrateOverrideCompetitorDetails.setId(333);
        webrateOverrideCompetitorDetails.setWebrateCompetitorsAccomClass(webrateCompetitorsAccomClassToDelete);
        webrateOverrideCompetitorDetails.setWebrateOverrideCompetitor(webrateOverrideCompetitor);
        webrateOverrideCompetitor.setWebrateOverrideCompetitorDetails(new HashSet<>(List.of(webrateOverrideCompetitorDetails)));
        competitor.setStatusId(INACTIVE_STATUS_ID);
        competitor.setWebrateCompetitorsAccomClasses(new HashSet<>(List.of(webrateCompetitorsAccomClassToDelete)));

        when(crudService.findByNamedQuery(WebrateAccomClassMapping.BY_PROPERTY_ID, with("propertyId", TEST_PROPERTY_ID).parameters())).thenReturn(Arrays.asList(accomClasses.get(0), accomClasses.get(1), accomClasses.get(2)));
        when(crudService.findAll(WebrateCompetitorsAccomClass.class)).thenReturn(List.of(webrateCompetitorsAccomClassToDelete));
        when(crudService.findByNamedQuery(WebrateCompetitors.BY_PROPERTY_ID, with("propertyId", TEST_PROPERTY_ID).parameters())).thenReturn(List.of(competitor));
        when(crudService.findByNamedQuery(WebrateOverrideCompetitorDetails.BY_ACCOMMAPPING_IDS, with("compAccomClassIds", List.of(102)).parameters())).thenReturn(List.of(webrateOverrideCompetitorDetails));
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.INDEPENDENT_PRODUCTS_ENABLED)).thenReturn(true);
        when(crudService.findByNamedQuery(WebrateCompetitorsAccomClass.BY_ACCOM_CLASS, QueryParameter.with("accomClassList", singletonList(accomClasses.get(2))).parameters())).thenReturn(List.of(webrateCompetitorsAccomClassToDelete));

        accomMappingService.deleteWebrateOverrideCompetitors(singletonList(accomClasses.get(2)));

        verify(crudService).findByNamedQuery(WebrateCompetitorsAccomClass.BY_ACCOM_CLASS,
                QueryParameter.with("accomClassList", singletonList(accomClasses.get(2))).parameters());
        verify(crudService).findByNamedQuery(WebrateOverrideCompetitorDetails.BY_ACCOMMAPPING_IDS,
                QueryParameter.with("compAccomClassIds", List.of(102)).parameters());
        verify(crudService).delete(List.of(webrateOverrideCompetitorDetails));
        verify(crudService).delete(List.of(webrateOverrideCompetitor));
        verify(crudService).delete(List.of(webrateCompetitorsAccomClassToDelete));
    }

    @Test
    @DisplayName("Update Competitor Accom Class Reference _ Independent Products Disabled _ No Existing Webrate Competitors Accom Class _ NoWebrateAccomClassMappingsToBeDeleted")
    void updateCompetitorAccomClassReference_IndependentProductsDisabled_NoExistingWebrateCompetitorsAccomClass_NoWebrateAccomClassMappingsToBeDeleted() {
        CrudService crudService = mock(CrudService.class);
        inject(accomMappingService, "crudService", crudService);

        Product barProduct = createBarProduct();
        Product independentProduct = createIndependentProduct();

        doReturn(createWebrateAccomTypeAverageRatesDTOs()).when(accomMappingService).getAverageRatesForWebrateAccomTypes();
        doReturn(createAccomClassADRSingleton()).when(accomMappingService).getADRByAccomClasses();
        List<WebrateAccomType> webrateAccomTypes = createWebrateAccomTypes();
        List<AccomClass> accomClasses = createAccomClasses();
        accomMappingService.mapWebrateAccomTypesToAccomClasses(webrateAccomTypes, accomClasses);

        createWebrateCompetitor();
        competitor.setStatusId(INACTIVE_STATUS_ID);

        WebrateAccomClassMapping webrateAccomClassMapping1 = new WebrateAccomClassMapping();
        webrateAccomClassMapping1.setAccomClass(accomClasses.get(0));
        WebrateAccomClassMapping webrateAccomClassMapping2 = new WebrateAccomClassMapping();
        webrateAccomClassMapping2.setAccomClass(accomClasses.get(1));
        WebrateAccomClassMapping webrateAccomClassMapping3 = new WebrateAccomClassMapping();
        webrateAccomClassMapping3.setAccomClass(accomClasses.get(2));

        when(crudService.findAll(WebrateAccomClassMapping.class)).thenReturn(Arrays.asList(webrateAccomClassMapping1, webrateAccomClassMapping2, webrateAccomClassMapping3));
        when(crudService.findAll(WebrateCompetitorsAccomClass.class)).thenReturn(List.of());
        when(crudService.findByNamedQuery(WebrateCompetitors.BY_PROPERTY_ID, with("propertyId", TEST_PROPERTY_ID).parameters())).thenReturn(List.of(competitor));
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.INDEPENDENT_PRODUCTS_ENABLED)).thenReturn(false);

        accomMappingService.updateCompetitorAccomClassReference(TEST_PROPERTY_ID, Arrays.asList(barProduct, independentProduct), new HashSet<>(List.of()));
        assertEquals(3, competitor.getWebrateCompetitorsAccomClasses().size());
        List<WebrateCompetitorsAccomClass> resultWebrateCompetitorsAccomClasses = new ArrayList<>(competitor.getWebrateCompetitorsAccomClasses());
        assertEquals(accomClasses.get(0), resultWebrateCompetitorsAccomClasses.get(0).getAccomClass());
        assertEquals(barProduct.getId(), resultWebrateCompetitorsAccomClasses.get(0).getProductID());
        assertEquals(competitor, resultWebrateCompetitorsAccomClasses.get(0).getWebrateCompetitor());
        assertEquals(accomClasses.get(1), resultWebrateCompetitorsAccomClasses.get(1).getAccomClass());
        assertEquals(competitor, resultWebrateCompetitorsAccomClasses.get(1).getWebrateCompetitor());
        assertEquals(barProduct.getId(), resultWebrateCompetitorsAccomClasses.get(1).getProductID());
        assertEquals(accomClasses.get(2), resultWebrateCompetitorsAccomClasses.get(2).getAccomClass());
        assertEquals(barProduct.getId(), resultWebrateCompetitorsAccomClasses.get(2).getProductID());
        assertEquals(competitor, resultWebrateCompetitorsAccomClasses.get(2).getWebrateCompetitor());

        verify(crudService, never()).executeUpdateByNamedQuery(any(String.class), anyMap());
        verify(crudService, never()).delete(any());
    }

    @Test
    @DisplayName("Update Competitor Accom Class Reference _ Independent Products Disabled _ Existing Webrate Competitors Accom Class _ NoWebrateAccomClassMappingsToBeDeleted")
    void updateCompetitorAccomClassReference_IndependentProductsDisabled_ExistingWebrateCompetitorsAccomClass_NoWebrateAccomClassMappingsToBeDeleted() {
        CrudService crudService = mock(CrudService.class);
        inject(accomMappingService, "crudService", crudService);

        Product barProduct = createBarProduct();
        Product independentProduct = createIndependentProduct();

        doReturn(createWebrateAccomTypeAverageRatesDTOs()).when(accomMappingService).getAverageRatesForWebrateAccomTypes();
        doReturn(createAccomClassADRSingleton()).when(accomMappingService).getADRByAccomClasses();
        List<WebrateAccomType> webrateAccomTypes = createWebrateAccomTypes();
        List<AccomClass> accomClasses = createAccomClasses();
        accomMappingService.mapWebrateAccomTypesToAccomClasses(webrateAccomTypes, accomClasses);

        createWebrateCompetitor();
        WebrateCompetitorsAccomClass webrateCompetitorsAccomClass = UniqueWebrateCompetitorsAccomClassCreator.createWebrateCompetitorsAccomClass(0, 0, competitor);
        webrateCompetitorsAccomClass.setAccomClass(accomClasses.get(0));
        webrateCompetitorsAccomClass.setId(100);
        webrateCompetitorsAccomClass.setProductID(barProduct.getId());
        competitor.setStatusId(INACTIVE_STATUS_ID);
        competitor.setWebrateCompetitorsAccomClasses(new HashSet<>(List.of(webrateCompetitorsAccomClass)));

        WebrateAccomClassMapping webrateAccomClassMapping1 = new WebrateAccomClassMapping();
        webrateAccomClassMapping1.setAccomClass(accomClasses.get(0));
        WebrateAccomClassMapping webrateAccomClassMapping2 = new WebrateAccomClassMapping();
        webrateAccomClassMapping2.setAccomClass(accomClasses.get(1));
        WebrateAccomClassMapping webrateAccomClassMapping3 = new WebrateAccomClassMapping();
        webrateAccomClassMapping3.setAccomClass(accomClasses.get(2));

        when(crudService.findAll(WebrateAccomClassMapping.class)).thenReturn(Arrays.asList(webrateAccomClassMapping1, webrateAccomClassMapping2, webrateAccomClassMapping3));
        when(crudService.findAll(WebrateCompetitorsAccomClass.class)).thenReturn(List.of(webrateCompetitorsAccomClass));
        when(crudService.findByNamedQuery(WebrateCompetitors.BY_PROPERTY_ID, with("propertyId", TEST_PROPERTY_ID).parameters())).thenReturn(List.of(competitor));
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.INDEPENDENT_PRODUCTS_ENABLED)).thenReturn(false);

        accomMappingService.updateCompetitorAccomClassReference(TEST_PROPERTY_ID, Arrays.asList(barProduct, independentProduct), new HashSet<>(List.of()));
        assertEquals(3, competitor.getWebrateCompetitorsAccomClasses().size());
        List<WebrateCompetitorsAccomClass> resultWebrateCompetitorsAccomClasses = new ArrayList<>(competitor.getWebrateCompetitorsAccomClasses());
        assertEquals(accomClasses.get(1), resultWebrateCompetitorsAccomClasses.get(0).getAccomClass());
        assertEquals(competitor, resultWebrateCompetitorsAccomClasses.get(0).getWebrateCompetitor());
        assertEquals(barProduct.getId(), resultWebrateCompetitorsAccomClasses.get(0).getProductID());
        assertEquals(accomClasses.get(2), resultWebrateCompetitorsAccomClasses.get(1).getAccomClass());
        assertEquals(barProduct.getId(), resultWebrateCompetitorsAccomClasses.get(1).getProductID());
        assertEquals(competitor, resultWebrateCompetitorsAccomClasses.get(1).getWebrateCompetitor());
        assertEquals(accomClasses.get(0), resultWebrateCompetitorsAccomClasses.get(2).getAccomClass());
        assertEquals(barProduct.getId(), resultWebrateCompetitorsAccomClasses.get(2).getProductID());
        assertEquals(100, resultWebrateCompetitorsAccomClasses.get(2).getId());
        assertEquals(competitor, resultWebrateCompetitorsAccomClasses.get(2).getWebrateCompetitor());

        verify(crudService, never()).executeUpdateByNamedQuery(any(String.class), anyMap());
        verify(crudService, never()).delete(any());
    }

    @Test
    @DisplayName("Update Competitor Accom Class Reference _ Independent Products Enabled _ No Existing Webrate Competitors Accom Class _ NoWebrateAccomClassMappingsToBeDeleted")
    void updateCompetitorAccomClassReference_IndependentProductsEnabled_NoExistingWebrateCompetitorsAccomClass_NoWebrateAccomClassMappingsToBeDeleted() {
        CrudService crudService = mock(CrudService.class);
        inject(accomMappingService, "crudService", crudService);

        Product barProduct = createBarProduct();
        Product independentProduct = createIndependentProduct();

        doReturn(createWebrateAccomTypeAverageRatesDTOs()).when(accomMappingService).getAverageRatesForWebrateAccomTypes();
        doReturn(createAccomClassADRSingleton()).when(accomMappingService).getADRByAccomClasses();
        List<WebrateAccomType> webrateAccomTypes = createWebrateAccomTypes();
        List<AccomClass> accomClasses = createAccomClasses();
        accomMappingService.mapWebrateAccomTypesToAccomClasses(webrateAccomTypes, accomClasses);

        ProductAccomType productAccomType = new ProductAccomType();
        productAccomType.setProduct(independentProduct);
        productAccomType.setAccomType(accomClasses.get(0).getAccomTypes().iterator().next());
        ProductAccomType productAccomType1 = new ProductAccomType();
        productAccomType1.setProduct(independentProduct);
        productAccomType1.setAccomType(accomClasses.get(1).getAccomTypes().iterator().next());
        ProductAccomType productAccomType2 = new ProductAccomType();
        productAccomType2.setProduct(independentProduct);
        productAccomType2.setAccomType(accomClasses.get(2).getAccomTypes().iterator().next());

        when(crudService.findAll(ProductAccomType.class)).thenReturn(Arrays.asList(productAccomType, productAccomType1, productAccomType2));

        createWebrateCompetitor();
        competitor.setStatusId(INACTIVE_STATUS_ID);

        WebrateAccomClassMapping webrateAccomClassMapping1 = new WebrateAccomClassMapping();
        webrateAccomClassMapping1.setAccomClass(accomClasses.get(0));
        WebrateAccomClassMapping webrateAccomClassMapping2 = new WebrateAccomClassMapping();
        webrateAccomClassMapping2.setAccomClass(accomClasses.get(1));
        WebrateAccomClassMapping webrateAccomClassMapping3 = new WebrateAccomClassMapping();
        webrateAccomClassMapping3.setAccomClass(accomClasses.get(2));

        when(crudService.findAll(WebrateAccomClassMapping.class)).thenReturn(Arrays.asList(webrateAccomClassMapping1, webrateAccomClassMapping2, webrateAccomClassMapping3));
        when(crudService.findAll(WebrateCompetitorsAccomClass.class)).thenReturn(List.of());
        when(crudService.findByNamedQuery(WebrateCompetitors.BY_PROPERTY_ID, with("propertyId", TEST_PROPERTY_ID).parameters())).thenReturn(List.of(competitor));
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.INDEPENDENT_PRODUCTS_ENABLED)).thenReturn(true);

        accomMappingService.updateCompetitorAccomClassReference(TEST_PROPERTY_ID, Arrays.asList(barProduct, independentProduct), new HashSet<>(List.of()));
        assertEquals(6, competitor.getWebrateCompetitorsAccomClasses().size());
        List<WebrateCompetitorsAccomClass> resultWebrateCompetitorsAccomClasses = new ArrayList<>(competitor.getWebrateCompetitorsAccomClasses());
        assertEquals(accomClasses.get(0), resultWebrateCompetitorsAccomClasses.get(0).getAccomClass());
        assertEquals(barProduct.getId(), resultWebrateCompetitorsAccomClasses.get(0).getProductID());
        assertEquals(competitor, resultWebrateCompetitorsAccomClasses.get(0).getWebrateCompetitor());
        assertEquals(accomClasses.get(1), resultWebrateCompetitorsAccomClasses.get(1).getAccomClass());
        assertEquals(competitor, resultWebrateCompetitorsAccomClasses.get(1).getWebrateCompetitor());
        assertEquals(barProduct.getId(), resultWebrateCompetitorsAccomClasses.get(1).getProductID());
        assertEquals(accomClasses.get(2), resultWebrateCompetitorsAccomClasses.get(2).getAccomClass());
        assertEquals(barProduct.getId(), resultWebrateCompetitorsAccomClasses.get(2).getProductID());
        assertEquals(competitor, resultWebrateCompetitorsAccomClasses.get(2).getWebrateCompetitor());
        assertEquals(accomClasses.get(0), resultWebrateCompetitorsAccomClasses.get(3).getAccomClass());
        assertEquals(independentProduct.getId(), resultWebrateCompetitorsAccomClasses.get(3).getProductID());
        assertEquals(competitor, resultWebrateCompetitorsAccomClasses.get(3).getWebrateCompetitor());
        assertEquals(accomClasses.get(1), resultWebrateCompetitorsAccomClasses.get(4).getAccomClass());
        assertEquals(competitor, resultWebrateCompetitorsAccomClasses.get(4).getWebrateCompetitor());
        assertEquals(independentProduct.getId(), resultWebrateCompetitorsAccomClasses.get(4).getProductID());
        assertEquals(accomClasses.get(2), resultWebrateCompetitorsAccomClasses.get(5).getAccomClass());
        assertEquals(independentProduct.getId(), resultWebrateCompetitorsAccomClasses.get(5).getProductID());
        assertEquals(competitor, resultWebrateCompetitorsAccomClasses.get(5).getWebrateCompetitor());

        verify(crudService, never()).executeUpdateByNamedQuery(any(String.class), anyMap());
        verify(crudService, never()).delete(any());
    }

    @Test
    @DisplayName("Update Competitor Accom Class Reference _ Independent Products Disable And RDL Enable _ No Existing Webrate Competitors Accom Class _ NoWebrateAccomClassMappingsToBeDeleted")
    void updateCompetitorAccomClassReference_IndependentProductsDisableAndRDLEnable_NoExistingWebrateCompetitorsAccomClass_NoWebrateAccomClassMappingsToBeDeleted() {
        CrudService crudService = mock(CrudService.class);
        inject(accomMappingService, "crudService", crudService);

        Product barProduct = createBarProduct();
        Product independentProduct = createIndependentProduct();

        doReturn(createWebrateAccomTypeAverageRatesDTOs()).when(accomMappingService).getAverageRatesForWebrateAccomTypes();
        doReturn(createAccomClassADRSingleton()).when(accomMappingService).getADRByAccomClasses();
        List<WebrateAccomType> webrateAccomTypes = createWebrateAccomTypes();
        List<AccomClass> accomClasses = createAccomClasses();
        accomMappingService.mapWebrateAccomTypesToAccomClasses(webrateAccomTypes, accomClasses);

        ProductAccomType productAccomType = new ProductAccomType();
        productAccomType.setProduct(independentProduct);
        productAccomType.setAccomType(accomClasses.get(0).getAccomTypes().iterator().next());
        ProductAccomType productAccomType1 = new ProductAccomType();
        productAccomType1.setProduct(independentProduct);
        productAccomType1.setAccomType(accomClasses.get(1).getAccomTypes().iterator().next());
        ProductAccomType productAccomType2 = new ProductAccomType();
        productAccomType2.setProduct(independentProduct);
        productAccomType2.setAccomType(accomClasses.get(2).getAccomTypes().iterator().next());

        when(crudService.findAll(ProductAccomType.class)).thenReturn(Arrays.asList(productAccomType, productAccomType1, productAccomType2));

        createWebrateCompetitor();
        competitor.setStatusId(INACTIVE_STATUS_ID);

        WebrateAccomClassMapping webrateAccomClassMapping1 = new WebrateAccomClassMapping();
        webrateAccomClassMapping1.setAccomClass(accomClasses.get(0));
        WebrateAccomClassMapping webrateAccomClassMapping2 = new WebrateAccomClassMapping();
        webrateAccomClassMapping2.setAccomClass(accomClasses.get(1));
        WebrateAccomClassMapping webrateAccomClassMapping3 = new WebrateAccomClassMapping();
        webrateAccomClassMapping3.setAccomClass(accomClasses.get(2));

        when(crudService.findAll(WebrateAccomClassMapping.class)).thenReturn(Arrays.asList(webrateAccomClassMapping1, webrateAccomClassMapping2, webrateAccomClassMapping3));
        when(crudService.findAll(WebrateCompetitorsAccomClass.class)).thenReturn(List.of());
        when(crudService.findByNamedQuery(WebrateCompetitors.BY_PROPERTY_ID, with("propertyId", TEST_PROPERTY_ID).parameters())).thenReturn(List.of(competitor));
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.INDEPENDENT_PRODUCTS_ENABLED)).thenReturn(false);
        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.IS_RDL_ENABLED)).thenReturn(true);
        when(propertyService.getPropertyById(TEST_PROPERTY_ID)).thenReturn(getProperty(TEST_UPS_ID));

        accomMappingService.updateCompetitorAccomClassReference(TEST_PROPERTY_ID, Arrays.asList(barProduct, independentProduct), new HashSet<>(List.of()));
        assertEquals(6, competitor.getWebrateCompetitorsAccomClasses().size());
        List<WebrateCompetitorsAccomClass> resultWebrateCompetitorsAccomClasses = new ArrayList<>(competitor.getWebrateCompetitorsAccomClasses());
        assertEquals(accomClasses.get(0), resultWebrateCompetitorsAccomClasses.get(0).getAccomClass());
        assertEquals(barProduct.getId(), resultWebrateCompetitorsAccomClasses.get(0).getProductID());
        assertEquals(competitor, resultWebrateCompetitorsAccomClasses.get(0).getWebrateCompetitor());
        assertEquals(accomClasses.get(1), resultWebrateCompetitorsAccomClasses.get(1).getAccomClass());
        assertEquals(competitor, resultWebrateCompetitorsAccomClasses.get(1).getWebrateCompetitor());
        assertEquals(barProduct.getId(), resultWebrateCompetitorsAccomClasses.get(1).getProductID());
        assertEquals(accomClasses.get(2), resultWebrateCompetitorsAccomClasses.get(2).getAccomClass());
        assertEquals(barProduct.getId(), resultWebrateCompetitorsAccomClasses.get(2).getProductID());
        assertEquals(competitor, resultWebrateCompetitorsAccomClasses.get(2).getWebrateCompetitor());
        assertEquals(accomClasses.get(0), resultWebrateCompetitorsAccomClasses.get(3).getAccomClass());
        assertEquals(independentProduct.getId(), resultWebrateCompetitorsAccomClasses.get(3).getProductID());
        assertEquals(competitor, resultWebrateCompetitorsAccomClasses.get(3).getWebrateCompetitor());
        assertEquals(accomClasses.get(1), resultWebrateCompetitorsAccomClasses.get(4).getAccomClass());
        assertEquals(competitor, resultWebrateCompetitorsAccomClasses.get(4).getWebrateCompetitor());
        assertEquals(independentProduct.getId(), resultWebrateCompetitorsAccomClasses.get(4).getProductID());
        assertEquals(accomClasses.get(2), resultWebrateCompetitorsAccomClasses.get(5).getAccomClass());
        assertEquals(independentProduct.getId(), resultWebrateCompetitorsAccomClasses.get(5).getProductID());
        assertEquals(competitor, resultWebrateCompetitorsAccomClasses.get(5).getWebrateCompetitor());

        verify(crudService, never()).executeUpdateByNamedQuery(any(String.class), anyMap());
        verify(crudService, never()).delete(any());
    }

    @Test
    @DisplayName("Update Competitor Accom Class Reference _ Independent Products Enabled _ Existing Webrate Competitors Accom Class _ NoWebrateAccomClassMappingsToBeDeleted")
    void updateCompetitorAccomClassReference_IndependentProductsEnabled_ExistingWebrateCompetitorsAccomClass_NoWebrateAccomClassMappingsToBeDeleted() {
        CrudService crudService = mock(CrudService.class);
        inject(accomMappingService, "crudService", crudService);

        Product barProduct = createBarProduct();
        Product independentProduct = createIndependentProduct();

        doReturn(createWebrateAccomTypeAverageRatesDTOs()).when(accomMappingService).getAverageRatesForWebrateAccomTypes();
        doReturn(createAccomClassADRSingleton()).when(accomMappingService).getADRByAccomClasses();
        List<WebrateAccomType> webrateAccomTypes = createWebrateAccomTypes();
        List<AccomClass> accomClasses = createAccomClasses();
        accomMappingService.mapWebrateAccomTypesToAccomClasses(webrateAccomTypes, accomClasses);

        ProductAccomType productAccomType = new ProductAccomType();
        productAccomType.setProduct(independentProduct);
        productAccomType.setAccomType(accomClasses.get(0).getAccomTypes().iterator().next());
        ProductAccomType productAccomType1 = new ProductAccomType();
        productAccomType1.setProduct(independentProduct);
        productAccomType1.setAccomType(accomClasses.get(1).getAccomTypes().iterator().next());
        ProductAccomType productAccomType2 = new ProductAccomType();
        productAccomType2.setProduct(independentProduct);
        productAccomType2.setAccomType(accomClasses.get(2).getAccomTypes().iterator().next());

        when(crudService.findAll(ProductAccomType.class)).thenReturn(Arrays.asList(productAccomType, productAccomType1, productAccomType2));

        createWebrateCompetitor();
        WebrateCompetitorsAccomClass webrateCompetitorsAccomClass = UniqueWebrateCompetitorsAccomClassCreator.createWebrateCompetitorsAccomClass(0, 0, competitor);
        webrateCompetitorsAccomClass.setAccomClass(accomClasses.get(0));
        webrateCompetitorsAccomClass.setId(100);
        webrateCompetitorsAccomClass.setProductID(barProduct.getId());
        WebrateCompetitorsAccomClass webrateCompetitorsAccomClass1 = UniqueWebrateCompetitorsAccomClassCreator.createWebrateCompetitorsAccomClass(0, 0, competitor);
        webrateCompetitorsAccomClass1.setAccomClass(accomClasses.get(0));
        webrateCompetitorsAccomClass1.setId(101);
        webrateCompetitorsAccomClass1.setProductID(independentProduct.getId());
        competitor.setStatusId(INACTIVE_STATUS_ID);
        competitor.setWebrateCompetitorsAccomClasses(new HashSet<>(Arrays.asList(webrateCompetitorsAccomClass, webrateCompetitorsAccomClass1)));

        WebrateAccomClassMapping webrateAccomClassMapping1 = new WebrateAccomClassMapping();
        webrateAccomClassMapping1.setAccomClass(accomClasses.get(0));
        WebrateAccomClassMapping webrateAccomClassMapping2 = new WebrateAccomClassMapping();
        webrateAccomClassMapping2.setAccomClass(accomClasses.get(1));
        WebrateAccomClassMapping webrateAccomClassMapping3 = new WebrateAccomClassMapping();
        webrateAccomClassMapping3.setAccomClass(accomClasses.get(2));

        when(crudService.findAll(WebrateAccomClassMapping.class)).thenReturn(Arrays.asList(webrateAccomClassMapping1, webrateAccomClassMapping2, webrateAccomClassMapping3));
        when(crudService.findAll(WebrateCompetitorsAccomClass.class)).thenReturn(Arrays.asList(webrateCompetitorsAccomClass, webrateCompetitorsAccomClass1));
        when(crudService.findByNamedQuery(WebrateCompetitors.BY_PROPERTY_ID, with("propertyId", TEST_PROPERTY_ID).parameters())).thenReturn(List.of(competitor));
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.INDEPENDENT_PRODUCTS_ENABLED)).thenReturn(true);

        accomMappingService.updateCompetitorAccomClassReference(TEST_PROPERTY_ID, Arrays.asList(barProduct, independentProduct), new HashSet<>(List.of()));
        assertEquals(6, competitor.getWebrateCompetitorsAccomClasses().size());
        List<WebrateCompetitorsAccomClass> resultWebrateCompetitorsAccomClasses = new ArrayList<>(competitor.getWebrateCompetitorsAccomClasses());
        assertEquals(accomClasses.get(1), resultWebrateCompetitorsAccomClasses.get(0).getAccomClass());
        assertEquals(competitor, resultWebrateCompetitorsAccomClasses.get(0).getWebrateCompetitor());
        assertEquals(barProduct.getId(), resultWebrateCompetitorsAccomClasses.get(0).getProductID());
        assertEquals(accomClasses.get(2), resultWebrateCompetitorsAccomClasses.get(1).getAccomClass());
        assertEquals(barProduct.getId(), resultWebrateCompetitorsAccomClasses.get(1).getProductID());
        assertEquals(competitor, resultWebrateCompetitorsAccomClasses.get(1).getWebrateCompetitor());
        assertEquals(accomClasses.get(1), resultWebrateCompetitorsAccomClasses.get(2).getAccomClass());
        assertEquals(competitor, resultWebrateCompetitorsAccomClasses.get(2).getWebrateCompetitor());
        assertEquals(independentProduct.getId(), resultWebrateCompetitorsAccomClasses.get(2).getProductID());
        assertEquals(accomClasses.get(2), resultWebrateCompetitorsAccomClasses.get(3).getAccomClass());
        assertEquals(independentProduct.getId(), resultWebrateCompetitorsAccomClasses.get(3).getProductID());
        assertEquals(competitor, resultWebrateCompetitorsAccomClasses.get(3).getWebrateCompetitor());
        assertEquals(accomClasses.get(0), resultWebrateCompetitorsAccomClasses.get(4).getAccomClass());
        assertEquals(barProduct.getId(), resultWebrateCompetitorsAccomClasses.get(4).getProductID());
        assertEquals(100, resultWebrateCompetitorsAccomClasses.get(4).getId());
        assertEquals(competitor, resultWebrateCompetitorsAccomClasses.get(4).getWebrateCompetitor());
        assertEquals(accomClasses.get(0), resultWebrateCompetitorsAccomClasses.get(5).getAccomClass());
        assertEquals(independentProduct.getId(), resultWebrateCompetitorsAccomClasses.get(5).getProductID());
        assertEquals(101, resultWebrateCompetitorsAccomClasses.get(5).getId());
        assertEquals(competitor, resultWebrateCompetitorsAccomClasses.get(5).getWebrateCompetitor());

        verify(crudService, never()).executeUpdateByNamedQuery(any(String.class), anyMap());
        verify(crudService, never()).delete(any());
    }

    @Test
    @DisplayName("Update Competitor Accom Class Reference _ Independent Products Enabled _ Existing Webrate Competitors Accom Class _ Delete _ NoWebrateAccomClassMappingsToBeDeleted")
    void updateCompetitorAccomClassReference_IndependentProductsEnabled_ExistingWebrateCompetitorsAccomClass_Delete_NoWebrateAccomClassMappingsToBeDeleted() {
        CrudService crudService = mock(CrudService.class);
        inject(accomMappingService, "crudService", crudService);

        Product barProduct = createBarProduct();
        Product independentProduct = createIndependentProduct();

        doReturn(createWebrateAccomTypeAverageRatesDTOs()).when(accomMappingService).getAverageRatesForWebrateAccomTypes();
        doReturn(createAccomClassADRSingleton()).when(accomMappingService).getADRByAccomClasses();
        List<WebrateAccomType> webrateAccomTypes = createWebrateAccomTypes();
        List<AccomClass> accomClasses = createAccomClasses();
        accomMappingService.mapWebrateAccomTypesToAccomClasses(webrateAccomTypes, accomClasses);

        ProductAccomType productAccomType = new ProductAccomType();
        productAccomType.setProduct(independentProduct);
        productAccomType.setAccomType(accomClasses.get(0).getAccomTypes().iterator().next());
        ProductAccomType productAccomType1 = new ProductAccomType();
        productAccomType1.setProduct(independentProduct);
        productAccomType1.setAccomType(accomClasses.get(1).getAccomTypes().iterator().next());
        when(crudService.findAll(ProductAccomType.class)).thenReturn(Arrays.asList(productAccomType, productAccomType1));

        createWebrateCompetitor();
        WebrateCompetitorsAccomClass webrateCompetitorsAccomClass = UniqueWebrateCompetitorsAccomClassCreator.createWebrateCompetitorsAccomClass(0, 0, competitor);
        webrateCompetitorsAccomClass.setAccomClass(accomClasses.get(0));
        webrateCompetitorsAccomClass.setId(100);
        webrateCompetitorsAccomClass.setProductID(barProduct.getId());
        WebrateCompetitorsAccomClass webrateCompetitorsAccomClass1 = UniqueWebrateCompetitorsAccomClassCreator.createWebrateCompetitorsAccomClass(0, 0, competitor);
        webrateCompetitorsAccomClass1.setAccomClass(accomClasses.get(0));
        webrateCompetitorsAccomClass1.setId(101);
        webrateCompetitorsAccomClass1.setProductID(independentProduct.getId());
        WebrateCompetitorsAccomClass webrateCompetitorsAccomClassToDelete = UniqueWebrateCompetitorsAccomClassCreator.createWebrateCompetitorsAccomClass(0, 0, competitor);
        webrateCompetitorsAccomClassToDelete.setAccomClass(accomClasses.get(2));
        webrateCompetitorsAccomClassToDelete.setId(102);
        webrateCompetitorsAccomClassToDelete.setProductID(independentProduct.getId());
        WebrateOverrideCompetitor webrateOverrideCompetitor = new WebrateOverrideCompetitor();
        webrateOverrideCompetitor.setId(222);
        WebrateOverrideCompetitorDetails webrateOverrideCompetitorDetails = new WebrateOverrideCompetitorDetails();
        webrateOverrideCompetitorDetails.setId(333);
        webrateOverrideCompetitorDetails.setWebrateCompetitorsAccomClass(webrateCompetitorsAccomClassToDelete);
        webrateOverrideCompetitorDetails.setWebrateOverrideCompetitor(webrateOverrideCompetitor);
        webrateOverrideCompetitor.setWebrateOverrideCompetitorDetails(new HashSet<>(List.of(webrateOverrideCompetitorDetails)));
        competitor.setStatusId(INACTIVE_STATUS_ID);
        competitor.setWebrateCompetitorsAccomClasses(new HashSet<>(Arrays.asList(webrateCompetitorsAccomClass, webrateCompetitorsAccomClass1, webrateCompetitorsAccomClassToDelete)));

        WebrateAccomClassMapping webrateAccomClassMapping1 = new WebrateAccomClassMapping();
        webrateAccomClassMapping1.setAccomClass(accomClasses.get(0));
        WebrateAccomClassMapping webrateAccomClassMapping2 = new WebrateAccomClassMapping();
        webrateAccomClassMapping2.setAccomClass(accomClasses.get(1));
        WebrateAccomClassMapping webrateAccomClassMapping3 = new WebrateAccomClassMapping();
        webrateAccomClassMapping3.setAccomClass(accomClasses.get(2));

        when(crudService.findAll(WebrateAccomClassMapping.class)).thenReturn(Arrays.asList(webrateAccomClassMapping1, webrateAccomClassMapping2, webrateAccomClassMapping3));
        when(crudService.findAll(WebrateCompetitorsAccomClass.class)).thenReturn(Arrays.asList(webrateCompetitorsAccomClass, webrateCompetitorsAccomClass1, webrateCompetitorsAccomClassToDelete));
        when(crudService.findByNamedQuery(WebrateCompetitors.BY_PROPERTY_ID, with("propertyId", TEST_PROPERTY_ID).parameters())).thenReturn(List.of(competitor));
        when(crudService.findByNamedQuery(WebrateOverrideCompetitorDetails.BY_ACCOMMAPPING_IDS, with("compAccomClassIds", List.of(102)).parameters())).thenReturn(List.of(webrateOverrideCompetitorDetails));
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.INDEPENDENT_PRODUCTS_ENABLED)).thenReturn(true);

        accomMappingService.updateCompetitorAccomClassReference(TEST_PROPERTY_ID, Arrays.asList(barProduct, independentProduct), new HashSet<>(List.of()));
        assertEquals(5, competitor.getWebrateCompetitorsAccomClasses().size());
        List<WebrateCompetitorsAccomClass> resultWebrateCompetitorsAccomClasses = new ArrayList<>(competitor.getWebrateCompetitorsAccomClasses());
        assertEquals(accomClasses.get(1), resultWebrateCompetitorsAccomClasses.get(0).getAccomClass());
        assertEquals(competitor, resultWebrateCompetitorsAccomClasses.get(0).getWebrateCompetitor());
        assertEquals(barProduct.getId(), resultWebrateCompetitorsAccomClasses.get(0).getProductID());
        assertEquals(accomClasses.get(2), resultWebrateCompetitorsAccomClasses.get(1).getAccomClass());
        assertEquals(barProduct.getId(), resultWebrateCompetitorsAccomClasses.get(1).getProductID());
        assertEquals(competitor, resultWebrateCompetitorsAccomClasses.get(1).getWebrateCompetitor());
        assertEquals(accomClasses.get(1), resultWebrateCompetitorsAccomClasses.get(2).getAccomClass());
        assertEquals(competitor, resultWebrateCompetitorsAccomClasses.get(2).getWebrateCompetitor());
        assertEquals(independentProduct.getId(), resultWebrateCompetitorsAccomClasses.get(2).getProductID());
        assertEquals(accomClasses.get(0), resultWebrateCompetitorsAccomClasses.get(3).getAccomClass());
        assertEquals(barProduct.getId(), resultWebrateCompetitorsAccomClasses.get(3).getProductID());
        assertEquals(100, resultWebrateCompetitorsAccomClasses.get(3).getId());
        assertEquals(competitor, resultWebrateCompetitorsAccomClasses.get(3).getWebrateCompetitor());
        assertEquals(accomClasses.get(0), resultWebrateCompetitorsAccomClasses.get(4).getAccomClass());
        assertEquals(independentProduct.getId(), resultWebrateCompetitorsAccomClasses.get(4).getProductID());
        assertEquals(101, resultWebrateCompetitorsAccomClasses.get(4).getId());
        assertEquals(competitor, resultWebrateCompetitorsAccomClasses.get(4).getWebrateCompetitor());

        verify(crudService).findByNamedQuery(WebrateOverrideCompetitorDetails.BY_ACCOMMAPPING_IDS,
                QueryParameter.with("compAccomClassIds", List.of(102)).parameters());
        verify(crudService).delete(List.of(webrateOverrideCompetitorDetails));
        verify(crudService).delete(Set.of(webrateOverrideCompetitor));
        verify(crudService).delete(List.of(webrateCompetitorsAccomClassToDelete));
    }

    @Test
    @DisplayName("Update Competitor Accom Class Reference _ Independent Products Enabled _ Existing Webrate Competitors Accom Class _ Delete _ WebrateAccomClassMappingsToBeDeleted")
    void updateCompetitorAccomClassReference_IndependentProductsEnabled_ExistingWebrateCompetitorsAccomClass_Delete_WebrateAccomClassMappingsToBeDeleted() {
        CrudService crudService = mock(CrudService.class);
        inject(accomMappingService, "crudService", crudService);

        Product barProduct = createBarProduct();
        Product independentProduct = createIndependentProduct();

        doReturn(createWebrateAccomTypeAverageRatesDTOs()).when(accomMappingService).getAverageRatesForWebrateAccomTypes();
        doReturn(createAccomClassADRSingleton()).when(accomMappingService).getADRByAccomClasses();
        List<WebrateAccomType> webrateAccomTypes = createWebrateAccomTypes();
        List<AccomClass> accomClasses = createAccomClasses();
        accomMappingService.mapWebrateAccomTypesToAccomClasses(webrateAccomTypes, accomClasses);

        ProductAccomType productAccomType = new ProductAccomType();
        productAccomType.setProduct(independentProduct);
        productAccomType.setAccomType(accomClasses.get(0).getAccomTypes().iterator().next());
        ProductAccomType productAccomType1 = new ProductAccomType();
        productAccomType1.setProduct(independentProduct);
        productAccomType1.setAccomType(accomClasses.get(1).getAccomTypes().iterator().next());
        when(crudService.findAll(ProductAccomType.class)).thenReturn(Arrays.asList(productAccomType, productAccomType1));

        createWebrateCompetitor();
        WebrateCompetitorsAccomClass webrateCompetitorsAccomClass = UniqueWebrateCompetitorsAccomClassCreator.createWebrateCompetitorsAccomClass(0, 0, competitor);
        webrateCompetitorsAccomClass.setAccomClass(accomClasses.get(0));
        webrateCompetitorsAccomClass.setId(100);
        webrateCompetitorsAccomClass.setProductID(barProduct.getId());
        WebrateCompetitorsAccomClass webrateCompetitorsAccomClass1 = UniqueWebrateCompetitorsAccomClassCreator.createWebrateCompetitorsAccomClass(0, 0, competitor);
        webrateCompetitorsAccomClass1.setAccomClass(accomClasses.get(0));
        webrateCompetitorsAccomClass1.setId(101);
        webrateCompetitorsAccomClass1.setProductID(independentProduct.getId());
        WebrateCompetitorsAccomClass webrateCompetitorsAccomClassToDelete1 = UniqueWebrateCompetitorsAccomClassCreator.createWebrateCompetitorsAccomClass(0, 0, competitor);
        webrateCompetitorsAccomClassToDelete1.setAccomClass(accomClasses.get(0));
        webrateCompetitorsAccomClassToDelete1.setId(99);
        webrateCompetitorsAccomClassToDelete1.setProductID(independentProduct.getId());
        WebrateCompetitorsAccomClass webrateCompetitorsAccomClass2 = UniqueWebrateCompetitorsAccomClassCreator.createWebrateCompetitorsAccomClass(0, 0, competitor);
        webrateCompetitorsAccomClass2.setAccomClass(accomClasses.get(1));
        webrateCompetitorsAccomClass2.setId(98);
        webrateCompetitorsAccomClass2.setProductID(independentProduct.getId());
        WebrateCompetitorsAccomClass webrateCompetitorsAccomClassToDelete3 = UniqueWebrateCompetitorsAccomClassCreator.createWebrateCompetitorsAccomClass(0, 0, competitor);
        webrateCompetitorsAccomClassToDelete3.setAccomClass(accomClasses.get(2));
        webrateCompetitorsAccomClassToDelete3.setId(102);
        webrateCompetitorsAccomClassToDelete3.setProductID(independentProduct.getId());

        WebrateOverrideCompetitor webrateOverrideCompetitor1 = new WebrateOverrideCompetitor();
        webrateOverrideCompetitor1.setId(102);
        WebrateOverrideCompetitorDetails webrateOverrideCompetitorDetails1 = new WebrateOverrideCompetitorDetails();
        webrateOverrideCompetitorDetails1.setId(152);
        webrateOverrideCompetitorDetails1.setWebrateCompetitorsAccomClass(webrateCompetitorsAccomClassToDelete1);
        webrateOverrideCompetitorDetails1.setWebrateOverrideCompetitor(webrateOverrideCompetitor1);
        webrateOverrideCompetitor1.setWebrateOverrideCompetitorDetails(new HashSet<>(List.of(webrateOverrideCompetitorDetails1)));

        WebrateOverrideCompetitor webrateOverrideCompetitor2 = new WebrateOverrideCompetitor();
        webrateOverrideCompetitor2.setId(202);
        WebrateOverrideCompetitorDetails webrateOverrideCompetitorDetails2 = new WebrateOverrideCompetitorDetails();
        webrateOverrideCompetitorDetails2.setId(302);
        webrateOverrideCompetitorDetails2.setWebrateCompetitorsAccomClass(webrateCompetitorsAccomClass2);
        webrateOverrideCompetitorDetails2.setWebrateOverrideCompetitor(webrateOverrideCompetitor2);
        webrateOverrideCompetitor2.setWebrateOverrideCompetitorDetails(new HashSet<>(List.of(webrateOverrideCompetitorDetails2)));

        WebrateOverrideCompetitor webrateOverrideCompetitor3 = new WebrateOverrideCompetitor();
        webrateOverrideCompetitor3.setId(222);
        WebrateOverrideCompetitorDetails webrateOverrideCompetitorDetails3 = new WebrateOverrideCompetitorDetails();
        webrateOverrideCompetitorDetails3.setId(333);
        webrateOverrideCompetitorDetails3.setWebrateCompetitorsAccomClass(webrateCompetitorsAccomClassToDelete3);
        webrateOverrideCompetitorDetails3.setWebrateOverrideCompetitor(webrateOverrideCompetitor3);
        webrateOverrideCompetitor3.setWebrateOverrideCompetitorDetails(new HashSet<>(List.of(webrateOverrideCompetitorDetails3)));
        competitor.setStatusId(INACTIVE_STATUS_ID);
        competitor.setWebrateCompetitorsAccomClasses(new HashSet<>(Arrays.asList(webrateCompetitorsAccomClass, webrateCompetitorsAccomClass1, webrateCompetitorsAccomClassToDelete1, webrateCompetitorsAccomClass2, webrateCompetitorsAccomClassToDelete3)));

        WebrateAccomClassMapping webrateAccomClassMapping1 = new WebrateAccomClassMapping();
        webrateAccomClassMapping1.setAccomClass(accomClasses.get(0));
        WebrateAccomClassMapping webrateAccomClassMapping2 = new WebrateAccomClassMapping();
        webrateAccomClassMapping2.setAccomClass(accomClasses.get(1));
        WebrateAccomClassMapping webrateAccomClassMapping3 = new WebrateAccomClassMapping();
        webrateAccomClassMapping3.setAccomClass(accomClasses.get(2));

        when(crudService.findAll(WebrateAccomClassMapping.class)).thenReturn(Arrays.asList(webrateAccomClassMapping1, webrateAccomClassMapping2, webrateAccomClassMapping3));
        when(crudService.findAll(WebrateCompetitorsAccomClass.class)).thenReturn(Arrays.asList(webrateCompetitorsAccomClass, webrateCompetitorsAccomClass1, webrateCompetitorsAccomClassToDelete1, webrateCompetitorsAccomClass2, webrateCompetitorsAccomClassToDelete3));
        when(crudService.findByNamedQuery(WebrateCompetitors.BY_PROPERTY_ID, with("propertyId", TEST_PROPERTY_ID).parameters())).thenReturn(List.of(competitor));
        when(crudService.findByNamedQuery(WebrateOverrideCompetitorDetails.BY_ACCOMMAPPING_IDS, with("compAccomClassIds", Arrays.asList(100, 101, 102)).parameters())).thenReturn(Arrays.asList(webrateOverrideCompetitorDetails1, webrateOverrideCompetitorDetails2, webrateOverrideCompetitorDetails3));
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.INDEPENDENT_PRODUCTS_ENABLED)).thenReturn(true);

        accomMappingService.updateCompetitorAccomClassReference(TEST_PROPERTY_ID, Arrays.asList(barProduct, independentProduct), new HashSet<>(List.of(webrateAccomClassMapping1)));
        assertEquals(3, competitor.getWebrateCompetitorsAccomClasses().size());
        List<WebrateCompetitorsAccomClass> resultWebrateCompetitorsAccomClasses = new ArrayList<>(competitor.getWebrateCompetitorsAccomClasses());
        assertEquals(accomClasses.get(1), resultWebrateCompetitorsAccomClasses.get(0).getAccomClass());
        assertEquals(competitor, resultWebrateCompetitorsAccomClasses.get(0).getWebrateCompetitor());
        assertEquals(barProduct.getId(), resultWebrateCompetitorsAccomClasses.get(0).getProductID());
        assertEquals(accomClasses.get(2), resultWebrateCompetitorsAccomClasses.get(1).getAccomClass());
        assertEquals(barProduct.getId(), resultWebrateCompetitorsAccomClasses.get(1).getProductID());
        assertEquals(competitor, resultWebrateCompetitorsAccomClasses.get(1).getWebrateCompetitor());
        assertEquals(accomClasses.get(1), resultWebrateCompetitorsAccomClasses.get(2).getAccomClass());
        assertEquals(competitor, resultWebrateCompetitorsAccomClasses.get(2).getWebrateCompetitor());
        assertEquals(independentProduct.getId(), resultWebrateCompetitorsAccomClasses.get(2).getProductID());

        verify(crudService).findByNamedQuery(WebrateOverrideCompetitorDetails.BY_ACCOMMAPPING_IDS,
                QueryParameter.with("compAccomClassIds", Arrays.asList(100, 101, 99, 102)).parameters());
        verify(crudService).delete(Arrays.asList(webrateCompetitorsAccomClass, webrateCompetitorsAccomClass1, webrateCompetitorsAccomClassToDelete1, webrateCompetitorsAccomClassToDelete3));
    }

    @Test
    void testResolveAlertWhenAllWebrateAccomTypeMapped() {
        WebrateAccomType webrateAccomType1 = getWebrateAccomType(100);
        setAccomClassMapping(webrateAccomType1);
        WebrateAccomType webrateAccomType2 = getWebrateAccomType(101);
        setAccomClassMapping(webrateAccomType2);
        List<WebrateAccomType> webrateAccomTypes = List.of(webrateAccomType1, webrateAccomType2);
        accomMappingService.checkAndResolveNewCompetitiveRoomTypeAlert(webrateAccomTypes, 5);
        verify(alertService).resolveAllAlerts(AlertType.NewCompetitiveRoomTypeFound, 5);
    }

    @Test
    void testNotResolveAlertWhenOneWebrateAccomTypeUnMapped() {
        WebrateAccomType webrateAccomType1 = getWebrateAccomType(100);
        setAccomClassMapping(webrateAccomType1);
        WebrateAccomType webrateAccomType2 = getWebrateAccomType(101);
        webrateAccomType2.setWebrateAccomClassMappings(Set.of());
        List<WebrateAccomType> webrateAccomTypes = List.of(webrateAccomType1, webrateAccomType2);
        accomMappingService.checkAndResolveNewCompetitiveRoomTypeAlert(webrateAccomTypes, 5);
        verify(alertService, never()).resolveAllAlerts(AlertType.NewCompetitiveRoomTypeFound, 5);
    }

    @Test
    void testResolveAlertWhenUnmappedWebrateAccomTypeDeleted() {
        WebrateAccomType webrateAccomType1 = getWebrateAccomType(100);
        setAccomClassMapping(webrateAccomType1);
        WebrateAccomType webrateAccomType2 = getWebrateAccomType(101);
        setAccomClassMapping(webrateAccomType2);
        webrateAccomType2.setShouldDelete(true);
        List<WebrateAccomType> webrateAccomTypes = List.of(webrateAccomType1, webrateAccomType2);
        accomMappingService.checkAndResolveNewCompetitiveRoomTypeAlert(webrateAccomTypes, 5);
        verify(alertService).resolveAllAlerts(AlertType.NewCompetitiveRoomTypeFound, 5);
    }

    @Test
    void testGetCountByAccomClassIdAndProductIdForActiveRanking() {
        setMockWebRateCompetitorsAccomClassData();
        assertEquals(5, accomMappingService.getCountByAccomClassIdAndProductIdForActiveRanking(1, 1));
    }

    @Test
    void testGetMaxWebrateRankingForDefaultConfigValidValues() {
        WebrateRankingAccomClass webrateRankingAccomClass = createWebrateRankingAccomClass(createAccomClass(1));
        int actualResult = accomMappingService.getMaxWebrateRankingForDefaultConfig(List.of(webrateRankingAccomClass));
        assertEquals(4, actualResult);
    }

    @Test
    void testGetMaxWebrateRankingForSeasonConfigValidValues() {
        WebrateRankingAccomClassOverride webrateRankingAccomClassOverride = createWebrateRankingAccomClassOverride(createAccomClass(2), new Date(), new Date(), 2);
        int actualResult = accomMappingService.getMaxWebrateRankingForSeasonConfig(List.of(webrateRankingAccomClassOverride));
        assertEquals(2, actualResult);
    }

    @Test
    void testGetMaxWebrateRankingForDefaultConfig() {
        List<WebrateRankingAccomClass> webrateRankingAccomClassList = new ArrayList<>();
        int actualResult = accomMappingService.getMaxWebrateRankingForDefaultConfig(webrateRankingAccomClassList);
        assertEquals(0, actualResult);
        actualResult = accomMappingService.getMaxWebrateRankingForDefaultConfig(null);
        assertEquals(0, actualResult);
    }

    @Test
    void testGetMaxWebrateRankingForSeasonConfig() {
        List<WebrateRankingAccomClassOverride> webrateRankingAccomClassOverrideList = new ArrayList<>();
        int actualResult = accomMappingService.getMaxWebrateRankingForSeasonConfig(webrateRankingAccomClassOverrideList);
        assertEquals(0, actualResult);
        actualResult = accomMappingService.getMaxWebrateRankingForSeasonConfig(null);
        assertEquals(0, actualResult);
    }

    @Test
    void testGetEffectiveCompetitorsCountForDefaultConfig() throws ParseException {
        setMockWebRateCompetitorsAccomClassData();
        List<WebrateOverrideCompetitorDetails> allWebrateOverrideCompetitorDetails = getWebrateOverrideCompetitorDetails();
        int count = accomMappingService.getEffectiveCompetitorsCountForDefaultConfig(1, 1, formatter.parse("2023-04-01"), allWebrateOverrideCompetitorDetails);
        assertEquals(4, count);
    }

    @Test
    void testGetEffectiveCompetitorsCountForSeasonConfigSeasonDatesWithinIgnoreCompetitorConfiguredDateRange() throws ParseException {
        setMockWebRateCompetitorsAccomClassData();
        List<WebrateOverrideCompetitorDetails> allWebrateOverrideCompetitorDetails = getWebrateOverrideCompetitorDetails();
        WebrateRankingAccomClassOverride webrateRankingAccomClassOverride = createWebrateRankingAccomClassOverride(createAccomClass(1), formatter.parse("2023-04-01"), formatter.parse("2023-05-01"), 1);
        int count = accomMappingService.getEffectiveCompetitorsCountForSeasonConfig(webrateRankingAccomClassOverride, allWebrateOverrideCompetitorDetails);
        assertEquals(4, count);
    }

    @Test
    void testGetEffectiveCompetitorsCountForSeasonConfigIgnoreCompetitorConfiguredWithinSeasonDateRange() throws ParseException {
        setMockWebRateCompetitorsAccomClassData();
        List<WebrateOverrideCompetitorDetails> allWebrateOverrideCompetitorDetails = getWebrateOverrideCompetitorDetails();
        WebrateRankingAccomClassOverride webrateRankingAccomClassOverride = createWebrateRankingAccomClassOverride(createAccomClass(1), formatter.parse("2021-04-01"), formatter.parse("2051-05-01"), 1);
        int count = accomMappingService.getEffectiveCompetitorsCountForSeasonConfig(webrateRankingAccomClassOverride, allWebrateOverrideCompetitorDetails);
        assertEquals(4, count);
    }

    @Test
    void testGetEffectiveCompetitorsCountForSeasonConfigNewSeasonIgnoreCompetitorConfiguredOutSideDateRange() throws ParseException {
        setMockWebRateCompetitorsAccomClassData();
        final List<WebrateOverrideCompetitorDetails> allWebrateOverrideCompetitorDetails = getWebrateOverrideCompetitorDetails();
        WebrateRankingAccomClassOverride webrateRankingAccomClassOverride = createWebrateRankingAccomClassOverride(createAccomClass(1), formatter.parse("2022-03-01"), formatter.parse("2022-03-31"), 1);
        int count = accomMappingService.getEffectiveCompetitorsCountForSeasonConfig(webrateRankingAccomClassOverride, allWebrateOverrideCompetitorDetails);
        assertEquals(5, count);
    }

    @Test
    void testGetEffectiveCompetitorsCountWhileAddingIgnoreCompetitor() throws ParseException {
        setMockWebRateCompetitorsAccomClassData();
        Date systemDate = LocalDateUtils.toDate(LocalDate.of(2022, 4, 20));
        Date ignoreCompStartDT = LocalDateUtils.toDate(LocalDate.of(2022, 4, 1));
        Date ignoreCompEndDT = LocalDateUtils.toDate(LocalDate.of(2050, 5, 1));
        final List<WebrateOverrideCompetitorDetails> allWebrateOverrideCompetitorDetails = getWebrateOverrideCompetitorDetails();
        int count = accomMappingService.getEffectiveCompetitorsCount(1, 1, systemDate, ignoreCompStartDT, ignoreCompEndDT, allWebrateOverrideCompetitorDetails);
        assertEquals(4, count);
    }

    @Test
    void testGetPresentAndFutureEffectiveCompetitorsCountWhileAddingIgnoreCompetitor() throws ParseException {
        setMockWebRateCompetitorsAccomClassData();
        Date systemDate = LocalDateUtils.toDate(LocalDate.of(2022, 4, 20));
        final List<WebrateOverrideCompetitorDetails> allWebrateOverrideCompetitorDetails = getWebrateOverrideCompetitorDetails();
        int count = accomMappingService.getPresentAndFutureEffectiveCompetitorsCount(1, 1, systemDate, allWebrateOverrideCompetitorDetails);
        assertEquals(3, count);
    }

    @Test
    void testGetWebrateOverrideCompetitorMapByProduct() throws ParseException {
        WebrateOverrideCompetitorDetails webrateOverrideCompetitorDetailsPast = getWebrateOverrideCompetitorDetails(createAccomClass(1), 1, 2);
        WebrateOverrideCompetitorDetails webrateOverrideCompetitorDetailsPresentRankingEnabled = getWebrateOverrideCompetitorDetails(createAccomClass(1), 1, 2);
        WebrateOverrideCompetitorDetails webrateOverrideCompetitorDetailsPresentDifferentAccomClass = getWebrateOverrideCompetitorDetails(createAccomClass(2), 1, 2);
        WebrateOverrideCompetitorDetails webrateOverrideCompetitorDetailsPresentRankingDisabled = getWebrateOverrideCompetitorDetails(createAccomClass(1), 0, 2);
        WebrateOverrideCompetitorDetails webrateOverrideCompetitorDetailsFuture = getWebrateOverrideCompetitorDetails(createAccomClass(1), 1, 2);

        webrateOverrideCompetitorDetailsPast.setWebrateOverrideCompetitor(createWebrateOverrideCompetitor(formatter.parse("2020-04-01"), formatter.parse("2020-05-01"), webrateOverrideCompetitorDetailsPast, 2));
        webrateOverrideCompetitorDetailsPresentRankingEnabled.setWebrateOverrideCompetitor(createWebrateOverrideCompetitor(formatter.parse("2022-04-01"), formatter.parse("2050-05-01"), webrateOverrideCompetitorDetailsPresentRankingEnabled, 2));
        webrateOverrideCompetitorDetailsPresentDifferentAccomClass.setWebrateOverrideCompetitor(createWebrateOverrideCompetitor(formatter.parse("2022-04-01"), formatter.parse("2050-05-01"), webrateOverrideCompetitorDetailsPresentDifferentAccomClass, 2));
        webrateOverrideCompetitorDetailsPresentRankingDisabled.setWebrateOverrideCompetitor(createWebrateOverrideCompetitor(formatter.parse("2022-04-01"), formatter.parse("2050-05-01"), webrateOverrideCompetitorDetailsPresentRankingDisabled, 2));
        webrateOverrideCompetitorDetailsFuture.setWebrateOverrideCompetitor(createWebrateOverrideCompetitor(formatter.parse("2120-04-01"), formatter.parse("2120-05-01"), webrateOverrideCompetitorDetailsFuture, 2));

        List<WebrateOverrideCompetitorDetails> allWebrateOverrideCompetitorDetails = List.of(webrateOverrideCompetitorDetailsPast, webrateOverrideCompetitorDetailsPresentRankingEnabled, webrateOverrideCompetitorDetailsPresentDifferentAccomClass, webrateOverrideCompetitorDetailsPresentRankingDisabled, webrateOverrideCompetitorDetailsFuture);

        List<WebrateOverrideCompetitor> actualWebrateOverrideCompetitorList = accomMappingService.getWebrateOverrideCompetitor(allWebrateOverrideCompetitorDetails, 1);
        assertEquals(3, actualWebrateOverrideCompetitorList.size());
    }

    @Test
    void testGetWebrateOverrideCompetitorMapByProductCompetitorDataIgnoredNotConfigured() {
        List<WebrateOverrideCompetitorDetails> allWebrateOverrideCompetitorDetails = new ArrayList<>();
        List<WebrateOverrideCompetitor> actualWebrateOverrideCompetitorList = accomMappingService.getWebrateOverrideCompetitor(allWebrateOverrideCompetitorDetails, 1);
        assertEquals(0, actualWebrateOverrideCompetitorList.size());
    }

    @Test
    void testAutoConfigureRoomClassMapping() {
        CrudService crudService = mock(CrudService.class);
        inject(accomMappingService, "crudService", crudService);
        AccomClass unassigned = new AccomClass();
        unassigned.setName("unassigned");
        when(accommodation2Service.findUnassignedAccomClass(5)).thenReturn(unassigned);
        when(alertService.resolveAllAlerts(AlertType.NewCompetitiveRoomTypeFound,5)).thenReturn(new ArrayList<Alert>());
        accomMappingService.autoConfigureRoomClassMapping();
        verify(crudService, times(1)).deleteAll(WebrateAccomClassMapping.class);
        verify(crudService, times(1)).saveWithFlushAndClear(anyList(), anyInt());
        verify(alertService,times(1)).resolveAllAlerts(AlertType.NewCompetitiveRoomTypeFound,5);
    }

    @Test
    void testCreateCompetitorAccomClassMapping() {
        CrudService crudService = mock(CrudService.class);
        inject(accomMappingService, "crudService", crudService);
        AccomClass standard = new AccomClass();
        standard.setName("STANDARD");
        when(accommodation2Service.findUnassignedAccomClass(5)).thenReturn(standard);
        when(alertService.resolveAllAlerts(AlertType.NewCompetitiveRoomTypeFound,5)).thenReturn(new ArrayList<Alert>());
        accomMappingService.autoConfigureRoomClassMapping();
        verify(crudService, times(1)).findAll(WebrateCompetitors.class);
        verify(alertService,times(1)).resolveAllAlerts(AlertType.NewCompetitiveRoomTypeFound,5);
    }

    @Test
    void testSaveCompetitorAccomClassMapping() {
        CrudService crudService = mock(CrudService.class);
        inject(accomMappingService, "crudService", crudService);
        AccomClass standard = new AccomClass();
        standard.setName("STANDARD");
        WebrateCompetitors webrateCompetitors = new WebrateCompetitors();
        webrateCompetitors.setWebrateCompetitorsName("test");

        when(accommodation2Service.findUnassignedAccomClass(5)).thenReturn(standard);
        when(crudService.findAll(WebrateCompetitors.class)).thenReturn(List.of(webrateCompetitors));
        when(alertService.resolveAllAlerts(AlertType.NewCompetitiveRoomTypeFound,5)).thenReturn(new ArrayList<Alert>());

        accomMappingService.autoConfigureRoomClassMapping();
        verify(crudService, times(1)).save(webrateCompetitors);
        verify(alertService,times(1)).resolveAllAlerts(AlertType.NewCompetitiveRoomTypeFound,5);
    }

    @Test
    void testRDLCRTMappingRebuild() throws JsonProcessingException {
        Client client = new Client();
        client.setId(1);
        client.setCode("Test-Client");

        Property property = new Property();
        property.setClient(client);
        property.setUpsId("test-123");

        PacmanWorkContextHelper.setPropertyId(1);
        when(propertyService.getPropertyById(1)).thenReturn(property);
        when(dateService.getCaughtUpDate()).thenReturn(new Date());

        ArgumentCaptor<EventType> eventTypeArgumentCaptor = ArgumentCaptor.forClass(EventType.class);
        accomMappingService.rdlCRTMappingRebuild();

        verify(g3SNSService).publishToSNS(eventTypeArgumentCaptor.capture(), anyMap());
        assertEquals(EventType.CUSTOM_ROOM_TYPE_MAPPING_REBUILD, eventTypeArgumentCaptor.getValue());
    }

    @Test
    void testShouldNotPublishRDLCRTMappingRebuildEventForTestProperty() throws JsonProcessingException {
        Client client = new Client();
        client.setId(1);
        client.setCode("Test-Client");

        Property property = new Property();
        property.setClient(client);
        property.setUpsId("test-123");

        PacmanWorkContextHelper.setPropertyId(1);
        when(propertyService.getPropertyById(1)).thenReturn(property);
        when(dateService.getCaughtUpDate()).thenReturn(new Date());

        ArgumentCaptor<EventType> eventTypeArgumentCaptor = ArgumentCaptor.forClass(EventType.class);
        when(configParamsService.getConfigParameterValueAtContext(
                anyString(), eq(FeatureTogglesConfigParamName.RDL_CLONED_FROM_PROPERTY_UPS_ID.value()))).thenReturn(TEST_UPS_ID);
        accomMappingService.rdlCRTMappingRebuild();
        verify(g3SNSService, never()).publishToSNS(eventTypeArgumentCaptor.capture(), anyMap());
    }

    @Test
    void shouldCopyWebrateOverrideChannelToIndependentProducts() {
        CrudService crudService = mock(CrudService.class);
        inject(accomMappingService, "crudService", crudService);
        when(crudService.findByNamedQuery(WebrateOverrideChannel.BY_PROPERTY_ID_FOR_BAR_ONLY,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters())).thenReturn(
                List.of(getMockWebrateOverrideChannels(), getMockWebrateOverrideChannels())
        );
        accomMappingService.copyWebrateOverrideChannelToIndependentProducts(List.of(2, 3));
        ArgumentCaptor<List> LIST_ARGUMENT_CAPTOR = ArgumentCaptor.forClass(List.class);
        verify(crudService).save(LIST_ARGUMENT_CAPTOR.capture());
        List<WebrateOverrideChannel> resultSet = LIST_ARGUMENT_CAPTOR.getValue();
        assertEquals(4, resultSet.size());
        assertEquals(2, resultSet.get(0).getProductID());
        assertEquals(3, resultSet.get(1).getProductID());
    }

    private WebrateOverrideChannel getMockWebrateOverrideChannels() {
        WebrateOverrideChannel webrateChannel = new WebrateOverrideChannel();
        webrateChannel.setProductID(1);
        webrateChannel.setPropertyId(1);
        webrateChannel.setWebrateOverrideName("s1");
        return webrateChannel;
    }

    @Test
    void shouldEnableDemandForIPOnlyIfDemandIsEnabledForBAR() {
        CrudService crudService = mock(CrudService.class);
        inject(accomMappingService, "crudService", crudService);
        List<WebrateCompetitorsAccomClass> wcac = getMockWebrateCompetitorAccomClasses();
        when(crudService.findAll(WebrateCompetitorsAccomClass.class)).thenReturn(wcac);

        accomMappingService.enableDemandBasedOnBARForIndependentProducts(List.of(2, 3));

        assertEquals(6, wcac.stream().filter(wc -> wc.getDemandEnabled() == 1).count());
        assertEquals(3, wcac.stream().filter(wc -> wc.getDemandEnabled() == 0).count());
    }

    @Test
    void getDemandEnabledForBAR() {
        List<WebrateCompetitorsAccomClass> mockClasses = getMockWebrateCompetitorAccomClasses().stream()
                .filter(wce -> wce.getProductID() == 1).collect(Collectors.toList());

        int result = accomMappingService.getDemandEnabledForBAR(1, 1, mockClasses);

        assertEquals(1, result);
    }

    @Test
    void test_getAllWebRateAccomTypeAccomClassMapping() {
        CrudService crudService = mock(CrudService.class);
        inject(accomMappingService, "crudService", crudService);
        when(crudService.findAll(WebrateAccomType.class)).thenReturn(List.of(
                mockWebRateAccomType(1, "Premium"),
                mockWebRateAccomType(2, "SUITE")));
        List<RoomClassMappingDTO> response = accomMappingService.getAllWebRateAccomTypeAccomClassMapping();
        assertEquals(2, response.size());
        verify(crudService).findAll(WebrateAccomType.class);
    }

    @Test
    void test_saveCompetitiveRoomTypeToAccomClass_MappingInserted() {
        CrudService crudService = mock(CrudService.class);
        inject(accomMappingService, "crudService", crudService);
        when(crudService.findAll(WebrateAccomClassMapping.class)).thenReturn(new ArrayList<>());
        when(crudService.saveWithFlushAndClear(Mockito.anyList(), Mockito.anyInt())).thenReturn(new ArrayList<>());
        List<RoomClassMappingDTO> roomClassMappingRequests = List.of(mockRoomClassMappingRequest("Premium", "DLX"));
        List<AccomClass> accomClasses = List.of(mockAccomClass(1, "STD"), mockAccomClass(2, "DLX"), mockAccomClass(3, "SUITE"));
        List<WebrateAccomType> existingWebRateAccomTypes = List.of(mockWebRateAccomType(1, "Premium"));
        accomMappingService.saveCompetitiveRoomTypeToAccomClass(roomClassMappingRequests, accomClasses, existingWebRateAccomTypes);
        verify(crudService).findAll(WebrateAccomClassMapping.class);
        verify(crudService).saveWithFlushAndClear(Mockito.anyList(), Mockito.anyInt());
    }

    @Test
    void test_saveCompetitiveRoomTypeToAccomClass_MappingUpdated() {
        CrudService crudService = mock(CrudService.class);
        inject(accomMappingService, "crudService", crudService);
        when(crudService.findAll(WebrateAccomClassMapping.class)).thenReturn(List.of(
                mockWebRateAccomClassMapping(1, 1, "Premium", 1, "STD")));
        when(crudService.saveWithFlushAndClear(Mockito.anyList(), Mockito.anyInt())).thenReturn(new ArrayList<>());
        List<RoomClassMappingDTO> roomClassMappingRequests = List.of(mockRoomClassMappingRequest("Premium", "DLX"));
        List<AccomClass> accomClasses = List.of(mockAccomClass(1, "STD"), mockAccomClass(2, "DLX"), mockAccomClass(3, "SUITE"));
        List<WebrateAccomType> existingWebRateAccomTypes = List.of(mockWebRateAccomType(1, "Premium"));
        accomMappingService.saveCompetitiveRoomTypeToAccomClass(roomClassMappingRequests, accomClasses, existingWebRateAccomTypes);
        verify(crudService).findAll(WebrateAccomClassMapping.class);
        verify(crudService).saveWithFlushAndClear(Mockito.anyList(), Mockito.anyInt());
    }

    private RoomClassMappingDTO mockRoomClassMappingRequest(String rtName, String rcCode) {
        RoomClassMappingDTO request = new RoomClassMappingDTO();
        request.setCompetitiveRT(rtName);
        request.setAccomClassCode(List.of(rcCode));
        return request;
    }

    private WebrateAccomClassMapping mockWebRateAccomClassMapping(int mapId, int webRateId, String rtName, int rcId, String rcCode) {
        WebrateAccomClassMapping webRateAccomClassMapping = new WebrateAccomClassMapping();
        webRateAccomClassMapping.setWebrateAccomType(mockWebRateAccomType(webRateId, rtName));
        webRateAccomClassMapping.setAccomClass(mockAccomClass(rcId, rcCode));
        webRateAccomClassMapping.setId(mapId);
        return webRateAccomClassMapping;
    }

    private AccomClass mockAccomClass(int id, String name) {
        AccomClass accomClass = new AccomClass();
        accomClass.setName(name);
        accomClass.setCode(name);
        accomClass.setId(id);
        return accomClass;
    }

    private WebrateAccomType mockWebRateAccomType(int id, String rtName) {
        WebrateAccomType webRateAccomType = new WebrateAccomType();
        webRateAccomType.setId(id);
        webRateAccomType.setWebrateAccomAlias(rtName);
        webRateAccomType.setWebrateAccomName(rtName);
        webRateAccomType.setWebrateAccomClassMappings(new HashSet<>());
        return webRateAccomType;
    }

    private List<WebrateCompetitorsAccomClass> getMockWebrateCompetitorAccomClasses() {
        WebrateCompetitors competitor1 = new WebrateCompetitors();
        competitor1.setId(1);
        WebrateCompetitors competitor2 = new WebrateCompetitors();
        competitor2.setId(2);
        AccomClass ac1 = new AccomClass(1, 1, BigDecimal.ZERO);
        AccomClass ac2 = new AccomClass(2, 1, BigDecimal.ZERO);
        List<WebrateCompetitorsAccomClass> mockClasses = new ArrayList<>();
        mockClasses.add(getWcac(competitor1, ac1, 1, 1));
        mockClasses.add(getWcac(competitor1, ac2, 1, 1));
        mockClasses.add(getWcac(competitor2, ac1, 0, 1));
        mockClasses.add(getWcac(competitor1, ac1, 0, 2));
        mockClasses.add(getWcac(competitor1, ac2, 0, 2));
        mockClasses.add(getWcac(competitor2, ac1, 0, 2));
        mockClasses.add(getWcac(competitor1, ac1, 0, 3));
        mockClasses.add(getWcac(competitor1, ac2, 0, 3));
        mockClasses.add(getWcac(competitor2, ac1, 1, 3));

        return mockClasses;
    }

    private static WebrateCompetitorsAccomClass getWcac(WebrateCompetitors competitor, AccomClass ac, Integer isDemandEnabled, Integer productId) {
        WebrateCompetitorsAccomClass wcac = new WebrateCompetitorsAccomClass();
        wcac.setDemandEnabled(isDemandEnabled);
        wcac.setProductID(productId);
        wcac.setWebrateCompetitor(competitor);
        wcac.setAccomClass(ac);
        return wcac;
    }


    private static void setMockWebRateCompetitorsAccomClassData() {
        CrudService crudService = mock(CrudService.class);
        inject(accomMappingService, "crudService", crudService);
        when(crudService.findByNamedQuerySingleResult(WebrateCompetitorsAccomClass.COUNT_ACCOM_CLASS_ID_AND_PRODUCT_ACTIVE_RANKING,
                QueryParameter.with("accomClassId", 1)
                        .and("productId", 1).parameters())).thenReturn(5L);
    }

    private List<WebrateOverrideCompetitorDetails> getWebrateOverrideCompetitorDetails() throws ParseException {
        WebrateOverrideCompetitorDetails webrateOverrideCompetitorDetailsPast = getWebrateOverrideCompetitorDetails(createAccomClass(1), 1, 1);
        WebrateOverrideCompetitorDetails webrateOverrideCompetitorDetailsPresent = getWebrateOverrideCompetitorDetails(createAccomClass(1), 1, 1);
        WebrateOverrideCompetitorDetails webrateOverrideCompetitorDetailsFuture = getWebrateOverrideCompetitorDetails(createAccomClass(1), 1, 1);
        webrateOverrideCompetitorDetailsPast.setWebrateOverrideCompetitor(createWebrateOverrideCompetitor(formatter.parse("2020-04-01"), formatter.parse("2020-05-01"), webrateOverrideCompetitorDetailsPast, 1));
        webrateOverrideCompetitorDetailsPresent.setWebrateOverrideCompetitor(createWebrateOverrideCompetitor(formatter.parse("2022-04-01"), formatter.parse("2050-05-01"), webrateOverrideCompetitorDetailsPresent, 1));
        webrateOverrideCompetitorDetailsFuture.setWebrateOverrideCompetitor(createWebrateOverrideCompetitor(formatter.parse("2120-04-01"), formatter.parse("2120-05-01"), webrateOverrideCompetitorDetailsFuture, 1));
        return List.of(webrateOverrideCompetitorDetailsPast, webrateOverrideCompetitorDetailsPresent, webrateOverrideCompetitorDetailsFuture);
    }

    private WebrateRankingAccomClass createWebrateRankingAccomClass(AccomClass accomClass) {
        WebrateRankingAccomClass webrateRankingAccomClass = new WebrateRankingAccomClass();
        webrateRankingAccomClass.setWebrateRanking(UniqueWebrateRankingCreator.createWebrateRanking());
        webrateRankingAccomClass.setAccomClass(accomClass);
        webrateRankingAccomClass.setProductID(1);
        webrateRankingAccomClass.setWebrateRankingMonday(createWebrateRanking(CompetitiveMarketPositionConstraintEnum.HIGH_RANGE.getWebrateRanking()));
        webrateRankingAccomClass.setWebrateRankingTuesday(createWebrateRanking(CompetitiveMarketPositionConstraintEnum.ABOVE_ALL_COMPETITORS.getWebrateRanking()));
        webrateRankingAccomClass.setWebrateRankingWednesday(createWebrateRanking(CompetitiveMarketPositionConstraintEnum.MID_RANGE.getWebrateRanking()));
        webrateRankingAccomClass.setWebrateRankingThursday(createWebrateRanking(CompetitiveMarketPositionConstraintEnum.NONE.getWebrateRanking()));
        webrateRankingAccomClass.setWebrateRankingFriday(createWebrateRanking(CompetitiveMarketPositionConstraintEnum.LOW_RANGE.getWebrateRanking()));
        webrateRankingAccomClass.setWebrateRankingSaturday(createWebrateRanking(CompetitiveMarketPositionConstraintEnum.HIGH_RANGE.getWebrateRanking()));
        webrateRankingAccomClass.setWebrateRankingSunday(createWebrateRanking(CompetitiveMarketPositionConstraintEnum.NOT_LOW.getWebrateRanking()));
        return webrateRankingAccomClass;
    }

    private AccomClass createAccomClass(int id) {
        AccomClass accomClass = new AccomClass();
        accomClass.setCode("Accom Code");
        accomClass.setName("Accom Name");
        accomClass.setId(id);
        return accomClass;
    }

    private WebrateRanking createWebrateRanking(String name) {
        WebrateRanking webrateRanking = new WebrateRanking();
        webrateRanking.setWebrateRankingName(name);
        return webrateRanking;
    }

    private WebrateRankingAccomClassOverride createWebrateRankingAccomClassOverride(AccomClass accomClass, Date startDate, Date endDate, int productId) {
        WebrateRankingAccomClassOverride webrateRankingAccomClassOverride = new WebrateRankingAccomClassOverride();
        webrateRankingAccomClassOverride.setProductID(productId);
        webrateRankingAccomClassOverride.setAccomClass(accomClass);
        webrateRankingAccomClassOverride.setWebrateRankingStartDT(startDate);
        webrateRankingAccomClassOverride.setWebrateRankingEndDT(endDate);
        webrateRankingAccomClassOverride.setWebrateRankingOvrMonday(createWebrateRanking(CompetitiveMarketPositionConstraintEnum.HIGH_RANGE.getWebrateRanking()));
        webrateRankingAccomClassOverride.setWebrateRankingOvrTuesday(createWebrateRanking(CompetitiveMarketPositionConstraintEnum.NONE.getWebrateRanking()));
        webrateRankingAccomClassOverride.setWebrateRankingOvrWednesday(createWebrateRanking(CompetitiveMarketPositionConstraintEnum.HIGH_RANGE.getWebrateRanking()));
        webrateRankingAccomClassOverride.setWebrateRankingOvrThursday(createWebrateRanking(CompetitiveMarketPositionConstraintEnum.ABOVE_ALL_COMPETITORS.getWebrateRanking()));
        webrateRankingAccomClassOverride.setWebrateRankingOvrFriday(createWebrateRanking(CompetitiveMarketPositionConstraintEnum.HIGH_RANGE.getWebrateRanking()));
        webrateRankingAccomClassOverride.setWebrateRankingOvrSaturday(createWebrateRanking(CompetitiveMarketPositionConstraintEnum.HIGH_RANGE.getWebrateRanking()));
        webrateRankingAccomClassOverride.setWebrateRankingOvrSunday(createWebrateRanking(CompetitiveMarketPositionConstraintEnum.HIGH_RANGE.getWebrateRanking()));
        return webrateRankingAccomClassOverride;
    }

    private WebrateCompetitorsAccomClass createWebrateCompetitorsAccomClass(AccomClass accomClass, int rankingEnabled) {
        WebrateCompetitorsAccomClass competitorsAccomClass = new WebrateCompetitorsAccomClass();
        competitorsAccomClass.setAccomClass(accomClass);
        competitorsAccomClass.setRankingEnabled(rankingEnabled);
        return competitorsAccomClass;
    }

    private WebrateOverrideCompetitor createWebrateOverrideCompetitor(Date startDate, Date endDate, WebrateOverrideCompetitorDetails webrateOverrideCompetitorDetails, int productId) {
        WebrateOverrideCompetitor overrideCompetitor = new WebrateOverrideCompetitor();
        overrideCompetitor.setProductID(productId);
        overrideCompetitor.setCompetitorOverrideStartDT(startDate);
        overrideCompetitor.setCompetitorOverrideEndDT(endDate);
        overrideCompetitor.setWebrateOverrideCompetitorDetails(Set.of(webrateOverrideCompetitorDetails));
        return overrideCompetitor;
    }

    private WebrateOverrideCompetitorDetails getWebrateOverrideCompetitorDetails(AccomClass accomClass, int rankingEnabled, int productId) {
        WebrateCompetitors webrateCompetitors = new WebrateCompetitors();
        webrateCompetitors.setId(1);
        webrateCompetitors.setStatusId(Constants.ACTIVE_STATUS_ID);
        WebrateCompetitorsAccomClass competitorsAccomClass = createWebrateCompetitorsAccomClass(accomClass, rankingEnabled);
        competitorsAccomClass.setProductID(productId);
        competitorsAccomClass.setWebrateCompetitor(webrateCompetitors);
        WebrateOverrideCompetitorDetails overrideCompetitorDetails = new WebrateOverrideCompetitorDetails();
        overrideCompetitorDetails.setProductID(productId);
        overrideCompetitorDetails.setWebrateCompetitorsAccomClass(competitorsAccomClass);
        return overrideCompetitorDetails;
    }

    private List<WebrateAccomType> getWebrateAccomTypes() {
        List<WebrateAccomType> webrateAccomTypes = new ArrayList<>();
        WebrateAccomType webrateAccomType = getWebrateAccomType(100);
        setAccomClassMapping(webrateAccomType);
        webrateAccomTypes.add(webrateAccomType);
        return webrateAccomTypes;
    }

    private static void setAccomClassMapping(WebrateAccomType webrateAccomType) {
        Set<WebrateAccomClassMapping> webrateAccomClassMappingSet = new HashSet<>();
        WebrateAccomClassMapping webrateAccomClassMapping = new WebrateAccomClassMapping();
        webrateAccomClassMapping.setAccomClass(new AccomClass() {

            {
                setCode(ACCOM_CODE);
                setId(100);
            }
        });
        webrateAccomClassMappingSet.add(webrateAccomClassMapping);
        webrateAccomType.setWebrateAccomClassMappings(webrateAccomClassMappingSet);
    }

    private static WebrateAccomType getWebrateAccomType(Integer id) {
        WebrateAccomType webrateAccomType = new WebrateAccomType();
        webrateAccomType.setId(id);
        webrateAccomType.setWebrateAccomName(NAME);
        webrateAccomType.setWebrateAccomAlias(ALIAS);
        webrateAccomType.setCreateDate(new Date());
        return webrateAccomType;
    }

    private void assertData(List<WebrateAccomClassMappingDTO> accomMappings) {
        assertEquals(1, accomMappings.size());

        WebrateAccomClassMappingDTO accomClassMappingData = accomMappings.get(0);
        assertEquals(NAME, accomClassMappingData.getWebrateAccomTypeAlias());
        assertEquals(ALIAS, accomClassMappingData.getWebrateAccomName());
        assertEquals(ACCOM_CODE, accomClassMappingData.getAccomClass().getCode());
    }

    private AccomClass getNewAccomClass() {
        int uniqueInt = new Random().nextInt();
        AccomClass accomClass1 = new AccomClass();
        accomClass1.setCode("Accom Code 1" + uniqueInt);
        accomClass1.setDescription("Accom Description 1");
        accomClass1.setName("Accom Name 1" + uniqueInt);
        accomClass1.setPropertyId(UniqueTenantPropertyCreator.getTenantProperty().getId());
        accomClass1.setSystemDefault(0);
        accomClass1.setStatusId(Constants.ACTIVE_STATUS_ID);
        return tenantCrudService().save(accomClass1);
    }

    public static Product createBarProduct() {
        final Product bar = new Product();
        bar.setId(1);
        bar.setSystemDefault(true);
        bar.setDisplayOrder(1);
        return bar;
    }

    public static Product createIndependentProduct() {
        final Product product = new Product();
        product.setId(2);
        product.setSystemDefault(false);
        product.setDisplayOrder(2);
        product.setCode("INDEPENDENT");
        return product;
    }

    private Product createdLinkedProduct() {
        Product linkedProduct = new Product();
        linkedProduct.setId(3);
        linkedProduct.setSystemDefault(false);
        linkedProduct.setDisplayOrder(3);
        linkedProduct.setCode(Product.AGILE_RATES_PRODUCT_CODE);
        return linkedProduct;
    }

}
