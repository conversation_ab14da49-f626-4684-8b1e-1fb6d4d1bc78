package com.ideas.tetris.pacman.services.pricingconfiguration.entity;

import com.ideas.g3.test.AbstractG3JupiterTest;
import com.ideas.tetris.pacman.services.commondaoandenities.global.dao.UniquePropertyCreator;
import com.ideas.tetris.pacman.services.overbooking.entity.OverbookingProperty;
import com.ideas.tetris.pacman.services.overbooking.entity.OverbookingTestUtil;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Test;

import java.math.BigInteger;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;

public class OverbookingPropertyAuditTest extends AbstractG3JupiterTest {

    private static final Byte REVISION_TYPE_CREATE = 0;
    private static final Byte REVISION_TYPE_UPDATE = 1;
    private int PROPERTY_ID = -1;
    private Integer persistedOverbookingPropertyId;
    private Integer latestRevision;

    @Test
    public void shouldMakeAuditEntriesForAddingOverbookingProperty() {
        PROPERTY_ID = UniquePropertyCreator.createUniqueTenantPropertyUntransed(tenantCrudService()).getId();
        OverbookingProperty overbookingProperty = OverbookingTestUtil.getOverBookingProperty();
        overbookingProperty.setPropertyId(PROPERTY_ID);
        OverbookingProperty persistedOverbookingProperty = tenantCrudService().save(overbookingProperty);
        commitTransaction(tenantCrudService());

        persistedOverbookingPropertyId = persistedOverbookingProperty.getId();
        latestRevision = getLatestRevisionforOverbookingAudit();
        List<Object[]> latestQueryResultsForAudit = getLatestRevisionResults(latestRevision);
        Object latestRevisionRow[] = latestQueryResultsForAudit.get(0);

        Integer addedRevisionId = (Integer) latestRevisionRow[1];
        Byte addedRevisionType = (Byte) latestRevisionRow[2];

        assertEquals(REVISION_TYPE_CREATE, addedRevisionType);
        assertEquals(persistedOverbookingProperty.getCreatedByUserId().intValue(), ((BigInteger) latestRevisionRow[11]).intValue());
    }

    @Test
    public void shouldMakeAuditEntriesForUpdatingOverbookingProperty() {
        PROPERTY_ID = UniquePropertyCreator.createUniqueTenantPropertyUntransed(tenantCrudService()).getId();
        OverbookingProperty overbookingProperty = OverbookingTestUtil.getOverBookingProperty();
        overbookingProperty.setPropertyId(PROPERTY_ID);
        OverbookingProperty persistedOverbookingProperty = tenantCrudService().save(overbookingProperty);
        commitTransaction(tenantCrudService());
        persistedOverbookingProperty.setFridayCeiling(100);
        tenantCrudService().save(persistedOverbookingProperty);
        commitTransaction(tenantCrudService());
        persistedOverbookingPropertyId = persistedOverbookingProperty.getId();
        latestRevision = getLatestRevisionforOverbookingAudit();
        List<Object[]> latestQueryResultsForAudit = getLatestRevisionResults(latestRevision);
        Object latestRevisionRow[] = latestQueryResultsForAudit.get(0);

        Integer addedRevisionId = (Integer) latestRevisionRow[1];
        Byte addedRevisionType = (Byte) latestRevisionRow[2];

        assertEquals(REVISION_TYPE_UPDATE, addedRevisionType);
        assertEquals(persistedOverbookingProperty.getCreatedByUserId().intValue(), ((BigInteger) latestRevisionRow[11]).intValue());
    }

    private List<Object[]> getLatestRevisionResults(Integer latestRevision) {
        List<Object[]> latestQueryResultsForAudit = tenantCrudService().getEntityManager()
                .createNativeQuery("select * from Overbooking_Property_Aud where REV = :revId").setParameter("revId", latestRevision)
                .getResultList();
        return latestQueryResultsForAudit;
    }

    private Integer getLatestRevisionforOverbookingAudit() {
        List<Object[]> queryResults = tenantCrudService().getEntityManager()
                .createNativeQuery("select * from Overbooking_Property_Aud order by REV desc").getResultList();
        Object row[] = queryResults.get(0);
        return (Integer) row[1];
    }

    @AfterEach
    public void tearDown() {
        beginTransaction(tenantCrudService());
        deleteTestData();
        commitTransaction(tenantCrudService());
    }

    public void deleteTestData() {
        tenantCrudService().getEntityManager()
                .createNativeQuery("delete from Overbooking_property_AUD where REV in (:rev) and property_ID = " + PROPERTY_ID)
                .setParameter("rev", latestRevision).executeUpdate();

        tenantCrudService()
                .getEntityManager()
                .createNativeQuery(
                        "delete from Overbooking_property where Overbooking_property_ID in (:propertyId) and property_ID = " + PROPERTY_ID)
                .setParameter("propertyId", persistedOverbookingPropertyId).executeUpdate();

        tenantCrudService().getEntityManager().createNativeQuery("delete from Property where Property_ID in (:propertyId)")
                .setParameter("propertyId", PROPERTY_ID).executeUpdate();
    }
}
