package com.ideas.tetris.pacman.services.opera;

import com.google.common.collect.ImmutableSet;
import com.ideas.g3.data.TestProperty;
import com.ideas.g3.test.AbstractG3JupiterTest;
import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.configparams.IntegrationConfigParamName;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomClass;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomType;
import com.ideas.tetris.pacman.services.accommodation.entity.UniqueAccomTypeCreator;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.*;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.dao.UniqueFileMetadataCreator;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.FileMetadata;
import com.ideas.tetris.pacman.services.filemetadata.FileMetadataService;
import com.ideas.tetris.pacman.services.marketsegment.component.MarketSegmentComponent;
import com.ideas.tetris.pacman.services.marketsegment.entity.MarketSegmentSummary;
import com.ideas.tetris.pacman.services.marketsegment.entity.MktSeg;
import com.ideas.tetris.pacman.services.marketsegment.service.AnalyticalMarketSegmentService;
import com.ideas.tetris.pacman.services.property.PropertyConfigParamService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.joda.time.LocalDate;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.math.BigDecimal;
import java.text.ParseException;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.*;
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class DataLoadSummaryServiceFromPacmanTest extends AbstractG3JupiterTest {
    private static final String UPDATE_MKT_SEG_CODE_BY_MS_ID = "update mkt_seg set mkt_seg_code = concat(mkt_seg_code,'_DEF') where mkt_seg_id = :mktSegId";
    private static final String UPDATE_RESERVATIONS_QUERY_BY_DAYS_MKT_SEG_METADATA = "update Reservation_night set arrival_DT = DATEADD(D,:daysBetween,arrival_DT), departure_DT = DATEADD(D,:daysBetween,departure_DT), occupancy_DT = DATEADD(D,:daysBetween,occupancy_DT), " +
            " booking_DT = DATEADD(D,:daysBetween,booking_DT), cancellation_DT = DATEADD(D,:daysBetween,cancellation_DT), file_Metadata_id = :fileMetadataId ,mkt_seg_id = :mktSegId";
    @Spy
    @InjectMocks
    DataLoadSummaryService dataLoadSummaryService;
    @Mock
    MarketSegmentComponent marketSegmentComponent;
    @Mock
    PacmanConfigParamsService pacmanConfigParamsService;
    @Mock
    private AnalyticalMarketSegmentService analyticalMarketSegmentService;
    @Mock
    private PropertyConfigParamService propertyConfigParamService;
    @Mock
    private FileMetadataService fileMetadataService;

    @BeforeEach
    public void setup() {
        MockitoAnnotations.initMocks(this);
        dataLoadSummaryService.crudService = tenantCrudService();
        doNothing().when(analyticalMarketSegmentService).createMissingMarketSegmentFromHotelMarketSummary();
    }

    @Test
    public void testOnlyReservationSummary() {
        final int accomTypeId = 4;
        final int mktSegId = 1;
        final Date minOccupancyDate = tenantCrudService().findByNamedQuerySingleResult(ReservationNight.FIND_MIN_OCCUPANCY_DT);
        final Integer fileMetadataId = UniqueFileMetadataCreator.createFileMetadata(minOccupancyDate);
        final Map<String, Object> onlyFileMetadataIdParam = QueryParameter.with("fileMetadataId", fileMetadataId).parameters();
        tenantCrudService().executeUpdateByNativeQuery("Update Reservation_night set file_metadata_id = :fileMetadataId", onlyFileMetadataIdParam);
        tenantCrudService().executeUpdateByNativeQuery("update mkt_seg set mkt_seg_code = concat(mkt_seg_code,'_DEF') where mkt_seg_id = :mktSegId", QueryParameter.with("mktSegId", mktSegId).parameters());
        MktSeg mktSeg = tenantCrudService().find(MktSeg.class, 1);
        final List<MktSeg> mktSegList = Collections.singletonList(mktSeg);
        when(marketSegmentComponent.findAllMarketSegments()).thenReturn(mktSegList);
        dataLoadSummaryService.buildRemainingMsRtSummary(fileMetadataId, mktSegList);
        final List<CurrentMktSegAccomActivity> summaryObjects = tenantCrudService().findAll(CurrentMktSegAccomActivity.class).stream().filter(m1 -> m1.getFileMetadataId().equals(fileMetadataId)).collect(Collectors.toList());
        final Map<Date, IMktSegAccomActivity> resultMap = summaryObjects.stream().collect(Collectors.toMap(CurrentMktSegAccomActivity::getOccupancyDate, Function.identity()));
        assertEquals(3, summaryObjects.size());
        verifyTransactionSummary(accomTypeId, mktSegId, minOccupancyDate, resultMap);
    }

    @Test
    public void testPostDepartureReservationSummary() {
        final int accomTypeId = 4;
        final int mktSegId = 1;
        final Date minOccupancyDate = tenantCrudService().findByNamedQuerySingleResult(ReservationNight.FIND_MIN_OCCUPANCY_DT);

        final Integer fileMetadataId = UniqueFileMetadataCreator.createFileMetadata(minOccupancyDate);
        final Map<String, Object> onlyFileMetadataIdParam = QueryParameter.with("fileMetadataId", fileMetadataId).parameters();
        tenantCrudService().executeUpdateByNativeQuery("insert into post_departure_revenue values ('7264078401', " +
                ":occupancyDate , :mktSegId , :accomTypeId, 20.0,20.0,0.0,20.0,'MS','RC',NULL)", QueryParameter.with("occupancyDate",
                DateUtil.addDaysToDate(minOccupancyDate, 3)).and("mktSegId", mktSegId).and("accomTypeId", accomTypeId).parameters());
        tenantCrudService().executeUpdateByNativeQuery("Update Reservation_night set file_metadata_id = :fileMetadataId", onlyFileMetadataIdParam);
        tenantCrudService().executeUpdateByNativeQuery("update mkt_seg set mkt_seg_code = concat(mkt_seg_code,'_DEF') where mkt_seg_id = :mktSegId", QueryParameter.with("mktSegId", mktSegId).parameters());
        MktSeg mktSeg = tenantCrudService().find(MktSeg.class, 1);
        final List<MktSeg> mktSegList = Collections.singletonList(mktSeg);
        when(marketSegmentComponent.findAllMarketSegments()).thenReturn(mktSegList);
        dataLoadSummaryService.buildRemainingMsRtSummary(fileMetadataId, mktSegList);
        final List<CurrentMktSegAccomActivity> summaryObjects = tenantCrudService().findAll(CurrentMktSegAccomActivity.class).stream().filter(m1 -> m1.getFileMetadataId().equals(fileMetadataId)).collect(Collectors.toList());
        final Map<Date, IMktSegAccomActivity> resultMap = summaryObjects.stream().collect(Collectors.toMap(CurrentMktSegAccomActivity::getOccupancyDate, Function.identity()));
        assertEquals(4, summaryObjects.size());
        verifyPostDepartureTransactionSummary(accomTypeId, mktSegId, minOccupancyDate, resultMap);
    }

    @Test
    void testPostDepartureReservationSummaryWithPreArrivals() {
        final int accomTypeId = 4;
        final int mktSegId = 1;
        final Date minOccupancyDate = tenantCrudService().findByNamedQuerySingleResult(ReservationNight.FIND_MIN_OCCUPANCY_DT);

        final Integer fileMetadataId = UniqueFileMetadataCreator.createFileMetadata(minOccupancyDate);
        final Map<String, Object> onlyFileMetadataIdParam = QueryParameter.with("fileMetadataId", fileMetadataId).parameters();
        tenantCrudService().executeUpdateByNativeQuery("insert into post_departure_revenue values ('7264078401', " +
                ":occupancyDate , :mktSegId , :accomTypeId, 20.0,20.0,0.0,20.0,'MS','RC',NULL)", QueryParameter.with("occupancyDate",
                DateUtil.addDaysToDate(minOccupancyDate, -1)).and("mktSegId", mktSegId).and("accomTypeId", accomTypeId).parameters());
        tenantCrudService().executeUpdateByNativeQuery("Update Reservation_night set file_metadata_id = :fileMetadataId", onlyFileMetadataIdParam);
        tenantCrudService().executeUpdateByNativeQuery("update mkt_seg set mkt_seg_code = concat(mkt_seg_code,'_DEF') where mkt_seg_id = :mktSegId", QueryParameter.with("mktSegId", mktSegId).parameters());
        MktSeg mktSeg = tenantCrudService().find(MktSeg.class, 1);
        final List<MktSeg> mktSegList = Collections.singletonList(mktSeg);
        when(marketSegmentComponent.findAllMarketSegments()).thenReturn(mktSegList);
        dataLoadSummaryService.buildRemainingMsRtSummary(fileMetadataId, mktSegList);
        final List<CurrentMktSegAccomActivity> summaryObjects = tenantCrudService().findAll(CurrentMktSegAccomActivity.class).stream().filter(m1 -> m1.getFileMetadataId().equals(fileMetadataId)).collect(Collectors.toList());
        final Map<Date, IMktSegAccomActivity> resultMap = summaryObjects.stream().collect(Collectors.toMap(CurrentMktSegAccomActivity::getOccupancyDate, Function.identity()));
        assertEquals(4, summaryObjects.size());
        // Pre-Arrival record should consider only revenue and no solds
        verifyTemporaryMktSegAccomActivity(resultMap, DateUtil.addDaysToDate(minOccupancyDate, -1), accomTypeId, mktSegId, 0, 0, 0, 0, BigDecimal.valueOf(20.00000), BigDecimal.valueOf(20.00000), BigDecimal.valueOf(20.00000));
        verifyTemporaryMktSegAccomActivity(resultMap, minOccupancyDate, accomTypeId, mktSegId, 1, 1, 0, 1, BigDecimal.valueOf(25.00000), BigDecimal.valueOf(47.50000), BigDecimal.valueOf(35.00000));
        verifyTemporaryMktSegAccomActivity(resultMap, DateUtil.addDaysToDate(minOccupancyDate, 1), accomTypeId, mktSegId, 1, 0, 0, 0, BigDecimal.valueOf(25.00000), BigDecimal.valueOf(47.50000), BigDecimal.valueOf(35.00000));
        verifyTemporaryMktSegAccomActivity(resultMap, DateUtil.addDaysToDate(minOccupancyDate, 2), accomTypeId, mktSegId, 0, 0, 1, 0, BigDecimal.valueOf(0.00000), BigDecimal.valueOf(0.0000), BigDecimal.valueOf(0.00000));
    }

    @Test
    public void testSummaryForCancellationNoShow() {
        when(propertyConfigParamService.isIncludeCancellationNoShowRevenueEnabled()).thenReturn(Boolean.TRUE);
        final int accomTypeId = 4;
        final int mktSegId = 1;
        final Date minOccupancyDate = tenantCrudService().findByNamedQuerySingleResult(ReservationNight.FIND_MIN_OCCUPANCY_DT);
        final Integer fileMetadataId = UniqueFileMetadataCreator.createFileMetadata(minOccupancyDate);
        final Map<String, Object> onlyFileMetadataIdParam = QueryParameter.with("fileMetadataId", fileMetadataId).parameters();
        tenantCrudService().executeUpdateByNativeQuery("Update Reservation_night set file_metadata_id = :fileMetadataId", onlyFileMetadataIdParam);
        tenantCrudService().executeUpdateByNativeQuery("update mkt_seg set mkt_seg_code = concat(mkt_seg_code,'_DEF') where mkt_seg_id = :mktSegId", QueryParameter.with("mktSegId", mktSegId).parameters());
        MktSeg mktSeg = tenantCrudService().find(MktSeg.class, 1);
        final List<MktSeg> mktSegList = Collections.singletonList(mktSeg);
        when(marketSegmentComponent.findAllMarketSegments()).thenReturn(mktSegList);
        dataLoadSummaryService.buildRemainingMsRtSummary(fileMetadataId, mktSegList);
        final List<CurrentMktSegAccomActivity> summaryObjects = tenantCrudService().findAll(CurrentMktSegAccomActivity.class).stream().filter(m1 -> m1.getFileMetadataId().equals(fileMetadataId)).collect(Collectors.toList());
        final Map<Date, IMktSegAccomActivity> resultMap = summaryObjects.stream().collect(Collectors.toMap(CurrentMktSegAccomActivity::getOccupancyDate, Function.identity()));
        assertEquals(3, summaryObjects.size());
        verifyTemporaryMktSegAccomActivity(resultMap, minOccupancyDate, accomTypeId, mktSegId, 1, 1, 0, 1, BigDecimal.valueOf(85.00000), BigDecimal.valueOf(70.00000), BigDecimal.valueOf(145.00000));
        verifyTemporaryMktSegAccomActivity(resultMap, DateUtil.addDaysToDate(minOccupancyDate, 1), accomTypeId, mktSegId, 1, 0, 0, 0, BigDecimal.valueOf(85.00000), BigDecimal.valueOf(70.00000), BigDecimal.valueOf(145.00000));
        verifyTemporaryMktSegAccomActivity(resultMap, DateUtil.addDaysToDate(minOccupancyDate, 2), accomTypeId, mktSegId, 0, 0, 1, 0, BigDecimal.valueOf(0.00000), BigDecimal.valueOf(0.0000), BigDecimal.valueOf(0.00000));
    }

    public void verifyTransactionSummary(int accomTypeId, int mktSegId, Date minOccupancyDate, Map<Date, IMktSegAccomActivity> resultMap) {
        verifyTemporaryMktSegAccomActivity(resultMap, minOccupancyDate, accomTypeId, mktSegId, 1, 1, 0, 1, BigDecimal.valueOf(35.00000), BigDecimal.valueOf(57.50000), BigDecimal.valueOf(45.00000));
        verifyTemporaryMktSegAccomActivity(resultMap, DateUtil.addDaysToDate(minOccupancyDate, 1), accomTypeId, mktSegId, 1, 0, 0, 0, BigDecimal.valueOf(35.00000), BigDecimal.valueOf(57.50000), BigDecimal.valueOf(45.00000));
        verifyTemporaryMktSegAccomActivity(resultMap, DateUtil.addDaysToDate(minOccupancyDate, 2), accomTypeId, mktSegId, 0, 0, 1, 0, BigDecimal.valueOf(0.00000), BigDecimal.valueOf(0.0000), BigDecimal.valueOf(0.00000));
    }

    public void verifyPostDepartureTransactionSummary(int accomTypeId, int mktSegId, Date minOccupancyDate, Map<Date, IMktSegAccomActivity> resultMap) {
        verifyTemporaryMktSegAccomActivity(resultMap, minOccupancyDate, accomTypeId, mktSegId, 1, 1, 0, 1, BigDecimal.valueOf(25.00000), BigDecimal.valueOf(47.50000), BigDecimal.valueOf(35.00000));
        verifyTemporaryMktSegAccomActivity(resultMap, DateUtil.addDaysToDate(minOccupancyDate, 1), accomTypeId, mktSegId, 1, 0, 0, 0, BigDecimal.valueOf(25.00000), BigDecimal.valueOf(47.50000), BigDecimal.valueOf(35.00000));
        verifyTemporaryMktSegAccomActivity(resultMap, DateUtil.addDaysToDate(minOccupancyDate, 2), accomTypeId, mktSegId, 0, 0, 1, 0, BigDecimal.valueOf(0.00000), BigDecimal.valueOf(0.0000), BigDecimal.valueOf(0.00000));
        verifyTemporaryMktSegAccomActivity(resultMap, DateUtil.addDaysToDate(minOccupancyDate, 3), accomTypeId, mktSegId, 0, 0, 0, 0, BigDecimal.valueOf(20.00000), BigDecimal.valueOf(20.0000), BigDecimal.valueOf(20.00000));
    }

    private void verifyTransactionPaceSummary(Date snapshotDt, int mktSegId, Date today, Map<Date, PaceMktSegActivity> resultMap) {
        Date minOccupancyDate = today;
        verifyPaceMktSegActivity(resultMap, minOccupancyDate, snapshotDt, mktSegId, 1, 1, 0, 1, BigDecimal.valueOf(35.00000), BigDecimal.valueOf(57.50000), BigDecimal.valueOf(45.00000));
        verifyPaceMktSegActivity(resultMap, DateUtil.addDaysToDate(minOccupancyDate, 1), snapshotDt, mktSegId, 1, 0, 0, 0, BigDecimal.valueOf(35.00000), BigDecimal.valueOf(57.50000), BigDecimal.valueOf(45.00000));
        verifyPaceMktSegActivity(resultMap, DateUtil.addDaysToDate(minOccupancyDate, 2), snapshotDt, mktSegId, 0, 0, 1, 0, BigDecimal.valueOf(0.00000), BigDecimal.valueOf(0.0000), BigDecimal.valueOf(0.00000));
    }

    private void verifyPaceMktSegActivity(Map<Date, PaceMktSegActivity> resultMap, Date minOccupancyDate, Date snapshotDt, Integer mktSegId, int roomsSold, int arrivals, int departures, int cancellations, BigDecimal roomRevenue, BigDecimal foodRevenue, BigDecimal totalRevenue) {
        final PaceMktSegActivity mktSegAccomActivity = resultMap.get(minOccupancyDate);
        assertEquals(snapshotDt, mktSegAccomActivity.getSnapShotDate());
        assertEquals(mktSegId, mktSegAccomActivity.getMktSegId());
        assertEquals(BigDecimal.valueOf(roomsSold), mktSegAccomActivity.getRoomsSold());
        assertEquals(BigDecimal.valueOf(arrivals), mktSegAccomActivity.getArrivals());
        assertEquals(BigDecimal.valueOf(departures), mktSegAccomActivity.getDepartures());
        assertEquals(BigDecimal.valueOf(cancellations), mktSegAccomActivity.getCancellations());
        assertEquals(0, roomRevenue.compareTo(mktSegAccomActivity.getRoomRevenue()));
        assertEquals(0, foodRevenue.compareTo(mktSegAccomActivity.getFoodRevenue()));
        assertEquals(0, totalRevenue.compareTo(mktSegAccomActivity.getTotalRevenue()));
    }

    public void verifyTemporaryMktSegAccomActivity(Map<Date, IMktSegAccomActivity> resultMap, Date minOccupancyDate, Integer accomTypeId, Integer mktSegId, int roomsSold, int arrivals, int departures, int cancellations, BigDecimal roomRevenue, BigDecimal foodRevenue, BigDecimal totalRevenue) {
        final IMktSegAccomActivity mktSegAccomActivity = resultMap.get(minOccupancyDate);
        assertEquals(accomTypeId, mktSegAccomActivity.getAccomTypeId());
        assertEquals(mktSegId, mktSegAccomActivity.getMktSegId());
        assertEquals(BigDecimal.valueOf(roomsSold), mktSegAccomActivity.getRoomsSold());
        assertEquals(BigDecimal.valueOf(arrivals), mktSegAccomActivity.getArrivals());
        assertEquals(BigDecimal.valueOf(departures), mktSegAccomActivity.getDepartures());
        assertEquals(BigDecimal.valueOf(cancellations), mktSegAccomActivity.getCancellations());
        assertEquals(0, roomRevenue.compareTo(mktSegAccomActivity.getRoomRevenue()));
        assertEquals(0, foodRevenue.compareTo(mktSegAccomActivity.getFoodRevenue()));
        assertEquals(0, totalRevenue.compareTo(mktSegAccomActivity.getTotalRevenue()));
    }


    @Test
    public void testOnlyGroupSummary() {
        final int mktSegId = 7;
        final int accomTypeId = 7;
        Date minOccupancyDate = getLatestSnapshotDate().toDate();
        final Integer fileMetadataId = UniqueFileMetadataCreator.createFileMetadata(minOccupancyDate);
        tenantCrudService().executeUpdateByNativeQuery("update mkt_seg set mkt_seg_code = concat(mkt_seg_code,'_DEF') where mkt_seg_id = :mktSegId", QueryParameter.with("mktSegId", mktSegId).parameters());
        MktSeg mktSeg = tenantCrudService().find(MktSeg.class, mktSegId);
        List<MktSeg> mktSegs = Collections.singletonList(mktSeg);
        dataLoadSummaryService.buildRemainingMsRtSummary(fileMetadataId, mktSegs);
        verifyZeroInteractions(marketSegmentComponent);
        CurrentMktSegAccomActivity example = createExample(mktSegId, accomTypeId, fileMetadataId);
        final Collection<CurrentMktSegAccomActivity> summaryObjects = tenantCrudService().findByExample(example);
        assertEquals(6, summaryObjects.size());
        final Map<Date, IMktSegAccomActivity> resultMap = summaryObjects.stream().collect(Collectors.toMap(CurrentMktSegAccomActivity::getOccupancyDate, Function.identity()));
        final Date firstOccDate = getLatestSnapshotDate().plusDays(50).toDate();
        verifyGroupSummary(mktSegId, firstOccDate, accomTypeId, resultMap);
    }

    protected LocalDate getLatestSnapshotDate() {
        FileMetadata latestFm =
                tenantCrudService().findByNamedQuerySingleResult(
                        FileMetadata.SNAPSHOT_DATE_BY_PROPERTY,
                        QueryParameter.with("propertyId", TestProperty.H1.getId()).parameters());
        return LocalDate.fromDateFields(latestFm.getSnapshotDt());
    }

    protected Date getLatestFileMetadataSnapshotDate() {
        FileMetadata latestFm =
                tenantCrudService().findByNamedQuerySingleResult(
                        FileMetadata.SNAPSHOT_DATE_BY_PROPERTY,
                        QueryParameter.with("propertyId", TestProperty.H1.getId()).parameters());
        return latestFm.getSnapshotDt();
    }

    @Test
    public void testGroupAndReservationSummary() {
        final int mktSegId = 7;
        final int accomTypeId = 7;
        Date minOccupancyDate = getLatestSnapshotDate().toDate();
        final Integer fileMetadataId = UniqueFileMetadataCreator.createFileMetadata(minOccupancyDate);
        modifyReservations(mktSegId, accomTypeId, fileMetadataId);
        tenantCrudService().executeUpdateByNativeQuery("update mkt_seg set mkt_seg_code = concat(mkt_seg_code,'_DEF') where mkt_seg_id = :mktSegId", QueryParameter.with("mktSegId", mktSegId).parameters());
        MktSeg mktSeg = tenantCrudService().find(MktSeg.class, mktSegId);
        when(marketSegmentComponent.findStraightMarketSegments()).thenReturn(Collections.emptySet());
        dataLoadSummaryService.buildRemainingMsRtSummary(fileMetadataId, Collections.singletonList(mktSeg));
        verifyZeroInteractions(marketSegmentComponent);
        CurrentMktSegAccomActivity example = createExample(mktSegId, accomTypeId, fileMetadataId);
        final Collection<IMktSegAccomActivity> summaryObjects = tenantCrudService().findByExample(example);
        assertEquals(9, summaryObjects.size());
        final Map<Date, IMktSegAccomActivity> resultMap = summaryObjects.stream().collect(Collectors.toMap(IMktSegAccomActivity::getOccupancyDate, Function.identity()));
        verifyTransactionSummary(accomTypeId, mktSegId, minOccupancyDate, resultMap);
        final Date firstOccDate = getLatestSnapshotDate().plusDays(50).toDate();
        verifyGroupSummary(mktSegId, firstOccDate, accomTypeId, resultMap);
    }

    @Test
    public void shouldHaveZeroSoldsNonZeroRevenueForPseudoRTTrans() throws ParseException {
        final int mktSegId = 7;
        final int accomTypeId = 7;
        int pseudoMktSegId = 3;
        AccomType pseudoAccomType = addPseudoAccomType();


        LocalDate latestSnapshotDate = getLatestSnapshotDate();
        Date minOccupancyDate = latestSnapshotDate.toDate();
        final Integer fileMetadataId = UniqueFileMetadataCreator.createFileMetadata(minOccupancyDate);

        modifyReservations(mktSegId, accomTypeId, fileMetadataId);
        addReservationsForPseudoRT(pseudoMktSegId, pseudoAccomType, fileMetadataId);
        MktSeg mktSeg1 = tenantCrudService().find(MktSeg.class, mktSegId);
        MktSeg mktSeg2 = tenantCrudService().find(MktSeg.class, pseudoMktSegId);

        dataLoadSummaryService.buildRemainingMsRtSummary(fileMetadataId, Arrays.asList(mktSeg1, mktSeg2));

        verifyForPseudoRTTransactions(pseudoMktSegId, pseudoAccomType, latestSnapshotDate, minOccupancyDate, fileMetadataId);
        verifyForNonPseudoRtTransactions(mktSegId, accomTypeId, latestSnapshotDate, minOccupancyDate, fileMetadataId);
    }

    @Test
    public void shouldHaveZeroSoldsNonZeroRevenueForDayUseTrans() throws ParseException {
        when(propertyConfigParamService.isDayUsePopulationEnabled()).thenReturn(Boolean.TRUE);
        final int mktSegId = 7;
        LocalDate latestSnapshotDate = getLatestSnapshotDate();
        Date minOccupancyDate = latestSnapshotDate.toDate();
        final Integer fileMetadataId = UniqueFileMetadataCreator.createFileMetadata(minOccupancyDate);
        AccomType accomType = UniqueAccomTypeCreator.createUniqueAccomType();

        addDayUseReservations(mktSegId, accomType, fileMetadataId);
        MktSeg mktSeg = tenantCrudService().find(MktSeg.class, mktSegId);
        dataLoadSummaryService.buildRemainingMsRtSummary(fileMetadataId,  Collections.singletonList(mktSeg));

        CurrentMktSegAccomActivity example = createExample(mktSegId, accomType.getId(), fileMetadataId);
        final Collection<IMktSegAccomActivity> summaryObjectsForDayUseTransactions = tenantCrudService().findByExample(example);
        assertEquals(2, summaryObjectsForDayUseTransactions.size());
        final Map<Date, IMktSegAccomActivity> resultForDayUseMap = summaryObjectsForDayUseTransactions.stream().collect(Collectors.toMap(IMktSegAccomActivity::getOccupancyDate, Function.identity()));
        verifyTransactionSummaryForPseudoOrDayUseTrans(accomType.getId(), mktSegId, latestSnapshotDate.toDate(), resultForDayUseMap, new BigDecimal(100), new BigDecimal(60), new BigDecimal(200));
        verifyTransactionSummaryForPseudoOrDayUseTrans(accomType.getId(), mktSegId, latestSnapshotDate.plusDays(1).toDate(), resultForDayUseMap, new BigDecimal(100), new BigDecimal(60), new BigDecimal(200));
    }

    @Test
    public void shouldNotConsiderDayUseTransWhenToggleOff() throws ParseException {
        when(propertyConfigParamService.isDayUsePopulationEnabled()).thenReturn(Boolean.FALSE);
        final int mktSegId = 7;
        LocalDate latestSnapshotDate = getLatestSnapshotDate();
        Date minOccupancyDate = latestSnapshotDate.toDate();
        final Integer fileMetadataId = UniqueFileMetadataCreator.createFileMetadata(minOccupancyDate);
        AccomType accomType = UniqueAccomTypeCreator.createUniqueAccomType();

        addDayUseReservations(mktSegId, accomType, fileMetadataId);
        MktSeg mktSeg = tenantCrudService().find(MktSeg.class, mktSegId);
        dataLoadSummaryService.buildRemainingMsRtSummary(fileMetadataId,  Collections.singletonList(mktSeg));

        CurrentMktSegAccomActivity example = createExample(mktSegId, accomType.getId(), fileMetadataId);
        final Collection<IMktSegAccomActivity> summaryObjectsForDayUseTransactions = tenantCrudService().findByExample(example);
        assertEquals(0, summaryObjectsForDayUseTransactions.size());
    }

    @Test
    public void shouldHaveZeroSoldsNonZeroRevenueForDayUseTransForPseudo() throws ParseException {
        when(propertyConfigParamService.isDayUsePopulationEnabled()).thenReturn(Boolean.TRUE);
        final int mktSegId = 7;
        LocalDate latestSnapshotDate = getLatestSnapshotDate();
        Date minOccupancyDate = latestSnapshotDate.toDate();
        final Integer fileMetadataId = UniqueFileMetadataCreator.createFileMetadata(minOccupancyDate);
        AccomType pseudoAccomType = addPseudoAccomType();

        addDayUseReservations(mktSegId, pseudoAccomType, fileMetadataId);
        MktSeg mktSeg = tenantCrudService().find(MktSeg.class, mktSegId);
        dataLoadSummaryService.buildRemainingMsRtSummary(fileMetadataId,  Collections.singletonList(mktSeg));

        CurrentMktSegAccomActivity example = createExample(mktSegId, pseudoAccomType.getId(), fileMetadataId);
        final Collection<IMktSegAccomActivity> summaryObjectsForDayUseTransactions = tenantCrudService().findByExample(example);
        assertEquals(2, summaryObjectsForDayUseTransactions.size());
        final Map<Date, IMktSegAccomActivity> resultForDayUseMap = summaryObjectsForDayUseTransactions.stream().collect(Collectors.toMap(IMktSegAccomActivity::getOccupancyDate, Function.identity()));
        verifyTransactionSummaryForPseudoOrDayUseTrans(pseudoAccomType.getId(), mktSegId, latestSnapshotDate.toDate(), resultForDayUseMap, new BigDecimal(100), new BigDecimal(60), new BigDecimal(200));
        verifyTransactionSummaryForPseudoOrDayUseTrans(pseudoAccomType.getId(), mktSegId, latestSnapshotDate.plusDays(1).toDate(), resultForDayUseMap, new BigDecimal(100), new BigDecimal(60), new BigDecimal(200));
    }

    private void verifyForNonPseudoRtTransactions(int mktSegId, int accomTypeId, LocalDate latestSnapshotDate, Date minOccupancyDate, Integer fileMetadataId) {
        CurrentMktSegAccomActivity example = createExample(mktSegId, accomTypeId, fileMetadataId);
        final Collection<IMktSegAccomActivity> summaryObjects = tenantCrudService().findByExample(example);
        assertEquals(9, summaryObjects.size());
        final Map<Date, IMktSegAccomActivity> resultMap = summaryObjects.stream().collect(Collectors.toMap(IMktSegAccomActivity::getOccupancyDate, Function.identity()));
        verifyTransactionSummary(accomTypeId, mktSegId, minOccupancyDate, resultMap);
        final Date firstOccDate = latestSnapshotDate.plusDays(50).toDate();
        verifyGroupSummary(mktSegId, firstOccDate, accomTypeId, resultMap);
    }

    private void verifyForPseudoRTTransactions(int pseudoMktSegId, AccomType pseudoAccomType, LocalDate latestSnapshotDate, Date minOccupancyDate, Integer fileMetadataId) {
        CurrentMktSegAccomActivity exampleForPseudoRT = createExample(pseudoMktSegId, pseudoAccomType.getId(), fileMetadataId);
        final Collection<IMktSegAccomActivity> summaryObjectsForPseudoTransactions = tenantCrudService().findByExample(exampleForPseudoRT);
        assertEquals(3, summaryObjectsForPseudoTransactions.size());
        final Map<Date, IMktSegAccomActivity> resultForPseudoMap = summaryObjectsForPseudoTransactions.stream().collect(Collectors.toMap(IMktSegAccomActivity::getOccupancyDate, Function.identity()));
        verifyTransactionSummaryForPseudoOrDayUseTrans(pseudoAccomType.getId(), pseudoMktSegId, minOccupancyDate, resultForPseudoMap, new BigDecimal(200), new BigDecimal(120), new BigDecimal(400));
        verifyTransactionSummaryForPseudoOrDayUseTrans(pseudoAccomType.getId(), pseudoMktSegId, latestSnapshotDate.plusDays(1).toDate(), resultForPseudoMap, new BigDecimal(200), new BigDecimal(120), new BigDecimal(400));
        verifyTransactionSummaryForPseudoOrDayUseTrans(pseudoAccomType.getId(), pseudoMktSegId, latestSnapshotDate.plusDays(2).toDate(), resultForPseudoMap, new BigDecimal(100), new BigDecimal(60), new BigDecimal(200));
    }


    private void verifyTransactionSummaryForPseudoOrDayUseTrans(Integer accomTypeId, Integer mktSegId, Date occupancyDate, Map<Date, IMktSegAccomActivity> resultForPseudoMap, BigDecimal expectedRoomRevenue, BigDecimal expectedFoodRevenue, BigDecimal expectedTotalRevenue) {
        final IMktSegAccomActivity mktSegAccomActivity = resultForPseudoMap.get(occupancyDate);
        assertEquals(accomTypeId, mktSegAccomActivity.getAccomTypeId());
        assertEquals(mktSegId, mktSegAccomActivity.getMktSegId());
        assertEquals(BigDecimal.ZERO, mktSegAccomActivity.getRoomsSold());
        assertEquals(BigDecimal.ZERO, mktSegAccomActivity.getArrivals());
        assertEquals(BigDecimal.ZERO, mktSegAccomActivity.getDepartures());
        assertEquals(BigDecimal.ZERO, mktSegAccomActivity.getCancellations());
        assertEquals(0, expectedRoomRevenue.compareTo(mktSegAccomActivity.getRoomRevenue()));
        assertEquals(0, expectedFoodRevenue.compareTo(mktSegAccomActivity.getFoodRevenue()));
        assertEquals(0, expectedTotalRevenue.compareTo(mktSegAccomActivity.getTotalRevenue()));
    }

    private void addReservationsForPseudoRT(int mktSegId, AccomType pseudoAccomType, Integer fileMetadataId) throws ParseException {
        LocalDate snapshotDate = getLatestSnapshotDate();
        LocalDate arrivalDate = snapshotDate;
        List<ReservationNight> transaction1 = UniqueReservationNightsCreator.buildReservationNights(arrivalDate, snapshotDate.plusDays(2), snapshotDate, null, "rateCode", "RESERVED", fileMetadataId, pseudoAccomType.getId(), mktSegId);
        List<ReservationNight> transaction2 = UniqueReservationNightsCreator.buildReservationNights(arrivalDate, snapshotDate.plusDays(3), snapshotDate, null, "rateCode", "RESERVED", fileMetadataId, pseudoAccomType.getId(), mktSegId);
        transaction1.addAll(transaction2);
        tenantCrudService().save(transaction1);

    }

    private void addDayUseReservations(int mktSegId, AccomType accomType, Integer fileMetadataId) throws ParseException {
        LocalDate snapshotDate = getLatestSnapshotDate();
        List<ReservationNight> transaction1 = UniqueReservationNightsCreator.buildReservationNights(snapshotDate, snapshotDate, snapshotDate, null, "rateCode", "RESERVED", fileMetadataId, accomType.getId(), mktSegId);
        List<ReservationNight> transaction2 = UniqueReservationNightsCreator.buildReservationNights(snapshotDate.plusDays(1), snapshotDate.plusDays(1), snapshotDate.plusDays(1), null, "rateCode", "RESERVED", fileMetadataId, accomType.getId(), mktSegId);
        transaction1.addAll(transaction2);
        tenantCrudService().save(transaction1);
    }

    private AccomType addPseudoAccomType() {
        AccomClass accomClass = tenantCrudService().findByNamedQuerySingleResult(AccomClass.BY_CODE, QueryParameter.with("code", "Unassigned").and("propertyId", 5).parameters());
        return UniqueAccomTypeCreator.createUniqueAccomTypeForAccomClassWithStatus(5, accomClass, "PX1", "PX1", 0, "N", 6);


    }

    @Test
    public void testUpdationOfMktAccomActivityFromTempTable() {
        final Date snapshotDt = getLatestSnapshotDate().toDate();
        int pastDays = 1;
        final int futureDays = 3;
        final Integer fileMetadata = UniqueFileMetadataCreator.createFileMetadata(snapshotDt, pastDays, futureDays);
        final List<AccomType> accomTypes = tenantCrudService().findAll(AccomType.class);
        final List<Integer> accomTypeIds = accomTypes.stream().map(AccomType::getId).collect(Collectors.toList());
        final int accomTypeCount = accomTypes.size();
        final List<MarketSegmentSummary> marketSegments = tenantCrudService().findAll(MarketSegmentSummary.class);
        final List<Integer> mktSegIds = marketSegments.stream().map(MarketSegmentSummary::getId).collect(Collectors.toList());
        final int mktSegCount = mktSegIds.size() - 1;
        Map<String, Object> params = QueryParameter.with("snapshotDT", snapshotDt).and("fileMetadataId", fileMetadata).and("businessStartDate", DateUtil.addDaysToDate(snapshotDt, -pastDays))
                .and("businessEndDate", DateUtil.addDaysToDate(snapshotDt, futureDays - 1))
                .and("mktSegIds", mktSegIds).and("accomTypeIds", accomTypeIds)
                .parameters();
        final int updateCount = tenantCrudService().executeUpdateByNamedQuery(CurrentMktSegAccomActivity.INSERT_ZERO_FILL_TEMP_MKT_SEG_ACCOM_ACTIVITY, params);
        assertEquals((pastDays + futureDays) * mktSegCount * accomTypeCount, updateCount);
        final int updatedRows = dataLoadSummaryService.updateMktAccomActivityFromTempTable();
        assertEquals((pastDays + futureDays) * mktSegCount * accomTypeCount, updatedRows);
    }

    @Test
    public void testPopulatePaceTableFromPacman() {
        final int mktSegId = 7;
        final int accomTypeId = 7;
        final List<MktSeg> mktSegList = tenantCrudService().findAll(MktSeg.class);
        final List<Integer> mktSegIds = mktSegList.stream().map(MktSeg::getId).collect(Collectors.toList());
        List<Integer> mktSegIdsBackup = new ArrayList<>(mktSegIds);
        Date minOccupancyDate = getLatestSnapshotDate().toDate();
        final Integer earlierFileMetadataId = UniqueFileMetadataCreator.createFileMetadata(minOccupancyDate);
        dataLoadSummaryService.zeroFillCurrentMktSegAccomActivity(tenantCrudService().find(FileMetadata.class, earlierFileMetadataId), mktSegIds);
        final Integer fileMetadataId = UniqueFileMetadataCreator.createFileMetadata(minOccupancyDate);
        modifyReservations(mktSegId, accomTypeId, fileMetadataId);
        tenantCrudService().executeUpdateByNativeQuery("update mkt_seg set mkt_seg_code = concat(mkt_seg_code,'_DEF') where mkt_seg_id = :mktSegId", QueryParameter.with("mktSegId", mktSegId).parameters());
        MktSeg mktSeg = tenantCrudService().find(MktSeg.class, mktSegId);
        Set<MktSeg> mktSegs = ImmutableSet.of(mktSeg);
        when(marketSegmentComponent.findStraightMarketSegments()).thenReturn(Collections.emptySet());
        when(marketSegmentComponent.findSplitMarketSegments()).thenReturn(mktSegs);
        when(marketSegmentComponent.findAllMarketSegments()).thenReturn(mktSegList);
        when(propertyConfigParamService.isDayUsePopulationEnabled()).thenReturn(false);
        when(pacmanConfigParamsService.getIntegerParameterValue(FeatureTogglesConfigParamName.TOTAL_RATE_ENABLED.value())).thenReturn(0);
        when(pacmanConfigParamsService.getParameterValue(IntegrationConfigParamName.CORE_PROPERTY_EXTERNAL_SYSTEM)).thenReturn(false);
        when(pacmanConfigParamsService.getParameterValue(FeatureTogglesConfigParamName.BUILD_SUMMARY_EXCLUSIVELY_FROM_TRANS_GROUP)).thenReturn(false);
        FileMetadata fm = tenantCrudService().find(FileMetadata.class, fileMetadataId);
        dataLoadSummaryService.loadRoomTypeMarketSegmentDataFromPacman(fm);
        int futureDays = fm.getFutureWindowSize();
        Date snapshotDt = fm.getSnapshotDt();
        int pastDays = fm.getPastWindowSize();
        List<Integer> accomTypeIds = tenantCrudService().findAll(AccomType.class).stream().map(AccomType::getId).collect(Collectors.toList());
        Map<String, Object> params = QueryParameter.with("snapshotDT", snapshotDt).and("fileMetadataId", fileMetadataId)
                .and("businessStartDate", DateUtil.addDaysToDate(snapshotDt, -pastDays)).and("businessEndDate", DateUtil.addDaysToDate(snapshotDt, futureDays - 1))
                .and("mktSegIds", mktSegIdsBackup).and("accomTypeIds", accomTypeIds)
                .parameters();
        tenantCrudService().executeUpdateByNamedQuery(CurrentMktSegAccomActivity.INSERT_ZERO_FILL_TEMP_MKT_SEG_ACCOM_ACTIVITY, params);
        verify(marketSegmentComponent, times(1)).findAllMarketSegments();
    }

    @Test
    public void testPopulatePaceTableFromPacmanSecondExtract() {
        final int mktSegId = 7;
        final int accomTypeId = 7;
        Date minOccupancyDate = getLatestSnapshotDate().toDate();
        final List<MktSeg> mktSegs = tenantCrudService().findAll(MktSeg.class);
        List<Integer> mktSegIds = mktSegs.stream().map(MktSeg::getId).collect(Collectors.toList());
        List<Integer> mktSegIdsBackup = new ArrayList<>(mktSegIds);
        final Integer earlierFileMetadataId = UniqueFileMetadataCreator.createFileMetadata(minOccupancyDate);
        dataLoadSummaryService.zeroFillCurrentMktSegAccomActivity(tenantCrudService().find(FileMetadata.class, earlierFileMetadataId), mktSegIds);
        modifyReservations(mktSegId, accomTypeId, earlierFileMetadataId);
        tenantCrudService().executeUpdateByNativeQuery("update mkt_seg set mkt_seg_code = concat(mkt_seg_code,'_DEF') where mkt_seg_id = :mktSegId", QueryParameter.with("mktSegId", mktSegId).parameters());
        MktSeg mktSeg = tenantCrudService().find(MktSeg.class, mktSegId);
        Set<MktSeg> splitMktSegs = ImmutableSet.of(mktSeg);
        when(propertyConfigParamService.isDayUsePopulationEnabled()).thenReturn(false);
        when(pacmanConfigParamsService.getIntegerParameterValue(FeatureTogglesConfigParamName.TOTAL_RATE_ENABLED.value())).thenReturn(0);
        when(pacmanConfigParamsService.getParameterValue(IntegrationConfigParamName.CORE_PROPERTY_EXTERNAL_SYSTEM)).thenReturn(false);
        when(marketSegmentComponent.findStraightMarketSegments()).thenReturn(Collections.emptySet());
        when(marketSegmentComponent.findSplitMarketSegments()).thenReturn(splitMktSegs);
        when(marketSegmentComponent.findAllMarketSegments()).thenReturn(mktSegs);
        FileMetadata fm = tenantCrudService().find(FileMetadata.class, earlierFileMetadataId);
        when(pacmanConfigParamsService.getParameterValue(FeatureTogglesConfigParamName.BUILD_SUMMARY_EXCLUSIVELY_FROM_TRANS_GROUP)).thenReturn(false);
        dataLoadSummaryService.loadRoomTypeMarketSegmentDataFromPacman(fm);
        int futureDays = fm.getFutureWindowSize();
        Date snapshotDt = fm.getSnapshotDt();
        int pastDays = fm.getPastWindowSize();
        List<Integer> accomTypeIds = tenantCrudService().findAll(AccomType.class).stream().map(AccomType::getId).collect(Collectors.toList());
        Map<String, Object> params = QueryParameter.with("snapshotDT", snapshotDt).and("fileMetadataId", earlierFileMetadataId)
                .and("businessStartDate", DateUtil.addDaysToDate(snapshotDt, -pastDays)).and("businessEndDate", DateUtil.addDaysToDate(snapshotDt, futureDays - 1))
                .and("mktSegIds", mktSegIdsBackup).and("accomTypeIds", accomTypeIds)
                .parameters();
        tenantCrudService().executeUpdateByNamedQuery(CurrentMktSegAccomActivity.INSERT_ZERO_FILL_TEMP_MKT_SEG_ACCOM_ACTIVITY, params);
        verify(marketSegmentComponent, times(1)).findAllMarketSegments();
        final long paceDays = getPaceDays(minOccupancyDate, pastDays);
        final int updatedValues = dataLoadSummaryService.loadRoomTypeMarketSegmentPaceFromPacman(tenantCrudService().find(FileMetadata.class, earlierFileMetadataId));
        Integer cntPaceMktActivity = tenantCrudService().findByNativeQuerySingleResult("select count(*) from pace_mkt_activity where file_metadata_id = :fileMetadataId", QueryParameter.with("fileMetadataId", earlierFileMetadataId).parameters());
        assertEquals(cntPaceMktActivity, updatedValues);
        final Date newSnapshotDt = DateUtil.addDaysToDate(minOccupancyDate, 1);
        //SECOND EXTRACT POPULATION
        final Integer newFileMetadataId = UniqueFileMetadataCreator.createFileMetadata(newSnapshotDt, 45, 364);
        tenantCrudService().executeUpdateByNativeQuery("update reservation_night set file_metadata_id = :fileMetadataId", QueryParameter.with("fileMetadataId", newFileMetadataId).parameters());
        fm = tenantCrudService().find(FileMetadata.class, newFileMetadataId);
        dataLoadSummaryService.loadRoomTypeMarketSegmentDataFromPacman(fm);
        assertEquals(paceDays - 1, getPaceDays(DateUtil.addDaysToDate(minOccupancyDate, 1), pastDays));
    }

    private long getPaceDays(Date minOccupancyDate, int pastDays) {
        final Date maxNonZeroDate = tenantCrudService().findByNamedQuerySingleResult(CurrentMktSegAccomActivity.FIND_MAX_NON_ZERO_OCCUPANCY_DT);
        return 1 + DateUtil.daysBetween(DateUtil.addDaysToDate(minOccupancyDate, -pastDays), maxNonZeroDate);
    }

    @Test
    public void testPopulateMktAccomActivityTableFromPacmanStraightAndSplitMS() {
        // PRESET
        Date today = getLatestSnapshotDate().toDate();
        final int mktSegId = 7;
        tenantCrudService().executeUpdateByNativeQuery(UPDATE_MKT_SEG_CODE_BY_MS_ID, QueryParameter.with("mktSegId", mktSegId).parameters());
        final Integer fileMetadataId = UniqueFileMetadataCreator.createFileMetadata(today);
        updateReservationToCurrentWindow(fileMetadataId, mktSegId);
        MktSeg mktSeg = tenantCrudService().find(MktSeg.class, mktSegId);

        // GIVEN
        final List<MktSeg> mktSegs = tenantCrudService().findAll(MktSeg.class);
        when(marketSegmentComponent.findAllMarketSegments()).thenReturn(mktSegs);
        when(marketSegmentComponent.findSplitMarketSegments()).thenReturn(ImmutableSet.of(mktSeg));
        when(propertyConfigParamService.isDayUsePopulationEnabled()).thenReturn(false);
        when(pacmanConfigParamsService.getIntegerParameterValue(FeatureTogglesConfigParamName.TOTAL_RATE_ENABLED.value())).thenReturn(0);
        when(pacmanConfigParamsService.getParameterValue(IntegrationConfigParamName.CORE_PROPERTY_EXTERNAL_SYSTEM)).thenReturn(false);
        final Set<MktSeg> straightMktSegs = mktSegs.stream().filter(m -> m.getId() != mktSegId).filter(m -> !"-1".equals(m.getCode())).collect(Collectors.toSet());
        when(marketSegmentComponent.findStraightMarketSegments()).thenReturn(straightMktSegs);
        when(pacmanConfigParamsService.getParameterValue(FeatureTogglesConfigParamName.BUILD_SUMMARY_EXCLUSIVELY_FROM_TRANS_GROUP)).thenReturn(false);
        final int testReservationAccomTypeId = 4;

        //WHEN
        FileMetadata fm = tenantCrudService().find(FileMetadata.class, fileMetadataId);
        final int updatedValues = dataLoadSummaryService.loadRoomTypeMarketSegmentDataFromPacman(fm);
        final int mktSegCount = mktSegs.size() - 1;
        int accomCount = tenantCrudService().findByNativeQuerySingleResult("select count(*) from accom_type;", new HashMap<>());
        assertEquals(((fm.getPastWindowSize() + fm.getFutureWindowSize()) * mktSegCount * accomCount), (long) updatedValues);
        //THEN
        final Map<Date, IMktSegAccomActivity> resultMap1 = IntStream.rangeClosed(0, 2).boxed()
                .map(d -> DateUtil.addDaysToDate(today, d))
                .map(dt -> createExampleMktSegAccomActivity(mktSegId, testReservationAccomTypeId, dt))
                .map(ex -> tenantCrudService().findByExampleSingleResult(ex))
                .collect(Collectors.toMap(MktSegAccomActivity::getOccupancyDate, Function.identity()));
        verifyTransactionSummary(testReservationAccomTypeId, mktSegId, today, resultMap1);

        final Date firstOccDate = getLatestSnapshotDate().plusDays(50).toDate();
        final int grpAccomTypeId = 7;
        final Map<Date, IMktSegAccomActivity> resultMap = IntStream.rangeClosed(0, 5).boxed()
                .map(d -> DateUtil.addDaysToDate(firstOccDate, d))
                .map(dt -> createExampleMktSegAccomActivity(mktSegId, grpAccomTypeId, dt))
                .map(ex -> tenantCrudService().findByExampleSingleResult(ex))
                .collect(Collectors.toMap(MktSegAccomActivity::getOccupancyDate, Function.identity()));
        verifyGroupSummary(mktSegId, firstOccDate, grpAccomTypeId, resultMap);
    }

    @Test
    public void testPopulateMktAccomActivityTableFromPacmanStraightAndSplitMS_PostDepartureRevenueForSharerReservation() {
        // PRESET
        Date today = getLatestFileMetadataSnapshotDate();
        final int mktSegId = 7;
        tenantCrudService().executeUpdateByNativeQuery(UPDATE_MKT_SEG_CODE_BY_MS_ID, QueryParameter.with("mktSegId", mktSegId).parameters());
        final Integer fileMetadataId = UniqueFileMetadataCreator.createFileMetadata(today);
        updateReservationToCurrentWindow(fileMetadataId, mktSegId);
        createPostDepartureRevenueData(today);
        MktSeg mktSeg = tenantCrudService().find(MktSeg.class, mktSegId);

        // GIVEN
        final List<MktSeg> mktSegs = tenantCrudService().findAll(MktSeg.class);
        when(marketSegmentComponent.findAllMarketSegments()).thenReturn(mktSegs);
        when(marketSegmentComponent.findSplitMarketSegments()).thenReturn(ImmutableSet.of(mktSeg));
        when(propertyConfigParamService.isDayUsePopulationEnabled()).thenReturn(false);
        when(pacmanConfigParamsService.getIntegerParameterValue(FeatureTogglesConfigParamName.TOTAL_RATE_ENABLED.value())).thenReturn(0);
        when(pacmanConfigParamsService.getParameterValue(IntegrationConfigParamName.CORE_PROPERTY_EXTERNAL_SYSTEM)).thenReturn(false);
        final Set<MktSeg> straightMktSegs = mktSegs.stream().filter(m -> m.getId() != mktSegId).filter(m -> !"-1".equals(m.getCode())).collect(Collectors.toSet());
        when(marketSegmentComponent.findStraightMarketSegments()).thenReturn(straightMktSegs);
        when(pacmanConfigParamsService.getParameterValue(FeatureTogglesConfigParamName.BUILD_SUMMARY_EXCLUSIVELY_FROM_TRANS_GROUP)).thenReturn(false);
        final int testReservationAccomTypeId = 4;

        //WHEN
        FileMetadata fm = tenantCrudService().find(FileMetadata.class, fileMetadataId);
        final int updatedValues = dataLoadSummaryService.loadRoomTypeMarketSegmentDataFromPacman(fm);
        final int mktSegCount = mktSegs.size() - 1;
        int accomCount = tenantCrudService().findByNativeQuerySingleResult("select count(*) from accom_type;", new HashMap<>());
        assertEquals(((fm.getPastWindowSize() + fm.getFutureWindowSize()) * mktSegCount * accomCount), (long) updatedValues);
        //THEN
        final Map<Date, IMktSegAccomActivity> resultMap1 = IntStream.rangeClosed(0, 2).boxed()
                .map(d -> DateUtil.addDaysToDate(today, d))
                .map(dt -> createExampleMktSegAccomActivity(mktSegId, testReservationAccomTypeId, dt))
                .map(ex -> tenantCrudService().findByExampleSingleResult(ex))
                .collect(Collectors.toMap(MktSegAccomActivity::getOccupancyDate, Function.identity()));
        verifyTransactionSummary(testReservationAccomTypeId, mktSegId, today, resultMap1);

        final Date firstOccDate = DateUtil.addDaysToDate(getLatestFileMetadataSnapshotDate(), 50);
        final int grpAccomTypeId = 7;
        final Map<Date, IMktSegAccomActivity> resultMap = IntStream.rangeClosed(0, 5).boxed()
                .map(d -> DateUtil.addDaysToDate(firstOccDate, d))
                .map(dt -> createExampleMktSegAccomActivity(mktSegId, grpAccomTypeId, dt))
                .map(ex -> tenantCrudService().findByExampleSingleResult(ex))
                .collect(Collectors.toMap(MktSegAccomActivity::getOccupancyDate, Function.identity()));
        verifyGroupSummary(mktSegId, firstOccDate, grpAccomTypeId, resultMap);

        assertEquals(new BigDecimal("20.00000"), (BigDecimal) tenantCrudService().findByNativeQuerySingleResult("select sum(Room_Revenue) from Mkt_Accom_Activity where Occupancy_DT = :occupancyDate and  Mkt_Seg_ID = 7 and Accom_Type_ID = 4\n" +
                "group by Occupancy_DT, Mkt_Seg_ID, Accom_Type_ID", QueryParameter.with("occupancyDate", DateUtil.addDaysToDate(today, 3)).parameters()));
    }

    @Test
    public void testPopulatePaceMktActivityFromPacmanStraightAndSplitMS() {
        // PRESET
        Date today = getLatestSnapshotDate().toDate();
        final int mktSegId = 7;
        tenantCrudService().executeUpdateByNativeQuery(UPDATE_MKT_SEG_CODE_BY_MS_ID, QueryParameter.with("mktSegId", mktSegId).parameters());
        final Integer fileMetadataId = UniqueFileMetadataCreator.createFileMetadata(today);
        updateReservationToCurrentWindow(fileMetadataId, mktSegId);
        MktSeg mktSeg = tenantCrudService().find(MktSeg.class, mktSegId);

        // GIVEN
        final List<MktSeg> mktSegs = tenantCrudService().findAll(MktSeg.class);
        when(marketSegmentComponent.findAllMarketSegments()).thenReturn(mktSegs);
        when(marketSegmentComponent.findSplitMarketSegments()).thenReturn(ImmutableSet.of(mktSeg));
        when(propertyConfigParamService.isDayUsePopulationEnabled()).thenReturn(false);
        when(pacmanConfigParamsService.getIntegerParameterValue(FeatureTogglesConfigParamName.TOTAL_RATE_ENABLED.value())).thenReturn(0);
        when(pacmanConfigParamsService.getParameterValue(IntegrationConfigParamName.CORE_PROPERTY_EXTERNAL_SYSTEM)).thenReturn(false);
        when(pacmanConfigParamsService.getParameterValue(FeatureTogglesConfigParamName.BUILD_SUMMARY_EXCLUSIVELY_FROM_TRANS_GROUP)).thenReturn(false);
        final Set<MktSeg> straightMktSegs = mktSegs.stream().filter(m -> m.getId() != mktSegId).filter(m -> !"-1".equals(m.getCode())).collect(Collectors.toSet());
        when(marketSegmentComponent.findStraightMarketSegments()).thenReturn(straightMktSegs);

        //WHEN
        FileMetadata fm = tenantCrudService().find(FileMetadata.class, fileMetadataId);
        dataLoadSummaryService.loadRoomTypeMarketSegmentDataFromPacman(fm);
        dataLoadSummaryService.loadRoomTypeMarketSegmentPaceFromPacman(fm);

        final Map<Date, PaceMktSegActivity> resultMap1 = IntStream.rangeClosed(0, 2).boxed()
                .map(d -> DateUtil.addDaysToDate(today, d))
                .map(dt -> createExamplePaceMktSegActivity(mktSegId, fileMetadataId, dt))
                .map(ex -> tenantCrudService().findByExampleSingleResult(ex))
                .collect(Collectors.toMap(PaceMktSegActivity::getOccupancyDate, Function.identity()));

        verifyTransactionPaceSummary(fm.getSnapshotDtTm(), mktSegId, today, resultMap1);

    }
    @Test
    public void testPopulateSelectiveCurrentMktAccomActivity() {
        final int mktSegId = 7;
        final int accomTypeId = 7;
        Date minOccupancyDate = getLatestSnapshotDate().toDate();
        final Integer fileMetadataId = UniqueFileMetadataCreator.createFileMetadata(minOccupancyDate);
        modifyReservations(mktSegId, accomTypeId, fileMetadataId);

        FileMetadata fm = tenantCrudService().find(FileMetadata.class, fileMetadataId);
        when(fileMetadataService.getFileMetadataById(fileMetadataId)).thenReturn(fm);
        final List<Integer> mktSegIds = Collections.singletonList(mktSegId);
        doNothing().when(dataLoadSummaryService).zeroFillCurrentMktSegAccomActivity(fm, mktSegIds);
        dataLoadSummaryService.populateSelectiveCurrentMktAccomActivity(fileMetadataId, mktSegIds);
        CurrentMktSegAccomActivity example = createExample(mktSegId, accomTypeId, fileMetadataId);
        final Collection<IMktSegAccomActivity> summaryObjects = tenantCrudService().findByExample(example);
        assertEquals(9, summaryObjects.size());
        final Map<Date, IMktSegAccomActivity> resultMap = summaryObjects.stream().collect(Collectors.toMap(IMktSegAccomActivity::getOccupancyDate, Function.identity()));
        verifyTransactionSummary(accomTypeId, mktSegId, minOccupancyDate, resultMap);
    }

    @Test
    public void testPopulateCurrentMktAccomActivityForAllMktSegs() {
        final int mktSegId = 7;
        final int accomTypeId = 7;
        Date minOccupancyDate = getLatestSnapshotDate().toDate();
        final Integer fileMetadataId = UniqueFileMetadataCreator.createFileMetadata(minOccupancyDate);
        modifyReservations(mktSegId, accomTypeId, fileMetadataId);

        MktSeg mktSeg = tenantCrudService().find(MktSeg.class, mktSegId);
        final List<MktSeg> mktSegList = Collections.singletonList(mktSeg);
        when(marketSegmentComponent.findAllMarketSegments()).thenReturn(mktSegList);
        FileMetadata fm = tenantCrudService().find(FileMetadata.class, fileMetadataId);
        final List<Integer> mktSegIds = Collections.singletonList(mktSegId);
        doNothing().when(dataLoadSummaryService).zeroFillCurrentMktSegAccomActivity(fm, mktSegIds);

        dataLoadSummaryService.populateCurrentMktAccomActivityForAllMktSegs(fm);

        verify(dataLoadSummaryService).zeroFillCurrentMktSegAccomActivity(fm, mktSegIds);
        CurrentMktSegAccomActivity example = createExample(mktSegId, accomTypeId, fileMetadataId);
        final Collection<IMktSegAccomActivity> summaryObjects = tenantCrudService().findByExample(example);
        assertEquals(9, summaryObjects.size());
        final Map<Date, IMktSegAccomActivity> resultMap = summaryObjects.stream().collect(Collectors.toMap(IMktSegAccomActivity::getOccupancyDate, Function.identity()));
        verifyTransactionSummary(accomTypeId, mktSegId, minOccupancyDate, resultMap);

        verifyGroupSummary(mktSegId, DateUtil.addDaysToDate(minOccupancyDate, 50), accomTypeId, resultMap);
    }

    @Test
    public void testPopulateCurrentMktAccomActivityForAllMktSegs_SplitReservations() {
        final int mktSegId = 7;
        final int accomTypeId = 7;
        Date minOccupancyDate = getLatestSnapshotDate().toDate();
        tenantCrudService().executeUpdateByNativeQuery("update reservation_night set departure_DT = dateadd(D,1,arrival_DT) where occupancy_DT = '2011-08-10';" +
                "update reservation_night set arrival_DT = dateadd(D,-1,departure_DT) where occupancy_DT = '2011-08-11';");
        final Integer fileMetadataId = UniqueFileMetadataCreator.createFileMetadata(minOccupancyDate);
        modifyReservations(mktSegId, accomTypeId, fileMetadataId);

        MktSeg mktSeg = tenantCrudService().find(MktSeg.class, mktSegId);
        final List<MktSeg> mktSegList = Collections.singletonList(mktSeg);
        when(marketSegmentComponent.findAllMarketSegments()).thenReturn(mktSegList);
        FileMetadata fm = tenantCrudService().find(FileMetadata.class, fileMetadataId);
        final List<Integer> mktSegIds = Collections.singletonList(mktSegId);
        doNothing().when(dataLoadSummaryService).zeroFillCurrentMktSegAccomActivity(fm, mktSegIds);

        dataLoadSummaryService.populateCurrentMktAccomActivityForAllMktSegs(fm);

        verify(dataLoadSummaryService).zeroFillCurrentMktSegAccomActivity(fm, mktSegIds);
        CurrentMktSegAccomActivity example = createExample(mktSegId, accomTypeId, fileMetadataId);
        final Collection<IMktSegAccomActivity> summaryObjects = tenantCrudService().findByExample(example);
        assertEquals(9, summaryObjects.size());
        final Map<Date, IMktSegAccomActivity> resultMap = summaryObjects.stream().collect(Collectors.toMap(IMktSegAccomActivity::getOccupancyDate, Function.identity()));
        verifyTransactionSummary(accomTypeId, mktSegId, minOccupancyDate, resultMap);

        verifyGroupSummary(mktSegId, DateUtil.addDaysToDate(minOccupancyDate, 50), accomTypeId, resultMap);
    }

    private PaceMktSegActivity createExamplePaceMktSegActivity(int mktSegId, Integer fileMetadataId, Date dt) {
        PaceMktSegActivity paceMktSegActivity = new PaceMktSegActivity();
        paceMktSegActivity.setMktSegId(mktSegId);
        paceMktSegActivity.setOccupancyDate(dt);
        paceMktSegActivity.setFileMetadataId(fileMetadataId);
        return paceMktSegActivity;
    }

    public Integer updateReservationToCurrentWindow(int fileMetadataId, int mktSegId) {
        final Date minReservationOccupancyDate = tenantCrudService().findByNamedQuerySingleResult(ReservationNight.FIND_MIN_OCCUPANCY_DT);
        final long daysBetween = DateUtil.daysBetween(minReservationOccupancyDate, getLatestSnapshotDate().toDate());
        return tenantCrudService().executeUpdateByNativeQuery(UPDATE_RESERVATIONS_QUERY_BY_DAYS_MKT_SEG_METADATA, QueryParameter.with("daysBetween", daysBetween).and("fileMetadataId", fileMetadataId).and("mktSegId", mktSegId).parameters());
    }

    private void createPostDepartureRevenueData(Date today) {
        tenantCrudService().executeUpdateByNativeQuery("insert into post_departure_revenue values ('12345678902', " +
                        ":occupancyDate , :mktSegId , :accomTypeId, 20.0,0.0,0.0,20.0,'MS','RC',20.0)",
                QueryParameter.with("occupancyDate", DateUtil.addDaysToDate(today, 3)).and("mktSegId", 7)
                        .and("accomTypeId", 4).parameters());
    }

    public void verifyGroupSummary(int mktSegId, Date firstOccDate, int grpAccomTypeId, Map<Date, IMktSegAccomActivity> resultMap) {
        verifyTemporaryMktSegAccomActivity(resultMap, firstOccDate, grpAccomTypeId, mktSegId, 0, 0, 0, 0, BigDecimal.valueOf(0.00000), BigDecimal.valueOf(0.0000), BigDecimal.valueOf(0.00000));
        verifyTemporaryMktSegAccomActivity(resultMap, DateUtil.addDaysToDate(firstOccDate, 1), grpAccomTypeId, mktSegId, 0, 0, 0, 0, BigDecimal.valueOf(0.00000), BigDecimal.valueOf(0.0000), BigDecimal.valueOf(0.00000));
        verifyTemporaryMktSegAccomActivity(resultMap, DateUtil.addDaysToDate(firstOccDate, 2), grpAccomTypeId, mktSegId, 0, 0, 0, 0, BigDecimal.valueOf(0.00000), BigDecimal.valueOf(0.0000), BigDecimal.valueOf(0.00000));
        verifyTemporaryMktSegAccomActivity(resultMap, DateUtil.addDaysToDate(firstOccDate, 3), grpAccomTypeId, mktSegId, 2, 2, 0, 0, BigDecimal.valueOf(170.00000), BigDecimal.valueOf(0.0000), BigDecimal.valueOf(170.00000));
        verifyTemporaryMktSegAccomActivity(resultMap, DateUtil.addDaysToDate(firstOccDate, 4), grpAccomTypeId, mktSegId, 11, 9, 0, 0, BigDecimal.valueOf(935.00000), BigDecimal.valueOf(0.0000), BigDecimal.valueOf(935.00000));
        verifyTemporaryMktSegAccomActivity(resultMap, DateUtil.addDaysToDate(firstOccDate, 5), grpAccomTypeId, mktSegId, 0, 0, 11, 0, BigDecimal.valueOf(0.00000), BigDecimal.valueOf(0.0000), BigDecimal.valueOf(0.00000));
    }

    public CurrentMktSegAccomActivity createExampleTemp(int mktSegId, int accomTypeId, Date occupancyDate) {
        CurrentMktSegAccomActivity example = new CurrentMktSegAccomActivity();
        example.setMktSegId(mktSegId);
        example.setAccomTypeId(accomTypeId);
        example.setOccupancyDate(occupancyDate);
        return example;
    }

    public MktSegAccomActivity createExampleMktSegAccomActivity(int mktSegId, int accomTypeId, Date occupancyDate) {
        MktSegAccomActivity example = new MktSegAccomActivity();
        example.setMktSegId(mktSegId);
        example.setAccomTypeId(accomTypeId);
        example.setOccupancyDate(occupancyDate);
        return example;

    }

    public CurrentMktSegAccomActivity createExample(int mktSegId, int accomTypeId, Integer fileMetadataId) {
        CurrentMktSegAccomActivity example = new CurrentMktSegAccomActivity();
        example.setFileMetadataId(fileMetadataId);
        example.setMktSegId(mktSegId);
        example.setAccomTypeId(accomTypeId);
        return example;
    }

    public void modifyReservations(int mktSegId, int accomTypeId, int fileMetadataId) {
        Date today = getLatestSnapshotDate().toDate();
        final Date minReservationOccupancyDate = tenantCrudService().findByNamedQuerySingleResult(ReservationNight.FIND_MIN_OCCUPANCY_DT);
        final long daysBetween = DateUtil.daysBetween(minReservationOccupancyDate, today);
        Function<Date, Date> addDays = d -> DateUtil.addDaysToDate(d, (int) daysBetween);
        final List<ReservationNight> reservations = tenantCrudService().findAll(ReservationNight.class);
        for (ReservationNight reservationNight : reservations) {
            reservationNight.setOccupancyDate(addDays.apply(reservationNight.getOccupancyDate()));
            reservationNight.setArrivalDate(addDays.apply(reservationNight.getArrivalDate()));
            reservationNight.setDepartureDate(addDays.apply(reservationNight.getDepartureDate()));
            reservationNight.setBookingDate(addDays.apply(reservationNight.getBookingDate()));
            reservationNight.setCancellationDate(addDays.apply(reservationNight.getCancellationDate()));
            reservationNight.setMarketSegId(mktSegId);
            reservationNight.setAccomTypeId(accomTypeId);
            reservationNight.setFileMetadataId(fileMetadataId);
        }
        tenantCrudService().save(reservations);
        tenantCrudService().flushAndClear();
    }

    private String print(CurrentMktSegAccomActivity m) {
        return ToStringBuilder.reflectionToString(m);
    }

}