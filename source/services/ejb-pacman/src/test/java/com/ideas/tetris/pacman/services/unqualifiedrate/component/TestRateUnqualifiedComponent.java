package com.ideas.tetris.pacman.services.unqualifiedrate.component;

import com.ideas.g3.data.TestProperty;
import com.ideas.g3.test.AbstractG3JupiterTest;
import com.ideas.tetris.pacman.common.configparams.IPConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomClass;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomType;
import com.ideas.tetris.pacman.services.accommodation.entity.UniqueAccomClassCreator;
import com.ideas.tetris.pacman.services.accommodation.entity.UniqueAccomTypeCreator;
import com.ideas.tetris.pacman.services.bestavailablerate.DecisionReasonType;
import com.ideas.tetris.pacman.services.bestavailablerate.OverrideService;
import com.ideas.tetris.pacman.services.bestavailablerate.RateDeterminator;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.Decision;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.DecisionBAROutput;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.DecisionBAROutputOverride;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.DecisionBAROutputOverrideDetails;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.PaceBAROutput;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.FileMetadata;
import com.ideas.tetris.pacman.services.contextholder.BusinessContextService;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.dateservice.dto.DateParameter;
import com.ideas.tetris.pacman.services.decision.DecisionService;
import com.ideas.tetris.pacman.services.eventaggregator.SyncEvent;
import com.ideas.tetris.pacman.services.eventaggregator.SyncEventAggregatorService;
import com.ideas.tetris.pacman.services.eventaggregator.SystemComponent;
import com.ideas.tetris.pacman.services.informationmanager.alert.service.AlertService;
import com.ideas.tetris.pacman.services.informationmanager.dto.AlertType;
import com.ideas.tetris.pacman.services.informationmanager.entity.InfoMgrInstanceEntity;
import com.ideas.tetris.pacman.services.informationmanager.entity.InfoMgrTypeEntity;
import com.ideas.tetris.pacman.services.informationmanager.service.InformationManagerCleanupService;
import com.ideas.tetris.pacman.services.propertygroup.service.PropertyGroupService;
import com.ideas.tetris.pacman.services.unqualifiedrate.dto.RateHeader;
import com.ideas.tetris.pacman.services.unqualifiedrate.dto.Season;
import com.ideas.tetris.pacman.services.unqualifiedrate.dto.SeasonDetail;
import com.ideas.tetris.pacman.services.unqualifiedrate.entity.RateUnqualified;
import com.ideas.tetris.pacman.services.unqualifiedrate.entity.RateUnqualifiedAccomClass;
import com.ideas.tetris.pacman.services.unqualifiedrate.entity.RateUnqualifiedClosed;
import com.ideas.tetris.pacman.services.unqualifiedrate.entity.RateUnqualifiedDefaults;
import com.ideas.tetris.pacman.services.unqualifiedrate.entity.RateUnqualifiedDetails;
import com.ideas.tetris.pacman.services.unqualifiedrate.entity.RateUnqualifiedLOSOverride;
import com.ideas.tetris.pacman.services.unqualifiedrate.entity.RateUnqualifiedMktSeg;
import com.ideas.tetris.pacman.services.unqualifiedrate.entity.RateUnqualifiedUserOverride;
import com.ideas.tetris.pacman.services.unqualifiedrate.entity.UniqueRateUnqualified;
import com.ideas.tetris.pacman.services.unqualifiedrate.entity.UniqueRateUnqualifiedAccomClass;
import com.ideas.tetris.pacman.services.unqualifiedrate.entity.UniqueRateUnqualifiedClosed;
import com.ideas.tetris.pacman.services.unqualifiedrate.entity.UniqueRateUnqualifiedDefaults;
import com.ideas.tetris.pacman.services.unqualifiedrate.entity.UniqueRateUnqualifiedDetails;
import com.ideas.tetris.pacman.services.unqualifiedrate.entity.UniqueRateUnqualifiedLOSOverride;
import com.ideas.tetris.pacman.services.unqualifiedrate.entity.UniqueRateUnqualifiedUserOverride;
import com.ideas.tetris.pacman.services.unqualifiedrate.enums.ActionKeyEnum;
import com.ideas.tetris.pacman.testdatabuilder.AccomClassBuilder;
import com.ideas.tetris.platform.common.contextholder.PacmanThreadLocalContextHolder;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.common.xml.schema.workcontext.v1.WorkContextType;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.hamcrest.Matcher;
import org.joda.time.LocalDate;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import javax.persistence.EntityManager;
import javax.persistence.NoResultException;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static com.ideas.tetris.pacman.common.constants.Constants.CONTEXT_KEY;
import static java.util.Arrays.asList;
import static java.util.Collections.EMPTY_LIST;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.hasItem;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.fail;
import static org.mockito.Mockito.isA;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.reset;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class TestRateUnqualifiedComponent extends AbstractG3JupiterTest {

    @Mock
    private PacmanConfigParamsService configParamsService;
    @Mock
    private SyncEventAggregatorService syncEventAggregatorService;

    @InjectMocks
    protected RateUnqualifiedComponent rateUnqualifiedComponent;

    private BusinessContextService businessContextService = new BusinessContextService();
    private DateService dateService;


    private DecisionService decisionservice;
    private AlertService alertService;
    private OverrideService overrideService;

    @BeforeEach
    public void setUp() {

        dateService = DateService.createTestInstance();
        PropertyGroupService propertyGroupService = new PropertyGroupService();
        dateService.setPropertyGroupService(propertyGroupService);
        dateService.setCrudService(tenantCrudService());
        dateService.setMultiPropertyCrudService(multiPropertyCrudService());

        rateUnqualifiedComponent.setCrudService(tenantCrudService());

        businessContextService.setConfigService(configParamsService);
        businessContextService.setCrudService(tenantCrudService());
        rateUnqualifiedComponent.setBusinessContextService(businessContextService);
        rateUnqualifiedComponent.setDateService(dateService);


        decisionservice = DecisionService.createTestInstance();
        decisionservice.setDateServiceLocal(dateService);
        decisionservice.setCrudService(tenantCrudService());
        rateUnqualifiedComponent.decisionService = decisionservice;
        alertService = new AlertService();
        rateUnqualifiedComponent.alertService = alertService;
        overrideService = new OverrideService();
        overrideService.setCrudService(tenantCrudService());
        overrideService.setRateDeterminator(new RateDeterminator());
        inject(rateUnqualifiedComponent, "overrideService", overrideService);
    }

    @Test
    public void testGetRateUnqualifiedByProperty() {
        RateUnqualified rateUQ = UniqueRateUnqualified.createRateUnqualified();
        List<RateUnqualified> rateUQList = rateUnqualifiedComponent
                .getRateUnqualifiedByProperty(rateUQ.getPropertyId());
        assertFalse(rateUQList.isEmpty());
    }

    @Test
    public void testGetAccomClassDetailsByRateUnqualified() {
        RateUnqualifiedAccomClass rateUQAccomClass = UniqueRateUnqualifiedAccomClass.createRateUnqualifiedAccomClass();
        List<RateUnqualifiedAccomClass> rateUQAccomClassList = rateUnqualifiedComponent.getAccomClassDetailsByRateUnqualified(rateUQAccomClass.getRateUnqualifiedId());
        assertTrue(rateUQAccomClassList.isEmpty());
    }

    @Test
    public void testGetAccomClassDetailsByRateUnqualifiedWithAccomTypeAssociation() {
        AccomType accomType = UniqueAccomTypeCreator.createUniqueAccomType();
        RateUnqualifiedAccomClass rateUQAccomClass = UniqueRateUnqualifiedAccomClass.createRateUnqualifiedAccomClassAssociatedWithAccomType(accomType);
        List<RateUnqualifiedAccomClass> rateUQAccomClassList = rateUnqualifiedComponent.getAccomClassDetailsByRateUnqualified(rateUQAccomClass.getRateUnqualifiedId());
        assertTrue(!rateUQAccomClassList.isEmpty());
    }

    @Test
    public void testGetRateUnqualfiedDetailsByAccomClassOne() {
        RateUnqualifiedAccomClass rateUQAccomClass = UniqueRateUnqualifiedAccomClass.createRateUnqualifiedAccomClass();
        when(configParamsService.getParameterValue(IPConfigParamName.BAR_ALLOW_AVAILABLE_FOR_ARRIVAL.value())).thenReturn("true");
        when(configParamsService.getParameterValue(IPConfigParamName.BAR_ALLOW_MIN_MAX_LOSOVERRIDE.value())).thenReturn("true");


        List<RateUnqualifiedAccomClass> rateUQAccomClassList = rateUnqualifiedComponent
                .getRateUnqualfiedDetailsByAccomClass(rateUQAccomClass.getRateUnqualifiedId(), rateUQAccomClass.getAccomClassId());
        assertTrue(!rateUQAccomClassList.isEmpty());
    }

    @Test
    public void testGetRateUnqualfiedDetailsByAccomClassTwo() {
        RateUnqualifiedDetails rateUQDetails = UniqueRateUnqualifiedDetails.createRateUnqualifiedDetails();
        AccomType accomType = tenantCrudService().find(AccomType.class, rateUQDetails.getAccomTypeId());
        RateUnqualifiedAccomClass rateUQAccomClass = new RateUnqualifiedAccomClass();
        rateUQAccomClass.setRateUnqualifiedId(rateUQDetails.getRateUnqualifiedId());
        rateUQAccomClass.setAccomClassId(accomType.getAccomClass().getId());
        rateUQAccomClass.setCreateDate(new Timestamp((new Date()).getTime()));
        rateUQAccomClass.setLastUpdatedDate(new Timestamp((new Date()).getTime()));
        tenantCrudService().save(rateUQAccomClass);

        when(configParamsService.getParameterValue(IPConfigParamName.BAR_ALLOW_AVAILABLE_FOR_ARRIVAL.value())).thenReturn("true");
        when(configParamsService.getParameterValue(IPConfigParamName.BAR_ALLOW_MIN_MAX_LOSOVERRIDE.value())).thenReturn("true");


        List<RateUnqualifiedAccomClass> rateUQAccomClassList = rateUnqualifiedComponent
                .getRateUnqualfiedDetailsByAccomClass(rateUQDetails.getRateUnqualifiedId(), accomType.getAccomClass().getId());
        assertTrue(!rateUQAccomClassList.isEmpty());
    }

    @Test
    public void testGetRateUnqualfiedDetailsByAccomClassThree() {
        RateUnqualifiedClosed rateUQClosed = UniqueRateUnqualifiedClosed
                .createRateUnqualifiedClosed();
        when(
                configParamsService
                        .getParameterValue(IPConfigParamName.BAR_ALLOW_AVAILABLE_FOR_ARRIVAL.value()))
                .thenReturn("true");
        when(
                configParamsService
                        .getParameterValue(IPConfigParamName.BAR_ALLOW_MIN_MAX_LOSOVERRIDE.value()))
                .thenReturn("true");

        List<RateUnqualifiedAccomClass> rateUQAccomList = rateUnqualifiedComponent
                .getRateUnqualfiedDetailsByAccomClass(rateUQClosed
                                .getRateUnqualifiedAccomClass().getRateUnqualifiedId(),
                        rateUQClosed.getRateUnqualifiedAccomClass()
                                .getAccomClassId());
        assertTrue(!rateUQAccomList.isEmpty());
    }

    @Test
    public void testGetRateUnqualfiedDetailsByAccomClassWithUser() {
        RateUnqualifiedUserOverride rateUQUserOverride = UniqueRateUnqualifiedUserOverride
                .createRateUnqualifiedUserOverride();
        when(
                configParamsService
                        .getParameterValue(IPConfigParamName.BAR_ALLOW_AVAILABLE_FOR_ARRIVAL.value()))
                .thenReturn("true");
        when(
                configParamsService
                        .getParameterValue(IPConfigParamName.BAR_ALLOW_MIN_MAX_LOSOVERRIDE.value()))
                .thenReturn("true");

        List<RateUnqualifiedAccomClass> rateUQAccomList = rateUnqualifiedComponent
                .getRateUnqualfiedDetailsByAccomClass(rateUQUserOverride
                                .getRateUnqualifiedAccomClass().getRateUnqualifiedId(),
                        rateUQUserOverride.getRateUnqualifiedAccomClass()
                                .getAccomClassId());
        assertTrue(!rateUQAccomList.isEmpty());
    }

    @Test
    public void testGetRateUnqualfiedDetailsByAccomClassWithLOS() {
        RateUnqualifiedLOSOverride rateUQLOSOverride = UniqueRateUnqualifiedLOSOverride
                .createRateUnqualifiedLOSOverride(1);
        when(
                configParamsService
                        .getParameterValue(IPConfigParamName.BAR_ALLOW_AVAILABLE_FOR_ARRIVAL.value()))
                .thenReturn("true");
        when(
                configParamsService
                        .getParameterValue(IPConfigParamName.BAR_ALLOW_MIN_MAX_LOSOVERRIDE.value()))
                .thenReturn("true");

        List<RateUnqualifiedAccomClass> rateUQAccomList = rateUnqualifiedComponent
                .getRateUnqualfiedDetailsByAccomClass(rateUQLOSOverride
                                .getRateUnqualifiedAccomClass().getRateUnqualifiedId(),
                        rateUQLOSOverride.getRateUnqualifiedAccomClass()
                                .getAccomClassId());
        assertTrue(!rateUQAccomList.isEmpty());
    }

    @Test
    public void testGetRateUnqualfiedDetailsByAccomClassFour() {
        RateUnqualifiedAccomClass rateUQAccomClass = UniqueRateUnqualifiedAccomClass
                .createRateUnqualifiedAccomClass();
        RateUnqualifiedMktSeg rateUnqualifiedMktSeg = new RateUnqualifiedMktSeg();
        rateUnqualifiedMktSeg.setRateUnqualifiedId(rateUQAccomClass
                .getRateUnqualifiedId());
        rateUnqualifiedMktSeg.setForecastGroupName("forecast Group Name");
        rateUnqualifiedMktSeg.setMktSegId(1);
        rateUnqualifiedMktSeg.setMktSegName("mkt Seg Name");
        tenantCrudService().save(rateUnqualifiedMktSeg);

        when(configParamsService.getParameterValue(IPConfigParamName.BAR_ALLOW_AVAILABLE_FOR_ARRIVAL.value())).thenReturn("true");
        when(configParamsService.getParameterValue(IPConfigParamName.BAR_ALLOW_MIN_MAX_LOSOVERRIDE.value())).thenReturn("true");


        List<RateUnqualifiedAccomClass> rateUQAccomList = rateUnqualifiedComponent
                .getRateUnqualfiedDetailsByAccomClass(
                        rateUQAccomClass.getRateUnqualifiedId(),
                        rateUQAccomClass.getAccomClassId());
        assertTrue(!rateUQAccomList.isEmpty());
    }

    @Test
    public void testDeleteRateUnqualifiedClosed() {
        WorkContextType wc = new WorkContextType();
        wc.setPropertyId(5);
        PacmanThreadLocalContextHolder.put(CONTEXT_KEY, wc);
        when(
                configParamsService
                        .getParameterValue(IPConfigParamName.BAR_ENABLE_SINGLE_BAR_DECISION.value()))
                .thenReturn("true");
        when(configParamsService.getParameterValue(IPConfigParamName.ROH_ROHENABLED.value()))
                .thenReturn("true");


        RateUnqualifiedClosed rateUQClosed = UniqueRateUnqualifiedClosed
                .createRateUnqualifiedClosed();
        rateUnqualifiedComponent.deleteRateUnqualifiedClosed(rateUQClosed
                .getId());
        RateUnqualifiedClosed dbRateUQClosed = tenantCrudService().find(
                RateUnqualifiedClosed.class, rateUQClosed.getId());
        assertNull(dbRateUQClosed);
    }

    @Test
    public void testDeleteRateUnqualifiedClosedForMaster() {
        WorkContextType wc = new WorkContextType();
        wc.setPropertyId(5);
        PacmanThreadLocalContextHolder.put(CONTEXT_KEY, wc);
        when(
                configParamsService
                        .getParameterValue(IPConfigParamName.BAR_ENABLE_SINGLE_BAR_DECISION.value()))
                .thenReturn("true");
        when(configParamsService.getParameterValue(IPConfigParamName.ROH_ROHENABLED.value()))
                .thenReturn("true");


        RateUnqualifiedClosed rateUQClosed = UniqueRateUnqualifiedClosed
                .createRateUnqualifiedClosed();
        RateUnqualifiedClosed rateUQClosed_sameStartEndDateAndAccomClass = UniqueRateUnqualifiedClosed
                .createRateUnqualifiedClosed();

        rateUQClosed.getRateUnqualifiedAccomClass().setAccomClassId(3);
        rateUnqualifiedComponent.deleteRateUnqualifiedClosed(rateUQClosed
                .getId());
        RateUnqualifiedClosed dbRateUQClosed = tenantCrudService().find(
                RateUnqualifiedClosed.class, rateUQClosed.getId());
        assertNull(dbRateUQClosed);

        RateUnqualifiedClosed dbRateUQLOSOverride_sameStartEndDateAndAccomClass = tenantCrudService()
                .find(RateUnqualifiedClosed.class, rateUQClosed_sameStartEndDateAndAccomClass.getId());
        assertNotNull(dbRateUQLOSOverride_sameStartEndDateAndAccomClass);

        rateUnqualifiedComponent
                .deleteRateUnqualifiedClosed(dbRateUQLOSOverride_sameStartEndDateAndAccomClass.getId());
        dbRateUQLOSOverride_sameStartEndDateAndAccomClass = tenantCrudService()
                .find(RateUnqualifiedClosed.class, rateUQClosed_sameStartEndDateAndAccomClass.getId());

        assertNull(dbRateUQLOSOverride_sameStartEndDateAndAccomClass);
    }

    @Test
    public void testDeleteRateUnqualifiedClosedOverrideOne() {
        WorkContextType wc = new WorkContextType();
        wc.setPropertyId(5);
        PacmanThreadLocalContextHolder.put(CONTEXT_KEY, wc);
        when(
                configParamsService
                        .getParameterValue(IPConfigParamName.BAR_ENABLE_SINGLE_BAR_DECISION.value()))
                .thenReturn("true");
        when(configParamsService.getParameterValue(IPConfigParamName.ROH_ROHENABLED.value()))
                .thenReturn("true");


        RateUnqualifiedClosed rateUQClosedOverride = UniqueRateUnqualifiedClosed
                .createRateUnqualifiedClosed();
        rateUnqualifiedComponent
                .deleteRateUnqualifiedClosed(rateUQClosedOverride.getId());
        RateUnqualifiedClosed dbRateUQClosedOverride = tenantCrudService().find(
                RateUnqualifiedClosed.class, rateUQClosedOverride.getId());
        assertNull(dbRateUQClosedOverride);
    }

    @Test
    public void testDeleteRateUnqualifiedClosedOverrideTwo() {
        WorkContextType wc = new WorkContextType();
        wc.setPropertyId(5);
        PacmanThreadLocalContextHolder.put(CONTEXT_KEY, wc);
        when(
                configParamsService
                        .getParameterValue(IPConfigParamName.BAR_ENABLE_SINGLE_BAR_DECISION.value()))
                .thenReturn("true");
        when(configParamsService.getParameterValue(IPConfigParamName.ROH_ROHENABLED.value()))
                .thenReturn("true");

        RateUnqualifiedClosed rateUQClosedOverride = UniqueRateUnqualifiedClosed
                .createRateUnqualifiedClosed();
        rateUQClosedOverride.getRateUnqualifiedAccomClass().setAccomClassId(3);
        rateUnqualifiedComponent
                .deleteRateUnqualifiedClosed(rateUQClosedOverride.getId());
        RateUnqualifiedClosed dbRateUQClosedOverride = tenantCrudService().find(
                RateUnqualifiedClosed.class, rateUQClosedOverride.getId());
        assertNull(dbRateUQClosedOverride);
    }

    @Test
    public void testDeleteRateUnqualifiedLOSOverrideForMaster() {
        WorkContextType wc = new WorkContextType();
        wc.setPropertyId(5);
        PacmanThreadLocalContextHolder.put(CONTEXT_KEY, wc);
        when(
                configParamsService
                        .getParameterValue(IPConfigParamName.BAR_ENABLE_SINGLE_BAR_DECISION.value()))
                .thenReturn("true");
        when(configParamsService.getParameterValue(IPConfigParamName.ROH_ROHENABLED.value()))
                .thenReturn("true");


        RateUnqualifiedLOSOverride rateUQLOSOverride = UniqueRateUnqualifiedLOSOverride
                .createRateUnqualifiedLOSOverride(1);
        RateUnqualifiedLOSOverride rateUQLOSOverride_sameStartEndDateAndAccomClass = UniqueRateUnqualifiedLOSOverride
                .createRateUnqualifiedLOSOverride(1);
        rateUQLOSOverride_sameStartEndDateAndAccomClass.getRateUnqualifiedAccomClass().setAccomClassId(3);
        rateUQLOSOverride.getRateUnqualifiedAccomClass().setAccomClassId(3);
        rateUnqualifiedComponent
                .deleteRateUnqualifiedLOSOverride(rateUQLOSOverride.getId());
        RateUnqualifiedLOSOverride dbRateUQLOSOverride = tenantCrudService().find(
                RateUnqualifiedLOSOverride.class, rateUQLOSOverride.getId());
        assertNull(dbRateUQLOSOverride);

        RateUnqualifiedLOSOverride dbRateUQLOSOverride_sameStartEndDateAndAccomClass = tenantCrudService()
                .find(RateUnqualifiedLOSOverride.class, rateUQLOSOverride_sameStartEndDateAndAccomClass.getId());
        assertNotNull(dbRateUQLOSOverride_sameStartEndDateAndAccomClass);

        rateUnqualifiedComponent
                .deleteRateUnqualifiedLOSOverride(dbRateUQLOSOverride_sameStartEndDateAndAccomClass.getId());
        dbRateUQLOSOverride_sameStartEndDateAndAccomClass = tenantCrudService()
                .find(RateUnqualifiedLOSOverride.class, rateUQLOSOverride_sameStartEndDateAndAccomClass.getId());
        assertNull(dbRateUQLOSOverride_sameStartEndDateAndAccomClass);
    }

    @Test
    public void testDeleteRateUnqualifiedLOSOverride() {
        WorkContextType wc = new WorkContextType();
        wc.setPropertyId(5);
        PacmanThreadLocalContextHolder.put(CONTEXT_KEY, wc);
        when(
                configParamsService
                        .getParameterValue(IPConfigParamName.BAR_ENABLE_SINGLE_BAR_DECISION.value()))
                .thenReturn("true");
        when(configParamsService.getParameterValue(IPConfigParamName.ROH_ROHENABLED.value()))
                .thenReturn("true");


        RateUnqualifiedLOSOverride rateUQLOSOverride = UniqueRateUnqualifiedLOSOverride
                .createRateUnqualifiedLOSOverride(1);
        rateUnqualifiedComponent
                .deleteRateUnqualifiedLOSOverride(rateUQLOSOverride.getId());
        RateUnqualifiedLOSOverride dbRateUQLOSOverride = tenantCrudService().find(
                RateUnqualifiedLOSOverride.class, rateUQLOSOverride.getId());
        assertNull(dbRateUQLOSOverride);
    }

    @Test
    public void testDeleteRateUnqualifiedLOSOverrideOne() {
        WorkContextType wc = new WorkContextType();
        wc.setPropertyId(5);
        PacmanThreadLocalContextHolder.put(CONTEXT_KEY, wc);
        when(
                configParamsService
                        .getParameterValue(IPConfigParamName.BAR_ENABLE_SINGLE_BAR_DECISION.value()))
                .thenReturn("true");
        when(configParamsService.getParameterValue(IPConfigParamName.ROH_ROHENABLED.value()))
                .thenReturn("true");


        RateUnqualifiedLOSOverride rateUQLOSOverride = UniqueRateUnqualifiedLOSOverride
                .createRateUnqualifiedLOSOverride(1);
        rateUnqualifiedComponent
                .deleteRateUnqualifiedLOSOverride(rateUQLOSOverride.getId());
        RateUnqualifiedLOSOverride dbRateUQLOSOverride = tenantCrudService().find(
                RateUnqualifiedLOSOverride.class, rateUQLOSOverride.getId());
        assertNull(dbRateUQLOSOverride);
    }

    @Test
    public void testDeleteRateUnqualifiedLOSOverrideTwo() {
        WorkContextType wc = new WorkContextType();
        wc.setPropertyId(5);
        PacmanThreadLocalContextHolder.put(CONTEXT_KEY, wc);
        when(
                configParamsService
                        .getParameterValue(IPConfigParamName.BAR_ENABLE_SINGLE_BAR_DECISION.value()))
                .thenReturn("true");
        when(configParamsService.getParameterValue(IPConfigParamName.ROH_ROHENABLED.value()))
                .thenReturn("true");

        RateUnqualifiedLOSOverride rateUQLOSOverride = UniqueRateUnqualifiedLOSOverride
                .createRateUnqualifiedLOSOverride(1);
        rateUQLOSOverride.getRateUnqualifiedAccomClass().setAccomClassId(3);
        rateUnqualifiedComponent
                .deleteRateUnqualifiedLOSOverride(rateUQLOSOverride.getId());
        RateUnqualifiedLOSOverride dbRateUQLOSOverride = tenantCrudService().find(
                RateUnqualifiedLOSOverride.class, rateUQLOSOverride.getId());
        assertNull(dbRateUQLOSOverride);
    }

    @Test
    public void testDeleteRateUnqualifiedUserOverride() {
        WorkContextType wc = new WorkContextType();
        wc.setPropertyId(5);
        PacmanThreadLocalContextHolder.put(CONTEXT_KEY, wc);

        when(
                configParamsService
                        .getParameterValue(IPConfigParamName.BAR_ENABLE_SINGLE_BAR_DECISION.value()))
                .thenReturn("true");
        when(configParamsService.getParameterValue(IPConfigParamName.ROH_ROHENABLED.value()))
                .thenReturn("true");


        RateUnqualifiedUserOverride rateUQUserOverride = UniqueRateUnqualifiedUserOverride
                .createRateUnqualifiedUserOverride();
        RateUnqualifiedUserOverride rateUQUOverride_sameStartEndDateAndAccomClass = UniqueRateUnqualifiedUserOverride
                .createRateUnqualifiedUserOverride();

        rateUnqualifiedComponent
                .deleteRateUnqualifiedUserOverride(rateUQUserOverride.getId());
        RateUnqualifiedUserOverride dbRateUQUserOverride = tenantCrudService().find(
                RateUnqualifiedUserOverride.class, rateUQUserOverride.getId());
        assertNull(dbRateUQUserOverride);

        RateUnqualifiedUserOverride dbRateUQLOSOverride_sameStartEndDateAndAccomClass = tenantCrudService()
                .find(RateUnqualifiedUserOverride.class, rateUQUOverride_sameStartEndDateAndAccomClass.getId());
        assertNotNull(dbRateUQLOSOverride_sameStartEndDateAndAccomClass);

        rateUnqualifiedComponent
                .deleteRateUnqualifiedUserOverride(dbRateUQLOSOverride_sameStartEndDateAndAccomClass.getId());
        dbRateUQLOSOverride_sameStartEndDateAndAccomClass = tenantCrudService()
                .find(RateUnqualifiedUserOverride.class, rateUQUOverride_sameStartEndDateAndAccomClass.getId());
        assertNull(dbRateUQLOSOverride_sameStartEndDateAndAccomClass);

    }

    @Test
    public void testDeleteRateUnqualifiedUserOverrideOne() {
        WorkContextType wc = new WorkContextType();
        wc.setPropertyId(5);
        PacmanThreadLocalContextHolder.put(CONTEXT_KEY, wc);
        when(
                configParamsService
                        .getParameterValue(IPConfigParamName.BAR_ENABLE_SINGLE_BAR_DECISION.value()))
                .thenReturn("true");
        when(configParamsService.getParameterValue(IPConfigParamName.ROH_ROHENABLED.value()))
                .thenReturn("true");


        RateUnqualifiedUserOverride rateUQUserOverride = UniqueRateUnqualifiedUserOverride
                .createRateUnqualifiedUserOverride();
        rateUnqualifiedComponent
                .deleteRateUnqualifiedUserOverride(rateUQUserOverride.getId());
        RateUnqualifiedUserOverride dbRateUQUserOverride = tenantCrudService().find(
                RateUnqualifiedUserOverride.class, rateUQUserOverride.getId());
        assertNull(dbRateUQUserOverride);
    }

    @Test
    public void testDeleteRateUnqualifiedUserOverrideTwo() {
        WorkContextType wc = new WorkContextType();
        wc.setPropertyId(5);
        PacmanThreadLocalContextHolder.put(CONTEXT_KEY, wc);
        when(
                configParamsService
                        .getParameterValue(IPConfigParamName.BAR_ENABLE_SINGLE_BAR_DECISION.value()))
                .thenReturn("true");
        when(configParamsService.getParameterValue(IPConfigParamName.ROH_ROHENABLED.value()))
                .thenReturn("true");

        RateUnqualifiedUserOverride rateUQUserOverride = UniqueRateUnqualifiedUserOverride
                .createRateUnqualifiedUserOverride();
        rateUQUserOverride.getRateUnqualifiedAccomClass().setAccomClassId(3);
        rateUnqualifiedComponent
                .deleteRateUnqualifiedUserOverride(rateUQUserOverride.getId());
        RateUnqualifiedUserOverride dbRateUQUserOverride = tenantCrudService().find(
                RateUnqualifiedUserOverride.class, rateUQUserOverride.getId());
        assertNull(dbRateUQUserOverride);
    }

    @Test
    public void testSaveRateUnqualified() {
        RateUnqualified rateUQ = UniqueRateUnqualified.createRateUnqualified();
        rateUQ.setDescription("Updated Description");
        List<RateUnqualified> rateUQList = new ArrayList<RateUnqualified>();
        rateUQList.add(rateUQ);
        rateUnqualifiedComponent.saveRateUnqualified(rateUQList);
        RateUnqualified dbRateUQ = tenantCrudService().find(RateUnqualified.class,
                rateUQ.getId());
        assertEquals(rateUQ.getDescription(), dbRateUQ.getDescription());
    }

    @Test
    public void testGetRateUnqualifiedByPropertyForAccomView() {
        Map<Integer, Integer> accomClassIdToViewOrderMap = createAccomClassIdToViewOrderMap();

        List<RateUnqualifiedAccomClass> rateUQAccomClassList = rateUnqualifiedComponent
                .getRateUnqualifiedByPropertyForAccomView(5);
        RateUnqualifiedAccomClass unqualifiedAccomClass = rateUQAccomClassList.stream().filter(ac -> ac.getAccomClassId() == 2).findFirst().get();

        assertTrue(!rateUQAccomClassList.isEmpty());
        assertEquals(3, unqualifiedAccomClass.getAccomTypeNames().size());
        assertTrue(unqualifiedAccomClass.getAccomTypeNames().contains("DOUBLE"));

        int expectedViewOrder = 1;
        for (RateUnqualifiedAccomClass rateUnqualifiedAccomClass : rateUQAccomClassList) {
            int viewOrder = accomClassIdToViewOrderMap.get(rateUnqualifiedAccomClass.getAccomClassId());
            assertEquals(viewOrder, expectedViewOrder++);
        }

        tenantCrudService().executeUpdateByNativeQuery("update Accom_Type set Display_Status_ID=2 where Accom_Type_ID=6");
        rateUQAccomClassList = rateUnqualifiedComponent.getRateUnqualifiedByPropertyForAccomView(5);
        unqualifiedAccomClass = rateUQAccomClassList.stream().filter(ac -> ac.getAccomClassId() == 2).findFirst().get();
        assertEquals(2, unqualifiedAccomClass.getAccomTypeNames().size());
        assertFalse(unqualifiedAccomClass.getAccomTypeNames().contains("DOUBLE"));
    }

    private Map<Integer, Integer> createAccomClassIdToViewOrderMap() {
        Map<Integer, Integer> map = new HashMap<>();
        List<AccomClass> accomClasses = tenantCrudService().findAll(AccomClass.class);
        accomClasses.forEach(accomClass -> map.put(accomClass.getId(), accomClass.getViewOrder()));
        return map;
    }


    public void createTransactionalDataForAccomClass(String accomClassName, Integer viewOrder, Integer rankOrder) {

        AccomClass c2 = AccomClassBuilder.buildDefaultAccomClass(PacmanWorkContextHelper.getPropertyId());
        c2.setViewOrder(2);

        AccomClass c1 = AccomClassBuilder.buildDefaultAccomClass(PacmanWorkContextHelper.getPropertyId());
        c1.setViewOrder(1);

        AccomClass c3 = AccomClassBuilder.buildDefaultAccomClass(PacmanWorkContextHelper.getPropertyId());
        c3.setViewOrder(3);

        tenantCrudService().save(Arrays.asList(c2, c1, c3));
    }

    @Test
    public void testIsAvailableOnArrivalSelected() {
        when(configParamsService.getParameterValue(IPConfigParamName.BAR_ALLOW_AVAILABLE_FOR_ARRIVAL.value())).thenReturn("true");

        boolean selected = rateUnqualifiedComponent.isAvailableOnArrivalSelected();
        assertTrue(selected);
    }

    @Test
    public void testIsLOSSelected() {
        when(
                configParamsService
                        .getParameterValue(IPConfigParamName.BAR_ALLOW_MIN_MAX_LOSOVERRIDE.value()))
                .thenReturn("true");

        boolean selected = rateUnqualifiedComponent.isLOSSelected();
        assertTrue(selected);
    }

    @Test
    public void testGetRateUnqualifiedByAccomClassId() {
        RateUnqualifiedAccomClass rateUQAccomClass = UniqueRateUnqualifiedAccomClass
                .createRateUnqualifiedAccomClass();
        List<RateUnqualified> rateUQList = rateUnqualifiedComponent
                .getRateUnqualifiedByAccomClassId(rateUQAccomClass
                        .getAccomClassId());
        assertTrue(!rateUQList.isEmpty());
        assertNotNull(rateUQList.get(0).getId());
        assertNotNull(rateUQList.get(0).getName());
        assertNotNull(rateUQList.get(0).getRanking());
    }

    @Test
    public void testSaveRateUnQualifiedDetailsOne() {
        WorkContextType wc = new WorkContextType();
        wc.setPropertyId(5);
        wc.setClientCode("BSTN");
        wc.setPropertyCode("H1");
        PacmanThreadLocalContextHolder.put(CONTEXT_KEY, wc);
        RateUnqualifiedLOSOverride rateUQLOSOverride = UniqueRateUnqualifiedLOSOverride
                .createRateUnqualifiedLOSOverride(1);
        rateUQLOSOverride.setFridayMaxLos(2.1f);
        List<RateUnqualifiedLOSOverride> rateUQLOSList = new ArrayList<RateUnqualifiedLOSOverride>();
        rateUQLOSList.add(rateUQLOSOverride);
        when(configParamsService.getParameterValue(IPConfigParamName.ROH_ROHENABLED.value()))
                .thenReturn("true");
        when(
                configParamsService
                        .getParameterValue(IPConfigParamName.BAR_ALLOW_AVAILABLE_FOR_ARRIVAL.value()))
                .thenReturn("true");
        when(
                configParamsService
                        .getParameterValue(IPConfigParamName.BAR_ALLOW_MIN_MAX_LOSOVERRIDE.value()))
                .thenReturn("true");
        when(
                configParamsService
                        .getParameterValue(IPConfigParamName.BAR_ENABLE_SINGLE_BAR_DECISION.value()))
                .thenReturn("true");

        rateUnqualifiedComponent.saveRateUnQualifiedDetails(null, null,
                rateUQLOSList, null);

        RateUnqualifiedLOSOverride dbLOSOverride = tenantCrudService().find(
                RateUnqualifiedLOSOverride.class, rateUQLOSOverride.getId());
        assertEquals(rateUQLOSOverride.getFridayMaxLos(),
                dbLOSOverride.getFridayMaxLos());

        verify(syncEventAggregatorService).registerSyncEvent(SyncEvent.PRICING_STRATEGY_CONFIG_CHANGED);
    }

    @Test
    public void testSaveRateUnQualifiedDetailsTwo() {
        WorkContextType wc = new WorkContextType();
        wc.setPropertyId(5);
        wc.setClientCode("BSTN");
        wc.setPropertyCode("H1");
        PacmanThreadLocalContextHolder.put(CONTEXT_KEY, wc);

        RateUnqualifiedLOSOverride rateUQLOSOverride = UniqueRateUnqualifiedLOSOverride
                .createRateUnqualifiedLOSOverride(1);
        tenantCrudService().getEntityManager().flush();
        tenantCrudService().getEntityManager().clear();
        rateUQLOSOverride.setMondayAvailable(0);
        List<RateUnqualifiedLOSOverride> rateUQLOSList = new ArrayList<RateUnqualifiedLOSOverride>();
        rateUQLOSList.add(rateUQLOSOverride);
        when(configParamsService.getParameterValue(IPConfigParamName.ROH_ROHENABLED.value()))
                .thenReturn("true");
        when(
                configParamsService
                        .getParameterValue(IPConfigParamName.BAR_ALLOW_AVAILABLE_FOR_ARRIVAL.value()))
                .thenReturn("true");
        when(
                configParamsService
                        .getParameterValue(IPConfigParamName.BAR_ENABLE_SINGLE_BAR_DECISION.value()))
                .thenReturn("true");
        when(
                configParamsService
                        .getParameterValue(IPConfigParamName.BAR_ALLOW_MIN_MAX_LOSOVERRIDE.value()))
                .thenReturn("false");

        rateUnqualifiedComponent.saveRateUnQualifiedDetails(null, null,
                rateUQLOSList, null);

        RateUnqualifiedLOSOverride dbLOSOverride = tenantCrudService().find(
                RateUnqualifiedLOSOverride.class, rateUQLOSOverride.getId());
        assertEquals(rateUQLOSOverride.getMondayAvailable(),
                dbLOSOverride.getMondayAvailable());

        verify(syncEventAggregatorService)
                .registerSyncEvent(SyncEvent.PRICING_STRATEGY_CONFIG_CHANGED);

    }

    @Test
    public void testSaveRateUnQualifiedDetailsThree() {
        WorkContextType wc = new WorkContextType();
        wc.setPropertyId(5);
        wc.setClientCode("BSTN");
        wc.setPropertyCode("H1");
        PacmanThreadLocalContextHolder.put(CONTEXT_KEY, wc);

        RateUnqualifiedLOSOverride rateUQLOSOverride = UniqueRateUnqualifiedLOSOverride
                .createRateUnqualifiedLOSOverride(0);

        List<RateUnqualifiedLOSOverride> rateUQLOSList = new ArrayList<RateUnqualifiedLOSOverride>();
        rateUQLOSList.add(rateUQLOSOverride);
        when(configParamsService.getParameterValue(IPConfigParamName.ROH_ROHENABLED.value()))
                .thenReturn("false");
        when(
                configParamsService
                        .getParameterValue(IPConfigParamName.BAR_ALLOW_AVAILABLE_FOR_ARRIVAL.value()))
                .thenReturn("false");
        when(
                configParamsService
                        .getParameterValue(IPConfigParamName.BAR_ENABLE_SINGLE_BAR_DECISION.value()))
                .thenReturn("true");
        when(
                configParamsService
                        .getParameterValue(IPConfigParamName.BAR_ALLOW_MIN_MAX_LOSOVERRIDE.value()))
                .thenReturn("false");

        rateUnqualifiedComponent.saveRateUnQualifiedDetails(null, null,
                rateUQLOSList, null);
        RateUnqualifiedLOSOverride dbLOSOverride = tenantCrudService().find(
                RateUnqualifiedLOSOverride.class, rateUQLOSOverride.getId());
        assertEquals(rateUQLOSOverride.getMondayAvailable(),
                dbLOSOverride.getMondayAvailable());

        verify(syncEventAggregatorService)
                .registerSyncEvent(SyncEvent.PRICING_STRATEGY_CONFIG_CHANGED);
    }

    @Test
    public void testSaveRateUnQualifiedDetails() {
        RateUnqualifiedDefaults rateUQDef = UniqueRateUnqualifiedDefaults
                .createRateUnqualifiedDefaults();
        rateUQDef.setUserOverrideOnly(0);
        RateUnqualifiedClosed rateUQClosed = UniqueRateUnqualifiedClosed
                .createRateUnqualifiedClosed();
        rateUQClosed.setNotes("Closed Notes Updated");
        RateUnqualifiedUserOverride rateUQUserOverride = UniqueRateUnqualifiedUserOverride
                .createRateUnqualifiedUserOverride();
        rateUQUserOverride.setNotes("User Override Notes Updated");
        rateUQUserOverride.setIsDeleted(null);
        RateUnqualifiedUserOverride rateUQUserOverrideDeleted = UniqueRateUnqualifiedUserOverride
                .createRateUnqualifiedUserOverride();
        rateUQUserOverrideDeleted.setIsDeleted(1);
        RateUnqualifiedLOSOverride rateUQLOSOverride = UniqueRateUnqualifiedLOSOverride
                .createRateUnqualifiedLOSOverride(1);
        rateUQLOSOverride.setNotes("LOS Override Notes Updated");
        RateUnqualifiedDefaults rateUQDefObj = tenantCrudService().find(
                RateUnqualifiedDefaults.class, rateUQDef.getId());
        assertEquals(rateUQDefObj.getUserOverrideOnly(),
                rateUQDef.getUserOverrideOnly());
        RateUnqualifiedClosed rateUQClosedObj = tenantCrudService().find(
                RateUnqualifiedClosed.class, rateUQClosed.getId());
        assertEquals(rateUQClosedObj.getNotes(), rateUQClosed.getNotes());
        RateUnqualifiedUserOverride rateUQUserOverrideObj = tenantCrudService().find(
                RateUnqualifiedUserOverride.class, rateUQUserOverride.getId());
        assertEquals(rateUQUserOverrideObj.getNotes(),
                rateUQUserOverride.getNotes());
        RateUnqualifiedLOSOverride rateUQLOSOverrideObj = tenantCrudService().find(
                RateUnqualifiedLOSOverride.class, rateUQLOSOverride.getId());
        assertEquals(rateUQLOSOverrideObj.getNotes(),
                rateUQLOSOverride.getNotes());
    }

    @Test
    public void testGetRateUnqualifiedByPropertyOne() {
        WorkContextType wc = new WorkContextType();
        wc.setPropertyId(5);
        PacmanThreadLocalContextHolder.put(CONTEXT_KEY, wc);
        List<RateUnqualified> rateUQList = rateUnqualifiedComponent
                .getRateUnqualifiedByProperty();

        assertTrue(!rateUQList.isEmpty());
    }

    @Test
    public void testGetRatesForInventoryHistory() {
        WorkContextType wc = new WorkContextType();
        wc.setPropertyId(5);
        PacmanThreadLocalContextHolder.put(CONTEXT_KEY, wc);
        List<RateUnqualified> rateUQList = rateUnqualifiedComponent
                .getRatesForInventoryHistory();
        assertTrue(!rateUQList.isEmpty());
    }

    @Test
    public void testGetRateUnqualifiedByPropertyForAccomViewOne() {
        WorkContextType wc = new WorkContextType();
        wc.setPropertyId(5);
        PacmanThreadLocalContextHolder.put(CONTEXT_KEY, wc);
        List<RateUnqualifiedAccomClass> rateUQAccomClassList = rateUnqualifiedComponent
                .getRateUnqualifiedByPropertyForAccomView();
        assertTrue(!rateUQAccomClassList.isEmpty());
    }

    @Test
    public void testSaveRateUnQualifiedDetailsDefaults() {
        WorkContextType wc = new WorkContextType();
        wc.setPropertyId(5);
        wc.setClientCode("BSTN");
        wc.setPropertyCode("H1");
        PacmanThreadLocalContextHolder.put(CONTEXT_KEY, wc);
        when(configParamsService.getParameterValue(IPConfigParamName.ROH_ROHENABLED.value()))
                .thenReturn("true");
        when(
                configParamsService
                        .getParameterValue(IPConfigParamName.BAR_ALLOW_AVAILABLE_FOR_ARRIVAL.value()))
                .thenReturn("true");
        when(
                configParamsService
                        .getParameterValue(IPConfigParamName.BAR_ENABLE_SINGLE_BAR_DECISION.value()))
                .thenReturn("true");
        when(
                configParamsService
                        .getParameterValue(IPConfigParamName.BAR_ALLOW_MIN_MAX_LOSOVERRIDE.value()))
                .thenReturn("false");
        RateUnqualifiedDefaults rateUQDefaults = UniqueRateUnqualifiedDefaults
                .createRateUnqualifiedDefaults();
        rateUQDefaults.setFridayAvailable(1);
        rateUQDefaults.getRateUnqualifiedAccomClass().setRateUnqualifiedId(4);

        rateUnqualifiedComponent.saveRateUnQualifiedDetails(rateUQDefaults,
                null, null, null);

        RateUnqualifiedDefaults dbDefaultOverride = tenantCrudService().find(
                RateUnqualifiedDefaults.class, rateUQDefaults.getId());
        assertEquals(rateUQDefaults.getFridayAvailable(),
                dbDefaultOverride.getFridayAvailable());

        verify(syncEventAggregatorService)
                .registerSyncEvent(SyncEvent.PRICING_STRATEGY_CONFIG_CHANGED);

    }

    @Test
    public void testSaveRateUnQualifiedDetailsCloseBAR() {
        WorkContextType wc = new WorkContextType();
        wc.setPropertyId(5);
        wc.setClientCode("BSTN");
        wc.setPropertyCode("H1");
        PacmanThreadLocalContextHolder.put(CONTEXT_KEY, wc);
        when(configParamsService.getParameterValue(IPConfigParamName.ROH_ROHENABLED.value()))
                .thenReturn("true");
        when(
                configParamsService
                        .getParameterValue(IPConfigParamName.BAR_ALLOW_AVAILABLE_FOR_ARRIVAL.value()))
                .thenReturn("true");
        when(
                configParamsService
                        .getParameterValue(IPConfigParamName.BAR_ENABLE_SINGLE_BAR_DECISION.value()))
                .thenReturn("true");
        when(
                configParamsService
                        .getParameterValue(IPConfigParamName.BAR_ALLOW_MIN_MAX_LOSOVERRIDE.value()))
                .thenReturn("false");

        RateUnqualifiedClosed rateUQClosed = UniqueRateUnqualifiedClosed
                .createRateUnqualifiedClosed();
        List<RateUnqualifiedClosed> rateUQClosedList = new ArrayList<RateUnqualifiedClosed>();
        rateUQClosedList.add(rateUQClosed);

        rateUnqualifiedComponent.saveRateUnQualifiedDetails(null,
                rateUQClosedList, null, null);

        RateUnqualifiedClosed dbClosedOverride = tenantCrudService().find(
                RateUnqualifiedClosed.class, rateUQClosed.getId());
        assertEquals(rateUQClosed.getEndDate(), dbClosedOverride.getEndDate());

        verify(syncEventAggregatorService)
                .registerSyncEvent(SyncEvent.PRICING_STRATEGY_CONFIG_CHANGED);

    }

    @Test
    public void testSaveRateUnQualifiedDetailsLOSOverride() {
        WorkContextType wc = new WorkContextType();
        wc.setPropertyId(5);
        wc.setClientCode("BSTN");
        wc.setPropertyCode("H1");
        PacmanThreadLocalContextHolder.put(CONTEXT_KEY, wc);
        when(configParamsService.getParameterValue(IPConfigParamName.ROH_ROHENABLED.value()))
                .thenReturn("true");
        when(
                configParamsService
                        .getParameterValue(IPConfigParamName.BAR_ALLOW_AVAILABLE_FOR_ARRIVAL.value()))
                .thenReturn("true");
        when(
                configParamsService
                        .getParameterValue(IPConfigParamName.BAR_ENABLE_SINGLE_BAR_DECISION.value()))
                .thenReturn("true");
        when(
                configParamsService
                        .getParameterValue(IPConfigParamName.BAR_ALLOW_MIN_MAX_LOSOVERRIDE.value()))
                .thenReturn("false");

        RateUnqualifiedLOSOverride rateUQLOSOverride = UniqueRateUnqualifiedLOSOverride
                .createRateUnqualifiedLOSOverride(1);
        List<RateUnqualifiedLOSOverride> rateUQLOSOverrideList = new ArrayList<RateUnqualifiedLOSOverride>();
        rateUQLOSOverrideList.add(rateUQLOSOverride);

        rateUnqualifiedComponent.saveRateUnQualifiedDetails(null, null,
                rateUQLOSOverrideList, null);

        RateUnqualifiedLOSOverride dbLOSOverride = tenantCrudService().find(
                RateUnqualifiedLOSOverride.class, rateUQLOSOverride.getId());
        assertEquals(rateUQLOSOverride.getEndDate(), dbLOSOverride.getEndDate());

        verify(syncEventAggregatorService)
                .registerSyncEvent(SyncEvent.PRICING_STRATEGY_CONFIG_CHANGED);

    }

    @Test
    public void testSaveRateUnQualifiedDetailsUserOverride() {
        WorkContextType wc = new WorkContextType();
        wc.setPropertyId(5);
        wc.setClientCode("BSTN");
        wc.setPropertyCode("H1");
        PacmanThreadLocalContextHolder.put(CONTEXT_KEY, wc);
        when(configParamsService.getParameterValue(IPConfigParamName.ROH_ROHENABLED.value()))
                .thenReturn("true");
        when(
                configParamsService
                        .getParameterValue(IPConfigParamName.BAR_ALLOW_AVAILABLE_FOR_ARRIVAL.value()))
                .thenReturn("true");
        when(
                configParamsService
                        .getParameterValue(IPConfigParamName.BAR_ENABLE_SINGLE_BAR_DECISION.value()))
                .thenReturn("true");
        when(
                configParamsService
                        .getParameterValue(IPConfigParamName.BAR_ALLOW_MIN_MAX_LOSOVERRIDE.value()))
                .thenReturn("false");

        RateUnqualifiedUserOverride rateUQUserOverride = UniqueRateUnqualifiedUserOverride
                .createRateUnqualifiedUserOverride();
        rateUQUserOverride.setIsDeleted(0);
        rateUQUserOverride.getRateUnqualifiedAccomClass().setRateUnqualifiedId(4);
        List<RateUnqualifiedUserOverride> rateUQUserOverrideList = new ArrayList<RateUnqualifiedUserOverride>();
        rateUQUserOverrideList.add(rateUQUserOverride);

        rateUnqualifiedComponent.saveRateUnQualifiedDetails(null, null, null,
                rateUQUserOverrideList);

        RateUnqualifiedUserOverride dbUserOverride = tenantCrudService().find(
                RateUnqualifiedUserOverride.class, rateUQUserOverride.getId());
        assertEquals(rateUQUserOverride.getEndDate(),
                dbUserOverride.getEndDate());

        verify(syncEventAggregatorService)
                .registerSyncEvent(SyncEvent.PRICING_STRATEGY_CONFIG_CHANGED);
    }

    @Test
    public void testSaveRateUnQualifiedDetailsDefaultsForMaster() {
        WorkContextType wc = new WorkContextType();
        wc.setPropertyId(5);
        wc.setClientCode("BSTN");
        wc.setPropertyCode("H1");
        PacmanThreadLocalContextHolder.put(CONTEXT_KEY, wc);
        when(configParamsService.getParameterValue(IPConfigParamName.ROH_ROHENABLED.value()))
                .thenReturn("true");
        when(
                configParamsService
                        .getParameterValue(IPConfigParamName.BAR_ALLOW_AVAILABLE_FOR_ARRIVAL.value()))
                .thenReturn("true");
        when(
                configParamsService
                        .getParameterValue(IPConfigParamName.BAR_ENABLE_SINGLE_BAR_DECISION.value()))
                .thenReturn("true");
        when(
                configParamsService
                        .getParameterValue(IPConfigParamName.BAR_ALLOW_MIN_MAX_LOSOVERRIDE.value()))
                .thenReturn("false");

        RateUnqualifiedDefaults rateUQDefaults = UniqueRateUnqualifiedDefaults
                .createRateUnqualifiedDefaults();
        rateUQDefaults.setFridayAvailable(1);
        rateUQDefaults.getRateUnqualifiedAccomClass().setRateUnqualifiedId(4);

        rateUnqualifiedComponent.saveRateUnQualifiedDetails(rateUQDefaults,
                null, null, null);

        RateUnqualifiedDefaults dbDefaultOverride = tenantCrudService().find(
                RateUnqualifiedDefaults.class, rateUQDefaults.getId());
        assertEquals(rateUQDefaults.getFridayAvailable(),
                dbDefaultOverride.getFridayAvailable());

        verify(syncEventAggregatorService)
                .registerSyncEvent(SyncEvent.PRICING_STRATEGY_CONFIG_CHANGED);
    }

    @Test
    public void testSaveRateUnQualifiedDetailsCloseBARForMaster() {
        WorkContextType wc = new WorkContextType();
        wc.setPropertyId(5);
        PacmanThreadLocalContextHolder.put(CONTEXT_KEY, wc);
        when(
                configParamsService
                        .getParameterValue(IPConfigParamName.BAR_ENABLE_SINGLE_BAR_DECISION.value()))
                .thenReturn("true");

        RateUnqualifiedComponent rateUnqualifiedComponentTwo = new RateUnqualifiedComponent();
        rateUnqualifiedComponentTwo.setCrudService(tenantCrudService());

        RateUnqualifiedClosed rateUQClosed = UniqueRateUnqualifiedClosed
                .createRateUnqualifiedClosed();
        rateUQClosed.getRateUnqualifiedAccomClass().setAccomClassId(3);
        List<RateUnqualifiedClosed> rateUQClosedList = new ArrayList<RateUnqualifiedClosed>();
        rateUQClosedList.add(rateUQClosed);

        rateUnqualifiedComponentTwo = mock(RateUnqualifiedComponent.class);
        when(rateUnqualifiedComponentTwo.isLOSSelected()).thenReturn(false);

        // FIXME: Ummm, is this testing that a mock gets called in a test?
        rateUnqualifiedComponentTwo.saveRateUnQualifiedDetails(null,
                rateUQClosedList, null, null);
        RateUnqualifiedClosed dbClosedOverride = tenantCrudService().find(
                RateUnqualifiedClosed.class, rateUQClosed.getId());
        assertEquals(rateUQClosed.getEndDate(), dbClosedOverride.getEndDate());

        verify(rateUnqualifiedComponentTwo).saveRateUnQualifiedDetails(null,
                rateUQClosedList, null, null);

    }

    @Test
    public void testSaveRateUnQualifiedDetailsUserOverrideForMaster() {
        WorkContextType wc = new WorkContextType();
        wc.setPropertyId(5);
        wc.setClientCode("BSTN");
        wc.setPropertyCode("H1");
        PacmanThreadLocalContextHolder.put(CONTEXT_KEY, wc);
        when(configParamsService.getParameterValue(IPConfigParamName.ROH_ROHENABLED.value()))
                .thenReturn("true");
        when(
                configParamsService
                        .getParameterValue(IPConfigParamName.BAR_ALLOW_AVAILABLE_FOR_ARRIVAL.value()))
                .thenReturn("true");
        when(
                configParamsService
                        .getParameterValue(IPConfigParamName.BAR_ENABLE_SINGLE_BAR_DECISION.value()))
                .thenReturn("true");
        when(
                configParamsService
                        .getParameterValue(IPConfigParamName.BAR_ALLOW_MIN_MAX_LOSOVERRIDE.value()))
                .thenReturn("false");

        RateUnqualifiedUserOverride rateUQUserOverride = UniqueRateUnqualifiedUserOverride
                .createRateUnqualifiedUserOverride();
        rateUQUserOverride.setIsDeleted(0);
        rateUQUserOverride.getRateUnqualifiedAccomClass().setAccomClassId(3);
        List<RateUnqualifiedUserOverride> rateUQUserOverrideList = new ArrayList<RateUnqualifiedUserOverride>();
        rateUQUserOverrideList.add(rateUQUserOverride);
        rateUnqualifiedComponent.saveRateUnQualifiedDetails(null, null, null,
                rateUQUserOverrideList);

        RateUnqualifiedUserOverride dbUserOverride = tenantCrudService().find(
                RateUnqualifiedUserOverride.class, rateUQUserOverride.getId());
        assertEquals(rateUQUserOverride.getEndDate(),
                dbUserOverride.getEndDate());

        verify(syncEventAggregatorService)
                .registerSyncEvent(SyncEvent.PRICING_STRATEGY_CONFIG_CHANGED);

    }

    @Test
    public void testSaveRateUnQualifiedDetailsLOSOverrideForMaster() {
        WorkContextType wc = new WorkContextType();
        wc.setPropertyId(5);
        wc.setClientCode("BSTN");
        wc.setPropertyCode("H1");
        PacmanThreadLocalContextHolder.put(CONTEXT_KEY, wc);
        when(configParamsService.getParameterValue(IPConfigParamName.ROH_ROHENABLED.value()))
                .thenReturn("true");
        when(
                configParamsService
                        .getParameterValue(IPConfigParamName.BAR_ALLOW_AVAILABLE_FOR_ARRIVAL.value()))
                .thenReturn("true");
        when(
                configParamsService
                        .getParameterValue(IPConfigParamName.BAR_ENABLE_SINGLE_BAR_DECISION.value()))
                .thenReturn("true");
        when(
                configParamsService
                        .getParameterValue(IPConfigParamName.BAR_ALLOW_MIN_MAX_LOSOVERRIDE.value()))
                .thenReturn("false");

        RateUnqualifiedLOSOverride rateUQLOSOverride = UniqueRateUnqualifiedLOSOverride
                .createRateUnqualifiedLOSOverride(1);
        rateUQLOSOverride.getRateUnqualifiedAccomClass().setAccomClassId(3);
        List<RateUnqualifiedLOSOverride> rateUQLOSOverrideList = new ArrayList<RateUnqualifiedLOSOverride>();
        rateUQLOSOverrideList.add(rateUQLOSOverride);

        rateUnqualifiedComponent.saveRateUnQualifiedDetails(null, null,
                rateUQLOSOverrideList, null);
        RateUnqualifiedLOSOverride dbLOSOverride = tenantCrudService().find(
                RateUnqualifiedLOSOverride.class, rateUQLOSOverride.getId());
        assertEquals(rateUQLOSOverride.getEndDate(), dbLOSOverride.getEndDate());

        verify(syncEventAggregatorService)
                .registerSyncEvent(SyncEvent.PRICING_STRATEGY_CONFIG_CHANGED);

    }

    @Test
    public void shouldCreateUnqualifiedRate() {
        String ratePlanName = "1><-_&#\\/";
        String description = "Rate_Code_Description";
        DateParameter startDate = new DateParameter(new Date());
        DateParameter endDate = new DateParameter(new Date());
        ActionKeyEnum actionKey = ActionKeyEnum.CREATE;

        RateHeader rateHeaderFromUI = ratePlanEnteredFromUI(ratePlanName, description, startDate, endDate, actionKey);
        FileMetadata fileMetadata = createFileMetadata();
        rateUnqualifiedComponent.saveUnqualifiedRate(rateHeaderFromUI, fileMetadata.getId());
        RateHeader savedRateHeader = convertRateUnqualifiedEntityToDTO(fetchRatePlan(ratePlanName));
        assertThat(
                savedRateHeader,
                new RatePlanMatcher(expectedRatePlan(ratePlanName, description,
                        startDate, endDate, actionKey)));
        assertEquals(fileMetadata.getId(), savedRateHeader.getFileMetadataId());
        assertEquals(ActionKeyEnum.UNCHANGED, savedRateHeader.getActionKey());
    }

    @Test
    public void ratePlanShouldNotBeNull() {
        RateHeader ratePlan1 = ratePlanEnteredFromUI(null, "Desc 1",
                new DateParameter(new Date()), new DateParameter(new Date()), null);
        final TetrisException tetrisException = assertThrows(TetrisException.class, () -> rateUnqualifiedComponent.saveUnqualifiedRate(ratePlan1, createFileMetadata().getId()));
        assertEquals("Invalid rate plan", tetrisException.getBaseMessage());
    }

    @Test
    public void shouldAllowEmptyDescription() {
        RateHeader ratePlan1 = ratePlanEnteredFromUI("BAR 1", "",
                new DateParameter(new Date()), new DateParameter(new Date()), ActionKeyEnum.CREATE);
        rateUnqualifiedComponent.saveUnqualifiedRate(ratePlan1, createFileMetadata().getId());
    }

    @Test
    public void shouldNotAllowNullDescripttion() {
        RateHeader ratePlan1 = ratePlanEnteredFromUI("BAR 1", null,
                new DateParameter(new Date()), new DateParameter(new Date()), ActionKeyEnum.CREATE);

        final TetrisException tetrisException = assertThrows(TetrisException.class, () -> rateUnqualifiedComponent.saveUnqualifiedRate(ratePlan1, createFileMetadata().getId()));
        assertEquals("Invalid rate plan", tetrisException.getBaseMessage());
    }

    @Test
    public void maxLengthOfDescriptionIs150() {
        RateHeader ratePlan1 = ratePlanEnteredFromUI(
                "BAR 1",
                "Tetris ROCKS !!!!!!!!!!!  Tetris ROCKS !!!!!!!!!!!  Tetris ROCKS !!!!!!!!!!!  Tetris ROCKS !!!!!!!!!!!  Tetris ROCKS !!!!!!!!!!!  Tetris ROCKS !!!!!!!!!!!   Tetris ROCKS !!!!!!!!!!! ",
                new DateParameter(new Date()), new DateParameter(new Date()), ActionKeyEnum.CREATE);

        final TetrisException tetrisException = assertThrows(TetrisException.class, () -> rateUnqualifiedComponent.saveUnqualifiedRate(ratePlan1, createFileMetadata().getId()));
        assertEquals("Invalid rate plan", tetrisException.getBaseMessage());
    }

    @Test
    public void maxLengthOfNameIs50() {
        RateHeader ratePlan1 = ratePlanEnteredFromUI(
                "Tetris ROCKS !!!!!!!!!!! Tetris ROCKS !!!!!!!!!!! Tetris ROCKS !!!!!!!!!!! Tetris ROCKS !!!!!!!!!!!",
                "Tetris ROCKS !!!!!!!!!!!",
                new DateParameter(new Date()), new DateParameter(new Date()), ActionKeyEnum.CREATE);

        final TetrisException tetrisException = assertThrows(TetrisException.class, () -> rateUnqualifiedComponent.saveUnqualifiedRate(ratePlan1, createFileMetadata().getId()));
        assertEquals("Invalid rate plan", tetrisException.getBaseMessage());
    }

    @Test
    public void startDateCannotBeNull() {
        RateHeader ratePlan1 = ratePlanEnteredFromUI(
                "Tetris ROCKS !!!!!!!!!!!",
                "Tetris ROCKS !!!!!!!!!!!",
                null, new DateParameter(new Date()), ActionKeyEnum.CREATE);

        final TetrisException tetrisException = assertThrows(TetrisException.class, () -> rateUnqualifiedComponent.saveUnqualifiedRate(ratePlan1, createFileMetadata().getId()));
        assertEquals("Invalid rate plan", tetrisException.getBaseMessage());
    }

    @Test
    public void endDateCannotBeNull() {
        RateHeader ratePlan1 = ratePlanEnteredFromUI(
                "Tetris ROCKS !!!!!!!!!!!",
                "Tetris ROCKS !!!!!!!!!!!",
                new DateParameter(new Date()), null, ActionKeyEnum.CREATE);

        final TetrisException tetrisException = assertThrows(TetrisException.class, () -> rateUnqualifiedComponent.saveUnqualifiedRate(ratePlan1, createFileMetadata().getId()));
        assertEquals("Invalid rate plan", tetrisException.getBaseMessage());
    }

    @Test
    public void startDateCanNotBeAfterEndDate() {
        RateHeader ratePlan1 = ratePlanEnteredFromUI(
                "Tetris ROCKS !!!!!!!!!!!",
                "Tetris ROCKS !!!!!!!!!!!",
                new DateParameter(DateUtil.addDaysToDate(new Date(), 5)), new DateParameter(new Date()), ActionKeyEnum.CREATE);
        final TetrisException tetrisException = assertThrows(TetrisException.class, () -> rateUnqualifiedComponent.saveUnqualifiedRate(ratePlan1, createFileMetadata().getId()));
        assertEquals("Start date can not be after end date", tetrisException.getBaseMessage());
    }

    @Test
    public void shouldNotAllowBlankRatePlanName() {
//		expectedException.expect(TetrisException.class);
//		expectedException.expectMessage("Invalid rate plan");
        RateHeader ratePlan1 = ratePlanEnteredFromUI(
                " ",
                "Tetris ROCKS !!!!!!!!!!!",
                new DateParameter(new Date()), new DateParameter(new Date()), ActionKeyEnum.CREATE);
        rateUnqualifiedComponent.saveUnqualifiedRate(ratePlan1, createFileMetadata().getId());
    }

    @Test
    public void shouldCreateRateUnqualifiedRates() {
        int ratePlanCount = tenantCrudService().findAll(RateUnqualified.class).size();
        rateUnqualifiedComponent.saveUnqualifiedRates(ratesFromUI());
        flushAndClear();
        List<RateUnqualified> ratePlansOfProperty = tenantCrudService()
                .findAll(RateUnqualified.class);
        assertEquals(ratePlanCount + 3, ratePlansOfProperty.size());
        List<RateHeader> expectedRatePlansFromDB = expectedRatePlansFromDB();

        final List<RateHeader> rateHeaders = convertRateUnqualifiedEntityToDTOS(ratePlansOfProperty);
        for (RateHeader expectedRatePlanFromDB : expectedRatePlansFromDB) {
            assertTrue(rateHeaders.stream().anyMatch(rateHeader ->
                    rateHeader.getDescription().equals(expectedRatePlanFromDB.getDescription())
                            && rateHeader.getName().equals(expectedRatePlanFromDB.getName())
                            && rateHeader.getRateUnqualifiedStartDate().equals(expectedRatePlanFromDB.getRateUnqualifiedStartDate())
                            && rateHeader.getRateUnqualifiedEndDate().equals(expectedRatePlanFromDB.getRateUnqualifiedEndDate())
                            && rateHeader.getPropertyId().equals(expectedRatePlanFromDB.getPropertyId())
                            && rateHeader.getDerivedRateCode().equals(expectedRatePlanFromDB.getDerivedRateCode())
                            && rateHeader.getIncludesPackage().equals(expectedRatePlanFromDB.getIncludesPackage())
                            && rateHeader.getPriceRelative().equals(expectedRatePlanFromDB.getPriceRelative())
                            && rateHeader.getStatusId().equals(expectedRatePlanFromDB.getStatusId())
                            && rateHeader.getSystemDefault().equals(expectedRatePlanFromDB.getSystemDefault())
                            && rateHeader.getYieldable().equals(expectedRatePlanFromDB.getYieldable())));
        }
    }

    @SuppressWarnings("unchecked")
    @Test
    public void shouldSaveRateUnqualifiedAccomClassMappingAndRateUnqualifiedDefaults() {
        CrudService mockCrudService = mockCrudService();
        List<RateUnqualified> list = new ArrayList<RateUnqualified>();

        EntityManager mockEntityManager = mock(EntityManager.class);
        when(mockCrudService.getEntityManager()).thenReturn(mockEntityManager);
        when(mockCrudService.findByNamedQuery(isA(String.class), isA(HashMap.class))).thenReturn(list);
        when(mockCrudService.save(isA(FileMetadata.class))).thenReturn(new FileMetadata());
        when(mockCrudService.save(isA(RateUnqualified.class))).thenReturn(new RateUnqualified());

        rateUnqualifiedComponent.saveUnqualifiedRates(ratesFromUI());
        verify(mockCrudService, times(3)).flush();
        verify(mockCrudService, times(6)).findByNamedQuery(isA(String.class), isA(HashMap.class));
        verify(mockCrudService, times(3)).save(isA(RateUnqualified.class));
    }

    @SuppressWarnings("unchecked")
    @Test
    public void ratePlansAreSavedWithSingleFileMetadata() {
        CrudService mockCrudService = mockCrudService();

        EntityManager mockEntityManager = mock(EntityManager.class);
        when(mockCrudService.getEntityManager()).thenReturn(mockEntityManager);
        when(mockCrudService.save(isA(RateUnqualified.class))).thenReturn(new RateUnqualified());
        when(mockCrudService.save(isA(FileMetadata.class))).thenReturn(new FileMetadata());

        List<RateHeader> ratesFromUI = ratesFromUI();
        List<RateUnqualified> list = new ArrayList<RateUnqualified>();
        when(mockCrudService.findByNamedQuery(isA(String.class), isA(HashMap.class))).thenReturn(list);

        rateUnqualifiedComponent.saveUnqualifiedRates(ratesFromUI);

        verify(mockCrudService, times(3)).flush();
        verify(mockCrudService, times(3)).save(isA(RateUnqualified.class));
        verify(mockCrudService).save(isA(FileMetadata.class));
    }

    @Test
    public void shouldFetchAllRatePlansExceptSystemDefault() {
        List<RateUnqualified> allRatePlansBeforeInsert = tenantCrudService().findAll(RateUnqualified.class);
        List<RateHeader> rateHeaders = convertRateUnqualifiedEntityToDTOS(allRatePlansBeforeInsert);
        Matcher<Iterable<? super RateHeader>> matcher = hasItem(new DefaultRatePlanMatcher());
        assertThat(rateHeaders, matcher);
        rateUnqualifiedComponent.saveUnqualifiedRates(ratesFromUI());
        List<RateHeader> retrievedRatePlans = rateUnqualifiedComponent.fetchUnqualifiedRates();
        assertEquals(allRatePlansBeforeInsert.size() + 2, retrievedRatePlans.size());
        assertFalse(retrievedRatePlans.stream().anyMatch(rateHeader -> rateHeader.getSystemDefault() == 1 && rateHeader.getName().equals("None")));
    }

    private List<RateHeader> convertRateUnqualifiedEntityToDTOS(List<RateUnqualified> rateUnqualifieds) {
        List<RateHeader> rateHeaders = new ArrayList<RateHeader>();
        for (RateUnqualified rateUnqualified : rateUnqualifieds) {
            RateHeader rateHeader = new RateHeader();
            rateHeader.setId(rateUnqualified.getId());
            rateHeader.setDescription(rateUnqualified.getDescription());
            rateHeader.setName(rateUnqualified.getName());
            rateHeader.setRateUnqualifiedStartDate(rateUnqualified.getRateUnqualifiedStartDate());
            rateHeader.setRateUnqualifiedEndDate(rateUnqualified.getRateUnqualifiedEndDate());

            rateHeader.setSystemDefault(rateUnqualified.getSystemDefault());
            rateHeader.setPropertyId(rateUnqualified.getPropertyId());
            rateHeader.setRanking(rateUnqualified.getRanking());
            rateHeader.setRemarks(rateUnqualified.getRemarks());
            rateHeader.setCurrency(rateUnqualified.getCurrency());
            rateHeader.setYieldable(rateUnqualified.getYieldable());
            rateHeader.setPriceRelative(rateUnqualified.getPriceRelative());
            rateHeader.setDerivedRateCode(rateUnqualified.getDerivedRateCode());
            rateHeader.setIncludesPackage(rateUnqualified.getIncludesPackage());
            rateHeader.setStatusId(rateUnqualified.getStatusId());
            rateHeader.setLastUpdateDTTM(rateUnqualified.getLastUpdatedDate());
            rateHeader.setCreatedateDTTM(rateUnqualified.getCreateDate());
            rateHeaders.add(rateHeader);
        }
        return rateHeaders;
    }

    @Test
    public void shouldCreateRateUnqualifiedDetailsWhileCreatingRatePlan() {
        List<RateUnqualified> allRatePlansBeforeInsert = tenantCrudService().findAll(RateUnqualified.class);
        List<RateHeader> rateHeaders = convertRateUnqualifiedEntityToDTOS(allRatePlansBeforeInsert);
        Matcher<Iterable<? super RateHeader>> matcher = hasItem(new DefaultRatePlanMatcher());
        assertThat(rateHeaders, matcher);
        List<RateHeader> ratesFromUI = ratesFromUI();
        rateDetailsFromUI(ratesFromUI);

        DateService mockDateService = mockDateService(2);
        when(configParamsService.getParameterValue(IPConfigParamName.FORECASTING_FORECAST_WINDOW_BDE.value())).thenReturn("365");
        when(syncEventAggregatorService.isSystemComponentDirty(SystemComponent.BAR_RATE_CONFIGURATION_CHANGED)).thenReturn(false);
        when(configParamsService.getParameterValue(IPConfigParamName.BAR_MAX_LOS.value())).thenReturn("8");
        reset(syncEventAggregatorService);
        rateUnqualifiedComponent.saveUnqualifiedRates(ratesFromUI);

        verify(configParamsService, times(3)).getParameterValue(IPConfigParamName.FORECASTING_FORECAST_WINDOW_BDE.value());
        verify(syncEventAggregatorService, times(3)).isSystemComponentDirty(SystemComponent.BAR_RATE_CONFIGURATION_CHANGED);
        verify(syncEventAggregatorService, times(3)).registerSyncEvent(SyncEvent.BAR_RATE_CONFIGURATION_CHANGED);
        verify(configParamsService).getParameterValue(IPConfigParamName.BAR_MAX_LOS.value());
        verify(mockDateService, times(3)).getCaughtUpDate();

        List<RateHeader> retrivedRateHeaders = rateUnqualifiedComponent.fetchUnqualifiedRates();
        assertTrue(retrivedRateHeaders.size() > 0);
        assertEquals(allRatePlansBeforeInsert.size() + 2, retrivedRateHeaders.size());
        List<RateHeader> retrivedJustCreatedRatePlans = new ArrayList<RateHeader>();
        for (RateHeader rateHeader : retrivedRateHeaders) {
            for (RateHeader rateUnqualifiedFromUI : ratesFromUI) {
                if (rateHeader.getName().equals(rateUnqualifiedFromUI.getName())) {
                    retrivedJustCreatedRatePlans.add(rateHeader);
                }
            }
        }
        assertEquals(3, retrivedJustCreatedRatePlans.size());
        for (RateHeader retrievedRateHeader : retrivedJustCreatedRatePlans) {
            assertNotNull(retrievedRateHeader.getId());

            Season firstSeasonDetails = retrievedRateHeader.getSeasons().get(0);//5
            Season secondSeasonDetails = retrievedRateHeader.getSeasons().get(1);//6

            assertEquals(new DateParameter(DateUtil.addDaysToDate(new Date(), 5)), firstSeasonDetails.getDetailsStartDate());
            assertEquals(new DateParameter(DateUtil.addDaysToDate(new Date(), 15)), firstSeasonDetails.getDetailsEndDate());

            assertEquals(new DateParameter(DateUtil.addDaysToDate(new Date(), 20)), secondSeasonDetails.getDetailsStartDate());
            assertEquals(new DateParameter(DateUtil.addDaysToDate(new Date(), 25)), secondSeasonDetails.getDetailsEndDate());

            Season expectedRateUnqualifiedDetailsDTO = addSeasonsDetails(Arrays.asList(5, 7), DateUtil.addDaysToDate(new Date(), 5), DateUtil.addDaysToDate(new Date(), 15));
            assertThat(firstSeasonDetails.getSeasonDetails().get(4), new RatePlanDetailsMatcher(expectedRateUnqualifiedDetailsDTO.getSeasonDetails().get(0)));
            assertThat(firstSeasonDetails.getSeasonDetails().get(2), new RatePlanDetailsMatcher(expectedRateUnqualifiedDetailsDTO.getSeasonDetails().get(1)));

            expectedRateUnqualifiedDetailsDTO = addSeasonsDetails(Arrays.asList(6), DateUtil.addDaysToDate(new Date(), 20), DateUtil.addDaysToDate(new Date(), 25));
            assertThat(secondSeasonDetails.getSeasonDetails().get(0), new RatePlanDetailsMatcher(expectedRateUnqualifiedDetailsDTO.getSeasonDetails().get(0)));
        }

    }

    @Test
    public void shouldCreateRateUnqualifiedDetailsWithExistingRatePlan() {
        List<RateUnqualified> allRatePlansBeforeInsert = tenantCrudService().findAll(RateUnqualified.class);
        List<RateHeader> rateHeaders = convertRateUnqualifiedEntityToDTOS(allRatePlansBeforeInsert);
        Matcher<Iterable<? super RateHeader>> matcher = hasItem(new DefaultRatePlanMatcher());
        assertThat(rateHeaders, matcher);

        when(configParamsService.getParameterValue(IPConfigParamName.BAR_MAX_LOS.value())).thenReturn("8");
        when(configParamsService.getParameterValue(IPConfigParamName.FORECASTING_FORECAST_WINDOW_BDE.value())).thenReturn("365");

        //lets add BAR 1 Rate Plan first
        RateHeader ratePlan1 = ratePlanEnteredFromUI("BAR 1", "Desc 1", new DateParameter(new Date()), new DateParameter(new Date()), ActionKeyEnum.CREATE);
        List<RateHeader> ratePlanList = new ArrayList<RateHeader>();
        ratePlanList.add(ratePlan1);
        rateUnqualifiedComponent.saveUnqualifiedRates(ratePlanList);

        tenantCrudService().getEntityManager().flush();
        tenantCrudService().getEntityManager().clear();

        List<RateHeader> retrievedRatePlans = rateUnqualifiedComponent.fetchUnqualifiedRates();
        assertTrue(retrievedRatePlans.size() > 0);
        RateHeader addedRatePlan = getRateUnqualifiedByName("BAR 1");

        //now add details for added rate plan - a case of adding details for existing rate plan
        List<RateHeader> ratesFromUI = new ArrayList<RateHeader>();
        ratesFromUI.add(addedRatePlan);
        rateDetailsFromUI(ratesFromUI);
        DateService mockDateService = mockDateService(2);
        //when(configParamsService.getParameterValue(Constants.MAX_LOS)).thenReturn("8");

        when(syncEventAggregatorService.isSystemComponentDirty(SystemComponent.BAR_RATE_CONFIGURATION_CHANGED)).thenReturn(false);

        rateUnqualifiedComponent.saveUnqualifiedRates(ratesFromUI);
        tenantCrudService().getEntityManager().flush();
        tenantCrudService().getEntityManager().clear();

        verify(mockDateService, times(1)).getCaughtUpDate();
        verify(configParamsService, times(2)).getParameterValue(IPConfigParamName.BAR_MAX_LOS.value());
        verify(configParamsService).getParameterValue(IPConfigParamName.FORECASTING_FORECAST_WINDOW_BDE.value());

        List<RateHeader> retrievedRatePlansWithDetails = rateUnqualifiedComponent.fetchUnqualifiedRates();
        List<RateHeader> retrivedJustCreatedRatePlans = new ArrayList<RateHeader>();
        for (RateHeader rateUnqualified : retrievedRatePlansWithDetails) {
            for (RateHeader rateUnqualifiedFromUI : ratesFromUI) {
                if (rateUnqualified.getName().equals(rateUnqualifiedFromUI.getName())) {
                    retrivedJustCreatedRatePlans.add(rateUnqualified);
                }
            }
        }
        assertEquals(1, retrivedJustCreatedRatePlans.size());
        for (RateHeader retrievedRateHeader : retrivedJustCreatedRatePlans) {
            assertNotNull(retrievedRateHeader.getId());

            Season firstSeasonDetails = retrievedRateHeader.getSeasons().get(0);//5
            Season secondSeasonDetails = retrievedRateHeader.getSeasons().get(1);//6

            assertEquals(new DateParameter(DateUtil.addDaysToDate(new Date(), 5)), firstSeasonDetails.getDetailsStartDate());
            assertEquals(new DateParameter(DateUtil.addDaysToDate(new Date(), 15)), firstSeasonDetails.getDetailsEndDate());

            assertEquals(new DateParameter(DateUtil.addDaysToDate(new Date(), 20)), secondSeasonDetails.getDetailsStartDate());
            assertEquals(new DateParameter(DateUtil.addDaysToDate(new Date(), 25)), secondSeasonDetails.getDetailsEndDate());

            Season expectedRateUnqualifiedDetailsDTO = addSeasonsDetails(Arrays.asList(5, 7), DateUtil.addDaysToDate(new Date(), 5), DateUtil.addDaysToDate(new Date(), 15));
            assertThat(firstSeasonDetails.getSeasonDetails().get(4), new RatePlanDetailsMatcher(expectedRateUnqualifiedDetailsDTO.getSeasonDetails().get(0)));
            assertThat(firstSeasonDetails.getSeasonDetails().get(2), new RatePlanDetailsMatcher(expectedRateUnqualifiedDetailsDTO.getSeasonDetails().get(1)));

            expectedRateUnqualifiedDetailsDTO = addSeasonsDetails(Arrays.asList(6), DateUtil.addDaysToDate(new Date(), 20), DateUtil.addDaysToDate(new Date(), 25));
            assertThat(secondSeasonDetails.getSeasonDetails().get(0), new RatePlanDetailsMatcher(expectedRateUnqualifiedDetailsDTO.getSeasonDetails().get(0)));
        }

        verify(syncEventAggregatorService).isSystemComponentDirty(SystemComponent.BAR_RATE_CONFIGURATION_CHANGED);
        verify(syncEventAggregatorService).registerSyncEvent(SyncEvent.BAR_RATE_CONFIGURATION_CHANGED);
    }

    @Test
    public void endDateCannotBeBeforeDetailsEndDate() {
        //lets add BAR 1 Rate Plan first
        RateHeader ratePlan1 = ratePlanEnteredFromUI("BAR 1", "Desc 1", new DateParameter(DateUtil.addDaysToDate(new Date(), 5)), new DateParameter(DateUtil.addDaysToDate(new Date(), 30)), ActionKeyEnum.CREATE);
        List<RateHeader> ratePlanList = new ArrayList<RateHeader>();
        ratePlanList.add(ratePlan1);
        rateDetailsFromUI(ratePlanList);
        DateService mockDateService = mockDateService(2);
        when(configParamsService.getParameterValue(IPConfigParamName.FORECASTING_FORECAST_WINDOW_BDE.value())).thenReturn("365");
        when(configParamsService.getParameterValue(IPConfigParamName.BAR_MAX_LOS.value())).thenReturn("8");

        when(syncEventAggregatorService.isSystemComponentDirty(SystemComponent.BAR_RATE_CONFIGURATION_CHANGED)).thenReturn(false);

        rateUnqualifiedComponent.saveUnqualifiedRates(ratePlanList);
        verify(mockDateService, times(1)).getCaughtUpDate();

        RateHeader addedRatePlan = getRateUnqualifiedByName("BAR 1");

        //now add details for added rate plan - a case of adding details for existing rate plan
        List<RateHeader> ratesFromUI = new ArrayList<RateHeader>();
        addedRatePlan.setRateUnqualifiedEndDate(new DateParameter(DateUtil.addDaysToDate(new Date(), 24)));
        ratesFromUI.add(addedRatePlan);

        TetrisException tetrisException = assertThrows(TetrisException.class, () -> rateUnqualifiedComponent.saveUnqualifiedRates(ratesFromUI));
        assertEquals("Start date can not be after details Start date or endDate cannot be before details endDate", tetrisException.getBaseMessage());

        verify(configParamsService).getParameterValue(IPConfigParamName.FORECASTING_FORECAST_WINDOW_BDE.value());
        verify(configParamsService, times(2)).getParameterValue(IPConfigParamName.BAR_MAX_LOS.value());

        verify(syncEventAggregatorService).isSystemComponentDirty(SystemComponent.BAR_RATE_CONFIGURATION_CHANGED);
        verify(syncEventAggregatorService).registerSyncEvent(SyncEvent.BAR_RATE_CONFIGURATION_CHANGED);
    }

    @Test
    public void startDateCannotBeAfterDetailsStartDate() {
        //lets add BAR 1 Rate Plan first
        RateHeader ratePlan1 = ratePlanEnteredFromUI("BAR 1", "Desc 1", new DateParameter(DateUtil.addDaysToDate(new Date(), 1)), new DateParameter(DateUtil.addDaysToDate(new Date(), 30)), ActionKeyEnum.CREATE);
        List<RateHeader> ratePlanList = new ArrayList<RateHeader>();
        ratePlanList.add(ratePlan1);
        rateDetailsFromUI(ratePlanList);
        DateService mockDateService = mockDateService(2);
        when(configParamsService.getParameterValue(IPConfigParamName.BAR_MAX_LOS.value())).thenReturn("8");
        when(configParamsService.getParameterValue(IPConfigParamName.FORECASTING_FORECAST_WINDOW_BDE.value())).thenReturn("365");
        when(syncEventAggregatorService.isSystemComponentDirty(SystemComponent.BAR_RATE_CONFIGURATION_CHANGED)).thenReturn(false);

        rateUnqualifiedComponent.saveUnqualifiedRates(ratePlanList);

        RateHeader addedRatePlan = getRateUnqualifiedByName("BAR 1");

        //now add details for added rate plan - a case of adding details for existing rate plan
        List<RateHeader> ratesFromUI = new ArrayList<RateHeader>();
        addedRatePlan.setRateUnqualifiedStartDate(new DateParameter(DateUtil.addDaysToDate(new Date(), 6)));
        ratesFromUI.add(addedRatePlan);

        final TetrisException tetrisException = assertThrows(TetrisException.class, () -> rateUnqualifiedComponent.saveUnqualifiedRates(ratesFromUI));
        assertEquals("Start date can not be after details Start date or endDate cannot be before details endDate", tetrisException.getBaseMessage());

        verify(configParamsService, times(2)).getParameterValue(IPConfigParamName.BAR_MAX_LOS.value());
        verify(configParamsService).getParameterValue(IPConfigParamName.FORECASTING_FORECAST_WINDOW_BDE.value());
        verify(syncEventAggregatorService).isSystemComponentDirty(SystemComponent.BAR_RATE_CONFIGURATION_CHANGED);
        verify(syncEventAggregatorService).registerSyncEvent(SyncEvent.BAR_RATE_CONFIGURATION_CHANGED);

        verify(mockDateService, times(1)).getCaughtUpDate();


    }

    @Test
    public void ratePlanStartDateEndDateEqualsToDetailsDates() {
        //lets add BAR 1 Rate Plan first
        RateHeader ratePlan1 = ratePlanEnteredFromUI("BAR 1", "Desc 1", new DateParameter(DateUtil.addDaysToDate(new Date(), 1)), new DateParameter(DateUtil.addDaysToDate(new Date(), 30)), ActionKeyEnum.CREATE);

        List<RateHeader> ratePlanList = new ArrayList<RateHeader>();
        ratePlanList.add(ratePlan1);
        rateDetailsFromUI(ratePlanList);

        DateService mockDateService = mockDateService(2);

        when(configParamsService.getParameterValue(IPConfigParamName.BAR_MAX_LOS.value())).thenReturn("8");
        when(configParamsService.getParameterValue(IPConfigParamName.FORECASTING_FORECAST_WINDOW_BDE.value())).thenReturn("365");
        when(syncEventAggregatorService.isSystemComponentDirty(SystemComponent.BAR_RATE_CONFIGURATION_CHANGED)).thenReturn(false);

        rateUnqualifiedComponent.saveUnqualifiedRates(ratePlanList);

        tenantCrudService().getEntityManager().flush();
        tenantCrudService().getEntityManager().clear();

        RateHeader addedRatePlan = getRateUnqualifiedByName("BAR 1");

        //now add details for added rate plan - a case of adding details for existing rate plan
        List<RateHeader> ratesFromUI = new ArrayList<RateHeader>();
        addedRatePlan.setRateUnqualifiedStartDate(new DateParameter(DateUtil.addDaysToDate(new Date(), 5)));
        addedRatePlan.setRateUnqualifiedEndDate(new DateParameter(DateUtil.addDaysToDate(new Date(), 25)));
        ratesFromUI.add(addedRatePlan);

        rateUnqualifiedComponent.saveUnqualifiedRates(ratesFromUI);

        verify(mockDateService, times(1)).getCaughtUpDate();
        verify(configParamsService, times(2)).getParameterValue(IPConfigParamName.BAR_MAX_LOS.value());
        verify(configParamsService).getParameterValue(IPConfigParamName.FORECASTING_FORECAST_WINDOW_BDE.value());
        verify(syncEventAggregatorService).isSystemComponentDirty(SystemComponent.BAR_RATE_CONFIGURATION_CHANGED);
        verify(syncEventAggregatorService).registerSyncEvent(SyncEvent.BAR_RATE_CONFIGURATION_CHANGED);

    }

    private RateHeader getRateUnqualifiedByName(String name) {
        RateUnqualified rateUnqualified;
        rateUnqualified = (RateUnqualified) tenantCrudService().getEntityManager().createQuery("SELECT r FROM RateUnqualified r WHERE r.name=:name").setParameter("name", name).getSingleResult();

        return convertRateUnqualifiedEntityToDTO(rateUnqualified);
    }

    private void rateDetailsFromUI(List<RateHeader> ratesFromUI) {
        for (RateHeader rateHeader : ratesFromUI) {
            List<Season> season = new ArrayList<Season>();
            season.add(addSeasonsDetails(Arrays.asList(5, 7), DateUtil.addDaysToDate(new Date(), 5), DateUtil.addDaysToDate(new Date(), 15)));
            season.add(addSeasonsDetails(Arrays.asList(6), DateUtil.addDaysToDate(new Date(), 20), DateUtil.addDaysToDate(new Date(), 25)));
            rateHeader.setSeasons(season);
        }
    }

    private Season addSeasonsDetails(List<Integer> accomTypeIds, Date seasonStartDate, Date seasonEndDate) {
        Season season = createSeason(seasonStartDate, seasonEndDate);

        List<SeasonDetail> roomTypeList = new ArrayList<SeasonDetail>();
        for (Integer accomTypeId : accomTypeIds) {
            SeasonDetail roomType = new SeasonDetail(new RateUnqualifiedDetails());
            addSeasonDetail(accomTypeId, roomType);
            roomTypeList.add(roomType);
        }

        season.setSeasonDetails(roomTypeList);

        return season;
    }

    private Season createSeason(Date startDate, Date endDate) {
        Season season = new Season();
        season.setDetailsStartDate(new DateParameter(startDate));
        season.setDetailsEndDate(new DateParameter(endDate));
        return season;
    }

    private void addSeasonDetail(int accomTypeId, SeasonDetail seasonDetail) {
        seasonDetail.setAccomTypeId(accomTypeId);
        seasonDetail.setSunday(applyDecimalFormatToBigDecimal(new BigDecimal(144.01)));
        seasonDetail.setMonday(applyDecimalFormatToBigDecimal(new BigDecimal(144.02)));
        seasonDetail.setTuesday(applyDecimalFormatToBigDecimal(new BigDecimal(144.03)));
        seasonDetail.setWednesday(applyDecimalFormatToBigDecimal(new BigDecimal(144.04)));
        seasonDetail.setThursday(applyDecimalFormatToBigDecimal(new BigDecimal(144.05)));
        seasonDetail.setFriday(applyDecimalFormatToBigDecimal(new BigDecimal(144.06)));
        seasonDetail.setSaturday(applyDecimalFormatToBigDecimal(new BigDecimal(144.07)));
        seasonDetail.setActionKey(ActionKeyEnum.CREATE);
    }

    private BigDecimal applyDecimalFormatToBigDecimal(BigDecimal bd) {
        DecimalFormat df = new DecimalFormat(".###");
        String str = df.format(bd);
        return new BigDecimal(str);
    }

    @Test
    public void shouldFetchRateUnqualifiedDetailsWithRatePlan() {
        int expectedSeasonSize = 5;
        List<RateHeader> rateHeaders = rateUnqualifiedComponent.fetchUnqualifiedRates();
        assertFalse(rateHeaders.isEmpty());
        assertFalse(rateHeaders.get(1).getSeasons().isEmpty());
        assertEquals(expectedSeasonSize, rateHeaders.get(1).getSeasons().size());
        assertEquals(ActionKeyEnum.UNCHANGED, rateHeaders.get(0).getActionKey());
        assertEquals(ActionKeyEnum.UNCHANGED, rateHeaders.get(0).getSeasons().get(0).getSeasonDetails().get(0).getActionKey());
        assertNotNull(rateHeaders.get(0).getSeasons().get(0).getSeasonDetails().get(3).getId());
        assertTrue(rateHeaders.get(0).getSeasons().get(0).getSeasonDetails().get(1).getMonday().compareTo(new BigDecimal(-1)) == 0);
    }

    @Test
    public void shouldFetchSeasonDetailsWithEmptyAccomTypes() {
        UniqueRateUnqualifiedDetails.createRateUnqualifiedDetails();

        List<RateHeader> fetchedRatePlanWithSeasons = rateUnqualifiedComponent.fetchUnqualifiedRates();
        RateHeader addedRateHeader = null;
        for (RateHeader rateHeader2 : fetchedRatePlanWithSeasons) {
            if (rateHeader2.getName().contains("Test name")) {
                addedRateHeader = rateHeader2;
                break;
            }
        }

        SeasonDetail nonAddedSeasonDetail = null;
        assertNotNull(addedRateHeader);
        for (SeasonDetail seasonDetail : addedRateHeader.getSeasons().get(0).getSeasonDetails()) {
            if (seasonDetail.getAccomTypeId() == 5) {
                nonAddedSeasonDetail = seasonDetail;
                break;
            }
        }

        assertNotNull(nonAddedSeasonDetail);
        assertTrue(nonAddedSeasonDetail.getMonday().compareTo(new BigDecimal(-1)) == 0);
        assertTrue(nonAddedSeasonDetail.getTuesday().compareTo(new BigDecimal(-1)) == 0);
        assertTrue(nonAddedSeasonDetail.getWednesday().compareTo(new BigDecimal(-1)) == 0);
        assertTrue(nonAddedSeasonDetail.getThursday().compareTo(new BigDecimal(-1)) == 0);
        assertTrue(nonAddedSeasonDetail.getFriday().compareTo(new BigDecimal(-1)) == 0);
        assertTrue(nonAddedSeasonDetail.getSaturday().compareTo(new BigDecimal(-1)) == 0);
        assertTrue(nonAddedSeasonDetail.getSunday().compareTo(new BigDecimal(-1)) == 0);
    }

    @Test
    public void shouldUseCrudServiceToFetchRatePlans() {
        CrudService mockCrudService = mockCrudService();
        HashMap<String, Object> parameters = new HashMap<String, Object>();
        parameters.put("propertyId", TestProperty.H1.getId());
        when(
                mockCrudService.findByNamedQuery(
                        RateUnqualified.ALL_ORDER_BY_NAME, parameters))
                .thenReturn(EMPTY_LIST);

        List<RateHeader> unqualifiedRates = rateUnqualifiedComponent
                .fetchUnqualifiedRates();
        assertEquals(unqualifiedRates, EMPTY_LIST);
    }

    private CrudService mockCrudService() {
        CrudService mockCrudService = mock(CrudService.class);
        rateUnqualifiedComponent.setCrudService(mockCrudService);
        return mockCrudService;
    }

    private List<RateHeader> ratesFromUI() {
        RateHeader ratePlan1 = ratePlanEnteredFromUI("BAR 1", "Desc 1",
                new DateParameter(new Date()), new DateParameter(new Date()), ActionKeyEnum.CREATE);
        RateHeader ratePlan2 = ratePlanEnteredFromUI("BAR 2", "Desc 1",
                new DateParameter(new Date()), new DateParameter(new Date()), ActionKeyEnum.CREATE);
        RateHeader ratePlan3 = ratePlanEnteredFromUI("BAR 3", "Desc 1",
                new DateParameter(new Date()), new DateParameter(new Date()), ActionKeyEnum.CREATE);
        return asList(ratePlan1, ratePlan2, ratePlan3);
    }

    private List<RateHeader> expectedRatePlansFromDB() {
        RateHeader ratePlan1 = ratePlanEnteredFromUI("BAR 1", "Desc 1",
                new DateParameter(new Date()), new DateParameter(new Date()), ActionKeyEnum.UNCHANGED);
        RateHeader ratePlan2 = ratePlanEnteredFromUI("BAR 2", "Desc 1",
                new DateParameter(new Date()), new DateParameter(new Date()), ActionKeyEnum.UNCHANGED);
        RateHeader ratePlan3 = ratePlanEnteredFromUI("BAR 3", "Desc 1",
                new DateParameter(new Date()), new DateParameter(new Date()), ActionKeyEnum.UNCHANGED);
        ratePlanWithDefaults(ratePlan1);
        ratePlanWithDefaults(ratePlan2);
        ratePlanWithDefaults(ratePlan3);
        return asList(ratePlan1, ratePlan2, ratePlan3);
    }

    private RateHeader ratePlanEnteredFromUI(String ratePlanName,
                                             String description, DateParameter startDate, DateParameter endDate, ActionKeyEnum actionKey) {
        RateHeader rateHeader = new RateHeader();
        rateHeader.setName(ratePlanName);
        rateHeader.setDescription(description);
        rateHeader.setRateUnqualifiedStartDate(startDate);
        rateHeader.setRateUnqualifiedEndDate(endDate);

        rateHeader.setPropertyId(PacmanWorkContextHelper.getPropertyId());
        rateHeader.setActionKey(actionKey);
        return rateHeader;
    }

    private RateHeader expectedRatePlan(String ratePlanName,
                                        String description, DateParameter startDate, DateParameter endDate, ActionKeyEnum actionKey) {
        RateHeader rateUnqualified = ratePlanEnteredFromUI(ratePlanName,
                description, startDate, endDate, actionKey);
        ratePlanWithDefaults(rateUnqualified);
        return rateUnqualified;
    }

    private void ratePlanWithDefaults(RateHeader rateHeader) {
        rateHeader.setCurrency("");
        rateHeader.setDerivedRateCode("None");
        rateHeader.setFileMetadataId(createFileMetadata().getId());
        rateHeader.setIncludesPackage(0);
        rateHeader.setPriceRelative(0);
        rateHeader.setStatusId(1);
        rateHeader.setSystemDefault(0);
        rateHeader.setYieldable(1);
    }

    private RateUnqualified fetchRatePlan(String ratePlanName) {
        Integer id = tenantCrudService()
                .findByNamedQuerySingleResult(RateUnqualified.GET_ACTIVE_RATE_UNQUALIFIED_ID_BY_NAME, QueryParameter.with("name", ratePlanName).parameters());
        assertTrue(id > 0);
        return tenantCrudService().find(RateUnqualified.class, id);
    }

    @Test
    public void shouldCheckForUniqueRatePlanName() {
        RateHeader ratePlan1 = ratePlanEnteredFromUI("BAR 1", "Desc 1", new DateParameter(new Date()), new DateParameter(new Date()), ActionKeyEnum.CREATE);
        rateUnqualifiedComponent.saveUnqualifiedRate(ratePlan1, createFileMetadata().getId());
        tenantCrudService().getEntityManager().flush();
        RateHeader ratePlan2 = ratePlanEnteredFromUI("BAR 1", "Desc 1", new DateParameter(new Date()), new DateParameter(new Date()), ActionKeyEnum.CREATE);
        assertThrows(TetrisException.class, () -> rateUnqualifiedComponent.saveUnqualifiedRate(ratePlan2, createFileMetadata().getId()));
    }


    private RateHeader convertRateUnqualifiedEntityToDTO(RateUnqualified rateUnqualified) {
        RateHeader rateHeader = new RateHeader();
        rateHeader.setId(rateUnqualified.getId());
        rateHeader.setDescription(rateUnqualified.getDescription());
        rateHeader.setName(rateUnqualified.getName());
        rateHeader.setRateUnqualifiedStartDate(rateUnqualified.getRateUnqualifiedStartDate());
        rateHeader.setRateUnqualifiedEndDate(rateUnqualified.getRateUnqualifiedEndDate());

        rateHeader.setSystemDefault(rateUnqualified.getSystemDefault());
        rateHeader.setPropertyId(PacmanWorkContextHelper.getPropertyId());

        rateHeader.setCurrency("");
        rateHeader.setDerivedRateCode("None");
        rateHeader.setFileMetadataId(rateUnqualified.getFileMetadataId());
        rateHeader.setIncludesPackage(0);
        rateHeader.setPriceRelative(0);
        rateHeader.setStatusId(1);
        rateHeader.setSystemDefault(0);
        rateHeader.setYieldable(1);
        rateHeader.setActionKey(ActionKeyEnum.UNCHANGED);
        return rateHeader;
    }

    @Test
    public void shouldCreateNewFileMetaDataEntry() {
        assertEquals(16, rateUnqualifiedComponent.createFileMetadata().getRecordTypeId());
    }

    private FileMetadata createFileMetadata() {
        return rateUnqualifiedComponent.createFileMetadata();
    }

    @Test
    public void shouldFetchAllActiveAccomTypes() throws Exception {
        CrudService mockCrudService = mockCrudService();
        when(mockCrudService.<AccomType>findByNamedQuery(AccomType.ALL_ACTIVE, QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters())).thenReturn(new ArrayList<AccomType>());

        rateUnqualifiedComponent.fetchActiveAccomTypes();
    }

    private Integer getRateUnqualifiedIdByName(String name) {
        try {
            return (Integer) tenantCrudService().getEntityManager().createQuery("SELECT ru.id FROM RateUnqualified ru WHERE ru.name=:name").setParameter("name", name).getSingleResult();
        } catch (NoResultException exe) {
            fail();
            return null;
        }
    }

    @SuppressWarnings("unchecked")
    @Test
    public void checkOrderOfFetchedSeason() {
        Integer rateUnqualifiedId = getRateUnqualifiedIdByName("LV0");
        List<RateUnqualifiedDetails> rateUnqualifiedDetails = tenantCrudService().findByNamedQuery(RateUnqualifiedDetails.BY_RATE_UNQUALIFIED_ID, QueryParameter.with("rateUnqualifiedId", rateUnqualifiedId).parameters());

        List<RateUnqualifiedDetails> rateUnqualifiedDetailsWithouOrder = tenantCrudService().getEntityManager().createQuery("select rud from RateUnqualifiedDetails as rud,AccomType as at where rud.accomTypeId = at.id and at.statusId = 1 and rud.rateUnqualifiedId =:rateUnqualifiedId")
                .setParameter("rateUnqualifiedId", rateUnqualifiedId).getResultList();

        final Comparator<RateUnqualifiedDetails> ORDER = new Comparator<RateUnqualifiedDetails>() {
            public int compare(RateUnqualifiedDetails r1, RateUnqualifiedDetails r2) {
                int dateCmp = r1.getStartDate().compareTo(r2.getStartDate());
                if (dateCmp != 0) {
                    return dateCmp;
                }

                return r1.getEndDate().compareTo(r2.getEndDate());
            }
        };

        Collections.sort(rateUnqualifiedDetailsWithouOrder, ORDER);

        assertEquals(rateUnqualifiedDetails.size(), rateUnqualifiedDetailsWithouOrder.size());

        for (int i = 0; i < rateUnqualifiedDetailsWithouOrder.size(); i++) {
            assertTrue(rateUnqualifiedDetails.get(i).getStartDate().compareTo(rateUnqualifiedDetailsWithouOrder.get(i).getStartDate()) == 0);
            assertTrue(rateUnqualifiedDetails.get(i).getEndDate().compareTo(rateUnqualifiedDetailsWithouOrder.get(i).getEndDate()) == 0);
        }

    }

    @Test
    public void shouldUpdateSeasonDetails() {
        RateUnqualified rateUnqualified = UniqueRateUnqualified.createRateUnqualifiedByDate(new Date(), DateUtil.addDaysToDate(new Date(), 60));
        AccomType accomType = UniqueAccomTypeCreator.createUniqueAccomType();
        RateUnqualifiedDetails rateUnqualifiedDetail =
                UniqueRateUnqualifiedDetails.createRateUnqualifiedDetailsForRateUnqualified(accomType.getId(),
                        rateUnqualified.getId(), DateUtil.addDaysToDate(new Date(), 1), DateUtil.addDaysToDate(new Date(), 10), 1f);

        RateHeader rateHeader = convertRateUnqualifiedEntityToDTO(rateUnqualified);
        rateUnqualifiedDetail.setFriday(new BigDecimal(300));

        Season season = createSeason(rateUnqualifiedDetail.getStartDate(), rateUnqualifiedDetail.getEndDate());

        SeasonDetail seasonDetail = convertRateUnqualifiedDetailsToSeasonDetailDTO(rateUnqualifiedDetail);
        List<SeasonDetail> seasonDetails = new ArrayList<SeasonDetail>();
        seasonDetails.add(seasonDetail);
        season.setSeasonDetails(seasonDetails);

        List<Season> seasons = new ArrayList<Season>();
        seasons.add(season);
        rateHeader.setSeasons(seasons);

        DateService mockDateService = mock(DateService.class);
        when(mockDateService.getCaughtUpDate()).thenReturn(DateUtil.addDaysToDate(DateUtil.getCurrentDate(), 2));

        rateUnqualifiedComponent.dateService = mockDateService;

        rateUnqualifiedComponent.saveOrDeleteSeasonDetails(rateHeader);
        verify(mockDateService, times(1)).getCaughtUpDate();

        RateUnqualifiedDetails rateUnqualifiedDetailFromDb = tenantCrudService().find(RateUnqualifiedDetails.class, rateUnqualifiedDetail.getId());
        assertNotNull(rateUnqualifiedDetailFromDb);
        assertTrue(300f == rateUnqualifiedDetailFromDb.getFriday().floatValue());
        assertTrue(rateUnqualifiedDetail.getMonday().floatValue() == rateUnqualifiedDetailFromDb.getMonday().floatValue());
    }

    private SeasonDetail convertRateUnqualifiedDetailsToSeasonDetailDTO(
            RateUnqualifiedDetails rateUnqualifiedDetail) {
        SeasonDetail seasonDetail = new SeasonDetail(rateUnqualifiedDetail);
        seasonDetail.setId(rateUnqualifiedDetail.getId());
        seasonDetail.setAccomTypeId(rateUnqualifiedDetail.getAccomTypeId());
        seasonDetail.setMonday(rateUnqualifiedDetail.getMonday());
        seasonDetail.setTuesday(rateUnqualifiedDetail.getTuesday());
        seasonDetail.setWednesday(rateUnqualifiedDetail.getWednesday());
        seasonDetail.setThursday(rateUnqualifiedDetail.getThursday());
        seasonDetail.setFriday(rateUnqualifiedDetail.getFriday());
        seasonDetail.setSaturday(rateUnqualifiedDetail.getSaturday());
        seasonDetail.setSunday(rateUnqualifiedDetail.getSunday());
        return seasonDetail;
    }

    @SuppressWarnings("unchecked")
    @Test
    public void shouldUpdateAndAddSeasonDetails() {
        BigDecimal updatedDowValue = new BigDecimal(300);
        Date now = new Date();
        RateUnqualified rateUnqualified = UniqueRateUnqualified.createRateUnqualifiedByDate(now, DateUtil.addDaysToDate(now, 60));
        AccomType accomType = UniqueAccomTypeCreator.createUniqueAccomType();
        RateUnqualifiedDetails rateUnqualifiedDetailForUpdate =
                UniqueRateUnqualifiedDetails.createRateUnqualifiedDetailsForRateUnqualified(accomType.getId(),
                        rateUnqualified.getId(), DateUtil.addDaysToDate(now, 1), DateUtil.addDaysToDate(now, 10), 1f);
        rateUnqualifiedDetailForUpdate.setFriday(updatedDowValue);
        RateUnqualifiedDetails rateUnqualifiedDetailForAdd = createRateUnqualifiedDetailEntity(rateUnqualified.getId(), DateUtil.addDaysToDate(now, 1), DateUtil.addDaysToDate(now, 10));

        RateHeader rateHeader = convertRateUnqualifiedEntityToDTO(rateUnqualified);
        Season season = createSeason(rateUnqualifiedDetailForUpdate.getStartDate(), rateUnqualifiedDetailForUpdate.getEndDate());

        List<SeasonDetail> seasonDetails = new ArrayList<SeasonDetail>();
        seasonDetails.add(convertRateUnqualifiedDetailsToSeasonDetailDTO(rateUnqualifiedDetailForUpdate));
        seasonDetails.add(convertRateUnqualifiedDetailsToSeasonDetailDTO(rateUnqualifiedDetailForAdd));
        season.setSeasonDetails(seasonDetails);

        List<Season> seasons = new ArrayList<Season>();
        seasons.add(season);
        rateHeader.setSeasons(seasons);

        DateService mockDateService = mock(DateService.class);
        when(mockDateService.getCaughtUpDate()).thenReturn(DateUtil.addDaysToDate(DateUtil.getCurrentDate(), 2));

        rateUnqualifiedComponent.dateService = mockDateService;
        when(configParamsService.getParameterValue(IPConfigParamName.FORECASTING_FORECAST_WINDOW_BDE.value())).thenReturn("365");

        rateUnqualifiedComponent.saveOrDeleteSeasonDetails(rateHeader);
        verify(mockDateService, times(1)).getCaughtUpDate();
        verify(configParamsService, times(1)).getParameterValue(IPConfigParamName.FORECASTING_FORECAST_WINDOW_BDE.value());

        List<RateUnqualifiedDetails> fetchedRateUnqualifiedDetails = tenantCrudService().findByNamedQuery(RateUnqualifiedDetails.BY_RATE_UNQUALIFIED_ID, QueryParameter.with("rateUnqualifiedId", rateUnqualified.getId()).parameters());

        assertNotNull(fetchedRateUnqualifiedDetails);
        assertEquals(2, fetchedRateUnqualifiedDetails.size());
        assertNotNull(fetchedRateUnqualifiedDetails.get(0).getId());
        assertNotNull(fetchedRateUnqualifiedDetails.get(1).getId());
    }

    @Test
    public void shouldDeleteSeasonDetails() {
        RateUnqualifiedDetails rateUnqualifiedDetail = UniqueRateUnqualifiedDetails.createRateUnqualifiedDetails();
        assertNotNull(rateUnqualifiedDetail);
        rateUnqualifiedComponent.deleteSeasonDetails(rateUnqualifiedDetail);
        RateUnqualifiedDetails rateUnqualifiedDetailDeleted = tenantCrudService().find(RateUnqualifiedDetails.class, rateUnqualifiedDetail.getId());
        assertNull(rateUnqualifiedDetailDeleted);
    }

    private RateUnqualifiedDetails createRateUnqualifiedDetailEntity(
            Integer rateUnqualifiedId, Date startDate, Date endDate) {
        RateUnqualifiedDetails rateUnqualifiedDetailForAdd = new RateUnqualifiedDetails();
        rateUnqualifiedDetailForAdd.setAccomTypeId(5);
        rateUnqualifiedDetailForAdd.setMonday(new BigDecimal(300));
        rateUnqualifiedDetailForAdd.setTuesday(new BigDecimal(300));
        rateUnqualifiedDetailForAdd.setWednesday(new BigDecimal(300));
        rateUnqualifiedDetailForAdd.setThursday(new BigDecimal(300));
        rateUnqualifiedDetailForAdd.setFriday(new BigDecimal(300));
        rateUnqualifiedDetailForAdd.setSaturday(new BigDecimal(300));
        rateUnqualifiedDetailForAdd.setSunday(new BigDecimal(300));
        rateUnqualifiedDetailForAdd.setStartDate(startDate);
        rateUnqualifiedDetailForAdd.setEndDate(endDate);
        rateUnqualifiedDetailForAdd.setRateUnqualifiedId(rateUnqualifiedId);
        return rateUnqualifiedDetailForAdd;
    }

    public void detailEndDateLessThanSystemDateThrowsException() {
        RateUnqualifiedDetails rateUnqualifiedDetails = createRateUnqualifiedDetailEntity(1, DateUtil.getCurrentDate(), DateUtil.addDaysToDate(DateUtil.getCurrentDate(), 5));
        DateService mockDateService = mockDateService(7);
        rateUnqualifiedComponent.dateService = mockDateService;
        assertThrows(TetrisException.class, () -> rateUnqualifiedComponent.validateSeasonDetail(rateUnqualifiedDetails, rateUnqualifiedComponent.dateService.getCaughtUpDate()));
        verify(mockDateService).getCaughtUpDate();
    }

    @Test
    public void detailEndDateShouldBeEqualToSystemDate() {
        RateUnqualifiedDetails rateUnqualifiedDetails = createRateUnqualifiedDetailEntity(1, DateUtil.getCurrentDate(), DateUtil.addDaysToDate(DateUtil.getCurrentDate(), 5));
        DateService mockDateService = mockDateService(5);
        rateUnqualifiedComponent.validateSeasonDetail(rateUnqualifiedDetails, rateUnqualifiedComponent.dateService.getCaughtUpDate());
        verify(mockDateService).getCaughtUpDate();
    }

    private DateService mockDateService(int addDays) {
        DateService mockDateService = mock(DateService.class);
        when(mockDateService.getCaughtUpDate()).thenReturn(DateUtil.addDaysToDate(DateUtil.getCurrentDate(), addDays));

        rateUnqualifiedComponent.dateService = mockDateService;
        return mockDateService;
    }

    @Test
    public void detailEndDateShouldBeGreaterToSystemDate() {
        RateUnqualifiedDetails rateUnqualifiedDetails = createRateUnqualifiedDetailEntity(1, DateUtil.getCurrentDate(), DateUtil.addDaysToDate(DateUtil.getCurrentDate(), 5));
        DateService mockDateService = mockDateService(2);
        rateUnqualifiedComponent.dateService = mockDateService;
        rateUnqualifiedComponent.validateSeasonDetail(rateUnqualifiedDetails, rateUnqualifiedComponent.dateService.getCaughtUpDate());
        verify(mockDateService).getCaughtUpDate();
    }

    private AccomType getAccomTypeByName(String name) {
        return (AccomType) tenantCrudService().getEntityManager().createQuery("SELECT a FROM AccomType as a WHERE a.name =:name").setParameter("name", name).getSingleResult();
    }

    @Test
    public void shouldNotFetchInActiveSeasonDetailsWithoutValue() {
        String inactiveAccomTypeName = "SUITE";
        AccomType inactiveAccomType = getAccomTypeByName(inactiveAccomTypeName);
        inactiveAccomType.setStatusId(Constants.INACTIVE_STATUS_ID);
        tenantCrudService().save(inactiveAccomType);
        AccomType activeAccomType = getAccomTypeByName("DELUXE");
        RateUnqualifiedDetails rateUnqualifiedDetailForUpdate =
                UniqueRateUnqualifiedDetails.createFutureRateUnQualifiedDetailsForAccomTypeID(activeAccomType.getId(), DateUtil.getCurrentDate());
        Integer rateUnqualifiedId = rateUnqualifiedDetailForUpdate.getRateUnqualifiedId();
        List<Season> seasons = rateUnqualifiedComponent.fetchSeasonsWithAccomTypes(rateUnqualifiedId);
        for (SeasonDetail seasonDetail : seasons.get(0).getSeasonDetails()) {
            assertFalse(seasonDetail.getAccomTypeName().equals(inactiveAccomTypeName));
        }
    }

    @Test
    public void shouldFetchDisplayInactiveSeasonDetailsWithValue() {
        String activeAccomTypeName = "SUITE";
        AccomType activeAccomType = getAccomTypeByName(activeAccomTypeName);
        activeAccomType.setDisplayStatusId(2);
        tenantCrudService().save(activeAccomType);
        RateUnqualifiedDetails rateUnqualifiedDetailForUpdate =
                UniqueRateUnqualifiedDetails.createFutureRateUnQualifiedDetailsForAccomTypeID(activeAccomType.getId(), DateUtil.getCurrentDate());
        Integer rateUnqualifiedId = rateUnqualifiedDetailForUpdate.getRateUnqualifiedId();
        List<Season> seasons = rateUnqualifiedComponent.fetchSeasonsWithAccomTypes(rateUnqualifiedId);
        SeasonDetail suiteSeason = seasons.get(0).getSeasonDetails().get(4);
        assertTrue(suiteSeason.getAccomTypeName().equals(activeAccomTypeName));
        assertFalse(suiteSeason.isAccomTypeDisplayActive());
    }

    @Test
    public void shouldFetchInActiveSeasonDetailsWithValue() {
        String inactiveAccomTypeName = "SUITE";
        AccomType inactiveAccomType = getAccomTypeByName(inactiveAccomTypeName);
        inactiveAccomType.setStatusId(Constants.INACTIVE_STATUS_ID);
        tenantCrudService().save(inactiveAccomType);
        RateUnqualifiedDetails rateUnqualifiedDetailForUpdate =
                UniqueRateUnqualifiedDetails.createFutureRateUnQualifiedDetailsForAccomTypeID(inactiveAccomType.getId(), DateUtil.getCurrentDate());
        Integer rateUnqualifiedId = rateUnqualifiedDetailForUpdate.getRateUnqualifiedId();
        List<Season> seasons = rateUnqualifiedComponent.fetchSeasonsWithAccomTypes(rateUnqualifiedId);
        assertTrue(seasons.get(0).getSeasonDetails().get(4).getAccomTypeName().equals(inactiveAccomTypeName));
    }

    @Test
    public void shouldFetchSeasonDetailsWithAllAccomTypes() {
        Integer rateUnqualifiedId = 4;
        List<Season> seasons = rateUnqualifiedComponent.fetchSeasonsWithAccomTypes(rateUnqualifiedId);
        for (Season season : seasons) {
            for (SeasonDetail seasonDetail : season.getSeasonDetails()) {
                if (seasonDetail.getId() == null) {
                    assertNotNull(seasonDetail.getAccomTypeName());
                    assertNotNull(season.getDetailsStartDate());
                    assertNotNull(season.getDetailsEndDate());
                } else {
                    assertNotNull(seasonDetail.getAccomTypeName());
                    assertNotNull(season.getDetailsStartDate());
                    assertNotNull(season.getDetailsEndDate());
                }
            }

            boolean isRoomTypePresentWithCode = season.getSeasonDetails().stream().anyMatch(seasonDetail ->
                    seasonDetail.getAccomTypeName().equals("DELUXE") && seasonDetail.getAccomTypeCode().equals("DLX")
            );
            assertTrue(isRoomTypePresentWithCode);
        }
    }

    @Test
    public void shouldFetchSeasonsAllAccomTypesWithNewLogic() {

        RateUnqualified rateUnqualified = UniqueRateUnqualified.createRateUnqualifiedByDate(new Date(), DateUtil.addDaysToDate(new Date(), 60));
        AccomType accomType = UniqueAccomTypeCreator.createUniqueAccomType();
        RateUnqualifiedDetails rateUnqualifiedDetailForSeason1 =
                UniqueRateUnqualifiedDetails.createRateUnqualifiedDetailsForRateUnqualified(accomType.getId(),
                        rateUnqualified.getId(), DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 1), DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 10), 1f);
        RateUnqualifiedDetails rateUnqualifiedDetailForSeason2 = UniqueRateUnqualifiedDetails.createRateUnqualifiedDetailsForRateUnqualified(accomType.getId(),
                rateUnqualified.getId(), DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 11), DateUtil.addDaysToDate(DateUtil.getCurrentDateWithoutTime(), 20), 2f);

        List<Season> seasons = rateUnqualifiedComponent.fetchSeasonsWithAccomTypes(rateUnqualified.getId());

        assertTrue(seasons.size() == 2);
        assertTrue(seasons.get(0).getDetailsStartDate().getTime().compareTo(rateUnqualifiedDetailForSeason1.getStartDate()) == 0);
        assertTrue(seasons.get(0).getDetailsEndDate().getTime().compareTo(rateUnqualifiedDetailForSeason1.getEndDate()) == 0);
        assertTrue(seasons.get(1).getDetailsStartDate().getTime().compareTo(rateUnqualifiedDetailForSeason2.getStartDate()) == 0);
        assertTrue(seasons.get(1).getDetailsEndDate().getTime().compareTo(rateUnqualifiedDetailForSeason2.getEndDate()) == 0);

        for (SeasonDetail seasonDetail : seasons.get(0).getSeasonDetails()) {
            System.out.println(seasonDetail.toString());
            if (accomType.getName().equalsIgnoreCase(seasonDetail.getAccomTypeName())) {
                assertTrue(compareSeasonWithRateDetail(seasonDetail, rateUnqualifiedDetailForSeason1));
            }
        }

        for (SeasonDetail seasonDetail : seasons.get(1).getSeasonDetails()) {
            System.out.println(seasonDetail.toString());
            if (accomType.getName().equalsIgnoreCase(seasonDetail.getAccomTypeName())) {
                assertTrue(compareSeasonWithRateDetail(seasonDetail, rateUnqualifiedDetailForSeason2));
            }
        }
    }


    private boolean compareSeasonWithRateDetail(SeasonDetail seasonDetail, RateUnqualifiedDetails rateUnqualifiedDetail) {
        return seasonDetail.getAccomTypeId().equals(rateUnqualifiedDetail.getAccomTypeId())
                && seasonDetail.getMonday().compareTo(rateUnqualifiedDetail.getMonday()) == 0
                && seasonDetail.getTuesday().compareTo(rateUnqualifiedDetail.getTuesday()) == 0
                && seasonDetail.getWednesday().compareTo(rateUnqualifiedDetail.getWednesday()) == 0
                && seasonDetail.getThursday().compareTo(rateUnqualifiedDetail.getThursday()) == 0
                && seasonDetail.getFriday().compareTo(rateUnqualifiedDetail.getFriday()) == 0
                && seasonDetail.getSaturday().compareTo(rateUnqualifiedDetail.getSaturday()) == 0
                && seasonDetail.getSunday().compareTo((rateUnqualifiedDetail.getSunday())) == 0;
    }

    @Test
    public void shouldNotFetchInactiveSeasonDetailsWithoutValueWithNewLogic() {
        String inactiveAccomTypeName = "SUITE";

        AccomType inactiveAccomType = getAccomTypeByName(inactiveAccomTypeName);
        inactiveAccomType.setStatusId(Constants.INACTIVE_STATUS_ID);
        tenantCrudService().save(inactiveAccomType);

        AccomType activeAccomType = getAccomTypeByName("DELUXE");

        RateUnqualifiedDetails rateUnqualifiedDetailForUpdate =
                UniqueRateUnqualifiedDetails.createFutureRateUnQualifiedDetailsForAccomTypeID(activeAccomType.getId(), DateUtil.getCurrentDate());
        Integer rateUnqualifiedId = rateUnqualifiedDetailForUpdate.getRateUnqualifiedId();

        List<Season> seasons = rateUnqualifiedComponent.fetchSeasonsWithAccomTypes(rateUnqualifiedId);

        assertTrue(seasons.size() == 1);
        assertTrue(seasons.get(0).getSeasonDetails().size() == 4);

        for (SeasonDetail seasonDetail : seasons.get(0).getSeasonDetails()) {
            assertFalse(seasonDetail.getAccomTypeName().equals(inactiveAccomTypeName));
        }
    }

    @Test
    public void shouldFetchInactiveSeasonDetailWithValueWithNewLogic() {
        String inactiveAccomTypeName = "SUITE";

        AccomType inactiveAccomType = getAccomTypeByName(inactiveAccomTypeName);
        inactiveAccomType.setStatusId(Constants.INACTIVE_STATUS_ID);
        tenantCrudService().save(inactiveAccomType);

        RateUnqualifiedDetails rateUnqualifiedDetailForUpdate =
                UniqueRateUnqualifiedDetails.createFutureRateUnQualifiedDetailsForAccomTypeID(inactiveAccomType.getId(), DateUtil.getCurrentDate());
        Integer rateUnqualifiedId = rateUnqualifiedDetailForUpdate.getRateUnqualifiedId();

        List<Season> seasons = rateUnqualifiedComponent.fetchSeasonsWithAccomTypes(rateUnqualifiedId);

        assertTrue(seasons.size() == 1);
        assertTrue(seasons.get(0).getSeasonDetails().size() == 5);

        assertTrue(seasons.get(0).getSeasonDetails().get(4).getAccomTypeName().equals(inactiveAccomTypeName));
    }

    @Test
    public void shouldSkipUnchangedSeasons() {
        List<Season> seasons = new ArrayList<Season>();
        Season season = new Season();
        season.setDetailsStartDate(DateParameter.fromDate(DateUtil.addDaysToDate(new Date(), 1)));
        season.setDetailsEndDate(DateParameter.fromDate(DateUtil.addDaysToDate(new Date(), 15)));

        List<SeasonDetail> seasonDetails = new ArrayList<SeasonDetail>();
        SeasonDetail seasonDetail = new SeasonDetail();
        addSeasonDetail(5, seasonDetail);
        seasonDetail.setActionKey(ActionKeyEnum.CREATE);
        seasonDetails.add(seasonDetail);

        seasonDetail = new SeasonDetail();
        addSeasonDetail(6, seasonDetail);
        seasonDetail.setActionKey(ActionKeyEnum.UPDATE);
        seasonDetails.add(seasonDetail);

        seasonDetail = new SeasonDetail();
        seasonDetail.setAccomTypeId(7);
        seasonDetail.setActionKey(ActionKeyEnum.UNCHANGED);
        seasonDetails.add(seasonDetail);

        season.setSeasonDetails(seasonDetails);
        seasons.add(season);
        rateUnqualifiedComponent.skipUnchangedSeasons(seasons);

        for (SeasonDetail modifiedSeasonDetail : seasons.get(0).getSeasonDetails()) {
            assertEquals(false, modifiedSeasonDetail.getActionKey() == ActionKeyEnum.UNCHANGED);
        }
    }

    @Test
    public void shouldDeleteSeason() {
        RateUnqualified rateUnqualified = UniqueRateUnqualified.createRateUnqualifiedByDate(new Date(), DateUtil.addDaysToDate(new Date(), 60));
        AccomType accomType = UniqueAccomTypeCreator.createUniqueAccomType();
        RateUnqualifiedDetails rateUnqualifiedDetailForSeason1 =
                UniqueRateUnqualifiedDetails.createRateUnqualifiedDetailsForRateUnqualified(accomType.getId(),
                        rateUnqualified.getId(), DateUtil.addDaysToDate(new Date(), 1), DateUtil.addDaysToDate(new Date(), 10), 1f);
        RateUnqualifiedDetails rateUnqualifiedDetailForSeason2 = UniqueRateUnqualifiedDetails.createRateUnqualifiedDetailsForRateUnqualified(accomType.getId(),
                rateUnqualified.getId(), DateUtil.addDaysToDate(new Date(), 11), DateUtil.addDaysToDate(new Date(), 20), 2f);

        RateHeader rateHeader = convertRateUnqualifiedEntityToDTO(rateUnqualified);
        Season season1 = createSeason(rateUnqualifiedDetailForSeason1.getStartDate(), rateUnqualifiedDetailForSeason1.getEndDate());
        Season season2 = createSeason(rateUnqualifiedDetailForSeason2.getStartDate(), rateUnqualifiedDetailForSeason1.getEndDate());

        List<SeasonDetail> seasonDetailsForSeason1 = new ArrayList<SeasonDetail>();
        List<SeasonDetail> seasonDetailsForSeason2 = new ArrayList<SeasonDetail>();
        seasonDetailsForSeason1.add(convertRateUnqualifiedDetailsToSeasonDetailDTO(rateUnqualifiedDetailForSeason1));
        seasonDetailsForSeason2.add(convertRateUnqualifiedDetailsToSeasonDetailDTO(rateUnqualifiedDetailForSeason2));
        season1.setSeasonDetails(seasonDetailsForSeason1);
        season2.setSeasonDetails(seasonDetailsForSeason2);

        List<Season> seasons = new ArrayList<Season>();
        seasons.add(season1);
        seasons.add(season2);
        rateHeader.setSeasons(seasons);

        List<RateHeader> retrievedRatePlans = rateUnqualifiedComponent.fetchUnqualifiedRates();
        assertTrue(retrievedRatePlans.size() > 0);

        RateHeader addedRatePlan = null;
        for (RateHeader retrievedRateHeader : retrievedRatePlans) {
            if (retrievedRateHeader.getName().equals(rateUnqualified.getName())) {
                addedRatePlan = retrievedRateHeader;
                break;
            }
        }
        assertNotNull(addedRatePlan);
        assertEquals(2, addedRatePlan.getSeasons().size());

        //now delete any season from this rate plan, we have two seasons deleting first
        for (SeasonDetail seasonDetail : addedRatePlan.getSeasons().get(0).getSeasonDetails()) {
            if (seasonDetail.getId() != null) {
                seasonDetail.setActionKey(ActionKeyEnum.DELETE);
            }
        }

        List<RateHeader> forSaveRatePlanList = new ArrayList<RateHeader>();
        forSaveRatePlanList.add(addedRatePlan);
        when(configParamsService.getParameterValue(IPConfigParamName.BAR_MAX_LOS.value())).thenReturn("8");
        when(configParamsService.getParameterValue(IPConfigParamName.FORECASTING_FORECAST_WINDOW_BDE.value())).thenReturn("365");
        when(syncEventAggregatorService.isSystemComponentDirty(SystemComponent.BAR_RATE_CONFIGURATION_CHANGED)).thenReturn(false);

        rateUnqualifiedComponent.saveUnqualifiedRates(forSaveRatePlanList);

        verify(syncEventAggregatorService).registerSyncEvent(SyncEvent.BAR_RATE_CONFIGURATION_CHANGED);

        retrievedRatePlans = rateUnqualifiedComponent.fetchUnqualifiedRates();
        for (RateHeader retrievedRateHeader : retrievedRatePlans) {
            if (retrievedRateHeader.getName().equals(rateUnqualified.getName())) {
                addedRatePlan = retrievedRateHeader;
                break;
            }
        }
        assertEquals(1, addedRatePlan.getSeasons().size());
    }

    @Test
    public void shouldTurnOnSyncFlag() {
        when(syncEventAggregatorService.isSystemComponentDirty(SystemComponent.BAR_RATE_CONFIGURATION_CHANGED)).thenReturn(false);

        rateUnqualifiedComponent.syncBARRateConfigChanged();
        verify(syncEventAggregatorService).registerSyncEvent(SyncEvent.BAR_RATE_CONFIGURATION_CHANGED);
    }

    @SuppressWarnings("unchecked")
    @Test
    public void shouldRemoBarDecisionOverrideWithNewDecisionID() {
        Decision decision = createDummyDecision();
        DecisionBAROutput decisionBarOutput = createDummyDecisionBarOutput(Constants.BARDECISIONOVERRIDE_USER);
        assertTrue(decisionBarOutput.getId() > 0);
        Season season = createSeason(decisionBarOutput.getArrivalDate(), decisionBarOutput.getArrivalDate());
        season.setActionKey(ActionKeyEnum.DELETE);
        rateUnqualifiedComponent.removeBarDecisionOverrides(decisionBarOutput.getRateUnqualified().getId(), season, decision.getId());
        List<DecisionBAROutputOverride> decisionOverrideList = tenantCrudService().getEntityManager().createQuery("select dbo from DecisionBAROutputOverride dbo where propertyId = 5" +
                " and oldRateUnqualified= " + decisionBarOutput.getRateUnqualified().getId() +
                " and decision_id = " + decision.getId() + " and arrivalDate = '" +
                DateUtil.formatDate(decisionBarOutput.getArrivalDate(), "yyyy-MM-dd") + "'").getResultList();
        assertTrue(decisionOverrideList.get(0).getNewOverride().equals("Pending"));
        assertTrue(decisionOverrideList.get(0).getOldOverride().equals("User"));
        assertTrue(decisionOverrideList.get(0).getDecision().getId().equals(decision.getId()));
    }

    private Decision createDummyDecision() {
        return decisionservice.createBAROverrideDecision();
    }

    @SuppressWarnings("unchecked")
    @Test
    public void shouldCreateBarDecisionPaceWithNewDecisionID() {
        Decision decision = createDummyDecision();
        DecisionBAROutput decisionBarOutput = createDummyDecisionBarOutput(Constants.BARDECISIONOVERRIDE_USER);
        assertTrue(decisionBarOutput.getId() > 0);
        Season season = createSeason(decisionBarOutput.getArrivalDate(), decisionBarOutput.getArrivalDate());
        season.setActionKey(ActionKeyEnum.DELETE);
        rateUnqualifiedComponent.createBarDecisionPace(decisionBarOutput.getRateUnqualified().getId(), season, rateUnqualifiedComponent.getNoRatePlan().getId(), decision.getId());
        List<PaceBAROutput> paceList = tenantCrudService().getEntityManager().createQuery("select pbo from PaceBAROutput pbo where propertyID = 5" +
                " and rateUnqualified= " + rateUnqualifiedComponent.getNoRatePlan().getId() +
                " and accom_Class_ID = " + decisionBarOutput.getAccomClassId() +
                " and decision_id = " + decision.getId() + " and arrivalDate = '" +
                DateUtil.formatDate(decisionBarOutput.getArrivalDate(), "yyyy-MM-dd") + "'").getResultList();
        assertTrue(paceList.get(0).getOverride().equals("Pending"));
        assertTrue(paceList.get(0).getReasonTypeId() == DecisionReasonType.CONFIG_ISSUES.getId());
        assertTrue(paceList.get(0).getDecision().getId().equals(decision.getId()));
    }

    @SuppressWarnings("unchecked")
    @Test
    public void shouldUpdateBarDecisionsWithNewDecisionID() {
        Decision decision = createDummyDecision();
        DecisionBAROutput decisionBarOutput = createDummyDecisionBarOutput(Constants.BARDECISIONOVERRIDE_USER);
        assertTrue(decisionBarOutput.getId() > 0);
        Season season = createSeason(decisionBarOutput.getArrivalDate(), decisionBarOutput.getArrivalDate());
        season.setActionKey(ActionKeyEnum.DELETE);
        tenantCrudService().getEntityManager().detach(decisionBarOutput);
        rateUnqualifiedComponent.updateBarDecisions(decisionBarOutput.getRateUnqualified().getId(), season, rateUnqualifiedComponent.getNoRatePlan().getId(), decision.getId());
        List<DecisionBAROutput> decisionList = tenantCrudService().findByNamedQuery(DecisionBAROutput.BY_ARRIVALDATE_AND_ACCOMCLASSID, QueryParameter.with("arrivalDate", decisionBarOutput.getArrivalDate())
                .and("accomClassId", decisionBarOutput.getAccomClassId()).parameters());
        assertTrue(decisionList.get(0).getRateUnqualified().getId().equals(rateUnqualifiedComponent.getNoRatePlan().getId()));
        assertTrue(decisionList.get(0).getOverride().equals("Pending"));
        assertTrue(decisionList.get(0).getReasonTypeId() == DecisionReasonType.CONFIG_ISSUES.getId());
        assertTrue(decisionList.get(0).getDecision().getId().equals(decision.getId()));
    }

    @SuppressWarnings("unchecked")
    @Test
    public void shouldRemoBarDecisionOverride() {
        DecisionBAROutput decisionBarOutput = createDummyDecisionBarOutput(Constants.BARDECISIONOVERRIDE_USER);
        assertTrue(decisionBarOutput.getId() > 0);
        Season season = createSeason(decisionBarOutput.getArrivalDate(), decisionBarOutput.getArrivalDate());
        season.setActionKey(ActionKeyEnum.DELETE);
        rateUnqualifiedComponent.removeBarDecisionOverrides(decisionBarOutput.getRateUnqualified().getId(), season, null);
        List<DecisionBAROutputOverride> decisionOverrideList = tenantCrudService().getEntityManager().createQuery(
                "select dbo from DecisionBAROutputOverride dbo where propertyId = 5" +
                        " and oldRateUnqualified= " + decisionBarOutput.getRateUnqualified().getId() +
                        " and decision_id = " + decisionBarOutput.getDecision().getId() + " and arrivalDate = '" +
                        DateUtil.formatDate(decisionBarOutput.getArrivalDate(), "yyyy-MM-dd") + "'").getResultList();
        assertTrue(decisionOverrideList.get(0).getNewOverride().equals("Pending"));
        assertTrue(decisionOverrideList.get(0).getOldOverride().equals("User"));
        assertTrue(decisionOverrideList.get(0).getDecision().getId().equals(decisionBarOutput.getDecision().getId()));
        assertNull(decisionOverrideList.get(0).getNewFloorRateUnqualified());
        assertNull(decisionOverrideList.get(0).getOldFloorRateUnqualified());
        assertTrue(decisionOverrideList.get(0).getNewRateUnqualified().getId().equals(decisionBarOutput.getRateUnqualified().getId()));
        assertTrue(decisionOverrideList.get(0).getOldRateUnqualified().getId().equals(decisionBarOutput.getRateUnqualified().getId()));
    }


    @SuppressWarnings("unchecked")
    @Test
    public void shouldRemoBarDecisionOverrideWhereFloorOverrideExists() {
        DecisionBAROutput decisionBarOutput = createDummyDecisionBarOutput(Constants.BARDECISIONOVERRIDE_FLOOR);
        assertTrue(decisionBarOutput.getId() > 0);
        Season season = createSeason(decisionBarOutput.getArrivalDate(), decisionBarOutput.getArrivalDate());
        season.setActionKey(ActionKeyEnum.DELETE);
        rateUnqualifiedComponent.removeBarDecisionOverrides(decisionBarOutput.getRateUnqualified().getId(), season, null);
        flushAndClear();
        List<DecisionBAROutputOverride> decisionOverrideList = tenantCrudService().getEntityManager().createQuery(
                "select dbo from DecisionBAROutputOverride dbo where propertyId = 5" +
                        " and oldRateUnqualified= " + decisionBarOutput.getRateUnqualified().getId() +
                        " and decision_id = " + decisionBarOutput.getDecision().getId() + " and arrivalDate = '" +
                        DateUtil.formatDate(decisionBarOutput.getArrivalDate(), "yyyy-MM-dd") + "'").getResultList();
        assertTrue(decisionOverrideList.get(0).getNewOverride().equals("Pending"));
        assertTrue(decisionOverrideList.get(0).getOldOverride().equals("Floor"));
        assertTrue(decisionOverrideList.get(0).getDecision().getId().intValue() == decisionBarOutput.getDecision().getId().intValue());
        assertNull(decisionOverrideList.get(0).getNewFloorRateUnqualified());
        assertTrue(decisionOverrideList.get(0).getOldFloorRateUnqualified().getId().equals(decisionBarOutput.getRateUnqualified().getId()));
        assertTrue(decisionOverrideList.get(0).getNewRateUnqualified().getId().equals(decisionBarOutput.getRateUnqualified().getId()));
        assertTrue(decisionOverrideList.get(0).getOldRateUnqualified().getId().equals(decisionBarOutput.getRateUnqualified().getId()));
    }

    @SuppressWarnings("unchecked")
    @Test
    public void shouldCreateBarDecisionPace() {
        DecisionBAROutput decisionBarOutput = createDummyDecisionBarOutput(Constants.BARDECISIONOVERRIDE_USER);
        assertTrue(decisionBarOutput.getId() > 0);
        Season season = createSeason(decisionBarOutput.getArrivalDate(), decisionBarOutput.getArrivalDate());
        season.setActionKey(ActionKeyEnum.DELETE);
        rateUnqualifiedComponent.createBarDecisionPace(decisionBarOutput.getRateUnqualified().getId(), season, rateUnqualifiedComponent.getNoRatePlan().getId(), null);
        List<PaceBAROutput> paceList = tenantCrudService().getEntityManager().createQuery("select pbo from PaceBAROutput pbo where propertyID = 5" +
                " and rateUnqualified= " + rateUnqualifiedComponent.getNoRatePlan().getId() +
                " and accom_Class_ID = " + decisionBarOutput.getAccomClassId() +
                " and arrivalDate = '" +
                DateUtil.formatDate(decisionBarOutput.getArrivalDate(), "yyyy-MM-dd") + "'").getResultList();
        assertTrue((paceList.get(0)).getOverride().equals("Pending"));
        assertTrue((paceList.get(0)).getReasonTypeId() == DecisionReasonType.CONFIG_ISSUES.getId());
        assertTrue((paceList.get(0)).getDecision().getId().equals(decisionBarOutput.getDecision().getId()));
        assertNull((paceList.get(0)).getFloorRateUnqualified());
        assertNull((paceList.get(0)).getCeilingRateUnqualified());
    }

    @SuppressWarnings("unchecked")
    @Test
    public void shouldCreateBarDecisionPaceWhereFloorOverrideExists() {
        DecisionBAROutput decisionBarOutput = createDummyDecisionBarOutput(Constants.BARDECISIONOVERRIDE_FLOOR);
        assertTrue(decisionBarOutput.getId() > 0);
        Season season = createSeason(decisionBarOutput.getArrivalDate(), decisionBarOutput.getArrivalDate());
        season.setActionKey(ActionKeyEnum.DELETE);
        rateUnqualifiedComponent.createBarDecisionPace(decisionBarOutput.getRateUnqualified().getId(), season, rateUnqualifiedComponent.getNoRatePlan().getId(), null);
        List<PaceBAROutput> paceList = tenantCrudService().getEntityManager().createQuery("select pbo from PaceBAROutput pbo where propertyID = 5" +
                " and rateUnqualified= " + rateUnqualifiedComponent.getNoRatePlan().getId() +
                " and accom_Class_ID = " + decisionBarOutput.getAccomClassId() +
                " and arrivalDate = '" +
                DateUtil.formatDate(decisionBarOutput.getArrivalDate(), "yyyy-MM-dd") + "'").getResultList();
        assertTrue(paceList.get(0).getOverride().equals("Pending"));
        assertTrue(paceList.get(0).getReasonTypeId() == DecisionReasonType.CONFIG_ISSUES.getId());
        assertTrue(paceList.get(0).getDecision().getId().equals(decisionBarOutput.getDecision().getId()));
        assertTrue(paceList.get(0).getFloorRateUnqualified().getId().equals(rateUnqualifiedComponent.getNoRatePlan().getId()));
    }

    @SuppressWarnings("unchecked")
    @Test
    public void shouldUpdateBarDecisionsWhenUserOverrideExists() {
        DecisionBAROutput decisionBarOutput = createDummyDecisionBarOutput(Constants.BARDECISIONOVERRIDE_USER);
        assertTrue(decisionBarOutput.getId() > 0);
        Season season = createSeason(decisionBarOutput.getArrivalDate(), decisionBarOutput.getArrivalDate());
        season.setActionKey(ActionKeyEnum.DELETE);
        tenantCrudService().getEntityManager().detach(decisionBarOutput);
        rateUnqualifiedComponent.updateBarDecisions(decisionBarOutput.getRateUnqualified().getId(), season, rateUnqualifiedComponent.getNoRatePlan().getId(), null);
        List<DecisionBAROutput> decisionList = tenantCrudService().findByNamedQuery(DecisionBAROutput.BY_ARRIVALDATE_AND_ACCOMCLASSID, QueryParameter.with("arrivalDate", decisionBarOutput.getArrivalDate())
                .and("accomClassId", decisionBarOutput.getAccomClassId()).parameters());
        assertTrue(decisionList.get(0).getRateUnqualified().getId().equals(rateUnqualifiedComponent.getNoRatePlan().getId()));
        assertTrue(decisionList.get(0).getOverride().equals("Pending"));
        assertTrue(decisionList.get(0).getReasonTypeId() == DecisionReasonType.CONFIG_ISSUES.getId());
        assertTrue(decisionList.get(0).getDecision().getId().equals(decisionBarOutput.getDecision().getId()));
        assertNull(decisionList.get(0).getFloorRateUnqualified());
    }

    @SuppressWarnings("unchecked")
    @Test
    public void shouldUpdateBarDecisions() {
        DecisionBAROutput decisionBarOutput = createDummyDecisionBarOutput(Constants.BARDECISIONOVERRIDE_NONE);
        assertTrue(decisionBarOutput.getId() > 0);
        Season season = createSeason(decisionBarOutput.getArrivalDate(), decisionBarOutput.getArrivalDate());
        season.setActionKey(ActionKeyEnum.DELETE);
        tenantCrudService().getEntityManager().detach(decisionBarOutput);
        rateUnqualifiedComponent.updateBarDecisions(decisionBarOutput.getRateUnqualified().getId(), season, rateUnqualifiedComponent.getNoRatePlan().getId(), null);
        List<DecisionBAROutput> decisionList = tenantCrudService().findByNamedQuery(DecisionBAROutput.BY_ARRIVALDATE_AND_ACCOMCLASSID, QueryParameter.with("arrivalDate", decisionBarOutput.getArrivalDate())
                .and("accomClassId", decisionBarOutput.getAccomClassId()).parameters());
        assertTrue(decisionList.get(0).getRateUnqualified().getId().equals(rateUnqualifiedComponent.getNoRatePlan().getId()));
        assertTrue(decisionList.get(0).getOverride().equals("None"));
        assertTrue(decisionList.get(0).getReasonTypeId() == DecisionReasonType.CONFIG_ISSUES.getId());
        assertTrue(decisionList.get(0).getDecision().getId().equals(decisionBarOutput.getDecision().getId()));
        assertNull(decisionList.get(0).getFloorRateUnqualified());
    }

    @SuppressWarnings("unchecked")
    @Test
    public void shouldUpdateBarDecisionsWhereFloorOverrideExists() {
        DecisionBAROutput decisionBarOutput = createDummyDecisionBarOutput(Constants.BARDECISIONOVERRIDE_FLOOR);
        assertTrue(decisionBarOutput.getId() > 0);
        Season season = createSeason(decisionBarOutput.getArrivalDate(), decisionBarOutput.getArrivalDate());
        season.setActionKey(ActionKeyEnum.DELETE);
        tenantCrudService().getEntityManager().detach(decisionBarOutput);
        rateUnqualifiedComponent.updateBarDecisions(decisionBarOutput.getRateUnqualified().getId(), season, rateUnqualifiedComponent.getNoRatePlan().getId(), null);
        List<DecisionBAROutput> decisionList = tenantCrudService().findByNamedQuery(DecisionBAROutput.BY_ARRIVALDATE_AND_ACCOMCLASSID, QueryParameter.with("arrivalDate", decisionBarOutput.getArrivalDate())
                .and("accomClassId", decisionBarOutput.getAccomClassId()).parameters());
        assertTrue(decisionList.get(0).getRateUnqualified().getId().equals(rateUnqualifiedComponent.getNoRatePlan().getId()));
        assertTrue(decisionList.get(0).getOverride().equals("Pending"));
        assertTrue(decisionList.get(0).getReasonTypeId() == DecisionReasonType.CONFIG_ISSUES.getId());
        assertTrue(decisionList.get(0).getDecision().getId().equals(decisionBarOutput.getDecision().getId()));
        assertTrue(decisionList.get(0).getFloorRateUnqualified().getId().equals(rateUnqualifiedComponent.getNoRatePlan().getId()));
    }

    @Test
    public void shouldfetchDefaultRatePlan() {
        RateUnqualified noRatePlan = rateUnqualifiedComponent.getNoRatePlan();
        assertTrue(noRatePlan.getSystemDefault() == 1);
        assertTrue(noRatePlan.getName().equals("None"));
    }

    @SuppressWarnings("unchecked")
    @Test
    public void shouldUpdateBarDecisionsPaceAndOverrides() {
        DecisionBAROutput decisionBarOutput = createDummyDecisionBarOutput(Constants.BARDECISIONOVERRIDE_USER);
        assertTrue(decisionBarOutput.getId() > 0);
        Season season = createSeason(decisionBarOutput.getArrivalDate(), decisionBarOutput.getArrivalDate());
        season.setActionKey(ActionKeyEnum.DELETE);
        CrudService mockCrudService = mockCrudService();
        DecisionService mockDecisionService = mock(DecisionService.class);
        rateUnqualifiedComponent.decisionService = mockDecisionService;
        Decision decision = new Decision();
        decision.setId(10001);
        when(mockDecisionService.createBAROverrideDecision()).thenReturn(decision);

        when(mockCrudService.findByNamedQuerySingleResult(RateUnqualified.DEFAULT_BY_PROPERTY_ID, QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters())).thenReturn(new RateUnqualified());
        when(mockCrudService.findByNamedQuerySingleResult(DecisionBAROutput.COUNT_OF_BAR_DECISION_FOR_DATE_RANGE_FOR_GIVEN_RATE_PLAN,
                QueryParameter.with("startDate", season.getDetailsStartDate().getTime()).and("endDate", season.getDetailsEndDate().getTime())
                        .and("ratePlanID", decisionBarOutput.getRateUnqualified().getId())
                        .and("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters())).thenReturn((long) 1);
        when(mockCrudService.executeUpdateByNamedQuery(isA(String.class), isA(Map.class))).thenReturn(1);

        rateUnqualifiedComponent.updateBarDecisionsAndOverrides(season, decisionBarOutput.getRateUnqualified().getId(), rateUnqualifiedComponent.dateService.getCaughtUpDate());

        verify(mockCrudService, times(9)).executeUpdateByNamedQuery(isA(String.class), isA(Map.class));
    }

    @SuppressWarnings("unchecked")
    @Test
    public void shouldUpdateBarDecisionsAndPace() {
        DecisionBAROutput decisionBarOutput = createDummyDecisionBarOutput(Constants.BARDECISIONOVERRIDE_USER);
        assertTrue(decisionBarOutput.getId() > 0);
        Season season = createSeason(decisionBarOutput.getArrivalDate(), decisionBarOutput.getArrivalDate());
        season.setActionKey(ActionKeyEnum.DELETE);
        CrudService mockCrudService = mockCrudService();
        DecisionService mockDecisionService = mock(DecisionService.class);
        rateUnqualifiedComponent.decisionService = mockDecisionService;
        Decision decision = new Decision();
        decision.setId(10001);
        when(mockDecisionService.createBAROverrideDecision()).thenReturn(decision);
        when(mockCrudService.findByNamedQuerySingleResult(RateUnqualified.DEFAULT_BY_PROPERTY_ID, QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters())).thenReturn(new RateUnqualified());
        when(mockCrudService.findByNamedQuerySingleResult(DecisionBAROutput.COUNT_OF_BAR_DECISION_FOR_DATE_RANGE_FOR_GIVEN_RATE_PLAN,
                QueryParameter.with("startDate", season.getDetailsStartDate().getTime()).and("endDate", season.getDetailsEndDate().getTime())
                        .and("ratePlanID", decisionBarOutput.getRateUnqualified().getId())
                        .and("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters())).thenReturn((long) 1);
        when(mockCrudService.executeUpdateByNamedQuery(isA(String.class), isA(Map.class))).thenReturn(1);

        rateUnqualifiedComponent.updateBarDecisionsAndOverrides(season, decisionBarOutput.getRateUnqualified().getId(), rateUnqualifiedComponent.dateService.getCaughtUpDate());
        verify(mockCrudService, times(9)).executeUpdateByNamedQuery(isA(String.class), isA(Map.class));
    }

    @SuppressWarnings("unchecked")
    @Test
    public void shouldDeleteRateDetails() {
        DateParameter startDateParameter = new DateParameter(new Date());
        DateParameter endDateParameter = new DateParameter(new Date());
        RateHeader rateHeader = ratePlanEnteredFromUI("TestRP", "TestRP", startDateParameter, endDateParameter, ActionKeyEnum.UPDATE);
        int dummyId = 21;
        Season season = createSeason(startDateParameter.getTime(), endDateParameter.getTime());
        season.setActionKey(ActionKeyEnum.DELETE);
        SeasonDetail seasonDetail = new SeasonDetail();
        addSeasonDetail(5, seasonDetail);
        seasonDetail.setId(dummyId);
        seasonDetail.setActionKey(ActionKeyEnum.DELETE);
        List<SeasonDetail> seasonDetails = new ArrayList<SeasonDetail>();
        seasonDetails.add(seasonDetail);
        season.setSeasonDetails(seasonDetails);
        List<Season> seasons = new ArrayList<Season>();
        seasons.add(season);
        rateHeader.setSeasons(seasons);

        CrudService mockCrudService = mockCrudService();
        when(mockCrudService.delete(RateUnqualifiedDetails.class, dummyId)).thenReturn(true);

        DecisionService mockDecisionService = mock(DecisionService.class);
        rateUnqualifiedComponent.decisionService = mockDecisionService;
        Decision decision = new Decision();
        decision.setId(10001);
        when(mockDecisionService.createBAROverrideDecision()).thenReturn(decision);

        when(mockCrudService.findByNamedQuerySingleResult(RateUnqualified.DEFAULT_BY_PROPERTY_ID, QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters())).thenReturn(new RateUnqualified());
        when(mockCrudService.findByNamedQuerySingleResult(DecisionBAROutput.COUNT_OF_BAR_DECISION_FOR_DATE_RANGE_FOR_GIVEN_RATE_PLAN,
                QueryParameter.with("startDate", season.getDetailsStartDate().getTime()).and("endDate", season.getDetailsEndDate().getTime())
                        .and("ratePlanID", rateHeader.getId())
                        .and("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters())).thenReturn((long) 1);
        when(mockCrudService.findAll(RateUnqualifiedDetails.class)).thenReturn(Arrays.asList(createRateUnqualifiedDetails()));
        when(mockCrudService.executeUpdateByNamedQuery(isA(String.class), isA(Map.class))).thenReturn(1);

        when(syncEventAggregatorService.isSystemComponentDirty(SystemComponent.BAR_RATE_CONFIGURATION_CHANGED)).thenReturn(false);

        rateUnqualifiedComponent.saveOrDeleteSeasonDetails(rateHeader);

        verify(mockCrudService).flush();
        verify(mockCrudService, times(9)).executeUpdateByNamedQuery(isA(String.class), isA(Map.class));
    }

    private RateUnqualifiedDetails createRateUnqualifiedDetails() {
        RateUnqualifiedDetails rateUnqualifiedDetail = new RateUnqualifiedDetails();
        rateUnqualifiedDetail.setId(21);
        rateUnqualifiedDetail.setCreateDate(DateUtil.getCurrentDateWithoutTime());
        rateUnqualifiedDetail.setCreatedByUserId(1);
        return rateUnqualifiedDetail;
    }

    @Test
    @Tag("unqualifiedrate-flaky")
    public void shouldInactivateRateHeaderAndDeleteDetails() {
        RateUnqualifiedDetails rateUnqualifiedDetails = UniqueRateUnqualifiedDetails.createRateUnqualifiedDetails();
        RateUnqualified rateUnqualified = tenantCrudService().find(RateUnqualified.class, rateUnqualifiedDetails.getRateUnqualifiedId());
        RateHeader rateHeader = convertRateUnqualifiedEntityToDTO(rateUnqualified);
        rateHeader.setActionKey(ActionKeyEnum.DELETE);
        Season season = createSeason(rateUnqualifiedDetails.getStartDate(), rateUnqualifiedDetails.getEndDate());
        List<SeasonDetail> seasonDetails = new ArrayList<SeasonDetail>();
        SeasonDetail seasonDetail = new SeasonDetail(rateUnqualifiedDetails);
        seasonDetail.setActionKey(ActionKeyEnum.DELETE);
        seasonDetails.add(seasonDetail);
        season.setSeasonDetails(seasonDetails);
        List<Season> seasons = new ArrayList<Season>();
        seasons.add(season);
        rateHeader.setSeasons(seasons);
        List<RateHeader> rateHeaders = new ArrayList<RateHeader>();
        rateHeaders.add(rateHeader);

        when(syncEventAggregatorService.isSystemComponentDirty(SystemComponent.BAR_RATE_CONFIGURATION_CHANGED)).thenReturn(false);
        when(configParamsService.getParameterValue(IPConfigParamName.BAR_MAX_LOS.value())).thenReturn("8");
        when(configParamsService.getParameterValue(IPConfigParamName.FORECASTING_FORECAST_WINDOW_BDE.value())).thenReturn("365");

        InformationManagerCleanupService mockInfoMgrCleanUpService = mock(InformationManagerCleanupService.class);
        rateUnqualifiedComponent.infoMgrCleanUpService = mockInfoMgrCleanUpService;

        rateUnqualifiedComponent.saveUnqualifiedRates(rateHeaders);
        RateUnqualified rateUnqualifiedAfterSave = tenantCrudService().find(RateUnqualified.class, rateUnqualifiedDetails.getRateUnqualifiedId());
        assertEquals(Constants.INACTIVE_STATUS_ID, rateUnqualifiedAfterSave.getStatusId());
        RateUnqualifiedDetails rateUnqualifiedDetailsAfterSave = tenantCrudService().find(RateUnqualifiedDetails.class, rateUnqualifiedDetails.getId());
        assertNull(rateUnqualifiedDetailsAfterSave);

        verify(syncEventAggregatorService).registerSyncEvent(SyncEvent.BAR_RATE_CONFIGURATION_CHANGED);
        verify(mockInfoMgrCleanUpService).cleanUpNotificationsForDeletedRatePlan(rateHeader.getId(), PacmanWorkContextHelper.getPropertyId());
    }

    private DecisionBAROutput createDummyDecisionBarOutput(String overrideType) {
        DecisionBAROutput decisionBarOutput = new DecisionBAROutput();
        decisionBarOutput.setAccomClassId(UniqueAccomClassCreator.createUniqueAccomClass().getId());
        decisionBarOutput.setArrivalDate(DateUtil.addDaysToDate(new Date(), 10));
        int maxDecisionId = tenantCrudService().findByNamedQuerySingleResult(Decision.GET_MAX_ID, QueryParameter.with("propertyId", 5).parameters());
        decisionBarOutput.setDecision(tenantCrudService().find(Decision.class, maxDecisionId));

        decisionBarOutput.setLengthOfStay(-1);
        decisionBarOutput.setMonthId(1);
        decisionBarOutput.setYearId(1);
        decisionBarOutput.setOverride(overrideType);
        Integer rateUnqualifiedID = tenantCrudService().findByNamedQuerySingleResult(RateUnqualified.GET_ACTIVE_RATE_UNQUALIFIED_ID_BY_NAME, QueryParameter.with("name", "LV0").parameters());
        RateUnqualified rateUnQualified = tenantCrudService().find(RateUnqualified.class, rateUnqualifiedID);
        decisionBarOutput.setRateUnqualified(rateUnQualified);
        decisionBarOutput.setReasonTypeId(DecisionReasonType.ALL_IS_WELL.getId());
        if (overrideType.equals(Constants.BARDECISIONOVERRIDE_FLOOR)) {
            decisionBarOutput.setFloorRateUnqualified(rateUnQualified);
        }
        decisionBarOutput.setPropertyID(5);

        decisionBarOutput.setCreateDate(new Date());
        return tenantCrudService().save(decisionBarOutput);
    }

    @Test
    public void shouldReturnTrueIfAcomTypeIsActive() {
        boolean accomTypeActive = rateUnqualifiedComponent.isAccomTypeActive(Constants.ACTIVE_STATUS_ID);
        assertTrue(accomTypeActive);
    }

    @Test
    public void shouldReturnFalseIfAcomTypeIsInactive() {
        boolean accomTypeActive = rateUnqualifiedComponent.isAccomTypeActive(Constants.INACTIVE_STATUS_ID);
        assertFalse(accomTypeActive);
    }

    @Test
    public void shouldfetchRateUnqualifiedDetailsAsWellAsEmptyDetails() {
        RateUnqualifiedDetails rateUnqualifiedDetails = UniqueRateUnqualifiedDetails.createRateUnqualifiedDetails();
        AccomType accomType = tenantCrudService().find(AccomType.class, 5);
        accomType.setStatusId(Constants.INACTIVE_STATUS_ID);
        tenantCrudService().save(accomType);
        RateUnqualifiedDetails rateUnqualifiedDetails1 = createRateUnqualifiedDetailEntity(rateUnqualifiedDetails.getRateUnqualifiedId(), DateUtil.getCurrentDate(), DateUtil.addDaysToDate(DateUtil.getCurrentDate(), 5));
        rateUnqualifiedDetails1.setEndDate(DateUtil.getCurrentDate());

        tenantCrudService().save(rateUnqualifiedDetails1);
        tenantCrudService().flushAndClear();
        List<Season> seasons = rateUnqualifiedComponent.fetchSeasonsWithAccomTypes(rateUnqualifiedDetails.getRateUnqualifiedId());
        tenantCrudService().flushAndClear();
        assertNotNull(seasons.get(0).getSeasonDetails());
        assertEquals(true, seasons.get(0).getSeasonDetails().get(3).isAccomTypeActive());
        assertNull(seasons.get(0).getSeasonDetails().get(3).getId());
        assertTrue((seasons.get(0).getSeasonDetails().get(3).getMonday()).compareTo(new BigDecimal(-1)) == 0);
        assertEquals(false, seasons.get(0).getSeasonDetails().get(5).isAccomTypeActive());
        assertNotNull(seasons.get(0).getSeasonDetails().get(5).getId());
        assertEquals(300, seasons.get(0).getSeasonDetails().get(5).getMonday().intValue());
        assertEquals(true, seasons.get(0).getSeasonDetails().get(0).isAccomTypeActive());
        assertNotNull(seasons.get(0).getSeasonDetails().get(0).getId());
        assertEquals(1, seasons.get(0).getSeasonDetails().get(0).getMonday().intValue());
    }

    @Test
    public void shouldSortOnActionKey() {
        List<RateHeader> rateHeaders = new ArrayList<RateHeader>();
        RateHeader rateHeader = ratePlanEnteredFromUI("BAR 1", "Desc 1",
                new DateParameter(new Date()), new DateParameter(new Date()), ActionKeyEnum.CREATE);

        RateHeader rateHeader1 = ratePlanEnteredFromUI("BAR 2", "Desc 2",
                new DateParameter(new Date()), new DateParameter(new Date()), ActionKeyEnum.DELETE);
        rateHeaders.add(rateHeader);
        rateHeaders.add(rateHeader1);
        rateUnqualifiedComponent.sortOnActionKey(rateHeaders);
        assertTrue(ActionKeyEnum.DELETE.equals(rateHeaders.get(0).getActionKey()));
        assertTrue(ActionKeyEnum.CREATE.equals(rateHeaders.get(1).getActionKey()));
    }

    @Test
    public void testDeleteSeasonDetails() {
        RateUnqualifiedDetails rateUnqualifiedDetail = UniqueRateUnqualifiedDetails.createRateUnqualifiedDetails();
        rateUnqualifiedComponent.deleteSeasonDetails(rateUnqualifiedDetail);
        RateUnqualifiedDetails rateUnqualifiedDetailAfterDelete = tenantCrudService().find(RateUnqualifiedDetails.class, rateUnqualifiedDetail.getId());
        assertNull(rateUnqualifiedDetailAfterDelete);
    }

    @Test
    public void seasonisInFuture() {
        Date caughtUpDate = dateService.getCaughtUpDate();
        Season season = createSeason(caughtUpDate, DateUtil.addDaysToDate(caughtUpDate, 3));
        assertTrue(rateUnqualifiedComponent.isSeasonInFuture(season.getDetailsEndDate().getTime(), caughtUpDate));
    }

    @Test
    public void seasonisInFutureWithEndadateEqualsCaughtUpDate() {
        Date caughtUpDate = dateService.getCaughtUpDate();
        Season season = createSeason(caughtUpDate, caughtUpDate);
        assertTrue(rateUnqualifiedComponent.isSeasonInFuture(season.getDetailsEndDate().getTime(), caughtUpDate));
    }

    @Test
    public void seasonisInPast() {
        Date caughtUpDate = dateService.getCaughtUpDate();
        Season season = createSeason(caughtUpDate, DateUtil.addDaysToDate(caughtUpDate, -3));
        assertFalse(rateUnqualifiedComponent.isSeasonInFuture(season.getDetailsEndDate().getTime(), caughtUpDate));
    }

    @SuppressWarnings("unchecked")
    @Test
    public void shouldSaveRateUnqualifiedRoomClassMapping() {
        RateUnqualified savedRateHeader = createRateUnqualifiedObject();
        rateUnqualifiedComponent.saveRateUnqualifiedRoomClassMapping(savedRateHeader.getId());
        List<RateUnqualifiedAccomClass> ratePlanRoomClassMappingList = tenantCrudService().findByNamedQuery(RateUnqualifiedAccomClass.GET_RATE_UNQUALIFIED_ROOM_CLASS_BY_RATE_UNQUALIFIED, QueryParameter.with("rateUnqualifiedId", savedRateHeader.getId()).parameters());
        assertTrue(ratePlanRoomClassMappingList.size() == 3);
    }

    @SuppressWarnings("unchecked")
    @Test
    public void shouldSaveRateUnqualifiedDefaults() {
        RateUnqualified savedRateHeader = createRateUnqualifiedObject();
        rateUnqualifiedComponent.saveRateUnqualifiedDefaults(savedRateHeader.getId(), 8);

        List<RateUnqualifiedAccomClass> ratePlanRoomClassMappingList = tenantCrudService().findByNamedQuery(RateUnqualifiedAccomClass.GET_RATE_UNQUALIFIED_ROOM_CLASS_BY_RATE_UNQUALIFIED,
                QueryParameter.with("rateUnqualifiedId", savedRateHeader.getId()).parameters());

        for (RateUnqualifiedAccomClass ratePlanRoomClassMapping : ratePlanRoomClassMappingList) {
            List<RateUnqualifiedDefaults> rateUnqualifiedDefaultsForRatePlan = tenantCrudService().findByNamedQuery(RateUnqualifiedDefaults.BY_RATE_UNQUALIFIED_ACCOM_CLASS,
                    QueryParameter.with("id", ratePlanRoomClassMapping.getId()).parameters());
            assertTrue(rateUnqualifiedDefaultsForRatePlan.size() > 0);
        }
    }


    private RateUnqualified createRateUnqualifiedObject() {
        RateUnqualified rateUnqualified = new RateUnqualified();
        rateUnqualified.setName("Test");
        rateUnqualified.setDescription("Test");
        rateUnqualified.setRateUnqualifiedStartDate(new DateParameter(new Date()));
        rateUnqualified.setRateUnqualifiedEndDate(new DateParameter(new Date()));
        rateUnqualified.setPropertyId(PacmanWorkContextHelper.getPropertyId());
        rateUnqualified.setCurrency("");
        rateUnqualified.setDerivedRateCode("None");
        rateUnqualified.setFileMetadataId(createFileMetadata().getId());
        rateUnqualified.setIncludesPackage(0);
        rateUnqualified.setPriceRelative(0);
        rateUnqualified.setStatusId(1);
        rateUnqualified.setSystemDefault(0);
        rateUnqualified.setYieldable(1);
        return tenantCrudService().save(rateUnqualified);
    }

    @SuppressWarnings("unchecked")
    @Test
    public void shouldReturnActiveAccomClassHavingRoomTypesMapped() {
        List<AccomClass> listActiveAccomClassesWithMappedRoomTypes = tenantCrudService().findByNamedQuery(AccomClass.ALL_ACTIVE_NON_DEFAULT_NONEMPTY, QueryParameter.with("propertyId",
                PacmanThreadLocalContextHolder.getWorkContext().getPropertyId()).parameters());
        List<AccomClass> activeAccomClasses = tenantCrudService().findByNamedQuery(AccomClass.ALL_ACTIVE_NON_DEFAULT, QueryParameter.with("propertyId",
                PacmanThreadLocalContextHolder.getWorkContext().getPropertyId()).parameters());
        assertEquals(activeAccomClasses.size(), listActiveAccomClassesWithMappedRoomTypes.size());
        assertTrue(activeAccomClasses.size() > 0);
        AccomClass accomClass = activeAccomClasses.get(0);
        accomClass.setStatusId(2);
        tenantCrudService().save(accomClass);
        activeAccomClasses = tenantCrudService().findByNamedQuery(AccomClass.ALL_ACTIVE_NON_DEFAULT, QueryParameter.with("propertyId",
                PacmanThreadLocalContextHolder.getWorkContext().getPropertyId()).parameters());
        assertEquals(2, activeAccomClasses.size());
        listActiveAccomClassesWithMappedRoomTypes = tenantCrudService().findByNamedQuery(AccomClass.ALL_ACTIVE_NON_DEFAULT_NONEMPTY, QueryParameter.with("propertyId",
                PacmanThreadLocalContextHolder.getWorkContext().getPropertyId()).parameters());
        listActiveAccomClassesWithMappedRoomTypes.size();
        assertEquals(2, listActiveAccomClassesWithMappedRoomTypes.size());
        accomClass = activeAccomClasses.get(0);
        AccomClass accomClass2 = activeAccomClasses.get(1);
        Set<AccomType> accomTypes = accomClass.getAccomTypes();
        for (AccomType accomType : accomTypes) {
            accomType.setAccomClass(accomClass2);
            tenantCrudService().save(accomType);
        }
        listActiveAccomClassesWithMappedRoomTypes = tenantCrudService().findByNamedQuery(AccomClass.ALL_ACTIVE_NON_DEFAULT_NONEMPTY, QueryParameter.with("propertyId",
                PacmanThreadLocalContextHolder.getWorkContext().getPropertyId()).parameters());
        assertEquals(1, listActiveAccomClassesWithMappedRoomTypes.size());
    }

    @Test
    public void shouldFetchSeasonsWithRoomClassRanking() {
        List<AccomClass> accomClassList = tenantCrudService().findAll(AccomClass.class);
        int rank = 0;
        for (AccomClass accomClass : accomClassList) {
            accomClass.setRankOrder(rank++);
            tenantCrudService().save(accomClass);
        }

        RateUnqualified rateUnqualified = UniqueRateUnqualified.createRateUnqualifiedByDate(new Date(), DateUtil.addDaysToDate(new Date(), 60));
        AccomClass lowerRankAccomClass = accomClassList.get(1);
        AccomType lowerRankAccomType = UniqueAccomTypeCreator.createUniqueAccomTypeForAccomClass(PacmanWorkContextHelper.getPropertyId(), lowerRankAccomClass);
        UniqueRateUnqualifiedDetails.createRateUnqualifiedDetailsForRateUnqualified(lowerRankAccomType.getId(),
                rateUnqualified.getId(), DateUtil.addDaysToDate(new Date(), 1), DateUtil.addDaysToDate(new Date(), 10), 1f);

        AccomClass highrRankAccomClass = accomClassList.get(2);
        AccomType higherRankAccomType = UniqueAccomTypeCreator.createUniqueAccomTypeForAccomClass(PacmanWorkContextHelper.getPropertyId(), highrRankAccomClass);
        UniqueRateUnqualifiedDetails.createRateUnqualifiedDetailsForRateUnqualified(higherRankAccomType.getId(),
                rateUnqualified.getId(), DateUtil.addDaysToDate(new Date(), 1), DateUtil.addDaysToDate(new Date(), 10), 1f);

        List<Season> seasons = rateUnqualifiedComponent.fetchSeasonsWithAccomTypes(rateUnqualified.getId());

        assertEquals("STD", seasons.get(0).getSeasonDetails().get(0).getRoomClassName());
        assertEquals("STD", seasons.get(0).getSeasonDetails().get(1).getRoomClassName());
        assertEquals("STD", seasons.get(0).getSeasonDetails().get(2).getRoomClassName());
        assertEquals("STD", seasons.get(0).getSeasonDetails().get(3).getRoomClassName());
        assertEquals("DLX", seasons.get(0).getSeasonDetails().get(4).getRoomClassName());
        assertEquals("DLX", seasons.get(0).getSeasonDetails().get(5).getRoomClassName());
        assertEquals("STE", seasons.get(0).getSeasonDetails().get(6).getRoomClassName());

        assertTrue(1 == seasons.get(0).getSeasonDetails().get(0).getRoomClassOrder());
        assertTrue(2 == seasons.get(0).getSeasonDetails().get(4).getRoomClassOrder());
        assertTrue(3 == seasons.get(0).getSeasonDetails().get(6).getRoomClassOrder());
    }

    @SuppressWarnings("unchecked")
    @Test
    public void shouldExcludeUnassignedRoomTypesWhileFetchingSeasons() {
        //lets add a new room type in unassigned room class
        List<AccomClass> unassignedAccomClasses = tenantCrudService().findByNamedQuery(AccomClass.BY_SYSTEMDEFAULT_AND_PROPERTY_ID_ACCOM_CLASS, QueryParameter.with("systemDefault", 1).and("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters());
        AccomType unassignedRoomType = UniqueAccomTypeCreator.createUniqueAccomTypeForAccomClass(PacmanWorkContextHelper.getPropertyId(), unassignedAccomClasses.get(0));
        //create any rate plan with details just to see if its fetching unassigned room type
        RateUnqualifiedDetails rateUnqualifiedDetail = UniqueRateUnqualifiedDetails.createRateUnqualifiedDetails();
        List<Season> seasonsAfter = rateUnqualifiedComponent.fetchSeasonsWithAccomTypes(rateUnqualifiedDetail.getRateUnqualifiedId());
        //check to see unassigned room type should not be in the list
        for (SeasonDetail seasonDetail : seasonsAfter.get(0).getSeasonDetails()) {
            assertFalse(seasonDetail.getAccomTypeId().equals(unassignedRoomType.getId()));
        }
    }

    @SuppressWarnings("unchecked")
    @Test
    public void shouldFetchAllActiveAccomTypesButExcludeUnassigned() {
        //lets add a new room type in unassigned room class
        List<AccomClass> unassignedAccomClasses = tenantCrudService().findByNamedQuery(AccomClass.BY_SYSTEMDEFAULT_AND_PROPERTY_ID_ACCOM_CLASS, QueryParameter.with("systemDefault", 1).and("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters());
        AccomType unassignedRoomType = UniqueAccomTypeCreator.createUniqueAccomTypeForAccomClass(PacmanWorkContextHelper.getPropertyId(), unassignedAccomClasses.get(0));
        List<AccomType> accomTypes = rateUnqualifiedComponent.fetchActiveAccomTypes();
        //check unassigned room type should not be in list
        for (AccomType accomType : accomTypes) {
            assertTrue(!accomType.getId().equals(unassignedRoomType.getId()));
        }
    }

    @Test
    public void shouldNotInsertInPaceTableIfDecisionIdIsNull() {
        DecisionBAROutput decisionBarOutput = createDummyDecisionBarOutput(Constants.BARDECISIONOVERRIDE_USER);
        Season season = createSeason(decisionBarOutput.getArrivalDate(), decisionBarOutput.getArrivalDate());
        season.setActionKey(ActionKeyEnum.DELETE);
        @SuppressWarnings("unchecked")
        List<PaceBAROutput> paceListb4 = tenantCrudService().getEntityManager().createQuery("select pbo from PaceBAROutput pbo where arrivalDate between " +
                "'" + DateUtil.formatDate(decisionBarOutput.getArrivalDate(), "yyyy-MM-dd") + "'" + " and "
                + "'" + DateUtil.formatDate(decisionBarOutput.getArrivalDate(), "yyyy-MM-dd") + "'" + "and propertyID =5").getResultList();
        int size = paceListb4.size();
        rateUnqualifiedComponent.updateBarDecisionsAndOverrides(season, 3, rateUnqualifiedComponent.dateService.getCaughtUpDate());
        @SuppressWarnings("unchecked")
        List<PaceBAROutput> paceListAfter = tenantCrudService().getEntityManager().createQuery("select pbo from PaceBAROutput pbo where arrivalDate between " +
                "'" + DateUtil.formatDate(decisionBarOutput.getArrivalDate(), "yyyy-MM-dd") + "'" + " and "
                + "'" + DateUtil.formatDate(decisionBarOutput.getArrivalDate(), "yyyy-MM-dd") + "'" + "and propertyID =5").getResultList();
        int sizeAfterUpdate = paceListAfter.size();
        assertEquals(size, sizeAfterUpdate);
    }

    @SuppressWarnings("unchecked")
    @Test
    public void shouldfetchAccomTypesByCodes() throws Exception {
        List<AccomType> accomTypeList = tenantCrudService().findByNamedQuery(AccomType.ALL_ACTIVE, QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters());
        Collection<Integer> accomTypeIds = rateUnqualifiedComponent.getAccomTypeIdsFromCodes(accomTypeList.get(0).getAccomTypeCode()
                + "," + accomTypeList.get(1).getAccomTypeCode());
        assertTrue(accomTypeIds.contains(accomTypeList.get(0).getId()));
        assertTrue(accomTypeIds.contains(accomTypeList.get(1).getId()));
    }

    @Test
    public void testUnassignedRooomTypeAlert_Data_For_NO_DAYS_DO_NOT_Resolve() {
        AccomType newAccomType = UniqueAccomTypeCreator.createUniqueAccomTypeForAccomClass(5, UniqueAccomClassCreator.createUniqueAccomClassForPropertyID(5, 0));
        tenantCrudService().flush();
        InfoMgrInstanceEntity instanceEntity = createInforMgrInstanceEntity(newAccomType.getAccomTypeCode());
        RateUnqualifiedComponent partialmockedRateComponent = spy(new RateUnqualifiedComponent());
        partialmockedRateComponent.setCrudService(tenantCrudService());
        dateService.setConfigParamsService(configParamsService);
        partialmockedRateComponent.setDateService(dateService);
        partialmockedRateComponent.alertService = alertService;
        List<Integer> accomTypeIds = Arrays.asList(newAccomType.getId());
        when(partialmockedRateComponent.getAccomTypeIdsFromCodes(newAccomType.getAccomTypeCode())).thenReturn(accomTypeIds);

        Integer result = partialmockedRateComponent.evaluateAlertResolution(instanceEntity);
        assertEquals(1, result.intValue());
    }

    @Test
    public void testUnassignedRooomTypeAlert_Data_For_ALL_DAYS_Resolve() {
        AccomType newAccomType = UniqueAccomTypeCreator.createUniqueAccomTypeForAccomClass(5, UniqueAccomClassCreator.createUniqueAccomClassForPropertyID(5, 0));
        RateUnqualifiedDetails rateUnqualifiedDetails = UniqueRateUnqualifiedDetails.createFutureRateUnQualifiedDetailsForAccomTypeID(newAccomType.getId(), dateService.getCaughtUpDate());
        rateUnqualifiedDetails.setEndDate(DateUtil.addDaysToDate(dateService.getCaughtUpDate(), 366));
        tenantCrudService().save(rateUnqualifiedDetails);
        tenantCrudService().flush();
        InfoMgrInstanceEntity instanceEntity = createInforMgrInstanceEntity(newAccomType.getAccomTypeCode());
        RateUnqualifiedComponent partialmockedRateComponent = spy(new RateUnqualifiedComponent());
        partialmockedRateComponent.setCrudService(tenantCrudService());
        dateService.setConfigParamsService(configParamsService);
        when(configParamsService.getParameterValue(IPConfigParamName.FORECASTING_FORECAST_WINDOW_BDE.value())).thenReturn("365");

        partialmockedRateComponent.setDateService(dateService);
        partialmockedRateComponent.alertService = alertService;
        List<Integer> accomTypeIds = Arrays.asList(newAccomType.getId());
        when(partialmockedRateComponent.getAccomTypeIdsFromCodes(newAccomType.getAccomTypeCode())).thenReturn(accomTypeIds);

        Integer result = partialmockedRateComponent.evaluateAlertResolution(instanceEntity);
        assertEquals(0, result.intValue());
    }

    @Test
    public void testUnassignedRooomTypeAlert_Data_For_SOME_DAYS_DON_NOT_Resolve() {
        AccomType newAccomType = UniqueAccomTypeCreator.createUniqueAccomTypeForAccomClass(5, UniqueAccomClassCreator.createUniqueAccomClassForPropertyID(5, 0));
        RateUnqualifiedDetails rateUnqualifiedDetails = UniqueRateUnqualifiedDetails.createFutureRateUnQualifiedDetailsForAccomTypeID(newAccomType.getId(), dateService.getCaughtUpDate());
        rateUnqualifiedDetails.setEndDate(DateUtil.addDaysToDate(dateService.getCaughtUpDate(), 100));
        tenantCrudService().save(rateUnqualifiedDetails);
        InfoMgrInstanceEntity instanceEntity = createInforMgrInstanceEntity(newAccomType.getAccomTypeCode());
        RateUnqualifiedComponent partialmockedRateComponent = spy(new RateUnqualifiedComponent());
        partialmockedRateComponent.setCrudService(tenantCrudService());
        dateService.setConfigParamsService(configParamsService);
        partialmockedRateComponent.setDateService(dateService);
        partialmockedRateComponent.alertService = alertService;
        when(configParamsService.getParameterValue(IPConfigParamName.FORECASTING_FORECAST_WINDOW_BDE.value())).thenReturn("365");

        List<Integer> accomTypeIds = Arrays.asList(newAccomType.getId());
        when(partialmockedRateComponent.getAccomTypeIdsFromCodes(newAccomType.getAccomTypeCode())).thenReturn(accomTypeIds);

        Integer result = partialmockedRateComponent.evaluateAlertResolution(instanceEntity);
        assertFalse(0 == result);
    }

    private InfoMgrInstanceEntity createInforMgrInstanceEntity(String details) {
        InfoMgrInstanceEntity instance = new InfoMgrInstanceEntity();
        InfoMgrTypeEntity infoMgrTpe = new InfoMgrTypeEntity();
        infoMgrTpe.setName(AlertType.UnassignedRoomType.name());
        instance.setAlertType(infoMgrTpe);

        instance.setDetails(details);
        return instance;
    }

    @Test
    @Tag("unqualifiedrate-flaky")
    public void shouldSearchRatePlansByStartDateEndDateAndRatePlans() {
        //GIVEN
        tenantCrudService().executeUpdateByNativeQuery("update Rate_Unqualified set Status_ID = 2");
        LocalDate currentDate = LocalDate.now();
        RateUnqualified rateUnqualified = UniqueRateUnqualified.createRateUnqualifiedByDate(currentDate.toDate(), currentDate.plusDays(30).toDate());
        RateUnqualifiedDetails rateUnqualifiedDetail1 = UniqueRateUnqualifiedDetails.createRateUnqualifiedDetailsForRateUnqualified(4, rateUnqualified.getId(),
                currentDate.plusDays(10).toDate(), currentDate.plusDays(20).toDate(), 2f);
        RateUnqualifiedDetails rateUnqualifiedDetail2 = UniqueRateUnqualifiedDetails.createRateUnqualifiedDetailsForRateUnqualified(5, rateUnqualified.getId(),
                currentDate.plusDays(10).toDate(), currentDate.plusDays(20).toDate(), 2f);
        Set<Integer> ratePlanIds = new HashSet<>();
        ratePlanIds.add(rateUnqualified.getId());
        //WHEN
        List<RateHeader> rateHeaders = rateUnqualifiedComponent.searchForRatePlansBy(LocalDate.now().plusDays(10), LocalDate.now().plusDays(20), ratePlanIds);
        //THEN
        assertEquals(1, rateHeaders.size());
        RateHeader actualRateHeader = rateHeaders.get(0);
        assertRateHeader(rateUnqualified, actualRateHeader);
        assertSeason(rateUnqualifiedDetail1, actualRateHeader.getSeasons().get(0));
        List<SeasonDetail> seasonDetails = actualRateHeader.getSeasons().get(0).getSeasonDetails();
        assertEquals(5, seasonDetails.size());

        assertNoRateForSeasonDetails(seasonDetails.get(0), 6);
        assertEquals("DBL", seasonDetails.get(0).getAccomTypeCode());
        assertEquals("STD", seasonDetails.get(0).getRoomClassName());

        assertNoRateForSeasonDetails(seasonDetails.get(1), 8);
        assertEquals("K", seasonDetails.get(1).getAccomTypeCode());
        assertEquals("STD", seasonDetails.get(1).getRoomClassName());

        assertNoRateForSeasonDetails(seasonDetails.get(2), 7);
        assertEquals("Q", seasonDetails.get(2).getAccomTypeCode());
        assertEquals("STD", seasonDetails.get(2).getRoomClassName());

        assertSeasonDetails(rateUnqualifiedDetail1, seasonDetails.get(3));
        assertEquals("DLX", seasonDetails.get(3).getAccomTypeCode());
        assertEquals("DLX", seasonDetails.get(3).getRoomClassName());

        assertSeasonDetails(rateUnqualifiedDetail2, seasonDetails.get(4));
        assertEquals("STE", seasonDetails.get(4).getAccomTypeCode());
        assertEquals("STE", seasonDetails.get(4).getRoomClassName());
    }

    private void assertNoRateForSeasonDetails(SeasonDetail seasonDetail, int expectedRoomTypeId) {
        assertEquals(expectedRoomTypeId, seasonDetail.getAccomTypeId().intValue(), "Expected Accom Type Id did not match");
        BigDecimal noRate = new BigDecimal("-1");
        assertEquals(noRate, seasonDetail.getSunday(), "Expected Sunday value did not match");
        assertEquals(noRate, seasonDetail.getMonday(), "Expected Monday value did not match");
        assertEquals(noRate, seasonDetail.getTuesday(), "Expected Tuesday value did not match");
        assertEquals(noRate, seasonDetail.getWednesday(), "Expected Wednesday value did not match");
        assertEquals(noRate, seasonDetail.getThursday(), "Expected Thursday value did not match");
        assertEquals(noRate, seasonDetail.getFriday(), "Expected Friday value did not match");
        assertEquals(noRate, seasonDetail.getSaturday(), "Expected Saturday value did not match");
    }

    @Test
    @Tag("unqualifiedrate-flaky")
    public void shouldSearchRatePlansByStartDateEndDateOnly() {
        //GIVEN
        tenantCrudService().executeUpdateByNativeQuery("update Rate_Unqualified set Status_ID = 2");
        LocalDate currentDate = LocalDate.now();
        RateUnqualified rateUnqualified = UniqueRateUnqualified.createRateUnqualifiedByDateAndName(currentDate.toDate(), currentDate.plusDays(30).toDate(), "LV0");
        RateUnqualifiedDetails rateUnqualifiedDetail1 = UniqueRateUnqualifiedDetails.createRateUnqualifiedDetailsForRateUnqualified(4, rateUnqualified.getId(),
                currentDate.plusDays(10).toDate(), currentDate.plusDays(20).toDate(), 2f);
        RateUnqualifiedDetails rateUnqualifiedDetail2 = UniqueRateUnqualifiedDetails.createRateUnqualifiedDetailsForRateUnqualified(5, rateUnqualified.getId(),
                currentDate.plusDays(10).toDate(), currentDate.plusDays(20).toDate(), 2f);
        RateUnqualifiedDetails rateUnqualifiedDetail3 = UniqueRateUnqualifiedDetails.createRateUnqualifiedDetailsForRateUnqualified(5, rateUnqualified.getId(),
                currentDate.plusDays(21).toDate(), currentDate.plusDays(30).toDate(), 2f);

        RateUnqualified rateUnqualified2 = UniqueRateUnqualified.createRateUnqualifiedByDateAndName(currentDate.toDate(), currentDate.plusDays(60).toDate(), "LV1");
        RateUnqualifiedDetails rateUnqualifiedDetail4 = UniqueRateUnqualifiedDetails.createRateUnqualifiedDetailsForRateUnqualified(4, rateUnqualified2.getId(),
                currentDate.plusDays(10).toDate(), currentDate.plusDays(20).toDate(), 4f);
        RateUnqualifiedDetails rateUnqualifiedDetail5 = UniqueRateUnqualifiedDetails.createRateUnqualifiedDetailsForRateUnqualified(5, rateUnqualified2.getId(),
                currentDate.plusDays(10).toDate(), currentDate.plusDays(20).toDate(), 4f);
        //WHEN
        List<RateHeader> rateHeaders = rateUnqualifiedComponent.searchForRatePlansBy(LocalDate.now().plusDays(10), LocalDate.now().plusDays(20), null);
        //THEN
        assertEquals(2, rateHeaders.size());
        RateHeader actualRateHeader = rateHeaders.get(0);
        assertRateHeader(rateUnqualified, actualRateHeader);
        assertSeason(rateUnqualifiedDetail1, actualRateHeader.getSeasons().get(0));

        List<SeasonDetail> actualSeasonDetails1 = actualRateHeader.getSeasons().get(0).getSeasonDetails();
        assertEquals(5, actualSeasonDetails1.size());

        assertNoRateForSeasonDetails(actualSeasonDetails1.get(0), 6);
        assertEquals("DBL", actualSeasonDetails1.get(0).getAccomTypeCode());
        assertEquals("STD", actualSeasonDetails1.get(0).getRoomClassName());

        assertNoRateForSeasonDetails(actualSeasonDetails1.get(1), 8);
        assertEquals("K", actualSeasonDetails1.get(1).getAccomTypeCode());
        assertEquals("STD", actualSeasonDetails1.get(1).getRoomClassName());

        assertNoRateForSeasonDetails(actualSeasonDetails1.get(2), 7);
        assertEquals("Q", actualSeasonDetails1.get(2).getAccomTypeCode());
        assertEquals("STD", actualSeasonDetails1.get(2).getRoomClassName());

        assertSeasonDetails(rateUnqualifiedDetail1, actualSeasonDetails1.get(3));
        assertEquals("DLX", actualSeasonDetails1.get(3).getAccomTypeCode());
        assertEquals("DLX", actualSeasonDetails1.get(3).getRoomClassName());

        assertSeasonDetails(rateUnqualifiedDetail2, actualSeasonDetails1.get(4));
        assertEquals("STE", actualSeasonDetails1.get(4).getAccomTypeCode());
        assertEquals("STE", actualSeasonDetails1.get(4).getRoomClassName());

        RateHeader actualRateHeader2 = rateHeaders.get(1);
        assertRateHeader(rateUnqualified2, actualRateHeader2);

        assertSeason(rateUnqualifiedDetail4, actualRateHeader2.getSeasons().get(0));
        List<SeasonDetail> actualSeasonDetails2 = actualRateHeader2.getSeasons().get(0).getSeasonDetails();
        assertEquals(5, actualSeasonDetails1.size());

        assertNoRateForSeasonDetails(actualSeasonDetails2.get(0), 6);
        assertEquals("DBL", actualSeasonDetails2.get(0).getAccomTypeCode());
        assertEquals("STD", actualSeasonDetails2.get(0).getRoomClassName());

        assertNoRateForSeasonDetails(actualSeasonDetails2.get(1), 8);
        assertEquals("K", actualSeasonDetails2.get(1).getAccomTypeCode());
        assertEquals("STD", actualSeasonDetails2.get(1).getRoomClassName());

        assertNoRateForSeasonDetails(actualSeasonDetails2.get(2), 7);
        assertEquals("Q", actualSeasonDetails2.get(2).getAccomTypeCode());
        assertEquals("STD", actualSeasonDetails2.get(2).getRoomClassName());

        assertSeasonDetails(rateUnqualifiedDetail4, actualSeasonDetails2.get(3));
        assertEquals("DLX", actualSeasonDetails2.get(3).getAccomTypeCode());
        assertEquals("DLX", actualSeasonDetails2.get(3).getRoomClassName());

        assertSeasonDetails(rateUnqualifiedDetail5, actualSeasonDetails2.get(4));
        assertEquals("STE", actualSeasonDetails2.get(4).getAccomTypeCode());
        assertEquals("STE", actualSeasonDetails2.get(4).getRoomClassName());
    }


    @Test
    @Tag("unqualifiedrate-flaky")
    public void shouldSearchRatePlansByRatePlanOnly() {
        //GIVEN
        tenantCrudService().executeUpdateByNativeQuery("update Rate_Unqualified set Status_ID = 2");
        LocalDate currentDate = LocalDate.now();
        RateUnqualified rateUnqualified = UniqueRateUnqualified.createRateUnqualifiedByDate(currentDate.toDate(), currentDate.plusDays(30).toDate());
        RateUnqualifiedDetails rateUnqualifiedDetail1 = UniqueRateUnqualifiedDetails.createRateUnqualifiedDetailsForRateUnqualified(4, rateUnqualified.getId(),
                currentDate.plusDays(10).toDate(), currentDate.plusDays(20).toDate(), 2f);
        RateUnqualifiedDetails rateUnqualifiedDetail2 = UniqueRateUnqualifiedDetails.createRateUnqualifiedDetailsForRateUnqualified(5, rateUnqualified.getId(),
                currentDate.plusDays(10).toDate(), currentDate.plusDays(20).toDate(), 2f);

        RateUnqualified rateUnqualified2 = UniqueRateUnqualified.createRateUnqualifiedByDate(currentDate.plusDays(31).toDate(), currentDate.plusDays(60).toDate());
        RateUnqualifiedDetails rateUnqualifiedDetail3 = UniqueRateUnqualifiedDetails.createRateUnqualifiedDetailsForRateUnqualified(4, rateUnqualified.getId(),
                currentDate.plusDays(31).toDate(), currentDate.plusDays(50).toDate(), 2f);
        RateUnqualifiedDetails rateUnqualifiedDetail4 = UniqueRateUnqualifiedDetails.createRateUnqualifiedDetailsForRateUnqualified(5, rateUnqualified.getId(),
                currentDate.plusDays(31).toDate(), currentDate.plusDays(50).toDate(), 2f);
        Set<Integer> ratePlanIds = new HashSet<>();
        ratePlanIds.add(rateUnqualified.getId());
        //WHEN
        List<RateHeader> rateHeaders = rateUnqualifiedComponent.searchForRatePlansBy(null, null, ratePlanIds);
        //THEN
        assertEquals(1, rateHeaders.size());
        RateHeader actualRateHeader = rateHeaders.get(0);
        assertRateHeader(rateUnqualified, actualRateHeader);
        assertSeason(rateUnqualifiedDetail1, actualRateHeader.getSeasons().get(0));

        List<SeasonDetail> seasonDetails = actualRateHeader.getSeasons().get(0).getSeasonDetails();
        assertEquals(5, seasonDetails.size());

        assertNoRateForSeasonDetails(seasonDetails.get(0), 6);
        assertEquals("DBL", seasonDetails.get(0).getAccomTypeCode());
        assertEquals("STD", seasonDetails.get(0).getRoomClassName());

        assertNoRateForSeasonDetails(seasonDetails.get(1), 8);
        assertEquals("K", seasonDetails.get(1).getAccomTypeCode());
        assertEquals("STD", seasonDetails.get(1).getRoomClassName());

        assertNoRateForSeasonDetails(seasonDetails.get(2), 7);
        assertEquals("Q", seasonDetails.get(2).getAccomTypeCode());
        assertEquals("STD", seasonDetails.get(2).getRoomClassName());

        assertSeasonDetails(rateUnqualifiedDetail1, seasonDetails.get(3));
        assertEquals("DLX", seasonDetails.get(3).getAccomTypeCode());
        assertEquals("DLX", seasonDetails.get(3).getRoomClassName());

        assertSeasonDetails(rateUnqualifiedDetail2, seasonDetails.get(4));
        assertEquals("STE", seasonDetails.get(4).getAccomTypeCode());
        assertEquals("STE", seasonDetails.get(4).getRoomClassName());
    }

    @Test
    @Tag("unqualifiedrate-flaky")
    public void shouldSearchSuchSeasonsWhenSelectedStartDateFallsBetweenSeason() {
        //GIVEN
        tenantCrudService().executeUpdateByNativeQuery("update Rate_Unqualified set Status_ID = 2");
        LocalDate currentDate = LocalDate.now();
        RateUnqualified rateUnqualified = UniqueRateUnqualified.createRateUnqualifiedByDate(currentDate.toDate(), currentDate.plusDays(30).toDate());
        RateUnqualifiedDetails rateUnqualifiedDetail1 = UniqueRateUnqualifiedDetails.createRateUnqualifiedDetailsForRateUnqualified(4, rateUnqualified.getId(),
                currentDate.plusDays(10).toDate(), currentDate.plusDays(20).toDate(), 2f);
        RateUnqualifiedDetails rateUnqualifiedDetail2 = UniqueRateUnqualifiedDetails.createRateUnqualifiedDetailsForRateUnqualified(5, rateUnqualified.getId(),
                currentDate.plusDays(10).toDate(), currentDate.plusDays(20).toDate(), 2f);
        //WHEN
        List<RateHeader> rateHeaders = rateUnqualifiedComponent.searchForRatePlansBy(LocalDate.now().plusDays(15), LocalDate.now().plusDays(20), null);
        //THEN
        assertEquals(1, rateHeaders.size());
        RateHeader actualRateHeader = rateHeaders.get(0);
        assertRateHeader(rateUnqualified, actualRateHeader);
        assertSeason(rateUnqualifiedDetail1, actualRateHeader.getSeasons().get(0));

        List<SeasonDetail> seasonDetails = actualRateHeader.getSeasons().get(0).getSeasonDetails();
        assertEquals(5, seasonDetails.size());

        assertNoRateForSeasonDetails(seasonDetails.get(0), 6);
        assertEquals("DBL", seasonDetails.get(0).getAccomTypeCode());
        assertEquals("STD", seasonDetails.get(0).getRoomClassName());

        assertNoRateForSeasonDetails(seasonDetails.get(1), 8);
        assertEquals("K", seasonDetails.get(1).getAccomTypeCode());
        assertEquals("STD", seasonDetails.get(1).getRoomClassName());

        assertNoRateForSeasonDetails(seasonDetails.get(2), 7);
        assertEquals("Q", seasonDetails.get(2).getAccomTypeCode());
        assertEquals("STD", seasonDetails.get(2).getRoomClassName());

        assertSeasonDetails(rateUnqualifiedDetail1, seasonDetails.get(3));
        assertEquals("DLX", seasonDetails.get(3).getAccomTypeCode());
        assertEquals("DLX", seasonDetails.get(3).getRoomClassName());

        assertSeasonDetails(rateUnqualifiedDetail2, seasonDetails.get(4));
        assertEquals("STE", seasonDetails.get(4).getAccomTypeCode());
        assertEquals("STE", seasonDetails.get(4).getRoomClassName());
    }

    @Test
    @Tag("unqualifiedrate-flaky")
    public void shouldSearchSuchSeasonsWhenSelectedEndDateFallsBetweenSeason() {
        //GIVEN
        tenantCrudService().executeUpdateByNativeQuery("update Rate_Unqualified set Status_ID = 2");
        LocalDate currentDate = LocalDate.now();
        RateUnqualified rateUnqualified = UniqueRateUnqualified.createRateUnqualifiedByDate(currentDate.toDate(), currentDate.plusDays(30).toDate());
        RateUnqualifiedDetails rateUnqualifiedDetail1 = UniqueRateUnqualifiedDetails.createRateUnqualifiedDetailsForRateUnqualified(4, rateUnqualified.getId(),
                currentDate.plusDays(10).toDate(), currentDate.plusDays(20).toDate(), 2f);
        RateUnqualifiedDetails rateUnqualifiedDetail2 = UniqueRateUnqualifiedDetails.createRateUnqualifiedDetailsForRateUnqualified(5, rateUnqualified.getId(),
                currentDate.plusDays(10).toDate(), currentDate.plusDays(20).toDate(), 2f);
        //WHEN
        List<RateHeader> rateHeaders = rateUnqualifiedComponent.searchForRatePlansBy(LocalDate.now().plusDays(10), LocalDate.now().plusDays(18), null);
        //THEN
        assertEquals(1, rateHeaders.size());
        RateHeader actualRateHeader = rateHeaders.get(0);
        assertRateHeader(rateUnqualified, actualRateHeader);
        assertSeason(rateUnqualifiedDetail1, actualRateHeader.getSeasons().get(0));

        List<SeasonDetail> seasonDetails = actualRateHeader.getSeasons().get(0).getSeasonDetails();
        assertEquals(5, seasonDetails.size());

        assertNoRateForSeasonDetails(seasonDetails.get(0), 6);
        assertEquals("DBL", seasonDetails.get(0).getAccomTypeCode());
        assertEquals("STD", seasonDetails.get(0).getRoomClassName());

        assertNoRateForSeasonDetails(seasonDetails.get(1), 8);
        assertEquals("K", seasonDetails.get(1).getAccomTypeCode());
        assertEquals("STD", seasonDetails.get(1).getRoomClassName());

        assertNoRateForSeasonDetails(seasonDetails.get(2), 7);
        assertEquals("Q", seasonDetails.get(2).getAccomTypeCode());
        assertEquals("STD", seasonDetails.get(2).getRoomClassName());

        assertSeasonDetails(rateUnqualifiedDetail1, seasonDetails.get(3));
        assertEquals("DLX", seasonDetails.get(3).getAccomTypeCode());
        assertEquals("DLX", seasonDetails.get(3).getRoomClassName());

        assertSeasonDetails(rateUnqualifiedDetail2, seasonDetails.get(4));
        assertEquals("STE", seasonDetails.get(4).getAccomTypeCode());
        assertEquals("STE", seasonDetails.get(4).getRoomClassName());
    }

    @Test
    @Tag("unqualifiedrate-flaky")
    public void shouldSearchSeasonsByStartDateOnly() {
        //GIVEN
        tenantCrudService().executeUpdateByNativeQuery("update Rate_Unqualified set Status_ID = 2");
        LocalDate currentDate = LocalDate.now();
        RateUnqualified rateUnqualified = UniqueRateUnqualified.createRateUnqualifiedByDate(currentDate.toDate(), currentDate.plusDays(30).toDate());
        RateUnqualifiedDetails rateUnqualifiedDetail1 = UniqueRateUnqualifiedDetails.createRateUnqualifiedDetailsForRateUnqualified(4, rateUnqualified.getId(),
                currentDate.plusDays(10).toDate(), currentDate.plusDays(20).toDate(), 2f);
        RateUnqualifiedDetails rateUnqualifiedDetail2 = UniqueRateUnqualifiedDetails.createRateUnqualifiedDetailsForRateUnqualified(5, rateUnqualified.getId(),
                currentDate.plusDays(10).toDate(), currentDate.plusDays(20).toDate(), 2f);
        //WHEN
        List<RateHeader> rateHeaders = rateUnqualifiedComponent.searchForRatePlansBy(LocalDate.now().plusDays(10), null, null);
        //THEN
        assertEquals(1, rateHeaders.size());
        RateHeader actualRateHeader = rateHeaders.get(0);
        assertRateHeader(rateUnqualified, actualRateHeader);
        assertSeason(rateUnqualifiedDetail1, actualRateHeader.getSeasons().get(0));
        List<SeasonDetail> seasonDetails = actualRateHeader.getSeasons().get(0).getSeasonDetails();
        assertEquals(5, seasonDetails.size());

        assertNoRateForSeasonDetails(seasonDetails.get(0), 6);
        assertEquals("DBL", seasonDetails.get(0).getAccomTypeCode());
        assertEquals("STD", seasonDetails.get(0).getRoomClassName());

        assertNoRateForSeasonDetails(seasonDetails.get(1), 8);
        assertEquals("K", seasonDetails.get(1).getAccomTypeCode());
        assertEquals("STD", seasonDetails.get(1).getRoomClassName());

        assertNoRateForSeasonDetails(seasonDetails.get(2), 7);
        assertEquals("Q", seasonDetails.get(2).getAccomTypeCode());
        assertEquals("STD", seasonDetails.get(2).getRoomClassName());

        assertSeasonDetails(rateUnqualifiedDetail1, seasonDetails.get(3));
        assertEquals("DLX", seasonDetails.get(3).getAccomTypeCode());
        assertEquals("DLX", seasonDetails.get(3).getRoomClassName());

        assertSeasonDetails(rateUnqualifiedDetail2, seasonDetails.get(4));
        assertEquals("STE", seasonDetails.get(4).getAccomTypeCode());
        assertEquals("STE", seasonDetails.get(4).getRoomClassName());
    }

    @Test
    @Tag("unqualifiedrate-flaky")
    public void shouldSearchSeasonsByEndDateOnly() {
        //GIVEN
        tenantCrudService().executeUpdateByNativeQuery("update Rate_Unqualified set Status_ID = 2");
        LocalDate currentDate = LocalDate.now();
        RateUnqualified rateUnqualified = UniqueRateUnqualified.createRateUnqualifiedByDate(currentDate.toDate(), currentDate.plusDays(30).toDate());
        RateUnqualifiedDetails rateUnqualifiedDetail1 = UniqueRateUnqualifiedDetails.createRateUnqualifiedDetailsForRateUnqualified(4, rateUnqualified.getId(),
                currentDate.plusDays(10).toDate(), currentDate.plusDays(20).toDate(), 2f);
        RateUnqualifiedDetails rateUnqualifiedDetail2 = UniqueRateUnqualifiedDetails.createRateUnqualifiedDetailsForRateUnqualified(5, rateUnqualified.getId(),
                currentDate.plusDays(10).toDate(), currentDate.plusDays(20).toDate(), 2f);
        //WHEN
        List<RateHeader> rateHeaders = rateUnqualifiedComponent.searchForRatePlansBy(null, LocalDate.now().plusDays(20), null);
        //THEN
        assertEquals(1, rateHeaders.size());
        RateHeader actualRateHeader = rateHeaders.get(0);
        assertRateHeader(rateUnqualified, actualRateHeader);
        assertSeason(rateUnqualifiedDetail1, actualRateHeader.getSeasons().get(0));

        List<SeasonDetail> seasonDetails = actualRateHeader.getSeasons().get(0).getSeasonDetails();
        assertEquals(5, seasonDetails.size());

        assertNoRateForSeasonDetails(seasonDetails.get(0), 6);
        assertEquals("DBL", seasonDetails.get(0).getAccomTypeCode());
        assertEquals("STD", seasonDetails.get(0).getRoomClassName());

        assertNoRateForSeasonDetails(seasonDetails.get(1), 8);
        assertEquals("K", seasonDetails.get(1).getAccomTypeCode());
        assertEquals("STD", seasonDetails.get(1).getRoomClassName());

        assertNoRateForSeasonDetails(seasonDetails.get(2), 7);
        assertEquals("Q", seasonDetails.get(2).getAccomTypeCode());
        assertEquals("STD", seasonDetails.get(2).getRoomClassName());

        assertSeasonDetails(rateUnqualifiedDetail1, seasonDetails.get(3));
        assertEquals("DLX", seasonDetails.get(3).getAccomTypeCode());
        assertEquals("DLX", seasonDetails.get(3).getRoomClassName());

        assertSeasonDetails(rateUnqualifiedDetail2, seasonDetails.get(4));
        assertEquals("STE", seasonDetails.get(4).getAccomTypeCode());
        assertEquals("STE", seasonDetails.get(4).getRoomClassName());
    }

    @Test
    @Tag("unqualifiedrate-flaky")
    public void shouldSearchSeasonsOrderByRoomClassRank() {
        //GIVEN
        tenantCrudService().executeUpdateByNativeQuery("update Rate_Unqualified set Status_ID = 2");
        LocalDate currentDate = LocalDate.now();
        RateUnqualified rateUnqualified = UniqueRateUnqualified.createRateUnqualifiedByDate(currentDate.toDate(), currentDate.plusDays(30).toDate());
        RateUnqualifiedDetails rateUnqualifiedDetail1 = UniqueRateUnqualifiedDetails.createRateUnqualifiedDetailsForRateUnqualified(4, rateUnqualified.getId(),
                currentDate.plusDays(10).toDate(), currentDate.plusDays(20).toDate(), 2f);
        RateUnqualifiedDetails rateUnqualifiedDetail3 = UniqueRateUnqualifiedDetails.createRateUnqualifiedDetailsForRateUnqualified(7, rateUnqualified.getId(),
                currentDate.plusDays(10).toDate(), currentDate.plusDays(20).toDate(), 2f);
        //WHEN
        List<RateHeader> rateHeaders = rateUnqualifiedComponent.searchForRatePlansBy(LocalDate.now().plusDays(10), LocalDate.now().plusDays(20), null);
        //THEN
        assertEquals(1, rateHeaders.size());
        RateHeader actualRateHeader = rateHeaders.get(0);
        assertRateHeader(rateUnqualified, actualRateHeader);
        assertSeason(rateUnqualifiedDetail1, actualRateHeader.getSeasons().get(0));

        List<SeasonDetail> seasonDetails = actualRateHeader.getSeasons().get(0).getSeasonDetails();
        assertEquals(5, seasonDetails.size());

        assertNoRateForSeasonDetails(seasonDetails.get(0), 6);
        assertEquals("DBL", seasonDetails.get(0).getAccomTypeCode());
        assertEquals("STD", seasonDetails.get(0).getRoomClassName());
        assertEquals(1, seasonDetails.get(0).getRoomClassOrder().intValue());

        assertNoRateForSeasonDetails(seasonDetails.get(1), 8);
        assertEquals("K", seasonDetails.get(1).getAccomTypeCode());
        assertEquals("STD", seasonDetails.get(1).getRoomClassName());
        assertEquals(1, seasonDetails.get(1).getRoomClassOrder().intValue());

        assertSeasonDetails(rateUnqualifiedDetail3, seasonDetails.get(2));
        assertEquals("Q", seasonDetails.get(2).getAccomTypeCode());
        assertEquals("STD", seasonDetails.get(2).getRoomClassName());
        assertEquals(1, seasonDetails.get(2).getRoomClassOrder().intValue());

        assertSeasonDetails(rateUnqualifiedDetail1, seasonDetails.get(3));
        assertEquals("DLX", seasonDetails.get(3).getAccomTypeCode());
        assertEquals("DLX", seasonDetails.get(3).getRoomClassName());
        assertEquals(2, seasonDetails.get(3).getRoomClassOrder().intValue());

        assertNoRateForSeasonDetails(seasonDetails.get(4), 5);
        assertEquals("STE", seasonDetails.get(4).getAccomTypeCode());
        assertEquals("STE", seasonDetails.get(4).getRoomClassName());
        assertEquals(3, seasonDetails.get(4).getRoomClassOrder().intValue());
    }

    @Test
    @Tag("unqualifiedrate-flaky")
    public void shouldSearchSeasonsOrderByRoomTypeNameUnderRoomClassRank() {
        //GIVEN
        tenantCrudService().executeUpdateByNativeQuery("update Rate_Unqualified set Status_ID = 2");
        LocalDate currentDate = LocalDate.now();
        RateUnqualified rateUnqualified = UniqueRateUnqualified.createRateUnqualifiedByDate(currentDate.toDate(), currentDate.plusDays(30).toDate());
        RateUnqualifiedDetails rateUnqualifiedDetail1 = UniqueRateUnqualifiedDetails.createRateUnqualifiedDetailsForRateUnqualified(4, rateUnqualified.getId(),
                currentDate.plusDays(10).toDate(), currentDate.plusDays(20).toDate(), 2f);
        RateUnqualifiedDetails rateUnqualifiedDetail2 = UniqueRateUnqualifiedDetails.createRateUnqualifiedDetailsForRateUnqualified(6, rateUnqualified.getId(),
                currentDate.plusDays(10).toDate(), currentDate.plusDays(20).toDate(), 2f);
        RateUnqualifiedDetails rateUnqualifiedDetail3 = UniqueRateUnqualifiedDetails.createRateUnqualifiedDetailsForRateUnqualified(7, rateUnqualified.getId(),
                currentDate.plusDays(10).toDate(), currentDate.plusDays(20).toDate(), 2f);
        //WHEN
        List<RateHeader> rateHeaders = rateUnqualifiedComponent.searchForRatePlansBy(LocalDate.now().plusDays(10), LocalDate.now().plusDays(20), null);
        //THEN
        assertEquals(1, rateHeaders.size());
        RateHeader actualRateHeader = rateHeaders.get(0);
        assertRateHeader(rateUnqualified, actualRateHeader);
        assertSeason(rateUnqualifiedDetail1, actualRateHeader.getSeasons().get(0));

        List<SeasonDetail> seasonDetails = actualRateHeader.getSeasons().get(0).getSeasonDetails();
        assertEquals(5, seasonDetails.size());

        assertSeasonDetails(rateUnqualifiedDetail2, seasonDetails.get(0));
        assertEquals("DBL", seasonDetails.get(0).getAccomTypeCode());
        assertEquals("STD", seasonDetails.get(0).getRoomClassName());
        assertEquals(1, seasonDetails.get(0).getRoomClassOrder().intValue());

        assertNoRateForSeasonDetails(seasonDetails.get(1), 8);
        assertEquals("K", seasonDetails.get(1).getAccomTypeCode());
        assertEquals("STD", seasonDetails.get(1).getRoomClassName());

        assertSeasonDetails(rateUnqualifiedDetail3, seasonDetails.get(2));
        assertEquals("Q", seasonDetails.get(2).getAccomTypeCode());
        assertEquals("STD", seasonDetails.get(2).getRoomClassName());
        assertEquals(1, seasonDetails.get(2).getRoomClassOrder().intValue());

        assertSeasonDetails(rateUnqualifiedDetail1, seasonDetails.get(3));
        assertEquals("DLX", seasonDetails.get(3).getAccomTypeCode());
        assertEquals("DLX", seasonDetails.get(3).getRoomClassName());
        assertEquals(2, seasonDetails.get(3).getRoomClassOrder().intValue());

        assertNoRateForSeasonDetails(seasonDetails.get(4), 5);
        assertEquals("STE", seasonDetails.get(4).getAccomTypeCode());
        assertEquals("STE", seasonDetails.get(4).getRoomClassName());
    }

    @Test
    @Tag("unqualifiedrate-flaky")
    public void shouldSearchSeasonsOrderByRatePlanName() {
        //GIVEN
        tenantCrudService().executeUpdateByNativeQuery("update Rate_Unqualified set Status_ID = 2");
        LocalDate currentDate = LocalDate.now();
        RateUnqualified rateUnqualified1 = UniqueRateUnqualified.createRateUnqualifiedByDateAndName(currentDate.toDate(), currentDate.plusDays(30).toDate(), "LV0");
        UniqueRateUnqualifiedDetails.createRateUnqualifiedDetailsForRateUnqualified(4, rateUnqualified1.getId(),
                currentDate.plusDays(10).toDate(), currentDate.plusDays(20).toDate(), 2f);
        UniqueRateUnqualifiedDetails.createRateUnqualifiedDetailsForRateUnqualified(6, rateUnqualified1.getId(),
                currentDate.plusDays(10).toDate(), currentDate.plusDays(20).toDate(), 2f);
        UniqueRateUnqualifiedDetails.createRateUnqualifiedDetailsForRateUnqualified(7, rateUnqualified1.getId(),
                currentDate.plusDays(10).toDate(), currentDate.plusDays(20).toDate(), 2f);
        RateUnqualified rateUnqualified2 = UniqueRateUnqualified.createRateUnqualifiedByDateAndName(currentDate.plusDays(31).toDate(), currentDate.plusDays(60).toDate(),
                "BAR1");
        UniqueRateUnqualifiedDetails.createRateUnqualifiedDetailsForRateUnqualified(4, rateUnqualified2.getId(),
                currentDate.plusDays(10).toDate(), currentDate.plusDays(20).toDate(), 2f);
        UniqueRateUnqualifiedDetails.createRateUnqualifiedDetailsForRateUnqualified(6, rateUnqualified2.getId(),
                currentDate.plusDays(10).toDate(), currentDate.plusDays(20).toDate(), 2f);
        UniqueRateUnqualifiedDetails.createRateUnqualifiedDetailsForRateUnqualified(7, rateUnqualified2.getId(),
                currentDate.plusDays(10).toDate(), currentDate.plusDays(20).toDate(), 2f);
        //WHEN
        List<RateHeader> rateHeaders = rateUnqualifiedComponent.searchForRatePlansBy(LocalDate.now(), LocalDate.now().plusDays(60), null);
        //THEN
        assertEquals(2, rateHeaders.size());
        assertRateHeader(rateUnqualified2, rateHeaders.get(0));
        assertRateHeader(rateUnqualified1, rateHeaders.get(1));
    }

    @Test
    public void testRemoveBarDecisionOverrideDetails() {
        Date today = DateUtil.getCurrentDateWithoutTime();
        AccomClass standardAccomClass = tenantCrudService().findByNamedQuerySingleResult(AccomClass.BY_CODE, QueryParameter.with("propertyId", TestProperty.H1.getId()).and("code", "DLX").parameters());
        QueryParameter params = QueryParameter.with("arrivalDate", today)
                .and("accomClassId", standardAccomClass.getId())
                .and("lengthOfStay", -1);
        RateUnqualified rateUnqualified = tenantCrudService().findByNamedQuerySingleResult(
                RateUnqualified.DEFAULT_BY_PROPERTY_ID,
                QueryParameter.with("propertyId", TestProperty.H1.getId()).parameters());
        DecisionBAROutput barOutput = tenantCrudService().findByNamedQuerySingleResult(
                DecisionBAROutput.BY_ARRIVALDATE_AND_ACCOMCLASSID_AND_LOS, params.parameters());

        final DecisionBAROutputOverride savedOverride = getDecisionBAROutputOverride(today, standardAccomClass, rateUnqualified, barOutput);

        rateUnqualifiedComponent.removeBarDecisionOverridesDetails(barOutput.getDecision().getId());

        List<DecisionBAROutputOverrideDetails> overrideDetails = tenantCrudService().findAll(DecisionBAROutputOverrideDetails.class).stream().filter(details -> details.getDecisionBAROutputOverride().equals(savedOverride)).collect(Collectors.toList());
        assertEquals(1, overrideDetails.size());
        assertNull(overrideDetails.get(0).getOldCeilingRateUnqualifiedValue());
        assertNull(overrideDetails.get(0).getOldFloorRateUnqualifiedValue());
        assertEquals(BigDecimal.ZERO, overrideDetails.get(0).getNewFloorRateUnqualifiedValue());
        assertEquals(BigDecimal.ZERO, overrideDetails.get(0).getNewFloorRateUnqualifiedValue());

    }

    @Test
    public void shouldFindApportionedAdrForAccomTypes() {
        AccomClass accomClass1 = UniqueAccomClassCreator.createUniqueAccomClassForPropertyID(PacmanWorkContextHelper.getPropertyId(), 0);
        AccomClass accomClass2 = UniqueAccomClassCreator.createUniqueAccomClassForPropertyID(PacmanWorkContextHelper.getPropertyId(), 0);

        AccomType accomType11 = UniqueAccomTypeCreator.createUniqueAccomTypeForAccomClass(PacmanWorkContextHelper.getPropertyId(), accomClass1, "AT11", "AT11", 100, "N");
        AccomType accomType12 = UniqueAccomTypeCreator.createUniqueAccomTypeForAccomClass(PacmanWorkContextHelper.getPropertyId(), accomClass1, "AT12", "AT12", 50, "N");
        AccomType accomType21 = UniqueAccomTypeCreator.createUniqueAccomTypeForAccomClass(PacmanWorkContextHelper.getPropertyId(), accomClass2, "AT21", "AT21", 70, "N");

        UniqueRateUnqualifiedDetails.createRateUnQualifiedDetailsForAccomTypeIDWithRates(accomType11.getId(), new Date(), DateUtil.addDaysToDate(new Date(), 10), "100", "10", "10", "10", "10", "10", "10");
        UniqueRateUnqualifiedDetails.createRateUnQualifiedDetailsForAccomTypeIDWithRates(accomType12.getId(), new Date(), DateUtil.addDaysToDate(new Date(), 10), "200", "10", "10", "10", "10", "10", "10");
        UniqueRateUnqualifiedDetails.createRateUnQualifiedDetailsForAccomTypeIDWithRates(accomType21.getId(), new Date(), DateUtil.addDaysToDate(new Date(), 10), "50", "10", "10", "10", "10", "10", "10");

        Map<Integer, BigDecimal> apportionedAdrForAccomClass = rateUnqualifiedComponent.getApportionedAdrForAccomTypes(Arrays.asList(accomType11.getId(), accomType12.getId(), accomType21.getId()));
        assertEquals(2, apportionedAdrForAccomClass.size());
        assertEquals(new BigDecimal("133.34"), apportionedAdrForAccomClass.get(accomClass1.getId()).setScale(2, BigDecimal.ROUND_UP));
        assertEquals(new BigDecimal("50.00"), apportionedAdrForAccomClass.get(accomClass2.getId()).setScale(2, BigDecimal.ROUND_UP));
    }

    @Test
    public void shouldFindApportionedAdrForSelectedNonZeroCapacityAccomTypes() {
        AccomClass accomClass1 = UniqueAccomClassCreator.createUniqueAccomClassForPropertyID(PacmanWorkContextHelper.getPropertyId(), 0);
        AccomClass accomClass2 = UniqueAccomClassCreator.createUniqueAccomClassForPropertyID(PacmanWorkContextHelper.getPropertyId(), 0);

        AccomType accomType11 = UniqueAccomTypeCreator.createUniqueAccomTypeForAccomClass(PacmanWorkContextHelper.getPropertyId(), accomClass1, "AT11", "AT11", 100, "N");
        AccomType accomType12 = UniqueAccomTypeCreator.createUniqueAccomTypeForAccomClass(PacmanWorkContextHelper.getPropertyId(), accomClass1, "AT12", "AT12", 50, "N");
        AccomType accomTypeWithZeroCapacity = UniqueAccomTypeCreator.createUniqueAccomTypeForAccomClass(PacmanWorkContextHelper.getPropertyId(), accomClass2, "sometestcode", "sometestcode", 0, "N");


        UniqueRateUnqualifiedDetails.createRateUnQualifiedDetailsForAccomTypeIDWithRates(accomType11.getId(), new Date(), DateUtil.addDaysToDate(new Date(), 10), "100", "10", "10", "10", "10", "10", "10");
        UniqueRateUnqualifiedDetails.createRateUnQualifiedDetailsForAccomTypeIDWithRates(accomType12.getId(), new Date(), DateUtil.addDaysToDate(new Date(), 10), "200", "10", "10", "10", "10", "10", "10");
        UniqueRateUnqualifiedDetails.createRateUnQualifiedDetailsForAccomTypeIDWithRates(accomTypeWithZeroCapacity.getId(), new Date(), DateUtil.addDaysToDate(new Date(), 10), "50", "10", "10", "10", "10", "10", "10");

        Map<Integer, BigDecimal> apportionedAdrForAccomClass = rateUnqualifiedComponent.getApportionedAdrForAccomTypes(Arrays.asList(accomType11.getId(), accomType12.getId(), accomTypeWithZeroCapacity.getId()));
        assertEquals(1, apportionedAdrForAccomClass.size());
        assertEquals(new BigDecimal("133.34"), apportionedAdrForAccomClass.get(accomClass1.getId()).setScale(2, BigDecimal.ROUND_UP));
    }

    @Test
    public void shouldFindApportionedAdrForNonZeroAccomTypesOnly() {
        AccomClass accomClass1 = UniqueAccomClassCreator.createUniqueAccomClassForPropertyID(PacmanWorkContextHelper.getPropertyId(), 0);
        AccomClass accomClass2 = UniqueAccomClassCreator.createUniqueAccomClassForPropertyID(PacmanWorkContextHelper.getPropertyId(), 0);

        AccomType accomType11 = UniqueAccomTypeCreator.createUniqueAccomTypeForAccomClass(PacmanWorkContextHelper.getPropertyId(), accomClass1, "AT11", "AT11", 100, "N");
        AccomType accomType12 = UniqueAccomTypeCreator.createUniqueAccomTypeForAccomClass(PacmanWorkContextHelper.getPropertyId(), accomClass1, "AT12", "AT12", 50, "N");
        AccomType accomTypeWithZeroCapacity = UniqueAccomTypeCreator.createUniqueAccomTypeForAccomClass(PacmanWorkContextHelper.getPropertyId(), accomClass2, "sometestcode", "sometestcode", 0, "N");

        UniqueRateUnqualifiedDetails.createRateUnQualifiedDetailsForAccomTypeIDWithRates(accomType11.getId(), new Date(), DateUtil.addDaysToDate(new Date(), 10), "100", "10", "10", "10", "10", "10", "10");
        UniqueRateUnqualifiedDetails.createRateUnQualifiedDetailsForAccomTypeIDWithRates(accomType12.getId(), new Date(), DateUtil.addDaysToDate(new Date(), 10), "200", "10", "10", "10", "10", "10", "10");
        UniqueRateUnqualifiedDetails.createRateUnQualifiedDetailsForAccomTypeIDWithRates(accomTypeWithZeroCapacity.getId(), new Date(), DateUtil.addDaysToDate(new Date(), 10), "50", "10", "10", "10", "10", "10", "10");

        Map<Integer, BigDecimal> apportionedAdrForAccomClass = rateUnqualifiedComponent.getApportionedAdr();


        assertEquals(new BigDecimal("133.34"), apportionedAdrForAccomClass.get(accomClass1.getId()).setScale(2, BigDecimal.ROUND_UP));
        assertTrue(null == apportionedAdrForAccomClass.get(accomClass2.getId()));
    }

    @Test
    public void shouldFindApportionedAdrForOnlyASubsetOfAccomTypes() {
        AccomClass accomClass1 = UniqueAccomClassCreator.createUniqueAccomClassForPropertyID(PacmanWorkContextHelper.getPropertyId(), 0);
        AccomClass accomClass2 = UniqueAccomClassCreator.createUniqueAccomClassForPropertyID(PacmanWorkContextHelper.getPropertyId(), 0);

        AccomType accomType11 = UniqueAccomTypeCreator.createUniqueAccomTypeForAccomClass(PacmanWorkContextHelper.getPropertyId(), accomClass1, "AT11", "AT11", 100, "N");
        AccomType accomType12 = UniqueAccomTypeCreator.createUniqueAccomTypeForAccomClass(PacmanWorkContextHelper.getPropertyId(), accomClass1, "AT12", "AT12", 50, "N");
        AccomType accomType21 = UniqueAccomTypeCreator.createUniqueAccomTypeForAccomClass(PacmanWorkContextHelper.getPropertyId(), accomClass2, "AT21", "AT21", 70, "N");

        UniqueRateUnqualifiedDetails.createRateUnQualifiedDetailsForAccomTypeIDWithRates(accomType11.getId(), new Date(), DateUtil.addDaysToDate(new Date(), 10), "100", "10", "10", "10", "10", "10", "10");
        UniqueRateUnqualifiedDetails.createRateUnQualifiedDetailsForAccomTypeIDWithRates(accomType12.getId(), new Date(), DateUtil.addDaysToDate(new Date(), 10), "200", "10", "10", "10", "10", "10", "10");
        UniqueRateUnqualifiedDetails.createRateUnQualifiedDetailsForAccomTypeIDWithRates(accomType21.getId(), new Date(), DateUtil.addDaysToDate(new Date(), 10), "50", "10", "10", "10", "10", "10", "10");

        Map<Integer, BigDecimal> apportionedAdrForAccomClass = rateUnqualifiedComponent.getApportionedAdrForAccomTypes(Arrays.asList(accomType11.getId(), accomType21.getId()));
        assertEquals(2, apportionedAdrForAccomClass.size());
        assertEquals(new BigDecimal("100.00"), apportionedAdrForAccomClass.get(accomClass1.getId()).setScale(2, BigDecimal.ROUND_UP));
        assertEquals(new BigDecimal("50.00"), apportionedAdrForAccomClass.get(accomClass2.getId()).setScale(2, BigDecimal.ROUND_UP));
    }

    @Test
    public void shouldFindApportionedAdrAccomTypesOfSameAccomClass() {
        AccomClass accomClass1 = UniqueAccomClassCreator.createUniqueAccomClassForPropertyID(PacmanWorkContextHelper.getPropertyId(), 0);
        AccomClass accomClass2 = UniqueAccomClassCreator.createUniqueAccomClassForPropertyID(PacmanWorkContextHelper.getPropertyId(), 0);

        AccomType accomType11 = UniqueAccomTypeCreator.createUniqueAccomTypeForAccomClass(PacmanWorkContextHelper.getPropertyId(), accomClass1, "AT11", "AT11", 100, "N");
        AccomType accomType12 = UniqueAccomTypeCreator.createUniqueAccomTypeForAccomClass(PacmanWorkContextHelper.getPropertyId(), accomClass1, "AT12", "AT12", 50, "N");
        AccomType accomType21 = UniqueAccomTypeCreator.createUniqueAccomTypeForAccomClass(PacmanWorkContextHelper.getPropertyId(), accomClass2, "AT21", "AT21", 70, "N");

        UniqueRateUnqualifiedDetails.createRateUnQualifiedDetailsForAccomTypeIDWithRates(accomType11.getId(), new Date(), DateUtil.addDaysToDate(new Date(), 10), "100", "10", "10", "10", "10", "10", "10");
        UniqueRateUnqualifiedDetails.createRateUnQualifiedDetailsForAccomTypeIDWithRates(accomType12.getId(), new Date(), DateUtil.addDaysToDate(new Date(), 10), "200", "10", "10", "10", "10", "10", "10");
        UniqueRateUnqualifiedDetails.createRateUnQualifiedDetailsForAccomTypeIDWithRates(accomType21.getId(), new Date(), DateUtil.addDaysToDate(new Date(), 10), "50", "10", "10", "10", "10", "10", "10");

        Map<Integer, BigDecimal> apportionedAdrForAccomClass = rateUnqualifiedComponent.getApportionedAdrForAccomTypes(Arrays.asList(accomType11.getId(), accomType12.getId()));
        assertEquals(1, apportionedAdrForAccomClass.size());
        assertEquals(new BigDecimal("133.34"), apportionedAdrForAccomClass.get(accomClass1.getId()).setScale(2, BigDecimal.ROUND_UP));
    }

    @Test
    void shouldNotReturnDiscontinuedRoomTypesForRateUnqualified() {
        RateUnqualifiedDetails rateUnqualifiedDetails = UniqueRateUnqualifiedDetails.createRateUnqualifiedDetails();

        AccomType accomType = tenantCrudService().find(AccomType.class, rateUnqualifiedDetails.getAccomTypeId());
        AccomType newRoomType = new AccomType();
        RateUnqualifiedDetails anotherRateUnqualifiedDetails = buildRateUnqualifiedDetailsForNewRoomType(rateUnqualifiedDetails, accomType, newRoomType);
        RateUnqualifiedAccomClass rateUQAccomClass = new RateUnqualifiedAccomClass();
        rateUQAccomClass.setRateUnqualifiedDetailsList(List.of(rateUnqualifiedDetails, anotherRateUnqualifiedDetails));
        rateUQAccomClass.setRateUnqualifiedId(rateUnqualifiedDetails.getRateUnqualifiedId());
        rateUQAccomClass.setAccomClassId(accomType.getAccomClass().getId());
        rateUQAccomClass.setCreateDate(new Timestamp((new Date()).getTime()));
        rateUQAccomClass.setLastUpdatedDate(new Timestamp((new Date()).getTime()));
        tenantCrudService().save(rateUQAccomClass);
        when(configParamsService.getParameterValue(IPConfigParamName.BAR_ALLOW_AVAILABLE_FOR_ARRIVAL.value())).thenReturn("true");
        when(configParamsService.getParameterValue(IPConfigParamName.BAR_ALLOW_MIN_MAX_LOSOVERRIDE.value())).thenReturn("true");

        List<RateUnqualifiedAccomClass> rateUQAccomClassList = rateUnqualifiedComponent
                .getRateUnqualfiedDetailsByAccomClass(rateUnqualifiedDetails.getRateUnqualifiedId(), accomType.getAccomClass().getId());
        assertEquals(2, rateUQAccomClassList.get(0).getRateUnqualifiedDetailsList().size());

        tenantCrudService().executeUpdateByNativeQuery("update Accom_Type set Display_Status_ID=2 where Accom_Type_ID=" + newRoomType.getId());

        rateUQAccomClassList = rateUnqualifiedComponent.getRateUnqualfiedDetailsByAccomClass(rateUnqualifiedDetails.getRateUnqualifiedId(),
                accomType.getAccomClass().getId());
        assertEquals(1, rateUQAccomClassList.get(0).getRateUnqualifiedDetailsList().size());
    }

    private RateUnqualifiedDetails buildRateUnqualifiedDetailsForNewRoomType(RateUnqualifiedDetails rateUQDetails, AccomType accomType, AccomType newAt) {
        newAt.setAccomClass(accomType.getAccomClass());
        newAt.setName("NewAT");
        newAt.setAccomTypeCode("NewAT");
        newAt.setPropertyId(5);
        newAt.setSystemDefault(0);
        newAt.setDisplayStatusId(1);
        newAt.setStatusId(1);
        tenantCrudService().save(newAt);
        RateUnqualifiedDetails anotherRateUnqualified =
                UniqueRateUnqualifiedDetails.createFutureRateUnQualifiedDetailsForAccomTypeID(newAt.getId(), DateUtil.getCurrentDate());
        anotherRateUnqualified.setRateUnqualifiedId(rateUQDetails.getRateUnqualifiedId());
        tenantCrudService().save(anotherRateUnqualified);
        return anotherRateUnqualified;
    }

    @Test
    public void shouldFindApportionedAdrForAccomTypesWithMultipleRates() {
        AccomClass accomClass1 = UniqueAccomClassCreator.createUniqueAccomClassForPropertyID(PacmanWorkContextHelper.getPropertyId(), 0);
        AccomClass accomClass2 = UniqueAccomClassCreator.createUniqueAccomClassForPropertyID(PacmanWorkContextHelper.getPropertyId(), 0);

        AccomType accomType11 = UniqueAccomTypeCreator.createUniqueAccomTypeForAccomClass(PacmanWorkContextHelper.getPropertyId(), accomClass1, "AT11", "AT11", 100, "N");
        AccomType accomType12 = UniqueAccomTypeCreator.createUniqueAccomTypeForAccomClass(PacmanWorkContextHelper.getPropertyId(), accomClass1, "AT12", "AT12", 50, "N");
        AccomType accomType21 = UniqueAccomTypeCreator.createUniqueAccomTypeForAccomClass(PacmanWorkContextHelper.getPropertyId(), accomClass2, "AT21", "AT21", 70, "N");

        UniqueRateUnqualifiedDetails.createRateUnQualifiedDetailsForAccomTypeIDWithRates(accomType11.getId(), new Date(), DateUtil.addDaysToDate(new Date(), 10), "100", "10", "10", "10", "10", "10", "10");
        UniqueRateUnqualifiedDetails.createRateUnQualifiedDetailsForAccomTypeIDWithRates(accomType11.getId(), DateUtil.addDaysToDate(new Date(), 20), DateUtil.addDaysToDate(new Date(), 30), "40", "10", "10", "10", "10", "10", "10");
        UniqueRateUnqualifiedDetails.createRateUnQualifiedDetailsForAccomTypeIDWithRates(accomType12.getId(), new Date(), DateUtil.addDaysToDate(new Date(), 10), "200", "10", "10", "10", "10", "10", "10");
        UniqueRateUnqualifiedDetails.createRateUnQualifiedDetailsForAccomTypeIDWithRates(accomType21.getId(), new Date(), DateUtil.addDaysToDate(new Date(), 10), "50", "10", "10", "10", "10", "10", "10");

        Map<Integer, BigDecimal> apportionedAdrForAccomClass = rateUnqualifiedComponent.getApportionedAdrForAccomTypes(Arrays.asList(accomType11.getId(), accomType12.getId(), accomType21.getId()));
        assertEquals(2, apportionedAdrForAccomClass.size());
        assertEquals(new BigDecimal("93.34"), apportionedAdrForAccomClass.get(accomClass1.getId()).setScale(2, BigDecimal.ROUND_UP));
        assertEquals(new BigDecimal("50.00"), apportionedAdrForAccomClass.get(accomClass2.getId()).setScale(2, BigDecimal.ROUND_UP));
    }

    private DecisionBAROutputOverride getDecisionBAROutputOverride(Date today, AccomClass standardAccomClass, RateUnqualified rateUnqualified, DecisionBAROutput barOutput) {
        DecisionBAROutputOverride override = new DecisionBAROutputOverride();
        override.setAccomClassId(standardAccomClass.getId());
        override.setPropertyId(TestProperty.H1.getId());
        override.setArrivalDate(today);
        override.setLengthOfStay(-1);
        override.setNewOverride(Constants.BARDECISIONOVERRIDE_FLOOR_AND_CEILING);
        override.setNewRateUnqualified(rateUnqualified);
        override.setNewFloorRateUnqualified(rateUnqualified);
        override.setNewCeilingRateUnqualified(rateUnqualified);
        override.setOldOverride(Constants.NONE);
        override.setOldRateUnqualified(rateUnqualified);
        override.setDecision(barOutput.getDecision());
        override.setCreateDate(today);
        override.setUserId(1);
        return tenantCrudService().save(override);
    }

    private void assertSeasonDetails(RateUnqualifiedDetails expectedRateUnqualifiedDetail, SeasonDetail actualSeasonDetail) {
        assertEquals(expectedRateUnqualifiedDetail.getAccomTypeId(), actualSeasonDetail.getAccomTypeId(), "Expected Accom Type Id did not match");
        assertEquals(expectedRateUnqualifiedDetail.getSunday(), actualSeasonDetail.getSunday(), "Expected Sunday value did not match");
        assertEquals(expectedRateUnqualifiedDetail.getMonday(), actualSeasonDetail.getMonday(), "Expected Monday value did not match");
        assertEquals(expectedRateUnqualifiedDetail.getTuesday(), actualSeasonDetail.getTuesday(), "Expected Tuesday value did not match");
        assertEquals(expectedRateUnqualifiedDetail.getWednesday(), actualSeasonDetail.getWednesday(), "Expected Wednesday value did not match");
        assertEquals(expectedRateUnqualifiedDetail.getThursday(), actualSeasonDetail.getThursday(), "Expected Thursday value did not match");
        assertEquals(expectedRateUnqualifiedDetail.getFriday(), actualSeasonDetail.getFriday(), "Expected Friday value did not match");
        assertEquals(expectedRateUnqualifiedDetail.getSaturday(), actualSeasonDetail.getSaturday(), "Expected Saturday value did not match");
    }

    private void assertRateHeader(RateUnqualified expectedRateUnqualified, RateHeader actualRateHeader) {
        assertEquals(5, actualRateHeader.getPropertyId().intValue());
        assertEquals(expectedRateUnqualified.getName(), actualRateHeader.getName());
        assertEquals(expectedRateUnqualified.getStartDate(), actualRateHeader.getStartDate().toDate());
        assertEquals(expectedRateUnqualified.getEndDate(), actualRateHeader.getEndDate().toDate());
    }

    private void assertSeason(RateUnqualifiedDetails rateUnqualifiedDetail, Season season) {
        assertEquals(rateUnqualifiedDetail.getStartDate(), season.getStartDate().toDate());
        assertEquals(rateUnqualifiedDetail.getEndDate(), season.getEndDate().toDate());
    }

}