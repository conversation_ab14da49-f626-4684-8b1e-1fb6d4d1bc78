package com.ideas.tetris.pacman.services.lastgooddecisions;

import com.ideas.g3.test.AbstractG3JupiterTest;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.Decision;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.UniqueDecisionCreator;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.dao.UniqueFileMetadataCreator;
import com.ideas.tetris.pacman.services.decision.DecisionService;
import com.ideas.tetris.pacman.services.inventorylimit.InventoryLimitDecisionService;
import com.ideas.tetris.pacman.services.lastgooddecisions.dto.MassLastGoodDecisionsDeliveryJobParameters;
import com.ideas.tetris.pacman.services.meetingpackagepricing.service.MeetingPackageDecisionService;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.entity.DecisionDeliveryType;
import com.ideas.tetris.platform.common.entity.HtngPartner;
import com.ideas.tetris.platform.common.job.JobName;
import com.ideas.tetris.platform.common.job.JobParameterKey;
import com.ideas.tetris.platform.common.job.JobServiceLocal;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import com.ideas.tetris.platform.services.daoandentities.entity.Property;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.condition.DisabledOnOs;
import org.junit.jupiter.api.condition.OS;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.math.BigInteger;
import java.text.ParseException;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.ideas.tetris.pacman.services.lastgooddecisions.LastGoodDecisionsService.DECISION_TYPES_NOT_IMPLEMENTED_IN_LKGDD_JOB;
import static com.ideas.tetris.platform.common.entity.DecisionDeliveryType.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyMap;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@DisabledOnOs(OS.LINUX)
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class LastGoodDecisionsServiceTest extends AbstractG3JupiterTest {

    @InjectMocks
    LastGoodDecisionsService lastGoodDecisionsService;

    @Mock
    LastGoodDecisionsMinMaxLOSService lastGoodDecisionsMinMaxLOSService;

    @Mock
    LastGoodDecisionsRateOfDayService lastGoodDecisionsRateOfDayService;

    @Mock
    LastGoodDecisionsFPLOSByRankService lastGoodDecisionsFPLOSByRankService;

    @Mock
    LastGoodDecisionsQualifiedFPLOSService lastGoodDecisionsQualifiedFPLOSService;

    @Mock
    LastGoodDecisionsAccomOverbookingService lastGoodDecisionsAccomOverbookingService;

    @Mock
    LastGoodDecisionFplosByHierarchyService lastGoodDecisionFplosByHierarchyService;

    @Mock
    LastGoodDecisionsOverbookingService lastGoodDecisionsOverbookingService;

    @Mock
    LastGoodDecisionsDailyBarService lastGoodDecisionsDailyBarService;

    @Mock
    LastGoodDecisionsLRVService lastGoodDecisionsLRVService;

    @Mock
    LastGoodDecisionLrvAtAccomTypeService lastGoodDecisionLrvAtAccomTypeService;

    @Mock
    LastGoodDecisionLraFplosService lastGoodDecisionLraFplosService;

    @Mock
    LastGoodDecisionLraMinlosService lastGoodDecisionLraMinlosService;

    @Mock
    InventoryLimitDecisionService inventoryLimitDecisionService;

    @Mock
    MeetingPackageDecisionService meetingPackageDecisionService;

    @Mock
    PacmanConfigParamsService pacmanConfigParamsService;

    @Mock
    protected JobServiceLocal jobService;

    @Mock
    DecisionService decisionService;

    @Mock
    protected CrudService crudService;

    public static final String LIST_OF_NEW_DECISIONS_NEEDS_TO_BE_ENABLED_FOR_SENDING_LAST_KNOWN_GOOD_DECISION_IN_LAST_GOOD_DECISIONS_DELIVERY_JOB = "ERROR:You have added new decision in config parameters. List of new decisions needs to be enabled for sending Last Known Good Decision in 'LastGoodDecisionsDeliveryJob': ";

    public static final String LIST_OF_NEW_OUTBOUNDS_NEEDS_TO_BE_ENABLED_FOR_SENDING_LAST_KNOWN_GOOD_DECISION_IN_LAST_GOOD_DECISIONS_DELIVERY_JOB = "ERROR:You have added new outbounds in config parameters. List of new outbounds needs to be enabled for sending Last Known Good Decision in 'LastGoodDecisionsDeliveryJob': ";

    public static final List<String> LIST_OF_ALL_AVAILABLE_DECISIONS_IN_CONFIG_PARAMETERS = Arrays.asList("agilerates", "hotelclosetoarrival", "barbylos", "barbylosatroomclass", "barbylosatroomtype", "barbylosbyroomtype", "barfplosbyhierarchy", "barfplosbyhierarchybyroomclass", "barfplosbyhotel", "barfplosbyrank", "barfplosbyroomclass", "barfplosbyroomtype", "dailybar", "dailybarbyratecode", "dbar", "fplos", "fplosbyratecategory", "fplosbyratecategorybyroomtype", "fplosbyratecode", "fplosbyratecodebyroomtype", "hoteloverbooking", "lastroomvaluebyroomclass", "lracontrolfplos", "lracontrolminlos", "lrvatroomclass", "lrvatroomtype", "minimumlengthofstaybyratecategory", "minimumlengthofstaybyratecategorybyroomtype", "minimumlengthofstaybyratecode", "minimumlengthofstaybyratecodebyroomtype", "minmaxlosbyratecode", "roomtypeoverbooking", "manualrestrictions", "profitadjustment", "inventoryLimit");

    public static final List<String> LIST_OF_ALL_AVAILABLE_OUTBOUNDS_IN_CONFIG_PARAMETERS = Arrays.asList("rds", "hilstar", "rezview", "opera", "pcrs", "myfidelio", "ratchet", "ors", "tars", "apaleo", "sihot", "ohd", "ocp", "decisiondelivery1");

    private static final String QUERY_TO_FETCH_ALL_DECISIONS_LIST = "SELECT name from Config_Parameter where name like 'pacman.%.uploadType'";

    public static final String QUERY_TO_FETCH_FUNCTION_DEFINITION = "select definition from sys.sql_modules where object_name(object_id) like '%s'";

    public static final String QUERY_TO_FETCH_COLUMNS_NAME_LIST_OF_TABLE = "SELECT distinct name=STUFF((SELECT ',' + CAST(name AS VARCHAR(MAX)) " + "FROM sys.columns t2 where object_id=OBJECT_ID('dbo.%s') FOR XML PATH('')),1,1,'') FROM sys.columns t1";

    public static final String COLUMNS_MODIFIED_IN_TABLES_UPDATE_LAST_GOOD_DECISIONS_DELIVERY_JOB = "Columns Modified in table %s. Please modify the " + "merge query of this decision in '%s' and then correct this test case, Also refer to 'LastGoodDecisionsDeliveryJob' if any changes required";

    public static final String ERROR_MESSAGE_WHEN_DIFFERENTIAL_QUERY_MODIFIED = "ERROR: You made changes in differential " + " query of '%s' decision, which also needs to be done in 'LastGoodDecisionsDeliveryJob'." + " please modify merge query of this decision in '%s' and then correct the differential query of this test case";

    @Test
    public void createAsOfMinMaxLosDecisions() {
        lastGoodDecisionsService.createAsOfMinMaxLosDecisions(1);
        verify(lastGoodDecisionsMinMaxLOSService).createAsOfMinMaxLosDecisions(1);
    }

    @Test
    public void createAsOfDecisionsForRateOfDay() {
        lastGoodDecisionsService.crudService = tenantCrudService();
        Decision decision = new Decision();
        decision.setId(1);
        decision.setDecisionTypeId(1);
        when(decisionService.createDecisionForLastGoodDecisions(Constants.DECISION_TYPE_BDE)).thenReturn(decision);
        lastGoodDecisionsService.createAsOfDecisionsForRateOfDay(1, 1);
        verify(lastGoodDecisionsRateOfDayService).createAsOfDecisionsForRateOfDay(1, 1);
    }

    @Test
    public void createAsOfDecisionsForBARFpLOSByRank() {
        lastGoodDecisionsService.createAsOfDecisionsForBARFplosByRank(1);
        verify(lastGoodDecisionsFPLOSByRankService).createLastGoodDecisions(1);
    }

    @Test
    public void createAsOfDecisionsForQualifiedFplos() {
        lastGoodDecisionsService.createAsOfDecisionsForQualifiedFplos(1);
        verify(lastGoodDecisionsQualifiedFPLOSService).createLastGoodDecisions(1);
    }

    @Test
    public void sendLastKnownGoodDecisions() {
        lastGoodDecisionsService.sendLastKnownGoodDecisions(1);
        verify(jobService).startJob(eq(JobName.LastGoodDecisionsDeliveryJob), anyMap());
    }

    @Test
    public void shouldReturnLatestRunProcessTypeId() {
        lastGoodDecisionsService.crudService = tenantCrudService();
        assertTrue(lastGoodDecisionsService.getLatestDecisionTypeId() > 0);
    }

    @Test
    public void shouldFetchLastGoodDecisions() {
        lastGoodDecisionsService.crudService = tenantCrudService();
        final List<Decision> lastFiveGoodDecisions = lastGoodDecisionsService.getLastGoodDecisions();
        assertTrue(lastFiveGoodDecisions.size() <= 10);
    }

    @Test
    public void createAsOfDecisionsForAccomTypeOverbooking() {
        lastGoodDecisionsService.createAsOfDecisionsForAccomTypeOverbooking(1, 1);
        verify(lastGoodDecisionsAccomOverbookingService).createLastGoodDecisions(1, 1);
    }

    @Test
    public void createAsOfDecisionsForBarFplosByHierarchy() {
        final Set<String> decisionParameters = Stream.of("BarFplosByHierarchyByRoomClass", "BarFplosByHierarchy").collect(Collectors.toSet());
        lastGoodDecisionsService.createAsOfDecisionsForBARFplosByHierarchy(1, decisionParameters);
        verify(lastGoodDecisionFplosByHierarchyService).createLastGoodDecisions(1, decisionParameters);
    }

    @Test
    public void createAsOfDecisionsForPropertyOverbooking() {
        lastGoodDecisionsService.createAsOfDecisionsForOverbooking(1, 2);
        verify(lastGoodDecisionsOverbookingService).createLastGoodDecisions(1, 2);
    }

    @Test
    public void createDailyBarDecisions() {
        lastGoodDecisionsService.createDailyBarDecisions(1);
        verify(lastGoodDecisionsDailyBarService).createLastGoodDecisions(1);
    }

    @Test
    public void createLRVDecisions() {
        lastGoodDecisionsService.createAsOfDecisionsForLRV(1, 23);
        verify(lastGoodDecisionsLRVService).createLastGoodDecisions(1, 23);
    }

    @Test
    public void createLRVAtRoomTypeDecisions() {
        lastGoodDecisionsService.createAsOfDecisionsForLRVAtAccomType(1, 23);
        verify(lastGoodDecisionLrvAtAccomTypeService).createLastGoodDecisions(1, 23);
    }

    @Test
    public void createLraFplosDecisions() {
        lastGoodDecisionsService.createAsOfDecisionsForLraFplos(1);
        verify(lastGoodDecisionLraFplosService).createLastGoodDecisions(1);
    }

    @Test
    public void createLraMinlosDecisions() {
        lastGoodDecisionsService.createAsOfDecisionsForLraMinlos(1);
        verify(lastGoodDecisionLraMinlosService).createLastGoodDecisions(1);
    }

    @Test
    void shouldCreateGroupProductInventoryLimitLastGoodDecisions(){
        Integer lastKnownGoodDecisionId = 89;
        Integer newDecisionId = 94;
        lastGoodDecisionsService.createAsOfDecisionsForGroupProductInventoryLimit(lastKnownGoodDecisionId, newDecisionId);
        verify(inventoryLimitDecisionService).createLastGoodDecisions(lastKnownGoodDecisionId, newDecisionId);
    }

    @Test
    void shouldCreateMeetingPackagePricingLastGoodDecisions() {
        Integer lastKnownGoodDecisionId = 89;
        Integer newDecisionId = 94;
        lastGoodDecisionsService.createAsOfDecisionsForMeetingPackagePricing(lastKnownGoodDecisionId, newDecisionId);
        verify(meetingPackageDecisionService).createLastGoodDecisions(lastKnownGoodDecisionId, newDecisionId);
    }


    @Test
    public void check_If_Any_New_Decisions_Got_Added_In_G3_And_Not_Considered_For_Sending_Last_Known_Good_Decision_Delivery_Job() {
        final Set<String> dbConfigParameters = getAllDBConfiguredDecisionList();
        final List<String> listOfDecisionsInConfigParameters = new ArrayList<>();
        listOfDecisionsInConfigParameters.addAll(LIST_OF_ALL_AVAILABLE_DECISIONS_IN_CONFIG_PARAMETERS);
        final List<String> newlyAddedDecisions = dbConfigParameters.stream().filter(value -> !listOfDecisionsInConfigParameters.stream().anyMatch(match -> match.equalsIgnoreCase(value))).collect(Collectors.toList());
        assertEquals(0, newlyAddedDecisions.size(), "Decisions Name list is not matching with decisions from Config Parameters Table. Please add those newly added decisions in LastGoodDecisionsDeliveryJob as well:" + newlyAddedDecisions);
        final Object listOfNewDecisions = dbConfigParameters.stream().filter(obj -> !listOfDecisionsInConfigParameters.contains(obj)).collect(Collectors.toList());
        assertEquals(listOfDecisionsInConfigParameters.size(), dbConfigParameters.size(), LIST_OF_NEW_DECISIONS_NEEDS_TO_BE_ENABLED_FOR_SENDING_LAST_KNOWN_GOOD_DECISION_IN_LAST_GOOD_DECISIONS_DELIVERY_JOB + listOfNewDecisions);
    }

    private Set<String> getAllDBConfiguredDecisionList() {
        Set decisionNames = new HashSet();
        final List<String> decisionsList = globalCrudService().findByNativeQuery(QUERY_TO_FETCH_ALL_DECISIONS_LIST);
        decisionsList.stream().forEach(value -> {
            List<String> values = Arrays.asList(value.split("\\."));
            decisionNames.add(values.get(values.size() - 2).toLowerCase());
        });
        return decisionNames;
    }

    @Test
    public void check_If_Any_New_ExternalSystem_Or_Outbound_Got_Added_In_G3_And_Not_Considered_For_Sending_Last_Known_Good_Decision_Delivery_Job() {
        List<String> outboundListExceptHtngPartners = getListOfOutboundFromDatabase();
        final List<String> listOfOutboundsInConfigParameters = new ArrayList<>();
        listOfOutboundsInConfigParameters.addAll(LIST_OF_ALL_AVAILABLE_OUTBOUNDS_IN_CONFIG_PARAMETERS);
        // Omitting HtngPartner names as those are going increase
        outboundListExceptHtngPartners.removeAll(Stream.of(HtngPartner.valuesConfiguredOutboundsAware()).map(value -> value.getName().toLowerCase()).collect(Collectors.toList()));
        // Explicitly adding Tars Outbound as it got removed due its also mentioned in the HtngParnter Enum
        outboundListExceptHtngPartners.add("tars");
        final List<String> newlyAddedOutbounds = outboundListExceptHtngPartners.stream().filter(value -> !listOfOutboundsInConfigParameters.stream().anyMatch(match -> match.equalsIgnoreCase(value))).collect(Collectors.toList());
        assertEquals(0, newlyAddedOutbounds.size(), "Outbounds List is not matching with outbounds from Config Parameters Table. Please add those newly added outbounds in LastGoodDecisionsDeliveryJob as well:" + newlyAddedOutbounds);
        Stream.of(HtngPartner.valuesConfiguredOutboundsAware()).filter(htngPartner -> !outboundListExceptHtngPartners.contains(htngPartner.getName().toLowerCase())).collect(Collectors.toSet());
        final Object listOfNewOutbounds = outboundListExceptHtngPartners.stream().filter(obj -> !LIST_OF_ALL_AVAILABLE_DECISIONS_IN_CONFIG_PARAMETERS.contains(obj)).collect(Collectors.toList());
        assertEquals(LIST_OF_ALL_AVAILABLE_OUTBOUNDS_IN_CONFIG_PARAMETERS.size(), outboundListExceptHtngPartners.size(), LIST_OF_NEW_OUTBOUNDS_NEEDS_TO_BE_ENABLED_FOR_SENDING_LAST_KNOWN_GOOD_DECISION_IN_LAST_GOOD_DECISIONS_DELIVERY_JOB + listOfNewOutbounds);
    }

    private List<String> getListOfOutboundFromDatabase() {
        final Set<String> dbConfigParameters = getAllDBConfiguredDecisionList();
        final List<Object> decisionsList = globalCrudService().findByNativeQuery(QUERY_TO_FETCH_ALL_DECISIONS_LIST);
        final Set<String> collect = decisionsList.stream().map(value -> ((String) value).split("\\.")[2]).map(String::toLowerCase).collect(Collectors.toSet());
        return collect.stream().filter(value -> !dbConfigParameters.contains(value)).collect(Collectors.toList());
    }

    @Test
    public void startMassLastKnownGoodDecisionsDeliveryJob() {
        MassLastGoodDecisionsDeliveryJobParameters massLastGoodDecisionsDeliveryJobParameters = new MassLastGoodDecisionsDeliveryJobParameters(Arrays.asList(5, 6), "2018-03-22 07:17:45.920", 10, 10, null, null);
        lastGoodDecisionsService.startMassLastKnownGoodDecisionsDeliveryJob(massLastGoodDecisionsDeliveryJobParameters);
        Map<String, Object> jobParameters = new HashMap<>();
        jobParameters.put(JobParameterKey.TENTATIVE_ISSUE_START_DATETIME, massLastGoodDecisionsDeliveryJobParameters.getTentativeIssueStartTime());
        jobParameters.put(JobParameterKey.INCOMING_SERIALIZABLE, massLastGoodDecisionsDeliveryJobParameters.getPropertyIds());
        jobParameters.put(JobParameterKey.BATCH_SIZE, massLastGoodDecisionsDeliveryJobParameters.getBatchSize());
        jobParameters.put(JobParameterKey.TIME_INTERVAL, massLastGoodDecisionsDeliveryJobParameters.getTimeInterval());
        verify(jobService).startGuaranteedNewInstance(JobName.MassLastGoodDecisionsDeliveryJob, jobParameters);
    }

    @Test
    public void validateIfPropertyEligibleForRunningLastGoodDecisionJobForTimeStamp() throws ParseException {
        assertTrue(lastGoodDecisionsService.validateIfPropertyEligibleForRunningLastGoodDecisionJobForTimeStamp(DateUtil.parseDate("2299-03-22 07:17:45.920", DateUtil.DATE_TIME_FORMAT)));
    }

    @Test
    public void startLastKnownGoodDecisionsDeliveryJob() throws ParseException {
        Property property = globalCrudService().find(Property.class, Integer.valueOf(5));
        final Map<String, Object> parameters = QueryParameter.with("tentativeIssueStartTime", DateUtil.parseDate("2299-03-22 07:17:45.920", DateUtil.DATE_TIME_FORMAT)).parameters();
        when(crudService.findByNamedQuery(Decision.GET_LATEST_DECISIONS_AFTER_GIVEN_DATE_TIME, parameters)).thenReturn(new ArrayList<>());
        final ArrayList<Object> objects = new ArrayList<>();
        BigInteger inte = new BigInteger("56");
        Object[] obj = new Object[3];
        obj[2] = inte;
        objects.add(obj);
        when(crudService.findByNamedQuery(Decision.GET_LATEST_DECISIONS_FOR_GIVEN_DATE_TIME, parameters, 1)).thenReturn(objects);
        lastGoodDecisionsService.startLastKnownGoodDecisionsDeliveryJob(property, "2299-03-22 07:17:45.920", null, null);
        verify(jobService).startJob(eq(JobName.LastGoodDecisionsDeliveryJob), anyMap());
    }

    @Test
    public void testFetchLatestDecisionIdForTimeStamp() {
        Date timeStamp = prepareDecisionsData();
        inject(lastGoodDecisionsService, "crudService", tenantCrudService());
        final BigInteger decisionId = lastGoodDecisionsService.fetchLatestDecisionIdForTimeStamp(timeStamp);
        assertNotNull(decisionId);
    }

    @Test
    public void shouldFetchInboundConfigurations() {
        lastGoodDecisionsService.globalCrudService = globalCrudService();
        final List<ConfigParameterInbound> configParameterInbounds = lastGoodDecisionsService.fetchInboundConfigurations();
        assertNotNull(configParameterInbounds);
    }

    @Test
    void shouldReturnAllDecisionTypes() {
        List<DecisionDeliveryType> allDecisionType = List.of(DecisionDeliveryType.values());
        Set<DecisionDeliveryType> decisionTypes = lastGoodDecisionsService.fetchAllDecisionDeliveryTypes();
        assertEquals(allDecisionType.size(),decisionTypes.size());
        assertTrue(decisionTypes.containsAll(allDecisionType));
    }

    @Test
    void shouldReturnAllNotImplementedDecisionTypes() {
        Set<DecisionDeliveryType> notImplementedDecisionType = DECISION_TYPES_NOT_IMPLEMENTED_IN_LKGDD_JOB;
        Set<DecisionDeliveryType> decisionTypes = lastGoodDecisionsService.fetchAllNotImplementedDecisionTypes();
        assertEquals(3, notImplementedDecisionType.size());
        assertTrue(decisionTypes.contains(BAR_BY_FPLOS));
        assertTrue(decisionTypes.contains(MANUAL_RESTRICTIONS));
        assertTrue(decisionTypes.contains(HOTEL_CLOSE_TO_ARRIVAL));
    }

    @Test
    void shouldReturnAllEligibleDecisionTypes() {
        Set<DecisionDeliveryType> decisionTypes = lastGoodDecisionsService.fetchAllEligibleDecisionDeliveryTypes();
        assertEquals(32, decisionTypes.size());
    }

    private Date prepareDecisionsData() {
        final int propertyId = 5;
        final Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.MILLISECOND, 0);
        final Date businessDate = calendar.getTime();
        final Date businessDate1 = DateUtil.addDaysToDate(businessDate, -4);
        UniqueDecisionCreator.createDecisionFor(propertyId, businessDate1, 1, businessDate1);
        UniqueFileMetadataCreator.createFileMetadata(businessDate1);
        final Date businessDate2 = DateUtil.addDaysToDate(businessDate, -3);
        UniqueDecisionCreator.createDecisionFor(propertyId, businessDate2, 1, businessDate2);
        UniqueFileMetadataCreator.createFileMetadata(businessDate2);
        final Date businessDate3 = DateUtil.addDaysToDate(businessDate, -2);
        UniqueDecisionCreator.createDecisionFor(propertyId, businessDate3, 1, businessDate3);
        UniqueFileMetadataCreator.createFileMetadata(businessDate3);
        LocalDateTime localDateTime = LocalDateTime.now().plusMinutes(1);
        return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }
}
