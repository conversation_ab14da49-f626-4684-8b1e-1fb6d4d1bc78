package com.ideas.tetris.pacman.services.limiteddatabuild.cloneProjectionBuilder;

import com.ideas.g3.data.TestClient;
import com.ideas.g3.data.TestProperty;
import com.ideas.g3.test.AbstractG3JupiterTest;
import com.ideas.tetris.pacman.common.configparams.*;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.limiteddatabuild.ProjectionDataService;
import com.ideas.tetris.pacman.services.limiteddatabuild.cloneProjectionBuilder.dto.*;
import com.ideas.tetris.pacman.services.limiteddatabuild.entity.cloneProjections.LDBMSMonthlyRatio;
import com.ideas.tetris.pacman.services.limiteddatabuild.entity.cloneProjections.LDBMonthlyRatio;
import com.ideas.tetris.pacman.services.limiteddatabuild.entity.cloneProjections.LDBTotalRoomsSoldRevenuePerMonth;
import com.ideas.tetris.pacman.services.limiteddatabuild.entity.cloneProjections.LDBTotalRoomsSoldRevenuePerMonthMktSeg;
import com.ideas.tetris.pacman.services.property.PropertyService;
import com.ideas.tetris.pacman.util.CollectionUtils;
import com.ideas.tetris.pacman.util.JavaLocalDateRange;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import com.ideas.tetris.platform.services.daoandentities.entity.Client;
import com.ideas.tetris.platform.services.daoandentities.entity.Property;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.Month;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.ideas.tetris.pacman.services.limiteddatabuild.cloneProjectionBuilder.CloneProjectionBuilderRepositoryTest.INSERT_INTO_IP_CFG;
import static com.ideas.tetris.pacman.services.limiteddatabuild.cloneProjectionBuilder.CloneProjectionBuilderService.*;
import static com.ideas.tetris.pacman.services.property.PropertyService.STANDARD_BUILD_TYPE_KEY;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

public class CloneProjectionBuilderServiceTest  extends AbstractG3JupiterTest {

    @InjectMocks
    CloneProjectionBuilderService cloneProjectionBuilderService;

    @InjectMocks
    CloneProjectionBuilderRepository repository;

    @Mock
    CloneProjectionBuilderRepository mockRepository;
    @Mock
    private PacmanConfigParamsService configParamsService;
    @Mock
    private ProjectionDataService projectionDataService;

    @Mock
    private DateService dateService;

    @Mock
    PropertyService propertyService;

    private final String MS1="MS1";
    private final String MS2="MS2";

    @BeforeEach
    public void setUp() {
        repository.setMultiPropertyCrudService(multiPropertyCrudService());
        cloneProjectionBuilderService.setCloneProjectionBuilderRepository(repository);
        PacmanWorkContextHelper.setClientCode("BSTN");
    }


    private Integer BUSINESS_TYPE_GROUP_ID = 1;
    private Integer BUSINESS_TYPE_TRANSIENT_ID = 2;

    @Test
    public void testFilterOutMaskedHistoryPropertiesWhenNoMaskedPeriodPresent() {
        LocalDate startDate = LocalDate.of(2024,1,1);
        List<Integer> validProperties = cloneProjectionBuilderService.filterOutMaskedHistoryProperties(getTestProperties(),startDate,startDate.plusDays(365));
        assertEquals(2,validProperties.size());
        assertEquals(TestProperty.H1.getId(),validProperties.get(0));
        assertEquals(TestProperty.H2.getId(),validProperties.get(1));
    }

    @Test
    public void testFilterOutMaskedHistoryPropertiesWhenMaskedPeriodPresentForSingleProperty() {
        when(configParamsService.getParameterValue(PreProductionConfigParamName.MASK_DAYS_THRESHOLD)).thenReturn(30);
        LocalDate startDate = LocalDate.of(2024,1,1);
        populateIPCFGWithActiveDates(TestProperty.H2.getId(),startDate,startDate.plusDays(37));
        List<Integer> validProperties = cloneProjectionBuilderService.filterOutMaskedHistoryProperties(getTestProperties(),startDate,startDate.plusDays(365));
        assertEquals(1,validProperties.size());
        assertEquals(TestProperty.H1.getId(),validProperties.get(0));
    }

    @Test
    public void testFilterOutMaskedHistoryPropertiesWhenMaskedPeriodPresentForAllProperty() {
        when(configParamsService.getParameterValue(PreProductionConfigParamName.MASK_DAYS_THRESHOLD)).thenReturn(30);
        LocalDate startDate = LocalDate.of(2024,1,1);
        populateIPCFGWithActiveDates(TestProperty.H1.getId(),startDate,startDate.plusDays(37));
        populateIPCFGWithActiveDates(TestProperty.H2.getId(),startDate,startDate.plusDays(7));
        List<Integer> validProperties = cloneProjectionBuilderService.filterOutMaskedHistoryProperties(getTestProperties(),startDate,startDate.plusDays(365));
        assertEquals(1,validProperties.size());
        assertEquals(TestProperty.H2.getId(),validProperties.get(0));
    }

    @Test
    void testPropertyYieldCurrencyCode(){
        when(configParamsService.getParameterValue(GUIConfigParamName.CORE_PROPERTY_YIELD_CURRENCY_CODE)).thenReturn("USD");
        when(configParamsService.getParameterValue(IntegrationConfigParamName.CORE_PROPERTY_BASE_CURRENCY_CODE)).thenReturn(null);

        String rmsCurrencyCode = cloneProjectionBuilderService.getRMSCurrencyCode();

        assertEquals("USD",rmsCurrencyCode);
        verify(configParamsService).getParameterValue(GUIConfigParamName.CORE_PROPERTY_YIELD_CURRENCY_CODE);
        verify(configParamsService).getParameterValue(IntegrationConfigParamName.CORE_PROPERTY_BASE_CURRENCY_CODE);
    }

    @Test
    void testPropertyBaseCurrencyCode(){
        when(configParamsService.getParameterValue(GUIConfigParamName.CORE_PROPERTY_YIELD_CURRENCY_CODE)).thenReturn(null);
        when(configParamsService.getParameterValue(IntegrationConfigParamName.CORE_PROPERTY_BASE_CURRENCY_CODE)).thenReturn("USD");

        String rmsCurrencyCode = cloneProjectionBuilderService.getRMSCurrencyCode();

        assertEquals("USD",rmsCurrencyCode);
        verify(configParamsService).getParameterValue(GUIConfigParamName.CORE_PROPERTY_YIELD_CURRENCY_CODE);
        verify(configParamsService).getParameterValue(IntegrationConfigParamName.CORE_PROPERTY_BASE_CURRENCY_CODE);
    }

    @Test
    void testGetRMSCurrencyCode_WhenYieldCurrencyAndBaseCurrencyNotSet(){
        when(configParamsService.getParameterValue(GUIConfigParamName.CORE_PROPERTY_YIELD_CURRENCY_CODE)).thenReturn(null);
        when(configParamsService.getParameterValue(IntegrationConfigParamName.CORE_PROPERTY_BASE_CURRENCY_CODE)).thenReturn(null);

        assertThrows(TetrisException.class,()->cloneProjectionBuilderService.getRMSCurrencyCode());
        verify(configParamsService).getParameterValue(GUIConfigParamName.CORE_PROPERTY_YIELD_CURRENCY_CODE);
        verify(configParamsService).getParameterValue(IntegrationConfigParamName.CORE_PROPERTY_BASE_CURRENCY_CODE);
    }

    @Test
    void testGetPropertyCapacity(){
        BigDecimal propertyCapacity = cloneProjectionBuilderService.getPropertyCapacity();

        assertEquals(BigDecimal.valueOf(448),propertyCapacity);
    }

    @Test
    void testGetPropertyBuildTypeKey(){
        when(configParamsService.getBooleanParameterValue(IPConfigParamName.CORE_LIMITED_DATA_BUILD_ENABLED.value(), TestClient.BSTN.name(), TestProperty.H1.name())).thenReturn(false);
        String propertyBuildTypeKey = cloneProjectionBuilderService.getPropertyBuildTypeKey(TestClient.BSTN.name(), TestProperty.H1.name());

        assertEquals(STANDARD_BUILD_TYPE_KEY,propertyBuildTypeKey);
    }


    @Test
    void testSortPropertiesByBusinessMixAndADRWhenBOTHAreMatching(){
        setUpBusinessMixDTO();
        LocalDate startDate = LocalDate.of(2024,1,1);
        Map<String,List<Integer>> categorizedProperties = cloneProjectionBuilderService.sortPropertiesByBusinessMixAndADR(getTestProperties(),startDate,startDate.plusDays(365),2,50.0,60.0,50.0,200.0);
        assertEquals(4,categorizedProperties.size());
        assertEquals(2,categorizedProperties.get(BOTH).size());
        List<Integer> allMatchingProp = categorizedProperties.get(BOTH);
        assertTrue(allMatchingProp.contains(TestProperty.H1.getId()));
        assertTrue(allMatchingProp.contains(TestProperty.H2.getId()));
    }

    @Test
    void testSortPropertiesByBusinessMixAndADRWhenADRAreMatching(){
        setUpBusinessMixDTO();
        LocalDate startDate = LocalDate.of(2024,1,1);
        Map<String,List<Integer>> categorizedProperties = cloneProjectionBuilderService.sortPropertiesByBusinessMixAndADR(getTestProperties(),startDate,startDate.plusDays(365),2,80.0,90.0,50.0,200.0);
        assertEquals(4,categorizedProperties.size());
        assertEquals(2,categorizedProperties.get(ADR).size());
        List<Integer> allMatchingProp = categorizedProperties.get(ADR);
        assertTrue(allMatchingProp.contains(TestProperty.H2.getId()));
    }

    @Test
    void testSortPropertiesByBusinessMixAndADRWhenBusinessMixAreMatching(){
        setUpBusinessMixDTO();
        LocalDate startDate = LocalDate.of(2024,1,1);
        Map<String,List<Integer>> categorizedProperties = cloneProjectionBuilderService.sortPropertiesByBusinessMixAndADR(getTestProperties(),startDate,startDate.plusDays(365),2,50.0,60.0,150.0,200.0);
        assertEquals(4,categorizedProperties.size());
        assertEquals(2,categorizedProperties.get(BUSINESS_MIX).size());
        List<Integer> allMatchingProp = categorizedProperties.get(BUSINESS_MIX);
        assertTrue(allMatchingProp.contains(TestProperty.H1.getId()));
        assertTrue(allMatchingProp.contains(TestProperty.H2.getId()));
    }


    @Test
    void testSortPropertiesByBusinessMixAndADRWhenNoneAreMatching(){
        setUpBusinessMixDTO();
        LocalDate startDate = LocalDate.of(2024,1,1);
        Map<String,List<Integer>> categorizedProperties = cloneProjectionBuilderService.sortPropertiesByBusinessMixAndADR(getTestProperties(),startDate,startDate.plusDays(365),2,80.0,90.0,150.0,200.0);
        assertEquals(4,categorizedProperties.size());
        assertEquals(2,categorizedProperties.get(NONE).size());
        List<Integer> allMatchingProp = categorizedProperties.get(NONE);
        assertTrue(allMatchingProp.contains(TestProperty.H1.getId()));
        assertTrue(allMatchingProp.contains(TestProperty.H2.getId()));
    }

    @Test
    void testSortPropertiesByBusinessMixAndADR_AllCombinations(){
        List<BusinessMixDTO> businessMixDTOS = getBusinessMixDTOS();
        businessMixDTOS.addAll( List.of(new BusinessMixDTO(10,BUSINESS_TYPE_GROUP_ID,30.0,80.0),
                new BusinessMixDTO(10,BUSINESS_TYPE_TRANSIENT_ID,70.0,140.0),
                new BusinessMixDTO(12,BUSINESS_TYPE_GROUP_ID,20.0,50.0),
                new BusinessMixDTO(12,BUSINESS_TYPE_TRANSIENT_ID,80.0,180.0)));
        cloneProjectionBuilderService.setCloneProjectionBuilderRepository(mockRepository);
        when(mockRepository.getBusinessMixDTOS(anyList(),any(),any())).thenReturn(businessMixDTOS);

        LocalDate startDate = LocalDate.of(2024,1,1);
        List<Integer> testProperties = getTestProperties();
        testProperties.addAll(List.of(10,12));
        Map<String,List<Integer>> categorizedProperties = cloneProjectionBuilderService.sortPropertiesByBusinessMixAndADR(testProperties,startDate,startDate.plusDays(365),2,50.0,60.0,110.0,150.0);

        assertEquals(4,categorizedProperties.size());
        assertEquals(1,categorizedProperties.get(BOTH).size());
        List<Integer> allMatchingProp = categorizedProperties.get(BOTH);
        assertTrue(allMatchingProp.contains(TestProperty.H2.getId()));

        assertEquals(1,categorizedProperties.get(BUSINESS_MIX).size());
        List<Integer> businessMixMatchingProp = categorizedProperties.get(BUSINESS_MIX);
        assertTrue(businessMixMatchingProp.contains(TestProperty.H1.getId()));

        assertEquals(1,categorizedProperties.get(ADR).size());
        List<Integer> adrMatchingProp = categorizedProperties.get(ADR);
        assertTrue(adrMatchingProp.contains(10));

        assertEquals(1,categorizedProperties.get(NONE).size());
        List<Integer> noneMatchingProp = categorizedProperties.get(NONE);
        assertTrue(noneMatchingProp.contains(12));
    }

    @Test
    void testSortPropertiesByBusinessMixAndADRWhenBusinessMixAndADRInputsNotProvided(){
        LocalDate startDate = LocalDate.of(2024,1,1);
        Map<String,List<Integer>> categorizedProperties = cloneProjectionBuilderService.sortPropertiesByBusinessMixAndADR(getTestProperties(),startDate,startDate.plusDays(365),2, -1.0,-1.0,-1.0,-1.0);
        assertEquals(1,categorizedProperties.size());
        assertEquals(2,categorizedProperties.get(NONE).size());
        List<Integer> noneMatchingProps = categorizedProperties.get(NONE);
        assertTrue(noneMatchingProps.contains(TestProperty.H1.getId()));
        assertTrue(noneMatchingProps.contains(TestProperty.H2.getId()));
    }

    @Test
    void testSortPropertiesByBusinessMixAndADRWhenBusinessMixInputNotProvided(){
        setUpBusinessMixDTO();
        LocalDate startDate = LocalDate.of(2024,1,1);
        Map<String,List<Integer>> categorizedProperties = cloneProjectionBuilderService.sortPropertiesByBusinessMixAndADR(getTestProperties(),startDate,startDate.plusDays(365),2, -1.0,-1.0,110.0,150.0);
        assertEquals(2,categorizedProperties.size());

        List<Integer> adrMatchingProps = categorizedProperties.get(ADR);
        assertEquals(1,adrMatchingProps.size());
        assertTrue(adrMatchingProps.contains(TestProperty.H2.getId()));

        List<Integer> noneMatchingProps = categorizedProperties.get(NONE);
        assertEquals(1,noneMatchingProps.size());
        assertTrue(noneMatchingProps.contains(TestProperty.H1.getId()));
    }

    @Test
    void testSortPropertiesByBusinessMixAndADRWhenADRInputNotProvided(){
        setUpBusinessMixDTO();
        LocalDate startDate = LocalDate.of(2024,1,1);
        Map<String,List<Integer>> categorizedProperties = cloneProjectionBuilderService.sortPropertiesByBusinessMixAndADR(getTestProperties(),startDate,startDate.plusDays(365),2, 40.0,50.0,-1.0,-1.0);
        assertEquals(2,categorizedProperties.size());

        List<Integer> businessMixMatchingProps = categorizedProperties.get(BUSINESS_MIX);
        assertEquals(1,businessMixMatchingProps.size());
        assertTrue(businessMixMatchingProps.contains(TestProperty.H2.getId()));

        List<Integer> noneMatchingProps = categorizedProperties.get(NONE);
        assertEquals(1,noneMatchingProps.size());
        assertTrue(noneMatchingProps.contains(TestProperty.H1.getId()));
    }

    @Test
    void testGetMonthlyADRDtos(){
        List<LDBTotalRoomsSoldRevenuePerMonth> totalRoomsSoldRevenuePerMonth = List.of(
                getLDBLdbTotalRoomsSoldRevenuePerMonth(LocalDate.of(2024,1,1),200,145,BigDecimal.valueOf(1500)),
                getLDBLdbTotalRoomsSoldRevenuePerMonth(LocalDate.of(2024,2,1),290,250,BigDecimal.valueOf(2500)),
                getLDBLdbTotalRoomsSoldRevenuePerMonth(LocalDate.of(2024,3,1),310,295,BigDecimal.valueOf(3000)),
                getLDBLdbTotalRoomsSoldRevenuePerMonth(LocalDate.of(2024,4,1),150,0,BigDecimal.ZERO)
        );
        cloneProjectionBuilderService.setCloneProjectionBuilderRepository(mockRepository);
        when(mockRepository.getTotalRoomsSoldRevenuePerMonth()).thenReturn(totalRoomsSoldRevenuePerMonth);

        List<MonthlyADRDto> monthlyADRDtos = cloneProjectionBuilderService.getMonthlyADRDtos();

        assertEquals(BigDecimal.valueOf(10.34), monthlyADRDtos.get(0).getMonthlyADR());
        assertEquals(BigDecimal.valueOf(10.00).setScale(2,RoundingMode.HALF_UP), monthlyADRDtos.get(1).getMonthlyADR());
        assertEquals(BigDecimal.valueOf(10.17), monthlyADRDtos.get(2).getMonthlyADR());
        assertEquals(BigDecimal.ZERO, monthlyADRDtos.get(3).getMonthlyADR());
    }

    @Test
    void testGetMonthlyMSADRDtos(){
        List<LDBTotalRoomsSoldRevenuePerMonthMktSeg> totalRoomsSoldRevenuePerMonthMktSegs = List.of(
                getLDBTotalRoomsSoldRevenuePerMonthMktSeg(LocalDate.of(2024,1,1),"BAR",145,BigDecimal.valueOf(2500)),
                getLDBTotalRoomsSoldRevenuePerMonthMktSeg(LocalDate.of(2024,1,1),"GOV",100,BigDecimal.valueOf(1500)),
                getLDBTotalRoomsSoldRevenuePerMonthMktSeg(LocalDate.of(2024,2,1),"BAR",98,BigDecimal.valueOf(1500)),
                getLDBTotalRoomsSoldRevenuePerMonthMktSeg(LocalDate.of(2024,2,1),"GOV",0,BigDecimal.ZERO)
        );
        cloneProjectionBuilderService.setCloneProjectionBuilderRepository(mockRepository);
        when(mockRepository.getTotalRoomsSoldRevenuePerMonthMktSegs()).thenReturn(totalRoomsSoldRevenuePerMonthMktSegs);
        List<MonthlyMSADRDto> monthlyMsADRDtos = cloneProjectionBuilderService.getMonthlyMSADRDtos();

        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("MMM-yyyy");
        Optional<MonthlyMSADRDto> govFeb = monthlyMsADRDtos.stream().filter(mdtos -> mdtos.getMktSegCode().equals("GOV") && mdtos.getProjectionMonthYear().equals(LocalDate.of(2024, 2, 1))).findFirst();
        Optional<MonthlyMSADRDto> govJan = monthlyMsADRDtos.stream().filter(mdtos -> mdtos.getMktSegCode().equals("GOV") && mdtos.getProjectionMonthYear().equals(LocalDate.of(2024, 1, 1))).findFirst();
        Optional<MonthlyMSADRDto> barFeb = monthlyMsADRDtos.stream().filter(mdtos -> mdtos.getMktSegCode().equals("BAR") && mdtos.getProjectionMonthYear().equals(LocalDate.of(2024, 2, 1))).findFirst();
        Optional<MonthlyMSADRDto> barJan = monthlyMsADRDtos.stream().filter(mdtos -> mdtos.getMktSegCode().equals("BAR") && mdtos.getProjectionMonthYear().equals(LocalDate.of(2024, 1, 1))).findFirst();
        assertEquals(BigDecimal.ZERO,govFeb.get().getMonthlyADR());
        assertEquals(BigDecimal.valueOf(15.00).setScale(2,RoundingMode.HALF_UP),govJan.get().getMonthlyADR());
        assertEquals(BigDecimal.valueOf(17.24),barJan.get().getMonthlyADR());
        assertEquals(BigDecimal.valueOf(15.31),barFeb.get().getMonthlyADR());
    }

    private LDBTotalRoomsSoldRevenuePerMonthMktSeg getLDBTotalRoomsSoldRevenuePerMonthMktSeg(LocalDate projectionMonthYear, String mktSegCode, Integer monthlyRoomsSold, BigDecimal monthlyRevenue){
        LDBTotalRoomsSoldRevenuePerMonthMktSeg ldbTotalRoomsSoldRevenuePerMonthMktSeg = new LDBTotalRoomsSoldRevenuePerMonthMktSeg();
        ldbTotalRoomsSoldRevenuePerMonthMktSeg.setProjectionMonthYear(projectionMonthYear);
        ldbTotalRoomsSoldRevenuePerMonthMktSeg.setMarketSegmentCode(mktSegCode);
        ldbTotalRoomsSoldRevenuePerMonthMktSeg.setMonthlyRoomsSold(monthlyRoomsSold);
        ldbTotalRoomsSoldRevenuePerMonthMktSeg.setMonthlyRevenue(monthlyRevenue);
        return ldbTotalRoomsSoldRevenuePerMonthMktSeg;
    }

    private LDBTotalRoomsSoldRevenuePerMonth getLDBLdbTotalRoomsSoldRevenuePerMonth(LocalDate projectionMonthYear, Integer monthlyCapacity, Integer monthlyRoomsSold, BigDecimal monthlyRevenue){
        LDBTotalRoomsSoldRevenuePerMonth ldbTotalRoomsSoldRevenuePerMonth = new LDBTotalRoomsSoldRevenuePerMonth();
        ldbTotalRoomsSoldRevenuePerMonth.setProjectionMonthYear(projectionMonthYear);
        ldbTotalRoomsSoldRevenuePerMonth.setMonthlyCapacity(monthlyCapacity);
        ldbTotalRoomsSoldRevenuePerMonth.setMonthlyRoomsSold(monthlyRoomsSold);
        ldbTotalRoomsSoldRevenuePerMonth.setMonthlyRevenue(monthlyRevenue);
        return ldbTotalRoomsSoldRevenuePerMonth;
    }

    @Test
    void testSaveLDBTotalRoomSoldAndRevenuePerMonth(){
        List<MonthlyADRDto> monthlyADRDtos = List.of(
                getMonthlyADRDto(LocalDate.of(2024, 1, 1), 200, 150, BigDecimal.valueOf(15000)),
                getMonthlyADRDto(LocalDate.of(2024, 2, 1), 290, 250, BigDecimal.valueOf(25000)),
                getMonthlyADRDto(LocalDate.of(2024, 3, 1), 100, 95, BigDecimal.valueOf(9500))
                );
        cloneProjectionBuilderService.saveLDBTotalRoomSoldAndRevenuePerMonth(monthlyADRDtos);

        List<LDBTotalRoomsSoldRevenuePerMonth> ldbTotalRoomsSoldRevenuePerMonths = tenantCrudService().findAll(LDBTotalRoomsSoldRevenuePerMonth.class);
        assertEquals(3,ldbTotalRoomsSoldRevenuePerMonths.size());
    }

    private MonthlyADRDto getMonthlyADRDto(LocalDate projectionMonthYear, Integer monthlyCapacity, Integer monthlyRoomSold, BigDecimal monthlyRevenue){
        MonthlyADRDto monthlyADRDto = new MonthlyADRDto();
        monthlyADRDto.setProjectionMonthYear(projectionMonthYear);
        monthlyADRDto.setMonthlyCapacity(monthlyCapacity);
        monthlyADRDto.setMonthlyRoomSold(monthlyRoomSold);
        monthlyADRDto.setMonthlyRevenue(monthlyRevenue);
        return monthlyADRDto;
    }

    @Test
    void testSaveLDBTotalRoomSoldAndRevenuePerMonthPerMktSeg(){
        List<MonthlyMSADRDto> monthlyMSADRDtos = List.of(
                getMonthlyMSADRDto(LocalDate.of(2024, 1, 1), "MS1", 100, BigDecimal.valueOf(10000)),
                getMonthlyMSADRDto(LocalDate.of(2024, 1, 1), "MS2", 50, BigDecimal.valueOf(5000)),
                getMonthlyMSADRDto(LocalDate.of(2024, 2, 1), "MS1", 95, BigDecimal.valueOf(9500)),
                getMonthlyMSADRDto(LocalDate.of(2024, 2, 1), "MS2", 45, BigDecimal.valueOf(4500))
        );

        Map<String, Map<String, MonthlyMSADRDto>> monthlyMSAdrDtosMap = monthlyMSADRDtos.stream().collect(Collectors.groupingBy(MonthlyMSADRDto::getMktSegCode, Collectors.toMap(monthlyMSADRDto -> cloneProjectionBuilderService.getStringFromLocalDate(monthlyMSADRDto.getProjectionMonthYear()), Function.identity())));

        cloneProjectionBuilderService.saveLDBTotalRoomSoldAndRevenuePerMonthPerMktSeg(monthlyMSAdrDtosMap);
        List<LDBTotalRoomsSoldRevenuePerMonthMktSeg> ldbTotalRoomsSoldRevenuePerMonthMktSegs = tenantCrudService().findAll(LDBTotalRoomsSoldRevenuePerMonthMktSeg.class);
        assertEquals(4,ldbTotalRoomsSoldRevenuePerMonthMktSegs.size());
    }

    private MonthlyMSADRDto getMonthlyMSADRDto(LocalDate projectionMonthYear, String mktSegCode, Integer monthlyRoomSold, BigDecimal monthlyRevenue){
        MonthlyMSADRDto monthlyMSADRDto = new MonthlyMSADRDto();
        monthlyMSADRDto.setProjectionMonthYear(projectionMonthYear);
        monthlyMSADRDto.setMktSegCode(mktSegCode);
        monthlyMSADRDto.setMonthlyRoomSold(monthlyRoomSold);
        monthlyMSADRDto.setMonthlyRevenue(monthlyRevenue);
        return monthlyMSADRDto;
    }

    @Test
    void testHistoricMonthWhenPeriod(){
        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(LocalDate.now());
        Map<Month, JavaLocalDateRange> dateMap = cloneProjectionBuilderService.getMonthToHistoricMonthMapping();
        assertEquals(12,dateMap.size());
    }

    /*@Test
    void testStartCloneProjection(){
        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(LocalDate.of(2024,8,1));

        LocalDate projectionStartDate = LocalDate.of(2025,1,1);
        LocalDate projectionEndDate = LocalDate.of(2025,3,31);
        CloneProjectionBuilder cloneProjectionBuilder = cloneProjectionBuilderService.startCloneProjection(projectionStartDate,projectionEndDate,getMonthlyRoomSoldAndRevenueDTOS());
        cloneProjectionBuilder.populateMonthlyProjections(Collections.emptyMap());
    }*/

    @Test
    void testMSMappingByCodeOnly(){
        setup(false);

        Object[] mktSegDTO = createMktSegDTO(6, "BAR", "BAR", 2, 3, 1, 0, 0, 0, 0, 0, 9, 100, 122);
        Object[] mktSegDTO1 = createMktSegDTO(6, "COMP", "COMP", 2, 3, 1, 0, 0, 0, 0, 0, 9, 100, 122);
        Object[] mktSegDTO2 = createMktSegDTO(6, "DISC", "DISC", 2, 3, 1, 0, 0, 0, 0, 0, 9, 100, 122);

        MktSegDetailsDto mktSegDTO3 = createMktSegDTOWithAttrbuted(5, "BAR", "BAR", 2, 2, 1, 0, 0, 0, 0, 0);
        MktSegDetailsDto mktSegDTO4 = createMktSegDTOWithAttrbuted(5, "DISC", "DISC", 2, 2, 1, 0, 0, 0, 0, 0);
        List<MktSegDetailsDto> dtos = new ArrayList<>();
        dtos.add(mktSegDTO3);
        dtos.add(mktSegDTO4);
        LocalDate now = LocalDate.of(2024, 9, 12);
        when(mockRepository.getMKtSegWithAttributes(false, 6, now, now)).thenReturn(List.of(mktSegDTO, mktSegDTO1, mktSegDTO2));
        when(mockRepository.getMKtSegWithAttributes(false, 7, now, now)).thenReturn(List.of(mktSegDTO, mktSegDTO1));
        List<Object[]> capacities = getCapacity(8, 4500d);

        when(mockRepository.monthWiseCapacity(any(), any(), any())).thenReturn(capacities);
        when(mockRepository.getLDBMKtSegWithAttributes(5)).thenReturn(dtos);
        Map<Integer, Map<String, List<String>>> mappedMS = cloneProjectionBuilderService.mappedMktSeg(List.of(6,7), now, now, true);
        assertEquals(2, mappedMS.size());
        Map<String, List<String>> mappedMS1 = mappedMS.get(6);
        assertTrue(mappedMS1.containsKey("BAR"));
        assertTrue(mappedMS1.containsKey("DISC"));
        Map<String, List<String>> mappedMS2 = mappedMS.get(7);
        assertTrue(mappedMS2.containsKey("BAR"));
    }

    @Test
    void testMSMappingByCodeOnlyForHilton(){
        setup(false);
        PacmanWorkContextHelper.setClientCode("Hilton");

        Object[] mktSegDTO = createMktSegDTO(6, "BAR", "BAR", 2, 3, 1, 0, 0, 0, 0, 0, 9, 100, 122);
        Object[] mktSegDTO1 = createMktSegDTO(6, "COMP", "COMP", 2, 3, 1, 0, 0, 0, 0, 0, 9, 100, 122);
        Object[] mktSegDTO2 = createMktSegDTO(6, "DISC", "DISC", 2, 3, 1, 0, 0, 0, 0, 0, 9, 100, 122);

        MktSegDetailsDto mktSegDTO3 = createMktSegDTOWithAttrbuted(5, "BAR", "BAR", 2, 2, 1, 0, 0, 0, 0, 0);
        MktSegDetailsDto mktSegDTO4 = createMktSegDTOWithAttrbuted(5, "DISC", "DISC", 2, 2, 1, 0, 0, 0, 0, 0);
        List<MktSegDetailsDto> dtos = new ArrayList<>();
        dtos.add(mktSegDTO3);
        dtos.add(mktSegDTO4);
        LocalDate now = LocalDate.of(2024, 9, 12);
        when(mockRepository.getMKtSegWithAttributes(false, 6, now, now)).thenReturn(List.of(mktSegDTO, mktSegDTO1, mktSegDTO2));
        when(mockRepository.getMKtSegWithAttributes(false, 7, now, now)).thenReturn(List.of(mktSegDTO, mktSegDTO1));
        when(projectionDataService.getMarketSegments()).thenReturn(List.of("BAR"));
        List<Object[]> capacities = getCapacity(8, 4500d);

        when(mockRepository.monthWiseCapacity(any(), any(), any())).thenReturn(capacities);
        when(mockRepository.getLDBMKtSegWithAttributes(5)).thenReturn(dtos);
        Map<Integer, Map<String, List<String>>> mappedMS = cloneProjectionBuilderService.mappedMktSeg(List.of(6,7), now, now, true);
        assertEquals(2, mappedMS.size());
        Map<String, List<String>> mappedMS1 = mappedMS.get(6);
        assertTrue(mappedMS1.containsKey("BAR"));
        assertFalse(mappedMS1.containsKey("DISC"));
        Map<String, List<String>> mappedMS2 = mappedMS.get(7);
        assertTrue(mappedMS2.containsKey("BAR"));
        PacmanWorkContextHelper.setClientCode("BSTN");
    }

    private static List<Object[]> getCapacity(Integer month, Double capacity) {
        Object[] capacities = {month, BigDecimal.valueOf(capacity)};
        ArrayList<Object[]> arrayList = new ArrayList<>();
        arrayList.add(capacities);
        return arrayList;
    }

    private void setup(boolean isAMS) {
        when(propertyService.getPropertyById(5)).thenReturn(createProperty(5, "H1", "BSTN"));
        when(propertyService.getPropertyById(6)).thenReturn(createProperty(6, "H2", "BSTN"));
        when(propertyService.getPropertyById(7)).thenReturn(createProperty(7, "H3", "BSTN1"));
        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.ANALYTICAL_MARKET_SEGMENT_ENABLED.getParameterName(), "BSTN", "H2")).thenReturn(isAMS);
        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.ANALYTICAL_MARKET_SEGMENT_ENABLED.getParameterName(), "BSTN1", "H3")).thenReturn(false);
        cloneProjectionBuilderService.setCloneProjectionBuilderRepository(mockRepository);
    }

    @Test
    void testMSMappingByCodeWhenAMS(){
        setup(true);

        Object[] mktSegDTO1 = createMktSegDTO(6, "BAR", "BAR", 2, 3, 1, 0, 0, 0, 0, 0, 9, 100, 122);
        Object[] mktSegDTO2 = createMktSegDTO(7, "COMP", "COMP", 2, 3, 1, 0, 0, 0, 0, 0, 9, 100, 122);
        Object[] mktSegDTO3 = createMktSegDTO(7, "DISC", "DISC", 2, 3, 1, 0, 0, 0, 0, 0, 9, 100, 122);
        Object[] mktSegDTO4 = createMktSegDTO(6, "DISC_DEF", "DISC", 2, 3, 1, 0, 0, 0, 0, 0, 9, 100, 122);
        Object[] mktSegDTO5 = createMktSegDTO(6, "DISC_QY", "DISC", 2, 3, 1, 0, 0, 0, 0, 0, 9, 100, 122);


        MktSegDetailsDto mktSegDTO6 = createMktSegDTOWithAttrbuted(5, "BAR", "BAR", 2, 2, 1, 0, 0, 0, 0, 0);
        MktSegDetailsDto mktSegDTO7 = createMktSegDTOWithAttrbuted(5, "DISC", "DISC", 2, 2, 1, 0, 0, 0, 0, 0);
        LocalDate now = LocalDate.of(2024, 9, 12);
        when(mockRepository.getMKtSegWithAttributes(true, 6, now, now)).thenReturn(List.of(mktSegDTO1, mktSegDTO2, mktSegDTO4, mktSegDTO5));
        when(mockRepository.getMKtSegWithAttributes(false, 7, now, now)).thenReturn(List.of(mktSegDTO2, mktSegDTO3));
        when(mockRepository.getLDBMKtSegWithAttributes(5)).thenReturn(List.of(mktSegDTO6, mktSegDTO7));
        when(mockRepository.monthWiseCapacity(any(), any(), any())).thenReturn(getCapacity(8, 4500d));
        Map<Integer, Map<String, List<String>>> mappedMS = cloneProjectionBuilderService.mappedMktSeg(List.of(6,7), now, now, true);

        assertEquals(2, mappedMS.size());
        Map<String, List<String>> mappedMS1 = mappedMS.get(7);
        assertTrue(mappedMS1.containsKey("DISC"));
        Map<String, List<String>> mappedMS2 = mappedMS.get(6);
        assertTrue(mappedMS2.containsKey("DISC"));
        assertEquals(2, mappedMS2.get("DISC").size());
        assertEquals("DISC_DEF", mappedMS2.get("DISC").get(0));
        assertEquals("DISC_QY", mappedMS2.get("DISC").get(1));
    }

    @Test
    void testMSMappingWithAttributes(){
        setup(false);
        Object[] mktSegDTO = createMktSegDTO(6, "BART", "BART", 2, 3, 1, 0, 0, 0, 0, 0, 9, 100, 122);
        Object[] mktSegDTO1 = createMktSegDTO(6, "COM", "COMP", 2, 3, 1, 0, 0, 0, 0, 0, 9, 100, 122);
        Object[] mktSegDTO2 = createMktSegDTO(6, "DISC_QY", "DISC_QY", 2, 2, 1, 0, 0, 0, 0, 0, 9, 100, 122);
        Object[] mktSegDTO3 = createMktSegDTO(7, "DISC", "DISC", 2, 3, 1, 0, 0, 0, 0, 0, 9, 100, 122);
        Object[] mktSegDTO4 = createMktSegDTO(7, "MS1", "MS1", 2, 1, 1, 0, 0, 0, 0, 0, 9, 100, 122);
        Object[] mktSegDTO5 = createMktSegDTO(7, "MS2", "MS2", 2, 3, 1, 0, 0, 0, 0, 0, 9, 100, 122);

        MktSegDetailsDto mktSegDTO6 = createMktSegDTOWithAttrbuted(5, "BAR", "BAR", 2, 3, 1, 0, 0, 0, 0, 0);
        MktSegDetailsDto mktSegDTO7 = createMktSegDTOWithAttrbuted(5, "CORP", "CORP", 2, 2, 1, 0, 0, 0, 0, 0);
        LocalDate now = LocalDate.of(2024, 9, 12);
        when(mockRepository.getMKtSegWithAttributes(false, 6, now, now)).thenReturn(List.of(mktSegDTO, mktSegDTO1, mktSegDTO2));
        when(mockRepository.getMKtSegWithAttributes(false, 7, now, now)).thenReturn(List.of(mktSegDTO3, mktSegDTO4, mktSegDTO5));
        when(mockRepository.getLDBMKtSegWithAttributes(5)).thenReturn(List.of(mktSegDTO6, mktSegDTO7));
        Map<Integer, Map<String, List<String>>> mappedMS = cloneProjectionBuilderService.mappedMktSeg(List.of(6,7), now, now, false);
        assertEquals(2, mappedMS.size());
        assertData1(mappedMS);
        assertData2(mappedMS);
    }

    @Test
    void testMSMappingWithFinalCalculations(){
        setup(false);
        LocalDate startDate = LocalDate.of(2024, 8, 1);
        LocalDate endDate = LocalDate.of(2024, 9, 30);
        when(mockRepository.getMKtSegWithAttributes(false, 6, startDate, endDate)).thenReturn(getMockDataForProperty1());
        when(mockRepository.getMKtSegWithAttributes(false, 7, startDate, endDate)).thenReturn(getMockDataForProperty2());
        when(mockRepository.getLDBMKtSegWithAttributes(5)).thenReturn(getMockDataForLDB());
        when(mockRepository.monthWiseCapacity(any(), any(), any())).thenReturn(getCapacity(8, 9000d));

        List<MonthlyRoomSoldAndRevenueDTO> monthlyRoomSoldAndRevenueDTOS = cloneProjectionBuilderService
                .mapMktSegWithCalculationFromSource(List.of(6, 7), startDate, endDate);
        List<MonthlyRoomSoldAndRevenueDTO> firstPropertyAug = monthlyRoomSoldAndRevenueDTOS.stream().filter(m -> m.getStdPropertyID().equals(6) && m.getMonth().equals(Month.AUGUST)).collect(Collectors.toList());
        assertCalculations1(firstPropertyAug);

        List<MonthlyRoomSoldAndRevenueDTO> firstPropertySep = monthlyRoomSoldAndRevenueDTOS.stream().filter(m -> m.getStdPropertyID().equals(6) && m.getMonth().equals(Month.SEPTEMBER)).collect(Collectors.toList());
        assertCalculation2(firstPropertySep);

        List<MonthlyRoomSoldAndRevenueDTO> secondPropertyAug = monthlyRoomSoldAndRevenueDTOS.stream().filter(m -> m.getStdPropertyID().equals(7) && m.getMonth().equals(Month.AUGUST)).collect(Collectors.toList());
        assertDataForProperty2(secondPropertyAug, 409, 95001d);

        List<MonthlyRoomSoldAndRevenueDTO> secondPropertySep = monthlyRoomSoldAndRevenueDTOS.stream().filter(m -> m.getStdPropertyID().equals(7) && m.getMonth().equals(Month.SEPTEMBER)).collect(Collectors.toList());
        assertDataForProperty2(secondPropertySep, 680, 165000d);
    }

    @Test
    void testMSMappingWithFinalCalculationsForHilton(){
        PacmanWorkContextHelper.setClientCode("Hilton");
        setup(false);
        LocalDate startDate = LocalDate.of(2024, 8, 1);
        LocalDate endDate = LocalDate.of(2024, 9, 30);
        when(mockRepository.getMKtSegWithAttributes(false, 6, startDate, endDate)).thenReturn(getMockDataForProperty1());
        when(mockRepository.getMKtSegWithAttributes(false, 7, startDate, endDate)).thenReturn(getMockDataForProperty2());
        when(mockRepository.getLDBMKtSegWithAttributes(5)).thenReturn(getMockDataForLDB());
        when(mockRepository.monthWiseCapacity(any(), any(), any())).thenReturn(getCapacity(8, 9000d));
        when(projectionDataService.getMarketSegments()).thenReturn(List.of("BAR"));

        List<MonthlyRoomSoldAndRevenueDTO> monthlyRoomSoldAndRevenueDTOS = cloneProjectionBuilderService
                .mapMktSegWithCalculationFromSource(List.of(6, 7), startDate, endDate);
        List<MonthlyRoomSoldAndRevenueDTO> firstPropertyAug = monthlyRoomSoldAndRevenueDTOS.stream().filter(m -> m.getStdPropertyID().equals(6) && m.getMonth().equals(Month.AUGUST)).collect(Collectors.toList());
        assertEquals(1, firstPropertyAug.size());
        assertEquals(450, firstPropertyAug.get(0).getMonthlyRoomSold());
        assertEquals(70000d, firstPropertyAug.get(0).getMonthlyRevenue().doubleValue());

        List<MonthlyRoomSoldAndRevenueDTO> firstPropertySep = monthlyRoomSoldAndRevenueDTOS.stream().filter(m -> m.getStdPropertyID().equals(6) && m.getMonth().equals(Month.SEPTEMBER)).collect(Collectors.toList());
        assertEquals(1, firstPropertySep.size());
        assertEquals(500, firstPropertySep.get(0).getMonthlyRoomSold());
        assertEquals(55000d, firstPropertySep.get(0).getMonthlyRevenue().doubleValue());
    }

    @Test
    void testFinalCalculationWithRelaxedAttributeMSForUnpairedMS(){
        setup(false);
        LocalDate startDate = LocalDate.of(2024, 8, 1);
        LocalDate endDate = LocalDate.of(2024, 9, 30);
        List<Object[]> mockDataForProperty1 = new ArrayList<>(getMockDataForProperty1());
        List<Object[]> mockDataForPropertyForRelaxed1 = getMockDataForPropertyForRelaxedToUnpairedMS(6);
        mockDataForProperty1.addAll(mockDataForPropertyForRelaxed1);
        when(mockRepository.getMKtSegWithAttributes(false, 6, startDate, endDate)).thenReturn(mockDataForProperty1);
        List<Object[]> mockDataForProperty2 = new ArrayList<>(getMockDataForProperty2());
        mockDataForProperty2.addAll(getMockDataForPropertyForRelaxedToUnpairedMS(7));
        when(mockRepository.getMKtSegWithAttributes(false, 7, startDate, endDate)).thenReturn(mockDataForProperty2);
        when(mockRepository.getLDBMKtSegWithAttributes(5)).thenReturn(getMockDataForLDB());
        when(mockRepository.monthWiseCapacity(any(), any(), any())).thenReturn(getCapacity(8, 9000d));

        List<MonthlyRoomSoldAndRevenueDTO> monthlyRoomSoldAndRevenueDTOS = cloneProjectionBuilderService
                .mapMktSegWithCalculationFromSource(List.of(6, 7), startDate, endDate);
        List<MonthlyRoomSoldAndRevenueDTO> firstPropertyAug = monthlyRoomSoldAndRevenueDTOS.stream()
                .filter(m -> m.getStdPropertyID().equals(6) && m.getMonth().equals(Month.AUGUST)).collect(Collectors.toList());
        assertAugForRelaxedAttributeForUnmappedMS(firstPropertyAug);

        List<MonthlyRoomSoldAndRevenueDTO> firstPropertySep = monthlyRoomSoldAndRevenueDTOS.stream()
                .filter(m -> m.getStdPropertyID().equals(6) && m.getMonth().equals(Month.SEPTEMBER)).collect(Collectors.toList());
        assertSepForRelaxedAttributeForUnmappedMS(firstPropertySep);

        List<MonthlyRoomSoldAndRevenueDTO> secondPropertyAug = monthlyRoomSoldAndRevenueDTOS.stream()
                .filter(m -> m.getStdPropertyID().equals(7) && m.getMonth().equals(Month.AUGUST)).collect(Collectors.toList());
        assertDataForProperty2(secondPropertyAug, 429, 120001d);

        List<MonthlyRoomSoldAndRevenueDTO> secondPropertySep = monthlyRoomSoldAndRevenueDTOS.stream()
                .filter(m -> m.getStdPropertyID().equals(7) && m.getMonth().equals(Month.SEPTEMBER)).collect(Collectors.toList());
        assertDataForProperty2(secondPropertySep, 690, 180000d);
    }

    @Test
    void testFinalCalculationWithRelaxedAttributeMSForPairedMS(){
        setup(false);
        LocalDate startDate = LocalDate.of(2024, 8, 1);
        LocalDate endDate = LocalDate.of(2024, 9, 30);
        List<Object[]> mockDataForProperty1 = new ArrayList<>(getMockDataForProperty1());
        mockDataForProperty1.addAll(getMockDataForPropertyForRelaxedToPairedMS(6));
        when(mockRepository.getMKtSegWithAttributes(false, 6, startDate, endDate)).thenReturn(mockDataForProperty1);
        List<Object[]> mockDataForProperty2 = new ArrayList<>(getMockDataForProperty2());
        mockDataForProperty2.addAll(getMockDataForPropertyForRelaxedToPairedMS(7));
        when(mockRepository.getMKtSegWithAttributes(false, 7, startDate, endDate)).thenReturn(mockDataForProperty2);
        when(mockRepository.getLDBMKtSegWithAttributes(5)).thenReturn(getMockDataForLDB());
        when(mockRepository.monthWiseCapacity(any(), any(), any())).thenReturn(getCapacity(8, 9000d));

        List<MonthlyRoomSoldAndRevenueDTO> monthlyRoomSoldAndRevenueDTOS = cloneProjectionBuilderService
                .mapMktSegWithCalculationFromSource(List.of(6, 7), startDate, endDate);
        List<MonthlyRoomSoldAndRevenueDTO> firstPropertyAug = monthlyRoomSoldAndRevenueDTOS.stream()
                .filter(m -> m.getStdPropertyID().equals(6) && m.getMonth().equals(Month.AUGUST)).collect(Collectors.toList());
        assertAugForRelaxedAttributeForMappedMS(firstPropertyAug);

        List<MonthlyRoomSoldAndRevenueDTO> firstPropertySep = monthlyRoomSoldAndRevenueDTOS.stream()
                .filter(m -> m.getStdPropertyID().equals(6) && m.getMonth().equals(Month.SEPTEMBER)).collect(Collectors.toList());
        assertSepForRelaxedAttributeForMappedMS(firstPropertySep);

        List<MonthlyRoomSoldAndRevenueDTO> secondPropertyAug = monthlyRoomSoldAndRevenueDTOS.stream()
                .filter(m -> m.getStdPropertyID().equals(7) && m.getMonth().equals(Month.AUGUST)).collect(Collectors.toList());
        assertDataForProperty2(secondPropertyAug, 429, 120001d);

        List<MonthlyRoomSoldAndRevenueDTO> secondPropertySep = monthlyRoomSoldAndRevenueDTOS.stream()
                .filter(m -> m.getStdPropertyID().equals(7) && m.getMonth().equals(Month.SEPTEMBER)).collect(Collectors.toList());
        assertDataForProperty2(secondPropertySep, 690, 180000d);
    }

    private static void assertDataForProperty2(List<MonthlyRoomSoldAndRevenueDTO> secondPropertySep, Integer roomsSold, Double revenue) {
        assertEquals(1, secondPropertySep.size());
        MonthlyRoomSoldAndRevenueDTO m3 = secondPropertySep.get(0);
        assertEquals(roomsSold, m3.getMonthlyRoomSold());
        assertEquals(revenue, m3.getMonthlyRevenue().doubleValue());
    }


    @Test
    void  testGetNearbyPropertiesUpsIds(){
        int radiusInMeters = 10000;
        String propertyUPSId = UUID.randomUUID().toString();
        String clientUPSId = UUID.randomUUID().toString();
        List<String> updIds = List.of(UUID.randomUUID().toString(), UUID.randomUUID().toString());
        Property property = createProperty(TestProperty.H1.getId(), TestProperty.H1.name(), TestClient.BSTN.name());
        Client client = new Client();
        client.setUpsClientUuid(clientUPSId);
        property.setClient(client);
        property.setUpsId(propertyUPSId);
        when(propertyService.getPropertyByCode(anyString(),anyString())).thenReturn(property);
        when(propertyService.getNearByPropertiesUpsIds(property.getClient().getUpsClientUuid(),property.getUpsId(),radiusInMeters)).thenReturn(updIds);

        List<String> nearbyPropertiesUpsIds = cloneProjectionBuilderService.getNearbyPropertiesUpsIds(radiusInMeters);
        assertTrue(CollectionUtils.isEqualCollection(updIds,nearbyPropertiesUpsIds));
        verify(propertyService).getPropertyByCode(anyString(),anyString());
        verify(propertyService).getNearByPropertiesUpsIds(anyString(),anyString(),anyInt());
    }

    private static void assertCalculation2(List<MonthlyRoomSoldAndRevenueDTO> firstPropertySep) {
        assertEquals(1, firstPropertySep.size());
        MonthlyRoomSoldAndRevenueDTO dto = firstPropertySep.get(0);
        assertEquals(501, dto.getMonthlyRoomSold());
        assertEquals(55001, dto.getMonthlyRevenue().doubleValue());
        assertData(dto.getMsRoomSoldAndRevenueDTOList().get(0), "BAR", 117, 11667d);
        assertData(dto.getMsRoomSoldAndRevenueDTOList().get(1), "CORP", 75, 10000d);
        assertData(dto.getMsRoomSoldAndRevenueDTOList().get(2), "CONS", 117, 11667d);
        assertData(dto.getMsRoomSoldAndRevenueDTOList().get(3), "CORPORATE", 75, 10000d);
        assertData(dto.getMsRoomSoldAndRevenueDTOList().get(4), "CNR", 117, 11667d);
        assertData(dto.getMsRoomSoldAndRevenueDTOList().get(5), "CNR1", 0, 0d);
    }

    private static void assertCalculations1(List<MonthlyRoomSoldAndRevenueDTO> firstPropertyAug) {
        assertEquals(1, firstPropertyAug.size());
        MonthlyRoomSoldAndRevenueDTO dto = firstPropertyAug.get(0);
        assertEquals(9000, dto.getMonthlyCapacity());
        assertEquals(450, dto.getMonthlyRoomSold());
        assertEquals(70001, dto.getMonthlyRevenue().doubleValue());
        assertData(dto.getMsRoomSoldAndRevenueDTOList().get(0), "BAR", 100, 16667d);
        assertData(dto.getMsRoomSoldAndRevenueDTOList().get(1), "CORP", 75, 10000d);
        assertData(dto.getMsRoomSoldAndRevenueDTOList().get(2), "CONS", 100, 16667d);
        assertData(dto.getMsRoomSoldAndRevenueDTOList().get(3), "CORPORATE", 75, 10000d);
        assertData(dto.getMsRoomSoldAndRevenueDTOList().get(4), "CNR", 100, 16667d);
        assertData(dto.getMsRoomSoldAndRevenueDTOList().get(5), "CNR1", 0, 0d);
    }

    private void assertAugForRelaxedAttributeForUnmappedMS(List<MonthlyRoomSoldAndRevenueDTO> dtoList) {
        assertEquals(1, dtoList.size());
        MonthlyRoomSoldAndRevenueDTO dto = dtoList.get(0);
        assertEquals(470, dto.getMonthlyRoomSold());
        assertEquals(95001, dto.getMonthlyRevenue().doubleValue());
        assertData(dto.getMsRoomSoldAndRevenueDTOList().get(0), "BAR", 100, 16667d);
        assertData(dto.getMsRoomSoldAndRevenueDTOList().get(1), "CORP", 75, 10000d);
        assertData(dto.getMsRoomSoldAndRevenueDTOList().get(2), "CONS", 100, 16667d);
        assertData(dto.getMsRoomSoldAndRevenueDTOList().get(3), "CORPORATE", 75, 10000d);
        assertData(dto.getMsRoomSoldAndRevenueDTOList().get(4), "CNR", 100, 16667d);
        assertData(dto.getMsRoomSoldAndRevenueDTOList().get(5), "CNR1", 10, 12500d);
        assertData(dto.getMsRoomSoldAndRevenueDTOList().get(6), "MS4", 10, 12500d);
    }

    private void assertAugForRelaxedAttributeForMappedMS(List<MonthlyRoomSoldAndRevenueDTO> dtoList) {
        assertEquals(1, dtoList.size());
        MonthlyRoomSoldAndRevenueDTO dto = dtoList.get(0);
        assertEquals(470, dto.getMonthlyRoomSold());
        assertEquals(95001, dto.getMonthlyRevenue().doubleValue());
        assertData(dto.getMsRoomSoldAndRevenueDTOList().get(0), "BAR", 104, 21667d);
        assertData(dto.getMsRoomSoldAndRevenueDTOList().get(1), "CORP", 79, 15000d);
        assertData(dto.getMsRoomSoldAndRevenueDTOList().get(2), "CONS", 104, 21667d);
        assertData(dto.getMsRoomSoldAndRevenueDTOList().get(3), "CORPORATE", 79, 15000d);
        assertData(dto.getMsRoomSoldAndRevenueDTOList().get(4), "CNR", 104, 21667d);
        assertData(dto.getMsRoomSoldAndRevenueDTOList().get(5), "CNR1", 0, 0d);
        assertData(dto.getMsRoomSoldAndRevenueDTOList().get(6), "MS4", 0, 0d);
    }

    private void assertSepForRelaxedAttributeForUnmappedMS(List<MonthlyRoomSoldAndRevenueDTO> dtoList) {
        assertEquals(1, dtoList.size());
        MonthlyRoomSoldAndRevenueDTO dto = dtoList.get(0);
        assertEquals(511, dto.getMonthlyRoomSold());
        assertEquals(70001, dto.getMonthlyRevenue().doubleValue());
        assertData(dto.getMsRoomSoldAndRevenueDTOList().get(0), "BAR", 117, 11667d);
        assertData(dto.getMsRoomSoldAndRevenueDTOList().get(1), "CORP", 75, 10000d);
        assertData(dto.getMsRoomSoldAndRevenueDTOList().get(2), "CONS", 117, 11667d);
        assertData(dto.getMsRoomSoldAndRevenueDTOList().get(3), "CORPORATE", 75, 10000d);
        assertData(dto.getMsRoomSoldAndRevenueDTOList().get(4), "CNR", 117, 11667d);
        assertData(dto.getMsRoomSoldAndRevenueDTOList().get(5), "CNR1", 5, 7500d);
        assertData(dto.getMsRoomSoldAndRevenueDTOList().get(6), "MS4", 5, 7500d);
    }

    private void assertSepForRelaxedAttributeForMappedMS(List<MonthlyRoomSoldAndRevenueDTO> dtoList) {
        assertEquals(1, dtoList.size());
        MonthlyRoomSoldAndRevenueDTO dto = dtoList.get(0);
        assertEquals(511, dto.getMonthlyRoomSold());
        assertEquals(70001, dto.getMonthlyRevenue().doubleValue());
        assertData(dto.getMsRoomSoldAndRevenueDTOList().get(0), "BAR", 119, 14667d);
        assertData(dto.getMsRoomSoldAndRevenueDTOList().get(1), "CORP", 77, 13000d);
        assertData(dto.getMsRoomSoldAndRevenueDTOList().get(2), "CONS", 119, 14667d);
        assertData(dto.getMsRoomSoldAndRevenueDTOList().get(3), "CORPORATE", 77, 13000d);
        assertData(dto.getMsRoomSoldAndRevenueDTOList().get(4), "CNR", 119, 14667d);
        assertData(dto.getMsRoomSoldAndRevenueDTOList().get(5), "CNR1", 0, 0d);
        assertData(dto.getMsRoomSoldAndRevenueDTOList().get(6), "MS4", 0, 0d);
    }

    private static void assertData(MSRoomSoldAndRevenueDTO dto1, String code, Integer roomsSold, Double revenue) {
        assertEquals(code, dto1.getLDBMarketSegment());
        assertEquals(roomsSold, dto1.getRoomSold());
        assertEquals(revenue, dto1.getRevenue().doubleValue());
    }

    private List<MktSegDetailsDto> getMockDataForLDB() {
        MktSegDetailsDto dto1 = createMktSegDTOWithAttrbuted(5, "BAR", "BAR", 2, 3, 1, 0, 0, 0, 0, 0);
        MktSegDetailsDto dto2 = createMktSegDTOWithAttrbuted(5, "CORP", "CORP", 2, 2, 1, 0, 0, 0, 0, 0);
        MktSegDetailsDto dto3 = createMktSegDTOWithAttrbuted(5, "CONS", "CONS", 2, 3, 1, 0, 0, 0, 0, 0);
        MktSegDetailsDto dto4 = createMktSegDTOWithAttrbuted(5, "CORPORATE", "CORPORATE", 2, 2, 1, 0, 0, 0, 0, 0);
        MktSegDetailsDto dto5 = createMktSegDTOWithAttrbuted(5, "CNR", "CNR", 2, 3, 1, 0, 0, 0, 0, 0);
        MktSegDetailsDto dto6 = createMktSegDTOWithAttrbuted(5, "CNR1", "CNR1", 2, 1, 0, 0, 0, 0, 0, 0);
        MktSegDetailsDto dto7 = createMktSegDTOWithAttrbuted(5, "MS4", "MS4", 2, 1, 0, 0, 0, 0, 0, 0);
        return new ArrayList<>(List.of(dto1, dto2, dto3, dto4, dto5, dto6,dto7));
    }

    private List<Object[]> getMockDataForProperty2() {
        // Second Property AUG
        Object[] dto1 = createMktSegDTO(7, "DISC", "DISC", 2, 3, 1, 0, 0, 0, 0, 0, 8, 120, 15000);
        Object[] dto2 = createMktSegDTO(7, "MS1", "MS1", 2, 3, 1, 0, 0, 0, 0, 0, 8, 90, 20000);
        Object[] dto3 = createMktSegDTO(7, "MS2", "MS2", 2, 3, 1, 0, 0, 0, 0, 0, 8, 100, 30000);
        Object[] dto4 = createMktSegDTO(7, "MS3", "MS3", 2, 2, 1, 0, 0, 0, 0, 0, 8, 100, 30000);
        // Second Property SEPT
        Object[] dto5 = createMktSegDTO(7, "DISC", "DISC", 2, 3, 1, 0, 0, 0, 0, 0, 9, 130, 25000);
        Object[] dto6 = createMktSegDTO(7, "MS1", "MS1", 2, 3, 1, 0, 0, 0, 0, 0, 9, 100, 20000);
        Object[] dto7 = createMktSegDTO(7, "MS2", "MS2", 2, 3, 1, 0, 0, 0, 0, 0, 9, 100, 30000);
        Object[] dto8 = createMktSegDTO(7, "MS3", "MS3", 2, 2, 1, 0, 0, 0, 0, 0, 9, 100, 30000);
        Object[] dto9 = createMktSegDTO(7, "MS4_DEF", "MS4", 2, 2, 1, 0, 0, 0, 0, 0, 9, 150, 30000);
        Object[] dto10 = createMktSegDTO(7, "MS4_QY", "MS4", 2, 2, 1, 0, 0, 0, 0, 0, 9, 100, 30000);
        return List.of(dto1, dto2, dto3, dto4, dto5, dto6, dto7, dto8,dto9,dto10);
    }

    private List<Object[]> getMockDataForProperty1() {
        // First Property AUG
        Object[] dto1 = createMktSegDTO(6, "BART", "BART", 2, 3, 1, 0, 0, 0, 0, 0, 8, 100, 20000);
        Object[] dto2 = createMktSegDTO(6, "COM", "COMP", 2, 3, 1, 0, 0, 0, 0, 0, 8, 200, 30000);
        Object[] dto3 = createMktSegDTO(6, "DISC_QY", "DISC_QY", 2, 2, 1, 0, 0, 0, 0, 0, 8, 150, 20000);
        // First Property SEP
        Object[] dto4 = createMktSegDTO(6, "BART", "BART", 2, 3, 1, 0, 0, 0, 0, 0, 9, 150, 10000);
        Object[] dto5 = createMktSegDTO(6, "COM", "COMP", 2, 3, 1, 0, 0, 0, 0, 0, 9, 200, 25000);
        Object[] dto6 = createMktSegDTO(6, "DISC_QY", "DISC_QY", 2, 2, 1, 0, 0, 0, 0, 0, 9, 150, 20000);
        return List.of(dto1, dto2, dto3, dto4, dto5, dto6);
    }

    private List<Object[]> getMockDataForPropertyForRelaxedToUnpairedMS(int propertyId) {
        Object[] dto1 = createMktSegDTO(propertyId, "CMP1", "CMP1", 2, 1, 0, 1, 0, 0, 0, 0, 8, 20, 25000);
        // First Property SEP
        Object[] dto2= createMktSegDTO(propertyId, "CMP2", "CMP2", 2, 1, 0, 1, 0, 0, 0, 0, 9, 10, 15000);
        return List.of(dto1, dto2);
    }

    private List<Object[]> getMockDataForPropertyForRelaxedToPairedMS(int propertyId) {
        Object[] dto1 = createMktSegDTO(propertyId, "CMP1", "CMP1", 2, 1, 1, 0, 0, 0, 0, 0, 8, 20, 25000);
        // First Property SEP
        Object[] dto2= createMktSegDTO(propertyId, "CMP2", "CMP2", 2, 1, 1, 0, 0, 0, 0, 0, 9, 10, 15000);
        return List.of(dto1, dto2);
    }

    private static void assertData1(Map<Integer, Map<String, List<String>>> mappedMS) {
        Map<String, List<String>> mappedMS1 = mappedMS.get(6);
        assertTrue(mappedMS1.containsKey("BAR"));
        assertTrue(mappedMS1.containsKey("CORP"));
        List<String> list = mappedMS1.get("BAR");
        assertEquals(2, list.size());
        assertTrue(list.stream().anyMatch(m -> m.equals("BART")));
        assertTrue(list.stream().anyMatch(m -> m.equals("COM")));

        list = mappedMS1.get("CORP");
        assertEquals(1, list.size());
        assertEquals("DISC_QY", list.get(0));
    }

    private static void assertData2(Map<Integer, Map<String, List<String>>> mappedMS) {
        Map<String, List<String>> mappedMS1 = mappedMS.get(7);
        assertEquals(1, mappedMS1.size());
        assertTrue(mappedMS1.containsKey("BAR"));
        List<String> list = mappedMS1.get("BAR");
        assertEquals(2, list.size());
        assertTrue(list.stream().anyMatch(m -> m.equals("DISC")));
        assertTrue(list.stream().anyMatch(m -> m.equals("MS2")));
    }

    private Property createProperty(Integer propertyId, String code, String clientCode) {
        Property property = new Property();
        property.setId(propertyId);
        property.setCode(code);
        Client client = new Client(2);
        client.setCode(clientCode);
        property.setClient(client);
        return property;
    }

    private MktSegDetailsDto createMktSegDTO1(String code, String originalCode, Integer busisnessTypeId) {
        MktSegDetailsDto dto = new MktSegDetailsDto();
        dto.setCode(code);
        dto.setOriginalMSCode(originalCode);
        dto.setBusinessTypeId(busisnessTypeId);
        dto.setYieldType(3);
        dto.setQualified(1);
        dto.setPriceByBar(0);
        dto.setFenced(0);
        dto.setPackageValue(0);
        dto.setBlockBookingPC(0);
        dto.setLinked(0);
        return dto;
    }

    private MktSegDetailsDto createMktSegDTOWithAttrbuted(Integer propertyId, String code, String originalCode, Integer busisnessTypeId,
                                                          Integer yieldTypeId, Integer qualified,
                                                          Integer fenced, Integer package1, Integer pricedByBAR,
                                                          Integer linked, Integer blockBooking) {
        MktSegDetailsDto dto = new MktSegDetailsDto();
        dto.setPropertyId(propertyId);
        dto.setCode(code);
        dto.setOriginalMSCode(originalCode);
        dto.setBusinessTypeId(busisnessTypeId);
        dto.setYieldType(yieldTypeId);
        dto.setQualified(qualified);
        dto.setFenced(fenced);
        dto.setPackageValue(package1);
        dto.setPriceByBar(pricedByBAR);
        dto.setLinked(linked);
        dto.setBlockBookingPC(blockBooking);
        dto.setAttribute();
        return dto;
    }

    private Object[] createMktSegDTO(String code, String originalCode, Integer busisnessTypeId) {
        Object[] dto = new Object[14];
        dto[1] = code;
        dto[2] = originalCode;
        dto[3] = busisnessTypeId;
        dto[4] = 3;
        dto[5] = 1;
        dto[6] = 0;
        dto[7] = 0;
        dto[8] = 0;
        dto[9] = 0;
        dto[10] = 0;
        return dto;
    }


    private Object[] createMktSegDTO(Integer propertyId, String code, String originalCode, Integer busisnessTypeId,
                                     Integer yieldTypeId, Integer qualified,
                                     Integer fenced, Integer package1, Integer pricedByBAR, Integer linked, Integer blockBooking,
                                     Integer month, Integer sundayRoomsSold, Integer revenue) {
        return new Object[] {
                propertyId, code, originalCode, busisnessTypeId, yieldTypeId, qualified, fenced, package1,
                pricedByBAR, linked, blockBooking, month, BigDecimal.valueOf(sundayRoomsSold),BigDecimal.ZERO,BigDecimal.ZERO,BigDecimal.ZERO,BigDecimal.ZERO,BigDecimal.ZERO,BigDecimal.ZERO, BigDecimal.valueOf(revenue)
        };
    }

    private void populateIPCFGWithActiveDates(Integer propertyId,LocalDate startDate, LocalDate endDate) {
        multiPropertyCrudService().executeNativeUpdateOnSingleProperty(propertyId,INSERT_INTO_IP_CFG,
                QueryParameter.with("propertyId",propertyId).and("startDate",startDate).and("endDate",endDate).parameters());
    }

    private List<Integer> getTestProperties(){
        List<Integer> properties = new ArrayList<>();
        properties.add(TestProperty.H1.getId());
        properties.add(TestProperty.H2.getId());
        return properties;
    }

    private void setUpBusinessMixDTO(){
        cloneProjectionBuilderService.setCloneProjectionBuilderRepository(mockRepository);
        when(mockRepository.getBusinessMixDTOS(anyList(),any(),any())).thenReturn(getBusinessMixDTOS());
    }
    private List<BusinessMixDTO> getBusinessMixDTOS(){
        List<BusinessMixDTO> businessMixDTOS = new ArrayList<>();
        BusinessMixDTO dto1 = new BusinessMixDTO(TestProperty.H1.getId(),BUSINESS_TYPE_GROUP_ID,40.0,100.0);
        BusinessMixDTO dto2 = new BusinessMixDTO(TestProperty.H1.getId(),BUSINESS_TYPE_TRANSIENT_ID,60.0,100.0);
        BusinessMixDTO dto3 = new BusinessMixDTO(TestProperty.H2.getId(),BUSINESS_TYPE_GROUP_ID,50.0,110.0);
        BusinessMixDTO dto4 = new BusinessMixDTO(TestProperty.H2.getId(),BUSINESS_TYPE_TRANSIENT_ID,50.0,110.0);
        businessMixDTOS.add(dto1);
        businessMixDTOS.add(dto2);
        businessMixDTOS.add(dto3);
        businessMixDTOS.add(dto4);
        return businessMixDTOS;
    }
    private List<MonthlyRoomSoldAndRevenueDTO> getMonthlyRoomSoldAndRevenueDTOS(){
        List<MonthlyRoomSoldAndRevenueDTO> monthlyRoomSoldAndRevenueDTOS = new ArrayList<>();
        monthlyRoomSoldAndRevenueDTOS.add(getRoomSoldAndRevenueDTO(TestProperty.H1.getId(),Month.JANUARY,2604,827,77,102482.0,7799.0));
        monthlyRoomSoldAndRevenueDTOS.add(getRoomSoldAndRevenueDTO(TestProperty.H1.getId(),Month.FEBRUARY,2436,751,52,87754.0,5153.0));
        monthlyRoomSoldAndRevenueDTOS.add(getRoomSoldAndRevenueDTO(TestProperty.H1.getId(),Month.MARCH,2604,867,107,131766.0,11235.0));

        monthlyRoomSoldAndRevenueDTOS.add(getRoomSoldAndRevenueDTO(TestProperty.H2.getId(),Month.JANUARY,2666,787,323,120532.0,36316.0));
        monthlyRoomSoldAndRevenueDTOS.add(getRoomSoldAndRevenueDTO(TestProperty.H2.getId(),Month.FEBRUARY,2494,609,361,95661.0,47068.0));
        monthlyRoomSoldAndRevenueDTOS.add(getRoomSoldAndRevenueDTO(TestProperty.H2.getId(),Month.MARCH,2666,653,	245,111699.0,35401.0));

        return monthlyRoomSoldAndRevenueDTOS;
    }
    private MonthlyRoomSoldAndRevenueDTO getRoomSoldAndRevenueDTO(Integer propertyID,Month month,Integer monthlyCapacity, Integer rs1,Integer rs2, Double rev1,Double rev2){
        List<MSRoomSoldAndRevenueDTO> list = new ArrayList<>();
        list.add(new MSRoomSoldAndRevenueDTO(MS1,rs1, BigDecimal.valueOf(rev1)));
        list.add(new MSRoomSoldAndRevenueDTO(MS2,rs2,BigDecimal.valueOf(rev2)));
        BigDecimal monthlyRev = BigDecimal.valueOf(rev1+rev2);
        return new MonthlyRoomSoldAndRevenueDTO(propertyID,month,monthlyCapacity,rs1+rs2,monthlyRev, list);
    }

    @Test
    public void testSaveMonthlyRatio() {
        Map<String, Double> totalOccupancyRatios = Map.of("Jan-2024", 0.50, "Feb-2024", 0.60, "Mar-2024", 0.74);
        cloneProjectionBuilderService.saveMonthlyRatio(totalOccupancyRatios, new HashMap<>());
        List<LDBMonthlyRatio> monthlyRatios = tenantCrudService().findAll(LDBMonthlyRatio.class);
        assertEquals(1,monthlyRatios.size());
        assertEquals(0.50, monthlyRatios.get(0).getJanRatio());
        assertEquals(0.60, monthlyRatios.get(0).getFebRatio());
        assertEquals(0.74, monthlyRatios.get(0).getMarRatio());
        assertEquals(0.0, monthlyRatios.get(0).getAprRatio());
    }


    @Test
    void testSaveMSMonthlyRatio() {
        Map<String, Double> totalOccupancyRatios1 = Map.of("Jan-2024", 0.50, "Feb-2024", 0.60, "Mar-2024", 0.74);
        Map<String, Double> totalOccupancyRatios2 = Map.of("Jan-2024", 0.60, "Feb-2024", 0.70, "Mar-2024", 0.84);
        Map<String, Double> totalOccupancyRatios3 = Map.of("Jan-2024", 0.70, "Feb-2024", 0.80, "Mar-2024", 0.94);
        Map<String, Map<String, Double>> mockData = Map.of("MS1", totalOccupancyRatios1,"MS2", totalOccupancyRatios2,"MS3", totalOccupancyRatios3);
        cloneProjectionBuilderService.saveLDBMSMonthlyRatio(mockData, new HashMap<>());
        List<LDBMSMonthlyRatio> monthlyRatios = tenantCrudService().findAll(LDBMSMonthlyRatio.class);
        assertEquals(3,monthlyRatios.size());
        Optional<LDBMSMonthlyRatio> ms1 = monthlyRatios.stream().filter(m -> m.getMarketSegmentCode().equals("MS1")).findFirst();
        Optional<LDBMSMonthlyRatio> ms2 = monthlyRatios.stream().filter(m -> m.getMarketSegmentCode().equals("MS1")).findFirst();
        Optional<LDBMSMonthlyRatio> ms3 = monthlyRatios.stream().filter(m -> m.getMarketSegmentCode().equals("MS1")).findFirst();
        assertData(ms1.get(), 0.5, 0.6, 0.74);
        assertData(ms2.get(), 0.6, 0.7, 0.84);
        assertData(ms3.get(), 0.7, 0.8, 0.94);
    }

    private static void assertData(LDBMSMonthlyRatio ms1, double janRatio, double febRatio, double marRatio) {
        assertEquals(0.50, ms1.getJanRatio());
        assertEquals(0.60, ms1.getFebRatio());
        assertEquals(0.74, ms1.getMarRatio());
        assertEquals(0.0, ms1.getAprRatio());
    }

}
