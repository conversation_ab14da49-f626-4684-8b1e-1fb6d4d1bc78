package com.ideas.tetris.pacman.services.informationmanager.alert.service;

import com.ideas.g3.test.AbstractG3JupiterTest;
import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.configparams.GUIConfigParamName;
import com.ideas.tetris.pacman.common.configparams.IntegrationConfigParamName;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomClass;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomType;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.informationmanager.dto.AlertType;
import com.ideas.tetris.pacman.services.informationmanager.entity.InfoMgrTypeEntity;
import com.ideas.tetris.pacman.services.product.Product;
import com.ideas.tetris.pacman.services.property.PropertyService;
import com.ideas.tetris.pacman.testdatabuilder.AccomTypeBuilder;
import com.ideas.tetris.pacman.testdatabuilder.ProductBuilder;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.externalsystem.ReservationSystem;
import com.ideas.tetris.platform.common.xml.schema.workcontext.v1.WorkContextType;
import com.ideas.tetris.platform.services.Stage;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.joda.time.LocalDate;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.util.HashMap;
import java.util.List;

import static org.mockito.Mockito.any;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.eq;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.testng.Assert.assertEquals;
import static org.testng.Assert.assertFalse;
import static org.testng.Assert.assertTrue;

@MockitoSettings(strictness = Strictness.LENIENT)
public class RoomTypeOffsetConfigurationAlertEvaluationServiceTest extends AbstractG3JupiterTest {

    @InjectMocks
    private RoomTypeOffsetConfigurationAlertEvaluationService service;

    @Mock
    private DateService dateService;

    @Mock
    private AlertService alertService;

    @Mock
    private PacmanConfigParamsService configParamsService;

    @Mock
    private PropertyService propertyService;

    @BeforeEach
    public void setUp() {
        service.crudService = tenantCrudService();
        tenantCrudService().executeUpdateByNativeQuery("update Accom_Type set Status_ID = 2");
        tenantCrudService().executeUpdateByNativeQuery("update Product set Status_ID = 2 where code <> 'BAR'");
        tenantCrudService().executeUpdateByNativeQuery("delete from Daily_Bar_Rate_Chart");
        tenantCrudService().executeUpdateByNativeQuery("delete from Daily_Bar_Config");
        tenantCrudService().executeUpdateByNativeQuery("insert into Daily_Bar_Rate_Chart values ('Fixed', 5.0, 5.0, 5.0, 5.0, 5.0, 5.0, 5.0, 5.0, 3, 1, GETDATE(), 1, GETDATE());");
        doReturn(true).when(propertyService).isStageAtLeast(eq(Stage.ONE_WAY));
    }

    // ESA Off - Valid BAR
    @Test
    public void getDefaulters_ESAOff_ValidBAR() {
        // Given
        LocalDate startDate = LocalDate.parse("2018-04-15");
        doReturn(LocalDate.parse("2018-04-30").toDate()).when(dateService).getDecisionUploadWindowEndDate();
        AccomClass roomClass = tenantCrudService().findAll(AccomClass.class).get(0);
        AccomType rt1 = tenantCrudService().save(AccomTypeBuilder.buildAccomType(roomClass, "RT1"));
        setESAEnabled(false);
        Product bar = tenantCrudService().findByNamedQuerySingleResult(Product.GET_BY_CODE, QueryParameter.with("code", "BAR").parameters());
        insertOffsets(rt1, bar, startDate, LocalDate.parse("2018-05-15"));
        // When
        List<String> defaulters = service.getDefaulters();
        // Then
        assertTrue(defaulters.isEmpty());
    }

    // ESA Off - Invalid BAR
    @Test
    public void getDefaulters_ESAOff_InValidBAR() {
        // Given
        LocalDate startDate = LocalDate.parse("2018-04-15");
        doReturn(LocalDate.parse("2018-04-30").toDate()).when(dateService).getDecisionUploadWindowEndDate();
        AccomClass roomClass = tenantCrudService().findAll(AccomClass.class).get(0);
        AccomType rt1 = tenantCrudService().save(AccomTypeBuilder.buildAccomType(roomClass, "RT1"));
        setESAEnabled(false);
        Product bar = tenantCrudService().findByNamedQuerySingleResult(Product.GET_BY_CODE, QueryParameter.with("code", "BAR").parameters());
        insertOffsets(rt1, bar, startDate, LocalDate.parse("2018-05-01"));
        // When
        List<String> defaulters = service.getDefaulters();
        // Then
        assertFalse(defaulters.isEmpty());
        assertEquals(defaulters.size(), 1);
        assertEquals(defaulters.get(0), "RT1:   BAR");
    }

    // ESA Off - Valid Bar, Valid P1
    @Test
    public void getDefaulters_ESAOff_ValidBAR_ValidP1() {
        // Given
        LocalDate startDate = LocalDate.parse("2018-04-15");
        doReturn(LocalDate.parse("2018-04-30").toDate()).when(dateService).getDecisionUploadWindowEndDate();
        AccomClass roomClass = tenantCrudService().findAll(AccomClass.class).get(0);
        AccomType rt1 = tenantCrudService().save(AccomTypeBuilder.buildAccomType(roomClass, "RT1"));
        setESAEnabled(false);
        Product bar = tenantCrudService().findByNamedQuerySingleResult(Product.GET_BY_CODE, QueryParameter.with("code", "BAR").parameters());
        Product p1 = tenantCrudService().save(ProductBuilder.createExtendedStayProduct("Weekly"));
        insertOffsets(rt1, bar, startDate, LocalDate.parse("2018-05-15"));
        insertOffsets(rt1, p1, startDate, LocalDate.parse("2018-05-15"));
        // When
        List<String> defaulters = service.getDefaulters();
        // Then
        assertTrue(defaulters.isEmpty());
    }

    // ESA Off - Valid Bar, Invalid P1
    @Test
    public void getDefaulters_ESAOff_ValidBAR_InValidP1() {
        // Given
        LocalDate startDate = LocalDate.parse("2018-04-15");
        doReturn(LocalDate.parse("2018-04-30").toDate()).when(dateService).getDecisionUploadWindowEndDate();
        AccomClass roomClass = tenantCrudService().findAll(AccomClass.class).get(0);
        AccomType rt1 = tenantCrudService().save(AccomTypeBuilder.buildAccomType(roomClass, "RT1"));
        setESAEnabled(false);
        Product bar = tenantCrudService().findByNamedQuerySingleResult(Product.GET_BY_CODE, QueryParameter.with("code", "BAR").parameters());
        Product p1 = tenantCrudService().save(ProductBuilder.createExtendedStayProduct("Weekly"));
        insertOffsets(rt1, bar, startDate, LocalDate.parse("2018-05-15"));
        insertOffsets(rt1, p1, startDate, LocalDate.parse("2018-05-01"));
        // When
        List<String> defaulters = service.getDefaulters();
        // Then
        assertTrue(defaulters.isEmpty());
    }

    // ESA On - Valid Bar, Valid P1
    @Test
    public void getDefaulters_ESAOn_ValidBAR_ValidP1() {
        // Given
        LocalDate startDate = LocalDate.parse("2018-04-15");
        doReturn(LocalDate.parse("2018-04-30").toDate()).when(dateService).getDecisionUploadWindowEndDate();
        AccomClass roomClass = tenantCrudService().findAll(AccomClass.class).get(0);
        AccomType rt1 = tenantCrudService().save(AccomTypeBuilder.buildAccomType(roomClass, "RT1"));
        setESAEnabled(true);
        Product bar = tenantCrudService().findByNamedQuerySingleResult(Product.GET_BY_CODE, QueryParameter.with("code", "BAR").parameters());
        Product p1 = tenantCrudService().save(ProductBuilder.createExtendedStayProduct("Weekly"));
        insertOffsets(rt1, bar, startDate, LocalDate.parse("2018-05-15"));
        insertOffsets(rt1, p1, startDate, LocalDate.parse("2018-05-15"));
        // When
        List<String> defaulters = service.getDefaulters();
        // Then
        assertTrue(defaulters.isEmpty());
    }

    // ESA On - Valid Bar, InValid P1
    @Test
    public void getDefaulters_ESAOn_ValidBAR_InValidP1() {
        // Given
        LocalDate startDate = LocalDate.parse("2018-04-15");
        doReturn(LocalDate.parse("2018-04-30").toDate()).when(dateService).getDecisionUploadWindowEndDate();
        AccomClass roomClass = tenantCrudService().findAll(AccomClass.class).get(0);
        AccomType rt1 = tenantCrudService().save(AccomTypeBuilder.buildAccomType(roomClass, "RT1"));
        setESAEnabled(true);
        Product bar = tenantCrudService().findByNamedQuerySingleResult(Product.GET_BY_CODE, QueryParameter.with("code", "BAR").parameters());
        Product p1 = tenantCrudService().save(ProductBuilder.createExtendedStayProduct("Weekly"));
        insertOffsets(rt1, bar, startDate, LocalDate.parse("2018-05-15"));
        insertOffsets(rt1, p1, startDate, LocalDate.parse("2018-05-01"));
        // When
        List<String> defaulters = service.getDefaulters();
        // Then
        assertFalse(defaulters.isEmpty());
        assertEquals(defaulters.size(), 1);
        assertEquals(defaulters.get(0), "RT1:   Weekly");
    }

    // ESA On - InValid Bar, Valid P1
    @Test
    public void getDefaulters_ESAOn_InValidBAR_ValidP1() {
        // Given
        LocalDate startDate = LocalDate.parse("2018-04-15");
        doReturn(LocalDate.parse("2018-04-30").toDate()).when(dateService).getDecisionUploadWindowEndDate();
        AccomClass roomClass = tenantCrudService().findAll(AccomClass.class).get(0);
        AccomType rt1 = tenantCrudService().save(AccomTypeBuilder.buildAccomType(roomClass, "RT1"));
        setESAEnabled(true);
        Product bar = tenantCrudService().findByNamedQuerySingleResult(Product.GET_BY_CODE, QueryParameter.with("code", "BAR").parameters());
        Product p1 = tenantCrudService().save(ProductBuilder.createExtendedStayProduct("Weekly"));
        insertOffsets(rt1, bar, startDate, LocalDate.parse("2018-05-01"));
        insertOffsets(rt1, p1, startDate, LocalDate.parse("2018-05-15"));
        // When
        List<String> defaulters = service.getDefaulters();
        // Then
        assertFalse(defaulters.isEmpty());
        assertEquals(defaulters.size(), 1);
        assertEquals(defaulters.get(0), "RT1:   BAR");
    }

    // ESA On - InValid Bar, InValid P1
    @Test
    public void getDefaulters_ESAOn_InValidBAR_InValidP1() {
        // Given
        LocalDate startDate = LocalDate.parse("2018-04-15");
        doReturn(LocalDate.parse("2018-04-30").toDate()).when(dateService).getDecisionUploadWindowEndDate();
        AccomClass roomClass = tenantCrudService().findAll(AccomClass.class).get(0);
        AccomType rt1 = tenantCrudService().save(AccomTypeBuilder.buildAccomType(roomClass, "RT1"));
        setESAEnabled(true);
        Product bar = tenantCrudService().findByNamedQuerySingleResult(Product.GET_BY_CODE, QueryParameter.with("code", "BAR").parameters());
        Product p1 = tenantCrudService().save(ProductBuilder.createExtendedStayProduct("Weekly"));
        insertOffsets(rt1, bar, startDate, LocalDate.parse("2018-05-01"));
        insertOffsets(rt1, p1, startDate, LocalDate.parse("2018-05-01"));
        // When
        List<String> defaulters = service.getDefaulters();
        // Then
        assertFalse(defaulters.isEmpty());
        assertEquals(defaulters.size(), 1);
        assertEquals(defaulters.get(0), "RT1:   BAR,Weekly");
    }

    private void setESAEnabled(boolean enabled) {
        doReturn(enabled ? ReservationSystem.REZVIEW.getConfigParameterValue() : "Hilton").when(configParamsService).getParameterValue(IntegrationConfigParamName.CORE_PROPERTY_EXTERNAL_SYSTEM);
    }
    @Test
    public void evaluate_RT1P1Valid_RT1P2Valid_RT2P1Valid() {
        // GIVEN
        LocalDate startDate = LocalDate.parse("2018-04-15");
        doReturn(startDate.toDate()).when(dateService).getCaughtUpDate();
        doReturn(LocalDate.parse("2018-04-30").toDate()).when(dateService).getDecisionUploadWindowEndDate();
        InfoMgrTypeEntity entity = tenantCrudService().findByNamedQuerySingleResult(InfoMgrTypeEntity.BY_NAME, QueryParameter.with("name", AlertType.RoomTypeOffsetConfigurationExpiring.name()).parameters());
        doReturn(entity).when(alertService).getAlertType(anyString());
        AccomClass roomClass = tenantCrudService().findAll(AccomClass.class).get(0);
        AccomType rt1 = tenantCrudService().save(AccomTypeBuilder.buildAccomType(roomClass, "RT1"));
        AccomType rt2 = tenantCrudService().save(AccomTypeBuilder.buildAccomType(roomClass, "RT2"));
        Product p1 = tenantCrudService().save(ProductBuilder.createExtendedStayProduct("Weekly"));
        Product p2 = tenantCrudService().save(ProductBuilder.createExtendedStayProduct("Bi-Weekly"));
        insertOffsets(rt1, p1, startDate, LocalDate.parse("2018-05-15"));
        insertOffsets(rt1, p2, startDate, LocalDate.parse("2018-05-15"));
        insertOffsets(rt2, p1, startDate, LocalDate.parse("2018-05-15"));
        when(alertService.isAlertEnable(AlertType.RoomTypeOffsetConfigurationExpiring)).thenReturn(true);
        // WHEN
        service.evaluate();
        // THEN
        verify(alertService, never()).resolveAllAlerts(AlertType.RoomTypeOffsetConfigurationExpiring, 5);
        verify(alertService, Mockito.times(1)).createAlert(any(), any(), anyString(), anyString(), any());
    }

    @Test
    public void evaluate_RT1P1Valid_RT1P2Valid_RT2P1Invalid() {
        // GIVEN
        LocalDate startDate = LocalDate.parse("2018-04-15");
        doReturn(LocalDate.parse("2018-04-30").toDate()).when(dateService).getDecisionUploadWindowEndDate();
        doReturn(startDate.toDate()).when(dateService).getCaughtUpDate();
        InfoMgrTypeEntity entity = tenantCrudService().findByNamedQuerySingleResult(InfoMgrTypeEntity.BY_NAME, QueryParameter.with("name", AlertType.RoomTypeOffsetConfigurationExpiring.name()).parameters());
        doReturn(entity).when(alertService).getAlertType(anyString());
        AccomClass roomClass = tenantCrudService().findAll(AccomClass.class).get(0);
        AccomType rt1 = tenantCrudService().save(AccomTypeBuilder.buildAccomType(roomClass, "RT1"));
        AccomType rt2 = tenantCrudService().save(AccomTypeBuilder.buildAccomType(roomClass, "RT2"));
        Product p1 = tenantCrudService().save(ProductBuilder.createExtendedStayProduct("Weekly"));
        Product p2 = tenantCrudService().save(ProductBuilder.createExtendedStayProduct("Bi-Weekly"));
        insertOffsets(rt1, p1, startDate, LocalDate.parse("2018-05-15"));
        insertOffsets(rt1, p2, startDate, LocalDate.parse("2018-05-15"));
        insertOffsets(rt2, p1, startDate, LocalDate.parse("2018-05-01"));
        when(alertService.isAlertEnable(AlertType.RoomTypeOffsetConfigurationExpiring)).thenReturn(true);
        // WHEN
        service.evaluate();
        // THEN
        verify(alertService, never()).resolveAllAlerts(AlertType.RoomTypeOffsetConfigurationExpiring, 5);
        verify(alertService, times(1)).createAlert(any(), any(), anyString(), anyString(), any());
    }

    @Test
    public void evaluate_CP_NOOffsets() {
        // GIVEN: Following configurations
        doReturn(false).when(service.configParamsService).getBooleanParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED);
        // WHEN: The method is called
        service.evaluate();
        // THEN: The method should not return anything
        verify(alertService, never()).resolveAllAlerts(AlertType.RoomTypeOffsetConfigurationExpiring, 5);
        verify(alertService, never()).createAlert(any(WorkContextType.class), any(InfoMgrTypeEntity.class), anyString(), anyString(), any(AlertType.class));
    }

    @Test
    public void evaluate_StageLessThanOneWay() {
        // GIVEN: Following configurations
        doReturn(false).when(propertyService).isStageAtLeast(eq(Stage.ONE_WAY));
        // WHEN: The method is called
        service.evaluate();
        // THEN: The method should not return anything
        verify(alertService, never()).resolveAllAlerts(AlertType.RoomTypeOffsetConfigurationExpiring, 5);
        verify(alertService, never()).createAlert(any(WorkContextType.class), any(InfoMgrTypeEntity.class), anyString(), anyString(), any(AlertType.class));
    }

    @Test
    public void evaluate_NoCP_DailyBarConfigurationEnabledFalse() {
        // GIVEN: Following configurations
        doReturn(false).when(service.configParamsService).getBooleanParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED);
        doReturn(false).when(configParamsService).getBooleanParameterValue(FeatureTogglesConfigParamName.DAILY_BAR_CONFIGURATION_ENABLED);
        // WHEN: The method is called
        service.evaluate();
        // THEN: The method should not return anything
        verify(alertService, never()).resolveAllAlerts(AlertType.RoomTypeOffsetConfigurationExpiring, 5);
        verify(alertService, never()).createAlert(any(WorkContextType.class), any(InfoMgrTypeEntity.class), anyString(), anyString(), any(AlertType.class));
    }

    private void insertOffsets(AccomType rt, Product p, LocalDate startDate, LocalDate endDate) {
        int dailyBarRateChartID = tenantCrudService().findByNativeQuerySingleResult("select TOP 1 Daily_Bar_Rate_Chart_ID from Daily_Bar_Rate_Chart", new HashMap<>());
        String sql = "insert into Daily_Bar_Config values (null, " + "'" + rt.getAccomTypeCode() + "'" + "," + "'" + startDate.toString("yyyy-MM-dd") + "'" + "," + "'" + endDate.toString("yyyy-MM-dd") + "'" + "," + dailyBarRateChartID + "," + dailyBarRateChartID + "," + dailyBarRateChartID + "," + dailyBarRateChartID + "," + "'" + p.getCode() + "'" + "," + p.getId() + "," + rt.getId() + "," + "1, GETDATE(), 1, GETDATE()" + ")";
        tenantCrudService().executeUpdateByNativeQuery(sql);
    }
}
