package com.ideas.tetris.pacman.services.bestavailablerate;

import com.ideas.g3.test.AbstractG3JupiterTest;
import com.ideas.tetris.pacman.common.configparams.IPConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomClass;
import com.ideas.tetris.pacman.services.accommodation.service.AccommodationService;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.Decision;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.DecisionBAROutput;
import com.ideas.tetris.pacman.services.bestavailablerate.rowmapper.OccupancyDateRevenueOccupancyDtoRowMapper;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.heatmap.entity.HeatMapRangeAndColors;
import com.ideas.tetris.pacman.services.heatmap.entity.HeatMapRangeAndColorsConfig;
import com.ideas.tetris.pacman.services.heatmap.entity.HeatMapRangeAndColorsConfigType;
import com.ideas.tetris.pacman.services.inventorygroup.entity.InventoryGroup;
import com.ideas.tetris.pacman.services.inventorygroup.entity.UniqueInventoryGroupCreator;
import com.ideas.tetris.pacman.services.inventorygroup.entity.UniqueInventoryGroupDetailsCreator;
import com.ideas.tetris.pacman.services.propertygroup.service.PropertyGroupService;
import com.ideas.tetris.pacman.services.unqualifiedrate.entity.RateUnqualified;
import com.ideas.tetris.pacman.services.unqualifiedrate.entity.UniqueRateUnqualified;
import com.ideas.tetris.platform.common.contextholder.PacmanThreadLocalContextHolder;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.common.xml.schema.workcontext.v1.WorkContextType;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.apache.commons.lang.StringUtils;
import org.joda.time.LocalDate;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static com.ideas.tetris.pacman.common.constants.Constants.BAR_DECISION_VALUE_RATEOFDAY;
import static com.ideas.tetris.pacman.common.constants.Constants.CONTEXT_KEY;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@MockitoSettings(strictness = Strictness.LENIENT)
public class HeatMapServiceTest extends AbstractG3JupiterTest {
    private static final int PROPERTY_ID = 5;
    private static final int ACCOM_CLASS_ID = 2;

    @InjectMocks
    private HeatMapService service = new HeatMapService();
    @Mock
    AccommodationService accommodationService;
    @Mock
    HeatMapService hmService = null;
    @Mock
    PacmanConfigParamsService pacmanConfigParamsService;
    @Mock
    DateService dateService;

    private List<String> colors = Arrays.asList("2A44FD", "3794FE", "49DEFF", "70FFA9",
            "A7FF68", "E4FF33", "F89B00", "F50000", "CE021B", "A8071A");
    private boolean fiscalCalendarEnabled;

    @BeforeEach
    public void setUp() {
        DateService dateService = new DateService() {
            @Override
            public Date getCaughtUpDate() {
                return DateUtil.getDateForCurrentMonth(2);
            }

            @Override
            public Date getBusinessDate() {
                return getCaughtUpDate();
            }

            @Override
            public Date getWebRateShoppingDate() {
                return getCaughtUpDate();
            }

            @Override
            public Date getUnqualifiedRateCaughtUpDate() {
                return getCaughtUpDate();
            }

            @Override
            public Date getHeatMapDisplayWindowEndDate() {
                return DateUtil.getLastDayOfNextMonth();
            }

            @Override
            public Date getHeatMapDisplayWindowStartDate() {
                return DateUtil.getFirstDayOfCurrentMonth();
            }
        };

        PacmanConfigParamsService configService = Mockito.mock(PacmanConfigParamsService.class);
        Mockito.when(configService.isEnablePhysicalCapacityConsideration()).thenReturn(false);
        service.setConfigService(configService);

        service.setCrudService(tenantCrudService());
        service.setDateService(dateService);
        service.setMultiPropertyCrudService(multiPropertyCrudService());

        RateDeterminator rateDeterminator = new RateDeterminator();
        rateDeterminator.setCrudService(tenantCrudService());
        service.setRateDeterminator(rateDeterminator);

        service.setConfigService(pacmanConfigParamsService);

        PropertyGroupService propertyGroupService = new PropertyGroupService();
        service.setPropertyGroupService(propertyGroupService);
        WorkContextType wc = new WorkContextType();
        wc.setUserId("1");
        wc.setPropertyId(PROPERTY_ID);
        PacmanThreadLocalContextHolder.put(CONTEXT_KEY, wc);
    }

    @Test
    public void testGetOrderedRatePlanNames() throws Exception {
        service.setDateService(dateService);
        when(dateService.getCaughtUpDate()).thenReturn(Calendar.getInstance().getTime());
        List<String> result = service.getOrderedRatePlanNames(ACCOM_CLASS_ID);
        Assertions.assertNotNull(result);
        Assertions.assertEquals(9, result.size());
        for (int i = 0; i < result.size(); i++) {
            Assertions.assertEquals("LV" + i, result.get(i));
        }
        verify(dateService).getCaughtUpDate();
    }

    @Test
    public void testGetUnqualifiedRateHeatMapData() throws Exception {
        setWorkcontext();
        PacmanConfigParamsService mockConfigParamService = Mockito.mock(PacmanConfigParamsService.class);
        service.setConfigService(mockConfigParamService);
        String context = String.format("%s.%s.%s", Constants.CONFIG_PARAMS_NODE_PREFIX, PacmanWorkContextHelper.getClientCode(), PacmanWorkContextHelper.getPropertyCode());
        Mockito.when(mockConfigParamService.getParameterValue(IPConfigParamName.BAR_BAR_DECISION.value())).thenReturn(BAR_DECISION_VALUE_RATEOFDAY);
        Mockito.when(mockConfigParamService.isLRAEnabled()).thenReturn(false);
        Mockito.when(mockConfigParamService.getBooleanParameterValue(IPConfigParamName.BAR_ENABLE_SINGLE_BAR_DECISION.value())).thenReturn(false);

        Map<Date, String> result = service.getUnqualifiedRateHeatMapData(ACCOM_CLASS_ID);
        Assertions.assertNotNull(result);
        Assertions.assertEquals(DateUtil.getNumberOfDaysInThisMonthAndNext(), result.size());
        List<Date> keys = new ArrayList<Date>(result.keySet());
        Collections.sort(keys);
        // Check the first few results to see if they match known expected values
        Assertions.assertEquals("LV2", result.get(keys.get(0)));
        Assertions.assertEquals("LV3", result.get(keys.get(1)));
        Assertions.assertEquals("LV5", result.get(keys.get(2)));
        Assertions.assertEquals("LV6", result.get(keys.get(3)));

    }

    private DecisionBAROutput createDecisionForLRA(Date arrivalDate, Integer accomClassId, Integer lengthOfStay) {

        Decision decision = new Decision();
        decision.setBusinessDate(DateUtil.addDaysToDate(arrivalDate, 1));
        decision.setCaughtUpDate(DateUtil.addDaysToDate(arrivalDate, 1));
        decision.setRateUnqualifiedDate(arrivalDate);
        decision.setWebRateDate(arrivalDate);
        decision.setDecisionTypeId(1);
        decision.setStartDate(DateUtil.addDaysToDate(arrivalDate, 1));
        decision.setEndDate(DateUtil.addDaysToDate(arrivalDate, 1));
        decision.setProcessStatusId(2);
        decision.setPropertyID(PROPERTY_ID);
        decision = tenantCrudService().save(decision);

        RateUnqualified rateUnqualified = UniqueRateUnqualified.createRateUnqualifiedByDate(arrivalDate, DateUtil.addDaysToDate(arrivalDate, 20));

        DecisionBAROutput decisionBarOutput = new DecisionBAROutput();
        decisionBarOutput.setAccomClassId(accomClassId);
        decisionBarOutput.setArrivalDate(arrivalDate);
        decisionBarOutput.setDecision(decision);
        decisionBarOutput.setPropertyID(PROPERTY_ID);
        decisionBarOutput.setRateUnqualified(rateUnqualified);
        decisionBarOutput.setLengthOfStay(lengthOfStay);
        decisionBarOutput.setOverride("None");
        decisionBarOutput.setReasonTypeId(6);
        decisionBarOutput.setCreateDate(DateUtil.getCurrentDate());
        decisionBarOutput = tenantCrudService().save(decisionBarOutput);
        return decisionBarOutput;
    }

    @Test
    public void shouldGetUnqualifiedRateHeatMapDataWhenLraIsEnabled() throws Exception {
        setWorkcontext();
        Integer lengthOfStay = -1;
        PacmanConfigParamsService mockConfigParamService = Mockito.mock(PacmanConfigParamsService.class);
        service.setConfigService(mockConfigParamService);
        String context = String.format("%s.%s.%s", Constants.CONFIG_PARAMS_NODE_PREFIX, PacmanWorkContextHelper.getClientCode(), PacmanWorkContextHelper.getPropertyCode());
        Mockito.when(mockConfigParamService.isLRAEnabled()).thenReturn(false);
        Mockito.when(mockConfigParamService.getBooleanParameterValue(IPConfigParamName.BAR_ENABLE_SINGLE_BAR_DECISION.value())).thenReturn(false);
        Date currentDate = DateUtil.getCurrentDateWithoutTime();
        DecisionBAROutput decisionBarOutput = createDecisionForLRA(currentDate, ACCOM_CLASS_ID, lengthOfStay);
        Map<Date, String> result = service.getUnqualifiedRateHeatMapData(ACCOM_CLASS_ID, -1, currentDate, currentDate);
        Assertions.assertNotNull(result);
        List<Date> keys = new ArrayList<Date>(result.keySet());
        Collections.sort(keys);
        Assertions.assertEquals("LRA", result.get(keys.get(0)));
    }

    private void setWorkcontext() {
        WorkContextType wc = new WorkContextType();
        wc.setUserId("1");
        wc.setPropertyId(PROPERTY_ID);
        wc.setClientCode("BSTN");
        wc.setPropertyCode("H1");
        PacmanThreadLocalContextHolder.put(CONTEXT_KEY, wc);
    }

    @Test
    public void testGetPeakDemandHeatMapData() throws Exception {
        Map<Date, BigDecimal> result = service.getPeakDemandHeatMapData();
        Assertions.assertNotNull(result);
        Assertions.assertEquals(DateUtil.getNumberOfDaysInThisMonthAndNext(), result.size());
        List<Date> keys = new ArrayList<Date>(result.keySet());
        Collections.sort(keys);
        // Check the first few results to see if they match known expected values
        Assertions.assertEquals(BigDecimal.valueOf(43.75), result.get(keys.get(0)).setScale(2, RoundingMode.HALF_UP));
        Assertions.assertEquals(BigDecimal.valueOf(47.32), result.get(keys.get(1)).setScale(2, RoundingMode.HALF_UP));
        Assertions.assertEquals(BigDecimal.valueOf(50.67), result.get(keys.get(2)).setScale(2, RoundingMode.HALF_UP));
        Assertions.assertEquals(BigDecimal.valueOf(53.79), result.get(keys.get(3)).setScale(2, RoundingMode.HALF_UP));
    }

    @Test
    public void testGetOccupancyForecastHeatMapData() throws Exception {
        Map<Date, BigDecimal> result = service.getOccupancyForecastHeatMapData();
        Assertions.assertNotNull(result);
        Assertions.assertEquals(DateUtil.getNumberOfDaysInThisMonthAndNext(), result.size());
        // Check the first few results to see if they match known expected values
        Assertions.assertEquals(BigDecimal.valueOf(61.607100).setScale(6), result.get(DateUtil.getDateForNextMonth(1)));
        Assertions.assertEquals(BigDecimal.valueOf(65.178500).setScale(6), result.get(DateUtil.getDateForNextMonth(2)));
        Assertions.assertEquals(BigDecimal.valueOf(68.526700).setScale(6), result.get(DateUtil.getDateForNextMonth(3)));
        Assertions.assertEquals(BigDecimal.valueOf(71.651700).setScale(6), result.get(DateUtil.getDateForNextMonth(4)));
    }

    @Test
    public void testGetHotelRevenueHeatMapData() throws Exception {
        Map<Date, BigDecimal> result = service.getHotelRevenueHeatMapData();
        Assertions.assertNotNull(result);

        Assertions.assertEquals(DateUtil.getNumberOfDaysInThisMonthAndNext(), result.size());

        List<Date> keys = new ArrayList<Date>(result.keySet());
        Collections.sort(keys);

        // Check the first few results to see if they match known expected values
        Assertions.assertEquals(new BigDecimal("16740.00000"), result.get(keys.get(0)));
        Assertions.assertEquals(new BigDecimal("8302.00000"), result.get(keys.get(1)));
        Assertions.assertEquals(new BigDecimal("8317.00000"), result.get(keys.get(2)));
        Assertions.assertEquals(new BigDecimal("8331.00000"), result.get(keys.get(3)));
    }

    @Test
    public void testGetHotelADRHeatMapData() throws Exception {
        Map<Date, BigDecimal> result = service.getHotelADRHeatMapData();
        Assertions.assertNotNull(result);

        Assertions.assertEquals(DateUtil.getNumberOfDaysInThisMonthAndNext(), result.size());

        List<Date> keys = new ArrayList<Date>(result.keySet());
        Collections.sort(keys);

        // Check the first few results to see if they match known expected values
        Assertions.assertEquals(new BigDecimal("90.000000"), result.get(keys.get(0)));
        Assertions.assertEquals(new BigDecimal("28.431507"), result.get(keys.get(1)));
        Assertions.assertEquals(new BigDecimal("27.091205"), result.get(keys.get(2)));
        Assertions.assertEquals(new BigDecimal("25.953271"), result.get(keys.get(3)));
    }

    @Test
    public void testGetHotelREVPARHeatMapData() throws Exception {
        Map<Date, BigDecimal> result = service.getHotelREVPARHeatMapData();
        Assertions.assertNotNull(result);

        Assertions.assertEquals(DateUtil.getNumberOfDaysInThisMonthAndNext(), result.size());

        List<Date> keys = new ArrayList<Date>(result.keySet());
        Collections.sort(keys);

        // Check the first few results to see if they match known expected values
        Assertions.assertEquals(new BigDecimal("37.366071"), result.get(keys.get(0)));
        Assertions.assertEquals(new BigDecimal("18.531250"), result.get(keys.get(1)));
        Assertions.assertEquals(new BigDecimal("18.564732"), result.get(keys.get(2)));
        Assertions.assertEquals(new BigDecimal("18.595982"), result.get(keys.get(3)));
    }

    @Test
    public void testGetAccomRevenueHeatMapData() throws Exception {
        Map<Date, BigDecimal> result = service.getAccomClassRevenueHeatMapData(ACCOM_CLASS_ID);
        Assertions.assertNotNull(result);

        Assertions.assertEquals(DateUtil.getNumberOfDaysInThisMonthAndNext(), result.size());

        List<Date> keys = new ArrayList<Date>(result.keySet());
        Collections.sort(keys);

        // Check the first few results to see if they match known expected values
        Assertions.assertEquals(new BigDecimal("10890.00000"), result.get(keys.get(0)));
        Assertions.assertEquals(new BigDecimal("4990.00000"), result.get(keys.get(1)));
        Assertions.assertEquals(new BigDecimal("4999.00000"), result.get(keys.get(2)));
        Assertions.assertEquals(new BigDecimal("5008.00000"), result.get(keys.get(3)));
    }

    @Test
    public void testGetAccomADRHeatMapData() throws Exception {
        Map<Date, BigDecimal> result = service.getAccomClassADRHeatMapData(ACCOM_CLASS_ID);
        Assertions.assertNotNull(result);

        Assertions.assertEquals(DateUtil.getNumberOfDaysInThisMonthAndNext(), result.size());

        List<Date> keys = new ArrayList<Date>(result.keySet());
        Collections.sort(keys);

        // Check the first few results to see if they match known expected values
        Assertions.assertEquals(new BigDecimal("90.000000"), result.get(keys.get(0)));
        Assertions.assertEquals(new BigDecimal("27.120000"), result.get(keys.get(1)));
        Assertions.assertEquals(new BigDecimal("25.900000"), result.get(keys.get(2)));
        Assertions.assertEquals(new BigDecimal("24.790000"), result.get(keys.get(3)));
    }

    @Test
    public void testGetAccomRevenueREVPARMapData_ActualCapacity() throws Exception {
        when(pacmanConfigParamsService.isEnablePhysicalCapacityConsideration()).thenReturn(false);
        Map<Date, BigDecimal> result = service.getAccomClassREVPARHeatMapData(ACCOM_CLASS_ID);
        Assertions.assertNotNull(result);

        Assertions.assertEquals(DateUtil.getNumberOfDaysInThisMonthAndNext(), result.size());

        List<Date> keys = new ArrayList<Date>(result.keySet());
        Collections.sort(keys);

        // Check the first few results to see if they match known expected values
        Assertions.assertEquals(new BigDecimal("87.820000"), result.get(keys.get(0)));
        Assertions.assertEquals(new BigDecimal("37.520000"), result.get(keys.get(1)));
        Assertions.assertEquals(new BigDecimal("35.200000"), result.get(keys.get(2)));
        Assertions.assertEquals(new BigDecimal("33.170000"), result.get(keys.get(3)));
        Assertions.assertEquals(new BigDecimal("15.960000"), result.get(keys.get(23)));
    }

    @Test
    public void testGetAccomRevenueREVPARMapData_PhysicalCapacity() throws Exception {
        when(pacmanConfigParamsService.isEnablePhysicalCapacityConsideration()).thenReturn(true);
        Map<Date, BigDecimal> result = service.getAccomClassREVPARHeatMapData(ACCOM_CLASS_ID);
        Assertions.assertNotNull(result);

        Assertions.assertEquals(DateUtil.getNumberOfDaysInThisMonthAndNext(), result.size());

        List<Date> keys = new ArrayList<Date>(result.keySet());
        Collections.sort(keys);

        // Check the first few results to see if they match known expected values
        Assertions.assertEquals(new BigDecimal("87.820000"), result.get(keys.get(0)));
        Assertions.assertEquals(new BigDecimal("37.520000"), result.get(keys.get(1)));
        Assertions.assertEquals(new BigDecimal("35.200000"), result.get(keys.get(2)));
        Assertions.assertEquals(new BigDecimal("33.170000"), result.get(keys.get(3)));
        Assertions.assertEquals(new BigDecimal("15.670000"), result.get(keys.get(23)));
    }

    @Test
    public void testGetOverbookingHeatMapData() {
        Map<Date, BigDecimal> result = service.getOverbookingHeatMapData();
        Assertions.assertNotNull(result);

        Assertions.assertEquals(DateUtil.getNumberOfDaysInThisMonthAndNext(), result.size());

        List<Date> keys = new ArrayList<Date>(result.keySet());
        Collections.sort(keys);

        // Check the first few results to see if they match known expected values
        Assertions.assertEquals(BigDecimal.valueOf(101.12), result.get(keys.get(9)).setScale(2, RoundingMode.HALF_UP));
        Assertions.assertEquals(BigDecimal.valueOf(101.34), result.get(keys.get(10)).setScale(2, RoundingMode.HALF_UP));
        Assertions.assertEquals(BigDecimal.valueOf(101.56), result.get(keys.get(11)).setScale(2, RoundingMode.HALF_UP));
        Assertions.assertEquals(BigDecimal.valueOf(101.79), result.get(keys.get(12)).setScale(2, RoundingMode.HALF_UP));
    }

    @Test
    public void testGetUnqualifiedRateHeatMapDataNoRatePlan() throws Exception {
        hmService = Mockito.mock(HeatMapService.class);
        Map<Date, String> resultData = new HashMap<Date, String>();
        resultData.put(new Date(), "None");
        Mockito.when(hmService.getUnqualifiedRateHeatMapData(ACCOM_CLASS_ID)).thenReturn(resultData);
        Map<Date, String> result = hmService.getUnqualifiedRateHeatMapData(ACCOM_CLASS_ID);
        Assertions.assertNotNull(result);
        List<Date> keys = new ArrayList<Date>(result.keySet());
        Collections.sort(keys);
        // Check the first results to see if they match known expected values
        Assertions.assertEquals("None", result.get(keys.get(0)));
    }

    @Test
    public void testGetRateIdsByAccomClass() throws Exception {
        Mockito.when(accommodationService.getAccomClassesByRankOrder()).thenReturn(Arrays.asList(new AccomClass(ACCOM_CLASS_ID, 1, BigDecimal.ONE)));
        HeatMapService spy = Mockito.spy(service);
        spy.getOrderedRatePlanByAccomClass();
        Mockito.verify(spy, Mockito.times(1)).getOrderedRatePlanNames(ACCOM_CLASS_ID);
    }

    @Test
    public void shouldGetOccupancyForecastHeatMapDataByInventoryGroup() {
        //GIVEN
        LocalDate today = LocalDate.now();
        InventoryGroup inventoryGroup = UniqueInventoryGroupCreator.createUniqueInventoryGroup();
        AccomClass accomClass = tenantCrudService().find(AccomClass.class, 2);
        UniqueInventoryGroupDetailsCreator.createUniqueInventoryGroupDetails(accomClass, inventoryGroup);
        when(pacmanConfigParamsService.isEnablePhysicalCapacityConsideration()).thenReturn(false);
        setOccupancyFcstBy(today, "300.00", "15.000");
        setAccomActivityBy(today, "150", "1", "1", "15", "100.00");
        DateService dateServiceMock = mock(DateService.class);
        service.setDateService(dateServiceMock);
        when(dateServiceMock.getCaughtUpDate()).thenReturn(today.toDate());
        //WHEN
        Map<Date, BigDecimal> occupancyForecastHeatMapDataByInventoryGroup =
                service.getOccupancyForecastHeatMapDataByInventoryGroup(today.toDate(), today.toDate(), inventoryGroup.getId());
        //THEN
        verify(dateServiceMock).getCaughtUpDate();
        Assertions.assertEquals(new BigDecimal("91.216200"), occupancyForecastHeatMapDataByInventoryGroup.get(today.toDate()));
    }

    @Test
    public void shouldReturnEmptyMapWhenOccupancyForecastHeatMapDataIsNotAvailableByInventoryGroup() {
        LocalDate today = LocalDate.now();
        CrudService mockCrudService = Mockito.mock(CrudService.class);
        service.setCrudService(mockCrudService);
        when(mockCrudService.findByNativeQuery(any(String.class), any(Map.class), any(OccupancyDateRevenueOccupancyDtoRowMapper.class))).thenReturn(null);
        Map<Date, BigDecimal> occupancyForecastHeatMapDataByInventoryGroup =
                service.getOccupancyForecastHeatMapDataByInventoryGroup(today.toDate(), today.toDate(), 1);
        Assertions.assertTrue(occupancyForecastHeatMapDataByInventoryGroup.isEmpty());
    }

    @Test
    public void shouldGetPeakDemandHeatMapDataByInventoryGroupByConsideringPhysicalCapacity() {
        //GIVEN
        LocalDate today = LocalDate.now();
        InventoryGroup inventoryGroup = UniqueInventoryGroupCreator.createUniqueInventoryGroup();
        AccomClass accomClass = tenantCrudService().find(AccomClass.class, 2);
        UniqueInventoryGroupDetailsCreator.createUniqueInventoryGroupDetails(accomClass, inventoryGroup);
        when(pacmanConfigParamsService.isEnablePhysicalCapacityConsideration()).thenReturn(true);
        setOccupancyDemandFcstBy(today, "5.0", "5.00", "6.00");
        setAccomActivityBy(today, "100", "5", "5", "15", "900.00");
        //WHEN
        Map<Date, BigDecimal> peakDemandHeatMapDataByInventoryGroup = service.getPeakDemandHeatMapDataByInventoryGroup(today.toDate(), today.toDate(), inventoryGroup.getId());
        //THEN
        Assertions.assertEquals(new BigDecimal("25.000000"), peakDemandHeatMapDataByInventoryGroup.get(today.toDate()));
    }

    @Test
    public void shouldGetPeakDemandHeatMapDataByInventoryGroupByConsideringAvailableCapacity() {
        //GIVEN
        LocalDate today = LocalDate.now();
        InventoryGroup inventoryGroup = UniqueInventoryGroupCreator.createUniqueInventoryGroup();
        AccomClass accomClass = tenantCrudService().find(AccomClass.class, 2);
        UniqueInventoryGroupDetailsCreator.createUniqueInventoryGroupDetails(accomClass, inventoryGroup);
        when(pacmanConfigParamsService.isEnablePhysicalCapacityConsideration()).thenReturn(false);
        setOccupancyDemandFcstBy(today, "5.0", "5.00", "6.00");
        setAccomActivityBy(today, "100", "5", "5", "15", "900.00");
        //WHEN
        Map<Date, BigDecimal> peakDemandHeatMapDataByInventoryGroup = service.getPeakDemandHeatMapDataByInventoryGroup(today.toDate(), today.toDate(), inventoryGroup.getId());
        //THEN
        Assertions.assertEquals(new BigDecimal("27.777700"), peakDemandHeatMapDataByInventoryGroup.get(today.toDate()));
    }

    private void setOccupancyDemandFcstBy(LocalDate occupancyDate, String remainingDemand, String peakDemand, String userRemainingDemand) {
        tenantCrudService().executeUpdateByNativeQuery("update Occupancy_Demand_FCST set Peak_Demand = '" + peakDemand
                + "', Remaining_Demand = '" + remainingDemand + "', User_Remaining_Demand = '" + userRemainingDemand + "' where Occupancy_DT = '" + occupancyDate + "'");
    }

    private void setOccupancyFcstBy(LocalDate occupancyDate, String revenue, String occupancyNbr) {
        tenantCrudService().executeUpdateByNativeQuery("update Occupancy_FCST set Occupancy_NBR = '" + occupancyNbr + "', Revenue = '" + revenue + "' where Occupancy_DT = '" + occupancyDate + "'");
    }

    private void setAccomActivityBy(LocalDate occupancyDate, String capacity, String roomsNotAvailMaint, String roomsNotAvailOther, String roomsSolds, String roomRevenue) {
        tenantCrudService().executeUpdateByNativeQuery("update Accom_Activity set Accom_Capacity = " + capacity + ", Rooms_Sold = " + roomsSolds +
                ", Rooms_Not_Avail_Maint = " + roomsNotAvailMaint + ", Rooms_Not_Avail_Other = " + roomsNotAvailOther + ", Room_Revenue = '" + roomRevenue + "' where Occupancy_DT = '" + occupancyDate + "'");
    }

    @Test
    public void getConfigTypeIdByConfigTypeTest_validConfigType() {
        CrudService mockCrudService = Mockito.mock(CrudService.class);
        service.setCrudService(mockCrudService);
        String configType = "DEFAULT";
        when(mockCrudService.findByNamedQuerySingleResult(HeatMapRangeAndColorsConfigType.GET_ID_BY_TYPE,
                QueryParameter.with("configType", configType).parameters())).thenReturn(1);
        Integer actual = service.getConfigTypeIdByConfigType(configType);
        Assertions.assertNotNull(actual);
        Assertions.assertEquals(Integer.valueOf(1), actual);
    }

    @Test
    public void getConfigTypeIdByConfigTypeTest_invalidConfigType() {
        CrudService mockCrudService = Mockito.mock(CrudService.class);
        service.setCrudService(mockCrudService);
        String configType = "CONFIG_TYPE";
        when(mockCrudService.findByNamedQuerySingleResult(HeatMapRangeAndColorsConfigType.GET_ID_BY_TYPE,
                QueryParameter.with("configType", configType).parameters())).thenReturn(null);
        Integer actual = service.getConfigTypeIdByConfigType(configType);
        Assertions.assertNull(actual);
    }

    @Test
    public void saveOrUpdateHeatMapRangeAndColorsTest() {
        CrudService mockCrudService = Mockito.mock(CrudService.class);
        service.setCrudService(mockCrudService);
        List<HeatMapRangeAndColors> heatMapRangeAndColorsList = prepareHeatMapRangeAndColorsList();
        List<HeatMapRangeAndColors> expectedHeatMapRangeAndColorsList = prepareHeatMapRangeAndColorsList();
        when(mockCrudService.save(heatMapRangeAndColorsList)).thenReturn(expectedHeatMapRangeAndColorsList);
        boolean actual = service.saveOrUpdateHeatMapRangeAndColors(heatMapRangeAndColorsList);
        Assertions.assertTrue(actual);
    }

    @Test
    public void saveHeatMapRangeAndColorsConfigTest() {
        CrudService mockCrudService = Mockito.mock(CrudService.class);
        service.setCrudService(mockCrudService);
        Set<HeatMapRangeAndColors> heatMapRangeAndColorsList = new HashSet(prepareHeatMapRangeAndColorsList());
        Integer customizeConfigTypeId = 2;
        HeatMapRangeAndColorsConfig heatMapRangeAndColorsConfig = new HeatMapRangeAndColorsConfig();
        heatMapRangeAndColorsConfig.setId(3);
        when(mockCrudService.save(any(HeatMapRangeAndColorsConfig.class))).thenReturn(heatMapRangeAndColorsConfig);
        when(mockCrudService.save(any(HeatMapRangeAndColors.class))).thenReturn(heatMapRangeAndColorsList.iterator().next());

        boolean actual = service.saveHeatMapRangeAndColorsConfig(heatMapRangeAndColorsList, customizeConfigTypeId);

        Assertions.assertTrue(actual);
    }

    private List<HeatMapRangeAndColors> prepareHeatMapRangeAndColorsList() {
        List<HeatMapRangeAndColors> heatMapRangeAndColorsList = new ArrayList<>();
        int rangeFrom = 0;
        int rangeTo = 30;
        for (int index = 0; index < colors.size(); index++) {
            HeatMapRangeAndColors heatMapRangeAndColors = new HeatMapRangeAndColors();
            heatMapRangeAndColors.setRangeFrom(rangeFrom);
            heatMapRangeAndColors.setRangeTo(rangeTo);
            heatMapRangeAndColors.setOccupancyForecastColorCode(colors.get(index));
            heatMapRangeAndColors.setPeakDemandColorCode(colors.get(index));
            heatMapRangeAndColorsList.add(heatMapRangeAndColors);
            if (rangeTo == 100) {
                rangeTo += 20;
                rangeFrom += 10;
            } else if (rangeFrom == 101) {
                rangeFrom += 21;
                rangeTo += 10;
            } else if (rangeFrom == 0) {
                rangeFrom = 1;
                rangeTo += 10;
            } else {
                rangeFrom += 10;
                rangeTo += 10;
            }

        }
        return heatMapRangeAndColorsList;
    }

    private List<HeatMapRangeAndColorsConfig> prepareConfigListWithRangeAndColors(Map<Integer, String> configTypesToLoadRangeAndColorsData) {
        List<HeatMapRangeAndColorsConfig> configList = new ArrayList<>();
        configTypesToLoadRangeAndColorsData.keySet().forEach(configTypeId -> {
            HeatMapRangeAndColorsConfig config = new HeatMapRangeAndColorsConfig();
            HeatMapRangeAndColorsConfigType heatMapRangeAndColorsConfigType = new HeatMapRangeAndColorsConfigType();
            heatMapRangeAndColorsConfigType.setId(configTypeId);
            heatMapRangeAndColorsConfigType.setConfigType(configTypesToLoadRangeAndColorsData.get(configTypeId));
            config.setHeatMapRangeAndColorsConfigType(heatMapRangeAndColorsConfigType);
            config.setHeatMapRangeAndColors(new HashSet(prepareHeatMapRangeAndColorsList()));
            configList.add(config);
        });
        return configList;
    }

    @Test
    public void getAllHeatMapRangeAndColorsConfigTest() {
        CrudService mockCrudService = Mockito.mock(CrudService.class);
        service.setCrudService(mockCrudService);
        Map<Integer, String> configTypesToLoadRangeAndColorsData = new HashMap<>();
        configTypesToLoadRangeAndColorsData.put(1, "DEFAULT");
        configTypesToLoadRangeAndColorsData.put(2, "CUSTOMIZE");

        List<HeatMapRangeAndColorsConfig> configList = prepareConfigListWithRangeAndColors(configTypesToLoadRangeAndColorsData);
        when(mockCrudService.findAll(HeatMapRangeAndColorsConfig.class))
                .thenReturn(configList);

        List<HeatMapRangeAndColorsConfig> actual = service.getAllHeatMapRangeAndColorsConfig(new ArrayList<>(configTypesToLoadRangeAndColorsData.values()));

        Assertions.assertNotNull(actual);
        Assertions.assertTrue(actual.get(0).isHeatMapRangeAndColorsInitialized());
        Assertions.assertEquals(actual.get(0).getHeatMapRangeAndColorsConfigType().getConfigType(), "DEFAULT");
        Assertions.assertTrue(actual.get(1).isHeatMapRangeAndColorsInitialized());
        Assertions.assertEquals(actual.get(1).getHeatMapRangeAndColorsConfigType().getConfigType(), "CUSTOMIZE");
    }

    @Test
    public void getHeatMapRangeAndColorsConfigType_Test() {
        CrudService mockCrudService = Mockito.mock(CrudService.class);
        service.setCrudService(mockCrudService);
        when(mockCrudService.findByNamedQuerySingleResult(any(String.class),
                any(Map.class))).thenReturn(getHeatMapRangeAndColorsConfigType());
        HeatMapRangeAndColorsConfigType heatMapRangeAndColorsConfigType = service.getHeatMapRangeAndColorsConfigType("SEASONAL");
        Assertions.assertNotNull(heatMapRangeAndColorsConfigType);
    }


    @Test
    public void getHeatMapRangeAndColorsByConfigId_Test() {
        CrudService mockCrudService = Mockito.mock(CrudService.class);
        service.setCrudService(mockCrudService);
        when(mockCrudService.findByNamedQuery(any(String.class),
                any(Map.class))).thenReturn(getHeatMapRangeAndColorsList());
        List<HeatMapRangeAndColors> heatMapRangeAndColorsList = service.getHeatMapRangeAndColorsByConfigId(1);
        Assertions.assertNotNull(heatMapRangeAndColorsList);
        Assertions.assertEquals(1, heatMapRangeAndColorsList.size());
        Assertions.assertEquals("#2EFFCDE", heatMapRangeAndColorsList.get(0).getOccupancyForecastColorCode());
    }

    @Test
    public void getHeatMapRangeAndColorsByConfigIdEmpty_Test() {
        CrudService mockCrudService = Mockito.mock(CrudService.class);
        service.setCrudService(mockCrudService);
        when(mockCrudService.findByNamedQuery(any(String.class),
                any(Map.class))).thenReturn(new ArrayList<>());
        List<HeatMapRangeAndColors> heatMapRangeAndColorsList = service.getHeatMapRangeAndColorsByConfigId(1);
        Assertions.assertNotNull(heatMapRangeAndColorsList);
        Assertions.assertEquals(0, heatMapRangeAndColorsList.size());
    }

    private List<Object> getHeatMapRangeAndColorsList() {
        List<Object> heatmapRangeAndColorsList = new ArrayList<>();
        HeatMapRangeAndColors heatMapRangeAndColors = new HeatMapRangeAndColors();
        heatMapRangeAndColors.setHeatMapRangeAndColorsConfigId(1);
        heatMapRangeAndColors.setId(1);
        heatMapRangeAndColors.setOccupancyForecastColorCode("#2EFFCDE");
        heatMapRangeAndColors.setPeakDemandColorCode("#2EFFCDE");
        heatmapRangeAndColorsList.add(heatMapRangeAndColors);
        return heatmapRangeAndColorsList;
    }

    private HeatMapRangeAndColorsConfigType getHeatMapRangeAndColorsConfigType() {
        HeatMapRangeAndColorsConfigType HeatMapRangeAndColorsConfigType
                = new HeatMapRangeAndColorsConfigType();
        HeatMapRangeAndColorsConfigType.setId(1);
        HeatMapRangeAndColorsConfigType.setConfigType("SEASONAL");
        return HeatMapRangeAndColorsConfigType;
    }

    @Test
    public void saveHeatmapRangeAndColorsForSeason_Test() {
        CrudService mockCrudService = Mockito.mock(CrudService.class);
        service.setCrudService(mockCrudService);
        Set<HeatMapRangeAndColors> heatMapRangeAndColorsSet = new HashSet<>();
        service.saveHeatmapRangeAndColorsForSeason(heatMapRangeAndColorsSet);
        Mockito.verify(mockCrudService).save(heatMapRangeAndColorsSet);
    }

    @Test
    public void removeSeasonalData_Test() {
        CrudService mockCrudService = Mockito.mock(CrudService.class);
        service.setCrudService(mockCrudService);
        when(mockCrudService.executeUpdateByNamedQuery(any(String.class),
                any(Map.class))).thenReturn(1);
        Assertions.assertEquals(1, service.removeSeasonalData(10));
    }

    private void setupHeatMapSettings() {
        CrudService tenantService = tenantCrudService();
        tenantService.executeUpdateByNativeQuery("DELETE FROM Heat_Map_Range_and_Colors ");
        tenantService.executeUpdateByNativeQuery("DELETE FROM Heat_Map_Range_and_Colors_Config ");
        tenantService.executeUpdateByNativeQuery("DELETE FROM Heat_Map_Range_and_Colors_Config_Type ");
        tenantService.executeUpdateByNativeQuery("INSERT INTO Heat_Map_Range_and_Colors_Config_Type " +
                "VALUES ('DEFAULT',11403,CURRENT_TIMESTAMP,null,CURRENT_TIMESTAMP), " +
                "('CUSTOMIZE',11403,CURRENT_TIMESTAMP,null,CURRENT_TIMESTAMP), " +
                "('SEASONAL',11403,CURRENT_TIMESTAMP,null,CURRENT_TIMESTAMP)");
        addHeatMapSettings("DEFAULT", null, null, -1);
    }

    private void addHeatMapSettings(String configType, String startDate, String endDate, int changeIndex) {
        CrudService tenantService = tenantCrudService();
        HeatMapRangeAndColorsConfig heatMapRangeAndColorsConfig = getHeatMapRangeAndColorsConfig(configType, startDate, endDate, tenantService);
        HeatMapRangeAndColorsConfig savedConfig = tenantService.save(heatMapRangeAndColorsConfig);
        List<HeatMapRangeAndColors> list = prepareHeatMapRangeAndColorsList();
        list.forEach(entity -> entity.setHeatMapRangeAndColorsConfigId(savedConfig.getId()));
        list.sort(Comparator.comparing(HeatMapRangeAndColors::getRangeFrom));
        if (changeIndex >= 0 && changeIndex < 10) {
            HeatMapRangeAndColors modify = list.get(changeIndex);
            modify.setOccupancyForecastColorCode("000000");
            modify.setPeakDemandColorCode("F2F2F2");
        }
        tenantService.save(new HashSet(list));
    }

    @Test
    public void getHeatMapConfigWithRangeAndColors_default() {
        setupHeatMapSettings();
        Map<HeatMapRangeAndColorsConfig, List<HeatMapRangeAndColors>> actual = service.getHeatMapConfigWithRangeAndColors(
                new Date("01-Jun-2020"),
                new Date("30-Nov-2020"), false);

        Assertions.assertNotNull(actual);
        Assertions.assertEquals(1, actual.size());
        actual.keySet().forEach(e -> {
            Assertions.assertEquals(e.getStartDate(), null);
            Assertions.assertEquals(e.getEndDate(), null);
        });
        Assertions.assertEquals(1, actual.keySet().stream()
                .filter(config -> (config.getHeatMapRangeAndColorsConfigType() != null) &&
                        StringUtils.equalsIgnoreCase(config.getHeatMapRangeAndColorsConfigType().getConfigType(), HeatMapService.DEFAULT))
                .count());
    }

    @Test
    public void getHeatMapConfigWithRangeAndColors_customize() {
        setupHeatMapSettings();
        int changeIndex = 2;
        addHeatMapSettings(HeatMapService.CUSTOMIZE, null, null, changeIndex);
        Map<HeatMapRangeAndColorsConfig, List<HeatMapRangeAndColors>> actual = service.getHeatMapConfigWithRangeAndColors(
                new Date("01-Jun-2020"),
                new Date("30-Nov-2020"), false);

        Assertions.assertNotNull(actual);
        Assertions.assertEquals(1, actual.size());
        actual.keySet().forEach(e -> {
            Assertions.assertEquals(e.getStartDate(), null);
            Assertions.assertEquals(e.getEndDate(), null);
            Assertions.assertTrue(actual.get(e).stream()
                    .filter(c -> StringUtils.equalsIgnoreCase(c.getOccupancyForecastColorCode(), "000000"))
                    .findFirst().isPresent());
        });
        Assertions.assertEquals(1, actual.keySet().stream()
                .filter(config -> (config.getHeatMapRangeAndColorsConfigType() != null) &&
                        StringUtils.equalsIgnoreCase(config.getHeatMapRangeAndColorsConfigType().getConfigType(), HeatMapService.CUSTOMIZE))
                .count());
    }

    @Test
    public void getHeatMapConfigWithRangeAndColors_defaultAndSeason() {
        setupHeatMapSettings();
        int changeIndex = 2;
        addHeatMapSettings("SEASONAL", "01-May-2020", "30-Jun-2020", changeIndex);
        Map<HeatMapRangeAndColorsConfig, List<HeatMapRangeAndColors>> actual = service.getHeatMapConfigWithRangeAndColors(
                new Date("01-Jun-2020"),
                new Date("30-Nov-2020"), false);

        Assertions.assertNotNull(actual);
        Assertions.assertEquals(2, actual.size());
        Assertions.assertEquals(1, actual.keySet().stream()
                .filter(config -> (config.getHeatMapRangeAndColorsConfigType() != null) &&
                        StringUtils.equalsIgnoreCase(config.getHeatMapRangeAndColorsConfigType().getConfigType(), HeatMapService.DEFAULT))
                .count());
        Assertions.assertEquals(1, actual.keySet().stream()
                .filter(config -> (config.getHeatMapRangeAndColorsConfigType() != null) &&
                        StringUtils.equalsIgnoreCase(config.getHeatMapRangeAndColorsConfigType().getConfigType(), "SEASONAL"))
                .count());
    }

    @Test
    public void getHeatMapConfigWithRangeAndColors_customizeAndSeason() {
        setupHeatMapSettings();
        int changeIndex = 2;
        addHeatMapSettings(HeatMapService.CUSTOMIZE, null, null, changeIndex);
        addHeatMapSettings("SEASONAL", "01-May-2020", "30-Jun-2020", changeIndex);
        addHeatMapSettings("SEASONAL", "01-Jul-2020", "31-Jul-2020", changeIndex);
        Map<HeatMapRangeAndColorsConfig, List<HeatMapRangeAndColors>> actual = service.getHeatMapConfigWithRangeAndColors(
                new Date("01-Jun-2020"),
                new Date("30-Nov-2020"), false);

        Assertions.assertNotNull(actual);
        Assertions.assertEquals(3, actual.size());
        Assertions.assertEquals(1, actual.keySet().stream()
                .filter(config -> (config.getHeatMapRangeAndColorsConfigType() != null) &&
                        StringUtils.equalsIgnoreCase(config.getHeatMapRangeAndColorsConfigType().getConfigType(), HeatMapService.CUSTOMIZE))
                .count());
        Assertions.assertEquals(2, actual.keySet().stream()
                .filter(config -> (config.getHeatMapRangeAndColorsConfigType() != null) &&
                        StringUtils.equalsIgnoreCase(config.getHeatMapRangeAndColorsConfigType().getConfigType(), "SEASONAL"))
                .count());
    }

    @Test
    public void getHeatMapConfigWithRangeAndColors_customizeAndSeasonsInGivenPeriod() {
        setupHeatMapSettings();
        int changeIndex = 2;
        addHeatMapSettings(HeatMapService.CUSTOMIZE, null, null, changeIndex);
        addHeatMapSettings("SEASONAL", "01-May-2020", "30-Jun-2020", changeIndex);
        addHeatMapSettings("SEASONAL", "01-Jul-2020", "31-Jul-2020", changeIndex);
        addHeatMapSettings("SEASONAL", "01-Jul-2021", "31-Jul-2021", changeIndex);
        Map<HeatMapRangeAndColorsConfig, List<HeatMapRangeAndColors>> actual = service.getHeatMapConfigWithRangeAndColors(
                new Date("01-Jun-2020"),
                new Date("30-Nov-2020"), false);

        Assertions.assertNotNull(actual);
        Assertions.assertEquals(3, actual.size());
        Assertions.assertEquals(1, actual.keySet().stream()
                .filter(config -> (config.getHeatMapRangeAndColorsConfigType() != null) &&
                        StringUtils.equalsIgnoreCase(config.getHeatMapRangeAndColorsConfigType().getConfigType(), HeatMapService.CUSTOMIZE))
                .count());
        Assertions.assertEquals(2, actual.keySet().stream()
                .filter(config -> (config.getHeatMapRangeAndColorsConfigType() != null) &&
                        StringUtils.equalsIgnoreCase(config.getHeatMapRangeAndColorsConfigType().getConfigType(), "SEASONAL"))
                .count());
    }

    @Test
    public void getHeatMapConfigWithRangeAndColors_customizeAndSingleSeasonForGivenPeriod() {
        setupHeatMapSettings();
        int changeIndex = 2;
        addHeatMapSettings(HeatMapService.CUSTOMIZE, null, null, changeIndex);
        addHeatMapSettings("SEASONAL", "01-Mar-2020", "30-Apr-2020", changeIndex);
        addHeatMapSettings("SEASONAL", "01-May-2020", "31-Dec-2020", changeIndex);
        addHeatMapSettings("SEASONAL", "01-Jan-2021", "31-Mar-2021", changeIndex);
        Map<HeatMapRangeAndColorsConfig, List<HeatMapRangeAndColors>> actual = service.getHeatMapConfigWithRangeAndColors(
                new Date("01-Jun-2020"),
                new Date("30-Nov-2020"), false);

        Assertions.assertNotNull(actual);
        Assertions.assertEquals(2, actual.size());
        Assertions.assertEquals(1, actual.keySet().stream()
                .filter(config -> (config.getHeatMapRangeAndColorsConfigType() != null) &&
                        StringUtils.equalsIgnoreCase(config.getHeatMapRangeAndColorsConfigType().getConfigType(), HeatMapService.CUSTOMIZE))
                .count());
        Assertions.assertEquals(1, actual.keySet().stream()
                .filter(config -> (config.getHeatMapRangeAndColorsConfigType() != null) &&
                        StringUtils.equalsIgnoreCase(config.getHeatMapRangeAndColorsConfigType().getConfigType(), "SEASONAL"))
                .count());

        Assertions.assertTrue(StringUtils.equalsIgnoreCase(((HeatMapRangeAndColorsConfig) actual.keySet().toArray()[1]).getStartDate(), "01-May-2020"));
        Assertions.assertTrue(StringUtils.equalsIgnoreCase(((HeatMapRangeAndColorsConfig) actual.keySet().toArray()[1]).getEndDate(), "31-Dec-2020"));
    }

    @Test
    public void getSeasonHeatMapRangeAndColorsConfig_Test() {
        setupHeatMapSettings();
        List<HeatMapRangeAndColorsConfig> actual = service.getSeasonHeatMapRangeAndColorsConfig(new LocalDate("2020-06-01"), new LocalDate("2020-06-30"), fiscalCalendarEnabled);
        Assertions.assertNotNull(actual);
        Assertions.assertEquals(1, actual.size());
        actual.forEach(e -> {
            Assertions.assertEquals(e.getStartDate(), null);
            Assertions.assertEquals(e.getEndDate(), null);
        });
        Assertions.assertEquals(1, actual.stream()
                .filter(config -> (config.getHeatMapRangeAndColorsConfigType() != null) &&
                        StringUtils.equalsIgnoreCase(config.getHeatMapRangeAndColorsConfigType().getConfigType(), HeatMapService.DEFAULT))
                .count());
    }

    @Test
    public void getSeasonHeatMapRangeAndColorsConfigForDefaultCustomizeAndSeason_Test() {
        setupHeatMapSettings();
        int changeIndex = 2;
        addHeatMapSettings(HeatMapService.CUSTOMIZE, null, null, changeIndex);
        addHeatMapSettings("SEASONAL", "01-Mar-2020", "30-Apr-2020", changeIndex);
        addHeatMapSettings("SEASONAL", "01-May-2020", "31-Dec-2020", changeIndex);
        List<HeatMapRangeAndColorsConfig> actual = service.getSeasonHeatMapRangeAndColorsConfig(new LocalDate("2020-02-01"), new LocalDate("2020-03-31"), fiscalCalendarEnabled);

        Assertions.assertNotNull(actual);
        Assertions.assertEquals(3, actual.size());
        Assertions.assertEquals(1, actual.stream()
                .filter(config -> (config.getHeatMapRangeAndColorsConfigType() != null) &&
                        StringUtils.equalsIgnoreCase(config.getHeatMapRangeAndColorsConfigType().getConfigType(), HeatMapService.CUSTOMIZE))
                .count());
        Assertions.assertEquals(1, actual.stream()
                .filter(config -> (config.getHeatMapRangeAndColorsConfigType() != null) &&
                        StringUtils.equalsIgnoreCase(config.getHeatMapRangeAndColorsConfigType().getConfigType(), "SEASONAL"))
                .count());

    }

    @Test
    public void getSeasonHeatMapRangeAndColorsConfigForDefaultAndSeason_Test() {
        setupHeatMapSettings();
        int changeIndex = 2;
        addHeatMapSettings("SEASONAL", "01-Mar-2020", "30-Apr-2020", changeIndex);
        addHeatMapSettings("SEASONAL", "01-May-2020", "31-Dec-2020", changeIndex);
        addHeatMapSettings("SEASONAL", "01-Jan-2021", "31-Mar-2021", changeIndex);
        List<HeatMapRangeAndColorsConfig> actual = service.getSeasonHeatMapRangeAndColorsConfig(new LocalDate("2020-02-01"), new LocalDate("2020-03-31"), fiscalCalendarEnabled);

        Assertions.assertNotNull(actual);
        Assertions.assertEquals(2, actual.size());
        Assertions.assertEquals(1, actual.stream()
                .filter(config -> (config.getHeatMapRangeAndColorsConfigType() != null) &&
                        StringUtils.equalsIgnoreCase(config.getHeatMapRangeAndColorsConfigType().getConfigType(), HeatMapService.DEFAULT))
                .count());
        Assertions.assertEquals(1, actual.stream()
                .filter(config -> (config.getHeatMapRangeAndColorsConfigType() != null) &&
                        StringUtils.equalsIgnoreCase(config.getHeatMapRangeAndColorsConfigType().getConfigType(), "SEASONAL"))
                .count());

        Assertions.assertTrue(StringUtils.equalsIgnoreCase(((HeatMapRangeAndColorsConfig) actual.toArray()[1]).getStartDate(), "01-Mar-2020"));
        Assertions.assertTrue(StringUtils.equalsIgnoreCase(((HeatMapRangeAndColorsConfig) actual.toArray()[1]).getEndDate(), "30-Apr-2020"));
    }

    @Test
    public void getSeasonHeatMapRangeAndColorsConfigForCustomizeAndSeasonFiscalDate_Test() {
        setupHeatMapSettings();
        int changeIndex = 2;
        addHeatMapSettingsForFiscal(HeatMapService.CUSTOMIZE, null, null, changeIndex, null, null);
        addHeatMapSettingsForFiscal("SEASONAL", "01-Mar-2020", "30-Apr-2020", changeIndex, "05-Mar-2020", "03-May-2020");
        addHeatMapSettingsForFiscal("SEASONAL", "01-May-2020", "30-Jun-2020", changeIndex, "06-May-2020", "30-Jun-2020");
        List<HeatMapRangeAndColorsConfig> actual = service.getSeasonHeatMapRangeAndColorsConfig(new LocalDate("2020-02-01"), new LocalDate("2020-03-31"), true);

        Assertions.assertNotNull(actual);
        Assertions.assertEquals(3, actual.size());
        Assertions.assertEquals(1, actual.stream()
                .filter(config -> (config.getHeatMapRangeAndColorsConfigType() != null) &&
                        StringUtils.equalsIgnoreCase(config.getHeatMapRangeAndColorsConfigType().getConfigType(), HeatMapService.CUSTOMIZE))
                .count());
        Assertions.assertEquals(1, actual.stream()
                .filter(config -> (config.getHeatMapRangeAndColorsConfigType() != null) &&
                        StringUtils.equalsIgnoreCase(config.getHeatMapRangeAndColorsConfigType().getConfigType(), "SEASONAL"))
                .count());
    }

    @Test
    public void getSeasonHeatMapRangeAndColorsConfigForDefaultAndSeasonFiscal_Test() {
        setupHeatMapSettings();
        int changeIndex = 2;
        addHeatMapSettingsForFiscal("SEASONAL", "01-Mar-2020", "30-Apr-2020", changeIndex, "05-Mar-2020", "03-May-2020");
        addHeatMapSettingsForFiscal("SEASONAL", "01-May-2020", "30-Jun-2020", changeIndex, "06-May-2020", "30-Jun-2020");
        List<HeatMapRangeAndColorsConfig> actual = service.getSeasonHeatMapRangeAndColorsConfig(new LocalDate("2020-02-01"), new LocalDate("2020-03-31"), true);

        Assertions.assertNotNull(actual);
        Assertions.assertEquals(2, actual.size());
        Assertions.assertEquals(1, actual.stream()
                .filter(config -> (config.getHeatMapRangeAndColorsConfigType() != null) &&
                        StringUtils.equalsIgnoreCase(config.getHeatMapRangeAndColorsConfigType().getConfigType(), HeatMapService.DEFAULT))
                .count());
        Assertions.assertEquals(1, actual.stream()
                .filter(config -> (config.getHeatMapRangeAndColorsConfigType() != null) &&
                        StringUtils.equalsIgnoreCase(config.getHeatMapRangeAndColorsConfigType().getConfigType(), "SEASONAL"))
                .count());

        Assertions.assertTrue(StringUtils.equalsIgnoreCase(((HeatMapRangeAndColorsConfig) actual.toArray()[1]).getFiscalStartDate(), "05-Mar-2020"));
        Assertions.assertTrue(StringUtils.equalsIgnoreCase(((HeatMapRangeAndColorsConfig) actual.toArray()[1]).getFiscalEndDate(), "03-May-2020"));
    }

    private void addHeatMapSettingsForFiscal(String configType, String startDate, String endDate, int changeIndex, String fiscalStartDate, String fiscalEndDate) {
        CrudService tenantService = tenantCrudService();
        HeatMapRangeAndColorsConfig heatMapRangeAndColorsConfig = getHeatMapRangeAndColorsConfig(configType, startDate, endDate, tenantService);
        heatMapRangeAndColorsConfig.setFiscalStartDate(fiscalStartDate);
        heatMapRangeAndColorsConfig.setFiscalEndDate(fiscalEndDate);
        HeatMapRangeAndColorsConfig savedConfig = tenantService.save(heatMapRangeAndColorsConfig);
        List<HeatMapRangeAndColors> list = prepareHeatMapRangeAndColorsList();
        list.forEach(entity -> entity.setHeatMapRangeAndColorsConfigId(savedConfig.getId()));
        list.sort(Comparator.comparing(HeatMapRangeAndColors::getRangeFrom));
        if (changeIndex >= 0 && changeIndex < 10) {
            HeatMapRangeAndColors modify = list.get(changeIndex);
            modify.setOccupancyForecastColorCode("000000");
            modify.setPeakDemandColorCode("F2F2F2");
        }
        tenantService.save(new HashSet(list));
    }

    private HeatMapRangeAndColorsConfig getHeatMapRangeAndColorsConfig(String configType, String startDate, String endDate, CrudService tenantService) {
        HeatMapRangeAndColorsConfig heatMapRangeAndColorsConfig = new HeatMapRangeAndColorsConfig();
        HeatMapRangeAndColorsConfigType type = tenantService.findByNamedQuerySingleResult(HeatMapRangeAndColorsConfigType.BY_CONFIG_TYPE,
                QueryParameter.with("configType", configType).parameters());
        heatMapRangeAndColorsConfig.setHeatMapRangeAndColorsConfigType(type);
        heatMapRangeAndColorsConfig.setStartDate(startDate);
        heatMapRangeAndColorsConfig.setEndDate(endDate);
        heatMapRangeAndColorsConfig.setCreateDate(LocalDateTime.now());
        heatMapRangeAndColorsConfig.setCreatedByUserId(Integer.parseInt(PacmanWorkContextHelper.getUserId()));
        return heatMapRangeAndColorsConfig;
    }
}