package com.ideas.tetris.pacman.services.budget;

import com.ideas.g3.test.AbstractG3JupiterTest;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomType;
import com.ideas.tetris.pacman.services.accommodation.entity.UniqueAccomTypeCreator;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.UniqueMktSegAccomActivityCreator;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.UniqueMktSegBusinessGroupCreator;
import com.ideas.tetris.pacman.services.budget.dto.BudgetDataDto;
import com.ideas.tetris.pacman.services.budget.dto.BudgetType;
import com.ideas.tetris.pacman.services.budget.dto.PercentagePattern;
import com.ideas.tetris.pacman.services.businessgroup.service.UniqueBusinessGroupCreator;
import com.ideas.tetris.pacman.services.businessgroup.service.UniqueMktSegCreator;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.marketsegment.entity.BusinessGroup;
import com.ideas.tetris.pacman.services.marketsegment.entity.MktSeg;
import com.ideas.tetris.platform.common.time.JavaLocalDateInterval;
import com.ideas.tetris.platform.common.time.JavaLocalDateUtils;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import java.math.BigDecimal;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.util.Comparator;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

public class BudgetServiceIntegrationTest extends AbstractG3JupiterTest {

    private BudgetService service = new BudgetService();
    private DateService mockDateService;
    private JavaLocalDateInterval localDateInterval;

    @BeforeEach
    public void setUp() {
        service.tenantCrudService = tenantCrudService();
        mockDateService = Mockito.mock(DateService.class);
        service.dateService = mockDateService;
        LocalDate startDate = JavaLocalDateUtils.getFirstDayOfCurrentMonth();
        LocalDate endDate = JavaLocalDateUtils.getLastDayOfCurrentMonth();
        localDateInterval = new JavaLocalDateInterval(startDate, endDate);
    }

    @Test
    public void shouldGetRoomsSoldsAndRevenueForTheSelectedDateRange() {
        //GIVEN
        LocalDate date = LocalDate.now();
        LocalDate startDate = service.getStartDateFor(date);
        LocalDate endDate = service.getEndDateFor(startDate);
        tenantCrudService().executeUpdateByNativeQuery(
                "update Mkt_Accom_Activity set Rooms_Sold = 10, Room_Revenue = 70, Food_Revenue = 30, Total_Revenue = 100 " +
                        "where Mkt_Seg_ID = 14 and Occupancy_DT between '" + startDate + "' and '" + endDate + "'");
        //WHEN
        List<BudgetDataDto> roomSoldsAndRevenueForTheDateRange = service.getRoomSoldsAndRevenueForTheDateRange(startDate, endDate, 1, BudgetType.BUSINESS_TYPE);
        roomSoldsAndRevenueForTheDateRange.sort(new Comparator<BudgetDataDto>() {
            @Override
            public int compare(BudgetDataDto budgetDataDto, BudgetDataDto t1) {
                return budgetDataDto.getOccupancyDate().compareTo(t1.getOccupancyDate());
            }
        });
        //THEN
        int day = 0;
        for (BudgetDataDto dto : roomSoldsAndRevenueForTheDateRange) {
            assertEquals(50, dto.getRoomsSold());
            assertEquals(new BigDecimal("500.00000"), dto.getRoomRevenue());
            assertEquals("Group", dto.getSegment());
            assertEquals(startDate.plusDays(day), dto.getOccupancyDate());
            day++;
        }
    }

    @Test
    public void shouldSkip_IP_Cfg_Mark_Property_Date_WhileGettingRoomsSoldsAndRevenueForSelectedDateRange() {
        //GIVEN
        LocalDate date = LocalDate.now();
        LocalDate startDate = service.getStartDateFor(date);
        LocalDate endDate = service.getEndDateFor(startDate);

        tenantCrudService().executeUpdateByNativeQuery(
                "update Mkt_Accom_Activity set Rooms_Sold = 10, Room_Revenue = 70, Food_Revenue = 30, Total_Revenue = 100 " +
                        "where Mkt_Seg_ID = 14 and Occupancy_DT between '" + startDate + "' and '" + endDate + "'");
        tenantCrudService().executeUpdateByNativeQuery("insert into IP_Cfg_Mark_Property_Date " +
                "values(5, '" + startDate.plusDays(14) + "', '" + startDate.plusDays(19) + "', 1, '2017-09-06 00:00:00.000', '', 2)");
        //WHEN
        List<BudgetDataDto> roomSoldsAndRevenueForTheDateRange = service.getRoomSoldsAndRevenueForTheDateRange(startDate, endDate, 1, BudgetType.BUSINESS_TYPE);
        //THEN
        for (BudgetDataDto dto : roomSoldsAndRevenueForTheDateRange) {
            assertTrue(startDate.plusDays(14).compareTo(dto.getOccupancyDate()) != 0, "Excluded dates should not match. " + dto.getOccupancyDate() + " is excluded.");
            assertTrue(startDate.plusDays(15).compareTo(dto.getOccupancyDate()) != 0, "Excluded dates should not match. " + dto.getOccupancyDate() + " is excluded.");
            assertTrue(startDate.plusDays(16).compareTo(dto.getOccupancyDate()) != 0, "Excluded dates should not match. " + dto.getOccupancyDate() + " is excluded.");
            assertTrue(startDate.plusDays(17).compareTo(dto.getOccupancyDate()) != 0, "Excluded dates should not match. " + dto.getOccupancyDate() + " is excluded.");
            assertTrue(startDate.plusDays(18).compareTo(dto.getOccupancyDate()) != 0, "Excluded dates should not match. " + dto.getOccupancyDate() + " is excluded.");
            assertTrue(startDate.plusDays(19).compareTo(dto.getOccupancyDate()) != 0, "Excluded dates should not match. " + dto.getOccupancyDate() + " is excluded.");
        }
    }

    @Test
    public void shouldGetRoomsSoldsAndRevenueFromSameMonthLastYear() {
        //GIVEN
        LocalDate date = LocalDate.now();
        LocalDate lastYear = date.minusYears(1);
        LocalDate startDate = service.getStartDateFor(lastYear);
        LocalDate endDate = service.getEndDateFor(startDate);
        tenantCrudService().executeUpdateByNativeQuery(
                "update Mkt_Accom_Activity set Rooms_Sold = 10, Room_Revenue = 70, Food_Revenue = 30, Total_Revenue = 100 " +
                        "where Mkt_Seg_ID = 14 and Occupancy_DT between '" + startDate + "' and '" + endDate + "'");
        //WHEN
        List<BudgetDataDto> roomsSoldsAndRevenueFromSameMonthLastYear = service.getRoomsSoldsAndRevenueFromSameMonthLastYear(localDateInterval, 1, BudgetType.BUSINESS_TYPE);
        roomsSoldsAndRevenueFromSameMonthLastYear.sort(new Comparator<BudgetDataDto>() {
            @Override
            public int compare(BudgetDataDto budgetDataDto, BudgetDataDto t1) {
                return budgetDataDto.getOccupancyDate().compareTo(t1.getOccupancyDate());
            }
        });
        //THEN
        int day = 0;
        for (BudgetDataDto dto : roomsSoldsAndRevenueFromSameMonthLastYear) {
            assertEquals(50, dto.getRoomsSold());
            assertEquals(new BigDecimal("500.00000"), dto.getRoomRevenue());
            assertEquals("Group", dto.getSegment());
            assertEquals(startDate.plusDays(day), dto.getOccupancyDate());
            day++;
        }
    }

    @Disabled("Flaky")
    @Test
    public void shouldBeAbleToGetTotalOfRoomsSoldsForTheGivenBudgetData() {
        //GIVEN
        LocalDate date = LocalDate.now();
        LocalDate lastYear = date.minusYears(1);
        LocalDate startDate = service.getStartDateFor(lastYear);
        LocalDate endDate = service.getEndDateFor(startDate);
        tenantCrudService().executeUpdateByNativeQuery(
                "update Mkt_Accom_Activity set Rooms_Sold = 10, Room_Revenue = 70, Food_Revenue = 30, Total_Revenue = 100 " +
                        "where Mkt_Seg_ID = 14 and Occupancy_DT between '" + startDate + "' and '" + endDate + "'");
        List<BudgetDataDto> roomsSoldsAndRevenueFromSameMonthLastYear = service.getRoomsSoldsAndRevenueFromSameMonthLastYear(localDateInterval, 1, BudgetType.BUSINESS_TYPE);
        //WHEN
        Integer totalRoomsSolds = service.getTotalOfRoomsSoldsFor(roomsSoldsAndRevenueFromSameMonthLastYear);
        //THEN
        long totalNoOfDays = DateUtil.getTotalNoOfDays(JavaLocalDateUtils.toDate(startDate), JavaLocalDateUtils.toDate(endDate));
        Integer expectedTotalRoomsSolds = (int) totalNoOfDays * 50;
        assertEquals(expectedTotalRoomsSolds, totalRoomsSolds);
    }

    @Disabled("Flaky")
    @Test
    public void shouldBeAbleToGetTotalOfRevenueForTheGivenBudgetData() {
        //GIVEN
        LocalDate date = LocalDate.now();
        LocalDate lastYear = date.minusYears(1);
        LocalDate startDate = service.getStartDateFor(lastYear);
        LocalDate endDate = service.getEndDateFor(startDate);
        tenantCrudService().executeUpdateByNativeQuery(
                "update Mkt_Accom_Activity set Rooms_Sold = 10, Room_Revenue = 70, Food_Revenue = 30, Total_Revenue = 100.50 " +
                        "where Mkt_Seg_ID = 14 and Occupancy_DT between '" + startDate + "' and '" + endDate + "'");
        List<BudgetDataDto> roomsSoldsAndRevenueFromSameMonthLastYear = service.getRoomsSoldsAndRevenueFromSameMonthLastYear(localDateInterval, 1, BudgetType.BUSINESS_TYPE);
        //WHEN
        BigDecimal totalRevenueSolds = service.getTotalOfRevenueFor(roomsSoldsAndRevenueFromSameMonthLastYear);
        //THEN
        long totalNoOfDays = DateUtil.getTotalNoOfDays(JavaLocalDateUtils.toDate(startDate), JavaLocalDateUtils.toDate(endDate));
        BigDecimal expectedTotalRevenue = BigDecimal.valueOf(totalNoOfDays).multiply(new BigDecimal("502.5"));
        assertEquals(expectedTotalRevenue, totalRevenueSolds);
    }

    @Test
    public void shouldBeAbleToGetTotalOfRoomsSoldsForTheGivenDow() {
        //GIVEN
        LocalDate date = LocalDate.now();
        LocalDate lastYear = date.minusYears(1);
        LocalDate startDate = service.getStartDateFor(lastYear);
        LocalDate endDate = service.getEndDateFor(startDate);
        tenantCrudService().executeUpdateByNativeQuery(
                "update Mkt_Accom_Activity set Rooms_Sold = 10, Room_Revenue = 70, Food_Revenue = 30, Total_Revenue = 100 " +
                        "where Mkt_Seg_ID = 14 and Occupancy_DT between '" + startDate + "' and '" + endDate + "'");
        List<BudgetDataDto> roomsSoldsAndRevenueFromSameMonthLastYear = service.getRoomsSoldsAndRevenueFromSameMonthLastYear(localDateInterval, 1, BudgetType.BUSINESS_TYPE);
        //WHEN
        int totalOfRoomsSoldsForDow = service.getTotalOfRoomsSoldsForDow(roomsSoldsAndRevenueFromSameMonthLastYear, DayOfWeek.MONDAY);
        //THEN
        int dayOccurence = getDayOccurenceFor(startDate, endDate, DayOfWeek.MONDAY.getValue());
        Integer expectedRoomSolds = dayOccurence * 50;
        assertEquals(expectedRoomSolds.intValue(), totalOfRoomsSoldsForDow);
    }

    private int getDayOccurenceFor(LocalDate startDate, LocalDate endDate, int day) {
        int dayOccurence = 0;
        while (startDate.compareTo(endDate) <= 0) {
            if (startDate.getDayOfWeek().getValue() == day) {
                dayOccurence++;
            }
            startDate = startDate.plusDays(1);
        }
        return dayOccurence;
    }

    @Test
    public void shouldBeAbleToGetTotalOfRevenueForTheGivenDow() {
        //GIVEN
        LocalDate date = LocalDate.now();
        LocalDate lastYear = date.minusYears(1);
        LocalDate startDate = service.getStartDateFor(lastYear);
        LocalDate endDate = service.getEndDateFor(startDate);
        tenantCrudService().executeUpdateByNativeQuery(
                "update Mkt_Accom_Activity set Rooms_Sold = 10, Room_Revenue = 70, Food_Revenue = 30, Total_Revenue = 100.50 " +
                        "where Mkt_Seg_ID = 14 and Occupancy_DT between '" + startDate + "' and '" + endDate + "'");
        List<BudgetDataDto> roomsSoldsAndRevenueFromSameMonthLastYear = service.getRoomsSoldsAndRevenueFromSameMonthLastYear(localDateInterval, 1, BudgetType.BUSINESS_TYPE);
        //WHEN
        BigDecimal totalRevenue = service.getTotalOfRevenueForDow(roomsSoldsAndRevenueFromSameMonthLastYear, DayOfWeek.MONDAY);
        //THEN
        int dayOccurence = getDayOccurenceFor(startDate, endDate, DayOfWeek.MONDAY.getValue());
        BigDecimal expectedRevenue = new BigDecimal("502.5").multiply(BigDecimal.valueOf(dayOccurence));
        assertEquals(expectedRevenue, totalRevenue);
    }

    @Test
    public void shouldBeAbleToCalculatePercentageOfForDow() {
        //WHEN
        BigDecimal percentage = service.calculatePercentageForTheDow(new BigDecimal(1550), new BigDecimal(250));
        //THEN
        assertEquals(new BigDecimal("16"), percentage);
    }

    @Disabled
    @Test
    public void shouldBeAbleToGetPercentagePatternForRoomsSolds() {
        PercentagePattern pattern = service.getPercentagePatternForRoomsSolds(1, localDateInterval, BudgetType.BUSINESS_TYPE);
        assertEquals(16, pattern.getMonday().intValue());
        assertEquals(17, pattern.getTuesday().intValue());
        assertEquals(12, pattern.getWednesday().intValue());
        assertEquals(13, pattern.getThursday().intValue());
        assertEquals(13, pattern.getFriday().intValue());
        assertEquals(13, pattern.getSaturday().intValue());
        assertEquals(16, pattern.getSunday().intValue());
        assertPatternPercentageTotal(pattern);
    }

    @Test
    public void shouldGetPercentagePatternForRoomsSoldsfromOneYearHistoryWhenNotFoundForSameMonthLastYear() {
        //GIVEN
        LocalDate date = LocalDate.now();
        LocalDate lastYear = date.minusYears(1);
        LocalDate startDate = service.getStartDateFor(lastYear);
        LocalDate endDate = startDate.plusYears(1);
        tenantCrudService().executeUpdateByNativeQuery(
                "update Mkt_Accom_Activity set Rooms_Sold = 10, Room_Revenue = 70, Food_Revenue = 30, Total_Revenue = 100.50 " +
                        "where Mkt_Seg_ID = 14 and Occupancy_DT between '" + lastYear + "' and '" + date + "'");
        tenantCrudService().executeUpdateByNativeQuery("insert into IP_Cfg_Mark_Property_Date " +
                "values(5, '" + startDate.plusDays(1) + "', '" + startDate.plusDays(29) + "', 1, '2017-09-06 00:00:00.000', '', 2)");
        Mockito.when(mockDateService.getCaughtUpLocalDate()).thenReturn(org.joda.time.LocalDate.now());
        //WHEN
        PercentagePattern pattern = service.getPercentagePatternForRoomsSolds(1, localDateInterval, BudgetType.BUSINESS_TYPE);
        //THEN
        assertPatternPercentageTotal(pattern);
    }

    @Test
    public void shouldGetPercentagePatternForRevenueFromOneYearHistoryWhenNotFoundForSameMonthLastYear() {
        //GIVEN
        LocalDate date = LocalDate.now();
        LocalDate lastYear = date.minusYears(1);
        LocalDate startDate = service.getStartDateFor(lastYear);
        LocalDate endDate = startDate.plusYears(1);
        tenantCrudService().executeUpdateByNativeQuery(
                "update Mkt_Accom_Activity set Rooms_Sold = 10, Room_Revenue = 70, Food_Revenue = 30, Total_Revenue = 100.50 " +
                        "where Mkt_Seg_ID = 14 and Occupancy_DT between '" + date.minusYears(1) + "' and '" + date + "'");
        tenantCrudService().executeUpdateByNativeQuery("insert into IP_Cfg_Mark_Property_Date " +
                "values(5, '" + startDate.plusDays(1) + "', '" + startDate.plusDays(29) + "', 1, '2017-09-06 00:00:00.000', '', 2)");
        Mockito.when(mockDateService.getCaughtUpLocalDate()).thenReturn(org.joda.time.LocalDate.now());
        //WHEN
        PercentagePattern pattern = service.getPercentagePatternForRevenue(1, localDateInterval, BudgetType.BUSINESS_TYPE);
        //THEN
        assertPatternPercentageTotal(pattern);
    }

    @Test
    public void shouldGetDefaultPercentagePatternForRoomsSoldsWhenNotFoundInTheOneYearHistory() {
        //GIVEN
        LocalDate date = LocalDate.now();
        LocalDate lastYear = date.minusYears(1);
        LocalDate startDate = service.getStartDateFor(lastYear);
        LocalDate endDate = startDate.plusYears(1);
        tenantCrudService().executeUpdateByNativeQuery(
                "delete from Mkt_Accom_Activity");
        Mockito.when(mockDateService.getCaughtUpLocalDate()).thenReturn(org.joda.time.LocalDate.now());
        //WHEN
        PercentagePattern pattern = service.getPercentagePatternForRoomsSolds(1, localDateInterval, BudgetType.BUSINESS_TYPE);
        //THEN
        assertDefaultPattern(pattern);
    }

    @Test
    public void shouldGetDefaultPercentagePatternForRevenueWhenNotFoundInTheOneYearHistory() {
        //GIVEN
        LocalDate date = LocalDate.now();
        LocalDate lastYear = date.minusYears(1);
        LocalDate startDate = service.getStartDateFor(lastYear);
        LocalDate endDate = startDate.plusYears(1);
        tenantCrudService().executeUpdateByNativeQuery(
                "delete from Mkt_Accom_Activity");
        Mockito.when(mockDateService.getCaughtUpLocalDate()).thenReturn(org.joda.time.LocalDate.now());
        //WHEN
        PercentagePattern pattern = service.getPercentagePatternForRevenue(1, localDateInterval, BudgetType.BUSINESS_TYPE);
        //THEN
        assertDefaultPattern(pattern);
    }

    @Disabled
    @Test
    public void shouldBeAbleToGetPercentagePatternForRevenue() {
        PercentagePattern pattern = service.getPercentagePatternForRevenue(1, localDateInterval, BudgetType.BUSINESS_TYPE);
        assertEquals(16, pattern.getMonday().intValue());
        assertEquals(17, pattern.getTuesday().intValue());
        assertEquals(12, pattern.getWednesday().intValue());
        assertEquals(13, pattern.getThursday().intValue());
        assertEquals(13, pattern.getFriday().intValue());
        assertEquals(13, pattern.getSaturday().intValue());
        assertEquals(16, pattern.getSunday().intValue());
        assertPatternPercentageTotal(pattern);
    }

    @Test
    public void shouldFindTotalRoomsRevenueForRevenueByBusinessGroup() {
        BusinessGroup businessGroup1 = UniqueBusinessGroupCreator.createUniqueBusinessGroupWithAssignedMktSegs(2);
        MktSeg mktSeg1 = UniqueMktSegCreator.createUniqueMktSegCreator();
        MktSeg mktSeg2 = UniqueMktSegCreator.createUniqueMktSegCreator();
        MktSeg mktSeg3 = UniqueMktSegCreator.createUniqueMktSegCreator();

        UniqueMktSegBusinessGroupCreator.createBusinessGroup(mktSeg1, businessGroup1);
        UniqueMktSegBusinessGroupCreator.createBusinessGroup(mktSeg2, businessGroup1);
        UniqueMktSegBusinessGroupCreator.createBusinessGroup(mktSeg3, businessGroup1);

        AccomType accomType = UniqueAccomTypeCreator.createUniqueAccomType();

        LocalDate today = LocalDate.now();
        LocalDate lastYearDate = today.minusYears(1);
        UniqueMktSegAccomActivityCreator.createMktSegAccomActivityCreatorFor(PacmanWorkContextHelper.getPropertyId(), mktSeg1.getId(), accomType.getId(), JavaLocalDateUtils.toDate(lastYearDate), 0, 0, 0, 10, 200);
        UniqueMktSegAccomActivityCreator.createMktSegAccomActivityCreatorFor(PacmanWorkContextHelper.getPropertyId(), mktSeg1.getId(), accomType.getId(), JavaLocalDateUtils.toDate(lastYearDate.minusDays(1)), 0, 0, 0, 15, 300);

        UniqueMktSegAccomActivityCreator.createMktSegAccomActivityCreatorFor(PacmanWorkContextHelper.getPropertyId(), mktSeg2.getId(), accomType.getId(), JavaLocalDateUtils.toDate(lastYearDate), 0, 0, 0, 10, 150);
        UniqueMktSegAccomActivityCreator.createMktSegAccomActivityCreatorFor(PacmanWorkContextHelper.getPropertyId(), mktSeg2.getId(), accomType.getId(), JavaLocalDateUtils.toDate(lastYearDate.minusDays(1)), 0, 0, 0, 25, 500);


        UniqueMktSegAccomActivityCreator.createMktSegAccomActivityCreatorFor(PacmanWorkContextHelper.getPropertyId(), mktSeg3.getId(), accomType.getId(), JavaLocalDateUtils.toDate(lastYearDate), 0, 0, 0, 5, 50);
        UniqueMktSegAccomActivityCreator.createMktSegAccomActivityCreatorFor(PacmanWorkContextHelper.getPropertyId(), mktSeg3.getId(), accomType.getId(), JavaLocalDateUtils.toDate(lastYearDate.minusDays(1)), 0, 0, 0, 20, 300);

        tenantCrudService().executeUpdateByNativeQuery("delete from IP_Cfg_Mark_Property_Date ");

        tenantCrudService().executeUpdateByNativeQuery("insert into IP_Cfg_Mark_Property_Date " +
                "values(5, '" + lastYearDate.minusDays(1).toString() + "', '" + lastYearDate.minusDays(1).toString() + "', 1, '2017-09-06 00:00:00.000', '', 2)");

        JavaLocalDateInterval localDateInterval = new JavaLocalDateInterval(today.minusDays(5), today);
        //  commitTransaction(tenantCrudService());
        List<BudgetDataDto> dataDtoList = service.getRoomsSoldsAndRevenueFromSameMonthLastYear(localDateInterval, businessGroup1.getId(), BudgetType.BUSINESS_GROUP);

        assertEquals(1, dataDtoList.size());
        assertEquals(new BigDecimal(400.00).setScale(2), dataDtoList.get(0).getRoomRevenue().setScale(2));
        assertEquals(25, dataDtoList.get(0).getRoomsSold());
    }

    @Test
    public void shouldFindTotalRevenueAndRoomsSoldsByBusinessGroup() {

        BusinessGroup businessGroup1 = UniqueBusinessGroupCreator.createUniqueBusinessGroupWithAssignedMktSegs(2);
        MktSeg mktSeg1 = UniqueMktSegCreator.createUniqueMktSegCreator();
        MktSeg mktSeg2 = UniqueMktSegCreator.createUniqueMktSegCreator();
        MktSeg mktSeg3 = UniqueMktSegCreator.createUniqueMktSegCreator();

        UniqueMktSegBusinessGroupCreator.createBusinessGroup(mktSeg1, businessGroup1);
        UniqueMktSegBusinessGroupCreator.createBusinessGroup(mktSeg2, businessGroup1);
        UniqueMktSegBusinessGroupCreator.createBusinessGroup(mktSeg3, businessGroup1);

        AccomType accomType = UniqueAccomTypeCreator.createUniqueAccomType();

        LocalDate today = LocalDate.now();
        LocalDate lastYearDate = today.minusYears(1);
        UniqueMktSegAccomActivityCreator.createMktSegAccomActivityCreatorFor(PacmanWorkContextHelper.getPropertyId(), mktSeg1.getId(), accomType.getId(), JavaLocalDateUtils.toDate(lastYearDate), 0, 0, 0, 10, 200);
        UniqueMktSegAccomActivityCreator.createMktSegAccomActivityCreatorFor(PacmanWorkContextHelper.getPropertyId(), mktSeg1.getId(), accomType.getId(), JavaLocalDateUtils.toDate(lastYearDate.minusDays(1)), 0, 0, 0, 15, 300);

        UniqueMktSegAccomActivityCreator.createMktSegAccomActivityCreatorFor(PacmanWorkContextHelper.getPropertyId(), mktSeg2.getId(), accomType.getId(), JavaLocalDateUtils.toDate(lastYearDate), 0, 0, 0, 10, 150);
        UniqueMktSegAccomActivityCreator.createMktSegAccomActivityCreatorFor(PacmanWorkContextHelper.getPropertyId(), mktSeg2.getId(), accomType.getId(), JavaLocalDateUtils.toDate(lastYearDate.minusDays(1)), 0, 0, 0, 25, 500);


        UniqueMktSegAccomActivityCreator.createMktSegAccomActivityCreatorFor(PacmanWorkContextHelper.getPropertyId(), mktSeg3.getId(), accomType.getId(), JavaLocalDateUtils.toDate(lastYearDate), 0, 0, 0, 5, 50);
        UniqueMktSegAccomActivityCreator.createMktSegAccomActivityCreatorFor(PacmanWorkContextHelper.getPropertyId(), mktSeg3.getId(), accomType.getId(), JavaLocalDateUtils.toDate(lastYearDate.minusDays(1)), 0, 0, 0, 20, 300);

        tenantCrudService().executeUpdateByNativeQuery("delete from IP_Cfg_Mark_Property_Date ");

        tenantCrudService().executeUpdateByNativeQuery("insert into IP_Cfg_Mark_Property_Date " +
                "values(5, '" + lastYearDate.minusDays(1).toString() + "', '" + lastYearDate.minusDays(1).toString() + "', 1, '2017-09-06 00:00:00.000', '', 2)");

        JavaLocalDateInterval localDateInterval = new JavaLocalDateInterval(today.minusDays(5), today);
        List<BudgetDataDto> dataDtoList = service.getRoomsSoldsAndRevenueFromSameMonthLastYear(localDateInterval, businessGroup1.getId(), BudgetType.BUSINESS_GROUP);

        assertEquals(1, dataDtoList.size());
        assertEquals(new BigDecimal(400.00).setScale(2), dataDtoList.get(0).getRoomRevenue().setScale(2));
        assertEquals(25, dataDtoList.get(0).getRoomsSold());
    }

    @Test
    public void shouldFindPercentageDowValuesForRevenueByBusinessGroup() {
        LocalDate today = LocalDate.now();
        BusinessGroup businessGroup1 = setupBusinessGroupMarketSegmentData(today);
        JavaLocalDateInterval localDateInterval = new JavaLocalDateInterval(today.minusDays(10), today);
        PercentagePattern percentagePattern = service.getPercentagePatternForRoomsSolds(businessGroup1.getId(), localDateInterval, BudgetType.BUSINESS_GROUP);

        assertPatternValuesNonZero(percentagePattern);
        assertPatternPercentageTotal(percentagePattern);
    }

    private void assertPatternValuesNonZero(PercentagePattern percentagePattern) {
        assertTrue(percentagePattern.getMonday() > 0);
        assertTrue(percentagePattern.getTuesday() > 0);
        assertTrue(percentagePattern.getWednesday() > 0);
        assertTrue(percentagePattern.getThursday() > 0);
        assertTrue(percentagePattern.getFriday() > 0);
        assertTrue(percentagePattern.getSaturday() > 0);
        assertTrue(percentagePattern.getSunday() > 0);
    }

    @Test
    public void shouldFindPercentageDowValuesForRoomsSoldByBusinessGroup() {
        LocalDate today = LocalDate.now();
        BusinessGroup businessGroup1 = setupBusinessGroupMarketSegmentData(today);
        JavaLocalDateInterval localDateInterval = new JavaLocalDateInterval(today.minusDays(10), today);
        PercentagePattern percentagePattern = service.getPercentagePatternForRevenue(businessGroup1.getId(), localDateInterval, BudgetType.BUSINESS_GROUP);

        assertPatternValuesNonZero(percentagePattern);
        assertPatternPercentageTotal(percentagePattern);
    }

    private BusinessGroup setupBusinessGroupMarketSegmentData(LocalDate today) {
        BusinessGroup businessGroup1 = UniqueBusinessGroupCreator.createUniqueBusinessGroupWithAssignedMktSegs(2);
        MktSeg mktSeg1 = UniqueMktSegCreator.createUniqueMktSegCreator();
        MktSeg mktSeg2 = UniqueMktSegCreator.createUniqueMktSegCreator();
        MktSeg mktSeg3 = UniqueMktSegCreator.createUniqueMktSegCreator();

        UniqueMktSegBusinessGroupCreator.createBusinessGroup(mktSeg1, businessGroup1);
        UniqueMktSegBusinessGroupCreator.createBusinessGroup(mktSeg2, businessGroup1);
        UniqueMktSegBusinessGroupCreator.createBusinessGroup(mktSeg3, businessGroup1);

        AccomType accomType = UniqueAccomTypeCreator.createUniqueAccomType();
        LocalDate lastYearDate = today.minusYears(1);

        UniqueMktSegAccomActivityCreator.createMktSegAccomActivityCreatorFor(PacmanWorkContextHelper.getPropertyId(), mktSeg1.getId(), accomType.getId(), JavaLocalDateUtils.toDate(lastYearDate), 0, 0, 0, 10, 200);
        UniqueMktSegAccomActivityCreator.createMktSegAccomActivityCreatorFor(PacmanWorkContextHelper.getPropertyId(), mktSeg1.getId(), accomType.getId(), JavaLocalDateUtils.toDate(lastYearDate.minusDays(1)), 0, 0, 0, 15, 300);
        UniqueMktSegAccomActivityCreator.createMktSegAccomActivityCreatorFor(PacmanWorkContextHelper.getPropertyId(), mktSeg1.getId(), accomType.getId(), JavaLocalDateUtils.toDate(lastYearDate.minusDays(2)), 0, 0, 0, 15, 300);
        UniqueMktSegAccomActivityCreator.createMktSegAccomActivityCreatorFor(PacmanWorkContextHelper.getPropertyId(), mktSeg1.getId(), accomType.getId(), JavaLocalDateUtils.toDate(lastYearDate.minusDays(3)), 0, 0, 0, 15, 300);
        UniqueMktSegAccomActivityCreator.createMktSegAccomActivityCreatorFor(PacmanWorkContextHelper.getPropertyId(), mktSeg1.getId(), accomType.getId(), JavaLocalDateUtils.toDate(lastYearDate.minusDays(4)), 0, 0, 0, 15, 300);
        UniqueMktSegAccomActivityCreator.createMktSegAccomActivityCreatorFor(PacmanWorkContextHelper.getPropertyId(), mktSeg1.getId(), accomType.getId(), JavaLocalDateUtils.toDate(lastYearDate.minusDays(5)), 0, 0, 0, 15, 300);
        UniqueMktSegAccomActivityCreator.createMktSegAccomActivityCreatorFor(PacmanWorkContextHelper.getPropertyId(), mktSeg1.getId(), accomType.getId(), JavaLocalDateUtils.toDate(lastYearDate.minusDays(6)), 0, 0, 0, 15, 300);

        UniqueMktSegAccomActivityCreator.createMktSegAccomActivityCreatorFor(PacmanWorkContextHelper.getPropertyId(), mktSeg2.getId(), accomType.getId(), JavaLocalDateUtils.toDate(lastYearDate), 0, 0, 0, 10, 150);
        UniqueMktSegAccomActivityCreator.createMktSegAccomActivityCreatorFor(PacmanWorkContextHelper.getPropertyId(), mktSeg2.getId(), accomType.getId(), JavaLocalDateUtils.toDate(lastYearDate.minusDays(1)), 0, 0, 0, 25, 500);
        UniqueMktSegAccomActivityCreator.createMktSegAccomActivityCreatorFor(PacmanWorkContextHelper.getPropertyId(), mktSeg2.getId(), accomType.getId(), JavaLocalDateUtils.toDate(lastYearDate.minusDays(2)), 0, 0, 0, 25, 500);
        UniqueMktSegAccomActivityCreator.createMktSegAccomActivityCreatorFor(PacmanWorkContextHelper.getPropertyId(), mktSeg2.getId(), accomType.getId(), JavaLocalDateUtils.toDate(lastYearDate.minusDays(3)), 0, 0, 0, 25, 500);
        UniqueMktSegAccomActivityCreator.createMktSegAccomActivityCreatorFor(PacmanWorkContextHelper.getPropertyId(), mktSeg2.getId(), accomType.getId(), JavaLocalDateUtils.toDate(lastYearDate.minusDays(4)), 0, 0, 0, 25, 500);
        UniqueMktSegAccomActivityCreator.createMktSegAccomActivityCreatorFor(PacmanWorkContextHelper.getPropertyId(), mktSeg2.getId(), accomType.getId(), JavaLocalDateUtils.toDate(lastYearDate.minusDays(5)), 0, 0, 0, 25, 500);
        UniqueMktSegAccomActivityCreator.createMktSegAccomActivityCreatorFor(PacmanWorkContextHelper.getPropertyId(), mktSeg2.getId(), accomType.getId(), JavaLocalDateUtils.toDate(lastYearDate.minusDays(6)), 0, 0, 0, 25, 500);


        tenantCrudService().executeUpdateByNativeQuery("delete from IP_Cfg_Mark_Property_Date ");

        tenantCrudService().executeUpdateByNativeQuery("insert into IP_Cfg_Mark_Property_Date " +
                "values(5, '" + today.plusDays(2).toString() + "', '" + today.plusDays(2).toString() + "', 1, '2017-09-06 00:00:00.000', '', 2)");
        return businessGroup1;
    }

    @Test
    public void shouldGetPercentagePatternForRoomsSoldsfromCaughtUpDt() {
        BusinessGroup businessGroup = setupToGetDataOfOneYearBeforeCaughtUpDateScenario();
        PercentagePattern pattern = service.getPercentagePatternForRoomsSolds(businessGroup.getId(), localDateInterval, BudgetType.BUSINESS_GROUP);
        assertPatternPercentageTotal(pattern);
    }

    @Test
    public void shouldGetPercentagePatternForRevenuefromCaughtUpDt() {
        BusinessGroup businessGroup1 = setupToGetDataOfOneYearBeforeCaughtUpDateScenario();
        PercentagePattern pattern = service.getPercentagePatternForRevenue(businessGroup1.getId(), localDateInterval, BudgetType.BUSINESS_GROUP);
        assertPatternPercentageTotal(pattern);
    }

    private void assertPatternPercentageTotal(PercentagePattern pattern) {
        assertEquals(100, pattern.getMonday() + (pattern.getTuesday()) + (pattern.getWednesday())
                + (pattern.getThursday()) + (pattern.getFriday()) + (pattern.getSaturday()) + (pattern.getSunday()));
    }

    private BusinessGroup setupToGetDataOfOneYearBeforeCaughtUpDateScenario() {
        BusinessGroup businessGroup1 = UniqueBusinessGroupCreator.createUniqueBusinessGroupWithAssignedMktSegs(1);
        StringBuilder query = new StringBuilder();
        query.append("insert into Mkt_Seg_Business_Group values (" + businessGroup1.getId() + "," + 14 + ",1,1, getdate(),1,getdate())");
        tenantCrudService().executeUpdateByNativeQuery(query.toString());

        LocalDate date = LocalDate.now();
        LocalDate lastYear = date.minusYears(1);
        LocalDate startDate = service.getStartDateFor(lastYear);
        LocalDate endDate = startDate.plusYears(1);
        tenantCrudService().executeUpdateByNativeQuery(
                "update Mkt_Accom_Activity set Rooms_Sold = 10, Room_Revenue = 70, Food_Revenue = 30, Total_Revenue = 100.50 " +
                        "where Mkt_Seg_ID = 14 and Occupancy_DT between '" + lastYear + "' and '" + date + "'");
        tenantCrudService().executeUpdateByNativeQuery("insert into IP_Cfg_Mark_Property_Date " +
                "values(5, '" + startDate.plusDays(1) + "', '" + startDate.plusDays(29) + "', 1, '2017-09-06 00:00:00.000', '', 2)");
        Mockito.when(mockDateService.getCaughtUpLocalDate()).thenReturn(org.joda.time.LocalDate.now());
        return businessGroup1;
    }

    @Test
    public void shouldGetDefaultPercentagePatternForRoomSoldWhenNoDataFoundForBusinessGroup() {
        BusinessGroup businessGroup = setupDataToGetDefaultPatternForBusinessGroup();
        PercentagePattern pattern = service.getPercentagePatternForRoomsSolds(businessGroup.getId(), localDateInterval, BudgetType.BUSINESS_GROUP);
        assertDefaultPattern(pattern);
    }

    private BusinessGroup setupDataToGetDefaultPatternForBusinessGroup() {
        BusinessGroup businessGroup = UniqueBusinessGroupCreator.createUniqueBusinessGroupWithAssignedMktSegs(1);
        Mockito.when(mockDateService.getCaughtUpLocalDate()).thenReturn(org.joda.time.LocalDate.now());
        return businessGroup;
    }

    @Test
    public void shouldGetDefaultPercentagePatternForRevenueWhenNoDataFoundForBusinessGroup() {
        BusinessGroup businessGroup = setupDataToGetDefaultPatternForBusinessGroup();
        PercentagePattern pattern = service.getPercentagePatternForRevenue(businessGroup.getId(), localDateInterval, BudgetType.BUSINESS_GROUP);
        assertDefaultPattern(pattern);
    }

    private void assertDefaultPattern(PercentagePattern pattern) {
        assertEquals(0, pattern.getMonday().intValue());
        assertEquals(0, pattern.getTuesday().intValue());
        assertEquals(0, pattern.getWednesday().intValue());
        assertEquals(0, pattern.getThursday().intValue());
        assertEquals(0, pattern.getFriday().intValue());
        assertEquals(0, pattern.getSaturday().intValue());
        assertEquals(0, pattern.getSunday().intValue());
        assertEquals(0, pattern.getMonday() + (pattern.getTuesday()) + (pattern.getWednesday())
                + (pattern.getThursday()) + (pattern.getFriday()) + (pattern.getSaturday()) + (pattern.getSunday()));
    }

}
