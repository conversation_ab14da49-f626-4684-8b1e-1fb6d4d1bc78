package com.ideas.tetris.pacman.services.informationmanager.notification.service;

import com.google.common.collect.Sets;
import com.ideas.cache.redis.configuration.IdeasRedisCacheManager;
import com.ideas.g3.data.TestProperty;
import com.ideas.g3.test.AbstractG3JupiterTest;
import com.ideas.infra.tetris.security.domain.LDAPUser;
import com.ideas.tetris.pacman.common.configparams.AlertConfigParamName;
import com.ideas.tetris.pacman.common.configparams.GUIConfigParamName;
import com.ideas.tetris.pacman.common.configparams.IPConfigParamName;
import com.ideas.tetris.pacman.common.configparams.IntegrationConfigParamName;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomClass;
import com.ideas.tetris.pacman.services.accommodation.entity.UniqueAccomClassCreator;
import com.ideas.tetris.pacman.services.dashboard.DashboardService;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.informationmanager.alert.AlertEvaluationException;
import com.ideas.tetris.pacman.services.informationmanager.alert.service.AlertService;
import com.ideas.tetris.pacman.services.informationmanager.dto.AlertType;
import com.ideas.tetris.pacman.services.informationmanager.dto.ExceptionConfigDTO;
import com.ideas.tetris.pacman.services.informationmanager.entity.InfoMgrExcepNotifEntity;
import com.ideas.tetris.pacman.services.informationmanager.entity.InfoMgrTypeEntity;
import com.ideas.tetris.pacman.services.informationmanager.entity.InformationMgrAlertConfigEntity;
import com.ideas.tetris.pacman.services.informationmanager.entity.InformationMgrLevelEntity;
import com.ideas.tetris.pacman.services.informationmanager.entity.InformationMgrSubTypeEntity;
import com.ideas.tetris.pacman.services.informationmanager.enums.ExceptionSubType;
import com.ideas.tetris.pacman.services.informationmanager.enums.LevelType;
import com.ideas.tetris.pacman.services.informationmanager.enums.MetricType;
import com.ideas.tetris.pacman.services.informationmanager.enums.RelationalOperator;
import com.ideas.tetris.pacman.services.informationmanager.enums.SubLevelType;
import com.ideas.tetris.pacman.services.informationmanager.exception.service.ExceptionAlertService;
import com.ideas.tetris.pacman.services.informationmanager.exception.service.ExceptionConfigService;
import com.ideas.tetris.pacman.services.informationmanager.exception.service.SystemExceptionEvaluatorService;
import com.ideas.tetris.pacman.services.informationmanager.exception.types.InfoMgrExcepNotifSnoozerFactory;
import com.ideas.tetris.pacman.services.informationmanager.notification.interfaces.INotificationEvaluator;
import com.ideas.tetris.pacman.services.informationmanager.notification.querybuilder.DecisionChangeFromLastNightlyOptimization;
import com.ideas.tetris.pacman.services.marketsegment.entity.BusinessType;
import com.ideas.tetris.pacman.services.propertygroup.service.PropertyGroupService;
import com.ideas.tetris.pacman.services.security.AuthorizationService;
import com.ideas.tetris.pacman.services.security.RoleService;
import com.ideas.tetris.pacman.services.security.UserAuthorizedPropertyCache;
import com.ideas.tetris.pacman.services.security.UserService;
import com.ideas.tetris.pacman.services.specialevent.service.SpecialEventService;
import com.ideas.tetris.pacman.services.unqualifiedrate.entity.RateUnqualified;
import com.ideas.tetris.platform.common.crudservice.AbstractMultiPropertyCrudService;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import com.ideas.tetris.platform.services.globalproperty.service.ClientPropertyCacheService;
import org.apache.commons.lang.time.DateUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import javax.persistence.Query;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collection;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.ideas.tetris.pacman.services.informationmanager.enums.RelationalOperator.GREATER_OR_EQUAL;
import static com.ideas.tetris.pacman.services.informationmanager.enums.RelationalOperator.LESS_THAN_OR_EQUAL;
import static com.ideas.tetris.pacman.services.informationmanager.notification.service.DecisionChangeEvaluatorForOverbookingTest.getNotificationEvaluator;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.anyObject;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;


@MockitoSettings(strictness = Strictness.LENIENT)
public class DecisionChangedEvaluatorServiceTest extends AbstractG3JupiterTest {

    DashboardService mockDashboardService = null;

    public INotificationEvaluator instance = null;

    SystemExceptionEvaluatorService exceptionEvaluator = null;

    DateService mockDateService;

    ExceptionConfigService alertExceptionService;

    PropertyGroupService propertyGroupService;

    PacmanConfigParamsService mockConfigParamsService;

    ExceptionAlertService exceptionAlertService;

    AlertService alertService;

    PacmanConfigParamsService configParamService;

    String businessDateStr = null;

    private Date dateInBetweenRange = null;

    private AccomClass accomClass;

    private PacmanConfigParamsService mockConfigService;


    private SpecialEventService specialEventService;

    private CrudService crudService;

    private AuthorizationService authService;

    private InfoMgrExcepNotifSnoozerFactory infoMgrExcepNotifSnoozerFactory;

    @BeforeEach
    public void setUp() {
        mockDateService = DateService.createTestInstance();
        CrudService crudService = tenantCrudService();
        mockDateService.setCrudService(crudService);
        propertyGroupService = new PropertyGroupService();
        mockDateService.setPropertyGroupService(propertyGroupService);
        mockDateService.setMultiPropertyCrudService(multiPropertyCrudService());
        alertExceptionService = new ExceptionConfigService();
        exceptionEvaluator = new SystemExceptionEvaluatorService();
        mockDashboardService = Mockito.mock(DashboardService.class);
        specialEventService = Mockito.mock(SpecialEventService.class);
        mockConfigParamsService = Mockito.mock(PacmanConfigParamsService.class);
        configParamService = Mockito.mock(PacmanConfigParamsService.class);
        mockDateService.setConfigParamsService(configParamService);
        alertExceptionService.crudService = tenantCrudService();
        alertExceptionService.setGlobalCrudService(globalCrudService());
        alertExceptionService.setMultiPropertyCrudService(multiPropertyCrudService());
        exceptionAlertService = new ExceptionAlertService();
        exceptionAlertService.setGlobalCrudService(globalCrudService());
        exceptionAlertService.setMultiPropertyCrudService(multiPropertyCrudService());
        exceptionEvaluator.setMultiPropertyCrudService(multiPropertyCrudService());
        exceptionEvaluator.setDateService(mockDateService);
        authService = new AuthorizationService();
        authService.setCrudService(tenantCrudService());
        authService.setGlobalCrudService(globalCrudService());
        alertService = new AlertService();
        alertService.setMultiPropertyCrudService(multiPropertyCrudService());
        infoMgrExcepNotifSnoozerFactory = Mockito.mock(InfoMgrExcepNotifSnoozerFactory.class);
        instance = getNotificationEvaluator(AlertType.DecisionChangeEx, mockConfigParamsService, multiPropertyCrudService(), globalCrudService(), mockDateService, specialEventService, tenantCrudService(), infoMgrExcepNotifSnoozerFactory);
        exceptionEvaluator.setAuthorizationService(authService);
        // exceptionEvaluator.decisionChangeEvaluatorService  = instance;
        alertExceptionService.setAuthorizationService(authService);
        setUpTestDataForDecisionChangePricing();
        MockitoAnnotations.initMocks(this);
        UserService userService = Mockito.mock(UserService.class);
        inject(authService, "userService", userService);
        RoleService roleService = Mockito.mock(RoleService.class);
        inject(authService, "roleService", roleService);
        LDAPUser u1 = new LDAPUser();
        u1.setUserId(11403);
        ArrayList<String> roleList = new ArrayList<>();
        roleList.add("roleId=-666 ON GROUP groupId=-666");
        u1.setRoles(roleList);
        when(userService.getById("11403")).thenReturn(u1);
        when(roleService.getPropertiesForUser(anyString(), anyObject())).thenReturn(Sets.newHashSet("5"));
        when(roleService.getPermsForUser(anyString(), anyString(), anyObject())).thenReturn(Sets.newHashSet("-666"));
        ClientPropertyCacheService clientPropertyCacheService = new ClientPropertyCacheService();
        authService.setClientPropertyCacheService(clientPropertyCacheService);
        UserAuthorizedPropertyCache userAuthorizedPropertyCache = new UserAuthorizedPropertyCache();
        userAuthorizedPropertyCache.setIdeasRedisCacheManager(new IdeasRedisCacheManager());
        authService.setUserAuthorizedPropertyCache(userAuthorizedPropertyCache);
    }

    private void mockConfigParameterService(String expectedReturnParameter, TestProperty tenantCode, int noOfReturnTimes, String clientCode) {
        mockConfigService = Mockito.mock(PacmanConfigParamsService.class);
        when(mockConfigService.getValue("pacman." + clientCode, GUIConfigParamName.CORE_DISPLAY_PROPERTY_CODE_OR_NAME.value())).thenReturn(expectedReturnParameter);
        authService.setPacmanConfigParamsService(mockConfigService);
        alertExceptionService.setConfigParamService(mockConfigService);
    }

    private void setUpTestDataForDecisionChangePricing() {
        accomClass = UniqueAccomClassCreator.createUniqueAccomClass();
        Date businessDate = mockDateService.getBusinessDate();
        businessDateStr = new SimpleDateFormat("yyyy-MM-dd").format(businessDate);
        dateInBetweenRange = DateUtil.addDaysToDate(businessDate, 3);
        Date lastBusinessDate = DateUtil.addDaysToDate(businessDate, -1);
        String lastbusinessDateStr = new SimpleDateFormat("yyyy-MM-dd").format(lastBusinessDate);
        String dateInBetweenRangeStr = new SimpleDateFormat("yyyy-MM-dd").format(dateInBetweenRange);
        BigInteger decisionid = getMaxDecisionIDforBusinessDate(businessDateStr);
        BigInteger lastdecisionid = getMaxDecisionIDforBusinessDate(lastbusinessDateStr);
        // insert the records into [PACE_LRV] table
        Query qryAccom = tenantCrudService().getEntityManager().createNativeQuery("INSERT INTO PACE_LRV(Decision_ID, Property_ID, Accom_Class_ID " + " ,Occupancy_DT, LRV,CreateDate_DTTM)VALUES(:decisionId,5," + accomClass.getId() + ",:dateInBetween,200,GETDATE())");
        qryAccom.setParameter("decisionId", decisionid);
        qryAccom.setParameter("dateInBetween", dateInBetweenRangeStr);
        qryAccom.executeUpdate();
        // insert the records into [PACE_LRV] table
        qryAccom = tenantCrudService().getEntityManager().createNativeQuery("INSERT INTO PACE_LRV(Decision_ID, Property_ID, Accom_Class_ID " + " ,Occupancy_DT, LRV,CreateDate_DTTM)VALUES(:decisionId,5," + accomClass.getId() + ",:dateInBetween,100,GETDATE())");
        qryAccom.setParameter("decisionId", lastdecisionid);
        qryAccom.setParameter("dateInBetween", dateInBetweenRangeStr);
        qryAccom.executeUpdate();
        Map<String, Object> lastParameters = new HashMap<>();
        lastParameters.put("decisionId", decisionid);
        lastParameters.put("dateInBetween", dateInBetweenRangeStr);
        lastParameters.put("accomClassId", accomClass.getId());
        // insert the records into [PACE_LRV_NOTIFICATION] table
        tenantCrudService().executeUpdateByNativeQuery("INSERT INTO PACE_LRV_NOTIFICATION(Decision_ID, Property_ID, Accom_Class_ID " + " ,Occupancy_DT, LRV,CreateDate_DTTM)VALUES(:decisionId,5,:accomClassId,:dateInBetween,200,GETDATE())", lastParameters);
        lastParameters.put("decisionId", lastdecisionid);
        // insert the records into [PACE_LRV_NOTIFICATION] table
        tenantCrudService().executeUpdateByNativeQuery("INSERT INTO PACE_LRV_NOTIFICATION(Decision_ID, Property_ID, Accom_Class_ID " + " ,Occupancy_DT, LRV,CreateDate_DTTM)VALUES(:decisionId,5,:accomClassId,:dateInBetween,100,GETDATE())", lastParameters);
    }

    private BigInteger getMaxDecisionIDforBusinessDate(String businessDateStr) {
        Query qry = tenantCrudService().getEntityManager().createNativeQuery("select dbo.ufn_get_decision_id_by_property(:propertyId,1,:businessDate)");
        qry.setParameter("propertyId", PROPERTY_ID5);
        qry.setParameter("businessDate", businessDateStr);
        List<BigInteger> currentresultList = qry.getResultList();
        return currentresultList.get(0);
    }

    @Test
    public void calculatePercentageChanged() {
        BigDecimal ninety = new BigDecimal(90.00);
        BigDecimal hundred = new BigDecimal(100.00);
        BigDecimal oneOhOne = new BigDecimal(101.00);
        BigDecimal oneTen = new BigDecimal(110.00);
        assertEquals(10.0, instance.calculatePercentageChanged(hundred, oneTen).doubleValue(), 0.01);
        assertEquals(1.0, instance.calculatePercentageChanged(hundred, oneOhOne).doubleValue(), 0.01);
        assertEquals(-9.1, instance.calculatePercentageChanged(oneTen, hundred).doubleValue(), 0.01);
        assertEquals(0, instance.calculatePercentageChanged(hundred, hundred).doubleValue(), 0.01);
        assertEquals(-10.0, instance.calculatePercentageChanged(hundred, ninety).doubleValue(), 0.01);
    }

    @Test
    public void parseDate() throws AlertEvaluationException {
        SimpleDateFormat sdf = new SimpleDateFormat("yyMMdd");
        String today = sdf.format(mockDateService.getCaughtUpDate());
        Date resultDate = instance.parseDate("TODAY");
        String result = sdf.format(resultDate);
        assertEquals(today, result);
    }

    @Test
    public void parseDate_badString() throws AlertEvaluationException {
        assertThrows(AlertEvaluationException.class, () -> {
            instance.parseDate("XXX");
        });
    }

    @Test
    public void parseDate_plusX() throws AlertEvaluationException {
        assertThrows(AlertEvaluationException.class, () -> {
            instance.parseDate("TODAY+X");
        });
    }

    @Test
    public void parseDate_plusNothing() throws AlertEvaluationException {
        assertThrows(AlertEvaluationException.class, () -> {
            instance.parseDate("TODAY+");
        });
    }

    @Test
    public void parseDate_plus30() throws AlertEvaluationException {
        SimpleDateFormat sdf = new SimpleDateFormat("yyMMdd");
        Date expectedDate = DateUtils.addDays(mockDateService.getCaughtUpDate(), 30);
        Date resultDate = instance.parseDate("TODAY+30");
        String expected = sdf.format(expectedDate);
        String result = sdf.format(resultDate);
        assertEquals(expected, result);
        resultDate = instance.parseDate("Today+30");
        assertEquals(expected, sdf.format(resultDate));
        resultDate = instance.parseDate("toDay+30");
        assertEquals(expected, sdf.format(resultDate));
    }

    @Test
    public void parseDate_minus1() throws AlertEvaluationException {
        Date expectedDate = DateUtils.addDays(mockDateService.getCaughtUpDate(), -1);
        Date resultDate = instance.parseDate("TODAY-1");
        SimpleDateFormat sdf = new SimpleDateFormat("yyMMdd");
        String expected = sdf.format(expectedDate);
        String result = sdf.format(resultDate);
        assertEquals(expected, result);
    }

    @Test
    public void parseDate_fixedDate() throws AlertEvaluationException {
        Date expected = new GregorianCalendar(2012, Calendar.MARCH, 7).getTime();
        String dateString = "03/07/2012";
        assertEquals(expected, instance.parseDate(dateString));
    }

    @Test
    public void findExceptionsToProcess() {
        InfoMgrTypeEntity alertTypeEntity = findAlertType("DecisionChangeEx");
        InformationMgrAlertConfigEntity config1 = createConfig(alertTypeEntity, false);
        InformationMgrAlertConfigEntity config2 = createConfig(alertTypeEntity, false);
        InformationMgrAlertConfigEntity config3 = createConfig(alertTypeEntity, false);
        InformationMgrAlertConfigEntity config4 = createConfig(alertTypeEntity, false);
        config4.setPropertyId(PacmanWorkContextHelper.getPropertyId());
        multiPropertyCrudService().save(config1);
        multiPropertyCrudService().save(config2);
        multiPropertyCrudService().save(config3);
        multiPropertyCrudService().save(config4);
        Mockito.when(configParamService.getParameterValue(IntegrationConfigParamName.VARIABLE_DECISION_WINDOW_DAYS.value())).thenReturn("30");
        Mockito.when(configParamService.getParameterValue(IPConfigParamName.OPTIMIZATION_OPTIMIZATION_WINDOW_BDE.value())).thenReturn("365");
        Mockito.when(configParamService.getParameterValue(IntegrationConfigParamName.COMPLETE_DECISION_UPLOAD_DAYS_OF_WEEK.value())).thenReturn("sunday");
        Mockito.when(configParamService.isApplyVaribaleDecisionWindow()).thenReturn(true);
        Mockito.when(configParamService.getBooleanParameterValue(IntegrationConfigParamName.OVERRIDE_VARIABLE_DECISION_WINDOW.value())).thenReturn(true);
        Collection<InformationMgrAlertConfigEntity> found = instance.findExceptionConfigurationsToProcess(AlertType.DecisionChangeEx.toString());
        assertFalse(found.isEmpty());
    }

    private InfoMgrTypeEntity findAlertType(String name) {
        InfoMgrTypeEntity alertTypeEntity = new InfoMgrTypeEntity();
        alertTypeEntity.setName("DecisionChangeEx");
        alertTypeEntity.setEnabled(true);
        alertTypeEntity = tenantCrudService().findByExampleSingleResult(alertTypeEntity);
        assertNotNull(alertTypeEntity);
        return alertTypeEntity;
    }

    private InformationMgrAlertConfigEntity createConfig(InfoMgrTypeEntity alertType) {
        return createConfig(alertType, false);
    }

    private InformationMgrAlertConfigEntity createConfig(InfoMgrTypeEntity alertType, boolean disabled) {
        InformationMgrLevelEntity objExceptionLevelEntity = tenantCrudService().findByNamedQuerySingleResult(InformationMgrLevelEntity.BY_NAME, QueryParameter.with("name", LevelType.ROOM_CLASS.getCode()).parameters());
        InformationMgrSubTypeEntity objExceptionSubTypeEntity = tenantCrudService().findByNamedQuerySingleResult(InformationMgrSubTypeEntity.BY_NAME, QueryParameter.with("name", ExceptionSubType.LRV.getCode()).parameters());
        InformationMgrAlertConfigEntity config = new InformationMgrAlertConfigEntity();
        config.setAlertTypeEntity(alertType);
        config.setCreatedByUserId(1);
        config.setDisabled(disabled);
        config.setExceptionLevel(objExceptionLevelEntity);
        config.setExceptionSubLevel(1);
        config.setExceptionSubType(objExceptionSubTypeEntity);
        config.setStartDate("Today +1");
        config.setEndDate("Today +90");
        config.setFrequency("1");
        config.setStatusId(1);
        config.setThresholdMetricType(MetricType.CURRENCY);
        config.setThresholdOperator(GREATER_OR_EQUAL.getCode());
        config.setThresholdValue(BigDecimal.ZERO);
        config.setSubLevelKeywordUsed(true);
        config.setPropertyId(6);
        return config;
    }

    @Test
    public void cutTheScoreInHalf() {
        InfoMgrExcepNotifEntity exception = new InfoMgrExcepNotifEntity();
        exception.setScore(77);
        assertEquals(39, instance.cutTheScoreInHalf(exception));
        exception.setScore(10);
        assertEquals(5, instance.cutTheScoreInHalf(exception));
        exception.setScore(5);
        assertEquals(3, instance.cutTheScoreInHalf(exception));
        exception.setScore(3);
        assertEquals(2, instance.cutTheScoreInHalf(exception));
        exception.setScore(1);
        assertEquals(0, instance.cutTheScoreInHalf(exception));
    }

    @Test
    public void hasExceededThreshold() throws AlertEvaluationException {
        BigDecimal threshold = new BigDecimal(40);
        String greaterEQ = GREATER_OR_EQUAL.getCode();
        String neq = RelationalOperator.NOT_EQUAL.getCode();
        String lessEq = LESS_THAN_OR_EQUAL.getCode();
        String increasedByOrDecreasedBy = RelationalOperator.INCREASED_OR_DECREASED_BY.getCode();
        InformationMgrAlertConfigEntity config = new InformationMgrAlertConfigEntity();
        InfoMgrTypeEntity infoMgrType = new InfoMgrTypeEntity();
        infoMgrType.setName("DecisionChangeEx");
        config.setAlertTypeEntity(infoMgrType);
        InformationMgrSubTypeEntity subType = new InformationMgrSubTypeEntity();
        subType.setName(ExceptionSubType.PRICING.getCode());
        config.setExceptionSubType(subType);
        assertTrue(instance.hasExceededThreshold(threshold, new BigDecimal(50.0), RelationalOperator.findByCode(greaterEQ), config));
        assertFalse(instance.hasExceededThreshold(threshold, new BigDecimal(-50.0), RelationalOperator.findByCode(greaterEQ), config));
        assertTrue(instance.hasExceededThreshold(threshold, new BigDecimal(40.0), RelationalOperator.findByCode(greaterEQ), config));
        assertFalse(instance.hasExceededThreshold(threshold, BigDecimal.valueOf(39.9), RelationalOperator.findByCode(greaterEQ), config));
        assertTrue(instance.hasExceededThreshold(threshold, BigDecimal.valueOf(-40), RelationalOperator.findByCode(lessEq), config));
        assertTrue(instance.hasExceededThreshold(threshold, BigDecimal.valueOf(-50), RelationalOperator.findByCode(lessEq), config));
        assertTrue(instance.hasExceededThreshold(threshold, BigDecimal.valueOf(-41), RelationalOperator.findByCode(lessEq), config));
        assertFalse(instance.hasExceededThreshold(threshold, BigDecimal.valueOf(1), RelationalOperator.findByCode(lessEq), config));
        assertFalse(instance.hasExceededThreshold(threshold, BigDecimal.valueOf(-30), RelationalOperator.findByCode(lessEq), config));
        assertFalse(instance.hasExceededThreshold(threshold, BigDecimal.valueOf(-20), RelationalOperator.findByCode(lessEq), config));
        assertFalse(instance.hasExceededThreshold(threshold, BigDecimal.valueOf(-39), RelationalOperator.findByCode(lessEq), config));
        assertFalse(instance.hasExceededThreshold(threshold, BigDecimal.valueOf(30), RelationalOperator.findByCode(lessEq), config));
        assertFalse(instance.hasExceededThreshold(threshold, BigDecimal.valueOf(40), RelationalOperator.findByCode(lessEq), config));
        assertFalse(instance.hasExceededThreshold(threshold, BigDecimal.valueOf(50), RelationalOperator.findByCode(lessEq), config));
        assertTrue(instance.hasExceededThreshold(threshold, new BigDecimal(50.0), RelationalOperator.findByCode(neq), null));
        assertFalse(instance.hasExceededThreshold(threshold, new BigDecimal(39.0), RelationalOperator.findByCode(neq), null));
        assertFalse(instance.hasExceededThreshold(threshold, new BigDecimal(-39.0), RelationalOperator.findByCode(neq), null));
        assertTrue(instance.hasExceededThreshold(threshold, new BigDecimal(-41.0), RelationalOperator.findByCode(neq), null));
        assertFalse(instance.hasExceededThreshold(threshold, BigDecimal.valueOf(40), RelationalOperator.findByCode(neq), null));
        assertTrue(instance.hasExceededThreshold(threshold, new BigDecimal(40.0), RelationalOperator.findByCode(increasedByOrDecreasedBy), null));
        assertTrue(instance.hasExceededThreshold(threshold, new BigDecimal(-40.0), RelationalOperator.findByCode(increasedByOrDecreasedBy), null));
        assertTrue(instance.hasExceededThreshold(threshold, new BigDecimal(41.0), RelationalOperator.findByCode(increasedByOrDecreasedBy), null));
        assertTrue(instance.hasExceededThreshold(threshold, new BigDecimal(-41.0), RelationalOperator.findByCode(increasedByOrDecreasedBy), null));
        assertFalse(instance.hasExceededThreshold(threshold, new BigDecimal(39.0), RelationalOperator.findByCode(increasedByOrDecreasedBy), null));
        assertFalse(instance.hasExceededThreshold(threshold, new BigDecimal(-39.0), RelationalOperator.findByCode(increasedByOrDecreasedBy), null));
        assertFalse(instance.hasExceededThreshold(threshold, new BigDecimal(39.5), RelationalOperator.findByCode(increasedByOrDecreasedBy), null));
        assertFalse(instance.hasExceededThreshold(threshold, new BigDecimal(-39.5), RelationalOperator.findByCode(increasedByOrDecreasedBy), null));
    }

    @Test
    public void shouldExceededThresholdForPriceByRankWhenCurrentDecisionRankIsGreaterThanOrEqualThresholdRank() throws AlertEvaluationException {
        InformationMgrAlertConfigEntity config = new InformationMgrAlertConfigEntity();
        InfoMgrTypeEntity infoMgrType = new InfoMgrTypeEntity();
        infoMgrType.setName(AlertType.DecisionAsOfLastNightlyOptimization.toString());
        config.setAlertTypeEntity(infoMgrType);
        InformationMgrSubTypeEntity subType = new InformationMgrSubTypeEntity();
        subType.setName(ExceptionSubType.PRICING.getCode());
        config.setExceptionSubType(subType);
        BigDecimal threshold = new BigDecimal(2);
        assertTrue(instance.hasExceededThreshold(threshold, new BigDecimal(1), GREATER_OR_EQUAL, config));
        assertTrue(instance.hasExceededThreshold(threshold, new BigDecimal(2), GREATER_OR_EQUAL, config));
        assertFalse(instance.hasExceededThreshold(threshold, new BigDecimal(3), GREATER_OR_EQUAL, config));
    }

    @Test
    public void shouldExceededThresholdForPriceByRankWhenCurrentDecisionRankIsLessThanOrEqualToThresholdRank() throws AlertEvaluationException {
        InformationMgrAlertConfigEntity config = new InformationMgrAlertConfigEntity();
        InfoMgrTypeEntity infoMgrType = new InfoMgrTypeEntity();
        infoMgrType.setName("DecisionAsOfLastNightlyOptimization");
        config.setAlertTypeEntity(infoMgrType);
        InformationMgrSubTypeEntity subType = new InformationMgrSubTypeEntity();
        subType.setName(ExceptionSubType.PRICING.getCode());
        config.setExceptionSubType(subType);
        BigDecimal threshold = new BigDecimal(2);
        assertTrue(instance.hasExceededThreshold(threshold, new BigDecimal(3), LESS_THAN_OR_EQUAL, config));
        assertTrue(instance.hasExceededThreshold(threshold, new BigDecimal(2), LESS_THAN_OR_EQUAL, config));
    }

    @Test
    public void calculateScore() {
        InfoMgrTypeEntity alertType = new InfoMgrTypeEntity();
        alertType.setScoreIncrement(10);
        alertType.setBaseScore(50);
        InfoMgrExcepNotifEntity exceptionAlert = new InfoMgrExcepNotifEntity();
        exceptionAlert.setAlertType(alertType);
        exceptionAlert.setScore(alertType.getBaseScore());
        InformationMgrSubTypeEntity subType = new InformationMgrSubTypeEntity();
        subType.setName(ExceptionSubType.LRV.getCode());
        exceptionAlert.setSubType(subType);
        BigDecimal threshold = BigDecimal.TEN;
        String operator = GREATER_OR_EQUAL.getCode();
        assertEquals(175, instance.calculateScore(exceptionAlert, threshold, new BigDecimal(25), operator));
        assertEquals(100, instance.calculateScore(exceptionAlert, threshold, BigDecimal.TEN, operator));
    }

    @Test
    public void shouldCalculateScoreWhenDeltaIsGreaterThanThreshold() {
        InfoMgrExcepNotifEntity exception = new InfoMgrExcepNotifEntity();
        exception.setScore(0);
        InformationMgrSubTypeEntity subType = new InformationMgrSubTypeEntity();
        subType.setName(ExceptionSubType.LRV.getCode());
        exception.setSubType(subType);
        InfoMgrTypeEntity alertType = new InfoMgrTypeEntity();
        alertType.setBaseScore(40);
        exception.setAlertType(alertType);
        assertEquals(60, instance.calculateScore(exception, new BigDecimal(100), new BigDecimal(150), GREATER_OR_EQUAL.getCode()));
    }

    @Test
    public void shouldCalculateScoreWhenDeltaIsLessThanThreshold() {
        InfoMgrExcepNotifEntity exception = new InfoMgrExcepNotifEntity();
        exception.setScore(0);
        InformationMgrSubTypeEntity subType = new InformationMgrSubTypeEntity();
        subType.setName(ExceptionSubType.LRV.getCode());
        exception.setSubType(subType);
        InfoMgrTypeEntity alertType = new InfoMgrTypeEntity();
        alertType.setBaseScore(40);
        exception.setAlertType(alertType);
        assertEquals(20, instance.calculateScore(exception, new BigDecimal(100), new BigDecimal(50), LESS_THAN_OR_EQUAL.getCode()));
    }

    @Test
    public void shouldCalculateScoreWhenDeltaIsEqualToThreshold() {
        InfoMgrExcepNotifEntity exception = new InfoMgrExcepNotifEntity();
        exception.setScore(0);
        InfoMgrTypeEntity alertType = new InfoMgrTypeEntity();
        alertType.setBaseScore(40);
        exception.setAlertType(alertType);
        InformationMgrSubTypeEntity subType = new InformationMgrSubTypeEntity();
        subType.setName(ExceptionSubType.LRV.getCode());
        exception.setSubType(subType);
        assertEquals(40, instance.calculateScore(exception, new BigDecimal(100), new BigDecimal(100), LESS_THAN_OR_EQUAL.getCode()));
    }

    @Test
    public void shouldBeAbleToCalculateDeltaForStaticThresholdForPriceByValueWhenCurrentValueIsGreaterThanThreshold() {
        Map<String, RateUnqualified> rateUnqualifieds = new HashMap<>();
        InformationMgrAlertConfigEntity config = new InformationMgrAlertConfigEntity();
        InfoMgrTypeEntity alertType = new InfoMgrTypeEntity();
        alertType.setName(AlertType.DecisionAsOfLastNightlyOptimization.toString());
        config.setAlertTypeEntity(alertType);
        InformationMgrSubTypeEntity exceptionSubType = new InformationMgrSubTypeEntity();
        exceptionSubType.setName(ExceptionSubType.PRICING_BY_VALUE.getCode());
        config.setExceptionSubType(exceptionSubType);
        assertEquals(new BigDecimal("150.00"), instance.calculateDeltaForStaticThreshold(config, new BigDecimal(100), "150.00", null, rateUnqualifieds));
    }

    @Test
    public void shouldBeAbleToCalculateDeltaForStaticThresholdForPriceByValueWhenCurrentValueIsLessThanThreshold() {
        Map<String, RateUnqualified> rateUnqualifieds = new HashMap<>();
        InformationMgrAlertConfigEntity config = new InformationMgrAlertConfigEntity();
        InfoMgrTypeEntity alertType = new InfoMgrTypeEntity();
        alertType.setName(AlertType.DecisionAsOfLastNightlyOptimization.toString());
        config.setAlertTypeEntity(alertType);
        InformationMgrSubTypeEntity exceptionSubType = new InformationMgrSubTypeEntity();
        exceptionSubType.setName(ExceptionSubType.PRICING_BY_VALUE.getCode());
        config.setExceptionSubType(exceptionSubType);
        assertEquals(new BigDecimal("150.00"), instance.calculateDeltaForStaticThreshold(config, new BigDecimal(100), "50.00", null, rateUnqualifieds));
    }

    @Test
    public void shouldBeAbleToCalculateDeltaForStaticThresholdForPriceByValueWhenCurrentValueIsEqualToThreshold() {
        Map<String, RateUnqualified> rateUnqualifieds = new HashMap<>();
        InformationMgrAlertConfigEntity config = new InformationMgrAlertConfigEntity();
        InfoMgrTypeEntity alertType = new InfoMgrTypeEntity();
        alertType.setName(AlertType.DecisionAsOfLastNightlyOptimization.toString());
        config.setAlertTypeEntity(alertType);
        InformationMgrSubTypeEntity exceptionSubType = new InformationMgrSubTypeEntity();
        exceptionSubType.setName(ExceptionSubType.PRICING_BY_VALUE.getCode());
        config.setExceptionSubType(exceptionSubType);
        assertEquals(new BigDecimal("100.00"), instance.calculateDeltaForStaticThreshold(config, new BigDecimal(100), "100.00", null, rateUnqualifieds));
    }

    @Test
    public void shouldReturnDeltaAsItIsWhenAlertIsNotStaticThreshold() {
        Map<String, RateUnqualified> rateUnqualifieds = new HashMap<>();
        InformationMgrAlertConfigEntity config = new InformationMgrAlertConfigEntity();
        InfoMgrTypeEntity alertType = new InfoMgrTypeEntity();
        alertType.setName(AlertType.DecisionChangeEx.toString());
        config.setAlertTypeEntity(alertType);
        assertEquals(new BigDecimal("100"), instance.calculateDeltaForStaticThreshold(config, null, null, new BigDecimal(100), rateUnqualifieds));
    }

    @Test
    public void shouldBeAbleToCalculateDeltaForStaticThresholdForPriceByRankWhenCurrentRankIsGreaterThanThreshold() {
        Map<String, RateUnqualified> rateUnqualifieds = new HashMap<>();
        RateUnqualified rateUnqualified = new RateUnqualified();
        rateUnqualified.setName("LV0");
        rateUnqualified.setRanking(1);
        rateUnqualifieds.put("LV0", rateUnqualified);
        InformationMgrAlertConfigEntity config = new InformationMgrAlertConfigEntity();
        config.setPropertyId(5);
        InfoMgrTypeEntity alertType = new InfoMgrTypeEntity();
        alertType.setName(AlertType.DecisionAsOfLastNightlyOptimization.toString());
        config.setAlertTypeEntity(alertType);
        InformationMgrSubTypeEntity exceptionSubType = new InformationMgrSubTypeEntity();
        exceptionSubType.setName(ExceptionSubType.PRICING.getCode());
        config.setExceptionSubType(exceptionSubType);
        AbstractMultiPropertyCrudService mockMultiPropertyCrudService = mock(AbstractMultiPropertyCrudService.class);
        instance.setMultiPropertyCrudService(mockMultiPropertyCrudService);
        RateUnqualified returnedRate = new RateUnqualified();
        returnedRate.setRanking(1);
        when(mockMultiPropertyCrudService.findByNamedQuerySingleResultForSingleProperty(5, RateUnqualified.FIND_RANK_BY_NAME, QueryParameter.with("name", "LV0").parameters())).thenReturn(returnedRate);
        assertEquals(new BigDecimal("5"), instance.calculateDeltaForStaticThreshold(config, new BigDecimal(3), "LV0", null, rateUnqualifieds));
    }

    @Test
    public void shouldBeAbleToCalculateDeltaForStaticThresholdForPriceByRankWhenCurrentRankIsLessThanThreshold() {
        Map<String, RateUnqualified> rateUnqualifieds = new HashMap<>();
        RateUnqualified rateUnqualified = new RateUnqualified();
        rateUnqualified.setName("LV3");
        rateUnqualified.setRanking(3);
        rateUnqualifieds.put("LV3", rateUnqualified);
        InformationMgrAlertConfigEntity config = new InformationMgrAlertConfigEntity();
        config.setPropertyId(5);
        InfoMgrTypeEntity alertType = new InfoMgrTypeEntity();
        alertType.setName(AlertType.DecisionAsOfLastNightlyOptimization.toString());
        config.setAlertTypeEntity(alertType);
        InformationMgrSubTypeEntity exceptionSubType = new InformationMgrSubTypeEntity();
        exceptionSubType.setName(ExceptionSubType.PRICING.getCode());
        config.setExceptionSubType(exceptionSubType);
        AbstractMultiPropertyCrudService mockMultiPropertyCrudService = mock(AbstractMultiPropertyCrudService.class);
        instance.setMultiPropertyCrudService(mockMultiPropertyCrudService);
        RateUnqualified returnedRate = new RateUnqualified();
        returnedRate.setRanking(3);
        when(mockMultiPropertyCrudService.findByNamedQuerySingleResultForSingleProperty(5, RateUnqualified.FIND_RANK_BY_NAME, QueryParameter.with("name", "LV3").parameters())).thenReturn(returnedRate);
        assertEquals(new BigDecimal("3"), instance.calculateDeltaForStaticThreshold(config, new BigDecimal(1), "LV3", null, rateUnqualifieds));
    }

    @Test
    public void shouldBeAbleToCalculateDeltaForStaticThresholdForPriceByRankWhenCurrentRankIsEqualToThreshold() {
        Map<String, RateUnqualified> rateUnqualifieds = new HashMap<>();
        RateUnqualified rateUnqualified = new RateUnqualified();
        rateUnqualified.setName("LV0");
        rateUnqualified.setRanking(1);
        rateUnqualifieds.put("LV0", rateUnqualified);
        InformationMgrAlertConfigEntity config = new InformationMgrAlertConfigEntity();
        config.setPropertyId(5);
        InfoMgrTypeEntity alertType = new InfoMgrTypeEntity();
        alertType.setName(AlertType.DecisionAsOfLastNightlyOptimization.toString());
        config.setAlertTypeEntity(alertType);
        InformationMgrSubTypeEntity exceptionSubType = new InformationMgrSubTypeEntity();
        exceptionSubType.setName(ExceptionSubType.PRICING.getCode());
        config.setExceptionSubType(exceptionSubType);
        AbstractMultiPropertyCrudService mockMultiPropertyCrudService = mock(AbstractMultiPropertyCrudService.class);
        instance.setMultiPropertyCrudService(mockMultiPropertyCrudService);
        RateUnqualified returnedRate = new RateUnqualified();
        returnedRate.setRanking(1);
        when(mockMultiPropertyCrudService.findByNamedQuerySingleResultForSingleProperty(5, RateUnqualified.FIND_RANK_BY_NAME, QueryParameter.with("name", "LV0").parameters())).thenReturn(returnedRate);
        assertEquals(new BigDecimal("1"), instance.calculateDeltaForStaticThreshold(config, new BigDecimal(1), "LV0", null, rateUnqualifieds));
    }

    @Test
    public void calculateMultiplierGreaterThanOperator() {
        String operator = GREATER_OR_EQUAL.getCode();
        assertEquals(new BigDecimal(3.4000).setScale(4, RoundingMode.HALF_UP), instance.calculateMultiplier(new BigDecimal(34.0), operator, BigDecimal.TEN));
        assertEquals(new BigDecimal(4.0000).setScale(4, RoundingMode.HALF_UP), instance.calculateMultiplier(new BigDecimal(40.0), operator, BigDecimal.TEN));
        assertEquals(new BigDecimal(1.1000).setScale(4, RoundingMode.HALF_UP), instance.calculateMultiplier(new BigDecimal(11.0), operator, BigDecimal.TEN));
        assertEquals(new BigDecimal(1.0100).setScale(4, RoundingMode.HALF_UP), instance.calculateMultiplier(BigDecimal.valueOf(10.1), operator, BigDecimal.TEN));
        assertEquals(new BigDecimal(9.9990).setScale(4, RoundingMode.HALF_UP), instance.calculateMultiplier(BigDecimal.valueOf(99.99), operator, BigDecimal.TEN));
        assertEquals(new BigDecimal(1.5000).setScale(4, RoundingMode.HALF_UP), instance.calculateMultiplier(new BigDecimal(15), operator, BigDecimal.TEN));
        assertEquals(new BigDecimal(2.1429).setScale(4, RoundingMode.HALF_UP), instance.calculateMultiplier(new BigDecimal(15), operator, new BigDecimal(7)));
        assertEquals(new BigDecimal(3.3333).setScale(4, RoundingMode.HALF_UP), instance.calculateMultiplier(BigDecimal.TEN, operator, new BigDecimal(3)));
    }

    @Test
    public void createAndUpdateExceptionwithDeltaZero() {
        List<Object[]> queryResultList = new ArrayList<>();
        Object[] obj = new Object[8];
        obj[0] = 1;
        obj[1] = 1;
        obj[2] = "2012-01-01";
        obj[3] = 1;
        // currentOptVal
        obj[4] = new BigDecimal(100);
        obj[5] = 1;
        // lastOptVal
        obj[6] = new BigDecimal(100);
        // delta
        obj[7] = new BigDecimal(0.0045);
        queryResultList.add(obj);
        mockConfigParameterService("Code", TestProperty.H1, 2, "BSTN");
        List<InformationMgrAlertConfigEntity> listOfExcep = alertExceptionService.findAllExceptions();
        int initialSize = listOfExcep.size();
        List<Integer> listPropertyIds = new ArrayList<>();
        listPropertyIds.add(PROPERTY_ID5);
        Map<String, List<InformationMgrAlertConfigEntity>> resultList = alertExceptionService.persistExceptionConfiguration(buildExceptionConfiguration(ExceptionSubType.LRV.getCode(), LevelType.ROOM_CLASS.getCode(), accomClass.getCode(), listPropertyIds, false));
        tenantCrudService().flush();
        listOfExcep = resultList.get(SUCCESS);
        try {
            instance.createAndUpdateException(queryResultList, listOfExcep.get(0), mockDateService.getBusinessDate());
        } catch (AlertEvaluationException e) {
            e.printStackTrace();
        }
        List<InfoMgrExcepNotifEntity> list = tenantCrudService().findAll(InfoMgrExcepNotifEntity.class);
        assertNotNull(list);
        assertEquals(0, list.size());
    }

    @Test
    public void createAndUpdateExceptionwithDeltaindecimalslessThanThreshhold() {
        List<Object[]> queryResultList = new ArrayList<>();
        Object[] obj = new Object[8];
        obj[0] = 1;
        obj[1] = 1;
        obj[2] = "2012-01-01";
        obj[3] = 1;
        // currentOptVal
        obj[4] = new BigDecimal(100);
        obj[5] = 1;
        // lastOptVal
        obj[6] = new BigDecimal(100);
        // delta
        obj[7] = new BigDecimal(1.80);
        queryResultList.add(obj);
        mockConfigParameterService("Code", TestProperty.H1, 2, "BSTN");
        List<InformationMgrAlertConfigEntity> listOfExcep = alertExceptionService.findAllExceptions();
        int initialSize = listOfExcep.size();
        List<Integer> listPropertyIds = new ArrayList<>();
        listPropertyIds.add(PROPERTY_ID5);
        Map<String, List<InformationMgrAlertConfigEntity>> resultList = alertExceptionService.persistExceptionConfiguration(buildExceptionConfiguration(ExceptionSubType.LRV.getCode(), LevelType.ROOM_CLASS.getCode(), accomClass.getCode(), listPropertyIds, false));
        tenantCrudService().flush();
        listOfExcep = resultList.get(SUCCESS);
        try {
            instance.createAndUpdateException(queryResultList, listOfExcep.get(0), mockDateService.getBusinessDate());
        } catch (AlertEvaluationException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
        List<InfoMgrExcepNotifEntity> list = tenantCrudService().findAll(InfoMgrExcepNotifEntity.class);
        assertNotNull(list);
        assertEquals(0, list.size());
    }

    @Test
    public void createAndUpdateExceptionWithDeltaZeroWithLessThanOperator() throws Exception {
        List<Object[]> queryResultList = new ArrayList<>();
        Object[] obj = new Object[8];
        obj[0] = 1;
        obj[1] = 1;
        obj[2] = DateUtil.addDaysToDate(new Date(), 1);
        obj[3] = 1;
        // currentOptVal
        obj[4] = new BigDecimal(100);
        obj[5] = 1;
        // lastOptVal
        obj[6] = new BigDecimal(100);
        // delta
        obj[7] = new BigDecimal(-20);
        queryResultList.add(obj);
        mockConfigParameterService("Code", TestProperty.H1, 2, "BSTN");
        List<InformationMgrAlertConfigEntity> listOfExcep = alertExceptionService.findAllExceptions();
        int initialSize = listOfExcep.size();
        List<Integer> listPropertyIds = new ArrayList<>();
        listPropertyIds.add(PROPERTY_ID5);
        Map<String, List<InformationMgrAlertConfigEntity>> resultList = alertExceptionService.persistExceptionConfiguration(buildExceptionConfiguration(ExceptionSubType.LRV.getCode(), LevelType.ROOM_CLASS.getCode(), accomClass.getCode(), listPropertyIds, false));
        tenantCrudService().flush();
        listOfExcep = resultList.get(SUCCESS);
        Mockito.when(mockConfigParamsService.getParameterValue(AlertConfigParamName.USER_EXCEPTIONS_ENABLED)).thenReturn(true);
        listOfExcep.get(0).setThresholdOperator(LESS_THAN_OR_EQUAL.getCode());
        instance.createAndUpdateException(queryResultList, listOfExcep.get(0), mockDateService.getBusinessDate());
        List<InfoMgrExcepNotifEntity> list = tenantCrudService().findAll(InfoMgrExcepNotifEntity.class);
        assertEquals(1, list.size());
        assertEquals(list.get(0).getCreatedBy(), "SSO User");
    }

    @Test
    public void createAndUpdateExceptionWithDeltaInDecimalsAndGreaterThanThreshold() throws Exception {
        List<Object[]> queryResultList = new ArrayList<>();
        Object[] obj = new Object[8];
        obj[0] = 1;
        obj[1] = 1;
        obj[2] = DateUtil.addDaysToDate(new Date(), 1);
        obj[3] = 1;
        // currentOptVal
        obj[4] = new BigDecimal(100);
        obj[5] = 1;
        // lastOptVal
        obj[6] = new BigDecimal(100);
        // delta
        obj[7] = new BigDecimal(2.80);
        queryResultList.add(obj);
        mockConfigParameterService("Code", TestProperty.H1, 2, "BSTN");
        List<InformationMgrAlertConfigEntity> listOfExcep = alertExceptionService.findAllExceptions();
        int initialSize = listOfExcep.size();
        List<Integer> listPropertyIds = new ArrayList<>();
        listPropertyIds.add(PROPERTY_ID5);
        Map<String, List<InformationMgrAlertConfigEntity>> resultList = alertExceptionService.persistExceptionConfiguration(buildExceptionConfiguration(ExceptionSubType.LRV.getCode(), LevelType.ROOM_CLASS.getCode(), accomClass.getCode(), listPropertyIds, false));
        tenantCrudService().flush();
        listOfExcep = resultList.get(SUCCESS);
        Mockito.when(mockConfigParamsService.getParameterValue(AlertConfigParamName.USER_EXCEPTIONS_ENABLED)).thenReturn(true);
        instance.createAndUpdateException(queryResultList, listOfExcep.get(0), mockDateService.getBusinessDate());
        List<InfoMgrExcepNotifEntity> list = tenantCrudService().findAll(InfoMgrExcepNotifEntity.class);
        assertEquals(1, list.size());
    }

    @Test
    public void calculateNotDefinedOperator() {
        assertThrows(IllegalArgumentException.class, () -> {
            String operator = "Not Defined";
            assertEquals(null, instance.calculateMultiplier(new BigDecimal(34.0), operator, BigDecimal.TEN));
        });
    }

    @Test
    public void calculateMultiplierLessThanOperator() {
        String operator = LESS_THAN_OR_EQUAL.getCode();
        assertEquals(new BigDecimal(3.4000).setScale(4, RoundingMode.HALF_UP), instance.calculateMultiplier(new BigDecimal(34.0), operator, BigDecimal.TEN));
        assertEquals(new BigDecimal(4.0000).setScale(4, RoundingMode.HALF_UP), instance.calculateMultiplier(new BigDecimal(40.0), operator, BigDecimal.TEN));
        assertEquals(new BigDecimal(1.1000).setScale(4, RoundingMode.HALF_UP), instance.calculateMultiplier(new BigDecimal(11.0), operator, BigDecimal.TEN));
        assertEquals(new BigDecimal(1.0100).setScale(4, RoundingMode.HALF_UP), instance.calculateMultiplier(BigDecimal.valueOf(10.1), operator, BigDecimal.TEN));
        assertEquals(new BigDecimal(9.9990).setScale(4, RoundingMode.HALF_UP), instance.calculateMultiplier(BigDecimal.valueOf(99.99), operator, BigDecimal.TEN));
        assertEquals(new BigDecimal(1.5000).setScale(4, RoundingMode.HALF_UP), instance.calculateMultiplier(new BigDecimal(15), operator, BigDecimal.TEN));
        assertEquals(new BigDecimal(2.1429).setScale(4, RoundingMode.HALF_UP), instance.calculateMultiplier(new BigDecimal(15), operator, new BigDecimal(7)));
        assertEquals(new BigDecimal(3.3333).setScale(4, RoundingMode.HALF_UP), instance.calculateMultiplier(BigDecimal.TEN, operator, new BigDecimal(3)));
    }

    @Test
    public void calculateMultiplierNotEqualsOperator() {
        String operator = RelationalOperator.NOT_EQUAL.getCode();
        assertEquals(new BigDecimal(3.4000).setScale(4, RoundingMode.HALF_UP), instance.calculateMultiplier(new BigDecimal(34.0), operator, BigDecimal.TEN));
        assertEquals(new BigDecimal(4.0000).setScale(4, RoundingMode.HALF_UP), instance.calculateMultiplier(new BigDecimal(40.0), operator, BigDecimal.TEN));
        assertEquals(new BigDecimal(1.1000).setScale(4, RoundingMode.HALF_UP), instance.calculateMultiplier(new BigDecimal(11.0), operator, BigDecimal.TEN));
        assertEquals(new BigDecimal(1.0100).setScale(4, RoundingMode.HALF_UP), instance.calculateMultiplier(BigDecimal.valueOf(10.1), operator, BigDecimal.TEN));
        assertEquals(new BigDecimal(9.9990).setScale(4, RoundingMode.HALF_UP), instance.calculateMultiplier(BigDecimal.valueOf(99.99), operator, BigDecimal.TEN));
        assertEquals(new BigDecimal(1.5000).setScale(4, RoundingMode.HALF_UP), instance.calculateMultiplier(new BigDecimal(15), operator, BigDecimal.TEN));
        assertEquals(new BigDecimal(2.1429).setScale(4, RoundingMode.HALF_UP), instance.calculateMultiplier(new BigDecimal(15), operator, new BigDecimal(7)));
        assertEquals(new BigDecimal(3.3333).setScale(4, RoundingMode.HALF_UP), instance.calculateMultiplier(BigDecimal.TEN, operator, new BigDecimal(3)));
    }

    public static final int PROPERTY_ID5 = 5;

    public static final String SUCCESS = "SUCCESS";

    public static final String TODAY = "Today";

    public static final String TODAY7 = "Today+7";

    public static final String FREQUENCY = "1";

    @Test
    public void testDecisionChangeForLRV() {
        List<Integer> listPropertyIds = new ArrayList<>();
        listPropertyIds.add(PROPERTY_ID5);
        mockConfigParameterService("Code", TestProperty.H1, 2, "BSTN");
        List<InformationMgrAlertConfigEntity> listOfExcep = alertExceptionService.findAllExceptions();
        int initialSize = listOfExcep.size();
        Map<String, List<InformationMgrAlertConfigEntity>> resultList = alertExceptionService.persistExceptionConfiguration(buildExceptionConfiguration(ExceptionSubType.LRV.getCode(), LevelType.ROOM_CLASS.getCode(), accomClass.getCode(), listPropertyIds, false));
        tenantCrudService().flush();
        listOfExcep = resultList.get(SUCCESS);
        Mockito.when(mockConfigParamsService.getParameterValue(AlertConfigParamName.USER_EXCEPTIONS_ENABLED)).thenReturn(true);
        instance.evaluateByType(listOfExcep, AlertType.DecisionChangeEx, new DecisionChangeFromLastNightlyOptimization(), false);
        List<InfoMgrExcepNotifEntity> list = tenantCrudService().findAll(InfoMgrExcepNotifEntity.class);
        assertEquals(1, list.size());
        assertTrue(list.get(0).getCurrentOptimizationValue().equals("200.00"));
        assertTrue(list.get(0).getLastOptimizationValue().equals("100.00"));
    }

    @Test
    public void testDecisionChangeForLRV_getDetails() {
        List<Integer> listPropertyIds = new ArrayList<>();
        listPropertyIds.add(PROPERTY_ID5);
        List<InformationMgrAlertConfigEntity> listOfExcep;
        Map<String, List<InformationMgrAlertConfigEntity>> resultList = alertExceptionService.persistExceptionConfiguration(buildExceptionConfiguration(ExceptionSubType.LRV.getCode(), LevelType.ROOM_CLASS.getCode(), accomClass.getCode(), listPropertyIds, false));
        tenantCrudService().flush();
        listOfExcep = resultList.get(SUCCESS);
        Mockito.when(mockConfigParamsService.getParameterValue(AlertConfigParamName.USER_EXCEPTIONS_ENABLED)).thenReturn(true);
        Object[] obj = new Object[8];
        obj[0] = 1;
        obj[1] = 1;
        obj[2] = dateInBetweenRange;
        obj[3] = 1;
        // currentOptVal
        obj[4] = new BigDecimal(200);
        obj[5] = 1;
        // lastOptVal
        obj[6] = new BigDecimal(100);
        // delta
        obj[7] = new BigDecimal(100);
        String actualString;
        actualString = instance.createDetails(listOfExcep.get(0), dateInBetweenRange, "200", new BigDecimal(100), new BigDecimal(50), obj, null);
        String expectedString = "current.last.room.value:200.00,last.room.value change:100,defined.threshold:>= 50";
        assertTrue(actualString.contains(expectedString));
    }

    @Test
    public void testDecisionChangeForLRV_getDetails_MasterClass() {
        List<Integer> listPropertyIds = new ArrayList<>();
        listPropertyIds.add(PROPERTY_ID5);
        List<InformationMgrAlertConfigEntity> listOfExcep;
        Map<String, List<InformationMgrAlertConfigEntity>> resultList = alertExceptionService.persistExceptionConfiguration(buildExceptionConfiguration(ExceptionSubType.LRV.getCode(), LevelType.ROOM_CLASS.getCode(), SubLevelType.MASTER_CLASS.getCode(), listPropertyIds, true));
        tenantCrudService().flush();
        listOfExcep = resultList.get(SUCCESS);
        Mockito.when(mockConfigParamsService.getParameterValue(AlertConfigParamName.USER_EXCEPTIONS_ENABLED)).thenReturn(true);
        Object[] obj = new Object[8];
        obj[0] = 1;
        obj[1] = 1;
        obj[2] = dateInBetweenRange;
        obj[3] = 1;
        // currentOptVal
        obj[4] = new BigDecimal(200);
        obj[5] = 1;
        // lastOptVal
        obj[6] = new BigDecimal(100);
        // delta
        obj[7] = new BigDecimal(100);
        String actualString;
        actualString = instance.createDetails(listOfExcep.get(0), dateInBetweenRange, "200", new BigDecimal(100), new BigDecimal(50), obj, null);
        String expectedString = "room.class:master.class (DLX),current.last.room.value:200.00,last.room.value change:100,defined.threshold:>= 50";
        assertTrue(actualString.contains(expectedString));
    }

    @Test
    public void testDecisionChangeForLRV_getDetails_Property() {
        List<Integer> listPropertyIds = new ArrayList<>();
        listPropertyIds.add(PROPERTY_ID5);
        List<InformationMgrAlertConfigEntity> listOfExcep;
        Map<String, List<InformationMgrAlertConfigEntity>> resultList = alertExceptionService.persistExceptionConfiguration(buildExceptionConfiguration(ExceptionSubType.LRV.getCode(), LevelType.PROPERTY.getCode(), "Master Class", listPropertyIds, true));
        tenantCrudService().flush();
        listOfExcep = resultList.get(SUCCESS);
        Mockito.when(mockConfigParamsService.getParameterValue(AlertConfigParamName.USER_EXCEPTIONS_ENABLED)).thenReturn(true);
        Object[] obj = new Object[8];
        obj[0] = 1;
        obj[1] = 1;
        obj[2] = dateInBetweenRange;
        obj[3] = 1;
        // currentOptVal
        obj[4] = new BigDecimal(200);
        obj[5] = 1;
        // lastOptVal
        obj[6] = new BigDecimal(100);
        // delta
        obj[7] = new BigDecimal(100);
        String actualString;
        actualString = instance.createDetails(listOfExcep.get(0), dateInBetweenRange, "200", new BigDecimal(100), new BigDecimal(50), obj, null);
        String expectedString = "level:property,current.last.room.value:200.00,last.room.value change:100,defined.threshold:>= 50";
        assertTrue(actualString.contains(expectedString));
    }

    @Test
    public void testDecisionChangeForLRV_getDetails_BusinessType() {
        BusinessType objBusinessType = tenantCrudService().findByNamedQuerySingleResult(BusinessType.BY_NAME, QueryParameter.with("name", "Transient").parameters());
        List<Integer> listPropertyIds = new ArrayList<>();
        listPropertyIds.add(PROPERTY_ID5);
        List<InformationMgrAlertConfigEntity> listOfExcep;
        Map<String, List<InformationMgrAlertConfigEntity>> resultList = alertExceptionService.persistExceptionConfiguration(buildExceptionConfiguration(ExceptionSubType.LRV.getCode(), LevelType.BUSINESS_TYPE.getCode(), objBusinessType.getName(), listPropertyIds, false));
        tenantCrudService().flush();
        listOfExcep = resultList.get(SUCCESS);
        Mockito.when(mockConfigParamsService.getParameterValue(AlertConfigParamName.USER_EXCEPTIONS_ENABLED)).thenReturn(true);
        Object[] obj = new Object[8];
        obj[0] = 1;
        obj[1] = 1;
        obj[2] = dateInBetweenRange;
        obj[3] = 1;
        // currentOptVal
        obj[4] = new BigDecimal(200);
        obj[5] = 1;
        // lastOptVal
        obj[6] = new BigDecimal(100);
        // delta
        obj[7] = new BigDecimal(100);
        String actualString;
        actualString = instance.createDetails(listOfExcep.get(0), dateInBetweenRange, "200", new BigDecimal(100), new BigDecimal(50), obj, null);
        String expectedString = "business.type:Transient,current.last.room.value:200.00,last.room.value change:100,defined.threshold:>= 50";
        assertTrue(actualString.contains(expectedString));
    }

    @Test
    public void testCreateAndUpdateExceptionWhenSpecialEventPresent() throws AlertEvaluationException {
        Date date = DateUtil.addDaysToDate(new Date(), 1);
        String specialEvent = "SPE1;SPE2";
        Map<Date, String> dateMap = new HashMap<>();
        dateMap.put(date, specialEvent);
        when(specialEventService.getSpecialEventsBetweenDates(any(Date.class), any(Date.class))).thenReturn(dateMap);
        List<Object[]> queryResultList = new ArrayList<>();
        Object[] obj = new Object[8];
        obj[0] = 1;
        obj[1] = 1;
        obj[2] = date;
        obj[3] = 1;
        // currentOptVal
        obj[4] = new BigDecimal(100);
        obj[5] = 1;
        // lastOptVal
        obj[6] = new BigDecimal(100);
        // delta
        obj[7] = new BigDecimal(-20);
        queryResultList.add(obj);
        mockConfigParameterService("Code", TestProperty.H1, 2, "BSTN");
        List<InformationMgrAlertConfigEntity> listOfExcep = alertExceptionService.findAllExceptions();

        List<Integer> listPropertyIds = new ArrayList<>();
        listPropertyIds.add(PROPERTY_ID5);
        Map<String, List<InformationMgrAlertConfigEntity>> resultList = alertExceptionService.persistExceptionConfiguration(buildExceptionConfiguration(ExceptionSubType.LRV.getCode(), LevelType.ROOM_CLASS.getCode(), accomClass.getCode(), listPropertyIds, false));
        tenantCrudService().flush();
        listOfExcep = resultList.get(SUCCESS);
        Mockito.when(mockConfigParamsService.getParameterValue(AlertConfigParamName.USER_EXCEPTIONS_ENABLED)).thenReturn(true);
        listOfExcep.get(0).setThresholdOperator(LESS_THAN_OR_EQUAL.getCode());

        instance.createAndUpdateException(queryResultList, listOfExcep.get(0), mockDateService.getBusinessDate());

        List<InfoMgrExcepNotifEntity> list = tenantCrudService().findAll(InfoMgrExcepNotifEntity.class);
        assertEquals(1, list.size());
        if (!instance.getClass().getName().contains("NotificationEvaluatorV2")) {
            assertTrue(list.get(0).getDetails().contains(specialEvent));
        }
    }

    private ExceptionConfigDTO buildExceptionConfiguration(String subType, String levelType, String sublevelType, List<Integer> listPropertyIds, boolean subLevelHasKeyword) {
        ExceptionConfigDTO objExceptionConfigDTO = new ExceptionConfigDTO();
        InfoMgrTypeEntity type = tenantCrudService().findByNamedQuerySingleResult(InfoMgrTypeEntity.BY_NAME, QueryParameter.with("name", AlertType.DecisionChangeEx.toString()).parameters());
        InformationMgrLevelEntity objExceptionLevelEntity = tenantCrudService().findByNamedQuerySingleResult(InformationMgrLevelEntity.BY_NAME, QueryParameter.with("name", levelType).parameters());
        InformationMgrSubTypeEntity objExceptionSubTypeEntity = tenantCrudService().findByNamedQuerySingleResult(InformationMgrSubTypeEntity.BY_NAME, QueryParameter.with("name", subType).parameters());
        objExceptionConfigDTO.setAlertTypeEntity(type);
        objExceptionConfigDTO.setDisabled(false);
        objExceptionConfigDTO.setEndDate(new SimpleDateFormat("MM/dd/yyyy").format(dateInBetweenRange));
        objExceptionConfigDTO.setExceptionLevel(objExceptionLevelEntity);
        objExceptionConfigDTO.setExceptionSubLevel(sublevelType);
        objExceptionConfigDTO.setExceptionSubType(objExceptionSubTypeEntity);
        objExceptionConfigDTO.setFrequency(FREQUENCY);
        objExceptionConfigDTO.setPropertyIds(listPropertyIds);
        objExceptionConfigDTO.setStartDate(new SimpleDateFormat("MM/dd/yyyy").format(dateInBetweenRange));
        objExceptionConfigDTO.setThresholdConstraint(">=");
        objExceptionConfigDTO.setThresholdValue(new BigDecimal(2));
        objExceptionConfigDTO.setStatusId(1);
        objExceptionConfigDTO.setMetricType(MetricType.CURRENCY);
        objExceptionConfigDTO.setSubLevelHasKeyword(subLevelHasKeyword);
        return objExceptionConfigDTO;
    }
}
