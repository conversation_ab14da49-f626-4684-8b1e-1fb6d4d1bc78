package com.ideas.tetris.pacman.services.reportsquery.pickUpAndChange;

import com.ideas.g3.data.TestProperty;
import com.ideas.g3.test.AbstractG3JupiterTest;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.PrepareTestDataForFetchingNonComponentRoomTypes;
import com.ideas.tetris.pacman.services.specialevent.entity.PropertySpecialEvent;
import com.ideas.tetris.pacman.services.specialevent.entity.PropertySpecialEventInstance;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.common.utils.map.MapBuilder;
import org.joda.time.LocalDate;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;

import java.text.SimpleDateFormat;
import java.util.Comparator;
import java.util.Date;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 * Created by idnrar on 19-09-2014.
 */
public class PickUpAndChangeBusinessTypeLevelDataTest extends AbstractG3JupiterTest {

    public static final String BUSINESS_TYPE_GROUP = "Group";
    public static final String BUSINESS_TYPE_TRANSIENT = "Transient";
    private LocalDate startDate;
    private Date javaStartDate;
    private String dow;
    private String dow1;
    private String dow2;
    private int propertyID = 6;
    private int recordTypeId = 3;
    private int processStatusId = 13;
    private int isRolling = 0;
    private int mktSeg1 = 7;
    private int mktSeg2 = 8;
    private int mktSeg3 = 9;
    private int mktSeg4 = 10;

    @BeforeEach
    public void setUp() {
        setWorkContextProperty(TestProperty.H2);
        startDate = getLocalDate(TestProperty.H2.getId().toString());
    }

    private void createTestData() {
        StringBuilder insertQuery = new StringBuilder();
        retrieveDayOfWeekForDateEqualToSystemDatePlusSix();

        int firstDecisionId = retrieveDecsionIDForBusinessDateEqualToSystemDateMinusEleven();
        int secondDecisionId = retrieveDecsionIDForBusinessDateEqualToSystemDateMinusEight();

        populateForecastPaceForMarketSegmentForFirstPacePoint(insertQuery, firstDecisionId);
        populateForecastPaceForMarketSegmentForSecondPacePoint(insertQuery, secondDecisionId);
        populateActivityPaceForMarketSegment(insertQuery);
        populateSpecialEventData(insertQuery);
        UpdateOccupancyForecastForGivenOccupancyDate(insertQuery);
        updateMktActivityData(insertQuery);
        updateMarketSegmentDetailsDataForGroupBusinessType(insertQuery);

        tenantCrudService().executeUpdateByNativeQuery(insertQuery.toString());
    }

    private void createTestData_FullJoin() {
        StringBuilder insertQuery = new StringBuilder();

        retrieveDayOfWeekForDateEqualToSystemDatePlusSix();

        int firstDecisionId = retrieveDecsionIDForBusinessDateEqualToSystemDateMinusEleven();
        int secondDecisionId = retrieveDecsionIDForBusinessDateEqualToSystemDateMinusEight();

        populateForecastPaceForMarketSegmentForFirstPacePoint(insertQuery, firstDecisionId);
        populateForecastPaceForMarketSegmentForSecondPacePoint(insertQuery, secondDecisionId);
        populateActivityPaceForMarketSegment(insertQuery);
        populateSpecialEventData(insertQuery);
        UpdateOccupancyForecastForGivenOccupancyDate(insertQuery);
        updateMktActivityData(insertQuery);

        retrieveDayOfWeekForDateEqualToSystemDatePlusSix();
        retrieveDayOfWeekForDateEqualToSystemDatePlusSeven();
        retrieveDayOfWeekForDateEqualToSystemDatePlusEight();

        populateForecastPaceForMarketSegmentFor_Transient(insertQuery, firstDecisionId, secondDecisionId, 7);
        populateForecastPaceForMarketSegmentFor_Group(insertQuery, firstDecisionId, secondDecisionId, 8);

        populateActivityPaceForMarketSegment_Transient(insertQuery, 7);
        populateActivityPaceForMarketSegment_Group(insertQuery, 8);

        UpdateOccupancyForecastForGivenOccupancyDate_Transient(insertQuery, 7);
        UpdateOccupancyForecastForGivenOccupancyDate_Group(insertQuery, 8);

        updateMktActivityData_Transient(insertQuery, 7);
        updateMktActivityData_Group(insertQuery, 8);

        updateMarketSegmentDetailsDataForGroupBusinessType(insertQuery);

        tenantCrudService().executeUpdateByNativeQuery(insertQuery.toString());
    }


    private void updateMarketSegmentDetailsDataForGroupBusinessType(StringBuilder insertQuery) {
        insertQuery.append(" update Mkt_Seg_Details set Business_Type_ID=1 where Mkt_Seg_ID in (" + mktSeg3 + "," + mktSeg4 + ") ");
    }


    private void updateMktActivityData(StringBuilder insertQuery) {
        insertQuery.append(" UPDATE [Mkt_Accom_Activity] SET [Rooms_Sold] = 11,[Arrivals] = 7,[Departures] = 2,[Cancellations] = 1, ");
        insertQuery.append(" [No_Shows] = 1,[Room_Revenue] = 653.14500,[Food_Revenue] = 21.98720,[Total_Revenue] = 633.76512 ");
        insertQuery.append(" WHERE Occupancy_DT='" + startDate.plusDays(6).toString() + "' and Mkt_Seg_ID=" + mktSeg1 + " ");
        insertQuery.append(" UPDATE [Mkt_Accom_Activity] SET [Rooms_Sold] = 13,[Arrivals] = 9,[Departures] = 3,[Cancellations] = 2, ");
        insertQuery.append(" [No_Shows] = 1,[Room_Revenue] = 655.12500,[Food_Revenue] = 21.98720,[Total_Revenue] = 643.76512 ");
        insertQuery.append(" WHERE Occupancy_DT='" + startDate.plusDays(6).toString() + "' and Mkt_Seg_ID=" + mktSeg2 + " ");
        insertQuery.append(" UPDATE [Mkt_Accom_Activity] SET [Rooms_Sold] = 15,[Arrivals] = 11,[Departures] = 4,[Cancellations] = 3, ");
        insertQuery.append(" [No_Shows] = 1,[Room_Revenue] = 677.67500,[Food_Revenue] = 23.98720,[Total_Revenue] = 653.76512 ");
        insertQuery.append(" WHERE Occupancy_DT='" + startDate.plusDays(6).toString() + "' and Mkt_Seg_ID=" + mktSeg3 + " ");
        insertQuery.append(" UPDATE [Mkt_Accom_Activity] SET [Rooms_Sold] = 17,[Arrivals] = 13,[Departures] = 5,[Cancellations] = 4, ");
        insertQuery.append(" [No_Shows] = 1,[Room_Revenue] = 689.13700,[Food_Revenue] = 25.98720,[Total_Revenue] = 663.76512 ");
        insertQuery.append(" WHERE Occupancy_DT='" + startDate.plusDays(6).toString() + "' and Mkt_Seg_ID=" + mktSeg4 + " ");
    }

    private void updateMktActivityData_Transient(StringBuilder insertQuery, int day) {
        insertQuery.append("Delete  from Mkt_Accom_Activity " + " WHERE Occupancy_DT='" + startDate.plusDays(day).toString() + "' and Mkt_Seg_ID=" + mktSeg3 + " ");
        insertQuery.append("Delete  from Mkt_Accom_Activity " + " WHERE Occupancy_DT='" + startDate.plusDays(day).toString() + "' and Mkt_Seg_ID=" + mktSeg4 + " ");
        insertQuery.append(" UPDATE [Mkt_Accom_Activity] SET [Rooms_Sold] = 11,[Arrivals] = 7,[Departures] = 2,[Cancellations] = 1, ");
        insertQuery.append(" [No_Shows] = 1,[Room_Revenue] = 653.14500,[Food_Revenue] = 21.98720,[Total_Revenue] = 633.76512 ");
        insertQuery.append(" WHERE Occupancy_DT='" + startDate.plusDays(day).toString() + "' and Mkt_Seg_ID=" + mktSeg1 + " ");

        insertQuery.append(" UPDATE [Mkt_Accom_Activity] SET [Rooms_Sold] = 13,[Arrivals] = 9,[Departures] = 3,[Cancellations] = 2, ");
        insertQuery.append(" [No_Shows] = 1,[Room_Revenue] = 655.12500,[Food_Revenue] = 21.98720,[Total_Revenue] = 643.76512 ");
        insertQuery.append(" WHERE Occupancy_DT='" + startDate.plusDays(day).toString() + "' and Mkt_Seg_ID=" + mktSeg2 + " ");
    }

    private void updateMktActivityData_Group(StringBuilder insertQuery, int day) {
        insertQuery.append("Delete  from Mkt_Accom_Activity " + " WHERE Occupancy_DT='" + startDate.plusDays(day).toString() + "' and Mkt_Seg_ID=" + mktSeg1 + " ");
        insertQuery.append("Delete  from Mkt_Accom_Activity " + " WHERE Occupancy_DT='" + startDate.plusDays(day).toString() + "' and Mkt_Seg_ID=" + mktSeg2 + " ");
        insertQuery.append(" UPDATE [Mkt_Accom_Activity] SET [Rooms_Sold] = 15,[Arrivals] = 11,[Departures] = 4,[Cancellations] = 3, ");
        insertQuery.append(" [No_Shows] = 1,[Room_Revenue] = 677.67500,[Food_Revenue] = 23.98720,[Total_Revenue] = 653.76512 ");
        insertQuery.append(" WHERE Occupancy_DT='" + startDate.plusDays(day).toString() + "' and Mkt_Seg_ID=" + mktSeg3 + " ");

        insertQuery.append(" UPDATE [Mkt_Accom_Activity] SET [Rooms_Sold] = 17,[Arrivals] = 13,[Departures] = 5,[Cancellations] = 4, ");
        insertQuery.append(" [No_Shows] = 1,[Room_Revenue] = 689.13700,[Food_Revenue] = 25.98720,[Total_Revenue] = 663.76512 ");
        insertQuery.append(" WHERE Occupancy_DT='" + startDate.plusDays(day).toString() + "' and Mkt_Seg_ID=" + mktSeg4 + " ");
    }


    private void UpdateOccupancyForecastForGivenOccupancyDate(StringBuilder insertQuery) {
        insertQuery.append(" update Occupancy_FCST set Occupancy_NBR=11.00,Revenue=189.00000  where Accom_Type_ID=9 and MKT_SEG_ID=7 and Occupancy_DT='" + startDate.plusDays(6).toString() + "'");
        insertQuery.append(" update Occupancy_FCST set Occupancy_NBR=11.00,Revenue=189.00000  where Accom_Type_ID=10 and MKT_SEG_ID=7 and Occupancy_DT='" + startDate.plusDays(6).toString() + "'");
        insertQuery.append(" update Occupancy_FCST set Occupancy_NBR=11.00,Revenue=189.00000  where Accom_Type_ID=11 and MKT_SEG_ID=7 and Occupancy_DT='" + startDate.plusDays(6).toString() + "'");
        insertQuery.append(" update Occupancy_FCST set Occupancy_NBR=12.00,Revenue=190.00000  where Accom_Type_ID=12 and MKT_SEG_ID=7 and Occupancy_DT='" + startDate.plusDays(6).toString() + "'");
        insertQuery.append(" update Occupancy_FCST set Occupancy_NBR=11.00,Revenue=189.00000  where Accom_Type_ID=9 and MKT_SEG_ID=8 and Occupancy_DT='" + startDate.plusDays(6).toString() + "'");
        insertQuery.append(" update Occupancy_FCST set Occupancy_NBR=11.00,Revenue=189.00000  where Accom_Type_ID=10 and MKT_SEG_ID=8 and Occupancy_DT='" + startDate.plusDays(6).toString() + "'");
        insertQuery.append(" update Occupancy_FCST set Occupancy_NBR=12.00,Revenue=190.00000  where Accom_Type_ID=11 and MKT_SEG_ID=8 and Occupancy_DT='" + startDate.plusDays(6).toString() + "'");
        insertQuery.append(" update Occupancy_FCST set Occupancy_NBR=12.00,Revenue=190.00000  where Accom_Type_ID=12 and MKT_SEG_ID=8 and Occupancy_DT='" + startDate.plusDays(6).toString() + "'");
        insertQuery.append(" update Occupancy_FCST set Occupancy_NBR=11.00,Revenue=189.00000  where Accom_Type_ID=9 and MKT_SEG_ID=9 and Occupancy_DT='" + startDate.plusDays(6).toString() + "'");
        insertQuery.append(" update Occupancy_FCST set Occupancy_NBR=12.00,Revenue=190.00000  where Accom_Type_ID=10 and MKT_SEG_ID=9 and Occupancy_DT='" + startDate.plusDays(6).toString() + "'");
        insertQuery.append(" update Occupancy_FCST set Occupancy_NBR=12.00,Revenue=190.00000  where Accom_Type_ID=11 and MKT_SEG_ID=9 and Occupancy_DT='" + startDate.plusDays(6).toString() + "'");
        insertQuery.append(" update Occupancy_FCST set Occupancy_NBR=12.00,Revenue=190.00000  where Accom_Type_ID=12 and MKT_SEG_ID=9 and Occupancy_DT='" + startDate.plusDays(6).toString() + "'");
        insertQuery.append(" update Occupancy_FCST set Occupancy_NBR=12.00,Revenue=190.00000  where Accom_Type_ID=9 and MKT_SEG_ID=10 and Occupancy_DT='" + startDate.plusDays(6).toString() + "'");
        insertQuery.append(" update Occupancy_FCST set Occupancy_NBR=12.00,Revenue=190.00000  where Accom_Type_ID=10 and MKT_SEG_ID=10 and Occupancy_DT='" + startDate.plusDays(6).toString() + "'");
        insertQuery.append(" update Occupancy_FCST set Occupancy_NBR=12.00,Revenue=190.00000  where Accom_Type_ID=11 and MKT_SEG_ID=10 and Occupancy_DT='" + startDate.plusDays(6).toString() + "'");
        insertQuery.append(" update Occupancy_FCST set Occupancy_NBR=13.00,Revenue=191.00000  where Accom_Type_ID=12 and MKT_SEG_ID=10 and Occupancy_DT='" + startDate.plusDays(6).toString() + "'");
    }

    private void UpdateOccupancyForecastForGivenOccupancyDate_Transient(StringBuilder insertQuery, int day) {
        insertQuery.append(" update Occupancy_FCST set Occupancy_NBR=11.00,Revenue=189.00000  where Accom_Type_ID=9 and MKT_SEG_ID=7 and Occupancy_DT='" + startDate.plusDays(day).toString() + "'");
        insertQuery.append(" update Occupancy_FCST set Occupancy_NBR=11.00,Revenue=189.00000  where Accom_Type_ID=10 and MKT_SEG_ID=7 and Occupancy_DT='" + startDate.plusDays(day).toString() + "'");
        insertQuery.append(" update Occupancy_FCST set Occupancy_NBR=11.00,Revenue=189.00000  where Accom_Type_ID=11 and MKT_SEG_ID=7 and Occupancy_DT='" + startDate.plusDays(day).toString() + "'");
        insertQuery.append(" update Occupancy_FCST set Occupancy_NBR=12.00,Revenue=190.00000  where Accom_Type_ID=12 and MKT_SEG_ID=7 and Occupancy_DT='" + startDate.plusDays(day).toString() + "'");
        insertQuery.append(" update Occupancy_FCST set Occupancy_NBR=11.00,Revenue=189.00000  where Accom_Type_ID=9 and MKT_SEG_ID=8 and Occupancy_DT='" + startDate.plusDays(day).toString() + "'");
        insertQuery.append(" update Occupancy_FCST set Occupancy_NBR=11.00,Revenue=189.00000  where Accom_Type_ID=10 and MKT_SEG_ID=8 and Occupancy_DT='" + startDate.plusDays(day).toString() + "'");
        insertQuery.append(" update Occupancy_FCST set Occupancy_NBR=12.00,Revenue=190.00000  where Accom_Type_ID=11 and MKT_SEG_ID=8 and Occupancy_DT='" + startDate.plusDays(day).toString() + "'");
        insertQuery.append(" update Occupancy_FCST set Occupancy_NBR=12.00,Revenue=190.00000  where Accom_Type_ID=12 and MKT_SEG_ID=8 and Occupancy_DT='" + startDate.plusDays(day).toString() + "'");
    }

    private void UpdateOccupancyForecastForGivenOccupancyDate_Group(StringBuilder insertQuery, int day) {
        insertQuery.append(" update Occupancy_FCST set Occupancy_NBR=11.00,Revenue=189.00000  where Accom_Type_ID=9 and MKT_SEG_ID=9 and Occupancy_DT='" + startDate.plusDays(day).toString() + "'");
        insertQuery.append(" update Occupancy_FCST set Occupancy_NBR=12.00,Revenue=190.00000  where Accom_Type_ID=10 and MKT_SEG_ID=9 and Occupancy_DT='" + startDate.plusDays(day).toString() + "'");
        insertQuery.append(" update Occupancy_FCST set Occupancy_NBR=12.00,Revenue=190.00000  where Accom_Type_ID=11 and MKT_SEG_ID=9 and Occupancy_DT='" + startDate.plusDays(day).toString() + "'");
        insertQuery.append(" update Occupancy_FCST set Occupancy_NBR=12.00,Revenue=190.00000  where Accom_Type_ID=12 and MKT_SEG_ID=9 and Occupancy_DT='" + startDate.plusDays(day).toString() + "'");
        insertQuery.append(" update Occupancy_FCST set Occupancy_NBR=12.00,Revenue=190.00000  where Accom_Type_ID=9 and MKT_SEG_ID=10 and Occupancy_DT='" + startDate.plusDays(day).toString() + "'");
        insertQuery.append(" update Occupancy_FCST set Occupancy_NBR=12.00,Revenue=190.00000  where Accom_Type_ID=10 and MKT_SEG_ID=10 and Occupancy_DT='" + startDate.plusDays(day).toString() + "'");
        insertQuery.append(" update Occupancy_FCST set Occupancy_NBR=12.00,Revenue=190.00000  where Accom_Type_ID=11 and MKT_SEG_ID=10 and Occupancy_DT='" + startDate.plusDays(day).toString() + "'");
        insertQuery.append(" update Occupancy_FCST set Occupancy_NBR=13.00,Revenue=191.00000  where Accom_Type_ID=12 and MKT_SEG_ID=10 and Occupancy_DT='" + startDate.plusDays(day).toString() + "'");
    }

    private void retrieveDayOfWeekForDateEqualToSystemDatePlusSix() {
        List dowList = tenantCrudService().findByNativeQuery("SELECT DATENAME(dw,'" + startDate.plusDays(6) + "') as theDayName");
        dow = dowList.get(0).toString();
    }

    private void retrieveDayOfWeekForDateEqualToSystemDatePlusSeven() {
        List dowList = tenantCrudService().findByNativeQuery("SELECT DATENAME(dw,'" + startDate.plusDays(7) + "') as theDayName");
        dow1 = dowList.get(0).toString();
    }

    private void retrieveDayOfWeekForDateEqualToSystemDatePlusEight() {
        List dowList = tenantCrudService().findByNativeQuery("SELECT DATENAME(dw,'" + startDate.plusDays(8) + "') as theDayName");
        dow2 = dowList.get(0).toString();
    }

    private int retrieveDecsionIDForBusinessDateEqualToSystemDateMinusEight() {
        List secondDecisionIdList = tenantCrudService().findByNativeQuery("select Decision_ID from Decision where Business_DT='" + startDate.minusDays(8).toString() + "' " +
                " and Decision_Type_ID=1");
        return Integer.valueOf(secondDecisionIdList.get(0) + "");
    }

    private int retrieveDecsionIDForBusinessDateEqualToSystemDateMinusEleven() {
        List firstDecisionIdList = tenantCrudService().findByNativeQuery("select Decision_ID from Decision where Business_DT='" + startDate.minusDays(11).toString() + "' " +
                " and Decision_Type_ID=1");
        return Integer.valueOf(firstDecisionIdList.get(0) + "");
    }

    private void populateSpecialEventData(StringBuilder insertQuery) {
        java.time.LocalDate startDate = getJavaLocalDate();
        insertQuery.append(" INSERT INTO [Property_Special_Event]");
        insertQuery.append(" ([Property_ID],[Template_Default],[Impact_On_Forecast],[Start_DTTM]");
        insertQuery.append(" ,[End_DTTM],[Repeatable],[Event_Frequency_ID],[Last_Updated_DTTM],[Status_ID]");
        insertQuery.append(" ,[Special_Event_Name],[Special_Event_Description],[Special_Event_Type_ID]");
        insertQuery.append(" ,[Delete_ID],[Created_By_User_ID],[Created_DTTM],[Last_Updated_By_User_ID])");
        insertQuery.append(" VALUES (6,1,1,'" + startDate.plusDays(6).toString() + "','" + startDate.plusDays(6).toString() + "',0,1,GETDATE(),1,'SP_PickUPReport','',1,0,1,GETDATE(),1)");

        insertQuery.append("INSERT INTO [Property_Special_Event_Instance]");
        insertQuery.append("([Property_Special_Event_ID],[Start_DTTM],[End_DTTM]");
        insertQuery.append(",[Pre_Event_Days],[Post_Event_Days],[Enable_Forecast]");
        insertQuery.append(",[Repeatable],[Event_Frequency_ID],[Status_ID]");
        insertQuery.append(",[Last_Updated_DTTM],[Created_By_User_ID]");
        insertQuery.append(",[Created_DTTM],[Last_Updated_By_User_ID])");
        insertQuery.append(" VALUES ((select MAX(Property_Special_Event_ID) max_Id from Property_Special_Event),'" + startDate.plusDays(6).toString() + "','" + startDate.plusDays(6).toString() + "',0,0,1,0,1,1,GETDATE(),1,GETDATE(),1)");

        insertQuery.append(" INSERT INTO [Property_Special_Event]");
        insertQuery.append(" ([Property_ID],[Template_Default],[Impact_On_Forecast],[Start_DTTM]");
        insertQuery.append(" ,[End_DTTM],[Repeatable],[Event_Frequency_ID],[Last_Updated_DTTM],[Status_ID]");
        insertQuery.append(" ,[Special_Event_Name],[Special_Event_Description],[Special_Event_Type_ID]");
        insertQuery.append(" ,[Delete_ID],[Created_By_User_ID],[Created_DTTM],[Last_Updated_By_User_ID])");
        insertQuery.append(" VALUES (6,1,1,'" + startDate.toString() + "','" + startDate.plusDays(3).toString() + "',0,1,GETDATE(),1,'SP_PickUPReport1','',1,0,1,GETDATE(),1)");

        insertQuery.append("INSERT INTO [Property_Special_Event_Instance]");
        insertQuery.append("([Property_Special_Event_ID],[Start_DTTM],[End_DTTM]");
        insertQuery.append(",[Pre_Event_Days],[Post_Event_Days],[Enable_Forecast]");
        insertQuery.append(",[Repeatable],[Event_Frequency_ID],[Status_ID]");
        insertQuery.append(",[Last_Updated_DTTM],[Created_By_User_ID]");
        insertQuery.append(",[Created_DTTM],[Last_Updated_By_User_ID])");
        insertQuery.append(" VALUES ((select MAX(Property_Special_Event_ID) max_Id from Property_Special_Event),'" + startDate.toString() + "','" + startDate.plusDays(3).toString() + "',0,0,1,0,1,1,GETDATE(),1,GETDATE(),1)");

        insertQuery.append(" INSERT INTO [Property_Special_Event]");
        insertQuery.append(" ([Property_ID],[Template_Default],[Impact_On_Forecast],[Start_DTTM]");
        insertQuery.append(" ,[End_DTTM],[Repeatable],[Event_Frequency_ID],[Last_Updated_DTTM],[Status_ID]");
        insertQuery.append(" ,[Special_Event_Name],[Special_Event_Description],[Special_Event_Type_ID]");
        insertQuery.append(" ,[Delete_ID],[Created_By_User_ID],[Created_DTTM],[Last_Updated_By_User_ID],[Impact_On_FS_Forecast])");
        insertQuery.append(" VALUES (6,1,3,'" + startDate.plusDays(6).toString() + "','" + startDate.plusDays(6).toString() + "',0,1,GETDATE(),1,'PickUp Change Report','',1,0,1,GETDATE(),0,0)");

        insertQuery.append("INSERT INTO [Property_Special_Event_Instance]");
        insertQuery.append("([Property_Special_Event_ID],[Start_DTTM],[End_DTTM]");
        insertQuery.append(",[Pre_Event_Days],[Post_Event_Days],[Enable_Forecast]");
        insertQuery.append(",[Repeatable],[Event_Frequency_ID],[Status_ID]");
        insertQuery.append(",[Last_Updated_DTTM],[Created_By_User_ID]");
        insertQuery.append(",[Created_DTTM],[Last_Updated_By_User_ID],[Special_Event_Instance_Name])");
        insertQuery.append(" VALUES ((select MAX(Property_Special_Event_ID) max_Id from Property_Special_Event),'" + startDate.plusDays(6).toString() + "','" + startDate.plusDays(6).toString() + "',0,0,0,0,1,1,GETDATE(),1,GETDATE(),1,'Test Instance')");
    }

    private void populateActivityPaceForMarketSegment(StringBuilder insertQuery) {
        insertQuery.append(" INSERT INTO [PACE_Mkt_Activity]");
        insertQuery.append(" ([Property_ID],[Occupancy_DT],[SnapShot_DTTM],[Business_Day_End_DT],[Mkt_Seg_ID]");
        insertQuery.append(" ,[Rooms_Sold],[Arrivals],[Departures],[Cancellations],[No_Shows],[Room_Revenue]");
        insertQuery.append(" ,[Food_Revenue],[Total_Revenue],[File_Metadata_ID],[Month_ID],[Year_ID],[Last_Updated_DTTM])");
        insertQuery.append(" VALUES (6,'" + startDate.plusDays(6).toString() + "',GETDATE()");
        insertQuery.append(" ,'" + startDate.minusDays(11).toString() + "'," + mktSeg1 + ",25,21,18,2");
        insertQuery.append(" ,1,1125.13567,225.76343");
        insertQuery.append(" ,1125.24563,1,1,1,GETDATE())");
        insertQuery.append(" INSERT INTO [PACE_Mkt_Activity]");
        insertQuery.append(" ([Property_ID],[Occupancy_DT],[SnapShot_DTTM],[Business_Day_End_DT],[Mkt_Seg_ID]");
        insertQuery.append(" ,[Rooms_Sold],[Arrivals],[Departures],[Cancellations],[No_Shows],[Room_Revenue]");
        insertQuery.append(" ,[Food_Revenue],[Total_Revenue],[File_Metadata_ID],[Month_ID],[Year_ID],[Last_Updated_DTTM])");
        insertQuery.append(" VALUES (6,'" + startDate.plusDays(6).toString() + "',GETDATE()");
        insertQuery.append(" ,'" + startDate.minusDays(8).toString() + "'," + mktSeg2 + ",35,12,16,1");
        insertQuery.append(" ,3,3125.43567,135.76343");
        insertQuery.append(" ,2145.74663,1,1,1,GETDATE())");

        insertQuery.append(" INSERT INTO [PACE_Mkt_Activity]");
        insertQuery.append(" ([Property_ID],[Occupancy_DT],[SnapShot_DTTM],[Business_Day_End_DT],[Mkt_Seg_ID]");
        insertQuery.append(" ,[Rooms_Sold],[Arrivals],[Departures],[Cancellations],[No_Shows],[Room_Revenue]");
        insertQuery.append(" ,[Food_Revenue],[Total_Revenue],[File_Metadata_ID],[Month_ID],[Year_ID],[Last_Updated_DTTM])");
        insertQuery.append(" VALUES (6,'" + startDate.plusDays(6).toString() + "',GETDATE()");
        insertQuery.append(" ,'" + startDate.minusDays(11).toString() + "'," + mktSeg3 + ",25,21,18,2");
        insertQuery.append(" ,1,1125.13567,225.76343");
        insertQuery.append(" ,1125.24563,1,1,1,GETDATE())");
        insertQuery.append(" INSERT INTO [PACE_Mkt_Activity]");
        insertQuery.append(" ([Property_ID],[Occupancy_DT],[SnapShot_DTTM],[Business_Day_End_DT],[Mkt_Seg_ID]");
        insertQuery.append(" ,[Rooms_Sold],[Arrivals],[Departures],[Cancellations],[No_Shows],[Room_Revenue]");
        insertQuery.append(" ,[Food_Revenue],[Total_Revenue],[File_Metadata_ID],[Month_ID],[Year_ID],[Last_Updated_DTTM])");
        insertQuery.append(" VALUES (6,'" + startDate.plusDays(6).toString() + "',GETDATE()");
        insertQuery.append(" ,'" + startDate.minusDays(8).toString() + "'," + mktSeg4 + ",39,13,13,4");
        insertQuery.append(" ,5,3756.47867,201.76343");
        insertQuery.append(" ,2678.84763,1,1,1,GETDATE())");
    }

    private void populateActivityPaceForMarketSegment_Transient(StringBuilder insertQuery, int day) {
        insertQuery.append(" INSERT INTO [PACE_Mkt_Activity]");
        insertQuery.append(" ([Property_ID],[Occupancy_DT],[SnapShot_DTTM],[Business_Day_End_DT],[Mkt_Seg_ID]");
        insertQuery.append(" ,[Rooms_Sold],[Arrivals],[Departures],[Cancellations],[No_Shows],[Room_Revenue]");
        insertQuery.append(" ,[Food_Revenue],[Total_Revenue],[File_Metadata_ID],[Month_ID],[Year_ID],[Last_Updated_DTTM])");
        insertQuery.append(" VALUES (6,'" + startDate.plusDays(day).toString() + "',GETDATE()");
        insertQuery.append(" ,'" + startDate.minusDays(11).toString() + "'," + mktSeg1 + ",25,21,18,2");
        insertQuery.append(" ,1,1125.13567,225.76343");
        insertQuery.append(" ,1125.24563,1,1,1,GETDATE())");
        insertQuery.append(" INSERT INTO [PACE_Mkt_Activity]");
        insertQuery.append(" ([Property_ID],[Occupancy_DT],[SnapShot_DTTM],[Business_Day_End_DT],[Mkt_Seg_ID]");
        insertQuery.append(" ,[Rooms_Sold],[Arrivals],[Departures],[Cancellations],[No_Shows],[Room_Revenue]");
        insertQuery.append(" ,[Food_Revenue],[Total_Revenue],[File_Metadata_ID],[Month_ID],[Year_ID],[Last_Updated_DTTM])");
        insertQuery.append(" VALUES (6,'" + startDate.plusDays(day).toString() + "',GETDATE()");
        insertQuery.append(" ,'" + startDate.minusDays(8).toString() + "'," + mktSeg2 + ",35,12,16,1");
        insertQuery.append(" ,3,3125.43567,135.76343");
        insertQuery.append(" ,2145.74663,1,1,1,GETDATE())");
    }

    private void populateActivityPaceForMarketSegment_Group(StringBuilder insertQuery, int day) {
        insertQuery.append(" INSERT INTO [PACE_Mkt_Activity]");
        insertQuery.append(" ([Property_ID],[Occupancy_DT],[SnapShot_DTTM],[Business_Day_End_DT],[Mkt_Seg_ID]");
        insertQuery.append(" ,[Rooms_Sold],[Arrivals],[Departures],[Cancellations],[No_Shows],[Room_Revenue]");
        insertQuery.append(" ,[Food_Revenue],[Total_Revenue],[File_Metadata_ID],[Month_ID],[Year_ID],[Last_Updated_DTTM])");
        insertQuery.append(" VALUES (6,'" + startDate.plusDays(day).toString() + "',GETDATE()");
        insertQuery.append(" ,'" + startDate.minusDays(11).toString() + "'," + mktSeg3 + ",25,21,18,2");
        insertQuery.append(" ,1,1125.13567,225.76343");
        insertQuery.append(" ,1125.24563,1,1,1,GETDATE())");
        insertQuery.append(" INSERT INTO [PACE_Mkt_Activity]");
        insertQuery.append(" ([Property_ID],[Occupancy_DT],[SnapShot_DTTM],[Business_Day_End_DT],[Mkt_Seg_ID]");
        insertQuery.append(" ,[Rooms_Sold],[Arrivals],[Departures],[Cancellations],[No_Shows],[Room_Revenue]");
        insertQuery.append(" ,[Food_Revenue],[Total_Revenue],[File_Metadata_ID],[Month_ID],[Year_ID],[Last_Updated_DTTM])");
        insertQuery.append(" VALUES (6,'" + startDate.plusDays(day).toString() + "',GETDATE()");
        insertQuery.append(" ,'" + startDate.minusDays(8).toString() + "'," + mktSeg4 + ",39,13,13,4");
        insertQuery.append(" ,5,3756.47867,201.76343");
        insertQuery.append(" ,2678.84763,1,1,1,GETDATE())");
    }

    private void populateForecastPaceForMarketSegmentForSecondPacePoint(StringBuilder insertQuery, int secondDecisionId) {
        insertQuery.append(" INSERT INTO [PACE_Mkt_Occupancy_FCST]([Decision_ID],[Property_ID],[MKT_SEG_ID]");
        insertQuery.append(" ,[Occupancy_DT],[Occupancy_NBR],[Revenue],[CreateDate_DTTM])");
        insertQuery.append(" VALUES (" + secondDecisionId + ",6,7,'" + startDate.plusDays(6).toString() + "',2.52,17.10556,GETDATE()),");
        insertQuery.append(" (" + secondDecisionId + ",6,8,'" + startDate.plusDays(6).toString() + "',3.35,27.30656,GETDATE()),");
        insertQuery.append(" (" + secondDecisionId + ",6,9,'" + startDate.plusDays(6).toString() + "',4.56,37.70456,GETDATE()),");
        insertQuery.append(" (" + secondDecisionId + ",6,10,'" + startDate.plusDays(6).toString() + "',5.45,17.40956,GETDATE())");
    }

    private void populateForecastPaceForMarketSegmentForFirstPacePoint(StringBuilder insertQuery, int firstDecisionId) {
        insertQuery.append(" INSERT INTO [PACE_Mkt_Occupancy_FCST]([Decision_ID],[Property_ID],[MKT_SEG_ID]");
        insertQuery.append(" ,[Occupancy_DT],[Occupancy_NBR],[Revenue],[CreateDate_DTTM])");
        insertQuery.append(" VALUES (" + firstDecisionId + ",6,7,'" + startDate.plusDays(6).toString() + "',1.25,47.10456,GETDATE()),");
        insertQuery.append(" (" + firstDecisionId + ",6,8,'" + startDate.plusDays(6).toString() + "',2.25,57.10456,GETDATE()),");
        insertQuery.append(" (" + firstDecisionId + ",6,9,'" + startDate.plusDays(6).toString() + "',3.25,67.10456,GETDATE()),");
        insertQuery.append(" (" + firstDecisionId + ",6,10,'" + startDate.plusDays(6).toString() + "',4.25,77.10456,GETDATE())");
    }

    private void populateForecastPaceForMarketSegmentFor_Transient(StringBuilder insertQuery, int firstDecisionId, int secondDecisionId, int day) {
        insertQuery.append(" INSERT INTO [PACE_Mkt_Occupancy_FCST]([Decision_ID],[Property_ID],[MKT_SEG_ID]");
        insertQuery.append(" ,[Occupancy_DT],[Occupancy_NBR],[Revenue],[CreateDate_DTTM]) VALUES ");
        insertQuery.append(" (" + secondDecisionId + ",6,7,'" + startDate.plusDays(day).toString() + "',2.52,17.10556,GETDATE()),");
        insertQuery.append(" (" + secondDecisionId + ",6,8,'" + startDate.plusDays(day).toString() + "',3.35,27.30656,GETDATE()),");
        insertQuery.append(" (" + firstDecisionId + ",6,7,'" + startDate.plusDays(day).toString() + "',1.25,47.10456,GETDATE()),");
        insertQuery.append(" (" + firstDecisionId + ",6,8,'" + startDate.plusDays(day).toString() + "',2.25,57.10456,GETDATE())");
    }

    private void populateForecastPaceForMarketSegmentFor_Group(StringBuilder insertQuery, int firstDecisionId, int secondDecisionId, int day) {
        insertQuery.append(" INSERT INTO [PACE_Mkt_Occupancy_FCST]([Decision_ID],[Property_ID],[MKT_SEG_ID]");
        insertQuery.append(" ,[Occupancy_DT],[Occupancy_NBR],[Revenue],[CreateDate_DTTM]) VALUES ");
        insertQuery.append(" (" + firstDecisionId + ",6,9,'" + startDate.plusDays(day).toString() + "',3.25,67.10456,GETDATE()),");
        insertQuery.append(" (" + firstDecisionId + ",6,10,'" + startDate.plusDays(day).toString() + "',4.25,77.10456,GETDATE()),");
        insertQuery.append(" (" + secondDecisionId + ",6,9,'" + startDate.plusDays(day).toString() + "',4.56,37.70456,GETDATE()),");
        insertQuery.append(" (" + secondDecisionId + ",6,10,'" + startDate.plusDays(day).toString() + "',5.45,17.40956,GETDATE())");
    }

    private String getCaughtUpDate(final String propertyId) {
        List caughtUpDates = tenantCrudService().findByNativeQuery("select  dbo.ufn_get_caughtup_date_by_property(" + propertyId + ",3,13)");
        return caughtUpDates.get(0).toString();
    }


    private LocalDate getLocalDate(final String propertyId) {
        return LocalDate.parse(getCaughtUpDate(propertyId));
    }

    private Date getJavaLocalDate(final String propertyId) {
        return DateUtil.toDate(getCaughtUpDate(propertyId));
    }

    private java.time.LocalDate getJavaLocalDate() {
        return java.time.LocalDate.parse(getCaughtUpDate(String.valueOf(propertyID)));
    }

    @Test
    public void shouldValidatePickUpAndChangeReportBusinessTypeLevelFunctionWithNonAggregateForStaticAndRollingDates() {
        //Setup
        createTestData();

        //Tests
        shouldValidatePickUpReportBusinessTypeLevelFunctionWithNonAggregateForStaticDate();
        shouldValidatePickUpReportBusinessTypeLevelFunctionWithNonAggregateForRollingDate();
        shouldValidateChangeReportBusinessTypeLevelFunctionWithNonAggregateForStaticDate();
        shouldValidateChangeReportBusinessTypeLevelFunctionWithNonAggregateForRollingDate();
    }

    private void shouldValidatePickUpReportBusinessTypeLevelFunctionWithNonAggregateForStaticDate() {
        isRolling = 0;
        List<Object[]> reportDataByStaticDates = tenantCrudService().findByNativeQuery("exec dbo.usp_pickup_report_comparative_view_bt " + propertyID + "," + recordTypeId + "," + processStatusId + ",'" + startDate.minusDays(11).toString() + "','" + startDate.minusDays(8).toString() + "','" + startDate.plusDays(6).toString() + "','" + startDate.plusDays(6).toString() + "'," + isRolling + ",'TODAY-4','TODAY-2','TODAY','TODAY'");
        assertPickUpReportDataAtBusinessTypeLevel("[shouldValidatePickUpReportBusinessTypeLevelFunctionWithNonAggregateForStaticDate] " + "Pickup Report at Business Type level with Static Dates", reportDataByStaticDates, "PickUp Change Report - Test Instance - Information Only, SP_PickUPReport");
    }

    @Test
    public void shouldValidatePickUpReportBusinessTypeLevelFunctionForPastAnalysisStartDate() {
        javaStartDate = getJavaLocalDate(TestProperty.H2.getId().toString());
        createTestData();
        isRolling = 0;
        List<Object[]> reportDataByStaticDates = tenantCrudService().findByNativeQuery("exec dbo.usp_pickup_report_comparative_view_bt " + propertyID + "," + recordTypeId + "," + processStatusId + ",'" + DateUtil.getDateAsString(javaStartDate, -100) + "','" + DateUtil.getDateAsString(javaStartDate, 6) + "','" + DateUtil.getDateAsString(javaStartDate, 6) + "','" + DateUtil.getDateAsString(javaStartDate, 6) + "'," + isRolling + ",'TODAY-4','TODAY-2','TODAY','TODAY'");
        assertNotEquals("0.00", String.format("%d", reportDataByStaticDates.get(0)[4]), "[shouldValidatePickUpReportBusinessTypeLevelFunctionForPastAnalysisStartDate] Pickup Report at Business Type level with Static Dates" + " - Transient Rooms Sold Pick Up ");
        assertNotEquals("0.00", String.format("%d", reportDataByStaticDates.get(0)[6]), "[shouldValidatePickUpReportBusinessTypeLevelFunctionForPastAnalysisStartDate] Pickup Report at Business Type level with Static Dates" + " - Group Rooms Sold Pick Up ");
    }

    @Test
    public void shouldValidatePickUpReportBusinessTypeLevelFunctionOnlyBusinessEndDateDataAvailable() {
        createTestData();
        isRolling = 0;
        List<Object[]> reportDataByStaticDates = tenantCrudService().findByNativeQuery("exec dbo.usp_pickup_report_comparative_view_bt " + propertyID + "," + recordTypeId + "," + processStatusId + ",'" + startDate.minusDays(12).toString() + "','" + startDate.minusDays(8).toString() + "','" + startDate.plusDays(6).toString() + "','" + startDate.plusDays(6).toString() + "'," + isRolling + ",'TODAY-4','TODAY-2','TODAY','TODAY'");
        assertPickUpReportDataAtBusinessTypeLevelOnlyBusinessEndDateDataAvailable("[shouldValidatePickUpReportBusinessTypeLevelFunctionWithNonAggregateForStaticDate] " + "Pickup Report at Business Type level with Static Dates", reportDataByStaticDates, "PickUp Change Report - Test Instance - Information Only, SP_PickUPReport");
    }

    @Test
    public void shouldValidatePickUpAndChangeReportBusinessTypeLevelFunctionWithNonAggregateForStaticAndRollingDatesFullJoin() {
        //Setup
        createTestData_FullJoin();
        //Tests
        shouldValidatePickUpReportBusinessTypeLevelFunctionWithNonAggregateForStaticDateFullJoin();
        shouldValidateChangeReportBusinessTypeLevelFunctionWithNonAggregateForStaticDateFullJoin();
    }

    private void shouldValidatePickUpReportBusinessTypeLevelFunctionWithNonAggregateForStaticDateFullJoin() {
        isRolling = 0;
        List<Object[]> reportDataByStaticDates = tenantCrudService().findByNativeQuery("exec dbo.usp_pickup_report_comparative_view_bt " + propertyID + "," + recordTypeId + "," + processStatusId + ",'" + startDate.minusDays(11).toString() + "','" + startDate.minusDays(8).toString() + "','" + startDate.plusDays(6).toString() + "','" + startDate.plusDays(8).toString() + "'," + isRolling + ",'TODAY-4','TODAY-2','TODAY','TODAY'");
        assertPickUpReportDataAtBusinessTypeLevelFullJoin("[shouldValidatePickUpReportBusinessTypeLevelFunctionWithNonAggregateForStaticDate] " + "Pickup Report at Business Type level with Static Dates", reportDataByStaticDates);
    }

    @Test
    @Tag("pickupAndChangeReportQueries-flaky")
    public void shouldValidatePickUpAndChangeReportGroupBusinessTypeLevelFunctionWhenActivityDateIsBeyondAnalysisDate() {
        setUpTables(BUSINESS_TYPE_GROUP);

        shouldValidatePickUpReportGroupBusinessTypeLevelFunctionForStaticDateWhenActivityDateIsBeyondAnalysisDate();
        shouldValidatePickUpReportGroupBusinessTypeLevelFunctionForRollingDateWhenActivityDateIsBeyondAnalysisDate();
        shouldValidateChangeGroupReportBusinessTypeLevelFunctionForStaticDateWhenActivityDateIsBeyondAnalysisDate();
        shouldValidateChangeGroupReportBusinessTypeLevelFunctionForRollingDateWhenActivityDateIsBeyondAnalysisDate();
    }

    private void shouldValidatePickUpReportGroupBusinessTypeLevelFunctionForStaticDateWhenActivityDateIsBeyondAnalysisDate() {
        isRolling = 0;
        List<Object[]> reportData = tenantCrudService().findByNativeQuery("select * from dbo.ufn_get_pickup_report_bt (" + propertyID + ",'group'," + recordTypeId + "," + processStatusId + ",'" + startDate.plusDays(1).toString() + "','" + startDate.plusDays(2).toString() + "','" + startDate.toString() + "','" + startDate.plusDays(3).toString() + "'," + isRolling + ",'TODAY-4','TODAY-2','TODAY','TODAY')");
        assertPickUpReportDataAtBusinessTypeLevelWhenActivityDateIsBeyondAnalysisDate("shouldValidatePickUpReportGroupBusinessTypeLevelFunctionForStaticDateWhenActivityDateIsBeyondAnalysisDate ", reportData);
    }

    private void shouldValidatePickUpReportGroupBusinessTypeLevelFunctionForRollingDateWhenActivityDateIsBeyondAnalysisDate() {
        isRolling = 1;
        List<Object[]> reportData = tenantCrudService().findByNativeQuery("select * from dbo.ufn_get_pickup_report_bt (" + propertyID + ",'group'," + recordTypeId + "," + processStatusId + ",'" + startDate.plusDays(1).toString() + "','" + startDate.plusDays(2).toString() + "','" + startDate.toString() + "','" + startDate.plusDays(3).toString() + "'," + isRolling + ",'TODAY+1','TODAY+2','TODAY','TODAY+3')");
        assertPickUpReportDataAtBusinessTypeLevelWhenActivityDateIsBeyondAnalysisDate("shouldValidatePickUpReportGroupBusinessTypeLevelFunctionForRollingDateWhenActivityDateIsBeyondAnalysisDate ", reportData);
    }

    private void setUpTables(String businessType) {
        int businessTypeId = getBusinessTypeIdFor(businessType);
        updateMarketSegmentData(businessTypeId);
        updateMarketAccomActivityData();
        updatePaceMarketActivityData();
        updateOccupancyForecastData();
        updateDecisionTable();
        updatePaceMarketOccupancyForecast();
    }

    @Test
    @Tag("pickupAndChangeReportQueries-flaky")
    public void shouldValidatePickUpAndChangeReportTransientBusinessTypeLevelFunctionWhenActivityDateIsBeyondAnalysisDate() {
        //Setup
        setUpTables(BUSINESS_TYPE_TRANSIENT);

        //Tests
        shouldValidatePickUpReportTransientBusinessTypeLevelFunctionForStaticDateWhenActivityDateIsBeyondAnalysisDate();
        shouldValidatePickUpReportTransientBusinessTypeLevelFunctionForRollingDateWhenActivityDateIsBeyondAnalysisDate();
        shouldValidateChangeTransientReportBusinessTypeLevelFunctionForStaticDateWhenActivityDateIsBeyondAnalysisDate();
        shouldValidateChangeTransientReportBusinessTypeLevelFunctionForRollingDateWhenActivityDateIsBeyondAnalysisDate();
    }

    private void shouldValidatePickUpReportTransientBusinessTypeLevelFunctionForStaticDateWhenActivityDateIsBeyondAnalysisDate() {
        isRolling = 0;
        List<Object[]> reportData = tenantCrudService().findByNativeQuery("select * from dbo.ufn_get_pickup_report_bt (" + propertyID + ",'transient'," + recordTypeId + "," + processStatusId + ",'" + startDate.plusDays(1).toString() + "','" + startDate.plusDays(2).toString() + "','" + startDate.toString() + "','" + startDate.plusDays(3).toString() + "'," + isRolling + ",'TODAY-4','TODAY-2','TODAY','TODAY')");
        assertPickUpReportDataAtBusinessTypeLevelWhenActivityDateIsBeyondAnalysisDate("shouldValidatePickUpReportTransientBusinessTypeLevelFunctionForStaticDateWhenActivityDateIsBeyondAnalysisDate ", reportData);
    }

    private void shouldValidatePickUpReportTransientBusinessTypeLevelFunctionForRollingDateWhenActivityDateIsBeyondAnalysisDate() {
        isRolling = 1;
        List<Object[]> reportData = tenantCrudService().findByNativeQuery("select * from dbo.ufn_get_pickup_report_bt (" + propertyID + ",'transient'," + recordTypeId + "," + processStatusId + ",'" + startDate.plusDays(1).toString() + "','" + startDate.plusDays(2).toString() + "','" + startDate.toString() + "','" + startDate.plusDays(3).toString() + "'," + isRolling + ",'TODAY+1','TODAY+2','TODAY','TODAY+3')");
        assertPickUpReportDataAtBusinessTypeLevelWhenActivityDateIsBeyondAnalysisDate("shouldValidatePickUpReportTransientBusinessTypeLevelFunctionForRollingDateWhenActivityDateIsBeyondAnalysisDate ", reportData);
    }

    private void assertPickUpReportDataAtBusinessTypeLevelWhenActivityDateIsBeyondAnalysisDate(String message, List<Object[]> reportData) {
        reportData.sort(new Comparator<Object[]>() {

            @Override
            public int compare(Object[] objects, Object[] t1) {
                return ((Date) objects[0]).compareTo((Date) t1[0]);
            }
        });
        assertTrue(reportData.size() == 4, message);
        assertTrue("0".equals(reportData.get(0)[4].toString()), message);
        assertTrue("-12".equals(reportData.get(1)[4].toString()), message);
        assertTrue("9".equals(reportData.get(2)[4].toString()), message);
        assertTrue("8".equals(reportData.get(3)[4].toString()), message);
        assertTrue("0.00000".equals(reportData.get(0)[6].toString()), message);
        assertTrue("163.69000".equals(reportData.get(1)[6].toString()), message);
        assertTrue("-3.35000".equals(reportData.get(2)[6].toString()), message);
        assertTrue("12.19000".equals(reportData.get(3)[6].toString()), message);
        assertTrue("0.00000".equals(reportData.get(0)[8].toString()), message);
        assertTrue("-470.01000".equals(reportData.get(1)[8].toString()), message);
        assertTrue("631.04000".equals(reportData.get(2)[8].toString()), message);
        assertTrue("574.96000".equals(reportData.get(3)[8].toString()), message);
        assertTrue("0.00000".equals(reportData.get(0)[10].toString()), message);
        assertTrue("5.38000".equals(reportData.get(1)[10].toString()), message);
        assertTrue("-7.86000".equals(reportData.get(2)[10].toString()), message);
        assertTrue("-5.16000".equals(reportData.get(3)[10].toString()), message);
        assertTrue("0.00000".equals(reportData.get(0)[12].toString()), message);
        assertTrue("9.65000".equals(reportData.get(1)[12].toString()), message);
        assertTrue("2.21000".equals(reportData.get(2)[12].toString()), message);
        assertTrue("3.60000".equals(reportData.get(3)[12].toString()), message);
        assertTrue("0.00000".equals(reportData.get(0)[14].toString()), message);
        assertTrue("6.54000".equals(reportData.get(1)[14].toString()), message);
        assertTrue("-3.19000".equals(reportData.get(2)[14].toString()), message);
        assertTrue("0.08000".equals(reportData.get(3)[14].toString()), message);
    }

    private void updateOccupancyForecastData() {
        tenantCrudService().executeUpdateByNativeQuery("truncate table Occupancy_FCST");
        tenantCrudService().flushAndClear();
        StringBuilder query = new StringBuilder();
        query.append(" INSERT INTO Occupancy_FCST");
        query.append(" VALUES (1,6," + mktSeg1 + ",9,'" + startDate.toString() + "',10,175,4,5,getDate(), 5)");
        query.append(" INSERT INTO Occupancy_FCST");
        query.append(" VALUES (1,6," + mktSeg1 + ",9,'" + startDate.plusDays(1).toString() + "',13,191,4,5,getDate(), 5)");
        query.append(" INSERT INTO Occupancy_FCST");
        query.append(" VALUES (1,6," + mktSeg1 + ",9,'" + startDate.plusDays(2).toString() + "',12,189,4,5,getDate(), 5)");
        query.append(" INSERT INTO Occupancy_FCST");
        query.append(" VALUES (1,6," + mktSeg1 + ",9,'" + startDate.plusDays(3).toString() + "',11,190,4,5,getDate(), 5)");
        tenantCrudService().executeUpdateByNativeQuery(query.toString());
    }

    private void updatePaceMarketOccupancyForecast() {
        int decisionId = Integer.valueOf(tenantCrudService().findByNativeQuery("select decision_id from Decision where Business_DT = '" + startDate.plusDays(1) + "'").get(0) + "");
        StringBuilder query = new StringBuilder();
        query.append(" INSERT INTO [PACE_Mkt_Occupancy_FCST]([Decision_ID],[Property_ID],[MKT_SEG_ID]");
        query.append(" ,[Occupancy_DT],[Occupancy_NBR],[Revenue],[CreateDate_DTTM])");
        query.append(" VALUES (" + decisionId + ",6,7,'" + startDate.plusDays(1).toString() + "',3.35,27.30656,GETDATE()),");
        query.append(" (" + decisionId + ",6,7,'" + startDate.plusDays(2).toString() + "',4.56,37.70456,GETDATE()),");
        query.append(" (" + decisionId + ",6,7,'" + startDate.plusDays(3).toString() + "',5.45,17.40956,GETDATE())");

        decisionId = Integer.valueOf(tenantCrudService().findByNativeQuery("select decision_id from Decision where Business_DT = '" + startDate.plusDays(2) + "'").get(0) + "");
        query.append(" INSERT INTO [PACE_Mkt_Occupancy_FCST]([Decision_ID],[Property_ID],[MKT_SEG_ID]");
        query.append(" ,[Occupancy_DT],[Occupancy_NBR],[Revenue],[CreateDate_DTTM])");
        query.append(" VALUES (" + decisionId + ",6,7,'" + startDate.plusDays(2).toString() + "',6.77,34.3528,GETDATE()),");
        query.append(" (" + decisionId + ",6,7,'" + startDate.plusDays(3).toString() + "',9.05,29.5956,GETDATE())");

        tenantCrudService().executeUpdateByNativeQuery(query.toString());
    }

    private void updateDecisionTable() {
        List decisionIdList = tenantCrudService().findByNativeQuery("select Decision_ID from Decision where Business_DT='" + startDate.plusDays(1).toString() + "' " +
                " and Decision_Type_ID=1");
        StringBuilder query = new StringBuilder();
        if (null == decisionIdList || decisionIdList.isEmpty()) {
            query.append("insert into decision values(" + propertyID + ",'" + startDate.plusDays(1) + "','" + new LocalDate() + "'," +
                    "'" + new LocalDate() + "','" + new LocalDate() + "',1,'" + new LocalDate() + "','" + new LocalDate() + "',2,'" + new LocalDate() + "')");
        }
        decisionIdList = tenantCrudService().findByNativeQuery("select Decision_ID from Decision where Business_DT='" + startDate.plusDays(2).toString() + "' " +
                " and Decision_Type_ID=1");
        if (null == decisionIdList || decisionIdList.isEmpty()) {
            query.append("insert into decision values(" + propertyID + ",'" + startDate.plusDays(2) + "','" + new LocalDate() + "'," +
                    "'" + new LocalDate() + "','" + new LocalDate() + "',1,'" + new LocalDate() + "','" + new LocalDate() + "',2,'" + new LocalDate() + "')");
        }
        if (!"".equals(query.toString())) {
            tenantCrudService().executeUpdateByNativeQuery(query.toString());
        }
    }

    private void updatePaceMarketActivityData() {
        tenantCrudService().executeUpdateByNativeQuery("truncate table pace_mkt_activity");
        tenantCrudService().flushAndClear();
        StringBuilder query = new StringBuilder();
        query.append(" INSERT INTO [PACE_Mkt_Activity]");
        query.append(" ([Property_ID],[Occupancy_DT],[SnapShot_DTTM],[Business_Day_End_DT],[Mkt_Seg_ID]");
        query.append(" ,[Rooms_Sold],[Arrivals],[Departures],[Cancellations],[No_Shows],[Room_Revenue]");
        query.append(" ,[Food_Revenue],[Total_Revenue],[File_Metadata_ID],[Month_ID],[Year_ID],[Last_Updated_DTTM])");
        query.append(" VALUES (6,'" + startDate.plusDays(1).toString() + "',GETDATE()");
        query.append(" ,'" + startDate.plusDays(1).toString() + "'," + mktSeg1 + ",25,21,18,2");
        query.append(" ,1,1125.136,225.76343");
        query.append(" ,1125.24563,1,1,1,GETDATE())");
        query.append(" INSERT INTO [PACE_Mkt_Activity]");
        query.append(" ([Property_ID],[Occupancy_DT],[SnapShot_DTTM],[Business_Day_End_DT],[Mkt_Seg_ID]");
        query.append(" ,[Rooms_Sold],[Arrivals],[Departures],[Cancellations],[No_Shows],[Room_Revenue]");
        query.append(" ,[Food_Revenue],[Total_Revenue],[File_Metadata_ID],[Month_ID],[Year_ID],[Last_Updated_DTTM])");
        query.append(" VALUES (6,'" + startDate.plusDays(2).toString() + "',GETDATE()");
        query.append(" ,'" + startDate.plusDays(1).toString() + "'," + mktSeg1 + ",30,12,16,1");
        query.append(" ,3,3125.436,135.76343");
        query.append(" ,2145.74663,1,1,1,GETDATE())");
        query.append(" INSERT INTO [PACE_Mkt_Activity]");
        query.append(" ([Property_ID],[Occupancy_DT],[SnapShot_DTTM],[Business_Day_End_DT],[Mkt_Seg_ID]");
        query.append(" ,[Rooms_Sold],[Arrivals],[Departures],[Cancellations],[No_Shows],[Room_Revenue]");
        query.append(" ,[Food_Revenue],[Total_Revenue],[File_Metadata_ID],[Month_ID],[Year_ID],[Last_Updated_DTTM])");
        query.append(" VALUES (6,'" + startDate.plusDays(3).toString() + "',GETDATE()");
        query.append(" ,'" + startDate.plusDays(1).toString() + "'," + mktSeg1 + ",32,12,16,1");
        query.append(" ,3,3125.436,135.76343");
        query.append(" ,2145.74663,1,1,1,GETDATE())");

        query.append(" INSERT INTO [PACE_Mkt_Activity]");
        query.append(" ([Property_ID],[Occupancy_DT],[SnapShot_DTTM],[Business_Day_End_DT],[Mkt_Seg_ID]");
        query.append(" ,[Rooms_Sold],[Arrivals],[Departures],[Cancellations],[No_Shows],[Room_Revenue]");
        query.append(" ,[Food_Revenue],[Total_Revenue],[File_Metadata_ID],[Month_ID],[Year_ID],[Last_Updated_DTTM])");
        query.append(" VALUES (6,'" + startDate.plusDays(2).toString() + "',GETDATE()");
        query.append(" ,'" + startDate.plusDays(2).toString() + "'," + mktSeg1 + ",39,13,13,4");
        query.append(" ,5,3756.478,201.76343");
        query.append(" ,2678.84763,1,1,1,GETDATE())");
        query.append(" INSERT INTO [PACE_Mkt_Activity]");
        query.append(" ([Property_ID],[Occupancy_DT],[SnapShot_DTTM],[Business_Day_End_DT],[Mkt_Seg_ID]");
        query.append(" ,[Rooms_Sold],[Arrivals],[Departures],[Cancellations],[No_Shows],[Room_Revenue]");
        query.append(" ,[Food_Revenue],[Total_Revenue],[File_Metadata_ID],[Month_ID],[Year_ID],[Last_Updated_DTTM])");
        query.append(" VALUES (6,'" + startDate.plusDays(3).toString() + "',GETDATE()");
        query.append(" ,'" + startDate.plusDays(2).toString() + "'," + mktSeg1 + ",40,13,13,4");
        query.append(" ,5,3700.4,201.76343");
        query.append(" ,2678.84763,1,1,1,GETDATE())");
        tenantCrudService().executeUpdateByNativeQuery(query.toString());
    }

    private void updateMarketAccomActivityData() {
        tenantCrudService().executeUpdateByNativeQuery("truncate table Mkt_Accom_Activity");
        tenantCrudService().flushAndClear();
        StringBuilder query = new StringBuilder();
        query.append(" insert into [Mkt_Accom_Activity] values(" + propertyID + ",'" + startDate + "','" + new LocalDate() + "'," + mktSeg1 + "," +
                "9,11,7,2,0,0,653.145,21.9870,633.76512,1,'" + new LocalDate() + "','" + new LocalDate() + "',0, 0, 0)");
        query.append(" insert into [Mkt_Accom_Activity] values(" + propertyID + ",'" + startDate.plusDays(1) + "','" + new LocalDate() + "'," + mktSeg1 + "," +
                "9,13,9,3,0,0,655.125,22.98720,643.76512,1,'" + new LocalDate() + "','" + new LocalDate() + "',0, 0, 0)");
        query.append(" insert into [Mkt_Accom_Activity] values(" + propertyID + ",'" + startDate.plusDays(2) + "','" + new LocalDate() + "'," + mktSeg1 + "," +
                "9,15,11,4,0,0,677.675,23.9870,653.76512,1,'" + new LocalDate() + "','" + new LocalDate() + "',0, 0, 0)");
        query.append(" insert into [Mkt_Accom_Activity] values(" + propertyID + ",'" + startDate.plusDays(3) + "','" + new LocalDate() + "'," + mktSeg1 + "," +
                "9,17,13,5,0,0,689.137,24.9870,663.76512,1,'" + new LocalDate() + "','" + new LocalDate() + "',0, 0, 0)");

        tenantCrudService().executeUpdateByNativeQuery(query.toString());
    }

    private int getBusinessTypeIdFor(String businessTypeName) {
        return (int) tenantCrudService().findByNativeQuery("select Business_Type_ID from Business_Type where Business_Type_Name = '" + businessTypeName + "'").get(0);
    }

    private void updateMarketSegmentData(int businessTypeId) {
        String query = " update Mkt_Seg_Details set Business_Type_ID=" + businessTypeId + " where Mkt_Seg_ID in (" + mktSeg1 + ") ";
        tenantCrudService().executeUpdateByNativeQuery(query);
    }

    private void shouldValidatePickUpReportBusinessTypeLevelFunctionWithNonAggregateForRollingDate() {
        isRolling = 1;
        List<Object[]> reportDataByRollingDates = tenantCrudService().findByNativeQuery("exec dbo.usp_pickup_report_comparative_view_bt " + propertyID + "," + recordTypeId + "," + processStatusId + ",'" + startDate.minusDays(11).toString() + "','" + startDate.minusDays(8).toString() + "','" + startDate.plusDays(6).toString() + "','" + startDate.plusDays(6).toString() + "'," + isRolling + ",'TODAY-11','TODAY-8','TODAY+6','TODAY+6'");
        assertPickUpReportDataAtBusinessTypeLevel("[shouldValidatePickUpReportBusinessTypeLevelFunctionWithNonAggregateForRollingDate] " + "Pickup Report at Business Type level with Rolling Dates", reportDataByRollingDates, "PickUp Change Report - Test Instance - Information Only, SP_PickUPReport");
    }

    private void shouldValidateChangeReportBusinessTypeLevelFunctionWithNonAggregateForStaticDate() {
        isRolling = 0;
        List<Object[]> reportDataByStaticDates = tenantCrudService().findByNativeQuery("exec dbo.usp_change_report_comparative_view_bt " + propertyID + "," + recordTypeId + "," + processStatusId + ",'" + startDate.minusDays(11).toString() + "','" + startDate.plusDays(6).toString() + "','" + startDate.plusDays(6).toString() + "'," + isRolling + ",'TODAY-4','TODAY','TODAY'");
        assertChangeReportDataAtBusinessTypeLevel("[shouldValidateChangeReportBusinessTypeLevelFunctionWithNonAggregateForStaticDate] " + "Change Report at Business Type level with Static Dates", reportDataByStaticDates, "PickUp Change Report - Test Instance - Information Only, SP_PickUPReport");
    }

    private void shouldValidateChangeReportBusinessTypeLevelFunctionWithNonAggregateForStaticDateFullJoin() {
        isRolling = 0;
        List<Object[]> reportDataByStaticDates = tenantCrudService().findByNativeQuery("exec dbo.usp_change_report_comparative_view_bt " + propertyID + "," + recordTypeId + "," + processStatusId + ",'" + startDate.minusDays(11).toString() + "','" + startDate.plusDays(6).toString() + "','" + startDate.plusDays(8).toString() + "'," + isRolling + ",'TODAY-4','TODAY','TODAY'");
        assertChangeReportDataAtBusinessTypeLevelFullJoin("[shouldValidateChangeReportBusinessTypeLevelFunctionWithNonAggregateForStaticDate] " + "Change Report at Business Type level with Static Dates", reportDataByStaticDates);
    }

    private void shouldValidateChangeGroupReportBusinessTypeLevelFunctionForStaticDateWhenActivityDateIsBeyondAnalysisDate() {
        isRolling = 0;
        List<Object[]> reportData = tenantCrudService().findByNativeQuery("select * from dbo.ufn_get_change_report_bt (" + propertyID + ",'group'," + recordTypeId + "," + processStatusId + ",'" + startDate.plusDays(1).toString() + "','" + startDate.toString() + "','" + startDate.plusDays(3).toString() + "'," + isRolling + ",'TODAY-4','TODAY','TODAY')");
        assertChangeReportDataAtBusinessTypeLevelWhenActivityDateIsBeyondAnalysisDate("shouldValidateChangeGroupReportBusinessTypeLevelFunctionForStaticDateWhenActivityDateIsBeyondAnalysisDate", reportData);
    }

    private void shouldValidateChangeGroupReportBusinessTypeLevelFunctionForRollingDateWhenActivityDateIsBeyondAnalysisDate() {
        isRolling = 1;
        List<Object[]> reportData = tenantCrudService().findByNativeQuery("select * from dbo.ufn_get_change_report_bt (" + propertyID + ",'group'," + recordTypeId + "," + processStatusId + ",'" + startDate.plusDays(1).toString() + "','" + startDate.toString() + "','" + startDate.plusDays(3).toString() + "'," + isRolling + ",'TODAY+1','TODAY','TODAY+4')");
        assertChangeReportDataAtBusinessTypeLevelWhenActivityDateIsBeyondAnalysisDate("shouldValidateChangeGroupReportBusinessTypeLevelFunctionForRollingDateWhenActivityDateIsBeyondAnalysisDate", reportData);
    }

    private void shouldValidateChangeTransientReportBusinessTypeLevelFunctionForStaticDateWhenActivityDateIsBeyondAnalysisDate() {
        isRolling = 0;
        List<Object[]> reportData = tenantCrudService().findByNativeQuery("select * from dbo.ufn_get_change_report_bt (" + propertyID + ",'transient'," + recordTypeId + "," + processStatusId + ",'" + startDate.plusDays(1).toString() + "','" + startDate.toString() + "','" + startDate.plusDays(3).toString() + "'," + isRolling + ",'TODAY-4','TODAY','TODAY')");
        assertChangeReportDataAtBusinessTypeLevelWhenActivityDateIsBeyondAnalysisDate("shouldValidateChangeTransientReportBusinessTypeLevelFunctionForStaticDateWhenActivityDateIsBeyondAnalysisDate ", reportData);
    }

    private void shouldValidateChangeTransientReportBusinessTypeLevelFunctionForRollingDateWhenActivityDateIsBeyondAnalysisDate() {
        isRolling = 1;
        List<Object[]> reportData = tenantCrudService().findByNativeQuery("select * from dbo.ufn_get_change_report_bt (" + propertyID + ",'transient'," + recordTypeId + "," + processStatusId + ",'" + startDate.plusDays(1).toString() + "','" + startDate.toString() + "','" + startDate.plusDays(3).toString() + "'," + isRolling + ",'TODAY+1','TODAY','TODAY+4')");
        assertChangeReportDataAtBusinessTypeLevelWhenActivityDateIsBeyondAnalysisDate("shouldValidateChangeTransientReportBusinessTypeLevelFunctionForRollingDateWhenActivityDateIsBeyondAnalysisDate ", reportData);
    }

    private void assertChangeReportDataAtBusinessTypeLevelWhenActivityDateIsBeyondAnalysisDate(String message, List<Object[]> reportData) {
        reportData.sort(new Comparator<Object[]>() {

            @Override
            public int compare(Object[] objects, Object[] t1) {
                return ((Date) objects[0]).compareTo((Date) t1[0]);
            }
        });
        assertEquals(4, reportData.size(), message);
        assertTrue("0".equals(reportData.get(0)[4].toString()), message);
        assertTrue("-12".equals(reportData.get(1)[4].toString()), message);
        assertTrue("-15".equals(reportData.get(2)[4].toString()), message);
        assertTrue("-15".equals(reportData.get(3)[4].toString()), message);
        assertTrue("0.00000".equals(reportData.get(0)[6].toString()), message);
        assertTrue("9.65000".equals(reportData.get(1)[6].toString()), message);
        assertTrue("7.44000".equals(reportData.get(2)[6].toString()), message);
        assertTrue("5.55000".equals(reportData.get(3)[6].toString()), message);
        assertTrue("0.00000".equals(reportData.get(0)[8].toString()), message);
        assertTrue("-470.01000".equals(reportData.get(1)[8].toString()), message);
        assertTrue("-2447.76000".equals(reportData.get(2)[8].toString()), message);
        assertTrue("-2436.30000".equals(reportData.get(3)[8].toString()), message);
        assertTrue("0.00000".equals(reportData.get(0)[10].toString()), message);
        assertTrue("163.69000".equals(reportData.get(1)[10].toString()), message);
        assertTrue("151.30000".equals(reportData.get(2)[10].toString()), message);
        assertTrue("172.59000".equals(reportData.get(3)[10].toString()), message);
        assertTrue("0.00000".equals(reportData.get(0)[12].toString()), message);
        assertTrue("5.38000".equals(reportData.get(1)[12].toString()), message);
        assertTrue("-59.00000".equals(reportData.get(2)[12].toString()), message);
        assertTrue("-57.13000".equals(reportData.get(3)[12].toString()), message);
        assertTrue("0.00000".equals(reportData.get(0)[14].toString()), message);
        assertTrue("6.54000".equals(reportData.get(1)[14].toString()), message);
        assertTrue("7.48000".equals(reportData.get(2)[14].toString()), message);
        assertTrue("14.08000".equals(reportData.get(3)[14].toString()), message);
    }

    private void shouldValidateChangeReportBusinessTypeLevelFunctionWithNonAggregateForRollingDate() {
        isRolling = 1;
        List<Object[]> reportDataByRollingDates = tenantCrudService().findByNativeQuery("exec dbo.usp_change_report_comparative_view_bt " + propertyID + "," + recordTypeId + "," + processStatusId + ",'" + startDate.minusDays(11).toString() + "','" + startDate.plusDays(6).toString() + "','" + startDate.plusDays(6).toString() + "'," + isRolling + ",'TODAY-11','TODAY+6','TODAY+6'");
        assertChangeReportDataAtBusinessTypeLevel("[shouldValidateChangeReportBusinessTypeLevelFunctionWithNonAggregateForRollingDate] " + "Change Report at Business Type level with Rolling Dates", reportDataByRollingDates, "PickUp Change Report - Test Instance - Information Only, SP_PickUPReport");
    }

    private void assertPickUpReportDataAtBusinessTypeLevel(String Level, List<Object[]> reportDataByStaticDates, String expectedSpecialEventName) {
        assertEquals(dow, (reportDataByStaticDates.get(0)[0].toString()), Level + " - DOW ");
        assertEquals(startDate.plusDays(6).toString(), (reportDataByStaticDates.get(0)[1].toString()), Level + " - Arrival Date ");
        assertEquals(expectedSpecialEventName, (reportDataByStaticDates.get(0)[2]), Level + " - Special Event ");
        assertEquals("96", String.format("%d", reportDataByStaticDates.get(0)[3]), Level + " - Transient Rooms Sold ");
        assertEquals("10", String.format("%d", reportDataByStaticDates.get(0)[4]), Level + " - Transient Rooms Sold Pick Up ");
        assertEquals("128", String.format("%d", reportDataByStaticDates.get(0)[5]), Level + " - Group Rooms Sold ");
        assertEquals("14", String.format("%d", reportDataByStaticDates.get(0)[6]), Level + " - Group Rooms Sold Pick Up ");
        assertEquals("91.00", String.format("%.2f", reportDataByStaticDates.get(0)[7]), Level + " - Transient Occupancy Forecast ");
        assertEquals("2.37", String.format("%.2f", reportDataByStaticDates.get(0)[8]), Level + " - Transient Occupancy Forecast Pick Up");
        assertEquals("96.00", String.format("%.2f", reportDataByStaticDates.get(0)[9]), Level + " - Group Occupancy Forecast ");
        assertEquals("2.51", String.format("%.2f", reportDataByStaticDates.get(0)[10]), Level + " - Group Occupancy Forecast Pick Up");
        assertEquals("5233.08", String.format("%.2f", reportDataByStaticDates.get(0)[11]), Level + " - Transient Booked Revenue ");
        assertEquals("2000.30", String.format("%.2f", reportDataByStaticDates.get(0)[12]), Level + " - Transient Booked Revenue Pick Up ");
        assertEquals("5467.25", String.format("%.2f", reportDataByStaticDates.get(0)[13]), Level + " - Group Booked Revenue ");
        assertEquals("2631.34", String.format("%.2f", reportDataByStaticDates.get(0)[14]), Level + " - Group Booked Revenue Pick Up ");
        assertEquals("1515.00", String.format("%.2f", reportDataByStaticDates.get(0)[15]), Level + " - Transient Forecasted Revenue ");
        assertEquals("-59.80", String.format("%.2f", reportDataByStaticDates.get(0)[16]), Level + " - Transient Forecasted Revenue Pick Up ");
        assertEquals("1520.00", String.format("%.2f", reportDataByStaticDates.get(0)[17]), Level + " - Group Forecasted Revenue ");
        assertEquals("-89.10", String.format("%.2f", reportDataByStaticDates.get(0)[18]), Level + " - Group Forecasted Revenue Pick Up ");
        assertEquals("54.51", String.format("%.2f", reportDataByStaticDates.get(0)[19]), Level + " - Transient Booked ADR ");
        assertEquals("44.29", String.format("%.2f", reportDataByStaticDates.get(0)[20]), Level + " - Transient Booked ADR Pick Up ");
        assertEquals("42.71", String.format("%.2f", reportDataByStaticDates.get(0)[21]), Level + " - Group Booked ADR ");
        assertEquals("51.31", String.format("%.2f", reportDataByStaticDates.get(0)[22]), Level + " - Group Booked ADR Pick Up ");
        assertEquals("16.65", String.format("%.2f", reportDataByStaticDates.get(0)[23]), Level + " - Transient Forecasted ADR");
        assertEquals("-22.21", String.format("%.2f", reportDataByStaticDates.get(0)[24]), Level + " - Transient Forecasted ADR Pick Up");
        assertEquals("15.83", String.format("%.2f", reportDataByStaticDates.get(0)[25]), Level + " - Group Forecasted ADR");
        assertEquals("-13.72", String.format("%.2f", reportDataByStaticDates.get(0)[26]), Level + " - Group Forecasted ADR Pick Up");
    }

    private void assertPickUpReportDataAtBusinessTypeLevelOnlyBusinessEndDateDataAvailable(String Level, List<Object[]> reportDataByStaticDates, String expectedSpecialEventName) {
        assertEquals("35", String.format("%d", reportDataByStaticDates.get(0)[4]), Level + " - Transient Rooms Sold Pick Up ");
        assertEquals("39", String.format("%d", reportDataByStaticDates.get(0)[6]), Level + " - Group Rooms Sold Pick Up ");
        assertEquals("3125.44", String.format("%.2f", reportDataByStaticDates.get(0)[12]), Level + " - Transient Booked Revenue Pick Up ");
        assertEquals("3756.48", String.format("%.2f", reportDataByStaticDates.get(0)[14]), Level + " - Group Booked Revenue Pick Up ");
        assertEquals("89.30", String.format("%.2f", reportDataByStaticDates.get(0)[20]), Level + " - Transient Booked ADR Pick Up ");
        assertEquals("96.32", String.format("%.2f", reportDataByStaticDates.get(0)[22]), Level + " - Group Booked ADR Pick Up ");
    }

    private void assertPickUpReportDataAtBusinessTypeLevelFullJoin(String Level, List<Object[]> reportDataByStaticDates) {
        assertNotNull(reportDataByStaticDates);
        assertTrue(reportDataByStaticDates.size() == 3);
        assertEquals(dow, (reportDataByStaticDates.get(0)[0].toString()), Level + " - DOW ");
        assertEquals(startDate.plusDays(6).toString(), (reportDataByStaticDates.get(0)[1].toString()), Level + " - Arrival Date ");
        assertEquals("PickUp Change Report - Test Instance - Information Only, SP_PickUPReport", (reportDataByStaticDates.get(0)[2]), Level + " - Special Event ");
        assertEquals("96", String.format("%d", reportDataByStaticDates.get(0)[3]), Level + " - Transient Rooms Sold ");
        assertEquals("10", String.format("%d", reportDataByStaticDates.get(0)[4]), Level + " - Transient Rooms Sold Pick Up ");
        assertEquals("128", String.format("%d", reportDataByStaticDates.get(0)[5]), Level + " - Group Rooms Sold ");
        assertEquals("14", String.format("%d", reportDataByStaticDates.get(0)[6]), Level + " - Group Rooms Sold Pick Up ");
        assertEquals("91.00", String.format("%.2f", reportDataByStaticDates.get(0)[7]), Level + " - Transient Occupancy Forecast ");
        assertEquals("2.37", String.format("%.2f", reportDataByStaticDates.get(0)[8]), Level + " - Transient Occupancy Forecast Pick Up");
        assertEquals("96.00", String.format("%.2f", reportDataByStaticDates.get(0)[9]), Level + " - Group Occupancy Forecast ");
        assertEquals("2.51", String.format("%.2f", reportDataByStaticDates.get(0)[10]), Level + " - Group Occupancy Forecast Pick Up");
        assertEquals("5233.08", String.format("%.2f", reportDataByStaticDates.get(0)[11]), Level + " - Transient Booked Revenue ");
        assertEquals("2000.30", String.format("%.2f", reportDataByStaticDates.get(0)[12]), Level + " - Transient Booked Revenue Pick Up ");
        assertEquals("5467.25", String.format("%.2f", reportDataByStaticDates.get(0)[13]), Level + " - Group Booked Revenue ");
        assertEquals("2631.34", String.format("%.2f", reportDataByStaticDates.get(0)[14]), Level + " - Group Booked Revenue Pick Up ");
        assertEquals("1515.00", String.format("%.2f", reportDataByStaticDates.get(0)[15]), Level + " - Transient Forecasted Revenue ");
        assertEquals("-59.80", String.format("%.2f", reportDataByStaticDates.get(0)[16]), Level + " - Transient Forecasted Revenue Pick Up ");
        assertEquals("1520.00", String.format("%.2f", reportDataByStaticDates.get(0)[17]), Level + " - Group Forecasted Revenue ");
        assertEquals("-89.10", String.format("%.2f", reportDataByStaticDates.get(0)[18]), Level + " - Group Forecasted Revenue Pick Up ");
        assertEquals("54.51", String.format("%.2f", reportDataByStaticDates.get(0)[19]), Level + " - Transient Booked ADR ");
        assertEquals("44.29", String.format("%.2f", reportDataByStaticDates.get(0)[20]), Level + " - Transient Booked ADR Pick Up ");
        assertEquals("42.71", String.format("%.2f", reportDataByStaticDates.get(0)[21]), Level + " - Group Booked ADR ");
        assertEquals("51.31", String.format("%.2f", reportDataByStaticDates.get(0)[22]), Level + " - Group Booked ADR Pick Up ");
        assertEquals("16.65", String.format("%.2f", reportDataByStaticDates.get(0)[23]), Level + " - Transient Forecasted ADR");
        assertEquals("-22.21", String.format("%.2f", reportDataByStaticDates.get(0)[24]), Level + " - Transient Forecasted ADR Pick Up");
        assertEquals("15.83", String.format("%.2f", reportDataByStaticDates.get(0)[25]), Level + " - Group Forecasted ADR");
        assertEquals("-13.72", String.format("%.2f", reportDataByStaticDates.get(0)[26]), Level + " - Group Forecasted ADR Pick Up");
        assertEquals(dow1, (reportDataByStaticDates.get(1)[0].toString()), Level + " - DOW ");
        assertEquals(startDate.plusDays(7).toString(), (reportDataByStaticDates.get(1)[1].toString()), Level + " - Arrival Date ");
        assertEquals("96", String.format("%d", reportDataByStaticDates.get(1)[3]), Level + " - Transient Rooms Sold ");
        assertEquals("10", String.format("%d", reportDataByStaticDates.get(1)[4]), Level + " - Transient Rooms Sold Pick Up ");
        assertNull(reportDataByStaticDates.get(1)[5], Level + " - Group Rooms Sold ");
        assertNull(reportDataByStaticDates.get(1)[6], Level + " - Group Rooms Sold Pick Up ");
        assertEquals("91.00", String.format("%.2f", reportDataByStaticDates.get(1)[7]), Level + " - Transient Occupancy Forecast ");
        assertEquals("2.37", String.format("%.2f", reportDataByStaticDates.get(1)[8]), Level + " - Transient Occupancy Forecast Pick Up");
        assertNull(reportDataByStaticDates.get(1)[9], Level + " - Group Occupancy Forecast ");
        assertNull(reportDataByStaticDates.get(1)[10], Level + " - Group Occupancy Forecast Pick Up");
        assertEquals("5233.08", String.format("%.2f", reportDataByStaticDates.get(1)[11]), Level + " - Transient Booked Revenue ");
        assertEquals("2000.30", String.format("%.2f", reportDataByStaticDates.get(1)[12]), Level + " - Transient Booked Revenue Pick Up ");
        assertNull(reportDataByStaticDates.get(1)[13], Level + " - Group Booked Revenue ");
        assertNull(reportDataByStaticDates.get(1)[14], Level + " - Group Booked Revenue Pick Up ");
        assertEquals("1515.00", String.format("%.2f", reportDataByStaticDates.get(1)[15]), Level + " - Transient Forecasted Revenue ");
        assertEquals("-59.80", String.format("%.2f", reportDataByStaticDates.get(1)[16]), Level + " - Transient Forecasted Revenue Pick Up ");
        assertNull(reportDataByStaticDates.get(1)[17], Level + " - Group Forecasted Revenue ");
        assertNull(reportDataByStaticDates.get(1)[18], Level + " - Group Forecasted Revenue Pick Up ");
        assertEquals("54.51", String.format("%.2f", reportDataByStaticDates.get(1)[19]), Level + " - Transient Booked ADR ");
        assertEquals("44.29", String.format("%.2f", reportDataByStaticDates.get(1)[20]), Level + " - Transient Booked ADR Pick Up ");
        assertNull(reportDataByStaticDates.get(1)[21], Level + " - Group Booked ADR ");
        assertNull(reportDataByStaticDates.get(1)[22], Level + " - Group Booked ADR Pick Up ");
        assertEquals("16.65", String.format("%.2f", reportDataByStaticDates.get(1)[23]), Level + " - Transient Forecasted ADR");
        assertEquals("-22.21", String.format("%.2f", reportDataByStaticDates.get(1)[24]), Level + " - Transient Forecasted ADR Pick Up");
        assertNull(reportDataByStaticDates.get(1)[25], Level + " - Group Forecasted ADR");
        assertNull(reportDataByStaticDates.get(1)[26], Level + " - Group Forecasted ADR Pick Up");
        assertEquals(dow2, (reportDataByStaticDates.get(2)[0].toString()), Level + " - DOW ");
        assertEquals(startDate.plusDays(8).toString(), (reportDataByStaticDates.get(2)[1].toString()), Level + " - Arrival Date ");
        assertNull(reportDataByStaticDates.get(2)[3], Level + " - Transient Rooms Sold ");
        assertNull(reportDataByStaticDates.get(2)[4], Level + " - Transient Rooms Sold Pick Up ");
        assertEquals("128", String.format("%d", reportDataByStaticDates.get(2)[5]), Level + " - Group Rooms Sold ");
        assertEquals("14", String.format("%d", reportDataByStaticDates.get(2)[6]), Level + " - Group Rooms Sold Pick Up ");
        assertNull(reportDataByStaticDates.get(2)[7], Level + " - Transient Occupancy Forecast ");
        assertNull(reportDataByStaticDates.get(2)[8], Level + " - Transient Occupancy Forecast Pick Up");
        assertEquals("96.00", String.format("%.2f", reportDataByStaticDates.get(2)[9]), Level + " - Group Occupancy Forecast ");
        assertEquals("2.51", String.format("%.2f", reportDataByStaticDates.get(2)[10]), Level + " - Group Occupancy Forecast Pick Up");
        assertNull(reportDataByStaticDates.get(2)[11], Level + " - Transient Booked Revenue ");
        assertNull(reportDataByStaticDates.get(2)[12], Level + " - Transient Booked Revenue Pick Up ");
        assertEquals("5467.25", String.format("%.2f", reportDataByStaticDates.get(2)[13]), Level + " - Group Booked Revenue ");
        assertEquals("2631.34", String.format("%.2f", reportDataByStaticDates.get(2)[14]), Level + " - Group Booked Revenue Pick Up ");
        assertNull(reportDataByStaticDates.get(2)[15], Level + " - Transient Forecasted Revenue ");
        assertNull(reportDataByStaticDates.get(2)[16], Level + " - Transient Forecasted Revenue Pick Up ");
        assertEquals("1520.00", String.format("%.2f", reportDataByStaticDates.get(2)[17]), Level + " - Group Forecasted Revenue ");
        assertEquals("-89.10", String.format("%.2f", reportDataByStaticDates.get(2)[18]), Level + " - Group Forecasted Revenue Pick Up ");
        assertNull(reportDataByStaticDates.get(2)[19], Level + " - Transient Booked ADR ");
        assertNull(reportDataByStaticDates.get(2)[20], Level + " - Transient Booked ADR Pick Up ");
        assertEquals("42.71", String.format("%.2f", reportDataByStaticDates.get(2)[21]), Level + " - Group Booked ADR ");
        assertEquals("51.31", String.format("%.2f", reportDataByStaticDates.get(0)[22]), Level + " - Group Booked ADR Pick Up ");
        assertNull(reportDataByStaticDates.get(2)[23], Level + " - Transient Forecasted ADR");
        assertNull(reportDataByStaticDates.get(2)[24], Level + " - Transient Forecasted ADR Pick Up");
        assertEquals("15.83", String.format("%.2f", reportDataByStaticDates.get(2)[25]), Level + " - Group Forecasted ADR");
        assertEquals("-13.72", String.format("%.2f", reportDataByStaticDates.get(2)[26]), Level + " - Group Forecasted ADR Pick Up");
    }

    private void assertChangeReportDataAtBusinessTypeLevel(String Level, List<Object[]> reportDataByStaticDates, String expectedSpecialEventName) {
        assertEquals(dow, (reportDataByStaticDates.get(0)[0].toString()), Level + " - DOW ");
        assertEquals(startDate.plusDays(6).toString(), (reportDataByStaticDates.get(0)[1].toString()), Level + " - Arrival Date ");
        assertEquals(expectedSpecialEventName, (reportDataByStaticDates.get(0)[2]), Level + " - Special Event ");
        assertEquals("96", (reportDataByStaticDates.get(0)[3].toString()), Level + " - Transient Rooms Sold ");
        assertEquals("71", (reportDataByStaticDates.get(0)[4].toString()), Level + " - Transient Rooms Sold Pick Up ");
        assertEquals("128", (reportDataByStaticDates.get(0)[5].toString()), Level + " - Group Rooms Sold ");
        assertEquals("103", (reportDataByStaticDates.get(0)[6].toString()), Level + " - Group Rooms Sold Pick Up ");
        assertEquals("91.00", String.format("%.2f", reportDataByStaticDates.get(0)[7]), Level + " - Transient Occupancy Forecast ");
        assertEquals("87.50", String.format("%.2f", reportDataByStaticDates.get(0)[8]), Level + " - Transient Occupancy Forecast Pick Up");
        assertEquals("96.00", String.format("%.2f", reportDataByStaticDates.get(0)[9]), Level + " - Group Occupancy Forecast ");
        assertEquals("88.50", String.format("%.2f", reportDataByStaticDates.get(0)[10]), Level + " - Group Occupancy Forecast Pick Up");
        assertEquals("5233.08", String.format("%.2f", reportDataByStaticDates.get(0)[11]), Level + " - Transient Booked Revenue ");
        assertEquals("4107.94", String.format("%.2f", reportDataByStaticDates.get(0)[12]), Level + " - Transient Booked Revenue Pick Up ");
        assertEquals("5467.25", String.format("%.2f", reportDataByStaticDates.get(0)[13]), Level + " - Group Booked Revenue ");
        assertEquals("4342.11", String.format("%.2f", reportDataByStaticDates.get(0)[14]), Level + " - Group Booked Revenue Pick Up ");
        assertEquals("1515.00", String.format("%.2f", reportDataByStaticDates.get(0)[15]), Level + " - Transient Forecasted Revenue ");
        assertEquals("1410.79", String.format("%.2f", reportDataByStaticDates.get(0)[16]), Level + " - Transient Forecasted Revenue Pick Up ");
        assertEquals("1520.00", String.format("%.2f", reportDataByStaticDates.get(0)[17]), Level + " - Group Forecasted Revenue ");
        assertEquals("1375.79", String.format("%.2f", reportDataByStaticDates.get(0)[18]), Level + " - Group Forecasted Revenue Pick Up ");
        assertEquals("54.51", String.format("%.2f", reportDataByStaticDates.get(0)[19]), Level + " - Transient Booked ADR ");
        assertEquals("9.50", String.format("%.2f", reportDataByStaticDates.get(0)[20]), Level + " - Transient Booked ADR Pick Up ");
        assertEquals("42.71", String.format("%.2f", reportDataByStaticDates.get(0)[21]), Level + " - Group Booked ADR ");
        assertEquals("-2.30", String.format("%.2f", reportDataByStaticDates.get(0)[22]), Level + " - Group Booked ADR Pick Up ");
        assertEquals("16.65", String.format("%.2f", reportDataByStaticDates.get(0)[23]), Level + " - Transient Forecasted ADR");
        assertEquals("-13.13", String.format("%.2f", reportDataByStaticDates.get(0)[24]), Level + " - Transient Forecasted ADR Pick Up");
        assertEquals("15.83", String.format("%.2f", reportDataByStaticDates.get(0)[25]), Level + " - Group Forecasted ADR");
        assertEquals("-3.39", String.format("%.2f", reportDataByStaticDates.get(0)[26]), Level + " - Group Forecasted ADR Pick Up");
    }

    private void assertChangeReportDataAtBusinessTypeLevelFullJoin(String Level, List<Object[]> reportDataByStaticDates) {
        assertNotNull(reportDataByStaticDates);
        assertTrue(reportDataByStaticDates.size() == 3);
        assertEquals(dow, (reportDataByStaticDates.get(0)[0].toString()), Level + " - DOW ");
        assertEquals(startDate.plusDays(6).toString(), (reportDataByStaticDates.get(0)[1].toString()), Level + " - Arrival Date ");
        assertEquals("PickUp Change Report - Test Instance - Information Only, SP_PickUPReport", (reportDataByStaticDates.get(0)[2]), Level + " - Special Event ");
        assertEquals("96", (reportDataByStaticDates.get(0)[3].toString()), Level + " - Transient Rooms Sold ");
        assertEquals("71", (reportDataByStaticDates.get(0)[4].toString()), Level + " - Transient Rooms Sold Pick Up ");
        assertEquals("128", (reportDataByStaticDates.get(0)[5].toString()), Level + " - Group Rooms Sold ");
        assertEquals("103", (reportDataByStaticDates.get(0)[6].toString()), Level + " - Group Rooms Sold Pick Up ");
        assertEquals("91.00", String.format("%.2f", reportDataByStaticDates.get(0)[7]), Level + " - Transient Occupancy Forecast ");
        assertEquals("87.50", String.format("%.2f", reportDataByStaticDates.get(0)[8]), Level + " - Transient Occupancy Forecast Pick Up");
        assertEquals("96.00", String.format("%.2f", reportDataByStaticDates.get(0)[9]), Level + " - Group Occupancy Forecast ");
        assertEquals("88.50", String.format("%.2f", reportDataByStaticDates.get(0)[10]), Level + " - Group Occupancy Forecast Pick Up");
        assertEquals("5233.08", String.format("%.2f", reportDataByStaticDates.get(0)[11]), Level + " - Transient Booked Revenue ");
        assertEquals("4107.94", String.format("%.2f", reportDataByStaticDates.get(0)[12]), Level + " - Transient Booked Revenue Pick Up ");
        assertEquals("5467.25", String.format("%.2f", reportDataByStaticDates.get(0)[13]), Level + " - Group Booked Revenue ");
        assertEquals("4342.11", String.format("%.2f", reportDataByStaticDates.get(0)[14]), Level + " - Group Booked Revenue Pick Up ");
        assertEquals("1515.00", String.format("%.2f", reportDataByStaticDates.get(0)[15]), Level + " - Transient Forecasted Revenue ");
        assertEquals("1410.79", String.format("%.2f", reportDataByStaticDates.get(0)[16]), Level + " - Transient Forecasted Revenue Pick Up ");
        assertEquals("1520.00", String.format("%.2f", reportDataByStaticDates.get(0)[17]), Level + " - Group Forecasted Revenue ");
        assertEquals("1375.79", String.format("%.2f", reportDataByStaticDates.get(0)[18]), Level + " - Group Forecasted Revenue Pick Up ");
        assertEquals("54.51", String.format("%.2f", reportDataByStaticDates.get(0)[19]), Level + " - Transient Booked ADR ");
        assertEquals("9.50", String.format("%.2f", reportDataByStaticDates.get(0)[20]), Level + " - Transient Booked ADR Pick Up ");
        assertEquals("42.71", String.format("%.2f", reportDataByStaticDates.get(0)[21]), Level + " - Group Booked ADR ");
        assertEquals("-2.30", String.format("%.2f", reportDataByStaticDates.get(0)[22]), Level + " - Group Booked ADR Pick Up ");
        assertEquals("16.65", String.format("%.2f", reportDataByStaticDates.get(0)[23]), Level + " - Transient Forecasted ADR");
        assertEquals("-13.13", String.format("%.2f", reportDataByStaticDates.get(0)[24]), Level + " - Transient Forecasted ADR Pick Up");
        assertEquals("15.83", String.format("%.2f", reportDataByStaticDates.get(0)[25]), Level + " - Group Forecasted ADR");
        assertEquals("-3.39", String.format("%.2f", reportDataByStaticDates.get(0)[26]), Level + " - Group Forecasted ADR Pick Up");
        assertEquals(dow1, (reportDataByStaticDates.get(1)[0].toString()), Level + " - DOW ");
        assertEquals(startDate.plusDays(7).toString(), (reportDataByStaticDates.get(1)[1].toString()), Level + " - Arrival Date ");
        assertEquals("96", (reportDataByStaticDates.get(1)[3].toString()), Level + " - Transient Rooms Sold ");
        assertEquals("71", (reportDataByStaticDates.get(1)[4].toString()), Level + " - Transient Rooms Sold Pick Up ");
        assertNull(reportDataByStaticDates.get(1)[5], Level + " - Group Rooms Sold ");
        assertNull(reportDataByStaticDates.get(1)[6], Level + " - Group Rooms Sold Pick Up ");
        assertEquals("91.00", String.format("%.2f", reportDataByStaticDates.get(1)[7]), Level + " - Transient Occupancy Forecast ");
        assertEquals("87.50", String.format("%.2f", reportDataByStaticDates.get(1)[8]), Level + " - Transient Occupancy Forecast Pick Up");
        assertNull(reportDataByStaticDates.get(1)[9], Level + " - Group Occupancy Forecast ");
        assertNull(reportDataByStaticDates.get(1)[10], Level + " - Group Occupancy Forecast Pick Up");
        assertEquals("5233.08", String.format("%.2f", reportDataByStaticDates.get(1)[11]), Level + " - Transient Booked Revenue ");
        assertEquals("4107.94", String.format("%.2f", reportDataByStaticDates.get(1)[12]), Level + " - Transient Booked Revenue Pick Up ");
        assertNull(reportDataByStaticDates.get(1)[13], Level + " - Group Booked Revenue ");
        assertNull(reportDataByStaticDates.get(1)[14], Level + " - Group Booked Revenue Pick Up ");
        assertEquals("1515.00", String.format("%.2f", reportDataByStaticDates.get(1)[15]), Level + " - Transient Forecasted Revenue ");
        assertEquals("1410.79", String.format("%.2f", reportDataByStaticDates.get(1)[16]), Level + " - Transient Forecasted Revenue Pick Up ");
        assertNull(reportDataByStaticDates.get(1)[17], Level + " - Group Forecasted Revenue ");
        assertNull(reportDataByStaticDates.get(1)[18], Level + " - Group Forecasted Revenue Pick Up ");
        assertEquals("54.51", String.format("%.2f", reportDataByStaticDates.get(1)[19]), Level + " - Transient Booked ADR ");
        assertEquals("9.50", String.format("%.2f", reportDataByStaticDates.get(1)[20]), Level + " - Transient Booked ADR Pick Up ");
        assertNull(reportDataByStaticDates.get(1)[21], Level + " - Group Booked ADR ");
        assertNull(reportDataByStaticDates.get(1)[22], Level + " - Group Booked ADR Pick Up ");
        assertEquals("16.65", String.format("%.2f", reportDataByStaticDates.get(1)[23]), Level + " - Transient Forecasted ADR");
        assertEquals("-13.13", String.format("%.2f", reportDataByStaticDates.get(1)[24]), Level + " - Transient Forecasted ADR Pick Up");
        assertNull(reportDataByStaticDates.get(1)[25], Level + " - Group Forecasted ADR");
        assertNull(reportDataByStaticDates.get(1)[26], Level + " - Group Forecasted ADR Pick Up");
        assertEquals(dow2, (reportDataByStaticDates.get(2)[0].toString()), Level + " - DOW ");
        assertEquals(startDate.plusDays(8).toString(), (reportDataByStaticDates.get(2)[1].toString()), Level + " - Arrival Date ");
        assertNull(reportDataByStaticDates.get(2)[3], Level + " - Transient Rooms Sold Pick Up ");
        assertNull(reportDataByStaticDates.get(2)[4], Level + " - Transient Rooms Sold Pick Up ");
        assertEquals("128", (reportDataByStaticDates.get(2)[5].toString()), Level + " - Group Rooms Sold ");
        assertEquals("103", (reportDataByStaticDates.get(2)[6].toString()), Level + " - Group Rooms Sold Pick Up ");
        assertNull(reportDataByStaticDates.get(2)[7], Level + " - Transient Occupancy Forecast ");
        assertNull(reportDataByStaticDates.get(2)[8], Level + " - Transient Occupancy Forecast Pick Up");
        assertEquals("96.00", String.format("%.2f", reportDataByStaticDates.get(2)[9]), Level + " - Group Occupancy Forecast ");
        assertEquals("88.50", String.format("%.2f", reportDataByStaticDates.get(2)[10]), Level + " - Group Occupancy Forecast Pick Up");
        assertNull(reportDataByStaticDates.get(2)[11], Level + " - Transient Booked Revenue ");
        assertNull(reportDataByStaticDates.get(2)[12], Level + " - Transient Booked Revenue Pick Up ");
        assertEquals("5467.25", String.format("%.2f", reportDataByStaticDates.get(2)[13]), Level + " - Group Booked Revenue ");
        assertEquals("4342.11", String.format("%.2f", reportDataByStaticDates.get(2)[14]), Level + " - Group Booked Revenue Pick Up ");
        assertNull(reportDataByStaticDates.get(2)[15], Level + " - Transient Forecasted Revenue ");
        assertNull(reportDataByStaticDates.get(2)[16], Level + " - Transient Forecasted Revenue Pick Up ");
        assertEquals("1520.00", String.format("%.2f", reportDataByStaticDates.get(2)[17]), Level + " - Group Forecasted Revenue ");
        assertEquals("1375.79", String.format("%.2f", reportDataByStaticDates.get(2)[18]), Level + " - Group Forecasted Revenue Pick Up ");
        assertNull(reportDataByStaticDates.get(2)[19], Level + " - Transient Booked ADR ");
        assertNull(reportDataByStaticDates.get(2)[20], Level + " - Transient Booked ADR Pick Up ");
        assertEquals("42.71", String.format("%.2f", reportDataByStaticDates.get(2)[21]), Level + " - Group Booked ADR ");
        assertEquals("-2.30", String.format("%.2f", reportDataByStaticDates.get(2)[22]), Level + " - Group Booked ADR Pick Up ");
        assertNull(reportDataByStaticDates.get(2)[23], Level + " - Transient Forecasted ADR");
        assertNull(reportDataByStaticDates.get(2)[24], Level + " - Transient Forecasted ADR Pick Up");
        assertEquals("15.83", String.format("%.2f", reportDataByStaticDates.get(2)[25]), Level + " - Group Forecasted ADR");
        assertEquals("-3.39", String.format("%.2f", reportDataByStaticDates.get(2)[26]), Level + " - Group Forecasted ADR Pick Up");
    }

    @Test
    public void shouldConsiderDEFINITEGroupsInBlockCalculation() {
        Date date = new Date();
        SimpleDateFormat sdFormat = new SimpleDateFormat("yyyy-MM-dd");
        String today = sdFormat.format(date);
        buildDataForDefiniteAndCancelledGroupBlock(today);
        String query = "select * from dbo.ufn_get_groupBlock_groupPickup_by_BusinessType " +
                "(" + propertyID + ",'" + today + "','" + today + "','group')";
        List<Object[]> reportData = tenantCrudService().findByNativeQuery(query);
        Integer booked = (Integer) reportData.get(0)[2];
        assertTrue(30 == booked, "It should contain 30 but it is " + booked);
    }

    private void buildDataForDefiniteAndCancelledGroupBlock(String today) {
        StringBuffer query = new StringBuffer("insert into Mkt_Seg ([Property_ID] ,[Mkt_Seg_Code]  ,[Mkt_Seg_Name]  ,[Mkt_Seg_Description] ,[Status_ID],[Last_Updated_DTTM],[Is_Editable],[Created_By_User_ID],[Created_DTTM],[Last_Updated_By_User_ID]) values(6,'MKT_BLK','MKT_BLK','MKT_BLK',1,GETDATE(),1,1,GETDATE(),1)");
        CrudService crudService = tenantCrudService();
        crudService.executeUpdateByNativeQuery(query.toString());
        List mktSegID = crudService.findByNativeQuery("select Mkt_Seg_ID from Mkt_Seg where Mkt_Seg_Code = 'MKT_BLK'");
        query = new StringBuffer("insert into Mkt_Seg_Details values(" + mktSegID.get(0) + ",1,1,1,1,50,0,0,0,NULL,0,2,2,0,1,GETDATE(),0,1,GETDATE(),1)");
        crudService.executeUpdateByNativeQuery(query.toString());
        query = new StringBuffer("insert into Group_Master values(6,'GRP_BLK1','GRP_BLK1','GRP_BLK1',NULL,NULL,'DEFINITE','TRANS'," + mktSegID.get(0) + "," +
                "'" + today + "','" + today + "','" + today + "','INDV',NULL,NULL,'Testcase','" + today + "',0, getDate())");
        crudService.executeUpdateByNativeQuery(query.toString());
        List groupBockID = crudService.findByNativeQuery("select Group_ID from Group_Master where Group_Code = 'GRP_BLK1'");
        query = new StringBuffer("insert into Group_Block (Group_ID, Occupancy_DT, Accom_Type_ID, Blocks, Pickup, Original_Blocks, rate) " +
                " values (" + groupBockID.get(0) + ",'" + today + "',9,10,6,10,88);")
                .append("insert into Group_Block (Group_ID, Occupancy_DT, Accom_Type_ID, Blocks, Pickup, Original_Blocks, rate)" +
                        " values(" + groupBockID.get(0) + ",'" + today + "',10,20,5,20,88);");
        crudService.executeUpdateByNativeQuery(query.toString());

        query = new StringBuffer("insert into Group_Master values(6,'GRP_BLK2','GRP_BLK2','GRP_BLK2',NULL,NULL,'CANCELLED','TRANS'," + mktSegID.get(0) + "," +
                "'" + today + "','" + today + "','" + today + "','INDV',NULL,NULL,'Testcase','" + today + "',0, getDate())");
        crudService.executeUpdateByNativeQuery(query.toString());
        groupBockID = crudService.findByNativeQuery("select Group_ID from Group_Master where Group_Code = 'GRP_BLK2'");
        query = new StringBuffer("insert into Group_Block (Group_ID, Occupancy_DT, Accom_Type_ID, Blocks, Pickup, Original_Blocks, rate) values (" + groupBockID.get(0) + ",'" + today + "',9,10,6,10,88);")
                .append("insert into Group_Block (Group_ID, Occupancy_DT, Accom_Type_ID, Blocks, Pickup, Original_Blocks, rate) values (" + groupBockID.get(0) + ",'" + today + "',10,20,5,20,88);");
        crudService.executeUpdateByNativeQuery(query.toString());
    }

    @Test
    @Tag("pickupAndChangeReportQueries-flaky")
    public void shouldReturnDataForPhysicalRoomsOnlyAndIgnoreComponentRoomsForSolds_Revenue_ADR_ForPickup_AtBusinessTypeTransient() {
        setWorkContextProperty(TestProperty.H1);
        LocalDate today = getLocalDate(TestProperty.H1.getId().toString());
        PrepareTestDataForFetchingNonComponentRoomTypes.prepareDataForTodayDate(today.toDate(), true, false, null, null, false);
        final LocalDate yesterday = new LocalDate(DateUtil.addDaysToDate(today.toDate(), -1));
        String queryStr = "select * from dbo.ufn_get_pickup_report_bt (" + 5 + ",'transient'," + recordTypeId + "," + processStatusId + ",'" + today + "','" + today + "','" + yesterday + "','" + today + "'," + isRolling + ",null,null,null,null )";
        PrepareTestDataForFetchingNonComponentRoomTypes.executeAndComparePickupAndChangeDataPoints(queryStr, 3, 7, 11, 9);
    }

    @Test
    @Tag("pickupAndChangeReportQueries-flaky")
    public void shouldReturnDataForPhysicalRoomsOnlyAndIgnoreComponentRoomsForSolds_Revenue_ADR_ForPickup_AtBusinessTypeGroup() {
        setWorkContextProperty(TestProperty.H1);
        LocalDate today = getLocalDate(TestProperty.H1.getId().toString());
        PrepareTestDataForFetchingNonComponentRoomTypes.prepareDataForTodayDate(today.toDate(), true, false, null, null, false);
        final CrudService crudService = tenantCrudService();
        crudService.executeUpdateByNativeQuery("update Mkt_Seg_Details set Business_Type_ID = 1");
        final LocalDate yesterday = new LocalDate(DateUtil.addDaysToDate(today.toDate(), -1));
        String queryStr = "select * from dbo.ufn_get_pickup_report_bt (" + 5 + ",'group'," + recordTypeId + "," + processStatusId + ",'" + today + "','" + today + "','" + yesterday + "','" + today + "'," + isRolling + ",null,null,null,null )";
        PrepareTestDataForFetchingNonComponentRoomTypes.executeAndComparePickupAndChangeDataPoints(queryStr, 3, 7, 11, 9);
    }

    @Test
    @Tag("pickupAndChangeReportQueries-flaky")
    public void shouldReturnDataForPhysicalRoomsOnlyAndIgnoreComponentRoomsForSolds_Revenue_ADR_ForChange_AtBusinessTypeTransient() {
        setWorkContextProperty(TestProperty.H1);
        LocalDate today = getLocalDate(TestProperty.H1.getId().toString());
        PrepareTestDataForFetchingNonComponentRoomTypes.prepareDataForTodayDate(today.toDate(), true, false, null, null, false);
        final LocalDate yesterday = new LocalDate(DateUtil.addDaysToDate(today.toDate(), -1));
        String queryStr = "select Rooms_Sold, Booked_Revenue, Occupancy_NBR, Booked_ADR from dbo.ufn_get_change_report_bt (" + 5 + ",'transient'," + recordTypeId + "," + processStatusId + ",'" + today + "','" + yesterday + "','" + today + "'," + isRolling + ",null,null,null )";
        PrepareTestDataForFetchingNonComponentRoomTypes.executeAndComparePickupAndChangeDataPoints(queryStr, 0, 1, 2, 3);
    }

    @Test
    @Tag("pickupAndChangeReportQueries-flaky")
    public void shouldReturnDataForPhysicalRoomsOnlyAndIgnoreComponentRoomsForSolds_Revenue_ADR_ForChange_AtBusinessTypeGroup() {
        setWorkContextProperty(TestProperty.H1);
        LocalDate today = getLocalDate(TestProperty.H1.getId().toString());
        PrepareTestDataForFetchingNonComponentRoomTypes.prepareDataForTodayDate(today.toDate(), true, false, null, null, false);
        final CrudService crudService = tenantCrudService();
        crudService.executeUpdateByNativeQuery("update Mkt_Seg_Details set Business_Type_ID = 1");
        final LocalDate yesterday = new LocalDate(DateUtil.addDaysToDate(today.toDate(), -1));
        String queryStr = "select Rooms_Sold, Booked_Revenue, Occupancy_NBR, Booked_ADR from dbo.ufn_get_change_report_bt (" + 5 + ",'group'," + recordTypeId + "," + processStatusId + ",'" + today + "','" + yesterday + "','" + today + "'," + isRolling + ",null,null,null )";
        PrepareTestDataForFetchingNonComponentRoomTypes.executeAndComparePickupAndChangeDataPoints(queryStr, 0, 1, 2, 3);
    }

    @Test
    public void shouldValidatePickUpReportBusinessTypeLevelFunctionWithNonAggregateForStaticDateAndSpecialEventNameWithInstanceName() {
        String eventInstanceName = "Test Instance";
        createTestData();
        updateSpecialEventInstanceNameOfTestSpecialEventData(startDate.plusDays(6).toString(), startDate.plusDays(6).toString(), "SP_PickUPReport", eventInstanceName);

        isRolling = 0;
        List<Object[]> reportDataByStaticDates = tenantCrudService().findByNativeQuery("exec dbo.usp_pickup_report_comparative_view_bt " + propertyID + "," + recordTypeId + "," + processStatusId + ",'" + startDate.minusDays(11).toString() + "','" + startDate.minusDays(8).toString() + "','" + startDate.plusDays(6).toString() + "','" + startDate.plusDays(6).toString() + "'," + isRolling + ",'TODAY-4','TODAY-2','TODAY','TODAY'");
        assertPickUpReportDataAtBusinessTypeLevel("[shouldValidatePickUpReportBusinessTypeLevelFunctionWithNonAggregateForStaticDate] " + "Pickup Report at Business Type level with Static Dates", reportDataByStaticDates, "PickUp Change Report - Test Instance - Information Only, SP_PickUPReport - Test Instance");
    }

    private void updateSpecialEventInstanceNameOfTestSpecialEventData(String eventStartDate, String eventEndDate, String specialEventName, String specialEventInstanceName) {
        List<PropertySpecialEvent> propertySpecialEvents = getPropertySpecialEventsByStartDateAndEndDateAndSpecialEventName(eventStartDate, eventEndDate, specialEventName);
        PropertySpecialEvent propertySpecialEvent = propertySpecialEvents.get(0);
        PropertySpecialEventInstance propertySpecialEventInstance = propertySpecialEvent.getPropertySpecialEventIntances().iterator().next();
        propertySpecialEventInstance.setEventInstanceName(specialEventInstanceName);
        tenantCrudService().save(propertySpecialEvent);
    }

    private List<PropertySpecialEvent> getPropertySpecialEventsByStartDateAndEndDateAndSpecialEventName(String eventStartDate, String eventEndDate, String specialEventName) {
        String selectQuery = "select Property_Special_Event_ID from [Property_Special_Event] WHERE [Start_DTTM] = :Start_DTTM and [End_DTTM] = :End_DTTM and [Special_Event_Name] = :Special_Event_Name";
        List<Object> specialEventsIds = tenantCrudService().findByNativeQuery(selectQuery,
                MapBuilder.with("Start_DTTM", eventStartDate)
                        .and("End_DTTM", eventEndDate)
                        .and("Special_Event_Name", specialEventName)
                        .get());

        Integer specialEventId = (Integer) specialEventsIds.get(0);
        return tenantCrudService().findByNamedQuery(PropertySpecialEvent.BY_ID_AND_PROPERTY, MapBuilder.with("propertyId", propertyID).and("specialEventId", specialEventId).get());
    }

    @Test
    public void shouldValidateChangeReportBusinessTypeLevelFunctionWithNonAggregateForStaticDateAndSpecialEventNameWithInstanceName() {
        String eventInstanceName = "Test Instance";
        createTestData();
        updateSpecialEventInstanceNameOfTestSpecialEventData(startDate.plusDays(6).toString(), startDate.plusDays(6).toString(), "SP_PickUPReport", eventInstanceName);

        isRolling = 0;
        List<Object[]> reportDataByStaticDates = tenantCrudService().findByNativeQuery("exec dbo.usp_change_report_comparative_view_bt " + propertyID + "," + recordTypeId + "," + processStatusId + ",'" + startDate.minusDays(11).toString() + "','" + startDate.plusDays(6).toString() + "','" + startDate.plusDays(6).toString() + "'," + isRolling + ",'TODAY-4','TODAY','TODAY'");
        assertChangeReportDataAtBusinessTypeLevel("[shouldValidateChangeReportBusinessTypeLevelFunctionWithNonAggregateForStaticDate] " + "Change Report at Business Type level with Static Dates", reportDataByStaticDates, "PickUp Change Report - Test Instance - Information Only, SP_PickUPReport - Test Instance");
    }

    @Test
    @Tag("pickupAndChangeReportQueries-flaky")
    public void shouldValidatePickUpReportGroupBusinessTypeLevelFunctionForStaticDateWhenActivityDateIsBeyondAnalysisDateAndForSpecialEventName() {
        createTestData();
        setUpTables(BUSINESS_TYPE_GROUP);

        shouldValidatePickUpReportGroupBusinessTypeLevelFunctionForStaticDateWhenActivityDateIsBeyondAnalysisDateAndForSpecialEventNameWithoutInstanceName();
        shouldValidatePickUpReportGroupBusinessTypeLevelFunctionForStaticDateWhenActivityDateIsBeyondAnalysisDateAndForSpecialEventNameWithInstanceName();
    }

    private void shouldValidatePickUpReportGroupBusinessTypeLevelFunctionForStaticDateWhenActivityDateIsBeyondAnalysisDateAndForSpecialEventNameWithoutInstanceName() {
        isRolling = 0;
        List<Object[]> reportData = tenantCrudService().findByNativeQuery("select * from dbo.ufn_get_pickup_report_bt (" + propertyID + ",'group'," + recordTypeId + "," + processStatusId + ",'" + startDate.plusDays(1).toString() + "','" + startDate.plusDays(2).toString() + "','" + startDate.toString() + "','" + startDate.plusDays(3).toString() + "'," + isRolling + ",'TODAY-4','TODAY-2','TODAY','TODAY')");
        assertPickUpReportDataAtBusinessTypeLevelWhenActivityDateIsBeyondAnalysisDateForSpecialEventName("shouldValidatePickUpReportGroupBusinessTypeLevelFunctionForStaticDateWhenActivityDateIsBeyondAnalysisDate ", reportData, "SP_PickUPReport1");
    }

    private void shouldValidatePickUpReportGroupBusinessTypeLevelFunctionForStaticDateWhenActivityDateIsBeyondAnalysisDateAndForSpecialEventNameWithInstanceName() {
        String eventInstanceName = "Test Instance";
        updateSpecialEventInstanceNameOfTestSpecialEventData(startDate.toString(), startDate.plusDays(3).toString(), "SP_PickUPReport1", eventInstanceName);

        isRolling = 0;
        List<Object[]> reportData = tenantCrudService().findByNativeQuery("select * from dbo.ufn_get_pickup_report_bt (" + propertyID + ",'group'," + recordTypeId + "," + processStatusId + ",'" + startDate.plusDays(1).toString() + "','" + startDate.plusDays(2).toString() + "','" + startDate.toString() + "','" + startDate.plusDays(3).toString() + "'," + isRolling + ",'TODAY-4','TODAY-2','TODAY','TODAY')");
        assertPickUpReportDataAtBusinessTypeLevelWhenActivityDateIsBeyondAnalysisDateForSpecialEventName("shouldValidatePickUpReportGroupBusinessTypeLevelFunctionForStaticDateWhenActivityDateIsBeyondAnalysisDate ", reportData, "SP_PickUPReport1 - Test Instance");
    }

    private void assertPickUpReportDataAtBusinessTypeLevelWhenActivityDateIsBeyondAnalysisDateForSpecialEventName(String message, List<Object[]> reportData, String expectedSpecialEventName) {
        reportData.sort(new Comparator<Object[]>() {

            @Override
            public int compare(Object[] objects, Object[] t1) {
                return ((Date) objects[0]).compareTo((Date) t1[0]);
            }
        });
        assertTrue(reportData.size() == 4, message);
        assertTrue("0".equals(reportData.get(0)[4].toString()), message);
        assertTrue("-12".equals(reportData.get(1)[4].toString()), message);
        assertTrue("9".equals(reportData.get(2)[4].toString()), message);
        assertTrue("8".equals(reportData.get(3)[4].toString()), message);
        assertTrue("0.00000".equals(reportData.get(0)[6].toString()), message);
        assertTrue("163.69000".equals(reportData.get(1)[6].toString()), message);
        assertTrue("-3.35000".equals(reportData.get(2)[6].toString()), message);
        assertTrue("12.19000".equals(reportData.get(3)[6].toString()), message);
        assertTrue("0.00000".equals(reportData.get(0)[8].toString()), message);
        assertTrue("-470.01000".equals(reportData.get(1)[8].toString()), message);
        assertTrue("631.04000".equals(reportData.get(2)[8].toString()), message);
        assertTrue("574.96000".equals(reportData.get(3)[8].toString()), message);
        assertTrue("0.00000".equals(reportData.get(0)[10].toString()), message);
        assertTrue("5.38000".equals(reportData.get(1)[10].toString()), message);
        assertTrue("-7.86000".equals(reportData.get(2)[10].toString()), message);
        assertTrue("-5.16000".equals(reportData.get(3)[10].toString()), message);
        assertTrue("0.00000".equals(reportData.get(0)[12].toString()), message);
        assertTrue("9.65000".equals(reportData.get(1)[12].toString()), message);
        assertTrue("2.21000".equals(reportData.get(2)[12].toString()), message);
        assertTrue("3.60000".equals(reportData.get(3)[12].toString()), message);
        assertTrue("0.00000".equals(reportData.get(0)[14].toString()), message);
        assertTrue("6.54000".equals(reportData.get(1)[14].toString()), message);
        assertTrue("-3.19000".equals(reportData.get(2)[14].toString()), message);
        assertTrue("0.08000".equals(reportData.get(3)[14].toString()), message);
        assertTrue(expectedSpecialEventName.equals(reportData.get(0)[15].toString()), message);
    }

    @Test
    @Tag("pickupAndChangeReportQueries-flaky")
    public void shouldValidateChangeGroupReportBusinessTypeLevelFunctionForStaticDateWhenActivityDateIsBeyondAnalysisDateAndForSpecialEventName() {
        createTestData();
        setUpTables(BUSINESS_TYPE_GROUP);

        shouldValidateChangeGroupReportBusinessTypeLevelFunctionForStaticDateWhenActivityDateIsBeyondAnalysisDateAndForSpecialEventNameWithoutInstanceName();
        shouldValidateChangeGroupReportBusinessTypeLevelFunctionForStaticDateWhenActivityDateIsBeyondAnalysisDateAndForSpecialEventNameWithInstanceName();
    }

    private void shouldValidateChangeGroupReportBusinessTypeLevelFunctionForStaticDateWhenActivityDateIsBeyondAnalysisDateAndForSpecialEventNameWithoutInstanceName() {
        isRolling = 0;
        List<Object[]> reportData = tenantCrudService().findByNativeQuery("select * from dbo.ufn_get_change_report_bt (" + propertyID + ",'group'," + recordTypeId + "," + processStatusId + ",'" + startDate.plusDays(1).toString() + "','" + startDate.toString() + "','" + startDate.plusDays(3).toString() + "'," + isRolling + ",'TODAY-4','TODAY','TODAY')");
        assertChangeReportDataAtBusinessTypeLevelWhenActivityDateIsBeyondAnalysisDateForSpecialEventName("shouldValidateChangeGroupReportBusinessTypeLevelFunctionForStaticDateWhenActivityDateIsBeyondAnalysisDate", reportData, "SP_PickUPReport1");
    }

    private void shouldValidateChangeGroupReportBusinessTypeLevelFunctionForStaticDateWhenActivityDateIsBeyondAnalysisDateAndForSpecialEventNameWithInstanceName() {
        String eventInstanceName = "Test Instance";
        updateSpecialEventInstanceNameOfTestSpecialEventData(startDate.toString(), startDate.plusDays(3).toString(), "SP_PickUPReport1", eventInstanceName);

        isRolling = 0;
        List<Object[]> reportData = tenantCrudService().findByNativeQuery("select * from dbo.ufn_get_change_report_bt (" + propertyID + ",'group'," + recordTypeId + "," + processStatusId + ",'" + startDate.plusDays(1).toString() + "','" + startDate.toString() + "','" + startDate.plusDays(3).toString() + "'," + isRolling + ",'TODAY-4','TODAY','TODAY')");
        assertChangeReportDataAtBusinessTypeLevelWhenActivityDateIsBeyondAnalysisDateForSpecialEventName("shouldValidateChangeGroupReportBusinessTypeLevelFunctionForStaticDateWhenActivityDateIsBeyondAnalysisDate", reportData, "SP_PickUPReport1 - Test Instance");
    }

    private void assertChangeReportDataAtBusinessTypeLevelWhenActivityDateIsBeyondAnalysisDateForSpecialEventName(String message, List<Object[]> reportData, String expectedSpecialEventName) {
        reportData.sort(new Comparator<Object[]>() {

            @Override
            public int compare(Object[] objects, Object[] t1) {
                return ((Date) objects[0]).compareTo((Date) t1[0]);
            }
        });
        assertEquals(4, reportData.size(), message);
        assertTrue("0".equals(reportData.get(0)[4].toString()), message);
        assertTrue("-12".equals(reportData.get(1)[4].toString()), message);
        assertTrue("-15".equals(reportData.get(2)[4].toString()), message);
        assertTrue("-15".equals(reportData.get(3)[4].toString()), message);
        assertTrue("0.00000".equals(reportData.get(0)[6].toString()), message);
        assertTrue("9.65000".equals(reportData.get(1)[6].toString()), message);
        assertTrue("7.44000".equals(reportData.get(2)[6].toString()), message);
        assertTrue("5.55000".equals(reportData.get(3)[6].toString()), message);
        assertTrue("0.00000".equals(reportData.get(0)[8].toString()), message);
        assertTrue("-470.01000".equals(reportData.get(1)[8].toString()), message);
        assertTrue("-2447.76000".equals(reportData.get(2)[8].toString()), message);
        assertTrue("-2436.30000".equals(reportData.get(3)[8].toString()), message);
        assertTrue("0.00000".equals(reportData.get(0)[10].toString()), message);
        assertTrue("163.69000".equals(reportData.get(1)[10].toString()), message);
        assertTrue("151.30000".equals(reportData.get(2)[10].toString()), message);
        assertTrue("172.59000".equals(reportData.get(3)[10].toString()), message);
        assertTrue("0.00000".equals(reportData.get(0)[12].toString()), message);
        assertTrue("5.38000".equals(reportData.get(1)[12].toString()), message);
        assertTrue("-59.00000".equals(reportData.get(2)[12].toString()), message);
        assertTrue("-57.13000".equals(reportData.get(3)[12].toString()), message);
        assertTrue("0.00000".equals(reportData.get(0)[14].toString()), message);
        assertTrue("6.54000".equals(reportData.get(1)[14].toString()), message);
        assertTrue("7.48000".equals(reportData.get(2)[14].toString()), message);
        assertTrue("14.08000".equals(reportData.get(3)[14].toString()), message);
        assertTrue(expectedSpecialEventName.equals(reportData.get(0)[15].toString()), message);
    }
}
