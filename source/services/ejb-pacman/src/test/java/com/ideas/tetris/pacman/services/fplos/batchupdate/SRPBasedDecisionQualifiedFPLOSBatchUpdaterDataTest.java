package com.ideas.tetris.pacman.services.fplos.batchupdate;

import com.ideas.g3.data.TestProperty;
import com.ideas.g3.test.AbstractG3JupiterTest;
import com.ideas.g3.test.category.SlowDBTest;
import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.configparams.GUIConfigParamName;
import com.ideas.tetris.pacman.common.configparams.IPConfigParamName;
import com.ideas.tetris.pacman.common.configparams.IntegrationConfigParamName;
import com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomClass;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.Decision;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.decision.DecisionService;
import com.ideas.tetris.pacman.services.fplos.FPLOSQualifiedDecisionService;
import com.ideas.tetris.pacman.services.fplos.FPLOSQualifiedRecommendationService;
import com.ideas.tetris.pacman.services.fplos.dto.FPLOSQualifiedChunkDetail;
import com.ideas.tetris.pacman.services.fplos.entity.FPLOSDecisions;
import com.ideas.tetris.pacman.services.grouppricing.configuration.entity.GroupPricingEvaluationMethod;
import com.ideas.tetris.pacman.services.hospitalityrooms.service.HospitalityRoomsService;
import com.ideas.tetris.pacman.services.limittotal.service.LimitTotalService;
import com.ideas.tetris.pacman.services.marketsegment.entity.BusinessType;
import com.ideas.tetris.pacman.services.propertygroup.service.PropertyGroupService;
import com.ideas.tetris.pacman.services.servicingcostbylos.entity.ServicingCostByLOSConfiguration;
import com.ideas.tetris.pacman.services.servicingcostbylos.service.ServicingCostByLOSService;
import com.ideas.tetris.pacman.services.virtualproperty.service.VirtualPropertyMappingService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.util.jdbc.JpaJdbcUtil;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.apache.commons.lang.StringUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.math.BigDecimal;
import java.sql.Connection;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName.HIGHEST_BAR_RESTRICTED_ENABLED;
import static com.ideas.tetris.pacman.common.configparams.IntegrationConfigParamName.CORE_PROPERTY_QUALIFIED_FPLOSMAX_LOS;
import static com.ideas.tetris.pacman.common.configparams.IntegrationConfigParamName.MIN_MAX_LOS_BY_RATE_CODE_UPLOADTYPE;
import static com.ideas.tetris.pacman.common.constants.Constants.END_DATE;
import static com.ideas.tetris.pacman.common.constants.Constants.FALSE;
import static com.ideas.tetris.pacman.common.constants.Constants.FULL;
import static com.ideas.tetris.pacman.common.constants.Constants.RATE_LEVEL_0;
import static com.ideas.tetris.pacman.common.constants.Constants.START_DATE;
import static com.ideas.tetris.pacman.common.constants.Constants.TRUE;
import static com.ideas.tetris.platform.common.externalsystem.ExternalSystem.TARS;
import static java.math.BigDecimal.valueOf;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@SlowDBTest
@MockitoSettings(strictness = Strictness.LENIENT)
public class SRPBasedDecisionQualifiedFPLOSBatchUpdaterDataTest extends AbstractG3JupiterTest {

    public static final String LOS = "7";
    public static final int TRANSIENT_BUSINESS_TYPE = 2;
    public static final String QR_MASTER_NON_DERIVED = "QR1";
    public static final String SPECIAL_RATE_PLAN = QR_MASTER_NON_DERIVED;
    public static final String NON_SPECIAL_RATE_PLAN = "QR2";
    public static final String CAUGHT_UP_DATE_3_JAN_SUN = "2016-01-03";
    public static final String RATE_PLAN_LV0 = "LV0";
    public static final String CAUGHT_UP_DATE_JAN_01_FRI = "2016-01-01";
    static final int PROPERTY_ID = 5;
    static final int NON_MASTER_ACCOM_CLASS_DLX = 2;
    static final int QUEEN_ACCOM_TYPE = 7;
    static final int KING_ACCOM_TYPE = 8;
    static final int MASTER_ACCOM_CLASS_ID = 3;
    static final int NON_MASTER_ACCOM_CLASS_ID = 2;
    static final int MASTER_ACCOM_TYPE_DDLX = 4;
    static final int MASTER_ACCOM_TYPE_ID_KING = 8;
    static final int NON_MASTER_ACCOM_TYPE_DBL = 6;
    static final int NON_MASTER_ACCOM_TYPE_Q = 7;
    static final List<BigDecimal> UNQUALIFIED_RATES_DATA = Arrays.asList(valueOf(50), valueOf(50), valueOf(50),
            valueOf(50), valueOf(50), valueOf(50), valueOf(50));
    @Spy
    FPLOSQualifiedRecommendationService fplosRecomendationService;
    @Spy
    ServicingCostByLOSService servicingCostByLOSService;
    @Mock
    DateService dateService;
    @Mock
    DecisionService decisionService;
    @Mock
    PacmanConfigParamsService pacmanConfigParamsService;
    @Mock
    HospitalityRoomsService hospitalityRoomsService;
    @Mock
    LimitTotalService limitTotalService;
    @Mock
    VirtualPropertyMappingService virtualPropertyMappingService;
    @Mock
    JpaJdbcUtil jpaJdbcUtil;
    private List<BigDecimal> masterLRVDataList;
    private FPLOSQualifiedDecisionService fplosDecisionService;
    private Date startDate;
    private String rateStartDateInStr;
    private Date endDate;
    private String rateEndDateInStr;
    private String LV0 = "LV0";

    @BeforeEach
    public void setUp() {
        setWorkContextProperty(TestProperty.H1);
        dateService = getDateService();

        decisionService.setDateServiceLocal(dateService);
        decisionService.setCrudService(tenantCrudService());

        inject(fplosRecomendationService, "crudService", tenantCrudService());
        inject(fplosRecomendationService, "configService", pacmanConfigParamsService);
        inject(fplosRecomendationService, "hospitalityRoomsService", hospitalityRoomsService);
        dateService.setConfigParamsService(pacmanConfigParamsService);
        inject(fplosRecomendationService, "dateService", dateService);
        inject(fplosRecomendationService, "decisionService", decisionService);
        inject(fplosRecomendationService, "virtualPropertyMappingService", virtualPropertyMappingService);
        inject(fplosRecomendationService, "jpaJdbcUtil", jpaJdbcUtil);
        inject(servicingCostByLOSService, "tenantCrudService", tenantCrudService());
        inject(fplosRecomendationService, "servicingCostByLOSService", servicingCostByLOSService);
        fplosDecisionService = new FPLOSQualifiedDecisionService();
        inject(fplosDecisionService, "limitTotalService", limitTotalService);
        inject(fplosDecisionService, "configService", pacmanConfigParamsService);
        inject(fplosDecisionService, "crudService", tenantCrudService());
        inject(fplosDecisionService, "dateServiceLocal", dateService);

        when(jpaJdbcUtil.getJdbcConnection(tenantCrudService())).thenReturn(connection(tenantCrudService()));
        prepareLRVList(39, 8);
        mockDecisionRecord();
        mockConfigParameters();
        populateServicingCost();
        andCaughtUpDateUpdatedTo(CAUGHT_UP_DATE_JAN_01_FRI);
        when(pacmanConfigParamsService.getParameterValue(PreProductionConfigParamName.DERIVED_RATES_FPLOS_CORRECTION_ENABLED)).thenReturn(Boolean.FALSE);
        when(limitTotalService.isLimitTotalEnabled()).thenReturn(Boolean.FALSE);
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_FPLOS_QUALIFIED_RECOMMENDATION_LOOP_STEP)).thenReturn(true);
        when(virtualPropertyMappingService.getPhysicalPropertyMasterClassMapForVirtualProperty(PROPERTY_ID)).thenReturn(Collections.emptyMap());
    }

    @Test
    public void verifyGeneratedFPLOSWhenHighestBARIsRestrictedAndSRPFPLOSAtTotalLevelIsFalse() {
        when(pacmanConfigParamsService.getBooleanParameterValue(HIGHEST_BAR_RESTRICTED_ENABLED)).thenReturn(true);
        when(pacmanConfigParamsService.getParameterValue(IntegrationConfigParamName.SRP_FPLOS_AT_TOTAL_LEVEL.value())).thenReturn(Boolean.FALSE.toString());
        whenConfigParamSetToReturn(IPConfigParamName.BAR_BAR_DECISION.value(), Constants.BAR_DECISION_VALUE_LOS)
                .whenConfigParamSetToReturn(CORE_PROPERTY_QUALIFIED_FPLOSMAX_LOS.value(), "7")
                .andRatePlanStartAndEndDateRangeAreSet(dateService.getCaughtUpDate(), 7)
                .andAccomActivityDataCleanedUp()
                .andAccomActivityDataInsertedForAccomType(QUEEN_ACCOM_TYPE, startDate, 7, 40, 1)
                .andAccomActivityDataInsertedForAccomType(KING_ACCOM_TYPE, startDate, 7, 40, 1)
                .andDecisionLRVDataIsPreparedForAccomClass(NON_MASTER_ACCOM_CLASS_DLX, startDate, masterLRVDataList.size(), masterLRVDataList)
                .andQualifiedRatePlanDataIsInserted(1, SPECIAL_RATE_PLAN, rateStartDateInStr, rateEndDateInStr, 3)
                .andQualifiedRatePlanDetailsIsInsertedForAccomType(1, QUEEN_ACCOM_TYPE, rateStartDateInStr, rateEndDateInStr,
                        valueOf(100), valueOf(50), valueOf(50),
                        valueOf(50), valueOf(50), valueOf(40), valueOf(38))
                .andQualifiedRatePlanDetailsIsInsertedForAccomType(1, KING_ACCOM_TYPE, rateStartDateInStr, rateEndDateInStr,
                        valueOf(100), valueOf(50), valueOf(50),
                        valueOf(50), valueOf(50), valueOf(40), valueOf(38))
                .andQualifiedRatePlanDataIsInserted(2, NON_SPECIAL_RATE_PLAN, rateStartDateInStr, rateEndDateInStr, 3)
                .andQualifiedRatePlanDetailsIsInsertedForAccomType(2, QUEEN_ACCOM_TYPE, rateStartDateInStr, rateEndDateInStr,
                        valueOf(100), valueOf(50), valueOf(50),
                        valueOf(50), valueOf(50), valueOf(40), valueOf(38))
                .andQualifiedRatePlanDetailsIsInsertedForAccomType(2, KING_ACCOM_TYPE, rateStartDateInStr, rateEndDateInStr,
                        valueOf(100), valueOf(50), valueOf(50),
                        valueOf(50), valueOf(50), valueOf(40), valueOf(38))
                .andQualifiedRatePlanDataIsInserted(3, LV0, rateStartDateInStr, rateEndDateInStr, 3)
                .andQualifiedRatePlanDetailsIsInsertedForAccomType(3, QUEEN_ACCOM_TYPE, rateStartDateInStr, rateEndDateInStr,
                        valueOf(100), valueOf(50), valueOf(50),
                        valueOf(50), valueOf(50), valueOf(40), valueOf(38))
                .andQualifiedRatePlanDetailsIsInsertedForAccomType(3, KING_ACCOM_TYPE, rateStartDateInStr, rateEndDateInStr,
                        valueOf(100), valueOf(50), valueOf(50),
                        valueOf(50), valueOf(50), valueOf(40), valueOf(38))
                .andDecisionRestrictHighestBARIsSetFor(QUEEN_ACCOM_TYPE, 1)
                .andDecisionRestrictHighestBARIsSetFor(KING_ACCOM_TYPE, 3)

                .thenFPLOSDecisionsAreGenerated(Arrays.asList(1, 2, 3), false, false)

                .assertBarByLOSFPLOSDecisionsWhenHighestBARIsRestricted();
    }

    @Test
    public void verifyGeneratedFPLOSWhenCPLRAAndHighestBARIsRestrictedAndSRPFPLOSAtTotalLevelIsFalse() {
        System.setProperty("pacman.fplos.cp.lra.enabled", "false");
        createDataForCPLRAAndHighestBARRestrictedAndSRPFPLOSAtRT();
    }

    @Test
    public void verifyGeneratedFPLOSWhenCPLRAAndHighestBARIsRestrictedAndSRPFPLOSAtTotalLevelIsFalseWhenSystemToggleOptimizeCPLRATrue() {
        System.setProperty("pacman.fplos.cp.lra.enabled", "true");
        createDataForCPLRAAndHighestBARRestrictedAndSRPFPLOSAtRT();
    }

    private void createDataForCPLRAAndHighestBARRestrictedAndSRPFPLOSAtRT() {
        when(pacmanConfigParamsService.getBooleanParameterValue(HIGHEST_BAR_RESTRICTED_ENABLED)).thenReturn(true);
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.LRAENABLED)).thenReturn(true);
        when(pacmanConfigParamsService.getBooleanParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED.value())).thenReturn(true);
        whenConfigParamSetToReturn(IPConfigParamName.BAR_BAR_DECISION.value(), Constants.BAR_DECISION_VALUE_LOS)
                .whenConfigParamSetToReturn(IntegrationConfigParamName.SRP_FPLOS_AT_TOTAL_LEVEL.value(), FALSE)
                .whenConfigParamSetToReturn(CORE_PROPERTY_QUALIFIED_FPLOSMAX_LOS.value(), "7")
                .andRatePlanStartAndEndDateRangeAreSet(dateService.getCaughtUpDate(), 7)
                .andAccomActivityDataCleanedUp()
                .andAccomActivityDataInsertedForAccomType(QUEEN_ACCOM_TYPE, startDate, 7, 40, 1)
                .andAccomActivityDataInsertedForAccomType(KING_ACCOM_TYPE, startDate, 7, 40, 1)
                .andDecisionLRVDataIsPreparedForAccomClass(NON_MASTER_ACCOM_CLASS_DLX, startDate, masterLRVDataList.size(), masterLRVDataList)
                .andQualifiedRatePlanDataIsInserted(1, SPECIAL_RATE_PLAN, rateStartDateInStr, rateEndDateInStr, 3)
                .andQualifiedRatePlanDetailsIsInsertedForAccomType(1, QUEEN_ACCOM_TYPE, rateStartDateInStr, rateEndDateInStr,
                        valueOf(100), valueOf(50), valueOf(50),
                        valueOf(50), valueOf(50), valueOf(40), valueOf(38))
                .andQualifiedRatePlanDetailsIsInsertedForAccomType(1, KING_ACCOM_TYPE, rateStartDateInStr, rateEndDateInStr,
                        valueOf(100), valueOf(50), valueOf(50),
                        valueOf(50), valueOf(50), valueOf(40), valueOf(38))
                .andQualifiedRatePlanDataIsInserted(2, NON_SPECIAL_RATE_PLAN, rateStartDateInStr, rateEndDateInStr, 3)
                .andQualifiedRatePlanDetailsIsInsertedForAccomType(2, QUEEN_ACCOM_TYPE, rateStartDateInStr, rateEndDateInStr,
                        valueOf(100), valueOf(50), valueOf(50),
                        valueOf(50), valueOf(50), valueOf(40), valueOf(38))
                .andQualifiedRatePlanDetailsIsInsertedForAccomType(2, KING_ACCOM_TYPE, rateStartDateInStr, rateEndDateInStr,
                        valueOf(100), valueOf(50), valueOf(50),
                        valueOf(50), valueOf(50), valueOf(40), valueOf(38))
                .andQualifiedRatePlanDataIsInserted(3, LV0, rateStartDateInStr, rateEndDateInStr, 3)
                .andQualifiedRatePlanDetailsIsInsertedForAccomType(3, QUEEN_ACCOM_TYPE, rateStartDateInStr, rateEndDateInStr,
                        valueOf(100), valueOf(50), valueOf(50),
                        valueOf(50), valueOf(50), valueOf(40), valueOf(38))
                .andQualifiedRatePlanDetailsIsInsertedForAccomType(3, KING_ACCOM_TYPE, rateStartDateInStr, rateEndDateInStr,
                        valueOf(100), valueOf(50), valueOf(50),
                        valueOf(50), valueOf(50), valueOf(40), valueOf(38))
                .andDecisionRestrictHighestBARIsSetFor(QUEEN_ACCOM_TYPE, 1)
                .andDecisionRestrictHighestBARIsSetFor(KING_ACCOM_TYPE, 3)

                .thenFPLOSDecisionsAreGenerated(Arrays.asList(1, 2, 3), false, false)

                .assertBarByLOSFPLOSDecisionsWhenHighestBARIsRestricted();
    }

    @Test
    public void verifyGeneratedFPLOSWhenCPLRAAndHighestBARIsRestrictedAndSRPFPLOSAtTotalLevelIsFalseAndDecisionReasonTypeIdIs6() {
        System.setProperty("pacman.fplos.cp.lra.enabled", "false");
        createDataWhenCPLRAAndHighestBARIsRestrictedAndSRPFPLOSAtTotalLevelIsFalseAndDecisionReasonTypeIdIs6();

    }

    @Test
    public void verifyGeneratedFPLOSWhenCPLRAAndHighestBARIsRestrictedAndSRPFPLOSAtTotalLevelIsFalseAndDecisionReasonTypeIdIs6WhenSystemToggleOptimizeCPLRATrue() {
        System.setProperty("pacman.fplos.cp.lra.enabled", "true");
        createDataWhenCPLRAAndHighestBARIsRestrictedAndSRPFPLOSAtTotalLevelIsFalseAndDecisionReasonTypeIdIs6();

    }

    private void createDataWhenCPLRAAndHighestBARIsRestrictedAndSRPFPLOSAtTotalLevelIsFalseAndDecisionReasonTypeIdIs6() {
        when(pacmanConfigParamsService.getBooleanParameterValue(HIGHEST_BAR_RESTRICTED_ENABLED)).thenReturn(true);
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.LRAENABLED)).thenReturn(true);
        when(pacmanConfigParamsService.getBooleanParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED.value())).thenReturn(true);
        setDecisionReasonTypeTo6();
        whenConfigParamSetToReturn(IPConfigParamName.BAR_BAR_DECISION.value(), Constants.BAR_DECISION_VALUE_LOS)
                .whenConfigParamSetToReturn(IntegrationConfigParamName.SRP_FPLOS_AT_TOTAL_LEVEL.value(), Constants.FALSE)
                .whenConfigParamSetToReturn(CORE_PROPERTY_QUALIFIED_FPLOSMAX_LOS.value(), "7")
                .andRatePlanStartAndEndDateRangeAreSet(dateService.getCaughtUpDate(), 7)
                .andAccomActivityDataCleanedUp()
                .andAccomActivityDataInsertedForAccomType(QUEEN_ACCOM_TYPE, startDate, 7, 40, 1)
                .andAccomActivityDataInsertedForAccomType(KING_ACCOM_TYPE, startDate, 7, 40, 1)
                .andDecisionLRVDataIsPreparedForAccomClass(NON_MASTER_ACCOM_CLASS_DLX, startDate, masterLRVDataList.size(), masterLRVDataList)
                .andQualifiedRatePlanDataIsInserted(1, SPECIAL_RATE_PLAN, rateStartDateInStr, rateEndDateInStr, 3)
                .andQualifiedRatePlanDetailsIsInsertedForAccomType(1, QUEEN_ACCOM_TYPE, rateStartDateInStr, rateEndDateInStr,
                        valueOf(100), valueOf(50), valueOf(50),
                        valueOf(50), valueOf(50), valueOf(40), valueOf(38))
                .andQualifiedRatePlanDetailsIsInsertedForAccomType(1, KING_ACCOM_TYPE, rateStartDateInStr, rateEndDateInStr,
                        valueOf(100), valueOf(50), valueOf(50),
                        valueOf(50), valueOf(50), valueOf(40), valueOf(38))
                .andQualifiedRatePlanDataIsInserted(2, NON_SPECIAL_RATE_PLAN, rateStartDateInStr, rateEndDateInStr, 3)
                .andQualifiedRatePlanDetailsIsInsertedForAccomType(2, QUEEN_ACCOM_TYPE, rateStartDateInStr, rateEndDateInStr,
                        valueOf(100), valueOf(50), valueOf(50),
                        valueOf(50), valueOf(50), valueOf(40), valueOf(38))
                .andQualifiedRatePlanDetailsIsInsertedForAccomType(2, KING_ACCOM_TYPE, rateStartDateInStr, rateEndDateInStr,
                        valueOf(100), valueOf(50), valueOf(50),
                        valueOf(50), valueOf(50), valueOf(40), valueOf(38))
                .andQualifiedRatePlanDataIsInserted(3, LV0, rateStartDateInStr, rateEndDateInStr, 3)
                .andQualifiedRatePlanDetailsIsInsertedForAccomType(3, QUEEN_ACCOM_TYPE, rateStartDateInStr, rateEndDateInStr,
                        valueOf(100), valueOf(50), valueOf(50),
                        valueOf(50), valueOf(50), valueOf(40), valueOf(38))
                .andQualifiedRatePlanDetailsIsInsertedForAccomType(3, KING_ACCOM_TYPE, rateStartDateInStr, rateEndDateInStr,
                        valueOf(100), valueOf(50), valueOf(50),
                        valueOf(50), valueOf(50), valueOf(40), valueOf(38))
                .andDecisionRestrictHighestBARIsSetFor(QUEEN_ACCOM_TYPE, 1)
                .andDecisionRestrictHighestBARIsSetFor(KING_ACCOM_TYPE, 3)

                .thenFPLOSDecisionsAreGenerated(Arrays.asList(1, 2, 3), false, false);

        List<FPLOSDecisions> decisions = fplosDecisionService.getFPLOSDecisionData(startDate, endDate);
        assertEquals(6, decisions.size());
        decisions.forEach(decision -> assertEquals(getExpectedFPLOSForLRA(decision), decision.getFplos(), decision.getRateCodeName() + decision.getAccomTypeName()));
    }

    private void setDecisionReasonTypeTo6() {
        java.sql.Date maxDate = tenantCrudService().findByNativeQuerySingleResult("select max(Arrival_Dt) from Cp_Decision_BAR_Output", null);
        LocalDate localDate = maxDate.toLocalDate();
        tenantCrudService().executeUpdateByNativeQuery("" +
                "update CP_Decision_Bar_Output set Arrival_DT = '2016-01-01', Decision_Reason_Type_ID=6 where Arrival_DT='" + maxDate + "' and accom_type_id in (7,8)\n" +
                "update CP_Decision_Bar_Output set Arrival_DT = '2016-01-02', Decision_Reason_Type_ID=6 where Arrival_DT='" + localDate.minusDays(1) + "' and accom_type_id in (7,8)\n" +
                "update CP_Decision_Bar_Output set Arrival_DT = '2016-01-03', Decision_Reason_Type_ID=6 where Arrival_DT='" + localDate.minusDays(2) + "' and accom_type_id in (7,8)\n" +
                "update CP_Decision_Bar_Output set Arrival_DT = '2016-01-04', Decision_Reason_Type_ID=6 where Arrival_DT='" + localDate.minusDays(3) + "' and accom_type_id in (7,8)\n" +
                "update CP_Decision_Bar_Output set Arrival_DT = '2016-01-05', Decision_Reason_Type_ID=6 where Arrival_DT='" + localDate.minusDays(4) + "' and accom_type_id in (7,8)\n" +
                "update CP_Decision_Bar_Output set Arrival_DT = '2016-01-06', Decision_Reason_Type_ID=6 where Arrival_DT='" + localDate.minusDays(5) + "' and accom_type_id in (7,8)\n" +
                "update CP_Decision_Bar_Output set Arrival_DT = '2016-01-07', Decision_Reason_Type_ID=6 where Arrival_DT='" + localDate.minusDays(6) + "' and accom_type_id in (7,8);");
    }

    private String getExpectedFPLOSForLRA(FPLOSDecisions decision) {
        if (decision.getRateCodeName().equals(LV0)) {
            return "NNNNNNN";
        }
        return "YNYYYYY";
    }

    @Test
    public void verifyGeneratedFPLOSWhenHighestBARIsRestrictedAndSRPFPLOSAtTotalLevelIsFalse_LimitTotalSRPIsTrue() {
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.GENERATE_LIMIT_TOTAL_SRP_RATES)).thenReturn(true);
        when(pacmanConfigParamsService.getBooleanParameterValue(HIGHEST_BAR_RESTRICTED_ENABLED)).thenReturn(true);
        whenConfigParamSetToReturn(IPConfigParamName.BAR_BAR_DECISION.value(), Constants.BAR_DECISION_VALUE_LOS)
                .whenConfigParamSetToReturn(IntegrationConfigParamName.SRP_FPLOS_AT_TOTAL_LEVEL.value(), Constants.FALSE)
                .whenConfigParamSetToReturn(CORE_PROPERTY_QUALIFIED_FPLOSMAX_LOS.value(), "7")
                .andRatePlanStartAndEndDateRangeAreSet(dateService.getCaughtUpDate(), 7)
                .andAccomActivityDataCleanedUp()
                .andAccomActivityDataInsertedForAccomType(QUEEN_ACCOM_TYPE, startDate, 7, 40, 1)
                .andAccomActivityDataInsertedForAccomType(KING_ACCOM_TYPE, startDate, 7, 40, 1)
                .andDecisionLRVDataIsPreparedForAccomClass(NON_MASTER_ACCOM_CLASS_DLX, startDate, masterLRVDataList.size(), masterLRVDataList)
                .andQualifiedRatePlanDataIsInserted(1, SPECIAL_RATE_PLAN, rateStartDateInStr, rateEndDateInStr, 3)
                .andQualifiedRatePlanDetailsIsInsertedForAccomType(1, QUEEN_ACCOM_TYPE, rateStartDateInStr, rateEndDateInStr,
                        valueOf(100), valueOf(50), valueOf(50),
                        valueOf(50), valueOf(50), valueOf(40), valueOf(38))
                .andQualifiedRatePlanDetailsIsInsertedForAccomType(1, KING_ACCOM_TYPE, rateStartDateInStr, rateEndDateInStr,
                        valueOf(100), valueOf(50), valueOf(50),
                        valueOf(50), valueOf(50), valueOf(40), valueOf(38))
                .andQualifiedRatePlanDataIsInserted(2, NON_SPECIAL_RATE_PLAN, rateStartDateInStr, rateEndDateInStr, 3)
                .andQualifiedRatePlanDetailsIsInsertedForAccomType(2, QUEEN_ACCOM_TYPE, rateStartDateInStr, rateEndDateInStr,
                        valueOf(100), valueOf(50), valueOf(50),
                        valueOf(50), valueOf(50), valueOf(40), valueOf(38))
                .andQualifiedRatePlanDetailsIsInsertedForAccomType(2, KING_ACCOM_TYPE, rateStartDateInStr, rateEndDateInStr,
                        valueOf(100), valueOf(50), valueOf(50),
                        valueOf(50), valueOf(50), valueOf(40), valueOf(38))
                .andQualifiedRatePlanDataIsInserted(3, LV0, rateStartDateInStr, rateEndDateInStr, 3)
                .andQualifiedRatePlanDetailsIsInsertedForAccomType(3, QUEEN_ACCOM_TYPE, rateStartDateInStr, rateEndDateInStr,
                        valueOf(100), valueOf(50), valueOf(50),
                        valueOf(50), valueOf(50), valueOf(40), valueOf(38))
                .andQualifiedRatePlanDetailsIsInsertedForAccomType(3, KING_ACCOM_TYPE, rateStartDateInStr, rateEndDateInStr,
                        valueOf(100), valueOf(50), valueOf(50),
                        valueOf(50), valueOf(50), valueOf(40), valueOf(38))
                .andDecisionRestrictHighestBARIsSetFor(QUEEN_ACCOM_TYPE, 1)
                .andDecisionRestrictHighestBARIsSetFor(KING_ACCOM_TYPE, 3)

                .thenFPLOSDecisionsAreGenerated(Arrays.asList(1, 2, 3), false, true)

                .assertBarByLOSFPLOSDecisionsWhenHighestBARIsRestricted();
    }

    @Test
    public void verifyGeneratedFPLOSWhenHighestBARIsRestrictedAndSRPFPLOSAtTotalLevelIsFalse_LimitTotalSRPIsTrue_LimitTotalMasterClass_DiffRC() {
        when(virtualPropertyMappingService.getPhysicalPropertyMasterClassMapForVirtualProperty(PROPERTY_ID)).thenReturn(Map.of("ABC", NON_MASTER_ACCOM_CLASS_DLX, "DEF", MASTER_ACCOM_CLASS_ID));
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.GENERATE_LIMIT_TOTAL_SRP_RATES)).thenReturn(true);
        when(pacmanConfigParamsService.getBooleanParameterValue(HIGHEST_BAR_RESTRICTED_ENABLED)).thenReturn(true);
        whenConfigParamSetToReturn(IPConfigParamName.BAR_BAR_DECISION.value(), Constants.BAR_DECISION_VALUE_LOS)
                .whenConfigParamSetToReturn(IntegrationConfigParamName.SRP_FPLOS_AT_TOTAL_LEVEL.value(), Constants.FALSE)
                .whenConfigParamSetToReturn(CORE_PROPERTY_QUALIFIED_FPLOSMAX_LOS.value(), "7")
                .andRatePlanStartAndEndDateRangeAreSet(dateService.getCaughtUpDate(), 7)
                .andAccomActivityDataCleanedUp()
                .andAccomActivityDataInsertedForAccomType(QUEEN_ACCOM_TYPE, startDate, 7, 40, 1)
                .andAccomActivityDataInsertedForAccomType(MASTER_ACCOM_TYPE_DDLX, startDate, 7, 40, 1)
                .andDecisionLRVDataIsPreparedForAccomClass(NON_MASTER_ACCOM_CLASS_DLX, startDate, masterLRVDataList.size(), masterLRVDataList)
                .andDecisionLRVDataIsPreparedForAccomClass(MASTER_ACCOM_CLASS_ID, startDate, masterLRVDataList.size(), List.of(valueOf(1000), valueOf(1030), valueOf(1050), valueOf(1030), valueOf(1040),
                        valueOf(1050), valueOf(1060), valueOf(1090)))
                .andQualifiedRatePlanDataIsInserted(1, "DEF_QR1", rateStartDateInStr, rateEndDateInStr, 3)
                .andQualifiedRatePlanDetailsIsInsertedForAccomType(1, QUEEN_ACCOM_TYPE, rateStartDateInStr, rateEndDateInStr,
                        valueOf(100), valueOf(50), valueOf(50),
                        valueOf(50), valueOf(50), valueOf(40), valueOf(38))
                .andQualifiedRatePlanDetailsIsInsertedForAccomType(1, MASTER_ACCOM_TYPE_DDLX, rateStartDateInStr, rateEndDateInStr,
                        valueOf(100), valueOf(50), valueOf(50),
                        valueOf(50), valueOf(50), valueOf(40), valueOf(38))
                .andQualifiedRatePlanDataIsInserted(2, "ABC_QR2", rateStartDateInStr, rateEndDateInStr, 3)
                .andQualifiedRatePlanDetailsIsInsertedForAccomType(2, QUEEN_ACCOM_TYPE, rateStartDateInStr, rateEndDateInStr,
                        valueOf(100), valueOf(50), valueOf(50),
                        valueOf(50), valueOf(50), valueOf(40), valueOf(38))
                .andQualifiedRatePlanDetailsIsInsertedForAccomType(2, MASTER_ACCOM_TYPE_DDLX, rateStartDateInStr, rateEndDateInStr,
                        valueOf(100), valueOf(50), valueOf(50),
                        valueOf(50), valueOf(50), valueOf(40), valueOf(38))
                .andQualifiedRatePlanDataIsInserted(3, LV0, rateStartDateInStr, rateEndDateInStr, 3)
                .andQualifiedRatePlanDetailsIsInsertedForAccomType(3, QUEEN_ACCOM_TYPE, rateStartDateInStr, rateEndDateInStr,
                        valueOf(100), valueOf(50), valueOf(50),
                        valueOf(50), valueOf(50), valueOf(40), valueOf(38))
                .andQualifiedRatePlanDetailsIsInsertedForAccomType(3, MASTER_ACCOM_TYPE_DDLX, rateStartDateInStr, rateEndDateInStr,
                        valueOf(100), valueOf(50), valueOf(50),
                        valueOf(50), valueOf(50), valueOf(40), valueOf(38))
                .andDecisionRestrictHighestBARIsSetFor(QUEEN_ACCOM_TYPE, 1)
                .andDecisionRestrictHighestBARIsSetFor(MASTER_ACCOM_TYPE_DDLX, 3)

                .thenFPLOSDecisionsAreGenerated(Arrays.asList(1, 2, 3), false, true)

                .assertLimitTotalMasterClassFPLOS();
    }

    private void assertLimitTotalMasterClassFPLOS() {
        List<FPLOSDecisions> decisions = fplosDecisionService.getFPLOSDecisionData(startDate, endDate);
        assertEquals(6, decisions.size());
        List<FPLOSDecisions> fplosDecisions = decisions.stream().filter(d -> !StringUtils.equalsIgnoreCase(d.getAccomTypeName(), "DLX")).collect(Collectors.toList());
        fplosDecisions.forEach(decision -> assertEquals(getExpectedFPLOS(decision), decision.getFplos(), decision.getRateCodeName() + decision.getAccomTypeName()));
        decisions.removeAll(fplosDecisions);
        assertEquals(3, decisions.size());
        decisions.forEach(decision -> {
            assertEquals("DLX", decision.getAccomTypeName());
            assertEquals("NNNNNNN", decision.getFplos());
        });
    }

    @Test
    public void verifyGeneratedFPLOSWhenCPLRAAndHighestBARIsRestrictedAndSRPFPLOSAtTotalLevelIsFalse_LimitTotalSRPIsTrue() {
        System.setProperty("pacman.fplos.cp.lra.enabled", "false");
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.GENERATE_LIMIT_TOTAL_SRP_RATES)).thenReturn(true);
        createDataForCPLRAAndHighestBARRestrictedAndSRPFPLOSAtRT();
    }

    @Test
    public void verifyGeneratedFPLOSWhenCPLRAAndHighestBARIsRestrictedAndSRPFPLOSAtTotalLevelIsFalse_LimitTotalSRPIsTrueWhenSystemToggleOptimizeCPLRATrue() {
        System.setProperty("pacman.fplos.cp.lra.enabled", "true");
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.GENERATE_LIMIT_TOTAL_SRP_RATES)).thenReturn(true);
        createDataForCPLRAAndHighestBARRestrictedAndSRPFPLOSAtRT();
    }

    @Test
    public void verifyGeneratedFPLOSForRateOfTheDayAndRTLevel() {
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.IS_DERIVED_QUALIFIED_RATE_PLAN_ENABLED.value())).thenReturn(true);
        when(pacmanConfigParamsService.getBooleanParameterValue(HIGHEST_BAR_RESTRICTED_ENABLED)).thenReturn(false);
        whenConfigParamSetToReturn(IPConfigParamName.BAR_BAR_DECISION.value(), Constants.BAR_DECISION_VALUE_RATEOFDAY)
                .whenConfigParamSetToReturn(IntegrationConfigParamName.SRP_FPLOS_AT_TOTAL_LEVEL.value(), Constants.FALSE)
                .whenConfigParamSetToReturn(CORE_PROPERTY_QUALIFIED_FPLOSMAX_LOS.value(), "7")
                .andRatePlanStartAndEndDateRangeAreSet(dateService.getCaughtUpDate(), 7)
                .andAccomActivityDataCleanedUp()
                .andAccomActivityDataInsertedForAccomType(QUEEN_ACCOM_TYPE, startDate, 7, 40, 1)
                .andAccomActivityDataInsertedForAccomType(KING_ACCOM_TYPE, startDate, 7, 40, 1)
                .andDecisionLRVDataIsPreparedForAccomClass(NON_MASTER_ACCOM_CLASS_DLX, startDate, masterLRVDataList.size(), masterLRVDataList)
                .andQualifiedRatePlanDataIsInserted(1, SPECIAL_RATE_PLAN, rateStartDateInStr, rateEndDateInStr, 3)
                .andQualifiedRatePlanDetailsIsInsertedForAccomType(1, QUEEN_ACCOM_TYPE, rateStartDateInStr, rateEndDateInStr,
                        valueOf(100), valueOf(50), valueOf(50),
                        valueOf(50), valueOf(50), valueOf(40), valueOf(38))
                .andQualifiedRatePlanDetailsIsInsertedForAccomType(1, KING_ACCOM_TYPE, rateStartDateInStr, rateEndDateInStr,
                        valueOf(100), valueOf(50), valueOf(50),
                        valueOf(50), valueOf(50), valueOf(40), valueOf(38))
                .andQualifiedRatePlanDataIsInserted(2, NON_SPECIAL_RATE_PLAN, rateStartDateInStr, rateEndDateInStr, 3)
                .andQualifiedRatePlanDetailsIsInsertedForAccomType(2, QUEEN_ACCOM_TYPE, rateStartDateInStr, rateEndDateInStr,
                        valueOf(100), valueOf(50), valueOf(50),
                        valueOf(50), valueOf(50), valueOf(40), valueOf(38))
                .andQualifiedRatePlanDetailsIsInsertedForAccomType(2, KING_ACCOM_TYPE, rateStartDateInStr, rateEndDateInStr,
                        valueOf(100), valueOf(50), valueOf(50),
                        valueOf(50), valueOf(50), valueOf(40), valueOf(38))
                .insertRateQualifiedLv0Data(RATE_PLAN_LV0, 10)
                .thenFPLOSDecisionsAreGenerated(Arrays.asList(1, 2, 10), false, false)
                .assertRTLevelFPLOSDecisions();
    }

    @Test
    public void verifyGeneratedFPLOSForRateOfTheDayAndRTLevel_ForCPAndDerivedQualifiedRatePlan() {
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.IS_DERIVED_QUALIFIED_RATE_PLAN_ENABLED.value())).thenReturn(true);
        when(pacmanConfigParamsService.getBooleanParameterValue(HIGHEST_BAR_RESTRICTED_ENABLED)).thenReturn(false);
        when(pacmanConfigParamsService.getBooleanParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED.value())).thenReturn(true);
        whenConfigParamSetToReturn(IPConfigParamName.BAR_BAR_DECISION.value(), Constants.BAR_DECISION_VALUE_RATEOFDAY);
        whenConfigParamSetToReturn(IntegrationConfigParamName.SRP_FPLOS_AT_TOTAL_LEVEL.value(), Constants.FALSE)
                .whenConfigParamSetToReturn(CORE_PROPERTY_QUALIFIED_FPLOSMAX_LOS.value(), "7")
                .andRatePlanStartAndEndDateRangeAreSet(dateService.getCaughtUpDate(), 7)
                .andAccomActivityDataCleanedUp()
                .andAccomActivityDataInsertedForAccomType(QUEEN_ACCOM_TYPE, startDate, 7, 40, 1)
                .andAccomActivityDataInsertedForAccomType(KING_ACCOM_TYPE, startDate, 7, 40, 1)
                .andDecisionLRVDataIsPreparedForAccomClass(NON_MASTER_ACCOM_CLASS_DLX, startDate, masterLRVDataList.size(), masterLRVDataList)
                .andQualifiedRatePlanDataIsInserted(1, SPECIAL_RATE_PLAN, rateStartDateInStr, rateEndDateInStr, 3)
                .andQualifiedRatePlanDetailsIsInsertedForAccomType(1, QUEEN_ACCOM_TYPE, rateStartDateInStr, rateEndDateInStr,
                        valueOf(100), valueOf(50), valueOf(50),
                        valueOf(50), valueOf(50), valueOf(40), valueOf(38))
                .andQualifiedRatePlanDetailsIsInsertedForAccomType(1, KING_ACCOM_TYPE, rateStartDateInStr, rateEndDateInStr,
                        valueOf(100), valueOf(50), valueOf(50),
                        valueOf(50), valueOf(50), valueOf(40), valueOf(38))
                .andQualifiedRatePlanDataIsInserted(2, NON_SPECIAL_RATE_PLAN, rateStartDateInStr, rateEndDateInStr, 3)
                .andQualifiedRatePlanDetailsIsInsertedForAccomType(2, QUEEN_ACCOM_TYPE, rateStartDateInStr, rateEndDateInStr,
                        valueOf(100), valueOf(50), valueOf(50),
                        valueOf(50), valueOf(50), valueOf(40), valueOf(38))
                .andQualifiedRatePlanDetailsIsInsertedForAccomType(2, KING_ACCOM_TYPE, rateStartDateInStr, rateEndDateInStr,
                        valueOf(100), valueOf(50), valueOf(50),
                        valueOf(50), valueOf(50), valueOf(40), valueOf(38))
                .insertRateQualifiedLv0Data(RATE_PLAN_LV0, 10)
                .thenFPLOSDecisionsAreGenerated(Arrays.asList(1, 2, 10), false, false)
                .assertRTLevelFPLOSDecisions();
    }

    @Test
    public void verifyGeneratedFPLOSForRateOfTheDayAndRTLevel_ForCPAndDerivedQualifiedRatePlan_WhenLeadSystemToggleIsFalse() {

        System.setProperty("pacman.recommendation.cp.fplos.lead.enabled", "false");
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.IS_DERIVED_QUALIFIED_RATE_PLAN_ENABLED.value())).thenReturn(true);
        when(pacmanConfigParamsService.getBooleanParameterValue(HIGHEST_BAR_RESTRICTED_ENABLED)).thenReturn(false);
        when(pacmanConfigParamsService.getBooleanParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED.value())).thenReturn(true);
        whenConfigParamSetToReturn(IPConfigParamName.BAR_BAR_DECISION.value(), Constants.BAR_DECISION_VALUE_RATEOFDAY);
        whenConfigParamSetToReturn(IntegrationConfigParamName.SRP_FPLOS_AT_TOTAL_LEVEL.value(), Constants.FALSE)
                .whenConfigParamSetToReturn(CORE_PROPERTY_QUALIFIED_FPLOSMAX_LOS.value(), "7")
                .andRatePlanStartAndEndDateRangeAreSet(dateService.getCaughtUpDate(), 7)
                .andAccomActivityDataCleanedUp()
                .andAccomActivityDataInsertedForAccomType(QUEEN_ACCOM_TYPE, startDate, 7, 40, 1)
                .andAccomActivityDataInsertedForAccomType(KING_ACCOM_TYPE, startDate, 7, 40, 1)
                .andDecisionLRVDataIsPreparedForAccomClass(NON_MASTER_ACCOM_CLASS_DLX, startDate, masterLRVDataList.size(), masterLRVDataList)
                .andQualifiedRatePlanDataIsInserted(1, SPECIAL_RATE_PLAN, rateStartDateInStr, rateEndDateInStr, 3)
                .andQualifiedRatePlanDetailsIsInsertedForAccomType(1, QUEEN_ACCOM_TYPE, rateStartDateInStr, rateEndDateInStr,
                        valueOf(100), valueOf(50), valueOf(50),
                        valueOf(50), valueOf(50), valueOf(40), valueOf(38))
                .andQualifiedRatePlanDetailsIsInsertedForAccomType(1, KING_ACCOM_TYPE, rateStartDateInStr, rateEndDateInStr,
                        valueOf(100), valueOf(50), valueOf(50),
                        valueOf(50), valueOf(50), valueOf(40), valueOf(38))
                .andQualifiedRatePlanDataIsInserted(2, NON_SPECIAL_RATE_PLAN, rateStartDateInStr, rateEndDateInStr, 3)
                .andQualifiedRatePlanDetailsIsInsertedForAccomType(2, QUEEN_ACCOM_TYPE, rateStartDateInStr, rateEndDateInStr,
                        valueOf(100), valueOf(50), valueOf(50),
                        valueOf(50), valueOf(50), valueOf(40), valueOf(38))
                .andQualifiedRatePlanDetailsIsInsertedForAccomType(2, KING_ACCOM_TYPE, rateStartDateInStr, rateEndDateInStr,
                        valueOf(100), valueOf(50), valueOf(50),
                        valueOf(50), valueOf(50), valueOf(40), valueOf(38))
                .insertRateQualifiedLv0Data(RATE_PLAN_LV0, 10)
                .thenFPLOSDecisionsAreGenerated(Arrays.asList(1, 2, 10), false, false)
                .assertRTLevelFPLOSDecisions();
    }

    private SRPBasedDecisionQualifiedFPLOSBatchUpdaterDataTest assertRTLevelFPLOSDecisions() {
        List<FPLOSDecisions> decisions = fplosDecisionService.getFPLOSDecisionData(startDate, endDate);
        assertEquals(4, decisions.size());
        assertEquals("YNYYYYY", decisions.get(0).getFplos());
        assertEquals("K", decisions.get(0).getAccomTypeName());
        assertEquals("QR1", decisions.get(0).getRateCodeName());

        assertEquals("YNYYYYY", decisions.get(1).getFplos());
        assertEquals("K", decisions.get(1).getAccomTypeName());
        assertEquals("QR2", decisions.get(1).getRateCodeName());

        assertEquals("YNYYYYY", decisions.get(2).getFplos());
        assertEquals("Q", decisions.get(2).getAccomTypeName());
        assertEquals("QR1", decisions.get(2).getRateCodeName());

        assertEquals("YNYYYYY", decisions.get(3).getFplos());
        assertEquals("Q", decisions.get(3).getAccomTypeName());
        assertEquals("QR2", decisions.get(3).getRateCodeName());

        return this;
    }

    private SRPBasedDecisionQualifiedFPLOSBatchUpdaterDataTest andDecisionRestrictHighestBARIsSetFor(int accomTypeId, int los) {
        String query = "INSERT INTO Decision_Restrict_Highest_Bar values(:accomClassId, :accomTypeId, " +
                ":los,:arrivalDate,:decisionId,:userId,:propertyId, getdate())";
        tenantCrudService().executeUpdateByNativeQuery(query,
                QueryParameter.with("accomClassId", NON_MASTER_ACCOM_CLASS_DLX)
                        .and("accomTypeId", accomTypeId)
                        .and("los", los)
                        .and("arrivalDate", CAUGHT_UP_DATE_JAN_01_FRI)
                        .and("decisionId", getDecision().getId())
                        .and("userId", 1)
                        .and("propertyId", PROPERTY_ID)
                        .parameters());
        return this;
    }

    private void assertBarByLOSFPLOSDecisionsWhenHighestBARIsRestricted() {
        List<FPLOSDecisions> decisions = fplosDecisionService.getFPLOSDecisionData(startDate, endDate);
        assertEquals(6, decisions.size());
        decisions.forEach(decision -> assertEquals(getExpectedFPLOS(decision), decision.getFplos(), decision.getRateCodeName() + decision.getAccomTypeName()));
    }

    private String getExpectedFPLOS(FPLOSDecisions decision) {
        if (decision.getRateCodeName().equals(LV0)) {
            return decision.getAccomTypeName().equalsIgnoreCase("Q") ? "NNYYYYY" : "YNNYYYY";
        }
        return "YNYYYYY";
    }

    private void assertFPLOSPattern(int expected) {
        List<FPLOSDecisions> decisions = fplosDecisionService.getFPLOSDecisionData(startDate, endDate);
        assertEquals(expected, decisions.size());
        assertEquals("YYYNYYN", decisions.get(0).getFplos());
    }

    @Test
    public void verifyGeneratedFPLOSWhenHighestBARIsNotRestrictedAndSRPFPLOSAtTotalLevelIsFalse() {
        when(pacmanConfigParamsService.getBooleanParameterValue(HIGHEST_BAR_RESTRICTED_ENABLED)).thenReturn(false);
        whenConfigParamSetToReturn(IPConfigParamName.BAR_BAR_DECISION.value(), Constants.BAR_DECISION_VALUE_LOS)
                .whenConfigParamSetToReturn(IntegrationConfigParamName.SRP_FPLOS_AT_TOTAL_LEVEL.value(), Constants.FALSE)
                .whenConfigParamSetToReturn(CORE_PROPERTY_QUALIFIED_FPLOSMAX_LOS.value(), "7")
                .andRatePlanStartAndEndDateRangeAreSet(dateService.getCaughtUpDate(), 7)
                .andAccomActivityDataCleanedUp()
                .andAccomActivityDataInsertedForAccomType(QUEEN_ACCOM_TYPE, startDate, 7, 40, 1)
                .andAccomActivityDataInsertedForAccomType(KING_ACCOM_TYPE, startDate, 7, 40, 1)
                .andDecisionLRVDataIsPreparedForAccomClass(NON_MASTER_ACCOM_CLASS_DLX, startDate, masterLRVDataList.size(), masterLRVDataList)
                .andQualifiedRatePlanDataIsInserted(1, SPECIAL_RATE_PLAN, rateStartDateInStr, rateEndDateInStr, 3)
                .andQualifiedRatePlanDetailsIsInsertedForAccomType(1, QUEEN_ACCOM_TYPE, rateStartDateInStr, rateEndDateInStr,
                        valueOf(100), valueOf(50), valueOf(50),
                        valueOf(50), valueOf(50), valueOf(40), valueOf(38))
                .andQualifiedRatePlanDetailsIsInsertedForAccomType(1, KING_ACCOM_TYPE, rateStartDateInStr, rateEndDateInStr,
                        valueOf(100), valueOf(50), valueOf(50),
                        valueOf(50), valueOf(50), valueOf(40), valueOf(38))
                .andQualifiedRatePlanDataIsInserted(2, NON_SPECIAL_RATE_PLAN, rateStartDateInStr, rateEndDateInStr, 3)
                .andQualifiedRatePlanDetailsIsInsertedForAccomType(2, QUEEN_ACCOM_TYPE, rateStartDateInStr, rateEndDateInStr,
                        valueOf(100), valueOf(50), valueOf(50),
                        valueOf(50), valueOf(50), valueOf(40), valueOf(38))
                .andQualifiedRatePlanDetailsIsInsertedForAccomType(2, KING_ACCOM_TYPE, rateStartDateInStr, rateEndDateInStr,
                        valueOf(100), valueOf(50), valueOf(50),
                        valueOf(50), valueOf(50), valueOf(40), valueOf(38))
                .insertRateQualifiedLv0Data(RATE_PLAN_LV0, 10)

                .thenFPLOSDecisionsAreGenerated(Arrays.asList(1, 2), false, false)

                .assertBabByLOSFPLOSDecisions();
    }

    @Test
    @Disabled
    public void shouldGenerateFPLOS_BySRP_AtTotalLevel_WithDerivedRatesForMasterAndNonMasterRates() {
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.IS_DERIVED_QUALIFIED_RATE_PLAN_ENABLED.value())).thenReturn(true);
        when(pacmanConfigParamsService.getBooleanParameterValue(IntegrationConfigParamName.SRP_FPLOS_AT_TOTAL_LEVEL)).thenReturn(true);

        whenConfigParamSetToReturn(IPConfigParamName.BAR_BAR_DECISION.value(), Constants.BAR_DECISION_VALUE_RATEOFDAY)

                .andGivenAccomTypeAddedToMasterAccomClass(MASTER_ACCOM_CLASS_ID, MASTER_ACCOM_TYPE_ID_KING)

                .andCaughtUpDateUpdatedTo(CAUGHT_UP_DATE_3_JAN_SUN)
                .andRatePlanStartAndEndDateRangeAreSet(dateService.getCaughtUpDate(), 9)

                .andDecisionLRVDataIsPreparedForAccomClass(MASTER_ACCOM_CLASS_ID, startDate, masterLRVDataList.size(), masterLRVDataList)

                .andAccomActivityDataCleanedUp()
                .andAccomActivityDataInsertedForAccomType(MASTER_ACCOM_TYPE_DDLX, startDate, 10, 40, 10)
                .andAccomActivityDataInsertedForAccomType(MASTER_ACCOM_TYPE_ID_KING, startDate, 10, 100, 15)

                .andQualifiedRatePlanDataIsInserted(1, QR_MASTER_NON_DERIVED, rateStartDateInStr, rateEndDateInStr, 3)
                .andQualifiedRatePlanDetailsIsInsertedForAccomType(1, MASTER_ACCOM_TYPE_DDLX, rateStartDateInStr, rateEndDateInStr,
                        valueOf(55), valueOf(40), valueOf(30), valueOf(40),
                        valueOf(0), valueOf(0), valueOf(60))
                .andQualifiedRatePlanDetailsIsInsertedForAccomType(1, MASTER_ACCOM_TYPE_ID_KING, rateStartDateInStr, rateEndDateInStr,
                        valueOf(100), valueOf(0), valueOf(45), valueOf(20),
                        valueOf(10), valueOf(0), valueOf(0))

                .andQualifiedRatePlanDataIsInserted(2, "QR2_NON_MASTER_DERIVED_VALUE", rateStartDateInStr, rateEndDateInStr, 1)
                .andQualifiedRatePlanDetailsIsInsertedForAccomType(2, NON_MASTER_ACCOM_TYPE_DBL, rateStartDateInStr, rateEndDateInStr,
                        valueOf(-12), valueOf(15), valueOf(20),
                        valueOf(-7.5), valueOf(-27), valueOf(-30), valueOf(-35.50))
                .andQualifiedRatePlanDetailsIsInsertedForAccomType(2, NON_MASTER_ACCOM_TYPE_Q, rateStartDateInStr, rateEndDateInStr,
                        valueOf(-15), valueOf(5), valueOf(10),
                        valueOf(0), valueOf(-15), valueOf(-20), valueOf(-30.50))

                .andQualifiedRatePlanDataIsInserted(3, "QR3_NON_MASTER_DERIVED_PERCENTAGE", rateStartDateInStr, rateEndDateInStr, 2)
                .andQualifiedRatePlanDetailsIsInsertedForAccomType(3, NON_MASTER_ACCOM_TYPE_Q, rateStartDateInStr, rateEndDateInStr,
                        valueOf(-15), valueOf(-18), valueOf(-25),
                        valueOf(-23.5), valueOf(10), valueOf(-7.5), valueOf(-25.8))
                .andQualifiedRatePlanDetailsIsInsertedForAccomType(3, NON_MASTER_ACCOM_TYPE_DBL, rateStartDateInStr, rateEndDateInStr,
                        valueOf(-99), valueOf(-95), valueOf(-91.5),
                        valueOf(-88.88), valueOf(-83), valueOf(-80), valueOf(-75.1))
                .andQualifiedRatePlanDataIsInserted(4, RATE_LEVEL_0, rateStartDateInStr, rateEndDateInStr, 2)

                .andUnqualifiedRatePlanDataIsInserted(1, "UQR_NON_MASTER_CLASS", rateStartDateInStr, rateEndDateInStr)
                .andUnqualifiedRatePlanDetailsIsInseretd(1, NON_MASTER_ACCOM_TYPE_DBL, rateStartDateInStr, rateEndDateInStr, UNQUALIFIED_RATES_DATA)
                .andUnqualifiedRatePlanDetailsIsInseretd(1, NON_MASTER_ACCOM_TYPE_Q, rateStartDateInStr, rateEndDateInStr, UNQUALIFIED_RATES_DATA)

                .prepareDecisionBarOutputData(1, NON_MASTER_ACCOM_CLASS_ID, startDate, endDate)
                .thenFPLOSDecisionsAreGenerated(Arrays.asList(1, 2, 3), true, false)
                .assertGeneratedFPLOSForDerivedRates();

    }

    @Test
    public void testDeriveFPLOSBySRPAtTotalLevelUsingDOWRatesAvailableForMasterClassOnly() {
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.PROFIT_OPTIMIZATION_ENABLED)).thenReturn(false);
        whenConfigParamSetToReturn(IntegrationConfigParamName.SRP_FPLOS_AT_TOTAL_LEVEL.value(), TRUE)
                .whenConfigParamSetToReturn(IPConfigParamName.BAR_BAR_DECISION.value(), Constants.BAR_DECISION_VALUE_LOS)

                .andGivenAccomTypeAddedToMasterAccomClass(MASTER_ACCOM_CLASS_ID, MASTER_ACCOM_TYPE_ID_KING)
                .andRatePlanStartAndEndDateRangeAreSet(dateService.getCaughtUpDate(), 9)

                .andDecisionLRVDataIsPreparedForAccomClass(MASTER_ACCOM_CLASS_ID, startDate, masterLRVDataList.size(), masterLRVDataList)

                .andAccomActivityDataCleanedUp()
                .andAccomActivityDataInsertedForAccomType(MASTER_ACCOM_TYPE_DDLX, startDate, 10, 40, 10)
                .andAccomActivityDataInsertedForAccomType(MASTER_ACCOM_TYPE_ID_KING, startDate, 10, 100, 15)

                .andQualifiedRatePlanDataIsInserted(1, QR_MASTER_NON_DERIVED, rateStartDateInStr, rateEndDateInStr, 3)
                .andQualifiedRatePlanDetailsIsInsertedForAccomType(1, MASTER_ACCOM_TYPE_DDLX, rateStartDateInStr, rateEndDateInStr,
                        valueOf(100), valueOf(40), valueOf(100), valueOf(50),
                        valueOf(50), valueOf(100), valueOf(60))
                .andQualifiedRatePlanDetailsIsInsertedForAccomType(1, MASTER_ACCOM_TYPE_ID_KING, rateStartDateInStr, rateEndDateInStr,
                        valueOf(50), valueOf(50), valueOf(45), valueOf(20),
                        valueOf(10), valueOf(10), valueOf(0))
                .insertRateQualifiedLv0Data(RATE_PLAN_LV0, 10)
                .thenFPLOSDecisionsAreGenerated(Arrays.asList(1, 10), true, false)
                .assertFPLOSTOtalLevelforMasterClass();
    }


    @Test
    public void testDeriveFPLOSBySRPAtTotalLevelUsingDOWRatesAvailableForMasterClassOnly_Tars() {
        when(pacmanConfigParamsService.getParameterValue(MIN_MAX_LOS_BY_RATE_CODE_UPLOADTYPE, TARS)).thenReturn(FULL);
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.PROFIT_OPTIMIZATION_ENABLED)).thenReturn(false);
        whenConfigParamSetToReturn(IPConfigParamName.BAR_BAR_DECISION.value(), Constants.BAR_DECISION_VALUE_LOS)
                .whenConfigParamSetToReturn(IntegrationConfigParamName.SRP_FPLOS_AT_TOTAL_LEVEL.value(), TRUE)

                .andGivenAccomTypeAddedToMasterAccomClass(MASTER_ACCOM_CLASS_ID, MASTER_ACCOM_TYPE_ID_KING)

                .andRatePlanStartAndEndDateRangeAreSet(dateService.getCaughtUpDate(), 9)

                .andDecisionLRVDataIsPreparedForAccomClass(MASTER_ACCOM_CLASS_ID, startDate, masterLRVDataList.size(), masterLRVDataList)

                .andAccomActivityDataCleanedUp()
                .andAccomActivityDataInsertedForAccomType(MASTER_ACCOM_TYPE_ID_KING, startDate, 10, 100, 15)
                .andAccomActivityDataInsertedForAccomType(MASTER_ACCOM_TYPE_DDLX, startDate, 10, 40, 10)

                .andQualifiedRatePlanDataIsInserted(1, QR_MASTER_NON_DERIVED, rateStartDateInStr, rateEndDateInStr, 3)
                .andQualifiedRatePlanDetailsIsInsertedForAccomType(1, MASTER_ACCOM_TYPE_DDLX, rateStartDateInStr, rateEndDateInStr,
                        valueOf(100), valueOf(40), valueOf(100), valueOf(50),
                        valueOf(50), valueOf(100), valueOf(60))
                .andQualifiedRatePlanDetailsIsInsertedForAccomType(1, MASTER_ACCOM_TYPE_ID_KING, rateStartDateInStr, rateEndDateInStr,
                        valueOf(50), valueOf(50), valueOf(45), valueOf(20),
                        valueOf(10), valueOf(10), valueOf(0))
                .insertRateQualifiedLv0Data(RATE_PLAN_LV0, 10)

                .thenFPLOSDecisionsAreGenerated(Arrays.asList(1, 10), true, false)
                .assertFPLOSAtTotalLevelForMasterClassTars();
    }


    @Test
    public void testFPLOSQualifiedDecisions_QualifiedFplosCalculationsBasedOnDowRates() {
        whenConfigParamSetToReturn(IPConfigParamName.BAR_BAR_DECISION.value(), Constants.BAR_DECISION_VALUE_LOS);
        whenConfigParamSetToReturn(IntegrationConfigParamName.CORE_PROPERTY_QUALIFIED_FPLOSMAX_LOS.value(), "7")

                .andRatePlanStartAndEndDateRangeAreSet(dateService.getCaughtUpDate(), 7)

                .andAccomActivityDataCleanedUp()
                .andAccomActivityDataInsertedForAccomType(QUEEN_ACCOM_TYPE, startDate, 7, 40, 1)
                .andAccomActivityDataInsertedForAccomType(KING_ACCOM_TYPE, startDate, 7, 40, 1)
                .andDecisionLRVDataIsPreparedForAccomClass(NON_MASTER_ACCOM_CLASS_DLX, startDate, masterLRVDataList.size(), masterLRVDataList)

                .andQualifiedRatePlanDataIsInserted(1, SPECIAL_RATE_PLAN, rateStartDateInStr, rateEndDateInStr, 3)
                .andQualifiedRatePlanDetailsIsInsertedForAccomType(1, QUEEN_ACCOM_TYPE, rateStartDateInStr, rateEndDateInStr,
                        valueOf(100), valueOf(50), valueOf(50),
                        valueOf(50), valueOf(50), valueOf(40), valueOf(38))
                .andQualifiedRatePlanDetailsIsInsertedForAccomType(1, KING_ACCOM_TYPE, rateStartDateInStr, rateEndDateInStr,
                        valueOf(100), valueOf(50), valueOf(50),
                        valueOf(50), valueOf(50), valueOf(40), valueOf(38))

                .andQualifiedRatePlanDataIsInserted(2, NON_SPECIAL_RATE_PLAN, rateStartDateInStr, rateEndDateInStr, 3)
                .andQualifiedRatePlanDetailsIsInsertedForAccomType(2, QUEEN_ACCOM_TYPE, rateStartDateInStr, rateEndDateInStr,
                        valueOf(100), valueOf(50), valueOf(50),
                        valueOf(50), valueOf(50), valueOf(40), valueOf(38))
                .andQualifiedRatePlanDetailsIsInsertedForAccomType(2, KING_ACCOM_TYPE, rateStartDateInStr, rateEndDateInStr,
                        valueOf(100), valueOf(50), valueOf(50),
                        valueOf(50), valueOf(50), valueOf(40), valueOf(38))
                .insertRateQualifiedLv0Data(RATE_PLAN_LV0, 10)

                .thenFPLOSDecisionsAreGenerated(Arrays.asList(1, 2), false, false)
                .assertBabByLOSFPLOSDecisions();
    }


    @Test
    public void testFPLOSQualifiedDecisions_resumeFlow() {
        whenConfigParamSetToReturn(IPConfigParamName.BAR_BAR_DECISION.value(), Constants.BAR_DECISION_VALUE_LOS);
        whenConfigParamSetToReturn(IntegrationConfigParamName.CORE_PROPERTY_QUALIFIED_FPLOSMAX_LOS.value(), "7")

                .andRatePlanStartAndEndDateRangeAreSet(dateService.getOptimizationWindowStartDate(), 7)

                .andAccomActivityDataCleanedUp()
                .andAccomActivityDataInsertedForAccomType(QUEEN_ACCOM_TYPE, startDate, 7, 40, 1)
                .andAccomActivityDataInsertedForAccomType(KING_ACCOM_TYPE, startDate, 7, 40, 1)
                .andDecisionLRVDataIsPreparedForAccomClass(NON_MASTER_ACCOM_CLASS_DLX, startDate, masterLRVDataList.size(), masterLRVDataList)

                .andQualifiedRatePlanDataIsInserted(1, SPECIAL_RATE_PLAN, rateStartDateInStr, rateEndDateInStr, 3)
                .andQualifiedRatePlanDetailsIsInsertedForAccomType(1, QUEEN_ACCOM_TYPE, rateStartDateInStr, rateEndDateInStr,
                        valueOf(100), valueOf(50), valueOf(50),
                        valueOf(50), valueOf(50), valueOf(40), valueOf(38))
                .andQualifiedRatePlanDetailsIsInsertedForAccomType(1, KING_ACCOM_TYPE, rateStartDateInStr, rateEndDateInStr,
                        valueOf(100), valueOf(50), valueOf(50),
                        valueOf(50), valueOf(50), valueOf(40), valueOf(38))

                .andQualifiedRatePlanDataIsInserted(2, NON_SPECIAL_RATE_PLAN, rateStartDateInStr, rateEndDateInStr, 3)
                .andQualifiedRatePlanDetailsIsInsertedForAccomType(2, QUEEN_ACCOM_TYPE, rateStartDateInStr, rateEndDateInStr,
                        valueOf(100), valueOf(50), valueOf(50),
                        valueOf(50), valueOf(50), valueOf(40), valueOf(38))
                .andQualifiedRatePlanDetailsIsInsertedForAccomType(2, KING_ACCOM_TYPE, rateStartDateInStr, rateEndDateInStr,
                        valueOf(100), valueOf(50), valueOf(50),
                        valueOf(50), valueOf(50), valueOf(40), valueOf(38))
                .insertRateQualifiedLv0Data(RATE_PLAN_LV0, 10);
        FPLOSQualifiedChunkDetail chunkDetails = createFPLOSDecisionsForFirstChunk(Arrays.asList(1, 2), false, false);
        assertNotNull(chunkDetails);
        assertNotNull(chunkDetails.getDecision());
        createFPLOSDecisionsForFirstChunkAfterResume(Arrays.asList(1, 2), chunkDetails).assertBabByLOSFPLOSDecisions();
    }

    private SRPBasedDecisionQualifiedFPLOSBatchUpdaterDataTest createFPLOSDecisionsForFirstChunkAfterResume(List<Integer> rateQualifiedIds, FPLOSQualifiedChunkDetail chunkDetails) {
        chunkDetails.setRateQualifiedIds(rateQualifiedIds);
        chunkDetails.setLastLoop(false);
        fplosRecomendationService.processChunk(startDate, endDate, chunkDetails);
        return this;
    }

    @Test
    public void testFPLOSQualifiedDecisions_QualifiedFplosCalculationsBasedOnDowRates_singleRateQualified() {
        whenConfigParamSetToReturn(IPConfigParamName.BAR_BAR_DECISION.value(), Constants.BAR_DECISION_VALUE_LOS);
        whenConfigParamSetToReturn(IntegrationConfigParamName.CORE_PROPERTY_QUALIFIED_FPLOSMAX_LOS.value(), "7")

                .andRatePlanStartAndEndDateRangeAreSet(dateService.getCaughtUpDate(), 7)

                .andAccomActivityDataCleanedUp()
                .andAccomActivityDataInsertedForAccomType(QUEEN_ACCOM_TYPE, startDate, 7, 40, 1)
                .andAccomActivityDataInsertedForAccomType(KING_ACCOM_TYPE, startDate, 7, 40, 1)
                .andDecisionLRVDataIsPreparedForAccomClass(NON_MASTER_ACCOM_CLASS_DLX, startDate, masterLRVDataList.size(), masterLRVDataList)

                .andQualifiedRatePlanDataIsInserted(1, SPECIAL_RATE_PLAN, rateStartDateInStr, rateEndDateInStr, 3)
                .andQualifiedRatePlanDetailsIsInsertedForAccomType(1, QUEEN_ACCOM_TYPE, rateStartDateInStr, rateEndDateInStr,
                        valueOf(100), valueOf(50), valueOf(50),
                        valueOf(50), valueOf(50), valueOf(40), valueOf(38))
                .andQualifiedRatePlanDetailsIsInsertedForAccomType(1, KING_ACCOM_TYPE, rateStartDateInStr, rateEndDateInStr,
                        valueOf(100), valueOf(50), valueOf(50),
                        valueOf(50), valueOf(50), valueOf(40), valueOf(38))

                .andQualifiedRatePlanDataIsInserted(2, NON_SPECIAL_RATE_PLAN, rateStartDateInStr, rateEndDateInStr, 3)
                .andQualifiedRatePlanDetailsIsInsertedForAccomType(2, QUEEN_ACCOM_TYPE, rateStartDateInStr, rateEndDateInStr,
                        valueOf(100), valueOf(50), valueOf(50),
                        valueOf(50), valueOf(50), valueOf(40), valueOf(38))
                .andQualifiedRatePlanDetailsIsInsertedForAccomType(2, KING_ACCOM_TYPE, rateStartDateInStr, rateEndDateInStr,
                        valueOf(100), valueOf(50), valueOf(50),
                        valueOf(50), valueOf(50), valueOf(40), valueOf(38))
                .thenFPLOSDecisionsAreGenerated(Arrays.asList(1), false, false)
                .assertBabByLOSFPLOSDecisionsRateQualified();
    }

    @Test
    public void testFPLOSQualifiedDecisions_QualifiedFplosCalculations_LimitTotal() {
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.GENERATE_LIMIT_TOTAL_SRP_RATES)).thenReturn(true);
        whenConfigParamSetToReturn(IPConfigParamName.BAR_BAR_DECISION.value(), Constants.BAR_DECISION_VALUE_LOS);
        whenConfigParamSetToReturn(IntegrationConfigParamName.CORE_PROPERTY_QUALIFIED_FPLOSMAX_LOS.value(), "7")
                .whenConfigParamSetToReturn(IntegrationConfigParamName.SRP_FPLOS_AT_TOTAL_LEVEL.value(), FALSE)

                .andRatePlanStartAndEndDateRangeAreSet(dateService.getCaughtUpDate(), 7)

                .andAccomActivityDataCleanedUp()
                .andAccomActivityDataInsertedForAccomType(QUEEN_ACCOM_TYPE, startDate, 7, 40, 1)
                .andAccomActivityDataInsertedForAccomType(KING_ACCOM_TYPE, startDate, 7, 40, 1)
                .andAccomActivityDataInsertedForAccomType(MASTER_ACCOM_TYPE_DDLX, startDate, 7, 40, 1)
                .andDecisionLRVDataIsPreparedForAccomClass(NON_MASTER_ACCOM_CLASS_DLX, startDate, masterLRVDataList.size(), masterLRVDataList)
                .andDecisionLRVDataIsPreparedForAccomClass(MASTER_ACCOM_CLASS_ID, startDate, masterLRVDataList.size(), masterLRVDataList)

                .andQualifiedRatePlanDataIsInserted(1, QR_MASTER_NON_DERIVED, rateStartDateInStr, rateEndDateInStr, 3)
                .andQualifiedRatePlanDetailsIsInsertedForAccomType(1, QUEEN_ACCOM_TYPE, rateStartDateInStr, rateEndDateInStr,
                        valueOf(100), valueOf(50), valueOf(50),
                        valueOf(50), valueOf(50), valueOf(40), valueOf(38))
                .andQualifiedRatePlanDetailsIsInsertedForAccomType(1, KING_ACCOM_TYPE, rateStartDateInStr, rateEndDateInStr,
                        valueOf(100), valueOf(50), valueOf(50),
                        valueOf(50), valueOf(50), valueOf(40), valueOf(38))
                .andQualifiedRatePlanDetailsIsInsertedForAccomType(1, MASTER_ACCOM_TYPE_DDLX, rateStartDateInStr, rateEndDateInStr,
                        valueOf(100), valueOf(50), valueOf(50),
                        valueOf(50), valueOf(50), valueOf(40), valueOf(38))

                .andQualifiedRatePlanDataIsInserted(2, NON_SPECIAL_RATE_PLAN, rateStartDateInStr, rateEndDateInStr, 3)
                .andQualifiedRatePlanDetailsIsInsertedForAccomType(2, QUEEN_ACCOM_TYPE, rateStartDateInStr, rateEndDateInStr,
                        valueOf(100), valueOf(50), valueOf(50),
                        valueOf(50), valueOf(50), valueOf(40), valueOf(38))
                .andQualifiedRatePlanDetailsIsInsertedForAccomType(2, KING_ACCOM_TYPE, rateStartDateInStr, rateEndDateInStr,
                        valueOf(100), valueOf(50), valueOf(50),
                        valueOf(50), valueOf(50), valueOf(40), valueOf(38))
                .andQualifiedRatePlanDetailsIsInsertedForAccomType(2, MASTER_ACCOM_TYPE_DDLX, rateStartDateInStr, rateEndDateInStr,
                        valueOf(100), valueOf(50), valueOf(50),
                        valueOf(50), valueOf(50), valueOf(40), valueOf(38))
                .andSetRatePlanAtTotalLevel(2)
                .insertRateQualifiedLv0Data(RATE_PLAN_LV0, 10)
                .thenFPLOSDecisionsAreGenerated(Arrays.asList(1, 2), false, true)
                .assertFPLOSWithLimitTotal();
    }

    @Test
    public void testFPLOSQualifiedDecisions_QualifiedFplosCalculations_LimitTotal_singleRateQualified() {
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.GENERATE_LIMIT_TOTAL_SRP_RATES)).thenReturn(true);
        whenConfigParamSetToReturn(IPConfigParamName.BAR_BAR_DECISION.value(), Constants.BAR_DECISION_VALUE_LOS);
        whenConfigParamSetToReturn(IntegrationConfigParamName.CORE_PROPERTY_QUALIFIED_FPLOSMAX_LOS.value(), "7")
                .whenConfigParamSetToReturn(IntegrationConfigParamName.SRP_FPLOS_AT_TOTAL_LEVEL.value(), FALSE)

                .andRatePlanStartAndEndDateRangeAreSet(dateService.getCaughtUpDate(), 7)

                .andAccomActivityDataCleanedUp()
                .andAccomActivityDataInsertedForAccomType(QUEEN_ACCOM_TYPE, startDate, 7, 40, 1)
                .andAccomActivityDataInsertedForAccomType(KING_ACCOM_TYPE, startDate, 7, 40, 1)
                .andAccomActivityDataInsertedForAccomType(MASTER_ACCOM_TYPE_DDLX, startDate, 7, 40, 1)
                .andDecisionLRVDataIsPreparedForAccomClass(NON_MASTER_ACCOM_CLASS_DLX, startDate, masterLRVDataList.size(), masterLRVDataList)
                .andDecisionLRVDataIsPreparedForAccomClass(MASTER_ACCOM_CLASS_ID, startDate, masterLRVDataList.size(), masterLRVDataList)

                .andQualifiedRatePlanDataIsInserted(1, QR_MASTER_NON_DERIVED, rateStartDateInStr, rateEndDateInStr, 3)
                .andQualifiedRatePlanDetailsIsInsertedForAccomType(1, QUEEN_ACCOM_TYPE, rateStartDateInStr, rateEndDateInStr,
                        valueOf(100), valueOf(50), valueOf(50),
                        valueOf(50), valueOf(50), valueOf(40), valueOf(38))
                .andQualifiedRatePlanDetailsIsInsertedForAccomType(1, KING_ACCOM_TYPE, rateStartDateInStr, rateEndDateInStr,
                        valueOf(100), valueOf(50), valueOf(50),
                        valueOf(50), valueOf(50), valueOf(40), valueOf(38))
                .andQualifiedRatePlanDetailsIsInsertedForAccomType(1, MASTER_ACCOM_TYPE_DDLX, rateStartDateInStr, rateEndDateInStr,
                        valueOf(100), valueOf(50), valueOf(50),
                        valueOf(50), valueOf(50), valueOf(40), valueOf(38))

                .andQualifiedRatePlanDataIsInserted(2, NON_SPECIAL_RATE_PLAN, rateStartDateInStr, rateEndDateInStr, 3)
                .andQualifiedRatePlanDetailsIsInsertedForAccomType(2, QUEEN_ACCOM_TYPE, rateStartDateInStr, rateEndDateInStr,
                        valueOf(100), valueOf(50), valueOf(50),
                        valueOf(50), valueOf(50), valueOf(40), valueOf(38))
                .andQualifiedRatePlanDetailsIsInsertedForAccomType(2, KING_ACCOM_TYPE, rateStartDateInStr, rateEndDateInStr,
                        valueOf(100), valueOf(50), valueOf(50),
                        valueOf(50), valueOf(50), valueOf(40), valueOf(38))
                .andQualifiedRatePlanDetailsIsInsertedForAccomType(2, MASTER_ACCOM_TYPE_DDLX, rateStartDateInStr, rateEndDateInStr,
                        valueOf(100), valueOf(50), valueOf(50),
                        valueOf(50), valueOf(50), valueOf(40), valueOf(38))
                .andSetRatePlanAtTotalLevel(2)
                .insertRateQualifiedLv0Data(RATE_PLAN_LV0, 10)
                .thenFPLOSDecisionsAreGenerated(Arrays.asList(1), false, true)
                .assertFPLOSWithLimitTotalRateQualified();
    }

    @Test
    public void testFPLOSQualifiedDecisions_QualifiedFplosCalculations_LimitTotal_noChangeInDecisions() {
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.GENERATE_LIMIT_TOTAL_SRP_RATES)).thenReturn(true);
        whenConfigParamSetToReturn(IPConfigParamName.BAR_BAR_DECISION.value(), Constants.BAR_DECISION_VALUE_LOS);
        whenConfigParamSetToReturn(IntegrationConfigParamName.CORE_PROPERTY_QUALIFIED_FPLOSMAX_LOS.value(), "7")
                .whenConfigParamSetToReturn(IntegrationConfigParamName.SRP_FPLOS_AT_TOTAL_LEVEL.value(), FALSE)

                .andRatePlanStartAndEndDateRangeAreSet(dateService.getCaughtUpDate(), 7)

                .andAccomActivityDataCleanedUp()
                .andAccomActivityDataInsertedForAccomType(QUEEN_ACCOM_TYPE, startDate, 7, 40, 1)
                .andAccomActivityDataInsertedForAccomType(KING_ACCOM_TYPE, startDate, 7, 40, 1)
                .andAccomActivityDataInsertedForAccomType(MASTER_ACCOM_TYPE_DDLX, startDate, 7, 40, 1)
                .andDecisionLRVDataIsPreparedForAccomClass(NON_MASTER_ACCOM_CLASS_DLX, startDate, masterLRVDataList.size(), masterLRVDataList)
                .andDecisionLRVDataIsPreparedForAccomClass(MASTER_ACCOM_CLASS_ID, startDate, masterLRVDataList.size(), masterLRVDataList)

                .andQualifiedRatePlanDataIsInserted(1, QR_MASTER_NON_DERIVED, rateStartDateInStr, rateEndDateInStr, 3)
                .andQualifiedRatePlanDetailsIsInsertedForAccomType(1, QUEEN_ACCOM_TYPE, rateStartDateInStr, rateEndDateInStr,
                        valueOf(100), valueOf(50), valueOf(50),
                        valueOf(50), valueOf(50), valueOf(40), valueOf(38))
                .andQualifiedRatePlanDetailsIsInsertedForAccomType(1, KING_ACCOM_TYPE, rateStartDateInStr, rateEndDateInStr,
                        valueOf(100), valueOf(50), valueOf(50),
                        valueOf(50), valueOf(50), valueOf(40), valueOf(38))
                .andQualifiedRatePlanDetailsIsInsertedForAccomType(1, MASTER_ACCOM_TYPE_DDLX, rateStartDateInStr, rateEndDateInStr,
                        valueOf(100), valueOf(50), valueOf(50),
                        valueOf(50), valueOf(50), valueOf(40), valueOf(38))

                .andQualifiedRatePlanDataIsInserted(2, NON_SPECIAL_RATE_PLAN, rateStartDateInStr, rateEndDateInStr, 3)
                .andQualifiedRatePlanDetailsIsInsertedForAccomType(2, QUEEN_ACCOM_TYPE, rateStartDateInStr, rateEndDateInStr,
                        valueOf(100), valueOf(50), valueOf(50),
                        valueOf(50), valueOf(50), valueOf(40), valueOf(38))
                .andQualifiedRatePlanDetailsIsInsertedForAccomType(2, KING_ACCOM_TYPE, rateStartDateInStr, rateEndDateInStr,
                        valueOf(100), valueOf(50), valueOf(50),
                        valueOf(50), valueOf(50), valueOf(40), valueOf(38))
                .andQualifiedRatePlanDetailsIsInsertedForAccomType(2, MASTER_ACCOM_TYPE_DDLX, rateStartDateInStr, rateEndDateInStr,
                        valueOf(100), valueOf(50), valueOf(50),
                        valueOf(50), valueOf(50), valueOf(40), valueOf(38))
                .andSetRatePlanAtTotalLevel(2)
                .andWithExistingIdenticalDecisions()
                .insertRateQualifiedLv0Data(RATE_PLAN_LV0, 10)
                .thenFPLOSDecisionsAreGenerated(Arrays.asList(1, 2), false, true)
                .assertFPLOSWithLimitTotal();
    }

    @Test
    public void testFPLOSQualifiedDecisions_QualifiedFplosCalculations_LimitTotal_rateQualifiedChangedFromRoomTypeToTotal() {
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.GENERATE_LIMIT_TOTAL_SRP_RATES)).thenReturn(true);
        whenConfigParamSetToReturn(IPConfigParamName.BAR_BAR_DECISION.value(), Constants.BAR_DECISION_VALUE_LOS);
        whenConfigParamSetToReturn(IntegrationConfigParamName.CORE_PROPERTY_QUALIFIED_FPLOSMAX_LOS.value(), "7")
                .whenConfigParamSetToReturn(IntegrationConfigParamName.SRP_FPLOS_AT_TOTAL_LEVEL.value(), FALSE)

                .andRatePlanStartAndEndDateRangeAreSet(dateService.getCaughtUpDate(), 7)

                .andAccomActivityDataCleanedUp()
                .andAccomActivityDataInsertedForAccomType(QUEEN_ACCOM_TYPE, startDate, 7, 40, 1)
                .andAccomActivityDataInsertedForAccomType(KING_ACCOM_TYPE, startDate, 7, 40, 1)
                .andAccomActivityDataInsertedForAccomType(MASTER_ACCOM_TYPE_DDLX, startDate, 7, 40, 1)
                .andDecisionLRVDataIsPreparedForAccomClass(NON_MASTER_ACCOM_CLASS_DLX, startDate, masterLRVDataList.size(), masterLRVDataList)
                .andDecisionLRVDataIsPreparedForAccomClass(MASTER_ACCOM_CLASS_ID, startDate, masterLRVDataList.size(), masterLRVDataList)

                .andQualifiedRatePlanDataIsInserted(1, QR_MASTER_NON_DERIVED, rateStartDateInStr, rateEndDateInStr, 3)
                .andQualifiedRatePlanDetailsIsInsertedForAccomType(1, QUEEN_ACCOM_TYPE, rateStartDateInStr, rateEndDateInStr,
                        valueOf(100), valueOf(50), valueOf(50),
                        valueOf(50), valueOf(50), valueOf(40), valueOf(38))
                .andQualifiedRatePlanDetailsIsInsertedForAccomType(1, KING_ACCOM_TYPE, rateStartDateInStr, rateEndDateInStr,
                        valueOf(100), valueOf(50), valueOf(50),
                        valueOf(50), valueOf(50), valueOf(40), valueOf(38))
                .andQualifiedRatePlanDetailsIsInsertedForAccomType(1, MASTER_ACCOM_TYPE_DDLX, rateStartDateInStr, rateEndDateInStr,
                        valueOf(100), valueOf(50), valueOf(50),
                        valueOf(50), valueOf(50), valueOf(40), valueOf(38))

                .andQualifiedRatePlanDataIsInserted(2, NON_SPECIAL_RATE_PLAN, rateStartDateInStr, rateEndDateInStr, 3)
                .andQualifiedRatePlanDetailsIsInsertedForAccomType(2, QUEEN_ACCOM_TYPE, rateStartDateInStr, rateEndDateInStr,
                        valueOf(100), valueOf(50), valueOf(50),
                        valueOf(50), valueOf(50), valueOf(40), valueOf(38))
                .andQualifiedRatePlanDetailsIsInsertedForAccomType(2, KING_ACCOM_TYPE, rateStartDateInStr, rateEndDateInStr,
                        valueOf(100), valueOf(50), valueOf(50),
                        valueOf(50), valueOf(50), valueOf(40), valueOf(38))
                .andQualifiedRatePlanDetailsIsInsertedForAccomType(2, MASTER_ACCOM_TYPE_DDLX, rateStartDateInStr, rateEndDateInStr,
                        valueOf(100), valueOf(50), valueOf(50),
                        valueOf(50), valueOf(50), valueOf(40), valueOf(38))
                .andSetRatePlanAtTotalLevel(2)
                .andWithExistingRoomTypeLevelDecisions()
                .insertRateQualifiedLv0Data(RATE_PLAN_LV0, 10)
                .thenFPLOSDecisionsAreGenerated(Arrays.asList(1, 2), false, true)
                .assertFPLOSWithLimitTotal();
    }

    private SRPBasedDecisionQualifiedFPLOSBatchUpdaterDataTest andWithExistingRoomTypeLevelDecisions() {
        tenantCrudService().executeUpdateByNativeQuery("insert into decision_qualified_fplos values (2, 5, 4, 2, '2016-01-01', 'NNYNNNN', GETDATE())");
        tenantCrudService().executeUpdateByNativeQuery("insert into decision_qualified_fplos values (2, 5, 7, 2, '2016-01-01', 'YNYYYYY', GETDATE())");
        tenantCrudService().executeUpdateByNativeQuery("insert into decision_qualified_fplos values (2, 5, 8, 2, '2016-01-01', 'YNYYYYY', GETDATE())");
        return this;
    }

    private SRPBasedDecisionQualifiedFPLOSBatchUpdaterDataTest andWithExistingIdenticalDecisions() {
        tenantCrudService().executeUpdateByNativeQuery("insert into decision_qualified_fplos values (2, 5, 4, 1, '2016-01-01', 'NNYNNNN', GETDATE())");
        tenantCrudService().executeUpdateByNativeQuery("insert into decision_qualified_fplos values (2, 5, 7, 1, '2016-01-01', 'YNYYYYY', GETDATE())");
        tenantCrudService().executeUpdateByNativeQuery("insert into decision_qualified_fplos values (2, 5, 8, 1, '2016-01-01', 'YNYYYYY', GETDATE())");
        return this;
    }

    private SRPBasedDecisionQualifiedFPLOSBatchUpdaterDataTest andSetRatePlanAtTotalLevel(int rateQualifiedId) {
        tenantCrudService().executeUpdateByNativeQuery("insert into Limit_Total_Rate_Qualified values (:rateQualifiedId);",
                QueryParameter.with("rateQualifiedId", rateQualifiedId).parameters());
        return this;
    }

    private SRPBasedDecisionQualifiedFPLOSBatchUpdaterDataTest assertBabByLOSFPLOSDecisionsRateQualified() {
        List<FPLOSDecisions> decisions = fplosDecisionService.getFPLOSDecisionData(startDate, endDate);
        assertEquals(2, decisions.size());
        assertEquals("YNYYYYY", decisions.get(0).getFplos());
        assertEquals("YNYYYYY", decisions.get(1).getFplos());
        return this;
    }

    private SRPBasedDecisionQualifiedFPLOSBatchUpdaterDataTest assertBabByLOSFPLOSDecisions() {
        List<FPLOSDecisions> decisions = fplosDecisionService.getFPLOSDecisionData(startDate, endDate);
        assertEquals(4, decisions.size());
        assertEquals("YNYYYYY", decisions.get(0).getFplos());
        assertEquals("YNYYYYY", decisions.get(1).getFplos());
        assertEquals("YNYYYYY", decisions.get(2).getFplos());
        assertEquals("YNYYYYY", decisions.get(3).getFplos());
        return this;
    }

    private SRPBasedDecisionQualifiedFPLOSBatchUpdaterDataTest assertBarByLOSFPLOSDecisionsWithDecisionId() {
        List<FPLOSDecisions> decisions = fplosDecisionService.getFPLOSDecisionData(startDate, endDate);
        assertEquals(4, decisions.size());
        assertEquals("YNYYYYY", decisions.get(0).getFplos());
        assertEquals("YNYYYYY", decisions.get(1).getFplos());
        assertEquals("YNYYYYY", decisions.get(2).getFplos());
        assertEquals("YNYYYYY", decisions.get(3).getFplos());
        return this;
    }

    private void assertFPLOSAtTotalLevelForMasterClassTars() {
        List<FPLOSDecisions> differentialsDecisions = fplosDecisionService.getFPLOSDecisionData(startDate, endDate);

        assertEquals(3, differentialsDecisions.size());
        assertEquals("NNNNYNNY", differentialsDecisions.get(0).getFplos());
        assertEquals("NNYYNNYY", differentialsDecisions.get(1).getFplos());
        assertEquals("YYYYNYYY", differentialsDecisions.get(2).getFplos());
    }

    private SRPBasedDecisionQualifiedFPLOSBatchUpdaterDataTest assertFPLOSWithLimitTotal() {
        List<FPLOSDecisions> decisions = fplosDecisionService.getFPLOSDecisionData(startDate, endDate);
        assertEquals(4, decisions.size());

        assertEquals("NNYNNNN", decisions.get(0).getFplos());
        assertEquals("DLX", decisions.get(0).getAccomTypeName());
        assertEquals(QR_MASTER_NON_DERIVED, decisions.get(0).getRateCodeName());

        assertEquals("YNYYYYY", decisions.get(1).getFplos());
        assertEquals("DLX", decisions.get(1).getAccomTypeName());
        assertEquals(NON_SPECIAL_RATE_PLAN, decisions.get(1).getRateCodeName());

        assertEquals("YNYYYYY", decisions.get(2).getFplos());
        assertEquals("K", decisions.get(2).getAccomTypeName());
        assertEquals(QR_MASTER_NON_DERIVED, decisions.get(2).getRateCodeName());

        assertEquals("YNYYYYY", decisions.get(3).getFplos());
        assertEquals("Q", decisions.get(3).getAccomTypeName());
        assertEquals(QR_MASTER_NON_DERIVED, decisions.get(3).getRateCodeName());
        return this;
    }

    private SRPBasedDecisionQualifiedFPLOSBatchUpdaterDataTest assertFPLOSWithLimitTotalRateQualified() {
        List<FPLOSDecisions> decisions = fplosDecisionService.getFPLOSDecisionData(startDate, endDate);
        assertEquals(3, decisions.size());

        assertEquals("NNYNNNN", decisions.get(0).getFplos());
        assertEquals("DLX", decisions.get(0).getAccomTypeName());
        assertEquals(QR_MASTER_NON_DERIVED, decisions.get(0).getRateCodeName());

        assertEquals("YNYYYYY", decisions.get(1).getFplos());
        assertEquals("K", decisions.get(1).getAccomTypeName());
        assertEquals(QR_MASTER_NON_DERIVED, decisions.get(1).getRateCodeName());

        assertEquals("YNYYYYY", decisions.get(2).getFplos());
        assertEquals("Q", decisions.get(2).getAccomTypeName());
        assertEquals(QR_MASTER_NON_DERIVED, decisions.get(2).getRateCodeName());
        return this;
    }

    private SRPBasedDecisionQualifiedFPLOSBatchUpdaterDataTest andRatePlanStartAndEndDateRangeAreSet(Date caughtUpDate, int daysToBeAdded) {
        startDate = caughtUpDate;
        rateStartDateInStr = DateUtil.formatDate(startDate, DateUtil.DEFAULT_DATE_FORMAT);
        endDate = dateService.getDecisionUploadWindowEndDate();
        rateEndDateInStr = DateUtil.formatDate(DateUtil.addDaysToDate(startDate, daysToBeAdded), DateUtil.DEFAULT_DATE_FORMAT);
        return this;
    }

    private SRPBasedDecisionQualifiedFPLOSBatchUpdaterDataTest thenFPLOSDecisionsAreGenerated(List<Integer> rateQualifiedIds, boolean isFPLOSAtTotalLevel, boolean isLimitTotalSRPRatesEnabled) {
        FPLOSQualifiedChunkDetail chunkDetail = new FPLOSQualifiedChunkDetail();
        chunkDetail.setRateQualifiedIds(rateQualifiedIds);
        chunkDetail.setLastLoop(false);
        chunkDetail.setFPLOSAtTotal(isFPLOSAtTotalLevel);
        chunkDetail.setLimitTotalSRPRatesEnabled(isLimitTotalSRPRatesEnabled);
        fplosRecomendationService.processChunk(startDate, endDate, chunkDetail);
        return this;
    }

    private FPLOSQualifiedChunkDetail createFPLOSDecisionsForFirstChunk(List<Integer> rateQualifiedIds, boolean isFPLOSAtTotalLevel, boolean isLimitTotalSRPRatesEnabled) {
        FPLOSQualifiedChunkDetail chunkDetail = new FPLOSQualifiedChunkDetail();
        chunkDetail.setRateQualifiedIds(rateQualifiedIds);
        chunkDetail.setLastLoop(false);
        chunkDetail.setFPLOSAtTotal(isFPLOSAtTotalLevel);
        chunkDetail.setLimitTotalSRPRatesEnabled(isLimitTotalSRPRatesEnabled);
        return fplosRecomendationService.processChunk(startDate, endDate, chunkDetail);
    }

    private SRPBasedDecisionQualifiedFPLOSBatchUpdaterDataTest whenConfigParamSetToReturn(String parameterName, String parameterValueToBeReturned) {
        when(pacmanConfigParamsService.getParameterValue(parameterName)).thenReturn(parameterValueToBeReturned);
        return this;
    }

    private SRPBasedDecisionQualifiedFPLOSBatchUpdaterDataTest andGivenAccomTypeAddedToMasterAccomClass(int accomClassId, int accomTypeID) {
        String updateAccomTypeQuery = "update Accom_Type set Accom_Class_ID = :accomClassId where Accom_Type_ID =  :accomTypeID";
        tenantCrudService().executeUpdateByNativeQuery(updateAccomTypeQuery,
                QueryParameter.with("accomClassId", accomClassId).and("accomTypeID", accomTypeID).parameters());
        return this;
    }

    private void populateServicingCost() {
        tenantCrudService().deleteAll(ServicingCostByLOSConfiguration.class);
        ServicingCostByLOSConfiguration servicingCostByLOSConfigurationMaster = buildTestConfiguration(MASTER_ACCOM_CLASS_ID, SPECIAL_RATE_PLAN, new BigDecimal(10), new BigDecimal(15), new BigDecimal(20), BigDecimal.ONE.intValue());
        ServicingCostByLOSConfiguration defaultServicingCostByLOSConfigurationMaster = buildTestConfiguration(MASTER_ACCOM_CLASS_ID, null, BigDecimal.ONE, BigDecimal.ONE, BigDecimal.ONE, BigDecimal.ZERO.intValue());
        ServicingCostByLOSConfiguration servicingCostByLOSConfigurationNonMaster = buildTestConfiguration(NON_MASTER_ACCOM_CLASS_ID, "MissingRate", new BigDecimal(10), new BigDecimal(15), new BigDecimal(20), BigDecimal.ONE.intValue());
        ServicingCostByLOSConfiguration defaultServicingCostByLOSConfigurationNonMaster = buildTestConfiguration(NON_MASTER_ACCOM_CLASS_ID, null, BigDecimal.ONE, BigDecimal.ONE, BigDecimal.ONE, BigDecimal.ZERO.intValue());
        tenantCrudService().save(Arrays.asList(servicingCostByLOSConfigurationMaster, defaultServicingCostByLOSConfigurationMaster, servicingCostByLOSConfigurationNonMaster, defaultServicingCostByLOSConfigurationNonMaster));
    }

    private ServicingCostByLOSConfiguration buildTestConfiguration(int accomClassID, String specialRatePlan, BigDecimal fullServicingCost, BigDecimal fullTurnCost, BigDecimal interimServicingCost, int intervalDays) {
        ServicingCostByLOSConfiguration testConfiguration = new ServicingCostByLOSConfiguration();
        testConfiguration.setPropertyId(TestProperty.H1.getId());
        testConfiguration.setBusinessType(getTransientBusinessType());
        testConfiguration.setAccomClass(getAccomClass(accomClassID));
        testConfiguration.setDefaultEvaluationMethod(GroupPricingEvaluationMethod.RC);
        testConfiguration.setRateCode(specialRatePlan);
        testConfiguration.setFullServicingCost(fullServicingCost);
        testConfiguration.setFullTurnServicingCost(fullTurnCost);
        testConfiguration.setInterimServicingCost(interimServicingCost);
        testConfiguration.setFullServicingIntervalDays(intervalDays);
        return testConfiguration;
    }

    private BusinessType getTransientBusinessType() {
        return tenantCrudService().find(BusinessType.class, TRANSIENT_BUSINESS_TYPE);
    }

    private AccomClass getAccomClass(int accomClassID) {
        return tenantCrudService().find(AccomClass.class, accomClassID);
    }

    private DateService getDateService() {
        DateService dateService = DateService.createTestInstance();
        PropertyGroupService propertyGroupService = new PropertyGroupService();
        dateService.setPropertyGroupService(propertyGroupService);
        dateService.setMultiPropertyCrudService(multiPropertyCrudService());
        dateService.setCrudService(tenantCrudService());
        return dateService;
    }


    private void mockDecisionRecord() {
        when(decisionService.createQualifiedFPLOSDecision()).thenReturn(getDecision());
    }

    private Decision getDecision() {
        Decision de = new Decision();
        de.setId(100);
        return de;
    }

    private void prepareLRVList(int startingValue, int length) {
        masterLRVDataList = new ArrayList<>();
        masterLRVDataList.add(valueOf(startingValue));
        for (int i = 2; i < length; i++) {
            masterLRVDataList.add(valueOf(startingValue + i));
        }

    }

    private void mockConfigParameters() {
        whenConfigParamSetToReturn(IntegrationConfigParamName.UPLOAD_DECISION_UPLOAD_WINDOW_BDEDAYS.value(), "10");
        whenConfigParamSetToReturn(IntegrationConfigParamName.APPLY_VARIABLE_DECISION_WINDOW.value(), FALSE);
        whenConfigParamSetToReturn(IntegrationConfigParamName.CORE_PROPERTY_QUALIFIED_FPLOSMAX_LOS.value(), "8");
        whenConfigParamSetToReturn(IntegrationConfigParamName.USE_EXTENDED_STAY_MAPPING.value(Constants.RATCHET), "");
        whenConfigParamSetToReturn(IPConfigParamName.BAR_BAR_DECISION.value(), Constants.BAR_DECISION_VALUE_RATEOFDAY);
        whenConfigParamSetToReturn(IPConfigParamName.BAR_ENABLE_SINGLE_BAR_DECISION.value(), FALSE);
        whenConfigParamSetToReturn(IPConfigParamName.BAR_MAX_LOS.value(), "7");
        whenConfigParamSetToReturn(IntegrationConfigParamName.SRP_FPLOS_AT_TOTAL_LEVEL.value(), FALSE);
        whenConfigParamSetToReturn(IntegrationConfigParamName.OPEN_LV0_OPEN_LV0ENABLED.value(), FALSE);

        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.HIGHEST_BAR_RESTRICTED_ENABLED)).thenReturn(Boolean.FALSE);
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.IS_DERIVED_QUALIFIED_RATE_PLAN_ENABLED.value())).thenReturn(Boolean.FALSE);
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.PROFIT_OPTIMIZATION_ENABLED)).thenReturn(Boolean.TRUE);
        when(pacmanConfigParamsService.getBooleanParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED.value())).thenReturn(Boolean.FALSE);
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.LRAENABLED.value())).thenReturn(Boolean.FALSE);
    }

    private SRPBasedDecisionQualifiedFPLOSBatchUpdaterDataTest andAccomActivityDataInsertedForAccomType(int accomTypeID, Date startDate, int activityDaysLength, int accomCapacity, int roomsSold) {
        List<Integer> accomCapacityList = new ArrayList();
        IntStream.range(0, activityDaysLength).forEach(i -> {
            accomCapacityList.add(accomCapacity);
        });
        return addAccomActivityEntries(accomTypeID, startDate, activityDaysLength, accomCapacityList, roomsSold);
    }

    private SRPBasedDecisionQualifiedFPLOSBatchUpdaterDataTest addAccomActivityEntries(int accomTypeID, Date startDate, int activityDaysLength, List accomCapacity, int roomsSold) {
        final StringBuilder insertAccomActivityQuery = new StringBuilder();

        String snapshotDateTime = DateUtil.formatDate(startDate, DateUtil.DEFAULT_DATE_FORMAT);
        insertAccomActivityQuery.append("INSERT INTO [Accom_Activity] (Property_ID,Occupancy_DT,SnapShot_DTTM,Accom_Type_ID" +
                ",Accom_Capacity,Rooms_Sold,Rooms_Not_Avail_Maint,Rooms_Not_Avail_Other,Arrivals,Departures,Cancellations" +
                ",No_Shows,Room_Revenue,Food_Revenue,Total_Revenue,File_Metadata_ID,Last_Updated_DTTM,CreateDate) VALUES\n");
        IntStream.range(0, activityDaysLength).forEach(i -> {
            insertAccomActivityQuery.append(" (" + PROPERTY_ID + ",'" + DateUtil.formatDate(DateUtil.addDaysToDate(startDate, i), DateUtil.DEFAULT_DATE_FORMAT) + "','" + snapshotDateTime + "'," + accomTypeID + "," + accomCapacity.get(i) + "," + roomsSold + ",0,0,0,0,0,0,0,0,0,1,CURRENT_TIMESTAMP,CURRENT_TIMESTAMP)\n");
            if (i < (activityDaysLength - 1)) {
                insertAccomActivityQuery.append(", ");
            }
        });
        insertAccomActivityQuery.append(";");
        tenantCrudService().executeUpdateByNativeQuery(insertAccomActivityQuery.toString());
        return this;
    }

    private SRPBasedDecisionQualifiedFPLOSBatchUpdaterDataTest andAccomActivityDataCleanedUp() {
        final StringBuilder cleanupAccomActivityDataQuery = new StringBuilder();
        cleanupAccomActivityDataQuery.append("delete from [Accom_Activity];");
        tenantCrudService().executeUpdateByNativeQuery(cleanupAccomActivityDataQuery.toString());
        return this;
    }

    private SRPBasedDecisionQualifiedFPLOSBatchUpdaterDataTest andDecisionLRVDataIsPreparedForAccomClass(int accomClassID, Date startDate, int days, List<BigDecimal> lrvList) {
        tenantCrudService().executeUpdateByNativeQuery("delete from Decision_LRV where Accom_Class_ID = " + accomClassID);
        final StringBuilder lrvDecisionQuery = new StringBuilder();
        lrvDecisionQuery.append("DECLARE @decisionID int\n");
        lrvDecisionQuery.append("select @decisionID = Max(Decision_ID) from Decision_LRV\n");
        lrvDecisionQuery.append("INSERT INTO Decision_LRV([Decision_ID],[Property_ID],[Accom_Class_ID],[Occupancy_DT],[LRV],[CreateDate_DTTM]) VALUES\n");
        IntStream.range(0, days).forEach(i -> {
            lrvDecisionQuery.append(" (@decisionID," + PROPERTY_ID + "," + accomClassID + ",'" + DateUtil.formatDate(DateUtil.addDaysToDate(startDate, i), DateUtil.DEFAULT_DATE_FORMAT) + "'," + lrvList.get(i) + ",CURRENT_TIMESTAMP)\n");
            if (i < (days - 1)) {
                lrvDecisionQuery.append(", ");
            }
        });
        lrvDecisionQuery.append(";");
        tenantCrudService().executeUpdateByNativeQuery(lrvDecisionQuery.toString());
        return this;
    }

    private SRPBasedDecisionQualifiedFPLOSBatchUpdaterDataTest andQualifiedRatePlanDataIsInserted(int rateQualifiedId, String rateCodeName, String qualifiedRateplanStartDate, String qualifiedRateplanEndDate, int rateQualifiedTypeId) {
        tenantCrudService().executeUpdateByNativeQuery("delete from Rate_Qualified_Details where Rate_Qualified_ID = " + rateQualifiedId);
        tenantCrudService().executeUpdateByNativeQuery("delete from Rate_Qualified_Adjustment where Rate_Qualified_ID = " + rateQualifiedId);
        tenantCrudService().executeUpdateByNativeQuery("delete from Rate_Qualified where Rate_Qualified_ID = " + rateQualifiedId);
        String rateQualifiedQuery = "SET IDENTITY_INSERT [Rate_Qualified] ON" +
                " INSERT INTO [Rate_Qualified]" +
                " ([Rate_Qualified_ID],[File_Metadata_ID],[Property_ID],[Rate_Code_Name],[Rate_Code_Description],[Start_Date_DT],[End_Date_DT]" +
                "   ,[Yieldable],[Price_Relative],[Reference_Rate_Code],[Includes_Package],[Last_Updated_DTTM]" +
                "   ,[Status_ID],[Created_By_User_ID],[Created_DTTM],[Last_Updated_By_User_ID],[Rate_Qualified_Type_Id])" +
                " VALUES (" + rateQualifiedId + ",(select MAX(File_Metadata_ID) from File_Metadata), " + PROPERTY_ID + ", '" + rateCodeName + "', 'rate description', '" + qualifiedRateplanStartDate + "', '" + qualifiedRateplanEndDate + "'" +
                "       , 1, 0, 'None', 0, CURRENT_TIMESTAMP, 1, 11403,CURRENT_TIMESTAMP, 11403, " + rateQualifiedTypeId + ")" +
                "SET IDENTITY_INSERT [Rate_Qualified] OFF;";
        tenantCrudService().executeUpdateByNativeQuery(rateQualifiedQuery);
        return this;
    }

    private SRPBasedDecisionQualifiedFPLOSBatchUpdaterDataTest andQualifiedRatePlanDetailsIsInsertedForAccomType(int rateQualifiedId, int accomTypeID, String qualifiedRateplanDetailsStartDate, String qualifiedRateplanDetailsEndDate, BigDecimal sunday, BigDecimal monday, BigDecimal tuesday, BigDecimal wednesday, BigDecimal thursday, BigDecimal friday, BigDecimal saturday) {
        final StringBuilder insertRateQualifiedDetails = new StringBuilder();
        insertRateQualifiedDetails.append("INSERT INTO [Rate_Qualified_Details]" +
                " ([Rate_Qualified_ID],[Accom_Type_ID],[Start_Date_DT],[End_Date_DT],[Sunday],[Monday],[Tuesday],[Wednesday],[Thursday],[Friday],[Saturday],[Created_By_User_ID],[Created_DTTM],[Last_Updated_By_User_ID],[Last_Updated_DTTM])\n" +
                " VALUES (" + rateQualifiedId + "," + accomTypeID + ",'" + qualifiedRateplanDetailsStartDate + "', '" + qualifiedRateplanDetailsEndDate + "'," + sunday + "," +
                monday + "," + tuesday + "," + wednesday + "," + thursday + "," + friday + "," + saturday + ", 11403, CURRENT_TIMESTAMP, 11403, CURRENT_TIMESTAMP);");
        tenantCrudService().executeUpdateByNativeQuery(insertRateQualifiedDetails.toString());
        return this;
    }

    private SRPBasedDecisionQualifiedFPLOSBatchUpdaterDataTest andUnqualifiedRatePlanDataIsInserted(int rateUnqualifiedId, String rateCodeName, String startDate, String endDate) {
        tenantCrudService().executeUpdateByNativeQuery("delete from Rate_Unqualified_Details where Rate_Unqualified_ID = " + rateUnqualifiedId);
        tenantCrudService().executeUpdateByNativeQuery("delete from Decision_Bar_Output where Rate_Unqualified_ID = " + rateUnqualifiedId);
        tenantCrudService().executeUpdateByNativeQuery("delete from Decision_Bar_Output where Floor_Rate_Unqualified_ID = " + rateUnqualifiedId);
        tenantCrudService().executeUpdateByNativeQuery("delete from PACE_Bar_Output where Rate_Unqualified_ID = " + rateUnqualifiedId);
        tenantCrudService().executeUpdateByNativeQuery("delete from PACE_Bar_Output where Floor_Rate_Unqualified_ID = " + rateUnqualifiedId);
        tenantCrudService().executeUpdateByNativeQuery("delete from Unqualified_Demand_FCST_Price where Rate_Unqualified_ID = " + rateUnqualifiedId);
        tenantCrudService().executeUpdateByNativeQuery("delete from Occupancy_Demand_FCST_OVR where Rate_Unqualified_ID = " + rateUnqualifiedId);
        tenantCrudService().executeUpdateByNativeQuery("delete from Arrival_Demand_FCST_OVR where Rate_Unqualified_ID = " + rateUnqualifiedId);

        tenantCrudService().executeUpdateByNativeQuery("delete from Rate_Unqualified_Defaults where Rate_Unqualified_Accom_Class_ID in (select Rate_Unqualified_Accom_Class_ID from Rate_Unqualified_Accom_Class where Rate_Unqualified_ID = " + rateUnqualifiedId + ")");
        tenantCrudService().executeUpdateByNativeQuery("delete from Rate_Unqualified_Accom_Class where Rate_Unqualified_ID = " + rateUnqualifiedId);
        tenantCrudService().executeUpdateByNativeQuery("delete from Decision_Bar_Output_OVR where Old_Rate_Unqualified_ID = " + rateUnqualifiedId + " or Old_Floor_Rate_Unqualified_ID = "
                + rateUnqualifiedId + " or New_Rate_Unqualified_ID = " + rateUnqualifiedId + " or New_Floor_Rate_Unqualified_ID = " + rateUnqualifiedId +
                " or Old_Ceil_Rate_Unqualified_ID = " + rateUnqualifiedId + " or New_Ceil_Rate_Unqualified_ID = " + rateUnqualifiedId);
        tenantCrudService().executeUpdateByNativeQuery("delete from Rate_Unqualified where Rate_Unqualified_ID = " + rateUnqualifiedId);
        String rateQualifiedQuery = "SET IDENTITY_INSERT [Rate_Unqualified] ON" +
                " INSERT INTO [Rate_Unqualified]" +
                " ([Rate_Unqualified_ID],[File_Metadata_ID],[Property_ID],[Rate_Code_Name],[Rate_Code_Description],[Start_Date_DT],[End_Date_DT]" +
                "   ,[Yieldable],[Price_Relative],[Derived_Rate_Code],[Includes_Package],[Ranking_Level],[System_Default],[Last_Updated_DTTM]" +
                "   ,[Status_ID],[Created_By_User_ID],[Created_DTTM],[Last_Updated_By_User_ID], [Managed_In_G3])" +
                " VALUES (" + rateUnqualifiedId + ",(select MAX(File_Metadata_ID) from File_Metadata), " + PROPERTY_ID + ", '" + rateCodeName + "', 'rate description', '" + startDate + "', '" + endDate + "'" +
                "       , 1, 0, 'None', 0, 0, 0, CURRENT_TIMESTAMP, 1, 11403,CURRENT_TIMESTAMP, 11403, 0)" +
                "SET IDENTITY_INSERT [Rate_Unqualified] OFF;";
        tenantCrudService().executeUpdateByNativeQuery(rateQualifiedQuery);
        return this;
    }

    private SRPBasedDecisionQualifiedFPLOSBatchUpdaterDataTest andUnqualifiedRatePlanDetailsIsInseretd(int rateUnqualifiedId, int accomTypeID, String startDate, String endDate, List<BigDecimal> rateDOWValues) {
        final StringBuilder insertRateQualifiedDetails = new StringBuilder();
        insertRateQualifiedDetails.append("INSERT INTO [Rate_Unqualified_Details]" +
                " ([Rate_Unqualified_ID],[Accom_Type_ID],[Start_Date_DT],[End_Date_DT],[Sunday],[Monday],[Tuesday],[Wednesday],[Thursday],[Friday],[Saturday],[Created_By_User_ID],[Created_DTTM],[Last_Updated_By_User_ID],[Last_Updated_DTTM])\n" +
                " VALUES (" + rateUnqualifiedId + "," + accomTypeID + ",'" + startDate + "', '" + endDate + "'," + rateDOWValues.get(0) + "," +
                rateDOWValues.get(1) + "," + rateDOWValues.get(2) + "," + rateDOWValues.get(3) + "," + rateDOWValues.get(4) + "," + rateDOWValues.get(5) + "," + rateDOWValues.get(6) + ", 11403, CURRENT_TIMESTAMP, 11403, CURRENT_TIMESTAMP);");
        tenantCrudService().executeUpdateByNativeQuery(insertRateQualifiedDetails.toString());
        return this;
    }

    private SRPBasedDecisionQualifiedFPLOSBatchUpdaterDataTest prepareDecisionBarOutputData(int rateUnqualifiedId, int accomClassID, Date startDate, Date endDate) {
        tenantCrudService().executeUpdateByNativeQuery("delete from decision_bar_output where decision_bar_output_ID = " + rateUnqualifiedId);
        StringBuilder queryStringBuilder = new StringBuilder();
        Date dateIterator = DateUtil.removeTimeFromDate(startDate);
        while (dateIterator.compareTo(endDate) <= 0) {
            queryStringBuilder.append(
                    "Insert into decision_bar_output values(" + getDecision().getId() + ", " + PROPERTY_ID + ", " + accomClassID + ",'" + DateUtil.formatDate(dateIterator, DateUtil.DEFAULT_DATE_FORMAT) + "'," + rateUnqualifiedId + ",-1,'None',null,1,null,null,getdate(),null);");
            dateIterator = DateUtil.addDaysToDate(dateIterator, 1);
        }
        tenantCrudService().executeUpdateByNativeQuery(queryStringBuilder.toString());
        return this;
    }


    private SRPBasedDecisionQualifiedFPLOSBatchUpdaterDataTest prepareDecisionBarOutputDataByLOS(int rateUnqualifiedId, int accomClassID, Date startDate, Integer los) {
        tenantCrudService().executeUpdateByNativeQuery("delete from decision_bar_output where decision_bar_output_ID = " + rateUnqualifiedId);
        StringBuilder queryStringBuilder = new StringBuilder();
        Date dateIterator = DateUtil.removeTimeFromDate(startDate);

        queryStringBuilder.append("Insert into decision_bar_output (Decision_ID,Property_ID,Accom_Class_ID,Arrival_DT,Rate_Unqualified_ID,")
                .append("LOS,Override,Floor_Rate_Unqualified_ID,Decision_Reason_Type_ID,Month_ID,Year_ID,CreateDate,Ceil_Rate_Unqualified_ID) ")
                .append("values(" + getDecision().getId() + ", " + PROPERTY_ID + ", " + accomClassID + ",'" + DateUtil.formatDate(dateIterator, DateUtil.DEFAULT_DATE_FORMAT))
                .append("'," + rateUnqualifiedId + "," + los + ",'None',null,1,null,null,getdate(),null);");

        tenantCrudService().executeUpdateByNativeQuery(queryStringBuilder.toString());
        return this;
    }


    private SRPBasedDecisionQualifiedFPLOSBatchUpdaterDataTest andCaughtUpDateUpdatedTo(String caughtUpDate) {
        String updateCaughtUpDateQuery = "UPDATE File_Metadata SET SnapShot_DT = :caughtUpDate"
                + " WHERE Record_Type_ID = 3 AND Process_Status_ID = 13 AND IsBDE = 1 AND property_Id =  :propertyID";
        tenantCrudService().executeUpdateByNativeQuery(updateCaughtUpDateQuery,
                QueryParameter.with("caughtUpDate", caughtUpDate).and("propertyID", PROPERTY_ID).parameters());
        return this;
    }


    private void assertGeneratedFPLOSForDerivedRates() {
        List<FPLOSDecisions> differentialsDecisions = fplosDecisionService.getFPLOSDecisionData(startDate, endDate);

        assertEquals(9, differentialsDecisions.size());
        assertEquals("YYYYNNNN", differentialsDecisions.get(0).getFplos());
        assertEquals("NNNNNNNN", differentialsDecisions.get(1).getFplos());
        assertEquals("NNNNNNNY", differentialsDecisions.get(2).getFplos());

        assertEquals("NYYYYYYY", differentialsDecisions.get(3).getFplos());
        assertEquals("YYYYYYYY", differentialsDecisions.get(4).getFplos());
        assertEquals("YYYYNYYY", differentialsDecisions.get(5).getFplos());

        assertEquals("YYNNYYNY", differentialsDecisions.get(6).getFplos());
        assertEquals("YNNYYNYY", differentialsDecisions.get(7).getFplos());
        assertEquals("NNYYNYYY", differentialsDecisions.get(8).getFplos());
    }

    private void assertFPLOSTOtalLevelforMasterClass() {
        assertFPLOSAtTotalLevelForMasterClassTars();
    }


    @Test
    public void SRPAtTotalLevelFalse_IsDerivedRateTrue_IsBarByLosTrue_withRateAdjustment() {

        List<BigDecimal> decisionLRV = getListOfRates(90, 190, 160, 350, 0, 50, 100);

        setToggleValues();
        whenConfigParamSetToReturn(IPConfigParamName.BAR_BAR_DECISION.value(), Constants.BAR_DECISION_VALUE_LOS)
                .whenConfigParamSetToReturn(IntegrationConfigParamName.SRP_FPLOS_AT_TOTAL_LEVEL.value(), Constants.FALSE)
                .whenConfigParamSetToReturn(CORE_PROPERTY_QUALIFIED_FPLOSMAX_LOS.value(), "7")
                .andCaughtUpDateUpdatedTo("2000-01-02")
                .andRatePlanStartAndEndDateRangeAreSet(dateService.getCaughtUpDate(), 7)
                .andAccomActivityDataCleanedUp()
                .andAccomActivityDataInsertedForAccomType(QUEEN_ACCOM_TYPE, startDate, 7, 40, 1)
                .andDecisionLRVDataIsPreparedForAccomClass(NON_MASTER_ACCOM_CLASS_DLX, startDate, decisionLRV.size(), decisionLRV);
        insertRateQualifiedData();
        insertRateUnqualifiedData();
        insertRateAdjustmentData();
        insertDecisionBarOutputData();

        thenFPLOSDecisionsAreGenerated(Arrays.asList(1, 2), false, false)
                .assertFPLOSPattern(1);
    }

    @Test
    public void SRPAtTotalLevelTrue_IsDerivedRateTrue_IsBarByLosTrue_withRateAdjustment() {

        List<BigDecimal> decisionLRV = getListOfRates(90, 190, 160, 350, 18, 50, 100);

        setToggleValues();
        whenConfigParamSetToReturn(IPConfigParamName.BAR_BAR_DECISION.value(), Constants.BAR_DECISION_VALUE_LOS)
                .whenConfigParamSetToReturn(IntegrationConfigParamName.SRP_FPLOS_AT_TOTAL_LEVEL.value(), TRUE)
                .whenConfigParamSetToReturn(CORE_PROPERTY_QUALIFIED_FPLOSMAX_LOS.value(), "7")
                .andCaughtUpDateUpdatedTo("2000-01-02")
                .andSetMasterAccomClassTo(2)
                .andRatePlanStartAndEndDateRangeAreSet(dateService.getCaughtUpDate(), 7)
                .andAccomActivityDataCleanedUp()
                .addAccomActivityEntries(QUEEN_ACCOM_TYPE, startDate, 7, getAccomActivityList(), 1)
                .addAccomActivityEntries(NON_MASTER_ACCOM_TYPE_DBL, startDate, 7, getAccomActivityList(), 1)
                .andDecisionLRVDataIsPreparedForAccomClass(NON_MASTER_ACCOM_CLASS_DLX, startDate, decisionLRV.size(), decisionLRV)
                .insertRateQualifiedData()
                .andQualifiedRatePlanDetailsIsInsertedForAccomType(1, NON_MASTER_ACCOM_TYPE_DBL, rateStartDateInStr, rateEndDateInStr,
                        valueOf(10), valueOf(11), valueOf(12),
                        valueOf(13), valueOf(14), valueOf(15), valueOf(16))
                .insertRateUnqualifiedData()
                .insertRateAdjustmentData()
                .insertDecisionBarOutputData()
                .cleanPaceDecisionTable()
                .insertRateQualifiedLv0Data(RATE_PLAN_LV0, 10)
                .thenFPLOSDecisionsAreGenerated(Arrays.asList(1, 2), true, false)
                .assertFPLOSPattern(1);
    }

    @Test
    public void SRPAtTotalLevelTrue_IsDerivedRateTrue_IsBarByLosTrue_withRateAdjustment_ChannelAdjON() {

        List<BigDecimal> decisionLRV = getListOfRates(90, 190, 160, 350, 18, 50, 100);

        setToggleValues();
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.CHANNEL_RESTRICTIONS_ADJUSTMENT)).thenReturn(true);
        whenConfigParamSetToReturn(IPConfigParamName.BAR_BAR_DECISION.value(), Constants.BAR_DECISION_VALUE_LOS)
                .whenConfigParamSetToReturn(IntegrationConfigParamName.SRP_FPLOS_AT_TOTAL_LEVEL.value(), TRUE)
                .whenConfigParamSetToReturn(CORE_PROPERTY_QUALIFIED_FPLOSMAX_LOS.value(), "7")
                .andCaughtUpDateUpdatedTo("2000-01-02")
                .andSetMasterAccomClassTo(2)
                .andRatePlanStartAndEndDateRangeAreSet(dateService.getCaughtUpDate(), 7)
                .andAccomActivityDataCleanedUp()
                .addAccomActivityEntries(QUEEN_ACCOM_TYPE, startDate, 7, getAccomActivityList(), 1)
                .addAccomActivityEntries(NON_MASTER_ACCOM_TYPE_DBL, startDate, 7, getAccomActivityList(), 1)
                .andDecisionLRVDataIsPreparedForAccomClass(NON_MASTER_ACCOM_CLASS_DLX, startDate, decisionLRV.size(), decisionLRV)
                .insertRateQualifiedData()
                .andQualifiedRatePlanDetailsIsInsertedForAccomType(1, NON_MASTER_ACCOM_TYPE_DBL, rateStartDateInStr, rateEndDateInStr,
                        valueOf(10), valueOf(11), valueOf(12),
                        valueOf(13), valueOf(14), valueOf(15), valueOf(16))
                .insertRateUnqualifiedData()
                .insertRateAdjustmentData()
                .insertDecisionBarOutputData()
                .cleanPaceDecisionTable()
                .insertRateQualifiedLv0Data(RATE_PLAN_LV0, 10)
                .thenFPLOSDecisionsAreGenerated(Arrays.asList(1, 2), true, false)
                .assertFPLOSPattern(1);
    }

    @Test
    public void SRPAtRTLevel_IsDerivedRateTrue_missingRateUnqualifiedDetails() {
        List<BigDecimal> decisionLRV = getListOfRates(90, 190, 160, 350, 0, 50, 100);
        setToggleValues();
        whenConfigParamSetToReturn(IPConfigParamName.BAR_BAR_DECISION.value(), Constants.BAR_DECISION_VALUE_LOS)
                .whenConfigParamSetToReturn(IntegrationConfigParamName.SRP_FPLOS_AT_TOTAL_LEVEL.value(), Constants.FALSE)
                .whenConfigParamSetToReturn(CORE_PROPERTY_QUALIFIED_FPLOSMAX_LOS.value(), "7")
                .andCaughtUpDateUpdatedTo("2000-01-02")
                .andRatePlanStartAndEndDateRangeAreSet(dateService.getCaughtUpDate(), 7)
                .andAccomActivityDataCleanedUp()
                .andAccomActivityDataInsertedForAccomType(QUEEN_ACCOM_TYPE, startDate, 7, 40, 1)
                .andAccomActivityDataInsertedForAccomType(NON_MASTER_ACCOM_TYPE_DBL, DateUtil.addDaysToDate(startDate, 1), 7, 40, 1)
                .andDecisionLRVDataIsPreparedForAccomClass(NON_MASTER_ACCOM_CLASS_DLX, startDate, decisionLRV.size(), decisionLRV);
        insertRateQualifiedLv0Data(RATE_PLAN_LV0, 10);
        insertRateQualifiedData();
        insertRateQualifiedDetailsDataForOtherRT("2000-01-03", "2000-01-10");

        insertRateUnqualifiedData();
        insertRateAdjustmentData();
        insertDecisionBarOutputData_missingUnqualifiedForLOS3();

        thenFPLOSDecisionsAreGenerated(Arrays.asList(1, 2, 10), false, false);
        assertFPLOSPatternNotGeneratedAsUnqualifiedDetailsMissing();
    }

    @Test
    public void SRPAtRTLevel_IsDerivedRateTrue_missingRateUnqualifiedDetailsForOneATSameACSameDay() {
        List<BigDecimal> decisionLRV = getListOfRates(90, 190, 160, 350, 0, 50, 100);
        setToggleValues();
        whenConfigParamSetToReturn(IPConfigParamName.BAR_BAR_DECISION.value(), Constants.BAR_DECISION_VALUE_LOS)
                .whenConfigParamSetToReturn(IntegrationConfigParamName.SRP_FPLOS_AT_TOTAL_LEVEL.value(), Constants.FALSE)
                .whenConfigParamSetToReturn(CORE_PROPERTY_QUALIFIED_FPLOSMAX_LOS.value(), "7")
                .andCaughtUpDateUpdatedTo("2000-01-02")
                .andRatePlanStartAndEndDateRangeAreSet(dateService.getCaughtUpDate(), 7)
                .andAccomActivityDataCleanedUp()
                .andAccomActivityDataInsertedForAccomType(QUEEN_ACCOM_TYPE, startDate, 7, 40, 1)
                .andAccomActivityDataInsertedForAccomType(NON_MASTER_ACCOM_TYPE_DBL, startDate, 7, 40, 1)
                .andDecisionLRVDataIsPreparedForAccomClass(NON_MASTER_ACCOM_CLASS_DLX, startDate, decisionLRV.size(), decisionLRV);
        insertRateQualifiedLv0Data(RATE_PLAN_LV0, 10);
        insertRateQualifiedData();
        insertRateQualifiedDetailsDataForOtherRT(rateStartDateInStr, rateEndDateInStr);
        insertRateUnqualifiedData();
        insertRateAdjustmentData();
        insertDecisionBarOutputData_missingUnqualifiedForLOS3_validDataForOtherATSameACSameDay();

        thenFPLOSDecisionsAreGenerated(Arrays.asList(1, 2, 10), false, false);
        List<FPLOSDecisions> decisions = fplosDecisionService.getFPLOSDecisionData(startDate, endDate);
        assertEquals(1, decisions.size());
        assertEquals(DateUtil.formatDate(startDate, DateUtil.DEFAULT_DATE_FORMAT),
                DateUtil.formatDate(decisions.get(0).getArrivalDate(), DateUtil.DEFAULT_DATE_FORMAT), "FPLOS is generated for 2000-01-02 for AT_ID =6 only");
        assertEquals("DBL", decisions.get(0).getAccomTypeName());
    }


    private void assertFPLOSPatternNotGeneratedAsUnqualifiedDetailsMissing() {
        List<FPLOSDecisions> decisions = fplosDecisionService.getFPLOSDecisionData(startDate, endDate);
        assertEquals(1, decisions.size());
        assertEquals(DateUtil.formatDate(DateUtil.addDaysToDate(startDate, 1), DateUtil.DEFAULT_DATE_FORMAT),
                DateUtil.formatDate(decisions.get(0).getArrivalDate(), DateUtil.DEFAULT_DATE_FORMAT), "FPLOS not generated for 2000-01-02 as unqualified details are missing");
        assertEquals("DBL", decisions.get(0).getAccomTypeName());
    }

    private SRPBasedDecisionQualifiedFPLOSBatchUpdaterDataTest insertDecisionBarOutputData_missingUnqualifiedForLOS3() {
        prepareDecisionBarOutputDataWithMissingLOS3();


        List<BigDecimal> unqualifiedRate3 = getListOfRates(160, 161, 162, 163, 164, 165, 166);
        Date nextDate = DateUtil.addDaysToDate(startDate, 1);
        String nextDateInString = DateUtil.formatDate(nextDate, DateUtil.DEFAULT_DATE_FORMAT);
        String date = DateUtil.formatDate(DateUtil.addDaysToDate(nextDate, 7), DateUtil.DEFAULT_DATE_FORMAT);
        prepareDecisionBarOutputDataByLOS(1, NON_MASTER_ACCOM_CLASS_ID, nextDate, 1)
                .prepareDecisionBarOutputDataByLOS(2, NON_MASTER_ACCOM_CLASS_ID, nextDate, 2)
                .prepareDecisionBarOutputDataByLOS(3, NON_MASTER_ACCOM_CLASS_ID, nextDate, 3)
                .prepareDecisionBarOutputDataByLOS(4, NON_MASTER_ACCOM_CLASS_ID, nextDate, 4)
                .prepareDecisionBarOutputDataByLOS(5, NON_MASTER_ACCOM_CLASS_ID, nextDate, 5)
                .prepareDecisionBarOutputDataByLOS(6, NON_MASTER_ACCOM_CLASS_ID, nextDate, 6)
                .prepareDecisionBarOutputDataByLOS(7, NON_MASTER_ACCOM_CLASS_ID, nextDate, 7)
                .andUnqualifiedRatePlanDetailsIsInseretd(3, NON_MASTER_ACCOM_TYPE_DBL, nextDateInString, date, unqualifiedRate3);
        return this;
    }

    private SRPBasedDecisionQualifiedFPLOSBatchUpdaterDataTest insertDecisionBarOutputData_missingUnqualifiedForLOS3_validDataForOtherATSameACSameDay() {
        prepareDecisionBarOutputDataWithMissingLOS3();


        List<BigDecimal> unqualifiedRate3 = getListOfRates(160, 161, 162, 163, 164, 165, 166);
        andUnqualifiedRatePlanDetailsIsInseretd(3, NON_MASTER_ACCOM_TYPE_DBL, DateUtil.formatDate(startDate, DateUtil.DEFAULT_DATE_FORMAT), DateUtil.formatDate(endDate, DateUtil.DEFAULT_DATE_FORMAT), unqualifiedRate3);
        return this;
    }

    private void prepareDecisionBarOutputDataWithMissingLOS3() {
        prepareDecisionBarOutputDataByLOS(1, NON_MASTER_ACCOM_CLASS_ID, startDate, 1)
                .prepareDecisionBarOutputDataByLOS(2, NON_MASTER_ACCOM_CLASS_ID, startDate, 2)
                .prepareDecisionBarOutputDataByLOSWithMissingDetails(3, NON_MASTER_ACCOM_CLASS_ID, startDate, 3)
                .prepareDecisionBarOutputDataByLOS(4, NON_MASTER_ACCOM_CLASS_ID, startDate, 4)
                .prepareDecisionBarOutputDataByLOS(5, NON_MASTER_ACCOM_CLASS_ID, startDate, 5)
                .prepareDecisionBarOutputDataByLOS(6, NON_MASTER_ACCOM_CLASS_ID, startDate, 6)
                .prepareDecisionBarOutputDataByLOS(7, NON_MASTER_ACCOM_CLASS_ID, startDate, 7);
    }

    private SRPBasedDecisionQualifiedFPLOSBatchUpdaterDataTest insertRateQualifiedDetailsDataForOtherRT(String startDate, String endDate) {
        andQualifiedRatePlanDetailsIsInsertedForAccomType(1, NON_MASTER_ACCOM_TYPE_DBL, startDate, endDate,
                valueOf(10), valueOf(11), valueOf(12),
                valueOf(13), valueOf(14), valueOf(15), valueOf(16));
        return this;
    }

    private SRPBasedDecisionQualifiedFPLOSBatchUpdaterDataTest prepareDecisionBarOutputDataByLOSWithMissingDetails(int rateUnqualifiedId, int accomClassID, Date startDate, Integer los) {
        tenantCrudService().executeUpdateByNativeQuery("delete from decision_bar_output where decision_bar_output_ID = " + rateUnqualifiedId);
        StringBuilder queryStringBuilder = new StringBuilder();
        Date dateIterator = DateUtil.removeTimeFromDate(startDate);

        queryStringBuilder.append("Insert into decision_bar_output (Decision_ID,Property_ID,Accom_Class_ID,Arrival_DT,Rate_Unqualified_ID,")
                .append("LOS,Override,Floor_Rate_Unqualified_ID,Decision_Reason_Type_ID,Month_ID,Year_ID,CreateDate,Ceil_Rate_Unqualified_ID) ")
                .append("values(" + getDecision().getId() + ", " + PROPERTY_ID + ", " + accomClassID + ",'" + DateUtil.formatDate(dateIterator, DateUtil.DEFAULT_DATE_FORMAT))
                .append("'," + rateUnqualifiedId + "," + los + ",'None',null,1,null,null,getdate(),null);");

        tenantCrudService().executeUpdateByNativeQuery("delete from rate_unqualified_details where rate_unqualified_id = " + rateUnqualifiedId);

        tenantCrudService().executeUpdateByNativeQuery(queryStringBuilder.toString());
        return this;
    }

    private SRPBasedDecisionQualifiedFPLOSBatchUpdaterDataTest andSetMasterAccomClassTo(int accomClassID) {
        tenantCrudService().executeUpdateByNativeQuery("update Accom_Class set Master_Class = case when Accom_Class_ID = " + accomClassID + " then 1 else 0 end ");
        return this;
    }

    private SRPBasedDecisionQualifiedFPLOSBatchUpdaterDataTest cleanPaceDecisionTable() {
        tenantCrudService().executeUpdateByNativeQuery("delete from PACE_Qualified_FPLOS where Arrival_DT = '" + rateStartDateInStr + "'");
        return this;
    }

    private List getAccomActivityList() {
        return Arrays.asList(10, 20, 30, 40, 50, 60, 70, 80, 90, 100);
    }

    private SRPBasedDecisionQualifiedFPLOSBatchUpdaterDataTest insertDecisionBarOutputData() {
        prepareDecisionBarOutputDataByLOS(1, NON_MASTER_ACCOM_CLASS_ID, startDate, 1)
                .prepareDecisionBarOutputDataByLOS(2, NON_MASTER_ACCOM_CLASS_ID, startDate, 2)
                .prepareDecisionBarOutputDataByLOS(3, NON_MASTER_ACCOM_CLASS_ID, startDate, 3)
                .prepareDecisionBarOutputDataByLOS(4, NON_MASTER_ACCOM_CLASS_ID, startDate, 4)
                .prepareDecisionBarOutputDataByLOS(5, NON_MASTER_ACCOM_CLASS_ID, startDate, 5)
                .prepareDecisionBarOutputDataByLOS(6, NON_MASTER_ACCOM_CLASS_ID, startDate, 6)
                .prepareDecisionBarOutputDataByLOS(7, NON_MASTER_ACCOM_CLASS_ID, startDate, 7);
        return this;
    }

    private SRPBasedDecisionQualifiedFPLOSBatchUpdaterDataTest insertRateAdjustmentData() {
        prepateRateAdjustmentData(1, 1, startDate, startDate, 2, 20, 1, "YieldableCost")
                .prepateRateAdjustmentData(2, 1, DateUtil.addDaysToDate(startDate, 1), DateUtil.addDaysToDate(startDate, 1), 2, 30, 2, "YieldableCost")
                .prepateRateAdjustmentData(3, 1, DateUtil.addDaysToDate(startDate, 2), DateUtil.addDaysToDate(startDate, 2), 2, 25, 2, "YieldableCost")
                .prepateRateAdjustmentData(4, 1, DateUtil.addDaysToDate(startDate, 3), DateUtil.addDaysToDate(startDate, 3), 2, 15, 2, "YieldableCost")
                .prepateRateAdjustmentData(5, 1, DateUtil.addDaysToDate(startDate, 4), DateUtil.addDaysToDate(startDate, 4), 2, 30, 2, "YieldableCost")
                .prepateRateAdjustmentData(6, 1, DateUtil.addDaysToDate(startDate, 5), DateUtil.addDaysToDate(startDate, 5), 2, 25, 2, "YieldableCost")
                .prepateRateAdjustmentData(7, 1, DateUtil.addDaysToDate(startDate, 6), DateUtil.addDaysToDate(startDate, 6), 2, 35, 2, "YieldableCost");
        return this;
    }

    private SRPBasedDecisionQualifiedFPLOSBatchUpdaterDataTest insertRateUnqualifiedData() {
        List<BigDecimal> unqualifiedRate1 = getListOfRates(200, 201, 202, 203, 204, 205, 206);
        List<BigDecimal> unqualifiedRate2 = getListOfRates(180, 181, 182, 183, 184, 185, 186);
        List<BigDecimal> unqualifiedRate3 = getListOfRates(160, 161, 162, 163, 164, 165, 166);
        List<BigDecimal> unqualifiedRate4 = getListOfRates(140, 141, 142, 143, 144, 145, 146);
        List<BigDecimal> unqualifiedRate5 = getListOfRates(120, 121, 122, 123, 124, 125, 126);
        List<BigDecimal> unqualifiedRate6 = getListOfRates(100, 101, 102, 103, 104, 105, 106);
        List<BigDecimal> unqualifiedRate7 = getListOfRates(90, 91, 92, 93, 94, 95, 96);
        andUnqualifiedRatePlanDataIsInserted(1, "UQR1", rateStartDateInStr, rateEndDateInStr)
                .andUnqualifiedRatePlanDataIsInserted(2, "UQR2", rateStartDateInStr, rateEndDateInStr)
                .andUnqualifiedRatePlanDataIsInserted(3, "UQR3", rateStartDateInStr, rateEndDateInStr)
                .andUnqualifiedRatePlanDataIsInserted(4, "UQR4", rateStartDateInStr, rateEndDateInStr)
                .andUnqualifiedRatePlanDataIsInserted(5, "UQR5", rateStartDateInStr, rateEndDateInStr)
                .andUnqualifiedRatePlanDataIsInserted(6, "UQR6", rateStartDateInStr, rateEndDateInStr)
                .andUnqualifiedRatePlanDataIsInserted(7, "UQR7", rateStartDateInStr, rateEndDateInStr)
                .andUnqualifiedRatePlanDetailsIsInseretd(1, NON_MASTER_ACCOM_TYPE_Q, rateStartDateInStr, rateEndDateInStr, unqualifiedRate1)
                .andUnqualifiedRatePlanDetailsIsInseretd(2, NON_MASTER_ACCOM_TYPE_Q, rateStartDateInStr, rateEndDateInStr, unqualifiedRate2)
                .andUnqualifiedRatePlanDetailsIsInseretd(3, NON_MASTER_ACCOM_TYPE_Q, rateStartDateInStr, rateEndDateInStr, unqualifiedRate3)
                .andUnqualifiedRatePlanDetailsIsInseretd(4, NON_MASTER_ACCOM_TYPE_Q, rateStartDateInStr, rateEndDateInStr, unqualifiedRate4)
                .andUnqualifiedRatePlanDetailsIsInseretd(5, NON_MASTER_ACCOM_TYPE_Q, rateStartDateInStr, rateEndDateInStr, unqualifiedRate5)
                .andUnqualifiedRatePlanDetailsIsInseretd(6, NON_MASTER_ACCOM_TYPE_Q, rateStartDateInStr, rateEndDateInStr, unqualifiedRate6)
                .andUnqualifiedRatePlanDetailsIsInseretd(7, NON_MASTER_ACCOM_TYPE_Q, rateStartDateInStr, rateEndDateInStr, unqualifiedRate7)
                .andUnqualifiedRatePlanDetailsIsInseretd(1, NON_MASTER_ACCOM_TYPE_DBL, rateStartDateInStr, rateEndDateInStr, unqualifiedRate1)
                .andUnqualifiedRatePlanDetailsIsInseretd(2, NON_MASTER_ACCOM_TYPE_DBL, rateStartDateInStr, rateEndDateInStr, unqualifiedRate1)
                .andUnqualifiedRatePlanDetailsIsInseretd(3, NON_MASTER_ACCOM_TYPE_DBL, rateStartDateInStr, rateEndDateInStr, unqualifiedRate1)
                .andUnqualifiedRatePlanDetailsIsInseretd(4, NON_MASTER_ACCOM_TYPE_DBL, rateStartDateInStr, rateEndDateInStr, unqualifiedRate4)
                .andUnqualifiedRatePlanDetailsIsInseretd(5, NON_MASTER_ACCOM_TYPE_DBL, rateStartDateInStr, rateEndDateInStr, unqualifiedRate4)
                .andUnqualifiedRatePlanDetailsIsInseretd(6, NON_MASTER_ACCOM_TYPE_DBL, rateStartDateInStr, rateEndDateInStr, unqualifiedRate4)
                .andUnqualifiedRatePlanDetailsIsInseretd(7, NON_MASTER_ACCOM_TYPE_DBL, rateStartDateInStr, rateEndDateInStr, unqualifiedRate7);
        return this;
    }


    private SRPBasedDecisionQualifiedFPLOSBatchUpdaterDataTest insertRateQualifiedData() {
        andQualifiedRatePlanDataIsInserted(1, SPECIAL_RATE_PLAN, rateStartDateInStr, rateEndDateInStr, 2)
                .andQualifiedRatePlanDetailsIsInsertedForAccomType(1, QUEEN_ACCOM_TYPE, rateStartDateInStr, rateEndDateInStr,
                        valueOf(10), valueOf(11), valueOf(12),
                        valueOf(13), valueOf(14), valueOf(15), valueOf(16));
        return this;
    }

    private SRPBasedDecisionQualifiedFPLOSBatchUpdaterDataTest insertRateQualifiedLv0Data(String ratePlanLv0, int rateQualifiedId) {
        andQualifiedRatePlanDataIsInserted(rateQualifiedId, ratePlanLv0, rateStartDateInStr, rateEndDateInStr, 2)
                .andQualifiedRatePlanDetailsIsInsertedForAccomType(rateQualifiedId, QUEEN_ACCOM_TYPE, rateStartDateInStr, rateEndDateInStr,
                        valueOf(10), valueOf(11), valueOf(12),
                        valueOf(13), valueOf(14), valueOf(15), valueOf(16));
        return this;
    }

    private void setToggleValues() {
        when(pacmanConfigParamsService.getParameterValue(PreProductionConfigParamName.DERIVED_RATES_FPLOS_CORRECTION_ENABLED)).thenReturn(Boolean.TRUE);
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.IS_DERIVED_QUALIFIED_RATE_PLAN_ENABLED.value())).thenReturn(true);
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.PROFIT_OPTIMIZATION_ENABLED)).thenReturn(false);
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.HIGHEST_BAR_RESTRICTED_ENABLED)).thenReturn(false);
        when(servicingCostByLOSService.getTransientServicingCostConfigurations(any())).thenReturn(Collections.emptyList());
    }

    private List<BigDecimal> getListOfRates(int i, int i2, int i3, int i4, int i5, int i6, int i7) {
        return Arrays.asList(valueOf(i), valueOf(i2), valueOf(i3),
                valueOf(i4), valueOf(i5), valueOf(i6), valueOf(i7));
    }

    private SRPBasedDecisionQualifiedFPLOSBatchUpdaterDataTest prepateRateAdjustmentData(int adjustmentID, int rateQualifiedId, Date startDate, Date endDate, int postingRuleId, Integer netValue, int netValueTypeId, String adjustmentType) {
        tenantCrudService().executeUpdateByNativeQuery("delete from Rate_Qualified_Adjustment where Rate_Qualified_Adjustment_ID = " + adjustmentID);
        StringBuilder queryStringBuilder = new StringBuilder();
        queryStringBuilder.append("SET IDENTITY_INSERT [Rate_Qualified_Adjustment] ON;")
                .append("INSERT INTO [dbo].[Rate_Qualified_Adjustment] ([Rate_Qualified_Adjustment_ID],[Rate_Qualified_ID] ,[Start_Date_DT] ,[End_Date_DT] ,[Posting_Rule_ID] ,[Net_Value] ,[Net_Value_Type_ID] ,[CreateDate_DTTM] ,[AdjustmentType])")
                .append(" VALUES (").append(adjustmentID).append(",")
                .append(rateQualifiedId).append(",'")
                .append(DateUtil.formatDate(startDate, DateUtil.DATE_TIME_FORMAT)).append("','")
                .append(DateUtil.formatDate(endDate, DateUtil.DATE_TIME_FORMAT)).append("',")
                .append(postingRuleId).append(",")
                .append(netValue).append(",")
                .append(netValueTypeId).append(",")
                .append("getdate(),'")
                .append(adjustmentType).append("');")
                .append("SET IDENTITY_INSERT [Rate_Qualified_Adjustment] OFF;");

        tenantCrudService().executeUpdateByNativeQuery(queryStringBuilder.toString());
        return this;
    }

    @Test
    public void createAndPopulateChannelRestrictionsAdjTable() {
        tenantCrudService().executeUpdateByNativeQuery("delete from Rate_Qualified; " +
                "insert into Rate_Qualified values (1,5,'SRP1','','','USD',getDAte(),GETDATE(),0,1,'',0,GETDATE(),1,1,GETDATE(),1,1,0);\n" +
                "insert into Rate_Qualified values (1,5,'SRP2','','','USD',getDAte(),GETDATE(),0,1,'',0,GETDATE(),1,1,GETDATE(),1,1,0);\n" +
                "insert into Rate_Qualified values (1,5,'SRP3','','','USD',getDAte(),GETDATE(),0,1,'',0,GETDATE(),1,1,GETDATE(),1,1,0);" +
                "delete from Channel_Restriction_Adjustment;delete from channel_srp_group_included_srp_mapping;" +
                "insert into Channel_Restriction_Adjustment values (0,'SRP_GRP_1',8,0,2,9,0,3,1,0,4,1,0,5,2,0,6,3,0,7,4,0,8,GETDATE(),GETDATE(),1,GETDATE(),1,GETDATE(),'H1');\n" +
                "insert into Channel_Restriction_Adjustment values (0,'SRP_GRP_2',1,0,12,2,0,13,4,0,14,3,0,15,7,0,16,6,0,17,5,0,18,GETDATE(),GETDATE(),1,GETDATE(),1,GETDATE(),'H2');\n" +
                "insert into channel_srp_group_included_srp_mapping values('SRP_GRP_2','SRP2',GETDATE(),GETDATE(),(select Channel_Restriction_Adjustment_ID from Channel_Restriction_Adjustment where SRP_Group_Code='SRP_GRP_2'));\n" +
                "insert into channel_srp_group_included_srp_mapping values('SRP_GRP_1','SRP1',GETDATE(),GETDATE(),(select Channel_Restriction_Adjustment_ID from Channel_Restriction_Adjustment where SRP_Group_Code='SRP_GRP_1'));");

        final List<String> rateQualifiedIds = tenantCrudService().findByNativeQuery("select Rate_Qualified_Id from Rate_Qualified;");
        SRPBasedDecisionQualifiedFPLOSBatchUpdater updater = new SRPBasedDecisionQualifiedFPLOSBatchUpdater(getDecisionQualifiedFPLOSBatchUpdaterBean());
        updater.propertyId = 5;
        final LocalDate date = LocalDate.of(2022, 02, 23);
        final List<Object[]> list = tenantCrudService().findByNativeQuery(updater.createRateQualifiedTempTable() + updater.createAndPopulateChannelRestrictionsAdjTable() + "select * from tempdb..#5_ChannelRestrictionsAdj;"
                , QueryParameter.with(START_DATE, date)
                        .and(END_DATE, date)
                        .and("rateQualifiedIds", StringUtils.join(rateQualifiedIds, ",")).parameters());
        assertEquals(2, list.size());
        assertEquals(getRateQualifiedID("SRP1"), (Integer) list.get(0)[0]);
        assertEquals(java.sql.Date.valueOf(date), list.get(0)[1]);
        assertEquals(valueOf(5).setScale(5), list.get(0)[2]);
        assertEquals(valueOf(1).setScale(5), list.get(0)[3]);
        assertEquals(valueOf(2).setScale(5), list.get(0)[4]);
        assertEquals(valueOf(3).setScale(5), list.get(0)[5]);
        assertEquals(valueOf(4).setScale(5), list.get(0)[6]);
        assertEquals(valueOf(8).setScale(5), list.get(0)[7]);
        assertEquals(valueOf(9).setScale(5), list.get(0)[8]);
        assertEquals(valueOf(1).setScale(5), list.get(0)[9]);
        assertEquals(getRateQualifiedID("SRP2"), (Integer) list.get(1)[0]);
        assertEquals(java.sql.Date.valueOf(date), list.get(1)[1]);
        assertEquals(valueOf(15).setScale(5), list.get(1)[2]);
        assertEquals(valueOf(3).setScale(5), list.get(1)[3]);
        assertEquals(valueOf(7).setScale(5), list.get(1)[4]);
        assertEquals(valueOf(6).setScale(5), list.get(1)[5]);
        assertEquals(valueOf(5).setScale(5), list.get(1)[6]);
        assertEquals(valueOf(1).setScale(5), list.get(1)[7]);
        assertEquals(valueOf(2).setScale(5), list.get(1)[8]);
        assertEquals(valueOf(4).setScale(5), list.get(1)[9]);
        tenantCrudService().executeUpdateByNativeQuery("  IF OBJECT_ID('tempdb..#5_RateQualifiedTable') IS NOT NULL DROP TABLE #5_RateQualifiedTable;" +
                "IF OBJECT_ID('tempdb..#5_ChannelRestrictionsAdj') IS NOT NULL DROP TABLE #5_ChannelRestrictionsAdj; " +
                "delete from Channel_Restriction_Adjustment;" +
                "delete from channel_srp_group_included_srp_mapping;");
    }

    private Integer getRateQualifiedID(final String rateCodeName) {
        return tenantCrudService().findByNativeQuerySingleResult("select Rate_Qualified_ID from Rate_Qualified where Rate_Code_Name = '" + rateCodeName + "'", null);
    }

    private DecisionQualifiedFPLOSBatchUpdaterBean getDecisionQualifiedFPLOSBatchUpdaterBean() {
        return new DecisionQualifiedFPLOSBatchUpdaterBean()
                .setRateQualifiedIds(Collections.emptyList())
                .setConnection(mock(Connection.class))
                .setLimitTotalSRPRatesEnabled(true)
                .setBarMaxLos(7);
    }

    @Test
    void updateChannelRestrictionsAdjustedRates_DataTest() {
        String string = "CREATE TABLE #temp_qualified_fplos_pid_5_LRA_1(\n" +
                "\n" +
                "    [Arrival_DT] [date] NOT NULL\n" +
                "    ,[Accom_Type_ID] [int] NOT NULL\n" +
                "    ,[Rate_Qualified_id] [int] NOT NULL\n" +
                "    ,[Rate1] [numeric](19,5) NOT NULL\n" +
                "    ,[Rate2] [numeric](19,5) NOT NULL\n" +
                "    ,[Rate3] [numeric](19,5) NOT NULL\n" +
                "    ,[Rate4] [numeric](19,5) NOT NULL\n" +
                "    ,[Rate5] [numeric](19,5) NOT NULL\n" +
                "    ,[Rate6] [numeric](19,5) NOT NULL\n" +
                "    ,[Rate7] [numeric](19,5) NOT NULL\n" +
                ");" +
                "insert into #temp_qualified_fplos_pid_5_LRA_1 values " +
                " ('2022-01-01', 4, (select Rate_Qualified_ID from Rate_Qualified where Rate_Code_Name = 'SRP1'), 100,200,300,400,500,600,700)," +
                " ('2022-01-02', 5, (select Rate_Qualified_ID from Rate_Qualified where Rate_Code_Name = 'SRP2'), 110,120,130,140,150,160,170), " +
                " ('2022-01-03', 6, (select Rate_Qualified_ID from Rate_Qualified where Rate_Code_Name = 'SRP3'), 21,22,23,24,25,26,27);";

        tenantCrudService().executeUpdateByNativeQuery("delete from Rate_Qualified; " +
                "insert into Rate_Qualified values (1,5,'SRP1','','','USD',getDAte(),GETDATE(),0,1,'',0,GETDATE(),1,1,GETDATE(),1,1,0);\n" +
                "insert into Rate_Qualified values (1,5,'SRP2','','','USD',getDAte(),GETDATE(),0,1,'',0,GETDATE(),1,1,GETDATE(),1,1,0);\n" +
                "insert into Rate_Qualified values (1,5,'SRP3','','','USD',getDAte(),GETDATE(),0,1,'',0,GETDATE(),1,1,GETDATE(),1,1,0);" +
                "delete from Channel_Restriction_Adjustment;delete from channel_srp_group_included_srp_mapping;" +
                "insert into Channel_Restriction_Adjustment values (0,'SRP_GRP_1',8,0,2,9,0,3,1,0,4,1,0,5,2,0,6,3,0,7,4,0,8,GETDATE(),GETDATE(),1,GETDATE(),1,GETDATE(),'H1');\n" +
                "insert into Channel_Restriction_Adjustment values (0,'SRP_GRP_2',1,0,12,2,0,13,null,0,14,3,0,15,7,0,16,6,0,17,5,0,18,GETDATE(),GETDATE(),1,GETDATE(),1,GETDATE(),'H2');\n" +
                "insert into channel_srp_group_included_srp_mapping values('SRP_GRP_2','SRP2',GETDATE(),GETDATE(),(select Channel_Restriction_Adjustment_ID from Channel_Restriction_Adjustment where SRP_Group_Code='SRP_GRP_2'));\n" +
                "insert into channel_srp_group_included_srp_mapping values('SRP_GRP_1','SRP1',GETDATE(),GETDATE(),(select Channel_Restriction_Adjustment_ID from Channel_Restriction_Adjustment where SRP_Group_Code='SRP_GRP_1'));");

        final List<String> rateQualifiedIds = tenantCrudService().findByNativeQuery("select Rate_Qualified_Id from Rate_Qualified;");
        SRPBasedDecisionQualifiedFPLOSBatchUpdater updater = new SRPBasedDecisionQualifiedFPLOSBatchUpdater(getDecisionQualifiedFPLOSBatchUpdaterBean());
        updater.propertyId = 5;
        final LocalDate startDate = LocalDate.of(2022, 01, 01);
        final LocalDate startDatePlus1 = LocalDate.of(2022, 01, 02);
        final LocalDate endDate = LocalDate.of(2022, 01, 03);
        final List<Object[]> list = tenantCrudService().findByNativeQuery(updater.createRateQualifiedTempTable() + updater.createAndPopulateChannelRestrictionsAdjTable() +
                        string +
                        new ChannelRestrictionsAdjQueryBuilder().updateChannelRestrictionsAdjustedRates("#temp_qualified_fplos_pid_5_LRA_1", "Rate", 7, 5) +
                        "select * from tempdb..#temp_qualified_fplos_pid_5_LRA_1;"
                , QueryParameter.with(START_DATE, startDate)
                        .and(END_DATE, endDate)
                        .and("rateQualifiedIds", StringUtils.join(rateQualifiedIds, ",")).parameters());

        assertEquals(3, list.size());

        assertResults(startDate, list.get(0), "SRP1", 4, 88, 184, 273, 396, 495, 588, 679);
        assertResults(startDatePlus1, list.get(1), "SRP2", 5, 96.9, 117.6, 130, 135.8, 139.5, 150.4, 161.5);
        assertResults(endDate, list.get(2), "SRP3", 6, 21, 22, 23, 24, 25, 26, 27);

        tenantCrudService().executeUpdateByNativeQuery("  IF OBJECT_ID('tempdb..#5_RateQualifiedTable') IS NOT NULL DROP TABLE #5_RateQualifiedTable;" +
                "IF OBJECT_ID('tempdb..#5_ChannelRestrictionsAdj') IS NOT NULL DROP TABLE #5_ChannelRestrictionsAdj; " +
                "IF OBJECT_ID('tempdb..#temp_qualified_fplos_pid_5_LRA_1') IS NOT NULL DROP TABLE #temp_qualified_fplos_pid_5_LRA_1; " +
                "delete from Channel_Restriction_Adjustment;" +
                "delete from channel_srp_group_included_srp_mapping;");
    }

    @Test
    void updateChannelRestrictionsAdjustedRatesForVP_DataTest() {
        String string = "CREATE TABLE #temp_qualified_fplos_pid_5_LRA_1(\n" +
                "\n" +
                "    [Arrival_DT] [date] NOT NULL\n" +
                "    ,[Accom_Type_ID] [int] NOT NULL\n" +
                "    ,[Rate_Qualified_id] [int] NOT NULL\n" +
                "    ,[Rate1] [numeric](19,5) NOT NULL\n" +
                "    ,[Rate2] [numeric](19,5) NOT NULL\n" +
                "    ,[Rate3] [numeric](19,5) NOT NULL\n" +
                "    ,[Rate4] [numeric](19,5) NOT NULL\n" +
                "    ,[Rate5] [numeric](19,5) NOT NULL\n" +
                "    ,[Rate6] [numeric](19,5) NOT NULL\n" +
                "    ,[Rate7] [numeric](19,5) NOT NULL\n" +
                ");" +
                "insert into #temp_qualified_fplos_pid_5_LRA_1 values " +
                " ('2022-01-01', 4, (select Rate_Qualified_ID from Rate_Qualified where Rate_Code_Name = 'VP1_SRP1'), 100,200,300,400,500,600,700)," +
                " ('2022-01-02', 5, (select Rate_Qualified_ID from Rate_Qualified where Rate_Code_Name = 'VP2_SRP1'), 110,120,130,140,150,160,170), " +
                " ('2022-01-03', 6, (select Rate_Qualified_ID from Rate_Qualified where Rate_Code_Name = 'SRP3'), 21,22,23,24,25,26,27);";

        tenantCrudService().executeUpdateByNativeQuery("delete from Rate_Qualified; " +
                "insert into Rate_Qualified values (1,5,'VP1_SRP1','','','USD',getDAte(),GETDATE(),0,1,'',0,GETDATE(),1,1,GETDATE(),1,1,0);\n" +
                "insert into Rate_Qualified values (1,5,'VP2_SRP1','','','USD',getDAte(),GETDATE(),0,1,'',0,GETDATE(),1,1,GETDATE(),1,1,0);\n" +
                "insert into Rate_Qualified values (1,5,'SRP3','','','USD',getDAte(),GETDATE(),0,1,'',0,GETDATE(),1,1,GETDATE(),1,1,0);" +
                "delete from Channel_Restriction_Adjustment;delete from channel_srp_group_included_srp_mapping;" +
                "insert into Channel_Restriction_Adjustment values (0,'SRP_GRP_1',8,0,2,9,0,3,1,0,4,1,0,5,2,0,6,3,0,7,4,0,8,GETDATE(),GETDATE(),1,GETDATE(),1,GETDATE(),'VP1');\n" +
                "insert into Channel_Restriction_Adjustment values (0,'SRP_GRP_1',1,0,12,2,0,13,null,0,14,3,0,15,7,0,16,6,0,17,5,0,18,GETDATE(),GETDATE(),1,GETDATE(),1,GETDATE(),'VP2');\n" +
                "insert into channel_srp_group_included_srp_mapping values('SRP_GRP_1','VP1_SRP1',GETDATE(),GETDATE(),(select Channel_Restriction_Adjustment_ID from Channel_Restriction_Adjustment where SRP_Group_Code='SRP_GRP_1' AND Property_Code='VP1'));\n" +
                "insert into channel_srp_group_included_srp_mapping values('SRP_GRP_1','VP2_SRP1',GETDATE(),GETDATE(),(select Channel_Restriction_Adjustment_ID from Channel_Restriction_Adjustment where SRP_Group_Code='SRP_GRP_1' AND Property_Code='VP2'));");

        final List<String> rateQualifiedIds = tenantCrudService().findByNativeQuery("select Rate_Qualified_Id from Rate_Qualified;");
        SRPBasedDecisionQualifiedFPLOSBatchUpdater updater = new SRPBasedDecisionQualifiedFPLOSBatchUpdater(getDecisionQualifiedFPLOSBatchUpdaterBean());
        updater.propertyId = 5;
        final LocalDate startDate = LocalDate.of(2022, 01, 01);
        final LocalDate startDatePlus1 = LocalDate.of(2022, 01, 02);
        final LocalDate endDate = LocalDate.of(2022, 01, 03);
        final List<Object[]> list = tenantCrudService().findByNativeQuery(updater.createRateQualifiedTempTable() + updater.createAndPopulateChannelRestrictionsAdjTable() +
                        string +
                        new ChannelRestrictionsAdjQueryBuilder().updateChannelRestrictionsAdjustedRates("#temp_qualified_fplos_pid_5_LRA_1", "Rate", 7, 5) +
                        "select * from tempdb..#temp_qualified_fplos_pid_5_LRA_1;"
                , QueryParameter.with(START_DATE, startDate)
                        .and(END_DATE, endDate)
                        .and("rateQualifiedIds", StringUtils.join(rateQualifiedIds, ",")).parameters());

        assertEquals(3, list.size());

        assertResults(startDate, list.get(0), "VP1_SRP1", 4, 88, 184, 273, 396, 495, 588, 679);
        assertResults(startDatePlus1, list.get(1), "VP2_SRP1", 5, 96.9, 117.6, 130, 135.8, 139.5, 150.4, 161.5);
        assertResults(endDate, list.get(2), "SRP3", 6, 21, 22, 23, 24, 25, 26, 27);

        tenantCrudService().executeUpdateByNativeQuery("  IF OBJECT_ID('tempdb..#5_RateQualifiedTable') IS NOT NULL DROP TABLE #5_RateQualifiedTable;" +
                "IF OBJECT_ID('tempdb..#5_ChannelRestrictionsAdj') IS NOT NULL DROP TABLE #5_ChannelRestrictionsAdj; " +
                "IF OBJECT_ID('tempdb..#temp_qualified_fplos_pid_5_LRA_1') IS NOT NULL DROP TABLE #temp_qualified_fplos_pid_5_LRA_1; " +
                "delete from Channel_Restriction_Adjustment;" +
                "delete from channel_srp_group_included_srp_mapping;");
    }

    private void assertResults(LocalDate date, Object[] record, String rateCode, int accomTypeID, double rate1, double rate2, double rate3, double rate4, double rate5, double rate6, double rate7) {
        assertEquals(java.sql.Date.valueOf(date), record[0]);
        assertEquals(accomTypeID, (Integer) record[1]);
        assertEquals(getRateQualifiedID(rateCode), (Integer) record[2]);
        assertEquals(valueOf(rate1).setScale(5), record[3]);
        assertEquals(valueOf(rate2).setScale(5), record[4]);
        assertEquals(valueOf(rate3).setScale(5), record[5]);
        assertEquals(valueOf(rate4).setScale(5), record[6]);
        assertEquals(valueOf(rate5).setScale(5), record[7]);
        assertEquals(valueOf(rate6).setScale(5), record[8]);
        assertEquals(valueOf(rate7).setScale(5), record[9]);
    }

    @Test
    public void createAndPopulateQualifiedRateMasterClassTable() {
        tenantCrudService().executeUpdateByNativeQuery("delete from Rate_Qualified; " +
                "insert into Rate_Qualified values (1,5,'ABC_SRP1','','','USD',getDAte(),GETDATE(),0,1,'',0,GETDATE(),1,1,GETDATE(),1,1,0);\n" +
                "insert into Rate_Qualified values (1,5,'ABC_SRP2','','','USD',getDAte(),GETDATE(),0,1,'',0,GETDATE(),1,1,GETDATE(),1,1,0);\n" +
                "insert into Rate_Qualified values (1,5,'XYZ_SRP3','','','USD',getDAte(),GETDATE(),0,1,'',0,GETDATE(),1,1,GETDATE(),1,1,0);\n" +
                "delete from Decision_LRV;\n" +
                "insert into Decision_LRV values ((select max(Decision_ID) from Decision), 5, 2, getDate(), 22, getdate(),null,null)\n" +
                "insert into Decision_LRV values ((select max(Decision_ID) from Decision), 5, 4, getDate(), 42, getdate(),null,null);\n");
        EffectiveLRVQueryBuilder effectiveLRVQueryBuilder = new EffectiveLRVQueryBuilder();
        SRPBasedDecisionQualifiedFPLOSBatchUpdater updater = new SRPBasedDecisionQualifiedFPLOSBatchUpdater(getDecisionQualifiedFPLOSBatchUpdaterBean());
        updater.propertyId = 5;
        Map<String, Integer> physicalPropertyMasterClassMap = Map.of("ABC", 2, "XYZ", 4);
        updater.physicalPropertyMasterClassMap = physicalPropertyMasterClassMap;
        updater.usePhysicalPropertyMasterClass = true;
        StringBuilder query = new StringBuilder();
        query.append(effectiveLRVQueryBuilder.createEffectiveLRVTable("temp_qualified_fplos_LT_pid_5", 7));
        query.append(effectiveLRVQueryBuilder.getOptimizedQueryToPopulateEffectiveLRVTable("temp_qualified_fplos_LT_pid_5", 7, true, true, physicalPropertyMasterClassMap));
        query.append(updater.createIntermediateRateTable("temp_qualified_fplos_LT_pid_5", 7, true, false));
        query.append("insert into #temp_qualified_fplos_LT_pid_5_1a_rate_qualified values ")
                //accomtype,accomClass,RateQualifiedID
                .append(" (6,2,(select Rate_Qualified_ID from Rate_Qualified where Rate_Code_Name = 'ABC_SRP1'),'ABC_SRP1',getdate(),getdate(),10,10,10,10,10,10,10), ")
                .append(" (6,2,(select Rate_Qualified_ID from Rate_Qualified where Rate_Code_Name = 'ABC_SRP2'),'ABC_SRP2',getdate(),getdate(),11,11,11,11,11,11,11), ")
                .append(" (5,4,(select Rate_Qualified_ID from Rate_Qualified where Rate_Code_Name = 'XYZ_SRP3'),'XYZ_SRP3',getdate(),getdate(),12,12,12,12,12,12,12); ");
        query.append(updater.createAndPopulateQualifiedRateMasterClassTable("temp_qualified_fplos_LT_pid_5"));
        query.append(updater.populateRateAndLRV("temp_qualified_fplos_LT_pid_5", 7, true, false));
        query.append(" select * from #temp_qualified_fplos_LT_pid_5_1a;");
        String finalQuery = query.toString();
        finalQuery = finalQuery.replaceAll("hospitality_rooms_clause_aa", "");
        LocalDate localDate = LocalDate.now();
        List<Object[]> result = tenantCrudService().findByNativeQuery(finalQuery, QueryParameter.with(START_DATE, localDate)
                .and(END_DATE, localDate).and("propertyId", PROPERTY_ID).parameters());
        NumberFormat formatter = new DecimalFormat("#0.0");
        assertEquals(3, result.size());

        assertEquals(java.sql.Date.valueOf(localDate), result.get(0)[0]);
        assertEquals(5, result.get(0)[1]);
        assertEquals(tenantCrudService().findByNativeQuerySingleResult("select Rate_Qualified_ID from Rate_Qualified where Rate_Code_Name = 'XYZ_SRP3'", null), result.get(0)[2]);
        assertEquals(valueOf(42.0).setScale(1), ((BigDecimal) result.get(0)[4]).setScale(1));

        assertEquals(java.sql.Date.valueOf(localDate), result.get(1)[0]);
        assertEquals(6, result.get(1)[1]);
        assertEquals(tenantCrudService().findByNativeQuerySingleResult("select Rate_Qualified_ID from Rate_Qualified where Rate_Code_Name = 'ABC_SRP1'", null), result.get(1)[2]);
        assertEquals(valueOf(22.0).setScale(1), ((BigDecimal) result.get(1)[4]).setScale(1));

        assertEquals(java.sql.Date.valueOf(localDate), result.get(2)[0]);
        assertEquals(6, result.get(2)[1]);
        assertEquals(tenantCrudService().findByNativeQuerySingleResult("select Rate_Qualified_ID from Rate_Qualified where Rate_Code_Name = 'ABC_SRP2'", null), result.get(2)[2]);
        assertEquals(valueOf(22.0).setScale(1), ((BigDecimal) result.get(2)[4]).setScale(1));
    }

}


