package com.ideas.tetris.pacman.services.pricingconfiguration.service;

import com.ideas.g3.test.AbstractG3JupiterTest;
import com.ideas.recommendation.model.CPDecisionBarOutputs;
import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.configparams.GUIConfigParamName;
import com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.accommodation.entity.*;
import com.ideas.tetris.pacman.services.agilerates.configuration.dto.IndependentProductConfigurationDTO;
import com.ideas.tetris.pacman.services.agilerates.configuration.entity.ProductAccomType;
import com.ideas.tetris.pacman.services.agilerates.configuration.entity.ProductPackage;
import com.ideas.tetris.pacman.services.agilerates.configuration.service.AgileRatesConfigurationService;
import com.ideas.tetris.pacman.services.bestavailablerate.AccomTypeSupplementService;
import com.ideas.tetris.pacman.services.bestavailablerate.CPDecisionContext;
import com.ideas.tetris.pacman.services.bestavailablerate.PrettyPricingService;
import com.ideas.tetris.pacman.services.bestavailablerate.PricingRule;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.*;
import com.ideas.tetris.pacman.services.centralrms.CentralRmsService;
import com.ideas.tetris.pacman.services.centralrms.SyncCentralRmsService;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.FileMetadata;
import com.ideas.tetris.pacman.services.configautomation.dto.AccomClassMinPriceDiffResponse;
import com.ideas.tetris.pacman.services.configautomation.dto.GPSyncBARCeilingFloorDTO;
import com.ideas.tetris.pacman.services.continuouspricing.CPTaxInclusiveMigrationService;
import com.ideas.tetris.pacman.services.currency.CurrencyService;
import com.ideas.tetris.pacman.services.datafeed.dto.pricingdata.MinPriceDifferentDTO;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.eventaggregator.SyncEvent;
import com.ideas.tetris.pacman.services.eventaggregator.SyncEventAggregatorService;
import com.ideas.tetris.pacman.services.grouppricing.configuration.entity.GroupPricingConfiguration;
import com.ideas.tetris.pacman.services.grouppricing.configuration.entity.GroupPricingConfigurationObjectMother;
import com.ideas.tetris.pacman.services.grouppricing.configuration.entity.GroupPricingEvaluationMethod;
import com.ideas.tetris.pacman.services.grouppricing.configuration.service.GroupPricingConfigurationService;
import com.ideas.tetris.pacman.services.override.InvalidateOverridesService;
import com.ideas.tetris.pacman.services.perpersonpricing.MaximumOccupantsEntity;
import com.ideas.tetris.pacman.services.perpersonpricing.OccupantBucketEntity;
import com.ideas.tetris.pacman.services.perpersonpricing.PerPersonPricingService;
import com.ideas.tetris.pacman.services.pricingconfiguration.dto.PricingConfigurationDTO;
import com.ideas.tetris.pacman.services.pricingconfiguration.entity.*;
import com.ideas.tetris.pacman.services.product.Product;
import com.ideas.tetris.pacman.services.product.ProductType;
import com.ideas.tetris.pacman.services.sas.entity.SASMacrosParameters;
import com.ideas.tetris.pacman.services.tax.entity.Tax;
import com.ideas.tetris.pacman.services.tax.service.TaxService;
import com.ideas.tetris.platform.common.contextholder.PacmanThreadLocalContextHolder;
import com.ideas.tetris.platform.common.crudservice.AbstractMultiPropertyCrudService;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.crudservice.RowMapper;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.common.job.JobName;
import com.ideas.tetris.platform.common.time.JavaLocalDateUtils;
import com.ideas.tetris.platform.common.time.LocalDateUtils;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import com.ideas.tetris.platform.services.daoandentities.entity.Status;
import com.ideas.tetris.platform.services.job.JobService;
import org.joda.time.LocalDate;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.ZoneId;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName.CENTRAL_RMS_AVAILABLE;
import static com.ideas.tetris.pacman.services.sas.entity.SASMacrosParameters.createParameterInsertQuery;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@MockitoSettings(strictness = Strictness.LENIENT)
public class PricingConfigurationServiceTest extends AbstractG3JupiterTest {

    @Mock
    private CrudService tenantCrudService;

    @Mock
    AbstractMultiPropertyCrudService multiPropertyCrudService;

    @Mock
    private PacmanConfigParamsService configParamsService;

    @Mock
    private SyncEventAggregatorService syncEventAggregatorService;

    @Mock
    private InvalidateOverridesService invalidateOverridesService;

    @Mock
    private GroupPricingConfigurationService groupPricingConfigurationService;

    @Mock
    private PrettyPricingService prettyPricingService;

    @Mock
    private TaxService taxService;

    @Mock
    private AccomTypeSupplementService accomTypeSupplementService;

    @Mock
    private CPTaxInclusiveMigrationService taxInclusiveMigrationService;

    @Mock
    private DateService dateService;

    @Mock
    private AgileRatesConfigurationService agileRatesConfigurationService;

    @Mock
    private SyncCentralRmsService syncCentralRmsService;

    @InjectMocks
    private PricingConfigurationService service;

    @Captor
    ArgumentCaptor<Collection> captor;

    @Mock
    private JobService jobService;

    @Mock
    private PerPersonPricingService perPersonPricingService;

    @Mock
    private CurrencyService currencyService;

    @Mock
    private PricingConfigurationLTBDEService pricingConfigurationLTBDEService;

    @Mock
    private CentralRmsService centralRmsService;

    private CrudService tenantCrudServiceReal = tenantCrudService();

    private final String PROPERTY_ID = "propertyId";
    private final String ACCOM_TYPES = "accomTypes";
    private static final PricingConfigurationDTO pricingConfigurationDTO = getPricingConfigurationDTO();

    private static PricingConfigurationDTO getPricingConfigurationDTO() {
        IndependentProductConfigurationDTO independentProductConfigurationDTO = new IndependentProductConfigurationDTO();
        Product product = new Product();
        product.setId(1);
        independentProductConfigurationDTO.setProduct(product);
        return new PricingConfigurationDTO(independentProductConfigurationDTO,false);
    }

    @Test
    public void getPricingAccomClasses() {
        PricingAccomClass pricingAccomClass = PricingConfigurationObjectMother.buildPricingAccomClass();

        Map<String, Object> queryParams = getQueryParamMapForPropertyID();

        when(multiPropertyCrudService.findByNamedQueryForSingleProperty(PacmanWorkContextHelper.getPropertyId(), PricingAccomClass.FIND_BY_PROPERTY_ID, queryParams))
                .thenReturn(Arrays.asList(pricingAccomClass));

        List<PricingAccomClass> pricingAccomClasses = service.getPricingAccomClasses();
        assertEquals(Arrays.asList(pricingAccomClass), pricingAccomClasses);
        verify(multiPropertyCrudService).findByNamedQueryForSingleProperty(PacmanWorkContextHelper.getPropertyId(), PricingAccomClass.FIND_BY_PROPERTY_ID, queryParams);
    }

    @Test
    public void getPricingAccomClasses_notConfigured() {
        Map<String, Object> queryParams = getQueryParamMapForPropertyID();

        when(multiPropertyCrudService.findByNamedQueryForSingleProperty(PacmanWorkContextHelper.getPropertyId(), PricingAccomClass.FIND_BY_PROPERTY_ID, queryParams))
                .thenReturn(new ArrayList());

        List<PricingAccomClass> pricingAccomClasses = service.getPricingAccomClasses();
        assertEquals(new ArrayList<>(), pricingAccomClasses);
        verify(multiPropertyCrudService, times(1)).findByNamedQueryForSingleProperty(PacmanWorkContextHelper.getPropertyId(), PricingAccomClass.FIND_BY_PROPERTY_ID, queryParams);

        when(multiPropertyCrudService.findByNamedQueryForSingleProperty(PacmanWorkContextHelper.getPropertyId(), PricingAccomClass.FIND_BY_PROPERTY_ID, queryParams))
                .thenReturn(null);

        List<PricingAccomClass> pricingAccomClasses2 = service.getPricingAccomClasses();
        assertEquals(new ArrayList<>(), pricingAccomClasses2);
        verify(multiPropertyCrudService, times(2)).findByNamedQueryForSingleProperty(PacmanWorkContextHelper.getPropertyId(), PricingAccomClass.FIND_BY_PROPERTY_ID, queryParams);
    }

    @Test
    public void getAllGroupPricingBaseAccomTypes() {
        PricingBaseAccomType pricingBaseAccomType = PricingConfigurationObjectMother.buildPricingBaseAccomType();

        Map<String, Object> queryParams = getQueryParamMapForPropertyID();

        when(multiPropertyCrudService.findByNamedQueryForSingleProperty(PacmanWorkContextHelper.getPropertyId(), GroupPricingBaseAccomType.FIND_BASE_AT_FOR_PRICEABLE_AC, queryParams))
                .thenReturn(Arrays.asList(pricingBaseAccomType));

        List<PricingBaseAccomType> pricingBaseAccomTypes = service.getAllGroupPricingBaseAccomTypes();
        assertEquals(Arrays.asList(pricingBaseAccomType), pricingBaseAccomTypes);
        verify(multiPropertyCrudService).findByNamedQueryForSingleProperty(PacmanWorkContextHelper.getPropertyId(), GroupPricingBaseAccomType.FIND_BASE_AT_FOR_PRICEABLE_AC, queryParams);
    }

    @Test
    public void getAllGroupPricingBaseAccomTypesWithPriceExcluded() {
        PricingBaseAccomType pricingBaseAccomType = PricingConfigurationObjectMother.buildPricingBaseAccomType();

        Map<String, Object> queryParams = getQueryParamMapForPropertyID();

        when(multiPropertyCrudService.findByNamedQueryForSingleProperty(PacmanWorkContextHelper.getPropertyId(), GroupPricingBaseAccomType.FIND_BASE_AT_FOR_PRICEABLE_AC_WITH_PRICE_EXCLUDED, queryParams))
                .thenReturn(Arrays.asList(pricingBaseAccomType));

        List<PricingBaseAccomType> pricingBaseAccomTypes = service.getAllGroupPricingBaseAccomTypesWithPriceExcluded();
        assertEquals(Arrays.asList(pricingBaseAccomType), pricingBaseAccomTypes);
        verify(multiPropertyCrudService).findByNamedQueryForSingleProperty(PacmanWorkContextHelper.getPropertyId(), GroupPricingBaseAccomType.FIND_BASE_AT_FOR_PRICEABLE_AC_WITH_PRICE_EXCLUDED, queryParams);
    }

    @Test
    public void getAllGroupPricingBaseAccomTypes_notConfigured() {
        Map<String, Object> queryParams = getQueryParamMapForPropertyID();

        when(multiPropertyCrudService.findByNamedQueryForSingleProperty(PacmanWorkContextHelper.getPropertyId(), GroupPricingBaseAccomType.FIND_BASE_AT_FOR_PRICEABLE_AC, queryParams))
                .thenReturn(new ArrayList());

        List<PricingBaseAccomType> pricingBaseAccomTypes = service.getAllGroupPricingBaseAccomTypes();
        assertEquals(new ArrayList<>(), pricingBaseAccomTypes);
        verify(multiPropertyCrudService, times(1)).findByNamedQueryForSingleProperty(PacmanWorkContextHelper.getPropertyId(), GroupPricingBaseAccomType.FIND_BASE_AT_FOR_PRICEABLE_AC, queryParams);

        when(multiPropertyCrudService.findByNamedQueryForSingleProperty(PacmanWorkContextHelper.getPropertyId(), GroupPricingBaseAccomType.FIND_BASE_AT_FOR_PRICEABLE_AC, queryParams))
                .thenReturn(null);

        List<PricingBaseAccomType> pricingBaseAccomTypes2 = service.getAllGroupPricingBaseAccomTypes();
        assertEquals(new ArrayList<>(), pricingBaseAccomTypes2);
        verify(multiPropertyCrudService, times(2)).findByNamedQueryForSingleProperty(PacmanWorkContextHelper.getPropertyId(), GroupPricingBaseAccomType.FIND_BASE_AT_FOR_PRICEABLE_AC, queryParams);
    }

    @Test
    public void getPricingAccomClassBaseRoomTypeMapping() {
        PricingAccomClass pricingAccomClass = PricingConfigurationObjectMother.buildPricingAccomClass();
        pricingAccomClass.getAccomClass().setName("STD");

        Map<String, Object> queryParams = getQueryParamMapForPropertyID();

        when(multiPropertyCrudService.findByNamedQueryForSingleProperty(PacmanWorkContextHelper.getPropertyId(), PricingAccomClass.FIND_BY_PROPERTY_ID, queryParams))
                .thenReturn(Arrays.asList(pricingAccomClass));

        Map<String, AccomType> pricingAccomClassBaseRoomTypeMapping = service.getPricingAccomClassBaseRoomTypeMapping();
        assertNotNull(pricingAccomClassBaseRoomTypeMapping.get("STD"));
    }

    @Test
    public void getNonExcludedPricingAccomClasses() {
        PricingAccomClass nonExcludedPricingAccomClass = PricingConfigurationObjectMother.buildPricingAccomClass();
        PricingAccomClass excludedPricingAccomClass = PricingConfigurationObjectMother.buildPricingAccomClass();
        excludedPricingAccomClass.setPriceExcluded(true);

        Map<String, Object> queryParams = getQueryParamMapForPropertyID();

        when(multiPropertyCrudService.findByNamedQueryForSingleProperty(PacmanWorkContextHelper.getPropertyId(), PricingAccomClass.FIND_BY_PROPERTY_ID, queryParams))
                .thenReturn(Arrays.asList(nonExcludedPricingAccomClass, excludedPricingAccomClass));

        List<PricingAccomClass> retPricingAccomClasses = service.getNonExcludedPricingAccomClasses(PacmanWorkContextHelper.getPropertyId());

        assertTrue(retPricingAccomClasses.size() == 1);
        assertSame(nonExcludedPricingAccomClass, retPricingAccomClasses.get(0));
        assertFalse(retPricingAccomClasses.get(0).isPriceExcluded());

        verify(multiPropertyCrudService).findByNamedQueryForSingleProperty(PacmanWorkContextHelper.getPropertyId(), PricingAccomClass.FIND_BY_PROPERTY_ID, queryParams);
    }

    @Test
    public void isAccomClassPriceExcluded_true() {
        PricingAccomClass pricingAccomClass = PricingConfigurationObjectMother.buildPricingAccomClass();
        AccomClass accomClass = new AccomClass();
        pricingAccomClass.setAccomClass(accomClass);
        pricingAccomClass.setPriceExcluded(true);

        Map<String, Object> queryParams = getQueryParamMapForPropertyID();

        when(multiPropertyCrudService.findByNamedQueryForSingleProperty(PacmanWorkContextHelper.getPropertyId(), PricingAccomClass.FIND_BY_PROPERTY_ID, queryParams))
                .thenReturn(Collections.singletonList(pricingAccomClass));

        assertTrue(service.isAccomClassPriceExcluded(accomClass));
    }

    @Test
    public void isAccomClassPriceExcluded_false() {
        PricingAccomClass pricingAccomClass = PricingConfigurationObjectMother.buildPricingAccomClass();
        AccomClass accomClass = new AccomClass();
        pricingAccomClass.setAccomClass(accomClass);
        pricingAccomClass.setPriceExcluded(false);

        Map<String, Object> queryParams = getQueryParamMapForPropertyID();

        when(tenantCrudService.findByNamedQuery(PricingAccomClass.FIND_BY_PROPERTY_ID, queryParams))
                .thenReturn(Collections.singletonList(pricingAccomClass));

        assertFalse(service.isAccomClassPriceExcluded(accomClass));

        //Different Accom Class
        pricingAccomClass.setAccomClass(new AccomClass());
        assertFalse(service.isAccomClassPriceExcluded(accomClass));
    }

    @Test
    public void savePricingAccomClasses() {
        List<PricingAccomClass> pricingAccomClasses = Collections.singletonList(PricingConfigurationObjectMother.buildPricingAccomClass());
        when(tenantCrudService.save(pricingAccomClasses)).thenReturn(pricingAccomClasses);
        when(tenantCrudService.executeUpdateByNamedQuery(PricingAccomClass.DELETE_BY_PROPERTY_ID,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters())).thenReturn(0);

        service.savePricingAccomClasses(pricingAccomClasses);

        verify(tenantCrudService).executeUpdateByNamedQuery(PricingAccomClass.DELETE_BY_PROPERTY_ID,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters());
        verify(tenantCrudService).save(pricingAccomClasses);
        verify(syncEventAggregatorService).registerSyncEvent(SyncEvent.PRICING_CONFIG_CHANGED);
    }

    @Test
    public void savePricingAccomClasses_BaseRoomTypeChanged() {
        List<PricingAccomClass> pricingAccomClasses = Collections.singletonList(PricingConfigurationObjectMother.buildPricingAccomClass());
        List<PricingBaseAccomType> pricingBaseAccomTypes = Collections.singletonList(PricingConfigurationObjectMother.buildPricingBaseAccomType());
        List<PricingBaseAccomType> transientPricingBaseAccomTypeSeasons = Collections.singletonList(PricingConfigurationObjectMother.buildPricingBaseAccomType());

        pricingAccomClasses.get(0).setAccomTypeChanged(true);

        Map<String, Object> accomClassQueryParams = getQueryParamMapForPropertyID();
        accomClassQueryParams.put("accomClassId", pricingAccomClasses.get(0).getAccomClass().getId());

        when(tenantCrudService.<PricingBaseAccomType>findByNamedQuery(TransientPricingBaseAccomType.FIND_BY_PROPERTY_AND_ACCOM_CLASS,
                accomClassQueryParams)).thenReturn(pricingBaseAccomTypes);
        when(tenantCrudService.executeUpdateByNamedQuery(PricingAccomClass.DELETE_BY_PROPERTY_ID,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters())).thenReturn(0);

        Mockito.doNothing().when(tenantCrudService).delete(pricingAccomClasses);
        when(tenantCrudService.save(pricingAccomClasses)).thenReturn(pricingAccomClasses);
        when(tenantCrudService.<PricingBaseAccomType>findByNamedQuery(TransientPricingBaseAccomType.FIND_PRICEABLE_SEASONS_BY_PROPERTY_ID,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters()))
                .thenReturn(transientPricingBaseAccomTypeSeasons);

        service.savePricingAccomClasses(pricingAccomClasses);

        verify(tenantCrudService).findByNamedQuery(TransientPricingBaseAccomType.FIND_BY_PROPERTY_AND_ACCOM_CLASS, accomClassQueryParams);
        verify(tenantCrudService).delete(pricingBaseAccomTypes);
        verify(tenantCrudService).executeUpdateByNamedQuery(PricingAccomClass.DELETE_BY_PROPERTY_ID, QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters());
        verify(tenantCrudService).save(pricingAccomClasses);
        verify(syncEventAggregatorService).registerSyncEvent(SyncEvent.PRICING_CONFIG_CHANGED);
        verify(tenantCrudService).save(any(TransientPricingBaseAccomType.class));
    }

    @Test
    public void savePricingAccomClasses_BaseRoomTypeChangedInvalidatesOverrides() {
        List<PricingAccomClass> pricingAccomClasses = Collections.singletonList(PricingConfigurationObjectMother.buildPricingAccomClass());
        List<PricingBaseAccomType> pricingBaseAccomTypes = Collections.singletonList(PricingConfigurationObjectMother.buildPricingBaseAccomType());
        List<PricingBaseAccomType> transientPricingBaseAccomTypeSeasons = Collections.singletonList(PricingConfigurationObjectMother.buildPricingBaseAccomType());

        pricingAccomClasses.get(0).setAccomTypeChanged(true);

        Map<String, Object> accomClassQueryParams = getQueryParamMapForPropertyID();
        accomClassQueryParams.put("accomClassId", pricingAccomClasses.get(0).getAccomClass().getId());

        when(configParamsService.getBooleanParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED)).thenReturn(true);
        when(tenantCrudService.<PricingBaseAccomType>findByNamedQuery(TransientPricingBaseAccomType.FIND_BY_PROPERTY_AND_ACCOM_CLASS,
                accomClassQueryParams)).thenReturn(pricingBaseAccomTypes);

        Mockito.doNothing().when(tenantCrudService).delete(pricingAccomClasses);
        when(tenantCrudService.save(pricingAccomClasses)).thenReturn(pricingAccomClasses);
        when(tenantCrudService.<PricingBaseAccomType>findByNamedQuery(TransientPricingBaseAccomType.FIND_PRICEABLE_SEASONS_BY_PROPERTY_ID,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters()))
                .thenReturn(transientPricingBaseAccomTypeSeasons);
        when(tenantCrudService.executeUpdateByNamedQuery(PricingAccomClass.DELETE_BY_PROPERTY_ID,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters())).thenReturn(0);

        service.savePricingAccomClasses(pricingAccomClasses);

        verify(tenantCrudService).findByNamedQuery(TransientPricingBaseAccomType.FIND_BY_PROPERTY_AND_ACCOM_CLASS, accomClassQueryParams);
        verify(tenantCrudService).delete(pricingBaseAccomTypes);
        verify(tenantCrudService).executeUpdateByNamedQuery(PricingAccomClass.DELETE_BY_PROPERTY_ID, QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters());
        verify(tenantCrudService).save(pricingAccomClasses);
        verify(syncEventAggregatorService).registerSyncEvent(SyncEvent.PRICING_CONFIG_CHANGED);
        verify(tenantCrudService).save(any(TransientPricingBaseAccomType.class));
        verify(invalidateOverridesService).invalidateCPOverrides(Collections.singletonList(pricingAccomClasses.get(0).getId()));
    }

    @Test
    public void shouldNotrunInvalidateCPOverrideWhenPricingAccomConfigChangesforNonCPProperty() {
        List<PricingAccomClass> pricingAccomClasses = Collections.singletonList(PricingConfigurationObjectMother.buildPricingAccomClass());
        List<PricingBaseAccomType> pricingBaseAccomTypes = Collections.singletonList(PricingConfigurationObjectMother.buildPricingBaseAccomType());
        List<PricingBaseAccomType> transientPricingBaseAccomTypeSeasons = Collections.singletonList(PricingConfigurationObjectMother.buildPricingBaseAccomType());

        pricingAccomClasses.get(0).setAccomTypeChanged(true);

        Map<String, Object> accomClassQueryParams = getQueryParamMapForPropertyID();
        accomClassQueryParams.put("accomClassId", pricingAccomClasses.get(0).getAccomClass().getId());

        when(configParamsService.getBooleanParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED.value())).thenReturn(false);
        when(tenantCrudService.<PricingBaseAccomType>findByNamedQuery(TransientPricingBaseAccomType.FIND_BY_PROPERTY_AND_ACCOM_CLASS,
                accomClassQueryParams)).thenReturn(pricingBaseAccomTypes);

        Mockito.doNothing().when(tenantCrudService).delete(pricingAccomClasses);
        when(tenantCrudService.save(pricingAccomClasses)).thenReturn(pricingAccomClasses);
        when(tenantCrudService.<PricingBaseAccomType>findByNamedQuery(TransientPricingBaseAccomType.FIND_PRICEABLE_SEASONS_BY_PROPERTY_ID,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters()))
                .thenReturn(transientPricingBaseAccomTypeSeasons);
        when(tenantCrudService.executeUpdateByNamedQuery(PricingAccomClass.DELETE_BY_PROPERTY_ID,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters())).thenReturn(0);
        Mockito.doNothing().when(invalidateOverridesService).invalidateCPOverrides(Collections.singletonList(pricingAccomClasses.get(0).getId()));

        service.savePricingAccomClasses(pricingAccomClasses);
        //Verify invalidateCP Override does not get called.
        verify(invalidateOverridesService, times(0)).invalidateCPOverrides(Collections.singletonList(pricingAccomClasses.get(0).getId()));
        verify(tenantCrudService).executeUpdateByNamedQuery(PricingAccomClass.DELETE_BY_PROPERTY_ID, QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters());
    }


    @Test
    public void savePricingAccomClasses_PriceExcludedChanged() {
        List<PricingAccomClass> pricingAccomClasses = Collections.singletonList(PricingConfigurationObjectMother.buildPricingAccomClass());
        List<PricingBaseAccomType> pricingBaseAccomTypes = Collections.singletonList(PricingConfigurationObjectMother.buildPricingBaseAccomType());
        List<PricingBaseAccomType> transientPricingBaseAccomTypeSeasons = Collections.singletonList(PricingConfigurationObjectMother.buildPricingBaseAccomType());

        pricingAccomClasses.get(0).setPriceExcludedChanged(true);

        Map<String, Object> accomClassQueryParams = getQueryParamMapForPropertyID();
        accomClassQueryParams.put("accomClassId", pricingAccomClasses.get(0).getAccomClass().getId());

        when(tenantCrudService.<PricingBaseAccomType>findByNamedQuery(TransientPricingBaseAccomType.FIND_BY_PROPERTY_AND_ACCOM_CLASS,
                accomClassQueryParams)).thenReturn(pricingBaseAccomTypes);
        when(tenantCrudService.<PricingBaseAccomType>findByNamedQuery(TransientPricingBaseAccomType.FIND_PRICEABLE_SEASONS_BY_PROPERTY_ID,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters()))
                .thenReturn(transientPricingBaseAccomTypeSeasons);
        when(tenantCrudService.executeUpdateByNamedQuery(PricingAccomClass.DELETE_BY_PROPERTY_ID,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters())).thenReturn(0);

        Mockito.doNothing().when(tenantCrudService).delete(pricingAccomClasses);
        when(tenantCrudService.save(pricingAccomClasses)).thenReturn(pricingAccomClasses);

        service.savePricingAccomClasses(pricingAccomClasses);

        verify(tenantCrudService).findByNamedQuery(TransientPricingBaseAccomType.FIND_BY_PROPERTY_AND_ACCOM_CLASS, accomClassQueryParams);
        verify(tenantCrudService).delete(pricingBaseAccomTypes);
        verify(tenantCrudService).executeUpdateByNamedQuery(PricingAccomClass.DELETE_BY_PROPERTY_ID, QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters());
        verify(tenantCrudService).save(pricingAccomClasses);
        verify(syncEventAggregatorService).registerSyncEvent(SyncEvent.PRICING_CONFIG_CHANGED);
        verify(tenantCrudService).save(any(TransientPricingBaseAccomType.class));
    }

    @Test
    public void ensureSeasonRowsForAlteredAccomClasses_isPriceExcluded() {
        List<PricingAccomClass> pricingAccomClasses = Collections.singletonList(PricingConfigurationObjectMother.buildPricingAccomClass());
        List<PricingBaseAccomType> transientPricingBaseAccomTypeSeasons = Collections.singletonList(PricingConfigurationObjectMother.buildPricingBaseAccomType());
        pricingAccomClasses.get(0).setPriceExcluded(true);
        pricingAccomClasses.get(0).setPriceExcludedChanged(true);

        ArgumentCaptor<TransientPricingBaseAccomType> captor = ArgumentCaptor.forClass(TransientPricingBaseAccomType.class);

        when(tenantCrudService.<PricingBaseAccomType>findByNamedQuery(TransientPricingBaseAccomType.FIND_PRICEABLE_SEASONS_BY_PROPERTY_ID,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters()))
                .thenReturn(transientPricingBaseAccomTypeSeasons);

        service.ensureSeasonRowsForAlteredAccomClasses(pricingAccomClasses);

        verify(tenantCrudService).save(captor.capture());
        TransientPricingBaseAccomType value = captor.getValue();
        assertEquals(pricingAccomClasses.get(0).getAccomType(), value.getAccomType());
        assertEquals(pricingAccomClasses.get(0).getPropertyId(), value.getPropertyId());
        assertEquals(transientPricingBaseAccomTypeSeasons.get(0).getStartDate(), value.getStartDate());
        assertEquals(transientPricingBaseAccomTypeSeasons.get(0).getEndDate(), value.getEndDate());
        assertEquals(Status.ACTIVE, value.getStatus());
        assertEquals(transientPricingBaseAccomTypeSeasons.get(0).getSeasonName(), value.getSeasonName());
    }

    @Test
    public void ensureSeasonRowsForAlteredAccomClasses_isAccomTypeChanged() {
        List<PricingAccomClass> pricingAccomClasses = Collections.singletonList(PricingConfigurationObjectMother.buildPricingAccomClass());
        List<PricingBaseAccomType> transientPricingBaseAccomTypeSeasons = Collections.singletonList(PricingConfigurationObjectMother.buildPricingBaseAccomType());
        pricingAccomClasses.get(0).setAccomTypeChanged(true);

        ArgumentCaptor<TransientPricingBaseAccomType> captor = ArgumentCaptor.forClass(TransientPricingBaseAccomType.class);

        when(tenantCrudService.<PricingBaseAccomType>findByNamedQuery(TransientPricingBaseAccomType.FIND_PRICEABLE_SEASONS_BY_PROPERTY_ID,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters()))
                .thenReturn(transientPricingBaseAccomTypeSeasons);

        service.ensureSeasonRowsForAlteredAccomClasses(pricingAccomClasses);

        verify(tenantCrudService).save(captor.capture());
        TransientPricingBaseAccomType value = captor.getValue();
        assertEquals(pricingAccomClasses.get(0).getAccomType(), value.getAccomType());
        assertEquals(pricingAccomClasses.get(0).getPropertyId(), value.getPropertyId());
        assertEquals(transientPricingBaseAccomTypeSeasons.get(0).getStartDate(), value.getStartDate());
        assertEquals(transientPricingBaseAccomTypeSeasons.get(0).getEndDate(), value.getEndDate());
        assertEquals(Status.ACTIVE, value.getStatus());
        assertEquals(transientPricingBaseAccomTypeSeasons.get(0).getSeasonName(), value.getSeasonName());
    }

    @Test
    public void getTransientPricingBaseAccomTypes() {
        PricingBaseAccomType pricingBaseAccomType = PricingConfigurationObjectMother.buildPricingBaseAccomType();

        Map<String, Object> queryParams = getQueryParamMapForPropertyID();

        when(tenantCrudService.findByNamedQuery(TransientPricingBaseAccomType.FIND_BASE_AT_FOR_PRICEABLE_AC, queryParams))
                .thenReturn(Arrays.asList(pricingBaseAccomType));

        service.getAllTransientPricingBaseAccomTypes();

        verify(tenantCrudService).findByNamedQuery(TransientPricingBaseAccomType.FIND_BASE_AT_FOR_PRICEABLE_AC,
                queryParams);
    }

    @Test
    public void getAllTransientPricingBaseAccomTypesWithJoinFetch() {
        PricingBaseAccomType pricingBaseAccomType = PricingConfigurationObjectMother.buildPricingBaseAccomType();
        Map<String, Object> queryParams = getQueryParamMapForPropertyID();
        when(tenantCrudService.findByNamedQuery(TransientPricingBaseAccomType.FIND_BASE_AT_FOR_PRICEABLE_AC_WITH_JOIN_FETCH, queryParams))
                .thenReturn(Arrays.asList(pricingBaseAccomType));

        service.getAllTransientPricingBaseAccomTypesWithJoinFetch();

        verify(tenantCrudService).findByNamedQuery(TransientPricingBaseAccomType.FIND_BASE_AT_FOR_PRICEABLE_AC_WITH_JOIN_FETCH, queryParams);
    }

    @Test
    void getAllTransientPricingBaseAccomTypesForAllAccomTypes_NonEmptyList() {
        List<PricingBaseAccomType> mockList = new ArrayList<>();
        PricingBaseAccomType mockItem = mock(PricingBaseAccomType.class);
        mockList.add(mockItem);

        when(tenantCrudService.findByNamedQuery(anyString(), anyMap())).thenReturn(new ArrayList<>(mockList));

        List<PricingBaseAccomType> result = service.getAllTransientPricingBaseAccomTypesForAllAccomTypes();

        assertEquals(1, result.size());
        verify(tenantCrudService).findByNamedQuery(anyString(), anyMap());
        verify(mockItem, times(1)).getAccomType();
    }

    @Test
    void getAllTransientPricingBaseAccomTypesForAllAccomTypes_EmptyList() {
        when(tenantCrudService.findByNamedQuery(anyString(), anyMap())).thenReturn(Collections.emptyList());

        List<PricingBaseAccomType> result = service.getAllTransientPricingBaseAccomTypesForAllAccomTypes();

        assertTrue(result.isEmpty());
        verify(tenantCrudService).findByNamedQuery(anyString(), anyMap());
    }

    @Test
    void getAllTransientPricingBaseAccomTypesForAllAccomTypes_NullList() {
        when(tenantCrudService.findByNamedQuery(anyString(), anyMap())).thenReturn(null);

        List<PricingBaseAccomType> result = service.getAllTransientPricingBaseAccomTypesForAllAccomTypes();

        assertNull(result);
        verify(tenantCrudService).findByNamedQuery(anyString(), anyMap());
    }

    @Test
    public void getTransientPricingBaseAccomTypeDefaults() {
        List<PricingBaseAccomType> allPriccingBaseAccomTypes = new ArrayList<PricingBaseAccomType>();
        PricingBaseAccomType defaultPricingBaseAccomType = PricingConfigurationObjectMother.buildPricingBaseAccomType();
        PricingBaseAccomType seasonPricingBaseAccomType = PricingConfigurationObjectMother
                .buildPricingBaseAccomTypeSeason();

        allPriccingBaseAccomTypes.add(defaultPricingBaseAccomType);
        allPriccingBaseAccomTypes.add(seasonPricingBaseAccomType);

        Map<String, Object> queryParams = getQueryParamMapForPropertyID();

        when(tenantCrudService.<PricingBaseAccomType>findByNamedQuery(TransientPricingBaseAccomType.FIND_BASE_AT_FOR_PRICEABLE_AC, queryParams))
                .thenReturn(allPriccingBaseAccomTypes);

        List<PricingBaseAccomType> returnBaseAccomTypes = service.getTransientPricingBaseAccomTypeDefaults();

        assertNotNull(returnBaseAccomTypes);
        assertTrue(returnBaseAccomTypes.size() == 1);
        assertNull(returnBaseAccomTypes.get(0).getStartDate());
        assertNull(returnBaseAccomTypes.get(0).getEndDate());
        verify(tenantCrudService).findByNamedQuery(TransientPricingBaseAccomType.FIND_BASE_AT_FOR_PRICEABLE_AC,
                queryParams);
    }

    @Test
    public void getTransientPricingBaseAccomTypesWithPriceExcluded() {
        List<PricingBaseAccomType> allPriccingBaseAccomTypes = new ArrayList<>();
        PricingBaseAccomType defaultPricingBaseAccomType = PricingConfigurationObjectMother.buildPricingBaseAccomType();
        PricingBaseAccomType seasonPricingBaseAccomType = PricingConfigurationObjectMother
                .buildPricingBaseAccomTypeSeason();

        allPriccingBaseAccomTypes.add(defaultPricingBaseAccomType);
        allPriccingBaseAccomTypes.add(seasonPricingBaseAccomType);

        Product product = new Product();
        product.setId(1);
        Map<String, Object> queryParams = getQueryParamsForPropertyAndProductId(1);
        when(tenantCrudService.<PricingBaseAccomType>findByNamedQuery(TransientPricingBaseAccomType.FIND_BASE_AT_FOR_PRICEABLE_AC_WITH_PRICE_EXCLUDED, queryParams))
                .thenReturn(allPriccingBaseAccomTypes);

        List<PricingBaseAccomType> returnBaseAccomTypes = service.getTransientPricingBaseAccomTypeDefaultsWithPriceExcluded(product);

        assertNotNull(returnBaseAccomTypes);
        assertTrue(returnBaseAccomTypes.size() == 1);
        assertNull(returnBaseAccomTypes.get(0).getStartDate());
        assertNull(returnBaseAccomTypes.get(0).getEndDate());
        verify(tenantCrudService).findByNamedQuery(TransientPricingBaseAccomType.FIND_BASE_AT_FOR_PRICEABLE_AC_WITH_PRICE_EXCLUDED,
                queryParams);
    }

    @Test
    public void getTransientPricingBaseAccomTypeSeasons() {
        List<PricingBaseAccomType> allPriccingBaseAccomTypes = new ArrayList<PricingBaseAccomType>();
        PricingBaseAccomType defaultPricingBaseAccomType = PricingConfigurationObjectMother.buildPricingBaseAccomType();
        PricingBaseAccomType seasonPricingBaseAccomType = PricingConfigurationObjectMother
                .buildPricingBaseAccomTypeSeason();

        allPriccingBaseAccomTypes.add(defaultPricingBaseAccomType);
        allPriccingBaseAccomTypes.add(seasonPricingBaseAccomType);

        Map<String, Object> queryParams = getQueryParamMapForPropertyID();

        when(tenantCrudService.<PricingBaseAccomType>findByNamedQuery(TransientPricingBaseAccomType.FIND_BASE_AT_FOR_PRICEABLE_AC, queryParams))
                .thenReturn(allPriccingBaseAccomTypes);

        List<PricingBaseAccomType> returnBaseAccomTypes = service.getTransientPricingBaseAccomTypeSeasons();

        assertNotNull(returnBaseAccomTypes);
        assertTrue(returnBaseAccomTypes.size() == 1);
        assertTrue(returnBaseAccomTypes.get(0).getStartDate() != null);
        assertTrue(returnBaseAccomTypes.get(0).getEndDate() != null);
        verify(tenantCrudService).findByNamedQuery(TransientPricingBaseAccomType.FIND_BASE_AT_FOR_PRICEABLE_AC,
                queryParams);
    }

    @Test
    public void getTransientPricingBaseAccomTypeSeasonsWithPriceExcluded() {
        List<PricingBaseAccomType> allPriccingBaseAccomTypes = new ArrayList<PricingBaseAccomType>();
        PricingBaseAccomType defaultPricingBaseAccomType = PricingConfigurationObjectMother.buildPricingBaseAccomType();
        PricingBaseAccomType seasonPricingBaseAccomType = PricingConfigurationObjectMother
                .buildPricingBaseAccomTypeSeason();

        allPriccingBaseAccomTypes.add(defaultPricingBaseAccomType);
        allPriccingBaseAccomTypes.add(seasonPricingBaseAccomType);


        Product product = new Product();
        product.setId(1);
        Map<String, Object> queryParams = getQueryParamsForPropertyAndProductId(1);

        when(tenantCrudService.<PricingBaseAccomType>findByNamedQuery(TransientPricingBaseAccomType.FIND_BASE_AT_FOR_PRICEABLE_AC_WITH_PRICE_EXCLUDED, queryParams))
                .thenReturn(allPriccingBaseAccomTypes);

        List<PricingBaseAccomType> returnBaseAccomTypes = service.getTransientPricingBaseAccomTypeSeasonsWithPriceExcluded(product);

        assertNotNull(returnBaseAccomTypes);
        assertTrue(returnBaseAccomTypes.size() == 1);
        assertTrue(returnBaseAccomTypes.get(0).getStartDate() != null);
        assertTrue(returnBaseAccomTypes.get(0).getEndDate() != null);
        verify(tenantCrudService).findByNamedQuery(TransientPricingBaseAccomType.FIND_BASE_AT_FOR_PRICEABLE_AC_WITH_PRICE_EXCLUDED,
                queryParams);
    }

    @Test
    public void getPriceExcludedPricingAccomClasses() {
        List<PricingAccomClass> priceExcludedClasses = Arrays.asList(new PricingAccomClass());
        Map<String, Object> queryParams = getQueryParamMapForPropertyID();
        when(multiPropertyCrudService.findByNamedQueryForSingleProperty(PacmanWorkContextHelper.getPropertyId(), PricingAccomClass.FIND_BY_PROPERTY_ID_AND_PRICE_EXCLUDED, queryParams))
                .thenReturn(Arrays.asList(priceExcludedClasses));
        List<PricingAccomClass> results = service.getPriceExcludedPricingAccomClasses();
        assertNotNull(results);
        assertTrue(results.size() == 1);
        verify(multiPropertyCrudService).findByNamedQueryForSingleProperty(PacmanWorkContextHelper.getPropertyId(), PricingAccomClass.FIND_BY_PROPERTY_ID_AND_PRICE_EXCLUDED,
                queryParams);
    }

    @Test
    public void saveTransientPricingBaseAccomTypes() {
        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.SYNC_GP_CEILING_FLOOR_WITH_PRIMARY_PRICED_PRODUCT))
                .thenReturn(false);
        List<PricingBaseAccomType> pricingBaseAccomTypes = Arrays
                .asList(PricingConfigurationObjectMother.buildPricingBaseAccomTypeSeason());
        Map<String, Object> queryParams = getQueryParamMapForPropertyID();
        when(tenantCrudService.<PricingBaseAccomType>findByNamedQuery(TransientPricingBaseAccomType.FIND_PRICEABLE_SEASONS_BY_PROPERTY_ID,
                queryParams)).thenReturn(pricingBaseAccomTypes);
        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(java.time.LocalDate.parse("2020-06-01"));

        service.saveTransientPricingBaseAccomTypes(pricingBaseAccomTypes);

        verify(tenantCrudService).save(pricingBaseAccomTypes);
        verify(taxInclusiveMigrationService).handleTransientPriceExcludedBaseRoomTypeOnSupplementChange();
        verify(tenantCrudService, never()).findByNamedQuery(TransientPricingBaseAccomType.FIND_PRICEABLE_SEASONS_BY_PROPERTY_ID,
                queryParams);
        verify(tenantCrudService, never()).delete(pricingBaseAccomTypes);
        verify(taxInclusiveMigrationService).handleTransientPriceExcludedBaseRoomTypeOnSupplementChange();
        verify(syncEventAggregatorService).registerSyncEvent(SyncEvent.PRICING_CONFIG_CHANGED);
        verify(pricingConfigurationLTBDEService).enabledLTBDEIfApplicableForCeilingFloorChange(pricingBaseAccomTypes, java.time.LocalDate.parse("2020-06-01"));
    }

    @Test
    public void shouldUseSupplementAsPercentage() {
        Product product = new Product();
        product.setId(1);
        PricingBaseAccomType updatedAccomType = PricingConfigurationObjectMother.buildPricingBaseAccomTypeWithTax();
        List<PricingBaseAccomType> pricingBaseAccomTypes = Collections.singletonList(updatedAccomType);
        AccomTypeSupplement supplement = PricingConfigurationObjectMother.buildCPConfigSupplementAccomType(1);
        supplement.setOffsetMethod(OffsetMethod.PERCENTAGE);

        when(accomTypeSupplementService.findSupplementByAccomTypeAndOccupancyTypeAndProduct(updatedAccomType.getAccomType(),
                OccupancyType.SINGLE, product)).thenReturn(supplement);
        Tax tax = createAndSetupTax();

        service.applySupplementsAndTax(pricingBaseAccomTypes, product);

        assertEquals(updatedAccomType.getMondayFloorRate(), tax.removeRoomTaxRate(getActualValue(updatedAccomType.getMondayFloorRateWithTax(), supplement.getMondaySupplementValue())));
        assertEquals(updatedAccomType.getMondayCeilingRate(), tax.removeRoomTaxRate(getActualValue(updatedAccomType.getMondayCeilingRateWithTax(), supplement.getMondaySupplementValue())));
        assertEquals(updatedAccomType.getTuesdayFloorRate(), tax.removeRoomTaxRate(getActualValue(updatedAccomType.getTuesdayFloorRateWithTax(), supplement.getTuesdaySupplementValue())));
        assertEquals(updatedAccomType.getTuesdayCeilingRate(), tax.removeRoomTaxRate(getActualValue(updatedAccomType.getTuesdayCeilingRateWithTax(), supplement.getTuesdaySupplementValue())));
        assertEquals(updatedAccomType.getWednesdayFloorRate(), tax.removeRoomTaxRate(getActualValue(updatedAccomType.getWednesdayFloorRateWithTax(), supplement.getWednesdaySupplementValue())));
        assertEquals(updatedAccomType.getWednesdayCeilingRate(), tax.removeRoomTaxRate(getActualValue(updatedAccomType.getWednesdayCeilingRateWithTax(), supplement.getWednesdaySupplementValue())));
        assertEquals(updatedAccomType.getThursdayFloorRate(), tax.removeRoomTaxRate(getActualValue(updatedAccomType.getThursdayFloorRateWithTax(), supplement.getThursdaySupplementValue())));
        assertEquals(updatedAccomType.getThursdayCeilingRate(), tax.removeRoomTaxRate(getActualValue(updatedAccomType.getThursdayCeilingRateWithTax(), supplement.getThursdaySupplementValue())));
        assertEquals(updatedAccomType.getFridayFloorRate(), tax.removeRoomTaxRate(getActualValue(updatedAccomType.getFridayFloorRateWithTax(), supplement.getFridaySupplementValue())));
        assertEquals(updatedAccomType.getFridayCeilingRate(), tax.removeRoomTaxRate(getActualValue(updatedAccomType.getFridayCeilingRateWithTax(), supplement.getFridaySupplementValue())));
        assertEquals(updatedAccomType.getSaturdayFloorRate(), tax.removeRoomTaxRate(getActualValue(updatedAccomType.getSaturdayFloorRateWithTax(), supplement.getSaturdaySupplementValue())));
        assertEquals(updatedAccomType.getSaturdayCeilingRate(), tax.removeRoomTaxRate(getActualValue(updatedAccomType.getSaturdayCeilingRateWithTax(), supplement.getSaturdaySupplementValue())));
        assertEquals(updatedAccomType.getSundayFloorRate(), tax.removeRoomTaxRate(getActualValue(updatedAccomType.getSundayFloorRateWithTax(), supplement.getSundaySupplementValue())));
        assertEquals(updatedAccomType.getSundayCeilingRate(), tax.removeRoomTaxRate(getActualValue(updatedAccomType.getSundayCeilingRateWithTax(), supplement.getSundaySupplementValue())));
    }

    @Test
    void shouldUseSupplementAsPercentageSeason() {
        Product product = new Product();
        product.setId(1);
        PricingBaseAccomType updatedAccomType = PricingConfigurationObjectMother.buildPricingBaseAccomTypeWithTax();
        java.time.LocalDate startDate = java.time.LocalDate.now();
        java.time.LocalDate endDate = startDate.plusDays(7);
        updatedAccomType.setStartDate(LocalDateUtils.toJodaLocalDate(startDate));
        updatedAccomType.setEndDate(LocalDateUtils.toJodaLocalDate(endDate));
        List<PricingBaseAccomType> pricingBaseAccomTypes = Collections.singletonList(updatedAccomType);
        AccomTypeSupplement supplement = PricingConfigurationObjectMother.buildCPConfigSupplementAccomType(1);
        supplement.setSundaySupplementValue(BigDecimal.valueOf(5));
        supplement.setMondaySupplementValue(BigDecimal.valueOf(5));
        supplement.setTuesdaySupplementValue(BigDecimal.valueOf(5));
        supplement.setWednesdaySupplementValue(BigDecimal.valueOf(5));
        supplement.setThursdaySupplementValue(BigDecimal.valueOf(5));
        supplement.setFridaySupplementValue(BigDecimal.valueOf(5));
        supplement.setSaturdaySupplementValue(BigDecimal.valueOf(5));
        supplement.setOffsetMethod(OffsetMethod.PERCENTAGE);

        when(accomTypeSupplementService.findSupplementByAccomTypeAndOccupancyTypeAndProduct(updatedAccomType.getAccomType(),
                OccupancyType.SINGLE, product)).thenReturn(supplement);
        Tax tax = createAndSetupTax();
        Map<LocalDate, Tax> taxesByDateRange = new HashMap<>();
        taxesByDateRange.put(LocalDateUtils.toJodaLocalDate(startDate), tax);
        taxesByDateRange.put(LocalDateUtils.toJodaLocalDate(startDate.plusDays(1)), tax);
        taxesByDateRange.put(LocalDateUtils.toJodaLocalDate(startDate.plusDays(2)), tax);
        taxesByDateRange.put(LocalDateUtils.toJodaLocalDate(startDate.plusDays(3)), tax);
        taxesByDateRange.put(LocalDateUtils.toJodaLocalDate(startDate.plusDays(4)), tax);
        taxesByDateRange.put(LocalDateUtils.toJodaLocalDate(startDate.plusDays(5)), tax);
        taxesByDateRange.put(LocalDateUtils.toJodaLocalDate(startDate.plusDays(6)), tax);
        taxesByDateRange.put(LocalDateUtils.toJodaLocalDate(startDate.plusDays(7)), tax);
        when(taxService.findTaxesForDateRange(LocalDateUtils.toJodaLocalDate(startDate), LocalDateUtils.toJodaLocalDate(endDate))).thenReturn(taxesByDateRange);

        Map<AccomTypeSupplementValuePK, AccomTypeSupplementValue> map = buildSupplementMapForRange(java.time.LocalDate.now(),
                java.time.LocalDate.now().plusDays(8), supplement.getAccomType().getId(),
                OccupancyType.SINGLE, OffsetMethod.PERCENTAGE);
        when(accomTypeSupplementService.getSupplementValueMap(LocalDateUtils.toJodaLocalDate(startDate), LocalDateUtils.toJodaLocalDate(endDate))).thenReturn(map);

        service.applySupplementsAndTax(pricingBaseAccomTypes, product);

        assertEquals(updatedAccomType.getMondayFloorRate(), tax.removeRoomTaxRate(getActualValue(updatedAccomType.getMondayFloorRateWithTax(), supplement.getMondaySupplementValue())));
        assertEquals(updatedAccomType.getMondayCeilingRate(), tax.removeRoomTaxRate(getActualValue(updatedAccomType.getMondayCeilingRateWithTax(), supplement.getMondaySupplementValue())));
        assertEquals(updatedAccomType.getTuesdayFloorRate(), tax.removeRoomTaxRate(getActualValue(updatedAccomType.getTuesdayFloorRateWithTax(), supplement.getTuesdaySupplementValue())));
        assertEquals(updatedAccomType.getTuesdayCeilingRate(), tax.removeRoomTaxRate(getActualValue(updatedAccomType.getTuesdayCeilingRateWithTax(), supplement.getTuesdaySupplementValue())));
        assertEquals(updatedAccomType.getWednesdayFloorRate(), tax.removeRoomTaxRate(getActualValue(updatedAccomType.getWednesdayFloorRateWithTax(), supplement.getWednesdaySupplementValue())));
        assertEquals(updatedAccomType.getWednesdayCeilingRate(), tax.removeRoomTaxRate(getActualValue(updatedAccomType.getWednesdayCeilingRateWithTax(), supplement.getWednesdaySupplementValue())));
        assertEquals(updatedAccomType.getThursdayFloorRate(), tax.removeRoomTaxRate(getActualValue(updatedAccomType.getThursdayFloorRateWithTax(), supplement.getThursdaySupplementValue())));
        assertEquals(updatedAccomType.getThursdayCeilingRate(), tax.removeRoomTaxRate(getActualValue(updatedAccomType.getThursdayCeilingRateWithTax(), supplement.getThursdaySupplementValue())));
        assertEquals(updatedAccomType.getFridayFloorRate(), tax.removeRoomTaxRate(getActualValue(updatedAccomType.getFridayFloorRateWithTax(), supplement.getFridaySupplementValue())));
        assertEquals(updatedAccomType.getFridayCeilingRate(), tax.removeRoomTaxRate(getActualValue(updatedAccomType.getFridayCeilingRateWithTax(), supplement.getFridaySupplementValue())));
        assertEquals(updatedAccomType.getSaturdayFloorRate(), tax.removeRoomTaxRate(getActualValue(updatedAccomType.getSaturdayFloorRateWithTax(), supplement.getSaturdaySupplementValue())));
        assertEquals(updatedAccomType.getSaturdayCeilingRate(), tax.removeRoomTaxRate(getActualValue(updatedAccomType.getSaturdayCeilingRateWithTax(), supplement.getSaturdaySupplementValue())));
        assertEquals(updatedAccomType.getSundayFloorRate(), tax.removeRoomTaxRate(getActualValue(updatedAccomType.getSundayFloorRateWithTax(), supplement.getSundaySupplementValue())));
        assertEquals(updatedAccomType.getSundayCeilingRate(), tax.removeRoomTaxRate(getActualValue(updatedAccomType.getSundayCeilingRateWithTax(), supplement.getSundaySupplementValue())));
    }

    private static BigDecimal getActualValue(BigDecimal floorRateWithTax, BigDecimal supplementValue) {
        return Supplement.removeSupplementFrom(floorRateWithTax, supplementValue, true);
    }

    @Test
    public void saveTransientPricingBaseAccomTypes_TaxInclusiveDefault() {
        Product product = new Product();
        product.setId(1);
        List<PricingBaseAccomType> pricingBaseAccomTypes = Collections.singletonList(PricingConfigurationObjectMother.buildPricingBaseAccomTypeSeasonTaxInclusive());
        PricingBaseAccomType transientBaseAccomType = pricingBaseAccomTypes.get(0);
        AccomType accomType = new AccomType();
        accomType.setId(1);
        transientBaseAccomType.setAccomType(accomType);
        transientBaseAccomType.setStartDate(null);
        transientBaseAccomType.setEndDate(null);

        Map<String, Object> queryParams = getQueryParamMapForPropertyID();

        Tax tax = createAndSetupTax();

        AccomTypeSupplement defaultSupp = buildDefaultSupplements(transientBaseAccomType.getAccomType(), OccupancyType.DOUBLE);

        when(tenantCrudService.findByNamedQuerySingleResult(Product.GET_BY_ID,
                QueryParameter.with("productId", pricingBaseAccomTypes.get(0).getProductID()).parameters())).thenReturn(product);
        when(accomTypeSupplementService.findSupplementByAccomTypeAndOccupancyTypeAndProduct(transientBaseAccomType.getAccomType(), OccupancyType.DOUBLE, product)).thenReturn(defaultSupp);
        when(tenantCrudService.<PricingBaseAccomType>findByNamedQuery(TransientPricingBaseAccomType.FIND_PRICEABLE_SEASONS_BY_PROPERTY_ID, queryParams)).thenReturn(pricingBaseAccomTypes);
        setUpCPRelatedConfigParameters();

        service.saveTransientPricingBaseAccomTypes(pricingBaseAccomTypes);

        assertEquals(tax.removeRoomTaxRate(transientBaseAccomType.getMondayCeilingRateWithTax().subtract(defaultSupp.getMondaySupplementValue())), transientBaseAccomType.getMondayCeilingRate());
        assertEquals(tax.removeRoomTaxRate(transientBaseAccomType.getMondayFloorRateWithTax().subtract(defaultSupp.getMondaySupplementValue())), transientBaseAccomType.getMondayFloorRate());

        assertEquals(tax.removeRoomTaxRate(transientBaseAccomType.getTuesdayCeilingRateWithTax().subtract(defaultSupp.getTuesdaySupplementValue())), transientBaseAccomType.getTuesdayCeilingRate());
        assertEquals(tax.removeRoomTaxRate(transientBaseAccomType.getTuesdayFloorRateWithTax().subtract(defaultSupp.getTuesdaySupplementValue())), transientBaseAccomType.getTuesdayFloorRate());

        assertEquals(tax.removeRoomTaxRate(transientBaseAccomType.getWednesdayCeilingRateWithTax().subtract(defaultSupp.getWednesdaySupplementValue())), transientBaseAccomType.getWednesdayCeilingRate());
        assertEquals(tax.removeRoomTaxRate(transientBaseAccomType.getWednesdayFloorRateWithTax().subtract(defaultSupp.getWednesdaySupplementValue())), transientBaseAccomType.getWednesdayFloorRate());

        assertEquals(tax.removeRoomTaxRate(transientBaseAccomType.getThursdayCeilingRateWithTax().subtract(defaultSupp.getThursdaySupplementValue())), transientBaseAccomType.getThursdayCeilingRate());
        assertEquals(tax.removeRoomTaxRate(transientBaseAccomType.getThursdayFloorRateWithTax().subtract(defaultSupp.getThursdaySupplementValue())), transientBaseAccomType.getThursdayFloorRate());

        assertEquals(tax.removeRoomTaxRate(transientBaseAccomType.getFridayCeilingRateWithTax().subtract(defaultSupp.getFridaySupplementValue())), transientBaseAccomType.getFridayCeilingRate());
        assertEquals(tax.removeRoomTaxRate(transientBaseAccomType.getFridayFloorRateWithTax().subtract(defaultSupp.getFridaySupplementValue())), transientBaseAccomType.getFridayFloorRate());

        assertEquals(tax.removeRoomTaxRate(transientBaseAccomType.getSaturdayCeilingRateWithTax().subtract(defaultSupp.getSaturdaySupplementValue())), transientBaseAccomType.getSaturdayCeilingRate());
        assertEquals(tax.removeRoomTaxRate(transientBaseAccomType.getSaturdayFloorRateWithTax().subtract(defaultSupp.getSaturdaySupplementValue())), transientBaseAccomType.getSaturdayFloorRate());

        assertEquals(tax.removeRoomTaxRate(transientBaseAccomType.getSundayCeilingRateWithTax().subtract(defaultSupp.getSundaySupplementValue())), transientBaseAccomType.getSundayCeilingRate());
        assertEquals(tax.removeRoomTaxRate(transientBaseAccomType.getSundayFloorRateWithTax().subtract(defaultSupp.getSundaySupplementValue())), transientBaseAccomType.getSundayFloorRate());

        verify(tenantCrudService).save(pricingBaseAccomTypes);
        verify(tenantCrudService, never()).findByNamedQuery(TransientPricingBaseAccomType.FIND_PRICEABLE_SEASONS_BY_PROPERTY_ID, queryParams);
        verify(syncEventAggregatorService).registerSyncEvent(SyncEvent.PRICING_CONFIG_CHANGED);
    }

    @Test
    public void saveTransientPricingBaseAccomTypes_TaxInclusiveSeason() {
        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.SYNC_GP_CEILING_FLOOR_WITH_PRIMARY_PRICED_PRODUCT)).thenReturn(false);
        List<PricingBaseAccomType> allPricingBaseAccomTypes = new ArrayList<>();
        PricingBaseAccomType transientBaseAccomTypeDefault = PricingConfigurationObjectMother.buildGroupPricingBaseAccomTypeWithTax();
        PricingBaseAccomType transientBaseAccomTypeSeason = PricingConfigurationObjectMother.buildPricingBaseAccomTypeSeasonTaxInclusive();
        PricingBaseAccomType transientBaseAccomTypeSeason2 = PricingConfigurationObjectMother.buildPricingBaseAccomTypeSeasonTaxInclusive();
        AccomType accomType = new AccomType();
        accomType.setId(1);
        LocalDate startDate = new LocalDate(2018, 10, 1);
        LocalDate endDate = new LocalDate(2018, 10, 1);

        transientBaseAccomTypeSeason.setAccomType(accomType);
        transientBaseAccomTypeSeason.setStartDate(startDate);
        transientBaseAccomTypeSeason.setEndDate(endDate);

        transientBaseAccomTypeSeason2.setAccomType(accomType);
        transientBaseAccomTypeSeason2.setStartDate(startDate);
        transientBaseAccomTypeSeason2.setEndDate(endDate);
        transientBaseAccomTypeSeason2.setMondayFloorRateWithTax(BigDecimal.TEN);
        transientBaseAccomTypeSeason2.setMondayCeilingRateWithTax(BigDecimal.valueOf(100));

        allPricingBaseAccomTypes.add(transientBaseAccomTypeDefault);
        allPricingBaseAccomTypes.add(transientBaseAccomTypeSeason);
        allPricingBaseAccomTypes.add(transientBaseAccomTypeSeason2);
        Map<String, Object> queryParams = getQueryParamMapForPropertyID();
        Tax tax = createAndSetupTax();
        Map<LocalDate, Tax> taxesByDateRange = new HashMap<>();
        taxesByDateRange.put(startDate, tax);
        when(taxService.findTaxesForDateRange(startDate, endDate)).thenReturn(taxesByDateRange);

        when(tenantCrudService.<PricingBaseAccomType>findByNamedQuery(TransientPricingBaseAccomType.FIND_PRICEABLE_SEASONS_BY_PROPERTY_ID, queryParams)).thenReturn(allPricingBaseAccomTypes);
        setUpCPRelatedConfigParameters();

        Map<AccomTypeSupplementValuePK, AccomTypeSupplementValue> map = buildSupplementMap(transientBaseAccomTypeSeason.getStartDate(), accomType.getId(), OccupancyType.DOUBLE);
        when(accomTypeSupplementService.getSupplementValueMap(transientBaseAccomTypeSeason.getStartDate(), transientBaseAccomTypeSeason.getEndDate())).thenReturn(map);

        service.saveTransientPricingBaseAccomTypes(allPricingBaseAccomTypes);

        AccomTypeSupplementValue supplement = map.get(new AccomTypeSupplementValuePK(1, transientBaseAccomTypeSeason2.getStartDate(), transientBaseAccomTypeSeason2.getAccomType().getId(), OccupancyType.DOUBLE));
        assertEquals(tax.removeRoomTaxRate(transientBaseAccomTypeSeason2.getMondayCeilingRateWithTax().subtract(supplement.getValue())), transientBaseAccomTypeSeason2.getMondayCeilingRate());
        assertEquals(tax.removeRoomTaxRate(transientBaseAccomTypeSeason2.getMondayFloorRateWithTax().subtract(supplement.getValue())), transientBaseAccomTypeSeason2.getMondayFloorRate());
        assertEquals(BigDecimal.valueOf(87.96).setScale(2, BigDecimal.ROUND_HALF_UP), transientBaseAccomTypeSeason2.getMondayCeilingRate().setScale(2, BigDecimal.ROUND_HALF_UP));
        assertEquals(BigDecimal.valueOf(4.63).setScale(2, BigDecimal.ROUND_HALF_UP), transientBaseAccomTypeSeason2.getMondayFloorRate().setScale(2, BigDecimal.ROUND_HALF_UP));

        verify(tenantCrudService).save(allPricingBaseAccomTypes);
        verify(tenantCrudService, never()).findByNamedQuery(TransientPricingBaseAccomType.FIND_PRICEABLE_SEASONS_BY_PROPERTY_ID, queryParams);
        verify(tenantCrudService, never()).delete(allPricingBaseAccomTypes);
        verify(syncEventAggregatorService).registerSyncEvent(SyncEvent.PRICING_CONFIG_CHANGED);
    }

    @Test
    public void saveTransientPricingBaseAccomTypes_TaxInclusiveSeasonWhenTaxbleSeasonsAreConfigured() {
        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.SYNC_GP_CEILING_FLOOR_WITH_PRIMARY_PRICED_PRODUCT)).thenReturn(false);
        List<PricingBaseAccomType> allPricingBaseAccomTypes = new ArrayList<>();
        PricingBaseAccomType transientBaseAccomTypeDefault = PricingConfigurationObjectMother.buildPricingBaseAccomTypeWithTax();
        PricingBaseAccomType transientBaseAccomTypeSeason = PricingConfigurationObjectMother.buildPricingBaseAccomTypeSeasonTaxInclusive();
        PricingBaseAccomType transientBaseAccomTypeSeason2 = PricingConfigurationObjectMother.buildPricingBaseAccomTypeSeasonTaxInclusive();
        AccomType accomType = new AccomType();
        accomType.setId(1);
        LocalDate startDate = new LocalDate(2018, 10, 1);
        LocalDate endDate = new LocalDate(2018, 10, 1);

        transientBaseAccomTypeSeason.setAccomType(accomType);
        transientBaseAccomTypeSeason.setStartDate(startDate);
        transientBaseAccomTypeSeason.setEndDate(endDate);
        transientBaseAccomTypeSeason.setMondayFloorRateWithTax(BigDecimal.TEN);
        transientBaseAccomTypeSeason.setMondayCeilingRateWithTax(BigDecimal.valueOf(100.0));

        transientBaseAccomTypeSeason2.setAccomType(accomType);
        transientBaseAccomTypeSeason2.setStartDate(startDate.plusDays(1));
        transientBaseAccomTypeSeason2.setEndDate(endDate.plusDays(1));
        transientBaseAccomTypeSeason2.setTuesdayFloorRateWithTax(BigDecimal.TEN);
        transientBaseAccomTypeSeason2.setTuesdayCeilingRateWithTax(BigDecimal.valueOf(100));

        allPricingBaseAccomTypes.add(transientBaseAccomTypeDefault);
        allPricingBaseAccomTypes.add(transientBaseAccomTypeSeason);
        allPricingBaseAccomTypes.add(transientBaseAccomTypeSeason2);
        Map<String, Object> queryParams = getQueryParamMapForPropertyID();
        Tax tax = createAndSetupTax();
        Tax seasonalTax = getTax(startDate.plusDays(1), startDate.plusDays(1), BigDecimal.valueOf(12));
        Map<LocalDate, Tax> taxesByDateRange = new HashMap<>();
        taxesByDateRange.put(startDate, tax);
        taxesByDateRange.put(endDate.plusDays(1), seasonalTax);
        when(taxService.findTaxesForDateRange(startDate, endDate.plusDays(1))).thenReturn(taxesByDateRange);
        when(tenantCrudService.<PricingBaseAccomType>findByNamedQuery(TransientPricingBaseAccomType.FIND_PRICEABLE_SEASONS_BY_PROPERTY_ID, queryParams)).thenReturn(allPricingBaseAccomTypes);
        setUpCPRelatedConfigParameters();

        Map<AccomTypeSupplementValuePK, AccomTypeSupplementValue> map = buildSupplementMap(transientBaseAccomTypeSeason.getStartDate(), accomType.getId(), OccupancyType.DOUBLE);
        when(accomTypeSupplementService.getSupplementValueMap(transientBaseAccomTypeSeason.getStartDate(), transientBaseAccomTypeSeason.getEndDate())).thenReturn(map);
        when(accomTypeSupplementService.getSupplementValueMap(transientBaseAccomTypeSeason2.getStartDate(), transientBaseAccomTypeSeason2.getEndDate())).thenReturn(map);

        service.saveTransientPricingBaseAccomTypes(allPricingBaseAccomTypes);

        AccomTypeSupplementValue supplement = map.get(new AccomTypeSupplementValuePK(1, transientBaseAccomTypeSeason2.getStartDate(), transientBaseAccomTypeSeason2.getAccomType().getId(), OccupancyType.DOUBLE));
        assertEquals(tax.removeRoomTaxRate(transientBaseAccomTypeDefault.getMondayCeilingRateWithTax().subtract(BigDecimal.ZERO)), transientBaseAccomTypeDefault.getMondayCeilingRate());
        assertEquals(tax.removeRoomTaxRate(transientBaseAccomTypeDefault.getMondayFloorRateWithTax().subtract(BigDecimal.ZERO)), transientBaseAccomTypeDefault.getMondayFloorRate());
        assertEquals(seasonalTax.removeRoomTaxRate(transientBaseAccomTypeSeason2.getTuesdayCeilingRateWithTax().subtract(supplement.getValue())), transientBaseAccomTypeSeason2.getTuesdayCeilingRate());
        assertEquals(seasonalTax.removeRoomTaxRate(transientBaseAccomTypeSeason2.getTuesdayFloorRateWithTax().subtract(supplement.getValue())), transientBaseAccomTypeSeason2.getTuesdayFloorRate());
        assertEquals(BigDecimal.valueOf(87.96), transientBaseAccomTypeSeason.getMondayCeilingRate().setScale(2, BigDecimal.ROUND_HALF_UP));
        assertEquals(BigDecimal.valueOf(4.63), transientBaseAccomTypeSeason.getMondayFloorRate().setScale(2, BigDecimal.ROUND_HALF_UP));
        assertEquals(BigDecimal.valueOf(84.82), transientBaseAccomTypeSeason2.getTuesdayCeilingRate().setScale(2, BigDecimal.ROUND_HALF_UP));
        assertEquals(BigDecimal.valueOf(4.46), transientBaseAccomTypeSeason2.getTuesdayFloorRate().setScale(2, BigDecimal.ROUND_HALF_UP));

        verify(tenantCrudService).save(allPricingBaseAccomTypes);
        verify(tenantCrudService, never()).findByNamedQuery(TransientPricingBaseAccomType.FIND_PRICEABLE_SEASONS_BY_PROPERTY_ID, queryParams);
        verify(tenantCrudService, never()).delete(allPricingBaseAccomTypes);
        verify(syncEventAggregatorService).registerSyncEvent(SyncEvent.PRICING_CONFIG_CHANGED);
    }

    @Test
    public void saveGroupPricingBaseAccomTypes_TaxInclusiveSeason() {
        List<PricingBaseAccomType> allPricingBaseAccomTypes = new ArrayList<>();
        PricingBaseAccomType groupPricingBaseAccomTypeDefault = PricingConfigurationObjectMother.buildGroupPricingBaseAccomTypeWithTax();
        PricingBaseAccomType groupPricingBaseAccomTypeSeason = PricingConfigurationObjectMother.buildGroupPricingBaseAccomTypeWithTax();
        AccomType accomType = new AccomType();
        accomType.setId(1);

        groupPricingBaseAccomTypeDefault.setAccomType(accomType);

        groupPricingBaseAccomTypeSeason.setAccomType(accomType);
        groupPricingBaseAccomTypeSeason.setStartDate(new LocalDate(2018, 10, 1));
        groupPricingBaseAccomTypeSeason.setEndDate(new LocalDate(2018, 10, 1));

        allPricingBaseAccomTypes.add(groupPricingBaseAccomTypeDefault);
        allPricingBaseAccomTypes.add(groupPricingBaseAccomTypeSeason);

        Map<String, Object> queryParams = getQueryParamMapForPropertyID();

        Tax tax = createAndSetupTax();
        Map<LocalDate, Tax> taxesByDateRange = new HashMap<>();
        taxesByDateRange.put(new LocalDate(2018, 10, 1), tax);
        when(taxService.findTaxesForDateRange(new LocalDate(2018, 10, 1), new LocalDate(2018, 10, 1))).thenReturn(taxesByDateRange);

        setUpCPRelatedConfigParameters();
        when(tenantCrudService.<PricingBaseAccomType>findByNamedQuery(GroupPricingBaseAccomType.FIND_PRICEABLE_SEASONS_BY_PROPERTY_ID, queryParams)).thenReturn(allPricingBaseAccomTypes);

        service.saveGroupPricingBaseAccomTypes(allPricingBaseAccomTypes);

        assertEquals(tax.removeRoomTaxRate(groupPricingBaseAccomTypeSeason.getMondayCeilingRateWithTax()), groupPricingBaseAccomTypeSeason.getMondayCeilingRate());
        assertEquals(tax.removeRoomTaxRate(groupPricingBaseAccomTypeSeason.getMondayFloorRateWithTax()), groupPricingBaseAccomTypeSeason.getMondayFloorRate());

        verify(tenantCrudService).save(allPricingBaseAccomTypes);
        verify(tenantCrudService, never()).findByNamedQuery(GroupPricingBaseAccomType.FIND_PRICEABLE_SEASONS_BY_PROPERTY_ID, queryParams);
        verify(tenantCrudService, never()).delete(allPricingBaseAccomTypes);
        verify(taxInclusiveMigrationService).handleGroupPriceExcludedBaseRoomTypeSupplementOnSave();
        verify(syncEventAggregatorService).registerSyncEvent(SyncEvent.PRICING_CONFIG_CHANGED);
    }

    @Test
    public void saveImpactedTransientPricingBaseAccomTypes_TaxInclusiveSeason() {
        setUpCPRelatedConfigParameters();
        LocalDate startDate = new LocalDate(2018, 10, 1);
        LocalDate endDate = new LocalDate(2018, 10, 1);
        PricingBaseAccomType transientBaseAccomTypeSeason = createPricingBaseAccomType(startDate, startDate);
        List<PricingBaseAccomType> allPricingBaseAccomTypes =
                Collections.singletonList(transientBaseAccomTypeSeason);
        Tax tax = createAndSetupTax();
        Map<LocalDate, Tax> taxesByDateRange = new HashMap<>();
        taxesByDateRange.put(startDate, tax);
        when(taxService.findTaxesForDateRange(startDate, endDate)).thenReturn(taxesByDateRange);
        Map<AccomTypeSupplementValuePK, AccomTypeSupplementValue> map = buildSupplementMap(transientBaseAccomTypeSeason.getStartDate(), 1, OccupancyType.DOUBLE);
        when(accomTypeSupplementService.getSupplementValueMap(transientBaseAccomTypeSeason.getStartDate(), transientBaseAccomTypeSeason.getEndDate())).thenReturn(map);
        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(java.time.LocalDate.parse("2020-06-01"));

        service.saveImpactedTransientPricingBaseAccomTypes(allPricingBaseAccomTypes);

        AccomTypeSupplementValue supplement = map.get(new AccomTypeSupplementValuePK(1, transientBaseAccomTypeSeason.getStartDate(), transientBaseAccomTypeSeason.getAccomType().getId(), OccupancyType.DOUBLE));
        assertEquals(tax.removeRoomTaxRate(transientBaseAccomTypeSeason.getMondayCeilingRateWithTax().subtract(supplement.getValue())), transientBaseAccomTypeSeason.getMondayCeilingRate());
        verify(tenantCrudService).save(allPricingBaseAccomTypes);
        verify(tenantCrudService, never()).findByNamedQuery(TransientPricingBaseAccomType.FIND_PRICEABLE_SEASONS_BY_PROPERTY_ID, getQueryParamMapForPropertyID());
        verify(taxInclusiveMigrationService).handleTransientPriceExcludedBaseRoomTypeOnSupplementChange();
        verify(syncEventAggregatorService).registerSyncEvent(SyncEvent.PRICING_CONFIG_CHANGED);
        verify(pricingConfigurationLTBDEService).enabledLTBDEIfApplicableForCeilingFloorChange(allPricingBaseAccomTypes, java.time.LocalDate.parse("2020-06-01"));
    }

    @Test
    public void saveImpactedTransientPricingBaseAccomTypes_NothingToSave() {
        setUpCPRelatedConfigParameters();
        service.saveImpactedTransientPricingBaseAccomTypes(new ArrayList<>());

        verify(tenantCrudService, never()).findByNamedQuerySingleResult(Product.GET_BY_ID,
                QueryParameter.with(anyObject(), anyObject()).parameters());
        verify(tenantCrudService, never()).save(anyList());
        verify(tenantCrudService, never()).findByNamedQuery(TransientPricingBaseAccomType.FIND_PRICEABLE_SEASONS_BY_PROPERTY_ID, getQueryParamMapForPropertyID());
        verify(taxInclusiveMigrationService).handleTransientPriceExcludedBaseRoomTypeOnSupplementChange();
        verify(syncEventAggregatorService).registerSyncEvent(SyncEvent.PRICING_CONFIG_CHANGED);
    }

    private PricingBaseAccomType createPricingBaseAccomType(LocalDate startDate, LocalDate endDate) {
        AccomType accomType = new AccomType();
        accomType.setId(1);
        PricingBaseAccomType transientBaseAccomTypeSeason = PricingConfigurationObjectMother.buildPricingBaseAccomTypeSeasonTaxInclusive();
        transientBaseAccomTypeSeason.setAccomType(accomType);
        transientBaseAccomTypeSeason.setAccomType(accomType);
        transientBaseAccomTypeSeason.setStartDate(startDate);
        transientBaseAccomTypeSeason.setEndDate(endDate);
        return transientBaseAccomTypeSeason;
    }

    @Test
    public void saveImpactedGroupPricingBaseAccomTypes_TaxInclusiveSeason() {
        setUpCPRelatedConfigParameters();
        PricingBaseAccomType groupPricingBaseAccomTypeSeason = PricingConfigurationObjectMother.buildGroupPricingBaseAccomTypeWithTax();
        List<PricingBaseAccomType> allPricingBaseAccomTypes = getPricingBaseAccomTypes(groupPricingBaseAccomTypeSeason);
        Tax tax = createAndSetupTax();
        Map<LocalDate, Tax> taxesByDateRange = new HashMap<>();
        taxesByDateRange.put(new LocalDate(2018, 10, 1), tax);
        when(taxService.findTaxesForDateRange(new LocalDate(2018, 10, 1), new LocalDate(2018, 10, 1))).thenReturn(taxesByDateRange);

        service.saveImpactedGroupPricingBaseAccomTypes(allPricingBaseAccomTypes);

        assertEquals(tax.removeRoomTaxRate(groupPricingBaseAccomTypeSeason.getMondayCeilingRateWithTax()), groupPricingBaseAccomTypeSeason.getMondayCeilingRate());
        assertEquals(tax.removeRoomTaxRate(groupPricingBaseAccomTypeSeason.getMondayFloorRateWithTax()), groupPricingBaseAccomTypeSeason.getMondayFloorRate());
        verify(tenantCrudService).save(allPricingBaseAccomTypes);

        verify(tenantCrudService, never()).findByNamedQuery(GroupPricingBaseAccomType.FIND_PRICEABLE_SEASONS_BY_PROPERTY_ID, getQueryParamMapForPropertyID());
        verify(taxInclusiveMigrationService).handleGroupPriceExcludedBaseRoomTypeSupplementOnSave();
        verify(syncEventAggregatorService).registerSyncEvent(SyncEvent.PRICING_CONFIG_CHANGED);
    }

    @Test
    public void deletePricingBaseAccomTypes() {
        LocalDate startDate = new LocalDate(2018, 10, 1);
        LocalDate endDate = new LocalDate(2018, 10, 1);
        PricingBaseAccomType transientBaseAccomTypeSeason = createPricingBaseAccomType(startDate, startDate);
        List<PricingBaseAccomType> allPricingBaseAccomTypes =
                Collections.singletonList(transientBaseAccomTypeSeason);
        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(java.time.LocalDate.parse("2020-06-01"));

        service.deletePricingBaseAccomTypes(allPricingBaseAccomTypes);
        verify(tenantCrudService).delete(allPricingBaseAccomTypes);
        verify(pricingConfigurationLTBDEService).enabledLTBDEIfApplicable(JavaLocalDateUtils.toJavaLocalDate(endDate), java.time.LocalDate.parse("2020-06-01"));
    }

    @Test
    public void shouldFetchAllFromTransientPricingBaseAccomTypeDraft() {
        PricingBaseAccomType pricingBaseAccomTypeDraft = PricingConfigurationObjectMother.buildPricingBaseAccomTypeDraft();
        List<PricingBaseAccomType> pricingBaseAccomTypes = new ArrayList<>();
        pricingBaseAccomTypes.add(pricingBaseAccomTypeDraft);
        when(tenantCrudService.<PricingBaseAccomType>findByNamedQuery(TransientPricingBaseAccomTypeDraft.GET_BY_PRODUCT,QueryParameter.with("productId",1).parameters())).thenReturn(pricingBaseAccomTypes);

        List<PricingBaseAccomType> allTransientPricingBaseAccomTypesInDraft = service.getTransientPricingBaseAccomTypesInDraftByProduct(1);
        assertEquals(1, allTransientPricingBaseAccomTypesInDraft.size());
        assertEquals("testSeason", allTransientPricingBaseAccomTypesInDraft.get(0).getSeasonName());

    }

    private Map<String, Object> getQueryParamMapForPropertyID() {
        Map<String, Object> queryParams = new HashMap<String, Object>();
        queryParams.put("propertyId", PacmanWorkContextHelper.getPropertyId());
        return queryParams;
    }

    private Map<String, Object> getQueryParamsForPropertyAndProductId(Integer productID) {
        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put("propertyId", PacmanWorkContextHelper.getPropertyId());
        queryParams.put("productId", productID);
        return queryParams;
    }

    private List<PricingBaseAccomType> getPricingBaseAccomTypes(PricingBaseAccomType groupPricingBaseAccomTypeSeason) {
        List<PricingBaseAccomType> allPricingBaseAccomTypes = new ArrayList<>();
        AccomType accomType = new AccomType();
        accomType.setId(1);
        groupPricingBaseAccomTypeSeason.setAccomType(accomType);
        groupPricingBaseAccomTypeSeason.setStartDate(new LocalDate(2018, 10, 1));
        groupPricingBaseAccomTypeSeason.setEndDate(new LocalDate(2018, 10, 1));
        allPricingBaseAccomTypes.add(groupPricingBaseAccomTypeSeason);
        return allPricingBaseAccomTypes;
    }

    private void setUpCPRelatedConfigParameters() {
        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.PER_PERSON_PRICING_ENABLED.value())).thenReturn(true);
    }


    @Test
    public void saveGroupPricingBaseAccomTypes_TaxInclusiveDefault() {
        setUpCPRelatedConfigParameters();
        PricingBaseAccomType groupPricingBaseAccomType = PricingConfigurationObjectMother.buildPricingBaseAccomTypeWithTax();
        AccomType accomType = new AccomType();
        accomType.setId(1);
        groupPricingBaseAccomType.setAccomType(accomType);
        groupPricingBaseAccomType.setStartDate(null);
        groupPricingBaseAccomType.setEndDate(null);
        List<PricingBaseAccomType> groupPricingBaseAccomTypes = Collections.singletonList(groupPricingBaseAccomType);
        Map<String, Object> queryParams = getQueryParamMapForPropertyID();
        Tax tax = createAndSetupTax();


        service.saveGroupPricingBaseAccomTypes(groupPricingBaseAccomTypes);

        assertEquals(tax.removeRoomTaxRate(groupPricingBaseAccomType.getMondayCeilingRateWithTax()), groupPricingBaseAccomType.getMondayCeilingRate());
        assertEquals(tax.removeRoomTaxRate(groupPricingBaseAccomType.getMondayFloorRateWithTax()), groupPricingBaseAccomType.getMondayFloorRate());

        assertEquals(tax.removeRoomTaxRate(groupPricingBaseAccomType.getTuesdayCeilingRateWithTax()), groupPricingBaseAccomType.getTuesdayCeilingRate());
        assertEquals(tax.removeRoomTaxRate(groupPricingBaseAccomType.getTuesdayFloorRateWithTax()), groupPricingBaseAccomType.getTuesdayFloorRate());

        assertEquals(tax.removeRoomTaxRate(groupPricingBaseAccomType.getWednesdayCeilingRateWithTax()), groupPricingBaseAccomType.getWednesdayCeilingRate());
        assertEquals(tax.removeRoomTaxRate(groupPricingBaseAccomType.getWednesdayFloorRateWithTax()), groupPricingBaseAccomType.getWednesdayFloorRate());

        assertEquals(tax.removeRoomTaxRate(groupPricingBaseAccomType.getThursdayCeilingRateWithTax()), groupPricingBaseAccomType.getThursdayCeilingRate());
        assertEquals(tax.removeRoomTaxRate(groupPricingBaseAccomType.getThursdayFloorRateWithTax()), groupPricingBaseAccomType.getThursdayFloorRate());

        assertEquals(tax.removeRoomTaxRate(groupPricingBaseAccomType.getFridayCeilingRateWithTax()), groupPricingBaseAccomType.getFridayCeilingRate());
        assertEquals(tax.removeRoomTaxRate(groupPricingBaseAccomType.getFridayFloorRateWithTax()), groupPricingBaseAccomType.getFridayFloorRate());

        assertEquals(tax.removeRoomTaxRate(groupPricingBaseAccomType.getSaturdayCeilingRateWithTax()), groupPricingBaseAccomType.getSaturdayCeilingRate());
        assertEquals(tax.removeRoomTaxRate(groupPricingBaseAccomType.getSaturdayFloorRateWithTax()), groupPricingBaseAccomType.getSaturdayFloorRate());

        assertEquals(tax.removeRoomTaxRate(groupPricingBaseAccomType.getSundayCeilingRateWithTax()), groupPricingBaseAccomType.getSundayCeilingRate());
        assertEquals(tax.removeRoomTaxRate(groupPricingBaseAccomType.getSundayFloorRateWithTax()), groupPricingBaseAccomType.getSundayFloorRate());

        verify(tenantCrudService).save(groupPricingBaseAccomTypes);
        verify(taxInclusiveMigrationService).handleGroupPriceExcludedBaseRoomTypeSupplementOnSave();
        verify(syncEventAggregatorService).registerSyncEvent(SyncEvent.PRICING_CONFIG_CHANGED);
        verify(tenantCrudService, never()).findByNamedQuery(GroupPricingBaseAccomType.FIND_PRICEABLE_SEASONS_BY_PROPERTY_ID, queryParams);
    }

    private Tax createAndSetupTax() {
        Tax tax = new Tax();
        tax.setRoomTaxRate(BigDecimal.valueOf(8));
        when(taxService.findTax()).thenReturn(tax);
        return tax;
    }

    @Test
    public void isBaseAccomTypeSetupComplete() {
        AccomClass accomClass1 = GroupPricingConfigurationObjectMother.buildAccomClass("STD", 10);

        PricingAccomClass pricingAccomClass1 = PricingConfigurationObjectMother.buildPricingAccomClass();
        pricingAccomClass1.setAccomClass(accomClass1);

        Map<String, Object> queryParams = getQueryParamMapForPropertyID();

        when(multiPropertyCrudService.findByNamedQueryForSingleProperty(PacmanWorkContextHelper.getPropertyId(), PricingAccomClass.FIND_BY_PROPERTY_ID, queryParams))
                .thenReturn(Arrays.asList(pricingAccomClass1));

        assertTrue(service.isBaseAccomTypeSetupComplete(PacmanWorkContextHelper.getPropertyId(), Arrays.asList(accomClass1)));
    }

    @Test
    public void isBaseAccomTypeSetupComplete_AccomTypeNotSet() {
        AccomClass accomClass1 = GroupPricingConfigurationObjectMother.buildAccomClass("STD", 10);

        PricingAccomClass pricingAccomClass1 = PricingConfigurationObjectMother.buildPricingAccomClass();
        pricingAccomClass1.setAccomClass(accomClass1);
        pricingAccomClass1.setAccomType(null);

        Map<String, Object> queryParams = getQueryParamMapForPropertyID();
        when(multiPropertyCrudService.findByNamedQueryForSingleProperty(PacmanWorkContextHelper.getPropertyId(), PricingAccomClass.FIND_BY_PROPERTY_ID, queryParams))
                .thenReturn(Arrays.asList(pricingAccomClass1));

        when(groupPricingConfigurationService.getAllActiveNonDefaultAccomClassByViewOrder())
                .thenReturn(new ArrayList<AccomClass>());

        assertFalse(service.isBaseAccomTypeSetupComplete());
    }

    @Test
    public void isBaseAccomTypeSetupComplete_NewAccomClassAdded() {
        AccomClass accomClass1 = GroupPricingConfigurationObjectMother.buildAccomClass("STD", 10);
        AccomClass accomClass2 = GroupPricingConfigurationObjectMother.buildAccomClass("DLX", 10);

        PricingAccomClass pricingAccomClass1 = PricingConfigurationObjectMother.buildPricingAccomClass();
        pricingAccomClass1.setAccomClass(accomClass1);

        Map<String, Object> queryParams = getQueryParamMapForPropertyID();
        when(tenantCrudService.findByNamedQuery(PricingAccomClass.FIND_BY_PROPERTY_ID, queryParams))
                .thenReturn(Arrays.asList(pricingAccomClass1));

        when(groupPricingConfigurationService.getAllActiveNonDefaultAccomClassByViewOrder())
                .thenReturn(Arrays.asList(accomClass1, accomClass2));

        assertFalse(service.isBaseAccomTypeSetupComplete());
    }

    @Test
    public void isBaseAccomTypeSetupComplete_AccomClassRemoved() {
        AccomClass accomClass1 = GroupPricingConfigurationObjectMother.buildAccomClass("STD", 10);

        PricingAccomClass pricingAccomClass1 = PricingConfigurationObjectMother.buildPricingAccomClass();
        pricingAccomClass1.setAccomClass(accomClass1);

        Map<String, Object> queryParams = getQueryParamMapForPropertyID();
        when(multiPropertyCrudService.findByNamedQueryForSingleProperty(PacmanWorkContextHelper.getPropertyId(), PricingAccomClass.FIND_BY_PROPERTY_ID, queryParams))
                .thenReturn(Arrays.asList(pricingAccomClass1));

        when(groupPricingConfigurationService.getAllActiveNonDefaultAccomClassByViewOrder())
                .thenReturn(new ArrayList<AccomClass>());

        assertFalse(service.isBaseAccomTypeSetupComplete());
    }

    @Test
    public void isBaseRoomTypeConfigurationComplete() {
        when(multiPropertyCrudService.findByNamedQuerySingleResultForSingleProperty(PacmanWorkContextHelper.getPropertyId(),
                PricingAccomClass.FIND_COUNT_OF_AC_WITHOUT_BASE_AT)).thenReturn(Integer.valueOf(0));
        assertTrue(service.isBaseRoomTypeConfigurationComplete());
        verify(multiPropertyCrudService).findByNamedQuerySingleResultForSingleProperty(PacmanWorkContextHelper.getPropertyId(),
                PricingAccomClass.FIND_COUNT_OF_AC_WITHOUT_BASE_AT);
    }

    @Test
    public void isBaseRoomTypeFloorAndCeilingConfigurationComplete() {
        when(multiPropertyCrudService.findByNamedQuerySingleResultForSingleProperty(eq(PacmanWorkContextHelper.getPropertyId()),
                eq(CPConfigMergedCeilingAndFloor.FIND_COUNT_OF_AC_WITHOUT_DEFAULT_FLOOR_AND_CEILING_FOR_BAR), anyMap())).thenReturn(Integer.valueOf(0));
        assertTrue(service.isBaseRoomTypeFloorAndCeilingConfigurationComplete());
        verify(multiPropertyCrudService).findByNamedQuerySingleResultForSingleProperty(eq(PacmanWorkContextHelper.getPropertyId()),
                eq(CPConfigMergedCeilingAndFloor.FIND_COUNT_OF_AC_WITHOUT_DEFAULT_FLOOR_AND_CEILING_FOR_BAR), anyMap());
    }

    @Test
    public void getDefaultCPConfigAccomClassMinPriceDiff() {
        List<AccomClassMinPriceDiff> accomClassMinPriceDiffs = Arrays.asList(new AccomClassMinPriceDiff());

        when(tenantCrudService.findAll(AccomClassMinPriceDiff.class)).thenReturn(accomClassMinPriceDiffs);

        assertEquals(accomClassMinPriceDiffs, service.getDefaultAccomClassMinPriceDiff());

        verify(tenantCrudService).findAll(AccomClassMinPriceDiff.class);
    }

    @Test
    public void getSeasonCPConfigAccomClassMinPriceDiffs() {
        List<AccomClassMinPriceDiffSeason> accomClassMinPriceDiffSeasons = Arrays.asList(new AccomClassMinPriceDiffSeason());

        when(tenantCrudService.findAll(AccomClassMinPriceDiffSeason.class)).thenReturn(accomClassMinPriceDiffSeasons);

        assertEquals(accomClassMinPriceDiffSeasons, service.getSeasonAccomClassMinPriceDiffs());

        verify(tenantCrudService).findAll(AccomClassMinPriceDiffSeason.class);
    }

    @Test
    public void saveDefaultCpConfigAccomClassMinPriceDiff() {
        AccomClassMinPriceDiff accomClassMinPriceDiff = new AccomClassMinPriceDiff();

        service.saveAccomClassMinPriceDiff(accomClassMinPriceDiff);

        verify(tenantCrudService).save(accomClassMinPriceDiff);
    }

    @Test
    public void saveDefaultCpConfigAccomClassMinPriceDiffs() {
        List<AccomClassMinPriceDiff> accomClassMinPriceDiffs = new ArrayList<>();

        service.saveAccomClassMinPriceDiffs(accomClassMinPriceDiffs);

        verify(tenantCrudService).save(accomClassMinPriceDiffs);
    }

    @Test
    public void saveDefaultCpConfigAccomClassMinPriceDiffSeasons_noExistingSeasons() {
        AccomClassMinPriceDiffSeason season1 = new AccomClassMinPriceDiffSeason();
        AccomClassMinPriceDiffSeason season2 = new AccomClassMinPriceDiffSeason();
        List<AccomClassMinPriceDiffSeason> accomClassMinPriceDiffSeasons = Arrays.asList(season1, season2);

        when(tenantCrudService.findAll(AccomClassMinPriceDiffSeason.class)).thenReturn(Collections.EMPTY_LIST);

        service.saveAccomClassMinPriceDiffSeasons(accomClassMinPriceDiffSeasons);

        verify(tenantCrudService).save(season1);
        verify(tenantCrudService).save(season2);
        verify(tenantCrudService, times(0)).delete(anyObject());
    }

    @Test
    void shouldNotCallToEnableForPricingWhenAnyApplicableChangeMadeButLTBDEForPricingConfigToggleIsDisabledOrNoSmallGroupProduct() {
        AccomClassMinPriceDiffSeason season1 = new AccomClassMinPriceDiffSeason();
        season1.setId(null);
        season1.setEndDate(JavaLocalDateUtils.toJodaLocalDate(java.time.LocalDate.parse("2024-01-01")));
        List<AccomClassMinPriceDiffSeason> accomClassMinPriceDiffSeasons = List.of(season1);

        when(tenantCrudService.findAll(AccomClassMinPriceDiffSeason.class)).thenReturn(Collections.EMPTY_LIST);
        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(java.time.LocalDate.parse("2022-01-01"));
        when(pricingConfigurationLTBDEService.isLTBDEOnConfigChangeEnabled()).thenReturn(false);

        service.saveAccomClassMinPriceDiffSeasons(accomClassMinPriceDiffSeasons);

        verify(tenantCrudService).save(season1);
        verify(pricingConfigurationLTBDEService, times(0)).enableLTBDEForPricing(true);
    }

    @Test
    void shouldCallToEnableLTBDEForPricingWhenAccomClassMinPriceDiffSeasonIsAddedAndIsBeyondOptimizationWindow() {
        AccomClassMinPriceDiffSeason season1 = new AccomClassMinPriceDiffSeason();
        season1.setId(null);
        season1.setEndDate(JavaLocalDateUtils.toJodaLocalDate(java.time.LocalDate.parse("2024-01-01")));
        List<AccomClassMinPriceDiffSeason> accomClassMinPriceDiffSeasons = List.of(season1);

        when(tenantCrudService.findAll(AccomClassMinPriceDiffSeason.class)).thenReturn(Collections.EMPTY_LIST);
        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(java.time.LocalDate.parse("2022-01-01"));
        when(pricingConfigurationLTBDEService.isLTBDEOnIndirectConfigChangedEnabled()).thenReturn(true);
        when(pricingConfigurationLTBDEService.isDateBeyondOptimizationWindow(JavaLocalDateUtils.toJavaLocalDate(season1.getEndDate()), java.time.LocalDate.parse("2022-01-01"))).thenReturn(true);

        service.saveAccomClassMinPriceDiffSeasons(accomClassMinPriceDiffSeasons);

        verify(tenantCrudService).save(season1);
        verify(pricingConfigurationLTBDEService).enableLTBDEForPricing(true);
    }

    @Test
    void shouldNotCallToEnableLTBDEForPricingWhenAccomClassMinPriceDiffSeasonIsAddedAndIsWithinOptimizationWindow() {
        AccomClassMinPriceDiffSeason season1 = new AccomClassMinPriceDiffSeason();
        season1.setId(null);
        season1.setEndDate(JavaLocalDateUtils.toJodaLocalDate(java.time.LocalDate.parse("2023-01-01")));
        List<AccomClassMinPriceDiffSeason> accomClassMinPriceDiffSeasons = List.of(season1);

        when(tenantCrudService.findAll(AccomClassMinPriceDiffSeason.class)).thenReturn(Collections.EMPTY_LIST);
        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(java.time.LocalDate.parse("2022-12-01"));
        when(pricingConfigurationLTBDEService.isLTBDEOnConfigChangeEnabled()).thenReturn(true);
        when(pricingConfigurationLTBDEService.isDateBeyondOptimizationWindow(JavaLocalDateUtils.toJavaLocalDate(season1.getEndDate()), java.time.LocalDate.parse("2022-12-01"))).thenReturn(false);

        service.saveAccomClassMinPriceDiffSeasons(accomClassMinPriceDiffSeasons);

        verify(tenantCrudService).save(season1);
        verify(pricingConfigurationLTBDEService, times(0)).enableLTBDEForPricing(true);
    }

    @Test
    void shouldCallToEnableLTBDEForPricingWhenAccomClassMinPriceDiffSeasonIsDeletedWhenLTBDEPricingConfigToggleIsEnabledAndSeasonIsBeyondOptimizationWindow() {
        verify(tenantCrudService, times(0)).delete(anyObject());
        AccomClassMinPriceDiffSeason season1 = new AccomClassMinPriceDiffSeason();
        season1.setId(1);
        season1.setEndDate(JavaLocalDateUtils.toJodaLocalDate(java.time.LocalDate.parse("2024-01-01")));

        AccomClassMinPriceDiffSeason season2 = new AccomClassMinPriceDiffSeason();
        season2.setId(2);
        season2.setEndDate(JavaLocalDateUtils.toJodaLocalDate(java.time.LocalDate.parse("2023-12-01")));

        List<AccomClassMinPriceDiffSeason> accomClassMinPriceDiffSeasons = List.of(season1);

        when(tenantCrudService.findAll(AccomClassMinPriceDiffSeason.class)).thenReturn(List.of(season1, season2));
        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(java.time.LocalDate.parse("2022-01-01"));
        when(pricingConfigurationLTBDEService.isLTBDEOnIndirectConfigChangedEnabled()).thenReturn(true);
        when(pricingConfigurationLTBDEService.isDateBeyondOptimizationWindow(java.time.LocalDate.parse("2023-12-01"),
                java.time.LocalDate.parse("2022-01-01"))).thenReturn(true);


        service.saveAccomClassMinPriceDiffSeasons(accomClassMinPriceDiffSeasons);

        verify(tenantCrudService).save(season1);
        verify(tenantCrudService, times(1)).delete(anyObject());
        verify(pricingConfigurationLTBDEService).enableLTBDEForPricing(true);
    }

    @Test
    void shouldNotCallToEnableLTBDEForPricingWhenAccomClassMinPriceDiffSeasonIsDeletedWhenLTBDEPricingConfigToggleIsDisabled() {
        verify(tenantCrudService, times(0)).delete(anyObject());
        AccomClassMinPriceDiffSeason season1 = new AccomClassMinPriceDiffSeason();
        season1.setId(1);
        season1.setEndDate(JavaLocalDateUtils.toJodaLocalDate(java.time.LocalDate.parse("2024-01-01")));

        AccomClassMinPriceDiffSeason season2 = new AccomClassMinPriceDiffSeason();
        season2.setId(2);
        season2.setEndDate(JavaLocalDateUtils.toJodaLocalDate(java.time.LocalDate.parse("2023-01-01")));

        List<AccomClassMinPriceDiffSeason> accomClassMinPriceDiffSeasons = List.of(season1);

        when(tenantCrudService.findAll(AccomClassMinPriceDiffSeason.class)).thenReturn(List.of(season1, season2));
        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(java.time.LocalDate.parse("2022-01-01"));
        when(pricingConfigurationLTBDEService.isLTBDEOnConfigChangeEnabled()).thenReturn(false);

        service.saveAccomClassMinPriceDiffSeasons(accomClassMinPriceDiffSeasons);

        verify(tenantCrudService).save(season1);
        verify(tenantCrudService, times(1)).delete(anyObject());
        verify(pricingConfigurationLTBDEService, times(0)).enableLTBDEForPricing(true);
    }

    @Test
    void shouldNotCallToEnableLTBDEForPricingWhenAccomClassMinPriceDiffSeasonIsDeletedWhenLTBDEPricingConfigToggleIsEnabledAndSeasonIsWithinOptimizationWindow() {
        verify(tenantCrudService, times(0)).delete(anyObject());
        AccomClassMinPriceDiffSeason season1 = new AccomClassMinPriceDiffSeason();
        season1.setId(1);
        season1.setEndDate(JavaLocalDateUtils.toJodaLocalDate(java.time.LocalDate.parse("2024-01-01")));

        AccomClassMinPriceDiffSeason season2 = new AccomClassMinPriceDiffSeason();
        season2.setId(2);
        season2.setEndDate(JavaLocalDateUtils.toJodaLocalDate(java.time.LocalDate.parse("2023-11-01")));

        List<AccomClassMinPriceDiffSeason> accomClassMinPriceDiffSeasons = List.of(season1);

        when(tenantCrudService.findAll(AccomClassMinPriceDiffSeason.class)).thenReturn(List.of(season1, season2));
        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(java.time.LocalDate.parse("2023-01-01"));
        when(pricingConfigurationLTBDEService.isLTBDEOnConfigChangeEnabled()).thenReturn(true);
        when(pricingConfigurationLTBDEService.isDateBeyondOptimizationWindow(java.time.LocalDate.parse("2023-11-01"),
                java.time.LocalDate.parse("2023-01-01"))).thenReturn(false);


        service.saveAccomClassMinPriceDiffSeasons(accomClassMinPriceDiffSeasons);

        verify(tenantCrudService).save(season1);
        verify(tenantCrudService, times(1)).delete(anyObject());
        verify(pricingConfigurationLTBDEService, times(0)).enableLTBDEForPricing(true);
    }

    @Test
    void shouldNotCallToEnableLTBDEForPricingWhenAccomClassMinPriceDiffSeasonValuesIsUpdatedAndDateIsBeyondOptimizationWindowAndLTBDEForConfigIsDisabled() {
        AccomClassMinPriceDiffSeason existingSeason = new AccomClassMinPriceDiffSeason();
        existingSeason.setId(1);
        existingSeason.setFridayDiffWithTax(BigDecimal.ONE);
        existingSeason.setEndDate(JavaLocalDateUtils.toJodaLocalDate(java.time.LocalDate.parse("2024-01-01")));

        AccomClassMinPriceDiffSeason updatedSeason = new AccomClassMinPriceDiffSeason();
        updatedSeason.setId(1);
        updatedSeason.setFridayDiffWithTax(BigDecimal.TEN);
        updatedSeason.setEndDate(JavaLocalDateUtils.toJodaLocalDate(java.time.LocalDate.parse("2024-01-01")));

        List<AccomClassMinPriceDiffSeason> accomClassMinPriceDiffSeasons = List.of(updatedSeason);

        when(tenantCrudService.findAll(AccomClassMinPriceDiffSeason.class)).thenReturn(List.of(existingSeason));
        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(java.time.LocalDate.parse("2022-01-01"));
        when(pricingConfigurationLTBDEService.isLTBDEOnConfigChangeEnabled()).thenReturn(false);

        service.saveAccomClassMinPriceDiffSeasons(accomClassMinPriceDiffSeasons);

        verify(tenantCrudService).save(updatedSeason);
        verify(tenantCrudService, times(0)).delete(anyObject());
        verify(pricingConfigurationLTBDEService, times(0)).enableLTBDEForPricing(true);
    }

    @Test
    void shouldCallToEnableLTBDEForPricingWhenAccomClassMinPriceDiffSeasonValuesIsUpdatedAndDateIsBeyondOptimizationWindow() {
        AccomClassMinPriceDiffSeason existingSeason = new AccomClassMinPriceDiffSeason();
        existingSeason.setId(1);
        existingSeason.setFridayDiffWithTax(BigDecimal.ONE);
        existingSeason.setSaturdayDiffWithTax(null);
        existingSeason.setSundayDiffWithTax(null);
        existingSeason.setEndDate(JavaLocalDateUtils.toJodaLocalDate(java.time.LocalDate.parse("2024-01-01")));

        AccomClassMinPriceDiffSeason updatedSeason = new AccomClassMinPriceDiffSeason();
        updatedSeason.setId(1);
        updatedSeason.setFridayDiffWithTax(BigDecimal.TEN);
        updatedSeason.setSaturdayDiffWithTax(null);
        updatedSeason.setSundayDiffWithTax(BigDecimal.ONE);
        updatedSeason.setEndDate(JavaLocalDateUtils.toJodaLocalDate(java.time.LocalDate.parse("2024-01-01")));

        List<AccomClassMinPriceDiffSeason> accomClassMinPriceDiffSeasons = List.of(updatedSeason);

        when(tenantCrudService.findAll(AccomClassMinPriceDiffSeason.class)).thenReturn(List.of(existingSeason));
        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(java.time.LocalDate.parse("2022-01-01"));
        when(pricingConfigurationLTBDEService.isLTBDEOnIndirectConfigChangedEnabled()).thenReturn(true);
        when(pricingConfigurationLTBDEService.isDateBeyondOptimizationWindow(JavaLocalDateUtils.toJavaLocalDate(updatedSeason.getEndDate()), java.time.LocalDate.parse("2022-01-01"))).thenReturn(true);


        service.saveAccomClassMinPriceDiffSeasons(accomClassMinPriceDiffSeasons);

        verify(tenantCrudService).save(updatedSeason);
        verify(tenantCrudService, times(0)).delete(anyObject());
        verify(pricingConfigurationLTBDEService).enableLTBDEForPricing(true);
    }

    @Test
    void shouldNotCallToEnableLTBDEForPricingWhenAccomClassMinPriceDiffSeasonValuesIsUpdatedAndEndDateIsWithinOptimizationWindow() {
        AccomClassMinPriceDiffSeason existingSeason = new AccomClassMinPriceDiffSeason();
        existingSeason.setId(1);
        existingSeason.setFridayDiffWithTax(BigDecimal.ONE);
        existingSeason.setEndDate(JavaLocalDateUtils.toJodaLocalDate(java.time.LocalDate.parse("2024-01-01")));

        AccomClassMinPriceDiffSeason updatedSeason = new AccomClassMinPriceDiffSeason();
        updatedSeason.setId(1);
        updatedSeason.setFridayDiffWithTax(BigDecimal.TEN);
        updatedSeason.setEndDate(JavaLocalDateUtils.toJodaLocalDate(java.time.LocalDate.parse("2024-01-01")));

        List<AccomClassMinPriceDiffSeason> accomClassMinPriceDiffSeasons = List.of(updatedSeason);

        when(tenantCrudService.findAll(AccomClassMinPriceDiffSeason.class)).thenReturn(List.of(existingSeason));
        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(java.time.LocalDate.parse("2023-12-01"));
        when(pricingConfigurationLTBDEService.isLTBDEOnConfigChangeEnabled()).thenReturn(true);
        when(pricingConfigurationLTBDEService.isDateBeyondOptimizationWindow(JavaLocalDateUtils.toJavaLocalDate(updatedSeason.getEndDate()), java.time.LocalDate.parse("2023-12-01"))).thenReturn(false);


        service.saveAccomClassMinPriceDiffSeasons(accomClassMinPriceDiffSeasons);

        verify(tenantCrudService).save(updatedSeason);
        verify(tenantCrudService, times(0)).delete(anyObject());
        verify(pricingConfigurationLTBDEService, times(0)).enableLTBDEForPricing(true);
    }

    @Test
    void shouldCallToEnableLTBDEForPricingWhenAccomClassMinPriceDiffSeasonValueAndEndDateAreUpdated() {
        java.time.LocalDate caughtUpDate = java.time.LocalDate.parse("2022-01-01");

        AccomClassMinPriceDiffSeason existingSeason = new AccomClassMinPriceDiffSeason();
        existingSeason.setId(1);
        existingSeason.setFridayDiffWithTax(BigDecimal.ONE);
        existingSeason.setEndDate(JavaLocalDateUtils.toJodaLocalDate(java.time.LocalDate.parse("2024-01-01")));

        AccomClassMinPriceDiffSeason updatedSeason = new AccomClassMinPriceDiffSeason();
        updatedSeason.setId(1);
        updatedSeason.setFridayDiffWithTax(BigDecimal.TEN);
        updatedSeason.setEndDate(JavaLocalDateUtils.toJodaLocalDate(java.time.LocalDate.parse("2024-02-01")));

        List<AccomClassMinPriceDiffSeason> accomClassMinPriceDiffSeasons = List.of(updatedSeason);

        when(pricingConfigurationLTBDEService.isLTBDEOnIndirectConfigChangedEnabled()).thenReturn(true);
        when(tenantCrudService.findAll(AccomClassMinPriceDiffSeason.class)).thenReturn(List.of(existingSeason));
        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(caughtUpDate);
        when(pricingConfigurationLTBDEService.isDateBeyondOptimizationWindow(JavaLocalDateUtils.toJavaLocalDate(existingSeason.getEndDate()), caughtUpDate)).thenReturn(true);

        service.saveAccomClassMinPriceDiffSeasons(accomClassMinPriceDiffSeasons);

        verify(tenantCrudService).save(updatedSeason);
        verify(tenantCrudService, times(0)).delete(anyObject());
        verify(pricingConfigurationLTBDEService).enableLTBDEForPricing(true);

    }

    @Test
    void shouldCallToEnableLTBDEForPricingWhenAccomClassMinPriceDiffSeasonValueAndStartDateAreUpdated() {
        java.time.LocalDate caughtUpDate = java.time.LocalDate.parse("2022-01-01");

        AccomClassMinPriceDiffSeason existingSeason = new AccomClassMinPriceDiffSeason();
        existingSeason.setId(1);
        existingSeason.setFridayDiffWithTax(BigDecimal.ONE);
        existingSeason.setStartDate(JavaLocalDateUtils.toJodaLocalDate(java.time.LocalDate.parse("2024-01-01")));
        existingSeason.setEndDate(JavaLocalDateUtils.toJodaLocalDate(java.time.LocalDate.parse("2024-04-01")));

        AccomClassMinPriceDiffSeason updatedSeason = new AccomClassMinPriceDiffSeason();
        updatedSeason.setId(1);
        updatedSeason.setFridayDiffWithTax(BigDecimal.TEN);
        updatedSeason.setStartDate(JavaLocalDateUtils.toJodaLocalDate(java.time.LocalDate.parse("2024-02-01")));
        updatedSeason.setEndDate(JavaLocalDateUtils.toJodaLocalDate(java.time.LocalDate.parse("2024-04-01")));

        List<AccomClassMinPriceDiffSeason> accomClassMinPriceDiffSeasons = List.of(updatedSeason);

        when(pricingConfigurationLTBDEService.isLTBDEOnIndirectConfigChangedEnabled()).thenReturn(true);
        when(tenantCrudService.findAll(AccomClassMinPriceDiffSeason.class)).thenReturn(List.of(existingSeason));
        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(caughtUpDate);
        when(pricingConfigurationLTBDEService.isDateBeyondOptimizationWindow(JavaLocalDateUtils.toJavaLocalDate(existingSeason.getEndDate()), caughtUpDate)).thenReturn(true);

        service.saveAccomClassMinPriceDiffSeasons(accomClassMinPriceDiffSeasons);

        verify(tenantCrudService).save(updatedSeason);
        verify(tenantCrudService, times(0)).delete(anyObject());
        verify(pricingConfigurationLTBDEService).enableLTBDEForPricing(true);
    }

    @Test
    void shouldCallToEnableLTBDEForPricingWhenAccomClassMinPriceDiffSeasonValueAndDatesAreUpdated() {
        java.time.LocalDate caughtUpDate = java.time.LocalDate.parse("2023-08-01");

        AccomClassMinPriceDiffSeason existingSeason = new AccomClassMinPriceDiffSeason();
        existingSeason.setId(1);
        existingSeason.setFridayDiffWithTax(BigDecimal.ONE);
        existingSeason.setStartDate(JavaLocalDateUtils.toJodaLocalDate(java.time.LocalDate.parse("2024-09-01")));
        existingSeason.setEndDate(JavaLocalDateUtils.toJodaLocalDate(java.time.LocalDate.parse("2024-10-01")));

        AccomClassMinPriceDiffSeason updatedSeason = new AccomClassMinPriceDiffSeason();
        updatedSeason.setId(1);
        updatedSeason.setFridayDiffWithTax(BigDecimal.TEN);
        updatedSeason.setStartDate(JavaLocalDateUtils.toJodaLocalDate(java.time.LocalDate.parse("2024-02-01")));
        updatedSeason.setEndDate(JavaLocalDateUtils.toJodaLocalDate(java.time.LocalDate.parse("2024-04-05")));

        List<AccomClassMinPriceDiffSeason> accomClassMinPriceDiffSeasons = List.of(updatedSeason);

        when(pricingConfigurationLTBDEService.isLTBDEOnIndirectConfigChangedEnabled()).thenReturn(true);
        when(tenantCrudService.findAll(AccomClassMinPriceDiffSeason.class)).thenReturn(List.of(existingSeason));
        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(caughtUpDate);
        when(pricingConfigurationLTBDEService.isDateBeyondOptimizationWindow(JavaLocalDateUtils.toJavaLocalDate(existingSeason.getEndDate()), caughtUpDate)).thenReturn(true);
        when(pricingConfigurationLTBDEService.isDateBeyondOptimizationWindow(JavaLocalDateUtils.toJavaLocalDate(updatedSeason.getEndDate()), caughtUpDate)).thenReturn(false);

        service.saveAccomClassMinPriceDiffSeasons(accomClassMinPriceDiffSeasons);

        verify(tenantCrudService).save(updatedSeason);
        verify(tenantCrudService, times(0)).delete(anyObject());
        verify(pricingConfigurationLTBDEService).enableLTBDEForPricing(true);
    }

    @Test
    void shouldCallToEnableLTBDEForPricingWhenAccomClassMinPriceDiffSeasonStartDateIsChangedFromWithinOptimizationWindowToExtendedOptimizationWindow() {
        java.time.LocalDate caughtUpDate = java.time.LocalDate.parse("2023-08-01");

        AccomClassMinPriceDiffSeason existingSeason = new AccomClassMinPriceDiffSeason();
        existingSeason.setId(1);
        existingSeason.setFridayDiffWithTax(BigDecimal.ONE);
        existingSeason.setStartDate(JavaLocalDateUtils.toJodaLocalDate(java.time.LocalDate.parse("2024-07-01")));
        existingSeason.setEndDate(JavaLocalDateUtils.toJodaLocalDate(java.time.LocalDate.parse("2024-10-01")));

        AccomClassMinPriceDiffSeason updatedSeason = new AccomClassMinPriceDiffSeason();
        updatedSeason.setId(1);
        updatedSeason.setFridayDiffWithTax(BigDecimal.ONE);
        updatedSeason.setStartDate(JavaLocalDateUtils.toJodaLocalDate(java.time.LocalDate.parse("2024-09-01")));
        updatedSeason.setEndDate(JavaLocalDateUtils.toJodaLocalDate(java.time.LocalDate.parse("2024-10-01")));

        List<AccomClassMinPriceDiffSeason> accomClassMinPriceDiffSeasons = List.of(updatedSeason);

        when(pricingConfigurationLTBDEService.isLTBDEOnIndirectConfigChangedEnabled()).thenReturn(true);
        when(tenantCrudService.findAll(AccomClassMinPriceDiffSeason.class)).thenReturn(List.of(existingSeason));
        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(caughtUpDate);
        when(pricingConfigurationLTBDEService.isDateBeyondOptimizationWindow(JavaLocalDateUtils.toJavaLocalDate(existingSeason.getEndDate()), caughtUpDate)).thenReturn(false);
        when(pricingConfigurationLTBDEService.isDateBeyondOptimizationWindow(JavaLocalDateUtils.toJavaLocalDate(updatedSeason.getEndDate()), caughtUpDate)).thenReturn(true);

        service.saveAccomClassMinPriceDiffSeasons(accomClassMinPriceDiffSeasons);

        verify(tenantCrudService).save(updatedSeason);
        verify(tenantCrudService, times(0)).delete(anyObject());
        verify(pricingConfigurationLTBDEService, times(1)).enableLTBDEForPricing(true);
    }

    @Test
    void shouldNotCallToEnableLTBDEForPricingWhenAccomClassMinPriceDiffSeasonStartDateIsChangedFromExtendedOptimizationWindowToWithinOptimizationWindow() {
        java.time.LocalDate caughtUpDate = java.time.LocalDate.parse("2023-08-01");

        AccomClassMinPriceDiffSeason existingSeason = new AccomClassMinPriceDiffSeason();
        existingSeason.setId(1);
        existingSeason.setFridayDiffWithTax(BigDecimal.ONE);
        existingSeason.setStartDate(JavaLocalDateUtils.toJodaLocalDate(java.time.LocalDate.parse("2024-09-01")));
        existingSeason.setEndDate(JavaLocalDateUtils.toJodaLocalDate(java.time.LocalDate.parse("2024-10-01")));

        AccomClassMinPriceDiffSeason updatedSeason = new AccomClassMinPriceDiffSeason();
        updatedSeason.setId(1);
        updatedSeason.setFridayDiffWithTax(BigDecimal.ONE);
        updatedSeason.setStartDate(JavaLocalDateUtils.toJodaLocalDate(java.time.LocalDate.parse("2024-07-01")));
        updatedSeason.setEndDate(JavaLocalDateUtils.toJodaLocalDate(java.time.LocalDate.parse("2024-10-01")));

        List<AccomClassMinPriceDiffSeason> accomClassMinPriceDiffSeasons = List.of(updatedSeason);

        when(pricingConfigurationLTBDEService.isLTBDEOnConfigChangeEnabled()).thenReturn(true);
        when(tenantCrudService.findAll(AccomClassMinPriceDiffSeason.class)).thenReturn(List.of(existingSeason));
        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(caughtUpDate);
        when(pricingConfigurationLTBDEService.isDateBeyondOptimizationWindow(JavaLocalDateUtils.toJavaLocalDate(existingSeason.getEndDate()), caughtUpDate)).thenReturn(true);
        when(pricingConfigurationLTBDEService.isDateBeyondOptimizationWindow(JavaLocalDateUtils.toJavaLocalDate(updatedSeason.getEndDate()), caughtUpDate)).thenReturn(false);

        service.saveAccomClassMinPriceDiffSeasons(accomClassMinPriceDiffSeasons);

        verify(tenantCrudService).save(updatedSeason);
        verify(tenantCrudService, times(0)).delete(anyObject());
        verify(pricingConfigurationLTBDEService, times(0)).enableLTBDEForPricing(true);
    }

    @Test
    void shouldCallToEnableLTBDEForPricingWhenAccomClassMinPriceDiffSeasonStartDateIsChangedAndInExtendedOptimizationWindow() {
        java.time.LocalDate caughtUpDate = java.time.LocalDate.parse("2023-08-01");

        AccomClassMinPriceDiffSeason existingSeason = new AccomClassMinPriceDiffSeason();
        existingSeason.setId(1);
        existingSeason.setFridayDiffWithTax(BigDecimal.ONE);
        existingSeason.setStartDate(JavaLocalDateUtils.toJodaLocalDate(java.time.LocalDate.parse("2024-09-01")));
        existingSeason.setEndDate(JavaLocalDateUtils.toJodaLocalDate(java.time.LocalDate.parse("2024-10-01")));

        AccomClassMinPriceDiffSeason updatedSeason = new AccomClassMinPriceDiffSeason();
        updatedSeason.setId(1);
        updatedSeason.setFridayDiffWithTax(BigDecimal.ONE);
        updatedSeason.setStartDate(JavaLocalDateUtils.toJodaLocalDate(java.time.LocalDate.parse("2024-09-12")));
        updatedSeason.setEndDate(JavaLocalDateUtils.toJodaLocalDate(java.time.LocalDate.parse("2024-10-01")));

        List<AccomClassMinPriceDiffSeason> accomClassMinPriceDiffSeasons = List.of(updatedSeason);

        when(pricingConfigurationLTBDEService.isLTBDEOnIndirectConfigChangedEnabled()).thenReturn(true);
        when(tenantCrudService.findAll(AccomClassMinPriceDiffSeason.class)).thenReturn(List.of(existingSeason));
        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(caughtUpDate);
        when(pricingConfigurationLTBDEService.isDateBeyondOptimizationWindow(JavaLocalDateUtils.toJavaLocalDate(existingSeason.getEndDate()), caughtUpDate)).thenReturn(true);
        when(pricingConfigurationLTBDEService.isDateBeyondOptimizationWindow(JavaLocalDateUtils.toJavaLocalDate(updatedSeason.getEndDate()), caughtUpDate)).thenReturn(true);

        service.saveAccomClassMinPriceDiffSeasons(accomClassMinPriceDiffSeasons);

        verify(tenantCrudService).save(updatedSeason);
        verify(tenantCrudService, times(0)).delete(anyObject());
        verify(pricingConfigurationLTBDEService, times(1)).enableLTBDEForPricing(true);
    }

    @Test
    void shouldNotCallToEnableLTBDEForPricingWhenAccomClassMinPriceDiffSeasonStartDateIsChangedAndInWithinOptimizationWindow() {
        java.time.LocalDate caughtUpDate = java.time.LocalDate.parse("2023-08-01");

        AccomClassMinPriceDiffSeason existingSeason = new AccomClassMinPriceDiffSeason();
        existingSeason.setId(1);
        existingSeason.setFridayDiffWithTax(BigDecimal.ONE);
        existingSeason.setStartDate(JavaLocalDateUtils.toJodaLocalDate(java.time.LocalDate.parse("2023-12-01")));
        existingSeason.setEndDate(JavaLocalDateUtils.toJodaLocalDate(java.time.LocalDate.parse("2024-01-01")));

        AccomClassMinPriceDiffSeason updatedSeason = new AccomClassMinPriceDiffSeason();
        updatedSeason.setId(1);
        updatedSeason.setFridayDiffWithTax(BigDecimal.ONE);
        updatedSeason.setStartDate(JavaLocalDateUtils.toJodaLocalDate(java.time.LocalDate.parse("2024-12-12")));
        updatedSeason.setEndDate(JavaLocalDateUtils.toJodaLocalDate(java.time.LocalDate.parse("2024-01-01")));

        List<AccomClassMinPriceDiffSeason> accomClassMinPriceDiffSeasons = List.of(updatedSeason);

        when(pricingConfigurationLTBDEService.isLTBDEOnConfigChangeEnabled()).thenReturn(true);
        when(tenantCrudService.findAll(AccomClassMinPriceDiffSeason.class)).thenReturn(List.of(existingSeason));
        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(caughtUpDate);
        when(pricingConfigurationLTBDEService.isDateBeyondOptimizationWindow(JavaLocalDateUtils.toJavaLocalDate(existingSeason.getEndDate()), caughtUpDate)).thenReturn(false);
        when(pricingConfigurationLTBDEService.isDateBeyondOptimizationWindow(JavaLocalDateUtils.toJavaLocalDate(updatedSeason.getEndDate()), caughtUpDate)).thenReturn(false);

        service.saveAccomClassMinPriceDiffSeasons(accomClassMinPriceDiffSeasons);

        verify(tenantCrudService).save(updatedSeason);
        verify(tenantCrudService, times(0)).delete(anyObject());
        verify(pricingConfigurationLTBDEService, times(0)).enableLTBDEForPricing(true);
    }

    @Test
    void shouldNotCallToEnableLTBDEForPricingWhenAccomClassMinPriceDiffSeasonEndDateIsChangedAndInWithinOptimizationWindow() {
        java.time.LocalDate caughtUpDate = java.time.LocalDate.parse("2023-08-01");

        AccomClassMinPriceDiffSeason existingSeason = new AccomClassMinPriceDiffSeason();
        existingSeason.setId(1);
        existingSeason.setFridayDiffWithTax(BigDecimal.ONE);
        existingSeason.setStartDate(JavaLocalDateUtils.toJodaLocalDate(java.time.LocalDate.parse("2023-12-01")));
        existingSeason.setEndDate(JavaLocalDateUtils.toJodaLocalDate(java.time.LocalDate.parse("2024-01-01")));

        AccomClassMinPriceDiffSeason updatedSeason = new AccomClassMinPriceDiffSeason();
        updatedSeason.setId(1);
        updatedSeason.setFridayDiffWithTax(BigDecimal.ONE);
        updatedSeason.setStartDate(JavaLocalDateUtils.toJodaLocalDate(java.time.LocalDate.parse("2024-12-01")));
        updatedSeason.setEndDate(JavaLocalDateUtils.toJodaLocalDate(java.time.LocalDate.parse("2024-02-01")));

        List<AccomClassMinPriceDiffSeason> accomClassMinPriceDiffSeasons = List.of(updatedSeason);

        when(pricingConfigurationLTBDEService.isLTBDEOnConfigChangeEnabled()).thenReturn(true);
        when(tenantCrudService.findAll(AccomClassMinPriceDiffSeason.class)).thenReturn(List.of(existingSeason));
        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(caughtUpDate);
        when(pricingConfigurationLTBDEService.isDateBeyondOptimizationWindow(JavaLocalDateUtils.toJavaLocalDate(existingSeason.getEndDate()), caughtUpDate)).thenReturn(false);
        when(pricingConfigurationLTBDEService.isDateBeyondOptimizationWindow(JavaLocalDateUtils.toJavaLocalDate(updatedSeason.getEndDate()), caughtUpDate)).thenReturn(false);

        service.saveAccomClassMinPriceDiffSeasons(accomClassMinPriceDiffSeasons);

        verify(tenantCrudService).save(updatedSeason);
        verify(tenantCrudService, times(0)).delete(anyObject());
        verify(pricingConfigurationLTBDEService, times(0)).enableLTBDEForPricing(true);
    }

    @Test
    void shouldCallToEnableLTBDEForPricingWhenAccomClassMinPriceDiffSeasonEndDateIsChangedAndInExtendedOptimizationWindow() {
        java.time.LocalDate caughtUpDate = java.time.LocalDate.parse("2023-08-01");

        AccomClassMinPriceDiffSeason existingSeason = new AccomClassMinPriceDiffSeason();
        existingSeason.setId(1);
        existingSeason.setFridayDiffWithTax(BigDecimal.ONE);
        existingSeason.setStartDate(JavaLocalDateUtils.toJodaLocalDate(java.time.LocalDate.parse("2023-12-01")));
        existingSeason.setEndDate(JavaLocalDateUtils.toJodaLocalDate(java.time.LocalDate.parse("2024-12-01")));

        AccomClassMinPriceDiffSeason updatedSeason = new AccomClassMinPriceDiffSeason();
        updatedSeason.setId(1);
        updatedSeason.setFridayDiffWithTax(BigDecimal.ONE);
        updatedSeason.setStartDate(JavaLocalDateUtils.toJodaLocalDate(java.time.LocalDate.parse("2024-12-01")));
        updatedSeason.setEndDate(JavaLocalDateUtils.toJodaLocalDate(java.time.LocalDate.parse("2024-12-12")));

        List<AccomClassMinPriceDiffSeason> accomClassMinPriceDiffSeasons = List.of(updatedSeason);

        when(pricingConfigurationLTBDEService.isLTBDEOnIndirectConfigChangedEnabled()).thenReturn(true);
        when(tenantCrudService.findAll(AccomClassMinPriceDiffSeason.class)).thenReturn(List.of(existingSeason));
        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(caughtUpDate);
        when(pricingConfigurationLTBDEService.isDateBeyondOptimizationWindow(JavaLocalDateUtils.toJavaLocalDate(existingSeason.getEndDate()), caughtUpDate)).thenReturn(true);
        when(pricingConfigurationLTBDEService.isDateBeyondOptimizationWindow(JavaLocalDateUtils.toJavaLocalDate(updatedSeason.getEndDate()), caughtUpDate)).thenReturn(true);

        service.saveAccomClassMinPriceDiffSeasons(accomClassMinPriceDiffSeasons);

        verify(tenantCrudService).save(updatedSeason);
        verify(tenantCrudService, times(0)).delete(anyObject());
        verify(pricingConfigurationLTBDEService, times(1)).enableLTBDEForPricing(true);
    }

    @Test
    void shouldCallToEnableLTBDEForPricingWhenAccomClassMinPriceDiffSeasonEndDateIsChangedFromWithinToExtendedOptimizationWindow() {
        java.time.LocalDate caughtUpDate = java.time.LocalDate.parse("2023-08-01");

        AccomClassMinPriceDiffSeason existingSeason = new AccomClassMinPriceDiffSeason();
        existingSeason.setId(1);
        existingSeason.setFridayDiffWithTax(BigDecimal.ONE);
        existingSeason.setStartDate(JavaLocalDateUtils.toJodaLocalDate(java.time.LocalDate.parse("2023-12-01")));
        existingSeason.setEndDate(JavaLocalDateUtils.toJodaLocalDate(java.time.LocalDate.parse("2024-01-01")));

        AccomClassMinPriceDiffSeason updatedSeason = new AccomClassMinPriceDiffSeason();
        updatedSeason.setId(1);
        updatedSeason.setFridayDiffWithTax(BigDecimal.ONE);
        updatedSeason.setStartDate(JavaLocalDateUtils.toJodaLocalDate(java.time.LocalDate.parse("2023-12-01")));
        updatedSeason.setEndDate(JavaLocalDateUtils.toJodaLocalDate(java.time.LocalDate.parse("2024-09-01")));

        List<AccomClassMinPriceDiffSeason> accomClassMinPriceDiffSeasons = List.of(updatedSeason);

        when(pricingConfigurationLTBDEService.isLTBDEOnIndirectConfigChangedEnabled()).thenReturn(true);
        when(tenantCrudService.findAll(AccomClassMinPriceDiffSeason.class)).thenReturn(List.of(existingSeason));
        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(caughtUpDate);
        when(pricingConfigurationLTBDEService.isDateBeyondOptimizationWindow(JavaLocalDateUtils.toJavaLocalDate(existingSeason.getEndDate()), caughtUpDate)).thenReturn(false);
        when(pricingConfigurationLTBDEService.isDateBeyondOptimizationWindow(JavaLocalDateUtils.toJavaLocalDate(updatedSeason.getEndDate()), caughtUpDate)).thenReturn(true);

        service.saveAccomClassMinPriceDiffSeasons(accomClassMinPriceDiffSeasons);

        verify(tenantCrudService).save(updatedSeason);
        verify(tenantCrudService, times(0)).delete(anyObject());
        verify(pricingConfigurationLTBDEService).enableLTBDEForPricing(true);
    }

    @Test
    void shouldCallToEnableLTBDEForPricingWhenAccomClassMinPriceDiffSeasonEndDateIsChangedFromExtendedOptimizationWindowToWithinOptimizationWindow() {
        java.time.LocalDate caughtUpDate = java.time.LocalDate.parse("2023-08-01");

        AccomClassMinPriceDiffSeason existingSeason = new AccomClassMinPriceDiffSeason();
        existingSeason.setId(1);
        existingSeason.setFridayDiffWithTax(BigDecimal.ONE);
        existingSeason.setStartDate(JavaLocalDateUtils.toJodaLocalDate(java.time.LocalDate.parse("2023-12-01")));
        existingSeason.setEndDate(JavaLocalDateUtils.toJodaLocalDate(java.time.LocalDate.parse("2024-09-01")));

        AccomClassMinPriceDiffSeason updatedSeason = new AccomClassMinPriceDiffSeason();
        updatedSeason.setId(1);
        updatedSeason.setFridayDiffWithTax(BigDecimal.ONE);
        updatedSeason.setStartDate(JavaLocalDateUtils.toJodaLocalDate(java.time.LocalDate.parse("2023-12-01")));
        updatedSeason.setEndDate(JavaLocalDateUtils.toJodaLocalDate(java.time.LocalDate.parse("2024-01-01")));

        List<AccomClassMinPriceDiffSeason> accomClassMinPriceDiffSeasons = List.of(updatedSeason);

        when(pricingConfigurationLTBDEService.isLTBDEOnIndirectConfigChangedEnabled()).thenReturn(true);
        when(tenantCrudService.findAll(AccomClassMinPriceDiffSeason.class)).thenReturn(List.of(existingSeason));
        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(caughtUpDate);
        when(pricingConfigurationLTBDEService.isDateBeyondOptimizationWindow(JavaLocalDateUtils.toJavaLocalDate(existingSeason.getEndDate()), caughtUpDate)).thenReturn(false);
        when(pricingConfigurationLTBDEService.isDateBeyondOptimizationWindow(JavaLocalDateUtils.toJavaLocalDate(updatedSeason.getEndDate()), caughtUpDate)).thenReturn(true);

        service.saveAccomClassMinPriceDiffSeasons(accomClassMinPriceDiffSeasons);

        verify(tenantCrudService).save(updatedSeason);
        verify(tenantCrudService, times(0)).delete(anyObject());
        verify(pricingConfigurationLTBDEService).enableLTBDEForPricing(true);
    }

    @Test
    public void saveDefaultCpConfigAccomClassMinPriceDiffSeasons_existingSeasonsOverwritten() {
        AccomClassMinPriceDiffSeason season1 = new AccomClassMinPriceDiffSeason();
        AccomClassMinPriceDiffSeason season2 = new AccomClassMinPriceDiffSeason();


        AccomClassMinPriceDiffSeason existingSeason1 = new AccomClassMinPriceDiffSeason();
        existingSeason1.setId(1);
        AccomClassMinPriceDiffSeason existingSeason2 = new AccomClassMinPriceDiffSeason();
        existingSeason2.setId(2);
        List<AccomClassMinPriceDiffSeason> accomClassMinPriceDiffSeasons = Arrays.asList(existingSeason1, season1, season2);
        List<AccomClassMinPriceDiffSeason> existingSeasons = Arrays.asList(existingSeason1, existingSeason2);

        when(tenantCrudService.findAll(AccomClassMinPriceDiffSeason.class)).thenReturn(existingSeasons);

        service.saveAccomClassMinPriceDiffSeasons(accomClassMinPriceDiffSeasons);

        verify(tenantCrudService).save(existingSeason1);
        verify(tenantCrudService).save(season1);
        verify(tenantCrudService).save(season2);
        verify(tenantCrudService).delete(captor.capture());
        Collection value = captor.getValue();
        assertEquals(1, value.size());
        assertTrue(value.contains(existingSeason2));
    }

    @Test
    public void deletePriceDiffDataByPriceRank_isPersisted() {
        AccomClassPriceRank priceRank = new AccomClassPriceRank();
        priceRank.setId(1);

        service.deletePriceDiffDataByPriceRank(priceRank);

        verify(tenantCrudService).executeUpdateByNamedQuery(AccomClassMinPriceDiff.DELETE_BY_PRICE_RANK, QueryParameter.with("priceRank", priceRank).parameters());
        verify(tenantCrudService).executeUpdateByNamedQuery(AccomClassMinPriceDiffSeason.DELETE_BY_PRICE_RANK, QueryParameter.with("priceRank", priceRank).parameters());
    }

    @Test
    public void deletePriceDiffDataByPriceRank_notPersisted() {
        AccomClassPriceRank priceRank = new AccomClassPriceRank();

        service.deletePriceDiffDataByPriceRank(priceRank);

        verify(tenantCrudService, times(0)).executeUpdateByNamedQuery(AccomClassMinPriceDiff.DELETE_BY_PRICE_RANK, QueryParameter.with("priceRank", priceRank).parameters());
        verify(tenantCrudService, times(0)).executeUpdateByNamedQuery(AccomClassMinPriceDiffSeason.DELETE_BY_PRICE_RANK, QueryParameter.with("priceRank", priceRank).parameters());
    }

    @Test
    public void deletePriceDiffDataByPriceRanks_notPersisted() {
        AccomClassPriceRank priceRank1 = new AccomClassPriceRank();
        AccomClassPriceRank priceRank2 = new AccomClassPriceRank();
        List<AccomClassPriceRank> priceRanks = Arrays.asList(priceRank1, priceRank2);

        service.deletePriceDiffDataByPriceRanks(priceRanks);

        verify(tenantCrudService, times(0)).executeUpdateByNamedQuery(AccomClassMinPriceDiff.DELETE_BY_PRICE_RANKS, QueryParameter.with("priceRanks", priceRanks).parameters());
        verify(tenantCrudService, times(0)).executeUpdateByNamedQuery(AccomClassMinPriceDiffSeason.DELETE_BY_PRICE_RANKS, QueryParameter.with("priceRanks", priceRanks).parameters());
    }

    @Test
    public void deletePriceDiffDataByPriceRanks_partiallyPersisted() {
        AccomClassPriceRank priceRank1 = new AccomClassPriceRank();
        priceRank1.setId(1);
        AccomClassPriceRank priceRank2 = new AccomClassPriceRank();
        List<AccomClassPriceRank> priceRanks = Arrays.asList(priceRank1, priceRank2);

        service.deletePriceDiffDataByPriceRanks(priceRanks);

        verify(tenantCrudService).executeUpdateByNamedQuery(AccomClassMinPriceDiff.DELETE_BY_PRICE_RANK, QueryParameter.with("priceRank", priceRank1).parameters());
        verify(tenantCrudService).executeUpdateByNamedQuery(AccomClassMinPriceDiffSeason.DELETE_BY_PRICE_RANK, QueryParameter.with("priceRank", priceRank1).parameters());
    }

    @Test
    public void deletePriceDiffDataByPriceRanks_isPersisted() {
        AccomClassPriceRank priceRank1 = new AccomClassPriceRank();
        priceRank1.setId(1);
        AccomClassPriceRank priceRank2 = new AccomClassPriceRank();
        priceRank2.setId(2);
        List<AccomClassPriceRank> priceRanks = Arrays.asList(priceRank1, priceRank2);

        service.deletePriceDiffDataByPriceRanks(priceRanks);

        verify(tenantCrudService).executeUpdateByNamedQuery(AccomClassMinPriceDiff.DELETE_BY_PRICE_RANK, QueryParameter.with("priceRank", priceRank1).parameters());
        verify(tenantCrudService).executeUpdateByNamedQuery(AccomClassMinPriceDiffSeason.DELETE_BY_PRICE_RANK, QueryParameter.with("priceRank", priceRank1).parameters());
        verify(tenantCrudService).executeUpdateByNamedQuery(AccomClassMinPriceDiff.DELETE_BY_PRICE_RANK, QueryParameter.with("priceRank", priceRank2).parameters());
        verify(tenantCrudService).executeUpdateByNamedQuery(AccomClassMinPriceDiffSeason.DELETE_BY_PRICE_RANK, QueryParameter.with("priceRank", priceRank2).parameters());
    }

    @Test
    public void findCPConfiguration() {
        CPConfiguration cpConfiguration = new CPConfiguration();

        Mockito.when(tenantCrudService.findOne(CPConfiguration.class)).thenReturn(cpConfiguration);

        assertEquals(cpConfiguration, service.findCPConfiguration());

        Mockito.verify(tenantCrudService).findOne(CPConfiguration.class);
    }

    @Test
    public void isSupplementEnabled() {
        CPConfiguration cpConfiguration = new CPConfiguration();
        cpConfiguration.setEnableSupplements(true);
        when(tenantCrudService.findOne(CPConfiguration.class)).thenReturn(cpConfiguration);
        when(configParamsService.getBooleanParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED)).thenReturn(Boolean.TRUE);
        when(configParamsService.getBooleanParameterValue(GUIConfigParamName.IS_SUPPLEMENTAL_ENABLED)).thenReturn(Boolean.TRUE);

        boolean supplementEnabled = service.isSupplementEnabled();

        assertTrue(supplementEnabled);
        Mockito.verify(tenantCrudService).findOne(CPConfiguration.class);
    }

    @Test
    public void saveCPConfiguration() {
        CPConfiguration cpConfiguration = new CPConfiguration();

        Mockito.when(tenantCrudService.save(cpConfiguration)).thenReturn(cpConfiguration);

        service.save(cpConfiguration);

        Mockito.verify(tenantCrudService).save(cpConfiguration);
    }

    @Test
    public void getCeilingAndFloorConfigWithTax() {
        LocalDate startDate = new LocalDate();
        LocalDate endDate = new LocalDate();
        boolean useOverrides = true;
        service.getCeilingAndFloorConfig(startDate, endDate, OccupancyType.SINGLE, useOverrides);
        Mockito.verify(tenantCrudService).findByNativeQuery(CPConfigMergedCeilingAndFloor
                        .USP_FIND_CEILING_AND_FLOOR_FOR_PROPERTY_BY_START_DATE_AND_END_DATE_WITH_TAX,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId())
                        .and("startDate", startDate.toDate())
                        .and("endDate", endDate.toDate())
                        .and("occupancyType", OccupancyType.SINGLE.getId())
                        .and("useOverrides", useOverrides ? 1 : 0).parameters(), CPConfigMergedCeilingAndFloor.class);

    }

    @Test
    public void getCeilingAndFloorConfigWithTaxWithoutOptimizedStoredProcedure() {
        System.setProperty("pacman.use.optimized.stored.procedure.pricing", "false");
        LocalDate startDate = new LocalDate();
        LocalDate endDate = new LocalDate();
        boolean useOverrides = true;
        service.getCeilingAndFloorConfig(startDate, endDate, OccupancyType.SINGLE, useOverrides);
        Mockito.verify(tenantCrudService).findByNamedQuery(CPConfigMergedCeilingAndFloor
                        .FIND_CEILING_AND_FLOOR_FOR_PROPERTY_BY_START_DATE_AND_END_DATE_WITH_TAX,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId())
                        .and("startDate", startDate)
                        .and("endDate", endDate)
                        .and("occupancyType", OccupancyType.SINGLE.getId())
                        .and("useOverrides", useOverrides ? 1 : 0).parameters());

    }

    @Test
    public void getCeilingAndFloorConfigWithTaxWithSP() {
        LocalDate startDate = new LocalDate();
        LocalDate endDate = new LocalDate();
        boolean useOverrides = true;
        System.setProperty("pacman.use.optimized.stored.procedure.pricing", "true");
        service.getCeilingAndFloorConfig(startDate, endDate, OccupancyType.SINGLE, useOverrides);
        Mockito.verify(tenantCrudService).findByNativeQuery(CPConfigMergedCeilingAndFloor
                        .USP_FIND_CEILING_AND_FLOOR_FOR_PROPERTY_BY_START_DATE_AND_END_DATE_WITH_TAX,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId())
                        .and("startDate", startDate.toDate())
                        .and("endDate", endDate.toDate())
                        .and("occupancyType", OccupancyType.SINGLE.getId())
                        .and("useOverrides", useOverrides ? 1 : 0).parameters(), CPConfigMergedCeilingAndFloor.class);

    }


    @Test
    public void findMergedOffset_withTax() {
        LocalDate arrivalDate = new LocalDate();
        PricingConfigurationService spy = Mockito.spy(service);
        when(configParamsService.getParameterValue(FeatureTogglesConfigParamName.PER_PERSON_PRICING_ENABLED)).thenReturn(false);
        spy.findMergedOffset(1, arrivalDate, Integer.valueOf(1));
        Mockito.verify(spy).findOffsetsForDates(arrivalDate, arrivalDate);
    }

    @Test
    public void findOffsetsForDates_withTax() {
        LocalDate localDate = new LocalDate();
        CPConfigMergedOffset cPConfigMergedOffsetmock = Mockito.mock(CPConfigMergedOffset.class);
        CPConfigMergedOffsetPK cpConfigMergedOffsetPK = Mockito.mock(CPConfigMergedOffsetPK.class);
        Mockito.when(cPConfigMergedOffsetmock.getId()).thenReturn(cpConfigMergedOffsetPK);
        Mockito.when(tenantCrudService.findByNamedQuery(CPConfigMergedOffset
                                .FIND_OFFSETS_WITH_TAX_FOR_PROPERTY_BY_START_DATE_AND_END_DATE,
                        QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId())
                                .and("startDate", localDate)
                                .and("endDate", localDate).parameters()))
                .thenReturn(Arrays.asList(cPConfigMergedOffsetmock));
        Map<CPConfigMergedOffsetPK, CPConfigMergedOffset> offsetsForDates = service.findOffsetsForDates(localDate, localDate);
        assertEquals(cPConfigMergedOffsetmock, offsetsForDates.get(cpConfigMergedOffsetPK));
    }

    @Test
    public void findOffsetsForDatesAndBaseOccupancyType_WithTax() {
        when(configParamsService.getParameterValue(FeatureTogglesConfigParamName.PER_PERSON_PRICING_ENABLED)).thenReturn(false);

        LocalDate startDate = new LocalDate();
        LocalDate endDate = startDate.plusDays(2);

        CPConfigMergedOffset cPConfigMergedOffsetmock = Mockito.mock(CPConfigMergedOffset.class);
        CPConfigMergedOffsetPK cpConfigMergedOffsetPK = Mockito.mock(CPConfigMergedOffsetPK.class);
        cpConfigMergedOffsetPK.setOccupancyType(OccupancyType.SINGLE);
        Mockito.when(cPConfigMergedOffsetmock.getId()).thenReturn(cpConfigMergedOffsetPK);
        Mockito.when(tenantCrudService.findByNamedQuery(CPConfigMergedOffset
                                .FIND_OFFSETS_WITH_TAX_FOR_PROPERTY_BY_START_DATE_AND_END_DATE_AND_OCCUPANCY_TYPE,
                        QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId())
                                .and("startDate", startDate)
                                .and("endDate", endDate)
                                .and("occupancyType", OccupancyType.SINGLE.getId()).parameters()))
                .thenReturn(Arrays.asList(cPConfigMergedOffsetmock));
        Map<CPConfigMergedOffsetPK, CPConfigMergedOffset> offsetsForDates = service.findOffsetsForDatesAndBaseOccupancyType(startDate, endDate);
        assertEquals(cPConfigMergedOffsetmock, offsetsForDates.get(cpConfigMergedOffsetPK));
    }

    @Test
    public void deleteTransientSeasons() {
        TransientPricingBaseAccomType transientPricingBaseAccomType = new TransientPricingBaseAccomType();
        List<TransientPricingBaseAccomType> transientPricingBaseAccomTypesSeasons = new ArrayList<>();
        transientPricingBaseAccomTypesSeasons.add(transientPricingBaseAccomType);

        when(tenantCrudService.findByNamedQuery(TransientPricingBaseAccomType.FIND_SEASONS_BY_PROPERTY_ID,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters())).thenReturn(null);
        service.deleteTransientSeasons();
        verify(tenantCrudService, times(0)).delete(Arrays.asList(transientPricingBaseAccomTypesSeasons));

        when(tenantCrudService.findByNamedQuery(TransientPricingBaseAccomType.FIND_SEASONS_BY_PROPERTY_ID,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters())).thenReturn(Arrays.asList(transientPricingBaseAccomTypesSeasons));
        service.deleteTransientSeasons();
        verify(tenantCrudService, times(1)).delete(Arrays.asList(transientPricingBaseAccomTypesSeasons));
    }

    @Test
    public void deleteGroupSeasons() {
        GroupPricingBaseAccomType groupPricingBaseAccomType = new GroupPricingBaseAccomType();
        List<GroupPricingBaseAccomType> groupPricingBaseAccomTypesSeasons = new ArrayList<>();
        groupPricingBaseAccomTypesSeasons.add(groupPricingBaseAccomType);

        when(tenantCrudService.findByNamedQuery(GroupPricingBaseAccomType.FIND_SEASONS_BY_PROPERTY_ID,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters())).thenReturn(null);
        service.deleteGroupSeasons();
        verify(tenantCrudService, times(0)).delete(Arrays.asList(groupPricingBaseAccomTypesSeasons));

        when(tenantCrudService.findByNamedQuery(GroupPricingBaseAccomType.FIND_SEASONS_BY_PROPERTY_ID,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters())).thenReturn(Arrays.asList(groupPricingBaseAccomTypesSeasons));
        service.deleteGroupSeasons();
        verify(tenantCrudService, times(1)).delete(Arrays.asList(groupPricingBaseAccomTypesSeasons));
    }

    @Test
    public void deleteOffsetSeasons() {
        CPConfigOffsetAccomType cpConfigOffsetAccomType = new CPConfigOffsetAccomType();
        List<CPConfigOffsetAccomType> cpConfigOffsetAccomTypesSeasons = new ArrayList<>();
        cpConfigOffsetAccomTypesSeasons.add(cpConfigOffsetAccomType);

        when(tenantCrudService.findByNamedQuery(CPConfigOffsetAccomType.FIND_SEASONS_BY_PROPERTY_ID,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters())).thenReturn(null);
        service.deleteOffsetSeasons();
        verify(tenantCrudService, times(0)).delete(Arrays.asList(cpConfigOffsetAccomTypesSeasons));

        when(tenantCrudService.findByNamedQuery(CPConfigOffsetAccomType.FIND_SEASONS_BY_PROPERTY_ID,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters())).thenReturn(Arrays.asList(cpConfigOffsetAccomTypesSeasons));
        service.deleteOffsetSeasons();
        verify(tenantCrudService, times(1)).delete(Arrays.asList(cpConfigOffsetAccomTypesSeasons));
    }

    @Test
    public void deleteSupplementsSeasons() {
        AccomTypeSupplement accomTypeSupplement = new AccomTypeSupplement();
        List<AccomTypeSupplement> accomTypeSupplementSeasons = new ArrayList<>();
        accomTypeSupplementSeasons.add(accomTypeSupplement);

        when(tenantCrudService.findByNamedQuery(AccomTypeSupplement.FIND_SEASON_SUPPLEMENTS)).thenReturn(null);
        service.deleteSupplementsSeasons();
        verify(tenantCrudService, times(0)).delete(Arrays.asList(accomTypeSupplementSeasons));

        when(tenantCrudService.findByNamedQuery(AccomTypeSupplement.FIND_SEASON_SUPPLEMENTS)).thenReturn(Arrays.asList(accomTypeSupplementSeasons));
        service.deleteSupplementsSeasons();
        verify(tenantCrudService, times(1)).delete(Arrays.asList(accomTypeSupplementSeasons));
    }

    @Test
    public void deletePricingConfigurationsForRoomType_BaseRoomType() {
        int accomTypeId = 1;
        AccomType accomTypeRemapped = new AccomType();
        accomTypeRemapped.setId(accomTypeId);
        AccomClass accomClass = new AccomClass();
        accomClass.setStatusId(Constants.INACTIVE_STATUS_ID - 1);
        accomTypeRemapped.setAccomClass(accomClass);

        AccomType existingAccomType = new AccomType();
        existingAccomType.setId(accomTypeId);
        AccomClass accomClassExisting = new AccomClass();
        int accomClassId = 2;
        accomClassExisting.setId(accomClassId);
        existingAccomType.setAccomClass(accomClassExisting);

        PricingAccomClass pricingAccomClass = new PricingAccomClass();
        pricingAccomClass.setAccomType(existingAccomType);

        when(tenantCrudService.find(AccomType.class, accomTypeId)).thenReturn(existingAccomType);
        when(tenantCrudService.findByNamedQuerySingleResult(
                PricingAccomClass.FIND_BY_PROPERTY_AND_ACCOM_CLASS,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId())
                        .and("accomClassId", existingAccomType.getAccomClass().getId()).parameters())).thenReturn(pricingAccomClass);

        Set<AccomType> remappedAccomTypes = new HashSet<>();
        remappedAccomTypes.add(accomTypeRemapped);
        service.deletePricingConfigurationsForRoomType(remappedAccomTypes);

        verify(tenantCrudService, times(1)).delete(PricingAccomClass.class, pricingAccomClass.getId());
        verify(tenantCrudService, times(1)).executeUpdateByNamedQuery(TransientPricingBaseAccomType.DELETE_BY_ACCOM_TYPE,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).and("accomType", existingAccomType).parameters());
        verify(tenantCrudService, times(1)).executeUpdateByNamedQuery(GroupPricingBaseAccomType.DELETE_BY_ACCOM_TYPE,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).and("accomType", existingAccomType).parameters());
        verify(tenantCrudService, times(1)).executeUpdateByNamedQuery(CPConfigOffsetAccomType.DELETE_BY_ACCOM_TYPES,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).and("accomTypes", remappedAccomTypes).parameters());
        verify(tenantCrudService, times(1)).executeUpdateByNamedQuery(CPConfigOffsetAccomType.DELETE_SEASONS_DATE_FOR_ACCOM_TYPE,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).and("accomTypes", remappedAccomTypes).parameters());
        verify(tenantCrudService, times(1)).executeUpdateByNamedQuery(AccomTypeSupplement.DELETE_SUPPLEMENT_BY_ACCOM_TYPE,
                QueryParameter.with("accomTypes", remappedAccomTypes).parameters());
    }

    @Test
    public void deletePricingConfigurationsForRoomType_AccomClassDeleted() {
        int accomTypeId = 1;
        AccomType accomTypeRemapped = new AccomType();
        accomTypeRemapped.setId(accomTypeId);
        AccomClass accomClass = new AccomClass();
        accomClass.setStatusId(Constants.INACTIVE_STATUS_ID);
        accomTypeRemapped.setAccomClass(accomClass);

        AccomType existingAccomType = new AccomType();
        existingAccomType.setId(accomTypeId);
        AccomClass accomClassExisting = new AccomClass();
        int accomClassId = 2;
        accomClassExisting.setId(accomClassId);
        existingAccomType.setAccomClass(accomClassExisting);

        when(tenantCrudService.find(AccomType.class, accomTypeId)).thenReturn(existingAccomType);
        when(tenantCrudService.findByNamedQuerySingleResult(
                PricingAccomClass.FIND_BY_PROPERTY_AND_ACCOM_CLASS,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId())
                        .and("accomClassId", existingAccomType.getAccomClass().getId()).parameters())).thenReturn(null);

        Set<AccomType> remappedAccomTypes = new HashSet<>();
        remappedAccomTypes.add(accomTypeRemapped);
        service.deletePricingConfigurationsForRoomType(remappedAccomTypes);

        verify(tenantCrudService, times(0)).delete(eq(PricingAccomClass.class), anyInt());
        verify(tenantCrudService, times(1)).executeUpdateByNamedQuery(TransientPricingBaseAccomType.DELETE_BY_ACCOM_TYPE,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).and("accomType", existingAccomType).parameters());
        verify(tenantCrudService, times(1)).executeUpdateByNamedQuery(GroupPricingBaseAccomType.DELETE_BY_ACCOM_TYPE,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).and("accomType", existingAccomType).parameters());
        verify(tenantCrudService, times(1)).executeUpdateByNamedQuery(CPConfigOffsetAccomType.DELETE_BY_ACCOM_TYPES,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).and("accomTypes", remappedAccomTypes).parameters());
        verify(tenantCrudService, times(1)).executeUpdateByNamedQuery(CPConfigOffsetAccomType.DELETE_SEASONS_DATE_FOR_ACCOM_TYPE,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).and("accomTypes", remappedAccomTypes).parameters());
        verify(tenantCrudService, times(1)).executeUpdateByNamedQuery(AccomTypeSupplement.DELETE_SUPPLEMENT_BY_ACCOM_TYPE,
                QueryParameter.with("accomTypes", remappedAccomTypes).parameters());
    }

    @Test
    public void deletePricingConfigurationsForRoomType_NonBaseRoomType() {
        int accomTypeId = 1;
        AccomType accomTypeRemapped = new AccomType();
        accomTypeRemapped.setId(accomTypeId);
        AccomClass accomClass = new AccomClass();
        accomClass.setStatusId(Constants.INACTIVE_STATUS_ID - 1);
        accomTypeRemapped.setAccomClass(accomClass);

        AccomType existingAccomType = new AccomType();
        existingAccomType.setId(accomTypeId);
        AccomClass accomClassExisting = new AccomClass();
        int accomClassId = 2;
        accomClassExisting.setId(accomClassId);
        existingAccomType.setAccomClass(accomClassExisting);

        PricingAccomClass pricingAccomClass = new PricingAccomClass();
        AccomType accomTypeForPricingAccomClass = new AccomType();
        accomTypeForPricingAccomClass.setId(accomTypeId + 1);
        pricingAccomClass.setAccomType(accomTypeForPricingAccomClass);

        when(tenantCrudService.find(AccomType.class, accomTypeId)).thenReturn(existingAccomType);
        when(tenantCrudService.findByNamedQuerySingleResult(
                PricingAccomClass.FIND_BY_PROPERTY_AND_ACCOM_CLASS,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId())
                        .and("accomClassId", existingAccomType.getAccomClass().getId()).parameters())).thenReturn(pricingAccomClass);

        Set<AccomType> remappedAccomTypes = new HashSet<>();
        remappedAccomTypes.add(accomTypeRemapped);
        service.deletePricingConfigurationsForRoomType(remappedAccomTypes);

        verify(tenantCrudService, times(0)).delete(PricingAccomClass.class, pricingAccomClass.getId());
        verify(tenantCrudService, times(0)).executeUpdateByNamedQuery(TransientPricingBaseAccomType.DELETE_BY_ACCOM_TYPE,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).and("accomType", existingAccomType).parameters());
        verify(tenantCrudService, times(0)).executeUpdateByNamedQuery(GroupPricingBaseAccomType.DELETE_BY_ACCOM_TYPE,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).and("accomType", existingAccomType).parameters());
        verify(tenantCrudService, times(1)).executeUpdateByNamedQuery(CPConfigOffsetAccomType.DELETE_BY_ACCOM_TYPES,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).and("accomTypes", remappedAccomTypes).parameters());
        verify(tenantCrudService, times(1)).executeUpdateByNamedQuery(CPConfigOffsetAccomType.DELETE_SEASONS_DATE_FOR_ACCOM_TYPE,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).and("accomTypes", remappedAccomTypes).parameters());
        verify(tenantCrudService, times(1)).executeUpdateByNamedQuery(AccomTypeSupplement.DELETE_SUPPLEMENT_BY_ACCOM_TYPE,
                QueryParameter.with("accomTypes", remappedAccomTypes).parameters());
    }

    @Test
    public void isCeilingFloorGroupComplete() {
        Integer propertyId = 5;
        PricingAccomClass pricingAccomClass = PricingConfigurationObjectMother.buildPricingAccomClass();
        PricingBaseAccomType groupPricingBaseAccomType = PricingConfigurationObjectMother.buildGroupPricingBaseAccomType();

        when(multiPropertyCrudService.findByNamedQueryForSingleProperty(
                propertyId, PricingAccomClass.FIND_BY_PROPERTY_ID,
                QueryParameter.with("propertyId", propertyId).parameters())).thenReturn(Collections.singletonList(pricingAccomClass));
        when(multiPropertyCrudService.findByNamedQueryForSingleProperty(
                propertyId, GroupPricingBaseAccomType.FIND_BASE_AT_FOR_PRICEABLE_AC,
                QueryParameter.with("propertyId", propertyId).parameters())).thenReturn(Collections.singletonList(groupPricingBaseAccomType));

        assertTrue(service.isCeilingFloorGroupComplete(propertyId));

        verify(multiPropertyCrudService).findByNamedQueryForSingleProperty(propertyId, PricingAccomClass.FIND_BY_PROPERTY_ID,
                QueryParameter.with("propertyId", propertyId).parameters());
        verify(multiPropertyCrudService).findByNamedQueryForSingleProperty(propertyId, GroupPricingBaseAccomType.FIND_BASE_AT_FOR_PRICEABLE_AC,
                QueryParameter.with("propertyId", propertyId).parameters());
    }

    @Test
    public void deletePricingConfigurationsByRoomTypes_BaseRoomType() {
        int accomTypeId = 1;
        AccomType accomTypeRemapped = new AccomType();
        accomTypeRemapped.setId(accomTypeId);
        AccomClass accomClass = new AccomClass();
        accomClass.setStatusId(Constants.ACTIVE_STATUS_ID);
        accomTypeRemapped.setAccomClass(accomClass);

        PricingAccomClass pricingAccomClass = new PricingAccomClass();
        pricingAccomClass.setAccomType(accomTypeRemapped);

        when(tenantCrudService.findByNamedQuerySingleResult(
                PricingAccomClass.FIND_BY_PROPERTY_AND_ACCOM_TYPE,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId())
                        .and("accomTypeId", accomTypeRemapped.getId()).parameters())).thenReturn(pricingAccomClass);

        Set<AccomType> remappedAccomTypes = new HashSet<>();
        remappedAccomTypes.add(accomTypeRemapped);
        service.deletePricingConfigurationsByRoomTypes(remappedAccomTypes);

        verify(tenantCrudService, times(1)).delete(PricingAccomClass.class, pricingAccomClass.getId());
        verify(tenantCrudService, times(1)).executeUpdateByNamedQuery(TransientPricingBaseAccomType.DELETE_BY_ACCOM_TYPE,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).and("accomType", accomTypeRemapped).parameters());
        verify(tenantCrudService, times(1)).executeUpdateByNamedQuery(GroupPricingBaseAccomType.DELETE_BY_ACCOM_TYPE,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).and("accomType", accomTypeRemapped).parameters());
        verify(tenantCrudService, times(1)).executeUpdateByNamedQuery(CPConfigOffsetAccomType.DELETE_BY_ACCOM_TYPES,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).and("accomTypes", remappedAccomTypes).parameters());
        verify(tenantCrudService, times(1)).executeUpdateByNamedQuery(CPConfigOffsetAccomType.DELETE_SEASONS_DATE_FOR_ACCOM_TYPE,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).and("accomTypes", remappedAccomTypes).parameters());
        verify(tenantCrudService, times(1)).executeUpdateByNamedQuery(AccomTypeSupplement.DELETE_SUPPLEMENT_BY_ACCOM_TYPE,
                QueryParameter.with("accomTypes", remappedAccomTypes).parameters());
    }

    @Test
    public void deletePricingConfigurationsByRoomTypes_NonBaseRoomType() {
        int accomTypeId = 1;
        AccomType accomTypeRemapped = new AccomType();
        accomTypeRemapped.setId(accomTypeId);
        AccomClass accomClass = new AccomClass();
        accomClass.setStatusId(Constants.ACTIVE_STATUS_ID);
        accomTypeRemapped.setAccomClass(accomClass);

        when(tenantCrudService.findByNamedQuerySingleResult(
                PricingAccomClass.FIND_BY_PROPERTY_AND_ACCOM_TYPE,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId())
                        .and("accomTypeId", accomTypeRemapped.getId()).parameters())).thenReturn(null);

        Set<AccomType> remappedAccomTypes = new HashSet<>();
        remappedAccomTypes.add(accomTypeRemapped);
        service.deletePricingConfigurationsByRoomTypes(remappedAccomTypes);

        verify(tenantCrudService, times(0)).delete(PricingAccomClass.class, eq(anyInt()));
        verify(tenantCrudService, times(0)).executeUpdateByNamedQuery(TransientPricingBaseAccomType.DELETE_BY_ACCOM_TYPE,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).and("accomType", accomTypeRemapped).parameters());
        verify(tenantCrudService, times(0)).executeUpdateByNamedQuery(GroupPricingBaseAccomType.DELETE_BY_ACCOM_TYPE,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).and("accomType", accomTypeRemapped).parameters());
        verify(tenantCrudService, times(1)).executeUpdateByNamedQuery(CPConfigOffsetAccomType.DELETE_BY_ACCOM_TYPES,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).and("accomTypes", remappedAccomTypes).parameters());
        verify(tenantCrudService, times(1)).executeUpdateByNamedQuery(CPConfigOffsetAccomType.DELETE_SEASONS_DATE_FOR_ACCOM_TYPE,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).and("accomTypes", remappedAccomTypes).parameters());
        verify(tenantCrudService, times(1)).executeUpdateByNamedQuery(AccomTypeSupplement.DELETE_SUPPLEMENT_BY_ACCOM_TYPE,
                QueryParameter.with("accomTypes", remappedAccomTypes).parameters());
    }

    @Test
    public void isCeilingFloorGroupComplete_false() {
        Integer propertyId = 5;
        PricingAccomClass pricingAccomClass1 = PricingConfigurationObjectMother.buildPricingAccomClass();
        PricingAccomClass pricingAccomClass2 = PricingConfigurationObjectMother.buildPricingAccomClass();
        PricingBaseAccomType groupPricingBaseAccomType = PricingConfigurationObjectMother.buildGroupPricingBaseAccomType();
        groupPricingBaseAccomType.setWednesdayCeilingRate(null);

        when(multiPropertyCrudService.findByNamedQueryForSingleProperty(propertyId, PricingAccomClass.FIND_BY_PROPERTY_ID,
                QueryParameter.with("propertyId", propertyId).parameters())).thenReturn(Arrays.asList(pricingAccomClass1, pricingAccomClass2));
        when(multiPropertyCrudService.findByNamedQueryForSingleProperty(propertyId, GroupPricingBaseAccomType.FIND_BASE_AT_FOR_PRICEABLE_AC,
                QueryParameter.with("propertyId", propertyId).parameters())).thenReturn(Collections.singletonList(groupPricingBaseAccomType));

        assertFalse(service.isCeilingFloorGroupComplete(propertyId));

        verify(multiPropertyCrudService).findByNamedQueryForSingleProperty(propertyId, PricingAccomClass.FIND_BY_PROPERTY_ID,
                QueryParameter.with("propertyId", propertyId).parameters());
        verify(multiPropertyCrudService).findByNamedQueryForSingleProperty(propertyId, GroupPricingBaseAccomType.FIND_BASE_AT_FOR_PRICEABLE_AC,
                QueryParameter.with("propertyId", propertyId).parameters());
    }

    @Test
    public void isFixedPriceRoomTypeConfigurationComplete() {
        Integer propertyId = PacmanWorkContextHelper.getPropertyId();

        when(multiPropertyCrudService.findByNamedQuerySingleResultForSingleProperty(propertyId, CPConfigMergedOffset.FIND_COUNT_OF_UNCONFIGURED_FIXED_PRICE_OFFSETS, QueryParameter.with("occupancyTypeID", 1).and("productId", 1).parameters())).thenReturn(1);
        when(configParamsService.getParameterValue(FeatureTogglesConfigParamName.PER_PERSON_PRICING_ENABLED)).thenReturn(false);

        assertFalse(service.isFixedPriceRoomTypeConfigurationComplete(propertyId, 1, CPConfigMergedOffset.FIND_COUNT_OF_UNCONFIGURED_BASE_AT_FIXED_PRICE_OFFSETS, CPConfigMergedOffset.FIND_COUNT_OF_UNCONFIGURED_FIXED_PRICE_OFFSETS));
        verify(multiPropertyCrudService).findByNamedQuerySingleResultForSingleProperty(propertyId, CPConfigMergedOffset.FIND_COUNT_OF_UNCONFIGURED_FIXED_PRICE_OFFSETS, QueryParameter.with("occupancyTypeID", 1).and("productId", 1).parameters());
    }

    @Test
    public void isFixedPriceRoomTypeConfigurationCompleteForBAR_At_AccomTypeLevel() {
        Integer propertyId = PacmanWorkContextHelper.getPropertyId();

        when(multiPropertyCrudService.findByNamedQuerySingleResultForSingleProperty(propertyId, CPConfigMergedOffset.FIND_COUNT_OF_UNCONFIGURED_BASE_AT_FIXED_PRICE_OFFSETS_BAR, QueryParameter.with("occupancyTypeID", 1).and("productId", 1).parameters())).thenReturn(1);
        when(configParamsService.getParameterValue(FeatureTogglesConfigParamName.PER_PERSON_PRICING_ENABLED)).thenReturn(false);
        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.CPBASE_ROOM_TYPE_ONLY_ENABLED.value())).thenReturn(true);

        assertFalse(service.isFixedPriceRoomTypeConfigurationComplete(propertyId, 1, CPConfigMergedOffset.FIND_COUNT_OF_UNCONFIGURED_BASE_AT_FIXED_PRICE_OFFSETS_BAR, CPConfigMergedOffset.FIND_COUNT_OF_UNCONFIGURED_FIXED_PRICE_OFFSETS_BAR));
        verify(multiPropertyCrudService).findByNamedQuerySingleResultForSingleProperty(propertyId, CPConfigMergedOffset.FIND_COUNT_OF_UNCONFIGURED_BASE_AT_FIXED_PRICE_OFFSETS_BAR, QueryParameter.with("occupancyTypeID", 1).and("productId", 1).parameters());
    }

    @Test
    public void isFixedPriceRoomTypeConfigurationCompleteForBAR_AccomClassLevel() {
        Integer propertyId = PacmanWorkContextHelper.getPropertyId();

        when(multiPropertyCrudService.findByNamedQuerySingleResultForSingleProperty(propertyId, CPConfigMergedOffset.FIND_COUNT_OF_UNCONFIGURED_FIXED_PRICE_OFFSETS_BAR, QueryParameter.with("occupancyTypeID", 1).and("productId", 1).parameters())).thenReturn(1);
        when(configParamsService.getParameterValue(FeatureTogglesConfigParamName.PER_PERSON_PRICING_ENABLED)).thenReturn(false);
        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.CPBASE_ROOM_TYPE_ONLY_ENABLED.value())).thenReturn(false);

        assertFalse(service.isFixedPriceRoomTypeConfigurationComplete(propertyId, 1, CPConfigMergedOffset.FIND_COUNT_OF_UNCONFIGURED_BASE_AT_FIXED_PRICE_OFFSETS_BAR, CPConfigMergedOffset.FIND_COUNT_OF_UNCONFIGURED_FIXED_PRICE_OFFSETS_BAR));
        verify(multiPropertyCrudService).findByNamedQuerySingleResultForSingleProperty(propertyId, CPConfigMergedOffset.FIND_COUNT_OF_UNCONFIGURED_FIXED_PRICE_OFFSETS_BAR, QueryParameter.with("occupancyTypeID", 1).and("productId", 1).parameters());
    }

    @Test
    public void testGetContinuesPricingApportionedAdr() {
        service.setTenantCrudService(tenantCrudService());
        Map<Integer, BigDecimal> continuesPricingApportionedAdr = service.getContinuesPricingApportionedAdr();
        assertNotNull(continuesPricingApportionedAdr);
        assertTrue(BigDecimal.valueOf(100.000000).compareTo(continuesPricingApportionedAdr.get(2)) == 0);
        assertTrue(BigDecimal.valueOf(125.000000).compareTo(continuesPricingApportionedAdr.get(3)) == 0);
        assertTrue(BigDecimal.valueOf(175.000000).compareTo(continuesPricingApportionedAdr.get(4)) == 0);
    }

    @Test
    public void shouldGetContinuousPricingApportionedAdrForSelectedAccomTypes() {
        service.setTenantCrudService(tenantCrudService());
        AccomClass accomClass1 = UniqueAccomClassCreator.createUniqueAccomClassForPropertyID(PacmanWorkContextHelper.getPropertyId(), 0);
        AccomClass accomClass2 = UniqueAccomClassCreator.createUniqueAccomClassForPropertyID(PacmanWorkContextHelper.getPropertyId(), 0);

        AccomType accomType11 = UniqueAccomTypeCreator.createUniqueAccomTypeForAccomClass(PacmanWorkContextHelper.getPropertyId(), accomClass1, "AT11", "AT11", 100, "N");
        AccomType accomType12 = UniqueAccomTypeCreator.createUniqueAccomTypeForAccomClass(PacmanWorkContextHelper.getPropertyId(), accomClass1, "AT12", "AT12", 50, "N");
        AccomType accomType21 = UniqueAccomTypeCreator.createUniqueAccomTypeForAccomClass(PacmanWorkContextHelper.getPropertyId(), accomClass2, "AT21", "AT21", 70, "N");

        insertCpCfgAcTestData(accomClass1, accomClass2, accomType11, accomType21);
        insertCpCfgBaseAtTestData(accomType11, accomType12, accomType21);

        Map<Integer, BigDecimal> continuesPricingApportionedAdr = service.getContinuesPricingApportionedAdrForAccomTypes(Arrays.asList(accomType11.getId(), accomType12.getId(), accomType21.getId()));
        assertEquals(2, continuesPricingApportionedAdr.size());
        assertEquals(new BigDecimal("80.00"), continuesPricingApportionedAdr.get(accomClass1.getId()).setScale(2, BigDecimal.ROUND_UP));
        assertEquals(new BigDecimal("160.00"), continuesPricingApportionedAdr.get(accomClass2.getId()).setScale(2, BigDecimal.ROUND_UP));
    }

    @Test
    public void shouldGetContinuousPricingApportionedAdrForSingleAccomType() {
        service.setTenantCrudService(tenantCrudService());
        AccomClass accomClass1 = UniqueAccomClassCreator.createUniqueAccomClassForPropertyID(PacmanWorkContextHelper.getPropertyId(), 0);
        AccomClass accomClass2 = UniqueAccomClassCreator.createUniqueAccomClassForPropertyID(PacmanWorkContextHelper.getPropertyId(), 0);

        AccomType accomType11 = UniqueAccomTypeCreator.createUniqueAccomTypeForAccomClass(PacmanWorkContextHelper.getPropertyId(), accomClass1, "AT11", "AT11", 100, "N");
        AccomType accomType12 = UniqueAccomTypeCreator.createUniqueAccomTypeForAccomClass(PacmanWorkContextHelper.getPropertyId(), accomClass1, "AT12", "AT12", 50, "N");
        AccomType accomType21 = UniqueAccomTypeCreator.createUniqueAccomTypeForAccomClass(PacmanWorkContextHelper.getPropertyId(), accomClass2, "AT21", "AT21", 70, "N");
        insertCpCfgAcTestData(accomClass1, accomClass2, accomType11, accomType21);

        insertCpCfgBaseAtTestData(accomType11, accomType12, accomType21);

        Map<Integer, BigDecimal> continuesPricingApportionedAdr = service.getContinuesPricingApportionedAdrForAccomTypes(Arrays.asList(accomType12.getId()));
        assertEquals(1, continuesPricingApportionedAdr.size());
        assertEquals(new BigDecimal("80.00"), continuesPricingApportionedAdr.get(accomClass1.getId()).setScale(2, BigDecimal.ROUND_UP));
    }


    @Test
    public void shouldGetContinuousPricingApportionedAdrForOnlyASubsetOfAccomTypes() {
        service.setTenantCrudService(tenantCrudService());
        AccomClass accomClass1 = UniqueAccomClassCreator.createUniqueAccomClassForPropertyID(PacmanWorkContextHelper.getPropertyId(), 0);
        AccomClass accomClass2 = UniqueAccomClassCreator.createUniqueAccomClassForPropertyID(PacmanWorkContextHelper.getPropertyId(), 0);

        AccomType accomType11 = UniqueAccomTypeCreator.createUniqueAccomTypeForAccomClass(PacmanWorkContextHelper.getPropertyId(), accomClass1, "AT11", "AT11", 100, "N");
        AccomType accomType12 = UniqueAccomTypeCreator.createUniqueAccomTypeForAccomClass(PacmanWorkContextHelper.getPropertyId(), accomClass1, "AT12", "AT12", 50, "N");
        AccomType accomType21 = UniqueAccomTypeCreator.createUniqueAccomTypeForAccomClass(PacmanWorkContextHelper.getPropertyId(), accomClass2, "AT21", "AT21", 70, "N");

        insertCpCfgAcTestData(accomClass1, accomClass2, accomType11, accomType21);


        insertCpCfgBaseAtTestData(accomType11, accomType12, accomType21);

        Map<Integer, BigDecimal> continuesPricingApportionedAdr = service.getContinuesPricingApportionedAdrForAccomTypes(Arrays.asList(accomType11.getId(), accomType21.getId()));
        assertEquals(2, continuesPricingApportionedAdr.size());
        assertEquals(new BigDecimal("80.00"), continuesPricingApportionedAdr.get(accomClass1.getId()).setScale(2, BigDecimal.ROUND_UP));
        assertEquals(new BigDecimal("160.00"), continuesPricingApportionedAdr.get(accomClass2.getId()).setScale(2, BigDecimal.ROUND_UP));
    }

    @Test
    public void updateTransientBaseAccomTypesForSupplementChange() {
        Product product = new Product();
        product.setId(1);
        TransientPricingBaseAccomType pricingBaseAccomType = (TransientPricingBaseAccomType) PricingConfigurationObjectMother.buildPricingBaseAccomTypeWithTax();
        TransientPricingBaseAccomType pricingBaseAccomTypeWithNullData = new TransientPricingBaseAccomType();
        pricingBaseAccomTypeWithNullData.setAccomType(pricingBaseAccomType.getAccomType());
        pricingBaseAccomTypeWithNullData.setStartDate(LocalDate.now());
        pricingBaseAccomTypeWithNullData.setEndDate(LocalDate.now().plusDays(4));
        pricingBaseAccomTypeWithNullData.setSeasonName("Test Sesaon");

        AccomTypeSupplement supplement = PricingConfigurationObjectMother.buildCPConfigSupplementAccomType(1);
        Tax tax = createAndSetupTax();

        when(tenantCrudService.findByNamedQuery(TransientPricingBaseAccomType.FIND_BASE_AT_FOR_PRICEABLE_AC,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters())).thenReturn(Arrays.asList(pricingBaseAccomType, pricingBaseAccomTypeWithNullData));
        when(configParamsService.getParameterValue(FeatureTogglesConfigParamName.PER_PERSON_PRICING_ENABLED)).thenReturn(false);
        doNothing().when(taxInclusiveMigrationService).handleTransientPriceExcludedBaseRoomTypeOnSupplementChange();
        when(tenantCrudService.findByNamedQuerySingleResult(Product.GET_BY_ID,
                QueryParameter.with("productId", pricingBaseAccomType.getProductID()).parameters())).thenReturn(product);
        when(accomTypeSupplementService.findSupplementByAccomTypeAndOccupancyTypeAndProduct(pricingBaseAccomType.getAccomType(), OccupancyType.SINGLE, product)).thenReturn(supplement);

        service.updateTransientBaseAccomTypesForSupplementChange();

        assertEquals(tax.removeRoomTaxRate(pricingBaseAccomType.getMondayCeilingRateWithTax().subtract(supplement.getMondaySupplementValue())), pricingBaseAccomType.getMondayCeilingRate());
        assertEquals(tax.removeRoomTaxRate(pricingBaseAccomType.getMondayFloorRateWithTax().subtract(supplement.getMondaySupplementValue())), pricingBaseAccomType.getMondayFloorRate());
        assertEquals(tax.removeRoomTaxRate(pricingBaseAccomType.getTuesdayCeilingRateWithTax().subtract(supplement.getTuesdaySupplementValue())), pricingBaseAccomType.getTuesdayCeilingRate());
        assertEquals(tax.removeRoomTaxRate(pricingBaseAccomType.getTuesdayFloorRateWithTax().subtract(supplement.getTuesdaySupplementValue())), pricingBaseAccomType.getTuesdayFloorRate());
        assertEquals(tax.removeRoomTaxRate(pricingBaseAccomType.getWednesdayCeilingRateWithTax().subtract(supplement.getWednesdaySupplementValue())), pricingBaseAccomType.getWednesdayCeilingRate());
        assertEquals(tax.removeRoomTaxRate(pricingBaseAccomType.getWednesdayFloorRateWithTax().subtract(supplement.getWednesdaySupplementValue())), pricingBaseAccomType.getWednesdayFloorRate());
        assertEquals(tax.removeRoomTaxRate(pricingBaseAccomType.getThursdayCeilingRateWithTax().subtract(supplement.getThursdaySupplementValue())), pricingBaseAccomType.getThursdayCeilingRate());
        assertEquals(tax.removeRoomTaxRate(pricingBaseAccomType.getThursdayFloorRateWithTax().subtract(supplement.getThursdaySupplementValue())), pricingBaseAccomType.getThursdayFloorRate());
        assertEquals(tax.removeRoomTaxRate(pricingBaseAccomType.getFridayCeilingRateWithTax().subtract(supplement.getFridaySupplementValue())), pricingBaseAccomType.getFridayCeilingRate());
        assertEquals(tax.removeRoomTaxRate(pricingBaseAccomType.getFridayFloorRateWithTax().subtract(supplement.getFridaySupplementValue())), pricingBaseAccomType.getFridayFloorRate());
        assertEquals(tax.removeRoomTaxRate(pricingBaseAccomType.getSaturdayCeilingRateWithTax().subtract(supplement.getSaturdaySupplementValue())), pricingBaseAccomType.getSaturdayCeilingRate());
        assertEquals(tax.removeRoomTaxRate(pricingBaseAccomType.getSaturdayFloorRateWithTax().subtract(supplement.getSaturdaySupplementValue())), pricingBaseAccomType.getSaturdayFloorRate());
        assertEquals(tax.removeRoomTaxRate(pricingBaseAccomType.getSundayCeilingRateWithTax().subtract(supplement.getSundaySupplementValue())), pricingBaseAccomType.getSundayCeilingRate());
        assertEquals(tax.removeRoomTaxRate(pricingBaseAccomType.getSundayFloorRateWithTax().subtract(supplement.getSundaySupplementValue())), pricingBaseAccomType.getSundayFloorRate());

        assertFloorValuesAreNull(pricingBaseAccomTypeWithNullData);
        assertCeilingValuesAreNull(pricingBaseAccomTypeWithNullData);

        verify(taxInclusiveMigrationService).handleTransientPriceExcludedBaseRoomTypeOnSupplementChange();
    }

    @Test
    public void updateGrouptBaseAccomTypesForSupplementChange() {
        Product product = new Product();
        product.setId(1);
        Integer propertyId = 5;
        GroupPricingBaseAccomType pricingBaseAccomType = (GroupPricingBaseAccomType) PricingConfigurationObjectMother.buildGroupPricingBaseAccomTypeWithTax();
        GroupPricingBaseAccomType pricingBaseAccomTypePriceExcluded = (GroupPricingBaseAccomType) PricingConfigurationObjectMother.buildGroupPricingBaseAccomTypeWithTax();
        GroupPricingBaseAccomType pricingBaseAccomTypeWithNullData = new GroupPricingBaseAccomType();
        pricingBaseAccomTypeWithNullData.setAccomType(pricingBaseAccomType.getAccomType());
        pricingBaseAccomTypeWithNullData.setStartDate(LocalDate.now());
        pricingBaseAccomTypeWithNullData.setEndDate(LocalDate.now().plusDays(4));
        pricingBaseAccomTypeWithNullData.setSeasonName("Test Sesaon");


        AccomTypeSupplement supplement = PricingConfigurationObjectMother.buildCPConfigSupplementAccomType(1);
        Tax tax = createAndSetupTax();

        when(multiPropertyCrudService.findByNamedQueryForSingleProperty(propertyId, GroupPricingBaseAccomType.FIND_BASE_AT_FOR_PRICEABLE_AC,
                QueryParameter.with("propertyId", propertyId).parameters())).thenReturn(Arrays.asList(pricingBaseAccomType, pricingBaseAccomTypeWithNullData, pricingBaseAccomTypePriceExcluded));
        when(configParamsService.getParameterValue(FeatureTogglesConfigParamName.PER_PERSON_PRICING_ENABLED)).thenReturn(false);
        doNothing().when(taxInclusiveMigrationService).handleGroupPriceExcludedBaseRoomTypeSupplementOnSave();
        when(tenantCrudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT)).thenReturn(product);
        when(accomTypeSupplementService.findSupplementByAccomTypeAndOccupancyTypeAndProduct(pricingBaseAccomType.getAccomType(), OccupancyType.SINGLE, product)).thenReturn(supplement);
        when(tenantCrudService.findByNamedQuery(TransientPricingBaseAccomType.FIND_BASE_AT_IN_NON_PRICEABLE_AC, QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters())).thenReturn(Arrays.asList(pricingBaseAccomTypePriceExcluded));

        service.updateGroupBaseAccomTypesForSupplementChange();

        assertEquals(tax.removeRoomTaxRate(pricingBaseAccomType.getMondayCeilingRateWithTax()), pricingBaseAccomType.getMondayCeilingRate());
        assertEquals(tax.removeRoomTaxRate(pricingBaseAccomType.getMondayFloorRateWithTax()), pricingBaseAccomType.getMondayFloorRate());
        assertEquals(tax.removeRoomTaxRate(pricingBaseAccomType.getTuesdayCeilingRateWithTax()), pricingBaseAccomType.getTuesdayCeilingRate());
        assertEquals(tax.removeRoomTaxRate(pricingBaseAccomType.getTuesdayFloorRateWithTax()), pricingBaseAccomType.getTuesdayFloorRate());
        assertEquals(tax.removeRoomTaxRate(pricingBaseAccomType.getWednesdayCeilingRateWithTax()), pricingBaseAccomType.getWednesdayCeilingRate());
        assertEquals(tax.removeRoomTaxRate(pricingBaseAccomType.getWednesdayFloorRateWithTax()), pricingBaseAccomType.getWednesdayFloorRate());
        assertEquals(tax.removeRoomTaxRate(pricingBaseAccomType.getThursdayCeilingRateWithTax()), pricingBaseAccomType.getThursdayCeilingRate());
        assertEquals(tax.removeRoomTaxRate(pricingBaseAccomType.getThursdayFloorRateWithTax()), pricingBaseAccomType.getThursdayFloorRate());
        assertEquals(tax.removeRoomTaxRate(pricingBaseAccomType.getFridayCeilingRateWithTax()), pricingBaseAccomType.getFridayCeilingRate());
        assertEquals(tax.removeRoomTaxRate(pricingBaseAccomType.getFridayFloorRateWithTax()), pricingBaseAccomType.getFridayFloorRate());
        assertEquals(tax.removeRoomTaxRate(pricingBaseAccomType.getSaturdayCeilingRateWithTax()), pricingBaseAccomType.getSaturdayCeilingRate());
        assertEquals(tax.removeRoomTaxRate(pricingBaseAccomType.getSaturdayFloorRateWithTax()), pricingBaseAccomType.getSaturdayFloorRate());
        assertEquals(tax.removeRoomTaxRate(pricingBaseAccomType.getSundayCeilingRateWithTax()), pricingBaseAccomType.getSundayCeilingRate());
        assertEquals(tax.removeRoomTaxRate(pricingBaseAccomType.getSundayFloorRateWithTax()), pricingBaseAccomType.getSundayFloorRate());

        assertFloorValuesAreNull(pricingBaseAccomTypeWithNullData);
        assertCeilingValuesAreNull(pricingBaseAccomTypeWithNullData);

        assertEquals(tax.removeRoomTaxRate(pricingBaseAccomTypePriceExcluded.getMondayCeilingRateWithTax().subtract(supplement.getMondaySupplementValue())), pricingBaseAccomTypePriceExcluded.getMondayCeilingRate());
        assertEquals(tax.removeRoomTaxRate(pricingBaseAccomTypePriceExcluded.getMondayFloorRateWithTax().subtract(supplement.getMondaySupplementValue())), pricingBaseAccomTypePriceExcluded.getMondayFloorRate());
        assertEquals(tax.removeRoomTaxRate(pricingBaseAccomTypePriceExcluded.getTuesdayCeilingRateWithTax().subtract(supplement.getTuesdaySupplementValue())), pricingBaseAccomTypePriceExcluded.getTuesdayCeilingRate());
        assertEquals(tax.removeRoomTaxRate(pricingBaseAccomTypePriceExcluded.getTuesdayFloorRateWithTax().subtract(supplement.getTuesdaySupplementValue())), pricingBaseAccomTypePriceExcluded.getTuesdayFloorRate());
        assertEquals(tax.removeRoomTaxRate(pricingBaseAccomTypePriceExcluded.getWednesdayCeilingRateWithTax().subtract(supplement.getWednesdaySupplementValue())), pricingBaseAccomTypePriceExcluded.getWednesdayCeilingRate());
        assertEquals(tax.removeRoomTaxRate(pricingBaseAccomTypePriceExcluded.getWednesdayFloorRateWithTax().subtract(supplement.getWednesdaySupplementValue())), pricingBaseAccomTypePriceExcluded.getWednesdayFloorRate());
        assertEquals(tax.removeRoomTaxRate(pricingBaseAccomTypePriceExcluded.getThursdayCeilingRateWithTax().subtract(supplement.getThursdaySupplementValue())), pricingBaseAccomTypePriceExcluded.getThursdayCeilingRate());
        assertEquals(tax.removeRoomTaxRate(pricingBaseAccomTypePriceExcluded.getThursdayFloorRateWithTax().subtract(supplement.getThursdaySupplementValue())), pricingBaseAccomTypePriceExcluded.getThursdayFloorRate());
        assertEquals(tax.removeRoomTaxRate(pricingBaseAccomTypePriceExcluded.getFridayCeilingRateWithTax().subtract(supplement.getFridaySupplementValue())), pricingBaseAccomTypePriceExcluded.getFridayCeilingRate());
        assertEquals(tax.removeRoomTaxRate(pricingBaseAccomTypePriceExcluded.getFridayFloorRateWithTax().subtract(supplement.getFridaySupplementValue())), pricingBaseAccomTypePriceExcluded.getFridayFloorRate());
        assertEquals(tax.removeRoomTaxRate(pricingBaseAccomTypePriceExcluded.getSaturdayCeilingRateWithTax().subtract(supplement.getSaturdaySupplementValue())), pricingBaseAccomTypePriceExcluded.getSaturdayCeilingRate());
        assertEquals(tax.removeRoomTaxRate(pricingBaseAccomTypePriceExcluded.getSaturdayFloorRateWithTax().subtract(supplement.getSaturdaySupplementValue())), pricingBaseAccomTypePriceExcluded.getSaturdayFloorRate());
        assertEquals(tax.removeRoomTaxRate(pricingBaseAccomTypePriceExcluded.getSundayCeilingRateWithTax().subtract(supplement.getSundaySupplementValue())), pricingBaseAccomTypePriceExcluded.getSundayCeilingRate());
        assertEquals(tax.removeRoomTaxRate(pricingBaseAccomTypePriceExcluded.getSundayFloorRateWithTax().subtract(supplement.getSundaySupplementValue())), pricingBaseAccomTypePriceExcluded.getSundayFloorRate());

        verify(taxInclusiveMigrationService).handleGroupPriceExcludedBaseRoomTypeOnSupplementChangeToggle();
    }

    @Test
    public void updateGroupBaseAccomTypesForSupplementChangeWhenSupplementOffsetIsPercent() {
        Product product = new Product();
        product.setId(1);
        Integer propertyId = 5;
        GroupPricingBaseAccomType pricingBaseAccomType = (GroupPricingBaseAccomType) PricingConfigurationObjectMother.buildGroupPricingBaseAccomTypeWithTax();
        GroupPricingBaseAccomType pricingBaseAccomTypePriceExcluded = (GroupPricingBaseAccomType) PricingConfigurationObjectMother.buildGroupPricingBaseAccomTypeWithTax();
        GroupPricingBaseAccomType pricingBaseAccomTypeWithNullData = new GroupPricingBaseAccomType();
        pricingBaseAccomTypeWithNullData.setAccomType(pricingBaseAccomType.getAccomType());
        pricingBaseAccomTypeWithNullData.setSeasonName("Test Sesaon");


        AccomTypeSupplement supplement = PricingConfigurationObjectMother.buildCPConfigSupplementAccomTypeForPercentage(1);
        Tax tax = createAndSetupTax();

        when(multiPropertyCrudService.findByNamedQueryForSingleProperty(propertyId, GroupPricingBaseAccomType.FIND_BASE_AT_FOR_PRICEABLE_AC,
                QueryParameter.with("propertyId", propertyId).parameters())).thenReturn(Arrays.asList(pricingBaseAccomType, pricingBaseAccomTypeWithNullData, pricingBaseAccomTypePriceExcluded));
        when(configParamsService.getParameterValue(FeatureTogglesConfigParamName.PER_PERSON_PRICING_ENABLED)).thenReturn(false);
        doNothing().when(taxInclusiveMigrationService).handleGroupPriceExcludedBaseRoomTypeSupplementOnSave();
        when(tenantCrudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT)).thenReturn(product);
        when(accomTypeSupplementService.findSupplementByAccomTypeAndOccupancyTypeAndProduct(pricingBaseAccomType.getAccomType(), OccupancyType.SINGLE, product)).thenReturn(supplement);
        when(tenantCrudService.findByNamedQuery(TransientPricingBaseAccomType.FIND_BASE_AT_IN_NON_PRICEABLE_AC, QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters())).thenReturn(Arrays.asList(pricingBaseAccomTypePriceExcluded));

        service.updateGroupBaseAccomTypesForSupplementChange();

        assertEquals(tax.removeRoomTaxRate(pricingBaseAccomType.getMondayCeilingRateWithTax()), pricingBaseAccomType.getMondayCeilingRate());
        assertEquals(tax.removeRoomTaxRate(pricingBaseAccomType.getMondayFloorRateWithTax()), pricingBaseAccomType.getMondayFloorRate());
        assertEquals(tax.removeRoomTaxRate(pricingBaseAccomType.getTuesdayCeilingRateWithTax()), pricingBaseAccomType.getTuesdayCeilingRate());
        assertEquals(tax.removeRoomTaxRate(pricingBaseAccomType.getTuesdayFloorRateWithTax()), pricingBaseAccomType.getTuesdayFloorRate());
        assertEquals(tax.removeRoomTaxRate(pricingBaseAccomType.getWednesdayCeilingRateWithTax()), pricingBaseAccomType.getWednesdayCeilingRate());
        assertEquals(tax.removeRoomTaxRate(pricingBaseAccomType.getWednesdayFloorRateWithTax()), pricingBaseAccomType.getWednesdayFloorRate());
        assertEquals(tax.removeRoomTaxRate(pricingBaseAccomType.getThursdayCeilingRateWithTax()), pricingBaseAccomType.getThursdayCeilingRate());
        assertEquals(tax.removeRoomTaxRate(pricingBaseAccomType.getThursdayFloorRateWithTax()), pricingBaseAccomType.getThursdayFloorRate());
        assertEquals(tax.removeRoomTaxRate(pricingBaseAccomType.getFridayCeilingRateWithTax()), pricingBaseAccomType.getFridayCeilingRate());
        assertEquals(tax.removeRoomTaxRate(pricingBaseAccomType.getFridayFloorRateWithTax()), pricingBaseAccomType.getFridayFloorRate());
        assertEquals(tax.removeRoomTaxRate(pricingBaseAccomType.getSaturdayCeilingRateWithTax()), pricingBaseAccomType.getSaturdayCeilingRate());
        assertEquals(tax.removeRoomTaxRate(pricingBaseAccomType.getSaturdayFloorRateWithTax()), pricingBaseAccomType.getSaturdayFloorRate());
        assertEquals(tax.removeRoomTaxRate(pricingBaseAccomType.getSundayCeilingRateWithTax()), pricingBaseAccomType.getSundayCeilingRate());
        assertEquals(tax.removeRoomTaxRate(pricingBaseAccomType.getSundayFloorRateWithTax()), pricingBaseAccomType.getSundayFloorRate());

        assertFloorValuesAreNull(pricingBaseAccomTypeWithNullData);
        assertCeilingValuesAreNull(pricingBaseAccomTypeWithNullData);

        assertEquals(tax.removeRoomTaxRate(Supplement.removeSupplementFrom(pricingBaseAccomTypePriceExcluded.getMondayCeilingRateWithTax(), supplement.getMondaySupplementValue(), supplement.getOffsetMethod())), pricingBaseAccomTypePriceExcluded.getMondayCeilingRate());
        assertEquals(tax.removeRoomTaxRate(Supplement.removeSupplementFrom(pricingBaseAccomTypePriceExcluded.getMondayFloorRateWithTax(), supplement.getMondaySupplementValue(), supplement.getOffsetMethod())), pricingBaseAccomTypePriceExcluded.getMondayFloorRate());
        assertEquals(tax.removeRoomTaxRate(Supplement.removeSupplementFrom(pricingBaseAccomTypePriceExcluded.getTuesdayCeilingRateWithTax(), supplement.getTuesdaySupplementValue(), supplement.getOffsetMethod())), pricingBaseAccomTypePriceExcluded.getTuesdayCeilingRate());
        assertEquals(tax.removeRoomTaxRate(Supplement.removeSupplementFrom(pricingBaseAccomTypePriceExcluded.getTuesdayFloorRateWithTax(), supplement.getTuesdaySupplementValue(), supplement.getOffsetMethod())), pricingBaseAccomTypePriceExcluded.getTuesdayFloorRate());
        assertEquals(tax.removeRoomTaxRate(Supplement.removeSupplementFrom(pricingBaseAccomTypePriceExcluded.getWednesdayCeilingRateWithTax(), supplement.getWednesdaySupplementValue(), supplement.getOffsetMethod())), pricingBaseAccomTypePriceExcluded.getWednesdayCeilingRate());
        assertEquals(tax.removeRoomTaxRate(Supplement.removeSupplementFrom(pricingBaseAccomTypePriceExcluded.getWednesdayFloorRateWithTax(), supplement.getWednesdaySupplementValue(), supplement.getOffsetMethod())), pricingBaseAccomTypePriceExcluded.getWednesdayFloorRate());
        assertEquals(tax.removeRoomTaxRate(Supplement.removeSupplementFrom(pricingBaseAccomTypePriceExcluded.getThursdayCeilingRateWithTax(), supplement.getThursdaySupplementValue(), supplement.getOffsetMethod())), pricingBaseAccomTypePriceExcluded.getThursdayCeilingRate());
        assertEquals(tax.removeRoomTaxRate(Supplement.removeSupplementFrom(pricingBaseAccomTypePriceExcluded.getThursdayFloorRateWithTax(), supplement.getThursdaySupplementValue(), supplement.getOffsetMethod())), pricingBaseAccomTypePriceExcluded.getThursdayFloorRate());
        assertEquals(tax.removeRoomTaxRate(Supplement.removeSupplementFrom(pricingBaseAccomTypePriceExcluded.getFridayCeilingRateWithTax(), supplement.getFridaySupplementValue(), supplement.getOffsetMethod())), pricingBaseAccomTypePriceExcluded.getFridayCeilingRate());
        assertEquals(tax.removeRoomTaxRate(Supplement.removeSupplementFrom(pricingBaseAccomTypePriceExcluded.getFridayFloorRateWithTax(), supplement.getFridaySupplementValue(), supplement.getOffsetMethod())), pricingBaseAccomTypePriceExcluded.getFridayFloorRate());
        assertEquals(tax.removeRoomTaxRate(Supplement.removeSupplementFrom(pricingBaseAccomTypePriceExcluded.getSaturdayCeilingRateWithTax(), supplement.getSaturdaySupplementValue(), supplement.getOffsetMethod())), pricingBaseAccomTypePriceExcluded.getSaturdayCeilingRate());
        assertEquals(tax.removeRoomTaxRate(Supplement.removeSupplementFrom(pricingBaseAccomTypePriceExcluded.getSaturdayFloorRateWithTax(), supplement.getSaturdaySupplementValue(), supplement.getOffsetMethod())), pricingBaseAccomTypePriceExcluded.getSaturdayFloorRate());
        assertEquals(tax.removeRoomTaxRate(Supplement.removeSupplementFrom(pricingBaseAccomTypePriceExcluded.getSundayCeilingRateWithTax(), supplement.getSundaySupplementValue(), supplement.getOffsetMethod())), pricingBaseAccomTypePriceExcluded.getSundayCeilingRate());
        assertEquals(tax.removeRoomTaxRate(Supplement.removeSupplementFrom(pricingBaseAccomTypePriceExcluded.getSundayFloorRateWithTax(), supplement.getSundaySupplementValue(), supplement.getOffsetMethod())), pricingBaseAccomTypePriceExcluded.getSundayFloorRate());

        verify(taxInclusiveMigrationService).handleGroupPriceExcludedBaseRoomTypeOnSupplementChangeToggle();
    }

    private void assertFloorValuesAreNull(PricingBaseAccomType pricingBaseAccomTypeWithNullData) {
        assertNull(pricingBaseAccomTypeWithNullData.getMondayFloorRate());
        assertNull(pricingBaseAccomTypeWithNullData.getMondayFloorRateWithTax());
        assertNull(pricingBaseAccomTypeWithNullData.getTuesdayFloorRate());
        assertNull(pricingBaseAccomTypeWithNullData.getTuesdayFloorRateWithTax());
        assertNull(pricingBaseAccomTypeWithNullData.getWednesdayFloorRate());
        assertNull(pricingBaseAccomTypeWithNullData.getWednesdayFloorRateWithTax());
        assertNull(pricingBaseAccomTypeWithNullData.getThursdayFloorRate());
        assertNull(pricingBaseAccomTypeWithNullData.getThursdayFloorRateWithTax());
        assertNull(pricingBaseAccomTypeWithNullData.getFridayFloorRate());
        assertNull(pricingBaseAccomTypeWithNullData.getFridayFloorRateWithTax());
        assertNull(pricingBaseAccomTypeWithNullData.getSaturdayFloorRate());
        assertNull(pricingBaseAccomTypeWithNullData.getSaturdayFloorRateWithTax());
        assertNull(pricingBaseAccomTypeWithNullData.getSundayFloorRate());
        assertNull(pricingBaseAccomTypeWithNullData.getSundayFloorRateWithTax());
    }

    private void assertCeilingValuesAreNull(PricingBaseAccomType pricingBaseAccomTypeWithNullData) {
        assertNull(pricingBaseAccomTypeWithNullData.getMondayCeilingRate());
        assertNull(pricingBaseAccomTypeWithNullData.getMondayCeilingRateWithTax());
        assertNull(pricingBaseAccomTypeWithNullData.getTuesdayCeilingRate());
        assertNull(pricingBaseAccomTypeWithNullData.getTuesdayCeilingRateWithTax());
        assertNull(pricingBaseAccomTypeWithNullData.getWednesdayCeilingRate());
        assertNull(pricingBaseAccomTypeWithNullData.getWednesdayCeilingRateWithTax());
        assertNull(pricingBaseAccomTypeWithNullData.getThursdayCeilingRate());
        assertNull(pricingBaseAccomTypeWithNullData.getThursdayCeilingRateWithTax());
        assertNull(pricingBaseAccomTypeWithNullData.getFridayCeilingRate());
        assertNull(pricingBaseAccomTypeWithNullData.getFridayCeilingRateWithTax());
        assertNull(pricingBaseAccomTypeWithNullData.getSaturdayCeilingRate());
        assertNull(pricingBaseAccomTypeWithNullData.getSaturdayCeilingRateWithTax());
        assertNull(pricingBaseAccomTypeWithNullData.getSundayCeilingRate());
        assertNull(pricingBaseAccomTypeWithNullData.getSundayCeilingRateWithTax());
    }

    @Test
    public void updateTransientBaseAccomTypesForSupplementChangeWhenSeasonableTaxesAreConfigured() {
        Product product = new Product();
        product.setId(1);
        PricingBaseAccomType pricingBaseAccomType = PricingConfigurationObjectMother.buildPricingBaseAccomTypeWithTax();
        PricingBaseAccomType transientBaseAccomTypeSeason = PricingConfigurationObjectMother.buildPricingBaseAccomTypeSeasonTaxInclusive();
        PricingBaseAccomType transientBaseAccomTypeSeason2 = PricingConfigurationObjectMother.buildPricingBaseAccomTypeSeasonTaxInclusive();
        AccomType accomType = new AccomType();
        accomType.setId(1);
        LocalDate startDate = new LocalDate(2018, 10, 1);
        LocalDate endDate = new LocalDate(2018, 10, 1);

        transientBaseAccomTypeSeason.setAccomType(accomType);
        transientBaseAccomTypeSeason.setStartDate(startDate);
        transientBaseAccomTypeSeason.setEndDate(endDate);
        transientBaseAccomTypeSeason.setMondayFloorRateWithTax(BigDecimal.TEN);
        transientBaseAccomTypeSeason.setMondayCeilingRateWithTax(BigDecimal.valueOf(100));

        transientBaseAccomTypeSeason2.setAccomType(accomType);
        transientBaseAccomTypeSeason2.setStartDate(startDate.plusDays(1));
        transientBaseAccomTypeSeason2.setEndDate(endDate.plusDays(1));
        transientBaseAccomTypeSeason2.setTuesdayFloorRateWithTax(BigDecimal.TEN);
        transientBaseAccomTypeSeason2.setTuesdayCeilingRateWithTax(BigDecimal.valueOf(100));

        AccomTypeSupplement supplement = PricingConfigurationObjectMother.buildCPConfigSupplementAccomType(1);
        AccomTypeSupplement supplement1 = PricingConfigurationObjectMother.buildCPConfigSupplementAccomType(1);
        supplement1.setStartDate(startDate);
        supplement1.setStartDate(endDate);
        AccomTypeSupplement supplement2 = PricingConfigurationObjectMother.buildCPConfigSupplementAccomType(1);
        supplement2.setStartDate(startDate.plusDays(1));
        supplement2.setStartDate(endDate.plusDays(1));

        Tax tax = createAndSetupTax();

        when(tenantCrudService.findByNamedQuery(TransientPricingBaseAccomType.FIND_BASE_AT_FOR_PRICEABLE_AC,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters())).thenReturn(Arrays.asList(pricingBaseAccomType, transientBaseAccomTypeSeason, transientBaseAccomTypeSeason2));
        when(configParamsService.getParameterValue(FeatureTogglesConfigParamName.PER_PERSON_PRICING_ENABLED)).thenReturn(false);
        doNothing().when(taxInclusiveMigrationService).handleTransientPriceExcludedBaseRoomTypeOnSupplementChange();
        when(tenantCrudService.findByNamedQuerySingleResult(Product.GET_BY_ID,
                QueryParameter.with("productId", pricingBaseAccomType.getProductID()).parameters())).thenReturn(product);
        when(accomTypeSupplementService.findSupplementByAccomTypeAndOccupancyTypeAndProduct(pricingBaseAccomType.getAccomType(), OccupancyType.SINGLE, product)).thenReturn(supplement);
        when(accomTypeSupplementService.getSupplementValueMap(startDate, endDate)).thenReturn(buildSupplementMap(startDate, accomType.getId(), OccupancyType.SINGLE));
        when(accomTypeSupplementService.getSupplementValueMap(startDate.plusDays(1), endDate.plusDays(1))).thenReturn(buildSupplementMap(startDate, accomType.getId(), OccupancyType.SINGLE));
        Tax seasonalTax = getTax(startDate.plusDays(1), startDate.plusDays(1), BigDecimal.valueOf(12));
        Map<LocalDate, Tax> taxesByDateRange = new HashMap<>();
        taxesByDateRange.put(startDate, tax);
        taxesByDateRange.put(startDate.plusDays(1), seasonalTax);
        when(taxService.findTaxesForDateRange(startDate, endDate.plusDays(1))).thenReturn(taxesByDateRange);

        service.updateTransientBaseAccomTypesForSupplementChange();

        assertEquals(tax.removeRoomTaxRate(transientBaseAccomTypeSeason.getMondayCeilingRateWithTax().subtract(BigDecimal.valueOf(5))), transientBaseAccomTypeSeason.getMondayCeilingRate());
        assertEquals(tax.removeRoomTaxRate(transientBaseAccomTypeSeason.getMondayFloorRateWithTax().subtract(BigDecimal.valueOf(5))), transientBaseAccomTypeSeason.getMondayFloorRate());
        assertEquals(seasonalTax.removeRoomTaxRate(transientBaseAccomTypeSeason2.getTuesdayCeilingRateWithTax().subtract(BigDecimal.valueOf(5))), transientBaseAccomTypeSeason2.getTuesdayCeilingRate());
        assertEquals(seasonalTax.removeRoomTaxRate(transientBaseAccomTypeSeason2.getTuesdayFloorRateWithTax().subtract(BigDecimal.valueOf(5))), transientBaseAccomTypeSeason2.getTuesdayFloorRate());
        assertEquals(tax.removeRoomTaxRate(pricingBaseAccomType.getMondayCeilingRateWithTax().subtract(supplement.getMondaySupplementValue())), pricingBaseAccomType.getMondayCeilingRate());
        assertEquals(tax.removeRoomTaxRate(pricingBaseAccomType.getMondayFloorRateWithTax().subtract(supplement.getMondaySupplementValue())), pricingBaseAccomType.getMondayFloorRate());
        assertEquals(tax.removeRoomTaxRate(pricingBaseAccomType.getTuesdayCeilingRateWithTax().subtract(supplement.getTuesdaySupplementValue())), pricingBaseAccomType.getTuesdayCeilingRate());
        assertEquals(tax.removeRoomTaxRate(pricingBaseAccomType.getTuesdayFloorRateWithTax().subtract(supplement.getTuesdaySupplementValue())), pricingBaseAccomType.getTuesdayFloorRate());
        assertEquals(tax.removeRoomTaxRate(pricingBaseAccomType.getWednesdayCeilingRateWithTax().subtract(supplement.getWednesdaySupplementValue())), pricingBaseAccomType.getWednesdayCeilingRate());
        assertEquals(tax.removeRoomTaxRate(pricingBaseAccomType.getWednesdayFloorRateWithTax().subtract(supplement.getWednesdaySupplementValue())), pricingBaseAccomType.getWednesdayFloorRate());
        assertEquals(tax.removeRoomTaxRate(pricingBaseAccomType.getThursdayCeilingRateWithTax().subtract(supplement.getThursdaySupplementValue())), pricingBaseAccomType.getThursdayCeilingRate());
        assertEquals(tax.removeRoomTaxRate(pricingBaseAccomType.getThursdayFloorRateWithTax().subtract(supplement.getThursdaySupplementValue())), pricingBaseAccomType.getThursdayFloorRate());
        assertEquals(tax.removeRoomTaxRate(pricingBaseAccomType.getFridayCeilingRateWithTax().subtract(supplement.getFridaySupplementValue())), pricingBaseAccomType.getFridayCeilingRate());
        assertEquals(tax.removeRoomTaxRate(pricingBaseAccomType.getFridayFloorRateWithTax().subtract(supplement.getFridaySupplementValue())), pricingBaseAccomType.getFridayFloorRate());
        assertEquals(tax.removeRoomTaxRate(pricingBaseAccomType.getSaturdayCeilingRateWithTax().subtract(supplement.getSaturdaySupplementValue())), pricingBaseAccomType.getSaturdayCeilingRate());
        assertEquals(tax.removeRoomTaxRate(pricingBaseAccomType.getSaturdayFloorRateWithTax().subtract(supplement.getSaturdaySupplementValue())), pricingBaseAccomType.getSaturdayFloorRate());
        assertEquals(tax.removeRoomTaxRate(pricingBaseAccomType.getSundayCeilingRateWithTax().subtract(supplement.getSundaySupplementValue())), pricingBaseAccomType.getSundayCeilingRate());
        assertEquals(tax.removeRoomTaxRate(pricingBaseAccomType.getSundayFloorRateWithTax().subtract(supplement.getSundaySupplementValue())), pricingBaseAccomType.getSundayFloorRate());

        verify(taxInclusiveMigrationService).handleTransientPriceExcludedBaseRoomTypeOnSupplementChange();
    }

    @Test
    public void testGetCPDecisionContextWithParameters() {
        final LocalDate startDate = LocalDate.now();
        final LocalDate endDate = LocalDate.now();

        final CPConfiguration cpConfiguration = new CPConfiguration();
        final Map<Integer, PricingRule> pricingRules = new HashMap<>();
        PricingRule pricingRule = new PricingRule();
        pricingRules.put(1, pricingRule);
        final Tax tax = new Tax();
        final List<PricingAccomClass> pricingAccomClasses = new ArrayList<>();
        final LocalDate caughtUpDate = LocalDate.now();

        //Determine maximumOccupantsEntities
        List<MaximumOccupantsEntity> maximumOccupantsEntitiesList = new ArrayList<>();
        MaximumOccupantsEntity entity = new MaximumOccupantsEntity();
        entity.setAccomTypeId(5);
        maximumOccupantsEntitiesList.add(entity);

        final List<ProductPackage> ungroupedProductPackages = new ArrayList<>();

        final List<OccupantBucketEntity> occupantBucketEntities = new ArrayList<>();
        OccupantBucketEntity childOccupancyBucket = new OccupantBucketEntity();
        childOccupancyBucket.setOccupancyType(OccupancyType.ONE_CHILD);
        occupantBucketEntities.add(childOccupancyBucket);
        OccupantBucketEntity adultOccupancyBucket = new OccupantBucketEntity();
        adultOccupancyBucket.setOccupancyType(OccupancyType.DOUBLE);
        occupantBucketEntities.add(adultOccupancyBucket);


        when(configParamsService.getParameterValue(FeatureTogglesConfigParamName.PER_PERSON_PRICING_ENABLED)).thenReturn(false);

        final CPDecisionContext cpDecisionContext = service.getCPDecisionContext(startDate, endDate, true, cpConfiguration, OccupancyType.DOUBLE,
                true, pricingRules, tax, pricingAccomClasses, occupantBucketEntities, maximumOccupantsEntitiesList,
                true, true, false, true, caughtUpDate, ungroupedProductPackages);

        assertNotNull(cpDecisionContext);
        assertEquals(cpConfiguration, cpDecisionContext.getCPConfiguration());
        assertTrue(cpDecisionContext.isBaseRoomTypeOnlyEnabled());
        assertEquals(pricingRule, cpDecisionContext.getPricingRule(1));
        assertEquals(tax, cpDecisionContext.getTax());

        Map<AccomClass, PricingAccomClass> pricingAccomClassMap = pricingAccomClasses.stream().collect(Collectors.toMap(PricingAccomClass::getAccomClass, Function.identity()));
        assertEquals(pricingAccomClassMap, cpDecisionContext.getPricingAccomClasses());

        assertEquals(OccupancyType.DOUBLE, cpDecisionContext.getMaxAdultOccupancyType());
        assertEquals(OccupancyType.ONE_CHILD, cpDecisionContext.getMaxChildQuantityOccupancyType());

        Map<Integer, MaximumOccupantsEntity> expectedMaximumEntitiesList = maximumOccupantsEntitiesList.stream().collect(Collectors.toMap(MaximumOccupantsEntity::getAccomTypeId, Function.identity(), (accomTypeId, maximumOccupantsEntity) -> accomTypeId));
        assertEquals(expectedMaximumEntitiesList, cpDecisionContext.getMaximumOccupantsEntities());

        assertEquals(OccupancyType.ONE_CHILD, cpDecisionContext.getMaxChildAgeBucketOccupancyType());

        assertEquals(caughtUpDate, cpDecisionContext.getCaughtUpDate());

        assertEquals(0, cpDecisionContext.getProductRateOffsets().size());
        assertEquals(0, cpDecisionContext.getProductPackages().size());
    }

    @Test
    public void testGetCPDecisionContextWith4ParametersUsingThreads() {
        System.setProperty("pacman.enable.multithreading.pricing.config.service", "true");
        final LocalDate startDate = LocalDate.now();
        final LocalDate endDate = LocalDate.now();

        final List<OccupantBucketEntity> occupantBucketEntities = new ArrayList<>();
        OccupantBucketEntity adultOccupancyBucket = new OccupantBucketEntity();
        adultOccupancyBucket.setOccupancyType(OccupancyType.DOUBLE);
        occupantBucketEntities.add(adultOccupancyBucket);

        when(configParamsService.getParameterValue(FeatureTogglesConfigParamName.PER_PERSON_PRICING_ENABLED)).thenReturn(false);
        when(tenantCrudService.findOne(CPConfiguration.class)).thenReturn(new CPConfiguration());
        when(tenantCrudService.findByNamedQuery(Product.GET_SYSTEM_DEFAULT)).thenReturn(Arrays.asList(new Product()));
        when(taxService.findTax()).thenReturn(new Tax());
        when(tenantCrudService.findAll(OccupantBucketEntity.class)).thenReturn(occupantBucketEntities);
        final CPDecisionContext cpDecisionContext = service.getCPDecisionContext(startDate, endDate, OccupancyType.DOUBLE, null);

        assertNotNull(cpDecisionContext);
    }


    private void insertCpCfgAcTestData(AccomClass accomClass1, AccomClass accomClass2, AccomType accomType11, AccomType accomType21) {
        tenantCrudService().executeUpdateByNativeQuery("insert into CP_Cfg_AC values(" + PacmanWorkContextHelper.getPropertyId() + "," + accomClass1.getId() + "," + accomType11.getId() + ",0,1, getdate(),1,getdate(),null, null,0)");
        tenantCrudService().executeUpdateByNativeQuery("insert into CP_Cfg_AC values(" + PacmanWorkContextHelper.getPropertyId() + "," + accomClass2.getId() + "," + accomType21.getId() + ",0,1, getdate(),1,getdate(),null, null,0)");
    }

    private void insertCpCfgBaseAtTestData(AccomType accomType11, AccomType accomType12, AccomType accomType21) {
        tenantCrudService().executeUpdateByNativeQuery("insert into CP_Cfg_Base_AT (Property_ID, Accom_Type_ID, Start_Date, End_Date, Sunday_Floor_Rate, Sunday_Ceil_Rate, Monday_Floor_Rate, Monday_Ceil_Rate, Tuesday_Floor_Rate, Tuesday_Ceil_Rate, Wednesday_Floor_Rate, Wednesday_Ceil_Rate, Thursday_Floor_Rate, Thursday_Ceil_Rate, Friday_Floor_Rate, Friday_Ceil_Rate, Saturday_Floor_Rate, Saturday_Ceil_Rate, Status_ID, Product_ID) " +
                "    values (" + PacmanWorkContextHelper.getPropertyId() + " , " + accomType11.getId() + " , null, null, 100.00, 60.00, 99.00, 99.00, 99.00, 99.00, 99.00, 99.00, 99.00, 99.00, 99.00, 99.00, 99.00, 99.00, 1, 1)");

        tenantCrudService().executeUpdateByNativeQuery("insert into CP_Cfg_Base_AT (Property_ID, Accom_Type_ID, Start_Date, End_Date, Sunday_Floor_Rate, Sunday_Ceil_Rate, Monday_Floor_Rate, Monday_Ceil_Rate, Tuesday_Floor_Rate, Tuesday_Ceil_Rate, Wednesday_Floor_Rate, Wednesday_Ceil_Rate, Thursday_Floor_Rate, Thursday_Ceil_Rate, Friday_Floor_Rate, Friday_Ceil_Rate, Saturday_Floor_Rate, Saturday_Ceil_Rate, Status_ID, Product_ID) " +
                "    values (" + PacmanWorkContextHelper.getPropertyId() + " , " + accomType21.getId() + ", null, null, 200.00, 120.00, 99.00, 99.00, 99.00, 99.00, 99.00, 99.00, 99.00, 99.00, 99.00, 99.00, 99.00, 99.00, 1, 1)");
    }

    private void createAndMockPricingAccomClassesForProperty(AccomClass accomClass1, AccomClass accomClass2) {
        PricingAccomClass pricingAccomClass1 = getPricingAccomClass(accomClass1, true);
        PricingAccomClass pricingAccomClass2 = getPricingAccomClass(accomClass2, false);

        Map<String, Object> queryParams = QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters();
        when(multiPropertyCrudService.findByNamedQueryForSingleProperty(PacmanWorkContextHelper.getPropertyId(), PricingAccomClass.FIND_BY_PROPERTY_ID, queryParams))
                .thenReturn(Arrays.asList(pricingAccomClass1, pricingAccomClass2));
    }

    private AccomClass getAccomClass(int accomClassId) {
        AccomClass accomClass = new AccomClass();
        accomClass.setId(accomClassId);
        return accomClass;
    }

    private PricingAccomClass getPricingAccomClass(AccomType accomType) {
        PricingAccomClass pricingAccomClass = PricingConfigurationObjectMother.buildPricingAccomClass();
        pricingAccomClass.setAccomType(accomType);
        return pricingAccomClass;
    }

    private PricingAccomClass getPricingAccomClass(AccomClass accomClass, boolean isPriceExcluded) {
        PricingAccomClass pricingAccomClass = PricingConfigurationObjectMother.buildPricingAccomClass();
        pricingAccomClass.setAccomClass(accomClass);
        pricingAccomClass.setPriceExcluded(isPriceExcluded);
        return pricingAccomClass;
    }

    private AccomType getAccomType(Integer accomTypeId) {
        AccomType accomType = new AccomType();
        accomType.setId(accomTypeId);
        return accomType;
    }

    @Test
    public void isPerRoomServicingCostAvailableForAllRoomClass_false() {
        Mockito.when(tenantCrudService.findByNamedQuery(AccomClass.ALL_ACTIVE_NON_DEFAULT_NONEMPTY_BY_RANK_ORDER, QueryParameter.with("propertyId", PacmanThreadLocalContextHolder.getWorkContext().getPropertyId()).parameters()))
                .thenReturn(Arrays.asList(getAccomClass(5)));
        Mockito.when(tenantCrudService.findByNamedQuery(GroupPricingConfiguration.FIND_BY_EVALUATION_METHOD,
                QueryParameter.with("defaultEvaluationMethod", GroupPricingEvaluationMethod.RC)
                        .and("propertyId", PacmanThreadLocalContextHolder.getWorkContext().getPropertyId()).parameters())).thenReturn(new ArrayList<>());
        service.setTenantCrudService(tenantCrudService);
        assertFalse(service.isPerRoomServicingCostAvailableForAllRoomClass());
    }

    @Test
    public void isPerRoomServicingCostAvailableForAllRoomClass_true() {
        Mockito.when(tenantCrudService.findByNamedQuery(AccomClass.ALL_ACTIVE_NON_DEFAULT_NONEMPTY_BY_RANK_ORDER,
                        QueryParameter.with("propertyId", PacmanThreadLocalContextHolder.getWorkContext().getPropertyId()).parameters()))
                .thenReturn(Arrays.asList(getAccomClass(5)));
        Mockito.when(tenantCrudService.findByNamedQuery(GroupPricingConfiguration.FIND_BY_EVALUATION_METHOD,
                QueryParameter.with("defaultEvaluationMethod", GroupPricingEvaluationMethod.RC)
                        .and("propertyId", PacmanThreadLocalContextHolder.getWorkContext().getPropertyId()).parameters())).thenReturn(Arrays.asList(getGroupPricingConfiguration(5)));
        service.setTenantCrudService(tenantCrudService);
        assertTrue(service.isPerRoomServicingCostAvailableForAllRoomClass());
    }

    @Test
    public void deleteChildAgeGroups() {
        service.deleteChildAgeGroups();
        verify(tenantCrudService, times(1)).deleteAll(OccupantBucketEntity.class);
    }

    @Test
    public void deleteMaximumOccupants() {
        service.deleteMaximumOccupants();
        verify(tenantCrudService, times(1)).deleteAll(MaximumOccupantsEntity.class);
    }

    @Test
    public void getActiveProductPackagesTest() {
        service.getActiveProductPackages();
        verify(tenantCrudService).findByNamedQuery(ProductPackage.BY_ACTIVE_PRODUCTS);
    }

    @Test
    public void getMaximumEntitiesList() {
        service.getMaximumEntitiesList();
        verify(tenantCrudService).findByNamedQuery(MaximumOccupantsEntity.ALL_ACTIVE);
    }

    private GroupPricingConfiguration getGroupPricingConfiguration(int accomClassId) {
        GroupPricingConfiguration groupPricingConfiguration = new GroupPricingConfiguration();
        groupPricingConfiguration.setAccomClass(getAccomClass(accomClassId));
        return groupPricingConfiguration;
    }

    private Map<AccomTypeSupplementValuePK, AccomTypeSupplementValue> buildSupplementMap(LocalDate
                                                                                                 arrivalDate, Integer accomTypeId, OccupancyType occupancyType) {
        Map<AccomTypeSupplementValuePK, AccomTypeSupplementValue> map = new HashMap<>();

        AccomTypeSupplementValuePK accomTypeSupplementValuePK = new AccomTypeSupplementValuePK(1, arrivalDate, accomTypeId, occupancyType);
        AccomTypeSupplementValue accomTypeSupplementValue = getAccomTypeSupplementValue(accomTypeSupplementValuePK);
        AccomTypeSupplementValuePK accomTypeSupplementValuePK1 = new AccomTypeSupplementValuePK(1, arrivalDate.plusDays(1), accomTypeId, occupancyType);
        AccomTypeSupplementValue accomTypeSupplementValue1 = getAccomTypeSupplementValue(accomTypeSupplementValuePK1);

        map.put(accomTypeSupplementValuePK, accomTypeSupplementValue);
        map.put(accomTypeSupplementValuePK1, accomTypeSupplementValue1);

        return map;
    }

    private Map<AccomTypeSupplementValuePK, AccomTypeSupplementValue> buildSupplementMapForRange(java.time.LocalDate startDate, java.time.LocalDate endDate,
                                                                                                 Integer accomTypeId,
                                                                                                 OccupancyType occupancyType, OffsetMethod offsetMethod) {
        Map<AccomTypeSupplementValuePK, AccomTypeSupplementValue> map = new HashMap<>();
        java.time.LocalDate firstDate = startDate;
        while (firstDate.isBefore(endDate)) {

            AccomTypeSupplementValuePK accomTypeSupplementValuePK = new AccomTypeSupplementValuePK(1, LocalDateUtils.toJodaLocalDate(firstDate), accomTypeId, occupancyType);
            AccomTypeSupplementValue accomTypeSupplementValue = getAccomTypeSupplementValue(accomTypeSupplementValuePK);
            accomTypeSupplementValue.setOffsetMethod(offsetMethod);
            map.put(accomTypeSupplementValuePK, accomTypeSupplementValue);
            firstDate = firstDate.plusDays(1);
        }
        return map;
    }

    private AccomTypeSupplementValue getAccomTypeSupplementValue(AccomTypeSupplementValuePK accomTypeSupplementValuePK) {
        AccomTypeSupplementValue accomTypeSupplementValue = new AccomTypeSupplementValue();
        accomTypeSupplementValue.setId(accomTypeSupplementValuePK);
        accomTypeSupplementValue.setValue(BigDecimal.valueOf(5));
        return accomTypeSupplementValue;
    }

    private AccomTypeSupplement buildDefaultSupplements(AccomType accomType, OccupancyType occupancyType) {
        AccomTypeSupplement defaultSupplement = new AccomTypeSupplement();

        defaultSupplement.setAccomType(accomType);
        defaultSupplement.setOccupancyType(occupancyType);
        defaultSupplement.setMondaySupplementValue(BigDecimal.valueOf(1));
        defaultSupplement.setTuesdaySupplementValue(BigDecimal.valueOf(2));
        defaultSupplement.setWednesdaySupplementValue(BigDecimal.valueOf(3));
        defaultSupplement.setThursdaySupplementValue(BigDecimal.valueOf(4));
        defaultSupplement.setFridaySupplementValue(BigDecimal.valueOf(5));
        defaultSupplement.setSaturdaySupplementValue(BigDecimal.valueOf(6));
        defaultSupplement.setSundaySupplementValue(BigDecimal.valueOf(7));

        return defaultSupplement;
    }

    private Tax getTax(LocalDate startDate, LocalDate endDate, BigDecimal taxRate) {
        Tax seasonalTax2 = new Tax();
        seasonalTax2.setRoomTaxRate(taxRate);
        seasonalTax2.setStartDate(startDate);
        seasonalTax2.setEndDate(endDate);
        return seasonalTax2;
    }

    @Test
    public void saveGroupPricingBaseAccomTypes_TaxInclusiveSeasonWhenTaxbleSeasonsAreConfigured() {
        PricingBaseAccomType groupPricingDefault = PricingConfigurationObjectMother.buildGroupPricingBaseAccomTypeWithTax();
        LocalDate startDate = new LocalDate(2018, 10, 1);
        LocalDate endDate = new LocalDate(2018, 10, 1);
        PricingBaseAccomType groupPricingAccomTypeSeason1 = createSeason(startDate, endDate);
        PricingBaseAccomType groupPricingAccomTypeSeason2 = createSeason(startDate.plusDays(1), endDate.plusDays(1));
        PricingBaseAccomType groupPricingAccomTypeSeason3 = new GroupPricingBaseAccomType();
        groupPricingAccomTypeSeason3.setStartDate(startDate.plusDays(10));
        groupPricingAccomTypeSeason3.setEndDate(startDate.plusDays(13));

        Tax tax = createAndSetupTax();
        Tax seasonalTax = getTax(startDate.plusDays(1), startDate.plusDays(1), BigDecimal.valueOf(12));
        Map<LocalDate, Tax> taxesByDateRange = new HashMap<>();
        taxesByDateRange.put(startDate, tax);
        taxesByDateRange.put(startDate.plusDays(1), seasonalTax);
        taxesByDateRange.put(startDate.plusDays(10), seasonalTax);
        taxesByDateRange.put(startDate.plusDays(11), seasonalTax);
        taxesByDateRange.put(startDate.plusDays(12), seasonalTax);
        taxesByDateRange.put(startDate.plusDays(13), seasonalTax);
        when(taxService.findTaxesForDateRange(startDate, startDate.plusDays(13))).thenReturn(taxesByDateRange);
        setUpCPRelatedConfigParameters();

        service.applyTax(Arrays.asList(groupPricingDefault, groupPricingAccomTypeSeason1, groupPricingAccomTypeSeason2, groupPricingAccomTypeSeason3));

        assertEquals(tax.removeRoomTaxRate(groupPricingDefault.getMondayCeilingRateWithTax()), groupPricingDefault.getMondayCeilingRate());
        assertEquals(tax.removeRoomTaxRate(groupPricingDefault.getMondayFloorRateWithTax()), groupPricingDefault.getMondayFloorRate());
        assertEquals(seasonalTax.removeRoomTaxRate(groupPricingAccomTypeSeason2.getTuesdayCeilingRateWithTax()), groupPricingAccomTypeSeason2.getTuesdayCeilingRate());
        assertEquals(seasonalTax.removeRoomTaxRate(groupPricingAccomTypeSeason2.getTuesdayFloorRateWithTax()), groupPricingAccomTypeSeason2.getTuesdayFloorRate());
        assertEquals(BigDecimal.valueOf(92.59), groupPricingAccomTypeSeason1.getMondayCeilingRate().setScale(2, BigDecimal.ROUND_HALF_UP));
        assertEquals(BigDecimal.valueOf(9.26), groupPricingAccomTypeSeason1.getMondayFloorRate().setScale(2, BigDecimal.ROUND_HALF_UP));
        assertEquals(BigDecimal.valueOf(89.29), groupPricingAccomTypeSeason2.getTuesdayCeilingRate().setScale(2, BigDecimal.ROUND_HALF_UP));
        assertEquals(BigDecimal.valueOf(8.93), groupPricingAccomTypeSeason2.getTuesdayFloorRate().setScale(2, BigDecimal.ROUND_HALF_UP));
        assertFloorValuesAreNull(groupPricingAccomTypeSeason3);
        assertCeilingValuesAreNull(groupPricingAccomTypeSeason3);

        verify(taxService).findTaxesForDateRange(startDate, endDate.plusDays(13));
    }

    private PricingBaseAccomType createSeason(LocalDate startDate, LocalDate endDate) {
        PricingBaseAccomType groupPricingAccomTypeSeason = PricingConfigurationObjectMother.buildGroupPricingBaseAccomTypeSeason();
        AccomType accomType = new AccomType();
        accomType.setId(1);
        groupPricingAccomTypeSeason.setAccomType(accomType);
        groupPricingAccomTypeSeason.setStartDate(startDate);
        groupPricingAccomTypeSeason.setEndDate(endDate);
        groupPricingAccomTypeSeason.setMondayFloorRateWithTax(BigDecimal.TEN);
        groupPricingAccomTypeSeason.setMondayCeilingRateWithTax(BigDecimal.valueOf(100));
        groupPricingAccomTypeSeason.setTuesdayFloorRateWithTax(BigDecimal.TEN);
        groupPricingAccomTypeSeason.setTuesdayCeilingRateWithTax(BigDecimal.valueOf(100));
        return groupPricingAccomTypeSeason;
    }

    @Test
    public void findDefaultOffsets() {
        CPConfigOffsetAccomType cpConfigOffsetAccomType = PricingConfigurationObjectMother.buildCPConfigOffsetAccomTypeWithTax();
        when(tenantCrudService.findByNamedQuery(com.ideas.tetris.pacman.services.bestavailablerate.entity.CPConfigOffsetAccomType.FIND_DEFAULT_OFFSETS)).thenReturn(Collections.singletonList(cpConfigOffsetAccomType));

        List<CPConfigOffsetAccomType> defaultOffsets = service.findDefaultOffsets();

        assertEquals(cpConfigOffsetAccomType, defaultOffsets.get(0));
        verify(tenantCrudService).findByNamedQuery(com.ideas.tetris.pacman.services.bestavailablerate.entity.CPConfigOffsetAccomType.FIND_DEFAULT_OFFSETS);
    }

    @Test
    public void getNonBasePricingAccomTypeNonPriceExcludeAndFixedOffset() {
        AccomClass accomClass = new AccomClass();
        accomClass.setId(1);
        AccomType baseAccomType = getAccomType(accomClass, 1);
        AccomType nonBaseAccomType = getAccomType(accomClass, 2);
        accomClass.setAccomTypes(new HashSet<>(Arrays.asList(baseAccomType, nonBaseAccomType)));
        PricingAccomClass pricingAccomClass = getPricingAccomClass(accomClass, false);
        pricingAccomClass.setAccomType(baseAccomType);
        PricingBaseAccomType pricingBaseAccomType = setUpData(baseAccomType, nonBaseAccomType, OffsetMethod.FIXED_OFFSET);

        List<PricingBaseAccomType> pricingNonBaseAccomTypes = service.getPricingNonBaseAccomTypesUsing(Collections.singletonList(pricingBaseAccomType), Collections.singletonList(nonBaseAccomType));

        assertEquals(1, pricingNonBaseAccomTypes.size());
        PricingBaseAccomType firstNonBaseAccomType = pricingNonBaseAccomTypes.get(0);
        assertEquals(BigDecimal.valueOf(134).setScale(2, RoundingMode.HALF_UP), firstNonBaseAccomType.getSundayFloorRateWithTax().setScale(2, RoundingMode.HALF_UP));
        assertEquals(BigDecimal.valueOf(254).setScale(2, RoundingMode.HALF_UP), firstNonBaseAccomType.getSundayCeilingRateWithTax().setScale(2, RoundingMode.HALF_UP));
        assertEquals(BigDecimal.valueOf(140).setScale(2, RoundingMode.HALF_UP), firstNonBaseAccomType.getMondayFloorRateWithTax().setScale(2, RoundingMode.HALF_UP));
        assertEquals(BigDecimal.valueOf(300).setScale(2, RoundingMode.HALF_UP), firstNonBaseAccomType.getMondayCeilingRateWithTax().setScale(2, RoundingMode.HALF_UP));
        assertEquals(BigDecimal.valueOf(125).setScale(2, RoundingMode.HALF_UP), firstNonBaseAccomType.getSaturdayFloorRateWithTax().setScale(2, RoundingMode.HALF_UP));
        assertEquals(BigDecimal.valueOf(265).setScale(2, RoundingMode.HALF_UP), firstNonBaseAccomType.getSaturdayCeilingRateWithTax().setScale(2, RoundingMode.HALF_UP));
    }

    private AccomType getAccomType(AccomClass accomClass, int id) {
        AccomType nonBaseAccomType = new AccomType();
        nonBaseAccomType.setId(id);
        nonBaseAccomType.setAccomClass(accomClass);
        return nonBaseAccomType;
    }

    private PricingBaseAccomType setUpData(AccomType baseAccomType, AccomType nonBaseAccomType, OffsetMethod offsetMethod) {
        PricingBaseAccomType pricingBaseAccomType = PricingConfigurationObjectMother.buildPricingBaseAccomTypeWithTax();
        pricingBaseAccomType.setAccomType(baseAccomType);
        CPConfigOffsetAccomType cpConfigOffsetAccomType = PricingConfigurationObjectMother.buildCPConfigOffsetAccomTypeWithTax();
        cpConfigOffsetAccomType.setAccomType(nonBaseAccomType);
        cpConfigOffsetAccomType.setOffsetMethod(offsetMethod);
        AccomTypeSupplement baseRTAccomTypeSupplement = PricingConfigurationObjectMother.buildCPConfigSupplementAccomType(1);
        baseRTAccomTypeSupplement.setSundaySupplementValue(BigDecimal.ONE);
        baseRTAccomTypeSupplement.setAccomType(baseAccomType);
        AccomTypeSupplement nonBaseRTAccomTypeSupplement = PricingConfigurationObjectMother.buildCPConfigSupplementAccomType(1);
        nonBaseRTAccomTypeSupplement.setSundaySupplementValue(BigDecimal.TEN);
        nonBaseRTAccomTypeSupplement.setAccomType(nonBaseAccomType);
        when(accomTypeSupplementService.findDefaultSupplements()).thenReturn(Arrays.asList(baseRTAccomTypeSupplement, nonBaseRTAccomTypeSupplement));
        when(tenantCrudService.findByNamedQuery(CPConfigOffsetAccomType.FIND_DEFAULT_OFFSETS)).thenReturn(Collections.singletonList(cpConfigOffsetAccomType));
        when(tenantCrudService.findByNamedQuery(PricingAccomClass.FIND_BY_PRICE_EXCLUDED)).thenReturn(Collections.emptyList());
        when(configParamsService.getParameterValue(FeatureTogglesConfigParamName.PER_PERSON_PRICING_ENABLED)).thenReturn(false);
        return pricingBaseAccomType;
    }

    @Test
    public void getNonBasePricingAccomTypeNonPriceExcludeAndPercentageOffset() {
        AccomClass accomClass = new AccomClass();
        accomClass.setId(1);
        AccomType baseAccomType = getAccomType(accomClass, 1);
        AccomType nonBaseAccomType = getAccomType(accomClass, 2);
        accomClass.setAccomTypes(new HashSet<>(Arrays.asList(baseAccomType, nonBaseAccomType)));
        PricingAccomClass pricingAccomClass = getPricingAccomClass(accomClass, false);
        pricingAccomClass.setAccomType(baseAccomType);
        PricingBaseAccomType pricingBaseAccomType = setUpData(baseAccomType, nonBaseAccomType, OffsetMethod.PERCENTAGE);

        List<PricingBaseAccomType> pricingNonBaseAccomTypes = service.getPricingNonBaseAccomTypesUsing(Collections.singletonList(pricingBaseAccomType), Collections.singletonList(nonBaseAccomType));

        assertEquals(1, pricingNonBaseAccomTypes.size());
        PricingBaseAccomType firstNonBaseAccomType = pricingNonBaseAccomTypes.get(0);
        assertEquals(BigDecimal.valueOf(136.25).setScale(2, RoundingMode.HALF_UP), firstNonBaseAccomType.getSundayFloorRateWithTax().setScale(2, RoundingMode.HALF_UP));
        assertEquals(BigDecimal.valueOf(286.25).setScale(2, RoundingMode.HALF_UP), firstNonBaseAccomType.getSundayCeilingRateWithTax().setScale(2, RoundingMode.HALF_UP));
        assertEquals(BigDecimal.valueOf(143.00).setScale(2, RoundingMode.HALF_UP), firstNonBaseAccomType.getMondayFloorRateWithTax().setScale(2, RoundingMode.HALF_UP));
        assertEquals(BigDecimal.valueOf(351.00).setScale(2, RoundingMode.HALF_UP), firstNonBaseAccomType.getMondayCeilingRateWithTax().setScale(2, RoundingMode.HALF_UP));
        assertEquals(BigDecimal.valueOf(126.00).setScale(2, RoundingMode.HALF_UP), firstNonBaseAccomType.getSaturdayFloorRateWithTax().setScale(2, RoundingMode.HALF_UP));
        assertEquals(BigDecimal.valueOf(294.00).setScale(2, RoundingMode.HALF_UP), firstNonBaseAccomType.getSaturdayCeilingRateWithTax().setScale(2, RoundingMode.HALF_UP));
    }

    @Test
    public void getNonBasePricingAccomTypePriceExcludeRC() {
        AccomClass accomClass = new AccomClass();
        accomClass.setId(1);
        AccomType baseAccomType = getAccomType(accomClass, 1);
        AccomType nonBaseAccomType = getAccomType(accomClass, 2);
        accomClass.setAccomTypes(new HashSet<>(Arrays.asList(baseAccomType, nonBaseAccomType)));
        PricingAccomClass pricingAccomClass = getPricingAccomClass(accomClass, true);
        pricingAccomClass.setAccomType(baseAccomType);
        PricingBaseAccomType pricingBaseAccomType = setUpData(baseAccomType, nonBaseAccomType, OffsetMethod.FIXED_PRICE);
        when(tenantCrudService.findByNamedQuery(PricingAccomClass.FIND_BY_PRICE_EXCLUDED)).thenReturn(Collections.singletonList(pricingAccomClass));

        List<PricingBaseAccomType> pricingNonBaseAccomTypes = service.getPricingNonBaseAccomTypesUsing(Collections.singletonList(pricingBaseAccomType), Collections.singletonList(nonBaseAccomType));

        assertEquals(1, pricingNonBaseAccomTypes.size());
        PricingBaseAccomType firstNonBaseAccomType = pricingNonBaseAccomTypes.get(0);
        assertEquals(BigDecimal.valueOf(25.00).setScale(2, RoundingMode.HALF_UP), firstNonBaseAccomType.getSundayFloorRateWithTax().setScale(2, RoundingMode.HALF_UP));
        assertEquals(BigDecimal.valueOf(25.00).setScale(2, RoundingMode.HALF_UP), firstNonBaseAccomType.getSundayCeilingRateWithTax().setScale(2, RoundingMode.HALF_UP));
        assertEquals(BigDecimal.valueOf(30.00).setScale(2, RoundingMode.HALF_UP), firstNonBaseAccomType.getMondayFloorRateWithTax().setScale(2, RoundingMode.HALF_UP));
        assertEquals(BigDecimal.valueOf(30.00).setScale(2, RoundingMode.HALF_UP), firstNonBaseAccomType.getMondayCeilingRateWithTax().setScale(2, RoundingMode.HALF_UP));
        assertEquals(BigDecimal.valueOf(20.00).setScale(2, RoundingMode.HALF_UP), firstNonBaseAccomType.getSaturdayFloorRateWithTax().setScale(2, RoundingMode.HALF_UP));
        assertEquals(BigDecimal.valueOf(20.00).setScale(2, RoundingMode.HALF_UP), firstNonBaseAccomType.getSaturdayCeilingRateWithTax().setScale(2, RoundingMode.HALF_UP));
    }

    @Test
    public void addOffsetValueToBaseCeilingFloor() {
        BigDecimal finalPrice = service.addOffsetValueTo(BigDecimal.valueOf(155), BigDecimal.TEN, OffsetMethod.PERCENTAGE);
        assertEquals(BigDecimal.valueOf(170.5).setScale(2, RoundingMode.HALF_UP), finalPrice.setScale(2, RoundingMode.HALF_UP));

        finalPrice = service.addOffsetValueTo(BigDecimal.valueOf(155), BigDecimal.TEN, OffsetMethod.FIXED_OFFSET);
        assertEquals(BigDecimal.valueOf(165).setScale(2, RoundingMode.HALF_UP), finalPrice.setScale(2, RoundingMode.HALF_UP));

        finalPrice = service.addOffsetValueTo(BigDecimal.valueOf(155), null, OffsetMethod.PERCENTAGE);
        assertEquals(BigDecimal.valueOf(155).setScale(2, RoundingMode.HALF_UP), finalPrice.setScale(2, RoundingMode.HALF_UP));

        finalPrice = service.addOffsetValueTo(BigDecimal.valueOf(155), null, OffsetMethod.FIXED_OFFSET);
        assertEquals(BigDecimal.valueOf(155).setScale(2, RoundingMode.HALF_UP), finalPrice.setScale(2, RoundingMode.HALF_UP));

        finalPrice = service.addOffsetValueTo(BigDecimal.valueOf(155), BigDecimal.ZERO, OffsetMethod.PERCENTAGE);
        assertEquals(BigDecimal.valueOf(155).setScale(2, RoundingMode.HALF_UP), finalPrice.setScale(2, RoundingMode.HALF_UP));

        finalPrice = service.addOffsetValueTo(BigDecimal.valueOf(155), BigDecimal.ZERO, OffsetMethod.FIXED_OFFSET);
        assertEquals(BigDecimal.valueOf(155).setScale(2, RoundingMode.HALF_UP), finalPrice.setScale(2, RoundingMode.HALF_UP));

        finalPrice = service.addOffsetValueTo(BigDecimal.ZERO, BigDecimal.TEN, OffsetMethod.PERCENTAGE);
        assertEquals(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP), finalPrice.setScale(2, RoundingMode.HALF_UP));

        finalPrice = service.addOffsetValueTo(BigDecimal.ZERO, BigDecimal.TEN, OffsetMethod.FIXED_OFFSET);
        assertEquals(BigDecimal.TEN.setScale(2, RoundingMode.HALF_UP), finalPrice.setScale(2, RoundingMode.HALF_UP));

        finalPrice = service.addOffsetValueTo(null, BigDecimal.TEN, OffsetMethod.PERCENTAGE);
        assertNull(finalPrice);

        finalPrice = service.addOffsetValueTo(null, BigDecimal.TEN, OffsetMethod.FIXED_OFFSET);
        assertNull(finalPrice);
    }

    @Test
    public void shouldConfigureCurrentConfigurationUsingDraftConfiguration() {
        createCeilingFloorDraftDefaultData();
        service.setTenantCrudService(tenantCrudService());
        service.configureCurrentConfigurationsUsingDraftValues(pricingConfigurationDTO);

        List<TransientPricingBaseAccomTypeDraft> draftValues = tenantCrudService().findAll(TransientPricingBaseAccomTypeDraft.class);
        List<TransientPricingBaseAccomType> currentConfigurations = tenantCrudService().findAll(TransientPricingBaseAccomType.class);

        assertEquals(0, draftValues.size());
        assertEquals(2, currentConfigurations.size());

        TransientPricingBaseAccomType transientPricingBaseAccomType1 = currentConfigurations.get(0);
        assertEquals("100.00", transientPricingBaseAccomType1.getSundayCeilingRate().setScale(2).toString());
        assertEquals("60.00", transientPricingBaseAccomType1.getSundayFloorRate().setScale(2).toString());

        assertEquals("109.00", transientPricingBaseAccomType1.getMondayFloorRateWithTax().setScale(2).toString());
        assertEquals("109.00", transientPricingBaseAccomType1.getMondayCeilingRateWithTax().setScale(2).toString());
        verify(pricingConfigurationLTBDEService).enabledLTBDEIfApplicable();
    }
    @Test
    public void shouldConfigureCurrentConfigurationUsingDraftConfiguration_forIndependentProduct() {
        createIndependentProduct();
        List<Product> products = tenantCrudService().findAll(Product.class);
        Optional<Product> lv2 = products.stream().filter(p -> p.getName().equals("LV2")).findFirst();
        createCeilingFloorDraftDefaultDataForIP(Integer.toString(lv2.get().getId()));
        service.setTenantCrudService(tenantCrudService());
        service.configureCurrentConfigurationsUsingDraftValues(getPricingConfigurationDTOForIP(lv2.get().getId()));

        List<TransientPricingBaseAccomTypeDraft> draftValues = tenantCrudService().findAll(TransientPricingBaseAccomTypeDraft.class);
        List<TransientPricingBaseAccomType> currentConfigurations = tenantCrudService().findAll(TransientPricingBaseAccomType.class);

        List<TransientPricingBaseAccomTypeDraft> draftValuesForIP = draftValues.stream().filter(draft -> draft.getProductID().equals(lv2.get().getId())).collect(Collectors.toList());
        List<TransientPricingBaseAccomType> currentValuesForIP = currentConfigurations.stream().filter(cc -> cc.getProductID().equals(lv2.get().getId())).collect(Collectors.toList());
        assertEquals(0, draftValuesForIP.size());
        assertEquals(2, currentValuesForIP.size());

        TransientPricingBaseAccomType transientPricingBaseAccomType1 = currentValuesForIP.get(0);
        assertEquals("100.00", transientPricingBaseAccomType1.getSundayCeilingRate().setScale(2).toString());
        assertEquals("60.00", transientPricingBaseAccomType1.getSundayFloorRate().setScale(2).toString());

        assertEquals("109.00", transientPricingBaseAccomType1.getMondayFloorRateWithTax().setScale(2).toString());
        assertEquals("109.00", transientPricingBaseAccomType1.getMondayCeilingRateWithTax().setScale(2).toString());
        verify(pricingConfigurationLTBDEService).enabledLTBDEIfApplicable();
    }

    @Test
    public void shouldConfigureCurrentConfigurationWithPriceExcludedForIndependentProduct() {
        createIndependentProduct();
        List<Product> products = tenantCrudService().findAll(Product.class);
        Optional<Product> lv2 = products.stream().filter(p -> p.getName().equals("LV2")).findFirst();
        createCeilingFloorDraftDefaultDataForIP(lv2.get().getId().toString());
        updatePriceExcluded(lv2.get().getId().toString());
        service.setTenantCrudService(tenantCrudService());
        service.configureCurrentConfigurationsUsingDraftValues(getPricingConfigurationDTOForIP(lv2.get().getId()));


        List<TransientPricingBaseAccomTypeDraft> draftValues = tenantCrudService().findAll(TransientPricingBaseAccomTypeDraft.class)
                .stream().filter(p->p.getProductID().equals(lv2.get().getId())).collect(Collectors.toList());
        List<TransientPricingBaseAccomType> currentConfigurations = tenantCrudService().findAll(TransientPricingBaseAccomType.class)
                .stream().filter(p->p.getProductID().equals(lv2.get().getId())).collect(Collectors.toList());

        assertEquals(0, draftValues.size());
        assertEquals(3, currentConfigurations.size());

        TransientPricingBaseAccomType transientPricingBaseAccomType_priceExcluded = currentConfigurations.get(0);
        assertEquals("100.00", transientPricingBaseAccomType_priceExcluded.getSundayCeilingRate().toString());

        TransientPricingBaseAccomType transientPricingBaseAccomType1 = currentConfigurations.get(2);
        assertEquals("100.00", transientPricingBaseAccomType1.getSundayCeilingRate().toString());
        assertEquals("100.00", transientPricingBaseAccomType1.getSundayFloorRate().toString());

        assertEquals("109.00", transientPricingBaseAccomType1.getMondayFloorRateWithTax().toString());
        assertEquals("109.00", transientPricingBaseAccomType1.getMondayCeilingRateWithTax().toString());
    }

    public int createIndependentProduct() {
        return tenantCrudService().executeUpdateByNativeQuery("INSERT INTO Product (Name, System_Default, Created_By_User_ID, Created_DTTM, Last_Updated_By_User_ID," +
                " Last_Updated_DTTM, Code, Type, Dependent_Product_ID, Description, Min_DTA, Max_DTA, Min_LOS, MAX_LOS, Is_Offset_For_Extra_Adult, Is_Offset_For_Extra_Child," +
                " Is_DOW_Offset, Is_RC_Offset, Is_DTA_Offset, Is_Upload, Status_ID, Is_Default_Inactive, Invalid_Reason_ID, Is_Optimized, Product_Floor_Rate, " +
                "Price_Rounding_Rule, Is_Publically_Available, Minimum_Price_Change, Offset_Method, Display_Order, Is_Fixed_Above_Bar, Send_Adjustment, Centrally_Managed, " +
                "Is_Overridable, Floor_Type, Floor_Percentage, Rate_Shopping_LOS_Min, Rate_Shopping_LOS_Max, Child_Pricing_Type, Product_Code_ID, Min_Rooms, Max_Rooms, " +
                "Is_Use_In_SG_Evaluation, Free_Nights_Enabled, Free_Upgrade_Enabled, Is_Largest_SG_Product) VALUES ('LV2', 0, 1, '2022-08-01 04:49:10.197', 11403, " +
                "'2024-12-18 19:45:01.733', 'INDEPENDENT', 'INDEPENDENTLY', NULL, 'Weekly/LV2 and Linked Products', 0, NULL, 7, 14, 0, 0, 0, 0, 0, 1, 1, 0, NULL, 1, NULL," +
                " 1, 1, NULL, NULL, 3, 0, 0, 0, 1, 2, NULL, 7, 14, 1, 1, -1, -1, 0, 0, 0, 0)");
    }

    private PricingConfigurationDTO getPricingConfigurationDTOForIP(int id) {
        IndependentProductConfigurationDTO independentProductConfigurationDTO = new IndependentProductConfigurationDTO();
        Product product = new Product();
        product.setId(id);
        independentProductConfigurationDTO.setProduct(product);
        return new PricingConfigurationDTO(independentProductConfigurationDTO,false);
  }


    @Test
    public void copyTransientAccomTypeToGroupPricingAccomTypeByconfigureCurrentConfigurationsUsingDraftValues() {
        createCeilingFloorDraftDefaultData();
        service.setTenantCrudService(tenantCrudService());
        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.SYNC_GP_CEILING_FLOOR_WITH_PRIMARY_PRICED_PRODUCT))
                .thenReturn(true);
        service.configureCurrentConfigurationsUsingDraftValues(pricingConfigurationDTO);

        List<TransientPricingBaseAccomTypeDraft> draftValues = tenantCrudService().findAll(TransientPricingBaseAccomTypeDraft.class);
        List<TransientPricingBaseAccomType> currentConfigurations = tenantCrudService().findAll(TransientPricingBaseAccomType.class);
        List<GroupPricingBaseAccomType> groupPricingConfigurations = tenantCrudService().findAll(GroupPricingBaseAccomType.class);


        assertEquals(0, draftValues.size());
        assertEquals(2, currentConfigurations.size());
        assertEquals(2, groupPricingConfigurations.size());

        TransientPricingBaseAccomType transientPricingBaseAccomType1 = currentConfigurations.get(0);
        GroupPricingBaseAccomType groupPricingBaseAccomType = groupPricingConfigurations.get(0);

        assertEquals(transientPricingBaseAccomType1.getSundayCeilingRate().setScale(2).toString(),
                groupPricingBaseAccomType.getSundayCeilingRate().setScale(2).toString());
        assertEquals(transientPricingBaseAccomType1.getSundayFloorRate().setScale(2).toString(),
                groupPricingBaseAccomType.getSundayFloorRate().setScale(2).toString());

        assertEquals(transientPricingBaseAccomType1.getMondayFloorRateWithTax().setScale(2).toString(),
                groupPricingBaseAccomType.getMondayFloorRateWithTax().setScale(2).toString());
        assertEquals(transientPricingBaseAccomType1.getMondayCeilingRateWithTax().setScale(2).toString(),
                groupPricingBaseAccomType.getMondayCeilingRateWithTax().setScale(2).toString());
    }

    @Test
    public void shouldConfigureCurrentConfigurationWithPriceExcluded() {
        createCeilingFloorDraftDefaultData();
        updatePriceExcluded("1");
        service.setTenantCrudService(tenantCrudService());
        service.configureCurrentConfigurationsUsingDraftValues(pricingConfigurationDTO);


        List<TransientPricingBaseAccomTypeDraft> draftValues = tenantCrudService().findAll(TransientPricingBaseAccomTypeDraft.class);
        List<TransientPricingBaseAccomType> currentConfigurations = tenantCrudService().findAll(TransientPricingBaseAccomType.class);

        assertEquals(0, draftValues.size());
        assertEquals(3, currentConfigurations.size());

        TransientPricingBaseAccomType transientPricingBaseAccomType_priceExcluded = currentConfigurations.get(0);
        assertEquals("100.00", transientPricingBaseAccomType_priceExcluded.getSundayCeilingRate().setScale(2).toString());

        TransientPricingBaseAccomType transientPricingBaseAccomType1 = currentConfigurations.get(2);
        assertEquals("100.00", transientPricingBaseAccomType1.getSundayCeilingRate().setScale(2).toString());
        assertEquals("100.00", transientPricingBaseAccomType1.getSundayFloorRate().setScale(2).toString());

        assertEquals("109.00", transientPricingBaseAccomType1.getMondayFloorRateWithTax().setScale(2).toString());
        assertEquals("109.00", transientPricingBaseAccomType1.getMondayCeilingRateWithTax().setScale(2).toString());
    }

    @Test
    public void shouldFetchFloorCeilingDraftDefaultData() {
        createCeilingFloorDraftDefaultData();
        createCeilingFloorDraftSeasonalData();
        service.setTenantCrudService(tenantCrudService());
        List<PricingBaseAccomType> defaultsInDraft = service.getTransientPricingBaseAccomTypeDefaultsInDraft(1);
        assertEquals(2, defaultsInDraft.size());
        assertEquals(BigDecimal.valueOf(100.00).setScale(2), defaultsInDraft.get(0).getSundayCeilingRate());
        assertEquals(BigDecimal.valueOf(60.00).setScale(2), defaultsInDraft.get(0).getSundayFloorRate());

    }

    @Test
    public void shouldFetchFloorCeilingSeosonalData() {
        createCeilingFloorDraftDefaultData();
        createCeilingFloorDraftSeasonalData();
        service.setTenantCrudService(tenantCrudService());
        List<PricingBaseAccomType> seasonsInDraft = service.getTransientPricingBaseAccomTypeSeasonsInDraft(1);
        assertEquals(2, seasonsInDraft.size());
        assertEquals(BigDecimal.valueOf(120.00).setScale(2), seasonsInDraft.get(0).getSundayCeilingRate());
        assertEquals(BigDecimal.valueOf(80.00).setScale(2), seasonsInDraft.get(0).getSundayFloorRate());

    }

    @Test
    public void shouldTestIfDraftDataAvailable() {
        service.setTenantCrudService(tenantCrudService());
        assertFalse(service.isTransientFloorCeilingDraftAvailable());
        createCeilingFloorDraftDefaultData();
        assertTrue(service.isTransientFloorCeilingDraftAvailable());
    }

    @Test
    public void shouldTestIfDraftDataAvailableForAProduct() {
        service.setTenantCrudService(tenantCrudService());
        assertFalse(service.isTransientFloorCeilingDraftAvailableByProduct(1));
        createCeilingFloorDraftDefaultData();
        assertTrue(service.isTransientFloorCeilingDraftAvailableByProduct(1));
        assertFalse(service.isTransientFloorCeilingDraftAvailableByProduct(6));
    }

    @Test
    public void shouldDeleteDraftValues() {
        service.setTenantCrudService(tenantCrudService());
        createCeilingFloorDraftDefaultData();
        createCeilingFloorDraftSeasonalData();
        service.deleteDraftValuesByProduct(1);
        int count = tenantCrudService().findByNativeQuerySingleResult("select count(*) from CP_Cfg_Base_AT_Draft", new HashMap<>());
        assertEquals(0, count);
    }

    @Test
    public void shouldCreateEmptyEntryOfPricingSeasonDraft() {
        service.setTenantCrudService(tenantCrudService());
        PricingAccomClass pricingAccomClass = new PricingAccomClass();
        AccomType accomType = new AccomType();
        accomType.setName("test");
        pricingAccomClass.setAccomType(accomType);
        pricingAccomClass.setPropertyId(5);


        PricingBaseAccomType pricingBaseAccomType = new TransientPricingBaseAccomTypeDraft();
        pricingBaseAccomType.setProductID(1);
        pricingBaseAccomType.setSeasonName("season1");
        pricingBaseAccomType.setStartDate(LocalDate.now());
        pricingBaseAccomType.setEndDate(LocalDate.now());

        PricingBaseAccomType newPricingSeasonDraft = service.saveEmptyPricingBaseAccomTypeDraft(pricingAccomClass, pricingBaseAccomType);
        assertNotNull(newPricingSeasonDraft);
        assertEquals("test", newPricingSeasonDraft.getAccomType().getName());
    }

    @Test
    public void shouldFetchFutureOnlySeasons() {
        service.setTenantCrudService(tenantCrudService());
        createCeilingFloorDraftSeasonalData();
        when(dateService.getCaughtUpDate()).thenReturn(LocalDate.parse("2020-06-01").toDate());
        List<PricingBaseAccomType> futureSeasonsInDraft = service.getFutureSeasonsInDraft(pricingConfigurationDTO.getProduct().getId());
        assertEquals(1, futureSeasonsInDraft.size());
        assertEquals(LocalDate.parse("2020-06-01"), futureSeasonsInDraft.get(0).getStartDate());
        assertEquals(LocalDate.parse("2020-09-01"), futureSeasonsInDraft.get(0).getEndDate());
    }

    @Test
    public void shouldFetchFutureOnlyTransientSeasons() {
        service.setTenantCrudService(tenantCrudService());
        createCeilingFloorTransientSeasonalData();
        when(dateService.getCaughtUpDate()).thenReturn(LocalDate.parse("2020-06-01").toDate());
        Product product = new Product();
        product.setId(1);
        List<PricingBaseAccomType> futureSeasons = service.getFutureTransientPricingAccomTypes(product);
        assertEquals(1, futureSeasons.size());
        assertEquals(LocalDate.parse("2020-06-01"), futureSeasons.get(0).getStartDate());
        assertEquals(LocalDate.parse("2020-09-01"), futureSeasons.get(0).getEndDate());
    }

    @Test
    public void shouldFetchSeasonsPartiallyInFuture() {
        service.setTenantCrudService(tenantCrudService());
        createCeilingFloorTransientSeasonalData();
        when(dateService.getCaughtUpDate()).thenReturn(LocalDate.parse("2020-02-02").toDate());
        Product product = new Product();
        product.setId(1);
        List<PricingBaseAccomType> futureSeasons = service.getFutureTransientPricingAccomTypes(product);
        assertEquals(2, futureSeasons.size());
        assertEquals(LocalDate.parse("2020-01-01"), futureSeasons.get(0).getStartDate());
        assertEquals(LocalDate.parse("2020-05-01"), futureSeasons.get(0).getEndDate());

        assertEquals(LocalDate.parse("2020-06-01"), futureSeasons.get(1).getStartDate());
        assertEquals(LocalDate.parse("2020-09-01"), futureSeasons.get(1).getEndDate());
    }

    @Test
    public void shouldDeleteDraftValuesForAccomClass() {
        service.setTenantCrudService(tenantCrudService());
        createCeilingFloorDraftDefaultData();
        createCeilingFloorDraftSeasonalData();
        List<TransientPricingBaseAccomTypeDraft> drafts = tenantCrudService().findAll(TransientPricingBaseAccomTypeDraft.class);
        assertEquals(4, drafts.size());
        service.deleteTransientPricingBaseAccomTypeDraft(2);
        drafts = tenantCrudService().findAll(TransientPricingBaseAccomTypeDraft.class);
        assertEquals(2, drafts.size());
        assertEquals(4, drafts.get(0).getAccomType().getAccomClass().getId());
        assertEquals(4, drafts.get(1).getAccomType().getAccomClass().getId());
    }

    @Test
    public void shouldDeleteAllPricingAccomClasses() {
        List<PricingAccomClass> pricingAccomClasses = tenantCrudService().findByNamedQuery(PricingAccomClass.FIND_BY_PROPERTY_ID, QueryParameter.with("propertyId", 5).parameters());
        assertTrue(pricingAccomClasses.size() > 0);
        service.setTenantCrudService(tenantCrudService());
        service.deleteAllPricincAccomClasses();
        List<PricingAccomClass> pricingAccomClassesAfterDeletion = tenantCrudService().findByNamedQuery(PricingAccomClass.FIND_BY_PROPERTY_ID, QueryParameter.with("propertyId", 5).parameters());
        assertTrue(pricingAccomClassesAfterDeletion.size() == 0);
    }

    @Test
    public void shouldFindIfAnyPricingAccomClassConfigured() {
        service.setTenantCrudService(tenantCrudService());
        assertTrue(service.isAnyPricingAccomClassConfigured());
    }

    @Test
    public void testAreDatesMatching() {
        assertTrue(service.areDatesMatching(null, null));
        LocalDate startDate = new LocalDate();
        LocalDate tomorrow = new LocalDate().plusDays(1);
        assertTrue(service.areDatesMatching(startDate, startDate));
        assertFalse(service.areDatesMatching(startDate, tomorrow));
        assertFalse(service.areDatesMatching(tomorrow, startDate));
    }

    @Test
    public void testHandleMigrationForPerPersonPricing() {
        Tax tax = new Tax();
        tax.setRoomTaxRate(BigDecimal.valueOf(25));
        when(taxService.findTax()).thenReturn(tax);

        AccomType accomType = new AccomType();
        CPConfigOffsetAccomType offset = new CPConfigOffsetAccomType();
        offset.setProductID(1);
        offset.setSundayOffsetValueWithTax(BigDecimal.TEN);
        offset.setSundayOffsetValue(BigDecimal.valueOf(8));
        offset.setAccomType(accomType);
        offset.setOccupancyType(OccupancyType.DOUBLE);
        offset.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        when(tenantCrudService.findAll(CPConfigOffsetAccomType.class)).thenReturn(new ArrayList<>(Arrays.asList(offset)));
        Map<Integer, PricingRule> pricingRules = new HashMap<>();
        pricingRules.put(1, new PricingRule());
        when(prettyPricingService.getPricingRules()).thenReturn(pricingRules);

        TransientPricingBaseAccomType tpbat = new TransientPricingBaseAccomType();
        tpbat.setProductID(1);
        tpbat.setSundayFloorRateWithTax(BigDecimal.valueOf(90));
        tpbat.setSundayFloorRate(BigDecimal.valueOf(72));
        tpbat.setAccomType(accomType);
        when(tenantCrudService.findAll(TransientPricingBaseAccomType.class)).thenReturn(new ArrayList<>(Arrays.asList(tpbat)));
        service.handleMigrationForPerPersonPricing();

        assertEquals(BigDecimal.valueOf(100).setScale(2), tpbat.getSundayFloorRateWithTax());
        assertEquals(BigDecimal.valueOf(80).setScale(5), tpbat.getSundayFloorRate());

        assertEquals(OccupancyType.DOUBLE, offset.getOccupancyType());
        assertEquals(BigDecimal.valueOf(10), offset.getSundayOffsetValueWithTax());
        assertEquals(BigDecimal.valueOf(8), offset.getSundayOffsetValue());
    }

    @Test
    public void testAdjustDefaultFloorAndCeilingValues() {
        Tax tax = new Tax();
        tax.setRoomTaxRate(BigDecimal.valueOf(25));

        AccomType accomType = new AccomType();
        CPConfigOffsetAccomType offset = new CPConfigOffsetAccomType();
        offset.setProductID(1);
        offset.setSundayOffsetValueWithTax(BigDecimal.TEN);
        offset.setSundayOffsetValue(BigDecimal.valueOf(8));
        offset.setAccomType(accomType);
        offset.setOccupancyType(OccupancyType.DOUBLE);

        TransientPricingBaseAccomType tpbat = new TransientPricingBaseAccomType();
        tpbat.setProductID(1);
        tpbat.setSundayFloorRateWithTax(BigDecimal.valueOf(90));
        tpbat.setSundayFloorRate(BigDecimal.valueOf(72));
        tpbat.setAccomType(accomType);

        Map<Integer, PricingRule> pricingRules = new HashMap<>();
        pricingRules.put(1, new PricingRule());

        service.adjustDefaultFloorAndCeilingValues(tax, Arrays.asList(offset), Arrays.asList(tpbat), pricingRules);

        assertEquals(BigDecimal.valueOf(100).setScale(2), tpbat.getSundayFloorRateWithTax());
        assertEquals(BigDecimal.valueOf(80).setScale(5), tpbat.getSundayFloorRate());
    }

    @Test
    public void testUpdateNewPPPRatesForDOW() {
        Tax tax = new Tax();
        tax.setRoomTaxRate(BigDecimal.valueOf(25));

        CPConfigOffsetAccomType offset = new CPConfigOffsetAccomType();
        offset.setSundayOffsetValueWithTax(BigDecimal.TEN);
        offset.setSundayOffsetValue(BigDecimal.valueOf(8));

        TransientPricingBaseAccomType tpbat = new TransientPricingBaseAccomType();
        tpbat.setSundayFloorRateWithTax(BigDecimal.valueOf(90));
        tpbat.setSundayFloorRate(BigDecimal.valueOf(72));
        service.updateNewPPPRatesForDOW(tax, tpbat, offset, new PricingRule());

        assertEquals(BigDecimal.valueOf(100).setScale(2), tpbat.getSundayFloorRateWithTax());
        assertEquals(BigDecimal.valueOf(80).setScale(5), tpbat.getSundayFloorRate());
    }

    @Test
    public void testUpdateNewPPPRatesForDOW_NullRoundingRules() {
        Tax tax = new Tax();
        tax.setRoomTaxRate(BigDecimal.valueOf(25));

        CPConfigOffsetAccomType offset = new CPConfigOffsetAccomType();
        offset.setSundayOffsetValueWithTax(BigDecimal.TEN);
        offset.setSundayOffsetValue(BigDecimal.valueOf(8));

        TransientPricingBaseAccomType tpbat = new TransientPricingBaseAccomType();
        tpbat.setSundayFloorRateWithTax(BigDecimal.valueOf(90));
        tpbat.setSundayFloorRate(BigDecimal.valueOf(72));
        service.updateNewPPPRatesForDOW(tax, tpbat, offset, null);

        assertEquals(BigDecimal.valueOf(100).setScale(2), tpbat.getSundayFloorRateWithTax());
        assertEquals(BigDecimal.valueOf(80).setScale(5), tpbat.getSundayFloorRate());
    }

    @Test
    public void testCalculateNewPPPRate() {
        Tax tax = new Tax();
        tax.setRoomTaxRate(BigDecimal.valueOf(25));

        TransientPricingBaseAccomType tpbat = new TransientPricingBaseAccomType();
        service.calculateNewPPPRate(tax, BigDecimal.valueOf(9), BigDecimal.ONE, OffsetMethod.FIXED_OFFSET, tpbat, TransientPricingBaseAccomType::setSundayFloorRate, TransientPricingBaseAccomType::setSundayFloorRateWithTax, new PricingRule());

        assertEquals(BigDecimal.valueOf(10).setScale(2), tpbat.getSundayFloorRateWithTax());
        assertEquals(BigDecimal.valueOf(8).setScale(5), tpbat.getSundayFloorRate());
    }

    @Test
    public void testCalculateNewPPPRate_NullRoundingRule() {
        Tax tax = new Tax();
        tax.setRoomTaxRate(BigDecimal.valueOf(25));

        TransientPricingBaseAccomType tpbat = new TransientPricingBaseAccomType();
        service.calculateNewPPPRate(tax, BigDecimal.valueOf(9), BigDecimal.ONE, OffsetMethod.FIXED_OFFSET, tpbat, TransientPricingBaseAccomType::setSundayFloorRate, TransientPricingBaseAccomType::setSundayFloorRateWithTax, null);

        assertEquals(BigDecimal.valueOf(10).setScale(2), tpbat.getSundayFloorRateWithTax());
        assertEquals(BigDecimal.valueOf(8).setScale(5), tpbat.getSundayFloorRate());
    }

    @Test
    public void testCalculateNewPPPRatePercentage() {
        Tax tax = new Tax();
        tax.setRoomTaxRate(BigDecimal.valueOf(20));

        TransientPricingBaseAccomType tpbat = new TransientPricingBaseAccomType();
        service.calculateNewPPPRate(tax, BigDecimal.valueOf(10), BigDecimal.valueOf(20), OffsetMethod.PERCENTAGE, tpbat, TransientPricingBaseAccomType::setSundayFloorRate, TransientPricingBaseAccomType::setSundayFloorRateWithTax, new PricingRule());

        assertEquals(BigDecimal.valueOf(12).setScale(2), tpbat.getSundayFloorRateWithTax());
        assertEquals(BigDecimal.valueOf(10).setScale(5), tpbat.getSundayFloorRate());
    }

    @Test
    public void testAdjustDefaultAndSeasonOffsetsBaseRoomType() {
        AccomType accomType = new AccomType();
        when(tenantCrudService.findByNamedQuery(PricingAccomClass.DISTINCT_BASE_ROOM_TYPES_BY_PROPERTY_ID,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters()))
                .thenReturn(Arrays.asList(accomType));

        Tax tax = new Tax();
        tax.setRoomTaxRate(BigDecimal.valueOf(25));

        CPConfigOffsetAccomType offset = new CPConfigOffsetAccomType();
        offset.setProductID(1);
        offset.setStatusId(Status.ACTIVE.getId());
        offset.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        offset.setOccupancyType(OccupancyType.DOUBLE);
        offset.setSundayOffsetValueWithTax(BigDecimal.TEN);
        offset.setSundayOffsetValue(BigDecimal.valueOf(8));
        offset.setAccomType(accomType);

        List<CPConfigOffsetAccomType> offsetList = new ArrayList<>(Arrays.asList(offset));

        Map<Integer, PricingRule> pricingRules = new HashMap<>();
        pricingRules.put(1, new PricingRule());

        service.adjustDefaultAndSeasonOffsets(tax, offsetList, pricingRules);

        assertEquals(OccupancyType.SINGLE, offset.getOccupancyType());
        assertEquals(BigDecimal.valueOf(-10), offset.getSundayOffsetValueWithTax());
        assertEquals(BigDecimal.valueOf(-8), offset.getSundayOffsetValue());
        assertNull(offset.getMondayOffsetValueWithTax());
        assertNull(offset.getMondayOffsetValue());
    }

    @Test
    void shouldNotSaveOffsetForDoubleOccupancyForBaseRoomType() {
        AccomClass accomClass = new AccomClass();
        accomClass.setCode("STANDARD");
        AccomType accomType = new AccomType();
        accomType.setId(10);
        accomType.setAccomClass(accomClass);
        accomType.setName("STD");

        when(tenantCrudService.findByNamedQuery(PricingAccomClass.DISTINCT_BASE_ROOM_TYPES_BY_PROPERTY_ID,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters()))
                .thenReturn(Arrays.asList(accomType));

        Tax tax = new Tax();
        tax.setRoomTaxRate(BigDecimal.valueOf(25));

        CPConfigOffsetAccomType offset = new CPConfigOffsetAccomType();
        offset.setProductID(1);
        offset.setStatusId(Status.ACTIVE.getId());
        offset.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        offset.setOccupancyType(OccupancyType.DOUBLE);
        offset.setSundayOffsetValueWithTax(BigDecimal.TEN);
        offset.setSundayOffsetValue(BigDecimal.valueOf(8));
        offset.setAccomType(accomType);

        List<CPConfigOffsetAccomType> offsetList = new ArrayList<>(Arrays.asList(offset));

        Map<Integer, PricingRule> pricingRules = new HashMap<>();
        pricingRules.put(1, new PricingRule());

        service.adjustDefaultAndSeasonOffsets(tax, offsetList, pricingRules);

        assertEquals(OccupancyType.SINGLE, offset.getOccupancyType());
        assertEquals(BigDecimal.valueOf(-10), offset.getSundayOffsetValueWithTax());
        assertEquals(BigDecimal.valueOf(-8), offset.getSundayOffsetValue());
        assertNull(offset.getMondayOffsetValueWithTax());
        assertNull(offset.getMondayOffsetValue());
        assertEquals(1, offsetList.size());
    }

    @Test
    public void testAdjustOffsetRecords() {
        CPConfigOffsetAccomType singleOffset = new CPConfigOffsetAccomType();
        singleOffset.setSundayOffsetValueWithTax(BigDecimal.ONE);
        singleOffset.setOffsetMethod(OffsetMethod.FIXED_OFFSET);

        CPConfigOffsetAccomType doubleOffset = new CPConfigOffsetAccomType();
        doubleOffset.setSundayOffsetValueWithTax(BigDecimal.TEN);
        doubleOffset.setOffsetMethod(OffsetMethod.FIXED_OFFSET);

        Tax tax = new Tax();
        tax.setRoomTaxRate(BigDecimal.valueOf(25));

        singleOffset.setSundayOffsetValue(tax.removeRoomTaxRate(BigDecimal.ONE));
        doubleOffset.setSundayOffsetValue(tax.removeRoomTaxRate(BigDecimal.TEN));

        service.adjustOffsetRecords(doubleOffset, singleOffset, false, tax, new PricingRule());

        assertEquals(BigDecimal.TEN, singleOffset.getSundayOffsetValueWithTax().negate());
        assertEquals(tax.removeRoomTaxRate(BigDecimal.TEN), singleOffset.getSundayOffsetValue().negate());

        assertEquals(BigDecimal.TEN, doubleOffset.getSundayOffsetValueWithTax());
        assertEquals(tax.removeRoomTaxRate(BigDecimal.TEN), doubleOffset.getSundayOffsetValue());

        singleOffset.setSundayOffsetValueWithTax(BigDecimal.ONE);
        doubleOffset.setSundayOffsetValueWithTax(BigDecimal.TEN);
        service.adjustOffsetRecords(doubleOffset, singleOffset, true, tax, new PricingRule());

        assertEquals(OccupancyType.SINGLE, doubleOffset.getOccupancyType());
        assertEquals(BigDecimal.TEN.negate(), doubleOffset.getSundayOffsetValueWithTax());
    }

    @Test
    public void testAdjustOffsetRecords_NullRoundingRules() {
        CPConfigOffsetAccomType singleOffset = new CPConfigOffsetAccomType();
        singleOffset.setSundayOffsetValueWithTax(BigDecimal.ONE);
        singleOffset.setOffsetMethod(OffsetMethod.FIXED_OFFSET);

        CPConfigOffsetAccomType doubleOffset = new CPConfigOffsetAccomType();
        doubleOffset.setSundayOffsetValueWithTax(BigDecimal.TEN);
        doubleOffset.setOffsetMethod(OffsetMethod.FIXED_OFFSET);

        Tax tax = new Tax();
        tax.setRoomTaxRate(BigDecimal.valueOf(25));

        singleOffset.setSundayOffsetValue(tax.removeRoomTaxRate(BigDecimal.ONE));
        doubleOffset.setSundayOffsetValue(tax.removeRoomTaxRate(BigDecimal.TEN));

        service.adjustOffsetRecords(doubleOffset, singleOffset, false, tax, null);

        assertEquals(BigDecimal.TEN, singleOffset.getSundayOffsetValueWithTax().negate());
        assertEquals(tax.removeRoomTaxRate(BigDecimal.TEN), singleOffset.getSundayOffsetValue().negate());

        assertEquals(BigDecimal.TEN, doubleOffset.getSundayOffsetValueWithTax());
        assertEquals(tax.removeRoomTaxRate(BigDecimal.TEN), doubleOffset.getSundayOffsetValue());

        singleOffset.setSundayOffsetValueWithTax(BigDecimal.ONE);
        doubleOffset.setSundayOffsetValueWithTax(BigDecimal.TEN);
        service.adjustOffsetRecords(doubleOffset, singleOffset, true, tax, null);

        assertEquals(OccupancyType.SINGLE, doubleOffset.getOccupancyType());
        assertEquals(BigDecimal.TEN.negate(), doubleOffset.getSundayOffsetValueWithTax());
    }

    @Test
    public void testUpdateSingleAndDoubleOffsetValuesNonBase() {
        CPConfigOffsetAccomType singleOffset = new CPConfigOffsetAccomType();
        CPConfigOffsetAccomType doubleOffset = new CPConfigOffsetAccomType();

        Tax tax = new Tax();
        tax.setRoomTaxRate(BigDecimal.valueOf(25));

        doubleOffset.setSundayOffsetValueWithTax(BigDecimal.TEN);
        doubleOffset.setSundayOffsetValue(tax.removeRoomTaxRate(BigDecimal.TEN));

        service.updateSingleAndDoubleOffsetValuesNonBase(BigDecimal.ONE, BigDecimal.TEN, false, new PricingRule(), tax, OffsetMethod.FIXED_OFFSET, singleOffset, doubleOffset, true, CPConfigOffsetAccomType::setSundayOffsetValue, CPConfigOffsetAccomType::setSundayOffsetValueWithTax);

        assertEquals(BigDecimal.TEN, singleOffset.getSundayOffsetValueWithTax().negate());
        assertEquals(tax.removeRoomTaxRate(BigDecimal.TEN), singleOffset.getSundayOffsetValue().negate());

        assertEquals(BigDecimal.TEN, doubleOffset.getSundayOffsetValueWithTax());
        assertEquals(tax.removeRoomTaxRate(BigDecimal.TEN), doubleOffset.getSundayOffsetValue());
    }

    @Test
    public void testUpdateSingleAndDoubleOffsetValuesNonBasePriceExcludedFixedOffset() {
        CPConfigOffsetAccomType singleOffset = new CPConfigOffsetAccomType();
        CPConfigOffsetAccomType doubleOffset = new CPConfigOffsetAccomType();

        Tax tax = new Tax();
        tax.setRoomTaxRate(BigDecimal.valueOf(25));
        service.updateSingleAndDoubleOffsetValuesNonBase(BigDecimal.valueOf(100), BigDecimal.TEN, true, new PricingRule(), tax, OffsetMethod.FIXED_OFFSET, singleOffset, doubleOffset, true, CPConfigOffsetAccomType::setSundayOffsetValue, CPConfigOffsetAccomType::setSundayOffsetValueWithTax);

        assertEquals(BigDecimal.TEN, doubleOffset.getSundayOffsetValueWithTax().negate());
        assertEquals(tax.removeRoomTaxRate(BigDecimal.TEN), doubleOffset.getSundayOffsetValue().negate());

        assertEquals(BigDecimal.TEN.add(BigDecimal.valueOf(100)).setScale(2), singleOffset.getSundayOffsetValueWithTax());
        assertEquals(tax.removeRoomTaxRate(BigDecimal.TEN.add(BigDecimal.valueOf(100)).setScale(5)), singleOffset.getSundayOffsetValue());
    }

    @Test
    public void testUpdateSingleAndDoubleOffsetValuesNonBasePriceExcludedPercentageOffset() {
        CPConfigOffsetAccomType singleOffset = new CPConfigOffsetAccomType();
        CPConfigOffsetAccomType doubleOffset = new CPConfigOffsetAccomType();

        Tax tax = new Tax();
        tax.setRoomTaxRate(BigDecimal.valueOf(25));
        service.updateSingleAndDoubleOffsetValuesNonBase(BigDecimal.valueOf(100), BigDecimal.TEN, true, new PricingRule(), tax, OffsetMethod.PERCENTAGE, singleOffset, doubleOffset, true, CPConfigOffsetAccomType::setSundayOffsetValue, CPConfigOffsetAccomType::setSundayOffsetValueWithTax);

        BigDecimal negatedValue = BigDecimal.TEN.setScale(5).divide(BigDecimal.ONE.add(BigDecimal.TEN.movePointLeft(2)), 5).negate();
        assertEquals(negatedValue, doubleOffset.getSundayOffsetValueWithTax());
        assertEquals(tax.removeRoomTaxRate(negatedValue), doubleOffset.getSundayOffsetValue());
        assertEquals(OccupancyType.SINGLE, doubleOffset.getOccupancyType());

        assertEquals(BigDecimal.valueOf(100).multiply(BigDecimal.TEN.movePointLeft(2).add(BigDecimal.ONE)).setScale(2), singleOffset.getSundayOffsetValueWithTax());
        assertEquals(tax.removeRoomTaxRate(BigDecimal.valueOf(100).multiply(BigDecimal.TEN.movePointLeft(2).add(BigDecimal.ONE)).setScale(5)), singleOffset.getSundayOffsetValue());
        assertEquals(OccupancyType.DOUBLE, singleOffset.getOccupancyType());
    }

    @Test
    public void testUpdateSingleAndDoubleOffsetValuesNonBasePriceExcludedPercentageOffset_NullRoundingRule() {
        CPConfigOffsetAccomType singleOffset = new CPConfigOffsetAccomType();
        CPConfigOffsetAccomType doubleOffset = new CPConfigOffsetAccomType();

        Tax tax = new Tax();
        tax.setRoomTaxRate(BigDecimal.valueOf(25));
        service.updateSingleAndDoubleOffsetValuesNonBase(BigDecimal.valueOf(100), BigDecimal.TEN, true, null, tax, OffsetMethod.PERCENTAGE, singleOffset, doubleOffset, true, CPConfigOffsetAccomType::setSundayOffsetValue, CPConfigOffsetAccomType::setSundayOffsetValueWithTax);

        BigDecimal negatedValue = BigDecimal.TEN.setScale(5).divide(BigDecimal.ONE.add(BigDecimal.TEN.movePointLeft(2)), 5).negate();
        assertEquals(negatedValue, doubleOffset.getSundayOffsetValueWithTax());
        assertEquals(tax.removeRoomTaxRate(negatedValue), doubleOffset.getSundayOffsetValue());
        assertEquals(OccupancyType.SINGLE, doubleOffset.getOccupancyType());

        assertEquals(BigDecimal.valueOf(100).multiply(BigDecimal.TEN.movePointLeft(2).add(BigDecimal.ONE)).setScale(2), singleOffset.getSundayOffsetValueWithTax());
        assertEquals(tax.removeRoomTaxRate(BigDecimal.valueOf(100).multiply(BigDecimal.TEN.movePointLeft(2).add(BigDecimal.ONE)).setScale(5)), singleOffset.getSundayOffsetValue());
        assertEquals(OccupancyType.DOUBLE, singleOffset.getOccupancyType());
    }

    @Test
    public void updateOffsetValuesNonBase_PriceExcludedRCHavingSingleOffsetAsNull() {
        CPConfigOffsetAccomType singleOffset = new CPConfigOffsetAccomType();
        CPConfigOffsetAccomType doubleOffset = new CPConfigOffsetAccomType();

        Tax tax = new Tax();
        tax.setRoomTaxRate(BigDecimal.valueOf(25));
        service.updateSingleAndDoubleOffsetValuesNonBase(null, BigDecimal.TEN, true, new PricingRule(),
                tax, OffsetMethod.FIXED_OFFSET, singleOffset, doubleOffset, false, CPConfigOffsetAccomType::setSundayOffsetValue,
                CPConfigOffsetAccomType::setSundayOffsetValueWithTax);

        assertEquals(BigDecimal.TEN, doubleOffset.getSundayOffsetValueWithTax().negate());
        assertEquals(tax.removeRoomTaxRate(BigDecimal.TEN), doubleOffset.getSundayOffsetValue().negate());

        assertEquals(BigDecimal.TEN.setScale(2, RoundingMode.HALF_UP), singleOffset.getSundayOffsetValueWithTax());
        assertEquals(tax.removeRoomTaxRate(BigDecimal.TEN.setScale(5, RoundingMode.HALF_UP)), singleOffset.getSundayOffsetValue());
    }

    @Test
    public void updateOffsetValuesNonBase_FixedOffset_PriceExcludedRCHavingDoubleOffsetAsNull() {
        CPConfigOffsetAccomType singleOffset = new CPConfigOffsetAccomType();
        CPConfigOffsetAccomType doubleOffset = new CPConfigOffsetAccomType();

        Tax tax = new Tax();
        tax.setRoomTaxRate(BigDecimal.valueOf(25));
        service.updateSingleAndDoubleOffsetValuesNonBase(BigDecimal.TEN, null, true, new PricingRule(),
                tax, OffsetMethod.FIXED_OFFSET, singleOffset, doubleOffset, false, CPConfigOffsetAccomType::setSundayOffsetValue,
                CPConfigOffsetAccomType::setSundayOffsetValueWithTax);

        assertEquals(BigDecimal.TEN.setScale(2, RoundingMode.HALF_UP), singleOffset.getSundayOffsetValueWithTax());
        assertEquals(tax.removeRoomTaxRate(BigDecimal.TEN), singleOffset.getSundayOffsetValue());

        assertNull(doubleOffset.getSundayOffsetValueWithTax());
        assertNull(doubleOffset.getSundayOffsetValue());

    }

    @Test
    public void updateOffsetValuesNonBase_FixedOffset_PriceExcludedRCHavingBothOffsetsAsNull() {
        CPConfigOffsetAccomType singleOffset = new CPConfigOffsetAccomType();
        CPConfigOffsetAccomType doubleOffset = new CPConfigOffsetAccomType();

        Tax tax = new Tax();
        tax.setRoomTaxRate(BigDecimal.valueOf(25));
        service.updateSingleAndDoubleOffsetValuesNonBase(null, null, true, new PricingRule(),
                tax, OffsetMethod.FIXED_OFFSET, singleOffset, doubleOffset, false, CPConfigOffsetAccomType::setSundayOffsetValue,
                CPConfigOffsetAccomType::setSundayOffsetValueWithTax);

        assertNull(singleOffset.getSundayOffsetValueWithTax());
        assertEquals(BigDecimal.ZERO.setScale(5, RoundingMode.HALF_UP), singleOffset.getSundayOffsetValue());

        assertNull(doubleOffset.getSundayOffsetValueWithTax());
        assertNull(doubleOffset.getSundayOffsetValue());

    }

    @Test
    public void revertOffsetValuesNonBase_FixedOffset_PriceExcludedRCHavingDoubleOffsetAsNull() {
        CPConfigOffsetAccomType singleOffset = new CPConfigOffsetAccomType();
        CPConfigOffsetAccomType doubleOffset = new CPConfigOffsetAccomType();

        Tax tax = new Tax();
        tax.setRoomTaxRate(BigDecimal.valueOf(25));
        service.updateSingleAndDoubleOffsetValuesNonBase(BigDecimal.TEN, null, true, new PricingRule(),
                tax, OffsetMethod.FIXED_OFFSET, singleOffset, doubleOffset, false, CPConfigOffsetAccomType::setSundayOffsetValue,
                CPConfigOffsetAccomType::setSundayOffsetValueWithTax);

        assertEquals(BigDecimal.TEN.setScale(2, RoundingMode.HALF_UP), singleOffset.getSundayOffsetValueWithTax());
        assertEquals(tax.removeRoomTaxRate(BigDecimal.TEN).setScale(5, RoundingMode.HALF_UP), singleOffset.getSundayOffsetValue());

        assertNull(doubleOffset.getSundayOffsetValueWithTax());
        assertNull(doubleOffset.getSundayOffsetValue());

    }

    @Test
    public void revertOffsetValuesNonBase_FixedOffset_PriceExcludedRCHavingBothOffsetsAsNull() {
        CPConfigOffsetAccomType singleOffset = new CPConfigOffsetAccomType();
        CPConfigOffsetAccomType doubleOffset = new CPConfigOffsetAccomType();

        Tax tax = new Tax();
        tax.setRoomTaxRate(BigDecimal.valueOf(25));
        service.updateSingleAndDoubleOffsetValuesNonBase(null, null, true, new PricingRule(),
                tax, OffsetMethod.FIXED_OFFSET, singleOffset, doubleOffset, false, CPConfigOffsetAccomType::setSundayOffsetValue,
                CPConfigOffsetAccomType::setSundayOffsetValueWithTax);

        assertNull(singleOffset.getSundayOffsetValueWithTax());
        assertEquals(BigDecimal.ZERO.setScale(5, RoundingMode.HALF_UP), singleOffset.getSundayOffsetValue());

        assertNull(doubleOffset.getSundayOffsetValueWithTax());
        assertNull(doubleOffset.getSundayOffsetValue());

    }

    @Test
    public void updateOffsetValuesNonBase_PercentOffset_PriceExcludedRCHavingDoubleOffsetAsNull() {
        CPConfigOffsetAccomType singleOffset = new CPConfigOffsetAccomType();
        CPConfigOffsetAccomType doubleOffset = new CPConfigOffsetAccomType();

        Tax tax = new Tax();
        tax.setRoomTaxRate(BigDecimal.valueOf(25));
        service.updateSingleAndDoubleOffsetValuesNonBase(BigDecimal.TEN, null, true, new PricingRule(),
                tax, OffsetMethod.PERCENTAGE, singleOffset, doubleOffset, false, CPConfigOffsetAccomType::setSundayOffsetValue,
                CPConfigOffsetAccomType::setSundayOffsetValueWithTax);

        assertEquals(BigDecimal.TEN.setScale(2, RoundingMode.HALF_UP), singleOffset.getSundayOffsetValueWithTax());
        assertEquals(tax.removeRoomTaxRate(BigDecimal.TEN).setScale(5, RoundingMode.HALF_UP), singleOffset.getSundayOffsetValue());

        assertNull(doubleOffset.getSundayOffsetValueWithTax());
        assertEquals(BigDecimal.ZERO.setScale(5, RoundingMode.HALF_UP), doubleOffset.getSundayOffsetValue());
    }

    @Test
    public void updateOffsetValuesNonBase_PercentOffset_PriceExcludedRCHavingDoubleOffsetAsNull_NullRoundingRule() {
        CPConfigOffsetAccomType singleOffset = new CPConfigOffsetAccomType();
        CPConfigOffsetAccomType doubleOffset = new CPConfigOffsetAccomType();

        Tax tax = new Tax();
        tax.setRoomTaxRate(BigDecimal.valueOf(25));
        service.updateSingleAndDoubleOffsetValuesNonBase(BigDecimal.TEN, null, true, null,
                tax, OffsetMethod.PERCENTAGE, singleOffset, doubleOffset, false, CPConfigOffsetAccomType::setSundayOffsetValue,
                CPConfigOffsetAccomType::setSundayOffsetValueWithTax);

        assertEquals(BigDecimal.TEN.setScale(2, RoundingMode.HALF_UP), singleOffset.getSundayOffsetValueWithTax());
        assertEquals(tax.removeRoomTaxRate(BigDecimal.TEN).setScale(5, RoundingMode.HALF_UP), singleOffset.getSundayOffsetValue());

        assertNull(doubleOffset.getSundayOffsetValueWithTax());
        assertEquals(BigDecimal.ZERO.setScale(5, RoundingMode.HALF_UP), doubleOffset.getSundayOffsetValue());
    }

    @Test
    public void updateOffsetValuesNonBase_PercentOffset_PriceExcludedRCHavingBothOffsetsAsNull() {
        CPConfigOffsetAccomType singleOffset = new CPConfigOffsetAccomType();
        CPConfigOffsetAccomType doubleOffset = new CPConfigOffsetAccomType();

        Tax tax = new Tax();
        tax.setRoomTaxRate(BigDecimal.valueOf(25));
        service.updateSingleAndDoubleOffsetValuesNonBase(null, null, true, new PricingRule(),
                tax, OffsetMethod.PERCENTAGE, singleOffset, doubleOffset, false, CPConfigOffsetAccomType::setSundayOffsetValue,
                CPConfigOffsetAccomType::setSundayOffsetValueWithTax);

        assertNull(singleOffset.getSundayOffsetValueWithTax());
        assertEquals(BigDecimal.ZERO.setScale(5, RoundingMode.HALF_UP), singleOffset.getSundayOffsetValue());

        assertNull(doubleOffset.getSundayOffsetValueWithTax());
        assertEquals(BigDecimal.ZERO.setScale(5, RoundingMode.HALF_UP), doubleOffset.getSundayOffsetValue());
    }

    @Test
    public void revertOffsetValuesNonBase_PercentOffset_PriceExcludedRCHavingDoubleOffsetAsNull() {
        CPConfigOffsetAccomType singleOffset = new CPConfigOffsetAccomType();
        CPConfigOffsetAccomType doubleOffset = new CPConfigOffsetAccomType();

        Tax tax = new Tax();
        tax.setRoomTaxRate(BigDecimal.valueOf(25));
        service.updateSingleAndDoubleOffsetValuesNonBase(BigDecimal.TEN, null, true, new PricingRule(),
                tax, OffsetMethod.PERCENTAGE, singleOffset, doubleOffset, false, CPConfigOffsetAccomType::setSundayOffsetValue,
                CPConfigOffsetAccomType::setSundayOffsetValueWithTax);

        assertEquals(BigDecimal.TEN.setScale(2, RoundingMode.HALF_UP), singleOffset.getSundayOffsetValueWithTax());
        assertEquals(tax.removeRoomTaxRate(BigDecimal.TEN).setScale(5, RoundingMode.HALF_UP), singleOffset.getSundayOffsetValue());

        assertNull(doubleOffset.getSundayOffsetValueWithTax());
        assertEquals(BigDecimal.ZERO.setScale(5, RoundingMode.HALF_UP), doubleOffset.getSundayOffsetValue());

    }

    @Test
    public void revertOffsetValuesNonBase_PercentOffset_PriceExcludedRCHavingDoubleOffsetAsNull_NullRoundingRule() {
        CPConfigOffsetAccomType singleOffset = new CPConfigOffsetAccomType();
        CPConfigOffsetAccomType doubleOffset = new CPConfigOffsetAccomType();

        Tax tax = new Tax();
        tax.setRoomTaxRate(BigDecimal.valueOf(25));
        service.updateSingleAndDoubleOffsetValuesNonBase(BigDecimal.TEN, null, true, null,
                tax, OffsetMethod.PERCENTAGE, singleOffset, doubleOffset, false, CPConfigOffsetAccomType::setSundayOffsetValue,
                CPConfigOffsetAccomType::setSundayOffsetValueWithTax);

        assertEquals(BigDecimal.TEN.setScale(2, RoundingMode.HALF_UP), singleOffset.getSundayOffsetValueWithTax());
        assertEquals(tax.removeRoomTaxRate(BigDecimal.TEN).setScale(5, RoundingMode.HALF_UP), singleOffset.getSundayOffsetValue());

        assertNull(doubleOffset.getSundayOffsetValueWithTax());
        assertEquals(BigDecimal.ZERO.setScale(5, RoundingMode.HALF_UP), doubleOffset.getSundayOffsetValue());

    }

    @Test
    public void revertOffsetValuesNonBase_PercentOffset_PriceExcludedRCHavingBothOffsetsAsNull() {
        CPConfigOffsetAccomType singleOffset = new CPConfigOffsetAccomType();
        CPConfigOffsetAccomType doubleOffset = new CPConfigOffsetAccomType();

        Tax tax = new Tax();
        tax.setRoomTaxRate(BigDecimal.valueOf(25));
        service.updateSingleAndDoubleOffsetValuesNonBase(null, null, true, new PricingRule(),
                tax, OffsetMethod.PERCENTAGE, singleOffset, doubleOffset, false, CPConfigOffsetAccomType::setSundayOffsetValue,
                CPConfigOffsetAccomType::setSundayOffsetValueWithTax);

        assertNull(singleOffset.getSundayOffsetValueWithTax());
        assertEquals(BigDecimal.ZERO.setScale(5, RoundingMode.HALF_UP), singleOffset.getSundayOffsetValue());

        assertNull(doubleOffset.getSundayOffsetValueWithTax());
        assertEquals(BigDecimal.ZERO.setScale(5, RoundingMode.HALF_UP), doubleOffset.getSundayOffsetValue());

    }

    @Test
    public void testUpdateOffsetValueForPercentageBaseRoomType() {
        CPConfigOffsetAccomType doubleOffset = new CPConfigOffsetAccomType();

        Tax tax = new Tax();
        tax.setRoomTaxRate(BigDecimal.valueOf(25));
        service.updateOffsetValueForPercentageBaseRoomType(doubleOffset, tax, BigDecimal.TEN, CPConfigOffsetAccomType::setSundayOffsetValue, CPConfigOffsetAccomType::setSundayOffsetValueWithTax);

        assertEquals(BigDecimal.TEN.negate(), doubleOffset.getSundayOffsetValueWithTax());
        assertEquals(tax.removeRoomTaxRate(BigDecimal.TEN.negate()), doubleOffset.getSundayOffsetValue());
    }

    @Test
    public void testAdjustDefaultAndSeasonOffsetsNonBaseRoomType() {
        when(tenantCrudService.findByNamedQuery(PricingAccomClass.DISTINCT_BASE_ROOM_TYPES_BY_PROPERTY_ID,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters()))
                .thenReturn(new ArrayList<>());

        AccomType accomType = new AccomType();
        Tax tax = new Tax();
        tax.setRoomTaxRate(BigDecimal.valueOf(25));

        CPConfigOffsetAccomType offset = new CPConfigOffsetAccomType();
        offset.setProductID(1);
        offset.setStatusId(Status.ACTIVE.getId());
        offset.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        offset.setOccupancyType(OccupancyType.DOUBLE);
        offset.setSundayOffsetValueWithTax(BigDecimal.TEN);
        offset.setSundayOffsetValue(BigDecimal.valueOf(8));
        offset.setAccomType(accomType);

        List<CPConfigOffsetAccomType> offsetList = new ArrayList<>(Arrays.asList(offset));

        Map<Integer, PricingRule> pricingRules = new HashMap<>();
        pricingRules.put(1, new PricingRule());

        service.adjustDefaultAndSeasonOffsets(tax, offsetList, pricingRules);

        assertEquals(OccupancyType.DOUBLE, offset.getOccupancyType());
        assertEquals(BigDecimal.valueOf(10), offset.getSundayOffsetValueWithTax());
        assertEquals(BigDecimal.valueOf(8), offset.getSundayOffsetValue());
        assertNull(offset.getMondayOffsetValueWithTax());
        assertNull(offset.getMondayOffsetValue());
    }

    @Test
    public void testAdjustDefaultAndSeasonOffsetsNonBaseRoomTypeDoubleOnly() {
        when(tenantCrudService.findByNamedQuery(PricingAccomClass.DISTINCT_BASE_ROOM_TYPES_BY_PROPERTY_ID,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters()))
                .thenReturn(new ArrayList<>());

        AccomType accomType = new AccomType();
        Tax tax = new Tax();
        tax.setRoomTaxRate(BigDecimal.valueOf(25));

        CPConfigOffsetAccomType offset = new CPConfigOffsetAccomType();
        offset.setProductID(1);
        offset.setStatusId(Status.ACTIVE.getId());
        offset.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        offset.setOccupancyType(OccupancyType.SINGLE);
        offset.setSundayOffsetValueWithTax(BigDecimal.TEN);
        offset.setSundayOffsetValue(BigDecimal.valueOf(8));
        offset.setAccomType(accomType);

        List<CPConfigOffsetAccomType> offsetList = new ArrayList<>(Arrays.asList(offset));

        Map<Integer, PricingRule> pricingRules = new HashMap<>();
        pricingRules.put(1, new PricingRule());

        service.adjustDefaultAndSeasonOffsets(tax, offsetList, pricingRules);

        assertEquals(OccupancyType.SINGLE, offset.getOccupancyType());
        assertNull(offset.getSundayOffsetValueWithTax());
        assertNull(offset.getSundayOffsetValue());
        assertNull(offset.getMondayOffsetValueWithTax());
        assertNull(offset.getMondayOffsetValue());
    }

    @Test
    public void testHandleRevertFromPerPersonPricing() {
        Tax tax = new Tax();
        tax.setRoomTaxRate(BigDecimal.valueOf(25));
        when(taxService.findTax()).thenReturn(tax);

        AccomType accomType = new AccomType();
        CPConfigOffsetAccomType offset = new CPConfigOffsetAccomType();
        offset.setProductID(1);
        offset.setSundayOffsetValueWithTax(BigDecimal.TEN);
        offset.setSundayOffsetValue(BigDecimal.valueOf(8));
        offset.setAccomType(accomType);
        offset.setOccupancyType(OccupancyType.SINGLE);
        offset.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        when(tenantCrudService.findAll(CPConfigOffsetAccomType.class)).thenReturn(new ArrayList<>(Arrays.asList(offset)));
        Map<Integer, PricingRule> pricingRules = new HashMap<>();
        pricingRules.put(1, new PricingRule());
        when(prettyPricingService.getPricingRules()).thenReturn(pricingRules);

        TransientPricingBaseAccomType tpbat = new TransientPricingBaseAccomType();
        tpbat.setProductID(1);
        tpbat.setSundayFloorRateWithTax(BigDecimal.valueOf(90));
        tpbat.setSundayFloorRate(BigDecimal.valueOf(72));
        tpbat.setAccomType(accomType);
        when(tenantCrudService.findAll(TransientPricingBaseAccomType.class)).thenReturn(new ArrayList<>(Arrays.asList(tpbat)));
        service.handleRevertFromPerPersonPricing();

        assertEquals(BigDecimal.valueOf(100).setScale(2), tpbat.getSundayFloorRateWithTax());
        assertEquals(BigDecimal.valueOf(80).setScale(5), tpbat.getSundayFloorRate());

        assertEquals(OccupancyType.SINGLE, offset.getOccupancyType());
        assertEquals(BigDecimal.valueOf(10), offset.getSundayOffsetValueWithTax());
        assertEquals(BigDecimal.valueOf(8), offset.getSundayOffsetValue());
    }

    @Test
    public void testRevertDefaultFloorAndCeilingValuesFromPerPersonPricing() {
        Tax tax = new Tax();
        tax.setRoomTaxRate(BigDecimal.valueOf(25));

        AccomType accomType = new AccomType();
        CPConfigOffsetAccomType offset = new CPConfigOffsetAccomType();
        offset.setProductID(1);
        offset.setSundayOffsetValueWithTax(BigDecimal.TEN);
        offset.setSundayOffsetValue(BigDecimal.valueOf(8));
        offset.setAccomType(accomType);
        offset.setOccupancyType(OccupancyType.SINGLE);

        TransientPricingBaseAccomType tpbat = new TransientPricingBaseAccomType();
        tpbat.setProductID(1);
        tpbat.setSundayFloorRateWithTax(BigDecimal.valueOf(90));
        tpbat.setSundayFloorRate(BigDecimal.valueOf(72));
        tpbat.setAccomType(accomType);

        Map<Integer, PricingRule> pricingRules = new HashMap<>();
        pricingRules.put(1, new PricingRule());

        service.revertDefaultFloorAndCeilingValuesFromPerPersonPricing(tax, Arrays.asList(offset), Arrays.asList(tpbat), pricingRules);

        assertEquals(BigDecimal.valueOf(100).setScale(2), tpbat.getSundayFloorRateWithTax());
        assertEquals(BigDecimal.valueOf(80).setScale(5), tpbat.getSundayFloorRate());
    }

    @Test
    public void testRevertPPPRatesForDOW() {
        Tax tax = new Tax();
        tax.setRoomTaxRate(BigDecimal.valueOf(25));

        CPConfigOffsetAccomType offset = new CPConfigOffsetAccomType();
        offset.setSundayOffsetValueWithTax(BigDecimal.TEN);
        offset.setSundayOffsetValue(BigDecimal.valueOf(8));

        TransientPricingBaseAccomType tpbat = new TransientPricingBaseAccomType();
        tpbat.setSundayFloorRateWithTax(BigDecimal.valueOf(90));
        tpbat.setSundayFloorRate(BigDecimal.valueOf(72));
        service.revertPPPRatesForDOW(tax, tpbat, offset, new PricingRule());

        assertEquals(BigDecimal.valueOf(100).setScale(2), tpbat.getSundayFloorRateWithTax());
        assertEquals(BigDecimal.valueOf(80).setScale(5), tpbat.getSundayFloorRate());
    }

    @Test
    public void testRevertPPPRate() {
        Tax tax = new Tax();
        tax.setRoomTaxRate(BigDecimal.valueOf(25));

        TransientPricingBaseAccomType tpbat = new TransientPricingBaseAccomType();
        service.calculateNewPPPRate(tax, BigDecimal.valueOf(9), BigDecimal.ONE, OffsetMethod.FIXED_OFFSET, tpbat, TransientPricingBaseAccomType::setSundayFloorRate, TransientPricingBaseAccomType::setSundayFloorRateWithTax, new PricingRule());

        assertEquals(BigDecimal.valueOf(10).setScale(2), tpbat.getSundayFloorRateWithTax());
        assertEquals(BigDecimal.valueOf(8).setScale(5), tpbat.getSundayFloorRate());
    }

    @Test
    public void testRevertDefaultAndSeasonOffsetsBaseRoomType() {
        TransientPricingBaseAccomType transientPricingBaseAccomType = new TransientPricingBaseAccomType();
        AccomType accomType = new AccomType();
        transientPricingBaseAccomType.setAccomType(accomType);
        when(tenantCrudService.findByNamedQuery(TransientPricingBaseAccomType.FIND_ALL_BY_PROPERTY_ID,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters()))
                .thenReturn(Arrays.asList(transientPricingBaseAccomType));

        Tax tax = new Tax();
        tax.setRoomTaxRate(BigDecimal.valueOf(25));

        CPConfigOffsetAccomType offset = new CPConfigOffsetAccomType();
        offset.setProductID(1);
        offset.setStatusId(Status.ACTIVE.getId());
        offset.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        offset.setOccupancyType(OccupancyType.SINGLE);
        offset.setSundayOffsetValueWithTax(BigDecimal.TEN);
        offset.setSundayOffsetValue(BigDecimal.valueOf(8));
        offset.setAccomType(accomType);

        List<CPConfigOffsetAccomType> offsetList = new ArrayList<>(Arrays.asList(offset));

        Map<Integer, PricingRule> pricingRules = new HashMap<>();
        pricingRules.put(1, new PricingRule());

        service.revertDefaultAndSeasonOffsets(tax, offsetList, pricingRules);

        assertEquals(OccupancyType.DOUBLE, offset.getOccupancyType());
        assertEquals(BigDecimal.valueOf(-10), offset.getSundayOffsetValueWithTax());
        assertEquals(BigDecimal.valueOf(-8), offset.getSundayOffsetValue());
        assertNull(offset.getMondayOffsetValueWithTax());
        assertNull(offset.getMondayOffsetValue());
    }

    @Test
    public void testRevertOffsetRecords() {
        CPConfigOffsetAccomType singleOffset = new CPConfigOffsetAccomType();
        singleOffset.setSundayOffsetValueWithTax(BigDecimal.ONE.negate());
        singleOffset.setOffsetMethod(OffsetMethod.FIXED_OFFSET);

        CPConfigOffsetAccomType doubleOffset = new CPConfigOffsetAccomType();
        doubleOffset.setSundayOffsetValueWithTax(BigDecimal.TEN);
        doubleOffset.setOffsetMethod(OffsetMethod.FIXED_OFFSET);

        Tax tax = new Tax();
        tax.setRoomTaxRate(BigDecimal.valueOf(25));

        singleOffset.setSundayOffsetValue(tax.removeRoomTaxRate(BigDecimal.ONE.negate()));
        doubleOffset.setSundayOffsetValue(tax.removeRoomTaxRate(BigDecimal.TEN));
        service.revertOffsetRecords(doubleOffset, singleOffset, false, tax, new PricingRule());

        assertEquals(BigDecimal.ONE, doubleOffset.getSundayOffsetValueWithTax());
        assertEquals(tax.removeRoomTaxRate(BigDecimal.ONE), doubleOffset.getSundayOffsetValue());

        assertEquals(BigDecimal.ONE.negate(), singleOffset.getSundayOffsetValueWithTax());
        assertEquals(tax.removeRoomTaxRate(BigDecimal.ONE.negate()), singleOffset.getSundayOffsetValue());

        singleOffset.setSundayOffsetValueWithTax(BigDecimal.ONE);
        doubleOffset.setSundayOffsetValueWithTax(BigDecimal.TEN);
        service.revertOffsetRecords(doubleOffset, singleOffset, true, tax, new PricingRule());

        assertEquals(OccupancyType.DOUBLE, singleOffset.getOccupancyType());
        assertEquals(BigDecimal.ONE.negate(), singleOffset.getSundayOffsetValueWithTax());
    }

    @Test
    public void testRevertSingleAndDoubleOffsetValuesNonBase() {
        CPConfigOffsetAccomType singleOffset = new CPConfigOffsetAccomType();
        CPConfigOffsetAccomType doubleOffset = new CPConfigOffsetAccomType();

        Tax tax = new Tax();
        tax.setRoomTaxRate(BigDecimal.valueOf(25));

        singleOffset.setSundayOffsetValueWithTax(BigDecimal.ONE.negate());
        singleOffset.setSundayOffsetValue(tax.removeRoomTaxRate(BigDecimal.ONE.negate()));

        service.revertSingleAndDoubleOffsetValuesNonBase(BigDecimal.ONE.negate(), BigDecimal.TEN, false, new PricingRule(), tax, OffsetMethod.FIXED_OFFSET, singleOffset, doubleOffset, true, CPConfigOffsetAccomType::setSundayOffsetValue, CPConfigOffsetAccomType::setSundayOffsetValueWithTax);

        assertEquals(BigDecimal.ONE, doubleOffset.getSundayOffsetValueWithTax());
        assertEquals(tax.removeRoomTaxRate(BigDecimal.ONE), doubleOffset.getSundayOffsetValue());

        assertEquals(BigDecimal.ONE.negate(), singleOffset.getSundayOffsetValueWithTax());
        assertEquals(tax.removeRoomTaxRate(BigDecimal.ONE.negate()), singleOffset.getSundayOffsetValue());
    }

    @Test
    public void testRevertSingleAndDoubleOffsetValuesNonBasePriceExcludedFixedOffset() {
        CPConfigOffsetAccomType singleOffset = new CPConfigOffsetAccomType();
        CPConfigOffsetAccomType doubleOffset = new CPConfigOffsetAccomType();

        Tax tax = new Tax();
        tax.setRoomTaxRate(BigDecimal.valueOf(25));
        service.revertSingleAndDoubleOffsetValuesNonBase(BigDecimal.TEN, BigDecimal.valueOf(100), true, new PricingRule(), tax, OffsetMethod.FIXED_OFFSET, singleOffset, doubleOffset, true, CPConfigOffsetAccomType::setSundayOffsetValue, CPConfigOffsetAccomType::setSundayOffsetValueWithTax);

        assertEquals(BigDecimal.TEN, singleOffset.getSundayOffsetValueWithTax().negate());
        assertEquals(tax.removeRoomTaxRate(BigDecimal.TEN), singleOffset.getSundayOffsetValue().negate());

        assertEquals(BigDecimal.valueOf(100).add(BigDecimal.TEN).setScale(2), doubleOffset.getSundayOffsetValueWithTax());
        assertEquals(tax.removeRoomTaxRate(BigDecimal.valueOf(100).add(BigDecimal.TEN).setScale(5)), doubleOffset.getSundayOffsetValue());
    }

    @Test
    public void testRevertSingleAndDoubleOffsetValuesNonBasePriceExcludedPercentageOffset() {
        CPConfigOffsetAccomType singleOffset = new CPConfigOffsetAccomType();
        CPConfigOffsetAccomType doubleOffset = new CPConfigOffsetAccomType();

        Tax tax = new Tax();
        tax.setRoomTaxRate(BigDecimal.valueOf(25));
        service.revertSingleAndDoubleOffsetValuesNonBase(BigDecimal.TEN, BigDecimal.valueOf(100), true, new PricingRule(), tax, OffsetMethod.PERCENTAGE, singleOffset, doubleOffset, true, CPConfigOffsetAccomType::setSundayOffsetValue, CPConfigOffsetAccomType::setSundayOffsetValueWithTax);

        assertEquals(BigDecimal.TEN, singleOffset.getSundayOffsetValueWithTax().negate());
        assertEquals(tax.removeRoomTaxRate(BigDecimal.TEN), singleOffset.getSundayOffsetValue().negate());

        assertEquals(BigDecimal.valueOf(100).multiply(BigDecimal.TEN.movePointLeft(2).add(BigDecimal.ONE)).setScale(2), doubleOffset.getSundayOffsetValueWithTax());
        assertEquals(tax.removeRoomTaxRate(BigDecimal.valueOf(100).multiply(BigDecimal.TEN.movePointLeft(2).add(BigDecimal.ONE)).setScale(5)), doubleOffset.getSundayOffsetValue());
    }

    @Test
    public void testRevertDefaultAndSeasonOffsetsNonBaseRoomType() {
        when(tenantCrudService.findByNamedQuery(TransientPricingBaseAccomType.FIND_ALL_BY_PROPERTY_ID,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters()))
                .thenReturn(new ArrayList<>());

        AccomType accomType = new AccomType();
        Tax tax = new Tax();
        tax.setRoomTaxRate(BigDecimal.valueOf(25));

        CPConfigOffsetAccomType offset = new CPConfigOffsetAccomType();
        offset.setProductID(1);
        offset.setStatusId(Status.ACTIVE.getId());
        offset.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        offset.setOccupancyType(OccupancyType.SINGLE);
        offset.setSundayOffsetValueWithTax(BigDecimal.TEN.negate());
        offset.setSundayOffsetValue(BigDecimal.valueOf(8).negate());
        offset.setAccomType(accomType);

        List<CPConfigOffsetAccomType> offsetList = new ArrayList<>(Arrays.asList(offset));

        Map<Integer, PricingRule> pricingRules = new HashMap<>();
        pricingRules.put(1, new PricingRule());

        service.revertDefaultAndSeasonOffsets(tax, offsetList, pricingRules);

        assertEquals(OccupancyType.SINGLE, offset.getOccupancyType());
        assertEquals(BigDecimal.valueOf(-10), offset.getSundayOffsetValueWithTax());
        assertEquals(BigDecimal.valueOf(-8), offset.getSundayOffsetValue());
        assertNull(offset.getMondayOffsetValueWithTax());
        assertNull(offset.getMondayOffsetValue());
    }

    @Test
    public void testRevertDefaultAndSeasonOffsetsNonBaseRoomTypeDoubleOnly() {
        when(tenantCrudService.findByNamedQuery(TransientPricingBaseAccomType.FIND_ALL_BY_PROPERTY_ID,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters()))
                .thenReturn(new ArrayList<>());

        AccomType accomType = new AccomType();
        Tax tax = new Tax();
        tax.setRoomTaxRate(BigDecimal.valueOf(25));

        CPConfigOffsetAccomType offset = new CPConfigOffsetAccomType();
        offset.setProductID(1);
        offset.setStatusId(Status.ACTIVE.getId());
        offset.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        offset.setOccupancyType(OccupancyType.DOUBLE);
        offset.setSundayOffsetValueWithTax(BigDecimal.TEN);
        offset.setSundayOffsetValue(BigDecimal.valueOf(8));
        offset.setAccomType(accomType);

        List<CPConfigOffsetAccomType> offsetList = new ArrayList<>(Arrays.asList(offset));

        Map<Integer, PricingRule> pricingRules = new HashMap<>();
        pricingRules.put(1, new PricingRule());

        service.revertDefaultAndSeasonOffsets(tax, offsetList, pricingRules);

        assertEquals(OccupancyType.DOUBLE, offset.getOccupancyType());
        assertNull(offset.getSundayOffsetValueWithTax());
        assertNull(offset.getSundayOffsetValue());
        assertNull(offset.getMondayOffsetValueWithTax());
        assertNull(offset.getMondayOffsetValue());
    }

    private void updatePriceExcluded(String productId) {
        tenantCrudService().executeUpdateByNativeQuery("insert into CP_Cfg_Base_AT_Draft   " +
                "    values (" + PacmanWorkContextHelper.getPropertyId() + " , " + 4 + " , null, null,  100.00,100.00, 99.00, 99.00, 99.00, 99.00, 99.00, 99.00, 99.00, 99.00, 99.00, 99.00, 99.00, 99.00, 1, 1, '2020-12-12', 1, '2020-12-12', 'test season', " +
                "     110.00,110.00, 109.00, 109.00, 109.00, 109.00, 109.00, 109.00, 109.00, 109.00, 109.00, 109.00, 109.00, 109.00, " + productId + ")");
        tenantCrudService().executeUpdateByNativeQuery("update CP_Cfg_AC set Is_Price_Excluded = 1 where accom_type_id = 4");
    }

    private void createCeilingFloorDraftDefaultData() {
        tenantCrudService().executeUpdateByNativeQuery("insert into CP_Cfg_Base_AT_Draft   " +
                "    values (" + PacmanWorkContextHelper.getPropertyId() + " , " + 5 + " , null, null,  60.00,100.00, 99.00, 99.00, 99.00, 99.00, 99.00, 99.00, 99.00, 99.00, 99.00, 99.00, 99.00, 99.00, 1, 1, '2020-12-12', 1, '2020-12-12', 'test season', " +
                "     70.00,110.00, 109.00, 109.00, 109.00, 109.00, 109.00, 109.00, 109.00, 109.00, 109.00, 109.00, 109.00, 109.00, 1)");

        tenantCrudService().executeUpdateByNativeQuery("insert into CP_Cfg_Base_AT_Draft   " +
                "    values (" + PacmanWorkContextHelper.getPropertyId() + " , " + 6 + " , null, null, 100.00, 160.00, 199.00, 199.00, 199.00, 199.00, 199.00, 199.00, 199.00, 99.00, 99.00, 99.00, 99.00, 99.00, 1, 1, '2020-12-12', 1, '2020-12-12', 'test season', " +
                "    210.00, 170.00, 209.00, 209.00, 209.00, 209.00, 209.00, 209.00, 209.00, 209.00, 209.00, 209.00, 209.00, 209.00, 1)");
    }
    private void createCeilingFloorDraftDefaultDataForIP(String id) {
        tenantCrudService().executeUpdateByNativeQuery("insert into CP_Cfg_Base_AT_Draft   " +
                "    values (" + PacmanWorkContextHelper.getPropertyId() + " , " + 5 + " , null, null,  60.00,100.00, 99.00, 99.00, 99.00, 99.00, 99.00, 99.00, 99.00, 99.00, 99.00, 99.00, 99.00, 99.00, 1, 1, '2020-12-12', 1, '2020-12-12', 'test season', " +
                "     70.00,110.00, 109.00, 109.00, 109.00, 109.00, 109.00, 109.00, 109.00, 109.00, 109.00, 109.00, 109.00, 109.00, " + id + ")");

        tenantCrudService().executeUpdateByNativeQuery("insert into CP_Cfg_Base_AT_Draft   " +
                "    values (" + PacmanWorkContextHelper.getPropertyId() + " , " + 6 + " , null, null, 100.00, 160.00, 199.00, 199.00, 199.00, 199.00, 199.00, 199.00, 199.00, 99.00, 99.00, 99.00, 99.00, 99.00, 1, 1, '2020-12-12', 1, '2020-12-12', 'test season', " +
                "    210.00, 170.00, 209.00, 209.00, 209.00, 209.00, 209.00, 209.00, 209.00, 209.00, 209.00, 209.00, 209.00, 209.00, " + id + ")");
    }

    private void createCeilingFloorDraftSeasonalData() {
        insertTestTransientDataInTable("CP_Cfg_Base_AT_Draft");
    }

    private void insertTestTransientDataInTable(String table) {
        tenantCrudService().executeUpdateByNativeQuery("insert into " + table +
                "    values (" + PacmanWorkContextHelper.getPropertyId() + " , " + 5 + " , '2020-01-01', '2020-05-01',  80.00,120.00, 99.00, 99.00, 99.00, 99.00, 99.00, 99.00, 99.00, 99.00, 99.00, 99.00, 99.00, 99.00, 1, 1, '2020-12-12', 1, '2020-12-12', 'test season', " +
                "     170.00,210.00, 109.00, 109.00, 109.00, 109.00, 109.00, 109.00, 109.00, 109.00, 109.00, 109.00, 109.00, 109.00, 1)");

        tenantCrudService().executeUpdateByNativeQuery("insert into " + table + "   " +
                "    values (" + PacmanWorkContextHelper.getPropertyId() + " , " + 6 + " , '2020-06-01', '2020-09-01', 100.00, 160.00, 199.00, 199.00, 199.00, 199.00, 199.00, 199.00, 199.00, 99.00, 99.00, 99.00, 99.00, 99.00, 1, 1, '2020-12-12', 1, '2020-12-12', 'test season', " +
                "    230.00, 370.00, 209.00, 209.00, 209.00, 209.00, 209.00, 209.00, 209.00, 209.00, 209.00, 209.00, 209.00, 209.00, 1)");
    }

    private void createCeilingFloorTransientSeasonalData() {
        insertTestTransientDataInTable("CP_Cfg_Base_AT");
    }


    @Test
    public void DefaultMinPriceDiffTest() {
        when(tenantCrudService.findAll(AccomClassMinPriceDiff.class)).thenReturn(getMinPriceDiffTestData());
        when(tenantCrudService.findAll(AccomClassMinPriceDiffSeason.class)).thenReturn(Collections.emptyList());
        verifyMinPriceDiffDefault(service.getDefaultAndSeasonMinPriceDiff(new Date()));
    }

    @Test
    public void DefaultAndSeasonalMinPriceDiffTest() {
        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put("currentSystemDate", LocalDate.now());
        when(tenantCrudService.findAll(AccomClassMinPriceDiff.class)).thenReturn(getMinPriceDiffTestData());
        when(tenantCrudService.findByNamedQuery(AccomClassMinPriceDiffSeason.GET_CURRENT_SEASON_BY_DATE, queryParams))
                .thenReturn(getSeasonalMinPriceDiffTestData());
        verifyMinPriceDiffDefaultWithSeasonal(service.getDefaultAndSeasonMinPriceDiff(new Date()));
    }

    @Test
    public void getGroupPricingBaseAccomTypeDefaultsWithPriceExcluded() {
        PricingBaseAccomType defaultPricingBaseAccomType = PricingConfigurationObjectMother.buildGroupPricingBaseAccomType();
        PricingBaseAccomType nonDefaultPricingBaseAccomType = PricingConfigurationObjectMother.buildGroupPricingBaseAccomTypeSeason();
        Map<String, Object> queryParams = getQueryParamMapForPropertyID();
        when(multiPropertyCrudService.findByNamedQueryForSingleProperty(PacmanWorkContextHelper.getPropertyId(), GroupPricingBaseAccomType.FIND_BASE_AT_FOR_PRICEABLE_AC_WITH_PRICE_EXCLUDED, queryParams))
                .thenReturn(Arrays.asList(defaultPricingBaseAccomType, nonDefaultPricingBaseAccomType));
        List<PricingBaseAccomType> result = service.getGroupPricingBaseAccomTypeDefaultsWithPriceExcluded(PacmanWorkContextHelper.getPropertyId());
        assertEquals(1, result.size());
        assertTrue(result.contains(defaultPricingBaseAccomType));
    }

    @Test
    public void getGroupPricingBaseAccomTypeSeasonsWithPriceExcluded() {
        PricingBaseAccomType defaultPricingBaseAccomType = PricingConfigurationObjectMother.buildGroupPricingBaseAccomType();
        PricingBaseAccomType nonDefaultPricingBaseAccomType = PricingConfigurationObjectMother.buildGroupPricingBaseAccomTypeSeason();
        Map<String, Object> queryParams = getQueryParamMapForPropertyID();
        when(multiPropertyCrudService.findByNamedQueryForSingleProperty(PacmanWorkContextHelper.getPropertyId(), GroupPricingBaseAccomType.FIND_BASE_AT_FOR_PRICEABLE_AC_WITH_PRICE_EXCLUDED, queryParams))
                .thenReturn(Arrays.asList(defaultPricingBaseAccomType, nonDefaultPricingBaseAccomType));
        List<PricingBaseAccomType> result = service.getGroupPricingBaseAccomTypeSeasonsWithPriceExcluded();
        assertEquals(1, result.size());
        assertTrue(result.contains(nonDefaultPricingBaseAccomType));
    }

    @Test
    public void testGetBaseProductAccomTypesForProduct() {
        Product product = new Product();

        AccomType baseAccomType = new AccomType();
        AccomType nonBaseAccomType = new AccomType();

        ProductAccomType basePat = new ProductAccomType();
        basePat.setAccomType(baseAccomType);

        ProductAccomType nonBasePat = new ProductAccomType();
        nonBasePat.setAccomType(nonBaseAccomType);

        PricingAccomClass basePac = new PricingAccomClass();
        basePac.setAccomType(baseAccomType);

        Map<String, Object> queryParams = getQueryParamMapForPropertyID();
        when(multiPropertyCrudService.findByNamedQueryForSingleProperty(PacmanWorkContextHelper.getPropertyId(), PricingAccomClass.FIND_BY_PROPERTY_ID, queryParams))
                .thenReturn(Collections.singletonList(basePac));
        when(tenantCrudService.findByNamedQuery(ProductAccomType.BY_PRODUCT, (QueryParameter.with("product", product).parameters())))
                .thenReturn(Arrays.asList(basePat, nonBasePat));

        List<ProductAccomType> result = service.getBaseProductAccomTypesForProduct(product);
        assertEquals(1, result.size());
        assertTrue(result.contains(basePat));
    }

    @Test
    public void testGetPricingAccomClassesForProduct() {
        AccomType accomType1 = new AccomType();
        AccomType accomType2 = new AccomType();

        PricingAccomClass pricingAccomClass1 = new PricingAccomClass();
        pricingAccomClass1.setAccomType(accomType1);
        PricingAccomClass pricingAccomClass2 = new PricingAccomClass();
        pricingAccomClass2.setAccomType(accomType2);

        ProductAccomType productAccomType1 = new ProductAccomType();
        productAccomType1.setAccomType(accomType1);

        Map<String, Object> queryParams = getQueryParamMapForPropertyID();
        when(multiPropertyCrudService.findByNamedQueryForSingleProperty(PacmanWorkContextHelper.getPropertyId(), PricingAccomClass.FIND_BY_PROPERTY_ID, queryParams))
                .thenReturn(new ArrayList<>(Arrays.asList(pricingAccomClass1, pricingAccomClass2)));

        when(agileRatesConfigurationService.findProductRoomTypeByProducts(any())).thenReturn(new ArrayList<>(Arrays.asList(productAccomType1)));

        // should return only pricingAccomClass1 as the product only has a room type mapping to accomType1
        List<PricingAccomClass> result = service.getPricingAccomClassesForProduct(new Product());
        assertEquals(1, result.size());
        assertTrue(result.contains(pricingAccomClass1));
    }

    @Test
    public void testGetNonExcludedPricingAccomClassesTransient() {
        AccomType accomType1 = new AccomType();

        PricingAccomClass pricingAccomClass = new PricingAccomClass();
        pricingAccomClass.setPriceExcluded(false);
        pricingAccomClass.setAccomType(accomType1);

        ProductAccomType productAccomType1 = new ProductAccomType();
        productAccomType1.setAccomType(accomType1);

        Product product = new Product();
        product.setCode(Product.INDEPENDENT_PRODUCT_CODE);

        Map<String, Object> queryParams = getQueryParamMapForPropertyID();
        when(multiPropertyCrudService.findByNamedQueryForSingleProperty(PacmanWorkContextHelper.getPropertyId(), PricingAccomClass.FIND_BY_PROPERTY_ID, queryParams))
                .thenReturn(new ArrayList<>(Arrays.asList(pricingAccomClass)));

        when(agileRatesConfigurationService.findProductRoomTypeByProducts(any())).thenReturn(new ArrayList<>(Arrays.asList(productAccomType1)));

        // test non independent product flow (remove once toggle has been removed)
        List<PricingAccomClass> result = service.getNonExcludedPricingAccomClassesTransient(PacmanWorkContextHelper.getPropertyId(), product);
        assertTrue(result.contains(pricingAccomClass));

        // test independent product flow
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.INDEPENDENT_PRODUCTS_ENABLED)).thenReturn(true);
        result = service.getNonExcludedPricingAccomClassesTransient(PacmanWorkContextHelper.getPropertyId(), product);
        assertTrue(result.contains(pricingAccomClass));

        verify(multiPropertyCrudService, times(2)).findByNamedQueryForSingleProperty(PacmanWorkContextHelper.getPropertyId(),
                PricingAccomClass.FIND_BY_PROPERTY_ID, queryParams);
        verify(agileRatesConfigurationService, times(1)).findProductRoomTypeByProducts(any());
    }

    @Test
    public void testSaveTransientPricingBaseAccomTypesWithIndependentProducts() {
        List<PricingBaseAccomType> pricingBaseAccomTypes = Arrays
                .asList(PricingConfigurationObjectMother.buildPricingBaseAccomTypeSeason());
        pricingBaseAccomTypes.forEach(pbat -> pbat.setProductID(1));

        Product independentProduct = new Product();
        independentProduct.setId(1);
        independentProduct.setCode(Product.INDEPENDENT_PRODUCT_CODE);

        Map<String, Object> queryParams = getQueryParamMapForPropertyID();
        when(tenantCrudService.<PricingBaseAccomType>findByNamedQuery(TransientPricingBaseAccomType.FIND_PRICEABLE_SEASONS_BY_PROPERTY_ID,
                queryParams)).thenReturn(pricingBaseAccomTypes);
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.INDEPENDENT_PRODUCTS_ENABLED)).thenReturn(true);
        when(tenantCrudService.findByNamedQuerySingleResult(Product.GET_BY_ID,
                QueryParameter.with("productId", pricingBaseAccomTypes.get(0).getProductID()).parameters()))
                .thenReturn(independentProduct);
        service.saveTransientPricingBaseAccomTypes(pricingBaseAccomTypes);

        verify(agileRatesConfigurationService).validateIndependentProducts();
        verify(tenantCrudService).save(pricingBaseAccomTypes);
        verify(taxInclusiveMigrationService).handleTransientPriceExcludedBaseRoomTypeOnSupplementChange();
        verify(tenantCrudService, never()).findByNamedQuery(TransientPricingBaseAccomType.FIND_PRICEABLE_SEASONS_BY_PROPERTY_ID,
                queryParams);
        verify(tenantCrudService, never()).delete(pricingBaseAccomTypes);
        verify(taxInclusiveMigrationService).handleTransientPriceExcludedBaseRoomTypeOnSupplementChange();
        verify(syncEventAggregatorService).registerSyncEvent(SyncEvent.PRICING_CONFIG_CHANGED);
    }

    @Test
    public void getRoomTypesForProducts() {
        Product product = new Product();
        AccomType accomType = new AccomType();
        ProductAccomType productAccomType = new ProductAccomType();
        productAccomType.setAccomType(accomType);

        when(tenantCrudService.findByNamedQuery(ProductAccomType.BY_PRODUCT,
                QueryParameter.with("product", product).parameters()))
                .thenReturn(new ArrayList<>(Arrays.asList(productAccomType)));

        List<AccomType> result = service.getRoomTypesForProduct(product);
        assertSame(accomType, result.get(0));
    }

    private void verifyMinPriceDiffDefault(List<MinPriceDifferentDTO> minPriceDifferentDTOList) {
        assertEquals(3, minPriceDifferentDTOList.size());
        assertMinPriceDiff(minPriceDifferentDTOList.get(0), "STANDARD", "DELUXE", "Default", BigDecimal.valueOf(23.55));
        assertMinPriceDiff(minPriceDifferentDTOList.get(1), "DELUXE", "JUNIOR", "Default", BigDecimal.valueOf(25.55));
        assertMinPriceDiff(minPriceDifferentDTOList.get(2), "JUNIOR", "SUITES", "Default", BigDecimal.valueOf(27.55));
    }

    private void verifyMinPriceDiffDefaultWithSeasonal(List<MinPriceDifferentDTO> minPriceDifferentDTOList) {
        assertEquals(4, minPriceDifferentDTOList.size());
        assertMinPriceDiff(minPriceDifferentDTOList.get(0), "STANDARD", "DELUXE", "Default", BigDecimal.valueOf(23.55));
        assertMinPriceDiff(minPriceDifferentDTOList.get(1), "DELUXE", "JUNIOR", "Default", BigDecimal.valueOf(25.55));
        assertMinPriceDiff(minPriceDifferentDTOList.get(2), "JUNIOR", "SUITES", "Default", BigDecimal.valueOf(27.55));
        assertMinPriceDiff(minPriceDifferentDTOList.get(3), "STANDARD", "DELUXE", "Seasonal", BigDecimal.valueOf(23.55));
    }

    private void assertMinPriceDiff(MinPriceDifferentDTO minPriceDifferentDTO, String roomClassCode, String nextHigherRankRoomClass, String category, BigDecimal pricediff) {
        assertEquals(minPriceDifferentDTO.getRoomClassCode(), roomClassCode);
        assertEquals(minPriceDifferentDTO.getNextHigherRankRoomClass(), nextHigherRankRoomClass);
        assertEquals(minPriceDifferentDTO.getSundayPrice(), pricediff);
        assertEquals(minPriceDifferentDTO.getMondayPrice(), pricediff);
        assertEquals(minPriceDifferentDTO.getTuesdayPrice(), pricediff);
        assertEquals(minPriceDifferentDTO.getWednesdayPrice(), pricediff);
        assertEquals(minPriceDifferentDTO.getThursdayPrice(), pricediff);
        assertEquals(minPriceDifferentDTO.getFridayPrice(), pricediff);
        assertEquals(minPriceDifferentDTO.getSaturdayPrice(), pricediff);
        assertEquals(minPriceDifferentDTO.getCategory(), category);
    }

    private List<AccomClassMinPriceDiff> getMinPriceDiffTestData() {

        AccomClass accomClass1 = new AccomClass();
        accomClass1.setId(1);
        accomClass1.setCode("STANDARD");
        AccomClass accomClass2 = new AccomClass();
        accomClass2.setId(2);
        accomClass2.setCode("DELUXE");
        AccomClass accomClass3 = new AccomClass();
        accomClass3.setId(3);
        accomClass3.setCode("JUNIOR");
        AccomClass accomClass4 = new AccomClass();
        accomClass4.setId(4);
        accomClass4.setCode("SUITES");

        AccomClassPriceRank priceRank1 = new AccomClassPriceRank(accomClass1, accomClass2, true);
        priceRank1.setId(1);
        AccomClassPriceRank priceRank2 = new AccomClassPriceRank(accomClass2, accomClass3, true);
        priceRank2.setId(2);
        AccomClassPriceRank priceRank3 = new AccomClassPriceRank(accomClass3, accomClass4, true);
        priceRank3.setId(3);

        List<AccomClassMinPriceDiff> accomClassMinPriceDiffList = new ArrayList<>();
        accomClassMinPriceDiffList.add(getAccomClassMinPriceDiffData(priceRank1, BigDecimal.valueOf(23.55)));
        accomClassMinPriceDiffList.add(getAccomClassMinPriceDiffData(priceRank2, BigDecimal.valueOf(25.55)));
        accomClassMinPriceDiffList.add(getAccomClassMinPriceDiffData(priceRank3, BigDecimal.valueOf(27.55)));

        return accomClassMinPriceDiffList;
    }

    private AccomClassMinPriceDiff getAccomClassMinPriceDiffData(AccomClassPriceRank accomClassPriceRank, BigDecimal priceDiff) {

        AccomClassMinPriceDiff accomClassMinPriceDiff = new AccomClassMinPriceDiff();
        accomClassMinPriceDiff.setAccomClassPriceRank(accomClassPriceRank);
        accomClassMinPriceDiff.setSundayDiffWithTax(priceDiff);
        accomClassMinPriceDiff.setMondayDiffWithTax(priceDiff);
        accomClassMinPriceDiff.setTuesdayDiffWithTax(priceDiff);
        accomClassMinPriceDiff.setWednesdayDiffWithTax(priceDiff);
        accomClassMinPriceDiff.setThursdayDiffWithTax(priceDiff);
        accomClassMinPriceDiff.setFridayDiffWithTax(priceDiff);
        accomClassMinPriceDiff.setSaturdayDiffWithTax(priceDiff);

        return accomClassMinPriceDiff;
    }

    private List getSeasonalMinPriceDiffTestData() {

        List<AccomClassMinPriceDiffSeason> accomClassMinPriceDiffSeasons = new ArrayList<>();
        BigDecimal priceDiff = BigDecimal.valueOf(23.55);
        AccomClassMinPriceDiffSeason season = new AccomClassMinPriceDiffSeason();

        AccomClass accomClass1 = new AccomClass();
        accomClass1.setId(1);
        accomClass1.setCode("STANDARD");
        AccomClass accomClass2 = new AccomClass();
        accomClass2.setId(2);
        accomClass2.setCode("DELUXE");

        AccomClassPriceRank priceRank1 = new AccomClassPriceRank(accomClass1, accomClass2, true);
        priceRank1.setId(1);

        season.setAccomClassPriceRank(priceRank1);
        season.setStartDate(LocalDate.now());
        season.setEndDate(LocalDate.now().plusDays(10));
        season.setSundayDiffWithTax(priceDiff);
        season.setMondayDiffWithTax(priceDiff);
        season.setTuesdayDiffWithTax(priceDiff);
        season.setWednesdayDiffWithTax(priceDiff);
        season.setThursdayDiffWithTax(priceDiff);
        season.setSaturdayDiffWithTax(priceDiff);
        season.setFridayDiffWithTax(priceDiff);
        accomClassMinPriceDiffSeasons.add(season);

        return accomClassMinPriceDiffSeasons;
    }

    @Test
    void startAutomatePricingConfigurationJob() {
        service.startAutomatePricingConfigurationJob(5);
        verify(jobService).startGuaranteedNewInstance(eq(JobName.AutomatePricingConfigurationsJob), anyMap());
    }

    @Test
    void updateFloorCeilingDefaultMacroValueTest() {
        inject(service, "tenantCrudService", tenantCrudService());

        service.updateFloorCeilingMacroValue("NUM_SEASONS", "2");

        assertEquals("2", getValueForNumSeasons());

        service.updateFloorCeilingMacroValue("NUM_SEASONS", "4");

        assertEquals("4", getValueForNumSeasons());
    }

    private Object getValueForNumSeasons() {
        return tenantCrudService().findByNativeQuerySingleResult("Select Parameter_Value from Sas_Macros_Parameters " +
                        " where Macro_Name = :macroName and Parameter_Name = :paramName ",
                QueryParameter.with("macroName", "rm_conf_floor_ceil")
                        .and("paramName", "NUM_SEASONS").parameters());
    }

    @Test
    void insertFloorCeilingMacroValues_IfMissing() {
        when(tenantCrudService.findByNamedQuerySingleResult(SASMacrosParameters.COUNT_BY_MACRO_NAME,
                QueryParameter.with("macroName", "rm_conf_floor_ceil").parameters())).thenReturn(12L);
        service.insertFloorCeilingMacroValues();
        verify(tenantCrudService).executeUpdateByNativeQuery(createParameterInsertQuery());
    }

    @Test
    void doNotInsertFloorCeilingMacroValues_IfNotMissing() {
        when(tenantCrudService.findByNamedQuerySingleResult(SASMacrosParameters.COUNT_BY_MACRO_NAME,
                QueryParameter.with("macroName", "rm_conf_floor_ceil").parameters())).thenReturn(14L);
        service.insertFloorCeilingMacroValues();
        verify(tenantCrudService).findByNamedQuerySingleResult(SASMacrosParameters.COUNT_BY_MACRO_NAME,
                QueryParameter.with("macroName", "rm_conf_floor_ceil").parameters());
        verifyNoMoreInteractions(tenantCrudService);
    }

    @Test
    void configureCurrentConfigurationsUsingDraftValues_applyCentralRMSWorkAround() {
        Product product = new Product();
        product.setId(1);
        when(configParamsService.getBooleanParameterValue(CENTRAL_RMS_AVAILABLE)).thenReturn(true);
        when(configParamsService.getBooleanParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED)).thenReturn(true);

        List<PricingBaseAccomType> configsBeforeDraftSave = List.of(PricingConfigurationObjectMother.buildPricingBaseAccomType());
        List<PricingBaseAccomType> configsAfterDraftSave = List.of(PricingConfigurationObjectMother.buildPricingBaseAccomTypeWithTax());

        when(tenantCrudService.findByNamedQuery(eq(TransientPricingBaseAccomType.FIND_BASE_AT_FOR_PRICEABLE_AC), anyMap()))
                .thenReturn(new ArrayList<>(configsBeforeDraftSave))
                .thenReturn(new ArrayList<>(configsAfterDraftSave));
        when(tenantCrudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT)).thenReturn(product);

        service.configureCurrentConfigurationsUsingDraftValues(pricingConfigurationDTO);

        verify(syncCentralRmsService).syncTransientPricingConfigurationData(configsBeforeDraftSave, true);
        verify(syncCentralRmsService).syncTransientPricingConfigurationData(configsAfterDraftSave, false);
    }

    @Test
    void configureCurrentConfigurationsUsingDraftValues_doNotApplyCentralRMSWorkAround() {
        Product product = new Product();
        product.setId(1);
        when(tenantCrudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT)).thenReturn(product);
        service.configureCurrentConfigurationsUsingDraftValues(pricingConfigurationDTO);
        verify(tenantCrudService, never()).findByNamedQuery(eq(TransientPricingBaseAccomType.FIND_BASE_AT_FOR_PRICEABLE_AC), any());
        verifyNoInteractions(syncCentralRmsService);
    }

    @Test
    void getAllTransientPricingBaseAccomTypesForBAR() {
        List<PricingBaseAccomType> configs = List.of(PricingConfigurationObjectMother.buildPricingBaseAccomTypeWithTax());

        when(tenantCrudService.findByNamedQuery(eq(TransientPricingBaseAccomType.FIND_BASE_AT_FOR_PRICEABLE_AC_BAR), any())).thenReturn(new ArrayList<>(configs));

        List<PricingBaseAccomType> result = service.getAllTransientPricingBaseAccomTypesForBAR();

        assertEquals(configs, result);
        verify(tenantCrudService).findByNamedQuery(eq(TransientPricingBaseAccomType.FIND_BASE_AT_FOR_PRICEABLE_AC_BAR), any());
    }

    @Test
    void getTransientPricingBaseAccomTypeSeasonsForBAR() {
        PricingBaseAccomType defaultConfig = PricingConfigurationObjectMother.buildPricingBaseAccomTypeWithTax();
        PricingBaseAccomType seasonConfig = PricingConfigurationObjectMother.buildPricingBaseAccomTypeSeason();
        List<PricingBaseAccomType> configs = List.of(defaultConfig, seasonConfig);

        when(tenantCrudService.findByNamedQuery(eq(TransientPricingBaseAccomType.FIND_BASE_AT_FOR_PRICEABLE_AC_BAR), any())).thenReturn(new ArrayList<>(configs));

        List<PricingBaseAccomType> result = service.getTransientPricingBaseAccomTypeSeasonsForBAR();

        assertEquals(List.of(seasonConfig), result);
        verify(tenantCrudService).findByNamedQuery(eq(TransientPricingBaseAccomType.FIND_BASE_AT_FOR_PRICEABLE_AC_BAR), any());
    }

    @Test
    void forceSyncPricingConfigsToCentralRMSWorkAround() {
        List<PricingBaseAccomType> configs = List.of(PricingConfigurationObjectMother.buildPricingBaseAccomTypeWithTax());
        when(tenantCrudService.findByNamedQuery(eq(TransientPricingBaseAccomType.FIND_BASE_AT_FOR_PRICEABLE_AC_BAR), any())).thenReturn(new ArrayList<>(configs));

        service.forceSyncPricingConfigsToCentralRMSWorkAround();

        verify(syncCentralRmsService).truncateTransientPricingConfigurationData();
        verify(syncCentralRmsService).syncTransientPricingConfigurationData(configs, false);

    }

    @Test
    public void getNotOverriddenCeilingFloorValuesTest() {
        Integer id = 1;
        LocalDate now = LocalDate.now();
        TransientPricingBaseAccomType transientPricingBaseAccomType = new TransientPricingBaseAccomType();
        when(tenantCrudService.findByNamedQuery(TransientPricingBaseAccomType.FIND_DEFAULT_AND_SEASON_FOR_PRODUCT_AND_ACCOM_TYPE,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId())
                        .and("productId", id)
                        .and("accomTypeId", id)
                        .and("date", now)
                        .parameters()))
                .thenReturn(List.of(transientPricingBaseAccomType));
        service.getNotOverriddenCeilingFloorValues(id, id, now);
        verify(agileRatesConfigurationService).retrieveCeilingFloorForParticularDate(transientPricingBaseAccomType, now);
    }

    @Test
    public void getNotOverriddenCeilingFloorValuesSeasonTest() {
        Integer id = 1;
        LocalDate now = LocalDate.now();
        TransientPricingBaseAccomType transientPricingBaseAccomType1 = new TransientPricingBaseAccomType();
        TransientPricingBaseAccomType transientPricingBaseAccomType2 = new TransientPricingBaseAccomType();
        transientPricingBaseAccomType2.setStartDate(now);
        when(tenantCrudService.findByNamedQuery(TransientPricingBaseAccomType.FIND_DEFAULT_AND_SEASON_FOR_PRODUCT_AND_ACCOM_TYPE,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId())
                        .and("productId", id)
                        .and("accomTypeId", id)
                        .and("date", now)
                        .parameters()))
                .thenReturn(List.of(transientPricingBaseAccomType1, transientPricingBaseAccomType2));
        service.getNotOverriddenCeilingFloorValues(id, id, now);
        verify(agileRatesConfigurationService).retrieveCeilingFloorForParticularDate(transientPricingBaseAccomType2, now);
    }

    @Test
    public void getBaseAccomTypeForProvidedNonBaseAccomTypeTest() {
        AccomType baseAccomType = new AccomType();
        baseAccomType.setId(1);
        AccomType nonBaseAccomType = new AccomType();
        nonBaseAccomType.setId(2);
        AccomClass accomClass = new AccomClass();
        accomClass.setId(1);
        accomClass.setAccomTypes(Set.of(baseAccomType, nonBaseAccomType));
        baseAccomType.setAccomClass(accomClass);
        nonBaseAccomType.setAccomClass(accomClass);
        PricingBaseAccomType pricingBaseAccomType = new TransientPricingBaseAccomType();
        pricingBaseAccomType.setAccomType(baseAccomType);
        when(tenantCrudService.findByNamedQuery(TransientPricingBaseAccomType.FIND_ALL_BY_PROPERTY_ID, QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters()))
                .thenReturn(List.of(pricingBaseAccomType));
        AccomType actualAccomType = service.getBaseAccomTypeForProvidedAccomType(nonBaseAccomType);
        assertEquals(baseAccomType, actualAccomType);
    }

    @Test
    public void getBaseAccomTypeForProvidedBaseAccomTypeTest() {
        AccomType baseAccomType = new AccomType();
        baseAccomType.setId(1);
        AccomType nonBaseAccomType = new AccomType();
        nonBaseAccomType.setId(2);
        AccomClass accomClass = new AccomClass();
        accomClass.setId(1);
        accomClass.setAccomTypes(Set.of(baseAccomType, nonBaseAccomType));
        baseAccomType.setAccomClass(accomClass);
        nonBaseAccomType.setAccomClass(accomClass);
        PricingBaseAccomType pricingBaseAccomType = new TransientPricingBaseAccomType();
        pricingBaseAccomType.setAccomType(baseAccomType);
        when(tenantCrudService.findByNamedQuery(TransientPricingBaseAccomType.FIND_ALL_BY_PROPERTY_ID, QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters()))
                .thenReturn(List.of(pricingBaseAccomType));
        AccomType actualAccomType = service.getBaseAccomTypeForProvidedAccomType(baseAccomType);
        assertEquals(baseAccomType, actualAccomType);
    }

    @Test
    void testSaveMinimumPriceDifferentialData() {
        when(tenantCrudService.deleteAll(AccomClassMinPriceDiff.class)).thenReturn(1);
        when(taxService.findTax()).thenReturn(mockTax());
        when(tenantCrudService.findAll(AccomClassPriceRank.class)).thenReturn(List.of(mockAccomClassPriceRank(1), mockAccomClassPriceRank(2)));
        when(tenantCrudService.save(Mockito.anyList())).thenReturn(List.of(mockAccomClassMinPriceDiff(1, BigDecimal.valueOf(100.00),
                BigDecimal.valueOf(120.00))));
        service.saveMinimumPriceDifferentialData(BigDecimal.valueOf(100.00), null);
        verify(tenantCrudService).deleteAll(AccomClassMinPriceDiff.class);
        verify(taxService).findTax();
        verify(tenantCrudService).findAll(AccomClassPriceRank.class);
        verify(tenantCrudService).save(Mockito.anyList());
    }

    @Test
    void testSaveMinimumPriceDifferentialDataWithCallToCurrencyService() {
        when(tenantCrudService.deleteAll(AccomClassMinPriceDiff.class)).thenReturn(1);
        when(taxService.findTax()).thenReturn(mockTax());
        when(currencyService.toYieldConversionFactor("USD")).thenReturn(2.00);
        when(tenantCrudService.findAll(AccomClassPriceRank.class)).thenReturn(List.of(mockAccomClassPriceRank(1), mockAccomClassPriceRank(2)));
        when(tenantCrudService.save(Mockito.anyList())).thenReturn(List.of(mockAccomClassMinPriceDiff(1, BigDecimal.valueOf(100.00),
                BigDecimal.valueOf(120.00))));
        service.saveMinimumPriceDifferentialData(BigDecimal.valueOf(100.00), "USD");
        verify(tenantCrudService).deleteAll(AccomClassMinPriceDiff.class);
        verify(taxService).findTax();
        verify(currencyService).toYieldConversionFactor("USD");
        verify(tenantCrudService).findAll(AccomClassPriceRank.class);
        verify(tenantCrudService).save(Mockito.anyList());
    }

    @Test
    void testGetAccomClassMinPriceDiffResponse() {
        when(tenantCrudService.findAll(AccomClassMinPriceDiff.class))
                .thenReturn(List.of(mockAccomClassMinPriceDiff(1, BigDecimal.valueOf(100.00), BigDecimal.valueOf(110.00)),
                        mockAccomClassMinPriceDiff(2, BigDecimal.valueOf(110.00), BigDecimal.valueOf(140.00))));
        List<AccomClassMinPriceDiffResponse> responses = service.getAccomClassMinPriceDiffResponse();
        Assertions.assertEquals(2, responses.size());
        verify(tenantCrudService).findAll(AccomClassMinPriceDiff.class);
    }

    @Test
    void testRemoveAllAccommodationClassMinPriceDiff() {
        when(tenantCrudService.deleteAll(AccomClassMinPriceDiff.class)).thenReturn(1);
        service.removeAllAccommodationClassMinPriceDiff();
        verify(tenantCrudService).deleteAll(AccomClassMinPriceDiff.class);
    }


    @Test
    public void removeHierarchiesTest() {
        service.removeHierarchies(Collections.emptyList());
        verify(agileRatesConfigurationService).removeHierarchies(anyList());
    }

    @Test
    void testSaveMinimumPriceDifferentialData_RoundingToFloorValue() {
        when(tenantCrudService.deleteAll(AccomClassMinPriceDiff.class)).thenReturn(1);
        when(taxService.findTax()).thenReturn(mockTax());
        when(tenantCrudService.findAll(AccomClassPriceRank.class)).thenReturn(List.of(mockAccomClassPriceRank(1), mockAccomClassPriceRank(2)));
        when(tenantCrudService.save(Mockito.anyList())).thenReturn(List.of(mockAccomClassMinPriceDiff(1, BigDecimal.valueOf(100.00),
                BigDecimal.valueOf(120.00))));
        ArgumentCaptor<List<AccomClassMinPriceDiff>> captor = ArgumentCaptor.forClass(List.class);
        service.saveMinimumPriceDifferentialData(BigDecimal.valueOf(10.49), null);
        verify(tenantCrudService).deleteAll(AccomClassMinPriceDiff.class);
        verify(taxService).findTax();
        verify(tenantCrudService).findAll(AccomClassPriceRank.class);
        verify(tenantCrudService).save(captor.capture());
        List<AccomClassMinPriceDiff> value = captor.getValue();
        assertEquals(10.00, value.get(0).getSundayDiffWithTax().doubleValue());
    }

    @Test
    void testSaveMinimumPriceDifferentialData_RoundingToCeilingValue() {
        when(tenantCrudService.deleteAll(AccomClassMinPriceDiff.class)).thenReturn(1);
        when(taxService.findTax()).thenReturn(mockTax());
        when(tenantCrudService.findAll(AccomClassPriceRank.class)).thenReturn(List.of(mockAccomClassPriceRank(1), mockAccomClassPriceRank(2)));
        when(tenantCrudService.save(Mockito.anyList())).thenReturn(List.of(mockAccomClassMinPriceDiff(1, BigDecimal.valueOf(100.00),
                BigDecimal.valueOf(120.00))));
        ArgumentCaptor<List<AccomClassMinPriceDiff>> captor = ArgumentCaptor.forClass(List.class);
        service.saveMinimumPriceDifferentialData(BigDecimal.valueOf(10.51), null);
        verify(tenantCrudService).deleteAll(AccomClassMinPriceDiff.class);
        verify(taxService).findTax();
        verify(tenantCrudService).findAll(AccomClassPriceRank.class);
        verify(tenantCrudService).save(captor.capture());
        List<AccomClassMinPriceDiff> value = captor.getValue();
        assertEquals(11.00, value.get(0).getSundayDiffWithTax().doubleValue());
    }

    @Test
    void testSaveMinimumPriceDifferentialData_RoundingToCeilingValueIfInputIsMidpoint() {
        when(tenantCrudService.deleteAll(AccomClassMinPriceDiff.class)).thenReturn(1);
        when(taxService.findTax()).thenReturn(mockTax());
        when(tenantCrudService.findAll(AccomClassPriceRank.class)).thenReturn(List.of(mockAccomClassPriceRank(1), mockAccomClassPriceRank(2)));
        when(tenantCrudService.save(Mockito.anyList())).thenReturn(List.of(mockAccomClassMinPriceDiff(1, BigDecimal.valueOf(100.00),
                BigDecimal.valueOf(120.00))));
        ArgumentCaptor<List<AccomClassMinPriceDiff>> captor = ArgumentCaptor.forClass(List.class);
        service.saveMinimumPriceDifferentialData(BigDecimal.valueOf(10.50), null);
        verify(tenantCrudService).deleteAll(AccomClassMinPriceDiff.class);
        verify(taxService).findTax();
        verify(tenantCrudService).findAll(AccomClassPriceRank.class);
        verify(tenantCrudService).save(captor.capture());
        List<AccomClassMinPriceDiff> value = captor.getValue();
        assertEquals(11.00, value.get(0).getSundayDiffWithTax().doubleValue());
    }

    @Test
    void testUpdateBarProductName() {
        Product product = new Product();
        product.setType(ProductType.DAILY.name());
        product.setName("OLD_NAME");
        when(tenantCrudService.findByNamedQuerySingleResult(Product.GET_BY_ID,
                QueryParameter.with("productId", 1).parameters())).thenReturn(product);
        service.updateBarProduct("OLD_NAME", "NEW_NAME", "rename_product");
        assertEquals("NEW_NAME", product.getName());
    }

    @Test
    void testUpdateBarProductNameDoesNotUpdateNonDailyProducts() {
        Product product = new Product();
        product.setType(ProductType.EXTENDEDSTAY.name());
        product.setName("OLD_NAME");
        when(tenantCrudService.findByNamedQuerySingleResult(Product.GET_BY_ID,
                QueryParameter.with("productId", 1).parameters())).thenReturn(product);
        assertThrows(TetrisException.class, () -> service.updateBarProduct("OLD_NAME", "NEW_NAME", "rename_product"));
    }

    @Test
    void testUpdateProductName() {
        Product product = new Product();
        product.setType(ProductType.DAILY.name());
        product.setName("OLD_NAME");
        when(tenantCrudService.findByNamedQuerySingleResult(Product.GET_BY_NAME,
                QueryParameter.with("name", "OLD_NAME").parameters())).thenReturn(product);
        service.updateProductName("OLD_NAME", "NEW_NAME", "rename_product");
        assertEquals("NEW_NAME", product.getName());
    }

    @Test
    public void shouldCreateDefaultGPCeilingFloorConfigUsingPricingCeilingFloor() {
        List<PricingBaseAccomType> pricingBaseAccomTypes = setDefaultGroupPricingAccomType(false, AdjustmentType.FIXED, AdjustmentType.FIXED);

        service.syncDefaultGPCeilingFloorWithPrimaryPricedProduct(pricingBaseAccomTypes);

        verifyCreateDefaultGPCeilingFloorConfigUsingPricingCeilingFloor();
    }

    @Test
    public void shouldUpdateDefaultGPCeilingFloorConfigUsingPricingCeilingFloor() {
        List<PricingBaseAccomType> pricingBaseAccomTypes = setDefaultGroupPricingAccomType(false, AdjustmentType.FIXED, AdjustmentType.FIXED);
        tenantCrudServiceReal.save(pricingBaseAccomTypes);

        service.syncDefaultGPCeilingFloorWithPrimaryPricedProduct(pricingBaseAccomTypes);
        tenantCrudServiceReal.flushAndClear();

        List<PricingBaseAccomType> pricingBaseAccomTypeList = tenantCrudServiceReal.findByNamedQuery(TransientPricingBaseAccomType.FIND_DEFAULT_BY_PROPERTY_ID, getQueryParamMapForPropertyID());
        PricingBaseAccomType pricingBaseAccomType = pricingBaseAccomTypeList.get(0);
        pricingBaseAccomType.setTuesdayFloorRateWithTax(new BigDecimal("225.55"));
        pricingBaseAccomType.setWednesdayFloorRateWithTax(new BigDecimal("245.40"));
        pricingBaseAccomType.setFridayCeilingRateWithTax(new BigDecimal("400.00"));
        pricingBaseAccomType.setMondayCeilingRateWithTax(new BigDecimal("300.00"));

        service.syncDefaultGPCeilingFloorWithPrimaryPricedProduct(Arrays.asList(pricingBaseAccomType));
        List<PricingBaseAccomType> groupPricing = tenantCrudServiceReal.findByNamedQuery(GroupPricingBaseAccomType.FIND_BY_PROPERTY_ID, getQueryParamMapForPropertyID());

        assertFalse(groupPricing.isEmpty());

        verifyUpdateDefaultGPCeilingFloorConfigUsingPricingCeilingFloor();
    }

    @Test
    public void shouldSyncGPCeilingFloorForPrimaryPricedProductWhenFixedAdjustmentIsAppliedToFloorAndCeiling() {
        List<PricingBaseAccomType> pricingBaseAccomTypes = setDefaultGroupPricingAccomType(true, AdjustmentType.FIXED, AdjustmentType.FIXED);

        service.syncDefaultGPCeilingFloorWithPrimaryPricedProduct(pricingBaseAccomTypes);

        verifyGPCeiligFloorPersistedWithFixedAdjustmentType();
    }

    private void verifyGPCeiligFloorPersistedWithFixedAdjustmentType() {
        List<PricingBaseAccomType> groupPricing = tenantCrudServiceReal.findByNamedQuery(GroupPricingBaseAccomType.FIND_BY_PROPERTY_ID, getQueryParamMapForPropertyID());

        assertFalse(groupPricing.isEmpty(), "Group Pricing season should be available in the database");
        assertEquals(groupPricing.get(0).getSundayCeilingRateWithTax(), new BigDecimal("340.00"));
        assertEquals(groupPricing.get(0).getSundayFloorRateWithTax(), new BigDecimal("210.00"));

        assertEquals(groupPricing.get(0).getMondayCeilingRateWithTax(), new BigDecimal("390.00"));
        assertEquals(groupPricing.get(0).getMondayFloorRateWithTax(), new BigDecimal("220.00"));

        assertEquals(groupPricing.get(0).getTuesdayCeilingRateWithTax(), new BigDecimal("390.00"));
        assertEquals(groupPricing.get(0).getTuesdayFloorRateWithTax(), new BigDecimal("230.00"));

        assertEquals(groupPricing.get(0).getWednesdayCeilingRateWithTax(), new BigDecimal("390.00"));
        assertEquals(groupPricing.get(0).getWednesdayFloorRateWithTax(), new BigDecimal("240.00"));

        assertEquals(groupPricing.get(0).getThursdayCeilingRateWithTax(), new BigDecimal("390.00"));
        assertEquals(groupPricing.get(0).getThursdayFloorRateWithTax(), new BigDecimal("250.00"));

        assertEquals(groupPricing.get(0).getFridayCeilingRateWithTax(), new BigDecimal("365.00"));
        assertEquals(groupPricing.get(0).getFridayFloorRateWithTax(), new BigDecimal("215.00"));

        assertEquals(groupPricing.get(0).getSaturdayCeilingRateWithTax(), new BigDecimal("365.00"));
        assertEquals(groupPricing.get(0).getSaturdayFloorRateWithTax(), new BigDecimal("225.00"));
    }

    @Test
    public void shouldGPCeilingFloorForPrimaryPricedProductWhenPercentageAdjustmentIsAppliedToFloorAndCeiling() {

        List<PricingBaseAccomType> pricingBaseAccomTypes = setDefaultGroupPricingAccomType(true, AdjustmentType.PERCENTAGE, AdjustmentType.PERCENTAGE);

        service.syncDefaultGPCeilingFloorWithPrimaryPricedProduct(pricingBaseAccomTypes);

        verifyGpCeilingFloorPersistedWithPercentangeAdjustment();
    }

    private void verifyGpCeilingFloorPersistedWithPercentangeAdjustment() {
        List<PricingBaseAccomType> groupPricing = tenantCrudServiceReal.findByNamedQuery(GroupPricingBaseAccomType.FIND_BY_PROPERTY_ID, getQueryParamMapForPropertyID());

        assertFalse(groupPricing.isEmpty(), "Group Pricing season should be available in the database");
        assertEquals(groupPricing.get(0).getSundayCeilingRateWithTax(), new BigDecimal("384.00"));
        assertEquals(groupPricing.get(0).getMondayCeilingRateWithTax(), new BigDecimal("444.00"));
        assertEquals(groupPricing.get(0).getTuesdayCeilingRateWithTax(), new BigDecimal("444.00"));
        assertEquals(groupPricing.get(0).getWednesdayCeilingRateWithTax(), new BigDecimal("444.00"));
        assertEquals(groupPricing.get(0).getThursdayCeilingRateWithTax(), new BigDecimal("444.00"));
        assertEquals(groupPricing.get(0).getFridayCeilingRateWithTax(), new BigDecimal("414.00"));
        assertEquals(groupPricing.get(0).getSaturdayCeilingRateWithTax(), new BigDecimal("414.00"));

        assertEquals(groupPricing.get(0).getSundayFloorRateWithTax(), new BigDecimal("220.00"));
        assertEquals(groupPricing.get(0).getMondayFloorRateWithTax(), new BigDecimal("231.00"));
        assertEquals(groupPricing.get(0).getTuesdayFloorRateWithTax(), new BigDecimal("242.00"));
        assertEquals(groupPricing.get(0).getWednesdayFloorRateWithTax(), new BigDecimal("253.00"));
        assertEquals(groupPricing.get(0).getThursdayFloorRateWithTax(), new BigDecimal("264.00"));
        assertEquals(groupPricing.get(0).getFridayFloorRateWithTax(), new BigDecimal("225.50"));
        assertEquals(groupPricing.get(0).getSaturdayFloorRateWithTax(), new BigDecimal("236.50"));
    }

    @Test
    public void shouldSyncGPCeilingFloorForPrimaryPricedProductWhenFixedAdjustmentIsAppliedToFloorAndPercentageAdjustmentIsAppliedToCeil() {
        List<PricingBaseAccomType> pricingBaseAccomTypes = setDefaultGroupPricingAccomType(true, AdjustmentType.FIXED, AdjustmentType.PERCENTAGE);

        service.syncDefaultGPCeilingFloorWithPrimaryPricedProduct(pricingBaseAccomTypes);

        verifyGpCeilingFloorPersistedWithPercentangeAndFixedAdjustment();

    }

    private void verifyGpCeilingFloorPersistedWithPercentangeAndFixedAdjustment() {
        List<PricingBaseAccomType> groupPricing = tenantCrudServiceReal.findByNamedQuery(GroupPricingBaseAccomType.FIND_BY_PROPERTY_ID, getQueryParamMapForPropertyID());

        assertFalse(groupPricing.isEmpty());

        assertEquals(groupPricing.get(0).getSundayCeilingRateWithTax(), new BigDecimal("384.00"));
        assertEquals(groupPricing.get(0).getSundayFloorRateWithTax(), new BigDecimal("210.00"));

        assertEquals(groupPricing.get(0).getMondayCeilingRateWithTax(), new BigDecimal("444.00"));
        assertEquals(groupPricing.get(0).getMondayFloorRateWithTax(), new BigDecimal("220.00"));

        assertEquals(groupPricing.get(0).getTuesdayCeilingRateWithTax(), new BigDecimal("444.00"));
        assertEquals(groupPricing.get(0).getTuesdayFloorRateWithTax(), new BigDecimal("230.00"));

        assertEquals(groupPricing.get(0).getWednesdayCeilingRateWithTax(), new BigDecimal("444.00"));
        assertEquals(groupPricing.get(0).getWednesdayFloorRateWithTax(), new BigDecimal("240.00"));

        assertEquals(groupPricing.get(0).getThursdayCeilingRateWithTax(), new BigDecimal("444.00"));
        assertEquals(groupPricing.get(0).getThursdayFloorRateWithTax(), new BigDecimal("250.00"));

        assertEquals(groupPricing.get(0).getFridayCeilingRateWithTax(), new BigDecimal("414.00"));
        assertEquals(groupPricing.get(0).getFridayFloorRateWithTax(), new BigDecimal("215.00"));

        assertEquals(groupPricing.get(0).getSaturdayCeilingRateWithTax(), new BigDecimal("414.00"));
        assertEquals(groupPricing.get(0).getSaturdayFloorRateWithTax(), new BigDecimal("225.00"));
    }

    @Test
    public void shouldCreateNewSeasonInGPCeilingFloorUsingPrimaryPricedProductCeilingFloorWhenFixedAdjustmentIsAppliedToFloorAndCeiling() {
        //Given
        List<PricingBaseAccomType> pricingBaseAccomTypes = setUpCommonDataForSyncGP(true, AdjustmentType.FIXED, AdjustmentType.FIXED);

        //Then
        service.syncGPCeilingFloorWithPrimaryPricedProductSeason(pricingBaseAccomTypes);

        //verify
        verifyCreateNewGPCeilingFloorRecordUsingPrimaryPricedProductCeilingFloorWithFixedAdjustmentType();
    }

    public void verifyCreateNewGPCeilingFloorRecordUsingPrimaryPricedProductCeilingFloorWithFixedAdjustmentType() {
        List<PricingBaseAccomType> groupPricing = tenantCrudServiceReal.findByNamedQuery(GroupPricingBaseAccomType.FIND_SEASONS_BY_PROPERTY_ID, getQueryParamMapForPropertyID());
        assertFalse(groupPricing.isEmpty(), "Group Pricing season should be available in the database");

        assertEquals(groupPricing.get(0).getSundayCeilingRateWithTax(), new BigDecimal(220.00).setScale(2));
        assertEquals(groupPricing.get(0).getSundayFloorRateWithTax(), new BigDecimal(100.00).setScale(2));

        assertEquals(groupPricing.get(0).getMondayCeilingRateWithTax(), new BigDecimal(270.00).setScale(2));
        assertEquals(groupPricing.get(0).getMondayFloorRateWithTax(), new BigDecimal(110.00).setScale(2));

        assertEquals(groupPricing.get(0).getTuesdayCeilingRateWithTax(), new BigDecimal(270.00).setScale(2));
        assertEquals(groupPricing.get(0).getTuesdayFloorRateWithTax(), new BigDecimal(110.00).setScale(2));

        assertEquals(groupPricing.get(0).getWednesdayCeilingRateWithTax(), new BigDecimal(270.00).setScale(2));
        assertEquals(groupPricing.get(0).getWednesdayFloorRateWithTax(), new BigDecimal(110.00).setScale(2));

        assertEquals(groupPricing.get(0).getThursdayCeilingRateWithTax(), new BigDecimal(270.00).setScale(2));
        assertEquals(groupPricing.get(0).getThursdayFloorRateWithTax(), new BigDecimal(110.00).setScale(2));

        assertEquals(groupPricing.get(0).getFridayCeilingRateWithTax(), new BigDecimal(245.00).setScale(2));
        assertEquals(groupPricing.get(0).getFridayFloorRateWithTax(), new BigDecimal(105.00).setScale(2));

        assertEquals(groupPricing.get(0).getSaturdayCeilingRateWithTax(), new BigDecimal(245.00).setScale(2));
        assertEquals(groupPricing.get(0).getSaturdayFloorRateWithTax(), new BigDecimal(105.00).setScale(2));
    }

    @Test
    public void shouldCreateNewSeasonInGPCeilingFloorUsingPrimaryPricedProductCeilingFloorWhenPercentageAdjustmentIsAppliedToFloorAndCeiling() {
        //Given
        List<PricingBaseAccomType> pricingBaseAccomTypes = setUpCommonDataForSyncGP(true, AdjustmentType.PERCENTAGE, AdjustmentType.PERCENTAGE);

        //Then
        service.syncGPCeilingFloorWithPrimaryPricedProductSeason(pricingBaseAccomTypes);

        //verify
        verifyCreateNewGPCeilingFloorUsingPrimaryPricedProductCeilingFloorWithPercentageAdjustmentType();
    }

    public void verifyCreateNewGPCeilingFloorUsingPrimaryPricedProductCeilingFloorWithPercentageAdjustmentType() {
        List<PricingBaseAccomType> groupPricing = tenantCrudServiceReal.findByNamedQuery(GroupPricingBaseAccomType.FIND_SEASONS_BY_PROPERTY_ID, getQueryParamMapForPropertyID());

        assertFalse(groupPricing.isEmpty(), "Group Pricing season should be available in the database");
        assertEquals(groupPricing.get(0).getSundayCeilingRateWithTax(), new BigDecimal(240.00).setScale(2));
        assertEquals(groupPricing.get(0).getSundayFloorRateWithTax(), new BigDecimal(99.00).setScale(2));

        assertEquals(groupPricing.get(0).getMondayCeilingRateWithTax(), new BigDecimal(300.00).setScale(2));
        assertEquals(groupPricing.get(0).getMondayFloorRateWithTax(), new BigDecimal(110.00).setScale(2));

        assertEquals(groupPricing.get(0).getTuesdayCeilingRateWithTax(), new BigDecimal(300.00).setScale(2));
        assertEquals(groupPricing.get(0).getTuesdayFloorRateWithTax(), new BigDecimal(110.00).setScale(2));

        assertEquals(groupPricing.get(0).getWednesdayCeilingRateWithTax(), new BigDecimal(300.00).setScale(2));
        assertEquals(groupPricing.get(0).getWednesdayFloorRateWithTax(), new BigDecimal(110.00).setScale(2));

        assertEquals(groupPricing.get(0).getThursdayCeilingRateWithTax(), new BigDecimal(300.00).setScale(2));
        assertEquals(groupPricing.get(0).getThursdayFloorRateWithTax(), new BigDecimal(110.00).setScale(2));

        assertEquals(groupPricing.get(0).getFridayCeilingRateWithTax(), new BigDecimal(270.00).setScale(2));
        assertEquals(groupPricing.get(0).getFridayFloorRateWithTax(), new BigDecimal(104.50).setScale(2));

        assertEquals(groupPricing.get(0).getSaturdayCeilingRateWithTax(), new BigDecimal(270.00).setScale(2));
        assertEquals(groupPricing.get(0).getSaturdayFloorRateWithTax(), new BigDecimal(104.50).setScale(2));
    }

    @Test
    public void shouldCreateNewGPCeilingFloorUsingPrimaryPricedProductCeilingFloorWhenFixedAdjustmentIsAppliedToFloorAndPercentageAdjustmentIsAppliedToCeil() {

        List<PricingBaseAccomType> pricingBaseAccomTypes = setUpCommonDataForSyncGP(true, AdjustmentType.FIXED, AdjustmentType.PERCENTAGE);

        service.syncGPCeilingFloorWithPrimaryPricedProductSeason(pricingBaseAccomTypes);

        verifyCreateNewGPCeilingFloorRecordUsingPrimaryPricedProductCeilingFloorWithCombinationOfPercentageAndFixedAdjustmentType();
    }

    public void verifyCreateNewGPCeilingFloorRecordUsingPrimaryPricedProductCeilingFloorWithCombinationOfPercentageAndFixedAdjustmentType() {
        List<PricingBaseAccomType> savedGPSeason = tenantCrudServiceReal.findByNamedQuery(GroupPricingBaseAccomType.FIND_SEASONS_BY_PROPERTY_ID, getQueryParamMapForPropertyID());

        assertFalse(savedGPSeason.isEmpty(), "Group Pricing season should be available in the database");
        assertEquals(savedGPSeason.get(0).getSundayCeilingRateWithTax(), new BigDecimal(240.00).setScale(2), "Sunday ceiling rate should be equals to the expected ceil rate");
        assertEquals(savedGPSeason.get(0).getSundayFloorRateWithTax(), new BigDecimal(100.00).setScale(2), "Sunday floor rate should be equals to the expected floor rate");

        assertEquals(savedGPSeason.get(0).getMondayCeilingRateWithTax(), new BigDecimal(300.00).setScale(2), "Monday ceiling rate should be equals to the expected ceil rate");
        assertEquals(savedGPSeason.get(0).getMondayFloorRateWithTax(), new BigDecimal(110.00).setScale(2), "Monday floor rate should be equals to the expected floor rate");

        assertEquals(savedGPSeason.get(0).getTuesdayCeilingRateWithTax(), new BigDecimal(300.00).setScale(2), "Tuesday ceiling rate should be equals to the expected ceil rate");
        assertEquals(savedGPSeason.get(0).getMondayFloorRateWithTax(), new BigDecimal(110.00).setScale(2), "Tuesday floor rate should be equals to the expected floor rate");

        assertEquals(savedGPSeason.get(0).getWednesdayCeilingRateWithTax(), new BigDecimal(300.00).setScale(2), "Wednesday ceiling rate should be equals to the expected ceil rate");
        assertEquals(savedGPSeason.get(0).getMondayFloorRateWithTax(), new BigDecimal(110.00).setScale(2), "Wednesday floor rate should be equals to the expected floor rate");

        assertEquals(savedGPSeason.get(0).getThursdayCeilingRateWithTax(), new BigDecimal(300.00).setScale(2), "Thursday ceiling rate should be equals to the expected ceil rate");
        assertEquals(savedGPSeason.get(0).getMondayFloorRateWithTax(), new BigDecimal(110.00).setScale(2), "Thursday floor rate should be equals to the expected floor rate");

        assertEquals(savedGPSeason.get(0).getFridayCeilingRateWithTax(), new BigDecimal(270.00).setScale(2), "Friday ceiling rate should be equals to the expected ceil rate");
        assertEquals(savedGPSeason.get(0).getFridayFloorRateWithTax(), new BigDecimal(105.00).setScale(2), "Friday floor rate should be equals to the expected floor rate");

        assertEquals(savedGPSeason.get(0).getSaturdayCeilingRateWithTax(), new BigDecimal(270.00).setScale(2), "Saturday ceiling rate should be equals to the expected ceil rate");
        assertEquals(savedGPSeason.get(0).getSaturdayFloorRateWithTax(), new BigDecimal(105.00).setScale(2), "Saturday floor rate should be equals to the expected floor rate");
    }

    @Test
    public void shouldCreateNewSeasonInGPCeilingFloorUsingPrimaryPricedProductCeilingFloor() {
        List<PricingBaseAccomType> pricingBaseAccomTypes = setUpCommonDataForSyncGP(false, AdjustmentType.FIXED, AdjustmentType.PERCENTAGE);

        service.syncGPCeilingFloorWithPrimaryPricedProductSeason(pricingBaseAccomTypes);

        verifyCreateNewGPCeilingFloorRecordUsingPrimaryPricedProductCeilingFloor();
    }

    private void verifyCreateNewGPCeilingFloorRecordUsingPrimaryPricedProductCeilingFloor() {
        List<PricingBaseAccomType> groupPricing = tenantCrudServiceReal.findByNamedQuery(GroupPricingBaseAccomType.FIND_SEASONS_BY_PROPERTY_ID, getQueryParamMapForPropertyID());

        assertFalse(groupPricing.isEmpty(), "Group Pricing season should be available in the database");

        assertEquals(new BigDecimal(200.00), groupPricing.get(0).getSundayCeilingRateWithTax());
        assertEquals(new BigDecimal(90.00), groupPricing.get(0).getSundayFloorRateWithTax());

        assertEquals(new BigDecimal(250.00), groupPricing.get(0).getMondayCeilingRateWithTax());
        assertEquals(new BigDecimal(100.00), groupPricing.get(0).getMondayFloorRateWithTax());

        assertEquals(new BigDecimal(250.00), groupPricing.get(0).getTuesdayCeilingRateWithTax());
        assertEquals(new BigDecimal(100.00), groupPricing.get(0).getTuesdayFloorRateWithTax());

        assertEquals(new BigDecimal(250.00), groupPricing.get(0).getWednesdayCeilingRateWithTax());
        assertEquals(new BigDecimal(100.00), groupPricing.get(0).getWednesdayFloorRateWithTax());

        assertEquals(new BigDecimal(250.00), groupPricing.get(0).getThursdayCeilingRateWithTax());
        assertEquals(new BigDecimal(100.00), groupPricing.get(0).getThursdayFloorRateWithTax());

        assertEquals(new BigDecimal(225.00), groupPricing.get(0).getFridayCeilingRateWithTax());
        assertEquals(new BigDecimal(95.00), groupPricing.get(0).getFridayFloorRateWithTax());

        assertEquals(new BigDecimal(225.00), groupPricing.get(0).getSaturdayCeilingRateWithTax());
        assertEquals(new BigDecimal(95.00), groupPricing.get(0).getSaturdayFloorRateWithTax());
    }

    @Test
    public void shouldSyncPrimaryPricedProductByDeletingExistingMatchingGPSeason() {
        List<PricingBaseAccomType> pricingBaseAccomTypes = setUpCommonDataForSyncGP(false, AdjustmentType.FIXED, AdjustmentType.PERCENTAGE);
        tenantCrudServiceReal.save(pricingBaseAccomTypes);

        service.syncGPCeilingFloorWithPrimaryPricedProductSeason(pricingBaseAccomTypes);
        tenantCrudServiceReal.flushAndClear();

        List<PricingBaseAccomType> oldPricingAccomType = tenantCrudServiceReal.findByNamedQuery(TransientPricingBaseAccomType.FIND_SEASONS_BY_PROPERTY_ID, getQueryParamMapForPropertyID());
        PricingBaseAccomType transientPricingBaseAccomTypes = oldPricingAccomType.get(0).clone();
        PricingBaseAccomType updatedSeasonPricingAccomType = oldPricingAccomType.get(0);
        java.time.LocalDate startDate = JavaLocalDateUtils.toJavaLocalDate(updatedSeasonPricingAccomType.getStartDate());
        java.time.LocalDate endDate = JavaLocalDateUtils.toJavaLocalDate(updatedSeasonPricingAccomType.getEndDate());
        startDate.plusDays(-10);
        endDate.plusDays(10);
        updatedSeasonPricingAccomType.setPropertyId(PacmanWorkContextHelper.getPropertyId());
        updatedSeasonPricingAccomType.setSeasonName("Winter changed");
        updatedSeasonPricingAccomType.setStartDate(JavaLocalDateUtils.toJodaLocalDate(startDate));
        updatedSeasonPricingAccomType.setEndDate(JavaLocalDateUtils.toJodaLocalDate(endDate));
        service.syncGPCeilingFloorWithPrimaryPricedProductSeason(Arrays.asList(updatedSeasonPricingAccomType));

        verifySyncPrimaryPricedProductByDeletingExistingMatchingGPSeason();

    }

    @Test
    public void fetchCPDecisionBAROutputsTest() {
        Product product = new Product();
        product.setId(1);
        Set<Product> products = new HashSet<>();
        products.add(product);
        AccomType accomType = new AccomType();
        accomType.setId(1);
        Set<AccomType> accomTypes = new HashSet<>();
        accomTypes.add(accomType);
        CPDecisionBarOutputs cpDecisionBarOutputs = new CPDecisionBarOutputs();
        when(tenantCrudService.findByNativeQuery(anyString(), anyMap(), any(RowMapper.class))).thenReturn(Arrays.asList(cpDecisionBarOutputs));
        java.time.LocalDate date = java.time.LocalDate.now();
        List<CPDecisionBAROutput> cpDecisionBAROutputsResult = service.fetchCPDecisionBAROutputs(products, Set.of(JavaLocalDateUtils.toJodaLocalDate(date)), accomTypes);
        verify(tenantCrudService, times(1)).findByNativeQuery(anyString(), anyMap(), any(RowMapper.class));
        Assertions.assertEquals(cpDecisionBAROutputsResult.get(0), cpDecisionBarOutputs);
    }

    private void verifySyncPrimaryPricedProductByDeletingExistingMatchingGPSeason() {
        List<PricingBaseAccomType> groupPricing = tenantCrudServiceReal.findByNamedQuery(GroupPricingBaseAccomType.FIND_SEASONS_BY_PROPERTY_ID, getQueryParamMapForPropertyID());
        List<PricingBaseAccomType> afterUpdateExistingSeason = tenantCrudServiceReal.findByNamedQuery(GroupPricingBaseAccomType.FIND_SEASONS_BY_PROPERTY_ID, getQueryParamMapForPropertyID());

        assertEquals(groupPricing.size(), afterUpdateExistingSeason.size());
        assertEquals(afterUpdateExistingSeason.get(0).getSundayCeilingRateWithTax(), groupPricing.get(0).getSundayCeilingRateWithTax());
        assertEquals(afterUpdateExistingSeason.get(0).getSundayFloorRateWithTax(), groupPricing.get(0).getSundayFloorRateWithTax());

        assertEquals(afterUpdateExistingSeason.get(0).getMondayCeilingRateWithTax(), groupPricing.get(0).getMondayCeilingRateWithTax());
        assertEquals(afterUpdateExistingSeason.get(0).getMondayFloorRateWithTax(), new BigDecimal(100.00).setScale(2));

        assertEquals(afterUpdateExistingSeason.get(0).getTuesdayCeilingRateWithTax(), groupPricing.get(0).getTuesdayCeilingRateWithTax());
        assertEquals(afterUpdateExistingSeason.get(0).getTuesdayFloorRateWithTax(), groupPricing.get(0).getTuesdayFloorRateWithTax());

        assertEquals(afterUpdateExistingSeason.get(0).getWednesdayCeilingRateWithTax(), groupPricing.get(0).getWednesdayCeilingRateWithTax());
        assertEquals(afterUpdateExistingSeason.get(0).getWednesdayFloorRateWithTax(), groupPricing.get(0).getWednesdayFloorRateWithTax());

        assertEquals(afterUpdateExistingSeason.get(0).getThursdayCeilingRateWithTax(), groupPricing.get(0).getThursdayCeilingRateWithTax());
        assertEquals(afterUpdateExistingSeason.get(0).getThursdayFloorRateWithTax(), groupPricing.get(0).getThursdayFloorRateWithTax());

        assertEquals(afterUpdateExistingSeason.get(0).getFridayCeilingRateWithTax(), groupPricing.get(0).getFridayCeilingRateWithTax());
        assertEquals(afterUpdateExistingSeason.get(0).getFridayFloorRateWithTax(), groupPricing.get(0).getFridayFloorRateWithTax());

        assertEquals(afterUpdateExistingSeason.get(0).getSaturdayCeilingRateWithTax(), groupPricing.get(0).getSaturdayCeilingRateWithTax());
        assertEquals(afterUpdateExistingSeason.get(0).getSaturdayFloorRateWithTax(), groupPricing.get(0).getSaturdayFloorRateWithTax());
    }

    @Test
    public void deleteGPCeilingFloorSeasonForPrimaryPricedProductMatchingSeason() {
        List<PricingBaseAccomType> pricingBaseAccomTypes = setUpCommonDataForSyncGP(false, AdjustmentType.FIXED, AdjustmentType.PERCENTAGE);

        service.syncGPCeilingFloorWithPrimaryPricedProductSeason(pricingBaseAccomTypes);
        tenantCrudServiceReal.flushAndClear();

        service.deleteGPCeilingFloorForPrimaryPricedProduct(pricingBaseAccomTypes);

        List<PricingBaseAccomType> groupPricing = tenantCrudServiceReal.findByNamedQuery(GroupPricingBaseAccomType.FIND_SEASONS_BY_PROPERTY_ID, getQueryParamMapForPropertyID());
        assertTrue(groupPricing.isEmpty());
    }

    @Test
    public void shouldSyncGPCeilingFloorUsingPricingCeilingFloorWhenSyncGPCeilingFloorWithPrimaryPricedProductIsEnabled() {
        List<PricingBaseAccomType> pricingBaseAccomTypes = setDefaultGroupPricingAccomType(false, AdjustmentType.FIXED, AdjustmentType.PERCENTAGE);
        PricingBaseAccomType seasonPricingAccomType = PricingConfigurationObjectMother.buildPricingBaseAccomTypeSeasonTaxInclusive();
        seasonPricingAccomType.setPropertyId(PacmanWorkContextHelper.getPropertyId());
        seasonPricingAccomType.setAccomType(pricingBaseAccomTypes.get(0).getAccomType());
        tenantCrudServiceReal.save(pricingBaseAccomTypes);
        tenantCrudServiceReal.save(seasonPricingAccomType);
        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.SYNC_GP_CEILING_FLOOR_WITH_PRIMARY_PRICED_PRODUCT))
                .thenReturn(true);

        service.syncGPCeilingFloorWithPricingCeilingFloor();

        verifyCreateDefaultGPCeilingFloorConfigUsingPricingCeilingFloor();
        verifyCreateNewGPCeilingFloorRecordUsingPrimaryPricedProductCeilingFloor();
    }

    @Test
    public void shouldDeleteExistingGPConfigAndSyncGPCeilingFloorUsingPricingCeilingFloorWhenSyncGPCeilingFloorWithPrimaryPricedProductIsEnabled() {
        List<PricingBaseAccomType> pricingBaseAccomTypes = setDefaultGroupPricingAccomType(false, AdjustmentType.FIXED, AdjustmentType.PERCENTAGE);
        List<PricingBaseAccomType> seasonBaseAccomTypes = setUpCommonDataForSyncGP(false, AdjustmentType.FIXED, AdjustmentType.PERCENTAGE);
        service.syncDefaultGPCeilingFloorWithPrimaryPricedProduct(pricingBaseAccomTypes);
        service.syncGPCeilingFloorWithPrimaryPricedProductSeason(seasonBaseAccomTypes);
        tenantCrudServiceReal.save(pricingBaseAccomTypes);
        PricingBaseAccomType updatedSeasonPricingAccomType = seasonBaseAccomTypes.get(0);
        java.time.LocalDate startDate = JavaLocalDateUtils.toJavaLocalDate(updatedSeasonPricingAccomType.getStartDate());
        java.time.LocalDate endDate = JavaLocalDateUtils.toJavaLocalDate(updatedSeasonPricingAccomType.getEndDate());
        startDate.plusDays(-10);
        endDate.plusDays(10);
        updatedSeasonPricingAccomType.setSeasonName("Winter changed");
        updatedSeasonPricingAccomType.setStartDate(JavaLocalDateUtils.toJodaLocalDate(startDate));
        updatedSeasonPricingAccomType.setEndDate(JavaLocalDateUtils.toJodaLocalDate(endDate));
        tenantCrudServiceReal.save(updatedSeasonPricingAccomType);
        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.SYNC_GP_CEILING_FLOOR_WITH_PRIMARY_PRICED_PRODUCT))
                .thenReturn(true);

        service.syncGPCeilingFloorWithPricingCeilingFloor();

        verifyCreateDefaultGPCeilingFloorConfigUsingPricingCeilingFloor();
        verifyUpdatedSeasonAfterSyncGPCeilingFloorFromPricingCeilingFloor("Winter changed", startDate, endDate);
    }

    @Test
    public void shouldSyncGPCeilingFloorFromPricingCeilingFloorUploadJobWhenSyncGPCeilingFloorWithPrimaryPricedProductIsEnabled() {
        List<PricingBaseAccomType> pricingBaseAccomTypes = setDefaultGroupPricingAccomType(false, AdjustmentType.FIXED, AdjustmentType.PERCENTAGE);
        PricingBaseAccomType seasonPricingAccomType = PricingConfigurationObjectMother.buildPricingBaseAccomTypeSeasonTaxInclusive();
        seasonPricingAccomType.setPropertyId(PacmanWorkContextHelper.getPropertyId());
        seasonPricingAccomType.setAccomType(pricingBaseAccomTypes.get(0).getAccomType());
        tenantCrudServiceReal.save(pricingBaseAccomTypes);
        tenantCrudServiceReal.save(seasonPricingAccomType);
        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.SYNC_GP_CEILING_FLOOR_WITH_PRIMARY_PRICED_PRODUCT))
                .thenReturn(true);

        service.syncGPCeilingFloorFromPricingCeilingFloorUploadJob();

        verifyCreateDefaultGPCeilingFloorConfigUsingPricingCeilingFloor();
        verifyCreateNewGPCeilingFloorRecordUsingPrimaryPricedProductCeilingFloor();
    }


    @Test
    public void shouldDeleteExistingGPConfigAndSyncGPCeilingFloorFromPricingCeilingFloorUploadJobWhenSyncGPCeilingFloorWithPrimaryPricedProductIsEnabled() {
        List<PricingBaseAccomType> pricingBaseAccomTypes = setDefaultGroupPricingAccomType(false, AdjustmentType.FIXED, AdjustmentType.PERCENTAGE);
        List<PricingBaseAccomType> seasonBaseAccomTypes = setUpCommonDataForSyncGP(false, AdjustmentType.FIXED, AdjustmentType.PERCENTAGE);
        service.syncDefaultGPCeilingFloorWithPrimaryPricedProduct(pricingBaseAccomTypes);
        service.syncGPCeilingFloorWithPrimaryPricedProductSeason(seasonBaseAccomTypes);
        tenantCrudServiceReal.save(pricingBaseAccomTypes);
        PricingBaseAccomType updatedSeasonPricingAccomType = seasonBaseAccomTypes.get(0);
        java.time.LocalDate startDate = JavaLocalDateUtils.toJavaLocalDate(updatedSeasonPricingAccomType.getStartDate());
        java.time.LocalDate endDate = JavaLocalDateUtils.toJavaLocalDate(updatedSeasonPricingAccomType.getEndDate());
        startDate.plusDays(-10);
        endDate.plusDays(10);
        updatedSeasonPricingAccomType.setSeasonName("Winter changed");
        updatedSeasonPricingAccomType.setStartDate(JavaLocalDateUtils.toJodaLocalDate(startDate));
        updatedSeasonPricingAccomType.setEndDate(JavaLocalDateUtils.toJodaLocalDate(endDate));
        tenantCrudServiceReal.save(updatedSeasonPricingAccomType);
        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.SYNC_GP_CEILING_FLOOR_WITH_PRIMARY_PRICED_PRODUCT))
                .thenReturn(true);

        service.syncGPCeilingFloorWithPricingCeilingFloor();

        verifyCreateDefaultGPCeilingFloorConfigUsingPricingCeilingFloor();
        verifyUpdatedSeasonAfterSyncGPCeilingFloorFromPricingCeilingFloor("Winter changed", startDate, endDate);
    }

    private void verifyUpdatedSeasonAfterSyncGPCeilingFloorFromPricingCeilingFloor(String seasonName, java.time.LocalDate startDate, java.time.LocalDate endDate) {
        List<PricingBaseAccomType> groupPricing = tenantCrudServiceReal.findByNamedQuery(GroupPricingBaseAccomType.FIND_SEASONS_BY_PROPERTY_ID, getQueryParamMapForPropertyID());

        assertFalse(groupPricing.isEmpty(), "Group Pricing season should be available in the database");

        PricingBaseAccomType seasonPricingBaseAccomType = groupPricing.get(0);
        assertEquals(seasonName, seasonPricingBaseAccomType.getSeasonName());
        assertEquals(startDate, JavaLocalDateUtils.toJavaLocalDate(seasonPricingBaseAccomType.getStartDate()));
        assertEquals(endDate, JavaLocalDateUtils.toJavaLocalDate(seasonPricingBaseAccomType.getEndDate()));
    }


    private AccomClassMinPriceDiff mockAccomClassMinPriceDiff(int rank, BigDecimal diff, BigDecimal diffWithTax) {
        AccomClassMinPriceDiff accomClassMinPriceDiff = new AccomClassMinPriceDiff();
        accomClassMinPriceDiff.setAccomClassPriceRank(mockAccomClassPriceRank(rank));
        accomClassMinPriceDiff.setSundayDiff(diff);
        accomClassMinPriceDiff.setMondayDiff(diff);
        accomClassMinPriceDiff.setTuesdayDiff(diff);
        accomClassMinPriceDiff.setWednesdayDiff(diff);
        accomClassMinPriceDiff.setThursdayDiff(diff);
        accomClassMinPriceDiff.setFridayDiff(diff);
        accomClassMinPriceDiff.setSaturdayDiff(diff);
        accomClassMinPriceDiff.setSundayDiffWithTax(diffWithTax);
        accomClassMinPriceDiff.setMondayDiffWithTax(diffWithTax);
        accomClassMinPriceDiff.setTuesdayDiffWithTax(diffWithTax);
        accomClassMinPriceDiff.setWednesdayDiffWithTax(diffWithTax);
        accomClassMinPriceDiff.setThursdayDiffWithTax(diffWithTax);
        accomClassMinPriceDiff.setFridayDiffWithTax(diffWithTax);
        accomClassMinPriceDiff.setSaturdayDiffWithTax(diffWithTax);
        return accomClassMinPriceDiff;
    }

    private AccomClassPriceRank mockAccomClassPriceRank(int id) {
        AccomClassPriceRank accomClassPriceRank = new AccomClassPriceRank();
        accomClassPriceRank.setId(id);
        return accomClassPriceRank;
    }

    private Tax mockTax() {
        Tax tax = new Tax();
        tax.setRoomTaxRate(BigDecimal.TEN);
        return tax;
    }

    private void verifyCreateDefaultGPCeilingFloorConfigUsingPricingCeilingFloor() {
        List<PricingBaseAccomType> groupPricing = tenantCrudServiceReal.findByNamedQuery(GroupPricingBaseAccomType.FIND_BY_PROPERTY_ID, getQueryParamMapForPropertyID());

        assertFalse(groupPricing.isEmpty(), "Group Pricing season should be available in the database");
        assertEquals(groupPricing.get(0).getSundayCeilingRateWithTax(), new BigDecimal("320"));
        assertEquals(groupPricing.get(0).getSundayFloorRateWithTax(), new BigDecimal("200"));

        assertEquals(groupPricing.get(0).getMondayCeilingRateWithTax(), new BigDecimal("370"));
        assertEquals(groupPricing.get(0).getMondayFloorRateWithTax(), new BigDecimal("210"));

        assertEquals(groupPricing.get(0).getTuesdayCeilingRateWithTax(), new BigDecimal("370"));
        assertEquals(groupPricing.get(0).getTuesdayFloorRateWithTax(), new BigDecimal("220"));

        assertEquals(groupPricing.get(0).getWednesdayCeilingRateWithTax(), new BigDecimal("370"));
        assertEquals(groupPricing.get(0).getWednesdayFloorRateWithTax(), new BigDecimal("230"));

        assertEquals(groupPricing.get(0).getThursdayCeilingRateWithTax(), new BigDecimal("370"));
        assertEquals(groupPricing.get(0).getThursdayFloorRateWithTax(), new BigDecimal("240"));

        assertEquals(groupPricing.get(0).getFridayCeilingRateWithTax(), new BigDecimal("345"));
        assertEquals(groupPricing.get(0).getFridayFloorRateWithTax(), new BigDecimal("205"));

        assertEquals(groupPricing.get(0).getSaturdayCeilingRateWithTax(), new BigDecimal("345"));
        assertEquals(groupPricing.get(0).getSaturdayFloorRateWithTax(), new BigDecimal("215"));
    }

    private void verifyUpdateDefaultGPCeilingFloorConfigUsingPricingCeilingFloor() {
        List<PricingBaseAccomType> groupPricing = tenantCrudServiceReal.findByNamedQuery(GroupPricingBaseAccomType.FIND_BY_PROPERTY_ID, getQueryParamMapForPropertyID());

        assertFalse(groupPricing.isEmpty(), "Group Pricing season should be available in the database");

        assertEquals(groupPricing.get(0).getMondayCeilingRateWithTax(), new BigDecimal("300.00"));
        assertEquals(groupPricing.get(0).getTuesdayFloorRateWithTax(), new BigDecimal("225.55"));
        assertEquals(groupPricing.get(0).getWednesdayFloorRateWithTax(), new BigDecimal("245.40"));
        assertEquals(groupPricing.get(0).getFridayCeilingRateWithTax(), new BigDecimal("400.00"));
    }

    @Test
    public void shouldSyncPrimaryPricedProductGPCeilingFloorWithPricing() {
        List<PricingBaseAccomType> pricingBaseAccomTypes = setDefaultGroupPricingAccomType(false, AdjustmentType.FIXED, AdjustmentType.PERCENTAGE);
        List<PricingBaseAccomType> seasonPricingBaseAccomTypes = setUpCommonDataForSyncGP(false, AdjustmentType.FIXED, AdjustmentType.PERCENTAGE);

        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(java.time.LocalDate.parse("2020-06-01"));

        service.syncPrimaryPricedProductGPCeilingFloorWithPricing(Arrays.asList(pricingBaseAccomTypes.get(0), seasonPricingBaseAccomTypes.get(0)));

        verifyCreateDefaultGPCeilingFloorConfigUsingPricingCeilingFloor();
        verifyCreateNewGPCeilingFloorRecordUsingPrimaryPricedProductCeilingFloor();
    }

    @Test
    public void shouldSyncGPCeilingFloorWithPricingCeilingFloorWhenSyncGPCeilingFloorWithPrimaryPricedProductIsEnabled() {
        List<PricingBaseAccomType> pricingBaseAccomTypes = setDefaultGroupPricingAccomType(false, AdjustmentType.FIXED, AdjustmentType.PERCENTAGE);
        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.SYNC_GP_CEILING_FLOOR_WITH_PRIMARY_PRICED_PRODUCT))
                .thenReturn(true);
        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(java.time.LocalDate.parse("2020-06-01"));

        service.saveTransientPricingBaseAccomTypes(Arrays.asList(pricingBaseAccomTypes.get(0)));

        verifyCreateDefaultGPCeilingFloorConfigUsingPricingCeilingFloor();
    }

    @Test
    public void shouldCreateNewSeasonInGpCeilingFloorWhenSyncGPCeilingFloorWithPrimaryPricedProductIsEnabled() {
        setUpCPRelatedConfigParameters();
        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.SYNC_GP_CEILING_FLOOR_WITH_PRIMARY_PRICED_PRODUCT))
                .thenReturn(true);
        List<PricingBaseAccomType> seasonPricingBaseAccomTypes = setUpCommonDataForSyncGP(false, AdjustmentType.FIXED, AdjustmentType.PERCENTAGE);

        PricingBaseAccomType transientBaseAccomTypeSeason = seasonPricingBaseAccomTypes.get(0);
        transientBaseAccomTypeSeason.setPropertyId(PacmanWorkContextHelper.getPropertyId());

        transientBaseAccomTypeSeason.setEndDate(transientBaseAccomTypeSeason.getStartDate());
        Tax tax = createAndSetupTax();
        Map<LocalDate, Tax> taxesByDateRange = new HashMap<>();
        taxesByDateRange.put(transientBaseAccomTypeSeason.getStartDate(), tax);
        when(taxService.findTaxesForDateRange(transientBaseAccomTypeSeason.getStartDate(), transientBaseAccomTypeSeason.getEndDate())).thenReturn(taxesByDateRange);
        Map<AccomTypeSupplementValuePK, AccomTypeSupplementValue> map = buildSupplementMap(transientBaseAccomTypeSeason.getStartDate(), 1, OccupancyType.DOUBLE);
        when(accomTypeSupplementService.getSupplementValueMap(transientBaseAccomTypeSeason.getStartDate(), transientBaseAccomTypeSeason.getEndDate())).thenReturn(map);
        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(java.time.LocalDate.parse("2020-06-01"));

        service.saveImpactedTransientPricingBaseAccomTypes(Arrays.asList(transientBaseAccomTypeSeason));

        verifyCreateNewSeasonInGpCeilingFloor();
    }

    @Test
    public void shouldDeleteExistingSeasonAndCreateNewSeasonInGpCeilingFloorWhenSyncGPCeilingFloorWithPrimaryPricedProductIsEnabled() {
        setUpCPRelatedConfigParameters();
        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.SYNC_GP_CEILING_FLOOR_WITH_PRIMARY_PRICED_PRODUCT))
                .thenReturn(true);
        List<PricingBaseAccomType> seasonPricingBaseAccomTypes = setUpCommonDataForSyncGP(false, AdjustmentType.FIXED, AdjustmentType.PERCENTAGE);

        PricingBaseAccomType transientBaseAccomTypeSeason = seasonPricingBaseAccomTypes.get(0);
        transientBaseAccomTypeSeason.setPropertyId(PacmanWorkContextHelper.getPropertyId());
        transientBaseAccomTypeSeason.setEndDate(transientBaseAccomTypeSeason.getStartDate());
        Tax tax = createAndSetupTax();
        tax.setStartDate(transientBaseAccomTypeSeason.getStartDate());
        tax.setEndDate(transientBaseAccomTypeSeason.getEndDate());

        Map<LocalDate, Tax> taxesByDateRange = new HashMap<>();
        taxesByDateRange.put(transientBaseAccomTypeSeason.getStartDate(), tax);
        when(taxService.findTaxesForDateRange(transientBaseAccomTypeSeason.getStartDate(), transientBaseAccomTypeSeason.getEndDate())).thenReturn(taxesByDateRange);
        Map<AccomTypeSupplementValuePK, AccomTypeSupplementValue> map = buildSupplementMap(transientBaseAccomTypeSeason.getStartDate(), 1, OccupancyType.DOUBLE);
        when(accomTypeSupplementService.getSupplementValueMap(transientBaseAccomTypeSeason.getStartDate(), transientBaseAccomTypeSeason.getEndDate())).thenReturn(map);
        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(java.time.LocalDate.parse("2020-06-01"));

        service.saveImpactedTransientPricingBaseAccomTypes(Arrays.asList(transientBaseAccomTypeSeason));
        List<PricingBaseAccomType> oldPricingAccomType = tenantCrudServiceReal.findByNamedQuery(TransientPricingBaseAccomType.FIND_SEASONS_BY_PROPERTY_ID, getQueryParamMapForPropertyID());

        service.saveImpactedTransientPricingBaseAccomTypes(oldPricingAccomType);

        verifyCreateNewSeasonInGpCeilingFloor();
    }

    private void verifyCreateNewSeasonInGpCeilingFloor() {
        List<PricingBaseAccomType> groupPricing = tenantCrudServiceReal.findByNamedQuery(GroupPricingBaseAccomType.FIND_SEASONS_BY_PROPERTY_ID, getQueryParamMapForPropertyID());

        assertFalse(groupPricing.isEmpty(), "Group Pricing season should be available in the database");
        assertEquals(new BigDecimal(250.00), groupPricing.get(0).getWednesdayCeilingRateWithTax());
        assertEquals(new BigDecimal(100.00), groupPricing.get(0).getWednesdayFloorRateWithTax());

    }

    private List<PricingBaseAccomType> setDefaultGroupPricingAccomType(Boolean adjustCeilingFloorValue, AdjustmentType floor, AdjustmentType ceil) {
        setUpGroupPricingSyncBARCeilingFloorDetails(adjustCeilingFloorValue, floor, ceil);

        tenantCrudServiceReal.deleteAll(GroupPricingBaseAccomType.class);
        tenantCrudServiceReal.deleteAll(TransientPricingBaseAccomType.class);

        PricingBaseAccomType basePricingAccomType = PricingConfigurationObjectMother.createPricingBaseAccomTypeWithTax();
        basePricingAccomType.setPropertyId(PacmanWorkContextHelper.getPropertyId());

        AccomType accomType = tenantCrudServiceReal.findByNamedQuerySingleResult(AccomType.BY_CODE, QueryParameter.with("code", "DLX").parameters());
        basePricingAccomType.setAccomType(accomType);
        tenantCrudServiceReal.flushAndClear();
        return Arrays.asList(basePricingAccomType);
    }

    private void setUpGroupPricingSyncBARCeilingFloorDetails(Boolean adjustCeilingFloorValue, AdjustmentType floor, AdjustmentType ceil) {
        inject(service, "tenantCrudService", tenantCrudService());
        inject(service, "multiPropertyCrudService", multiPropertyCrudService());
        createAndSetupTax();
        GroupPricingSyncBARCeilingFloorDetails groupPricingSyncBARCeilingFloorDetails =
                tenantCrudServiceReal.findByNamedQuerySingleResult(GroupPricingSyncBARCeilingFloorDetails.FIND_DEFAULT_BAR_CEILING_FLOOR);
        groupPricingSyncBARCeilingFloorDetails.setUseBARCeilingAndFloor(true);
        groupPricingSyncBARCeilingFloorDetails.setAdjustCeilingFloorValues(adjustCeilingFloorValue);
        groupPricingSyncBARCeilingFloorDetails.setFloorAdjustmentType(floor);
        groupPricingSyncBARCeilingFloorDetails.setCeilingAdjustmentType(ceil);
        groupPricingSyncBARCeilingFloorDetails.setFloorAdjustmentValue(new BigDecimal(10.00));
        groupPricingSyncBARCeilingFloorDetails.setCeilingAdjustmentValue(new BigDecimal(20.00));
        tenantCrudServiceReal.save(groupPricingSyncBARCeilingFloorDetails);
    }

    public List<PricingBaseAccomType> setUpCommonDataForSyncGP(Boolean adjustCeilingFloorValue, AdjustmentType floor, AdjustmentType ceil) {
        setUpGroupPricingSyncBARCeilingFloorDetails(adjustCeilingFloorValue, floor, ceil);

        Map<String, Object> queryParams = getQueryParamMapForPropertyID();
        tenantCrudServiceReal.executeUpdateByNamedQuery(GroupPricingBaseAccomType.DELETE_SEASONS_BY_PROPERTY_ID, queryParams);

        PricingBaseAccomType seasonPricingAccomType = PricingConfigurationObjectMother.buildPricingBaseAccomTypeSeasonTaxInclusive();
        seasonPricingAccomType.setPropertyId(PacmanWorkContextHelper.getPropertyId());

        AccomType accomType = tenantCrudServiceReal.findByNamedQuerySingleResult(AccomType.BY_CODE, QueryParameter.with("code", "DLX").parameters());
        seasonPricingAccomType.setAccomType(accomType);
        tenantCrudServiceReal.flushAndClear();
        return Arrays.asList(seasonPricingAccomType);
    }

    @Test
    public void shouldDeleteExistingSeasonsAndCreateNewSeasonsInGpCeilingFloorWhenSyncGPCeilingFloorWithPrimaryPricedProductIsEnabled() {
        setUpCPRelatedConfigParameters();
        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.SYNC_GP_CEILING_FLOOR_WITH_PRIMARY_PRICED_PRODUCT))
                .thenReturn(true);
        List<PricingBaseAccomType> seasonPricingBaseAccomTypes = setUpCommonDataForSyncGP(false, AdjustmentType.FIXED, AdjustmentType.PERCENTAGE);
        AccomType deluxeAccomType = tenantCrudServiceReal.findByNamedQuerySingleResult(AccomType.BY_CODE, QueryParameter.with("code", "DLX").parameters());
        AccomType suiteAccomType = tenantCrudServiceReal.findByNamedQuerySingleResult(AccomType.BY_CODE, QueryParameter.with("code", "STE").parameters());

        Integer propertyId = PacmanWorkContextHelper.getPropertyId();
        PricingBaseAccomType transientBaseAccomTypeSeason = seasonPricingBaseAccomTypes.get(0);
        transientBaseAccomTypeSeason.setEndDate(transientBaseAccomTypeSeason.getStartDate());
        transientBaseAccomTypeSeason.setPropertyId(propertyId);
        PricingBaseAccomType suiteAccomTypePricingWinterSeason = PricingConfigurationObjectMother.buildPricingBaseAccomTypeSeason();
        suiteAccomTypePricingWinterSeason.setEndDate(transientBaseAccomTypeSeason.getStartDate());
        suiteAccomTypePricingWinterSeason.setAccomType(suiteAccomType);
        suiteAccomTypePricingWinterSeason.setPropertyId(propertyId);

        PricingBaseAccomType deluxeAccomTypePricingSeason = PricingConfigurationObjectMother.buildPricingBaseAccomTypeSeason();
        java.time.LocalDate startDate = java.time.LocalDate.of(2024, 03, 07);
        java.time.LocalDate endDate = java.time.LocalDate.of(2024, 03, 12);

        deluxeAccomTypePricingSeason.setSeasonName("March");
        deluxeAccomTypePricingSeason.setAccomType(deluxeAccomType);
        deluxeAccomTypePricingSeason.setStartDate(JavaLocalDateUtils.toJodaLocalDate(startDate));
        deluxeAccomTypePricingSeason.setEndDate(JavaLocalDateUtils.toJodaLocalDate(endDate));
        deluxeAccomTypePricingSeason.setPropertyId(propertyId);

        PricingBaseAccomType suiteAccomTypePricingSeason = PricingConfigurationObjectMother.buildPricingBaseAccomTypeSeason();
        suiteAccomTypePricingSeason.setSeasonName("March");
        suiteAccomTypePricingSeason.setAccomType(suiteAccomType);
        suiteAccomTypePricingSeason.setStartDate(JavaLocalDateUtils.toJodaLocalDate(startDate));
        suiteAccomTypePricingSeason.setEndDate(JavaLocalDateUtils.toJodaLocalDate(endDate));
        suiteAccomTypePricingSeason.setPropertyId(propertyId);

        Tax tax = createAndSetupTax();
        tax.setStartDate(transientBaseAccomTypeSeason.getStartDate());
        tax.setEndDate(transientBaseAccomTypeSeason.getEndDate());

        Map<LocalDate, Tax> taxesByDateRange = new HashMap<>();
        taxesByDateRange.put(transientBaseAccomTypeSeason.getStartDate(), tax);
        taxesByDateRange.put(JavaLocalDateUtils.toJodaLocalDate(startDate), tax);
        taxesByDateRange.put(JavaLocalDateUtils.toJodaLocalDate(startDate.plusDays(1)), tax);
        taxesByDateRange.put(JavaLocalDateUtils.toJodaLocalDate(startDate.plusDays(2)), tax);
        taxesByDateRange.put(JavaLocalDateUtils.toJodaLocalDate(startDate.plusDays(3)), tax);
        taxesByDateRange.put(JavaLocalDateUtils.toJodaLocalDate(startDate.plusDays(4)), tax);
        taxesByDateRange.put(JavaLocalDateUtils.toJodaLocalDate(endDate), tax);
        when(taxService.findTaxesForDateRange(transientBaseAccomTypeSeason.getStartDate(), deluxeAccomTypePricingSeason.getEndDate())).thenReturn(taxesByDateRange);

        Map<AccomTypeSupplementValuePK, AccomTypeSupplementValue> map = buildSupplementMap(transientBaseAccomTypeSeason.getStartDate(), 1, OccupancyType.DOUBLE);
        when(accomTypeSupplementService.getSupplementValueMap(transientBaseAccomTypeSeason.getStartDate(), transientBaseAccomTypeSeason.getEndDate())).thenReturn(map);
        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(java.time.LocalDate.parse("2020-06-01"));

        service.saveImpactedTransientPricingBaseAccomTypes(Arrays.asList(transientBaseAccomTypeSeason, suiteAccomTypePricingWinterSeason, deluxeAccomTypePricingSeason, suiteAccomTypePricingSeason));
        List<PricingBaseAccomType> oldPricingAccomType = tenantCrudServiceReal.findByNamedQuery(TransientPricingBaseAccomType.FIND_SEASONS_BY_PROPERTY_ID, getQueryParamMapForPropertyID());

        service.saveImpactedTransientPricingBaseAccomTypes(oldPricingAccomType);

        verifyCreateNewSeasonSInGpCeilingFloor();
    }

    private void verifyCreateNewSeasonSInGpCeilingFloor() {
        List<PricingBaseAccomType> groupPricing = tenantCrudServiceReal.findByNamedQuery(GroupPricingBaseAccomType.FIND_SEASONS_BY_PROPERTY_ID, getQueryParamMapForPropertyID());

        assertFalse(groupPricing.isEmpty(), "Group Pricing season should be available in the database");
        assertEquals(4, groupPricing.size());

        PricingBaseAccomType winterSeason = groupPricing.get(0);
        assertEquals("Winter", winterSeason.getSeasonName());
        assertEquals(JavaLocalDateUtils.toJodaLocalDate(java.time.LocalDate.of(2015, 7, 1)), winterSeason.getStartDate());
        assertEquals(JavaLocalDateUtils.toJodaLocalDate(java.time.LocalDate.of(2015, 7, 1)), winterSeason.getEndDate());

        PricingBaseAccomType marchSeason = groupPricing.get(3);
        assertEquals("March", marchSeason.getSeasonName());
        assertEquals(JavaLocalDateUtils.toJodaLocalDate(java.time.LocalDate.of(2024, 03, 07)), marchSeason.getStartDate());
        assertEquals(JavaLocalDateUtils.toJodaLocalDate(java.time.LocalDate.of(2024, 03, 12)), marchSeason.getEndDate());

        assertEquals(new BigDecimal(250.00), groupPricing.get(0).getWednesdayCeilingRateWithTax());
        assertEquals(new BigDecimal(100.00), groupPricing.get(0).getWednesdayFloorRateWithTax());

    }

    @Test
    public void shouldSyncGPCeilingFloorWithPricingCeilingFloorWhenSyncGPCeilingFloorWithPrimaryPricedProductIsEnabledSeason() {
        List<PricingBaseAccomType> pricingBaseAccomTypes = setDefaultGroupPricingAccomType(false, AdjustmentType.FIXED, AdjustmentType.PERCENTAGE);
        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.SYNC_GP_CEILING_FLOOR_WITH_PRIMARY_PRICED_PRODUCT))
                .thenReturn(true);
        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(java.time.LocalDate.parse("2020-06-01"));

        service.saveTransientPricingBaseAccomTypes(Arrays.asList(pricingBaseAccomTypes.get(0)));

        List<PricingBaseAccomType> pricingBaseAccomType = tenantCrudServiceReal.findByNamedQuery(TransientPricingBaseAccomType.FIND_DEFAULT_BY_PRODUCT_ID, QueryParameter.with("productId", 1).and("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters());

        PricingBaseAccomType oldPricingAccomType = pricingBaseAccomType.get(0);
        oldPricingAccomType.setMondayCeilingRateWithTax(new BigDecimal(200.00));
        service.saveImpactedTransientPricingBaseAccomTypes(Arrays.asList(oldPricingAccomType));


        List<PricingBaseAccomType> groupPricing = tenantCrudServiceReal.findByNamedQuery(GroupPricingBaseAccomType.FIND_BY_PROPERTY_ID, getQueryParamMapForPropertyID());

        assertEquals(new BigDecimal(200.00), groupPricing.get(0).getMondayCeilingRateWithTax());
    }

    @Test
    void shouldDeletePricingAccomClassWhenPresentForRoomClass() {

        AccomClass standard = new AccomClass();
        standard.setId(101);

        PricingAccomClass pricingAccomClass = new PricingAccomClass();
        pricingAccomClass.setAccomClass(standard);
        pricingAccomClass.setId(10);

        when(tenantCrudService.findByNamedQuerySingleResult(
                PricingAccomClass.FIND_BY_PROPERTY_AND_ACCOM_CLASS,
                QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId())
                        .and("accomClassId", standard.getId()).parameters())).thenReturn(pricingAccomClass);

        service.deletePricingAccomClass(standard);

        verify(tenantCrudService).delete(PricingAccomClass.class, pricingAccomClass.getId());
    }

    @Test
    void shouldDeleteTransientPricingConfigurationForAccomTypes() {
        AccomType accomType = new AccomType();
        accomType.setId(1);
        service.deleteTransientPricingConfigurationsByAccomTypes(Set.of(accomType));
        tenantCrudService.executeUpdateByNamedQuery(TransientPricingBaseAccomType.DELETE_BY_ACCOM_TYPES,
                QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).and("accomTypes", Set.of(accomType)).parameters());

    }

    @Test
    void shouldDeleteGroupPricingConfigurationsForAccomTypes() {
        AccomType accomType = new AccomType();
        accomType.setId(1);
        service.deleteGroupPricingConfigurationsByAccomTypes(Set.of(accomType));
        tenantCrudService.executeUpdateByNamedQuery(GroupPricingBaseAccomType.DELETE_BY_ACCOM_TYPES,
                QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).and("accomTypes", Set.of(accomType)).parameters());
    }

    @Test
    void shouldDeletePricingOffsetForAccomTypes() {
        AccomType accomType = new AccomType();
        accomType.setId(1);
        service.deletePricingOffsetAccomTypesByAccomTypes(Set.of(accomType));
        tenantCrudService.executeUpdateByNamedQuery(CPConfigOffsetAccomType.DELETE_BY_ACCOM_TYPES,
                QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).and(ACCOM_TYPES, Set.of(accomType)).parameters());
    }

    @Test
    void shouldDeletePricingConfigurationSupplementsForAccomTypes() {
        AccomType accomType = new AccomType();
        accomType.setId(1);
        service.deletePricingConfigurationSupplementsForAccomTypes(Set.of(accomType));
        tenantCrudService.executeUpdateByNamedQuery(AccomTypeSupplement.DELETE_SUPPLEMENT_BY_ACCOM_TYPE,
                QueryParameter.with(ACCOM_TYPES, Set.of(accomType)).parameters());

    }

    @Test
    public void shouldSyncGroupPricingCeilingFloorWithPricingCeilingFloor() {
        setDefaultGroupPricingAccomType(Boolean.FALSE, AdjustmentType.FIXED, AdjustmentType.FIXED);
        assertTrue(service.syncGroupPricingCeilingFloorWithPricingCeilingFloor(getGPSyncBARCeilingFloorDTO()));
    }

    @Test
    public void shouldNotSyncGroupPricingCeilingFloorWithPricingCeilingFloorWhenDefaultConfigurationAlreadyPresent() {
        setDefaultGroupPricingAccomType(Boolean.FALSE, AdjustmentType.FIXED, AdjustmentType.FIXED);
        List<PricingBaseAccomType> pricingBaseAccomTypes = setDefaultGroupPricingAccomType(false, AdjustmentType.FIXED, AdjustmentType.FIXED);
        tenantCrudServiceReal.save(pricingBaseAccomTypes);

        service.syncDefaultGPCeilingFloorWithPrimaryPricedProduct(pricingBaseAccomTypes);
        tenantCrudServiceReal.flushAndClear();
        assertFalse(service.syncGroupPricingCeilingFloorWithPricingCeilingFloor(getGPSyncBARCeilingFloorDTO()));

    }

    @Test
    public void shouldNotSyncGroupPricingCeilingFloorWithPricingCeilingFloorWhenDefaultAndSeasonConfigurationAlreadyPresent() {
        //Default configuration
        List<PricingBaseAccomType> pricingBaseAccomTypes = setDefaultGroupPricingAccomType(false, AdjustmentType.FIXED, AdjustmentType.FIXED);
        tenantCrudServiceReal.save(pricingBaseAccomTypes);
        service.syncDefaultGPCeilingFloorWithPrimaryPricedProduct(pricingBaseAccomTypes);
        tenantCrudServiceReal.flushAndClear();

        //Season configuration
        List<PricingBaseAccomType> seasonPricingBaseAccomTypes = setUpCommonDataForSyncGP(true, AdjustmentType.FIXED, AdjustmentType.FIXED);
        service.syncGPCeilingFloorWithPrimaryPricedProductSeason(seasonPricingBaseAccomTypes);

        assertFalse(service.syncGroupPricingCeilingFloorWithPricingCeilingFloor(getGPSyncBARCeilingFloorDTO()));
    }

    @Test
    public void shouldSyncGroupPricingCeilingFloorWithPricingCeilingFloorAfterDeletingExistingConfiguration() {
        //Default configuration
        List<PricingBaseAccomType> pricingBaseAccomTypes = setDefaultGroupPricingAccomType(false, AdjustmentType.FIXED, AdjustmentType.FIXED);
        tenantCrudServiceReal.save(pricingBaseAccomTypes);
        service.syncDefaultGPCeilingFloorWithPrimaryPricedProduct(pricingBaseAccomTypes);
        tenantCrudServiceReal.flushAndClear();

        //Season configuration
        List<PricingBaseAccomType> seasonPricingBaseAccomTypes = setUpCommonDataForSyncGP(true, AdjustmentType.FIXED, AdjustmentType.FIXED);
        service.syncGPCeilingFloorWithPrimaryPricedProductSeason(seasonPricingBaseAccomTypes);
        GPSyncBARCeilingFloorDTO dto = getGPSyncBARCeilingFloorDTO();
        assertFalse(service.syncGroupPricingCeilingFloorWithPricingCeilingFloor(dto));
        service.deleteGroupPricingConfigurationByProperty();
        assertTrue(service.syncGroupPricingCeilingFloorWithPricingCeilingFloor(dto));
    }


    @Test
    void shouldNotSaveDataWhenGPSyncBARCeilingFloorDTOIsEmpty(){
        GroupPricingSyncBARCeilingFloorDetails groupPricingSyncBARCeilingFloorDetails = new GroupPricingSyncBARCeilingFloorDetails();
        groupPricingSyncBARCeilingFloorDetails.setUseBARCeilingAndFloor(true);
        groupPricingSyncBARCeilingFloorDetails.setAdjustCeilingFloorValues(true);
        groupPricingSyncBARCeilingFloorDetails.setFloorAdjustmentType(AdjustmentType.FIXED);
        groupPricingSyncBARCeilingFloorDetails.setCeilingAdjustmentType(AdjustmentType.FIXED);
        groupPricingSyncBARCeilingFloorDetails.setFloorAdjustmentValue(new BigDecimal(10.00));
        groupPricingSyncBARCeilingFloorDetails.setCeilingAdjustmentValue(new BigDecimal(20.00));

        when(tenantCrudService.findByNamedQuerySingleResult(GroupPricingSyncBARCeilingFloorDetails.FIND_DEFAULT_BAR_CEILING_FLOOR)).thenReturn(groupPricingSyncBARCeilingFloorDetails);
        service.updateUseBarCeilingFloorValue(new GPSyncBARCeilingFloorDTO());
        assertEquals(AdjustmentType.FIXED,groupPricingSyncBARCeilingFloorDetails.getCeilingAdjustmentType());
    }

    @Test
    void shouldNotSaveDataWhenGPSyncBARCeilingFloorDTOIsNotEmpty(){
        GroupPricingSyncBARCeilingFloorDetails groupPricingSyncBARCeilingFloorDetails = new GroupPricingSyncBARCeilingFloorDetails();
        groupPricingSyncBARCeilingFloorDetails.setUseBARCeilingAndFloor(true);
        groupPricingSyncBARCeilingFloorDetails.setAdjustCeilingFloorValues(true);
        groupPricingSyncBARCeilingFloorDetails.setFloorAdjustmentType(AdjustmentType.FIXED);
        groupPricingSyncBARCeilingFloorDetails.setCeilingAdjustmentType(AdjustmentType.FIXED);
        groupPricingSyncBARCeilingFloorDetails.setFloorAdjustmentValue(new BigDecimal(10.00));
        groupPricingSyncBARCeilingFloorDetails.setCeilingAdjustmentValue(new BigDecimal(20.00));

        when(tenantCrudService.findByNamedQuerySingleResult(GroupPricingSyncBARCeilingFloorDetails.FIND_DEFAULT_BAR_CEILING_FLOOR)).thenReturn(groupPricingSyncBARCeilingFloorDetails);
        service.updateUseBarCeilingFloorValue(getGPSyncBARCeilingFloorDTO());
        assertEquals(AdjustmentType.PERCENTAGE,groupPricingSyncBARCeilingFloorDetails.getCeilingAdjustmentType());
    }
    @Test
    public void shouldDeletingExistingGPConfiguration() {
        inject(service,"tenantCrudService",tenantCrudServiceReal);
        List<PricingBaseAccomType> groupPricing = tenantCrudServiceReal.findByNamedQuery(GroupPricingBaseAccomType.FIND_BY_PROPERTY_ID, getQueryParamMapForPropertyID());
        assertFalse(groupPricing.isEmpty());
        assertEquals(groupPricing.size(),service.deleteGroupPricingConfigurationByProperty());
        List<PricingBaseAccomType> groupPricingAfterDelete = tenantCrudServiceReal.findByNamedQuery(GroupPricingBaseAccomType.FIND_BY_PROPERTY_ID, getQueryParamMapForPropertyID());
        assertTrue(groupPricingAfterDelete.isEmpty());
    }

    @Test
    void deletePricingConfigurationsForBaseRoomType_CentralRMS_Sync_Enabled() {
        int accomTypeId = 1;
        AccomType accomTypeRemapped = new AccomType();
        accomTypeRemapped.setId(accomTypeId);
        AccomClass accomClass = new AccomClass();
        accomClass.setStatusId(Constants.INACTIVE_STATUS_ID - 1);
        accomTypeRemapped.setAccomClass(accomClass);

        AccomType existingAccomType = new AccomType();
        existingAccomType.setId(accomTypeId);
        AccomClass accomClassExisting = new AccomClass();
        int accomClassId = 2;
        accomClassExisting.setId(accomClassId);
        existingAccomType.setAccomClass(accomClassExisting);

        PricingAccomClass pricingAccomClass = new PricingAccomClass();
        pricingAccomClass.setAccomType(existingAccomType);

        when(tenantCrudService.find(AccomType.class, accomTypeId)).thenReturn(existingAccomType);
        when(tenantCrudService.findByNamedQuerySingleResult(
                PricingAccomClass.FIND_BY_PROPERTY_AND_ACCOM_CLASS,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId())
                        .and("accomClassId", existingAccomType.getAccomClass().getId()).parameters())).thenReturn(pricingAccomClass);
        when(configParamsService.getBooleanParameterValue(CENTRAL_RMS_AVAILABLE)).thenReturn(true);
        doNothing().when(centralRmsService).syncPricingDataOnRoomClassChange();

        Set<AccomType> remappedAccomTypes = new HashSet<>();
        remappedAccomTypes.add(accomTypeRemapped);

        PricingConfigurationService spyService = spy(service);
        when(spyService.isCentralRmsSyncAvailable()).thenReturn(true);

        spyService.deletePricingConfigurationsForRoomType(remappedAccomTypes);

        verify(tenantCrudService, times(1)).delete(PricingAccomClass.class, pricingAccomClass.getId());
        verify(tenantCrudService, times(1)).executeUpdateByNamedQuery(TransientPricingBaseAccomType.DELETE_BY_ACCOM_TYPE,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).and("accomType", existingAccomType).parameters());
        verify(tenantCrudService, times(1)).executeUpdateByNamedQuery(GroupPricingBaseAccomType.DELETE_BY_ACCOM_TYPE,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).and("accomType", existingAccomType).parameters());
        verify(tenantCrudService, times(1)).executeUpdateByNamedQuery(CPConfigOffsetAccomType.DELETE_BY_ACCOM_TYPES,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).and("accomTypes", remappedAccomTypes).parameters());
        verify(tenantCrudService, times(1)).executeUpdateByNamedQuery(CPConfigOffsetAccomType.DELETE_SEASONS_DATE_FOR_ACCOM_TYPE,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).and("accomTypes", remappedAccomTypes).parameters());
        verify(tenantCrudService, times(1)).executeUpdateByNamedQuery(AccomTypeSupplement.DELETE_SUPPLEMENT_BY_ACCOM_TYPE,
                QueryParameter.with("accomTypes", remappedAccomTypes).parameters());
        verify(centralRmsService).syncPricingDataOnRoomClassChange();
    }
    @Test
    public void testHasOneYearHistoricalData_NoFileMetadataButHasAccomActivity() {
        java.time.LocalDate caughtUpDate = java.time.LocalDate.of(2023, 1, 1);
        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(caughtUpDate);
        Date oneYearAgo = Date.from(caughtUpDate.minusYears(1).atStartOfDay(ZoneId.systemDefault()).toInstant());
        Date caughtUpAsDate = Date.from(caughtUpDate.atStartOfDay(ZoneId.systemDefault()).toInstant());

        when(tenantCrudService.<Integer>findByNamedQuery(
                eq(FileMetadata.HAS_ONE_YEAR_HISTORY),
                eq(QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId())
                        .and("fromDate", oneYearAgo)
                        .and("caughtUpDate", caughtUpAsDate)
                        .parameters())))
                .thenReturn(Arrays.asList(0));

        when(tenantCrudService.<Integer>findByNamedQuery(
                eq(AccomActivity.HAS_ONE_YEAR_HISTORY),
                eq(QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId())
                        .and("fromDate", oneYearAgo)
                        .and("caughtUpDate", caughtUpAsDate)
                        .parameters())))
                .thenReturn(Arrays.asList(1));

        assertTrue(service.hasOneYearHistoricalData());
        verify(tenantCrudService).findByNamedQuery(
                eq(FileMetadata.HAS_ONE_YEAR_HISTORY),
                any(Map.class));
        verify(tenantCrudService).findByNamedQuery(
                eq(AccomActivity.HAS_ONE_YEAR_HISTORY),
                any(Map.class));
    }

    @Test
    public void testHasOneYearHistoricalData_NoHistory() {
        java.time.LocalDate caughtUpDate = java.time.LocalDate.of(2023, 1, 1);
        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(caughtUpDate);
        Date oneYearAgo = Date.from(caughtUpDate.minusYears(1).atStartOfDay(ZoneId.systemDefault()).toInstant());
        Date caughtUpAsDate = Date.from(caughtUpDate.atStartOfDay(ZoneId.systemDefault()).toInstant());

        when(tenantCrudService.<Integer>findByNamedQuery(
                eq(FileMetadata.HAS_ONE_YEAR_HISTORY),
                eq(QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId())
                        .and("fromDate", oneYearAgo)
                        .and("caughtUpDate", caughtUpAsDate)
                        .parameters())))
                .thenReturn(Arrays.asList(0));

        when(tenantCrudService.<Integer>findByNamedQuery(
                eq(AccomActivity.HAS_ONE_YEAR_HISTORY),
                eq(QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId())
                        .and("fromDate", oneYearAgo)
                        .and("caughtUpDate", caughtUpAsDate)
                        .parameters())))
                .thenReturn(Arrays.asList(0));

        assertFalse(service.hasOneYearHistoricalData());
    }

    @Test
    public void testHasOneYearHistoricalData_EmptyResults() {
        java.time.LocalDate caughtUpDate = java.time.LocalDate.of(2023, 1, 1);
        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(caughtUpDate);
        Date oneYearAgo = Date.from(caughtUpDate.minusYears(1).atStartOfDay(ZoneId.systemDefault()).toInstant());
        Date caughtUpAsDate = Date.from(caughtUpDate.atStartOfDay(ZoneId.systemDefault()).toInstant());

        when(tenantCrudService.<Integer>findByNamedQuery(
                eq(FileMetadata.HAS_ONE_YEAR_HISTORY),
                eq(QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId())
                        .and("fromDate", oneYearAgo)
                        .and("caughtUpDate", caughtUpAsDate)
                        .parameters())))
                .thenReturn(Collections.emptyList());

        when(tenantCrudService.<Integer>findByNamedQuery(
                eq(AccomActivity.HAS_ONE_YEAR_HISTORY),
                eq(QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId())
                        .and("fromDate", oneYearAgo)
                        .and("caughtUpDate", caughtUpAsDate)
                        .parameters())))
                .thenReturn(Collections.emptyList());

        assertFalse(service.hasOneYearHistoricalData());
    }

    private GPSyncBARCeilingFloorDTO getGPSyncBARCeilingFloorDTO(){
        GPSyncBARCeilingFloorDTO dto = new GPSyncBARCeilingFloorDTO();
        dto.setPreserveConfigurations(true);
        dto.setCeilingAdjustmentType(AdjustmentType.PERCENTAGE);
        dto.setFloorAdjustmentType(AdjustmentType.PERCENTAGE);
        dto.setCeilingAdjustmentValue(BigDecimal.valueOf(200.0));
        dto.setFloorAdjustmentValue(BigDecimal.valueOf(100.0));
        return dto;
    }
}