package com.ideas.tetris.pacman.services.job;

import com.ideas.g3.test.AbstractG3JupiterTest;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import org.joda.time.LocalDateTime;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.util.Date;

import static org.junit.jupiter.api.Assertions.assertTrue;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class RecentJobViewCriteriaTest extends AbstractG3JupiterTest {

    @Test
    public void testInvalidServerTimeInCriteria() {
        Date date = DateUtil.getDate(8, 2, 2020, 1, 30, 0);
        LocalDateTime invalidServerTime = LocalDateTime.fromDateFields(date).plusHours(1);
        JobViewCriteria jobViewCriteria = new JobViewCriteria();
        jobViewCriteria.setDateRangeStart(invalidServerTime);
        jobViewCriteria.setDateRangeEnd(invalidServerTime);
        assertTrue(jemsCrudService().findByCriteria(jobViewCriteria).isEmpty());
    }
}
