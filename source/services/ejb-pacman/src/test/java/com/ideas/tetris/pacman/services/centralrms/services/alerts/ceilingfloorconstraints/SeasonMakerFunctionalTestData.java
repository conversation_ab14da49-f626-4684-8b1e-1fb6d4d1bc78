package com.ideas.tetris.pacman.services.centralrms.services.alerts.ceilingfloorconstraints;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomClass;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomType;
import com.ideas.tetris.pacman.services.centralrms.models.ceilingfloorconstraints.CeilingFloorConstraintsAlertChangeCFSuggestion;
import com.ideas.tetris.pacman.services.centralrms.models.ceilingfloorconstraints.SeasonMakerInputRequest;
import com.ideas.tetris.pacman.services.centralrms.models.ceilingfloorconstraints.SeasonMakerOperations;
import com.ideas.tetris.pacman.services.pricingconfiguration.entity.TransientPricingBaseAccomType;
import com.ideas.tetris.platform.services.daoandentities.entity.Status;
import org.joda.time.DateTime;
import org.joda.time.LocalDate;
import org.joda.time.format.DateTimeFormat;
import org.junit.jupiter.params.provider.Arguments;

import java.math.BigDecimal;
import java.time.DayOfWeek;
import java.util.*;
import java.util.stream.Stream;

public class SeasonMakerFunctionalTestData {
    public static String OCCUPANCY_DATE;
    public static String ROOM_CLASS_CODE;


    public static String BASE_ROOM_TYPE;
    public static String CEILING_VALUE;
    public static String FLOOR_VALUE;
    private static String ID;
    private static String PRODUCT_ID;
    private static String PROPERTY_ID;

    private static String BASE_AT;
    private static String START_DATE;
    private static String END_DATE;
    private static String SUN_CEIL;
    private static String SUN_FLOOR;
    private static String MON_CEIL;
    private static String MON_FLOOR;
    private static String TUE_CEIL;
    private static String TUE_FLOOR;
    private static String WED_CEIL;
    private static String WED_FLOOR;
    private static String THU_CEIL;
    private static String THU_FLOOR;
    private static String FRI_CEIL;
    private static String FRI_FLOOR;
    private static String SAT_CEIL;
    private static String SAT_FLOOR;
    private static String SEASON_NAME;
    public static Set<AccomType> getAllBaseRoomTypes() {
        Set<AccomType> baseRoomTypes = new HashSet<>();
        baseRoomTypes.add(getBaseAccomType("STD","RT1"));
        baseRoomTypes.add(getBaseAccomType("DLX","RT2"));
        baseRoomTypes.add(getBaseAccomType("STE","RT3"));
        return baseRoomTypes;
    }

    public static AccomType getBaseAccomType(String accomClassCode,String accomTypeCode) {
        int accomClassId = roomClassIds.valueOf(accomClassCode).returnID();
        int accomTypeId = roomTypeIds.valueOf(accomTypeCode).returnID();
        AccomClass accomClassObj = new AccomClass();
        accomClassObj.setId(accomClassId);
        accomClassObj.setCode(accomClassCode);
        accomClassObj.setName(accomClassCode);
        AccomType baseAccomTypeObj = new AccomType();
        baseAccomTypeObj.setId(accomTypeId);
        baseAccomTypeObj.setAccomTypeCode(accomTypeCode);
        baseAccomTypeObj.setName(accomTypeCode);
        baseAccomTypeObj.setAccomClass(accomClassObj);
        baseAccomTypeObj.setAccomTypeCapacity(50);
        baseAccomTypeObj.setBaseRoomType(true);
        accomClassObj.setAccomTypes(Set.of(baseAccomTypeObj));
        return baseAccomTypeObj;
    }


    public enum roomClassIds
    {
        STD{@Override public int returnID() {return 1;}  },
        DLX{@Override public int returnID() {return 2;}  },
        STE{@Override public int returnID() {return 3;}  };
        public abstract int returnID();
        roomClassIds(){

        }
    }

    public enum roomTypeIds
    {
        RT1{@Override public int returnID() {return 1;}  },
        RT2{@Override public int returnID() {return 2;}  },
        RT3{@Override public int returnID() {return 3;}  },
        RT4{@Override public int returnID() {return 4;}  },
        RT5{@Override public int returnID() {return 5;}  },
        RT6{@Override public int returnID() {return 6;}  },
        RT7{@Override public int returnID() {return 7;}  },
        RT8{@Override public int returnID() {return 8;}  },
        RT9{@Override public int returnID() {return 9;}  };

        public abstract int returnID();
        roomTypeIds(){

        }
    }

    private static LocalDate caughtUpDate1stJan() {
        return new LocalDate(2025, 1, 1);
    }

    private static LocalDate caughtUpDate19thFeb() {
        return new LocalDate(2025, 02, 19);
    }

    public static Stream<Arguments> seasonMakerProvider() {
        return Stream.of(
                get_TC_1_Data(),
                get_TC_2_Data(),
                get_TC_3_Data(),
                get_TC_4_Data(),
                get_TC_5_Data(),
                get_TC_6_Data(),
                get_TC_7_Data(),
                get_TC_8_Data(),
                get_TC_9_Data(),
                get_TC_10_Data(),
                get_TC_11_Data(),
                get_TC_12_Data(),
                get_TC_13_Data(),
                get_TC_14_Data(),
                get_TC_15_Data(),
                get_TC_16_Data(),
                get_TC_17_Data(),
                get_TC_18_Data(),
                get_TC_19_Data(),
                get_TC_20_Data(),
                get_TC_21_Data()

        );
    }

    private static Arguments get_TC_1_Data() {
        //  Existing Seasons     -->  NA
        //  Applied Seasons      -->  |-------NewS1-------|
        //  Expected New Seasons -->  |-------NewS1-------|

        return Arguments.of(
                "TC 1 - Season Creation--No Existing Season",
                SeasonMakerInputRequest.builder()
                        .changeCFSuggestions(userAppliedSuggestions_TC_1())
                        .userConfiguredSeasons(userConfiguredSeasons_TC_1())
                        .caughtUpDate(caughtUpDate19thFeb())
                        .productId(1)
                        .propertyId(6)
                        .build(),
                expectedSeasons_TC_1()
        );
    }

    private static Arguments get_TC_2_Data() {
        //                         Start            End
        //  Existing Seasons     -->  |------s1-------|
        //  Applied Seasons      -->  |------NewS1----|
        //  Deleted Seasons     -->   |------s1-------|
        //  Expected New Seasons -->  |------NewS1----|
        return Arguments.of(
                "TC 2- Season Creation--Existing Overlapping Season Present",
                SeasonMakerInputRequest.builder()
                        .changeCFSuggestions(userAppliedSuggestions_TC_2())
                        .userConfiguredSeasons(userConfiguredSeasons_TC_2())
                        .caughtUpDate(caughtUpDate19thFeb())
                        .productId(1)
                        .propertyId(6)
                        .build(),
                expectedSeasons_TC_2()
        );
    }

    private static Arguments get_TC_3_Data() {
        //                         Start            End
        //  Existing Seasons     -->  |------s1------|
        //  Applied Seasons      -->                    |-------NewS1-------|
        //  Skipped Seasons     -->   |------s1------|
        //  Expected New Seasons -->                    |-------NewS1-------|

        return Arguments.of(
                "TC 3- Season Creation--Existing Non Overlapping Season Present",
                SeasonMakerInputRequest.builder()
                        .changeCFSuggestions(userAppliedSuggestions_TC_3())
                        .userConfiguredSeasons(userConfiguredSeasons_TC_3())
                        .caughtUpDate(caughtUpDate19thFeb())
                        .productId(1)
                        .propertyId(6)
                        .build(),
                expectedSeasons_TC_3()
        );
    }

    private static Arguments get_TC_4_Data() {
        //  All Ceiling Floor values remain same across seasons
        //  Existing Seasons     -->  |------s1------|       |--------s2--------|
        //  Applied Seasons      -->                                     |-----News1----|
        //  Skipped Seasons      -->  |------s1------|
        //  Deleted Seasons      -->                         |--------s2--------|
        //  Expected New Seasons -->                         |-----NewS1----------------|
        return Arguments.of(
                "TC 4- Season Creation--Existing  Overlapping Season Present",
                SeasonMakerInputRequest.builder()
                        .changeCFSuggestions(userAppliedSuggestions_TC_4())
                        .userConfiguredSeasons(userConfiguredSeasons_TC_4())
                        .caughtUpDate(caughtUpDate19thFeb())
                        .productId(1)
                        .propertyId(6)
                        .build(),
                expectedSeasons_TC_4()
        );
    }

    private static Arguments get_TC_5_Data() {
        //  All Ceiling Floor values  different in user applied suggestion
        //  Existing Seasons     -->  |------s1------|       |--------s2--------|
        //  Applied Seasons      -->                                     |-----News1----|
        //  Skipped Seasons      -->  |------s1------|
        //  Deleted Seasons      -->                         |--------s2--------|
        //  Expected New Seasons -->                         |-----NewS1----------------|
        return Arguments.of(
                "TC 5- Season Creation--Existing  Overlapping Season Present",
                SeasonMakerInputRequest.builder()
                        .changeCFSuggestions(userAppliedSuggestions_TC_5())
                        .userConfiguredSeasons(userConfiguredSeasons_TC_5())
                        .caughtUpDate(caughtUpDate19thFeb())
                        .productId(1)
                        .propertyId(6)
                        .build(),
                expectedSeasons_TC_5()
        );
    }



    private static Arguments get_TC_6_Data() {
        //  New Season creation when 3 Room Classes exist
        //  Existing Seasons     -->  |------s1------|       |--------s2--------|
        //  Applied Seasons      -->                                     |-----News1----|
        //  Skipped Seasons      -->  |------s1------|
        //  Deleted Seasons      -->                         |--------s2--------|
        //  Expected New Seasons -->                         |-----NewS1----------------|
        return Arguments.of(
                "TC 6- 3 RC Season Creation--New Season Creation",
                SeasonMakerInputRequest.builder()
                        .changeCFSuggestions(userAppliedSuggestions_TC_6())
                        .userConfiguredSeasons(userConfiguredSeasons_TC_6())
                        .caughtUpDate(caughtUpDate19thFeb())
                        .productId(1)
                        .propertyId(6)
                        .build(),
                expectedSeasons_TC_6()
        );
    }

    private static Arguments get_TC_7_Data() {
        //  New Season creation when 3 Room Classes exist
        //  Existing Seasons     -->  |------s1------|       |--------s2--------|
        //  Applied Seasons      -->               |-----News1----|
        //  Skipped Seasons      -->  NA
        //  Deleted Seasons      -->  |------s1------|       |--------s2--------|
        //  Expected New Seasons -->  |-----NewS1-------||-----NewS2------------|
        return Arguments.of(
                "TC 7- 3 RC Season Creation--Existing two Season Overlapping",
                SeasonMakerInputRequest.builder()
                        .changeCFSuggestions(userAppliedSuggestions_TC_7())
                        .userConfiguredSeasons(userConfiguredSeasons_TC_7())
                        .caughtUpDate(caughtUpDate19thFeb())
                        .productId(1)
                        .propertyId(6)
                        .build(),
                expectedSeasons_TC_7()
        );
    }
    private static Arguments get_TC_8_Data() {
        //  New Season creation when 3 Room Classes exist
        //  Existing Seasons     -->              |------s1------|
        //  Applied Seasons      -->           |----------News1-------|
        //  Skipped Seasons      -->  NA
        //  Deleted Seasons      -->              |------s1------|
        //  Expected New Seasons -->           |----------NewS1--------|
        return Arguments.of(
                "TC 8- 3 RC Season Creation--Applied suggestions Overlapping Existing one Season ",
                SeasonMakerInputRequest.builder()
                        .changeCFSuggestions(userAppliedSuggestions_TC_8())
                        .userConfiguredSeasons(userConfiguredSeasons_TC_8())
                        .caughtUpDate(caughtUpDate19thFeb())
                        .productId(1)
                        .propertyId(6)
                        .build(),
                expectedSeasons_TC_8()
        );
    }
    private static Arguments get_TC_9_Data() {
        //  All Ceiling Floor values  different in user applied suggestion
        //  Existing Seasons     -->  |------s1------|       |--------s2--------|
        //  Applied Seasons      -->              |-----News1----|
        //  Skipped Seasons      -->  NA
        //  Deleted Seasons      -->  |------s1------|        |--------s2--------|
        //  Expected New Seasons -->  |------------NewS1-------------------------|
        return Arguments.of(
                "TC 9- Season Creation--Existing Overlapping Seasons Present in alternative days",
                SeasonMakerInputRequest.builder()
                        .changeCFSuggestions(userAppliedSuggestions_TC_9())
                        .userConfiguredSeasons(userConfiguredSeasons_TC_9())
                        .caughtUpDate(caughtUpDate19thFeb())
                        .productId(1)
                        .propertyId(6)
                        .build(),
                expectedSeasons_TC_9()
        );
    }

    private static Arguments get_TC_10_Data() {
        //  Existing Seasons     -->  |------s1------|       |--------s2--------|
        //  Applied Seasons      -->              |--------------------News1----------------------------|
        //  Skipped Seasons      -->  NA
        //  Deleted Seasons      -->  |------s1------|        |--------s2--------|
        //  Expected New Seasons -->  |-NewS1-|-NewS2-|-NewS3-|-NewS4-|-NewS5-|-NewS6-------------------|
        return Arguments.of(
                "TC 10- Season Creation--Existing Overlapping 1 day Seasons Present and alternative suggested dates",
                SeasonMakerInputRequest.builder()
                        .changeCFSuggestions(userAppliedSuggestions_TC_10())
                        .userConfiguredSeasons(userConfiguredSeasons_TC_10())
                        .caughtUpDate(caughtUpDate19thFeb())
                        .productId(1)
                        .propertyId(6)
                        .build(),
                expectedSeasons_TC_10()
        );
    }

    private static Arguments get_TC_11_Data() {
        //  All Ceiling Floor values remain same across seasons
        //  Existing Seasons     -->       |--------s1--------|
        //  Applied Seasons      -->                  |-News1-|
        //  Skipped Seasons      -->
        //  Deleted Seasons      -->       |--------s1--------|
        //  Expected New Seasons -->       |-----NewS1--------|
        return Arguments.of(
                "TC 11- Season Creation--Applied Suggestions partially overlaps with existing Season ",
                SeasonMakerInputRequest.builder()
                        .changeCFSuggestions(userAppliedSuggestions_TC_11())
                        .userConfiguredSeasons(userConfiguredSeasons_TC_11())
                        .caughtUpDate(caughtUpDate19thFeb())
                        .productId(1)
                        .propertyId(6)
                        .build(),
                expectedSeasons_TC_11()
        );
    }
    private static Arguments get_TC_12_Data() {
        //  All Ceiling Floor values remain same across seasons
        //  Existing Seasons     -->       |----------s1-----------|   |------s2-----|
        //  Applied Seasons      -->       |--News1-------||-News2-|   |-----NewS3---||News4|
        //  Skipped Seasons      -->
        //  Deleted Seasons      -->       |----------s1-----------|   |------s2-----|
        //  Expected New Seasons -->       |--News1-------||-News2-|   |-----NewS3---||News4|
        return Arguments.of(
                "TC 12- Season Creation--Applied Suggestions partially overlaps with existing Seasons and season creation breaks at when repetating DOW values are different",
                SeasonMakerInputRequest.builder()
                        .changeCFSuggestions(userAppliedSuggestions_TC_12())
                        .userConfiguredSeasons(userConfiguredSeasons_TC_12())
                        .caughtUpDate(caughtUpDate1stJan())
                        .productId(1)
                        .propertyId(6)
                        .build(),
                expectedSeasons_TC_12()
        );
    }
    private static Arguments get_TC_13_Data() {
        //  All Ceiling Floor values remain same across seasons
        //  Existing Seasons     -->       |----------s1-----------|   |------s2-----|
        //  Applied Seasons      -->       |--News1-------||-News2-|   |-----NewS3---||News4|
        //  Skipped Seasons      -->
        //  Deleted Seasons      -->       |----------s1-----------|   |------s2-----|
        //  Expected New Seasons -->       |--News1-------||-News2-|   |-----NewS3----------|
        return Arguments.of(
                "TC 13- Season Creation--Applied Suggestions partially overlaps with existing Seasons and season creation continues at when repeating DOW values are same",
                SeasonMakerInputRequest.builder()
                        .changeCFSuggestions(userAppliedSuggestions_TC_13())
                        .userConfiguredSeasons(userConfiguredSeasons_TC_13())
                        .caughtUpDate(caughtUpDate1stJan())
                        .productId(1)
                        .propertyId(6)
                        .build(),
                expectedSeasons_TC_13()
        );
    }

    private static Arguments get_TC_14_Data() {
        //  All Ceiling Floor values remain same across seasons
        //  Existing Seasons     -->           |----------s1---------|     |------s2-----|
        //  Applied Seasons      -->       |--------------------News1------------------------|
        //  Skipped Seasons      -->
        //  Deleted Seasons      -->           |----------s1---------|     |------s2-----|
        //  Expected New Seasons -->       |------------------News1-------------||---NewS2---|
        return Arguments.of(
                "TC 14- Season Creation--Applied Suggestions overlaps with two existing Seasons",
                SeasonMakerInputRequest.builder()
                        .changeCFSuggestions(userAppliedSuggestions_TC_14())
                        .userConfiguredSeasons(userConfiguredSeasons_TC_14())
                        .caughtUpDate(caughtUpDate1stJan())
                        .productId(1)
                        .propertyId(6)
                        .build(),
                expectedSeasons_TC_14()
        );
    }
    private static Arguments get_TC_15_Data() {
        //  All Ceiling Floor values remain same across seasons
        //  Existing Seasons     -->           |----------s1--------|                       |------s2-----|
        //  Applied Seasons      -->                                 |---NewS1--||----News2|
        //  Skipped Seasons      -->           |----------s1--------|                       |------s2-----|
        //  Deleted Seasons      -->
        //  Expected New Seasons -->                                 |---NewS1--||----News2|
        return Arguments.of(
                "TC 15- Season Creation--Applied Suggestions overlaps with second existing Seasons and starts after first season",
                SeasonMakerInputRequest.builder()
                        .changeCFSuggestions(userAppliedSuggestions_TC_15())
                        .userConfiguredSeasons(userConfiguredSeasons_TC_15())
                        .caughtUpDate(caughtUpDate1stJan())
                        .productId(1)
                        .propertyId(6)
                        .build(),
                expectedSeasons_TC_15()
        );
    }
    private static Arguments get_TC_16_Data() {
        //  Existing Seasons     -->  |------s1------||--------s2--------||--------s3--------|
        //  Applied Seasons      -->  |-News1-|       |-News2-|         |-News3-|
        //  Skipped Seasons      -->  NA
        //  Deleted Seasons      -->  |------s1------||--------s2--------||--------s3--------|
        //  Expected New Seasons -->  |-NewS1-----------|-NewS2-----------|-NewS3----|-NewS4-|
        return Arguments.of(
                "TC 16- Season Creation--Existing partially overlapping 3 seasons and suggested dates are start date of each seasons",
                SeasonMakerInputRequest.builder()
                        .changeCFSuggestions(userAppliedSuggestions_TC_16())
                        .userConfiguredSeasons(userConfiguredSeasons_TC_16())
                        .caughtUpDate(caughtUpDate19thFeb())
                        .productId(1)
                        .propertyId(6)
                        .build(),
                expectedSeasons_TC_16()
        );
    }
    private static Arguments get_TC_17_Data() {
        //  Existing Seasons     -->  |------s1------||--------s2--------||--------s3--------|
        //  Applied Seasons      -->         |-News1-|           |-News2-|           |-News3-|
        //  Skipped Seasons      -->  NA
        //  Deleted Seasons      -->  |------s1------||--------s2--------||--------s3--------|
        //  Expected New Seasons -->  |-NewS1-----------|-NewS2-----------|-NewS3------------|
        return Arguments.of(
                "TC 17- Season Creation--Existing partially overlapping 3 seasons and suggested dates are end date of each seasons",
                SeasonMakerInputRequest.builder()
                        .changeCFSuggestions(userAppliedSuggestions_TC_17())
                        .userConfiguredSeasons(userConfiguredSeasons_TC_17())
                        .caughtUpDate(caughtUpDate19thFeb())
                        .productId(1)
                        .propertyId(6)
                        .build(),
                expectedSeasons_TC_17()
        );
    }
    private static Arguments get_TC_18_Data() {
        //  Existing Seasons     -->  |----s1---|      |----s2----|      |------s3-----|
        //  Applied Seasons      -->            |-NewS1|          |-NewS2|
        //  Skipped Seasons      -->  NA
        //  Deleted Seasons      -->  |----s1---|      |----s2----|
        //  Expected New Seasons -->  |------------NewS1-------------||--------NewS2---|
        return Arguments.of(
                "TC 18- Season Creation--Existing 3 seasons with gap in between and suggested dates are feeling the gap for the seasons",
                SeasonMakerInputRequest.builder()
                        .changeCFSuggestions(userAppliedSuggestions_TC_18())
                        .userConfiguredSeasons(userConfiguredSeasons_TC_18())
                        .caughtUpDate(caughtUpDate19thFeb())
                        .productId(1)
                        .propertyId(6)
                        .build(),
                expectedSeasons_TC_18()
        );
    }
    private static Arguments get_TC_19_Data() {
        //  All Ceiling Floor values remain same across seasons
        //  Existing Seasons     -->            |------s1-----|                       |------s2-----|           |------s3--------|
        //  Applied Seasons      -->  |--NewS1-||----News2----||-NewS3--|  |-NewS4--| |----News5----| |-NewS6--||----News7-------|
        //  Skipped Seasons      -->
        //  Deleted Seasons      -->            |------s1-----|                       |------s2-----|           |------s3--------|
        //  Expected New Seasons -->  |--NewS1-||----News2----||-NewS3--|  |-NewS4--| |----News5----| |-NewS6--||----News7-------|
        return Arguments.of(
                "TC 19- Season Creation--Applied Suggestions overlaps with three existing Seasons at start, middle and between seasons as well",
                SeasonMakerInputRequest.builder()
                        .changeCFSuggestions(userAppliedSuggestions_TC_19())
                        .userConfiguredSeasons(userConfiguredSeasons_TC_19())
                        .caughtUpDate(caughtUpDate1stJan())
                        .productId(1)
                        .propertyId(6)
                        .build(),
                expectedSeasons_TC_19()
        );
    }
    private static Arguments get_TC_20_Data() {
        //  All Ceiling Floor values remain same across seasons
        //  Existing Seasons     -->
        //  Applied Seasons      -->  |--NewS1--|  |--News2--|  |--NewS3--|  |-NewS4--|
        //  Skipped Seasons      -->
        //  Deleted Seasons      -->
        //  Expected New Seasons -->  |--NewS1--|  |--News2--|  |--NewS3--|  |-NewS4--|
        return Arguments.of(
                "TC 20- Season Creation--No Existing Season present and applied suggestions are alternate dates",
                SeasonMakerInputRequest.builder()
                        .changeCFSuggestions(userAppliedSuggestions_TC_20())
                        .userConfiguredSeasons(userConfiguredSeasons_TC_20())
                        .caughtUpDate(caughtUpDate1stJan())
                        .productId(1)
                        .propertyId(6)
                        .build(),
                expectedSeasons_TC_20()
        );
    }
    private static Arguments get_TC_21_Data() {
        //  All Ceiling Floor values remain same across seasons
        //CaughtupDate           -->                 1st jan
        //  Existing Seasons     -->  |-------------------S1-----------------------|
        //  Applied Seasons      -->                         |--NewS1--|
        //  Skipped Seasons      -->
        //  Updated Seasons      -->  |-------------------S1-----------------------|
        //  Expected New Seasons -->  |--NewS1-----||--News2--------||-----News3---|
        return Arguments.of(
                "TC 21- Season Creation--Partially in past Existing Season present and applied suggestion is overlapping with season after caughtup date",
                SeasonMakerInputRequest.builder()
                        .changeCFSuggestions(userAppliedSuggestions_TC_21())
                        .userConfiguredSeasons(userConfiguredSeasons_TC_21())
                        .caughtUpDate(caughtUpDate1stJan())
                        .productId(1)
                        .propertyId(6)
                        .build(),
                expectedSeasons_TC_21()
        );
    }
    private static List<TransientPricingBaseAccomType> userConfiguredSeasons_TC_1() {
        String[][] seasonsUserConfigured =
                {
                        { ID  ,PRODUCT_ID ,PROPERTY_ID ,ROOM_CLASS_CODE , BASE_AT ,START_DATE  ,END_DATE    ,SUN_CEIL ,SUN_FLOOR ,MON_CEIL ,MON_FLOOR ,TUE_CEIL ,TUE_FLOOR ,WED_CEIL ,WED_FLOOR ,THU_CEIL ,THU_FLOOR ,FRI_CEIL ,FRI_FLOOR ,SAT_CEIL ,SAT_FLOOR , SEASON_NAME }
                };
        return (seasonsUserConfigured.length!=1?getSeasonsList(seasonsUserConfigured,true):Collections.emptyList());
    }


    private static List<TransientPricingBaseAccomType> userConfiguredSeasons_TC_2() {
        String[][] seasonsUserConfigured =
                {
                        { ID  ,PRODUCT_ID ,PROPERTY_ID ,ROOM_CLASS_CODE , BASE_AT ,START_DATE  ,END_DATE    ,SUN_CEIL ,SUN_FLOOR ,MON_CEIL ,MON_FLOOR ,TUE_CEIL ,TUE_FLOOR ,WED_CEIL ,WED_FLOOR ,THU_CEIL ,THU_FLOOR ,FRI_CEIL ,FRI_FLOOR ,SAT_CEIL ,SAT_FLOOR , SEASON_NAME },
                        { "1" ,"1"        ,"6"         , "STD"          , "RT1"   ,"2025-02-20","2025-02-26","200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   , "s1"  }
                };
        return (seasonsUserConfigured.length!=1?getSeasonsList(seasonsUserConfigured,true):Collections.emptyList());
    }

    private static List<TransientPricingBaseAccomType> userConfiguredSeasons_TC_3() {
        String[][] seasonsUserConfigured =
                {
                        { ID  ,PRODUCT_ID ,PROPERTY_ID ,ROOM_CLASS_CODE , BASE_AT ,START_DATE  ,END_DATE    ,SUN_CEIL ,SUN_FLOOR ,MON_CEIL ,MON_FLOOR ,TUE_CEIL ,TUE_FLOOR ,WED_CEIL ,WED_FLOOR ,THU_CEIL ,THU_FLOOR ,FRI_CEIL ,FRI_FLOOR ,SAT_CEIL ,SAT_FLOOR , SEASON_NAME },
                        { "1" ,"1"        ,"6"         , "STD"          , "RT1"   ,"2025-02-20","2025-02-26","200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   , "s1"  }
                };
        return (seasonsUserConfigured.length!=1?getSeasonsList(seasonsUserConfigured,true):Collections.emptyList());
    }

    private static List<TransientPricingBaseAccomType> userConfiguredSeasons_TC_4() {
        String[][] seasonsUserConfigured =
                {
                        { ID  ,PRODUCT_ID ,PROPERTY_ID ,ROOM_CLASS_CODE , BASE_AT ,START_DATE  ,END_DATE    ,SUN_CEIL ,SUN_FLOOR ,MON_CEIL ,MON_FLOOR ,TUE_CEIL ,TUE_FLOOR ,WED_CEIL ,WED_FLOOR ,THU_CEIL ,THU_FLOOR ,FRI_CEIL ,FRI_FLOOR ,SAT_CEIL ,SAT_FLOOR , SEASON_NAME },
                        { "1" ,"1"        ,"6"         , "STD"          , "RT1"   ,"2025-02-20","2025-02-26","200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   , "s1"  },
                        { "2" ,"1"        ,"6"         , "STD"          , "RT1"   ,"2025-03-13","2025-03-22","200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   , "s2"  }
                };
        return (seasonsUserConfigured.length!=1?getSeasonsList(seasonsUserConfigured,true):Collections.emptyList());
    }


    private static List<TransientPricingBaseAccomType> userConfiguredSeasons_TC_5() {
        String[][] seasonsUserConfigured =
                {
                        { ID  ,PRODUCT_ID ,PROPERTY_ID ,ROOM_CLASS_CODE , BASE_AT ,START_DATE  ,END_DATE    ,SUN_CEIL ,SUN_FLOOR ,MON_CEIL ,MON_FLOOR ,TUE_CEIL ,TUE_FLOOR ,WED_CEIL ,WED_FLOOR ,THU_CEIL ,THU_FLOOR ,FRI_CEIL ,FRI_FLOOR ,SAT_CEIL ,SAT_FLOOR , SEASON_NAME },
                        { "1" ,"1"        ,"6"         , "STD"          , "RT1"   ,"2025-02-20","2025-02-26","200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   , "s1"  },
                        { "2" ,"1"        ,"6"         , "STD"          , "RT1"   ,"2025-03-13","2025-03-22","200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   , "s2"  }
                };
        return (seasonsUserConfigured.length!=1?getSeasonsList(seasonsUserConfigured,true):Collections.emptyList());
    }


    private static List<TransientPricingBaseAccomType> userConfiguredSeasons_TC_6() {
        String[][] seasonsUserConfigured =
                {
                        { ID  ,PRODUCT_ID ,PROPERTY_ID ,ROOM_CLASS_CODE , BASE_AT ,START_DATE  ,END_DATE    ,SUN_CEIL ,SUN_FLOOR ,MON_CEIL ,MON_FLOOR ,TUE_CEIL ,TUE_FLOOR ,WED_CEIL ,WED_FLOOR ,THU_CEIL ,THU_FLOOR ,FRI_CEIL ,FRI_FLOOR ,SAT_CEIL ,SAT_FLOOR , SEASON_NAME },
                        //Season - s1
                        { "1" ,"1"        ,"6"         , "STD"          , "RT1"   ,"2025-02-20","2025-02-26","200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   , "s1"  },
                        { "2" ,"1"        ,"6"         , "DLX"          , "RT2"   ,"2025-02-20","2025-02-26","200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   , "s1"  },
                        { "3" ,"1"        ,"6"         , "STE"          , "RT3"   ,"2025-02-20","2025-02-26","200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   , "s1"  },
                        //Season - s2
                        { "4" ,"1"        ,"6"         , "STD"          , "RT1"   ,"2025-03-13","2025-03-22","200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   , "s2"  },
                        { "5" ,"1"        ,"6"         , "DLX"          , "RT2"   ,"2025-03-13","2025-03-22","200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   , "s2"  },
                        { "6" ,"1"        ,"6"         , "STE"          , "RT3"   ,"2025-03-13","2025-03-22","200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   , "s2"  }

                };
        return (seasonsUserConfigured.length!=1?getSeasonsList(seasonsUserConfigured,true):Collections.emptyList());
    }

    private static List<TransientPricingBaseAccomType> userConfiguredSeasons_TC_7() {
        String[][] seasonsUserConfigured =
                {
                        { ID  ,PRODUCT_ID ,PROPERTY_ID ,ROOM_CLASS_CODE , BASE_AT ,START_DATE  ,END_DATE    ,SUN_CEIL ,SUN_FLOOR ,MON_CEIL ,MON_FLOOR ,TUE_CEIL ,TUE_FLOOR ,WED_CEIL ,WED_FLOOR ,THU_CEIL ,THU_FLOOR ,FRI_CEIL ,FRI_FLOOR ,SAT_CEIL ,SAT_FLOOR , SEASON_NAME },
                        //Season - s1
                        { "1" ,"1"        ,"6"         , "STD"          , "RT1"   ,"2025-02-20","2025-02-26","200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   , "s1"  },
                        { "2" ,"1"        ,"6"         , "DLX"          , "RT2"   ,"2025-02-20","2025-02-26","200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   , "s1"  },
                        { "3" ,"1"        ,"6"         , "STE"          , "RT3"   ,"2025-02-20","2025-02-26","200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   , "s1"  },
                        //Season - s2
                        { "4" ,"1"        ,"6"         , "STD"          , "RT1"   ,"2025-03-03","2025-03-12","200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   , "s2"  },
                        { "5" ,"1"        ,"6"         , "DLX"          , "RT2"   ,"2025-03-03","2025-03-12","200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   , "s2"  },
                        { "6" ,"1"        ,"6"         , "STE"          , "RT3"   ,"2025-03-03","2025-03-12","200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   , "s2"  }

                };
        return (seasonsUserConfigured.length!=1?getSeasonsList(seasonsUserConfigured,true):Collections.emptyList());
    }

    private static List<TransientPricingBaseAccomType> userConfiguredSeasons_TC_8() {
        String[][] seasonsUserConfigured =
                {
                        { ID  ,PRODUCT_ID ,PROPERTY_ID ,ROOM_CLASS_CODE , BASE_AT ,START_DATE  ,END_DATE    ,SUN_CEIL ,SUN_FLOOR ,MON_CEIL ,MON_FLOOR ,TUE_CEIL ,TUE_FLOOR ,WED_CEIL ,WED_FLOOR ,THU_CEIL ,THU_FLOOR ,FRI_CEIL ,FRI_FLOOR ,SAT_CEIL ,SAT_FLOOR , SEASON_NAME },
                        //Season - s1
                        { "1" ,"1"        ,"6"         , "STD"          , "RT1"   ,"2025-02-25","2025-03-03","200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   , "s1"  },
                        { "2" ,"1"        ,"6"         , "DLX"          , "RT2"   ,"2025-02-25","2025-03-03","200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   , "s1"  },
                        { "3" ,"1"        ,"6"         , "STE"          , "RT3"   ,"2025-02-25","2025-03-03","200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   , "s1"  },

                };
        return (seasonsUserConfigured.length!=1?getSeasonsList(seasonsUserConfigured,true):Collections.emptyList());
    }
    private static List<TransientPricingBaseAccomType> userConfiguredSeasons_TC_11() {
        String[][] seasonsUserConfigured =
                {
                        { ID  ,PRODUCT_ID ,PROPERTY_ID ,ROOM_CLASS_CODE , BASE_AT ,START_DATE  ,END_DATE    ,SUN_CEIL ,SUN_FLOOR ,MON_CEIL ,MON_FLOOR ,TUE_CEIL ,TUE_FLOOR ,WED_CEIL ,WED_FLOOR ,THU_CEIL ,THU_FLOOR ,FRI_CEIL ,FRI_FLOOR ,SAT_CEIL ,SAT_FLOOR , SEASON_NAME },
                        { "1" ,"1"        ,"6"         , "STD"          , "RT1"   ,"2025-04-20","2025-04-26","200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   , "s1"  }

                };
        return (seasonsUserConfigured.length!=1?getSeasonsList(seasonsUserConfigured,true):Collections.emptyList());
    }

    private static List<TransientPricingBaseAccomType> userConfiguredSeasons_TC_9() {
        String[][] seasonsUserConfigured =
                {
                        { ID  ,PRODUCT_ID ,PROPERTY_ID ,ROOM_CLASS_CODE , BASE_AT ,START_DATE  ,END_DATE    ,SUN_CEIL ,SUN_FLOOR ,MON_CEIL ,MON_FLOOR ,TUE_CEIL ,TUE_FLOOR ,WED_CEIL ,WED_FLOOR ,THU_CEIL ,THU_FLOOR ,FRI_CEIL ,FRI_FLOOR ,SAT_CEIL ,SAT_FLOOR , SEASON_NAME },
                        { "1" ,"1"        ,"6"         , "STD"          , "RT1"   ,"2025-02-20","2025-02-20","200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   , "s1"  },
                        { "2" ,"1"        ,"6"         , "STD"          , "RT1"   ,"2025-02-22","2025-02-22","200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   , "s2"  }
                };
        return (seasonsUserConfigured.length!=1?getSeasonsList(seasonsUserConfigured,true):Collections.emptyList());
    }

    private static List<TransientPricingBaseAccomType> userConfiguredSeasons_TC_10() {
        String[][] seasonsUserConfigured =
                {
                        { ID  ,PRODUCT_ID ,PROPERTY_ID ,ROOM_CLASS_CODE , BASE_AT ,START_DATE  ,END_DATE    ,SUN_CEIL ,SUN_FLOOR ,MON_CEIL ,MON_FLOOR ,TUE_CEIL ,TUE_FLOOR ,WED_CEIL ,WED_FLOOR ,THU_CEIL ,THU_FLOOR ,FRI_CEIL ,FRI_FLOOR ,SAT_CEIL ,SAT_FLOOR , SEASON_NAME },
                        { "1" ,"1"        ,"6"         , "STD"          , "RT1"   ,"2025-02-20","2025-02-20", "null"  ,  "null"  , "null"  , "null"   , "null"  ,  "null"  , "null"  ,  "null"  , "201.00", "76.00"  , "null"  ,  "null"  ,  "null" ,  "null"  ,     "s1"    },
                        { "1" ,"1"        ,"6"         , "STD"          , "RT1"   ,"2025-02-22","2025-02-22", "null"  ,  "null"  , "null"  , "null"   , "null"  ,  "null"  , "null"  ,  "null"  ,  "null" ,  "null"  , "null"  ,  "null"  , "201.00", "76.00"  ,     "s2"    },
                };
        return (seasonsUserConfigured.length!=1?getSeasonsList(seasonsUserConfigured,true):Collections.emptyList());
    }

    private static List<TransientPricingBaseAccomType> userConfiguredSeasons_TC_12() {
        String[][] seasonsUserConfigured =
                {
                        { ID  ,PRODUCT_ID ,PROPERTY_ID ,ROOM_CLASS_CODE , BASE_AT ,START_DATE  ,END_DATE    ,SUN_CEIL ,SUN_FLOOR ,MON_CEIL ,MON_FLOOR ,TUE_CEIL ,TUE_FLOOR ,WED_CEIL ,WED_FLOOR ,THU_CEIL ,THU_FLOOR ,FRI_CEIL ,FRI_FLOOR ,SAT_CEIL ,SAT_FLOOR , SEASON_NAME },
                        { "1" ,"1"        ,"6"         , "STD"          , "RT1"   ,"2025-01-20","2025-02-18", "100.00" , "50.00" , "101.00" , "51.00" , "102.00" , "52.00"  , "103.00" , "53.00" , "104.00" , "54.00" , "105.00" , "55.00" , "106.00" , "56.00"  , "s1"    },
                        { "2" ,"1"        ,"6"         , "STD"          , "RT1"   ,"2025-02-20","2025-02-28", "107.00" , "57.00"  , "108.00" , "58.00"  , "109.00" , "59.00"  , "110.00" , "60.00" , "111.00" , "61.00" , "112.00" , "62.00" , "113.00" , "63.00"  , "s2"    }

                };
        return (seasonsUserConfigured.length!=1?getSeasonsList(seasonsUserConfigured,true):Collections.emptyList());
    }

    private static List<TransientPricingBaseAccomType> userConfiguredSeasons_TC_13() {
        String[][] seasonsUserConfigured =
                {
                        { ID  ,PRODUCT_ID ,PROPERTY_ID ,ROOM_CLASS_CODE , BASE_AT ,START_DATE  ,END_DATE    ,SUN_CEIL ,SUN_FLOOR ,MON_CEIL ,MON_FLOOR ,TUE_CEIL ,TUE_FLOOR ,WED_CEIL ,WED_FLOOR ,THU_CEIL ,THU_FLOOR ,FRI_CEIL ,FRI_FLOOR ,SAT_CEIL ,SAT_FLOOR , SEASON_NAME },
                        { "1" ,"1"        ,"6"         , "STD"          , "RT1"   ,"2025-01-20","2025-02-18", "100.00" , "50.00" , "101.00" , "51.00" , "102.00" , "52.00"  , "103.00" , "53.00" , "104.00" , "54.00" , "105.00" , "55.00" , "106.00" , "56.00"  , "s1"    },
                        { "2" ,"1"        ,"6"         , "STD"          , "RT1"   ,"2025-02-20","2025-02-28", "107.00" , "57.00"  , "108.00" , "58.00"  , "109.00" , "59.00"  , "110.00" , "60.00" , "111.00" , "61.00" , "112.00" , "62.00" , "113.00" , "63.00"  , "s2"    }

                };
        return (seasonsUserConfigured.length!=1?getSeasonsList(seasonsUserConfigured,true):Collections.emptyList());
    }

    private static List<TransientPricingBaseAccomType> userConfiguredSeasons_TC_14() {
        String[][] seasonsUserConfigured =
                {
                        { ID  ,PRODUCT_ID ,PROPERTY_ID ,ROOM_CLASS_CODE , BASE_AT ,START_DATE  ,END_DATE    ,SUN_CEIL ,SUN_FLOOR ,MON_CEIL ,MON_FLOOR ,TUE_CEIL ,TUE_FLOOR ,WED_CEIL ,WED_FLOOR ,THU_CEIL ,THU_FLOOR ,FRI_CEIL ,FRI_FLOOR ,SAT_CEIL ,SAT_FLOOR , SEASON_NAME },
                        { "1" ,"1"        ,"6"         , "STD"          , "RT1"   ,"2025-02-17","2025-02-20","null" ,"null"   ,"201.00" ,"75.00"   ,"202.00" ,"76.00"   ,"203.00" ,"77.00"   ,"204.00" ,"78.00"   ,"null" ,"null"   ,"null" ,"null"   , "s1"  },
                        { "2" ,"1"        ,"6"         , "STD"          , "RT1"   ,"2025-02-23","2025-02-25","null" ,"null"   ,"201.00" ,"75.00"   ,"202.00" ,"76.00"   ,"203.00" ,"77.00"   ,"204.00" ,"78.00"   ,"null" ,"null"   ,"null" ,"null"   , "s2"  }
                };
        return (seasonsUserConfigured.length!=1?getSeasonsList(seasonsUserConfigured,true):Collections.emptyList());
    }
    private static List<TransientPricingBaseAccomType> userConfiguredSeasons_TC_15() {
        String[][] seasonsUserConfigured =
                {
                        { ID  ,PRODUCT_ID ,PROPERTY_ID ,ROOM_CLASS_CODE , BASE_AT ,START_DATE  ,END_DATE    ,SUN_CEIL ,SUN_FLOOR ,MON_CEIL ,MON_FLOOR ,TUE_CEIL ,TUE_FLOOR ,WED_CEIL ,WED_FLOOR ,THU_CEIL ,THU_FLOOR ,FRI_CEIL ,FRI_FLOOR ,SAT_CEIL ,SAT_FLOOR , SEASON_NAME },
                        { "1" ,"1"        ,"6"         , "STD"          , "RT1"   ,"2025-01-20","2025-02-15", "100.00" , "50.00" , "101.00" , "51.00" , "102.00" , "52.00"  , "103.00" , "53.00" , "104.00" , "54.00" , "105.00" , "55.00" , "106.00" , "56.00"  , "s1"    },
                        { "2" ,"1"        ,"6"         , "STD"          , "RT1"   ,"2025-02-26","2025-03-02", "107.00" , "57.00"  , "108.00" , "58.00"  , "109.00" , "59.00"  , "110.00" , "60.00" , "111.00" , "61.00" , "112.00" , "62.00" , "113.00" , "63.00"  , "s2"    }

                };
        return (seasonsUserConfigured.length!=1?getSeasonsList(seasonsUserConfigured,true):Collections.emptyList());
    }

    private static List<TransientPricingBaseAccomType> userConfiguredSeasons_TC_16() {
        String[][] seasonsUserConfigured =
                {
                        { ID  ,PRODUCT_ID ,PROPERTY_ID ,ROOM_CLASS_CODE , BASE_AT ,START_DATE  ,END_DATE    ,SUN_CEIL ,SUN_FLOOR ,MON_CEIL ,MON_FLOOR ,TUE_CEIL ,TUE_FLOOR ,WED_CEIL ,WED_FLOOR ,THU_CEIL ,THU_FLOOR ,FRI_CEIL ,FRI_FLOOR ,SAT_CEIL ,SAT_FLOOR , SEASON_NAME },

                        { "1" ,"1"        ,"6"         , "STD"          , "RT1"   ,"2025-02-25","2025-03-01","200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   , "s1"  },
                        { "2" ,"1"        ,"6"         , "STD"          , "RT1"   ,"2025-03-02","2025-03-10","200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   , "s2"  },
                        { "3" ,"1"        ,"6"         , "STD"          , "RT1"   ,"2025-03-11","2025-03-18","200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   , "s3"  },

                };
        return (seasonsUserConfigured.length!=1?getSeasonsList(seasonsUserConfigured,true):Collections.emptyList());
    }

    private static List<TransientPricingBaseAccomType> userConfiguredSeasons_TC_17() {
        String[][] seasonsUserConfigured =
                {
                        { ID  ,PRODUCT_ID ,PROPERTY_ID ,ROOM_CLASS_CODE , BASE_AT ,START_DATE  ,END_DATE    ,SUN_CEIL ,SUN_FLOOR ,MON_CEIL ,MON_FLOOR ,TUE_CEIL ,TUE_FLOOR ,WED_CEIL ,WED_FLOOR ,THU_CEIL ,THU_FLOOR ,FRI_CEIL ,FRI_FLOOR ,SAT_CEIL ,SAT_FLOOR , SEASON_NAME },
                        { "1" ,"1"        ,"6"         , "STD"          , "RT1"   ,"2025-02-25","2025-03-01","200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   , "s1"  },
                        { "2" ,"1"        ,"6"         , "STD"          , "RT1"   ,"2025-03-02","2025-03-10","200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   , "s2"  },
                        { "3" ,"1"        ,"6"         , "STD"          , "RT1"   ,"2025-03-11","2025-03-18","200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   , "s3"  },

                };
        return (seasonsUserConfigured.length!=1?getSeasonsList(seasonsUserConfigured,true):Collections.emptyList());
    }

    private static List<TransientPricingBaseAccomType> userConfiguredSeasons_TC_18() {
        String[][] seasonsUserConfigured =
                {
                        { ID  ,PRODUCT_ID ,PROPERTY_ID ,ROOM_CLASS_CODE , BASE_AT ,START_DATE  ,END_DATE    ,SUN_CEIL ,SUN_FLOOR ,MON_CEIL ,MON_FLOOR ,TUE_CEIL ,TUE_FLOOR ,WED_CEIL ,WED_FLOOR ,THU_CEIL ,THU_FLOOR ,FRI_CEIL ,FRI_FLOOR ,SAT_CEIL ,SAT_FLOOR , SEASON_NAME },
                        { "1" ,"1"        ,"6"         , "STD"          , "RT1"   ,"2025-03-03","2025-03-05","null"   ,"null"    ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00"   ,"75"    ,"null"   ,"null"    ,"null"   ,"null"    ,"null"   ,"null"     , "s1"  },
                        { "2" ,"1"        ,"6"         , "STD"          , "RT1"   ,"2025-03-06","2025-03-08","null"   ,"null"    ,"null"   ,"null"    ,"null"   ,"null"    ,"null"   ,"null"    ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"    , "s2"  },
                        { "3" ,"1"        ,"6"         , "STD"          , "RT1"   ,"2025-03-09","2025-03-09","200.00" ,"75.00"   ,"null"   ,"null"    ,"null"   ,"null"    ,"null"   ,"null"    ,"null"   ,"null"    ,"null"   ,"null"    ,"null"   ,"null"     , "s3"  },

                };
        return (seasonsUserConfigured.length!=1?getSeasonsList(seasonsUserConfigured,true):Collections.emptyList());
    }
    private static List<TransientPricingBaseAccomType> userConfiguredSeasons_TC_19() {
        String[][] seasonsUserConfigured =
                {
                        { ID  ,PRODUCT_ID ,PROPERTY_ID ,ROOM_CLASS_CODE , BASE_AT ,START_DATE  ,END_DATE    ,SUN_CEIL ,SUN_FLOOR ,MON_CEIL ,MON_FLOOR ,TUE_CEIL ,TUE_FLOOR ,WED_CEIL ,WED_FLOOR ,THU_CEIL ,THU_FLOOR ,FRI_CEIL ,FRI_FLOOR ,SAT_CEIL ,SAT_FLOOR , SEASON_NAME },
                        { "1" ,"1"        ,"6"         , "STD"          , "RT1"   ,"2025-02-01","2025-02-03", "100.00" , "70.00" , "101.00" , "71.00" , "null" , "null"  , "null" , "null", "null" , "null" ,"null" , "null", "106.00" , "76.00"  , "s1"    },
                        { "2" ,"1"        ,"6"         , "STD"          , "RT1"   ,"2025-02-07","2025-02-13", "111.00" , "81.00"  , "112.00" , "82.00"  , "113.00" , "83.00"  , "114.00" , "84.00" , "115.00" , "85.00" , "116.00" , "86.00" , "117.00" , "87.00"  , "s2"    },
                        { "3" ,"1"        ,"6"         , "STD"          , "RT1"   ,"2025-02-17","2025-02-26", "121.00" , "91.00"  , "122.00" , "92.00"  , "123.00" , "93.00"  , "124.00" , "94.00" , "125.00" , "95.00" , "126.00" , "96.00" , "127.00" , "97.00"  , "s3"    }

                };
        return (seasonsUserConfigured.length!=1?getSeasonsList(seasonsUserConfigured,true):Collections.emptyList());
    }
    private static List<TransientPricingBaseAccomType> userConfiguredSeasons_TC_20() {
        String[][] seasonsUserConfigured =
                {
                        { ID  ,PRODUCT_ID ,PROPERTY_ID ,ROOM_CLASS_CODE , BASE_AT ,START_DATE  ,END_DATE    ,SUN_CEIL ,SUN_FLOOR ,MON_CEIL ,MON_FLOOR ,TUE_CEIL ,TUE_FLOOR ,WED_CEIL ,WED_FLOOR ,THU_CEIL ,THU_FLOOR ,FRI_CEIL ,FRI_FLOOR ,SAT_CEIL ,SAT_FLOOR , SEASON_NAME }
                };
        return (seasonsUserConfigured.length!=1?getSeasonsList(seasonsUserConfigured,true):Collections.emptyList());
    }
    private static List<TransientPricingBaseAccomType> userConfiguredSeasons_TC_21() {
        String[][] seasonsUserConfigured =
                {
                        { ID  ,PRODUCT_ID ,PROPERTY_ID ,ROOM_CLASS_CODE , BASE_AT ,START_DATE  ,END_DATE    ,SUN_CEIL ,SUN_FLOOR ,MON_CEIL ,MON_FLOOR ,TUE_CEIL ,TUE_FLOOR ,WED_CEIL ,WED_FLOOR ,THU_CEIL ,THU_FLOOR ,FRI_CEIL ,FRI_FLOOR ,SAT_CEIL ,SAT_FLOOR , SEASON_NAME },
                        { "1" ,"1"        ,"6"         , "STD"          , "RT1"   ,"2024-12-15","2025-01-15", "111.00" , "81.00"  , "112.00" , "82.00"  , "113.00" , "83.00"  , "114.00" , "84.00" , "115.00" , "85.00" , "116.00" , "86.00" , "117.00" , "87.00"  , "s2"    },
                };
        return (seasonsUserConfigured.length!=1?getSeasonsList(seasonsUserConfigured,true):Collections.emptyList());
    }
    private static List<CeilingFloorConstraintsAlertChangeCFSuggestion> userAppliedSuggestions_TC_1()
    {
        String[][] CFSuggestion =
                {
                        { OCCUPANCY_DATE , ROOM_CLASS_CODE , BASE_ROOM_TYPE, CEILING_VALUE, FLOOR_VALUE },
                        { "2025-02-20"   , "STD"           , "RT1"         , "200.00"     , "75.00"     },
                        { "2025-02-21"   , "STD"           , "RT1"         , "200.00"     , "75.00"     },
                        { "2025-02-22"   , "STD"           , "RT1"         , "200.00"     , "75.00"     },
                        { "2025-02-23"   , "STD"           , "RT1"         , "200.00"     , "75.00"     },
                        { "2025-02-24"   , "STD"           , "RT1"         , "200.00"     , "75.00"     },
                        { "2025-02-25"   , "STD"           , "RT1"         , "200.00"     , "75.00"     },
                        { "2025-02-26"   , "STD"           , "RT1"         , "200.00"     , "75.00"     }
                };
        return getUserAppliedSuggestionsList(CFSuggestion);
    }

    private static List<CeilingFloorConstraintsAlertChangeCFSuggestion> userAppliedSuggestions_TC_2()
    {
        String[][] CFSuggestion =
                {
                        { OCCUPANCY_DATE , ROOM_CLASS_CODE , BASE_ROOM_TYPE, CEILING_VALUE, FLOOR_VALUE },
                        { "2025-02-20"   , "STD"           , "RT1"         , "200.00"     , "75.00"     },
                        { "2025-02-21"   , "STD"           , "RT1"         , "200.00"     , "75.00"     },
                        { "2025-02-22"   , "STD"           , "RT1"         , "200.00"     , "75.00"     },
                        { "2025-02-23"   , "STD"           , "RT1"         , "200.00"     , "75.00"     },
                        { "2025-02-24"   , "STD"           , "RT1"         , "200.00"     , "75.00"     },
                        { "2025-02-25"   , "STD"           , "RT1"         , "200.00"     , "75.00"     },
                        { "2025-02-26"   , "STD"           , "RT1"         , "200.00"     , "75.00"     }
                };
        return getUserAppliedSuggestionsList(CFSuggestion);
    }

    private static List<CeilingFloorConstraintsAlertChangeCFSuggestion> userAppliedSuggestions_TC_3()
    {
        String[][] CFSuggestion =
                {
                        { OCCUPANCY_DATE , ROOM_CLASS_CODE , BASE_ROOM_TYPE, CEILING_VALUE, FLOOR_VALUE },
                        { "2025-03-20"   , "STD"           , "RT1"         , "200.00"     , "75.00"     },
                        { "2025-03-21"   , "STD"           , "RT1"         , "200.00"     , "75.00"     },
                        { "2025-03-22"   , "STD"           , "RT1"         , "200.00"     , "75.00"     },
                        { "2025-03-23"   , "STD"           , "RT1"         , "200.00"     , "75.00"     },
                        { "2025-03-24"   , "STD"           , "RT1"         , "200.00"     , "75.00"     },
                        { "2025-03-25"   , "STD"           , "RT1"         , "200.00"     , "75.00"     },
                        { "2025-03-26"   , "STD"           , "RT1"         , "200.00"     , "75.00"     }
                };
        return getUserAppliedSuggestionsList(CFSuggestion);
    }

    private static List<CeilingFloorConstraintsAlertChangeCFSuggestion> userAppliedSuggestions_TC_4()
    {
        String[][] CFSuggestion =
                {
                        { OCCUPANCY_DATE , ROOM_CLASS_CODE , BASE_ROOM_TYPE, CEILING_VALUE, FLOOR_VALUE },
                        { "2025-03-20"   , "STD"           , "RT1"         , "200.00"     , "75.00"     },
                        { "2025-03-21"   , "STD"           , "RT1"         , "200.00"     , "75.00"     },
                        { "2025-03-22"   , "STD"           , "RT1"         , "200.00"     , "75.00"     },
                        { "2025-03-23"   , "STD"           , "RT1"         , "200.00"     , "75.00"     },
                        { "2025-03-24"   , "STD"           , "RT1"         , "200.00"     , "75.00"     },
                        { "2025-03-25"   , "STD"           , "RT1"         , "200.00"     , "75.00"     },
                        { "2025-03-26"   , "STD"           , "RT1"         , "200.00"     , "75.00"     }
                };
        return getUserAppliedSuggestionsList(CFSuggestion);
    }


    private static List<CeilingFloorConstraintsAlertChangeCFSuggestion> userAppliedSuggestions_TC_5()
    {
        String[][] CFSuggestion =
                {
                        { OCCUPANCY_DATE , ROOM_CLASS_CODE , BASE_ROOM_TYPE, CEILING_VALUE, FLOOR_VALUE },
                        { "2025-03-20"   , "STD"           , "RT1"         , "201.00"     , "76.00"     },
                        { "2025-03-21"   , "STD"           , "RT1"         , "201.00"     , "76.00"     },
                        { "2025-03-22"   , "STD"           , "RT1"         , "201.00"     , "76.00"     },
                        { "2025-03-23"   , "STD"           , "RT1"         , "201.00"     , "76.00"     },
                        { "2025-03-24"   , "STD"           , "RT1"         , "201.00"     , "76.00"     },
                        { "2025-03-25"   , "STD"           , "RT1"         , "201.00"     , "76.00"     },
                        { "2025-03-26"   , "STD"           , "RT1"         , "201.00"     , "76.00"     }
                };
        return getUserAppliedSuggestionsList(CFSuggestion);
    }


    private static List<CeilingFloorConstraintsAlertChangeCFSuggestion> userAppliedSuggestions_TC_6()
    {
        String[][] CFSuggestion =
                {
                        { OCCUPANCY_DATE , ROOM_CLASS_CODE , BASE_ROOM_TYPE, CEILING_VALUE, FLOOR_VALUE },
                        { "2025-03-20"   , "STD"           , "RT1"         , "201.00"     , "76.00"     },
                        { "2025-03-20"   , "DLX"           , "RT2"         , "201.00"     , "76.00"     },
                        { "2025-03-20"   , "STE"           , "RT3"         , "201.00"     , "76.00"     },
                        { "2025-03-21"   , "STD"           , "RT1"         , "201.00"     , "76.00"     },
                        { "2025-03-21"   , "DLX"           , "RT2"         , "201.00"     , "76.00"     },
                        { "2025-03-21"   , "STE"           , "RT3"         , "201.00"     , "76.00"     },
                        { "2025-03-22"   , "STD"           , "RT1"         , "201.00"     , "76.00"     },
                        { "2025-03-22"   , "DLX"           , "RT2"         , "201.00"     , "76.00"     },
                        { "2025-03-22"   , "STE"           , "RT3"         , "201.00"     , "76.00"     },
                        { "2025-03-23"   , "STD"           , "RT1"         , "201.00"     , "76.00"     },
                        { "2025-03-23"   , "DLX"           , "RT2"         , "201.00"     , "76.00"     },
                        { "2025-03-23"   , "STE"           , "RT3"         , "201.00"     , "76.00"     },
                        { "2025-03-24"   , "STD"           , "RT1"         , "201.00"     , "76.00"     },
                        { "2025-03-24"   , "DLX"           , "RT2"         , "201.00"     , "76.00"     },
                        { "2025-03-24"   , "STE"           , "RT3"         , "201.00"     , "76.00"     },
                        { "2025-03-25"   , "STD"           , "RT1"         , "201.00"     , "76.00"     },
                        { "2025-03-25"   , "DLX"           , "RT2"         , "201.00"     , "76.00"     },
                        { "2025-03-25"   , "STE"           , "RT3"         , "201.00"     , "76.00"     },
                        { "2025-03-26"   , "STD"           , "RT1"         , "201.00"     , "76.00"     },
                        { "2025-03-26"   , "DLX"           , "RT2"         , "201.00"     , "76.00"     },
                        { "2025-03-26"   , "STE"           , "RT3"         , "201.00"     , "76.00"     }
                };
        return getUserAppliedSuggestionsList(CFSuggestion);
    }
    private static List<CeilingFloorConstraintsAlertChangeCFSuggestion> userAppliedSuggestions_TC_7()
    {
        String[][] CFSuggestion =
                {
                        { OCCUPANCY_DATE , ROOM_CLASS_CODE , BASE_ROOM_TYPE, CEILING_VALUE, FLOOR_VALUE },
                        { "2025-02-26"   , "STD"           , "RT1"         , "201.00"     , "76.00"     },
                        { "2025-02-26"   , "DLX"           , "RT2"         , "201.00"     , "76.00"     },
                        { "2025-02-26"   , "STE"           , "RT3"         , "201.00"     , "76.00"     },
                        { "2025-02-27"   , "STD"           , "RT1"         , "201.00"     , "76.00"     },
                        { "2025-02-27"   , "DLX"           , "RT2"         , "201.00"     , "76.00"     },
                        { "2025-02-27"   , "STE"           , "RT3"         , "201.00"     , "76.00"     },
                        { "2025-02-28"   , "STD"           , "RT1"         , "201.00"     , "76.00"     },
                        { "2025-02-28"   , "DLX"           , "RT2"         , "201.00"     , "76.00"     },
                        { "2025-02-28"   , "STE"           , "RT3"         , "201.00"     , "76.00"     },
                        { "2025-03-01"   , "STD"           , "RT1"         , "201.00"     , "76.00"     },
                        { "2025-03-01"   , "DLX"           , "RT2"         , "201.00"     , "76.00"     },
                        { "2025-03-01"   , "STE"           , "RT3"         , "201.00"     , "76.00"     },
                        { "2025-03-02"   , "STD"           , "RT1"         , "201.00"     , "76.00"     },
                        { "2025-03-02"   , "DLX"           , "RT2"         , "201.00"     , "76.00"     },
                        { "2025-03-02"   , "STE"           , "RT3"         , "201.00"     , "76.00"     },
                        { "2025-03-03"   , "STD"           , "RT1"         , "201.00"     , "76.00"     },
                        { "2025-03-03"   , "DLX"           , "RT2"         , "201.00"     , "76.00"     },
                        { "2025-03-03"   , "STE"           , "RT3"         , "201.00"     , "76.00"     },
                        { "2025-03-04"   , "STD"           , "RT1"         , "201.00"     , "76.00"     },
                        { "2025-03-04"   , "DLX"           , "RT2"         , "201.00"     , "76.00"     },
                        { "2025-03-04"   , "STE"           , "RT3"         , "201.00"     , "76.00"     }
                };
        return getUserAppliedSuggestionsList(CFSuggestion);
    }
    private static List<CeilingFloorConstraintsAlertChangeCFSuggestion> userAppliedSuggestions_TC_8()
    {
        String[][] CFSuggestion =
                {
                        { OCCUPANCY_DATE , ROOM_CLASS_CODE , BASE_ROOM_TYPE, CEILING_VALUE, FLOOR_VALUE },
                        { "2025-02-24"   , "STD"           , "RT1"         , "201.00"     , "76.00"     },
                        { "2025-02-24"   , "DLX"           , "RT2"         , "201.00"     , "76.00"     },
                        { "2025-02-24"   , "STE"           , "RT3"         , "201.00"     , "76.00"     },
                        { "2025-02-25"   , "STD"           , "RT1"         , "201.00"     , "76.00"     },
                        { "2025-02-25"   , "DLX"           , "RT2"         , "201.00"     , "76.00"     },
                        { "2025-02-25"   , "STE"           , "RT3"         , "201.00"     , "76.00"     },
                        { "2025-02-26"   , "STD"           , "RT1"         , "201.00"     , "76.00"     },
                        { "2025-02-26"   , "DLX"           , "RT2"         , "201.00"     , "76.00"     },
                        { "2025-02-26"   , "STE"           , "RT3"         , "201.00"     , "76.00"     },
                        { "2025-02-27"   , "STD"           , "RT1"         , "201.00"     , "76.00"     },
                        { "2025-02-27"   , "DLX"           , "RT2"         , "201.00"     , "76.00"     },
                        { "2025-02-27"   , "STE"           , "RT3"         , "201.00"     , "76.00"     },
                        { "2025-02-28"   , "STD"           , "RT1"         , "201.00"     , "76.00"     },
                        { "2025-02-28"   , "DLX"           , "RT2"         , "201.00"     , "76.00"     },
                        { "2025-02-28"   , "STE"           , "RT3"         , "201.00"     , "76.00"     },
                        { "2025-03-01"   , "STD"           , "RT1"         , "201.00"     , "76.00"     },
                        { "2025-03-01"   , "DLX"           , "RT2"         , "201.00"     , "76.00"     },
                        { "2025-03-01"   , "STE"           , "RT3"         , "201.00"     , "76.00"     },
                        { "2025-03-02"   , "STD"           , "RT1"         , "201.00"     , "76.00"     },
                        { "2025-03-02"   , "DLX"           , "RT2"         , "201.00"     , "76.00"     },
                        { "2025-03-02"   , "STE"           , "RT3"         , "201.00"     , "76.00"     },
                        { "2025-03-03"   , "STD"           , "RT1"         , "201.00"     , "76.00"     },
                        { "2025-03-03"   , "DLX"           , "RT2"         , "201.00"     , "76.00"     },
                        { "2025-03-03"   , "STE"           , "RT3"         , "201.00"     , "76.00"     },
                        { "2025-03-04"   , "STD"           , "RT1"         , "201.00"     , "76.00"     },
                        { "2025-03-04"   , "DLX"           , "RT2"         , "201.00"     , "76.00"    },
                        { "2025-03-04"   , "STE"           , "RT3"         , "201.00"     , "76.00"     }
                };
        return getUserAppliedSuggestionsList(CFSuggestion);
    }
    private static List<CeilingFloorConstraintsAlertChangeCFSuggestion> userAppliedSuggestions_TC_9()
    {
        String[][] CFSuggestion =
                {
                        { OCCUPANCY_DATE , ROOM_CLASS_CODE , BASE_ROOM_TYPE, CEILING_VALUE, FLOOR_VALUE },
                        { "2025-02-20"   , "STD"           , "RT1"         , "201.00"     , "76.00"     },
                        { "2025-02-21"   , "STD"           , "RT1"         , "201.00"     , "76.00"     },
                        { "2025-02-22"   , "STD"           , "RT1"         , "201.00"     , "76.00"     },
                        { "2025-02-23"   , "STD"           , "RT1"         , "201.00"     , "76.00"     },
                        { "2025-02-24"   , "STD"           , "RT1"         , "201.00"     , "76.00"     },
                        { "2025-02-25"   , "STD"           , "RT1"         , "201.00"     , "76.00"     },
                        { "2025-02-26"   , "STD"           , "RT1"         , "201.00"     , "76.00"     }
                };
        return getUserAppliedSuggestionsList(CFSuggestion);
    }
    private static List<CeilingFloorConstraintsAlertChangeCFSuggestion> userAppliedSuggestions_TC_10()
    {
        String[][] CFSuggestion =
                {
                        { OCCUPANCY_DATE , ROOM_CLASS_CODE , BASE_ROOM_TYPE, CEILING_VALUE, FLOOR_VALUE },
                        { "2025-02-20"   , "STD"           , "RT1"         , "201.00"     , "76.00"     },
                        { "2025-02-22"   , "STD"           , "RT1"         , "201.00"     , "76.00"     },
                        { "2025-02-24"   , "STD"           , "RT1"         , "201.00"     , "76.00"     },
                        { "2025-02-26"   , "STD"           , "RT1"         , "201.00"     , "76.00"     },
                        { "2025-02-28"   , "STD"           , "RT1"         , "201.00"     , "76.00"     },
                        { "2025-03-01"   , "STD"           , "RT1"         , "201.00"     , "76.00"     },
                        { "2025-03-02"   , "STD"           , "RT1"         , "201.00"     , "76.00"     }
                };
        return getUserAppliedSuggestionsList(CFSuggestion);
    }

    private static List<CeilingFloorConstraintsAlertChangeCFSuggestion> userAppliedSuggestions_TC_11()
    {
        String[][] CFSuggestion =
                {
                        { OCCUPANCY_DATE , ROOM_CLASS_CODE , BASE_ROOM_TYPE, CEILING_VALUE, FLOOR_VALUE },
                        { "2025-04-24"   , "STD"           , "RT1"         , "201.00"     , "76.00"     },
                        { "2025-04-25"   , "STD"           , "RT1"         , "201.00"     , "76.00"     },
                        { "2025-04-26"   , "STD"           , "RT1"         , "201.00"     , "76.00"     }
                };
        return getUserAppliedSuggestionsList(CFSuggestion);
    }

    private static List<CeilingFloorConstraintsAlertChangeCFSuggestion> userAppliedSuggestions_TC_12()
    {
        String[][] CFSuggestion =
                {
                        { OCCUPANCY_DATE , ROOM_CLASS_CODE , BASE_ROOM_TYPE, CEILING_VALUE, FLOOR_VALUE },
                        { "2025-02-16"   , "STD"           , "RT1"         , "201.00"     , "50.00"     },
                        { "2025-02-18"   , "STD"           , "RT1"         , "203.00"     , "52.00"     },
                        { "2025-02-22"   , "STD"           , "RT1"         , "201.00"     , "63.00"     },
                        { "2025-03-01"   , "STD"           , "RT1"         , "201.00"     , "70.00"     }
                };
        return getUserAppliedSuggestionsList(CFSuggestion);
    }
    private static List<CeilingFloorConstraintsAlertChangeCFSuggestion> userAppliedSuggestions_TC_13()
    {
        String[][] CFSuggestion =
                {
                        { OCCUPANCY_DATE , ROOM_CLASS_CODE , BASE_ROOM_TYPE, CEILING_VALUE, FLOOR_VALUE },
                        { "2025-02-16"   , "STD"           , "RT1"         , "201.00"     , "50.00"     },
                        { "2025-02-18"   , "STD"           , "RT1"         , "203.00"     , "52.00"     },
                        { "2025-02-22"   , "STD"           , "RT1"         , "201.00"     , "63.00"     },
                        { "2025-03-01"   , "STD"           , "RT1"         , "201.00"     , "63.00"     }
                };
        return getUserAppliedSuggestionsList(CFSuggestion);
    }
    private static List<CeilingFloorConstraintsAlertChangeCFSuggestion> userAppliedSuggestions_TC_14()
    {
        String[][] CFSuggestion =
                {
                        { OCCUPANCY_DATE , ROOM_CLASS_CODE , BASE_ROOM_TYPE, CEILING_VALUE, FLOOR_VALUE },
                        { "2025-02-16"   , "STD"           , "RT1"         , "210.00"     , "74.00"     },
                        { "2025-02-17"   , "STD"           , "RT1"         , "211.00"     , "75.00"     },
                        { "2025-02-18"   , "STD"           , "RT1"         , "212.00"     , "76.00"     },
                        { "2025-02-19"   , "STD"           , "RT1"         , "213.00"     , "77.00"     },
                        { "2025-02-20"   , "STD"           , "RT1"         , "214.00"     , "78.00"     },
                        { "2025-02-21"   , "STD"           , "RT1"         , "215.00"     , "79.00"     },
                        { "2025-02-22"   , "STD"           , "RT1"         , "216.00"     , "80.00"     },
                        { "2025-02-23"   , "STD"           , "RT1"         , "210.00"     , "74.00"     },
                        { "2025-02-24"   , "STD"           , "RT1"         , "211.00"     , "75.00"     },
                        { "2025-02-25"   , "STD"           , "RT1"         , "212.00"     , "76.00"     },
                        { "2025-02-26"   , "STD"           , "RT1"         , "213.00"     , "80.00"     },
                        { "2025-02-27"   , "STD"           , "RT1"         , "214.00"     , "80.00"     }

                };
        return getUserAppliedSuggestionsList(CFSuggestion);
    }
    private static List<CeilingFloorConstraintsAlertChangeCFSuggestion> userAppliedSuggestions_TC_15()
    {
        String[][] CFSuggestion =
                {
                        { OCCUPANCY_DATE , ROOM_CLASS_CODE , BASE_ROOM_TYPE, CEILING_VALUE, FLOOR_VALUE },
                        { "2025-02-16"   , "STD"           , "RT1"         , "210.00"     , "70.00"     },
                        { "2025-02-17"   , "STD"           , "RT1"         , "211.00"     , "70.00"     },
                        { "2025-02-18"   , "STD"           , "RT1"         , "212.00"     , "70.00"     },
                        { "2025-02-19"   , "STD"           , "RT1"         , "213.00"     , "70.00"     },
                        { "2025-02-20"   , "STD"           , "RT1"         , "214.00"     , "70.00"     },
                        { "2025-02-21"   , "STD"           , "RT1"         , "215.00"     , "70.00"     },
                        { "2025-02-22"   , "STD"           , "RT1"         , "216.00"     , "70.00"     },
                        { "2025-02-23"   , "STD"           , "RT1"         , "217.00"     , "70.00"     },
                        { "2025-02-24"   , "STD"           , "RT1"         , "218.00"     , "70.00"     },
                        { "2025-02-25"   , "STD"           , "RT1"         , "219.00"     , "70.00"     }


                };
        return getUserAppliedSuggestionsList(CFSuggestion);
    }

    private static List<CeilingFloorConstraintsAlertChangeCFSuggestion> userAppliedSuggestions_TC_16()
    {
        String[][] CFSuggestion =
                {
                        { OCCUPANCY_DATE , ROOM_CLASS_CODE , BASE_ROOM_TYPE, CEILING_VALUE, FLOOR_VALUE },
                        { "2025-02-25"   , "STD"           , "RT1"         , "210.00"     , "70.00"     },
                        { "2025-03-02"   , "STD"           , "RT1"         , "211.00"     , "70.00"     },
                        { "2025-03-11"   , "STD"           , "RT1"         , "212.00"     , "70.00"     }

                };
        return getUserAppliedSuggestionsList(CFSuggestion);
    }

    private static List<CeilingFloorConstraintsAlertChangeCFSuggestion> userAppliedSuggestions_TC_17()
    {
        String[][] CFSuggestion =
                {
                        { OCCUPANCY_DATE , ROOM_CLASS_CODE , BASE_ROOM_TYPE, CEILING_VALUE, FLOOR_VALUE },
                        { "2025-03-01"   , "STD"           , "RT1"         , "210.00"     , "70.00"     },
                        { "2025-03-10"   , "STD"           , "RT1"         , "211.00"     , "70.00"     },
                        { "2025-03-18"   , "STD"           , "RT1"         , "212.00"     , "70.00"     }

                };
        return getUserAppliedSuggestionsList(CFSuggestion);
    }

    private static List<CeilingFloorConstraintsAlertChangeCFSuggestion> userAppliedSuggestions_TC_18()
    {
        String[][] CFSuggestion =
                {
                        { OCCUPANCY_DATE , ROOM_CLASS_CODE , BASE_ROOM_TYPE, CEILING_VALUE, FLOOR_VALUE },
                        { "2025-03-05"   , "STD"           , "RT1"         , "210.00"     , "70.00"     },
                        { "2025-03-06"   , "STD"           , "RT1"         , "211.00"     , "70.00"     },


                };
        return getUserAppliedSuggestionsList(CFSuggestion);
    }
    private static List<CeilingFloorConstraintsAlertChangeCFSuggestion> userAppliedSuggestions_TC_19()
    {
        String[][] CFSuggestion =
                {
                        { OCCUPANCY_DATE , ROOM_CLASS_CODE , BASE_ROOM_TYPE, CEILING_VALUE, FLOOR_VALUE },
                        { "2025-01-28"   , "STD"           , "RT1"         , "210.00"     , "70.00"     },
                        { "2025-02-02"   , "STD"           , "RT1"         , "215.00"     , "75.00"     },
                        { "2025-02-04"   , "STD"           , "RT1"         , "217.00"     , "70.00"     },
                        { "2025-02-06"   , "STD"           , "RT1"         , "219.00"     , "70.00"     },
                        { "2025-02-10"   , "STD"           , "RT1"         , "223.00"     , "82.00"     },
                        { "2025-02-11"   , "STD"           , "RT1"         , "224.00"     , "83.00"     },
                        { "2025-02-13"   , "STD"           , "RT1"         , "226.00"     , "85.00"     },
                        { "2025-02-15"   , "STD"           , "RT1"         , "228.00"     , "70.00"     },
                        { "2025-02-16"   , "STD"           , "RT1"         , "229.00"     , "70.00"     },
                        { "2025-02-20"   , "STD"           , "RT1"         , "233.00"     , "95.00"     }


                };
        return getUserAppliedSuggestionsList(CFSuggestion);
    }
    private static List<CeilingFloorConstraintsAlertChangeCFSuggestion> userAppliedSuggestions_TC_20()
    {
        String[][] CFSuggestion =
                {
                        { OCCUPANCY_DATE , ROOM_CLASS_CODE , BASE_ROOM_TYPE, CEILING_VALUE, FLOOR_VALUE },
                        { "2025-01-01"   , "STD"           , "RT1"         , "210.00"     , "70.00"     },
                        { "2025-01-03"   , "STD"           , "RT1"         , "215.00"     , "75.00"     },
                        { "2025-01-05"   , "STD"           , "RT1"         , "217.00"     , "70.00"     },
                        { "2025-01-07"   , "STD"           , "RT1"         , "219.00"     , "70.00"     },

                };
        return getUserAppliedSuggestionsList(CFSuggestion);
    }
    private static List<CeilingFloorConstraintsAlertChangeCFSuggestion> userAppliedSuggestions_TC_21()
    {
        String[][] CFSuggestion =
                {
                        { OCCUPANCY_DATE , ROOM_CLASS_CODE , BASE_ROOM_TYPE, CEILING_VALUE, FLOOR_VALUE },
                        { "2025-01-02"   , "STD"           , "RT1"         , "210.00"     , "70.00"     },

                };
        return getUserAppliedSuggestionsList(CFSuggestion);
    }

    private static List<CeilingFloorConstraintsAlertChangeCFSuggestion> getUserAppliedSuggestionsList(String[][] CFSuggestion)
    {
        org.joda.time.format.DateTimeFormatter format = DateTimeFormat.forPattern("yyyy-MM-dd");
        List<CeilingFloorConstraintsAlertChangeCFSuggestion> ls = new ArrayList<>();
        for(int i=1;i<CFSuggestion.length;i++)
        {
            ls.add(CeilingFloorConstraintsAlertChangeCFSuggestion.builder()
                    .occupancyDate(new LocalDate().parse(CFSuggestion[i][0],format))
                    .roomClassCode(CFSuggestion[i][1])
                    .baseRoomTypeCode(CFSuggestion[i][2])
                    .ceilingValue(BigDecimal.valueOf(Double.valueOf(CFSuggestion[i][3])))
                    .floorValue(BigDecimal.valueOf(Double.valueOf(CFSuggestion[i][4])))
                    .build());
        }
        return ls;
    }

    private static SeasonMakerOperations expectedSeasons_TC_1()
    {
        String[][] seasonsCreated =
                {
                        { ID  ,PRODUCT_ID ,PROPERTY_ID ,ROOM_CLASS_CODE , BASE_AT ,START_DATE  ,END_DATE    ,SUN_CEIL ,SUN_FLOOR ,MON_CEIL ,MON_FLOOR ,TUE_CEIL ,TUE_FLOOR ,WED_CEIL ,WED_FLOOR ,THU_CEIL ,THU_FLOOR ,FRI_CEIL ,FRI_FLOOR ,SAT_CEIL ,SAT_FLOOR },
                        { "1" ,"1"        ,"6"         , "STD"          , "RT1"   ,"2025-02-20","2025-02-26","200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   }
                };
        return SeasonMakerOperations.builder()
                .seasonsToBeCreated(getSeasonsList(seasonsCreated,false))
                .seasonsToBeSkipped(Collections.emptyList())
                .seasonsToBeUpdated(Collections.emptyList())
                .seasonsToBeDeleted(Collections.emptyList())
                .build();
    }

    private static SeasonMakerOperations expectedSeasons_TC_2()
    {
        String[][] seasonsCreated =
                {
                        { ID  ,PRODUCT_ID ,PROPERTY_ID ,ROOM_CLASS_CODE , BASE_AT ,START_DATE  ,END_DATE    ,SUN_CEIL ,SUN_FLOOR ,MON_CEIL ,MON_FLOOR ,TUE_CEIL ,TUE_FLOOR ,WED_CEIL ,WED_FLOOR ,THU_CEIL ,THU_FLOOR ,FRI_CEIL ,FRI_FLOOR ,SAT_CEIL ,SAT_FLOOR },
                        { "1" ,"1"        ,"6"         , "STD"          , "RT1"   ,"2025-02-20","2025-02-26","200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   }
                };

        String[][] seasonsDeleted =
                {
                        { ID  ,PRODUCT_ID ,PROPERTY_ID ,ROOM_CLASS_CODE , BASE_AT ,START_DATE  ,END_DATE    ,SUN_CEIL ,SUN_FLOOR ,MON_CEIL ,MON_FLOOR ,TUE_CEIL ,TUE_FLOOR ,WED_CEIL ,WED_FLOOR ,THU_CEIL ,THU_FLOOR ,FRI_CEIL ,FRI_FLOOR ,SAT_CEIL ,SAT_FLOOR, SEASON_NAME },
                        { "1" ,"1"        ,"6"         , "STD"          , "RT1"   ,"2025-02-20","2025-02-26","200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"  , "s1" }
                };

        return SeasonMakerOperations.builder()
                .seasonsToBeCreated(getSeasonsList(seasonsCreated,false))
                .seasonsToBeSkipped(Collections.emptyList())
                .seasonsToBeUpdated(Collections.emptyList())
                .seasonsToBeDeleted(getSeasonsList(seasonsDeleted,true))
                .build();
    }


    private static SeasonMakerOperations expectedSeasons_TC_3()
    {
        String[][] seasonsCreated =
                {
                        { ID  ,PRODUCT_ID ,PROPERTY_ID ,ROOM_CLASS_CODE , BASE_AT ,START_DATE  ,END_DATE    ,SUN_CEIL ,SUN_FLOOR ,MON_CEIL ,MON_FLOOR ,TUE_CEIL ,TUE_FLOOR ,WED_CEIL ,WED_FLOOR ,THU_CEIL ,THU_FLOOR ,FRI_CEIL ,FRI_FLOOR ,SAT_CEIL ,SAT_FLOOR },
                        { "1" ,"1"        ,"6"         , "STD"          , "RT1"   ,"2025-03-20","2025-03-26","200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   }
                };

        String[][] seasonsSkipped =
                {
                        { ID  ,PRODUCT_ID ,PROPERTY_ID ,ROOM_CLASS_CODE , BASE_AT ,START_DATE  ,END_DATE    ,SUN_CEIL ,SUN_FLOOR ,MON_CEIL ,MON_FLOOR ,TUE_CEIL ,TUE_FLOOR ,WED_CEIL ,WED_FLOOR ,THU_CEIL ,THU_FLOOR ,FRI_CEIL ,FRI_FLOOR ,SAT_CEIL ,SAT_FLOOR, SEASON_NAME },
                        { "1" ,"1"        ,"6"         , "STD"          , "RT1"   ,"2025-02-20","2025-02-26","200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"  , "s1" }
                };

        return SeasonMakerOperations.builder()
                .seasonsToBeCreated(getSeasonsList(seasonsCreated,false))
                .seasonsToBeSkipped(getSeasonsList(seasonsSkipped,true))
                .seasonsToBeUpdated(Collections.emptyList())
                .seasonsToBeDeleted(Collections.emptyList())
                .build();
    }


    private static SeasonMakerOperations expectedSeasons_TC_4()
    {
        String[][] seasonsCreated =
                {
                        { ID  ,PRODUCT_ID ,PROPERTY_ID ,ROOM_CLASS_CODE , BASE_AT ,START_DATE  ,END_DATE    ,SUN_CEIL ,SUN_FLOOR ,MON_CEIL ,MON_FLOOR ,TUE_CEIL ,TUE_FLOOR ,WED_CEIL ,WED_FLOOR ,THU_CEIL ,THU_FLOOR ,FRI_CEIL ,FRI_FLOOR ,SAT_CEIL ,SAT_FLOOR },
                        { "1" ,"1"        ,"6"         , "STD"          , "RT1"   ,"2025-03-13","2025-03-26","200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   }
                };

        String[][] seasonsSkipped =
                {
                        { ID  ,PRODUCT_ID ,PROPERTY_ID ,ROOM_CLASS_CODE , BASE_AT ,START_DATE  ,END_DATE    ,SUN_CEIL ,SUN_FLOOR ,MON_CEIL ,MON_FLOOR ,TUE_CEIL ,TUE_FLOOR ,WED_CEIL ,WED_FLOOR ,THU_CEIL ,THU_FLOOR ,FRI_CEIL ,FRI_FLOOR ,SAT_CEIL ,SAT_FLOOR, SEASON_NAME },
                        { "1" ,"1"        ,"6"         , "STD"          , "RT1"   ,"2025-02-20","2025-02-26","200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"  , "s1"        }
                };

        String[][] seasonsDeleted =
                {
                        { ID  ,PRODUCT_ID ,PROPERTY_ID ,ROOM_CLASS_CODE , BASE_AT ,START_DATE  ,END_DATE    ,SUN_CEIL ,SUN_FLOOR ,MON_CEIL ,MON_FLOOR ,TUE_CEIL ,TUE_FLOOR ,WED_CEIL ,WED_FLOOR ,THU_CEIL ,THU_FLOOR ,FRI_CEIL ,FRI_FLOOR ,SAT_CEIL ,SAT_FLOOR, SEASON_NAME  },
                        { "2" ,"1"        ,"6"         , "STD"          , "RT1"   ,"2025-03-13","2025-03-22","200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"  , "s2"         }
                };

        return SeasonMakerOperations.builder()
                .seasonsToBeCreated(getSeasonsList(seasonsCreated,false))
                .seasonsToBeSkipped(getSeasonsList(seasonsSkipped,true))
                .seasonsToBeUpdated(Collections.emptyList())
                .seasonsToBeDeleted(getSeasonsList(seasonsDeleted,true))
                .build();
    }


    private static SeasonMakerOperations expectedSeasons_TC_5()
    {
        String[][] seasonsCreated =
                {
                        { ID  ,PRODUCT_ID ,PROPERTY_ID ,ROOM_CLASS_CODE , BASE_AT ,START_DATE  ,END_DATE    ,SUN_CEIL ,SUN_FLOOR ,MON_CEIL ,MON_FLOOR ,TUE_CEIL ,TUE_FLOOR ,WED_CEIL ,WED_FLOOR ,THU_CEIL ,THU_FLOOR ,FRI_CEIL ,FRI_FLOOR ,SAT_CEIL ,SAT_FLOOR },
                        { "1" ,"1"        ,"6"         , "STD"          , "RT1"   ,"2025-03-13","2025-03-19","200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   },
                        { "2" ,"1"        ,"6"         , "STD"          , "RT1"   ,"2025-03-20","2025-03-26","201.00" ,"76.00"   ,"201.00" ,"76.00"   ,"201.00" ,"76.00"   ,"201.00" ,"76.00"   ,"201.00" ,"76.00"   ,"201.00" ,"76.00"   ,"201.00" ,"76.00"   }
                };

        String[][] seasonsSkipped =
                {
                        { ID  ,PRODUCT_ID ,PROPERTY_ID ,ROOM_CLASS_CODE , BASE_AT ,START_DATE  ,END_DATE    ,SUN_CEIL ,SUN_FLOOR ,MON_CEIL ,MON_FLOOR ,TUE_CEIL ,TUE_FLOOR ,WED_CEIL ,WED_FLOOR ,THU_CEIL ,THU_FLOOR ,FRI_CEIL ,FRI_FLOOR ,SAT_CEIL ,SAT_FLOOR, SEASON_NAME },
                        { "1" ,"1"        ,"6"         , "STD"          , "RT1"   ,"2025-02-20","2025-02-26","200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"  , "s1"        }
                };

        String[][] seasonsDeleted =
                {
                        { ID  ,PRODUCT_ID ,PROPERTY_ID ,ROOM_CLASS_CODE , BASE_AT ,START_DATE  ,END_DATE    ,SUN_CEIL ,SUN_FLOOR ,MON_CEIL ,MON_FLOOR ,TUE_CEIL ,TUE_FLOOR ,WED_CEIL ,WED_FLOOR ,THU_CEIL ,THU_FLOOR ,FRI_CEIL ,FRI_FLOOR ,SAT_CEIL ,SAT_FLOOR , SEASON_NAME  },
                        { "2" ,"1"        ,"6"         , "STD"          , "RT1"   ,"2025-03-13","2025-03-22","200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   , "s2"         }
                };

        return SeasonMakerOperations.builder()
                .seasonsToBeCreated(getSeasonsList(seasonsCreated,false))
                .seasonsToBeSkipped(getSeasonsList(seasonsSkipped,true))
                .seasonsToBeUpdated(Collections.emptyList())
                .seasonsToBeDeleted(getSeasonsList(seasonsDeleted,true))
                .build();
    }


    private static SeasonMakerOperations expectedSeasons_TC_6()
    {
        String[][] seasonsCreated =
                {
                        { ID  ,PRODUCT_ID ,PROPERTY_ID ,ROOM_CLASS_CODE , BASE_AT ,START_DATE  ,END_DATE    ,SUN_CEIL ,SUN_FLOOR ,MON_CEIL ,MON_FLOOR ,TUE_CEIL ,TUE_FLOOR ,WED_CEIL ,WED_FLOOR ,THU_CEIL ,THU_FLOOR ,FRI_CEIL ,FRI_FLOOR ,SAT_CEIL ,SAT_FLOOR },
                        { "1" ,"1"        ,"6"         , "STD"          , "RT1"   ,"2025-03-13","2025-03-19","200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"},
                        { "2" ,"1"        ,"6"         , "DLX"          , "RT2"   ,"2025-03-13","2025-03-19","200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"},
                        { "3" ,"1"        ,"6"         , "STE"          , "RT3"   ,"2025-03-13","2025-03-19","200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"},

                        { "4" ,"1"        ,"6"         , "STD"          , "RT1"   ,"2025-03-20","2025-03-26","201.00" ,"76.00"   ,"201.00" ,"76.00"   ,"201.00" ,"76.00"   ,"201.00" ,"76.00"   ,"201.00" ,"76.00"   ,"201.00" ,"76.00"   ,"201.00" ,"76.00"},
                        { "5" ,"1"        ,"6"         , "DLX"          , "RT2"   ,"2025-03-20","2025-03-26","201.00" ,"76.00"   ,"201.00" ,"76.00"   ,"201.00" ,"76.00"   ,"201.00" ,"76.00"   ,"201.00" ,"76.00"   ,"201.00" ,"76.00"   ,"201.00" ,"76.00"},
                        { "6" ,"1"        ,"6"         , "STE"          , "RT3"   ,"2025-03-20","2025-03-26","201.00" ,"76.00"   ,"201.00" ,"76.00"   ,"201.00" ,"76.00"   ,"201.00" ,"76.00"   ,"201.00" ,"76.00"   ,"201.00" ,"76.00"   ,"201.00" ,"76.00"}

                };

        String[][] seasonsSkipped =
                {
                        { ID  ,PRODUCT_ID ,PROPERTY_ID ,ROOM_CLASS_CODE , BASE_AT ,START_DATE  ,END_DATE    ,SUN_CEIL ,SUN_FLOOR ,MON_CEIL ,MON_FLOOR ,TUE_CEIL ,TUE_FLOOR ,WED_CEIL ,WED_FLOOR ,THU_CEIL ,THU_FLOOR ,FRI_CEIL ,FRI_FLOOR ,SAT_CEIL ,SAT_FLOOR, SEASON_NAME },
                        { "1" ,"1"        ,"6"         , "STD"          , "RT1"   ,"2025-02-20","2025-02-26","200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"  , "s1"        },
                        { "2" ,"1"        ,"6"         , "DLX"          , "RT2"   ,"2025-02-20","2025-02-26","200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"  , "s1"        },
                        { "3" ,"1"        ,"6"         , "STE"          , "RT3"   ,"2025-02-20","2025-02-26","200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"  , "s1"        }

                };

        String[][] seasonsDeleted =
                {
                        { ID  ,PRODUCT_ID ,PROPERTY_ID ,ROOM_CLASS_CODE , BASE_AT ,START_DATE  ,END_DATE    ,SUN_CEIL ,SUN_FLOOR ,MON_CEIL ,MON_FLOOR ,TUE_CEIL ,TUE_FLOOR ,WED_CEIL ,WED_FLOOR ,THU_CEIL ,THU_FLOOR ,FRI_CEIL ,FRI_FLOOR ,SAT_CEIL ,SAT_FLOOR , SEASON_NAME  },
                        { "4" ,"1"        ,"6"         , "STD"          , "RT1"   ,"2025-03-13","2025-03-22","200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"  , "s2"        },
                        { "5" ,"1"        ,"6"         , "DLX"          , "RT2"   ,"2025-03-13","2025-03-22","200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"  , "s2"        },
                        { "6" ,"1"        ,"6"         , "STE"          , "RT3"   ,"2025-03-13","2025-03-22","200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"  , "s2"        }

                };

        return SeasonMakerOperations.builder()
                .seasonsToBeCreated(getSeasonsList(seasonsCreated,false))
                .seasonsToBeSkipped(getSeasonsList(seasonsSkipped,true))
                .seasonsToBeUpdated(Collections.emptyList())
                .seasonsToBeDeleted(getSeasonsList(seasonsDeleted,true))
                .build();
    }

    private static SeasonMakerOperations expectedSeasons_TC_7()
    {
        String[][] seasonsCreated =
                {
                        { ID  ,PRODUCT_ID ,PROPERTY_ID ,ROOM_CLASS_CODE , BASE_AT ,START_DATE  ,END_DATE    ,SUN_CEIL ,SUN_FLOOR ,MON_CEIL ,MON_FLOOR ,TUE_CEIL ,TUE_FLOOR ,WED_CEIL ,WED_FLOOR ,THU_CEIL ,THU_FLOOR ,FRI_CEIL ,FRI_FLOOR ,SAT_CEIL ,SAT_FLOOR },
                        { "1" ,"1"        ,"6"         , "STD"          , "RT1"   ,"2025-02-20","2025-02-26","200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"201.00" ,"76.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"},
                        { "2" ,"1"        ,"6"         , "DLX"          , "RT2"   ,"2025-02-20","2025-02-26","200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"201.00" ,"76.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"},
                        { "3" ,"1"        ,"6"         , "STE"          , "RT3"   ,"2025-02-20","2025-02-26","200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"201.00" ,"76.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"},

                        { "4" ,"1"        ,"6"         , "STD"          , "RT1"   ,"2025-02-27","2025-03-05","201.00" ,"76.00"   ,"201.00" ,"76.00"   ,"201.00" ,"76.00"   ,"200.00" ,"75.00"   ,"201.00" ,"76.00"   ,"201.00" ,"76.00"   ,"201.00" ,"76.00"},
                        { "5" ,"1"        ,"6"         , "DLX"          , "RT2"   ,"2025-02-27","2025-03-05","201.00" ,"76.00"   ,"201.00" ,"76.00"   ,"201.00" ,"76.00"   ,"200.00" ,"75.00"   ,"201.00" ,"76.00"   ,"201.00" ,"76.00"   ,"201.00" ,"76.00"},
                        { "6" ,"1"        ,"6"         , "STE"          , "RT3"   ,"2025-02-27","2025-03-05","201.00" ,"76.00"   ,"201.00" ,"76.00"   ,"201.00" ,"76.00"   ,"200.00" ,"75.00"   ,"201.00" ,"76.00"   ,"201.00" ,"76.00"   ,"201.00" ,"76.00"},

                        { "7" ,"1"        ,"6"         , "STD"          , "RT1"   ,"2025-03-06","2025-03-12","200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"},
                        { "8" ,"1"        ,"6"         , "DLX"          , "RT2"   ,"2025-03-06","2025-03-12","200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"},
                        { "9" ,"1"        ,"6"         , "STE"          , "RT3"   ,"2025-03-06","2025-03-12","200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"}
                };

        String[][] seasonsDeleted =
                {
                        { ID  ,PRODUCT_ID ,PROPERTY_ID ,ROOM_CLASS_CODE , BASE_AT ,START_DATE  ,END_DATE    ,SUN_CEIL ,SUN_FLOOR ,MON_CEIL ,MON_FLOOR ,TUE_CEIL ,TUE_FLOOR ,WED_CEIL ,WED_FLOOR ,THU_CEIL ,THU_FLOOR ,FRI_CEIL ,FRI_FLOOR ,SAT_CEIL ,SAT_FLOOR , SEASON_NAME},
                        { "1" ,"1"        ,"6"         , "STD"          , "RT1"   ,"2025-02-20","2025-02-26","200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00", "s1" },
                        { "2" ,"1"        ,"6"         , "DLX"          , "RT2"   ,"2025-02-20","2025-02-26","200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00", "s1" },
                        { "3" ,"1"        ,"6"         , "STE"          , "RT3"   ,"2025-02-20","2025-02-26","200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00", "s1" },

                        { "4" ,"1"        ,"6"         , "STD"          , "RT1"   ,"2025-03-03","2025-03-12","200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00", "s2" },
                        { "5" ,"1"        ,"6"         , "DLX"          , "RT2"   ,"2025-03-03","2025-03-12","200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00", "s2" },
                        { "6" ,"1"        ,"6"         , "STE"          , "RT3"   ,"2025-03-03","2025-03-12","200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00", "s2" },

                };

        return SeasonMakerOperations.builder()
                .seasonsToBeCreated(getSeasonsList(seasonsCreated,false))
                .seasonsToBeSkipped(Collections.emptyList())
                .seasonsToBeUpdated(Collections.emptyList())
                .seasonsToBeDeleted(getSeasonsList(seasonsDeleted,true))
                .build();
    }
    private static SeasonMakerOperations expectedSeasons_TC_8()
    {
        String[][] seasonsCreated =
                {
                        { ID  ,PRODUCT_ID ,PROPERTY_ID ,ROOM_CLASS_CODE , BASE_AT ,START_DATE  ,END_DATE    ,SUN_CEIL ,SUN_FLOOR ,MON_CEIL ,MON_FLOOR ,TUE_CEIL ,TUE_FLOOR ,WED_CEIL ,WED_FLOOR ,THU_CEIL ,THU_FLOOR ,FRI_CEIL ,FRI_FLOOR ,SAT_CEIL ,SAT_FLOOR },
                        { "1" ,"1"        ,"6"         , "STD"          , "RT1"   ,"2025-02-24","2025-03-04","201.00" ,"76.00"   ,"201.00" ,"76.00"   ,"201.00" ,"76.00"   ,"201.00" ,"76.00"   ,"201.00" ,"76.00"  ,"201.00" ,"76.00"   ,"201.00" ,"76.00"},
                        { "2" ,"1"        ,"6"         , "DLX"          , "RT2"   ,"2025-02-24","2025-03-04","201.00" ,"76.00"   ,"201.00" ,"76.00"   ,"201.00" ,"76.00"   ,"201.00" ,"76.00"   ,"201.00" ,"76.00"  ,"201.00" ,"76.00"   ,"201.00" ,"76.00"},
                        { "3" ,"1"        ,"6"         , "STE"          , "RT3"   ,"2025-02-24","2025-03-04","201.00" ,"76.00"   ,"201.00" ,"76.00"   ,"201.00" ,"76.00"   ,"201.00" ,"76.00"   ,"201.00" ,"76.00"  ,"201.00" ,"76.00"   ,"201.00" ,"76.00"},
                };

        String[][] seasonsDeleted =
                {
                        { ID  ,PRODUCT_ID ,PROPERTY_ID ,ROOM_CLASS_CODE , BASE_AT ,START_DATE  ,END_DATE    ,SUN_CEIL ,SUN_FLOOR ,MON_CEIL ,MON_FLOOR ,TUE_CEIL ,TUE_FLOOR ,WED_CEIL ,WED_FLOOR ,THU_CEIL ,THU_FLOOR ,FRI_CEIL ,FRI_FLOOR ,SAT_CEIL ,SAT_FLOOR , SEASON_NAME},
                        { "1" ,"1"        ,"6"         , "STD"          , "RT1"   ,"2025-02-25","2025-03-03","200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00", "s1" },
                        { "2" ,"1"        ,"6"         , "DLX"          , "RT2"   ,"2025-02-25","2025-03-03","200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00", "s1" },
                        { "3" ,"1"        ,"6"         , "STE"          , "RT3"   ,"2025-02-25","2025-03-03","200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00", "s1" }
                };

        return SeasonMakerOperations.builder()
                .seasonsToBeCreated(getSeasonsList(seasonsCreated,false))
                .seasonsToBeSkipped(Collections.emptyList())
                .seasonsToBeUpdated(Collections.emptyList())
                .seasonsToBeDeleted(getSeasonsList(seasonsDeleted,true))
                .build();
    }
    private static SeasonMakerOperations expectedSeasons_TC_9()
    {
        String[][] seasonsCreated =
                {
                        { ID  ,PRODUCT_ID ,PROPERTY_ID ,ROOM_CLASS_CODE , BASE_AT ,START_DATE  ,END_DATE    ,SUN_CEIL ,SUN_FLOOR ,MON_CEIL ,MON_FLOOR ,TUE_CEIL ,TUE_FLOOR ,WED_CEIL ,WED_FLOOR ,THU_CEIL ,THU_FLOOR ,FRI_CEIL ,FRI_FLOOR ,SAT_CEIL ,SAT_FLOOR },
                        { "1" ,"1"        ,"6"         , "STD"          , "RT1"   ,"2025-02-20","2025-02-26","201.00" ,"76.00"   ,"201.00" ,"76.00"   ,"201.00" ,"76.00"   ,"201.00" ,"76.00"   ,"201.00" ,"76.00"  ,"201.00" ,"76.00"   ,"201.00" ,"76.00"},

                };

        String[][] seasonsDeleted =
                {
                        { ID  ,PRODUCT_ID ,PROPERTY_ID ,ROOM_CLASS_CODE , BASE_AT ,START_DATE  ,END_DATE    ,SUN_CEIL ,SUN_FLOOR ,MON_CEIL ,MON_FLOOR ,TUE_CEIL ,TUE_FLOOR ,WED_CEIL ,WED_FLOOR ,THU_CEIL ,THU_FLOOR ,FRI_CEIL ,FRI_FLOOR ,SAT_CEIL ,SAT_FLOOR , SEASON_NAME},
                        { "1" ,"1"        ,"6"         , "STD"          , "RT1"   ,"2025-02-20","2025-02-20","200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   , "s1"  },
                        { "2" ,"1"        ,"6"         , "STD"          , "RT1"   ,"2025-02-22","2025-02-22","200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   , "s2"  }
                };

        return SeasonMakerOperations.builder()
                .seasonsToBeCreated(getSeasonsList(seasonsCreated,false))
                .seasonsToBeSkipped(Collections.emptyList())
                .seasonsToBeUpdated(Collections.emptyList())
                .seasonsToBeDeleted(getSeasonsList(seasonsDeleted,true))
                .build();
    }
    private static SeasonMakerOperations expectedSeasons_TC_10()
    {
        String[][] seasonsCreated =
                {
                        { ID  ,PRODUCT_ID ,PROPERTY_ID ,ROOM_CLASS_CODE , BASE_AT ,START_DATE  ,END_DATE    ,SUN_CEIL ,SUN_FLOOR ,MON_CEIL ,MON_FLOOR ,TUE_CEIL ,TUE_FLOOR ,WED_CEIL ,WED_FLOOR ,THU_CEIL ,THU_FLOOR ,FRI_CEIL ,FRI_FLOOR ,SAT_CEIL ,SAT_FLOOR },
                        { "1" ,"1"        ,"6"         , "STD"          , "RT1"   ,"2025-02-20","2025-02-20",  "null" ,  "null"  ,  "null" ,   "null" , "null"  ,  "null"  , "null"  ,  "null"  , "201.00","76.00"  ,   "null" ,  "null"  ,  "null" ,  "null"  },
                        { "1" ,"1"        ,"6"         , "STD"          , "RT1"   ,"2025-02-22","2025-02-22",  "null" ,  "null"  ,  "null" ,   "null" , "null"  ,  "null"  , "null"  ,  "null"  ,  "null" ,  "null" ,   "null" ,  "null"  , "201.00", "76.00"  },
                        { "1" ,"1"        ,"6"         , "STD"          , "RT1"   ,"2025-02-24","2025-02-24",  "null" ,  "null"  , "201.00",  "76.00" , "null"  ,  "null"  , "null"  ,  "null"  ,  "null" ,  "null" ,   "null" ,  "null"  ,  "null" ,  "null"  },
                        { "1" ,"1"        ,"6"         , "STD"          , "RT1"   ,"2025-02-26","2025-02-26",  "null" ,  "null"  ,  "null" ,   "null" , "null"  ,  "null"  , "201.00", "76.00"  ,  "null" ,  "null" ,   "null" ,  "null"  ,  "null" ,  "null"  },
                        { "1" ,"1"        ,"6"         , "STD"          , "RT1"   ,"2025-02-28","2025-03-02","201.00" , "76.00"  ,  "null" ,   "null" , "null"  ,  "null"  , "null"  ,  "null"  ,  "null" ,  "null" ,  "201.00", "76.00"  , "201.00", "76.00"  },

                };

        String[][] seasonsDeleted =
                {
                        { ID  ,PRODUCT_ID ,PROPERTY_ID ,ROOM_CLASS_CODE , BASE_AT ,START_DATE   ,END_DATE    ,SUN_CEIL ,SUN_FLOOR ,MON_CEIL ,MON_FLOOR ,TUE_CEIL ,TUE_FLOOR ,WED_CEIL ,WED_FLOOR ,THU_CEIL ,THU_FLOOR ,FRI_CEIL ,FRI_FLOOR ,SAT_CEIL ,SAT_FLOOR , SEASON_NAME },
                        { "1" ,"1"        ,"6"         , "STD"          , "RT1"   ,"2025-02-20" ,"2025-02-20",  "null"  ,   "null", "null"  ,  "null"  , "null"  ,  "null"  ,  "null" ,  "null"  , "201.00","76.00"  ,   "null" ,  "null"  ,  "null" ,  "null"  ,     "s1"    },
                        { "1" ,"1"        ,"6"         , "STD"          , "RT1"   ,"2025-02-22" ,"2025-02-22",  "null"  ,   "null", "null"  ,  "null"  , "null"  ,  "null"  ,  "null" ,  "null"  ,  "null" ,  "null" ,   "null" ,  "null"  , "201.00", "76.00"  ,     "s2"    },
                };

        return SeasonMakerOperations.builder()
                .seasonsToBeCreated(getSeasonsList(seasonsCreated,false))
                .seasonsToBeSkipped(Collections.emptyList())
                .seasonsToBeUpdated(Collections.emptyList())
                .seasonsToBeDeleted(getSeasonsList(seasonsDeleted,true))
                .build();
    }

    private static SeasonMakerOperations expectedSeasons_TC_11()
    {
        String[][] seasonsCreated =
                {
                        { ID  ,PRODUCT_ID ,PROPERTY_ID ,ROOM_CLASS_CODE , BASE_AT ,START_DATE  ,END_DATE    ,SUN_CEIL ,SUN_FLOOR ,MON_CEIL ,MON_FLOOR ,TUE_CEIL ,TUE_FLOOR ,WED_CEIL ,WED_FLOOR ,THU_CEIL ,THU_FLOOR ,FRI_CEIL ,FRI_FLOOR ,SAT_CEIL ,SAT_FLOOR },
                        { "1" ,"1"        ,"6"         , "STD"          , "RT1"   ,"2025-04-20","2025-04-26","200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"201.00" ,"76.00"   ,"201.00" ,"76.00"   ,"201.00" ,"76.00"   }
                };

        String[][] seasonsDeleted =
                {
                        { ID  ,PRODUCT_ID ,PROPERTY_ID ,ROOM_CLASS_CODE , BASE_AT ,START_DATE  ,END_DATE    ,SUN_CEIL ,SUN_FLOOR ,MON_CEIL ,MON_FLOOR ,TUE_CEIL ,TUE_FLOOR ,WED_CEIL ,WED_FLOOR ,THU_CEIL ,THU_FLOOR ,FRI_CEIL ,FRI_FLOOR ,SAT_CEIL ,SAT_FLOOR , SEASON_NAME  },
                        { "1" ,"1"        ,"6"         , "STD"          , "RT1"   ,"2025-04-20","2025-04-26","200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   , "s1"         }
                };

        return SeasonMakerOperations.builder()
                .seasonsToBeCreated(getSeasonsList(seasonsCreated,false))
                .seasonsToBeSkipped(Collections.emptyList())
                .seasonsToBeUpdated(Collections.emptyList())
                .seasonsToBeDeleted(getSeasonsList(seasonsDeleted,true))
                .build();
    }
    private static SeasonMakerOperations expectedSeasons_TC_12()
    {
        String[][] seasonsCreated =
                {
                        { ID  ,PRODUCT_ID ,PROPERTY_ID ,ROOM_CLASS_CODE , BASE_AT ,START_DATE  ,END_DATE    ,SUN_CEIL ,SUN_FLOOR ,MON_CEIL ,MON_FLOOR ,TUE_CEIL ,TUE_FLOOR ,WED_CEIL ,WED_FLOOR ,THU_CEIL ,THU_FLOOR ,FRI_CEIL ,FRI_FLOOR ,SAT_CEIL ,SAT_FLOOR },
                        { "1" ,"1"        ,"6"         , "STD"          , "RT1"   ,"2025-01-20","2025-02-15", "100.00" , "50.00" , "101.00" , "51.00" , "102.00" , "52.00"  , "103.00" , "53.00" , "104.00" , "54.00" , "105.00" , "55.00" , "106.00" , "56.00"},
                        { "2" ,"1"        ,"6"         , "STD"          , "RT1"   ,"2025-02-16","2025-02-18",  "201.00" , "50.00"  , "101.00" , "51.00" , "203.00" ,"52.00"  , "null"  ,  "null"  ,  "null" ,  "null" ,   "null" ,  "null"  , "null", "null" },
                        { "3" ,"1"        ,"6"         , "STD"          , "RT1"   ,"2025-02-20","2025-02-28", "107.00" , "57.00"  , "108.00" , "58.00"  , "109.00" , "59.00"  , "110.00" , "60.00" , "111.00" , "61.00" , "112.00" , "62.00" , "201.00" , "63.00"},
                        { "4" ,"1"        ,"6"         , "STD"          , "RT1"   ,"2025-03-01","2025-03-01",  "null"  ,  "null"  , "null"  ,  "null" , "null"  ,  "null"  , "null"  ,  "null"  ,  "null" ,  "null" ,   "null" ,  "null"  , "201.00" , "70.00" },
                };

        String[][] seasonsDeleted =
                {
                        { ID  ,PRODUCT_ID ,PROPERTY_ID ,ROOM_CLASS_CODE , BASE_AT ,START_DATE  ,END_DATE    ,SUN_CEIL ,SUN_FLOOR ,MON_CEIL ,MON_FLOOR ,TUE_CEIL ,TUE_FLOOR ,WED_CEIL ,WED_FLOOR ,THU_CEIL ,THU_FLOOR ,FRI_CEIL ,FRI_FLOOR ,SAT_CEIL ,SAT_FLOOR , SEASON_NAME },
                        { "1" ,"1"        ,"6"         , "STD"          , "RT1"   ,"2025-01-20","2025-02-18", "100.00" , "50.00" , "101.00" , "51.00" , "102.00" , "52.00"  , "103.00" , "53.00" , "104.00" , "54.00" , "105.00" , "55.00" , "106.00" , "56.00"  , "s1"    },
                        { "2" ,"1"        ,"6"         , "STD"          , "RT1"   ,"2025-02-20","2025-02-28", "107.00" , "57.00"  , "108.00" , "58.00"  , "109.00" , "59.00"  , "110.00" , "60.00" , "111.00" , "61.00" , "112.00" , "62.00" , "113.00" , "63.00"  , "s2"    }
                };

        return SeasonMakerOperations.builder()
                .seasonsToBeCreated(getSeasonsList(seasonsCreated,false))
                .seasonsToBeSkipped(Collections.emptyList())
                .seasonsToBeUpdated(Collections.emptyList())
                .seasonsToBeDeleted(getSeasonsList(seasonsDeleted,true))
                .build();
    }
    private static SeasonMakerOperations expectedSeasons_TC_13()
    {
        String[][] seasonsCreated =
                {
                        { ID  ,PRODUCT_ID ,PROPERTY_ID ,ROOM_CLASS_CODE , BASE_AT ,START_DATE  ,END_DATE    ,SUN_CEIL ,SUN_FLOOR ,MON_CEIL ,MON_FLOOR ,TUE_CEIL ,TUE_FLOOR ,WED_CEIL ,WED_FLOOR ,THU_CEIL ,THU_FLOOR ,FRI_CEIL ,FRI_FLOOR ,SAT_CEIL ,SAT_FLOOR },
                        { "1" ,"1"        ,"6"         , "STD"          , "RT1"   ,"2025-01-20","2025-02-15", "100.00" , "50.00" , "101.00" , "51.00" , "102.00" , "52.00"  , "103.00" , "53.00" , "104.00" , "54.00" , "105.00" , "55.00" , "106.00" , "56.00"},
                        { "2" ,"1"        ,"6"         , "STD"          , "RT1"   ,"2025-02-16","2025-02-18",  "201.00" , "50.00"  , "101.00" , "51.00" , "203.00" ,"52.00"  , "null"  ,  "null"  ,  "null" ,  "null" ,   "null" ,  "null"  , "null", "null" },
                        { "3" ,"1"        ,"6"         , "STD"          , "RT1"   ,"2025-02-20","2025-03-01", "107.00" , "57.00"  , "108.00" , "58.00"  , "109.00" , "59.00"  , "110.00" , "60.00" , "111.00" , "61.00" , "112.00" , "62.00" , "201.00" , "63.00"},

                };

        String[][] seasonsDeleted =
                {
                        { ID  ,PRODUCT_ID ,PROPERTY_ID ,ROOM_CLASS_CODE , BASE_AT ,START_DATE  ,END_DATE    ,SUN_CEIL ,SUN_FLOOR ,MON_CEIL ,MON_FLOOR ,TUE_CEIL ,TUE_FLOOR ,WED_CEIL ,WED_FLOOR ,THU_CEIL ,THU_FLOOR ,FRI_CEIL ,FRI_FLOOR ,SAT_CEIL ,SAT_FLOOR , SEASON_NAME },
                        { "1" ,"1"        ,"6"         , "STD"          , "RT1"   ,"2025-01-20","2025-02-18", "100.00" , "50.00" , "101.00" , "51.00" , "102.00" , "52.00"  , "103.00" , "53.00" , "104.00" , "54.00" , "105.00" , "55.00" , "106.00" , "56.00"  , "s1"    },
                        { "2" ,"1"        ,"6"         , "STD"          , "RT1"   ,"2025-02-20","2025-02-28", "107.00" , "57.00"  , "108.00" , "58.00"  , "109.00" , "59.00"  , "110.00" , "60.00" , "111.00" , "61.00" , "112.00" , "62.00" , "113.00" , "63.00"  , "s2"    }
                };

        return SeasonMakerOperations.builder()
                .seasonsToBeCreated(getSeasonsList(seasonsCreated,false))
                .seasonsToBeSkipped(Collections.emptyList())
                .seasonsToBeUpdated(Collections.emptyList())
                .seasonsToBeDeleted(getSeasonsList(seasonsDeleted,true))
                .build();
    }
    private static SeasonMakerOperations expectedSeasons_TC_14()
    {
        String[][] seasonsCreated =
                {
                        { ID  ,PRODUCT_ID ,PROPERTY_ID ,ROOM_CLASS_CODE , BASE_AT ,START_DATE  ,END_DATE    ,SUN_CEIL ,SUN_FLOOR ,MON_CEIL ,MON_FLOOR ,TUE_CEIL ,TUE_FLOOR ,WED_CEIL ,WED_FLOOR ,THU_CEIL ,THU_FLOOR ,FRI_CEIL ,FRI_FLOOR ,SAT_CEIL ,SAT_FLOOR },
                        { "1" ,"1"        ,"6"         , "STD"          , "RT1"   ,"2025-02-16","2025-02-25","210.00" ,"74.00"   ,"211.00" ,"75.00"   ,"212.00" ,"76.00"   ,"213.00" ,"77.00"   ,"214.00" ,"78.00"   ,"215.00" ,"79.00"   ,"216.00" ,"80.00" },
                        { "2" ,"1"        ,"6"         , "STD"          , "RT1"   ,"2025-02-26","2025-02-27","null" ,"null"   ,"null" ,"null"   ,"null" ,"null"   ,"213.00" ,"80.00"   ,"214.00" ,"80.00"   ,"null" ,"null"   ,"null" ,"null"}
                };

        String[][] seasonsDeleted =
                {
                        { ID  ,PRODUCT_ID ,PROPERTY_ID ,ROOM_CLASS_CODE , BASE_AT ,START_DATE  ,END_DATE    ,SUN_CEIL ,SUN_FLOOR ,MON_CEIL ,MON_FLOOR ,TUE_CEIL ,TUE_FLOOR ,WED_CEIL ,WED_FLOOR ,THU_CEIL ,THU_FLOOR ,FRI_CEIL ,FRI_FLOOR ,SAT_CEIL ,SAT_FLOOR , SEASON_NAME },
                        { "1" ,"1"        ,"6"         , "STD"          , "RT1"   ,"2025-02-17","2025-02-20","null" ,"null"   ,"201.00" ,"75.00"   ,"202.00" ,"76.00"   ,"203.00" ,"77.00"   ,"204.00" ,"78.00"   ,"null" ,"null"   ,"null" ,"null"   , "s1"  },
                        { "2" ,"1"        ,"6"         , "STD"          , "RT1"   ,"2025-02-23","2025-02-25","null" ,"null"   ,"201.00" ,"75.00"   ,"202.00" ,"76.00"   ,"203.00" ,"77.00"   ,"204.00" ,"78.00"   ,"null" ,"null"   ,"null" ,"null"   , "s2"  }
                };

        return SeasonMakerOperations.builder()
                .seasonsToBeCreated(getSeasonsList(seasonsCreated,false))
                .seasonsToBeSkipped(Collections.emptyList())
                .seasonsToBeUpdated(Collections.emptyList())
                .seasonsToBeDeleted(getSeasonsList(seasonsDeleted,true))
                .build();
    }
    private static SeasonMakerOperations expectedSeasons_TC_15()
    {
        String[][] seasonsCreated =
                {
                        { ID  ,PRODUCT_ID ,PROPERTY_ID ,ROOM_CLASS_CODE , BASE_AT ,START_DATE  ,END_DATE    ,SUN_CEIL ,SUN_FLOOR ,MON_CEIL ,MON_FLOOR ,TUE_CEIL ,TUE_FLOOR ,WED_CEIL ,WED_FLOOR ,THU_CEIL ,THU_FLOOR ,FRI_CEIL ,FRI_FLOOR ,SAT_CEIL ,SAT_FLOOR },
                        { "1" ,"1"        ,"6"         , "STD"          , "RT1"   ,"2025-02-16","2025-02-22","210.00" ,"70.00"   ,"211.00" ,"70.00"   ,"212.00" ,"70.00"   ,"213.00" ,"70.00"   ,"214.00" ,"70.00"   ,"215.00" ,"70.00"   ,"216.00" ,"70.00" },
                        { "2" ,"1"        ,"6"         , "STD"          , "RT1"   ,"2025-02-23","2025-02-25","217.00" ,"70.00"   ,"218.00" ,"70.00"   ,"219.00" ,"70.00"   ,"null" ,"null"   ,"null" ,"null"  ,"null" ,"null"   ,"null" ,"null"}
                };

        String[][] seasonsSkipped =
                {
                        { ID  ,PRODUCT_ID ,PROPERTY_ID ,ROOM_CLASS_CODE , BASE_AT ,START_DATE  ,END_DATE    ,SUN_CEIL ,SUN_FLOOR ,MON_CEIL ,MON_FLOOR ,TUE_CEIL ,TUE_FLOOR ,WED_CEIL ,WED_FLOOR ,THU_CEIL ,THU_FLOOR ,FRI_CEIL ,FRI_FLOOR ,SAT_CEIL ,SAT_FLOOR , SEASON_NAME },
                        { "1" ,"1"        ,"6"         , "STD"          , "RT1"   ,"2025-01-20","2025-02-15", "100.00" , "50.00" , "101.00" , "51.00" , "102.00" , "52.00"  , "103.00" , "53.00" , "104.00" , "54.00" , "105.00" , "55.00" , "106.00" , "56.00"  , "s1"    },
                        { "2" ,"1"        ,"6"         , "STD"          , "RT1"   ,"2025-02-26","2025-03-02", "107.00" , "57.00"  , "108.00" , "58.00"  , "109.00" , "59.00"  , "110.00" , "60.00" , "111.00" , "61.00" , "112.00" , "62.00" , "113.00" , "63.00"  , "s2"    }
                };

        return SeasonMakerOperations.builder()
                .seasonsToBeCreated(getSeasonsList(seasonsCreated,false))
                .seasonsToBeSkipped(getSeasonsList(seasonsSkipped,true))
                .seasonsToBeUpdated(Collections.emptyList())
                .seasonsToBeDeleted(Collections.emptyList())
                .build();
    }
    private static SeasonMakerOperations expectedSeasons_TC_16()
    {
        String[][] seasonsCreated =
                {
                        { ID  ,PRODUCT_ID ,PROPERTY_ID ,ROOM_CLASS_CODE , BASE_AT ,START_DATE  ,END_DATE    ,SUN_CEIL ,SUN_FLOOR ,MON_CEIL ,MON_FLOOR ,TUE_CEIL ,TUE_FLOOR ,WED_CEIL ,WED_FLOOR ,THU_CEIL ,THU_FLOOR ,FRI_CEIL ,FRI_FLOOR ,SAT_CEIL ,SAT_FLOOR , SEASON_NAME },

                        { "1" ,"1"        ,"6"         , "STD"          , "RT1"   ,"2025-02-25","2025-03-03","211.00" ,"70.00"   ,"200.00" ,"75.00"   ,"210.00" ,"70.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   , "s1"  },
                        { "2" ,"1"        ,"6"         , "STD"          , "RT1"   ,"2025-03-04","2025-03-10","200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   , "s2"  },
                        { "3" ,"1"        ,"6"         , "STD"          , "RT1"   ,"2025-03-11","2025-03-17","200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"212.00" ,"70.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   , "s3"  },
                        { "4" ,"1"        ,"6"         , "STD"          , "RT1"   ,"2025-03-18","2025-03-18","null"   , "null"   ,"null"   ,"null"    ,"200.00" ,"75.00"   ,"null"   ,"null"    ,"null"   , "null"   ,"null"   ,"null"    ,"null"   ,"null"    , "s4"  },

                };

        String[][] seasonsDeleted =
                {
                        { ID  ,PRODUCT_ID ,PROPERTY_ID ,ROOM_CLASS_CODE , BASE_AT ,START_DATE  ,END_DATE    ,SUN_CEIL ,SUN_FLOOR ,MON_CEIL ,MON_FLOOR ,TUE_CEIL ,TUE_FLOOR ,WED_CEIL ,WED_FLOOR ,THU_CEIL ,THU_FLOOR ,FRI_CEIL ,FRI_FLOOR ,SAT_CEIL ,SAT_FLOOR , SEASON_NAME },

                        { "1" ,"1"        ,"6"         , "STD"          , "RT1"   ,"2025-02-25","2025-03-01","200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   , "s1"  },
                        { "2" ,"1"        ,"6"         , "STD"          , "RT1"   ,"2025-03-02","2025-03-10","200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   , "s2"  },
                        { "3" ,"1"        ,"6"         , "STD"          , "RT1"   ,"2025-03-11","2025-03-18","200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   , "s3"  },
                };

        return SeasonMakerOperations.builder()
                .seasonsToBeCreated(getSeasonsList(seasonsCreated,false))
                .seasonsToBeUpdated(Collections.emptyList())
                .seasonsToBeSkipped(Collections.emptyList())
                .seasonsToBeDeleted(getSeasonsList(seasonsDeleted,true))
                .build();
    }

    private static SeasonMakerOperations expectedSeasons_TC_17()
    {
        String[][] seasonsCreated =
                {
                        { ID  ,PRODUCT_ID ,PROPERTY_ID ,ROOM_CLASS_CODE , BASE_AT ,START_DATE  ,END_DATE    ,SUN_CEIL ,SUN_FLOOR ,MON_CEIL ,MON_FLOOR ,TUE_CEIL ,TUE_FLOOR ,WED_CEIL ,WED_FLOOR ,THU_CEIL ,THU_FLOOR ,FRI_CEIL ,FRI_FLOOR ,SAT_CEIL ,SAT_FLOOR , SEASON_NAME },
                        { "1" ,"1"        ,"6"         , "STD"          , "RT1"   ,"2025-02-25","2025-03-07","200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"210.00" ,"70.00"   , "s1"  },
                        { "2" ,"1"        ,"6"         , "STD"          , "RT1"   ,"2025-03-08","2025-03-16","200.00" ,"75.00"   ,"211.00" ,"70.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   , "s2"  },
                        { "3" ,"1"        ,"6"         , "STD"          , "RT1"   ,"2025-03-17","2025-03-18","null"   ,"null"    ,"200.00" ,"75.00"   ,"212.00" ,"70.00"   ,"null"   ,"null"    ,"null"   ,"null"    ,"null"   ,"null"    ,"null"   ,"null"    , "s3"  },

                };

        String[][] seasonsDeleted =
                {
                        { ID  ,PRODUCT_ID ,PROPERTY_ID ,ROOM_CLASS_CODE , BASE_AT ,START_DATE  ,END_DATE    ,SUN_CEIL ,SUN_FLOOR ,MON_CEIL ,MON_FLOOR ,TUE_CEIL ,TUE_FLOOR ,WED_CEIL ,WED_FLOOR ,THU_CEIL ,THU_FLOOR ,FRI_CEIL ,FRI_FLOOR ,SAT_CEIL ,SAT_FLOOR , SEASON_NAME },
                        { "1" ,"1"        ,"6"         , "STD"          , "RT1"   ,"2025-02-25","2025-03-01","200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   , "s1"  },
                        { "2" ,"1"        ,"6"         , "STD"          , "RT1"   ,"2025-03-02","2025-03-10","200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   , "s2"  },
                        { "3" ,"1"        ,"6"         , "STD"          , "RT1"   ,"2025-03-11","2025-03-18","200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   , "s3"  },
                };

        return SeasonMakerOperations.builder()
                .seasonsToBeCreated(getSeasonsList(seasonsCreated,false))
                .seasonsToBeUpdated(Collections.emptyList())
                .seasonsToBeSkipped(Collections.emptyList())
                .seasonsToBeDeleted(getSeasonsList(seasonsDeleted,true))
                .build();
    }

    private static SeasonMakerOperations expectedSeasons_TC_18()
    {
        String[][] seasonsCreated =
                {
                        { ID  ,PRODUCT_ID ,PROPERTY_ID ,ROOM_CLASS_CODE , BASE_AT ,START_DATE  ,END_DATE    ,SUN_CEIL ,SUN_FLOOR ,MON_CEIL ,MON_FLOOR ,TUE_CEIL ,TUE_FLOOR ,WED_CEIL ,WED_FLOOR ,THU_CEIL ,THU_FLOOR ,FRI_CEIL ,FRI_FLOOR ,SAT_CEIL ,SAT_FLOOR , SEASON_NAME },
                        { "1" ,"1"        ,"6"         , "STD"          , "RT1"   ,"2025-03-03","2025-03-08","null"  ,"null"    ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"210.00" ,"70.00"   ,"211.00" ,"70.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   , "s1"  },
                };

        String[][] seasonsDeleted =
                {
                        { ID  ,PRODUCT_ID ,PROPERTY_ID ,ROOM_CLASS_CODE , BASE_AT ,START_DATE  ,END_DATE    ,SUN_CEIL ,SUN_FLOOR ,MON_CEIL ,MON_FLOOR ,TUE_CEIL ,TUE_FLOOR ,WED_CEIL ,WED_FLOOR ,THU_CEIL ,THU_FLOOR ,FRI_CEIL ,FRI_FLOOR ,SAT_CEIL ,SAT_FLOOR , SEASON_NAME },
                        { "1" ,"1"        ,"6"         , "STD"          , "RT1"   ,"2025-03-03","2025-03-05","null"   ,"null"    ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00"   ,"75"    ,"null"   ,"null"    ,"null"   ,"null"    ,"null"   ,"null"     , "s1"  },
                        { "2" ,"1"        ,"6"         , "STD"          , "RT1"   ,"2025-03-06","2025-03-08","null"   ,"null"    ,"null"   ,"null"    ,"null"   ,"null"    ,"null"   ,"null"    ,"200.00" ,"75.00"   ,"200.00" ,"75.00"   ,"200.00" ,"75.00"    , "s2"  },
                };

        String[][] seasonsSkipped =
                {
                        { ID  ,PRODUCT_ID ,PROPERTY_ID ,ROOM_CLASS_CODE , BASE_AT ,START_DATE  ,END_DATE    ,SUN_CEIL ,SUN_FLOOR ,MON_CEIL ,MON_FLOOR ,TUE_CEIL ,TUE_FLOOR ,WED_CEIL ,WED_FLOOR ,THU_CEIL ,THU_FLOOR ,FRI_CEIL ,FRI_FLOOR ,SAT_CEIL ,SAT_FLOOR , SEASON_NAME },
                        { "3" ,"1"        ,"6"         , "STD"          , "RT1"   ,"2025-03-09","2025-03-09","200.00" ,"75.00"   ,"null"   ,"null"    ,"null"   ,"null"    ,"null"   ,"null"    ,"null"   ,"null"    ,"null"   ,"null"    ,"null"   ,"null"     , "s3"  },

                };

        return SeasonMakerOperations.builder()
                .seasonsToBeCreated(getSeasonsList(seasonsCreated,false))
                .seasonsToBeUpdated(Collections.emptyList())
                .seasonsToBeSkipped(getSeasonsList(seasonsSkipped,true))
                .seasonsToBeDeleted(getSeasonsList(seasonsDeleted,true))
                .build();
    }
    private static SeasonMakerOperations expectedSeasons_TC_19()
    {
        String[][] seasonsCreated =
                {
                        { ID  ,PRODUCT_ID ,PROPERTY_ID ,ROOM_CLASS_CODE , BASE_AT ,START_DATE  ,END_DATE    ,SUN_CEIL ,SUN_FLOOR ,MON_CEIL ,MON_FLOOR ,TUE_CEIL ,TUE_FLOOR ,WED_CEIL ,WED_FLOOR ,THU_CEIL ,THU_FLOOR ,FRI_CEIL ,FRI_FLOOR ,SAT_CEIL ,SAT_FLOOR },
                        { "1" ,"1"        ,"6"         , "STD"          , "RT1"   ,"2025-01-28","2025-01-28","null" ,"null"   ,"null" ,"null"   ,"210.00" ,"70.00"   ,"null" ,"null"   ,"null" ,"null"   ,"null" ,"null"   ,"null" ,"null" },
                        { "2" ,"1"        ,"6"         , "STD"          , "RT1"   ,"2025-02-01","2025-02-04","215.00" ,"75.00"   ,"101.00" ,"71.00"   ,"217.00" ,"70.00"    ,"null" ,"null"   ,"null" ,"null"   ,"null" ,"null"   ,"106.00" ,"76.00" },
                        { "3" ,"1"        ,"6"         , "STD"          , "RT1"   ,"2025-02-06","2025-02-12","111.00" ,"81.00"   ,"223.00" ,"82.00"   ,"224.00" ,"83.00"   ,"114.00" ,"84.00"  ,"219.00" ,"70.00"   ,"116.00" ,"86.00"   ,"117.00" ,"87.00" },
                        { "4" ,"1"        ,"6"         , "STD"          , "RT1"   ,"2025-02-13","2025-02-13","null" ,"null"   ,"null" ,"null"   ,"null" ,"null"   ,"null" ,"null"   ,"226.00" ,"85.00"   ,"null" ,"null"  ,"null" ,"null" },
                        { "5" ,"1"        ,"6"         , "STD"          , "RT1"   ,"2025-02-15","2025-02-21","229.00" ,"70.00"   ,"122.00" ,"92.00"   ,"123.00" ,"93.00"   ,"124.00" ,"94.00"   ,"233.00" ,"95.00"  ,"126.00" ,"96.00"   ,"228.00" ,"70.00" },
                        { "6" ,"1"        ,"6"         , "STD"          , "RT1"   ,"2025-02-22","2025-02-26","121.00" ,"91.00"   ,"122.00" ,"92.00"   ,"123.00" ,"93.00"   ,"124.00" ,"94.00"   ,"null" ,"null"  ,"null" ,"null"    ,"127.00" ,"97.00"}
                };

        String[][] seasonsDeleted =
                {
                        { ID  ,PRODUCT_ID ,PROPERTY_ID ,ROOM_CLASS_CODE , BASE_AT ,START_DATE  ,END_DATE    ,SUN_CEIL ,SUN_FLOOR ,MON_CEIL ,MON_FLOOR ,TUE_CEIL ,TUE_FLOOR ,WED_CEIL ,WED_FLOOR ,THU_CEIL ,THU_FLOOR ,FRI_CEIL ,FRI_FLOOR ,SAT_CEIL ,SAT_FLOOR ,SEASON_NAME  },
                        { "1" ,"1"        ,"6"         , "STD"          , "RT1"   ,"2025-02-01","2025-02-03", "100.00" , "70.00" , "101.00" , "71.00" , "null" , "null"  , "null" , "null", "null" , "null" ,"null" , "null", "106.00" , "76.00" , "s1"  },
                        { "2" ,"1"        ,"6"         , "STD"          , "RT1"   ,"2025-02-07","2025-02-13", "111.00" , "81.00"  , "112.00" , "82.00"  , "113.00" , "83.00"  , "114.00" , "84.00" , "115.00" , "85.00" , "116.00" , "86.00" , "117.00" , "87.00", "s2" },
                        { "3" ,"1"        ,"6"         , "STD"          , "RT1"   ,"2025-02-17","2025-02-26", "121.00" , "91.00"  , "122.00" , "92.00"  , "123.00" , "93.00"  , "124.00" , "94.00" , "125.00" , "95.00" , "126.00" , "96.00" , "127.00" , "97.00" , "s3" }
                };

        return SeasonMakerOperations.builder()
                .seasonsToBeCreated(getSeasonsList(seasonsCreated,false))
                .seasonsToBeSkipped(Collections.emptyList())
                .seasonsToBeUpdated(Collections.emptyList())
                .seasonsToBeDeleted(getSeasonsList(seasonsDeleted,true))
                .build();
    }
    private static SeasonMakerOperations expectedSeasons_TC_20()
    {
        String[][] seasonsCreated =
                {
                        { ID  ,PRODUCT_ID ,PROPERTY_ID ,ROOM_CLASS_CODE , BASE_AT ,START_DATE  ,END_DATE    ,SUN_CEIL ,SUN_FLOOR ,MON_CEIL ,MON_FLOOR ,TUE_CEIL ,TUE_FLOOR ,WED_CEIL ,WED_FLOOR ,THU_CEIL ,THU_FLOOR ,FRI_CEIL ,FRI_FLOOR ,SAT_CEIL ,SAT_FLOOR },
                        { "1" ,"1"        ,"6"         , "STD"          , "RT1"   ,"2025-01-01","2025-01-01","null" ,"null"   ,"null" ,"null"   ,"null" ,"null"   ,"210.00" ,"70.00"   ,"null" ,"null"   ,"null" ,"null"   ,"null" ,"null"   },
                        { "2" ,"1"        ,"6"         , "STD"          , "RT1"   ,"2025-01-03","2025-01-03","null" ,"null"   ,"null" ,"null"   ,"null" ,"null"   ,"null" ,"null"   ,"null" ,"null"   ,"215.00" ,"75.00"   ,"null" ,"null"   },
                        { "3" ,"1"        ,"6"         , "STD"          , "RT1"   ,"2025-01-05","2025-01-05","217.00" ,"70.00"  ,"null" ,"null"   ,"null" ,"null"   ,"null" ,"null"   ,"null" ,"null"   ,"null" ,"null"   ,"null" ,"null"  },
                        { "4" ,"1"        ,"6"         , "STD"          , "RT1"   ,"2025-01-07","2025-01-07","null" ,"null"   ,"null" ,"null"   ,"219.00" ,"70.00"   ,"null" ,"null"   ,"null" ,"null"   ,"null" ,"null"   ,"null" ,"null"   }
                };
        return SeasonMakerOperations.builder()
                .seasonsToBeCreated(getSeasonsList(seasonsCreated,false))
                .seasonsToBeSkipped(Collections.emptyList())
                .seasonsToBeUpdated(Collections.emptyList())
                .seasonsToBeDeleted(Collections.emptyList())
                .build();
    }
    private static SeasonMakerOperations expectedSeasons_TC_21()
    {
        String[][] seasonsCreated =
                {
                        { ID  ,PRODUCT_ID ,PROPERTY_ID ,ROOM_CLASS_CODE , BASE_AT ,START_DATE  ,END_DATE    ,SUN_CEIL ,SUN_FLOOR ,MON_CEIL ,MON_FLOOR ,TUE_CEIL ,TUE_FLOOR ,WED_CEIL ,WED_FLOOR ,THU_CEIL ,THU_FLOOR ,FRI_CEIL ,FRI_FLOOR ,SAT_CEIL ,SAT_FLOOR },
                        { "2" ,"1"        ,"6"         , "STD"          , "RT1"   ,"2025-01-01","2025-01-08", "111.00" , "81.00"  , "112.00" , "82.00"  , "113.00" , "83.00"  , "114.00" , "84.00" , "210.00" , "70.00" , "116.00" , "86.00" , "117.00" , "87.00"    },
                        { "3" ,"1"        ,"6"         , "STD"          , "RT1"   ,"2025-01-09","2025-01-15", "111.00" , "81.00"  , "112.00" , "82.00"  , "113.00" , "83.00"  , "114.00" , "84.00" , "115.00" , "85.00" , "116.00" , "86.00" , "117.00" , "87.00"    }
                };

        String[][] seasonsUpdated =
                {
                        { ID  ,PRODUCT_ID ,PROPERTY_ID ,ROOM_CLASS_CODE , BASE_AT ,START_DATE  ,END_DATE    ,SUN_CEIL ,SUN_FLOOR ,MON_CEIL ,MON_FLOOR ,TUE_CEIL ,TUE_FLOOR ,WED_CEIL ,WED_FLOOR ,THU_CEIL ,THU_FLOOR ,FRI_CEIL ,FRI_FLOOR ,SAT_CEIL ,SAT_FLOOR , SEASON_NAME },
                        { "1" ,"1"        ,"6"         , "STD"          , "RT1"   ,"2024-12-15","2024-12-31", "111.00" , "81.00"  , "112.00" , "82.00"  , "113.00" , "83.00"  , "114.00" , "84.00" , "115.00" , "85.00" , "116.00" , "86.00" , "117.00" , "87.00"  , "CentralRMS 15-Dec-2024"    }
                };

        return SeasonMakerOperations.builder()
                .seasonsToBeCreated(getSeasonsList(seasonsCreated,false))
                .seasonsToBeSkipped(Collections.emptyList())
                .seasonsToBeUpdated(getSeasonsList(seasonsUpdated,true))
                .seasonsToBeDeleted(Collections.emptyList())
                .build();
    }
    private static List<TransientPricingBaseAccomType> getSeasonsList(String[][] seasonsData,boolean setIDflag)
    {
        org.joda.time.format.DateTimeFormatter format = DateTimeFormat.forPattern("yyyy-MM-dd");
        DateTime parsedDate = null;
        String formattedDate = null;

        List<TransientPricingBaseAccomType> expectedSeasonsList = new ArrayList<>();
        for(int i=1;i<seasonsData.length;i++)
        {
            TransientPricingBaseAccomType seasonEntry = new TransientPricingBaseAccomType();
            if ((setIDflag == true)) {
                seasonEntry.setId(Integer.parseInt(seasonsData[i][0]));
                seasonEntry.setSeasonName(seasonsData[i][21]);
            } else {
                seasonEntry.setId(null);
                parsedDate = DateTime.parse(seasonsData[i][5]);
                formattedDate = parsedDate.toString("dd-MMM-yyyy");
                seasonEntry.setSeasonName("CentralRMS "+formattedDate);
            }
            seasonEntry.setProductID(Integer.parseInt(seasonsData[1][1]));
            seasonEntry.setPropertyId(Integer.parseInt(seasonsData[1][2]));
            seasonEntry.setStartDate(new LocalDate().parse(seasonsData[i][5],format));
            seasonEntry.setEndDate(new LocalDate().parse(seasonsData[i][6],format));
            if(!seasonsData[i][7].equals("null") ) {
                seasonEntry.setCeilingAndFloorTaxValuesByDow(DayOfWeek.SUNDAY, BigDecimal.valueOf(Double.valueOf(seasonsData[i][7])), BigDecimal.valueOf(Double.valueOf(seasonsData[i][8])));
            }
            if(!seasonsData[i][9].equals("null")) {
                seasonEntry.setCeilingAndFloorTaxValuesByDow(DayOfWeek.MONDAY, BigDecimal.valueOf(Double.valueOf(seasonsData[i][9])), BigDecimal.valueOf(Double.valueOf(seasonsData[i][10])));
            }
            if(!seasonsData[i][11].equals("null")) {
                seasonEntry.setCeilingAndFloorTaxValuesByDow(DayOfWeek.TUESDAY, BigDecimal.valueOf(Double.valueOf(seasonsData[i][11])), BigDecimal.valueOf(Double.valueOf(seasonsData[i][12])));
            }
            if(!seasonsData[i][13].equals("null")) {
                seasonEntry.setCeilingAndFloorTaxValuesByDow(DayOfWeek.WEDNESDAY, BigDecimal.valueOf(Double.valueOf(seasonsData[i][13])), BigDecimal.valueOf(Double.valueOf(seasonsData[i][14])));
            }
            if(!seasonsData[i][15].equals("null")) {
                seasonEntry.setCeilingAndFloorTaxValuesByDow(DayOfWeek.THURSDAY, BigDecimal.valueOf(Double.valueOf(seasonsData[i][15])), BigDecimal.valueOf(Double.valueOf(seasonsData[i][16])));
            }
            if(!seasonsData[i][17].equals("null")) {
                seasonEntry.setCeilingAndFloorTaxValuesByDow(DayOfWeek.FRIDAY, BigDecimal.valueOf(Double.valueOf(seasonsData[i][17])), BigDecimal.valueOf(Double.valueOf(seasonsData[i][18])));
            }
            if(!seasonsData[i][19].equals("null")) {
                seasonEntry.setCeilingAndFloorTaxValuesByDow(DayOfWeek.SATURDAY, BigDecimal.valueOf(Double.valueOf(seasonsData[i][19])), BigDecimal.valueOf(Double.valueOf(seasonsData[i][20])));
            }
            seasonEntry.setStatus(Status.ACTIVE);
            seasonEntry.setAccomType(getBaseAccomType(seasonsData[i][3],seasonsData[i][4]));
            expectedSeasonsList.add(seasonEntry);
        }
        return  expectedSeasonsList;
    }


}