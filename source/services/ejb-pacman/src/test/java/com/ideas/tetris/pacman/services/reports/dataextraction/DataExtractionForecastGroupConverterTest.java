package com.ideas.tetris.pacman.services.reports.dataextraction;

import com.ideas.tetris.pacman.services.reports.dataextraction.entity.DataExtractionForecastGroup;
import com.ideas.tetris.pacman.services.reports.dataextraction.entity.DataExtractionReportDto;
import com.ideas.tetris.pacman.services.reports.dataextraction.entity.DataExtractionType;
import com.ideas.tetris.pacman.services.scheduledreport.domain.ReportSheet;
import com.ideas.tetris.pacman.services.scheduledreport.entity.Language;
import com.ideas.tetris.pacman.services.scheduledreport.entity.ScheduledReport;
import com.ideas.tetris.pacman.services.scheduledreport.entity.criteria.DataExtractionReportCriteria;
import org.joda.time.LocalDate;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertArrayEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

public class DataExtractionForecastGroupConverterTest {

    private static final String DATA_EXTRACTION_FORECAST_GROUP_PARAMS = "Comp3=-1##Competitor=false##IsDep_Hotel=false##IsNS_MS=false##Comp4=-1##" +
            "IsDep_BV=false##Comp5=-1##Comp1=-1##Comp2=-1##IsRev_BV=false##IsRNAO_Hotel=false##IsRev_RT=false##IsSplEvt_Hotel=false##" +
            "IsBAR_RT=false##IsDep_RC=false##IsOOO_RC=false##IsArr_MS=false##IsNS_FG=true##IsCap_RC=false##IsRT=false##param_isRollingDate=1##" +
            "IsDep_RT=false##IsOOO_RT=false##IsBAR_Hotel=false##IsBAR_RC=false##isLOS=false##IsCap_RT=false##IsBV=false##JNDI_NAME=000005##" +
            "IsArr_FG=true##param_IsShowLastYearDataChecked=true##IsRS_STLY_FG=true##IsOcFcst_RC=false##IsRev_STLY_MS=false##IsADR_FG=true##" +
            "param_User_ID=11403##IsADR_MS=false##IsMS=false##isBARByDay_Hotel=true##IsNS_BV=false##IsCncel_FG=true##IsRS_STLY_MS=false##" +
            "IsLRV_Hotel=false##IsOcFcst_BV=false##IsRev_RC=false##IsUserUnConDmd_RC=false##IsRS_STLY_Hotel=false##IsUserProjected_FG=false##" +
            "IsRS_RC=false##IsCncel_MS=false##EndDate=1970010100000##IsOvbk_RT=false##param_IsWashAtHotelLevel=false##IsSysUnDmd_FG=true##" +
            "IsRS_RT=false##IsUserProjected_MS=false##IsRev_STLY_FG=true##IsHotel=false##IsRS_BV=false##IsUserUnConDmdTotal_Hotel=false##" +
            "StartDate=1970010100000##IsRNAO_RT=false##param_SheetForCriteria=false##IsRev_FG=true##IsRev_Hotel=false##IsOOO_Hotel=false##" +
            "IsRNAO_RC=false##IsOvbk_Hotel=false##userLocale=en_US##IsArr_Hotel=false##IsNS_RT=false##IsSysUnConDmd_RC=false##IsFG=true##" +
            "param_Property_ID=5##IsRS_STLY_BV=false##IsDep_MS=false##IsArr_RC=false##IsADR_Hotel=false##IsDep_FG=true##IsNS_RC=false##" +
            "IsADR_BV=false##param_Rolling_End_Date=TODAY+80##IsRS_STLY_RT=false##maxLOS=8##IsCncel_BV=false##IsArr_RT=false##" +
            "IsRev_STLY_RC=false##IsCap_Hotel=false##IsADR_RT=false##IsRevPar_RC=false##IsCncel_RT=false##IsUserProjected_Hotel=false##" +
            "IsRS_STLY_RC=false##IsSysUnConDmdTtl_Hotel=false##IsNS_Hotel=false##IsRS_MS=false##IsArr_BV=false##isRestrictHighestBarEnabled=false##" +
            "IsADR_RC=false##IsRC=false##param_Rolling_Start_Date=TODAY##IsRS_Hotel=false##isContinuousPricingEnabled=false##" +
            "IsRev_STLY_Hotel=false##IsOcFcst_MS=false##IsRevPar_RT=false##IS_IGNORE_PAGINATION=true##IsSysGrpWashPer_FG=true##IsCncel_Hotel=false##" +
            "IsRevPar_Hotel=false##IsCncel_RC=false##IsRev_STLY_BV=false##IsOcFcst_FG=true##IsLRV_RC=false##IsRev_STLY_RT=false##IsRev_MS=false##" +
            "IsOcFcst_Hotel=false##IsRS_FG=true##IsUserUnDmd_FG=true##";


    @Test
    public void testForecastGroupReportSheetAllData() throws Exception {
        Map<String, String> reportParamsMap = DataExtractionReportUtil.parseReportParams(DATA_EXTRACTION_FORECAST_GROUP_PARAMS);
        DataExtractionReportCriteria reportCriteria = DataExtractionReportUtil.populateReportCriteria(reportParamsMap);
        ScheduledReport<DataExtractionReportCriteria> scheduledReport = new ScheduledReport<>();
        scheduledReport.setReportCriteria(reportCriteria);
        scheduledReport.setLanguage(Language.ENGLISH);
        ReportSheet actualForecastGroupSheet = DataExtractionForecastGroupConverter.getForecastGroupReportSheet(getDataExtractionForecastGroupDTO(), scheduledReport);
        assertNotNull(actualForecastGroupSheet);

        Object[] expectedForecastGroupRowData = new Object[]{"Hilton - Pune", "Monday", "03-Oct-2016", "05-Oct-2015", "Complementary", "15", "18", "18",
                "11", "13", "8", "10", "0", "0", "0", "0", "720.0", "900.0", "900.0", "915.0", "900.0", "25.0", "18.0", "0.0", "18.0", "0.0", "18.0", "6.0", "0.0", "6.0",
                "0.0", "48.0", "50.0", "36.6", "50.0"};
        Object[] actualForecastGroupRowdata = actualForecastGroupSheet.getRow(0);
        assertArrayEquals(actualForecastGroupRowdata, expectedForecastGroupRowData);

        Object[] expectedHeaderRow = new Object[]{"Property Name", "Day of Week", "Occupancy Date", "Comparison Date Last Year", "Forecast Group",
                "Occupancy On Books This Year", "Occupancy On Books Last Year Actual", "Occupancy On Books STLY", "Arrivals This Year",
                "Arrivals Last Year Actual", "Departures This Year", "Departures Last Year Actual", "Cancelled This Year", "Cancelled Last Year Actual",
                "No Show This Year", "No Show Last Year Actual", "Booked Room Revenue This Year", "Booked Room Revenue Last Year Actual",
                "Booked Room Revenue STLY", "Forecasted Room Revenue This Year", "Forecasted Room Revenue Last Year Actual",
                "Occupancy Forecast This Year", "Occupancy Forecast Last Year Actual", "System Unconstrained Demand This Year",
                "System Unconstrained Demand Last Year Actual", "User Demand This Year", "User Demand Last Year Actual", "System Wash % This Year",
                "System Wash % Last Year Actual", "User Wash % This Year", "User Wash % Last Year Actual", "ADR On Books This Year",
                "ADR On Books Last Year Actual", "ADR Forecast This Year", "ADR Forecast Last Year Actual"};
        Object[] actualHeaderRow = actualForecastGroupSheet.getHeaderRow(0);
        assertArrayEquals(actualHeaderRow, expectedHeaderRow);
    }

    @Test
    public void testForecastGroupReportSheetThisYearData() throws Exception {
        Map<String, String> reportParamsMap = DataExtractionReportUtil.parseReportParams(DATA_EXTRACTION_FORECAST_GROUP_PARAMS);
        DataExtractionReportCriteria reportCriteria = DataExtractionReportUtil.populateReportCriteria(reportParamsMap);
        reportCriteria.setShowLastYearData(false);
        ScheduledReport<DataExtractionReportCriteria> scheduledReport = new ScheduledReport<>();
        scheduledReport.setReportCriteria(reportCriteria);
        scheduledReport.setLanguage(Language.ENGLISH);
        ReportSheet actualForecastGroupSheet = DataExtractionForecastGroupConverter.getForecastGroupReportSheet(getDataExtractionForecastGroupDTO(), scheduledReport);
        assertNotNull(actualForecastGroupSheet);

        Object[] expectedForecastGroupRowData = new Object[]{"Hilton - Pune", "Monday", "03-Oct-2016", "Complementary", "15", "18", "11", "8", "0", "0",
                "720.0", "900.0", "915.0", "25.0", "0.0", "0.0", "6.0", "6.0", "48.0", "36.6"};
        Object[] actualForecastGroupRowdata = actualForecastGroupSheet.getRow(0);
        assertArrayEquals(actualForecastGroupRowdata, expectedForecastGroupRowData);

        Object[] expectedHeaderRow = new Object[]{"Property Name", "Day of Week", "Occupancy Date", "Forecast Group", "Occupancy On Books This Year",
                "Occupancy On Books STLY", "Arrivals This Year", "Departures This Year", "Cancelled This Year", "No Show This Year",
                "Booked Room Revenue This Year", "Booked Room Revenue STLY", "Forecasted Room Revenue This Year", "Occupancy Forecast This Year",
                "System Unconstrained Demand This Year", "User Demand This Year", "System Wash % This Year", "User Wash % This Year",
                "ADR On Books This Year", "ADR Forecast This Year"};
        Object[] actualHeaderRow = actualForecastGroupSheet.getHeaderRow(0);
        assertArrayEquals(actualHeaderRow, expectedHeaderRow);
    }

    @Test
    public void testForecastGroupReportSheetThisYearDataCondition1() throws Exception {
        Map<String, String> reportParamsMap = DataExtractionReportUtil.parseReportParams(DATA_EXTRACTION_FORECAST_GROUP_PARAMS);
        DataExtractionReportCriteria reportCriteria = DataExtractionReportUtil.populateReportCriteria(reportParamsMap);
        reportCriteria.setShowLastYearData(false);
        reportCriteria.setForecastGroupArrivals(false);
        reportCriteria.setForecastGroupDepartures(false);
        reportCriteria.setForecastGroupRoomsSold(false);
        reportCriteria.setForecastGroupRoomsSoldSTLY(false);
        ScheduledReport<DataExtractionReportCriteria> scheduledReport = new ScheduledReport<>();
        scheduledReport.setReportCriteria(reportCriteria);
        scheduledReport.setLanguage(Language.ENGLISH);
        ReportSheet actualForecastGroupSheet = DataExtractionForecastGroupConverter.getForecastGroupReportSheet(getDataExtractionForecastGroupDTO(), scheduledReport);
        assertNotNull(actualForecastGroupSheet);

        Object[] expectedForecastGroupRowData = new Object[]{"Hilton - Pune", "Monday", "03-Oct-2016", "Complementary", "0", "0", "720.0", "900.0", "915.0",
                "25.0", "0.0", "0.0", "6.0", "6.0", "48.0", "36.6"};
        Object[] actualForecastGroupRowdata = actualForecastGroupSheet.getRow(0);
        assertArrayEquals(actualForecastGroupRowdata, expectedForecastGroupRowData);

        Object[] expectedHeaderRow = new Object[]{"Property Name", "Day of Week", "Occupancy Date", "Forecast Group", "Cancelled This Year", "No Show This Year",
                "Booked Room Revenue This Year", "Booked Room Revenue STLY", "Forecasted Room Revenue This Year", "Occupancy Forecast This Year",
                "System Unconstrained Demand This Year", "User Demand This Year", "System Wash % This Year",
                "User Wash % This Year", "ADR On Books This Year", "ADR Forecast This Year"};
        Object[] actualHeaderRow = actualForecastGroupSheet.getHeaderRow(0);
        assertArrayEquals(actualHeaderRow, expectedHeaderRow);
    }

    @Test
    public void testForecastGroupReportSheetThisYearDataCondition2() throws Exception {
        Map<String, String> reportParamsMap = DataExtractionReportUtil.parseReportParams(DATA_EXTRACTION_FORECAST_GROUP_PARAMS);
        DataExtractionReportCriteria reportCriteria = DataExtractionReportUtil.populateReportCriteria(reportParamsMap);
        reportCriteria.setForecastGroupArrivals(false);
        reportCriteria.setForecastGroupDepartures(false);
        reportCriteria.setForecastGroupRoomsSold(false);
        reportCriteria.setForecastGroupRoomsSoldSTLY(false);
        ScheduledReport<DataExtractionReportCriteria> scheduledReport = new ScheduledReport<>();
        scheduledReport.setReportCriteria(reportCriteria);
        scheduledReport.setLanguage(Language.ENGLISH);
        ReportSheet actualForecastGroupSheet = DataExtractionForecastGroupConverter.getForecastGroupReportSheet(getDataExtractionForecastGroupDTO(), scheduledReport);
        assertNotNull(actualForecastGroupSheet);

        Object[] expectedForecastGroupRowData = new Object[]{"Hilton - Pune", "Monday", "03-Oct-2016", "05-Oct-2015", "Complementary", "0", "0", "0", "0", "720.0", "900.0", "900.0", "915.0", "900.0", "25.0", "18.0", "0.0",
                "18.0", "0.0", "18.0", "6.0", "0.0", "6.0", "0.0", "48.0", "50.0", "36.6", "50.0"
        };
        Object[] actualForecastGroupRowdata = actualForecastGroupSheet.getRow(0);
        assertArrayEquals(actualForecastGroupRowdata, expectedForecastGroupRowData);

        Object[] expectedHeaderRow = new Object[]{"Property Name", "Day of Week", "Occupancy Date", "Comparison Date Last Year", "Forecast Group", "Cancelled This Year", "Cancelled Last Year Actual",
                "No Show This Year", "No Show Last Year Actual", "Booked Room Revenue This Year", "Booked Room Revenue Last Year Actual", "Booked Room Revenue STLY", "Forecasted Room Revenue This Year",
                "Forecasted Room Revenue Last Year Actual", "Occupancy Forecast This Year", "Occupancy Forecast Last Year Actual", "System Unconstrained Demand This Year", "System Unconstrained Demand Last Year Actual",
                "User Demand This Year", "User Demand Last Year Actual", "System Wash % This Year", "System Wash % Last Year Actual", "User Wash % This Year", "User Wash % Last Year Actual",
                "ADR On Books This Year", "ADR On Books Last Year Actual", "ADR Forecast This Year", "ADR Forecast Last Year Actual"
        };
        Object[] actualHeaderRow = actualForecastGroupSheet.getHeaderRow(0);
        assertArrayEquals(actualHeaderRow, expectedHeaderRow);
    }

    @Test
    public void testForecastGroupReportSheetThisYearDataCondition3() throws Exception {
        Map<String, String> reportParamsMap = DataExtractionReportUtil.parseReportParams(DATA_EXTRACTION_FORECAST_GROUP_PARAMS);
        DataExtractionReportCriteria reportCriteria = DataExtractionReportUtil.populateReportCriteria(reportParamsMap);
        reportCriteria.setForecastGroupArrivals(false);
        reportCriteria.setForecastGroupDepartures(false);
        reportCriteria.setForecastGroupRoomsSold(false);
        reportCriteria.setForecastGroupRoomsSoldSTLY(false);
        reportCriteria.setForecastGroupCancellations(false);
        reportCriteria.setForecastGroupNoShow(false);
        reportCriteria.setForecastGroupRevenue(false);
        reportCriteria.setForecastGroupRevenueSTLY(false);
        ScheduledReport<DataExtractionReportCriteria> scheduledReport = new ScheduledReport<>();
        scheduledReport.setReportCriteria(reportCriteria);
        scheduledReport.setLanguage(Language.ENGLISH);
        ReportSheet actualForecastGroupSheet = DataExtractionForecastGroupConverter.getForecastGroupReportSheet(getDataExtractionForecastGroupDTO(), scheduledReport);
        assertNotNull(actualForecastGroupSheet);

        Object[] expectedForecastGroupRowData = new Object[]{"Hilton - Pune", "Monday", "03-Oct-2016", "05-Oct-2015", "Complementary", "25.0", "18.0", "0.0", "18.0", "0.0", "18.0", "6.0", "0.0", "6.0",
                "0.0", "48.0", "50.0", "36.6", "50.0"};
        Object[] actualForecastGroupRowdata = actualForecastGroupSheet.getRow(0);
        assertArrayEquals(actualForecastGroupRowdata, expectedForecastGroupRowData);

        Object[] expectedHeaderRow = new Object[]{"Property Name", "Day of Week", "Occupancy Date", "Comparison Date Last Year", "Forecast Group", "Occupancy Forecast This Year", "Occupancy Forecast Last Year Actual",
                "System Unconstrained Demand This Year", "System Unconstrained Demand Last Year Actual", "User Demand This Year", "User Demand Last Year Actual", "System Wash % This Year", "System Wash % Last Year Actual",
                "User Wash % This Year", "User Wash % Last Year Actual", "ADR On Books This Year", "ADR On Books Last Year Actual", "ADR Forecast This Year", "ADR Forecast Last Year Actual"};
        Object[] actualHeaderRow = actualForecastGroupSheet.getHeaderRow(0);
        assertArrayEquals(actualHeaderRow, expectedHeaderRow);
    }

    private Map<DataExtractionType, List<DataExtractionReportDto>> getDataExtractionForecastGroupDTO() {
        Map<DataExtractionType, List<DataExtractionReportDto>> dataExtractionTypeListMap = new HashMap<>();
        List<DataExtractionReportDto> dtoList = new ArrayList<>();
        DataExtractionForecastGroup dto = new DataExtractionForecastGroup();


        dto.setPropertyName("Hilton - Pune");  // Property Name
        dto.setDayOfWeek("Monday");  // Day of Week
        dto.setOccupancyDate(new LocalDate("2016-10-03").toDate());  // Occupancy Date
        dto.setComparisonDateLastYear(new LocalDate("2015-10-05").toDate());  // Comparison Date Last Year
        dto.setForecastGroupName("Complementary");  // Forecast Group
        dto.setRoomsSoldThisYear(15);  // Occupancy On Books This Year
        dto.setRoomsSoldLastYear(18);  // Occupancy On Books Last Year Actual
        dto.setRoomsSoldSTLY(18);  // Occupancy On Books STLY
        dto.setArrivalsThisYear(11);  // Arrivals This Year
        dto.setArrivalsLastYear(13);  // Arrivals Last Year Actual
        dto.setDeparturesThisYear(8);  // Departures This Year
        dto.setDeparturesLastYear(10);  // Departures Last Year Actual
        dto.setCancelledThisYear(0);  // Cancelled This Year
        dto.setCancelledLastYear(0);  // Cancelled Last Year Actual
        dto.setNoShowThisYear(0);  // No Show This Year
        dto.setNoShowLastYear(0);  // No Show Last Year Actual
        dto.setOnBooksRevenueThisYear(new BigDecimal(720));  // Booked Room Revenue This Year
        dto.setOnBooksRevenueLastYear(new BigDecimal(900));  // Booked Room Revenue Last Year Actual
        dto.setOnBooksRevenueSTLY(new BigDecimal(900));  // Booked Room Revenue STLY
        dto.setRoomRevenueThisYear(new BigDecimal(915));  // Forecasted Room Revenue This Year
        dto.setRoomRevenueLastYear(new BigDecimal(900));  // Forecasted Room Revenue Last Year Actual
        dto.setOccupancyForecastThisYear(new BigDecimal(25));  // Occupancy Forecast This Year
        dto.setOccupancyForecastLastYear(new BigDecimal(18));  // Occupancy Forecast Last Year Actual
        dto.setSystemDemandThisYear(new BigDecimal(0));  // System Unconstrained Demand This Year
        dto.setSystemDemandLastYear(new BigDecimal(18));  // System Unconstrained Demand Last Year Actual
        dto.setUserDemandThisYear(new BigDecimal(0));  // User Demand This Year
        dto.setUserDemandLastYear(new BigDecimal(18));  // User Demand Last Year Actual
        dto.setSystemWashThisYear(new BigDecimal(6));  // System Wash % This Year
        dto.setSystemWashLastYear(new BigDecimal(0));  // System Wash % Last Year Actual
        dto.setUserWashThisYear(new BigDecimal(6));  // User Wash % This Year
        dto.setUserWashLastYear(new BigDecimal(0));  // User Wash % Last Year Actual
        dto.setOnBooksADRThisYear(new BigDecimal(48));  // ADR On Books This Year
        dto.setOnBooksADRLastYear(new BigDecimal(50));  // ADR On Books Last Year Actual
        dto.setAdrThisYear(new BigDecimal(36.6));  // ADR Forecast This Year
        dto.setAdrLastYear(new BigDecimal(50));  // ADR Forecast Last Year Actual

        dtoList.add(dto);
        dataExtractionTypeListMap.put(DataExtractionType.FORECAST_GROUP, dtoList);
        return dataExtractionTypeListMap;
    }

}