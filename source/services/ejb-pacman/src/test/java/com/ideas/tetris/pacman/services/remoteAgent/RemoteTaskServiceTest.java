package com.ideas.tetris.pacman.services.remoteAgent;

import com.ideas.g3.data.TestClient;
import com.ideas.g3.integration.opera.agent.task.AgentTask;
import com.ideas.g3.integration.opera.agent.task.AgentTaskType;
import com.ideas.g3.integration.opera.agent.task.ParameterType;
import com.ideas.g3.integration.opera.agent.task.PropertyTask;
import com.ideas.g3.integration.opera.agent.task.PropertyTaskType;
import com.ideas.g3.integration.opera.agent.task.RemoteTaskDto;
import com.ideas.g3.integration.opera.agent.task.RemoteTaskStatus;
import com.ideas.g3.integration.opera.agent.task.TaskParameterDto;
import com.ideas.g3.test.AbstractG3JupiterTest;
import com.ideas.infra.tetris.security.jaas.TetrisPrincipal;
import com.ideas.tetris.pacman.common.configparams.GUIConfigParamName;
import com.ideas.tetris.pacman.common.configparams.IntegrationConfigParamName;
import com.ideas.tetris.pacman.common.configparams.RemoteAgentConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.marketsegment.component.MarketSegmentComponent;
import com.ideas.tetris.pacman.services.marketsegment.entity.MktSeg;
import com.ideas.tetris.pacman.services.opera.OperaIncomingFile;
import com.ideas.tetris.pacman.services.opera.ProcessDecisionStatus;
import com.ideas.tetris.pacman.services.property.PropertyService;
import com.ideas.tetris.pacman.services.remoteAgent.entity.PropertyRemoteTask;
import com.ideas.tetris.pacman.services.remoteAgent.entity.PropertyTaskParameter;
import com.ideas.tetris.pacman.services.remoteAgent.entity.RemoteAgent;
import com.ideas.tetris.pacman.services.remoteAgent.entity.RemoteAgentType;
import com.ideas.tetris.pacman.services.remoteAgent.entity.RemoteTask;
import com.ideas.tetris.pacman.services.remoteAgent.entity.RemoteTaskCriteria;
import com.ideas.tetris.pacman.services.remoteAgent.entity.TaskClass;
import com.ideas.tetris.pacman.services.remoteAgent.entity.TaskParameter;
import com.ideas.tetris.platform.common.businessservice.async.AsyncCallbackData;
import com.ideas.tetris.platform.common.contextholder.PacmanThreadLocalContextHolder;
import com.ideas.tetris.platform.common.crudservice.AbstractMultiPropertyCrudService;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.errorhandling.ErrorCode;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.common.job.JobCallback;
import com.ideas.tetris.platform.common.xml.schema.workcontext.v1.WorkContextType;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import com.ideas.tetris.platform.services.daoandentities.entity.Client;
import com.ideas.tetris.platform.services.daoandentities.entity.Property;
import com.ideas.tetris.platform.services.daoandentities.entity.Status;
import org.joda.time.LocalDate;
import org.joda.time.LocalDateTime;
import org.joda.time.LocalTime;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.ideas.g3.integration.opera.agent.task.RemoteTaskStatus.FAILED;
import static com.ideas.g3.integration.opera.agent.task.RemoteTaskStatus.IN_PROGRESS;
import static com.ideas.g3.integration.opera.agent.task.RemoteTaskStatus.PENDING;
import static com.ideas.tetris.platform.common.externalsystem.ExternalSystem.OPERA_AGENT;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.fail;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyMapOf;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoMoreInteractions;
import static org.mockito.Mockito.verifyZeroInteractions;
import static org.mockito.Mockito.when;

@MockitoSettings(strictness = Strictness.LENIENT)
public class RemoteTaskServiceTest extends AbstractG3JupiterTest {

    private static final DateTimeFormatter FORMAT = DateTimeFormat.forPattern("yyyy-MM-dd HH:mm:ss");
    private static final LocalDateTime CREATED_DATE = LocalDateTime.parse("2015-09-01 12:34:56", FORMAT);
    private static final LocalDateTime COMPLETED_DATE = LocalDateTime.parse("2015-09-02 12:43:56", FORMAT);
    private static final LocalDateTime SCHEDULED_DATE = LocalDateTime.parse("2015-09-02 01:00:00", FORMAT);
    private static final String RESULTS_SUCCESS = "some additional details about the results of the task";
    private static final String RESULTS_FAILURE = "some additional details about the failure";
    private static final String EXCEPTION_RESULTS_FAILURE = "Tetris Errors: [0 - UNEXPECTED_ERROR - some additional details about the failure - User.id: 11403 - Workcontext: null]";
    private static final Long JOB_INSTANCE_ID = 321L;
    private static final Integer REMOTE_AGENT_ID = 1211;
    private static final int PROPERTY_ID = 444;
    private static final Integer TASK_ID = 555;
    private static final String CORRELATION_ID = "correlationId";
    private static final String PROPERTY_CODE = "myProp";
    private static final String CLIENT_CODE = "myClient";
    @Mock
    PacmanConfigParamsService configService;
    @Mock
    PropertyService propertyService;
    @Mock
    JobCallback jobCallback;
    @Mock
    CrudService crudService;
    @Mock
    AbstractMultiPropertyCrudService multiPropertyCrudService;
    @Mock
    ProcessDecisionStatus processDecisionStatus;
    @Mock
    MarketSegmentComponent marketSegmentComponent;
    @InjectMocks
    private RemoteTaskService remoteTaskService;
    private RemoteAgent remoteAgent;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        remoteTaskService.globalCrudService = globalCrudService();
        RemoteTaskService.JOB_CALLBACK = jobCallback;
        remoteAgent = persistRemoteAgent();
        WorkContextType workContext = new WorkContextType();
        PacmanWorkContextHelper.setWorkContext(workContext);
        PacmanWorkContextHelper.setPropertyId(PROPERTY_ID);
    }

    @Test
    public void getRemoteTasks_allTasks() {
        RemoteTask remoteTask = persistRemoteTask(AgentTaskType.ADD_PROPERTY.toString(), RemoteTaskStatus.COMPLETED.toString());

        RemoteTaskCriteria criteria = new RemoteTaskCriteria();

        List<RemoteTaskDto> results = remoteTaskService.getRemoteTasks(criteria);

        assertEquals(1, results.size());
        AgentTask dto = (AgentTask) results.get(0);
        assertEquals(remoteTask.getId().intValue(), dto.getTaskId());
        assertEquals(remoteAgent.getId().intValue(), dto.getAgentId());
        assertEquals(RemoteTaskStatus.COMPLETED, dto.getStatus());
        assertEquals(AgentTaskType.ADD_PROPERTY, dto.getType());
        assertEquals(CREATED_DATE, dto.getCreatedDate());
        assertEquals(COMPLETED_DATE, dto.getCompletedDate());
        assertEquals(SCHEDULED_DATE, dto.getScheduledDate());
        assertEquals(0, dto.getParameters().size());
    }

    @Test
    public void getRemoteTasks_scheduledTasks() {
        LocalDateTime now = new LocalDateTime();
        persistScheduledTask(now.plusMinutes(5));
        RemoteTask expectedTask = persistScheduledTask(now.minusMinutes(5));

        RemoteTaskCriteria criteria = new RemoteTaskCriteria();
        criteria.setScheduledDate(now);

        List<RemoteTaskDto> results = remoteTaskService.getRemoteTasks(criteria);

        assertEquals(1, results.size());
        AgentTask dto = (AgentTask) results.get(0);
        assertEquals(expectedTask.getId().intValue(), dto.getTaskId());
        assertEquals(remoteAgent.getId().intValue(), dto.getAgentId());
        assertEquals(RemoteTaskStatus.PENDING, dto.getStatus());
        assertEquals(AgentTaskType.ADD_PROPERTY, dto.getType());
        assertEquals(CREATED_DATE, dto.getCreatedDate());
        assertNull(dto.getCompletedDate());
        assertEquals(now.minusMinutes(5), dto.getScheduledDate());
        assertEquals(0, dto.getParameters().size());
    }

    @Test
    public void getRemoteTaskCount() {
        persistRemoteTask(PropertyTaskType.DOWNLOAD_FEED_DATA.toString(), RemoteTaskStatus.COMPLETED.toString());
        persistInProgressRemoteTask(PropertyTaskType.DOWNLOAD_FEED_DATA.toString());

        RemoteTaskCriteria criteria = new RemoteTaskCriteria();
        Set<String> statuses = new HashSet<>();
        statuses.add(RemoteTaskStatus.IN_PROGRESS.toString());
        criteria.setStatuses(statuses);
        criteria.setScheduledDate(SCHEDULED_DATE);

        assertEquals(1, remoteTaskService.getRemoteTaskCount(criteria));
    }

    @Test
    public void saveRemoteTask_newTask() {
        AgentTask dto = new AgentTask();
        dto.setAgentId(remoteAgent.getId());
        dto.setType(AgentTaskType.ADD_PROPERTY);
        dto.setStatus(RemoteTaskStatus.IN_PROGRESS);
        dto.setCreatedDate(CREATED_DATE);
        dto.setCompletedDate(COMPLETED_DATE);
        dto.setScheduledDate(SCHEDULED_DATE);

        AgentTask result = (AgentTask) remoteTaskService.saveRemoteTask(dto);

        assertEquals(remoteAgent.getId().intValue(), result.getAgentId());
        assertEquals(AgentTaskType.ADD_PROPERTY, result.getType());
        assertEquals(RemoteTaskStatus.IN_PROGRESS, result.getStatus());
        assertEquals(CREATED_DATE, result.getCreatedDate());
        assertEquals(COMPLETED_DATE, result.getCompletedDate());
        assertEquals(SCHEDULED_DATE, result.getScheduledDate());
    }

    @Test
    public void saveRemoteTask_taskExists() {
        RemoteTask remoteTask = persistRemoteTask(AgentTaskType.ADD_PROPERTY.toString(), RemoteTaskStatus.COMPLETED.toString());

        AgentTask dto = new AgentTask();
        dto.setTaskId(remoteTask.getId());
        dto.setAgentId(remoteAgent.getId());
        dto.setType(AgentTaskType.ADD_PROPERTY);
        dto.setStatus(RemoteTaskStatus.IN_PROGRESS);
        dto.setCreatedDate(CREATED_DATE.plusHours(1));
        dto.setCompletedDate(COMPLETED_DATE.plusHours(1));
        dto.setScheduledDate(SCHEDULED_DATE.plusHours(1));

        AgentTask result = (AgentTask) remoteTaskService.saveRemoteTask(dto);

        assertEquals(remoteTask.getId().intValue(), result.getTaskId());
        assertEquals(remoteAgent.getId().intValue(), result.getAgentId());
        assertEquals(AgentTaskType.ADD_PROPERTY, result.getType());
        assertEquals(RemoteTaskStatus.IN_PROGRESS, result.getStatus());
        assertEquals(CREATED_DATE.plusHours(1), result.getCreatedDate());
        assertEquals(COMPLETED_DATE.plusHours(1), result.getCompletedDate());
        assertEquals(SCHEDULED_DATE.plusHours(1), result.getScheduledDate());
    }

    @Test
    public void taskCompleted_withId() throws Exception {
        RemoteTask inProgressTask = persistInProgressRemoteTask(AgentTaskType.ADD_PROPERTY.toString());

        AgentTask completedTask = (AgentTask) remoteTaskService.taskCompleted(inProgressTask.getId(), COMPLETED_DATE, RESULTS_SUCCESS);

        assertEquals(inProgressTask.getId().intValue(), completedTask.getTaskId());
        assertEquals(remoteAgent.getId().intValue(), completedTask.getAgentId());
        assertEquals(AgentTaskType.ADD_PROPERTY, completedTask.getType());
        assertEquals(RemoteTaskStatus.COMPLETED, completedTask.getStatus());
        assertEquals(CREATED_DATE, completedTask.getCreatedDate());
        assertEquals(COMPLETED_DATE, completedTask.getCompletedDate());
        assertEquals(SCHEDULED_DATE, completedTask.getScheduledDate());
        assertEquals(RESULTS_SUCCESS, completedTask.getResults());
    }

    @Test
    public void taskCompleted_withId_taskNotFound() throws Exception {
        try {
            remoteTaskService.taskCompleted(-123, COMPLETED_DATE, RESULTS_SUCCESS);
            fail("Should have thrown exception");
        } catch (TetrisException e) {
            assertEquals(ErrorCode.REMOTE_TASK_NOT_FOUND, e.getErrorCode());
        }
    }

    @Test
    public void taskCompleted_withId_taskNotInProgress() {
        try {
            RemoteTask inProgressTask = persistInProgressRemoteTask(PropertyTaskType.DOWNLOAD_FEED_DATA.toString());
            RemoteTask abandonedTask = remoteTaskService.abandonRemoteTask(inProgressTask);

            remoteTaskService.taskCompleted(abandonedTask.getId(), COMPLETED_DATE, RESULTS_SUCCESS);
            fail("Should have thrown exception");
        } catch (TaskNotInProgressException e) {
            // expected exception
        }
    }

    @Test
    public void taskCompleted_withEntity() throws Exception {
        RemoteTask inProgressTask = persistInProgressRemoteTask(PropertyTaskType.DOWNLOAD_FEED_DATA.toString());

        RemoteTask completedTask = remoteTaskService.taskCompleted(inProgressTask, COMPLETED_DATE, RESULTS_SUCCESS);

        assertEquals(inProgressTask.getId(), completedTask.getId());
        assertEquals(remoteAgent.getId(), completedTask.getRemoteAgent().getId());
        assertEquals(PropertyTaskType.DOWNLOAD_FEED_DATA.toString(), completedTask.getTaskType());
        assertEquals(RemoteTaskStatus.COMPLETED.toString(), completedTask.getTaskStatus());
        assertEquals(CREATED_DATE, completedTask.getCreatedDate());
        assertEquals(COMPLETED_DATE, completedTask.getCompletedDate());
        assertEquals(SCHEDULED_DATE, completedTask.getScheduledDate());
        assertEquals(RESULTS_SUCCESS, completedTask.getResults());
    }

    @Test
    public void taskCompleted_withJobCallback() throws Exception {
        RemoteTask inProgressTask = persistInProgressRemoteTaskWithJobId(12345L);

        RemoteTask completedTask = remoteTaskService.taskCompleted(inProgressTask, COMPLETED_DATE, RESULTS_SUCCESS);

        assertEquals(inProgressTask.getId(), completedTask.getId());
        assertEquals(remoteAgent.getId(), completedTask.getRemoteAgent().getId());
        assertEquals(AgentTaskType.ADD_PROPERTY.toString(), completedTask.getTaskType());
        assertEquals(RemoteTaskStatus.COMPLETED.toString(), completedTask.getTaskStatus());
        assertEquals(CREATED_DATE, completedTask.getCreatedDate());
        assertEquals(COMPLETED_DATE, completedTask.getCompletedDate());
        assertEquals(SCHEDULED_DATE, completedTask.getScheduledDate());
        assertEquals(RESULTS_SUCCESS, completedTask.getResults());

        ArgumentCaptor<AsyncCallbackData> captor = ArgumentCaptor.forClass(AsyncCallbackData.class);
        verify(jobCallback).execute(captor.capture());
        AsyncCallbackData asyncCallbackData = captor.getValue();
        assertEquals(Long.valueOf(12345), asyncCallbackData.getJobInstanceId());
        assertTrue(asyncCallbackData.wasSuccessful());
        assertEquals(RESULTS_SUCCESS, asyncCallbackData.getResponse());
    }

    @Test
    public void taskCompleted_withEntity_null() throws Exception {
        assertThrows(IllegalArgumentException.class, () -> {
            remoteTaskService.taskCompleted(null, COMPLETED_DATE, RESULTS_SUCCESS);
        });
    }

    @Test
    public void taskCompleted_withEntity_taskNotInProgress() {
        RemoteTask inProgressTask = persistInProgressRemoteTaskWithJobId(12345L);
        RemoteTask abandonedTask = remoteTaskService.abandonRemoteTask(inProgressTask);

        try {
            remoteTaskService.taskCompleted(abandonedTask, COMPLETED_DATE, RESULTS_SUCCESS);
            fail("should have thrown exception");
        } catch (TaskNotInProgressException e) {
            // expected exception
        }

        Mockito.verifyZeroInteractions(jobCallback);
    }

    @Test
    public void taskCompleted_withEntity_taskNotInProgressWithoutPrincipal() {
        PacmanThreadLocalContextHolder.setPrincipal(null);

        RemoteTask inProgressTask = persistInProgressRemoteTaskWithJobId(12345L);
        RemoteTask abandonedTask = remoteTaskService.abandonRemoteTask(inProgressTask);

        try {
            remoteTaskService.taskCompleted(abandonedTask, COMPLETED_DATE, RESULTS_SUCCESS);
            fail("should have thrown exception");
        } catch (TaskNotInProgressException e) {
            // expected exception
        }

        Mockito.verifyZeroInteractions(jobCallback);
    }

    @Test
    public void taskFailed_withId() throws Exception {
        RemoteTask inProgressTask = persistInProgressRemoteTask(AgentTaskType.ADD_PROPERTY.toString());

        AgentTask completedTask = (AgentTask) remoteTaskService.taskFailed(inProgressTask.getId(), COMPLETED_DATE, RESULTS_FAILURE);

        assertEquals(inProgressTask.getId().intValue(), completedTask.getTaskId());
        assertEquals(remoteAgent.getId().intValue(), completedTask.getAgentId());
        assertEquals(AgentTaskType.ADD_PROPERTY, completedTask.getType());
        assertEquals(RemoteTaskStatus.FAILED, completedTask.getStatus());
        assertEquals(CREATED_DATE, completedTask.getCreatedDate());
        assertEquals(COMPLETED_DATE, completedTask.getCompletedDate());
        assertEquals(SCHEDULED_DATE, completedTask.getScheduledDate());
        assertEquals(RESULTS_FAILURE, completedTask.getResults());
    }

    @Test
    public void taskFailed_withId_taskNotFound() throws Exception {
        try {
            remoteTaskService.taskFailed(-123, COMPLETED_DATE, RESULTS_SUCCESS);
            fail("Should have thrown exception");
        } catch (TetrisException e) {
            assertEquals(ErrorCode.REMOTE_TASK_NOT_FOUND, e.getErrorCode());
        }
    }

    @Test
    public void taskFailed_withId_taskNotInProgress() {
        try {
            RemoteTask inProgressTask = persistInProgressRemoteTask(PropertyTaskType.DOWNLOAD_FEED_DATA.toString());
            RemoteTask abandonedTask = remoteTaskService.abandonRemoteTask(inProgressTask);

            remoteTaskService.taskFailed(abandonedTask.getId(), COMPLETED_DATE, RESULTS_SUCCESS);
            fail("Should have thrown exception");
        } catch (TaskNotInProgressException e) {
            // expected exception
        }
    }

    @Test
    public void taskFailed_withEntity() throws Exception {
        RemoteTask inProgressTask = persistInProgressRemoteTask(PropertyTaskType.DOWNLOAD_FEED_DATA.toString());

        RemoteTask completedTask = remoteTaskService.taskFailed(inProgressTask, COMPLETED_DATE, RESULTS_FAILURE);

        assertEquals(inProgressTask.getId(), completedTask.getId());
        assertEquals(remoteAgent.getId(), completedTask.getRemoteAgent().getId());
        assertEquals(PropertyTaskType.DOWNLOAD_FEED_DATA.toString(), completedTask.getTaskType());
        assertEquals(RemoteTaskStatus.FAILED.toString(), completedTask.getTaskStatus());
        assertEquals(CREATED_DATE, completedTask.getCreatedDate());
        assertEquals(COMPLETED_DATE, completedTask.getCompletedDate());
        assertEquals(SCHEDULED_DATE, completedTask.getScheduledDate());
        assertEquals(RESULTS_FAILURE, completedTask.getResults());
    }

    @Test
    public void taskFailed_withJobCallback() throws Exception {
        RemoteTask inProgressTask = persistInProgressRemoteTaskWithJobId(12345L);

        AgentTask completedTask = (AgentTask) remoteTaskService.taskFailed(inProgressTask.getId(), COMPLETED_DATE, RESULTS_FAILURE);

        assertEquals(inProgressTask.getId().intValue(), completedTask.getTaskId());
        assertEquals(remoteAgent.getId().intValue(), completedTask.getAgentId());
        assertEquals(AgentTaskType.ADD_PROPERTY, completedTask.getType());
        assertEquals(RemoteTaskStatus.CLOSED, completedTask.getStatus());
        assertEquals(CREATED_DATE, completedTask.getCreatedDate());
        assertEquals(COMPLETED_DATE, completedTask.getCompletedDate());
        assertEquals(SCHEDULED_DATE, completedTask.getScheduledDate());
        assertEquals(RESULTS_FAILURE, completedTask.getResults());

        ArgumentCaptor<AsyncCallbackData> captor = ArgumentCaptor.forClass(AsyncCallbackData.class);
        verify(jobCallback).execute(captor.capture());
        AsyncCallbackData asyncCallbackData = captor.getValue();
        assertEquals(Long.valueOf("12345"), asyncCallbackData.getJobInstanceId());
        assertFalse(asyncCallbackData.wasSuccessful());
        assertEquals(EXCEPTION_RESULTS_FAILURE, ((TetrisException) asyncCallbackData.getResponse()).getMessage());
    }

    @Test
    public void taskFailed_withEntity_null() throws Exception {
        assertThrows(IllegalArgumentException.class, () -> {
            remoteTaskService.taskFailed(null, COMPLETED_DATE, RESULTS_SUCCESS);
        });
    }

    @Test
    public void taskFailed_withEntity_taskNotInProgress() {
        RemoteTask inProgressTask = persistInProgressRemoteTaskWithJobId(12345L);
        RemoteTask abandonedTask = remoteTaskService.abandonRemoteTask(inProgressTask);

        try {
            remoteTaskService.taskFailed(abandonedTask, COMPLETED_DATE, RESULTS_SUCCESS);
            fail("should have thrown exception");
        } catch (TaskNotInProgressException e) {
            // expected exception
        }

        Mockito.verifyZeroInteractions(jobCallback);
    }

    private RemoteAgent persistRemoteAgent() {
        RemoteAgent remoteAgent = new RemoteAgent();
        remoteAgent.setClient(new Client(TestClient.BSTN.getId()));
        remoteAgent.setType(RemoteAgentType.OPMS);
        remoteAgent.setMachineName("");
        remoteAgent.setOsVersion("");
        return globalCrudService().save(remoteAgent);
    }

    private RemoteTask persistRemoteTask(String taskType, String taskStatus) {
        RemoteTask remoteTask = new RemoteTask();
        remoteTask.setRemoteAgent(remoteAgent);
        remoteTask.setTaskClass(TaskClass.AGENT.toString());
        remoteTask.setTaskType(taskType);
        remoteTask.setTaskStatus(taskStatus);
        remoteTask.setCreatedDate(CREATED_DATE);
        remoteTask.setCompletedDate(COMPLETED_DATE);
        remoteTask.setScheduledDate(SCHEDULED_DATE);
        remoteTask.setParameters(new ArrayList<>());
        return globalCrudService().save(remoteTask);
    }

    private RemoteTask persistInProgressRemoteTask(String taskType) {
        RemoteTask remoteTask = new RemoteTask();
        remoteTask.setRemoteAgent(remoteAgent);
        remoteTask.setTaskClass(TaskClass.AGENT.toString());
        remoteTask.setTaskType(taskType);
        remoteTask.setTaskStatus(RemoteTaskStatus.IN_PROGRESS.toString());
        remoteTask.setCreatedDate(CREATED_DATE);
        remoteTask.setCompletedDate(null);
        remoteTask.setScheduledDate(SCHEDULED_DATE);
        remoteTask.setParameters(new ArrayList<>());
        return globalCrudService().save(remoteTask);
    }

    private RemoteTask persistInProgressRemoteTaskWithJobId(Long jobId) {
        RemoteTask remoteTask = persistInProgressRemoteTask(AgentTaskType.ADD_PROPERTY.toString());
        ArrayList<TaskParameter> parameters = new ArrayList<>();
        TaskParameter parameter = getTaskParameter(remoteTask, ParameterType.JOB_INSTANCE_ID.name(), jobId.toString());
        parameters.add(parameter);
        remoteTask.setParameters(parameters);
        return globalCrudService().save(remoteTask);
    }

    private TaskParameter getTaskParameter(RemoteTask remoteTask, String parameterType, String parameterValue) {
        TaskParameter parameter = new TaskParameter();
        parameter.setRemoteTask(remoteTask);
        parameter.setParameterType(parameterType);
        parameter.setValue(parameterValue);
        return parameter;
    }

    private RemoteTask persistScheduledTask(LocalDateTime scheduledDate) {
        RemoteTask remoteTask = new RemoteTask();
        remoteTask.setRemoteAgent(remoteAgent);
        remoteTask.setTaskClass(TaskClass.AGENT.toString());
        remoteTask.setTaskType(AgentTaskType.ADD_PROPERTY.toString());
        remoteTask.setTaskStatus(RemoteTaskStatus.PENDING.toString());
        remoteTask.setCreatedDate(CREATED_DATE);
        remoteTask.setCompletedDate(null);
        remoteTask.setScheduledDate(scheduledDate);
        remoteTask.setParameters(new ArrayList<>());
        return globalCrudService().save(remoteTask);
    }

    @Test
    public void test_createDeletePropertyTask() {
        remoteTaskService.globalCrudService = crudService;
        Property property = new Property();
        property.setCode(PROPERTY_CODE);
        Client client = new Client();
        client.setCode(CLIENT_CODE);
        property.setClient(client);
        RemoteTask entity = new RemoteTask();
        entity.setTaskClass(TaskClass.AGENT.toString());
        RemoteAgent agent = new RemoteAgent();
        agent.setId(REMOTE_AGENT_ID);
        agent.setClient(client);
        when(crudService.find(RemoteAgent.class, REMOTE_AGENT_ID)).thenReturn(agent);
        entity.setRemoteAgent(agent);
        entity.setRemoteTaskId(TASK_ID);
        entity.setTaskStatus(RemoteTaskStatus.PENDING.toString());
        entity.setTaskType(AgentTaskType.DELETE_PROPERTY.toString());
        TaskParameter param1 = new TaskParameter();
        param1.setParameterType(ParameterType.PROPERTY_ID.toString());
        param1.setValue(String.valueOf(PROPERTY_ID));
        entity.addParameter(param1);
        TaskParameter param2 = new TaskParameter();
        param2.setParameterType(ParameterType.JOB_INSTANCE_ID.toString());
        param2.setValue(String.valueOf(JOB_INSTANCE_ID));
        entity.addParameter(param2);
        entity.setCreatedDate(CREATED_DATE);
        when(crudService.save(any(RemoteTask.class))).thenReturn(entity);
        AgentTask result = remoteTaskService.createDeletePropertyTask(REMOTE_AGENT_ID, PROPERTY_ID, JOB_INSTANCE_ID);
        assertEquals(REMOTE_AGENT_ID.intValue(), result.getAgentId());
        assertNull(result.getCompletedDate());
        assertNotNull(result.getCreatedDate());
        assertNull(result.getResults());
        assertNull(result.getScheduledDate());
        Set<TaskParameterDto> parameters = result.getParameters();
        assertEquals(2, parameters.size());
        assertEquals(RemoteTaskStatus.PENDING, result.getStatus());
        assertNotEquals(-1, result.getTaskId());
        assertEquals(AgentTaskType.DELETE_PROPERTY, result.getType());
        ArgumentCaptor<RemoteTask> captor = ArgumentCaptor.forClass(RemoteTask.class);
        verify(crudService).save(captor.capture());
        RemoteTask task = captor.getValue();
        assertNull(task.getCompletedDate());
        assertNotNull(task.getCreatedDate());
        assertNull(task.getId());
        assertEquals(JOB_INSTANCE_ID.toString(), task.getParameterValue(ParameterType.JOB_INSTANCE_ID));
        assertEquals(String.valueOf(PROPERTY_ID), task.getParameterValue(ParameterType.PROPERTY_ID));
        assertNull(task.getResults());
        assertNull(task.getScheduledDate());
        assertEquals(TaskClass.AGENT.toString(), task.getTaskClass());
        assertEquals(AgentTaskType.DELETE_PROPERTY.toString(), task.getTaskType());
    }

    @Test
    public void test_taskCompleted_deleteProperty() throws Exception {
        remoteTaskService.globalCrudService = crudService;
        RemoteTask task = new RemoteTask();
        task.setCreatedDate(CREATED_DATE);
        task.setId(TASK_ID);
        RemoteAgent agent = new RemoteAgent();
        agent.setId(REMOTE_AGENT_ID);
        task.setRemoteAgent(agent);
        task.setTaskClass(TaskClass.AGENT.toString());
        task.setTaskStatus(RemoteTaskStatus.IN_PROGRESS.toString());
        TaskParameter param1 = new TaskParameter();
        param1.setParameterType(ParameterType.PROPERTY_ID.toString());
        param1.setValue(String.valueOf(PROPERTY_ID));
        task.addParameter(param1);
        TaskParameter param2 = new TaskParameter();
        param2.setParameterType(ParameterType.JOB_INSTANCE_ID.toString());
        param2.setValue(String.valueOf(JOB_INSTANCE_ID));
        task.addParameter(param2);
        task.setTaskType(AgentTaskType.DELETE_PROPERTY.toString());
        List<RemoteTask> matchingTasks = new ArrayList<>();
        when(crudService.findByCriteria(any(RemoteTaskCriteria.class))).thenReturn(matchingTasks);
        when(crudService.save(task)).thenReturn(task);
        RemoteTask completedTask = remoteTaskService.taskCompleted(task, COMPLETED_DATE, RESULTS_SUCCESS);

        assertEquals(TASK_ID, completedTask.getId());
        assertEquals(REMOTE_AGENT_ID, completedTask.getRemoteAgent().getId());
        assertEquals(AgentTaskType.DELETE_PROPERTY.toString(), completedTask.getTaskType());
        assertEquals(RemoteTaskStatus.COMPLETED.toString(), completedTask.getTaskStatus());
        assertEquals(CREATED_DATE, completedTask.getCreatedDate());
        assertEquals(COMPLETED_DATE, completedTask.getCompletedDate());
        assertNull(completedTask.getScheduledDate());
        assertEquals(RESULTS_SUCCESS, completedTask.getResults());

        ArgumentCaptor<AsyncCallbackData> captor = ArgumentCaptor.forClass(AsyncCallbackData.class);
        verify(jobCallback).execute(captor.capture());
        AsyncCallbackData asyncCallbackData = captor.getValue();
        assertEquals(JOB_INSTANCE_ID, asyncCallbackData.getJobInstanceId());
        assertTrue(asyncCallbackData.wasSuccessful());
        assertEquals(RESULTS_SUCCESS, asyncCallbackData.getResponse());
        ArgumentCaptor<RemoteTaskCriteria> criteriaCaptor = ArgumentCaptor.forClass(RemoteTaskCriteria.class);
        verify(crudService).findByCriteria(criteriaCaptor.capture());
        RemoteTaskCriteria criteria = criteriaCaptor.getValue();
        Set<String> statuses = criteria.getStatuses();
        assertEquals(2, statuses.size());
        assertTrue(statuses.contains(RemoteTaskStatus.IN_PROGRESS.toString()));
        assertTrue(statuses.contains(RemoteTaskStatus.PENDING.toString()));
        Set<String> types = criteria.getTaskTypes();
        assertEquals(1, types.size());
        assertTrue(types.contains(AgentTaskType.DELETE_PROPERTY.toString()));

    }

    @Test
    public void test_taskCompleted_deletePropertyOthersInProgress() throws Exception {
        remoteTaskService.globalCrudService = crudService;
        RemoteTask task = new RemoteTask();
        task.setCreatedDate(CREATED_DATE);
        task.setId(TASK_ID);
        RemoteAgent agent = new RemoteAgent();
        agent.setId(REMOTE_AGENT_ID);
        task.setRemoteAgent(agent);
        task.setTaskClass(TaskClass.AGENT.toString());
        task.setTaskStatus(RemoteTaskStatus.IN_PROGRESS.toString());
        TaskParameter param1 = new TaskParameter();
        param1.setParameterType(ParameterType.PROPERTY_ID.toString());
        param1.setValue(String.valueOf(PROPERTY_ID));
        task.addParameter(param1);
        TaskParameter param2 = new TaskParameter();
        param2.setParameterType(ParameterType.JOB_INSTANCE_ID.toString());
        param2.setValue(String.valueOf(JOB_INSTANCE_ID));
        task.addParameter(param2);
        task.setTaskType(AgentTaskType.DELETE_PROPERTY.toString());
        List<RemoteTask> matchingTasks = new ArrayList<>();
        RemoteTask inProgressTask = new RemoteTask();
        inProgressTask.addParameter(param1);
        matchingTasks.add(inProgressTask);
        when(crudService.findByCriteria(any(RemoteTaskCriteria.class))).thenReturn(matchingTasks);
        when(crudService.save(task)).thenReturn(task);
        RemoteTask completedTask = remoteTaskService.taskCompleted(task, COMPLETED_DATE, RESULTS_SUCCESS);

        assertEquals(TASK_ID, completedTask.getId());
        assertEquals(REMOTE_AGENT_ID, completedTask.getRemoteAgent().getId());
        assertEquals(AgentTaskType.DELETE_PROPERTY.toString(), completedTask.getTaskType());
        assertEquals(RemoteTaskStatus.COMPLETED.toString(), completedTask.getTaskStatus());
        assertEquals(CREATED_DATE, completedTask.getCreatedDate());
        assertEquals(COMPLETED_DATE, completedTask.getCompletedDate());
        assertNull(completedTask.getScheduledDate());
        assertEquals(RESULTS_SUCCESS, completedTask.getResults());

        ArgumentCaptor<RemoteTaskCriteria> criteriaCaptor = ArgumentCaptor.forClass(RemoteTaskCriteria.class);
        verify(crudService).findByCriteria(criteriaCaptor.capture());
        RemoteTaskCriteria criteria = criteriaCaptor.getValue();
        Set<String> statuses = criteria.getStatuses();
        assertEquals(2, statuses.size());
        assertTrue(statuses.contains(RemoteTaskStatus.IN_PROGRESS.toString()));
        assertTrue(statuses.contains(RemoteTaskStatus.PENDING.toString()));
        Set<String> types = criteria.getTaskTypes();
        assertEquals(1, types.size());
        assertTrue(types.contains(AgentTaskType.DELETE_PROPERTY.toString()));

        Mockito.verifyZeroInteractions(jobCallback);
    }

    @Test
    public void test_taskCompleted_deletePropertyOthersInProgressDifferentProperty() throws Exception {
        remoteTaskService.globalCrudService = crudService;
        RemoteTask task = new RemoteTask();
        task.setCreatedDate(CREATED_DATE);
        task.setId(TASK_ID);
        RemoteAgent agent = new RemoteAgent();
        agent.setId(REMOTE_AGENT_ID);
        task.setRemoteAgent(agent);
        task.setTaskClass(TaskClass.AGENT.toString());
        task.setTaskStatus(RemoteTaskStatus.IN_PROGRESS.toString());
        TaskParameter param1 = new TaskParameter();
        param1.setParameterType(ParameterType.PROPERTY_ID.toString());
        param1.setValue(String.valueOf(PROPERTY_ID));
        task.addParameter(param1);
        TaskParameter param2 = new TaskParameter();
        param2.setParameterType(ParameterType.JOB_INSTANCE_ID.toString());
        param2.setValue(String.valueOf(JOB_INSTANCE_ID));
        task.addParameter(param2);
        task.setTaskType(AgentTaskType.DELETE_PROPERTY.toString());
        List<RemoteTask> matchingTasks = new ArrayList<>();
        RemoteTask inProgressTask = new RemoteTask();
        TaskParameter param3 = new TaskParameter();
        param3.setParameterType(ParameterType.PROPERTY_ID.toString());
        param3.setValue(String.valueOf(PROPERTY_ID + 1)); // does not match the property in the test
        inProgressTask.addParameter(param3);
        matchingTasks.add(inProgressTask);
        when(crudService.findByCriteria(any(RemoteTaskCriteria.class))).thenReturn(matchingTasks);
        when(crudService.save(task)).thenReturn(task);
        RemoteTask completedTask = remoteTaskService.taskCompleted(task, COMPLETED_DATE, RESULTS_SUCCESS);

        assertEquals(TASK_ID, completedTask.getId());
        assertEquals(REMOTE_AGENT_ID, completedTask.getRemoteAgent().getId());
        assertEquals(AgentTaskType.DELETE_PROPERTY.toString(), completedTask.getTaskType());
        assertEquals(RemoteTaskStatus.COMPLETED.toString(), completedTask.getTaskStatus());
        assertEquals(CREATED_DATE, completedTask.getCreatedDate());
        assertEquals(COMPLETED_DATE, completedTask.getCompletedDate());
        assertNull(completedTask.getScheduledDate());
        assertEquals(RESULTS_SUCCESS, completedTask.getResults());

        ArgumentCaptor<AsyncCallbackData> captor = ArgumentCaptor.forClass(AsyncCallbackData.class);
        verify(jobCallback).execute(captor.capture());
        AsyncCallbackData asyncCallbackData = captor.getValue();
        assertEquals(JOB_INSTANCE_ID, asyncCallbackData.getJobInstanceId());
        assertTrue(asyncCallbackData.wasSuccessful());
        assertEquals(RESULTS_SUCCESS, asyncCallbackData.getResponse());
        ArgumentCaptor<RemoteTaskCriteria> criteriaCaptor = ArgumentCaptor.forClass(RemoteTaskCriteria.class);
        verify(crudService).findByCriteria(criteriaCaptor.capture());
        RemoteTaskCriteria criteria = criteriaCaptor.getValue();
        Set<String> statuses = criteria.getStatuses();
        assertEquals(2, statuses.size());
        assertTrue(statuses.contains(RemoteTaskStatus.IN_PROGRESS.toString()));
        assertTrue(statuses.contains(RemoteTaskStatus.PENDING.toString()));
        Set<String> types = criteria.getTaskTypes();
        assertEquals(1, types.size());
        assertTrue(types.contains(AgentTaskType.DELETE_PROPERTY.toString()));
    }

    @Test
    public void test_taskFailed_deleteProperty() throws Exception {
        remoteTaskService.globalCrudService = crudService;
        RemoteTask task = new RemoteTask();
        task.setCreatedDate(CREATED_DATE);
        task.setId(TASK_ID);
        RemoteAgent agent = new RemoteAgent();
        agent.setId(REMOTE_AGENT_ID);
        task.setRemoteAgent(agent);
        task.setTaskClass(TaskClass.AGENT.toString());
        task.setTaskStatus(RemoteTaskStatus.IN_PROGRESS.toString());
        TaskParameter param1 = new TaskParameter();
        param1.setParameterType(ParameterType.PROPERTY_ID.toString());
        param1.setValue(String.valueOf(PROPERTY_ID));
        task.addParameter(param1);
        TaskParameter param2 = new TaskParameter();
        param2.setParameterType(ParameterType.JOB_INSTANCE_ID.toString());
        param2.setValue(String.valueOf(JOB_INSTANCE_ID));
        task.addParameter(param2);
        task.setTaskType(AgentTaskType.DELETE_PROPERTY.toString());
        when(crudService.save(task)).thenReturn(task);
        RemoteTask completedTask = remoteTaskService.taskFailed(task, COMPLETED_DATE, RESULTS_FAILURE);

        assertEquals(TASK_ID, completedTask.getId());
        assertEquals(REMOTE_AGENT_ID, completedTask.getRemoteAgent().getId());
        assertEquals(AgentTaskType.DELETE_PROPERTY.toString(), completedTask.getTaskType());
        assertEquals(RemoteTaskStatus.CLOSED.toString(), completedTask.getTaskStatus());
        assertEquals(CREATED_DATE, completedTask.getCreatedDate());
        assertEquals(COMPLETED_DATE, completedTask.getCompletedDate());
        assertNull(completedTask.getScheduledDate());
        assertEquals(RESULTS_FAILURE, completedTask.getResults());

        ArgumentCaptor<AsyncCallbackData> captor = ArgumentCaptor.forClass(AsyncCallbackData.class);
        verify(jobCallback).execute(captor.capture());
        AsyncCallbackData asyncCallbackData = captor.getValue();
        assertEquals(JOB_INSTANCE_ID, asyncCallbackData.getJobInstanceId());
        assertFalse(asyncCallbackData.wasSuccessful());
        assertTrue(((TetrisException) asyncCallbackData.getResponse()).getMessage().contains(RESULTS_FAILURE));
    }

    @Test
    public void test_taskFailed_UploadAgileRates_Not_A_LastRateCode() throws Exception {
        remoteTaskService.globalCrudService = crudService;
        RemoteTask task = new RemoteTask();
        task.setCreatedDate(CREATED_DATE);
        task.setId(TASK_ID);
        RemoteAgent agent = new RemoteAgent();
        agent.setId(REMOTE_AGENT_ID);
        task.setRemoteAgent(agent);
        task.setTaskClass(TaskClass.AGENT.toString());
        task.setTaskStatus(RemoteTaskStatus.IN_PROGRESS.toString());
        TaskParameter param1 = new TaskParameter();
        param1.setParameterType(ParameterType.PROPERTY_ID.toString());
        param1.setValue(String.valueOf(PROPERTY_ID));
        task.addParameter(param1);
        TaskParameter param2 = new TaskParameter();
        param2.setParameterType(ParameterType.JOB_INSTANCE_ID.toString());
        param2.setValue(String.valueOf(JOB_INSTANCE_ID));
        task.addParameter(param2);
        TaskParameter isLastRateCodeParameter = new TaskParameter();
        isLastRateCodeParameter.setParameterType(ParameterType.IS_LAST_RATE_CODE.toString());
        isLastRateCodeParameter.setValue(String.valueOf(Boolean.FALSE));
        task.addParameter(isLastRateCodeParameter);
        task.setTaskType(PropertyTaskType.UPLOAD_AGILE_RATES.toString());
        when(crudService.save(task)).thenReturn(task);
        RemoteTask completedTask = remoteTaskService.taskFailed(task, COMPLETED_DATE, RESULTS_FAILURE);

        assertEquals(TASK_ID, completedTask.getId());
        assertEquals(REMOTE_AGENT_ID, completedTask.getRemoteAgent().getId());
        assertEquals(PropertyTaskType.UPLOAD_AGILE_RATES.toString(), completedTask.getTaskType());
        assertEquals(RemoteTaskStatus.CLOSED.toString(), completedTask.getTaskStatus());
        assertEquals(CREATED_DATE, completedTask.getCreatedDate());
        assertEquals(COMPLETED_DATE, completedTask.getCompletedDate());
        assertNull(completedTask.getScheduledDate());
        assertEquals(RESULTS_FAILURE, completedTask.getResults());

        ArgumentCaptor<AsyncCallbackData> captor = ArgumentCaptor.forClass(AsyncCallbackData.class);
        verify(jobCallback).execute(captor.capture());
        verifyNoMoreInteractions(processDecisionStatus);
        AsyncCallbackData asyncCallbackData = captor.getValue();
        assertEquals(JOB_INSTANCE_ID, asyncCallbackData.getJobInstanceId());
        assertTrue(asyncCallbackData.wasSuccessful());
        assertTrue(((TetrisException) asyncCallbackData.getResponse()).getMessage().contains(RESULTS_FAILURE));
    }

    @Test
    public void test_taskFailed_UploadAgileRates_Is_LastRateCode() throws Exception {
        remoteTaskService.globalCrudService = crudService;
        RemoteTask task = new RemoteTask();
        task.setCreatedDate(CREATED_DATE);
        task.setId(TASK_ID);
        RemoteAgent agent = new RemoteAgent();
        agent.setId(REMOTE_AGENT_ID);
        task.setRemoteAgent(agent);
        task.setTaskClass(TaskClass.AGENT.toString());
        task.setTaskStatus(RemoteTaskStatus.IN_PROGRESS.toString());
        TaskParameter param1 = new TaskParameter();
        param1.setParameterType(ParameterType.PROPERTY_ID.toString());
        param1.setValue(String.valueOf(PROPERTY_ID));
        task.addParameter(param1);
        TaskParameter param2 = new TaskParameter();
        param2.setParameterType(ParameterType.JOB_INSTANCE_ID.toString());
        param2.setValue(String.valueOf(JOB_INSTANCE_ID));
        task.addParameter(param2);
        TaskParameter isLastRateCodeParameter = new TaskParameter();
        isLastRateCodeParameter.setParameterType(ParameterType.IS_LAST_RATE_CODE.toString());
        isLastRateCodeParameter.setValue(String.valueOf(Boolean.TRUE));
        task.addParameter(isLastRateCodeParameter);
        task.setTaskType(PropertyTaskType.UPLOAD_AGILE_RATES.toString());
        when(crudService.save(task)).thenReturn(task);
        RemoteTask completedTask = remoteTaskService.taskFailed(task, COMPLETED_DATE, RESULTS_FAILURE);

        assertEquals(TASK_ID, completedTask.getId());
        assertEquals(REMOTE_AGENT_ID, completedTask.getRemoteAgent().getId());
        assertEquals(PropertyTaskType.UPLOAD_AGILE_RATES.toString(), completedTask.getTaskType());
        assertEquals(RemoteTaskStatus.CLOSED.toString(), completedTask.getTaskStatus());
        assertEquals(CREATED_DATE, completedTask.getCreatedDate());
        assertEquals(COMPLETED_DATE, completedTask.getCompletedDate());
        assertNull(completedTask.getScheduledDate());
        assertEquals(RESULTS_FAILURE, completedTask.getResults());

        ArgumentCaptor<AsyncCallbackData> captor = ArgumentCaptor.forClass(AsyncCallbackData.class);
        verify(jobCallback).execute(captor.capture());
        verify(processDecisionStatus).fail(Constants.AGILE_RATES, null, RESULTS_FAILURE);
        AsyncCallbackData asyncCallbackData = captor.getValue();
        assertEquals(JOB_INSTANCE_ID, asyncCallbackData.getJobInstanceId());
        assertTrue(asyncCallbackData.wasSuccessful());
        assertTrue(((TetrisException) asyncCallbackData.getResponse()).getMessage().contains(RESULTS_FAILURE));
    }

    @Test
    public void test_taskCompleted_UploadAgileRates_Not_A_LastRateCode() throws Exception {
        remoteTaskService.globalCrudService = crudService;
        RemoteTask task = new RemoteTask();
        task.setCreatedDate(CREATED_DATE);
        task.setId(TASK_ID);
        RemoteAgent agent = new RemoteAgent();
        agent.setId(REMOTE_AGENT_ID);
        task.setRemoteAgent(agent);
        task.setTaskClass(TaskClass.AGENT.toString());
        task.setTaskStatus(RemoteTaskStatus.IN_PROGRESS.toString());
        TaskParameter param1 = new TaskParameter();
        param1.setParameterType(ParameterType.PROPERTY_ID.toString());
        param1.setValue(String.valueOf(PROPERTY_ID));
        task.addParameter(param1);
        TaskParameter param2 = new TaskParameter();
        param2.setParameterType(ParameterType.JOB_INSTANCE_ID.toString());
        param2.setValue(String.valueOf(JOB_INSTANCE_ID));
        task.addParameter(param2);
        TaskParameter isLastRateCodeParameter = new TaskParameter();
        isLastRateCodeParameter.setParameterType(ParameterType.IS_LAST_RATE_CODE.toString());
        isLastRateCodeParameter.setValue(String.valueOf(Boolean.FALSE));
        task.addParameter(isLastRateCodeParameter);
        task.setTaskType(PropertyTaskType.UPLOAD_AGILE_RATES.toString());
        when(crudService.save(task)).thenReturn(task);
        RemoteTask completedTask = remoteTaskService.taskCompleted(task, COMPLETED_DATE, RESULTS_SUCCESS);

        assertEquals(TASK_ID, completedTask.getId());
        assertEquals(REMOTE_AGENT_ID, completedTask.getRemoteAgent().getId());
        assertEquals(PropertyTaskType.UPLOAD_AGILE_RATES.toString(), completedTask.getTaskType());
        assertEquals(RemoteTaskStatus.COMPLETED.toString(), completedTask.getTaskStatus());
        assertEquals(CREATED_DATE, completedTask.getCreatedDate());
        assertEquals(COMPLETED_DATE, completedTask.getCompletedDate());
        assertNull(completedTask.getScheduledDate());
        assertEquals(RESULTS_SUCCESS, completedTask.getResults());

        ArgumentCaptor<AsyncCallbackData> captor = ArgumentCaptor.forClass(AsyncCallbackData.class);
        verify(jobCallback).execute(captor.capture());
        verifyNoMoreInteractions(processDecisionStatus);
        AsyncCallbackData asyncCallbackData = captor.getValue();
        assertEquals(JOB_INSTANCE_ID, asyncCallbackData.getJobInstanceId());
        assertTrue(asyncCallbackData.wasSuccessful());
        assertEquals(RESULTS_SUCCESS, asyncCallbackData.getResponse());
    }

    @Test
    public void test_taskCompleted_UploadAgileRates_Is_A_LastRateCode_Job_Suc() throws Exception {
        remoteTaskService.globalCrudService = crudService;
        RemoteTask task = new RemoteTask();
        task.setCreatedDate(CREATED_DATE);
        task.setId(TASK_ID);
        RemoteAgent agent = new RemoteAgent();
        agent.setId(REMOTE_AGENT_ID);
        task.setRemoteAgent(agent);
        task.setTaskClass(TaskClass.AGENT.toString());
        task.setTaskStatus(RemoteTaskStatus.IN_PROGRESS.toString());
        TaskParameter param1 = new TaskParameter();
        param1.setParameterType(ParameterType.PROPERTY_ID.toString());
        param1.setValue(String.valueOf(PROPERTY_ID));
        task.addParameter(param1);
        TaskParameter param2 = new TaskParameter();
        param2.setParameterType(ParameterType.JOB_INSTANCE_ID.toString());
        param2.setValue(String.valueOf(JOB_INSTANCE_ID));
        task.addParameter(param2);
        TaskParameter isLastRateCodeParameter = new TaskParameter();
        isLastRateCodeParameter.setParameterType(ParameterType.IS_LAST_RATE_CODE.toString());
        isLastRateCodeParameter.setValue(String.valueOf(Boolean.TRUE));
        task.addParameter(isLastRateCodeParameter);
        task.setTaskType(PropertyTaskType.UPLOAD_AGILE_RATES.toString());
        when(crudService.findByNamedQuerySingleResult(PropertyRemoteTask.COUNT_BY_PROPERTY_TASK_TYPE_STATUS_JOB_ID,
                QueryParameter.with("remoteAgentId", REMOTE_AGENT_ID).and("jobInstanceID", JOB_INSTANCE_ID).parameters())).thenReturn(2);
        when(crudService.save(task)).thenReturn(task);
        RemoteTask completedTask = remoteTaskService.taskCompleted(task, COMPLETED_DATE, RESULTS_SUCCESS);

        assertEquals(TASK_ID, completedTask.getId());
        assertEquals(REMOTE_AGENT_ID, completedTask.getRemoteAgent().getId());
        assertEquals(PropertyTaskType.UPLOAD_AGILE_RATES.toString(), completedTask.getTaskType());
        assertEquals(RemoteTaskStatus.COMPLETED.toString(), completedTask.getTaskStatus());
        assertEquals(CREATED_DATE, completedTask.getCreatedDate());
        assertEquals(COMPLETED_DATE, completedTask.getCompletedDate());
        assertNull(completedTask.getScheduledDate());
        assertEquals(RESULTS_SUCCESS, completedTask.getResults());

        ArgumentCaptor<AsyncCallbackData> captor = ArgumentCaptor.forClass(AsyncCallbackData.class);
        verify(jobCallback).execute(captor.capture());
        AsyncCallbackData asyncCallbackData = captor.getValue();
        assertEquals(JOB_INSTANCE_ID, asyncCallbackData.getJobInstanceId());
        assertTrue(asyncCallbackData.wasSuccessful());
        assertEquals(RESULTS_SUCCESS, asyncCallbackData.getResponse());
    }

    @Test
    public void test_taskCompleted_UploadAgileRates_Is_A_LastRateCode_Job_Success() throws Exception {
        remoteTaskService.globalCrudService = crudService;
        RemoteTask task = new RemoteTask();
        task.setCreatedDate(CREATED_DATE);
        task.setId(TASK_ID);
        RemoteAgent agent = new RemoteAgent();
        agent.setId(REMOTE_AGENT_ID);
        task.setRemoteAgent(agent);
        task.setTaskClass(TaskClass.AGENT.toString());
        task.setTaskStatus(RemoteTaskStatus.IN_PROGRESS.toString());
        TaskParameter param1 = new TaskParameter();
        param1.setParameterType(ParameterType.PROPERTY_ID.toString());
        param1.setValue(String.valueOf(PROPERTY_ID));
        task.addParameter(param1);
        TaskParameter param2 = new TaskParameter();
        param2.setParameterType(ParameterType.JOB_INSTANCE_ID.toString());
        param2.setValue(String.valueOf(JOB_INSTANCE_ID));
        task.addParameter(param2);
        TaskParameter isLastRateCodeParameter = new TaskParameter();
        isLastRateCodeParameter.setParameterType(ParameterType.IS_LAST_RATE_CODE.toString());
        isLastRateCodeParameter.setValue(String.valueOf(Boolean.TRUE));
        task.addParameter(isLastRateCodeParameter);
        task.setTaskType(PropertyTaskType.UPLOAD_AGILE_RATES.toString());
        when(crudService.findByNamedQuerySingleResult(PropertyRemoteTask.COUNT_BY_PROPERTY_TASK_TYPE_STATUS_JOB_ID,
                QueryParameter.with("remoteAgentId", REMOTE_AGENT_ID).and("jobInstanceID", JOB_INSTANCE_ID).parameters())).thenReturn(0);
        when(crudService.save(task)).thenReturn(task);
        RemoteTask completedTask = remoteTaskService.taskCompleted(task, COMPLETED_DATE, RESULTS_SUCCESS);

        assertEquals(TASK_ID, completedTask.getId());
        assertEquals(REMOTE_AGENT_ID, completedTask.getRemoteAgent().getId());
        assertEquals(PropertyTaskType.UPLOAD_AGILE_RATES.toString(), completedTask.getTaskType());
        assertEquals(RemoteTaskStatus.COMPLETED.toString(), completedTask.getTaskStatus());
        assertEquals(CREATED_DATE, completedTask.getCreatedDate());
        assertEquals(COMPLETED_DATE, completedTask.getCompletedDate());
        assertNull(completedTask.getScheduledDate());
        assertEquals(RESULTS_SUCCESS, completedTask.getResults());

        ArgumentCaptor<AsyncCallbackData> captor = ArgumentCaptor.forClass(AsyncCallbackData.class);
        verify(jobCallback).execute(captor.capture());
        AsyncCallbackData asyncCallbackData = captor.getValue();
        assertEquals(JOB_INSTANCE_ID, asyncCallbackData.getJobInstanceId());
        assertTrue(asyncCallbackData.wasSuccessful());
        assertEquals(RESULTS_SUCCESS, asyncCallbackData.getResponse());
    }

    @Test
    public void testPurgeRemoteTasks() {
        remoteTaskService.globalCrudService = crudService;
        when(crudService.executeUpdateByNamedQuery(anyString(), anyMapOf(String.class, Object.class))).thenReturn(23);
        final Integer daysToRetain = 10;
        when(configService.getValue(anyString(), anyString())).thenReturn(daysToRetain.toString());
        final LocalDateTime retainTimeThreshold = LocalDate.now().minusDays(daysToRetain).toLocalDateTime(LocalTime.MIDNIGHT);
        final Set<String> excludedStatuses = Stream.of(IN_PROGRESS, PENDING, FAILED).map(RemoteTaskStatus::toString).collect(Collectors.toSet());
        Map<String, Object> params = QueryParameter.with("retainDate", retainTimeThreshold).and("statusList", excludedStatuses).parameters();
        remoteTaskService.purgeRemoteTasks();
        verify(configService).getValue(anyString(), eq(GUIConfigParamName.REMOTE_AGENT_TASKS_DAYS_TO_RETAIN.value()));
        verify(crudService).executeUpdateByNamedQuery(eq(RemoteTask.DELETE_BY_DATE_AND_EXCLUDE_STATUS_LIST), eq(params));
    }

    @Test
    public void test_getPropertyRemoteTasks() {
        Property property = new Property();
        property.setStatus(Status.ACTIVE);
        when(propertyService.getPropertyById(PROPERTY_ID)).thenReturn(property);

        Set<String> statuses = Stream.of(RemoteTaskStatus.PENDING.toString()).collect(Collectors.toSet());
        PropertyRemoteTask task = getTestPropertyRemoteTask(PropertyTaskType.DOWNLOAD_FEED_DATA);
        when(multiPropertyCrudService.findByNamedQueryForSingleProperty(PROPERTY_ID, PropertyRemoteTask.BY_AGENT_AND_STATUS,
                QueryParameter.with("remoteAgentId", REMOTE_AGENT_ID).and("statuses", statuses).parameters())).thenReturn(Collections.singletonList(task));
        List<PropertyTask> results = remoteTaskService.getPropertyRemoteTasks(REMOTE_AGENT_ID, statuses);
        assertEquals(1, results.size());
        PropertyTask resultTask = results.get(0);
        assertEquals(TASK_ID.intValue(), resultTask.getTaskId());
        assertEquals(REMOTE_AGENT_ID.intValue(), resultTask.getAgentId());
        assertEquals(RemoteTaskStatus.IN_PROGRESS, resultTask.getStatus());
        assertEquals(PropertyTaskType.DOWNLOAD_FEED_DATA, resultTask.getType());
        verify(multiPropertyCrudService).findByNamedQueryForSingleProperty(PROPERTY_ID, PropertyRemoteTask.BY_AGENT_AND_STATUS,
                QueryParameter.with("remoteAgentId", REMOTE_AGENT_ID).and("statuses", statuses).parameters());
    }

    @Test
    public void test_getPropertyRemoteTasksPropertyDisabled() {
        Property property = new Property();
        property.setStatus(Status.MAINTENANCE);
        when(propertyService.getPropertyById(PROPERTY_ID)).thenReturn(property);
        Set<String> statuses = Stream.of(RemoteTaskStatus.PENDING.toString()).collect(Collectors.toSet());

        List<PropertyTask> results = remoteTaskService.getPropertyRemoteTasks(REMOTE_AGENT_ID, statuses);
        assertEquals(0, results.size());
        verifyZeroInteractions(multiPropertyCrudService);
    }

    @Test
    public void test_deletePropertyRemoteTask_pending() {
        PropertyTask task = new PropertyTask();
        task.setStatus(RemoteTaskStatus.PENDING);
        task.setTaskId(TASK_ID);
        task.setPropertyId(PROPERTY_ID);
        task.setType(PropertyTaskType.UPLOAD_DECISIONS);
        remoteTaskService.deletePropertyRemoteTask(task);
        ArgumentCaptor<PropertyRemoteTask> captor = ArgumentCaptor.forClass(PropertyRemoteTask.class);
        verify(multiPropertyCrudService).delete(eq(PROPERTY_ID), captor.capture());
        PropertyRemoteTask deletedTask = captor.getValue();
        assertEquals(task.getTaskId(), deletedTask.getId().intValue());
    }

    @Test
    public void test_abandonPropertyRemoteTask() {
        PacmanThreadLocalContextHolder.setPrincipal(null);
        PropertyTask task = new PropertyTask();
        task.setStatus(RemoteTaskStatus.IN_PROGRESS);
        task.setTaskId(TASK_ID);
        task.setType(PropertyTaskType.UPLOAD_DECISIONS);
        PropertyRemoteTask entity = getTestPropertyRemoteTask(PropertyTaskType.UPLOAD_DECISIONS);
        when(multiPropertyCrudService.save(any(), any(PropertyRemoteTask.class))).thenReturn(entity);
        remoteTaskService.abandonPropertyRemoteTask(task, PROPERTY_ID);
        ArgumentCaptor<PropertyRemoteTask> captor = ArgumentCaptor.forClass(PropertyRemoteTask.class);
        verify(multiPropertyCrudService).save(eq(PROPERTY_ID), captor.capture());
        PropertyRemoteTask savedTask = captor.getValue();
        assertEquals(task.getTaskId(), savedTask.getId().intValue());
        assertEquals(RemoteTaskStatus.ABANDONED.toString(), savedTask.getTaskStatus());
        assertNotNull(savedTask.getCompletedDate());
    }

    @Test
    public void test_getPropertyRemoteTasks_types() {
        Property property = new Property();
        property.setStatus(Status.ACTIVE);
        when(propertyService.getPropertyById(PROPERTY_ID)).thenReturn(property);
        Set<String> types = Stream.of(PropertyTaskType.DOWNLOAD_FEED_DATA.toString()).collect(Collectors.toSet());
        Set<String> statuses = Stream.of(RemoteTaskStatus.IN_PROGRESS.toString()).collect(Collectors.toSet());
        PropertyRemoteTask task = getTestPropertyRemoteTask(PropertyTaskType.DOWNLOAD_FEED_DATA);
        when(multiPropertyCrudService.findByNamedQueryForSingleProperty(PROPERTY_ID, PropertyRemoteTask.BY_TYPE_AND_STATUS,
                QueryParameter.with("types", types).and("statuses", statuses).parameters())).thenReturn(Collections.singletonList(task));
        List<PropertyTask> results = remoteTaskService.getPropertyRemoteTasks(types, statuses);
        assertEquals(1, results.size());
        PropertyTask resultTask = results.get(0);
        assertEquals(TASK_ID.intValue(), resultTask.getTaskId());
        assertEquals(REMOTE_AGENT_ID.intValue(), resultTask.getAgentId());
        assertEquals(RemoteTaskStatus.IN_PROGRESS, resultTask.getStatus());
        assertEquals(PropertyTaskType.DOWNLOAD_FEED_DATA, resultTask.getType());
        verify(multiPropertyCrudService).findByNamedQueryForSingleProperty(PROPERTY_ID, PropertyRemoteTask.BY_TYPE_AND_STATUS,
                QueryParameter.with("types", types).and("statuses", statuses).parameters());
    }

    @Test
    public void test_saveTask_tenant() {
        PropertyRemoteTask task = new PropertyRemoteTask();
        when(multiPropertyCrudService.save(PROPERTY_ID, task)).thenReturn(task);
        PropertyRemoteTask results = remoteTaskService.saveTask(task);
        assertEquals(task, results);
        verify(multiPropertyCrudService).save(PROPERTY_ID, task);
    }

    @Test
    public void test_taskFailed_tenant_notInProgress() {
        PropertyRemoteTask task = new PropertyRemoteTask();
        task.setId(TASK_ID);
        task.setTaskStatus(RemoteTaskStatus.PENDING.toString());
        try {
            remoteTaskService.propertyTaskFailed(task, COMPLETED_DATE, RESULTS_FAILURE, PROPERTY_ID);
            fail("Expected call to propertyTaskFailed to throw TaskNotInProgressException");
        } catch (TaskNotInProgressException e) {
            assertEquals(e.getMessage(), "Unable to fail task " + TASK_ID + " because its status is: " + RemoteTaskStatus.PENDING.toString());
        }
    }

    @Test
    public void test_taskFailed_tenant_noJobInstance() throws TaskNotInProgressException {
        PropertyRemoteTask task = getTestPropertyRemoteTask(PropertyTaskType.DOWNLOAD_FEED_DATA);
        when(multiPropertyCrudService.save(PROPERTY_ID, task)).thenReturn(task);
        PropertyRemoteTask result = remoteTaskService.propertyTaskFailed(task, COMPLETED_DATE, RESULTS_FAILURE, PROPERTY_ID);
        assertEquals(task, result);
        ArgumentCaptor<PropertyRemoteTask> taskCaptor = ArgumentCaptor.forClass(PropertyRemoteTask.class);
        verify(multiPropertyCrudService).save(eq(PROPERTY_ID), taskCaptor.capture());
        task = taskCaptor.getValue();
        assertEquals(COMPLETED_DATE, task.getCompletedDate());
        assertEquals(RESULTS_FAILURE, task.getResults());
        assertEquals(RemoteTaskStatus.FAILED.toString(), task.getTaskStatus());
        verifyNoMoreInteractions(processDecisionStatus);
    }

    @Test
    public void test_taskFailed_tenant_agileRate_noJobInstance() throws TaskNotInProgressException {
        PropertyRemoteTask task = getTestPropertyRemoteTask(PropertyTaskType.UPLOAD_AGILE_RATES);
        when(multiPropertyCrudService.save(PROPERTY_ID, task)).thenReturn(task);
        PropertyRemoteTask result = remoteTaskService.propertyTaskFailed(task, COMPLETED_DATE, RESULTS_FAILURE, PROPERTY_ID);
        assertEquals(task, result);
        ArgumentCaptor<PropertyRemoteTask> taskCaptor = ArgumentCaptor.forClass(PropertyRemoteTask.class);
        verify(multiPropertyCrudService).save(eq(PROPERTY_ID), taskCaptor.capture());
        task = taskCaptor.getValue();
        assertEquals(COMPLETED_DATE, task.getCompletedDate());
        assertEquals(RESULTS_FAILURE, task.getResults());
        assertEquals(RemoteTaskStatus.FAILED.toString(), task.getTaskStatus());
        verifyNoMoreInteractions(processDecisionStatus);
    }

    @Test
    public void test_taskFailed_lastAgileRate_noJobInstance() throws TaskNotInProgressException {
        String EXTERNAL_SYSTEM = "myExtSys";
        PropertyRemoteTask task = getTestPropertyRemoteTask(PropertyTaskType.UPLOAD_AGILE_RATES);
        task.addParameter(new PropertyTaskParameter(ParameterType.IS_LAST_RATE_CODE.toString(), "true"));
        task.addParameter(new PropertyTaskParameter(ParameterType.OPERA_SYS_REQ.toString(), EXTERNAL_SYSTEM));
        when(multiPropertyCrudService.find(PROPERTY_ID, PropertyRemoteTask.class, TASK_ID)).thenReturn(task);
        when(multiPropertyCrudService.save(PROPERTY_ID, task)).thenReturn(task);
        remoteTaskService.propertyTaskFailed(TASK_ID, COMPLETED_DATE, RESULTS_FAILURE);
        ArgumentCaptor<PropertyRemoteTask> taskCaptor = ArgumentCaptor.forClass(PropertyRemoteTask.class);
        verify(multiPropertyCrudService).save(eq(PROPERTY_ID), taskCaptor.capture());
        task = taskCaptor.getValue();
        assertEquals(COMPLETED_DATE, task.getCompletedDate());
        assertEquals(RESULTS_FAILURE, task.getResults());
        assertEquals(RemoteTaskStatus.FAILED.toString(), task.getTaskStatus());
        verify(processDecisionStatus).fail(Constants.AGILE_RATES, EXTERNAL_SYSTEM, RESULTS_FAILURE);
    }

    @Test
    public void test_taskFailed_lastAgileRate() throws TaskNotInProgressException {
        String EXTERNAL_SYSTEM = "myExtSys";
        PropertyRemoteTask task = getTestPropertyRemoteTask(PropertyTaskType.UPLOAD_AGILE_RATES);
        task.addParameter(new PropertyTaskParameter(ParameterType.IS_LAST_RATE_CODE.toString(), "true"));
        task.addParameter(new PropertyTaskParameter(ParameterType.OPERA_SYS_REQ.toString(), EXTERNAL_SYSTEM));
        task.addParameter(new PropertyTaskParameter(ParameterType.JOB_INSTANCE_ID.toString(), JOB_INSTANCE_ID.toString()));
        when(multiPropertyCrudService.find(PROPERTY_ID, PropertyRemoteTask.class, TASK_ID)).thenReturn(task);
        when(multiPropertyCrudService.save(PROPERTY_ID, task)).thenReturn(task);
        remoteTaskService.propertyTaskFailed(TASK_ID, COMPLETED_DATE, RESULTS_FAILURE);
        ArgumentCaptor<PropertyRemoteTask> taskCaptor = ArgumentCaptor.forClass(PropertyRemoteTask.class);
        verify(multiPropertyCrudService).save(eq(PROPERTY_ID), taskCaptor.capture());
        task = taskCaptor.getValue();
        assertEquals(COMPLETED_DATE, task.getCompletedDate());
        assertEquals(RESULTS_FAILURE, task.getResults());
        assertEquals(RemoteTaskStatus.CLOSED.toString(), task.getTaskStatus());
        verify(processDecisionStatus).fail(Constants.AGILE_RATES, EXTERNAL_SYSTEM, RESULTS_FAILURE);
    }

    @Test
    public void test_getPropertyRemoteTask() {
        PropertyRemoteTask task = getTestPropertyRemoteTask(PropertyTaskType.DOWNLOAD_FEED_DATA);
        when(multiPropertyCrudService.find(PROPERTY_ID, PropertyRemoteTask.class, TASK_ID)).thenReturn(task);
        PropertyTask result = remoteTaskService.getPropertyRemoteTask(TASK_ID);
        assertEquals(TASK_ID.intValue(), result.getTaskId());
        assertEquals(PropertyTaskType.DOWNLOAD_FEED_DATA, result.getType());
        assertEquals(REMOTE_AGENT_ID.intValue(), result.getAgentId());
        assertEquals(RemoteTaskStatus.IN_PROGRESS, result.getStatus());
        verify(multiPropertyCrudService).find(PROPERTY_ID, PropertyRemoteTask.class, TASK_ID);
    }

    @Test
    public void test_savePropertyRemoteTask() {
        PropertyTask task = new PropertyTask();
        task.setType(PropertyTaskType.DOWNLOAD_FEED_DATA);
        task.setTaskId(TASK_ID);
        task.setAgentId(REMOTE_AGENT_ID);
        task.setStatus(RemoteTaskStatus.IN_PROGRESS);
        task.setParameters(new HashSet<>());
        PropertyRemoteTask taskEntity = getTestPropertyRemoteTask(PropertyTaskType.DOWNLOAD_FEED_DATA);
        when(multiPropertyCrudService.save(any(), any(PropertyRemoteTask.class))).thenReturn(taskEntity);
        PropertyTask result = (PropertyTask) remoteTaskService.savePropertyRemoteTask(task);
        assertEquals(TASK_ID.intValue(), result.getTaskId());
        assertEquals(PropertyTaskType.DOWNLOAD_FEED_DATA, result.getType());
        assertEquals(REMOTE_AGENT_ID.intValue(), result.getAgentId());
        assertEquals(RemoteTaskStatus.IN_PROGRESS, result.getStatus());
        verify(multiPropertyCrudService).save(any(), any(PropertyRemoteTask.class));
    }

    @Test
    public void test_toTenantEntity() {
        PropertyTask dto = new PropertyTask();
        dto.setTaskId(TASK_ID);
        dto.setType(PropertyTaskType.UPLOAD_DECISIONS);
        dto.setStatus(RemoteTaskStatus.IN_PROGRESS);
        dto.setCompletedDate(COMPLETED_DATE);
        dto.setCreatedDate(CREATED_DATE);
        dto.setScheduledDate(SCHEDULED_DATE);
        dto.setResults(RESULTS_SUCCESS);
        dto.setAgentId(REMOTE_AGENT_ID);
        dto.addParameter(new TaskParameterDto(ParameterType.OPERA_SYS_REQ, "PMS"));
        PropertyRemoteTask result = remoteTaskService.toTenantEntity(dto);
        assertEquals(TASK_ID, result.getId());
        assertEquals(PropertyTaskType.UPLOAD_DECISIONS.toString(), result.getTaskType());
        assertEquals(RemoteTaskStatus.IN_PROGRESS.toString(), result.getTaskStatus());
        assertEquals(COMPLETED_DATE, result.getCompletedDate());
        assertEquals(CREATED_DATE, result.getCreatedDate());
        assertEquals(SCHEDULED_DATE, result.getScheduledDate());
        assertEquals(RESULTS_SUCCESS, result.getResults());
        assertEquals(REMOTE_AGENT_ID, result.getRemoteAgentId());
        assertEquals("PMS", result.getParameterValue(ParameterType.OPERA_SYS_REQ));
    }

    @Test
    public void test_propertyTaskCompleted() throws TaskNotInProgressException {
        PropertyRemoteTask taskEntity = getTestPropertyRemoteTask(PropertyTaskType.DOWNLOAD_FEED_DATA);
        when(multiPropertyCrudService.find(PROPERTY_ID, PropertyRemoteTask.class, TASK_ID)).thenReturn(taskEntity);
        when(multiPropertyCrudService.save(PROPERTY_ID, taskEntity)).thenReturn(taskEntity);
        PropertyTask result = (PropertyTask) remoteTaskService.propertyTaskCompleted(TASK_ID, COMPLETED_DATE, RESULTS_SUCCESS);
        assertEquals(TASK_ID.intValue(), result.getTaskId());
        assertEquals(PropertyTaskType.DOWNLOAD_FEED_DATA, result.getType());
        assertEquals(REMOTE_AGENT_ID.intValue(), result.getAgentId());
        assertEquals(RemoteTaskStatus.COMPLETED, result.getStatus());
        assertEquals(COMPLETED_DATE, result.getCompletedDate());
        assertEquals(RESULTS_SUCCESS, result.getResults());
        verify(multiPropertyCrudService).save(PROPERTY_ID, taskEntity);
        verify(multiPropertyCrudService).find(PROPERTY_ID, PropertyRemoteTask.class, TASK_ID);
    }

    @Test
    public void test_propertyTaskFailed() throws TaskNotInProgressException {
        PropertyRemoteTask task = getTestPropertyRemoteTask(PropertyTaskType.UPLOAD_DECISIONS);
        when(multiPropertyCrudService.find(PROPERTY_ID, PropertyRemoteTask.class, TASK_ID)).thenReturn(task);
        when(multiPropertyCrudService.save(PROPERTY_ID, task)).thenReturn(task);
        RemoteTaskDto result = remoteTaskService.propertyTaskFailed(TASK_ID, COMPLETED_DATE, RESULTS_FAILURE);
        assertEquals(TASK_ID.intValue(), result.getTaskId());
        assertEquals(REMOTE_AGENT_ID.intValue(), result.getAgentId());
        assertEquals(RemoteTaskStatus.FAILED, result.getStatus());
        verify(multiPropertyCrudService).find(PROPERTY_ID, PropertyRemoteTask.class, TASK_ID);
        ArgumentCaptor<PropertyRemoteTask> taskCaptor = ArgumentCaptor.forClass(PropertyRemoteTask.class);
        verify(multiPropertyCrudService).save(eq(PROPERTY_ID), taskCaptor.capture());
        task = taskCaptor.getValue();
        assertEquals(COMPLETED_DATE, task.getCompletedDate());
        assertEquals(RESULTS_FAILURE, task.getResults());
        assertEquals(RemoteTaskStatus.FAILED.toString(), task.getTaskStatus());
    }

    @Test
    public void test_closePropertyRemoteTasks() {
        String USER_NAME = "myUser";
        TetrisPrincipal principal = new TetrisPrincipal();
        principal.setDisplayName(USER_NAME);
        PacmanThreadLocalContextHolder.setPrincipal(principal);
        PropertyRemoteTask entity = new PropertyRemoteTask();
        entity.setId(TASK_ID);
        entity.setTaskStatus(RemoteTaskStatus.FAILED.toString());
        when(multiPropertyCrudService.find(PROPERTY_ID, PropertyRemoteTask.class, TASK_ID)).thenReturn(entity);
        remoteTaskService.closePropertyRemoteTasks(Stream.of(TASK_ID).collect(Collectors.toSet()));
        assertEquals(RemoteTaskStatus.CLOSED.toString(), entity.getTaskStatus());
        verify(multiPropertyCrudService).find(PROPERTY_ID, PropertyRemoteTask.class, TASK_ID);
        ArgumentCaptor<Object> saveCaptor = ArgumentCaptor.forClass(Object.class);
        verify(multiPropertyCrudService, times(3)).save(eq(PROPERTY_ID), (PropertyTaskParameter) saveCaptor.capture());
        List<Object> savedEntities = saveCaptor.getAllValues();
        assertEquals(3, savedEntities.size());
        PropertyTaskParameter parameter = (PropertyTaskParameter) savedEntities.get(0);
        assertEquals(ParameterType.CLOSED_BY.toString(), parameter.getParameterType());
        assertEquals(USER_NAME, parameter.getValue());
        parameter = (PropertyTaskParameter) savedEntities.get(1);
        assertEquals(ParameterType.CLOSED_DATE.toString(), parameter.getParameterType());
        assertNotNull(parameter.getValue());
        assertEquals(entity, savedEntities.get(2));
    }

    /**
     * Verify that RemoteTaskService.createDataFeedTask() populates ParameterType.MKT_SEGMENTS_TO_INCL
     * with straight market segments when IntegrationConfigParamName.FILTER_MARKET_SEGMENT_SUMMARY is true
     * and feedType is OperaIncomingFile.CURRENT_SEGMENT_AND_ROOM_TYPE_SUMMARY
     */
    @Test
    public void test_marketSegmentFilter() {
        MktSeg mktSeg1 = new MktSeg();
        mktSeg1.setCode("TEST1");
        MktSeg mktSeg2 = new MktSeg();
        mktSeg2.setCode("TEST2");
        Set<MktSeg> testStraightMarketSegments = new HashSet<>();
        testStraightMarketSegments.add(mktSeg1);
        testStraightMarketSegments.add(mktSeg2);
        when(marketSegmentComponent.findStraightMarketSegments()).thenReturn(testStraightMarketSegments);
        String expectedMarketSegmentsToInclude = "TEST1,TEST2";

        // Make mock crud service attach a task ID to avoid NPE.
        when(multiPropertyCrudService.save(anyInt(), any(PropertyRemoteTask.class))).thenAnswer(i -> {
            PropertyRemoteTask test = (PropertyRemoteTask) i.getArguments()[1];
            test.setId(TASK_ID);
            return test;
        });
        when(configService.getParameterValue(IntegrationConfigParamName.FILTER_MARKET_SEGMENT_SUMMARY, OPERA_AGENT)).thenReturn(true);
        when(configService.getParameterValue(GUIConfigParamName.POPULATION_ANALYTICAL_MARKET_SEGMENT_MAPPING_COMPLETE)).thenReturn("true");

        PropertyTask taskShouldFilterMS = remoteTaskService.createDataFeedTask(REMOTE_AGENT_ID, PROPERTY_ID, OperaIncomingFile.CURRENT_SEGMENT_AND_ROOM_TYPE_SUMMARY, CORRELATION_ID, SCHEDULED_DATE, 0, 0, 0, JOB_INSTANCE_ID, false);
        List actualMarketSegmentsToInclude = taskShouldFilterMS.getParameters().stream().filter(dto -> dto.getType().equals(ParameterType.MKT_SEGMENTS_TO_INCL)).map(TaskParameterDto::getValue).collect(Collectors.toList());

        assertEquals(1, actualMarketSegmentsToInclude.size());
        assertEquals(expectedMarketSegmentsToInclude, actualMarketSegmentsToInclude.get(0));

        PropertyTask taskShouldNotFilterMS = remoteTaskService.createDataFeedTask(REMOTE_AGENT_ID, PROPERTY_ID, OperaIncomingFile.PAST_SEGMENT_AND_ROOM_TYPE_SUMMARY, CORRELATION_ID, SCHEDULED_DATE, 0, 0, 0, JOB_INSTANCE_ID, false);
        List actualNoMarketSegmentsToInclude = taskShouldNotFilterMS.getParameters().stream().filter(dto -> dto.getType().equals(ParameterType.MKT_SEGMENTS_TO_INCL)).map(TaskParameterDto::getValue).collect(Collectors.toList());

        assertEquals(0, actualNoMarketSegmentsToInclude.size());
    }

    /**
     * Make sure we don't reference a ParameterType that may not exist on the agent yet (since they're not all up to date).
     */
    @Test
    public void test_createDataFeedTask_backwards_compatible() {
        // Make mock crud service attach a task ID to avoid NPE.
        when(multiPropertyCrudService.save(anyInt(), any(PropertyRemoteTask.class))).thenAnswer(i -> {
            PropertyRemoteTask test = (PropertyRemoteTask) i.getArguments()[1];
            test.setId(TASK_ID);
            return test;
        });
        when(configService.getParameterValue(IntegrationConfigParamName.FILTER_MARKET_SEGMENT_SUMMARY, OPERA_AGENT)).thenReturn("false");
        when(configService.getParameterValue(GUIConfigParamName.POPULATION_ANALYTICAL_MARKET_SEGMENT_MAPPING_COMPLETE)).thenReturn("false");

        for (boolean testValue : Arrays.asList(true, false)) {
            // given
            when(configService.getBooleanParameterValue(RemoteAgentConfigParamName.EXCLUDE_ZERO_GROUP_BLOCK_PICKUP.value(Constants.OPERA))).thenReturn(testValue);
            // when
            PropertyTask actual = remoteTaskService.createDataFeedTask(REMOTE_AGENT_ID, PROPERTY_ID, OperaIncomingFile.CURRENT_SEGMENT_AND_ROOM_TYPE_SUMMARY, CORRELATION_ID, SCHEDULED_DATE, 0, 0, 0, JOB_INSTANCE_ID, testValue);
            // then
            assertEquals(PROPERTY_ID, actual.getPropertyId());
            assertEquals(REMOTE_AGENT_ID, Integer.valueOf(actual.getAgentId()));
            assertNotNull(actual.getCreatedDate());
            assertEquals(PENDING, actual.getStatus());
            assertEquals(SCHEDULED_DATE, actual.getScheduledDate());
            assertEquals(testValue, Boolean.parseBoolean((String) actual.getParameterValue(ParameterType.EXCLUDE_ZERO_GROUP)));
            assertEquals(testValue, Boolean.parseBoolean((String) actual.getParameterValue(ParameterType.OVRRIDE_BUSINESS_DT)));
        }
    }

    private PropertyRemoteTask getTestPropertyRemoteTask(PropertyTaskType propertyTaskType) {
        PropertyRemoteTask task = new PropertyRemoteTask();
        task.setTaskType(propertyTaskType.toString());
        task.setId(TASK_ID);
        task.setRemoteAgentId(REMOTE_AGENT_ID);
        task.setTaskStatus(RemoteTaskStatus.IN_PROGRESS.toString());
        task.setParameters(new ArrayList<>());
        return task;
    }

    @Test
    public void test_getRemoteAgents() {
        remoteTaskService.globalCrudService = crudService;
        List<Integer> remoteAgentIdList = getRemoteAgents();
        when(crudService.<Integer>findByNamedQuery(RemoteAgent.SELECT_REMOTE_AGENT_ID)).thenReturn(remoteAgentIdList);
        List<Integer> remoteAgents = remoteTaskService.getRemoteAgents();
        assertEquals(2, remoteAgents.size());
    }

    @Test
    public void test_getRemoteAgents_null() {
        remoteTaskService.globalCrudService = crudService;
        when(crudService.<Integer>findByNamedQuery(RemoteAgent.SELECT_REMOTE_AGENT_ID)).thenReturn(null);
        List<Integer> remoteAgents = remoteTaskService.getRemoteAgents();
        assertNull(remoteAgents);
    }

    private List<Integer> getRemoteAgents() {
        List<Integer> remoteAgentIdList = new ArrayList<>();
        remoteAgentIdList.add(1);
        remoteAgentIdList.add(2);
        return remoteAgentIdList;
    }

    @Test
    public void test_getPropertyRemoteTasks_byAgentIds() {
        Property property = new Property();
        property.setStatus(Status.ACTIVE);
        when(propertyService.getPropertyById(PROPERTY_ID)).thenReturn(property);
        Set<String> types = Stream.of(PropertyTaskType.DOWNLOAD_FEED_DATA.toString()).collect(Collectors.toSet());
        Set<String> statuses = Stream.of(RemoteTaskStatus.IN_PROGRESS.toString()).collect(Collectors.toSet());
        Set<Integer> agents = Stream.of(1, 2, 3).collect(Collectors.toSet());
        PropertyRemoteTask task = getTestPropertyRemoteTask(PropertyTaskType.DOWNLOAD_FEED_DATA);
        when(multiPropertyCrudService.findByNamedQueryForSingleProperty(PROPERTY_ID, PropertyRemoteTask.BY_TYPE_STATUS_AND_AGENT_IDS,
                QueryParameter.with("types", types).and("statuses", statuses).and("agentIds", agents).parameters())).thenReturn(Collections.singletonList(task));
        List<PropertyTask> results = remoteTaskService.getPropertyRemoteTasks(types, statuses, agents);
        assertEquals(1, results.size());
        PropertyTask resultTask = results.get(0);
        assertEquals(TASK_ID.intValue(), resultTask.getTaskId());
        assertEquals(REMOTE_AGENT_ID.intValue(), resultTask.getAgentId());
        assertEquals(RemoteTaskStatus.IN_PROGRESS, resultTask.getStatus());
        assertEquals(PropertyTaskType.DOWNLOAD_FEED_DATA, resultTask.getType());
        verify(multiPropertyCrudService).findByNamedQueryForSingleProperty(PROPERTY_ID, PropertyRemoteTask.BY_TYPE_STATUS_AND_AGENT_IDS,
                QueryParameter.with("types", types).and("statuses", statuses).and("agentIds", agents).parameters());
    }

    @Test
    public void createAddPropertyTask() {
        remoteTaskService.globalCrudService = crudService;
        ArgumentCaptor<RemoteTask> remoteTaskCaptor = ArgumentCaptor.forClass(RemoteTask.class);

        Client client = new Client();
        client.setCode("clnt");

        RemoteAgent remoteAgent = new RemoteAgent();
        remoteAgent.setId(3);
        remoteAgent.setClient(client);

        Property property = new Property();
        property.setId(12);
        property.setName("prop1");
        property.setClient(client);

        remoteTaskService.createAddPropertyTask(remoteAgent, property);

        verify(crudService).save(remoteTaskCaptor.capture());
        RemoteTask task = remoteTaskCaptor.getValue();
        assertNotNull(task);
        assertEquals("PENDING", task.getTaskStatus());
        assertEquals("ADD_PROPERTY", task.getTaskType());
        assertEquals("12", task.getParameterValue(ParameterType.PROPERTY_ID));
        assertEquals("prop1", task.getParameterValue(ParameterType.PROPERTY_NAME));
        assertNull(task.getParameterValue(ParameterType.CLIENT_CODE));
    }

    @Test
    public void createAddPropertyTask_MultiClient() {
        remoteTaskService.globalCrudService = crudService;
        ArgumentCaptor<RemoteTask> remoteTaskCaptor = ArgumentCaptor.forClass(RemoteTask.class);

        Client client = new Client();
        client.setCode("clnt");

        RemoteAgent remoteAgent = new RemoteAgent();
        remoteAgent.setId(3);

        Property property = new Property();
        property.setId(12);
        property.setName("prop1");
        property.setClient(client);

        remoteTaskService.createAddPropertyTask(remoteAgent, property);

        verify(crudService).save(remoteTaskCaptor.capture());
        RemoteTask task = remoteTaskCaptor.getValue();
        assertNotNull(task);
        assertEquals("PENDING", task.getTaskStatus());
        assertEquals("ADD_PROPERTY", task.getTaskType());
        assertEquals("12", task.getParameterValue(ParameterType.PROPERTY_ID));
        assertEquals("prop1", task.getParameterValue(ParameterType.PROPERTY_NAME));
        assertEquals("clnt", task.getParameterValue(ParameterType.CLIENT_CODE));
    }
}
