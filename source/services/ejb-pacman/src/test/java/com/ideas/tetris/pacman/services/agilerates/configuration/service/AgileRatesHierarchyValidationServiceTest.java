package com.ideas.tetris.pacman.services.agilerates.configuration.service;

import com.ideas.g3.test.AbstractG3JupiterTest;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomClass;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomType;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.CPConfigOffsetAccomType;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.OccupancyType;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.OffsetMethod;
import com.ideas.tetris.pacman.services.pricingconfiguration.entity.PricingAccomClass;
import com.ideas.tetris.pacman.services.pricingconfiguration.entity.PricingBaseAccomType;
import com.ideas.tetris.pacman.services.pricingconfiguration.entity.TransientPricingBaseAccomType;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import org.apache.commons.lang3.tuple.Pair;
import org.joda.time.LocalDate;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static com.ideas.tetris.pacman.common.constants.Constants.PROPERTY_ID;
import static java.util.stream.Collectors.groupingBy;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.when;

@MockitoSettings(strictness = Strictness.LENIENT)
public class AgileRatesHierarchyValidationServiceTest extends AbstractG3JupiterTest {

    @Mock
    CrudService tenantCrudService;

    @InjectMocks
    AgileRatesHierarchyValidationService agileRatesHierarchyValidationService;

    @Test
    public void isHierarchyBetweenIPsValidTest_ceilingFloor_defaultsToDefaults_valid() {
        AccomClass ac1 = createAccomClass(1, "AC1");
        AccomType at1 = createAccomType(1, "AT1", ac1);
        AccomType at2 = createAccomType(2, "AT2", ac1);
        ac1.setAccomTypes(Set.of(at1, at2));

        AccomClass ac2 = createAccomClass(2, "AC2");
        AccomType at3 = createAccomType(3, "AT3", ac2);
        AccomType at4 = createAccomType(4, "AT4", ac2);
        ac2.setAccomTypes(Set.of(at3, at4));
        PricingAccomClass pricingAccomClass1 = createPricingAccomClass(1, at1, ac1);
        PricingAccomClass pricingAccomClass2 = createPricingAccomClass(2, at3, ac2);


        PricingBaseAccomType selectedPricingBaseAccomType1 = createPricingBaseAccomType(
                BigDecimal.valueOf(100), BigDecimal.valueOf(20), at1, null, null);
        PricingBaseAccomType selectedPricingBaseAccomType2 = createPricingBaseAccomType(
                BigDecimal.valueOf(150), BigDecimal.valueOf(70), at3, null, null);

        PricingBaseAccomType relatedPricingBaseAccomType1 = createPricingBaseAccomType(
                BigDecimal.valueOf(120), BigDecimal.valueOf(40), at1, null, null);
        PricingBaseAccomType relatedPricingBaseAccomType2 = createPricingBaseAccomType(
                BigDecimal.valueOf(170), BigDecimal.valueOf(90), at3, null, null);
        Set<String> warnings = new HashSet<>();
        when(tenantCrudService.findByNamedQuery(PricingAccomClass.FIND_BY_PROPERTY_ID,
                QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).parameters()))
                .thenReturn(List.of(pricingAccomClass1, pricingAccomClass2));
        assertTrue(agileRatesHierarchyValidationService.isHierarchyBetweenIPsValid(
                groupCeilingFloorValues(List.of(selectedPricingBaseAccomType1, selectedPricingBaseAccomType2)),
                groupCeilingFloorValues(List.of(relatedPricingBaseAccomType1, relatedPricingBaseAccomType2)),
                new HashMap<>(),
                new HashMap<>(),
                new HashSet<>(),
                BigDecimal.ZERO,
                warnings,
                c -> true));
    }

    @Test
    public void isHierarchyBetweenIPsValidTest_ceilingFloor_defaultsToDefaults_invalid() {
        AccomClass ac1 = createAccomClass(1, "AC1");
        AccomType at1 = createAccomType(1, "AT1", ac1);
        AccomType at2 = createAccomType(2, "AT2", ac1);
        ac1.setAccomTypes(Set.of(at1, at2));

        AccomClass ac2 = createAccomClass(2, "AC2");
        AccomType at3 = createAccomType(3, "AT3", ac2);
        AccomType at4 = createAccomType(4, "AT4", ac2);
        ac2.setAccomTypes(Set.of(at3, at4));
        PricingAccomClass pricingAccomClass1 = createPricingAccomClass(1, at1, ac1);
        PricingAccomClass pricingAccomClass2 = createPricingAccomClass(2, at3, ac2);

        PricingBaseAccomType selectedPricingBaseAccomType1 = createPricingBaseAccomType(
                BigDecimal.valueOf(100), BigDecimal.valueOf(20), at1, null, null);
        PricingBaseAccomType selectedPricingBaseAccomType2 = createPricingBaseAccomType(
                BigDecimal.valueOf(150), BigDecimal.valueOf(70), at3, null, null);

        PricingBaseAccomType relatedPricingBaseAccomType1 = createPricingBaseAccomType(
                BigDecimal.valueOf(120), BigDecimal.valueOf(40), at1, null, null);
        PricingBaseAccomType relatedPricingBaseAccomType2 = createPricingBaseAccomType(
                BigDecimal.valueOf(140), BigDecimal.valueOf(90), at3, null, null);
        Set<String> warnings = new HashSet<>();
        when(tenantCrudService.findByNamedQuery(PricingAccomClass.FIND_BY_PROPERTY_ID,
                QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).parameters()))
                .thenReturn(List.of(pricingAccomClass1, pricingAccomClass2));
        assertFalse(agileRatesHierarchyValidationService.isHierarchyBetweenIPsValid(
                groupCeilingFloorValues(List.of(selectedPricingBaseAccomType1, selectedPricingBaseAccomType2)),
                groupCeilingFloorValues(List.of(relatedPricingBaseAccomType1, relatedPricingBaseAccomType2)),
                new HashMap<>(),
                new HashMap<>(),
                new HashSet<>(),
                BigDecimal.ZERO,
                warnings,
                c -> true
        ));
    }

    @Test
    public void isHierarchyBetweenIPsValidTest_ceilingFloorAndOffsets_defaultsToDefaults_valid() {
        AccomClass ac1 = createAccomClass(1, "AC1");
        AccomType at1 = createAccomType(1, "AT1", ac1);
        AccomType at2 = createAccomType(2, "AT2", ac1);
        ac1.setAccomTypes(Set.of(at1, at2));

        AccomClass ac2 = createAccomClass(2, "AC2");
        AccomType at3 = createAccomType(3, "AT3", ac2);
        AccomType at4 = createAccomType(4, "AT4", ac2);
        ac2.setAccomTypes(Set.of(at3, at4));
        PricingAccomClass pricingAccomClass1 = createPricingAccomClass(1, at1, ac1);
        PricingAccomClass pricingAccomClass2 = createPricingAccomClass(2, at3, ac2);

        PricingBaseAccomType selectedPricingBaseAccomType1 = createPricingBaseAccomType(
                BigDecimal.valueOf(100), BigDecimal.valueOf(20), at1, null, null);
        PricingBaseAccomType selectedPricingBaseAccomType2 = createPricingBaseAccomType(
                BigDecimal.valueOf(150), BigDecimal.valueOf(70), at3, null, null);
        CPConfigOffsetAccomType selectedCPConfigOffsetAccomType1 = createCPConfigOffsetAccomType(
                OffsetMethod.FIXED_OFFSET, at2, BigDecimal.valueOf(15), OccupancyType.SINGLE, null, null);

        PricingBaseAccomType relatedPricingBaseAccomType1 = createPricingBaseAccomType(
                BigDecimal.valueOf(120), BigDecimal.valueOf(40), at1, null, null);
        PricingBaseAccomType relatedPricingBaseAccomType2 = createPricingBaseAccomType(
                BigDecimal.valueOf(170), BigDecimal.valueOf(90), at3, null, null);
        Set<String> warnings = new HashSet<>();
        when(tenantCrudService.findByNamedQuery(PricingAccomClass.FIND_BY_PROPERTY_ID,
                QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).parameters()))
                .thenReturn(List.of(pricingAccomClass1, pricingAccomClass2));
        assertTrue(agileRatesHierarchyValidationService.isHierarchyBetweenIPsValid(
                groupCeilingFloorValues(List.of(selectedPricingBaseAccomType1, selectedPricingBaseAccomType2)),
                groupCeilingFloorValues(List.of(relatedPricingBaseAccomType1, relatedPricingBaseAccomType2)),
                groupOffsetsValues(List.of(selectedCPConfigOffsetAccomType1)),
                new HashMap<>(),
                new HashSet<>(),
                BigDecimal.ZERO,
                warnings,
                c -> true
        ));
    }

    @Test
    public void isHierarchyBetweenIPsValidTest_ceilingFloorAndOffsets_defaultsToDefaults_invalid() {
        AccomClass ac1 = createAccomClass(1, "AC1");
        AccomType at1 = createAccomType(1, "AT1", ac1);
        AccomType at2 = createAccomType(2, "AT2", ac1);
        ac1.setAccomTypes(Set.of(at1, at2));

        AccomClass ac2 = createAccomClass(2, "AC2");
        AccomType at3 = createAccomType(3, "AT3", ac2);
        AccomType at4 = createAccomType(4, "AT4", ac2);
        ac2.setAccomTypes(Set.of(at3, at4));
        PricingAccomClass pricingAccomClass1 = createPricingAccomClass(1, at1, ac1);
        PricingAccomClass pricingAccomClass2 = createPricingAccomClass(2, at3, ac2);

        PricingBaseAccomType selectedPricingBaseAccomType1 = createPricingBaseAccomType(
                BigDecimal.valueOf(100), BigDecimal.valueOf(20), at1, null, null);
        PricingBaseAccomType selectedPricingBaseAccomType2 = createPricingBaseAccomType(
                BigDecimal.valueOf(150), BigDecimal.valueOf(70), at3, null, null);
        CPConfigOffsetAccomType selectedCPConfigOffsetAccomType1 = createCPConfigOffsetAccomType(
                OffsetMethod.FIXED_OFFSET, at2, BigDecimal.valueOf(21), OccupancyType.SINGLE, null, null);

        PricingBaseAccomType relatedPricingBaseAccomType1 = createPricingBaseAccomType(
                BigDecimal.valueOf(120), BigDecimal.valueOf(40), at1, null, null);
        PricingBaseAccomType relatedPricingBaseAccomType2 = createPricingBaseAccomType(
                BigDecimal.valueOf(170), BigDecimal.valueOf(90), at3, null, null);
        Set<String> warnings = new HashSet<>();
        when(tenantCrudService.findByNamedQuery(PricingAccomClass.FIND_BY_PROPERTY_ID,
                QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).parameters()))
                .thenReturn(List.of(pricingAccomClass1, pricingAccomClass2));
        assertFalse(agileRatesHierarchyValidationService.isHierarchyBetweenIPsValid(
                groupCeilingFloorValues(List.of(selectedPricingBaseAccomType1, selectedPricingBaseAccomType2)),
                groupCeilingFloorValues(List.of(relatedPricingBaseAccomType1, relatedPricingBaseAccomType2)),
                groupOffsetsValues(List.of(selectedCPConfigOffsetAccomType1)),
                new HashMap<>(),
                new HashSet<>(),
                BigDecimal.ZERO,
                warnings,
                c -> true
        ));
    }

    @Test
    public void isHierarchyBetweenIPsValidTest_ceilingFloorAndOffsets_defaultsToDefaults_valid2() {
        AccomClass ac1 = createAccomClass(1, "AC1");
        AccomType at1 = createAccomType(1, "AT1", ac1);
        AccomType at2 = createAccomType(2, "AT2", ac1);
        ac1.setAccomTypes(Set.of(at1, at2));

        AccomClass ac2 = createAccomClass(2, "AC2");
        AccomType at3 = createAccomType(3, "AT3", ac2);
        AccomType at4 = createAccomType(4, "AT4", ac2);
        ac2.setAccomTypes(Set.of(at3, at4));
        PricingAccomClass pricingAccomClass1 = createPricingAccomClass(1, at1, ac1);
        PricingAccomClass pricingAccomClass2 = createPricingAccomClass(2, at3, ac2);

        PricingBaseAccomType selectedPricingBaseAccomType1 = createPricingBaseAccomType(
                BigDecimal.valueOf(100), BigDecimal.valueOf(20), at1, null, null);
        PricingBaseAccomType selectedPricingBaseAccomType2 = createPricingBaseAccomType(
                BigDecimal.valueOf(150), BigDecimal.valueOf(70), at3, null, null);
        CPConfigOffsetAccomType selectedCPConfigOffsetAccomType1 = createCPConfigOffsetAccomType(
                OffsetMethod.FIXED_OFFSET, at2, BigDecimal.valueOf(21), OccupancyType.SINGLE, null, null);
        CPConfigOffsetAccomType selectedCPConfigOffsetAccomType2 = createCPConfigOffsetAccomType(
                OffsetMethod.FIXED_OFFSET, at4, BigDecimal.valueOf(21), OccupancyType.SINGLE, null, null);

        PricingBaseAccomType relatedPricingBaseAccomType1 = createPricingBaseAccomType(
                BigDecimal.valueOf(120), BigDecimal.valueOf(40), at1, null, null);
        PricingBaseAccomType relatedPricingBaseAccomType2 = createPricingBaseAccomType(
                BigDecimal.valueOf(170), BigDecimal.valueOf(90), at3, null, null);
        CPConfigOffsetAccomType relatedCPConfigOffsetAccomType1 = createCPConfigOffsetAccomType(
                OffsetMethod.FIXED_OFFSET, at2, BigDecimal.valueOf(2), OccupancyType.SINGLE, null, null);
        CPConfigOffsetAccomType relatedCPConfigOffsetAccomType2 = createCPConfigOffsetAccomType(
                OffsetMethod.FIXED_OFFSET, at4, BigDecimal.valueOf(2), OccupancyType.SINGLE, null, null);

        Set<String> warnings = new HashSet<>();
        when(tenantCrudService.findByNamedQuery(PricingAccomClass.FIND_BY_PROPERTY_ID,
                QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).parameters()))
                .thenReturn(List.of(pricingAccomClass1, pricingAccomClass2));
        assertTrue(agileRatesHierarchyValidationService.isHierarchyBetweenIPsValid(
                groupCeilingFloorValues(List.of(selectedPricingBaseAccomType1, selectedPricingBaseAccomType2)),
                groupCeilingFloorValues(List.of(relatedPricingBaseAccomType1, relatedPricingBaseAccomType2)),
                groupOffsetsValues(List.of(selectedCPConfigOffsetAccomType1)),
                groupOffsetsValues(List.of(relatedCPConfigOffsetAccomType1)),
                new HashSet<>(),
                BigDecimal.ZERO,
                warnings,
                c -> true
        ));
    }

    @Test
    public void isHierarchyBetweenIPsValidTest_ceilingFloorAndOffsets_defaultsToDefaults_invalid2() {
        AccomClass ac1 = createAccomClass(1, "AC1");
        AccomType at1 = createAccomType(1, "AT1", ac1);
        AccomType at2 = createAccomType(2, "AT2", ac1);
        ac1.setAccomTypes(Set.of(at1, at2));

        AccomClass ac2 = createAccomClass(2, "AC2");
        AccomType at3 = createAccomType(3, "AT3", ac2);
        AccomType at4 = createAccomType(4, "AT4", ac2);
        ac2.setAccomTypes(Set.of(at3, at4));
        PricingAccomClass pricingAccomClass1 = createPricingAccomClass(1, at1, ac1);
        PricingAccomClass pricingAccomClass2 = createPricingAccomClass(2, at3, ac2);

        PricingBaseAccomType selectedPricingBaseAccomType1 = createPricingBaseAccomType(
                BigDecimal.valueOf(100), BigDecimal.valueOf(20), at1, null, null);
        PricingBaseAccomType selectedPricingBaseAccomType2 = createPricingBaseAccomType(
                BigDecimal.valueOf(150), BigDecimal.valueOf(70), at3, null, null);
        CPConfigOffsetAccomType selectedCPConfigOffsetAccomType1 = createCPConfigOffsetAccomType(
                OffsetMethod.FIXED_OFFSET, at2, BigDecimal.valueOf(21), OccupancyType.SINGLE, null, null);
        CPConfigOffsetAccomType selectedCPConfigOffsetAccomType2 = createCPConfigOffsetAccomType(
                OffsetMethod.FIXED_OFFSET, at4, BigDecimal.valueOf(21), OccupancyType.SINGLE, null, null);

        PricingBaseAccomType relatedPricingBaseAccomType1 = createPricingBaseAccomType(
                BigDecimal.valueOf(120), BigDecimal.valueOf(40), at1, null, null);
        PricingBaseAccomType relatedPricingBaseAccomType2 = createPricingBaseAccomType(
                BigDecimal.valueOf(170), BigDecimal.valueOf(90), at3, null, null);
        CPConfigOffsetAccomType relatedCPConfigOffsetAccomType1 = createCPConfigOffsetAccomType(
                OffsetMethod.FIXED_OFFSET, at2, BigDecimal.valueOf(2), OccupancyType.SINGLE, null, null);
        CPConfigOffsetAccomType relatedCPConfigOffsetAccomType2 = createCPConfigOffsetAccomType(
                OffsetMethod.FIXED_OFFSET, at4, BigDecimal.valueOf(0), OccupancyType.SINGLE, null, null);

        Set<String> warnings = new HashSet<>();
        when(tenantCrudService.findByNamedQuery(PricingAccomClass.FIND_BY_PROPERTY_ID,
                QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).parameters()))
                .thenReturn(List.of(pricingAccomClass1, pricingAccomClass2));
        assertFalse(agileRatesHierarchyValidationService.isHierarchyBetweenIPsValid(
                groupCeilingFloorValues(List.of(selectedPricingBaseAccomType1, selectedPricingBaseAccomType2)),
                groupCeilingFloorValues(List.of(relatedPricingBaseAccomType1, relatedPricingBaseAccomType2)),
                groupOffsetsValues(List.of(selectedCPConfigOffsetAccomType1, selectedCPConfigOffsetAccomType2)),
                groupOffsetsValues(List.of(relatedCPConfigOffsetAccomType1, relatedCPConfigOffsetAccomType2)),
                new HashSet<>(),
                BigDecimal.ZERO,
                warnings,
                c -> true
        ));
    }

    @Test
    public void isHierarchyBetweenIPsValidTest_ceilingFloorAndOffsets_seasonsToDefaults_valid() {
        AccomClass ac1 = createAccomClass(1, "AC1");
        AccomType at1 = createAccomType(1, "AT1", ac1);
        AccomType at2 = createAccomType(2, "AT2", ac1);
        ac1.setAccomTypes(Set.of(at1, at2));

        AccomClass ac2 = createAccomClass(2, "AC2");
        AccomType at3 = createAccomType(3, "AT3", ac2);
        AccomType at4 = createAccomType(4, "AT4", ac2);
        ac2.setAccomTypes(Set.of(at3, at4));
        PricingAccomClass pricingAccomClass1 = createPricingAccomClass(1, at1, ac1);
        PricingAccomClass pricingAccomClass2 = createPricingAccomClass(2, at3, ac2);

        PricingBaseAccomType selectedDefaultsPricingBaseAccomType1 = createPricingBaseAccomType(
                BigDecimal.valueOf(100), BigDecimal.valueOf(20), at1, null, null);
        PricingBaseAccomType selectedDefaultsPricingBaseAccomType2 = createPricingBaseAccomType(
                BigDecimal.valueOf(150), BigDecimal.valueOf(70), at3, null, null);
        PricingBaseAccomType selectedSeasonalPricingBaseAccomType1 = createPricingBaseAccomType(
                BigDecimal.valueOf(110), BigDecimal.valueOf(30), at1, LocalDate.parse("2023-06-01"), LocalDate.parse("2023-07-01"));
        PricingBaseAccomType selectedSeasonalPricingBaseAccomType2 = createPricingBaseAccomType(
                BigDecimal.valueOf(160), BigDecimal.valueOf(80), at3, LocalDate.parse("2023-06-01"), LocalDate.parse("2023-07-01"));

        CPConfigOffsetAccomType selectedCPConfigOffsetAccomType1 = createCPConfigOffsetAccomType(
                OffsetMethod.FIXED_OFFSET, at2, BigDecimal.valueOf(6), OccupancyType.SINGLE, null, null);
        CPConfigOffsetAccomType selectedCPConfigOffsetAccomType2 = createCPConfigOffsetAccomType(
                OffsetMethod.FIXED_OFFSET, at4, BigDecimal.valueOf(6), OccupancyType.SINGLE, null, null);

        PricingBaseAccomType relatedPricingBaseAccomType1 = createPricingBaseAccomType(
                BigDecimal.valueOf(120), BigDecimal.valueOf(40), at1, null, null);
        PricingBaseAccomType relatedPricingBaseAccomType2 = createPricingBaseAccomType(
                BigDecimal.valueOf(170), BigDecimal.valueOf(90), at3, null, null);
        CPConfigOffsetAccomType relatedCPConfigOffsetAccomType1 = createCPConfigOffsetAccomType(
                OffsetMethod.FIXED_OFFSET, at2, BigDecimal.valueOf(2), OccupancyType.SINGLE, null, null);
        CPConfigOffsetAccomType relatedCPConfigOffsetAccomType2 = createCPConfigOffsetAccomType(
                OffsetMethod.FIXED_OFFSET, at4, BigDecimal.valueOf(0), OccupancyType.SINGLE, null, null);

        Set<String> warnings = new HashSet<>();
        when(tenantCrudService.findByNamedQuery(PricingAccomClass.FIND_BY_PROPERTY_ID,
                QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).parameters()))
                .thenReturn(List.of(pricingAccomClass1, pricingAccomClass2));
        assertTrue(agileRatesHierarchyValidationService.isHierarchyBetweenIPsValid(
                groupCeilingFloorValues(List.of(selectedDefaultsPricingBaseAccomType1, selectedDefaultsPricingBaseAccomType2, selectedSeasonalPricingBaseAccomType1, selectedSeasonalPricingBaseAccomType2)),
                groupCeilingFloorValues(List.of(relatedPricingBaseAccomType1, relatedPricingBaseAccomType2)),
                groupOffsetsValues(List.of(selectedCPConfigOffsetAccomType1, selectedCPConfigOffsetAccomType2)),
                groupOffsetsValues(List.of(relatedCPConfigOffsetAccomType1, relatedCPConfigOffsetAccomType2)),
                new HashSet<>(),
                BigDecimal.ZERO,
                warnings,
                c -> true
        ));
    }

    @Test
    public void isHierarchyBetweenIPsValidTest_ceilingFloorAndOffsets_seasonsToDefaults_invalid() {
        AccomClass ac1 = createAccomClass(1, "AC1");
        AccomType at1 = createAccomType(1, "AT1", ac1);
        AccomType at2 = createAccomType(2, "AT2", ac1);
        ac1.setAccomTypes(Set.of(at1, at2));

        AccomClass ac2 = createAccomClass(2, "AC2");
        AccomType at3 = createAccomType(3, "AT3", ac2);
        AccomType at4 = createAccomType(4, "AT4", ac2);
        ac2.setAccomTypes(Set.of(at3, at4));
        PricingAccomClass pricingAccomClass1 = createPricingAccomClass(1, at1, ac1);
        PricingAccomClass pricingAccomClass2 = createPricingAccomClass(2, at3, ac2);

        PricingBaseAccomType selectedDefaultsPricingBaseAccomType1 = createPricingBaseAccomType(
                BigDecimal.valueOf(100), BigDecimal.valueOf(20), at1, null, null);
        PricingBaseAccomType selectedDefaultsPricingBaseAccomType2 = createPricingBaseAccomType(
                BigDecimal.valueOf(150), BigDecimal.valueOf(70), at3, null, null);
        PricingBaseAccomType selectedSeasonalPricingBaseAccomType1 = createPricingBaseAccomType(
                BigDecimal.valueOf(110), BigDecimal.valueOf(30), at1, LocalDate.parse("2023-06-01"), LocalDate.parse("2023-07-01"));
        PricingBaseAccomType selectedSeasonalPricingBaseAccomType2 = createPricingBaseAccomType(
                BigDecimal.valueOf(160), BigDecimal.valueOf(80), at3, LocalDate.parse("2023-06-01"), LocalDate.parse("2023-07-01"));

        CPConfigOffsetAccomType selectedCPConfigOffsetAccomType1 = createCPConfigOffsetAccomType(
                OffsetMethod.FIXED_OFFSET, at2, BigDecimal.valueOf(6), OccupancyType.SINGLE, null, null);
        CPConfigOffsetAccomType selectedCPConfigOffsetAccomType2 = createCPConfigOffsetAccomType(
                OffsetMethod.FIXED_OFFSET, at4, BigDecimal.valueOf(6), OccupancyType.SINGLE, null, null);
        CPConfigOffsetAccomType selectedSeasonalCPConfigOffsetAccomType2 = createCPConfigOffsetAccomType(
                OffsetMethod.FIXED_OFFSET, at4, BigDecimal.valueOf(11), OccupancyType.SINGLE, LocalDate.parse("2023-07-01"), LocalDate.parse("2023-08-01"));

        PricingBaseAccomType relatedPricingBaseAccomType1 = createPricingBaseAccomType(
                BigDecimal.valueOf(120), BigDecimal.valueOf(40), at1, null, null);
        PricingBaseAccomType relatedPricingBaseAccomType2 = createPricingBaseAccomType(
                BigDecimal.valueOf(170), BigDecimal.valueOf(90), at3, null, null);
        CPConfigOffsetAccomType relatedCPConfigOffsetAccomType1 = createCPConfigOffsetAccomType(
                OffsetMethod.FIXED_OFFSET, at2, BigDecimal.valueOf(2), OccupancyType.SINGLE, null, null);
        CPConfigOffsetAccomType relatedCPConfigOffsetAccomType2 = createCPConfigOffsetAccomType(
                OffsetMethod.FIXED_OFFSET, at4, BigDecimal.valueOf(0), OccupancyType.SINGLE, null, null);

        Set<String> warnings = new HashSet<>();
        when(tenantCrudService.findByNamedQuery(PricingAccomClass.FIND_BY_PROPERTY_ID,
                QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).parameters()))
                .thenReturn(List.of(pricingAccomClass1, pricingAccomClass2));
        assertTrue(agileRatesHierarchyValidationService.isHierarchyBetweenIPsValid(
                groupCeilingFloorValues(List.of(selectedDefaultsPricingBaseAccomType1, selectedDefaultsPricingBaseAccomType2, selectedSeasonalPricingBaseAccomType1, selectedSeasonalPricingBaseAccomType2)),
                groupCeilingFloorValues(List.of(relatedPricingBaseAccomType1, relatedPricingBaseAccomType2)),
                groupOffsetsValues(List.of(selectedCPConfigOffsetAccomType1, selectedCPConfigOffsetAccomType2, selectedSeasonalCPConfigOffsetAccomType2)),
                groupOffsetsValues(List.of(relatedCPConfigOffsetAccomType1, relatedCPConfigOffsetAccomType2)),
                new HashSet<>(),
                BigDecimal.ZERO,
                warnings,
                c -> true
        ));
    }

    @Test
    public void isHierarchyBetweenIPsValidTest_ceilingFloorAndOffsets_seasonsToDefaults_invalid2() {
        AccomClass ac1 = createAccomClass(1, "AC1");
        AccomType at1 = createAccomType(1, "AT1", ac1);
        AccomType at2 = createAccomType(2, "AT2", ac1);
        ac1.setAccomTypes(Set.of(at1, at2));

        AccomClass ac2 = createAccomClass(2, "AC2");
        AccomType at3 = createAccomType(3, "AT3", ac2);
        AccomType at4 = createAccomType(4, "AT4", ac2);
        ac2.setAccomTypes(Set.of(at3, at4));
        PricingAccomClass pricingAccomClass1 = createPricingAccomClass(1, at1, ac1);
        PricingAccomClass pricingAccomClass2 = createPricingAccomClass(2, at3, ac2);

        PricingBaseAccomType selectedDefaultsPricingBaseAccomType1 = createPricingBaseAccomType(
                BigDecimal.valueOf(100), BigDecimal.valueOf(20), at1, null, null);
        PricingBaseAccomType selectedDefaultsPricingBaseAccomType2 = createPricingBaseAccomType(
                BigDecimal.valueOf(150), BigDecimal.valueOf(70), at3, null, null);
        PricingBaseAccomType selectedSeasonalPricingBaseAccomType1 = createPricingBaseAccomType(
                BigDecimal.valueOf(110), BigDecimal.valueOf(30), at1, LocalDate.parse("2023-06-01"), LocalDate.parse("2023-07-01"));
        PricingBaseAccomType selectedSeasonalPricingBaseAccomType2 = createPricingBaseAccomType(
                BigDecimal.valueOf(160), BigDecimal.valueOf(80), at3, LocalDate.parse("2023-06-01"), LocalDate.parse("2023-07-01"));

        CPConfigOffsetAccomType selectedCPConfigOffsetAccomType1 = createCPConfigOffsetAccomType(
                OffsetMethod.FIXED_OFFSET, at2, BigDecimal.valueOf(6), OccupancyType.SINGLE, null, null);
        CPConfigOffsetAccomType selectedCPConfigOffsetAccomType2 = createCPConfigOffsetAccomType(
                OffsetMethod.FIXED_OFFSET, at4, BigDecimal.valueOf(6), OccupancyType.SINGLE, null, null);
        CPConfigOffsetAccomType selectedSeasonalCPConfigOffsetAccomType2 = createCPConfigOffsetAccomType(
                OffsetMethod.FIXED_OFFSET, at4, BigDecimal.valueOf(11), OccupancyType.SINGLE, LocalDate.parse("2023-07-01"), LocalDate.parse("2023-08-01"));

        PricingBaseAccomType relatedPricingBaseAccomType1 = createPricingBaseAccomType(
                BigDecimal.valueOf(120), BigDecimal.valueOf(40), at1, null, null);
        PricingBaseAccomType relatedPricingBaseAccomType2 = createPricingBaseAccomType(
                BigDecimal.valueOf(170), BigDecimal.valueOf(90), at3, null, null);
        CPConfigOffsetAccomType relatedCPConfigOffsetAccomType1 = createCPConfigOffsetAccomType(
                OffsetMethod.FIXED_OFFSET, at2, BigDecimal.valueOf(2), OccupancyType.SINGLE, null, null);
        CPConfigOffsetAccomType relatedCPConfigOffsetAccomType2 = createCPConfigOffsetAccomType(
                OffsetMethod.FIXED_OFFSET, at4, BigDecimal.valueOf(0), OccupancyType.SINGLE, null, null);
        CPConfigOffsetAccomType relatedSeasonalCPConfigOffsetAccomType2 = createCPConfigOffsetAccomType(
                OffsetMethod.FIXED_OFFSET, at4, BigDecimal.valueOf(15), OccupancyType.SINGLE, LocalDate.parse("2023-08-01"), LocalDate.parse("2023-09-01"));

        Set<String> warnings = new HashSet<>();
        when(tenantCrudService.findByNamedQuery(PricingAccomClass.FIND_BY_PROPERTY_ID,
                QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).parameters()))
                .thenReturn(List.of(pricingAccomClass1, pricingAccomClass2));
        assertTrue(agileRatesHierarchyValidationService.isHierarchyBetweenIPsValid(
                groupCeilingFloorValues(List.of(selectedDefaultsPricingBaseAccomType1, selectedDefaultsPricingBaseAccomType2, selectedSeasonalPricingBaseAccomType1, selectedSeasonalPricingBaseAccomType2)),
                groupCeilingFloorValues(List.of(relatedPricingBaseAccomType1, relatedPricingBaseAccomType2)),
                groupOffsetsValues(List.of(selectedCPConfigOffsetAccomType1, selectedCPConfigOffsetAccomType2, selectedSeasonalCPConfigOffsetAccomType2)),
                groupOffsetsValues(List.of(relatedCPConfigOffsetAccomType1, relatedCPConfigOffsetAccomType2, relatedSeasonalCPConfigOffsetAccomType2)),
                new HashSet<>(),
                BigDecimal.ZERO,
                warnings,
                c -> true
        ));
    }

    @Test
    public void isHierarchyBetweenIPsValidTest_ceilingFloorAndOffsets_default_to_defaults_invalid() {
        AccomClass ac1 = createAccomClass(1, "AC1");
        AccomType at1 = createAccomType(1, "AT1", ac1);
        AccomType at2 = createAccomType(2, "AT2", ac1);
        ac1.setAccomTypes(Set.of(at1, at2));

        AccomClass ac2 = createAccomClass(2, "AC2");
        AccomType at3 = createAccomType(3, "AT3", ac2);
        AccomType at4 = createAccomType(4, "AT4", ac2);
        ac2.setAccomTypes(Set.of(at3, at4));
        PricingAccomClass pricingAccomClass1 = createPricingAccomClass(1, at1, ac1);
        PricingAccomClass pricingAccomClass2 = createPricingAccomClass(2, at3, ac2);

        PricingBaseAccomType selectedDefaultsPricingBaseAccomType1 = createPricingBaseAccomType(
                BigDecimal.valueOf(130), BigDecimal.valueOf(20), at1, null, null);
        PricingBaseAccomType selectedDefaultsPricingBaseAccomType2 = createPricingBaseAccomType(
                BigDecimal.valueOf(150), BigDecimal.valueOf(70), at3, null, null);
        PricingBaseAccomType selectedSeasonalPricingBaseAccomType1 = createPricingBaseAccomType(
                BigDecimal.valueOf(110), BigDecimal.valueOf(30), at1, LocalDate.parse("2023-06-01"), LocalDate.parse("2023-07-01"));
        PricingBaseAccomType selectedSeasonalPricingBaseAccomType2 = createPricingBaseAccomType(
                BigDecimal.valueOf(160), BigDecimal.valueOf(80), at3, LocalDate.parse("2023-06-01"), LocalDate.parse("2023-07-01"));

        CPConfigOffsetAccomType selectedCPConfigOffsetAccomType1 = createCPConfigOffsetAccomType(
                OffsetMethod.FIXED_OFFSET, at2, BigDecimal.valueOf(6), OccupancyType.SINGLE, null, null);
        CPConfigOffsetAccomType selectedCPConfigOffsetAccomType2 = createCPConfigOffsetAccomType(
                OffsetMethod.FIXED_OFFSET, at4, BigDecimal.valueOf(6), OccupancyType.SINGLE, null, null);
        CPConfigOffsetAccomType selectedSeasonalCPConfigOffsetAccomType2 = createCPConfigOffsetAccomType(
                OffsetMethod.FIXED_OFFSET, at4, BigDecimal.valueOf(11), OccupancyType.SINGLE, LocalDate.parse("2023-07-01"), LocalDate.parse("2023-08-01"));

        PricingBaseAccomType relatedPricingBaseAccomType1 = createPricingBaseAccomType(
                BigDecimal.valueOf(120), BigDecimal.valueOf(40), at1, null, null);
        PricingBaseAccomType relatedPricingBaseAccomType2 = createPricingBaseAccomType(
                BigDecimal.valueOf(170), BigDecimal.valueOf(90), at3, null, null);
        CPConfigOffsetAccomType relatedCPConfigOffsetAccomType1 = createCPConfigOffsetAccomType(
                OffsetMethod.FIXED_OFFSET, at2, BigDecimal.valueOf(2), OccupancyType.SINGLE, null, null);
        CPConfigOffsetAccomType relatedCPConfigOffsetAccomType2 = createCPConfigOffsetAccomType(
                OffsetMethod.FIXED_OFFSET, at4, BigDecimal.valueOf(0), OccupancyType.SINGLE, null, null);
        CPConfigOffsetAccomType relatedSeasonalCPConfigOffsetAccomType2 = createCPConfigOffsetAccomType(
                OffsetMethod.FIXED_OFFSET, at4, BigDecimal.valueOf(15), OccupancyType.SINGLE, LocalDate.parse("2023-08-01"), LocalDate.parse("2023-09-01"));

        Set<String> warnings = new HashSet<>();
        when(tenantCrudService.findByNamedQuery(PricingAccomClass.FIND_BY_PROPERTY_ID,
                QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).parameters()))
                .thenReturn(List.of(pricingAccomClass1, pricingAccomClass2));
        assertFalse(agileRatesHierarchyValidationService.isHierarchyBetweenIPsValid(
                groupCeilingFloorValues(List.of(selectedDefaultsPricingBaseAccomType1, selectedDefaultsPricingBaseAccomType2, selectedSeasonalPricingBaseAccomType1, selectedSeasonalPricingBaseAccomType2)),
                groupCeilingFloorValues(List.of(relatedPricingBaseAccomType1, relatedPricingBaseAccomType2)),
                groupOffsetsValues(List.of(selectedCPConfigOffsetAccomType1, selectedCPConfigOffsetAccomType2, selectedSeasonalCPConfigOffsetAccomType2)),
                groupOffsetsValues(List.of(relatedCPConfigOffsetAccomType1, relatedCPConfigOffsetAccomType2, relatedSeasonalCPConfigOffsetAccomType2)),
                new HashSet<>(),
                BigDecimal.ZERO,
                warnings,
                c -> true
        ));
    }

    private Map<Pair<LocalDate, LocalDate>, List<CPConfigOffsetAccomType>> groupOffsetsValues(List<CPConfigOffsetAccomType> offsets) {
        return offsets.stream()
                .collect(groupingBy(offset -> Pair.of(offset.getStartDate(), offset.getEndDate())));
    }

    private Map<Pair<LocalDate, LocalDate>, List<PricingBaseAccomType>> groupCeilingFloorValues(List<PricingBaseAccomType> ceilingFloorValues) {
        return ceilingFloorValues
                .stream()
                .collect(groupingBy(ceilingFloor -> Pair.of(ceilingFloor.getStartDate(), ceilingFloor.getEndDate())));
    }

    private CPConfigOffsetAccomType createCPConfigOffsetAccomType(OffsetMethod offsetMethod, AccomType accomType,
                                                                  BigDecimal offsetValue, OccupancyType occupancyType, LocalDate startDate, LocalDate endDate) {
        CPConfigOffsetAccomType cpConfigOffsetAccomType = new CPConfigOffsetAccomType();
        cpConfigOffsetAccomType.setOffsetMethod(offsetMethod);
        cpConfigOffsetAccomType.setAccomType(accomType);
        cpConfigOffsetAccomType.setEndDate(endDate);
        cpConfigOffsetAccomType.setFridayOffsetValueWithTax(offsetValue);
        cpConfigOffsetAccomType.setMondayOffsetValueWithTax(offsetValue);
        cpConfigOffsetAccomType.setOccupancyType(occupancyType);
        cpConfigOffsetAccomType.setSaturdayOffsetValueWithTax(offsetValue);
        cpConfigOffsetAccomType.setStartDate(startDate);
        cpConfigOffsetAccomType.setSundayOffsetValueWithTax(offsetValue);
        cpConfigOffsetAccomType.setThursdayOffsetValueWithTax(offsetValue);
        cpConfigOffsetAccomType.setTuesdayOffsetValueWithTax(offsetValue);
        cpConfigOffsetAccomType.setWednesdayOffsetValueWithTax(offsetValue);
        return cpConfigOffsetAccomType;
    }

    private PricingBaseAccomType createPricingBaseAccomType(BigDecimal ceiling, BigDecimal floor, AccomType accomType, LocalDate startDate, LocalDate endDate) {
        PricingBaseAccomType pricingBaseAccomType = new TransientPricingBaseAccomType();
        pricingBaseAccomType.setAccomType(accomType);
        pricingBaseAccomType.setSundayCeilingRateWithTax(ceiling);
        pricingBaseAccomType.setMondayCeilingRateWithTax(ceiling);
        pricingBaseAccomType.setTuesdayCeilingRateWithTax(ceiling);
        pricingBaseAccomType.setWednesdayCeilingRateWithTax(ceiling);
        pricingBaseAccomType.setThursdayCeilingRateWithTax(ceiling);
        pricingBaseAccomType.setFridayCeilingRateWithTax(ceiling);
        pricingBaseAccomType.setSaturdayCeilingRateWithTax(ceiling);
        pricingBaseAccomType.setSundayFloorRateWithTax(floor);
        pricingBaseAccomType.setMondayFloorRateWithTax(floor);
        pricingBaseAccomType.setTuesdayFloorRateWithTax(floor);
        pricingBaseAccomType.setWednesdayFloorRateWithTax(floor);
        pricingBaseAccomType.setThursdayFloorRateWithTax(floor);
        pricingBaseAccomType.setFridayFloorRateWithTax(floor);
        pricingBaseAccomType.setSaturdayFloorRateWithTax(floor);
        pricingBaseAccomType.setStartDate(startDate);
        pricingBaseAccomType.setEndDate(endDate);
        return pricingBaseAccomType;
    }

    private AccomType createAccomType(Integer id, String name, AccomClass accomClass) {
        AccomType accomType = new AccomType();
        accomType.setId(id);
        accomType.setName(name);
        accomType.setAccomClass(accomClass);

        return accomType;
    }

    private AccomClass createAccomClass(Integer id, String name) {
        AccomClass accomClass = new AccomClass();
        accomClass.setId(id);
        accomClass.setName(name);
        return accomClass;
    }

    private PricingAccomClass createPricingAccomClass(Integer id, AccomType accomType, AccomClass accomClass) {
        PricingAccomClass pricingAccomClass = new PricingAccomClass();
        pricingAccomClass.setId(id);
        pricingAccomClass.setAccomType(accomType);
        pricingAccomClass.setAccomClass(accomClass);
        return pricingAccomClass;
    }
}
