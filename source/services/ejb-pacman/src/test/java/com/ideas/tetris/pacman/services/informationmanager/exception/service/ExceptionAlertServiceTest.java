package com.ideas.tetris.pacman.services.informationmanager.exception.service;

import com.google.common.collect.Sets;
import com.ideas.g3.data.TestProperty;
import com.ideas.g3.test.AbstractG3JupiterTest;
import com.ideas.infra.tetris.security.jaas.TetrisPrincipal;
import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.configparams.GUIConfigParamName;
import com.ideas.tetris.pacman.common.configparams.IntegrationConfigParamName;
import com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.informationmanager.cache.InfoMgrStatusEntityCache;
import com.ideas.tetris.pacman.services.informationmanager.dto.Alert;
import com.ideas.tetris.pacman.services.informationmanager.dto.AlertHistory;
import com.ideas.tetris.pacman.services.informationmanager.dto.AlertType;
import com.ideas.tetris.pacman.services.informationmanager.dto.ExceptionAlert;
import com.ideas.tetris.pacman.services.informationmanager.dto.ExceptionConfigDTO;
import com.ideas.tetris.pacman.services.informationmanager.dto.SearchCriteriaDTO;
import com.ideas.tetris.pacman.services.informationmanager.entity.InfoMgrExcepNotifEntity;
import com.ideas.tetris.pacman.services.informationmanager.entity.InfoMgrExcepNotifSkipDatesEntity;
import com.ideas.tetris.pacman.services.informationmanager.entity.InfoMgrInstanceEntity;
import com.ideas.tetris.pacman.services.informationmanager.entity.InfoMgrInstanceStepStateEntity;
import com.ideas.tetris.pacman.services.informationmanager.entity.InfoMgrStatusEntity;
import com.ideas.tetris.pacman.services.informationmanager.entity.InfoMgrTypeEntity;
import com.ideas.tetris.pacman.services.informationmanager.entity.InformationMgrAlertConfigEntity;
import com.ideas.tetris.pacman.services.informationmanager.entity.InformationMgrLevelEntity;
import com.ideas.tetris.pacman.services.informationmanager.entity.InformationMgrSubTypeEntity;
import com.ideas.tetris.pacman.services.informationmanager.enums.ExceptionSubType;
import com.ideas.tetris.pacman.services.informationmanager.enums.LevelType;
import com.ideas.tetris.pacman.services.informationmanager.enums.MetricType;
import com.ideas.tetris.pacman.services.informationmanager.enums.SubLevelType;
import com.ideas.tetris.pacman.services.product.Product;
import com.ideas.tetris.pacman.services.property.PropertyService;
import com.ideas.tetris.pacman.services.propertygroup.service.PropertyGroupService;
import com.ideas.tetris.pacman.services.security.AuthorizationService;
import com.ideas.tetris.pacman.services.security.GlobalUser;
import com.ideas.tetris.pacman.services.security.RoleService;
import com.ideas.tetris.pacman.services.security.UserGlobalDBService;
import com.ideas.tetris.pacman.services.security.UserService;
import com.ideas.tetris.platform.common.contextholder.PacmanThreadLocalContextHolder;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.xml.schema.workcontext.v1.WorkContextType;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import com.ideas.tetris.platform.services.daoandentities.entity.Property;
import org.joda.time.LocalDate;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static com.ideas.tetris.platform.common.contextholder.PacmanWorkContextTestHelper.createTetrisPrincipal;
import static com.ideas.tetris.platform.common.contextholder.PlatformWorkContextTestHelper.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;
import static com.ideas.tetris.platform.common.contextholder.PlatformWorkContextTestHelper.WC_CLIENT_CODE_TEST;
import static com.ideas.tetris.platform.common.contextholder.PlatformWorkContextTestHelper.WC_CN_REGULAR_USER;
import static com.ideas.tetris.platform.common.contextholder.PlatformWorkContextTestHelper.WC_DN_REGULAR_USER;
import static com.ideas.tetris.platform.common.contextholder.PlatformWorkContextTestHelper.WC_USER_ID_SSO;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyMap;
import static org.mockito.ArgumentMatchers.anyObject;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;


@MockitoSettings(strictness = Strictness.LENIENT)
public class ExceptionAlertServiceTest extends AbstractG3JupiterTest {

    private static final Integer STATUS_NEW = 3;

    private static final String DESCRIPTION = "test description";

    private static final String DETAILS = "test details : Los";

    private static final AlertType TYPE = AlertType.DecisionChangeEx;

    private static final int SCORE = 50;

    public static final String FREQUENCY = "1";

    public static final String TODAY = "Today";

    public static final String TODAY7 = "Today+7";

    private static final String CLIENT = "unittest";

    private static final Integer CLIENT_ID_DUMMY = 1;

    private static final Integer PROPERTY_ID_PARIS = 6;

    private static final String PROPERTY_CODE_PARIS = "H2";

    private static final String UID_SSO_USER = "11403";

    private static final String UID_REGULAR_USER = "5003";

    private static final String DN_PREFIX = "uid=";

    private static final String DN_SUFFIX = ",ou=users,o=" + CLIENT + ",dc=ideas,dc=com";

    private static final String DN_REGULAR_USER = DN_PREFIX + UID_REGULAR_USER + DN_SUFFIX;

    private static final String CREATED_BY = "SSO User";

    private static final String CN_REGULAR_USER = "Regular User";

    public static final String THIS_IS_OPEN_AM_TOKEN = "ThisIsOpenAmToken";

    private ExceptionAlertService service = new ExceptionAlertService();

    private WorkContextType workContext;

    private Property property;

    PropertyGroupService propertyGroupService;

    DateService mockDateService;

    ExceptionConfigService exceptionConfigService;

    PacmanConfigParamsService pacmanConfigParamsService;

    PacmanConfigParamsService mockConfigParamsService;

    UserService userService = new UserService();

    AuthorizationService authorizationService = null;

    AuthorizationService authService;

    @Mock
    private UserGlobalDBService mockUserGlobalDBService;

    @Mock
    private PropertyService propertyService;

    @BeforeEach
    public void setUp() {
        setWorkContextProperty(TestProperty.H2);
        exceptionConfigService = new ExceptionConfigService();
        property = globalCrudService().find(Property.class, 6);
        authorizationService = mock(AuthorizationService.class);
        authService = new AuthorizationService();
        authService.setCrudService(tenantCrudService());
        authService.setGlobalCrudService(globalCrudService());
        userService.setAuthorizationService(authService);
        userService.setAuthorizationService(authService);
        when(authorizationService.retrieveAuthorizedProperties()).thenReturn(Arrays.asList(property));
        exceptionConfigService.crudService = tenantCrudService();
        exceptionConfigService.setGlobalCrudService(globalCrudService());
        exceptionConfigService.setMultiPropertyCrudService(multiPropertyCrudService());
        service.setGlobalCrudService(globalCrudService());
        service.setMultiPropertyCrudService(multiPropertyCrudService());
        service.setTenantCrudService(tenantCrudService());
        service.userService = userService;
        service.setPropertyService(propertyService);
        propertyGroupService = new PropertyGroupService();
        mockDateService = DateService.createTestInstance();
        mockDateService.setPropertyGroupService(propertyGroupService);
        mockDateService.setCrudService(tenantCrudService());
        service.propertyGroupService = propertyGroupService;
        service.setAuthorizationService(authorizationService);
        service.dateService = mockDateService;
        mockConfigParamsService = mock(PacmanConfigParamsService.class);
        service.setConfigService(mockConfigParamsService);
        userService.setPacmanConfigParamsService(mockConfigParamsService);
        pacmanConfigParamsService = mock(PacmanConfigParamsService.class);
        authService.setPacmanConfigParamsService(pacmanConfigParamsService);
        InfoMgrStatusEntityCache infoMgrStatusEntityCache = Mockito.mock(InfoMgrStatusEntityCache.class);
        Mockito.when(infoMgrStatusEntityCache.get(anyString())).thenReturn(tenantCrudService().find(InfoMgrStatusEntity.class, Constants.ALERT_STATUS_NEW_ID));
        service.setInfoMgrStatusEntityCache(infoMgrStatusEntityCache);
        // Regular user did not have access to prop id 5 so we are using prop 6 (hilton paris)
        workContext = new WorkContextType();
        workContext.setUserId(UID_SSO_USER);
        workContext.setClientCode(CLIENT);
        workContext.setClientId(CLIENT_ID_DUMMY);
        workContext.setPropertyId(PROPERTY_ID_PARIS);
        workContext.setPropertyCode(PROPERTY_CODE_PARIS);
        PacmanWorkContextHelper.setWorkContext(workContext);
        @SuppressWarnings("serial")
        Map<String, String> attributes = new HashMap<String, String>() {

            {
                put("cn", CN_REGULAR_USER);
                put("dn", DN_REGULAR_USER);
            }
        };
        String token = THIS_IS_OPEN_AM_TOKEN;
        TetrisPrincipal principal = new TetrisPrincipal(UID_SSO_USER, CLIENT, token, attributes);
        PacmanThreadLocalContextHolder.put(Constants.SSO_USER_PRINCIPAL, principal);
        when(pacmanConfigParamsService.getParameterValue(GUIConfigParamName.IS_PROPERTY_READY_FOR_EXTERNAL_USER)).thenReturn(true);
    }

    @Test
    public void createExceptionAlert() {
        InfoMgrTypeEntity alertTypeEntity = new InfoMgrTypeEntity();
        alertTypeEntity.setDescription(DESCRIPTION);
        alertTypeEntity.setBaseScore(SCORE);
        alertTypeEntity.setAlertCategory(Constants.EXCEPTION_CATEGORY);
        Date occupancyDate = new Date();
        // ExceptionSubType subType = ExceptionSubType.LRV;
        InformationMgrSubTypeEntity objExceptionSubTypeEntity = tenantCrudService().findByNamedQuerySingleResult(InformationMgrSubTypeEntity.BY_NAME, QueryParameter.with("name", ExceptionSubType.LRV.getCode()).parameters());
        List<Integer> listPropertyIds = new ArrayList<Integer>();
        listPropertyIds.add(PROPERTY_ID_PARIS);
        Map<String, List<InformationMgrAlertConfigEntity>> resultList = exceptionConfigService.persistExceptionConfiguration(buildExceptionConfiguration(ExceptionSubType.LRV.getCode(), LevelType.ROOM_CLASS.getCode(), SubLevelType.MASTER_CLASS.getCode(), listPropertyIds, true));
        tenantCrudService().flush();
        List<InformationMgrAlertConfigEntity> list = resultList.get("SUCCESS");
        list.get(0).setExceptionSubType(objExceptionSubTypeEntity);
        InfoMgrExcepNotifEntity result = service.createExceptionAlert(alertTypeEntity, DETAILS, TYPE, occupancyDate, list.get(0), null);
        assertNotNull(result.getCreatedBy());
        assertNotNull(result);
    }

    @Test
    public void createExceptionAlertWithNullOccDate() {
        InfoMgrTypeEntity alertTypeEntity = new InfoMgrTypeEntity();
        alertTypeEntity.setDescription(DESCRIPTION);
        alertTypeEntity.setBaseScore(SCORE);
        alertTypeEntity.setAlertCategory(Constants.EXCEPTION_CATEGORY);
        InformationMgrSubTypeEntity objExceptionSubTypeEntity = tenantCrudService().findByNamedQuerySingleResult(InformationMgrSubTypeEntity.BY_NAME, QueryParameter.with("name", ExceptionSubType.LRV.getCode()).parameters());
        List<Integer> listPropertyIds = new ArrayList<Integer>();
        listPropertyIds.add(PROPERTY_ID_PARIS);
        Map<String, List<InformationMgrAlertConfigEntity>> resultList = exceptionConfigService.persistExceptionConfiguration(buildExceptionConfiguration(ExceptionSubType.LRV.getCode(), LevelType.ROOM_CLASS.getCode(), SubLevelType.MASTER_CLASS.getCode(), listPropertyIds, true));
        tenantCrudService().flush();
        List<InformationMgrAlertConfigEntity> list = resultList.get("SUCCESS");
        InfoMgrExcepNotifEntity result = service.createExceptionAlert(alertTypeEntity, DETAILS, TYPE, null, list.get(0), null);
        assertNotNull(result);
    }

    @Test
    public void findExceptionAlertAndUpdate() {
        InfoMgrTypeEntity alertTypeEntity = new InfoMgrTypeEntity();
        alertTypeEntity.setId(21);
        alertTypeEntity.setDescription(DESCRIPTION);
        alertTypeEntity.setBaseScore(SCORE);
        alertTypeEntity.setScoreIncrement(10);
        alertTypeEntity.setAlertCategory(Constants.SYSTEM_EXCEPTION_CATEGORY);
        Date occupancyDate = new Date();
        InformationMgrSubTypeEntity subType = tenantCrudService().findByNamedQuerySingleResult(InformationMgrSubTypeEntity.BY_NAME, QueryParameter.with("name", ExceptionSubType.LRV.getCode()).parameters());
        List<Integer> listPropertyIds = new ArrayList<Integer>();
        listPropertyIds.add(6);
        Map<String, List<InformationMgrAlertConfigEntity>> resultList = exceptionConfigService.persistExceptionConfiguration(buildExceptionConfiguration(ExceptionSubType.LRV.getCode(), LevelType.ROOM_CLASS.getCode(), SubLevelType.MASTER_CLASS.getCode(), listPropertyIds, true));
        tenantCrudService().flush();
        List<InformationMgrAlertConfigEntity> list = resultList.get("SUCCESS");
        InfoMgrExcepNotifEntity result = service.createExceptionAlert(alertTypeEntity, DETAILS, TYPE, occupancyDate, list.get(0), null);
        assertNotNull(result);
        InfoMgrExcepNotifEntity infoMgrExcepNotifEntity = getInfoMgrExcepNotifEntity(alertTypeEntity, occupancyDate, subType, list);
        Map<Date, InfoMgrExcepNotifEntity> existingException = new HashMap() {

            {
                put(occupancyDate, infoMgrExcepNotifEntity);
            }
        };
        InfoMgrExcepNotifEntity updatedResult = service.createExceptionAlert(alertTypeEntity, DETAILS, TYPE, occupancyDate, list.get(0), existingException);
        assertTrue(updatedResult.getScore() > result.getScore());
        assertNotNull(result);
    }

    private InfoMgrExcepNotifEntity getInfoMgrExcepNotifEntity(InfoMgrTypeEntity alertTypeEntity, Date occupancyDate, InformationMgrSubTypeEntity subType, List<InformationMgrAlertConfigEntity> list) {
        InfoMgrExcepNotifEntity infoMgrExcepNotifEntity = new InfoMgrExcepNotifEntity();
        infoMgrExcepNotifEntity.setOccupancyDate(occupancyDate);
        infoMgrExcepNotifEntity.setAlertType(alertTypeEntity);
        infoMgrExcepNotifEntity.setSubType(subType);
        infoMgrExcepNotifEntity.setId(1);
        infoMgrExcepNotifEntity.setCreatedBy(CREATED_BY);
        infoMgrExcepNotifEntity.setPropertyId(6);
        infoMgrExcepNotifEntity.setScore(50);
        infoMgrExcepNotifEntity.setExceptionAlertConfigEntityId(list.get(0).getId());
        return infoMgrExcepNotifEntity;
    }

    @Test
    public void findExistingAlert() {
        InfoMgrTypeEntity alertType = tenantCrudService().find(InfoMgrTypeEntity.class, 1);
        assertNotNull(alertType);
        InfoMgrStatusEntity alertStatus = tenantCrudService().find(InfoMgrStatusEntity.class, 1);
        assertNotNull(alertStatus);
        InformationMgrSubTypeEntity subType = tenantCrudService().findByNamedQuerySingleResult(InformationMgrSubTypeEntity.BY_NAME, QueryParameter.with("name", ExceptionSubType.LRV.getCode()).parameters());
        InfoMgrExcepNotifEntity e = new InfoMgrExcepNotifEntity();
        e.setOccupancyDate(new Date());
        e.setAlertType(alertType);
        e.setSubType(subType);
        e.setDescription("description");
        e.setDetails("the devil is in the");
        e.setPropertyId(6);
        e.setStatusId(STATUS_NEW);
        e.setScore(10);
        e.setAlertStatus(alertStatus);
        e.setCreatedBy("test user");
        e.setCreateDate(new Date());
        e.setLastModificationDate(new Date());
        tenantCrudService().save(e);
        Integer id = e.getId();
        InfoMgrExcepNotifEntity found = tenantCrudService().find(InfoMgrExcepNotifEntity.class, id);
        assertNotNull(found);
        Date occupancyDate = found.getOccupancyDate();
        assertNotNull(occupancyDate);
        GregorianCalendar gc = new GregorianCalendar();
        gc.setTime(occupancyDate);
    }

    @Test
    public void getNewOpenExceptionAlertCount() throws Exception {
        Integer count = service.getOpenExceptionAlertCount();
        assertEquals(0, count.intValue());
        buildExceptionAlert(DESCRIPTION, DETAILS, TYPE, ExceptionSubType.LRV);
        count = service.getOpenExceptionAlertCount();
        assertEquals(1, count.intValue());
        List<InfoMgrInstanceEntity> list = service.findAll();
        assertNotNull(list);
    }

    @Test
    public void getNewExceptionAlerts() {
        buildExceptionAlert(DESCRIPTION, DETAILS, TYPE, ExceptionSubType.LRV);
        List<ExceptionAlert> newAlertsList = service.getNewExceptionAlerts();
        assertEquals(1, newAlertsList.size());
    }

    @Test
    public void getNewExecptionAlertCount() {
        buildExceptionAlert(DESCRIPTION, DETAILS, TYPE, ExceptionSubType.LRV);
        Integer newAlertsCount = service.getNewExceptionAlertCount();
        assertEquals(new Integer(1), newAlertsCount);
    }

    @Test
    public void getOpenExceptionAlerts() {
        List<ExceptionAlert> alerts = service.getOpenExceptionAlerts();
        assertNotNull(alerts);
        assertEquals(0, alerts.size());
        buildExceptionAlert(DESCRIPTION, DETAILS, TYPE, ExceptionSubType.LRV);
        alerts = service.getOpenExceptionAlerts();
        assertNotNull(alerts);
        assertEquals(1, alerts.size());
        Alert alert = alerts.get(0);
        assertEquals(PROPERTY_ID_PARIS, alert.getPropertyId());
        assertEquals("last.room.value change.outside.of.defined.threshold", alert.getDescription());
        assertEquals(TYPE, alert.getType());
        assertEquals(SCORE, alert.getScore());
        assertEquals(CREATED_BY, alert.getCreatedBy());
    }

    private InfoMgrExcepNotifEntity buildExceptionAlert(String description, String details, AlertType alertType, ExceptionSubType exceptionSubType) {
        WorkContextType workContext = new WorkContextType();
        workContext.setUserId(CREATED_BY);
        workContext.setPropertyId(PROPERTY_ID_PARIS);
        workContext.setPropertyCode("");
        List<Integer> listPropertyIds = new ArrayList<Integer>();
        listPropertyIds.add(PROPERTY_ID_PARIS);
        Map<String, List<InformationMgrAlertConfigEntity>> resultList = exceptionConfigService.persistExceptionConfiguration(buildExceptionConfiguration(exceptionSubType.getCode(), LevelType.ROOM_CLASS.getCode(), SubLevelType.MASTER_CLASS.getCode(), listPropertyIds, true));
        tenantCrudService().flush();
        List<InformationMgrAlertConfigEntity> list = resultList.get("SUCCESS");
        InfoMgrTypeEntity alertTypeEntity = new InfoMgrTypeEntity();
        alertTypeEntity.setDescription(description);
        alertTypeEntity.setBaseScore(SCORE);
        alertTypeEntity.setAlertCategory(Constants.EXCEPTION_CATEGORY);
        Date occupancyDate = new Date();
        InformationMgrSubTypeEntity subType = tenantCrudService().findByNamedQuerySingleResult(InformationMgrSubTypeEntity.BY_NAME, QueryParameter.with("name", ExceptionSubType.LRV.getCode()).parameters());
        list.get(0).setExceptionSubType(subType);
        InfoMgrExcepNotifEntity exceptionAlert = service.createExceptionAlert(alertTypeEntity, details, alertType, occupancyDate, list.get(0), null);
        exceptionAlert = tenantCrudService().save(exceptionAlert);
        service.createAndSaveExceptionHistory(new ArrayList<>(), null, Arrays.asList(exceptionAlert));
        return exceptionAlert;
    }

    @Test
    public void getHistory() {
        InfoMgrExcepNotifEntity alert = buildExceptionAlert(DESCRIPTION, DETAILS, TYPE, ExceptionSubType.LRV);
        List<AlertHistory> historyList = service.getHistory(alert.getId(), alert.getPropertyId());
        assertNotNull(historyList);
        assertEquals(1, historyList.size());
        AlertHistory history = historyList.get(0);
        assertNull(history.getAssociatedComment());
        assertEquals(CREATED_BY, history.getCreatedBy());
        assertEquals(SCORE, history.getScore());
        assertEquals("Created", history.getType());
    }

    @Test
    public void resolveExceptionAlert() {
        InfoMgrExcepNotifEntity excepNotifEntity = buildExceptionAlert(DESCRIPTION, DETAILS, TYPE, ExceptionSubType.LRV);
        List<ExceptionAlert> alerts = service.getOpenExceptionAlerts();
        assertNotNull(alerts);
        assertEquals(1, alerts.size());
        service.resolveExceptionAlert(excepNotifEntity.getId(), excepNotifEntity.getPropertyId());
        alerts = service.getOpenExceptionAlerts();
        assertNotNull(alerts);
        assertEquals(1, alerts.size());
    }

    @Test
    public void viewExceptionAlert() {
        buildExceptionAlert(DESCRIPTION, DETAILS, TYPE, ExceptionSubType.LRV);
        List<ExceptionAlert> alerts = service.getOpenExceptionAlerts();
        assertNotNull(alerts);
        assertEquals(1, alerts.size());
        ExceptionAlert exceptionAlert = alerts.get(0);
        assertEquals("New", exceptionAlert.getStatus());
        service.viewExceptionAlert(exceptionAlert.getAlertId(), exceptionAlert.getPropertyId());
        alerts = service.getOpenExceptionAlerts();
        assertNotNull(alerts);
        assertEquals(1, alerts.size());
        exceptionAlert = alerts.get(0);
        assertEquals("Viewed", exceptionAlert.getStatus());
    }

    @Test
    public void actionExceptionAlert() {
        buildExceptionAlert(DESCRIPTION, DETAILS, TYPE, ExceptionSubType.LRV);
        List<ExceptionAlert> alerts = service.getOpenExceptionAlerts();
        ExceptionAlert alert = alerts.get(0);
        service.actionExceptionAlert(alert.getAlertId(), alert.getPropertyId());
    }

    @Test
    public void doExceptionAlertStep() {
        buildExceptionAlert(DESCRIPTION, DETAILS, TYPE, ExceptionSubType.LRV);
        List<ExceptionAlert> alerts = service.getOpenExceptionAlerts();
        ExceptionAlert alert = alerts.get(0);
        service.doStep(alert.getAlertId(), alert.getPropertyId(), alert.getSteps().get(0).getStepId(), "Test action");
    }

    @Test
    public void resolveAllExceptions() {
        buildExceptionAlert(DESCRIPTION, DETAILS, TYPE, ExceptionSubType.LRV);
        List<ExceptionAlert> alerts = service.getOpenExceptionAlerts();
        assertNotNull(alerts);
        assertEquals(1, alerts.size());
        service.resolveAllExceptionAlerts(TYPE, PROPERTY_ID_PARIS);
        alerts = service.getOpenExceptionAlerts();
        assertNotNull(alerts);
        assertEquals(1, alerts.size());
    }

    @Test
    public void resolveAllAlertTypes() {
        buildExceptionAlert(DESCRIPTION, DETAILS, TYPE, ExceptionSubType.LRV);
        List<ExceptionAlert> alerts = service.getOpenExceptionAlerts();
        assertNotNull(alerts);
        assertEquals(1, alerts.size());
        service.resolveAllExceptionAlerts(TYPE, PROPERTY_ID_PARIS);
        alerts = service.getOpenExceptionAlerts();
        assertNotNull(alerts);
        assertEquals(1, alerts.size());
    }

    @Test
    public void testSuspendOrRevertRaisedException() {
        buildExceptionAlert(DESCRIPTION, DETAILS, TYPE, ExceptionSubType.LRV);
        List<ExceptionAlert> alerts = service.getOpenExceptionAlerts();
        ExceptionAlert alert = alerts.get(0);
        service.suspendOrRevertRaisedException(alert.getAlertId(), alert.getPropertyId(), alert.getSteps().get(0).getStepId(), true);
        List<InfoMgrExcepNotifSkipDatesEntity> listSkipDates = tenantCrudService().findAll(InfoMgrExcepNotifSkipDatesEntity.class);
        assertNotNull(listSkipDates);
    }

    @Test
    public void testSearchRaisedExceptionsWithCriteria() {
        setUpContext();
        RoleService roleService = Mockito.mock(RoleService.class);
        inject(authService, "roleService", roleService);
        when(roleService.getPermsForUser(anyString(), anyString(), anyObject())).thenReturn(Sets.newHashSet("-666"));
        buildExceptionAlert(DESCRIPTION, DETAILS, TYPE, ExceptionSubType.LRV);
        SearchCriteriaDTO searchCriteria = new SearchCriteriaDTO();
        List<Integer> propertyIds = new ArrayList<Integer>();
        propertyIds.add(PROPERTY_ID_PARIS);
        propertyIds.add(5);
        searchCriteria.setPropertyIds(propertyIds);
        List<Integer> exceptionTypes = new ArrayList<Integer>();
        exceptionTypes.add(17);
        searchCriteria.setExceptionTypes(exceptionTypes);
        List<Integer> status = new ArrayList<Integer>();
        status.add(1);
        searchCriteria.setExceptionStatus(status);
        List<Integer> score = new ArrayList<Integer>();
        score.add(1);
        score.add(2);
        score.add(3);
        searchCriteria.setScores(score);
        when(mockConfigParamsService.getValue("pacman.BSTN.H2", IntegrationConfigParamName.CORE_PROPERTY_TIME_ZONE.value())).thenReturn("America/Chicago");
        List<ExceptionAlert> listFoundExceptions = service.searchNotificationsRaisedWithCriteria(searchCriteria);
        assertTrue(listFoundExceptions.size() > 0);
    }

    @Test
    public void testMakeNoChange() {
        buildExceptionAlert(DESCRIPTION, DETAILS, TYPE, ExceptionSubType.LRV);
        setStatusCacheMock();
        List<ExceptionAlert> alerts = service.getOpenExceptionAlerts();
        ExceptionAlert alert = alerts.get(0);
        service.makeNoChange(alert.getAlertId(), alert.getPropertyId(), alert.getSteps().get(0).getStepId());
        InfoMgrInstanceEntity objAlertEntity = tenantCrudService().find(InfoMgrInstanceEntity.class, alert.getAlertId());
        assertEquals(Constants.ALERT_STATUS_RESOLVED_ID, objAlertEntity.getAlertStatus().getId().intValue());
        assertEquals(0, objAlertEntity.getScore());
    }

    private void setStatusCacheMock() {
        InfoMgrStatusEntityCache infoMgrStatusEntityCache = Mockito.mock(InfoMgrStatusEntityCache.class);
        Mockito.when(infoMgrStatusEntityCache.get(anyString())).thenReturn(tenantCrudService().find(InfoMgrStatusEntity.class, Constants.ALERT_STATUS_RESOLVED_ID));
        service.setInfoMgrStatusEntityCache(infoMgrStatusEntityCache);
    }

    @Test
    public void testgetStep() {
        buildExceptionAlert(DESCRIPTION, DETAILS, TYPE, ExceptionSubType.LRV);
        setStatusCacheMock();
        List<ExceptionAlert> alerts = service.getOpenExceptionAlerts();
        ExceptionAlert alert = alerts.get(0);
        service.makeNoChange(alert.getAlertId(), alert.getPropertyId(), alert.getSteps().get(0).getStepId());
        InfoMgrInstanceEntity objAlertEntity = tenantCrudService().find(InfoMgrInstanceEntity.class, alert.getAlertId());
        assertEquals(Constants.ALERT_STATUS_RESOLVED_ID, objAlertEntity.getAlertStatus().getId().intValue());
        assertEquals(0, objAlertEntity.getScore());
        @SuppressWarnings("unchecked")
        List<InfoMgrInstanceStepStateEntity> stepsEntity = tenantCrudService().findByNamedQuery(InfoMgrInstanceStepStateEntity.BY_ALERT_INSTANCE, QueryParameter.with("instanceId", objAlertEntity.getId()).parameters());
        assertEquals(8, stepsEntity.size());
    }

    @Test
    public void testSearchNotificationWithCriteria() {
        setUpContext();
        RoleService roleService = Mockito.mock(RoleService.class);
        inject(authService, "roleService", roleService);
        when(roleService.getPermsForUser(anyString(), anyString(), anyObject())).thenReturn(Sets.newHashSet("-666"));
        buildExceptionAlert(DESCRIPTION, DETAILS, TYPE, ExceptionSubType.LRV);
        when(mockConfigParamsService.getValue("pacman.BSTN.H2", IntegrationConfigParamName.CORE_PROPERTY_TIME_ZONE.value())).thenReturn("America/Chicago");
        List<ExceptionAlert> listFoundExceptions = service.searchNotificationsRaisedWithCriteria(null);
    }

    private ExceptionConfigDTO buildExceptionConfiguration(String subType, String levelType, String sublevelId, List<Integer> listPropertyIds, boolean subLevelKeywordUsed) {
        ExceptionConfigDTO objExceptionConfigDTO = new ExceptionConfigDTO();
        InfoMgrTypeEntity type = tenantCrudService().findByNamedQuerySingleResult(InfoMgrTypeEntity.BY_NAME, QueryParameter.with("name", AlertType.DecisionChangeEx.toString()).parameters());
        InformationMgrLevelEntity objExceptionLevelEntity = tenantCrudService().findByNamedQuerySingleResult(InformationMgrLevelEntity.BY_NAME, QueryParameter.with("name", levelType).parameters());
        InformationMgrSubTypeEntity objExceptionSubTypeEntity = tenantCrudService().findByNamedQuerySingleResult(InformationMgrSubTypeEntity.BY_NAME, QueryParameter.with("name", subType).parameters());
        // InformationMgrSubLevelEntity objSublevel = (InformationMgrSubLevelEntity)crudService.findByNamedQuerySingleResult(InformationMgrSubLevelEntity.BY_NAME, QueryParameter.with("name", sublevelType).parameters());
        objExceptionConfigDTO.setAlertTypeEntity(type);
        objExceptionConfigDTO.setDisabled(false);
        objExceptionConfigDTO.setEndDate(TODAY7);
        objExceptionConfigDTO.setExceptionLevel(objExceptionLevelEntity);
        objExceptionConfigDTO.setExceptionSubLevel(sublevelId);
        objExceptionConfigDTO.setExceptionSubType(objExceptionSubTypeEntity);
        objExceptionConfigDTO.setFrequency(FREQUENCY);
        objExceptionConfigDTO.setPropertyIds(listPropertyIds);
        objExceptionConfigDTO.setStartDate(TODAY);
        objExceptionConfigDTO.setThresholdConstraint(">=");
        objExceptionConfigDTO.setThresholdValue(new BigDecimal(50));
        objExceptionConfigDTO.setStatusId(1);
        objExceptionConfigDTO.setMetricType(MetricType.CURRENCY);
        objExceptionConfigDTO.setSubLevelHasKeyword(subLevelKeywordUsed);
        return objExceptionConfigDTO;
    }

    @Test
    public void testRankingPricingInExceptionAlert_DecisionAsOfLastNightlyOptimization() {
        ExceptionAlert alert = new ExceptionAlert();
        alert.setCurrentOptimizationValue("BAR6");
        alert.setLastOptVal("BAR2");
        alert.setPropertyId(6);
        InformationMgrSubTypeEntity informationMgrSubTypeEntity = new InformationMgrSubTypeEntity();
        informationMgrSubTypeEntity.setName("Pricing");
        alert.setSubType(informationMgrSubTypeEntity);
        alert.setThresholdOperator("<>");
        alert.setLastOptValueForPricing("4");
        alert.setThresholdValue(new BigDecimal(2));
        alert.setType(AlertType.DecisionAsOfLastNightlyOptimization);
        service.setRankingForPricingException(alert);
        assertTrue(alert.getCurrentOptValueForPricing() != null);
        assertTrue(alert.getLastOptValueForPricing() != null);
    }

    @Test
    public void testRankingPricingInExceptionAlert_DecisionAsOfLastOptimization() {
        ExceptionAlert alert = new ExceptionAlert();
        alert.setCurrentOptimizationValue("BAR6");
        alert.setLastOptVal("BAR2");
        alert.setPropertyId(6);
        InformationMgrSubTypeEntity informationMgrSubTypeEntity = new InformationMgrSubTypeEntity();
        informationMgrSubTypeEntity.setName("Pricing");
        alert.setSubType(informationMgrSubTypeEntity);
        alert.setThresholdOperator("<>");
        alert.setLastOptValueForPricing("4");
        alert.setThresholdValue(new BigDecimal(2));
        alert.setType(AlertType.DecisionAsOfLastOptimization);
        service.setRankingForPricingException(alert);
        assertTrue(alert.getCurrentOptValueForPricing() != null);
        assertTrue(alert.getLastOptValueForPricing() != null);
    }

    @Test
    public void testGetExcepOrNotifById() {
        setUpContext();
        RoleService roleService = Mockito.mock(RoleService.class);
        inject(authService, "roleService", roleService);
        when(roleService.getPermsForUser(anyString(), anyString(), anyObject())).thenReturn(Sets.newHashSet("-666"));
        InfoMgrExcepNotifEntity alert = buildExceptionAlert(DESCRIPTION, DETAILS, TYPE, ExceptionSubType.LRV);
        ExceptionAlert objExceptionAlert = service.getExcepOrNotifById(alert.getId(), alert.getPropertyId());
        Property expectedProperty = globalCrudService().find(Property.class, alert.getPropertyId());
        assertNotNull(objExceptionAlert);
        assertEquals(alert.getId(), objExceptionAlert.getAlertId());
        assertEquals(Constants.EXCEPTION_CATEGORY, objExceptionAlert.getCategory());
        assertNotNull(objExceptionAlert.getPropertyName());
        assertNotNull(objExceptionAlert.getPropertyCode());
        assertEquals(expectedProperty.getName(), objExceptionAlert.getPropertyName());
        assertEquals(expectedProperty.getCode(), objExceptionAlert.getPropertyCode());
    }

    private void setUpContext() {
        authService.setUserGlobalDBService(mockUserGlobalDBService);
        when(mockUserGlobalDBService.getGlobalUserById(anyInt())).thenReturn(getInternalUser(WC_USER_ID_SSO));
        inject(userService, "userGlobalDBService", mockUserGlobalDBService);
        createTetrisPrincipal(WC_DN_REGULAR_USER, WC_CN_REGULAR_USER, WC_USER_ID_SSO, WC_CLIENT_CODE_TEST, false);
        workContext().setUserId(WC_USER_ID_SSO);
        workContext().setPropertyId(PROPERTY_ID_PARIS);
    }

    @Test
    public void testGetPropertiesForFilter() {
        List<Property> propertyIds = service.getPropertiesForFilter();
        assertNotNull(propertyIds);
    }

    @Test
    public void shouldGetPropertiesForFilterWithSortedOrder() {
        PropertyGroupService mockPropertyGroupService = mock(PropertyGroupService.class);
        when(mockPropertyGroupService.getPropertyContextAsList()).thenReturn(Arrays.asList(5, 6, 10));
        Property propertyHiltonParis = globalCrudService().find(Property.class, 6);
        Property propertyHiltonPune = globalCrudService().find(Property.class, 5);
        Property propertySheraton = globalCrudService().find(Property.class, 10);
        propertyHiltonParis.setDisplayLabelField(propertyHiltonParis.getName());
        propertyHiltonPune.setDisplayLabelField(propertyHiltonPune.getName());
        propertySheraton.setDisplayLabelField(propertySheraton.getName());
        List<Property> mockedProperties = Arrays.asList(propertySheraton, propertyHiltonPune, propertyHiltonParis);
        when(propertyService.getPropertiesWithDisplayLabelFieldByIds(Arrays.asList(5, 6, 10))).thenReturn(mockedProperties);
        service.propertyGroupService = mockPropertyGroupService;
        List<Property> propertiesForFilter = service.getPropertiesForFilter();
        assertEquals(propertySheraton.getId(), propertiesForFilter.get(0).getId());
        assertEquals(propertyHiltonPune.getId(), propertiesForFilter.get(1).getId());
        assertEquals(propertyHiltonParis.getId(), propertiesForFilter.get(2).getId());
    }

    @Test
    public void testSearchNotificationWithCriteriaNew() {
        buildExceptionAlert(DESCRIPTION, DETAILS, TYPE, ExceptionSubType.LRV);
        when(mockConfigParamsService.getValue("pacman.BSTN.H2", IntegrationConfigParamName.CORE_PROPERTY_TIME_ZONE.value())).thenReturn("America/Chicago");
        property.setDisplayLabelField(property.getName());
        List<ExceptionAlert> listFoundExceptions = service.getNotificationsWithSearchCriteria(null);
        assertEquals(1, listFoundExceptions.size());
        assertNull(listFoundExceptions.get(0).getCurrentOptimizationValue());
        assertNull(listFoundExceptions.get(0).getLastOptVal());
        assertNull(listFoundExceptions.get(0).getDetailsMetadata());
        assertNull(listFoundExceptions.get(0).getDetailsByLOS());
        assertNotNull(listFoundExceptions.get(0).getPropertyCode());
        assertNotNull(listFoundExceptions.get(0).getPropertyName());
        assertNotNull(listFoundExceptions.get(0).getPropertyDisplayLabelField());
    }

    @Test
    public void shouldNotFetchSnoozedExceptionsForDefaultCriteria() {
        InfoMgrExcepNotifEntity excepNotifEntity = buildExceptionAlert(DESCRIPTION, DETAILS, TYPE, ExceptionSubType.LRV);
        InfoMgrStatusEntity snoozedStatusEntity = tenantCrudService().find(InfoMgrStatusEntity.class, Constants.ALERT_STATUS_SNOOZED_ID);
        excepNotifEntity.setAlertStatus(snoozedStatusEntity);
        tenantCrudService().save(excepNotifEntity);
        when(mockConfigParamsService.getValue("pacman.BSTN.H2", IntegrationConfigParamName.CORE_PROPERTY_TIME_ZONE.value())).thenReturn("America/Chicago");
        property.setDisplayLabelField(property.getName());
        SearchCriteriaDTO searchCriteriaDTO = new SearchCriteriaDTO();
        List<ExceptionAlert> listFoundExceptions = service.getNotificationsWithSearchCriteria(searchCriteriaDTO);
        assertEquals(0, listFoundExceptions.size());
    }

    @Test
    public void testGetInstancesSortedOnScore() {
        buildExceptionAlert(DESCRIPTION, DETAILS, TYPE, ExceptionSubType.LRV);
        when(mockConfigParamsService.getValue("pacman.BSTN.H2", IntegrationConfigParamName.CORE_PROPERTY_TIME_ZONE.value())).thenReturn("America/Chicago");
        List<InfoMgrExcepNotifEntity> listFoundExceptions = service.getInstancesSortedOnScore(null, Constants.EXCEPTION_CATEGORY);
        assertEquals(1, listFoundExceptions.size());
    }

    @Test
    public void testGetExtendedDetailsForInstance() {
        setUpContext();
        RoleService roleService = Mockito.mock(RoleService.class);
        inject(authService, "roleService", roleService);
        when(roleService.getPermsForUser(anyString(), anyString(), anyObject())).thenReturn(Sets.newHashSet("-666"));
        buildExceptionAlert(DESCRIPTION, DETAILS, TYPE, ExceptionSubType.LRV);
        List<ExceptionAlert> alerts = service.getOpenExceptionAlerts();
        ExceptionAlert exceptionAlert = alerts.get(0);
        InfoMgrExcepNotifEntity infoMgrExcepNotifEntity = tenantCrudService().find(InfoMgrExcepNotifEntity.class, exceptionAlert.getAlertId());
        infoMgrExcepNotifEntity.setCurrentOptimizationValue("4085.47");
        infoMgrExcepNotifEntity.setLastOptimizationValue("3729.39");
        tenantCrudService().save(infoMgrExcepNotifEntity);
        exceptionAlert = service.getExtendedDetailsForInstance(exceptionAlert);
        assertNotNull(exceptionAlert.getDetailsMetadata());
        assertNotNull(exceptionAlert.getSteps());
        assertNotNull(exceptionAlert.getCurrentOptimizationValue());
        assertNotNull(exceptionAlert.getLastOptVal());
    }

    @Test
    public void testGetExtendedDetailsForInstanceForCPEnabledProperties() {
        setUpContext();
        GlobalUser extUser = getInternalUser(WC_USER_ID_SSO);
        extUser.setInternal(false);
        when(mockUserGlobalDBService.getGlobalUserById(anyInt())).thenReturn(extUser);
        when(pacmanConfigParamsService.getBooleanParameterValue("pacman.feature.PricingRedesignEnabled")).thenReturn(true);
        RoleService roleService = Mockito.mock(RoleService.class);
        inject(authService, "roleService", roleService);
        Set<String> pages = new HashSet(Arrays.asList("pageCode=pricing&access=readWrite"));
        when(roleService.getPermsForUser(anyString(), anyString(), anyObject())).thenReturn(pages);
        buildExceptionAlert(DESCRIPTION, DETAILS, TYPE, ExceptionSubType.LRV);
        List<ExceptionAlert> alerts = service.getOpenExceptionAlerts();
        ExceptionAlert exceptionAlert = alerts.get(0);
        InfoMgrExcepNotifEntity infoMgrExcepNotifEntity = tenantCrudService().find(InfoMgrExcepNotifEntity.class, exceptionAlert.getAlertId());
        infoMgrExcepNotifEntity.setCurrentOptimizationValue("4085.47");
        infoMgrExcepNotifEntity.setLastOptimizationValue("3729.39");
        tenantCrudService().save(infoMgrExcepNotifEntity);
        exceptionAlert = service.getExtendedDetailsForInstance(exceptionAlert);
        assertEquals(true, exceptionAlert.getSteps().get(1).isHasPermission());
    }

    @Test
    public void testSetDetailsMetaDataForExceptionAlertInstance() {
        InfoMgrExcepNotifEntity infoMgrExcepNotifEntity = buildExceptionAlert(DESCRIPTION, DETAILS, TYPE, ExceptionSubType.LRV);
        List<ExceptionAlert> alerts = service.getOpenExceptionAlerts();
        ExceptionAlert exceptionAlert = alerts.get(0);
        service.setDetailsMetaDataForExceptionAlertInstance(infoMgrExcepNotifEntity, exceptionAlert);
        assertNotNull(exceptionAlert.getDetailsMetadata());
        assertNull(exceptionAlert.getDetailsByLOS());
    }

    @Test
    public void testSetDetailsMetaDataForCompetitiveNotification() {
        InfoMgrExcepNotifEntity infoMgrExcepNotifEntity = buildExceptionAlert(DESCRIPTION, DETAILS, TYPE, ExceptionSubType.LRV);
        List<ExceptionAlert> alerts = service.getOpenExceptionAlerts();
        ExceptionAlert exceptionAlert = alerts.get(0);

        exceptionAlert.getSubType().setName("COMP_SUBTYPE");
        exceptionAlert.setProduct(getProduct(false, false, true));

        when(mockConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.AGILE_RATES_ENABLED)).thenReturn(true);
        when(mockConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.ENABLE_AGILE_NOTIFICATIONS)).thenReturn(true);
        when(mockConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_PRODUCT_DIMENSION_FOR_COMPETITIVE_NOTIFICATIONS)).thenReturn(true);

        service.setDetailsMetaDataForExceptionAlertInstance(infoMgrExcepNotifEntity, exceptionAlert);
        assertNotNull(exceptionAlert.getDetailsMetadata());
        assertTrue(exceptionAlert.getDetailsMetadata().get(0).contains("agile.rates.product.name"));
    }

    @Test
    public void testSetDetailsMetaDataForIndependentProduct() {
        InfoMgrExcepNotifEntity infoMgrExcepNotifEntity = buildExceptionAlert(DESCRIPTION, DETAILS, TYPE, ExceptionSubType.LRV);
        ExceptionAlert exceptionAlert = getExceptionAlertByProductType(true, false, false);

        service.setDetailsMetaDataForExceptionAlertInstance(infoMgrExcepNotifEntity, exceptionAlert);
        assertNotNull(exceptionAlert.getDetailsMetadata());
        assertTrue(exceptionAlert.getDetailsMetadata().get(0).contains("v-icon light-purple-icon"));
    }

    @Test
    public void testSetDetailsMetaDataForOptimizedProduct() {
        InfoMgrExcepNotifEntity infoMgrExcepNotifEntity = buildExceptionAlert(DESCRIPTION, DETAILS, TYPE, ExceptionSubType.LRV);
        ExceptionAlert exceptionAlert = getExceptionAlertByProductType(false, true, false);

        service.setDetailsMetaDataForExceptionAlertInstance(infoMgrExcepNotifEntity, exceptionAlert);
        assertNotNull(exceptionAlert.getDetailsMetadata());
        assertTrue(exceptionAlert.getDetailsMetadata().get(0).contains("v-icon SMALL green-color FontAwesome"));
    }

    @Test
    public void testSetDetailsMetaDataForLinkedProduct() {
        InfoMgrExcepNotifEntity infoMgrExcepNotifEntity = buildExceptionAlert(DESCRIPTION, DETAILS, TYPE, ExceptionSubType.LRV);
        ExceptionAlert exceptionAlert = getExceptionAlertByProductType(false, false, true);

        service.setDetailsMetaDataForExceptionAlertInstance(infoMgrExcepNotifEntity, exceptionAlert);
        assertNotNull(exceptionAlert.getDetailsMetadata());
        assertTrue(exceptionAlert.getDetailsMetadata().get(0).contains("v-icon orange-icon FontAwesome"));
    }

    @Test
    public void testSetDetailsMetaDataForSystemDefaultProduct() {
        InfoMgrExcepNotifEntity infoMgrExcepNotifEntity = buildExceptionAlert(DESCRIPTION, DETAILS, TYPE, ExceptionSubType.LRV);
        ExceptionAlert exceptionAlert = getExceptionAlertByProductType(false, false, false);

        service.setDetailsMetaDataForExceptionAlertInstance(infoMgrExcepNotifEntity, exceptionAlert);
        assertNotNull(exceptionAlert.getDetailsMetadata());
        assertFalse(exceptionAlert.getDetailsMetadata().get(0).contains("v-icon"));
    }

    @Test
    public void testSetDetailsMetaDataForExceptionAlertInstance_DetailsByLOS() {
        InfoMgrExcepNotifEntity infoMgrExcepNotifEntity = buildExceptionAlert(AlertType.LRVExceedingUserDefinedBAR.getDescription(), DETAILS, AlertType.LRVExceedingUserDefinedBAR, ExceptionSubType.LRV);
        ExceptionAlert exceptionAlert = service.findByAlertId(infoMgrExcepNotifEntity.getId());
        service.setDetailsMetaDataForExceptionAlertInstance(infoMgrExcepNotifEntity, exceptionAlert);
        assertNull(exceptionAlert.getDetailsMetadata());
        assertNotNull(exceptionAlert.getDetailsByLOS());
    }

    @Test
    public void testSetDetailsMetaDataForExceptionAlertInstance_DetailsByLOSCeiling() {
        InfoMgrExcepNotifEntity infoMgrExcepNotifEntity = buildExceptionAlert(AlertType.LRVExceedingCeilingBAR.getDescription(), DETAILS, AlertType.LRVExceedingCeilingBAR, ExceptionSubType.LRV);
        ExceptionAlert exceptionAlert = service.findByAlertId(infoMgrExcepNotifEntity.getId());
        service.setDetailsMetaDataForExceptionAlertInstance(infoMgrExcepNotifEntity, exceptionAlert);
        assertNull(exceptionAlert.getDetailsMetadata());
        assertNotNull(exceptionAlert.getDetailsByLOS());
    }

    @Test
    public void testSetDetailsMetaDataForExceptionAlertInstance_DetailsByLOS_TypeDecisionAsOfLastOptimization() {
        String exceptionDetails = "metric:Pricing,occupancyDate:DT_16-Apr-2018_DT,room.class:STANDARD,current.price:LV6,defined.threshold:>= LV8,LOS:1";
        InfoMgrExcepNotifEntity infoMgrExcepNotifEntity = buildExceptionAlert(AlertType.DecisionAsOfLastOptimization.getDescription(), exceptionDetails, AlertType.DecisionAsOfLastOptimization, ExceptionSubType.LRV);
        List<ExceptionAlert> alerts = service.getOpenExceptionAlerts();
        ExceptionAlert exceptionAlert = alerts.get(0);
        service.setDetailsMetaDataForExceptionAlertInstance(infoMgrExcepNotifEntity, exceptionAlert);
        assertNull(exceptionAlert.getDetailsByLOS());
        assertNotNull(exceptionAlert.getDetailsMetadata());
        assertTrue(exceptionAlert.getDetailsMetadata().contains("LOS:1"));
    }

    @Test
    public void testSetDetailsMetaDataForExceptionAlertInstance_DetailsByLOS_TypeDecisionAsOfLastNightlyOptimization() {
        String exceptionDetails = "metric:Pricing,occupancyDate:DT_16-Apr-2018_DT,room.class:STANDARD,current.price:LV6,defined.threshold:>= LV8,LOS:1";
        InfoMgrExcepNotifEntity infoMgrExcepNotifEntity = buildExceptionAlert(AlertType.DecisionAsOfLastNightlyOptimization.getDescription(), exceptionDetails, AlertType.DecisionAsOfLastNightlyOptimization, ExceptionSubType.LRV);
        List<ExceptionAlert> alerts = service.getOpenExceptionAlerts();
        ExceptionAlert exceptionAlert = alerts.get(0);
        service.setDetailsMetaDataForExceptionAlertInstance(infoMgrExcepNotifEntity, exceptionAlert);
        assertNull(exceptionAlert.getDetailsByLOS());
        assertNotNull(exceptionAlert.getDetailsMetadata());
        assertTrue(exceptionAlert.getDetailsMetadata().contains("LOS:1"));
    }

    private GlobalUser getInternalUser(String wcUserIdSuperDuper) {
        GlobalUser user = new GlobalUser();
        user.setInternal(true);
        user.setId(Integer.parseInt(wcUserIdSuperDuper));
        user.setClientCode(Constants.CLIENT_INTERNAL);
        return user;
    }

    @Test
    public void testCreateExceptionHistoryForNewExceptionShouldCreateHistory() {
        WorkContextType workContext = new WorkContextType();
        workContext.setUserId(CREATED_BY);
        workContext.setPropertyId(PROPERTY_ID_PARIS);
        workContext.setPropertyCode("");
        List<Integer> listPropertyIds = new ArrayList<Integer>();
        listPropertyIds.add(PROPERTY_ID_PARIS);
        Map<String, List<InformationMgrAlertConfigEntity>> resultList = exceptionConfigService.persistExceptionConfiguration(buildExceptionConfiguration(ExceptionSubType.LRV.getCode(), LevelType.ROOM_CLASS.getCode(), SubLevelType.MASTER_CLASS.getCode(), listPropertyIds, true));
        tenantCrudService().flush();
        List<InformationMgrAlertConfigEntity> list = resultList.get("SUCCESS");
        InfoMgrTypeEntity alertTypeEntity = new InfoMgrTypeEntity();
        alertTypeEntity.setDescription(DESCRIPTION);
        alertTypeEntity.setBaseScore(SCORE);
        alertTypeEntity.setAlertCategory(Constants.EXCEPTION_CATEGORY);
        Date occupancyDate = new Date();
        InformationMgrSubTypeEntity subType = tenantCrudService().findByNamedQuerySingleResult(InformationMgrSubTypeEntity.BY_NAME, QueryParameter.with("name", ExceptionSubType.LRV.getCode()).parameters());
        list.get(0).setExceptionSubType(subType);
        InfoMgrExcepNotifEntity exceptionAlert = service.createExceptionAlert(alertTypeEntity, DESCRIPTION, AlertType.OOORoomsAffectingDemand, occupancyDate, list.get(0), null);
        exceptionAlert = tenantCrudService().save(exceptionAlert);
        ArrayList<ExceptionAlert> listOfExceptionCreatedorUpdated = new ArrayList<>();
        service.createAndSaveExceptionHistory(listOfExceptionCreatedorUpdated, null, Arrays.asList(exceptionAlert));
        assertEquals(1, listOfExceptionCreatedorUpdated.size());
        assertEquals(AlertType.OOORoomsAffectingDemand.getDescription(), listOfExceptionCreatedorUpdated.get(0).getDescription());
        List<Object[]> historyList = tenantCrudService().findByNativeQuery("select * from Info_Mgr_History");
        assertEquals(SCORE, historyList.get(0)[5]);
        assertEquals(listOfExceptionCreatedorUpdated.get(0).getAlertId(), historyList.get(0)[1]);
    }

    @Test
    public void testCreateExceptionHistoryForExistingExceptionShouldCreateHistory() {
        WorkContextType workContext = new WorkContextType();
        workContext.setUserId(CREATED_BY);
        workContext.setPropertyId(PROPERTY_ID_PARIS);
        workContext.setPropertyCode("");
        List<Integer> listPropertyIds = new ArrayList<Integer>();
        listPropertyIds.add(PROPERTY_ID_PARIS);
        Map<String, List<InformationMgrAlertConfigEntity>> resultList = exceptionConfigService.persistExceptionConfiguration(buildExceptionConfiguration(ExceptionSubType.LRV.getCode(), LevelType.ROOM_CLASS.getCode(), SubLevelType.MASTER_CLASS.getCode(), listPropertyIds, true));
        tenantCrudService().flush();
        List<InformationMgrAlertConfigEntity> list = resultList.get("SUCCESS");
        InfoMgrTypeEntity alertTypeEntity = new InfoMgrTypeEntity();
        alertTypeEntity.setDescription(DESCRIPTION);
        alertTypeEntity.setBaseScore(SCORE);
        alertTypeEntity.setAlertCategory(Constants.EXCEPTION_CATEGORY);
        LocalDate occupancyDate = new LocalDate();
        InformationMgrSubTypeEntity subType = tenantCrudService().findByNamedQuerySingleResult(InformationMgrSubTypeEntity.BY_NAME, QueryParameter.with("name", ExceptionSubType.LRV.getCode()).parameters());
        list.get(0).setExceptionSubType(subType);
        InfoMgrExcepNotifEntity exceptionAlert = service.createExceptionAlert(alertTypeEntity, DESCRIPTION, AlertType.OOORoomsAffectingDemand, occupancyDate.toDate(), list.get(0), null);
        exceptionAlert = tenantCrudService().save(exceptionAlert);
        InfoMgrExcepNotifEntity finalExceptionAlert = exceptionAlert;
        Map<Date, InfoMgrExcepNotifEntity> existingException = new HashMap() {

            {
                put(occupancyDate.toDate(), finalExceptionAlert);
            }
        };
        ArrayList<ExceptionAlert> listOfExceptionCreatedorUpdated = new ArrayList<>();
        service.createAndSaveExceptionHistory(listOfExceptionCreatedorUpdated, existingException, Arrays.asList(exceptionAlert));
        assertEquals(1, listOfExceptionCreatedorUpdated.size());
        assertEquals(AlertType.OOORoomsAffectingDemand.getDescription(), listOfExceptionCreatedorUpdated.get(0).getDescription());
        List<Object[]> historyList = tenantCrudService().findByNativeQuery("select * from Info_Mgr_History");
        assertEquals(listOfExceptionCreatedorUpdated.get(0).getAlertId(), historyList.get(0)[1]);
    }

    @Test
    public void testGetExtendedDetailsForAlertsList() {
        setUpContext();
        RoleService roleService = Mockito.mock(RoleService.class);
        inject(authService, "roleService", roleService);
        when(roleService.getPermsForUser(anyString(), anyString(), anyObject())).thenReturn(Sets.newHashSet("-666"));
        buildExceptionAlert(DESCRIPTION, DETAILS, TYPE, ExceptionSubType.LRV);
        buildExceptionAlert(DESCRIPTION, DETAILS, TYPE, ExceptionSubType.PRICING);
        List<ExceptionAlert> alerts = service.getOpenExceptionAlerts();
        InfoMgrExcepNotifEntity infoMgrExcepNotifEntity = tenantCrudService().find(InfoMgrExcepNotifEntity.class, alerts.get(0).getAlertId());
        infoMgrExcepNotifEntity.setCurrentOptimizationValue("4085.47");
        infoMgrExcepNotifEntity.setLastOptimizationValue("3729.39");
        tenantCrudService().save(infoMgrExcepNotifEntity);
        InfoMgrExcepNotifEntity infoMgrExcepNotifEntity2 = tenantCrudService().find(InfoMgrExcepNotifEntity.class, alerts.get(1).getAlertId());
        infoMgrExcepNotifEntity2.setCurrentOptimizationValue("5000.42");
        infoMgrExcepNotifEntity2.setLastOptimizationValue("3050.39");
        tenantCrudService().save(infoMgrExcepNotifEntity2);
        List<ExceptionAlert> alertResults = service.getExtendedDetailsForExceptionAlertsList(alerts);
        assertNotNull(alertResults.get(0).getDetailsMetadata());
        assertNotNull(alertResults.get(0).getSteps());
        assertNotNull(alertResults.get(1).getDetailsMetadata());
        assertNotNull(alertResults.get(1).getSteps());
    }

    private ExceptionAlert getExceptionAlertByProductType(boolean isIndependentProduct, boolean isOptimizedProduct, boolean isLinkedProduct) {
        List<ExceptionAlert> alerts = service.getOpenExceptionAlerts();
        ExceptionAlert exceptionAlert = alerts.get(0);
        exceptionAlert.getSubType().setName("PRICING_BY_VALUE");
        exceptionAlert.setProduct(getProduct(isIndependentProduct, isOptimizedProduct, isLinkedProduct));

        when(mockConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.AGILE_RATES_ENABLED)).thenReturn(true);
        when(mockConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.ENABLE_AGILE_NOTIFICATIONS)).thenReturn(true);
        when(mockConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.INDEPENDENT_PRODUCTS_ENABLED)).thenReturn(isIndependentProduct || isLinkedProduct);
        return exceptionAlert;
    }

    private Product getProduct(boolean isIndependentProduct, boolean isOptimizedProduct, boolean isLinkedProduct) {
        Product product = new Product();
        product.setId(1);
        product.setName("Product");
        if (isIndependentProduct) {
            product.setCode("INDEPENDENT");
        } else if (isOptimizedProduct) {
            product.setOptimized(true);
        } else if (!isLinkedProduct) {
            product.setSystemDefault(true);
        }
        return product;
    }
}
