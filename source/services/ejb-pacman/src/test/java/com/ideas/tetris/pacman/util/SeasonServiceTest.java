package com.ideas.tetris.pacman.util;

import com.ideas.tetris.pacman.services.agilerates.configuration.dto.AgileRatesSeason;
import com.ideas.tetris.pacman.services.agilerates.configuration.entity.ProductRateOffset;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.grouppricing.configuration.entity.GroupPricingConfigurationAncillaryAssignmentSeason;
import com.ideas.tetris.pacman.services.grouppricing.configuration.entity.GroupPricingConfigurationMARSeason;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import org.joda.time.LocalDate;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import javax.persistence.EntityManager;
import java.math.BigDecimal;
import java.time.Month;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

public class SeasonServiceTest {
    @Mock
    DateService dateService;

    @InjectMocks
    SeasonService service;

    @BeforeEach
    public void setUp() {
        service = new SeasonService();

        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void removeAndJoinWithEmptyList() {
        List<GroupPricingConfigurationMARSeason> retVal = new ArrayList<GroupPricingConfigurationMARSeason>();

        service.removeAndJoin(retVal, null, new LocalDate(2012, 12, 31));
        assertEquals(0, retVal.size());
    }

    @Test
    public void removeAndJoinWithSingleSeasonAfterCaughtUpDate() {
        List<GroupPricingConfigurationMARSeason> seasons = new ArrayList<GroupPricingConfigurationMARSeason>();

        GroupPricingConfigurationMARSeason season = new GroupPricingConfigurationMARSeason();
        season.setStartDate(new LocalDate(2013, 1, 1));
        season.setEndDate(new LocalDate(2013, 1, 10));
        seasons.add(season);

        Mockito.when(dateService.getCaughtUpDate()).thenReturn(new LocalDate(2012, 12, 31).toDate());

        service.removeAndJoin(seasons, season, new LocalDate(2012, 12, 31));
        assertEquals(0, seasons.size());
    }

    @Test
    public void removeAndJoinWithSingleSeasonWithSameStartDateAsCaughtUpDate() {
        List<GroupPricingConfigurationMARSeason> seasons = new ArrayList<GroupPricingConfigurationMARSeason>();

        GroupPricingConfigurationMARSeason season = new GroupPricingConfigurationMARSeason();
        season.setStartDate(new LocalDate(2013, 1, 1));
        season.setEndDate(new LocalDate(2013, 1, 10));
        seasons.add(season);

        Mockito.when(dateService.getCaughtUpDate()).thenReturn(new LocalDate(2013, 1, 1).toDate());

        service.removeAndJoin(seasons, season, new LocalDate(2012, 12, 31));
        assertEquals(0, seasons.size());
    }

    @Test
    public void removeAndJoinWithSingleSeasonWithStartDateAfterCaughtUpDate() {
        List<GroupPricingConfigurationMARSeason> seasons = new ArrayList<GroupPricingConfigurationMARSeason>();

        GroupPricingConfigurationMARSeason season = new GroupPricingConfigurationMARSeason();
        season.setStartDate(new LocalDate(2013, 1, 1));
        season.setEndDate(new LocalDate(2013, 1, 10));
        seasons.add(season);

        Mockito.when(dateService.getCaughtUpDate()).thenReturn(new LocalDate(2012, 1, 1).toDate());

        service.removeAndJoin(seasons, season, new LocalDate(2012, 12, 31));
        assertEquals(0, seasons.size());
    }

    @Test
    public void updateEndDateIfSeasonInPastIfCaughtUpDateBeforeEndDate() {
        GroupPricingConfigurationMARSeason season = new GroupPricingConfigurationMARSeason();
        season.setStartDate(DateUtil.convertJavaToJodaLocalDate(java.time.LocalDate.of(2017, Month.JANUARY, 1)));
        season.setEndDate(DateUtil.convertJavaToJodaLocalDate(java.time.LocalDate.of(2017, Month.JANUARY, 10)));

        service.updateEndDateIfSeasonInPast(java.time.LocalDate.of(2017, Month.JANUARY, 6), season);
        assertEquals(java.time.LocalDate.of(2017, Month.JANUARY, 6).minusDays(1),
                DateUtil.convertJodaToJavaLocalDate(season.getEndDate()));
    }

    @Test
    public void removeAndJoinWithPartialSeasonBeingLeft() {
        List<GroupPricingConfigurationMARSeason> seasons = new ArrayList<GroupPricingConfigurationMARSeason>();

        GroupPricingConfigurationMARSeason season = new GroupPricingConfigurationMARSeason();
        season.setStartDate(new LocalDate(2013, 9, 7));
        season.setEndDate(new LocalDate(2013, 9, 15));
        seasons.add(season);

        service.removeAndJoin(seasons, season, new LocalDate(2013, 9, 11));
        assertEquals(1, seasons.size());

        GroupPricingConfigurationMARSeason foundSeason = seasons.get(0);
        assertEquals(new LocalDate(2013, 9, 11).minusDays(1), foundSeason.getEndDate());
    }

    @Test
    public void removeAndJoinWithSingleSeasonBeforeCaughtUpDate() {
        List<GroupPricingConfigurationMARSeason> seasons = new ArrayList<GroupPricingConfigurationMARSeason>();

        GroupPricingConfigurationMARSeason season = new GroupPricingConfigurationMARSeason();
        season.setStartDate(new LocalDate(2013, 1, 1));
        season.setEndDate(new LocalDate(2013, 1, 10));
        seasons.add(season);

        LocalDate caughtUpDate = new LocalDate(2013, 2, 1);

        service.removeAndJoin(seasons, season, caughtUpDate);
        assertEquals(1, seasons.size());
    }

    @Test
    public void removeAndJoinWithNonConsecutiveSeasons() {
        List<GroupPricingConfigurationMARSeason> seasons = new ArrayList<GroupPricingConfigurationMARSeason>();

        GroupPricingConfigurationMARSeason firstSeason = new GroupPricingConfigurationMARSeason();
        firstSeason.setStartDate(new LocalDate(2013, 1, 1));
        firstSeason.setEndDate(new LocalDate(2013, 1, 10));
        seasons.add(firstSeason);

        GroupPricingConfigurationMARSeason secondSeason = new GroupPricingConfigurationMARSeason();
        secondSeason.setStartDate(new LocalDate(2013, 1, 12));
        secondSeason.setEndDate(new LocalDate(2013, 1, 20));
        seasons.add(secondSeason);

        GroupPricingConfigurationMARSeason thirdSeason = new GroupPricingConfigurationMARSeason();
        thirdSeason.setStartDate(new LocalDate(2013, 1, 22));
        thirdSeason.setEndDate(new LocalDate(2013, 1, 31));
        seasons.add(thirdSeason);

        Mockito.when(dateService.getCaughtUpDate()).thenReturn(new LocalDate(2012, 12, 31).toDate());

        service.removeAndJoin(seasons, secondSeason, new LocalDate(dateService.getCaughtUpDate()));
        assertEquals(2, seasons.size());

        GroupPricingConfigurationMARSeason season1 = seasons.get(0);
        assertEquals(new LocalDate(2013, 1, 1), season1.getStartDate());
        assertEquals(new LocalDate(2013, 1, 10), season1.getEndDate());

        GroupPricingConfigurationMARSeason season2 = seasons.get(1);
        assertEquals(new LocalDate(2013, 1, 22), season2.getStartDate());
        assertEquals(new LocalDate(2013, 1, 31), season2.getEndDate());
    }

    @Test
    public void removeAndDoNotJoinWithConsecutiveSeasonsWithDifferentValues() {
        List<GroupPricingConfigurationMARSeason> seasons = new ArrayList<GroupPricingConfigurationMARSeason>();

        GroupPricingConfigurationMARSeason firstSeason = new GroupPricingConfigurationMARSeason();
        firstSeason.setStartDate(new LocalDate(2013, 1, 1));
        firstSeason.setEndDate(new LocalDate(2013, 1, 10));
        firstSeason.setSundayMAR(BigDecimal.ZERO);
        seasons.add(firstSeason);

        GroupPricingConfigurationMARSeason secondSeason = new GroupPricingConfigurationMARSeason();
        secondSeason.setStartDate(new LocalDate(2013, 1, 11));
        secondSeason.setEndDate(new LocalDate(2013, 1, 21));
        seasons.add(secondSeason);

        GroupPricingConfigurationMARSeason thirdSeason = new GroupPricingConfigurationMARSeason();
        thirdSeason.setStartDate(new LocalDate(2013, 1, 22));
        thirdSeason.setEndDate(new LocalDate(2013, 1, 31));
        seasons.add(thirdSeason);

        Mockito.when(dateService.getCaughtUpDate()).thenReturn(new LocalDate(2012, 12, 31).toDate());

        service.removeAndJoin(seasons, secondSeason, new LocalDate(dateService.getCaughtUpDate()));
        assertEquals(2, seasons.size());

        GroupPricingConfigurationMARSeason season1 = seasons.get(0);
        assertEquals(new LocalDate(2013, 1, 1), season1.getStartDate());
        assertEquals(new LocalDate(2013, 1, 10), season1.getEndDate());

        GroupPricingConfigurationMARSeason season2 = seasons.get(1);
        assertEquals(new LocalDate(2013, 1, 22), season2.getStartDate());
        assertEquals(new LocalDate(2013, 1, 31), season2.getEndDate());
    }

    @Test
    public void applySplitWithEmptyList() {
        List<GroupPricingConfigurationMARSeason> retVal = new ArrayList<GroupPricingConfigurationMARSeason>();

        service.applySplit(retVal, null, new LocalDate(2012, 12, 31));
        assertEquals(0, retVal.size());
    }


    @Test
    public void applySplitWithEditingSameObjectAfterCaughtUpDate() {
        List<GroupPricingConfigurationMARSeason> seasons = new ArrayList<GroupPricingConfigurationMARSeason>();

        GroupPricingConfigurationMARSeason firstSeason = new GroupPricingConfigurationMARSeason();
        firstSeason.setId(1);
        firstSeason.setStartDate(new LocalDate(2013, 1, 10));
        firstSeason.setEndDate(new LocalDate(2013, 1, 20));
        firstSeason.setSundayMAR(BigDecimal.ZERO);
        seasons.add(firstSeason);

        CrudService tenantCrudService = Mockito.mock(CrudService.class);
        Mockito.when(tenantCrudService.find(GroupPricingConfigurationMARSeason.class, 1)).thenReturn(firstSeason);
        service.setTenantCrudService(tenantCrudService);

        EntityManager entityManager = Mockito.mock(EntityManager.class);
        Mockito.when(tenantCrudService.getEntityManager()).thenReturn(entityManager);

        GroupPricingConfigurationMARSeason editedSeason = firstSeason.clone();
        editedSeason.setId(1);
        editedSeason.setStartDate(new LocalDate(2013, 1, 10));
        editedSeason.setEndDate(new LocalDate(2013, 1, 20));
        editedSeason.setSundayMAR(BigDecimal.TEN);

        service.applySplit(seasons, editedSeason, new LocalDate(2013, 1, 15));
        assertEquals(2, seasons.size());
    }

    @Test
    public void applySplitWithEditingSameObjectAfterCaughtUpDateAndSecondEdit() {
        List<GroupPricingConfigurationMARSeason> seasons = new ArrayList<GroupPricingConfigurationMARSeason>();

        GroupPricingConfigurationMARSeason firstSeason = new GroupPricingConfigurationMARSeason();
        firstSeason.setId(1);
        firstSeason.setStartDate(new LocalDate(2013, 1, 10));
        firstSeason.setEndDate(new LocalDate(2013, 1, 20));
        firstSeason.setSundayMAR(BigDecimal.ZERO);
        seasons.add(firstSeason);

        CrudService tenantCrudService = Mockito.mock(CrudService.class);
        Mockito.when(tenantCrudService.find(GroupPricingConfigurationMARSeason.class, 1)).thenReturn(firstSeason);
        service.setTenantCrudService(tenantCrudService);

        EntityManager entityManager = Mockito.mock(EntityManager.class);
        Mockito.when(tenantCrudService.getEntityManager()).thenReturn(entityManager);

        GroupPricingConfigurationMARSeason editedSeason = firstSeason.clone();
        editedSeason.setId(1);
        editedSeason.setStartDate(new LocalDate(2013, 1, 10));
        editedSeason.setEndDate(new LocalDate(2013, 1, 20));
        editedSeason.setSundayMAR(BigDecimal.TEN);

        service.applySplit(seasons, editedSeason, new LocalDate(2013, 1, 15));
        assertEquals(2, seasons.size());

        GroupPricingConfigurationMARSeason season1 = seasons.get(0);
        assertEquals(new LocalDate(2013, 1, 10), season1.getStartDate());
        assertEquals(new LocalDate(2013, 1, 14), season1.getEndDate());
        assertEquals(BigDecimal.ZERO, season1.getSundayMAR());

        GroupPricingConfigurationMARSeason season2 = seasons.get(1);
        assertEquals(new LocalDate(2013, 1, 15), season2.getStartDate());
        assertEquals(new LocalDate(2013, 1, 20), season2.getEndDate());
        assertEquals(BigDecimal.TEN, season2.getSundayMAR());

        GroupPricingConfigurationMARSeason editedSeason2 = firstSeason.clone();
        editedSeason2.setStartDate(new LocalDate(2013, 1, 17));
        editedSeason2.setEndDate(new LocalDate(2013, 1, 20));
        editedSeason2.setSundayMAR(BigDecimal.TEN);

        service.applySplit(seasons, editedSeason2, new LocalDate(2013, 1, 15));
        assertEquals(3, seasons.size());
    }

    @Test
    public void applySplitWithSingleSeason() {
        List<GroupPricingConfigurationMARSeason> seasons = new ArrayList<GroupPricingConfigurationMARSeason>();

        service.applySplit(seasons, new GroupPricingConfigurationMARSeason(), new LocalDate(2013, 1, 1));
        assertEquals(1, seasons.size());
    }

    @Test
    public void applySplitWithTwoNonConnectedSeasons() {
        List<GroupPricingConfigurationMARSeason> seasons = new ArrayList<GroupPricingConfigurationMARSeason>();

        GroupPricingConfigurationMARSeason firstSeason = new GroupPricingConfigurationMARSeason();
        firstSeason.setStartDate(new LocalDate(2013, 1, 1));
        firstSeason.setEndDate(new LocalDate(2013, 1, 31));
        seasons.add(firstSeason);

        GroupPricingConfigurationMARSeason changedSeason = new GroupPricingConfigurationMARSeason();
        changedSeason.setStartDate(new LocalDate(2013, 2, 1));
        changedSeason.setEndDate(new LocalDate(2013, 2, 28));

        service.applySplit(seasons, changedSeason, new LocalDate(2013, 1, 1));
        assertEquals(2, seasons.size());

        GroupPricingConfigurationMARSeason season1 = seasons.get(0);
        assertEquals(new LocalDate(2013, 1, 1), season1.getStartDate());
        assertEquals(new LocalDate(2013, 1, 31), season1.getEndDate());

        GroupPricingConfigurationMARSeason season2 = seasons.get(1);
        assertEquals(new LocalDate(2013, 2, 1), season2.getStartDate());
        assertEquals(new LocalDate(2013, 2, 28), season2.getEndDate());
    }

    @Test
    public void willSplitOccur_withTwoNonConnectedSeasons_shouldNotSplit() {
        List<GroupPricingConfigurationMARSeason> seasons = new ArrayList<GroupPricingConfigurationMARSeason>();

        GroupPricingConfigurationMARSeason firstSeason = new GroupPricingConfigurationMARSeason();
        firstSeason.setStartDate(new LocalDate(2013, 1, 1));
        firstSeason.setEndDate(new LocalDate(2013, 1, 31));
        seasons.add(firstSeason);

        GroupPricingConfigurationMARSeason changedSeason = new GroupPricingConfigurationMARSeason();
        changedSeason.setStartDate(new LocalDate(2013, 2, 1));
        changedSeason.setEndDate(new LocalDate(2013, 2, 28));

        assertFalse(service.willSplitOccur(seasons, changedSeason), "No split");
    }

    @Test
    public void applySplitWithTwoCompletelyOverlappedSeasonsSameDates() {
        List<GroupPricingConfigurationMARSeason> seasons = new ArrayList<GroupPricingConfigurationMARSeason>();

        GroupPricingConfigurationMARSeason firstSeason = new GroupPricingConfigurationMARSeason();
        firstSeason.setStartDate(new LocalDate(2013, 1, 1));
        firstSeason.setEndDate(new LocalDate(2013, 1, 31));
        seasons.add(firstSeason);

        GroupPricingConfigurationMARSeason changedSeason = new GroupPricingConfigurationMARSeason();
        changedSeason.setStartDate(new LocalDate(2013, 1, 1));
        changedSeason.setEndDate(new LocalDate(2013, 2, 28));
        changedSeason.setSundayMAR(BigDecimal.ZERO);

        service.applySplit(seasons, changedSeason, new LocalDate(2013, 1, 1));
        assertEquals(1, seasons.size());

        GroupPricingConfigurationMARSeason season1 = seasons.get(0);
        assertEquals(new LocalDate(2013, 1, 1), season1.getStartDate());
        assertEquals(new LocalDate(2013, 2, 28), season1.getEndDate());

        // Verify that the season has the values of the changed season
        assertEquals(BigDecimal.ZERO, season1.getSundayMAR());
    }

    @Test
    public void applySplitWithTwoCompletelyOverlappedSeasons() {
        List<GroupPricingConfigurationMARSeason> seasons = new ArrayList<GroupPricingConfigurationMARSeason>();

        GroupPricingConfigurationMARSeason firstSeason = new GroupPricingConfigurationMARSeason();
        firstSeason.setStartDate(new LocalDate(2013, 1, 1));
        firstSeason.setEndDate(new LocalDate(2013, 1, 31));
        seasons.add(firstSeason);

        GroupPricingConfigurationMARSeason changedSeason = new GroupPricingConfigurationMARSeason();
        changedSeason.setStartDate(new LocalDate(2013, 1, 1));
        changedSeason.setEndDate(new LocalDate(2013, 1, 31));
        changedSeason.setSundayMAR(BigDecimal.ZERO);

        service.applySplit(seasons, changedSeason, new LocalDate(2013, 1, 1));
        assertEquals(1, seasons.size());

        GroupPricingConfigurationMARSeason season1 = seasons.get(0);
        assertEquals(new LocalDate(2013, 1, 1), season1.getStartDate());
        assertEquals(new LocalDate(2013, 1, 31), season1.getEndDate());

        // Verify that the season has the values of the changed season
        assertEquals(BigDecimal.ZERO, season1.getSundayMAR());
    }

    @Test
    public void applySplitAtEndOfSeasonWithDifferentEndDates() {
        List<GroupPricingConfigurationMARSeason> seasons = new ArrayList<GroupPricingConfigurationMARSeason>();

        GroupPricingConfigurationMARSeason firstSeason = new GroupPricingConfigurationMARSeason();
        firstSeason.setStartDate(new LocalDate(2013, 1, 1));
        firstSeason.setEndDate(new LocalDate(2013, 1, 31));
        firstSeason.setSundayMAR(BigDecimal.ZERO);
        seasons.add(firstSeason);

        GroupPricingConfigurationMARSeason changedSeason = new GroupPricingConfigurationMARSeason();
        changedSeason.setStartDate(new LocalDate(2013, 1, 15));
        changedSeason.setEndDate(new LocalDate(2013, 2, 1));
        changedSeason.setSundayMAR(BigDecimal.TEN);

        service.applySplit(seasons, changedSeason, new LocalDate(2013, 1, 1));
        assertEquals(2, seasons.size());

        // Verify that the season has the values of the changed season
        GroupPricingConfigurationMARSeason season1 = seasons.get(0);
        assertEquals(new LocalDate(2013, 1, 1), season1.getStartDate());
        assertEquals(new LocalDate(2013, 1, 14), season1.getEndDate());
        assertEquals(BigDecimal.ZERO, season1.getSundayMAR());

        GroupPricingConfigurationMARSeason season2 = seasons.get(1);
        assertEquals(new LocalDate(2013, 1, 15), season2.getStartDate());
        assertEquals(new LocalDate(2013, 2, 1), season2.getEndDate());
        assertEquals(BigDecimal.TEN, season2.getSundayMAR());
    }


    @Test
    public void applySplitAtBeginningOfSeasonWithDifferentEndDatesInFuture() {
        List<GroupPricingConfigurationMARSeason> seasons = new ArrayList<GroupPricingConfigurationMARSeason>();

        GroupPricingConfigurationMARSeason firstSeason = new GroupPricingConfigurationMARSeason();
        firstSeason.setStartDate(new LocalDate(2013, 1, 10));
        firstSeason.setEndDate(new LocalDate(2013, 1, 31));
        firstSeason.setSundayMAR(BigDecimal.ZERO);
        seasons.add(firstSeason);

        GroupPricingConfigurationMARSeason changedSeason = new GroupPricingConfigurationMARSeason();
        changedSeason.setStartDate(new LocalDate(2013, 1, 1));
        changedSeason.setEndDate(new LocalDate(2013, 1, 15));
        changedSeason.setSundayMAR(BigDecimal.TEN);

        service.applySplit(seasons, changedSeason, new LocalDate(2013, 1, 1));
        assertEquals(2, seasons.size());

        // Verify that the season has the values of the changed season
        GroupPricingConfigurationMARSeason season1 = seasons.get(0);
        assertEquals(new LocalDate(2013, 1, 1), season1.getStartDate());
        assertEquals(new LocalDate(2013, 1, 15), season1.getEndDate());
        assertEquals(BigDecimal.TEN, season1.getSundayMAR());

        GroupPricingConfigurationMARSeason season2 = seasons.get(1);
        assertEquals(new LocalDate(2013, 1, 16), season2.getStartDate());
        assertEquals(new LocalDate(2013, 1, 31), season2.getEndDate());
        assertEquals(BigDecimal.ZERO, season2.getSundayMAR());
    }

    @Test
    public void applySplitAtBeginningOfSeasonWithDifferentEndDatesInFuture_changedSeasonIsInList() {
        List<GroupPricingConfigurationMARSeason> seasons = new ArrayList<GroupPricingConfigurationMARSeason>();

        GroupPricingConfigurationMARSeason firstSeason = new GroupPricingConfigurationMARSeason();
        firstSeason.setStartDate(new LocalDate(2013, 1, 10));
        firstSeason.setEndDate(new LocalDate(2013, 1, 31));
        firstSeason.setSundayMAR(BigDecimal.ZERO);
        seasons.add(firstSeason);

        GroupPricingConfigurationMARSeason changedSeason = new GroupPricingConfigurationMARSeason();
        changedSeason.setStartDate(new LocalDate(2013, 1, 1));
        changedSeason.setEndDate(new LocalDate(2013, 1, 15));
        changedSeason.setSundayMAR(BigDecimal.TEN);
        seasons.add(changedSeason);

        service.applySplit(seasons, changedSeason, new LocalDate(2013, 1, 1));
        assertEquals(2, seasons.size());

        // Verify that the season has the values of the changed season
        GroupPricingConfigurationMARSeason season1 = seasons.get(0);
        assertEquals(new LocalDate(2013, 1, 1), season1.getStartDate());
        assertEquals(new LocalDate(2013, 1, 15), season1.getEndDate());
        assertEquals(BigDecimal.TEN, season1.getSundayMAR());

        GroupPricingConfigurationMARSeason season2 = seasons.get(1);
        assertEquals(new LocalDate(2013, 1, 16), season2.getStartDate());
        assertEquals(new LocalDate(2013, 1, 31), season2.getEndDate());
        assertEquals(BigDecimal.ZERO, season2.getSundayMAR());
    }

    @Test
    public void willSplitOccur_atBeginningOfSeasonWithDifferentEndDatesInFuture() {
        List<GroupPricingConfigurationMARSeason> seasons = new ArrayList<GroupPricingConfigurationMARSeason>();

        GroupPricingConfigurationMARSeason firstSeason = new GroupPricingConfigurationMARSeason();
        firstSeason.setStartDate(new LocalDate(2013, 1, 10));
        firstSeason.setEndDate(new LocalDate(2013, 1, 31));
        firstSeason.setSundayMAR(BigDecimal.ZERO);
        seasons.add(firstSeason);

        GroupPricingConfigurationMARSeason changedSeason = new GroupPricingConfigurationMARSeason();
        changedSeason.setStartDate(new LocalDate(2013, 1, 1));
        changedSeason.setEndDate(new LocalDate(2013, 1, 15));
        changedSeason.setSundayMAR(BigDecimal.TEN);

        assertTrue(service.willSplitOccur(seasons, changedSeason), "Will split");
    }

    @Test
    public void willSplitOccur_atBeginningOfSeasonWithDifferentEndDatesInFuture_changedSeasonIsInList() {
        List<GroupPricingConfigurationMARSeason> seasons = new ArrayList<GroupPricingConfigurationMARSeason>();

        GroupPricingConfigurationMARSeason firstSeason = new GroupPricingConfigurationMARSeason();
        firstSeason.setStartDate(new LocalDate(2013, 1, 10));
        firstSeason.setEndDate(new LocalDate(2013, 1, 31));
        firstSeason.setSundayMAR(BigDecimal.ZERO);
        seasons.add(firstSeason);

        GroupPricingConfigurationMARSeason changedSeason = new GroupPricingConfigurationMARSeason();
        changedSeason.setStartDate(new LocalDate(2013, 1, 1));
        changedSeason.setEndDate(new LocalDate(2013, 1, 15));
        changedSeason.setSundayMAR(BigDecimal.TEN);
        seasons.add(changedSeason);

        assertTrue(service.willSplitOccur(seasons, changedSeason), "Will split");
    }

    @Test
    public void applySplitAtEndOfSeasonWithSameEndDates() {
        List<GroupPricingConfigurationMARSeason> seasons = new ArrayList<GroupPricingConfigurationMARSeason>();

        GroupPricingConfigurationMARSeason firstSeason = new GroupPricingConfigurationMARSeason();
        firstSeason.setStartDate(new LocalDate(2013, 1, 1));
        firstSeason.setEndDate(new LocalDate(2013, 1, 31));
        firstSeason.setSundayMAR(BigDecimal.ZERO);
        seasons.add(firstSeason);

        GroupPricingConfigurationMARSeason changedSeason = new GroupPricingConfigurationMARSeason();
        changedSeason.setStartDate(new LocalDate(2013, 1, 15));
        changedSeason.setEndDate(new LocalDate(2013, 1, 31));
        changedSeason.setSundayMAR(BigDecimal.TEN);

        service.applySplit(seasons, changedSeason, new LocalDate(2013, 1, 1));
        assertEquals(2, seasons.size());

        // Verify that the season has the values of the changed season
        GroupPricingConfigurationMARSeason season1 = seasons.get(0);
        assertEquals(new LocalDate(2013, 1, 1), season1.getStartDate());
        assertEquals(new LocalDate(2013, 1, 14), season1.getEndDate());
        assertEquals(BigDecimal.ZERO, season1.getSundayMAR());

        GroupPricingConfigurationMARSeason season2 = seasons.get(1);
        assertEquals(new LocalDate(2013, 1, 15), season2.getStartDate());
        assertEquals(new LocalDate(2013, 1, 31), season2.getEndDate());
        assertEquals(BigDecimal.TEN, season2.getSundayMAR());
    }

    @Test
    public void applySplitWithTwoOverlappedSeasonsThatResultsInThreeSeasons() {
        List<GroupPricingConfigurationMARSeason> seasons = new ArrayList<GroupPricingConfigurationMARSeason>();

        GroupPricingConfigurationMARSeason firstSeason = new GroupPricingConfigurationMARSeason();
        firstSeason.setStartDate(new LocalDate(2013, 1, 1));
        firstSeason.setEndDate(new LocalDate(2013, 1, 31));
        firstSeason.setSundayMAR(BigDecimal.ZERO);
        seasons.add(firstSeason);

        GroupPricingConfigurationMARSeason changedSeason = new GroupPricingConfigurationMARSeason();
        changedSeason.setStartDate(new LocalDate(2013, 1, 10));
        changedSeason.setEndDate(new LocalDate(2013, 1, 20));
        changedSeason.setSundayMAR(BigDecimal.TEN);

        service.applySplit(seasons, changedSeason, new LocalDate(2013, 1, 1));
        assertEquals(3, seasons.size());

        GroupPricingConfigurationMARSeason season1 = seasons.get(0);
        assertEquals(new LocalDate(2013, 1, 1), season1.getStartDate());
        assertEquals(new LocalDate(2013, 1, 9), season1.getEndDate());
        assertEquals(BigDecimal.ZERO, season1.getSundayMAR());

        GroupPricingConfigurationMARSeason season2 = seasons.get(1);
        assertEquals(new LocalDate(2013, 1, 10), season2.getStartDate());
        assertEquals(new LocalDate(2013, 1, 20), season2.getEndDate());
        assertEquals(BigDecimal.TEN, season2.getSundayMAR());

        GroupPricingConfigurationMARSeason season3 = seasons.get(2);
        assertEquals(new LocalDate(2013, 1, 21), season3.getStartDate());
        assertEquals(new LocalDate(2013, 1, 31), season3.getEndDate());
        assertEquals(BigDecimal.ZERO, season3.getSundayMAR());
    }

    @Test
    public void applySplitWithSameObject() {
        List<GroupPricingConfigurationMARSeason> seasons = new ArrayList<GroupPricingConfigurationMARSeason>();

        GroupPricingConfigurationMARSeason firstSeason = new GroupPricingConfigurationMARSeason();
        firstSeason.setId(1);
        firstSeason.setStartDate(new LocalDate(2013, 1, 10));
        firstSeason.setEndDate(new LocalDate(2013, 1, 20));
        firstSeason.setSundayMAR(BigDecimal.ZERO);
        seasons.add(firstSeason);

        service.applySplit(seasons, firstSeason, new LocalDate(2012, 12, 31));
        assertEquals(1, seasons.size());

        GroupPricingConfigurationMARSeason season1 = seasons.get(0);
        assertEquals(Integer.valueOf(1), season1.getId());
        assertEquals(new LocalDate(2013, 1, 10), season1.getStartDate());
        assertEquals(new LocalDate(2013, 1, 20), season1.getEndDate());
        assertEquals(BigDecimal.ZERO, season1.getSundayMAR());
    }

    @Test
    public void applySplitWithTwoOverlappedSeasonsWithSameStartDate() {
        List<GroupPricingConfigurationMARSeason> seasons = new ArrayList<GroupPricingConfigurationMARSeason>();

        GroupPricingConfigurationMARSeason firstSeason = new GroupPricingConfigurationMARSeason();
        firstSeason.setStartDate(new LocalDate(2013, 10, 1));
        firstSeason.setEndDate(new LocalDate(2013, 10, 30));
        firstSeason.setSundayMAR(BigDecimal.ZERO);
        seasons.add(firstSeason);

        GroupPricingConfigurationMARSeason changedSeason = new GroupPricingConfigurationMARSeason();
        changedSeason.setStartDate(new LocalDate(2013, 10, 1));
        changedSeason.setEndDate(new LocalDate(2013, 10, 10));

        service.applySplit(seasons, changedSeason, new LocalDate(2013, 1, 1));
        assertEquals(2, seasons.size());

        GroupPricingConfigurationMARSeason season1 = seasons.get(0);
        assertEquals(new LocalDate(2013, 10, 1), season1.getStartDate());
        assertEquals(new LocalDate(2013, 10, 10), season1.getEndDate());
        assertNull(season1.getSundayMAR());

        GroupPricingConfigurationMARSeason season2 = seasons.get(1);
        assertEquals(new LocalDate(2013, 10, 11), season2.getStartDate());
        assertEquals(new LocalDate(2013, 10, 30), season2.getEndDate());
        assertEquals(BigDecimal.ZERO, season2.getSundayMAR());
    }

    @Test
    public void applySplitWithTwoOverlappedSeasonsWithEndDateSameAsStartDate() {
        List<GroupPricingConfigurationMARSeason> seasons = new ArrayList<GroupPricingConfigurationMARSeason>();

        GroupPricingConfigurationMARSeason firstSeason = new GroupPricingConfigurationMARSeason();
        firstSeason.setStartDate(new LocalDate(2013, 10, 1));
        firstSeason.setEndDate(new LocalDate(2013, 10, 30));
        firstSeason.setSundayMAR(BigDecimal.ZERO);
        seasons.add(firstSeason);

        GroupPricingConfigurationMARSeason changedSeason = new GroupPricingConfigurationMARSeason();
        changedSeason.setStartDate(new LocalDate(2013, 10, 30));
        changedSeason.setEndDate(new LocalDate(2013, 11, 10));

        service.applySplit(seasons, changedSeason, new LocalDate(2013, 1, 1));
        assertEquals(2, seasons.size());

        GroupPricingConfigurationMARSeason season1 = seasons.get(0);
        assertEquals(new LocalDate(2013, 10, 1), season1.getStartDate());
        assertEquals(new LocalDate(2013, 10, 29), season1.getEndDate());
        assertEquals(BigDecimal.ZERO, season1.getSundayMAR());

        GroupPricingConfigurationMARSeason season2 = seasons.get(1);
        assertEquals(new LocalDate(2013, 10, 30), season2.getStartDate());
        assertEquals(new LocalDate(2013, 11, 10), season2.getEndDate());
        assertNull(season2.getSundayMAR());
    }

    @Test
    public void applySplitWithTwoOverlappedSeasonsExistingStartDateSameAsNewEndDate() {
        List<GroupPricingConfigurationMARSeason> seasons = new ArrayList<GroupPricingConfigurationMARSeason>();

        GroupPricingConfigurationMARSeason firstSeason = new GroupPricingConfigurationMARSeason();
        firstSeason.setStartDate(new LocalDate(2013, 9, 13));
        firstSeason.setEndDate(new LocalDate(2013, 10, 30));
        firstSeason.setSundayMAR(BigDecimal.ZERO);
        seasons.add(firstSeason);

        GroupPricingConfigurationMARSeason changedSeason = new GroupPricingConfigurationMARSeason();
        changedSeason.setStartDate(new LocalDate(2013, 8, 13));
        changedSeason.setEndDate(new LocalDate(2013, 9, 13));

        service.applySplit(seasons, changedSeason, new LocalDate(2012, 1, 1));
        assertEquals(2, seasons.size());

        GroupPricingConfigurationMARSeason season1 = seasons.get(0);
        assertEquals(new LocalDate(2013, 8, 13), season1.getStartDate());
        assertEquals(new LocalDate(2013, 9, 13), season1.getEndDate());
        assertNull(season1.getSundayMAR());

        GroupPricingConfigurationMARSeason season2 = seasons.get(1);
        assertEquals(new LocalDate(2013, 9, 14), season2.getStartDate());
        assertEquals(new LocalDate(2013, 10, 30), season2.getEndDate());
        assertEquals(BigDecimal.ZERO, season2.getSundayMAR());
    }

    @Test
    public void willSplitOccur_nonOverlappingDates_doesNotSplit() {
        List<Season> seasons = new ArrayList<Season>();

        GroupPricingConfigurationAncillaryAssignmentSeason firstSeason = new GroupPricingConfigurationAncillaryAssignmentSeason();
        firstSeason.setStartDate(new LocalDate(2013, 10, 1));
        firstSeason.setEndDate(new LocalDate(2013, 10, 30));
        seasons.add(firstSeason);

        GroupPricingConfigurationAncillaryAssignmentSeason changedSeason = new GroupPricingConfigurationAncillaryAssignmentSeason();
        changedSeason.setStartDate(new LocalDate(2013, 11, 1));
        changedSeason.setEndDate(new LocalDate(2013, 11, 30));

        assertFalse(service.willSplitOccur(seasons, changedSeason), "Will not split");
    }

    @Test
    public void willSplitOccur_overlappingDates_doesSplit() {
        List<Season> seasons = new ArrayList<Season>();

        GroupPricingConfigurationAncillaryAssignmentSeason firstSeason = new GroupPricingConfigurationAncillaryAssignmentSeason();
        firstSeason.setStartDate(new LocalDate(2013, 10, 1));
        firstSeason.setEndDate(new LocalDate(2013, 10, 30));
        seasons.add(firstSeason);

        //no overlap
        GroupPricingConfigurationAncillaryAssignmentSeason secondSeason = new GroupPricingConfigurationAncillaryAssignmentSeason();
        secondSeason.setStartDate(new LocalDate(2013, 11, 1));
        secondSeason.setEndDate(new LocalDate(2013, 11, 30));

        // Set overlap
        GroupPricingConfigurationAncillaryAssignmentSeason changedSeason = secondSeason.clone();
        changedSeason.setStartDate(new LocalDate(2013, 10, 15));

        assertTrue(service.willSplitOccur(seasons, changedSeason), "Will split");
    }

    @Test
    public void willSplitOccur_hasDefaultSeasonAndNonOverlappingDates_doesNotSplit() {
        List<Season> seasons = new ArrayList<Season>();

        GroupPricingConfigurationAncillaryAssignmentSeason defaultSeason = new GroupPricingConfigurationAncillaryAssignmentSeason();
        seasons.add(defaultSeason);

        GroupPricingConfigurationAncillaryAssignmentSeason firstSeason = new GroupPricingConfigurationAncillaryAssignmentSeason();
        firstSeason.setStartDate(new LocalDate(2013, 10, 1));
        firstSeason.setEndDate(new LocalDate(2013, 10, 30));
        seasons.add(firstSeason);

        GroupPricingConfigurationAncillaryAssignmentSeason changedSeason = new GroupPricingConfigurationAncillaryAssignmentSeason();
        changedSeason.setStartDate(new LocalDate(2013, 11, 1));
        changedSeason.setEndDate(new LocalDate(2013, 11, 30));

        assertFalse(service.willSplitOccur(seasons, changedSeason), "Will not split");
    }

    @Test
    public void willSplitOccur_hasDefaultSeasonAndOverlappingDates_doesSplit() {
        List<Season> seasons = new ArrayList<Season>();

        GroupPricingConfigurationAncillaryAssignmentSeason defaultSeason = new GroupPricingConfigurationAncillaryAssignmentSeason();
        seasons.add(defaultSeason);

        GroupPricingConfigurationAncillaryAssignmentSeason firstSeason = new GroupPricingConfigurationAncillaryAssignmentSeason();
        firstSeason.setStartDate(new LocalDate(2013, 10, 1));
        firstSeason.setEndDate(new LocalDate(2013, 10, 30));
        seasons.add(firstSeason);

        //no overlap
        GroupPricingConfigurationAncillaryAssignmentSeason secondSeason = new GroupPricingConfigurationAncillaryAssignmentSeason();
        secondSeason.setStartDate(new LocalDate(2013, 11, 1));
        secondSeason.setEndDate(new LocalDate(2013, 11, 30));
        seasons.add(secondSeason);

        // Set overlap
        GroupPricingConfigurationAncillaryAssignmentSeason changedSeason = secondSeason.clone();
        changedSeason.setStartDate(new LocalDate(2013, 10, 15));

        assertTrue(service.willSplitOccur(seasons, changedSeason), "Will split");
    }

    @Test
    public void willSplitOccur_startDateBeforeExistingAndEndDateBetweenExisting_willSplit() {

        List<GroupPricingConfigurationAncillaryAssignmentSeason> seasons = new ArrayList<GroupPricingConfigurationAncillaryAssignmentSeason>();

        GroupPricingConfigurationAncillaryAssignmentSeason firstSeason = new GroupPricingConfigurationAncillaryAssignmentSeason();
        firstSeason.setStartDate(new LocalDate(2013, 10, 15));
        firstSeason.setEndDate(new LocalDate(2013, 10, 30));
        seasons.add(firstSeason);

        // Set overlap
        GroupPricingConfigurationAncillaryAssignmentSeason changedSeason = new GroupPricingConfigurationAncillaryAssignmentSeason();
        changedSeason.setStartDate(new LocalDate(2013, 10, 12)); //start before
        changedSeason.setEndDate(new LocalDate(2013, 10, 25)); //end in middle

        assertTrue(service.willSplitOccur(seasons, changedSeason), "Will split");
    }

    @Test
    public void getOverlappingSeasons_withTwoNonConnectedSeasons_shouldNotSplit() {
        List<AgileRatesSeason> seasons = new ArrayList<>();

        AgileRatesSeason firstSeason = new AgileRatesSeason();
        firstSeason.setStartDate(new LocalDate(2013, 1, 1));
        firstSeason.setEndDate(new LocalDate(2013, 1, 31));
        seasons.add(firstSeason);

        AgileRatesSeason changedSeason = new AgileRatesSeason();
        changedSeason.setStartDate(new LocalDate(2013, 2, 1));
        changedSeason.setEndDate(new LocalDate(2013, 2, 28));

        List<AgileRatesSeason> overlappingSeasons = service.getOverlappingSeasons(seasons, changedSeason);
        assertTrue(overlappingSeasons.isEmpty());
    }


    @Test
    public void getOverlappingSeasons_atBeginningOfSeasonWithDifferentEndDatesInFuture() {
        List<AgileRatesSeason> seasons = new ArrayList<>();

        AgileRatesSeason firstSeason = new AgileRatesSeason();
        firstSeason.setStartDate(new LocalDate(2013, 1, 10));
        firstSeason.setEndDate(new LocalDate(2013, 1, 31));
        seasons.add(firstSeason);

        AgileRatesSeason changedSeason = new AgileRatesSeason();
        changedSeason.setStartDate(new LocalDate(2013, 1, 1));
        changedSeason.setEndDate(new LocalDate(2013, 1, 15));

        List<AgileRatesSeason> overlappingSeasons = service.getOverlappingSeasons(seasons, changedSeason);
        assertEquals(1, overlappingSeasons.size());
        assertEquals(overlappingSeasons.get(0), firstSeason);
    }

    @Test
    public void getOverlappingSeasons_atBeginningOfSeasonWithDifferentEndDatesInFuture_changedSeasonIsInList() {
        List<AgileRatesSeason> seasons = new ArrayList<>();

        AgileRatesSeason firstSeason = new AgileRatesSeason();
        firstSeason.setStartDate(new LocalDate(2013, 1, 10));
        firstSeason.setEndDate(new LocalDate(2013, 1, 31));
        seasons.add(firstSeason);

        AgileRatesSeason changedSeason = new AgileRatesSeason();
        changedSeason.setStartDate(new LocalDate(2013, 1, 1));
        changedSeason.setEndDate(new LocalDate(2013, 1, 15));
        seasons.add(changedSeason);

        List<AgileRatesSeason> overlappingSeasons = service.getOverlappingSeasons(seasons, changedSeason);
        assertEquals(1, overlappingSeasons.size());
        assertEquals(overlappingSeasons.get(0), firstSeason);
    }

    @Test
    public void getOverlappingSeasons_nonOverlappingDates_doesNotSplit() {
        List<AgileRatesSeason> seasons = new ArrayList<>();

        AgileRatesSeason firstSeason = new AgileRatesSeason();
        firstSeason.setStartDate(new LocalDate(2013, 10, 1));
        firstSeason.setEndDate(new LocalDate(2013, 10, 30));
        seasons.add(firstSeason);

        AgileRatesSeason changedSeason = new AgileRatesSeason();
        changedSeason.setStartDate(new LocalDate(2013, 11, 1));
        changedSeason.setEndDate(new LocalDate(2013, 11, 30));

        List<AgileRatesSeason> overlappingSeasons = service.getOverlappingSeasons(seasons, changedSeason);
        assertTrue(overlappingSeasons.isEmpty());
    }

    @Test
    public void getOverlappingSeasons_overlappingDates_doesSplit() {
        List<AgileRatesSeason> seasons = new ArrayList<>();

        AgileRatesSeason firstSeason = new AgileRatesSeason();
        firstSeason.setStartDate(new LocalDate(2013, 10, 1));
        firstSeason.setEndDate(new LocalDate(2013, 10, 30));
        seasons.add(firstSeason);

        //no overlap
        AgileRatesSeason secondSeason = new AgileRatesSeason();
        secondSeason.setStartDate(new LocalDate(2013, 11, 1));
        secondSeason.setEndDate(new LocalDate(2013, 11, 30));
        secondSeason.setSeasonRateOffsets(Arrays.asList(new ProductRateOffset()));

        // Set overlap
        AgileRatesSeason changedSeason = secondSeason.clone();
        changedSeason.setStartDate(new LocalDate(2013, 10, 15));

        List<AgileRatesSeason> overlappingSeasons = service.getOverlappingSeasons(seasons, changedSeason);
        assertEquals(1, overlappingSeasons.size());
        assertEquals(overlappingSeasons.get(0), firstSeason);
    }

    @Test
    public void getOverlappingSeasons_hasDefaultSeasonAndNonOverlappingDates_doesNotSplit() {
        List<AgileRatesSeason> seasons = new ArrayList<>();

        AgileRatesSeason firstSeason = new AgileRatesSeason();
        firstSeason.setStartDate(new LocalDate(2013, 10, 1));
        firstSeason.setEndDate(new LocalDate(2013, 10, 30));
        seasons.add(firstSeason);

        AgileRatesSeason changedSeason = new AgileRatesSeason();
        changedSeason.setStartDate(new LocalDate(2013, 11, 1));
        changedSeason.setEndDate(new LocalDate(2013, 11, 30));

        List<AgileRatesSeason> overlappingSeasons = service.getOverlappingSeasons(seasons, changedSeason);
        assertTrue(overlappingSeasons.isEmpty());
    }

    @Test
    public void getOverlappingSeasons_hasDefaultSeasonAndOverlappingDates_doesSplit() {
        List<AgileRatesSeason> seasons = new ArrayList<>();

        AgileRatesSeason firstSeason = new AgileRatesSeason();
        firstSeason.setStartDate(new LocalDate(2013, 10, 1));
        firstSeason.setEndDate(new LocalDate(2013, 10, 30));
        seasons.add(firstSeason);

        //no overlap
        AgileRatesSeason secondSeason = new AgileRatesSeason();
        secondSeason.setStartDate(new LocalDate(2013, 11, 1));
        secondSeason.setEndDate(new LocalDate(2013, 11, 30));
        secondSeason.setSeasonRateOffsets(Arrays.asList(new ProductRateOffset()));
        seasons.add(secondSeason);

        // Set overlap
        AgileRatesSeason changedSeason = secondSeason.clone();
        changedSeason.setStartDate(new LocalDate(2013, 10, 15));

        List<AgileRatesSeason> overlappingSeasons = service.getOverlappingSeasons(seasons, changedSeason);
        assertEquals(2, overlappingSeasons.size());
        assertEquals(overlappingSeasons.get(0), firstSeason);
        assertEquals(overlappingSeasons.get(1), secondSeason);
    }

    @Test
    public void getOverlappingSeasons_startDateBeforeExistingAndEndDateBetweenExisting_willSplit() {

        List<AgileRatesSeason> seasons = new ArrayList<>();

        AgileRatesSeason firstSeason = new AgileRatesSeason();
        firstSeason.setStartDate(new LocalDate(2013, 10, 15));
        firstSeason.setEndDate(new LocalDate(2013, 10, 30));
        seasons.add(firstSeason);

        // Set overlap
        AgileRatesSeason changedSeason = new AgileRatesSeason();
        changedSeason.setStartDate(new LocalDate(2013, 10, 12)); //start before
        changedSeason.setEndDate(new LocalDate(2013, 10, 25)); //end in middle

        List<AgileRatesSeason> overlappingSeasons = service.getOverlappingSeasons(seasons, changedSeason);
        assertEquals(1, overlappingSeasons.size());
        assertEquals(overlappingSeasons.get(0), firstSeason);
    }
}
