package com.ideas.tetris.pacman.services.individualtransactions;


import com.google.common.collect.Lists;
import com.ideas.g3.test.AbstractG3JupiterTest;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomType;
import com.ideas.tetris.pacman.services.accommodation.service.AccommodationService;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.IndividualTransactions;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.ReservationNight;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.FileMetadata;
import com.ideas.tetris.pacman.services.filemetadata.FileMetadataService;
import com.ideas.tetris.pacman.services.individualtransactions.converter.IndividualTransactionsConverter;
import com.ideas.tetris.pacman.services.property.PropertyService;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.joda.time.LocalDate;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.math.BigDecimal;
import java.util.*;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.anyMapOf;
import static org.mockito.Mockito.eq;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoMoreInteractions;
import static org.mockito.Mockito.when;

@MockitoSettings(strictness = Strictness.LENIENT)
public class IndividualTransactionsServiceTest extends AbstractG3JupiterTest {

    public static final String CORRELATION_ID = "correlationId";
    private static final String RESERVATION_ID = "reservationId";
    private static final Integer FILE_METADATA_ID = 100;
    private int nextTransactionId = 1;

    @Mock
    private FileMetadataService fileMetadataService;
    @Mock
    private IndividualTransactionsConverter individualTransactionsConverter;
    @Mock
    private PacmanConfigParamsService configParamsService;
    @Mock
    private PropertyService propertyService;
    @InjectMocks
    private IndividualTransactionsService individualTransactionsService;


    @Test
    public void testDeleteSharers() {
        individualTransactionsService.tenantCrudService = tenantCrudService();
        ArrayList<Map<String, Object>> individualTransactionsMap = new ArrayList<>();
        FileMetadata fileMetadata = new FileMetadata();
        fileMetadata.setId(FILE_METADATA_ID);
        fileMetadata.setTenantPropertyId(5);
        fileMetadata.setSnapshotDt(new Date());
        fileMetadata.setPastWindowSize(45);
        IndividualTransactions shareTransaction = createShareTransaction(nextTransactionId++, false);
        List<IndividualTransactions> transactionsList = Lists.newArrayList(shareTransaction);
        when(individualTransactionsConverter.convert(individualTransactionsMap, FILE_METADATA_ID, new HashSet<>())).thenReturn(transactionsList);
        when(fileMetadataService.findByFileLocation(anyString())).thenReturn(fileMetadata);
        individualTransactionsService.saveFromMap(individualTransactionsMap, CORRELATION_ID);
        verify(individualTransactionsConverter).convert(individualTransactionsMap, FILE_METADATA_ID, new HashSet<>());
    }

    @Test
    public void testDeletePreviousSharers() {
        individualTransactionsService.tenantCrudService = tenantCrudService();
        ArrayList<Map<String, Object>> individualTransactionsMap = new ArrayList<>();
        FileMetadata fileMetadata = new FileMetadata();
        fileMetadata.setId(FILE_METADATA_ID);
        fileMetadata.setTenantPropertyId(5);
        fileMetadata.setSnapshotDt(new Date());
        fileMetadata.setPastWindowSize(45);
        IndividualTransactions existingTransaction = createShareTransaction(nextTransactionId++, true);
        List<IndividualTransactions> transactionsList = Lists.newArrayList(existingTransaction);
        when(individualTransactionsConverter.convert(individualTransactionsMap, FILE_METADATA_ID, new HashSet<>())).thenReturn(transactionsList);
        when(fileMetadataService.findByFileLocation(anyString())).thenReturn(fileMetadata);

        individualTransactionsService.saveFromMap(individualTransactionsMap, CORRELATION_ID);
        verify(individualTransactionsConverter).convert(individualTransactionsMap, FILE_METADATA_ID, new HashSet<>());
    }

    @Test
    public void testSaveInvBlockCodeAndMarketCode() {
        tenantCrudService().executeUpdateByNativeQuery("delete from Reservation_Night where Reservation_Identifier = '777' ");
        commitTransaction(tenantCrudService());
        individualTransactionsService.tenantCrudService = tenantCrudService();
        when(propertyService.getReservationDataVersion()).thenReturn(2);

        FileMetadata fileMetadata = new FileMetadata();
        fileMetadata.setId(3);
        fileMetadata.setTenantPropertyId(5);
        fileMetadata.setSnapshotDt(new Date());
        fileMetadata.setPastWindowSize(45);

        when(fileMetadataService.findByFileLocation(any())).thenReturn(fileMetadata);

        LocalDate d = new LocalDate();
        List<IndividualTransactions> list = new ArrayList<>();
        IndividualTransactions individualTransactions = new IndividualTransactions();
        individualTransactions.setArrivalDate(d.toDate());
        individualTransactions.setDepartureDate(d.plusDays(1).toDate());
        individualTransactions.setInvBlockCode("123");
        individualTransactions.setFileMetadataId(3);
        individualTransactions.setPropertyId(1);
        individualTransactions.setAccomTypeId(4);
        individualTransactions.setBookingDate(new Date());
        individualTransactions.setCreateDate(new Date());
        individualTransactions.setIndividualStatus("XX");
        individualTransactions.setMarketSegId(1);
        individualTransactions.setReservationIdentifier("777");
        individualTransactions.setRoomRevenue(BigDecimal.ONE);
        individualTransactions.setTotalRevenue(BigDecimal.TEN);
        individualTransactions.setMarketCode("marketCode1");
        list.add(individualTransactions);
        when(individualTransactionsConverter.convert(any(), any(), any())).thenReturn(list);

        List<Map<String, Object>> individualTransactionsMap = new ArrayList<>();
        when(individualTransactionsConverter.getReservationIdsForSplits(any())).thenReturn(new HashSet<>());

        inject(individualTransactionsService, "tenantCrudService", tenantCrudService());
        individualTransactionsService.saveFromMap(individualTransactionsMap, CORRELATION_ID);

        commitTransaction(tenantCrudService());
        List<Object[]> o = tenantCrudService().findByNativeQuery("select * from Reservation_Night where Reservation_Identifier = '777' ");
        assertEquals("123", o.get(0)[33]);
        assertEquals("marketCode1", o.get(0)[34]);
        tenantCrudService().executeUpdateByNativeQuery("delete from Reservation_Night where Reservation_Identifier = '777' ");
        commitTransaction(tenantCrudService());
    }

    @Test
    public void testSaveFromMap_cancel_version1() {
        individualTransactionsService.tenantCrudService = tenantCrudService();
        ArrayList<Map<String, Object>> individualTransactionsMap = new ArrayList<>();
        FileMetadata fileMetadata = new FileMetadata();
        fileMetadata.setId(FILE_METADATA_ID);

        fileMetadata.setTenantPropertyId(5);
        fileMetadata.setSnapshotDt(new Date());
        fileMetadata.setPastWindowSize(45);
        List<IndividualTransactions> transactions = createTransactions(nextTransactionId++, true);
        when(individualTransactionsConverter.convert(individualTransactionsMap, FILE_METADATA_ID, new HashSet<>())).thenReturn(transactions);
        when(fileMetadataService.findByFileLocation(anyString())).thenReturn(fileMetadata);
        individualTransactionsService.saveFromMap(individualTransactionsMap, CORRELATION_ID);
        verify(individualTransactionsConverter).convert(individualTransactionsMap, FILE_METADATA_ID, new HashSet<>());
    }

    @Test
    public void averageLOSAtHotelTest() {
        Date arrivalDate = DateUtil.getDate(10, 03, 2021);
        Date departureDate = DateUtil.getDate(14, 03, 2021);
        createReservationNight(arrivalDate, departureDate, "123456");
        createReservationNight(arrivalDate, departureDate, "1234567");
        individualTransactionsService.tenantCrudService = tenantCrudService();
        Double actual = individualTransactionsService.updateWeightedAverageLOS();
        assertEquals(4.0, actual);
    }

    private void createReservationNight(Date arrivalDate, Date departureDate, String reservationIdentifier) {
        ReservationNight reservationNight = new ReservationNight();
        reservationNight.setIndividualStatus("CO");
        reservationNight.setAccomTypeId(4);
        reservationNight.setReservationIdentifier(reservationIdentifier);
        reservationNight.setArrivalDate(arrivalDate);
        reservationNight.setDepartureDate(departureDate);
        reservationNight.setMarketSegId(1);
        reservationNight.setRoomRevenue(BigDecimal.ONE);
        reservationNight.setTotalRevenue(BigDecimal.TEN);
        reservationNight.setMarketCode("marketCode1");
        reservationNight.setFileMetadataId(1);
        reservationNight.setPropertyId(5);
        reservationNight.setBookingDate(arrivalDate);
        reservationNight.setCreateDate(new Date());

        tenantCrudService().save(reservationNight);

    }

    private List<IndividualTransactions> createTransactions(int id, boolean isCancel) {
        List<IndividualTransactions> transactions = new ArrayList<>();
        FileMetadata fileMetadata = createFileMetadata();
        if (isCancel) {
            transactions.add(createCancelledTransaction(id, fileMetadata));
            transactions.add(createCancelledTransaction2(id + 1000, fileMetadata));
        } else {
            transactions.add(createTransaction(id, fileMetadata));
            transactions.add(createCancelledTransaction(id + 1000, fileMetadata));
        }
        return transactions;
    }

    private FileMetadata createFileMetadata() {
        FileMetadata fileMetadata = new FileMetadata();
        fileMetadata.setTenantPropertyId(5);
        fileMetadata.setSnapshotDt(new Date());
        fileMetadata.setFileLocation("sjdsakjfd");
        fileMetadata.setPreparedDt(new Date(326955));
        fileMetadata.setPreparedTm(new Date(643));
        fileMetadata.setProcessStatusId(2);
        fileMetadata.setPastWindowSize(45);
        fileMetadata.setSnapshotDt(new Date(3463432));
        fileMetadata.setSnapshotTm(new Date(348));
        fileMetadata.setRecordTypeId(2);
        tenantCrudService().save(fileMetadata);
        return fileMetadata;
    }

    private IndividualTransactions createCancelledTransaction(int id, FileMetadata fileMetadata) {
        IndividualTransactions transactions = new IndividualTransactions();
        transactions.setPropertyId(5);
        transactions.setId(id);
        transactions.setBookingDate(new Date());
        transactions.setCancellationDate(new Date());
        transactions.setReservationIdentifier("201");
        transactions.setIndividualStatus("XX");
        transactions.setFileMetadataId(fileMetadata.getId());
        transactions.setAccomTypeId(4);
        transactions.setMarketSegId(3);
        transactions.setArrivalDate(new Date(23432848));
        transactions.setDepartureDate(new Date(63432848));
        transactions.setRoomRevenue(BigDecimal.valueOf(2349.00));
        transactions.setTotalRevenue(BigDecimal.valueOf(2349.00));
        return transactions;
    }

    private IndividualTransactions createCancelledTransaction2(int id, FileMetadata fileMetadata) {
        IndividualTransactions transactions = new IndividualTransactions();
        transactions.setPropertyId(5);
        transactions.setId(id);
        transactions.setBookingDate(new Date());
        transactions.setCancellationDate(new Date());
        transactions.setReservationIdentifier("101");
        transactions.setIndividualStatus("XX");
        transactions.setFileMetadataId(fileMetadata.getId());
        transactions.setAccomTypeId(4);
        transactions.setMarketSegId(3);
        transactions.setArrivalDate(new Date(23432848));
        transactions.setDepartureDate(new Date(63432848));
        transactions.setRoomRevenue(BigDecimal.valueOf(2349.00));
        transactions.setTotalRevenue(BigDecimal.valueOf(2349.00));
        return transactions;
    }

    private IndividualTransactions createTransaction(int id, FileMetadata fileMetadata) {
        IndividualTransactions transactions = new IndividualTransactions();
        transactions.setFileMetadataId(fileMetadata.getId());
        transactions.setPropertyId(12);
        transactions.setId(id);
        transactions.setBookingDate(new Date());
        transactions.setReservationIdentifier(RESERVATION_ID);
        transactions.setIndividualStatus("SS");
        return transactions;
    }

    private IndividualTransactions createShareTransaction(int id, boolean previousSharers) {
        return createShareTransaction(id, previousSharers, null);
    }

    private IndividualTransactions createShareTransaction(int id, boolean previousSharers, String invBlockCode) {
        FileMetadata fileMetadata = createFileMetadata();
        IndividualTransactions transaction = new IndividualTransactions();
        transaction.setPropertyId(5);
        transaction.setId(id);
        transaction.setFileMetadataId(fileMetadata.getId());
        transaction.setBookingDate(new Date());
        transaction.setReservationIdentifier(RESERVATION_ID);
        transaction.setIndividualStatus("SS");
        transaction.setSharers(Lists.newArrayList("a", "b"));
        transaction.setAccomTypeId(4);
        transaction.setMarketSegId(3);
        transaction.setArrivalDate(new Date(23432848));
        transaction.setDepartureDate(new Date(63432848));
        transaction.setRoomRevenue(BigDecimal.valueOf(2349.00));
        transaction.setTotalRevenue(BigDecimal.valueOf(2349.00));

        if (previousSharers) {
            transaction.setPreviousSharers(Lists.newArrayList("c"));
        }

        return transaction;
    }
}
