package com.ideas.g3.api.controller.remoteAgent;

import com.ideas.g3.integration.opera.agent.task.PropertyTaskType;
import com.ideas.tetris.pacman.services.opera.ValidateRedirectService;
import com.ideas.tetris.pacman.services.remoteAgent.RemoteAgentService;
import com.ideas.tetris.pacman.services.remoteAgent.TaskNotInProgressException;
import org.jboss.resteasy.plugins.providers.multipart.MultipartFormDataInput;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;

import static org.mockito.ArgumentMatchers.nullable;
import static org.mockito.Mockito.verify;


@ExtendWith(MockitoExtension.class)
public class RemoteAgentServiceControllerTest { 

    @Mock
    private RemoteAgentService service;

    @Mock
    private ValidateRedirectService validateRedirectService;

    @InjectMocks
    private RemoteAgentServiceController controller;

    @Test
    public void taskFailedtaskIdresultsrequest() throws TaskNotInProgressException { 
        controller.taskFailed("0", "results", (HttpServletRequest) null);
        verify(service).taskFailed(nullable(int.class), nullable(String.class), nullable(HttpServletRequest.class));
    }

    @Test
    public void getNextTaskagentIdmachineNameosVersionjreVersionagentPollIntervalagentPropertyPollIntervalagentPropertyThreadsrequest() { 
        controller.getNextTask("0", "(String) null", (String) null, (String) null, (String) null, (String) null, (String) null, (HttpServletRequest) null);
        verify(service).getNextTask(nullable(int.class), nullable(String.class), nullable(String.class), nullable(String.class), nullable(String.class), nullable(String.class), nullable(String.class), nullable(HttpServletRequest.class));
    }

    @Test
    public void taskCompletedtaskIdresultsrequest() throws TaskNotInProgressException { 
        controller.taskCompleted("0", "results", (HttpServletRequest) null);
        verify(service).taskCompleted(nullable(int.class), nullable(String.class), nullable(HttpServletRequest.class));
    }

    @Test
    public void downloadMonitormultipartFormDataInputcorrelationIdfeedTypepropertyIdrequest() throws IOException {
        controller.downloadMonitor((MultipartFile) null, "correlationId", "feedType", "1", (HttpServletRequest) null);
        verify(service).downloadMonitor(nullable(MultipartFile.class), nullable(String.class), nullable(String.class), nullable(String.class));
    }

    @Test
    public void auditDateagentIdtaskIdauditDateStrrequest() { 
        controller.auditDate("0", "0", (String) null, (HttpServletRequest) null);
        verify(service).auditDate(nullable(int.class), nullable(int.class), nullable(String.class), nullable(HttpServletRequest.class));
    }

    @Test
    public void downloadFeedmultipartFormDataInputcorrelationIdfeedTypetaskIdchunkIdlastChunkbusinessDaterequest() { 
        controller.downloadFeed((MultipartFile) null, "correlationId", "feedType", "0", "chunkId", false, "businessDate", (HttpServletRequest) null);
        verify(service).downloadFeed(nullable(MultipartFormDataInput.class), nullable(MultipartFile.class), nullable(String.class), nullable(String.class), nullable(Integer.class), nullable(String.class), nullable(boolean.class), nullable(String.class), nullable(HttpServletRequest.class));
    }

    @Test
    public void propertyTaskFailedtaskIdresultsrequest() throws TaskNotInProgressException { 
        controller.propertyTaskFailed("0", "results", (HttpServletRequest) null);
        verify(service).propertyTaskFailed(nullable(int.class), nullable(String.class), nullable(HttpServletRequest.class));
    }

    @Test
    public void getNextPropertyTaskagentIdpropertyIdpmsAvailableorsAvailablerequest() { 
        controller.getNextPropertyTask("0", "0", false, false, (HttpServletRequest) null);
        verify(service).getNextPropertyTask(nullable(int.class), nullable(int.class), nullable(boolean.class), nullable(boolean.class), nullable(HttpServletRequest.class));
    }

    @Test
    public void createPropertyRemoteTaskagentIdpropertyIdtaskType() { 
        controller.createPropertyRemoteTask("0", "0", "NOTHING_TO_DO", (HttpServletRequest) null);
        verify(service).createPropertyRemoteTask(nullable(int.class), nullable(int.class), nullable(PropertyTaskType.class));
    }

    @Test
    public void propertyTaskCompletedtaskIdresultsrequest() throws TaskNotInProgressException { 
        controller.propertyTaskCompleted("0", "results", (HttpServletRequest) null);
        verify(service).propertyTaskCompleted(nullable(int.class), nullable(String.class), nullable(HttpServletRequest.class));
    }

}
