<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns="http://www.opentravel.org/OTA/2003/05" targetNamespace="http://www.opentravel.org/OTA/2003/05" elementFormDefault="qualified" version="5.001" id="OTA2003A2011A">
	<xs:include schemaLocation="OTA_SimpleTypes.xsd"/>
	<xs:annotation>
		<xs:documentation xml:lang="en">All Schema files in the OpenTravel Alliance specification are made available according to the terms defined by the OpenTravel License Agreement at http://www.opentravel.org/Specifications/Default.aspx.</xs:documentation>
	</xs:annotation>
	<xs:attributeGroup name="AcceptablePaymentCardGroup">
		<xs:annotation>
			<xs:documentation xml:lang="en">This complex type defines the information needed to describe a type of payment card that is acceptable as a form of payment. A usage fee (amount or percentage) may also be stated for this particular card type.</xs:documentation>
		</xs:annotation>
		<xs:attribute name="CardType" type="OTA_CodeType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">A code used to identify this payment card. Refer to OpenTravel Code List Card Type (CDT).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="CardName" type="StringLength1to32" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The name used to describe this type of payment card, for example, American Express.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="UsagePercentage" type="Percentage" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">If applicable, defines the percentage of the total amount that is incurred as a usage fee.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="UsageAmount" type="Money" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">If applicable, defines the additonal amount that is incurred as a usage fee.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attributeGroup ref="CurrencyCodeGroup">
			<xs:annotation>
				<xs:documentation xml:lang="en">Identifies the currency of the amount.</xs:documentation>
			</xs:annotation>
		</xs:attributeGroup>
	</xs:attributeGroup>
	<xs:attributeGroup name="AirportLocationGroup">
		<xs:annotation>
			<xs:documentation xml:lang="en">Airport location includes 3 letter code, terminal and gate.</xs:documentation>
		</xs:annotation>
		<xs:attribute name="LocationCode" type="StringLength1to8" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Location code used to identify a specific airport.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="CodeContext" type="StringLength1to32" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Identifies the context of the identifying code, such as IATA, ARC, or internal code, etc.</xs:documentation>
				<xs:documentation xml:lang="en">
					<LegacyDefaultValue>IATA</LegacyDefaultValue>
				</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="Terminal" type="StringLength1to16" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Arrival or departure terminal (e.g., Concourse A)</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="Gate" type="StringLength1to16" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Arrival or departure gate (e.g., B12)</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:attributeGroup>
	<xs:attributeGroup name="AltLangID_Group">
		<xs:annotation>
			<xs:documentation xml:lang="en">Identifies the alternate language for a customer or message. The human language is identified by ISO 639 codes.</xs:documentation>
		</xs:annotation>
		<xs:attribute name="AltLangID" type="xs:language" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Identifies the alternate language for a customer or message. The human language is identified by ISO 639 codes.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:attributeGroup>
	<xs:attributeGroup name="AreaID_Group">
		<xs:annotation>
			<xs:documentation xml:lang="en">An identifier of a geographical area.</xs:documentation>
		</xs:annotation>
		<xs:attribute name="AreaID" type="NumericStringLength1to8" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">An identifier of an area as defined by a hotel reservation system.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:attributeGroup>
	<xs:attributeGroup name="BirthDateGroup">
		<xs:annotation>
			<xs:documentation xml:lang="en">Used to provide a date of birth.</xs:documentation>
		</xs:annotation>
		<xs:attribute name="BirthDate" type="xs:date" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Indicates the date of birth as indicated in the document, in ISO 8601 prescribed format.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:attributeGroup>
	<xs:attributeGroup name="BookingChannelGroup">
		<xs:annotation>
			<xs:documentation xml:lang="en">Specifies the booking channel types and whether it is the primary means of connectivity of the source.</xs:documentation>
		</xs:annotation>
		<xs:attribute name="Type" type="OTA_CodeType" use="required">
			<xs:annotation>
				<xs:documentation xml:lang="en">The type of booking channel (e.g. Global Distribution System (GDS), Alternative Distribution System (ADS), Sales and Catering System (SCS), Property Management System (PMS), Central Reservation System (CRS), Tour Operator System (TOS), Internet and ALL). Refer to OpenTravel Code List Booking Channel Type (BCT).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="Primary" type="xs:boolean" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Indicates whether the enumerated booking channel is the primary means of connectivity used by the source.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:attributeGroup>
	<xs:attributeGroup name="ChargeUnitGroup">
		<xs:annotation>
			<xs:documentation xml:lang="en">Specifies charge information by unit (e.g., room, person, item) and frequency (e.g., daily, weekly, stay).</xs:documentation>
		</xs:annotation>
		<xs:attribute name="ChargeUnit" type="OTA_CodeType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">This is the unit for which the charge applies (e.g. room, person, seat). Refer to OpenTravel Code List Charge Type (CHG).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="ChargeFrequency" type="OTA_CodeType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">This is the timeframe used to apply the charge during the course of the reservation (e.g. Daily, Weekly, Stay). Refer to OpenTravel Code List Charge Type (CHG).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="ChargeUnitExempt" type="xs:positiveInteger" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Number of units permitted before charges are applied (e.g., more than 4 persons).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="ChargeFrequencyExempt" type="xs:positiveInteger" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">ChargeFrequency exemptions before charges are applied (e.g. after 2 nights).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="MaxChargeUnitApplies" type="xs:positiveInteger" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Maximum number of Units for which the charge will be applied (e.g., waive charges above 10 rooms).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="MaxChargeFrequencyApplies" type="xs:positiveInteger" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Maximum number of times the charge will be applied (e.g. waive charges above 30 nights).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:attributeGroup>
	<xs:attributeGroup name="CitizenCountryNameGroup">
		<xs:annotation>
			<xs:documentation xml:lang="en">Name of the (self-professed) country that is claimed for citizenship.</xs:documentation>
		</xs:annotation>
		<xs:attributeGroup ref="DefaultIndGroup">
			<xs:annotation>
				<xs:documentation xml:lang="en">Indicates that the receiving system should assume the default value if the user specifies no overriding value or action.</xs:documentation>
			</xs:annotation>
		</xs:attributeGroup>
		<xs:attribute name="Code" type="ISO3166" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">A 2 character country code as defined in ISO3166.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:attributeGroup>
	<xs:attributeGroup name="CodeGroup">
		<xs:annotation>
			<xs:documentation xml:lang="en">Used to specify a code and the context of the code.</xs:documentation>
		</xs:annotation>
		<xs:attribute name="Code" type="StringLength1to8" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Any code used to specify an item, for example, type of traveler, service code, room amenity, etc.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="CodeContext" type="StringLength1to32" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Identifies the source authority for the code.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:attributeGroup>
	<xs:attributeGroup name="CodeInfoGroup">
		<xs:annotation>
			<xs:documentation xml:lang="en">This is intended to be used in conjunction with an attribute that uses an OpenTravel Code list. It is used  to provide additional information about the code being referenced.</xs:documentation>
			<xs:documentation xml:lang="en">May be used to give further detail on the code or to remove an obsolete item.</xs:documentation>
		</xs:annotation>
		<xs:attribute name="CodeDetail" type="StringLength1to128" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">May be used to give further detail on the code.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attributeGroup ref="RemovalGroup">
			<xs:annotation>
				<xs:documentation xml:lang="en">This is used to indicate that an item is obsolete.</xs:documentation>
			</xs:annotation>
		</xs:attributeGroup>
	</xs:attributeGroup>
	<xs:attributeGroup name="CodeListGroup">
		<xs:annotation>
			<xs:documentation xml:lang="en">Used to specify a code and its associated attributes; meaning is derived from actual use.</xs:documentation>
		</xs:annotation>
		<xs:attributeGroup ref="CodeGroup">
			<xs:annotation>
				<xs:documentation xml:lang="en">Used to specify a code and the context of the code.</xs:documentation>
			</xs:annotation>
		</xs:attributeGroup>
		<xs:attribute name="URI" type="xs:anyURI" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Identifies the location of the code table.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attributeGroup ref="QuantityGroup">
			<xs:annotation>
				<xs:documentation xml:lang="en">Specifies the number of items that are identified by the Code (e.g., 2 adults, 5 first class seats).</xs:documentation>
			</xs:annotation>
		</xs:attributeGroup>
	</xs:attributeGroup>
	<xs:attributeGroup name="CodePrefGroup">
		<xs:annotation>
			<xs:documentation xml:lang="en">Provides a code along with the preferred usage of this information.</xs:documentation>
		</xs:annotation>
		<xs:attribute name="Code" type="StringLength1to8" use="required">
			<xs:annotation>
				<xs:documentation xml:lang="en">A code for which a preference may be specified.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attributeGroup ref="PreferLevelGroup">
			<xs:annotation>
				<xs:documentation xml:lang="en">Used to indicate a level of preference for an associated item (i.e., only, unacceptable, preferred).</xs:documentation>
			</xs:annotation>
		</xs:attributeGroup>
	</xs:attributeGroup>
	<xs:attributeGroup name="CompanyID_AttributesGroup">
		<xs:annotation>
			<xs:documentation xml:lang="en">Provides detailed information on a company.</xs:documentation>
		</xs:annotation>
		<xs:attribute name="CompanyShortName" type="StringLength1to32" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Used to provide the company common name.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="TravelSector" type="OTA_CodeType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The travel sector the company is associated with, such as air, car and hotel. Refer to OpenTravel Code List Travel Sector (TVS).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="Code" type="StringLength1to16" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Identifies a company by the company code.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="CodeContext" type="StringLength1to32" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Identifies the context of the identifying code, such as DUNS, IATA or internal code, etc.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:attributeGroup>
	<xs:attributeGroup name="CurrencyAmountGroup">
		<xs:annotation>
			<xs:documentation xml:lang="en">Provides a monetary amount and the currency code to reflect the currency in which this amount is expressed.</xs:documentation>
		</xs:annotation>
		<xs:attribute name="Amount" type="Money" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">A monetary amount.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attributeGroup ref="CurrencyCodeGroup">
			<xs:annotation>
				<xs:documentation xml:lang="en">Provides a currency code to reflect the currency in which an amount may be expressed as well as the number of decimal places of that currency.</xs:documentation>
			</xs:annotation>
		</xs:attributeGroup>
	</xs:attributeGroup>
	<xs:attributeGroup name="CurrencyCodeGroup">
		<xs:annotation>
			<xs:documentation xml:lang="en">Provides a currency code to reflect the currency in which an amount may be expressed.</xs:documentation>
		</xs:annotation>
		<xs:attribute name="CurrencyCode" type="AlphaLength3" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The code specifying a monetary unit. Use ISO 4217, three alpha code.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="DecimalPlaces" type="xs:nonNegativeInteger" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Indicates the number of decimal places for a particular currency. This is equivalent to the ISO 4217 standard "minor unit". Typically used when the amount provided includes the minor unit of currency without a decimal point (e.g., USD 8500 needs DecimalPlaces="2" to represent $85).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:attributeGroup>
	<xs:attributeGroup name="CustomerLoyaltyGroup">
		<xs:annotation>
			<xs:documentation xml:lang="en">Program rewarding frequent use by accumulating credits for services provided by vendors.</xs:documentation>
		</xs:annotation>
		<xs:attributeGroup ref="PrivacyGroup"/>
		<xs:attribute name="ProgramID" type="StringLength1to16" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Identifier to indicate the company owner of the loyalty program.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="MembershipID" type="StringLength1to32" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Unique identifier of the member in the program (membership number, account number, etc.).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="TravelSector" type="OTA_CodeType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Identifies the travel sector. Refer to OpenTravel Code List Travel Sector (TVS).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attributeGroup ref="LoyalLevelGroup">
			<xs:annotation>
				<xs:documentation xml:lang="en">Provides the level of this customer within a loyalty program.</xs:documentation>
			</xs:annotation>
		</xs:attributeGroup>
		<xs:attributeGroup ref="SingleVendorIndGroup">
			<xs:annotation>
				<xs:documentation xml:lang="en">Indicates the alliance status of a program.</xs:documentation>
			</xs:annotation>
		</xs:attributeGroup>
		<xs:attributeGroup ref="SignupDateGroup">
			<xs:annotation>
				<xs:documentation xml:lang="en">Provides the date of registration for this customer for the loyalty program.</xs:documentation>
			</xs:annotation>
		</xs:attributeGroup>
		<xs:attributeGroup ref="EffectiveExpireOptionalDateGroup">
			<xs:annotation>
				<xs:documentation xml:lang="en">The effective date and/or expiration date of this customer's membership in this loytalty program.</xs:documentation>
			</xs:annotation>
		</xs:attributeGroup>
		<xs:attribute name="RPH" type="RPH_Type" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">A reference placeholder for this loyalty membership.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="VendorCode" type="ListOfStringLength1to8" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Indicate the partner(s)/vendor(s) for which the customer loyalty number is valid.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="PrimaryLoyaltyIndicator" type="xs:boolean" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">When true, indicates this is the primary customer loyalty program and when false, indicates this is not the primary customer loyalty program.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="AllianceLoyaltyLevelName" type="StringLength1to128" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Name of the alliance loyalty level (e.g.,OneWorld  uses Emerald, Ruby, etc and SkyTeam uses Elite, etc.)</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="CustomerType" type="StringLength1to8" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Original assessment of the customer by the travel agent.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="CustomerValue" type="StringLength1to8" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The supplier's ranking of the customer (e.g., VIP, numerical ranking).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="Password" type="StringLength1to32" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The password for the member in the program. </xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:attributeGroup>
	<xs:attributeGroup name="DatePeriodGroup">
		<xs:annotation>
			<xs:documentation xml:lang="en">Used to define a period of time using either actual dates or a day and month.</xs:documentation>
		</xs:annotation>
		<xs:attribute name="StartPeriod" type="DateOrMonthDay" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Defines the start of a period either the day and month or the actual date.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="Duration" type="DurationType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Defines the duration of a period.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="EndPeriod" type="DateOrMonthDay" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Defines the end of a period either the day and month or the actual date.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:attributeGroup>
	<xs:attributeGroup name="DateTimeSpanGroup">
		<xs:annotation>
			<xs:documentation xml:lang="en">The attributes of the OTA DateTimeSpan data type are based on the W3C base data types of timeInstant and timeDuration.</xs:documentation>
			<xs:documentation xml:lang="en">The lexical representation for timeDuration is the [ISO 8601] extended format PnYn MnDTnH nMnS, where nY represents the number of years, nM the number of months, nD the number of days, T is the date/time separator, nH the number of hours, nM the number of minutes and nS the number of seconds. The number of seconds can include decimal digits to arbitrary precision. As an example, 7 months, 2 days, 2hours and 30 minutes would be expressed as P0Y7M2DT2H30M0S. Truncated representations are allowed provided they conform to ISO 8601 format. Time periods, i.e. specific durations of time, can be represented by supplying two items of information: a start instant and a duration or a start instant and an end instant or an end instant and a duration. The OTA standards use the XML mapping that provides for two elements to represent the specific period of time; a startInstant and a duration.</xs:documentation>
		</xs:annotation>
		<xs:attribute name="Start" type="DateOrTimeOrDateTimeType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The starting value of the time span.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="Duration" type="DurationType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The duration datatype represents a combination of year, month, day and time values representing a single duration of time, encoded as a single string.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="End" type="DateOrTimeOrDateTimeType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The ending value of the time span.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:attributeGroup>
	<xs:attributeGroup name="DateTimeStampGroup">
		<xs:annotation>
			<xs:documentation xml:lang="en">Creation date time, Creator Id, last modification date time and last Modifier Id.</xs:documentation>
		</xs:annotation>
		<xs:attribute name="CreateDateTime" type="xs:dateTime" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Time stamp of the creation.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="CreatorID" type="StringLength1to32" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">ID of creator. The creator could be a software system identifier or an identifier of an employee resposible for the creation.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="LastModifyDateTime" type="xs:dateTime" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Time stamp of last modification.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="LastModifierID" type="StringLength1to32" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Identifies the last software system or person to modify a record.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="PurgeDate" type="xs:date" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Date an item will be purged from a database (e.g., from a live database to an archive).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:attributeGroup>
	<xs:attributeGroup name="DeadlineGroup">
		<xs:annotation>
			<xs:documentation xml:lang="en">The absolute deadline or amount of offset time before a deadline for a payment of cancel goes into effect.</xs:documentation>
		</xs:annotation>
		<xs:attribute name="AbsoluteDeadline" type="TimeOrDateTimeType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Defines the absolute deadline. Either this or the offset attributes may be used.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="OffsetTimeUnit" type="TimeUnitType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The units of time, e.g.: days, hours, etc., that apply to the deadline.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="OffsetUnitMultiplier" type="Numeric0to999" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The number of units of DeadlineTimeUnit.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="OffsetDropTime" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">An enumerated type indicating when the deadline drop time goes into effect.</xs:documentation>
			</xs:annotation>
			<xs:simpleType>
				<xs:restriction base="xs:NMTOKEN">
					<xs:enumeration value="BeforeArrival"/>
					<xs:enumeration value="AfterBooking"/>
					<xs:enumeration value="AfterConfirmation">
						<xs:annotation>
							<xs:documentation xml:lang="en">The deadline information applies from when the reservation was confirmed.  In some systems the confirmation time will differ from the booking time.</xs:documentation>
						</xs:annotation>
					</xs:enumeration>
					<xs:enumeration value="AfterArrival">
						<xs:annotation>
							<xs:documentation xml:lang="en">The deadline applies after the scheduled arrival time.</xs:documentation>
						</xs:annotation>
					</xs:enumeration>
				</xs:restriction>
			</xs:simpleType>
		</xs:attribute>
	</xs:attributeGroup>
	<xs:attributeGroup name="DefaultIndGroup">
		<xs:annotation>
			<xs:documentation xml:lang="en">Indicates that the receiving system should assume the default value if the user specifies no overriding value or action.</xs:documentation>
		</xs:annotation>
		<xs:attribute name="DefaultInd" type="xs:boolean" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">When true, indicates a default value should be used.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:attributeGroup>
	<xs:attributeGroup name="DetailResponseGroup">
		<xs:annotation>
			<xs:documentation xml:lang="en">Used to determine the level of detail in the response.</xs:documentation>
		</xs:annotation>
		<xs:attribute name="DetailResponse" type="xs:boolean" use="required">
			<xs:annotation>
				<xs:documentation xml:lang="en">To indicate whether full details should be returned in the response. "True" indicates details should be included and "false" means details are not required. 
				</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:attributeGroup>
	<xs:attributeGroup name="DiscountInfoGroup">
		<xs:annotation>
			<xs:documentation>Used to provide details of either the discount or promotion.</xs:documentation>
		</xs:annotation>
		<xs:attributeGroup ref="CurrencyAmountGroup">
			<xs:annotation>
				<xs:documentation xml:lang="en">The discount amount.</xs:documentation>
			</xs:annotation>
		</xs:attributeGroup>
		<xs:attribute name="Percent" type="Percentage" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The discount as a percent.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="ID" type="StringLength1to32" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">A unique identifier for the discount.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="Description" type="StringLength1to128" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Textual information regarding the discount.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:attributeGroup>
	<xs:attributeGroup name="DistanceAttributesGroup">
		<xs:annotation>
			<xs:documentation xml:lang="en">Used to provide distance and direction information.</xs:documentation>
		</xs:annotation>
		<xs:attribute name="Distance" type="NumericStringLength1to16" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">An optional attribute indicating the distance to/from a reference point. When used in conjunction with DistanceMax, this represents the minimum distance.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="DistanceMeasure" type="StringLength1to16" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">When the Distance attribute contains a value, (presumably a numerical value), the unit of measure is a string value that indicate what units are used for the value.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="Direction" type="StringLength1to8" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">An optional string value used to indicate the compass point(s) direction, e.g.: S, SE (South, Southeast), FROM the Reference Point TO the hotel location if the search is not a full circumference from the reference point.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="DistanceMax" type="NumericStringLength1to16" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">An optional attribute indicating the maximum distance to/from a reference point.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="UnitOfMeasureCode" type="OTA_CodeType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The unit of measure in a code format. Refer to OpenTravel Code List Unit of Measure Code (UOM).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:attributeGroup>
	<xs:attributeGroup name="DOW_PatternGroup">
		<xs:annotation>
			<xs:documentation xml:lang="en">If a day(s) of the week is set to true then the associated item is available on that day of the week  (e.g., if Mon="true" then a flight operates on Mondays or a certain rate is available on Mondays).</xs:documentation>
		</xs:annotation>
		<xs:attribute name="Mon" type="xs:boolean" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">When true, apply to Monday.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="Tue" type="xs:boolean" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">When true, apply to Tuesday.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="Weds" type="xs:boolean" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">When true, apply to Wednesday.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="Thur" type="xs:boolean" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">When true, apply to Thursday.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="Fri" type="xs:boolean" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">When true, apply to Friday.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="Sat" type="xs:boolean" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">When true, apply to Saturday.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="Sun" type="xs:boolean" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">When true, apply to Sunday.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:attributeGroup>
	<xs:attributeGroup name="EffectiveExpireOptionalDateGroup">
		<xs:annotation>
			<xs:documentation xml:lang="en">Used to send the effective date and/or expiration date.</xs:documentation>
		</xs:annotation>
		<xs:attribute name="EffectiveDate" type="xs:date" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Indicates the starting date.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="ExpireDate" type="xs:date" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Indicates the ending date.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="ExpireDateExclusiveIndicator" type="xs:boolean" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">When true, indicates that the ExpireDate is the first day after the applicable period (e.g. when expire date is Oct 15  the last date of the period is Oct 14).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:attributeGroup>
	<xs:attributeGroup name="ErrorWarningAttributeGroup">
		<xs:annotation>
			<xs:documentation xml:lang="en">Used to identify an application error by either text, code, or by an online description and also to give the status, tag, and/or identification of the record that may have caused the error.</xs:documentation>
		</xs:annotation>
		<xs:attribute name="ShortText" type="StringLength1to64" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">An abbreviated version of the error in textual format.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="Code" type="OTA_CodeType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">If present, this refers to a table of coded values exchanged between applications to identify errors or warnings. Refer to OpenTravel Code List Error Codes (ERR).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="DocURL" type="xs:anyURI" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">If present, this URL refers to an online description of the error that occurred.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="Status" type="StringLength1to64" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">If present, recommended values are those enumerated in the OTA_ErrorRS, (NotProcessed | Incomplete | Complete | Unknown) however, the data type is designated as string data, recognizing that trading partners may identify additional status conditions not included in the enumeration.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="Tag" type="xs:string" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">If present, this attribute may identify an unknown or misspelled tag that caused an error in processing. It is recommended that the Tag attribute use XPath notation to identify the location of a tag in the event that more than one tag of the same name is present in the document. Alternatively, the tag name alone can be used to identify missing data [Type=ReqFieldMissing].</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="RecordID" type="StringLength1to64" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">If present, this attribute allows for batch processing and the identification of the record that failed amongst a group of records. This value may contain a concatenation of a unique failed transaction ID with specific record(s) associated with that transaction.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:attributeGroup>
	<xs:attributeGroup name="ExchangeRateGroup">
		<xs:annotation>
			<xs:documentation xml:lang="en">Used to specify the rate for exchanging from one currency to another currency.</xs:documentation>
		</xs:annotation>
		<xs:attribute name="FromCurrency" type="AlphaLength3" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The source currency for a conversion.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="ToCurrency" type="AlphaLength3" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The target currency for the conversion.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="Rate" type="xs:decimal" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The rate used for conversion from the source currency to the target currency.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="Date" type="xs:date" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The date of the conversion rate.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:attributeGroup>
	<xs:attributeGroup name="FeeTaxGroup">
		<xs:annotation>
			<xs:documentation xml:lang="en">Defines the fees and/or taxes associated with a charge (e.g. taxes associated with a hotel rate).</xs:documentation>
		</xs:annotation>
		<xs:attribute name="Type" type="AmountDeterminationType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Used to indicate if the amount is inclusive or exclusive of other charges, such as taxes, or is cumulative (amounts have been added to each other).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="Code" type="OTA_CodeType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Code identifying the fee (e.g.,agency fee, municipality fee). Refer to OpenTravel Code List Fee Tax Type (FTT).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="Percent" type="Percentage" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Fee percentage; if zero, assume use of the Amount attribute (Amount or Percent must be a zero value).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attributeGroup ref="CurrencyAmountGroup">
			<xs:annotation>
				<xs:documentation xml:lang="en">Provides a currency code and an amount for the fee or tax.</xs:documentation>
			</xs:annotation>
		</xs:attributeGroup>
	</xs:attributeGroup>
	<xs:attributeGroup name="FileAttachmentGroup">
		<xs:annotation>
			<xs:documentation xml:lang="en">Provides information about any files attached (e.g., multimedia objects) at the transport layer (e.g., HTTP/SOAP)</xs:documentation>
		</xs:annotation>
		<xs:attributeGroup ref="CodeInfoGroup">
			<xs:annotation>
				<xs:documentation xml:lang="en">May be used to give further detail on the code or to remove an obsolete item.</xs:documentation>
			</xs:annotation>
		</xs:attributeGroup>
		<xs:attribute name="ContentData" type="xs:string" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Vendor-specific format that contains the content data for the multimedia object.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="Description" type="xs:string" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">A short description of the multimedia object.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="PictureCategoryCode" type="OTA_CodeType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">A code defining the type of picture (e.g. Exterior, Lobby, Reception area, RoomTypes, Facilities, Dining areas, Meeting Rooms, Logo). Refer to OpenTravel Code List Picture Category Code (PIC).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="Version" type="xs:string" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The version of the multimedia object.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="ContentTitle" type="xs:string" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The title for the multimedia object.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="ContentCaption" type="xs:string" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The caption to be published with the multimedia file.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="CopyrightNotice" type="xs:string" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The information describing the copyright notice for the multimedia object at a hotel facility. If this field is filled in, this copyright notice must be printed with the multimedia object.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="FileName" type="StringLength1to64" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Specifies the name of the file being sent.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="FileSize" type="xs:positiveInteger" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The size of the file sent, in bytes. This may be used to validate that the received file is the correct size.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="MultimediaObjectHeight" type="xs:positiveInteger" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The height of the image.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="MultimediaObjectWidth" type="xs:positiveInteger" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The width of the image.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="UnitOfMeasureCode" type="OTA_CodeType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The unit of measure for the multimedia object (e.g., inches, pixels, centimeters). Refer to OpenTravel Code List Unit of Measure Code (UOM).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="ContentID" type="StringLength1to64" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The content ID of a file attachment with the prefix 'cid:'. The value of this can be used to retrieve the corresponding attachment by the receiving system.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="ContentCode" type="OTA_CodeType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Description of the multimedia object or attached file contents. Refer to OpenTravel Code List Content Code (CTT).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="ContentFormatCode" type="OTA_CodeType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The specific file format of the multimedia object or attached file (e.g., mpeg, jpg, gif). Refer to OpenTravel Code List Content Format Code (CFC).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="RecordID" type="StringLength1to32" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Uniquely identifies this file in the message.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:attributeGroup>
	<xs:attributeGroup name="FormattedInd">
		<xs:annotation>
			<xs:documentation xml:lang="en">Identifies if the associated data is formatted into its individual pieces, or exists as a single entity.</xs:documentation>
		</xs:annotation>
		<xs:attribute name="FormattedInd" type="xs:boolean" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Specifies if the associated data is formatted or not. When true, then it is formatted; when false, then not formatted.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:attributeGroup>
	<xs:attributeGroup name="GenderGroup">
		<xs:annotation>
			<xs:documentation xml:lang="en">Used to indicate the gender of a person, if known.</xs:documentation>
		</xs:annotation>
		<xs:attribute name="Gender" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Identifies the gender.</xs:documentation>
			</xs:annotation>
			<xs:simpleType>
				<xs:restriction base="xs:NMTOKEN">
					<xs:enumeration value="Male"/>
					<xs:enumeration value="Female"/>
					<xs:enumeration value="Unknown"/>
					<xs:enumeration value="Male_NoShare"/>
					<xs:enumeration value="Female_NoShare"/>
				</xs:restriction>
			</xs:simpleType>
		</xs:attribute>
	</xs:attributeGroup>
	<xs:attributeGroup name="HotelReferenceGroup">
		<xs:annotation>
			<xs:documentation xml:lang="en">HotelReference: The hotel reference identifies a specific hotel by using the Chain Code, the Brand Code, and the Hotel Code. The codes used are agreed upon by trading partners.</xs:documentation>
		</xs:annotation>
		<xs:attribute name="ChainCode" type="StringLength1to8" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The code that identifies a hotel chain or management group. The hotel chain code is decided between vendors. This attribute is optional if the hotel is an independent property that can be identified by the HotelCode attribute.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="BrandCode" type="StringLength1to8" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">A code that identifies the brand or flag of a hotel, often used for independently-owned or franchised properties who are known by a specific brand.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="HotelCode" type="StringLength1to16" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The code that uniquely identifies a single hotel property. The hotel code is decided between vendors.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="HotelCityCode" type="StringLength1to8" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The IATA city code; for example DCA, ORD.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="HotelName" type="StringLength1to128" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">A text field used to communicate the proper name of the hotel.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="HotelCodeContext" type="StringLength1to32" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">A text field used to communicate the context (or source of - ex Sabre, Galileo, Worldspan, Amadeus) the HotelReferenceGroup codes.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="ChainName" type="StringLength1to64" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The name of the hotel chain (e.g., Hilton, Marriott, Hyatt, Starwood).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="BrandName" type="StringLength1to64" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The name of a brand of hotels (e.g., Courtyard, Hampton Inn).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attributeGroup ref="AreaID_Group">
			<xs:annotation>
				<xs:documentation xml:lang="en">An identifier of an area as defined by a hotel reservation system.</xs:documentation>
			</xs:annotation>
		</xs:attributeGroup>
	</xs:attributeGroup>
	<xs:attributeGroup name="ID_Group">
		<xs:annotation>
			<xs:documentation xml:lang="en">Used to provide a required unique identifier.</xs:documentation>
		</xs:annotation>
		<xs:attribute name="ID" type="StringLength1to32" use="required">
			<xs:annotation>
				<xs:documentation xml:lang="en">A unique identifying value assigned by the creating system. The ID attribute may be used to reference a primary-key value within a database or in a particular implementation.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:attributeGroup>
	<xs:attributeGroup name="ID_LevelTitleGroup">
		<xs:annotation>
			<xs:documentation xml:lang="en">Provides employee information.</xs:documentation>
		</xs:annotation>
		<xs:attributeGroup ref="ID_OptionalGroup">
			<xs:annotation>
				<xs:documentation xml:lang="en">Identifier assigned to the employee.</xs:documentation>
			</xs:annotation>
		</xs:attributeGroup>
		<xs:attribute name="Level" type="StringLength1to32" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Level in employer organization (e.g. seniority) that conveys privileges.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="Title" type="StringLength1to32" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Title of employee in the employer company that conveys rank or privileges.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:attributeGroup>
	<xs:attributeGroup name="ID_OptionalGroup">
		<xs:annotation>
			<xs:documentation xml:lang="en">Used to provide an optional unique identifier.</xs:documentation>
		</xs:annotation>
		<xs:attribute name="ID" type="StringLength1to32" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">A unique identifying value assigned by the creating system. The ID attribute may be used to reference a primary-key value within a database or in a particular implementation.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:attributeGroup>
	<xs:attributeGroup name="IssuerNameGroup">
		<xs:annotation>
			<xs:documentation xml:lang="en">Name of bank or organization issuing the card (e.g., alumni association, bank, fraternal organization, etc.).</xs:documentation>
		</xs:annotation>
		<xs:attribute name="BankID" type="StringLength1to64" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Code of bank issuing the card.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:attributeGroup>
	<xs:attributeGroup name="LanguageGroup">
		<xs:annotation>
			<xs:documentation xml:lang="en">Identifies language.</xs:documentation>
		</xs:annotation>
		<xs:attribute name="Language" type="xs:language" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Language identification.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:attributeGroup>
	<xs:attributeGroup name="LocationGroup">
		<xs:annotation>
			<xs:documentation xml:lang="en">A code identifying a location.</xs:documentation>
		</xs:annotation>
		<xs:attribute name="LocationCode" type="StringLength1to16" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">A code used to identify a location.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="CodeContext" type="StringLength1to32" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Identifies the context of the identifying code (e.g., IATA, ARC, or internal code).</xs:documentation>
				<xs:documentation xml:lang="en">
					<LegacyDefaultValue>IATA</LegacyDefaultValue>
				</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:attributeGroup>
	<xs:attributeGroup name="LoyalLevelGroup">
		<xs:annotation>
			<xs:documentation xml:lang="en">Provides the level within a loyalty program.</xs:documentation>
		</xs:annotation>
		<xs:attribute name="LoyalLevel" type="StringLength1to16" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Indicates special privileges in program assigned to individual.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="LoyalLevelCode" type="Numeric1to3" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Provides a numeric code assigned to a particular loyalty level.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:attributeGroup>
	<xs:attributeGroup name="LoyaltyCertificateGroup">
		<xs:annotation>
			<xs:documentation xml:lang="en">Identifies the Loyalty Program, membership, form factor used by the certificate and its current status.</xs:documentation>
		</xs:annotation>
		<xs:attributeGroup ref="ID_OptionalGroup">
			<xs:annotation>
				<xs:documentation xml:lang="en">Identifies the individual program or promotion within a loyalty scheme.</xs:documentation>
			</xs:annotation>
		</xs:attributeGroup>
		<xs:attribute name="ID_Context" type="StringLength1to32" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Identifies the source of the code that identifies program or promotion within a loyalty scheme.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attributeGroup ref="LoyaltyCertificateNumberGroup">
			<xs:annotation>
				<xs:documentation xml:lang="en">Identifies the unique certificate number and the loyalty program and the membership ID associated with this certificate.</xs:documentation>
			</xs:annotation>
		</xs:attributeGroup>
		<xs:attributeGroup ref="EffectiveExpireOptionalDateGroup">
			<xs:annotation>
				<xs:documentation xml:lang="en">Identifies either the date range when the Certificate is valid or the dates against which the certificate is being applied.</xs:documentation>
			</xs:annotation>
		</xs:attributeGroup>
		<xs:attribute name="NmbrOfNights" type="xs:nonNegativeInteger" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The number of nights of the hotel stay that are used to calculate the redemption amount.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="Format" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Indicates what form the certificate is in e.g. Paper or Electronic.</xs:documentation>
			</xs:annotation>
			<xs:simpleType>
				<xs:restriction base="xs:NMTOKEN">
					<xs:enumeration value="Paper"/>
					<xs:enumeration value="Electronic"/>
				</xs:restriction>
			</xs:simpleType>
		</xs:attribute>
		<xs:attribute name="Status" type="StringLength1to16" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Used to define the status of the certificate e.g. Available, Voided, Cancelled, Reserved, Used.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:attributeGroup>
	<xs:attributeGroup name="LoyaltyCertificateNumberGroup">
		<xs:annotation>
			<xs:documentation xml:lang="en">Identifies a loyalty certificate.</xs:documentation>
		</xs:annotation>
		<xs:attribute name="CertificateNumber" type="StringLength1to32" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The loyalty redemption certificate identifier.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="MemberNumber" type="StringLength1to32" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Unique identifier of the member in the program.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="ProgramName" type="StringLength1to64" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">This identifies the loyalty program.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:attributeGroup>
	<xs:attributeGroup name="MaxResponsesGroup">
		<xs:annotation>
			<xs:documentation xml:lang="en">Used to specify the maximum number of responses to a request.</xs:documentation>
		</xs:annotation>
		<xs:attribute name="MaxResponses" type="xs:positiveInteger" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">A positive integer value that indicates the maximum number of responses desired in the return.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:attributeGroup>
	<xs:attributeGroup name="MultimediaDescriptionGroup">
		<xs:annotation>
			<xs:documentation xml:lang="en">Generic information about a multimedia item.</xs:documentation>
		</xs:annotation>
		<xs:attribute name="ContentID" type="StringLength1to64" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The content ID of a file attachment with the prefix 'cid:'. The value of this can be used to retrieve the corresponding attachment by the receiving system.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="Title" type="StringLength1to64" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The title of the multimedia object.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="Author" type="StringLength1to64" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The author of the multimedia object.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="CopyrightNotice" type="StringLength1to64" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">A copyright notice for the multimedia object.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="CopyrightOwner" type="StringLength1to64" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Owner of the copyright for the multimedia content.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="CopyrightStart" type="DateOrDateTimeType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The start date for which the multimedia content rights are claimed.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="CopyrightEnd" type="DateOrDateTimeType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The end date for which the multimedia content rights are claimed.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="EffectiveStart" type="DateOrDateTimeType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The start date for which the content is considered valid.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="EffectiveEnd" type="DateOrDateTimeType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The end date for which the content is considered valid.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="ApplicableStart" type="DateOrMonthDay" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Start month and day or date for which the multimedia content is relevent (e.g. the start of a season or the start of an event). When a year is not used (i.e. only the month and day) it signifies a recurring event.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="ApplicableEnd" type="DateOrMonthDay" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">End month and day or date for which the multimedia content is relevent (e.g. the end of a season or the start of an event). When a year is not used (i.e. only the month and day) it signifies a recurring event.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="RecordID" type="StringLength1to32" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Uniquely identifies this file in the message.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="SourceID" type="StringLength1to32" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Unique identifier for the source of the multimedia object (e.g., the original image file).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:attributeGroup>
	<xs:attributeGroup name="MultimediaItemGroup">
		<xs:annotation>
			<xs:documentation xml:lang="en">Specific information about a multimedia item.</xs:documentation>
		</xs:annotation>
		<xs:attribute name="Language" type="xs:language" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Identifies the language of the multimedia item.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="Format" type="OTA_CodeType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The code associated with the format of the multimedia item. Refer to OpenTravel Code list Content Format Code (CFC).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="FileSize" type="xs:positiveInteger" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The size of the multimedia file in bytes.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="FileName" type="StringLength1to64" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The name of the multimedia  file.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:attributeGroup>
	<xs:attributeGroup name="NameOptionalCodeGroup">
		<xs:annotation>
			<xs:documentation xml:lang="en">An attribute group to be used when the associated item has a required name and an optional code. If the length of the name could exceed 64 characters the complexType LongNameoptionalCodeType should be used.</xs:documentation>
		</xs:annotation>
		<xs:attribute name="Name" type="StringLength1to64" use="required">
			<xs:annotation>
				<xs:documentation xml:lang="en">The name of an item.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="Code" type="StringLength1to8" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Provides the code identifying the item.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:attributeGroup>
	<xs:attributeGroup name="OccupancyGroup">
		<xs:annotation>
			<xs:documentation xml:lang="en">Minimum and maximum occupancy values.</xs:documentation>
		</xs:annotation>
		<xs:attribute name="MinOccupancy" type="Numeric0to99" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Minimum number of persons allowed in a unit of accommodation or place.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="MaxOccupancy" type="Numeric1to99" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Maximum number of persons allowed in a unit of accommodation or place.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:attributeGroup>
	<xs:attributeGroup name="OfficeTypeGroup">
		<xs:annotation>
			<xs:documentation xml:lang="en">Designates the office category within an organization.</xs:documentation>
		</xs:annotation>
		<xs:attribute name="OfficeType" type="OfficeLocationType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Indicates main office, field office, or division of the organization.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:attributeGroup>
	<xs:attributeGroup name="OptionalCodeOptionalNameGroup">
		<xs:annotation>
			<xs:documentation xml:lang="en">An attribute group to be used when the associated item has an  optional code and an optional name.</xs:documentation>
		</xs:annotation>
		<xs:attribute name="Code" type="StringLength1to8" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Provides the code identifying the item.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="Name" type="StringLength1to64" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The name of an item.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:attributeGroup>
	<xs:attributeGroup name="OriginalIssueAttributes">
		<xs:annotation>
			<xs:documentation xml:lang="en">Provides information about the original document on which the reissue is based.</xs:documentation>
		</xs:annotation>
		<xs:attribute name="OriginalTicketNumber" type="StringLength1to32" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The original ticket number in a series if reissuances.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="OriginalIssuePlace" type="StringLength1to8" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Place where the original ticket was issued.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="OriginalIssueDate" type="xs:date" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Date when the original ticket was issued.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="OriginalIssueIATA" type="StringLength1to8" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">IATA office number, which issued original ticket.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="OriginalPaymentForm" type="StringLength1to64" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Payment type for the original ticket.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="CheckInhibitorType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Indicates if the check digit of the ticket number or the interline agreement has to be checked or not.</xs:documentation>
			</xs:annotation>
			<xs:simpleType>
				<xs:restriction base="xs:NMTOKEN">
					<xs:enumeration value="CheckDigit">
						<xs:annotation>
							<xs:documentation xml:lang="en">The CheckDigit of the TicketNumber does not need to be verified.</xs:documentation>
						</xs:annotation>
					</xs:enumeration>
					<xs:enumeration value="InterlineAgreement">
						<xs:annotation>
							<xs:documentation xml:lang="en">No automatic interline agreement check needs to be performed on the ticket to be reissued.</xs:documentation>
						</xs:annotation>
					</xs:enumeration>
					<xs:enumeration value="Both">
						<xs:annotation>
							<xs:documentation xml:lang="en">Neither the ticket number check digit nor the interline agreement needs to be checked.</xs:documentation>
						</xs:annotation>
					</xs:enumeration>
				</xs:restriction>
			</xs:simpleType>
		</xs:attribute>
		<xs:attribute name="CouponRPHs" type="ListOfRPH" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Gives a list of references to coupon numbers of the ticket which will be taken for payment.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:attributeGroup>
	<xs:attributeGroup name="OTA_PayloadStdAttributes">
		<xs:annotation>
			<xs:documentation xml:lang="en">The OTA_PayloadStdAttributes defines the standard attributes that appear on the root element for all OpenTravel messages.</xs:documentation>
		</xs:annotation>
		<xs:attribute name="EchoToken" type="StringLength1to128" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">A reference for additional message identification, assigned by the requesting host system. When a request message includes an echo token the corresponding response message MUST include an echo token with an identical value.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="TimeStamp" type="xs:dateTime" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Indicates the creation date and time of the message in UTC using the following format specified by ISO 8601; YYYY-MM-DDThh:mm:ssZ with time values using the 24 hour clock (e.g. 20 November 2003, 1:59:38 pm UTC becomes 2003-11-20T13:59:38Z).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="Target" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Used to indicate whether the request is for the Test or Production system.</xs:documentation>
				<xs:documentation xml:lang="en">
					<LegacyDefaultValue>Production</LegacyDefaultValue>
				</xs:documentation>
			</xs:annotation>
			<xs:simpleType>
				<xs:restriction base="xs:NMTOKEN">
					<xs:enumeration value="Test">
						<xs:annotation>
							<xs:documentation xml:lang="en">A test system.</xs:documentation>
						</xs:annotation>
					</xs:enumeration>
					<xs:enumeration value="Production">
						<xs:annotation>
							<xs:documentation xml:lang="en">A production system.</xs:documentation>
						</xs:annotation>
					</xs:enumeration>
				</xs:restriction>
			</xs:simpleType>
		</xs:attribute>
		<xs:attribute name="TargetName" type="StringLength1to64" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Used to indicate the name of the Test or Production system.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="Version" type="xs:decimal" use="required">
			<xs:annotation>
				<xs:documentation xml:lang="en">For all OpenTravel versioned messages, the version of the message is indicated by a decimal value.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="TransactionIdentifier" type="xs:string" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">A unique identifier to relate all messages within a transaction (e.g. this would be sent in all request and response messages that are part of an on-going transaction).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="SequenceNmbr" type="xs:nonNegativeInteger" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Used to identify the sequence number of the transaction as assigned by the sending system; allows for an application to process messages in a certain order or to request a resynchronization of messages in the event that a system has been off-line and needs to retrieve messages that were missed.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="TransactionStatusCode" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">This indicates where this message falls within a sequence of messages.</xs:documentation>
			</xs:annotation>
			<xs:simpleType>
				<xs:restriction base="xs:NMTOKEN">
					<xs:enumeration value="Start">
						<xs:annotation>
							<xs:documentation xml:lang="en">This is the first message within a transaction.</xs:documentation>
						</xs:annotation>
					</xs:enumeration>
					<xs:enumeration value="End">
						<xs:annotation>
							<xs:documentation xml:lang="en">This is the last message within a transaction.</xs:documentation>
						</xs:annotation>
					</xs:enumeration>
					<xs:enumeration value="Rollback">
						<xs:annotation>
							<xs:documentation xml:lang="en">This indicates that all messages within the current transaction must be ignored.</xs:documentation>
						</xs:annotation>
					</xs:enumeration>
					<xs:enumeration value="InSeries">
						<xs:annotation>
							<xs:documentation xml:lang="en">This is any message that is not the first or last message within a transaction.</xs:documentation>
						</xs:annotation>
					</xs:enumeration>
					<xs:enumeration value="Continuation">
						<xs:annotation>
							<xs:documentation xml:lang="en">Specifies that this is a followup request asking for more of what was requested in the previous request.</xs:documentation>
						</xs:annotation>
					</xs:enumeration>
					<xs:enumeration value="Subsequent">
						<xs:annotation>
							<xs:documentation xml:lang="en">This request message is a subsequent request based on the previous message sent in this transaction.</xs:documentation>
						</xs:annotation>
					</xs:enumeration>
				</xs:restriction>
			</xs:simpleType>
		</xs:attribute>
		<xs:attributeGroup ref="PrimaryLangID_Group">
			<xs:annotation>
				<xs:documentation xml:lang="en">Identifes the primary language for the message.</xs:documentation>
			</xs:annotation>
		</xs:attributeGroup>
		<xs:attributeGroup ref="AltLangID_Group">
			<xs:annotation>
				<xs:documentation xml:lang="en">Identifes the alternate language for the message.</xs:documentation>
			</xs:annotation>
		</xs:attributeGroup>
		<xs:attribute name="RetransmissionIndicator" type="xs:boolean" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">When true, indicates the message is being re-sent after a failure. It is recommended that this attribute is used (i.e., set to TRUE) only when a message is retransmitted.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="CorrelationID" type="StringLength1to64" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Allow end-to-end correlation of log messages with the corresponding Web service message throughout the processing of the Web service message.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:attributeGroup>
	<xs:attributeGroup name="PaymentCardDateGroup">
		<xs:annotation>
			<xs:documentation xml:lang="en">Indicates the start and end date for a payment card.</xs:documentation>
		</xs:annotation>
		<xs:attribute name="EffectiveDate" type="MMYYDate" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Indicates the starting date.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="ExpireDate" type="MMYYDate" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Indicates the ending date.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:attributeGroup>
	<xs:attributeGroup name="PositionGroup">
		<xs:annotation>
			<xs:documentation xml:lang="en">Used to specify the geographic coordinates of a location.</xs:documentation>
		</xs:annotation>
		<xs:attribute name="Latitude" type="StringLength1to16" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The measure of the angular distance on a meridian north or south of the equator.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="Longitude" type="StringLength1to16" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The measure of the angular distance on a meridian east or west of the prime meridian.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="Altitude" type="StringLength1to16" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The height of an item, typically above sea level.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="AltitudeUnitOfMeasureCode" type="OTA_CodeType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Provides the unit of measure for the altitude (e.g., feet, meters, miles, kilometers). Refer to OpenTravel Code List Unit of Measure Code (UOM).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="PositionAccuracy" type="OTA_CodeType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Indicates the accuracy of the property's geo-coding, since the property's longitude and latitude may not always be exact. Refer to OpenTravel Code List Position Accuracy Code (PAC).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:attributeGroup>
	<xs:attributeGroup name="PreferLevelGroup">
		<xs:attribute name="PreferLevel" type="PreferLevelType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Used to indicate a level of preference for an associated item.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:attributeGroup>
	<xs:attributeGroup name="PrimaryLangID_Group">
		<xs:annotation>
			<xs:documentation xml:lang="en">Identifes the primary language preference for the message.</xs:documentation>
		</xs:annotation>
		<xs:attribute name="PrimaryLangID" type="xs:language" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Identifies the primary language preference for the message. The human language is identified by ISO 639 codes.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:attributeGroup>
	<xs:attributeGroup name="PrivacyGroup">
		<xs:annotation>
			<xs:documentation xml:lang="en">Allows for control of the sharing of data between parties.</xs:documentation>
		</xs:annotation>
		<xs:attribute name="ShareSynchInd" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Permission for sharing data for synchronization of information held by other travel service providers.</xs:documentation>
			</xs:annotation>
			<xs:simpleType>
				<xs:annotation>
					<xs:documentation xml:lang="en"> If the value=Inherit, specifies data sharing permissions for synchronization of information held by other travel service providers.</xs:documentation>
				</xs:annotation>
				<xs:restriction base="xs:NMTOKEN">
					<xs:enumeration value="Yes"/>
					<xs:enumeration value="No"/>
					<xs:enumeration value="Inherit"/>
				</xs:restriction>
			</xs:simpleType>
		</xs:attribute>
		<xs:attribute name="ShareMarketInd" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Permission for sharing data for marketing purposes.</xs:documentation>
			</xs:annotation>
			<xs:simpleType>
				<xs:annotation>
					<xs:documentation xml:lang="en"> If the value=Inherit, specifies data sharing permissions for marketing purposes.</xs:documentation>
				</xs:annotation>
				<xs:restriction base="xs:NMTOKEN">
					<xs:enumeration value="Yes"/>
					<xs:enumeration value="No"/>
					<xs:enumeration value="Inherit"/>
				</xs:restriction>
			</xs:simpleType>
		</xs:attribute>
	</xs:attributeGroup>
	<xs:attributeGroup name="ProcessingInfoGroup">
		<xs:annotation>
			<xs:documentation xml:lang="en">Specifies the action to be taken on this information and the effective and discontinue dates.</xs:documentation>
		</xs:annotation>
		<xs:attribute name="Action" type="ActionType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">To specify if the action to be taken is a replacement, addition, deletion, or update.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attributeGroup ref="DateTimeSpanGroup">
			<xs:annotation>
				<xs:documentation xml:lang="en">The effective and discontinue dates for the information.</xs:documentation>
			</xs:annotation>
		</xs:attributeGroup>
		<xs:attributeGroup ref="CompanyID_AttributesGroup">
			<xs:annotation>
				<xs:documentation xml:lang="en">Identifies the owner of the information.</xs:documentation>
			</xs:annotation>
		</xs:attributeGroup>
	</xs:attributeGroup>
	<xs:attributeGroup name="ProfileTypeGroup">
		<xs:annotation>
			<xs:documentation xml:lang="en">Used to specify a profile type.</xs:documentation>
		</xs:annotation>
		<xs:attribute name="ProfileType" type="OTA_CodeType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Code to specify a profile such as Customer, Tour Operator, Corporation, etc. Refer to OpenTravel Code List Profile Type (PRT).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:attributeGroup>
	<xs:attributeGroup name="PromotionCodeGroup">
		<xs:annotation>
			<xs:documentation xml:lang="en">Used to provide a promotion code.</xs:documentation>
		</xs:annotation>
		<xs:attribute name="PromotionCode" type="StringLength1to32" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Promotion code is the identifier used by the host to link directly with a specific named advertising campaign. By including the required code, the client is able to gain access to special offers which may have been created for a specifically targeted group via a CRM system or for a wider advertising campaign using Television or press adverts.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="PromotionVendorCode" type="ListOfStringLength1to8" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">List of the vendor codes associated with a promotion.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:attributeGroup>
	<xs:attributeGroup name="QuantityGroup">
		<xs:annotation>
			<xs:documentation xml:lang="en">Used to define a quantity.</xs:documentation>
		</xs:annotation>
		<xs:attribute name="Quantity" type="xs:nonNegativeInteger" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Used to define the quantity for an associated element or attribute.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:attributeGroup>
	<xs:attributeGroup name="QueueGroup">
		<xs:annotation>
			<xs:documentation xml:lang="en">Information to identify a queue.</xs:documentation>
		</xs:annotation>
		<xs:attribute name="PseudoCityCode" type="StringLength1to16" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The ATA/ IATA airport/city code, office code, pseudo city code, etc. of the queue.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="QueueNumber" type="AlphaNumericStringLength1to8" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">An identifier specifying the queue on which the booking file resides in the system.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="QueueCategory" type="AlphaNumericStringLength1to8" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The category of the queue.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="SystemCode" type="StringLength1to8" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Identifies the airline and/or system where the queue resides. If this is omitted, the airline and/or system code (AirlineVendorID) contained in the point of sale information should be used.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="QueueID" type="AlphaNumericStringLength1to8" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">An additional identifier to determine the exact queue on which a reservation record should be placed.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:attributeGroup>
	<xs:attributeGroup name="RateQualifierCoreGroup">
		<xs:annotation>
			<xs:documentation xml:lang="en">Defines the rate information that is common to all transactions.  Such information may include rate codes, rate type, promotional codes, etc. This information may be used to determine the rate that is made available.</xs:documentation>
		</xs:annotation>
		<xs:attribute name="TravelPurpose" type="OTA_CodeType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Used to indicate the purpose, whether for business, personal or other. Refer to OpenTravel Code List Travel Purpose (TVP).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="RateCategory" type="OTA_CodeType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The RateCategory attribute defines a set of valid values for the category of a rate. Typically rates are offered as either Leisure rates or Business (Corporate) rates, with a business rate usually including additional costs such as the cost of insurance, etc.  This set of values defines the rate categories. Refer to OpenTravel Code List Rate Category (RTC).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="CorpDiscountNmbr" type="StringLength1to32" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">This is the vendor specific code used to identify a special rate associated with a specific organization.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attributeGroup ref="PromotionCodeGroup">
			<xs:annotation>
				<xs:documentation xml:lang="en">Promotion code is the identifier used by the host to link directly with a specific named advertising campaign. By including the required code, the client is able to gain access to special offers which may have been created for a specifically targeted group via a CRM system or for a wider advertising campaign using Television or press adverts.</xs:documentation>
			</xs:annotation>
		</xs:attributeGroup>
		<xs:attribute name="RateQualifier" type="StringLength1to32" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">This is the vendor specific code for rate codes (e.g. WES, 2A, DLY00).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="RatePeriod" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The RatePeriod attribute defines the type of rate that may be applied. For example, typically car rental rates differ based upon the duration of the rental, and the actual rate is then expressed in terms of the period of the rental.</xs:documentation>
			</xs:annotation>
			<xs:simpleType>
				<xs:restriction base="RatePeriodSimpleType"/>
			</xs:simpleType>
		</xs:attribute>
		<xs:attribute name="GuaranteedInd" type="xs:boolean" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">When true, only guaranteed rates should be returned. When false, all rates should be returned.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:attributeGroup>
	<xs:attributeGroup name="RateRangeGroup">
		<xs:annotation>
			<xs:documentation xml:lang="en">A range of monetary values within which the cost of the available products are requested.</xs:documentation>
		</xs:annotation>
		<xs:attribute name="MinRate" type="Money" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">A decimal value that indicates the minimum monetary value to be considered in a request for an available product.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="MaxRate" type="Money" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">A decimal value that indicates the maximum monetary value to be considered in a request for an available product.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="FixedRate" type="Money" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The rate amount used in place of MinRate and MaxRate when a specific rate is being requested.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="RateTimeUnit" type="TimeUnitType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Used to specify the period of time to which the rates apply.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attributeGroup ref="CurrencyCodeGroup">
			<xs:annotation>
				<xs:documentation xml:lang="en">Used to provide currency code and decimal places for the rate attributes.</xs:documentation>
			</xs:annotation>
		</xs:attributeGroup>
	</xs:attributeGroup>
	<xs:attributeGroup name="RegionGroup">
		<xs:annotation>
			<xs:documentation xml:lang="en">Contains the region information for the sailing</xs:documentation>
		</xs:annotation>
		<xs:attribute name="RegionCode" type="StringLength1to8" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Specifies a region code.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="RegionName" type="StringLength1to64" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Specifies the region name.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="SubRegionCode" type="StringLength1to8" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">A sub-region code for the specified region.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="SubRegionName" type="StringLength1to32" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">A sub-region name for the specified region.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:attributeGroup>
	<xs:attributeGroup name="RelativePositionGroup">
		<xs:annotation>
			<xs:documentation xml:lang="en">Defines the position of an entity in relation to another entity (e.g. from an airport to a hotel, the relationship is dependant on use).</xs:documentation>
		</xs:annotation>
		<xs:attribute name="Direction" type="xs:string" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Defines the cardinal direction (e.g., north, south, southwest).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="Distance" type="xs:string" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Defines the distance between two points.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="DistanceUnitName" type="DistanceUnitNameType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">
					<DeprecationWarning>Candidate for potential removal, usage is not recommended. Deprecation Warning added in 2006A.</DeprecationWarning>
				</xs:documentation>
				<xs:documentation xml:lang="en">Provides the ability to specify the unit of measure to which the "Distance" attribute is referring.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="UnitOfMeasureCode" type="OTA_CodeType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The unit of measure in a code format. Refer to OpenTravel Code List Unit of Measure Code (UOM).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:attributeGroup>
	<xs:attributeGroup name="RemovalGroup">
		<xs:annotation>
			<xs:documentation xml:lang="en">This is used to indicate that an item is obsolete.</xs:documentation>
		</xs:annotation>
		<xs:attribute name="Removal" type="xs:boolean" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">If true, this item is obsolete and should be removed from the receiving system.</xs:documentation>
				<xs:documentation xml:lang="en">
					<LegacyDefaultValue>false</LegacyDefaultValue>
				</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:attributeGroup>
	<xs:attributeGroup name="ReqRespVersion">
		<xs:annotation>
			<xs:documentation xml:lang="en">Used to request the version of the payload message desired for the response.</xs:documentation>
		</xs:annotation>
		<xs:attribute name="ReqRespVersion" type="StringLength1to64" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Used to request the version of the payload message desired for the response.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:attributeGroup>
	<xs:attributeGroup name="ResponseGroup">
		<xs:annotation>
			<xs:documentation xml:lang="en">Indicates that additional records are available and provides the echo token to be used to retrieve those records.</xs:documentation>
		</xs:annotation>
		<xs:attribute name="MoreIndicator" type="xs:boolean" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">If true, this indicates more items are available. If false, no more items are available.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="MoreDataEchoToken" type="StringLength1to128" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">A reference to the last response returned. Originally set in the response message and will be used in the next query for more details.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attributeGroup ref="MaxResponsesGroup">
			<xs:annotation>
				<xs:documentation xml:lang="en">Used to specify the maximum number of responses to a request.</xs:documentation>
			</xs:annotation>
		</xs:attributeGroup>
	</xs:attributeGroup>
	<xs:attributeGroup name="RPH_PrefGroup">
		<xs:annotation>
			<xs:documentation xml:lang="en">Indicates a preference for an item that is referred to using a Reference Place Holder (RPH). Often an object is stored in a collection of similar objects, and a preference for use of one these objects is to be made known. This complex type can be used to specify the preference level, and to provide the indicator to the object in the collection.</xs:documentation>
		</xs:annotation>
		<xs:attributeGroup ref="PreferLevelGroup">
			<xs:annotation>
				<xs:documentation xml:lang="en">Used to indicate a level of preference for an item associated with the reference place holder.</xs:documentation>
			</xs:annotation>
		</xs:attributeGroup>
		<xs:attribute name="RPH" type="RPH_Type" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The unique reference for an object within this message.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:attributeGroup>
	<xs:attributeGroup name="SeatLocationGroup">
		<xs:annotation>
			<xs:documentation>Attributes to describe the seat deck, row and the seat number within the row.</xs:documentation>
		</xs:annotation>
		<xs:attribute name="DeckLevel" type="StringLength1to32" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The level of the deck, e.g. "Upper."</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="DeckNumber" type="Numeric1to99" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Deck associated to seat row number.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="RowNumber" type="Numeric1to999" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Sequence number of a row.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="SeatInRow" type="AlphaLength1" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Seat number within a row.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="RowSequenceNumber" type="xs:integer" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The absolute sequence number of real or virtual rows on a plane that enables the assignment of seats that are not in a physical row, e.g. jump seats.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="SeatSequenceNumber" type="xs:integer" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The absolute sequence of seats within a row that enables the assignment of seats outside the physical sequence.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:attributeGroup>
	<xs:attributeGroup name="SeatRequestAttributes">
		<xs:annotation>
			<xs:documentation xml:lang="en">Attributes for seat request. Note: you can choose a specific seat or just a general preference.</xs:documentation>
		</xs:annotation>
		<xs:attribute name="SeatNumber" type="xs:string" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Used to provide the seat number.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="SeatPreference" type="ListOfOTA_CodeType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Refer to OpenTravel Code List Seat Preference (STP).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attributeGroup ref="SmokingIndicatorGroup">
			<xs:annotation>
				<xs:documentation xml:lang="en">Indicates whether or not smoking is allowed.</xs:documentation>
			</xs:annotation>
		</xs:attributeGroup>
		<xs:attribute name="DeckLevel" type="StringLength1to32" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The level of the deck, e.g. "Upper."</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="RowNumber" type="Numeric1to999" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Sequence number of a row.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="SeatInRow" type="AlphaLength1" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Seat number within a row.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:attributeGroup>
	<xs:attributeGroup name="ShareAllGroup">
		<xs:annotation>
			<xs:documentation xml:lang="en">Used to indicate whether information can be shared.</xs:documentation>
		</xs:annotation>
		<xs:attribute name="ShareAllSynchInd" type="YesNoType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Permission for sharing all data in profile for synchronization of profiles held by other travel service providers.</xs:documentation>
				<xs:documentation xml:lang="en">
					<LegacyDefaultValue>No</LegacyDefaultValue>
				</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="ShareAllMarketInd" type="YesNoType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Permission for sharing all data in profile for marketing purposes. A yes value indicates that the customer has chosen to opt-in to marketing communication. This is used in combination with the ShareAllOptOutInd and only one of these attributes should have a value of yes. </xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="ShareAllOptOutInd" type="YesNoType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">When yes, a customer has explicitly opted out of marketing communication. This is used in combination with the ShareAllMarketInd and only one of these attributes should have a value of yes. </xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:attributeGroup>
	<xs:attributeGroup name="SignupDateGroup">
		<xs:annotation>
			<xs:documentation xml:lang="en">Provides the date of registration for a loyalty program.</xs:documentation>
		</xs:annotation>
		<xs:attribute name="SignupDate" type="xs:date" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Indicates when the member signed up for the loyalty program.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:attributeGroup>
	<xs:attributeGroup name="SingleVendorIndGroup">
		<xs:annotation>
			<xs:documentation xml:lang="en">Indicates the alliance status of a program.</xs:documentation>
		</xs:annotation>
		<xs:attribute name="SingleVendorInd" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Indicates if program is affiliated with a group of related offers accumulating credits.</xs:documentation>
			</xs:annotation>
			<xs:simpleType>
				<xs:restriction base="xs:NMTOKEN">
					<xs:enumeration value="SingleVndr">
						<xs:annotation>
							<xs:documentation xml:lang="en">Indicates the program is not part of an alliance.</xs:documentation>
						</xs:annotation>
					</xs:enumeration>
					<xs:enumeration value="Alliance">
						<xs:annotation>
							<xs:documentation xml:lang="en">Indicates the program is part of an alliance.</xs:documentation>
						</xs:annotation>
					</xs:enumeration>
				</xs:restriction>
			</xs:simpleType>
		</xs:attribute>
	</xs:attributeGroup>
	<xs:attributeGroup name="SmokingIndicatorGroup">
		<xs:annotation>
			<xs:documentation xml:lang="en">Identifies a position with regard to the smoking of cigarettes, either Allowed or NotAllowed. This may be of use when expressing a preference (I prefer a room that allows smoking) or when stating the attributes of an item (smoking in this rental car is not allowed).</xs:documentation>
		</xs:annotation>
		<xs:attribute name="SmokingAllowed" type="xs:boolean" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Indicates smoking is allowed when true and is not allowed when false.</xs:documentation>
				<xs:documentation xml:lang="en">
					<LegacyDefaultValue>false</LegacyDefaultValue>
				</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:attributeGroup>
	<xs:attributeGroup name="TelephoneAttributesGroup">
		<xs:annotation>
			<xs:documentation xml:lang="en">Provides telephone information details.</xs:documentation>
		</xs:annotation>
		<xs:attribute name="PhoneLocationType" type="OTA_CodeType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Describes the location of the phone, such as Home, Office, Property Reservation Office, etc. Refer to OpenTravel Code List Phone Location Type (PLT).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="PhoneTechType" type="OTA_CodeType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Indicates type of technology associated with this telephone number, such as Voice, Data, Fax, Pager, Mobile, TTY, etc. Refer to OpenTravel Code List Phone Technology Type (PTT).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="PhoneUseType" type="OTA_CodeType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Describes the type of telephone number, in the context of its general use (e.g. Home, Business, Emergency Contact, Travel Arranger, Day, Evening). Refer to OpenTravel Code List Phone Use Type (PUT).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="CountryAccessCode" type="NumericStringLength1to3" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Code assigned by telecommunications authorities for international country access identifier.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="AreaCityCode" type="NumericStringLength1to8" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Code assigned for telephones in a specific region, city, or area.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="PhoneNumber" type="StringLength1to32" use="required">
			<xs:annotation>
				<xs:documentation xml:lang="en">Telephone number assigned to a single location.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="Extension" type="NumericStringLength1to5" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Extension to reach a specific party at the phone number.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="PIN" type="StringLength1to8" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Additional codes used for pager or telephone access rights.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="Remark" type="StringLength1to128" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">A remark associated with the telephone number.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:attributeGroup>
	<xs:attributeGroup name="TelephoneGroup">
		<xs:annotation>
			<xs:documentation xml:lang="en">Detailed telephone information.</xs:documentation>
		</xs:annotation>
		<xs:attributeGroup ref="PrivacyGroup">
			<xs:annotation>
				<xs:documentation xml:lang="en">Allows for control of the sharing of telephone information between parties.</xs:documentation>
			</xs:annotation>
		</xs:attributeGroup>
		<xs:attributeGroup ref="TelephoneAttributesGroup">
			<xs:annotation>
				<xs:documentation xml:lang="en">Provides telephone information details.</xs:documentation>
			</xs:annotation>
		</xs:attributeGroup>
		<xs:attributeGroup ref="FormattedInd">
			<xs:annotation>
				<xs:documentation xml:lang="en">Identifies if the associated data is formatted into its individual pieces, or exists as a single entity.</xs:documentation>
			</xs:annotation>
		</xs:attributeGroup>
	</xs:attributeGroup>
	<xs:attributeGroup name="TelephoneInfoGroup">
		<xs:annotation>
			<xs:documentation xml:lang="en">Information about a telephone number, including the actual number and its usage.</xs:documentation>
		</xs:annotation>
		<xs:attributeGroup ref="TelephoneGroup">
			<xs:annotation>
				<xs:documentation xml:lang="en">Detailed telephone information.</xs:documentation>
			</xs:annotation>
		</xs:attributeGroup>
		<xs:attributeGroup ref="DefaultIndGroup">
			<xs:annotation>
				<xs:documentation xml:lang="en">Indicates that the receiving system should assume the default value if the user specifies no overriding value or action.</xs:documentation>
			</xs:annotation>
		</xs:attributeGroup>
		<xs:attribute name="RPH" type="RPH_Type" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Used elsewhere in the message to reference a specific telephone number (including faxes).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:attributeGroup>
	<xs:attributeGroup name="TimeWindowGroup">
		<xs:annotation>
			<xs:documentation xml:lang="en">Total time span covered by this availability request (from the earliest arrival to the latest departure).</xs:documentation>
		</xs:annotation>
		<xs:attribute name="EarliestDate" type="DateOrDateTimeType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The earliest ending date/time for the availability requested, expressed in dateTime format as prescribed by ISO 8601.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="LatestDate" type="DateOrDateTimeType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The latest ending date/time for the availability requested, expressed in dateTime format as prescribed by ISO 8601.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="DOW" type="DayOfWeekType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The Day of Week of the starting date for the availability requested. Enumerated values of StartDOW are the seven days of the week: Monday, Tuesday, Wednesday, Thursday, Friday, Saturday, or Sunday.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:attributeGroup>
	<xs:attributeGroup name="TravelDateTimeAttributesGroup">
		<xs:annotation>
			<xs:documentation xml:lang="en">Provides times related to a travel segment.</xs:documentation>
		</xs:annotation>
		<xs:attribute name="DayofWeek" type="DayOfWeekType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The day of week of travel segment.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="CheckInTime" type="xs:dateTime" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The check in time and date of travel segment.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="DepartureDateTime" type="xs:dateTime" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The departure time and date of the travel segment.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="ArrivalDateTime" type="xs:dateTime" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The arrival time and date of the travel segment.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:attributeGroup>
	<xs:attributeGroup name="TravelerCountGroup">
		<xs:annotation>
			<xs:documentation xml:lang="en">Defines information on the number of travelers of a specific type (e.g.  a driver type can be either one of a defined set, Adult, YoungDriver, YoungerDriver, or it may be a code that is acceptable to both Trading Partners).</xs:documentation>
		</xs:annotation>
		<xs:attribute name="Age" type="Numeric0to999" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">This is used to specify age in years.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="Code" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Refer to OpenTravel Code List Age Qualifying Code (AQC), Rail Passenger Type Code (PXC), or use StringLength1to8 with CodeContext to use a non-OpenTravel Code.</xs:documentation>
			</xs:annotation>
			<xs:simpleType>
				<xs:union memberTypes="StringLength1to8 OTA_CodeType"/>
			</xs:simpleType>
		</xs:attribute>
		<xs:attribute name="CodeContext" type="StringLength1to32" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Identifies the source authority for the code.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="URI" type="xs:anyURI" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Identifies the location of the code table.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attributeGroup ref="QuantityGroup">
			<xs:annotation>
				<xs:documentation xml:lang="en">Specifies the number of travelers.</xs:documentation>
			</xs:annotation>
		</xs:attributeGroup>
	</xs:attributeGroup>
	<xs:attributeGroup name="TripInformationGroup">
		<xs:annotation>
			<xs:documentation xml:lang="en">Information about a traveler participating in a rebate program.</xs:documentation>
		</xs:annotation>
		<xs:attribute name="TripPurpose" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The purpose of the trip. If the customer has a default trip purpose this should be pre-populated with that value and the @TripPurposeRequiredInd should be set to FALSE.</xs:documentation>
			</xs:annotation>
			<xs:simpleType>
				<xs:restriction base="xs:NMTOKEN">
					<xs:enumeration value="Business">
						<xs:annotation>
							<xs:documentation xml:lang="en">The trip purpose is for business only.</xs:documentation>
						</xs:annotation>
					</xs:enumeration>
					<xs:enumeration value="BusinessAndPleasure">
						<xs:annotation>
							<xs:documentation xml:lang="en">The trip purpose is for a combination of business and pleasure.</xs:documentation>
						</xs:annotation>
					</xs:enumeration>
					<xs:enumeration value="Pleasure">
						<xs:annotation>
							<xs:documentation xml:lang="en">The trip purpose is for pleasure only.</xs:documentation>
						</xs:annotation>
					</xs:enumeration>
					<xs:enumeration value="Unknown">
						<xs:annotation>
							<xs:documentation xml:lang="en">The trip purpose is unknown.</xs:documentation>
						</xs:annotation>
					</xs:enumeration>
				</xs:restriction>
			</xs:simpleType>
		</xs:attribute>
		<xs:attribute name="TripPurposeRequiredInd" type="xs:boolean" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">When true, the customer does not have a default trip purpose selected and they need to be prompted for the trip purpose.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:attributeGroup>
	<xs:attributeGroup name="UniqueID_Group">
		<xs:annotation>
			<xs:documentation xml:lang="en">Provides unique identification information.</xs:documentation>
		</xs:annotation>
		<xs:attribute name="URL" type="xs:anyURI" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">URL that identifies the location associated with the record identified by the UniqueID.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="Type" type="OTA_CodeType" use="required">
			<xs:annotation>
				<xs:documentation xml:lang="en">A reference to the type of object defined by the UniqueID element. Refer to OpenTravel Code List Unique ID Type (UIT).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="Instance" type="StringLength1to32" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The identification of a record as it exists at a point in time. An instance is used in update messages where the sender must assure the server that the update sent refers to the most recent modification level of the object being updated.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attributeGroup ref="ID_Group">
			<xs:annotation>
				<xs:documentation xml:lang="en">Used to provide a required unique identifier.</xs:documentation>
			</xs:annotation>
		</xs:attributeGroup>
		<xs:attribute name="ID_Context" type="StringLength1to32" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Used to identify the source of the identifier (e.g., IATA, ABTA).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:attributeGroup>
	<xs:attributeGroup name="UnitsOfMeasureGroup">
		<xs:annotation>
			<xs:documentation xml:lang="en">Provides measurement information.</xs:documentation>
		</xs:annotation>
		<xs:attribute name="UnitOfMeasureQuantity" type="xs:decimal" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">This is the numeric value associated with the measurement.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="UnitOfMeasure" type="StringLength1to16" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">This is the standard unit of measure name (e.g., it could be generic such as metric or imperial or specific such as inches, feet, yards, miles, millimeters, centimeters, meters, kilometers- according to usage).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="UnitOfMeasureCode" type="OTA_CodeType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The unit of measure in a code format (e.g., inches, pixels, centimeters). Refer to OpenTravel Code List Unit of Measure Code (UOM).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:attributeGroup>
	<xs:attributeGroup name="VoucherGroup">
		<xs:annotation>
			<xs:documentation xml:lang="en">A form of payment using a voucher or coupon.</xs:documentation>
		</xs:annotation>
		<xs:attributeGroup ref="EffectiveExpireOptionalDateGroup">
			<xs:annotation>
				<xs:documentation xml:lang="en">The date when a voucher becomes valid for use, if applicable, and the the date when a voucher or series of coupons expires and is no longer valid.</xs:documentation>
			</xs:annotation>
		</xs:attributeGroup>
		<xs:attribute name="SeriesCode" type="StringLength1to32" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Identification of a series of coupons or vouchers identified by serial number(s).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:attributeGroup>
	<xs:complexType name="AcceptablePaymentCardsInfoType">
		<xs:annotation>
			<xs:documentation xml:lang="en">This complex type identifies payment cards that are acceptable for a specific form of payment, along with the ability to provide free text information regarding payment cards.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="AcceptablePaymentCards" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">A collection of payment cards that are acceptable as a form of payment.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="AcceptablePaymentCard" maxOccurs="99">
							<xs:annotation>
								<xs:documentation xml:lang="en">Specific information of one payment card that is acceptable as a form of payment.</xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:attributeGroup ref="AcceptablePaymentCardGroup">
									<xs:annotation>
										<xs:documentation xml:lang="en">Provides the information needed to describe a type of payment card that is acceptable as a form of payment. A usage fee (amount or percentage) may also be stated for this particular card type.</xs:documentation>
									</xs:annotation>
								</xs:attributeGroup>
							</xs:complexType>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="Info" type="FormattedTextType" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">General information regarding the use of payment cards.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="AcceptedPaymentsType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Used to define the types of payments accepted.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="AcceptedPayment" type="PaymentFormType" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation xml:lang="en">An acceptable form of payment.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="AddressInfoType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Information about an address that identifies a location for a specific purposes.</xs:documentation>
		</xs:annotation>
		<xs:complexContent>
			<xs:extension base="AddressType">
				<xs:attributeGroup ref="DefaultIndGroup">
					<xs:annotation>
						<xs:documentation xml:lang="en">Indicates whether or not this is the default address.</xs:documentation>
					</xs:annotation>
				</xs:attributeGroup>
				<xs:attribute name="UseType" type="OTA_CodeType" use="optional">
					<xs:annotation>
						<xs:documentation xml:lang="en">Describes the use of the address (e.g. mailing, delivery, billing, etc.). Refer to OpenTravel Code List Address Use Type (AUT).</xs:documentation>
					</xs:annotation>
				</xs:attribute>
				<xs:attribute name="RPH" type="RPH_Type" use="optional">
					<xs:annotation>
						<xs:documentation xml:lang="en">Used elsewhere in the message to reference this specific address.</xs:documentation>
					</xs:annotation>
				</xs:attribute>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:complexType name="AddressType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Provides address information.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="StreetNmbr" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">May contain the street number and optionally the street name.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:simpleContent>
						<xs:extension base="StreetNmbrType">
							<xs:attribute name="StreetNmbrSuffix" type="StringLength1to8" use="optional">
								<xs:annotation>
									<xs:documentation xml:lang="en">Usually a letter right after the street number (A in 66-A, B in 123-B etc).</xs:documentation>
								</xs:annotation>
							</xs:attribute>
							<xs:attribute name="StreetDirection" type="StringLength1to8" use="optional">
								<xs:annotation>
									<xs:documentation xml:lang="en">Street direction of an address (e.g., N, E, S, NW, SW).</xs:documentation>
								</xs:annotation>
							</xs:attribute>
							<xs:attribute name="RuralRouteNmbr" type="NumericStringLength1to5" use="optional">
								<xs:annotation>
									<xs:documentation xml:lang="en">Numerical equivalent of a rural township as defined within a given area (e.g., 12, 99).</xs:documentation>
								</xs:annotation>
							</xs:attribute>
						</xs:extension>
					</xs:simpleContent>
				</xs:complexType>
			</xs:element>
			<xs:element name="BldgRoom" minOccurs="0" maxOccurs="2">
				<xs:annotation>
					<xs:documentation xml:lang="en">Building name, room, apartment, or suite number.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:simpleContent>
						<xs:extension base="StringLength0to64">
							<xs:attribute name="BldgNameIndicator" type="xs:boolean" use="optional">
								<xs:annotation>
									<xs:documentation xml:lang="en">When true, the information is a building name.</xs:documentation>
								</xs:annotation>
							</xs:attribute>
						</xs:extension>
					</xs:simpleContent>
				</xs:complexType>
			</xs:element>
			<xs:element name="AddressLine" type="StringLength1to255" minOccurs="0" maxOccurs="5">
				<xs:annotation>
					<xs:documentation xml:lang="en">When the address is unformatted (FormattedInd="false") these lines will contain free form address details. When the address is formatted and street number and street name must be sent independently, the street number will be sent using StreetNmbr, and the street name will be sent in the first AddressLine occurrence.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="CityName" type="StringLength1to64" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">City (e.g., Dublin), town, or postal station (i.e., a postal service territory, often used in a military address).</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="PostalCode" type="StringLength1to16" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Post Office Code number.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="County" type="StringLength1to32" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">County or Region Name (e.g., Fairfax).</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="StateProv" type="StateProvType" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">State or Province name (e.g., Texas).</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="CountryName" type="CountryNameType" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Country name (e.g., Ireland).</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
		<xs:attributeGroup ref="FormattedInd">
			<xs:annotation>
				<xs:documentation xml:lang="en">Specifies if the associated data is formatted or not. When true, then it is formatted; when false, then not formatted.</xs:documentation>
			</xs:annotation>
		</xs:attributeGroup>
		<xs:attributeGroup ref="PrivacyGroup">
			<xs:annotation>
				<xs:documentation xml:lang="en">Allows for control of the sharing of address information between parties.</xs:documentation>
			</xs:annotation>
		</xs:attributeGroup>
		<xs:attribute name="Type" type="OTA_CodeType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Defines the type of address (e.g. home, business, other). Refer to OpenTravel Code List Communication Location Type (CLT).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="Remark" type="StringLength1to128" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">A remark associated with this address.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:complexType>
	<xs:complexType name="BankAcctType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Customer bank accounts for payments, either for paper checks or electronic funds transfer.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="BankAcctName" type="StringLength1to64" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The name the account is held under.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
		<xs:attributeGroup ref="PrivacyGroup">
			<xs:annotation>
				<xs:documentation xml:lang="en">Allows for control of the sharing of bank account information between parties.</xs:documentation>
			</xs:annotation>
		</xs:attributeGroup>
		<xs:attribute name="BankID" type="StringLength1to64" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Code assigned by authorities to financial institutions; sometimes called bank routing number.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="AcctType" type="StringLength1to32" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Describes the bank account used for financing travel (e.g., checking, savings, investment).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="BankAcctNumber" type="StringLength1to32" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Identifier for the account assigned by the bank.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="ChecksAcceptedInd" type="xs:boolean" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">If true, checks are accepted. If false, checks are not accepted.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="CheckNumber" type="StringLength1to32" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The number of the check used for payment.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:complexType>
	<xs:complexType name="BlackoutDateType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Provides blackout date information.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="BlackoutDate" type="DateTimeSpanType" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation xml:lang="en">Indicates black-out dates for the travel product/service.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="CancelInfoRQType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Identifies the common, or core, information associated with the request for cancellation of a reservation or other type of record.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="UniqueID" type="UniqueID_Type" maxOccurs="2">
				<xs:annotation>
					<xs:documentation xml:lang="en">Sending own UniqueID and partner UniqueID - the receiving system needs to know which one - UniqueID acts as a reference for each system.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="PersonName" type="PersonNameType" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The person's name in a reservation.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="CancelType" type="TransactionActionType" use="required">
			<xs:annotation>
				<xs:documentation xml:lang="en">Used to specify if this is to initiate a cancellation or to commit the cancellation.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:complexType>
	<xs:complexType name="CancelInfoRSType">
		<xs:annotation>
			<xs:documentation xml:lang="en">May contain rules associated with canceling a reservation as well as the supplier's cancellation number.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="CancelRules" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">A collection of cancellation rules.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="CancelRule" type="CancelRuleType" maxOccurs="unbounded">
							<xs:annotation>
								<xs:documentation xml:lang="en">Details of a cancellation rule.</xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="UniqueID" type="UniqueID_Type" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Contains the supplier's cancellation number.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="CancelRuleType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Provides the cancellation amount due according to the time before the booking date that the cancellation occurs. The amount may be either an amount or a percentage (e.g. 50% within 30 days or $100 outside 30 days).</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="PaymentCard" type="PaymentCardType" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The card to be charged with the cancellation fee.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="CancelByDate" type="DateOrDateTimeType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The date by which a cancellation must be made in order to avoid this cancellation penalty.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attributeGroup ref="CurrencyAmountGroup">
			<xs:annotation>
				<xs:documentation xml:lang="en">Provides the amount of the cancellation charge.</xs:documentation>
			</xs:annotation>
		</xs:attributeGroup>
		<xs:attribute name="Percent" type="Percentage" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The percentage to be applied for a cancellation.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="Type" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Defines how the cancellation penalty will be applied.</xs:documentation>
			</xs:annotation>
			<xs:simpleType>
				<xs:restriction base="xs:NMTOKEN">
					<xs:enumeration value="Refund"/>
					<xs:enumeration value="Charge"/>
					<xs:enumeration value="Forfeiture"/>
				</xs:restriction>
			</xs:simpleType>
		</xs:attribute>
	</xs:complexType>
	<xs:complexType name="CommentType">
		<xs:annotation>
			<xs:documentation xml:lang="en">A collection of comments.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="Comment" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation xml:lang="en">Comment details.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:complexContent>
						<xs:extension base="ParagraphType">
							<xs:attribute name="CommentOriginatorCode" type="StringLength1to16" use="optional">
								<xs:annotation>
									<xs:documentation xml:lang="en">Unique identifier for the system which created the comment.</xs:documentation>
								</xs:annotation>
							</xs:attribute>
							<xs:attribute name="GuestViewable" type="xs:boolean" use="optional">
								<xs:annotation>
									<xs:documentation xml:lang="en">When true, the comment may be shown to the consumer. When false, the comment may not be shown to the consumer.</xs:documentation>
								</xs:annotation>
							</xs:attribute>
						</xs:extension>
					</xs:complexContent>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="CommissionType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Contains details pertaining to commissions.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="UniqueID" type="UniqueID_Type" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Identifies the recipient of the commission.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="CommissionableAmount" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The amount on which commission is calculated.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:attributeGroup ref="CurrencyAmountGroup">
						<xs:annotation>
							<xs:documentation xml:lang="en">Provides the currency amount on which the commission is applied.</xs:documentation>
						</xs:annotation>
					</xs:attributeGroup>
					<xs:attribute name="TaxInclusiveIndicator" type="xs:boolean" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">When true, indicates that the commission is calculated using the rate including tax. When false, indicates that the commission is calculated using the net rate.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
				</xs:complexType>
			</xs:element>
			<xs:element name="PrepaidAmount" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The amount of commission paid to the agency prior to the service being rendered.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:attributeGroup ref="CurrencyAmountGroup">
						<xs:annotation>
							<xs:documentation xml:lang="en">Provides the amount of commission paid to the agency prior to the service being rendered.</xs:documentation>
						</xs:annotation>
					</xs:attributeGroup>
				</xs:complexType>
			</xs:element>
			<xs:element name="FlatCommission" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">A fixed commission amount.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:attributeGroup ref="CurrencyAmountGroup">
						<xs:annotation>
							<xs:documentation xml:lang="en">A fixed commission amount.</xs:documentation>
						</xs:annotation>
					</xs:attributeGroup>
				</xs:complexType>
			</xs:element>
			<xs:element name="CommissionPayableAmount" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The amount of commission to be paid.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:attributeGroup ref="CurrencyAmountGroup">
						<xs:annotation>
							<xs:documentation xml:lang="en">The amount of commission to be paid.</xs:documentation>
						</xs:annotation>
					</xs:attributeGroup>
				</xs:complexType>
			</xs:element>
			<xs:element name="Comment" type="ParagraphType" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Text related to the commission.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="StatusType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Indicates the status of the commission payment itself (e.g. no-show indicates that a different commission may be applied if the reservation is not fulfilled).</xs:documentation>
			</xs:annotation>
			<xs:simpleType>
				<xs:restriction base="xs:NMTOKEN">
					<xs:enumeration value="Full">
						<xs:annotation>
							<xs:documentation xml:lang="en">Indicates full commission.</xs:documentation>
						</xs:annotation>
					</xs:enumeration>
					<xs:enumeration value="Partial">
						<xs:annotation>
							<xs:documentation xml:lang="en">Indicates partial commission.</xs:documentation>
						</xs:annotation>
					</xs:enumeration>
					<xs:enumeration value="Non-paying">
						<xs:annotation>
							<xs:documentation xml:lang="en">Indicates no commission.</xs:documentation>
						</xs:annotation>
					</xs:enumeration>
					<xs:enumeration value="No-show">
						<xs:annotation>
							<xs:documentation xml:lang="en">Indicates customer did not use the reserved product or service and did not cancel. This "no show" may impact commission.</xs:documentation>
						</xs:annotation>
					</xs:enumeration>
					<xs:enumeration value="Adjustment">
						<xs:annotation>
							<xs:documentation xml:lang="en">Indicates the commission is being adjusted.</xs:documentation>
						</xs:annotation>
					</xs:enumeration>
					<xs:enumeration value="Commissionable">
						<xs:annotation>
							<xs:documentation xml:lang="en">Indicates the requested rate is commissionable.</xs:documentation>
						</xs:annotation>
					</xs:enumeration>
				</xs:restriction>
			</xs:simpleType>
		</xs:attribute>
		<xs:attribute name="Percent" type="Percentage" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The percent applied to the commissionable amount to determine the commission payable amount.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attributeGroup ref="CurrencyCodeGroup">
			<xs:annotation>
				<xs:documentation xml:lang="en">Indicates the currency to be applied to the amounts located in the child elements.</xs:documentation>
			</xs:annotation>
		</xs:attributeGroup>
		<xs:attribute name="ReasonCode" type="StringLength1to8" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Identifies the reason why a commission is not paid or not paid in full.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="BillToID" type="StringLength1to8" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Identifies who should be billed for the commission amount.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="Frequency" type="OTA_CodeType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">This is the frequency at which the commission is applied (e.g. per stay, daily). Refer to OpenTravel Code List Charge Type (CHG).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="MaxCommissionUnitApplies" type="xs:positiveInteger" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Maximum number of units for which the commission will be applied. This may be used in conjunction with the frequency attribute.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="CapAmount" type="Money" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The highest monetary value that may be paid for the commission.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:complexType>
	<xs:complexType name="CompanyNameType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Identifies a company by name.</xs:documentation>
		</xs:annotation>
		<xs:simpleContent>
			<xs:extension base="StringLength0to128">
				<xs:attributeGroup ref="CompanyID_AttributesGroup">
					<xs:annotation>
						<xs:documentation xml:lang="en">Provides detailed information on a company.</xs:documentation>
					</xs:annotation>
				</xs:attributeGroup>
				<xs:attribute name="Division" type="StringLength1to32" use="optional">
					<xs:annotation>
						<xs:documentation xml:lang="en">The division name or ID with which the contact is associated.</xs:documentation>
					</xs:annotation>
				</xs:attribute>
				<xs:attribute name="Department" type="StringLength1to32" use="optional">
					<xs:annotation>
						<xs:documentation xml:lang="en">The department name or ID with which the contact is associated.</xs:documentation>
					</xs:annotation>
				</xs:attribute>
			</xs:extension>
		</xs:simpleContent>
	</xs:complexType>
	<xs:complexType name="ConnectionType">
		<xs:annotation>
			<xs:documentation xml:lang="en">To specify connection locations, preference level for each, min connection time, and whether location is specified for stopping or changing.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="ConnectionLocation" maxOccurs="9">
				<xs:annotation>
					<xs:documentation xml:lang="en">Specifies a connection location, preference level, min connection time, and whether the location is allowed for stopping or changing.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:simpleContent>
						<xs:extension base="LocationType">
							<xs:attribute name="Inclusive" type="xs:boolean" use="optional">
								<xs:annotation>
									<xs:documentation xml:lang="en">When true, indicates the location is acceptable for a connection. When false  the location should not be included for a connection.</xs:documentation>
									<xs:documentation xml:lang="en">
										<LegacyDefaultValue>true</LegacyDefaultValue>
									</xs:documentation>
								</xs:annotation>
							</xs:attribute>
							<xs:attributeGroup ref="PreferLevelGroup">
								<xs:annotation>
									<xs:documentation xml:lang="en">The preference level for the connection point - only, unacceptable, preferred.</xs:documentation>
								</xs:annotation>
							</xs:attributeGroup>
							<xs:attribute name="MinChangeTime" type="xs:nonNegativeInteger" use="optional">
								<xs:annotation>
									<xs:documentation xml:lang="en">Number of minutes between connections.</xs:documentation>
								</xs:annotation>
							</xs:attribute>
							<xs:attribute name="ConnectionInfo" use="optional">
								<xs:annotation>
									<xs:documentation xml:lang="en">Specifies whether a connection is for a stopover or a change of flights.</xs:documentation>
								</xs:annotation>
								<xs:simpleType>
									<xs:restriction base="xs:string">
										<xs:enumeration value="Via"/>
										<xs:enumeration value="Stop"/>
										<xs:enumeration value="Change"/>
									</xs:restriction>
								</xs:simpleType>
							</xs:attribute>
							<xs:attribute name="MultiAirportCityInd" type="xs:boolean" use="optional">
								<xs:annotation>
									<xs:documentation xml:lang="en">If true, other airports within this city may be considered (e.g., EWR, JFK when origin location is LGA.)</xs:documentation>
								</xs:annotation>
							</xs:attribute>
							<xs:attribute name="ConnectType" use="optional">
								<xs:annotation>
									<xs:documentation xml:lang="en">The type of connection desired, Online, Offline, Interline. Online refers to host airline connections. Offline refers to non-host airline connections. Interline refers to a combination of host airline and non-host airline connections.</xs:documentation>
								</xs:annotation>
								<xs:simpleType>
									<xs:restriction base="xs:NMTOKEN">
										<xs:enumeration value="Online">
											<xs:annotation>
												<xs:documentation xml:lang="en">Own host airlines only.</xs:documentation>
											</xs:annotation>
										</xs:enumeration>
										<xs:enumeration value="Offline">
											<xs:annotation>
												<xs:documentation xml:lang="en">Non host airlines only.</xs:documentation>
											</xs:annotation>
										</xs:enumeration>
										<xs:enumeration value="Interline">
											<xs:annotation>
												<xs:documentation xml:lang="en">Own and other airlines.</xs:documentation>
											</xs:annotation>
										</xs:enumeration>
									</xs:restriction>
								</xs:simpleType>
							</xs:attribute>
						</xs:extension>
					</xs:simpleContent>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="ContactPersonType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Name of an individual and appropriate contact information.  May be contact information for the customer or someone affiliated with the customer.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="PersonName" type="PersonNameType" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">This provides name information for a person.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Telephone" minOccurs="0" maxOccurs="5">
				<xs:annotation>
					<xs:documentation xml:lang="en">Information about a telephone number, including the actual number and its usage.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:attributeGroup ref="TelephoneInfoGroup">
						<xs:annotation>
							<xs:documentation xml:lang="en">Information about a contact's telephone number, including the actual number and its usage.</xs:documentation>
						</xs:annotation>
					</xs:attributeGroup>
				</xs:complexType>
			</xs:element>
			<xs:element name="Address" type="AddressInfoType" minOccurs="0" maxOccurs="5">
				<xs:annotation>
					<xs:documentation xml:lang="en">Information about an address that identifies a location for a specific purposes.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Email" type="EmailType" minOccurs="0" maxOccurs="5">
				<xs:annotation>
					<xs:documentation xml:lang="en">Electronic email addresses, in IETF specified format.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="URL" type="URL_Type" minOccurs="0" maxOccurs="5">
				<xs:annotation>
					<xs:documentation xml:lang="en">Web site address, in IETF specified format.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="CompanyName" type="CompanyNameType" minOccurs="0" maxOccurs="3">
				<xs:annotation>
					<xs:documentation xml:lang="en">Identifies a company by name.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="EmployeeInfo" type="EmployeeInfoType" minOccurs="0" maxOccurs="3">
				<xs:annotation>
					<xs:documentation xml:lang="en">Employment identification; using an employee ID number, title, level within the company, and an indication of their status, i.e.: active, retired, on leave, or terminated from employment. Additional information about an employee can be entered into the element, including the name of the employee, if it differs from the person name identified as a customer or contact.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
		<xs:attributeGroup ref="PrivacyGroup">
			<xs:annotation>
				<xs:documentation xml:lang="en">Allows for control of the sharing of data between parties.</xs:documentation>
			</xs:annotation>
		</xs:attributeGroup>
		<xs:attributeGroup ref="DefaultIndGroup">
			<xs:annotation>
				<xs:documentation xml:lang="en">Indicates that the receiving system should assume the default value if the user specifies no overriding value or action.</xs:documentation>
			</xs:annotation>
		</xs:attributeGroup>
		<xs:attribute name="ContactType" type="StringLength1to32" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Type of contact in the context of use for the travel experience; such as permanent, temporary, affiliation, travel arranger, etc.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="Relation" type="StringLength1to32" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Indicates the type of relationship with the person or company in the profile, such as Spouse, Child, Family, Business Associate, Interest Group, Medical, Security,Other, etc.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="EmergencyFlag" type="xs:boolean" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Indicates if this contact should be used in the case of an emergency.</xs:documentation>
				<xs:documentation xml:lang="en">
					<LegacyDefaultValue>false</LegacyDefaultValue>
				</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="RPH" type="RPH_Type" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Provides a unique reference to this contact person.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="CommunicationMethodCode" type="OTA_CodeType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Provides the preferred method of communication. Refer to OpenTravel Code list Distribution Type (DTB).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="DocumentDistribMethodCode" type="OTA_CodeType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Indicates the method of distribution for the booking documentation. Refer to OpenTravel Code list Distribution Type (DTB).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:complexType>
	<xs:complexType name="CountryNameType">
		<xs:annotation>
			<xs:documentation xml:lang="en">The name or code of a country (e.g. as used in an address or to specify citizenship of a traveller).</xs:documentation>
		</xs:annotation>
		<xs:simpleContent>
			<xs:extension base="StringLength0to64">
				<xs:attribute name="Code" type="ISO3166" use="optional">
					<xs:annotation>
						<xs:documentation xml:lang="en">ISO 3166 code for a country.</xs:documentation>
					</xs:annotation>
				</xs:attribute>
			</xs:extension>
		</xs:simpleContent>
	</xs:complexType>
	<xs:complexType name="CustomerType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Contains basic data on the customer's identity, location, relationships, finances, memberships, etc.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="PersonName" type="PersonNameType" minOccurs="0" maxOccurs="5">
				<xs:annotation>
					<xs:documentation xml:lang="en">Detailed name information for the customer.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Telephone" minOccurs="0" maxOccurs="99">
				<xs:annotation>
					<xs:documentation xml:lang="en">Information on a telephone number for the customer.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:attributeGroup ref="EffectiveExpireOptionalDateGroup">
						<xs:annotation>
							<xs:documentation xml:lang="en">The first and last dates between which this telephone number is in effect.</xs:documentation>
						</xs:annotation>
					</xs:attributeGroup>
					<xs:attributeGroup ref="TelephoneInfoGroup">
						<xs:annotation>
							<xs:documentation xml:lang="en">Information about a telephone number, including the actual number and its usage.</xs:documentation>
						</xs:annotation>
					</xs:attributeGroup>
					<xs:attribute name="TransferAction" type="TransferActionType" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">Indicates under what conditions the element will be transfered to the booking.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="ParentCompanyRef" type="StringLength1to8" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">A reference to the company from which this element has been inherited.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
				</xs:complexType>
			</xs:element>
			<xs:element name="Email" minOccurs="0" maxOccurs="99">
				<xs:annotation>
					<xs:documentation xml:lang="en">Information on an email address for the customer.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:complexContent>
						<xs:extension base="EmailType">
							<xs:attribute name="TransferAction" type="TransferActionType" use="optional">
								<xs:annotation>
									<xs:documentation xml:lang="en">Indicates under what conditions the element will be transfered to the booking.</xs:documentation>
								</xs:annotation>
							</xs:attribute>
							<xs:attribute name="ParentCompanyRef" type="StringLength1to8" use="optional">
								<xs:annotation>
									<xs:documentation xml:lang="en">A reference to the company from which this element has been inherited.</xs:documentation>
								</xs:annotation>
							</xs:attribute>
						</xs:extension>
					</xs:complexContent>
				</xs:complexType>
			</xs:element>
			<xs:element name="Address" minOccurs="0" maxOccurs="99">
				<xs:annotation>
					<xs:documentation xml:lang="en">Detailed information on an address for the customer.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:complexContent>
						<xs:extension base="AddressInfoType">
							<xs:sequence>
								<xs:element name="CompanyName" type="CompanyNameType" minOccurs="0">
									<xs:annotation>
										<xs:documentation xml:lang="en">Identifies a company.</xs:documentation>
									</xs:annotation>
								</xs:element>
								<xs:element name="AddresseeName" type="PersonNameType" minOccurs="0">
									<xs:annotation>
										<xs:documentation>Name of the person to whom this address relates.</xs:documentation>
									</xs:annotation>
								</xs:element>
							</xs:sequence>
							<xs:attributeGroup ref="EffectiveExpireOptionalDateGroup">
								<xs:annotation>
									<xs:documentation xml:lang="en">The first and last dates between which this address is in effect.</xs:documentation>
								</xs:annotation>
							</xs:attributeGroup>
							<xs:attribute name="ValidationStatus" use="optional">
								<xs:annotation>
									<xs:documentation xml:lang="en">Indicates if the address has been validated or not and if it has been successful or not.</xs:documentation>
								</xs:annotation>
								<xs:simpleType>
									<xs:restriction base="xs:NMTOKEN">
										<xs:enumeration value="SystemValidated">
											<xs:annotation>
												<xs:documentation xml:lang="en">Indicates the address has been validated automatically.</xs:documentation>
											</xs:annotation>
										</xs:enumeration>
										<xs:enumeration value="UserValidated">
											<xs:annotation>
												<xs:documentation xml:lang="en">Indicates the address has been validated by the agent.</xs:documentation>
											</xs:annotation>
										</xs:enumeration>
										<xs:enumeration value="NotChecked">
											<xs:annotation>
												<xs:documentation xml:lang="en">Indicates the address has not been validated.</xs:documentation>
											</xs:annotation>
										</xs:enumeration>
									</xs:restriction>
								</xs:simpleType>
							</xs:attribute>
							<xs:attribute name="TransferAction" type="TransferActionType" use="optional">
								<xs:annotation>
									<xs:documentation xml:lang="en">Indicates under what conditions the element will be transfered to the booking.</xs:documentation>
								</xs:annotation>
							</xs:attribute>
							<xs:attribute name="ParentCompanyRef" type="StringLength1to8" use="optional">
								<xs:annotation>
									<xs:documentation xml:lang="en">A reference to the company from which this element has been inherited.</xs:documentation>
								</xs:annotation>
							</xs:attribute>
						</xs:extension>
					</xs:complexContent>
				</xs:complexType>
			</xs:element>
			<xs:element name="URL" minOccurs="0" maxOccurs="5">
				<xs:annotation>
					<xs:documentation xml:lang="en">Information on a URL for the customer.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:complexContent>
						<xs:extension base="URL_Type">
							<xs:attribute name="TransferAction" type="TransferActionType" use="optional">
								<xs:annotation>
									<xs:documentation xml:lang="en">Indicates under what conditions this element will be transfered to the booking.</xs:documentation>
								</xs:annotation>
							</xs:attribute>
						</xs:extension>
					</xs:complexContent>
				</xs:complexType>
			</xs:element>
			<xs:element name="CitizenCountryName" minOccurs="0" maxOccurs="3">
				<xs:annotation>
					<xs:documentation xml:lang="en">Name of the (self-professed) country that is claimed for citizenship.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:attributeGroup ref="CitizenCountryNameGroup">
						<xs:annotation>
							<xs:documentation xml:lang="en">Name of the (self-professed) country that is claimed for citizenship.</xs:documentation>
						</xs:annotation>
					</xs:attributeGroup>
				</xs:complexType>
			</xs:element>
			<xs:element name="PhysChallName" minOccurs="0" maxOccurs="5">
				<xs:annotation>
					<xs:documentation xml:lang="en">Describes the customer's physical challenge.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:simpleContent>
						<xs:extension base="StringLength0to255">
							<xs:attribute name="PhysChallInd" type="xs:boolean" use="optional">
								<xs:annotation>
									<xs:documentation xml:lang="en">When true, indicates the customer is physically challenged.</xs:documentation>
								</xs:annotation>
							</xs:attribute>
						</xs:extension>
					</xs:simpleContent>
				</xs:complexType>
			</xs:element>
			<xs:element name="PetInfo" type="StringLength1to64" minOccurs="0" maxOccurs="3">
				<xs:annotation>
					<xs:documentation xml:lang="en">Describes the customer's pet.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="PaymentForm" minOccurs="0" maxOccurs="100">
				<xs:annotation>
					<xs:documentation xml:lang="en">Methods of providing funds and guarantees for travel by the customer.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:complexContent>
						<xs:extension base="PaymentFormType">
							<xs:sequence>
								<xs:element name="AssociatedSupplier" minOccurs="0">
									<xs:annotation>
										<xs:documentation>The supplier for whom this is the preferred method of payment.</xs:documentation>
									</xs:annotation>
									<xs:complexType>
										<xs:attributeGroup ref="CompanyID_AttributesGroup">
											<xs:annotation>
												<xs:documentation xml:lang="en">Information about the supplier.</xs:documentation>
											</xs:annotation>
										</xs:attributeGroup>
									</xs:complexType>
								</xs:element>
							</xs:sequence>
							<xs:attribute name="TransferAction" type="TransferActionType" use="optional">
								<xs:annotation>
									<xs:documentation xml:lang="en">Indicates under what conditions the element will be transfered to the booking.</xs:documentation>
								</xs:annotation>
							</xs:attribute>
							<xs:attribute name="DefaultInd" type="xs:boolean" use="optional">
								<xs:annotation>
									<xs:documentation xml:lang="en">When true, indicates the preferred form of payment.</xs:documentation>
								</xs:annotation>
							</xs:attribute>
							<xs:attribute name="ParentCompanyRef" type="StringLength1to8" use="optional">
								<xs:annotation>
									<xs:documentation xml:lang="en">A reference to the company from which this element has been inherited.</xs:documentation>
								</xs:annotation>
							</xs:attribute>
						</xs:extension>
					</xs:complexContent>
				</xs:complexType>
			</xs:element>
			<xs:element name="RelatedTraveler" type="RelatedTravelerType" minOccurs="0" maxOccurs="99">
				<xs:annotation>
					<xs:documentation xml:lang="en">Identifies a traveler associated with the customer.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="ContactPerson" type="ContactPersonType" minOccurs="0" maxOccurs="5">
				<xs:annotation>
					<xs:documentation xml:lang="en">Information on a contact person for the customer.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Document" type="DocumentType" minOccurs="0" maxOccurs="99">
				<xs:annotation>
					<xs:documentation xml:lang="en">Detailed document information for the customer (e.g.,  driver license, passport, visa).</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="CustLoyalty" minOccurs="0" maxOccurs="25">
				<xs:annotation>
					<xs:documentation xml:lang="en">Loyalty program information for the customer.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="MemberPreferences" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">Loyalty program preferences specified by the enrolling member.</xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:sequence>
									<xs:element name="AdditionalReward" minOccurs="0" maxOccurs="5">
										<xs:annotation>
											<xs:documentation xml:lang="en">Additional programs that are honored by the primary loyalty account.</xs:documentation>
										</xs:annotation>
										<xs:complexType>
											<xs:sequence>
												<xs:element name="CompanyName" type="CompanyNameType" minOccurs="0"/>
												<xs:element name="Name" type="PersonNameType" minOccurs="0"/>
											</xs:sequence>
											<xs:attribute name="MemberID" type="StringLength1to32" use="optional"/>
										</xs:complexType>
									</xs:element>
									<xs:element name="Offer" minOccurs="0" maxOccurs="5">
										<xs:annotation>
											<xs:documentation xml:lang="en">Source from which members can receive information.</xs:documentation>
										</xs:annotation>
										<xs:complexType>
											<xs:sequence>
												<xs:element name="Communication" minOccurs="0" maxOccurs="5">
													<xs:annotation>
														<xs:documentation xml:lang="en">Preferred method of offer communication.</xs:documentation>
													</xs:annotation>
													<xs:complexType>
														<xs:attribute name="DistribType" type="OTA_CodeType" use="optional">
															<xs:annotation>
																<xs:documentation xml:lang="en">An enumerated list of method of communication. Refer to OpenTravel Code List Distribution Type (DTB).</xs:documentation>
															</xs:annotation>
														</xs:attribute>
													</xs:complexType>
												</xs:element>
											</xs:sequence>
											<xs:attribute name="Type" use="optional">
												<xs:annotation>
													<xs:documentation xml:lang="en">An enumerated list of offer sources.</xs:documentation>
												</xs:annotation>
												<xs:simpleType>
													<xs:restriction base="xs:string">
														<xs:enumeration value="Partner"/>
														<xs:enumeration value="Loyalty"/>
													</xs:restriction>
												</xs:simpleType>
											</xs:attribute>
										</xs:complexType>
									</xs:element>
								</xs:sequence>
								<xs:attribute name="Awareness" type="StringLength1to32" use="optional">
									<xs:annotation>
										<xs:documentation xml:lang="en">Indicates how person became aware of loyalty program.</xs:documentation>
									</xs:annotation>
								</xs:attribute>
								<xs:attributeGroup ref="PromotionCodeGroup">
									<xs:annotation>
										<xs:documentation xml:lang="en">Loyalty enrollment promotion code.</xs:documentation>
									</xs:annotation>
								</xs:attributeGroup>
								<xs:attribute name="AwardsPreference" use="optional">
									<xs:annotation>
										<xs:documentation xml:lang="en">Method by which awards are allocated.</xs:documentation>
									</xs:annotation>
									<xs:simpleType>
										<xs:restriction base="xs:string">
											<xs:enumeration value="Points"/>
											<xs:enumeration value="Miles"/>
										</xs:restriction>
									</xs:simpleType>
								</xs:attribute>
							</xs:complexType>
						</xs:element>
						<xs:element name="SecurityInfo" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">Information allowing member to securely access account.</xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:sequence>
									<xs:element name="PasswordHint" minOccurs="0" maxOccurs="2">
										<xs:annotation>
											<xs:documentation xml:lang="en">Alternate method to password for account access.</xs:documentation>
										</xs:annotation>
										<xs:complexType>
											<xs:simpleContent>
												<xs:extension base="xs:string">
													<xs:attribute name="Hint" use="optional">
														<xs:simpleType>
															<xs:restriction base="xs:string">
																<xs:enumeration value="Question"/>
																<xs:enumeration value="Answer"/>
															</xs:restriction>
														</xs:simpleType>
													</xs:attribute>
												</xs:extension>
											</xs:simpleContent>
										</xs:complexType>
									</xs:element>
								</xs:sequence>
								<xs:attribute name="Username" type="StringLength1to32"/>
								<xs:attribute name="Password" type="StringLength1to32"/>
							</xs:complexType>
						</xs:element>
						<xs:element name="SubAccountBalance" minOccurs="0" maxOccurs="99">
							<xs:annotation>
								<xs:documentation xml:lang="en">Used to specify a sub-account and its point balance associated with this loyalty account.</xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:attribute name="Type" type="StringLength1to16" use="optional">
									<xs:annotation>
										<xs:documentation xml:lang="en">Describes the type of sub account (e.g. miles, points, vouchers, stays).</xs:documentation>
									</xs:annotation>
								</xs:attribute>
								<xs:attribute name="Balance" type="xs:integer" use="optional">
									<xs:annotation>
										<xs:documentation xml:lang="en">The current balance for this sub account.</xs:documentation>
									</xs:annotation>
								</xs:attribute>
							</xs:complexType>
						</xs:element>
					</xs:sequence>
					<xs:attributeGroup ref="CustomerLoyaltyGroup">
						<xs:annotation>
							<xs:documentation xml:lang="en">Program rewarding frequent use by accumulating credits for services provided by vendors.</xs:documentation>
						</xs:annotation>
					</xs:attributeGroup>
					<xs:attribute name="Remark" type="StringLength1to128" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">A remark associated with the customer's loyalty program.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
				</xs:complexType>
			</xs:element>
			<xs:element name="EmployeeInfo" type="EmployeeInfoType" minOccurs="0" maxOccurs="3">
				<xs:annotation>
					<xs:documentation xml:lang="en">Employment information for the customer.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="EmployerInfo" type="CompanyNameType" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Identifies the customer's employer.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="AdditionalLanguage" minOccurs="0" maxOccurs="5">
				<xs:annotation>
					<xs:documentation>Additional languages spoken by the traveler.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:attribute name="Code" type="xs:language" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">Code for the language spoken by the customer.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
				</xs:complexType>
			</xs:element>
			<xs:element ref="TPA_Extensions" minOccurs="0"/>
		</xs:sequence>
		<xs:attributeGroup ref="GenderGroup">
			<xs:annotation>
				<xs:documentation xml:lang="en">Identifies the gender of the customer.</xs:documentation>
			</xs:annotation>
		</xs:attributeGroup>
		<xs:attribute name="Deceased" type="xs:boolean" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">When true the customer is deceased.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="LockoutType" type="StringLength1to16" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Indicates reason for locking out record, such as Emergency, Incident, etc.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attributeGroup ref="BirthDateGroup">
			<xs:annotation>
				<xs:documentation xml:lang="en">Identifies the birth date of the customer.</xs:documentation>
			</xs:annotation>
		</xs:attributeGroup>
		<xs:attributeGroup ref="CurrencyCodeGroup">
			<xs:annotation>
				<xs:documentation xml:lang="en">Type of funds preferred for reviewing monetary values, in ISO 4217 codes.</xs:documentation>
			</xs:annotation>
		</xs:attributeGroup>
		<xs:attribute name="VIP_Indicator" type="xs:boolean" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">If true, indicates a very important person.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="Text" type="StringLength1to32" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Used to specify textual information about the customer.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attributeGroup ref="LanguageGroup">
			<xs:annotation>
				<xs:documentation xml:lang="en">The primary language of the customer.</xs:documentation>
			</xs:annotation>
		</xs:attributeGroup>
		<xs:attribute name="CustomerValue" type="StringLength1to8" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The supplier's ranking of the customer (e.g., VIP, numerical ranking).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="MaritalStatus" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Marital status of the traveler.</xs:documentation>
			</xs:annotation>
			<xs:simpleType>
				<xs:restriction base="xs:NMTOKEN">
					<xs:enumeration value="Annulled">
						<xs:annotation>
							<xs:documentation>The marriage of the traveler has been annulled.</xs:documentation>
						</xs:annotation>
					</xs:enumeration>
					<xs:enumeration value="Co-habitating">
						<xs:annotation>
							<xs:documentation>The traveler is living with someone, but not married.</xs:documentation>
						</xs:annotation>
					</xs:enumeration>
					<xs:enumeration value="Divorced">
						<xs:annotation>
							<xs:documentation>The traveler is divorced.</xs:documentation>
						</xs:annotation>
					</xs:enumeration>
					<xs:enumeration value="Engaged">
						<xs:annotation>
							<xs:documentation>The traveler is engaged.</xs:documentation>
						</xs:annotation>
					</xs:enumeration>
					<xs:enumeration value="Married">
						<xs:annotation>
							<xs:documentation>
 the traveler is married
</xs:documentation>
						</xs:annotation>
					</xs:enumeration>
					<xs:enumeration value="Separated">
						<xs:annotation>
							<xs:documentation>The traveler is separated.</xs:documentation>
						</xs:annotation>
					</xs:enumeration>
					<xs:enumeration value="Single">
						<xs:annotation>
							<xs:documentation>The traveler is single.</xs:documentation>
						</xs:annotation>
					</xs:enumeration>
					<xs:enumeration value="Widowed">
						<xs:annotation>
							<xs:documentation>The traveler is widowed.</xs:documentation>
						</xs:annotation>
					</xs:enumeration>
					<xs:enumeration value="Unknown">
						<xs:annotation>
							<xs:documentation>The marital status of the traveler is unknown.</xs:documentation>
						</xs:annotation>
					</xs:enumeration>
				</xs:restriction>
			</xs:simpleType>
		</xs:attribute>
		<xs:attribute name="PreviouslyMarriedIndicator" type="xs:boolean" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">When true, indicates the customer was previously married. When false, indicates the customer was not previously married.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="ChildQuantity" type="Numeric1to99" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The number of children of the customer.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:complexType>
	<xs:complexType name="DateTimeSpanType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Used to specify a time window range by either specifying an earliest and latest date for the start date and end date or by giving a date with a time period that can be applied before and/or after the start date.</xs:documentation>
		</xs:annotation>
		<xs:choice>
			<xs:element name="DateWindowRange" type="TimeInstantType">
				<xs:annotation>
					<xs:documentation xml:lang="en">Specifies a time period that can be applied before and/or after the start date.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:sequence>
				<xs:element name="StartDateWindow" minOccurs="0">
					<xs:annotation>
						<xs:documentation xml:lang="en">The earliest and latest dates acceptable for the start date.</xs:documentation>
					</xs:annotation>
					<xs:complexType>
						<xs:attributeGroup ref="TimeWindowGroup">
							<xs:annotation>
								<xs:documentation xml:lang="en">Identifies a date range.</xs:documentation>
							</xs:annotation>
						</xs:attributeGroup>
					</xs:complexType>
				</xs:element>
				<xs:element name="EndDateWindow" minOccurs="0">
					<xs:annotation>
						<xs:documentation xml:lang="en">The earliest and latest dates acceptable for the end date.</xs:documentation>
					</xs:annotation>
					<xs:complexType>
						<xs:attributeGroup ref="TimeWindowGroup">
							<xs:annotation>
								<xs:documentation xml:lang="en">Identifies a date range.</xs:documentation>
							</xs:annotation>
						</xs:attributeGroup>
					</xs:complexType>
				</xs:element>
			</xs:sequence>
		</xs:choice>
		<xs:attributeGroup ref="DateTimeSpanGroup">
			<xs:annotation>
				<xs:documentation xml:lang="en">Defines the date and/or time span.</xs:documentation>
			</xs:annotation>
		</xs:attributeGroup>
	</xs:complexType>
	<xs:complexType name="DirectBillType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Company name and location for sending invoice for remittances for travel services.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="CompanyName" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Company name to whom remittance should be directed.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:simpleContent>
						<xs:extension base="CompanyNameType">
							<xs:attribute name="ContactName" type="StringLength1to64" use="optional">
								<xs:annotation>
									<xs:documentation xml:lang="en">This may be used to pass the name of the contact at the company for which the direct bill applies.</xs:documentation>
								</xs:annotation>
							</xs:attribute>
						</xs:extension>
					</xs:simpleContent>
				</xs:complexType>
			</xs:element>
			<xs:element name="Address" type="AddressInfoType" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Address where remittance should be directed.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Email" type="EmailType" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Email address to which remittance should be directed.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Telephone" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Telephone number associated with company to whom remittance is being directed.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:attributeGroup ref="TelephoneInfoGroup">
						<xs:annotation>
							<xs:documentation xml:lang="en">Information about a telephone number, including the actual number and its usage.</xs:documentation>
						</xs:annotation>
					</xs:attributeGroup>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
		<xs:attributeGroup ref="PrivacyGroup">
			<xs:annotation>
				<xs:documentation xml:lang="en">Allows for control of the sharing of direct bill data between parties.</xs:documentation>
			</xs:annotation>
		</xs:attributeGroup>
		<xs:attribute name="DirectBill_ID" type="StringLength1to32" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Identifier for the organization to be billed directly for travel services.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="BillingNumber" type="StringLength0to64" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The number assigned by the subscriber for billing reconciliation of a corporate account.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:complexType>
	<xs:complexType name="DocumentType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Provides information on a specific document.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:choice minOccurs="0">
				<xs:element name="DocHolderName" type="StringLength1to64" minOccurs="0">
					<xs:annotation>
						<xs:documentation xml:lang="en">The name of the document holder in unformatted text (Mr. Sam Jones).</xs:documentation>
					</xs:annotation>
				</xs:element>
				<xs:element name="DocHolderFormattedName" type="PersonNameType" minOccurs="0">
					<xs:annotation>
						<xs:documentation xml:lang="en">The name of document holder in formatted text.</xs:documentation>
					</xs:annotation>
				</xs:element>
			</xs:choice>
			<xs:element name="DocLimitations" type="StringLength1to64" minOccurs="0" maxOccurs="9">
				<xs:annotation>
					<xs:documentation xml:lang="en">Used to indicate any limitations on the document (e.g. as a person may only be allowed to spend a max of 30 days in country on a visitor's visa).</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="AdditionalPersonNames" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">A container for additional person names.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="AdditionalPersonName" type="StringLength1to64" maxOccurs="99">
							<xs:annotation>
								<xs:documentation xml:lang="en">The name of an additional person listed on this document.</xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
		<xs:attributeGroup ref="PrivacyGroup">
			<xs:annotation>
				<xs:documentation xml:lang="en">Allows for control of the sharing of document data between parties.</xs:documentation>
			</xs:annotation>
		</xs:attributeGroup>
		<xs:attribute name="DocIssueAuthority" type="StringLength1to64" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Indicates the group or association that granted the document.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="DocIssueLocation" type="StringLength1to64" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Indicates the location where the document was issued.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="DocID" type="StringLength1to32" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Unique number assigned by authorities to document.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="DocType" type="OTA_CodeType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Indicates the type of document (e.g. Passport, Military ID, Drivers License, national ID, Vaccination Certificate). Refer to OpenTravel Code List Document Type (DOC).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attributeGroup ref="GenderGroup">
			<xs:annotation>
				<xs:documentation xml:lang="en">The gender of the document holder.</xs:documentation>
			</xs:annotation>
		</xs:attributeGroup>
		<xs:attributeGroup ref="BirthDateGroup">
			<xs:annotation>
				<xs:documentation xml:lang="en">The birth date of the document holder.</xs:documentation>
			</xs:annotation>
		</xs:attributeGroup>
		<xs:attributeGroup ref="EffectiveExpireOptionalDateGroup">
			<xs:annotation>
				<xs:documentation xml:lang="en">The effective and expiry date of the document.</xs:documentation>
			</xs:annotation>
		</xs:attributeGroup>
		<xs:attribute name="DocIssueStateProv" type="StateProvCodeType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">State or Province where the document was issued.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="DocIssueCountry" type="ISO3166" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Country where the document was issued.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="BirthCountry" type="ISO3166" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Specifies the birth country of the document holder.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="BirthPlace" type="StringLength1to64" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Specifies the birth place of the document holder (e.g., city, state, county, province), when designating a country of birth, use BirthCountry.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="DocHolderNationality" type="ISO3166" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The country code of the nationality of the document holder.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="ContactName" type="StringLength1to64" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Provides contact name associated with the document.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="HolderType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Provides the type of document holder.</xs:documentation>
			</xs:annotation>
			<xs:simpleType>
				<xs:restriction base="xs:NMTOKEN">
					<xs:enumeration value="Infant">
						<xs:annotation>
							<xs:documentation xml:lang="en">The document holder is an infant.</xs:documentation>
						</xs:annotation>
					</xs:enumeration>
					<xs:enumeration value="HeadOfHousehold">
						<xs:annotation>
							<xs:documentation xml:lang="en">The document holder is a head of household.</xs:documentation>
						</xs:annotation>
					</xs:enumeration>
				</xs:restriction>
			</xs:simpleType>
		</xs:attribute>
		<xs:attribute name="Remark" type="StringLength1to128" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">A remark associated with the document.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="PostalCode" type="StringLength1to16" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Defines the postal code (e.g., ZIP code) on the document.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:complexType>
	<xs:complexType name="DonationType">
		<xs:annotation>
			<xs:documentation>Provides information about donations made during a booking.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="FrontOfficeInfo" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The identifying fields for the front office for a Massive Good donation: Product Name, Product version, Office ID and Corporate ID.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:attribute name="ProductName" type="xs:string" use="required">
						<xs:annotation>
							<xs:documentation xml:lang="en">The Massive Good/ Amadeus Donation Service product name.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="ProductVersion" type="xs:string" use="required">
						<xs:annotation>
							<xs:documentation xml:lang="en">The Massive Good/ Amadeus Donation Service product version.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="OfficeID" type="xs:string" use="required">
						<xs:annotation>
							<xs:documentation xml:lang="en">The Massive Good/ Amadeus Donation Service office ID.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="CorporateID" type="xs:string" use="required">
						<xs:annotation>
							<xs:documentation xml:lang="en">The Massive Good/ Amadeus Donation Service corporate ID.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
				</xs:complexType>
			</xs:element>
			<xs:element name="CreditCardInfo">
				<xs:annotation>
					<xs:documentation xml:lang="en">Donation credit card information.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:complexContent>
						<xs:extension base="PaymentCardType">
							<xs:attribute name="Currency" type="AlphaLength3" use="required">
								<xs:annotation>
									<xs:documentation xml:lang="en">The donation currency code (ISO 4217.) </xs:documentation>
								</xs:annotation>
							</xs:attribute>
							<xs:attribute name="DonationAmount" type="Money" use="required">
								<xs:annotation>
									<xs:documentation xml:lang="en">The donation amount.</xs:documentation>
								</xs:annotation>
							</xs:attribute>
						</xs:extension>
					</xs:complexContent>
				</xs:complexType>
			</xs:element>
			<xs:element name="DonorInfo" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Donor information, including Email Address, First Name, Last Name, Street Address, City and Zip Code.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="Name" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">The donor name.</xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:complexContent>
									<xs:extension base="PersonNameType"/>
								</xs:complexContent>
							</xs:complexType>
						</xs:element>
						<xs:element name="ContactInfo" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">Donor address and email address information.</xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:complexContent>
									<xs:extension base="AddressType">
										<xs:attribute name="EmailAddress" type="xs:string" use="optional">
											<xs:annotation>
												<xs:documentation xml:lang="en">The donor email address.</xs:documentation>
											</xs:annotation>
										</xs:attribute>
									</xs:extension>
								</xs:complexContent>
							</xs:complexType>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="Language" type="xs:language" use="required">
			<xs:annotation>
				<xs:documentation xml:lang="en">The localized error response language. </xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="GDS_ID" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The ID of the GDS (global distribution service) providing the donation.</xs:documentation>
			</xs:annotation>
			<xs:simpleType>
				<xs:restriction base="xs:NMTOKEN">
					<xs:enumeration value="1A">
						<xs:annotation>
							<xs:documentation xml:lang="en">Amadeus</xs:documentation>
						</xs:annotation>
					</xs:enumeration>
					<xs:enumeration value="1G">
						<xs:annotation>
							<xs:documentation xml:lang="en">Galileo</xs:documentation>
						</xs:annotation>
					</xs:enumeration>
					<xs:enumeration value="1P">
						<xs:annotation>
							<xs:documentation xml:lang="en">Worldspan</xs:documentation>
						</xs:annotation>
					</xs:enumeration>
					<xs:enumeration value="1V">
						<xs:annotation>
							<xs:documentation xml:lang="en">Apollo</xs:documentation>
						</xs:annotation>
					</xs:enumeration>
					<xs:enumeration value="1W">
						<xs:annotation>
							<xs:documentation xml:lang="en">Sabre</xs:documentation>
						</xs:annotation>
					</xs:enumeration>
					<xs:enumeration value="WE">
						<xs:annotation>
							<xs:documentation xml:lang="en">Website</xs:documentation>
						</xs:annotation>
					</xs:enumeration>
					<xs:enumeration value="WS">
						<xs:annotation>
							<xs:documentation xml:lang="en">WebService</xs:documentation>
						</xs:annotation>
					</xs:enumeration>
					<xs:enumeration value="MF">
						<xs:annotation>
							<xs:documentation xml:lang="en">Millennium Foundation</xs:documentation>
						</xs:annotation>
					</xs:enumeration>
					<xs:enumeration value="ZZ">
						<xs:annotation>
							<xs:documentation xml:lang="en">Other</xs:documentation>
						</xs:annotation>
					</xs:enumeration>
				</xs:restriction>
			</xs:simpleType>
		</xs:attribute>
		<xs:attribute name="AskForReceiptInd" type="xs:boolean" use="required">
			<xs:annotation>
				<xs:documentation xml:lang="en">If true, the donor wants a donation receipt. </xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="CountryCode" type="ISO3166" use="required">
			<xs:annotation>
				<xs:documentation xml:lang="en">The 2 character ISO3166 country code of the donor.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="StateCode" type="StateProvCodeType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The origin state of the donor.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:complexType>
	<xs:complexType name="EmailType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Electronic email addresses, in IETF specified format.</xs:documentation>
		</xs:annotation>
		<xs:simpleContent>
			<xs:extension base="StringLength1to128">
				<xs:attributeGroup ref="PrivacyGroup">
					<xs:annotation>
						<xs:documentation xml:lang="en">Allows for control of the sharing of email information between parties.</xs:documentation>
					</xs:annotation>
				</xs:attributeGroup>
				<xs:attributeGroup ref="DefaultIndGroup">
					<xs:annotation>
						<xs:documentation xml:lang="en">Identifies whether or not this is the default email address.</xs:documentation>
					</xs:annotation>
				</xs:attributeGroup>
				<xs:attribute name="EmailType" type="OTA_CodeType" use="optional">
					<xs:annotation>
						<xs:documentation xml:lang="en">Defines the purpose of the e-mail address (e.g. personal, business, listserve). Refer to OpenTravel Code List Email Address Type (EAT).</xs:documentation>
					</xs:annotation>
				</xs:attribute>
				<xs:attribute name="RPH" type="RPH_Type" use="optional">
					<xs:annotation>
						<xs:documentation xml:lang="en">Used elsewhere in the message to reference this specific email address.</xs:documentation>
					</xs:annotation>
				</xs:attribute>
				<xs:attribute name="Remark" type="StringLength1to128" use="optional">
					<xs:annotation>
						<xs:documentation xml:lang="en">A remark associated with the e-mail address.</xs:documentation>
					</xs:annotation>
				</xs:attribute>
			</xs:extension>
		</xs:simpleContent>
	</xs:complexType>
	<xs:complexType name="EmployeeInfoType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Employment identification; using an employee ID number, title, level within the company, and an indication of their status (e.g., active, retired, on leave, or terminated from employment).  Additional information about an employee can be entered into the element, including the name of the employee, if it differs from the person name identified as a customer or contact.</xs:documentation>
		</xs:annotation>
		<xs:simpleContent>
			<xs:extension base="StringLength0to64">
				<xs:attribute name="EmployeeId" type="StringLength1to16">
					<xs:annotation>
						<xs:documentation xml:lang="en">Identifier assigned to the employee.</xs:documentation>
					</xs:annotation>
				</xs:attribute>
				<xs:attribute name="EmployeeLevel" type="StringLength1to16" use="optional">
					<xs:annotation>
						<xs:documentation xml:lang="en">Level in employer organization (e.g. seniority) that coveys privileges.</xs:documentation>
					</xs:annotation>
				</xs:attribute>
				<xs:attribute name="EmployeeTitle" type="StringLength1to64" use="optional">
					<xs:annotation>
						<xs:documentation xml:lang="en">Title of employee in the employer company that conveys rank or privileges.</xs:documentation>
					</xs:annotation>
				</xs:attribute>
				<xs:attribute name="EmployeeStatus" type="OTA_CodeType" use="optional">
					<xs:annotation>
						<xs:documentation xml:lang="en">Status of employment. Refer to OpenTravel Code List Employee Status (EMP).</xs:documentation>
					</xs:annotation>
				</xs:attribute>
			</xs:extension>
		</xs:simpleContent>
	</xs:complexType>
	<xs:complexType name="EquipmentType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Specifies the aircraft equipment type.</xs:documentation>
		</xs:annotation>
		<xs:simpleContent>
			<xs:extension base="StringLength0to64">
				<xs:attribute name="AirEquipType" type="StringLength3" use="optional">
					<xs:annotation>
						<xs:documentation xml:lang="en">This is the 3 character IATA code.</xs:documentation>
					</xs:annotation>
				</xs:attribute>
				<xs:attribute name="ChangeofGauge" type="xs:boolean" use="optional">
					<xs:annotation>
						<xs:documentation xml:lang="en">Indicates there is an equipment change.</xs:documentation>
						<xs:documentation xml:lang="en">
							<LegacyDefaultValue>false</LegacyDefaultValue>
						</xs:documentation>
					</xs:annotation>
				</xs:attribute>
			</xs:extension>
		</xs:simpleContent>
	</xs:complexType>
	<xs:complexType name="ErrorType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Standard way to indicate that an error occurred during the processing of an OpenTravel message. If the message successfully processes, but there are business errors, those errors should be passed in the warning element.</xs:documentation>
		</xs:annotation>
		<xs:simpleContent>
			<xs:extension base="FreeTextType">
				<xs:attribute name="Type" type="OTA_CodeType" use="required">
					<xs:annotation>
						<xs:documentation xml:lang="en">The Error element MUST contain the Type attribute that uses a recommended set of values to indicate the error type. The validating XSD can expect to accept values that it has NOT been explicitly coded for and process them by using Type ="Unknown". Refer to OpenTravel Code List Error Warning Type (EWT).</xs:documentation>
					</xs:annotation>
				</xs:attribute>
				<xs:attributeGroup ref="ErrorWarningAttributeGroup">
					<xs:annotation>
						<xs:documentation xml:lang="en">Details of the error.</xs:documentation>
					</xs:annotation>
				</xs:attributeGroup>
				<xs:attribute name="NodeList" type="xs:string" use="optional">
					<xs:annotation>
						<xs:documentation xml:lang="en">An XPath expression that selects all the nodes whose data caused this error. Further, this expression should have an additional contraint which contains the data of the node. This will provide the offending data back to systems that cannot maintain the original message.</xs:documentation>
					</xs:annotation>
				</xs:attribute>
			</xs:extension>
		</xs:simpleContent>
	</xs:complexType>
	<xs:complexType name="ErrorsType">
		<xs:annotation>
			<xs:documentation xml:lang="en">A collection of errors that occurred during the processing of a message.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="Error" type="ErrorType" maxOccurs="99">
				<xs:annotation>
					<xs:documentation xml:lang="en">An error that occurred during the processing of a message.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="FeeType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Used for non-tax fees and charges (e.g. service charges) .</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="Taxes" type="TaxesType" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Used for taxes on the associated fee.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Description" type="ParagraphType" minOccurs="0" maxOccurs="5">
				<xs:annotation>
					<xs:documentation xml:lang="en">Text description of the fees in a given language.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="TaxInclusive" type="xs:boolean">
			<xs:annotation>
				<xs:documentation xml:lang="en">Indicates whether taxes are included when figuring the fees.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attributeGroup ref="FeeTaxGroup">
			<xs:annotation>
				<xs:documentation xml:lang="en">Provides details of the fee.</xs:documentation>
			</xs:annotation>
		</xs:attributeGroup>
		<xs:attributeGroup ref="EffectiveExpireOptionalDateGroup">
			<xs:annotation>
				<xs:documentation xml:lang="en">Provides the effective and expiry dates for the fee.</xs:documentation>
			</xs:annotation>
		</xs:attributeGroup>
		<xs:attribute name="MandatoryIndicator" type="xs:boolean" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">When true, indicates the fee is mandatory. When false, the fee is not mandatory.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="RPH" type="RPH_Type" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">An index code to identify an instance in a collection of like items.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attributeGroup ref="ChargeUnitGroup">
			<xs:annotation>
				<xs:documentation xml:lang="en">Specifies charge information by unit (e.g., room, person, item) and frequency (e.g., daily, weekly, stay).</xs:documentation>
			</xs:annotation>
		</xs:attributeGroup>
		<xs:attribute name="TaxableIndicator" type="xs:boolean" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">When true, indicates that the fee is subject to tax.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:complexType>
	<xs:complexType name="FeesType">
		<xs:annotation>
			<xs:documentation xml:lang="en">A collection of fees.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="Fee" type="FeeType" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation xml:lang="en">Fee Amount that is applied to the rate. Fees are used for non tax amounts like service charges.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="FlightSegmentBaseType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Construct for holding a flight segment availability object.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="DepartureAirport" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Departure point of flight segment.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:attributeGroup ref="AirportLocationGroup">
						<xs:annotation>
							<xs:documentation xml:lang="en">Identifies the airport location for the departure.</xs:documentation>
						</xs:annotation>
					</xs:attributeGroup>
				</xs:complexType>
			</xs:element>
			<xs:element name="ArrivalAirport" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Arrival point of flight segment.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:attributeGroup ref="AirportLocationGroup">
						<xs:annotation>
							<xs:documentation xml:lang="en">Identifies the airport location for the arrival.</xs:documentation>
						</xs:annotation>
					</xs:attributeGroup>
				</xs:complexType>
			</xs:element>
			<xs:element name="OperatingAirline" type="OperatingAirlineType" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The operating airline of the flight if it is a codeshare flight.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Equipment" type="EquipmentType" minOccurs="0" maxOccurs="2">
				<xs:annotation>
					<xs:documentation xml:lang="en">The type of equipment used for the flight.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="DepartureDateTime" type="xs:dateTime" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The date and time of the flight segment departure.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="ArrivalDateTime" type="xs:dateTime" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Specifies the arrival date and time of a flight.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="StopQuantity" type="xs:nonNegativeInteger" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The number of stops the flight makes.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="RPH" type="RPH_Type" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Reference place holder for this flight segment.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="InfoSource" type="InfoSourceType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Used to specify the source of the data being exchanged as determined by trading partners.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:complexType>
	<xs:complexType name="FormattedTextSubSectionType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Used to provide subsection formatted text information.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="Paragraph" type="ParagraphType" maxOccurs="99">
				<xs:annotation>
					<xs:documentation xml:lang="en">Details and content of a paragraph for a formatted text message.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="SubTitle" type="StringLength1to64" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">This attribute may be used to provide a title for a sub-section of the formatted free text. A sub-section may have multiple related paragraphs of information. For example, if used to provide driving directions there may be multiple paragraphs, and these paragraphs may be grouped into a sub-section called "Driving from the North". A second subsection may be titled "Driving from the South", and may contain several paragraphs to describe the driving directions when driving from the south.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="SubCode" type="StringLength1to8" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">An optional code that may be assigned to this sub-section of formatted free text.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="SubSectionNumber" type="Numeric1to999" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">This attribute may be used when there is a need to number all of the sub-sections of information that is to be presented.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:complexType>
	<xs:complexType name="FormattedTextTextType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Provides text and indicates whether it is formatted or not.</xs:documentation>
		</xs:annotation>
		<xs:simpleContent>
			<xs:extension base="xs:string">
				<xs:attribute name="Formatted" type="xs:boolean" use="optional">
					<xs:annotation>
						<xs:documentation xml:lang="en">Textual information, which may be formatted as a line of information, or unformatted, as a paragraph of text.</xs:documentation>
					</xs:annotation>
				</xs:attribute>
				<xs:attributeGroup ref="LanguageGroup">
					<xs:annotation>
						<xs:documentation xml:lang="en">The language in which the text is provided.</xs:documentation>
					</xs:annotation>
				</xs:attributeGroup>
				<xs:attribute name="TextFormat" use="optional">
					<xs:annotation>
						<xs:documentation xml:lang="en">Indicates the format of text used in the description e.g. unformatted  or html.</xs:documentation>
					</xs:annotation>
					<xs:simpleType>
						<xs:restriction base="xs:NMTOKEN">
							<xs:enumeration value="PlainText">
								<xs:annotation>
									<xs:documentation xml:lang="en">Textual data that is in ASCII format.</xs:documentation>
								</xs:annotation>
							</xs:enumeration>
							<xs:enumeration value="HTML">
								<xs:annotation>
									<xs:documentation xml:lang="en">HTML formatted text.</xs:documentation>
								</xs:annotation>
							</xs:enumeration>
						</xs:restriction>
					</xs:simpleType>
				</xs:attribute>
			</xs:extension>
		</xs:simpleContent>
	</xs:complexType>
	<xs:complexType name="FormattedTextType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Collection of formatted text sub sections.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="SubSection" type="FormattedTextSubSectionType" maxOccurs="99">
				<xs:annotation>
					<xs:documentation xml:lang="en">Subsection formatted text information.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="Title" type="StringLength1to64" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">This attribute may be used to provide a title for the formatted free text, for example, Driving Directions. Each of the sub sections that are defined to be a part of the formatted text would provide detailed information about the subject identified by the title.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="Language" type="xs:language" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The language in which the content is provided.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:complexType>
	<xs:complexType name="FreeTextType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Textual information to provide descriptions and/or additional information.</xs:documentation>
		</xs:annotation>
		<xs:simpleContent>
			<xs:extension base="xs:string">
				<xs:attributeGroup ref="LanguageGroup">
					<xs:annotation>
						<xs:documentation xml:lang="en">Language of the text.</xs:documentation>
					</xs:annotation>
				</xs:attributeGroup>
			</xs:extension>
		</xs:simpleContent>
	</xs:complexType>
	<xs:complexType name="ImageDescriptionType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Describes an image item.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="ImageFormat" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation xml:lang="en">A set of images for a given category which may be provided in multiple formats.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:complexContent>
						<xs:extension base="ImageItemType">
							<xs:attributeGroup ref="MultimediaDescriptionGroup">
								<xs:annotation>
									<xs:documentation xml:lang="en">Detailed information about an image.</xs:documentation>
								</xs:annotation>
							</xs:attributeGroup>
							<xs:attribute name="Language" type="xs:language" use="optional">
								<xs:annotation>
									<xs:documentation xml:lang="en">The language in which the image text is provided.</xs:documentation>
								</xs:annotation>
							</xs:attribute>
							<xs:attribute name="Format" type="OTA_CodeType" use="optional">
								<xs:annotation>
									<xs:documentation xml:lang="en">Identifies the format of an image. Refer to OpenTravel Code List Content Format Code (CFC).</xs:documentation>
								</xs:annotation>
							</xs:attribute>
							<xs:attribute name="FileName" type="StringLength1to64" use="optional">
								<xs:annotation>
									<xs:documentation xml:lang="en">The name of the image file.</xs:documentation>
								</xs:annotation>
							</xs:attribute>
							<xs:attribute name="FileSize" type="xs:positiveInteger" use="optional">
								<xs:annotation>
									<xs:documentation xml:lang="en">The size of the image file.</xs:documentation>
								</xs:annotation>
							</xs:attribute>
							<xs:attribute name="DimensionCategory" type="StringLength1to16" use="optional">
								<xs:annotation>
									<xs:documentation xml:lang="en">Associates the image size to a given category (e.g., 70x70, 100x100, 480x480, thumbnail). For example, if an image with a dimension of 72x73 is sent, it may be categorized as a 70x70 image.</xs:documentation>
								</xs:annotation>
							</xs:attribute>
							<xs:attribute name="IsOriginalIndicator" type="xs:boolean" use="optional">
								<xs:annotation>
									<xs:documentation xml:lang="en">When true, the image is the original file and format. When false, the image is not the original file and format.</xs:documentation>
								</xs:annotation>
							</xs:attribute>
						</xs:extension>
					</xs:complexContent>
				</xs:complexType>
			</xs:element>
			<xs:element name="Description" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation xml:lang="en">The description associated with the image in a specific language.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:simpleContent>
						<xs:extension base="FormattedTextTextType">
							<xs:attribute name="Caption" type="StringLength1to128" use="optional">
								<xs:annotation>
									<xs:documentation xml:lang="en">The caption associated to a specific image category which can be provided in different languages.</xs:documentation>
								</xs:annotation>
							</xs:attribute>
						</xs:extension>
					</xs:simpleContent>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="Category" type="OTA_CodeType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Specifies the image category. Refer to OpenTravel Code list Picture Category Code (PIC).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:complexType>
	<xs:complexType name="ImageItemsType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Collection of image items.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="ImageItem" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation xml:lang="en">Image of a given category.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:complexContent>
						<xs:extension base="ImageDescriptionType">
							<xs:attributeGroup ref="DateTimeStampGroup">
								<xs:annotation>
									<xs:documentation xml:lang="en">Creation and modification information for this image item.</xs:documentation>
								</xs:annotation>
							</xs:attributeGroup>
							<xs:attributeGroup ref="RemovalGroup">
								<xs:annotation>
									<xs:documentation xml:lang="en">If true, this item is obsolete and should be removed from the receiving system.</xs:documentation>
								</xs:annotation>
							</xs:attributeGroup>
							<xs:attribute name="Version" type="xs:string" use="optional">
								<xs:annotation>
									<xs:documentation xml:lang="en">The version of the image item.</xs:documentation>
								</xs:annotation>
							</xs:attribute>
							<xs:attributeGroup ref="ID_OptionalGroup">
								<xs:annotation>
									<xs:documentation xml:lang="en">A unique identifying value assigned by the creating system. The ID attribute may be used to reference a primary-key value within a database or in a particular implementation.</xs:documentation>
								</xs:annotation>
							</xs:attributeGroup>
						</xs:extension>
					</xs:complexContent>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="ImageItemType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Details for an image of a given category.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="URL" type="xs:anyURI" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">URL of the multimedia item for a specific format.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="UnitOfMeasureCode" type="OTA_CodeType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The unit of measure for the image item. Refer to OpenTravel Code list Unit of Measure (UOM).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="Width" type="xs:positiveInteger" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The width of the image item (unit specified by unit of measure).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="Height" type="xs:positiveInteger" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The height of the image item (unit specified by unit of measure).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:complexType>
	<xs:complexType name="ItemSearchCriterionType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Identifies the criterion for a search.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="Position" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The Position element contains three attributes, Latitude, Longitude, and Altitude, used to indicate the geographic location(s) requested by the search, expressed in notation specified by ISO standard 6709. It is likely that only the first two attributes, Latitude and Longitude, would be needed to define a geographic area.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:attributeGroup ref="PositionGroup">
						<xs:annotation>
							<xs:documentation xml:lang="en">The geographic coordinates for the search.</xs:documentation>
						</xs:annotation>
					</xs:attributeGroup>
				</xs:complexType>
			</xs:element>
			<xs:element name="Address" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Uses any part of address information, such as street name, postal code, or country code.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:complexContent>
						<xs:extension base="AddressType">
							<xs:attribute name="SameCountryInd" type="xs:boolean" use="optional">
								<xs:annotation>
									<xs:documentation xml:lang="en">When true, only locations in the same country as the specified city's country should be selected.</xs:documentation>
								</xs:annotation>
							</xs:attribute>
							<xs:attribute name="AddressSearchScope">
								<xs:annotation>
									<xs:documentation xml:lang="en">Determines how the keywords specified in the Address element will be processed.</xs:documentation>
								</xs:annotation>
								<xs:simpleType>
									<xs:restriction base="xs:NMTOKEN">
										<xs:enumeration value="Primary">
											<xs:annotation>
												<xs:documentation xml:lang="en">The address search keyword(s) will be compared to the physical address of the property.</xs:documentation>
											</xs:annotation>
										</xs:enumeration>
										<xs:enumeration value="Alternate">
											<xs:annotation>
												<xs:documentation xml:lang="en">The 'city' area attractions associated with the property will be searched.</xs:documentation>
											</xs:annotation>
										</xs:enumeration>
										<xs:enumeration value="PrimaryAndAlternate">
											<xs:annotation>
												<xs:documentation xml:lang="en">The address search keyword(s) will be compared to the physical address of the property and the 'city' area attractions associated with the property will be searched.</xs:documentation>
											</xs:annotation>
										</xs:enumeration>
									</xs:restriction>
								</xs:simpleType>
							</xs:attribute>
						</xs:extension>
					</xs:complexContent>
				</xs:complexType>
			</xs:element>
			<xs:element name="Telephone" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Telephone number(s) used in the search.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:attributeGroup ref="TelephoneGroup">
						<xs:annotation>
							<xs:documentation xml:lang="en">Detailed telephone information for the search.</xs:documentation>
						</xs:annotation>
					</xs:attributeGroup>
				</xs:complexType>
			</xs:element>
			<xs:element name="RefPoint" minOccurs="0" maxOccurs="999">
				<xs:annotation>
					<xs:documentation xml:lang="en">The Reference Point element allows for a search by proximity to a designated reference point by name.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:simpleContent>
						<xs:extension base="StringLength0to64">
							<xs:attribute name="StateProv" type="StateProvCodeType" use="optional">
								<xs:annotation>
									<xs:documentation xml:lang="en">The state or province in which the reference point is located.</xs:documentation>
								</xs:annotation>
							</xs:attribute>
							<xs:attribute name="CountryCode" type="ISO3166" use="optional">
								<xs:annotation>
									<xs:documentation xml:lang="en">The country in which the reference point is located.</xs:documentation>
								</xs:annotation>
							</xs:attribute>
							<xs:attributeGroup ref="CodeListGroup">
								<xs:annotation>
									<xs:documentation xml:lang="en">Used to specify a reference point by a code.</xs:documentation>
								</xs:annotation>
							</xs:attributeGroup>
							<xs:attribute name="RefPointType" type="OTA_CodeType" use="optional">
								<xs:annotation>
									<xs:documentation xml:lang="en">Indicates the type of location being referenced (e.g., Airport, Hotel). Refer to the OpenTravel Code table Index Point Code (IPC).</xs:documentation>
								</xs:annotation>
							</xs:attribute>
							<xs:attribute name="Name" type="StringLength1to128" use="optional">
								<xs:annotation>
									<xs:documentation xml:lang="en">The name of the reference point.</xs:documentation>
								</xs:annotation>
							</xs:attribute>
							<xs:attribute name="CityName" type="StringLength1to64" use="optional">
								<xs:annotation>
									<xs:documentation xml:lang="en">The name of the city associated with this reference point.</xs:documentation>
								</xs:annotation>
							</xs:attribute>
						</xs:extension>
					</xs:simpleContent>
				</xs:complexType>
			</xs:element>
			<xs:element name="CodeRef" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Indicates the location of points of interest.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:simpleContent>
						<xs:extension base="LocationType">
							<xs:attribute name="VicinityCode" type="OTA_CodeType" use="optional">
								<xs:annotation>
									<xs:documentation xml:lang="en">Used to identify the vicinity of the location. Refer to OpenTravel Codelist Vehicle Where at Facility (VWF).</xs:documentation>
								</xs:annotation>
							</xs:attribute>
						</xs:extension>
					</xs:simpleContent>
				</xs:complexType>
			</xs:element>
			<xs:element name="HotelRef" minOccurs="0" maxOccurs="99">
				<xs:annotation>
					<xs:documentation xml:lang="en">Indicates the detail of hotel reference information.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:attributeGroup ref="HotelReferenceGroup">
						<xs:annotation>
							<xs:documentation xml:lang="en">Detailed hotel information for the search.</xs:documentation>
						</xs:annotation>
					</xs:attributeGroup>
					<xs:attribute name="SegmentCategoryCode" type="OTA_CodeType" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">Used to search for hotels within a particular market segment. Refer to OpenTravel Code Segment Category Code Type (SEG).</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="PropertyClassCode" type="OTA_CodeType" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">Refer to OpenTravel Code list OpenTravel Code List Property Class Type (PCT).</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="ArchitecturalStyleCode" type="OTA_CodeType" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">Refer to OpenTravel Code List Architectural Style Code (ARC).</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="SupplierIntegrationLevel" type="xs:nonNegativeInteger" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">The level of integration of a property to provide automated transaction information. The lower the number, the higher the integration (e.g., a 1 means the supplier has the highest level of integration automation).</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="LocationCategoryCode" type="OTA_CodeType" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">Used to search for hotels in a specific location category (e.g. downtown, airport or suburban, etc.). Refer to OpenTravel Code List Location Category Codes (LOC).</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="ExtendedCitySearchIndicator" type="xs:boolean" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">When true indicates the search should be performed beyond the hotel city code, typically this could include neighboring cities to the specified hotel city code.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
				</xs:complexType>
			</xs:element>
			<xs:element name="Radius" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Used to specify the extent of a search area. The extent is relative to an element (position, address, hotel reference, etc.) present in this ItemSearchCriterionType that specifies a location.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:attributeGroup ref="DistanceAttributesGroup">
						<xs:annotation>
							<xs:documentation xml:lang="en">Used to provide distance and direction information from the referenced location.</xs:documentation>
						</xs:annotation>
					</xs:attributeGroup>
				</xs:complexType>
			</xs:element>
			<xs:element name="MapArea" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Provides coordinates used to define the area of a map.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:attribute name="NorthLatitude" type="StringLength1to16" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">The latitude of the northern boundary of the map.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="SouthLatitude" type="StringLength1to16" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">The latitude of the southern boundary of the map.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="EastLongitude" type="StringLength1to16" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">The longitude of the eastern boundary of the map.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="WestLongitude" type="StringLength1to16" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">The longitude of the western boundary of the map.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
				</xs:complexType>
			</xs:element>
			<xs:element name="AdditionalContents" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">A collection of Additional Content elements.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="AdditionalContent" maxOccurs="unbounded">
							<xs:annotation>
								<xs:documentation xml:lang="en">Describes the specific content requested.</xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:attribute name="ContentGroupCode" type="OTA_CodeType" use="required">
									<xs:annotation>
										<xs:documentation xml:lang="en">When used, the response message will be filtered to return only the content that was requested. Refer to OpenTravel code list Information Type (INF).</xs:documentation>
									</xs:annotation>
								</xs:attribute>
								<xs:attributeGroup ref="CodeInfoGroup">
									<xs:annotation>
										<xs:documentation xml:lang="en">This is intended to be used in conjunction with the ContentGroupCode attribute to provide additional information about the code being referenced.</xs:documentation>
									</xs:annotation>
								</xs:attributeGroup>
							</xs:complexType>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="ExactMatch" type="xs:boolean" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Values of "true" or "false", indicating whether the string of the search value must be an exact match.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="ImportanceType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">An enumerated list, indicating the level of importance of the search criterion. Acceptable values are "Mandatory", "High", "Medium", or "Low."</xs:documentation>
			</xs:annotation>
			<xs:simpleType>
				<xs:restriction base="StringLength1to16">
					<xs:enumeration value="Mandatory">
						<xs:annotation>
							<xs:documentation xml:lang="en">Indicates the item is required.</xs:documentation>
						</xs:annotation>
					</xs:enumeration>
					<xs:enumeration value="High">
						<xs:annotation>
							<xs:documentation xml:lang="en">Indicates a high level of importance.</xs:documentation>
						</xs:annotation>
					</xs:enumeration>
					<xs:enumeration value="Medium">
						<xs:annotation>
							<xs:documentation xml:lang="en">Indicates a medium level of importance.</xs:documentation>
						</xs:annotation>
					</xs:enumeration>
					<xs:enumeration value="Low">
						<xs:annotation>
							<xs:documentation xml:lang="en">Indicates a low level of importance.</xs:documentation>
						</xs:annotation>
					</xs:enumeration>
				</xs:restriction>
			</xs:simpleType>
		</xs:attribute>
		<xs:attribute name="Ranking" type="xs:integer" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Defines a ranking scale expressed as integers; meaning and scale are based on individual implementations.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:complexType>
	<xs:complexType name="LocationGeneralType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Provides high-level information regarding a location.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="CityName" type="StringLength1to64" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">City (e.g., Dublin), town, or postal station (i.e., a postal service territory, often used in a military address).</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="StateProv" type="StateProvType" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">State or Province name (e.g., Texas).</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="CountryName" type="CountryNameType" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Country name (e.g., Ireland).</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="LocationType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Code and optional string to describe a location point.</xs:documentation>
		</xs:annotation>
		<xs:simpleContent>
			<xs:extension base="xs:string">
				<xs:attributeGroup ref="LocationGroup">
					<xs:annotation>
						<xs:documentation xml:lang="en">A code identifying a location.</xs:documentation>
					</xs:annotation>
				</xs:attributeGroup>
			</xs:extension>
		</xs:simpleContent>
	</xs:complexType>
	<xs:complexType name="MessageAcknowledgementType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Information to acknowledge the receipt of a message.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:choice>
				<xs:sequence>
					<xs:element name="Success" type="SuccessType">
						<xs:annotation>
							<xs:documentation xml:lang="en">Returning an empty element of this type indicates the successful processing of an OpenTravel message. This is used in conjunction with Warnings to report any warnings or business errors.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="Warnings" type="WarningsType" minOccurs="0">
						<xs:annotation>
							<xs:documentation xml:lang="en">Used when a message has been successfully processed to report any warnings or business errors that occurred.</xs:documentation>
						</xs:annotation>
					</xs:element>
				</xs:sequence>
				<xs:element name="Errors" type="ErrorsType">
					<xs:annotation>
						<xs:documentation xml:lang="en">Indicates an error occurred during the processing of an OpenTravel message. If the message successfully processes, but there are business errors, those errors should be passed in the warning element.</xs:documentation>
					</xs:annotation>
				</xs:element>
			</xs:choice>
			<xs:element name="UniqueID" type="UniqueID_Type" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">May be used to return the unique id from the request message.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element ref="TPA_Extensions" minOccurs="0"/>
		</xs:sequence>
		<xs:attributeGroup ref="OTA_PayloadStdAttributes">
			<xs:annotation>
				<xs:documentation xml:lang="en">The OTA_PayloadStdAttributes defines the standard attributes that appear on the root element for all OpenTravel Messages.</xs:documentation>
			</xs:annotation>
		</xs:attributeGroup>
	</xs:complexType>
	<xs:complexType name="MonetaryRuleType">
		<xs:annotation>
			<xs:documentation xml:lang="en">This defines the information pertaining to rules and amounts associated with these rules.</xs:documentation>
		</xs:annotation>
		<xs:simpleContent>
			<xs:extension base="StringLength0to255">
				<xs:attributeGroup ref="CurrencyAmountGroup">
					<xs:annotation>
						<xs:documentation xml:lang="en">Provides the currency amount pertaining to the rule.</xs:documentation>
					</xs:annotation>
				</xs:attributeGroup>
				<xs:attribute name="RuleType" type="OTA_CodeType" use="required">
					<xs:annotation>
						<xs:documentation xml:lang="en">Refer to OpenTravel Code List Rule Type (RUL).</xs:documentation>
					</xs:annotation>
				</xs:attribute>
				<xs:attribute name="Percent" type="Percentage" use="optional">
					<xs:annotation>
						<xs:documentation xml:lang="en">The percent applicable to the monetary rule.</xs:documentation>
					</xs:annotation>
				</xs:attribute>
				<xs:attribute name="DateTime" type="xs:dateTime" use="optional">
					<xs:annotation>
						<xs:documentation xml:lang="en">The date and time applicable to this monetary rule.</xs:documentation>
					</xs:annotation>
				</xs:attribute>
				<xs:attribute name="PaymentType" type="OTA_CodeType" use="optional">
					<xs:annotation>
						<xs:documentation xml:lang="en">Refer to OpenTravel Code List Payment Type (PMT).</xs:documentation>
					</xs:annotation>
				</xs:attribute>
				<xs:attribute name="RateConvertedInd" type="xs:boolean" use="optional">
					<xs:annotation>
						<xs:documentation xml:lang="en">When true, indicates the amount was converted from another currency.</xs:documentation>
					</xs:annotation>
				</xs:attribute>
				<xs:attributeGroup ref="DeadlineGroup"/>
			</xs:extension>
		</xs:simpleContent>
	</xs:complexType>
	<xs:complexType name="MultimediaDescriptionsType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Contains multimedia item(s).</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="MultimediaDescription" type="MultimediaDescriptionType" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation xml:lang="en">A multimedia item.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="LastUpdated" type="xs:dateTime" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The date and time when the collection of multimedia information was last updated.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:complexType>
	<xs:complexType name="MultimediaDescriptionType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Describes multimedia item(s).</xs:documentation>
		</xs:annotation>
		<xs:choice minOccurs="0">
			<xs:element name="VideoItems" type="VideoItemsType" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">A collection of video items.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="ImageItems" type="ImageItemsType" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">A collection of image items.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="TextItems" type="TextItemsType" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">A collection of text items.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:choice>
		<xs:attribute name="InfoCode" type="OTA_CodeType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Used to designate a particular type of description such as marketing. Refer to OpenTravel Code List Information Type (INF).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="AdditionalDetailCode" type="OTA_CodeType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Used to designate a particular type of additional information. Refer to OpenTravel Code List Additional Detail Type (ADT).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attributeGroup ref="ID_OptionalGroup">
			<xs:annotation>
				<xs:documentation xml:lang="en">A unique identifying value assigned by the creating system. The ID attribute may be used to reference a primary-key value within a database or in a particular implementation.</xs:documentation>
			</xs:annotation>
		</xs:attributeGroup>
		<xs:attribute name="LastUpdated" type="xs:dateTime" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The date and time when the multimedia information was last updated.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="Version" type="xs:string" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The version of the content, typically in the form of "1" or "2.3."</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:complexType>
	<xs:complexType name="OperatingAirlineType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Identifies the operating carrier and flight number.</xs:documentation>
		</xs:annotation>
		<xs:simpleContent>
			<xs:extension base="CompanyNameType">
				<xs:attribute name="FlightNumber" type="FlightNumberType" use="optional">
					<xs:annotation>
						<xs:documentation xml:lang="en">The flight number as assigned by the operating carrier.</xs:documentation>
					</xs:annotation>
				</xs:attribute>
				<xs:attribute name="ResBookDesigCode" type="UpperCaseAlphaLength1to2" use="optional">
					<xs:annotation>
						<xs:documentation xml:lang="en">The reservation booking designator of the operating carrier when different from the marketing carrier.</xs:documentation>
					</xs:annotation>
				</xs:attribute>
			</xs:extension>
		</xs:simpleContent>
	</xs:complexType>
	<xs:complexType name="OperationScheduleType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Details of an operating schedule (e.g. a golf tee time may be more expensive during peak hours v. off peak hours).</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="OperationTimes" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">A collection of OperationTimes.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="OperationTime" maxOccurs="999">
							<xs:annotation>
								<xs:documentation xml:lang="en">Provides operating times of a facility.</xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:attributeGroup ref="DOW_PatternGroup">
									<xs:annotation>
										<xs:documentation xml:lang="en">The day(s) of week to which the operation time applies.</xs:documentation>
									</xs:annotation>
								</xs:attributeGroup>
								<xs:attributeGroup ref="DateTimeSpanGroup">
									<xs:annotation>
										<xs:documentation xml:lang="en">The date span applicable to the operation.</xs:documentation>
									</xs:annotation>
								</xs:attributeGroup>
								<xs:attribute name="AdditionalOperationInfoCode" type="OTA_CodeType" use="optional">
									<xs:annotation>
										<xs:documentation xml:lang="en">Used to provide additional information regarding operation times (e.g., after hours operations, restricted times). Refer to OpenTravel Code List Additional Operation Info (OPR).</xs:documentation>
									</xs:annotation>
								</xs:attribute>
								<xs:attribute name="Frequency" type="StringLength1to64" use="optional">
									<xs:annotation>
										<xs:documentation xml:lang="en">The frequency with which this operation occurs (e.g., 'on the hour', 'on the half hour').</xs:documentation>
									</xs:annotation>
								</xs:attribute>
								<xs:attribute name="Text" type="StringLength0to64" use="optional">
									<xs:annotation>
										<xs:documentation xml:lang="en">Textual information for this period of operation.</xs:documentation>
									</xs:annotation>
								</xs:attribute>
							</xs:complexType>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
		<xs:attributeGroup ref="DateTimeSpanGroup">
			<xs:annotation>
				<xs:documentation xml:lang="en">The date span applicable to the operation schedule.</xs:documentation>
			</xs:annotation>
		</xs:attributeGroup>
	</xs:complexType>
	<xs:complexType name="OperationSchedulesType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Collection of operation schedules.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="OperationSchedule" type="OperationScheduleType" maxOccurs="999">
				<xs:annotation>
					<xs:documentation xml:lang="en">The OperationSchedule class defines the dates and hours of operation.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
		<xs:attributeGroup ref="DateTimeSpanGroup">
			<xs:annotation>
				<xs:documentation xml:lang="en">The date range for which the operation schedule information is valid.</xs:documentation>
			</xs:annotation>
		</xs:attributeGroup>
	</xs:complexType>
	<xs:complexType name="OperationSchedulePlusChargeType">
		<xs:annotation>
			<xs:documentation xml:lang="en">This allows a charge to be associated with operating times (e.g. a golf tee time may be more expensive during peak hours v. off peak hours).</xs:documentation>
		</xs:annotation>
		<xs:complexContent>
			<xs:extension base="OperationScheduleType">
				<xs:sequence>
					<xs:element name="Charge" type="FeeType" minOccurs="0" maxOccurs="5">
						<xs:annotation>
							<xs:documentation xml:lang="en">Cost associated with an amenity.</xs:documentation>
						</xs:annotation>
					</xs:element>
				</xs:sequence>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:complexType name="OperationSchedulesPlusChargeType">
		<xs:annotation>
			<xs:documentation xml:lang="en">The OperationSchedule class defines the dates and hours of operation in addition the charges that may apply.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="OperationSchedule" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation xml:lang="en">The OperationSchedule class defines details the dates and hours of operation.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:complexContent>
						<xs:extension base="OperationSchedulePlusChargeType">
							<xs:attribute name="Name" type="StringLength0to128" use="optional">
								<xs:annotation>
									<xs:documentation xml:lang="en">Used to provide a name for a sub-operation (e.g. an activity or event).</xs:documentation>
								</xs:annotation>
							</xs:attribute>
						</xs:extension>
					</xs:complexContent>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="OrdersType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Provides the details of one or more orders.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="Order" minOccurs="0" maxOccurs="99">
				<xs:annotation>
					<xs:documentation xml:lang="en">Information pertaining to a specific order.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="Products" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">A collection of products.</xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:sequence>
									<xs:element name="Product" minOccurs="0" maxOccurs="99">
										<xs:annotation>
											<xs:documentation xml:lang="en">The details associated to a specific product.</xs:documentation>
										</xs:annotation>
										<xs:complexType>
											<xs:attribute name="ProductIssueDate" type="DateOrDateTimeType" use="optional">
												<xs:annotation>
													<xs:documentation xml:lang="en">The date or date and time that the product was issued.</xs:documentation>
												</xs:annotation>
											</xs:attribute>
											<xs:attribute name="ProductID" type="StringLength1to32" use="optional">
												<xs:annotation>
													<xs:documentation xml:lang="en">An identification number associated to the specific product.</xs:documentation>
												</xs:annotation>
											</xs:attribute>
											<xs:attribute name="ProductType" type="StringLength1to64" use="optional">
												<xs:annotation>
													<xs:documentation xml:lang="en">Identifies the type of product being purchased.</xs:documentation>
												</xs:annotation>
											</xs:attribute>
											<xs:attribute name="ProductQuantity" type="xs:nonNegativeInteger" use="optional">
												<xs:annotation>
													<xs:documentation xml:lang="en">The number of the specific product being purchased.</xs:documentation>
												</xs:annotation>
											</xs:attribute>
											<xs:attribute name="ProductSerialNumber" type="StringLength1to32" use="optional">
												<xs:annotation>
													<xs:documentation xml:lang="en">The serial number of the specific product.</xs:documentation>
												</xs:annotation>
											</xs:attribute>
											<xs:attribute name="DiscountCode" type="StringLength1to16" use="optional">
												<xs:annotation>
													<xs:documentation xml:lang="en">The discount code that applies to the specific product.</xs:documentation>
												</xs:annotation>
											</xs:attribute>
											<xs:attributeGroup ref="CurrencyAmountGroup">
												<xs:annotation>
													<xs:documentation xml:lang="en">The amount related to the specific item (e.g., if the item being purchased is a gift certificate, and only one item is being purchased the full amount is applied to the gift certificate).</xs:documentation>
												</xs:annotation>
											</xs:attributeGroup>
											<xs:attribute name="Status" use="optional">
												<xs:annotation>
													<xs:documentation xml:lang="en">The status of the product. This attribute would primarily be used in the response message to identify the status of the item being purchased.</xs:documentation>
												</xs:annotation>
												<xs:simpleType>
													<xs:restriction base="xs:NMTOKEN">
														<xs:enumeration value="OrderPending">
															<xs:annotation>
																<xs:documentation xml:lang="en">The order has been submitted, but has not yet been confirmed.</xs:documentation>
															</xs:annotation>
														</xs:enumeration>
														<xs:enumeration value="BackOrder">
															<xs:annotation>
																<xs:documentation xml:lang="en">The product being purchased is on back order.</xs:documentation>
															</xs:annotation>
														</xs:enumeration>
														<xs:enumeration value="Unavailable">
															<xs:annotation>
																<xs:documentation xml:lang="en">The product being purchased is unavailable.</xs:documentation>
															</xs:annotation>
														</xs:enumeration>
														<xs:enumeration value="Confirmed">
															<xs:annotation>
																<xs:documentation xml:lang="en">The order has been confirmed.</xs:documentation>
															</xs:annotation>
														</xs:enumeration>
													</xs:restriction>
												</xs:simpleType>
											</xs:attribute>
											<xs:attribute name="ListOfRecipientRPH" type="ListOfRPH" use="optional">
												<xs:annotation>
													<xs:documentation xml:lang="en">The recipient(s) to whom the product pertains.</xs:documentation>
												</xs:annotation>
											</xs:attribute>
										</xs:complexType>
									</xs:element>
								</xs:sequence>
							</xs:complexType>
						</xs:element>
					</xs:sequence>
					<xs:attribute name="OrderType" type="StringLength1to16" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">Identifies the type of order.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="OrderID" type="StringLength1to32" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">The identification number associated to the orders.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="ListOfRecipientRPH" type="ListOfRPH" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">The recipient(s) to whom the order pertains.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="OrderType" type="StringLength1to16" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Identifies the type of orders.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="DiscountCode" type="StringLength1to16" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">A discount code that applies to the orders.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="VendorPurchaseOrderID" type="StringLength1to32" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The purchase order number of a sales intermediary.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="OrderID" type="StringLength1to32" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The identification number associated to the orders.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:complexType>
	<xs:complexType name="OriginDestinationInformationType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Origin and Destination location, and time information for the request. Also includes the ability to specify a connection location for the search.</xs:documentation>
		</xs:annotation>
		<xs:complexContent>
			<xs:extension base="TravelDateTimeType">
				<xs:sequence>
					<xs:element name="OriginLocation">
						<xs:annotation>
							<xs:documentation xml:lang="en">Travel Origin Location - for example, air uses the IATA 3 letter code.</xs:documentation>
						</xs:annotation>
						<xs:complexType>
							<xs:simpleContent>
								<xs:extension base="LocationType">
									<xs:attribute name="MultiAirportCityInd" type="xs:boolean" use="optional">
										<xs:annotation>
											<xs:documentation xml:lang="en">If true, other airports within this city may be considered (i.e., EWR, JFK when origin location is LGA.)</xs:documentation>
										</xs:annotation>
									</xs:attribute>
									<xs:attribute name="AlternateLocationInd" type="xs:boolean" use="optional">
										<xs:annotation>
											<xs:documentation xml:lang="en">If true, alternate locations may be considered.</xs:documentation>
										</xs:annotation>
									</xs:attribute>
								</xs:extension>
							</xs:simpleContent>
						</xs:complexType>
					</xs:element>
					<xs:element name="DestinationLocation">
						<xs:annotation>
							<xs:documentation xml:lang="en">Travel Destination Location - for example, air uses the IATA 3 letter code.</xs:documentation>
						</xs:annotation>
						<xs:complexType>
							<xs:simpleContent>
								<xs:extension base="LocationType">
									<xs:attribute name="MultiAirportCityInd" type="xs:boolean" use="optional">
										<xs:annotation>
											<xs:documentation xml:lang="en">If true, other airports within this city may be considered (i.e., EWR, JFK when origin location is LGA.)</xs:documentation>
										</xs:annotation>
									</xs:attribute>
									<xs:attribute name="AlternateLocationInd" type="xs:boolean" use="optional">
										<xs:annotation>
											<xs:documentation xml:lang="en">If true, alternate locations may be considered.</xs:documentation>
										</xs:annotation>
									</xs:attribute>
								</xs:extension>
							</xs:simpleContent>
						</xs:complexType>
					</xs:element>
					<xs:element name="ConnectionLocations" type="ConnectionType" minOccurs="0">
						<xs:annotation>
							<xs:documentation xml:lang="en">Travel Connection Location - for example, air uses the IATA 3 letter code.</xs:documentation>
						</xs:annotation>
					</xs:element>
				</xs:sequence>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:complexType name="POS_Type">
		<xs:annotation>
			<xs:documentation xml:lang="en">Point of Sale (POS) identifies the party or connection channel making the request.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="Source" type="SourceType" maxOccurs="10">
				<xs:annotation>
					<xs:documentation xml:lang="en">This holds the details about the requestor. It may be repeated to also accommodate the delivery systems.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="ParagraphType">
		<xs:annotation>
			<xs:documentation xml:lang="en">An indication of a new paragraph for a sub-section of a formatted text message.</xs:documentation>
		</xs:annotation>
		<xs:choice minOccurs="0" maxOccurs="unbounded">
			<xs:element name="Text" type="FormattedTextTextType">
				<xs:annotation>
					<xs:documentation xml:lang="en">Formatted text content.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Image" type="xs:string">
				<xs:annotation>
					<xs:documentation xml:lang="en">An image for this paragraph.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="URL" type="xs:anyURI">
				<xs:annotation>
					<xs:documentation xml:lang="en">A URL for this paragraph.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="ListItem">
				<xs:annotation>
					<xs:documentation xml:lang="en">Formatted text content and an associated item or sequence number.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:simpleContent>
						<xs:extension base="FormattedTextTextType">
							<xs:attribute name="ListItem" type="xs:integer" use="optional">
								<xs:annotation>
									<xs:documentation xml:lang="en">The item or sequence number.</xs:documentation>
								</xs:annotation>
							</xs:attribute>
						</xs:extension>
					</xs:simpleContent>
				</xs:complexType>
			</xs:element>
		</xs:choice>
		<xs:attribute name="Name" type="StringLength1to64" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">In many cases the description repeats, this will allow you to define the information that is being sent, typically used when multiple occurrences of ParagraphType are being sent.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="ParagraphNumber" type="xs:nonNegativeInteger" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The sequence number for the paragraph.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attributeGroup ref="DateTimeStampGroup">
			<xs:annotation>
				<xs:documentation xml:lang="en">Creation date time, Creator ID, last modification date time and last modifier ID for the paragraph.</xs:documentation>
			</xs:annotation>
		</xs:attributeGroup>
		<xs:attributeGroup ref="LanguageGroup">
			<xs:annotation>
				<xs:documentation xml:lang="en">The language for this paragraph.</xs:documentation>
			</xs:annotation>
		</xs:attributeGroup>
	</xs:complexType>
	<xs:complexType name="PaymentCardType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Identification about a specific credit card.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="CardHolderName" type="StringLength1to64" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Name of the card holder.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="CardIssuerName" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Issuer of the card.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:attributeGroup ref="IssuerNameGroup">
						<xs:annotation>
							<xs:documentation xml:lang="en">Name of bank or organization issuing the card (e.g., alumni association, bank, fraternal organization).</xs:documentation>
						</xs:annotation>
					</xs:attributeGroup>
				</xs:complexType>
			</xs:element>
			<xs:element name="Address" type="AddressType" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Card holder's address used for additional authorization checks.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Telephone" minOccurs="0" maxOccurs="5">
				<xs:annotation>
					<xs:documentation>Card holder's telephone number used for additional authorization checks.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:attributeGroup ref="TelephoneInfoGroup">
						<xs:annotation>
							<xs:documentation xml:lang="en">Used to provide phone numbers for a card holder.</xs:documentation>
						</xs:annotation>
					</xs:attributeGroup>
				</xs:complexType>
			</xs:element>
			<xs:element name="Email" type="EmailType" minOccurs="0" maxOccurs="3">
				<xs:annotation>
					<xs:documentation>Card holder's email address(es) used for additional authorization checks.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="CustLoyalty" minOccurs="0" maxOccurs="5">
				<xs:annotation>
					<xs:documentation>Customer loyalty information used for additional authorization checks.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:attributeGroup ref="CustomerLoyaltyGroup">
						<xs:annotation>
							<xs:documentation xml:lang="en">Customer loyalty information.</xs:documentation>
						</xs:annotation>
					</xs:attributeGroup>
				</xs:complexType>
			</xs:element>
			<xs:element name="SignatureOnFile" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Indicates if the signature is authorized as a form of guarantee. In some countries, the customer can sign the payment card slip as a form of guarantee.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:attribute name="SignatureOnFileIndicator" type="xs:boolean" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">When true, indicates a signature has been obtained.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attributeGroup ref="EffectiveExpireOptionalDateGroup">
						<xs:annotation>
							<xs:documentation xml:lang="en">Provides the date range for which the signature is accepted for a guarantee.</xs:documentation>
						</xs:annotation>
					</xs:attributeGroup>
				</xs:complexType>
			</xs:element>
			<xs:element name="MagneticStripe" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Card Magnetic Stripe Data as defined by ISO 7813 for banking cards.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:attribute name="Track1" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">The binary magnetic stripe data for track 1.</xs:documentation>
						</xs:annotation>
						<xs:simpleType>
							<xs:restriction base="xs:base64Binary">
								<xs:minLength value="0"/>
								<xs:maxLength value="108"/>
							</xs:restriction>
						</xs:simpleType>
					</xs:attribute>
					<xs:attribute name="Track2" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">The binary magnetic stripe data for track 2.</xs:documentation>
						</xs:annotation>
						<xs:simpleType>
							<xs:restriction base="xs:base64Binary">
								<xs:minLength value="0"/>
								<xs:maxLength value="56"/>
							</xs:restriction>
						</xs:simpleType>
					</xs:attribute>
					<xs:attribute name="Track3" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">The binary magnetic stripe data for track 3.</xs:documentation>
						</xs:annotation>
						<xs:simpleType>
							<xs:restriction base="xs:base64Binary">
								<xs:minLength value="0"/>
								<xs:maxLength value="144"/>
							</xs:restriction>
						</xs:simpleType>
					</xs:attribute>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
		<xs:attributeGroup ref="PrivacyGroup">
			<xs:annotation>
				<xs:documentation xml:lang="en">Allows for control of the sharing of payment card data between parties.</xs:documentation>
			</xs:annotation>
		</xs:attributeGroup>
		<xs:attribute name="CardType" type="OTA_CodeType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Indicates the type of magnetic striped card. Refer to OpenTravel Code List Card Type (CDT).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="CardCode" type="PaymentCardCodeType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The 2 character code of the credit card issuer.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="CardNumber" type="NumericStringLength1to19" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Credit card number embossed on the card.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="SeriesCode" type="NumericStringLength1to8" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Verification digits printed on the card following the embossed number. This may also accommodate the customer identification/batch number (CID), card verification value (CVV2 ), card validation code number (CVC2) on credit card.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attributeGroup ref="PaymentCardDateGroup">
			<xs:annotation>
				<xs:documentation xml:lang="en">Date the card becomes valid for use (optional) and the date the card expires (required) in ISO 8601 prescribed format.</xs:documentation>
			</xs:annotation>
		</xs:attributeGroup>
		<xs:attribute name="MaskedCardNumber" type="AlphaNumericStringLength1to19" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">May be used to send a concealed credit card number (e.g., xxxxxxxxxxxx9922).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="CardHolderRPH" type="RPH_Type" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Provides a reference pointer that links the payment card to the payment card holder.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="ExtendPaymentIndicator" type="xs:boolean" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">When true, the credit card company is requested to delay the date on which the amount of this transaction is applied to the customer's account.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="CountryOfIssue" type="ISO3166" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Code for the country where the credit card was issued.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="ExtendedPaymentQuantity" type="Numeric1to999" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">A code used to specifiy the installment payment plan or number of payment installments.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="SignatureOnFileIndicator" type="xs:boolean" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">When true, the cardholder signature is on file.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="CompanyCardReference" type="AlphaNumericStringLength1to19" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Reference to the company sponsor for this particular card (e.g. a Universal Airline Travel Plan (UATP) card or any loyalty scheme sponsored card).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="Remark" type="StringLength1to128" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">A remark associated with this payment card.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="EncryptionKey" type="xs:string" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Database key necessary to retrieve the full credit card (compliant with PCI DSS standards).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:complexType>
	<xs:complexType name="PaymentDetailType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Details of payment.</xs:documentation>
		</xs:annotation>
		<xs:complexContent>
			<xs:extension base="PaymentFormType">
				<xs:sequence>
					<xs:element name="PaymentAmount" minOccurs="0" maxOccurs="2">
						<xs:annotation>
							<xs:documentation>Provides the monetary amount due for payment as quoted. A second instance could show the actual paid amount in a different currency.</xs:documentation>
						</xs:annotation>
						<xs:complexType>
							<xs:attributeGroup ref="CurrencyAmountGroup">
								<xs:annotation>
									<xs:documentation xml:lang="en">The currency amount of the payment.</xs:documentation>
								</xs:annotation>
							</xs:attributeGroup>
							<xs:attribute name="ApprovalCode" type="StringLength1to16" use="optional">
								<xs:annotation>
									<xs:documentation xml:lang="en">The approval code returned as part of an authorization process.</xs:documentation>
								</xs:annotation>
							</xs:attribute>
							<xs:attribute name="RefundCalcMethod" use="optional">
								<xs:annotation>
									<xs:documentation xml:lang="en">Specifies the method of how the refund was calculated.</xs:documentation>
								</xs:annotation>
								<xs:simpleType>
									<xs:restriction base="xs:NMTOKEN">
										<xs:enumeration value="System">
											<xs:annotation>
												<xs:documentation xml:lang="en">Refund was calculated by a system.</xs:documentation>
											</xs:annotation>
										</xs:enumeration>
										<xs:enumeration value="Manual">
											<xs:annotation>
												<xs:documentation xml:lang="en">Refund was calculated manually.</xs:documentation>
											</xs:annotation>
										</xs:enumeration>
									</xs:restriction>
								</xs:simpleType>
							</xs:attribute>
						</xs:complexType>
					</xs:element>
					<xs:element name="Commission" type="CommissionType" minOccurs="0">
						<xs:annotation>
							<xs:documentation>Used to specify the commission details when paid to a third or internal  party.</xs:documentation>
						</xs:annotation>
					</xs:element>
				</xs:sequence>
				<xs:attribute name="PaymentType" type="OTA_CodeType" use="optional">
					<xs:annotation>
						<xs:documentation xml:lang="en">Used to specify the form of payment. Refer to OpenTravel Code List Payment Type (PMT).</xs:documentation>
					</xs:annotation>
				</xs:attribute>
				<xs:attribute name="SplitPaymentInd" type="xs:boolean" use="optional">
					<xs:annotation>
						<xs:documentation xml:lang="en">When true, indicates that more than one form of payment will be used.</xs:documentation>
					</xs:annotation>
				</xs:attribute>
				<xs:attribute name="AuthorizedDays" type="Numeric1to999" use="optional">
					<xs:annotation>
						<xs:documentation xml:lang="en">Number of days being charged to this payment method.</xs:documentation>
					</xs:annotation>
				</xs:attribute>
				<xs:attribute name="PrimaryPaymentInd" type="xs:boolean" use="optional">
					<xs:annotation>
						<xs:documentation xml:lang="en">When true, indicates this is the primary method of payment.</xs:documentation>
					</xs:annotation>
				</xs:attribute>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:complexType name="PaymentFormType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Ways of providing funds and guarantees for travel by the individual.</xs:documentation>
		</xs:annotation>
		<xs:choice minOccurs="0">
			<xs:element name="PaymentCard" type="PaymentCardType">
				<xs:annotation>
					<xs:documentation xml:lang="en">Details of a debit or credit card.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="BankAcct" type="BankAcctType">
				<xs:annotation>
					<xs:documentation xml:lang="en">Details of a bank account.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="DirectBill" type="DirectBillType">
				<xs:annotation>
					<xs:documentation xml:lang="en">Details of a direct billing arrangement.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Voucher">
				<xs:annotation>
					<xs:documentation xml:lang="en">Details of a paper or electronic document indicating prepayment.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:attributeGroup ref="VoucherGroup">
						<xs:annotation>
							<xs:documentation xml:lang="en">The effective date, expiry date and series code of the voucher.</xs:documentation>
						</xs:annotation>
					</xs:attributeGroup>
					<xs:attribute name="BillingNumber" type="StringLength1to64" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">Reference of the billing account which handles the payment transaction.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="SupplierIdentifier" type="StringLength1to64" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">Unique identifier of the electronic voucher, created by the supplier.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="Identifier" type="StringLength1to64" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">Unique identifier of the electronic voucher.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="ValueType" type="StringLength1to32" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">Defines the type of voucher (e.g., full credit or partial credit).</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="ElectronicIndicator" type="xs:boolean" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">When true, indicates the voucher is electronic. An E-voucher is a value document issued by the Travel Agent for the customer. The e-voucher can be used as a proof of reservation, but more normally, as a full-payment or partial payment.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
				</xs:complexType>
			</xs:element>
			<xs:element name="LoyaltyRedemption">
				<xs:annotation>
					<xs:documentation xml:lang="en">Details of a loyalty redemption arrangement. This is normally miles or points.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="LoyaltyCertificate" minOccurs="0" maxOccurs="9">
							<xs:annotation>
								<xs:documentation xml:lang="en">A certificate may be needed in order to redeem miles or points. Certificates may be used in combination with each other as part of a reservation.</xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:attributeGroup ref="LoyaltyCertificateGroup">
									<xs:annotation>
										<xs:documentation xml:lang="en">Identifies the loyalty scheme, programs and promotions within the scheme, membership reference, format of the certificate, the number of nights it can be used and its current status.</xs:documentation>
									</xs:annotation>
								</xs:attributeGroup>
							</xs:complexType>
						</xs:element>
					</xs:sequence>
					<xs:attributeGroup ref="LoyaltyCertificateNumberGroup">
						<xs:annotation>
							<xs:documentation xml:lang="en">Identifies the Loyalty scheme, programs and promotions within the scheme, membership reference, form factor used by the certificate, the number of nights it can be used for and its current status.</xs:documentation>
						</xs:annotation>
					</xs:attributeGroup>
					<xs:attributeGroup ref="PromotionCodeGroup">
						<xs:annotation>
							<xs:documentation xml:lang="en">Used to provide a promotion code of the loyalty redemption.</xs:documentation>
						</xs:annotation>
					</xs:attributeGroup>
					<xs:attribute name="RedemptionQuantity" type="xs:positiveInteger" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">The quantity of loyalty units being redeemed.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
				</xs:complexType>
			</xs:element>
			<xs:element name="MiscChargeOrder">
				<xs:annotation>
					<xs:documentation xml:lang="en">Details of a miscellaneous charge order (MCO).</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:attribute name="TicketNumber" type="StringLength1to32" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">The ticket number of the miscellaneous charge order (MCO).</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attributeGroup ref="OriginalIssueAttributes">
						<xs:annotation>
							<xs:documentation xml:lang="en">Provides information about the original document on which the reissue is based</xs:documentation>
						</xs:annotation>
					</xs:attributeGroup>
					<xs:attribute name="PaperMCO_ExistInd" type="xs:boolean" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">Indicates if a paper or electronic MCO exists.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
				</xs:complexType>
			</xs:element>
			<xs:element name="Ticket">
				<xs:annotation>
					<xs:documentation xml:lang="en">Details of a ticket to be exchanged.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="ConjunctionTicketNbr" minOccurs="0" maxOccurs="16">
							<xs:annotation>
								<xs:documentation xml:lang="en">Conjunction ticket number in case a conjunction ticket is exchanged.</xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:simpleContent>
									<xs:extension base="StringLength1to32">
										<xs:attribute name="Coupons" type="ListOfRPH">
											<xs:annotation>
												<xs:documentation xml:lang="en">Gives the coupon numbers of the ticket, which will be taken for payment.</xs:documentation>
											</xs:annotation>
										</xs:attribute>
									</xs:extension>
								</xs:simpleContent>
							</xs:complexType>
						</xs:element>
					</xs:sequence>
					<xs:attribute name="TicketNumber" type="StringLength1to32">
						<xs:annotation>
							<xs:documentation xml:lang="en">The ticket number which is exchanged.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attributeGroup ref="OriginalIssueAttributes"/>
					<xs:attribute name="ReroutingType" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">Indicates if the rerouting, which made the exchange necessary was voluntary or involuntary.</xs:documentation>
						</xs:annotation>
						<xs:simpleType>
							<xs:restriction base="xs:NMTOKEN">
								<xs:enumeration value="voluntary"/>
								<xs:enumeration value="involuntary"/>
							</xs:restriction>
						</xs:simpleType>
					</xs:attribute>
					<xs:attribute name="ReasonForReroute" type="StringLength1to64" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">Gives more information about the rerouting.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
				</xs:complexType>
			</xs:element>
			<xs:element name="Cash">
				<xs:annotation>
					<xs:documentation xml:lang="en">Used to indicate payment in cash.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:attribute name="CashIndicator" type="xs:boolean" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">If true, this indicates cash is being used.</xs:documentation>
							<xs:documentation xml:lang="en">
								<LegacyDefaultValue>true</LegacyDefaultValue>
							</xs:documentation>
						</xs:annotation>
					</xs:attribute>
				</xs:complexType>
			</xs:element>
		</xs:choice>
		<xs:attributeGroup ref="PrivacyGroup">
			<xs:annotation>
				<xs:documentation xml:lang="en">Allows for control of the sharing of payment form data between parties.</xs:documentation>
			</xs:annotation>
		</xs:attributeGroup>
		<xs:attribute name="CostCenterID" type="StringLength1to32" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">A reference to identify the billing department for allocating cost of travel to company account.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="RPH" type="RPH_Type" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Provides a reference to a specific form of payment.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="PaymentTransactionTypeCode" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">This is used to indicate either a charge, reserve (deposit) or refund.</xs:documentation>
			</xs:annotation>
			<xs:simpleType>
				<xs:restriction base="xs:NMTOKEN">
					<xs:enumeration value="charge">
						<xs:annotation>
							<xs:documentation xml:lang="en">This indicates that an actual payment has been made.</xs:documentation>
						</xs:annotation>
					</xs:enumeration>
					<xs:enumeration value="reserve">
						<xs:annotation>
							<xs:documentation xml:lang="en">This indicates that a hold for the indicated amount has been placed on a credit card or that a cash amount has been taken from the customer to guarantee final payment.</xs:documentation>
						</xs:annotation>
					</xs:enumeration>
					<xs:enumeration value="refund">
						<xs:annotation>
							<xs:documentation xml:lang="en">This indicates that the payment amount of this PaymentDetail element is for a refund.</xs:documentation>
						</xs:annotation>
					</xs:enumeration>
				</xs:restriction>
			</xs:simpleType>
		</xs:attribute>
		<xs:attribute name="GuaranteeIndicator" type="xs:boolean" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">When true, indicates this represents a guarantee rather than a payment form.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="GuaranteeTypeCode" type="OTA_CodeType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Used to specify the method of guarantee. Refer to OpenTravel Code List Payment Type (PMT).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="GuaranteeID" type="StringLength1to64" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Provides the identifier as specified by the GuaranteeTypeCode (e.g., Corporate ID or IATA number).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="Remark" type="StringLength1to128" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">A remark associated with the payment form.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:complexType>
	<xs:complexType name="PaymentRulesType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Collection of payment rules.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="PaymentRule" type="MonetaryRuleType" maxOccurs="9">
				<xs:annotation>
					<xs:documentation xml:lang="en">One specific payment rule associated with this reservation. For example, a date by which a deposit must be received.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="PersonNameType">
		<xs:annotation>
			<xs:documentation xml:lang="en">This provides name information for a person.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="NamePrefix" type="StringLength1to16" minOccurs="0" maxOccurs="3">
				<xs:annotation>
					<xs:documentation xml:lang="en">Salutation of honorific (e.g. Mr., Mrs., Ms., Miss, Dr.)</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="GivenName" type="StringLength1to64" minOccurs="0" maxOccurs="5">
				<xs:annotation>
					<xs:documentation xml:lang="en">Given name, first name or names.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="MiddleName" type="StringLength1to64" minOccurs="0" maxOccurs="3">
				<xs:annotation>
					<xs:documentation xml:lang="en">The middle name of the person name.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="SurnamePrefix" type="StringLength1to16" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The surname prefix, e.g "van der", "von", "de".</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Surname" type="StringLength1to64">
				<xs:annotation>
					<xs:documentation xml:lang="en">Family name, last name. May also be used for full name if the sending system does not have the ability to separate a full name into its parts, e.g. the surname element may be used to pass the full name.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="NameSuffix" type="StringLength1to16" minOccurs="0" maxOccurs="3">
				<xs:annotation>
					<xs:documentation xml:lang="en">Hold various name suffixes and letters (e.g. Jr., Sr., III, Ret., Esq.)</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="NameTitle" type="StringLength1to16" minOccurs="0" maxOccurs="5">
				<xs:annotation>
					<xs:documentation xml:lang="en">Degree or honors (e.g., Ph.D., M.D.)</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Document" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Document  information for verification purposes and also used for additional filtering for common names.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:attribute name="DocID" type="StringLength1to32" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">Unique number assigned by authorities to document.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="DocType" type="OTA_CodeType" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">Indicates the type of document (e.g. Passport, Military ID, Drivers License, national ID, Vaccination Certificate). Refer to OpenTravel Code List Document Type (DOC).</xs:documentation>
						</xs:annotation>
					</xs:attribute>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
		<xs:attributeGroup ref="PrivacyGroup">
			<xs:annotation>
				<xs:documentation xml:lang="en">Allows for control of the sharing of person name data between parties.</xs:documentation>
			</xs:annotation>
		</xs:attributeGroup>
		<xs:attribute name="NameType" type="OTA_CodeType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Type of name of the individual, such as former, nickname, alternate or alias name. Refer to OpenTravel Code List Name Type (NAM).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:complexType>
	<xs:complexType name="RateQualifierType">
		<xs:annotation>
			<xs:documentation xml:lang="en">The RateQualifierType complex type describes fully rate information associated with a specific rate quotation, including the description of any promotions that may apply.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="PromoDesc" type="StringLength1to32" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">This may be used to provide additional information about the promotion code.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="RateComments" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Collection of rate comments.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="RateComment" maxOccurs="15">
							<xs:annotation>
								<xs:documentation xml:lang="en">This may be used to provide any additional information about rates (e.g., must return by Monday at 8 AM).</xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:simpleContent>
									<xs:extension base="FormattedTextTextType">
										<xs:attribute name="Name" type="StringLength1to64" use="optional">
											<xs:annotation>
												<xs:documentation xml:lang="en">Defines the type of rate comments (e.g., textual rule, marketing information).</xs:documentation>
											</xs:annotation>
										</xs:attribute>
									</xs:extension>
								</xs:simpleContent>
							</xs:complexType>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
		<xs:attributeGroup ref="RateQualifierCoreGroup">
			<xs:annotation>
				<xs:documentation xml:lang="en">Basic information pertaining to a rate.</xs:documentation>
			</xs:annotation>
		</xs:attributeGroup>
		<xs:attribute name="ArriveByFlight" type="xs:boolean" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Indicates if this rate is only available to those customers who are flying to the vehicle rental location.</xs:documentation>
				<xs:documentation xml:lang="en">
					<LegacyDefaultValue>false</LegacyDefaultValue>
				</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="RateAuthorizationCode" type="StringLength1to32" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The rate authorization code for this rate.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="VendorRateID" type="StringLength1to32" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en"> The identifier assigned to this rate by the vendor.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:complexType>
	<xs:complexType name="RebateType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Information about a suppliers participation in a rebate program, e.g. a VAT (value added tax) program.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="PaymentInformation" type="PaymentCardType" minOccurs="0" maxOccurs="9">
				<xs:annotation>
					<xs:documentation xml:lang="en">Information about payments eligible for a rebate program.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="ParticipationInd" type="xs:boolean" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">When true, the supplier is participating in a rebate program, e.g. a VAT (value added tax) program.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="ProgramName" type="xs:string" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The name of the rebate program.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attributeGroup ref="TripInformationGroup"/>
	</xs:complexType>
	<xs:complexType name="RecipientInfosType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Information about one or more recipients.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="RecipientInfo" maxOccurs="99">
				<xs:annotation>
					<xs:documentation xml:lang="en">Contact and/or reservation information pertaining to the recipient.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:complexContent>
						<xs:extension base="ContactPersonType">
							<xs:sequence>
								<xs:element name="ReservationID" type="UniqueID_Type" minOccurs="0" maxOccurs="2">
									<xs:annotation>
										<xs:documentation xml:lang="en">Provides the reservation number of the recipient for delivery of the product.</xs:documentation>
									</xs:annotation>
								</xs:element>
								<xs:element name="ShippingInfo" minOccurs="0">
									<xs:annotation>
										<xs:documentation xml:lang="en">Informtion pertaining to the shipment of a product to the recipient.</xs:documentation>
									</xs:annotation>
									<xs:complexType>
										<xs:attribute name="ShippingType" type="StringLength1to16" use="optional">
											<xs:annotation>
												<xs:documentation xml:lang="en">The method of shipment (e.g., air, ground, pickup).</xs:documentation>
											</xs:annotation>
										</xs:attribute>
										<xs:attribute name="ShippingCarrier" type="StringLength1to32" use="optional">
											<xs:annotation>
												<xs:documentation xml:lang="en">The shipping carrier (e.g., USPS, UPS, FedEx).</xs:documentation>
											</xs:annotation>
										</xs:attribute>
										<xs:attributeGroup ref="CurrencyAmountGroup">
											<xs:annotation>
												<xs:documentation xml:lang="en">The charges associated with shipment of the item.</xs:documentation>
											</xs:annotation>
										</xs:attributeGroup>
									</xs:complexType>
								</xs:element>
								<xs:element name="Comments" minOccurs="0">
									<xs:annotation>
										<xs:documentation xml:lang="en">A collection of comments.</xs:documentation>
									</xs:annotation>
									<xs:complexType>
										<xs:sequence>
											<xs:element name="Comment" type="ParagraphType" maxOccurs="99">
												<xs:annotation>
													<xs:documentation xml:lang="en">Comment information pertaining to the purchase. This may be used to pass a message to be printed on a note card.</xs:documentation>
												</xs:annotation>
											</xs:element>
										</xs:sequence>
									</xs:complexType>
								</xs:element>
							</xs:sequence>
						</xs:extension>
					</xs:complexContent>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="ReferencePlaceHolderType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Used to provide a reference to an object that is stored elsewhere in a collection of the same objects.</xs:documentation>
		</xs:annotation>
		<xs:attribute name="RPH" type="RPH_Type">
			<xs:annotation>
				<xs:documentation xml:lang="en">The unique reference for an object within this message.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:complexType>
	<xs:complexType name="RelatedTravelerType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Other traveler profiles associated with an specific individual.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="UniqueID" type="UniqueID_Type" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Identifies the profile of the related traveler.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="PersonName" type="PersonNameType" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Person associated with the traveler.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
		<xs:attributeGroup ref="PrivacyGroup">
			<xs:annotation>
				<xs:documentation xml:lang="en">Allows for control of the sharing of related traveler data between parties.</xs:documentation>
			</xs:annotation>
		</xs:attributeGroup>
		<xs:attribute name="Relation" type="StringLength1to32" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Indicates the type of relationship with the person in the profile, such as Spouse, Child, Family, Business Associate, Interest Group, Medical, Security, Other, etc.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attributeGroup ref="BirthDateGroup">
			<xs:annotation>
				<xs:documentation xml:lang="en">Birth date of the related traveler.</xs:documentation>
			</xs:annotation>
		</xs:attributeGroup>
	</xs:complexType>
	<xs:complexType name="RelativePositionType">
		<xs:annotation>
			<xs:documentation xml:lang="en">The RelativePosition object contains information about the direction, distance and travel time to/from a facility (hotel, car rental location, or airport) or to/from a designated location.</xs:documentation>
		</xs:annotation>
		<xs:complexContent>
			<xs:extension base="TransportationsType">
				<xs:attributeGroup ref="RelativePositionGroup">
					<xs:annotation>
						<xs:documentation xml:lang="en">Defines the position of an entity in relation to another entity (e.g. from an airport to a hotel, the relationship is dependant on use).</xs:documentation>
					</xs:annotation>
				</xs:attributeGroup>
				<xs:attribute name="Nearest" type="xs:boolean" use="optional">
					<xs:annotation>
						<xs:documentation xml:lang="en">The indicator for whether this location is nearest.</xs:documentation>
					</xs:annotation>
				</xs:attribute>
				<xs:attribute name="IndexPointCode" type="OTA_CodeType" use="optional">
					<xs:annotation>
						<xs:documentation xml:lang="en">This is the object referred to by the relative position (e.g. cross street, airport). Refer to OpenTravel Code List Index Point Code (IPC).</xs:documentation>
					</xs:annotation>
				</xs:attribute>
				<xs:attribute name="Name" type="StringLength0to64" use="optional">
					<xs:annotation>
						<xs:documentation xml:lang="en">This is used to accommodate a city name, rail station name etc. when using the indexPoint attribute.</xs:documentation>
					</xs:annotation>
				</xs:attribute>
				<xs:attribute name="PrimaryIndicator" type="xs:boolean" use="optional">
					<xs:annotation>
						<xs:documentation xml:lang="en">Indicates whether the reference point is considered the main reference point for the specific type of IndexPointCode (e.g., in Dallas, where IndexPointCode=airport Dallas/Fort Worth airport would be the primary airport even if another airport such as Love Field is closer).</xs:documentation>
					</xs:annotation>
				</xs:attribute>
				<xs:attribute name="ToFrom" use="optional">
					<xs:annotation>
						<xs:documentation xml:lang="en">Used to indicate whether the context is to a facility or from a facility.</xs:documentation>
					</xs:annotation>
					<xs:simpleType>
						<xs:restriction base="xs:NMTOKEN">
							<xs:enumeration value="ToFacility">
								<xs:annotation>
									<xs:documentation xml:lang="en">Indicates the direction is to the facility based on use (e.g., hotel, car rental location, airport).</xs:documentation>
								</xs:annotation>
							</xs:enumeration>
							<xs:enumeration value="FromFacility">
								<xs:annotation>
									<xs:documentation xml:lang="en">Indicates the direction is from the facility based on use (e.g., hotel, car rental location, airport).</xs:documentation>
								</xs:annotation>
							</xs:enumeration>
						</xs:restriction>
					</xs:simpleType>
				</xs:attribute>
				<xs:attribute name="ApproximateDistanceInd" type="xs:boolean" use="optional">
					<xs:annotation>
						<xs:documentation xml:lang="en">When true, the distance information is approximate.</xs:documentation>
					</xs:annotation>
				</xs:attribute>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:complexType name="RestaurantType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Information associated with a specific restaurant.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="MultimediaDescriptions" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Multimedia information about the restaurant.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:complexContent>
						<xs:extension base="MultimediaDescriptionsType">
							<xs:attribute name="Attire" type="StringLength1to64" use="optional">
								<xs:annotation>
									<xs:documentation xml:lang="en">Used to pass restaurant attire information.</xs:documentation>
								</xs:annotation>
							</xs:attribute>
						</xs:extension>
					</xs:complexContent>
				</xs:complexType>
			</xs:element>
			<xs:element name="RelativePosition" type="RelativePositionType" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Indicates the directions to a specific restaurant.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="OperationSchedules" type="OperationSchedulesPlusChargeType" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Collection of operating times for the restaurant including detail such as season, day of week, or a combination.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="InfoCodes" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Collection of types of restaurant.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="InfoCode" maxOccurs="unbounded">
							<xs:annotation>
								<xs:documentation xml:lang="en">Indicates the generic type of restaurant such as fast food, cafe, fine dining, etc.</xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:attribute name="Name" use="optional">
									<xs:annotation>
										<xs:documentation xml:lang="en">This name refers to an OpenTravel Code List table (e.g. RestaurantCategoryCode/InfoCode). The actual code is passed in the Code attribute.</xs:documentation>
									</xs:annotation>
									<xs:simpleType>
										<xs:restriction base="StringLength1to32">
											<xs:enumeration value="SrvcInfo">
												<xs:annotation>
													<xs:documentation xml:lang="en">Refer to OpenTravel Code List Restaurant Srvc Info (RSI).</xs:documentation>
												</xs:annotation>
											</xs:enumeration>
											<xs:enumeration value="Beverage">
												<xs:annotation>
													<xs:documentation xml:lang="en">This uses OpenTravel Code Table Beverage Code (BEV).</xs:documentation>
												</xs:annotation>
											</xs:enumeration>
											<xs:enumeration value="AvailableMealCategory">
												<xs:annotation>
													<xs:documentation xml:lang="en">This uses OpenTravel Code Table Available Meal Category Codes (AMC).</xs:documentation>
												</xs:annotation>
											</xs:enumeration>
											<xs:enumeration value="RestaurantCategory">
												<xs:annotation>
													<xs:documentation xml:lang="en">This uses OpenTravel Code Table Restaurant Category Code (RES).</xs:documentation>
												</xs:annotation>
											</xs:enumeration>
											<xs:enumeration value="RestaurantPolicy">
												<xs:annotation>
													<xs:documentation xml:lang="en">This uses OpenTravel Code Table Restaurant Policy Code (RPC).</xs:documentation>
												</xs:annotation>
											</xs:enumeration>
										</xs:restriction>
									</xs:simpleType>
								</xs:attribute>
								<xs:attribute name="Code" type="OTA_CodeType" use="optional">
									<xs:annotation>
										<xs:documentation xml:lang="en">Refer to OpenTravel Code List Restaurant Category (RES).</xs:documentation>
									</xs:annotation>
								</xs:attribute>
								<xs:attributeGroup ref="CodeInfoGroup">
									<xs:annotation>
										<xs:documentation xml:lang="en">May be used to give further detail on the code or to remove an obsolete item.</xs:documentation>
									</xs:annotation>
								</xs:attributeGroup>
							</xs:complexType>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="CuisineCodes" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Collection of cuisine types of restaurant.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="CuisineCode" maxOccurs="unbounded">
							<xs:annotation>
								<xs:documentation xml:lang="en">The code for the type of cuisine served at the restaurant.</xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:attribute name="Code" type="OTA_CodeType" use="optional">
									<xs:annotation>
										<xs:documentation xml:lang="en">Refer to OpenTravel Code List Main Cuisine Code (CUI).</xs:documentation>
									</xs:annotation>
								</xs:attribute>
								<xs:attributeGroup ref="CodeInfoGroup">
									<xs:annotation>
										<xs:documentation xml:lang="en">May be used to give further detail on the code or to remove an obsolete item.</xs:documentation>
									</xs:annotation>
								</xs:attributeGroup>
								<xs:attribute name="IsMain" type="xs:boolean" use="optional">
									<xs:annotation>
										<xs:documentation xml:lang="en">Indicates whether this cuisine code is the main cuisine offered by restaurant.</xs:documentation>
									</xs:annotation>
								</xs:attribute>
								<xs:attribute name="ExistsCode" type="OTA_CodeType" use="optional">
									<xs:annotation>
										<xs:documentation xml:lang="en">This attribute is used to explicitly define whether the Code applies. Refer to OpenTravel Code list Option Type Code (OTC). This is used in conjunction with Code.</xs:documentation>
									</xs:annotation>
								</xs:attribute>
							</xs:complexType>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="DescriptiveText" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Descriptive text that describes the restaurant.</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:minLength value="1"/>
						<xs:maxLength value="500"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="RestaurantName" type="xs:string" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The name of the restaurant at the facility.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="MaxSeatingCapacity" type="xs:nonNegativeInteger" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The total seating capacity for this restaurant.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="MaxSingleParty" type="xs:nonNegativeInteger" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The maximum number of people that can be seated as a single party in this restaurant.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="InvCode" type="xs:string" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Identification code of the restaurant service or facility for inventory and booking purposes if the service is an inventoriable item.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="OfferBreakfast" type="xs:boolean" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">If TRUE, breakfast is served.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="OfferLunch" type="xs:boolean" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">If TRUE, lunch is served.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="OfferDinner" type="xs:boolean" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">If TRUE, dinner is served.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="OfferBrunch" type="xs:boolean" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">If TRUE, brunch is served.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="ProximityCode" type="OTA_CodeType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Denotes whether a service is onsite, offsite or information is not available. Refer to OpenTravel Code Table Proximity (PRX).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attributeGroup ref="ID_OptionalGroup">
			<xs:annotation>
				<xs:documentation xml:lang="en">This may be used to uniquely identify the restaurant.</xs:documentation>
			</xs:annotation>
		</xs:attributeGroup>
		<xs:attribute name="Sort" type="xs:nonNegativeInteger" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Used to define the display order.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="ReservationReqInd" type="xs:boolean" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">If TRUE, a reservation must be made to dine in the restaurant.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:complexType>
	<xs:complexType name="SourceType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Provides information on the source of a request.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="RequestorID" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">An identifier of the entity making the request (e.g. ATA/IATA/ID number, Electronic Reservation Service Provider (ERSP), Association of British Travel Agents.(ABTA)).</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:complexContent>
						<xs:extension base="UniqueID_Type">
							<xs:attribute name="MessagePassword" type="StringLength1to16" use="optional">
								<xs:annotation>
									<xs:documentation xml:lang="en">This password provides an additional level of security that the recipient can use to validate the sending party's authority to use the message.</xs:documentation>
								</xs:annotation>
							</xs:attribute>
						</xs:extension>
					</xs:complexContent>
				</xs:complexType>
			</xs:element>
			<xs:element name="Position" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Specifies the latitude and longitude of a source.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:attributeGroup ref="PositionGroup">
						<xs:annotation>
							<xs:documentation xml:lang="en">Used to specify the geographic coordinates of the source of the request.</xs:documentation>
						</xs:annotation>
					</xs:attributeGroup>
				</xs:complexType>
			</xs:element>
			<xs:element name="BookingChannel" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Specifies the booking channel type and whether it is the primary means of connectivity of the source.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="CompanyName" type="CompanyNameType" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">Identifies the company that is associated with the booking channel.</xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
					<xs:attributeGroup ref="BookingChannelGroup">
						<xs:annotation>
							<xs:documentation xml:lang="en">Specifies the booking channel type and whether it is the primary means of connectivity of the source.</xs:documentation>
						</xs:annotation>
					</xs:attributeGroup>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="AgentSine" type="StringLength1to16" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Identifies the party within the requesting entity.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="PseudoCityCode" type="StringLength1to16" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">An identification code assigned to an office/agency by a reservation system.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="ISOCountry" type="ISO3166" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The country code of the requesting party.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="ISOCurrency" type="AlphaLength3" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The currency code in which the reservation will be ticketed.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="AgentDutyCode" type="StringLength1to16" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">An authority code assigned to a requestor.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="AirlineVendorID" type="UpperCaseAlphaNumericLength2to3" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The IATA assigned airline code.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="AirportCode" type="UpperCaseAlphaNumericLength3to5" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The IATA assigned airport code.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="FirstDepartPoint" type="StringLength3" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The point of first departure in a trip.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="ERSP_UserID" type="StringLength1to16" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Electronic Reservation Service Provider (ERSP) assigned identifier used to identify the individual using the ERSP system.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="TerminalID" type="StringLength1to32" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">This is the electronic address of the device from which information is entered.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:complexType>
	<xs:complexType name="SpecialRequestType">
		<xs:annotation>
			<xs:documentation xml:lang="en">A collection of SpecialRequest objects. The collection of all special requests associated with any part of the reservation (the reservation in its entirety, one or more guests, or one or more room stays). Which special requests belong to which part is determined by each object's SpecialRequestRPHs collection.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="SpecialRequest" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation xml:lang="en">The SpecialRequest object indicates special requests for a particular guest, service or reservation.  Each of these may be independent of any that are tied to the profile (see Profile Synchronization standard).</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:complexContent>
						<xs:extension base="ParagraphType">
							<xs:attribute name="RequestCode" type="StringLength1to16" use="optional">
								<xs:annotation>
									<xs:documentation xml:lang="en">This identifies a special request for this reservation and is typically hotel-specific.</xs:documentation>
								</xs:annotation>
							</xs:attribute>
							<xs:attribute name="CodeContext" type="StringLength1to32" use="optional">
								<xs:annotation>
									<xs:documentation xml:lang="en">Identifies the source authority for the RequestCode.</xs:documentation>
								</xs:annotation>
							</xs:attribute>
							<xs:attribute name="NumberOfUnits" type="xs:integer" use="optional">
								<xs:annotation>
									<xs:documentation xml:lang="en">Allows you to pass the number of units that the special request is for (e.g., if 4 rooms are booked you may want 3 with double/double beds and 1 with a king).</xs:documentation>
								</xs:annotation>
							</xs:attribute>
						</xs:extension>
					</xs:complexContent>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="StateProvType">
		<xs:annotation>
			<xs:documentation xml:lang="en">State, province, or region name or code needed to identify location.</xs:documentation>
		</xs:annotation>
		<xs:simpleContent>
			<xs:extension base="StringLength0to64">
				<xs:attribute name="StateCode" type="StateProvCodeType">
					<xs:annotation>
						<xs:documentation xml:lang="en">The standard code or abbreviation for the state, province, or region.</xs:documentation>
					</xs:annotation>
				</xs:attribute>
			</xs:extension>
		</xs:simpleContent>
	</xs:complexType>
	<xs:complexType name="StreetNmbrType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Street name; number on street.</xs:documentation>
		</xs:annotation>
		<xs:simpleContent>
			<xs:extension base="StringLength0to64">
				<xs:attribute name="PO_Box" type="StringLength1to16" use="optional">
					<xs:annotation>
						<xs:documentation xml:lang="en">Defines a Post Office Box number.</xs:documentation>
					</xs:annotation>
				</xs:attribute>
			</xs:extension>
		</xs:simpleContent>
	</xs:complexType>
	<xs:complexType name="SuccessType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Returning an empty element of this type indicates the successful processing of an OpenTravel message. This is used in conjunction with the Warning Type to report any warnings or business errors.</xs:documentation>
		</xs:annotation>
	</xs:complexType>
	<xs:complexType name="TPA_ExtensionsType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Allows extensions to be added to the OpenTravel specification per trading partner agreement.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:any processContents="skip" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="TaxType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Applicable tax element. This element allows for both percentages and flat amounts. If one field is used, the other should be zero since logically, taxes should be calculated in only one of the two ways.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="TaxDescription" type="ParagraphType" minOccurs="0" maxOccurs="5">
				<xs:annotation>
					<xs:documentation xml:lang="en">Text description of the taxes in a given language.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
		<xs:attributeGroup ref="FeeTaxGroup">
			<xs:annotation>
				<xs:documentation xml:lang="en">Provides details of the tax.</xs:documentation>
			</xs:annotation>
		</xs:attributeGroup>
		<xs:attributeGroup ref="EffectiveExpireOptionalDateGroup">
			<xs:annotation>
				<xs:documentation xml:lang="en">Provides the effective and expiry dates for the tax.</xs:documentation>
			</xs:annotation>
		</xs:attributeGroup>
		<xs:attributeGroup ref="ChargeUnitGroup">
			<xs:annotation>
				<xs:documentation xml:lang="en">Specifies charge information by unit (e.g., room, person, item) and frequency (e.g., daily, weekly, stay).</xs:documentation>
			</xs:annotation>
		</xs:attributeGroup>
	</xs:complexType>
	<xs:complexType name="TaxesType">
		<xs:annotation>
			<xs:documentation xml:lang="en">A collection of taxes.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="Tax" type="TaxType" minOccurs="0" maxOccurs="99">
				<xs:annotation>
					<xs:documentation xml:lang="en">An individual tax.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
		<xs:attributeGroup ref="CurrencyAmountGroup">
			<xs:annotation>
				<xs:documentation xml:lang="en">Used to provide a total of all the taxes.</xs:documentation>
			</xs:annotation>
		</xs:attributeGroup>
	</xs:complexType>
	<xs:complexType name="TextDescriptionType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Describes a text item.</xs:documentation>
		</xs:annotation>
		<xs:choice>
			<xs:element name="URL" type="xs:anyURI" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The URL for a specific text item.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Description" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation xml:lang="en">The text in a specific language.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:simpleContent>
						<xs:extension base="FormattedTextTextType">
							<xs:attribute name="ListItem" type="xs:integer" use="optional">
								<xs:annotation>
									<xs:documentation xml:lang="en">Sequence number associated with this description.</xs:documentation>
								</xs:annotation>
							</xs:attribute>
						</xs:extension>
					</xs:simpleContent>
				</xs:complexType>
			</xs:element>
		</xs:choice>
		<xs:attribute name="Category" type="OTA_CodeType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Specifies the text category. Refer to OpenTravel Code list Picture Category Code (PIC).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attributeGroup ref="MultimediaDescriptionGroup">
			<xs:annotation>
				<xs:documentation xml:lang="en">Generic information about the text multimedia item.</xs:documentation>
			</xs:annotation>
		</xs:attributeGroup>
		<xs:attribute name="Language" type="xs:language" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The language of the text item.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:complexType>
	<xs:complexType name="TextItemsType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Collection of text items.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="TextItem" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation xml:lang="en">Text description of a given category.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:complexContent>
						<xs:extension base="TextDescriptionType">
							<xs:attributeGroup ref="DateTimeStampGroup">
								<xs:annotation>
									<xs:documentation xml:lang="en">Creation and modification information for this text item.</xs:documentation>
								</xs:annotation>
							</xs:attributeGroup>
							<xs:attributeGroup ref="RemovalGroup">
								<xs:annotation>
									<xs:documentation xml:lang="en">If true, this item is obsolete and should be removed from the receiving system.</xs:documentation>
								</xs:annotation>
							</xs:attributeGroup>
							<xs:attribute name="Version" type="xs:string" use="optional">
								<xs:annotation>
									<xs:documentation xml:lang="en">The version of the text item.</xs:documentation>
								</xs:annotation>
							</xs:attribute>
						</xs:extension>
					</xs:complexContent>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="TimeDurationType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Used to specify a time period, which may additionally include a minimum and/or maximum duration.</xs:documentation>
		</xs:annotation>
		<xs:simpleContent>
			<xs:extension base="xs:duration">
				<xs:attribute name="Minimum" type="xs:duration" use="optional">
					<xs:annotation>
						<xs:documentation xml:lang="en">A minimum duration.</xs:documentation>
					</xs:annotation>
				</xs:attribute>
				<xs:attribute name="Maximum" type="xs:duration" use="optional">
					<xs:annotation>
						<xs:documentation xml:lang="en">A maximum duration.</xs:documentation>
					</xs:annotation>
				</xs:attribute>
			</xs:extension>
		</xs:simpleContent>
	</xs:complexType>
	<xs:complexType name="TimeInstantType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Specifies a time window.</xs:documentation>
		</xs:annotation>
		<xs:simpleContent>
			<xs:extension base="DateOrDateTimeType">
				<xs:attribute name="WindowBefore" type="xs:duration" use="optional">
					<xs:annotation>
						<xs:documentation xml:lang="en">A period of time that can be applied to another time resulting in an earlier range of time.</xs:documentation>
					</xs:annotation>
				</xs:attribute>
				<xs:attribute name="WindowAfter" type="xs:duration" use="optional">
					<xs:annotation>
						<xs:documentation xml:lang="en">A period of time that can be applied to another time resulting in a later range of time.</xs:documentation>
					</xs:annotation>
				</xs:attribute>
				<xs:attribute name="CrossDateAllowedIndicator" type="xs:boolean" use="optional">
					<xs:annotation>
						<xs:documentation xml:lang="en">When true the requested period may extend over the previous or following day. When false, the search period is restricted to the date specified. Normally used when the window duration is in hours.</xs:documentation>
					</xs:annotation>
				</xs:attribute>
			</xs:extension>
		</xs:simpleContent>
	</xs:complexType>
	<xs:complexType name="TotalType">
		<xs:annotation>
			<xs:documentation xml:lang="en">The total amount charged for the service including additional amounts and fees.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="Taxes" type="TaxesType" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">A collection of taxes.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="AmountBeforeTax" type="Money" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The total amount not including any associated tax  (e.g., sales tax, VAT, GST or any associated tax).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="AmountAfterTax" type="Money" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The total amount including all associated taxes  (e.g., sales tax, VAT, GST or any associated tax).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attributeGroup ref="CurrencyCodeGroup"/>
		<xs:attribute name="AdditionalFeesExcludedIndicator" type="xs:boolean" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">When true, amounts do not contain additional fees or charges.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="Type" type="OTA_CodeType" use="optional">
			<xs:annotation>
				<xs:documentation>Type of charge. Refer to OpenTravel Code List Charge Type (CHG).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="ServiceOverrideIndicator" type="xs:boolean" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">When true indicates that the service amount has been overridden.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="RateOverrideIndicator" type="xs:boolean" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">When true indicates that the rate amount has been overridden.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="AmountIncludingMarkup" type="Money" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">This amount includes markup and taxes.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:complexType>
	<xs:complexType name="TransportationsType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Used to define the types of transportation offered.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="Transportations" type="TransportationType" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Collection of directions to/from a specific location via various modes of transportation.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="TransportationType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Defines the type of transportation offered.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="Transportation" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation xml:lang="en">Detailed transportation information.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="MultimediaDescriptions" type="MultimediaDescriptionsType" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">Multimedia information about the transportation.</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="OperationSchedules" type="OperationSchedulesType" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">Collection of operation schedules for the transportation.</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="DescriptiveText" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">Descriptive text that describes the transportation.</xs:documentation>
							</xs:annotation>
							<xs:simpleType>
								<xs:restriction base="xs:string">
									<xs:minLength value="1"/>
									<xs:maxLength value="500"/>
								</xs:restriction>
							</xs:simpleType>
						</xs:element>
					</xs:sequence>
					<xs:attribute name="NotificationRequired" type="StringLength1to64" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">This would be used for information such as a shuttle needs to be requested or a reservation is required.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="TransportationCode" type="OTA_CodeType" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">The mode of transportation. Refer to OpenTravel Code List Transportation Code (TRP).</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="ChargeUnit" type="OTA_CodeType" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">Refer to OpenTravel Codelist Charge Type (CHG).</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="Included" type="xs:boolean" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">When true there is no additional charge for transportation.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attributeGroup ref="CodeInfoGroup">
						<xs:annotation>
							<xs:documentation xml:lang="en">May be used to give further detail on the code (e.g. if a trolley is chosen, the trolley name could be added here) or to remove an obsolete item.</xs:documentation>
						</xs:annotation>
					</xs:attributeGroup>
					<xs:attribute name="Description" type="StringLength0to64" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">Descriptive information about the mode of transportation.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="TypicalTravelTime" type="xs:string" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">The normal (average) travel time required to get to or from the location, measured in minutes.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attributeGroup ref="CurrencyAmountGroup">
						<xs:annotation>
							<xs:documentation xml:lang="en">The currency amount of the tranportation charge.</xs:documentation>
						</xs:annotation>
					</xs:attributeGroup>
					<xs:attribute name="ExistsCode" type="OTA_CodeType" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">This attribute is used to explicitly define whether the type of transportation applies. Refer to OpenTravel Code list Option Type Code (OTC). This is used in conjunction with TransportationCode.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attributeGroup ref="ID_OptionalGroup">
						<xs:annotation>
							<xs:documentation xml:lang="en">A unique identifying value assigned by the creating system. The ID attribute may be used to reference a primary-key value within a database or in a particular implementation.</xs:documentation>
						</xs:annotation>
					</xs:attributeGroup>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="TravelDateTimeType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Date and time of trip that allows specifying a time window before and after the given date.</xs:documentation>
		</xs:annotation>
		<xs:choice>
			<xs:element name="DepartureDateTime" type="TimeInstantType">
				<xs:annotation>
					<xs:documentation xml:lang="en">The departure date and optionally a time period that can be applied before and/or after the departure date.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="ArrivalDateTime" type="TimeInstantType">
				<xs:annotation>
					<xs:documentation xml:lang="en">The arrival date and optionally a time period that can be applied before and/or after the arrival date.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:choice>
	</xs:complexType>
	<xs:complexType name="TravelerRPHs">
		<xs:annotation>
			<xs:documentation xml:lang="en">A container to relate individual travelers to an inventory or chargeable item.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="TravelerRPH" maxOccurs="99">
				<xs:annotation>
					<xs:documentation xml:lang="en">Provides a pointer to a traveler defined elsewhere in this message.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:attribute name="RPH" type="RPH_Type" use="required">
						<xs:annotation>
							<xs:documentation xml:lang="en">A pointer to identify a traveler defined elsewhere in this message.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="URL_Type">
		<xs:annotation>
			<xs:documentation xml:lang="en">Web site address, in IETF specified format.</xs:documentation>
		</xs:annotation>
		<xs:simpleContent>
			<xs:extension base="xs:anyURI">
				<xs:attributeGroup ref="PrivacyGroup">
					<xs:annotation>
						<xs:documentation xml:lang="en">Allows for control of the sharing of URL data between parties.</xs:documentation>
					</xs:annotation>
				</xs:attributeGroup>
				<xs:attribute name="Type" type="StringLength1to16" use="optional">
					<xs:annotation>
						<xs:documentation xml:lang="en">Defines the purpose of the URL address, such as personal, business, public, etc.</xs:documentation>
					</xs:annotation>
				</xs:attribute>
				<xs:attributeGroup ref="DefaultIndGroup">
					<xs:annotation>
						<xs:documentation xml:lang="en">Indicates whether or not this is the default URL.</xs:documentation>
					</xs:annotation>
				</xs:attributeGroup>
			</xs:extension>
		</xs:simpleContent>
	</xs:complexType>
	<xs:complexType name="UniqueID_Type">
		<xs:annotation>
			<xs:documentation xml:lang="en">An identifier used to uniquely reference an object in a system (e.g. an airline reservation reference, customer profile reference, booking confirmation number, or a reference to a previous availability quote).</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="CompanyName" type="CompanyNameType" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Identifies the company that is associated with the UniqueID.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
		<xs:attributeGroup ref="UniqueID_Group"/>
	</xs:complexType>
	<xs:complexType name="VendorMessageType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Provides formatted textual information that a vendor wishes to make known. The type of information is indicated.</xs:documentation>
		</xs:annotation>
		<xs:complexContent>
			<xs:extension base="FormattedTextType">
				<xs:attribute name="InfoType" type="OTA_CodeType" use="required">
					<xs:annotation>
						<xs:documentation xml:lang="en">To define the type of information such as Description, Policy, Marketing, etc. Refer to OpenTravel Code List Information Type (INF).</xs:documentation>
					</xs:annotation>
				</xs:attribute>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:complexType name="VendorMessagesType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Collection of vendor messages.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="VendorMessage" type="VendorMessageType" maxOccurs="99">
				<xs:annotation>
					<xs:documentation xml:lang="en">A specific message associated with this vendor.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="VerificationType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Collection of data used to ensure the correct reservation is canceled or modified (e.g. in the case of a hotel reservation modification, a CustLoyalty/ MembershipID would be verified as part of the reservation that you plan to modify to ensure the correct reservation is being modified).</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="PersonName" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Detailed name information.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:complexContent>
						<xs:extension base="PersonNameType">
							<xs:attribute name="PartialName" type="xs:boolean" use="optional">
								<xs:annotation>
									<xs:documentation xml:lang="en">When true the full name is not provided.</xs:documentation>
								</xs:annotation>
							</xs:attribute>
						</xs:extension>
					</xs:complexContent>
				</xs:complexType>
			</xs:element>
			<xs:element name="Email" type="EmailType" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Information on an email address.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="TelephoneInfo" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Information on a telephone number.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:attributeGroup ref="TelephoneInfoGroup">
						<xs:annotation>
							<xs:documentation xml:lang="en">Information about a telephone number, including the actual number and its usage.</xs:documentation>
						</xs:annotation>
					</xs:attributeGroup>
				</xs:complexType>
			</xs:element>
			<xs:element name="PaymentCard" type="PaymentCardType" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Payment Card information.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="AddressInfo" type="AddressInfoType" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Detailed information on an address.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="CustLoyalty" minOccurs="0" maxOccurs="5">
				<xs:annotation>
					<xs:documentation xml:lang="en">Loyalty program information.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:attributeGroup ref="CustomerLoyaltyGroup">
						<xs:annotation>
							<xs:documentation xml:lang="en">Details of the customer loyalty membership.</xs:documentation>
						</xs:annotation>
					</xs:attributeGroup>
				</xs:complexType>
			</xs:element>
			<xs:element name="Vendor" type="CompanyNameType" minOccurs="0" maxOccurs="5">
				<xs:annotation>
					<xs:documentation xml:lang="en">Vendor or vendors associated with the reservation.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="ReservationTimeSpan" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The start and end date of the reservation.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:attributeGroup ref="DateTimeSpanGroup">
						<xs:annotation>
							<xs:documentation xml:lang="en">The start and end date of the reservation.</xs:documentation>
						</xs:annotation>
					</xs:attributeGroup>
				</xs:complexType>
			</xs:element>
			<xs:element name="AssociatedQuantity" minOccurs="0" maxOccurs="5">
				<xs:annotation>
					<xs:documentation xml:lang="en">Quantity or quantities that are associated with the reservation (e.g., number of seats, number of rooms, number of people).</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:attributeGroup ref="CodeListGroup">
						<xs:annotation>
							<xs:documentation xml:lang="en">Used to specify a quantity of an item as defined by the code.</xs:documentation>
						</xs:annotation>
					</xs:attributeGroup>
				</xs:complexType>
			</xs:element>
			<xs:element name="StartLocation" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Start location associated with the reservation.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:simpleContent>
						<xs:extension base="LocationType">
							<xs:attribute name="AssociatedDateTime" type="xs:dateTime" use="optional">
								<xs:annotation>
									<xs:documentation xml:lang="en">A date and time associated with this start location.</xs:documentation>
								</xs:annotation>
							</xs:attribute>
						</xs:extension>
					</xs:simpleContent>
				</xs:complexType>
			</xs:element>
			<xs:element name="EndLocation" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">End location associated with the reservation.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:simpleContent>
						<xs:extension base="LocationType">
							<xs:attribute name="AssociatedDateTime" type="xs:dateTime" use="optional">
								<xs:annotation>
									<xs:documentation xml:lang="en">A date and time associated with this end location.</xs:documentation>
								</xs:annotation>
							</xs:attribute>
						</xs:extension>
					</xs:simpleContent>
				</xs:complexType>
			</xs:element>
			<xs:element ref="TPA_Extensions" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="VideoDescriptionType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Describes a video item.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="VideoFormat" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation xml:lang="en">A set of video of a given category can be provided in different Format, each format will be described in this element.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:complexContent>
						<xs:extension base="VideoItemType">
							<xs:attributeGroup ref="MultimediaDescriptionGroup">
								<xs:annotation>
									<xs:documentation xml:lang="en">Multimedia information for the video file.</xs:documentation>
								</xs:annotation>
							</xs:attributeGroup>
							<xs:attributeGroup ref="ID_OptionalGroup">
								<xs:annotation>
									<xs:documentation xml:lang="en">A unique identifying value assigned by the creating system. The ID attribute may be used to reference a primary-key value within a database or in a particular implementation.</xs:documentation>
								</xs:annotation>
							</xs:attributeGroup>
						</xs:extension>
					</xs:complexContent>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="Category" type="OTA_CodeType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Specifies the video category. Refer to OpenTravel Code list Picture Category Code (PIC).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:complexType>
	<xs:complexType name="VideoItemsType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Collection of video items.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="VideoItem" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation xml:lang="en">Each video item represents a specific category.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:complexContent>
						<xs:extension base="VideoDescriptionType">
							<xs:attribute name="Language" type="xs:language" use="optional">
								<xs:annotation>
									<xs:documentation xml:lang="en">The language associated with the caption for the video.</xs:documentation>
								</xs:annotation>
							</xs:attribute>
							<xs:attribute name="Caption" type="StringLength1to128" use="optional">
								<xs:annotation>
									<xs:documentation xml:lang="en">The caption associated to a specific video category which can be provided in different languages.</xs:documentation>
								</xs:annotation>
							</xs:attribute>
							<xs:attributeGroup ref="RemovalGroup">
								<xs:annotation>
									<xs:documentation xml:lang="en">If true, this item is obsolete and should be removed from the receiving system.</xs:documentation>
								</xs:annotation>
							</xs:attributeGroup>
							<xs:attribute name="Version" type="xs:string" use="optional">
								<xs:annotation>
									<xs:documentation xml:lang="en">The version of the video item.</xs:documentation>
								</xs:annotation>
							</xs:attribute>
							<xs:attributeGroup ref="DateTimeStampGroup">
								<xs:annotation>
									<xs:documentation xml:lang="en">Creation and modification information for this video item.</xs:documentation>
								</xs:annotation>
							</xs:attributeGroup>
						</xs:extension>
					</xs:complexContent>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="VideoItemType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Details for a video of a given category.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="URL" type="xs:anyURI" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">URL of the multimedia item for a specific format.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="UnitOfMeasureCode" type="OTA_CodeType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The unit of measure associated with all the dimensions of the multimedia item. Refer to OpenTravel Code list Unit of Measure (UOM).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="Width" type="xs:positiveInteger" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The width of the video item (unit specified by unit of measure).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="Height" type="xs:positiveInteger" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The height of the video item (unit specified by unit of measure).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="BitRate" type="xs:positiveInteger" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The bit rate of the video item.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="Length" type="xs:positiveInteger" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The length of the video item (unit specified by unit of measure).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attributeGroup ref="MultimediaItemGroup">
			<xs:annotation>
				<xs:documentation xml:lang="en">Multimedia information for the video item.</xs:documentation>
			</xs:annotation>
		</xs:attributeGroup>
	</xs:complexType>
	<xs:complexType name="WarningType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Used when a message has been successfully processed to report any warnings or business errors that occurred.</xs:documentation>
		</xs:annotation>
		<xs:simpleContent>
			<xs:extension base="FreeTextType">
				<xs:attribute name="Type" type="OTA_CodeType" use="required">
					<xs:annotation>
						<xs:documentation xml:lang="en">The Warning element MUST contain the Type attribute that uses a recommended set of values to indicate the warning type. The validating XSD can expect to accept values that it has NOT been explicitly coded for and process them by using Type ="Unknown". Refer to OpenTravel Code List Error Warning Type (EWT).</xs:documentation>
					</xs:annotation>
				</xs:attribute>
				<xs:attributeGroup ref="ErrorWarningAttributeGroup">
					<xs:annotation>
						<xs:documentation xml:lang="en">Details of the warning.</xs:documentation>
					</xs:annotation>
				</xs:attributeGroup>
				<xs:attribute name="RPH" type="RPH_Type" use="optional">
					<xs:annotation>
						<xs:documentation xml:lang="en">Reference Place Holder used as an index for this warning.</xs:documentation>
					</xs:annotation>
				</xs:attribute>
			</xs:extension>
		</xs:simpleContent>
	</xs:complexType>
	<xs:complexType name="WarningsType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Collection of warnings.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="Warning" type="WarningType" maxOccurs="999">
				<xs:annotation>
					<xs:documentation xml:lang="en">Used in conjunction with the Success element to define a business error. </xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="WrittenConfInstType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Method by which confirmations should be delivered.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="SupplementalData" type="ParagraphType" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Additional data that will be sent with the confirmation. This could be used to include a map, pictures, or any other information that the reservation source wishes to include with the confirmation.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Email" type="EmailType" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">An email address to which the confirmation should be sent.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="LanguageID" type="xs:string" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The language in which the confirmation should be provided.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="AddresseeName" type="xs:string" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The name to which the confirmation should be addressed.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="Address" type="xs:string" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The mailing address to which the confirmation should be delivered.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="Telephone" type="xs:string" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The telephone number associated with the delivery address.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="ConfirmInd" type="xs:boolean" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">When true a written confirmation was requested and will be sent.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:complexType>
	<xs:element name="TPA_Extensions" type="TPA_ExtensionsType">
		<xs:annotation>
			<xs:documentation xml:lang="en">A placeholder in the schema to allow for additional elements and attributes to be included if required, per Trading Partner Agreement (TPA).</xs:documentation>
		</xs:annotation>
	</xs:element>
</xs:schema>
