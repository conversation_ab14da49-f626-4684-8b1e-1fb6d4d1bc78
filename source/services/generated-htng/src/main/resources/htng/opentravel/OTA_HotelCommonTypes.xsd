<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns="http://www.opentravel.org/OTA/2003/05" targetNamespace="http://www.opentravel.org/OTA/2003/05" elementFormDefault="qualified" version="11.001" id="OTA2003A2011A">
	<xs:include schemaLocation="OTA_Profile.xsd"/>
	<xs:include schemaLocation="OTA_HotelReservation.xsd"/>
	<xs:annotation>
		<xs:documentation xml:lang="en">All Schema files in the OpenTravel Alliance specification are made available according to the terms defined by the OpenTravel License Agreement at http://www.opentravel.org/Specifications/Default.aspx.</xs:documentation>
	</xs:annotation>
	<xs:simpleType name="AvailabilityStatusType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Identifies the availability status of an item.</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:NMTOKENS">
			<xs:enumeration value="Open">
				<xs:annotation>
					<xs:documentation xml:lang="en">Inventory is available for sale.</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="Close">
				<xs:annotation>
					<xs:documentation xml:lang="en">Inventory is not available for sale.</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="ClosedOnArrival">
				<xs:annotation>
					<xs:documentation xml:lang="en">Inventory is not available for sale to arriving guests.</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="ClosedOnArrivalOnRequest">
				<xs:annotation>
					<xs:documentation xml:lang="en">Inventory may not be available for sale to arriving guests.</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="OnRequest">
				<xs:annotation>
					<xs:documentation xml:lang="en">Inventory may be available.</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="RemoveCloseOnly">
				<xs:annotation>
					<xs:documentation xml:lang="en">Remove Close restriction while keeping other restrictions in place.</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="RatePlanEnum">
		<xs:annotation>
			<xs:documentation xml:lang="en">Identifies rate plan types.</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:NMTOKENS">
			<xs:enumeration value="Government">
				<xs:annotation>
					<xs:documentation xml:lang="en">Inventory is available for sale.</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="Negotiated">
				<xs:annotation>
					<xs:documentation xml:lang="en">Inventory is not available for sale.</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="Preferred">
				<xs:annotation>
					<xs:documentation xml:lang="en">Inventory is not available for sale to arriving guests.</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="Other_">
				<xs:annotation>
					<xs:documentation xml:lang="en">Inventory may not be available for sale to arriving guests.</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
		</xs:restriction>
	</xs:simpleType>
	<xs:attributeGroup name="AgeQualifyingGroup">
		<xs:annotation>
			<xs:documentation xml:lang="en">Base age group of occupants for which this rate is valid (ex Adult).</xs:documentation>
		</xs:annotation>
		<xs:attribute name="AgeQualifyingCode" type="OTA_CodeType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Defines an age range or age category of a guest (e.g., under 21, over 65, teen, infant). Refer to OpenTravel Code List Age Qualifying Code (AQC).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="MinAge" type="Numeric1to999" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">MinAge: The minimum age to qualify for AgeQualifyingCode.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="MaxAge" type="Numeric1to999" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Max Age: The maximum age to qualify for AgeQualifyingCode.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="AgeTimeUnit" type="TimeUnitType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">TimeUnit : Qualifier for Age.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="AgeBucket" type="StringLength1to16" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Defines the age range category or bucket a guest can be booked into.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:attributeGroup>
	<xs:attributeGroup name="BillingType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Defines charges to be billed to a master account.</xs:documentation>
		</xs:annotation>
		<xs:attribute name="BillingType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Indicates charges to be billed to the master account.</xs:documentation>
			</xs:annotation>
			<xs:simpleType>
				<xs:restriction base="xs:string">
					<xs:enumeration value="EachPaysOwn"/>
					<xs:enumeration value="SignRoomAndTax"/>
					<xs:enumeration value="SignAllCharges"/>
					<xs:enumeration value="SignRoomOnly"/>
				</xs:restriction>
			</xs:simpleType>
		</xs:attribute>
		<xs:attribute name="SignFoodAndBev" type="xs:boolean" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Food and beverage billed to master account. If false, guest pays own.</xs:documentation>
				<xs:documentation xml:lang="en">
					<LegacyDefaultValue>false</LegacyDefaultValue>
				</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:attributeGroup>
	<xs:attributeGroup name="CountCategorySummaryGroup">
		<xs:annotation>
			<xs:documentation xml:lang="en">This group contains category count data.</xs:documentation>
		</xs:annotation>
		<xs:attribute name="SummaryCount" type="xs:nonNegativeInteger" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The total count for the category in CountCategoryCode.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="CountCategoryCode" type="OTA_CodeType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The representation of a count category such as Guests : Number of guests, RoomsOcc : Rooms occupied. Refer to OpenTravel Code List Count Category Code (CNT).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:attributeGroup>
	<xs:attributeGroup name="DeliveryResponseGroup">
		<xs:annotation>
			<xs:documentation xml:lang="en">This allows for you to pass a minimum and maximum time duration in which the response can be expected (e.g. if the expected response time is 12 to 24 hours that minimum duration would be T12H and the maximum duration would be T24H, if the response time is within 24 hours, only use the maximum). This may be used in conjunction with ResponseTimeUnit.</xs:documentation>
		</xs:annotation>
		<xs:attribute name="MinimumTime" type="xs:duration" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Specifies the earliest time within which the detailed response will be communicated. Used in conjunction with MaximumTime to define a range (e.g. the 2 in "2-4 days").</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="MaximumTime" type="xs:duration" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Specifies the latest time the detailed response will be communicated. Used in conjunction with MinimumTime to define a range (e.g. the 4 in "2-4 days").</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="ResponseMethod" type="OTA_CodeType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Specifies how the detailed response will be communicated (e.g. XML, e-mail, phone call). Refer to OpenTravel Code List Distribution Type (DTB).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="ResponseTimeUnit" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">This defines the type of day or time span to which the minimum and maximum duration refers, either business day or elapsed time (e.g. if the expected response time is one business day, the ResponseTimeUnit value would equal business day and the MaximumTime attribute value would be P1D).</xs:documentation>
			</xs:annotation>
			<xs:simpleType>
				<xs:restriction base="xs:string">
					<xs:enumeration value="business day"/>
					<xs:enumeration value="elapsed time"/>
				</xs:restriction>
			</xs:simpleType>
		</xs:attribute>
	</xs:attributeGroup>
	<xs:attributeGroup name="DimensionGroup">
		<xs:annotation>
			<xs:documentation xml:lang="en">Provides dimension details for an object with which it is associated (e.g., a dance floor, riser, meeting room).</xs:documentation>
		</xs:annotation>
		<xs:attribute name="Area" type="xs:decimal" use="optional"/>
		<xs:attribute name="Height" type="xs:decimal" use="optional"/>
		<xs:attribute name="Length" type="xs:decimal" use="optional"/>
		<xs:attribute name="Width" type="xs:decimal" use="optional"/>
		<xs:attribute name="Units" type="StringLength1to16" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">This defines the unit in which the dimensions are expressed (e.g. it could be generic such as metric or imperial or specific such as inches, feet, yards, miles, millimeters, centimeters, meters, kilometers- according to usage).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="UnitOfMeasureCode" type="OTA_CodeType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The unit of measure in a code format. Refer to OpenTravel Code List Unit of Measure Code (UOM).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:attributeGroup>
	<xs:attributeGroup name="DisplayGroup">
		<xs:annotation>
			<xs:documentation xml:lang="en">Provides information on whether an item may be displayed and in what order.</xs:documentation>
		</xs:annotation>
		<xs:attribute name="RestrictedDisplayIndicator" type="xs:boolean" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">When true, used to indicate the rate should not be displayed. When false, indicates the rate may be displayed.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="Sort" type="xs:nonNegativeInteger" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Used to define the display order.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:attributeGroup>
	<xs:attributeGroup name="DropTimeOffsetGroup">
		<xs:annotation>
			<xs:documentation xml:lang="en">Defines the offset of time for a drop time to go into effect.</xs:documentation>
		</xs:annotation>
		<xs:attribute name="Time" type="xs:duration" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The amount of time before a DropTime goes into effect.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="Type" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">An enumerated type indicating when the drop time goes into effect.</xs:documentation>
				<xs:documentation xml:lang="en">Values: Before Arrival, After Booking.</xs:documentation>
			</xs:annotation>
			<xs:simpleType>
				<xs:restriction base="xs:NMTOKEN">
					<xs:enumeration value="BeforeArrival"/>
					<xs:enumeration value="AfterBooking"/>
				</xs:restriction>
			</xs:simpleType>
		</xs:attribute>
	</xs:attributeGroup>
	<xs:attributeGroup name="GuestCountGroup">
		<xs:annotation>
			<xs:documentation xml:lang="en">Defines the number of guests.</xs:documentation>
		</xs:annotation>
		<xs:attribute name="AgeQualifyingCode" type="OTA_CodeType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">A code representing a business rule that determines the charges for a guest based upon age range (e.g. Adult, Child, Senior, Child With Adult, Child Without Adult). This attribute allows for an increase in rate by occupant class. Refer to OpenTravel Code List Age Qualifying Code (AQC).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="Age" type="Numeric0to999" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Defines the age of a guest.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="Count" type="Numeric1to999" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The number of guests in one AgeQualifyingCode or Count.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="AgeBucket" type="StringLength1to16" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Defines the age range category or bucket a guest can be booked into. This is typically used in conjunction with the age qualifying code to further define the applicable age range.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:attributeGroup>
	<xs:attributeGroup name="InvBlockCodeApplyGroup">
		<xs:annotation>
			<xs:documentation xml:lang="en">Used to define whether this applies to a block.</xs:documentation>
		</xs:annotation>
		<xs:attribute name="InvBlockCodeApply" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">An enumerated type that defines whether this applies to a block or a block grouping code, or does not apply to blocks.</xs:documentation>
			</xs:annotation>
			<xs:simpleType>
				<xs:restriction base="xs:NMTOKEN">
					<xs:enumeration value="DoesNotApply"/>
					<xs:enumeration value="BlockCode"/>
					<xs:enumeration value="BlockGroupingCode"/>
				</xs:restriction>
			</xs:simpleType>
		</xs:attribute>
	</xs:attributeGroup>
	<xs:attributeGroup name="InvBlockCodeGroup">
		<xs:annotation>
			<xs:documentation xml:lang="en">The inventory block information.</xs:documentation>
		</xs:annotation>
		<xs:attribute name="Code" type="StringLength1to16">
			<xs:annotation>
				<xs:documentation xml:lang="en">Trading partner code associated with a room block.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:attributeGroup>
	<xs:attributeGroup name="InvBlockCutoffGroup">
		<xs:annotation>
			<xs:documentation xml:lang="en">Defines the absolute deadline or amount of offset time before unused block inventory is returned to general inventory.</xs:documentation>
		</xs:annotation>
		<xs:attribute name="AbsoluteCutoff" type="DateOrTimeOrDateTimeType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Defines the absolute deadline. Either this or the offset attributes may be used.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="OffsetDuration" type="xs:duration" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The units of time, e.g.: days, hours, etc., that apply to the deadline.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="OffsetCalculationMode" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">This indicates how the offset period is applied for the release back to general inventory of unbooked rooms in the inventory block .</xs:documentation>
			</xs:annotation>
			<xs:simpleType>
				<xs:restriction base="xs:NMTOKEN">
					<xs:enumeration value="BeforeBlockStartDate">
						<xs:annotation>
							<xs:documentation xml:lang="en">Rooms will be released on the date calculated by subtracting the offset duration from the block start (first arrival) date.</xs:documentation>
						</xs:annotation>
					</xs:enumeration>
					<xs:enumeration value="AfterBlockCreation">
						<xs:annotation>
							<xs:documentation xml:lang="en">The offset duration is applied from the block creation date.</xs:documentation>
						</xs:annotation>
					</xs:enumeration>
				</xs:restriction>
			</xs:simpleType>
		</xs:attribute>
	</xs:attributeGroup>
	<xs:attributeGroup name="InvBlockDatesGroup">
		<xs:annotation>
			<xs:documentation xml:lang="en">Provides date information for the inventory block.</xs:documentation>
		</xs:annotation>
		<xs:attributeGroup ref="DateTimeSpanGroup"/>
		<xs:attribute name="EndDateExtensionIndicator" type="xs:boolean" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">If true, this indicates that the block has no tangible end date. The block continues indefinitely.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attributeGroup ref="InvBlockCutoffGroup"/>
	</xs:attributeGroup>
	<xs:attributeGroup name="InvBlockGroup">
		<xs:annotation>
			<xs:documentation xml:lang="en">This is a collection of attributes that provide information about a block of inventory.</xs:documentation>
		</xs:annotation>
		<xs:attribute name="BookingStatus" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Provides information as to whether the rooms are available for booking.</xs:documentation>
			</xs:annotation>
			<xs:simpleType>
				<xs:restriction base="xs:NMTOKEN">
					<xs:enumeration value="Open">
						<xs:annotation>
							<xs:documentation xml:lang="en">Rooms are available for booking.</xs:documentation>
						</xs:annotation>
					</xs:enumeration>
					<xs:enumeration value="Closed">
						<xs:annotation>
							<xs:documentation xml:lang="en">Rooms are not available for booking.</xs:documentation>
						</xs:annotation>
					</xs:enumeration>
				</xs:restriction>
			</xs:simpleType>
		</xs:attribute>
		<xs:attribute name="InvBlockTypeCode" type="OTA_CodeType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Describes the type of inventory the block is being created for. Refer to OpenTravel Code List Inventory Block Type (IBT).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="InvBlockCode" type="StringLength1to32" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">This is the group id typically used to make a booking in a central reservation system.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="InvBlockGroupingCode" type="StringLength1to16" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">This code identifies the inventory group that contains multiple blocks. This allows for nested blocks.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="InvBlockName" type="StringLength1to32" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">This is the short name of the Inventory Block.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="InvBlockLongName" type="StringLength1to64" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">This is the full name of the Inventory Block.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="InvBlockStatusCode" type="OTA_CodeType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">This defines the status of the block. Refer to OpenTravel Code List Inventory Block Status (IBS).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="PMS_InvBlockID" type="StringLength1to16" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">This is used as a cross reference to the property management system (e.g. PMS Group Master Number).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="OpportunityID" type="StringLength1to16" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">This is the identifier (i.e. opportunity) that refers back to the same block in the requesting system (e.g. a sales system or a wholesaler).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="InvBlockCompanyID" type="StringLength1to16" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">This is used to identify the company or corporation associated with the inventory block, it is not a direct bill number.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="RestrictedBookingCodeList" type="ListOfOTA_CodeType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">This is who is allowed to book against the block.  Refer to OpenTravel Code List Unique ID Type (UIT). If this attribute is not used, there are no restrictions to the booking access.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="RestrictedViewingCodeList" type="ListOfOTA_CodeType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">This is who is allowed to view the block. Refer to OpenTravel Code List Unique ID Type (UIT). If this attribute is not used, there are no restrictions to the viewing access.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="TransactionAction" type="TransactionActionType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Used to specify the action to be taken on the block (e.g., create, book, modify, cancel).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="TransactionDetail" type="StringLength1to64" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">This provides the specific action to be taken on the block according to the TransactionAction attribute.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="QuoteID" type="StringLength1to32" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">This identifies a specific quote for an inventory block.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:attributeGroup>
	<xs:attributeGroup name="InventoryGroup">
		<xs:annotation>
			<xs:documentation xml:lang="en">Used to define  the inventory code.</xs:documentation>
		</xs:annotation>
		<xs:attribute name="InvCodeApplication" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">An enumerated type that identifies whether the InvCode is a single item which can be inventoried or a group of items which can be inventoried. Values are: Does Not Apply, Inventory Code, Inventory Grouping Code.</xs:documentation>
			</xs:annotation>
			<xs:simpleType>
				<xs:restriction base="xs:NMTOKEN">
					<xs:enumeration value="DoesNotApply"/>
					<xs:enumeration value="InvCode"/>
					<xs:enumeration value="InvGroupingCode"/>
				</xs:restriction>
			</xs:simpleType>
		</xs:attribute>
		<xs:attribute name="InvCode" type="StringLength1to16" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Code that identifies an inventory item.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="InvType" type="StringLength1to16" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">A value that indicates the type of inventory for which this request is made. If the inventory item is a room, typical values could be double, king, etc.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="InvTypeCode" type="StringLength1to16" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Specific system inventory type code. If the inventory item is a room, typical values could be room type code, e.g.: A1K, A1Q etc. Values may use the OpenTravel Code list or a code specific to the property or hotel brand.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="IsRoom" type="xs:boolean" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Simple indicator to detect if inventory is a room.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:attributeGroup>
	<xs:attributeGroup name="MapRequestedGroup">
		<xs:annotation>
			<xs:documentation xml:lang="en">Defines map requirements in the response.</xs:documentation>
		</xs:annotation>
		<xs:attribute name="MapRequired" type="xs:boolean" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">If "true", then a link to a map will be returned in the response which indicates the position of the matching hotel(s).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="MapHeight" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Map height in pixels.</xs:documentation>
			</xs:annotation>
			<xs:simpleType>
				<xs:restriction base="xs:positiveInteger">
					<xs:maxInclusive value="480"/>
				</xs:restriction>
			</xs:simpleType>
		</xs:attribute>
		<xs:attribute name="MapWidth" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Map width in pixels.</xs:documentation>
			</xs:annotation>
			<xs:simpleType>
				<xs:restriction base="xs:positiveInteger">
					<xs:maxInclusive value="640"/>
				</xs:restriction>
			</xs:simpleType>
		</xs:attribute>
	</xs:attributeGroup>
	<xs:attributeGroup name="MealsIncludedGroup">
		<xs:annotation>
			<xs:documentation xml:lang="en">Used to identify meals that are included.</xs:documentation>
		</xs:annotation>
		<xs:attribute name="Breakfast" type="xs:boolean" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">When true, indicates breakfast is included.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="Lunch" type="xs:boolean" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">When true, indicates lunch is included.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="Dinner" type="xs:boolean" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">When true, indicates dinner is included.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="MealPlanIndicator" type="xs:boolean" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">When true, a meal plan is included in this rate plan. When false, a meal plan is not included in this rate plan.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="MealPlanCodes" type="ListOfOTA_CodeType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Used to identify the types of meals included with a rate plan. Refer to OpenTravel Code List Meal Plan Type (MPT).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:attributeGroup>
	<xs:attributeGroup name="MethodInfoGroup">
		<xs:annotation>
			<xs:documentation xml:lang="en">Indicates reservation billing and booking method.</xs:documentation>
		</xs:annotation>
		<xs:attributeGroup ref="BillingType">
			<xs:annotation>
				<xs:documentation xml:lang="en">This identifies the payment rules for the inventory block rate plan.</xs:documentation>
			</xs:annotation>
		</xs:attributeGroup>
		<xs:attribute name="ReservationMethodCode" type="OTA_CodeType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">This is used to indicate the method by which reservations are to be received.  Refer to OpenTravel Code List Reservation Method Code (RMC).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:attributeGroup>
	<xs:attributeGroup name="OverWriteGroup">
		<xs:annotation>
			<xs:documentation xml:lang="en">Used to denote whether this message is used to replace all information.</xs:documentation>
		</xs:annotation>
		<xs:attribute name="Overwrite" type="xs:boolean" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Indicates whether this message is to replace all information.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:attributeGroup>
	<xs:attributeGroup name="RatePlanGroup">
		<xs:annotation>
			<xs:documentation xml:lang="en">The RatePlanCode assigned by the receiving system for the inventory item in response to a new rate plan notification. (Implementation Notes: This would only be returned when the notification is of type New and the sender is translating RatePlanCode values. On subsequent transactions for this rate plan, the sender would populate the RatePlanCode attribute with this value returned by the receiver.)</xs:documentation>
		</xs:annotation>
		<xs:attribute name="RatePlanType" type="OTA_CodeType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">An enumerated type that allows the query to specify a rate category type, and provides major categories for comparison across brands. Refer to OpenTravel Code List Rate Plan Type (RPT).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="RatePlanCode" type="StringLength1to64" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">A string value may be used to request a particular code or an ID if the guest qualifies for a specific rate, such as AARP, AAA, a corporate rate, etc., or to specify a negotiated code as a result of a negotiated rate.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="RatePlanID" type="StringLength1to64" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">A text field used to indicate a special  ID code that is associated with the rate and is essential in the reservation request in order to obtain the rate. Examples are Corporate ID.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="RatePlanQualifier" type="xs:boolean" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The Rate Plan Qualifier is used to control the sort order of RatePlans. Those Rate Plans that are not qualified will appear first, those that are qualified will appear last.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attributeGroup ref="PromotionCodeGroup"/>
		<xs:attribute name="RatePlanCategory" type="StringLength1to64" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Hotel systems often group multiple rate plans into a single category. This refers to that category that is specific to the hotel CRS/ PMS and should not be confused with a GDS rate category.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:attributeGroup>
	<xs:attributeGroup name="RatePlanCodeTypeGroup">
		<xs:annotation>
			<xs:documentation xml:lang="en">Values: Does Not Apply, Rate Plan Code, Rate Plan Grouping Code.</xs:documentation>
		</xs:annotation>
		<xs:attribute name="RatePlanCodeType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">An enumerated list that defines whether the RatePlanCodeType does not apply, applies to a rate plan code or applies to a rate plan grouping code.</xs:documentation>
			</xs:annotation>
			<xs:simpleType>
				<xs:restriction base="xs:NMTOKEN">
					<xs:enumeration value="DoesNotApply"/>
					<xs:enumeration value="RatePlanCode"/>
					<xs:enumeration value="RatePlanGroupingCode"/>
				</xs:restriction>
			</xs:simpleType>
		</xs:attribute>
	</xs:attributeGroup>
	<xs:attributeGroup name="ResponseTypeGroup">
		<xs:annotation>
			<xs:documentation xml:lang="en">Indicates the type of data to be returned, either a list of hotel records or a list of area (city/reference) codes.</xs:documentation>
		</xs:annotation>
		<xs:attribute name="ResponseType">
			<xs:annotation>
				<xs:documentation xml:lang="en">Defines whether the response is a property list or an area list.</xs:documentation>
			</xs:annotation>
			<xs:simpleType>
				<xs:restriction base="StringLength1to16">
					<xs:enumeration value="PropertyList"/>
					<xs:enumeration value="AreaList"/>
					<xs:enumeration value="PropertyRateList">
						<xs:annotation>
							<xs:documentation xml:lang="en">Provides the ability to control if rate information is to be returned within the response.</xs:documentation>
						</xs:annotation>
					</xs:enumeration>
					<xs:enumeration value="RateInfoDetails">
						<xs:annotation>
							<xs:documentation xml:lang="en">Provides the ability to request full complete rate details for a specific rate plan(s).</xs:documentation>
						</xs:annotation>
					</xs:enumeration>
					<xs:enumeration value="ProfilePrefs">
						<xs:annotation>
							<xs:documentation xml:lang="en">Indicates that the reply content reflects active profile preferences.</xs:documentation>
						</xs:annotation>
					</xs:enumeration>
				</xs:restriction>
			</xs:simpleType>
		</xs:attribute>
	</xs:attributeGroup>
	<xs:attributeGroup name="RestrictionStatusGroup">
		<xs:annotation>
			<xs:documentation xml:lang="en">Describes the status of a restriction on a room and/or rate.</xs:documentation>
		</xs:annotation>
		<xs:attribute name="Restriction" use="optional">
			<xs:simpleType>
				<xs:restriction base="xs:NMTOKENS">
					<xs:enumeration value="Master"/>
					<xs:enumeration value="Arrival"/>
					<xs:enumeration value="Departure"/>
					<xs:enumeration value="NonGuarantee"/>
					<xs:enumeration value="TravelAgent"/>
				</xs:restriction>
			</xs:simpleType>
		</xs:attribute>
		<xs:attribute name="Status" type="AvailabilityStatusType" use="optional"/>
		<xs:attribute name="SellThroughOpenIndicator" type="xs:boolean" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">When true, this indicates sell through is open and when false, this indicates sell through is closed. Typically this would be used in conjunction with the Status attribute to indicate that bookings may be allowed based on a trading partner agreement even though one or more days are closed.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:attributeGroup>
	<xs:attributeGroup name="RevenueCategorySummaryGroup">
		<xs:annotation>
			<xs:documentation xml:lang="en">This element has a revenue amount data for its revenue category, identified using OpenTravel Code List RCC, such as Room Revenue, Food and Beverage Revenue.</xs:documentation>
		</xs:annotation>
		<xs:attribute name="RevenueCategoryCode" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The representation of a revenue category. The following basic Revenue Categories are suggested: RoomRevenue: Total of room revenue at property; FoodRevenue: Total of restaurant and room service revenue at property; MeetingRevenue: Total of meeting room revenue at property; BarRevenue: Total of bar revenue at property; OtherRevenue: Total of other miscellaneous revenue at property; and TotalRevenue: Total of all revenue at property. Refer to OpenTravel Code List Revenue Category Code (RCC). This is a union type which allows you to pass either the OpenTravel Code or a string of length 1 to 16.</xs:documentation>
			</xs:annotation>
			<xs:simpleType>
				<xs:union memberTypes="OTA_CodeType StringLength1to16"/>
			</xs:simpleType>
		</xs:attribute>
		<xs:attributeGroup ref="CurrencyAmountGroup"/>
	</xs:attributeGroup>
	<xs:attributeGroup name="RoomGroup">
		<xs:annotation>
			<xs:documentation xml:lang="en">Used to define a room (eg. its location, configuration, view).</xs:documentation>
		</xs:annotation>
		<xs:attribute name="RoomType" type="StringLength1to16" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">(Formerly, RoomInventoryCode) A code value that indicates the type of room for which this request is made, e.g.: double, king, etc. Values may use the Hotel Descriptive Content table or a codes specific to the property or hotel brand.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="RoomTypeCode" type="StringLength1to16" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Specific system room type code, ex: A1K, A1Q etc.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="RoomCategory" type="OTA_CodeType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Indicates the category of the room. Typical values would be Moderate, Standard, or Deluxe. Refer to OpenTravel Code List Segment Category Code (SEG).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="RoomID" type="StringLength1to16" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">A string value representing the unique identification of a room if the request is looking for a specific room.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="Floor" type="Numeric1to999" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Floor on which the room is located.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="InvBlockCode" type="StringLength1to32" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">A code or identification number that identifies the room  stay as part of a group, package tour, or block of rooms designated in the inventory.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attributeGroup ref="PromotionCodeGroup"/>
		<xs:attribute name="RoomLocationCode" type="OTA_CodeType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Indicates the location of the room within the hotel structure. Typical values would be "Near Exit","Close to elevator", "Low Floor" or "High Floor". Refer to OpenTravel Code List Room Location Type (RLT).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="RoomViewCode" type="OTA_CodeType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Indicates the view of the room. Typical values would be "Ocean view", "Pool view" or "Garden View". Refer to OpenTravel Code List Room View Type (RVT).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="BedTypeCode" type="ListOfOTA_CodeType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Indicates the type of bed(s) found in the room. Typical values would be Double, Twin, Queen, or King. Refer to OpenTravel Code List Bed Type Code (BED).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="NonSmoking" type="xs:boolean" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Non-smoking indicator.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="Configuration" type="xs:string" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Textual description of room configuration.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="SizeMeasurement" type="xs:string" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Textual description of room dimensions.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="Quantity" type="Numeric1to999" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Defines the number of the item in question.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="Composite" type="xs:boolean" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Indicates that the room (suite) is a composite of smaller units.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="RoomClassificationCode" type="OTA_CodeType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Specifies the room classification (e.g., cabin, apartment). Refer to OpenTravel Code List Guest Room Info (GRI).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="RoomArchitectureCode" type="OTA_CodeType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Specifies the architectural style of a room. Refer to OpenTravel Code List Architectural Style Code (ARC).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="RoomGender" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Used to request or specify a gender assignment for a room. Note: Typically used by Hosteliers.</xs:documentation>
			</xs:annotation>
			<xs:simpleType>
				<xs:restriction base="xs:NMTOKEN">
					<xs:enumeration value="Male"/>
					<xs:enumeration value="Female"/>
					<xs:enumeration value="MaleAndFemale"/>
					<xs:enumeration value="Unknown"/>
				</xs:restriction>
			</xs:simpleType>
		</xs:attribute>
		<xs:attribute name="SharedRoomInd" type="xs:boolean" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">If TRUE, the room requires or has sharing available. Note: Typically used by Hosteliers.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:attributeGroup>
	<xs:attributeGroup name="SelectedLoyaltyGroup">
		<xs:annotation>
			<xs:documentation xml:lang="en">SelectedLoyalty communicates the Loyalty program and points to be credited for a specific stay. The SelectedLoyalty class originates  in the CRS Reservation Synchronization standard.</xs:documentation>
		</xs:annotation>
		<xs:attribute name="ReservationActionType" type="StringLength1to16" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Tells the status of the reservation (e.g. stay, no-show, cancelled, etc.)</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="SelectedLoyaltyRPH" type="RPH_Type" use="optional"/>
		<xs:attribute name="ProgramCode" type="StringLength1to8" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">This may be used for different loyalty awards (e.g. points and miles awarded).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="BonusCode" type="StringLength1to8" use="optional"/>
		<xs:attribute name="AccountID" type="StringLength1to16" use="optional"/>
		<xs:attribute name="PointsEarned" type="StringLength1to8" use="optional"/>
	</xs:attributeGroup>
	<xs:attributeGroup name="StatisticCodeGroup">
		<xs:annotation>
			<xs:documentation xml:lang="en">Defines the code and the category from the system providing the statistic data.</xs:documentation>
		</xs:annotation>
		<xs:attribute name="StatCode" type="StringLength1to64" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Actual code used by the system to collect the statistics (e.g. CORP, RACK if category is Market Segment).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="StatCategoryCode" type="OTA_CodeType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Category Code category of StatCode attribute (e.g. Market Segment). Refer to Code List SCC.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:attributeGroup>
	<xs:attributeGroup name="StatisticReportGroup">
		<xs:annotation>
			<xs:documentation xml:lang="en">Attribute Group to identify a Statistic Report exchanged via OTA_HotelStats or OTA_HotelStatsNotif.</xs:documentation>
		</xs:annotation>
		<xs:attribute name="FiscalDate" type="DateOrDateTimeType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Hotel fiscal date for statistics.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="ReportCode" type="StringLength1to64" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Identifies the type of statistics collected. Each ReportCode corresponds to a set of category summaries based upon a predetermined agreement.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:attributeGroup>
	<xs:attributeGroup name="StatusApplicationGroup">
		<xs:annotation>
			<xs:documentation xml:lang="en">Used to indicate to which block codes/rate plans/inventory codes a status should be applied.</xs:documentation>
		</xs:annotation>
		<xs:attributeGroup ref="DateTimeSpanGroup"/>
		<xs:attributeGroup ref="RatePlanGroup"/>
		<xs:attributeGroup ref="InventoryGroup"/>
	</xs:attributeGroup>
	<xs:complexType name="AddressesType">
		<xs:annotation>
			<xs:documentation xml:lang="en">The HotelAddress class defines the addresses at this hotel facility.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="Address" maxOccurs="unbounded">
				<xs:complexType>
					<xs:complexContent>
						<xs:extension base="AddressInfoType">
							<xs:attributeGroup ref="RemovalGroup">
								<xs:annotation>
									<xs:documentation xml:lang="en">This is used to indicate that an item is obsolete.</xs:documentation>
								</xs:annotation>
							</xs:attributeGroup>
							<xs:attributeGroup ref="ID_OptionalGroup">
								<xs:annotation>
									<xs:documentation xml:lang="en">The ID attribute in this group is a unique identifying value assigned by the creating system and may be used to reference a primary-key value within a database or in a particular implementation.</xs:documentation>
								</xs:annotation>
							</xs:attributeGroup>
						</xs:extension>
					</xs:complexContent>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="AdditionalDetailType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Used to send additional information.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="DetailDescription" type="ParagraphType" minOccurs="0"/>
		</xs:sequence>
		<xs:attribute name="Type" type="OTA_CodeType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Used to define the type of information being sent (e.g., rate description, property description, room information). Refer to OpenTravel Code List Additional Detail Type (ADT). </xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="Code" type="StringLength1to16" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Trading partner code associated to AdditionalDetailType.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attributeGroup ref="CurrencyAmountGroup"/>
	</xs:complexType>
	<xs:complexType name="AdditionalDetailsType">
		<xs:annotation>
			<xs:documentation xml:lang="en">A collection of AdditionalDetail.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="AdditionalDetail" type="AdditionalDetailType" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation xml:lang="en">Used to send additional information.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="AdditionalGuestAmountType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Charges related to additional guests.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="Amount" type="TotalType">
				<xs:annotation>
					<xs:documentation xml:lang="en">The amount charged for an additional guest.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="AddlGuestAmtDescription" type="ParagraphType" minOccurs="0" maxOccurs="9">
				<xs:annotation>
					<xs:documentation xml:lang="en">Descriptive information regarding amounts charged for additional guests.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="MaxAdditionalGuests" type="Numeric1to999" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Increase the base rate by the additional occupant amount for each additional occupant of the same age group up to this maximum number of occupants of this age group.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attributeGroup ref="AgeQualifyingGroup"/>
		<xs:attribute name="Type" type="OTA_CodeType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">A code representing the charges related to additional guests. Refer to OpenTravel Code List Additional Detail Type (ADT). Typically, the extra person information code would be used here.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="Percent" type="Percentage" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The percent charged for an additional guest.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="RPH" type="RPH_Type" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">An index code to identify an instance in a collection of like items.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:complexType>
	<xs:complexType name="AdjustmentsType">
		<xs:annotation>
			<xs:documentation xml:lang="en">AdjustmentsType.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="Adjustment" maxOccurs="unbounded">
				<xs:complexType>
					<xs:attribute name="ReservationOriginatorCode" type="StringLength1to16" use="optional"/>
					<xs:attribute name="ConfirmationID" type="StringLength1to16" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">The confirmation ID number.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="ReservationID" type="StringLength1to16" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">The reservation ID number.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="RoomInventoryCode" type="StringLength1to16" use="optional"/>
					<xs:attributeGroup ref="PromotionCodeGroup"/>
					<xs:attribute name="AdjustReason" type="StringLength1to32" use="optional"/>
					<xs:attribute name="Sequence" type="xs:positiveInteger" use="optional"/>
					<xs:attribute name="InvValue" use="optional">
						<xs:simpleType>
							<xs:restriction base="xs:integer">
								<xs:enumeration value="1"/>
								<xs:enumeration value="-1"/>
							</xs:restriction>
						</xs:simpleType>
					</xs:attribute>
					<xs:attributeGroup ref="DateTimeSpanGroup"/>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="RequestID" type="StringLength1to16" use="optional"/>
	</xs:complexType>
	<xs:complexType name="AmountLiteType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Summary version of the AmountType, initially created for the Travel Itinerary Message set.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="Base" type="TotalType">
				<xs:annotation>
					<xs:documentation xml:lang="en">The base amount charged for the accommodation or service per unit of time (ex: Nightly, Weekly, etc). If TaxInclusive is set to True, then taxes are included in the base amount. Note that any additional charges should itemized in the other elements.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
		<xs:attributeGroup ref="EffectiveExpireOptionalDateGroup"/>
		<xs:attributeGroup ref="AgeQualifyingGroup"/>
		<xs:attribute name="GuaranteedInd" type="xs:boolean" use="optional"/>
	</xs:complexType>
	<xs:complexType name="AmountPercentType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Defines the percentage basis  for calculating the fee amount or the amount.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="Taxes" type="TaxesType" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">A collection of taxes.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="TaxInclusive" type="xs:boolean" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Indicates whether taxes are included when figuring the base amount.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="FeesInclusive" type="xs:boolean" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Indicates whether fees, such as those imposed by a travel agency or other booking agent, are included when figuring cancel penalties.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="NmbrOfNights" type="xs:nonNegativeInteger" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The number of nights of the hotel stay that are used to calculate the fee amount.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="BasisType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Provides the basis for how the amount of the guarantee is calculated.</xs:documentation>
			</xs:annotation>
			<xs:simpleType>
				<xs:restriction base="StringLength1to16">
					<xs:enumeration value="FullStay">
						<xs:annotation>
							<xs:documentation xml:lang="en">Uses the full stay as the basis.</xs:documentation>
						</xs:annotation>
					</xs:enumeration>
					<xs:enumeration value="Nights">
						<xs:annotation>
							<xs:documentation xml:lang="en">Uses the nights as the basis.</xs:documentation>
						</xs:annotation>
					</xs:enumeration>
					<xs:enumeration value="FirstLast">
						<xs:annotation>
							<xs:documentation xml:lang="en">Uses the first and last night as the basis.</xs:documentation>
						</xs:annotation>
					</xs:enumeration>
				</xs:restriction>
			</xs:simpleType>
		</xs:attribute>
		<xs:attribute name="Percent" type="Percentage" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The percentage used to calculate the amount.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attributeGroup ref="CurrencyAmountGroup">
			<xs:annotation>
				<xs:documentation xml:lang="en">Provides a monetary amount and the currency code to reflect the currency in which this amount is expressed.</xs:documentation>
			</xs:annotation>
		</xs:attributeGroup>
		<xs:attribute name="ApplyAs" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Used to specify how the amount needs to be applied with respect to the stay.</xs:documentation>
			</xs:annotation>
			<xs:simpleType>
				<xs:restriction base="xs:NMTOKEN">
					<xs:enumeration value="FirstNightDeposit">
						<xs:annotation>
							<xs:documentation xml:lang="en">Amount to be applied as a deposit for the first night.</xs:documentation>
						</xs:annotation>
					</xs:enumeration>
					<xs:enumeration value="LastNightDepost">
						<xs:annotation>
							<xs:documentation xml:lang="en">Amount to be applied as a deposit for the last night.</xs:documentation>
						</xs:annotation>
					</xs:enumeration>
					<xs:enumeration value="FirstAndLastNightDeposit">
						<xs:annotation>
							<xs:documentation xml:lang="en">Amount to be applied as a deposit for the first and last nights.</xs:documentation>
						</xs:annotation>
					</xs:enumeration>
					<xs:enumeration value="FirstNightPayment">
						<xs:annotation>
							<xs:documentation xml:lang="en">Amount to be applied as payment for the first night.</xs:documentation>
						</xs:annotation>
					</xs:enumeration>
					<xs:enumeration value="LastNightPayment">
						<xs:annotation>
							<xs:documentation xml:lang="en">Amount to be applied as payment for the last night.</xs:documentation>
						</xs:annotation>
					</xs:enumeration>
					<xs:enumeration value="FirstAndLastNightPayment">
						<xs:annotation>
							<xs:documentation xml:lang="en">Amount to be applied as payment for the first and last nights.</xs:documentation>
						</xs:annotation>
					</xs:enumeration>
				</xs:restriction>
			</xs:simpleType>
		</xs:attribute>
	</xs:complexType>
	<xs:complexType name="AmountType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Base charge and additional charges related to a room that includes such things as additional guest amounts, cancel fees, etc. Also includes Discount percentages, total amount, and the rate description.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="Base" type="TotalType" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The base amount charged for the accommodation or service per unit of time (ex: Nightly, Weekly, etc). If TaxInclusive is set to True, then taxes are included in the base amount. Note that any additional charges should itemized in the other elements.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="AdditionalGuestAmounts" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">A collection of charges that apply to for additional occupants, guests or service users (over and above the rate's MaxGuest Applicable).</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="AdditionalGuestAmount" type="AdditionalGuestAmountType" maxOccurs="unbounded">
							<xs:annotation>
								<xs:documentation xml:lang="en">Collection of incremental charges per age qualifying code for additional guests. Amount charged for additional occupant is with respect to age group of the base guests.</xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
					<xs:attribute name="AmountBeforeTax" type="Money" use="optional"/>
					<xs:attribute name="AmountAfterTax" type="Money" use="optional"/>
					<xs:attributeGroup ref="CurrencyCodeGroup"/>
				</xs:complexType>
			</xs:element>
			<xs:element name="Fees" type="FeesType" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">A collection of Fees.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="CancelPolicies" type="CancelPenaltiesType" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">A collection of Cancellation Policies.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="PaymentPolicies" type="RequiredPaymentsType" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">A collection of Payment Policies.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Discount" minOccurs="0" maxOccurs="5">
				<xs:annotation>
					<xs:documentation xml:lang="en">Discount percentage and/or Amount, code and textual reason for discount</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:complexContent>
						<xs:extension base="DiscountType">
							<xs:attribute name="AppliesTo" use="optional">
								<xs:annotation>
									<xs:documentation xml:lang="en">Used to identify the monetary amount to which the discount applies.</xs:documentation>
								</xs:annotation>
								<xs:simpleType>
									<xs:restriction base="xs:NMTOKEN">
										<xs:enumeration value="Base">
											<xs:annotation>
												<xs:documentation xml:lang="en">Indicates the discount applies to the amount identified in the Base element.</xs:documentation>
											</xs:annotation>
										</xs:enumeration>
										<xs:enumeration value="AdditionalGuestAmount">
											<xs:annotation>
												<xs:documentation xml:lang="en">Indicates the discount applies to the amount identified in the AdditionalGuestAmount element.</xs:documentation>
											</xs:annotation>
										</xs:enumeration>
										<xs:enumeration value="Fee">
											<xs:annotation>
												<xs:documentation xml:lang="en">Indicates the discount applies to the amount identified in the Fee element.</xs:documentation>
											</xs:annotation>
										</xs:enumeration>
									</xs:restriction>
								</xs:simpleType>
							</xs:attribute>
							<xs:attribute name="ItemRPH" type="RPH_Type" use="optional">
								<xs:annotation>
									<xs:documentation xml:lang="en">Used to identify the specific item referred to by the AppliesTo attribute.</xs:documentation>
								</xs:annotation>
							</xs:attribute>
						</xs:extension>
					</xs:complexContent>
				</xs:complexType>
			</xs:element>
			<xs:element name="Total" type="TotalType" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The total amount charged for this rate including additional occupant amounts and fees.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="RateDescription" type="ParagraphType" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Description of the rate associated with the various monetary amounts and policies.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="AdditionalCharges" type="HotelAdditionalChargesType" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Collection of additional charges.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
		<xs:attributeGroup ref="EffectiveExpireOptionalDateGroup">
			<xs:annotation>
				<xs:documentation xml:lang="en">The effective date range for a charge. The EffectiveDate is used by Dynamic Packaging as the date the service is offered at the specified rate (used in conjunction with RateTimeUnit and UnitMultiplier attributes to denote a rate for a duration.)</xs:documentation>
			</xs:annotation>
		</xs:attributeGroup>
		<xs:attributeGroup ref="AgeQualifyingGroup"/>
		<xs:attribute name="GuaranteedInd" type="xs:boolean" use="optional"/>
		<xs:attribute name="NumberOfUnits" type="xs:integer" use="optional"/>
		<xs:attribute name="RateTimeUnit" type="TimeUnitType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Indicates the time unit for the rate.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="UnitMultiplier" type="xs:positiveInteger" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Indicates the number of rate time units such as "3 Days".</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="MinGuestApplicable" type="xs:positiveInteger" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Indicates the minimum number of guests at this rate.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="MaxGuestApplicable" type="xs:positiveInteger" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Indicates the maximum number of guests at this rate.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="MinLOS" type="xs:nonNegativeInteger" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Indicates the minimum length of stay.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="MaxLOS" type="xs:nonNegativeInteger" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Indicates the maximum length of stay.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="StayOverDate" type="DayOfWeekType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Day of week guest is required to stay over in order to be eligible for this rate.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="AlternateCurrencyInd" type="xs:boolean" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">When true, indicates the amounts are provided in an alternate currency. When false, indicates the amounts are provided in the primary currency. This may be used to indicate that the currency provided is different from the requested or stored currency.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="ChargeType" type="OTA_CodeType">
			<xs:annotation>
				<xs:documentation xml:lang="en">The type of the amount being charged, e.g. per night. Refer to OpenTravel Code List Charge Type Code (CHG).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="QuoteID" type="StringLength1to64">
			<xs:annotation>
				<xs:documentation xml:lang="en">A reference string used to match a query, with rates, to a given time. This is useful for matching prices within a given quote period.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:complexType>
	<xs:complexType name="AreasType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Defines an area determined by the hotel reservation system.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="Area" maxOccurs="99">
				<xs:annotation>
					<xs:documentation xml:lang="en">An area determined by the hotel reservation system.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="AreaDescription" type="ParagraphType" minOccurs="0"/>
					</xs:sequence>
					<xs:attributeGroup ref="AreaID_Group"/>
					<xs:attribute name="CityCode" type="StringLength1to8" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">The IATA city code; for example DCA, ORD.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="StateProvCode" type="StateProvCodeType" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">The standard code or abbreviation for the state, province, or region.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="CountryCode" type="ISO3166" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">The country in which the area is located.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="AvailStatusMessageType">
		<xs:sequence>
			<xs:element name="StatusApplicationControl" type="StatusApplicationControlType" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Information on what the  AvailStatus Message applies to (i.e. the combination of inventory and rate codes) and the period of application.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="LengthsOfStay" type="LengthsOfStayType" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Collection of Length of Stay elements. These LOS elements indicate what LOS restrictions are to be added or removed. Some systems include this information directly with the Availability Status as opposed to the booking restriction.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="BestAvailableRates" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">A collection of the best rates available.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="BestAvailableRate" maxOccurs="unbounded">
							<xs:annotation>
								<xs:documentation xml:lang="en">The details of a best available rate.</xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:attribute name="LengthOfStayTime" type="xs:integer" use="optional">
									<xs:annotation>
										<xs:documentation xml:lang="en">Used in conjunction with the TimeUnit to define the length of stay.</xs:documentation>
									</xs:annotation>
								</xs:attribute>
								<xs:attribute name="LengthOfStayTimeUnit" type="TimeUnitType" use="optional">
									<xs:annotation>
										<xs:documentation xml:lang="en">A time unit used to apply this status message to other inventory, and with more granularity than daily. Values: Year, Month, Week, Day, Hour, Minute, Second.</xs:documentation>
									</xs:annotation>
								</xs:attribute>
								<xs:attribute name="RatePlanCode" type="StringLength1to64" use="optional">
									<xs:annotation>
										<xs:documentation xml:lang="en">A string value used to specify the rate code as the best available rate.</xs:documentation>
									</xs:annotation>
								</xs:attribute>
								<xs:attributeGroup ref="CurrencyAmountGroup">
									<xs:annotation>
										<xs:documentation xml:lang="en">The amount of the best available rate. This may be used in conjuction with the RatePlanCode attribute to define its rate.</xs:documentation>
									</xs:annotation>
								</xs:attributeGroup>
								<xs:attribute name="TaxInclusive" type="xs:boolean" use="optional">
									<xs:annotation>
										<xs:documentation xml:lang="en">When true indicates tax is included. When false tax is not included.</xs:documentation>
									</xs:annotation>
								</xs:attribute>
							</xs:complexType>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="HurdleRate" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Restriction based on the minimum rate to be considered for availability, ex. can sell weekend rate only if charging the hurdle rate or more.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:attributeGroup ref="CurrencyAmountGroup"/>
				</xs:complexType>
			</xs:element>
			<xs:element name="Delta" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Incremental amount added to the hurdle rate (e.g. Amount= 20 USD and Ceiling=5, the maximum Delta would be 100 USD).</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:attributeGroup ref="CurrencyAmountGroup"/>
					<xs:attribute name="Ceiling" type="xs:positiveInteger" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">Upper limit on the number of Deltas that should be added to the Rate Hurdle for each transient reservation.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="MaxSold" type="xs:positiveInteger" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">Upper limit on the number of transient reservations that should be accepted.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
				</xs:complexType>
			</xs:element>
			<xs:element name="UniqueID" type="UniqueID_Type" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The unique identifier element allows the trading partners to  uniquely identify each AvailStatusMessage, for tracing of transactions.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="RestrictionStatus" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Availability status assigned to the room rate combination.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:attributeGroup ref="RestrictionStatusGroup"/>
					<xs:attribute name="MaxAdvancedBookingOffset" type="xs:duration" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">Maximum days before the arrival date for which this rate plan may be booked.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="MinAdvancedBookingOffset" type="xs:duration" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">Minimum days before the arrival date for which this rate plan may be booked.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="Override" type="xs:boolean" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Mechanism to allow the user to override settings at the reservation system and to allow the RMS to replace this overridden values: If value = "false", the reservations system may ignore the settings passed and keep values overridden by the user. If value = "true", the reservations system must replace values overridden by the user.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="BookingLimitMessageType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">An enumerated type defining the function of the booking limit message. Values: RemoveLimit, SetLimit, AdjustLimit.</xs:documentation>
			</xs:annotation>
			<xs:simpleType>
				<xs:restriction base="xs:string">
					<xs:enumeration value="RemoveLimit"/>
					<xs:enumeration value="SetLimit"/>
					<xs:enumeration value="AdjustLimit"/>
				</xs:restriction>
			</xs:simpleType>
		</xs:attribute>
		<xs:attribute name="BookingLimit" type="xs:nonNegativeInteger" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Number of units of inventory that can be sold.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="LocatorID" type="xs:positiveInteger" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Can be used to communicate back to the sender exactly which transaction may have had a problem (e.g. "Message 214 had an invalid date range").</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="BookingThreshold" type="xs:integer" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Number of units down to which inventory can be sold.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="RoomGender" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Used to request or specify a gender assignment for a room. Note: Typically used by Hosteliers.</xs:documentation>
			</xs:annotation>
			<xs:simpleType>
				<xs:restriction base="xs:NMTOKEN">
					<xs:enumeration value="Male"/>
					<xs:enumeration value="Female"/>
					<xs:enumeration value="MaleAndFemale"/>
					<xs:enumeration value="Unknown"/>
				</xs:restriction>
			</xs:simpleType>
		</xs:attribute>
		<xs:attribute name="SharedRoomInd" type="xs:boolean" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">If TRUE, the room requires or has sharing available. Note: Typically used by Hosteliers.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:complexType>
	<xs:complexType name="BaseInvCountType">
		<xs:annotation>
			<xs:documentation xml:lang="en">The set of changes in the number of units of base inventory for one inventory type (code) to be made on the server. The server must successfully update all messages. Failure to update any single status message will result in the failure of all messages.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="StatusApplicationControl" type="StatusApplicationControlType" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Information on what the InvCountNotif Message applies to (i.e. the combination of inventory and/or rate codes) and the period of application.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="InvCounts" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">A collection of inventory counts.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="InvCount" maxOccurs="unbounded">
							<xs:annotation>
								<xs:documentation xml:lang="en">Individual inventory count (e.g., Physical, Available, Sold, OOO, NAFS).</xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:sequence>
									<xs:element name="InvBlockCutoff" minOccurs="0">
										<xs:annotation>
											<xs:documentation xml:lang="en">Used for information regarding inventory block cutoff dates.</xs:documentation>
										</xs:annotation>
										<xs:complexType>
											<xs:attributeGroup ref="InvBlockCutoffGroup">
												<xs:annotation>
													<xs:documentation xml:lang="en">The usage here provides details regarding the inventory count cutoffs. This attribute group was reused and does not apply to blocks, even though the word "block" appears in the attribute group name.</xs:documentation>
												</xs:annotation>
											</xs:attributeGroup>
										</xs:complexType>
									</xs:element>
								</xs:sequence>
								<xs:attribute name="CountType" type="OTA_CodeType" use="optional">
									<xs:annotation>
										<xs:documentation xml:lang="en">
This identifies the type of inventory count being reported. Refer to OpenTravel Code List Inventory Count Type (INV).</xs:documentation>
									</xs:annotation>
								</xs:attribute>
								<xs:attribute name="Count" type="xs:integer" use="optional"/>
								<xs:attribute name="AdjustReason" type="StringLength1to32" use="optional">
									<xs:annotation>
										<xs:documentation xml:lang="en">Additional information as to the nature of the inventory adjustment; eg. GUEST EXTENDED STAY, or ROOM CHANGE, etc.</xs:documentation>
									</xs:annotation>
								</xs:attribute>
								<xs:attribute name="ActionType" use="optional">
									<xs:annotation>
										<xs:documentation xml:lang="en">An enumerated type.  'Adjustment' is the offset up or down to the inventoried item (day to day business); 'Used' means the Inv value is how much of this inventoried item has been used/sold at this point in time;  'Remaining' means that the Inv value shows what is left for the inventoried item at this point in time; 'Allocation' means this is how much of the inventoried item is going to be allocated to the receiver for them to pull from inventory.</xs:documentation>
									</xs:annotation>
									<xs:simpleType>
										<xs:restriction base="xs:string">
											<xs:enumeration value="Adjustment"/>
											<xs:enumeration value="Used"/>
											<xs:enumeration value="Remaining"/>
											<xs:enumeration value="Allocation"/>
										</xs:restriction>
									</xs:simpleType>
								</xs:attribute>
							</xs:complexType>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="OffSell" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Communicates additional information about inventory that is not available for sale.</xs:documentation>
					<xs:documentation xml:lang="en">Communicates information about inventory that is not available for sale.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:attribute name="OffSellValueType" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">An enumerated type that defines whether to replace or adjust current values. This value could be negative, as an adjustment could 	reduce the inventory items that are on offsell, or be used to offer an oversell. Values: Total (should replace any existing offsell value); Adjustment (should be added to any existing offsell value).</xs:documentation>
						</xs:annotation>
						<xs:simpleType>
							<xs:restriction base="xs:string">
								<xs:enumeration value="Total"/>
								<xs:enumeration value="Adjustment"/>
							</xs:restriction>
						</xs:simpleType>
					</xs:attribute>
					<xs:attribute name="OffSellValue" type="Money" use="optional"/>
				</xs:complexType>
			</xs:element>
			<xs:element name="UniqueID" type="UniqueID_Type" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The unique identifier element allows the trading partners to  uniquely identify each InvCountNotifRQ, (i.e. the entire message) for transaction traceability.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="BasicPropertyInfoType">
		<xs:annotation>
			<xs:documentation xml:lang="en">An abbreviated short summary of hotel descriptive information.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="VendorMessages" type="VendorMessagesType" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">A collection of VenderMessages.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Position" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">An element that identifies the geographic position of the hotel. The Position element uses the representation defined by ISO Standard 6709 to define a geographic point location.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:attributeGroup ref="PositionGroup"/>
					<xs:attribute name="MapURL" type="xs:anyURI" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">The URL of a map image.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
				</xs:complexType>
			</xs:element>
			<xs:element name="Address" type="AddressInfoType" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Public address of the hotel property.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="ContactNumbers" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Collection of hotel contact numbers.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="ContactNumber" minOccurs="0" maxOccurs="unbounded">
							<xs:annotation>
								<xs:documentation xml:lang="en">Contact numbers of the hotel property. Examples are telephone and fax numbers.</xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:attributeGroup ref="TelephoneInfoGroup"/>
							</xs:complexType>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="Award" minOccurs="0" maxOccurs="5">
				<xs:annotation>
					<xs:documentation xml:lang="en">An element that identifies the hotel ratings.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:attribute name="Provider" type="xs:string" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">The name of the award or ratings provider.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="Rating" type="xs:string" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">The actual award or rating received by the hotel facility.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
				</xs:complexType>
			</xs:element>
			<xs:element name="RelativePosition" type="RelativePositionType" minOccurs="0"/>
			<xs:element name="HotelAmenity" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation xml:lang="en">Hotel-level amenities (pool, etc) for searching.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:attribute name="Code" type="OTA_CodeType" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">Refer to OpenTravel Code List Hotel Amenity Code (HAC).</xs:documentation>
						</xs:annotation>
					</xs:attribute>
				</xs:complexType>
			</xs:element>
			<xs:element name="Recreation" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation xml:lang="en">Identifies recreation facilities or amenities of interest.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:attribute name="Code" type="OTA_CodeType" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">Used to identify a specific recreation activity. Refer to OpenTravel Code list Recreation Srvc Type (RST).</xs:documentation>
						</xs:annotation>
					</xs:attribute>
				</xs:complexType>
			</xs:element>
			<xs:element name="Service" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation xml:lang="en">Identifies business services of interest.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:attribute name="BusinessServiceCode" type="OTA_CodeType" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">Used to identify a specific business service. Refer to OpenTravel Code list Business Srvc Type (BUS).</xs:documentation>
						</xs:annotation>
					</xs:attribute>
				</xs:complexType>
			</xs:element>
			<xs:element name="Policy" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Policy information for this hotel.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:attribute name="CheckInTime" type="xs:time" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">The checkin time required by this hotel for a room stay.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="CheckOutTime" type="xs:time" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">The checkout time required by this hotel for a room stay.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
		<xs:attributeGroup ref="HotelReferenceGroup"/>
		<xs:attribute name="HotelSegmentCategoryCode" type="OTA_CodeType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Identifies the segment (e.g., luxury, upscale, extended stay) of the hotel. Refer to OpenTravel Codelist Segment Category Code (SEG).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="SupplierIntegrationLevel" type="xs:nonNegativeInteger" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The level of integration of a property to provide automated transaction information. The lower the number, the higher the integration (e.g., a 1 means the supplier has the highest level of integration automation).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="MaxGroupRoomQuantity" type="xs:nonNegativeInteger" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Provides the maximum number of rooms that can be booked in a property for a group.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="CurrencyCode" type="AlphaLength3" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The code specifying a monetary unit. Use ISO 4217, three alpha code.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="MasterChainCode" type="StringLength1to8" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The code that identifies a hotel chain or management group. The hotel chain code is decided between vendors. This attribute is optional if the hotel is an independent property that can be identified by the HotelCode attribute. Use it in conjunction with the chain code to determine the actual chain code that is used between trading partners.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:complexType>
	<xs:complexType name="BookingRulesType">
		<xs:annotation>
			<xs:documentation xml:lang="en">A collection of BookingRule.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="BookingRule" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation xml:lang="en">The BookingRule element defines restrictions to rates and stays at the hotel for a given rate plan, room type or rate plan/room type combination.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="AcceptableGuarantees" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">A collection of accepted guarantees.</xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:sequence>
									<xs:element name="AcceptableGuarantee" maxOccurs="unbounded">
										<xs:annotation>
											<xs:documentation xml:lang="en">The guarantee information.</xs:documentation>
										</xs:annotation>
										<xs:complexType>
											<xs:complexContent>
												<xs:extension base="GuaranteeType">
													<xs:attribute name="GuaranteePolicyType" use="optional">
														<xs:annotation>
															<xs:documentation xml:lang="en">GuaranteePolicyType: An enumerated type that defines the guarantee policy applied to the booking restrictions.</xs:documentation>
														</xs:annotation>
														<xs:simpleType>
															<xs:restriction base="xs:NMTOKEN">
																<xs:enumeration value="NoGuaranteesAccepted"/>
																<xs:enumeration value="GuaranteesAccepted"/>
																<xs:enumeration value="GuaranteesRequired"/>
																<xs:enumeration value="DepositRequired"/>
																<xs:enumeration value="GuaranteesNotRequired"/>
																<xs:enumeration value="DepositNotRequired"/>
																<xs:enumeration value="PrepayRequired">
																	<xs:annotation>
																		<xs:documentation xml:lang="en">Prepayment is required.</xs:documentation>
																	</xs:annotation>
																</xs:enumeration>
																<xs:enumeration value="PrepayNotRequired">
																	<xs:annotation>
																		<xs:documentation xml:lang="en">Prepayment is not required.</xs:documentation>
																	</xs:annotation>
																</xs:enumeration>
																<xs:enumeration value="NoDepositsAccepted">
																	<xs:annotation>
																		<xs:documentation xml:lang="en">Deposits not accepted.</xs:documentation>
																	</xs:annotation>
																</xs:enumeration>
															</xs:restriction>
														</xs:simpleType>
													</xs:attribute>
													<xs:attribute name="PaymentType" type="OTA_CodeType" use="optional">
														<xs:annotation>
															<xs:documentation xml:lang="en">Refer to OpenTravel Code List Payment Type (PMT).</xs:documentation>
														</xs:annotation>
													</xs:attribute>
													<xs:attributeGroup ref="CurrencyCodeGroup"/>
													<xs:attribute name="UnacceptablePaymentType" type="OTA_CodeType" use="optional">
														<xs:annotation>
															<xs:documentation xml:lang="en">Used to denote unacceptable forms of payment. Refer to OpenTravel Code List Payment Type (PMT).</xs:documentation>
														</xs:annotation>
													</xs:attribute>
												</xs:extension>
											</xs:complexContent>
										</xs:complexType>
									</xs:element>
								</xs:sequence>
							</xs:complexType>
						</xs:element>
						<xs:element name="CancelPenalties" type="CancelPenaltiesType" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">A collection of required payments that are part of the booking restriction.</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="RequiredPaymts" type="RequiredPaymentsType" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">A collection of required payments that are part of the booking restriction.</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="LengthsOfStay" type="LengthsOfStayType" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">A collection of lengths of stay.</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="DOW_Restrictions" type="DOW_RestrictionsType" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">A collection of Day Of Week restrictions.</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="RestrictionStatus" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">Availability status assigned to the room rate combination for this booking rule.</xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:attributeGroup ref="RestrictionStatusGroup"/>
							</xs:complexType>
						</xs:element>
						<xs:element name="Viewerships" type="ViewershipsType" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">A collection of Viewships.</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="AddtionalRules" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">A collection of Additional rules.</xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:sequence>
									<xs:element name="AdditionalRule" maxOccurs="unbounded">
										<xs:annotation>
											<xs:documentation xml:lang="en">Indicates an additional rule for the reservation (e.g., cancelable, modifiable or refundable).</xs:documentation>
										</xs:annotation>
										<xs:complexType>
											<xs:attribute name="AdditionalRule" use="optional">
												<xs:annotation>
													<xs:documentation xml:lang="en">Provides additional rules for the reservation.</xs:documentation>
												</xs:annotation>
												<xs:simpleType>
													<xs:restriction base="xs:NMTOKEN">
														<xs:enumeration value="IsCancelable">
															<xs:annotation>
																<xs:documentation xml:lang="en">Indicates cancellation is allowed.</xs:documentation>
															</xs:annotation>
														</xs:enumeration>
														<xs:enumeration value="IsModifiable">
															<xs:annotation>
																<xs:documentation/>
															</xs:annotation>
														</xs:enumeration>
														<xs:enumeration value="IsRefundable">
															<xs:annotation>
																<xs:documentation/>
															</xs:annotation>
														</xs:enumeration>
														<xs:enumeration value="NotCancelable">
															<xs:annotation>
																<xs:documentation xml:lang="en">Indicates cancellation is not allowed.</xs:documentation>
															</xs:annotation>
														</xs:enumeration>
														<xs:enumeration value="NotModifiable">
															<xs:annotation>
																<xs:documentation xml:lang="en">Indicates modification is not allowed.</xs:documentation>
															</xs:annotation>
														</xs:enumeration>
														<xs:enumeration value="NotRefundable">
															<xs:annotation>
																<xs:documentation xml:lang="en">Indicates refunds are not allowed.</xs:documentation>
															</xs:annotation>
														</xs:enumeration>
														<xs:enumeration value="IsCommissionable">
															<xs:annotation>
																<xs:documentation xml:lang="en">Indicates commissions are allowed.</xs:documentation>
															</xs:annotation>
														</xs:enumeration>
														<xs:enumeration value="NotCommissionable">
															<xs:annotation>
																<xs:documentation xml:lang="en">Indicates commissions are not allowed.</xs:documentation>
															</xs:annotation>
														</xs:enumeration>
														<xs:enumeration value="CertificateRequired">
															<xs:annotation>
																<xs:documentation xml:lang="en">Indicates a certificate is required.</xs:documentation>
															</xs:annotation>
														</xs:enumeration>
														<xs:enumeration value="ID_Required">
															<xs:annotation>
																<xs:documentation xml:lang="en">Indicates an ID is required.</xs:documentation>
															</xs:annotation>
														</xs:enumeration>
													</xs:restriction>
												</xs:simpleType>
											</xs:attribute>
										</xs:complexType>
									</xs:element>
								</xs:sequence>
							</xs:complexType>
						</xs:element>
						<xs:element name="Description" type="ParagraphType" minOccurs="0" maxOccurs="20">
							<xs:annotation>
								<xs:documentation xml:lang="en">General description of booking rule.</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="UniqueID" type="UniqueID_Type" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">The unique identifier element allows the trading partners to  uniquely identify each Booking Rule, for transaction tracability.</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="CheckoutCharge" minOccurs="0" maxOccurs="2">
							<xs:annotation>
								<xs:documentation xml:lang="en">The charges that may apply to an early or late checkout.</xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:attributeGroup ref="CurrencyAmountGroup"/>
								<xs:attribute name="Percent" type="Percentage" use="optional">
									<xs:annotation>
										<xs:documentation xml:lang="en">The percentage of the room rate that applies to an early/late checkout.</xs:documentation>
									</xs:annotation>
								</xs:attribute>
								<xs:attribute name="Type" use="optional">
									<xs:annotation>
										<xs:documentation xml:lang="en">To specify if the charge applies to early or late checkout.</xs:documentation>
									</xs:annotation>
									<xs:simpleType>
										<xs:restriction base="xs:NMTOKEN">
											<xs:enumeration value="Early"/>
											<xs:enumeration value="Late"/>
										</xs:restriction>
									</xs:simpleType>
								</xs:attribute>
								<xs:attributeGroup ref="CodeInfoGroup">
									<xs:annotation>
										<xs:documentation xml:lang="en">May be used to give further detail on the enumerated list or to remove an obsolete item.</xs:documentation>
									</xs:annotation>
								</xs:attributeGroup>
								<xs:attribute name="NmbrOfNights" type="xs:nonNegativeInteger" use="optional">
									<xs:annotation>
										<xs:documentation xml:lang="en">The number of nights used to calculate the fee amount.</xs:documentation>
									</xs:annotation>
								</xs:attribute>
								<xs:attribute name="ExistsCode" type="OTA_CodeType" use="optional">
									<xs:annotation>
										<xs:documentation xml:lang="en">This attribute is used to explicitly define whether the checkout charge applies. Refer to OpenTravel Code list Option Type Code (OTC). This is used in conjunction with Type.</xs:documentation>
									</xs:annotation>
								</xs:attribute>
								<xs:attribute name="BalanceOfStayInd" type="xs:boolean" use="optional">
									<xs:annotation>
										<xs:documentation xml:lang="en">If true the charge for early checkout is the amount that would be charged for the remaining time of the original reservation.</xs:documentation>
									</xs:annotation>
								</xs:attribute>
							</xs:complexType>
						</xs:element>
					</xs:sequence>
					<xs:attributeGroup ref="CodeListGroup">
						<xs:annotation>
							<xs:documentation xml:lang="en">Used to specify a  rate plan code, room type code or rate plan/room type combination code and its associated attributes.</xs:documentation>
						</xs:annotation>
					</xs:attributeGroup>
					<xs:attributeGroup ref="DateTimeSpanGroup"/>
					<xs:attribute name="MaxAdvancedBookingOffset" type="xs:duration" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">Maximum days before the arrival date for which this rate plan may be booked.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="MinAdvancedBookingOffset" type="xs:duration" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">Minimum days before the arrival date for which this rate plan may be booked.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="ForceGuaranteeOffset" type="xs:duration" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">Number of days prior to arrival date at which a guarantee is required.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="DepositWaiverOffset" type="xs:duration" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">Number of days prior to arrival date for which a deposit is no longer accepted (because of a too short time delay between reservation and guest arrival).</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="MinTotalOccupancy" type="xs:nonNegativeInteger" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">Defines the minimum number of total occupants required for a rate plan.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="MaxTotalOccupancy" type="xs:nonNegativeInteger" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">Defines the maximum number of total occupants allowed for a rate plan.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="AbsoluteDropTime" type="DateOrDateTimeType" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">The fixed time that the booking restriction goes into effect to cancel the non-guaranteed reservation. Either this attribute or the DropTimeOffset element may be used.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="GenerallyBookable" type="xs:boolean" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">Indicates whether this rate plan can be booked by those not in the viewership collection.  If this attribute is False (0), then the viewerships collection lists those who have view-only or bookable status for this rate plan. If this attribute is True (1), then the viewerships collection lists those who have view-only or not-viewable status for this rate plan.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="PriceViewable" type="xs:boolean" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">Indicates whether the price for this rate plan can be viewed, or whether the guest must contact another entity to obtain price information.</xs:documentation>
							<xs:documentation xml:lang="en">Valid values: 0 = (No) Price Not Viewable, 1 = (Yes) Price Viewable.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="QualifiedRateYN" type="xs:boolean" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">Boolean value indicating whether a specific rate plan must be qualified prior to inclusion in availability response messages.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="AddressRequired" type="xs:boolean" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">An address is required to complete the booking.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attributeGroup ref="InvBlockCutoffGroup">
						<xs:annotation>
							<xs:documentation xml:lang="en">Defines when block inventory is released back to general inventory.</xs:documentation>
						</xs:annotation>
					</xs:attributeGroup>
					<xs:attribute name="MaxContiguousBookings" type="xs:nonNegativeInteger" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">The maximum number of times a rate may be booked contiguously within a reservation (often used with packages).</xs:documentation>
						</xs:annotation>
					</xs:attribute>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="CancelPenaltiesType">
		<xs:annotation>
			<xs:documentation xml:lang="en">A collection of CancelPenalty.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="CancelPenalty" type="CancelPenaltyType" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation xml:lang="en">Defines the cancellation penalty of the hotel facility.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="CancelPolicyIndicator" type="xs:boolean" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">When true, indicates a cancel policy exits. When false, no cancel policy exists. Typically this indicator is used when details are not being sent.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:complexType>
	<xs:complexType name="CancelPenaltyType">
		<xs:annotation>
			<xs:documentation xml:lang="en">The CancelPenalty class defines the cancellation policy of the hotel facility.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="Deadline" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Cancellation deadline, absolute or relative.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:attributeGroup ref="DeadlineGroup">
						<xs:annotation>
							<xs:documentation xml:lang="en">The absolute deadline or amount of offset time before a deadline for a payment of cancel goes into effect.</xs:documentation>
						</xs:annotation>
					</xs:attributeGroup>
				</xs:complexType>
			</xs:element>
			<xs:element name="AmountPercent" type="AmountPercentType" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Cancellation fee expressed as a fixed amount, or percentage of/or room nights.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="PenaltyDescription" type="ParagraphType" minOccurs="0" maxOccurs="9">
				<xs:annotation>
					<xs:documentation xml:lang="en">Text description of the Penalty in a given language.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="ConfirmClassCode" type="StringLength1to16" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Confirm Class.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="PolicyCode" type="StringLength1to16" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Policy Class.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="NonRefundable" type="xs:boolean" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Indicates that any prepayment for the reservation is non refundable, therefore a 100% penalty on the prepayment is applied, irrespective of deadline.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attributeGroup ref="DOW_PatternGroup">
			<xs:annotation>
				<xs:documentation xml:lang="en">If a day(s) of the week is set to true then the associated policy applies to that day of week.</xs:documentation>
			</xs:annotation>
		</xs:attributeGroup>
		<xs:attributeGroup ref="DateTimeSpanGroup">
			<xs:annotation>
				<xs:documentation xml:lang="en">The date time span for which the policy applies.</xs:documentation>
			</xs:annotation>
		</xs:attributeGroup>
		<xs:attribute name="RoomTypeCode" type="StringLength1to16" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">A system specific room type to which this cancellation penalty applies.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:complexType>
	<xs:complexType name="DestinationSystemCodesType">
		<xs:annotation>
			<xs:documentation xml:lang="en">A collection of DestinationSystemCode.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="DestinationSystemCode" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation xml:lang="en">The destination system code defines a system to which information is to be provided.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:simpleContent>
						<xs:extension base="StringLength1to32"/>
					</xs:simpleContent>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="DiscountType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Identifies and provides details about the discount.</xs:documentation>
		</xs:annotation>
		<xs:complexContent>
			<xs:extension base="TotalType">
				<xs:sequence>
					<xs:element name="DiscountReason" type="ParagraphType"/>
				</xs:sequence>
				<xs:attribute name="TaxInclusive" type="xs:boolean" use="optional"/>
				<xs:attribute name="Percent" type="Percentage" use="optional"/>
				<xs:attribute name="DiscountCode" type="StringLength1to16" use="optional">
					<xs:annotation>
						<xs:documentation xml:lang="en">Specifies the type of discount (e.g., No condition, LOS, Deposit or Total amount spent).</xs:documentation>
					</xs:annotation>
				</xs:attribute>
				<xs:attribute name="RestrictedDisplayIndicator" type="xs:boolean" use="optional">
					<xs:annotation>
						<xs:documentation xml:lang="en">When true, used to indicate the discount should not be displayed. When false, indicates the discount may be displayed.</xs:documentation>
					</xs:annotation>
				</xs:attribute>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:complexType name="DOW_RestrictionsType">
		<xs:sequence>
			<xs:element name="AvailableDaysOfWeek" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Days of week on which this room/rate combination is available.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:attributeGroup ref="DOW_PatternGroup"/>
				</xs:complexType>
			</xs:element>
			<xs:element name="ArrivalDaysOfWeek" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Days of Week on which the guest can arrive.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:attributeGroup ref="DOW_PatternGroup"/>
				</xs:complexType>
			</xs:element>
			<xs:element name="DepartureDaysOfWeek" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Days of Week on which the guest can leave.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:attributeGroup ref="DOW_PatternGroup"/>
				</xs:complexType>
			</xs:element>
			<xs:element name="RequiredDaysOfWeek" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Days of Week on which the guest has to stay at the hotel.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:attributeGroup ref="DOW_PatternGroup"/>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="DOW_RulesType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Indicates the day of week rules that apply based on the DOW_TypeCode.</xs:documentation>
		</xs:annotation>
		<xs:attributeGroup ref="DOW_PatternGroup"/>
		<xs:attribute name="DOW_TypeCode" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">This specifies the type of rule for which the day of week or date span applies.</xs:documentation>
			</xs:annotation>
			<xs:simpleType>
				<xs:restriction base="xs:NMTOKEN">
					<xs:enumeration value="Arrival">
						<xs:annotation>
							<xs:documentation xml:lang="en">The days of week when arrival is allowed.</xs:documentation>
						</xs:annotation>
					</xs:enumeration>
					<xs:enumeration value="Available">
						<xs:annotation>
							<xs:documentation xml:lang="en">The days of week when there is availability for booking.</xs:documentation>
						</xs:annotation>
					</xs:enumeration>
					<xs:enumeration value="Required">
						<xs:annotation>
							<xs:documentation xml:lang="en">The days of week for which the product must be booked.</xs:documentation>
						</xs:annotation>
					</xs:enumeration>
				</xs:restriction>
			</xs:simpleType>
		</xs:attribute>
		<xs:attributeGroup ref="DateTimeSpanGroup">
			<xs:annotation>
				<xs:documentation xml:lang="en">This will allow a specific date range to be defined.</xs:documentation>
			</xs:annotation>
		</xs:attributeGroup>
	</xs:complexType>
	<xs:complexType name="FeaturesType">
		<xs:annotation>
			<xs:documentation xml:lang="en">A collection of Feature.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="Feature" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation xml:lang="en">Describes the security and physically challenged features that a hotel offers.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="Charge" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">Indicates whether this feature is chargeable.</xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:attributeGroup ref="CurrencyAmountGroup"/>
							</xs:complexType>
						</xs:element>
						<xs:element name="MultimediaDescriptions" type="MultimediaDescriptionsType" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">Multimedia information about the feature.</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="DescriptiveText" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">Descriptive text that describes the feature.</xs:documentation>
							</xs:annotation>
							<xs:simpleType>
								<xs:restriction base="xs:string">
									<xs:pattern value="[A-Za-z0-9]{1,500}"/>
								</xs:restriction>
							</xs:simpleType>
						</xs:element>
					</xs:sequence>
					<xs:attributeGroup ref="CodeInfoGroup"/>
					<xs:attribute name="AccessibleCode" type="OTA_CodeType" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">Refer to OpenTravel Code List Physically Challenged Feature Code (PHY). For any of the codes which apply to a room count, use the GuestRoom\@Quantity.	Additionally, GuestRoom\@RoomTypeName may be used to pass the specific room type or you could pass "all" or "total" to indicate the AccessibleCode applies to the total hotel.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="SecurityCode" type="OTA_CodeType" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">Refer to OpenTravel Code List Security Feature Code (SEC).</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="ExistsCode" type="OTA_CodeType" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">This attribute is used to explicitly define whether an amenity or service is offered. Refer to OpenTravel Codelist Option Type Code (OTC). This is used in conjunction with AccessibleCode or SecurityCode.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="ProximityCode" type="OTA_CodeType" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">Denotes the general location of a feature. Refer to OpenTravel Codelist Proximity (PRX).</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attributeGroup ref="ID_OptionalGroup">
						<xs:annotation>
							<xs:documentation xml:lang="en">A unique identifying value assigned by the creating system. The ID attribute may be used to reference a primary-key value within a database or in a particular implementation.</xs:documentation>
						</xs:annotation>
					</xs:attributeGroup>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="GDS_InfoType">
		<xs:annotation>
			<xs:documentation xml:lang="en">This defines codes used by individual GDS's, which can be used to upload rate information.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="GDS_Codes" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The element acts as a container for GDS_Code. It is used to send item for each GDS.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="GDS_Code" minOccurs="0" maxOccurs="unbounded">
							<xs:annotation>
								<xs:documentation xml:lang="en">Provides detailed information regarding the specified GDS.</xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:sequence>
									<xs:element name="GDS_CodeDetails" minOccurs="0">
										<xs:annotation>
											<xs:documentation xml:lang="en">Collection of GDS_CodeDetails.</xs:documentation>
										</xs:annotation>
										<xs:complexType>
											<xs:sequence>
												<xs:element name="GDS_CodeDetail" minOccurs="0" maxOccurs="unbounded">
													<xs:annotation>
														<xs:documentation xml:lang="en">This holds detailed information pertaining to the agencies authorized to book this rate.</xs:documentation>
													</xs:annotation>
													<xs:complexType>
														<xs:attribute name="PseudoCityCode" type="StringLength1to16" use="optional">
															<xs:annotation>
																<xs:documentation xml:lang="en">This is used to pass the pseudo city code (i.e., the code that identifies a specific agency location) of the agency that has authority to book the negotiated rate(s)).</xs:documentation>
															</xs:annotation>
														</xs:attribute>
														<xs:attribute name="AgencyName" type="StringLength1to64" use="optional">
															<xs:annotation>
																<xs:documentation xml:lang="en">This is the agency with authority to book the negotiated rate(s).</xs:documentation>
															</xs:annotation>
														</xs:attribute>
													</xs:complexType>
												</xs:element>
											</xs:sequence>
										</xs:complexType>
									</xs:element>
								</xs:sequence>
								<xs:attribute name="ChainCode" use="optional">
									<xs:annotation>
										<xs:documentation xml:lang="en">This is the 2 character GDS Chain Code used to identify a specific chain.</xs:documentation>
									</xs:annotation>
									<xs:simpleType>
										<xs:restriction base="xs:string">
											<xs:minLength value="2"/>
											<xs:maxLength value="2"/>
										</xs:restriction>
									</xs:simpleType>
								</xs:attribute>
								<xs:attribute name="GDS_PropertyCode" type="StringLength1to16" use="optional">
									<xs:annotation>
										<xs:documentation xml:lang="en">This is the GDS Property Code used to identify a specific hotel.</xs:documentation>
									</xs:annotation>
								</xs:attribute>
								<xs:attribute name="GDS_Name" type="StringLength1to32" use="required">
									<xs:annotation>
										<xs:documentation xml:lang="en">Type of GDS for hotel such as Apollo, Amadeus, Sabre, Worldspan etc.</xs:documentation>
									</xs:annotation>
								</xs:attribute>
								<xs:attribute name="LoadGDSIndicator" type="xs:boolean" use="optional">
									<xs:annotation>
										<xs:documentation xml:lang="en">This attribute is used in conjunction with the GDS_Name. When true, this indicates rates will be loaded to the GDS specified by the GDS_Name.</xs:documentation>
									</xs:annotation>
								</xs:attribute>
								<xs:attribute name="GDS_PropertyLongName" type="StringLength1to64" use="optional">
									<xs:annotation>
										<xs:documentation xml:lang="en">A property long name per channel.</xs:documentation>
									</xs:annotation>
								</xs:attribute>
								<xs:attribute name="GDS_PropertyShortName" type="StringLength1to32" use="optional">
									<xs:annotation>
										<xs:documentation xml:lang="en">A property short name per channel.</xs:documentation>
									</xs:annotation>
								</xs:attribute>
								<xs:attribute name="GDS_RoomTypeCode" type="StringLength1to16" use="optional">
									<xs:annotation>
										<xs:documentation xml:lang="en">A GDS channel room code.</xs:documentation>
									</xs:annotation>
								</xs:attribute>
							</xs:complexType>
						</xs:element>
					</xs:sequence>
					<xs:attribute name="LoadGDSIndicator" type="xs:boolean" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">When true, this indicates rates will be loaded to specified GDSs. This attribute is also under GDS_Code to associate the rate load for an individual GDS. (This may also be used to idicate the willingness to upload rates to GDSs even if not specified in the request message.)</xs:documentation>
						</xs:annotation>
					</xs:attribute>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="MasterChainCode" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">This is the 2 character master chain code that identifes a specific chain that is recognized by all GDS's.</xs:documentation>
			</xs:annotation>
			<xs:simpleType>
				<xs:restriction base="xs:string">
					<xs:minLength value="2"/>
					<xs:maxLength value="2"/>
				</xs:restriction>
			</xs:simpleType>
		</xs:attribute>
	</xs:complexType>
	<xs:complexType name="GuaranteeType">
		<xs:annotation>
			<xs:documentation xml:lang="en">The guarantee information to hold a reservation</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="GuaranteesAccepted" minOccurs="0">
				<xs:complexType>
					<xs:sequence>
						<xs:element name="GuaranteeAccepted" maxOccurs="unbounded">
							<xs:annotation>
								<xs:documentation xml:lang="en">Guarantee Detail.</xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:complexContent>
									<xs:extension base="PaymentFormType">
										<xs:attribute name="Default" type="xs:boolean">
											<xs:annotation>
												<xs:documentation xml:lang="en">This is to indicate that the information in the model is the default (e.g. if PaymentCard information is completed then this would be considered the default if the boolean is true).</xs:documentation>
											</xs:annotation>
										</xs:attribute>
										<xs:attribute name="NoCardHolderInfoReqInd" type="xs:boolean" use="optional">
											<xs:annotation>
												<xs:documentation xml:lang="en">If true, no credit card holder information is required. If false, it is not required.</xs:documentation>
											</xs:annotation>
										</xs:attribute>
										<xs:attribute name="NameReqInd" type="xs:boolean" use="optional">
											<xs:annotation>
												<xs:documentation xml:lang="en">If true, the credit card holder name is required. If false, it is not required.</xs:documentation>
											</xs:annotation>
										</xs:attribute>
										<xs:attribute name="AddressReqInd" type="xs:boolean" use="optional">
											<xs:annotation>
												<xs:documentation xml:lang="en">If true, credit card holder address is required. If false, it is not required.</xs:documentation>
											</xs:annotation>
										</xs:attribute>
										<xs:attribute name="PhoneReqInd" type="xs:boolean" use="optional">
											<xs:annotation>
												<xs:documentation xml:lang="en">If true, credit card holder phone number is required. If false, it is not required.</xs:documentation>
											</xs:annotation>
										</xs:attribute>
										<xs:attribute name="InterbankNbrReqInd" type="xs:boolean" use="optional">
											<xs:annotation>
												<xs:documentation xml:lang="en">If true, the credit card interbank number is required. If false, it is not required.</xs:documentation>
											</xs:annotation>
										</xs:attribute>
										<xs:attribute name="BookingSourceAllowedInd" type="xs:boolean" use="optional">
											<xs:annotation>
												<xs:documentation xml:lang="en">When true, the booking source may be used to guarantee the booking.</xs:documentation>
											</xs:annotation>
										</xs:attribute>
										<xs:attribute name="CorpDiscountNbrAllowedInd" type="xs:boolean" use="optional">
											<xs:annotation>
												<xs:documentation xml:lang="en">When true, the corporate discount number may be used to guarantee the booking.</xs:documentation>
											</xs:annotation>
										</xs:attribute>
									</xs:extension>
								</xs:complexContent>
							</xs:complexType>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="Deadline" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Guarantee deadline, absolute or relative.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:attributeGroup ref="DeadlineGroup"/>
				</xs:complexType>
			</xs:element>
			<xs:element name="Comments" type="CommentType" minOccurs="0"/>
			<xs:element name="GuaranteeDescription" type="ParagraphType" minOccurs="0" maxOccurs="9">
				<xs:annotation>
					<xs:documentation xml:lang="en">Text description of the Guarantee in a given language.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="RetributionType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">An enumerated type defining the type of action taken when the deadline has been exceeded. Valid values: Res Automatically Cancelled, Res No Longer Guaranteed.</xs:documentation>
			</xs:annotation>
			<xs:simpleType>
				<xs:restriction base="StringLength1to32">
					<xs:enumeration value="ResAutoCancelled"/>
					<xs:enumeration value="ResNotGuaranteed"/>
				</xs:restriction>
			</xs:simpleType>
		</xs:attribute>
		<xs:attribute name="GuaranteeCode" type="StringLength1to32" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Guarantee Code</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="GuaranteeType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">GuaranteeType: GuaranteeType An enumerated type defining the guarantee to be applied to this reservation.</xs:documentation>
			</xs:annotation>
			<xs:simpleType>
				<xs:restriction base="StringLength1to32">
					<xs:enumeration value="GuaranteeRequired">
						<xs:annotation>
							<xs:documentation xml:lang="en">A guarantee is required.</xs:documentation>
						</xs:annotation>
					</xs:enumeration>
					<xs:enumeration value="None">
						<xs:annotation>
							<xs:documentation xml:lang="en">No guarantee is required.</xs:documentation>
						</xs:annotation>
					</xs:enumeration>
					<xs:enumeration value="CC/DC/Voucher"/>
					<xs:enumeration value="Profile"/>
					<xs:enumeration value="Deposit"/>
					<xs:enumeration value="PrePay">
						<xs:annotation>
							<xs:documentation xml:lang="en">Indicates prepayment, typically this means payment is required at booking.</xs:documentation>
						</xs:annotation>
					</xs:enumeration>
				</xs:restriction>
			</xs:simpleType>
		</xs:attribute>
		<xs:attribute name="HoldTime" type="xs:time" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The room will held up until this time without a guarantee.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:complexType>
	<xs:complexType name="GuestCountType">
		<xs:annotation>
			<xs:documentation xml:lang="en">A collection of GuestCount by age group.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="GuestCount" maxOccurs="99">
				<xs:annotation>
					<xs:documentation xml:lang="en">A recurring element that identifies the number of guests and ages of the guests.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:attributeGroup ref="GuestCountGroup"/>
					<xs:attribute name="ResGuestRPH" type="RPH_Type" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">Used to refer to the guest associated with this reservation.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="IsPerRoom" type="xs:boolean" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">IsPerRoom means that the guests defined in the GuestCounts object apply to each room in the NumberOfRooms for the RoomStay.  Value of "false" means that the guests defined in the GuestCounts object apply to all rooms combined in the NumberOfRooms for the RoomStay.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:complexType>
	<xs:complexType name="GuestRoomType">
		<xs:annotation>
			<xs:documentation xml:lang="en">GuestRoomType is used to contain all the information on a guest room.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="Quantities" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Max rollaways, number of beds.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:attribute name="MaxRollaways" type="xs:nonNegativeInteger" use="optional"/>
					<xs:attribute name="StandardNumBeds" type="xs:nonNegativeInteger" use="optional"/>
					<xs:attribute name="MaximumAdditionalGuests" type="xs:nonNegativeInteger" use="optional"/>
					<xs:attribute name="MinBillableGuests" type="xs:nonNegativeInteger" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">Indicates the minimum number of guests for which a room charge will be applied, even if there are fewer guests in the room.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
				</xs:complexType>
			</xs:element>
			<xs:element name="Occupancy" minOccurs="0" maxOccurs="5">
				<xs:annotation>
					<xs:documentation xml:lang="en">Provides parameters of occupancy limits.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:attributeGroup ref="OccupancyGroup">
						<xs:annotation>
							<xs:documentation xml:lang="en">Minimum or maximum number of people allowed in a room type as defined by age.</xs:documentation>
						</xs:annotation>
					</xs:attributeGroup>
					<xs:attributeGroup ref="AgeQualifyingGroup">
						<xs:annotation>
							<xs:documentation xml:lang="en">Specifies the age parameters for the occupancy of this guest room type.</xs:documentation>
						</xs:annotation>
					</xs:attributeGroup>
				</xs:complexType>
			</xs:element>
			<xs:element name="Room" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Attributes to describe the room from room category to location to view to bed type.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:attributeGroup ref="RoomGroup"/>
				</xs:complexType>
			</xs:element>
			<xs:element name="Amenities" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Collection of room level amenities.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="Amenity" maxOccurs="unbounded">
							<xs:annotation>
								<xs:documentation xml:lang="en">Amenity Code Attribute is used to hold actual amenity code.</xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:attribute name="AmenityCode" type="OTA_CodeType">
									<xs:annotation>
										<xs:documentation xml:lang="en">Refer to OpenTravel Code List Room Amenity Type (RMA).</xs:documentation>
									</xs:annotation>
								</xs:attribute>
							</xs:complexType>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="RoomLevelFees" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Collection of fees charged at the room level.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:complexContent>
						<xs:extension base="FeesType">
							<xs:attributeGroup ref="CodeListGroup"/>
						</xs:extension>
					</xs:complexContent>
				</xs:complexType>
			</xs:element>
			<xs:element name="AdditionalGuestAmount" type="AdditionalGuestAmountType" minOccurs="0" maxOccurs="5">
				<xs:annotation>
					<xs:documentation xml:lang="en">Additional guest amounts which are attached to room, not rate.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Description" type="ParagraphType" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Description of the Room.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="HotelAdditionalChargesType">
		<xs:sequence>
			<xs:element name="AdditionalCharge" maxOccurs="99">
				<xs:annotation>
					<xs:documentation xml:lang="en">Amenities or services to which a charge applies.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="Amount" type="TotalType" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">The amount charged for an amenity or service.</xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
					<xs:attribute name="RoomAmenityCode" type="OTA_CodeType" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">Identifes the types of room amenities to which a charge applies. Refer to OpenTravel Code List Room Amenity Type (RMA).</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attributeGroup ref="QuantityGroup"/>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="AmountBeforeTax" type="Money" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Total additional charges before taxes.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="AmountAfterTax" type="Money" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Total additional charges after taxes.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attributeGroup ref="CurrencyCodeGroup">
			<xs:annotation>
				<xs:documentation xml:lang="en">Currency code and number of decimal places used.</xs:documentation>
			</xs:annotation>
		</xs:attributeGroup>
	</xs:complexType>
	<xs:complexType name="HotelPaymentFormType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Defines how an account will be settled at checkout.</xs:documentation>
		</xs:annotation>
		<xs:complexContent>
			<xs:extension base="PaymentFormType">
				<xs:sequence>
					<xs:element name="MasterAccountUsage" minOccurs="0">
						<xs:annotation>
							<xs:documentation xml:lang="en">Authorized usage of the Master Account established at the hotel for the purposes of billing arrangements.</xs:documentation>
						</xs:annotation>
						<xs:complexType>
							<xs:attributeGroup ref="BillingType"/>
						</xs:complexType>
					</xs:element>
				</xs:sequence>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:complexType name="HotelReservationIDsType">
		<xs:annotation>
			<xs:documentation xml:lang="en">A Collection of HotelReservationID objects for a given reservation. The collection of all ReservationIDs can include Passenger Name Record (PNR), Guest Name Record (GNR) and Guest Folio numbers. Associated with each can be a Confirmation number which is usually given to the guest.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="HotelReservationID" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation xml:lang="en">The HotelReservationID  object contains various unique (ReservationID) and non unique (ConfirmationID, CancellationID) identifiers that the trading partners associate with a given reservation.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:attribute name="ResID_Type" type="OTA_CodeType" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">Defines the type of  Reservation ID (e.g. reservation number, cancellation number). Refer to OpenTravel Code List Unique ID Type (UIT).</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="ResID_Value" type="StringLength1to64" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">This is the actual value associated with ResID_Type as generated by the system that is the source of the ResID_Type.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="ResID_Source" type="StringLength1to64" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">A unique identifier to indicate the source system which generated the ResID_Value.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="ResID_SourceContext" type="StringLength1to64" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">Additional information on Source.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="ResID_Date" type="xs:dateTime" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">Date of the creation of this reservation.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="ForGuest" type="xs:boolean">
						<xs:annotation>
							<xs:documentation xml:lang="en">Used to determine if the ResID_Value is given to guest.</xs:documentation>
							<xs:documentation xml:lang="en">
								<LegacyDefaultValue>false</LegacyDefaultValue>
							</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="ResGuestRPH" type="RPH_Type" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">This is a reference placeholder, used as an index for this guest in this reservation. In the ResGuest object it is used like all other RPH attributes to send the delta of a reservation. It is used by the RoomStay and Service objects to indicate which guests are associated with that room stay or service.  It is also used to link a guest with a ReservationID.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="CancelOriginatorCode" type="StringLength1to64" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">Identifying code of the system that initiated the cancel of this reservation.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="CancellationDate" type="xs:dateTime" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">Date this reservation was cancelled.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="HotelReservationID_RPH" type="RPH_Type" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">his is a reference placeholder, used as an index for this reservation ID.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="HotelRoomListType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Provides the details of a rooming list.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="UniqueID" type="UniqueID_Type" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The unique indicator for the group rooming list or tour operator booking reference.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Guests" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">A collection of Guest.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="Guest" maxOccurs="unbounded">
							<xs:annotation>
								<xs:documentation xml:lang="en">A person staying in a room.</xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:complexContent>
									<xs:extension base="ContactPersonType">
										<xs:sequence>
											<xs:element name="UniqueID" type="UniqueID_Type" minOccurs="0">
												<xs:annotation>
													<xs:documentation xml:lang="en">The confirmation number for a particular guest. This is used with the 'GuestAction' attribute value of 'Add-Update' to indicate an update.</xs:documentation>
												</xs:annotation>
											</xs:element>
											<xs:element name="Loyalty" minOccurs="0" maxOccurs="5">
												<xs:complexType>
													<xs:attributeGroup ref="SelectedLoyaltyGroup"/>
												</xs:complexType>
											</xs:element>
											<xs:element name="GuaranteePayment" minOccurs="0" maxOccurs="2">
												<xs:annotation>
													<xs:documentation xml:lang="en">Guarantee or Payment information for an individual guest.</xs:documentation>
												</xs:annotation>
												<xs:complexType>
													<xs:complexContent>
														<xs:extension base="HotelPaymentFormType">
															<xs:attribute name="DetailType" use="optional">
																<xs:simpleType>
																	<xs:restriction base="xs:string">
																		<xs:enumeration value="Payment"/>
																		<xs:enumeration value="Guarantee"/>
																	</xs:restriction>
																</xs:simpleType>
															</xs:attribute>
															<xs:attribute name="GuaranteeType" use="optional">
																<xs:annotation>
																	<xs:documentation xml:lang="en">GuaranteeType: GuaranteeType An enumerated type defining the guarantee to be applied to this reservation.</xs:documentation>
																</xs:annotation>
																<xs:simpleType>
																	<xs:restriction base="StringLength1to32">
																		<xs:enumeration value="GuaranteeRequired"/>
																		<xs:enumeration value="None"/>
																		<xs:enumeration value="CC/DC/Voucher"/>
																		<xs:enumeration value="Profile"/>
																		<xs:enumeration value="Deposit"/>
																		<xs:enumeration value="PrePay">
																			<xs:annotation>
																				<xs:documentation xml:lang="en">Indicates prepayment, typically this means payment is required at booking.</xs:documentation>
																			</xs:annotation>
																		</xs:enumeration>
																	</xs:restriction>
																</xs:simpleType>
															</xs:attribute>
														</xs:extension>
													</xs:complexContent>
												</xs:complexType>
											</xs:element>
											<xs:element name="AdditionalDetails" type="AdditionalDetailsType" minOccurs="0"/>
										</xs:sequence>
										<xs:attribute name="GuestAction" type="ActionType" use="optional">
											<xs:annotation>
												<xs:documentation xml:lang="en">This attributes indicates the change in status of the guest.</xs:documentation>
											</xs:annotation>
										</xs:attribute>
										<xs:attribute name="PrintConfoInd" type="xs:boolean" use="optional">
											<xs:annotation>
												<xs:documentation xml:lang="en">Indicates whether to send the printed confirmation.</xs:documentation>
											</xs:annotation>
										</xs:attribute>
									</xs:extension>
								</xs:complexContent>
							</xs:complexType>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="MasterContact" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Planner or coordinator contact information for the rooming list.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:complexContent>
						<xs:extension base="ContactPersonType">
							<xs:sequence>
								<xs:element name="UniqueIDs" minOccurs="0">
									<xs:annotation>
										<xs:documentation xml:lang="en">This identifies the  master account number and related information.</xs:documentation>
									</xs:annotation>
									<xs:complexType>
										<xs:sequence>
											<xs:element name="UniqueID" type="UniqueID_Type" minOccurs="0" maxOccurs="9">
												<xs:annotation>
													<xs:documentation xml:lang="en">The unique identifiers for the Event Planner or Coordinator. Can include EP IATA# and other unique IDs such as an Event Planner ID assigned by the  receiving system.</xs:documentation>
												</xs:annotation>
											</xs:element>
										</xs:sequence>
									</xs:complexType>
								</xs:element>
								<xs:element name="Loyalty" minOccurs="0" maxOccurs="5">
									<xs:complexType>
										<xs:attributeGroup ref="SelectedLoyaltyGroup"/>
									</xs:complexType>
								</xs:element>
							</xs:sequence>
						</xs:extension>
					</xs:complexContent>
				</xs:complexType>
			</xs:element>
			<xs:element name="MasterAccount" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Account established at the hotel for the purposes of billing arrangements for an entity.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:complexContent>
						<xs:extension base="DirectBillType">
							<xs:attributeGroup ref="BillingType"/>
						</xs:extension>
					</xs:complexContent>
				</xs:complexType>
			</xs:element>
			<xs:element name="RoomStays" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">A collection of RoomStay.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="RoomStay" maxOccurs="unbounded">
							<xs:annotation>
								<xs:documentation xml:lang="en">Details the success, failure and warnings for the RoomStay.</xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:complexContent>
									<xs:extension base="RoomStayType">
										<xs:sequence minOccurs="0">
											<xs:element name="HotelReservationIDs" type="HotelReservationIDsType" minOccurs="0"/>
											<xs:element name="RoomShares" type="RoomSharesType" minOccurs="0">
												<xs:annotation>
													<xs:documentation xml:lang="en">Collection of shared rooms for room stay.</xs:documentation>
												</xs:annotation>
											</xs:element>
											<xs:element name="UniqueID" type="UniqueID_Type" minOccurs="0">
												<xs:annotation>
													<xs:documentation xml:lang="en">Provides a mechanism for uniquely identifying a room stay (e.g. this would be useful for a modification).</xs:documentation>
												</xs:annotation>
											</xs:element>
											<xs:choice minOccurs="0">
												<xs:sequence>
													<xs:element name="Success" type="SuccessType"/>
													<xs:element name="Warnings" type="WarningsType" minOccurs="0"/>
												</xs:sequence>
												<xs:element name="Errors" type="ErrorsType"/>
											</xs:choice>
										</xs:sequence>
										<xs:attribute name="RoomStay" type="ActionType" use="optional">
											<xs:annotation>
												<xs:documentation xml:lang="en">This attribute indicates the change to the reservation.</xs:documentation>
											</xs:annotation>
										</xs:attribute>
									</xs:extension>
								</xs:complexContent>
							</xs:complexType>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="Event" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Defines the event to which the rooming list applies.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="EventContact" type="ContactPersonType">
							<xs:annotation>
								<xs:documentation xml:lang="en">The event organizer or host.</xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
					<xs:attribute name="MeetingName" type="xs:string" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">This is used to identify the name of the meeting.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attributeGroup ref="DateTimeSpanGroup">
						<xs:annotation>
							<xs:documentation xml:lang="en">Used for the earliest start date and the latest end date of the event.</xs:documentation>
						</xs:annotation>
					</xs:attributeGroup>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
		<xs:attributeGroup ref="HotelReferenceGroup"/>
		<xs:attribute name="GroupBlockCode" type="StringLength1to16" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The code that identifies which group within a specific hotel for which this room list has been provided.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="CreationDate" type="xs:dateTime" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Date this room list was initially created.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:complexType>
	<xs:complexType name="HotelSearchCriteriaType">
		<xs:annotation>
			<xs:documentation xml:lang="en">A collection of single search criterion items.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="Criterion" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation xml:lang="en">Child elements that identify a single search criterion by criteria type. Because many of the types include partial matches to string values such as partial addresses (street names without a number) or partial telephone numbers (area code or three-digit prefix area, etc.) a ExactMatch attribute indicates whether the match to the string value must be exact.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:complexContent>
						<xs:extension base="HotelSearchCriterionType">
							<xs:attribute name="MoreDataEchoToken" type="StringLength1to128" use="optional">
								<xs:annotation>
									<xs:documentation xml:lang="en">The search response returns this attribute if there were additional items that could not fit within the response. The text value returned should be echoed in the subsequent request to indicate where to begin the next block of data.</xs:documentation>
								</xs:annotation>
							</xs:attribute>
							<xs:attribute name="InfoSource" type="InfoSourceType" use="optional">
								<xs:annotation>
									<xs:documentation xml:lang="en">Used to specify the source of the data being exchanged as determined by trading partners.</xs:documentation>
								</xs:annotation>
							</xs:attribute>
							<xs:attribute name="AlternateAvailability" use="optional">
								<xs:annotation>
									<xs:documentation xml:lang="en">Identifies under what circumstances alternate availability should be returned.</xs:documentation>
								</xs:annotation>
								<xs:simpleType>
									<xs:restriction base="xs:NMTOKEN">
										<xs:enumeration value="Never">
											<xs:annotation>
												<xs:documentation xml:lang="en">Do not return alternate availability.</xs:documentation>
											</xs:annotation>
										</xs:enumeration>
										<xs:enumeration value="WhenUnavailable">
											<xs:annotation>
												<xs:documentation xml:lang="en">Only return alternates when requested is not available.</xs:documentation>
											</xs:annotation>
										</xs:enumeration>
										<xs:enumeration value="Always">
											<xs:annotation>
												<xs:documentation xml:lang="en">Always return alternates even when request is available.</xs:documentation>
											</xs:annotation>
										</xs:enumeration>
									</xs:restriction>
								</xs:simpleType>
							</xs:attribute>
							<xs:attribute name="AddressSearchScope">
								<xs:annotation>
									<xs:documentation xml:lang="en">If "Primary", the address search keyword(s) will be compared to the physical address of the property. If "Alternate", the 'city' area attractions associated with the property will be searched.</xs:documentation>
								</xs:annotation>
								<xs:simpleType>
									<xs:restriction base="xs:NMTOKEN">
										<xs:enumeration value="Primary"/>
										<xs:enumeration value="Alternate"/>
										<xs:enumeration value="PrimaryAndAlternate">
											<xs:annotation>
												<xs:documentation xml:lang="en">The search keyword(s) will be compared to the physical address of the property and the 'city' area attractions associated with the property will be searched.</xs:documentation>
											</xs:annotation>
										</xs:enumeration>
									</xs:restriction>
								</xs:simpleType>
							</xs:attribute>
						</xs:extension>
					</xs:complexContent>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="AvailableOnlyIndicator" type="xs:boolean" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">When true, return only hotels that are available.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="BestOnlyIndicator" type="xs:boolean">
			<xs:annotation>
				<xs:documentation xml:lang="en">When true, return only the lowest room rate for hotels that are available.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:complexType>
	<xs:complexType name="HotelSearchCriterionType">
		<xs:annotation>
			<xs:documentation xml:lang="en">A collection of Profile objects or Unique IDs of Profiles.</xs:documentation>
		</xs:annotation>
		<xs:complexContent>
			<xs:extension base="ItemSearchCriterionType">
				<xs:sequence>
					<xs:element name="HotelAmenity" minOccurs="0" maxOccurs="10">
						<xs:annotation>
							<xs:documentation xml:lang="en">Hotel level amenities for searches.</xs:documentation>
						</xs:annotation>
						<xs:complexType>
							<xs:attribute name="Code" type="OTA_CodeType" use="optional">
								<xs:annotation>
									<xs:documentation xml:lang="en">An amenity available to guests that applies to the property as a whole (hotel level) versus an individual guest or meeting room type. Refer to OpenTravel Code List Hotel Amenity Code (HAC).</xs:documentation>
								</xs:annotation>
							</xs:attribute>
							<xs:attribute name="ComplimentaryInd" type="xs:boolean" use="optional">
								<xs:annotation>
									<xs:documentation xml:lang="en">When true, there is no charge for the amenity. When false, there is a fee associated with the amenity.</xs:documentation>
								</xs:annotation>
							</xs:attribute>
						</xs:complexType>
					</xs:element>
					<xs:element name="RoomAmenity" type="RoomAmenityPrefType" minOccurs="0" maxOccurs="5">
						<xs:annotation>
							<xs:documentation xml:lang="en">Room level amenities for searches.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="HotelFeature" minOccurs="0" maxOccurs="10">
						<xs:annotation>
							<xs:documentation xml:lang="en">Used to search for hotels based on hotel features.</xs:documentation>
						</xs:annotation>
						<xs:complexType>
							<xs:attribute name="SecurityFeatureCode" type="OTA_CodeType" use="optional">
								<xs:annotation>
									<xs:documentation xml:lang="en">Hotel security feature that is used as a qualifier when searching for properties. Refer to OpenTravel Code list Security Feature Code (SEC).</xs:documentation>
								</xs:annotation>
							</xs:attribute>
							<xs:attribute name="AccessibilityCode" type="OTA_CodeType" use="optional">
								<xs:annotation>
									<xs:documentation xml:lang="en">Hotel accessibility feature that is used as a qualifier when searching for properties. Refer to OpenTravel Code list Physically Challenged Feature Code (PHY).</xs:documentation>
								</xs:annotation>
							</xs:attribute>
						</xs:complexType>
					</xs:element>
					<xs:element name="Award" minOccurs="0" maxOccurs="5">
						<xs:annotation>
							<xs:documentation xml:lang="en">An element that identifies the hotel ratings.</xs:documentation>
						</xs:annotation>
						<xs:complexType>
							<xs:attribute name="Provider" type="xs:string" use="optional">
								<xs:annotation>
									<xs:documentation xml:lang="en">The name of the award or ratings provider.</xs:documentation>
								</xs:annotation>
							</xs:attribute>
							<xs:attribute name="Rating" type="xs:string" use="optional">
								<xs:annotation>
									<xs:documentation xml:lang="en">The actual award or rating received by the hotel facility.</xs:documentation>
								</xs:annotation>
							</xs:attribute>
						</xs:complexType>
					</xs:element>
					<xs:element name="Recreation" minOccurs="0" maxOccurs="5">
						<xs:annotation>
							<xs:documentation xml:lang="en">Identifies recreation facilities or amenities of interest.</xs:documentation>
						</xs:annotation>
						<xs:complexType>
							<xs:attribute name="Code" type="OTA_CodeType" use="optional">
								<xs:annotation>
									<xs:documentation xml:lang="en">Used to identify a specific recreation activity. Refer to OpenTravel Code list Recreation Srvc Type (RST).</xs:documentation>
								</xs:annotation>
							</xs:attribute>
						</xs:complexType>
					</xs:element>
					<xs:element name="Service" minOccurs="0" maxOccurs="99">
						<xs:annotation>
							<xs:documentation xml:lang="en">Identifies business services of interest.</xs:documentation>
						</xs:annotation>
						<xs:complexType>
							<xs:attribute name="BusinessServiceCode" type="OTA_CodeType" use="optional">
								<xs:annotation>
									<xs:documentation xml:lang="en">Used to identify a specific business service. Refer to OpenTravel Code list Business Srvc Type (BUS).</xs:documentation>
								</xs:annotation>
							</xs:attribute>
							<xs:attribute name="ServiceInventoryCode" type="StringLength1to16" use="optional">
								<xs:annotation>
									<xs:documentation xml:lang="en">The representation of the specific service being reserved.</xs:documentation>
								</xs:annotation>
							</xs:attribute>
							<xs:attribute name="Quantity" type="Numeric1to999" use="optional">
								<xs:annotation>
									<xs:documentation xml:lang="en">The number of services (e.g., tickets, rounds of golf).</xs:documentation>
								</xs:annotation>
							</xs:attribute>
						</xs:complexType>
					</xs:element>
					<xs:element name="Transportation" minOccurs="0" maxOccurs="5">
						<xs:annotation>
							<xs:documentation xml:lang="en">Identifies transportation facilities.</xs:documentation>
						</xs:annotation>
						<xs:complexType>
							<xs:attribute name="Code" type="OTA_CodeType" use="optional">
								<xs:annotation>
									<xs:documentation xml:lang="en">Used to define the mode of available transportation. Refer to OpenTravel Code List Transportation Code (TRP).</xs:documentation>
								</xs:annotation>
							</xs:attribute>
						</xs:complexType>
					</xs:element>
					<xs:element name="StayDateRange" type="DateTimeSpanType" minOccurs="0">
						<xs:annotation>
							<xs:documentation xml:lang="en">Range of dates, or fixed set of dates for Availability Request. Date range can also be specified by using start dates and number of nights (minimum, maximum or fixed).</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="RateRange" minOccurs="0" maxOccurs="5">
						<xs:annotation>
							<xs:documentation xml:lang="en">Requested rate or rate range. Repeats to allow the identification of multiple ranges for multiple room stay candidates.</xs:documentation>
						</xs:annotation>
						<xs:complexType>
							<xs:attribute name="RoomStayCandidateRPH" type="RPH_Type" use="optional">
								<xs:annotation>
									<xs:documentation xml:lang="en">Used to associate a rate range with a room stay candidate.</xs:documentation>
								</xs:annotation>
							</xs:attribute>
							<xs:attributeGroup ref="RateRangeGroup"/>
							<xs:attribute name="RateMode" type="OTA_CodeType" use="optional">
								<xs:annotation>
									<xs:documentation xml:lang="en">This supports returning a type of rate (minimum, maximum, etc.) as an alternative to returning a specific rate. Refer to OpenTravel Code List Rate Mode (RMO).</xs:documentation>
								</xs:annotation>
							</xs:attribute>
						</xs:complexType>
					</xs:element>
					<xs:element name="RatePlanCandidates" minOccurs="0">
						<xs:annotation>
							<xs:documentation xml:lang="en">Collection of requested rate plans.</xs:documentation>
						</xs:annotation>
						<xs:complexType>
							<xs:complexContent>
								<xs:extension base="RatePlanCandidatesType">
									<xs:attribute name="TaxesIncludedInd" type="xs:boolean" use="optional">
										<xs:annotation>
											<xs:documentation xml:lang="en">If TRUE, the rates prices in the response message must include all taxes and surcharges.</xs:documentation>
										</xs:annotation>
									</xs:attribute>
								</xs:extension>
							</xs:complexContent>
						</xs:complexType>
					</xs:element>
					<xs:element name="Profiles" type="ProfilesType" minOccurs="0">
						<xs:annotation>
							<xs:documentation xml:lang="en">A collection of profile objects or unique IDs of profiles.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="RoomStayCandidates" minOccurs="0">
						<xs:annotation>
							<xs:documentation xml:lang="en">Collection of room stay candidates.</xs:documentation>
						</xs:annotation>
						<xs:complexType>
							<xs:sequence>
								<xs:element name="RoomStayCandidate" type="RoomStayCandidateType" maxOccurs="unbounded">
									<xs:annotation>
										<xs:documentation xml:lang="en">Element used to identify available room products.</xs:documentation>
									</xs:annotation>
								</xs:element>
							</xs:sequence>
						</xs:complexType>
					</xs:element>
					<xs:element name="AcceptedPayments" type="AcceptedPaymentsType" minOccurs="0">
						<xs:annotation>
							<xs:documentation xml:lang="en">Provides the ability to search for hotel(s) based on whether they accept specific form(s) of payment.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="Media" minOccurs="0" maxOccurs="5">
						<xs:annotation>
							<xs:documentation xml:lang="en">Used to search for hotels based on available media content.</xs:documentation>
						</xs:annotation>
						<xs:complexType>
							<xs:attribute name="ContentCode" type="OTA_CodeType" use="optional">
								<xs:annotation>
									<xs:documentation xml:lang="en">Type of media that is used as a qualifier when searching for properties. Refer to OpenTravel Code list Content Code (CTT).</xs:documentation>
								</xs:annotation>
							</xs:attribute>
						</xs:complexType>
					</xs:element>
					<xs:element name="HotelMeetingFacility" minOccurs="0">
						<xs:annotation>
							<xs:documentation xml:lang="en">Used to search for hotels based on meeting facility requirements.</xs:documentation>
						</xs:annotation>
						<xs:complexType>
							<xs:attribute name="MeetingRoomCount" type="xs:nonNegativeInteger" use="optional">
								<xs:annotation>
									<xs:documentation xml:lang="en">The total number of unique meeting rooms provided at the hotel facility. For example, if the hotel has a grand ballroom that breaks into Salon A, B, and C -the total number of unique meeting rooms is three.</xs:documentation>
								</xs:annotation>
							</xs:attribute>
							<xs:attribute name="LargestSeatingCapacity" type="xs:nonNegativeInteger" use="optional">
								<xs:annotation>
									<xs:documentation xml:lang="en">The largest seating capacity available in the largest meeting room - generally this is for theatre-style room setup.</xs:documentation>
								</xs:annotation>
							</xs:attribute>
							<xs:attribute name="LargestRoomSpace" type="xs:nonNegativeInteger" use="optional">
								<xs:annotation>
									<xs:documentation xml:lang="en">The amount of room space for the largest unique meeting room at the hotel facility.</xs:documentation>
								</xs:annotation>
							</xs:attribute>
							<xs:attribute name="UnitOfMeasureCode" type="OTA_CodeType" use="optional">
								<xs:annotation>
									<xs:documentation xml:lang="en">The unit of measure in a code format (e.g., inches, pixels, centimeters). Refer to OpenTravel Code List Unit of Measure Code (UOM).</xs:documentation>
								</xs:annotation>
							</xs:attribute>
							<xs:attribute name="MeetingRoomCode" type="OTA_CodeType" use="optional">
								<xs:annotation>
									<xs:documentation xml:lang="en">Used as a qualifier when searching for properties. Refer to OpenTravel Code list Meeting Room Code (MRC).</xs:documentation>
								</xs:annotation>
							</xs:attribute>
						</xs:complexType>
					</xs:element>
					<xs:element name="MealPlan" minOccurs="0">
						<xs:annotation>
							<xs:documentation xml:lang="en">Used to search for hotels that offer types of meal plan, such as "all-inclusive". This matches against the static property content, not the meal included in the rate.</xs:documentation>
						</xs:annotation>
						<xs:complexType>
							<xs:attribute name="Code" type="ListOfOTA_CodeType">
								<xs:annotation>
									<xs:documentation xml:lang="en">Refer to OpenTravel Code List Meal Plan Type (MPT).</xs:documentation>
								</xs:annotation>
							</xs:attribute>
						</xs:complexType>
					</xs:element>
					<xs:element name="RebatePrograms" minOccurs="0">
						<xs:annotation>
							<xs:documentation xml:lang="en">Collection of rebate programs the hotel participates in.</xs:documentation>
						</xs:annotation>
						<xs:complexType>
							<xs:sequence>
								<xs:element name="RebateProgram" type="RebateType" maxOccurs="unbounded">
									<xs:annotation>
										<xs:documentation xml:lang="en">Information about a rebate program the hotel participates in, such as "Value Added Tax" (VAT).</xs:documentation>
									</xs:annotation>
								</xs:element>
							</xs:sequence>
						</xs:complexType>
					</xs:element>
					<xs:element ref="TPA_Extensions" minOccurs="0"/>
				</xs:sequence>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:complexType name="InvBlockRoomType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Used to define the room types and all of their supporting data within a room block.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="RoomTypeAllocations" minOccurs="0" maxOccurs="10">
				<xs:annotation>
					<xs:documentation xml:lang="en">This allows for multiple allocations to be blocked for a specific room type.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="RoomTypeAllocation" maxOccurs="unbounded">
							<xs:annotation>
								<xs:documentation xml:lang="en">This is the number of rooms blocked for a specific room type for specific dates.</xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:attributeGroup ref="DateTimeSpanGroup">
									<xs:annotation>
										<xs:documentation xml:lang="en">This is the date range to which rooms are allocated for availability by room type.</xs:documentation>
									</xs:annotation>
								</xs:attributeGroup>
								<xs:attribute name="NumberOfUnits" type="xs:integer" use="optional">
									<xs:annotation>
										<xs:documentation xml:lang="en">The number of rooms allocated as available for the dates specified in the DateTimeSpanGroup.</xs:documentation>
									</xs:annotation>
								</xs:attribute>
								<xs:attribute name="CompRoomQuantity" type="Numeric1to999" use="optional">
									<xs:annotation>
										<xs:documentation xml:lang="en">The number of complimentary rooms allocated as part of the inventory block. This may be a flat number of rooms or it may be used in conjunction with CompRoomFactor.</xs:documentation>
									</xs:annotation>
								</xs:attribute>
								<xs:attribute name="CompRoomFactor" type="Numeric1to999" use="optional">
									<xs:annotation>
										<xs:documentation xml:lang="en">Used to indicate how many rooms need to be sold before a complimentary room is granted (e.g. CompRoomQuantity="1" and CompRoomFactor="100" means 1 complimentary room is granted per 100 rooms sold). </xs:documentation>
									</xs:annotation>
								</xs:attribute>
								<xs:attribute name="EndDateIndicator" type="xs:boolean" use="optional">
									<xs:annotation>
										<xs:documentation xml:lang="en">If true, this indicator designates that the room allocation is applied through to the End date in the RoomType DateTimeSpanGroup if it exists or to the End date in the InvBlock DateTimeSpanGroup. This would be used in place of RoomTypeAllocation/Duration and RoomTypeAllocation/End.</xs:documentation>
									</xs:annotation>
								</xs:attribute>
								<xs:attribute name="SellLimit" type="xs:positiveInteger" use="optional">
									<xs:annotation>
										<xs:documentation xml:lang="en">The contracted quantity or ceiling of inventory, which may differ from the NumberOfUnits allocated.</xs:documentation>
									</xs:annotation>
								</xs:attribute>
								<xs:attribute name="ProcureBlockCode" type="StringLength1to16" use="optional">
									<xs:annotation>
										<xs:documentation xml:lang="en">The block from which to take inventory when NumberOfUnits is depleted. This is used in conjunction with the SellLimit attribute. If this attribute is not present, inventory is taken from general inventory.</xs:documentation>
									</xs:annotation>
								</xs:attribute>
								<xs:attribute name="AllocationID" type="StringLength1to32" use="optional">
									<xs:annotation>
										<xs:documentation xml:lang="en">Used to refer to a specific allocation already in the receiving system.</xs:documentation>
									</xs:annotation>
								</xs:attribute>
							</xs:complexType>
						</xs:element>
					</xs:sequence>
					<xs:attribute name="RoomTypePickUpStatus" type="OTA_CodeType" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">Refer to OpenTravel Code list (INV) Inventory Count Type.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
				</xs:complexType>
			</xs:element>
			<xs:element name="RatePlans" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">This allows for multiple rate plans for a specific room type.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="RatePlan" maxOccurs="99">
							<xs:annotation>
								<xs:documentation xml:lang="en">This is a specific rate plan defined for a specific room type.</xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:complexContent>
									<xs:extension base="RateUploadType">
										<xs:sequence>
											<xs:element name="MarketCode" minOccurs="0" maxOccurs="5">
												<xs:annotation>
													<xs:documentation xml:lang="en">This is user specific information used for tracking and  market segmentation reporting.</xs:documentation>
												</xs:annotation>
												<xs:complexType>
													<xs:attribute name="MarketCode" type="StringLength1to64" use="optional">
														<xs:annotation>
															<xs:documentation xml:lang="en">The company specific code that relates to the market being sold to (e.g. corporate, government, association, social).</xs:documentation>
														</xs:annotation>
													</xs:attribute>
													<xs:attribute name="MarketCodeName" type="StringLength1to64" use="optional">
														<xs:annotation>
															<xs:documentation xml:lang="en">The descriptive name of the code that relates to the market being sold to (e.g. corporate, government, association, social).</xs:documentation>
														</xs:annotation>
													</xs:attribute>
													<xs:attribute name="CommissionableIndicator" type="xs:boolean" use="optional">
														<xs:annotation>
															<xs:documentation xml:lang="en">If true, indicates that the rate for this market code is commissionable. The Commission element may be used to send details regarding the commission.</xs:documentation>
														</xs:annotation>
													</xs:attribute>
												</xs:complexType>
											</xs:element>
											<xs:element name="Commission" type="CommissionType" minOccurs="0">
												<xs:annotation>
													<xs:documentation xml:lang="en">Commission associated with the RatePlan. This can be a percentage or a flat amount.</xs:documentation>
												</xs:annotation>
											</xs:element>
											<xs:element name="MethodInfo" minOccurs="0" maxOccurs="5">
												<xs:annotation>
													<xs:documentation xml:lang="en">This is used to indicate the reservation and billing methods for the rate plan.</xs:documentation>
												</xs:annotation>
												<xs:complexType>
													<xs:attributeGroup ref="MethodInfoGroup"/>
												</xs:complexType>
											</xs:element>
											<xs:element name="DaysOfWeeks" minOccurs="0">
												<xs:annotation>
													<xs:documentation xml:lang="en">This is available so multiple rules or multiple date ranges may be applied.</xs:documentation>
												</xs:annotation>
												<xs:complexType>
													<xs:sequence>
														<xs:element name="DaysOfWeek" type="DOW_RulesType" maxOccurs="unbounded">
															<xs:annotation>
																<xs:documentation xml:lang="en">Used to define rate plan by day of week rules for availability, required arrival, or required stay.</xs:documentation>
															</xs:annotation>
														</xs:element>
													</xs:sequence>
												</xs:complexType>
											</xs:element>
										</xs:sequence>
										<xs:attribute name="RatePlanCode" type="StringLength1to16" use="optional">
											<xs:annotation>
												<xs:documentation xml:lang="en">The RatePlanCode assigned for the inventory item.</xs:documentation>
											</xs:annotation>
										</xs:attribute>
										<xs:attribute name="BookingCode" type="StringLength1to16" use="optional">
											<xs:annotation>
												<xs:documentation xml:lang="en">This is used to indicate the code with which to book the item and is primarily used to exchange information with GDSs or other systems.</xs:documentation>
											</xs:annotation>
										</xs:attribute>
										<xs:attributeGroup ref="PromotionCodeGroup">
											<xs:annotation>
												<xs:documentation xml:lang="en">This indicates additional special services included in the rate plan (e.g. breakfast included).</xs:documentation>
											</xs:annotation>
										</xs:attributeGroup>
										<xs:attribute name="UpgradeIndicator" type="xs:boolean" use="optional">
											<xs:annotation>
												<xs:documentation xml:lang="en">Indicator that signifies whether a room block rate plan is available for room type upgrade.</xs:documentation>
											</xs:annotation>
										</xs:attribute>
									</xs:extension>
								</xs:complexContent>
							</xs:complexType>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="DaysOfWeeks" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">This is available so multiple rules or multiple date ranges may be applied.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="DaysOfWeek" type="DOW_RulesType" maxOccurs="unbounded">
							<xs:annotation>
								<xs:documentation xml:lang="en">Used to define room type by day of week rules for availability, required arrival, or required stay.</xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
		<xs:attributeGroup ref="DateTimeSpanGroup">
			<xs:annotation>
				<xs:documentation xml:lang="en">This is the date range to which rooms are assigned to the inventory block.</xs:documentation>
			</xs:annotation>
		</xs:attributeGroup>
		<xs:attribute name="RoomTypeCode" type="StringLength1to16" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Specfic system room type code (e.g. GENR, CONC, SUIT).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:complexType>
	<xs:complexType name="InvBlockType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Used to define the details of an inventory block.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="HotelRef" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">This is used to identify the hotel to which the block applies.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:attributeGroup ref="HotelReferenceGroup"/>
				</xs:complexType>
			</xs:element>
			<xs:element name="InvBlockDates" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">This is used to indicate the date or date range applicable to this inventory block.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:attributeGroup ref="InvBlockDatesGroup"/>
				</xs:complexType>
			</xs:element>
			<xs:element name="RoomTypes" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">This allows for multiple room types to be defined within an inventory block.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="RoomType" type="InvBlockRoomType" maxOccurs="99">
							<xs:annotation>
								<xs:documentation xml:lang="en">This is a specific room type defined within an inventory block.</xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="MethodInfo" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">This is used to indicate the reservation and billing methods for a single inventory block.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:attributeGroup ref="MethodInfoGroup"/>
				</xs:complexType>
			</xs:element>
			<xs:element name="BlockDescriptions" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Collection of free form information about the inventory block.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="BlockDescription" maxOccurs="99">
							<xs:annotation>
								<xs:documentation xml:lang="en">Used for free form descriptive information about the inventory block.</xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:complexContent>
									<xs:extension base="ParagraphType">
										<xs:attributeGroup ref="DateTimeSpanGroup">
											<xs:annotation>
												<xs:documentation xml:lang="en">This may be used to define the date range for which a set of free form text applies to the inventory block.</xs:documentation>
											</xs:annotation>
										</xs:attributeGroup>
									</xs:extension>
								</xs:complexContent>
							</xs:complexType>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="Contacts" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">A collection of contacts.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="Contact" type="ContactPersonType" maxOccurs="99">
							<xs:annotation>
								<xs:documentation xml:lang="en">This may be used for multiple sets of contact information (e.g., sales manager, group contact, event manager).</xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="DestinationSystemCodes" type="DestinationSystemCodesType" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">A collection of destination system codes. These are systems for which this inventory block is targeted.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
		<xs:attributeGroup ref="InvBlockGroup"/>
	</xs:complexType>
	<xs:complexType name="InvCountType">
		<xs:sequence>
			<xs:element name="Inventory" type="BaseInvCountType" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation xml:lang="en">This is the inventory information for a given rate plan, room type, date, etc.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="UniqueID" type="UniqueID_Type" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The unique identifier element allows the trading partners to  uniquely identify each Inventory Count Message, for transaction tracability.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
		<xs:attributeGroup ref="HotelReferenceGroup"/>
	</xs:complexType>
	<xs:complexType name="LengthsOfStayType">
		<xs:annotation>
			<xs:documentation xml:lang="en">A collection of LengthOfStay.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="LengthOfStay" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation xml:lang="en">A collection of patterns defining allowable lengths of stay (LOS).</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="LOS_Pattern" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">Used to define the LOS Pattern.</xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:attribute name="FullPatternLOS" type="StringLength1to32" use="optional">
									<xs:annotation>
										<xs:documentation xml:lang="en">This may be used when MinMaxMessageType is FullPatternLOS to identify the open and closed status by LOS (e.g., if LengthsOfStay@FixedPatternLength="5" then FullPatternLOS="YYNYY" or FullPatternLOS="11011").</xs:documentation>
									</xs:annotation>
								</xs:attribute>
							</xs:complexType>
						</xs:element>
					</xs:sequence>
					<xs:attribute name="Time" type="xs:integer" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">Used in conjunction with the MinMaxMessageType and the TimeUnit to define the length of stay requirements.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="TimeUnit" type="TimeUnitType" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">A time unit used to apply this status message to other inventory, and with more granularity than daily. Values: Year, Month, Week, Day, Hour, Minute, Second.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="OpenStatusIndicator" type="xs:boolean" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">Indicates if the length of stay is open or closed when MinMaxMessageType is FullPatternLOS if true then open and if false then closed.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="MinMaxMessageType" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">An enumerated type used to define how the minimum and maximum LOS is applied.</xs:documentation>
							<xs:documentation xml:lang="en">Values: Set Minimum LOS, Remove Minimum LOS, Set Maximum LOS, Remove Maximum LOS, Set Forward Minimum Stay, Remove 	Forward 		Minimum Stay, Set Forward Maximum Stay, Remove Forward Maximum Stay.</xs:documentation>
						</xs:annotation>
						<xs:simpleType>
							<xs:restriction base="xs:string">
								<xs:enumeration value="SetMinLOS">
									<xs:annotation>
										<xs:documentation xml:lang="en">Used to set the minimum length of stay (LOS).</xs:documentation>
									</xs:annotation>
								</xs:enumeration>
								<xs:enumeration value="RemoveMinLOS">
									<xs:annotation>
										<xs:documentation xml:lang="en">Used to remove the minimum length of stay (LOS).</xs:documentation>
									</xs:annotation>
								</xs:enumeration>
								<xs:enumeration value="SetMaxLOS">
									<xs:annotation>
										<xs:documentation xml:lang="en">Used to set the maximum length of stay (LOS).</xs:documentation>
									</xs:annotation>
								</xs:enumeration>
								<xs:enumeration value="RemoveMaxLOS">
									<xs:annotation>
										<xs:documentation xml:lang="en">Used to remove the maximum length of stay (LOS).</xs:documentation>
									</xs:annotation>
								</xs:enumeration>
								<xs:enumeration value="SetForwardMinStay"/>
								<xs:enumeration value="RemoveForwardMinStay"/>
								<xs:enumeration value="SetForwardMaxStay"/>
								<xs:enumeration value="RemoveForwardMaxStay"/>
								<xs:enumeration value="FixedLOS">
									<xs:annotation>
										<xs:documentation xml:lang="en">This indicates the required length of stay (LOS).</xs:documentation>
									</xs:annotation>
								</xs:enumeration>
								<xs:enumeration value="FullPatternLOS">
									<xs:annotation>
										<xs:documentation xml:lang="en">This indicates allowable length of stay (LOS). When used, there is an option to fully define the open and closed status with the attribute FullPatternLOS in the subelement LOS_Pattern.</xs:documentation>
									</xs:annotation>
								</xs:enumeration>
								<xs:enumeration value="MinLOS">
									<xs:annotation>
										<xs:documentation xml:lang="en">Used to specify the minimum length of stay.</xs:documentation>
									</xs:annotation>
								</xs:enumeration>
								<xs:enumeration value="MaxLOS">
									<xs:annotation>
										<xs:documentation xml:lang="en">Used to specify the maximum length of stay.</xs:documentation>
									</xs:annotation>
								</xs:enumeration>
							</xs:restriction>
						</xs:simpleType>
					</xs:attribute>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="ArrivalDateBased" type="xs:boolean" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">True indicates that LOS is based on arrival date. False indicates that LOS is based on stay date.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="FixedPatternLength" type="Numeric1to999" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The number of elements in a fixed pattern length of stay (FPLOS) array.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:complexType>
	<xs:complexType name="MeetingRoomCapacityType">
		<xs:annotation>
			<xs:documentation xml:lang="en">The MeetingRoomCapacity object that defines the largest room seating capacity for a meeting room at the hotel facility.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="Occupancy" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The Occupancy details of a meeting room.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="MinRoomCharge" type="FeeType" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">The minimum room charge for this meeting room such as room rental fee.</xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
					<xs:attribute name="MinOccupancy" type="xs:nonNegativeInteger" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">The minimum number of people in this meeting room.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="MaxOccupancy" type="xs:nonNegativeInteger" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">The maximum number of people in this meeting room.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="StandardOccupancy" type="xs:nonNegativeInteger" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">The standard number of people in this meeting room.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="MeetingRoomFormatCode" type="OTA_CodeType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The code for the format of a meeting room at the hotel facility, such as U-shape, banquet, conference style, etc. A hotel may indicate all formats that apply to the meeting room. Refer to OpenTravel Code List Meeting Room Format Code (MRF).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:complexType>
	<xs:complexType name="MeetingRoomCodeType">
		<xs:sequence>
			<xs:element name="Charge" type="FeeType" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The minimum charge for this meeting room code.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="MultimediaDescriptions" type="MultimediaDescriptionsType" minOccurs="0"/>
		</xs:sequence>
		<xs:attribute name="Code" type="OTA_CodeType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">This is used to define items that may be available for any of the meeting rooms. Refer to OpenTravel Code List Meeting Room Code (MRC).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="ExistsCode" type="OTA_CodeType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">This attribute is used to explicitly define whether an amenity or service is offered. Refer to OpenTravel Code list Option Type Code (OTC). This is used in conjunction with Code.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attributeGroup ref="CodeInfoGroup">
			<xs:annotation>
				<xs:documentation xml:lang="en">May be used to give further detail on the code or to remove an obsolete item.</xs:documentation>
			</xs:annotation>
		</xs:attributeGroup>
		<xs:attributeGroup ref="QuantityGroup"/>
		<xs:attribute name="DiscountsAvailableCode" type="OTA_CodeType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">This is used for discounts that apply to meetings (eg. corporate, military). Refer to OpenTravel Code List Discounts Available (DIS).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attributeGroup ref="ID_OptionalGroup">
			<xs:annotation>
				<xs:documentation xml:lang="en">A unique identifying value assigned by the creating system.  The ID attribute may be used to reference a primary-key value within a database or in a particular implementation.</xs:documentation>
			</xs:annotation>
		</xs:attributeGroup>
	</xs:complexType>
	<xs:complexType name="MeetingRoomsType">
		<xs:annotation>
			<xs:documentation xml:lang="en">A collection of MeetingRoom objects that provide the codes and description of the meeting rooms in the hotel.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="MeetingRoom" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation xml:lang="en">Dedicated or non-dedicated space or area in which to hold a meeting.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="Codes" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">Codes or abbreviations for meeting rooms.</xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:sequence>
									<xs:element name="Code" type="MeetingRoomCodeType" maxOccurs="unbounded"/>
								</xs:sequence>
							</xs:complexType>
						</xs:element>
						<xs:element name="Dimension" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">Dimensions of the meeting room.</xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:attributeGroup ref="DimensionGroup"/>
							</xs:complexType>
						</xs:element>
						<xs:element name="AvailableCapacities" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">The amount of usable meeting space available at a property.</xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:sequence>
									<xs:element name="MeetingRoomCapacity" type="MeetingRoomCapacityType" maxOccurs="unbounded">
										<xs:annotation>
											<xs:documentation xml:lang="en">The measureable dimensions and capacities of a meeting room.</xs:documentation>
										</xs:annotation>
									</xs:element>
								</xs:sequence>
							</xs:complexType>
						</xs:element>
						<xs:element name="Features" type="FeaturesType" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">Collection of features.</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="MultimediaDescriptions" type="MultimediaDescriptionsType" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">Information describing the meeting room.</xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
					<xs:attribute name="Irregular" type="xs:boolean" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">This provides information as to whether the room has an irregular shape, if true the room would not be of a traditional square or rectangular style.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="PropertySystemName" type="xs:string" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">This is the room name as it is defined by the system. The name used internally may differ from the name used by guests.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="RoomName" type="xs:string" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">This is the meeting room name as provided to the guests.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="Sort" type="xs:nonNegativeInteger" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">This is a number assigned to rooms, usually used to define the display order.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="MeetingRoomCapacity" type="xs:nonNegativeInteger" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">The total number of people permitted in the meeting room.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attributeGroup ref="RemovalGroup"/>
					<xs:attributeGroup ref="ID_OptionalGroup">
						<xs:annotation>
							<xs:documentation xml:lang="en">This may be used to uniquely identify a meeting room.</xs:documentation>
						</xs:annotation>
					</xs:attributeGroup>
					<xs:attribute name="Access" type="xs:string" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">Used to imply the type of access to the meeting space (e.g. private access, public access, etc.)</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="MeetingRoomTypeCode" type="OTA_CodeType" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">Defines the type of the meeting room being described (eg. boardroom, ballroom, exhibit space). Refer to OpenTravel Codelist Meeting Room Format Code (MRF).</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="MeetingRoomLevel" type="StringLength1to64" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">Defines the level in the facility where the meeting room is located (i.e., lobby, mezzanine, first floor, ground, outdoor, etc.)</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="DedicatedIndicator" type="xs:boolean" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">When true, the room is used for a single purpose as indicated by the MeetingRoomTypeCode attribute.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="MeetingRoomCount" type="xs:nonNegativeInteger" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The total number of unique meeting rooms provided at the hotel facility. For example, if the hotel has a grand ballroom that breaks into Salon A, B, and C -the total number of unique meeting rooms is three.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="SmallestRoomSpace" type="xs:nonNegativeInteger" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The amount of room space for the smallest unique meeting room at the hotel facility.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="LargestRoomSpace" type="xs:nonNegativeInteger" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The amount of room space for the largest unique meeting room at the hotel facility.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attributeGroup ref="UnitsOfMeasureGroup"/>
		<xs:attribute name="TotalRoomSpace" type="xs:nonNegativeInteger" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The total amount of unique room space provided at the hotel facility. Note: when counting space, if there is grand ballroom that is comprised of Salon A, B, and C - count the total measurement of the ballroom only.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="LargestSeatingCapacity" type="xs:nonNegativeInteger" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The largest seating capacity available in the largest meeting room - generally this is for theatre-style room setup.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="SecondLargestSeatingCapacity" type="xs:nonNegativeInteger" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The second largest seating capacity available in the second largest meeting room - generally this is for theatre-style room setup. This is excluding any space within the largest meeting room.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="SmallestSeatingCapacity" type="xs:nonNegativeInteger" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The smallest seating capacity available in a meeting room - generally this is for theatre-style room setup.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="TotalRoomSeatingCapacity" type="xs:nonNegativeInteger" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The total seating capacity available at the hotel facility.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="LargestRoomHeight" type="xs:nonNegativeInteger" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The ceiling height of the largest meeting room.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:complexType>
	<xs:complexType name="MembershipType">
		<xs:annotation>
			<xs:documentation xml:lang="en">A collection of Membership objects. Memberships provides a list of reward programs which may be credited with points accrued from the guest's activity. Which memberships are to be applied to which part is determined by each object's SelectedMembershipRPHs collection.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="Membership" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation xml:lang="en">The SelectedMembership object identifies the frequent customer reward program and (optionally) indicates points awarded for stay activity.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:attribute name="ProgramCode" type="StringLength1to32" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">The code or name of the membership program ('Hertz', 'AAdvantage', etc.).</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="BonusCode" type="StringLength1to32" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">The code or name of the bonus program. BonusCode can be used to indicate the level of membership (Gold Club, Platinum member, etc.)</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="AccountID" type="StringLength1to64" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">The account identification number for this particular member in this particular program.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="PointsEarned" type="xs:integer" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">The total number of points earned through the selected membership.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="TravelSector" type="OTA_CodeType" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">Identifies the travel sector. Refer to OpenTravel Code List Travel Sector (TVS).</xs:documentation>
						</xs:annotation>
					</xs:attribute>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="MessageType">
		<xs:annotation>
			<xs:documentation xml:lang="en">If StartSeqNmbr and EndSeqNmbr are not sent, the request will be assumed to be for the last sequence number.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="OriginalPayloadStdAttributes" minOccurs="0">
				<xs:complexType>
					<xs:attributeGroup ref="OTA_PayloadStdAttributes"/>
				</xs:complexType>
			</xs:element>
			<xs:element name="MessageContent" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">This container is designed to store the response message.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
		<xs:attributeGroup ref="HotelReferenceGroup"/>
		<xs:attributeGroup ref="DateTimeSpanGroup"/>
		<xs:attribute name="StartSeqNmbr" type="xs:nonNegativeInteger" use="optional"/>
		<xs:attribute name="EndSeqNmbr" type="xs:nonNegativeInteger" use="optional"/>
		<xs:attribute name="MessageType" type="StringLength1to8" use="optional"/>
		<xs:attribute name="ResponseValue" type="StringLength1to8" use="optional"/>
		<xs:attribute name="RequestCode" type="xs:string" use="optional"/>
		<xs:attribute name="ReasonForRequest" type="xs:string" use="optional"/>
		<xs:attribute name="UserName" type="xs:string" use="optional"/>
		<xs:attribute name="RatePlanCode" type="xs:string" use="optional"/>
		<xs:attribute name="ConfirmationID" type="xs:string" use="optional"/>
		<xs:attribute name="ReservationID" type="xs:string" use="optional"/>
	</xs:complexType>
	<xs:complexType name="ProductDescriptionsType">
		<xs:annotation>
			<xs:documentation xml:lang="en">A collection of ProductDescription.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="ProductDescription" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation xml:lang="en">Provides a description of the product.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="Description" type="ParagraphType" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">A brief description of the product at this lodging facility generally used by central reservations offices or travel agents.</xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
					<xs:attribute name="ProductDescriptionRPH" type="RPH_Type">
						<xs:annotation>
							<xs:documentation xml:lang="en">This is a reference placeholder, used as an index for this object.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="PropertyValueMatchType">
		<xs:annotation>
			<xs:documentation xml:lang="en">A property that matches some or all of the search criteria.</xs:documentation>
		</xs:annotation>
		<xs:complexContent>
			<xs:extension base="BasicPropertyInfoType">
				<xs:sequence>
					<xs:element name="SearchValueMatch" minOccurs="0" maxOccurs="unbounded">
						<xs:annotation>
							<xs:documentation xml:lang="en">The string value used to search for a property is returned. The SearchValueMatch element returns the input value and offers systems the ability to generate text in the Warnings that communicate the reason for failure to locate a hotel. For example, a Reference Point search may return the following processing message; "No hotels found within 5 miles of Oswego Regional Airport".</xs:documentation>
						</xs:annotation>
						<xs:complexType>
							<xs:simpleContent>
								<xs:extension base="StringLength1to8">
									<xs:attribute name="Match" type="xs:boolean" use="required">
										<xs:annotation>
											<xs:documentation xml:lang="en">Indication of whether a match was found. The datatype is Boolean (true | false).</xs:documentation>
										</xs:annotation>
									</xs:attribute>
									<xs:attribute name="Relevance" type="Percentage">
										<xs:annotation>
											<xs:documentation xml:lang="en">An optional attribute, expressed as a decimal value, representing a percentage of 100%, used to indicate the degree to which the property identified meets the search criteria.</xs:documentation>
										</xs:annotation>
									</xs:attribute>
								</xs:extension>
							</xs:simpleContent>
						</xs:complexType>
					</xs:element>
					<xs:element name="Amenities" minOccurs="0">
						<xs:annotation>
							<xs:documentation xml:lang="en">A collection of available amenities.</xs:documentation>
						</xs:annotation>
						<xs:complexType>
							<xs:sequence>
								<xs:element name="Amenity" maxOccurs="unbounded">
									<xs:annotation>
										<xs:documentation xml:lang="en">This provides an area to pass amenity information.</xs:documentation>
									</xs:annotation>
									<xs:complexType>
										<xs:simpleContent>
											<xs:extension base="RoomAmenityPrefType">
												<xs:attribute name="PropertyAmenityType" type="OTA_CodeType" use="optional">
													<xs:annotation>
														<xs:documentation xml:lang="en">Identifies the amenities offered by the hotel. Refer to OpenTravel Code List Hotel Amenity Code (HAC).</xs:documentation>
													</xs:annotation>
												</xs:attribute>
											</xs:extension>
										</xs:simpleContent>
									</xs:complexType>
								</xs:element>
							</xs:sequence>
						</xs:complexType>
					</xs:element>
					<xs:element name="RateRange" minOccurs="0">
						<xs:annotation>
							<xs:documentation xml:lang="en">Used to provide the minimum and maximum range of rates at this hotel property.</xs:documentation>
						</xs:annotation>
						<xs:complexType>
							<xs:attributeGroup ref="RateRangeGroup">
								<xs:annotation>
									<xs:documentation xml:lang="en">Used to provide the minimum and maximum range of rates at this hotel property.</xs:documentation>
								</xs:annotation>
							</xs:attributeGroup>
							<xs:attribute name="InfoSource" type="InfoSourceType" use="optional">
								<xs:annotation>
									<xs:documentation xml:lang="en">Used to provide the source of the rate range.</xs:documentation>
								</xs:annotation>
							</xs:attribute>
							<xs:attribute name="TaxRate" type="Percentage" use="optional">
								<xs:annotation>
									<xs:documentation xml:lang="en">The tax rate at this property.</xs:documentation>
								</xs:annotation>
							</xs:attribute>
							<xs:attribute name="RateInfoNotAvailableInd" type="xs:boolean" use="optional">
								<xs:annotation>
									<xs:documentation xml:lang="en">When true, rate information is not available.</xs:documentation>
								</xs:annotation>
							</xs:attribute>
						</xs:complexType>
					</xs:element>
				</xs:sequence>
				<xs:attribute name="MoreDataEchoToken" type="StringLength1to128" use="optional">
					<xs:annotation>
						<xs:documentation xml:lang="en">The search response returns this attribute if there were additional items that could not fit within the response. The text value returned should be echoed in the subsequent request in "Criteria/Criterion@MoreDataEchoToken" to indicate where to begin the next block of data.</xs:documentation>
					</xs:annotation>
				</xs:attribute>
				<xs:attribute name="SameCountryInd" type="xs:boolean" use="optional">
					<xs:annotation>
						<xs:documentation xml:lang="en">When true, this property is in the same country as the requested city's country. When false, indicates this country is not the same as the requested city's country.</xs:documentation>
					</xs:annotation>
				</xs:attribute>
				<xs:attribute name="AvailabilityStatus" type="RateIndicatorType" use="optional">
					<xs:annotation>
						<xs:documentation xml:lang="en">The availability status of the property.</xs:documentation>
					</xs:annotation>
				</xs:attribute>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:complexType name="RateAmountMessageType">
		<xs:sequence>
			<xs:element name="StatusApplicationControl" type="StatusApplicationControlType" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Information on what the  RateAmount Message applies to (i.e. the combination of inventory and rate codes) and the period of application.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Rates" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">A collection of rate changes to be synchronized between systems.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="Rate" maxOccurs="unbounded">
							<xs:annotation>
								<xs:documentation xml:lang="en">An individual rate, which is a collection of amounts by guest, additional guests, fees, collection of related guarantee, cancel and payment policies, a description and the unique id to identify the rate. Rate restrictions can be sent along with the rate as attributes of this rate.</xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:complexContent>
									<xs:extension base="RateUploadType">
										<xs:attribute name="RateChangeIndicator" type="xs:boolean" use="optional">
											<xs:annotation>
												<xs:documentation xml:lang="en">When true, indicates a rate change is applicable. When false, a rate change does not apply.</xs:documentation>
											</xs:annotation>
										</xs:attribute>
									</xs:extension>
								</xs:complexContent>
							</xs:complexType>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="LocatorID" type="xs:positiveInteger" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Can be used to communicate back to the sender exactly which transaction may have had a problem (e.g. "Message 214 had an invalid date range").</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:complexType>
	<xs:complexType name="RateLiteType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Summary version of the RateType, initially created for the Travel Itinerary Message set.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="Rate" type="AmountLiteType" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation xml:lang="en">The Lite Rate contains the Base amount as well as the associated taxes.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="RatePlanCandidatesType">
		<xs:sequence>
			<xs:element name="RatePlanCandidate" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation xml:lang="en">Element used to identify available products and rates.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="HotelRefs" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">A collection of hotel identifiers to which the rate plan applies.</xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:sequence>
									<xs:element name="HotelRef" maxOccurs="unbounded">
										<xs:annotation>
											<xs:documentation xml:lang="en">Information to identify one or more hotels.</xs:documentation>
										</xs:annotation>
										<xs:complexType>
											<xs:attributeGroup ref="HotelReferenceGroup"/>
										</xs:complexType>
									</xs:element>
								</xs:sequence>
							</xs:complexType>
						</xs:element>
						<xs:element name="MealsIncluded" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">Defines which meals are included with this rate plan.</xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:attributeGroup ref="MealsIncludedGroup">
									<xs:annotation>
										<xs:documentation xml:lang="en">Defines which meals are included with this rate plan.</xs:documentation>
									</xs:annotation>
								</xs:attributeGroup>
							</xs:complexType>
						</xs:element>
						<xs:element name="ArrivalPolicy" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">Defines policy types for this rate plan.</xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:attribute name="GuaranteePolicyIndicator" type="xs:boolean" use="optional">
									<xs:annotation>
										<xs:documentation xml:lang="en">When true, return rates with a guarantee policy.</xs:documentation>
									</xs:annotation>
								</xs:attribute>
								<xs:attribute name="DepositPolicyIndicator" type="xs:boolean" use="optional">
									<xs:annotation>
										<xs:documentation xml:lang="en">When true, return rates with a deposit policy.</xs:documentation>
									</xs:annotation>
								</xs:attribute>
								<xs:attribute name="HoldTimePolicyIndicator" type="xs:boolean" use="optional">
									<xs:annotation>
										<xs:documentation xml:lang="en">When true, return rates with a hold time policy.</xs:documentation>
									</xs:annotation>
								</xs:attribute>
							</xs:complexType>
						</xs:element>
						<xs:element name="RatePlanCommission" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">Defines commission attributes for this rate plan.</xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:attribute name="MaxCommissionPercentage" type="Percentage" use="optional">
									<xs:annotation>
										<xs:documentation xml:lang="en">Defines the maximum commission percentage requested.</xs:documentation>
									</xs:annotation>
								</xs:attribute>
								<xs:attribute name="MinCommissionPercentage" type="Percentage" use="optional">
									<xs:annotation>
										<xs:documentation xml:lang="en">Defines the minimum commission percentage requested.</xs:documentation>
									</xs:annotation>
								</xs:attribute>
								<xs:attribute name="CommissionableIndicator" type="xs:boolean" use="optional">
									<xs:annotation>
										<xs:documentation xml:lang="en">When true, indicates the rate requested is commissionable.</xs:documentation>
									</xs:annotation>
								</xs:attribute>
							</xs:complexType>
						</xs:element>
					</xs:sequence>
					<xs:attributeGroup ref="RatePlanGroup"/>
					<xs:attribute name="RPH" type="RPH_Type" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">A unique identifier for this rate plan candidate.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="PrepaidQualifier" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">Qualifies whether the response should include prepaid rates, exclude prepaid rates, or include prepaid rates only.</xs:documentation>
						</xs:annotation>
						<xs:simpleType>
							<xs:restriction base="xs:NMTOKEN">
								<xs:enumeration value="IncludePrepaid">
									<xs:annotation>
										<xs:documentation xml:lang="en">Return prepaid and non-prepaid rates.</xs:documentation>
									</xs:annotation>
								</xs:enumeration>
								<xs:enumeration value="PrepaidOnly">
									<xs:annotation>
										<xs:documentation xml:lang="en">Only return prepaid rates.</xs:documentation>
									</xs:annotation>
								</xs:enumeration>
								<xs:enumeration value="ExcludePrepaid">
									<xs:annotation>
										<xs:documentation xml:lang="en">Exclude prepaid rates from the response.</xs:documentation>
									</xs:annotation>
								</xs:enumeration>
							</xs:restriction>
						</xs:simpleType>
					</xs:attribute>
					<xs:attribute name="AvailRatesOnlyInd" type="xs:boolean" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">If TRUE, the response should include ONLY those rates that are available in the date range specified. If FALSE, all rates are to be returned.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="RatePlanLiteType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Summary version of the RoomPlanType, initially created for the Travel Itinerary Message set.</xs:documentation>
		</xs:annotation>
		<xs:sequence minOccurs="0">
			<xs:element name="Guarantee" type="GuaranteeType" minOccurs="0" maxOccurs="5">
				<xs:annotation>
					<xs:documentation xml:lang="en">Guarantee information that applies to the rate plan. A maximum of 5 occurances are available for use depending on the context.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="RatePlanDescription" type="ParagraphType" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Describes the rate plan.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="RatePlanCode" type="StringLength1to16">
			<xs:annotation>
				<xs:documentation xml:lang="en">The RatePlanCode assigned by the receiving system for the inventory item in response to a new rate plan notification. (Implementation Notes: This would only be returned when the notification is of type New and the sender is translating RatePlanCode values. On subsequent transactions for this rate plan, the sender would populate the RatePlanCode attribute with this value returned by the receiver.)</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attributeGroup ref="EffectiveExpireOptionalDateGroup"/>
		<xs:attribute name="RateIndicator" type="RateIndicatorType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Information pertaining to the availability of the rate plan.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="RatePlanType" type="OTA_CodeType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">An enumerated type that allows the query to specify a rate category type, and provides major categories for comparison across brands. Refer to OpenTravel Code List Rate Plan Type (RPT).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="RatePlanID" type="StringLength1to64" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">A text field used to indicate a special  ID code that is associated with the rate and is  required in the reservation request in order to obtain the rate. Examples are Corporate ID and  Promotion Code.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:complexType>
	<xs:complexType name="RatePlanType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Defines the details of the rate plan as used in the booking process.</xs:documentation>
			<xs:documentation xml:lang="en">Policies and descriptions that apply to a rate plan.</xs:documentation>
			<xs:documentation xml:lang="en">Information significant to defining a rate plan.</xs:documentation>
		</xs:annotation>
		<xs:sequence minOccurs="0">
			<xs:element name="Guarantee" type="GuaranteeType" minOccurs="0" maxOccurs="5">
				<xs:annotation>
					<xs:documentation xml:lang="en">Guarantee information that applies to the rate plan. A maximum of 5 occurances are available for use depending on the context.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="CancelPenalties" type="CancelPenaltiesType" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Collection of cancellation penalties.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="RatePlanDescription" type="ParagraphType" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Describes the rate plan.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="RatePlanInclusions" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Defines charges that are included in this rate plan.This element allows for future extension should there be a need.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="RatePlanInclusionDesciption" type="ParagraphType" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">Description of what is included in the rate plan.</xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
					<xs:attribute name="TaxInclusive" type="xs:boolean" use="optional"/>
					<xs:attribute name="ServiceFeeInclusive" type="xs:boolean" use="optional"/>
				</xs:complexType>
			</xs:element>
			<xs:element name="Commission" type="CommissionType" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Commission associated with the RatePlan. This can be a percentage or a flat amount.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="MealsIncluded" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Defines which meals are included with this rate program.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:attributeGroup ref="MealsIncludedGroup">
						<xs:annotation>
							<xs:documentation xml:lang="en">Defines which meals are included with this rate program.</xs:documentation>
						</xs:annotation>
					</xs:attributeGroup>
				</xs:complexType>
			</xs:element>
			<xs:element name="RestrictionStatus" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Used to indicate whether the rate is on request or available.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:attributeGroup ref="RestrictionStatusGroup"/>
				</xs:complexType>
			</xs:element>
			<xs:element name="AdditionalDetails" type="AdditionalDetailsType" minOccurs="0"/>
		</xs:sequence>
		<xs:attribute name="BookingCode" type="StringLength1to16" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">This is used to indicate the item booked and is primarily used to exchange information with GDSs.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="RatePlanCode" type="StringLength1to16" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The RatePlanCode assigned by the receiving system for the inventory item in response to a new rate plan notification. (Implementation Notes: This would only be returned when the notification is of type New and the sender is translating RatePlanCode values. On subsequent transactions for this rate plan, the sender would populate the RatePlanCode attribute with this value returned by the receiver.)</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attributeGroup ref="EffectiveExpireOptionalDateGroup"/>
		<xs:attribute name="RateIndicator" type="RateIndicatorType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Information pertaining to the availability of the rate plan.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="RatePlanType" type="OTA_CodeType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">An enumerated type that allows the query to specify a rate category type, and provides major categories for comparison across brands. Refer to OpenTravel Code List Rate Plan Type (RPT).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="RatePlanID" type="StringLength1to64" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">A text field used to provide a special  ID code that is associated with the rate and is required in the reservation request in order to obtain the rate. Examples are a corporate ID number, a promotion code or a membership number.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="RatePlanName" type="StringLength1to64" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Provides the name of the rate plan or group. Typically used with RatePlanType to further describe the rate plan.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="MarketCode" type="StringLength1to64" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The code that relates to the market being sold to (e.g., the corporate market, packages).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="AvailabilityStatus" type="RateIndicatorType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Used to specify an availability status for the rate plan.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="ID_RequiredInd" type="xs:boolean" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">When true, indicates proof of qualification for this rate is required.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="PriceViewableInd" type="xs:boolean" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">When true, the price for this rate plan can be viewed by the guest. When false, the guest must contact another entity to obtain price information.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="QualificationType" type="OTA_CodeType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Indicates the type of document required by the guest to qualify for this rate plan. Refer to OpenTravel Code List Document Type (DOC).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="AvailableQuantity" type="xs:integer" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Provides the number of rooms available within this rate plan.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="PrepaidIndicator" type="xs:boolean" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">When true, indicates if the rate is a prepaid rate.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:complexType>
	<xs:complexType name="RateType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Individual rate amount. This rate is valid for a range of number of occupants and an occupant type.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="Rate" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation xml:lang="en">The  Rate contains a collection of elements that define the amount of the rate, associated fees, additional occupant amounts as well as payment and cancellation policies. Taxes can be broken out or included within the various amounts. A currency can be associated to each amount  The applicable period of the the rate are indicated by the effective dates. Restrictions that may apply to that rate, such as the minimum or maximum length of stay,  stay-over dates (such as a Saturday night), min/max guests applicable for the rate, and age group (ex Adult) are attributes of Rate. It indicates the number of units that the quoted rate is based upon, as well as the TimeUnits type used that the rate is based upon, e.g.: 3days at $100.00 per day.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:complexContent>
						<xs:extension base="AmountType">
							<xs:sequence>
								<xs:element ref="TPA_Extensions" minOccurs="0"/>
							</xs:sequence>
							<xs:attribute name="Duration" type="xs:duration" use="optional"/>
							<xs:attribute name="RateMode" type="OTA_CodeType" use="optional">
								<xs:annotation>
									<xs:documentation xml:lang="en">This supports returning a type of rate (minimum, maximum, etc.) as an alternative to returning a specific rate. Refer to OpenTravel Code List Rate Mode (RMO).</xs:documentation>
								</xs:annotation>
							</xs:attribute>
							<xs:attribute name="CachedIndicator" type="xs:boolean" use="optional">
								<xs:annotation>
									<xs:documentation xml:lang="en">When true, this indicates the rate data is not real-time.</xs:documentation>
								</xs:annotation>
							</xs:attribute>
							<xs:attribute name="RateSource" type="StringLength1to32" use="optional">
								<xs:annotation>
									<xs:documentation xml:lang="en">The source from which the rate was acquired.</xs:documentation>
								</xs:annotation>
							</xs:attribute>
							<xs:attribute name="RateTypeCode" type="OTA_CodeType" use="optional">
								<xs:annotation>
									<xs:documentation xml:lang="en">Used to identify the type of rate. Refer to OpenTravel Code list Rate Plan Type (RPT).</xs:documentation>
								</xs:annotation>
							</xs:attribute>
							<xs:attribute name="RoomPricingType" type="PricingType" use="optional">
								<xs:annotation>
									<xs:documentation xml:lang="en">Specifies how the room is priced (per night, per person, etc.).</xs:documentation>
								</xs:annotation>
							</xs:attribute>
						</xs:extension>
					</xs:complexContent>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="RateUploadType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Defines the details of a rate.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="BaseByGuestAmts" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">A collection of Base charges by number of guests.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="BaseByGuestAmt" maxOccurs="unbounded">
							<xs:annotation>
								<xs:documentation xml:lang="en">Base charge for a given number of guests for a given age qualifying code.</xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:complexContent>
									<xs:extension base="TotalType">
										<xs:sequence>
											<xs:element name="NumberOfGuestsDescription" type="ParagraphType" minOccurs="0" maxOccurs="9">
												<xs:annotation>
													<xs:documentation xml:lang="en">Description of number of guests and the associated age group (ex 2 Adults).</xs:documentation>
												</xs:annotation>
											</xs:element>
										</xs:sequence>
										<xs:attribute name="Code" type="StringLength1to16">
											<xs:annotation>
												<xs:documentation xml:lang="en">The code associated with this base charge.</xs:documentation>
											</xs:annotation>
										</xs:attribute>
										<xs:attribute name="NumberOfGuests" type="Numeric1to999" use="optional">
											<xs:annotation>
												<xs:documentation xml:lang="en">The number of guests associated with this base charge.</xs:documentation>
											</xs:annotation>
										</xs:attribute>
										<xs:attributeGroup ref="AgeQualifyingGroup"/>
									</xs:extension>
								</xs:complexContent>
							</xs:complexType>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="AdditionalGuestAmounts" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">A collection of charges that apply to for additional occupants, guests or service users (over and above the rate's MaxGuest Applicable).</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="AdditionalGuestAmount" maxOccurs="unbounded">
							<xs:annotation>
								<xs:documentation xml:lang="en">Collection of incremental charges per age qualifying code for additional guests. Amount charged for additional occupant is with respect to age group of the base guests.</xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:sequence>
									<xs:element name="Taxes" type="TaxesType" minOccurs="0"/>
									<xs:element name="AddlGuestAmtDescription" type="ParagraphType" minOccurs="0" maxOccurs="9">
										<xs:annotation>
											<xs:documentation xml:lang="en">Text description of the AdditionalGuestAmount in a given language.</xs:documentation>
										</xs:annotation>
									</xs:element>
								</xs:sequence>
								<xs:attribute name="TaxInclusive" type="xs:boolean" use="optional">
									<xs:annotation>
										<xs:documentation xml:lang="en">Indicates whether taxes are included when figuring the additional occupant amounts.</xs:documentation>
									</xs:annotation>
								</xs:attribute>
								<xs:attribute name="MaxAdditionalGuests" type="Numeric1to999" use="optional">
									<xs:annotation>
										<xs:documentation xml:lang="en">Increase the base rate by the additional occupant amount for each additional occupant of the same age group up to this maximum number of occupants of this age group.</xs:documentation>
									</xs:annotation>
								</xs:attribute>
								<xs:attributeGroup ref="AgeQualifyingGroup"/>
								<xs:attributeGroup ref="FeeTaxGroup"/>
							</xs:complexType>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="Fees" type="FeesType" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">A collection of Fees.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="GuaranteePolicies" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">A collection of Guarantee Policies.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="GuaranteePolicy" type="GuaranteeType" maxOccurs="unbounded">
							<xs:annotation>
								<xs:documentation xml:lang="en">GuaranteePolicy.</xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="CancelPolicies" type="CancelPenaltiesType" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">A collection of Cancellation Policies.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="PaymentPolicies" type="RequiredPaymentsType" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">A collection of Payment Policies.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="RateDescription" type="ParagraphType" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Desription of Rate being uploaded.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="UniqueID" type="UniqueID_Type" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The unique identifier element allows the trading partners to uniquely identify each Rate being uploaded, for traceable transactions.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="MealsIncluded" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Defines which meals are included with this rate program.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:attributeGroup ref="MealsIncludedGroup">
						<xs:annotation>
							<xs:documentation xml:lang="en">Defines which meals are included with this rate program.</xs:documentation>
						</xs:annotation>
					</xs:attributeGroup>
				</xs:complexType>
			</xs:element>
			<xs:element name="AdditionalCharges" type="HotelAdditionalChargesType" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Breakout of additional charges as part of the rate plan.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="NumberOfUnits" type="xs:integer" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Indicates the number of rooms blocked or capped for this rate plan.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="RateTimeUnit" type="TimeUnitType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Enumeration of time units upon which the RateAmount is based (e.g., daily, weekly, single rate for full stay).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="UnitMultiplier" type="Numeric1to999" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The number of RateTimeUnits that the rate Amount is based upon (e.g., flat rate for 3 days).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="MinGuestApplicable" type="Numeric1to999" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Minimum number of occupants for which this rate is valid (ex 1).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="MaxGuestApplicable" type="Numeric1to999" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Maximum number of occupants for which this rate is valid (ex 2).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attributeGroup ref="AgeQualifyingGroup"/>
		<xs:attribute name="MinLOS" type="StringLength1to16" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The minimum length of stay required by this rate plan.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="MaxLOS" type="StringLength1to16" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The maximum length of stay allowed by this rate plan.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="StayOverDate" type="DayOfWeekType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Indicates a specific day on which the guest must stay over in order to be eligible for the quoted rate plan.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attributeGroup ref="DateTimeSpanGroup"/>
		<xs:attributeGroup ref="DOW_PatternGroup"/>
		<xs:attributeGroup ref="CurrencyCodeGroup"/>
		<xs:attribute name="RateTier" type="StringLength1to64" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Hotel systems often have different tiers for a given rate plan; this attribute is used to designate a specific tier within the rate plan (e.g. high, medium, low).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:complexType>
	<xs:complexType name="RequiredPaymentLiteType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Summary version of the RequiredPaymentType, initially created for the Travel Itinerary Message set.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="AcceptedPayments" type="AcceptedPaymentsType" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Collection of forms of payment accepted for payment.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="AmountPercent" type="AmountPercentType" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Payment expressed as a fixed amount, or a percentage of/or room nights.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="RetributionType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">An enumerated type defining the type of action taken when the deadline has been exceeded. Valid values: Res Automatically Cancelled, Res No Longer Guaranteed.</xs:documentation>
			</xs:annotation>
			<xs:simpleType>
				<xs:restriction base="StringLength1to32">
					<xs:enumeration value="ResAutoCancelled"/>
					<xs:enumeration value="ResNotGuaranteed"/>
				</xs:restriction>
			</xs:simpleType>
		</xs:attribute>
	</xs:complexType>
	<xs:complexType name="RequiredPaymentsType">
		<xs:annotation>
			<xs:documentation xml:lang="en">A collection of required payments.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="GuaranteePayment" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation xml:lang="en">Used to define the deposit policy, guarantees policy, and/or accepted forms of payment.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="AcceptedPayments" type="AcceptedPaymentsType" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">Collection of forms of payment accepted for payment.</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="AmountPercent" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">Payment expressed as a fixed amount, or a percentage of/or room nights.</xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:complexContent>
									<xs:extension base="AmountPercentType">
										<xs:attribute name="OverriddenAmountIndicator" type="xs:boolean" use="optional">
											<xs:annotation>
												<xs:documentation xml:lang="en">When true, indicates that the amount has been overridden.</xs:documentation>
											</xs:annotation>
										</xs:attribute>
									</xs:extension>
								</xs:complexContent>
							</xs:complexType>
						</xs:element>
						<xs:element name="Deadline" minOccurs="0" maxOccurs="2">
							<xs:annotation>
								<xs:documentation xml:lang="en">Payment deadline, absolute or relative.</xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:attributeGroup ref="DeadlineGroup"/>
								<xs:attribute name="OverrideIndicator" type="xs:boolean" use="optional">
									<xs:annotation>
										<xs:documentation xml:lang="en">When true, indicates that the deadline has been overridden.</xs:documentation>
									</xs:annotation>
								</xs:attribute>
							</xs:complexType>
						</xs:element>
						<xs:element name="Description" type="ParagraphType" minOccurs="0" maxOccurs="5">
							<xs:annotation>
								<xs:documentation xml:lang="en">Text description of the Payment in a given language.</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="Address" minOccurs="0" maxOccurs="3">
							<xs:annotation>
								<xs:documentation xml:lang="en">The address to which a deposit may be sent.</xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:complexContent>
									<xs:extension base="AddressInfoType">
										<xs:attribute name="AddresseeName" type="StringLength1to64" use="optional">
											<xs:annotation>
												<xs:documentation xml:lang="en">The name of the reciever of the payment.</xs:documentation>
											</xs:annotation>
										</xs:attribute>
									</xs:extension>
								</xs:complexContent>
							</xs:complexType>
						</xs:element>
						<xs:element ref="TPA_Extensions" minOccurs="0"/>
					</xs:sequence>
					<xs:attribute name="RetributionType" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">An enumerated type defining the type of action taken when the deadline has been exceeded. Valid values: Res Automatically Cancelled, Res No Longer Guaranteed.</xs:documentation>
						</xs:annotation>
						<xs:simpleType>
							<xs:restriction base="StringLength1to32">
								<xs:enumeration value="ResAutoCancelled"/>
								<xs:enumeration value="ResNotGuaranteed"/>
							</xs:restriction>
						</xs:simpleType>
					</xs:attribute>
					<xs:attribute name="PaymentCode" type="StringLength1to8" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">This defines the form of payment. Recommended usage of this is with the Payment Type in OpenTravel Code List, this datatype will be updated in the future. In order to maintain forward compatability a change is not being made in this publication. This will be corrected in a future version, when a major update is released.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="Type" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">Used to specify the type of information being sent (i.e., RequiredPayment, GuaranteePolicy, AcceptedPaymentForms).</xs:documentation>
						</xs:annotation>
						<xs:simpleType>
							<xs:restriction base="xs:string">
								<xs:enumeration value="RequiredPayment">
									<xs:annotation>
										<xs:documentation xml:lang="en">A required payment, such as a deposit due or payment in the event of cancellation.</xs:documentation>
									</xs:annotation>
								</xs:enumeration>
								<xs:enumeration value="GuaranteePolicy">
									<xs:annotation>
										<xs:documentation xml:lang="en">Used to define policies required to guarantee a reservation at the property (e.g., room(s), function space(s)).</xs:documentation>
									</xs:annotation>
								</xs:enumeration>
								<xs:enumeration value="AcceptedPaymentForms">
									<xs:annotation>
										<xs:documentation xml:lang="en">Used to define the acceptable payment forms of the property.</xs:documentation>
									</xs:annotation>
								</xs:enumeration>
							</xs:restriction>
						</xs:simpleType>
					</xs:attribute>
					<xs:attribute name="GuaranteeCode" type="StringLength1to32" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">Guarantee Code</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="GuaranteeType" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">GuaranteeType: GuaranteeType An enumerated type defining the guarantee to be applied to this reservation.</xs:documentation>
						</xs:annotation>
						<xs:simpleType>
							<xs:restriction base="StringLength1to32">
								<xs:enumeration value="GuaranteeRequired"/>
								<xs:enumeration value="None"/>
								<xs:enumeration value="CC/DC/Voucher"/>
								<xs:enumeration value="Profile"/>
								<xs:enumeration value="Deposit"/>
								<xs:enumeration value="PrePay">
									<xs:annotation>
										<xs:documentation xml:lang="en">Indicates prepayment, typically this means payment is required at booking.</xs:documentation>
									</xs:annotation>
								</xs:enumeration>
							</xs:restriction>
						</xs:simpleType>
					</xs:attribute>
					<xs:attribute name="HoldTime" type="xs:time" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">The room will held up until this time without a guarantee.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attributeGroup ref="DOW_PatternGroup"/>
					<xs:attributeGroup ref="DateTimeSpanGroup"/>
					<xs:attribute name="NoCardHolderInfoReqInd" type="xs:boolean" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en"> If true, no credit cardholder information is required for guarantee/deposit.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="NameInd" type="xs:boolean" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">If true, the credit cardholder name is required for guarantee/deposit.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="AddressInd" type="xs:boolean" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">If true, the credit cardholder address is required for guarantee/deposit.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="PhoneInd" type="xs:boolean" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">If true, the credit cardholder phone number is required for guarantee/deposit.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="InterbankNbrInd" type="xs:boolean" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">If true, the Interbank Card Association number is required for guarantee/deposit.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="RoomTypeCode" type="StringLength1to16" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">A system specific room type to which this guarantee payment information applies.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="InfoSource" type="InfoSourceType" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">To specify the source of the rate of exchange for a currency code.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="NonRefundableIndicator" type="xs:boolean" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">When true, indicates that any prepayment for the reservation is non refundable, therefore a 100% penalty on the prepayment is applied, irrespective of deadline.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="PolicyCode" type="StringLength1to16" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">Used to specify the type of payment policy.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="AgencyNameAddrReqInd" type="xs:boolean" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">If TRUE, the agency name and address are required for guarantee/deposit.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="CompanyNameAddrReqInd" type="xs:boolean" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">If TRUE, the company name and address are required for guarantee/deposit.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="RFP_ResponseDetailType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Provides an area to send comments regarding the response.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="Comments" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Collection of comments used for additional data about the response.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="Comment" type="ParagraphType" maxOccurs="99">
							<xs:annotation>
								<xs:documentation xml:lang="en">Open comments to send additional data about the response.</xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="DetailIncludedIndicator" type="xs:boolean" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">This is an indicator that denotes whether the message contains detailed information; if false this indicates the message is an acknowledgement only, if true this indicates that detailed information is included in this message.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attributeGroup ref="DeliveryResponseGroup">
			<xs:annotation>
				<xs:documentation xml:lang="en">This is used to send information regarding when and how the detailed response will be delivered.</xs:documentation>
			</xs:annotation>
		</xs:attributeGroup>
		<xs:attribute name="DeclineIndicator" type="xs:boolean" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">If true, the supplier cannot accomodate the request and will not send detailed response information. If false, the supplier plans to respond.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="DeclineReasonCode" type="OTA_CodeType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Identifies the reason the request is being declined. Refer to OpenTravel Code List Decline Reason (DEC).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="CodeDetail" type="StringLength1to64" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">This is intended for use as a customized DeclineReasonCode. This may be used when "other" is stated as the DeclineReasonCode or to give additional information with other DeclineReasonCodes.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:complexType>
	<xs:complexType name="RoomRateLiteType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Summary version of the RoomRateType, initially created for the Travel Itinerary Message set.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="Rates" type="RateLiteType"/>
		</xs:sequence>
		<xs:attributeGroup ref="EffectiveExpireOptionalDateGroup"/>
		<xs:attribute name="RoomTypeCode" type="StringLength1to16" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Specfic system room type code, ex: A1K, A1Q etc.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="InvBlockCode" type="StringLength1to32" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Code that identifies an inventory block.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="NumberOfUnits" type="xs:integer" use="optional"/>
		<xs:attributeGroup ref="RatePlanGroup"/>
	</xs:complexType>
	<xs:complexType name="RoomRateType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Individual rate amount. This rate is valid for a range of number of occupants and an occupant type.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="Rates" type="RateType" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Individual rate amount. This rate is valid for a range of number of occupants and an occupant type.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="RoomRateDescription" type="ParagraphType" minOccurs="0" maxOccurs="20">
				<xs:annotation>
					<xs:documentation xml:lang="en">The description or name of a room rate.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Features" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">A collection of features that are made available as part of this speciific room and this specifiic rate plan.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="Feature" minOccurs="0" maxOccurs="99">
							<xs:annotation>
								<xs:documentation xml:lang="en">Allows for the description of any features and/or amenities that are made available as part of this specific room and this specifiic rate plan, for example, large screen television, video/DVD player in room, room service breakfast, and details about that breakfast.</xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:sequence>
									<xs:element name="Description" type="ParagraphType" minOccurs="0" maxOccurs="5"/>
								</xs:sequence>
								<xs:attribute name="RoomAmenity" type="OTA_CodeType" use="optional">
									<xs:annotation>
										<xs:documentation xml:lang="en">Refer to OpenTravel Code List Room Amenity Type (RMA).</xs:documentation>
									</xs:annotation>
								</xs:attribute>
								<xs:attribute name="Quantity" type="Numeric0to999" use="optional">
									<xs:annotation>
										<xs:documentation xml:lang="en">Provides the quantity of the amenity in the room. When zero, the amenity is either unavailable or not allowed.</xs:documentation>
									</xs:annotation>
								</xs:attribute>
								<xs:attribute name="RoomViewCode" type="OTA_CodeType" use="optional">
									<xs:annotation>
										<xs:documentation xml:lang="en">Specifies the type of view a room has. Refer to OpenTravel Code List Room View Type (RVT).</xs:documentation>
									</xs:annotation>
								</xs:attribute>
							</xs:complexType>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="Total" type="TotalType" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The total of all rates for this room rate type.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Availability" minOccurs="0" maxOccurs="5">
				<xs:annotation>
					<xs:documentation xml:lang="en">Provides information on the availability of the room rate.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:attribute name="AvailabilityStatus" type="RateIndicatorType" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">Used to specify an availability status for the room rate.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="BookingCode" type="StringLength1to16" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">This is used to indicate the item booked and is primarily used to exchange information with GDSs.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attributeGroup ref="EffectiveExpireOptionalDateGroup"/>
		<xs:attribute name="RoomTypeCode" type="StringLength1to16" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Specfic system room type code, ex: A1K, A1Q etc.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="InvBlockCode" type="StringLength1to32" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Code that identifies an inventory block.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="NumberOfUnits" type="xs:integer" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The number of rooms.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attributeGroup ref="RatePlanGroup"/>
		<xs:attribute name="AvailabilityStatus" type="RateIndicatorType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Used to specify an availability status for the room rate.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="RoomID" type="StringLength1to16" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">A string value representing the unique identification of a room.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:complexType>
	<xs:complexType name="RoomStayLiteType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Summary version of the RoomStayType, initially created for the Travel Itinerary Message set.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="RoomTypes" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Room type for reservation.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="RoomType" type="RoomTypeLiteType" maxOccurs="9"/>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="RatePlans" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Rate Plan info.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="RatePlan" type="RatePlanLiteType" maxOccurs="9"/>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="RoomRates" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Reservation rate(s).</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="RoomRate" type="RoomRateLiteType" maxOccurs="9"/>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="GuestCounts" type="GuestCountType" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Number of guests associated with this reservation.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="TimeSpan" type="DateTimeSpanType" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Date and time of check-in and check-out.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Guarantee" type="GuaranteeType" minOccurs="0" maxOccurs="5">
				<xs:annotation>
					<xs:documentation xml:lang="en">The guarantee information associated to the Room Stay. A maximum of 5 occurances are available for use depending on the context.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="DepositPayment" type="RequiredPaymentLiteType" minOccurs="0" maxOccurs="5">
				<xs:annotation>
					<xs:documentation xml:lang="en">A collection of deposit and/or payments for the Room Stay. A maximum of 5 occurances are available for use depending on the context.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="BasicPropertyInfo" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Basic hotel property information.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:attributeGroup ref="HotelReferenceGroup"/>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="RoomSharesType">
		<xs:annotation>
			<xs:documentation xml:lang="en">A collection of RoomShare.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="RoomShare" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation xml:lang="en">Guests to share a room.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="GuestRPHs" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">Collection of Guest RPH items.</xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:sequence>
									<xs:element name="GuestRPH" maxOccurs="99">
										<xs:annotation>
											<xs:documentation xml:lang="en">References one of the guest(s) staying in this room.</xs:documentation>
										</xs:annotation>
										<xs:complexType>
											<xs:simpleContent>
												<xs:extension base="RPH_Type">
													<xs:attributeGroup ref="DateTimeSpanGroup"/>
												</xs:extension>
											</xs:simpleContent>
										</xs:complexType>
									</xs:element>
								</xs:sequence>
							</xs:complexType>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="RoomStayCandidateType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Used to identify available room products.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="GuestCounts" type="GuestCountType" minOccurs="0"/>
			<xs:element name="RoomAmenity" type="RoomAmenityPrefType" minOccurs="0" maxOccurs="5">
				<xs:annotation>
					<xs:documentation xml:lang="en">This element is used to pass room attributes. Typical attributes are smoking, non-smoking, wheelchair access,  room location (low floor, high floor, close to elevator, etc) and view (garden, pool, ocean, etc.)</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
		<xs:attributeGroup ref="RoomGroup"/>
		<xs:attribute name="RPH" type="RPH_Type" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">A unique identifier for this room stay candidate.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="RatePlanCandidateRPH" type="RPH_Type" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Used to refer to the Rate Plan Candidate associated with this room stay.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="BookingCode" type="StringLength1to16" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Used to specify the booking code for which availability is requested.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attributeGroup ref="EffectiveExpireOptionalDateGroup">
			<xs:annotation>
				<xs:documentation xml:lang="en">Identifies the dates when the RoomStayCandidate is applicable when there are multiple room stay requirements within a StayDateRange.</xs:documentation>
			</xs:annotation>
		</xs:attributeGroup>
	</xs:complexType>
	<xs:complexType name="RoomStayType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Details on the Room Stay including Guest Counts, Time Span of this Room Stay, pointers to Res Guests, guest Memberships, Comments and Special Requests pertaining to this particular Room Stay and finally finacial information related to the Room Stay, including Guarantee, Deposit and Payment and Cancellation Penalties.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="RoomTypes" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">A collection of Room Types associated with a particular Room Stay.</xs:documentation>
					<xs:documentation xml:lang="en">The RoomType element is used to contain all the room type information for a single RateType Code (ex A1K) for a given date range.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="RoomType" type="RoomTypeType" maxOccurs="unbounded"/>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="RatePlans" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">A collection of Rate Plans associated with a particular Room Stay.</xs:documentation>
					<xs:documentation xml:lang="en">The rate plan element is used to contain all the rate information for a single Rate Plan Code (ex RACK) for a given date range. A given Rate Plan may have variable rates, over the effective period of the Rate Plan, this is represented by the child element Rates.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="RatePlan" type="RatePlanType" maxOccurs="unbounded"/>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="RoomRates" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">A collection of Room Rates associated with a particular Room Stay. Each Room Rate combination can have multiple rates. Example King room, Rack rate plan, Monday through Thursday, weekday amount, Friday and Saturday, weekend amount.</xs:documentation>
					<xs:documentation xml:lang="en">The combination of a given Rate Plan and Room Type. This allows for support for systems where Rate Plans are child of Room Type as well as systems which Room Types are child of Rate Plans.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="RoomRate" maxOccurs="unbounded">
							<xs:complexType>
								<xs:complexContent>
									<xs:extension base="RoomRateType">
										<xs:sequence>
											<xs:element name="AdvanceBookingRestriction" minOccurs="0">
												<xs:annotation>
													<xs:documentation xml:lang="en">The period during which the booking must be made.</xs:documentation>
												</xs:annotation>
												<xs:complexType>
													<xs:attributeGroup ref="DateTimeSpanGroup">
														<xs:annotation>
															<xs:documentation xml:lang="en">The period of time during which the booking must be made.</xs:documentation>
														</xs:annotation>
													</xs:attributeGroup>
													<xs:attribute name="MinAdvanceBookingOffset" type="xs:duration" use="optional">
														<xs:annotation>
															<xs:documentation xml:lang="en">The minimum lead time required for the booking.</xs:documentation>
														</xs:annotation>
													</xs:attribute>
													<xs:attribute name="MaxAdvanceBookingOffset" type="xs:duration" use="optional">
														<xs:annotation>
															<xs:documentation xml:lang="en">The maximum lead time required for the booking.</xs:documentation>
														</xs:annotation>
													</xs:attribute>
													<xs:attributeGroup ref="DOW_PatternGroup">
														<xs:annotation>
															<xs:documentation xml:lang="en">The day(s) of the week either on which the booking may be made or the day(s) of arrival for the booking.</xs:documentation>
														</xs:annotation>
													</xs:attributeGroup>
												</xs:complexType>
											</xs:element>
											<xs:element name="Restrictions" minOccurs="0">
												<xs:annotation>
													<xs:documentation xml:lang="en">A collections of restrictions.</xs:documentation>
												</xs:annotation>
												<xs:complexType>
													<xs:sequence>
														<xs:element name="Restriction" maxOccurs="unbounded">
															<xs:annotation>
																<xs:documentation xml:lang="en">Information on the restriction associated with the RoomRate element.</xs:documentation>
															</xs:annotation>
															<xs:complexType>
																<xs:sequence>
																	<xs:element name="DOW_Restrictions" type="DOW_RestrictionsType">
																		<xs:annotation>
																			<xs:documentation xml:lang="en">A collection of day of week restrictions.</xs:documentation>
																		</xs:annotation>
																	</xs:element>
																</xs:sequence>
																<xs:attributeGroup ref="EffectiveExpireOptionalDateGroup">
																	<xs:annotation>
																		<xs:documentation xml:lang="en">The effective date range for which the restriction applies.</xs:documentation>
																	</xs:annotation>
																</xs:attributeGroup>
															</xs:complexType>
														</xs:element>
													</xs:sequence>
												</xs:complexType>
											</xs:element>
											<xs:element name="ServiceRPHs" type="ServiceRPHsType" minOccurs="0">
												<xs:annotation>
													<xs:documentation xml:lang="en">A container for the unique references to the services for the room stay.</xs:documentation>
												</xs:annotation>
											</xs:element>
											<xs:element name="GuestCounts" minOccurs="0">
												<xs:annotation>
													<xs:documentation xml:lang="en">A collection of Guest Counts associated with the room rate.</xs:documentation>
												</xs:annotation>
												<xs:complexType>
													<xs:sequence>
														<xs:element name="GuestCount" maxOccurs="99">
															<xs:annotation>
																<xs:documentation xml:lang="en">A recurring element that identifies the number of guests and ages of the guests.</xs:documentation>
															</xs:annotation>
															<xs:complexType>
																<xs:attributeGroup ref="GuestCountGroup"/>
															</xs:complexType>
														</xs:element>
													</xs:sequence>
												</xs:complexType>
											</xs:element>
										</xs:sequence>
									</xs:extension>
								</xs:complexContent>
							</xs:complexType>
						</xs:element>
						<xs:element ref="TPA_Extensions" minOccurs="0"/>
					</xs:sequence>
					<xs:attribute name="MoreRatesExistInd" type="xs:boolean" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">When true, indicates more rates exist. When false, all rate information is provided.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
				</xs:complexType>
			</xs:element>
			<xs:element name="GuestCounts" type="GuestCountType" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">A collection of Guest Counts associated with Room Stay. A child Guest Count element is required for each distinct age group.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="TimeSpan" type="DateTimeSpanType" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The Time Span which covers the Room Stay.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Guarantee" type="GuaranteeType" minOccurs="0" maxOccurs="5">
				<xs:annotation>
					<xs:documentation xml:lang="en">The guarantee information associated to the Room Stay. A maximum of 5 occurances are available for use depending on the context.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="DepositPayments" type="RequiredPaymentsType" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">A collection of deposit and/or payments for the Room Stay.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="CancelPenalties" type="CancelPenaltiesType" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">A collection of Cancellation Penalties objects for the Room Stay.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Discount" type="DiscountType" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Discount percentage and/or Amount, code and textual reason for discount.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Total" type="TotalType" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The total amount charged for the Room Stay including additional occupant amounts and fees. If TaxInclusive is set to True, then taxes are included in the total amount.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="BasicPropertyInfo" type="BasicPropertyInfoType" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Property Information for the Room Stay.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="MapURL" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">When requested, a link to a map is returned in the response which indicates the position of the matching hotel(s).</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:simpleContent>
						<xs:extension base="xs:anyURI">
							<xs:attribute name="BottomRightLatitude" type="xs:decimal">
								<xs:annotation>
									<xs:documentation xml:lang="en">The bottom right latitude coordinate.</xs:documentation>
								</xs:annotation>
							</xs:attribute>
							<xs:attribute name="BottomRightLongitude" type="xs:decimal">
								<xs:annotation>
									<xs:documentation xml:lang="en">The bottom right longitude coordinate.</xs:documentation>
								</xs:annotation>
							</xs:attribute>
							<xs:attribute name="TopLeftLatitude" type="xs:decimal">
								<xs:annotation>
									<xs:documentation xml:lang="en">The top left latitude coordinate.</xs:documentation>
								</xs:annotation>
							</xs:attribute>
							<xs:attribute name="TopLeftLongitude" type="xs:decimal">
								<xs:annotation>
									<xs:documentation xml:lang="en">The top left longitude coordinate.</xs:documentation>
								</xs:annotation>
							</xs:attribute>
							<xs:attribute name="Height" use="optional">
								<xs:annotation>
									<xs:documentation xml:lang="en">The height of the map in pixels (maximum 480).</xs:documentation>
								</xs:annotation>
								<xs:simpleType>
									<xs:restriction base="Numeric1to999">
										<xs:maxInclusive value="480"/>
									</xs:restriction>
								</xs:simpleType>
							</xs:attribute>
							<xs:attribute name="Width">
								<xs:annotation>
									<xs:documentation xml:lang="en">The width of the map in pixels (maximum 640).</xs:documentation>
								</xs:annotation>
								<xs:simpleType>
									<xs:restriction base="Numeric1to999">
										<xs:maxInclusive value="640"/>
									</xs:restriction>
								</xs:simpleType>
							</xs:attribute>
							<xs:attribute name="ZoomFactor">
								<xs:annotation>
									<xs:documentation xml:lang="en">The zoom factor of the map (from 0 to 12.)</xs:documentation>
								</xs:annotation>
								<xs:simpleType>
									<xs:restriction base="Numeric0to99">
										<xs:maxInclusive value="12"/>
									</xs:restriction>
								</xs:simpleType>
							</xs:attribute>
						</xs:extension>
					</xs:simpleContent>
				</xs:complexType>
			</xs:element>
			<xs:element ref="TPA_Extensions" minOccurs="0"/>
		</xs:sequence>
		<xs:attribute name="MarketCode" type="StringLength1to64" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The code that relates to the market being sold to (e.g., the corporate market, packages).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="SourceOfBusiness" type="StringLength1to64" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">To specify where the business came from e.g. radio, newspaper ad, etc.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="DiscountCode" type="StringLength1to64" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">A discount code known to the hotel.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attributeGroup ref="PromotionCodeGroup"/>
		<xs:attribute name="RoomStayStatus" type="HotelResStatusType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Identifies the status of the room stay.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="WarningRPH" type="ListOfRPH" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">This references the RPH found in the Warning element. Used to identify the warnings associated with this RoomStay.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="RoomStayLanguage" type="xs:language" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Language of the response for the RoomStay (property). Note that the requested language may not be available for all properties and so the language of the response for each property may vary.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:complexType>
	<xs:complexType name="RoomTypeLiteType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Summary version of the RoomTypeType, initially created for the Travel Itinerary Message set.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="RoomDescription" type="ParagraphType" minOccurs="0"/>
			<xs:element name="Amenity" type="RoomAmenityPrefType" minOccurs="0" maxOccurs="5"/>
		</xs:sequence>
		<xs:attribute name="RoomTypeCode" type="StringLength1to16" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Specfic system room type code, ex: A1K, A1Q etc.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="NumberOfUnits" type="xs:integer" use="optional"/>
	</xs:complexType>
	<xs:complexType name="RoomTypeType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Provides details regarding rooms, usually guest rooms.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="RoomDescription" type="ParagraphType" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Textual information regarding the room.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="AdditionalDetails" type="AdditionalDetailsType" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">A collection of additional details.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Amenities" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">A collection of amenity information.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="Amenity" type="RoomAmenityPrefType" minOccurs="0" maxOccurs="unbounded">
							<xs:annotation>
								<xs:documentation xml:lang="en">Used to provide room amenity information.</xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="Occupancy" minOccurs="0" maxOccurs="5">
				<xs:annotation>
					<xs:documentation xml:lang="en">Provides parameters of occupancy limits.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:attributeGroup ref="OccupancyGroup">
						<xs:annotation>
							<xs:documentation xml:lang="en">Minimum or maximum number of people allowed in a room type as defined by age.</xs:documentation>
						</xs:annotation>
					</xs:attributeGroup>
					<xs:attributeGroup ref="AgeQualifyingGroup">
						<xs:annotation>
							<xs:documentation xml:lang="en">Specifies the age parameters for the occupancy of this guest room type.</xs:documentation>
						</xs:annotation>
					</xs:attributeGroup>
				</xs:complexType>
			</xs:element>
			<xs:element ref="TPA_Extensions" minOccurs="0"/>
		</xs:sequence>
		<xs:attribute name="NumberOfUnits" type="xs:integer" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The number of rooms that have been combined to create this room type.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="IsRoom" type="xs:boolean" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Indicates the room is a sleeping room when true.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="IsConverted" type="xs:boolean" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Indicates the room is converted when true.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="IsAlternate" type="xs:boolean" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Indicates the room is an alternate room type to the requested room type when true.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="ReqdGuaranteeType" type="xs:string" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Denotes the form of guarantee for this room.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attributeGroup ref="RoomGroup">
			<xs:annotation>
				<xs:documentation xml:lang="en">Provides details of the room type.</xs:documentation>
			</xs:annotation>
		</xs:attributeGroup>
	</xs:complexType>
	<xs:complexType name="RoutingHopType">
		<xs:annotation>
			<xs:documentation xml:lang="en">A collection of RoutingHop objects. his details the path that the original request or notification traveled.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="RoutingHop" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation xml:lang="en">A RoutingHop object conveys information about the path that this notification update took (e.g. how many and which systems it passed through).</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:attribute name="SystemCode" type="xs:string" use="optional"/>
					<xs:attribute name="LocalRefID" type="xs:string" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">Identifier within the System that refers to this specific document (reservation).</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="TimeStamp" type="xs:dateTime" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">TimeStamp: TimeInstant - The date and time that the reservation passed through a routing hop.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="Comment" type="xs:string" use="optional"/>
					<xs:attribute name="SequenceNmbr" type="xs:integer" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">Sequential number assigned to hops, Base 0.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="Data" type="xs:string" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">Data: String - This attribute is provided so that each system can put in whatever data it would like (e.g., auditing information).</xs:documentation>
						</xs:annotation>
					</xs:attribute>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="SellableProductsType">
		<xs:annotation>
			<xs:documentation xml:lang="en">The SellableProduct class defines the inventoried item for this rate plan.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="SellableProduct" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation xml:lang="en">The individual sellable product. A sellable product may be a guest room, a meeting room or an inventory block. Attributes of SellableProduct are the inventory codes, information on the use, application and sequencing of the inventory information.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="DestinationSystemCodes" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">A collection of destination system codes. These are systems for which this Sellable product is targeted.</xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:sequence>
									<xs:element name="DestinationSystemCode" maxOccurs="unbounded">
										<xs:annotation>
											<xs:documentation xml:lang="en">The destination system code defines a system to which information is to be provided.</xs:documentation>
										</xs:annotation>
										<xs:complexType>
											<xs:simpleContent>
												<xs:extension base="StringLength1to32">
													<xs:attribute name="ChainRateLevelCrossRef" type="StringLength1to64" use="optional">
														<xs:annotation>
															<xs:documentation xml:lang="en">A text field used to define a specific destination system code for a corresponding RateLevelCode.</xs:documentation>
														</xs:annotation>
													</xs:attribute>
													<xs:attribute name="ChainRateCodeCrossRef" type="StringLength1to64" use="optional">
														<xs:annotation>
															<xs:documentation xml:lang="en">A text field used to define a specific destination system code for a corresponding ChainRateCode.</xs:documentation>
														</xs:annotation>
													</xs:attribute>
												</xs:extension>
											</xs:simpleContent>
										</xs:complexType>
									</xs:element>
								</xs:sequence>
							</xs:complexType>
						</xs:element>
						<xs:choice>
							<xs:element name="GuestRoom" type="GuestRoomType">
								<xs:annotation>
									<xs:documentation xml:lang="en">Guest room information if this sellable product is a guest room.</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="MeetingRooms" type="MeetingRoomsType">
								<xs:annotation>
									<xs:documentation xml:lang="en">Meeting room information if this sellable product is a meeting room.</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="InventoryBlock">
								<xs:annotation>
									<xs:documentation xml:lang="en">Inventory block information if this sellable product is an inventory block.</xs:documentation>
								</xs:annotation>
								<xs:complexType>
									<xs:attributeGroup ref="InvBlockCodeGroup"/>
								</xs:complexType>
							</xs:element>
						</xs:choice>
						<xs:element name="Description" type="ParagraphType" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">Description of the sellable product.</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="UniqueID" type="UniqueID_Type" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">The SellableProduct class defines the inventoriable item for this rate plan.</xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
					<xs:attribute name="RPH" type="RPH_Type" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">This is a reference placeholder, used as an index for this sellable product.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attributeGroup ref="InventoryGroup"/>
					<xs:attribute name="InvNotifType" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">An enumerated type that indicates whether this is a new inventory item or an update of an existing inventory item. Values: New, Delta modification, Full overlay modification.</xs:documentation>
						</xs:annotation>
						<xs:simpleType>
							<xs:restriction base="xs:string">
								<xs:enumeration value="New"/>
								<xs:enumeration value="Delta"/>
								<xs:enumeration value="Overlay"/>
							</xs:restriction>
						</xs:simpleType>
					</xs:attribute>
					<xs:attribute name="InvStatusType" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">An enumerated type that indicates the status of the inventory item, ie: whether this notification is an initial announcement of a new inventory item, an update of an active (bookable) inventory item, or a notification of an inventory item that is no longer in effect. Values: Initial, Active, Deactivated.</xs:documentation>
						</xs:annotation>
						<xs:simpleType>
							<xs:restriction base="xs:string">
								<xs:enumeration value="Initial"/>
								<xs:enumeration value="Active"/>
								<xs:enumeration value="Deactivated"/>
							</xs:restriction>
						</xs:simpleType>
					</xs:attribute>
					<xs:attribute name="InvGroupingCode" type="xs:string" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">The identification of the inventory grouping. Whether to use the sending/querying or the receiving/responding system's identification depends on which system is doing the translating.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="OrderSequence" type="Numeric1to999" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">The order which the items should be acknowledged by the receiving entity.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attributeGroup ref="DateTimeSpanGroup">
						<xs:annotation>
							<xs:documentation xml:lang="en">Defines the sellable date range for this product.</xs:documentation>
						</xs:annotation>
					</xs:attributeGroup>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="StatisticApplicationSetType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Includes the statistic data reported and the codes for which it has been aggregated, if applicable. The applicable date range for the data is defined in its attributes.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="StatisticCodes" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Collection of StatisticCode elements. Used if data is partitioned/aggregated.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="StatisticCode" maxOccurs="5">
							<xs:annotation>
								<xs:documentation xml:lang="en">Defines the codes and corresponding categories for which the data in the other elements has been gathered.</xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:attributeGroup ref="StatisticCodeGroup"/>
							</xs:complexType>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="RevenueCategorySummaries" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Collection of RevenueCategorySummary elements. Used if revenue values reported.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="RevenueCategorySummary" maxOccurs="unbounded">
							<xs:annotation>
								<xs:documentation xml:lang="en">This element has revenue amount data for its revenue category, identified using OpenTravel Code List RCC, such as Room Revenue, Food and Beverage Revenue.</xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:attributeGroup ref="RevenueCategorySummaryGroup"/>
							</xs:complexType>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="CountCategorySummaries" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Collection of CountCategorySummary elements. Used if count values reported.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:annotation>
						<xs:documentation xml:lang="en">A collection of CountCategorySummaryType elements.</xs:documentation>
					</xs:annotation>
					<xs:sequence>
						<xs:element name="CountCategorySummary" maxOccurs="unbounded">
							<xs:annotation>
								<xs:documentation xml:lang="en">This element has count data for each count category, identified using OpenTravel Code List CNT, such as number of guests, rooms occupied, etc.</xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:attributeGroup ref="CountCategorySummaryGroup"/>
							</xs:complexType>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="ReportSummaries" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Collection of ReportSummary elements.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:annotation>
						<xs:documentation xml:lang="en">Container for ReportSummary elements of type ParagraphType.</xs:documentation>
					</xs:annotation>
					<xs:sequence>
						<xs:element name="ReportSummary" type="ParagraphType" maxOccurs="unbounded">
							<xs:annotation>
								<xs:documentation xml:lang="en">This element has text information, included with the data in the report as needed.</xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
		<xs:attributeGroup ref="DateTimeSpanGroup"/>
	</xs:complexType>
	<xs:complexType name="StatisticType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Defines all details needed to create a statistical report.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="StatisticApplicationSets">
				<xs:annotation>
					<xs:documentation xml:lang="en">Collection of StatisticApplicationSet Elements.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="StatisticApplicationSet" type="StatisticApplicationSetType" maxOccurs="unbounded">
							<xs:annotation>
								<xs:documentation xml:lang="en">Repeated for each date or set of statistic codes.</xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
		<xs:attributeGroup ref="HotelReferenceGroup"/>
		<xs:attributeGroup ref="StatisticReportGroup"/>
	</xs:complexType>
	<xs:complexType name="StatisticsType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Collection of Statistic Elements.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="Statistic" type="StatisticType" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation xml:lang="en">Used to define the report fiscal date, report code, and the hotel(s) for which data is being reported.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="StatusApplicationControlType">
		<xs:annotation>
			<xs:documentation xml:lang="en">The StatusApplicationControl class is used to indicate to which block codes/rate plans/inventory codes a status should be applied.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="DestinationSystemCodes" type="DestinationSystemCodesType" minOccurs="0"/>
		</xs:sequence>
		<xs:attributeGroup ref="StatusApplicationGroup"/>
		<xs:attributeGroup ref="DOW_PatternGroup"/>
		<xs:attributeGroup ref="RatePlanCodeTypeGroup"/>
		<xs:attribute name="RateTier" type="StringLength1to64" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Hotel systems often have different tiers for a given rate plan; this attribute is used to designate a specific tier within the rate plan (e.g. high, medium, low).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="AllRateCode" type="xs:boolean" use="optional"/>
		<xs:attribute name="AllInvCode" type="xs:boolean" use="optional"/>
		<xs:attributeGroup ref="InvBlockCodeApplyGroup"/>
		<xs:attribute name="InvBlockCode" type="StringLength1to32" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Code that identifies an inventory block.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="Override" type="xs:boolean" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Mechanism to allow the user to override settings at the reservation system and to allow the RMS to replace this overridden values: If value = 0 (No), the reservations system may ignore the settings passed and keep values overridden by the user. If value = 1 (Yes), the reservations system must replace values overridden by the user.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="QuoteID" type="StringLength1to32" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">This identifies a specific quote.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="SubBlockCode" type="StringLength1to32" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">This is the code defining a subset of the inventory block.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="WingIdentifier" type="StringLength1to32" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Used to identify the wing of a hotel.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:complexType>
	<xs:complexType name="ViewershipsType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Viewerships determine  the distribution channel(s) for a rate plan. As an optional element, the absence of Viewerships implies a generally available rate, while the presence of a Viewerships collection implies qualifications on the rate. This may also be used to define a system that is able to view the hotel content.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="Viewership" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation xml:lang="en">Viewership defines a system that is allowed to view the rateplan identified in a Hotel Rate Plan Notification message. This may also be used to define a system that is able to view the hotel content.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="ViewershipCodes" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">A collection of Viewship codes</xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:sequence>
									<xs:element name="ViewershipCode" type="xs:string">
										<xs:annotation>
											<xs:documentation xml:lang="en">The viewership code (e.g. pseudo-city code).</xs:documentation>
										</xs:annotation>
									</xs:element>
								</xs:sequence>
							</xs:complexType>
						</xs:element>
						<xs:element name="SystemCodes" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">A collection of system codes. </xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:sequence>
									<xs:element name="SystemCode" maxOccurs="unbounded">
										<xs:annotation>
											<xs:documentation xml:lang="en">The system code (e.g., AA, 1P, 1G, 1A) associated with this viewership record.</xs:documentation>
										</xs:annotation>
										<xs:complexType>
											<xs:simpleContent>
												<xs:extension base="xs:string">
													<xs:attributeGroup ref="DisplayGroup">
														<xs:annotation>
															<xs:documentation xml:lang="en"/>
														</xs:annotation>
													</xs:attributeGroup>
												</xs:extension>
											</xs:simpleContent>
										</xs:complexType>
									</xs:element>
								</xs:sequence>
								<xs:attribute name="SystemCodesInclusive" type="xs:boolean" use="optional">
									<xs:annotation>
										<xs:documentation xml:lang="en">Indicates whether the collection of System Codes is inclusive or exclusive. Values: false=Exclusive, true=Inclusive.</xs:documentation>
									</xs:annotation>
								</xs:attribute>
							</xs:complexType>
						</xs:element>
						<xs:element name="ProfileTypes" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">A collection of profile types.</xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:sequence>
									<xs:element name="ProfileType" maxOccurs="unbounded">
										<xs:annotation>
											<xs:documentation xml:lang="en">Profile types associated with the viewership, i.e, the types of profiles allowed to view this information. Please refer the OpenTravel Code List PRT.</xs:documentation>
										</xs:annotation>
										<xs:complexType>
											<xs:attributeGroup ref="ProfileTypeGroup"/>
										</xs:complexType>
									</xs:element>
								</xs:sequence>
							</xs:complexType>
						</xs:element>
						<xs:element name="ProfileRefs" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">A collection of profile references.</xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:sequence>
									<xs:element name="ProfileRef" type="UniqueID_Type" maxOccurs="unbounded">
										<xs:annotation>
											<xs:documentation xml:lang="en">The actual Unique ID of the  profile associated with the viewship, i.e. this is the pointer to the profile on the trading partners system for the company, agency, etc that is allowed to view this information.</xs:documentation>
										</xs:annotation>
									</xs:element>
								</xs:sequence>
							</xs:complexType>
						</xs:element>
						<xs:element name="Profiles" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">A collection of profiles.</xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:sequence>
									<xs:element name="Profile" type="ProfileType" maxOccurs="unbounded">
										<xs:annotation>
											<xs:documentation xml:lang="en">Profile associated with the Viewership, i.e. the information required to identify the company, agency, etc that is allowed to view the information.</xs:documentation>
										</xs:annotation>
									</xs:element>
								</xs:sequence>
							</xs:complexType>
						</xs:element>
						<xs:element name="LocationCodes" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">A collections of strings that identify the locations, such as regions or countries, etc., associated with this viewership record.</xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:sequence>
									<xs:element name="LocationCode" maxOccurs="unbounded">
										<xs:complexType>
											<xs:attribute name="CityCode" type="StringLength1to8" use="optional"/>
											<xs:attribute name="StateProvinceCode" type="StringLength1to8" use="optional"/>
											<xs:attribute name="CountryCode" type="ISO3166" use="optional"/>
										</xs:complexType>
									</xs:element>
								</xs:sequence>
								<xs:attribute name="LocationCodesInclusive" type="xs:boolean" use="optional">
									<xs:annotation>
										<xs:documentation xml:lang="en">Indicates whether the collection of Location Codes is inclusive or exclusive.	</xs:documentation>
										<xs:documentation xml:lang="en">Values: false=Exclusive, true=Inclusive.</xs:documentation>
									</xs:annotation>
								</xs:attribute>
							</xs:complexType>
						</xs:element>
						<xs:element name="BookingChannelCodes" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">A collections of strings that identify booking channels, such as GDSs or Internet sites, etc., associated with this viewership record.</xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:sequence>
									<xs:element name="BookingChannelCode" maxOccurs="unbounded">
										<xs:annotation>
											<xs:documentation xml:lang="en">Specific code in relation to POS booking source (e.g. Expedia, hotels.com, etc.).</xs:documentation>
										</xs:annotation>
										<xs:complexType>
											<xs:simpleContent>
												<xs:extension base="xs:string">
													<xs:attributeGroup ref="DisplayGroup">
														<xs:annotation>
															<xs:documentation xml:lang="en">Provides information on whether an item may be displayed and in what order.</xs:documentation>
														</xs:annotation>
													</xs:attributeGroup>
												</xs:extension>
											</xs:simpleContent>
										</xs:complexType>
									</xs:element>
								</xs:sequence>
								<xs:attribute name="ChannelCodesInclusive" type="xs:boolean" use="optional">
									<xs:annotation>
										<xs:documentation xml:lang="en">Indicates whether the collection of Channel Codes for booking channels is inclusive or exclusive. Values: false=Exclusive, true=Inclusive.</xs:documentation>
									</xs:annotation>
								</xs:attribute>
							</xs:complexType>
						</xs:element>
						<xs:element name="DistributorTypes" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">A Collection of strings that identify the distibutors (ex Tour Operators, Agency Chains, etc) which are allowed  viewership.</xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:sequence>
									<xs:element name="DistributorType" maxOccurs="unbounded">
										<xs:complexType>
											<xs:simpleContent>
												<xs:extension base="xs:string">
													<xs:attribute name="DistributorCode" type="StringLength1to16">
														<xs:annotation>
															<xs:documentation xml:lang="en">Code used to uniquely identify the distributor.</xs:documentation>
														</xs:annotation>
													</xs:attribute>
													<xs:attribute name="DistributorTypeCode" type="StringLength1to16">
														<xs:annotation>
															<xs:documentation xml:lang="en">Type code is used to qualify the Distibutor into general classes of distributors.</xs:documentation>
														</xs:annotation>
													</xs:attribute>
												</xs:extension>
											</xs:simpleContent>
										</xs:complexType>
									</xs:element>
								</xs:sequence>
							</xs:complexType>
						</xs:element>
					</xs:sequence>
					<xs:attribute name="ViewershipRPH" type="RPH_Type" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">This is a reference placeholder, used as an index for multiple Viewership classes.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="ViewOnly" type="xs:boolean" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">Indicates whether this rate plan is bookable by the entity having viewership. Values: false= Bookable, true= View Only (not bookable).</xs:documentation>
						</xs:annotation>
					</xs:attribute>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="WeatherInfoType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Provides detailed information regarding weather.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="Precipitation" minOccurs="0" maxOccurs="2">
				<xs:annotation>
					<xs:documentation xml:lang="en">Provides average precipitation and the unit in which it is measured for a Period.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:attribute name="AveragePrecipitation" type="xs:decimal" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">Defines the average precipitation for the time as designated in Period and is qualified by the UnitOfMeasure.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="UnitOfMeasure" type="StringLength1to16" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">This is the unit of measure as it applies to AveragePercipitation (e.g.inches or centimeters).</xs:documentation>
						</xs:annotation>
					</xs:attribute>
				</xs:complexType>
			</xs:element>
			<xs:element name="Temperature" minOccurs="0" maxOccurs="2">
				<xs:annotation>
					<xs:documentation xml:lang="en">Provides average temperatures and the unit in which they are measured for a Period.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:attribute name="AverageHighTemp" type="xs:integer" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">Defines the average high tempature for the time as designated in Period and is qualified by the TempUnit.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="AverageLowTemp" type="xs:integer" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">Defines the average low tempature for the time as designated in Period and is qualified by the TempUnit.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="TempUnit" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">Provides the units in which the AverageHighTemp and AverageLowTemp are defined (i.e. Celsius or Fahrenheit).</xs:documentation>
						</xs:annotation>
						<xs:simpleType>
							<xs:restriction base="xs:NMTOKEN">
								<xs:enumeration value="Celsius"/>
								<xs:enumeration value="Fahrenheit"/>
							</xs:restriction>
						</xs:simpleType>
					</xs:attribute>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="Period" type="xs:string" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">This may be used to define a time period for which a certain type of weather occurs (e.g. January or Spring).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:complexType>
</xs:schema>
