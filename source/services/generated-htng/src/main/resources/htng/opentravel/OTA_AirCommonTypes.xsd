<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns="http://www.opentravel.org/OTA/2003/05" targetNamespace="http://www.opentravel.org/OTA/2003/05" elementFormDefault="qualified" version="10.000" id="OTA2003A2011A">
	<xs:include schemaLocation="OTA_CommonTypes.xsd"/>
	<xs:annotation>
		<xs:documentation xml:lang="en">All Schema files in the OpenTravel Alliance specification are made available according to the terms defined by the OpenTravel License Agreement at http://www.opentravel.org/Specifications/Default.aspx.</xs:documentation>
	</xs:annotation>
	<xs:simpleType name="ActionCodeType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Identifies the action code for a booking - OK, Waitlist etc.</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:enumeration value="OK">
				<xs:annotation>
					<xs:documentation xml:lang="en">Status is confirmed.</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="Waitlist">
				<xs:annotation>
					<xs:documentation xml:lang="en">Status is waitlisted.</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="Other">
				<xs:annotation>
					<xs:documentation xml:lang="en">Status is other.</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="Cancel">
				<xs:annotation>
					<xs:documentation xml:lang="en">Status is cancel.</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="Need">
				<xs:annotation>
					<xs:documentation xml:lang="en"> Status is need.</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="AirTripDirectionType">
		<xs:restriction base="xs:NMTOKEN">
			<xs:enumeration value="Outbound"/>
			<xs:enumeration value="Return"/>
			<xs:enumeration value="All"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="AirTripType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Identifies the trip type - one way, return, circle trip, open jaw.</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:enumeration value="OneWay">
				<xs:annotation>
					<xs:documentation xml:lang="en">Identifies a one way trip type.</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="OneWayOnly">
				<xs:annotation>
					<xs:documentation xml:lang="en">Cannot be doubled to create a roundtrip. </xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="Return">
				<xs:annotation>
					<xs:documentation xml:lang="en">Identifies a return trip type.</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="Circle">
				<xs:annotation>
					<xs:documentation xml:lang="en">Identifies a circle trip type.</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="OpenJaw">
				<xs:annotation>
					<xs:documentation xml:lang="en">Identifies an open jaw trip type.</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="Other">
				<xs:annotation>
					<xs:documentation xml:lang="en">Identifies an other trip type.</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="Outbound">
				<xs:annotation>
					<xs:documentation xml:lang="en">The direction for the fare is outbound.</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="OutboundSeasonRoundtrip">
				<xs:annotation>
					<xs:documentation xml:lang="en">The direction for the fare is outbound seasonal roundtrip.</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="Non-directional">
				<xs:annotation>
					<xs:documentation xml:lang="en">There is no direction specified for the fare.</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="Inbound">
				<xs:annotation>
					<xs:documentation xml:lang="en">The direction for the fare is inbound.</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="Roundtrip">
				<xs:annotation>
					<xs:documentation xml:lang="en">Identifies travel from one point to another point and return to the original point.  (The outbound fare shall be used also for the inbound fare component for the purpose of determing if the pricing unit is a round trip).</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="DisplayOrderType">
		<xs:restriction base="xs:NMTOKEN">
			<xs:enumeration value="ByDepartureTime">
				<xs:annotation>
					<xs:documentation xml:lang="en">Display products by departure time</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="ByArrivalTime">
				<xs:annotation>
					<xs:documentation xml:lang="en">Display products by arrival time</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="ByJourneyTime">
				<xs:annotation>
					<xs:documentation xml:lang="en">Display products by journey time</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="ByPriceHighToLow">
				<xs:annotation>
					<xs:documentation xml:lang="en"/>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="ByPriceLowToHigh">
				<xs:annotation>
					<xs:documentation xml:lang="en"/>
				</xs:annotation>
			</xs:enumeration>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="FareAmountType">
		<xs:annotation>
			<xs:documentation>Used to specify if ticket amount is bulk, IT, or NOADC</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:NMTOKEN">
			<xs:enumeration value="NOADC"/>
			<xs:enumeration value="Bulk"/>
			<xs:enumeration value="IT"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="FareApplicationType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Indicates how the fare may be applied, such as one way or roundtrip.</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:enumeration value="OneWay"/>
			<xs:enumeration value="Return"/>
			<xs:enumeration value="HalfReturn"/>
			<xs:enumeration value="Roundtrip">
				<xs:annotation>
					<xs:documentation xml:lang="en">Specifies that the fare is for a roundtrip.</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="OneWayOnly">
				<xs:annotation>
					<xs:documentation xml:lang="en">The fare can only be treated as one way - can not be doubled to create a roundtrip fare.</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="FareStatusType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Identifies whether the fare was constructed, published, created, etc.</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:NMTOKEN">
			<xs:enumeration value="constructed"/>
			<xs:enumeration value="published"/>
			<xs:enumeration value="created"/>
			<xs:enumeration value="fareByRule">
				<xs:annotation>
					<xs:documentation xml:lang="en">Specifies that the fare was built based on rules.
</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="fareByRulePrivate">
				<xs:annotation>
					<xs:documentation xml:lang="en">The private fare was built by rules.</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="GlobalIndicatorType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Specifies the global travel area.</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:NMTOKEN">
			<xs:enumeration value="AP">
				<xs:annotation>
					<xs:documentation xml:lang="en">Atlantic/Pacific Round-the-World</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="AT">
				<xs:annotation>
					<xs:documentation xml:lang="en">Atlantic Ocean</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="CT">
				<xs:annotation>
					<xs:documentation xml:lang="en">Circle trip</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="DO">
				<xs:annotation>
					<xs:documentation xml:lang="en">Domestic</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="EH">
				<xs:annotation>
					<xs:documentation xml:lang="en">Eastern Hemisphere</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="FE">
				<xs:annotation>
					<xs:documentation xml:lang="en">Within the Far East</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="PA">
				<xs:annotation>
					<xs:documentation xml:lang="en">Pacific Ocean</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="PN">
				<xs:annotation>
					<xs:documentation xml:lang="en">TC1-TC3 via Pacific/N. America</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="PO">
				<xs:annotation>
					<xs:documentation xml:lang="en">Polar Route</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="RU">
				<xs:annotation>
					<xs:documentation xml:lang="en">Russia Area 3</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="RW">
				<xs:annotation>
					<xs:documentation xml:lang="en">Round the world</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="SA">
				<xs:annotation>
					<xs:documentation xml:lang="en">South Atlantic only</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="TS">
				<xs:annotation>
					<xs:documentation xml:lang="en">Trans Siberia Route</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="WH">
				<xs:annotation>
					<xs:documentation xml:lang="en">Western Hemisphere</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="MealServiceType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Enumerated List (Meal Code in brackets): Breakfast (B); Snack (S); Dinner (D); Hot Meal (H); Lunch (L); Refreshments (R); Complimentary Liquor (C); Meal (M); Liquor for Purchase (P); Food for Purchase (F); Cold Meal (O); No Meal Service (-). Alternately, a String of Length 32 can be used if the above list does not suffice.</xs:documentation>
		</xs:annotation>
		<xs:union>
			<xs:simpleType>
				<xs:restriction base="xs:string">
					<xs:enumeration value="Breakfast">
						<xs:annotation>
							<xs:documentation xml:lang="en">Identifies a breakfast meal service.</xs:documentation>
						</xs:annotation>
					</xs:enumeration>
					<xs:enumeration value="Snack">
						<xs:annotation>
							<xs:documentation xml:lang="en">Identifies a snack meal service.</xs:documentation>
						</xs:annotation>
					</xs:enumeration>
					<xs:enumeration value="Dinner">
						<xs:annotation>
							<xs:documentation xml:lang="en">Identifies a dinner meal service.</xs:documentation>
						</xs:annotation>
					</xs:enumeration>
					<xs:enumeration value="Hot Meal">
						<xs:annotation>
							<xs:documentation xml:lang="en">Identifies a hot meal service.</xs:documentation>
						</xs:annotation>
					</xs:enumeration>
					<xs:enumeration value="Lunch">
						<xs:annotation>
							<xs:documentation xml:lang="en">Identifies a lunch meal service.</xs:documentation>
						</xs:annotation>
					</xs:enumeration>
					<xs:enumeration value="Refreshments">
						<xs:annotation>
							<xs:documentation xml:lang="en">Identifies a refreshments meal service.</xs:documentation>
						</xs:annotation>
					</xs:enumeration>
					<xs:enumeration value="Complimentary Liquor">
						<xs:annotation>
							<xs:documentation xml:lang="en">Identifies a complimentary liquor meal service.</xs:documentation>
						</xs:annotation>
					</xs:enumeration>
					<xs:enumeration value="Meal">
						<xs:annotation>
							<xs:documentation xml:lang="en">Identifies a meal service exists.</xs:documentation>
						</xs:annotation>
					</xs:enumeration>
					<xs:enumeration value="Liquor for Purchase">
						<xs:annotation>
							<xs:documentation xml:lang="en">Identifies that liquor is available for purchase.</xs:documentation>
						</xs:annotation>
					</xs:enumeration>
					<xs:enumeration value="Food for Purchase">
						<xs:annotation>
							<xs:documentation xml:lang="en">Identifies that food is available for purchase.</xs:documentation>
						</xs:annotation>
					</xs:enumeration>
					<xs:enumeration value="Cold Meal">
						<xs:annotation>
							<xs:documentation xml:lang="en">Identifies a cold meal service is available.</xs:documentation>
						</xs:annotation>
					</xs:enumeration>
					<xs:enumeration value="No Meal Service">
						<xs:annotation>
							<xs:documentation xml:lang="en">Identifies that no meal service is available.</xs:documentation>
						</xs:annotation>
					</xs:enumeration>
				</xs:restriction>
			</xs:simpleType>
			<xs:simpleType>
				<xs:restriction base="StringLength1to32"/>
			</xs:simpleType>
		</xs:union>
	</xs:simpleType>
	<xs:simpleType name="PricingSourceType">
		<xs:annotation>
			<xs:documentation xml:lang="en">It can be used to indicate whether the fare is public or private.</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:enumeration value="Published">
				<xs:annotation>
					<xs:documentation xml:lang="en">Published fare.</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="Private">
				<xs:annotation>
					<xs:documentation xml:lang="en">Private fare.</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="Both">
				<xs:annotation>
					<xs:documentation xml:lang="en">Fare is both public and private.</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="PurposeType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Used to specify base, net, sell or refund amount.</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:NMTOKEN">
			<xs:enumeration value="Sell">
				<xs:annotation>
					<xs:documentation xml:lang="en"/>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="Net">
				<xs:annotation>
					<xs:documentation xml:lang="en"/>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="Base"/>
			<xs:enumeration value="Refund"/>
			<xs:enumeration value="Additional"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="SpecialRemarkOptionType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Identifies the type of special remark used.</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:enumeration value="Itinerary">
				<xs:annotation>
					<xs:documentation xml:lang="en">Remarks apply to the itinerary.</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="Invoice">
				<xs:annotation>
					<xs:documentation xml:lang="en">Remarks apply to the invoice.</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="Endorsement">
				<xs:annotation>
					<xs:documentation xml:lang="en">Remarks apply to the endorsement.</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="Save">
				<xs:annotation>
					<xs:documentation xml:lang="en">Remarks which can be deleted by the author only.</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="Confidential">
				<xs:annotation>
					<xs:documentation xml:lang="en">Confidential remarks which are visible only to the author and system providers.</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="Free">
				<xs:annotation>
					<xs:documentation xml:lang="en">Free text remarks which can be sent to specific airlines.</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="GRMS">
				<xs:annotation>
					<xs:documentation xml:lang="en">Remarks from or to a specific group revenue management system (GRMS).</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="Split">
				<xs:annotation>
					<xs:documentation xml:lang="en">Remarks containing information about split transaction (Split off PNR address, time, who, etc.).</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="StayUnitType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Defines the 'Units' that can be applied to Stay restrictions.</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:enumeration value="Minutes">
				<xs:annotation>
					<xs:documentation xml:lang="en">Stay restriction in minutes.</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="Hours">
				<xs:annotation>
					<xs:documentation xml:lang="en">Stay restriction in hours.</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="Days">
				<xs:annotation>
					<xs:documentation xml:lang="en">Stay restriction in days.</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="Months">
				<xs:annotation>
					<xs:documentation xml:lang="en">Stay restriction in months.</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="MON">
				<xs:annotation>
					<xs:documentation xml:lang="en">Monday</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="TUES">
				<xs:annotation>
					<xs:documentation xml:lang="en">Tuesday</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="WED">
				<xs:annotation>
					<xs:documentation xml:lang="en">Wednesday</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="THU">
				<xs:annotation>
					<xs:documentation xml:lang="en">Thursday</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="FRI">
				<xs:annotation>
					<xs:documentation xml:lang="en">Friday</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="SAT">
				<xs:annotation>
					<xs:documentation xml:lang="en">Saturday</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="SUN">
				<xs:annotation>
					<xs:documentation xml:lang="en">Sunday</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
		</xs:restriction>
	</xs:simpleType>
	<xs:attributeGroup name="AirRowCharacteristicsGroup">
		<xs:annotation>
			<xs:documentation xml:lang="en">OpenTravel Codes for AirRowType</xs:documentation>
		</xs:annotation>
		<xs:attribute name="AirRowType" type="OTA_CodeType" use="required">
			<xs:annotation>
				<xs:documentation xml:lang="en">Refer to OpenTravel Code List Air Row Type (ROW).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:attributeGroup>
	<xs:attributeGroup name="AirDetailsRSAttributes">
		<xs:annotation>
			<xs:documentation xml:lang="en">Provides information concerning flight  times and mileage.</xs:documentation>
		</xs:annotation>
		<xs:attribute name="TotalFlightTime" type="xs:duration" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The total duration of time a flight is airborne.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="TotalGroundTime" type="xs:duration" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The total duration of time a flight is on the ground.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="TotalTripTime" type="xs:duration" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The total duration of time required for a flight operation (ground and air).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="TotalMiles" type="xs:positiveInteger" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Total miles for a flight segment.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:attributeGroup>
	<xs:attributeGroup name="AirProcessingInfoGroup">
		<xs:annotation>
			<xs:documentation xml:lang="en">A collection of information that specifies how the message processing should occur or how the data should be returned.</xs:documentation>
		</xs:annotation>
		<xs:attribute name="TargetSource" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Specifies to whom the request should be targeted for the information that is to be returned.</xs:documentation>
			</xs:annotation>
			<xs:simpleType>
				<xs:restriction base="xs:NMTOKEN">
					<xs:enumeration value="Core">
						<xs:annotation>
							<xs:documentation xml:lang="en">Requested information should be based on travel data (availabiltiy, rates) stored inhouse.</xs:documentation>
						</xs:annotation>
					</xs:enumeration>
					<xs:enumeration value="Vendor">
						<xs:annotation>
							<xs:documentation xml:lang="en">Requested information should taken from the vendor's system.</xs:documentation>
						</xs:annotation>
					</xs:enumeration>
				</xs:restriction>
			</xs:simpleType>
		</xs:attribute>
		<xs:attribute name="FlightSvcInfoIndicator" type="xs:boolean" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">If true, flight service information should be returned in the response.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="DisplayOrder" type="DisplayOrderType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Specifies the order in which the information should be returned.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="ReducedDataIndicator" type="xs:boolean" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">If true, reduced data should be returned</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="BaseFaresOnlyIndicator" type="xs:boolean" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">If true, only base fare information should be returned</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="SearchType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Specifies (at a high level) the type of search criteria for this request.</xs:documentation>
			</xs:annotation>
			<xs:simpleType>
				<xs:restriction base="xs:NMTOKEN">
					<xs:enumeration value="Normal">
						<xs:annotation>
							<xs:documentation xml:lang="en">No special conditions (default)</xs:documentation>
						</xs:annotation>
					</xs:enumeration>
					<xs:enumeration value="Window">
						<xs:annotation>
							<xs:documentation xml:lang="en">Search should be done for a window of time.</xs:documentation>
						</xs:annotation>
					</xs:enumeration>
					<xs:enumeration value="ArrivalTime">
						<xs:annotation>
							<xs:documentation xml:lang="en">Search should be based on arrival time.</xs:documentation>
						</xs:annotation>
					</xs:enumeration>
					<xs:enumeration value="PowerFlight">
						<xs:annotation>
							<xs:documentation xml:lang="en">Checks availability and fares by manually entered data.</xs:documentation>
						</xs:annotation>
					</xs:enumeration>
					<xs:enumeration value="AvailableOnly">
						<xs:annotation>
							<xs:documentation xml:lang="en">Check for available flights.</xs:documentation>
						</xs:annotation>
					</xs:enumeration>
					<xs:enumeration value="WaitlistOnly">
						<xs:annotation>
							<xs:documentation xml:lang="en">Only check only for waitlist flights.</xs:documentation>
						</xs:annotation>
					</xs:enumeration>
					<xs:enumeration value="AvailableAndWaitlist">
						<xs:annotation>
							<xs:documentation xml:lang="en">Check for available and waitlisted flights.</xs:documentation>
						</xs:annotation>
					</xs:enumeration>
					<xs:enumeration value="FreighterFlights">
						<xs:annotation>
							<xs:documentation xml:lang="en">Check for available freighter flights.</xs:documentation>
						</xs:annotation>
					</xs:enumeration>
					<xs:enumeration value="Reward">
						<xs:annotation>
							<xs:documentation xml:lang="en">Check for available frequent flyer reward flights and classes.</xs:documentation>
						</xs:annotation>
					</xs:enumeration>
				</xs:restriction>
			</xs:simpleType>
		</xs:attribute>
		<xs:attribute name="AvailabilityIndicator" type="xs:boolean" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">If true, booking class availability should be returned in the response for each of the flight segments.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:attributeGroup>
	<xs:attributeGroup name="BookingClassAvailabilityGroup">
		<xs:annotation>
			<xs:documentation xml:lang="en">Holds booking class and available seats quantity.</xs:documentation>
		</xs:annotation>
		<xs:attribute name="ResBookDesigCode" type="UpperCaseAlphaLength1to2" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Reservation Booking Designator (RBD) code (e.g. Y).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="ResBookDesigQuantity" type="NumericStringLength1to3" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Seat quantity available for this Reservation Booking Designator (RBD).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="ResBookDesigStatusCode" type="OTA_CodeType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Refer to OpenTravel Code List Res Book Designator Status Code (RBD). This provides the status (e.g. waitlist open, available, available by direct request to supplier only).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:attributeGroup>
	<xs:attributeGroup name="BookingClassPrefGroup">
		<xs:annotation>
			<xs:documentation xml:lang="en">Booking class code and preference level for specifying booking classes preferred/not preferred in a request.</xs:documentation>
		</xs:annotation>
		<xs:attribute name="ResBookDesigCode" type="UpperCaseAlphaLength1to2" use="required">
			<xs:annotation>
				<xs:documentation xml:lang="en">Booking class code</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attributeGroup ref="PreferLevelGroup">
			<xs:annotation>
				<xs:documentation xml:lang="en">The preference level for the booking class.</xs:documentation>
			</xs:annotation>
		</xs:attributeGroup>
		<xs:attribute name="ResBookDesigCodeType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">To specify the types of RBD's (Res Book Desig Codes) that should be returned as opposed to a specific RBD.</xs:documentation>
			</xs:annotation>
			<xs:simpleType>
				<xs:restriction base="xs:NMTOKEN">
					<xs:enumeration value="Displayable">
						<xs:annotation>
							<xs:documentation xml:lang="en">Only return displayable RBDs.</xs:documentation>
						</xs:annotation>
					</xs:enumeration>
					<xs:enumeration value="NonDisplayable">
						<xs:annotation>
							<xs:documentation xml:lang="en">Only return non-displayable RBD's.</xs:documentation>
						</xs:annotation>
					</xs:enumeration>
					<xs:enumeration value="All">
						<xs:annotation>
							<xs:documentation xml:lang="en">Return all RBD's, displayable and non-displayable.</xs:documentation>
						</xs:annotation>
					</xs:enumeration>
				</xs:restriction>
			</xs:simpleType>
		</xs:attribute>
	</xs:attributeGroup>
	<xs:attributeGroup name="CodeListAirGroup">
		<xs:annotation>
			<xs:documentation xml:lang="en">Used to specify a code and its associated attributes; meaning is derived from actual use (plus SecondaryCode and SupplierCode).</xs:documentation>
		</xs:annotation>
		<xs:attributeGroup ref="CodeListGroup"/>
		<xs:attribute name="SecondaryCode" type="StringLength1to16" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">An additional attribute to allow flexibility for particular organizations who require an additional code.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="SupplierCode" type="UpperCaseAlphaLength1to3" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">An additional attribute to allow flexibility for particular organizations who require an additional supplier code.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:attributeGroup>
	<xs:attributeGroup name="DirectAndStopsGroup">
		<xs:annotation>
			<xs:documentation xml:lang="en">Attribute collection providing information on direct flight categorization and the number of stops made.</xs:documentation>
		</xs:annotation>
		<xs:attribute name="DirectFlightsOnly" type="xs:boolean" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Indicates the same flight number on the same airline regardless of number of stops in most cases.</xs:documentation>
				<xs:documentation xml:lang="en">
					<LegacyDefaultValue>false</LegacyDefaultValue>
				</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="NumberStops" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en"> Information regarding the number of stops made.</xs:documentation>
			</xs:annotation>
			<xs:simpleType>
				<xs:restriction base="xs:nonNegativeInteger">
					<xs:maxInclusive value="9"/>
					<xs:minInclusive value="0"/>
				</xs:restriction>
			</xs:simpleType>
		</xs:attribute>
	</xs:attributeGroup>
	<xs:attributeGroup name="DiscountPricingGroup">
		<xs:annotation>
			<xs:documentation xml:lang="en">The information needed for applying a discount to a fare.</xs:documentation>
		</xs:annotation>
		<xs:attribute name="Purpose" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Used to specify the purpose of the discount pricing.</xs:documentation>
			</xs:annotation>
			<xs:simpleType>
				<xs:restriction base="xs:NMTOKEN">
					<xs:enumeration value="Airline">
						<xs:annotation>
							<xs:documentation xml:lang="en">The discount pricing is from the airline.</xs:documentation>
						</xs:annotation>
					</xs:enumeration>
					<xs:enumeration value="Custom">
						<xs:annotation>
							<xs:documentation xml:lang="en">The discount pricing is a custom discount.</xs:documentation>
						</xs:annotation>
					</xs:enumeration>
				</xs:restriction>
			</xs:simpleType>
		</xs:attribute>
		<xs:attribute name="Type" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Specifies the type of amount being sent.</xs:documentation>
			</xs:annotation>
			<xs:simpleType>
				<xs:restriction base="xs:NMTOKEN">
					<xs:enumeration value="Amount">
						<xs:annotation>
							<xs:documentation xml:lang="en">The discount is an amount.</xs:documentation>
						</xs:annotation>
					</xs:enumeration>
					<xs:enumeration value="Percent">
						<xs:annotation>
							<xs:documentation xml:lang="en">The discount is a percentage.</xs:documentation>
						</xs:annotation>
					</xs:enumeration>
					<xs:enumeration value="PlusUpAmount">
						<xs:annotation>
							<xs:documentation xml:lang="en">A discount on an international fare that is not a stored fare.</xs:documentation>
						</xs:annotation>
					</xs:enumeration>
				</xs:restriction>
			</xs:simpleType>
		</xs:attribute>
		<xs:attribute name="Usage" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Used to specify how the discount is to be applied.</xs:documentation>
			</xs:annotation>
			<xs:simpleType>
				<xs:restriction base="xs:NMTOKEN">
					<xs:enumeration value="Plus">
						<xs:annotation>
							<xs:documentation xml:lang="en">The discount being applied is an increase to the fare.</xs:documentation>
						</xs:annotation>
					</xs:enumeration>
					<xs:enumeration value="Minus">
						<xs:annotation>
							<xs:documentation xml:lang="en">The discount being applied is subtracted from the fare.</xs:documentation>
						</xs:annotation>
					</xs:enumeration>
				</xs:restriction>
			</xs:simpleType>
		</xs:attribute>
		<xs:attribute name="Discount" type="MoneyOrPercentageType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The monetary amount or percentage of discount that should be applied.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="TicketDesignatorCode" type="StringLength1to16" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Specifies the code applicable to the fare that is being discounted.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="Text" type="StringLength1to128" use="optional"/>
	</xs:attributeGroup>
	<xs:attributeGroup name="FareRestrictPrefGroup">
		<xs:annotation>
			<xs:documentation xml:lang="en">Identifies preferences for airfare restrictions acceptable or not acceptable for a given travel situation.</xs:documentation>
		</xs:annotation>
		<xs:attributeGroup ref="PreferLevelGroup"/>
		<xs:attribute name="FareRestriction" type="OTA_CodeType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Identifies the type of fare restriction, such as None, Advance Purchase and Change Penalties. Refer to OpenTravel Code List Fare Restriction (FAR).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="Date" type="DateOrDateTimeType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">A date that is associated to the fare restriction.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:attributeGroup>
	<xs:attributeGroup name="FareTypePrefGroup">
		<xs:annotation>
			<xs:documentation xml:lang="en">Specifies a type of fare and a preference level for the type.</xs:documentation>
		</xs:annotation>
		<xs:attribute name="FareType" use="required">
			<xs:annotation>
				<xs:documentation xml:lang="en">The type of fare required (e.g. unrestricted, excursion). Refer to the Fare Qualifier OpenTravel Code list (FAQ).</xs:documentation>
			</xs:annotation>
			<xs:simpleType>
				<xs:union memberTypes="UpperCaseAlphaLength1to3 OTA_CodeType"/>
			</xs:simpleType>
		</xs:attribute>
		<xs:attribute name="PreferLevel" type="PreferLevelType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Used to indicate a level of preference for a fare type.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:attributeGroup>
	<xs:attributeGroup name="FlifoLegAttributes">
		<xs:annotation>
			<xs:documentation xml:lang="en">Provides information for a flight leg.</xs:documentation>
		</xs:annotation>
		<xs:attribute name="FlightNumber" type="FlightNumberType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en"> The flight number of the flight.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="JourneyDuration" type="xs:duration" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The duration of the flight from departure location to destination  location.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="GroundDuration" type="xs:duration" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The duration of a ground stop.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="AccumulatedDuration" type="xs:duration" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The total duration time of the flight. This is the combination of both JourneyDuration and GroundDuration.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="LegDistance" type="xs:nonNegativeInteger" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Miles aquired per flight segments, usually used for earning of frequent flyer miles.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:attributeGroup>
	<xs:attributeGroup name="FlightRefNumberGroup">
		<xs:annotation>
			<xs:documentation xml:lang="en">A reference place holder used as a pointer to link back to a flight number.</xs:documentation>
		</xs:annotation>
		<xs:attribute name="RPH" type="RPH_Type">
			<xs:annotation>
				<xs:documentation xml:lang="en">A reference pointer used to link a flight number to the search or response.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:attributeGroup>
	<xs:attributeGroup name="GlobalDirectionGroup">
		<xs:annotation>
			<xs:documentation xml:lang="en">The global direction and maximum permitted miles for the fare.</xs:documentation>
		</xs:annotation>
		<xs:attribute name="GlobalIndicatorCode" type="GlobalIndicatorType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Indicates the global direction.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="MaximumPermittedMileage" type="xs:integer" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The maximum mileage (in miles) that can be travelled under this contract.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="IncludeIndicator" type="xs:boolean" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">When true, the global direction can be used for travel. When false, the global direction cannot be used for travel.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:attributeGroup>
	<xs:attributeGroup name="NegotiatedFareAttributes">
		<xs:annotation>
			<xs:documentation xml:lang="en">Attribute collection providing private fare profile.</xs:documentation>
		</xs:annotation>
		<xs:attribute name="NegotiatedFare" type="xs:boolean" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Indicator to show if this is a private fare.</xs:documentation>
				<xs:documentation xml:lang="en">
					<LegacyDefaultValue>false</LegacyDefaultValue>
				</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="NegotiatedFareCode" type="xs:string" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Code used to identify the private fare.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:attributeGroup>
	<xs:attributeGroup name="OperationTimeGroup">
		<xs:annotation>
			<xs:documentation xml:lang="en">Provides information for operational events for a flight leg (e.g., off-ground)</xs:documentation>
		</xs:annotation>
		<xs:attribute name="Time" type="TimeOrDateTimeType" use="required">
			<xs:annotation>
				<xs:documentation xml:lang="en">Time or date/time an operational event happened.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="OperationType" type="OTA_CodeType" use="required">
			<xs:annotation>
				<xs:documentation xml:lang="en">Type of operational event (e.g., off-ground). References the OpenTravel Code Table Other Time Type (OTT).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="TimeType" use="required">
			<xs:annotation>
				<xs:documentation xml:lang="en">Describes whether the operational event time is scheduled, estimated or actual.</xs:documentation>
			</xs:annotation>
			<xs:simpleType>
				<xs:restriction base="xs:NMTOKEN">
					<xs:enumeration value="Actual">
						<xs:annotation>
							<xs:documentation xml:lang="en">The actual operation time.</xs:documentation>
						</xs:annotation>
					</xs:enumeration>
					<xs:enumeration value="Scheduled">
						<xs:annotation>
							<xs:documentation xml:lang="en">The scheduled time.</xs:documentation>
						</xs:annotation>
					</xs:enumeration>
					<xs:enumeration value="Estimated">
						<xs:annotation>
							<xs:documentation xml:lang="en">The estimated time.</xs:documentation>
						</xs:annotation>
					</xs:enumeration>
				</xs:restriction>
			</xs:simpleType>
		</xs:attribute>
		<xs:attribute name="ReasonCode" type="OTA_CodeType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The reason attributed to a delay or cancellation. Refers to OpenTravel Code Table Flight Delay Code (FDC).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:attributeGroup>
	<xs:attributeGroup name="OriginDestinationGroup">
		<xs:annotation>
			<xs:documentation>Used to specify the origin and destination airport/city codes.</xs:documentation>
		</xs:annotation>
		<xs:attribute name="OriginCityCode" type="StringLength1to8" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The origin airport city code for this EMD.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="OriginCodeContext" type="StringLength1to32" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Identifies the context of the origin city code such as IATA, ARC, or internal code.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="DestinationCityCode" type="StringLength1to8" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The destination airport city code for this EMD.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="DestinationCodeContext" type="StringLength1to32" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Identifies the context of the destination city code such as IATA, ARC, or internal code.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:attributeGroup>
	<xs:attributeGroup name="PricingInfoGroup">
		<xs:annotation>
			<xs:documentation xml:lang="en">Use to specify a type of pricing information and whether it should be applied or not applied to the pricing.</xs:documentation>
		</xs:annotation>
		<xs:attribute name="Type" type="OTA_CodeType" use="required">
			<xs:annotation>
				<xs:documentation xml:lang="en">Used to specify the type of pricing information. References OpenTravel Code list Pricing Processing Code (PPC).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="ExcludeInd" type="xs:boolean" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">When true the information in the type attribute should not be applied to the pricing.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="Qualifier" type="OTA_CodeType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Used to give additional information on the pricing preference. Refer to OpenTravel Code List Pricing Qualification Code (PQC).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:attributeGroup>
	<xs:attributeGroup name="PriceRequestAttributes">
		<xs:annotation>
			<xs:documentation xml:lang="en">Attribute collection used to describe a price request.</xs:documentation>
		</xs:annotation>
		<xs:attribute name="FareQualifier" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Used to specify the type of fare required. Refer to OpenTravel Code List Fare Qualifier (FAQ) or use airline industry standard fare codes.</xs:documentation>
			</xs:annotation>
			<xs:simpleType>
				<xs:union memberTypes="OTA_CodeType UpperCaseAlphaLength1to3"/>
			</xs:simpleType>
		</xs:attribute>
		<xs:attribute name="NegotiatedFaresOnly" type="xs:boolean" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Indicator to identify whether or not the price is applicable only to private fares.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="CurrencyCode" type="AlphaLength3" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Type of funds preferred for reviewing monetary values, in ISO 4217 codes.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="PricingSource" type="PricingSourceType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">It can be used to indicate whether the fare is public or private.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="Reprice" type="xs:boolean" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">If true repricing is requested.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="ValidatingAirlineCode" type="AlphaNumericStringLength1to8" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The code for the validating airline.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="RequestedTicketingDate" type="DateOrDateTimeType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The date on which the customer requests the booking to be ticketed.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="SaleCountry" type="ISO3166" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The country in which the booking will be made (sold).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:attributeGroup>
	<xs:attributeGroup name="ReasonForIssuanceGroup">
		<xs:annotation>
			<xs:documentation>Used to specify the reason code and subcode and the description.</xs:documentation>
		</xs:annotation>
		<xs:attribute name="Code" type="OTA_CodeType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The reason for the issuance of the EMD.  Refer to OpenTravel Code List EMD Reason For Issuance- 4183 (ERI).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="SubCode" type="OTA_CodeType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">A subcode for the reason of the issuance of the EMD. Refer to OpenTravel Code List EMD Reason For Issuance- 4183 (ERI).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="Description" type="StringLength1to128" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The corresponding description to the reason for issuance code.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:attributeGroup>
	<xs:attributeGroup name="SeatDetailsGroup">
		<xs:annotation>
			<xs:documentation xml:lang="en">Describes the seat attributes.</xs:documentation>
		</xs:annotation>
		<xs:attribute name="SeatAvailability" type="OTA_CodeType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Availability status of the particular seat. Refer to OpenTravel Code List Seat Availability (SAV).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="SeatNumber" type="AlphaLength1" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Actual seat number within a particular row, typically A, B etc.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="SeatCharacteristics" type="ListOfOTA_CodeType" use="required">
			<xs:annotation>
				<xs:documentation xml:lang="en">Describes the characteristics of a specific seat. Refer to OpenTravel Code List Air Seat Type (AST).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="AirBookDesigCode" type="AlphaLength1to2" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Allows that a seat may be assigned a class code.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="SeatSequenceNumber" type="xs:integer" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The absolute sequence of seat within a row. (Enabling to show seats/locations outside the physical sequence. e.g. Aisles, galleys or Jumpseats)</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:attributeGroup>
	<xs:attributeGroup name="TravelerRefNumberGroup">
		<xs:annotation>
			<xs:documentation xml:lang="en">A reference place holder used as a pointer to link back to the traveler.</xs:documentation>
		</xs:annotation>
		<xs:attribute name="RPH" type="RPH_Type" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">A unique reference for the traveler.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="SurnameRefNumber" type="StringLength1to8" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Used to identify and associate travelers with the same surname. </xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:attributeGroup>
	<xs:complexType name="AdvResTicketingType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Container used to hold information regarding advance reservation and/or advance ticketing.</xs:documentation>
		</xs:annotation>
		<xs:sequence minOccurs="0">
			<xs:element name="AdvReservation" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Specifies constraints on when advance reservations can be made.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:attribute name="LatestTimeOfDay" type="TimeOrDateTimeType" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">The time of day by which reservations must be made on the last day that advance reservations can be made.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="LatestPeriod" type="NumericStringLength1to3" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">The amount of elapsed time or number of occurrences of a day of the week before departure needed to satisfy an advance reservation requirement.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="LatestUnit" type="StayUnitType" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">The unit of elapsed time or the day of the week to be applied to the LatestPeriod value.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
				</xs:complexType>
			</xs:element>
			<xs:element name="AdvTicketing" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Specifies advance ticketing restrictions.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:attribute name="FromResTimeOfDay" type="TimeOrDateTimeType" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">The time of day after reservations are made by which a ticket must be purchased.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="FromResPeriod" type="NumericStringLength1to3" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">A length of time expressed as either an amount of time or the number of occurrences of a day of the week after reservations are made that a ticket must be purchased.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="FromResUnit" type="StayUnitType" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">The unit of elapsed time or the day of the week to be applied to the period after reservation are made that a ticket must be purchased.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="FromDepartTimeOfDay" type="TimeOrDateTimeType" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">The time of day prior to departure when that a ticket must be purchased.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="FromDepartPeriod" type="NumericStringLength1to3" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">A length of time expressed as either an amount of time or the number of occurrences of a day of the week before departure that a ticket must be purchased.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="FromDepartUnit" type="StayUnitType" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">The unit of elapsed time or the day of the week to be applied to the the period before departure that a ticket must be purchased.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="AdvResInd" type="xs:boolean">
			<xs:annotation>
				<xs:documentation xml:lang="en">Indicator for identifying whether or not advance reservation restrictions are involved in the request or response.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="AdvTicketingInd" type="xs:boolean" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Indicator for identifying whether or not advance ticketing restrictions are involved in the request or response.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="RequestedTicketingDate" type="DateOrDateTimeType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The date a traveller wishes to ticket their reservation.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:complexType>
	<xs:complexType name="AirFeeType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Defines the data fields available for the fees.</xs:documentation>
		</xs:annotation>
		<xs:simpleContent>
			<xs:extension base="ShortDescriptionType">
				<xs:attribute name="FeeCode" type="StringLength1to16" use="required">
					<xs:annotation>
						<xs:documentation xml:lang="en">Identifies the code for the fee.</xs:documentation>
					</xs:annotation>
				</xs:attribute>
				<xs:attributeGroup ref="CurrencyAmountGroup">
					<xs:annotation>
						<xs:documentation xml:lang="en">Provides a monetary amount and the currency code to reflect the currency in which this amount is expressed.</xs:documentation>
						<xs:documentation xml:lang="en">Defines a fee in terms of its amount, currency and decimal places.</xs:documentation>
					</xs:annotation>
				</xs:attributeGroup>
				<xs:attribute name="TaxPercentage" type="Percentage" use="optional">
					<xs:annotation>
						<xs:documentation xml:lang="en">Indicates the tax percentage included in this fee (e.g., the Value Added Tax (VAT) percentage).</xs:documentation>
					</xs:annotation>
				</xs:attribute>
				<xs:attribute name="Operation" type="ActionType" use="optional">
					<xs:annotation>
						<xs:documentation xml:lang="en">Indicates the required modification to the element.</xs:documentation>
					</xs:annotation>
				</xs:attribute>
				<xs:attribute name="FeeTransactionType" use="optional">
					<xs:annotation>
						<xs:documentation xml:lang="en">Used to indicate the type of fee  (e.g. charge, refund or exempt).</xs:documentation>
					</xs:annotation>
					<xs:simpleType>
						<xs:restriction base="xs:NMTOKEN">
							<xs:enumeration value="charge">
								<xs:annotation>
									<xs:documentation xml:lang="en">Indicates that a fee or a combination of fees is to be charged.</xs:documentation>
								</xs:annotation>
							</xs:enumeration>
							<xs:enumeration value="exempt">
								<xs:annotation>
									<xs:documentation xml:lang="en">Indicates that a fee or a combination of fees are exempt on the ticket.</xs:documentation>
								</xs:annotation>
							</xs:enumeration>
							<xs:enumeration value="refund">
								<xs:annotation>
									<xs:documentation xml:lang="en">Indicates that a fee or a combination of fees is remaining on the ticket for refund or reissue.</xs:documentation>
								</xs:annotation>
							</xs:enumeration>
							<xs:enumeration value="reserve">
								<xs:annotation>
									<xs:documentation xml:lang="en">Indicates that a fee or a combination of fees has been collected and is held in reserve on the ticket for distribution.</xs:documentation>
								</xs:annotation>
							</xs:enumeration>
						</xs:restriction>
					</xs:simpleType>
				</xs:attribute>
				<xs:attribute name="RPH" type="RPH_Type" use="optional">
					<xs:annotation>
						<xs:documentation xml:lang="en">A unique reference for the fee type (commonly used for modification.)</xs:documentation>
					</xs:annotation>
				</xs:attribute>
			</xs:extension>
		</xs:simpleContent>
	</xs:complexType>
	<xs:complexType name="AirItineraryPricingInfoType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Pricing Information for an Air Itinerary.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="ItinTotalFare" minOccurs="0" maxOccurs="2">
				<xs:annotation>
					<xs:documentation xml:lang="en">Total price of the itinerary.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:complexContent>
						<xs:extension base="FareType">
							<xs:attribute name="Usage" use="optional">
								<xs:annotation>
									<xs:documentation xml:lang="en">Specifies the usage of the passenger fare structure.</xs:documentation>
								</xs:annotation>
								<xs:simpleType>
									<xs:restriction base="xs:NMTOKEN">
										<xs:enumeration value="PassengerFare">
											<xs:annotation>
												<xs:documentation xml:lang="en">The fare information for the passenger fees.</xs:documentation>
											</xs:annotation>
										</xs:enumeration>
										<xs:enumeration value="TicketFee">
											<xs:annotation>
												<xs:documentation xml:lang="en">The fare information for the ticket fees.</xs:documentation>
											</xs:annotation>
										</xs:enumeration>
									</xs:restriction>
								</xs:simpleType>
							</xs:attribute>
						</xs:extension>
					</xs:complexContent>
				</xs:complexType>
			</xs:element>
			<xs:element name="PTC_FareBreakdowns" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">This is a collection of PTC Fare Breakdowns.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="PTC_FareBreakdown" type="PTCFareBreakdownType" maxOccurs="256">
							<xs:annotation>
								<xs:documentation xml:lang="en"> Per passenger type code pricing for a travel itinerary. </xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="FareInfos" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">This is a collection of FareInfo.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="FareInfo" maxOccurs="10">
							<xs:annotation>
								<xs:documentation xml:lang="en">Detailed information on individual priced fares</xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:annotation>
									<xs:documentation xml:lang="en"> Information used to define a fare and its associated rules information.</xs:documentation>
								</xs:annotation>
								<xs:complexContent>
									<xs:extension base="FareInfoType">
										<xs:sequence>
											<xs:element ref="TPA_Extensions" minOccurs="0"/>
										</xs:sequence>
										<xs:attribute name="Operation" type="ActionType" use="optional">
											<xs:annotation>
												<xs:documentation xml:lang="en">Indicates the required modification to the element.</xs:documentation>
											</xs:annotation>
										</xs:attribute>
										<xs:attribute name="RPH" type="RPH_Type" use="optional">
											<xs:annotation>
												<xs:documentation xml:lang="en">Provides a reference to a specific FareInfo item between an air modification request and the existing air reservation.</xs:documentation>
											</xs:annotation>
										</xs:attribute>
									</xs:extension>
								</xs:complexContent>
							</xs:complexType>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="PriceRequestInformation" type="PriceRequestInformationType" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Identifies pricing source, if negotiated fares are requested and if it is a reprice request.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="PricingSource" type="PricingSourceType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Used to indicate whether the pricing is public or private</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="ValidatingAirlineCode" type="StringLength1to8" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The code of the validating airline.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="QuoteID" type="StringLength1to128" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">A text field used to provide a special ID code that is associated with the priced itinerary that may be used in the reservation request in order to obtain the quoted rate.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:complexType>
	<xs:complexType name="AirItineraryType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Specifies the origin and destination of the traveler. Attributes: DirectionInd - A directional indicator that identifies a type of air booking, either one-way, round-trip, or open-jaw with the enumeration of (OneWay | RT | OpenJaw) respectively; ActionCode - Indicates the status of the booking, such as OK or Wait-List; NumberInParty - Indicates the traveler count.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="OriginDestinationOptions">
				<xs:annotation>
					<xs:documentation xml:lang="en">A collection of  OriginDestinationOption</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="OriginDestinationOption" maxOccurs="99">
							<xs:annotation>
								<xs:documentation xml:lang="en">A container for OriginDestinationOptionType.</xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:complexContent>
									<xs:extension base="OriginDestinationOptionType">
										<xs:attribute name="RefNumber" type="Numeric1to99" use="optional">
											<xs:annotation>
												<xs:documentation xml:lang="en">When a PricedItinerary element contains multiple solutions and a single price, this attribute identifies the OriginDestinationPair from the request.</xs:documentation>
											</xs:annotation>
										</xs:attribute>
									</xs:extension>
								</xs:complexContent>
							</xs:complexType>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="DirectionInd" type="AirTripType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en"> Identifies whether travel is: one way, return trip, circle trip, open jaw, other.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:complexType>
	<xs:complexType name="AirReservationType">
		<xs:annotation>
			<xs:documentation xml:lang="en"> Contains all booking response information pertaining to a completed reservation.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="AirItinerary" type="AirItineraryType" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">A collection of all flight segments requested for booking.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="PriceInfo" type="BookingPriceInfoType" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Pricing information for the air itinerary to be booked e.g. this data could come from the OTA_AirPriceRS data.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="TravelerInfo" type="TravelerInfoType" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">All traveler information relevant to a booking request.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Fulfillment" type="FulfillmentType" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">All payment information relevant to a booking request.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Ticketing" type="TicketingInfoType" minOccurs="0" maxOccurs="99">
				<xs:annotation>
					<xs:documentation xml:lang="en">Information used to specify the ticketing arrangement or a summary of the associated ticket(s).</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Queues" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">A container for queue information.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="Queue" maxOccurs="10">
							<xs:annotation>
								<xs:documentation xml:lang="en">Specifies queue information for this booking.</xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:attributeGroup ref="QueueGroup"/>
								<xs:attribute name="DateTime" type="DateOrDateTimeType" use="optional">
									<xs:annotation>
										<xs:documentation xml:lang="en">Date/time when the time initiated queuing should take place.</xs:documentation>
									</xs:annotation>
								</xs:attribute>
								<xs:attribute name="Text" type="StringLength1to128" use="optional">
									<xs:annotation>
										<xs:documentation xml:lang="en">Text describing why the queuing takes place.
</xs:documentation>
									</xs:annotation>
								</xs:attribute>
								<xs:attribute name="CarrierCode" type="StringLength1to8" use="optional">
									<xs:annotation>
										<xs:documentation xml:lang="en">Identifies airline/system on which the reservation is being queued.</xs:documentation>
									</xs:annotation>
								</xs:attribute>
								<xs:attribute name="Operation" type="ActionType" use="optional">
									<xs:annotation>
										<xs:documentation xml:lang="en">Indicates the required modification to the element.</xs:documentation>
									</xs:annotation>
								</xs:attribute>
							</xs:complexType>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="BookingReferenceID" minOccurs="0" maxOccurs="99">
				<xs:annotation>
					<xs:documentation xml:lang="en">Optional field available for use by trading partners as determined by their needs.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:complexContent>
						<xs:extension base="UniqueID_Type">
							<xs:attribute name="FlightRefNumberRPHList" type="ListOfRPH" use="optional">
								<xs:annotation>
									<xs:documentation xml:lang="en">This provides a list of flight segment RPHs associated with a specific Booking Reference ID.</xs:documentation>
								</xs:annotation>
							</xs:attribute>
						</xs:extension>
					</xs:complexContent>
				</xs:complexType>
			</xs:element>
			<xs:element name="Comment" type="FormattedTextTextType" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Textual information for the reservation.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="PricingOverview" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Information applicable to the pricing of the reservation.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="PricingIndicator" minOccurs="0" maxOccurs="5">
							<xs:annotation>
								<xs:documentation xml:lang="en">Specific information about the price.</xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:attributeGroup ref="PricingInfoGroup">
									<xs:annotation>
										<xs:documentation xml:lang="en">Specific information about the price.</xs:documentation>
									</xs:annotation>
								</xs:attributeGroup>
							</xs:complexType>
						</xs:element>
						<xs:element name="Account" minOccurs="0" maxOccurs="5">
							<xs:annotation>
								<xs:documentation xml:lang="en">Specifies an account code applicable to the pricing and reservation.</xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:attribute name="Code" type="StringLength1to32" use="required">
									<xs:annotation>
										<xs:documentation xml:lang="en">The account code applicable to the fare.</xs:documentation>
									</xs:annotation>
								</xs:attribute>
							</xs:complexType>
						</xs:element>
						<xs:element name="Comment" type="FreeTextType" minOccurs="0" maxOccurs="10">
							<xs:annotation>
								<xs:documentation xml:lang="en">A comment applicable to all the pricing information.</xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
					<xs:attribute name="StatisticalCode" type="AlphaNumericStringLength1to8" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">Used for domestic or international sales differentiation.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="ValidatingAirlineCode" type="AlphaNumericStringLength1to8" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">The validating airline code for the ticketing.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="DepartureDate" type="DateOrDateTimeType" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">The departure date of the first leg of the booked itinerary.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="PriceType" type="AlphaNumericStringLength1to8" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">Used to specify the type of price.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="NUC_Rate" type="xs:decimal" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">The neutral unit currency rate.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="ExchangeRate" type="xs:decimal" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">The currency exchange rate.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
				</xs:complexType>
			</xs:element>
			<xs:element name="EMD_Info" type="EMD_Type" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Provides the electronic miscellaneous document information for a previously issued EMD.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="LastModified" type="DateOrDateTimeType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Specifies the date the reservation was last modified.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:complexType>
	<xs:complexType name="AirTaxType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Defines the data fields available for air tax. The element text of this type may contain a description of the tax.</xs:documentation>
		</xs:annotation>
		<xs:simpleContent>
			<xs:extension base="ShortDescriptionType">
				<xs:attribute name="TaxCode" type="StringLength1to16" use="optional">
					<xs:annotation>
						<xs:documentation xml:lang="en">Identifies the code for the tax.</xs:documentation>
					</xs:annotation>
				</xs:attribute>
				<xs:attributeGroup ref="CurrencyAmountGroup"/>
				<xs:attribute name="TaxCountry" type="ISO3166" use="optional">
					<xs:annotation>
						<xs:documentation xml:lang="en">Used to identify the country imposing the tax.</xs:documentation>
					</xs:annotation>
				</xs:attribute>
				<xs:attribute name="TaxName" type="StringLength1to64" use="optional">
					<xs:annotation>
						<xs:documentation xml:lang="en">Identifies the tax code by name.</xs:documentation>
					</xs:annotation>
				</xs:attribute>
				<xs:attribute name="TaxExemptInd" type="xs:boolean" use="optional">
					<xs:annotation>
						<xs:documentation xml:lang="en">
							<DeprecationWarning>This attribute is a candidate for deprecation. Its use is not recommended. Please instead use the new TaxTransactionType attribute which has taken its place. Deprecation warning added in 2009A.</DeprecationWarning>
						</xs:documentation>
						<xs:documentation xml:lang="en">When true, the fare is exempt from the tax specified by the tax code.</xs:documentation>
					</xs:annotation>
				</xs:attribute>
				<xs:attribute name="Operation" type="ActionType" use="optional">
					<xs:annotation>
						<xs:documentation xml:lang="en">Indicates the required modification to the element.</xs:documentation>
					</xs:annotation>
				</xs:attribute>
				<xs:attribute name="TaxTransactionType" use="optional">
					<xs:annotation>
						<xs:documentation xml:lang="en">Used to indicate the type of tax (e.g. charge, refund or exempt).</xs:documentation>
					</xs:annotation>
					<xs:simpleType>
						<xs:restriction base="xs:NMTOKEN">
							<xs:enumeration value="charge">
								<xs:annotation>
									<xs:documentation xml:lang="en">Indicates that a tax or a combination of taxes is to be charged.</xs:documentation>
								</xs:annotation>
							</xs:enumeration>
							<xs:enumeration value="exempt">
								<xs:annotation>
									<xs:documentation xml:lang="en">Indicates that a tax or a combination of taxes are exempt on the ticket.</xs:documentation>
								</xs:annotation>
							</xs:enumeration>
							<xs:enumeration value="refund">
								<xs:annotation>
									<xs:documentation xml:lang="en">Indicates that a tax or a combination of taxes is remaining on the ticket for refund or reissue.</xs:documentation>
								</xs:annotation>
							</xs:enumeration>
							<xs:enumeration value="reserve">
								<xs:annotation>
									<xs:documentation xml:lang="en">Indicates that a tax or a combination of taxes has been collected and is held in reserve on the ticket for distribution.</xs:documentation>
								</xs:annotation>
							</xs:enumeration>
							<xs:enumeration value="paid">
								<xs:annotation>
									<xs:documentation xml:lang="en">Indicates that a tax or a combination of taxes have already been paid for.</xs:documentation>
								</xs:annotation>
							</xs:enumeration>
						</xs:restriction>
					</xs:simpleType>
				</xs:attribute>
				<xs:attribute name="RPH" type="RPH_Type" use="optional">
					<xs:annotation>
						<xs:documentation xml:lang="en">A unique reference for the tax type (commonly used for modification.)</xs:documentation>
					</xs:annotation>
				</xs:attribute>
				<xs:attribute name="RefundableInd" type="xs:boolean" use="optional">
					<xs:annotation>
						<xs:documentation xml:lang="en">If true, this tax amount may be refunded on ticket cancellation.</xs:documentation>
					</xs:annotation>
				</xs:attribute>
			</xs:extension>
		</xs:simpleContent>
	</xs:complexType>
	<xs:complexType name="AirTravelerType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Information about the person traveling. Gender - the gender of the customer, if needed. BirthDate - Date of Birth. Currency - the preferred currency in which monetary amounts should be returned.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="ProfileRef" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Stored information about a customer. May contain readily available information relevant to the booking.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="UniqueID" type="UniqueID_Type">
							<xs:annotation>
								<xs:documentation xml:lang="en"> An identifier used to uniquely reference a customer profile.</xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="PersonName" type="PersonNameType" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en"> Name information of the person traveling.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Telephone" minOccurs="0" maxOccurs="20">
				<xs:annotation>
					<xs:documentation xml:lang="en">Telephone number for the person traveling.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:attributeGroup ref="TelephoneInfoGroup"/>
					<xs:attribute name="Operation" type="ActionType" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">Indicates the required modification to the element.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="LocationCode" type="StringLength1to8" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">A 3 character ATA/ IATA city code of telephone location.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
				</xs:complexType>
			</xs:element>
			<xs:element name="Email" minOccurs="0" maxOccurs="3">
				<xs:annotation>
					<xs:documentation xml:lang="en">Email address of the person traveling.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:simpleContent>
						<xs:extension base="EmailType">
							<xs:attribute name="Operation" type="ActionType" use="optional">
								<xs:annotation>
									<xs:documentation xml:lang="en">Indicates the required modification to the element.</xs:documentation>
								</xs:annotation>
							</xs:attribute>
						</xs:extension>
					</xs:simpleContent>
				</xs:complexType>
			</xs:element>
			<xs:element name="Address" minOccurs="0" maxOccurs="5">
				<xs:annotation>
					<xs:documentation xml:lang="en"> Address information of the person traveling.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:complexContent>
						<xs:extension base="AddressType">
							<xs:attribute name="Operation" type="ActionType" use="optional">
								<xs:annotation>
									<xs:documentation xml:lang="en">Indicates the required modification to the element.</xs:documentation>
								</xs:annotation>
							</xs:attribute>
						</xs:extension>
					</xs:complexContent>
				</xs:complexType>
			</xs:element>
			<xs:element name="CustLoyalty" minOccurs="0" maxOccurs="5">
				<xs:annotation>
					<xs:documentation xml:lang="en"> Identifies the loyalty program(s) that the customer belongs to and associated information.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:attributeGroup ref="CustomerLoyaltyGroup"/>
					<xs:attribute name="Operation" type="ActionType" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">Indicates the required modification to the element.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
				</xs:complexType>
			</xs:element>
			<xs:element name="Document" minOccurs="0" maxOccurs="10">
				<xs:annotation>
					<xs:documentation xml:lang="en">Official travel document information associated with the person traveling.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:complexContent>
						<xs:extension base="DocumentType">
							<xs:attribute name="Operation" type="ActionType" use="optional">
								<xs:annotation>
									<xs:documentation xml:lang="en">Indicates the required modification to the element.</xs:documentation>
								</xs:annotation>
							</xs:attribute>
						</xs:extension>
					</xs:complexContent>
				</xs:complexType>
			</xs:element>
			<xs:element name="PassengerTypeQuantity" type="PassengerTypeQuantityType" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Specifies the number of travelers of a given passenger type (e.g., Adult, Child)</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="TravelerRefNumber" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Direct reference of traveler assigned by requesting system.  Used as a cross reference between data segments.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:attributeGroup ref="TravelerRefNumberGroup"/>
				</xs:complexType>
			</xs:element>
			<xs:element name="FlightSegmentRPHs" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Reference pointers to flight segments</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="FlightSegmentRPH" type="RPH_Type" maxOccurs="99">
							<xs:annotation>
								<xs:documentation xml:lang="en">Reference to the flight segments for this traveler</xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
		<xs:attributeGroup ref="GenderGroup"/>
		<xs:attributeGroup ref="PrivacyGroup"/>
		<xs:attribute name="BirthDate" type="xs:date" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Traveler's date of birth.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="CurrencyCode" type="AlphaLength3" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Specifies the code for the currency units.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="PassengerTypeCode" type="AlphaLength3" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Code used to indicate the type of traveler that will be traveling (e.g., ADT, CHD, INF, GRP).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="AccompaniedByInfant" type="xs:boolean" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Indicates if an infant accompanying a traveler is with or without a seat.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:complexType>
	<xs:complexType name="AuthorizationType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Information with which a traveller's identification is verified and/or charges are authorized.</xs:documentation>
			<xs:documentation xml:lang="en">ToDo - this types should be moved to OTA_CommonTypes. It's required in a common types file, since the both AuthRQ/RS use it.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:choice>
				<xs:element name="CheckAuthorization" minOccurs="0">
					<xs:annotation>
						<xs:documentation xml:lang="en">Specifies check information about the customer seeking authorization.</xs:documentation>
					</xs:annotation>
					<xs:complexType>
						<xs:sequence>
							<xs:element name="BankAcct" type="BankAcctType">
								<xs:annotation>
									<xs:documentation xml:lang="en">Specifies bank account information about the customer seeking authorization.</xs:documentation>
								</xs:annotation>
							</xs:element>
						</xs:sequence>
						<xs:attributeGroup ref="CurrencyAmountGroup">
							<xs:annotation>
								<xs:documentation xml:lang="en">The amount of money for which the the requester is seeking authorization.</xs:documentation>
							</xs:annotation>
						</xs:attributeGroup>
					</xs:complexType>
				</xs:element>
				<xs:element name="CreditCardAuthorization" minOccurs="0">
					<xs:annotation>
						<xs:documentation xml:lang="en">Specifies credit card information about the customer seeking authorization.</xs:documentation>
					</xs:annotation>
					<xs:complexType>
						<xs:sequence>
							<xs:element name="CreditCard" type="PaymentCardType">
								<xs:annotation>
									<xs:documentation xml:lang="en">Specifies the credit card information for which authorization is required. </xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="ID" type="UniqueID_Type" minOccurs="0" maxOccurs="5">
								<xs:annotation>
									<xs:documentation>Identification of an authorization party (e.g., merchant, acquirer).</xs:documentation>
								</xs:annotation>
							</xs:element>
						</xs:sequence>
						<xs:attributeGroup ref="CurrencyAmountGroup">
							<xs:annotation>
								<xs:documentation xml:lang="en">Specifies the amount of money for which the the requester is seeking authorization.</xs:documentation>
							</xs:annotation>
						</xs:attributeGroup>
						<xs:attribute name="SourceType" use="optional">
							<xs:annotation>
								<xs:documentation xml:lang="en">Information describing the point of sale.</xs:documentation>
							</xs:annotation>
							<xs:simpleType>
								<xs:restriction base="xs:NMTOKEN">
									<xs:enumeration value="NormalTransaction">
										<xs:annotation>
											<xs:documentation xml:lang="en">Agent representation (for example, a ticket office).</xs:documentation>
										</xs:annotation>
									</xs:enumeration>
									<xs:enumeration value="MailOrPhoneOrder">
										<xs:annotation>
											<xs:documentation xml:lang="en">The source of the purchase request is a mail or phone order.</xs:documentation>
										</xs:annotation>
									</xs:enumeration>
									<xs:enumeration value="UnattendedTerminal">
										<xs:annotation>
											<xs:documentation xml:lang="en">The source of the purchase request is an unattended terminal such as a kiosk or ATM.</xs:documentation>
										</xs:annotation>
									</xs:enumeration>
									<xs:enumeration value="MerchantIsSuspicious">
										<xs:annotation>
											<xs:documentation xml:lang="en">Indicates that the merchant requesting the credit seems to be suspicious or fraudulent.</xs:documentation>
										</xs:annotation>
									</xs:enumeration>
									<xs:enumeration value="eCommerceSecuredTransaction">
										<xs:annotation>
											<xs:documentation xml:lang="en">Indicates an e-comerce transaction provided over a secure channel. For example, SSL (secure sockets layer).</xs:documentation>
										</xs:annotation>
									</xs:enumeration>
									<xs:enumeration value="eCommerceUnsecuredTransaction">
										<xs:annotation>
											<xs:documentation xml:lang="en">Indicates an e-comerce transaction provided over an unsecured channel. For example HTTP.</xs:documentation>
										</xs:annotation>
									</xs:enumeration>
									<xs:enumeration value="InFlightAirPhone">
										<xs:annotation>
											<xs:documentation xml:lang="en">The purchase request is made from an in flight air phone.</xs:documentation>
										</xs:annotation>
									</xs:enumeration>
									<xs:enumeration value="CID_NotLegible">
										<xs:annotation>
											<xs:documentation xml:lang="en">The customer identification number cannot be read.</xs:documentation>
										</xs:annotation>
									</xs:enumeration>
									<xs:enumeration value="CID_NotOnCard">
										<xs:annotation>
											<xs:documentation xml:lang="en">There is no customer identification number on the card.</xs:documentation>
										</xs:annotation>
									</xs:enumeration>
								</xs:restriction>
							</xs:simpleType>
						</xs:attribute>
						<xs:attribute name="ExtendedPaymentInd" type="xs:boolean" use="optional">
							<xs:annotation>
								<xs:documentation xml:lang="en">If true, the requester would like to apply extended payment conditions to this authorization.</xs:documentation>
							</xs:annotation>
						</xs:attribute>
						<xs:attribute name="ExtendedPaymentQuantity" type="Numeric1to999" use="optional">
							<xs:annotation>
								<xs:documentation xml:lang="en">The number of equal amount, partial payments for the extended payment.</xs:documentation>
							</xs:annotation>
						</xs:attribute>
						<xs:attribute name="ExtendedPaymentFrequency" type="TimeUnitType" use="optional">
							<xs:annotation>
								<xs:documentation xml:lang="en">The frequency of extended payment installments.</xs:documentation>
							</xs:annotation>
						</xs:attribute>
						<xs:attribute name="AuthorizationCode" use="optional">
							<xs:annotation>
								<xs:documentation xml:lang="en">This is the approval code received on the original authorization request. Only used in the case where the requested transaction is to reverse the authorization. </xs:documentation>
							</xs:annotation>
							<xs:simpleType>
								<xs:restriction base="xs:string">
									<xs:pattern value="[A-Za-z0-9]{1,12}"/>
								</xs:restriction>
							</xs:simpleType>
						</xs:attribute>
						<xs:attribute name="ReversalIndicator" type="xs:boolean" use="optional">
							<xs:annotation>
								<xs:documentation xml:lang="en">If true, indicates a request to reverse a credit authorization.</xs:documentation>
							</xs:annotation>
						</xs:attribute>
						<xs:attribute name="CardPresentInd" type="xs:boolean" use="optional">
							<xs:annotation>
								<xs:documentation xml:lang="en">When true, the customer has actually presented the credit card.</xs:documentation>
							</xs:annotation>
						</xs:attribute>
						<xs:attribute name="E_CommerceCode" type="AlphaNumericStringLength1to8" use="optional">
							<xs:annotation>
								<xs:documentation xml:lang="en">The electronic commerce indicator required for some credit card authorizations, such as the Verified by Visa Process.</xs:documentation>
							</xs:annotation>
						</xs:attribute>
						<xs:attribute name="AuthTransactionID" type="StringLength1to32" use="optional">
							<xs:annotation>
								<xs:documentation xml:lang="en">The authentication transaction identifier required for some credit card authorizations, such as the Verified by Visa Process.</xs:documentation>
							</xs:annotation>
						</xs:attribute>
						<xs:attribute name="AuthVerificationValue" type="StringLength1to32" use="optional">
							<xs:annotation>
								<xs:documentation xml:lang="en">The cardholder authentication verfication value required for some credit card authorization, such as the Verified by Visa Process.</xs:documentation>
							</xs:annotation>
						</xs:attribute>
					</xs:complexType>
				</xs:element>
				<xs:element name="AccountAuthorization" minOccurs="0">
					<xs:annotation>
						<xs:documentation>Specifies account information about the customer seeking authorization.</xs:documentation>
					</xs:annotation>
					<xs:complexType>
						<xs:sequence>
							<xs:element name="AccountInfo" minOccurs="0">
								<xs:annotation>
									<xs:documentation>Information about the account.</xs:documentation>
								</xs:annotation>
								<xs:complexType>
									<xs:attribute name="AccountName" type="StringLength1to64" use="optional">
										<xs:annotation>
											<xs:documentation xml:lang="en">The name on the account.</xs:documentation>
										</xs:annotation>
									</xs:attribute>
									<xs:attribute name="CompanyName" type="StringLength1to64" use="optional">
										<xs:annotation>
											<xs:documentation xml:lang="en">Describes the name of the company to which the account is attached.</xs:documentation>
										</xs:annotation>
									</xs:attribute>
									<xs:attribute name="AccountID" type="StringLength1to64" use="optional">
										<xs:annotation>
											<xs:documentation xml:lang="en">Identifier for the account assigned by the merchant.</xs:documentation>
										</xs:annotation>
									</xs:attribute>
									<xs:attribute name="Password" type="StringLength1to64" use="optional">
										<xs:annotation>
											<xs:documentation xml:lang="en">Password attached to the account.</xs:documentation>
										</xs:annotation>
									</xs:attribute>
									<xs:attribute name="Code" type="OTA_CodeType" use="optional">
										<xs:annotation>
											<xs:documentation xml:lang="en">Describes the type of the account. Refer to OpenTravel Code List Payment Type (PMT).</xs:documentation>
										</xs:annotation>
									</xs:attribute>
								</xs:complexType>
							</xs:element>
						</xs:sequence>
						<xs:attributeGroup ref="CurrencyAmountGroup"/>
						<xs:attribute name="NonISO_CurrencyCode" type="StringLength1to8" use="optional">
							<xs:annotation>
								<xs:documentation xml:lang="en">Specifying the currency used in the authorization, when it isn't a ISO 4217, three alpha code (e.g., points, miles).</xs:documentation>
							</xs:annotation>
						</xs:attribute>
					</xs:complexType>
				</xs:element>
			</xs:choice>
			<xs:element name="DriversLicenseAuthorization" type="DocumentType" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Information used for validating a drivers license or for supporting a check or credit card authorization request.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="BookingReferenceID" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The booking or confirmation number for which charges are being authorized.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:complexContent>
						<xs:extension base="UniqueID_Type">
							<xs:attribute name="IgnoreReservationInd" type="xs:boolean" use="optional">
								<xs:annotation>
									<xs:documentation xml:lang="en">When true, the PNR should not be updated with the authorization information returned.</xs:documentation>
								</xs:annotation>
							</xs:attribute>
						</xs:extension>
					</xs:complexContent>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="PrincipalCompanyCode" type="StringLength1to16" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The code associated with the company (e.g., two to three character airline designator) submitting a request to an authorization vendor system.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="RefNumber" type="StringLength1to8" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">A unique identifier for an authorization request. It may be used to link a response to a corresponding request item.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:complexType>
	<xs:complexType name="BookFlightSegmentType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Container for the flight segment data plus the MarriageGrp.</xs:documentation>
			<xs:documentation xml:lang="en"> Construct for holding the booked flight segment information.</xs:documentation>
		</xs:annotation>
		<xs:complexContent>
			<xs:extension base="FlightSegmentType">
				<xs:sequence>
					<xs:element name="MarriageGrp" type="StringLength1to16" minOccurs="0">
						<xs:annotation>
							<xs:documentation xml:lang="en">Many airlines link connection flights together by terming them married segments.  When two or more segments are married, they must be processed as one unit. The segments must be moved, cancelled, and/or priced together. The value of the marriage group must be the same for all segments.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="BookingClassAvails" minOccurs="0" maxOccurs="3">
						<xs:annotation>
							<xs:documentation xml:lang="en">A collection of availability counts per booking class.</xs:documentation>
						</xs:annotation>
						<xs:complexType>
							<xs:sequence>
								<xs:element name="BookingClassAvail" maxOccurs="26">
									<xs:annotation>
										<xs:documentation xml:lang="en">Booking codes available to be sold for a particular flight segment.</xs:documentation>
									</xs:annotation>
									<xs:complexType>
										<xs:attributeGroup ref="BookingClassAvailabilityGroup"/>
										<xs:attribute name="RPH" type="RPH_Type" use="optional">
											<xs:annotation>
												<xs:documentation xml:lang="en">RPH refers back to Marketing Cabin Type.</xs:documentation>
											</xs:annotation>
										</xs:attribute>
									</xs:complexType>
								</xs:element>
							</xs:sequence>
							<xs:attribute name="CabinType" type="CabinType" use="optional">
								<xs:annotation>
									<xs:documentation xml:lang="en">Marketing name as defined by an airline for the first, business or economy cabin.</xs:documentation>
								</xs:annotation>
							</xs:attribute>
						</xs:complexType>
					</xs:element>
					<xs:element name="Comment" type="FreeTextType" minOccurs="0" maxOccurs="999">
						<xs:annotation>
							<xs:documentation xml:lang="en">Free text information that the marketing carrier may send about this flight.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="StopLocation" minOccurs="0" maxOccurs="9">
						<xs:annotation>
							<xs:documentation xml:lang="en">A location where the flight is scheduled to stop en route to its destination for this flight segment.</xs:documentation>
						</xs:annotation>
						<xs:complexType>
							<xs:attributeGroup ref="LocationGroup">
								<xs:annotation>
									<xs:documentation xml:lang="en">A location where the flight is scheduled to stop en route to its destination for this flight segment.</xs:documentation>
								</xs:annotation>
							</xs:attributeGroup>
						</xs:complexType>
					</xs:element>
				</xs:sequence>
				<xs:attribute name="ResBookDesigCode" type="UpperCaseAlphaLength1to2" use="optional">
					<xs:annotation>
						<xs:documentation xml:lang="en">Specific Booking Class for this segment.</xs:documentation>
					</xs:annotation>
				</xs:attribute>
				<xs:attribute name="NumberInParty" type="xs:positiveInteger" use="optional">
					<xs:annotation>
						<xs:documentation xml:lang="en"> Number of travelers associated with this segment.</xs:documentation>
					</xs:annotation>
				</xs:attribute>
				<xs:attribute name="Status" type="OTA_CodeType" use="optional">
					<xs:annotation>
						<xs:documentation xml:lang="en">Code providing status information for this segment. Refer to OpenTravel Code List Status (STS).</xs:documentation>
					</xs:annotation>
				</xs:attribute>
				<xs:attribute name="E_TicketEligibility" use="optional">
					<xs:annotation>
						<xs:documentation xml:lang="en">Specifies whether a flight segment is eligible for electronic ticketing.</xs:documentation>
					</xs:annotation>
					<xs:simpleType>
						<xs:restriction base="StringLength1to16">
							<xs:enumeration value="Eligible">
								<xs:annotation>
									<xs:documentation xml:lang="en">Analogous to the IATA PADIS code: electronic ticket candidate.</xs:documentation>
								</xs:annotation>
							</xs:enumeration>
							<xs:enumeration value="Not Eligible">
								<xs:annotation>
									<xs:documentation xml:lang="en">Analogous to the IATA PADIS code: not an electronic ticket candidate.</xs:documentation>
								</xs:annotation>
							</xs:enumeration>
							<xs:enumeration value="Required">
								<xs:annotation>
									<xs:documentation xml:lang="en">Analogous to the IATA PADIS code: electronic ticketing required.</xs:documentation>
								</xs:annotation>
							</xs:enumeration>
						</xs:restriction>
					</xs:simpleType>
				</xs:attribute>
				<xs:attribute name="MealCode" type="MealServiceType" use="optional">
					<xs:annotation>
						<xs:documentation xml:lang="en">The applicable meal service code for this flight.</xs:documentation>
					</xs:annotation>
				</xs:attribute>
				<xs:attribute name="DepartureDay" type="DayOfWeekType" use="optional"/>
				<xs:attribute name="StopoverInd" type="xs:boolean" use="optional">
					<xs:annotation>
						<xs:documentation xml:lang="en">When true a stopover is permitted (airline display "O").  When false a stopover is not permitted (airline display "X").</xs:documentation>
					</xs:annotation>
				</xs:attribute>
				<xs:attribute name="LineNumber" type="Numeric1to999" use="optional">
					<xs:annotation>
						<xs:documentation xml:lang="en">A number that corresponds to a line in a previous response of flight segments.</xs:documentation>
					</xs:annotation>
				</xs:attribute>
				<xs:attribute name="ConnectionType" type="StringLength0to8" use="optional">
					<xs:annotation>
						<xs:documentation xml:lang="en">Used to provide the type of connection for this flight segment.</xs:documentation>
					</xs:annotation>
				</xs:attribute>
				<xs:attribute name="ParticipationLevelCode" type="OTA_CodeType" use="optional">
					<xs:annotation>
						<xs:documentation xml:lang="en">To specify the level of participation by a vendor in another system. Refer to OpenTravel Code List Participation Level Code (PLC).</xs:documentation>
					</xs:annotation>
				</xs:attribute>
				<xs:attribute name="Distance" type="xs:nonNegativeInteger" use="optional">
					<xs:annotation>
						<xs:documentation xml:lang="en">Miles acquired per flight segment, usually used for earning of frequent flyer miles.</xs:documentation>
					</xs:annotation>
				</xs:attribute>
				<xs:attribute name="DateChangeNbr" type="xs:string" use="optional">
					<xs:annotation>
						<xs:documentation xml:lang="en">The number of days by which the flight's arrival date differs from its departure date (e.g., +1, -1).</xs:documentation>
					</xs:annotation>
				</xs:attribute>
				<xs:attribute name="ValidConnectionInd" type="xs:boolean" use="optional">
					<xs:annotation>
						<xs:documentation xml:lang="en">When true, this flight segment constitutes a valid connection.</xs:documentation>
					</xs:annotation>
				</xs:attribute>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:complexType name="BookingPriceInfoType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Container for all the fare information.</xs:documentation>
			<xs:documentation xml:lang="en"> Pricing information for an itinerary.</xs:documentation>
		</xs:annotation>
		<xs:complexContent>
			<xs:extension base="AirItineraryPricingInfoType">
				<xs:attribute name="RepriceRequired" type="xs:boolean">
					<xs:annotation>
						<xs:documentation xml:lang="en">If true re-pricing of the itinerary is required. </xs:documentation>
						<xs:documentation xml:lang="en">
							<LegacyDefaultValue>false</LegacyDefaultValue>
						</xs:documentation>
					</xs:annotation>
				</xs:attribute>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:complexType name="CabinAvailabilityType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Construct for holding cabin class information, such as seat availability or meal codes. Can be up to three of these per flight segment or air leg - First, Business and Economy.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="Meal" minOccurs="0" maxOccurs="5">
				<xs:annotation>
					<xs:documentation xml:lang="en">Identifies any meal and/or beverage services that are provided.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:attribute name="MealCode" type="MealServiceType" use="required">
						<xs:annotation>
							<xs:documentation xml:lang="en">Used to designate a meal or beverage service.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
				</xs:complexType>
			</xs:element>
			<xs:element name="BaggageAllowance" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Provides the maximum weight of checked-in bags per passenger in this cabin.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:attributeGroup ref="UnitsOfMeasureGroup"/>
				</xs:complexType>
			</xs:element>
			<xs:element name="Entertainment" minOccurs="0" maxOccurs="99">
				<xs:annotation>
					<xs:documentation xml:lang="en">Identifies any entertainment services that are provided on a per cabin basis.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:attribute name="Code" type="OTA_CodeType" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">A code used to designate an entertainment service. Refer to OpenTravel Code list Flight Service Code (FSC).</xs:documentation>
						</xs:annotation>
					</xs:attribute>
				</xs:complexType>
			</xs:element>
			<xs:element name="FlightLoadInfo" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Information used by an airline employee to see the flight load and make decisions regarding non-revenue travel. This information may be used in the reservation process to determine if the flight is oversold or any additional reservations could be made on this flight segment in a specific cabin.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:attribute name="AuthorizedSeatQty" type="xs:positiveInteger" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">The quantity of seats authorized for sale for a cabin and flight leg.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="NRSA_StandbyPaxQty" type="xs:positiveInteger" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">The listed quantity of standby passengers (non-revenue PAX) for a cabin and flight leg.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="RevenuePaxQty" type="xs:positiveInteger" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">The quantity of booked revenue passengers (revenue PAX) for each cabin and flight leg.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="CabinType" type="CabinType" use="required">
			<xs:annotation>
				<xs:documentation xml:lang="en">A section of an aircraft identified by the service level (e.g., First, Business, Economy.)</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="CabinCapacity" type="xs:positiveInteger" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The total quantity of  physical seats in the cabin.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:complexType>
	<xs:complexType name="CabinClassType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Describes the Cabin details in a seat map</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="AirRows" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Collection of Air Rows</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="AirRow" type="RowDetailsType" maxOccurs="999">
							<xs:annotation>
								<xs:documentation xml:lang="en">Row in a Cabin class of a flight</xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="CabinType" type="CabinType" use="required">
			<xs:annotation>
				<xs:documentation xml:lang="en">Cabin class for which the seat map details is being given. Could be first, business or economy.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="Name" type="StringLength1to32" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Name that a particular airline/ CRS may give to the  cabin class</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:complexType>
	<xs:complexType name="EMD_Type">
		<xs:annotation>
			<xs:documentation xml:lang="en">A container for electronic miscellaneous document information</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="TravelerRefNumber" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Direct reference of traveler assigned by requesting system.  Used as a cross reference between data segments.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:attributeGroup ref="TravelerRefNumberGroup"/>
				</xs:complexType>
			</xs:element>
			<xs:element name="AgentID" type="UniqueID_Type" minOccurs="0" maxOccurs="4">
				<xs:annotation>
					<xs:documentation>The agent numeric code, booking agent id, issuing agent id and/or servicing airline/system provider ID.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="PaymentDetail" type="PaymentDetailType" minOccurs="0" maxOccurs="9">
				<xs:annotation>
					<xs:documentation>The payment information.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="OriginDestination" minOccurs="0">
				<xs:annotation>
					<xs:documentation>The true origin and destination.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:attributeGroup ref="OriginDestinationGroup"/>
				</xs:complexType>
			</xs:element>
			<xs:element name="CustLoyalty" minOccurs="0" maxOccurs="5">
				<xs:annotation>
					<xs:documentation>Frequent flyer reference.  ProgramID=airline code, MembershipID=FF number.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:attributeGroup ref="CustomerLoyaltyGroup"/>
				</xs:complexType>
			</xs:element>
			<xs:element name="Endorsement" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Endorsement/ restriction remarks.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:attribute name="Info" type="StringLength1to255" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">Remarks provided to ensure common understanding between the passenger and an airline when a fare is restricted.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
				</xs:complexType>
			</xs:element>
			<xs:element name="AddReferenceID" type="UniqueID_Type" minOccurs="0" maxOccurs="5">
				<xs:annotation>
					<xs:documentation>ID contains the actual identifier (e.g. invoice, airline confirmation number, customer id or airline indexing field.)</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="BaseFare" minOccurs="0" maxOccurs="3">
				<xs:annotation>
					<xs:documentation>Used to specify the base fare, the base fare net and the base fare sell amounts.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:attributeGroup ref="CurrencyAmountGroup">
						<xs:annotation>
							<xs:documentation xml:lang="en">Used to specify the base, base net, or base sell fare.</xs:documentation>
						</xs:annotation>
					</xs:attributeGroup>
					<xs:attribute name="Purpose" type="PurposeType" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">Used to specify if just base or base fare net or base fare sell.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="FareAmountType" type="FareAmountType" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">Used to specify if ticket fare is bulk ticket, inclusive tour, or no additional charge.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
				</xs:complexType>
			</xs:element>
			<xs:element name="EquivFare" minOccurs="0" maxOccurs="3">
				<xs:annotation>
					<xs:documentation>Used to specify the equivalent - equivalent net or equivalent sell paid fare.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:attributeGroup ref="CurrencyAmountGroup">
						<xs:annotation>
							<xs:documentation xml:lang="en">The equivalent fare paid, equivalent fare paid net amount, or the equivalent fare paid sell amount.</xs:documentation>
						</xs:annotation>
					</xs:attributeGroup>
					<xs:attribute name="Purpose" type="PurposeType" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">Used to specify if just equivalent fare paid or paid net or paid sell.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="FareAmountType" type="FareAmountType" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">Used to specify if ticket fare is bulk ticket, inclusive tour, or no additional charge.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="BankExchangeRate" type="Money" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">Banker's buying rate used to convert the base amount into the equivalent amount paid.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
				</xs:complexType>
			</xs:element>
			<xs:element name="TotalFare" minOccurs="0" maxOccurs="3">
				<xs:annotation>
					<xs:documentation>Used to specify the total, total net, total sell fare or refund amount.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:attributeGroup ref="CurrencyAmountGroup">
						<xs:annotation>
							<xs:documentation xml:lang="en">The total, total net, or total sell fare.</xs:documentation>
						</xs:annotation>
					</xs:attributeGroup>
					<xs:attribute name="Purpose" type="PurposeType" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">Used to specify if total amount, total net or total sell.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="FareAmountType" type="FareAmountType" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">Used to specify if ticket fare is bulk ticket, inclusive tour, or no additional charge.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
				</xs:complexType>
			</xs:element>
			<xs:element name="Taxes" minOccurs="0">
				<xs:annotation>
					<xs:documentation>A collection of taxes.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="Tax" maxOccurs="99">
							<xs:annotation>
								<xs:documentation>The tax applicable to the ticket/document.</xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:complexContent>
									<xs:extension base="AirTaxType"/>
								</xs:complexContent>
							</xs:complexType>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="UnstructuredFareCalc" minOccurs="0" maxOccurs="3">
				<xs:annotation>
					<xs:documentation xml:lang="en">Contains the fare calculation information for the stored passenger fare.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:simpleContent>
						<xs:extension base="xs:string">
							<xs:attribute name="FareCalcMode" type="AlphaNumericStringLength1" use="optional">
								<xs:annotation>
									<xs:documentation xml:lang="en">Used to identify the method of pricing for this transaction (e.g., manual,  automated pricing). Should be 1 character in length.</xs:documentation>
								</xs:annotation>
							</xs:attribute>
							<xs:attribute name="Operation" type="ActionType" use="optional">
								<xs:annotation>
									<xs:documentation xml:lang="en">Indicates the required modification to the element.</xs:documentation>
								</xs:annotation>
							</xs:attribute>
							<xs:attribute name="Type" type="PurposeType" use="optional">
								<xs:annotation>
									<xs:documentation xml:lang="en">Used to specify net or sell amount.</xs:documentation>
								</xs:annotation>
							</xs:attribute>
							<xs:attribute name="ReportingCode" type="AlphaNumericStringLength1" use="optional">
								<xs:annotation>
									<xs:documentation xml:lang="en">A secondary identifier reflecting the method of pricing for this transaction used in the reporting process.</xs:documentation>
								</xs:annotation>
							</xs:attribute>
							<xs:attribute name="Info" type="xs:string" use="required">
								<xs:annotation>
									<xs:documentation xml:lang="en">The fare calculation area.</xs:documentation>
								</xs:annotation>
							</xs:attribute>
						</xs:extension>
					</xs:simpleContent>
				</xs:complexType>
			</xs:element>
			<xs:element name="FareInfo" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Nonendorsable and nonrefundable indicators, penalty restriction indicator, pricing system, tour code, ISO country code, around the world fare indicator and non-reissuable/non-exchangeable indicator.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:complexContent>
						<xs:extension base="ET_FareInfo">
							<xs:sequence>
								<xs:element name="PenaltyAmount" type="VoluntaryChangesType" minOccurs="0" maxOccurs="3">
									<xs:annotation>
										<xs:documentation>Non-refundable, no value, penalty amounts.</xs:documentation>
									</xs:annotation>
								</xs:element>
							</xs:sequence>
							<xs:attribute name="NonEndorsableInd" type="xs:boolean" use="optional">
								<xs:annotation>
									<xs:documentation xml:lang="en">When true, the fare is nonendorsable.</xs:documentation>
								</xs:annotation>
							</xs:attribute>
							<xs:attribute name="NonRefundableInd" type="xs:boolean" use="optional">
								<xs:annotation>
									<xs:documentation xml:lang="en">When true, the fare is non refundable.</xs:documentation>
								</xs:annotation>
							</xs:attribute>
							<xs:attribute name="PenaltyRestrictionInd" type="xs:boolean" use="optional">
								<xs:annotation>
									<xs:documentation xml:lang="en">When true, a penalty restriction applies.</xs:documentation>
								</xs:annotation>
							</xs:attribute>
							<xs:attributeGroup ref="CompanyID_AttributesGroup">
								<xs:annotation>
									<xs:documentation xml:lang="en">A two/three character, IATA assigned, code identifying the system used to price the itinerary for the ticket being issued.</xs:documentation>
								</xs:annotation>
							</xs:attributeGroup>
							<xs:attribute name="PresentCreditCardInd" type="xs:boolean" use="optional">
								<xs:annotation>
									<xs:documentation xml:lang="en">When true, the credit/debit card used for purchase must be presented.</xs:documentation>
								</xs:annotation>
							</xs:attribute>
							<xs:attribute name="AroundTheWorldFareInd" type="xs:boolean" use="optional">
								<xs:annotation>
									<xs:documentation xml:lang="en">When true, this fare is classifed as a published/promotional 'Around the World' fare.</xs:documentation>
								</xs:annotation>
							</xs:attribute>
							<xs:attribute name="NonInterlineableInd" type="xs:boolean" use="optional">
								<xs:annotation>
									<xs:documentation xml:lang="en">When true, the EMD is non-interlineable.</xs:documentation>
								</xs:annotation>
							</xs:attribute>
							<xs:attribute name="NonCommissionableInd" type="xs:boolean" use="optional">
								<xs:annotation>
									<xs:documentation xml:lang="en">When true, the EMD is non-commissionable.</xs:documentation>
								</xs:annotation>
							</xs:attribute>
							<xs:attribute name="NonReissuableNonExchgInd" type="xs:boolean" use="optional">
								<xs:annotation>
									<xs:documentation xml:lang="en">When true, the EMD is non reissuable and/or non exchangeable.</xs:documentation>
								</xs:annotation>
							</xs:attribute>
						</xs:extension>
					</xs:complexContent>
				</xs:complexType>
			</xs:element>
			<xs:element name="TicketDocument" maxOccurs="99">
				<xs:annotation>
					<xs:documentation>The EMD type, in connection number, fee owner and date of issue.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="CouponInfo" maxOccurs="4">
							<xs:annotation>
								<xs:documentation>Number, status, in connection nbr, itinerary sequence nbr, coupon ref, not valid before/after, involuntary indicator, and in connection qualfier.</xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:sequence>
									<xs:element name="SoldFlightSegmentRPH" type="RPH_Type" minOccurs="0">
										<xs:annotation>
											<xs:documentation xml:lang="en">Reference to the flight segments for this coupon.</xs:documentation>
										</xs:annotation>
									</xs:element>
									<xs:choice>
										<xs:element name="CheckedInAirlineRPH" type="RPH_Type" minOccurs="0">
											<xs:annotation>
												<xs:documentation>The checked-in/lifted airline information.</xs:documentation>
											</xs:annotation>
										</xs:element>
										<xs:element name="FlownAirlineSegmentRPH" type="RPH_Type" minOccurs="0">
											<xs:annotation>
												<xs:documentation xml:lang="en">Reference to the flight segments for this coupon</xs:documentation>
											</xs:annotation>
										</xs:element>
									</xs:choice>
									<xs:element name="ExcessBaggage" minOccurs="0">
										<xs:annotation>
											<xs:documentation>Excess baggage information.</xs:documentation>
										</xs:annotation>
										<xs:complexType>
											<xs:attributeGroup ref="CurrencyAmountGroup">
												<xs:annotation>
													<xs:documentation xml:lang="en">The currency code and rate per unit.</xs:documentation>
												</xs:annotation>
											</xs:attributeGroup>
											<xs:attributeGroup ref="UnitsOfMeasureGroup">
												<xs:annotation>
													<xs:documentation xml:lang="en">The over allowance qualifier and total number of excess.</xs:documentation>
												</xs:annotation>
											</xs:attributeGroup>
										</xs:complexType>
									</xs:element>
									<xs:element name="PresentInfo" minOccurs="0">
										<xs:annotation>
											<xs:documentation>The present to and at information.</xs:documentation>
										</xs:annotation>
										<xs:complexType>
											<xs:attribute name="To" type="StringLength1to128" use="optional">
												<xs:annotation>
													<xs:documentation xml:lang="en">Name of service provider.</xs:documentation>
												</xs:annotation>
											</xs:attribute>
											<xs:attribute name="At" type="StringLength1to128" use="optional">
												<xs:annotation>
													<xs:documentation xml:lang="en">The location of the service provider.</xs:documentation>
												</xs:annotation>
											</xs:attribute>
										</xs:complexType>
									</xs:element>
									<xs:element name="ReasonForIssuance" minOccurs="0">
										<xs:annotation>
											<xs:documentation>The reason for issuance information.</xs:documentation>
										</xs:annotation>
										<xs:complexType>
											<xs:attributeGroup ref="ReasonForIssuanceGroup"/>
										</xs:complexType>
									</xs:element>
									<xs:element name="ValidatingAirline" minOccurs="0">
										<xs:annotation>
											<xs:documentation>The validating airline for this EMD coupon.</xs:documentation>
										</xs:annotation>
										<xs:complexType>
											<xs:attributeGroup ref="CompanyID_AttributesGroup"/>
										</xs:complexType>
									</xs:element>
									<xs:element name="FiledFeeInfo" minOccurs="0">
										<xs:annotation>
											<xs:documentation>Filed fee information.</xs:documentation>
										</xs:annotation>
										<xs:complexType>
											<xs:attributeGroup ref="CurrencyAmountGroup"/>
											<xs:attribute name="BSR_Rate" type="xs:decimal" use="optional">
												<xs:annotation>
													<xs:documentation xml:lang="en">The bankers selling rate used to convert the filed fee into the currency of payment of the EMD.</xs:documentation>
												</xs:annotation>
											</xs:attribute>
										</xs:complexType>
									</xs:element>
								</xs:sequence>
								<xs:attribute name="Number" type="Numeric1to4" use="optional">
									<xs:annotation>
										<xs:documentation xml:lang="en">The applicable coupon number.</xs:documentation>
									</xs:annotation>
								</xs:attribute>
								<xs:attribute name="InConnectionNbr" type="Numeric1to4" use="optional">
									<xs:annotation>
										<xs:documentation xml:lang="en">The coupon number associated with the 'in connection with' document number.</xs:documentation>
									</xs:annotation>
								</xs:attribute>
								<xs:attribute name="CouponReference" type="StringLength1to8" use="optional">
									<xs:annotation>
										<xs:documentation xml:lang="en">The sequential number (segment identifier) of an individual flight segment, e.g., 1 of 3.</xs:documentation>
									</xs:annotation>
								</xs:attribute>
								<xs:attribute name="FareBasisCode" type="StringLength1to16" use="optional">
									<xs:annotation>
										<xs:documentation xml:lang="en">The applicable fare basis code.</xs:documentation>
									</xs:annotation>
								</xs:attribute>
								<xs:attribute name="Start" type="DateOrTimeOrDateTimeType" use="optional">
									<xs:annotation>
										<xs:documentation xml:lang="en">The starting value of the time span. </xs:documentation>
									</xs:annotation>
								</xs:attribute>
								<xs:attribute name="Duration" type="DurationType" use="optional">
									<xs:annotation>
										<xs:documentation xml:lang="en">The duration datatype represents a combination of year, month, day and time values representing a single duration of time, encoded as a single string. </xs:documentation>
									</xs:annotation>
								</xs:attribute>
								<xs:attribute name="End" type="DateOrTimeOrDateTimeType" use="optional">
									<xs:annotation>
										<xs:documentation xml:lang="en">The ending value of the time span. </xs:documentation>
									</xs:annotation>
								</xs:attribute>
								<xs:attribute name="Status" type="OTA_CodeType" use="optional">
									<xs:annotation>
										<xs:documentation xml:lang="en">Code providing status information for this coupon. Refer to OpenTravel Code List Status (STS).</xs:documentation>
									</xs:annotation>
								</xs:attribute>
								<xs:attribute name="CouponItinerarySeqNbr" type="Numeric0to99" use="optional">
									<xs:annotation>
										<xs:documentation xml:lang="en">The number representing the order in which this coupon was used or is to be used.</xs:documentation>
									</xs:annotation>
								</xs:attribute>
								<xs:attributeGroup ref="UnitsOfMeasureGroup">
									<xs:annotation>
										<xs:documentation xml:lang="en">The applicable free baggage allowance.  UnitOfMeasureQuanity will contain either the weight or the number of pieces.</xs:documentation>
									</xs:annotation>
								</xs:attributeGroup>
								<xs:attribute name="InvoluntaryIndCode" use="optional">
									<xs:annotation>
										<xs:documentation xml:lang="en">Used to indicate when a tranaction ocurred due to an involuntary change.</xs:documentation>
									</xs:annotation>
									<xs:simpleType>
										<xs:restriction base="xs:NMTOKEN">
											<xs:enumeration value="I">
												<xs:annotation>
													<xs:documentation xml:lang="en">Involuntary - no reason given.</xs:documentation>
												</xs:annotation>
											</xs:enumeration>
											<xs:enumeration value="L">
												<xs:annotation>
													<xs:documentation xml:lang="en">Labour/strike</xs:documentation>
												</xs:annotation>
											</xs:enumeration>
											<xs:enumeration value="S">
												<xs:annotation>
													<xs:documentation xml:lang="en">Schedule change</xs:documentation>
												</xs:annotation>
											</xs:enumeration>
										</xs:restriction>
									</xs:simpleType>
								</xs:attribute>
								<xs:attribute name="SettlementAuthCode" type="AlphaNumericStringLength1to14" use="optional">
									<xs:annotation>
										<xs:documentation xml:lang="en">A reference number generated by the validating carrier authorising settlment of a coupon.</xs:documentation>
									</xs:annotation>
								</xs:attribute>
								<xs:attribute name="Value" type="Money" use="optional">
									<xs:annotation>
										<xs:documentation xml:lang="en">The value associated to a single coupon of a miscellaneous document.</xs:documentation>
									</xs:annotation>
								</xs:attribute>
								<xs:attribute name="AssociateInd" type="xs:boolean" use="optional">
									<xs:annotation>
										<xs:documentation xml:lang="en">When true, the coupon should be associated.  When false, it should be disassociated.</xs:documentation>
									</xs:annotation>
								</xs:attribute>
								<xs:attribute name="PromotionalCode" type="AlphaNumericStringLength1to14" use="optional">
									<xs:annotation>
										<xs:documentation xml:lang="en">Used to specify a promotional code that applies to the fee.</xs:documentation>
									</xs:annotation>
								</xs:attribute>
								<xs:attribute name="Remark" type="StringLength1to128" use="optional">
									<xs:annotation>
										<xs:documentation xml:lang="en">Free text describing the service on the EMD.</xs:documentation>
									</xs:annotation>
								</xs:attribute>
								<xs:attribute name="TaxOnEMD_Ind" type="xs:boolean" use="optional">
									<xs:annotation>
										<xs:documentation xml:lang="en">When true, a tax has been collected and is due to a local government from the validating carrier.</xs:documentation>
									</xs:annotation>
								</xs:attribute>
								<xs:attribute name="AssocFareBasisCode" type="StringLength1to16" use="optional">
									<xs:annotation>
										<xs:documentation xml:lang="en">The applicable code as provided for in IATA Resolution 728 for the associated EMD.</xs:documentation>
									</xs:annotation>
								</xs:attribute>
								<xs:attribute name="ConsumedAtIssuanceInd" type="xs:boolean" use="optional">
									<xs:annotation>
										<xs:documentation xml:lang="en">When true, the coupon is consumed at issuance.</xs:documentation>
									</xs:annotation>
								</xs:attribute>
								<xs:attribute name="DateOfService" type="DateOrDateTimeType" use="optional">
									<xs:annotation>
										<xs:documentation xml:lang="en">The scheduled date of service for this coupon of an EMD.</xs:documentation>
									</xs:annotation>
								</xs:attribute>
							</xs:complexType>
						</xs:element>
					</xs:sequence>
					<xs:attribute name="TicketDocumentNbr" type="StringLength1to16" use="required">
						<xs:annotation>
							<xs:documentation xml:lang="en">Document number comprised of the airline code, form code, and serial number.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="Type" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">Used to indicate a ticket or EMD.</xs:documentation>
						</xs:annotation>
						<xs:simpleType>
							<xs:restriction base="xs:NMTOKEN">
								<xs:enumeration value="Ticket"/>
								<xs:enumeration value="EMD"/>
							</xs:restriction>
						</xs:simpleType>
					</xs:attribute>
					<xs:attribute name="PrimaryDocInd" type="xs:boolean" use="optional">
						<xs:annotation>
							<xs:documentation>When true, this is the primary ticket/document number. </xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="InConnectionDocNbr" type="StringLength1to16" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">The ticket/document number of another ticket to which this ticket is connected.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="DateOfIssue" type="xs:date" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">The date a document was issued.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="ExchangeTktNbrInd" type="xs:boolean" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">If true, this is the new ticket number for a ticket that is being exchanged.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attributeGroup ref="CompanyID_AttributesGroup">
						<xs:annotation>
							<xs:documentation xml:lang="en">The two/three character designator representing the carrier whose fee was used on the EMD coupons.</xs:documentation>
						</xs:annotation>
					</xs:attributeGroup>
				</xs:complexType>
			</xs:element>
			<xs:element name="Commission" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Commission information for this ticket/document.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:attribute name="Type" type="OTA_CodeType" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">A code to identify the type of commission.  Refer to OpenTravel Code List "EMD Commission Information".</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attributeGroup ref="CurrencyAmountGroup"/>
					<xs:attribute name="Percent" type="Percentage" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">The decimal percentage claimed by the agent as eligible commission.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
				</xs:complexType>
			</xs:element>
			<xs:element name="FareComponent" type="FareComponentType" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Price quote date, account code, input designator, component count and bank exchange rate.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="CarrierFeeInfo" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Data for the collection of non-airport/ government fees.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="PaymentDetail" type="PaymentDetailType" minOccurs="0">
							<xs:annotation>
								<xs:documentation>Form of payment information.</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="CarrierFee" minOccurs="0" maxOccurs="9">
							<xs:annotation>
								<xs:documentation>Used to specify if the carrier fee is related to the booking, ticketing, or service and associated information.</xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:sequence>
									<xs:element name="FeeAmount" maxOccurs="9">
										<xs:annotation>
											<xs:documentation>The type of fee being imposed by a carrier, the amount and other related information.</xs:documentation>
										</xs:annotation>
										<xs:complexType>
											<xs:attribute name="Type" type="AlphaNumericStringLength1to3" use="required">
												<xs:annotation>
													<xs:documentation xml:lang="en">1 to 3 alpha numeric carrier fee code.</xs:documentation>
												</xs:annotation>
											</xs:attribute>
											<xs:attribute name="Amount" type="Money" use="required">
												<xs:annotation>
													<xs:documentation xml:lang="en">The carrier fee amount.</xs:documentation>
												</xs:annotation>
											</xs:attribute>
											<xs:attribute name="ApplicationCode" type="ListOfOTA_CodeType" use="required">
												<xs:annotation>
													<xs:documentation xml:lang="en">To specify if not interlineable, not refundable, commissionable or not commissionable.  Refer to OpenTravel Code List "EMD Application Code PADIS 9988".</xs:documentation>
												</xs:annotation>
											</xs:attribute>
											<xs:attributeGroup ref="OriginDestinationGroup">
												<xs:annotation>
													<xs:documentation xml:lang="en">The from and to airport/city code for this carrier fee.</xs:documentation>
												</xs:annotation>
											</xs:attributeGroup>
										</xs:complexType>
									</xs:element>
								</xs:sequence>
								<xs:attribute name="Type" type="OTA_CodeType" use="required">
									<xs:annotation>
										<xs:documentation xml:lang="en">The type of carrier fee, i.e., related to booking or ticketing or service.  Refer to OpenTravel Code List "EMD Carrier Fee Type."</xs:documentation>
									</xs:annotation>
								</xs:attribute>
								<xs:attribute name="Number" type="Numeric0to4" use="optional">
									<xs:annotation>
										<xs:documentation xml:lang="en">A sequential number representing the order of the fare comonents within the fare component calculation.</xs:documentation>
									</xs:annotation>
								</xs:attribute>
								<xs:attribute name="TariffNumber" type="NumericStringLength1to3" use="optional">
									<xs:annotation>
										<xs:documentation xml:lang="en">The tariff number where the fare data is filed.</xs:documentation>
									</xs:annotation>
								</xs:attribute>
								<xs:attribute name="RuleNumber" type="AlphaNumericStringLength1to8" use="optional">
									<xs:annotation>
										<xs:documentation xml:lang="en">The rule number within the tariff where the fare data is filed.</xs:documentation>
									</xs:annotation>
								</xs:attribute>
								<xs:attribute name="RuleCode" type="AlphaNumericStringLength1to3" use="optional">
									<xs:annotation>
										<xs:documentation xml:lang="en">System generated data indicating the ticketed fare is from a specific rule filed fare sent by the issuing system.</xs:documentation>
									</xs:annotation>
								</xs:attribute>
								<xs:attributeGroup ref="CompanyID_AttributesGroup">
									<xs:annotation>
										<xs:documentation xml:lang="en">In the Code attribute is the two/three character designator representing the carrier whose fee is represented by the carrier fee code.  In the CompanyShortName is the Carrier fee commercial name.</xs:documentation>
									</xs:annotation>
								</xs:attributeGroup>
								<xs:attribute name="FareClassCode" type="StringLength1to8" use="optional">
									<xs:annotation>
										<xs:documentation xml:lang="en">The fare class code that must be used when matching alternate cateory data as specifed by the carrier fee fare rule code, carrier fee rule number, and carrier fee tariff number fields.</xs:documentation>
									</xs:annotation>
								</xs:attribute>
								<xs:attribute name="ReportingCode" type="AlphaNumericStringLength1" use="optional">
									<xs:annotation>
										<xs:documentation xml:lang="en">The identifier reflecting the method of pricing for the carrier fee.</xs:documentation>
									</xs:annotation>
								</xs:attribute>
							</xs:complexType>
						</xs:element>
						<xs:element name="Taxes" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">A collection of  taxes applicable to the carrier fee.</xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:sequence>
									<xs:element name="Tax" type="AirTaxType" minOccurs="0" maxOccurs="99">
										<xs:annotation>
											<xs:documentation xml:lang="en">A tax applicable to a carrier fee or to the carrier fee tax.</xs:documentation>
										</xs:annotation>
									</xs:element>
								</xs:sequence>
							</xs:complexType>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="ExchResidualFareComponent" minOccurs="0" maxOccurs="99">
				<xs:annotation>
					<xs:documentation>The exchanged residual fare component information.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:complexContent>
						<xs:extension base="FareComponentType">
							<xs:sequence>
								<xs:element name="Taxes" minOccurs="0">
									<xs:annotation>
										<xs:documentation>The tax, fee, or charge amount information. </xs:documentation>
									</xs:annotation>
									<xs:complexType>
										<xs:sequence>
											<xs:element name="Tax" type="AirTaxType" maxOccurs="99"/>
										</xs:sequence>
									</xs:complexType>
								</xs:element>
								<xs:element name="TotalAmount" minOccurs="0">
									<xs:annotation>
										<xs:documentation>The total residual value, including applicable tax, fees and charges.</xs:documentation>
									</xs:annotation>
									<xs:complexType>
										<xs:attribute name="Purpose" type="PurposeType" use="optional">
											<xs:annotation>
												<xs:documentation xml:lang="en">Not applicable here.</xs:documentation>
											</xs:annotation>
										</xs:attribute>
										<xs:attributeGroup ref="CurrencyAmountGroup"/>
									</xs:complexType>
								</xs:element>
							</xs:sequence>
						</xs:extension>
					</xs:complexContent>
				</xs:complexType>
			</xs:element>
			<xs:element name="OriginalIssueInfo" minOccurs="0">
				<xs:annotation>
					<xs:documentation>The original ticket/document issue information.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:attribute name="Information" use="optional">
						<xs:annotation>
							<xs:documentation>Original ticket issue information.</xs:documentation>
						</xs:annotation>
						<xs:simpleType>
							<xs:restriction base="xs:string">
								<xs:minLength value="0"/>
								<xs:maxLength value="34"/>
							</xs:restriction>
						</xs:simpleType>
					</xs:attribute>
					<xs:attribute name="TicketDocumentNbr" type="StringLength1to16" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">The original ticket/document number.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="IssuingAgentID" type="NumericStringLength1to8" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">ID of the original issuer of the ticket/document.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="DateOfIssue" type="xs:date" use="optional">
						<xs:annotation>
							<xs:documentation>Date the ticket/document was originally issued.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="LocationCode" type="StringLength1to8" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">Location of original issue.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
				</xs:complexType>
			</xs:element>
			<xs:element name="ReissuedFlown" minOccurs="0" maxOccurs="99">
				<xs:annotation>
					<xs:documentation>The reissued flown flight coupon information.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="FlightSegmentRPH" type="RPH_Type" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">Reference to the flight segments for this coupon.</xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
					<xs:attribute name="Number" type="Numeric1to4" use="required">
						<xs:annotation>
							<xs:documentation xml:lang="en">Coupon number.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="CouponItinerarySeqNbr" type="Numeric0to99" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">The number representing the order in which this coupon was used or is to be used.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="FareBasisCode" type="StringLength1to16" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">The applicable fare basis code.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="TicketDocumentNbr" type="StringLength1to16" use="required">
						<xs:annotation>
							<xs:documentation xml:lang="en">Document number comprised of the airline code, form code, and serial number.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="DateOfIssue" type="xs:date" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">The date the ticket was issued.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="WaiverCode" type="AlphaNumericStringLength1to19" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">A validating carrier assigned code allowing a fare rule modification or override applied to a flown coupon from the ticket being reissued.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="TicketDesignatorCode" type="StringLength1to16" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">The applicable code as provided for in ATA SIPP Reso 110.16 or IATA Reso 728, for each flown coupon from the ticket being reissued.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
				</xs:complexType>
			</xs:element>
			<xs:element name="ResponseComment" type="FreeTextType" minOccurs="0">
				<xs:annotation>
					<xs:documentation>A response message from the controlling carrier.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="PresentInfo" minOccurs="0">
				<xs:annotation>
					<xs:documentation>The present to and at information.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:attribute name="To" type="StringLength1to128" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">The name of the service provider.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="At" type="StringLength1to128" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">The location of the service provider.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
				</xs:complexType>
			</xs:element>
			<xs:element name="ReasonForIssuance" minOccurs="0">
				<xs:annotation>
					<xs:documentation>The reason for issuance information.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:attributeGroup ref="ReasonForIssuanceGroup"/>
				</xs:complexType>
			</xs:element>
			<xs:element name="ValidatingAirline" minOccurs="0">
				<xs:annotation>
					<xs:documentation>The validating airline for all EMD's for this passenger.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:attributeGroup ref="CompanyID_AttributesGroup"/>
				</xs:complexType>
			</xs:element>
			<xs:element name="TaxCouponInformation" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Used to specify tax coupon information.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="TicketDocument" maxOccurs="4">
							<xs:annotation>
								<xs:documentation>The ticket/document number to which the tax coupon data applies.</xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:sequence>
									<xs:element name="CouponNumber" maxOccurs="4">
										<xs:annotation>
											<xs:documentation>The applicable coupon number.</xs:documentation>
										</xs:annotation>
										<xs:complexType>
											<xs:sequence>
												<xs:element name="TaxCouponInfo" minOccurs="0">
													<xs:annotation>
														<xs:documentation xml:lang="en">The tax coupon information for cabin and air equipment.</xs:documentation>
													</xs:annotation>
													<xs:complexType>
														<xs:attribute name="Cabin" type="CabinType" use="optional">
															<xs:annotation>
																<xs:documentation xml:lang="en">The sold cabin of passenger travel for the coupon.</xs:documentation>
															</xs:annotation>
														</xs:attribute>
														<xs:attribute name="AirEquipType" type="StringLength3" use="optional">
															<xs:annotation>
																<xs:documentation xml:lang="en">The standard code of the equipment used for the coupon flight.</xs:documentation>
															</xs:annotation>
														</xs:attribute>
													</xs:complexType>
												</xs:element>
												<xs:element name="Tax" type="AirTaxType" minOccurs="0" maxOccurs="99">
													<xs:annotation>
														<xs:documentation>The tax/fee charge for this coupon.</xs:documentation>
													</xs:annotation>
												</xs:element>
												<xs:element name="UnticketedPointInfo" minOccurs="0" maxOccurs="5">
													<xs:annotation>
														<xs:documentation xml:lang="en">Unticketed information, including city airport code and arrival/ departure dates.</xs:documentation>
													</xs:annotation>
													<xs:complexType>
														<xs:attribute name="CityAirportCode" type="StringLength1to8" use="optional">
															<xs:annotation>
																<xs:documentation xml:lang="en">The airport or city code identifying an unticketed point which occurs between the coupon origin and destination.</xs:documentation>
															</xs:annotation>
														</xs:attribute>
														<xs:attribute name="ArrivalDate" type="DateOrDateTimeType" use="optional">
															<xs:annotation>
																<xs:documentation xml:lang="en">The date and time of arrival in GMT at the unticketed point.</xs:documentation>
															</xs:annotation>
														</xs:attribute>
														<xs:attribute name="DepartureDate" type="DateOrDateTimeType" use="optional">
															<xs:annotation>
																<xs:documentation xml:lang="en">The date and time of depature in GMT from the unticketed point.</xs:documentation>
															</xs:annotation>
														</xs:attribute>
														<xs:attribute name="AirEquipType" type="StringLength3" use="optional">
															<xs:annotation>
																<xs:documentation xml:lang="en">The standard code of the quipment used for the coupon flight departing from the unticketed point.</xs:documentation>
															</xs:annotation>
														</xs:attribute>
													</xs:complexType>
												</xs:element>
											</xs:sequence>
											<xs:attribute name="Number" type="Numeric1to4" use="required">
												<xs:annotation>
													<xs:documentation xml:lang="en">The coupon number.</xs:documentation>
												</xs:annotation>
											</xs:attribute>
										</xs:complexType>
									</xs:element>
								</xs:sequence>
								<xs:attribute name="TicketDocumentNbr" type="StringLength1to16" use="required">
									<xs:annotation>
										<xs:documentation xml:lang="en">The ticket/document number for this transaction.</xs:documentation>
									</xs:annotation>
								</xs:attribute>
							</xs:complexType>
						</xs:element>
					</xs:sequence>
					<xs:attribute name="BirthDate" type="xs:date" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">The date of birth of the passenger.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="JourneyTurnaroundCityCode" type="StringLength1to8" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">The airport/city code which has been assumed to be the journey turnaround point in the construction of the itinerary by the pricing system.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="TotalFltSegQty" type="Numeric0to99" use="required">
			<xs:annotation>
				<xs:documentation xml:lang="en">Total number of flight segments.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attributeGroup ref="QuantityGroup"/>
		<xs:attribute name="SpecificData" type="StringLength1to32" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Passenger specific data.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="TaxOnCommissionInd" type="xs:boolean" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">When true, a tax is to be calculated by the BSP Processing Centre.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="TicketingModeCode" type="AlphaNumericStringLength1" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">An indication of the method of ticket generation.  Refer to OpenTravel Code List EMD Ticketing Mode Code-5387 (ETM).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="EMD_Type">
			<xs:annotation>
				<xs:documentation xml:lang="en">Type of EMD. EMD-S (standalone) or EMD-A (associated with flight segment and an eticket.)</xs:documentation>
			</xs:annotation>
			<xs:simpleType>
				<xs:restriction base="xs:NMTOKEN">
					<xs:enumeration value="EMD-S"/>
					<xs:enumeration value="EMD-A"/>
				</xs:restriction>
			</xs:simpleType>
		</xs:attribute>
		<xs:attribute name="QuoteInd" type="xs:boolean">
			<xs:annotation>
				<xs:documentation xml:lang="en">When true, indicates that it is a quote type EMD or if false is an issued EMD.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="Operation" type="ActionType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Indicates the required modification to the element.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="RPH" type="RPH_Type" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Provides a reference to a specific EMD.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:complexType>
	<xs:complexType name="ET_FareInfo">
		<xs:annotation>
			<xs:documentation>Account code, net reporting code, nonendorsable and nonrefundable indicators, penalty restriction indicator, pricing system, statistical code and tour code.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="Waiver" minOccurs="0" maxOccurs="5">
				<xs:annotation>
					<xs:documentation>A code assigned by the validating carrier that allows fare rule modification or override.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:attribute name="Code" type="AlphaNumericStringLength1to19" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">A code assigned by the validating carrier that allows fare rule modification or override.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="Type" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">The waiver type.</xs:documentation>
						</xs:annotation>
						<xs:simpleType>
							<xs:restriction base="xs:NMTOKEN">
								<xs:enumeration value="ExchangedReissued"/>
								<xs:enumeration value="FareComponent"/>
							</xs:restriction>
						</xs:simpleType>
					</xs:attribute>
				</xs:complexType>
			</xs:element>
			<xs:element name="RuleIndicator" minOccurs="0" maxOccurs="5">
				<xs:annotation>
					<xs:documentation>System generated data indicating the ticket fare is an ATPCO Category number fare.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:attribute name="RuleCode" type="NumericStringLength1to3" use="required">
						<xs:annotation>
							<xs:documentation xml:lang="en">The ATPCO category number for the fare.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="NetReportingCode" type="AlphaNumericStringLength1to3" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">A code indicating the transaction is subject to a net reporting arrangement.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="StatisticalCode" type="AlphaNumericStringLength1to3" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Used for domestic or international sales differentiation.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="TourCode" type="StringLength1to16" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The applicable tour code.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="CountryCodeOfIssue" type="ISO3166" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The country code where the ticket is issued.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:complexType>
	<xs:complexType name="FareBasisCodeType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Fare basis code for the price for this PTC</xs:documentation>
		</xs:annotation>
		<xs:simpleContent>
			<xs:extension base="StringLength1to16">
				<xs:attribute name="FlightSegmentRPH" type="RPH_Type" use="optional">
					<xs:annotation>
						<xs:documentation xml:lang="en">A reference to the flight segment associated with this fare basis code.</xs:documentation>
					</xs:annotation>
				</xs:attribute>
				<xs:attribute name="NotValidBefore" type="xs:date" use="optional">
					<xs:annotation>
						<xs:documentation xml:lang="en">The date before which the fare basis code is not valid.</xs:documentation>
					</xs:annotation>
				</xs:attribute>
				<xs:attribute name="NotValidAfter" type="xs:date" use="optional">
					<xs:annotation>
						<xs:documentation xml:lang="en">The date after which the fare basis code is not valid.</xs:documentation>
					</xs:annotation>
				</xs:attribute>
				<xs:attribute name="Operation" type="ActionType" use="optional">
					<xs:annotation>
						<xs:documentation xml:lang="en">Indicates the required modification to the element.</xs:documentation>
					</xs:annotation>
				</xs:attribute>
			</xs:extension>
		</xs:simpleContent>
	</xs:complexType>
	<xs:complexType name="FareComponentType">
		<xs:annotation>
			<xs:documentation>Information representing sold data supporting the fare construction applicable to each fare component in a transaction.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="PriceableUnit" maxOccurs="99">
				<xs:annotation>
					<xs:documentation>Priceable unit number.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="FareComponentDetail" maxOccurs="99">
							<xs:annotation>
								<xs:documentation>Component number, tariff number, rule number, fare owner, waiver code, PTC, rule code, fare basis code, ticket designator. ticket designator qualifier, and agreement code.</xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:sequence>
									<xs:element name="CouponSequence" maxOccurs="99">
										<xs:annotation>
											<xs:documentation>Sequence number, coupon/itinerary sequence nbr, from and to city codes, stopover indicator, and airline designator.</xs:documentation>
										</xs:annotation>
										<xs:complexType>
											<xs:attribute name="SequenceNbr" type="StringLength1to8" use="optional">
												<xs:annotation>
													<xs:documentation xml:lang="en">Represents the sequential order of the coupon(s) that apply within a specific fare component of a ticket being issued, e.g. 1 of 5.</xs:documentation>
												</xs:annotation>
											</xs:attribute>
											<xs:attribute name="CouponItinerarySeqNbr" type="Numeric0to99" use="optional">
												<xs:annotation>
													<xs:documentation xml:lang="en">The number representing the order in which this coupon was is to be used.</xs:documentation>
												</xs:annotation>
											</xs:attribute>
											<xs:attributeGroup ref="OriginDestinationGroup">
												<xs:annotation>
													<xs:documentation xml:lang="en">The from and to airport/city code.</xs:documentation>
												</xs:annotation>
											</xs:attributeGroup>
											<xs:attribute name="StopoverInd" type="xs:boolean" use="optional">
												<xs:annotation>
													<xs:documentation xml:lang="en">When true, a stopover is permitted.</xs:documentation>
												</xs:annotation>
											</xs:attribute>
											<xs:attributeGroup ref="CompanyID_AttributesGroup">
												<xs:annotation>
													<xs:documentation xml:lang="en">The two/three character designator identifying the marketing carrier whose fare is used from a board point to a stopover or connection point.</xs:documentation>
												</xs:annotation>
											</xs:attributeGroup>
											<xs:attribute name="ResBookDesigCode" type="UpperCaseAlphaLength1to2" use="optional">
												<xs:annotation>
													<xs:documentation xml:lang="en">The RBD at the time of pricing.</xs:documentation>
												</xs:annotation>
											</xs:attribute>
										</xs:complexType>
									</xs:element>
									<xs:element name="ConstructionPrinciple" minOccurs="0" maxOccurs="99">
										<xs:annotation>
											<xs:documentation>Principle indicator, from and to airport/city code, amount and percent.</xs:documentation>
										</xs:annotation>
										<xs:complexType>
											<xs:attribute name="Code" type="AlphaNumericStringLength1to3" use="required">
												<xs:annotation>
													<xs:documentation xml:lang="en">A code identifying a rule used to formulate the fare.</xs:documentation>
												</xs:annotation>
											</xs:attribute>
											<xs:attributeGroup ref="OriginDestinationGroup">
												<xs:annotation>
													<xs:documentation xml:lang="en">The from and to airport/city code.</xs:documentation>
												</xs:annotation>
											</xs:attributeGroup>
											<xs:attribute name="Amount" type="Money" use="optional">
												<xs:annotation>
													<xs:documentation xml:lang="en">An amount applicable to the fare rule based on the fare component construction principle indicator.</xs:documentation>
												</xs:annotation>
											</xs:attribute>
											<xs:attribute name="Percent" type="Percentage" use="optional">
												<xs:annotation>
													<xs:documentation xml:lang="en">A percentage applicable to the fare rule based on the fare component construction principle indicator.</xs:documentation>
												</xs:annotation>
											</xs:attribute>
										</xs:complexType>
									</xs:element>
									<xs:element name="BaseAmount" maxOccurs="3">
										<xs:annotation>
											<xs:documentation>The base, base net, and base sell amounts.</xs:documentation>
										</xs:annotation>
										<xs:complexType>
											<xs:attribute name="Purpose" type="PurposeType" use="optional">
												<xs:annotation>
													<xs:documentation xml:lang="en">Used to specify base, base net or base sell amount.</xs:documentation>
												</xs:annotation>
											</xs:attribute>
											<xs:attributeGroup ref="CurrencyAmountGroup">
												<xs:annotation>
													<xs:documentation xml:lang="en">The base, base sell, or base net fare amount and currency code.</xs:documentation>
												</xs:annotation>
											</xs:attributeGroup>
										</xs:complexType>
									</xs:element>
									<xs:element name="TicketDesignator" minOccurs="0" maxOccurs="9">
										<xs:annotation>
											<xs:documentation xml:lang="en">Ticket designation code and qualifier.</xs:documentation>
										</xs:annotation>
										<xs:complexType>
											<xs:attribute name="TicketDesignatorCode" type="StringLength1to16" use="required">
												<xs:annotation>
													<xs:documentation xml:lang="en">The applicable code as provided for in ATA SIPP Reso 110.16 or IATA Reso 728.</xs:documentation>
												</xs:annotation>
											</xs:attribute>
											<xs:attribute name="TicketDesignatorQualifier" type="AlphaNumericStringLength1to3" use="optional">
												<xs:annotation>
													<xs:documentation>Used to qualify the ticket designator.</xs:documentation>
												</xs:annotation>
											</xs:attribute>
										</xs:complexType>
									</xs:element>
								</xs:sequence>
								<xs:attribute name="Number" type="Numeric0to4" use="required">
									<xs:annotation>
										<xs:documentation xml:lang="en">A sequential number representing the order of the fare comonents within the fare component calculation.</xs:documentation>
									</xs:annotation>
								</xs:attribute>
								<xs:attribute name="TariffNumber" type="NumericStringLength1to3" use="optional">
									<xs:annotation>
										<xs:documentation xml:lang="en">The tariff number where the fare data is filed.</xs:documentation>
									</xs:annotation>
								</xs:attribute>
								<xs:attribute name="RuleNumber" type="AlphaNumericStringLength1to8" use="optional">
									<xs:annotation>
										<xs:documentation xml:lang="en">The rule number within the tariff where the fare data is filed.
</xs:documentation>
									</xs:annotation>
								</xs:attribute>
								<xs:attributeGroup ref="CompanyID_AttributesGroup">
									<xs:annotation>
										<xs:documentation xml:lang="en">The two/three character designator representing the carrier whose fare within the tariff was used.</xs:documentation>
									</xs:annotation>
								</xs:attributeGroup>
								<xs:attribute name="WaiverCode" type="AlphaNumericStringLength1to19" use="optional">
									<xs:annotation>
										<xs:documentation xml:lang="en">A validating carrier assigned code that allows a fare rule modification or override.</xs:documentation>
									</xs:annotation>
								</xs:attribute>
								<xs:attribute name="PassengerTypeCode" type="AlphaLength3" use="optional">
									<xs:annotation>
										<xs:documentation xml:lang="en">The passenger type code.</xs:documentation>
									</xs:annotation>
								</xs:attribute>
								<xs:attribute name="RuleCode" type="AlphaNumericStringLength1to3" use="optional">
									<xs:annotation>
										<xs:documentation xml:lang="en">System generated data indicating the ticketed fare is from a specific rule filed fare sent by the issuing system.</xs:documentation>
									</xs:annotation>
								</xs:attribute>
								<xs:attribute name="FareBasisCode" type="StringLength1to16" use="optional">
									<xs:annotation>
										<xs:documentation xml:lang="en">The applicable code as provided for in ATA SIPP Reso 110.16 or IATA Reso 728.</xs:documentation>
									</xs:annotation>
								</xs:attribute>
								<xs:attribute name="AgreementCode" type="StringLength1to16" use="optional">
									<xs:annotation>
										<xs:documentation xml:lang="en">The applicable Tour/Value Coding/Contract/Commercial Agreement Reference Code used to price the fare component.  </xs:documentation>
									</xs:annotation>
								</xs:attribute>
							</xs:complexType>
						</xs:element>
					</xs:sequence>
					<xs:attribute name="Number" type="AlphaNumericStringLength1to8" use="required">
						<xs:annotation>
							<xs:documentation xml:lang="en">A sequential number representing the priceable unit in which the fare component resides.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
				</xs:complexType>
			</xs:element>
			<xs:element name="TotalConstructionAmount" maxOccurs="3">
				<xs:annotation>
					<xs:documentation>The total base, total net, or total sell construction amount. </xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:attribute name="Purpose" type="PurposeType" use="required">
						<xs:annotation>
							<xs:documentation xml:lang="en">Used to specify base, base net, or base sell.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attributeGroup ref="CurrencyAmountGroup">
						<xs:annotation>
							<xs:documentation xml:lang="en">The total construction amount and the applicable currency code.</xs:documentation>
						</xs:annotation>
					</xs:attributeGroup>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="PriceQuoteDate" type="DateOrDateTimeType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The date on which the fare was calculated and quoted.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="AccountCode" type="StringLength1to32" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">A qualifier for pricing to select fares.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="PricingDesignator" type="StringLength1to32" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">A qualifier for pricing to select fares.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attributeGroup ref="QuantityGroup">
			<xs:annotation>
				<xs:documentation xml:lang="en">The total number of fare components within the fare component calculation.</xs:documentation>
			</xs:annotation>
		</xs:attributeGroup>
		<xs:attribute name="ExchangeRate" type="xs:decimal" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">A published value used to convert a NUC amount to the currency of commencement of travel.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:complexType>
	<xs:complexType name="FareInfoType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Rules for this priced option.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="DepartureDate" type="xs:dateTime" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Departure Date for this priced fare</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="FareReference" minOccurs="0" maxOccurs="5">
				<xs:annotation>
					<xs:documentation xml:lang="en">Used to provide the fare basis code, the fare class code, and/or ticket designator.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:simpleContent>
						<xs:extension base="StringLength0to8">
							<xs:attribute name="ResBookDesigCode" type="UpperCaseAlphaLength1to2" use="optional">
								<xs:annotation>
									<xs:documentation xml:lang="en">Identifies the class of service for the specified fare basis code.</xs:documentation>
								</xs:annotation>
							</xs:attribute>
							<xs:attribute name="TicketDesignatorCode" type="StringLength1to16" use="optional">
								<xs:annotation>
									<xs:documentation xml:lang="en">Specifies the discount code applicable to the fare that is associated with this fare basis code.</xs:documentation>
								</xs:annotation>
							</xs:attribute>
							<xs:attribute name="AccountCode" type="StringLength1to32" use="optional">
								<xs:annotation>
									<xs:documentation xml:lang="en">A code to uniquely identify a fare account.</xs:documentation>
								</xs:annotation>
							</xs:attribute>
						</xs:extension>
					</xs:simpleContent>
				</xs:complexType>
			</xs:element>
			<xs:element name="RuleInfo" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Information regarding restrictions governing use of the fare.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:complexContent>
						<xs:extension base="RuleInfoType">
							<xs:attribute name="TripType" type="AirTripType" use="optional">
								<xs:annotation>
									<xs:documentation xml:lang="en">To specify if the trip is one way or roundtrip.</xs:documentation>
								</xs:annotation>
							</xs:attribute>
							<xs:attribute name="MoneySaverInd" type="xs:boolean" use="optional">
								<xs:annotation>
									<xs:documentation xml:lang="en">If true, the fare rule being requested is for all airline fares for the specified city pair. If false, it is not.
</xs:documentation>
								</xs:annotation>
							</xs:attribute>
						</xs:extension>
					</xs:complexContent>
				</xs:complexType>
			</xs:element>
			<xs:element name="FilingAirline" type="CompanyNameType" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The airline that filed the fare(s).</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="MarketingAirline" type="CompanyNameType" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation xml:lang="en">The marketing airline.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="DepartureAirport" type="LocationType" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Departure point of flight segment.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="ArrivalAirport" type="LocationType" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Arrival point of flight segment.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Date" minOccurs="0" maxOccurs="5">
				<xs:annotation>
					<xs:documentation xml:lang="en">Date information applicable to the fare rules.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:attribute name="Date" type="DateOrDateTimeType" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">The applicable date for the purpose specified in the Type attribute.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="Type" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">Specifies the type of date.</xs:documentation>
						</xs:annotation>
						<xs:simpleType>
							<xs:restriction base="xs:NMTOKEN">
								<xs:enumeration value="LastRuleChange">
									<xs:annotation>
										<xs:documentation xml:lang="en">The last date the rule was changed.</xs:documentation>
									</xs:annotation>
								</xs:enumeration>
								<xs:enumeration value="RuleBecomesInvalid">
									<xs:annotation>
										<xs:documentation xml:lang="en">The date after which the rule is no longer valid.</xs:documentation>
									</xs:annotation>
								</xs:enumeration>
								<xs:enumeration value="RestrictiveFareEffective">
									<xs:annotation>
										<xs:documentation xml:lang="en">The date on which the restrictive fare becomes effective.</xs:documentation>
									</xs:annotation>
								</xs:enumeration>
								<xs:enumeration value="RestrictiveFareDiscontinue">
									<xs:annotation>
										<xs:documentation xml:lang="en">The date on which the restrictive fare is discontinued.</xs:documentation>
									</xs:annotation>
								</xs:enumeration>
							</xs:restriction>
						</xs:simpleType>
					</xs:attribute>
				</xs:complexType>
			</xs:element>
			<xs:element name="FareInfo" minOccurs="0" maxOccurs="15">
				<xs:annotation>
					<xs:documentation xml:lang="en">Fares and related information for this fare rule.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="Date" minOccurs="0" maxOccurs="5">
							<xs:annotation>
								<xs:documentation xml:lang="en">Date information applicable to the fare.</xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:attribute name="Date" type="DateOrDateTimeType" use="optional">
									<xs:annotation>
										<xs:documentation xml:lang="en">The applicable date for the purpose specified in the Type attribute.</xs:documentation>
									</xs:annotation>
								</xs:attribute>
								<xs:attribute name="Type" use="optional">
									<xs:simpleType>
										<xs:restriction base="xs:NMTOKEN">
											<xs:enumeration value="EffectiveTravel">
												<xs:annotation>
													<xs:documentation xml:lang="en">The first travel date for this fare.</xs:documentation>
												</xs:annotation>
											</xs:enumeration>
											<xs:enumeration value="DiscontinueTravel">
												<xs:annotation>
													<xs:documentation xml:lang="en">The last travel date for this fare.</xs:documentation>
												</xs:annotation>
											</xs:enumeration>
											<xs:enumeration value="FirstTicketing">
												<xs:annotation>
													<xs:documentation xml:lang="en">The first date for ticketing.</xs:documentation>
												</xs:annotation>
											</xs:enumeration>
											<xs:enumeration value="LastTicketing">
												<xs:annotation>
													<xs:documentation xml:lang="en">The last date for ticketing.</xs:documentation>
												</xs:annotation>
											</xs:enumeration>
											<xs:enumeration value="TravelCompletion">
												<xs:annotation>
													<xs:documentation xml:lang="en">The date by which travel must be completed.</xs:documentation>
												</xs:annotation>
											</xs:enumeration>
										</xs:restriction>
									</xs:simpleType>
								</xs:attribute>
							</xs:complexType>
						</xs:element>
						<xs:element name="Fare" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">The base and total fare and tax amounts associated with the rule.</xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:attribute name="BaseAmount" type="Money" use="optional">
									<xs:annotation>
										<xs:documentation xml:lang="en">The base amount of the fare.</xs:documentation>
									</xs:annotation>
								</xs:attribute>
								<xs:attribute name="BaseNUC_Amount" type="Money" use="optional">
									<xs:annotation>
										<xs:documentation xml:lang="en">The base neutral unit of construction amount.</xs:documentation>
									</xs:annotation>
								</xs:attribute>
								<xs:attribute name="TaxAmount" type="Money" use="optional">
									<xs:annotation>
										<xs:documentation xml:lang="en">The tax amount for the fare associated to the rule.</xs:documentation>
									</xs:annotation>
								</xs:attribute>
								<xs:attribute name="TotalFare" type="Money" use="optional">
									<xs:annotation>
										<xs:documentation xml:lang="en">The total fare associated to the rule.</xs:documentation>
									</xs:annotation>
								</xs:attribute>
								<xs:attribute name="FareDescription" type="StringLength0to64" use="optional">
									<xs:annotation>
										<xs:documentation xml:lang="en">A description of the fare.</xs:documentation>
									</xs:annotation>
								</xs:attribute>
							</xs:complexType>
						</xs:element>
						<xs:element name="PTC" minOccurs="0" maxOccurs="5">
							<xs:annotation>
								<xs:documentation xml:lang="en">The passenger types for which the fare is applicable.</xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:attribute name="PassengerTypeCode" type="AlphaLength3" use="optional">
									<xs:annotation>
										<xs:documentation xml:lang="en">The passenger type code for this fare.</xs:documentation>
									</xs:annotation>
								</xs:attribute>
							</xs:complexType>
						</xs:element>
					</xs:sequence>
					<xs:attribute name="FareBasisCode" type="StringLength1to16" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">The fare basis code for the fare for this rule.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="GlobalIndicatorCode" type="GlobalIndicatorType" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">The global direction for this fare rule.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="MaximumPermittedMileage" type="xs:integer" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">The maximum mileage (in miles) that can be travelled under this fare.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="TripType" type="AirTripType" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">Tthe type of trip associated with the rule.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="FareType" type="UpperCaseAlphaLength1to3" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">Specifies the fare type for this fare.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="FareStatus" type="FareStatusType" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">Identifies whether the fare was constructed, published, created, etc.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="Operation" type="ActionType" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">Indicates the required modification to the element.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="RPH" type="RPH_Type" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">Provides a reference to a specific FareInfo item between an air modification request and the existing air reservation.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
				</xs:complexType>
			</xs:element>
			<xs:element name="DiscountPricing" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The information needed when a passenger presents a discount/promotional coupon for a dollar/percentage off the fare or when a passenger qualifies for a percentage discount such as a senior discount.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:attributeGroup ref="DiscountPricingGroup"/>
				</xs:complexType>
			</xs:element>
			<xs:element name="City" minOccurs="0" maxOccurs="99">
				<xs:annotation>
					<xs:documentation xml:lang="en">A city code valid for this fare.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:attributeGroup ref="LocationGroup">
						<xs:annotation>
							<xs:documentation xml:lang="en">A city location code.</xs:documentation>
						</xs:annotation>
					</xs:attributeGroup>
				</xs:complexType>
			</xs:element>
			<xs:element name="Airport" minOccurs="0" maxOccurs="99">
				<xs:annotation>
					<xs:documentation xml:lang="en">An airport code valid for this fare.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:attributeGroup ref="LocationGroup">
						<xs:annotation>
							<xs:documentation xml:lang="en">An airport locaton code.</xs:documentation>
						</xs:annotation>
					</xs:attributeGroup>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
		<xs:attributeGroup ref="NegotiatedFareAttributes"/>
		<xs:attribute name="CurrencyCode" type="AlphaLength3" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The ISO 4217 currency code associated with the fare rule information.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="TariffNumber" type="AlphaNumericStringLength1to8" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The tariff number for the rule.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="RuleNumber" type="AlphaNumericStringLength1to8" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The number associated with the fare rule.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="RoutingNumber" type="Numeric1to9999" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The routing number applicable to this fare.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="NbrOfCities" type="Numeric1to99" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The number of cities applicable to this fare.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:complexType>
	<xs:complexType name="FareType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Holds a base fare, tax, total and currency information on a price</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="BaseFare" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Price of the inventory excluding taxes and fees.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:attributeGroup ref="CurrencyAmountGroup"/>
					<xs:attributeGroup ref="ExchangeRateGroup"/>
					<xs:attribute name="Operation" type="ActionType" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">Indicates the required modification to the element.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
				</xs:complexType>
			</xs:element>
			<xs:element name="EquivFare" minOccurs="0" maxOccurs="9">
				<xs:annotation>
					<xs:documentation xml:lang="en">Price of the inventory excluding taxes and fees in the payable currency.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:attributeGroup ref="CurrencyAmountGroup"/>
					<xs:attribute name="Operation" type="ActionType" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">Indicates the required modification to the element.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
				</xs:complexType>
			</xs:element>
			<xs:element name="Taxes" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">This is a collection of Taxes</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="Tax" type="AirTaxType" maxOccurs="99">
							<xs:annotation>
								<xs:documentation xml:lang="en">Any individual tax applied to the fare</xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
					<xs:attribute name="Amount" type="Money" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">The total of all the taxes.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
				</xs:complexType>
			</xs:element>
			<xs:element name="Fees" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">This is a collection of Fees</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="Fee" type="AirFeeType" maxOccurs="9">
							<xs:annotation>
								<xs:documentation xml:lang="en">Any additional fee incurred by the passenger but not shown on the ticket.</xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
					<xs:attributeGroup ref="CurrencyAmountGroup">
						<xs:annotation>
							<xs:documentation xml:lang="en">The fee total and the appropriate currency code.</xs:documentation>
						</xs:annotation>
					</xs:attributeGroup>
				</xs:complexType>
			</xs:element>
			<xs:element name="TotalFare" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The total price that the passenger would pay (includes fare, taxes, fees)</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:attributeGroup ref="CurrencyAmountGroup"/>
					<xs:attribute name="Operation" type="ActionType" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">Indicates the required modification to the element.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
				</xs:complexType>
			</xs:element>
			<xs:element name="FareConstruction" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Should not contain unstructured FareCalc data.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:attribute name="FormattedIndicator" type="xs:boolean" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">When true, this is structured fare calculation information.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="Language" type="xs:language" use="optional"/>
					<xs:attribute name="OriginCityCode" type="StringLength1to8" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">The origin airport city code for this fare construction.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="OriginCodeContext" type="StringLength1to32" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">Identifies the context of the origin city code such as IATA, ARC, or internal code.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="DestinationCityCode" type="StringLength1to8" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">The destination airport city code for this fare construction.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="DestinationCodeContext" type="StringLength1to32" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">Identifies the context of the destination city code such as IATA, ARC, or internal code.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="Operation" type="ActionType" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">Indicates the required modification to the element.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
				</xs:complexType>
			</xs:element>
			<xs:element name="UnstructuredFareCalc" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Contains the fare calc information for the stored fare for the passenger.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:simpleContent>
						<xs:extension base="xs:string">
							<xs:attribute name="FareCalcMode" type="AlphaNumericStringLength1" use="optional">
								<xs:annotation>
									<xs:documentation xml:lang="en">Used to identify the method of pricing for this transaction (e.g., manual,  automated pricing). Should be 1 character in length.</xs:documentation>
								</xs:annotation>
							</xs:attribute>
							<xs:attribute name="Operation" type="ActionType" use="optional">
								<xs:annotation>
									<xs:documentation xml:lang="en">Indicates the required modification to the element.</xs:documentation>
								</xs:annotation>
							</xs:attribute>
						</xs:extension>
					</xs:simpleContent>
				</xs:complexType>
			</xs:element>
			<xs:element name="FareBaggageAllowance" minOccurs="0" maxOccurs="99">
				<xs:annotation>
					<xs:documentation xml:lang="en">Checked baggage allowance for the fare.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:attributeGroup ref="UnitsOfMeasureGroup">
						<xs:annotation>
							<xs:documentation xml:lang="en">Baggage allowance for this fare.</xs:documentation>
						</xs:annotation>
					</xs:attributeGroup>
					<xs:attribute name="FlightSegmentRPH" type="RPH_Type" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">The segment this baggage allowance is associated with.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="Operation" type="ActionType" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">Indicates the required modification to the element.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
				</xs:complexType>
			</xs:element>
			<xs:element name="TourCode" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The tour code associated with the fare.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:simpleContent>
						<xs:extension base="StringLength1to16">
							<xs:attribute name="Operation" type="ActionType" use="optional">
								<xs:annotation>
									<xs:documentation xml:lang="en">Indicates the required modification to the element.</xs:documentation>
								</xs:annotation>
							</xs:attribute>
						</xs:extension>
					</xs:simpleContent>
				</xs:complexType>
			</xs:element>
			<xs:element name="Remark" minOccurs="0" maxOccurs="10">
				<xs:annotation>
					<xs:documentation xml:lang="en">Free flow remarks for the fare breakdown.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:simpleContent>
						<xs:extension base="StringLength0to64"/>
					</xs:simpleContent>
				</xs:complexType>
			</xs:element>
			<xs:element name="OriginalIssueInfo" minOccurs="0">
				<xs:annotation>
					<xs:documentation>The original ticket/document issue information.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:attribute name="TicketDocumentNbr" type="StringLength1to16" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">The original ticket/document number.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="IssuingAgentID" type="NumericStringLength1to8" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">ID of the original issuer of the ticket/document.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="DateOfIssue" type="xs:date" use="optional">
						<xs:annotation>
							<xs:documentation>Date the ticket/document was originally issued.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="LocationCode" type="StringLength1to8" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">Location of original issue.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="IssuingAirlineCode" type="StringLength1to16" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">The code of the original issuing airline.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
				</xs:complexType>
			</xs:element>
			<xs:element name="ExchangeInfo" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Exchange ticket/document information.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="CouponInfo" minOccurs="0" maxOccurs="4">
							<xs:annotation>
								<xs:documentation>Optional information on coupon numbers being exchanged.</xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:attribute name="Number" type="Numeric1to4" use="optional">
									<xs:annotation>
										<xs:documentation xml:lang="en">The applicable coupon number.</xs:documentation>
									</xs:annotation>
								</xs:attribute>
							</xs:complexType>
						</xs:element>
						<xs:element name="OriginalOriginDestination" minOccurs="0">
							<xs:annotation>
								<xs:documentation>The original origin and destination for exchanged ticket/documents.</xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:attribute name="OriginCityCode" type="StringLength1to8" use="optional">
									<xs:annotation>
										<xs:documentation xml:lang="en">Origin city code.</xs:documentation>
									</xs:annotation>
								</xs:attribute>
								<xs:attribute name="DestinationCityCode" type="StringLength1to8" use="optional">
									<xs:annotation>
										<xs:documentation xml:lang="en">Destination city code.</xs:documentation>
									</xs:annotation>
								</xs:attribute>
							</xs:complexType>
						</xs:element>
					</xs:sequence>
					<xs:attribute name="TicketDocumentNbr" type="StringLength1to16" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">The new ticket/document number.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
				</xs:complexType>
			</xs:element>
			<xs:element name="Discounts" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Collection for discounts.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="Discount" maxOccurs="99">
							<xs:annotation>
								<xs:documentation>Provides discount information associated with the fare.</xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:attributeGroup ref="DiscountInfoGroup">
									<xs:annotation>
										<xs:documentation xml:lang="en">Details regarding the discount.</xs:documentation>
									</xs:annotation>
								</xs:attributeGroup>
							</xs:complexType>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element ref="TPA_Extensions" minOccurs="0"/>
		</xs:sequence>
		<xs:attributeGroup ref="NegotiatedFareAttributes">
			<xs:annotation>
				<xs:documentation xml:lang="en"> Used to indicate a negotiated fare and, if so, the fare code.</xs:documentation>
			</xs:annotation>
		</xs:attributeGroup>
		<xs:attribute name="TicketDesignatorCode" type="StringLength1to16" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Specifies a discount code applicable to the fare.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="TotalNbrTrips" type="Numeric1to99" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The total number of trips in this itinerary.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="TotalNbrPTC" type="Numeric1to99" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The total number of passenger type codes in this priced itinerary.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:complexType>
	<xs:complexType name="FlightLegType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Specifies minimal information about a flight.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="DepartureAirport" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The departure airport for the flight.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:attributeGroup ref="LocationGroup">
						<xs:annotation>
							<xs:documentation xml:lang="en">The departure airport/city code.</xs:documentation>
						</xs:annotation>
					</xs:attributeGroup>
				</xs:complexType>
			</xs:element>
			<xs:element name="ArrivalAirport" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">The arrival airport for the flight.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:attributeGroup ref="LocationGroup">
						<xs:annotation>
							<xs:documentation xml:lang="en">The arrival airport/city code.</xs:documentation>
						</xs:annotation>
					</xs:attributeGroup>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="FlightNumber" type="FlightNumberType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">A flight number.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="ResBookDesigCode" type="UpperCaseAlphaLength1to2" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The reservation booking designator for a flight.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="Date" type="xs:dateTime" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">A date for the flight.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:complexType>
	<xs:complexType name="FlightSegmentType">
		<xs:annotation>
			<xs:documentation xml:lang="en">FlightSegmentType extends FlightSegmentBaseType to provide additional functionality.</xs:documentation>
		</xs:annotation>
		<xs:complexContent>
			<xs:extension base="FlightSegmentBaseType">
				<xs:sequence>
					<xs:element name="MarketingAirline" minOccurs="0">
						<xs:annotation>
							<xs:documentation xml:lang="en">The marketing airline. This is required for use with scheduled airline messages but may be omitted for requests by tour operators.</xs:documentation>
						</xs:annotation>
						<xs:complexType>
							<xs:simpleContent>
								<xs:extension base="CompanyNameType">
									<xs:attributeGroup ref="SingleVendorIndGroup">
										<xs:annotation>
											<xs:documentation xml:lang="en">To specifiy if an airline is a member of an alliance.</xs:documentation>
										</xs:annotation>
									</xs:attributeGroup>
								</xs:extension>
							</xs:simpleContent>
						</xs:complexType>
					</xs:element>
				</xs:sequence>
				<xs:attribute name="FlightNumber" type="FlightNumberType" use="optional">
					<xs:annotation>
						<xs:documentation xml:lang="en">The flight number of the flight. This is required for use with scheduled airline messages but may be omitted for requests by tour operators.</xs:documentation>
					</xs:annotation>
				</xs:attribute>
				<xs:attribute name="TourOperatorFlightID" type="StringLength1to8" use="optional">
					<xs:annotation>
						<xs:documentation xml:lang="en">ID of a flight in the Tour Operator's inventory. This flight is not necessarily in the inventory of an airline. Rather, it is a code created by tour operators.</xs:documentation>
					</xs:annotation>
				</xs:attribute>
				<xs:attribute name="GovernmentApprovalInd" type="xs:boolean" use="optional">
					<xs:annotation>
						<xs:documentation xml:lang="en">When true, indicates that the flight is subject to government approval.</xs:documentation>
					</xs:annotation>
				</xs:attribute>
				<xs:attribute name="GovernmentApprovalText" type="xs:string" use="optional">
					<xs:annotation>
						<xs:documentation xml:lang="en">Provides additional information concerning the flight being subject to government approval.</xs:documentation>
					</xs:annotation>
				</xs:attribute>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:complexType name="FulfillmentType">
		<xs:annotation>
			<xs:documentation>Payment information relevant to a booking.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="PaymentDetails" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Container for Payment Detail.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="PaymentDetail" maxOccurs="99">
							<xs:annotation>
								<xs:documentation xml:lang="en">Form of payment details for the requested booking. It is possible to use multiple forms of payment for one transaction, therefore this element may be used more than once.</xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:complexContent>
									<xs:extension base="PaymentDetailType">
										<xs:attribute name="Operation" type="ActionType" use="optional">
											<xs:annotation>
												<xs:documentation xml:lang="en">Indicates the required modification to the element.</xs:documentation>
											</xs:annotation>
										</xs:attribute>
									</xs:extension>
								</xs:complexContent>
							</xs:complexType>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="DeliveryAddress" type="AddressType" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Ticket delivery information for the booking request.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Name" type="PersonNameType" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Purchaser name details</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Receipt" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Specifies the requested distribution method for the ticket receipt.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:attribute name="DistribType" type="OTA_CodeType">
						<xs:annotation>
							<xs:documentation xml:lang="en">Refer to OpenTravel Code List Distribution Type List (DTB).</xs:documentation>
						</xs:annotation>
					</xs:attribute>
				</xs:complexType>
			</xs:element>
			<xs:element name="PaymentText" minOccurs="0" maxOccurs="5">
				<xs:annotation>
					<xs:documentation xml:lang="en">Used to provide textual information concerning fulfillment.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:simpleContent>
						<xs:extension base="FormattedTextTextType">
							<xs:attribute name="Name" type="StringLength1to16" use="optional">
								<xs:annotation>
									<xs:documentation xml:lang="en">Used to specify the type of information.</xs:documentation>
								</xs:annotation>
							</xs:attribute>
						</xs:extension>
					</xs:simpleContent>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="MarketingCabinType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Marketing name for the First, Business or Economy cabin.</xs:documentation>
		</xs:annotation>
		<xs:complexContent>
			<xs:extension base="CabinAvailabilityType">
				<xs:attribute name="Name" type="StringLength1to32" use="optional">
					<xs:annotation>
						<xs:documentation xml:lang="en">The marketing name of the cabin that is specific to the supplier.</xs:documentation>
					</xs:annotation>
				</xs:attribute>
				<xs:attribute name="RPH" type="RPH_Type" use="optional">
					<xs:annotation>
						<xs:documentation xml:lang="en">The reference place holder to link the marketing cabin information and the RBD.</xs:documentation>
					</xs:annotation>
				</xs:attribute>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:complexType name="OriginDestinationOptionType">
		<xs:annotation>
			<xs:documentation xml:lang="en">A container for flight segments.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="FlightSegment" maxOccurs="8">
				<xs:annotation>
					<xs:documentation xml:lang="en">A container for necessary data to describe one or more legs of a single flight number.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:complexContent>
						<xs:extension base="BookFlightSegmentType">
							<xs:sequence>
								<xs:element ref="TPA_Extensions" minOccurs="0"/>
							</xs:sequence>
						</xs:extension>
					</xs:complexContent>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="OtherServiceInfoType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Other Service Information (OSI) for relevant airlines</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="TravelerRefNumber" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation xml:lang="en">One or more travelers to whom this request applies</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:attributeGroup ref="TravelerRefNumberGroup"/>
				</xs:complexType>
			</xs:element>
			<xs:element name="Airline" type="CompanyNameType">
				<xs:annotation>
					<xs:documentation xml:lang="en">The airline to which the OSI applies. When applicable to all airlines or an unknown airline use Code="YY".</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Text" type="StringLength1to64">
				<xs:annotation>
					<xs:documentation xml:lang="en">The OSI text.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="Code" type="AlphaLength4" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">A proprietary code used between systems to identify an Other Service Information (OSI) item.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:complexType>
	<xs:complexType name="PassengerTypeQuantityType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Specifies a PTC and the associated number of PTC's - for use in specifying passenger lists.</xs:documentation>
		</xs:annotation>
		<xs:attributeGroup ref="TravelerCountGroup"/>
	</xs:complexType>
	<xs:complexType name="PriceRequestInformationType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Identify pricing source, if negotiated fares are requested and if it is a reprice request.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="NegotiatedFareCode" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation xml:lang="en">Contains negotiated fare code information.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:simpleContent>
						<xs:extension base="xs:string">
							<xs:attributeGroup ref="CodeListAirGroup">
								<xs:annotation>
									<xs:documentation xml:lang="en">Contains code information for a negotiated fare.</xs:documentation>
								</xs:annotation>
							</xs:attributeGroup>
							<xs:attribute name="TicketDesignatorCode" type="StringLength1to16" use="optional">
								<xs:annotation>
									<xs:documentation xml:lang="en">The ticket designator code for this negotiated fare code.</xs:documentation>
								</xs:annotation>
							</xs:attribute>
							<xs:attribute name="OverrideRuleInd" type="xs:boolean" use="optional">
								<xs:annotation>
									<xs:documentation xml:lang="en">When true, the rules for the negotiated fare should not be applied.</xs:documentation>
								</xs:annotation>
							</xs:attribute>
						</xs:extension>
					</xs:simpleContent>
				</xs:complexType>
			</xs:element>
			<xs:element name="RebookOption" minOccurs="0" maxOccurs="99">
				<xs:annotation>
					<xs:documentation xml:lang="en">Provides information concerning a flight segment that can be rebooked for a lower fare.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:attribute name="FlightSegmentRPH" type="RPH_Type" use="required">
						<xs:annotation>
							<xs:documentation xml:lang="en">A reference to a booked flight segment.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="ResBookDesigCode" type="UpperCaseAlphaLength1to2" use="required">
						<xs:annotation>
							<xs:documentation xml:lang="en">The class of service in which the flight should be rebooked for a lower fare.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
		<xs:attributeGroup ref="PriceRequestAttributes">
			<xs:annotation>
				<xs:documentation xml:lang="en">Used to describe a price request.</xs:documentation>
			</xs:annotation>
		</xs:attributeGroup>
		<xs:attribute name="CabinType" type="CabinType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Identifies the type of cabin (i.e., first, business, economy) requested.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="TicketingCountry" type="ISO3166" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The ISO country code where the ticket will be issued.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="OverrideAirlineCode" type="AlphaNumericStringLength1to8" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The airline which should be used to override the system selected pricing airline.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:complexType>
	<xs:complexType name="PricedItinerariesType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Container for priced itineraries.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="PricedItinerary" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation xml:lang="en">Itinerary with pricing information.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:complexContent>
						<xs:extension base="PricedItineraryType">
							<xs:attribute name="OriginDestinationRefNumber" type="Numeric1to99" use="optional">
								<xs:annotation>
									<xs:documentation xml:lang="en">This attribute refers back to origin destination information in the OTA_AirLowFareSearchRQ message.</xs:documentation>
								</xs:annotation>
							</xs:attribute>
						</xs:extension>
					</xs:complexContent>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="PricedItineraryType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Itinerary with pricing information.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="AirItinerary" type="AirItineraryType" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Specifies the origin and destination of the traveler. Attributes: DirectionInd - A directional indicator that identifies a type of air booking, either one-way, round-trip, or open-jaw with the enumeration of (OneWay | RT | OpenJaw) respectively. ActionCode - Indicates the status of the booking, such as OK or Wait-List. NumberInParty - Indicates the traveler count. </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="AirItineraryPricingInfo" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en"> Pricing Information for an Air Itinerary.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:complexContent>
						<xs:extension base="AirItineraryPricingInfoType">
							<xs:attribute name="RepriceRequired" type="xs:boolean">
								<xs:annotation>
									<xs:documentation xml:lang="en"> If true re-pricing of the itinerary is required.</xs:documentation>
								</xs:annotation>
							</xs:attribute>
						</xs:extension>
					</xs:complexContent>
				</xs:complexType>
			</xs:element>
			<xs:element name="Notes" type="FreeTextType" minOccurs="0" maxOccurs="5">
				<xs:annotation>
					<xs:documentation xml:lang="en">Provides for free form descriptive information for the priced itinerary.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="TicketingInfo" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Container for TicketingInfoRS_Type.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:complexContent>
						<xs:extension base="TicketingInfoRS_Type">
							<xs:sequence>
								<xs:element name="DeliveryInfo" minOccurs="0" maxOccurs="5">
									<xs:annotation>
										<xs:documentation xml:lang="en">Shipping information for the ticket.</xs:documentation>
									</xs:annotation>
									<xs:complexType>
										<xs:attribute name="DistribType" type="OTA_CodeType" use="optional">
											<xs:annotation>
												<xs:documentation xml:lang="en">Specifies the manner in which a ticket will be sent to the traveler. Refer to OpenTravel Code List Distribution Type (DTB).</xs:documentation>
											</xs:annotation>
										</xs:attribute>
										<xs:attributeGroup ref="CurrencyAmountGroup">
											<xs:annotation>
												<xs:documentation xml:lang="en">Specifies the cost of the ticket delivery option.</xs:documentation>
											</xs:annotation>
										</xs:attributeGroup>
									</xs:complexType>
								</xs:element>
							</xs:sequence>
							<xs:attribute name="PaymentType" type="ListOfOTA_CodeType" use="optional">
								<xs:annotation>
									<xs:documentation xml:lang="en">Specifies the allowable forms of payment (i.e., check, cash, credit card).  Refer to OpenTravel Code List Payment Type (PMT).</xs:documentation>
								</xs:annotation>
							</xs:attribute>
						</xs:extension>
					</xs:complexContent>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="SequenceNumber" type="xs:positiveInteger" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Assigns a number to priced itineraries.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="PriceType" type="StringLength1to8" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Specifies how the pricing was done.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="NUC_Rate" type="xs:decimal" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The neutral unit currency rate.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="ExchangeRate" type="xs:decimal" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The currency exchange rate.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:complexType>
	<xs:complexType name="PTCFareBreakdownType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Per passenger type code pricing for this itinerary. Set if fareBreakdown was requested.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="PassengerTypeQuantity" type="PassengerTypeQuantityType">
				<xs:annotation>
					<xs:documentation xml:lang="en">Number of individuals traveling under this PTC</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="FareBasisCodes" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">This is a collection of fare basis codes.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="FareBasisCode" type="FareBasisCodeType" maxOccurs="299">
							<xs:annotation>
								<xs:documentation xml:lang="en">Fare basis code for the price for this PTC</xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="PassengerFare" minOccurs="0" maxOccurs="2">
				<xs:annotation>
					<xs:documentation xml:lang="en">The total passenger fare with cost breakdown.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:complexContent>
						<xs:extension base="FareType">
							<xs:sequence>
								<xs:element name="TicketFeeDetail" minOccurs="0">
									<xs:annotation>
										<xs:documentation xml:lang="en">The ticket fee information for this passsenger type code.</xs:documentation>
									</xs:annotation>
									<xs:complexType>
										<xs:sequence>
											<xs:element name="Fee" maxOccurs="9">
												<xs:annotation>
													<xs:documentation xml:lang="en">The ticket fee code.</xs:documentation>
												</xs:annotation>
												<xs:complexType>
													<xs:sequence>
														<xs:element name="BaseFee" minOccurs="0">
															<xs:annotation>
																<xs:documentation xml:lang="en">The base fee amount.</xs:documentation>
															</xs:annotation>
															<xs:complexType>
																<xs:attribute name="Amount" type="Money" use="required">
																	<xs:annotation>
																		<xs:documentation xml:lang="en">The base fee amount.</xs:documentation>
																	</xs:annotation>
																</xs:attribute>
															</xs:complexType>
														</xs:element>
														<xs:element name="Taxes" minOccurs="0">
															<xs:annotation>
																<xs:documentation xml:lang="en">A collection of taxes.</xs:documentation>
															</xs:annotation>
															<xs:complexType>
																<xs:sequence>
																	<xs:element name="Tax" type="AirTaxType" maxOccurs="99">
																		<xs:annotation>
																			<xs:documentation xml:lang="en">The tax for the ticket fee.</xs:documentation>
																		</xs:annotation>
																	</xs:element>
																</xs:sequence>
																<xs:attribute name="Amount" type="Money" use="optional">
																	<xs:annotation>
																		<xs:documentation xml:lang="en">The total amount for all ticket fee taxes.</xs:documentation>
																	</xs:annotation>
																</xs:attribute>
															</xs:complexType>
														</xs:element>
														<xs:element name="Total" minOccurs="0">
															<xs:annotation>
																<xs:documentation xml:lang="en">The total of the base fee and taxes.</xs:documentation>
															</xs:annotation>
															<xs:complexType>
																<xs:attribute name="Amount" type="Money" use="required">
																	<xs:annotation>
																		<xs:documentation xml:lang="en">The total of the base fee and taxes.</xs:documentation>
																	</xs:annotation>
																</xs:attribute>
															</xs:complexType>
														</xs:element>
													</xs:sequence>
													<xs:attribute name="FeeCode" type="StringLength1to16" use="required"/>
													<xs:attribute name="Description" type="StringLength0to128" use="optional"/>
												</xs:complexType>
											</xs:element>
											<xs:element name="Total" minOccurs="0" maxOccurs="3">
												<xs:annotation>
													<xs:documentation xml:lang="en">Used to specify the total base fees, total taxes and total of both.</xs:documentation>
												</xs:annotation>
												<xs:complexType>
													<xs:attribute name="Type" use="required">
														<xs:simpleType>
															<xs:restriction base="xs:NMTOKEN">
																<xs:enumeration value="Base">
																	<xs:annotation>
																		<xs:documentation xml:lang="en">The total of base ticket fees.</xs:documentation>
																	</xs:annotation>
																</xs:enumeration>
																<xs:enumeration value="Tax">
																	<xs:annotation>
																		<xs:documentation xml:lang="en">The total of all taxes for the ticket fees.</xs:documentation>
																	</xs:annotation>
																</xs:enumeration>
																<xs:enumeration value="Total">
																	<xs:annotation>
																		<xs:documentation xml:lang="en">The total of all ticket fees.</xs:documentation>
																	</xs:annotation>
																</xs:enumeration>
															</xs:restriction>
														</xs:simpleType>
													</xs:attribute>
													<xs:attribute name="Amount" type="Money" use="required"/>
												</xs:complexType>
											</xs:element>
										</xs:sequence>
										<xs:attributeGroup ref="CurrencyCodeGroup">
											<xs:annotation>
												<xs:documentation xml:lang="en">The currency code for all of the ticketing fees.</xs:documentation>
											</xs:annotation>
										</xs:attributeGroup>
									</xs:complexType>
								</xs:element>
							</xs:sequence>
							<xs:attribute name="Usage" use="optional">
								<xs:annotation>
									<xs:documentation xml:lang="en">Specifies the usage of the passenger fare structure.</xs:documentation>
								</xs:annotation>
								<xs:simpleType>
									<xs:restriction base="xs:NMTOKEN">
										<xs:enumeration value="PassengerFare">
											<xs:annotation>
												<xs:documentation xml:lang="en">The fare information for the passenger fees.</xs:documentation>
											</xs:annotation>
										</xs:enumeration>
										<xs:enumeration value="TicketFee">
											<xs:annotation>
												<xs:documentation xml:lang="en">The fare information for the ticket fees.</xs:documentation>
											</xs:annotation>
										</xs:enumeration>
									</xs:restriction>
								</xs:simpleType>
							</xs:attribute>
						</xs:extension>
					</xs:complexContent>
				</xs:complexType>
			</xs:element>
			<xs:element name="TravelerRefNumber" minOccurs="0" maxOccurs="99">
				<xs:annotation>
					<xs:documentation xml:lang="en">Contains the RPH reference to the traveler.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:attributeGroup ref="TravelerRefNumberGroup"/>
				</xs:complexType>
			</xs:element>
			<xs:element name="TicketDesignators" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">This is a collection of ticket designator elements.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="TicketDesignator" maxOccurs="10">
							<xs:annotation>
								<xs:documentation xml:lang="en">Contains the discount code and a flight reference applicable to the fare.</xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:attribute name="FlightRefRPH" type="RPH_Type" use="optional">
									<xs:annotation>
										<xs:documentation xml:lang="en">Identifies the flight to which this ticket designator applies.</xs:documentation>
									</xs:annotation>
								</xs:attribute>
								<xs:attribute name="TicketDesignatorCode" type="StringLength1to16" use="optional">
									<xs:annotation>
										<xs:documentation xml:lang="en">Specifies a discount code applicable to the fare.</xs:documentation>
									</xs:annotation>
								</xs:attribute>
								<xs:attribute name="TicketDesignatorExtension" type="StringLength1to16" use="optional">
									<xs:annotation>
										<xs:documentation xml:lang="en">Specifies an extension that a carrier may apply to a  ticket designator.</xs:documentation>
									</xs:annotation>
								</xs:attribute>
							</xs:complexType>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="Endorsements" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Container for endorsements.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="Endorsement" maxOccurs="9">
							<xs:annotation>
								<xs:documentation xml:lang="en">Specifies ticket endorsement information.</xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:complexContent>
									<xs:extension base="FreeTextType">
										<xs:attribute name="Operation" type="ActionType" use="optional">
											<xs:annotation>
												<xs:documentation xml:lang="en">Indicates the required modification to the element.</xs:documentation>
											</xs:annotation>
										</xs:attribute>
									</xs:extension>
								</xs:complexContent>
							</xs:complexType>
						</xs:element>
					</xs:sequence>
					<xs:attribute name="NonRefundableIndicator" type="xs:boolean" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">Indicates whether the ticket is refundable. If true, the ticket is NOT refundable.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="NonEndorsableIndicator" type="xs:boolean" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">Indicates whether the ticket is endorsable. If true, the ticket is NOT endorsable.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
				</xs:complexType>
			</xs:element>
			<xs:element name="FareInfo" minOccurs="0" maxOccurs="99">
				<xs:annotation>
					<xs:documentation xml:lang="en">Detailed information on individual priced fares.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:annotation>
						<xs:documentation xml:lang="en">Information used to define a fare and its associated rules information.</xs:documentation>
					</xs:annotation>
					<xs:complexContent>
						<xs:extension base="FareInfoType">
							<xs:sequence>
								<xs:element name="PassengerFare" type="FareType">
									<xs:annotation>
										<xs:documentation xml:lang="en">The fare with cost breakdown.</xs:documentation>
									</xs:annotation>
								</xs:element>
							</xs:sequence>
						</xs:extension>
					</xs:complexContent>
				</xs:complexType>
			</xs:element>
			<xs:element name="PricingUnit" minOccurs="0" maxOccurs="20">
				<xs:annotation>
					<xs:documentation xml:lang="en">A pricing unit for a passenger type code.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="FareComponent" maxOccurs="99">
							<xs:annotation>
								<xs:documentation xml:lang="en">Provides the data for a component of the fare.</xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:sequence>
									<xs:element name="FlightLeg" maxOccurs="99">
										<xs:annotation>
											<xs:documentation xml:lang="en">The flight leg information for the fare component.</xs:documentation>
										</xs:annotation>
										<xs:complexType>
											<xs:complexContent>
												<xs:extension base="BookFlightSegmentType">
													<xs:attribute name="SurchargeInd" type="xs:boolean" use="optional">
														<xs:annotation>
															<xs:documentation xml:lang="en">When true, a surcharge applies to this flight leg.</xs:documentation>
														</xs:annotation>
													</xs:attribute>
													<xs:attribute name="FareBasisCode" type="StringLength1to16" use="optional">
														<xs:annotation>
															<xs:documentation xml:lang="en">The fare basis code for this flight leg.</xs:documentation>
														</xs:annotation>
													</xs:attribute>
													<xs:attributeGroup ref="UnitsOfMeasureGroup">
														<xs:annotation>
															<xs:documentation xml:lang="en">Used to specify the free baggage allowance for this flight leg.</xs:documentation>
														</xs:annotation>
													</xs:attributeGroup>
												</xs:extension>
											</xs:complexContent>
										</xs:complexType>
									</xs:element>
								</xs:sequence>
								<xs:attribute name="Number" type="Numeric1to99" use="required">
									<xs:annotation>
										<xs:documentation xml:lang="en">The fare component number.</xs:documentation>
									</xs:annotation>
								</xs:attribute>
								<xs:attributeGroup ref="CurrencyAmountGroup">
									<xs:annotation>
										<xs:documentation xml:lang="en">The currency code and the amount for the fare component.</xs:documentation>
									</xs:annotation>
								</xs:attributeGroup>
							</xs:complexType>
						</xs:element>
					</xs:sequence>
					<xs:attribute name="UnitNumber" type="Numeric1to99" use="required">
						<xs:annotation>
							<xs:documentation xml:lang="en">A sequential number that identifies this pricing unit.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="PricingSource" type="PricingSourceType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Indicates whether the fare is public, private or both.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="FlightRefNumberRPHList" type="ListOfRPH" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">One or more flights to which this fare applies.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:complexType>
	<xs:complexType name="RowDetailsType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Describes the row details in a seat map</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="AirSeats">
				<xs:annotation>
					<xs:documentation xml:lang="en">A Collection of Air Seat</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="AirSeat" maxOccurs="99">
							<xs:annotation>
								<xs:documentation xml:lang="en">A Seat within a row</xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:annotation>
									<xs:documentation xml:lang="en">Describes the seat attributes.</xs:documentation>
								</xs:annotation>
								<xs:attributeGroup ref="SeatDetailsGroup"/>
								<xs:attribute name="RPH" type="RPH_Type" use="optional">
									<xs:annotation>
										<xs:documentation xml:lang="en">Should be used for situations in which a passenger in the input request is already assigned a seat. The RPH value will correspond to the RPH in the element  TravelRefNumber within the element TravelerInfoSummary.</xs:documentation>
									</xs:annotation>
								</xs:attribute>
							</xs:complexType>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="AirRowCharacteristics">
				<xs:annotation>
					<xs:documentation xml:lang="en">Contains a list of characteristics of an air row.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:attribute name="CharacteristicList" type="ListOfOTA_CodeType" use="required">
						<xs:annotation>
							<xs:documentation xml:lang="en">Describes the characteristics of a specific seat row. Refer to OpenTravel Code List Air Row Type (ROW).</xs:documentation>
						</xs:annotation>
					</xs:attribute>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="MaxNumberOfSeats" type="Numeric0to99">
			<xs:annotation>
				<xs:documentation xml:lang="en">Maximum number of seats per row.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="RowNumber" type="xs:integer" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Specifies the actual  (physical) row number in the seat map.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="AirBookDesigCode" type="UpperCaseAlphaLength1to2" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Allows that a seat may be assigned a class code.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="RowSequenceNumber" type="xs:integer" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The absolute sequence number of real or virtual rows on a plane. (Enabling the display of non existing intermediate rows e.g. aisles, galleys or jumpseats).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:complexType>
	<xs:complexType name="RuleInfoType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Contains summary fare rule information as well as detailed Rule Information for Fare Basis Codes. Information may be actual rules data or the results returned from a rules-based inquiry.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="ResTicketingRules" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">General container for rules regarding fare reservation,  ticketing and sale restrictions</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="AdvResTicketing" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">Container for holding rules regarding advance reservation or ticketing restrictions. </xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:complexContent>
									<xs:extension base="AdvResTicketingType">
										<xs:attribute name="FirstTicketDate" type="DateOrDateTimeType" use="optional">
											<xs:annotation>
												<xs:documentation xml:lang="en">The first date that a ticket may be issued for this fare.</xs:documentation>
											</xs:annotation>
										</xs:attribute>
										<xs:attribute name="LastTicketDate" type="DateOrDateTimeType" use="optional">
											<xs:annotation>
												<xs:documentation xml:lang="en">The last date that a ticket may be issued for this fare.</xs:documentation>
											</xs:annotation>
										</xs:attribute>
									</xs:extension>
								</xs:complexContent>
							</xs:complexType>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="LengthOfStayRules" type="StayRestrictionsType" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Rules providing minimum or maximum stay restrictions.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="ChargesRules" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">General container for rules specifying amounts for such things as: surcharges, deposits, change penalties, cancellation penalties, etc.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="VoluntaryChanges" type="VoluntaryChangesType" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">Specifies a voluntary change charge.</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="VoluntaryRefunds" type="VoluntaryChangesType" minOccurs="0">
							<xs:annotation>
								<xs:documentation xml:lang="en">Specifies a Voluntary Refund (cancellation) charge.</xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="SeatMapDetailsType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Details of a seat map for a particular aircraft</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="CabinClass" maxOccurs="5">
				<xs:annotation>
					<xs:documentation xml:lang="en">Describes the Cabin details in a seat map.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:complexContent>
						<xs:extension base="CabinClassType">
							<xs:attribute name="StartingRow" type="Numeric1to3" use="optional">
								<xs:annotation>
									<xs:documentation xml:lang="en">Specify the starting row number for this cabin class.</xs:documentation>
								</xs:annotation>
							</xs:attribute>
							<xs:attribute name="EndingRow" type="Numeric1to3" use="optional">
								<xs:annotation>
									<xs:documentation xml:lang="en">Specify the ending row number for this cabin class.</xs:documentation>
								</xs:annotation>
							</xs:attribute>
						</xs:extension>
					</xs:complexContent>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="TravelerRefNumberRPHs" type="ListOfRPH" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The reference number is used as a cross reference between the AirTravelerType and the SeatMapDetails. This will be used only if different seat maps are valid for different passengers for the same flight segment.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:complexType>
	<xs:complexType name="SeatRequestType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Object to hold a passengers' seat request</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="DepartureAirport" type="LocationType" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Departure point of flight segment.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="ArrivalAirport" type="LocationType" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Arrival point of flight segment.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Airline" type="CompanyNameType" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Specify the airline the seat was requested for.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
		<xs:attributeGroup ref="SeatRequestAttributes">
			<xs:annotation>
				<xs:documentation xml:lang="en">Attributes for seat request. Note: you can choose a specific seat or just a general preference</xs:documentation>
			</xs:annotation>
		</xs:attributeGroup>
		<xs:attribute name="DepartureDate" type="DateOrDateTimeType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The departure date of the flight for the seat requested.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="FlightNumber" type="FlightNumberType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The number of the flight for which this seat is requested.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="Status" type="OTA_CodeType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Code providing status information for this seat request. Refer to OpenTravel Code List Status (STS).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:complexType>
	<xs:complexType name="SpecialRemarkType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Itinerary Remarks, Invoice Remarks, etc.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="TravelerRefNumber" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation xml:lang="en">One or more travelers to whom this request applies.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:attributeGroup ref="TravelerRefNumberGroup"/>
					<xs:attribute name="RangePosition" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">Specifies whether this is the first or last traveler reference number in a range.</xs:documentation>
						</xs:annotation>
						<xs:simpleType>
							<xs:restriction base="xs:NMTOKEN">
								<xs:enumeration value="First">
									<xs:annotation>
										<xs:documentation xml:lang="en">The first number in a range.</xs:documentation>
									</xs:annotation>
								</xs:enumeration>
								<xs:enumeration value="Last">
									<xs:annotation>
										<xs:documentation xml:lang="en">The last number in a range.</xs:documentation>
									</xs:annotation>
								</xs:enumeration>
							</xs:restriction>
						</xs:simpleType>
					</xs:attribute>
				</xs:complexType>
			</xs:element>
			<xs:element name="FlightRefNumber" minOccurs="0" maxOccurs="10">
				<xs:annotation>
					<xs:documentation xml:lang="en">One or more flights to whom this request applies</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:attributeGroup ref="FlightRefNumberGroup"/>
				</xs:complexType>
			</xs:element>
			<xs:element name="Text" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Text associated with remark</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Airline" type="CompanyNameType" minOccurs="0" maxOccurs="5">
				<xs:annotation>
					<xs:documentation xml:lang="en">Denotes the receiver (or target) airline(s) for the remark.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="AuthorizedViewers" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">A container for authorized viewers. </xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="AuthorizedViewer" maxOccurs="99">
							<xs:annotation>
								<xs:documentation xml:lang="en">Specifies those authorized to view a confidential special remark.</xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:attribute name="ViewerCode" type="StringLength1to16" use="optional">
									<xs:annotation>
										<xs:documentation xml:lang="en">Identifies an authorized viewer of a confidential remark. Can be a 3 character ATA/IATA airport/city code, an office ID, pseudo city code, etc.</xs:documentation>
									</xs:annotation>
								</xs:attribute>
								<xs:attribute name="ViewerCarrierCode" type="StringLength1to8" use="optional">
									<xs:annotation>
										<xs:documentation xml:lang="en">Carrier code that may be used in conjunction with the viewer code to identify those authorized to view the confidential special remark.</xs:documentation>
									</xs:annotation>
								</xs:attribute>
							</xs:complexType>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="RemarkType" type="OTA_CodeType" use="required">
			<xs:annotation>
				<xs:documentation xml:lang="en">Type of special remark used (e.g., itinerary remark, invoice remark). Refer to OpenTravel Code List Special Remark Option Type (SRO).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="ID" type="AlphaNumericStringLength1to8" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Used to further define the remark type.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:complexType>
	<xs:complexType name="SpecialReqDetailsType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Remarks, OSIs, Seat Requests etc.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="SeatRequests" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">A collection of Seat Request</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="SeatRequest" maxOccurs="unbounded">
							<xs:annotation>
								<xs:documentation xml:lang="en">Seating requests for each passenger for each air leg of this trip.</xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:annotation>
									<xs:documentation xml:lang="en"> AWG to revisit.</xs:documentation>
								</xs:annotation>
								<xs:complexContent>
									<xs:extension base="SeatRequestType">
										<xs:attribute name="TravelerRefNumberRPHList" type="ListOfRPH" use="optional">
											<xs:annotation>
												<xs:documentation xml:lang="en">One or more travelers to whom this request applies.</xs:documentation>
											</xs:annotation>
										</xs:attribute>
										<xs:attribute name="FlightRefNumberRPHList" type="ListOfRPH" use="optional">
											<xs:annotation>
												<xs:documentation xml:lang="en">One or more flights to whom this request applies.</xs:documentation>
											</xs:annotation>
										</xs:attribute>
										<xs:attribute name="PartialSeatingInd" type="xs:boolean" use="optional">
											<xs:annotation>
												<xs:documentation xml:lang="en">When true, the carrier supports partial seating. When false, the carrier does not support partial seating.</xs:documentation>
											</xs:annotation>
										</xs:attribute>
									</xs:extension>
								</xs:complexContent>
							</xs:complexType>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="SpecialServiceRequests" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">A collection of Special Service Request.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="SpecialServiceRequest" maxOccurs="unbounded">
							<xs:annotation>
								<xs:documentation xml:lang="en">Special Service Requests (SSR) for this booking.</xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:annotation>
									<xs:documentation xml:lang="en"> AWG to revisit.</xs:documentation>
								</xs:annotation>
								<xs:complexContent>
									<xs:extension base="SpecialServiceRequestType">
										<xs:sequence>
											<xs:element name="FlightLeg" type="FlightLegType" minOccurs="0">
												<xs:annotation>
													<xs:documentation xml:lang="en">Flight information associated to this special request, used when FlightRefNumberRPHList is not available or is different.</xs:documentation>
												</xs:annotation>
											</xs:element>
										</xs:sequence>
										<xs:attribute name="TravelerRefNumberRPHList" type="ListOfRPH" use="optional">
											<xs:annotation>
												<xs:documentation xml:lang="en">One or more travelers to whom this request applies.</xs:documentation>
											</xs:annotation>
										</xs:attribute>
										<xs:attribute name="FlightRefNumberRPHList" type="ListOfRPH" use="optional">
											<xs:annotation>
												<xs:documentation xml:lang="en">One or more flights to whom this request applies.</xs:documentation>
											</xs:annotation>
										</xs:attribute>
										<xs:attributeGroup ref="BirthDateGroup">
											<xs:annotation>
												<xs:documentation xml:lang="en">The birth date of the traveler to whom this SSR applies.</xs:documentation>
											</xs:annotation>
										</xs:attributeGroup>
									</xs:extension>
								</xs:complexContent>
							</xs:complexType>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="OtherServiceInformations" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">A collection of Other Service Information.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="OtherServiceInformation" maxOccurs="unbounded">
							<xs:annotation>
								<xs:documentation xml:lang="en">Other Service Information (OSI) for relevant airlines.</xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:complexContent>
									<xs:extension base="OtherServiceInfoType">
										<xs:attribute name="RPH" type="RPH_Type" use="optional">
											<xs:annotation>
												<xs:documentation xml:lang="en">Unique value associated with the OSI.</xs:documentation>
											</xs:annotation>
										</xs:attribute>
										<xs:attribute name="Operation" type="ActionType" use="optional">
											<xs:annotation>
												<xs:documentation xml:lang="en">Indicates the required modification to the element.</xs:documentation>
											</xs:annotation>
										</xs:attribute>
									</xs:extension>
								</xs:complexContent>
							</xs:complexType>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="Remarks" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">A collection of Remark.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="Remark" maxOccurs="unbounded">
							<xs:annotation>
								<xs:documentation xml:lang="en">Supplementary information for this booking.</xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:simpleContent>
									<xs:extension base="xs:string">
										<xs:attribute name="RPH" type="RPH_Type" use="optional">
											<xs:annotation>
												<xs:documentation xml:lang="en">Unique value associated with the Remark.</xs:documentation>
											</xs:annotation>
										</xs:attribute>
										<xs:attribute name="Operation" type="ActionType" use="optional">
											<xs:annotation>
												<xs:documentation xml:lang="en">Indicates the required modification to the element.</xs:documentation>
											</xs:annotation>
										</xs:attribute>
									</xs:extension>
								</xs:simpleContent>
							</xs:complexType>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="SpecialRemarks" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">A collection of Special Remark.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="SpecialRemark" maxOccurs="unbounded">
							<xs:annotation>
								<xs:documentation xml:lang="en">Itinerary Remarks, Invoice Remarks, etc.</xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:complexContent>
									<xs:extension base="SpecialRemarkType">
										<xs:sequence>
											<xs:element name="FlightLeg" type="FlightLegType" minOccurs="0">
												<xs:annotation>
													<xs:documentation xml:lang="en">Flight information associated to this special remark, used when FlightRefNumberRPHList is not available or is different.</xs:documentation>
												</xs:annotation>
											</xs:element>
										</xs:sequence>
										<xs:attribute name="Operation" type="ActionType" use="optional">
											<xs:annotation>
												<xs:documentation xml:lang="en">Indicates the required modification to the element.</xs:documentation>
											</xs:annotation>
										</xs:attribute>
										<xs:attribute name="RPH" type="RPH_Type" use="optional">
											<xs:annotation>
												<xs:documentation xml:lang="en">Unique value associated with the Special Remark.</xs:documentation>
											</xs:annotation>
										</xs:attribute>
									</xs:extension>
								</xs:complexContent>
							</xs:complexType>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="SpecialServiceRequestType">
		<xs:annotation>
			<xs:documentation xml:lang="en">SSR's for this booking request, for example meals.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="Airline" type="CompanyNameType" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Specify airline to request availability for.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Text" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Text associated with remark.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="SSRCode" type="AlphaLength4" use="required">
			<xs:annotation>
				<xs:documentation xml:lang="en">The four alpha position industry code identifying a particular type of special service request.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="ServiceQuantity" type="Numeric1to99" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Used to specify the number of special services.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="Status" type="OTA_CodeType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Code providing status information for this special service request. Refer to OpenTravel Code List Status (STS).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="Number" type="Numeric1to999" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">A number which identifies an SSR in a system.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:complexType>
	<xs:complexType name="SpecificFlightInfoType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Specify actual airline, flight number, or booking class</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="FlightNumber" type="FlightNumberType" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Specific flight number to request availability for. Requires that Airline is also supplied.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Airline" type="CompanyNameType" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Specify airline to request availability for.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="BookingClassPref" minOccurs="0" maxOccurs="5">
				<xs:annotation>
					<xs:documentation xml:lang="en">Specifies specific booking classes to include and/or exclude in the response.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:attributeGroup ref="BookingClassPrefGroup">
						<xs:annotation>
							<xs:documentation xml:lang="en">Specifies a booking class to be included or excluded in the response.</xs:documentation>
						</xs:annotation>
					</xs:attributeGroup>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="StayRestrictionsType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Type defining Min and Max Stay Restrictions.</xs:documentation>
		</xs:annotation>
		<xs:sequence minOccurs="0">
			<xs:element name="MinimumStay" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Specifies restrictions for the shortest length/period of time or earliest day return travel can commence or be completed.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:attribute name="ReturnTimeOfDay" type="TimeOrDateTimeType" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">The time of day when return travel may commence.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="MinStay" type="Numeric1to99" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">The amount of elapsed time or number of occurrences of a day of the week needed to satisfy a minimum stay requirement.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="StayUnit" type="StayUnitType" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">The unit of elapsed time or the day of the week applied to the MinStay value.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="MinStayDate" type="TimeOrDateTimeType" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">The specific date for the minimum stay requirement.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="ComplicatedRulesInd" type="xs:boolean" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">If true, there are complicated rules for the minimum stay requirement.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
				</xs:complexType>
			</xs:element>
			<xs:element name="MaximumStay" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Specifies restrictions for the  longest length/period of time or last day to begin or complete the return.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:attribute name="ReturnType" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">Code indicating whether travel must commence or be completed in order to satisfy the stay restriction.</xs:documentation>
						</xs:annotation>
						<xs:simpleType>
							<xs:restriction base="xs:string">
								<xs:enumeration value="C">
									<xs:annotation>
										<xs:documentation xml:lang="en">Return travel must be Completed.</xs:documentation>
									</xs:annotation>
								</xs:enumeration>
								<xs:enumeration value="S">
									<xs:annotation>
										<xs:documentation xml:lang="en">Return travel must be Started.</xs:documentation>
									</xs:annotation>
								</xs:enumeration>
							</xs:restriction>
						</xs:simpleType>
					</xs:attribute>
					<xs:attribute name="ReturnTimeOfDay" type="TimeOrDateTimeType" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">The time of day when return travel may commence.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="MaxStay" type="Numeric1to999" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">The amount of elapsed time or number of occurrences of a day of the week that must occur to satisfy a maximum stay requirement.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="StayUnit" type="StayUnitType" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">The unit of elapsed time or the day of the week applied to the MaxStay value.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="MaxStayDate" type="TimeOrDateTimeType" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">The specific date for the maximum stay requirement.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="ComplicatedRulesInd" type="xs:boolean" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">If true, there are complicated rules for the maximum stay requirement.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="StayRestrictionsInd" type="xs:boolean" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">True indicates that Stay Restrictions exist.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:complexType>
	<xs:complexType name="TicketingInfoRS_Type">
		<xs:annotation>
			<xs:documentation xml:lang="en">Extends TicketingInfoType to provide an eTicketNumber.</xs:documentation>
			<xs:documentation xml:lang="en"> Minimum information about ticketing required to complete the booking transaction plus eTicket number.</xs:documentation>
		</xs:annotation>
		<xs:complexContent>
			<xs:extension base="TicketingInfoType">
				<xs:attribute name="eTicketNumber" type="AlphaNumericStringLength1to14" use="optional">
					<xs:annotation>
						<xs:documentation xml:lang="en">If reservation is electronically ticketed at time of booking, this is the field for the eTicket number.</xs:documentation>
					</xs:annotation>
				</xs:attribute>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:complexType name="TicketingInfoType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Minimum information about ticketing required to complete the booking transaction.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="TicketAdvisory" minOccurs="0" maxOccurs="10">
				<xs:annotation>
					<xs:documentation xml:lang="en">Open text field available for additional ticket information.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:complexContent>
						<xs:extension base="FreeTextType">
							<xs:attribute name="Operation" type="ActionType" use="optional">
								<xs:annotation>
									<xs:documentation xml:lang="en">Indicates the required modification to the element.</xs:documentation>
								</xs:annotation>
							</xs:attribute>
						</xs:extension>
					</xs:complexContent>
				</xs:complexType>
			</xs:element>
			<xs:element name="TicketingVendor" minOccurs="0">
				<xs:annotation>
					<xs:documentation>The vendor that issues or services the ticket.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:attributeGroup ref="CompanyID_AttributesGroup">
						<xs:annotation>
							<xs:documentation xml:lang="en">The code of the vendor that issues or services the ticket.</xs:documentation>
						</xs:annotation>
					</xs:attributeGroup>
				</xs:complexType>
			</xs:element>
			<xs:element name="PricingSystem" minOccurs="0">
				<xs:annotation>
					<xs:documentation>The system that priced the itinerary for this ticket.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:attributeGroup ref="CompanyID_AttributesGroup">
						<xs:annotation>
							<xs:documentation xml:lang="en">The code of the system that priced the itinerary for the ticket.</xs:documentation>
						</xs:annotation>
					</xs:attributeGroup>
				</xs:complexType>
			</xs:element>
			<xs:element name="TotalFare" minOccurs="0">
				<xs:annotation>
					<xs:documentation>The total price paid for this ticket.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:attributeGroup ref="CurrencyAmountGroup">
						<xs:annotation>
							<xs:documentation xml:lang="en">Details for the total price paid for this ticket.</xs:documentation>
						</xs:annotation>
					</xs:attributeGroup>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="TicketTimeLimit" type="xs:dateTime" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">TicketTimeLimit - Indicates the ticketing arrangement, and allows for the requirement that an itinerary must be ticketed by a certain date and time.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="CancelOnExpiryInd" type="xs:boolean" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">When true, unticketed bookings should be cancelled when the TicketTimeLimit has expired.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="TicketType" type="TicketType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">TicketType - Indicates the type of ticket (Paper, eTicket)</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="TicketingStatus" type="OTA_CodeType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Code for setting and displaying detailed ticketing information. Refer to OpenTravel Code List Ticketing Status (TST).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="FlightSegmentRefNumber" type="ListOfRPH" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Specifies one or more segment numbers for ticketing purposes. This RPH is associated with the RPH contained in the FlightSegment element in AirBookRQ.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="TravelerRefNumber" type="ListOfRPH" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Specifies one or more traveler names for ticketing purposes. This RPH is associated with the RPH contained in the TravelerRefNumber element in AirBookRQ.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="ReverseTktgSegmentsInd" type="xs:boolean" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Applies a reverse sequence of the outbound travel to the inbound travel.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="PseudoCityCode" type="StringLength1to16" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">An identification code assigned to an office/agency by a reservation system.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="RequestedTicketingDate" type="DateOrDateTimeType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The date on which ticketing should occur.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="TimeLimitMinutes" type="Numeric1to99" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The number of minutes until the ticket must be issued for the itinerary.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="BookingChangeType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Indicates that the booking has changed since this ticket was issued and the two are no longer in sync and identifies what has changed in the booking.</xs:documentation>
			</xs:annotation>
			<xs:simpleType>
				<xs:restriction base="xs:NMTOKEN">
					<xs:enumeration value="FlightSegment">
						<xs:annotation>
							<xs:documentation xml:lang="en">Indicates at least one flight segment has changed.</xs:documentation>
						</xs:annotation>
					</xs:enumeration>
					<xs:enumeration value="TravelerName">
						<xs:annotation>
							<xs:documentation xml:lang="en">Indicates the traveler name has changed.</xs:documentation>
						</xs:annotation>
					</xs:enumeration>
					<xs:enumeration value="Both">
						<xs:annotation>
							<xs:documentation xml:lang="en">Indicates both the flight segment and the traveler name have changed.</xs:documentation>
						</xs:annotation>
					</xs:enumeration>
				</xs:restriction>
			</xs:simpleType>
		</xs:attribute>
		<xs:attribute name="TicketDocumentNbr" type="StringLength1to16" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">The ticket document number including the airline code, the form code, and the serial number.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="PassengerTypeCode" type="AlphaLength3" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Code used to indicate the ticketing-relevant type of traveler that has been quoted (e.g., MIL, CHD, INF, SEN).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="Operation" type="ActionType" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Indicates the required modification to the element.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="MiscTicketingCode" type="ListOfOTA_CodeType" use="optional">
			<xs:annotation>
				<xs:documentation>Specifies one or more ticketing relevant codes. Refer to OpenTravel code list Misc Ticketing Code (MTC).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:complexType>
	<xs:complexType name="TravelerInformationType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Specify passenger numbers and types.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="PassengerTypeQuantity" type="PassengerTypeQuantityType" maxOccurs="10">
				<xs:annotation>
					<xs:documentation xml:lang="en">Specify number of passengers using Passenger Type Codes.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="AirTraveler" type="AirTravelerType" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Information profiling the person traveling: Gender - the gender of the customer, if needed; BirthDate - Date of Birth; Currency - the preferred currency in which monetary amounts should be returned.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="TravelerInfoSummaryType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Specify passenger numbers and types</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="SeatsRequested" type="xs:nonNegativeInteger" minOccurs="0" maxOccurs="99">
				<xs:annotation>
					<xs:documentation xml:lang="en"> Number of seats requested.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="AirTravelerAvail" type="TravelerInformationType" minOccurs="0" maxOccurs="99">
				<xs:annotation>
					<xs:documentation xml:lang="en">Specifies passenger numbers and types.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="PriceRequestInformation" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Identifies pricing source, if negotiated fares are requested and if it is a reprice request.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:complexContent>
						<xs:extension base="PriceRequestInformationType">
							<xs:sequence>
								<xs:element name="FareRestrictionPref" minOccurs="0" maxOccurs="5">
									<xs:annotation>
										<xs:documentation xml:lang="en">Constrains the pricing to those fares with restrictions that satisfy user-imposed limitations.</xs:documentation>
									</xs:annotation>
									<xs:complexType>
										<xs:attributeGroup ref="FareRestrictPrefGroup">
											<xs:annotation>
												<xs:documentation xml:lang="en">Used to specify a fare restriction and the preference level for the restriction.</xs:documentation>
											</xs:annotation>
										</xs:attributeGroup>
									</xs:complexType>
								</xs:element>
								<xs:element name="Tax" type="AirTaxType" minOccurs="0" maxOccurs="20">
									<xs:annotation>
										<xs:documentation xml:lang="en">Used to specify tax information which may be used to override the taxes in the pricing system.</xs:documentation>
									</xs:annotation>
								</xs:element>
								<xs:element name="DiscountPricing" minOccurs="0">
									<xs:annotation>
										<xs:documentation xml:lang="en">The information needed when a passenger presents a discount/promotional coupon for a dollar/percentage of the fare or when a passenger qualifies for a percentage discount such as a senior discount.</xs:documentation>
									</xs:annotation>
									<xs:complexType>
										<xs:sequence>
											<xs:element name="FlightReference" minOccurs="0" maxOccurs="16">
												<xs:annotation>
													<xs:documentation xml:lang="en">Specifies a flight to which the discount pricing applies.</xs:documentation>
												</xs:annotation>
												<xs:complexType>
													<xs:attribute name="FlightRefNumber" type="StringLength1to64" use="required">
														<xs:annotation>
															<xs:documentation xml:lang="en">The flight segment to which the discount pricing should be applied.</xs:documentation>
														</xs:annotation>
													</xs:attribute>
												</xs:complexType>
											</xs:element>
										</xs:sequence>
										<xs:attributeGroup ref="DiscountPricingGroup"/>
									</xs:complexType>
								</xs:element>
								<xs:element name="SegmentOverride" minOccurs="0" maxOccurs="20">
									<xs:annotation>
										<xs:documentation xml:lang="en">Used to specify a segment that should be overridden.</xs:documentation>
									</xs:annotation>
									<xs:complexType>
										<xs:attribute name="SegmentNumber" type="Numeric1to99" use="required">
											<xs:annotation>
												<xs:documentation xml:lang="en">Used to specify a segment that should be overridden.</xs:documentation>
											</xs:annotation>
										</xs:attribute>
										<xs:attribute name="SegmentType" use="required">
											<xs:annotation>
												<xs:documentation xml:lang="en">Used to specify what should be overridden.</xs:documentation>
											</xs:annotation>
											<xs:simpleType>
												<xs:restriction base="xs:NMTOKEN">
													<xs:enumeration value="Connection">
														<xs:annotation>
															<xs:documentation xml:lang="en">Ignore that this is a connection segment.</xs:documentation>
														</xs:annotation>
													</xs:enumeration>
													<xs:enumeration value="Stopover">
														<xs:annotation>
															<xs:documentation xml:lang="en">Ignore that this is a stopover segment.</xs:documentation>
														</xs:annotation>
													</xs:enumeration>
													<xs:enumeration value="TurnaroundPoint">
														<xs:annotation>
															<xs:documentation xml:lang="en">Ignore that this is a turnaround point segment.</xs:documentation>
														</xs:annotation>
													</xs:enumeration>
												</xs:restriction>
											</xs:simpleType>
										</xs:attribute>
									</xs:complexType>
								</xs:element>
								<xs:element name="Account" minOccurs="0" maxOccurs="99">
									<xs:annotation>
										<xs:documentation xml:lang="en">Specifies an account code to be used in pricing.</xs:documentation>
									</xs:annotation>
									<xs:complexType>
										<xs:attribute name="CodeOnlyFaresInd" type="xs:boolean" use="optional">
											<xs:annotation>
												<xs:documentation xml:lang="en">If true, return only the fares for the specified account code.</xs:documentation>
											</xs:annotation>
										</xs:attribute>
										<xs:attribute name="Code" type="StringLength1to32" use="required">
											<xs:annotation>
												<xs:documentation xml:lang="en">The account code for which fares are requested.</xs:documentation>
											</xs:annotation>
										</xs:attribute>
									</xs:complexType>
								</xs:element>
								<xs:element name="LocationRequirement" minOccurs="0">
									<xs:annotation>
										<xs:documentation xml:lang="en">Location requirement information for pricing.</xs:documentation>
									</xs:annotation>
									<xs:complexType>
										<xs:attribute name="Type" use="optional">
											<xs:annotation>
												<xs:documentation xml:lang="en">Used to specify the location requirement type affecting the pricing.</xs:documentation>
											</xs:annotation>
											<xs:simpleType>
												<xs:restriction base="xs:NMTOKEN">
													<xs:enumeration value="LocalEmployee">
														<xs:annotation>
															<xs:documentation xml:lang="en">The customer is a local employee.</xs:documentation>
														</xs:annotation>
													</xs:enumeration>
													<xs:enumeration value="LocalResident">
														<xs:annotation>
															<xs:documentation xml:lang="en">The customer is a local resident.</xs:documentation>
														</xs:annotation>
													</xs:enumeration>
													<xs:enumeration value="LocalNationalityShipReg">
														<xs:annotation>
															<xs:documentation xml:lang="en">The customer is a local national or on a ship registered to the location.</xs:documentation>
														</xs:annotation>
													</xs:enumeration>
												</xs:restriction>
											</xs:simpleType>
										</xs:attribute>
										<xs:attribute name="State" type="StateProvCodeType" use="optional">
											<xs:annotation>
												<xs:documentation xml:lang="en">The state location required for some fares.</xs:documentation>
											</xs:annotation>
										</xs:attribute>
										<xs:attribute name="Country" type="ISO3166" use="optional">
											<xs:annotation>
												<xs:documentation xml:lang="en">The country location required for some fares.</xs:documentation>
											</xs:annotation>
										</xs:attribute>
									</xs:complexType>
								</xs:element>
							</xs:sequence>
						</xs:extension>
					</xs:complexContent>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="TravelerInfoType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Identifies passenger(s) who will travel on the reservation.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="AirTraveler" minOccurs="0" maxOccurs="99">
				<xs:annotation>
					<xs:documentation xml:lang="en">Information about the person traveling.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:complexContent>
						<xs:extension base="AirTravelerType">
							<xs:sequence>
								<xs:element name="Comment" minOccurs="0" maxOccurs="5">
									<xs:annotation>
										<xs:documentation xml:lang="en">A comment about the air traveler.</xs:documentation>
									</xs:annotation>
									<xs:complexType>
										<xs:simpleContent>
											<xs:extension base="FormattedTextTextType">
												<xs:attribute name="Name" type="StringLength1to16" use="optional">
													<xs:annotation>
														<xs:documentation xml:lang="en">Used to specify the type of comment.</xs:documentation>
													</xs:annotation>
												</xs:attribute>
											</xs:extension>
										</xs:simpleContent>
									</xs:complexType>
								</xs:element>
							</xs:sequence>
						</xs:extension>
					</xs:complexContent>
				</xs:complexType>
			</xs:element>
			<xs:element name="SpecialReqDetails" type="SpecialReqDetailsType" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation xml:lang="en">Provides detailed information regarding any special needs, requests, or remarks associated with the traveler</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="VoluntaryChangesType">
		<xs:annotation>
			<xs:documentation xml:lang="en">Specifies charges and/or penalties associated with making ticket changes after purchase.</xs:documentation>
		</xs:annotation>
		<xs:sequence minOccurs="0">
			<xs:element name="Penalty" minOccurs="0">
				<xs:annotation>
					<xs:documentation xml:lang="en">Specifies penalty charges as either a currency amount or a percentage of the fare</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:attribute name="PenaltyType" type="xs:string" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">Indicates the type of penalty involved in the search or response.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="DepartureStatus" type="xs:string" use="optional">
						<xs:annotation>
							<xs:documentation xml:lang="en">Identifier used to indicate whether the change occurs before or after departure from the origin city.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attributeGroup ref="CurrencyAmountGroup">
						<xs:annotation>
							<xs:documentation xml:lang="en">The penalty charge defined a fee in terms of its amount, currency and decimal places.</xs:documentation>
						</xs:annotation>
					</xs:attributeGroup>
					<xs:attribute name="Percent" type="Percentage">
						<xs:annotation>
							<xs:documentation xml:lang="en">The penalty charge conveyed as a percent of the total fare.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="VolChangeInd" type="xs:boolean" use="optional">
			<xs:annotation>
				<xs:documentation xml:lang="en">Indicator used to specify whether voluntary change and other penalties are involved in the search or response.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:complexType>
</xs:schema>
