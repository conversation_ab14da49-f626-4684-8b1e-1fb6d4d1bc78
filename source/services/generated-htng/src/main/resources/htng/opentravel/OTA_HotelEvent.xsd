<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns="http://www.opentravel.org/OTA/2003/05" targetNamespace="http://www.opentravel.org/OTA/2003/05" elementFormDefault="qualified" version="2.000" id="OTA2006B2011A">
  <xs:include schemaLocation="OTA_HotelRFP.xsd"/>
  <xs:annotation>
    <xs:documentation xml:lang="en">All Schema files in the OpenTravel Alliance specification are made available according to the terms defined by the OpenTravel License Agreement at http://www.opentravel.org/Specifications/Default.aspx.</xs:documentation>
  </xs:annotation>
  <xs:simpleType name="EventDayType">
    <xs:annotation>
      <xs:documentation>Allows for the specification of a pre-event, post event or published event day.</xs:documentation>
    </xs:annotation>
    <xs:restriction base="xs:NMTOKEN">
      <xs:enumeration value="PreEvent">
        <xs:annotation>
          <xs:documentation xml:lang="en">Indicates this day is prior to the published event dates.</xs:documentation>
        </xs:annotation>
      </xs:enumeration>
      <xs:enumeration value="PostEvent">
        <xs:annotation>
          <xs:documentation xml:lang="en">Indicates this day is after the published event dates.</xs:documentation>
        </xs:annotation>
      </xs:enumeration>
      <xs:enumeration value="Published">
        <xs:annotation>
          <xs:documentation xml:lang="en">Indicates this day is part of the published event dates.</xs:documentation>
        </xs:annotation>
      </xs:enumeration>
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="ProviderType">
    <xs:annotation>
      <xs:documentation xml:lang="en">Identifies who is providing an item or service.</xs:documentation>
    </xs:annotation>
    <xs:restriction base="xs:NMTOKEN">
      <xs:enumeration value="NotRequired">
        <xs:annotation>
          <xs:documentation xml:lang="en">Indicates no requirement.</xs:documentation>
        </xs:annotation>
      </xs:enumeration>
      <xs:enumeration value="Group">
        <xs:annotation>
          <xs:documentation xml:lang="en">Indicates the group will provide.</xs:documentation>
        </xs:annotation>
      </xs:enumeration>
      <xs:enumeration value="Venue">
        <xs:annotation>
          <xs:documentation xml:lang="en">Indicates the venue will provide.</xs:documentation>
        </xs:annotation>
      </xs:enumeration>
      <xs:enumeration value="OutsideVendor">
        <xs:annotation>
          <xs:documentation xml:lang="en">Indicates an outside vendor will provide.</xs:documentation>
        </xs:annotation>
      </xs:enumeration>
    </xs:restriction>
  </xs:simpleType>
  <xs:attributeGroup name="AttendanceGroup">
    <xs:annotation>
      <xs:documentation xml:lang="en">Defines attendee information for an event.</xs:documentation>
    </xs:annotation>
    <xs:attribute name="ExpectedTotalQuantity" type="xs:nonNegativeInteger" use="optional">
      <xs:annotation>
        <xs:documentation xml:lang="en">The expected number of total attendees at the event.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="DomesticQuantity" type="xs:nonNegativeInteger" use="optional">
      <xs:annotation>
        <xs:documentation xml:lang="en">The expected number of domestic attendees for the event.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="InternationalQuantity" type="xs:nonNegativeInteger" use="optional">
      <xs:annotation>
        <xs:documentation xml:lang="en">The expected number of international attendess for the event.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="PreRegisteredQuantity" type="xs:nonNegativeInteger" use="optional">
      <xs:annotation>
        <xs:documentation xml:lang="en">The number of preregistered attendees for the event.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
  </xs:attributeGroup>
  <xs:attributeGroup name="PostEventAttendanceGroup">
    <xs:annotation>
      <xs:documentation xml:lang="en">Defines attendee information for an event.</xs:documentation>
    </xs:annotation>
    <xs:attribute name="TotalQuantity" type="xs:nonNegativeInteger" use="optional">
      <xs:annotation>
        <xs:documentation xml:lang="en">The expected number of total attendees at the event.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="DomesticQuantity" type="xs:nonNegativeInteger" use="optional">
      <xs:annotation>
        <xs:documentation xml:lang="en">The expected number of domestic attendees for the event.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="InternationalQuantity" type="xs:nonNegativeInteger" use="optional">
      <xs:annotation>
        <xs:documentation xml:lang="en">The expected number of international attendess for the event.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="PreRegisteredQuantity" type="xs:nonNegativeInteger" use="optional">
      <xs:annotation>
        <xs:documentation xml:lang="en">The number of preregistered attendees for the event.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="OnsiteRegisteredQuantity" type="xs:nonNegativeInteger" use="optional">
      <xs:annotation>
        <xs:documentation xml:lang="en">The number of attendees for the event that registered onsite.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="NoShowQuantity" type="xs:nonNegativeInteger" use="optional">
      <xs:annotation>
        <xs:documentation xml:lang="en">The number of no shows for the event.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="ExhibitorQuantity" type="xs:nonNegativeInteger" use="optional">
      <xs:annotation>
        <xs:documentation xml:lang="en">The number of exhibitor attendees for the event.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="QuantityType" use="optional">
      <xs:annotation>
        <xs:documentation xml:lang="en">Defines the type of information (i.e., estimated, guaranteed, actual).</xs:documentation>
      </xs:annotation>
      <xs:simpleType>
        <xs:restriction base="xs:NMTOKEN">
          <xs:enumeration value="Estimated">
            <xs:annotation>
              <xs:documentation xml:lang="en">The projected quantity for this event.</xs:documentation>
            </xs:annotation>
          </xs:enumeration>
          <xs:enumeration value="Guaranteed">
            <xs:annotation>
              <xs:documentation xml:lang="en">The guaranteed quantity for this event.</xs:documentation>
            </xs:annotation>
          </xs:enumeration>
          <xs:enumeration value="Actual">
            <xs:annotation>
              <xs:documentation xml:lang="en">The realized quantity for this event.</xs:documentation>
            </xs:annotation>
          </xs:enumeration>
        </xs:restriction>
      </xs:simpleType>
    </xs:attribute>
  </xs:attributeGroup>
  <xs:attributeGroup name="ProviderGroup">
    <xs:annotation>
      <xs:documentation xml:lang="en">Identifies the provider of an item or service which with it is associated.</xs:documentation>
    </xs:annotation>
    <xs:attribute name="ProvidedBy" type="ProviderType" use="optional">
      <xs:annotation>
        <xs:documentation xml:lang="en">Identifies who will provide the item or service (e.g., group, venue).</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="ProviderName" type="StringLength1to64" use="optional">
      <xs:annotation>
        <xs:documentation xml:lang="en">Identifes the name of the provider of the item or service.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
  </xs:attributeGroup>
  <xs:attributeGroup name="RoomPickUpGroup">
    <xs:annotation>
      <xs:documentation>Provides room pick-up for a specific time parameter.</xs:documentation>
    </xs:annotation>
    <xs:attribute name="TotalNumberOfUnits" type="xs:integer" use="optional">
      <xs:annotation>
        <xs:documentation xml:lang="en">Total number of rooms for this day.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="PickUpParameter" use="optional">
      <xs:annotation>
        <xs:documentation xml:lang="en">Identifies a point in time on which this number of units was booked.</xs:documentation>
      </xs:annotation>
      <xs:simpleType>
        <xs:restriction base="xs:NMTOKEN">
          <xs:enumeration value="90_Day">
            <xs:annotation>
              <xs:documentation xml:lang="en">90 days before the event start date.</xs:documentation>
            </xs:annotation>
          </xs:enumeration>
          <xs:enumeration value="60_Day">
            <xs:annotation>
              <xs:documentation xml:lang="en">60 days before the event start date.</xs:documentation>
            </xs:annotation>
          </xs:enumeration>
          <xs:enumeration value="30_Day">
            <xs:annotation>
              <xs:documentation xml:lang="en">30 days before the event start date.</xs:documentation>
            </xs:annotation>
          </xs:enumeration>
          <xs:enumeration value="21_Day">
            <xs:annotation>
              <xs:documentation xml:lang="en">21 days before the event start date.</xs:documentation>
            </xs:annotation>
          </xs:enumeration>
          <xs:enumeration value="14_Day">
            <xs:annotation>
              <xs:documentation xml:lang="en">14 days before the event start date.</xs:documentation>
            </xs:annotation>
          </xs:enumeration>
          <xs:enumeration value="7_Day">
            <xs:annotation>
              <xs:documentation xml:lang="en">7 days before the event start date.</xs:documentation>
            </xs:annotation>
          </xs:enumeration>
          <xs:enumeration value="Cutoff">
            <xs:annotation>
              <xs:documentation xml:lang="en">At the cutoff date.</xs:documentation>
            </xs:annotation>
          </xs:enumeration>
          <xs:enumeration value="Actual">
            <xs:annotation>
              <xs:documentation xml:lang="en">At the close-out of the event.</xs:documentation>
            </xs:annotation>
          </xs:enumeration>
        </xs:restriction>
      </xs:simpleType>
    </xs:attribute>
  </xs:attributeGroup>
  <xs:attributeGroup name="TaxExemptGroup">
    <xs:annotation>
      <xs:documentation xml:lang="en">Provides tax exempt information for an organization.</xs:documentation>
    </xs:annotation>
    <xs:attribute name="TaxExemptIndicator" type="xs:boolean" use="optional">
      <xs:annotation>
        <xs:documentation xml:lang="en">When true, indicates the exhibiting company is tax exempt.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="TaxExemptID" type="StringLength1to32" use="optional">
      <xs:annotation>
        <xs:documentation xml:lang="en">Exhibiting company's tax exempt identifier.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="TaxExemptType" use="optional">
      <xs:annotation>
        <xs:documentation xml:lang="en">Used to indicate whether the exhibiting company's tax ID number is state or federal issued.</xs:documentation>
      </xs:annotation>
      <xs:simpleType>
        <xs:restriction base="xs:NMTOKEN">
          <xs:enumeration value="State">
            <xs:annotation>
              <xs:documentation xml:lang="en">Indicates the tax exempt status is from the state government.</xs:documentation>
            </xs:annotation>
          </xs:enumeration>
          <xs:enumeration value="Federal">
            <xs:annotation>
              <xs:documentation xml:lang="en">Indicates the tax exempt status is from the federal government.</xs:documentation>
            </xs:annotation>
          </xs:enumeration>
          <xs:enumeration value="City">
            <xs:annotation>
              <xs:documentation xml:lang="en">Indicates the tax exempt status is from the city government.</xs:documentation>
            </xs:annotation>
          </xs:enumeration>
        </xs:restriction>
      </xs:simpleType>
    </xs:attribute>
  </xs:attributeGroup>
  <xs:complexType name="EventType">
    <xs:annotation>
      <xs:documentation xml:lang="en">Information details for an event.</xs:documentation>
    </xs:annotation>
    <xs:sequence>
      <xs:element name="Event" maxOccurs="unbounded">
        <xs:annotation>
          <xs:documentation xml:lang="en">Information for an event. Typically, this would be used to send information regarding related events (e.g., the same training class being taken by different organizations).</xs:documentation>
        </xs:annotation>
        <xs:complexType>
          <xs:sequence>
            <xs:element name="Contacts" minOccurs="0">
              <xs:annotation>
                <xs:documentation xml:lang="en">A collection of contacts associated with the event.</xs:documentation>
              </xs:annotation>
              <xs:complexType>
                <xs:sequence>
                  <xs:element name="Contact" maxOccurs="unbounded">
                    <xs:annotation>
                      <xs:documentation xml:lang="en">A contact associated with the event.</xs:documentation>
                    </xs:annotation>
                    <xs:complexType>
                      <xs:complexContent>
                        <xs:extension base="ContactPersonType">
                          <xs:sequence>
                            <xs:element name="Comments" minOccurs="0">
                              <xs:annotation>
                                <xs:documentation xml:lang="en">A collection of comments pertaining to the contact.</xs:documentation>
                              </xs:annotation>
                              <xs:complexType>
                                <xs:sequence>
                                  <xs:element name="Comment" type="ParagraphType" maxOccurs="99">
                                    <xs:annotation>
                                      <xs:documentation xml:lang="en">Used to provide additional information about the contact or the contact organization.</xs:documentation>
                                    </xs:annotation>
                                  </xs:element>
                                </xs:sequence>
                              </xs:complexType>
                            </xs:element>
                          </xs:sequence>
                          <xs:attribute name="OnSiteIndicator" type="xs:boolean" use="optional">
                            <xs:annotation>
                              <xs:documentation xml:lang="en">When true, indicates that the contact is on site.</xs:documentation>
                            </xs:annotation>
                          </xs:attribute>
                        </xs:extension>
                      </xs:complexContent>
                    </xs:complexType>
                  </xs:element>
                </xs:sequence>
              </xs:complexType>
            </xs:element>
            <xs:element name="AttendeeInfo" minOccurs="0">
              <xs:annotation>
                <xs:documentation xml:lang="en">The number of attendees to the event.</xs:documentation>
              </xs:annotation>
              <xs:complexType>
                <xs:sequence>
                  <xs:element name="Comments" minOccurs="0">
                    <xs:annotation>
                      <xs:documentation xml:lang="en">A collection of comments pertaining to the attendees.</xs:documentation>
                    </xs:annotation>
                    <xs:complexType>
                      <xs:sequence>
                        <xs:element name="Comment" type="ParagraphType" maxOccurs="99">
                          <xs:annotation>
                            <xs:documentation xml:lang="en">Comments about the attendees as a group (e.g., accessibility or special needs or demographic profile comments).</xs:documentation>
                          </xs:annotation>
                        </xs:element>
                      </xs:sequence>
                    </xs:complexType>
                  </xs:element>
                </xs:sequence>
                <xs:attributeGroup ref="AttendanceGroup"/>
              </xs:complexType>
            </xs:element>
            <xs:element name="EventInfos" minOccurs="0">
              <xs:annotation>
                <xs:documentation xml:lang="en">A collection of event infos.</xs:documentation>
              </xs:annotation>
              <xs:complexType>
                <xs:sequence>
                  <xs:element name="EventInfo" maxOccurs="unbounded">
                    <xs:annotation>
                      <xs:documentation xml:lang="en">Details regarding a particular occurence of an event.</xs:documentation>
                    </xs:annotation>
                    <xs:complexType>
                      <xs:sequence>
                        <xs:element name="AdditionalDates" minOccurs="0">
                          <xs:annotation>
                            <xs:documentation xml:lang="en">A collection of additional dates.</xs:documentation>
                          </xs:annotation>
                          <xs:complexType>
                            <xs:sequence>
                              <xs:element name="AdditionalDate" maxOccurs="unbounded">
                                <xs:annotation>
                                  <xs:documentation xml:lang="en">Additional date/time information (e.g., dates and times associated with pre- and post-convention meetings, major arrival and departure dates for event attendees, group arrivals and departure dates).</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                  <xs:attributeGroup ref="DateTimeSpanGroup">
                                    <xs:annotation>
                                      <xs:documentation xml:lang="en">The start and end dates associated with the Type.</xs:documentation>
                                    </xs:annotation>
                                  </xs:attributeGroup>
                                  <xs:attribute name="Type" use="optional">
                                    <xs:annotation>
                                      <xs:documentation xml:lang="en">The type of dates being defined. (e.g., pre-convention meeting, post-convention meeting, major arrivals, major departures).</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                      <xs:restriction base="xs:NMTOKEN">
                                        <xs:enumeration value="PreConMeeting">
                                          <xs:annotation>
                                            <xs:documentation xml:lang="en">Indicates a pre-convention meeting.</xs:documentation>
                                          </xs:annotation>
                                        </xs:enumeration>
                                        <xs:enumeration value="PostConMeeting">
                                          <xs:annotation>
                                            <xs:documentation xml:lang="en">Indicates a post-convention meeting, typically between the event planner and the facility.</xs:documentation>
                                          </xs:annotation>
                                        </xs:enumeration>
                                        <xs:enumeration value="MajorArrival">
                                          <xs:annotation>
                                            <xs:documentation xml:lang="en">The major arrival date(s) for the group.</xs:documentation>
                                          </xs:annotation>
                                        </xs:enumeration>
                                        <xs:enumeration value="MajorDeparture">
                                          <xs:annotation>
                                            <xs:documentation xml:lang="en">The major departure date(s) for the group.</xs:documentation>
                                          </xs:annotation>
                                        </xs:enumeration>
                                        <xs:enumeration value="Published">
                                          <xs:annotation>
                                            <xs:documentation xml:lang="en">The published event date(s) for the group.</xs:documentation>
                                          </xs:annotation>
                                        </xs:enumeration>
                                        <xs:enumeration value="GroupArrival">
                                          <xs:annotation>
                                            <xs:documentation xml:lang="en">The date associated with the arrival of the first contingent of the group.</xs:documentation>
                                          </xs:annotation>
                                        </xs:enumeration>
                                        <xs:enumeration value="GroupDeparture">
                                          <xs:annotation>
                                            <xs:documentation xml:lang="en">The date associated with the departure of the last contingent of the group.</xs:documentation>
                                          </xs:annotation>
                                        </xs:enumeration>
                                      </xs:restriction>
                                    </xs:simpleType>
                                  </xs:attribute>
                                  <xs:attribute name="Name" type="StringLength1to64" use="optional">
                                    <xs:annotation>
                                      <xs:documentation xml:lang="en">The name of a group or meeting to which the dates apply.</xs:documentation>
                                    </xs:annotation>
                                  </xs:attribute>
                                  <xs:attribute name="LocationName" type="StringLength1to64" use="optional">
                                    <xs:annotation>
                                      <xs:documentation xml:lang="en">The location of the meeting to which the dates apply.</xs:documentation>
                                    </xs:annotation>
                                  </xs:attribute>
                                  <xs:attribute name="AttendeeQuantity" type="xs:nonNegativeInteger" use="optional">
                                    <xs:annotation>
                                      <xs:documentation xml:lang="en">The number of people to whom the dates apply.</xs:documentation>
                                    </xs:annotation>
                                  </xs:attribute>
                                </xs:complexType>
                              </xs:element>
                            </xs:sequence>
                          </xs:complexType>
                        </xs:element>
                        <xs:element name="Contacts" minOccurs="0">
                          <xs:annotation>
                            <xs:documentation xml:lang="en">A collection of contacts that pertain to this occurence of the event.  </xs:documentation>
                          </xs:annotation>
                          <xs:complexType>
                            <xs:sequence>
                              <xs:element name="Contact" maxOccurs="99">
                                <xs:annotation>
                                  <xs:documentation xml:lang="en">A contact that pertains to this occurence of the event. </xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                  <xs:complexContent>
                                    <xs:extension base="ContactPersonType">
                                      <xs:sequence>
                                        <xs:element name="Amenities" minOccurs="0">
                                          <xs:annotation>
                                            <xs:documentation xml:lang="en">A collection of amenities.</xs:documentation>
                                          </xs:annotation>
                                          <xs:complexType>
                                            <xs:sequence>
                                              <xs:element name="Amenity" type="RoomAmenityPrefType" maxOccurs="99">
                                                <xs:annotation>
                                                  <xs:documentation xml:lang="en">An amenity for the contact.</xs:documentation>
                                                </xs:annotation>
                                              </xs:element>
                                            </xs:sequence>
                                          </xs:complexType>
                                        </xs:element>
                                        <xs:element name="Comments" minOccurs="0">
                                          <xs:annotation>
                                            <xs:documentation xml:lang="en">A collection of comments.</xs:documentation>
                                          </xs:annotation>
                                          <xs:complexType>
                                            <xs:sequence>
                                              <xs:element name="Comment" type="ParagraphType" maxOccurs="99">
                                                <xs:annotation>
                                                  <xs:documentation xml:lang="en">Used to provide additional information about the contact or the contact organization.</xs:documentation>
                                                </xs:annotation>
                                              </xs:element>
                                            </xs:sequence>
                                          </xs:complexType>
                                        </xs:element>
                                      </xs:sequence>
                                      <xs:attribute name="VIP_Indicator" type="xs:boolean" use="optional">
                                        <xs:annotation>
                                          <xs:documentation xml:lang="en">When true, the contact is a very important person.</xs:documentation>
                                        </xs:annotation>
                                      </xs:attribute>
                                      <xs:attribute name="ArrivalDate" type="DateOrTimeOrDateTimeType" use="optional">
                                        <xs:annotation>
                                          <xs:documentation xml:lang="en">The arrival date of this contact.</xs:documentation>
                                        </xs:annotation>
                                      </xs:attribute>
                                      <xs:attribute name="DepartureDate" type="DateOrTimeOrDateTimeType" use="optional">
                                        <xs:annotation>
                                          <xs:documentation xml:lang="en">The departure date of this contact.</xs:documentation>
                                        </xs:annotation>
                                      </xs:attribute>
                                    </xs:extension>
                                  </xs:complexContent>
                                </xs:complexType>
                              </xs:element>
                            </xs:sequence>
                          </xs:complexType>
                        </xs:element>
                        <xs:element name="Exhibition" minOccurs="0" maxOccurs="5">
                          <xs:annotation>
                            <xs:documentation xml:lang="en">Describes an event at which products and services are displayed.</xs:documentation>
                          </xs:annotation>
                          <xs:complexType>
                            <xs:sequence>
                              <xs:element name="ExhibitDetails" minOccurs="0">
                                <xs:annotation>
                                  <xs:documentation xml:lang="en">A collection of exhibit details.</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                  <xs:sequence>
                                    <xs:element name="ExhibitDetail" maxOccurs="unbounded">
                                      <xs:annotation>
                                        <xs:documentation xml:lang="en">Describes a single exhibit within an exhibitiion.</xs:documentation>
                                      </xs:annotation>
                                      <xs:complexType>
                                        <xs:sequence>
                                          <xs:element name="Contacts" minOccurs="0">
                                            <xs:annotation>
                                              <xs:documentation xml:lang="en">A collection of contacts for an exhibit.</xs:documentation>
                                            </xs:annotation>
                                            <xs:complexType>
                                              <xs:sequence>
                                                <xs:element name="Contact" type="ContactPersonType" maxOccurs="99">
                                                  <xs:annotation>
                                                    <xs:documentation xml:lang="en">Contact information pertaining to an exhibit.</xs:documentation>
                                                  </xs:annotation>
                                                </xs:element>
                                              </xs:sequence>
                                            </xs:complexType>
                                          </xs:element>
                                          <xs:element name="TaxExemptInfo" minOccurs="0" maxOccurs="3">
                                            <xs:annotation>
                                              <xs:documentation xml:lang="en">Used to provide tax exemption information for the exhibiting company of the booth.</xs:documentation>
                                            </xs:annotation>
                                            <xs:complexType>
                                              <xs:sequence>
                                                <xs:element name="Comments" minOccurs="0">
                                                  <xs:annotation>
                                                    <xs:documentation xml:lang="en">A collection of comments pertaining to the tax exemption information.</xs:documentation>
                                                  </xs:annotation>
                                                  <xs:complexType>
                                                    <xs:sequence>
                                                      <xs:element name="Comment" type="ParagraphType" maxOccurs="99">
                                                        <xs:annotation>
                                                          <xs:documentation xml:lang="en">A comment about the tax exemption information. </xs:documentation>
                                                        </xs:annotation>
                                                      </xs:element>
                                                    </xs:sequence>
                                                  </xs:complexType>
                                                </xs:element>
                                              </xs:sequence>
                                              <xs:attributeGroup ref="TaxExemptGroup">
                                                <xs:annotation>
                                                  <xs:documentation xml:lang="en">Tax exempt information for the exhibiting company of the booth.</xs:documentation>
                                                </xs:annotation>
                                              </xs:attributeGroup>
                                            </xs:complexType>
                                          </xs:element>
                                        </xs:sequence>
                                        <xs:attribute name="ExhibitTypeCode" type="OTA_CodeType" use="optional">
                                          <xs:annotation>
                                            <xs:documentation xml:lang="en">Type of booths for the entire exhibition. Refer to OpenTravel Code Table Exhibit Type (EXH).</xs:documentation>
                                          </xs:annotation>
                                        </xs:attribute>
                                        <xs:attribute name="BoothNumber" type="StringLength1to32" use="optional">
                                          <xs:annotation>
                                            <xs:documentation xml:lang="en">The exhibit booth number.</xs:documentation>
                                          </xs:annotation>
                                        </xs:attribute>
                                        <xs:attributeGroup ref="DimensionGroup">
                                          <xs:annotation>
                                            <xs:documentation xml:lang="en">The dimensions of the booth. </xs:documentation>
                                          </xs:annotation>
                                        </xs:attributeGroup>
                                      </xs:complexType>
                                    </xs:element>
                                  </xs:sequence>
                                </xs:complexType>
                              </xs:element>
                              <xs:element name="ExhibitorInfo" minOccurs="0">
                                <xs:annotation>
                                  <xs:documentation xml:lang="en">Exhibitor (i.e., the people working the booths) information for the event.</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                  <xs:sequence>
                                    <xs:element name="Comments" minOccurs="0">
                                      <xs:annotation>
                                        <xs:documentation xml:lang="en">A collection of comments pertaining to the exhibitors.</xs:documentation>
                                      </xs:annotation>
                                      <xs:complexType>
                                        <xs:sequence>
                                          <xs:element name="Comment" type="ParagraphType" maxOccurs="99">
                                            <xs:annotation>
                                              <xs:documentation xml:lang="en">A comment about the exhibitors as a group (e.g., accessibility or special needs or demographic profile comments).</xs:documentation>
                                            </xs:annotation>
                                          </xs:element>
                                        </xs:sequence>
                                      </xs:complexType>
                                    </xs:element>
                                  </xs:sequence>
                                  <xs:attributeGroup ref="AttendanceGroup">
                                    <xs:annotation>
                                      <xs:documentation xml:lang="en">Attendance information for the exhibitors.</xs:documentation>
                                    </xs:annotation>
                                  </xs:attributeGroup>
                                </xs:complexType>
                              </xs:element>
                              <xs:element name="AdditionalDates" minOccurs="0">
                                <xs:annotation>
                                  <xs:documentation xml:lang="en">A collection of additional dates.</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                  <xs:sequence>
                                    <xs:element name="AdditionalDate" maxOccurs="unbounded">
                                      <xs:annotation>
                                        <xs:documentation xml:lang="en">Date information petaining to the exhibition.</xs:documentation>
                                      </xs:annotation>
                                      <xs:complexType>
                                        <xs:attributeGroup ref="DateTimeSpanGroup">
                                          <xs:annotation>
                                            <xs:documentation xml:lang="en">The start and end dates associated with the Type.</xs:documentation>
                                          </xs:annotation>
                                        </xs:attributeGroup>
                                        <xs:attribute name="Type" use="optional">
                                          <xs:annotation>
                                            <xs:documentation xml:lang="en">Defines the type of dates (e.g., contacted, exhibitor move in, exhibitor move out).</xs:documentation>
                                          </xs:annotation>
                                          <xs:simpleType>
                                            <xs:restriction base="xs:NMTOKEN">
                                              <xs:enumeration value="Contracted">
                                                <xs:annotation>
                                                  <xs:documentation xml:lang="en">The contracted dates for the exhibition.</xs:documentation>
                                                </xs:annotation>
                                              </xs:enumeration>
                                              <xs:enumeration value="ContractorMoveIn">
                                                <xs:annotation>
                                                  <xs:documentation xml:lang="en">The contractor move-in date for the exhibition.</xs:documentation>
                                                </xs:annotation>
                                              </xs:enumeration>
                                              <xs:enumeration value="ContractorMoveOut">
                                                <xs:annotation>
                                                  <xs:documentation xml:lang="en">The contractor move-out date for the exhibition.</xs:documentation>
                                                </xs:annotation>
                                              </xs:enumeration>
                                              <xs:enumeration value="ExhibitorMoveIn">
                                                <xs:annotation>
                                                  <xs:documentation xml:lang="en">The exhibitor move-in date for the exhibition.</xs:documentation>
                                                </xs:annotation>
                                              </xs:enumeration>
                                              <xs:enumeration value="ExhibitorMoveOut">
                                                <xs:annotation>
                                                  <xs:documentation xml:lang="en">The exhibitor move-out date for the exhibition.</xs:documentation>
                                                </xs:annotation>
                                              </xs:enumeration>
                                            </xs:restriction>
                                          </xs:simpleType>
                                        </xs:attribute>
                                      </xs:complexType>
                                    </xs:element>
                                  </xs:sequence>
                                </xs:complexType>
                              </xs:element>
                              <xs:element name="Comments" minOccurs="0">
                                <xs:annotation>
                                  <xs:documentation xml:lang="en">A collection of comments pertaining to the exhibition.</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                  <xs:sequence>
                                    <xs:element name="Comment" type="ParagraphType" maxOccurs="99">
                                      <xs:annotation>
                                        <xs:documentation xml:lang="en">A comment pertaining to the exhibition.</xs:documentation>
                                      </xs:annotation>
                                    </xs:element>
                                  </xs:sequence>
                                </xs:complexType>
                              </xs:element>
                            </xs:sequence>
                            <xs:attribute name="Type" use="optional">
                              <xs:annotation>
                                <xs:documentation xml:lang="en">Used to designate the exhibition as public, private or public/private.</xs:documentation>
                              </xs:annotation>
                              <xs:simpleType>
                                <xs:restriction base="xs:NMTOKEN">
                                  <xs:enumeration value="Public">
                                    <xs:annotation>
                                      <xs:documentation xml:lang="en">Indicates the exhibition is open to the public.</xs:documentation>
                                    </xs:annotation>
                                  </xs:enumeration>
                                  <xs:enumeration value="Private">
                                    <xs:annotation>
                                      <xs:documentation xml:lang="en">Indicates the exhibition is private.</xs:documentation>
                                    </xs:annotation>
                                  </xs:enumeration>
                                  <xs:enumeration value="PublicPrivate">
                                    <xs:annotation>
                                      <xs:documentation xml:lang="en">Portions of the exhibition are open to the public and portions of the exhibition are private.</xs:documentation>
                                    </xs:annotation>
                                  </xs:enumeration>
                                </xs:restriction>
                              </xs:simpleType>
                            </xs:attribute>
                            <xs:attribute name="KitDistributionCode" type="OTA_CodeType" use="optional">
                              <xs:annotation>
                                <xs:documentation xml:lang="en">Describes how an exhibitor kit will be distributed (e.g. e-mail, fax). Refer to OpenTravel Code table Distribution Type (DTB).</xs:documentation>
                              </xs:annotation>
                            </xs:attribute>
                            <xs:attribute name="KitFormatCode" type="OTA_CodeType" use="optional">
                              <xs:annotation>
                                <xs:documentation xml:lang="en">Describes the format of the exhibitor kit (e.g. hardcopy, CDROM). Refer to OpenTravel Code table Format (FMT)</xs:documentation>
                              </xs:annotation>
                            </xs:attribute>
                            <xs:attribute name="GeneralServiceContractorInd" type="xs:boolean" use="optional">
                              <xs:annotation>
                                <xs:documentation xml:lang="en">When true, a general service contractor (GSC) has been selected. If false, no GSC has been selected.</xs:documentation>
                              </xs:annotation>
                            </xs:attribute>
                            <xs:attribute name="GrossExhibitionSpace" type="xs:nonNegativeInteger" use="optional">
                              <xs:annotation>
                                <xs:documentation xml:lang="en">The gross exhibition space required for the event.</xs:documentation>
                              </xs:annotation>
                            </xs:attribute>
                            <xs:attribute name="NetExhibitionSpace" type="xs:nonNegativeInteger" use="optional">
                              <xs:annotation>
                                <xs:documentation xml:lang="en">The net exhibition space required for the event.</xs:documentation>
                              </xs:annotation>
                            </xs:attribute>
                            <xs:attribute name="UnitOfMeasureCode" type="OTA_CodeType" use="optional">
                              <xs:annotation>
                                <xs:documentation xml:lang="en">The unit of measure (e.g., square feet, square meters) in a code format . Refer to OpenTravel Code List Unit of Measure Code (UOM).</xs:documentation>
                              </xs:annotation>
                            </xs:attribute>
                            <xs:attribute name="ExhibitQuantity" type="xs:nonNegativeInteger" use="optional">
                              <xs:annotation>
                                <xs:documentation xml:lang="en">The number of exhibits expected.</xs:documentation>
                              </xs:annotation>
                            </xs:attribute>
                            <xs:attribute name="CompanyQuantity" type="xs:nonNegativeInteger" use="optional">
                              <xs:annotation>
                                <xs:documentation xml:lang="en">The number of exhibiting companies expected.</xs:documentation>
                              </xs:annotation>
                            </xs:attribute>
                            <xs:attribute name="SecuredAreaIndicator" type="xs:boolean" use="optional">
                              <xs:annotation>
                                <xs:documentation xml:lang="en">When true, indicates the area needs to be secured. When false, the area does not need to be secured.</xs:documentation>
                              </xs:annotation>
                            </xs:attribute>
                          </xs:complexType>
                        </xs:element>
                        <xs:element name="Transportations" type="TransportationType" minOccurs="0">
                          <xs:annotation>
                            <xs:documentation xml:lang="en">A collection of transportation elements.</xs:documentation>
                          </xs:annotation>
                        </xs:element>
                        <xs:element name="TaxExemptInfo" minOccurs="0" maxOccurs="3">
                          <xs:annotation>
                            <xs:documentation xml:lang="en">Used to provide tax exemption information for the event.</xs:documentation>
                          </xs:annotation>
                          <xs:complexType>
                            <xs:sequence>
                              <xs:element name="Comments" minOccurs="0">
                                <xs:annotation>
                                  <xs:documentation xml:lang="en">A collection of comments pertaining to the tax exemption information.</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                  <xs:sequence>
                                    <xs:element name="Comment" type="ParagraphType" maxOccurs="unbounded">
                                      <xs:annotation>
                                        <xs:documentation xml:lang="en">Used for comments about the billing information.</xs:documentation>
                                      </xs:annotation>
                                    </xs:element>
                                  </xs:sequence>
                                </xs:complexType>
                              </xs:element>
                            </xs:sequence>
                            <xs:attributeGroup ref="TaxExemptGroup">
                              <xs:annotation>
                                <xs:documentation xml:lang="en">Tax exemption information for the event.</xs:documentation>
                              </xs:annotation>
                            </xs:attributeGroup>
                          </xs:complexType>
                        </xs:element>
                        <xs:element name="Comments" minOccurs="0">
                          <xs:annotation>
                            <xs:documentation xml:lang="en">A collection of comments that pertain to this occurrence of the event.</xs:documentation>
                          </xs:annotation>
                          <xs:complexType>
                            <xs:sequence>
                              <xs:element name="Comment" type="ParagraphType" minOccurs="0" maxOccurs="unbounded">
                                <xs:annotation>
                                  <xs:documentation xml:lang="en">A comment that pertains to this occurrence of the event.</xs:documentation>
                                </xs:annotation>
                              </xs:element>
                            </xs:sequence>
                          </xs:complexType>
                        </xs:element>
                      </xs:sequence>
                      <xs:attributeGroup ref="DateTimeSpanGroup">
                        <xs:annotation>
                          <xs:documentation xml:lang="en">The start of the earliest function and the end of the latest function (excludes pre-convention and post-convention meetings).</xs:documentation>
                        </xs:annotation>
                      </xs:attributeGroup>
                    </xs:complexType>
                  </xs:element>
                </xs:sequence>
              </xs:complexType>
            </xs:element>
            <xs:element name="Sites" minOccurs="0">
              <xs:annotation>
                <xs:documentation xml:lang="en">A collection of sites.</xs:documentation>
              </xs:annotation>
              <xs:complexType>
                <xs:sequence>
                  <xs:element name="Site" maxOccurs="99">
                    <xs:annotation>
                      <xs:documentation xml:lang="en">Identifies the site where functions for the event will be held.</xs:documentation>
                    </xs:annotation>
                    <xs:complexType>
                      <xs:sequence>
                        <xs:element name="Contacts" minOccurs="0">
                          <xs:annotation>
                            <xs:documentation xml:lang="en">A collection of contacts associated with a specific site.</xs:documentation>
                          </xs:annotation>
                          <xs:complexType>
                            <xs:sequence>
                              <xs:element name="Contact" type="ContactPersonType" maxOccurs="99">
                                <xs:annotation>
                                  <xs:documentation xml:lang="en">Contact information as well as site address information.</xs:documentation>
                                </xs:annotation>
                              </xs:element>
                            </xs:sequence>
                          </xs:complexType>
                        </xs:element>
                        <xs:element name="RoomBlock" minOccurs="0" maxOccurs="99">
                          <xs:annotation>
                            <xs:documentation xml:lang="en">Information pertaining to room requirements for this event.</xs:documentation>
                          </xs:annotation>
                          <xs:complexType>
                            <xs:sequence>
                              <xs:element name="ReservationMethod" minOccurs="0" maxOccurs="5">
                                <xs:annotation>
                                  <xs:documentation xml:lang="en">Information regarding the reservation method.</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                  <xs:attribute name="Code" type="OTA_CodeType" use="optional">
                                    <xs:annotation>
                                      <xs:documentation xml:lang="en">The type of reservation method to be used. Refer to OpenTravel Code list Reservation Method Code (RMC).</xs:documentation>
                                    </xs:annotation>
                                  </xs:attribute>
                                </xs:complexType>
                              </xs:element>
                              <xs:element name="StayDays" minOccurs="0">
                                <xs:annotation>
                                  <xs:documentation xml:lang="en">A collection of stay days.</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                  <xs:sequence>
                                    <xs:element name="StayDay" maxOccurs="unbounded">
                                      <xs:annotation>
                                        <xs:documentation xml:lang="en">A particular stay day.</xs:documentation>
                                      </xs:annotation>
                                      <xs:complexType>
                                        <xs:attribute name="DayNumber" type="xs:positiveInteger" use="required">
                                          <xs:annotation>
                                            <xs:documentation xml:lang="en">The number which indicates the day of the room requirements (e.g. 1, 2, 3). This is relative to the Start attribute in the EventInfo element.</xs:documentation>
                                          </xs:annotation>
                                        </xs:attribute>
                                        <xs:attribute name="GuestQuantity" type="xs:nonNegativeInteger" use="optional">
                                          <xs:annotation>
                                            <xs:documentation xml:lang="en">Total number of guests for this day.</xs:documentation>
                                          </xs:annotation>
                                        </xs:attribute>
                                        <xs:attribute name="TotalNumberOfUnits" type="xs:integer" use="optional">
                                          <xs:annotation>
                                            <xs:documentation xml:lang="en">Total number of rooms needed for this day.</xs:documentation>
                                          </xs:annotation>
                                        </xs:attribute>
                                      </xs:complexType>
                                    </xs:element>
                                    <xs:element name="Comments" minOccurs="0">
                                      <xs:annotation>
                                        <xs:documentation xml:lang="en">A collection of comments pertaining to the stay. </xs:documentation>
                                      </xs:annotation>
                                      <xs:complexType>
                                        <xs:sequence>
                                          <xs:element name="Comment" type="ParagraphType" maxOccurs="99">
                                            <xs:annotation>
                                              <xs:documentation xml:lang="en">A comment pertaining to the stay. </xs:documentation>
                                            </xs:annotation>
                                          </xs:element>
                                        </xs:sequence>
                                      </xs:complexType>
                                    </xs:element>
                                  </xs:sequence>
                                  <xs:attribute name="FirstStayDayOfWeek" type="DayOfWeekType" use="optional">
                                    <xs:annotation>
                                      <xs:documentation xml:lang="en">Identifies the first day of the week of the room block.</xs:documentation>
                                    </xs:annotation>
                                  </xs:attribute>
                                </xs:complexType>
                              </xs:element>
                            </xs:sequence>
                            <xs:attribute name="TotalRoomNightQuantity" type="xs:nonNegativeInteger" use="optional">
                              <xs:annotation>
                                <xs:documentation xml:lang="en">Total number of room nights needed. </xs:documentation>
                              </xs:annotation>
                            </xs:attribute>
                            <xs:attribute name="PeakRoomNightQuantity" type="xs:nonNegativeInteger" use="optional">
                              <xs:annotation>
                                <xs:documentation xml:lang="en">Total number of rooms needed on the peak night. </xs:documentation>
                              </xs:annotation>
                            </xs:attribute>
                            <xs:attribute name="ConcessionsIndicator" type="xs:boolean" use="optional">
                              <xs:annotation>
                                <xs:documentation xml:lang="en">When true, indicates that a rebate, assessment or commission exists. When false, rebates, assessments or commissions do not exist.</xs:documentation>
                              </xs:annotation>
                            </xs:attribute>
                            <xs:attribute name="HousingProviderName" type="StringLength1to255" use="optional">
                              <xs:annotation>
                                <xs:documentation xml:lang="en">A third party housing providor name.</xs:documentation>
                              </xs:annotation>
                            </xs:attribute>
                          </xs:complexType>
                        </xs:element>
                        <xs:element name="EventDays" minOccurs="0">
                          <xs:annotation>
                            <xs:documentation xml:lang="en">A collection of event days.</xs:documentation>
                          </xs:annotation>
                          <xs:complexType>
                            <xs:sequence>
                              <xs:element name="EventDay" maxOccurs="unbounded">
                                <xs:annotation>
                                  <xs:documentation xml:lang="en">Defines the functions for a specific day of the event.</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                  <xs:sequence>
                                    <xs:element name="EventDayFunctions" minOccurs="0">
                                      <xs:annotation>
                                        <xs:documentation xml:lang="en">A collection of multiple EventDayFunctions.</xs:documentation>
                                      </xs:annotation>
                                      <xs:complexType>
                                        <xs:sequence>
                                          <xs:element name="EventDayFunction" maxOccurs="unbounded">
                                            <xs:annotation>
                                              <xs:documentation xml:lang="en">A container for the events requirements data.</xs:documentation>
                                            </xs:annotation>
                                            <xs:complexType>
                                              <xs:sequence minOccurs="0">
                                                <xs:element name="Contacts" minOccurs="0">
                                                  <xs:annotation>
                                                    <xs:documentation xml:lang="en">A collection of contacts associated with a function</xs:documentation>
                                                  </xs:annotation>
                                                  <xs:complexType>
                                                    <xs:sequence>
                                                      <xs:element name="Contact" maxOccurs="unbounded">
                                                        <xs:annotation>
                                                          <xs:documentation xml:lang="en">A contact associated with a function.</xs:documentation>
                                                        </xs:annotation>
                                                        <xs:complexType>
                                                          <xs:complexContent>
                                                            <xs:extension base="ContactPersonType">
                                                              <xs:sequence>
                                                                <xs:element name="Comments" minOccurs="0">
                                                                  <xs:annotation>
                                                                    <xs:documentation xml:lang="en">A collection of comments.</xs:documentation>
                                                                  </xs:annotation>
                                                                  <xs:complexType>
                                                                    <xs:sequence>
                                                                      <xs:element name="Comment" type="ParagraphType" maxOccurs="unbounded">
                                                                        <xs:annotation>
                                                                          <xs:documentation xml:lang="en">A comment associated to the contact.</xs:documentation>
                                                                        </xs:annotation>
                                                                      </xs:element>
                                                                    </xs:sequence>
                                                                  </xs:complexType>
                                                                </xs:element>
                                                              </xs:sequence>
                                                            </xs:extension>
                                                          </xs:complexContent>
                                                        </xs:complexType>
                                                      </xs:element>
                                                    </xs:sequence>
                                                  </xs:complexType>
                                                </xs:element>
                                                <xs:element name="AudioVisuals" minOccurs="0">
                                                  <xs:annotation>
                                                    <xs:documentation xml:lang="en">A collection of audio visual needs for a function. </xs:documentation>
                                                  </xs:annotation>
                                                  <xs:complexType>
                                                    <xs:sequence>
                                                      <xs:element name="AudioVisual" maxOccurs="unbounded">
                                                        <xs:annotation>
                                                          <xs:documentation xml:lang="en">Defines a specific audio visual need for a function.</xs:documentation>
                                                        </xs:annotation>
                                                        <xs:complexType>
                                                          <xs:sequence>
                                                            <xs:element name="Comments" minOccurs="0">
                                                              <xs:annotation>
                                                                <xs:documentation xml:lang="en">A collection of comments associated with the audio visual item.</xs:documentation>
                                                              </xs:annotation>
                                                              <xs:complexType>
                                                                <xs:sequence>
                                                                  <xs:element name="Comment" type="ParagraphType" maxOccurs="99">
                                                                    <xs:annotation>
                                                                      <xs:documentation xml:lang="en">A comment associated with the audio visual item.</xs:documentation>
                                                                    </xs:annotation>
                                                                  </xs:element>
                                                                </xs:sequence>
                                                              </xs:complexType>
                                                            </xs:element>
                                                          </xs:sequence>
                                                          <xs:attribute name="Code" type="OTA_CodeType" use="optional">
                                                            <xs:annotation>
                                                              <xs:documentation xml:lang="en">The specific audio visual requirement being defined.  Refer to OpenTravel Code Table Meeting Room code (MRC).</xs:documentation>
                                                            </xs:annotation>
                                                          </xs:attribute>
                                                          <xs:attribute name="AudioVisualPref" type="PreferLevelType" use="optional">
                                                            <xs:annotation>
                                                              <xs:documentation xml:lang="en">Defines the preference for the audio visual item (e.g., preferred, required).</xs:documentation>
                                                            </xs:annotation>
                                                          </xs:attribute>
                                                          <xs:attributeGroup ref="QuantityGroup">
                                                            <xs:annotation>
                                                              <xs:documentation xml:lang="en">The number of this audio visual item.</xs:documentation>
                                                            </xs:annotation>
                                                          </xs:attributeGroup>
                                                          <xs:attributeGroup ref="CurrencyAmountGroup">
                                                            <xs:annotation>
                                                              <xs:documentation xml:lang="en">The amount charged for each audio visual item.</xs:documentation>
                                                            </xs:annotation>
                                                          </xs:attributeGroup>
                                                        </xs:complexType>
                                                      </xs:element>
                                                    </xs:sequence>
                                                    <xs:attributeGroup ref="ProviderGroup">
                                                      <xs:annotation>
                                                        <xs:documentation xml:lang="en">Identifies the audio visual provider.</xs:documentation>
                                                      </xs:annotation>
                                                    </xs:attributeGroup>
                                                  </xs:complexType>
                                                </xs:element>
                                                <xs:element name="RoomSetup" minOccurs="0">
                                                  <xs:annotation>
                                                    <xs:documentation xml:lang="en">The room setup requirements for the function.</xs:documentation>
                                                  </xs:annotation>
                                                  <xs:complexType>
                                                    <xs:sequence>
                                                      <xs:element name="SetupDetails" minOccurs="0">
                                                        <xs:annotation>
                                                          <xs:documentation xml:lang="en">A collection of setup requirements for the function.</xs:documentation>
                                                        </xs:annotation>
                                                        <xs:complexType>
                                                          <xs:sequence>
                                                            <xs:element name="SetupDetail" maxOccurs="unbounded">
                                                              <xs:annotation>
                                                                <xs:documentation xml:lang="en">A setup requirement for the function.</xs:documentation>
                                                              </xs:annotation>
                                                              <xs:complexType>
                                                                <xs:sequence>
                                                                  <xs:element name="Comments" minOccurs="0">
                                                                    <xs:annotation>
                                                                      <xs:documentation xml:lang="en">A collection of comments associated with the function setup.</xs:documentation>
                                                                    </xs:annotation>
                                                                    <xs:complexType>
                                                                      <xs:sequence>
                                                                        <xs:element name="Comment" type="ParagraphType" maxOccurs="99">
                                                                          <xs:annotation>
                                                                            <xs:documentation xml:lang="en">A comment associated with the function setup.</xs:documentation>
                                                                          </xs:annotation>
                                                                        </xs:element>
                                                                      </xs:sequence>
                                                                    </xs:complexType>
                                                                  </xs:element>
                                                                </xs:sequence>
                                                                <xs:attributeGroup ref="DimensionGroup">
                                                                  <xs:annotation>
                                                                    <xs:documentation xml:lang="en">The dimensions of the item in this setup detail.</xs:documentation>
                                                                  </xs:annotation>
                                                                </xs:attributeGroup>
                                                                <xs:attribute name="Code" type="OTA_CodeType" use="optional">
                                                                  <xs:annotation>
                                                                    <xs:documentation xml:lang="en">Indicates additional details of the function setup  (e.g. risers, perimeter seating). Refer to OpenTravel Code List Meeting Room Code (MRC).</xs:documentation>
                                                                  </xs:annotation>
                                                                </xs:attribute>
                                                                <xs:attributeGroup ref="QuantityGroup">
                                                                  <xs:annotation>
                                                                    <xs:documentation xml:lang="en">The number of this item needed. </xs:documentation>
                                                                  </xs:annotation>
                                                                </xs:attributeGroup>
                                                              </xs:complexType>
                                                            </xs:element>
                                                          </xs:sequence>
                                                        </xs:complexType>
                                                      </xs:element>
                                                      <xs:element name="Diagram" type="ParagraphType" minOccurs="0">
                                                        <xs:annotation>
                                                          <xs:documentation xml:lang="en">A diagram of the room setup.</xs:documentation>
                                                        </xs:annotation>
                                                      </xs:element>
                                                    </xs:sequence>
                                                    <xs:attribute name="RoomSetupCode" type="OTA_CodeType" use="optional">
                                                      <xs:annotation>
                                                        <xs:documentation xml:lang="en">Indicates the arrangement of the function space (e.g. conference or banquet style). Refer to OpenTravel Code List Meeting Room Format Code (MRF).</xs:documentation>
                                                      </xs:annotation>
                                                    </xs:attribute>
                                                    <xs:attribute name="SetForQuantity" type="xs:nonNegativeInteger" use="optional">
                                                      <xs:annotation>
                                                        <xs:documentation xml:lang="en">The number of people for whom the room should be set.</xs:documentation>
                                                      </xs:annotation>
                                                    </xs:attribute>
                                                    <xs:attribute name="SetByTime" type="DateOrTimeOrDateTimeType" use="optional">
                                                      <xs:annotation>
                                                        <xs:documentation xml:lang="en">The time or date and time by which the function space must be set up.</xs:documentation>
                                                      </xs:annotation>
                                                    </xs:attribute>
                                                    <xs:attribute name="DismantleByTime" type="DateOrTimeOrDateTimeType" use="optional">
                                                      <xs:annotation>
                                                        <xs:documentation xml:lang="en">The time or date and time by which the function space must be broken down.</xs:documentation>
                                                      </xs:annotation>
                                                    </xs:attribute>
                                                  </xs:complexType>
                                                </xs:element>
                                                <xs:element name="SessionTimes" minOccurs="0" maxOccurs="5">
                                                  <xs:annotation>
                                                    <xs:documentation xml:lang="en">The start and end times for a session or show within a function.</xs:documentation>
                                                  </xs:annotation>
                                                  <xs:complexType>
                                                    <xs:attribute name="StartTime" type="xs:time" use="optional">
                                                      <xs:annotation>
                                                        <xs:documentation xml:lang="en">The time a session is scheduled to begin.</xs:documentation>
                                                      </xs:annotation>
                                                    </xs:attribute>
                                                    <xs:attribute name="EndTime" type="xs:time" use="optional">
                                                      <xs:annotation>
                                                        <xs:documentation xml:lang="en">The time a session is scheduled to finish.</xs:documentation>
                                                      </xs:annotation>
                                                    </xs:attribute>
                                                  </xs:complexType>
                                                </xs:element>
                                                <xs:element name="FoodAndBeverages" minOccurs="0">
                                                  <xs:annotation>
                                                    <xs:documentation xml:lang="en">A collection of food and beverage details for a function.</xs:documentation>
                                                  </xs:annotation>
                                                  <xs:complexType>
                                                    <xs:sequence>
                                                      <xs:element name="FoodAndBeverage" maxOccurs="unbounded">
                                                        <xs:annotation>
                                                          <xs:documentation xml:lang="en">Food and beverage details for a function.</xs:documentation>
                                                        </xs:annotation>
                                                        <xs:complexType>
                                                          <xs:sequence>
                                                            <xs:element name="Comments" minOccurs="0">
                                                              <xs:annotation>
                                                                <xs:documentation xml:lang="en">A collection of comments associated with the food and beverage requirement.</xs:documentation>
                                                              </xs:annotation>
                                                              <xs:complexType>
                                                                <xs:sequence>
                                                                  <xs:element name="Comment" type="ParagraphType" maxOccurs="99">
                                                                    <xs:annotation>
                                                                      <xs:documentation xml:lang="en">A comment associated with the food and beverage requirement.</xs:documentation>
                                                                    </xs:annotation>
                                                                  </xs:element>
                                                                </xs:sequence>
                                                              </xs:complexType>
                                                            </xs:element>
                                                            <xs:element name="Menus" minOccurs="0">
                                                              <xs:annotation>
                                                                <xs:documentation xml:lang="en">A collection of menus.</xs:documentation>
                                                              </xs:annotation>
                                                              <xs:complexType>
                                                                <xs:sequence>
                                                                  <xs:element name="Menu" maxOccurs="99">
                                                                    <xs:annotation>
                                                                      <xs:documentation xml:lang="en">Menu details.</xs:documentation>
                                                                    </xs:annotation>
                                                                    <xs:complexType>
                                                                      <xs:sequence>
                                                                        <xs:element name="Comments" minOccurs="0">
                                                                          <xs:annotation>
                                                                            <xs:documentation xml:lang="en">A collection of comments associated with the menu requirement.</xs:documentation>
                                                                          </xs:annotation>
                                                                          <xs:complexType>
                                                                            <xs:sequence>
                                                                              <xs:element name="Comment" type="ParagraphType" maxOccurs="99">
                                                                                <xs:annotation>
                                                                                  <xs:documentation xml:lang="en">A comment associated with the menu requirement.</xs:documentation>
                                                                                </xs:annotation>
                                                                              </xs:element>
                                                                            </xs:sequence>
                                                                          </xs:complexType>
                                                                        </xs:element>
                                                                      </xs:sequence>
                                                                      <xs:attribute name="Name" type="StringLength1to128" use="optional">
                                                                        <xs:annotation>
                                                                          <xs:documentation xml:lang="en">The name of a set menu or menu item.</xs:documentation>
                                                                        </xs:annotation>
                                                                      </xs:attribute>
                                                                      <xs:attributeGroup ref="CurrencyAmountGroup">
                                                                        <xs:annotation>
                                                                          <xs:documentation xml:lang="en">The charge for this menu or menu item.</xs:documentation>
                                                                        </xs:annotation>
                                                                      </xs:attributeGroup>
                                                                      <xs:attribute name="ChargeUnit" type="OTA_CodeType" use="optional">
                                                                        <xs:annotation>
                                                                          <xs:documentation xml:lang="en">Defines how the charge is applied (e.g. per person, per gallon, per tray). Use OpenTravel Code list Charge Type (CHG).</xs:documentation>
                                                                        </xs:annotation>
                                                                      </xs:attribute>
                                                                      <xs:attribute name="Quantity" type="xs:decimal" use="optional">
                                                                        <xs:annotation>
                                                                          <xs:documentation xml:lang="en">The requested quantity for this menu or menu item.</xs:documentation>
                                                                        </xs:annotation>
                                                                      </xs:attribute>
                                                                    </xs:complexType>
                                                                  </xs:element>
                                                                </xs:sequence>
                                                              </xs:complexType>
                                                            </xs:element>
                                                          </xs:sequence>
                                                          <xs:attribute name="SetForQuantity" type="xs:nonNegativeInteger" use="optional">
                                                            <xs:annotation>
                                                              <xs:documentation xml:lang="en">The number of settings required.</xs:documentation>
                                                            </xs:annotation>
                                                          </xs:attribute>
                                                          <xs:attribute name="ServiceTime" type="DateOrTimeOrDateTimeType" use="optional">
                                                            <xs:annotation>
                                                              <xs:documentation xml:lang="en">The time or date and time when food and beverage will be served.</xs:documentation>
                                                            </xs:annotation>
                                                          </xs:attribute>
                                                          <xs:attribute name="AttendeeQuantity" type="xs:nonNegativeInteger" use="optional">
                                                            <xs:annotation>
                                                              <xs:documentation xml:lang="en">The anticipated number of people to be served.</xs:documentation>
                                                            </xs:annotation>
                                                          </xs:attribute>
                                                          <xs:attribute name="GuaranteeQuantity" type="xs:nonNegativeInteger" use="optional">
                                                            <xs:annotation>
                                                              <xs:documentation xml:lang="en">The number of people guaranteed.</xs:documentation>
                                                            </xs:annotation>
                                                          </xs:attribute>
                                                          <xs:attribute name="MealTypeCode" type="OTA_CodeType" use="optional">
                                                            <xs:annotation>
                                                              <xs:documentation xml:lang="en">The type of meal being served. Refer to OpenTravel Code list Available Meal Category (AMC).</xs:documentation>
                                                            </xs:annotation>
                                                          </xs:attribute>
                                                          <xs:attribute name="ServiceTypeCode" type="OTA_CodeType" use="optional">
                                                            <xs:annotation>
                                                              <xs:documentation xml:lang="en">The type of food and beverage service being provided (e.g., buffet, plated). Refer to OpenTravel Code list Event Charge (EVT).</xs:documentation>
                                                            </xs:annotation>
                                                          </xs:attribute>
                                                        </xs:complexType>
                                                      </xs:element>
                                                    </xs:sequence>
                                                    <xs:attributeGroup ref="ProviderGroup">
                                                      <xs:annotation>
                                                        <xs:documentation xml:lang="en">Identifies who will be providing the food and beverage for the function.</xs:documentation>
                                                      </xs:annotation>
                                                    </xs:attributeGroup>
                                                  </xs:complexType>
                                                </xs:element>
                                                <xs:element name="Services" minOccurs="0">
                                                  <xs:annotation>
                                                    <xs:documentation xml:lang="en">A collection of services.</xs:documentation>
                                                  </xs:annotation>
                                                  <xs:complexType>
                                                    <xs:sequence>
                                                      <xs:element name="Service" maxOccurs="unbounded">
                                                        <xs:annotation>
                                                          <xs:documentation xml:lang="en">Details regarding an additional services(e.g., security, first aid).</xs:documentation>
                                                        </xs:annotation>
                                                        <xs:complexType>
                                                          <xs:sequence>
                                                            <xs:element name="Utilities" minOccurs="0">
                                                              <xs:annotation>
                                                                <xs:documentation xml:lang="en">A collection of utilities.</xs:documentation>
                                                              </xs:annotation>
                                                              <xs:complexType>
                                                                <xs:sequence>
                                                                  <xs:element name="Utility" maxOccurs="99">
                                                                    <xs:annotation>
                                                                      <xs:documentation xml:lang="en">A utility requirement for this function.</xs:documentation>
                                                                    </xs:annotation>
                                                                    <xs:complexType>
                                                                      <xs:sequence>
                                                                        <xs:element name="Comments" minOccurs="0">
                                                                          <xs:annotation>
                                                                            <xs:documentation xml:lang="en">A collection of comments.</xs:documentation>
                                                                          </xs:annotation>
                                                                          <xs:complexType>
                                                                            <xs:sequence>
                                                                              <xs:element name="Comment" type="ParagraphType" maxOccurs="99">
                                                                                <xs:annotation>
                                                                                  <xs:documentation xml:lang="en">A comment associated to the service.</xs:documentation>
                                                                                </xs:annotation>
                                                                              </xs:element>
                                                                            </xs:sequence>
                                                                          </xs:complexType>
                                                                        </xs:element>
                                                                      </xs:sequence>
                                                                      <xs:attribute name="Code" type="OTA_CodeType" use="optional">
                                                                        <xs:annotation>
                                                                          <xs:documentation xml:lang="en">The type of this utility (e.g., air, drain, natural gas). Refer to OpenTravel Code list Utility Service Code (USC).</xs:documentation>
                                                                        </xs:annotation>
                                                                      </xs:attribute>
                                                                      <xs:attributeGroup ref="QuantityGroup">
                                                                        <xs:annotation>
                                                                          <xs:documentation xml:lang="en">The requested number of this utility item.</xs:documentation>
                                                                        </xs:annotation>
                                                                      </xs:attributeGroup>
                                                                      <xs:attributeGroup ref="CurrencyAmountGroup">
                                                                        <xs:annotation>
                                                                          <xs:documentation xml:lang="en">The amount charged for the utility item.</xs:documentation>
                                                                        </xs:annotation>
                                                                      </xs:attributeGroup>
                                                                      <xs:attribute name="Name" type="StringLength0to128" use="optional">
                                                                        <xs:annotation>
                                                                          <xs:documentation xml:lang="en">The name of the utility.</xs:documentation>
                                                                        </xs:annotation>
                                                                      </xs:attribute>
                                                                      <xs:attribute name="TelecommunicationCode" type="OTA_CodeType" use="optional">
                                                                        <xs:annotation>
                                                                          <xs:documentation xml:lang="en">A telecommunication service (e.g., analog phone line, ethernet connection). Refer to OpenTravel Code list Business Srvc Type (BUS).</xs:documentation>
                                                                        </xs:annotation>
                                                                      </xs:attribute>
                                                                      <xs:attribute name="Description" type="StringLength1to64" use="optional">
                                                                        <xs:annotation>
                                                                          <xs:documentation xml:lang="en">A description of the utility.</xs:documentation>
                                                                        </xs:annotation>
                                                                      </xs:attribute>
                                                                    </xs:complexType>
                                                                  </xs:element>
                                                                </xs:sequence>
                                                              </xs:complexType>
                                                            </xs:element>
                                                            <xs:element name="Comments" minOccurs="0">
                                                              <xs:annotation>
                                                                <xs:documentation xml:lang="en">A collection of comments.</xs:documentation>
                                                              </xs:annotation>
                                                              <xs:complexType>
                                                                <xs:sequence>
                                                                  <xs:element name="Comment" type="ParagraphType" maxOccurs="99">
                                                                    <xs:annotation>
                                                                      <xs:documentation xml:lang="en">A comment associated to the service.</xs:documentation>
                                                                    </xs:annotation>
                                                                  </xs:element>
                                                                </xs:sequence>
                                                              </xs:complexType>
                                                            </xs:element>
                                                          </xs:sequence>
                                                          <xs:attributeGroup ref="ProviderGroup">
                                                            <xs:annotation>
                                                              <xs:documentation xml:lang="en">Used to define who will provide the service.</xs:documentation>
                                                            </xs:annotation>
                                                          </xs:attributeGroup>
                                                          <xs:attribute name="LocationName" type="StringLength1to64" use="optional">
                                                            <xs:annotation>
                                                              <xs:documentation xml:lang="en">Identifies a location (e.g. individual hotel room, hotel lobby, exterior, pool).</xs:documentation>
                                                            </xs:annotation>
                                                          </xs:attribute>
                                                          <xs:attribute name="StartTime" type="xs:time" use="optional">
                                                            <xs:annotation>
                                                              <xs:documentation xml:lang="en">The time the service is scheduled to begin.</xs:documentation>
                                                            </xs:annotation>
                                                          </xs:attribute>
                                                          <xs:attribute name="EndTime" type="xs:time" use="optional">
                                                            <xs:annotation>
                                                              <xs:documentation xml:lang="en">The time the service is scheduled to finish.</xs:documentation>
                                                            </xs:annotation>
                                                          </xs:attribute>
                                                          <xs:attribute name="Code" type="OTA_CodeType" use="optional">
                                                            <xs:annotation>
                                                              <xs:documentation xml:lang="en">Identifies a type of service (e.g. security, first aid, decor). Refer to OpenTravel Codelist Meeting Room Code (MRC).</xs:documentation>
                                                            </xs:annotation>
                                                          </xs:attribute>
                                                          <xs:attribute name="CodeDetail" type="StringLength1to64" use="optional">
                                                            <xs:annotation>
                                                              <xs:documentation xml:lang="en">May be used to give further detail on the code. </xs:documentation>
                                                            </xs:annotation>
                                                          </xs:attribute>
                                                        </xs:complexType>
                                                      </xs:element>
                                                    </xs:sequence>
                                                  </xs:complexType>
                                                </xs:element>
                                                <xs:element name="Comments" minOccurs="0">
                                                  <xs:annotation>
                                                    <xs:documentation xml:lang="en">A collection of comments associated to the function.</xs:documentation>
                                                  </xs:annotation>
                                                  <xs:complexType>
                                                    <xs:sequence>
                                                      <xs:element name="Comment" type="ParagraphType" maxOccurs="99">
                                                        <xs:annotation>
                                                          <xs:documentation xml:lang="en">A comment associated to the function.</xs:documentation>
                                                        </xs:annotation>
                                                      </xs:element>
                                                    </xs:sequence>
                                                  </xs:complexType>
                                                </xs:element>
                                              </xs:sequence>
                                              <xs:attribute name="FunctionName" type="StringLength1to64" use="optional">
                                                <xs:annotation>
                                                  <xs:documentation xml:lang="en">The name of the function.</xs:documentation>
                                                </xs:annotation>
                                              </xs:attribute>
                                              <xs:attribute name="FunctionType" type="OTA_CodeType" use="optional">
                                                <xs:annotation>
                                                  <xs:documentation xml:lang="en">The type of function being requested (e.g. breakfast meeting or reception). Refer to OpenTravel Code table Event Charge (EVT).</xs:documentation>
                                                </xs:annotation>
                                              </xs:attribute>
                                              <xs:attribute name="LocationName" type="StringLength1to64" use="optional">
                                                <xs:annotation>
                                                  <xs:documentation xml:lang="en">Identifies a location (e.g. individual hotel room, hotel lobby, exterior, pool) for the function.</xs:documentation>
                                                </xs:annotation>
                                              </xs:attribute>
                                              <xs:attribute name="LocationID" type="StringLength1to32" use="optional">
                                                <xs:annotation>
                                                  <xs:documentation xml:lang="en">The ID of the room where the function is taking place.</xs:documentation>
                                                </xs:annotation>
                                              </xs:attribute>
                                              <xs:attribute name="TwentyFourHourHold" type="xs:boolean" use="optional">
                                                <xs:annotation>
                                                  <xs:documentation xml:lang="en">When true, a location is held on a 24-hour basis (e.g., ensures a set-up is not disturbed).</xs:documentation>
                                                </xs:annotation>
                                              </xs:attribute>
                                              <xs:attribute name="StartTime" type="xs:time" use="optional">
                                                <xs:annotation>
                                                  <xs:documentation xml:lang="en">The time the function is scheduled to begin.</xs:documentation>
                                                </xs:annotation>
                                              </xs:attribute>
                                              <xs:attribute name="EndTime" type="xs:time" use="optional">
                                                <xs:annotation>
                                                  <xs:documentation xml:lang="en">The time the function is scheduled to finish.</xs:documentation>
                                                </xs:annotation>
                                              </xs:attribute>
                                              <xs:attribute name="AttendeeQuantity" type="xs:nonNegativeInteger" use="optional">
                                                <xs:annotation>
                                                  <xs:documentation xml:lang="en">The number of people attending this function.</xs:documentation>
                                                </xs:annotation>
                                              </xs:attribute>
                                              <xs:attribute name="RequiredKeyQuantity" type="xs:nonNegativeInteger" use="optional">
                                                <xs:annotation>
                                                  <xs:documentation xml:lang="en">The number of keys required for this function space.</xs:documentation>
                                                </xs:annotation>
                                              </xs:attribute>
                                              <xs:attribute name="ReKeyIndicator" type="xs:boolean" use="optional">
                                                <xs:annotation>
                                                  <xs:documentation xml:lang="en">When true, indicates the room lock needs rekeyed.</xs:documentation>
                                                </xs:annotation>
                                              </xs:attribute>
                                              <xs:attribute name="PostIndicator" type="xs:boolean" use="optional">
                                                <xs:annotation>
                                                  <xs:documentation xml:lang="en">When true, indicates the function should be posted (e.g., on a reader board).</xs:documentation>
                                                </xs:annotation>
                                              </xs:attribute>
                                              <xs:attribute name="ID" type="StringLength1to32" use="optional">
                                                <xs:annotation>
                                                  <xs:documentation xml:lang="en">An identifier for this function.</xs:documentation>
                                                </xs:annotation>
                                              </xs:attribute>
                                              <xs:attribute name="ExhibitionIndicator" type="xs:boolean" use="optional">
                                                <xs:annotation>
                                                  <xs:documentation xml:lang="en">When true, indicates that this function is an exhibition.</xs:documentation>
                                                </xs:annotation>
                                              </xs:attribute>
                                            </xs:complexType>
                                          </xs:element>
                                        </xs:sequence>
                                      </xs:complexType>
                                    </xs:element>
                                    <xs:element name="Services" minOccurs="0">
                                      <xs:annotation>
                                        <xs:documentation xml:lang="en">A collection of services.</xs:documentation>
                                      </xs:annotation>
                                      <xs:complexType>
                                        <xs:sequence>
                                          <xs:element name="Service" maxOccurs="99">
                                            <xs:annotation>
                                              <xs:documentation xml:lang="en">Details regarding additional services (e.g., security, first aid).</xs:documentation>
                                            </xs:annotation>
                                            <xs:complexType>
                                              <xs:attributeGroup ref="ProviderGroup">
                                                <xs:annotation>
                                                  <xs:documentation xml:lang="en">Identifies the provider of the service.</xs:documentation>
                                                </xs:annotation>
                                              </xs:attributeGroup>
                                              <xs:attribute name="LocationName" type="StringLength1to64" use="optional">
                                                <xs:annotation>
                                                  <xs:documentation xml:lang="en">Identifies a location (e.g. individual hotel room, hotel lobby, exterior, pool).</xs:documentation>
                                                </xs:annotation>
                                              </xs:attribute>
                                              <xs:attribute name="StartTime" type="xs:time" use="optional">
                                                <xs:annotation>
                                                  <xs:documentation xml:lang="en">The time the service is scheduled to begin.</xs:documentation>
                                                </xs:annotation>
                                              </xs:attribute>
                                              <xs:attribute name="EndTime" type="xs:time" use="optional">
                                                <xs:annotation>
                                                  <xs:documentation xml:lang="en">The time the service is scheduled to finish.</xs:documentation>
                                                </xs:annotation>
                                              </xs:attribute>
                                              <xs:attribute name="Code" type="OTA_CodeType" use="optional">
                                                <xs:annotation>
                                                  <xs:documentation xml:lang="en">Identifies a meeting or business service (e.g. security, first aid). Refer to OpenTravel Codelist Meeting Room Code (MRC).</xs:documentation>
                                                </xs:annotation>
                                              </xs:attribute>
                                            </xs:complexType>
                                          </xs:element>
                                        </xs:sequence>
                                      </xs:complexType>
                                    </xs:element>
                                    <xs:element name="OffSiteTransportations" minOccurs="0">
                                      <xs:annotation>
                                        <xs:documentation xml:lang="en">A collection of off-site transportation data.</xs:documentation>
                                      </xs:annotation>
                                      <xs:complexType>
                                        <xs:sequence>
                                          <xs:element name="OffSiteTransportation" maxOccurs="99">
                                            <xs:annotation>
                                              <xs:documentation xml:lang="en">Transportation information for an off-site function.</xs:documentation>
                                            </xs:annotation>
                                            <xs:complexType>
                                              <xs:sequence>
                                                <xs:element name="Location" minOccurs="0" maxOccurs="9">
                                                  <xs:annotation>
                                                    <xs:documentation xml:lang="en">Location information for a departure, drop-off, or return.</xs:documentation>
                                                  </xs:annotation>
                                                  <xs:complexType>
                                                    <xs:attribute name="Name" type="StringLength1to64" use="optional">
                                                      <xs:annotation>
                                                        <xs:documentation xml:lang="en">The name of the location.</xs:documentation>
                                                      </xs:annotation>
                                                    </xs:attribute>
                                                    <xs:attribute name="Time" type="xs:time" use="optional">
                                                      <xs:annotation>
                                                        <xs:documentation xml:lang="en">The time for this transportation activity (e.g., .return, drop-off, departure).</xs:documentation>
                                                      </xs:annotation>
                                                    </xs:attribute>
                                                    <xs:attribute name="Type" use="optional">
                                                      <xs:annotation>
                                                        <xs:documentation xml:lang="en">The type of location (e.g., return, drop-off, departure).</xs:documentation>
                                                      </xs:annotation>
                                                      <xs:simpleType>
                                                        <xs:restriction base="xs:NMTOKEN">
                                                          <xs:enumeration value="Departure">
                                                            <xs:annotation>
                                                              <xs:documentation xml:lang="en">Identifies that this location is the departure location.</xs:documentation>
                                                            </xs:annotation>
                                                          </xs:enumeration>
                                                          <xs:enumeration value="DropOff">
                                                            <xs:annotation>
                                                              <xs:documentation xml:lang="en">Identifies that this location is the drop-off location.</xs:documentation>
                                                            </xs:annotation>
                                                          </xs:enumeration>
                                                          <xs:enumeration value="Return">
                                                            <xs:annotation>
                                                              <xs:documentation xml:lang="en">Identifies that this location is the return location.</xs:documentation>
                                                            </xs:annotation>
                                                          </xs:enumeration>
                                                        </xs:restriction>
                                                      </xs:simpleType>
                                                    </xs:attribute>
                                                  </xs:complexType>
                                                </xs:element>
                                                <xs:element name="Comments" minOccurs="0">
                                                  <xs:annotation>
                                                    <xs:documentation xml:lang="en">A collection of comments associated with the transportation.</xs:documentation>
                                                  </xs:annotation>
                                                  <xs:complexType>
                                                    <xs:sequence>
                                                      <xs:element name="Comment" type="ParagraphType" maxOccurs="99">
                                                        <xs:annotation>
                                                          <xs:documentation xml:lang="en">A comment associated with the transportation.</xs:documentation>
                                                        </xs:annotation>
                                                      </xs:element>
                                                    </xs:sequence>
                                                  </xs:complexType>
                                                </xs:element>
                                              </xs:sequence>
                                              <xs:attributeGroup ref="ProviderGroup">
                                                <xs:annotation>
                                                  <xs:documentation xml:lang="en">Identifies the company providing the transportation.</xs:documentation>
                                                </xs:annotation>
                                              </xs:attributeGroup>
                                              <xs:attribute name="FunctionName" type="StringLength1to64" use="optional">
                                                <xs:annotation>
                                                  <xs:documentation xml:lang="en">The name of the off-site function.</xs:documentation>
                                                </xs:annotation>
                                              </xs:attribute>
                                            </xs:complexType>
                                          </xs:element>
                                        </xs:sequence>
                                      </xs:complexType>
                                    </xs:element>
                                  </xs:sequence>
                                  <xs:attribute name="DayNumber" type="xs:positiveInteger" use="required">
                                    <xs:annotation>
                                      <xs:documentation xml:lang="en">The number which indicates the day of the event (e.g. 1, 2, 3). This is relative to the Start attribute in the EventInfo element.</xs:documentation>
                                    </xs:annotation>
                                  </xs:attribute>
                                </xs:complexType>
                              </xs:element>
                            </xs:sequence>
                            <xs:attribute name="FirstEventDayOfWeek" type="DayOfWeekType" use="optional">
                              <xs:annotation>
                                <xs:documentation xml:lang="en">This is used to identify the first day of the week for the event.</xs:documentation>
                              </xs:annotation>
                            </xs:attribute>
                          </xs:complexType>
                        </xs:element>
                        <xs:element name="ShippingReceivingDetails" minOccurs="0">
                          <xs:annotation>
                            <xs:documentation xml:lang="en">A collection of shipping and receiving details.</xs:documentation>
                          </xs:annotation>
                          <xs:complexType>
                            <xs:sequence>
                              <xs:element name="ShippingReceivingDetail" maxOccurs="unbounded">
                                <xs:annotation>
                                  <xs:documentation xml:lang="en">The shipping and receiving details for a specific shipment.</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                  <xs:sequence>
                                    <xs:element name="Contact" type="ContactPersonType" minOccurs="0" maxOccurs="3">
                                      <xs:annotation>
                                        <xs:documentation xml:lang="en">Contact information for the sender, receiver and shipping company.</xs:documentation>
                                      </xs:annotation>
                                    </xs:element>
                                  </xs:sequence>
                                  <xs:attribute name="TrackingNumber" type="StringLength1to64" use="optional">
                                    <xs:annotation>
                                      <xs:documentation xml:lang="en">The tracking number for the shipment.</xs:documentation>
                                    </xs:annotation>
                                  </xs:attribute>
                                  <xs:attributeGroup ref="QuantityGroup">
                                    <xs:annotation>
                                      <xs:documentation xml:lang="en">The number of items being shipped.</xs:documentation>
                                    </xs:annotation>
                                  </xs:attributeGroup>
                                  <xs:attribute name="ExpectedDeliveryDate" type="DateOrDateTimeType" use="optional">
                                    <xs:annotation>
                                      <xs:documentation xml:lang="en">The date the shipment is expected to be delivered.</xs:documentation>
                                    </xs:annotation>
                                  </xs:attribute>
                                </xs:complexType>
                              </xs:element>
                              <xs:element name="Comments" minOccurs="0">
                                <xs:annotation>
                                  <xs:documentation xml:lang="en">A collection of comments pertaining to a shipment. </xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                  <xs:sequence>
                                    <xs:element name="Comment" type="ParagraphType" maxOccurs="99">
                                      <xs:annotation>
                                        <xs:documentation xml:lang="en">A comment pertaining to a shipment. </xs:documentation>
                                      </xs:annotation>
                                    </xs:element>
                                  </xs:sequence>
                                </xs:complexType>
                              </xs:element>
                            </xs:sequence>
                            <xs:attribute name="PrivateVehicleDeliveryQuantity" type="xs:nonNegativeInteger" use="optional">
                              <xs:annotation>
                                <xs:documentation xml:lang="en">The number of privately owned vehicle (POV) deliveries.</xs:documentation>
                              </xs:annotation>
                            </xs:attribute>
                          </xs:complexType>
                        </xs:element>
                        <xs:element name="Comments" minOccurs="0">
                          <xs:annotation>
                            <xs:documentation xml:lang="en">A collection of comments pertaining to a specific site.</xs:documentation>
                          </xs:annotation>
                          <xs:complexType>
                            <xs:sequence>
                              <xs:element name="Comment" type="ParagraphType" maxOccurs="99">
                                <xs:annotation>
                                  <xs:documentation xml:lang="en">A comment pertaining to a specific site.</xs:documentation>
                                </xs:annotation>
                              </xs:element>
                            </xs:sequence>
                          </xs:complexType>
                        </xs:element>
                      </xs:sequence>
                      <xs:attribute name="PrimaryFacilityIndicator" type="xs:boolean" use="optional">
                        <xs:annotation>
                          <xs:documentation xml:lang="en">When true, indicates the site is the primary facility for the event.</xs:documentation>
                        </xs:annotation>
                      </xs:attribute>
                      <xs:attribute name="HeadquarterHotelIndicator" type="xs:boolean" use="optional">
                        <xs:annotation>
                          <xs:documentation xml:lang="en">When true, indicates the site is the primary hotel for the event.</xs:documentation>
                        </xs:annotation>
                      </xs:attribute>
                      <xs:attributeGroup ref="HotelReferenceGroup">
                        <xs:annotation>
                          <xs:documentation xml:lang="en">Identifies a particular site where functions for the event will be held.</xs:documentation>
                        </xs:annotation>
                      </xs:attributeGroup>
                    </xs:complexType>
                  </xs:element>
                </xs:sequence>
              </xs:complexType>
            </xs:element>
            <xs:element name="Comments" minOccurs="0">
              <xs:annotation>
                <xs:documentation xml:lang="en">A collection of comments pertaining to the event.</xs:documentation>
              </xs:annotation>
              <xs:complexType>
                <xs:sequence>
                  <xs:element name="Comment" type="ParagraphType" maxOccurs="99">
                    <xs:annotation>
                      <xs:documentation xml:lang="en">A comment pertaining to the event.</xs:documentation>
                    </xs:annotation>
                  </xs:element>
                </xs:sequence>
              </xs:complexType>
            </xs:element>
            <xs:element name="CustomQuestions" minOccurs="0">
              <xs:annotation>
                <xs:documentation>A container for collecting custom questions and answers.</xs:documentation>
              </xs:annotation>
              <xs:complexType>
                <xs:sequence>
                  <xs:element name="CustomQuestion" type="CustomQuestionType" maxOccurs="unbounded">
                    <xs:annotation>
                      <xs:documentation>Details regarding a particular question.</xs:documentation>
                    </xs:annotation>
                  </xs:element>
                </xs:sequence>
              </xs:complexType>
            </xs:element>
          </xs:sequence>
          <xs:attribute name="Name" type="xs:string" use="optional">
            <xs:annotation>
              <xs:documentation xml:lang="en">Identifies the name of the event.</xs:documentation>
            </xs:annotation>
          </xs:attribute>
          <xs:attribute name="Acronym" type="StringLength1to32" use="optional">
            <xs:annotation>
              <xs:documentation xml:lang="en">Identifies an acronym for the event.</xs:documentation>
            </xs:annotation>
          </xs:attribute>
          <xs:attribute name="URL" type="xs:anyURI" use="optional">
            <xs:annotation>
              <xs:documentation xml:lang="en">Identifies a web site associated with the event.</xs:documentation>
            </xs:annotation>
          </xs:attribute>
          <xs:attribute name="Type" type="OTA_CodeType" use="optional">
            <xs:annotation>
              <xs:documentation xml:lang="en">Identifies the type of event (e.g., Board Meeting, Trade Show).  Refer to OpenTravel Code List Event Type (ETT).</xs:documentation>
            </xs:annotation>
          </xs:attribute>
          <xs:attribute name="Frequency" type="FrequencyType" use="optional">
            <xs:annotation>
              <xs:documentation xml:lang="en">Used to indicate the frequency of this event.</xs:documentation>
            </xs:annotation>
          </xs:attribute>
          <xs:attribute name="MandatoryIndicator" type="xs:boolean" use="optional">
            <xs:annotation>
              <xs:documentation xml:lang="en">When true, the attendees are required to attend this event.  When false, attendance at the event is optional.</xs:documentation>
            </xs:annotation>
          </xs:attribute>
          <xs:attribute name="SpouseInvitedIndicator" type="xs:boolean" use="optional">
            <xs:annotation>
              <xs:documentation xml:lang="en">When true, the attendees spouses are invited to the event.</xs:documentation>
            </xs:annotation>
          </xs:attribute>
          <xs:attribute name="ChildrenInvitedIndicator" type="xs:boolean" use="optional">
            <xs:annotation>
              <xs:documentation xml:lang="en">When true, the attendees children are invited to the event.</xs:documentation>
            </xs:annotation>
          </xs:attribute>
          <xs:attribute name="Scope" use="optional">
            <xs:annotation>
              <xs:documentation xml:lang="en">Defines the size of the event by identifying if one or multiple venues are needed for the event.</xs:documentation>
            </xs:annotation>
            <xs:simpleType>
              <xs:restriction base="xs:NMTOKEN">
                <xs:enumeration value="CityWide">
                  <xs:annotation>
                    <xs:documentation xml:lang="en">The event occupies facilities throughout the city.</xs:documentation>
                  </xs:annotation>
                </xs:enumeration>
                <xs:enumeration value="SingleVenue">
                  <xs:annotation>
                    <xs:documentation xml:lang="en">The event occupies a single facility.</xs:documentation>
                  </xs:annotation>
                </xs:enumeration>
                <xs:enumeration value="MultipleVenue">
                  <xs:annotation>
                    <xs:documentation xml:lang="en">The event occupies several facilities in the city.</xs:documentation>
                  </xs:annotation>
                </xs:enumeration>
              </xs:restriction>
            </xs:simpleType>
          </xs:attribute>
          <xs:attribute name="PreEventSetUpIndicator" type="xs:boolean" use="optional">
            <xs:annotation>
              <xs:documentation xml:lang="en">When true, indicates the event requires a move-in or set-up prior to the actual start of the event.</xs:documentation>
            </xs:annotation>
          </xs:attribute>
          <xs:attribute name="PreEventSetUpContractor" type="StringLength1to128" use="optional">
            <xs:annotation>
              <xs:documentation xml:lang="en">Identifies the contractor responsible for the pre-event setup.</xs:documentation>
            </xs:annotation>
          </xs:attribute>
          <xs:attribute name="Status" type="StringLength1to32" use="optional">
            <xs:annotation>
              <xs:documentation xml:lang="en">The status of the event (e.g., definite, tentative, cancelled).</xs:documentation>
            </xs:annotation>
          </xs:attribute>
        </xs:complexType>
      </xs:element>
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="EventReportType">
    <xs:annotation>
      <xs:documentation>Provides actualized event information.</xs:documentation>
    </xs:annotation>
    <xs:sequence>
      <xs:element name="EventSites" minOccurs="0">
        <xs:annotation>
          <xs:documentation xml:lang="en">A collection of sites utilized by the event.</xs:documentation>
        </xs:annotation>
        <xs:complexType>
          <xs:sequence>
            <xs:element name="EventSite" type="PostEventSiteReportType" maxOccurs="99">
              <xs:annotation>
                <xs:documentation xml:lang="en">A site utilized by the event.</xs:documentation>
              </xs:annotation>
            </xs:element>
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="GeneralEventInfo" minOccurs="0">
        <xs:annotation>
          <xs:documentation xml:lang="en">High-level information regarding the event.</xs:documentation>
        </xs:annotation>
        <xs:complexType>
          <xs:sequence>
            <xs:element name="EventContacts" minOccurs="0">
              <xs:annotation>
                <xs:documentation xml:lang="en">A collection of contacts that pertain to this event.  </xs:documentation>
              </xs:annotation>
              <xs:complexType>
                <xs:sequence>
                  <xs:element name="EventContact" maxOccurs="99">
                    <xs:annotation>
                      <xs:documentation xml:lang="en">A contact that pertains to this event. </xs:documentation>
                    </xs:annotation>
                    <xs:complexType>
                      <xs:complexContent>
                        <xs:extension base="ContactPersonType">
                          <xs:attribute name="Role" type="StringLength1to32" use="optional">
                            <xs:annotation>
                              <xs:documentation xml:lang="en">Specifies the role of the contact (e.g., event planner, informational contact).</xs:documentation>
                            </xs:annotation>
                          </xs:attribute>
                        </xs:extension>
                      </xs:complexContent>
                    </xs:complexType>
                  </xs:element>
                </xs:sequence>
              </xs:complexType>
            </xs:element>
            <xs:element name="EventLocation" type="LocationGeneralType" minOccurs="0">
              <xs:annotation>
                <xs:documentation xml:lang="en">Provides high-level event location information (i.e., city, state/province, country).</xs:documentation>
              </xs:annotation>
            </xs:element>
            <xs:element name="AttendeeInfo" minOccurs="0" maxOccurs="3">
              <xs:annotation>
                <xs:documentation xml:lang="en">Specifies the number of event attendees.</xs:documentation>
              </xs:annotation>
              <xs:complexType>
                <xs:sequence>
                  <xs:element name="Comments" minOccurs="0">
                    <xs:annotation>
                      <xs:documentation xml:lang="en">A collection of comments pertaining to the attendees.</xs:documentation>
                    </xs:annotation>
                    <xs:complexType>
                      <xs:sequence>
                        <xs:element name="Comment" type="ParagraphType" maxOccurs="99">
                          <xs:annotation>
                            <xs:documentation xml:lang="en">Comments about the attendees as a group. </xs:documentation>
                          </xs:annotation>
                        </xs:element>
                      </xs:sequence>
                    </xs:complexType>
                  </xs:element>
                </xs:sequence>
                <xs:attributeGroup ref="PostEventAttendanceGroup">
                  <xs:annotation>
                    <xs:documentation xml:lang="en">Attendance quantities qualifed as estimated, guaranteed or actual.</xs:documentation>
                  </xs:annotation>
                </xs:attributeGroup>
              </xs:complexType>
            </xs:element>
            <xs:element name="Dates" minOccurs="0">
              <xs:annotation>
                <xs:documentation xml:lang="en">A collection of dates for past or future events.</xs:documentation>
              </xs:annotation>
              <xs:complexType>
                <xs:sequence>
                  <xs:element name="Date" maxOccurs="99">
                    <xs:annotation>
                      <xs:documentation xml:lang="en">Specifies event date and associated location information.</xs:documentation>
                    </xs:annotation>
                    <xs:complexType>
                      <xs:complexContent>
                        <xs:extension base="DateTimeSpanType">
                          <xs:sequence>
                            <xs:element name="LocationCategories" minOccurs="0" maxOccurs="5">
                              <xs:annotation>
                                <xs:documentation xml:lang="en">Used to define requirements or preferences in location for the event.</xs:documentation>
                              </xs:annotation>
                              <xs:complexType>
                                <xs:sequence>
                                  <xs:element name="Location" minOccurs="0">
                                    <xs:annotation>
                                      <xs:documentation xml:lang="en">Preferred event location.</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                      <xs:complexContent>
                                        <xs:extension base="LocationGeneralType">
                                          <xs:attributeGroup ref="HotelReferenceGroup">
                                            <xs:annotation>
                                              <xs:documentation xml:lang="en">The venue for which event information is being provided (e.g., hotel, convention center, golf course).</xs:documentation>
                                            </xs:annotation>
                                          </xs:attributeGroup>
                                        </xs:extension>
                                      </xs:complexContent>
                                    </xs:complexType>
                                  </xs:element>
                                  <xs:element name="Category" minOccurs="0" maxOccurs="99">
                                    <xs:annotation>
                                      <xs:documentation xml:lang="en">Used to define the preferences or requirements regarding an event location.</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                      <xs:complexContent>
                                        <xs:extension base="FormattedTextTextType">
                                          <xs:attribute name="LocationCategoryCode" type="OTA_CodeType" use="optional">
                                            <xs:annotation>
                                              <xs:documentation xml:lang="en">Refer to OpenTravel Code table Location Category Codes (LOC).</xs:documentation>
                                            </xs:annotation>
                                          </xs:attribute>
                                          <xs:attributeGroup ref="PreferLevelGroup">
                                            <xs:annotation>
                                              <xs:documentation xml:lang="en">Describes whether the location is preferred, required or there is no preference.</xs:documentation>
                                            </xs:annotation>
                                          </xs:attributeGroup>
                                        </xs:extension>
                                      </xs:complexContent>
                                    </xs:complexType>
                                  </xs:element>
                                </xs:sequence>
                              </xs:complexType>
                            </xs:element>
                            <xs:element name="Comments" minOccurs="0">
                              <xs:annotation>
                                <xs:documentation>Collection of comments.</xs:documentation>
                              </xs:annotation>
                              <xs:complexType>
                                <xs:sequence>
                                  <xs:element name="Comment" type="ParagraphType" maxOccurs="99">
                                    <xs:annotation>
                                      <xs:documentation xml:lang="en">Comments regarding these dates or date range.</xs:documentation>
                                    </xs:annotation>
                                  </xs:element>
                                </xs:sequence>
                              </xs:complexType>
                            </xs:element>
                          </xs:sequence>
                          <xs:attribute name="ContractedIndicator" type="xs:boolean" use="optional">
                            <xs:annotation>
                              <xs:documentation xml:lang="en">When true, indicates that the event has already been contracted for these dates.</xs:documentation>
                            </xs:annotation>
                          </xs:attribute>
                          <xs:attribute name="EventDateType" use="optional">
                            <xs:annotation>
                              <xs:documentation xml:lang="en">Used in conjunction with Start and End to identify the type of date.</xs:documentation>
                            </xs:annotation>
                            <xs:simpleType>
                              <xs:restriction base="xs:NMTOKEN">
                                <xs:enumeration value="Past">
                                  <xs:annotation>
                                    <xs:documentation xml:lang="en">Identifies a past occurance of the event.</xs:documentation>
                                  </xs:annotation>
                                </xs:enumeration>
                                <xs:enumeration value="Current">
                                  <xs:annotation>
                                    <xs:documentation xml:lang="en">Identifies the dates for this occurance of the event.</xs:documentation>
                                  </xs:annotation>
                                </xs:enumeration>
                                <xs:enumeration value="Future">
                                  <xs:annotation>
                                    <xs:documentation xml:lang="en">Identifies a future occurance of the event.</xs:documentation>
                                  </xs:annotation>
                                </xs:enumeration>
                              </xs:restriction>
                            </xs:simpleType>
                          </xs:attribute>
                        </xs:extension>
                      </xs:complexContent>
                    </xs:complexType>
                  </xs:element>
                </xs:sequence>
              </xs:complexType>
            </xs:element>
            <xs:element name="HousingInfo" minOccurs="0">
              <xs:annotation>
                <xs:documentation xml:lang="en">General hotel usage information.</xs:documentation>
              </xs:annotation>
              <xs:complexType>
                <xs:sequence>
                  <xs:element name="Comments" minOccurs="0">
                    <xs:annotation>
                      <xs:documentation xml:lang="en">A collection of comments regarding the general hotel usage.</xs:documentation>
                    </xs:annotation>
                    <xs:complexType>
                      <xs:sequence>
                        <xs:element name="Comment" type="ParagraphType" maxOccurs="99">
                          <xs:annotation>
                            <xs:documentation xml:lang="en">Comments regarding the general hotel usage.</xs:documentation>
                          </xs:annotation>
                        </xs:element>
                      </xs:sequence>
                    </xs:complexType>
                  </xs:element>
                </xs:sequence>
                <xs:attribute name="SleepingRoomsIndicator" type="xs:boolean" use="optional">
                  <xs:annotation>
                    <xs:documentation xml:lang="en">When true, indicates the event utilized sleeping rooms.</xs:documentation>
                  </xs:annotation>
                </xs:attribute>
                <xs:attribute name="HotelQuantity" type="xs:nonNegativeInteger" use="optional">
                  <xs:annotation>
                    <xs:documentation xml:lang="en">Specifies the total number of hotels utilized by the event.</xs:documentation>
                  </xs:annotation>
                </xs:attribute>
                <xs:attribute name="PeakRoomNightQuantity" type="xs:nonNegativeInteger" use="optional">
                  <xs:annotation>
                    <xs:documentation xml:lang="en">Specifies the total number of sleeping rooms utilized on the peak night across all hotels.</xs:documentation>
                  </xs:annotation>
                </xs:attribute>
                <xs:attribute name="HousingProviderCode" type="OTA_CodeType" use="optional">
                  <xs:annotation>
                    <xs:documentation xml:lang="en">The type of housing service provider used for this event. Refer to OpenTravel Code list Destination Service Provider Type (DSP).</xs:documentation>
                  </xs:annotation>
                </xs:attribute>
                <xs:attribute name="HousingProviderName" type="StringLength1to255" use="optional">
                  <xs:annotation>
                    <xs:documentation xml:lang="en">Name of the housing service provider used for this event.</xs:documentation>
                  </xs:annotation>
                </xs:attribute>
              </xs:complexType>
            </xs:element>
            <xs:element name="FoodAndBeverageInfo" minOccurs="0">
              <xs:annotation>
                <xs:documentation xml:lang="en">General food and beverage information.</xs:documentation>
              </xs:annotation>
              <xs:complexType>
                <xs:sequence>
                  <xs:element name="Comments" minOccurs="0">
                    <xs:annotation>
                      <xs:documentation xml:lang="en">A collection of comments regarding the general food and beverage information.</xs:documentation>
                    </xs:annotation>
                    <xs:complexType>
                      <xs:sequence>
                        <xs:element name="Comment" type="ParagraphType" maxOccurs="99">
                          <xs:annotation>
                            <xs:documentation xml:lang="en">Comments regarding the general food and beverage information. </xs:documentation>
                          </xs:annotation>
                        </xs:element>
                      </xs:sequence>
                    </xs:complexType>
                  </xs:element>
                </xs:sequence>
                <xs:attribute name="FoodAndBeverageIndicator" type="xs:boolean" use="optional">
                  <xs:annotation>
                    <xs:documentation xml:lang="en">When true, indicates this event had at least one food and beverage function.</xs:documentation>
                  </xs:annotation>
                </xs:attribute>
                <xs:attribute name="LargestAttendanceQuantity" type="xs:nonNegativeInteger" use="optional">
                  <xs:annotation>
                    <xs:documentation xml:lang="en">Specifies the attendance at the largest food and beverage function for the event.</xs:documentation>
                  </xs:annotation>
                </xs:attribute>
                <xs:attribute name="LargestAttendanceFunctionType" type="OTA_CodeType" use="optional">
                  <xs:annotation>
                    <xs:documentation xml:lang="en">Identifies the type of function with the largest attendance. Refer to OpenTravel Code list Event Charge (EVT).</xs:documentation>
                  </xs:annotation>
                </xs:attribute>
                <xs:attribute name="LargestRevenueFunctionType" type="OTA_CodeType" use="optional">
                  <xs:annotation>
                    <xs:documentation xml:lang="en">Identifies the type of function that generated the most revenue. Refer to OpenTravel Code list Event Charge (EVT).</xs:documentation>
                  </xs:annotation>
                </xs:attribute>
                <xs:attribute name="ICW_Indicator" type="xs:boolean" use="optional">
                  <xs:annotation>
                    <xs:documentation xml:lang="en">In conjunction with indicator. When true, indicates there was at least one function that was not sponsored by this event, but that was held in conjunction with this event.</xs:documentation>
                  </xs:annotation>
                </xs:attribute>
              </xs:complexType>
            </xs:element>
            <xs:element name="FunctionSpaceInfo" minOccurs="0">
              <xs:annotation>
                <xs:documentation xml:lang="en">General function space information for this event.</xs:documentation>
              </xs:annotation>
              <xs:complexType>
                <xs:sequence>
                  <xs:element name="LargestFunctionSpace" minOccurs="0">
                    <xs:annotation>
                      <xs:documentation xml:lang="en">Information regarding the largest function space for this event.</xs:documentation>
                    </xs:annotation>
                    <xs:complexType>
                      <xs:attribute name="AttendanceQuantity" type="xs:nonNegativeInteger" use="optional">
                        <xs:annotation>
                          <xs:documentation xml:lang="en">Specifies the attendance at the largest function for the event.</xs:documentation>
                        </xs:annotation>
                      </xs:attribute>
                      <xs:attribute name="TwentyFourHourHoldInd" type="xs:boolean" use="optional">
                        <xs:annotation>
                          <xs:documentation xml:lang="en">When true, indicates the largest function space required a 24-hour hold (e.g., ensures a set-up is not disturbed).</xs:documentation>
                        </xs:annotation>
                      </xs:attribute>
                      <xs:attribute name="RoomSetupCode" type="OTA_CodeType" use="optional">
                        <xs:annotation>
                          <xs:documentation xml:lang="en">Indicates the arrangement of the largest function space (e.g. conference or banquet style) for this event. Refer to OpenTravel Code List Meeting Room Format Code (MRF).</xs:documentation>
                        </xs:annotation>
                      </xs:attribute>
                      <xs:attribute name="AudioVisualCode" type="ListOfOTA_CodeType" use="optional">
                        <xs:annotation>
                          <xs:documentation xml:lang="en">The specific audio visual requirement for the largest function for this event.  Refer to OpenTravel Code Table Meeting Room code (MRC).</xs:documentation>
                        </xs:annotation>
                      </xs:attribute>
                    </xs:complexType>
                  </xs:element>
                  <xs:element name="BreakoutSessions" minOccurs="0">
                    <xs:annotation>
                      <xs:documentation xml:lang="en">General breakout session information for this event.</xs:documentation>
                    </xs:annotation>
                    <xs:complexType>
                      <xs:attribute name="LargestConcurrentQuantity" type="xs:nonNegativeInteger" use="optional">
                        <xs:annotation>
                          <xs:documentation xml:lang="en">The greatest number of breakout sessions running concurrently.</xs:documentation>
                        </xs:annotation>
                      </xs:attribute>
                      <xs:attribute name="LargestDailyQuantity" type="xs:nonNegativeInteger" use="optional">
                        <xs:annotation>
                          <xs:documentation xml:lang="en">The greatest number of breakout sessions during a single day.</xs:documentation>
                        </xs:annotation>
                      </xs:attribute>
                      <xs:attribute name="TypicalSeatQuantity" type="xs:nonNegativeInteger" use="optional">
                        <xs:annotation>
                          <xs:documentation xml:lang="en">Number of seats for which breakout sessions are typically set.</xs:documentation>
                        </xs:annotation>
                      </xs:attribute>
                      <xs:attribute name="TypicalRoomSetupCode" type="OTA_CodeType" use="optional">
                        <xs:annotation>
                          <xs:documentation xml:lang="en">Indicates the typical arrangement of the breakout space (e.g. conference or banquet style) for this event. Refer to OpenTravel Code List Meeting Room Format Code (MRF).</xs:documentation>
                        </xs:annotation>
                      </xs:attribute>
                    </xs:complexType>
                  </xs:element>
                  <xs:element name="FunctionSpaceRequirements" minOccurs="0" maxOccurs="10">
                    <xs:annotation>
                      <xs:documentation xml:lang="en">General function space requirements for this event.</xs:documentation>
                    </xs:annotation>
                    <xs:complexType>
                      <xs:attributeGroup ref="QuantityGroup">
                        <xs:annotation>
                          <xs:documentation xml:lang="en">Used to define the amount of the Requirement needed for the event.</xs:documentation>
                        </xs:annotation>
                      </xs:attributeGroup>
                      <xs:attribute name="Requirement" use="optional">
                        <xs:annotation>
                          <xs:documentation xml:lang="en">Defines a particular function space need. Used in conjunction with quantity to define the number of the item(s) needed.</xs:documentation>
                        </xs:annotation>
                        <xs:simpleType>
                          <xs:restriction base="xs:NMTOKEN">
                            <xs:enumeration value="RegistrationArea">
                              <xs:annotation>
                                <xs:documentation xml:lang="en">Special area designated for event registration.</xs:documentation>
                              </xs:annotation>
                            </xs:enumeration>
                            <xs:enumeration value="LoungeArea">
                              <xs:annotation>
                                <xs:documentation xml:lang="en">Special lounge area designated for the event.</xs:documentation>
                              </xs:annotation>
                            </xs:enumeration>
                            <xs:enumeration value="OfficeSpace">
                              <xs:annotation>
                                <xs:documentation xml:lang="en">Special office space area designated for the event.</xs:documentation>
                              </xs:annotation>
                            </xs:enumeration>
                            <xs:enumeration value="TabletopExhibitSpace">
                              <xs:annotation>
                                <xs:documentation xml:lang="en">Special area designated for table top displays.</xs:documentation>
                              </xs:annotation>
                            </xs:enumeration>
                          </xs:restriction>
                        </xs:simpleType>
                      </xs:attribute>
                    </xs:complexType>
                  </xs:element>
                  <xs:element name="Comments" minOccurs="0">
                    <xs:annotation>
                      <xs:documentation xml:lang="en">A collection of comments regarding the general function space information.</xs:documentation>
                    </xs:annotation>
                    <xs:complexType>
                      <xs:sequence>
                        <xs:element name="Comment" type="ParagraphType" maxOccurs="99">
                          <xs:annotation>
                            <xs:documentation xml:lang="en">Comments regarding the general function space information. </xs:documentation>
                          </xs:annotation>
                        </xs:element>
                      </xs:sequence>
                    </xs:complexType>
                  </xs:element>
                </xs:sequence>
                <xs:attribute name="FunctionSpaceIndicator" type="xs:boolean" use="optional">
                  <xs:annotation>
                    <xs:documentation xml:lang="en">When true, indicates the event required function space.</xs:documentation>
                  </xs:annotation>
                </xs:attribute>
                <xs:attribute name="PropertyTypeCode" type="ListOfOTA_CodeType" use="optional">
                  <xs:annotation>
                    <xs:documentation xml:lang="en">Used to indicate the type of facilities used for this event. Refer to OpenTravel Code List Property Class Type (PCT).</xs:documentation>
                  </xs:annotation>
                </xs:attribute>
                <xs:attribute name="PreFunctionSpaceIndicator" type="xs:boolean" use="optional">
                  <xs:annotation>
                    <xs:documentation xml:lang="en">When true, indicates function space was required prior to the official start of the event.</xs:documentation>
                  </xs:annotation>
                </xs:attribute>
                <xs:attribute name="PreEventSetupIndicator" type="xs:boolean" use="optional">
                  <xs:annotation>
                    <xs:documentation xml:lang="en">When true, indicates the event requires a move-in or set-up prior to the actual start of the event.</xs:documentation>
                  </xs:annotation>
                </xs:attribute>
                <xs:attribute name="MoveInRequirement" type="xs:duration" use="optional">
                  <xs:annotation>
                    <xs:documentation xml:lang="en">Defines the duration necessary for move-in.</xs:documentation>
                  </xs:annotation>
                </xs:attribute>
                <xs:attribute name="MoveOutRequirement" type="xs:duration" use="optional">
                  <xs:annotation>
                    <xs:documentation xml:lang="en">Defines the duration necessary for move-out.</xs:documentation>
                  </xs:annotation>
                </xs:attribute>
                <xs:attribute name="UtilityCode" type="ListOfOTA_CodeType" use="optional">
                  <xs:annotation>
                    <xs:documentation xml:lang="en">Type of utility(ies) required for this event's function space (e.g., air, natural gas). Refer to OpenTravel Code list Utility Service Code (USC).</xs:documentation>
                  </xs:annotation>
                </xs:attribute>
                <xs:attribute name="TelecommunicationCode" type="ListOfOTA_CodeType" use="optional">
                  <xs:annotation>
                    <xs:documentation xml:lang="en">Telecommunication service(s) required for the event (e.g., analog phone line, ethernet connection). Refer to OpenTravel Code list Business Srvc Type (BUS).</xs:documentation>
                  </xs:annotation>
                </xs:attribute>
              </xs:complexType>
            </xs:element>
            <xs:element name="ExhibitionInfo" minOccurs="0">
              <xs:annotation>
                <xs:documentation xml:lang="en">General exhibition information for this event.</xs:documentation>
              </xs:annotation>
              <xs:complexType>
                <xs:attribute name="ExhibitionSpaceIndicator" type="xs:boolean" use="optional">
                  <xs:annotation>
                    <xs:documentation xml:lang="en">When true, indicates the event required exhibition space.</xs:documentation>
                  </xs:annotation>
                </xs:attribute>
                <xs:attribute name="PropertyTypeCode" type="ListOfOTA_CodeType" use="optional">
                  <xs:annotation>
                    <xs:documentation xml:lang="en">Used to indicate the type of facilities used for this exhibition. Refer to OpenTravel Code List Property Class Type (PCT).</xs:documentation>
                  </xs:annotation>
                </xs:attribute>
              </xs:complexType>
            </xs:element>
            <xs:element name="Comments" minOccurs="0">
              <xs:annotation>
                <xs:documentation xml:lang="en">A collection of comments that pertain to the overall event.  </xs:documentation>
              </xs:annotation>
              <xs:complexType>
                <xs:sequence>
                  <xs:element name="Comment" type="ParagraphType" minOccurs="0" maxOccurs="unbounded">
                    <xs:annotation>
                      <xs:documentation xml:lang="en">A comment that pertains to the overall event (e.g., event overview, event objectives).</xs:documentation>
                    </xs:annotation>
                  </xs:element>
                </xs:sequence>
              </xs:complexType>
            </xs:element>
          </xs:sequence>
          <xs:attribute name="Name" type="xs:string" use="optional">
            <xs:annotation>
              <xs:documentation xml:lang="en">Identifies the name of the event.</xs:documentation>
            </xs:annotation>
          </xs:attribute>
          <xs:attribute name="Acronym" type="StringLength1to32" use="optional">
            <xs:annotation>
              <xs:documentation xml:lang="en">Identifies an acronym for the event.</xs:documentation>
            </xs:annotation>
          </xs:attribute>
          <xs:attribute name="URL" type="xs:anyURI" use="optional">
            <xs:annotation>
              <xs:documentation xml:lang="en">Identifies a web site associated with the event.</xs:documentation>
            </xs:annotation>
          </xs:attribute>
          <xs:attribute name="Type" type="OTA_CodeType" use="optional">
            <xs:annotation>
              <xs:documentation xml:lang="en">Identifies the type of event (e.g., Board Meeting, Trade Show).  Refer to OpenTravel Code List Event Type (ETT).</xs:documentation>
            </xs:annotation>
          </xs:attribute>
          <xs:attribute name="Frequency" type="FrequencyType" use="optional">
            <xs:annotation>
              <xs:documentation xml:lang="en">Used to indicate the frequency of this event.</xs:documentation>
            </xs:annotation>
          </xs:attribute>
          <xs:attribute name="SpouseInvitedIndicator" type="xs:boolean" use="optional">
            <xs:annotation>
              <xs:documentation xml:lang="en">When true, the attendees spouses are invited to the event.</xs:documentation>
            </xs:annotation>
          </xs:attribute>
          <xs:attribute name="ChildrenInvitedIndicator" type="xs:boolean" use="optional">
            <xs:annotation>
              <xs:documentation xml:lang="en">When true, the attendees children are invited to the event.</xs:documentation>
            </xs:annotation>
          </xs:attribute>
          <xs:attribute name="Scope" use="optional">
            <xs:annotation>
              <xs:documentation xml:lang="en">Defines the size of the event by identifying if one or multiple venues are needed for the event.</xs:documentation>
            </xs:annotation>
            <xs:simpleType>
              <xs:restriction base="xs:NMTOKEN">
                <xs:enumeration value="CityWide">
                  <xs:annotation>
                    <xs:documentation xml:lang="en">The event occupies facilities throughout the city.</xs:documentation>
                  </xs:annotation>
                </xs:enumeration>
                <xs:enumeration value="SingleVenue">
                  <xs:annotation>
                    <xs:documentation xml:lang="en">The event occupies a single facility.</xs:documentation>
                  </xs:annotation>
                </xs:enumeration>
                <xs:enumeration value="MultipleVenue">
                  <xs:annotation>
                    <xs:documentation xml:lang="en">The event occupies several facilities in the city.</xs:documentation>
                  </xs:annotation>
                </xs:enumeration>
              </xs:restriction>
            </xs:simpleType>
          </xs:attribute>
          <xs:attribute name="OffsiteVenueIndicator" type="xs:boolean" use="optional">
            <xs:annotation>
              <xs:documentation xml:lang="en">When true, a venue off-site was utilized.</xs:documentation>
            </xs:annotation>
          </xs:attribute>
          <xs:attribute name="ShuttleServiceIndicator" type="xs:boolean" use="optional">
            <xs:annotation>
              <xs:documentation xml:lang="en">When true, shuttle service was provided.</xs:documentation>
            </xs:annotation>
          </xs:attribute>
          <xs:attribute name="DestinationServiceProviderCode" type="ListOfOTA_CodeType" use="optional">
            <xs:annotation>
              <xs:documentation xml:lang="en">Identifies the type of desitnation service provider used. Refer to OpenTravel Code List Destination Service Provider Type (DSP).</xs:documentation>
            </xs:annotation>
          </xs:attribute>
          <xs:attribute name="AttendeeGuestProgramIndicator" type="xs:boolean" use="optional">
            <xs:annotation>
              <xs:documentation xml:lang="en">When true, indicates organized programs were available to attendee's guests (e.g., a tour).</xs:documentation>
            </xs:annotation>
          </xs:attribute>
          <xs:attribute name="EventReportRPH" type="ListOfRPH" use="optional">
            <xs:annotation>
              <xs:documentation xml:lang="en">References the other events that were held in conjunction with (ICW) this event as identified by the RPH located in their EventReport element.</xs:documentation>
            </xs:annotation>
          </xs:attribute>
        </xs:complexType>
      </xs:element>
    </xs:sequence>
    <xs:attribute name="Version" type="xs:decimal" use="optional">
      <xs:annotation>
        <xs:documentation xml:lang="en">Identifies the version of the report.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="TimeStamp" type="xs:dateTime" use="optional">
      <xs:annotation>
        <xs:documentation xml:lang="en">Time stamp of when this version of the report was completed.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="RPH" type="RPH_Type" use="optional">
      <xs:annotation>
        <xs:documentation xml:lang="en">This Reference Place Holder (RPH) is an index code used to identify an instance of the event report.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
  </xs:complexType>
  <xs:complexType name="PostEventSiteReportType">
    <xs:annotation>
      <xs:documentation>Provides event site information.</xs:documentation>
    </xs:annotation>
    <xs:sequence>
      <xs:element name="ResponseType" type="RFP_ResponseDetailType" minOccurs="0">
        <xs:annotation>
          <xs:documentation>This is used to send information about the data that will be returned, specifically when and in what delivery mode.</xs:documentation>
        </xs:annotation>
      </xs:element>
      <xs:element name="Event_ID" minOccurs="0" maxOccurs="9">
        <xs:annotation>
          <xs:documentation xml:lang="en">This is a UniqueID that is associated with an individual event. This element repeats to accommodate the IDs of multiple systems.</xs:documentation>
        </xs:annotation>
        <xs:complexType>
          <xs:complexContent>
            <xs:extension base="UniqueID_Type">
              <xs:attribute name="MeetingName" type="xs:string" use="optional">
                <xs:annotation>
                  <xs:documentation xml:lang="en">This is used to identify the name of the meeting in the request.</xs:documentation>
                </xs:annotation>
              </xs:attribute>
            </xs:extension>
          </xs:complexContent>
        </xs:complexType>
      </xs:element>
      <xs:element name="Date" minOccurs="0">
        <xs:annotation>
          <xs:documentation xml:lang="en">Date or date range for this event for this site. This date range encompasses any pre-event, published and post event dates.</xs:documentation>
        </xs:annotation>
        <xs:complexType>
          <xs:attributeGroup ref="DateTimeSpanGroup">
            <xs:annotation>
              <xs:documentation xml:lang="en">Date or date range for this event for this site. These dates include pre-event, published and post event dates.</xs:documentation>
            </xs:annotation>
          </xs:attributeGroup>
        </xs:complexType>
      </xs:element>
      <xs:element name="Contacts" minOccurs="0">
        <xs:annotation>
          <xs:documentation xml:lang="en">A collection of contacts associated with a specific site.</xs:documentation>
        </xs:annotation>
        <xs:complexType>
          <xs:sequence>
            <xs:element name="Contact" maxOccurs="99">
              <xs:annotation>
                <xs:documentation xml:lang="en">Contact information as well as site address information. </xs:documentation>
              </xs:annotation>
              <xs:complexType>
                <xs:complexContent>
                  <xs:extension base="ContactPersonType">
                    <xs:sequence>
                      <xs:element name="Amenities" minOccurs="0">
                        <xs:annotation>
                          <xs:documentation xml:lang="en">A collection of amenities.</xs:documentation>
                        </xs:annotation>
                        <xs:complexType>
                          <xs:sequence>
                            <xs:element name="Amenity" type="RoomAmenityPrefType" maxOccurs="99">
                              <xs:annotation>
                                <xs:documentation xml:lang="en">An amenity for the contact.</xs:documentation>
                              </xs:annotation>
                            </xs:element>
                          </xs:sequence>
                        </xs:complexType>
                      </xs:element>
                      <xs:element name="Comments" minOccurs="0">
                        <xs:annotation>
                          <xs:documentation xml:lang="en">A collection of comments.</xs:documentation>
                        </xs:annotation>
                        <xs:complexType>
                          <xs:sequence>
                            <xs:element name="Comment" type="ParagraphType" maxOccurs="99">
                              <xs:annotation>
                                <xs:documentation xml:lang="en">Used to provide additional information about the contact or the contact organization.</xs:documentation>
                              </xs:annotation>
                            </xs:element>
                          </xs:sequence>
                        </xs:complexType>
                      </xs:element>
                    </xs:sequence>
                    <xs:attribute name="VIP_Indicator" type="xs:boolean" use="optional">
                      <xs:annotation>
                        <xs:documentation xml:lang="en">When true, the contact is a very important person.</xs:documentation>
                      </xs:annotation>
                    </xs:attribute>
                    <xs:attribute name="ArrivalDate" type="DateOrTimeOrDateTimeType" use="optional">
                      <xs:annotation>
                        <xs:documentation xml:lang="en">The arrival date of this contact.</xs:documentation>
                      </xs:annotation>
                    </xs:attribute>
                    <xs:attribute name="DepartureDate" type="DateOrTimeOrDateTimeType" use="optional">
                      <xs:annotation>
                        <xs:documentation xml:lang="en">The departure date of this contact.</xs:documentation>
                      </xs:annotation>
                    </xs:attribute>
                  </xs:extension>
                </xs:complexContent>
              </xs:complexType>
            </xs:element>
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="AttendeeInfo" minOccurs="0" maxOccurs="3">
        <xs:annotation>
          <xs:documentation xml:lang="en">The number of attendees by type at this site for this event.</xs:documentation>
        </xs:annotation>
        <xs:complexType>
          <xs:sequence>
            <xs:element name="Comments" minOccurs="0">
              <xs:annotation>
                <xs:documentation xml:lang="en">A collection of comments pertaining to the attendees.</xs:documentation>
              </xs:annotation>
              <xs:complexType>
                <xs:sequence>
                  <xs:element name="Comment" type="ParagraphType" maxOccurs="99">
                    <xs:annotation>
                      <xs:documentation xml:lang="en">Comments about the attendees as a group (e.g., accessibility or special needs or demographic profile comments).</xs:documentation>
                    </xs:annotation>
                  </xs:element>
                </xs:sequence>
              </xs:complexType>
            </xs:element>
          </xs:sequence>
          <xs:attributeGroup ref="PostEventAttendanceGroup">
            <xs:annotation>
              <xs:documentation xml:lang="en">Attendance quantities qualifed as estimated, guaranteed or actual.</xs:documentation>
            </xs:annotation>
          </xs:attributeGroup>
        </xs:complexType>
      </xs:element>
      <xs:element name="EventDays" minOccurs="0">
        <xs:annotation>
          <xs:documentation xml:lang="en">A collection of event days.</xs:documentation>
        </xs:annotation>
        <xs:complexType>
          <xs:sequence>
            <xs:element name="EventDay" maxOccurs="unbounded">
              <xs:annotation>
                <xs:documentation xml:lang="en">Defines the functions for a specific day of the event.</xs:documentation>
              </xs:annotation>
              <xs:complexType>
                <xs:sequence>
                  <xs:element name="EventDayFunctions" minOccurs="0">
                    <xs:annotation>
                      <xs:documentation xml:lang="en">A collection of multiple EventDayFunctions.</xs:documentation>
                    </xs:annotation>
                    <xs:complexType>
                      <xs:sequence>
                        <xs:element name="EventDayFunction" maxOccurs="unbounded">
                          <xs:annotation>
                            <xs:documentation xml:lang="en">The requirements for the function.</xs:documentation>
                          </xs:annotation>
                          <xs:complexType>
                            <xs:sequence minOccurs="0">
                              <xs:element name="Contacts" minOccurs="0">
                                <xs:annotation>
                                  <xs:documentation xml:lang="en">A collection of contacts associated with the function.</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                  <xs:sequence>
                                    <xs:element name="Contact" maxOccurs="unbounded">
                                      <xs:annotation>
                                        <xs:documentation xml:lang="en">A contact associated with the function.</xs:documentation>
                                      </xs:annotation>
                                      <xs:complexType>
                                        <xs:complexContent>
                                          <xs:extension base="ContactPersonType">
                                            <xs:sequence>
                                              <xs:element name="Comments" minOccurs="0">
                                                <xs:annotation>
                                                  <xs:documentation xml:lang="en">A collection of comments.</xs:documentation>
                                                </xs:annotation>
                                                <xs:complexType>
                                                  <xs:sequence>
                                                    <xs:element name="Comment" type="ParagraphType" maxOccurs="unbounded">
                                                      <xs:annotation>
                                                        <xs:documentation xml:lang="en">A comment associated to the contact.</xs:documentation>
                                                      </xs:annotation>
                                                    </xs:element>
                                                  </xs:sequence>
                                                </xs:complexType>
                                              </xs:element>
                                            </xs:sequence>
                                          </xs:extension>
                                        </xs:complexContent>
                                      </xs:complexType>
                                    </xs:element>
                                  </xs:sequence>
                                </xs:complexType>
                              </xs:element>
                              <xs:element name="AudioVisuals" minOccurs="0">
                                <xs:annotation>
                                  <xs:documentation xml:lang="en">A collection of audio visual needs for a function. </xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                  <xs:sequence>
                                    <xs:element name="AudioVisual" maxOccurs="unbounded">
                                      <xs:annotation>
                                        <xs:documentation xml:lang="en">Defines a specific audio visual need and its charge for this function.</xs:documentation>
                                      </xs:annotation>
                                      <xs:complexType>
                                        <xs:sequence>
                                          <xs:element name="Comments" minOccurs="0">
                                            <xs:annotation>
                                              <xs:documentation xml:lang="en">A collection of comments associated with the audio visual item.</xs:documentation>
                                            </xs:annotation>
                                            <xs:complexType>
                                              <xs:sequence>
                                                <xs:element name="Comment" type="ParagraphType" maxOccurs="99">
                                                  <xs:annotation>
                                                    <xs:documentation xml:lang="en">A comment associated with the audio visual item.</xs:documentation>
                                                  </xs:annotation>
                                                </xs:element>
                                              </xs:sequence>
                                            </xs:complexType>
                                          </xs:element>
                                        </xs:sequence>
                                        <xs:attribute name="Code" type="OTA_CodeType" use="optional">
                                          <xs:annotation>
                                            <xs:documentation xml:lang="en">The specific audio visual requirement being defined. Refer to OpenTravel Code Table Meeting Room code (MRC).</xs:documentation>
                                          </xs:annotation>
                                        </xs:attribute>
                                        <xs:attribute name="AudioVisualPref" type="PreferLevelType" use="optional">
                                          <xs:annotation>
                                            <xs:documentation xml:lang="en">Defines the preference for the audio visual item (e.g., preferred, required).</xs:documentation>
                                          </xs:annotation>
                                        </xs:attribute>
                                        <xs:attributeGroup ref="QuantityGroup">
                                          <xs:annotation>
                                            <xs:documentation xml:lang="en">The number of this audio visual item.</xs:documentation>
                                          </xs:annotation>
                                        </xs:attributeGroup>
                                        <xs:attributeGroup ref="CurrencyAmountGroup">
                                          <xs:annotation>
                                            <xs:documentation xml:lang="en">The amount charged for each audio visual item.</xs:documentation>
                                          </xs:annotation>
                                        </xs:attributeGroup>
                                      </xs:complexType>
                                    </xs:element>
                                  </xs:sequence>
                                  <xs:attributeGroup ref="ProviderGroup">
                                    <xs:annotation>
                                      <xs:documentation xml:lang="en">Identifies the audio visual provider.</xs:documentation>
                                    </xs:annotation>
                                  </xs:attributeGroup>
                                </xs:complexType>
                              </xs:element>
                              <xs:element name="RoomSetup" minOccurs="0">
                                <xs:annotation>
                                  <xs:documentation xml:lang="en">The room setup for the function.</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                  <xs:attribute name="RoomSetupCode" type="OTA_CodeType" use="optional">
                                    <xs:annotation>
                                      <xs:documentation xml:lang="en">Indicates the arrangement of the function space (e.g. conference or banquet style). Refer to OpenTravel Code List Meeting Room Format Code (MRF).</xs:documentation>
                                    </xs:annotation>
                                  </xs:attribute>
                                  <xs:attribute name="SetForQuantity" type="xs:nonNegativeInteger" use="optional">
                                    <xs:annotation>
                                      <xs:documentation xml:lang="en">The number of people for whom the room should be set.</xs:documentation>
                                    </xs:annotation>
                                  </xs:attribute>
                                </xs:complexType>
                              </xs:element>
                              <xs:element name="SessionTimes" minOccurs="0" maxOccurs="5">
                                <xs:annotation>
                                  <xs:documentation xml:lang="en">The start and end times for a session or show within a function.</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                  <xs:attribute name="StartTime" type="xs:time" use="optional">
                                    <xs:annotation>
                                      <xs:documentation xml:lang="en">The time a session is scheduled to begin.</xs:documentation>
                                    </xs:annotation>
                                  </xs:attribute>
                                  <xs:attribute name="EndTime" type="xs:time" use="optional">
                                    <xs:annotation>
                                      <xs:documentation xml:lang="en">The time a session is scheduled to finish.</xs:documentation>
                                    </xs:annotation>
                                  </xs:attribute>
                                </xs:complexType>
                              </xs:element>
                              <xs:element name="FoodAndBeverages" minOccurs="0">
                                <xs:annotation>
                                  <xs:documentation xml:lang="en">A collection of food and beverage details for a function.</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                  <xs:sequence>
                                    <xs:element name="FoodAndBeverage" maxOccurs="unbounded">
                                      <xs:annotation>
                                        <xs:documentation xml:lang="en">Food and beverage details for a function.</xs:documentation>
                                      </xs:annotation>
                                      <xs:complexType>
                                        <xs:sequence>
                                          <xs:element name="Comments" minOccurs="0">
                                            <xs:annotation>
                                              <xs:documentation xml:lang="en">A collection of comments associated with the food and beverage requirement.</xs:documentation>
                                            </xs:annotation>
                                            <xs:complexType>
                                              <xs:sequence>
                                                <xs:element name="Comment" type="ParagraphType" maxOccurs="99">
                                                  <xs:annotation>
                                                    <xs:documentation xml:lang="en">A comment associated with the food and beverage requirement.</xs:documentation>
                                                  </xs:annotation>
                                                </xs:element>
                                              </xs:sequence>
                                            </xs:complexType>
                                          </xs:element>
                                          <xs:element name="Menus" minOccurs="0">
                                            <xs:annotation>
                                              <xs:documentation xml:lang="en">A collection of menus.</xs:documentation>
                                            </xs:annotation>
                                            <xs:complexType>
                                              <xs:sequence>
                                                <xs:element name="Menu" maxOccurs="99">
                                                  <xs:annotation>
                                                    <xs:documentation xml:lang="en">Menu and menu charges.</xs:documentation>
                                                  </xs:annotation>
                                                  <xs:complexType>
                                                    <xs:sequence>
                                                      <xs:element name="Comments" minOccurs="0">
                                                        <xs:annotation>
                                                          <xs:documentation xml:lang="en">A collection of comments associated with the menu requirement.</xs:documentation>
                                                        </xs:annotation>
                                                        <xs:complexType>
                                                          <xs:sequence>
                                                            <xs:element name="Comment" type="ParagraphType" maxOccurs="99">
                                                              <xs:annotation>
                                                                <xs:documentation xml:lang="en">A comment associated with the menu requirement.</xs:documentation>
                                                              </xs:annotation>
                                                            </xs:element>
                                                          </xs:sequence>
                                                        </xs:complexType>
                                                      </xs:element>
                                                    </xs:sequence>
                                                    <xs:attribute name="Name" type="StringLength1to128" use="optional">
                                                      <xs:annotation>
                                                        <xs:documentation xml:lang="en">The name of a set menu or menu item.</xs:documentation>
                                                      </xs:annotation>
                                                    </xs:attribute>
                                                    <xs:attributeGroup ref="CurrencyAmountGroup">
                                                      <xs:annotation>
                                                        <xs:documentation xml:lang="en">The charge for this menu or menu item.</xs:documentation>
                                                      </xs:annotation>
                                                    </xs:attributeGroup>
                                                    <xs:attribute name="ChargeUnit" type="OTA_CodeType" use="optional">
                                                      <xs:annotation>
                                                        <xs:documentation xml:lang="en">Defines how the charge is applied (e.g. per person, per gallon, per tray). Use OpenTravel Code list Charge Type (CHG).</xs:documentation>
                                                      </xs:annotation>
                                                    </xs:attribute>
                                                    <xs:attribute name="Quantity" type="xs:decimal" use="optional">
                                                      <xs:annotation>
                                                        <xs:documentation xml:lang="en">The requested quantity for this menu or menu item.</xs:documentation>
                                                      </xs:annotation>
                                                    </xs:attribute>
                                                  </xs:complexType>
                                                </xs:element>
                                              </xs:sequence>
                                            </xs:complexType>
                                          </xs:element>
                                        </xs:sequence>
                                        <xs:attribute name="SetForQuantity" type="xs:nonNegativeInteger" use="optional">
                                          <xs:annotation>
                                            <xs:documentation xml:lang="en">The number of settings required.</xs:documentation>
                                          </xs:annotation>
                                        </xs:attribute>
                                        <xs:attribute name="ServiceTime" type="DateOrTimeOrDateTimeType" use="optional">
                                          <xs:annotation>
                                            <xs:documentation xml:lang="en">The time or date and time of food and beverage service.</xs:documentation>
                                          </xs:annotation>
                                        </xs:attribute>
                                        <xs:attribute name="AttendeeQuantity" type="xs:nonNegativeInteger" use="optional">
                                          <xs:annotation>
                                            <xs:documentation xml:lang="en">The number of people served.</xs:documentation>
                                          </xs:annotation>
                                        </xs:attribute>
                                        <xs:attribute name="GuaranteeQuantity" type="xs:nonNegativeInteger" use="optional">
                                          <xs:annotation>
                                            <xs:documentation xml:lang="en">The number of people guaranteed.</xs:documentation>
                                          </xs:annotation>
                                        </xs:attribute>
                                        <xs:attribute name="MealTypeCode" type="OTA_CodeType" use="optional">
                                          <xs:annotation>
                                            <xs:documentation xml:lang="en">The type of meal being served. Refer to OpenTravel Code list Available Meal Category (AMC).</xs:documentation>
                                          </xs:annotation>
                                        </xs:attribute>
                                        <xs:attribute name="ServiceTypeCode" type="OTA_CodeType" use="optional">
                                          <xs:annotation>
                                            <xs:documentation xml:lang="en">The type of food and beverage service being provided (e.g., buffet, plated). Refer to OpenTravel Code list Event Charge (EVT).</xs:documentation>
                                          </xs:annotation>
                                        </xs:attribute>
                                      </xs:complexType>
                                    </xs:element>
                                  </xs:sequence>
                                  <xs:attributeGroup ref="ProviderGroup">
                                    <xs:annotation>
                                      <xs:documentation xml:lang="en">Identifies who will be providing the food and beverage for the function.</xs:documentation>
                                    </xs:annotation>
                                  </xs:attributeGroup>
                                </xs:complexType>
                              </xs:element>
                              <xs:element name="Services" minOccurs="0">
                                <xs:annotation>
                                  <xs:documentation xml:lang="en">A collection of services.</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                  <xs:sequence>
                                    <xs:element name="Service" maxOccurs="unbounded">
                                      <xs:annotation>
                                        <xs:documentation xml:lang="en">Details regarding an additional service (e.g., security, first aid).</xs:documentation>
                                      </xs:annotation>
                                      <xs:complexType>
                                        <xs:sequence>
                                          <xs:element name="Utilities" minOccurs="0">
                                            <xs:annotation>
                                              <xs:documentation xml:lang="en">A collection of utilities.</xs:documentation>
                                            </xs:annotation>
                                            <xs:complexType>
                                              <xs:sequence>
                                                <xs:element name="Utility" maxOccurs="99">
                                                  <xs:annotation>
                                                    <xs:documentation xml:lang="en">A utility requirement for this function.</xs:documentation>
                                                  </xs:annotation>
                                                  <xs:complexType>
                                                    <xs:sequence>
                                                      <xs:element name="Comments" minOccurs="0">
                                                        <xs:annotation>
                                                          <xs:documentation xml:lang="en">A collection of comments.</xs:documentation>
                                                        </xs:annotation>
                                                        <xs:complexType>
                                                          <xs:sequence>
                                                            <xs:element name="Comment" type="ParagraphType" maxOccurs="99">
                                                              <xs:annotation>
                                                                <xs:documentation xml:lang="en">A comment associated to the service.</xs:documentation>
                                                              </xs:annotation>
                                                            </xs:element>
                                                          </xs:sequence>
                                                        </xs:complexType>
                                                      </xs:element>
                                                    </xs:sequence>
                                                    <xs:attribute name="Code" type="OTA_CodeType" use="optional">
                                                      <xs:annotation>
                                                        <xs:documentation xml:lang="en">The type of this utility (e.g., air, drain, natural gas). Refer to OpenTravel Code list Utility Service Code (USC).</xs:documentation>
                                                      </xs:annotation>
                                                    </xs:attribute>
                                                    <xs:attributeGroup ref="QuantityGroup">
                                                      <xs:annotation>
                                                        <xs:documentation xml:lang="en">The requested number of this utility item.</xs:documentation>
                                                      </xs:annotation>
                                                    </xs:attributeGroup>
                                                    <xs:attributeGroup ref="CurrencyAmountGroup">
                                                      <xs:annotation>
                                                        <xs:documentation xml:lang="en">The amount charged for the utility item.</xs:documentation>
                                                      </xs:annotation>
                                                    </xs:attributeGroup>
                                                    <xs:attribute name="Name" type="StringLength1to128" use="optional">
                                                      <xs:annotation>
                                                        <xs:documentation xml:lang="en">The name of the utility.</xs:documentation>
                                                      </xs:annotation>
                                                    </xs:attribute>
                                                    <xs:attribute name="TelecommunicationCode" type="OTA_CodeType" use="optional">
                                                      <xs:annotation>
                                                        <xs:documentation xml:lang="en">A telecommunication service (e.g., analog phone line, ethernet connection). Refer to OpenTravel Code list Business Srvc Type (BUS).</xs:documentation>
                                                      </xs:annotation>
                                                    </xs:attribute>
                                                    <xs:attribute name="Description" type="StringLength1to64" use="optional">
                                                      <xs:annotation>
                                                        <xs:documentation xml:lang="en">A description of the utility.</xs:documentation>
                                                      </xs:annotation>
                                                    </xs:attribute>
                                                  </xs:complexType>
                                                </xs:element>
                                              </xs:sequence>
                                            </xs:complexType>
                                          </xs:element>
                                          <xs:element name="Comments" minOccurs="0">
                                            <xs:annotation>
                                              <xs:documentation xml:lang="en">A collection of comments.</xs:documentation>
                                            </xs:annotation>
                                            <xs:complexType>
                                              <xs:sequence>
                                                <xs:element name="Comment" type="ParagraphType" maxOccurs="99">
                                                  <xs:annotation>
                                                    <xs:documentation xml:lang="en">A comment associated to the service.</xs:documentation>
                                                  </xs:annotation>
                                                </xs:element>
                                              </xs:sequence>
                                            </xs:complexType>
                                          </xs:element>
                                        </xs:sequence>
                                        <xs:attributeGroup ref="ProviderGroup">
                                          <xs:annotation>
                                            <xs:documentation xml:lang="en">Used to define who will provide the service.</xs:documentation>
                                          </xs:annotation>
                                        </xs:attributeGroup>
                                        <xs:attribute name="LocationName" type="StringLength1to64" use="optional">
                                          <xs:annotation>
                                            <xs:documentation xml:lang="en">Identifies a location (e.g. individual hotel room, hotel lobby, exterior, pool).</xs:documentation>
                                          </xs:annotation>
                                        </xs:attribute>
                                        <xs:attribute name="StartTime" type="xs:time" use="optional">
                                          <xs:annotation>
                                            <xs:documentation xml:lang="en">The time the service is scheduled to begin.</xs:documentation>
                                          </xs:annotation>
                                        </xs:attribute>
                                        <xs:attribute name="EndTime" type="xs:time" use="optional">
                                          <xs:annotation>
                                            <xs:documentation xml:lang="en">The time the service is scheduled to finish.</xs:documentation>
                                          </xs:annotation>
                                        </xs:attribute>
                                        <xs:attribute name="Code" type="OTA_CodeType" use="optional">
                                          <xs:annotation>
                                            <xs:documentation xml:lang="en">Identifies a type of service (e.g. security, first aid, decor). Refer to OpenTravel Codelist Meeting Room Code (MRC).</xs:documentation>
                                          </xs:annotation>
                                        </xs:attribute>
                                        <xs:attribute name="CodeDetail" type="StringLength1to64" use="optional">
                                          <xs:annotation>
                                            <xs:documentation xml:lang="en">May be used to give further detail on the code. </xs:documentation>
                                          </xs:annotation>
                                        </xs:attribute>
                                      </xs:complexType>
                                    </xs:element>
                                  </xs:sequence>
                                </xs:complexType>
                              </xs:element>
                              <xs:element name="FunctionCharges" minOccurs="0">
                                <xs:annotation>
                                  <xs:documentation xml:lang="en">A collection of charges related to this function.</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                  <xs:sequence>
                                    <xs:element name="FunctionCharge" maxOccurs="unbounded">
                                      <xs:annotation>
                                        <xs:documentation xml:lang="en">A summary of a specific charge associated with a function (i.e., total audiovisual charges for the function).</xs:documentation>
                                      </xs:annotation>
                                      <xs:complexType>
                                        <xs:sequence>
                                          <xs:element name="Taxes" type="TaxesType" minOccurs="0"/>
                                          <xs:element name="Comment" type="ParagraphType" minOccurs="0"/>
                                        </xs:sequence>
                                        <xs:attribute name="FunctionChargeCode" type="OTA_CodeType" use="optional">
                                          <xs:annotation>
                                            <xs:documentation xml:lang="en">This describes charges related to a function (e.g. set-up; room rental; break-out; breakfast; lunch; dinner; miscellaneous). Refer to OpenTravel Code List Event Charge (EVT).</xs:documentation>
                                          </xs:annotation>
                                        </xs:attribute>
                                        <xs:attributeGroup ref="CurrencyAmountGroup">
                                          <xs:annotation>
                                            <xs:documentation xml:lang="en">Actual charge for the service identified by the function charge code.</xs:documentation>
                                          </xs:annotation>
                                        </xs:attributeGroup>
                                      </xs:complexType>
                                    </xs:element>
                                  </xs:sequence>
                                </xs:complexType>
                              </xs:element>
                              <xs:element name="Comments" minOccurs="0">
                                <xs:annotation>
                                  <xs:documentation xml:lang="en">A collection of comments associated to the function.</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                  <xs:sequence>
                                    <xs:element name="Comment" type="ParagraphType" maxOccurs="99">
                                      <xs:annotation>
                                        <xs:documentation xml:lang="en">A comment associated to the function.</xs:documentation>
                                      </xs:annotation>
                                    </xs:element>
                                  </xs:sequence>
                                </xs:complexType>
                              </xs:element>
                            </xs:sequence>
                            <xs:attribute name="FunctionName" type="StringLength1to64" use="optional">
                              <xs:annotation>
                                <xs:documentation xml:lang="en">The name of the function.</xs:documentation>
                              </xs:annotation>
                            </xs:attribute>
                            <xs:attribute name="FunctionType" type="OTA_CodeType" use="optional">
                              <xs:annotation>
                                <xs:documentation xml:lang="en">The type of function being requested (e.g. breakfast meeting or reception). Refer to OpenTravel Code table Event Charge (EVT).</xs:documentation>
                              </xs:annotation>
                            </xs:attribute>
                            <xs:attribute name="LocationName" type="StringLength1to64" use="optional">
                              <xs:annotation>
                                <xs:documentation xml:lang="en">Identifies a location (e.g. individual hotel room, hotel lobby, exterior, pool) for the function.</xs:documentation>
                              </xs:annotation>
                            </xs:attribute>
                            <xs:attribute name="LocationID" type="StringLength1to32" use="optional">
                              <xs:annotation>
                                <xs:documentation xml:lang="en">The ID of the room where the function is taking place.</xs:documentation>
                              </xs:annotation>
                            </xs:attribute>
                            <xs:attribute name="TwentyFourHourHoldInd" type="xs:boolean" use="optional">
                              <xs:annotation>
                                <xs:documentation xml:lang="en">When true, a location is held on a 24-hour basis (e.g., ensures a set-up is not disturbed).</xs:documentation>
                              </xs:annotation>
                            </xs:attribute>
                            <xs:attribute name="StartTime" type="xs:time" use="optional">
                              <xs:annotation>
                                <xs:documentation xml:lang="en">The time the function is scheduled to begin.</xs:documentation>
                              </xs:annotation>
                            </xs:attribute>
                            <xs:attribute name="EndTime" type="xs:time" use="optional">
                              <xs:annotation>
                                <xs:documentation xml:lang="en">The time the function is scheduled to finish.</xs:documentation>
                              </xs:annotation>
                            </xs:attribute>
                            <xs:attribute name="AttendeeQuantity" type="xs:nonNegativeInteger" use="optional">
                              <xs:annotation>
                                <xs:documentation xml:lang="en">The number of people attending this function.</xs:documentation>
                              </xs:annotation>
                            </xs:attribute>
                            <xs:attribute name="RequiredKeyQuantity" type="xs:nonNegativeInteger" use="optional">
                              <xs:annotation>
                                <xs:documentation xml:lang="en">The number of keys required for this function space.</xs:documentation>
                              </xs:annotation>
                            </xs:attribute>
                            <xs:attribute name="ReKeyIndicator" type="xs:boolean" use="optional">
                              <xs:annotation>
                                <xs:documentation xml:lang="en">When true, indicates the room lock needs to be rekeyed.</xs:documentation>
                              </xs:annotation>
                            </xs:attribute>
                            <xs:attribute name="PostIndicator" type="xs:boolean" use="optional">
                              <xs:annotation>
                                <xs:documentation xml:lang="en">When true, indicates the function was posted (e.g., on a reader board).</xs:documentation>
                              </xs:annotation>
                            </xs:attribute>
                            <xs:attribute name="ID" type="StringLength1to32" use="optional">
                              <xs:annotation>
                                <xs:documentation xml:lang="en">An identifier for this function.</xs:documentation>
                              </xs:annotation>
                            </xs:attribute>
                            <xs:attribute name="ExhibitionIndicator" type="xs:boolean" use="optional">
                              <xs:annotation>
                                <xs:documentation xml:lang="en">When true, indicates this function was an exhibition.</xs:documentation>
                              </xs:annotation>
                            </xs:attribute>
                          </xs:complexType>
                        </xs:element>
                      </xs:sequence>
                    </xs:complexType>
                  </xs:element>
                </xs:sequence>
                <xs:attribute name="DayNumber" type="xs:positiveInteger" use="required">
                  <xs:annotation>
                    <xs:documentation xml:lang="en">The number which indicates the day of the event (e.g. 1, 2, 3). This is relative to the Start attribute in the Date element.</xs:documentation>
                  </xs:annotation>
                </xs:attribute>
                <xs:attribute name="DayType" type="EventDayType" use="optional">
                  <xs:annotation>
                    <xs:documentation xml:lang="en">Indicates the day is a pre-event day, a post event day or a published event day.</xs:documentation>
                  </xs:annotation>
                </xs:attribute>
              </xs:complexType>
            </xs:element>
          </xs:sequence>
          <xs:attribute name="FirstEventDayOfWeek" type="DayOfWeekType" use="optional">
            <xs:annotation>
              <xs:documentation xml:lang="en">This is used to identify the first day of the week based on the start date provided in the Date element.</xs:documentation>
            </xs:annotation>
          </xs:attribute>
        </xs:complexType>
      </xs:element>
      <xs:element name="Exhibition" minOccurs="0" maxOccurs="5">
        <xs:annotation>
          <xs:documentation xml:lang="en">Describes an event at which products and services are displayed.</xs:documentation>
        </xs:annotation>
        <xs:complexType>
          <xs:sequence>
            <xs:element name="ExhibitDetails" minOccurs="0">
              <xs:annotation>
                <xs:documentation xml:lang="en">A collection of exhibit details.</xs:documentation>
              </xs:annotation>
              <xs:complexType>
                <xs:sequence>
                  <xs:element name="ExhibitDetail" maxOccurs="unbounded">
                    <xs:annotation>
                      <xs:documentation xml:lang="en">Describes a single exhibit within an exhibitiion.</xs:documentation>
                    </xs:annotation>
                    <xs:complexType>
                      <xs:sequence>
                        <xs:element name="Contacts" minOccurs="0">
                          <xs:annotation>
                            <xs:documentation xml:lang="en">A collection of contacts for an exhibit.</xs:documentation>
                          </xs:annotation>
                          <xs:complexType>
                            <xs:sequence>
                              <xs:element name="Contact" type="ContactPersonType" maxOccurs="99">
                                <xs:annotation>
                                  <xs:documentation xml:lang="en">Contact information pertaining to an exhibit.</xs:documentation>
                                </xs:annotation>
                              </xs:element>
                            </xs:sequence>
                          </xs:complexType>
                        </xs:element>
                      </xs:sequence>
                      <xs:attribute name="ExhibitTypeCode" type="OTA_CodeType" use="optional">
                        <xs:annotation>
                          <xs:documentation xml:lang="en">Type of booths for the entire exhibition. Refer to OpenTravel Code Table Exhibit Type (EXH).</xs:documentation>
                        </xs:annotation>
                      </xs:attribute>
                      <xs:attributeGroup ref="DimensionGroup">
                        <xs:annotation>
                          <xs:documentation xml:lang="en">The dimensions of the booth. </xs:documentation>
                        </xs:annotation>
                      </xs:attributeGroup>
                    </xs:complexType>
                  </xs:element>
                </xs:sequence>
                <xs:attribute name="FoodAndBeverageIndicator" type="xs:boolean" use="optional">
                  <xs:annotation>
                    <xs:documentation xml:lang="en">When true, indicates one or more exhibits required food and beverage service.</xs:documentation>
                  </xs:annotation>
                </xs:attribute>
                <xs:attribute name="FoodAndBeverageBoothQuantity" type="xs:nonNegativeInteger" use="optional">
                  <xs:annotation>
                    <xs:documentation xml:lang="en">Number of booths requiring food and beverage service.</xs:documentation>
                  </xs:annotation>
                </xs:attribute>
              </xs:complexType>
            </xs:element>
            <xs:element name="ExhibitorInfo" minOccurs="0">
              <xs:annotation>
                <xs:documentation xml:lang="en">Exhibitor (i.e., the people working the booths) information for the event.</xs:documentation>
              </xs:annotation>
              <xs:complexType>
                <xs:sequence>
                  <xs:element name="Comments" minOccurs="0">
                    <xs:annotation>
                      <xs:documentation xml:lang="en">A collection of comments pertaining to the exhibitors.</xs:documentation>
                    </xs:annotation>
                    <xs:complexType>
                      <xs:sequence>
                        <xs:element name="Comment" type="ParagraphType" maxOccurs="99">
                          <xs:annotation>
                            <xs:documentation xml:lang="en">A comment about the exhibitors as a group (e.g., accessibility or special needs or demographic profile comments).</xs:documentation>
                          </xs:annotation>
                        </xs:element>
                      </xs:sequence>
                    </xs:complexType>
                  </xs:element>
                </xs:sequence>
                <xs:attributeGroup ref="PostEventAttendanceGroup">
                  <xs:annotation>
                    <xs:documentation xml:lang="en">Attendance information for the exhibitors.</xs:documentation>
                  </xs:annotation>
                </xs:attributeGroup>
              </xs:complexType>
            </xs:element>
            <xs:element name="AdditionalDates" minOccurs="0">
              <xs:annotation>
                <xs:documentation xml:lang="en">A collection of additional dates.</xs:documentation>
              </xs:annotation>
              <xs:complexType>
                <xs:sequence>
                  <xs:element name="AdditionalDate" maxOccurs="unbounded">
                    <xs:annotation>
                      <xs:documentation xml:lang="en">Date information petaining to the exhibition.</xs:documentation>
                    </xs:annotation>
                    <xs:complexType>
                      <xs:attributeGroup ref="DateTimeSpanGroup">
                        <xs:annotation>
                          <xs:documentation xml:lang="en">The start and end dates associated with the Type.</xs:documentation>
                        </xs:annotation>
                      </xs:attributeGroup>
                      <xs:attribute name="Type" use="optional">
                        <xs:annotation>
                          <xs:documentation xml:lang="en">Defines the type of dates (e.g., contacted, exhibitor move in, exhibitor move out).</xs:documentation>
                        </xs:annotation>
                        <xs:simpleType>
                          <xs:restriction base="xs:NMTOKEN">
                            <xs:enumeration value="Contracted">
                              <xs:annotation>
                                <xs:documentation xml:lang="en">The contracted dates for the exhibition.</xs:documentation>
                              </xs:annotation>
                            </xs:enumeration>
                            <xs:enumeration value="ContractorMoveIn">
                              <xs:annotation>
                                <xs:documentation xml:lang="en">The contractor move-in date for the exhibition.</xs:documentation>
                              </xs:annotation>
                            </xs:enumeration>
                            <xs:enumeration value="ContractorMoveOut">
                              <xs:annotation>
                                <xs:documentation xml:lang="en">The contractor move-out date for the exhibition.</xs:documentation>
                              </xs:annotation>
                            </xs:enumeration>
                            <xs:enumeration value="ExhibitorMoveIn">
                              <xs:annotation>
                                <xs:documentation xml:lang="en">The exhibitor move-in date for the exhibition.</xs:documentation>
                              </xs:annotation>
                            </xs:enumeration>
                            <xs:enumeration value="ExhibitorMoveOut">
                              <xs:annotation>
                                <xs:documentation xml:lang="en">The exhibitor move-out date for the exhibition.</xs:documentation>
                              </xs:annotation>
                            </xs:enumeration>
                          </xs:restriction>
                        </xs:simpleType>
                      </xs:attribute>
                    </xs:complexType>
                  </xs:element>
                </xs:sequence>
              </xs:complexType>
            </xs:element>
            <xs:element name="Comments" minOccurs="0">
              <xs:annotation>
                <xs:documentation xml:lang="en">A collection of comments pertaining to the exhibition.</xs:documentation>
              </xs:annotation>
              <xs:complexType>
                <xs:sequence>
                  <xs:element name="Comment" type="ParagraphType" maxOccurs="99">
                    <xs:annotation>
                      <xs:documentation xml:lang="en">A comment pertaining to the exhibition.</xs:documentation>
                    </xs:annotation>
                  </xs:element>
                </xs:sequence>
              </xs:complexType>
            </xs:element>
          </xs:sequence>
          <xs:attribute name="Type" use="optional">
            <xs:annotation>
              <xs:documentation xml:lang="en">Used to designate the exhibition as public, private or public/private.</xs:documentation>
            </xs:annotation>
            <xs:simpleType>
              <xs:restriction base="xs:NMTOKEN">
                <xs:enumeration value="Public">
                  <xs:annotation>
                    <xs:documentation xml:lang="en">Indicates the exhibition is open to the public.</xs:documentation>
                  </xs:annotation>
                </xs:enumeration>
                <xs:enumeration value="Private">
                  <xs:annotation>
                    <xs:documentation xml:lang="en">Indicates the exhibition is private.</xs:documentation>
                  </xs:annotation>
                </xs:enumeration>
                <xs:enumeration value="PublicPrivate">
                  <xs:annotation>
                    <xs:documentation xml:lang="en">Portions of the exhibition are open to the public and portions of the exhibition are private.</xs:documentation>
                  </xs:annotation>
                </xs:enumeration>
              </xs:restriction>
            </xs:simpleType>
          </xs:attribute>
          <xs:attribute name="GrossExhibitionSpace" type="xs:nonNegativeInteger" use="optional">
            <xs:annotation>
              <xs:documentation xml:lang="en">The gross exhibition space required for the event.</xs:documentation>
            </xs:annotation>
          </xs:attribute>
          <xs:attribute name="NetExhibitionSpace" type="xs:nonNegativeInteger" use="optional">
            <xs:annotation>
              <xs:documentation xml:lang="en">The net exhibition space required for the event.</xs:documentation>
            </xs:annotation>
          </xs:attribute>
          <xs:attribute name="UnitOfMeasureCode" type="OTA_CodeType" use="optional">
            <xs:annotation>
              <xs:documentation xml:lang="en">The unit of measure (e.g., square feet, square meters) in a code format . Refer to OpenTravel Code List Unit of Measure Code (UOM).</xs:documentation>
            </xs:annotation>
          </xs:attribute>
          <xs:attribute name="ExhibitQuantity" type="xs:nonNegativeInteger" use="optional">
            <xs:annotation>
              <xs:documentation xml:lang="en">The number of exhibits expected.</xs:documentation>
            </xs:annotation>
          </xs:attribute>
          <xs:attribute name="CompanyQuantity" type="xs:nonNegativeInteger" use="optional">
            <xs:annotation>
              <xs:documentation xml:lang="en">The number of exhibiting companies expected.</xs:documentation>
            </xs:annotation>
          </xs:attribute>
          <xs:attribute name="GeneralServiceContractorInd" type="xs:boolean" use="optional">
            <xs:annotation>
              <xs:documentation xml:lang="en">When true, a general service contractor (GSC) has been selected. If false, no GSC has been selected.</xs:documentation>
            </xs:annotation>
          </xs:attribute>
          <xs:attribute name="SecuredAreaIndicator" type="xs:boolean" use="optional">
            <xs:annotation>
              <xs:documentation xml:lang="en">When true, indicates the area needs to be secured. When false, the area does not need to be secured.</xs:documentation>
            </xs:annotation>
          </xs:attribute>
        </xs:complexType>
      </xs:element>
      <xs:element name="RoomBlocks" minOccurs="0">
        <xs:annotation>
          <xs:documentation xml:lang="en">A collection of room blocks for this event.</xs:documentation>
        </xs:annotation>
        <xs:complexType>
          <xs:sequence>
            <xs:element name="RoomBlock" maxOccurs="99">
              <xs:annotation>
                <xs:documentation xml:lang="en">Information pertaining to a room block for this event.</xs:documentation>
              </xs:annotation>
              <xs:complexType>
                <xs:sequence>
                  <xs:element name="ReservationMethod" minOccurs="0" maxOccurs="5">
                    <xs:annotation>
                      <xs:documentation xml:lang="en">Information regarding the reservation method.</xs:documentation>
                    </xs:annotation>
                    <xs:complexType>
                      <xs:attribute name="Code" type="OTA_CodeType" use="optional">
                        <xs:annotation>
                          <xs:documentation xml:lang="en">The type of reservation method to be used. Refer to OpenTravel Code list Reservation Method Code (RMC).</xs:documentation>
                        </xs:annotation>
                      </xs:attribute>
                    </xs:complexType>
                  </xs:element>
                  <xs:element name="StayDays" minOccurs="0">
                    <xs:annotation>
                      <xs:documentation xml:lang="en">A collection of stay days.</xs:documentation>
                    </xs:annotation>
                    <xs:complexType>
                      <xs:sequence>
                        <xs:element name="StayDay" maxOccurs="unbounded">
                          <xs:annotation>
                            <xs:documentation xml:lang="en">A particular stay day.</xs:documentation>
                          </xs:annotation>
                          <xs:complexType>
                            <xs:sequence>
                              <xs:element name="Rates" minOccurs="0">
                                <xs:annotation>
                                  <xs:documentation xml:lang="en">Collection of rates for a particular room type.</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                  <xs:sequence>
                                    <xs:element name="Rate" maxOccurs="99">
                                      <xs:annotation>
                                        <xs:documentation xml:lang="en">Rate for a particular room type.</xs:documentation>
                                      </xs:annotation>
                                      <xs:complexType>
                                        <xs:sequence>
                                          <xs:element name="Taxes" type="TaxesType" minOccurs="0">
                                            <xs:annotation>
                                              <xs:documentation xml:lang="en">Taxes related to this room type.</xs:documentation>
                                            </xs:annotation>
                                          </xs:element>
                                          <xs:element name="Fees" type="FeesType" minOccurs="0">
                                            <xs:annotation>
                                              <xs:documentation xml:lang="en">Fees associated with this room type.</xs:documentation>
                                            </xs:annotation>
                                          </xs:element>
                                        </xs:sequence>
                                        <xs:attributeGroup ref="CurrencyAmountGroup"/>
                                        <xs:attribute name="OccupancyRate" use="optional">
                                          <xs:annotation>
                                            <xs:documentation xml:lang="en">The different rates charged for the occupancy for the room which could be flat (a rate not variable by number of people), single, double, triple, or quad.</xs:documentation>
                                          </xs:annotation>
                                          <xs:simpleType>
                                            <xs:restriction base="xs:string">
                                              <xs:enumeration value="Flat"/>
                                              <xs:enumeration value="Single"/>
                                              <xs:enumeration value="Double"/>
                                              <xs:enumeration value="Triple"/>
                                              <xs:enumeration value="Quad"/>
                                            </xs:restriction>
                                          </xs:simpleType>
                                        </xs:attribute>
                                      </xs:complexType>
                                    </xs:element>
                                  </xs:sequence>
                                </xs:complexType>
                              </xs:element>
                              <xs:element name="RoomPickUp" minOccurs="0" maxOccurs="8">
                                <xs:annotation>
                                  <xs:documentation>Individual room pick-up by day or by day and room type.</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                  <xs:attributeGroup ref="RoomPickUpGroup">
                                    <xs:annotation>
                                      <xs:documentation xml:lang="en">Individual room pick-up by day or by day and room type.</xs:documentation>
                                    </xs:annotation>
                                  </xs:attributeGroup>
                                </xs:complexType>
                              </xs:element>
                            </xs:sequence>
                            <xs:attribute name="DayNumber" type="xs:positiveInteger" use="required">
                              <xs:annotation>
                                <xs:documentation xml:lang="en">The number which indicates the day of the stay  (e.g. 1, 2, 3). This is relative to the Start attribute in the Date element.</xs:documentation>
                              </xs:annotation>
                            </xs:attribute>
                            <xs:attribute name="DayType" type="EventDayType" use="optional">
                              <xs:annotation>
                                <xs:documentation xml:lang="en">Indicates the day is a pre-event day, a post event day or a published event day.</xs:documentation>
                              </xs:annotation>
                            </xs:attribute>
                            <xs:attribute name="GuestQuantity" type="xs:nonNegativeInteger" use="optional">
                              <xs:annotation>
                                <xs:documentation xml:lang="en">Total number of guests for this day.</xs:documentation>
                              </xs:annotation>
                            </xs:attribute>
                            <xs:attribute name="TotalNumberOfUnits" type="xs:integer" use="optional">
                              <xs:annotation>
                                <xs:documentation xml:lang="en">Total number of rooms needed for this day.</xs:documentation>
                              </xs:annotation>
                            </xs:attribute>
                            <xs:attribute name="RoomTypeCode" type="StringLength1to16" use="optional">
                              <xs:annotation>
                                <xs:documentation xml:lang="en">A hotel code that indicates the type of room.</xs:documentation>
                              </xs:annotation>
                            </xs:attribute>
                            <xs:attribute name="RoomTypeName" type="StringLength1to32" use="optional">
                              <xs:annotation>
                                <xs:documentation xml:lang="en">Provides the name for the room type or the room type code.</xs:documentation>
                              </xs:annotation>
                            </xs:attribute>
                            <xs:attribute name="RoomType" type="StringLength1to32" use="optional">
                              <xs:annotation>
                                <xs:documentation xml:lang="en">A general room type classification for types of rooms sold (e.g., single, concierge, suite) rather than a hotel specific room type code.</xs:documentation>
                              </xs:annotation>
                            </xs:attribute>
                            <xs:attribute name="ContractedRoomBlock" type="xs:nonNegativeInteger" use="optional">
                              <xs:annotation>
                                <xs:documentation xml:lang="en">The total number of contracted rooms.</xs:documentation>
                              </xs:annotation>
                            </xs:attribute>
                            <xs:attribute name="FinalRoomBlock" type="xs:nonNegativeInteger" use="optional">
                              <xs:annotation>
                                <xs:documentation xml:lang="en">The final number of rooms blocked for this event.  This may be revised from the original contracted number of rooms.</xs:documentation>
                              </xs:annotation>
                            </xs:attribute>
                          </xs:complexType>
                        </xs:element>
                      </xs:sequence>
                      <xs:attribute name="FirstStayDayOfWeek" type="DayOfWeekType" use="optional">
                        <xs:annotation>
                          <xs:documentation xml:lang="en">This is used to identify the first day of the week based on the start date provided in the Date element.</xs:documentation>
                        </xs:annotation>
                      </xs:attribute>
                    </xs:complexType>
                  </xs:element>
                  <xs:element name="TotalRoomPickUp" minOccurs="0" maxOccurs="8">
                    <xs:annotation>
                      <xs:documentation>Total room pick-up for the room block for a specific time parameter.</xs:documentation>
                    </xs:annotation>
                    <xs:complexType>
                      <xs:attributeGroup ref="RoomPickUpGroup">
                        <xs:annotation>
                          <xs:documentation xml:lang="en">Total room pick-up for the room block for a specific time parameter.</xs:documentation>
                        </xs:annotation>
                      </xs:attributeGroup>
                    </xs:complexType>
                  </xs:element>
                </xs:sequence>
                <xs:attribute name="TotalRoomNightQuantity" type="xs:nonNegativeInteger" use="optional">
                  <xs:annotation>
                    <xs:documentation xml:lang="en">Total number of room nights needed. </xs:documentation>
                  </xs:annotation>
                </xs:attribute>
                <xs:attribute name="PeakRoomNightQuantity" type="xs:nonNegativeInteger" use="optional">
                  <xs:annotation>
                    <xs:documentation xml:lang="en">Total number of rooms needed on the peak night. </xs:documentation>
                  </xs:annotation>
                </xs:attribute>
                <xs:attribute name="HousingProviderName" type="StringLength1to255" use="optional">
                  <xs:annotation>
                    <xs:documentation xml:lang="en">A third party housing providor name.</xs:documentation>
                  </xs:annotation>
                </xs:attribute>
                <xs:attribute name="InvBlockCode" type="StringLength1to32" use="optional">
                  <xs:annotation>
                    <xs:documentation xml:lang="en">The group id that identifies this event's room block within a specific hotel.</xs:documentation>
                  </xs:annotation>
                </xs:attribute>
                <xs:attribute name="CompRoomQuantity" type="xs:nonNegativeInteger" use="optional">
                  <xs:annotation>
                    <xs:documentation xml:lang="en">Total number of complimentary room nights used at this property.</xs:documentation>
                  </xs:annotation>
                </xs:attribute>
                <xs:attribute name="StaffRoomQuantity" type="xs:nonNegativeInteger" use="optional">
                  <xs:annotation>
                    <xs:documentation xml:lang="en">Total number of staff room nights used at this property.</xs:documentation>
                  </xs:annotation>
                </xs:attribute>
                <xs:attribute name="ContractedDate" type="DateOrTimeOrDateTimeType" use="optional">
                  <xs:annotation>
                    <xs:documentation xml:lang="en">The date the contract was signed for the room block.</xs:documentation>
                  </xs:annotation>
                </xs:attribute>
                <xs:attribute name="CutoffDate" type="DateOrTimeOrDateTimeType" use="optional">
                  <xs:annotation>
                    <xs:documentation xml:lang="en">The date the room block was closed to booking.</xs:documentation>
                  </xs:annotation>
                </xs:attribute>
                <xs:attribute name="CutoffDateExercisedIndicator" type="xs:boolean" use="optional">
                  <xs:annotation>
                    <xs:documentation xml:lang="en">When true, the cutoff date was excercised.</xs:documentation>
                  </xs:annotation>
                </xs:attribute>
                <xs:attribute name="RequestedOversellPercentage" type="Percentage" use="optional">
                  <xs:annotation>
                    <xs:documentation xml:lang="en">Percentage of rooms requested by the housing provider above the contracted room block.</xs:documentation>
                  </xs:annotation>
                </xs:attribute>
              </xs:complexType>
            </xs:element>
            <xs:element name="Comments" minOccurs="0">
              <xs:annotation>
                <xs:documentation xml:lang="en">A collection of comments pertaining to the stay. </xs:documentation>
              </xs:annotation>
              <xs:complexType>
                <xs:sequence>
                  <xs:element name="Comment" type="ParagraphType" maxOccurs="99">
                    <xs:annotation>
                      <xs:documentation xml:lang="en">A comment pertaining to the stay. </xs:documentation>
                    </xs:annotation>
                  </xs:element>
                </xs:sequence>
              </xs:complexType>
            </xs:element>
          </xs:sequence>
          <xs:attribute name="TotalBlockQuantity" type="xs:nonNegativeInteger" use="optional">
            <xs:annotation>
              <xs:documentation xml:lang="en">The total number of room blocks for this event at this property (i.e., sub blocks plus primary room block).</xs:documentation>
            </xs:annotation>
          </xs:attribute>
        </xs:complexType>
      </xs:element>
      <xs:element name="Transportations" type="TransportationType" minOccurs="0">
        <xs:annotation>
          <xs:documentation xml:lang="en">A collection of transportation requirements for this site for this event.</xs:documentation>
        </xs:annotation>
      </xs:element>
      <xs:element name="TaxExemptInfo" minOccurs="0" maxOccurs="3">
        <xs:annotation>
          <xs:documentation xml:lang="en">Used to provide tax exemption information for the event.</xs:documentation>
        </xs:annotation>
        <xs:complexType>
          <xs:sequence>
            <xs:element name="Comments" minOccurs="0">
              <xs:annotation>
                <xs:documentation xml:lang="en">A collection of comments pertaining to the tax exemption information.</xs:documentation>
              </xs:annotation>
              <xs:complexType>
                <xs:sequence>
                  <xs:element name="Comment" type="ParagraphType" maxOccurs="unbounded">
                    <xs:annotation>
                      <xs:documentation xml:lang="en">Used for comments about the billing information. </xs:documentation>
                    </xs:annotation>
                  </xs:element>
                </xs:sequence>
              </xs:complexType>
            </xs:element>
          </xs:sequence>
          <xs:attributeGroup ref="TaxExemptGroup">
            <xs:annotation>
              <xs:documentation xml:lang="en">Tax exemption information for the event.</xs:documentation>
            </xs:annotation>
          </xs:attributeGroup>
        </xs:complexType>
      </xs:element>
      <xs:element name="Comments" minOccurs="0">
        <xs:annotation>
          <xs:documentation xml:lang="en">A collection of comments that pertain to this occurrence of the event.  </xs:documentation>
        </xs:annotation>
        <xs:complexType>
          <xs:sequence>
            <xs:element name="Comment" type="ParagraphType" minOccurs="0" maxOccurs="unbounded">
              <xs:annotation>
                <xs:documentation xml:lang="en">A comment that pertains to this occurrence of the event.  </xs:documentation>
              </xs:annotation>
            </xs:element>
          </xs:sequence>
        </xs:complexType>
      </xs:element>
    </xs:sequence>
    <xs:attributeGroup ref="HotelReferenceGroup">
      <xs:annotation>
        <xs:documentation xml:lang="en">The venue for which event information is being provided (e.g., hotel, convention center, golf course).</xs:documentation>
      </xs:annotation>
    </xs:attributeGroup>
    <xs:attribute name="LocationCategoryCode" type="OTA_CodeType" use="optional">
      <xs:annotation>
        <xs:documentation xml:lang="en">Defines the hotel location category.  Refer to OpenTravel Code table Location Category Codes (LOC).</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="PrimaryFacilityIndicator" type="xs:boolean" use="optional">
      <xs:annotation>
        <xs:documentation xml:lang="en">When true, indicates the site is the primary facility for the event.</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="HeadquarterHotelIndicator" type="xs:boolean" use="optional">
      <xs:annotation>
        <xs:documentation xml:lang="en">When true, indicates the site is the headquarter hotel for the event. </xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attribute name="PropertyTypeCode" type="OTA_CodeType" use="optional">
      <xs:annotation>
        <xs:documentation xml:lang="en">Used to indicate a property type. Refer to OpenTravel Code List Property Class Type (PCT).</xs:documentation>
      </xs:annotation>
    </xs:attribute>
    <xs:attributeGroup ref="CurrencyCodeGroup">
      <xs:annotation>
        <xs:documentation xml:lang="en">Provides a currency code to reflect the currency in which the event charges are expressed.</xs:documentation>
      </xs:annotation>
    </xs:attributeGroup>
  </xs:complexType>
</xs:schema>
