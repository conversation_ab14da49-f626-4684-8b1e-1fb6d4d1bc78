<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="DataExtractionReport"  pageWidth="540" pageHeight="715" orientation="Landscape" columnWidth="540" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" isIgnorePagination="true" uuid="2d95da3e-0e6a-4ecd-b175-62ec485b6576">
	<property name="ireport.zoom" value="1.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<property name="ireport.jasperserver.url" value="http://qa-rpt.tetris.ideasdev.int:8080/jasperserver-pro/services/repository"/>
	<template>"jrxmls/customStyles.jrtx"</template>
	<style name="Title" fontSize="50" isBold="true" />
	<style name="SubTitle" forecolor="#736343" fontSize="18"/>
	<style name="Column header" forecolor="#666666" fontSize="12" isBold="true"/>
	<style name="Detail" fontSize="12"/>
	<style name="Row" mode="Transparent">
		<conditionalStyle>
			<conditionExpression><![CDATA[$V{REPORT_COUNT}%2 == 0]]></conditionExpression>
			<style backcolor="#E6DAC3"/>
		</conditionalStyle>
	</style>
	<style name="table">
		<box>
			<pen lineWidth="1.0" lineColor="#000000"/>
		</box>
	</style>
	<style name="table_TH" mode="Opaque" backcolor="#F0F8FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table_CH" mode="Opaque" backcolor="#BFE1FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table_TD" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<subDataset name="DataExtractAtAccomType" uuid="9ae094f4-318d-4bb3-a433-09b287c22c92">
		<parameter name="StartDate" class="java.util.Date">
			<defaultValueExpression><![CDATA[]]></defaultValueExpression>
		</parameter>
		<parameter name="EndDate" class="java.util.Date">
			<defaultValueExpression><![CDATA[]]></defaultValueExpression>
		</parameter>
		<parameter name="param_Property_ID" class="java.lang.Integer">
			<defaultValueExpression><![CDATA[5]]></defaultValueExpression>
		</parameter>
		<parameter name="includeInactiveRT" class="java.lang.Integer">
			<defaultValueExpression><![CDATA[1]]></defaultValueExpression>
		</parameter>
		<parameter name="includePseudoRT" class="java.lang.Integer">
			<defaultValueExpression><![CDATA[0]]></defaultValueExpression>
		</parameter>
		<parameter name="IsCap" class="java.lang.Boolean">
			<defaultValueExpression><![CDATA[]]></defaultValueExpression>
		</parameter>
		<parameter name="IsRS" class="java.lang.Boolean">
			<defaultValueExpression><![CDATA[]]></defaultValueExpression>
		</parameter>
		<parameter name="IsArr" class="java.lang.Boolean">
			<defaultValueExpression><![CDATA[]]></defaultValueExpression>
		</parameter>
		<parameter name="IsDep" class="java.lang.Boolean">
			<defaultValueExpression><![CDATA[]]></defaultValueExpression>
		</parameter>
		<parameter name="IsRNAO" class="java.lang.Boolean">
			<defaultValueExpression><![CDATA[]]></defaultValueExpression>
		</parameter>
		<parameter name="IsCncel" class="java.lang.Boolean">
			<defaultValueExpression><![CDATA[]]></defaultValueExpression>
		</parameter>
		<parameter name="IsNS" class="java.lang.Boolean">
			<defaultValueExpression><![CDATA[]]></defaultValueExpression>
		</parameter>
		<parameter name="IsRev" class="java.lang.Boolean">
			<defaultValueExpression><![CDATA[]]></defaultValueExpression>
		</parameter>
		<parameter name="IsOvbk" class="java.lang.Boolean">
			<defaultValueExpression><![CDATA[]]></defaultValueExpression>
		</parameter>
		<parameter name="IsRevPar" class="java.lang.Boolean">
			<defaultValueExpression><![CDATA[]]></defaultValueExpression>
		</parameter>
		<parameter name="IsLRV" class="java.lang.Boolean">
			<defaultValueExpression><![CDATA[]]></defaultValueExpression>
		</parameter>
		<parameter name="IsADR" class="java.lang.Boolean">
			<defaultValueExpression><![CDATA[]]></defaultValueExpression>
		</parameter>
		<parameter name="param_BaseCurrency" class="java.lang.String">
			<defaultValueExpression><![CDATA["USD"]]></defaultValueExpression>
		</parameter>
		<parameter name="account_id" class="java.lang.String">
			<defaultValueExpression><![CDATA["---"]]></defaultValueExpression>
		</parameter>
		<parameter name="IsOOO" class="java.lang.Boolean">
			<defaultValueExpression><![CDATA[]]></defaultValueExpression>
		</parameter>
		<parameter name="JNDI_NAME" class="java.lang.String"/>
		<parameter name="param_isRollingDate" class="java.lang.Integer"/>
		<parameter name="param_Rolling_Start_Date" class="java.lang.String"/>
		<parameter name="param_Rolling_End_Date" class="java.lang.String"/>
		<parameter name="param_IsShowLastYearDataChecked" class="java.lang.Boolean"/>
		<parameter name="param_IsShowLast2YearsDataChecked" class="java.lang.Boolean"/>
		<parameter name="param_IsShowYear2019DataChecked" class="java.lang.Boolean"/>
		<parameter name="param_IsCompRoomDataExcluded" class="java.lang.Boolean"/>
		<parameter name="PROPERTY_TIME_ZONE" class="java.lang.String" isForPrompting="false">
			<defaultValueExpression><![CDATA[""]]></defaultValueExpression>
		</parameter>
		<parameter name="userDateFormat" class="java.lang.String" isForPrompting="false">
			<defaultValueExpression><![CDATA["yyyy-MM-dd"]]></defaultValueExpression>
		</parameter>
		<parameter name="userLocale" class="java.lang.String" isForPrompting="false">
			<defaultValueExpression><![CDATA["en_US"]]></defaultValueExpression>
		</parameter>
		<parameter name="dateFormatter" class="java.text.DateFormat" isForPrompting="false">
			<defaultValueExpression><![CDATA[]]></defaultValueExpression>
		</parameter>
		<parameter name="IsRS_STLY_RT" class="java.lang.Boolean"/>
		<parameter name="IsRev_STLY_RT" class="java.lang.Boolean"/>
		<parameter name="IsSTLY" class="java.lang.Integer"/>
		<parameter name="IsRS_ST2Y_RT" class="java.lang.Boolean"/>
		<parameter name="IsRev_ST2Y_RT" class="java.lang.Boolean"/>
		<parameter name="IsST2Y" class="java.lang.Integer"/>
		<parameter name="IsRS_ST19_RT" class="java.lang.Boolean"/>
		<parameter name="IsRev_ST19_RT" class="java.lang.Boolean"/>
		<parameter name="IsST19" class="java.lang.Integer"/>
		<parameter name="usePhysicalCapacity" class="java.lang.String">
			<defaultValueExpression><![CDATA["false"]]></defaultValueExpression>
		</parameter>
		<parameter name="IsRemainingCapacity" class="java.lang.Boolean">
			<defaultValueExpression><![CDATA["false"]]></defaultValueExpression>
		</parameter>
		<queryString>
			<![CDATA[exec dbo.usp_dataextraction_report_rt_thisyear_lastyear $P{param_Property_ID},3,13,$P{StartDate},$P{EndDate},$P{param_isRollingDate},$P{param_Rolling_Start_Date},$P{param_Rolling_End_Date},$P{IsSTLY},$P{IsST2Y},$P{usePhysicalCapacity},$P{includeInactiveRT},$P{includePseudoRT},$P{param_IsCompRoomDataExcluded},$P{IsST19},$P{param_IsShowYear2019DataChecked}]]>
		</queryString>
		<field name="Property_Name" class="java.lang.String"/>
		<field name="Occupancy_DT" class="java.util.Date"/>
		<field name="dow" class="java.lang.String"/>
		<field name="Comparison_date_last_year" class="java.util.Date"/>
		<field name="Comparison_date_last_two_years" class="java.util.Date"/>
        <field name="Comparison_date_year_2019" class="java.util.Date"/>
		<field name="Accom_Class_Name" class="java.lang.String"/>
		<field name="Accom_Type_Name" class="java.lang.String"/>
		<field name="Accom_Capacity_ty" class="java.lang.Integer"/>
		<field name="Accom_Capacity_ly" class="java.lang.Integer"/>
		<field name="Rooms_Sold_ty" class="java.lang.Integer"/>
		<field name="Rooms_Sold_ly" class="java.lang.Integer"/>
		<field name="Rooms_Sold_l2y" class="java.lang.Integer"/>
		<field name="Rooms_Sold_y2019" class="java.lang.Integer"/>
		<field name="onBooks_Revenue_ty" class="java.math.BigDecimal"/>
		<field name="onBooks_Revenue_ly" class="java.math.BigDecimal"/>
		<field name="onBooks_Revenue_l2y" class="java.math.BigDecimal"/>
		<field name="onBooks_Revenue_y2019" class="java.math.BigDecimal"/>
		<field name="onBooks_ADR_ty" class="java.math.BigDecimal"/>
		<field name="onBooks_ADR_ly" class="java.math.BigDecimal"/>
		<field name="onBooks_ADR_l2y" class="java.math.BigDecimal"/>
		<field name="onBooks_ADR_y2019" class="java.math.BigDecimal"/>
		<field name="onBooks_REVPAR_ty" class="java.math.BigDecimal"/>
		<field name="onBooks_REVPAR_ly" class="java.math.BigDecimal"/>
		<field name="onBooks_REVPAR_l2y" class="java.math.BigDecimal"/>
		<field name="onBooks_REVPAR_y2019" class="java.math.BigDecimal"/>
		<field name="Arrivals_ty" class="java.lang.Integer"/>
		<field name="Arrivals_ly" class="java.lang.Integer"/>
		<field name="Departures_ty" class="java.lang.Integer"/>
		<field name="Departures_ly" class="java.lang.Integer"/>
		<field name="Rooms_Not_Avail_Maint_ty" class="java.lang.Integer"/>
		<field name="Rooms_Not_Avail_Maint_ly" class="java.lang.Integer"/>
		<field name="Rooms_Not_Avail_Other_ty" class="java.lang.Integer"/>
		<field name="Rooms_Not_Avail_Other_ly" class="java.lang.Integer"/>
		<field name="Cancellations_ty" class="java.lang.Integer"/>
		<field name="Cancellations_ly" class="java.lang.Integer"/>
		<field name="No_Shows_ty" class="java.lang.Integer"/>
		<field name="No_Shows_ly" class="java.lang.Integer"/>
		<field name="Room_Revenue_ty" class="java.math.BigDecimal"/>
		<field name="Room_Revenue_ly" class="java.math.BigDecimal"/>
		<field name="Food_Revenue_ty" class="java.math.BigDecimal"/>
		<field name="Food_Revenue_ly" class="java.math.BigDecimal"/>
		<field name="ADR_ty" class="java.math.BigDecimal"/>
		<field name="ADR_ly" class="java.math.BigDecimal"/>
		<field name="Total_Revenue_ty" class="java.math.BigDecimal"/>
		<field name="Total_Revenue_ly" class="java.math.BigDecimal"/>
		<field name="REVPAR_ty" class="java.math.BigDecimal"/>
		<field name="REVPAR_ly" class="java.math.BigDecimal"/>
		<field name="Overbooking_Decision_ty" class="java.math.BigDecimal"/>
		<field name="Overbooking_Decision_ly" class="java.math.BigDecimal"/>
		<field name="Rooms_Sold_stly" class="java.lang.Integer"/>
		<field name="onBooks_Revenue_stly" class="java.math.BigDecimal"/>
		<field name="Rooms_Sold_st2y" class="java.lang.Integer"/>
		<field name="onBooks_Revenue_st2y" class="java.math.BigDecimal"/>
		<field name="Rooms_Sold_st19" class="java.lang.Integer"/>
		<field name="onBooks_Revenue_st19" class="java.math.BigDecimal"/>
		<field name="lrvRC_ty" class="java.math.BigDecimal"/>
		<field name="lrvRT_ty" class="java.math.BigDecimal"/>
		<field name="Remaining_Capacity_This_Year" class="java.lang.Integer"/>
		<field name="Remaining_Capacity_Last_Year" class="java.lang.Integer"/>
        <field name="CloseLV0_Pattern_ty" class="java.lang.String"/>
        <field name="CloseLV0_Pattern_ly" class="java.lang.String"/>
	</subDataset>
	<subDataset name="DataExt_DS_AT" uuid="ee826eb8-8e98-4cd6-82eb-e0f016f1187d">
		<parameter name="StartDate" class="java.util.Date">
			<defaultValueExpression><![CDATA[]]></defaultValueExpression>
		</parameter>
		<parameter name="EndDate" class="java.util.Date">
			<defaultValueExpression><![CDATA[]]></defaultValueExpression>
		</parameter>
		<parameter name="JNDI_NAME" class="java.lang.String"/>
		<parameter name="param_isRollingDate" class="java.lang.Integer"/>
		<parameter name="param_Rolling_Start_Date" class="java.lang.String"/>
		<parameter name="param_Rolling_End_Date" class="java.lang.String"/>
		<parameter name="param_User_ID" class="java.lang.Integer"/>
		<parameter name="param_Property_ID" class="java.lang.Integer">
			<defaultValueExpression><![CDATA[5]]></defaultValueExpression>
		</parameter>
		<parameter name="param_BaseCurrency" class="java.lang.String">
			<defaultValueExpression><![CDATA["USD"]]></defaultValueExpression>
		</parameter>
		<parameter name="account_id" class="java.lang.String">
			<defaultValueExpression><![CDATA["---"]]></defaultValueExpression>
		</parameter>
		<parameter name="Jasper_custom_formatter" class="com.ideas.tetris.platform.reports.jasperreports.formatter.JasperCustomFormatter" isForPrompting="false">
			<defaultValueExpression><![CDATA[new com.ideas.tetris.platform.reports.jasperreports.formatter.JasperCustomFormatter()]]></defaultValueExpression>
		</parameter>
		<parameter name="PROPERTY_TIME_ZONE" class="java.lang.String" isForPrompting="false">
			<defaultValueExpression><![CDATA[""]]></defaultValueExpression>
		</parameter>
		<parameter name="userDateFormat" class="java.lang.String" isForPrompting="false">
			<defaultValueExpression><![CDATA["yyyy-MM-dd"]]></defaultValueExpression>
		</parameter>
		<parameter name="userLocale" class="java.lang.String" isForPrompting="false">
			<defaultValueExpression><![CDATA["en_US"]]></defaultValueExpression>
		</parameter>
		<parameter name="dateFormatter" class="java.text.DateFormat" isForPrompting="false">
			<defaultValueExpression><![CDATA[]]></defaultValueExpression>
		</parameter>
		<parameter name="usePhysicalCapacity" class="java.lang.String">
			<defaultValueExpression><![CDATA["false"]]></defaultValueExpression>
		</parameter>
        <parameter name="Include_CloseLV0" class="java.lang.String">
            <defaultValueExpression><![CDATA["false"]]></defaultValueExpression>
        </parameter>
		<queryString>
			<![CDATA[select * from dbo.ufn_get_filter_selection
(
$P{param_Property_ID},
$P{param_User_ID},
$P{param_BaseCurrency},
$P{param_isRollingDate},
$P{StartDate},
$P{EndDate},
'',
'',
'',
'',
'',
'',
$P{param_Rolling_Start_Date},
$P{param_Rolling_End_Date},
'',
'',
'',
'',
'',
''
)]]>
		</queryString>
		<field name="property_name" class="java.lang.String"/>
		<field name="created_by" class="java.lang.String"/>
		<field name="genration_date" class="java.lang.String"/>
		<field name="start_date" class="java.util.Date"/>
		<field name="end_date" class="java.util.Date"/>
		<field name="analysis_start_date" class="java.lang.String"/>
		<field name="analysis_end_date" class="java.lang.String"/>
		<field name="analysis_business_dt" class="java.lang.String"/>
		<field name="comparision_start_date" class="java.lang.String"/>
		<field name="comparision_end_date" class="java.lang.String"/>
		<field name="comparision_business_dt" class="java.lang.String"/>
		<field name="param_BaseCurrency" class="java.lang.String"/>
	</subDataset>
	<parameter name="StartDate" class="java.util.Date">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="EndDate" class="java.util.Date">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="param_Property_ID" class="java.lang.Integer">
		<defaultValueExpression><![CDATA[5]]></defaultValueExpression>
	</parameter>
	<parameter name="includeInactiveRT" class="java.lang.Integer">
		<defaultValueExpression><![CDATA[1]]></defaultValueExpression>
	</parameter>
	<parameter name="includePseudoRT" class="java.lang.Integer">
		<defaultValueExpression><![CDATA[0]]></defaultValueExpression>
	</parameter>
	<parameter name="IsCap" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="IsRS" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="IsArr" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="IsDep" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="IsRNAO" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="IsCncel" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="IsNS" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="IsRev" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="IsRevPar" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="IsLRV" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="IsADR" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="IsOvbk" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="param_BaseCurrency" class="java.lang.String">
		<defaultValueExpression><![CDATA["USD"]]></defaultValueExpression>
	</parameter>
	<parameter name="Include_CloseLV0" class="java.lang.String">
        <defaultValueExpression><![CDATA[]]></defaultValueExpression>
    </parameter>
	<parameter name="account_id" class="java.lang.String">
		<defaultValueExpression><![CDATA["---"]]></defaultValueExpression>
	</parameter>
	<parameter name="IsOOO" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="JNDI_NAME" class="java.lang.String"/>
	<parameter name="param_isRollingDate" class="java.lang.Integer">
		<defaultValueExpression><![CDATA[0]]></defaultValueExpression>
	</parameter>
	<parameter name="param_Rolling_Start_Date" class="java.lang.String"/>
	<parameter name="param_Rolling_End_Date" class="java.lang.String"/>
	<parameter name="param_IsShowLastYearDataChecked" class="java.lang.Boolean"/>
	<parameter name="param_IsShowLast2YearsDataChecked" class="java.lang.Boolean"/>
	<parameter name="param_IsShowYear2019DataChecked" class="java.lang.Boolean"/>
	<parameter name="param_IsCompRoomDataExcluded" class="java.lang.Boolean"/>
	<parameter name="param_User_ID" class="java.lang.Integer"/>
	<parameter name="Jasper_custom_formatter" class="com.ideas.tetris.platform.reports.jasperreports.formatter.JasperCustomFormatter" isForPrompting="false">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="PROPERTY_TIME_ZONE" class="java.lang.String" isForPrompting="false">
			<defaultValueExpression><![CDATA[""]]></defaultValueExpression>
		</parameter>
		<parameter name="userDateFormat" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA["yyyy-MM-dd"]]></defaultValueExpression>
	</parameter>
	<parameter name="userLocale" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA["en_US"]]></defaultValueExpression>
	</parameter>
	<parameter name="dateFormatter" class="java.text.DateFormat" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{REPORT_FORMAT_FACTORY}.createDateFormat( $P{userDateFormat}, $P{REPORT_LOCALE}, null )]]></defaultValueExpression>
	</parameter>
	<parameter name="param_SheetForCriteria" class="java.lang.Boolean" isForPrompting="false">
		<defaultValueExpression><![CDATA[false]]></defaultValueExpression>
	</parameter>
	<parameter name="IsRS_STLY_RT" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="IsRev_STLY_RT" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="IsRS_ST2Y_RT" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="IsRev_ST2Y_RT" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="IsRS_ST19_RT" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="IsRev_ST19_RT" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="usePhysicalCapacity" class="java.lang.String">
		<defaultValueExpression><![CDATA["false"]]></defaultValueExpression>
	</parameter>
	<parameter name="IsRemainingCapacity" class="java.lang.String"/>
	<queryString>
		<![CDATA[select getdate() as date]]>
	</queryString>
	<field name="date" class="java.sql.Timestamp"/>
	<title>
		<band height="30">
			<printWhenExpression><![CDATA[$P{IS_IGNORE_PAGINATION} && $P{param_SheetForCriteria}.toString().equalsIgnoreCase("true")?false:true]]></printWhenExpression>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="0" y="0" width="540" height="30" uuid="d2bd9d4c-e1db-4408-9909-ad8849164fc3">
					<propertyExpression name="net.sf.jasperreports.export.xls.sheet.name"><![CDATA[$R{room.type}]]></propertyExpression>
				</reportElement>
				<box leftPadding="2"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="12" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$R{dataExtractionReport.atRoomType.title}]]></textFieldExpression>
			</textField>
		</band>
	</title>
	<pageHeader>
		<band height="50">
			<printWhenExpression><![CDATA[($P{IS_IGNORE_PAGINATION} && new Boolean($P{param_SheetForCriteria})==false)?true:false]]></printWhenExpression>
			<componentElement>
				<reportElement key="xlsElem" style="table" x="0" y="0" width="418" height="50" uuid="39138486-88a1-43c5-bea9-ec2739a72d70">
					<printWhenExpression><![CDATA[$P{IS_IGNORE_PAGINATION}]]></printWhenExpression>
				</reportElement>
				<jr:table xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd">
					<datasetRun subDataset="DataExt_DS_AT" uuid="e54b2cdb-1428-4ba7-84ee-379965c31c0e">
						<datasetParameter name="param_Property_ID">
							<datasetParameterExpression><![CDATA[$P{param_Property_ID}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="param_BaseCurrency">
							<datasetParameterExpression><![CDATA[$P{param_BaseCurrency}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="account_id">
							<datasetParameterExpression><![CDATA[$P{account_id}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="JNDI_NAME">
							<datasetParameterExpression><![CDATA[$P{JNDI_NAME}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="param_isRollingDate">
							<datasetParameterExpression><![CDATA[$P{param_isRollingDate}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="param_User_ID">
							<datasetParameterExpression><![CDATA[$P{param_User_ID}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="StartDate">
							<datasetParameterExpression><![CDATA[$P{StartDate}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="EndDate">
							<datasetParameterExpression><![CDATA[$P{EndDate}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="param_Rolling_Start_Date">
							<datasetParameterExpression><![CDATA[$P{param_Rolling_Start_Date}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="param_Rolling_End_Date">
							<datasetParameterExpression><![CDATA[$P{param_Rolling_End_Date}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="Jasper_custom_formatter">
							<datasetParameterExpression><![CDATA[$P{Jasper_custom_formatter}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="PROPERTY_TIME_ZONE">
							<datasetParameterExpression><![CDATA[$P{PROPERTY_TIME_ZONE}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="userDateFormat">
							<datasetParameterExpression><![CDATA[$P{userDateFormat}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="userLocale">
							<datasetParameterExpression><![CDATA[$P{userLocale}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="dateFormatter">
							<datasetParameterExpression><![CDATA[$P{dateFormatter}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="Include_CloseLV0">
							<datasetParameterExpression><![CDATA[$P{Include_CloseLV0}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="usePhysicalCapacity">
							<datasetParameterExpression><![CDATA[new Boolean($P{usePhysicalCapacity}) ? 1 : 0]]></datasetParameterExpression>
						</datasetParameter>
						<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
					</datasetRun>
					<jr:columnGroup width="720" uuid="98f054f3-a682-4954-b4aa-77f7ff85d306">
						<jr:tableHeader height="20" rowSpan="1">
							<textField>
								<reportElement x="0" y="0" width="630" height="20" uuid="497dcd14-dab9-4c07-a370-cce3541d0f3d"/>
								<box leftPadding="10"/>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font size="11" isBold="true"/>
								</textElement>
								<textFieldExpression><![CDATA[$R{report.ReportCriteria}]]></textFieldExpression>
							</textField>
						</jr:tableHeader>
						<jr:column width="180" uuid="e50582b7-b8a4-42aa-a526-d67611f96e7d">
							<jr:tableHeader height="0" rowSpan="1"/>
							<jr:columnHeader style="table_CH" height="20" rowSpan="1">
								<textField>
									<reportElement x="0" y="0" width="180" height="20" uuid="07ff82f2-80e4-43ba-9412-4c0162d8a76e"/>
									<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
										<font isBold="true"/>
									</textElement>
									<textFieldExpression><![CDATA[$R{common.propertyName}]]></textFieldExpression>
								</textField>
							</jr:columnHeader>
							<jr:detailCell style="table_TD" height="20" rowSpan="1">
								<textField isStretchWithOverflow="true">
									<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="180" height="20" uuid="54e1567e-5961-406e-a4de-b5cdf9078680"/>
									<textElement textAlignment="Center" verticalAlignment="Middle"/>
									<textFieldExpression><![CDATA[$F{property_name}]]></textFieldExpression>
								</textField>
							</jr:detailCell>
						</jr:column>
						<jr:column width="90" uuid="419676e9-720b-4aa6-953c-95d62fabb492">
							<jr:tableHeader height="0" rowSpan="1"/>
							<jr:columnHeader style="table_CH" height="20" rowSpan="1">
								<textField>
									<reportElement x="0" y="0" width="90" height="20" uuid="5d088de5-84de-4b93-9db5-182cd0622f7c"/>
									<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
										<font isBold="true"/>
									</textElement>
									<textFieldExpression><![CDATA[$R{startDate}]]></textFieldExpression>
								</textField>
							</jr:columnHeader>
							<jr:detailCell style="table_TD" height="20" rowSpan="1">
								<textField isStretchWithOverflow="true">
									<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="20" uuid="56421bfa-d6a2-4a42-af86-10434189ebca"/>
									<textElement textAlignment="Center" verticalAlignment="Middle"/>
									<textFieldExpression><![CDATA[$F{start_date}]]></textFieldExpression>
									<patternExpression><![CDATA[$P{userDateFormat}]]></patternExpression>
								</textField>
							</jr:detailCell>
						</jr:column>
						<jr:column width="90" uuid="660c1c43-c971-4fec-bef9-dbc6c88ccb04">
							<jr:tableHeader height="0" rowSpan="1"/>
							<jr:columnHeader style="table_CH" height="20" rowSpan="1">
								<textField>
									<reportElement x="0" y="0" width="90" height="20" uuid="b10e845a-28b8-4d93-b3f6-ab3c7609bcab"/>
									<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
										<font isBold="true"/>
									</textElement>
									<textFieldExpression><![CDATA[$R{endDate}]]></textFieldExpression>
								</textField>
							</jr:columnHeader>
							<jr:detailCell style="table_TD" height="20" rowSpan="1">
								<textField isStretchWithOverflow="true">
									<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="20" uuid="be94aa4b-918c-48d9-931b-04e69047b12a"/>
									<textElement textAlignment="Center" verticalAlignment="Middle"/>
									<textFieldExpression><![CDATA[$F{end_date}]]></textFieldExpression>
									<patternExpression><![CDATA[$P{userDateFormat}]]></patternExpression>
								</textField>
							</jr:detailCell>
						</jr:column>
						<jr:column width="90" uuid="dd7f59b3-08ae-42f2-b382-f5e85a8ee0e1">
							<jr:tableHeader height="0" rowSpan="1"/>
							<jr:tableFooter height="0" rowSpan="1"/>
							<jr:columnHeader style="table_CH" height="20" rowSpan="1">
								<textField>
									<reportElement x="0" y="0" width="90" height="20" uuid="47b79eef-ef82-4c8f-b56a-04f477571d9e"/>
									<textElement textAlignment="Center" verticalAlignment="Middle">
										<font isBold="true"/>
									</textElement>
									<textFieldExpression><![CDATA[$R{currency}]]></textFieldExpression>
								</textField>
							</jr:columnHeader>
							<jr:detailCell style="table_TD" height="20" rowSpan="1">
								<textField isStretchWithOverflow="true">
									<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="20" uuid="72ea82c5-eee8-46f8-8bd3-1f6f07541b7f"/>
									<textElement textAlignment="Center" verticalAlignment="Middle"/>
									<textFieldExpression><![CDATA[$F{param_BaseCurrency}]]></textFieldExpression>
								</textField>
							</jr:detailCell>
						</jr:column>
						<jr:column width="90" uuid="ed0dfbd9-5998-4a51-acc6-bc3b8b3fda23">
							<jr:tableHeader height="0" rowSpan="1"/>
							<jr:tableFooter height="0" rowSpan="1"/>
							<jr:columnHeader style="table_CH" height="20" rowSpan="1">
								<textField>
									<reportElement x="0" y="0" width="90" height="20" uuid="3a8ebeed-50c6-4afd-bdb4-f0fc3dc14c57"/>
									<textElement textAlignment="Center" verticalAlignment="Middle">
										<font isBold="true"/>
									</textElement>
									<textFieldExpression><![CDATA[$R{createdBy}]]></textFieldExpression>
								</textField>
							</jr:columnHeader>
							<jr:detailCell style="table_TD" height="20" rowSpan="1">
								<textField isStretchWithOverflow="true">
									<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="20" uuid="5c1c00e1-557f-474b-8b6b-c3915edd34a8"/>
									<textElement textAlignment="Center" verticalAlignment="Middle"/>
									<textFieldExpression><![CDATA[$F{created_by}]]></textFieldExpression>
								</textField>
							</jr:detailCell>
						</jr:column>
						<jr:column width="90" uuid="e3ecbfff-813a-4a63-b384-8817a25b1ea8">
							<jr:tableHeader height="0" rowSpan="1"/>
							<jr:tableFooter height="0" rowSpan="1"/>
							<jr:columnHeader style="table_CH" height="20" rowSpan="1">
								<textField>
									<reportElement x="0" y="0" width="90" height="20" uuid="cbcaac0e-5918-4920-aee6-55f95d584754"/>
									<textElement textAlignment="Center" verticalAlignment="Middle">
										<font isBold="true"/>
									</textElement>
									<textFieldExpression><![CDATA[$R{report.GeneratedOn}]]></textFieldExpression>
								</textField>
							</jr:columnHeader>
							<jr:detailCell style="table_TD" height="20" rowSpan="1">
								<textField isStretchWithOverflow="true">
									<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="20" uuid="e6e148b9-4906-4ee1-ada8-b67a61042a90"/>
									<textElement textAlignment="Center" verticalAlignment="Middle"/>
									<textFieldExpression><![CDATA[$P{Jasper_custom_formatter}.getDateInPropertyTimeZone($F{genration_date}, $P{REPORT_TIME_ZONE}, $P{PROPERTY_TIME_ZONE},$P{userDateFormat}+" HH:mm:ss", $P{REPORT_LOCALE})]]></textFieldExpression>
								</textField>
							</jr:detailCell>
						</jr:column>
						<jr:column width="90" uuid="5b1137b0-92e8-484c-9bf7-9a67776157f8">
							<jr:tableHeader height="0" rowSpan="1"/>
							<jr:columnHeader style="table_CH" height="20" rowSpan="1">
								<textField>
									<reportElement x="0" y="0" width="90" height="20" uuid="84cd1217-82e0-46eb-ab67-75d42b4a594c"/>
									<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
										<font isBold="true"/>
									</textElement>
									<textFieldExpression><![CDATA[$R{common.account.id}]]></textFieldExpression>
								</textField>
							</jr:columnHeader>
							<jr:detailCell style="table_TD" height="20" rowSpan="1">
								<textField isStretchWithOverflow="true">
									<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="20" uuid="7eda6e6c-3ec9-4573-8f8c-35f11631edca"/>
									<textElement textAlignment="Center" verticalAlignment="Middle"/>
									<textFieldExpression><![CDATA[$P{account_id}]]></textFieldExpression>
								</textField>
							</jr:detailCell>
						</jr:column>
					</jr:columnGroup>
				</jr:table>
			</componentElement>
		</band>
	</pageHeader>
	<columnHeader>
		<band height="21">
			<printWhenExpression><![CDATA[($P{IS_IGNORE_PAGINATION} && new Boolean($P{param_SheetForCriteria})==false)?true:false]]></printWhenExpression>
			<staticText>
				<reportElement x="0" y="0" width="180" height="21" uuid="bac0df83-fe2f-4f06-aef5-4688bf455aaa"/>
				<text><![CDATA[]]></text>
			</staticText>
		</band>
	</columnHeader>
	<detail>
		<band height="50">
			<componentElement>
				<reportElement key="table" style="table" stretchType="RelativeToBandHeight" x="0" y="0" width="540" height="50" uuid="b9ffbdbb-0c6d-4a11-857b-62e5c1b21c87"/>
				<jr:table xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd">
					<datasetRun subDataset="DataExtractAtAccomType" uuid="66242849-5f9b-4b15-ac61-956cab761b6e">
						<datasetParameter name="IsCap">
							<datasetParameterExpression><![CDATA[new Boolean($P{IsCap})]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="IsRS">
							<datasetParameterExpression><![CDATA[new Boolean($P{IsRS})]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="IsArr">
							<datasetParameterExpression><![CDATA[new Boolean($P{IsArr})]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="IsDep">
							<datasetParameterExpression><![CDATA[new Boolean($P{IsDep})]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="IsRNAO">
							<datasetParameterExpression><![CDATA[new Boolean($P{IsRNAO})]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="IsCncel">
							<datasetParameterExpression><![CDATA[new Boolean($P{IsCncel})]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="IsNS">
							<datasetParameterExpression><![CDATA[new Boolean($P{IsNS})]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="IsRev">
							<datasetParameterExpression><![CDATA[new Boolean($P{IsRev})]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="IsOvbk">
							<datasetParameterExpression><![CDATA[new Boolean($P{IsOvbk})]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="IsRevPar">
							<datasetParameterExpression><![CDATA[new Boolean($P{IsRevPar})]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="IsLRV">
							<datasetParameterExpression><![CDATA[new Boolean($P{IsLRV})]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="IsADR">
							<datasetParameterExpression><![CDATA[new Boolean($P{IsADR})]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="IsOOO">
							<datasetParameterExpression><![CDATA[new Boolean($P{IsOOO})]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="StartDate">
							<datasetParameterExpression><![CDATA[$P{StartDate}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="EndDate">
							<datasetParameterExpression><![CDATA[$P{EndDate}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="param_Property_ID">
							<datasetParameterExpression><![CDATA[$P{param_Property_ID}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="includeInactiveRT">
							<datasetParameterExpression><![CDATA[$P{includeInactiveRT}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="includePseudoRT">
							<datasetParameterExpression><![CDATA[$P{includePseudoRT}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="JNDI_NAME">
							<datasetParameterExpression><![CDATA[$P{JNDI_NAME}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="param_isRollingDate">
							<datasetParameterExpression><![CDATA[$P{param_isRollingDate}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="param_Rolling_Start_Date">
							<datasetParameterExpression><![CDATA[$P{param_Rolling_Start_Date}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="param_Rolling_End_Date">
							<datasetParameterExpression><![CDATA[$P{param_Rolling_End_Date}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="param_IsShowLastYearDataChecked">
							<datasetParameterExpression><![CDATA[$P{param_IsShowLastYearDataChecked}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="param_IsShowLast2YearsDataChecked">
							<datasetParameterExpression><![CDATA[$P{param_IsShowLast2YearsDataChecked}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="param_IsShowYear2019DataChecked">
							<datasetParameterExpression><![CDATA[$P{param_IsShowYear2019DataChecked}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="param_IsCompRoomDataExcluded">
							<datasetParameterExpression><![CDATA[$P{param_IsCompRoomDataExcluded}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="PROPERTY_TIME_ZONE">
							<datasetParameterExpression><![CDATA[$P{PROPERTY_TIME_ZONE}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="userDateFormat">
							<datasetParameterExpression><![CDATA[$P{userDateFormat}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="userLocale">
							<datasetParameterExpression><![CDATA[$P{userLocale}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="dateFormatter">
							<datasetParameterExpression><![CDATA[$P{dateFormatter}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="IsRS_STLY_RT">
							<datasetParameterExpression><![CDATA[new Boolean($P{IsRS_STLY_RT})]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="IsRev_STLY_RT">
							<datasetParameterExpression><![CDATA[new Boolean($P{IsRev_STLY_RT})]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="IsSTLY">
							<datasetParameterExpression><![CDATA[(new Boolean($P{IsRS_STLY_RT}) || new Boolean($P{IsRev_STLY_RT})) ? 1 : 0]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="IsRS_ST2Y_RT">
							<datasetParameterExpression><![CDATA[new Boolean($P{IsRS_ST2Y_RT})]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="IsRev_ST2Y_RT">
							<datasetParameterExpression><![CDATA[new Boolean($P{IsRev_ST2Y_RT})]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="IsST2Y">
							<datasetParameterExpression><![CDATA[(new Boolean($P{IsRS_ST2Y_RT}) || new Boolean($P{IsRev_ST2Y_RT})) ? 1 : 0]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="IsRS_ST19_RT">
							<datasetParameterExpression><![CDATA[new Boolean($P{IsRS_ST19_RT})]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="IsRev_ST19_RT">
							<datasetParameterExpression><![CDATA[new Boolean($P{IsRev_ST19_RT})]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="IsST19">
							<datasetParameterExpression><![CDATA[(new Boolean($P{IsRS_ST19_RT}) || new Boolean($P{IsRev_ST19_RT})) ? 1 : 0]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="usePhysicalCapacity">
							<datasetParameterExpression><![CDATA[new Boolean($P{usePhysicalCapacity}) ? 1 : 0]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="IsRemainingCapacity">
							<datasetParameterExpression><![CDATA[new Boolean($P{IsRemainingCapacity})]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="param_BaseCurrency">
							<datasetParameterExpression><![CDATA[$P{param_BaseCurrency}]]></datasetParameterExpression>
						</datasetParameter>
						<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
					</datasetRun>
					<jr:columnGroup width="5310" uuid="7c2a96d0-ac2e-40d2-a2fc-beb868f09607">
						<jr:column width="180" uuid="05c6c96d-cc11-4997-adb9-828eacb380b9">
							<jr:columnHeader style="table_CH" height="50" rowSpan="1">
								<textField isStretchWithOverflow="true" isBlankWhenNull="true">
									<reportElement stretchType="RelativeToBandHeight" mode="Opaque" x="0" y="0" width="180" height="50" backcolor="#BFE1FF" uuid="c43908f4-b8a2-4f90-b5b2-b935d37e6ec4"/>
									<box>
										<topPen lineWidth="0.5"/>
										<leftPen lineWidth="0.5"/>
										<bottomPen lineWidth="0.5"/>
										<rightPen lineWidth="0.5"/>
									</box>
									<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
										<font isBold="true"/>
									</textElement>
									<textFieldExpression><![CDATA[$R{common.propertyName}]]></textFieldExpression>
								</textField>
							</jr:columnHeader>
							<jr:detailCell style="table_TD" height="30" rowSpan="1">
								<textField isStretchWithOverflow="true" isBlankWhenNull="true">
									<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="180" height="30" uuid="69e1b4a0-0fb3-44d6-9729-c5fb63cc2f60"/>
									<textElement textAlignment="Center" verticalAlignment="Middle"/>
									<textFieldExpression><![CDATA[$F{Property_Name}]]></textFieldExpression>
								</textField>
							</jr:detailCell>
						</jr:column>
						<jr:column width="90" uuid="ecfac573-d3b2-40cc-87c9-72fb8e68de2c">
							<jr:columnHeader style="table_CH" height="50" rowSpan="1">
								<textField isStretchWithOverflow="true" isBlankWhenNull="true">
									<reportElement stretchType="RelativeToBandHeight" mode="Opaque" x="0" y="0" width="90" height="50" backcolor="#BFE1FF" uuid="3e5f22fe-a565-49db-b7ff-fd29190c2a18"/>
									<box>
										<topPen lineWidth="0.5"/>
										<leftPen lineWidth="0.5"/>
										<bottomPen lineWidth="0.5"/>
										<rightPen lineWidth="0.5"/>
									</box>
									<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
										<font isBold="true"/>
									</textElement>
									<textFieldExpression><![CDATA[$R{common.dow}]]></textFieldExpression>
								</textField>
							</jr:columnHeader>
							<jr:detailCell style="table_TD" height="30" rowSpan="1">
								<textField isStretchWithOverflow="true" isBlankWhenNull="true">
									<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30" uuid="39386425-0e8c-4ec7-a081-61f2f4f48e1c"/>
									<textElement textAlignment="Center" verticalAlignment="Middle"/>
									<textFieldExpression><![CDATA[str($F{dow}.toLowerCase())]]></textFieldExpression>
								</textField>
							</jr:detailCell>
						</jr:column>
						<jr:column width="90" uuid="dd7aa291-77ba-4353-9f94-6185848c2c76">
							<jr:columnHeader style="table_CH" height="50" rowSpan="1">
								<textField isStretchWithOverflow="true" isBlankWhenNull="true">
									<reportElement stretchType="RelativeToBandHeight" mode="Opaque" x="0" y="0" width="90" height="50" backcolor="#BFE1FF" uuid="1afbc803-ebfb-435c-a77d-2dd92a6c3c14"/>
									<box>
										<topPen lineWidth="0.5"/>
										<leftPen lineWidth="0.5"/>
										<bottomPen lineWidth="0.5"/>
										<rightPen lineWidth="0.5"/>
									</box>
									<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
										<font isBold="true"/>
									</textElement>
									<textFieldExpression><![CDATA[$R{occupancyDate}]]></textFieldExpression>
								</textField>
							</jr:columnHeader>
							<jr:detailCell style="table_TD" height="30" rowSpan="1">
								<textField isStretchWithOverflow="true" isBlankWhenNull="true">
									<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30" uuid="737f39ac-1b78-4617-845c-4e11accaf5ad"/>
									<textElement textAlignment="Center" verticalAlignment="Middle"/>
									<textFieldExpression><![CDATA[$F{Occupancy_DT}]]></textFieldExpression>
									<patternExpression><![CDATA[$P{userDateFormat}]]></patternExpression>
								</textField>
							</jr:detailCell>
						</jr:column>
						<jr:column width="90" uuid="67e9a236-5612-4075-8f80-82d7dbc79797">
							<printWhenExpression><![CDATA[$P{param_IsShowLastYearDataChecked}]]></printWhenExpression>
							<jr:tableHeader height="0" rowSpan="1"/>
							<jr:tableFooter height="0" rowSpan="1"/>
							<jr:columnHeader style="table_CH" height="50" rowSpan="1">
								<textField isStretchWithOverflow="true" isBlankWhenNull="true">
									<reportElement stretchType="RelativeToBandHeight" mode="Opaque" x="0" y="0" width="90" height="50" backcolor="#BFE1FF" uuid="a00a5090-3119-4596-b710-c0c191ec6ca3"/>
									<box>
										<topPen lineWidth="0.5"/>
										<leftPen lineWidth="0.5"/>
										<bottomPen lineWidth="0.5"/>
										<rightPen lineWidth="0.5"/>
									</box>
									<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
										<font isBold="true"/>
									</textElement>
									<textFieldExpression><![CDATA[$R{common.comparisonDateLastYear}]]></textFieldExpression>
								</textField>
							</jr:columnHeader>
							<jr:detailCell style="table_TD" height="30" rowSpan="1">
								<textField isStretchWithOverflow="true" isBlankWhenNull="true">
									<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30" uuid="d990f3bb-a2ab-46cb-ba76-84e14ccd3675"/>
									<textElement textAlignment="Center" verticalAlignment="Middle"/>
									<textFieldExpression><![CDATA[$F{Comparison_date_last_year}]]></textFieldExpression>
									<patternExpression><![CDATA[$P{userDateFormat}]]></patternExpression>
								</textField>
							</jr:detailCell>
						</jr:column>
						<jr:column width="90" uuid="67e9a236-5612-4075-8f80-82d7dbc79797">
							<printWhenExpression><![CDATA[$P{param_IsShowLast2YearsDataChecked}]]></printWhenExpression>
							<jr:tableHeader height="0" rowSpan="1"/>
							<jr:tableFooter height="0" rowSpan="1"/>
							<jr:columnHeader style="table_CH" height="50" rowSpan="1">
								<textField isStretchWithOverflow="true" isBlankWhenNull="true">
									<reportElement stretchType="RelativeToBandHeight" mode="Opaque" x="0" y="0" width="90" height="50" backcolor="#BFE1FF" uuid="a00a5090-3119-4596-b710-c0c191ec6ca3"/>
									<box>
										<topPen lineWidth="0.5"/>
										<leftPen lineWidth="0.5"/>
										<bottomPen lineWidth="0.5"/>
										<rightPen lineWidth="0.5"/>
									</box>
									<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
										<font isBold="true"/>
									</textElement>
									<textFieldExpression><![CDATA[$R{common.comparisonDateLast2Years}]]></textFieldExpression>
								</textField>
							</jr:columnHeader>
							<jr:detailCell style="table_TD" height="30" rowSpan="1">
								<textField isStretchWithOverflow="true" isBlankWhenNull="true">
									<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30" uuid="d990f3bb-a2ab-46cb-ba76-84e14ccd3675"/>
									<textElement textAlignment="Center" verticalAlignment="Middle"/>
									<textFieldExpression><![CDATA[$F{Comparison_date_last_two_years}]]></textFieldExpression>
									<patternExpression><![CDATA[$P{userDateFormat}]]></patternExpression>
								</textField>
							</jr:detailCell>
						</jr:column>
						<jr:column width="90" uuid="67e9a236-5612-4075-8f80-82d7dbc79797">
							<printWhenExpression><![CDATA[$P{param_IsShowYear2019DataChecked}]]></printWhenExpression>
							<jr:tableHeader height="0" rowSpan="1"/>
							<jr:tableFooter height="0" rowSpan="1"/>
							<jr:columnHeader style="table_CH" height="50" rowSpan="1">
								<textField isStretchWithOverflow="true" isBlankWhenNull="true">
									<reportElement stretchType="RelativeToBandHeight" mode="Opaque" x="0" y="0" width="90" height="50" backcolor="#BFE1FF" uuid="a00a5090-3119-4596-b710-c0c191ec6ca3"/>
									<box>
										<topPen lineWidth="0.5"/>
										<leftPen lineWidth="0.5"/>
										<bottomPen lineWidth="0.5"/>
										<rightPen lineWidth="0.5"/>
									</box>
									<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
										<font isBold="true"/>
									</textElement>
									<textFieldExpression><![CDATA[$R{common.comparisonDateYear2019}]]></textFieldExpression>
								</textField>
							</jr:columnHeader>
							<jr:detailCell style="table_TD" height="30" rowSpan="1">
								<textField isStretchWithOverflow="true" isBlankWhenNull="true">
									<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30" uuid="d990f3bb-a2ab-46cb-ba76-84e14ccd3675"/>
									<textElement textAlignment="Center" verticalAlignment="Middle"/>
									<textFieldExpression><![CDATA[$F{Comparison_date_year_2019}]]></textFieldExpression>
									<patternExpression><![CDATA[$P{userDateFormat}]]></patternExpression>
								</textField>
							</jr:detailCell>
						</jr:column>
						<jr:column width="90" uuid="4c05d479-5c9f-4894-ac88-ac4247421c53">
							<jr:columnHeader style="table_CH" height="50" rowSpan="1">
								<textField isStretchWithOverflow="true" isBlankWhenNull="true">
									<reportElement stretchType="RelativeToBandHeight" mode="Opaque" x="0" y="0" width="90" height="50" backcolor="#BFE1FF" uuid="20f5cb5e-1c33-49d3-9321-0bd80fe08e66"/>
									<box>
										<topPen lineWidth="0.5"/>
										<leftPen lineWidth="0.5"/>
										<bottomPen lineWidth="0.5"/>
										<rightPen lineWidth="0.5"/>
									</box>
									<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
										<font isBold="true"/>
									</textElement>
									<textFieldExpression><![CDATA[$R{room.type}]]></textFieldExpression>
								</textField>
							</jr:columnHeader>
							<jr:detailCell style="table_TD" height="30" rowSpan="1">
								<textField isStretchWithOverflow="true" isBlankWhenNull="true">
									<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30" uuid="19ba6347-41d4-4907-96ae-ff136939c008"/>
									<textElement textAlignment="Center" verticalAlignment="Middle"/>
									<textFieldExpression><![CDATA[$F{Accom_Type_Name}]]></textFieldExpression>
								</textField>
							</jr:detailCell>
						</jr:column>
						<jr:column width="90" uuid="6dd1b17d-31e5-41e6-a8e0-2ed827e7e912">
							<jr:columnHeader style="table_CH" height="50" rowSpan="1">
								<textField isStretchWithOverflow="true" isBlankWhenNull="true">
									<reportElement stretchType="RelativeToBandHeight" mode="Opaque" x="0" y="0" width="90" height="50" backcolor="#BFE1FF" uuid="2b7a03c5-493c-4c84-af74-9b3fc4aac259"/>
									<box>
										<topPen lineWidth="0.5"/>
										<leftPen lineWidth="0.5"/>
										<bottomPen lineWidth="0.5"/>
										<rightPen lineWidth="0.5"/>
									</box>
									<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
										<font isBold="true"/>
									</textElement>
									<textFieldExpression><![CDATA[$R{room.class}]]></textFieldExpression>
								</textField>
							</jr:columnHeader>
							<jr:detailCell style="table_TD" height="30" rowSpan="1">
								<textField isStretchWithOverflow="true" isBlankWhenNull="true">
									<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30" uuid="d2e87d4e-e79b-4269-9a8f-3287102197ca"/>
									<textElement textAlignment="Center" verticalAlignment="Middle"/>
									<textFieldExpression><![CDATA[$F{Accom_Class_Name}]]></textFieldExpression>
								</textField>
							</jr:detailCell>
						</jr:column>
						<jr:columnGroup width="180" uuid="c4f60daf-c6cd-4a57-9bc4-07731e92ef39">
							<printWhenExpression><![CDATA[new Boolean($P{IsCap})]]></printWhenExpression>
							<jr:column width="90" uuid="8fc3c931-38ac-4e9e-94ee-8aadaa9b2e7d">
								<jr:columnHeader style="table_CH" height="50" rowSpan="1">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" mode="Opaque" x="0" y="0" width="90" height="50" backcolor="#BFE1FF" uuid="6922032e-72ba-4694-8db3-cfdef7ce10e0"/>
										<box>
											<topPen lineWidth="0.5"/>
											<leftPen lineWidth="0.5"/>
											<bottomPen lineWidth="0.5"/>
											<rightPen lineWidth="0.5"/>
										</box>
										<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression><![CDATA[$R{common.capacity} + " " + $R{common.thisYear}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:detailCell style="table_TD" height="30" rowSpan="1">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30" uuid="dd427e3e-5927-4c89-a56f-60b53546e0b3"/>
										<textElement textAlignment="Center" verticalAlignment="Middle"/>
										<textFieldExpression><![CDATA[$F{Accom_Capacity_ty}]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
							<jr:column width="90" uuid="2f4b38d5-ef74-41da-a164-8454f9e8e998">
								<printWhenExpression><![CDATA[$P{param_IsShowLastYearDataChecked}]]></printWhenExpression>
								<jr:tableHeader height="0" rowSpan="1"/>
								<jr:tableFooter height="0" rowSpan="1"/>
								<jr:columnHeader style="table_CH" height="50" rowSpan="1">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" mode="Opaque" x="0" y="0" width="90" height="50" backcolor="#BFE1FF" uuid="ec50740f-fc6e-4d2a-a9c1-3c7caeb9c3e3"/>
										<box>
											<topPen lineWidth="0.5"/>
											<leftPen lineWidth="0.5"/>
											<bottomPen lineWidth="0.5"/>
											<rightPen lineWidth="0.5"/>
										</box>
										<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression><![CDATA[$R{common.capacity} + " " + $R{common.lastYearActual}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:detailCell style="table_TD" height="30" rowSpan="1">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30" uuid="9e8121a1-ee47-4f26-95e2-c274483d0e59"/>
										<textElement textAlignment="Center" verticalAlignment="Middle"/>
										<textFieldExpression><![CDATA[$F{Accom_Capacity_ly}]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
						</jr:columnGroup>

                        <jr:columnGroup width="180" uuid="c4f60daf-c6cd-4a57-9bc4-07731e92ef39">
							<printWhenExpression><![CDATA[new Boolean($P{IsRemainingCapacity})]]></printWhenExpression>
							<jr:column width="90" uuid="8fc3c931-38ac-4e9e-94ee-8aadaa9b2e7d">
								<jr:columnHeader style="table_CH" height="50" rowSpan="1">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" mode="Opaque" x="0" y="0" width="90" height="50" backcolor="#BFE1FF" uuid="6922032e-72ba-4694-8db3-cfdef7ce10e0"/>
										<box>
											<topPen lineWidth="0.5"/>
											<leftPen lineWidth="0.5"/>
											<bottomPen lineWidth="0.5"/>
											<rightPen lineWidth="0.5"/>
										</box>
										<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression><![CDATA[$R{REMAINING_CAPACITY} + " " + $R{common.thisYear}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:detailCell style="table_TD" height="30" rowSpan="1">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30" uuid="dd427e3e-5927-4c89-a56f-60b53546e0b3"/>
										<textElement textAlignment="Center" verticalAlignment="Middle"/>
										<textFieldExpression><![CDATA[$F{Remaining_Capacity_This_Year}]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
							<jr:column width="90" uuid="2f4b38d5-ef74-41da-a164-8454f9e8e998">
								<printWhenExpression><![CDATA[$P{param_IsShowLastYearDataChecked}]]></printWhenExpression>
								<jr:tableHeader height="0" rowSpan="1"/>
								<jr:tableFooter height="0" rowSpan="1"/>
								<jr:columnHeader style="table_CH" height="50" rowSpan="1">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" mode="Opaque" x="0" y="0" width="90" height="50" backcolor="#BFE1FF" uuid="ec50740f-fc6e-4d2a-a9c1-3c7caeb9c3e3"/>
										<box>
											<topPen lineWidth="0.5"/>
											<leftPen lineWidth="0.5"/>
											<bottomPen lineWidth="0.5"/>
											<rightPen lineWidth="0.5"/>
										</box>
										<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression><![CDATA[$R{REMAINING_CAPACITY} + " " + $R{common.lastYearActual}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:detailCell style="table_TD" height="30" rowSpan="1">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30" uuid="9e8121a1-ee47-4f26-95e2-c274483d0e59"/>
										<textElement textAlignment="Center" verticalAlignment="Middle"/>
										<textFieldExpression><![CDATA[$F{Remaining_Capacity_Last_Year}]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
						</jr:columnGroup>

						<jr:columnGroup width="630" uuid="5a21d7df-8218-4f08-afb5-75fa7473180e">
							<printWhenExpression><![CDATA[new Boolean($P{IsRS}) || new Boolean($P{IsRS_STLY_RT}) || new Boolean($P{IsRS_ST2Y_RT}) || new Boolean($P{IsRS_ST19_RT})]]></printWhenExpression>
							<jr:column width="90" uuid="ccb13299-a42e-4c35-92e7-050041b74116">
								<printWhenExpression><![CDATA[new Boolean($P{IsRS})]]></printWhenExpression>
								<jr:columnHeader style="table_CH" height="50" rowSpan="1">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" mode="Opaque" x="0" y="0" width="90" height="50" backcolor="#BFE1FF" uuid="32bba919-f69d-4d2a-9935-ad205ace28a2"/>
										<box>
											<topPen lineWidth="0.5"/>
											<leftPen lineWidth="0.5"/>
											<bottomPen lineWidth="0.5"/>
											<rightPen lineWidth="0.5"/>
										</box>
										<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression><![CDATA[$R{roomssold} + " " + $R{common.thisYear}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:detailCell style="table_TD" height="30" rowSpan="1">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30" uuid="4b2eb048-558f-4574-a74f-97851a2f079f"/>
										<textElement textAlignment="Center" verticalAlignment="Middle"/>
										<textFieldExpression><![CDATA[$F{Rooms_Sold_ty} == null ? 0 : $F{Rooms_Sold_ty}]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
							<jr:column width="90" uuid="ccf7cd00-73d2-4223-b7c2-89e7451c644e">
								<printWhenExpression><![CDATA[$P{param_IsShowLastYearDataChecked}]]></printWhenExpression>
								<jr:tableHeader height="0" rowSpan="1"/>
								<jr:tableFooter height="0" rowSpan="1"/>
								<jr:columnHeader style="table_CH" height="50" rowSpan="1">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" mode="Opaque" x="0" y="0" width="90" height="50" backcolor="#BFE1FF" uuid="0f58aca7-b082-4d49-9f6e-54cb09779e48"/>
										<box>
											<topPen lineWidth="0.5"/>
											<leftPen lineWidth="0.5"/>
											<bottomPen lineWidth="0.5"/>
											<rightPen lineWidth="0.5"/>
										</box>
										<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression><![CDATA[$R{roomssold} + " " + $R{common.lastYearActual}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:columnFooter height="0" rowSpan="1"/>
								<jr:detailCell style="table_TD" height="30" rowSpan="1">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30" uuid="865f231a-23a6-4d08-b881-7c05b3c69eb8"/>
										<textElement textAlignment="Center" verticalAlignment="Middle"/>
										<textFieldExpression><![CDATA[$F{Rooms_Sold_ly} == null ? 0 : $F{Rooms_Sold_ly}]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
							<jr:column width="90" uuid="ccf7cd00-73d2-4223-b7c2-89e7451c644e">
								<printWhenExpression><![CDATA[$P{param_IsShowLast2YearsDataChecked}]]></printWhenExpression>
								<jr:tableHeader height="0" rowSpan="1"/>
								<jr:tableFooter height="0" rowSpan="1"/>
								<jr:columnHeader style="table_CH" height="50" rowSpan="1">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" mode="Opaque" x="0" y="0" width="90" height="50" backcolor="#BFE1FF" uuid="0f58aca7-b082-4d49-9f6e-54cb09779e48"/>
										<box>
											<topPen lineWidth="0.5"/>
											<leftPen lineWidth="0.5"/>
											<bottomPen lineWidth="0.5"/>
											<rightPen lineWidth="0.5"/>
										</box>
										<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression><![CDATA[$R{roomssold} + " " + $R{common.last2YearsActual}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:columnFooter height="0" rowSpan="1"/>
								<jr:detailCell style="table_TD" height="30" rowSpan="1">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30" uuid="865f231a-23a6-4d08-b881-7c05b3c69eb8"/>
										<textElement textAlignment="Center" verticalAlignment="Middle"/>
										<textFieldExpression><![CDATA[$F{Rooms_Sold_l2y} == null ? 0 : $F{Rooms_Sold_l2y}]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
							<jr:column width="90" uuid="ccf7cd00-73d2-4223-b7c2-89e7451c644e">
								<printWhenExpression><![CDATA[$P{param_IsShowYear2019DataChecked}]]></printWhenExpression>
								<jr:tableHeader height="0" rowSpan="1"/>
								<jr:tableFooter height="0" rowSpan="1"/>
								<jr:columnHeader style="table_CH" height="50" rowSpan="1">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" mode="Opaque" x="0" y="0" width="90" height="50" backcolor="#BFE1FF" uuid="0f58aca7-b082-4d49-9f6e-54cb09779e48"/>
										<box>
											<topPen lineWidth="0.5"/>
											<leftPen lineWidth="0.5"/>
											<bottomPen lineWidth="0.5"/>
											<rightPen lineWidth="0.5"/>
										</box>
										<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression><![CDATA[$R{roomssold} + " " + $R{common.year2019Actual}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:columnFooter height="0" rowSpan="1"/>
								<jr:detailCell style="table_TD" height="30" rowSpan="1">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30" uuid="865f231a-23a6-4d08-b881-7c05b3c69eb8"/>
										<textElement textAlignment="Center" verticalAlignment="Middle"/>
										<textFieldExpression><![CDATA[$F{Rooms_Sold_y2019} == null ? 0 : $F{Rooms_Sold_y2019}]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
							<jr:column width="90" uuid="7d0b9013-4849-4bb0-b23c-d03dfa766b1d">
								<printWhenExpression><![CDATA[new Boolean($P{IsRS_STLY_RT})]]></printWhenExpression>
								<jr:tableHeader height="0" rowSpan="1"/>
								<jr:tableFooter height="0" rowSpan="1"/>
								<jr:columnHeader style="table_CH" height="50" rowSpan="1">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" mode="Opaque" x="0" y="0" width="90" height="50" backcolor="#BFE1FF" uuid="1755a5ce-49f6-4a76-9425-a910284281a8"/>
										<box>
											<topPen lineWidth="0.5"/>
											<leftPen lineWidth="0.5"/>
											<bottomPen lineWidth="0.5"/>
											<rightPen lineWidth="0.5"/>
										</box>
										<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression><![CDATA[$R{roomssold} + " " + $R{common.STLY}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:columnFooter height="0" rowSpan="1"/>
								<jr:detailCell style="table_TD" height="30" rowSpan="1">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30" uuid="cd5aa365-fbd5-465e-9d38-43527ae7e70c"/>
										<textElement textAlignment="Center" verticalAlignment="Middle"/>
										<textFieldExpression><![CDATA[$F{Rooms_Sold_stly} == null ? 0 : $F{Rooms_Sold_stly}]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
						<jr:column width="90" uuid="7d0b9013-4849-4bb0-b23c-d03dfa766b1d">
								<printWhenExpression><![CDATA[new Boolean($P{IsRS_ST2Y_RT})]]></printWhenExpression>
								<jr:tableHeader height="0" rowSpan="1"/>
								<jr:tableFooter height="0" rowSpan="1"/>
								<jr:columnHeader style="table_CH" height="50" rowSpan="1">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" mode="Opaque" x="0" y="0" width="90" height="50" backcolor="#BFE1FF" uuid="1755a5ce-49f6-4a76-9425-a910284281a8"/>
										<box>
											<topPen lineWidth="0.5"/>
											<leftPen lineWidth="0.5"/>
											<bottomPen lineWidth="0.5"/>
											<rightPen lineWidth="0.5"/>
										</box>
										<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression><![CDATA[$R{roomssold} + " " + $R{common.ST2Y}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:columnFooter height="0" rowSpan="1"/>
								<jr:detailCell style="table_TD" height="30" rowSpan="1">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30" uuid="cd5aa365-fbd5-465e-9d38-43527ae7e70c"/>
										<textElement textAlignment="Center" verticalAlignment="Middle"/>
										<textFieldExpression><![CDATA[$F{Rooms_Sold_st2y} == null ? 0 : $F{Rooms_Sold_st2y}]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
						<jr:column width="90" uuid="7d0b9013-4849-4bb0-b23c-d03dfa766b1d">
								<printWhenExpression><![CDATA[new Boolean($P{IsRS_ST19_RT})]]></printWhenExpression>
								<jr:tableHeader height="0" rowSpan="1"/>
								<jr:tableFooter height="0" rowSpan="1"/>
								<jr:columnHeader style="table_CH" height="50" rowSpan="1">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" mode="Opaque" x="0" y="0" width="90" height="50" backcolor="#BFE1FF" uuid="1755a5ce-49f6-4a76-9425-a910284281a8"/>
										<box>
											<topPen lineWidth="0.5"/>
											<leftPen lineWidth="0.5"/>
											<bottomPen lineWidth="0.5"/>
											<rightPen lineWidth="0.5"/>
										</box>
										<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression><![CDATA[$R{roomssold} + " " + $R{common.ST19}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:columnFooter height="0" rowSpan="1"/>
								<jr:detailCell style="table_TD" height="30" rowSpan="1">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30" uuid="cd5aa365-fbd5-465e-9d38-43527ae7e70c"/>
										<textElement textAlignment="Center" verticalAlignment="Middle"/>
										<textFieldExpression><![CDATA[$F{Rooms_Sold_st19} == null ? 0 : $F{Rooms_Sold_st19}]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
						</jr:columnGroup>
						<jr:columnGroup width="180" uuid="a6c7644d-647a-4ad9-aa00-8af4a47c8907">
							<printWhenExpression><![CDATA[new Boolean($P{IsArr})]]></printWhenExpression>
							<jr:column width="90" uuid="7dbe4af7-541f-4140-ab06-7653f6cfc806">
								<jr:columnHeader style="table_CH" height="50" rowSpan="1">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" mode="Opaque" x="0" y="0" width="90" height="50" backcolor="#BFE1FF" uuid="e2768a9a-869a-4a3c-a997-cb91feaf385f"/>
										<box>
											<topPen lineWidth="0.5"/>
											<leftPen lineWidth="0.5"/>
											<bottomPen lineWidth="0.5"/>
											<rightPen lineWidth="0.5"/>
										</box>
										<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression><![CDATA[$R{arrivals} + " " + $R{common.thisYear}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:detailCell style="table_TD" height="30" rowSpan="1">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30" uuid="34002db5-99a7-467c-a400-184010865de4"/>
										<textElement textAlignment="Center" verticalAlignment="Middle"/>
										<textFieldExpression><![CDATA[$F{Arrivals_ty}]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
							<jr:column width="90" uuid="a0538354-86b5-4aef-a782-ec719e68d1fa">
								<printWhenExpression><![CDATA[$P{param_IsShowLastYearDataChecked}]]></printWhenExpression>
								<jr:tableHeader height="0" rowSpan="1"/>
								<jr:tableFooter height="0" rowSpan="1"/>
								<jr:columnHeader style="table_CH" height="50" rowSpan="1">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" mode="Opaque" x="0" y="0" width="90" height="50" backcolor="#BFE1FF" uuid="aac2960e-b0e6-4241-b6be-29543e99a265"/>
										<box>
											<topPen lineWidth="0.5"/>
											<leftPen lineWidth="0.5"/>
											<bottomPen lineWidth="0.5"/>
											<rightPen lineWidth="0.5"/>
										</box>
										<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression><![CDATA[$R{arrivals} + " " + $R{common.lastYearActual}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:columnFooter height="0" rowSpan="1"/>
								<jr:detailCell style="table_TD" height="30" rowSpan="1">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30" uuid="bc446800-a5c9-4884-ac50-92ff68d09c67"/>
										<textElement textAlignment="Center" verticalAlignment="Middle"/>
										<textFieldExpression><![CDATA[$F{Arrivals_ly}]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
						</jr:columnGroup>
						<jr:columnGroup width="180" uuid="6673da0d-cd19-4a9c-9734-f33a029c97a2">
							<printWhenExpression><![CDATA[new Boolean($P{IsDep})]]></printWhenExpression>
							<jr:column width="90" uuid="2a915d1c-e6b3-4d47-9ff8-43321fe326a0">
								<jr:columnHeader style="table_CH" height="50" rowSpan="1">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" mode="Opaque" x="0" y="0" width="90" height="50" backcolor="#BFE1FF" uuid="6195276a-7e16-4823-8cf6-7888503a146d"/>
										<box>
											<topPen lineWidth="0.5"/>
											<leftPen lineWidth="0.5"/>
											<bottomPen lineWidth="0.5"/>
											<rightPen lineWidth="0.5"/>
										</box>
										<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression><![CDATA[$R{common.departures} + " " + $R{common.thisYear}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:detailCell style="table_TD" height="30" rowSpan="1">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30" uuid="1a0cf38c-9641-4335-b01b-90b8088428e8"/>
										<textElement textAlignment="Center" verticalAlignment="Middle"/>
										<textFieldExpression><![CDATA[$F{Departures_ty}]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
							<jr:column width="90" uuid="0cfeb773-8e0d-4474-9ad8-7faa816dee41">
								<printWhenExpression><![CDATA[$P{param_IsShowLastYearDataChecked}]]></printWhenExpression>
								<jr:tableHeader height="0" rowSpan="1"/>
								<jr:tableFooter height="0" rowSpan="1"/>
								<jr:columnHeader style="table_CH" height="50" rowSpan="1">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" mode="Opaque" x="0" y="0" width="90" height="50" backcolor="#BFE1FF" uuid="4eff4715-dae3-4e7c-8784-8fb0b2a23048"/>
										<box>
											<topPen lineWidth="0.5"/>
											<leftPen lineWidth="0.5"/>
											<bottomPen lineWidth="0.5"/>
											<rightPen lineWidth="0.5"/>
										</box>
										<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression><![CDATA[$R{common.departures} + " " + $R{common.lastYearActual}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:columnFooter height="0" rowSpan="1"/>
								<jr:detailCell style="table_TD" height="30" rowSpan="1">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30" uuid="db809cb8-f39c-47e7-8f44-5fe79db6b92d"/>
										<textElement textAlignment="Center" verticalAlignment="Middle"/>
										<textFieldExpression><![CDATA[$F{Departures_ly}]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
						</jr:columnGroup>
						<jr:columnGroup width="180" uuid="793f03b7-c3cc-4f9d-92df-3ca87c7e4881">
							<printWhenExpression><![CDATA[new Boolean($P{IsOOO})]]></printWhenExpression>
							<jr:column width="90" uuid="fc620131-ffc3-45f3-bc1d-6c8f261699ae">
								<jr:columnHeader style="table_CH" height="50" rowSpan="1">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" mode="Opaque" x="0" y="0" width="90" height="50" backcolor="#BFE1FF" uuid="*************-400e-8c18-22f5f7b29c53"/>
										<box>
											<topPen lineWidth="0.5"/>
											<leftPen lineWidth="0.5"/>
											<bottomPen lineWidth="0.5"/>
											<rightPen lineWidth="0.5"/>
										</box>
										<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression><![CDATA[$R{report.column.roomsNAOutOfOrder} + " " + $R{common.thisYear}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:detailCell style="table_TD" height="30" rowSpan="1">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30" uuid="882a8043-8027-46e2-b3f3-ccab8dc9465e"/>
										<textElement textAlignment="Center" verticalAlignment="Middle"/>
										<textFieldExpression><![CDATA[$F{Rooms_Not_Avail_Maint_ty}]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
							<jr:column width="90" uuid="f2d9cf82-52e3-4297-9f9e-dfce90a23b79">
								<printWhenExpression><![CDATA[$P{param_IsShowLastYearDataChecked}]]></printWhenExpression>
								<jr:tableHeader height="0" rowSpan="1"/>
								<jr:tableFooter height="0" rowSpan="1"/>
								<jr:columnHeader style="table_CH" height="50" rowSpan="1">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" mode="Opaque" x="0" y="0" width="90" height="50" backcolor="#BFE1FF" uuid="38b0122b-a1c5-4d94-9046-9c98ce037d89"/>
										<box>
											<topPen lineWidth="0.5"/>
											<leftPen lineWidth="0.5"/>
											<bottomPen lineWidth="0.5"/>
											<rightPen lineWidth="0.5"/>
										</box>
										<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression><![CDATA[$R{report.column.roomsNAOutOfOrder} + " " + $R{common.lastYearActual}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:columnFooter height="0" rowSpan="1"/>
								<jr:detailCell style="table_TD" height="30" rowSpan="1">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30" uuid="b4e5c4cc-1250-4dff-8734-69dae69ab8eb"/>
										<textElement textAlignment="Center" verticalAlignment="Middle"/>
										<textFieldExpression><![CDATA[$F{Rooms_Not_Avail_Maint_ly}]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
						</jr:columnGroup>
						<jr:columnGroup width="180" uuid="0b5f657b-443a-412c-b41b-2961acbecf10">
							<printWhenExpression><![CDATA[new Boolean($P{IsRNAO})]]></printWhenExpression>
							<jr:column width="90" uuid="2b5d3c7b-97d1-434c-9783-f041c4d8b5f9">
								<jr:columnHeader style="table_CH" height="50" rowSpan="1">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" mode="Opaque" x="0" y="0" width="90" height="50" backcolor="#BFE1FF" uuid="00b71d9e-2665-443f-8590-86ed31866cf9"/>
										<box>
											<topPen lineWidth="0.5"/>
											<leftPen lineWidth="0.5"/>
											<bottomPen lineWidth="0.5"/>
											<rightPen lineWidth="0.5"/>
										</box>
										<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression><![CDATA[$R{report.column.roomsNAOther} + " " + $R{common.thisYear}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:detailCell style="table_TD" height="30" rowSpan="1">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30" uuid="216983c3-5d8e-4d2e-b4e9-5465c1dc15e8"/>
										<textElement textAlignment="Center" verticalAlignment="Middle"/>
										<textFieldExpression><![CDATA[$F{Rooms_Not_Avail_Other_ty}]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
							<jr:column width="90" uuid="368545d9-1e88-46a2-a439-a98ee1ff6a06">
								<printWhenExpression><![CDATA[$P{param_IsShowLastYearDataChecked}]]></printWhenExpression>
								<jr:tableHeader height="0" rowSpan="1"/>
								<jr:tableFooter height="0" rowSpan="1"/>
								<jr:columnHeader style="table_CH" height="50" rowSpan="1">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" mode="Opaque" x="0" y="0" width="90" height="50" backcolor="#BFE1FF" uuid="44c0d999-ee54-4955-b8f1-4ac959c52361"/>
										<box>
											<topPen lineWidth="0.5"/>
											<leftPen lineWidth="0.5"/>
											<bottomPen lineWidth="0.5"/>
											<rightPen lineWidth="0.5"/>
										</box>
										<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression><![CDATA[$R{report.column.roomsNAOther} + " " + $R{common.lastYearActual}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:columnFooter height="0" rowSpan="1"/>
								<jr:detailCell style="table_TD" height="30" rowSpan="1">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30" uuid="69e48e86-7e1d-4238-9674-23b3e3fcbc0c"/>
										<textElement textAlignment="Center" verticalAlignment="Middle"/>
										<textFieldExpression><![CDATA[$F{Rooms_Not_Avail_Other_ly}]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
						</jr:columnGroup>
						<jr:columnGroup width="180" uuid="192ac3e2-30b7-4a14-b588-8b3d6350ae93">
							<printWhenExpression><![CDATA[new Boolean($P{IsCncel})]]></printWhenExpression>
							<jr:column width="90" uuid="db805c98-7ac3-470b-afa2-c631b1139f04">
								<jr:columnHeader style="table_CH" height="50" rowSpan="1">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" mode="Opaque" x="0" y="0" width="90" height="50" backcolor="#BFE1FF" uuid="de7229f2-2a6e-4b7a-a6d4-1df38a58e47a"/>
										<box>
											<topPen lineWidth="0.5"/>
											<leftPen lineWidth="0.5"/>
											<bottomPen lineWidth="0.5"/>
											<rightPen lineWidth="0.5"/>
										</box>
										<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression><![CDATA[$R{cancelled} + " " + $R{common.thisYear}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:detailCell style="table_TD" height="30" rowSpan="1">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30" uuid="f0384389-9ea6-47d3-b7de-3c121330e275"/>
										<textElement textAlignment="Center" verticalAlignment="Middle"/>
										<textFieldExpression><![CDATA[$F{Cancellations_ty}]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
							<jr:column width="90" uuid="b186fa70-a145-4ce1-9cb2-1fbfffbd1dac">
								<printWhenExpression><![CDATA[$P{param_IsShowLastYearDataChecked}]]></printWhenExpression>
								<jr:tableHeader height="0" rowSpan="1"/>
								<jr:tableFooter height="0" rowSpan="1"/>
								<jr:columnHeader style="table_CH" height="50" rowSpan="1">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" mode="Opaque" x="0" y="0" width="90" height="50" backcolor="#BFE1FF" uuid="f3dab2f0-147a-41b2-a220-0d17267cb722"/>
										<box>
											<topPen lineWidth="0.5"/>
											<leftPen lineWidth="0.5"/>
											<bottomPen lineWidth="0.5"/>
											<rightPen lineWidth="0.5"/>
										</box>
										<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression><![CDATA[$R{cancelled} + " " + $R{common.lastYearActual}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:columnFooter height="0" rowSpan="1"/>
								<jr:detailCell style="table_TD" height="30" rowSpan="1">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30" uuid="99c1cb9d-3b44-4327-952a-7825f8217172"/>
										<textElement textAlignment="Center" verticalAlignment="Middle"/>
										<textFieldExpression><![CDATA[$F{Cancellations_ly}]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
						</jr:columnGroup>
						<jr:columnGroup width="180" uuid="bc1ea68f-5102-45d7-8341-fc3d1dea7854">
							<printWhenExpression><![CDATA[new Boolean($P{IsNS})]]></printWhenExpression>
							<jr:column width="90" uuid="812642cb-99f0-4801-a45a-83f10c163e28">
								<jr:columnHeader style="table_CH" height="50" rowSpan="1">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" mode="Opaque" x="0" y="0" width="90" height="50" backcolor="#BFE1FF" uuid="e26be90d-58ba-456b-804d-989507ed980e"/>
										<box>
											<topPen lineWidth="0.5"/>
											<leftPen lineWidth="0.5"/>
											<bottomPen lineWidth="0.5"/>
											<rightPen lineWidth="0.5"/>
										</box>
										<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression><![CDATA[$R{noshow} + " " + $R{common.thisYear}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:detailCell style="table_TD" height="30" rowSpan="1">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30" uuid="d754b65f-100a-406c-907f-f38723efc1b8"/>
										<textElement textAlignment="Center" verticalAlignment="Middle"/>
										<textFieldExpression><![CDATA[$F{No_Shows_ty}]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
							<jr:column width="90" uuid="faec8058-4724-4df4-9ac5-14a8b7e53604">
								<printWhenExpression><![CDATA[$P{param_IsShowLastYearDataChecked}]]></printWhenExpression>
								<jr:tableHeader height="0" rowSpan="1"/>
								<jr:tableFooter height="0" rowSpan="1"/>
								<jr:columnHeader style="table_CH" height="50" rowSpan="1">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" mode="Opaque" x="0" y="0" width="90" height="50" backcolor="#BFE1FF" uuid="6699f001-6138-4ca7-bbe4-f84d95a240c0"/>
										<box>
											<topPen lineWidth="0.5"/>
											<leftPen lineWidth="0.5"/>
											<bottomPen lineWidth="0.5"/>
											<rightPen lineWidth="0.5"/>
										</box>
										<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression><![CDATA[$R{noshow} + " " + $R{common.lastYearActual}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:columnFooter height="0" rowSpan="1"/>
								<jr:detailCell style="table_TD" height="30" rowSpan="1">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30" uuid="9799406e-368c-45c9-a859-c6300b4ff3b5"/>
										<textElement textAlignment="Center" verticalAlignment="Middle"/>
										<textFieldExpression><![CDATA[$F{No_Shows_ly}]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
						</jr:columnGroup>
						<jr:columnGroup width="630" uuid="07e0f8f2-9098-448d-bef3-3487e8ac07b9">
							<printWhenExpression><![CDATA[new Boolean($P{IsRev}) || new Boolean($P{IsRev_STLY_RT}) || new Boolean($P{IsRev_ST2Y_RT}) || new Boolean($P{IsRev_ST19_RT})]]></printWhenExpression>
							<jr:column width="90" uuid="51db28a6-6d23-4b9d-884f-c87edd8a7934">
								<printWhenExpression><![CDATA[new Boolean($P{IsRev})]]></printWhenExpression>
								<jr:columnHeader style="table_CH" height="50" rowSpan="1">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" mode="Opaque" x="0" y="0" width="90" height="50" backcolor="#BFE1FF" uuid="e80cbfbe-ba1f-4547-8e05-bfd24e1f6ade"/>
										<box>
											<topPen lineWidth="0.5"/>
											<leftPen lineWidth="0.5"/>
											<bottomPen lineWidth="0.5"/>
											<rightPen lineWidth="0.5"/>
										</box>
										<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression><![CDATA[$R{report.column.bookedRoomRevenue} + " " + $R{common.thisYear}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:detailCell style="table_TD" height="30" rowSpan="1">
									<textField pattern="###0.00" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF" uuid="7a5ef5a3-7fd5-435f-bd51-c6f11fe72364"/>
										<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
											<font size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" />
											<paragraph lineSpacing="Single"/>
										</textElement>
										<textFieldExpression><![CDATA[$F{onBooks_Revenue_ty} == null ? 0 : $F{onBooks_Revenue_ty}]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
							<jr:column width="90" uuid="a51124a6-874a-4f9e-b4f8-3e6ffa46a20d">
								<printWhenExpression><![CDATA[$P{param_IsShowLastYearDataChecked}]]></printWhenExpression>
								<jr:tableHeader height="0" rowSpan="1"/>
								<jr:tableFooter height="0" rowSpan="1"/>
								<jr:columnHeader style="table_CH" height="50" rowSpan="1">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" mode="Opaque" x="0" y="0" width="90" height="50" backcolor="#BFE1FF" uuid="55a79f57-7f7a-4a91-a2a1-c824f24f873d"/>
										<box>
											<topPen lineWidth="0.5"/>
											<leftPen lineWidth="0.5"/>
											<bottomPen lineWidth="0.5"/>
											<rightPen lineWidth="0.5"/>
										</box>
										<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression><![CDATA[$R{report.column.bookedRoomRevenue} + " " + $R{common.lastYearActual}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:columnFooter height="0" rowSpan="1"/>
								<jr:detailCell style="table_TD" height="30" rowSpan="1">
									<textField pattern="###0.00" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF" uuid="77db0c04-20e2-430a-abf9-e09df9542913"/>
										<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
											<font isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" />
											<paragraph lineSpacing="Single"/>
										</textElement>
										<textFieldExpression><![CDATA[$F{onBooks_Revenue_ly} == null ? 0 : $F{onBooks_Revenue_ly}]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
							<jr:column width="90" uuid="a51124a6-874a-4f9e-b4f8-3e6ffa46a20d">
								<printWhenExpression><![CDATA[$P{param_IsShowLast2YearsDataChecked}]]></printWhenExpression>
								<jr:tableHeader height="0" rowSpan="1"/>
								<jr:tableFooter height="0" rowSpan="1"/>
								<jr:columnHeader style="table_CH" height="50" rowSpan="1">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" mode="Opaque" x="0" y="0" width="90" height="50" backcolor="#BFE1FF" uuid="55a79f57-7f7a-4a91-a2a1-c824f24f873d"/>
										<box>
											<topPen lineWidth="0.5"/>
											<leftPen lineWidth="0.5"/>
											<bottomPen lineWidth="0.5"/>
											<rightPen lineWidth="0.5"/>
										</box>
										<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression><![CDATA[$R{report.column.bookedRoomRevenue} + " " + $R{common.last2YearsActual}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:columnFooter height="0" rowSpan="1"/>
								<jr:detailCell style="table_TD" height="30" rowSpan="1">
									<textField pattern="###0.00" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF" uuid="77db0c04-20e2-430a-abf9-e09df9542913"/>
										<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
											<font isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" />
											<paragraph lineSpacing="Single"/>
										</textElement>
										<textFieldExpression><![CDATA[$F{onBooks_Revenue_l2y} == null ? 0 : $F{onBooks_Revenue_l2y}]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
							<jr:column width="90" uuid="a51124a6-874a-4f9e-b4f8-3e6ffa46a20d">
								<printWhenExpression><![CDATA[$P{param_IsShowYear2019DataChecked}]]></printWhenExpression>
								<jr:tableHeader height="0" rowSpan="1"/>
								<jr:tableFooter height="0" rowSpan="1"/>
								<jr:columnHeader style="table_CH" height="50" rowSpan="1">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" mode="Opaque" x="0" y="0" width="90" height="50" backcolor="#BFE1FF" uuid="55a79f57-7f7a-4a91-a2a1-c824f24f873d"/>
										<box>
											<topPen lineWidth="0.5"/>
											<leftPen lineWidth="0.5"/>
											<bottomPen lineWidth="0.5"/>
											<rightPen lineWidth="0.5"/>
										</box>
										<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression><![CDATA[$R{report.column.bookedRoomRevenue} + " " + $R{common.year2019Actual}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:columnFooter height="0" rowSpan="1"/>
								<jr:detailCell style="table_TD" height="30" rowSpan="1">
									<textField pattern="###0.00" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF" uuid="77db0c04-20e2-430a-abf9-e09df9542913"/>
										<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
											<font isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" />
											<paragraph lineSpacing="Single"/>
										</textElement>
										<textFieldExpression><![CDATA[$F{onBooks_Revenue_y2019} == null ? 0 : $F{onBooks_Revenue_y2019}]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
							<jr:column width="90" uuid="30a38064-2be2-4f73-8bb7-94b969075158">
								<printWhenExpression><![CDATA[new Boolean($P{IsRev_STLY_RT})]]></printWhenExpression>
								<jr:tableHeader height="0" rowSpan="1"/>
								<jr:tableFooter height="0" rowSpan="1"/>
								<jr:columnHeader style="table_CH" height="50" rowSpan="1">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" mode="Opaque" x="0" y="0" width="90" height="50" backcolor="#BFE1FF" uuid="cff60c83-21a2-40e7-b2ff-bdebd33a104b"/>
										<box>
											<topPen lineWidth="0.5"/>
											<leftPen lineWidth="0.5"/>
											<bottomPen lineWidth="0.5"/>
											<rightPen lineWidth="0.5"/>
										</box>
										<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression><![CDATA[$R{report.column.bookedRoomRevenue} + " " + $R{common.STLY}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:columnFooter height="0" rowSpan="1"/>
								<jr:detailCell style="table_TD" height="30" rowSpan="1">
									<textField pattern="###0.00" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF" uuid="c589ed59-0849-46b5-8cb3-0aecf6b3b9f9"/>
										<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
											<font isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" />
											<paragraph lineSpacing="Single"/>
										</textElement>
										<textFieldExpression><![CDATA[$F{onBooks_Revenue_stly} == null ? 0 : $F{onBooks_Revenue_stly}]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
						<jr:column width="90" uuid="30a38064-2be2-4f73-8bb7-94b969075158">
								<printWhenExpression><![CDATA[new Boolean($P{IsRev_ST2Y_RT})]]></printWhenExpression>
								<jr:tableHeader height="0" rowSpan="1"/>
								<jr:tableFooter height="0" rowSpan="1"/>
								<jr:columnHeader style="table_CH" height="50" rowSpan="1">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" mode="Opaque" x="0" y="0" width="90" height="50" backcolor="#BFE1FF" uuid="cff60c83-21a2-40e7-b2ff-bdebd33a104b"/>
										<box>
											<topPen lineWidth="0.5"/>
											<leftPen lineWidth="0.5"/>
											<bottomPen lineWidth="0.5"/>
											<rightPen lineWidth="0.5"/>
										</box>
										<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression><![CDATA[$R{report.column.bookedRoomRevenue} + " " + $R{common.ST2Y}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:columnFooter height="0" rowSpan="1"/>
								<jr:detailCell style="table_TD" height="30" rowSpan="1">
									<textField pattern="###0.00" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF" uuid="c589ed59-0849-46b5-8cb3-0aecf6b3b9f9"/>
										<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
											<font isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" />
											<paragraph lineSpacing="Single"/>
										</textElement>
										<textFieldExpression><![CDATA[$F{onBooks_Revenue_st2y} == null ? 0 : $F{onBooks_Revenue_st2y}]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
						<jr:column width="90" uuid="30a38064-2be2-4f73-8bb7-94b969075158">
								<printWhenExpression><![CDATA[new Boolean($P{IsRev_ST19_RT})]]></printWhenExpression>
								<jr:tableHeader height="0" rowSpan="1"/>
								<jr:tableFooter height="0" rowSpan="1"/>
								<jr:columnHeader style="table_CH" height="50" rowSpan="1">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" mode="Opaque" x="0" y="0" width="90" height="50" backcolor="#BFE1FF" uuid="cff60c83-21a2-40e7-b2ff-bdebd33a104b"/>
										<box>
											<topPen lineWidth="0.5"/>
											<leftPen lineWidth="0.5"/>
											<bottomPen lineWidth="0.5"/>
											<rightPen lineWidth="0.5"/>
										</box>
										<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression><![CDATA[$R{report.column.bookedRoomRevenue} + " " + $R{common.ST19}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:columnFooter height="0" rowSpan="1"/>
								<jr:detailCell style="table_TD" height="30" rowSpan="1">
									<textField pattern="###0.00" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF" uuid="c589ed59-0849-46b5-8cb3-0aecf6b3b9f9"/>
										<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
											<font isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" />
											<paragraph lineSpacing="Single"/>
										</textElement>
										<textFieldExpression><![CDATA[$F{onBooks_Revenue_st19} == null ? 0 : $F{onBooks_Revenue_st19}]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
						</jr:columnGroup>
						<jr:columnGroup width="180" uuid="b154d76c-8a65-4493-9cd1-1f6d8795348e">
							<printWhenExpression><![CDATA[new Boolean($P{IsRev})]]></printWhenExpression>
							<jr:column width="90" uuid="23c725c6-e011-4378-abc0-d2749e434ffa">
								<jr:columnHeader style="table_CH" height="50" rowSpan="1">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" mode="Opaque" x="0" y="0" width="90" height="50" backcolor="#BFE1FF" uuid="ce5ed45a-34df-4978-a993-5c80cf018105"/>
										<box>
											<topPen lineWidth="0.5"/>
											<leftPen lineWidth="0.5"/>
											<bottomPen lineWidth="0.5"/>
											<rightPen lineWidth="0.5"/>
										</box>
										<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression><![CDATA[$R{report.column.forecastedRoomRevenue} + " " + $R{common.thisYear}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:detailCell style="table_TD" height="30" rowSpan="1">
									<textField isStretchWithOverflow="true" pattern="###0.00" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30" uuid="013ce951-2758-429e-b065-3f5d042aa929"/>
										<textElement textAlignment="Center" verticalAlignment="Middle"/>
										<textFieldExpression><![CDATA[$F{Room_Revenue_ty} == null ? 0 : $F{Room_Revenue_ty}]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
							<jr:column width="90" uuid="9769417d-0fb6-41e8-b65e-6fcbd5bf4458">
								<printWhenExpression><![CDATA[$P{param_IsShowLastYearDataChecked}]]></printWhenExpression>
								<jr:tableHeader height="0" rowSpan="1"/>
								<jr:tableFooter height="0" rowSpan="1"/>
								<jr:columnHeader style="table_CH" height="50" rowSpan="1">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" mode="Opaque" x="0" y="0" width="90" height="50" backcolor="#BFE1FF" uuid="143289f2-9eb2-4b58-bef3-ee73039657dc"/>
										<box>
											<topPen lineWidth="0.5"/>
											<leftPen lineWidth="0.5"/>
											<bottomPen lineWidth="0.5"/>
											<rightPen lineWidth="0.5"/>
										</box>
										<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression><![CDATA[$R{report.column.forecastedRoomRevenue} + " " + $R{common.lastYearActual}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:columnFooter height="0" rowSpan="1"/>
								<jr:detailCell style="table_TD" height="30" rowSpan="1">
									<textField isStretchWithOverflow="true" pattern="###0.00" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30" uuid="e6f1fad5-3eaa-4238-8742-0d835f50ff88"/>
										<textElement textAlignment="Center" verticalAlignment="Middle"/>
										<textFieldExpression><![CDATA[$F{Room_Revenue_ly} == null ? 0 : $F{Room_Revenue_ly}]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
						</jr:columnGroup>
						<jr:columnGroup width="180" uuid="88db6a50-7bdc-4200-a592-6ebde6150b67">
							<printWhenExpression><![CDATA[new Boolean($P{IsOvbk})]]></printWhenExpression>
							<jr:column width="90" uuid="65ec306d-caf6-46bc-921f-dfe46e304a93">
								<jr:columnHeader style="table_CH" height="50" rowSpan="1">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" mode="Opaque" x="0" y="0" width="90" height="50" backcolor="#BFE1FF" uuid="be4c5fe8-ec96-4918-af74-d80b93acb1cc"/>
										<box>
											<topPen lineWidth="0.5"/>
											<leftPen lineWidth="0.5"/>
											<bottomPen lineWidth="0.5"/>
											<rightPen lineWidth="0.5"/>
										</box>
										<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression><![CDATA[$R{common.overbooking} + " " + $R{common.thisYear}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:detailCell style="table_TD" height="30" rowSpan="1">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30" uuid="0ae01c5d-c3bf-4426-92af-7e8dcfeb00ef"/>
										<textElement textAlignment="Center" verticalAlignment="Middle"/>
										<textFieldExpression><![CDATA[$F{Overbooking_Decision_ty}]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
							<jr:column width="90" uuid="3f0656ee-edcb-4bdb-89cd-2868d7cfbbd4">
								<printWhenExpression><![CDATA[$P{param_IsShowLastYearDataChecked}]]></printWhenExpression>
								<jr:tableHeader height="0" rowSpan="1"/>
								<jr:tableFooter height="0" rowSpan="1"/>
								<jr:columnHeader style="table_CH" height="50" rowSpan="1">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" mode="Opaque" x="0" y="0" width="90" height="50" backcolor="#BFE1FF" uuid="e8e8414e-cf1c-415f-bc1c-bfe5855fc9b2"/>
										<box>
											<topPen lineWidth="0.5"/>
											<leftPen lineWidth="0.5"/>
											<bottomPen lineWidth="0.5"/>
											<rightPen lineWidth="0.5"/>
										</box>
										<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression><![CDATA[$R{common.overbooking} + " " + $R{common.lastYearActual}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:columnFooter height="0" rowSpan="1"/>
								<jr:detailCell style="table_TD" height="30" rowSpan="1">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30" uuid="2f29107f-e1a7-4390-9aad-16c59fc8b573"/>
										<textElement textAlignment="Center" verticalAlignment="Middle"/>
										<textFieldExpression><![CDATA[$F{Overbooking_Decision_ly}]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
						</jr:columnGroup>
						<jr:columnGroup width="360" uuid="ed3edd21-81c4-4db8-afff-bbeb2071008c">
							<printWhenExpression><![CDATA[new Boolean($P{IsRevPar})]]></printWhenExpression>
							<jr:column width="90" uuid="2e216b37-dcec-4620-ad99-bf38defea51d">
								<jr:columnHeader style="table_CH" height="50" rowSpan="1">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" mode="Opaque" x="0" y="0" width="90" height="50" backcolor="#BFE1FF" uuid="a18caf2e-ab50-4f77-8309-bbf1f7227e09"/>
										<box>
											<topPen lineWidth="0.5"/>
											<leftPen lineWidth="0.5"/>
											<bottomPen lineWidth="0.5"/>
											<rightPen lineWidth="0.5"/>
										</box>
										<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression><![CDATA[$R{report.column.bookedRevPar} + " " + $R{common.thisYear}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:detailCell style="table_TD" height="30" rowSpan="1">
									<textField pattern="###0.00" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF" uuid="4f229156-9f34-46e7-aed1-3687fe69ce34"/>
										<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
											<font size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" />
											<paragraph lineSpacing="Single"/>
										</textElement>
										<textFieldExpression><![CDATA[$F{onBooks_REVPAR_ty} == null ? 0 : $F{onBooks_REVPAR_ty}]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
							<jr:column width="90" uuid="23606167-1f40-4243-8b0e-399ee63cd23b">
								<printWhenExpression><![CDATA[$P{param_IsShowLastYearDataChecked}]]></printWhenExpression>
								<jr:tableHeader height="0" rowSpan="1"/>
								<jr:tableFooter height="0" rowSpan="1"/>
								<jr:columnHeader style="table_CH" height="50" rowSpan="1">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" mode="Opaque" x="0" y="0" width="90" height="50" backcolor="#BFE1FF" uuid="12bb28da-9ea3-4c22-a51f-2fb5348725b6"/>
										<box>
											<topPen lineWidth="0.5"/>
											<leftPen lineWidth="0.5"/>
											<bottomPen lineWidth="0.5"/>
											<rightPen lineWidth="0.5"/>
										</box>
										<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression><![CDATA[$R{report.column.bookedRevPar} + " " + $R{common.lastYearActual}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:columnFooter height="0" rowSpan="1"/>
								<jr:detailCell style="table_TD" height="30" rowSpan="1">
									<textField pattern="###0.00" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF" uuid="9cfe07e2-7b2f-463b-a544-ab86a850811f"/>
										<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
											<font isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" />
											<paragraph lineSpacing="Single"/>
										</textElement>
										<textFieldExpression><![CDATA[$F{onBooks_REVPAR_ly} == null ? 0 : $F{onBooks_REVPAR_ly}]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
						    <jr:column width="90" uuid="23606167-1f40-4243-8b0e-399ee63cd23b">
								<printWhenExpression><![CDATA[$P{param_IsShowLast2YearsDataChecked}]]></printWhenExpression>
								<jr:tableHeader height="0" rowSpan="1"/>
								<jr:tableFooter height="0" rowSpan="1"/>
								<jr:columnHeader style="table_CH" height="50" rowSpan="1">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" mode="Opaque" x="0" y="0" width="90" height="50" backcolor="#BFE1FF" uuid="12bb28da-9ea3-4c22-a51f-2fb5348725b6"/>
										<box>
											<topPen lineWidth="0.5"/>
											<leftPen lineWidth="0.5"/>
											<bottomPen lineWidth="0.5"/>
											<rightPen lineWidth="0.5"/>
										</box>
										<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression><![CDATA[$R{report.column.bookedRevPar} + " " + $R{common.last2YearsActual}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:columnFooter height="0" rowSpan="1"/>
								<jr:detailCell style="table_TD" height="30" rowSpan="1">
									<textField pattern="###0.00" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF" uuid="9cfe07e2-7b2f-463b-a544-ab86a850811f"/>
										<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
											<font isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" />
											<paragraph lineSpacing="Single"/>
										</textElement>
										<textFieldExpression><![CDATA[$F{onBooks_REVPAR_l2y} == null ? 0 : $F{onBooks_REVPAR_l2y}]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
						    <jr:column width="90" uuid="23606167-1f40-4243-8b0e-399ee63cd23b">
								<printWhenExpression><![CDATA[$P{param_IsShowYear2019DataChecked}]]></printWhenExpression>
								<jr:tableHeader height="0" rowSpan="1"/>
								<jr:tableFooter height="0" rowSpan="1"/>
								<jr:columnHeader style="table_CH" height="50" rowSpan="1">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" mode="Opaque" x="0" y="0" width="90" height="50" backcolor="#BFE1FF" uuid="12bb28da-9ea3-4c22-a51f-2fb5348725b6"/>
										<box>
											<topPen lineWidth="0.5"/>
											<leftPen lineWidth="0.5"/>
											<bottomPen lineWidth="0.5"/>
											<rightPen lineWidth="0.5"/>
										</box>
										<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression><![CDATA[$R{report.column.bookedRevPar} + " " + $R{common.year2019Actual}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:columnFooter height="0" rowSpan="1"/>
								<jr:detailCell style="table_TD" height="30" rowSpan="1">
									<textField pattern="###0.00" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF" uuid="9cfe07e2-7b2f-463b-a544-ab86a850811f"/>
										<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
											<font isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" />
											<paragraph lineSpacing="Single"/>
										</textElement>
										<textFieldExpression><![CDATA[$F{onBooks_REVPAR_y2019} == null ? 0 : $F{onBooks_REVPAR_y2019}]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
						</jr:columnGroup>
						<jr:columnGroup width="180" uuid="d696d797-a71a-4e74-99b6-1648819d9039">
							<printWhenExpression><![CDATA[new Boolean($P{IsRevPar})]]></printWhenExpression>
							<jr:column width="90" uuid="1e08fca9-d528-4477-8147-4016dee0ec43">
								<jr:columnHeader style="table_CH" height="50" rowSpan="1">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" mode="Opaque" x="0" y="0" width="90" height="50" backcolor="#BFE1FF" uuid="f033c910-40b7-44d1-a2e1-ead265f9b49c"/>
										<box>
											<topPen lineWidth="0.5"/>
											<leftPen lineWidth="0.5"/>
											<bottomPen lineWidth="0.5"/>
											<rightPen lineWidth="0.5"/>
										</box>
										<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression><![CDATA[$R{report.column.forecastedRevPar} + " " + $R{common.thisYear}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:detailCell style="table_TD" height="30" rowSpan="1">
									<textField isStretchWithOverflow="true" pattern="###0.00" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30" uuid="2d0abf4a-278e-4929-9816-7a7162c72074"/>
										<textElement textAlignment="Center" verticalAlignment="Middle"/>
										<textFieldExpression><![CDATA[$F{REVPAR_ty} == null ? 0 : $F{REVPAR_ty}]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
							<jr:column width="90" uuid="b1f1edc7-4dcd-49f8-adf8-80cc95732342">
								<printWhenExpression><![CDATA[$P{param_IsShowLastYearDataChecked}]]></printWhenExpression>
								<jr:tableHeader height="0" rowSpan="1"/>
								<jr:tableFooter height="0" rowSpan="1"/>
								<jr:columnHeader style="table_CH" height="50" rowSpan="1">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" mode="Opaque" x="0" y="0" width="90" height="50" backcolor="#BFE1FF" uuid="6438b841-32f0-48b9-8f95-fe6b7d94b86d"/>
										<box>
											<topPen lineWidth="0.5"/>
											<leftPen lineWidth="0.5"/>
											<bottomPen lineWidth="0.5"/>
											<rightPen lineWidth="0.5"/>
										</box>
										<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression><![CDATA[$R{report.column.forecastedRevPar} + " " + $R{common.lastYearActual}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:columnFooter height="0" rowSpan="1"/>
								<jr:detailCell style="table_TD" height="30" rowSpan="1">
									<textField isStretchWithOverflow="true" pattern="###0.00" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30" uuid="540d360c-bec8-4296-acdd-a87235ae933b"/>
										<textElement textAlignment="Center" verticalAlignment="Middle"/>
										<textFieldExpression><![CDATA[$F{REVPAR_ly} == null ? 0 : $F{REVPAR_ly}]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
						</jr:columnGroup>
						<jr:columnGroup width="360" uuid="057b768d-4659-4b7e-a649-e2d27ec28f54">
							<printWhenExpression><![CDATA[new Boolean($P{IsADR})]]></printWhenExpression>
							<jr:column width="90" uuid="72e8d56e-1845-4afb-89b8-19e6f45f8587">
								<jr:columnHeader style="table_CH" height="50" rowSpan="1">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" mode="Opaque" x="0" y="0" width="90" height="50" backcolor="#BFE1FF" uuid="f4e26c7d-2654-4136-845b-f6d41e691585"/>
										<box>
											<topPen lineWidth="0.5"/>
											<leftPen lineWidth="0.5"/>
											<bottomPen lineWidth="0.5"/>
											<rightPen lineWidth="0.5"/>
										</box>
										<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression><![CDATA[$R{report.column.bookedAdr} + " " + $R{common.thisYear}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:detailCell style="table_TD" height="30" rowSpan="1">
									<textField pattern="###0.00" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF" uuid="c482ceee-4d43-47d5-b9e4-57c475cfb02d"/>
										<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
											<font size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" />
											<paragraph lineSpacing="Single"/>
										</textElement>
										<textFieldExpression><![CDATA[$F{onBooks_ADR_ty} == null ? 0 : $F{onBooks_ADR_ty}]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
							<jr:column width="90" uuid="5dc74d1e-0340-4653-8c79-e51a1427667e">
								<printWhenExpression><![CDATA[$P{param_IsShowLastYearDataChecked}]]></printWhenExpression>
								<jr:tableHeader height="0" rowSpan="1"/>
								<jr:tableFooter height="0" rowSpan="1"/>
								<jr:columnHeader style="table_CH" height="50" rowSpan="1">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" mode="Opaque" x="0" y="0" width="90" height="50" backcolor="#BFE1FF" uuid="f4e97067-995c-4636-836a-8b33fd2c801e"/>
										<box>
											<topPen lineWidth="0.5"/>
											<leftPen lineWidth="0.5"/>
											<bottomPen lineWidth="0.5"/>
											<rightPen lineWidth="0.5"/>
										</box>
										<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression><![CDATA[$R{report.column.bookedAdr} + " " + $R{common.lastYearActual}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:columnFooter height="0" rowSpan="1"/>
								<jr:detailCell style="table_TD" height="30" rowSpan="1">
									<textField pattern="###0.00" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF" uuid="2b6d12a3-7645-43b2-a4af-6c9f16b3f18b"/>
										<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
											<font isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" />
											<paragraph lineSpacing="Single"/>
										</textElement>
										<textFieldExpression><![CDATA[$F{onBooks_ADR_ly} == null ? 0 : $F{onBooks_ADR_ly}]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
						    <jr:column width="90" uuid="5dc74d1e-0340-4653-8c79-e51a1427667e">
								<printWhenExpression><![CDATA[$P{param_IsShowLast2YearsDataChecked}]]></printWhenExpression>
								<jr:tableHeader height="0" rowSpan="1"/>
								<jr:tableFooter height="0" rowSpan="1"/>
								<jr:columnHeader style="table_CH" height="50" rowSpan="1">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" mode="Opaque" x="0" y="0" width="90" height="50" backcolor="#BFE1FF" uuid="f4e97067-995c-4636-836a-8b33fd2c801e"/>
										<box>
											<topPen lineWidth="0.5"/>
											<leftPen lineWidth="0.5"/>
											<bottomPen lineWidth="0.5"/>
											<rightPen lineWidth="0.5"/>
										</box>
										<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression><![CDATA[$R{report.column.bookedAdr} + " " + $R{common.last2YearsActual}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:columnFooter height="0" rowSpan="1"/>
								<jr:detailCell style="table_TD" height="30" rowSpan="1">
									<textField pattern="###0.00" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF" uuid="2b6d12a3-7645-43b2-a4af-6c9f16b3f18b"/>
										<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
											<font isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" />
											<paragraph lineSpacing="Single"/>
										</textElement>
										<textFieldExpression><![CDATA[$F{onBooks_ADR_l2y} == null ? 0 : $F{onBooks_ADR_l2y}]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
						    <jr:column width="90" uuid="5dc74d1e-0340-4653-8c79-e51a1427667e">
								<printWhenExpression><![CDATA[$P{param_IsShowYear2019DataChecked}]]></printWhenExpression>
								<jr:tableHeader height="0" rowSpan="1"/>
								<jr:tableFooter height="0" rowSpan="1"/>
								<jr:columnHeader style="table_CH" height="50" rowSpan="1">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" mode="Opaque" x="0" y="0" width="90" height="50" backcolor="#BFE1FF" uuid="f4e97067-995c-4636-836a-8b33fd2c801e"/>
										<box>
											<topPen lineWidth="0.5"/>
											<leftPen lineWidth="0.5"/>
											<bottomPen lineWidth="0.5"/>
											<rightPen lineWidth="0.5"/>
										</box>
										<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression><![CDATA[$R{report.column.bookedAdr} + " " + $R{common.year2019Actual}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:columnFooter height="0" rowSpan="1"/>
								<jr:detailCell style="table_TD" height="30" rowSpan="1">
									<textField pattern="###0.00" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF" uuid="2b6d12a3-7645-43b2-a4af-6c9f16b3f18b"/>
										<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
											<font isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" />
											<paragraph lineSpacing="Single"/>
										</textElement>
										<textFieldExpression><![CDATA[$F{onBooks_ADR_y2019} == null ? 0 : $F{onBooks_ADR_y2019}]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
						</jr:columnGroup>
						<jr:columnGroup width="180" uuid="0214cf6b-0c4a-4905-ace2-ef0141d2cdd3">
							<printWhenExpression><![CDATA[new Boolean($P{IsADR})]]></printWhenExpression>
							<jr:column width="90" uuid="df317045-0f67-4675-8620-ad621b5a8534">
								<jr:columnHeader style="table_CH" height="50" rowSpan="1">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" mode="Opaque" x="0" y="0" width="90" height="50" backcolor="#BFE1FF" uuid="13798c3a-5d4f-46f3-9b23-246d9b03433b"/>
										<box>
											<topPen lineWidth="0.5"/>
											<leftPen lineWidth="0.5"/>
											<bottomPen lineWidth="0.5"/>
											<rightPen lineWidth="0.5"/>
										</box>
										<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression><![CDATA[$R{report.column.forecastedAdr} + " " + $R{common.thisYear}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:detailCell style="table_TD" height="30" rowSpan="1">
									<textField isStretchWithOverflow="true" pattern="###0.00" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30" uuid="f0fefffe-1dda-4d8a-9cf6-b2deeab86024"/>
										<textElement textAlignment="Center" verticalAlignment="Middle"/>
										<textFieldExpression><![CDATA[$F{ADR_ty} == null ? 0 : $F{ADR_ty}]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
							<jr:column width="90" uuid="16386428-a71f-4bb9-8db4-a67f4dbd56df">
								<printWhenExpression><![CDATA[$P{param_IsShowLastYearDataChecked}]]></printWhenExpression>
								<jr:tableHeader height="0" rowSpan="1"/>
								<jr:tableFooter height="0" rowSpan="1"/>
								<jr:columnHeader style="table_CH" height="50" rowSpan="1">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" mode="Opaque" x="0" y="0" width="90" height="50" backcolor="#BFE1FF" uuid="7d6cb100-7bfe-4d0e-8f23-f5d43b6fcc08"/>
										<box>
											<topPen lineWidth="0.5"/>
											<leftPen lineWidth="0.5"/>
											<bottomPen lineWidth="0.5"/>
											<rightPen lineWidth="0.5"/>
										</box>
										<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression><![CDATA[$R{report.column.forecastedAdr} + " " + $R{common.lastYearActual}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:columnFooter height="0" rowSpan="1"/>
								<jr:detailCell style="table_TD" height="30" rowSpan="1">
									<textField isStretchWithOverflow="true" pattern="###0.00" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30" uuid="b3600660-57bb-4f6d-9b21-7915c292e9de"/>
										<textElement textAlignment="Center" verticalAlignment="Middle"/>
										<textFieldExpression><![CDATA[$F{ADR_ly} == null ? 0 : $F{ADR_ly}]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
						</jr:columnGroup>
						<jr:columnGroup width="180" uuid="26e5a03e-4daf-4dba-b502-023f48619949">
							<printWhenExpression><![CDATA[new Boolean($P{IsLRV})]]></printWhenExpression>
							<jr:column width="90" uuid="754bac46-e5d2-428c-a4c0-606450764ada">
								<jr:tableHeader height="0" rowSpan="1"/>
								<jr:tableFooter height="0" rowSpan="1"/>
								<jr:columnHeader style="table_CH" height="50" rowSpan="1">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" mode="Opaque" x="0" y="0" width="90" height="50" backcolor="#BFE1FF" uuid="34e824f3-2e98-44d5-a33b-8a7f158b6fa8"/>
										<box>
											<topPen lineWidth="0.5"/>
											<leftPen lineWidth="0.5"/>
											<bottomPen lineWidth="0.5"/>
											<rightPen lineWidth="0.5"/>
										</box>
										<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression><![CDATA[$R{roomClass} + " " + $R{report.column.lastRoomValue} + " " + $R{common.thisYear}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:columnFooter height="0" rowSpan="1"/>
								<jr:detailCell style="table_TD" height="30" rowSpan="1">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30" uuid="e9c0f939-8500-4635-ade1-4bbd7ac66829"/>
										<textElement textAlignment="Center" verticalAlignment="Middle"/>
										<textFieldExpression><![CDATA[$F{lrvRC_ty}]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
							<jr:column width="90" uuid="4c54d2d3-e471-4026-8b5c-39ef5617fa27">
								<jr:tableHeader height="0" rowSpan="1"/>
								<jr:tableFooter height="0" rowSpan="1"/>
								<jr:columnHeader style="table_CH" height="50" rowSpan="1">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" mode="Opaque" x="0" y="0" width="90" height="50" backcolor="#BFE1FF" uuid="255ee66f-9ddc-4522-b92b-5df626fee03a"/>
										<box>
											<topPen lineWidth="0.5"/>
											<leftPen lineWidth="0.5"/>
											<bottomPen lineWidth="0.5"/>
											<rightPen lineWidth="0.5"/>
										</box>
										<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression><![CDATA[$R{room.type} + " " + $R{report.column.lastRoomValue} + " " + $R{common.thisYear}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:columnFooter height="0" rowSpan="1"/>
								<jr:detailCell style="table_TD" height="30" rowSpan="1">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30" uuid="34a8f5b5-03b6-48bc-a657-9279f7d5859e"/>
										<textElement textAlignment="Center" verticalAlignment="Middle"/>
										<textFieldExpression><![CDATA[$F{lrvRT_ty}]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
						</jr:columnGroup>
						<jr:columnGroup width="180" uuid="26e5a03e-4daf-4dba-b502-023f48619999">
							<printWhenExpression><![CDATA[new Boolean($P{Include_CloseLV0})]]></printWhenExpression>
							<jr:column width="90" uuid="4c05d479-5c9f-4894-ac88-ac4247421d53">
								<jr:columnHeader style="table_CH" height="50" rowSpan="1">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" mode="Opaque" x="0" y="0" width="90" height="50" backcolor="#BFE1FF" uuid="20f5cb5e-1c33-49d3-9321-0bd80fe08e66"/>
										<box>
											<topPen lineWidth="0.5"/>
											<leftPen lineWidth="0.5"/>
											<bottomPen lineWidth="0.5"/>
											<rightPen lineWidth="0.5"/>
										</box>
										<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression><![CDATA[$R{closelv0}+ " " + $R{common.thisYear}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:detailCell style="table_TD" height="30" rowSpan="1">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30" uuid="19ba6347-41d4-4907-96ae-ff136939c008"/>
										<textElement textAlignment="Center" verticalAlignment="Middle"/>
										<textFieldExpression><![CDATA[$F{CloseLV0_Pattern_ty}]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
							<jr:column width="90" uuid="4c05d479-5c9f-4894-ac88-ac4267421c53">
							    <printWhenExpression><![CDATA[$P{param_IsShowLastYearDataChecked}]]></printWhenExpression>
								<jr:columnHeader style="table_CH" height="50" rowSpan="1">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" mode="Opaque" x="0" y="0" width="90" height="50" backcolor="#BFE1FF" uuid="20f5cb5e-1c33-49d3-9321-0bd80fe08e66"/>
										<box>
											<topPen lineWidth="0.5"/>
											<leftPen lineWidth="0.5"/>
											<bottomPen lineWidth="0.5"/>
											<rightPen lineWidth="0.5"/>
										</box>
										<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression><![CDATA[$R{closelv0}+ " " + $R{lastyear}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:detailCell style="table_TD" height="30" rowSpan="1">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30" uuid="19ba6347-41d4-4907-96ae-ff136939c008"/>
										<textElement textAlignment="Center" verticalAlignment="Middle"/>
										<textFieldExpression><![CDATA[$F{CloseLV0_Pattern_ly}]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
						</jr:columnGroup>
					</jr:columnGroup>
				</jr:table>
			</componentElement>
		</band>
	</detail>
	<summary>
		<band height="20">
			<textField>
				<reportElement x="0" y="0" width="180" height="20" forecolor="#FFFFFF" uuid="54730879-10c2-4807-af55-af6e0a446991">
					<property name="net.sf.jasperreports.export.xls.break.after.row" value="true"/>
					<propertyExpression name="net.sf.jasperreports.export.xls.sheet.name"><![CDATA[$R{room.type}]]></propertyExpression>
				</reportElement>
				<textFieldExpression><![CDATA["Dummy for excel - new worksheet"]]></textFieldExpression>
			</textField>
		</band>
	</summary>
</jasperReport>
