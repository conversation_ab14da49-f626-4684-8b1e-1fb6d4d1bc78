<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="Change_Special_Rate_Plan" pageWidth="1310" pageHeight="842" whenNoDataType="AllSectionsNoDetail" columnWidth="1290" leftMargin="10" rightMargin="10" topMargin="5" bottomMargin="5" isIgnorePagination="true">
    <property name="ireport.zoom" value="1.0"/>
    <property name="ireport.x" value="0"/>
    <property name="ireport.y" value="0"/>
    <property name="ireport.jasperserver.reportUnit" value="/public/Reports/changeReportAtSRP"/>
	<template>"jrxmls/customStyles.jrtx"</template>
    <style name="table">
        <box>
            <pen lineWidth="1.0" lineColor="#000000"/>
        </box>
    </style>
    <style name="table_TH" mode="Opaque" backcolor="#F0F8FF">
        <box>
            <pen lineWidth="0.5" lineColor="#000000"/>
        </box>
    </style>
    <style name="table_CH" mode="Opaque" backcolor="#BFE1FF">
        <box>
            <pen lineWidth="0.5" lineColor="#000000"/>
        </box>
    </style>
    <style name="table_TD" mode="Opaque" backcolor="#FFFFFF">
        <box>
            <pen lineWidth="0.5" lineColor="#000000"/>
        </box>
    </style>
    <style name="table 1">
        <box>
            <pen lineWidth="1.0" lineColor="#000000"/>
        </box>
    </style>
    <style name="table 1_TH" mode="Opaque" backcolor="#F0F8FF">
        <box>
            <pen lineWidth="0.5" lineColor="#000000"/>
        </box>
    </style>
    <style name="table 1_CH" mode="Opaque" backcolor="#BFE1FF">
        <box>
            <pen lineWidth="0.5" lineColor="#000000"/>
        </box>
    </style>
    <style name="table 1_TD" mode="Opaque" backcolor="#FFFFFF">
        <box>
            <pen lineWidth="0.5" lineColor="#000000"/>
        </box>
    </style>
    <style name="table 2">
        <box>
            <pen lineWidth="1.0" lineColor="#000000"/>
        </box>
    </style>
    <style name="table 2_TH" mode="Opaque" backcolor="#F0F8FF">
        <box>
            <pen lineWidth="0.5" lineColor="#000000"/>
        </box>
    </style>
    <style name="table 2_CH" mode="Opaque" backcolor="#BFE1FF">
        <box>
            <pen lineWidth="0.5" lineColor="#000000"/>
        </box>
    </style>
    <style name="table 2_TD" mode="Opaque" backcolor="#FFFFFF">
        <box>
            <pen lineWidth="0.5" lineColor="#000000"/>
        </box>
    </style>
    <style name="table 3">
        <box>
            <pen lineWidth="1.0" lineColor="#000000"/>
        </box>
    </style>
    <style name="table 3_TH" mode="Opaque" backcolor="#F0F8FF">
        <box>
            <pen lineWidth="0.5" lineColor="#000000"/>
        </box>
    </style>
    <style name="table 3_CH" mode="Opaque" backcolor="#BFE1FF">
        <box>
            <pen lineWidth="0.5" lineColor="#000000"/>
        </box>
    </style>
    <style name="table 3_TD" mode="Opaque" backcolor="#FFFFFF">
        <box>
            <pen lineWidth="0.5" lineColor="#000000"/>
        </box>
    </style>
    <subDataset name="ds_change_srp">
        <parameter name="param_EndDate" class="java.util.Date">
            <defaultValueExpression><![CDATA[]]></defaultValueExpression>
        </parameter>
        <parameter name="param_StartDate" class="java.util.Date">
            <defaultValueExpression><![CDATA[]]></defaultValueExpression>
        </parameter>
        <parameter name="param_Property_ID" class="java.lang.Integer"/>
        <parameter name="param_Business_StartDate" class="java.util.Date"/>
        <parameter name="param_BaseCurrency" class="java.lang.String">
            <defaultValueExpression><![CDATA["USD"]]></defaultValueExpression>
        </parameter>
        <parameter name="param_SRPNames" class="java.lang.String">
            <defaultValueExpression><![CDATA[]]></defaultValueExpression>
        </parameter>
        <parameter name="srpNamesList" class="java.util.List"/>
        <parameter name="srp1" class="java.lang.String" isForPrompting="false">
            <defaultValueExpression><![CDATA[(!$P{srpNamesList}.get(0).equals("-1"))?(String)$P{srpNamesList}.get(0):""]]></defaultValueExpression>
        </parameter>
        <parameter name="srp2" class="java.lang.String" isForPrompting="false">
            <defaultValueExpression><![CDATA[(!$P{srpNamesList}.get(1).equals("-1"))?(String)$P{srpNamesList}.get(1):""]]></defaultValueExpression>
        </parameter>
        <parameter name="srp3" class="java.lang.String" isForPrompting="false">
            <defaultValueExpression><![CDATA[(String)$P{srpNamesList}.get(2)]]></defaultValueExpression>
        </parameter>
        <parameter name="srp4" class="java.lang.String" isForPrompting="false">
            <defaultValueExpression><![CDATA[(String)$P{srpNamesList}.get(3)]]></defaultValueExpression>
        </parameter>
        <parameter name="srp5" class="java.lang.String" isForPrompting="false">
            <defaultValueExpression><![CDATA[(String)$P{srpNamesList}.get(4)]]></defaultValueExpression>
        </parameter>
        <parameter name="srp6" class="java.lang.String" isForPrompting="false">
            <defaultValueExpression><![CDATA[(String)$P{srpNamesList}.get(5)]]></defaultValueExpression>
        </parameter>
        <parameter name="srp7" class="java.lang.String" isForPrompting="false">
            <defaultValueExpression><![CDATA[(String)$P{srpNamesList}.get(6)]]></defaultValueExpression>
        </parameter>
        <parameter name="srp8" class="java.lang.String" isForPrompting="false">
            <defaultValueExpression><![CDATA[(String)$P{srpNamesList}.get(7)]]></defaultValueExpression>
        </parameter>
        <parameter name="srp9" class="java.lang.String" isForPrompting="false">
            <defaultValueExpression><![CDATA[(String)$P{srpNamesList}.get(8)]]></defaultValueExpression>
        </parameter>
        <parameter name="srp10" class="java.lang.String" isForPrompting="false">
            <defaultValueExpression><![CDATA[(String)$P{srpNamesList}.get(9)]]></defaultValueExpression>
        </parameter>
        <parameter name="srp11" class="java.lang.String" isForPrompting="false">
            <defaultValueExpression><![CDATA[(String)$P{srpNamesList}.get(10)]]></defaultValueExpression>
        </parameter>
        <parameter name="srp12" class="java.lang.String" isForPrompting="false">
            <defaultValueExpression><![CDATA[(String)$P{srpNamesList}.get(11)]]></defaultValueExpression>
        </parameter>
        <parameter name="srp13" class="java.lang.String" isForPrompting="false">
            <defaultValueExpression><![CDATA[(String)$P{srpNamesList}.get(12)]]></defaultValueExpression>
        </parameter>
        <parameter name="srp14" class="java.lang.String" isForPrompting="false">
            <defaultValueExpression><![CDATA[(String)$P{srpNamesList}.get(13)]]></defaultValueExpression>
        </parameter>
        <parameter name="srp15" class="java.lang.String" isForPrompting="false">
            <defaultValueExpression><![CDATA[(String)$P{srpNamesList}.get(14)]]></defaultValueExpression>
        </parameter>
        <parameter name="srp16" class="java.lang.String" isForPrompting="false">
            <defaultValueExpression><![CDATA[(String)$P{srpNamesList}.get(15)]]></defaultValueExpression>
        </parameter>
        <parameter name="srp17" class="java.lang.String" isForPrompting="false">
            <defaultValueExpression><![CDATA[(String)$P{srpNamesList}.get(16)]]></defaultValueExpression>
        </parameter>
        <parameter name="srp18" class="java.lang.String" isForPrompting="false">
            <defaultValueExpression><![CDATA[(String)$P{srpNamesList}.get(17)]]></defaultValueExpression>
        </parameter>
        <parameter name="srp19" class="java.lang.String" isForPrompting="false">
            <defaultValueExpression><![CDATA[(String)$P{srpNamesList}.get(18)]]></defaultValueExpression>
        </parameter>
        <parameter name="srp20" class="java.lang.String" isForPrompting="false">
            <defaultValueExpression><![CDATA[(String)$P{srpNamesList}.get(19)]]></defaultValueExpression>
        </parameter>
        <parameter name="srp21" class="java.lang.String" isForPrompting="false">
            <defaultValueExpression><![CDATA[(String)$P{srpNamesList}.get(20)]]></defaultValueExpression>
        </parameter>
        <parameter name="srp22" class="java.lang.String" isForPrompting="false">
            <defaultValueExpression><![CDATA[(String)$P{srpNamesList}.get(21)]]></defaultValueExpression>
        </parameter>
        <parameter name="srp23" class="java.lang.String" isForPrompting="false">
            <defaultValueExpression><![CDATA[(String)$P{srpNamesList}.get(22)]]></defaultValueExpression>
        </parameter>
        <parameter name="srp24" class="java.lang.String" isForPrompting="false">
            <defaultValueExpression><![CDATA[(String)$P{srpNamesList}.get(23)]]></defaultValueExpression>
        </parameter>
        <parameter name="srp25" class="java.lang.String" isForPrompting="false">
            <defaultValueExpression><![CDATA[(String)$P{srpNamesList}.get(24)]]></defaultValueExpression>
        </parameter>
        <parameter name="JNDI_NAME" class="java.lang.String"/>
        <parameter name="param_isRollingDate" class="java.lang.Integer">
            <defaultValueExpression><![CDATA[0]]></defaultValueExpression>
        </parameter>
        <parameter name="param_RollingBusinessStartDate" class="java.lang.String"/>
        <parameter name="param_Rolling_Start_Date" class="java.lang.String"/>
        <parameter name="param_Rolling_End_Date" class="java.lang.String"/>
		<parameter name="post_departure_revenue_adjustment_enabled" class="java.lang.Integer">
            <defaultValueExpression><![CDATA[0]]></defaultValueExpression>
        </parameter>
        <parameter name="PROPERTY_TIME_ZONE" class="java.lang.String" isForPrompting="false">
			<defaultValueExpression><![CDATA[""]]></defaultValueExpression>
		</parameter>
		<parameter name="userDateFormat" class="java.lang.String" isForPrompting="false">
            <defaultValueExpression><![CDATA[]]></defaultValueExpression>
        </parameter>
        <parameter name="dateFormatter" class="java.text.DateFormat" isForPrompting="false">
            <defaultValueExpression><![CDATA[]]></defaultValueExpression>
        </parameter>
        <parameter name="userLocale" class="java.lang.String" isForPrompting="false">
            <defaultValueExpression><![CDATA[]]></defaultValueExpression>
        </parameter>
        <queryString>
            <![CDATA[
                            EXECUTE dbo.usp_get_change_report_comparative_view_srp_by_reservation_night
                                    $P{param_Property_ID},$P{srp1},$P{srp2},$P{srp3},$P{srp4},$P{srp5},$P{srp6},$P{srp7},$P{srp8},$P{srp9},$P{srp10},$P{srp11},$P{srp12},$P{srp13},$P{srp14},
                                    $P{srp15},$P{srp16},$P{srp17},$P{srp18},$P{srp19},$P{srp20},$P{srp21},$P{srp22},$P{srp23},$P{srp24},$P{srp25},$P{param_Business_StartDate},$P{param_StartDate},
                                    $P{param_EndDate},$P{param_isRollingDate},$P{param_RollingBusinessStartDate},$P{param_Rolling_Start_Date},$P{param_Rolling_End_Date},
                                    $P{post_departure_revenue_adjustment_enabled}

            ]]>
        </queryString>
        <field name="occupancy_dt" class="java.util.Date"/>
        <field name="dow" class="java.lang.String"/>
        <field name="srp1_Rooms_onBooks" class="java.lang.Integer"/>
        <field name="srp1_Rooms_onBooks_diff" class="java.lang.Integer"/>
        <field name="srp2_Rooms_onBooks" class="java.lang.Integer"/>
        <field name="srp2_Rooms_onBooks_diff" class="java.lang.Integer"/>
        <field name="srp3_Rooms_onBooks" class="java.lang.Integer"/>
        <field name="srp3_Rooms_onBooks_diff" class="java.lang.Integer"/>
        <field name="srp4_Rooms_onBooks" class="java.lang.Integer"/>
        <field name="srp4_Rooms_onBooks_diff" class="java.lang.Integer"/>
        <field name="srp5_Rooms_onBooks" class="java.lang.Integer"/>
        <field name="srp5_Rooms_onBooks_diff" class="java.lang.Integer"/>
        <field name="srp6_Rooms_onBooks" class="java.lang.Integer"/>
        <field name="srp6_Rooms_onBooks_diff" class="java.lang.Integer"/>
        <field name="srp7_Rooms_onBooks" class="java.lang.Integer"/>
        <field name="srp7_Rooms_onBooks_diff" class="java.lang.Integer"/>
        <field name="srp8_Rooms_onBooks" class="java.lang.Integer"/>
        <field name="srp8_Rooms_onBooks_diff" class="java.lang.Integer"/>
        <field name="srp9_Rooms_onBooks" class="java.lang.Integer"/>
        <field name="srp9_Rooms_onBooks_diff" class="java.lang.Integer"/>
        <field name="srp10_Rooms_onBooks" class="java.lang.Integer"/>
        <field name="srp10_Rooms_onBooks_diff" class="java.lang.Integer"/>
        <field name="srp11_Rooms_onBooks" class="java.lang.Integer"/>
        <field name="srp11_Rooms_onBooks_diff" class="java.lang.Integer"/>
        <field name="srp12_Rooms_onBooks" class="java.lang.Integer"/>
        <field name="srp12_Rooms_onBooks_diff" class="java.lang.Integer"/>
        <field name="srp13_Rooms_onBooks" class="java.lang.Integer"/>
        <field name="srp13_Rooms_onBooks_diff" class="java.lang.Integer"/>
        <field name="srp14_Rooms_onBooks" class="java.lang.Integer"/>
        <field name="srp14_Rooms_onBooks_diff" class="java.lang.Integer"/>
        <field name="srp15_Rooms_onBooks" class="java.lang.Integer"/>
        <field name="srp15_Rooms_onBooks_diff" class="java.lang.Integer"/>
        <field name="srp16_Rooms_onBooks" class="java.lang.Integer"/>
        <field name="srp16_Rooms_onBooks_diff" class="java.lang.Integer"/>
        <field name="srp17_Rooms_onBooks" class="java.lang.Integer"/>
        <field name="srp17_Rooms_onBooks_diff" class="java.lang.Integer"/>
        <field name="srp18_Rooms_onBooks" class="java.lang.Integer"/>
        <field name="srp18_Rooms_onBooks_diff" class="java.lang.Integer"/>
        <field name="srp19_Rooms_onBooks" class="java.lang.Integer"/>
        <field name="srp19_Rooms_onBooks_diff" class="java.lang.Integer"/>
        <field name="srp20_Rooms_onBooks" class="java.lang.Integer"/>
        <field name="srp20_Rooms_onBooks_diff" class="java.lang.Integer"/>
        <field name="srp21_Rooms_onBooks" class="java.lang.Integer"/>
        <field name="srp21_Rooms_onBooks_diff" class="java.lang.Integer"/>
        <field name="srp22_Rooms_onBooks" class="java.lang.Integer"/>
        <field name="srp22_Rooms_onBooks_diff" class="java.lang.Integer"/>
        <field name="srp23_Rooms_onBooks" class="java.lang.Integer"/>
        <field name="srp23_Rooms_onBooks_diff" class="java.lang.Integer"/>
        <field name="srp24_Rooms_onBooks" class="java.lang.Integer"/>
        <field name="srp24_Rooms_onBooks_diff" class="java.lang.Integer"/>
        <field name="srp25_Rooms_onBooks" class="java.lang.Integer"/>
        <field name="srp25_Rooms_onBooks_diff" class="java.lang.Integer"/>
        <field name="srp1_Revenue" class="java.math.BigDecimal"/>
        <field name="srp1_Revenue_diff" class="java.math.BigDecimal"/>
        <field name="srp2_Revenue" class="java.math.BigDecimal"/>
        <field name="srp2_Revenue_diff" class="java.math.BigDecimal"/>
        <field name="srp3_Revenue" class="java.math.BigDecimal"/>
        <field name="srp3_Revenue_diff" class="java.math.BigDecimal"/>
        <field name="srp4_Revenue" class="java.math.BigDecimal"/>
        <field name="srp4_Revenue_diff" class="java.math.BigDecimal"/>
        <field name="srp5_Revenue" class="java.math.BigDecimal"/>
        <field name="srp5_Revenue_diff" class="java.math.BigDecimal"/>
        <field name="srp6_Revenue" class="java.math.BigDecimal"/>
        <field name="srp6_Revenue_diff" class="java.math.BigDecimal"/>
        <field name="srp7_Revenue" class="java.math.BigDecimal"/>
        <field name="srp7_Revenue_diff" class="java.math.BigDecimal"/>
        <field name="srp8_Revenue" class="java.math.BigDecimal"/>
        <field name="srp8_Revenue_diff" class="java.math.BigDecimal"/>
        <field name="srp9_Revenue" class="java.math.BigDecimal"/>
        <field name="srp9_Revenue_diff" class="java.math.BigDecimal"/>
        <field name="srp10_Revenue" class="java.math.BigDecimal"/>
        <field name="srp10_Revenue_diff" class="java.math.BigDecimal"/>
        <field name="srp11_Revenue" class="java.math.BigDecimal"/>
        <field name="srp11_Revenue_diff" class="java.math.BigDecimal"/>
        <field name="srp12_Revenue" class="java.math.BigDecimal"/>
        <field name="srp12_Revenue_diff" class="java.math.BigDecimal"/>
        <field name="srp13_Revenue" class="java.math.BigDecimal"/>
        <field name="srp13_Revenue_diff" class="java.math.BigDecimal"/>
        <field name="srp14_Revenue" class="java.math.BigDecimal"/>
        <field name="srp14_Revenue_diff" class="java.math.BigDecimal"/>
        <field name="srp15_Revenue" class="java.math.BigDecimal"/>
        <field name="srp15_Revenue_diff" class="java.math.BigDecimal"/>
        <field name="srp16_Revenue" class="java.math.BigDecimal"/>
        <field name="srp16_Revenue_diff" class="java.math.BigDecimal"/>
        <field name="srp17_Revenue" class="java.math.BigDecimal"/>
        <field name="srp17_Revenue_diff" class="java.math.BigDecimal"/>
        <field name="srp18_Revenue" class="java.math.BigDecimal"/>
        <field name="srp18_Revenue_diff" class="java.math.BigDecimal"/>
        <field name="srp19_Revenue" class="java.math.BigDecimal"/>
        <field name="srp19_Revenue_diff" class="java.math.BigDecimal"/>
        <field name="srp20_Revenue" class="java.math.BigDecimal"/>
        <field name="srp20_Revenue_diff" class="java.math.BigDecimal"/>
        <field name="srp21_Revenue" class="java.math.BigDecimal"/>
        <field name="srp21_Revenue_diff" class="java.math.BigDecimal"/>
        <field name="srp22_Revenue" class="java.math.BigDecimal"/>
        <field name="srp22_Revenue_diff" class="java.math.BigDecimal"/>
        <field name="srp23_Revenue" class="java.math.BigDecimal"/>
        <field name="srp23_Revenue_diff" class="java.math.BigDecimal"/>
        <field name="srp24_Revenue" class="java.math.BigDecimal"/>
        <field name="srp24_Revenue_diff" class="java.math.BigDecimal"/>
        <field name="srp25_Revenue" class="java.math.BigDecimal"/>
        <field name="srp25_Revenue_diff" class="java.math.BigDecimal"/>
        <field name="srp1_ADR" class="java.math.BigDecimal"/>
        <field name="srp1_ADR_diff" class="java.math.BigDecimal"/>
        <field name="srp2_ADR" class="java.math.BigDecimal"/>
        <field name="srp2_ADR_diff" class="java.math.BigDecimal"/>
        <field name="srp3_ADR" class="java.math.BigDecimal"/>
        <field name="srp3_ADR_diff" class="java.math.BigDecimal"/>
        <field name="srp4_ADR" class="java.math.BigDecimal"/>
        <field name="srp4_ADR_diff" class="java.math.BigDecimal"/>
        <field name="srp5_ADR" class="java.math.BigDecimal"/>
        <field name="srp5_ADR_diff" class="java.math.BigDecimal"/>
        <field name="srp6_ADR" class="java.math.BigDecimal"/>
        <field name="srp6_ADR_diff" class="java.math.BigDecimal"/>
        <field name="srp7_ADR" class="java.math.BigDecimal"/>
        <field name="srp7_ADR_diff" class="java.math.BigDecimal"/>
        <field name="srp8_ADR" class="java.math.BigDecimal"/>
        <field name="srp8_ADR_diff" class="java.math.BigDecimal"/>
        <field name="srp9_ADR" class="java.math.BigDecimal"/>
        <field name="srp9_ADR_diff" class="java.math.BigDecimal"/>
        <field name="srp10_ADR" class="java.math.BigDecimal"/>
        <field name="srp10_ADR_diff" class="java.math.BigDecimal"/>
        <field name="srp11_ADR" class="java.math.BigDecimal"/>
        <field name="srp11_ADR_diff" class="java.math.BigDecimal"/>
        <field name="srp12_ADR" class="java.math.BigDecimal"/>
        <field name="srp12_ADR_diff" class="java.math.BigDecimal"/>
        <field name="srp13_ADR" class="java.math.BigDecimal"/>
        <field name="srp13_ADR_diff" class="java.math.BigDecimal"/>
        <field name="srp14_ADR" class="java.math.BigDecimal"/>
        <field name="srp14_ADR_diff" class="java.math.BigDecimal"/>
        <field name="srp15_ADR" class="java.math.BigDecimal"/>
        <field name="srp15_ADR_diff" class="java.math.BigDecimal"/>
        <field name="srp16_ADR" class="java.math.BigDecimal"/>
        <field name="srp16_ADR_diff" class="java.math.BigDecimal"/>
        <field name="srp17_ADR" class="java.math.BigDecimal"/>
        <field name="srp17_ADR_diff" class="java.math.BigDecimal"/>
        <field name="srp18_ADR" class="java.math.BigDecimal"/>
        <field name="srp18_ADR_diff" class="java.math.BigDecimal"/>
        <field name="srp19_ADR" class="java.math.BigDecimal"/>
        <field name="srp19_ADR_diff" class="java.math.BigDecimal"/>
        <field name="srp20_ADR" class="java.math.BigDecimal"/>
        <field name="srp20_ADR_diff" class="java.math.BigDecimal"/>
        <field name="srp21_ADR" class="java.math.BigDecimal"/>
        <field name="srp21_ADR_diff" class="java.math.BigDecimal"/>
        <field name="srp22_ADR" class="java.math.BigDecimal"/>
        <field name="srp22_ADR_diff" class="java.math.BigDecimal"/>
        <field name="srp23_ADR" class="java.math.BigDecimal"/>
        <field name="srp23_ADR_diff" class="java.math.BigDecimal"/>
        <field name="srp24_ADR" class="java.math.BigDecimal"/>
        <field name="srp24_ADR_diff" class="java.math.BigDecimal"/>
        <field name="srp25_ADR" class="java.math.BigDecimal"/>
        <field name="srp25_ADR_diff" class="java.math.BigDecimal"/>
    </subDataset>
    <subDataset name="ExcelHeader">
        <parameter name="param_BaseCurrency" class="java.lang.String">
            <defaultValueExpression><![CDATA["USD"]]></defaultValueExpression>
        </parameter>
        <parameter name="param_AnalysisEndDate" class="java.util.Date">
            <defaultValueExpression><![CDATA[]]></defaultValueExpression>
        </parameter>
        <parameter name="param_AnalysisStartDate" class="java.util.Date">
            <defaultValueExpression><![CDATA[]]></defaultValueExpression>
        </parameter>
        <parameter name="param_Property_ID" class="java.lang.Integer">
            <defaultValueExpression><![CDATA[5]]></defaultValueExpression>
        </parameter>
        <parameter name="param_ComparisonStartDate" class="java.util.Date">
            <defaultValueExpression><![CDATA[]]></defaultValueExpression>
        </parameter>
        <parameter name="param_isRollingDate" class="java.lang.Integer">
            <defaultValueExpression><![CDATA[0]]></defaultValueExpression>
        </parameter>
        <parameter name="param_RollingAnalysisStartDate" class="java.lang.String"/>
        <parameter name="param_RollingAnalysisEndDate" class="java.lang.String">
            <parameterDescription><![CDATA[]]></parameterDescription>
        </parameter>
        <parameter name="param_RollingComparisionStartDate" class="java.lang.String"/>
        <parameter name="param_User_ID" class="java.lang.Integer"/>
        <parameter name="JNDI_NAME" class="java.lang.String"/>
        <parameter name="Jasper_custom_formatter" class="com.ideas.tetris.platform.reports.jasperreports.formatter.JasperCustomFormatter" isForPrompting="false">
            <defaultValueExpression><![CDATA[new com.ideas.tetris.platform.reports.jasperreports.formatter.JasperCustomFormatter()]]></defaultValueExpression>
        </parameter>
        <parameter name="PROPERTY_TIME_ZONE" class="java.lang.String" isForPrompting="false">
			<defaultValueExpression><![CDATA[""]]></defaultValueExpression>
		</parameter>
		<parameter name="userDateFormat" class="java.lang.String" isForPrompting="false">
            <defaultValueExpression><![CDATA[]]></defaultValueExpression>
        </parameter>
        <parameter name="dateFormatter" class="java.text.DateFormat" isForPrompting="false">
            <defaultValueExpression><![CDATA[]]></defaultValueExpression>
        </parameter>
        <parameter name="userLocale" class="java.lang.String" isForPrompting="false">
            <defaultValueExpression><![CDATA[]]></defaultValueExpression>
        </parameter>
        <queryString>
            <![CDATA[select * from dbo.ufn_get_filter_selection
(
$P{param_Property_ID},
$P{param_User_ID},
$P{param_BaseCurrency},
$P{param_isRollingDate},
'',
'',
$P{param_AnalysisStartDate},
$P{param_AnalysisEndDate},
'',
$P{param_ComparisonStartDate},
'',
'',
'',
'',
$P{param_RollingAnalysisStartDate},
$P{param_RollingAnalysisEndDate},
'',
$P{param_RollingComparisionStartDate},
'',
''
)]]>
        </queryString>
        <field name="property_name" class="java.lang.String"/>
        <field name="created_by" class="java.lang.String"/>
        <field name="genration_date" class="java.lang.String"/>
        <field name="start_date" class="java.lang.String"/>
        <field name="end_date" class="java.lang.String"/>
        <field name="analysis_start_date" class="java.util.Date"/>
        <field name="analysis_end_date" class="java.util.Date"/>
        <field name="analysis_business_dt" class="java.lang.String"/>
        <field name="comparision_start_date" class="java.util.Date"/>
        <field name="comparision_end_date" class="java.lang.String"/>
        <field name="comparision_business_dt" class="java.lang.String"/>
        <field name="param_BaseCurrency" class="java.lang.String"/>
    </subDataset>
    <parameter name="param_Property_ID" class="java.lang.Integer"/>
    <parameter name="param_StartDate" class="java.util.Date">
        <defaultValueExpression><![CDATA[]]></defaultValueExpression>
    </parameter>
    <parameter name="param_EndDate" class="java.util.Date">
        <defaultValueExpression><![CDATA[]]></defaultValueExpression>
    </parameter>
    <parameter name="param_Business_StartDate" class="java.util.Date"/>
    <parameter name="param_BaseCurrency" class="java.lang.String">
        <defaultValueExpression><![CDATA["USD"]]></defaultValueExpression>
    </parameter>
    <parameter name="param_SRPNames" class="java.lang.String">
        <defaultValueExpression><![CDATA[]]></defaultValueExpression>
    </parameter>
    <parameter name="srpNamesList" class="java.util.List" isForPrompting="false">
        <defaultValueExpression><![CDATA[($P{param_SRPNames}.length()>0)?Arrays.asList($P{param_SRPNames}.split( "," )):new ArrayList()]]></defaultValueExpression>
    </parameter>
    <parameter name="JNDI_NAME" class="java.lang.String"/>
    <parameter name="param_isRollingDate" class="java.lang.Integer">
        <defaultValueExpression><![CDATA[0]]></defaultValueExpression>
    </parameter>
    <parameter name="param_RollingBusinessStartDate" class="java.lang.String"/>
    <parameter name="param_Rolling_Start_Date" class="java.lang.String"/>
    <parameter name="param_Rolling_End_Date" class="java.lang.String"/>
	<parameter name="post_departure_revenue_adjustment_enabled" class="java.lang.Integer">
		<defaultValueExpression><![CDATA[0]]></defaultValueExpression>
	</parameter>
    <parameter name="param_User_ID" class="java.lang.Integer"/>
    <parameter name="Jasper_custom_formatter" class="com.ideas.tetris.platform.reports.jasperreports.formatter.JasperCustomFormatter" isForPrompting="false">
        <defaultValueExpression><![CDATA[]]></defaultValueExpression>
    </parameter>
    <parameter name="PROPERTY_TIME_ZONE" class="java.lang.String" isForPrompting="false">
			<defaultValueExpression><![CDATA[""]]></defaultValueExpression>
		</parameter>
		<parameter name="userDateFormat" class="java.lang.String" isForPrompting="false">
        <defaultValueExpression><![CDATA["yyyy-MM-dd"]]></defaultValueExpression>
    </parameter>
    <parameter name="dateFormatter" class="java.text.DateFormat" isForPrompting="false">
        <defaultValueExpression><![CDATA[$P{REPORT_FORMAT_FACTORY}.createDateFormat( $P{userDateFormat}, $P{REPORT_LOCALE}, null )]]></defaultValueExpression>
    </parameter>
    <parameter name="userLocale" class="java.lang.String" isForPrompting="false">
        <defaultValueExpression><![CDATA["en_US"]]></defaultValueExpression>
    </parameter>
    <parameter name="param_SheetForCriteria" class="java.lang.Boolean" isForPrompting="false">
        <defaultValueExpression><![CDATA[false]]></defaultValueExpression>
    </parameter>
    <queryString>
        <![CDATA[SELECT GETDATE() as date]]>
    </queryString>
    <field name="date" class="java.sql.Timestamp"/>
    <background>
        <band splitType="Stretch"/>
    </background>
    <title>
        <band height="20">
            <printWhenExpression><![CDATA[$P{IS_IGNORE_PAGINATION} && $P{param_SheetForCriteria}.toString().equalsIgnoreCase("true")?false:true]]></printWhenExpression>
            <textField>
                <reportElement x="0" y="0" width="1290" height="20">
                    <propertyExpression name="net.sf.jasperreports.export.xls.sheet.name"><![CDATA[$R{change.special.rate.plan}]]></propertyExpression>
                </reportElement>
                <textElement textAlignment="Left" verticalAlignment="Middle">
                    <font size="12" isBold="true"/>
                </textElement>
                <textFieldExpression class="java.lang.String"><![CDATA["   "+$R{change.report.at.special.rate.plan.level}+" "+$R{side.by.side.view}]]></textFieldExpression>
            </textField>
        </band>
    </title>
    <pageHeader>
        <band height="50">
            <printWhenExpression><![CDATA[($P{IS_IGNORE_PAGINATION} && new Boolean($P{param_SheetForCriteria})==false)?true:false]]></printWhenExpression>
            <componentElement>
                <reportElement key="table 3" style="table 3" stretchType="RelativeToBandHeight" x="0" y="0" width="360" height="50"/>
                <jr:table xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd">
                    <datasetRun subDataset="ExcelHeader">
                        <datasetParameter name="param_Property_ID">
                            <datasetParameterExpression><![CDATA[$P{param_Property_ID}]]></datasetParameterExpression>
                        </datasetParameter>
                        <datasetParameter name="param_User_ID">
                            <datasetParameterExpression><![CDATA[$P{param_User_ID}]]></datasetParameterExpression>
                        </datasetParameter>
                        <datasetParameter name="param_BaseCurrency">
                            <datasetParameterExpression><![CDATA[$P{param_BaseCurrency}]]></datasetParameterExpression>
                        </datasetParameter>
                        <datasetParameter name="JNDI_NAME">
                            <datasetParameterExpression><![CDATA[$P{JNDI_NAME}]]></datasetParameterExpression>
                        </datasetParameter>
                        <datasetParameter name="param_AnalysisStartDate">
                            <datasetParameterExpression><![CDATA[$P{param_StartDate}]]></datasetParameterExpression>
                        </datasetParameter>
                        <datasetParameter name="param_AnalysisEndDate">
                            <datasetParameterExpression><![CDATA[$P{param_EndDate}]]></datasetParameterExpression>
                        </datasetParameter>
                        <datasetParameter name="param_ComparisonStartDate">
                            <datasetParameterExpression><![CDATA[$P{param_Business_StartDate}]]></datasetParameterExpression>
                        </datasetParameter>
                        <datasetParameter name="param_isRollingDate">
                            <datasetParameterExpression><![CDATA[$P{param_isRollingDate}]]></datasetParameterExpression>
                        </datasetParameter>
                        <datasetParameter name="param_RollingAnalysisStartDate">
                            <datasetParameterExpression><![CDATA[$P{param_Rolling_Start_Date}]]></datasetParameterExpression>
                        </datasetParameter>
                        <datasetParameter name="param_RollingAnalysisEndDate">
                            <datasetParameterExpression><![CDATA[$P{param_Rolling_End_Date}]]></datasetParameterExpression>
                        </datasetParameter>
                        <datasetParameter name="param_RollingComparisionStartDate">
                            <datasetParameterExpression><![CDATA[$P{param_RollingBusinessStartDate}]]></datasetParameterExpression>
                        </datasetParameter>
                        <datasetParameter name="Jasper_custom_formatter">
                            <datasetParameterExpression><![CDATA[$P{Jasper_custom_formatter}]]></datasetParameterExpression>
                        </datasetParameter>
                        <datasetParameter name="PROPERTY_TIME_ZONE">
							<datasetParameterExpression><![CDATA[$P{PROPERTY_TIME_ZONE}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="userDateFormat">
                            <datasetParameterExpression><![CDATA[$P{userDateFormat}]]></datasetParameterExpression>
                        </datasetParameter>
                        <datasetParameter name="dateFormatter">
                            <datasetParameterExpression><![CDATA[$P{dateFormatter}]]></datasetParameterExpression>
                        </datasetParameter>
                        <datasetParameter name="userLocale">
                            <datasetParameterExpression><![CDATA[$P{userLocale}]]></datasetParameterExpression>
                        </datasetParameter>
                        <connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
                    </datasetRun>
                    <jr:columnGroup width="630">
                        <jr:tableHeader height="30" rowSpan="1">
                            <textField>
                                <reportElement x="0" y="0" width="630" height="30"/>
                                <textElement textAlignment="Center" verticalAlignment="Middle">
                                    <font size="11" isBold="true"/>
                                </textElement>
                                <textFieldExpression class="java.lang.String"><![CDATA[$R{report.ReportCriteria}]]></textFieldExpression>
                            </textField>
                        </jr:tableHeader>
                        <jr:column width="90">
                            <jr:tableHeader style="table 3_TH" height="0" rowSpan="1"/>
                            <jr:tableFooter style="table 3_TH" height="0" rowSpan="1"/>
                            <jr:columnHeader style="table 3_CH" height="30" rowSpan="1">
                                <textField>
                                    <reportElement x="0" y="0" width="90" height="30"/>
                                    <textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
                                        <font isBold="true"/>
                                    </textElement>
                                    <textFieldExpression class="java.lang.String"><![CDATA[$R{common.propertyName}]]></textFieldExpression>
                                </textField>
                            </jr:columnHeader>
                            <jr:detailCell style="table 3_TD" height="20" rowSpan="1">
                                <textField isStretchWithOverflow="true">
                                    <reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="20"/>
                                    <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                    <textFieldExpression class="java.lang.String"><![CDATA[$F{property_name}]]></textFieldExpression>
                                </textField>
                            </jr:detailCell>
                        </jr:column>
                        <jr:column width="90">
                            <jr:tableHeader style="table 3_TH" height="0" rowSpan="1"/>
                            <jr:tableFooter style="table 3_TH" height="0" rowSpan="1"/>
                            <jr:columnHeader style="table 3_CH" height="30" rowSpan="1">
                                <textField>
                                    <reportElement x="0" y="0" width="90" height="30"/>
                                    <textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
                                        <font isBold="true"/>
                                    </textElement>
                                    <textFieldExpression class="java.lang.String"><![CDATA[$R{analysisStartDateLabel}]]></textFieldExpression>
                                </textField>
                            </jr:columnHeader>
                            <jr:detailCell style="table 3_TD" height="20" rowSpan="1">
                                <textField isStretchWithOverflow="true">
                                    <reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="20"/>
                                    <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                    <textFieldExpression class="java.util.Date"><![CDATA[$F{analysis_start_date}]]></textFieldExpression>
                                        <patternExpression><![CDATA[$P{userDateFormat}]]></patternExpression>
                                </textField>
                            </jr:detailCell>
                        </jr:column>
                        <jr:column width="90">
                            <jr:tableHeader style="table 3_TH" height="0" rowSpan="1"/>
                            <jr:tableFooter style="table 3_TH" height="0" rowSpan="1"/>
                            <jr:columnHeader style="table 3_CH" height="30" rowSpan="1">
                                <textField>
                                    <reportElement x="0" y="0" width="90" height="30"/>
                                    <textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
                                        <font isBold="true"/>
                                    </textElement>
                                    <textFieldExpression class="java.lang.String"><![CDATA[$R{performanceComparisonReport.filter.label.analysisEndDate}]]></textFieldExpression>
                                </textField>
                            </jr:columnHeader>
                            <jr:detailCell style="table 3_TD" height="20" rowSpan="1">
                                <textField isStretchWithOverflow="true">
                                    <reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="20"/>
                                    <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                    <textFieldExpression class="java.util.Date"><![CDATA[$F{analysis_end_date}]]></textFieldExpression>
                                        <patternExpression><![CDATA[$P{userDateFormat}]]></patternExpression>
                                </textField>
                            </jr:detailCell>
                        </jr:column>
                        <jr:column width="90">
                            <jr:tableHeader style="table 3_TH" height="0" rowSpan="1"/>
                            <jr:tableFooter style="table 3_TH" height="0" rowSpan="1"/>
                            <jr:columnHeader style="table 3_CH" height="30" rowSpan="1">
                                <textField>
                                    <reportElement x="0" y="0" width="90" height="30"/>
                                    <textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
                                        <font isBold="true"/>
                                    </textElement>
                                    <textFieldExpression class="java.lang.String"><![CDATA[$R{activity.start.date}]]></textFieldExpression>
                                </textField>
                            </jr:columnHeader>
                            <jr:detailCell style="table 3_TD" height="20" rowSpan="1">
                                <textField isStretchWithOverflow="true">
                                    <reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="20"/>
                                    <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                    <textFieldExpression class="java.util.Date"><![CDATA[$F{comparision_start_date}]]></textFieldExpression>
                                        <patternExpression><![CDATA[$P{userDateFormat}]]></patternExpression>
                                </textField>
                            </jr:detailCell>
                        </jr:column>
                        <jr:column width="90">
                            <jr:tableHeader style="table 3_TH" height="0" rowSpan="1"/>
                            <jr:tableFooter style="table 3_TH" height="0" rowSpan="1"/>
                            <jr:columnHeader style="table 3_CH" height="30" rowSpan="1">
                                <textField>
                                    <reportElement x="0" y="0" width="90" height="30"/>
                                    <textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
                                        <font isBold="true"/>
                                    </textElement>
                                    <textFieldExpression class="java.lang.String"><![CDATA[$R{currency}]]></textFieldExpression>
                                </textField>
                            </jr:columnHeader>
                            <jr:detailCell style="table 3_TD" height="20" rowSpan="1">
                                <textField isStretchWithOverflow="true">
                                    <reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="20"/>
                                    <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                    <textFieldExpression class="java.lang.String"><![CDATA[$F{param_BaseCurrency}]]></textFieldExpression>
                                </textField>
                            </jr:detailCell>
                        </jr:column>
                        <jr:column width="90">
                            <jr:tableHeader style="table 3_TH" height="0" rowSpan="1"/>
                            <jr:tableFooter style="table 3_TH" height="0" rowSpan="1"/>
                            <jr:columnHeader style="table 3_CH" height="30" rowSpan="1">
                                <textField>
                                    <reportElement x="0" y="0" width="90" height="30"/>
                                    <textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
                                        <font isBold="true"/>
                                    </textElement>
                                    <textFieldExpression class="java.lang.String"><![CDATA[$R{createdBy}]]></textFieldExpression>
                                </textField>
                            </jr:columnHeader>
                            <jr:detailCell style="table 3_TD" height="20" rowSpan="1">
                                <textField isStretchWithOverflow="true">
                                    <reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="20"/>
                                    <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                    <textFieldExpression class="java.lang.String"><![CDATA[$F{created_by}]]></textFieldExpression>
                                </textField>
                            </jr:detailCell>
                        </jr:column>
                        <jr:column width="90">
                            <jr:tableHeader style="table 3_TH" height="0" rowSpan="1"/>
                            <jr:tableFooter style="table 3_TH" height="0" rowSpan="1"/>
                            <jr:columnHeader style="table 3_CH" height="30" rowSpan="1">
                                <textField>
                                    <reportElement x="0" y="0" width="90" height="30"/>
                                    <textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
                                        <font isBold="true"/>
                                    </textElement>
                                    <textFieldExpression class="java.lang.String"><![CDATA[$R{report.GeneratedOn}]]></textFieldExpression>
                                </textField>
                            </jr:columnHeader>
                            <jr:detailCell style="table 3_TD" height="20" rowSpan="1">
                                <textField isStretchWithOverflow="true">
                                    <reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="20"/>
                                    <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                    <textFieldExpression class="java.lang.String"><![CDATA[$P{Jasper_custom_formatter}.getDateInPropertyTimeZone($F{genration_date}, $P{REPORT_TIME_ZONE}, $P{PROPERTY_TIME_ZONE},$P{userDateFormat}+" HH:mm:ss", $P{REPORT_LOCALE})]]></textFieldExpression>
                                </textField>
                            </jr:detailCell>
                        </jr:column>
                    </jr:columnGroup>
                </jr:table>
            </componentElement>
        </band>
    </pageHeader>
    <columnHeader>
        <band height="20">
            <printWhenExpression><![CDATA[($P{IS_IGNORE_PAGINATION} && new Boolean($P{param_SheetForCriteria})==false)?true:false]]></printWhenExpression>
            <staticText>
                <reportElement x="0" y="0" width="90" height="20"/>
                <textElement/>
                <text><![CDATA[]]></text>
            </staticText>
        </band>
    </columnHeader>
    <detail>
        <band height="90" splitType="Stretch">
            <componentElement>
                <reportElement key="table 2" style="table 2" x="0" y="0" width="1290" height="90"/>
                <jr:table xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd">
                    <datasetRun subDataset="ds_change_srp">
                        <datasetParameter name="param_Property_ID">
                            <datasetParameterExpression><![CDATA[$P{param_Property_ID}]]></datasetParameterExpression>
                        </datasetParameter>
                        <datasetParameter name="param_Business_StartDate">
                            <datasetParameterExpression><![CDATA[$P{param_Business_StartDate}]]></datasetParameterExpression>
                        </datasetParameter>
                        <datasetParameter name="param_StartDate">
                            <datasetParameterExpression><![CDATA[$P{param_StartDate}]]></datasetParameterExpression>
                        </datasetParameter>
                        <datasetParameter name="param_EndDate">
                            <datasetParameterExpression><![CDATA[$P{param_EndDate}]]></datasetParameterExpression>
                        </datasetParameter>
                        <datasetParameter name="param_BaseCurrency">
                            <datasetParameterExpression><![CDATA[$P{param_BaseCurrency}]]></datasetParameterExpression>
                        </datasetParameter>
                        <datasetParameter name="param_SRPNames">
                            <datasetParameterExpression><![CDATA[$P{param_SRPNames}]]></datasetParameterExpression>
                        </datasetParameter>
                        <datasetParameter name="srpNamesList">
                            <datasetParameterExpression><![CDATA[$P{srpNamesList}]]></datasetParameterExpression>
                        </datasetParameter>
                        <datasetParameter name="JNDI_NAME">
                            <datasetParameterExpression><![CDATA[$P{JNDI_NAME}]]></datasetParameterExpression>
                        </datasetParameter>
                        <datasetParameter name="param_isRollingDate">
                            <datasetParameterExpression><![CDATA[$P{param_isRollingDate}]]></datasetParameterExpression>
                        </datasetParameter>
                        <datasetParameter name="param_RollingBusinessStartDate">
                            <datasetParameterExpression><![CDATA[$P{param_RollingBusinessStartDate}]]></datasetParameterExpression>
                        </datasetParameter>
                        <datasetParameter name="param_Rolling_Start_Date">
                            <datasetParameterExpression><![CDATA[$P{param_Rolling_Start_Date}]]></datasetParameterExpression>
                        </datasetParameter>
                        <datasetParameter name="param_Rolling_End_Date">
                            <datasetParameterExpression><![CDATA[$P{param_Rolling_End_Date}]]></datasetParameterExpression>
                        </datasetParameter>
						<datasetParameter name="post_departure_revenue_adjustment_enabled">
                            <datasetParameterExpression><![CDATA[$P{post_departure_revenue_adjustment_enabled}]]></datasetParameterExpression>
                        </datasetParameter>
                        <datasetParameter name="PROPERTY_TIME_ZONE">
							<datasetParameterExpression><![CDATA[$P{PROPERTY_TIME_ZONE}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="userDateFormat">
                            <datasetParameterExpression><![CDATA[$P{userDateFormat}]]></datasetParameterExpression>
                        </datasetParameter>
                        <datasetParameter name="dateFormatter">
                            <datasetParameterExpression><![CDATA[$P{dateFormatter}]]></datasetParameterExpression>
                        </datasetParameter>
                        <datasetParameter name="userLocale">
                            <datasetParameterExpression><![CDATA[$P{userLocale}]]></datasetParameterExpression>
                        </datasetParameter>
                        <connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
                    </datasetRun>
                    <jr:columnGroup width="13680">
                        <jr:column width="90">
                            <jr:columnHeader style="table 2_CH" height="80" rowSpan="3">
                                <textField pattern="" isBlankWhenNull="true">
                                    <reportElement mode="Transparent" x="0" y="0" width="90" height="80" forecolor="#000000" backcolor="#FFFFFF"/>
                                    <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                        <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                    </textElement>
                                    <textFieldExpression class="java.lang.String"><![CDATA[$R{day.of.arrival}]]></textFieldExpression>
                                </textField>
                            </jr:columnHeader>
                            <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                <textField>
                                    <reportElement x="0" y="0" width="90" height="20"/>
                                    <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                    <textFieldExpression class="java.util.Date"><![CDATA[$F{occupancy_dt}]]></textFieldExpression>
                                        <patternExpression><![CDATA[$P{userDateFormat}]]></patternExpression>
                                </textField>
                            </jr:detailCell>
                        </jr:column>
                        <jr:column width="90">
                            <jr:columnHeader style="table 2_CH" height="80" rowSpan="3">
                                <textField pattern="" isBlankWhenNull="true">
                                    <reportElement mode="Transparent" x="0" y="0" width="90" height="80" forecolor="#000000" backcolor="#FFFFFF"/>
                                    <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                        <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                    </textElement>
                                    <textFieldExpression class="java.lang.String"><![CDATA[$R{common.dow}]]></textFieldExpression>
                                </textField>
                            </jr:columnHeader>
                            <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                <textField>
                                    <reportElement x="0" y="0" width="90" height="20"/>
                                    <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                    <textFieldExpression class="java.lang.String"><![CDATA[str($F{dow}.toLowerCase())]]></textFieldExpression>
                                </textField>
                            </jr:detailCell>
                        </jr:column>
                        <jr:columnGroup width="4500">
                            <jr:columnHeader style="table 2_CH" height="20" rowSpan="1">
                                <textField pattern="" isBlankWhenNull="true">
                                    <reportElement mode="Transparent" x="0" y="0" width="4500" height="20" forecolor="#000000" backcolor="#FFFFFF"/>
                                    <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                        <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                    </textElement>
                                    <textFieldExpression class="java.lang.String"><![CDATA[$R{performanceComparisonReport.output.column.occupancyOnBooks}]]></textFieldExpression>
                                </textField>
                            </jr:columnHeader>
                            <jr:columnGroup width="180">
                                <printWhenExpression><![CDATA[($P{srpNamesList}.size()>=1 && (!$P{srpNamesList}.get(0).equals("-1")))?true:false]]></printWhenExpression>
                                <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                    <textField>
                                        <reportElement x="0" y="0" width="180" height="30"/>
                                        <textElement textAlignment="Center" verticalAlignment="Middle">
                                            <font isBold="true"/>
                                        </textElement>
                                        <textFieldExpression class="java.lang.String"><![CDATA[$P{srpNamesList}.size()>=1?(String)$P{srpNamesList}.get(0):""]]></textFieldExpression>
                                    </textField>
                                </jr:columnHeader>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField isBlankWhenNull="true">
                                            <reportElement x="0" y="0" width="90" height="30"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle">
                                                <font isBold="true"/>
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{common.current}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField>
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.lang.Integer"><![CDATA[$F{srp1_Rooms_onBooks}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{change}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField>
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.lang.Integer"><![CDATA[$F{srp1_Rooms_onBooks_diff}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                            </jr:columnGroup>
                            <jr:columnGroup width="180">
                                <printWhenExpression><![CDATA[($P{srpNamesList}.size()>=2 && (!$P{srpNamesList}.get(1).equals("-1")))?true:false]]></printWhenExpression>
                                <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                    <textField pattern="" isBlankWhenNull="false">
                                        <reportElement mode="Transparent" x="0" y="0" width="180" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                        <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                            <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                        </textElement>
                                        <textFieldExpression class="java.lang.String"><![CDATA[($P{srpNamesList}.size()>=2 && (String)$P{srpNamesList}.get(1)!= "-1")?(String)$P{srpNamesList}.get(1):""]]></textFieldExpression>
                                    </textField>
                                </jr:columnHeader>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{common.current}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField>
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.lang.Integer"><![CDATA[$F{srp2_Rooms_onBooks}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{change}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField>
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.lang.Integer"><![CDATA[$F{srp2_Rooms_onBooks_diff}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                            </jr:columnGroup>
                            <jr:columnGroup width="180">
                                <printWhenExpression><![CDATA[($P{srpNamesList}.size()>=3 && (!$P{srpNamesList}.get(2).equals("-1")))?true:false]]></printWhenExpression>
                                <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                    <textField>
                                        <reportElement x="0" y="0" width="180" height="30"/>
                                        <textElement textAlignment="Center" verticalAlignment="Middle">
                                            <font isBold="true"/>
                                        </textElement>
                                        <textFieldExpression class="java.lang.String"><![CDATA[$P{srpNamesList}.size()>=3?(String)$P{srpNamesList}.get(2):""]]></textFieldExpression>
                                    </textField>
                                </jr:columnHeader>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{common.current}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField>
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.lang.Integer"><![CDATA[$F{srp3_Rooms_onBooks}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{change}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField>
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.lang.Integer"><![CDATA[$F{srp3_Rooms_onBooks_diff}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                            </jr:columnGroup>
                            <jr:columnGroup width="180">
                                <printWhenExpression><![CDATA[($P{srpNamesList}.size()>=4 && (!$P{srpNamesList}.get(3).equals("-1")))?true:false]]></printWhenExpression>
                                <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                    <textField>
                                        <reportElement x="0" y="0" width="180" height="30"/>
                                        <textElement textAlignment="Center" verticalAlignment="Middle">
                                            <font isBold="true"/>
                                        </textElement>
                                        <textFieldExpression class="java.lang.String"><![CDATA[$P{srpNamesList}.size()>=4?(String)$P{srpNamesList}.get(3):""]]></textFieldExpression>
                                    </textField>
                                </jr:columnHeader>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{common.current}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField>
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.lang.Integer"><![CDATA[$F{srp4_Rooms_onBooks}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{change}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField>
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.lang.Integer"><![CDATA[$F{srp4_Rooms_onBooks_diff}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                            </jr:columnGroup>
                            <jr:columnGroup width="180">
                                <printWhenExpression><![CDATA[($P{srpNamesList}.size()>=5 && (!$P{srpNamesList}.get(4).equals("-1")))?true:false]]></printWhenExpression>
                                <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                    <textField>
                                        <reportElement x="0" y="0" width="180" height="30"/>
                                        <textElement textAlignment="Center" verticalAlignment="Middle">
                                            <font isBold="true"/>
                                        </textElement>
                                        <textFieldExpression class="java.lang.String"><![CDATA[$P{srpNamesList}.size()>=5?(String)$P{srpNamesList}.get(4):""]]></textFieldExpression>
                                    </textField>
                                </jr:columnHeader>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{common.current}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField>
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.lang.Integer"><![CDATA[$F{srp5_Rooms_onBooks}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{change}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField>
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.lang.Integer"><![CDATA[$F{srp5_Rooms_onBooks_diff}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                            </jr:columnGroup>
                            <jr:columnGroup width="180">
                                <printWhenExpression><![CDATA[($P{srpNamesList}.size()>=6 && (!$P{srpNamesList}.get(5).equals("-1")))?true:false]]></printWhenExpression>
                                <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                    <textField>
                                        <reportElement x="0" y="0" width="180" height="30"/>
                                        <textElement textAlignment="Center" verticalAlignment="Middle">
                                            <font isBold="true"/>
                                        </textElement>
                                        <textFieldExpression class="java.lang.String"><![CDATA[$P{srpNamesList}.size()>=6?(String)$P{srpNamesList}.get(5):""]]></textFieldExpression>
                                    </textField>
                                </jr:columnHeader>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{common.current}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField>
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.lang.Integer"><![CDATA[$F{srp6_Rooms_onBooks}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{change}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField>
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.lang.Integer"><![CDATA[$F{srp6_Rooms_onBooks_diff}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                            </jr:columnGroup>
                            <jr:columnGroup width="180">
                                <printWhenExpression><![CDATA[($P{srpNamesList}.size()>=7 && (!$P{srpNamesList}.get(6).equals("-1")))?true:false]]></printWhenExpression>
                                <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                    <textField>
                                        <reportElement x="0" y="0" width="180" height="30"/>
                                        <textElement textAlignment="Center" verticalAlignment="Middle">
                                            <font isBold="true"/>
                                        </textElement>
                                        <textFieldExpression class="java.lang.String"><![CDATA[$P{srpNamesList}.size()>=7?(String)$P{srpNamesList}.get(6):""]]></textFieldExpression>
                                    </textField>
                                </jr:columnHeader>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{common.current}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField>
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.lang.Integer"><![CDATA[$F{srp7_Rooms_onBooks}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{change}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField>
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.lang.Integer"><![CDATA[$F{srp7_Rooms_onBooks_diff}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                            </jr:columnGroup>
                            <jr:columnGroup width="180">
                                <printWhenExpression><![CDATA[($P{srpNamesList}.size()>=8 && (!$P{srpNamesList}.get(7).equals("-1")))?true:false]]></printWhenExpression>
                                <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                    <textField>
                                        <reportElement x="0" y="0" width="180" height="30"/>
                                        <textElement textAlignment="Center" verticalAlignment="Middle">
                                            <font isBold="true"/>
                                        </textElement>
                                        <textFieldExpression class="java.lang.String"><![CDATA[($P{srpNamesList}.size()>=8)?(String)$P{srpNamesList}.get(7):""]]></textFieldExpression>
                                    </textField>
                                </jr:columnHeader>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{common.current}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField>
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.lang.Integer"><![CDATA[$F{srp8_Rooms_onBooks}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{change}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField>
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.lang.Integer"><![CDATA[$F{srp8_Rooms_onBooks_diff}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                            </jr:columnGroup>
                            <jr:columnGroup width="180">
                                <printWhenExpression><![CDATA[($P{srpNamesList}.size()>=9 && (!$P{srpNamesList}.get(8).equals("-1")))?true:false]]></printWhenExpression>
                                <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                    <textField>
                                        <reportElement x="0" y="0" width="180" height="30"/>
                                        <textElement textAlignment="Center" verticalAlignment="Middle">
                                            <font isBold="true"/>
                                        </textElement>
                                        <textFieldExpression class="java.lang.String"><![CDATA[($P{srpNamesList}.size()>=9)?(String)$P{srpNamesList}.get(8):""]]></textFieldExpression>
                                    </textField>
                                </jr:columnHeader>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{common.current}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField>
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.lang.Integer"><![CDATA[$F{srp9_Rooms_onBooks}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{change}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField>
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.lang.Integer"><![CDATA[$F{srp9_Rooms_onBooks_diff}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                            </jr:columnGroup>
                            <jr:columnGroup width="180">
                                <printWhenExpression><![CDATA[($P{srpNamesList}.size()>=10 && (!$P{srpNamesList}.get(9).equals("-1")))?true:false]]></printWhenExpression>
                                <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                    <textField>
                                        <reportElement x="0" y="0" width="180" height="30"/>
                                        <textElement textAlignment="Center" verticalAlignment="Middle">
                                            <font isBold="true"/>
                                        </textElement>
                                        <textFieldExpression class="java.lang.String"><![CDATA[($P{srpNamesList}.size()>=10)?(String)$P{srpNamesList}.get(9):""]]></textFieldExpression>
                                    </textField>
                                </jr:columnHeader>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{common.current}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField>
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.lang.Integer"><![CDATA[$F{srp10_Rooms_onBooks}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{change}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField>
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.lang.Integer"><![CDATA[$F{srp10_Rooms_onBooks_diff}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                            </jr:columnGroup>
                            <jr:columnGroup width="180">
                                <printWhenExpression><![CDATA[($P{srpNamesList}.size()>=11 && (!$P{srpNamesList}.get(10).equals("-1")))?true:false]]></printWhenExpression>
                                <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                    <textField>
                                        <reportElement x="0" y="0" width="180" height="30"/>
                                        <textElement textAlignment="Center" verticalAlignment="Middle">
                                            <font isBold="true"/>
                                        </textElement>
                                        <textFieldExpression class="java.lang.String"><![CDATA[($P{srpNamesList}.size()>=11)?(String)$P{srpNamesList}.get(10):""]]></textFieldExpression>
                                    </textField>
                                </jr:columnHeader>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{common.current}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField>
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.lang.Integer"><![CDATA[$F{srp11_Rooms_onBooks}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{change}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField>
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.lang.Integer"><![CDATA[$F{srp11_Rooms_onBooks_diff}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                            </jr:columnGroup>
                            <jr:columnGroup width="180">
                                <printWhenExpression><![CDATA[($P{srpNamesList}.size()>=12 && (!$P{srpNamesList}.get(11).equals("-1")))?true:false]]></printWhenExpression>
                                <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                    <textField>
                                        <reportElement x="0" y="0" width="180" height="30"/>
                                        <textElement textAlignment="Center" verticalAlignment="Middle">
                                            <font isBold="true"/>
                                        </textElement>
                                        <textFieldExpression class="java.lang.String"><![CDATA[($P{srpNamesList}.size()>=12)?(String)$P{srpNamesList}.get(11):""]]></textFieldExpression>
                                    </textField>
                                </jr:columnHeader>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{common.current}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField>
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.lang.Integer"><![CDATA[$F{srp12_Rooms_onBooks}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{change}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField>
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.lang.Integer"><![CDATA[$F{srp12_Rooms_onBooks_diff}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                            </jr:columnGroup>
                            <jr:columnGroup width="180">
                                <printWhenExpression><![CDATA[($P{srpNamesList}.size()>=13 && (!$P{srpNamesList}.get(12).equals("-1")))?true:false]]></printWhenExpression>
                                <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                    <textField>
                                        <reportElement x="0" y="0" width="180" height="30"/>
                                        <textElement textAlignment="Center" verticalAlignment="Middle">
                                            <font isBold="true"/>
                                        </textElement>
                                        <textFieldExpression class="java.lang.String"><![CDATA[($P{srpNamesList}.size()>=13)?(String)$P{srpNamesList}.get(12):""]]></textFieldExpression>
                                    </textField>
                                </jr:columnHeader>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{common.current}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField>
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.lang.Integer"><![CDATA[$F{srp13_Rooms_onBooks}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{change}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField>
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.lang.Integer"><![CDATA[$F{srp13_Rooms_onBooks_diff}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                            </jr:columnGroup>
                            <jr:columnGroup width="180">
                                <printWhenExpression><![CDATA[($P{srpNamesList}.size()>=14 && (!$P{srpNamesList}.get(13).equals("-1")))?true:false]]></printWhenExpression>
                                <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                    <textField>
                                        <reportElement x="0" y="0" width="180" height="30"/>
                                        <textElement textAlignment="Center" verticalAlignment="Middle">
                                            <font isBold="true"/>
                                        </textElement>
                                        <textFieldExpression class="java.lang.String"><![CDATA[($P{srpNamesList}.size()>=14)?(String)$P{srpNamesList}.get(13):""]]></textFieldExpression>
                                    </textField>
                                </jr:columnHeader>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{common.current}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField>
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.lang.Integer"><![CDATA[$F{srp14_Rooms_onBooks}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{change}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField>
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.lang.Integer"><![CDATA[$F{srp14_Rooms_onBooks_diff}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                            </jr:columnGroup>
                            <jr:columnGroup width="180">
                                <printWhenExpression><![CDATA[($P{srpNamesList}.size()>=15 && (!$P{srpNamesList}.get(14).equals("-1")))?true:false]]></printWhenExpression>
                                <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                    <textField>
                                        <reportElement x="0" y="0" width="180" height="30"/>
                                        <textElement textAlignment="Center" verticalAlignment="Middle">
                                            <font isBold="true"/>
                                        </textElement>
                                        <textFieldExpression class="java.lang.String"><![CDATA[($P{srpNamesList}.size()>=15)?(String)$P{srpNamesList}.get(14):""]]></textFieldExpression>
                                    </textField>
                                </jr:columnHeader>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{common.current}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField>
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.lang.Integer"><![CDATA[$F{srp15_Rooms_onBooks}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{change}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField>
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.lang.Integer"><![CDATA[$F{srp15_Rooms_onBooks_diff}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                            </jr:columnGroup>
                            <jr:columnGroup width="180">
                                <printWhenExpression><![CDATA[($P{srpNamesList}.size()>=16 && (!$P{srpNamesList}.get(15).equals("-1")))?true:false]]></printWhenExpression>
                                <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                    <textField>
                                        <reportElement x="0" y="0" width="180" height="30"/>
                                        <textElement textAlignment="Center" verticalAlignment="Middle">
                                            <font isBold="true"/>
                                        </textElement>
                                        <textFieldExpression class="java.lang.String"><![CDATA[($P{srpNamesList}.size()>=16)?(String)$P{srpNamesList}.get(15):""]]></textFieldExpression>
                                    </textField>
                                </jr:columnHeader>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{common.current}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField>
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.lang.Integer"><![CDATA[$F{srp16_Rooms_onBooks}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{change}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField>
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.lang.Integer"><![CDATA[$F{srp16_Rooms_onBooks_diff}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                            </jr:columnGroup>
                            <jr:columnGroup width="180">
                                <printWhenExpression><![CDATA[($P{srpNamesList}.size()>=17 && (!$P{srpNamesList}.get(16).equals("-1")))?true:false]]></printWhenExpression>
                                <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                    <textField>
                                        <reportElement x="0" y="0" width="180" height="30"/>
                                        <textElement textAlignment="Center" verticalAlignment="Middle">
                                            <font isBold="true"/>
                                        </textElement>
                                        <textFieldExpression class="java.lang.String"><![CDATA[($P{srpNamesList}.size()>=17)?(String)$P{srpNamesList}.get(16):""]]></textFieldExpression>
                                    </textField>
                                </jr:columnHeader>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{common.current}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField>
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.lang.Integer"><![CDATA[$F{srp17_Rooms_onBooks}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{change}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField>
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.lang.Integer"><![CDATA[$F{srp17_Rooms_onBooks_diff}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                            </jr:columnGroup>
                            <jr:columnGroup width="180">
                                <printWhenExpression><![CDATA[($P{srpNamesList}.size()>=18 && (!$P{srpNamesList}.get(17).equals("-1")))?true:false]]></printWhenExpression>
                                <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                    <textField>
                                        <reportElement x="0" y="0" width="180" height="30"/>
                                        <textElement textAlignment="Center" verticalAlignment="Middle">
                                            <font isBold="true"/>
                                        </textElement>
                                        <textFieldExpression class="java.lang.String"><![CDATA[($P{srpNamesList}.size()>=18)?(String)$P{srpNamesList}.get(17):""]]></textFieldExpression>
                                    </textField>
                                </jr:columnHeader>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{common.current}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField>
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.lang.Integer"><![CDATA[$F{srp18_Rooms_onBooks}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{change}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField>
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.lang.Integer"><![CDATA[$F{srp18_Rooms_onBooks_diff}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                            </jr:columnGroup>
                            <jr:columnGroup width="180">
                                <printWhenExpression><![CDATA[($P{srpNamesList}.size()>=19 && (!$P{srpNamesList}.get(18).equals("-1")))?true:false]]></printWhenExpression>
                                <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                    <textField>
                                        <reportElement x="0" y="0" width="180" height="30"/>
                                        <textElement textAlignment="Center" verticalAlignment="Middle">
                                            <font isBold="true"/>
                                        </textElement>
                                        <textFieldExpression class="java.lang.String"><![CDATA[($P{srpNamesList}.size()>=19)?(String)$P{srpNamesList}.get(18):""]]></textFieldExpression>
                                    </textField>
                                </jr:columnHeader>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{common.current}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField>
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.lang.Integer"><![CDATA[$F{srp19_Rooms_onBooks}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{change}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField>
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.lang.Integer"><![CDATA[$F{srp19_Rooms_onBooks_diff}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                            </jr:columnGroup>
                            <jr:columnGroup width="180">
                                <printWhenExpression><![CDATA[($P{srpNamesList}.size()>=20 && (!$P{srpNamesList}.get(19).equals("-1")))?true:false]]></printWhenExpression>
                                <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                    <textField>
                                        <reportElement x="0" y="0" width="180" height="30"/>
                                        <textElement textAlignment="Center" verticalAlignment="Middle">
                                            <font isBold="true"/>
                                        </textElement>
                                        <textFieldExpression class="java.lang.String"><![CDATA[($P{srpNamesList}.size()>=20)?(String)$P{srpNamesList}.get(19):""]]></textFieldExpression>
                                    </textField>
                                </jr:columnHeader>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{common.current}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField>
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.lang.Integer"><![CDATA[$F{srp20_Rooms_onBooks}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{change}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField>
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.lang.Integer"><![CDATA[$F{srp20_Rooms_onBooks_diff}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                            </jr:columnGroup>
                            <jr:columnGroup width="180">
                                <printWhenExpression><![CDATA[($P{srpNamesList}.size()>=21 && (!$P{srpNamesList}.get(20).equals("-1")))?true:false]]></printWhenExpression>
                                <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                    <textField>
                                        <reportElement x="0" y="0" width="180" height="30"/>
                                        <textElement textAlignment="Center" verticalAlignment="Middle">
                                            <font isBold="true"/>
                                        </textElement>
                                        <textFieldExpression class="java.lang.String"><![CDATA[($P{srpNamesList}.size()>=21)?(String)$P{srpNamesList}.get(20):""]]></textFieldExpression>
                                    </textField>
                                </jr:columnHeader>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{common.current}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField>
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.lang.Integer"><![CDATA[$F{srp21_Rooms_onBooks}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{change}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField>
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.lang.Integer"><![CDATA[$F{srp21_Rooms_onBooks_diff}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                            </jr:columnGroup>
                            <jr:columnGroup width="180">
                                <printWhenExpression><![CDATA[($P{srpNamesList}.size()>=22 && (!$P{srpNamesList}.get(21).equals("-1")))?true:false]]></printWhenExpression>
                                <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                    <textField>
                                        <reportElement x="0" y="0" width="180" height="30"/>
                                        <textElement textAlignment="Center" verticalAlignment="Middle">
                                            <font isBold="true"/>
                                        </textElement>
                                        <textFieldExpression class="java.lang.String"><![CDATA[($P{srpNamesList}.size()>=22)?(String)$P{srpNamesList}.get(21):""]]></textFieldExpression>
                                    </textField>
                                </jr:columnHeader>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{common.current}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField>
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.lang.Integer"><![CDATA[$F{srp22_Rooms_onBooks}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{change}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField>
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.lang.Integer"><![CDATA[$F{srp22_Rooms_onBooks_diff}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                            </jr:columnGroup>
                            <jr:columnGroup width="180">
                                <printWhenExpression><![CDATA[($P{srpNamesList}.size()>=23 && (!$P{srpNamesList}.get(22).equals("-1")))?true:false]]></printWhenExpression>
                                <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                    <textField>
                                        <reportElement x="0" y="0" width="180" height="30"/>
                                        <textElement textAlignment="Center" verticalAlignment="Middle">
                                            <font isBold="true"/>
                                        </textElement>
                                        <textFieldExpression class="java.lang.String"><![CDATA[($P{srpNamesList}.size()>=23)?(String)$P{srpNamesList}.get(22):""]]></textFieldExpression>
                                    </textField>
                                </jr:columnHeader>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{common.current}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField>
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.lang.Integer"><![CDATA[$F{srp23_Rooms_onBooks}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{change}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField>
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.lang.Integer"><![CDATA[$F{srp23_Rooms_onBooks_diff}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                            </jr:columnGroup>
                            <jr:columnGroup width="180">
                                <printWhenExpression><![CDATA[($P{srpNamesList}.size()>=24 && (!$P{srpNamesList}.get(23).equals("-1")))?true:false]]></printWhenExpression>
                                <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                    <textField>
                                        <reportElement x="0" y="0" width="180" height="30"/>
                                        <textElement textAlignment="Center" verticalAlignment="Middle">
                                            <font isBold="true"/>
                                        </textElement>
                                        <textFieldExpression class="java.lang.String"><![CDATA[($P{srpNamesList}.size()>=24)?(String)$P{srpNamesList}.get(23):""]]></textFieldExpression>
                                    </textField>
                                </jr:columnHeader>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{common.current}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField>
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.lang.Integer"><![CDATA[$F{srp24_Rooms_onBooks}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{change}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField>
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.lang.Integer"><![CDATA[$F{srp24_Rooms_onBooks_diff}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                            </jr:columnGroup>
                            <jr:columnGroup width="180">
                                <printWhenExpression><![CDATA[($P{srpNamesList}.size()>=25 && (!$P{srpNamesList}.get(24).equals("-1")))?true:false]]></printWhenExpression>
                                <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                    <textField>
                                        <reportElement x="0" y="0" width="180" height="30"/>
                                        <textElement textAlignment="Center" verticalAlignment="Middle">
                                            <font isBold="true"/>
                                        </textElement>
                                        <textFieldExpression class="java.lang.String"><![CDATA[($P{srpNamesList}.size()>=25)?(String)$P{srpNamesList}.get(24):""]]></textFieldExpression>
                                    </textField>
                                </jr:columnHeader>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{common.current}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField>
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.lang.Integer"><![CDATA[$F{srp25_Rooms_onBooks}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{change}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField>
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.lang.Integer"><![CDATA[$F{srp25_Rooms_onBooks_diff}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                            </jr:columnGroup>
                        </jr:columnGroup>
                        <jr:columnGroup width="4500">
                            <jr:columnHeader style="table 2_CH" height="20" rowSpan="1">
                                <textField>
                                    <reportElement x="0" y="0" width="4500" height="20"/>
                                    <textElement textAlignment="Center" verticalAlignment="Middle">
                                        <font isBold="true"/>
                                    </textElement>
                                    <textFieldExpression class="java.lang.String"><![CDATA[$R{revenue.on.books} + " (" + $P{param_BaseCurrency} + ")"]]></textFieldExpression>
                                </textField>
                            </jr:columnHeader>
                            <jr:columnGroup width="180">
                                <printWhenExpression><![CDATA[($P{srpNamesList}.size()>=1 && (!$P{srpNamesList}.get(0).equals("-1")))?true:false]]></printWhenExpression>
                                <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                    <textField>
                                        <reportElement x="0" y="0" width="180" height="30"/>
                                        <textElement textAlignment="Center" verticalAlignment="Middle">
                                            <font isBold="true"/>
                                        </textElement>
                                        <textFieldExpression class="java.lang.String"><![CDATA[$P{srpNamesList}.size()>=1?(String)$P{srpNamesList}.get(0):""]]></textFieldExpression>
                                    </textField>
                                </jr:columnHeader>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{common.current}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField pattern="###0.00" isBlankWhenNull="true">
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{srp1_Revenue}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{change}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField pattern="###0.00" isBlankWhenNull="true">
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{srp1_Revenue_diff}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                            </jr:columnGroup>
                            <jr:columnGroup width="180">
                                <printWhenExpression><![CDATA[($P{srpNamesList}.size()>=2 && (!$P{srpNamesList}.get(1).equals("-1")))?true:false]]></printWhenExpression>
                                <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                    <textField pattern="" isBlankWhenNull="false">
                                        <reportElement mode="Transparent" x="0" y="0" width="180" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                        <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                            <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                        </textElement>
                                        <textFieldExpression class="java.lang.String"><![CDATA[$P{srpNamesList}.size()>=2?(String)$P{srpNamesList}.get(1):""]]></textFieldExpression>
                                    </textField>
                                </jr:columnHeader>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{common.current}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField pattern="###0.00" isBlankWhenNull="true">
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{srp2_Revenue}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{change}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField pattern="###0.00" isBlankWhenNull="true">
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{srp2_Revenue_diff}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                            </jr:columnGroup>
                            <jr:columnGroup width="180">
                                <printWhenExpression><![CDATA[($P{srpNamesList}.size()>=3 && (!$P{srpNamesList}.get(2).equals("-1")))?true:false]]></printWhenExpression>
                                <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                    <textField>
                                        <reportElement x="0" y="0" width="180" height="30"/>
                                        <textElement textAlignment="Center" verticalAlignment="Middle">
                                            <font isBold="true"/>
                                        </textElement>
                                        <textFieldExpression class="java.lang.String"><![CDATA[$P{srpNamesList}.size()>=3?(String)$P{srpNamesList}.get(2):""]]></textFieldExpression>
                                    </textField>
                                </jr:columnHeader>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{common.current}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField pattern="###0.00" isBlankWhenNull="true">
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{srp3_Revenue}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{change}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField pattern="###0.00" isBlankWhenNull="true">
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{srp3_Revenue_diff}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                            </jr:columnGroup>
                            <jr:columnGroup width="180">
                                <printWhenExpression><![CDATA[($P{srpNamesList}.size()>=4 && (!$P{srpNamesList}.get(3).equals("-1")))?true:false]]></printWhenExpression>
                                <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                    <textField>
                                        <reportElement x="0" y="0" width="180" height="30"/>
                                        <textElement textAlignment="Center" verticalAlignment="Middle">
                                            <font isBold="true"/>
                                        </textElement>
                                        <textFieldExpression class="java.lang.String"><![CDATA[$P{srpNamesList}.size()>=4?(String)$P{srpNamesList}.get(3):""]]></textFieldExpression>
                                    </textField>
                                </jr:columnHeader>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{common.current}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField pattern="###0.00" isBlankWhenNull="true">
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{srp4_Revenue}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{change}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField pattern="###0.00" isBlankWhenNull="true">
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{srp4_Revenue_diff}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                            </jr:columnGroup>
                            <jr:columnGroup width="180">
                                <printWhenExpression><![CDATA[($P{srpNamesList}.size()>=5 && (!$P{srpNamesList}.get(4).equals("-1")))?true:false]]></printWhenExpression>
                                <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                    <textField>
                                        <reportElement x="0" y="0" width="180" height="30"/>
                                        <textElement textAlignment="Center" verticalAlignment="Middle">
                                            <font isBold="true"/>
                                        </textElement>
                                        <textFieldExpression class="java.lang.String"><![CDATA[$P{srpNamesList}.size()>=5?(String)$P{srpNamesList}.get(4):""]]></textFieldExpression>
                                    </textField>
                                </jr:columnHeader>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{common.current}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField pattern="###0.00" isBlankWhenNull="true">
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{srp5_Revenue}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{change}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField pattern="###0.00" isBlankWhenNull="true">
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{srp5_Revenue_diff}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                            </jr:columnGroup>
                            <jr:columnGroup width="180">
                                <printWhenExpression><![CDATA[($P{srpNamesList}.size()>=6 && (!$P{srpNamesList}.get(5).equals("-1")))?true:false]]></printWhenExpression>
                                <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                    <textField>
                                        <reportElement x="0" y="0" width="180" height="30"/>
                                        <textElement textAlignment="Center" verticalAlignment="Middle">
                                            <font isBold="true"/>
                                        </textElement>
                                        <textFieldExpression class="java.lang.String"><![CDATA[$P{srpNamesList}.size()>=6?(String)$P{srpNamesList}.get(5):""]]></textFieldExpression>
                                    </textField>
                                </jr:columnHeader>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{common.current}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField pattern="###0.00" isBlankWhenNull="true">
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{srp6_Revenue}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{change}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField pattern="###0.00" isBlankWhenNull="true">
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{srp6_Revenue_diff}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                            </jr:columnGroup>
                            <jr:columnGroup width="180">
                                <printWhenExpression><![CDATA[($P{srpNamesList}.size()>=7 && (!$P{srpNamesList}.get(6).equals("-1")))?true:false]]></printWhenExpression>
                                <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                    <textField>
                                        <reportElement x="0" y="0" width="180" height="30"/>
                                        <textElement textAlignment="Center" verticalAlignment="Middle">
                                            <font isBold="true"/>
                                        </textElement>
                                        <textFieldExpression class="java.lang.String"><![CDATA[$P{srpNamesList}.size()>=7?(String)$P{srpNamesList}.get(6):""]]></textFieldExpression>
                                    </textField>
                                </jr:columnHeader>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{common.current}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField pattern="###0.00" isBlankWhenNull="true">
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{srp7_Revenue}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{change}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField pattern="###0.00" isBlankWhenNull="true">
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{srp7_Revenue_diff}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                            </jr:columnGroup>
                            <jr:columnGroup width="180">
                                <printWhenExpression><![CDATA[($P{srpNamesList}.size()>=8 && (!$P{srpNamesList}.get(7).equals("-1")))?true:false]]></printWhenExpression>
                                <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                    <textField>
                                        <reportElement x="0" y="0" width="180" height="30"/>
                                        <textElement textAlignment="Center" verticalAlignment="Middle">
                                            <font isBold="true"/>
                                        </textElement>
                                        <textFieldExpression class="java.lang.String"><![CDATA[$P{srpNamesList}.size()>=8?(String)$P{srpNamesList}.get(7):""]]></textFieldExpression>
                                    </textField>
                                </jr:columnHeader>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{common.current}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField pattern="###0.00" isBlankWhenNull="true">
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{srp8_Revenue}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{change}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField pattern="###0.00" isBlankWhenNull="true">
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{srp8_Revenue_diff}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                            </jr:columnGroup>
                            <jr:columnGroup width="180">
                                <printWhenExpression><![CDATA[($P{srpNamesList}.size()>=9 && (!$P{srpNamesList}.get(8).equals("-1")))?true:false]]></printWhenExpression>
                                <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                    <textField>
                                        <reportElement x="0" y="0" width="180" height="30"/>
                                        <textElement textAlignment="Center" verticalAlignment="Middle">
                                            <font isBold="true"/>
                                        </textElement>
                                        <textFieldExpression class="java.lang.String"><![CDATA[$P{srpNamesList}.size()>=9?(String)$P{srpNamesList}.get(8):""]]></textFieldExpression>
                                    </textField>
                                </jr:columnHeader>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{common.current}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField pattern="###0.00" isBlankWhenNull="true">
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{srp9_Revenue}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{change}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField pattern="###0.00" isBlankWhenNull="true">
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{srp9_Revenue_diff}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                            </jr:columnGroup>
                            <jr:columnGroup width="180">
                                <printWhenExpression><![CDATA[($P{srpNamesList}.size()>=10 && (!$P{srpNamesList}.get(9).equals("-1")))?true:false]]></printWhenExpression>
                                <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                    <textField>
                                        <reportElement x="0" y="0" width="180" height="30"/>
                                        <textElement textAlignment="Center" verticalAlignment="Middle">
                                            <font isBold="true"/>
                                        </textElement>
                                        <textFieldExpression class="java.lang.String"><![CDATA[$P{srpNamesList}.size()>=10?(String)$P{srpNamesList}.get(9):""]]></textFieldExpression>
                                    </textField>
                                </jr:columnHeader>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{common.current}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField pattern="###0.00" isBlankWhenNull="true">
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{srp10_Revenue}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{change}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField pattern="###0.00" isBlankWhenNull="true">
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{srp10_Revenue_diff}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                            </jr:columnGroup>
                            <jr:columnGroup width="180">
                                <printWhenExpression><![CDATA[($P{srpNamesList}.size()>=11 && (!$P{srpNamesList}.get(10).equals("-1")))?true:false]]></printWhenExpression>
                                <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                    <textField>
                                        <reportElement x="0" y="0" width="180" height="30"/>
                                        <textElement textAlignment="Center" verticalAlignment="Middle">
                                            <font isBold="true"/>
                                        </textElement>
                                        <textFieldExpression class="java.lang.String"><![CDATA[$P{srpNamesList}.size()>=11?(String)$P{srpNamesList}.get(10):""]]></textFieldExpression>
                                    </textField>
                                </jr:columnHeader>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{common.current}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField pattern="###0.00" isBlankWhenNull="true">
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{srp11_Revenue}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{change}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField pattern="###0.00" isBlankWhenNull="true">
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{srp11_Revenue_diff}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                            </jr:columnGroup>
                            <jr:columnGroup width="180">
                                <printWhenExpression><![CDATA[($P{srpNamesList}.size()>=12 && (!$P{srpNamesList}.get(11).equals("-1")))?true:false]]></printWhenExpression>
                                <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                    <textField>
                                        <reportElement x="0" y="0" width="180" height="30"/>
                                        <textElement textAlignment="Center" verticalAlignment="Middle">
                                            <font isBold="true"/>
                                        </textElement>
                                        <textFieldExpression class="java.lang.String"><![CDATA[$P{srpNamesList}.size()>=12?(String)$P{srpNamesList}.get(11):""]]></textFieldExpression>
                                    </textField>
                                </jr:columnHeader>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{common.current}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField pattern="###0.00" isBlankWhenNull="true">
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{srp12_Revenue}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{change}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField pattern="###0.00" isBlankWhenNull="true">
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{srp12_Revenue_diff}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                            </jr:columnGroup>
                            <jr:columnGroup width="180">
                                <printWhenExpression><![CDATA[($P{srpNamesList}.size()>=13 && (!$P{srpNamesList}.get(12).equals("-1")))?true:false]]></printWhenExpression>
                                <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                    <textField>
                                        <reportElement x="0" y="0" width="180" height="30"/>
                                        <textElement textAlignment="Center" verticalAlignment="Middle">
                                            <font isBold="true"/>
                                        </textElement>
                                        <textFieldExpression class="java.lang.String"><![CDATA[$P{srpNamesList}.size()>=13?(String)$P{srpNamesList}.get(12):""]]></textFieldExpression>
                                    </textField>
                                </jr:columnHeader>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{common.current}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField pattern="###0.00" isBlankWhenNull="true">
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{srp13_Revenue}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{change}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField pattern="###0.00" isBlankWhenNull="true">
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{srp13_Revenue_diff}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                            </jr:columnGroup>
                            <jr:columnGroup width="180">
                                <printWhenExpression><![CDATA[($P{srpNamesList}.size()>=14 && (!$P{srpNamesList}.get(13).equals("-1")))?true:false]]></printWhenExpression>
                                <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                    <textField>
                                        <reportElement x="0" y="0" width="180" height="30"/>
                                        <textElement textAlignment="Center" verticalAlignment="Middle">
                                            <font isBold="true"/>
                                        </textElement>
                                        <textFieldExpression class="java.lang.String"><![CDATA[$P{srpNamesList}.size()>=14?(String)$P{srpNamesList}.get(13):""]]></textFieldExpression>
                                    </textField>
                                </jr:columnHeader>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{common.current}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField pattern="###0.00" isBlankWhenNull="true">
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{srp14_Revenue}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{change}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField pattern="###0.00" isBlankWhenNull="true">
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{srp14_Revenue_diff}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                            </jr:columnGroup>
                            <jr:columnGroup width="180">
                                <printWhenExpression><![CDATA[($P{srpNamesList}.size()>=15 && (!$P{srpNamesList}.get(14).equals("-1")))?true:false]]></printWhenExpression>
                                <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                    <textField>
                                        <reportElement x="0" y="0" width="180" height="30"/>
                                        <textElement textAlignment="Center" verticalAlignment="Middle">
                                            <font isBold="true"/>
                                        </textElement>
                                        <textFieldExpression class="java.lang.String"><![CDATA[$P{srpNamesList}.size()>=15?(String)$P{srpNamesList}.get(14):""]]></textFieldExpression>
                                    </textField>
                                </jr:columnHeader>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{common.current}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField pattern="###0.00" isBlankWhenNull="true">
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{srp15_Revenue}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{change}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField pattern="###0.00" isBlankWhenNull="true">
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{srp15_Revenue_diff}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                            </jr:columnGroup>
                            <jr:columnGroup width="180">
                                <printWhenExpression><![CDATA[($P{srpNamesList}.size()>=16 && (!$P{srpNamesList}.get(15).equals("-1")))?true:false]]></printWhenExpression>
                                <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                    <textField>
                                        <reportElement x="0" y="0" width="180" height="30"/>
                                        <textElement textAlignment="Center" verticalAlignment="Middle">
                                            <font isBold="true"/>
                                        </textElement>
                                        <textFieldExpression class="java.lang.String"><![CDATA[$P{srpNamesList}.size()>=16?(String)$P{srpNamesList}.get(15):""]]></textFieldExpression>
                                    </textField>
                                </jr:columnHeader>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{common.current}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField pattern="###0.00" isBlankWhenNull="true">
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{srp16_Revenue}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{change}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField pattern="###0.00" isBlankWhenNull="true">
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{srp16_Revenue_diff}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                            </jr:columnGroup>
                            <jr:columnGroup width="180">
                                <printWhenExpression><![CDATA[($P{srpNamesList}.size()>=17 && (!$P{srpNamesList}.get(16).equals("-1")))?true:false]]></printWhenExpression>
                                <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                    <textField>
                                        <reportElement x="0" y="0" width="180" height="30"/>
                                        <textElement textAlignment="Center" verticalAlignment="Middle">
                                            <font isBold="true"/>
                                        </textElement>
                                        <textFieldExpression class="java.lang.String"><![CDATA[$P{srpNamesList}.size()>=17?(String)$P{srpNamesList}.get(16):""]]></textFieldExpression>
                                    </textField>
                                </jr:columnHeader>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{common.current}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField pattern="###0.00" isBlankWhenNull="true">
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{srp17_Revenue}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{change}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField pattern="###0.00" isBlankWhenNull="true">
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{srp17_Revenue_diff}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                            </jr:columnGroup>
                            <jr:columnGroup width="180">
                                <printWhenExpression><![CDATA[($P{srpNamesList}.size()>=18 && (!$P{srpNamesList}.get(17).equals("-1")))?true:false]]></printWhenExpression>
                                <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                    <textField>
                                        <reportElement x="0" y="0" width="180" height="30"/>
                                        <textElement textAlignment="Center" verticalAlignment="Middle">
                                            <font isBold="true"/>
                                        </textElement>
                                        <textFieldExpression class="java.lang.String"><![CDATA[$P{srpNamesList}.size()>=18?(String)$P{srpNamesList}.get(17):""]]></textFieldExpression>
                                    </textField>
                                </jr:columnHeader>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{common.current}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField pattern="###0.00" isBlankWhenNull="true">
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{srp18_Revenue}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{change}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField pattern="###0.00" isBlankWhenNull="true">
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{srp18_Revenue_diff}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                            </jr:columnGroup>
                            <jr:columnGroup width="180">
                                <printWhenExpression><![CDATA[($P{srpNamesList}.size()>=19 && (!$P{srpNamesList}.get(18).equals("-1")))?true:false]]></printWhenExpression>
                                <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                    <textField>
                                        <reportElement x="0" y="0" width="180" height="30"/>
                                        <textElement textAlignment="Center" verticalAlignment="Middle">
                                            <font isBold="true"/>
                                        </textElement>
                                        <textFieldExpression class="java.lang.String"><![CDATA[$P{srpNamesList}.size()>=19?(String)$P{srpNamesList}.get(18):""]]></textFieldExpression>
                                    </textField>
                                </jr:columnHeader>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{common.current}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField pattern="###0.00" isBlankWhenNull="true">
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{srp19_Revenue}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{change}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField pattern="###0.00" isBlankWhenNull="true">
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{srp19_Revenue_diff}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                            </jr:columnGroup>
                            <jr:columnGroup width="180">
                                <printWhenExpression><![CDATA[($P{srpNamesList}.size()>=20 && (!$P{srpNamesList}.get(19).equals("-1")))?true:false]]></printWhenExpression>
                                <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                    <textField>
                                        <reportElement x="0" y="0" width="180" height="30"/>
                                        <textElement textAlignment="Center" verticalAlignment="Middle">
                                            <font isBold="true"/>
                                        </textElement>
                                        <textFieldExpression class="java.lang.String"><![CDATA[$P{srpNamesList}.size()>=20?(String)$P{srpNamesList}.get(19):""]]></textFieldExpression>
                                    </textField>
                                </jr:columnHeader>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{common.current}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField pattern="###0.00" isBlankWhenNull="true">
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{srp20_Revenue}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{change}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField pattern="###0.00" isBlankWhenNull="true">
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{srp20_Revenue_diff}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                            </jr:columnGroup>
                            <jr:columnGroup width="180">
                                <printWhenExpression><![CDATA[($P{srpNamesList}.size()>=21 && (!$P{srpNamesList}.get(20).equals("-1")))?true:false]]></printWhenExpression>
                                <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                    <textField>
                                        <reportElement x="0" y="0" width="180" height="30"/>
                                        <textElement textAlignment="Center" verticalAlignment="Middle">
                                            <font isBold="true"/>
                                        </textElement>
                                        <textFieldExpression class="java.lang.String"><![CDATA[$P{srpNamesList}.size()>=21?(String)$P{srpNamesList}.get(20):""]]></textFieldExpression>
                                    </textField>
                                </jr:columnHeader>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{common.current}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField pattern="###0.00" isBlankWhenNull="true">
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{srp21_Revenue}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{change}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField pattern="###0.00" isBlankWhenNull="true">
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{srp21_Revenue_diff}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                            </jr:columnGroup>
                            <jr:columnGroup width="180">
                                <printWhenExpression><![CDATA[($P{srpNamesList}.size()>=22 && (!$P{srpNamesList}.get(21).equals("-1")))?true:false]]></printWhenExpression>
                                <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                    <textField>
                                        <reportElement x="0" y="0" width="180" height="30"/>
                                        <textElement textAlignment="Center" verticalAlignment="Middle">
                                            <font isBold="true"/>
                                        </textElement>
                                        <textFieldExpression class="java.lang.String"><![CDATA[$P{srpNamesList}.size()>=22?(String)$P{srpNamesList}.get(21):""]]></textFieldExpression>
                                    </textField>
                                </jr:columnHeader>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{common.current}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField pattern="###0.00" isBlankWhenNull="true">
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{srp22_Revenue}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{change}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField pattern="###0.00" isBlankWhenNull="true">
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{srp22_Revenue_diff}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                            </jr:columnGroup>
                            <jr:columnGroup width="180">
                                <printWhenExpression><![CDATA[($P{srpNamesList}.size()>=23 && (!$P{srpNamesList}.get(22).equals("-1")))?true:false]]></printWhenExpression>
                                <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                    <textField>
                                        <reportElement x="0" y="0" width="180" height="30"/>
                                        <textElement textAlignment="Center" verticalAlignment="Middle">
                                            <font isBold="true"/>
                                        </textElement>
                                        <textFieldExpression class="java.lang.String"><![CDATA[$P{srpNamesList}.size()>=23?(String)$P{srpNamesList}.get(22):""]]></textFieldExpression>
                                    </textField>
                                </jr:columnHeader>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{common.current}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField pattern="###0.00" isBlankWhenNull="true">
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{srp23_Revenue}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{change}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField pattern="###0.00" isBlankWhenNull="true">
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{srp23_Revenue_diff}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                            </jr:columnGroup>
                            <jr:columnGroup width="180">
                                <printWhenExpression><![CDATA[($P{srpNamesList}.size()>=24 && (!$P{srpNamesList}.get(23).equals("-1")))?true:false]]></printWhenExpression>
                                <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                    <textField>
                                        <reportElement x="0" y="0" width="180" height="30"/>
                                        <textElement textAlignment="Center" verticalAlignment="Middle">
                                            <font isBold="true"/>
                                        </textElement>
                                        <textFieldExpression class="java.lang.String"><![CDATA[$P{srpNamesList}.size()>=24?(String)$P{srpNamesList}.get(23):""]]></textFieldExpression>
                                    </textField>
                                </jr:columnHeader>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{common.current}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField pattern="###0.00" isBlankWhenNull="true">
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{srp24_Revenue}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{change}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField pattern="###0.00" isBlankWhenNull="true">
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{srp24_Revenue_diff}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                            </jr:columnGroup>
                            <jr:columnGroup width="180">
                                <printWhenExpression><![CDATA[($P{srpNamesList}.size()>=25 && (!$P{srpNamesList}.get(24).equals("-1")))?true:false]]></printWhenExpression>
                                <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                    <textField>
                                        <reportElement x="0" y="0" width="180" height="30"/>
                                        <textElement textAlignment="Center" verticalAlignment="Middle">
                                            <font isBold="true"/>
                                        </textElement>
                                        <textFieldExpression class="java.lang.String"><![CDATA[$P{srpNamesList}.size()>=25?(String)$P{srpNamesList}.get(24):""]]></textFieldExpression>
                                    </textField>
                                </jr:columnHeader>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{common.current}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField pattern="###0.00" isBlankWhenNull="true">
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{srp25_Revenue}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{change}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField pattern="###0.00" isBlankWhenNull="true">
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{srp25_Revenue_diff}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                            </jr:columnGroup>
                        </jr:columnGroup>
                        <jr:columnGroup width="4500">
                            <jr:columnHeader style="table 2_CH" height="20" rowSpan="1">
                                <textField>
                                    <reportElement x="0" y="0" width="4500" height="20"/>
                                    <textElement textAlignment="Center" verticalAlignment="Middle">
                                        <font isBold="true"/>
                                    </textElement>
                                    <textFieldExpression class="java.lang.String"><![CDATA[$R{adr.on.books}+ " (" + $P{param_BaseCurrency} + ")"]]></textFieldExpression>
                                </textField>
                            </jr:columnHeader>
                            <jr:columnGroup width="180">
                                <printWhenExpression><![CDATA[($P{srpNamesList}.size()>=1 && (!$P{srpNamesList}.get(0).equals("-1")))?true:false]]></printWhenExpression>
                                <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                    <textField>
                                        <reportElement x="0" y="0" width="180" height="30"/>
                                        <textElement textAlignment="Center" verticalAlignment="Middle">
                                            <font isBold="true"/>
                                        </textElement>
                                        <textFieldExpression class="java.lang.String"><![CDATA[$P{srpNamesList}.size()>=1?(String)$P{srpNamesList}.get(0):""]]></textFieldExpression>
                                    </textField>
                                </jr:columnHeader>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{common.current}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField pattern="###0.00" isBlankWhenNull="true">
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{srp1_ADR}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{change}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField pattern="###0.00" isBlankWhenNull="true">
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{srp1_ADR_diff}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                            </jr:columnGroup>
                            <jr:columnGroup width="180">
                                <printWhenExpression><![CDATA[($P{srpNamesList}.size()>=2 && (!$P{srpNamesList}.get(1).equals("-1")))?true:false]]></printWhenExpression>
                                <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                    <textField pattern="" isBlankWhenNull="false">
                                        <reportElement mode="Transparent" x="0" y="0" width="180" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                        <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                            <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                        </textElement>
                                        <textFieldExpression class="java.lang.String"><![CDATA[$P{srpNamesList}.size()>=2?(String)$P{srpNamesList}.get(1):""]]></textFieldExpression>
                                    </textField>
                                </jr:columnHeader>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{common.current}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField pattern="###0.00" isBlankWhenNull="true">
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{srp2_ADR}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{change}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField pattern="###0.00" isBlankWhenNull="true">
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{srp2_ADR_diff}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                            </jr:columnGroup>
                            <jr:columnGroup width="180">
                                <printWhenExpression><![CDATA[($P{srpNamesList}.size()>=3 && (!$P{srpNamesList}.get(2).equals("-1")))?true:false]]></printWhenExpression>
                                <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                    <textField>
                                        <reportElement x="0" y="0" width="180" height="30"/>
                                        <textElement textAlignment="Center" verticalAlignment="Middle">
                                            <font isBold="true"/>
                                        </textElement>
                                        <textFieldExpression class="java.lang.String"><![CDATA[$P{srpNamesList}.size()>=3?(String)$P{srpNamesList}.get(2):""]]></textFieldExpression>
                                    </textField>
                                </jr:columnHeader>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{common.current}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField pattern="###0.00" isBlankWhenNull="true">
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{srp3_ADR}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{change}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField pattern="###0.00" isBlankWhenNull="true">
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{srp3_ADR_diff}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                            </jr:columnGroup>
                            <jr:columnGroup width="180">
                                <printWhenExpression><![CDATA[($P{srpNamesList}.size()>=4 && (!$P{srpNamesList}.get(3).equals("-1")))?true:false]]></printWhenExpression>
                                <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                    <textField>
                                        <reportElement x="0" y="0" width="180" height="30"/>
                                        <textElement textAlignment="Center" verticalAlignment="Middle">
                                            <font isBold="true"/>
                                        </textElement>
                                        <textFieldExpression class="java.lang.String"><![CDATA[$P{srpNamesList}.size()>=4?(String)$P{srpNamesList}.get(3):""]]></textFieldExpression>
                                    </textField>
                                </jr:columnHeader>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{common.current}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField pattern="###0.00" isBlankWhenNull="true">
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{srp4_ADR}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{change}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField pattern="###0.00" isBlankWhenNull="true">
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{srp4_ADR_diff}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                            </jr:columnGroup>
                            <jr:columnGroup width="180">
                                <printWhenExpression><![CDATA[($P{srpNamesList}.size()>=5 && (!$P{srpNamesList}.get(4).equals("-1")))?true:false]]></printWhenExpression>
                                <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                    <textField>
                                        <reportElement x="0" y="0" width="180" height="30"/>
                                        <textElement textAlignment="Center" verticalAlignment="Middle">
                                            <font isBold="true"/>
                                        </textElement>
                                        <textFieldExpression class="java.lang.String"><![CDATA[$P{srpNamesList}.size()>=5?(String)$P{srpNamesList}.get(4):""]]></textFieldExpression>
                                    </textField>
                                </jr:columnHeader>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{common.current}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField pattern="###0.00" isBlankWhenNull="true">
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{srp5_ADR}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{change}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField pattern="###0.00" isBlankWhenNull="true">
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{srp5_ADR_diff}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                            </jr:columnGroup>
                            <jr:columnGroup width="180">
                                <printWhenExpression><![CDATA[($P{srpNamesList}.size()>=6 && (!$P{srpNamesList}.get(5).equals("-1")))?true:false]]></printWhenExpression>
                                <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                    <textField>
                                        <reportElement x="0" y="0" width="180" height="30"/>
                                        <textElement textAlignment="Center" verticalAlignment="Middle">
                                            <font isBold="true"/>
                                        </textElement>
                                        <textFieldExpression class="java.lang.String"><![CDATA[$P{srpNamesList}.size()>=6?(String)$P{srpNamesList}.get(5):""]]></textFieldExpression>
                                    </textField>
                                </jr:columnHeader>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{common.current}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField pattern="###0.00" isBlankWhenNull="true">
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{srp6_ADR}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{change}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField pattern="###0.00" isBlankWhenNull="true">
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{srp6_ADR_diff}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                            </jr:columnGroup>
                            <jr:columnGroup width="180">
                                <printWhenExpression><![CDATA[($P{srpNamesList}.size()>=7 && (!$P{srpNamesList}.get(6).equals("-1")))?true:false]]></printWhenExpression>
                                <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                    <textField>
                                        <reportElement x="0" y="0" width="180" height="30"/>
                                        <textElement textAlignment="Center" verticalAlignment="Middle">
                                            <font isBold="true"/>
                                        </textElement>
                                        <textFieldExpression class="java.lang.String"><![CDATA[$P{srpNamesList}.size()>=7?(String)$P{srpNamesList}.get(6):""]]></textFieldExpression>
                                    </textField>
                                </jr:columnHeader>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{common.current}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField pattern="###0.00" isBlankWhenNull="true">
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{srp7_ADR}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{change}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField pattern="###0.00" isBlankWhenNull="true">
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{srp7_ADR_diff}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                            </jr:columnGroup>
                            <jr:columnGroup width="180">
                                <printWhenExpression><![CDATA[($P{srpNamesList}.size()>=8 && (!$P{srpNamesList}.get(7).equals("-1")))?true:false]]></printWhenExpression>
                                <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                    <textField>
                                        <reportElement x="0" y="0" width="180" height="30"/>
                                        <textElement textAlignment="Center" verticalAlignment="Middle">
                                            <font isBold="true"/>
                                        </textElement>
                                        <textFieldExpression class="java.lang.String"><![CDATA[$P{srpNamesList}.size()>=8?(String)$P{srpNamesList}.get(7):""]]></textFieldExpression>
                                    </textField>
                                </jr:columnHeader>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{common.current}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField pattern="###0.00" isBlankWhenNull="true">
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{srp8_ADR}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{change}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField pattern="###0.00" isBlankWhenNull="true">
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{srp8_ADR_diff}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                            </jr:columnGroup>
                            <jr:columnGroup width="180">
                                <printWhenExpression><![CDATA[($P{srpNamesList}.size()>=9 && (!$P{srpNamesList}.get(8).equals("-1")))?true:false]]></printWhenExpression>
                                <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                    <textField>
                                        <reportElement x="0" y="0" width="180" height="30"/>
                                        <textElement textAlignment="Center" verticalAlignment="Middle">
                                            <font isBold="true"/>
                                        </textElement>
                                        <textFieldExpression class="java.lang.String"><![CDATA[$P{srpNamesList}.size()>=9?(String)$P{srpNamesList}.get(8):""]]></textFieldExpression>
                                    </textField>
                                </jr:columnHeader>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{common.current}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField pattern="###0.00" isBlankWhenNull="true">
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{srp9_ADR}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{change}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField pattern="###0.00" isBlankWhenNull="true">
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{srp9_ADR_diff}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                            </jr:columnGroup>
                            <jr:columnGroup width="180">
                                <printWhenExpression><![CDATA[($P{srpNamesList}.size()>=10 && (!$P{srpNamesList}.get(9).equals("-1")))?true:false]]></printWhenExpression>
                                <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                    <textField>
                                        <reportElement x="0" y="0" width="180" height="30"/>
                                        <textElement textAlignment="Center" verticalAlignment="Middle">
                                            <font isBold="true"/>
                                        </textElement>
                                        <textFieldExpression class="java.lang.String"><![CDATA[$P{srpNamesList}.size()>=10?(String)$P{srpNamesList}.get(9):""]]></textFieldExpression>
                                    </textField>
                                </jr:columnHeader>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{common.current}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField pattern="###0.00" isBlankWhenNull="true">
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{srp10_ADR}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{change}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField pattern="###0.00" isBlankWhenNull="true">
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{srp10_ADR_diff}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                            </jr:columnGroup>
                            <jr:columnGroup width="180">
                                <printWhenExpression><![CDATA[($P{srpNamesList}.size()>=11 && (!$P{srpNamesList}.get(10).equals("-1")))?true:false]]></printWhenExpression>
                                <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                    <textField>
                                        <reportElement x="0" y="0" width="180" height="30"/>
                                        <textElement textAlignment="Center" verticalAlignment="Middle">
                                            <font isBold="true"/>
                                        </textElement>
                                        <textFieldExpression class="java.lang.String"><![CDATA[$P{srpNamesList}.size()>=11?(String)$P{srpNamesList}.get(10):""]]></textFieldExpression>
                                    </textField>
                                </jr:columnHeader>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{common.current}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField pattern="###0.00" isBlankWhenNull="true">
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{srp11_ADR}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{change}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField pattern="###0.00" isBlankWhenNull="true">
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{srp11_ADR_diff}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                            </jr:columnGroup>
                            <jr:columnGroup width="180">
                                <printWhenExpression><![CDATA[($P{srpNamesList}.size()>=12 && (!$P{srpNamesList}.get(11).equals("-1")))?true:false]]></printWhenExpression>
                                <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                    <textField>
                                        <reportElement x="0" y="0" width="180" height="30"/>
                                        <textElement textAlignment="Center" verticalAlignment="Middle">
                                            <font isBold="true"/>
                                        </textElement>
                                        <textFieldExpression class="java.lang.String"><![CDATA[$P{srpNamesList}.size()>=12?(String)$P{srpNamesList}.get(11):""]]></textFieldExpression>
                                    </textField>
                                </jr:columnHeader>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{common.current}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField pattern="###0.00" isBlankWhenNull="true">
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{srp12_ADR}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{change}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField pattern="###0.00" isBlankWhenNull="true">
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{srp12_ADR_diff}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                            </jr:columnGroup>
                            <jr:columnGroup width="180">
                                <printWhenExpression><![CDATA[($P{srpNamesList}.size()>=13 && (!$P{srpNamesList}.get(12).equals("-1")))?true:false]]></printWhenExpression>
                                <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                    <textField>
                                        <reportElement x="0" y="0" width="180" height="30"/>
                                        <textElement textAlignment="Center" verticalAlignment="Middle">
                                            <font isBold="true"/>
                                        </textElement>
                                        <textFieldExpression class="java.lang.String"><![CDATA[$P{srpNamesList}.size()>=13?(String)$P{srpNamesList}.get(12):""]]></textFieldExpression>
                                    </textField>
                                </jr:columnHeader>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{common.current}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField pattern="###0.00" isBlankWhenNull="true">
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{srp13_ADR}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{change}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField pattern="###0.00" isBlankWhenNull="true">
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{srp13_ADR_diff}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                            </jr:columnGroup>
                            <jr:columnGroup width="180">
                                <printWhenExpression><![CDATA[($P{srpNamesList}.size()>=14 && (!$P{srpNamesList}.get(13).equals("-1")))?true:false]]></printWhenExpression>
                                <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                    <textField>
                                        <reportElement x="0" y="0" width="180" height="30"/>
                                        <textElement textAlignment="Center" verticalAlignment="Middle">
                                            <font isBold="true"/>
                                        </textElement>
                                        <textFieldExpression class="java.lang.String"><![CDATA[$P{srpNamesList}.size()>=14?(String)$P{srpNamesList}.get(13):""]]></textFieldExpression>
                                    </textField>
                                </jr:columnHeader>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{common.current}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField pattern="###0.00" isBlankWhenNull="true">
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{srp14_ADR}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{change}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField pattern="###0.00" isBlankWhenNull="true">
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{srp14_ADR_diff}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                            </jr:columnGroup>
                            <jr:columnGroup width="180">
                                <printWhenExpression><![CDATA[($P{srpNamesList}.size()>=15 && (!$P{srpNamesList}.get(14).equals("-1")))?true:false]]></printWhenExpression>
                                <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                    <textField>
                                        <reportElement x="0" y="0" width="180" height="30"/>
                                        <textElement textAlignment="Center" verticalAlignment="Middle">
                                            <font isBold="true"/>
                                        </textElement>
                                        <textFieldExpression class="java.lang.String"><![CDATA[$P{srpNamesList}.size()>=15?(String)$P{srpNamesList}.get(14):""]]></textFieldExpression>
                                    </textField>
                                </jr:columnHeader>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{common.current}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField pattern="###0.00" isBlankWhenNull="true">
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{srp15_ADR}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{change}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField pattern="###0.00" isBlankWhenNull="true">
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{srp15_ADR_diff}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                            </jr:columnGroup>
                            <jr:columnGroup width="180">
                                <printWhenExpression><![CDATA[($P{srpNamesList}.size()>=16 && (!$P{srpNamesList}.get(15).equals("-1")))?true:false]]></printWhenExpression>
                                <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                    <textField>
                                        <reportElement x="0" y="0" width="180" height="30"/>
                                        <textElement textAlignment="Center" verticalAlignment="Middle">
                                            <font isBold="true"/>
                                        </textElement>
                                        <textFieldExpression class="java.lang.String"><![CDATA[$P{srpNamesList}.size()>=16?(String)$P{srpNamesList}.get(15):""]]></textFieldExpression>
                                    </textField>
                                </jr:columnHeader>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{common.current}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField pattern="###0.00" isBlankWhenNull="true">
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{srp16_ADR}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{change}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField pattern="###0.00" isBlankWhenNull="true">
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{srp16_ADR_diff}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                            </jr:columnGroup>
                            <jr:columnGroup width="180">
                                <printWhenExpression><![CDATA[($P{srpNamesList}.size()>=17 && (!$P{srpNamesList}.get(16).equals("-1")))?true:false]]></printWhenExpression>
                                <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                    <textField>
                                        <reportElement x="0" y="0" width="180" height="30"/>
                                        <textElement textAlignment="Center" verticalAlignment="Middle">
                                            <font isBold="true"/>
                                        </textElement>
                                        <textFieldExpression class="java.lang.String"><![CDATA[$P{srpNamesList}.size()>=17?(String)$P{srpNamesList}.get(16):""]]></textFieldExpression>
                                    </textField>
                                </jr:columnHeader>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{common.current}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField pattern="###0.00" isBlankWhenNull="true">
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{srp17_ADR}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{change}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField pattern="###0.00" isBlankWhenNull="true">
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{srp17_ADR_diff}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                            </jr:columnGroup>
                            <jr:columnGroup width="180">
                                <printWhenExpression><![CDATA[($P{srpNamesList}.size()>=18 && (!$P{srpNamesList}.get(17).equals("-1")))?true:false]]></printWhenExpression>
                                <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                    <textField>
                                        <reportElement x="0" y="0" width="180" height="30"/>
                                        <textElement textAlignment="Center" verticalAlignment="Middle">
                                            <font isBold="true"/>
                                        </textElement>
                                        <textFieldExpression class="java.lang.String"><![CDATA[$P{srpNamesList}.size()>=18?(String)$P{srpNamesList}.get(17):""]]></textFieldExpression>
                                    </textField>
                                </jr:columnHeader>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{common.current}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField pattern="###0.00" isBlankWhenNull="true">
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{srp18_ADR}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{change}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField pattern="###0.00" isBlankWhenNull="true">
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{srp18_ADR_diff}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                            </jr:columnGroup>
                            <jr:columnGroup width="180">
                                <printWhenExpression><![CDATA[($P{srpNamesList}.size()>=19 && (!$P{srpNamesList}.get(18).equals("-1")))?true:false]]></printWhenExpression>
                                <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                    <textField>
                                        <reportElement x="0" y="0" width="180" height="30"/>
                                        <textElement textAlignment="Center" verticalAlignment="Middle">
                                            <font isBold="true"/>
                                        </textElement>
                                        <textFieldExpression class="java.lang.String"><![CDATA[$P{srpNamesList}.size()>=19?(String)$P{srpNamesList}.get(18):""]]></textFieldExpression>
                                    </textField>
                                </jr:columnHeader>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{common.current}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField pattern="###0.00" isBlankWhenNull="true">
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{srp19_ADR}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{change}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField pattern="###0.00" isBlankWhenNull="true">
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{srp19_ADR_diff}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                            </jr:columnGroup>
                            <jr:columnGroup width="180">
                                <printWhenExpression><![CDATA[($P{srpNamesList}.size()>=20 && (!$P{srpNamesList}.get(19).equals("-1")))?true:false]]></printWhenExpression>
                                <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                    <textField>
                                        <reportElement x="0" y="0" width="180" height="30"/>
                                        <textElement textAlignment="Center" verticalAlignment="Middle">
                                            <font isBold="true"/>
                                        </textElement>
                                        <textFieldExpression class="java.lang.String"><![CDATA[$P{srpNamesList}.size()>=20?(String)$P{srpNamesList}.get(19):""]]></textFieldExpression>
                                    </textField>
                                </jr:columnHeader>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{common.current}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField pattern="###0.00" isBlankWhenNull="true">
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{srp20_ADR}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{change}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField pattern="###0.00" isBlankWhenNull="true">
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{srp20_ADR_diff}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                            </jr:columnGroup>
                            <jr:columnGroup width="180">
                                <printWhenExpression><![CDATA[($P{srpNamesList}.size()>=21 && (!$P{srpNamesList}.get(20).equals("-1")))?true:false]]></printWhenExpression>
                                <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                    <textField>
                                        <reportElement x="0" y="0" width="180" height="30"/>
                                        <textElement textAlignment="Center" verticalAlignment="Middle">
                                            <font isBold="true"/>
                                        </textElement>
                                        <textFieldExpression class="java.lang.String"><![CDATA[$P{srpNamesList}.size()>=21?(String)$P{srpNamesList}.get(20):""]]></textFieldExpression>
                                    </textField>
                                </jr:columnHeader>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{common.current}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField pattern="###0.00" isBlankWhenNull="true">
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{srp21_ADR}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{change}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField pattern="###0.00" isBlankWhenNull="true">
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{srp21_ADR_diff}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                            </jr:columnGroup>
                            <jr:columnGroup width="180">
                                <printWhenExpression><![CDATA[($P{srpNamesList}.size()>=22 && (!$P{srpNamesList}.get(21).equals("-1")))?true:false]]></printWhenExpression>
                                <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                    <textField>
                                        <reportElement x="0" y="0" width="180" height="30"/>
                                        <textElement textAlignment="Center" verticalAlignment="Middle">
                                            <font isBold="true"/>
                                        </textElement>
                                        <textFieldExpression class="java.lang.String"><![CDATA[$P{srpNamesList}.size()>=22?(String)$P{srpNamesList}.get(21):""]]></textFieldExpression>
                                    </textField>
                                </jr:columnHeader>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{common.current}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField pattern="###0.00" isBlankWhenNull="true">
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{srp22_ADR}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{change}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField pattern="###0.00" isBlankWhenNull="true">
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{srp22_ADR_diff}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                            </jr:columnGroup>
                            <jr:columnGroup width="180">
                                <printWhenExpression><![CDATA[($P{srpNamesList}.size()>=23 && (!$P{srpNamesList}.get(22).equals("-1")))?true:false]]></printWhenExpression>
                                <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                    <textField>
                                        <reportElement x="0" y="0" width="180" height="30"/>
                                        <textElement textAlignment="Center" verticalAlignment="Middle">
                                            <font isBold="true"/>
                                        </textElement>
                                        <textFieldExpression class="java.lang.String"><![CDATA[$P{srpNamesList}.size()>=23?(String)$P{srpNamesList}.get(22):""]]></textFieldExpression>
                                    </textField>
                                </jr:columnHeader>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{common.current}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField pattern="###0.00" isBlankWhenNull="true">
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{srp23_ADR}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{change}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField pattern="###0.00" isBlankWhenNull="true">
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{srp23_ADR_diff}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                            </jr:columnGroup>
                            <jr:columnGroup width="180">
                                <printWhenExpression><![CDATA[($P{srpNamesList}.size()>=24 && (!$P{srpNamesList}.get(23).equals("-1")))?true:false]]></printWhenExpression>
                                <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                    <textField>
                                        <reportElement x="0" y="0" width="180" height="30"/>
                                        <textElement textAlignment="Center" verticalAlignment="Middle">
                                            <font isBold="true"/>
                                        </textElement>
                                        <textFieldExpression class="java.lang.String"><![CDATA[$P{srpNamesList}.size()>=24?(String)$P{srpNamesList}.get(23):""]]></textFieldExpression>
                                    </textField>
                                </jr:columnHeader>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{common.current}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField pattern="###0.00" isBlankWhenNull="true">
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{srp24_ADR}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{change}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField pattern="###0.00" isBlankWhenNull="true">
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{srp24_ADR_diff}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                            </jr:columnGroup>
                            <jr:columnGroup width="180">
                                <printWhenExpression><![CDATA[($P{srpNamesList}.size()>=25 && (!$P{srpNamesList}.get(24).equals("-1")))?true:false]]></printWhenExpression>
                                <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                    <textField>
                                        <reportElement x="0" y="0" width="180" height="30"/>
                                        <textElement textAlignment="Center" verticalAlignment="Middle">
                                            <font isBold="true"/>
                                        </textElement>
                                        <textFieldExpression class="java.lang.String"><![CDATA[$P{srpNamesList}.size()>=25?(String)$P{srpNamesList}.get(24):""]]></textFieldExpression>
                                    </textField>
                                </jr:columnHeader>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{common.current}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField pattern="###0.00" isBlankWhenNull="true">
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{srp25_ADR}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                                <jr:column width="90">
                                    <jr:columnHeader style="table 2_CH" height="30" rowSpan="1">
                                        <textField pattern="" isBlankWhenNull="true">
                                            <reportElement mode="Transparent" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
                                                <font size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" />
                                            </textElement>
                                            <textFieldExpression class="java.lang.String"><![CDATA[$R{change}]]></textFieldExpression>
                                        </textField>
                                    </jr:columnHeader>
                                    <jr:detailCell style="table 2_TD" height="20" rowSpan="1">
                                        <textField pattern="###0.00" isBlankWhenNull="true">
                                            <reportElement x="0" y="0" width="90" height="20"/>
                                            <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                            <textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{srp25_ADR_diff}]]></textFieldExpression>
                                        </textField>
                                    </jr:detailCell>
                                </jr:column>
                            </jr:columnGroup>
                        </jr:columnGroup>
                    </jr:columnGroup>
                </jr:table>
            </componentElement>
        </band>
        <band height="45">
            <printWhenExpression><![CDATA[($P{IS_IGNORE_PAGINATION} && new Boolean($P{param_SheetForCriteria}))?true:false]]></printWhenExpression>
            <textField>
                <reportElement x="0" y="24" width="1290" height="20">
                    <propertyExpression name="net.sf.jasperreports.export.xls.break.before.row"><![CDATA[$P{param_SheetForCriteria}.toString().equalsIgnoreCase("true")?"true":"false"]]></propertyExpression>
                    <propertyExpression name="net.sf.jasperreports.export.xls.sheet.name"><![CDATA[$R{report.ReportCriteria}]]></propertyExpression>
                </reportElement>
                <textElement textAlignment="Left" verticalAlignment="Middle">
                    <font size="12" isBold="true"/>
                </textElement>
                <textFieldExpression><![CDATA["   "+$R{change.report.at.special.rate.plan.level}+" "+$R{side.by.side.view}]]></textFieldExpression>
            </textField>
            <textField>
                <reportElement x="0" y="0" width="1290" height="20">
                    <propertyExpression name="net.sf.jasperreports.export.xls.sheet.name"><![CDATA[$R{change.special.rate.plan}]]></propertyExpression>
                </reportElement>
                <textElement textAlignment="Left" verticalAlignment="Middle">
                    <font size="12" isBold="true"/>
                </textElement>
                <textFieldExpression><![CDATA["   "]]></textFieldExpression>
            </textField>
        </band>
        <band height="50">
            <printWhenExpression><![CDATA[($P{IS_IGNORE_PAGINATION} && new Boolean($P{param_SheetForCriteria}))?true:false
]]></printWhenExpression>
            <componentElement>
                <reportElement key="table 3" style="table 3" stretchType="RelativeToBandHeight" x="0" y="0" width="360" height="50"/>
                <jr:table xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd">
                    <datasetRun subDataset="ExcelHeader">
                        <datasetParameter name="param_Property_ID">
                            <datasetParameterExpression><![CDATA[$P{param_Property_ID}]]></datasetParameterExpression>
                        </datasetParameter>
                        <datasetParameter name="param_User_ID">
                            <datasetParameterExpression><![CDATA[$P{param_User_ID}]]></datasetParameterExpression>
                        </datasetParameter>
                        <datasetParameter name="param_BaseCurrency">
                            <datasetParameterExpression><![CDATA[$P{param_BaseCurrency}]]></datasetParameterExpression>
                        </datasetParameter>
                        <datasetParameter name="JNDI_NAME">
                            <datasetParameterExpression><![CDATA[$P{JNDI_NAME}]]></datasetParameterExpression>
                        </datasetParameter>
                        <datasetParameter name="param_AnalysisStartDate">
                            <datasetParameterExpression><![CDATA[$P{param_StartDate}]]></datasetParameterExpression>
                        </datasetParameter>
                        <datasetParameter name="param_AnalysisEndDate">
                            <datasetParameterExpression><![CDATA[$P{param_EndDate}]]></datasetParameterExpression>
                        </datasetParameter>
                        <datasetParameter name="param_ComparisonStartDate">
                            <datasetParameterExpression><![CDATA[$P{param_Business_StartDate}]]></datasetParameterExpression>
                        </datasetParameter>
                        <datasetParameter name="param_isRollingDate">
                            <datasetParameterExpression><![CDATA[$P{param_isRollingDate}]]></datasetParameterExpression>
                        </datasetParameter>
                        <datasetParameter name="param_RollingAnalysisStartDate">
                            <datasetParameterExpression><![CDATA[$P{param_Rolling_Start_Date}]]></datasetParameterExpression>
                        </datasetParameter>
                        <datasetParameter name="param_RollingAnalysisEndDate">
                            <datasetParameterExpression><![CDATA[$P{param_Rolling_End_Date}]]></datasetParameterExpression>
                        </datasetParameter>
                        <datasetParameter name="param_RollingComparisionStartDate">
                            <datasetParameterExpression><![CDATA[$P{param_RollingBusinessStartDate}]]></datasetParameterExpression>
                        </datasetParameter>
                        <datasetParameter name="Jasper_custom_formatter">
                            <datasetParameterExpression><![CDATA[$P{Jasper_custom_formatter}]]></datasetParameterExpression>
                        </datasetParameter>
                        <datasetParameter name="PROPERTY_TIME_ZONE">
							<datasetParameterExpression><![CDATA[$P{PROPERTY_TIME_ZONE}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="userDateFormat">
                            <datasetParameterExpression><![CDATA[$P{userDateFormat}]]></datasetParameterExpression>
                        </datasetParameter>
                        <datasetParameter name="dateFormatter">
                            <datasetParameterExpression><![CDATA[$P{dateFormatter}]]></datasetParameterExpression>
                        </datasetParameter>
                        <datasetParameter name="userLocale">
                            <datasetParameterExpression><![CDATA[$P{userLocale}]]></datasetParameterExpression>
                        </datasetParameter>
                        <connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
                    </datasetRun>
                    <jr:columnGroup width="630">
                        <jr:tableHeader height="30" rowSpan="1">
                            <textField>
                                <reportElement x="0" y="0" width="630" height="30"/>
                                <textElement textAlignment="Center" verticalAlignment="Middle">
                                    <font size="11" isBold="true"/>
                                </textElement>
                                <textFieldExpression><![CDATA[$R{report.ReportCriteria}]]></textFieldExpression>
                            </textField>
                        </jr:tableHeader>
                        <jr:column width="90">
                            <jr:tableHeader style="table 3_TH" height="0" rowSpan="1"/>
                            <jr:tableFooter style="table 3_TH" height="0" rowSpan="1"/>
                            <jr:columnHeader style="table 3_CH" height="30" rowSpan="1">
                                <textField>
                                    <reportElement x="0" y="0" width="90" height="30"/>
                                    <textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
                                        <font isBold="true"/>
                                    </textElement>
                                    <textFieldExpression><![CDATA[$R{common.propertyName}]]></textFieldExpression>
                                </textField>
                            </jr:columnHeader>
                            <jr:detailCell style="table 3_TD" height="20" rowSpan="1">
                                <textField isStretchWithOverflow="true">
                                    <reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="20"/>
                                    <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                    <textFieldExpression><![CDATA[$F{property_name}]]></textFieldExpression>
                                </textField>
                            </jr:detailCell>
                        </jr:column>
                        <jr:column width="90">
                            <jr:tableHeader style="table 3_TH" height="0" rowSpan="1"/>
                            <jr:tableFooter style="table 3_TH" height="0" rowSpan="1"/>
                            <jr:columnHeader style="table 3_CH" height="30" rowSpan="1">
                                <textField>
                                    <reportElement x="0" y="0" width="90" height="30"/>
                                    <textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
                                        <font isBold="true"/>
                                    </textElement>
                                    <textFieldExpression><![CDATA[$R{analysisStartDateLabel}]]></textFieldExpression>
                                </textField>
                            </jr:columnHeader>
                            <jr:detailCell style="table 3_TD" height="20" rowSpan="1">
                                <textField isStretchWithOverflow="true">
                                    <reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="20"/>
                                    <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                    <textFieldExpression><![CDATA[$F{analysis_start_date}]]></textFieldExpression>
                                    <patternExpression><![CDATA[$P{userDateFormat}]]></patternExpression>
                                </textField>
                            </jr:detailCell>
                        </jr:column>
                        <jr:column width="90">
                            <jr:tableHeader style="table 3_TH" height="0" rowSpan="1"/>
                            <jr:tableFooter style="table 3_TH" height="0" rowSpan="1"/>
                            <jr:columnHeader style="table 3_CH" height="30" rowSpan="1">
                                <textField>
                                    <reportElement x="0" y="0" width="90" height="30"/>
                                    <textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
                                        <font isBold="true"/>
                                    </textElement>
                                    <textFieldExpression><![CDATA[$R{performanceComparisonReport.filter.label.analysisEndDate}]]></textFieldExpression>
                                </textField>
                            </jr:columnHeader>
                            <jr:detailCell style="table 3_TD" height="20" rowSpan="1">
                                <textField isStretchWithOverflow="true">
                                    <reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="20"/>
                                    <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                    <textFieldExpression><![CDATA[$F{analysis_end_date}]]></textFieldExpression>
                                    <patternExpression><![CDATA[$P{userDateFormat}]]></patternExpression>
                                </textField>
                            </jr:detailCell>
                        </jr:column>
                        <jr:column width="90">
                            <jr:tableHeader style="table 3_TH" height="0" rowSpan="1"/>
                            <jr:tableFooter style="table 3_TH" height="0" rowSpan="1"/>
                            <jr:columnHeader style="table 3_CH" height="30" rowSpan="1">
                                <textField>
                                    <reportElement x="0" y="0" width="90" height="30"/>
                                    <textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
                                        <font isBold="true"/>
                                    </textElement>
                                    <textFieldExpression><![CDATA[$R{activity.start.date}]]></textFieldExpression>
                                </textField>
                            </jr:columnHeader>
                            <jr:detailCell style="table 3_TD" height="20" rowSpan="1">
                                <textField isStretchWithOverflow="true">
                                    <reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="20"/>
                                    <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                    <textFieldExpression><![CDATA[$F{comparision_start_date}]]></textFieldExpression>
                                    <patternExpression><![CDATA[$P{userDateFormat}]]></patternExpression>
                                </textField>
                            </jr:detailCell>
                        </jr:column>
                        <jr:column width="90">
                            <jr:tableHeader style="table 3_TH" height="0" rowSpan="1"/>
                            <jr:tableFooter style="table 3_TH" height="0" rowSpan="1"/>
                            <jr:columnHeader style="table 3_CH" height="30" rowSpan="1">
                                <textField>
                                    <reportElement x="0" y="0" width="90" height="30"/>
                                    <textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
                                        <font isBold="true"/>
                                    </textElement>
                                    <textFieldExpression><![CDATA[$R{currency}]]></textFieldExpression>
                                </textField>
                            </jr:columnHeader>
                            <jr:detailCell style="table 3_TD" height="20" rowSpan="1">
                                <textField isStretchWithOverflow="true">
                                    <reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="20"/>
                                    <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                    <textFieldExpression><![CDATA[$F{param_BaseCurrency}]]></textFieldExpression>
                                </textField>
                            </jr:detailCell>
                        </jr:column>
                        <jr:column width="90">
                            <jr:tableHeader style="table 3_TH" height="0" rowSpan="1"/>
                            <jr:tableFooter style="table 3_TH" height="0" rowSpan="1"/>
                            <jr:columnHeader style="table 3_CH" height="30" rowSpan="1">
                                <textField>
                                    <reportElement x="0" y="0" width="90" height="30"/>
                                    <textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
                                        <font isBold="true"/>
                                    </textElement>
                                    <textFieldExpression><![CDATA[$R{createdBy}]]></textFieldExpression>
                                </textField>
                            </jr:columnHeader>
                            <jr:detailCell style="table 3_TD" height="20" rowSpan="1">
                                <textField isStretchWithOverflow="true">
                                    <reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="20"/>
                                    <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                    <textFieldExpression><![CDATA[$F{created_by}]]></textFieldExpression>
                                </textField>
                            </jr:detailCell>
                        </jr:column>
                        <jr:column width="90">
                            <jr:tableHeader style="table 3_TH" height="0" rowSpan="1"/>
                            <jr:tableFooter style="table 3_TH" height="0" rowSpan="1"/>
                            <jr:columnHeader style="table 3_CH" height="30" rowSpan="1">
                                <textField>
                                    <reportElement x="0" y="0" width="90" height="30"/>
                                    <textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
                                        <font isBold="true"/>
                                    </textElement>
                                    <textFieldExpression><![CDATA[$R{report.GeneratedOn}]]></textFieldExpression>
                                </textField>
                            </jr:columnHeader>
                            <jr:detailCell style="table 3_TD" height="20" rowSpan="1">
                                <textField isStretchWithOverflow="true">
                                    <reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="20"/>
                                    <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                    <textFieldExpression><![CDATA[$P{Jasper_custom_formatter}.getDateInPropertyTimeZone($F{genration_date}, $P{REPORT_TIME_ZONE}, $P{PROPERTY_TIME_ZONE},$P{userDateFormat}+" HH:mm:ss", $P{REPORT_LOCALE})]]></textFieldExpression>
                                </textField>
                            </jr:detailCell>
                        </jr:column>
                    </jr:columnGroup>
                </jr:table>
            </componentElement>
        </band>
    </detail>
    <pageFooter>
        <band splitType="Stretch"/>
    </pageFooter>
    <summary>
        <band splitType="Stretch"/>
    </summary>
    <noData>
        <band height="40">
            <textField>
                <reportElement x="0" y="0" width="1290" height="40"/>
                <textElement textAlignment="Left" verticalAlignment="Middle">
                    <font size="12" isBold="true"/>
                </textElement>
                <textFieldExpression class="java.lang.String"><![CDATA["  "+$R{change.report.at.special.rate.plan.level}+"  ---   "
+$R{no.data.for.filter.criteria}]]></textFieldExpression>
            </textField>
        </band>
    </noData>
</jasperReport>