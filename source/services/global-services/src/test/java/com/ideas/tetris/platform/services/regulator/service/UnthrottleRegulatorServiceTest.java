package com.ideas.tetris.platform.services.regulator.service;

import com.ideas.g3.rule.SystemPropertiesExtension;
import com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.crudservice.RowMapper;
import com.ideas.tetris.platform.common.remoting.regulator.entities.RegulatorRequest;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.hamcrest.BaseMatcher;
import org.hamcrest.Description;
import org.hamcrest.Matcher;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.util.*;

import static com.ideas.tetris.platform.services.regulator.service.UnthrottleRegulatorService.DB_SERVER_NAME;
import static com.ideas.tetris.platform.services.regulator.service.UnthrottleRegulatorService.SAS_SERVER_NAME;
import static com.ideas.tetris.platform.services.regulator.service.UnthrottleRegulatorService.SERVICE_NAME;
import static java.util.Collections.singletonList;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.anyMap;
import static org.mockito.Mockito.anyObject;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoMoreInteractions;
import static org.mockito.Mockito.when;

@SuppressWarnings("unchecked")
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class UnthrottleRegulatorServiceTest {

    @RegisterExtension
    public SystemPropertiesExtension systemPropertiesExtension = new SystemPropertiesExtension();

    @Mock
    CrudService crudService;

    @Mock
    RegulatorMessageProducer messageProducer;

    @Mock
    CompleteRegulatorService completeRegulatorService;

    @InjectMocks
    UnthrottleRegulatorService unthrottleRegulatorService;

    @Mock
    PacmanConfigParamsService configService;

    private static final String NEW_FIND_NEXT_HIGHEST_PRIORITY_REQUEST_TO_RELEASE_WITH_NOLOCK = "select top 1 Request_ID, Property_ID from [Regulator_Request] with(nolock) where [Request_ID] not in (:requestIds) and status_id in (6) order by priority desc, event_date, CreateDate";
    private static final String NEW_FIND_NEXT_SERVICE_NAME_REQUEST_TO_RELEASE_WITH_DB_SERVER_WITH_NOLOCK = "select top 1 Request_ID, Property_ID from [Regulator_Request] with(nolock) where [Request_ID] not in (:requestIds) and status_id in (6) and [DB_Server_Name] = :dbServerName and [Service_Name] in (:serviceNames) order by priority desc, event_date, CreateDate";
    private static final String NEW_FIND_NEXT_DB_SERVER_REQUEST_TO_RELEASE_WITH_NOLOCK = "select top 1 Request_ID, Property_ID from [Regulator_Request] with(nolock) where [Request_ID] not in (:requestIds) and status_id in (6) and [Service_Name] = :serviceName and [DB_Server_Name] IN (SELECT\n" +
            "\tDISTINCT [DB_Server_Name]\n" +
            "FROM (\n" +
            "\t\tSELECT [DB_Server_Name], CASE WHEN [Status_ID] = 2 THEN 'Running' ELSE 'Throttled' END AS STATUS, COUNT(*) AS COUNT\n" +
            "\t\tFROM [Regulator_Request] with(nolock) WHERE [Service_Name] in (:dbServiceNames) \n" +
            "\t\tAND Ignore_In_Throttler = 0 \n" +
            "\t\tAND [Status_ID] IN (2,6) \n" +
            "\t\tGROUP BY [DB_Server_Name], [Status_ID] \n" +
            "\t) regulatorStatuses \n" +
            "PIVOT ( \n" +
            "\tAVG(COUNT) FOR Status IN (Running,Throttled) \n" +
            ") dbServersWithAvailability \n" +
            "WHERE RUNNING IS NULL OR RUNNING < :dbThrottleCount) order by priority desc, event_date, CreateDate";
    private static final String NEW_FIND_NEXT_SAS_SERVER_REQUEST_TO_RELEASE_WITH_DB_SERVER_WITH_NOLOCK = "select top 1 Request_ID, Property_ID from [Regulator_Request] with(nolock) where [Request_ID] not in (:requestIds) and status_id in (6) and [SAS_Server_Name] = :sasServerName and [Service_Name] in (:serviceNames) and [DB_Server_Name] IN (SELECT\n" +
            "\tDISTINCT [DB_Server_Name]\n" +
            "FROM (\n" +
            "\t\tSELECT [DB_Server_Name], CASE WHEN [Status_ID] = 2 THEN 'Running' ELSE 'Throttled' END AS STATUS, COUNT(*) AS COUNT\n" +
            "\t\tFROM [Regulator_Request] with(nolock) WHERE [Service_Name] in (:dbServiceNames) \n" +
            "\t\tAND Ignore_In_Throttler = 0 \n" +
            "\t\tAND [Status_ID] IN (2,6) \n" +
            "\t\tGROUP BY [DB_Server_Name], [Status_ID] \n" +
            "\t) regulatorStatuses \n" +
            "PIVOT ( \n" +
            "\tAVG(COUNT) FOR Status IN (Running,Throttled) \n" +
            ") dbServersWithAvailability \n" +
            "WHERE RUNNING IS NULL OR RUNNING < :dbThrottleCount) order by priority desc, event_date, CreateDate";

    private static final String NEW_FIND_NEXT_DB_SERVER_REQUEST_TO_RELEASE_WITH_SAS_SERVER_WITH_NOLOCK = "select top 1 Request_ID, Property_ID from [Regulator_Request] with(nolock) where [Request_ID] not in (:requestIds) and status_id in (6) and [SAS_Server_Name] IN (SELECT \n" +
            "\tDISTINCT [SAS_Server_Name] \n" +
            "FROM ( \n" +
            "\t\tSELECT [SAS_Server_Name], CASE WHEN [Status_ID] = 2 THEN 'Running' ELSE 'Throttled' END AS STATUS, COUNT(*) AS COUNT \n" +
            "\t\tFROM [Regulator_Request] with(nolock) WHERE [Service_Name] in (:sasServiceNames) \n" +
            "\t\tAND Ignore_In_Throttler = 0 \n" +
            "\t\tAND [Status_ID] IN (2,6) \n" +
            "\t\tGROUP BY [SAS_Server_Name], [Status_ID] \n" +
            "\t) regulatorStatuses \n" +
            "PIVOT ( \n" +
            "\tAVG(COUNT) FOR Status IN (Running,Throttled) \n" +
            ") sasServersWithAvailability \n" +
            "WHERE RUNNING IS NULL OR RUNNING < :sasThrottleCount) and [DB_Server_Name] = :dbServerName and [Service_Name] in (:serviceNames) order by priority desc, event_date, CreateDate";

    private static final String NEW_FIND_NEXT_SERVICE_NAME_REQUEST_TO_RELEASE_WITH_DB_AND_SAS_SERVER_WITH_NOLOCK = "select top 1 Request_ID, Property_ID from [Regulator_Request] with(nolock) where [Request_ID] not in (:requestIds) and status_id in (6) and [Service_Name] = :serviceName and [SAS_Server_Name] IN (SELECT \n" +
            "\tDISTINCT [SAS_Server_Name] \n" +
            "FROM ( \n" +
            "\t\tSELECT [SAS_Server_Name], CASE WHEN [Status_ID] = 2 THEN 'Running' ELSE 'Throttled' END AS STATUS, COUNT(*) AS COUNT \n" +
            "\t\tFROM [Regulator_Request] with(nolock) WHERE [Service_Name] in (:sasServiceNames) \n" +
            "\t\tAND Ignore_In_Throttler = 0 \n" +
            "\t\tAND [Status_ID] IN (2,6) \n" +
            "\t\tGROUP BY [SAS_Server_Name], [Status_ID] \n" +
            "\t) regulatorStatuses \n" +
            "PIVOT ( \n" +
            "\tAVG(COUNT) FOR Status IN (Running,Throttled) \n" +
            ") sasServersWithAvailability \n" +
            "WHERE RUNNING IS NULL OR RUNNING < :sasThrottleCount) and [DB_Server_Name] IN (SELECT\n" +
            "\tDISTINCT [DB_Server_Name]\n" +
            "FROM (\n" +
            "\t\tSELECT [DB_Server_Name], CASE WHEN [Status_ID] = 2 THEN 'Running' ELSE 'Throttled' END AS STATUS, COUNT(*) AS COUNT\n" +
            "\t\tFROM [Regulator_Request] with(nolock) WHERE [Service_Name] in (:dbServiceNames) \n" +
            "\t\tAND Ignore_In_Throttler = 0 \n" +
            "\t\tAND [Status_ID] IN (2,6) \n" +
            "\t\tGROUP BY [DB_Server_Name], [Status_ID] \n" +
            "\t) regulatorStatuses \n" +
            "PIVOT ( \n" +
            "\tAVG(COUNT) FOR Status IN (Running,Throttled) \n" +
            ") dbServersWithAvailability \n" +
            "WHERE RUNNING IS NULL OR RUNNING < :dbThrottleCount) order by priority desc, event_date, CreateDate";

    private static final String NEW_FIND_NEXT_SERVICE_NAME_REQUEST_TO_RELEASE_WITH_NOLOCK = "select top 1 Request_ID, Property_ID from [Regulator_Request] with(nolock) where [Request_ID] not in (:requestIds) and status_id in (6) and [Service_Name] = :serviceName order by priority desc, event_date, CreateDate";

    private static final String NEW_FIND_NEXT_SAS_SERVER_REQUEST_TO_RELEASE_WITH_NOLOCK = "select top 1 Request_ID, Property_ID from [Regulator_Request] with(nolock) where [Request_ID] not in (:requestIds) and status_id in (6) and [SAS_Server_Name] = :sasServerName and [Service_Name] in (:serviceNames) order by priority desc, event_date, CreateDate";

    private static final String NEW_FIND_NEXT_SERVICE_NAME_REQUEST_TO_RELEASE_WITH_SAS_SERVER_WITH_NOLOCK = "select top 1 Request_ID, Property_ID from [Regulator_Request] with(nolock) where [Request_ID] not in (:requestIds) and status_id in (6) and [Service_Name] = :serviceName and [SAS_Server_Name] IN (SELECT \n" +
            "\tDISTINCT [SAS_Server_Name] \n" +
            "FROM ( \n" +
            "\t\tSELECT [SAS_Server_Name], CASE WHEN [Status_ID] = 2 THEN 'Running' ELSE 'Throttled' END AS STATUS, COUNT(*) AS COUNT \n" +
            "\t\tFROM [Regulator_Request] with(nolock) WHERE [Service_Name] in (:sasServiceNames) \n" +
            "\t\tAND Ignore_In_Throttler = 0 \n" +
            "\t\tAND [Status_ID] IN (2,6) \n" +
            "\t\tGROUP BY [SAS_Server_Name], [Status_ID] \n" +
            "\t) regulatorStatuses \n" +
            "PIVOT ( \n" +
            "\tAVG(COUNT) FOR Status IN (Running,Throttled) \n" +
            ") sasServersWithAvailability \n" +
            "WHERE RUNNING IS NULL OR RUNNING < :sasThrottleCount) order by priority desc, event_date, CreateDate";

    private static final String NEW_FIND_NEXT_SERVICE_NAME_REQUEST_TO_RELEASE_WITH_APP_DB_AND_SAS_SERVER_WITH_NOLOCK = "select top 1 Request_ID, Property_ID from [Regulator_Request] with(nolock) where [Request_ID] not in (:requestIds) and status_id in (6) and [Service_Name] = :serviceName and [SAS_Server_Name] IN (SELECT \n" +
            "\tDISTINCT [SAS_Server_Name] \n" +
            "FROM ( \n" +
            "\t\tSELECT [SAS_Server_Name], CASE WHEN [Status_ID] = 2 THEN 'Running' ELSE 'Throttled' END AS STATUS, COUNT(*) AS COUNT \n" +
            "\t\tFROM [Regulator_Request] with(nolock) WHERE [Service_Name] in (:sasServiceNames) \n" +
            "\t\tAND Ignore_In_Throttler = 0 \n" +
            "\t\tAND [Status_ID] IN (2,6) \n" +
            "\t\tGROUP BY [SAS_Server_Name], [Status_ID] \n" +
            "\t) regulatorStatuses \n" +
            "PIVOT ( \n" +
            "\tAVG(COUNT) FOR Status IN (Running,Throttled) \n" +
            ") sasServersWithAvailability \n" +
            "WHERE RUNNING IS NULL OR RUNNING < :sasThrottleCount) and [DB_Server_Name] IN (SELECT\n" +
            "\tDISTINCT [DB_Server_Name]\n" +
            "FROM (\n" +
            "\t\tSELECT [DB_Server_Name], CASE WHEN [Status_ID] = 2 THEN 'Running' ELSE 'Throttled' END AS STATUS, COUNT(*) AS COUNT\n" +
            "\t\tFROM [Regulator_Request] with(nolock) WHERE [Service_Name] in (:dbServiceNames) \n" +
            "\t\tAND Ignore_In_Throttler = 0 \n" +
            "\t\tAND [Status_ID] IN (2,6) \n" +
            "\t\tGROUP BY [DB_Server_Name], [Status_ID] \n" +
            "\t) regulatorStatuses \n" +
            "PIVOT ( \n" +
            "\tAVG(COUNT) FOR Status IN (Running,Throttled) \n" +
            ") dbServersWithAvailability \n" +
            "WHERE RUNNING IS NULL OR RUNNING < :dbThrottleCount) and [App_Server_Name] IN (SELECT\n" +
            "\tDISTINCT [App_Server_Name]\n" +
            "FROM (\n" +
            "\t\tSELECT [App_Server_Name], CASE WHEN [Status_ID] = 2 THEN 'Running' ELSE 'Throttled' END AS STATUS, COUNT(*) AS COUNT\n" +
            "\t\tFROM [Regulator_Request] with(nolock) WHERE [Service_Name] = :serviceName AND [App_Server_Name] = :appServerName  \n" +
            "\t\tAND Ignore_In_Throttler = 0 \n" +
            "\t\tAND [Status_ID] IN (2,6) \n" +
            "\t\tGROUP BY [App_Server_Name], [Status_ID] \n" +
            "\t) regulatorStatuses \n" +
            "PIVOT ( \n" +
            "\tAVG(COUNT) FOR Status IN (Running,Throttled) \n" +
            ") appServersWithAvailability \n" +
            "WHERE RUNNING IS NULL OR RUNNING < :appThrottleCount) order by priority desc, event_date, CreateDate";

    private static final String NEW_FIND_NEXT_APP_DB_SERVER_REQUEST_TO_RELEASE_WITH_NOLOCK = "select top 1 Request_ID, Property_ID from [Regulator_Request] with(nolock) where [Request_ID] not in (:requestIds) and status_id in (6) and [Service_Name] = :serviceName and [DB_Server_Name] IN (SELECT\n" +
            "\tDISTINCT [DB_Server_Name]\n" +
            "FROM (\n" +
            "\t\tSELECT [DB_Server_Name], CASE WHEN [Status_ID] = 2 THEN 'Running' ELSE 'Throttled' END AS STATUS, COUNT(*) AS COUNT\n" +
            "\t\tFROM [Regulator_Request] with(nolock) WHERE [Service_Name] in (:dbServiceNames) \n" +
            "\t\tAND Ignore_In_Throttler = 0 \n" +
            "\t\tAND [Status_ID] IN (2,6) \n" +
            "\t\tGROUP BY [DB_Server_Name], [Status_ID] \n" +
            "\t) regulatorStatuses \n" +
            "PIVOT ( \n" +
            "\tAVG(COUNT) FOR Status IN (Running,Throttled) \n" +
            ") dbServersWithAvailability \n" +
            "WHERE RUNNING IS NULL OR RUNNING < :dbThrottleCount) and [App_Server_Name] IN (SELECT\n" +
            "\tDISTINCT [App_Server_Name]\n" +
            "FROM (\n" +
            "\t\tSELECT [App_Server_Name], CASE WHEN [Status_ID] = 2 THEN 'Running' ELSE 'Throttled' END AS STATUS, COUNT(*) AS COUNT\n" +
            "\t\tFROM [Regulator_Request] with(nolock) WHERE [Service_Name] = :serviceName AND [App_Server_Name] = :appServerName  \n" +
            "\t\tAND Ignore_In_Throttler = 0 \n" +
            "\t\tAND [Status_ID] IN (2,6) \n" +
            "\t\tGROUP BY [App_Server_Name], [Status_ID] \n" +
            "\t) regulatorStatuses \n" +
            "PIVOT ( \n" +
            "\tAVG(COUNT) FOR Status IN (Running,Throttled) \n" +
            ") appServersWithAvailability \n" +
            "WHERE RUNNING IS NULL OR RUNNING < :appThrottleCount) order by priority desc, event_date, CreateDate";

    private static final String NEW_FIND_NEXT_SERVICE_NAME_REQUEST_TO_RELEASE_WITH_APP_AND_SAS_SERVER_WITH_NOLOCK = "select top 1 Request_ID, Property_ID from [Regulator_Request] with(nolock) where [Request_ID] not in (:requestIds) and status_id in (6) and [Service_Name] = :serviceName and [SAS_Server_Name] IN (SELECT \n" +
            "\tDISTINCT [SAS_Server_Name] \n" +
            "FROM ( \n" +
            "\t\tSELECT [SAS_Server_Name], CASE WHEN [Status_ID] = 2 THEN 'Running' ELSE 'Throttled' END AS STATUS, COUNT(*) AS COUNT \n" +
            "\t\tFROM [Regulator_Request] with(nolock) WHERE [Service_Name] in (:sasServiceNames) \n" +
            "\t\tAND Ignore_In_Throttler = 0 \n" +
            "\t\tAND [Status_ID] IN (2,6) \n" +
            "\t\tGROUP BY [SAS_Server_Name], [Status_ID] \n" +
            "\t) regulatorStatuses \n" +
            "PIVOT ( \n" +
            "\tAVG(COUNT) FOR Status IN (Running,Throttled) \n" +
            ") sasServersWithAvailability \n" +
            "WHERE RUNNING IS NULL OR RUNNING < :sasThrottleCount) and [App_Server_Name] IN (SELECT\n" +
            "\tDISTINCT [App_Server_Name]\n" +
            "FROM (\n" +
            "\t\tSELECT [App_Server_Name], CASE WHEN [Status_ID] = 2 THEN 'Running' ELSE 'Throttled' END AS STATUS, COUNT(*) AS COUNT\n" +
            "\t\tFROM [Regulator_Request] with(nolock) WHERE [Service_Name] = :serviceName AND [App_Server_Name] = :appServerName  \n" +
            "\t\tAND Ignore_In_Throttler = 0 \n" +
            "\t\tAND [Status_ID] IN (2,6) \n" +
            "\t\tGROUP BY [App_Server_Name], [Status_ID] \n" +
            "\t) regulatorStatuses \n" +
            "PIVOT ( \n" +
            "\tAVG(COUNT) FOR Status IN (Running,Throttled) \n" +
            ") appServersWithAvailability \n" +
            "WHERE RUNNING IS NULL OR RUNNING < :appThrottleCount) order by priority desc, event_date, CreateDate";

    private static final String NEW_FIND_NEXT_SERVICE_NAME_WITH_APP_SERVER_REQUEST_TO_RELEASE_WITH_NOLOCK = "select top 1 Request_ID, Property_ID from [Regulator_Request] with(nolock) where [Request_ID] not in (:requestIds) and status_id in (6) and [Service_Name] = :serviceName and [App_Server_Name] IN (SELECT\n" +
            "\tDISTINCT [App_Server_Name]\n" +
            "FROM (\n" +
            "\t\tSELECT [App_Server_Name], CASE WHEN [Status_ID] = 2 THEN 'Running' ELSE 'Throttled' END AS STATUS, COUNT(*) AS COUNT\n" +
            "\t\tFROM [Regulator_Request] with(nolock) WHERE [Service_Name] = :serviceName AND [App_Server_Name] = :appServerName  \n" +
            "\t\tAND Ignore_In_Throttler = 0 \n" +
            "\t\tAND [Status_ID] IN (2,6) \n" +
            "\t\tGROUP BY [App_Server_Name], [Status_ID] \n" +
            "\t) regulatorStatuses \n" +
            "PIVOT ( \n" +
            "\tAVG(COUNT) FOR Status IN (Running,Throttled) \n" +
            ") appServersWithAvailability \n" +
            "WHERE RUNNING IS NULL OR RUNNING < :appThrottleCount) order by priority desc, event_date, CreateDate";

    @Test
    public void unthrottleRequestUsingAllUnthrottlingNonProcessCRSFileWithServerAvailability() {
        System.setProperty(Constants.REGULATOR_SERVICE_SAS_SERVER_THROTTLE, "10");
        System.setProperty(Constants.REGULATOR_SERVICE_SAS_SERVER_THROTTLE_JOBS, SERVICE_NAME);
        System.setProperty(Constants.REGULATOR_SERVICE_DB_SERVER_THROTTLE, "10");
        System.setProperty(Constants.REGULATOR_SERVICE_DB_SERVER_THROTTLE_JOBS, SERVICE_NAME);

        RegulatorRequest regulatorRequest = buildRegulatorRequest();

        UnthrottleRequest nextHighestRequest = new UnthrottleRequest(1, 9);
        UnthrottleRequest nextJobRequest = new UnthrottleRequest(2, 10);
        UnthrottleRequest nextDBRequest = new UnthrottleRequest(3, 11);
        UnthrottleRequest nextSASRequest = new UnthrottleRequest(4, 12);

        when(crudService.findByNamedQuerySingleResult(RegulatorRequest.FIND_BY_ID_WITH_NOLOCK, QueryParameter.with("id", regulatorRequest.getId()).parameters())).thenReturn(regulatorRequest);
        when(crudService.findByNativeQuerySingleResult(eq(NEW_FIND_NEXT_HIGHEST_PRIORITY_REQUEST_TO_RELEASE_WITH_NOLOCK), anyMap(), isA(RowMapper.class))).thenReturn(nextHighestRequest);
        when(crudService.findByNativeQuerySingleResult(eq(NEW_FIND_NEXT_SAS_SERVER_REQUEST_TO_RELEASE_WITH_DB_SERVER_WITH_NOLOCK), anyMap(), isA(RowMapper.class))).thenReturn(nextSASRequest);
        when(crudService.findByNativeQuerySingleResult(eq(NEW_FIND_NEXT_DB_SERVER_REQUEST_TO_RELEASE_WITH_SAS_SERVER_WITH_NOLOCK), anyMap(), isA(RowMapper.class))).thenReturn(nextDBRequest);
        when(crudService.findByNativeQuerySingleResult(eq(NEW_FIND_NEXT_SERVICE_NAME_REQUEST_TO_RELEASE_WITH_DB_AND_SAS_SERVER_WITH_NOLOCK), anyMap(), isA(RowMapper.class))).thenReturn(nextJobRequest);

        unthrottleRegulatorService.unthrottleRequest(regulatorRequest.getId());

        ArgumentCaptor<Map> parametersCaptor = ArgumentCaptor.forClass(Map.class);
        verify(crudService).findByNamedQuerySingleResult(RegulatorRequest.FIND_BY_ID_WITH_NOLOCK, QueryParameter.with("id", regulatorRequest.getId()).parameters());
        verify(crudService).findByNativeQuerySingleResult(eq(NEW_FIND_NEXT_HIGHEST_PRIORITY_REQUEST_TO_RELEASE_WITH_NOLOCK), parametersCaptor.capture(), anyObject());
        verify(crudService).findByNativeQuerySingleResult(eq(NEW_FIND_NEXT_SAS_SERVER_REQUEST_TO_RELEASE_WITH_DB_SERVER_WITH_NOLOCK), parametersCaptor.capture(), isA(RowMapper.class));
        verify(crudService).findByNativeQuerySingleResult(eq(NEW_FIND_NEXT_DB_SERVER_REQUEST_TO_RELEASE_WITH_SAS_SERVER_WITH_NOLOCK), parametersCaptor.capture(), isA(RowMapper.class));
        verify(crudService).findByNativeQuerySingleResult(eq(NEW_FIND_NEXT_SERVICE_NAME_REQUEST_TO_RELEASE_WITH_DB_AND_SAS_SERVER_WITH_NOLOCK), parametersCaptor.capture(), isA(RowMapper.class));

        verifyNoMoreInteractions(crudService);
        verify(messageProducer).sendUnblockRequestMessage(singletonList(nextHighestRequest.getRequestId()));
        verify(messageProducer).sendUnblockRequestMessage(singletonList(nextJobRequest.getRequestId()));
        verify(messageProducer).sendUnblockRequestMessage(singletonList(nextDBRequest.getRequestId()));
        verify(messageProducer).sendUnblockRequestMessage(singletonList(nextSASRequest.getRequestId()));
        verify(completeRegulatorService).unblockRequests(regulatorRequest);
        verifyNoMoreInteractions(messageProducer);
    }

    @Test
    public void unthrottleRequestUsingAllUnthrottlingNonProcessCRSFileWithSameRequestIdWithDBandSASServerAvailability() {
        System.setProperty(Constants.REGULATOR_SERVICE_SAS_SERVER_THROTTLE, "10");
        System.setProperty(Constants.REGULATOR_SERVICE_SAS_SERVER_THROTTLE_JOBS, SERVICE_NAME);
        System.setProperty(Constants.REGULATOR_SERVICE_DB_SERVER_THROTTLE, "10");
        System.setProperty(Constants.REGULATOR_SERVICE_DB_SERVER_THROTTLE_JOBS, SERVICE_NAME);

        RegulatorRequest regulatorRequest = buildRegulatorRequest();

        UnthrottleRequest nextHighestRequest = new UnthrottleRequest(1, 10);
        UnthrottleRequest nextJobRequest = new UnthrottleRequest(1, 10);
        UnthrottleRequest nextDBRequest = new UnthrottleRequest(1, 10);
        UnthrottleRequest nextSASRequest = new UnthrottleRequest(1, 10);

        when(crudService.findByNamedQuerySingleResult(RegulatorRequest.FIND_BY_ID_WITH_NOLOCK, QueryParameter.with("id", regulatorRequest.getId()).parameters())).thenReturn(regulatorRequest);
        when(crudService.findByNativeQuerySingleResult(eq(NEW_FIND_NEXT_HIGHEST_PRIORITY_REQUEST_TO_RELEASE_WITH_NOLOCK), anyMap(), isA(RowMapper.class))).thenReturn(nextHighestRequest);
        when(crudService.findByNativeQuerySingleResult(eq(NEW_FIND_NEXT_SAS_SERVER_REQUEST_TO_RELEASE_WITH_DB_SERVER_WITH_NOLOCK), anyMap(), isA(RowMapper.class))).thenReturn(nextSASRequest);
        when(crudService.findByNativeQuerySingleResult(eq(NEW_FIND_NEXT_DB_SERVER_REQUEST_TO_RELEASE_WITH_SAS_SERVER_WITH_NOLOCK), anyMap(), isA(RowMapper.class))).thenReturn(nextDBRequest);
        when(crudService.findByNativeQuerySingleResult(eq(NEW_FIND_NEXT_SERVICE_NAME_REQUEST_TO_RELEASE_WITH_DB_AND_SAS_SERVER_WITH_NOLOCK), anyMap(), isA(RowMapper.class))).thenReturn(nextJobRequest);

        unthrottleRegulatorService.unthrottleRequest(regulatorRequest.getId());

        verify(crudService).findByNamedQuerySingleResult(RegulatorRequest.FIND_BY_ID_WITH_NOLOCK, QueryParameter.with("id", regulatorRequest.getId()).parameters());
        ArgumentCaptor<Map> parameterCaptor = ArgumentCaptor.forClass(Map.class);
        verify(crudService).findByNativeQuerySingleResult(eq(NEW_FIND_NEXT_HIGHEST_PRIORITY_REQUEST_TO_RELEASE_WITH_NOLOCK), parameterCaptor.capture(), isA(RowMapper.class));
        verify(crudService).findByNativeQuerySingleResult(eq(NEW_FIND_NEXT_SAS_SERVER_REQUEST_TO_RELEASE_WITH_DB_SERVER_WITH_NOLOCK), parameterCaptor.capture(), isA(RowMapper.class));
        verify(crudService).findByNativeQuerySingleResult(eq(NEW_FIND_NEXT_DB_SERVER_REQUEST_TO_RELEASE_WITH_SAS_SERVER_WITH_NOLOCK), parameterCaptor.capture(), isA(RowMapper.class));
        verify(crudService).findByNativeQuerySingleResult(eq(NEW_FIND_NEXT_SERVICE_NAME_REQUEST_TO_RELEASE_WITH_DB_AND_SAS_SERVER_WITH_NOLOCK), parameterCaptor.capture(), isA(RowMapper.class));

        verifyNoMoreInteractions(crudService);
        verify(messageProducer).sendUnblockRequestMessage(singletonList(nextHighestRequest.getRequestId()));
        verify(completeRegulatorService).unblockRequests(regulatorRequest);
        verifyNoMoreInteractions(messageProducer);
    }

    @Test
    public void unthrottleRequestUsingAllUnthrottlingNonProcessCRSFileWithSameRequestIdWithAllServerAvailability() {
        System.setProperty(Constants.REGULATOR_SERVICE_SAS_SERVER_THROTTLE, "10");
        System.setProperty(Constants.REGULATOR_SERVICE_SAS_SERVER_THROTTLE_JOBS, SERVICE_NAME);
        System.setProperty(Constants.REGULATOR_SERVICE_DB_SERVER_THROTTLE, "10");
        System.setProperty(Constants.REGULATOR_SERVICE_DB_SERVER_THROTTLE_JOBS, SERVICE_NAME);
        System.setProperty(Constants.REGULATOR_SERVICE_APP_SERVER_THROTTLE + SERVICE_NAME, "10");

        RegulatorRequest regulatorRequest = buildRegulatorRequest();

        UnthrottleRequest nextHighestRequest = new UnthrottleRequest(1, 10);
        UnthrottleRequest nextJobRequest = new UnthrottleRequest(1, 10);
        UnthrottleRequest nextDBRequest = new UnthrottleRequest(1, 10);
        UnthrottleRequest nextSASRequest = new UnthrottleRequest(1, 10);

        when(crudService.findByNamedQuerySingleResult(RegulatorRequest.FIND_BY_ID_WITH_NOLOCK, QueryParameter.with("id", regulatorRequest.getId()).parameters())).thenReturn(regulatorRequest);
        when(crudService.findByNativeQuerySingleResult(eq(NEW_FIND_NEXT_HIGHEST_PRIORITY_REQUEST_TO_RELEASE_WITH_NOLOCK), anyMap(), isA(RowMapper.class))).thenReturn(nextHighestRequest);
        when(crudService.findByNativeQuerySingleResult(eq(NEW_FIND_NEXT_SERVICE_NAME_WITH_APP_SERVER_REQUEST_TO_RELEASE_WITH_NOLOCK), anyMap(), isA(RowMapper.class))).thenReturn(null);
        when(crudService.findByNativeQuerySingleResult(eq(NEW_FIND_NEXT_SAS_SERVER_REQUEST_TO_RELEASE_WITH_DB_SERVER_WITH_NOLOCK), anyMap(), isA(RowMapper.class))).thenReturn(nextSASRequest);
        when(crudService.findByNativeQuerySingleResult(eq(NEW_FIND_NEXT_DB_SERVER_REQUEST_TO_RELEASE_WITH_SAS_SERVER_WITH_NOLOCK), anyMap(), isA(RowMapper.class))).thenReturn(nextDBRequest);
        when(crudService.findByNativeQuerySingleResult(eq(NEW_FIND_NEXT_SERVICE_NAME_REQUEST_TO_RELEASE_WITH_APP_DB_AND_SAS_SERVER_WITH_NOLOCK), anyMap(), isA(RowMapper.class))).thenReturn(nextJobRequest);

        unthrottleRegulatorService.unthrottleRequest(regulatorRequest.getId());

        verify(crudService).findByNamedQuerySingleResult(RegulatorRequest.FIND_BY_ID_WITH_NOLOCK, QueryParameter.with("id", regulatorRequest.getId()).parameters());
        ArgumentCaptor<Map> parameterCaptor = ArgumentCaptor.forClass(Map.class);
        verify(crudService).findByNativeQuerySingleResult(eq(NEW_FIND_NEXT_HIGHEST_PRIORITY_REQUEST_TO_RELEASE_WITH_NOLOCK), parameterCaptor.capture(), isA(RowMapper.class));
        verify(crudService).findByNativeQuerySingleResult(eq(NEW_FIND_NEXT_SERVICE_NAME_WITH_APP_SERVER_REQUEST_TO_RELEASE_WITH_NOLOCK), anyMap(), isA(RowMapper.class));
        verify(crudService).findByNativeQuerySingleResult(eq(NEW_FIND_NEXT_SAS_SERVER_REQUEST_TO_RELEASE_WITH_DB_SERVER_WITH_NOLOCK), parameterCaptor.capture(), isA(RowMapper.class));
        verify(crudService).findByNativeQuerySingleResult(eq(NEW_FIND_NEXT_DB_SERVER_REQUEST_TO_RELEASE_WITH_SAS_SERVER_WITH_NOLOCK), parameterCaptor.capture(), isA(RowMapper.class));
        verify(crudService).findByNativeQuerySingleResult(eq(NEW_FIND_NEXT_SERVICE_NAME_REQUEST_TO_RELEASE_WITH_APP_DB_AND_SAS_SERVER_WITH_NOLOCK), parameterCaptor.capture(), isA(RowMapper.class));

        verifyNoMoreInteractions(crudService);
        verify(messageProducer).sendUnblockRequestMessage(singletonList(nextHighestRequest.getRequestId()));
        verify(completeRegulatorService).unblockRequests(regulatorRequest);
        verifyNoMoreInteractions(messageProducer);
    }

    @Test
    public void unthrottleRequestUsingHighestServiceNameAndDBThrottleNonProcessCRSFileWithServerAvailability() {
        System.clearProperty(Constants.REGULATOR_SERVICE_SAS_SERVER_THROTTLE);
        System.setProperty(Constants.REGULATOR_SERVICE_DB_SERVER_THROTTLE, "10");
        System.setProperty(Constants.REGULATOR_SERVICE_DB_SERVER_THROTTLE_JOBS, SERVICE_NAME);
        System.setProperty(Constants.REGULATOR_SERVICE_APP_SERVER_THROTTLE + SERVICE_NAME, "10");

        RegulatorRequest regulatorRequest = buildRegulatorRequest();

        UnthrottleRequest nextHighestRequest = new UnthrottleRequest(1, 9);
        UnthrottleRequest nextJobRequest = new UnthrottleRequest(2, 10);
        UnthrottleRequest nextDBRequest = new UnthrottleRequest(3, 11);

        when(crudService.findByNamedQuerySingleResult(RegulatorRequest.FIND_BY_ID_WITH_NOLOCK, QueryParameter.with("id", regulatorRequest.getId()).parameters())).thenReturn(regulatorRequest);
        when(crudService.findByNativeQuerySingleResult(eq(NEW_FIND_NEXT_HIGHEST_PRIORITY_REQUEST_TO_RELEASE_WITH_NOLOCK), anyMap(), isA(RowMapper.class))).thenReturn(nextHighestRequest);
        when(crudService.findByNativeQuerySingleResult(eq(NEW_FIND_NEXT_SERVICE_NAME_WITH_APP_SERVER_REQUEST_TO_RELEASE_WITH_NOLOCK), anyMap(), isA(RowMapper.class))).thenReturn(null);
        when(crudService.findByNativeQuerySingleResult(eq(NEW_FIND_NEXT_SERVICE_NAME_REQUEST_TO_RELEASE_WITH_DB_SERVER_WITH_NOLOCK), anyMap(), isA(RowMapper.class))).thenReturn(nextJobRequest);
        when(crudService.findByNativeQuerySingleResult(eq(NEW_FIND_NEXT_APP_DB_SERVER_REQUEST_TO_RELEASE_WITH_NOLOCK), anyMap(), isA(RowMapper.class))).thenReturn(nextDBRequest);

        unthrottleRegulatorService.unthrottleRequest(regulatorRequest.getId());

        ArgumentCaptor<Map> parametersCaptor = ArgumentCaptor.forClass(Map.class);
        verify(crudService).findByNamedQuerySingleResult(RegulatorRequest.FIND_BY_ID_WITH_NOLOCK, QueryParameter.with("id", regulatorRequest.getId()).parameters());
        verify(crudService).findByNativeQuerySingleResult(eq(NEW_FIND_NEXT_HIGHEST_PRIORITY_REQUEST_TO_RELEASE_WITH_NOLOCK), parametersCaptor.capture(), isA(RowMapper.class));
        verify(crudService).findByNativeQuerySingleResult(eq(NEW_FIND_NEXT_SERVICE_NAME_WITH_APP_SERVER_REQUEST_TO_RELEASE_WITH_NOLOCK), anyMap(), isA(RowMapper.class));
        verify(crudService).findByNativeQuerySingleResult(eq(NEW_FIND_NEXT_SERVICE_NAME_REQUEST_TO_RELEASE_WITH_DB_SERVER_WITH_NOLOCK), parametersCaptor.capture(), isA(RowMapper.class));
        verify(crudService).findByNativeQuerySingleResult(eq(NEW_FIND_NEXT_APP_DB_SERVER_REQUEST_TO_RELEASE_WITH_NOLOCK), parametersCaptor.capture(), isA(RowMapper.class));

        verifyNoMoreInteractions(crudService);
        verify(messageProducer).sendUnblockRequestMessage(singletonList(nextHighestRequest.getRequestId()));
        verify(messageProducer).sendUnblockRequestMessage(singletonList(nextJobRequest.getRequestId()));
        verify(messageProducer).sendUnblockRequestMessage(singletonList(nextDBRequest.getRequestId()));
        verify(completeRegulatorService).unblockRequests(regulatorRequest);
        verifyNoMoreInteractions(messageProducer);

        System.setProperty("jems.regulator.unthrottleIndividual", "false");
        unthrottleRegulatorService.unthrottleRequest(regulatorRequest.getId());
        // verifying with 4 as total count of requests processed
        verify(messageProducer, times(4)).sendUnblockRequestMessage(any());

    }

    @Test
    public void unthrottleRequestUsingHighestServiceNameAndDBThrottleNonProcessCRSFileWithServerAvailability_WithLogging() {
        System.setProperty("g3.enable.unthrottle.request.logging", Boolean.toString(true));
        unthrottleRequestUsingHighestServiceNameAndDBThrottleNonProcessCRSFileWithServerAvailability();
    }

    @Test
    public void unthrottleRequestUsingHighestServiceNameAndDBThrottleNonProcessCRSFileWithAppAndDBServerAvailability() {
        System.clearProperty(Constants.REGULATOR_SERVICE_SAS_SERVER_THROTTLE);
        System.setProperty(Constants.REGULATOR_SERVICE_DB_SERVER_THROTTLE, "10");
        System.setProperty(Constants.REGULATOR_SERVICE_DB_SERVER_THROTTLE_JOBS, SERVICE_NAME);

        RegulatorRequest regulatorRequest = buildRegulatorRequest();

        UnthrottleRequest nextHighestRequest = new UnthrottleRequest(1, 9);
        UnthrottleRequest nextJobRequest = new UnthrottleRequest(2, 10);
        UnthrottleRequest nextDBRequest = new UnthrottleRequest(3, 11);

        when(crudService.findByNamedQuerySingleResult(RegulatorRequest.FIND_BY_ID_WITH_NOLOCK, QueryParameter.with("id", regulatorRequest.getId()).parameters())).thenReturn(regulatorRequest);
        when(crudService.findByNativeQuerySingleResult(eq(NEW_FIND_NEXT_HIGHEST_PRIORITY_REQUEST_TO_RELEASE_WITH_NOLOCK), anyMap(), isA(RowMapper.class))).thenReturn(nextHighestRequest);
        when(crudService.findByNativeQuerySingleResult(eq(NEW_FIND_NEXT_SERVICE_NAME_REQUEST_TO_RELEASE_WITH_DB_SERVER_WITH_NOLOCK), anyMap(), isA(RowMapper.class))).thenReturn(nextJobRequest);
        when(crudService.findByNativeQuerySingleResult(eq(NEW_FIND_NEXT_DB_SERVER_REQUEST_TO_RELEASE_WITH_NOLOCK), anyMap(), isA(RowMapper.class))).thenReturn(nextDBRequest);

        unthrottleRegulatorService.unthrottleRequest(regulatorRequest.getId());

        ArgumentCaptor<Map> parametersCaptor = ArgumentCaptor.forClass(Map.class);
        verify(crudService).findByNamedQuerySingleResult(RegulatorRequest.FIND_BY_ID_WITH_NOLOCK, QueryParameter.with("id", regulatorRequest.getId()).parameters());
        verify(crudService).findByNativeQuerySingleResult(eq(NEW_FIND_NEXT_HIGHEST_PRIORITY_REQUEST_TO_RELEASE_WITH_NOLOCK), parametersCaptor.capture(), isA(RowMapper.class));
        verify(crudService).findByNativeQuerySingleResult(eq(NEW_FIND_NEXT_SERVICE_NAME_REQUEST_TO_RELEASE_WITH_DB_SERVER_WITH_NOLOCK), parametersCaptor.capture(), isA(RowMapper.class));
        verify(crudService).findByNativeQuerySingleResult(eq(NEW_FIND_NEXT_DB_SERVER_REQUEST_TO_RELEASE_WITH_NOLOCK), parametersCaptor.capture(), isA(RowMapper.class));

        verifyNoMoreInteractions(crudService);
        verify(messageProducer).sendUnblockRequestMessage(singletonList(nextHighestRequest.getRequestId()));
        verify(messageProducer).sendUnblockRequestMessage(singletonList(nextJobRequest.getRequestId()));
        verify(messageProducer).sendUnblockRequestMessage(singletonList(nextDBRequest.getRequestId()));
        verify(completeRegulatorService).unblockRequests(regulatorRequest);
        verifyNoMoreInteractions(messageProducer);
    }

    @Test
    public void unthrottleRequestUsingHighestServiceNameAndDBThrottleNonProcessCRSFileForNonDBThrottleJob() {
        System.clearProperty(Constants.REGULATOR_SERVICE_SAS_SERVER_THROTTLE);
        System.setProperty(Constants.REGULATOR_SERVICE_DB_SERVER_THROTTLE, "10");
        System.setProperty(Constants.REGULATOR_SERVICE_DB_SERVER_THROTTLE_JOBS, "someOtherJob");

        RegulatorRequest regulatorRequest = buildRegulatorRequest();

        UnthrottleRequest nextHighestRequest = new UnthrottleRequest(1, 9);
        UnthrottleRequest nextJobRequest = new UnthrottleRequest(2, 10);

        ArgumentCaptor<Map> argumentCaptor = ArgumentCaptor.forClass(Map.class);
        when(crudService.findByNamedQuerySingleResult(RegulatorRequest.FIND_BY_ID_WITH_NOLOCK, QueryParameter.with("id", regulatorRequest.getId()).parameters())).thenReturn(regulatorRequest);
        when(crudService.findByNativeQuerySingleResult(eq(NEW_FIND_NEXT_HIGHEST_PRIORITY_REQUEST_TO_RELEASE_WITH_NOLOCK), argumentCaptor.capture(), isA(RowMapper.class))).thenReturn(nextHighestRequest);
        when(crudService.findByNativeQuerySingleResult(eq(NEW_FIND_NEXT_SERVICE_NAME_REQUEST_TO_RELEASE_WITH_NOLOCK), argumentCaptor.capture(), isA(RowMapper.class))).thenReturn(nextJobRequest);

        unthrottleRegulatorService.unthrottleRequest(regulatorRequest.getId());

        ArgumentCaptor<Map> parametersCaptor = ArgumentCaptor.forClass(Map.class);
        verify(crudService).findByNamedQuerySingleResult(RegulatorRequest.FIND_BY_ID_WITH_NOLOCK, QueryParameter.with("id", regulatorRequest.getId()).parameters());
        verify(crudService).findByNativeQuerySingleResult(eq(NEW_FIND_NEXT_HIGHEST_PRIORITY_REQUEST_TO_RELEASE_WITH_NOLOCK), parametersCaptor.capture(), isA(RowMapper.class));
        verify(crudService).findByNativeQuerySingleResult(eq(NEW_FIND_NEXT_SERVICE_NAME_REQUEST_TO_RELEASE_WITH_NOLOCK), parametersCaptor.capture(), isA(RowMapper.class));

        verifyNoMoreInteractions(crudService);
        verify(messageProducer).sendUnblockRequestMessage(singletonList(nextHighestRequest.getRequestId()));
        verify(messageProducer).sendUnblockRequestMessage(singletonList(nextJobRequest.getRequestId()));
        verify(completeRegulatorService).unblockRequests(regulatorRequest);
        verifyNoMoreInteractions(messageProducer);
    }

    @Test
    public void unthrottleRequestUsingHighestServiceNameAndSASThrottleNonProcessCRSFileWithAppServerAvailability() {
        System.setProperty(Constants.REGULATOR_SERVICE_SAS_SERVER_THROTTLE, "10");
        System.setProperty(Constants.REGULATOR_SERVICE_SAS_SERVER_THROTTLE_JOBS, SERVICE_NAME);
        System.clearProperty(Constants.REGULATOR_SERVICE_DB_SERVER_THROTTLE);
        System.setProperty(Constants.REGULATOR_SERVICE_APP_SERVER_THROTTLE + SERVICE_NAME, "10");

        RegulatorRequest regulatorRequest = buildRegulatorRequest();

        UnthrottleRequest nextHighestRequest = new UnthrottleRequest(1, 9);
        UnthrottleRequest nextJobRequest = new UnthrottleRequest(2, 10);
        UnthrottleRequest nextSASRequest = new UnthrottleRequest(4, 11);

        when(crudService.findByNamedQuerySingleResult(RegulatorRequest.FIND_BY_ID_WITH_NOLOCK, QueryParameter.with("id", regulatorRequest.getId()).parameters())).thenReturn(regulatorRequest);
        when(crudService.findByNativeQuerySingleResult(eq(NEW_FIND_NEXT_HIGHEST_PRIORITY_REQUEST_TO_RELEASE_WITH_NOLOCK), anyMap(), isA(RowMapper.class))).thenReturn(nextHighestRequest);
        when(crudService.findByNativeQuerySingleResult(eq(NEW_FIND_NEXT_SERVICE_NAME_WITH_APP_SERVER_REQUEST_TO_RELEASE_WITH_NOLOCK), anyMap(), isA(RowMapper.class))).thenReturn(null);
        when(crudService.findByNativeQuerySingleResult(eq(NEW_FIND_NEXT_SAS_SERVER_REQUEST_TO_RELEASE_WITH_NOLOCK), anyMap(), isA(RowMapper.class))).thenReturn(nextJobRequest);
        when(crudService.findByNativeQuerySingleResult(eq(NEW_FIND_NEXT_SERVICE_NAME_REQUEST_TO_RELEASE_WITH_APP_AND_SAS_SERVER_WITH_NOLOCK), anyMap(), isA(RowMapper.class))).thenReturn(nextSASRequest);

        unthrottleRegulatorService.unthrottleRequest(regulatorRequest.getId());

        verify(crudService).findByNamedQuerySingleResult(RegulatorRequest.FIND_BY_ID_WITH_NOLOCK, QueryParameter.with("id", regulatorRequest.getId()).parameters());
        ArgumentCaptor<Map> parametersCaptor = ArgumentCaptor.forClass(Map.class);
        verify(crudService).findByNativeQuerySingleResult(eq(NEW_FIND_NEXT_HIGHEST_PRIORITY_REQUEST_TO_RELEASE_WITH_NOLOCK), parametersCaptor.capture(), isA(RowMapper.class));
        verify(crudService).findByNativeQuerySingleResult(eq(NEW_FIND_NEXT_SERVICE_NAME_WITH_APP_SERVER_REQUEST_TO_RELEASE_WITH_NOLOCK), anyMap(), isA(RowMapper.class));
        verify(crudService).findByNativeQuerySingleResult(eq(NEW_FIND_NEXT_SAS_SERVER_REQUEST_TO_RELEASE_WITH_NOLOCK), parametersCaptor.capture(), isA(RowMapper.class));
        verify(crudService).findByNativeQuerySingleResult(eq(NEW_FIND_NEXT_SERVICE_NAME_REQUEST_TO_RELEASE_WITH_APP_AND_SAS_SERVER_WITH_NOLOCK), parametersCaptor.capture(), isA(RowMapper.class));

        verifyNoMoreInteractions(crudService);
        verify(messageProducer).sendUnblockRequestMessage(singletonList(nextHighestRequest.getRequestId()));
        verify(messageProducer).sendUnblockRequestMessage(singletonList(nextJobRequest.getRequestId()));
        verify(messageProducer).sendUnblockRequestMessage(singletonList(nextSASRequest.getRequestId()));
        verify(completeRegulatorService).unblockRequests(regulatorRequest);
        verifyNoMoreInteractions(messageProducer);
    }

    @Test
    public void unthrottleRequestUsingHighestServiceNameAndSASThrottleNonProcessCRSFileWithServerAvailability() {
        System.setProperty(Constants.REGULATOR_SERVICE_SAS_SERVER_THROTTLE, "10");
        System.setProperty(Constants.REGULATOR_SERVICE_SAS_SERVER_THROTTLE_JOBS, SERVICE_NAME);
        System.clearProperty(Constants.REGULATOR_SERVICE_DB_SERVER_THROTTLE);

        RegulatorRequest regulatorRequest = buildRegulatorRequest();

        UnthrottleRequest nextHighestRequest = new UnthrottleRequest(1, 9);
        UnthrottleRequest nextJobRequest = new UnthrottleRequest(2, 10);
        UnthrottleRequest nextSASRequest = new UnthrottleRequest(4, 11);

        when(crudService.findByNamedQuerySingleResult(RegulatorRequest.FIND_BY_ID_WITH_NOLOCK, QueryParameter.with("id", regulatorRequest.getId()).parameters())).thenReturn(regulatorRequest);
        when(crudService.findByNativeQuerySingleResult(eq(NEW_FIND_NEXT_HIGHEST_PRIORITY_REQUEST_TO_RELEASE_WITH_NOLOCK), anyMap(), isA(RowMapper.class))).thenReturn(nextHighestRequest);
        when(crudService.findByNativeQuerySingleResult(eq(NEW_FIND_NEXT_SAS_SERVER_REQUEST_TO_RELEASE_WITH_NOLOCK), anyMap(), isA(RowMapper.class))).thenReturn(nextJobRequest);
        when(crudService.findByNativeQuerySingleResult(eq(NEW_FIND_NEXT_SERVICE_NAME_REQUEST_TO_RELEASE_WITH_SAS_SERVER_WITH_NOLOCK), anyMap(), isA(RowMapper.class))).thenReturn(nextSASRequest);

        unthrottleRegulatorService.unthrottleRequest(regulatorRequest.getId());

        verify(crudService).findByNamedQuerySingleResult(RegulatorRequest.FIND_BY_ID_WITH_NOLOCK, QueryParameter.with("id", regulatorRequest.getId()).parameters());
        ArgumentCaptor<Map> parametersCaptor = ArgumentCaptor.forClass(Map.class);
        verify(crudService).findByNativeQuerySingleResult(eq(NEW_FIND_NEXT_HIGHEST_PRIORITY_REQUEST_TO_RELEASE_WITH_NOLOCK), parametersCaptor.capture(), isA(RowMapper.class));
        verify(crudService).findByNativeQuerySingleResult(eq(NEW_FIND_NEXT_SAS_SERVER_REQUEST_TO_RELEASE_WITH_NOLOCK), parametersCaptor.capture(), isA(RowMapper.class));
        verify(crudService).findByNativeQuerySingleResult(eq(NEW_FIND_NEXT_SERVICE_NAME_REQUEST_TO_RELEASE_WITH_SAS_SERVER_WITH_NOLOCK), parametersCaptor.capture(), isA(RowMapper.class));

        verifyNoMoreInteractions(crudService);
        verify(messageProducer).sendUnblockRequestMessage(singletonList(nextHighestRequest.getRequestId()));
        verify(messageProducer).sendUnblockRequestMessage(singletonList(nextJobRequest.getRequestId()));
        verify(messageProducer).sendUnblockRequestMessage(singletonList(nextSASRequest.getRequestId()));
        verify(completeRegulatorService).unblockRequests(regulatorRequest);
        verifyNoMoreInteractions(messageProducer);
    }

    @Test
    public void unthrottleRequestUsingHighestServiceNameAndAppAndSASThrottleNonProcessCRSFileForNonSASThrottleJob() {
        System.clearProperty(Constants.REGULATOR_SERVICE_DB_SERVER_THROTTLE);
        System.setProperty(Constants.REGULATOR_SERVICE_SAS_SERVER_THROTTLE, "10");
        System.setProperty(Constants.REGULATOR_SERVICE_SAS_SERVER_THROTTLE_JOBS, "someOtherJob");
        System.setProperty(Constants.REGULATOR_SERVICE_APP_SERVER_THROTTLE + SERVICE_NAME, "10");

        RegulatorRequest regulatorRequest = buildRegulatorRequest();

        UnthrottleRequest nextHighestRequest = new UnthrottleRequest(1, 9);
        UnthrottleRequest nextJobRequest = new UnthrottleRequest(2, 10);

        when(crudService.findByNamedQuerySingleResult(RegulatorRequest.FIND_BY_ID_WITH_NOLOCK, QueryParameter.with("id", regulatorRequest.getId()).parameters())).thenReturn(regulatorRequest);
        when(crudService.findByNativeQuerySingleResult(eq(NEW_FIND_NEXT_HIGHEST_PRIORITY_REQUEST_TO_RELEASE_WITH_NOLOCK), anyMap(), isA(RowMapper.class))).thenReturn(nextHighestRequest);
        when(crudService.findByNativeQuerySingleResult(eq(NEW_FIND_NEXT_SERVICE_NAME_WITH_APP_SERVER_REQUEST_TO_RELEASE_WITH_NOLOCK), anyMap(), isA(RowMapper.class))).thenReturn(null);
        when(crudService.findByNativeQuerySingleResult(eq(NEW_FIND_NEXT_SERVICE_NAME_WITH_APP_SERVER_REQUEST_TO_RELEASE_WITH_NOLOCK), anyMap(), isA(RowMapper.class))).thenReturn(nextJobRequest);

        unthrottleRegulatorService.unthrottleRequest(regulatorRequest.getId());

        ArgumentCaptor<Map> parametersCaptor = ArgumentCaptor.forClass(Map.class);
        verify(crudService).findByNamedQuerySingleResult(RegulatorRequest.FIND_BY_ID_WITH_NOLOCK, QueryParameter.with("id", regulatorRequest.getId()).parameters());
        verify(crudService).findByNativeQuerySingleResult(eq(NEW_FIND_NEXT_HIGHEST_PRIORITY_REQUEST_TO_RELEASE_WITH_NOLOCK), parametersCaptor.capture(), anyObject());
        verify(crudService, times(2)).findByNativeQuerySingleResult(eq(NEW_FIND_NEXT_SERVICE_NAME_WITH_APP_SERVER_REQUEST_TO_RELEASE_WITH_NOLOCK), parametersCaptor.capture(), anyObject());

        verifyNoMoreInteractions(crudService);
        verify(messageProducer).sendUnblockRequestMessage(singletonList(nextHighestRequest.getRequestId()));
        verify(messageProducer).sendUnblockRequestMessage(singletonList(nextJobRequest.getRequestId()));
        verify(completeRegulatorService).unblockRequests(regulatorRequest);
        verifyNoMoreInteractions(messageProducer);
    }

    @Test
    public void unthrottleRequestUsingHighestServiceNameAndSASThrottleNonProcessCRSFileForNonSASThrottleJob() {
        System.clearProperty(Constants.REGULATOR_SERVICE_DB_SERVER_THROTTLE);
        System.setProperty(Constants.REGULATOR_SERVICE_SAS_SERVER_THROTTLE, "10");
        System.setProperty(Constants.REGULATOR_SERVICE_SAS_SERVER_THROTTLE_JOBS, "someOtherJob");

        RegulatorRequest regulatorRequest = buildRegulatorRequest();

        UnthrottleRequest nextHighestRequest = new UnthrottleRequest(1, 9);
        UnthrottleRequest nextJobRequest = new UnthrottleRequest(2, 10);

        when(crudService.findByNamedQuerySingleResult(RegulatorRequest.FIND_BY_ID_WITH_NOLOCK, QueryParameter.with("id", regulatorRequest.getId()).parameters())).thenReturn(regulatorRequest);
        when(crudService.findByNativeQuerySingleResult(eq(NEW_FIND_NEXT_HIGHEST_PRIORITY_REQUEST_TO_RELEASE_WITH_NOLOCK), anyMap(), isA(RowMapper.class))).thenReturn(nextHighestRequest);
        when(crudService.findByNativeQuerySingleResult(eq(NEW_FIND_NEXT_SERVICE_NAME_REQUEST_TO_RELEASE_WITH_NOLOCK), anyMap(), isA(RowMapper.class))).thenReturn(nextJobRequest);

        unthrottleRegulatorService.unthrottleRequest(regulatorRequest.getId());

        ArgumentCaptor<Map> parametersCaptor = ArgumentCaptor.forClass(Map.class);
        verify(crudService).findByNamedQuerySingleResult(RegulatorRequest.FIND_BY_ID_WITH_NOLOCK, QueryParameter.with("id", regulatorRequest.getId()).parameters());
        verify(crudService).findByNativeQuerySingleResult(eq(NEW_FIND_NEXT_HIGHEST_PRIORITY_REQUEST_TO_RELEASE_WITH_NOLOCK), parametersCaptor.capture(), anyObject());
        verify(crudService).findByNativeQuerySingleResult(eq(NEW_FIND_NEXT_SERVICE_NAME_REQUEST_TO_RELEASE_WITH_NOLOCK), parametersCaptor.capture(), anyObject());

        verifyNoMoreInteractions(crudService);
        verify(messageProducer).sendUnblockRequestMessage(singletonList(nextHighestRequest.getRequestId()));
        verify(messageProducer).sendUnblockRequestMessage(singletonList(nextJobRequest.getRequestId()));
        verify(completeRegulatorService).unblockRequests(regulatorRequest);
        verifyNoMoreInteractions(messageProducer);
    }

    private RegulatorRequest buildRegulatorRequest() {
        RegulatorRequest regulatorRequest = new RegulatorRequest();
        regulatorRequest.setId(1);
        regulatorRequest.setDbServerName(DB_SERVER_NAME);
        regulatorRequest.setSasServerName(SAS_SERVER_NAME);
        regulatorRequest.setServiceName(SERVICE_NAME);
        return regulatorRequest;
    }

    private <T> Matcher<List<T>> listContains(final T... items) {
        List<T> expectedList = Arrays.asList(items);
        return new BaseMatcher<List<T>>() {
            @Override
            public boolean matches(Object o) {
                List<T> actualList;
                try {
                    actualList = (List<T>) o;
                } catch (ClassCastException e) {
                    return false;
                }
                Set<T> expectedSet = new HashSet<>(expectedList);
                Set<T> actualSet = new HashSet<>(actualList);
                return actualSet.equals(expectedSet);
            }

            @Override
            public void describeTo(Description description) {

            }
        };
    }

}