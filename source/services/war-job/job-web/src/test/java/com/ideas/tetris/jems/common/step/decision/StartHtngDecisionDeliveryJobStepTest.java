package com.ideas.tetris.jems.common.step.decision;

import com.ideas.g3.rule.SystemPropertiesExtension;
import com.ideas.tetris.jems.core.job.context.JobExecutionContextKey;
import com.ideas.tetris.jems.core.job.context.JobExecutionUtil;
import com.ideas.tetris.jems.core.step.tasklet.AbstractTaskletStep;
import com.ideas.tetris.jems.core.test.StepJupiterTest;
import com.ideas.tetris.jems.ngi.job.htng.NGIHtngDecisionDeliveryHelper;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.decisiontranslator.DecisionUtils;
import com.ideas.tetris.pacman.services.forcefulldecisions.entity.ForceFullDecisions;
import com.ideas.tetris.pacman.services.forcefulldecisions.service.ForceFullDecisionsService;
import com.ideas.tetris.pacman.services.outbounddecisiondelivery.ProcessOutboundDecisions;
import com.ideas.tetris.pacman.services.property.PropertyService;
import com.ideas.tetris.platform.common.entity.DecisionDeliveryType;
import com.ideas.tetris.platform.common.entity.DecisionDestination;
import com.ideas.tetris.platform.common.entity.HtngPartner;
import com.ideas.tetris.platform.common.job.JobName;
import com.ideas.tetris.platform.common.job.JobParameterKey;
import com.ideas.tetris.platform.services.Stage;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.batch.core.JobExecution;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.core.StepExecution;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.batch.test.MetaDataInstanceFactory;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.ideas.tetris.pacman.common.constants.Constants.RDS;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

class StartHtngDecisionDeliveryJobStepTest extends StepJupiterTest {

    @RegisterExtension
    public SystemPropertiesExtension systemPropertiesExtension = new SystemPropertiesExtension();

    @InjectMocks
    StartHtngDecisionDeliveryJobStep htngDecisionDeliveryJobStep;
    @Mock
    ProcessOutboundDecisions processOutboundDecisions;
    @Mock
    DecisionUtils decisionUtils;
    @Mock
    PacmanConfigParamsService pacmanConfigParamsService;
    @Mock
    NGIHtngDecisionDeliveryHelper ngiHtngDecisionDeliveryHelper;
    @Mock
    private ForceFullDecisionsService forceFullDecisionsService;
    @Mock
    private PropertyService propertyService;

    @Test
    void isStepAsync() {
        assertFalse(htngDecisionDeliveryJobStep.isStepAsync(stepExecution));
    }

    @Test
    void shouldExecuteStep() {
        PacmanWorkContextHelper.setWorkContext(super.getDefaultWorkContext());
        jobWorkContext.setPropertyStage(Stage.TWO_WAY.getCode());
        when(processOutboundDecisions.shouldStartHtngDecisionDeliveryJob(HtngPartner.SYNXIS.getConfig())).thenReturn(true);
        assertTrue(htngDecisionDeliveryJobStep.shouldExecuteStep(jobExecution, jobWorkContext).shouldExecute());
    }

    @Test
    void shouldNotExecuteStepStageOneWay() {
        jobWorkContext.setPropertyStage(Stage.ONE_WAY.getCode());
        when(processOutboundDecisions.shouldStartHtngDecisionDeliveryJob(HtngPartner.SYNXIS.getConfig())).thenReturn(true);
        assertFalse(htngDecisionDeliveryJobStep.shouldExecuteStep(jobExecution, jobWorkContext).shouldExecute());
    }

    @Test
    void shouldNotExecuteStep() {
        PacmanWorkContextHelper.setWorkContext(super.getDefaultWorkContext());
        jobWorkContext.setPropertyStage(Stage.TWO_WAY.getCode());
        when(processOutboundDecisions.shouldStartHtngDecisionDeliveryJob(HtngPartner.SYNXIS.getConfig())).thenReturn(false);
        assertFalse(htngDecisionDeliveryJobStep.shouldExecuteStep(jobExecution, jobWorkContext).shouldExecute());
    }

    @Test
    void shouldNotExecuteStepWithDecisionDeliveryServiceEnabledTrue() {
        PacmanWorkContextHelper.setWorkContext(super.getDefaultWorkContext());
        jobWorkContext.setPropertyStage(Stage.TWO_WAY.getCode());
        when(processOutboundDecisions.shouldStartHtngDecisionDeliveryJob(HtngPartner.SYNXIS.getConfig())).thenReturn(true);
        when(pacmanConfigParamsService.getBooleanParameterValue("pacman.integration.synxis.decisionDeliveryServiceEnabled", CLIENT_CODE, PROP_CODE)).thenReturn(true);
        assertFalse(htngDecisionDeliveryJobStep.shouldExecuteStep(jobExecution, jobWorkContext).shouldExecute());
    }

    @Test
    void shouldExecuteStepWithDecisionDeliveryServiceEnabledTrue_WithMultipleExternalSystemDecisionEnabled() {
        PacmanWorkContextHelper.setWorkContext(super.getDefaultWorkContext());
        jobWorkContext.setPropertyStage(Stage.TWO_WAY.getCode());
        when(processOutboundDecisions.shouldStartHtngDecisionDeliveryJob(HtngPartner.SYNXIS.getConfig())).thenReturn(true);
        when(processOutboundDecisions.shouldStartHtngDecisionDeliveryJob(HtngPartner.HOTELCUBE.getConfig())).thenReturn(true);
        when(pacmanConfigParamsService.getBooleanParameterValue("pacman.integration.synxis.decisionDeliveryServiceEnabled", CLIENT_CODE, PROP_CODE)).thenReturn(true);
        assertTrue(htngDecisionDeliveryJobStep.shouldExecuteStep(jobExecution, jobWorkContext).shouldExecute());
    }

    @Test
    void shouldNotExecuteStepWithDecisionDeliveryServiceEnabledTrue_WithMultipleExternalSystemDecisionEnabled() {
        PacmanWorkContextHelper.setWorkContext(super.getDefaultWorkContext());
        jobWorkContext.setPropertyStage(Stage.TWO_WAY.getCode());
        when(processOutboundDecisions.shouldStartHtngDecisionDeliveryJob(HtngPartner.SYNXIS.getConfig())).thenReturn(true);
        when(processOutboundDecisions.shouldStartHtngDecisionDeliveryJob(HtngPartner.HOTELCUBE.getConfig())).thenReturn(true);
        when(pacmanConfigParamsService.getBooleanParameterValue("pacman.integration.synxis.decisionDeliveryServiceEnabled", CLIENT_CODE, PROP_CODE)).thenReturn(true);
        when(pacmanConfigParamsService.getBooleanParameterValue("pacman.integration.hotelcube.decisionDeliveryServiceEnabled", CLIENT_CODE, PROP_CODE)).thenReturn(true);
        assertFalse(htngDecisionDeliveryJobStep.shouldExecuteStep(jobExecution, jobWorkContext).shouldExecute());
    }


    @Test
    void doInvokeEvaluateConfiguredOutboundsDisabled() throws Exception {
        Map<DecisionDestination, List<DecisionDeliveryType>> configuredDecisionDestinations = new HashMap<>();
        ArrayList<DecisionDeliveryType> deliveryTypes = new ArrayList<>();
        deliveryTypes.add(DecisionDeliveryType.DAILY_BAR_BY_RATE_CODE);
        configuredDecisionDestinations.put(DecisionDestination.SYNXIS, deliveryTypes);
        List<HtngPartner> configuredHTNGPartners = new ArrayList<>();
        configuredHTNGPartners.add(HtngPartner.SYNXIS);
        JobExecutionUtil.setOnExecutionContext(stepExecution.getJobExecution(), JobExecutionContextKey.CONFIGURED_EXTERNAL_SYSTEMS, configuredDecisionDestinations);
        JobExecutionUtil.setOnExecutionContext(stepExecution.getJobExecution(),
                JobExecutionContextKey.INPUT_PROCESSING_ID, 1234);
        when(decisionUtils.getConfiguredHTNGPartners(configuredDecisionDestinations)).thenReturn(configuredHTNGPartners);
        htngDecisionDeliveryJobStep.doInvoke(stepExecution, jobWorkContext);
        verify(processOutboundDecisions).startHtngDecisionDeliveryJobIfConfigured(HtngPartner.SYNXIS.getConfig(), 1234, JobName.HtngDecisionDeliveryJob);
    }

    @Test
    void getConfiguredHtngOutbounds() {
        assertEquals(0, htngDecisionDeliveryJobStep.getConfiguredHtngOutbounds().size());

        System.setProperty("pacman.configured.outbounds", "SYNXIS");
        List<HtngPartner> htngPartners = htngDecisionDeliveryJobStep.getConfiguredHtngOutbounds();
        assertEquals(1, htngPartners.size());
        assertEquals(HtngPartner.SYNXIS, htngPartners.get(0));

        System.setProperty("pacman.configured.outbounds", "SYNXIS,SITEMINDER");
        htngPartners = htngDecisionDeliveryJobStep.getConfiguredHtngOutbounds();
        assertEquals(2, htngPartners.size());
        assertEquals(HtngPartner.SYNXIS, htngPartners.get(0));
        assertEquals(HtngPartner.SITEMINDER, htngPartners.get(1));
    }

    @Test
    void shouldExecuteStepWithConfiguredHtngOutbound() {
        jobWorkContext.setPropertyStage(Stage.TWO_WAY.getCode());
        System.setProperty("pacman.configured.outbounds", "SYNXIS");

        assertTrue(htngDecisionDeliveryJobStep.shouldExecuteStep(jobExecution, jobWorkContext).shouldExecute());
        verifyNoInteractions(processOutboundDecisions);
    }

    @Test
    void shouldExecuteStepWithConfiguredHtngOutboundWithMultipleOutbounds() {
        jobWorkContext.setPropertyStage(Stage.TWO_WAY.getCode());
        System.setProperty("pacman.configured.outbounds", "SYNXIS,SITEMINDER");

        assertTrue(htngDecisionDeliveryJobStep.shouldExecuteStep(jobExecution, jobWorkContext).shouldExecute());
        verifyNoInteractions(processOutboundDecisions);
    }

    @Test
    void doInvokeWithConfiguredHtngOutbound() throws Exception {
        System.setProperty("pacman.configured.outbounds", "SYNXIS");

        JobExecutionUtil.setOnExecutionContext(stepExecution.getJobExecution(), JobExecutionContextKey.INPUT_PROCESSING_ID, 1234);
        htngDecisionDeliveryJobStep.doInvoke(stepExecution, jobWorkContext);
        verify(processOutboundDecisions).startHtngDecisionDeliveryJobIfConfigured(HtngPartner.SYNXIS.getConfig(), 1234, JobName.HtngDecisionDeliveryJob);
    }

    @Test
    void doInvokeWithTwoConfiguredHtngOutbound() throws Exception {
        System.setProperty("pacman.configured.outbounds", "SYNXIS,SITEMINDER");

        JobExecutionUtil.setOnExecutionContext(stepExecution.getJobExecution(), JobExecutionContextKey.INPUT_PROCESSING_ID, 1234);
        htngDecisionDeliveryJobStep.doInvoke(stepExecution, jobWorkContext);
        verify(processOutboundDecisions).startHtngDecisionDeliveryJobIfConfigured(HtngPartner.SYNXIS.getConfig(), 1234, JobName.HtngDecisionDeliveryJob);
        verify(processOutboundDecisions).startHtngDecisionDeliveryJobIfConfigured(HtngPartner.SITEMINDER.getConfig(), 1234, JobName.HtngDecisionDeliveryJob);
    }

    @Test
    public void shouldSkipStepWhenNotPresentInSelectedOutbounds(){
        JobParameters selectedOutboundInJobParameters = jobParametersBuilder.addString(JobParameterKey.SELECTED_OUTBOUND_STEPS, "[StartRdsDecisionJobStep]").toJobParameters();
        JobExecution jobExecutionMock = Mockito.mock(JobExecution.class);
        when(jobExecutionMock.getJobParameters()).thenReturn(selectedOutboundInJobParameters);
        AbstractTaskletStep.ShouldExecuteStepResult shouldExecuteStepResult = htngDecisionDeliveryJobStep.shouldExecuteStep(jobExecutionMock, jobWorkContext);
        assertFalse(shouldExecuteStepResult.shouldExecute());
        assertEquals("StartHtngDecisionDeliveryJobStep is not in selected outbounds so skipping this step.", shouldExecuteStepResult.getMessage());
    }

    @Test
    public void shouldNotSkipStepWhenSelectedOutboundsNotPresentInParameters(){
        jobWorkContext.setPropertyStage(Stage.TWO_WAY.getCode());
        System.setProperty("pacman.configured.outbounds", "SYNXIS,SITEMINDER");
        JobParameters selectedOutboundInJobParameters = jobParametersBuilder.addString(JobParameterKey.PROPERTY_CODE, "0026").toJobParameters();
        JobExecution jobExecutionMock = Mockito.mock(JobExecution.class);
        when(jobExecutionMock.getJobParameters()).thenReturn(selectedOutboundInJobParameters);
        AbstractTaskletStep.ShouldExecuteStepResult shouldExecuteStepResult = htngDecisionDeliveryJobStep.shouldExecuteStep(jobExecutionMock, jobWorkContext);
        assertTrue(shouldExecuteStepResult.shouldExecute());
    }

    @Test
    public void shouldNotSkipStepWhenPresentInSelectedInOutboundsInParameters(){
        jobWorkContext.setPropertyStage(Stage.TWO_WAY.getCode());
        System.setProperty("pacman.configured.outbounds", "SYNXIS,SITEMINDER");
        JobParameters selectedOutboundInJobParameters = jobParametersBuilder.addString(JobParameterKey.SELECTED_OUTBOUND_STEPS, "[StartHtngDecisionDeliveryJobStep]").toJobParameters();
        JobExecution jobExecutionMock = Mockito.mock(JobExecution.class);
        when(jobExecutionMock.getJobParameters()).thenReturn(selectedOutboundInJobParameters);
        AbstractTaskletStep.ShouldExecuteStepResult shouldExecuteStepResult = htngDecisionDeliveryJobStep.shouldExecuteStep(jobExecutionMock, jobWorkContext);
        assertTrue(shouldExecuteStepResult.shouldExecute());
    }


    @Test
    void invokeOnlySelectedHTNGVendorsWhenImmediateOutbound() throws Exception {
        jobParametersBuilder.addString(JobParameterKey.SELECTED_OUTBOUND_STEPS, "[StartHtngDecisionDeliveryJobStep]");
        jobExecution = MetaDataInstanceFactory.createJobExecution("NGIAllDecisionDeliveryJob", 12L, 123L, jobParametersBuilder.toJobParameters());
        stepExecution = jobExecution.createStepExecution("StartHtngDecisionDeliveryJobStep");
        JobExecutionUtil.setOnExecutionContext(stepExecution.getJobExecution(), JobExecutionContextKey.INPUT_PROCESSING_ID, 1234);
        PacmanWorkContextHelper.setWorkContext(super.getDefaultWorkContext());
        PacmanWorkContextHelper.setPropertyId(5);
        PacmanWorkContextHelper.setClientId(2);
        jobWorkContext.setPropertyStage(Stage.TWO_WAY.getCode());

        when(forceFullDecisionsService.getForceFullDecisionsOfProperty(2, 5)).thenReturn(getHtngPartnersForDecisionDelivery());
        when(propertyService.isForceFullDecisions(5)).thenReturn(false);

        htngDecisionDeliveryJobStep.doInvoke(stepExecution, jobWorkContext);

        verify(forceFullDecisionsService, times(1)).getForceFullDecisionsOfProperty(2, 5);
        verify(processOutboundDecisions, times(5)).startHtngDecisionDeliveryJobIfConfigured(any(), any(), any());
    }

    @Test
    void invokeAllConfiguredHTNGVendorsWhenImmediateOutboundAndAllOutboundSelected() throws Exception {
        jobParametersBuilder.addString(JobParameterKey.SELECTED_OUTBOUND_STEPS, "[StartHtngDecisionDeliveryJobStep]");
        jobExecution = MetaDataInstanceFactory.createJobExecution("NGIAllDecisionDeliveryJob", 12L, 123L, jobParametersBuilder.toJobParameters());
        stepExecution = jobExecution.createStepExecution("StartHtngDecisionDeliveryJobStep");
        JobExecutionUtil.setOnExecutionContext(stepExecution.getJobExecution(), JobExecutionContextKey.INPUT_PROCESSING_ID, 1234);
        PacmanWorkContextHelper.setWorkContext(super.getDefaultWorkContext());
        PacmanWorkContextHelper.setPropertyId(5);
        PacmanWorkContextHelper.setClientId(2);
        jobWorkContext.setPropertyStage(Stage.TWO_WAY.getCode());

        when(propertyService.isForceFullDecisions(5)).thenReturn(true);

        htngDecisionDeliveryJobStep.doInvoke(stepExecution, jobWorkContext);

        verify(forceFullDecisionsService, times(0)).getForceFullDecisionsOfProperty(2, 5);
        verify(processOutboundDecisions, times(86)).startHtngDecisionDeliveryJobIfConfigured(any(), any(), any());
    }

    @Test
    void invokeAllConfiguredHTNGVendorsWhenLastKnownGoodDecisionAndAllOutboundSelected() throws Exception {
        jobParametersBuilder.addString(JobParameterKey.SELECTED_OUTBOUND_STEPS, "[StartHtngDecisionDeliveryJobStep]");
        jobExecution = MetaDataInstanceFactory.createJobExecution("LastGoodDecisionsDeliveryJob", 12L, 123L, jobParametersBuilder.toJobParameters());
        stepExecution = jobExecution.createStepExecution("StartHtngDecisionDeliveryJobStep");
        JobExecutionUtil.setOnExecutionContext(stepExecution.getJobExecution(), JobExecutionContextKey.INPUT_PROCESSING_ID, 1234);
        PacmanWorkContextHelper.setWorkContext(super.getDefaultWorkContext());
        PacmanWorkContextHelper.setPropertyId(5);
        PacmanWorkContextHelper.setClientId(2);
        jobWorkContext.setPropertyStage(Stage.TWO_WAY.getCode());

        htngDecisionDeliveryJobStep.doInvoke(stepExecution, jobWorkContext);

        verify(forceFullDecisionsService, times(0)).getForceFullDecisionsOfProperty(2, 5);
        verify(processOutboundDecisions, times(86)).startHtngDecisionDeliveryJobIfConfigured(any(), any(), any());
    }


    @Test
    void invokeAllConfiguredHTNGVendorsInDailyProcessing() throws Exception {
        JobExecutionUtil.setOnExecutionContext(stepExecution.getJobExecution(), JobExecutionContextKey.INPUT_PROCESSING_ID, 1234);
        when(forceFullDecisionsService.getForceFullDecisionsOfProperty(2, 5)).thenReturn(getHtngPartnersForDecisionDelivery());
        htngDecisionDeliveryJobStep.doInvoke(stepExecution, jobWorkContext);
        verify(forceFullDecisionsService, times(0)).getForceFullDecisionsOfProperty(2, 5);
        verify(processOutboundDecisions, times(86)).startHtngDecisionDeliveryJobIfConfigured(any(), any(), any());
    }

    private List<ForceFullDecisions> getHtngPartnersForDecisionDelivery() {
        List<ForceFullDecisions> decisions = new ArrayList<>();
        List<String> outbounds = List.of(HtngPartner.SYNXIS.getName(), HtngPartner.SYNXIS2.getName(), HtngPartner.SYNXIS3.getName(), HtngPartner.ANYHTNG.getName(), HtngPartner.PROTEL.getName(), RDS);
        outbounds.forEach( name -> {
            ForceFullDecisions ffd = new ForceFullDecisions();
            ffd.setOutboundName(name);
            decisions.add(ffd);
        });
        return decisions;
    }
}