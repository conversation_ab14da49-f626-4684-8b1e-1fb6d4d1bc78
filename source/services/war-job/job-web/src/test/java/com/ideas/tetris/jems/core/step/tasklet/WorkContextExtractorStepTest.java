package com.ideas.tetris.jems.core.step.tasklet;

import com.ideas.tetris.jems.core.job.context.JobExecutionContextKey;
import com.ideas.tetris.jems.core.job.context.JobExecutionUtil;
import com.ideas.tetris.jems.core.job.entity.JobInstanceWorkContext;
import com.ideas.tetris.jems.core.job.service.RelativeFilePathService;
import com.ideas.tetris.jems.core.repository.dao.JobInstanceWorkContextDao;
import com.ideas.tetris.jems.spring.aspect.jobstepcontext.beans.JobStepContextAwarePropertyService;
import com.ideas.tetris.pacman.services.datasourceswitching.DataSourceCacheBean;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import com.ideas.tetris.platform.services.daoandentities.entity.DBLoc;
import com.ideas.tetris.platform.services.daoandentities.entity.Property;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.batch.core.StepExecution;
import org.springframework.batch.repeat.RepeatStatus;
import org.springframework.batch.test.MetaDataInstanceFactory;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.when;

public class WorkContextExtractorStepTest {
    public static final String PROPERTY_CODE = "UPDATED";
    @Mock
    JobInstanceWorkContextDao jobInstanceWorkContextDao;
    @Mock
    PacmanConfigParamsService configService;
    @Mock
    JobStepContextAwarePropertyService propertyService;
    @Mock
    DataSourceCacheBean dataSourceCacheBean;
    @InjectMocks
    WorkContextExtractorStep workContextExtractorStep;
    JobInstanceWorkContext jobInstanceWorkContext;
    StepExecution stepExecution;
    @Mock
    private CrudService mockGlobalCrudService;

    @Mock
    private RelativeFilePathService relativeFilePathService;

    @BeforeEach
    public void setup() {
        stepExecution = MetaDataInstanceFactory.createStepExecution();

        jobInstanceWorkContext = new JobInstanceWorkContext();
        jobInstanceWorkContext.setJobInstanceId(JobExecutionUtil.getJobInstanceId(stepExecution.getJobExecution()));

        workContextExtractorStep = new WorkContextExtractorStep() {
            @Override
            public void updateJobInstanceWorkContext(StepExecution stepExecution, JobInstanceWorkContext jobInstanceWorkContext) {
                jobInstanceWorkContext.setPropertyCode(PROPERTY_CODE);
                jobInstanceWorkContext.setPropertyId(1);
            }
        };

        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testExecute() throws Exception {
        RepeatStatus repeatStatus = workContextExtractorStep.doExecute(stepExecution, jobInstanceWorkContext);
        assertEquals(RepeatStatus.FINISHED, repeatStatus);

        assertEquals(JobExecutionUtil.getJobInstanceId(stepExecution.getJobExecution()),
                jobInstanceWorkContext.getJobInstanceId());
        assertEquals(PROPERTY_CODE, jobInstanceWorkContext.getPropertyCode());

        Mockito.verify(jobInstanceWorkContextDao)
                .updateJobInstanceWorkContext(jobInstanceWorkContext);
    }

    @Test
    public void populateWorkContext_HappyPath() {
        Property property = new Property();
        property.setId(1);
        DBLoc dbLoc = new DBLoc();
        dbLoc.setId(10);
        property.setDbLocId(dbLoc.getId());
        when(dataSourceCacheBean.getDBLoc(property.getId())).thenReturn(dbLoc);
        workContextExtractorStep.populateWorkContext(stepExecution, jobInstanceWorkContext, null, null, 1, null, null, null, null);
        Integer dbLocId = (Integer) JobExecutionUtil.getFromExecutionContext(stepExecution.getJobExecution(), JobExecutionContextKey.DBLOC_ID);
        assertEquals(10, dbLocId.intValue());
    }

    @Test
    public void populateWorkContext_WithNullProperty() {
        validateDBLoc(null);
    }

    @Test
    public void populateWorkContext_WithNullDBLoc() {
        validateDBLoc(new Property());
    }

    private void validateDBLoc(Property property) {
        when(mockGlobalCrudService.find(Mockito.eq(Property.class), anyInt())).thenReturn(property);
        workContextExtractorStep.populateWorkContext(stepExecution, jobInstanceWorkContext, null, null, null, null, null, null, null);
        Integer dbLocId = (Integer) JobExecutionUtil.getFromExecutionContext(stepExecution.getJobExecution(), JobExecutionContextKey.DBLOC_ID);
        assertNull(dbLocId);
    }
}
