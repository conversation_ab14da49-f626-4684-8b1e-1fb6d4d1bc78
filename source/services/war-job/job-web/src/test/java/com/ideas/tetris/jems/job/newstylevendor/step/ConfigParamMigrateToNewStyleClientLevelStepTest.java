package com.ideas.tetris.jems.job.newstylevendor.step;

import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.configsparam.service.NewStyleVendorConfigService;
import com.ideas.tetris.platform.common.configparams.entities.ConfigParameter;
import com.ideas.tetris.platform.common.configparams.entities.ConfigParameterValue;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.job.JobParameterKey;
import com.ideas.tetris.platform.common.xml.schema.workcontext.v1.WorkContextType;
import com.ideas.tetris.platform.services.configparams.DataMigration;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.batch.core.JobExecution;
import org.springframework.batch.core.JobParameter;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.core.StepExecution;
import org.springframework.batch.item.ExecutionContext;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.ideas.g3.test.AbstractG3JupiterTest.inject;
import static com.ideas.tetris.platform.common.contextholder.PlatformWorkContextTestHelper.WC_CLIENT_CODE_BLACKSTONE;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class ConfigParamMigrateToNewStyleClientLevelStepTest {
    @InjectMocks
    private ConfigParamMigrateToNewStyleClientLevelStep step;
    @Mock
    private StepExecution stepExecution;
    @Mock
    private CrudService crudService;
    @Mock
    private DataMigration addOperation;
    @Mock
    private JobExecution jobExecution;
    @Mock
    private ExecutionContext executionContext;
    @Mock
    PacmanConfigParamsService pacmanConfigParamsService;

    @InjectMocks
    NewStyleVendorConfigService service = new NewStyleVendorConfigService();

    private final ConfigParamMigrateToNewStyleClientLevelStepTest When = this;
    private final String SYNXIX = "SYNXIS";
    private static final int PROPERTY_ID = 5;
    public static final String CLIENT_CODE = "ABC";

    @BeforeEach
    public void setUp() throws Exception {
        inject(step, "service", service);
        MockitoAnnotations.initMocks(this);
        PacmanWorkContextHelper.setWorkContext(CLIENT_CODE, PROPERTY_ID);
        when(stepExecution.getJobExecution()).thenReturn(jobExecution);
        when(jobExecution.getExecutionContext()).thenReturn(executionContext);
    }

    @Test
    public void shouldExecuteStepForValidClient() {
        When.configParamMigrationIsInvokedFor(WC_CLIENT_CODE_BLACKSTONE)
                .thenMigrationStepShouldExecute();
    }

    @Test
    public void shouldNotExecuteStepForInValidClient() {
        When.configParamMigrationIsInvokedFor(null)
                .thenMigrationStepShouldNotBeExecuted();
    }

    @Test
    public void shouldNotExecuteStepForValidProperty() {
        When.configParamMigrationIsInvokedForProperty()
                .thenMigrationStepShouldNotBeExecuted();
    }

    @Test
    public void shouldMigrateOldStyleParametersToNewStyleForGivenClient() throws Exception {
        List<Object> configParamValues = getSampleOldStyleConfigParamValues();
        When.configParamMigrationIsInvokedFor(WC_CLIENT_CODE_BLACKSTONE)
                .andExistingOldStyleParametersAre(configParamValues)
                .andMigrationIsRun()
                .thenAllOldStyleParametersShouldGetMigratedToNewStyle(configParamValues);
    }

    private void thenMigrationStepShouldNotBeExecuted() {
        Assertions.assertFalse(shouldExecuteMigrationStep());
    }

    private void thenMigrationStepShouldExecute() {
        Assertions.assertTrue(shouldExecuteMigrationStep());
    }

    private boolean shouldExecuteMigrationStep() {
        return step.shouldExecuteStep(jobExecution, null).shouldExecute();
    }

    private void thenAllOldStyleParametersShouldGetMigratedToNewStyle(List<Object> configParamValues) {
        configParamValues.forEach(configParameterValue -> verify(addOperation).modify((ConfigParameterValue) configParameterValue));
    }

    private ConfigParamMigrateToNewStyleClientLevelStepTest andMigrationIsRun() throws Exception {
        step.doInvoke(stepExecution, null);
        return this;
    }

    private ConfigParamMigrateToNewStyleClientLevelStepTest andExistingOldStyleParametersAre(List<Object> configParamValues) {
        when(crudService.findByNamedQuery(ConfigParameterValue.GET_OLD_STYLE_CONFIG_PARAMETER_VALUES_CLIENT_LEVEL,
                getQueryParameters())).thenReturn(configParamValues);
        return this;
    }

    private ConfigParamMigrateToNewStyleClientLevelStepTest configParamMigrationIsInvokedFor(String clientCode) {
        when(jobExecution.getJobParameters()).thenReturn(getJobParametersFor(clientCode));
        when(stepExecution.getJobParameters()).thenReturn(getJobParametersFor(clientCode));
        PacmanWorkContextHelper.setWorkContext(new WorkContextType());
        return this;
    }

    private ConfigParamMigrateToNewStyleClientLevelStepTest configParamMigrationIsInvokedForProperty() {
        final Map<String, JobParameter> jobParameterMap = getJobParameterMapFor(WC_CLIENT_CODE_BLACKSTONE);
        jobParameterMap.put(JobParameterKey.PROPERTY_CODE, new JobParameter(com.ideas.tetris.platform.common.contextholder.PlatformWorkContextTestHelper.WC_PROPERTY_CODE_PUNE));
        when(jobExecution.getJobParameters()).thenReturn(new JobParameters(jobParameterMap));
        PacmanWorkContextHelper.setWorkContext(new WorkContextType());
        return this;
    }

    private Map<String, Object> getQueryParameters() {
        return QueryParameter.with("client", WC_CLIENT_CODE_BLACKSTONE)
                .and("vendor", SYNXIX).parameters();
    }

    private List<Object> getSampleOldStyleConfigParamValues() {
        ConfigParameterValue configParameterValue_1 = new ConfigParameterValue();
        ConfigParameter configParameter_1 = new ConfigParameter();
        configParameter_1.setName("test_1");
        configParameterValue_1.setConfigParameter(configParameter_1);

        ConfigParameterValue configParameterValue_2 = new ConfigParameterValue();
        ConfigParameter configParameter_2 = new ConfigParameter();
        configParameter_2.setName("test_2");
        configParameterValue_2.setConfigParameter(configParameter_2);
        return Arrays.asList(configParameterValue_1, configParameterValue_2);
    }

    private JobParameters getJobParametersFor(String clientCode) {
        Map<String, JobParameter> parameterMap = getJobParameterMapFor(clientCode);
        return new JobParameters(parameterMap);
    }

    private Map<String, JobParameter> getJobParameterMapFor(String clientCode) {
        Map<String, JobParameter> parameterMap = new HashMap<>();
        parameterMap.put(JobParameterKey.CLIENT_CODE, new JobParameter(clientCode));
        parameterMap.put(JobParameterKey.EXTERNAL_SYSTEM, new JobParameter(SYNXIX));
        return parameterMap;
    }
}