package com.ideas.tetris.jems.job.inventorylimit;

import com.ideas.tetris.jems.core.job.context.JobExecutionContextKey;
import com.ideas.tetris.jems.core.job.entity.JobInstanceWorkContext;
import com.ideas.tetris.jems.core.step.tasklet.AbstractTaskletStep;
import com.ideas.tetris.pacman.services.inventorylimit.InventoryLimitDecisionService;
import com.ideas.tetris.pacman.services.inventorylimit.VirtualPropertyInventoryLimitDecisionService;
import com.ideas.tetris.pacman.services.property.PropertyService;
import com.ideas.tetris.platform.common.contextholder.PacmanWorkContextTestHelper;
import com.ideas.tetris.platform.services.Stage;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.batch.core.JobExecution;
import org.springframework.batch.core.StepExecution;
import org.springframework.batch.item.ExecutionContext;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class NGIPopulateInventoryLimitSplitRatioForVirtualPropertyStepTest {

    @InjectMocks
    private NGIPopulateInventoryLimitSplitRatioForVirtualPropertyStep splitHotelInventoryLimitRatioForVPStep;
    @Mock
    private InventoryLimitDecisionService inventoryLimitDecisionService;
    @Mock
    private PropertyService propertyService;
    @Mock
    private VirtualPropertyInventoryLimitDecisionService vpSplitHotelInventoryLimitDecisionByPhysicalPropertyService;
    @Mock
    private JobExecution jobExecution;
    @Mock
    private StepExecution stepExecution;
    @Mock
    private ExecutionContext executionContext;
    private JobInstanceWorkContext jobInstanceWorkContext;

    @BeforeEach
    public void setUp() {
        PacmanWorkContextTestHelper.setup_SSOUser_ParisProperty_WorkContext();
        jobInstanceWorkContext = new JobInstanceWorkContext();
    }

    @Test
    void stepShouldNotBeAsync() {
        assertFalse(splitHotelInventoryLimitRatioForVPStep.isStepAsync(stepExecution));
    }

    @Test
    void shouldNotExecuteStepWhenPropertyStageNotTwoWay() {
        jobInstanceWorkContext.setPropertyStage(Stage.ONE_WAY.getCode());

        AbstractTaskletStep.ShouldExecuteStepResult shouldExecuteStepResult = splitHotelInventoryLimitRatioForVPStep.shouldExecuteStep(jobExecution, jobInstanceWorkContext);

        assertFalse(shouldExecuteStepResult.shouldExecute());
        assertEquals("Property stage is not Two-Way", shouldExecuteStepResult.getMessage());
    }

    @Test
    void shouldNotExecuteStepWhenPropertyStageIsTwoWayAndDecisionUploadTypeNotConfigured() {
        jobInstanceWorkContext.setPropertyStage(Stage.TWO_WAY.getCode());
        when(jobExecution.getExecutionContext()).thenReturn(executionContext);
        when(executionContext.get(JobExecutionContextKey.EXTERNAL_SYSTEM)).thenReturn("Hilstar");
        when(inventoryLimitDecisionService.inventoryLimitDecisionUploadTypeConfigured("Hilstar")).thenReturn(false);
        when(propertyService.isPropertyVirtual()).thenReturn(true);

        AbstractTaskletStep.ShouldExecuteStepResult shouldExecuteStepResult = splitHotelInventoryLimitRatioForVPStep.shouldExecuteStep(jobExecution, jobInstanceWorkContext);

        assertFalse(shouldExecuteStepResult.shouldExecute());
        assertEquals("Inventory Limit Decision Upload Type is not Configured", shouldExecuteStepResult.getMessage());
        verify(executionContext).get(JobExecutionContextKey.EXTERNAL_SYSTEM);
        verify(inventoryLimitDecisionService).inventoryLimitDecisionUploadTypeConfigured("Hilstar");
    }

    @Test
    void shouldNotExecuteStepWhenPropertyIsNotVirtual() {
        jobInstanceWorkContext.setPropertyStage(Stage.TWO_WAY.getCode());
        when(propertyService.isPropertyVirtual()).thenReturn(false);

        AbstractTaskletStep.ShouldExecuteStepResult shouldExecuteStepResult = splitHotelInventoryLimitRatioForVPStep.shouldExecuteStep(jobExecution, jobInstanceWorkContext);

        assertFalse(shouldExecuteStepResult.shouldExecute());
        assertEquals("Property is not virtual", shouldExecuteStepResult.getMessage());
        verify(propertyService).isPropertyVirtual();
    }

    @Test
    void shouldExecuteStepWhenPropertyStageIsTwoWayAndDecisionUploadTypeConfigured() {
        jobInstanceWorkContext.setPropertyStage(Stage.TWO_WAY.getCode());
        when(jobExecution.getExecutionContext()).thenReturn(executionContext);
        when(executionContext.get(JobExecutionContextKey.EXTERNAL_SYSTEM)).thenReturn("Hilstar");
        when(inventoryLimitDecisionService.inventoryLimitDecisionUploadTypeConfigured("Hilstar")).thenReturn(true);
        when(propertyService.isPropertyVirtual()).thenReturn(true);

        AbstractTaskletStep.ShouldExecuteStepResult shouldExecuteStepResult = splitHotelInventoryLimitRatioForVPStep.shouldExecuteStep(jobExecution, jobInstanceWorkContext);

        assertTrue(shouldExecuteStepResult.shouldExecute());
        verify(inventoryLimitDecisionService).inventoryLimitDecisionUploadTypeConfigured("Hilstar");
    }

    @Test
    void shouldCreateInventoryLimitDecisionSplitRatioByPhysicalPropertyCode() throws Exception {
        when(executionContext.get(JobExecutionContextKey.OPERATION_TYPE)).thenReturn("BDE");
        when(jobExecution.getExecutionContext()).thenReturn(executionContext);
        when(stepExecution.getJobExecution()).thenReturn(jobExecution);

        splitHotelInventoryLimitRatioForVPStep.doInvoke(stepExecution, jobInstanceWorkContext);

        verify(vpSplitHotelInventoryLimitDecisionByPhysicalPropertyService).populateVPInventoryLimitPropertySplitRatio("BDE");
    }

}