package com.ideas.tetris.jems.common.step.componentroom;

import com.ideas.tetris.jems.core.job.context.JobExecutionContextKey;
import com.ideas.tetris.jems.core.job.entity.JobInstanceWorkContext;
import com.ideas.tetris.jems.core.step.tasklet.AbstractTaskletStep;
import com.ideas.tetris.jems.core.test.StepJupiterTest;
import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.FileMetadata;
import com.ideas.tetris.pacman.services.componentrooms.services.ComponentRoomService;
import com.ideas.tetris.pacman.services.componentrooms.services.RemainingCapacityCalculator;
import com.ideas.tetris.pacman.services.filemetadata.FileMetadataService;
import com.ideas.tetris.pacman.services.marketsegment.entity.ProcessStatus;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.batch.core.JobExecution;
import org.springframework.batch.core.StepExecution;
import org.springframework.batch.item.ExecutionContext;

import static org.mockito.ArgumentMatchers.nullable;
import static org.testng.AssertJUnit.assertFalse;

public class ComponentRoomCalculationStepTest extends StepJupiterTest {

    @Mock
    StepExecution stepExecution;
    @Mock
    JobExecution jobExecution;
    @Mock
    ExecutionContext executionContext;
    @Mock
    PacmanConfigParamsService pacmanConfigParamsService;

    @Mock
    RemainingCapacityCalculator remainingCapacityCalculator;

    @Mock
    FileMetadataService fileMetadataService;
    @Mock
    ComponentRoomService componentRoomService;

    @InjectMocks
    ComponentRoomCalculationStep componentRoomCalculationStep;

    private void setMocks() {
        Mockito.when(executionContext.get(Mockito.anyString())).thenReturn(1);
        Mockito.when(stepExecution.getJobExecution()).thenReturn(jobExecution);
        Mockito.when(jobExecution.getExecutionContext()).thenReturn(executionContext);
    }

    @Test
    public void testShouldInvoke() throws Exception {
        setMocks();
        PacmanWorkContextHelper.setWorkContext(super.getDefaultWorkContext());
        JobInstanceWorkContext jobInstanceWorkContext = new JobInstanceWorkContext();
        Mockito.when(executionContext.get(JobExecutionContextKey.OPERATION_TYPE)).thenReturn("BDE");
        componentRoomCalculationStep.doInvoke(stepExecution, jobInstanceWorkContext);
        Mockito.verify(remainingCapacityCalculator).calculateAndSaveOutOfOrder(true);
    }

    @Test
    public void testShouldInvokeForCDP() throws Exception {
        setMocks();
        Mockito.when(executionContext.get(JobExecutionContextKey.OPERATION_TYPE)).thenReturn("CDP");
        PacmanWorkContextHelper.setWorkContext(super.getDefaultWorkContext());
        JobInstanceWorkContext jobInstanceWorkContext = new JobInstanceWorkContext();
        componentRoomCalculationStep.doInvoke(stepExecution, jobInstanceWorkContext);
        Mockito.verify(remainingCapacityCalculator).calculateAndSaveOutOfOrder(false);
    }

    @Test
    public void testShouldAllowExecuteStep() throws Exception {
        setMocks();
        Mockito.when(executionContext.get(JobExecutionContextKey.OPERATION_TYPE)).thenReturn("CDP");
        PacmanWorkContextHelper.setWorkContext(super.getDefaultWorkContext());
        JobInstanceWorkContext jobInstanceWorkContext = new JobInstanceWorkContext();
        Mockito.when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.COMPONENT_ROOMS_ENABLED.value())).thenReturn(true);
        Mockito.when(componentRoomService.isComponentRoomEnabledWithValidConfiguration(nullable(Integer.class))).thenReturn(true);
        FileMetadata fm = new FileMetadata();
        fm.setProcessStatusId(ProcessStatus.IN_PROGRESS);
        Mockito.when(fileMetadataService.getFileMetadataById(Mockito.anyInt())).thenReturn(fm);
        AbstractTaskletStep.ShouldExecuteStepResult result = componentRoomCalculationStep.shouldExecuteStep(jobExecution, jobInstanceWorkContext);
        Assertions.assertEquals(true, result.shouldExecute());
    }

    @Test
    public void testShouldAllowExecuteIfFilemetadataIsNullStep() throws Exception {
        setMocks();
        Mockito.when(executionContext.get(JobExecutionContextKey.OPERATION_TYPE)).thenReturn("CDP");
        PacmanWorkContextHelper.setWorkContext(super.getDefaultWorkContext());
        JobInstanceWorkContext jobInstanceWorkContext = new JobInstanceWorkContext();
        Mockito.when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.COMPONENT_ROOMS_ENABLED.value())).thenReturn(true);
        Mockito.when(componentRoomService.isComponentRoomEnabledWithValidConfiguration(nullable(Integer.class))).thenReturn(true);
        Mockito.when(fileMetadataService.getFileMetadataById(Mockito.anyInt())).thenReturn(null);
        AbstractTaskletStep.ShouldExecuteStepResult result = componentRoomCalculationStep.shouldExecuteStep(jobExecution, jobInstanceWorkContext);
        Assertions.assertEquals(true, result.shouldExecute());
    }

    @Test
    public void testShouldNotAllowExecuteStepDueToAlreadySuccessfulSTatus() throws Exception {
        setMocks();
        Mockito.when(executionContext.get(JobExecutionContextKey.OPERATION_TYPE)).thenReturn("CDP");
        PacmanWorkContextHelper.setWorkContext(super.getDefaultWorkContext());
        JobInstanceWorkContext jobInstanceWorkContext = new JobInstanceWorkContext();
        Mockito.when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.COMPONENT_ROOMS_ENABLED.value())).thenReturn(true);
        Mockito.when(componentRoomService.isComponentRoomEnabledWithValidConfiguration(Mockito.anyInt())).thenReturn(true);
        FileMetadata fm = new FileMetadata();
        fm.setProcessStatusId(ProcessStatus.SUCCESSFUL);
        Mockito.when(fileMetadataService.getFileMetadataById(Mockito.anyInt())).thenReturn(fm);
        AbstractTaskletStep.ShouldExecuteStepResult result = componentRoomCalculationStep.shouldExecuteStep(jobExecution, jobInstanceWorkContext);
        Assertions.assertEquals(false, result.shouldExecute());
    }

    @Test
    public void testShouldNotAllowExecuteStepDueToInvalidConfiguration() throws Exception {
        setMocks();
        Mockito.when(executionContext.get(JobExecutionContextKey.OPERATION_TYPE)).thenReturn("BDE");
        PacmanWorkContextHelper.setWorkContext(super.getDefaultWorkContext());
        JobInstanceWorkContext jobInstanceWorkContext = new JobInstanceWorkContext();
        Mockito.when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.COMPONENT_ROOMS_ENABLED.value())).thenReturn(true);
        Mockito.when(componentRoomService.isComponentRoomEnabledWithValidConfiguration(Mockito.anyInt())).thenReturn(false);
        AbstractTaskletStep.ShouldExecuteStepResult result = componentRoomCalculationStep.shouldExecuteStep(jobExecution, jobInstanceWorkContext);
        assertFalse("Should not allow to execute step ", result.shouldExecute());
    }

    @Test
    public void testShouldNotAllowExecuteStepDueToLincesedIsNotEnabled() throws Exception {
        setMocks();
        Mockito.when(executionContext.get(JobExecutionContextKey.OPERATION_TYPE)).thenReturn("BDE");
        PacmanWorkContextHelper.setWorkContext(super.getDefaultWorkContext());
        JobInstanceWorkContext jobInstanceWorkContext = new JobInstanceWorkContext();
        Mockito.when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.COMPONENT_ROOMS_ENABLED.value())).thenReturn(false);
        Mockito.when(componentRoomService.isComponentRoomEnabledWithValidConfiguration(Mockito.anyInt())).thenReturn(true);
        AbstractTaskletStep.ShouldExecuteStepResult result = componentRoomCalculationStep.shouldExecuteStep(jobExecution, jobInstanceWorkContext);

        assertFalse("Should not allow to execute step ", result.shouldExecute());
    }
}