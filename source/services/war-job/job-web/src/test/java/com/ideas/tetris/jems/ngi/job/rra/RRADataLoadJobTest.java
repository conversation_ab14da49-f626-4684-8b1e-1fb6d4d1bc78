package com.ideas.tetris.jems.ngi.job.rra;

import com.ideas.tetris.jems.core.configuration.TestApplicationConfiguration;
import com.ideas.tetris.jems.core.step.AbstractStep;
import com.ideas.tetris.jems.core.test.JobJupiterTest;
import com.ideas.tetris.platform.common.job.JobParameterKey;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.context.support.AnnotationConfigContextLoader;

import org.springframework.beans.factory.annotation.Autowired;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

@ContextConfiguration(classes = TestApplicationConfiguration.class, loader = AnnotationConfigContextLoader.class)
@ExtendWith(SpringExtension.class)
public class RRADataLoadJobTest extends JobJupiterTest {

    private static final String DETERMINE_WORK_CONTEXT_FROM_PROPERTY_CODE_STEP = "determineWorkContextFromPropertyCodeStep";
    private static final String ACQUIRE_REGULATOR_REQUEST_STEP = "acquireRegulatorRequestStep";
    private static final String RRA_CREATE_FILEMETADATA_STEP = "RRACreateFileMetadataStep";
    private static final String RRA_CONFIG_DATALOAD_STEP = "RRAConfigDataLoadStep";
    private static final String RRA_SCORE_DATALOAD_STEP = "RRAScoreDataLoadStep";
    private static final String RRA_SET_FILEMETADATA_TO_SUCCESS_STEP = "RRASetFileMetadataToSuccessStep";
    private static final String COMPLETE_REGULATOR_REQUEST_STEP = "completeRegulatorRequestStep";
    public static final List<String> listOfStepNameInSequenceOfProcessing = Arrays.asList(DETERMINE_WORK_CONTEXT_FROM_PROPERTY_CODE_STEP, ACQUIRE_REGULATOR_REQUEST_STEP,
            RRA_CREATE_FILEMETADATA_STEP, RRA_CONFIG_DATALOAD_STEP
            , RRA_SCORE_DATALOAD_STEP, RRA_SET_FILEMETADATA_TO_SUCCESS_STEP, COMPLETE_REGULATOR_REQUEST_STEP);
    @Autowired
    RRADataLoadJob rraDataLoadJob;

    @BeforeEach
    public void setup() throws Exception {
        super.setup();
    }

    @Test
    public void testStepCount() {
        assertEquals(listOfStepNameInSequenceOfProcessing.size(), rraDataLoadJob.steps().size());
    }

    @Test
    public void testRequiredParameters() {
        Set<String> requiredParameters = new HashSet<String>(Arrays.asList(rraDataLoadJob.requiredParameters()));
        assertEquals(5, requiredParameters.size());
        assertTrue(requiredParameters.contains(JobParameterKey.PROPERTY_ID));
        assertTrue(requiredParameters.contains(JobParameterKey.CLIENT_CODE));
        assertTrue(requiredParameters.contains(JobParameterKey.PROPERTY_CODE));
        assertTrue(requiredParameters.contains(JobParameterKey.CORRELATION_ID));
    }

    @Test
    public void testStepPresentInSequenceOfProcessingWithMatchingBeanName() {
        List<AbstractStep> steps = rraDataLoadJob.steps();
        int position = 0;
        for (AbstractStep step : steps) {
            assertEquals(step.getBeanName(), listOfStepNameInSequenceOfProcessing.get(position), "Mismatch for position :" + position);
            position++;
        }
    }

}
