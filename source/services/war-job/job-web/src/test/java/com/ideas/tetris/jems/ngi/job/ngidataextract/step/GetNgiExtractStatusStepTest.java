package com.ideas.tetris.jems.ngi.job.ngidataextract.step;

import com.ideas.tetris.jems.core.step.tasklet.AbstractTaskletStep;
import com.ideas.tetris.jems.core.test.StepJupiterTest;
import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.services.monitoring.dailyProcessing.entity.PropertyDailyProcessing;
import com.ideas.tetris.pacman.services.ngi.extractstatus.NgiDataExtractService;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import com.ideas.tetris.platform.services.daoandentities.entity.Client;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.anyMap;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class GetNgiExtractStatusStepTest extends StepJupiterTest {

    public static final String CLIENT_CODE = "cc";
    @InjectMocks
    @Spy
    private GetNgiExtractStatusStep getNgiExtractStatusStep;
    @Mock
    private NgiDataExtractService ngiDataExtractService;

    @Mock
    private NgiExtractStatusReader ngiExtractStatusReader;

    @Mock
    private NgiDataExtractWriter ngiDataExtractWriter;

    @Mock
    private CrudService crudService;

    @Mock
    private PacmanConfigParamsService configParamsService;

    @Test
    public void itemReader() {
        assertEquals(ngiExtractStatusReader, getNgiExtractStatusStep.itemReader());
    }

    @Test
    public void itemWriter() {
        assertEquals(ngiDataExtractWriter, getNgiExtractStatusStep.itemWriter());
    }

    @Test
    public void chunkSize() {
        assertEquals(1, getNgiExtractStatusStep.chunkSize());
    }

    @Test
    public void shouldExecuteWhenClientDashboardDisabled() throws Exception {
        AbstractTaskletStep.ShouldExecuteStepResult shouldExecuteStepResult = getNgiExtractStatusStep.shouldExecuteStep(jobExecution, jobWorkContext);
        Assertions.assertFalse(shouldExecuteStepResult.shouldExecute());
    }

    @Test
    public void shouldExecuteWhenClientDashboardEnabled() {
        when(configParamsService.getParameterValueByClientLevel(FeatureTogglesConfigParamName.CLIENT_PROCESSING_DASHBOARD_ENABLED.getParameterName(), CLIENT_CODE)).thenReturn("true");
        when(crudService.findByNamedQuery(Client.GET_ACTIVE_CLIENTS)).thenReturn(getClientCodes());
        when(crudService.findByNativeQuery(eq(PropertyDailyProcessing.FETCH_EXTRACT_NOT_RECEIVED_PROPERTY_LIST), anyMap())).thenReturn(new ArrayList<>());
        AbstractTaskletStep.ShouldExecuteStepResult shouldExecuteStepResult = getNgiExtractStatusStep.shouldExecuteStep(jobExecution, jobWorkContext);
        Assertions.assertTrue(shouldExecuteStepResult.shouldExecute());
    }

    @Test
    public void shouldSkipWhenNoClientSubscribedClientDashboard() {
        when(crudService.findByNamedQuery(Client.GET_ACTIVE_CLIENTS)).thenReturn(getClientCodes());
        AbstractTaskletStep.ShouldExecuteStepResult shouldExecuteStepResult = getNgiExtractStatusStep.shouldExecuteStep(jobExecution, jobWorkContext);
        Assertions.assertFalse(shouldExecuteStepResult.shouldExecute());
    }

    private ArrayList<Object> getClientCodes() {
        ArrayList<Object> clients = new ArrayList<>();
        Client client = new Client();
        client.setCode(CLIENT_CODE);
        clients.add(client);
        return clients;
    }
}