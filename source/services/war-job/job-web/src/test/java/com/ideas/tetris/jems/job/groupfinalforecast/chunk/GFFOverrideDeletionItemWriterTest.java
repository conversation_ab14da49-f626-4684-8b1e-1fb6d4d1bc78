package com.ideas.tetris.jems.job.groupfinalforecast.chunk;

import com.ideas.g3.test.AbstractG3JupiterTest;
import com.ideas.tetris.jems.core.job.context.JobExecutionContextKey;
import com.ideas.tetris.jems.core.job.context.JobExecutionUtil;
import com.ideas.tetris.pacman.services.groupfinalforecast.GFFOverrideAtFgService;
import com.ideas.tetris.platform.services.daoandentities.entity.Property;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.batch.core.StepExecution;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.batch.test.MetaDataInstanceFactory;

import java.util.Arrays;
import java.util.List;

public class GFFOverrideDeletionItemWriterTest extends AbstractG3JupiterTest {

    @Mock
    private GFFOverrideAtFgService gffOverrideAtFgService;
    @InjectMocks
    private GFFOverrideDeletionItemWriter gffOverrideDeletionItemWriter;

    private StepExecution stepExecution;

    @BeforeEach
    public void setUp() {
        stepExecution = MetaDataInstanceFactory.createStepExecution();
        stepExecution.setExecutionContext(new ExecutionContext());
    }

    @Test
    public void shouldVerifyGffOverrideDeletionFlow() {
        List<Property> properties = Arrays.asList(
                createProperty(11001, "P1"),
                createProperty(11002, "P2")
        );

        gffOverrideDeletionItemWriter.doWrite(stepExecution, properties);

        Assertions.assertEquals(properties.get(0), JobExecutionUtil.getFromExecutionContext(stepExecution.getJobExecution(), JobExecutionContextKey.GFF_OVR_DELETION_FROM_PROPERTY));
    }

    private Property createProperty(Integer id, String code) {
        Property property = new Property(id);
        property.setCode(code);

        return property;
    }
}
