package com.ideas.tetris.jems.common.step.decision.htng;

import com.ideas.g3.integration.htng.decision.AgileRates;
import com.ideas.g3.integration.htng.dto.MappedRateCodeHolder;
import com.ideas.g3.integration.htng.service.decision.AgileRatesDecisionService;
import com.ideas.tetris.jems.core.job.context.JobExecutionUtil;
import com.ideas.tetris.jems.core.step.tasklet.AbstractTaskletStep;
import com.ideas.tetris.jems.core.test.StepJupiterTest;
import com.ideas.tetris.pacman.services.accommodation.rateCodeVendorMapping.RateCodeVendorMapping;
import com.ideas.tetris.pacman.services.accommodation.rateCodeVendorMapping.RateCodeVendorMappingService;
import com.ideas.tetris.pacman.services.agilerates.configuration.service.AgileRatesConfigurationService;
import com.ideas.tetris.pacman.services.product.Product;
import com.ideas.tetris.platform.common.job.JobParameterKey;
import com.ideas.tetris.platform.services.Stage;
import org.apache.commons.collections.CollectionUtils;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.batch.core.BatchStatus;
import org.springframework.batch.core.ExitStatus;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.ideas.tetris.jems.core.job.context.JobExecutionUtil.getFromExecutionContext;
import static com.ideas.tetris.jems.core.job.context.JobExecutionUtil.setOnExecutionContext;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.reset;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyZeroInteractions;
import static org.mockito.Mockito.when;

public class AgileRatesDeliveryLoopStepTest extends StepJupiterTest {

    private static final String EXTERNAL_SYSTEM = "theExternalSystem";
    private static final String EXTERNAL_SYSTEM_PARAMETER = "." + EXTERNAL_SYSTEM;
    private static final String RATE_CODE_LIST_CONTEXT_KEY = "agileRateCodeList";
    private static final String FAILED_RATE_CODES_CONTEXT_KEY = "failedAgileRateCodes";
    private static final String CURRENT_RATE_CODE_INDEX_CONTEXT_KEY = "currentRateCodeIndex";
    private static final String AGGREGATE_PARTNER_ERRORS_KEY = "aggregatePartnerErrors";
    private static final String SOAP_CURRENT_CHUNK_CONTEXT_KEY = "SOAP Current Chunk";
    private static final String CHUNKING_ENABLED_CONTEXT_KEY = "decisionChunkingEnabled";
    private static final String MAPPED_PRODUCT_CODES_CONTEXT_KEY = "mappedProductCodes";
    private static final String MAPPED_RATE_CODE_INDEX_CONTEXT_KEY = "mappedRateCodeIndex";

    @Mock
    private AgileRatesDecisionService agileRatesDecisionService;

    @Mock
    private AgileRatesConfigurationService agileRatesConfigurationService;

    @Mock
    private AgileRates agileRates;

    @Mock
    RateCodeVendorMappingService rateCodeVendorMappingService;

    @Mock
    private HtngDecisionDeliveryContextHelper contextHelper;

    @InjectMocks
    private AgileRatesDeliveryLoopStep step;

    private JobExecutionUtil jobExecutionUtil;

    @BeforeEach
    public void before() {
        MockitoAnnotations.initMocks(this);
        jobParametersBuilder.addString(JobParameterKey.EXTERNAL_SYSTEM_NAME, EXTERNAL_SYSTEM_PARAMETER);
        generateStepExecutionFromMetadata();
        when(contextHelper.getExternalSystemName(stepExecution.getJobExecution())).thenReturn(EXTERNAL_SYSTEM);
        when(contextHelper.getExternalSystemNameParameter(stepExecution.getJobExecution())).thenReturn(EXTERNAL_SYSTEM_PARAMETER);
        step.setBeanName(stepExecution.getStepName());
    }

    @AfterEach
    public void after() {
        reset(agileRatesDecisionService);
    }

    @Test
    public void shouldExecuteStep() {
        assertNull(getFromExecutionContext(jobExecution, RATE_CODE_LIST_CONTEXT_KEY));
        jobWorkContext.setPropertyStage(Stage.TWO_WAY.getCode());
        when(agileRatesDecisionService.agileRatesFeatureEnabled()).thenReturn(true);
        when(agileRatesDecisionService.agileRatesDecisionsEnabled(EXTERNAL_SYSTEM_PARAMETER)).thenReturn(true);

        Product one = getProduct("rc2", true);
        Product two = getProduct("rc3", true);
        Product inactive = getProduct("xxx", false);
        List<Product> rateCodeList = new ArrayList<>(Arrays.asList(one, two, inactive));
        when(agileRatesConfigurationService.findAgileRatesProducts()).thenReturn(rateCodeList);
        when(rateCodeVendorMappingService.getRateCodeNameVendorMappings(EXTERNAL_SYSTEM_PARAMETER, Arrays.asList("rc2", "rc3", "xxx")))
                .thenReturn(Collections.emptyList());

        AbstractTaskletStep.ShouldExecuteStepResult result = step.shouldExecuteStep(jobExecution, jobWorkContext);

        assertTrue(result.shouldExecute(), result.getMessage());
        assertFalse(jobExecutionUtil.isLoopComplete(stepExecution.getJobExecution(), stepExecution.getStepName()));

        List<String> rateCodes = (List<String>) getFromExecutionContext(jobExecution, RATE_CODE_LIST_CONTEXT_KEY);
        assertEquals(new ArrayList<>(Arrays.asList("rc2", "rc3")), rateCodes);
        assertFalse(rateCodes.contains("xxx"));
    }

    @Test
    public void shouldExecuteStep_MappedProduct() {
        assertNull(getFromExecutionContext(jobExecution, RATE_CODE_LIST_CONTEXT_KEY));
        jobWorkContext.setPropertyStage(Stage.TWO_WAY.getCode());
        when(agileRatesDecisionService.agileRatesFeatureEnabled()).thenReturn(true);
        when(agileRatesDecisionService.agileRatesDecisionsEnabled(EXTERNAL_SYSTEM_PARAMETER)).thenReturn(true);

        Product one = getProduct("rc2", true);
        Product two = getProduct("rc3", true);
        Product inactive = getProduct("xxx", false);
        List<Product> rateCodeList = new ArrayList<>(Arrays.asList(one, two, inactive));
        when(agileRatesConfigurationService.findAgileRatesProducts()).thenReturn(rateCodeList);
        when(rateCodeVendorMappingService.getRateCodeNameVendorMappings(EXTERNAL_SYSTEM_PARAMETER, Arrays.asList("rc2", "rc3", "xxx")))
                .thenReturn(getRateCodeVendorMappings(Arrays.asList("rc3")));

        AbstractTaskletStep.ShouldExecuteStepResult result = step.shouldExecuteStep(jobExecution, jobWorkContext);

        assertTrue(result.shouldExecute(), result.getMessage());
        assertFalse(jobExecutionUtil.isLoopComplete(stepExecution.getJobExecution(), stepExecution.getStepName()));

        List<String> rateCodes = (List<String>) getFromExecutionContext(jobExecution, RATE_CODE_LIST_CONTEXT_KEY);
        assertTrue(rateCodes.contains("rc3"));
        assertFalse(rateCodes.contains("rc2"));
        assertFalse(rateCodes.contains("xxx"));
    }

    @Test
    public void shouldExecuteStep_MappedProductNotActive() {
        assertNull(getFromExecutionContext(jobExecution, RATE_CODE_LIST_CONTEXT_KEY));
        jobWorkContext.setPropertyStage(Stage.TWO_WAY.getCode());
        when(agileRatesDecisionService.agileRatesFeatureEnabled()).thenReturn(true);
        when(agileRatesDecisionService.agileRatesDecisionsEnabled(EXTERNAL_SYSTEM_PARAMETER)).thenReturn(true);

        Product one = getProduct("rc2", true);
        Product two = getProduct("rc3", true);
        Product inactive = getProduct("xxx", false);
        List<Product> rateCodeList = new ArrayList<>(Arrays.asList(one, two, inactive));
        when(agileRatesConfigurationService.findAgileRatesProducts()).thenReturn(rateCodeList);
        when(rateCodeVendorMappingService.getRateCodeNameVendorMappings(EXTERNAL_SYSTEM_PARAMETER, Arrays.asList("rc2", "rc3", "xxx")))
                .thenReturn(getRateCodeVendorMappings(Arrays.asList("xxx")));

        AbstractTaskletStep.ShouldExecuteStepResult result = step.shouldExecuteStep(jobExecution, jobWorkContext);

        assertFalse(result.shouldExecute(), result.getMessage());
        assertTrue(jobExecutionUtil.isLoopComplete(jobExecution, stepExecution.getStepName()));
    }

    private Product getProduct(String name, boolean active) {
        Product product = new Product();
        product.setName(name);
        product.setActive(active);
        product.setUpload(true);
        return product;
    }

    @Test
    public void shouldExecuteStep_wrongStage() {
        assertNull(getFromExecutionContext(jobExecution, RATE_CODE_LIST_CONTEXT_KEY));
        jobWorkContext.setPropertyStage(Stage.DATA_CAPTURE.getCode());
        when(agileRatesDecisionService.agileRatesFeatureEnabled()).thenReturn(true);
        when(agileRatesDecisionService.agileRatesDecisionsEnabled(EXTERNAL_SYSTEM_PARAMETER)).thenReturn(true);

        Product one = new Product();
        one.setName("rc2");
        Product two = new Product();
        two.setName("rc3");
        List<Product> rateCodeList = new ArrayList<>(Arrays.asList(one, two));
        when(agileRatesConfigurationService.findAgileRatesProducts()).thenReturn(rateCodeList);

        AbstractTaskletStep.ShouldExecuteStepResult result = step.shouldExecuteStep(jobExecution, jobWorkContext);

        assertFalse(result.shouldExecute());
        assertTrue(jobExecutionUtil.isLoopComplete(jobExecution, stepExecution.getStepName()));

        assertEquals("Property stage is not TWO-WAY", result.getMessage());
        assertNull(getFromExecutionContext(jobExecution, RATE_CODE_LIST_CONTEXT_KEY));
    }

    @Test
    void shouldExecuteStep_featureNotEnabled() {
        assertNull(getFromExecutionContext(jobExecution, RATE_CODE_LIST_CONTEXT_KEY));
        jobWorkContext.setPropertyStage(Stage.TWO_WAY.getCode());
        when(agileRatesDecisionService.agileRatesFeatureEnabled()).thenReturn(false);
        when(agileRatesDecisionService.isSmallGroupProductEnabled()).thenReturn(false);
        when(agileRatesDecisionService.agileRatesDecisionsEnabled(EXTERNAL_SYSTEM_PARAMETER)).thenReturn(true);

        Product one = new Product();
        one.setName("rc2");
        Product two = new Product();
        two.setName("rc3");
        List<Product> rateCodeList = new ArrayList<>(Arrays.asList(one, two));
        when(agileRatesConfigurationService.findAgileRatesProducts()).thenReturn(rateCodeList);

        AbstractTaskletStep.ShouldExecuteStepResult result = step.shouldExecuteStep(jobExecution, jobWorkContext);

        assertFalse(result.shouldExecute());
        assertTrue(jobExecutionUtil.isLoopComplete(jobExecution, stepExecution.getStepName()));

        assertEquals("Agile Rates/Small Group feature is not enabled for this property.", result.getMessage());
        assertNull(getFromExecutionContext(jobExecution, RATE_CODE_LIST_CONTEXT_KEY));
    }

    @Test
    public void shouldExecuteStep_decisionsNotConfiguredOrNotAvailable() {
        assertNull(getFromExecutionContext(jobExecution, RATE_CODE_LIST_CONTEXT_KEY));
        jobWorkContext.setPropertyStage(Stage.TWO_WAY.getCode());
        when(agileRatesDecisionService.agileRatesFeatureEnabled()).thenReturn(true);
        when(agileRatesDecisionService.agileRatesDecisionsEnabled(EXTERNAL_SYSTEM_PARAMETER)).thenReturn(false);

        Product one = new Product();
        one.setName("rc2");
        Product two = new Product();
        two.setName("rc3");
        List<Product> rateCodeList = new ArrayList<>(Arrays.asList(one, two));
        when(agileRatesConfigurationService.findAgileRatesProducts()).thenReturn(rateCodeList);

        AbstractTaskletStep.ShouldExecuteStepResult result = step.shouldExecuteStep(jobExecution, jobWorkContext);

        assertFalse(result.shouldExecute());
        assertTrue(jobExecutionUtil.isLoopComplete(jobExecution, stepExecution.getStepName()));

        assertEquals("Agile Rates decisions are not enabled for this property.", result.getMessage());
        assertNull(getFromExecutionContext(jobExecution, RATE_CODE_LIST_CONTEXT_KEY));
    }

    @Test
    public void shouldExecuteStep_noActiveProductsForUpload() {
        assertNull(getFromExecutionContext(jobExecution, RATE_CODE_LIST_CONTEXT_KEY));
        jobWorkContext.setPropertyStage(Stage.TWO_WAY.getCode());
        when(agileRatesDecisionService.agileRatesFeatureEnabled()).thenReturn(true);
        when(agileRatesDecisionService.agileRatesDecisionsEnabled(EXTERNAL_SYSTEM_PARAMETER)).thenReturn(true);

        List<Product> rateCodeList = new ArrayList<>();
        when(agileRatesConfigurationService.findAgileRatesProducts()).thenReturn(rateCodeList);

        AbstractTaskletStep.ShouldExecuteStepResult result = step.shouldExecuteStep(jobExecution, jobWorkContext);

        assertFalse(result.shouldExecute());
        assertTrue(jobExecutionUtil.isLoopComplete(jobExecution, stepExecution.getStepName()));

        assertEquals("There are no active agile products configured for upload.", result.getMessage());
        assertNull(getFromExecutionContext(jobExecution, RATE_CODE_LIST_CONTEXT_KEY));
    }

    @Test
    public void shouldExecuteStep_rateCodeListNotNull() {
        List<String> rateCodeList = new ArrayList<>(Arrays.asList("rc2", "rc3"));
        setOnExecutionContext(jobExecution, RATE_CODE_LIST_CONTEXT_KEY, rateCodeList);

        AbstractTaskletStep.ShouldExecuteStepResult result = step.shouldExecuteStep(jobExecution, jobWorkContext);

        assertTrue(result.shouldExecute());
        assertFalse(jobExecutionUtil.isLoopComplete(stepExecution.getJobExecution(), stepExecution.getStepName()));

        assertEquals(rateCodeList, getFromExecutionContext(jobExecution, RATE_CODE_LIST_CONTEXT_KEY));
        verifyZeroInteractions(agileRatesDecisionService);
    }

    @Test
    public void shouldContinue_noIndexInContext_NoChunking_NoMapping() {
        List<String> rateCodes = new ArrayList<>(Arrays.asList("rc2"));
        List<String> rateCodeMappings = new ArrayList<>();

        setOnExecutionContext(jobExecution, RATE_CODE_LIST_CONTEXT_KEY, rateCodes);
        assertNull(getFromExecutionContext(jobExecution, CURRENT_RATE_CODE_INDEX_CONTEXT_KEY));
        setOnExecutionContext(jobExecution, FAILED_RATE_CODES_CONTEXT_KEY, new ArrayList<>());
        setOnExecutionContext(jobExecution, CHUNKING_ENABLED_CONTEXT_KEY, false);
        setOnExecutionContext(jobExecution, MAPPED_PRODUCT_CODES_CONTEXT_KEY, Collections.singletonList(rateCodeMappings));

        assertTrue(step.shouldContinue(jobExecution, stepExecution.getStepName()));

        assertEquals(0, getFromExecutionContext(jobExecution, CURRENT_RATE_CODE_INDEX_CONTEXT_KEY));
        assertEquals(rateCodes, getFromExecutionContext(jobExecution, RATE_CODE_LIST_CONTEXT_KEY));
        verifyZeroInteractions(agileRates);
        assertFalse((Boolean) getFromExecutionContext(jobExecution, CHUNKING_ENABLED_CONTEXT_KEY));
        assertNull(getFromExecutionContext(jobExecution, MAPPED_RATE_CODE_INDEX_CONTEXT_KEY));
    }

    @Test
    public void shouldContinue_noIndexInContext_ChunkingEnabled_NoMapping() {
        List<String> rateCodes = new ArrayList<>(Arrays.asList("rc2"));
        List<String> rateCodeMappings = new ArrayList<>();

        setOnExecutionContext(jobExecution, RATE_CODE_LIST_CONTEXT_KEY, rateCodes);
        assertNull(getFromExecutionContext(jobExecution, CURRENT_RATE_CODE_INDEX_CONTEXT_KEY));
        setOnExecutionContext(jobExecution, FAILED_RATE_CODES_CONTEXT_KEY, new ArrayList<>());
        setOnExecutionContext(jobExecution, CHUNKING_ENABLED_CONTEXT_KEY, true);
        setOnExecutionContext(jobExecution, MAPPED_PRODUCT_CODES_CONTEXT_KEY, Collections.singletonList(rateCodeMappings));

        assertTrue(step.shouldContinue(jobExecution, stepExecution.getStepName()));

        assertEquals(0, getFromExecutionContext(jobExecution, CURRENT_RATE_CODE_INDEX_CONTEXT_KEY));
        assertEquals(rateCodes, getFromExecutionContext(jobExecution, RATE_CODE_LIST_CONTEXT_KEY));
        verifyZeroInteractions(agileRates);
        assertTrue((Boolean) getFromExecutionContext(jobExecution, CHUNKING_ENABLED_CONTEXT_KEY));
        assertNull(getFromExecutionContext(jobExecution, MAPPED_RATE_CODE_INDEX_CONTEXT_KEY));
    }

    @Test
    public void shouldContinue_noIndexInContext_NoChunking_WithMapping() {
        List<String> rateCodes = new ArrayList<>(Arrays.asList("rc2"));
        setOnExecutionContext(jobExecution, RATE_CODE_LIST_CONTEXT_KEY, rateCodes);
        assertNull(getFromExecutionContext(jobExecution, CURRENT_RATE_CODE_INDEX_CONTEXT_KEY));
        setOnExecutionContext(jobExecution, FAILED_RATE_CODES_CONTEXT_KEY, new ArrayList<>());
        setOnExecutionContext(jobExecution, CHUNKING_ENABLED_CONTEXT_KEY, false);
        setOnExecutionContext(jobExecution, MAPPED_PRODUCT_CODES_CONTEXT_KEY, Collections.singletonList(
                Arrays.asList(
                        getMappedRateCodeHolder("MC1", null),
                        getMappedRateCodeHolder("MC2", null))));

        assertTrue(step.shouldContinue(jobExecution, stepExecution.getStepName()));

        assertEquals(0, getFromExecutionContext(jobExecution, CURRENT_RATE_CODE_INDEX_CONTEXT_KEY));
        assertEquals(rateCodes, getFromExecutionContext(jobExecution, RATE_CODE_LIST_CONTEXT_KEY));
        verifyZeroInteractions(agileRates);
        assertFalse((Boolean) getFromExecutionContext(jobExecution, CHUNKING_ENABLED_CONTEXT_KEY));
        assertEquals(0, getFromExecutionContext(jobExecution, MAPPED_RATE_CODE_INDEX_CONTEXT_KEY));
    }

    @Test
    public void shouldContinue_noIndexInContext_WithChunking_WithMapping() {
        List<String> rateCodes = new ArrayList<>(Arrays.asList("rc2"));
        setOnExecutionContext(jobExecution, RATE_CODE_LIST_CONTEXT_KEY, rateCodes);
        assertNull(getFromExecutionContext(jobExecution, CURRENT_RATE_CODE_INDEX_CONTEXT_KEY));
        setOnExecutionContext(jobExecution, FAILED_RATE_CODES_CONTEXT_KEY, new ArrayList<>());
        setOnExecutionContext(jobExecution, CHUNKING_ENABLED_CONTEXT_KEY, true);
        setOnExecutionContext(jobExecution, MAPPED_PRODUCT_CODES_CONTEXT_KEY, Collections.singletonList(
                Arrays.asList(
                        getMappedRateCodeHolder("MC1", null),
                        getMappedRateCodeHolder("MC2", null))));

        assertTrue(step.shouldContinue(jobExecution, stepExecution.getStepName()));

        assertEquals(0, getFromExecutionContext(jobExecution, CURRENT_RATE_CODE_INDEX_CONTEXT_KEY));
        assertEquals(rateCodes, getFromExecutionContext(jobExecution, RATE_CODE_LIST_CONTEXT_KEY));
        verifyZeroInteractions(agileRates);
        assertTrue((Boolean) getFromExecutionContext(jobExecution, CHUNKING_ENABLED_CONTEXT_KEY));
        assertEquals(0, getFromExecutionContext(jobExecution, MAPPED_RATE_CODE_INDEX_CONTEXT_KEY));
    }

    @Test
    public void shouldContinue_moreToDeliver() {
        List<String> rateCodes = new ArrayList<>(Arrays.asList("rc2", "rc3"));
        setOnExecutionContext(jobExecution, RATE_CODE_LIST_CONTEXT_KEY, rateCodes);
        setOnExecutionContext(jobExecution, CURRENT_RATE_CODE_INDEX_CONTEXT_KEY, 0);
        setOnExecutionContext(jobExecution, FAILED_RATE_CODES_CONTEXT_KEY, new ArrayList<>());

        assertTrue(step.shouldContinue(jobExecution, stepExecution.getStepName()));

        assertEquals(1, getFromExecutionContext(jobExecution, CURRENT_RATE_CODE_INDEX_CONTEXT_KEY));
        assertEquals(rateCodes, getFromExecutionContext(jobExecution, RATE_CODE_LIST_CONTEXT_KEY));
        verifyZeroInteractions(agileRates);
    }

    @Test
    public void shouldContinue_doneAndAllDelivered() {
        List<String> rateCodes = new ArrayList<>(Arrays.asList("rc2", "rc3"));
        setOnExecutionContext(jobExecution, RATE_CODE_LIST_CONTEXT_KEY, rateCodes);
        setOnExecutionContext(jobExecution, CURRENT_RATE_CODE_INDEX_CONTEXT_KEY, 1);
        setOnExecutionContext(jobExecution, FAILED_RATE_CODES_CONTEXT_KEY, new ArrayList<>());

        assertFalse(step.shouldContinue(jobExecution, stepExecution.getStepName()));

        assertEquals(2, getFromExecutionContext(jobExecution, CURRENT_RATE_CODE_INDEX_CONTEXT_KEY));
        assertEquals(rateCodes, getFromExecutionContext(jobExecution, RATE_CODE_LIST_CONTEXT_KEY));
        verify(agileRates).updateRecordForDecisionDeliveryTracking(EXTERNAL_SYSTEM, true);
    }

    @Test
    public void shouldContinue_doneButNotAllDelivered() {
        List<String> rateCodes = new ArrayList<>(Arrays.asList("rc2", "rc3"));
        List<String> failedRateCodes = new ArrayList<>(Collections.singletonList("rc3"));
        setOnExecutionContext(jobExecution, RATE_CODE_LIST_CONTEXT_KEY, rateCodes);
        setOnExecutionContext(jobExecution, CURRENT_RATE_CODE_INDEX_CONTEXT_KEY, 1);
        setOnExecutionContext(jobExecution, FAILED_RATE_CODES_CONTEXT_KEY, failedRateCodes);

        assertFalse(step.shouldContinue(jobExecution, stepExecution.getStepName()));

        assertEquals(2, getFromExecutionContext(jobExecution, CURRENT_RATE_CODE_INDEX_CONTEXT_KEY));
        assertEquals(rateCodes, getFromExecutionContext(jobExecution, RATE_CODE_LIST_CONTEXT_KEY));
        verify(agileRates).updateRecordForDecisionDeliveryTracking(EXTERNAL_SYSTEM, false);
    }

    @Test
    public void shouldContinue_Chunking_MoreChunksToDeliver() {
        when(contextHelper.isChunkingEnabled(stepExecution.getJobExecution())).thenReturn(true);

        List<String> rateCodes = new ArrayList<>(Arrays.asList("rc2", "rc3"));
        setOnExecutionContext(jobExecution, RATE_CODE_LIST_CONTEXT_KEY, rateCodes);
        setOnExecutionContext(jobExecution, CURRENT_RATE_CODE_INDEX_CONTEXT_KEY, 1);
        setOnExecutionContext(jobExecution, FAILED_RATE_CODES_CONTEXT_KEY, new ArrayList<>());
        when(contextHelper.getDecisionCurrentChunk(stepExecution.getJobExecution())).thenReturn(2);
        when(contextHelper.getDecisionTotalChunks(jobExecution)).thenReturn(5);
        when(contextHelper.getDecisionChunkSizeMap(jobExecution)).thenReturn(Collections.singletonMap(1, 1));

        assertTrue(step.shouldContinue(jobExecution, stepExecution.getStepName()));

        assertEquals(1, getFromExecutionContext(jobExecution, CURRENT_RATE_CODE_INDEX_CONTEXT_KEY));
        assertEquals(rateCodes, getFromExecutionContext(jobExecution, RATE_CODE_LIST_CONTEXT_KEY));
        verify(contextHelper).setDecisionCurrentChunk(stepExecution.getJobExecution(), 3);
        verifyZeroInteractions(agileRates);
    }

    @Test
    public void shouldContinue_Chunking_AllChunksDelivered() {
        when(contextHelper.isChunkingEnabled(stepExecution.getJobExecution())).thenReturn(true);

        List<String> rateCodes = new ArrayList<>(Arrays.asList("rc2", "rc3"));
        setOnExecutionContext(jobExecution, RATE_CODE_LIST_CONTEXT_KEY, rateCodes);
        setOnExecutionContext(jobExecution, CURRENT_RATE_CODE_INDEX_CONTEXT_KEY, 0);
        setOnExecutionContext(jobExecution, FAILED_RATE_CODES_CONTEXT_KEY, new ArrayList<>());
        when(contextHelper.getDecisionCurrentChunk(stepExecution.getJobExecution())).thenReturn(5);
        when(contextHelper.getDecisionTotalChunks(jobExecution)).thenReturn(5);
        when(contextHelper.getDecisionChunkSizeMap(jobExecution)).thenReturn(Collections.singletonMap(1, 1));

        assertTrue(step.shouldContinue(jobExecution, stepExecution.getStepName()));

        assertEquals(1, getFromExecutionContext(jobExecution, CURRENT_RATE_CODE_INDEX_CONTEXT_KEY));
        assertEquals(rateCodes, getFromExecutionContext(jobExecution, RATE_CODE_LIST_CONTEXT_KEY));
        verify(contextHelper).removeChunkingValuesFromContext(stepExecution.getJobExecution());
        verifyZeroInteractions(agileRates);
    }

    @Test
    public void isChunkedDeliveryComplete_True() {
        when(contextHelper.isChunkingEnabled(stepExecution.getJobExecution())).thenReturn(true);
        when(contextHelper.getDecisionCurrentChunk(stepExecution.getJobExecution())).thenReturn(5);
        when(contextHelper.getDecisionTotalChunks(jobExecution)).thenReturn(5);
        when(contextHelper.getDecisionChunkSizeMap(jobExecution)).thenReturn(Collections.singletonMap(1, 1));

        assertTrue(step.isChunkedDeliveryComplete(jobExecution));

        verify(contextHelper).removeChunkingValuesFromContext(stepExecution.getJobExecution());
    }

    @Test
    public void isChunkedDeliveryComplete_False() {
        when(contextHelper.isChunkingEnabled(stepExecution.getJobExecution())).thenReturn(true);
        when(contextHelper.getDecisionCurrentChunk(stepExecution.getJobExecution())).thenReturn(2);
        when(contextHelper.getDecisionTotalChunks(jobExecution)).thenReturn(5);
        when(contextHelper.getDecisionChunkSizeMap(jobExecution)).thenReturn(Collections.singletonMap(1, 1));

        assertFalse(step.isChunkedDeliveryComplete(jobExecution));

        verify(contextHelper, never()).removeChunkingValuesFromContext(stepExecution.getJobExecution());
    }

    @Test
    public void isChunkedDeliveryComplete_ChunkingNotEnabled() {
        when(contextHelper.isChunkingEnabled(stepExecution.getJobExecution())).thenReturn(false);
        assertTrue(step.isChunkedDeliveryComplete(jobExecution));
    }

    @Test
    public void afterStep_FailedRateCodes_WithChunking() {
        List<String> failedRateCodes = new ArrayList<>(Collections.singletonList("rc3"));
        List<List<MappedRateCodeHolder>> mappedRateCodes = Collections.singletonList(
                Arrays.asList(
                        getMappedRateCodeHolder("MC1", true),
                        getMappedRateCodeHolder("MC2", false)));

        setOnExecutionContext(jobExecution, RATE_CODE_LIST_CONTEXT_KEY, new ArrayList<>(Arrays.asList("rc3")));
        setOnExecutionContext(jobExecution, FAILED_RATE_CODES_CONTEXT_KEY, failedRateCodes);
        setOnExecutionContext(jobExecution, CURRENT_RATE_CODE_INDEX_CONTEXT_KEY, 0);
        when(contextHelper.isChunkingEnabled(stepExecution.getJobExecution())).thenReturn(true);
        when(contextHelper.getDecisionCurrentChunk(stepExecution.getJobExecution())).thenReturn(3);
        setOnExecutionContext(jobExecution, MAPPED_PRODUCT_CODES_CONTEXT_KEY, mappedRateCodes);
        setOnExecutionContext(jobExecution, MAPPED_RATE_CODE_INDEX_CONTEXT_KEY, 1);

        stepExecution.setExitStatus(ExitStatus.COMPLETED);

        ExitStatus result = step.afterStep(stepExecution);

        assertNull(getFromExecutionContext(jobExecution, CURRENT_RATE_CODE_INDEX_CONTEXT_KEY));
        assertEquals(failedRateCodes, getFromExecutionContext(jobExecution, RATE_CODE_LIST_CONTEXT_KEY));
        assertTrue(((List<MappedRateCodeHolder>) getFromExecutionContext(jobExecution, FAILED_RATE_CODES_CONTEXT_KEY)).isEmpty());
        assertEquals(BatchStatus.FAILED, stepExecution.getStatus());
        assertEquals(mappedRateCodes, getFromExecutionContext(jobExecution, MAPPED_PRODUCT_CODES_CONTEXT_KEY));

        assertEquals(ExitStatus.FAILED.getExitCode(), result.getExitCode());
        assertEquals("Agile Rates Decision Upload Failed for Rate Codes [rc3]", result.getExitDescription());
    }

    @Test
    public void afterStep_PartnerError_NoChunking() {
        List<String> failedRateCodes = new ArrayList<>(Collections.singletonList("rc3"));
        List<List<MappedRateCodeHolder>> mappedRateCodes = Collections.singletonList(
                Arrays.asList(
                        getMappedRateCodeHolder("MC1", true),
                        getMappedRateCodeHolder("MC2", false)));

        setOnExecutionContext(jobExecution, RATE_CODE_LIST_CONTEXT_KEY, new ArrayList<>(Arrays.asList("rc3")));
        setOnExecutionContext(jobExecution, FAILED_RATE_CODES_CONTEXT_KEY, failedRateCodes);
        setOnExecutionContext(jobExecution, CURRENT_RATE_CODE_INDEX_CONTEXT_KEY, 0);
        setOnExecutionContext(jobExecution, AGGREGATE_PARTNER_ERRORS_KEY, Collections.singletonList("Partner errors for rate code rc3: [Bummer!]"));
        when(contextHelper.isChunkingEnabled(stepExecution.getJobExecution())).thenReturn(true);
        setOnExecutionContext(jobExecution, MAPPED_PRODUCT_CODES_CONTEXT_KEY, mappedRateCodes);
        setOnExecutionContext(jobExecution, MAPPED_RATE_CODE_INDEX_CONTEXT_KEY, 1);
        stepExecution.setExitStatus(ExitStatus.COMPLETED);

        ExitStatus result = step.afterStep(stepExecution);

        assertNull(getFromExecutionContext(jobExecution, CURRENT_RATE_CODE_INDEX_CONTEXT_KEY));
        assertNull(getFromExecutionContext(jobExecution, AGGREGATE_PARTNER_ERRORS_KEY));
        assertEquals(failedRateCodes, getFromExecutionContext(jobExecution, RATE_CODE_LIST_CONTEXT_KEY));
        assertTrue(((List<String>) getFromExecutionContext(jobExecution, FAILED_RATE_CODES_CONTEXT_KEY)).isEmpty());
        assertEquals(BatchStatus.FAILED, stepExecution.getStatus());
        assertNull(getFromExecutionContext(jobExecution, SOAP_CURRENT_CHUNK_CONTEXT_KEY));
        assertEquals(mappedRateCodes, getFromExecutionContext(jobExecution, MAPPED_PRODUCT_CODES_CONTEXT_KEY));

        assertEquals(ExitStatus.FAILED.getExitCode(), result.getExitCode());
        assertEquals("Agile Rates Decision Upload Failed for Rate Codes [rc3], Partner errors received [Partner errors for rate code rc3: [Bummer!]]", result.getExitDescription());
    }

    @Test
    public void afterStep_Complete() {
        List<List<MappedRateCodeHolder>> mappedRateCodes = Collections.singletonList(
                Arrays.asList(
                        getMappedRateCodeHolder("MC1", true),
                        getMappedRateCodeHolder("MC2", true)));
        setOnExecutionContext(jobExecution, RATE_CODE_LIST_CONTEXT_KEY, new ArrayList<>(Arrays.asList("rc2")));
        setOnExecutionContext(jobExecution, CURRENT_RATE_CODE_INDEX_CONTEXT_KEY, 0);
        when(contextHelper.isChunkingEnabled(stepExecution.getJobExecution())).thenReturn(true);
        when(contextHelper.getDecisionCurrentChunk(stepExecution.getJobExecution())).thenReturn(3);
        setOnExecutionContext(jobExecution, MAPPED_PRODUCT_CODES_CONTEXT_KEY, mappedRateCodes);
        setOnExecutionContext(jobExecution, MAPPED_RATE_CODE_INDEX_CONTEXT_KEY, 1);

        stepExecution.setExitStatus(ExitStatus.COMPLETED);

        ExitStatus result = step.afterStep(stepExecution);

        assertEquals(Arrays.asList("rc2"), getFromExecutionContext(jobExecution, RATE_CODE_LIST_CONTEXT_KEY));
        verify(contextHelper).removeChunkingValuesFromContext(stepExecution.getJobExecution());
        verify(contextHelper).removeChunkingSettingsFromContext(stepExecution);

        assertEquals(ExitStatus.COMPLETED.getExitCode(), result.getExitCode());
    }

    @Test
    public void getProductMappings() {
        List<String> agileProducts = Arrays.asList("P1", "P2", "P3", "P4");
        when(rateCodeVendorMappingService.getRateCodeNameVendorMappings(EXTERNAL_SYSTEM, agileProducts)).
                thenReturn(getRateCodeVendorMappings(Arrays.asList("P1", "P3")));

        Map<String, List<String>> result = step.getProductMappings(new HashSet(agileProducts), EXTERNAL_SYSTEM);

        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals(Arrays.asList("P1_m0", "P1_m1"), result.get("P1"));
        assertEquals(Arrays.asList("P3_m0", "P3_m1"), result.get("P3"));
    }

    @Test
    public void getContextList() {
        List<String> agileProducts = Arrays.asList("P1", "P3");
        when(rateCodeVendorMappingService.getRateCodeNameVendorMappings(EXTERNAL_SYSTEM, agileProducts)).
                thenReturn(getRateCodeVendorMappings(Arrays.asList("P3", "P1")));

        Map<String, List<String>> mappings = step.getProductMappings(new HashSet(agileProducts), EXTERNAL_SYSTEM);
        Iterator<String> iter = mappings.keySet().iterator();
        assertEquals("P3", iter.next());
        assertEquals("P1", iter.next());

        List<List<MappedRateCodeHolder>> contextList = step.getRateCodeMappingContextList(agileProducts, mappings);

        assertNotNull(contextList);
        assertTrue(contextList.get(0).get(0).getRateCode().startsWith(agileProducts.get(0)));
        assertTrue(contextList.get(1).get(0).getRateCode().startsWith(agileProducts.get(1)));
    }

    @Test
    public void shouldContinue_MappedProduct_MoreRateCodesToDeliver() {
        List<String> agileProducts = Arrays.asList("P1", "P3");
        when(rateCodeVendorMappingService.getRateCodeNameVendorMappings(EXTERNAL_SYSTEM, agileProducts)).
                thenReturn(getRateCodeVendorMappings(Arrays.asList("P3", "P1")));

        Map<String, List<String>> mappings = step.getProductMappings(new HashSet(agileProducts), EXTERNAL_SYSTEM);
        List<List<MappedRateCodeHolder>> contextList = step.getRateCodeMappingContextList(agileProducts, mappings);

        setOnExecutionContext(jobExecution, RATE_CODE_LIST_CONTEXT_KEY, agileProducts);
        setOnExecutionContext(jobExecution, CURRENT_RATE_CODE_INDEX_CONTEXT_KEY, 0);
        setOnExecutionContext(jobExecution, FAILED_RATE_CODES_CONTEXT_KEY, new ArrayList<>());
        setOnExecutionContext(jobExecution, MAPPED_PRODUCT_CODES_CONTEXT_KEY, contextList);

        assertTrue(step.shouldContinue(jobExecution, stepExecution.getStepName()));

        assertEquals(0, getFromExecutionContext(jobExecution, CURRENT_RATE_CODE_INDEX_CONTEXT_KEY));
        assertEquals(contextList, getFromExecutionContext(jobExecution, MAPPED_PRODUCT_CODES_CONTEXT_KEY));
        assertEquals(0, getFromExecutionContext(jobExecution, MAPPED_RATE_CODE_INDEX_CONTEXT_KEY));

        verifyZeroInteractions(agileRates);
    }

    @Test
    public void shouldContinue_MappedProduct_Resume_FirstCodeSuccess_SecondCodeFailure() {
        List<String> agileProducts = Arrays.asList("P1", "P3");
        when(rateCodeVendorMappingService.getRateCodeNameVendorMappings(EXTERNAL_SYSTEM, agileProducts)).
                thenReturn(getRateCodeVendorMappings(Arrays.asList("P3", "P1")));

        Map<String, List<String>> mappings = step.getProductMappings(new HashSet(agileProducts), EXTERNAL_SYSTEM);
        List<List<MappedRateCodeHolder>> contextList = step.getRateCodeMappingContextList(agileProducts, mappings);
        contextList.get(0).get(0).setSuccess(true);
        contextList.get(0).get(1).setSuccess(false);

        setOnExecutionContext(jobExecution, RATE_CODE_LIST_CONTEXT_KEY, agileProducts);
        setOnExecutionContext(jobExecution, CURRENT_RATE_CODE_INDEX_CONTEXT_KEY, 0);
        setOnExecutionContext(jobExecution, FAILED_RATE_CODES_CONTEXT_KEY, new ArrayList<>());
        setOnExecutionContext(jobExecution, MAPPED_PRODUCT_CODES_CONTEXT_KEY, contextList);

        assertTrue(step.shouldContinue(jobExecution, stepExecution.getStepName()));

        assertEquals(0, getFromExecutionContext(jobExecution, CURRENT_RATE_CODE_INDEX_CONTEXT_KEY));
        assertEquals(contextList, getFromExecutionContext(jobExecution, MAPPED_PRODUCT_CODES_CONTEXT_KEY));
        assertEquals(1, getFromExecutionContext(jobExecution, MAPPED_RATE_CODE_INDEX_CONTEXT_KEY));

        verifyZeroInteractions(agileRates);
    }

    @Test
    public void shouldContinue_MappedProduct_AllRateCodesDelivered() {
        List<String> agileProducts = Arrays.asList("P1", "P3");
        when(rateCodeVendorMappingService.getRateCodeNameVendorMappings(EXTERNAL_SYSTEM, agileProducts)).
                thenReturn(getRateCodeVendorMappings(Arrays.asList("P3", "P1")));

        Map<String, List<String>> mappings = step.getProductMappings(new HashSet(agileProducts), EXTERNAL_SYSTEM);
        List<List<MappedRateCodeHolder>> contextList = step.getRateCodeMappingContextList(agileProducts, mappings);
        contextList.get(0).get(0).setSuccess(true);
        contextList.get(0).get(1).setSuccess(true);

        setOnExecutionContext(jobExecution, RATE_CODE_LIST_CONTEXT_KEY, agileProducts);
        setOnExecutionContext(jobExecution, CURRENT_RATE_CODE_INDEX_CONTEXT_KEY, 0);
        setOnExecutionContext(jobExecution, FAILED_RATE_CODES_CONTEXT_KEY, new ArrayList<>());
        setOnExecutionContext(jobExecution, MAPPED_PRODUCT_CODES_CONTEXT_KEY, contextList);
        setOnExecutionContext(jobExecution, MAPPED_RATE_CODE_INDEX_CONTEXT_KEY, 1);

        assertTrue(step.shouldContinue(jobExecution, stepExecution.getStepName()));

        assertEquals(1, getFromExecutionContext(jobExecution, CURRENT_RATE_CODE_INDEX_CONTEXT_KEY));
        assertEquals(contextList, getFromExecutionContext(jobExecution, MAPPED_PRODUCT_CODES_CONTEXT_KEY));
        assertEquals(0, getFromExecutionContext(jobExecution, MAPPED_RATE_CODE_INDEX_CONTEXT_KEY));

        verifyZeroInteractions(agileRates);
    }

    @Test
    void shouldExecuteStep_SmallGroupProducts() {
        assertNull(getFromExecutionContext(jobExecution, RATE_CODE_LIST_CONTEXT_KEY));
        jobWorkContext.setPropertyStage(Stage.TWO_WAY.getCode());
        when(agileRatesDecisionService.isSmallGroupProductEnabled()).thenReturn(true);
        when(agileRatesDecisionService.agileRatesDecisionsEnabled(EXTERNAL_SYSTEM_PARAMETER)).thenReturn(true);

        Product one = getProduct("sgp1", true);
        Product two = getProduct("sgp2", true);

        List<Product> rateCodeList = new ArrayList<>(Arrays.asList(one, two));
        when(agileRatesConfigurationService.findActiveUploadEnabledSmallGroupProducts()).thenReturn(rateCodeList);
        when(rateCodeVendorMappingService.getRateCodeNameVendorMappings(EXTERNAL_SYSTEM_PARAMETER, Arrays.asList("sgp1", "sgp2")))
                .thenReturn(Collections.emptyList());

        AbstractTaskletStep.ShouldExecuteStepResult result = step.shouldExecuteStep(jobExecution, jobWorkContext);

        assertTrue(result.shouldExecute(), result.getMessage());
        assertFalse(jobExecutionUtil.isLoopComplete(stepExecution.getJobExecution(), stepExecution.getStepName()));

        List<String> rateCodes = (List<String>) getFromExecutionContext(jobExecution, RATE_CODE_LIST_CONTEXT_KEY);
        assertEquals(new ArrayList<>(Arrays.asList("sgp1", "sgp2")), rateCodes);
    }

    @Test
    void shouldExecuteStep_MappedSmallGroupProduct() {
        assertNull(getFromExecutionContext(jobExecution, RATE_CODE_LIST_CONTEXT_KEY));
        jobWorkContext.setPropertyStage(Stage.TWO_WAY.getCode());
        when(agileRatesDecisionService.isSmallGroupProductEnabled()).thenReturn(true);
        when(agileRatesDecisionService.agileRatesDecisionsEnabled(EXTERNAL_SYSTEM_PARAMETER)).thenReturn(true);

        Product one = getProduct("sgp1", true);
        Product two = getProduct("sgp2", true);

        List<Product> rateCodeList = new ArrayList<>(Arrays.asList(one, two));
        when(agileRatesConfigurationService.findActiveUploadEnabledSmallGroupProducts()).thenReturn(rateCodeList);
        when(rateCodeVendorMappingService.getRateCodeNameVendorMappings(EXTERNAL_SYSTEM_PARAMETER, Arrays.asList("sgp1", "sgp2")))
                .thenReturn(getRateCodeVendorMappings(List.of("sgp2")));

        AbstractTaskletStep.ShouldExecuteStepResult result = step.shouldExecuteStep(jobExecution, jobWorkContext);

        assertTrue(result.shouldExecute(), result.getMessage());
        assertFalse(jobExecutionUtil.isLoopComplete(stepExecution.getJobExecution(), stepExecution.getStepName()));

        List<String> rateCodes = (List<String>) getFromExecutionContext(jobExecution, RATE_CODE_LIST_CONTEXT_KEY);
        assertTrue(rateCodes.contains("sgp2"));
        assertFalse(rateCodes.contains("sgp1"));
    }

    @Test
    void shouldExecuteStep_noActiveSmallGroupProductsForUpload() {
        assertNull(getFromExecutionContext(jobExecution, RATE_CODE_LIST_CONTEXT_KEY));
        jobWorkContext.setPropertyStage(Stage.TWO_WAY.getCode());
        when(agileRatesDecisionService.isSmallGroupProductEnabled()).thenReturn(true);
        when(agileRatesDecisionService.agileRatesDecisionsEnabled(EXTERNAL_SYSTEM_PARAMETER)).thenReturn(true);

        List<Product> rateCodeList = new ArrayList<>();
        when(agileRatesConfigurationService.findActiveUploadEnabledSmallGroupProducts()).thenReturn(rateCodeList);

        AbstractTaskletStep.ShouldExecuteStepResult result = step.shouldExecuteStep(jobExecution, jobWorkContext);

        assertFalse(result.shouldExecute());
        assertTrue(jobExecutionUtil.isLoopComplete(jobExecution, stepExecution.getStepName()));

        assertEquals("There are no active agile products configured for upload.", result.getMessage());
        assertNull(getFromExecutionContext(jobExecution, RATE_CODE_LIST_CONTEXT_KEY));
    }

    @Test
    void shouldExecuteStep_SgpDecisionsNotConfiguredOrNotAvailable() {
        assertNull(getFromExecutionContext(jobExecution, RATE_CODE_LIST_CONTEXT_KEY));
        jobWorkContext.setPropertyStage(Stage.TWO_WAY.getCode());
        when(agileRatesDecisionService.isSmallGroupProductEnabled()).thenReturn(true);
        when(agileRatesDecisionService.agileRatesDecisionsEnabled(EXTERNAL_SYSTEM_PARAMETER)).thenReturn(false);

        Product one = getProduct("sgp1", true);
        Product two = getProduct("sgp2", true);

        List<Product> rateCodeList = new ArrayList<>(Arrays.asList(one, two));
        when(agileRatesConfigurationService.findActiveUploadEnabledSmallGroupProducts()).thenReturn(rateCodeList);

        AbstractTaskletStep.ShouldExecuteStepResult result = step.shouldExecuteStep(jobExecution, jobWorkContext);

        assertFalse(result.shouldExecute());
        assertTrue(jobExecutionUtil.isLoopComplete(jobExecution, stepExecution.getStepName()));

        assertEquals("Agile Rates decisions are not enabled for this property.", result.getMessage());
        assertNull(getFromExecutionContext(jobExecution, RATE_CODE_LIST_CONTEXT_KEY));
    }

    @Test
    void shouldExecuteStep_AgileRateAndSmallGroupProducts() {
        assertNull(getFromExecutionContext(jobExecution, RATE_CODE_LIST_CONTEXT_KEY));
        jobWorkContext.setPropertyStage(Stage.TWO_WAY.getCode());
        when(agileRatesDecisionService.agileRatesFeatureEnabled()).thenReturn(true);
        when(agileRatesDecisionService.isSmallGroupProductEnabled()).thenReturn(true);
        when(agileRatesDecisionService.agileRatesDecisionsEnabled(EXTERNAL_SYSTEM_PARAMETER)).thenReturn(true);

        Product arp1 = getProduct("arp1", true);
        Product arp2 = getProduct("arp2", true);
        Product arpInactive = getProduct("ina", false);
        List<Product> agileProductRateCodeList = new ArrayList<>(Arrays.asList(arp1, arp2, arpInactive));
        when(agileRatesConfigurationService.findAgileRatesProducts()).thenReturn(agileProductRateCodeList);

        Product sgp1 = getProduct("sgp1", true);
        Product sgp2 = getProduct("sgp2", true);
        List<Product> smallGroupRateCodeList = new ArrayList<>(Arrays.asList(sgp1, sgp2));
        when(agileRatesConfigurationService.findActiveUploadEnabledSmallGroupProducts()).thenReturn(smallGroupRateCodeList);
        when(rateCodeVendorMappingService.getRateCodeNameVendorMappings(EXTERNAL_SYSTEM_PARAMETER, Arrays.asList("arp1", "arp2", "ina", "sgp1", "sgp2")))
                .thenReturn(Collections.emptyList());

        AbstractTaskletStep.ShouldExecuteStepResult result = step.shouldExecuteStep(jobExecution, jobWorkContext);

        assertTrue(result.shouldExecute(), result.getMessage());
        assertFalse(jobExecutionUtil.isLoopComplete(stepExecution.getJobExecution(), stepExecution.getStepName()));

        List<String> rateCodes = (List<String>) getFromExecutionContext(jobExecution, RATE_CODE_LIST_CONTEXT_KEY);
        assertTrue(CollectionUtils.isEqualCollection(Arrays.asList("arp1", "arp2", "sgp1", "sgp2"), rateCodes));
    }

    @Test
    void shouldExecuteStep_MappedAgileRateAndSmallGroupProducts() {
        assertNull(getFromExecutionContext(jobExecution, RATE_CODE_LIST_CONTEXT_KEY));
        jobWorkContext.setPropertyStage(Stage.TWO_WAY.getCode());
        when(agileRatesDecisionService.agileRatesFeatureEnabled()).thenReturn(true);
        when(agileRatesDecisionService.isSmallGroupProductEnabled()).thenReturn(true);
        when(agileRatesDecisionService.agileRatesDecisionsEnabled(EXTERNAL_SYSTEM_PARAMETER)).thenReturn(true);

        Product arp1 = getProduct("arp1", true);
        Product arp2 = getProduct("arp2", true);
        Product arpInactive = getProduct("ina", false);
        List<Product> agileProductRateCodeList = new ArrayList<>(Arrays.asList(arp1, arp2, arpInactive));
        when(agileRatesConfigurationService.findAgileRatesProducts()).thenReturn(agileProductRateCodeList);

        Product sgp1 = getProduct("sgp1", true);
        Product sgp2 = getProduct("sgp2", true);
        List<Product> smallGroupRateCodeList = new ArrayList<>(Arrays.asList(sgp1, sgp2));
        when(agileRatesConfigurationService.findActiveUploadEnabledSmallGroupProducts()).thenReturn(smallGroupRateCodeList);

        final List<String> productRateCodes = new ArrayList<>(Stream.concat(agileProductRateCodeList.stream(), smallGroupRateCodeList.stream()).collect(Collectors.toList())
                .stream().collect(Collectors.toMap(Product::getName, Function.identity()))
                .keySet());
        when(rateCodeVendorMappingService.getRateCodeNameVendorMappings(EXTERNAL_SYSTEM_PARAMETER, productRateCodes))
                .thenReturn(getRateCodeVendorMappings(Arrays.asList("arp1", "sgp2")));

        AbstractTaskletStep.ShouldExecuteStepResult result = step.shouldExecuteStep(jobExecution, jobWorkContext);

        assertTrue(result.shouldExecute(), result.getMessage());
        assertFalse(jobExecutionUtil.isLoopComplete(stepExecution.getJobExecution(), stepExecution.getStepName()));

        List<String> rateCodes = (List<String>) getFromExecutionContext(jobExecution, RATE_CODE_LIST_CONTEXT_KEY);
        assertTrue(rateCodes.contains("arp1"));
        assertTrue(rateCodes.contains("sgp2"));
        assertFalse(rateCodes.contains("arp2"));
        assertFalse(rateCodes.contains("sgp1"));
    }

    @Test
    void shouldExecuteStep_MappedAgileRateAndSmallGroupProductsNotActive() {
        assertNull(getFromExecutionContext(jobExecution, RATE_CODE_LIST_CONTEXT_KEY));
        jobWorkContext.setPropertyStage(Stage.TWO_WAY.getCode());
        when(agileRatesDecisionService.agileRatesFeatureEnabled()).thenReturn(true);
        when(agileRatesDecisionService.isSmallGroupProductEnabled()).thenReturn(true);
        when(agileRatesDecisionService.agileRatesDecisionsEnabled(EXTERNAL_SYSTEM_PARAMETER)).thenReturn(true);

        Product arp1 = getProduct("arp1", true);
        Product arp2 = getProduct("arp2", true);
        Product arpInactive = getProduct("ina", false);
        List<Product> agileProductRateCodeList = new ArrayList<>(Arrays.asList(arp1, arp2, arpInactive));
        when(agileRatesConfigurationService.findAgileRatesProducts()).thenReturn(agileProductRateCodeList);

        Product sgp1 = getProduct("sgp1", true);
        Product sgp2 = getProduct("sgp2", true);
        List<Product> smallGroupRateCodeList = new ArrayList<>(Arrays.asList(sgp1, sgp2));
        when(agileRatesConfigurationService.findActiveUploadEnabledSmallGroupProducts()).thenReturn(smallGroupRateCodeList);

        final List<String> productRateCodes = new ArrayList<>(Stream.concat(agileProductRateCodeList.stream(), smallGroupRateCodeList.stream()).collect(Collectors.toList())
                .stream().collect(Collectors.toMap(Product::getName, Function.identity()))
                .keySet());
        when(rateCodeVendorMappingService.getRateCodeNameVendorMappings(EXTERNAL_SYSTEM_PARAMETER, productRateCodes))
                .thenReturn(getRateCodeVendorMappings(List.of("ina")));

        AbstractTaskletStep.ShouldExecuteStepResult result = step.shouldExecuteStep(jobExecution, jobWorkContext);

        assertFalse(result.shouldExecute(), result.getMessage());
        assertTrue(jobExecutionUtil.isLoopComplete(stepExecution.getJobExecution(), stepExecution.getStepName()));
    }


    private List<RateCodeVendorMapping> getRateCodeVendorMappings(List<String> mappedProducts) {
        List<RateCodeVendorMapping> mappings = new ArrayList<>();
        for (String current : mappedProducts) {
            for (int i = 0; i < 2; i++) {
                mappings.add(getRateCodeVendorMapping(current, i));
            }
        }
        return mappings;
    }

    private RateCodeVendorMapping getRateCodeVendorMapping(String rateCodeName, int i) {
        RateCodeVendorMapping mapping = new RateCodeVendorMapping();
        mapping.setRateCodeName(rateCodeName);
        mapping.setVendorRateCode(rateCodeName + "_m" + i);
        return mapping;
    }

    private MappedRateCodeHolder getMappedRateCodeHolder(String rateCode, Boolean success) {
        MappedRateCodeHolder holder = new MappedRateCodeHolder(rateCode);
        holder.setSuccess(success);
        return holder;
    }
}