package com.ideas.tetris.jems.job.rateshopping.step;

import com.ideas.tetris.pacman.services.property.ExtractDetailsServiceLocal;
import com.ideas.tetris.pacman.services.property.dto.WebRateExtractDetails;
import com.ideas.tetris.pacman.util.file.PacmanFileUtilService;
import com.ideas.tetris.platform.common.job.JobParameterKey;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.batch.core.ExitStatus;
import org.springframework.batch.repeat.RepeatStatus;

import java.io.File;
import java.util.Date;

public class WebRateCatchupFilesCopyStepTest extends WebRateStepTest {
    @InjectMocks
    private WebRateCatchupFilesCopyStep step;

    @Mock
    private ExtractDetailsServiceLocal extractDetailsService;
    @Mock
    @PacmanFileUtilService.PacmanQualifier
    private PacmanFileUtilService pacmanFileUtilService;
    @Mock
    private WebRateExtractDetails extractDetails;

    @BeforeEach
    @Override
    public void setup() {
        super.setup();
    }

    @Test
    public void execute() throws Exception {
        Date today = DateUtil.getCurrentDateWithoutTime();
        jobParametersBuilder.addString(JobParameterKey.DATE_START, DateUtil.addYearsToDate(today, -1).toString());
        jobParametersBuilder.addString(JobParameterKey.DATE_END, DateUtil.addDaysToDate(today, -2).toString());
        regenerateStepContext();

        mockStepContextAndWorkContext();

        Mockito.when(pacmanFileUtilService.createDirectoryWithCleanUp(Mockito.any(File.class))).thenReturn(catchupFolder);
        Mockito.when(extractDetailsService.getWebRateExtractDetailsWithFilePaths(PROP_ID)).thenReturn(extractDetails);
        Mockito.doNothing().when(extractDetails).copyIncomingExtractsToDirectory(
                Mockito.any(Date.class), Mockito.any(Date.class), Mockito.any(File.class));

        RepeatStatus status = step.execute(stepContribution, chunkContext);
        Assertions.assertEquals(RepeatStatus.FINISHED, status);
        Assertions.assertEquals(ExitStatus.EXECUTING, stepExecution.getExitStatus());

        Mockito.verify(pacmanFileUtilService, Mockito.times(1)).createDirectoryWithCleanUp(Mockito.any(File.class));
        Mockito.verify(extractDetailsService, Mockito.times(1)).getWebRateExtractDetailsWithFilePaths(PROP_ID);
        Mockito.verify(extractDetails, Mockito.times(1)).copyIncomingExtractsToDirectory(
                Mockito.any(Date.class), Mockito.any(Date.class), Mockito.any(File.class));
        verifyStepContextAndWorkContext();
    }
}
