package com.ideas.tetris.jems.ngi.job.ngi.step;

import com.ideas.tetris.jems.core.step.context.StepExecutionContextKey;
import com.ideas.tetris.jems.core.step.context.StepExecutionUtil;
import com.ideas.tetris.jems.core.test.StepJupiterTest;
import com.ideas.tetris.jems.ngi.job.ngi.item.RateItemReader;
import com.ideas.tetris.jems.ngi.job.ngi.item.ResyncRateItemReader;
import com.ideas.tetris.jems.ngi.job.ngi.item.UnqualifiedResyncRateItemReader;
import com.ideas.tetris.jems.ngi.job.ngi.item.v2.UnqualifiedResyncRateV2ItemReader;
import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.common.utils.systemconfig.SystemConfig;
import com.ideas.tetris.pacman.services.unqualifiedrate.entity.RateUnqualified;
import com.ideas.tetris.pacman.services.unqualifiedrate.serivce.RateUnqualifiedService;
import com.ideas.tetris.platform.common.job.JobParameterKey;
import com.ideas.tetris.platform.common.utils.map.MapBuilder;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.batch.core.ExitStatus;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyZeroInteractions;
import static org.mockito.Mockito.when;

@MockitoSettings(strictness = Strictness.LENIENT)
@ExtendWith(MockitoExtension.class)
public class UnqualifiedRateResyncStepTest extends StepJupiterTest {

    @Mock
    private UnqualifiedResyncRateItemReader unqualifiedResyncRateItemReader;

    @Mock
    private UnqualifiedResyncRateV2ItemReader unqualifiedResyncRateV2ItemReader;

    @Mock
    private RateUnqualifiedService rateUnqualifiedService;

    @Mock
    private PacmanConfigParamsService configParamsService;

    @InjectMocks
    private UnqualifiedRateResyncStep instance;

    private List<? extends Map<String, Object>> listToSave = new ArrayList<>();

    @BeforeEach
    public void setUp() throws Exception {
        super.setup();

        jobParametersBuilder.addString(JobParameterKey.CORRELATION_ID, "123");
        jobParametersBuilder.addString(JobParameterKey.RESYNC_RATES_TYPE, ResyncRateItemReader.ALL_RATES);
        regenerateStepContext();
        listToSave = Arrays.asList(getMap("one", "two"), getMap("three", "four"));
    }

    @Test
    public void testItemReader() {
        if (SystemConfig.isPmsInboundV2()) {
            assertEquals(unqualifiedResyncRateV2ItemReader, instance.itemReader());
        } else {
            assertEquals(unqualifiedResyncRateItemReader, instance.itemReader());
        }
    }

    @Test
    public void testChunkSize() {
        assertEquals(RateItemReader.DEFAULT_RATE_CHUNK_SIZE, instance.chunkSize());
    }

    @Test
    public void testWrite() throws Exception {
        when(rateUnqualifiedService.saveFromMap(anyList())).thenReturn(Arrays.asList(new RateUnqualified(), new RateUnqualified()));
        instance.write(listToSave);
        verify(rateUnqualifiedService).saveFromMap(anyList());
    }

    @Test
    public void shouldExecuteStepForUnqualifiedRates() {
        jobParametersBuilder.addString(JobParameterKey.RESYNC_RATES_TYPE, ResyncRateItemReader.UNQUALIFIED_RATES);
        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.UNQUALIFIED_RATE_DEFERRED_DELIVERY.value(Constants.NGI))).thenReturn(true);
        regenerateStepContext();
        assertTrue(instance.shouldExecuteStep(jobExecution, null).shouldExecute());
    }

    @Test
    public void shouldNotExecuteStepForWhenUnqualifiedRatesPopulationIsDisabled() {
        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.UNQUALIFIED_RATE_DEFERRED_DELIVERY.value(Constants.NGI))).thenReturn(false);
        regenerateStepContext();
        assertFalse(instance.shouldExecuteStep(jobExecution, null).shouldExecute());
    }

    @Test
    public void shouldNotExecuteStepForQualifiedRates() {
        jobParametersBuilder.addString(JobParameterKey.RESYNC_RATES_TYPE, ResyncRateItemReader.QUALIFIED_RATES);
        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.UNQUALIFIED_RATE_DEFERRED_DELIVERY.value(Constants.NGI))).thenReturn(true);
        regenerateStepContext();
        assertFalse(instance.shouldExecuteStep(jobExecution, null).shouldExecute());
    }

    @Test
    public void testAfterStep_completed() {
        doNothing().when(rateUnqualifiedService).setPricingSyncFlag();
        stepExecution.setExitStatus(ExitStatus.COMPLETED);
        StepExecutionUtil.setOnExecutionContext(stepExecution, StepExecutionContextKey.NGI_TOTAL_ELEMENTS, 1L);
        assertEquals(ExitStatus.COMPLETED, instance.afterStep(stepExecution));
        verify(rateUnqualifiedService).setPricingSyncFlag();
    }

    @Test
    public void testAfterStep_completed_zeroElements() {
        doNothing().when(rateUnqualifiedService).setPricingSyncFlag();
        stepExecution.setExitStatus(ExitStatus.COMPLETED);
        StepExecutionUtil.setOnExecutionContext(stepExecution, StepExecutionContextKey.NGI_TOTAL_ELEMENTS, 0L);
        assertEquals(ExitStatus.COMPLETED, instance.afterStep(stepExecution));
        verifyZeroInteractions(rateUnqualifiedService);
    }

    @Test
    public void testAfterStep_failed() {
        stepExecution.setExitStatus(ExitStatus.FAILED);
        assertEquals(ExitStatus.FAILED, instance.afterStep(stepExecution));
        verifyZeroInteractions(rateUnqualifiedService);
    }

    @Test
    public void testAfterStep_skipped() {
        stepExecution.setExitStatus(ExitStatus.NOOP);
        assertEquals(ExitStatus.NOOP, instance.afterStep(stepExecution));
        verifyZeroInteractions(rateUnqualifiedService);
    }

    public Map<String, Object> getMap(String key, String value) {
        return MapBuilder.with(key, value).get();
    }
}