package com.ideas.tetris.jems.common.step.recommendation;

import com.ideas.tetris.jems.common.step.chunk.DateChunkItemReader;
import com.ideas.tetris.jems.common.step.chunk.DateChunkItemWriter;
import com.ideas.tetris.jems.common.step.chunk.PacmanChunkSizeProvider;
import com.ideas.tetris.jems.core.job.entity.JobInstanceWorkContext;
import com.ideas.tetris.jems.core.step.tasklet.AbstractTaskletStep;
import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName;
import com.ideas.tetris.pacman.services.datafeed.service.DecisionConfigurationService;
import com.ideas.tetris.pacman.services.qualifiedrate.service.RateQualifiedService;
import com.ideas.tetris.platform.services.Stage;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.batch.core.JobExecution;
import org.springframework.batch.core.StepExecution;
import org.springframework.batch.item.ExecutionContext;
import org.testng.Assert;

import static com.ideas.g3.test.AbstractG3JupiterTest.inject;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class FPLOSQualifiedCdpRecommendationStepTest {

    @Mock
    private FPLOSQualifiedCDPRecommendationChunkWriter recommendationWriter;

    @Mock
    private FPLOSQualifiedCDPRecommendationChunkReader recommendationReader;
    @Mock
    private DecisionConfigurationService decisionConfigurationService;

    @InjectMocks
    FPLOSQualifiedCdpRecommendationStep step;
    private FPLOSQualifiedCdpRecommendationStep fplosQualifiedCdpRecommendationStep = new FPLOSQualifiedCdpRecommendationStep();

    @Mock
    private StepExecution stepExecution;

    @Mock
    private JobExecution jobExecution;

    @Mock
    private ExecutionContext context;

    @Mock
    RateQualifiedService rateQualifiedService;

    @Mock
    PacmanConfigParamsService pacmanConfigParamsService;

    @Mock
    private PacmanChunkSizeProvider chunkSizeProvider;

    @BeforeEach
    public void setUp() throws Exception {
        inject(fplosQualifiedCdpRecommendationStep, "rateQualifiedService", rateQualifiedService);
        when(rateQualifiedService.getYieldableQualifiedRatesCount()).thenReturn(1);
        PacmanConfigParamsService mockPacmanConfigParamsService = Mockito.mock(PacmanConfigParamsService.class);
        fplosQualifiedCdpRecommendationStep.pacmanConfigParamsService = mockPacmanConfigParamsService;
        when(stepExecution.getJobExecution()).thenReturn(jobExecution);
        when(stepExecution.getJobExecution()).thenReturn(jobExecution);
        when(jobExecution.getExecutionContext()).thenReturn(context);
        when(mockPacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_FPLOS_QUALIFIED_RECOMMENDATION_LOOP_STEP)).thenReturn(false);

    }

    @Test
    public void testStep() {
        assertTrue(step.itemReader() instanceof DateChunkItemReader);
        assertTrue(step.itemReader() instanceof AbstractFpLosDateChunkReader);
        assertTrue(step.itemReader() instanceof FPLOSQualifiedCDPRecommendationChunkReader);

        assertTrue(step.itemWriter() instanceof DateChunkItemWriter);
        assertTrue(step.itemWriter() instanceof FPLOSQualifiedCDPRecommendationChunkWriter);
    }

    @Test
    public void shouldNotInvokeStepWhenStageIsDataCapture() throws Exception {
        JobInstanceWorkContext jobInstanceWorkContext = new JobInstanceWorkContext();
        jobInstanceWorkContext.setPropertyStage(Stage.DATA_CAPTURE.getCode());
        assertFalse(fplosQualifiedCdpRecommendationStep.shouldExecuteStep(jobExecution, jobInstanceWorkContext).shouldExecute());
    }

    @Test
    public void shouldNotInvokeStepWhenStageIsNull() throws Exception {
        JobInstanceWorkContext jobInstanceWorkContext = new JobInstanceWorkContext();
        assertFalse(fplosQualifiedCdpRecommendationStep.shouldExecuteStep(jobExecution, jobInstanceWorkContext).shouldExecute());
    }

    @Test
    public void shouldInvokeStepWhenStageIsTWOWay() throws Exception {
        JobInstanceWorkContext jobInstanceWorkContext = new JobInstanceWorkContext();
        jobInstanceWorkContext.setPropertyStage(Stage.TWO_WAY.getCode());
        assertTrue(fplosQualifiedCdpRecommendationStep.shouldExecuteStep(jobExecution, jobInstanceWorkContext).shouldExecute());
    }


    @Test
    public void shouldNotInvokeStepWhenStageIsOneWayAndGenerateRestrictionsInOneFlagIsOff() throws Exception {
        PacmanConfigParamsService mockPacmanConfigParamsService = Mockito.mock(PacmanConfigParamsService.class);
        fplosQualifiedCdpRecommendationStep.setConfigParamService(mockPacmanConfigParamsService);
        fplosQualifiedCdpRecommendationStep.setDecisionConfigurationService(decisionConfigurationService);
        when(mockPacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.GENERATE_RESTRICTIONS_IN_ONE_WAY.value())).thenReturn(false);
        JobInstanceWorkContext jobInstanceWorkContext = new JobInstanceWorkContext();
        jobInstanceWorkContext.setPropertyStage(Stage.ONE_WAY.getCode());
        assertFalse(fplosQualifiedCdpRecommendationStep.shouldExecuteStep(jobExecution, jobInstanceWorkContext).shouldExecute());
    }
    @Test
    public void shouldNotInvokeStepWhenStageIsOneWayAndGenerateRestrictionsInOneFlagIsOffAndGoLiveIsTrue() throws Exception {
        PacmanConfigParamsService mockPacmanConfigParamsService = Mockito.mock(PacmanConfigParamsService.class);
        fplosQualifiedCdpRecommendationStep.setConfigParamService(mockPacmanConfigParamsService);
        fplosQualifiedCdpRecommendationStep.setDecisionConfigurationService(decisionConfigurationService);
        when(mockPacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.GENERATE_RESTRICTIONS_IN_ONE_WAY.value())).thenReturn(false);
        when(decisionConfigurationService.isGoLiveEnabled()).thenReturn(true);
        JobInstanceWorkContext jobInstanceWorkContext = new JobInstanceWorkContext();
        jobInstanceWorkContext.setPropertyStage(Stage.ONE_WAY.getCode());
        assertTrue(fplosQualifiedCdpRecommendationStep.shouldExecuteStep(jobExecution, jobInstanceWorkContext).shouldExecute());
    }

    @Test
    public void shouldInvokeStepWhenStageIsOneWayAndGenerateRestrictionsInOneFlagIsOn() throws Exception {
        PacmanConfigParamsService mockPacmanConfigParamsService = Mockito.mock(PacmanConfigParamsService.class);
        fplosQualifiedCdpRecommendationStep.setConfigParamService(mockPacmanConfigParamsService);
        when(mockPacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.GENERATE_RESTRICTIONS_IN_ONE_WAY.value())).thenReturn(true);
        JobInstanceWorkContext jobInstanceWorkContext = new JobInstanceWorkContext();
        jobInstanceWorkContext.setPropertyStage(Stage.ONE_WAY.getCode());
        assertTrue(fplosQualifiedCdpRecommendationStep.shouldExecuteStep(jobExecution, jobInstanceWorkContext).shouldExecute());
    }

    @Test
    public void shouldNotExecuteStepWhenLoopToggleIsOn() {
        JobInstanceWorkContext jobInstanceWorkContext = new JobInstanceWorkContext();
        PacmanConfigParamsService mockPacmanConfigParamsService = Mockito.mock(PacmanConfigParamsService.class);
        fplosQualifiedCdpRecommendationStep.pacmanConfigParamsService = mockPacmanConfigParamsService;
        when(mockPacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_FPLOS_QUALIFIED_RECOMMENDATION_LOOP_STEP)).thenReturn(true);
        AbstractTaskletStep.ShouldExecuteStepResult result = fplosQualifiedCdpRecommendationStep.shouldExecuteStep(jobExecution, jobInstanceWorkContext);
        assertFalse(result.shouldExecute());
        assertEquals("FPLOS Loop step is enabled, skipped this step", result.getMessage());
    }

    @Test
    void testChunkSizeDefault() {
        inject(step, "chunkSizeProvider", new PacmanChunkSizeProvider());
        Assert.assertEquals(step.chunkSize(), FPLOSQualifiedCdpRecommendationStep.DEFAULT_DATE_RANGE_CHUNK_SIZE);
    }

    @Test
    void testChunkSizeConfigParam() {
        when(chunkSizeProvider.getOrDefault(FPLOSQualifiedCdpRecommendationStep.class.getSimpleName(), FPLOSQualifiedCdpRecommendationStep.DEFAULT_DATE_RANGE_CHUNK_SIZE)).thenReturn(365);
        Assert.assertEquals(step.chunkSize(), 365);
    }

}
