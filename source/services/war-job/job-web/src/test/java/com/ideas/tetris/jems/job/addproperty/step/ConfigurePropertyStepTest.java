package com.ideas.tetris.jems.job.addproperty.step;

import com.ideas.tetris.jems.core.job.context.JobExecutionContextKey;
import com.ideas.tetris.jems.core.job.context.JobExecutionUtil;
import com.ideas.tetris.jems.core.test.StepJupiterTest;
import com.ideas.tetris.pacman.common.configparams.*;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.services.decisionconfig.SubscriptionEnum;
import com.ideas.tetris.pacman.services.eventaggregator.SyncEvent;
import com.ideas.tetris.pacman.services.property.configuration.dto.impl.PropertyAttributePropertyNewAttributeConfigurationDto;
import com.ideas.tetris.pacman.services.property.configuration.dto.impl.PropertySetupPropertyConfigurationDto;
import com.ideas.tetris.pacman.services.property.configuration.service.PropertyConfigurationLoaderService;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.externalsystem.ExternalSubSystem;
import com.ideas.tetris.platform.common.externalsystem.ExternalSystemHelper;
import com.ideas.tetris.platform.common.externalsystem.ReservationSystem;
import com.ideas.tetris.platform.common.job.JobParameterKey;
import com.ideas.tetris.platform.common.license.LicenseService;
import com.ideas.tetris.platform.common.license.entities.LicensePackage;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import static com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName.IS_HOT_START_PROPERTY;
import static com.ideas.tetris.pacman.common.configparams.IPConfigParamName.CORE_PROPERTY_MIN_CONSECUTIVE_DAYS_THRESHOLD;
import static com.ideas.tetris.pacman.common.constants.Constants.TRUE;
import static com.ideas.tetris.platform.common.license.entities.LicensePackage.GET_LICENSE_PACKAGE_BY_NAME;
import static org.mockito.Mockito.*;
import static org.mockito.MockitoAnnotations.initMocks;

public class ConfigurePropertyStepTest extends StepJupiterTest {

    @InjectMocks
    ConfigurePropertyStep step;

    @Mock
    ConfigurePropertyStep stepMock;

    @Mock
    private PacmanConfigParamsService configService;

    @Mock
    private PropertyConfigurationLoaderService propertyConfigurationLoaderService;

    @Mock
    private ExternalSystemHelper externalSystemHelper;

    @Mock
    private CrudService globalCrudService;

    @Mock
    private LicenseService licenseService;

    private String context = "pacman.client1111.prop2222";

    private String LICENSE_PACKAGE = "Sample License Package";

    @BeforeEach
    public void setUp() {
        initMocks(this);

        jobParametersBuilder.addString(JobParameterKey.PROPERTY_TIMEZONE, "property/timezone");
        jobParametersBuilder.addString(JobParameterKey.YIELD_CURRENCY, "someCurrency");
        jobParametersBuilder.addString(JobParameterKey.BASE_CURRENCY, "someCurrency");
        generateStepExecutionFromMetadata();

        jobWorkContext.setClientId(1111);
        jobWorkContext.setClientCode("client1111");
        jobWorkContext.setPropertyId(2222);
        jobWorkContext.setPropertyCode("prop2222");
    }

    @Test
    public void isAsync() {
        Assertions.assertFalse(step.isStepAsync(null));
    }

    @Test
    public void testDoInvokeHilstar_BrandCode() throws Exception {
        jobParametersBuilder.addString(JobParameterKey.EXTERNAL_SYSTEM, ReservationSystem.NGI.getConfigParameterValue());
        jobParametersBuilder.addString(JobParameterKey.EXTERNAL_SUB_SYSTEM, ExternalSubSystem.HTNG.getConfigParameterValue());
        jobParametersBuilder.addString(JobParameterKey.SUBSCRIPTION_TYPE, "Not Defined");
        jobParametersBuilder.addString(JobParameterKey.CONFIGURATION_METHOD, "Manual");
        generateStepExecutionFromMetadata();

        JobExecutionUtil.setOnExecutionContext(jobExecution, JobExecutionContextKey.INBOUND_INTEGRATION_TYPE, ReservationSystem.HILSTAR);

        PropertySetupPropertyConfigurationDto dto = createPropertyConfigurationDto();
        when(propertyConfigurationLoaderService.getPropertySetupConfiguration(jobWorkContext.getClientId(), jobWorkContext.getPropertyCode())).thenReturn(dto);

        var attributeCfgDto = createPropertyAttributePropertyNewAttributeConfigurationDto();
        when(propertyConfigurationLoaderService.getPropertyNewAttributeConfiguration(
                jobWorkContext.getClientId(), jobWorkContext.getPropertyCode())
        ).thenReturn(attributeCfgDto);
        when(configService.getBooleanParameterValue(FeatureTogglesConfigParamName.SYNC_PROPERTY_ATTRIBUTE_PAIRING_CHANGES_FROM_FDS)).thenReturn(Boolean.TRUE);

        step.doInvoke(stepExecution, jobWorkContext);

        verifyCommonParameters(false);
        verify(configService).addParameterValue(context, IPConfigParamName.BAR_ALLOW_MIN_MAX_LOSOVERRIDE.value(), dto.isAllowMinMaxLOSForBar().toString());
        verify(configService).addParameterValue(context, IPConfigParamName.BAR_ALLOW_AVAILABLE_FOR_ARRIVAL.value(), dto.isAllowDayOfArrivalAvailability().toString());
        verify(configService).addParameterValue(context, IntegrationConfigParamName.DEFAULT_MARKET_SEGMENT.value(Constants.RATCHET), dto.getDefaultMarketSegment());
        verify(configService).addParameterValue(context, IPConfigParamName.BAR_BAR_OVRD_DISPLAY_COMPETITOR.value(), dto.getCompetitivePriceOption());
        verify(configService).addParameterValue(context, IPConfigParamName.BAR_BAR_DECISION.value(), dto.getBarDecisionType());

        verify(configService).addParameterValue(context, IntegrationConfigParamName.PAST_DAYS.value(Constants.RATCHET), "365");
        verify(configService).addParameterValue(context, IntegrationConfigParamName.FUTURE_DAYS.value(), "365");
        verify(configService).addParameterValue(context, IntegrationConfigParamName.NEW_RM_COST_RAW.value(Constants.RATCHET), "true");

        verify(configService).addParameterValue(context, IntegrationConfigParamName.CORE_PROPERTY_EXTERNAL_SYSTEM.value(), ReservationSystem.NGI.getConfigParameterValue());
        verify(configService).addParameterValue(context, IntegrationConfigParamName.CORE_PROPERTY_EXTERNAL_SYSTEM_SUB_SYSTEM.value(), ExternalSubSystem.HTNG.getConfigParameterValue());

        verify(configService).addParameterValue(context, IntegrationConfigParamName.CORE_PROPERTY_CONFIGURATION_COMPLETE.value(), "false");
        verify(configService).addParameterValue(context, PreProductionConfigParamName.MULTI_UNIT_GNR_ENABLED.value(), Constants.TRUE);
        verify(configService).addParameterValue(context, PreProductionConfigParamName.BOOKED_VS_ORIGINAL_ENABLED.value(), Constants.TRUE);
        verify(configService).addParameterValue(context, FeatureTogglesConfigParamName.G3_RMS_CORE_SUBSCRIPTION_TYPE.value(), "Not Defined");
        verify(configService).addParameterValue(context, FeatureTogglesConfigParamName.G3_CONFIGURATION_METHOD.value(), "Manual");

        verify(propertyConfigurationLoaderService).setValuesForOvrdPropertyAttributeTable();
        verify(propertyConfigurationLoaderService).setIpConfigRuntimeParamTable();
        verify(propertyConfigurationLoaderService).setBookingWindowStartDateValueForGroupAndNoFcst();
        verify(propertyConfigurationLoaderService).getPropertySetupConfiguration(1111, "prop2222");
        verify(propertyConfigurationLoaderService).getPropertyNewAttributeConfiguration(1111, "prop2222");
        verifyNoMoreInteractions(propertyConfigurationLoaderService);
    }

    @Test
    public void testDoInvokeHilstarVariableDecisionWindowIsFalse_BrandCode() throws Exception {
        jobParametersBuilder.addString(JobParameterKey.EXTERNAL_SYSTEM, ReservationSystem.NGI.getConfigParameterValue());
        jobParametersBuilder.addString(JobParameterKey.EXTERNAL_SUB_SYSTEM, ExternalSubSystem.HTNG.getConfigParameterValue());
        jobParametersBuilder.addString(JobParameterKey.SUBSCRIPTION_TYPE, "Not Defined");
        jobParametersBuilder.addString(JobParameterKey.CONFIGURATION_METHOD, "Manual");
        generateStepExecutionFromMetadata();

        JobExecutionUtil.setOnExecutionContext(jobExecution, JobExecutionContextKey.INBOUND_INTEGRATION_TYPE, ReservationSystem.HILSTAR);

        PropertySetupPropertyConfigurationDto dto = createPropertyConfigurationDto("Europe/Madrid");
        when(propertyConfigurationLoaderService.getPropertySetupConfiguration(jobWorkContext.getClientId(), jobWorkContext.getPropertyCode())).thenReturn(dto);

        var attributeCfgDto = createPropertyAttributePropertyNewAttributeConfigurationDto();
        when(propertyConfigurationLoaderService.getPropertyNewAttributeConfiguration(
                jobWorkContext.getClientId(), jobWorkContext.getPropertyCode())
        ).thenReturn(attributeCfgDto);
        when(configService.getBooleanParameterValue(FeatureTogglesConfigParamName.SYNC_PROPERTY_ATTRIBUTE_PAIRING_CHANGES_FROM_FDS)).thenReturn(Boolean.TRUE);

        step.doInvoke(stepExecution, jobWorkContext);

        verifyCommonParameters(false);
        verify(configService).addParameterValue(context, IPConfigParamName.BAR_ALLOW_MIN_MAX_LOSOVERRIDE.value(), dto.isAllowMinMaxLOSForBar().toString());
        verify(configService).addParameterValue(context, IPConfigParamName.BAR_ALLOW_AVAILABLE_FOR_ARRIVAL.value(), dto.isAllowDayOfArrivalAvailability().toString());
        verify(configService).addParameterValue(context, IntegrationConfigParamName.DEFAULT_MARKET_SEGMENT.value(Constants.RATCHET), dto.getDefaultMarketSegment());
        verify(configService).addParameterValue(context, IPConfigParamName.BAR_BAR_OVRD_DISPLAY_COMPETITOR.value(), dto.getCompetitivePriceOption());
        verify(configService).addParameterValue(context, IPConfigParamName.BAR_BAR_DECISION.value(), dto.getBarDecisionType());

        verify(configService).addParameterValue(context, IntegrationConfigParamName.PAST_DAYS.value(Constants.RATCHET), "365");
        verify(configService).addParameterValue(context, IntegrationConfigParamName.FUTURE_DAYS.value(), "365");
        verify(configService).addParameterValue(context, IntegrationConfigParamName.NEW_RM_COST_RAW.value(Constants.RATCHET), "true");

        verify(configService).addParameterValue(context, IntegrationConfigParamName.CORE_PROPERTY_EXTERNAL_SYSTEM.value(), ReservationSystem.NGI.getConfigParameterValue());
        verify(configService).addParameterValue(context, IntegrationConfigParamName.CORE_PROPERTY_EXTERNAL_SYSTEM_SUB_SYSTEM.value(), ExternalSubSystem.HTNG.getConfigParameterValue());
        verify(configService).addParameterValue(context, IntegrationConfigParamName.APPLY_VARIABLE_DECISION_WINDOW.value(), Constants.FALSE);
        verify(configService).addParameterValue(context, FeatureTogglesConfigParamName.G3_RMS_CORE_SUBSCRIPTION_TYPE.value(), "Not Defined");
        verify(configService).addParameterValue(context, FeatureTogglesConfigParamName.G3_CONFIGURATION_METHOD.value(), "Manual");

        verify(configService).addParameterValue(context, IntegrationConfigParamName.CORE_PROPERTY_CONFIGURATION_COMPLETE.value(), "false");
        verify(configService).addParameterValue(context, PreProductionConfigParamName.MULTI_UNIT_GNR_ENABLED.value(), Constants.TRUE);
        verify(configService).addParameterValue(context, PreProductionConfigParamName.BOOKED_VS_ORIGINAL_ENABLED.value(), Constants.TRUE);

        verify(propertyConfigurationLoaderService).setValuesForOvrdPropertyAttributeTable();
        verify(propertyConfigurationLoaderService).setIpConfigRuntimeParamTable();
        verify(propertyConfigurationLoaderService).setBookingWindowStartDateValueForGroupAndNoFcst();
        verify(propertyConfigurationLoaderService).getPropertySetupConfiguration(1111, "prop2222");
        verify(propertyConfigurationLoaderService).getPropertyNewAttributeConfiguration(1111, "prop2222");
        verifyNoMoreInteractions(propertyConfigurationLoaderService);
    }

    @Test
    public void testDoInvokeHilstarPropertyAttributeDtoReturnsBrandCodeNull() throws Exception {
        jobParametersBuilder.addString(JobParameterKey.SUBSCRIPTION_TYPE, "Not Defined");
        jobParametersBuilder.addString(JobParameterKey.CONFIGURATION_METHOD, "Manual");
        jobParametersBuilder.addString(JobParameterKey.EXTERNAL_SYSTEM, ReservationSystem.NGI.getConfigParameterValue());
        jobParametersBuilder.addString(JobParameterKey.EXTERNAL_SUB_SYSTEM, ExternalSubSystem.HTNG.getConfigParameterValue());
        generateStepExecutionFromMetadata();

        JobExecutionUtil.setOnExecutionContext(jobExecution, JobExecutionContextKey.INBOUND_INTEGRATION_TYPE, ReservationSystem.HILSTAR);

        PropertySetupPropertyConfigurationDto dto = createPropertyConfigurationDto();
        when(propertyConfigurationLoaderService.getPropertySetupConfiguration(jobWorkContext.getClientId(), jobWorkContext.getPropertyCode())).thenReturn(dto);

        var attributeCfgDto = createPropertyAttributePropertyNewAttributeConfigurationDto(null);
        when(propertyConfigurationLoaderService.getPropertyNewAttributeConfiguration(
                jobWorkContext.getClientId(), jobWorkContext.getPropertyCode())
        ).thenReturn(attributeCfgDto);
        when(configService.getBooleanParameterValue(FeatureTogglesConfigParamName.SYNC_PROPERTY_ATTRIBUTE_PAIRING_CHANGES_FROM_FDS)).thenReturn(Boolean.TRUE);

        step.doInvoke(stepExecution, jobWorkContext);

        verifyCommonParameters(false);
        verify(configService).addParameterValue(context, IPConfigParamName.BAR_ALLOW_MIN_MAX_LOSOVERRIDE.value(), dto.isAllowMinMaxLOSForBar().toString());
        verify(configService).addParameterValue(context, IPConfigParamName.BAR_ALLOW_AVAILABLE_FOR_ARRIVAL.value(), dto.isAllowDayOfArrivalAvailability().toString());
        verify(configService).addParameterValue(context, IntegrationConfigParamName.DEFAULT_MARKET_SEGMENT.value(Constants.RATCHET), dto.getDefaultMarketSegment());
        verify(configService).addParameterValue(context, IPConfigParamName.BAR_BAR_OVRD_DISPLAY_COMPETITOR.value(), dto.getCompetitivePriceOption());
        verify(configService).addParameterValue(context, IPConfigParamName.BAR_BAR_DECISION.value(), dto.getBarDecisionType());

        verify(configService).addParameterValue(context, IntegrationConfigParamName.PAST_DAYS.value(Constants.RATCHET), "365");
        verify(configService).addParameterValue(context, IntegrationConfigParamName.FUTURE_DAYS.value(), "365");
        verify(configService).addParameterValue(context, IntegrationConfigParamName.NEW_RM_COST_RAW.value(Constants.RATCHET), "true");

        verify(configService).addParameterValue(context, IntegrationConfigParamName.CORE_PROPERTY_EXTERNAL_SYSTEM.value(), ReservationSystem.NGI.getConfigParameterValue());
        verify(configService).addParameterValue(context, IntegrationConfigParamName.CORE_PROPERTY_EXTERNAL_SYSTEM_SUB_SYSTEM.value(), ExternalSubSystem.HTNG.getConfigParameterValue());

        verify(configService).addParameterValue(context, IntegrationConfigParamName.CORE_PROPERTY_CONFIGURATION_COMPLETE.value(), "false");
        verify(configService).addParameterValue(context, PreProductionConfigParamName.MULTI_UNIT_GNR_ENABLED.value(), Constants.TRUE);
        verify(configService).addParameterValue(context, PreProductionConfigParamName.BOOKED_VS_ORIGINAL_ENABLED.value(), Constants.FALSE);
        verify(configService).addParameterValue(context, FeatureTogglesConfigParamName.G3_RMS_CORE_SUBSCRIPTION_TYPE.value(), "Not Defined");
        verify(configService).addParameterValue(context, FeatureTogglesConfigParamName.G3_CONFIGURATION_METHOD.value(), "Manual");

        verify(propertyConfigurationLoaderService).setValuesForOvrdPropertyAttributeTable();
        verify(propertyConfigurationLoaderService).setIpConfigRuntimeParamTable();
        verify(propertyConfigurationLoaderService).setBookingWindowStartDateValueForGroupAndNoFcst();
        verify(propertyConfigurationLoaderService).getPropertySetupConfiguration(1111, "prop2222");
        verify(propertyConfigurationLoaderService).getPropertyNewAttributeConfiguration(1111, "prop2222");
        verifyNoMoreInteractions(propertyConfigurationLoaderService);
    }

    private PropertySetupPropertyConfigurationDto createPropertyConfigurationDto() {
        return createPropertyConfigurationDto(null);
    }

    private PropertySetupPropertyConfigurationDto createPropertyConfigurationDto(String timezone) {
        PropertySetupPropertyConfigurationDto dto = new PropertySetupPropertyConfigurationDto();
        dto.setPropertyTimezone(timezone);
        dto.setAllowMinMaxLOSForBar(true);
        dto.setAllowDayOfArrivalAvailability(true);
        dto.setDefaultMarketSegment("defaultMS");
        dto.setBarDecisionType("bar123");
        dto.setCompetitivePriceOption("compPriceOption");
        return dto;
    }

    private PropertyAttributePropertyNewAttributeConfigurationDto createPropertyAttributePropertyNewAttributeConfigurationDto(String brandCode) {
        var dto = new PropertyAttributePropertyNewAttributeConfigurationDto();
        dto.setBrandCode(brandCode);
        return dto;
    }

    private PropertyAttributePropertyNewAttributeConfigurationDto createPropertyAttributePropertyNewAttributeConfigurationDto() {
        return createPropertyAttributePropertyNewAttributeConfigurationDto("PO");
    }

    @Test
    public void testDoInvokePCRS_BrandCode() throws Exception {
        jobParametersBuilder.addString(JobParameterKey.EXTERNAL_SYSTEM, ReservationSystem.NGI.getConfigParameterValue());
        jobParametersBuilder.addString(JobParameterKey.EXTERNAL_SUB_SYSTEM, ExternalSubSystem.HTNG.getConfigParameterValue());
        jobParametersBuilder.addString(JobParameterKey.SUBSCRIPTION_TYPE, "Not Defined");
        jobParametersBuilder.addString(JobParameterKey.CONFIGURATION_METHOD, "Manual");
        generateStepExecutionFromMetadata();

        JobExecutionUtil.setOnExecutionContext(jobExecution, JobExecutionContextKey.INBOUND_INTEGRATION_TYPE, ReservationSystem.PCRS);

        PropertySetupPropertyConfigurationDto dto = createPropertyConfigurationDto();
        when(propertyConfigurationLoaderService.getPropertySetupConfiguration(jobWorkContext.getClientId(), jobWorkContext.getPropertyCode())).thenReturn(dto);

        var attributeCfgDto = createPropertyAttributePropertyNewAttributeConfigurationDto();
        when(propertyConfigurationLoaderService.getPropertyNewAttributeConfiguration(
                jobWorkContext.getClientId(), jobWorkContext.getPropertyCode())
        ).thenReturn(attributeCfgDto);
        when(configService.getBooleanParameterValue(FeatureTogglesConfigParamName.SYNC_PROPERTY_ATTRIBUTE_PAIRING_CHANGES_FROM_FDS)).thenReturn(Boolean.TRUE);

        step.doInvoke(stepExecution, jobWorkContext);

        verifyCommonParameters(false);
        verify(configService).addParameterValue(context, IPConfigParamName.BAR_ALLOW_MIN_MAX_LOSOVERRIDE.value(), dto.isAllowMinMaxLOSForBar().toString());
        verify(configService).addParameterValue(context, IPConfigParamName.BAR_ALLOW_AVAILABLE_FOR_ARRIVAL.value(), dto.isAllowDayOfArrivalAvailability().toString());
        verify(configService).addParameterValue(context, IntegrationConfigParamName.DEFAULT_MARKET_SEGMENT.value(Constants.RATCHET), dto.getDefaultMarketSegment());
        verify(configService).addParameterValue(context, IPConfigParamName.BAR_BAR_OVRD_DISPLAY_COMPETITOR.value(), dto.getCompetitivePriceOption());
        verify(configService).addParameterValue(context, IPConfigParamName.BAR_BAR_DECISION.value(), dto.getBarDecisionType());

        verify(configService).addParameterValue(context, IntegrationConfigParamName.PAST_DAYS.value(Constants.RATCHET), "365");
        verify(configService).addParameterValue(context, IntegrationConfigParamName.FUTURE_DAYS.value(), "365");
        verify(configService).addParameterValue(context, IntegrationConfigParamName.NEW_RM_COST_RAW.value(Constants.RATCHET), "true");

        verify(configService).addParameterValue(context, IntegrationConfigParamName.CORE_PROPERTY_EXTERNAL_SYSTEM.value(), ReservationSystem.NGI.getConfigParameterValue());
        verify(configService).addParameterValue(context, IntegrationConfigParamName.CORE_PROPERTY_EXTERNAL_SYSTEM_SUB_SYSTEM.value(), ExternalSubSystem.HTNG.getConfigParameterValue());

        verify(configService).addParameterValue(context, IntegrationConfigParamName.CORE_PROPERTY_CONFIGURATION_COMPLETE.value(), "false");

        verify(configService).addParameterValue(context, IPConfigParamName.FORECASTING_FORECAST_WINDOW_BDE.value(), "358");
        verify(configService).addParameterValue(context, IPConfigParamName.OPTIMIZATION_OPTIMIZATION_WINDOW_BDE.value(), "358");
        verify(configService).addParameterValue(context, IntegrationConfigParamName.UPLOAD_DECISION_UPLOAD_WINDOW_BDEDAYS.value(), "358");
        verify(configService).addParameterValue(context, FeatureTogglesConfigParamName.G3_RMS_CORE_SUBSCRIPTION_TYPE.value(), "Not Defined");
        verify(configService).addParameterValue(context, FeatureTogglesConfigParamName.G3_CONFIGURATION_METHOD.value(), "Manual");

        verify(configService).addParameterValue(context, PreProductionConfigParamName.MULTI_UNIT_GNR_ENABLED.value(), Constants.TRUE);
        verify(configService).addParameterValue(context, PreProductionConfigParamName.BOOKED_VS_ORIGINAL_ENABLED.value(), Constants.TRUE);

        verify(propertyConfigurationLoaderService).setValuesForOvrdPropertyAttributeTable();
        verify(propertyConfigurationLoaderService).setIpConfigRuntimeParamTable();
        verify(propertyConfigurationLoaderService).setBookingWindowStartDateValueForGroupAndNoFcst();
        verify(propertyConfigurationLoaderService).getPropertySetupConfiguration(1111, "prop2222");
        verify(propertyConfigurationLoaderService).getPropertyNewAttributeConfiguration(1111, "prop2222");
        verifyNoMoreInteractions(propertyConfigurationLoaderService);
    }

    @Test
    public void testDoInvokeOperaWithOptionalParameters() throws Exception {
        jobParametersBuilder.addString(JobParameterKey.WEB_RATE_ALIAS, "alias2222");
        jobParametersBuilder.addString(JobParameterKey.CRS_TIMEZONE, "crs/timezone");
        jobParametersBuilder.addString(JobParameterKey.ENABLEYC, "false");
        jobParametersBuilder.addString(JobParameterKey.BASE_CURRENCY, "someCurrency");
        jobParametersBuilder.addString(JobParameterKey.SUBSCRIPTION_TYPE, "Not Defined");
        jobParametersBuilder.addString(JobParameterKey.CONFIGURATION_METHOD, "Manual");
        jobParametersBuilder.addString(JobParameterKey.PROPERTY_BUILD_TYPE, "HOT_START");
        generateStepExecutionFromMetadata();

        JobExecutionUtil.setOnExecutionContext(jobExecution, JobExecutionContextKey.INBOUND_INTEGRATION_TYPE, ReservationSystem.OPERA);

        step.doInvoke(stepExecution, jobWorkContext);

        verifyCommonParameters(true);
        verify(configService).addParameterValue(context, IntegrationConfigParamName.CORE_PROPERTY_APPLY_YIELD_CURRENCY.value(), "false");
        verify(configService).addParameterValue(context, IntegrationConfigParamName.CORE_PROPERTY_BASE_CURRENCY_CODE.value(), "someCurrency");
        verify(configService).addParameterValue(context, FeatureTogglesConfigParamName.BACKFILL_ENABLED.value(), "true");
        verify(configService).addParameterValue(context, FeatureTogglesConfigParamName.G3_RMS_CORE_SUBSCRIPTION_TYPE.value(), "Not Defined");
        verify(configService).addParameterValue(context, FeatureTogglesConfigParamName.G3_CONFIGURATION_METHOD.value(), "Manual");
        verify(configService).addParameterValue(context, IS_HOT_START_PROPERTY.getParameterName(), TRUE);
        verify(configService).addParameterValue(context, CORE_PROPERTY_MIN_CONSECUTIVE_DAYS_THRESHOLD.getParameterName(), "1000");

        verifyNoMoreInteractions(configService);
        verify(propertyConfigurationLoaderService).setBookingWindowStartDateValueForGroupAndNoFcst();
        verifyNoMoreInteractions(propertyConfigurationLoaderService);
    }

    @Test
    public void forHiltonPropertiesQuestionnaireAndSystemAccessRelatedParametersWontBeUpdated_BrandCode() throws Exception {
        generateStepExecutionFromMetadata();
        JobExecutionUtil.setOnExecutionContext(jobExecution, JobExecutionContextKey.INBOUND_INTEGRATION_TYPE, ReservationSystem.PCRS);
        jobParametersBuilder.addString(JobParameterKey.SUBSCRIPTION_TYPE, "Not Defined");
        jobParametersBuilder.addString(JobParameterKey.CONFIGURATION_METHOD, "Manual");
        when(externalSystemHelper.isHilstar()).thenReturn(true);
        when(configService.getParameterValue(IntegrationConfigParamName.CORE_PROPERTY_EXTERNAL_SYSTEM)).thenReturn("PCRS");
        PropertySetupPropertyConfigurationDto propertyConfigurationDto = createPropertyConfigurationDto();
        var propertyAttributeDto = createPropertyAttributePropertyNewAttributeConfigurationDto();
        when(propertyConfigurationLoaderService.getPropertySetupConfiguration(jobWorkContext.getClientId(), jobWorkContext.getPropertyCode())).thenReturn(propertyConfigurationDto);
        when(propertyConfigurationLoaderService.getPropertyNewAttributeConfiguration(
                jobWorkContext.getClientId(), jobWorkContext.getPropertyCode())
        ).thenReturn(propertyAttributeDto);
        when(configService.getBooleanParameterValue(FeatureTogglesConfigParamName.SYNC_PROPERTY_ATTRIBUTE_PAIRING_CHANGES_FROM_FDS)).thenReturn(Boolean.TRUE);
        generateStepExecutionFromMetadata();

        step.doInvoke(stepExecution, jobWorkContext);

        verify(configService, never()).addParameterValue(context, GUIConfigParamName.IS_PROPERTY_READY_FOR_EXTERNAL_USER.value(), Constants.FALSE);
        verify(configService, never()).addParameterValue(context, FeatureTogglesConfigParamName.ENABLE_CLIENT_QUESTIONNAIRE.value(), Constants.TRUE);
    }

    @Test
    public void testDoInvokeOperaWithOptionalParameters_LDB() throws Exception {
        jobParametersBuilder.addString(JobParameterKey.WEB_RATE_ALIAS, "alias2222");
        jobParametersBuilder.addString(JobParameterKey.CRS_TIMEZONE, "crs/timezone");
        jobParametersBuilder.addString(JobParameterKey.ENABLEYC, "false");
        jobParametersBuilder.addString(JobParameterKey.BASE_CURRENCY, "someCurrency");
        jobParametersBuilder.addString(JobParameterKey.LIMITED_DATA_BUILD, "true");
        jobParametersBuilder.addString(JobParameterKey.SUBSCRIPTION_TYPE, "Not Defined");
        jobParametersBuilder.addString(JobParameterKey.CONFIGURATION_METHOD, "Manual");
        generateStepExecutionFromMetadata();

        JobExecutionUtil.setOnExecutionContext(jobExecution, JobExecutionContextKey.INBOUND_INTEGRATION_TYPE, ReservationSystem.OPERA);

        step.doInvoke(stepExecution, jobWorkContext);

        verifyCommonParameters(true);
        verify(configService).addParameterValue(context, IntegrationConfigParamName.CORE_PROPERTY_APPLY_YIELD_CURRENCY.value(), "false");
        verify(configService).addParameterValue(context, IntegrationConfigParamName.CORE_PROPERTY_BASE_CURRENCY_CODE.value(), "someCurrency");
        verify(configService).addParameterValue(context, FeatureTogglesConfigParamName.G3_RMS_CORE_SUBSCRIPTION_TYPE.value(), "Not Defined");
        verify(configService).addParameterValue(context, FeatureTogglesConfigParamName.G3_CONFIGURATION_METHOD.value(), "Manual");

        verifyNoMoreInteractions(configService);
        verify(propertyConfigurationLoaderService).setBookingWindowStartDateValueForGroupAndNoFcst();
        verifyNoMoreInteractions(propertyConfigurationLoaderService);
    }

    @Test
    public void testDoInvokeRezview() throws Exception {
        jobParametersBuilder.addString(JobParameterKey.SUBSCRIPTION_TYPE, "Not Defined");
        jobParametersBuilder.addString(JobParameterKey.CONFIGURATION_METHOD, "Manual");
        generateStepExecutionFromMetadata();
        JobExecutionUtil.setOnExecutionContext(jobExecution, JobExecutionContextKey.INBOUND_INTEGRATION_TYPE, ReservationSystem.REZVIEW);

        step.doInvoke(stepExecution, jobWorkContext);

        verifyCommonParameters(false);
        verify(configService).addParameterValue(context, IntegrationConfigParamName.CORE_PROPERTY_CONFIGURATION_COMPLETE.value(), "false");
        verify(configService).addParameterValue(context, FeatureTogglesConfigParamName.G3_RMS_CORE_SUBSCRIPTION_TYPE.value(), "Not Defined");
        verify(configService).addParameterValue(context, FeatureTogglesConfigParamName.G3_CONFIGURATION_METHOD.value(), "Manual");
        verifyNoMoreInteractions(configService);
        verify(propertyConfigurationLoaderService).setBookingWindowStartDateValueForGroupAndNoFcst();
        verifyNoMoreInteractions(propertyConfigurationLoaderService);
    }

    @Test
    public void testDoInvokeRezviewWithOptionalParameters() throws Exception {
        jobParametersBuilder.addString(JobParameterKey.WEB_RATE_ALIAS, "alias2222");
        jobParametersBuilder.addString(JobParameterKey.CRS_TIMEZONE, "crs/timezone");
        jobParametersBuilder.addString(JobParameterKey.SUBSCRIPTION_TYPE, "Not Defined");
        jobParametersBuilder.addString(JobParameterKey.CONFIGURATION_METHOD, "Manual");
        generateStepExecutionFromMetadata();

        JobExecutionUtil.setOnExecutionContext(jobExecution, JobExecutionContextKey.INBOUND_INTEGRATION_TYPE, ReservationSystem.REZVIEW);

        step.doInvoke(stepExecution, jobWorkContext);

        verifyCommonParameters(true);
        verify(configService).addParameterValue(context, IntegrationConfigParamName.CORE_PROPERTY_CONFIGURATION_COMPLETE.value(), "false");
        verify(configService).addParameterValue(context, FeatureTogglesConfigParamName.G3_RMS_CORE_SUBSCRIPTION_TYPE.value(), "Not Defined");
        verify(configService).addParameterValue(context, FeatureTogglesConfigParamName.G3_CONFIGURATION_METHOD.value(), "Manual");
        verifyNoMoreInteractions(configService);
        verify(propertyConfigurationLoaderService).setBookingWindowStartDateValueForGroupAndNoFcst();
        verifyNoMoreInteractions(propertyConfigurationLoaderService);
    }

    @Test
    public void verifyOperaSpecificParametersareSet() throws Exception {
        jobParametersBuilder.addString(JobParameterKey.WEB_RATE_ALIAS, "alias2222");
        jobParametersBuilder.addString(JobParameterKey.CRS_TIMEZONE, "crs/timezone");
        jobParametersBuilder.addString(JobParameterKey.ENABLEYC, "false");
        jobParametersBuilder.addString(JobParameterKey.BASE_CURRENCY, "someCurrency");
        jobParametersBuilder.addString(JobParameterKey.SUBSCRIPTION_TYPE, "Not Defined");
        jobParametersBuilder.addString(JobParameterKey.CONFIGURATION_METHOD, "Manual");
        generateStepExecutionFromMetadata();

        JobExecutionUtil.setOnExecutionContext(jobExecution, JobExecutionContextKey.INBOUND_INTEGRATION_TYPE, ReservationSystem.OPERA);

        step.doInvoke(stepExecution, jobWorkContext);
        verifyCommonParameters(true);
        verify(configService).addParameterValue(context, IntegrationConfigParamName.CORE_PROPERTY_APPLY_YIELD_CURRENCY.value(), "false");
        verify(configService).addParameterValue(context, IntegrationConfigParamName.CORE_PROPERTY_BASE_CURRENCY_CODE.value(), "someCurrency");
        verify(configService).addParameterValue(context, FeatureTogglesConfigParamName.G3_RMS_CORE_SUBSCRIPTION_TYPE.value(), "Not Defined");
        verify(configService).addParameterValue(context, FeatureTogglesConfigParamName.G3_CONFIGURATION_METHOD.value(), "Manual");
        verify(configService).addParameterValue(context, FeatureTogglesConfigParamName.BACKFILL_ENABLED.value(), "true");
        verifyNoMoreInteractions(configService);
    }

    @Test
    public void testDoInvokeOpera() throws Exception {

        jobParametersBuilder.addString(JobParameterKey.ENABLEYC, "false");
        jobParametersBuilder.addString(JobParameterKey.BASE_CURRENCY, "someCurrency");
        jobParametersBuilder.addString(JobParameterKey.SUBSCRIPTION_TYPE, "Not Defined");
        jobParametersBuilder.addString(JobParameterKey.CONFIGURATION_METHOD, "Manual");
        generateStepExecutionFromMetadata();
        JobExecutionUtil.setOnExecutionContext(jobExecution, JobExecutionContextKey.INBOUND_INTEGRATION_TYPE, ReservationSystem.OPERA);

        step.doInvoke(stepExecution, jobWorkContext);

        verifyCommonParameters(false);
        verify(configService).addParameterValue(context, IntegrationConfigParamName.CORE_PROPERTY_APPLY_YIELD_CURRENCY.value(), "false");
        verify(configService).addParameterValue(context, IntegrationConfigParamName.CORE_PROPERTY_BASE_CURRENCY_CODE.value(), "someCurrency");
        verify(configService).addParameterValue(context, FeatureTogglesConfigParamName.BACKFILL_ENABLED.value(), "true");
        verify(configService).addParameterValue(context, FeatureTogglesConfigParamName.G3_RMS_CORE_SUBSCRIPTION_TYPE.value(), "Not Defined");
        verify(configService).addParameterValue(context, FeatureTogglesConfigParamName.G3_CONFIGURATION_METHOD.value(), "Manual");
        verifyNoMoreInteractions(configService);
    }

    @Test
    public void verifyOperaSpecificParametersareNotSetforOtherClients() throws Exception {
        jobParametersBuilder.addString(JobParameterKey.WEB_RATE_ALIAS, "alias2222");
        jobParametersBuilder.addString(JobParameterKey.CRS_TIMEZONE, "crs/timezone");
        jobParametersBuilder.addString(JobParameterKey.ENABLEYC, "false");
        jobParametersBuilder.addString(JobParameterKey.BASE_CURRENCY, "someCurrency");
        jobParametersBuilder.addString(JobParameterKey.SUBSCRIPTION_TYPE, "Not Defined");
        jobParametersBuilder.addString(JobParameterKey.CONFIGURATION_METHOD, "Manual");
        generateStepExecutionFromMetadata();

        JobExecutionUtil.setOnExecutionContext(jobExecution, JobExecutionContextKey.INBOUND_INTEGRATION_TYPE, ReservationSystem.REZVIEW);

        step.doInvoke(stepExecution, jobWorkContext);

        verifyCommonParameters(true);
        verify(configService).addParameterValue(context, IntegrationConfigParamName.CORE_PROPERTY_CONFIGURATION_COMPLETE.value(), "false");
        verify(configService).addParameterValue(context, FeatureTogglesConfigParamName.G3_RMS_CORE_SUBSCRIPTION_TYPE.value(), "Not Defined");
        verify(configService).addParameterValue(context, FeatureTogglesConfigParamName.G3_CONFIGURATION_METHOD.value(), "Manual");
        verifyNoMoreInteractions(configService);
    }

    @Test
    public void doInvokeSetExternalSubSystem() throws Exception {
        jobParametersBuilder.addString(JobParameterKey.WEB_RATE_ALIAS, "alias2222");
        jobParametersBuilder.addString(JobParameterKey.CRS_TIMEZONE, "crs/timezone");
        jobParametersBuilder.addString(JobParameterKey.EXTERNAL_SUB_SYSTEM, ExternalSubSystem.OXI.getConfigParameterValue());
        jobParametersBuilder.addString(JobParameterKey.SUBSCRIPTION_TYPE, "Not Defined");
        jobParametersBuilder.addString(JobParameterKey.CONFIGURATION_METHOD, "Manual");
        generateStepExecutionFromMetadata();

        step.doInvoke(stepExecution, jobWorkContext);

        verifyCommonParameters(true);
        verify(configService).addParameterValue(context, IntegrationConfigParamName.CORE_PROPERTY_EXTERNAL_SYSTEM_SUB_SYSTEM.value(), ExternalSubSystem.OXI.getConfigParameterValue());
        verify(configService).addParameterValue(context, FeatureTogglesConfigParamName.BACKFILL_ENABLED.value(), "true");
        verify(configService).addParameterValue(context, IntegrationConfigParamName.CORE_PROPERTY_CONFIGURATION_COMPLETE.value(), "false");
        verify(configService).addParameterValue(context, FeatureTogglesConfigParamName.G3_RMS_CORE_SUBSCRIPTION_TYPE.value(), "Not Defined");
        verify(configService).addParameterValue(context, FeatureTogglesConfigParamName.G3_CONFIGURATION_METHOD.value(), "Manual");

        verifyNoMoreInteractions(configService);
    }

    @Test
    public void doInvokeSetRemoteTaskWhenOxi() throws Exception {
        jobParametersBuilder.addString(JobParameterKey.WEB_RATE_ALIAS, "alias2222");
        jobParametersBuilder.addString(JobParameterKey.CRS_TIMEZONE, "crs/timezone");
        jobParametersBuilder.addString(JobParameterKey.SUBSCRIPTION_TYPE, "Not Defined");
        jobParametersBuilder.addString(JobParameterKey.CONFIGURATION_METHOD, "Manual");
        generateStepExecutionFromMetadata();

        when(externalSystemHelper.isOXI()).thenReturn(true);

        step.doInvoke(stepExecution, jobWorkContext);

        verifyCommonParameters(true);
        verify(configService).addParameterValue(context, FeatureTogglesConfigParamName.BACKFILL_ENABLED.value(), "true");
        verify(configService).addParameterValue(context, IntegrationConfigParamName.CORE_PROPERTY_CONFIGURATION_COMPLETE.value(), "false");
        verify(configService).addParameterValue(context, FeatureTogglesConfigParamName.G3_RMS_CORE_SUBSCRIPTION_TYPE.value(), "Not Defined");
        verify(configService).addParameterValue(context, FeatureTogglesConfigParamName.G3_CONFIGURATION_METHOD.value(), "Manual");

        verifyNoMoreInteractions(configService);
    }

    @Test
    public void doInvokeNotSetRemoteTaskWhenNotOxi() throws Exception {
        jobParametersBuilder.addString(JobParameterKey.WEB_RATE_ALIAS, "alias2222");
        jobParametersBuilder.addString(JobParameterKey.CRS_TIMEZONE, "crs/timezone");
        jobParametersBuilder.addString(JobParameterKey.SUBSCRIPTION_TYPE, "Not Defined");
        jobParametersBuilder.addString(JobParameterKey.CONFIGURATION_METHOD, "Manual");
        generateStepExecutionFromMetadata();

        when(externalSystemHelper.isOXI()).thenReturn(false);

        step.doInvoke(stepExecution, jobWorkContext);

        verifyCommonParameters(true);
        verify(configService).addParameterValue(context, FeatureTogglesConfigParamName.BACKFILL_ENABLED.value(), "true");
        verify(configService).addParameterValue(context, IntegrationConfigParamName.CORE_PROPERTY_CONFIGURATION_COMPLETE.value(), "false");
        verify(configService).addParameterValue(context, FeatureTogglesConfigParamName.G3_RMS_CORE_SUBSCRIPTION_TYPE.value(), "Not Defined");
        verify(configService).addParameterValue(context, FeatureTogglesConfigParamName.G3_CONFIGURATION_METHOD.value(), "Manual");
        verifyNoMoreInteractions(configService);
    }

    private void verifyCommonParameters(boolean includeOptional) {
        verify(configService).addParameterValue(context, IntegrationConfigParamName.CORE_PROPERTY_TIME_ZONE.value(), "property/timezone");
        verify(configService).addParameterValue(context, GUIConfigParamName.CORE_PROPERTY_YIELD_CURRENCY_CODE.value(), "someCurrency");
        verify(configService).addParameterValue(context, IntegrationConfigParamName.YIELD_CURRENCY_CODE.value(Constants.RATCHET), "someCurrency");

        for (SyncEvent syncEvent : SyncEvent.values()) {
            verify(configService).addParameterValue(context, syncEvent.getGlobalParameter(), "false");
        }
        if (includeOptional) {
            verify(configService).addParameterValue(context, IPConfigParamName.BAR_WEB_RATE_ALIAS.value(), "alias2222");
            verify(configService).addParameterValue(context, IntegrationConfigParamName.CRS_TIME_ZONE.value(), "crs/timezone");
        }
    }

    @Test
    public void forNGIPropertiesQuestionnaireFlagWouldBeTurnedOnAndSystemAccessFlagWouldBeTurnedOff() throws Exception {
        jobParametersBuilder.addString(JobParameterKey.SUBSCRIPTION_TYPE, "Not Defined");
        jobParametersBuilder.addString(JobParameterKey.CONFIGURATION_METHOD, "Manual");
        generateStepExecutionFromMetadata();
        JobExecutionUtil.setOnExecutionContext(jobExecution, JobExecutionContextKey.INBOUND_INTEGRATION_TYPE, ReservationSystem.NGI);

        step.doInvoke(stepExecution, jobWorkContext);

        verify(configService).addParameterValue(context, GUIConfigParamName.IS_PROPERTY_READY_FOR_EXTERNAL_USER.value(), Constants.FALSE);
        verify(configService).addParameterValue(context, FeatureTogglesConfigParamName.ENABLE_CLIENT_QUESTIONNAIRE.value(), Constants.TRUE);
    }

    @Test
    public void testRelatedParameterChangesLimitedEdition() throws Exception {

        jobParametersBuilder.addString(JobParameterKey.SUBSCRIPTION_TYPE, SubscriptionEnum.LIMITED_EDITION.getSubscriptionType());
        generateStepExecutionFromMetadata();
        JobExecutionUtil.setOnExecutionContext(jobExecution, JobExecutionContextKey.INBOUND_INTEGRATION_TYPE, ReservationSystem.NGI);
        step.doInvoke(stepExecution, jobWorkContext);

        verify(configService).addParameterValue(context, FeatureTogglesConfigParamName.HIDE_ADVANCED_SETTINGS_AND_RTO_OVERBOOKING_TYPE.value(), Constants.TRUE);
        verify(configService).addParameterValue(context, FeatureTogglesConfigParamName.MAX_UPLOADED_AGILE_RATES.value(), "5");
        verify(configService).addParameterValue(context, FeatureTogglesConfigParamName.FORECAST_DASHBOARD_ENABLED.value(), Constants.FALSE);
        verify(configService).addParameterValue(context, IntegrationConfigParamName.CORE_PROPERTY_CDP_DAILYMAX.value(), "2");
        verify(configService).addParameterValue(context, IPConfigParamName.OPTIMIZATION_OPTIMIZATION_WINDOW_CDP.value(), "30");
        verify(configService).addParameterValue(context, FeatureTogglesConfigParamName.WHAT_IF_ENABLED.value(), Constants.FALSE);
        verify(configService).addParameterValue(context, FeatureTogglesConfigParamName.MAX_ACTIVE_AGILE_RATES.value(), "5");

    }

    @Test
    public void testRelatedParameterChangesStandardEdition() throws Exception {

        jobParametersBuilder.addString(JobParameterKey.SUBSCRIPTION_TYPE, SubscriptionEnum.STANDARD_EDITION.getSubscriptionType());
        generateStepExecutionFromMetadata();
        JobExecutionUtil.setOnExecutionContext(jobExecution, JobExecutionContextKey.INBOUND_INTEGRATION_TYPE, ReservationSystem.NGI);
        step.doInvoke(stepExecution, jobWorkContext);

        verify(configService).addParameterValue(context, FeatureTogglesConfigParamName.HIDE_ADVANCED_SETTINGS_AND_RTO_OVERBOOKING_TYPE.value(), Constants.FALSE);
        verify(configService).addParameterValue(context, FeatureTogglesConfigParamName.MAX_UPLOADED_AGILE_RATES.value(), "5");
        verify(configService).addParameterValue(context, FeatureTogglesConfigParamName.FORECAST_DASHBOARD_ENABLED.value(), Constants.TRUE);
        verify(configService).addParameterValue(context, IntegrationConfigParamName.CORE_PROPERTY_CDP_DAILYMAX.value(), "3");
        verify(configService).addParameterValue(context, FeatureTogglesConfigParamName.WHAT_IF_ENABLED.value(), Constants.TRUE);
        verify(configService).addParameterValue(context, FeatureTogglesConfigParamName.MAX_ACTIVE_AGILE_RATES.value(), "5");

    }

    @Test
    public void testRelatedParameterChangesPlusEdition() throws Exception {

        jobParametersBuilder.addString(JobParameterKey.SUBSCRIPTION_TYPE, SubscriptionEnum.PLUS_EDITION.getSubscriptionType());
        generateStepExecutionFromMetadata();
        JobExecutionUtil.setOnExecutionContext(jobExecution, JobExecutionContextKey.INBOUND_INTEGRATION_TYPE, ReservationSystem.NGI);
        step.doInvoke(stepExecution, jobWorkContext);

        verify(configService).addParameterValue(context, FeatureTogglesConfigParamName.HIDE_ADVANCED_SETTINGS_AND_RTO_OVERBOOKING_TYPE.value(), Constants.FALSE);
        verify(configService).addParameterValue(context, FeatureTogglesConfigParamName.MAX_UPLOADED_AGILE_RATES.value(), "10");
        verify(configService).addParameterValue(context, FeatureTogglesConfigParamName.FORECAST_DASHBOARD_ENABLED.value(), Constants.TRUE);
        verify(configService).addParameterValue(context, IntegrationConfigParamName.CORE_PROPERTY_CDP_DAILYMAX.value(), "3");
        verify(configService).addParameterValue(context, FeatureTogglesConfigParamName.WHAT_IF_ENABLED.value(), Constants.TRUE);
        verify(configService).addParameterValue(context, FeatureTogglesConfigParamName.MAX_ACTIVE_AGILE_RATES.value(), "10");
        verify(configService).addParameterValue(context, FeatureTogglesConfigParamName.GROUP_PRICING_ENABLED.value(), Constants.TRUE);

    }

    @Test
    void shouldConfigureLicensePackageForNewPropertyWhenLicensePackageNotInSubscriptionEnum() throws Exception {
        LicensePackage licencePackage = LicensePackage.builder().id(1).packageName(LICENSE_PACKAGE).build();

        when(globalCrudService.findByNamedQuerySingleResult(GET_LICENSE_PACKAGE_BY_NAME,
                QueryParameter.with("packageName", LICENSE_PACKAGE).parameters())).thenReturn(licencePackage);

        jobParametersBuilder.addString(JobParameterKey.CONFIGURATION_METHOD, "Manual");
        jobParametersBuilder.addString(JobParameterKey.SUBSCRIPTION_TYPE, LICENSE_PACKAGE);
        generateStepExecutionFromMetadata();
        JobExecutionUtil.setOnExecutionContext(jobExecution, JobExecutionContextKey.INBOUND_INTEGRATION_TYPE, ReservationSystem.NGI);

        step.doInvoke(stepExecution, jobWorkContext);

        verify(licenseService).updatePropertyLicenceMapping(LICENSE_PACKAGE, jobWorkContext.getPropertyId());
    }

    @Test
    void shouldDoNothingForNewPropertyWhenLicensePackageIsPresentInEnum() throws Exception {
        jobParametersBuilder.addString(JobParameterKey.SUBSCRIPTION_TYPE, "Not Defined");
        jobParametersBuilder.addString(JobParameterKey.CONFIGURATION_METHOD, "Manual");
        generateStepExecutionFromMetadata();
        JobExecutionUtil.setOnExecutionContext(jobExecution, JobExecutionContextKey.INBOUND_INTEGRATION_TYPE, ReservationSystem.NGI);
        step.doInvoke(stepExecution, jobWorkContext);

        verifyNoInteractions(licenseService);
        verifyNoInteractions(globalCrudService);
    }
}
