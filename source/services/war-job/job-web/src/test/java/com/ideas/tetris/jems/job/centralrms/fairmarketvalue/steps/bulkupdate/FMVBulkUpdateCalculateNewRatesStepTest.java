package com.ideas.tetris.jems.job.centralrms.fairmarketvalue.steps.bulkupdate;

import com.ideas.tetris.jems.core.job.context.JobExecutionUtil;
import com.ideas.tetris.jems.core.job.entity.SerializableJobExecutionParam;
import com.ideas.tetris.jems.core.repository.dao.SerializableJobExecutionParamDao;
import com.ideas.tetris.jems.core.step.tasklet.AbstractTaskletStep;
import com.ideas.tetris.pacman.services.centralrms.fmv.entity.FairMarketValuePriceConfig;
import com.ideas.tetris.pacman.services.centralrms.models.fairmarketvalue.FairMarketValueBulkUpdateV2;
import com.ideas.tetris.pacman.services.centralrms.services.fairmarketvalue.CentralRMSFairMarketValueConfigurationService;
import com.ideas.tetris.platform.common.job.JobParameterKey;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.batch.core.JobParameter;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.core.StepExecution;
import org.springframework.batch.test.MetaDataInstanceFactory;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.ideas.tetris.jems.core.job.context.JobExecutionContextKey.FAIR_MARKET_VALUE_CONFIGS_TO_UPDATE;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class FMVBulkUpdateCalculateNewRatesStepTest {

    @Mock
    private CentralRMSFairMarketValueConfigurationService centralRMSFairMarketValueConfigurationService;

    @Mock
    private SerializableJobExecutionParamDao serializableJobExecutionParamDao;

    @InjectMocks
    private FMVBulkUpdateCalculateNewRatesStep step;

    @Test
    void isStepAsync() {
        assertFalse(step.isStepAsync(null));
    }

    @Test
    void doInvoke() throws Exception {
        int serializableJobExecutionParamId = 1;
        String userId = "11403";
        String propertyId = "1000";
        Map<String, JobParameter> parameters = new HashMap<>();
        parameters.put(JobParameterKey.USER_ID, new JobParameter(userId));
        parameters.put(JobParameterKey.PROPERTY_ID, new JobParameter(propertyId));
        parameters.put(JobParameterKey.INCOMING_SERIALIZABLE, new JobParameter(Long.valueOf(serializableJobExecutionParamId)));

        JobParameters jobParameters = new JobParameters(parameters);
        StepExecution stepExecution = MetaDataInstanceFactory.createStepExecution(jobParameters);

        FairMarketValuePriceConfig configToUpdate = new FairMarketValuePriceConfig();
        configToUpdate.setMondayScale(BigDecimal.TEN);

        FairMarketValuePriceConfig updatedConfig = new FairMarketValuePriceConfig();
        updatedConfig.setMondayScale(BigDecimal.ONE);

        FairMarketValueBulkUpdateV2 bulkUpdate = new FairMarketValueBulkUpdateV2();
        SerializableJobExecutionParam serializableJobExecutionParam = new SerializableJobExecutionParam();
        serializableJobExecutionParam.setId(serializableJobExecutionParamId);
        serializableJobExecutionParam.setSerializable(bulkUpdate);

        when(serializableJobExecutionParamDao.getSerializableJobExecutionParam(serializableJobExecutionParamId)).thenReturn(serializableJobExecutionParam);

        List<FairMarketValuePriceConfig> toUpdate = List.of(configToUpdate);
        List<FairMarketValuePriceConfig> updated = List.of(updatedConfig);
        stepExecution.getJobExecution().getExecutionContext().put(FAIR_MARKET_VALUE_CONFIGS_TO_UPDATE, toUpdate);

        when(centralRMSFairMarketValueConfigurationService.calculateNewRates(toUpdate, bulkUpdate)).thenReturn(updated);

        step.doInvoke(stepExecution, null);

        verify(centralRMSFairMarketValueConfigurationService).calculateNewRates(toUpdate, bulkUpdate);
        assertEquals(updated, JobExecutionUtil.getFromExecutionContext(stepExecution.getJobExecution(), FAIR_MARKET_VALUE_CONFIGS_TO_UPDATE));
    }

    @Test
    public void shouldExecuteStepTrue() {
        int serializableJobExecutionParamId = 1;
        String userId = "11403";
        String propertyId = "1000";
        Map<String, JobParameter> parameters = new HashMap<>();
        parameters.put(JobParameterKey.USER_ID, new JobParameter(userId));
        parameters.put(JobParameterKey.PROPERTY_ID, new JobParameter(propertyId));
        parameters.put(JobParameterKey.INCOMING_SERIALIZABLE, new JobParameter(Long.valueOf(serializableJobExecutionParamId)));

        JobParameters jobParameters = new JobParameters(parameters);
        StepExecution stepExecution = MetaDataInstanceFactory.createStepExecution(jobParameters);
        FairMarketValuePriceConfig FairMarketValuePriceConfig = new FairMarketValuePriceConfig();
        FairMarketValuePriceConfig.setMondayScale(BigDecimal.TEN);

        FairMarketValuePriceConfig configToUpdate = new FairMarketValuePriceConfig();
        configToUpdate.setMondayScale(BigDecimal.TEN);

        stepExecution.getJobExecution().getExecutionContext().put(FAIR_MARKET_VALUE_CONFIGS_TO_UPDATE, List.of(configToUpdate));

        assertEquals(AbstractTaskletStep.ShouldExecuteStepResult.TRUE, step.shouldExecuteStep(stepExecution.getJobExecution(), null));
    }

    @Test
    public void shouldExecuteStepFalse() {
        int serializableJobExecutionParamId = 1;
        String userId = "11403";
        String propertyId = "1000";
        Map<String, JobParameter> parameters = new HashMap<>();
        parameters.put(JobParameterKey.USER_ID, new JobParameter(userId));
        parameters.put(JobParameterKey.PROPERTY_ID, new JobParameter(propertyId));
        parameters.put(JobParameterKey.INCOMING_SERIALIZABLE, new JobParameter(Long.valueOf(serializableJobExecutionParamId)));

        JobParameters jobParameters = new JobParameters(parameters);
        StepExecution stepExecution = MetaDataInstanceFactory.createStepExecution(jobParameters);

        stepExecution.getJobExecution().getExecutionContext().put(FAIR_MARKET_VALUE_CONFIGS_TO_UPDATE, Collections.emptyList());

        assertEquals(AbstractTaskletStep.ShouldExecuteStepResult.FALSE, step.shouldExecuteStep(stepExecution.getJobExecution(), null));
    }
}
