package com.ideas.tetris.jems.job.centralrms.overrides.pricing.advanced.steps;

import com.ideas.tetris.jems.core.job.context.JobExecutionUtil;
import com.ideas.tetris.jems.core.job.entity.SerializableJobExecutionParam;
import com.ideas.tetris.jems.core.repository.dao.SerializableJobExecutionParamDao;
import com.ideas.tetris.jems.core.step.tasklet.AbstractTaskletStep;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomClass;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomType;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.CPDecisionBAROutput;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.CPDecisionBAROutputOverride;
import com.ideas.tetris.pacman.services.centralrms.models.common.SpecialEventSettings;
import com.ideas.tetris.pacman.services.centralrms.models.overrides.pricing.AdvancedAction;
import com.ideas.tetris.pacman.services.centralrms.models.overrides.pricing.AdvancedPricingOverrideConfig;
import com.ideas.tetris.pacman.services.centralrms.models.overrides.pricing.AdvancedPricingOverrideRequest;
import com.ideas.tetris.pacman.services.centralrms.models.overrides.pricing.AdvancedPricingOverrideSetting;
import com.ideas.tetris.pacman.services.centralrms.models.overrides.pricing.CentralRMSCPDecisionBAROutputRemovalModel;
import com.ideas.tetris.pacman.services.centralrms.models.pricing.common.OverrideType;
import com.ideas.tetris.pacman.services.centralrms.models.util.Tuple2;
import com.ideas.tetris.pacman.services.centralrms.repository.decision.pricing.CentralRMSCPDecisionBAROutputRepository;
import com.ideas.tetris.pacman.services.centralrms.repository.rooms.CentralRMSRoomClassRepository;
import com.ideas.tetris.pacman.services.centralrms.services.overrides.pricing.CentralRMSPricingOverridesRemovalModelPreparer;
import com.ideas.tetris.pacman.services.centralrms.services.seasons.strategies.CentralRMSSpecialEventOverlapSplitter;
import com.ideas.tetris.platform.common.job.JobParameterKey;
import org.apache.commons.collections.ListUtils;
import org.apache.commons.lang3.Range;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.batch.core.JobParameter;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.core.StepExecution;
import org.springframework.batch.test.MetaDataInstanceFactory;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static com.ideas.tetris.jems.core.job.context.JobExecutionContextKey.PRICING_OVERRIDES_TO_REMOVE;
import static com.ideas.tetris.jems.core.job.context.JobExecutionContextKey.PRICING_OVERRIDES_TO_REMOVE_BY_PERSISTENCE;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class CentralRMSPrepareAdvancePricingOverridesToRemoveStepTest {

    @Mock
    private CentralRMSCPDecisionBAROutputRepository centralRMSCPDecisionBAROutputRepository;

    @Mock
    private CentralRMSRoomClassRepository centralRMSRoomClassRepository;

    @Mock
    private CentralRMSSpecialEventOverlapSplitter splitter;

    @Mock
    private CentralRMSPricingOverridesRemovalModelPreparer centralRMSPricingOverridesRemovalModelPreparer;

    @Mock
    private SerializableJobExecutionParamDao serializableJobExecutionParamDao;

    @InjectMocks
    private CentralRMSPrepareAdvancePricingOverridesToRemoveStep step;

    @Test
    void shouldExecuteStep_true() {
        String userId = "11403";
        String propertyId = "1000";
        Map<String, JobParameter> parameters = new HashMap<>();
        parameters.put(JobParameterKey.USER_ID, new JobParameter(userId));
        parameters.put(JobParameterKey.PROPERTY_ID, new JobParameter(propertyId));
        parameters.put(JobParameterKey.INCOMING_SERIALIZABLE, new JobParameter(1L));

        JobParameters jobParameters = new JobParameters(parameters);
        StepExecution stepExecution = MetaDataInstanceFactory.createStepExecution(jobParameters);

        AdvancedPricingOverrideRequest request = AdvancedPricingOverrideRequest.builder()
                .roomClassToRoomTypeToOverrides(Map.of(
                        "DELUXE", Map.of("KNG", List.of(AdvancedPricingOverrideConfig.builder()
                                .configs(Map.of(OverrideType.CEILING, AdvancedPricingOverrideSetting.builder().action(AdvancedAction.REMOVE).build()))
                                .build()))
                )).build();

        SerializableJobExecutionParam serializableJobExecutionParam = mock(SerializableJobExecutionParam.class);

        when(serializableJobExecutionParamDao.getSerializableJobExecutionParam(1)).thenReturn(serializableJobExecutionParam);
        when(serializableJobExecutionParam.getSerializable()).thenReturn(request);

        assertEquals(AbstractTaskletStep.ShouldExecuteStepResult.TRUE, step.shouldExecuteStep(stepExecution.getJobExecution(), null));
    }

    @Test
    void shouldExecuteStep_false() {
        String userId = "11403";
        String propertyId = "1000";
        Map<String, JobParameter> parameters = new HashMap<>();
        parameters.put(JobParameterKey.USER_ID, new JobParameter(userId));
        parameters.put(JobParameterKey.PROPERTY_ID, new JobParameter(propertyId));
        parameters.put(JobParameterKey.INCOMING_SERIALIZABLE, new JobParameter(1L));

        JobParameters jobParameters = new JobParameters(parameters);
        StepExecution stepExecution = MetaDataInstanceFactory.createStepExecution(jobParameters);

        AdvancedPricingOverrideRequest request = AdvancedPricingOverrideRequest.builder()
                .roomClassToRoomTypeToOverrides(Map.of(
                        "DELUXE", Map.of("KNG", List.of(AdvancedPricingOverrideConfig.builder()
                                .configs(Map.of(OverrideType.CEILING, AdvancedPricingOverrideSetting.builder().action(AdvancedAction.SET).build()))
                                .build()))
                )).build();

        SerializableJobExecutionParam serializableJobExecutionParam = mock(SerializableJobExecutionParam.class);

        when(serializableJobExecutionParamDao.getSerializableJobExecutionParam(1)).thenReturn(serializableJobExecutionParam);
        when(serializableJobExecutionParam.getSerializable()).thenReturn(request);

        assertEquals(AbstractTaskletStep.ShouldExecuteStepResult.FALSE, step.shouldExecuteStep(stepExecution.getJobExecution(), null));
    }

    @Test
    void invoke_splitIncludeOverlaps() throws Exception {
        String userId = "11403";
        String propertyId = "1000";
        Map<String, JobParameter> parameters = new HashMap<>();
        parameters.put(JobParameterKey.USER_ID, new JobParameter(userId));
        parameters.put(JobParameterKey.PROPERTY_ID, new JobParameter(propertyId));
        parameters.put(JobParameterKey.INCOMING_SERIALIZABLE, new JobParameter(1L));

        JobParameters jobParameters = new JobParameters(parameters);
        StepExecution stepExecution = MetaDataInstanceFactory.createStepExecution(jobParameters);
        Set<DayOfWeek> daysOfWeek = Set.of(DayOfWeek.MONDAY, DayOfWeek.WEDNESDAY);
        SpecialEventSettings specialEventSettings = SpecialEventSettings.builder()
                .overlapsWithSpecialEvents(true)
                .specialEvents(Set.of("WINTER"))
                .build();

        AdvancedPricingOverrideConfig deluxeKNGConfig = AdvancedPricingOverrideConfig.builder()
                .startDate(LocalDate.of(2023, 1, 1))
                .endDate(LocalDate.of(2023, 12, 31))
                .daysOfWeek(daysOfWeek)
                .specialEventSettings(specialEventSettings)
                .configs(Map.of(
                        OverrideType.CEILING, AdvancedPricingOverrideSetting.builder().action(AdvancedAction.REMOVE).build(),
                        OverrideType.FLOOR, AdvancedPricingOverrideSetting.builder().action(AdvancedAction.INCREASE).build(),
                        OverrideType.SPECIFIC, AdvancedPricingOverrideSetting.builder().action(AdvancedAction.REMOVE).build()
                ))
                .build();

        AdvancedPricingOverrideConfig standardDblConfig = AdvancedPricingOverrideConfig.builder()
                .startDate(LocalDate.of(2024, 1, 1))
                .endDate(LocalDate.of(2024, 12, 31))
                .daysOfWeek(daysOfWeek)
                .specialEventSettings(specialEventSettings)
                .configs(Map.of(
                        OverrideType.CEILING, AdvancedPricingOverrideSetting.builder().action(AdvancedAction.REMOVE).build(),
                        OverrideType.FLOOR, AdvancedPricingOverrideSetting.builder().action(AdvancedAction.INCREASE).build(),
                        OverrideType.SPECIFIC, AdvancedPricingOverrideSetting.builder().action(AdvancedAction.REMOVE).build()
                ))
                .build();

        AdvancedPricingOverrideRequest request = AdvancedPricingOverrideRequest.builder()
                .roomClassToRoomTypeToOverrides(Map.of(
                        "DELUXE", Map.of("KNG", List.of(deluxeKNGConfig)),
                        "STANDARD", Map.of("DBL", List.of(standardDblConfig))
                )).build();

        SerializableJobExecutionParam serializableJobExecutionParam = mock(SerializableJobExecutionParam.class);
        when(serializableJobExecutionParamDao.getSerializableJobExecutionParam(1)).thenReturn(serializableJobExecutionParam);
        when(serializableJobExecutionParam.getSerializable()).thenReturn(request);

        AccomClass deluxe = new AccomClass();
        deluxe.setId(1);
        deluxe.setName("DELUXE");

        AccomType kng = new AccomType();
        kng.setName("KNG");
        kng.setId(3);
        kng.setStatusId(1);
        deluxe.setAccomTypes(Set.of(kng));

        AccomClass standard = new AccomClass();
        standard.setId(2);
        standard.setName("STANDARD");

        AccomType dbl = new AccomType();
        dbl.setName("DBL");
        dbl.setId(4);
        dbl.setStatusId(1);
        standard.setAccomTypes(Set.of(dbl));

        CPDecisionBAROutput e1 = new CPDecisionBAROutput();
        e1.setId(1L);

        CPDecisionBAROutput e2 = new CPDecisionBAROutput();
        e2.setId(2L);

        CPDecisionBAROutput e3 = new CPDecisionBAROutput();
        e3.setId(3L);
        List<CPDecisionBAROutput> cpDecisionBAROutputsToRemoveDeluxe = List.of(e1, e2, e3);
        List<CPDecisionBAROutput> cpDecisionBAROutputsToRemoveByPersistenceDeluxe = List.of(e1);

        List<CPDecisionBAROutput> cpDecisionBAROutputsToRemoveStandard = List.of(e2);
        List<CPDecisionBAROutput> cpDecisionBAROutputsToRemoveByPersistenceStandard = List.of();

        when(centralRMSRoomClassRepository.getActiveAccomClassByNameIn(request.getRoomClassToRoomTypeToOverrides().keySet())).thenReturn(List.of(deluxe, standard));

        when(splitter.splitIncludeOverlapsWithSpecialEvents(LocalDate.of(2023, 1, 1), LocalDate.of(2023, 12, 31), specialEventSettings.getSpecialEvents()))
                .thenReturn(List.of(Range.between(LocalDate.of(2023, 1, 1), LocalDate.of(2023, 2, 1), LocalDate::compareTo)));
        when(splitter.splitIncludeOverlapsWithSpecialEvents(LocalDate.of(2024, 1, 1), LocalDate.of(2024, 12, 31), specialEventSettings.getSpecialEvents()))
                .thenReturn(List.of(Range.between(LocalDate.of(2024, 9, 1), LocalDate.of(2024, 12, 1), LocalDate::compareTo)));

        when(centralRMSCPDecisionBAROutputRepository.getCpDecisionBAROutputsWithOverridesAndDateBetweenAndDOWInAndOverrideTypeInAndAccomTypeIdIn(
                LocalDate.of(2023, 1, 1),
                LocalDate.of(2023, 2, 1),
                daysOfWeek,
                Set.of(OverrideType.CEILING, OverrideType.SPECIFIC),
                Set.of(3)
        )).thenReturn(cpDecisionBAROutputsToRemoveDeluxe);

        when(centralRMSCPDecisionBAROutputRepository.getCpDecisionBAROutputsWithOverridesAndDateBetweenAndDOWInAndOverrideTypeInAndAccomTypeIdIn(
                LocalDate.of(2024, 9, 1),
                LocalDate.of(2024, 12, 1),
                daysOfWeek,
                Set.of(OverrideType.CEILING, OverrideType.SPECIFIC),
                Set.of(4)
        )).thenReturn(cpDecisionBAROutputsToRemoveStandard);

        List<Tuple2<CPDecisionBAROutputOverride, CPDecisionBAROutput>> toRemoveByPersistenceDeluxe = cpDecisionBAROutputsToRemoveByPersistenceDeluxe.stream()
                .map(cpDecision -> Tuple2.of(mock(CPDecisionBAROutputOverride.class), cpDecision))
                .collect(Collectors.toList());

        List<Tuple2<CPDecisionBAROutputOverride, CPDecisionBAROutput>> toRemoveByPersistenceStandard = cpDecisionBAROutputsToRemoveByPersistenceStandard.stream()
                .map(cpDecision -> Tuple2.of(mock(CPDecisionBAROutputOverride.class), cpDecision))
                .collect(Collectors.toList());

        CentralRMSCPDecisionBAROutputRemovalModel removalModelDeluxe = CentralRMSCPDecisionBAROutputRemovalModel.builder()
                .toRemove(cpDecisionBAROutputsToRemoveDeluxe)
                .toRemoveByPersistence(toRemoveByPersistenceDeluxe)
                .build();

        CentralRMSCPDecisionBAROutputRemovalModel removalModelStandard = CentralRMSCPDecisionBAROutputRemovalModel.builder()
                .toRemove(cpDecisionBAROutputsToRemoveStandard)
                .toRemoveByPersistence(toRemoveByPersistenceStandard)
                .build();

        when(centralRMSPricingOverridesRemovalModelPreparer.prepareCentralRMSCPDecisionBAROutputRemovals(cpDecisionBAROutputsToRemoveDeluxe, Set.of(OverrideType.CEILING, OverrideType.SPECIFIC))).thenReturn(removalModelDeluxe);
        when(centralRMSPricingOverridesRemovalModelPreparer.prepareCentralRMSCPDecisionBAROutputRemovals(cpDecisionBAROutputsToRemoveStandard, Set.of(OverrideType.CEILING, OverrideType.SPECIFIC))).thenReturn(removalModelStandard);

        step.invoke(stepExecution, null);

        // order is annoying
        List<CPDecisionBAROutput> allCpDecisionsToRemove = ListUtils.union(cpDecisionBAROutputsToRemoveDeluxe, cpDecisionBAROutputsToRemoveStandard);
        List<CPDecisionBAROutput> allCpDecisionsToRemoveResult = ((List) JobExecutionUtil.getFromExecutionContext(stepExecution.getJobExecution(), PRICING_OVERRIDES_TO_REMOVE));
        assertEquals(allCpDecisionsToRemove.size(), allCpDecisionsToRemoveResult.size());
        assertTrue(allCpDecisionsToRemove.containsAll(allCpDecisionsToRemoveResult));

        List<Tuple2<CPDecisionBAROutputOverride, CPDecisionBAROutput>> allCpDecisionsToRemoveByPersistence = ListUtils.union(toRemoveByPersistenceDeluxe, toRemoveByPersistenceStandard);
        List<Tuple2<CPDecisionBAROutputOverride, CPDecisionBAROutput>> allCpDecisionsToRemoveResultByPersistence = ((List) JobExecutionUtil.getFromExecutionContext(stepExecution.getJobExecution(), PRICING_OVERRIDES_TO_REMOVE_BY_PERSISTENCE));
        assertEquals(allCpDecisionsToRemoveByPersistence.size(), allCpDecisionsToRemoveResultByPersistence.size());
        assertTrue(allCpDecisionsToRemoveByPersistence.containsAll(allCpDecisionsToRemoveResultByPersistence));
    }

    @Test
    void invoke_excludeOverlaps() throws Exception {
        String userId = "11403";
        String propertyId = "1000";
        Map<String, JobParameter> parameters = new HashMap<>();
        parameters.put(JobParameterKey.USER_ID, new JobParameter(userId));
        parameters.put(JobParameterKey.PROPERTY_ID, new JobParameter(propertyId));
        parameters.put(JobParameterKey.INCOMING_SERIALIZABLE, new JobParameter(1L));

        JobParameters jobParameters = new JobParameters(parameters);
        StepExecution stepExecution = MetaDataInstanceFactory.createStepExecution(jobParameters);
        Set<DayOfWeek> daysOfWeek = Set.of(DayOfWeek.MONDAY, DayOfWeek.WEDNESDAY);
        SpecialEventSettings specialEventSettings = SpecialEventSettings.builder()
                .overlapsWithSpecialEvents(false)
                .specialEvents(Set.of("WINTER"))
                .build();

        AdvancedPricingOverrideConfig deluxeKNGConfig = AdvancedPricingOverrideConfig.builder()
                .startDate(LocalDate.of(2023, 1, 1))
                .endDate(LocalDate.of(2023, 12, 31))
                .daysOfWeek(daysOfWeek)
                .specialEventSettings(specialEventSettings)
                .configs(Map.of(
                        OverrideType.CEILING, AdvancedPricingOverrideSetting.builder().action(AdvancedAction.REMOVE).build(),
                        OverrideType.FLOOR, AdvancedPricingOverrideSetting.builder().action(AdvancedAction.INCREASE).build(),
                        OverrideType.SPECIFIC, AdvancedPricingOverrideSetting.builder().action(AdvancedAction.REMOVE).build()
                ))
                .build();

        AdvancedPricingOverrideConfig standardDblConfig = AdvancedPricingOverrideConfig.builder()
                .startDate(LocalDate.of(2024, 1, 1))
                .endDate(LocalDate.of(2024, 12, 31))
                .daysOfWeek(daysOfWeek)
                .specialEventSettings(specialEventSettings)
                .configs(Map.of(
                        OverrideType.CEILING, AdvancedPricingOverrideSetting.builder().action(AdvancedAction.REMOVE).build(),
                        OverrideType.FLOOR, AdvancedPricingOverrideSetting.builder().action(AdvancedAction.INCREASE).build(),
                        OverrideType.SPECIFIC, AdvancedPricingOverrideSetting.builder().action(AdvancedAction.REMOVE).build()
                ))
                .build();

        AdvancedPricingOverrideRequest request = AdvancedPricingOverrideRequest.builder()
                .roomClassToRoomTypeToOverrides(Map.of(
                        "DELUXE", Map.of("KNG", List.of(deluxeKNGConfig)),
                        "STANDARD", Map.of("DBL", List.of(standardDblConfig))
                )).build();

        SerializableJobExecutionParam serializableJobExecutionParam = mock(SerializableJobExecutionParam.class);
        when(serializableJobExecutionParamDao.getSerializableJobExecutionParam(1)).thenReturn(serializableJobExecutionParam);
        when(serializableJobExecutionParam.getSerializable()).thenReturn(request);

        AccomClass deluxe = new AccomClass();
        deluxe.setId(1);
        deluxe.setName("DELUXE");

        AccomType kng = new AccomType();
        kng.setName("KNG");
        kng.setId(3);
        kng.setStatusId(1);
        deluxe.setAccomTypes(Set.of(kng));

        AccomClass standard = new AccomClass();
        standard.setId(2);
        standard.setName("STANDARD");

        AccomType dbl = new AccomType();
        dbl.setName("DBL");
        dbl.setId(4);
        dbl.setStatusId(1);
        standard.setAccomTypes(Set.of(dbl));

        CPDecisionBAROutput e1 = new CPDecisionBAROutput();
        e1.setId(1L);

        CPDecisionBAROutput e2 = new CPDecisionBAROutput();
        e2.setId(2L);

        CPDecisionBAROutput e3 = new CPDecisionBAROutput();
        e3.setId(3L);

        List<CPDecisionBAROutput> cpDecisionBAROutputsToRemoveDeluxe = List.of(e1, e2, e3);
        List<CPDecisionBAROutput> cpDecisionBAROutputsToRemoveByPersistenceDeluxe = List.of(e2);

        List<CPDecisionBAROutput> cpDecisionBAROutputsToRemoveStandard = List.of(e3);
        List<CPDecisionBAROutput> cpDecisionBAROutputsToRemoveByPersistenceStandard = List.of();

        when(centralRMSRoomClassRepository.getActiveAccomClassByNameIn(request.getRoomClassToRoomTypeToOverrides().keySet())).thenReturn(List.of(deluxe, standard));

        when(splitter.splitExcludeOverlapsWithSpecialEvents(LocalDate.of(2023, 1, 1), LocalDate.of(2023, 12, 31), specialEventSettings.getSpecialEvents()))
                .thenReturn(List.of(Range.between(LocalDate.of(2023, 1, 1), LocalDate.of(2023, 2, 1), LocalDate::compareTo)));
        when(splitter.splitExcludeOverlapsWithSpecialEvents(LocalDate.of(2024, 1, 1), LocalDate.of(2024, 12, 31), specialEventSettings.getSpecialEvents()))
                .thenReturn(List.of(Range.between(LocalDate.of(2024, 9, 1), LocalDate.of(2024, 12, 1), LocalDate::compareTo)));

        when(centralRMSCPDecisionBAROutputRepository.getCpDecisionBAROutputsWithOverridesAndDateBetweenAndDOWInAndOverrideTypeInAndAccomTypeIdIn(
                LocalDate.of(2023, 1, 1),
                LocalDate.of(2023, 2, 1),
                daysOfWeek,
                Set.of(OverrideType.CEILING, OverrideType.SPECIFIC),
                Set.of(3)
        )).thenReturn(cpDecisionBAROutputsToRemoveDeluxe);

        when(centralRMSCPDecisionBAROutputRepository.getCpDecisionBAROutputsWithOverridesAndDateBetweenAndDOWInAndOverrideTypeInAndAccomTypeIdIn(
                LocalDate.of(2024, 9, 1),
                LocalDate.of(2024, 12, 1),
                daysOfWeek,
                Set.of(OverrideType.CEILING, OverrideType.SPECIFIC),
                Set.of(4)
        )).thenReturn(cpDecisionBAROutputsToRemoveStandard);

        List<Tuple2<CPDecisionBAROutputOverride, CPDecisionBAROutput>> toRemoveByPersistenceDeluxe = cpDecisionBAROutputsToRemoveByPersistenceDeluxe.stream()
                .map(cpDecision -> Tuple2.of(mock(CPDecisionBAROutputOverride.class), cpDecision))
                .collect(Collectors.toList());

        List<Tuple2<CPDecisionBAROutputOverride, CPDecisionBAROutput>> toRemoveByPersistenceStandard = cpDecisionBAROutputsToRemoveByPersistenceStandard.stream()
                .map(cpDecision -> Tuple2.of(mock(CPDecisionBAROutputOverride.class), cpDecision))
                .collect(Collectors.toList());

        CentralRMSCPDecisionBAROutputRemovalModel removalModelDeluxe = CentralRMSCPDecisionBAROutputRemovalModel.builder()
                .toRemove(cpDecisionBAROutputsToRemoveDeluxe)
                .toRemoveByPersistence(toRemoveByPersistenceDeluxe)
                .build();

        CentralRMSCPDecisionBAROutputRemovalModel removalModelStandard = CentralRMSCPDecisionBAROutputRemovalModel.builder()
                .toRemove(cpDecisionBAROutputsToRemoveStandard)
                .toRemoveByPersistence(toRemoveByPersistenceStandard)
                .build();

        when(centralRMSPricingOverridesRemovalModelPreparer.prepareCentralRMSCPDecisionBAROutputRemovals(cpDecisionBAROutputsToRemoveDeluxe, Set.of(OverrideType.CEILING, OverrideType.SPECIFIC))).thenReturn(removalModelDeluxe);
        when(centralRMSPricingOverridesRemovalModelPreparer.prepareCentralRMSCPDecisionBAROutputRemovals(cpDecisionBAROutputsToRemoveStandard, Set.of(OverrideType.CEILING, OverrideType.SPECIFIC))).thenReturn(removalModelStandard);

        step.invoke(stepExecution, null);

        // order is annoying
        List<CPDecisionBAROutput> allCpDecisionsToRemove = ListUtils.union(cpDecisionBAROutputsToRemoveDeluxe, cpDecisionBAROutputsToRemoveStandard);
        List<CPDecisionBAROutput> allCpDecisionsToRemoveResult = ((List) JobExecutionUtil.getFromExecutionContext(stepExecution.getJobExecution(), PRICING_OVERRIDES_TO_REMOVE));
        assertEquals(allCpDecisionsToRemove.size(), allCpDecisionsToRemoveResult.size());
        assertTrue(allCpDecisionsToRemove.containsAll(allCpDecisionsToRemoveResult));

        List<Tuple2<CPDecisionBAROutputOverride, CPDecisionBAROutput>> allCpDecisionsToRemoveByPersistence = ListUtils.union(toRemoveByPersistenceDeluxe, toRemoveByPersistenceStandard);
        List<Tuple2<CPDecisionBAROutputOverride, CPDecisionBAROutput>> allCpDecisionsToRemoveResultByPersistence = ((List) JobExecutionUtil.getFromExecutionContext(stepExecution.getJobExecution(), PRICING_OVERRIDES_TO_REMOVE_BY_PERSISTENCE));
        assertEquals(allCpDecisionsToRemoveByPersistence.size(), allCpDecisionsToRemoveResultByPersistence.size());
        assertTrue(allCpDecisionsToRemoveByPersistence.containsAll(allCpDecisionsToRemoveResultByPersistence));
    }

    @Test
    void invoke_filtersOutInactiveRoomTypes() throws Exception {
        String userId = "11403";
        String propertyId = "1000";
        Map<String, JobParameter> parameters = new HashMap<>();
        parameters.put(JobParameterKey.USER_ID, new JobParameter(userId));
        parameters.put(JobParameterKey.PROPERTY_ID, new JobParameter(propertyId));
        parameters.put(JobParameterKey.INCOMING_SERIALIZABLE, new JobParameter(1L));

        JobParameters jobParameters = new JobParameters(parameters);
        StepExecution stepExecution = MetaDataInstanceFactory.createStepExecution(jobParameters);
        Set<DayOfWeek> daysOfWeek = Set.of(DayOfWeek.MONDAY, DayOfWeek.WEDNESDAY);
        SpecialEventSettings specialEventSettings = SpecialEventSettings.builder()
                .overlapsWithSpecialEvents(false)
                .specialEvents(Set.of("WINTER"))
                .build();

        AdvancedPricingOverrideConfig deluxeKNGConfig = AdvancedPricingOverrideConfig.builder()
                .startDate(LocalDate.of(2023, 1, 1))
                .endDate(LocalDate.of(2023, 12, 31))
                .daysOfWeek(daysOfWeek)
                .specialEventSettings(specialEventSettings)
                .configs(Map.of(
                        OverrideType.CEILING, AdvancedPricingOverrideSetting.builder().action(AdvancedAction.REMOVE).build(),
                        OverrideType.FLOOR, AdvancedPricingOverrideSetting.builder().action(AdvancedAction.INCREASE).build(),
                        OverrideType.SPECIFIC, AdvancedPricingOverrideSetting.builder().action(AdvancedAction.REMOVE).build()
                ))
                .build();

        AdvancedPricingOverrideConfig standardDblConfig = AdvancedPricingOverrideConfig.builder()
                .startDate(LocalDate.of(2024, 1, 1))
                .endDate(LocalDate.of(2024, 12, 31))
                .daysOfWeek(daysOfWeek)
                .specialEventSettings(specialEventSettings)
                .configs(Map.of(
                        OverrideType.CEILING, AdvancedPricingOverrideSetting.builder().action(AdvancedAction.REMOVE).build(),
                        OverrideType.FLOOR, AdvancedPricingOverrideSetting.builder().action(AdvancedAction.INCREASE).build(),
                        OverrideType.SPECIFIC, AdvancedPricingOverrideSetting.builder().action(AdvancedAction.REMOVE).build()
                ))
                .build();

        AdvancedPricingOverrideRequest request = AdvancedPricingOverrideRequest.builder()
                .roomClassToRoomTypeToOverrides(Map.of(
                        "DELUXE", Map.of("KNG", List.of(deluxeKNGConfig)),
                        "STANDARD", Map.of("DBL", List.of(standardDblConfig))
                )).build();

        SerializableJobExecutionParam serializableJobExecutionParam = mock(SerializableJobExecutionParam.class);
        when(serializableJobExecutionParamDao.getSerializableJobExecutionParam(1)).thenReturn(serializableJobExecutionParam);
        when(serializableJobExecutionParam.getSerializable()).thenReturn(request);

        AccomClass deluxe = new AccomClass();
        deluxe.setId(1);
        deluxe.setName("DELUXE");

        AccomType activeKNG = new AccomType();
        activeKNG.setName("KNG");
        activeKNG.setAccomTypeCode("KNG");
        activeKNG.setStatusId(1);
        activeKNG.setId(3);

        AccomType inactiveKNG = new AccomType();
        inactiveKNG.setName("KNG");
        inactiveKNG.setAccomTypeCode("KNG");
        inactiveKNG.setStatusId(2);
        inactiveKNG.setId(4);

        deluxe.setAccomTypes(Set.of(activeKNG, inactiveKNG));

        AccomClass standard = new AccomClass();
        standard.setId(2);
        standard.setName("STANDARD");

        AccomType dbl = new AccomType();
        dbl.setName("DBL");
        dbl.setId(4);
        dbl.setStatusId(1);
        standard.setAccomTypes(Set.of(dbl));

        CPDecisionBAROutput e1 = new CPDecisionBAROutput();
        e1.setId(1L);

        CPDecisionBAROutput e2 = new CPDecisionBAROutput();
        e2.setId(2L);

        CPDecisionBAROutput e3 = new CPDecisionBAROutput();
        e3.setId(3L);

        List<CPDecisionBAROutput> cpDecisionBAROutputsToRemoveDeluxe = List.of(e1, e2, e3);
        List<CPDecisionBAROutput> cpDecisionBAROutputsToRemoveByPersistenceDeluxe = List.of(e1);

        List<CPDecisionBAROutput> cpDecisionBAROutputsToRemoveStandard = List.of(e1);
        List<CPDecisionBAROutput> cpDecisionBAROutputsToRemoveByPersistenceStandard = List.of();

        when(centralRMSRoomClassRepository.getActiveAccomClassByNameIn(request.getRoomClassToRoomTypeToOverrides().keySet())).thenReturn(List.of(deluxe, standard));

        when(splitter.splitExcludeOverlapsWithSpecialEvents(LocalDate.of(2023, 1, 1), LocalDate.of(2023, 12, 31), specialEventSettings.getSpecialEvents()))
                .thenReturn(List.of(Range.between(LocalDate.of(2023, 1, 1), LocalDate.of(2023, 2, 1), LocalDate::compareTo)));
        when(splitter.splitExcludeOverlapsWithSpecialEvents(LocalDate.of(2024, 1, 1), LocalDate.of(2024, 12, 31), specialEventSettings.getSpecialEvents()))
                .thenReturn(List.of(Range.between(LocalDate.of(2024, 9, 1), LocalDate.of(2024, 12, 1), LocalDate::compareTo)));

        when(centralRMSCPDecisionBAROutputRepository.getCpDecisionBAROutputsWithOverridesAndDateBetweenAndDOWInAndOverrideTypeInAndAccomTypeIdIn(
                LocalDate.of(2023, 1, 1),
                LocalDate.of(2023, 2, 1),
                daysOfWeek,
                Set.of(OverrideType.CEILING, OverrideType.SPECIFIC),
                Set.of(3)
        )).thenReturn(cpDecisionBAROutputsToRemoveDeluxe);

        when(centralRMSCPDecisionBAROutputRepository.getCpDecisionBAROutputsWithOverridesAndDateBetweenAndDOWInAndOverrideTypeInAndAccomTypeIdIn(
                LocalDate.of(2024, 9, 1),
                LocalDate.of(2024, 12, 1),
                daysOfWeek,
                Set.of(OverrideType.CEILING, OverrideType.SPECIFIC),
                Set.of(4)
        )).thenReturn(cpDecisionBAROutputsToRemoveStandard);

        List<Tuple2<CPDecisionBAROutputOverride, CPDecisionBAROutput>> toRemoveByPersistenceDeluxe = cpDecisionBAROutputsToRemoveByPersistenceDeluxe.stream()
                .map(cpDecision -> Tuple2.of(mock(CPDecisionBAROutputOverride.class), cpDecision))
                .collect(Collectors.toList());

        List<Tuple2<CPDecisionBAROutputOverride, CPDecisionBAROutput>> toRemoveByPersistenceStandard = cpDecisionBAROutputsToRemoveByPersistenceStandard.stream()
                .map(cpDecision -> Tuple2.of(mock(CPDecisionBAROutputOverride.class), cpDecision))
                .collect(Collectors.toList());

        CentralRMSCPDecisionBAROutputRemovalModel removalModelDeluxe = CentralRMSCPDecisionBAROutputRemovalModel.builder()
                .toRemove(cpDecisionBAROutputsToRemoveDeluxe)
                .toRemoveByPersistence(toRemoveByPersistenceDeluxe)
                .build();

        CentralRMSCPDecisionBAROutputRemovalModel removalModelStandard = CentralRMSCPDecisionBAROutputRemovalModel.builder()
                .toRemove(cpDecisionBAROutputsToRemoveStandard)
                .toRemoveByPersistence(toRemoveByPersistenceStandard)
                .build();

        when(centralRMSPricingOverridesRemovalModelPreparer.prepareCentralRMSCPDecisionBAROutputRemovals(cpDecisionBAROutputsToRemoveDeluxe, Set.of(OverrideType.CEILING, OverrideType.SPECIFIC))).thenReturn(removalModelDeluxe);
        when(centralRMSPricingOverridesRemovalModelPreparer.prepareCentralRMSCPDecisionBAROutputRemovals(cpDecisionBAROutputsToRemoveStandard, Set.of(OverrideType.CEILING, OverrideType.SPECIFIC))).thenReturn(removalModelStandard);

        step.invoke(stepExecution, null);

        // order is annoying
        List<CPDecisionBAROutput> allCpDecisionsToRemove = ListUtils.union(cpDecisionBAROutputsToRemoveDeluxe, cpDecisionBAROutputsToRemoveStandard);
        List<CPDecisionBAROutput> allCpDecisionsToRemoveResult = ((List) JobExecutionUtil.getFromExecutionContext(stepExecution.getJobExecution(), PRICING_OVERRIDES_TO_REMOVE));
        assertEquals(allCpDecisionsToRemove.size(), allCpDecisionsToRemoveResult.size());
        assertTrue(allCpDecisionsToRemove.containsAll(allCpDecisionsToRemoveResult));

        List<Tuple2<CPDecisionBAROutputOverride, CPDecisionBAROutput>> allCpDecisionsToRemoveByPersistence = ListUtils.union(toRemoveByPersistenceDeluxe, toRemoveByPersistenceStandard);
        List<Tuple2<CPDecisionBAROutputOverride, CPDecisionBAROutput>> allCpDecisionsToRemoveResultByPersistence = ((List) JobExecutionUtil.getFromExecutionContext(stepExecution.getJobExecution(), PRICING_OVERRIDES_TO_REMOVE_BY_PERSISTENCE));
        assertEquals(allCpDecisionsToRemoveByPersistence.size(), allCpDecisionsToRemoveResultByPersistence.size());
        assertTrue(allCpDecisionsToRemoveByPersistence.containsAll(allCpDecisionsToRemoveResultByPersistence));
    }
}
