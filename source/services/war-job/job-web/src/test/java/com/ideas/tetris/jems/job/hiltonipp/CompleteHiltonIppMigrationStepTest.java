package com.ideas.tetris.jems.job.hiltonipp;

import com.ideas.tetris.jems.core.job.entity.JobInstanceWorkContext;
import com.ideas.tetris.jems.core.step.tasklet.AbstractTaskletStep;
import com.ideas.tetris.jems.core.test.StepJupiterTest;
import com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName;
import com.ideas.tetris.pacman.services.hiltonipp.HiltonIppTransitionConfig;
import com.ideas.tetris.pacman.services.hiltonipp.HiltonIppTransitionService;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import java.util.List;

import static com.ideas.tetris.pacman.services.hiltonipp.enums.HiltonIppTransitionStage.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

class CompleteHiltonIppMigrationStepTest extends StepJupiterTest {

    @InjectMocks
    CompleteHiltonIppMigrationStep step;

    @Mock
    private PacmanConfigParamsService configParamsService;
    @Mock
    HiltonIppTransitionService hiltonIppTransitionService;

    @Test
    void isStepAsync() {
        assertFalse(step.isStepAsync(stepExecution));
    }

    @Test
    void testDoInvoke() throws Exception {
        HiltonIppTransitionConfig config = new HiltonIppTransitionConfig();
        config.setTransitionStage(TRANSITION_COMPLETED);
        when(hiltonIppTransitionService.getTransitionConfigByStatus(List.of(TRANSITION_IN_PROGRESS))).thenReturn(config);
        doNothing().when(hiltonIppTransitionService).updateTransitionStage(config, TRANSITION_COMPLETED);
        String message = (String) step.doInvoke(stepExecution, new JobInstanceWorkContext());
        assertEquals("Hilton Ipp Migration Completed", message);
        verify(hiltonIppTransitionService).getTransitionConfigByStatus(List.of(TRANSITION_IN_PROGRESS));
        verify(hiltonIppTransitionService).updateTransitionStage(config, TRANSITION_COMPLETED);
    }

    @Test
    void shouldNotExecuteWhenPropertyIsNotIppEnabled() {
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.IS_HILTON_IPP_ENABLED)).thenReturn(false);
        AbstractTaskletStep.ShouldExecuteStepResult shouldExecuteStepResult = step.shouldExecuteStep(jobExecution, new JobInstanceWorkContext());
        assertFalse(shouldExecuteStepResult.shouldExecute());
        assertEquals("Not a Hilton IPP enabled property", shouldExecuteStepResult.getMessage());
    }

    @Test
    void shouldNotExecuteWhenValidTransitionConfigMissing() {
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.IS_HILTON_IPP_ENABLED)).thenReturn(true);
        when(hiltonIppTransitionService.getTransitionConfigByStatus(List.of(TRANSITION_IN_PROGRESS))).thenReturn(null);
        AbstractTaskletStep.ShouldExecuteStepResult shouldExecuteStepResult = step.shouldExecuteStep(jobExecution, new JobInstanceWorkContext());
        assertFalse(shouldExecuteStepResult.shouldExecute());
        assertEquals("Ipp Migration Not In Progress", shouldExecuteStepResult.getMessage());
    }

    @Test
    void shouldExecuteWhenValidTransitionConfigFound() {
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.IS_HILTON_IPP_ENABLED)).thenReturn(true);
        when(hiltonIppTransitionService.getTransitionConfigByStatus(List.of(TRANSITION_IN_PROGRESS))).thenReturn(new HiltonIppTransitionConfig());
        AbstractTaskletStep.ShouldExecuteStepResult shouldExecuteStepResult = step.shouldExecuteStep(jobExecution, new JobInstanceWorkContext());
        assertTrue(shouldExecuteStepResult.shouldExecute());
    }
}