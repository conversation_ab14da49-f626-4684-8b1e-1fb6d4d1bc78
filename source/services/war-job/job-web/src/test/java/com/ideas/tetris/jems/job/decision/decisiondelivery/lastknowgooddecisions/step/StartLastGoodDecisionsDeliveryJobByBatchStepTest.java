package com.ideas.tetris.jems.job.decision.decisiondelivery.lastknowgooddecisions.step;

import com.ideas.tetris.jems.core.job.context.JobExecutionUtil;
import com.ideas.tetris.jems.core.step.context.StepExecutionContextKey;
import com.ideas.tetris.jems.core.test.StepJupiterTest;
import com.ideas.tetris.platform.common.job.JobParameterKey;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.*;
import java.util.stream.Stream;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.batch.core.ExitStatus;
import org.springframework.batch.test.MetaDataInstanceFactory;

import java.util.*;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

public class StartLastGoodDecisionsDeliveryJobByBatchStepTest extends StepJupiterTest {

    @Mock
    MassLastKnownGoodDecisionsJobService massLastKnownGoodDecisionsJobService;
    @InjectMocks
    StartLastGoodDecisionsDeliveryJobByBatchStep step;

    private final static List<Integer> propertyIdList = List.of(1,2,3,4,5);

    @BeforeEach
    void setUp() {
        jobParametersBuilder.addLong(JobParameterKey.BATCH_SIZE, Long.valueOf(2));
        jobParametersBuilder.addLong(JobParameterKey.TIME_INTERVAL, Long.valueOf(30));
        stepExecution = MetaDataInstanceFactory.createStepExecution(jobParametersBuilder.toJobParameters());
    }

    @Test
    void testStepIsNotAsync(){
        assertFalse(step.isStepAsync(stepExecution));
    }

    @Test
    void testStepIsNotTransactional(){
        assertFalse(step.isTransactional());
    }

    @Test
    void shouldProcessBatchWhenInvoked() throws Exception {
        JobExecutionUtil.setOnExecutionContext(stepExecution.getJobExecution(), StepExecutionContextKey.LKGDD_REMAINING_PROPERTIES, new ArrayDeque<>(propertyIdList));
        JobExecutionUtil.setOnExecutionContext(stepExecution.getJobExecution(), StepExecutionContextKey.LKGDD_CURRENT_PROPERTY_BATCH, new HashSet<>());

        step.doInvoke(stepExecution, jobWorkContext);

        verify(massLastKnownGoodDecisionsJobService, times(5)).startLGKDJob(any(), anyInt());
        verify(massLastKnownGoodDecisionsJobService, times(2)).waitForSeconds(30);
        Queue<Integer> resultQueue = (Queue<Integer>) JobExecutionUtil.getFromExecutionContext(stepExecution.getJobExecution(), StepExecutionContextKey.LKGDD_REMAINING_PROPERTIES);
        assertTrue(resultQueue.isEmpty());
        HashSet<Integer> resultSet = (HashSet<Integer>) JobExecutionUtil.getFromExecutionContext(stepExecution.getJobExecution(), StepExecutionContextKey.LKGDD_CURRENT_PROPERTY_BATCH);
        assertTrue(resultSet.containsAll(propertyIdList));

    }

    @Test
    void setEmptyCurrentBatchSetBeforeStep(){
        JobExecutionUtil.setOnExecutionContext(stepExecution.getJobExecution(), StepExecutionContextKey.LKGDD_REMAINING_PROPERTIES, new ArrayDeque<>(propertyIdList));
        JobExecutionUtil.setOnExecutionContext(stepExecution.getJobExecution(), StepExecutionContextKey.LKGDD_CURRENT_PROPERTY_BATCH, new HashSet<>(List.of(6,7,8)));

        step.beforeStep(stepExecution);

        HashSet<Integer> resultSet = (HashSet<Integer>) JobExecutionUtil.getFromExecutionContext(stepExecution.getJobExecution(), StepExecutionContextKey.LKGDD_CURRENT_PROPERTY_BATCH);
        assertTrue(resultSet.isEmpty());
    }

    @ParameterizedTest
    @MethodSource("getValidExitStatusForAfterStep")
    void transferCurrentBatchToRemainingQueueAfterStep(ExitStatus exitStatus){
        stepExecution.setExitStatus(exitStatus);
        JobExecutionUtil.setOnExecutionContext(stepExecution.getJobExecution(), StepExecutionContextKey.LKGDD_REMAINING_PROPERTIES, new ArrayDeque<>(propertyIdList));
        JobExecutionUtil.setOnExecutionContext(stepExecution.getJobExecution(), StepExecutionContextKey.LKGDD_CURRENT_PROPERTY_BATCH, new HashSet<>(List.of(6,7,8)));

        ExitStatus result = step.afterStep(stepExecution);

        assertEquals(exitStatus, result);
        Queue<Integer> resultQueue = (Queue<Integer>) JobExecutionUtil.getFromExecutionContext(stepExecution.getJobExecution(), StepExecutionContextKey.LKGDD_REMAINING_PROPERTIES);
        assertTrue(resultQueue.containsAll(Arrays.asList(1,2,3,4,5,6,7,8)));
        assertEquals(8, resultQueue.size());

        HashSet<Integer> resultSet = (HashSet<Integer>) JobExecutionUtil.getFromExecutionContext(stepExecution.getJobExecution(), StepExecutionContextKey.LKGDD_CURRENT_PROPERTY_BATCH);
        assertTrue(Objects.isNull(resultSet));
    }

    @ParameterizedTest
    @MethodSource("getInValidExitStatusForAfterStep")
    void doNotTransferCurrentBatchToRemainingQueueAfterStep(ExitStatus exitStatus){
        stepExecution.setExitStatus(exitStatus);
        JobExecutionUtil.setOnExecutionContext(stepExecution.getJobExecution(), StepExecutionContextKey.LKGDD_REMAINING_PROPERTIES, new ArrayDeque<>(propertyIdList));
        JobExecutionUtil.setOnExecutionContext(stepExecution.getJobExecution(), StepExecutionContextKey.LKGDD_CURRENT_PROPERTY_BATCH, new HashSet<>(List.of(6,7,8)));

        ExitStatus result = step.afterStep(stepExecution);

        assertEquals(exitStatus, result);
        Queue<Integer> resultQueue = (Queue<Integer>) JobExecutionUtil.getFromExecutionContext(stepExecution.getJobExecution(), StepExecutionContextKey.LKGDD_REMAINING_PROPERTIES);
        assertFalse(resultQueue.containsAll(Arrays.asList(1,2,3,4,5,6,7,8)));
        assertEquals(5, resultQueue.size());

        HashSet<Integer> resultSet = (HashSet<Integer>) JobExecutionUtil.getFromExecutionContext(stepExecution.getJobExecution(), StepExecutionContextKey.LKGDD_CURRENT_PROPERTY_BATCH);
        assertTrue(Objects.isNull(resultSet));
    }

    static Stream<Arguments> getInValidExitStatusForAfterStep() {
        return Stream.of(
                Arguments.of(ExitStatus.COMPLETED),
                Arguments.of(ExitStatus.UNKNOWN),
                Arguments.of(ExitStatus.NOOP),
                Arguments.of(ExitStatus.EXECUTING)
        );
    }

    static Stream<Arguments> getValidExitStatusForAfterStep() {
        return Stream.of(
                Arguments.of(ExitStatus.FAILED),
                Arguments.of(ExitStatus.STOPPED)
        );
    }

}