package com.ideas.tetris.jems.job.pmsmigration.step;

import com.ideas.tetris.jems.core.step.tasklet.AbstractTaskletStep;
import com.ideas.tetris.jems.job.pmsmigration.service.PMSRevampConfigurationReader;
import com.ideas.tetris.pacman.services.mktsegrecoding.service.MktSegRecodingService;
import com.ideas.tetris.pacman.services.pacebackfill.BackFillService;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Answers;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.batch.core.JobExecution;
import org.springframework.batch.core.StepExecution;
import org.springframework.batch.item.ExecutionContext;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class MktAccomRecodingPaceBackFillStepTest {
    @Mock
    private BackFillService backfillService;
    @Mock
    private MktSegRecodingService mktSegRecodingService;
    @Mock(answer = Answers.RETURNS_DEEP_STUBS)
    private PMSRevampConfigurationReader pmsRevampConfigurationReader;
    @InjectMocks
    private MktAccomRecodingPaceBackFillStep theStep;
    @Mock
    StepExecution stepExecution;
    @Mock
    JobExecution jobExecution;
    @Mock
    ExecutionContext executionContext;

    @Test
    public void shouldExecuteIfRoomTypesChangesNoMktChanges() {
        when(pmsRevampConfigurationReader.getPMSMigrationConfiguration().isRoomTypeChanged()).thenReturn(true);
        AbstractTaskletStep.ShouldExecuteStepResult shouldExecuteStepResult = theStep.shouldExecuteBackFill(Collections.emptyList());
        Assertions.assertTrue(shouldExecuteStepResult.shouldExecute());
    }

    @Test
    public void shouldExecuteIfNoRoomTypesChangesButMktChanges() {
        when(pmsRevampConfigurationReader.getPMSMigrationConfiguration().isRoomTypeChanged()).thenReturn(false);
        AbstractTaskletStep.ShouldExecuteStepResult shouldExecuteStepResult = theStep.shouldExecuteBackFill(mktSegIds());
        Assertions.assertTrue(shouldExecuteStepResult.shouldExecute());
    }

    @Test
    public void shouldNotExecuteIfNoRoomTypesChangesNoMktChanges() {
        when(pmsRevampConfigurationReader.getPMSMigrationConfiguration().isRoomTypeChanged()).thenReturn(false);
        AbstractTaskletStep.ShouldExecuteStepResult shouldExecuteStepResult = theStep.shouldExecuteBackFill(Collections.emptyList());
        Assertions.assertFalse(shouldExecuteStepResult.shouldExecute());
    }

    private void setupMocks() {
        when(stepExecution.getJobExecution()).thenReturn(jobExecution);
        when(jobExecution.getExecutionContext()).thenReturn(executionContext);
    }

    @Test
    public void doInvoke_RoomTypeChanges() {
        setupMocks();
        when(pmsRevampConfigurationReader.getPMSMigrationConfiguration().isRoomTypeChanged()).thenReturn(true);
        theStep.invokeBackFill(mktSegIds(), stepExecution);
        verify(backfillService).execute(BackFillService.ALL, false, false, false, true, null, true, true);
        verifyNoInteractions(mktSegRecodingService);
    }

    @Test
    public void doInvoke_MktSegChangesNoRoomTypeChanges() {
        setupMocks();
        when(pmsRevampConfigurationReader.getPMSMigrationConfiguration().isRoomTypeChanged()).thenReturn(false);
        List<Integer> mktSegIds = mktSegIds();
        theStep.invokeBackFill(mktSegIds, stepExecution);
        verify(mktSegRecodingService).executeAndSyncTenantMS(mktSegIds);
        verifyNoInteractions(backfillService);
    }

    private List<Integer> mktSegIds() {
        return Arrays.asList(1, 2, 3);
    }
}