package com.ideas.tetris.jems.ngi.job.ngi.step;

import com.ideas.tetris.jems.core.step.tasklet.reactivechunking.ChunkCompletionCriteria;
import com.ideas.tetris.jems.core.test.StepJupiterTest;
import com.ideas.tetris.jems.ngi.job.ngi.item.ReservationItemReader;
import com.ideas.tetris.jems.ngi.job.ngi.item.v2.ReservationV2ItemReader;
import com.ideas.tetris.pacman.services.individualtransactions.IndividualTransactionsService;
import com.ideas.tetris.platform.common.job.JobName;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.batch.core.JobInstance;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.nullable;

class ReservationDataLoadReactiveChunkStepTest extends StepJupiterTest {

    @InjectMocks
    private ReservationDataLoadReactiveChunkStep step;

    @Mock
    private ReservationItemReader reservationItemReader;

    @Mock
    private ReservationV2ItemReader itemReader;

    @Mock
    private IndividualTransactionsService individualTransactionsService;

    @Mock
    private StepExecutionParamUtil stepExecutionParamUtil;

    @AfterEach
    void tearDown() {
        // Restore default
        System.setProperty("remove.requiresNew.ReservationDataLoadStep", "false");
    }

    @Test
    void shouldExecute_StepEnabled_Activity() {
        System.setProperty("remove.requiresNew.ReservationDataLoadStep", "true");
        assertTrue(step.shouldExecuteStep(jobExecution, jobWorkContext).shouldExecute());
    }

    @Test
    void shouldExecute_StepDisabled() {
        System.setProperty("remove.requiresNew.ReservationDataLoadStep", "false");
        assertFalse(step.shouldExecuteStep(jobExecution, jobWorkContext).shouldExecute());
    }

    @Test
    void shouldExecute_StepEnabled_NotActivity() {
        System.setProperty("remove.requiresNew.ReservationDataLoadStep", "true");
        jobExecution.setJobInstance(new JobInstance(1L, JobName.NGICatchupJob.toString()));
        assertFalse(step.shouldExecuteStep(jobExecution, jobWorkContext).shouldExecute());
    }

    @Test
    public void returnsItemReader() {
        assertNotNull(step.delegateItemReader());
    }

    @Test
    public void writeCallsSaveMethod() throws Exception {
        step.write(new ArrayList<>());
        Mockito.verify(individualTransactionsService).saveFromMapInSameTxn(anyList(), nullable(String.class));
    }

    @Test
    void chunkCompletionCriteria() {
        ChunkCompletionCriteria<Map<String, Object>> chunkCompletionCriteria = step.chunkCompletionCriteria();
        assertFalse(chunkCompletionCriteria.shouldComplete(shortStayReservation(), shortStayReservation()));
        assertTrue(chunkCompletionCriteria.shouldComplete(longStayReservation(), shortStayReservation()));
        assertTrue(chunkCompletionCriteria.shouldComplete(shortStayReservation(), longStayReservation()));
        assertTrue(chunkCompletionCriteria.shouldComplete(longStayReservation(), longStayReservation()));
    }

    @Test
    public void isLongStayReservation_Boundary() {
        assertFalse(step.isLongStayReservation(shortStayReservation()));
        assertTrue(step.isLongStayReservation(longStayReservation()));
    }

    private Map<String, Object> longStayReservation() {
        return getReservationMap("2019-09-29");
    }

    private Map<String, Object> shortStayReservation() {
        return getReservationMap("2019-09-28");
    }

    @Test
    public void getEarliestArrivalDate() {
        assertEquals(LocalDate.of(2019, 5, 1), step.getEarliestArrival(getRoomStays()));
    }

    @Test
    public void getEarliestArrivalDate_NotFound() {
        assertNull(step.getEarliestArrival(Collections.singletonList(Collections.emptyMap())));
    }

    @Test
    public void getLatestDepartureDate() {
        assertEquals(LocalDate.of(2019, 5, 21), step.getLatestDeparture(getRoomStays()));
    }

    @Test
    public void getLatestDepartureDate_NotFound() {
        assertNull(step.getLatestDeparture(Collections.singletonList(Collections.emptyMap())));
    }

    private Map<String, Object> getReservationMap(String departureDate) {
        Map<String, Object> reservation = new LinkedHashMap<>();
        reservation.put("roomStays", Collections.singletonList(getStayMap("2019-05-01", departureDate)));
        return reservation;
    }

    private List<Map<String, Object>> getRoomStays() {
        List<Map<String, Object>> stays = new ArrayList<>();
        stays.add(getStayMap("2019-05-05", "2019-05-13"));
        stays.add(getStayMap("2019-05-01", "2019-05-05"));
        stays.add(getStayMap("2019-05-17", "2019-05-21"));
        stays.add(getStayMap("2019-05-13", "2019-05-17"));
        return stays;
    }

    private Map<String, Object> getStayMap(String arrivalDate, String departureDate) {
        Map<String, Object> stay = new LinkedHashMap<>();
        stay.put("arrivalDate", arrivalDate);
        stay.put("departureDate", departureDate);
        return stay;
    }
}
