package com.ideas.tetris.jems.ngi.job.decisiondelivery.step.handler;

import com.ideas.tetris.jems.core.job.entity.JobInstanceWorkContext;
import com.ideas.tetris.jems.ngi.client.DecisionDeliveryClient;
import com.ideas.tetris.jems.ngi.job.decisiondelivery.step.context.NGIDecisionDeliveryContextHelper;
import com.ideas.tetris.jems.ngi.job.decisiondelivery.step.converter.NGIDailyBarConverter;
import com.ideas.tetris.jems.ngi.model.decisiondelivery.DecisionDeliveryProperties;
import com.ideas.tetris.pacman.services.ngi.NGIDecisionService;
import com.ideas.tetris.pacman.services.ngi.decision.NGIDecisionType;
import com.ideas.tetris.pacman.services.ngi.decision.decisiondelivery.DecisionType;
import com.ideas.tetris.pacman.services.ngi.dto.DailyBar;
import com.ideas.tetris.pacman.services.ngi.dto.NGIDailyBar;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.batch.core.StepExecution;

import java.util.Date;
import java.util.List;

import static com.ideas.tetris.pacman.common.constants.Constants.CLIENT_CODE;
import static com.ideas.tetris.pacman.common.constants.Constants.PROPERTY_CODE;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.only;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.verifyNoMoreInteractions;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class DecisionDeliveryAgileRateUploadHandlerTest {

    @Mock
    private NGIDecisionDeliveryContextHelper contextHelper;

    @Mock
    private NGIDecisionService decisionService;

    @Mock
    private NGIDailyBarConverter dailyBarConverter;

    @Mock
    private DecisionDeliveryClient decisionDeliveryClient;

    @InjectMocks
    private DecisionDeliveryAgileRateUploadHandler uploadHandler;

    @Test
    void testGetDecisionType() {

        // Given
        final DecisionType expectedDecisionType = DecisionType.AGILE_RATES;

        // When
        final NGIDecisionType actualDecisionType = uploadHandler.getDecisionType();

        // Then
        assertEquals(expectedDecisionType, actualDecisionType);
    }

    @Test
    void testHasDecisions_forFalse() {

        // Given
        final String vendorId = "APALEO";

        final DecisionDeliveryProperties decisionDeliveryProperties = mock(DecisionDeliveryProperties.class);
        final StepExecution stepExecution = mock(StepExecution.class);
        final JobInstanceWorkContext jobInstanceWorkContext = mock(JobInstanceWorkContext.class);

        when(contextHelper.getVendorIdDependingOnDecisionDelivery1(stepExecution.getJobExecution())).thenReturn(vendorId);
        when(decisionService.getStatusAgileDecisions(vendorId)).thenReturn(Boolean.FALSE);

        // When
        final boolean hasDecisions = uploadHandler
                .hasDecisions(decisionDeliveryProperties, stepExecution, jobInstanceWorkContext);

        // Then
        assertFalse(hasDecisions);

        verify(contextHelper, times(1)).getVendorIdDependingOnDecisionDelivery1(stepExecution.getJobExecution());
        verifyNoMoreInteractions(contextHelper);

        verify(decisionService, only()).getStatusAgileDecisions(vendorId);

        verifyNoInteractions(dailyBarConverter);
        verifyNoInteractions(decisionDeliveryClient);
    }

    @Test
    void testHasDecisions_forTrue() {

        // Given
        final String vendorId = "APALEO";

        final DecisionDeliveryProperties decisionDeliveryProperties = mock(DecisionDeliveryProperties.class);
        final StepExecution stepExecution = mock(StepExecution.class);
        final JobInstanceWorkContext jobInstanceWorkContext = mock(JobInstanceWorkContext.class);

        when(contextHelper.getVendorIdDependingOnDecisionDelivery1(stepExecution.getJobExecution())).thenReturn(vendorId);
        when(decisionService.getStatusAgileDecisions(vendorId)).thenReturn(Boolean.TRUE);

        // When
        final boolean hasDecisions = uploadHandler
                .hasDecisions(decisionDeliveryProperties, stepExecution, jobInstanceWorkContext);

        // Then
        assertTrue(hasDecisions);

        verify(contextHelper, times(1)).getVendorIdDependingOnDecisionDelivery1(stepExecution.getJobExecution());
        verifyNoMoreInteractions(contextHelper);

        verify(decisionService, only()).getStatusAgileDecisions(vendorId);

        verifyNoInteractions(dailyBarConverter);
        verifyNoInteractions(decisionDeliveryClient);
    }

    @Test
    void testUploadDecisions() {

        // Given
        final String currencyCode = "currencyCode";
        final boolean taxExcluded = false;

        final DecisionDeliveryProperties decisionDeliveryProperties = mock(DecisionDeliveryProperties.class);
        final StepExecution stepExecution = mock(StepExecution.class);
        final JobInstanceWorkContext jobInstanceWorkContext = mock(JobInstanceWorkContext.class);
        final List<DailyBar> dailyBars = List.of(new DailyBar());
        final List<NGIDailyBar> ngiDailyBars = List.of(mock(NGIDailyBar.class));

        when(contextHelper.getCurrencyCode()).thenReturn(currencyCode);
        when(contextHelper.isTaxExcluded(stepExecution.getJobExecution())).thenReturn(taxExcluded);


        when(dailyBarConverter.fromDailyBars(dailyBars)).thenReturn(ngiDailyBars);

        // When
        uploadHandler.uploadDecisions(decisionDeliveryProperties, stepExecution, dailyBars);

        // Then
        verify(contextHelper, times(1)).getCurrencyCode();
        verify(contextHelper, times(1)).isTaxExcluded(stepExecution.getJobExecution());
        verifyNoMoreInteractions(contextHelper);


        verify(dailyBarConverter, only()).fromDailyBars(dailyBars);

        verify(decisionDeliveryClient, only()).uploadDailyBarDecisions(decisionDeliveryProperties, ngiDailyBars);
    }

    @Test
    void testLoadDecisions() {
        // Given
        String propertyId = "propertyId";
        String vendorId = "APALEO";
        final List<DailyBar> expectedDecisions = List.of(new DailyBar());
        StepExecution stepExecution = mock(StepExecution.class);
        final DecisionDeliveryProperties decisionDeliveryProperties = mock(DecisionDeliveryProperties.class);

        when(contextHelper.getPropertyId(stepExecution.getJobExecution())).thenReturn(propertyId);
        when(contextHelper.getVendorIdDependingOnDecisionDelivery1(stepExecution.getJobExecution())).thenReturn(vendorId);
        when(decisionService.getAgileRateDecision(propertyId, vendorId, null, null)).thenReturn(expectedDecisions);

        // When
        List<DailyBar> actualDecisions = uploadHandler.loadDecisions(CLIENT_CODE, PROPERTY_CODE, stepExecution);

        // Then
        verify(contextHelper, times(1)).getPropertyId(stepExecution.getJobExecution());
        verify(contextHelper, times(1)).getVendorIdDependingOnDecisionDelivery1(stepExecution.getJobExecution());
        verify(decisionService, only()).getAgileRateDecision(propertyId, vendorId, null, null);
        assertEquals(expectedDecisions, actualDecisions);
    }

    @Test
    void shouldLoadDecisionsWithDatesFromContextHelper() {
        // Given
        String propertyId = "propertyId";
        String vendorId = "APALEO";
        Date currentDate = DateUtil.getCurrentDate();
        final List<DailyBar> expectedDecisions = List.of(new DailyBar());
        StepExecution stepExecution = mock(StepExecution.class);
        final DecisionDeliveryProperties decisionDeliveryProperties = mock(DecisionDeliveryProperties.class);

        when(contextHelper.getPropertyId(stepExecution.getJobExecution())).thenReturn(propertyId);
        when(contextHelper.getVendorIdDependingOnDecisionDelivery1(stepExecution.getJobExecution())).thenReturn(vendorId);
        when(contextHelper.getUploadWindowStartDate(stepExecution.getJobExecution())).thenReturn(currentDate);
        when(contextHelper.getUploadWindowEndDate(stepExecution.getJobExecution())).thenReturn(currentDate);
        when(decisionService.getAgileRateDecision(propertyId, vendorId, currentDate, currentDate)).thenReturn(expectedDecisions);

        // When
        List<DailyBar> actualDecisions = uploadHandler.loadDecisions(CLIENT_CODE, PROPERTY_CODE, stepExecution);

        // Then
        verify(contextHelper, times(1)).getPropertyId(stepExecution.getJobExecution());
        verify(contextHelper, times(1)).getVendorIdDependingOnDecisionDelivery1(stepExecution.getJobExecution());
        verify(decisionService, only()).getAgileRateDecision(propertyId, vendorId, currentDate, currentDate);
        assertEquals(expectedDecisions, actualDecisions);
    }

}
