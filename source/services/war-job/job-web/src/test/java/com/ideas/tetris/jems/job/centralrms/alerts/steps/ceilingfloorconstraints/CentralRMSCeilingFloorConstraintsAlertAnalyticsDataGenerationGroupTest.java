package com.ideas.tetris.jems.job.centralrms.alerts.steps.ceilingfloorconstraints;

import com.ideas.tetris.jems.core.step.tasklet.AbstractTaskletStep;
import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.platform.common.xml.schema.workcontext.v1.WorkContextType;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.batch.core.JobExecution;
import org.springframework.batch.item.ExecutionContext;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class CentralRMSCeilingFloorConstraintsAlertAnalyticsDataGenerationGroupTest {

    @Mock
    private PacmanConfigParamsService configParamsService;

    @Mock
    private DateService dateService;

    @InjectMocks
    private CentralRMSCeilingFloorConstraintsAlertAnalyticsDataGenerationGroup group;

    @BeforeEach
    void setup() {
        WorkContextType workContextType = new WorkContextType();
        PacmanWorkContextHelper.setWorkContext(workContextType);
        PacmanWorkContextHelper.setClientCode("CLIENT");
        PacmanWorkContextHelper.setPropertyCode("PROPERTY");
    }

    @Test
    void shouldExecuteStep() {
        JobExecution jobExecution = new JobExecution(1L);
        ExecutionContext executionContext = new ExecutionContext();
        executionContext.put("SHOULD_RUN_ANALYTICS_DATA_GENERATION", true);
        jobExecution.setExecutionContext(executionContext);

        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.CENTRAL_RMS_CEILING_FLOOR_CONSTRAINTS_ALERTS_ENABLED.value(), "CLIENT", "PROPERTY")).thenReturn(true);
        assertEquals(AbstractTaskletStep.ShouldExecuteStepResult.TRUE, group.shouldExecuteStep(jobExecution, null));
    }

    @Test
    void shouldExecuteStep_false() {
        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.CENTRAL_RMS_CEILING_FLOOR_CONSTRAINTS_ALERTS_ENABLED.value(), "CLIENT", "PROPERTY")).thenReturn(false);
        assertEquals(false, group.shouldExecuteStep(null, null).shouldExecute());
    }

    @Test
    void shouldExecuteStep_run_flag_false() {
        JobExecution jobExecution = new JobExecution(1L);
        ExecutionContext executionContext = new ExecutionContext();
        executionContext.put("SHOULD_RUN_ANALYTICS_DATA_GENERATION", false);
        jobExecution.setExecutionContext(executionContext);

        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.CENTRAL_RMS_CEILING_FLOOR_CONSTRAINTS_ALERTS_ENABLED.value(), "CLIENT", "PROPERTY")).thenReturn(true);
        assertEquals(false, group.shouldExecuteStep(jobExecution, null).shouldExecute());
    }
}
