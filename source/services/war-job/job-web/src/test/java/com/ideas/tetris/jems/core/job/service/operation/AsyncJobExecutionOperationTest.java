package com.ideas.tetris.jems.core.job.service.operation;

import com.ideas.tetris.jems.core.job.service.RelativeFilePathService;
import com.ideas.tetris.jems.core.step.context.StepExecutionContextKey;
import com.ideas.tetris.pacman.common.xml.schema.revision.response.v1.Error;
import com.ideas.tetris.platform.common.businessservice.async.AsyncCallbackData;
import com.ideas.tetris.platform.common.businessservice.async.response.AsyncCallbackError;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.batch.core.StepExecution;
import org.springframework.batch.test.MetaDataInstanceFactory;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
class AsyncJobExecutionOperationTest {

    @InjectMocks
    private AsyncJobExecutionOperation operation;
    @Mock
    private RelativeFilePathService relativeFilePathService;

    @SuppressWarnings("unchecked")
    @Test
    void updateStepExecutionContext() {
        StepExecution stepExecution = MetaDataInstanceFactory.createStepExecution();
        AsyncCallbackData asyncCallbackData = new AsyncCallbackData();
        var responseFiles = List.of("file1", "file2");
        asyncCallbackData.setResponseFileReferences(responseFiles);
        asyncCallbackData.setRequestString("request-string");
        asyncCallbackData.setResponseString("response-string");
        asyncCallbackData.setResponse(new Object());
        List<AsyncCallbackError> errors = List.of(new Error(), new Error());
        asyncCallbackData.setErrors(errors);
        asyncCallbackData.setDecisionsPathPrefix("s3://uri-path/file");

        operation.updateStepExecutionContext(stepExecution, asyncCallbackData);

        var executionContext = stepExecution.getExecutionContext();
        assertEquals("request-string", executionContext.get(StepExecutionContextKey.REQUEST_STRING));
        assertEquals("response-string", executionContext.get(StepExecutionContextKey.RESPONSE_STRING));
        assertEquals(errors, executionContext.get(StepExecutionContextKey.RESPONSE_ERRORS));

        var fileReferences = (List<String>) executionContext.get(StepExecutionContextKey.RESPONSE_FILE_REFERENCES);
        assertNotNull(fileReferences);
        assertEquals("file1", fileReferences.get(0));
        assertEquals("file2", fileReferences.get(1));
        Mockito.verify(relativeFilePathService).updateRelativeFilePaths(asyncCallbackData);
    }

    @Test
    void updateStepExecutionContext_nullValues() {
        StepExecution stepExecution = MetaDataInstanceFactory.createStepExecution();
        AsyncCallbackData asyncCallbackData = new AsyncCallbackData();

        operation.updateStepExecutionContext(stepExecution, asyncCallbackData);

        var executionContext = stepExecution.getExecutionContext();
        assertNull(executionContext.get(StepExecutionContextKey.REQUEST_STRING));
        assertNull(executionContext.get(StepExecutionContextKey.RESPONSE_STRING));
        assertNull(executionContext.get(StepExecutionContextKey.RESPONSE_FILE_REFERENCES));
        assertNull(executionContext.get(StepExecutionContextKey.RESPONSE_ERRORS));
        assertNull(executionContext.get(StepExecutionContextKey.DECISIONS_PATH_PREFIX));
        Mockito.verify(relativeFilePathService).updateRelativeFilePaths(asyncCallbackData);
    }
}