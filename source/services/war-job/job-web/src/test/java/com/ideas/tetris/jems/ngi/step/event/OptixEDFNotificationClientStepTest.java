package com.ideas.tetris.jems.ngi.step.event;

import com.ideas.api.event.OptixClientDataReady;
import com.ideas.api.event.OptixDataReadyRestEndpoints;
import com.ideas.api.event.UenEnvelopeEventSource;
import com.ideas.tetris.jems.core.job.entity.JobInstanceWorkContext;
import com.ideas.tetris.jems.core.job.entity.JobState;
import com.ideas.tetris.jems.core.repository.dao.JobStateDao;
import com.ideas.tetris.jems.core.step.tasklet.AbstractTaskletStep;
import com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.job.entity.ExecutionStatus;
import com.ideas.tetris.platform.common.job.JobName;
import com.ideas.tetris.platform.common.job.JobParameterKey;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.apache.commons.lang.time.DateUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.batch.core.JobExecution;
import org.springframework.batch.core.JobInstance;
import org.springframework.batch.core.JobParameter;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.core.StepExecution;
import org.springframework.batch.item.ExecutionContext;

import java.net.URI;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;

import static com.ideas.tetris.pacman.common.configparams.IntegrationConfigParamName.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class OptixEDFNotificationClientStepTest {

    @Mock(lenient = true)
    private PacmanConfigParamsService pacmanConfigParamsService;

    @Mock
    private DateService dateService;

    @Mock
    private JobStateDao jobStateDao;

    @Mock(lenient = true)
    private StepExecution stepExecution;

    @Mock(lenient = true)
    private JobExecution jobExecution;

    @Mock(lenient = true)
    private JobInstanceWorkContext jobInstanceWorkContext;

    @Mock(lenient = true)
    private ExecutionContext executionContext;

    // Using spy instead of @InjectMocks to be able to mock parent class methods
    private OptixEDFNotificationClientStep step;

    private final Date caughtUpDate = new Date();
    private final Integer propertyId = 12345;
    private final String clientCode = "SANDBOX";
    private final String callbackUrl = "http://example.com/callback";
    private final String ftpHost = "ftp.example.com";
    private final String ftpPassword = "password123";
    private final String certFileName = "cert.pem";
    private final String keyPassPhrase = "passphrase123";
    private final int ftpPort = 2222;
    private final String ftpUsername = "myusername";
    private final String ftpDirectory = "/foo/bar";
    // Use the actual parameter name from the enum instead of mocking it
    private final String notificationEnabledParam = PreProductionConfigParamName.IS_OPTIX_EDF_NOTIFICATION_ENABLED.getParameterName();

    @BeforeEach
    public void setUp() {
        // Create a spy instead of using @InjectMocks
        step = spy(new OptixEDFNotificationClientStep());
        step.setJobStateDao(jobStateDao);
        step.setPacmanConfigParamsService(pacmanConfigParamsService);
        step.setDateService(dateService);

        // Common mocks
        when(stepExecution.getJobExecution()).thenReturn(jobExecution);
        when(jobExecution.getExecutionContext()).thenReturn(new ExecutionContext());
        when(jobExecution.getJobInstance()).thenReturn(new JobInstance((long) propertyId, "JobName"));
        when(jobExecution.getJobParameters()).thenReturn(new JobParameters());
        when(jobExecution.getExecutionContext()).thenReturn(executionContext);

        // Config params
        when(pacmanConfigParamsService.getParameterValueByClientLevel(OPTIX_DATAFEED_FTP_HOST.value(), clientCode)).thenReturn(ftpHost);
        when(pacmanConfigParamsService.getParameterValueByClientLevel(OPTIX_DATAFEED_FTP_PASSWORD.value(), clientCode)).thenReturn(ftpPassword);
        when(pacmanConfigParamsService.getParameterValueByClientLevel(OPTIX_DATAFEED_SFTP_CERTIFICATE_FILE_NAME.value(), clientCode)).thenReturn(certFileName);
        when(pacmanConfigParamsService.getParameterValueByClientLevel(OPTIX_DATAFEED_SFTP_PRIVATE_KEY_PASS_PHRASE.value(), clientCode)).thenReturn(keyPassPhrase);
        when(pacmanConfigParamsService.getParameterValueByClientLevel(OPTIX_DATAFEED_FTP_PORT.value(), clientCode)).thenReturn(String.valueOf(ftpPort));
        when(pacmanConfigParamsService.getParameterValueByClientLevel(OPTIX_DATAFEED_FTP_USERNAME.value(), clientCode)).thenReturn(ftpUsername);
        when(pacmanConfigParamsService.getParameterValueByClientLevel(OPTIX_DATAFEED_FTP_REMOTE_DIRECTORY.value(), clientCode)).thenReturn(ftpDirectory);

    }

    @Test
    public void shouldExecuteStep_edfNotEnabled_returnsFalse() {
        setupJobParameters(false);
        // Arrange
        when(pacmanConfigParamsService.getParameterValueByClientLevel(notificationEnabledParam, clientCode)).thenReturn("false");

        // Act
        AbstractTaskletStep.ShouldExecuteStepResult result = step.shouldExecuteStep(jobExecution, jobInstanceWorkContext);

        // Assert
        assertFalse(result.shouldExecute());
    }

    @Test
    public void shouldExecuteStep_edfNotEnabled_returnsNull() {
        setupJobParameters(false);
        // Arrange
        when(pacmanConfigParamsService.getParameterValueByClientLevel(notificationEnabledParam, clientCode)).thenReturn(null);

        // Act
        AbstractTaskletStep.ShouldExecuteStepResult result = step.shouldExecuteStep(jobExecution, jobInstanceWorkContext);

        // Assert
        assertFalse(result.shouldExecute());
    }


    @Test
    public void shouldExecuteStep_edfNotEnabled_returnsTrue() {
        setupJobParameters(false);
        // Arrange
        when(pacmanConfigParamsService.getParameterValueByClientLevel(notificationEnabledParam, clientCode)).thenReturn("true");

        // Act
        AbstractTaskletStep.ShouldExecuteStepResult result = step.shouldExecuteStep(jobExecution, jobInstanceWorkContext);

        // Assert
        assertTrue(result.shouldExecute());
    }


    @Test
    public void createEventSource_withNoJobState_returnsFirstUploadTrue() {
        // Arrange

        // Create proper mock objects for the method returns
        OptixDataReadyRestEndpoints mockEndpoints = new OptixDataReadyRestEndpoints();

        // Mock the parent class methods using doReturn().when() pattern
        doReturn(mockEndpoints).when(step).getOptixRestEndpoints(
                eq(clientCode), any(), eq(true), eq(true));

        // Fix: Use HashSet instead of ArrayList to match return type of getEndpointFrequencyTypes
        doReturn(new HashSet<>()).when(step).getEndpointFrequencyTypesAtClientLevel(any(), eq(clientCode));
        Date lastSuccessfulDate = new Date(0);
        doReturn(lastSuccessfulDate).when(step).getLastSuccessfulRun(jobExecution, clientCode);

        setupJobParameters(false);
        when(dateService.getCurrentDate()).thenReturn(new Date(0));

        // Act
        OptixClientDataReady result = step.createEventSource(stepExecution, jobInstanceWorkContext);

        // Assert
        assertNotNull(result);
        assertEquals(OptixClientDataReady.ProcessTypeEnum.BDE, result.getProcessType());
        assertFalse(result.getIncludeHistoryData());
        assertEquals(URI.create(callbackUrl), result.getBaseCallbackURL());
        assertEquals(ftpHost, result.getFtpHost());
        assertEquals(ftpPassword, result.getFtpPassword());
        assertEquals(certFileName, result.getSftpCertificateFileName());
        assertEquals(keyPassPhrase, result.getSftpPrivateKeyPassPhrase());
        assertEquals(ftpPort, result.getFtpPort().intValue());
        assertEquals(ftpUsername, result.getFtpUsername());
        assertEquals(ftpDirectory, result.getFtpRemoteDirectory());
        assertEquals(DateUtil.formatDate(lastSuccessfulDate, DateUtil.DATE_TIME_FORMAT), result.getLastSuccessDate());
    }

    @Test
    public void createEventSource_withJobState_andGenerateHistoryTrue_returnsFirstUploadTrue() {
        // Arrange

        // Create proper mock objects for the method returns
        OptixDataReadyRestEndpoints mockEndpoints = new OptixDataReadyRestEndpoints();

        // Mock the parent class methods using doReturn().when() pattern
        doReturn(mockEndpoints).when(step).getOptixRestEndpoints(
                eq(clientCode), any(), eq(false), eq(true));

        // Fix: Use HashSet instead of ArrayList to match return type of getEndpointFrequencyTypes
        doReturn(new HashSet<>()).when(step).getEndpointFrequencyTypesAtClientLevel(any(), eq(clientCode));

        setupJobParameters(true);
        Date lastSuccessfulDate = new Date();
        doReturn(lastSuccessfulDate).when(step).getLastSuccessfulRun(jobExecution, clientCode);

        // Act
        OptixClientDataReady result = step.createEventSource(stepExecution, jobInstanceWorkContext);

        // Assert
        assertNotNull(result);
        assertFalse(result.getIncludeHistoryData());
    }

    @Test
    public void createEventSource_withJobState_andGenerateHistoryFalse_returnsFirstUploadFalse() {
        // Arrange

        // Create proper mock objects for the method returns
        OptixDataReadyRestEndpoints mockEndpoints = new OptixDataReadyRestEndpoints();

        // Mock the parent class methods using doReturn().when() pattern
        doReturn(mockEndpoints).when(step).getOptixRestEndpoints(
                eq(clientCode), any(), eq(false), eq(true));

        // Fix: Use HashSet instead of ArrayList to match return type of getEndpointFrequencyTypes
        doReturn(new HashSet<>()).when(step).getEndpointFrequencyTypesAtClientLevel(any(), eq(clientCode));

        setupJobParameters(false);
        Date lastSuccessfulDate = new Date();
        doReturn(lastSuccessfulDate).when(step).getLastSuccessfulRun(jobExecution, clientCode);

        // Act
        OptixClientDataReady result = step.createEventSource(stepExecution, jobInstanceWorkContext);

        // Assert
        assertNotNull(result);
        assertFalse(result.getIncludeHistoryData());
    }

    @Test
    public void toEventSource_convertsCorrectly() {
        // Arrange
        OptixClientDataReady optixClientDataReady = new OptixClientDataReady();
        UenEnvelopeEventSource expectedResult = new UenEnvelopeEventSource(optixClientDataReady);

        // Mock the UenEnvelopeEventSource creation to avoid NoClassDefFoundError
        doReturn(expectedResult).when(step).toEventSource(optixClientDataReady);

        // Act
        UenEnvelopeEventSource result = step.toEventSource(optixClientDataReady);

        // Assert
        assertNotNull(result);
        assertEquals(optixClientDataReady, result.getOptixClientDataReady());
    }

    @Test
    void getLastSuccessfulDate_Historical_ReturnsCurrentDate() {
        setupJobParameters(true);
        var date = step.getLastSuccessfulRun(jobExecution, clientCode);
        assertNotNull(date);
        assertEquals(new Date(0), date);
    }

    @Test
    void getLastSuccessfulDate_NonHistorical_NoJobHistory_ReturnsCurrentHistory() {
        setupJobParameters(false);
        var date = step.getLastSuccessfulRun(jobExecution, clientCode);
        assertNotNull(date);
        assertEquals(new Date(0), date);
    }

    @Test
    void getLastSuccessfulDate_NonHistorical_HistoryForOnlyOneJob_ReturnsDate() {
        setupJobParameters(false);

        var jobState = new JobState();
        jobState.setEndTime(DateUtils.addDays(new Date(), -10));
        when(jobStateDao.getMostRecentJobStateForClient(clientCode, JobName.GenerateOptixClientLevelDatafeedFile.name(), ExecutionStatus.COMPLETED)).thenReturn(jobState);

        var date = step.getLastSuccessfulRun(jobExecution, clientCode);
        assertEquals(jobState.getEndTime(), date);
    }

    @Test
    void getLastSuccessfulDate_NonHistorical_MultipleJobsWithHistory_ReturnsMostRecentOne() {
        setupJobParameters(false);

        var jobState1 = new JobState();
        jobState1.setEndTime(DateUtils.addDays(new Date(), -10));
        when(jobStateDao.getMostRecentJobStateForClient(clientCode, JobName.GenerateOptixClientLevelDatafeedFile.name(), ExecutionStatus.COMPLETED)).thenReturn(jobState1);

        var jobState2 = new JobState();
        jobState2.setEndTime(DateUtils.addDays(new Date(), -15));
        when(jobStateDao.getMostRecentJobStateForClient(clientCode, JobName.GenerateOptixClientLevelDatafeedFileOnDemand.name(), ExecutionStatus.COMPLETED)).thenReturn(jobState2);

        var date = step.getLastSuccessfulRun(jobExecution, clientCode);
        assertEquals(jobState1.getEndTime(), date);
    }

    @Test
    public void getEventType_returnsCorrectType() {
        // Act
        var result = step.getEventType();

        // Assert
        assertEquals("OPTIX_CLIENT_DATA_READY", result);
    }

    private void setupJobParameters(boolean generateHistoryFiles) {
        Map<String, JobParameter> parameterMap = new HashMap<>();
        parameterMap.put(JobParameterKey.BASE_CALL_BACK_URL, new JobParameter(callbackUrl));
        parameterMap.put(JobParameterKey.GENERATE_HISTORY_FILES, new JobParameter("", generateHistoryFiles));
        parameterMap.put(JobParameterKey.CLIENT_CODE, new JobParameter(clientCode));
        JobParameters jobParameters = new JobParameters(parameterMap);
        when(jobExecution.getJobParameters()).thenReturn(jobParameters);
    }
}
