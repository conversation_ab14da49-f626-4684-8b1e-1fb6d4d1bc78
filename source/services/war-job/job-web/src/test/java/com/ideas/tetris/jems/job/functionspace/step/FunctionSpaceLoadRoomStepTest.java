package com.ideas.tetris.jems.job.functionspace.step;

import com.ideas.tetris.jems.ngi.client.NGIRestClient;
import com.ideas.tetris.pacman.services.functionspace.configuration.entity.FunctionSpaceCombinationFunctionRoom;
import com.ideas.tetris.pacman.services.functionspace.configuration.entity.FunctionSpaceFunctionRoom;
import com.ideas.tetris.pacman.services.functionspace.configuration.entity.FunctionSpaceFunctionRoomPriceTier;
import com.ideas.tetris.pacman.services.functionspace.configuration.entity.FunctionSpaceFunctionRoomType;
import com.ideas.tetris.pacman.services.ngi.NGIConvertService;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import org.joda.time.LocalDate;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import static com.ideas.tetris.jems.job.functionspace.step.FunctionSpaceLoadRoomStep.QUERT_DELETE_FUNCTION_ROOM_PART;
import static com.ideas.tetris.jems.job.functionspace.step.FunctionSpaceLoadRoomStep.QUERY_UPDATE_EXISTING_ENTITY_TYPE;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class FunctionSpaceLoadRoomStepTest  {
    private static final String NO_ROOM = "NoRoom";
    private static final String COMBO_ROOM = "ComboRoom";
    private static final String NON_COMBO_ROOM = "NonComboRoom";
    @InjectMocks
    private FunctionSpaceLoadRoomStep step;
    @InjectMocks
    private NGIConvertService convertService = spy(NGIConvertService.class);
    @Mock
    NGIRestClient ngiRestClient;
    private List<Map<String, Object>> newRoomJson;
    @Mock
    private CrudService tenantCrudService;

    @Test
    public void isStepAsync() throws Exception {
        Assertions.assertFalse(step.isStepAsync(null));
    }

    @Test
    public void shouldSaveFunctionSpaceFunctionRoom_withNoExistingRoom() throws Exception {
        buildNewRoomToSave(NON_COMBO_ROOM)
                .cookExistingRoom(NO_ROOM)
                .saveNewRoom()
                .verifySaveWithNoExistingRoom(FunctionSpaceFunctionRoom.class);
    }

    @Test
    public void shouldSaveFunctionSpaceFunctionRoom_withExistingFunctionRoom() throws Exception {
        buildNewRoomToSave(NON_COMBO_ROOM)
                .cookExistingRoom(NON_COMBO_ROOM)
                .saveNewRoom()
                .verifySameRoomTypeSavedAgain(FunctionSpaceFunctionRoom.class);
    }

    @Test
    public void shouldSaveFunctionSpaceFunctionRoom_withExistingCombinationalFunctionRoom() throws Exception {
        buildNewRoomToSave(NON_COMBO_ROOM)
                .cookExistingRoom(COMBO_ROOM)
                .saveNewRoom()
                .verifyExistingEntityTypeChanged()
                .verifyFunctionRoomPartIdDeleted()
                .verifyChangeOfRoomTypeSaved(FunctionSpaceFunctionRoom.class);
    }


    @Test
    public void shouldSaveCombinationalFunctionRoom_withNoExistingRoom() throws Exception {
        buildNewRoomToSave(COMBO_ROOM)
                .cookExistingRoom(NO_ROOM)
                .saveNewRoom()
                .verifySaveWithNoExistingRoom(FunctionSpaceCombinationFunctionRoom.class);
    }

    @Test
    public void shouldSaveCombinationalFunctionRoom_withExistingFunctionRoom() throws Exception {
        buildNewRoomToSave(COMBO_ROOM)
                .cookExistingRoom(NON_COMBO_ROOM)
                .saveNewRoom()
                .verifyExistingEntityTypeChanged()
                .verifyChangeOfRoomTypeSaved(FunctionSpaceCombinationFunctionRoom.class);
    }

    @Test
    public void shouldSaveCombinationalFunctionRoom_withExistingCombinationalFunctionRoom() throws Exception {
        buildNewRoomToSave(COMBO_ROOM)
                .cookExistingRoom(COMBO_ROOM)
                .saveNewRoom()
                .verifySameRoomTypeSavedAgain(FunctionSpaceCombinationFunctionRoom.class);
    }

    private <T extends FunctionSpaceFunctionRoom> void verifySameRoomTypeSavedAgain(Class<T> roomType) {
        verify(tenantCrudService).findByNamedQuerySingleResult(eq(FunctionSpaceFunctionRoom.FIND_ROOM_BY_SALES_CATERING_IDENTIFIER), anyMapOf(String.class, Object.class));
        verify(tenantCrudService).findByNamedQuerySingleResult(eq(FunctionSpaceFunctionRoomType.FIND_BY_ABBREVIATION), anyMapOf(String.class, Object.class));
        verify(tenantCrudService).save(any(roomType));
        verify(tenantCrudService, never()).executeUpdateByNativeQuery(anyString(), anyMapOf(String.class, Object.class));
        verify(tenantCrudService, never()).flushAndClear();
    }

    private <T extends FunctionSpaceFunctionRoom> void verifySaveWithNoExistingRoom(Class<T> roomType) {
        verify(tenantCrudService).findByNamedQuerySingleResult(eq(FunctionSpaceFunctionRoom.FIND_ROOM_BY_SALES_CATERING_IDENTIFIER), anyMapOf(String.class, Object.class));
        verify(tenantCrudService).save(any(roomType));
        verify(tenantCrudService, never()).executeUpdateByNativeQuery(anyString(), anyMapOf(String.class, Object.class));
        verify(tenantCrudService, never()).flushAndClear();
    }

    private <T extends FunctionSpaceFunctionRoom> void verifyChangeOfRoomTypeSaved(Class<T> roomType) {
        verify(tenantCrudService).findByNamedQuerySingleResult(eq(FunctionSpaceFunctionRoom.FIND_ROOM_BY_SALES_CATERING_IDENTIFIER), anyMapOf(String.class, Object.class));
        verify(tenantCrudService).findByNamedQuerySingleResult(eq(FunctionSpaceFunctionRoomType.FIND_BY_ABBREVIATION), anyMapOf(String.class, Object.class));
        verify(tenantCrudService).save(any(roomType));
        verify(tenantCrudService).flushAndClear();
    }

    private FunctionSpaceLoadRoomStepTest verifyExistingEntityTypeChanged() {
        verify(tenantCrudService).executeUpdateByNativeQuery(eq(QUERY_UPDATE_EXISTING_ENTITY_TYPE), anyMapOf(String.class, Object.class));
        return this;
    }

    private FunctionSpaceLoadRoomStepTest verifyFunctionRoomPartIdDeleted() {
        verify(tenantCrudService).executeUpdateByNativeQuery(eq(QUERT_DELETE_FUNCTION_ROOM_PART), anyMapOf(String.class, Object.class));
        return this;
    }

    private FunctionSpaceLoadRoomStepTest saveNewRoom() throws Exception {
        step.write(newRoomJson);
        return this;
    }

    private FunctionSpaceLoadRoomStepTest cookExistingRoom(String existingRoom) {
        if (NO_ROOM.equalsIgnoreCase(existingRoom)) {
            when(tenantCrudService.findByNamedQuerySingleResult(eq(FunctionSpaceFunctionRoom.FIND_ROOM_BY_SALES_CATERING_IDENTIFIER), anyMapOf(String.class, Object.class))).thenReturn(null);
        } else if (NON_COMBO_ROOM.equalsIgnoreCase(existingRoom)) {
            when(tenantCrudService.findByNamedQuerySingleResult(eq(FunctionSpaceFunctionRoom.FIND_ROOM_BY_SALES_CATERING_IDENTIFIER), anyMapOf(String.class, Object.class))).thenReturn(buildFunctionSpaceFunctionRoom("FunctionRoom"));
            when(tenantCrudService.findByNamedQuerySingleResult(eq(FunctionSpaceFunctionRoomType.FIND_BY_ABBREVIATION), anyMapOf(String.class, Object.class))).thenReturn(new FunctionSpaceFunctionRoomType());
        } else {
            when(tenantCrudService.findByNamedQuerySingleResult(eq(FunctionSpaceFunctionRoom.FIND_ROOM_BY_SALES_CATERING_IDENTIFIER), anyMapOf(String.class, Object.class))).thenReturn(buildFunctionSpaceCombinationFunctionRoom("NonFunctionRoom"));
            when(tenantCrudService.findByNamedQuerySingleResult(eq(FunctionSpaceFunctionRoomType.FIND_BY_ABBREVIATION), anyMapOf(String.class, Object.class))).thenReturn(new FunctionSpaceFunctionRoomType());
        }
        return this;
    }

    private FunctionSpaceLoadRoomStepTest buildNewRoomToSave(String roomType) {
        newRoomJson = new ArrayList<Map<String, Object>>();
        Map<String, Object> data = new LinkedHashMap<String, Object>();
        data.put("propertyID", null);
        data.put("status", 1);
        data.put("areaSqFeet", new BigDecimal(230));
        data.put("areaSqMeters", new BigDecimal(300));
        data.put("maxOccupancy", 100);
        data.put("includeForPricing", Boolean.FALSE);
        data.put("alias", "some alieas");
        data.put("name", "some name");
        data.put("description", "some description");
        data.put("diaryName", "some diary name");
        data.put("building", "abc building");
        data.put("floorName", "fifth floor");
        data.put("lengthInFeet", new BigDecimal(120));
        data.put("widthInFeet", new BigDecimal(60));
        data.put("widthInMeters", new BigDecimal(100));
        data.put("forceAlternate", Boolean.FALSE);
        data.put("shareable", Boolean.FALSE);
        data.put("diaryDisplay", "some display");
        data.put("salesCateringIdentifier", "abc identifier");
        data.put("roomTypeAbbreviation", "XYZ");
        data.put("combo", COMBO_ROOM.equalsIgnoreCase(roomType));
        newRoomJson.add(data);
        return this;
    }

    private FunctionSpaceFunctionRoom buildFunctionSpaceFunctionRoom(String name) {
        FunctionSpaceFunctionRoom functionSpaceFunctionRoom = new FunctionSpaceFunctionRoom();
        functionSpaceFunctionRoom.setPropertyId(5);
        functionSpaceFunctionRoom.setFunctionSpaceFunctionRoomType(buildFunctionSpaceFunctionRoomType());
        functionSpaceFunctionRoom.setFunctionSpaceFunctionRoomPriceTier(FunctionSpaceFunctionRoomPriceTier.TIER_1);
        functionSpaceFunctionRoom.setAreaSqMeters(new BigDecimal(92));
        functionSpaceFunctionRoom.setAreaSqFeet(new BigDecimal(1000));
        functionSpaceFunctionRoom.setMaxOccupancy(20);
        functionSpaceFunctionRoom.setIncludeForPricing(true);
        functionSpaceFunctionRoom.setAlias("Alias");
        functionSpaceFunctionRoom.setName(name);
        functionSpaceFunctionRoom.setDescription("Description");
        functionSpaceFunctionRoom.setDiaryName("Diary Name");
        functionSpaceFunctionRoom.setBuilding("Building");
        functionSpaceFunctionRoom.setBuilding("Floor Name");
        functionSpaceFunctionRoom.setLengthInFeet(new BigDecimal(100));
        functionSpaceFunctionRoom.setLengthInMeters(new BigDecimal(30));
        functionSpaceFunctionRoom.setWidthInFeet(BigDecimal.TEN);
        functionSpaceFunctionRoom.setWidthInMeters(new BigDecimal(3));
        functionSpaceFunctionRoom.setForceAlternate(false);
        functionSpaceFunctionRoom.setShareable(true);
        functionSpaceFunctionRoom.setDiaryDisplay(true);
        functionSpaceFunctionRoom.setSalesCateringIdentifier(name);
        functionSpaceFunctionRoom.setEffectiveDate(new LocalDate(2015, 1, 1));
        functionSpaceFunctionRoom.setIneffectiveDate(new LocalDate(2016, 1, 1));
        return functionSpaceFunctionRoom;
    }

    private FunctionSpaceCombinationFunctionRoom buildFunctionSpaceCombinationFunctionRoom(String name) {
        FunctionSpaceCombinationFunctionRoom combinationFunctionRoom = new FunctionSpaceCombinationFunctionRoom();

        combinationFunctionRoom.setPropertyId(5);
        combinationFunctionRoom.setFunctionSpaceFunctionRoomPriceTier(FunctionSpaceFunctionRoomPriceTier.TIER_1);
        combinationFunctionRoom.setAreaSqFeet(new BigDecimal(1000));
        combinationFunctionRoom.setAreaSqMeters(new BigDecimal(92));
        combinationFunctionRoom.setMaxOccupancy(20);
        combinationFunctionRoom.setIncludeForPricing(true);
        combinationFunctionRoom.setAlias("Alias");
        combinationFunctionRoom.setName(name);
        combinationFunctionRoom.setDescription("Description");
        combinationFunctionRoom.setDiaryName("Diary Name");
        combinationFunctionRoom.setBuilding("Building");
        combinationFunctionRoom.setBuilding("Floor Name");
        combinationFunctionRoom.setLengthInFeet(new BigDecimal(100));
        combinationFunctionRoom.setLengthInMeters(new BigDecimal(30));
        combinationFunctionRoom.setWidthInFeet(BigDecimal.TEN);
        combinationFunctionRoom.setWidthInMeters(new BigDecimal(3));
        combinationFunctionRoom.setForceAlternate(false);
        combinationFunctionRoom.setShareable(true);
        combinationFunctionRoom.setDiaryDisplay(true);
        combinationFunctionRoom.setSalesCateringIdentifier(name);

        combinationFunctionRoom.addIndivisibleFunctionRoom(buildFunctionSpaceFunctionRoom("Indivisible Room 1"));
        combinationFunctionRoom.addIndivisibleFunctionRoom(buildFunctionSpaceFunctionRoom("Indivisible Room 2"));

        return combinationFunctionRoom;
    }

    public static FunctionSpaceFunctionRoomType buildFunctionSpaceFunctionRoomType() {
        FunctionSpaceFunctionRoomType functionSpaceFunctionRoomType = new FunctionSpaceFunctionRoomType();
        functionSpaceFunctionRoomType.setPropertyId(5);
        functionSpaceFunctionRoomType.setAbbreviation("DEFAULT_ABBREVIATION");
        functionSpaceFunctionRoomType.setName("DEFAULT_NAME");
        return functionSpaceFunctionRoomType;
    }
}
