package com.ideas.tetris.jems.ngi.job.sihot.step;

import com.ideas.tetris.jems.core.step.tasklet.AbstractTaskletStep;
import com.ideas.tetris.jems.core.test.StepJupiterTest;
import com.ideas.tetris.pacman.services.ngi.decision.decisiondelivery.DecisionType;
import com.ideas.tetris.platform.common.externalsystem.ExternalSystem;
import com.ideas.tetris.platform.common.job.JobName;
import com.ideas.tetris.platform.common.job.JobParameterKey;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.batch.core.JobExecution;
import org.springframework.batch.core.JobParameters;

import java.util.Set;

import static com.ideas.tetris.pacman.services.ngi.decision.decisiondelivery.DecisionType.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.verify;


@ExtendWith(MockitoExtension.class)
class StartNGISihotDecisionDeliveryJobStepTest extends StepJupiterTest {

    @InjectMocks
    @Spy
    private StartNGISihotDecisionDeliveryJobStep step;

    @Test
    void testGetDestination() {
        assertEquals(ExternalSystem.SIHOT.getCode(), step.getDestination());
    }

    @Test
    void testGetVendorId() {
        assertEquals(StartNGISihotDecisionDeliveryJobStep.SIHOT_VENDOR_ID, step.getVendorId());
    }

    @Test
    void testGetConfiguredDecisionTypes() {
        Set<DecisionType> expectedDecisionTypes = Set.of(
                DAILY_BAR, AGILE_RATES, LAST_ROOM_VALUE_BY_ROOM_TYPE, MINLOS_BY_RATE_CODE_BY_ROOM_TYPE, HOTEL_OVER_BOOKING, ROOM_TYPE_OVER_BOOKING, BAR_BY_LOS, BAR_BY_LOS_BY_ROOM_TYPE);
        assertIterableEquals(expectedDecisionTypes, step.getConfiguredDecisionTypes());
    }

    @Test
    void testGetDecisionDeliveryByTypeJobName() {
        assertEquals(JobName.NGISihotDecisionDeliveryByDecisionTypeJob, step.getDecisionDeliveryByTypeJobName());
    }

    @Test
    public void shouldSkipStepWhenNotPresentInSelectedOutbounds(){
        JobParameters selectedOutboundInJobParameters = jobParametersBuilder.addString(JobParameterKey.SELECTED_OUTBOUND_STEPS, "[StartHtngDecisionDeliveryJobStep]").toJobParameters();
        JobExecution jobExecutionMock = Mockito.mock(JobExecution.class);
        when(jobExecutionMock.getJobParameters()).thenReturn(selectedOutboundInJobParameters);
        AbstractTaskletStep.ShouldExecuteStepResult shouldExecuteStepResult = step.shouldExecuteStep(jobExecutionMock, jobWorkContext);
        assertFalse(shouldExecuteStepResult.shouldExecute());
        assertEquals("StartNGISihotDecisionDeliveryJobStep is not in selected outbounds so skipping this step.", shouldExecuteStepResult.getMessage());
    }

    @Test
    public void shouldNotSkipStepWhenSelectedOutboundsNotPresentInParameters(){
        JobParameters selectedOutboundInJobParameters = jobParametersBuilder.addString(JobParameterKey.PROPERTY_CODE, "0026").toJobParameters();
        JobExecution jobExecutionMock = Mockito.mock(JobExecution.class);
        when(jobExecutionMock.getJobParameters()).thenReturn(selectedOutboundInJobParameters);
        doReturn(new AbstractTaskletStep.ShouldExecuteStepResult()).when(step).getShouldExecuteStepResult(jobExecutionMock, jobWorkContext);
        step.shouldExecuteStep(jobExecutionMock, jobWorkContext);
        verify(step).getShouldExecuteStepResult(jobExecutionMock, jobWorkContext);
    }

    @Test
    public void shouldNotSkipStepWhenPresentInSelectedInOutboundsInParameters(){
        JobParameters selectedOutboundInJobParameters = jobParametersBuilder.addString(JobParameterKey.SELECTED_OUTBOUND_STEPS, "[StartNGISihotDecisionDeliveryJobStep]").toJobParameters();
        JobExecution jobExecutionMock = Mockito.mock(JobExecution.class);
        when(jobExecutionMock.getJobParameters()).thenReturn(selectedOutboundInJobParameters);
        doReturn(new AbstractTaskletStep.ShouldExecuteStepResult()).when(step).getShouldExecuteStepResult(jobExecutionMock, jobWorkContext);
        step.shouldExecuteStep(jobExecutionMock, jobWorkContext);
        verify(step).getShouldExecuteStepResult(jobExecutionMock, jobWorkContext);
    }

}