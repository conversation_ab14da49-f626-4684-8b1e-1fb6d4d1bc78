package com.ideas.tetris.jems.job.centralrms.overbooking.constraint.steps;

import com.ideas.tetris.jems.core.job.entity.SerializableJobExecutionParam;
import com.ideas.tetris.jems.core.repository.dao.SerializableJobExecutionParamDao;
import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.services.centralrms.exception.CentralRMSUserNotAuthorizedException;
import com.ideas.tetris.pacman.services.centralrms.models.overbooking.UnconstrainOverbookingRequest;
import com.ideas.tetris.pacman.services.centralrms.models.overbooking.UnconstrainedOverbookingConfig;
import com.ideas.tetris.pacman.services.centralrms.models.overbooking.UnconstrainedOverbookingOverrideConfig;
import com.ideas.tetris.pacman.services.security.UserService;
import com.ideas.tetris.platform.common.job.JobParameterKey;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.batch.core.JobParameter;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.core.StepExecution;
import org.springframework.batch.test.MetaDataInstanceFactory;

import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class CheckUnconstrainingOverbookingPermissionsStepTest {
    @Mock
    private UserService userService;

    @Mock
    private PacmanConfigParamsService pacmanConfigParamsService;

    @Mock
    private SerializableJobExecutionParamDao serializableJobExecutionParamDao;

    @InjectMocks
    private CheckUnconstrainingOverbookingPermissionsStep step;

    @Nested
    class NavigatorSuite {
        @Test
        void invoke_noNavigatorConfigPermission() {
            String userId = "11403";
            String propertyId = "1000";
            Map<String, JobParameter> parameters = new HashMap<>();
            parameters.put(JobParameterKey.USER_ID, new JobParameter(userId));
            parameters.put(JobParameterKey.PROPERTY_ID, new JobParameter(propertyId));
            parameters.put(JobParameterKey.INCOMING_SERIALIZABLE, new JobParameter(1L));

            JobParameters jobParameters = new JobParameters(parameters);
            StepExecution stepExecution = MetaDataInstanceFactory.createStepExecution(jobParameters);

            UnconstrainOverbookingRequest request = UnconstrainOverbookingRequest.builder()
                    .unconstrainedOverbookingConfigs(Set.of(UnconstrainedOverbookingConfig.builder().build()))
                    .unconstrainedOverbookingSeasonConfigs(Collections.emptySet())
                    .unconstrainedOverbookingCeilingDefaultConfigs(Collections.emptySet())
                    .unconstrainedOverbookingOverrideConfigs(Collections.emptySet())
                    .build();

            SerializableJobExecutionParam serializableJobExecutionParam = mock(SerializableJobExecutionParam.class);
            when(serializableJobExecutionParamDao.getSerializableJobExecutionParam(1)).thenReturn(serializableJobExecutionParam);
            when(serializableJobExecutionParam.getSerializable()).thenReturn(request);
            when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.CENTRAL_RMS_G3_PERMISSIONS_ENABLED)).thenReturn(true);

            Set<String> permissions = new HashSet<>();
            permissions.add("pageCode=roomsConfiguration&access=read");

            when(userService.getAuthorizedPagePermissionsForProperty(userId, propertyId)).thenReturn(permissions);

            CentralRMSUserNotAuthorizedException ex = assertThrows(CentralRMSUserNotAuthorizedException.class, () -> step.invoke(stepExecution, null));

            assertEquals("You do not have the permissions to change overbooking configurations", ex.getMessage());
            verify(pacmanConfigParamsService).getBooleanParameterValue(FeatureTogglesConfigParamName.CENTRAL_RMS_G3_PERMISSIONS_ENABLED);
            verify(userService, times(1)).getAuthorizedPagePermissionsForProperty(userId, propertyId);
        }

        @Test
        void invoke_noNavigatorOverridePermission() {
            String userId = "11403";
            String propertyId = "1000";
            Map<String, JobParameter> parameters = new HashMap<>();
            parameters.put(JobParameterKey.USER_ID, new JobParameter(userId));
            parameters.put(JobParameterKey.PROPERTY_ID, new JobParameter(propertyId));
            parameters.put(JobParameterKey.INCOMING_SERIALIZABLE, new JobParameter(1L));

            JobParameters jobParameters = new JobParameters(parameters);
            StepExecution stepExecution = MetaDataInstanceFactory.createStepExecution(jobParameters);

            UnconstrainOverbookingRequest request = UnconstrainOverbookingRequest.builder()
                    .unconstrainedOverbookingConfigs(Collections.emptySet())
                    .unconstrainedOverbookingSeasonConfigs(Collections.emptySet())
                    .unconstrainedOverbookingCeilingDefaultConfigs(Collections.emptySet())
                    .unconstrainedOverbookingOverrideConfigs(Set.of(UnconstrainedOverbookingOverrideConfig.builder().build()))
                    .build();

            SerializableJobExecutionParam serializableJobExecutionParam = mock(SerializableJobExecutionParam.class);
            when(serializableJobExecutionParamDao.getSerializableJobExecutionParam(1)).thenReturn(serializableJobExecutionParam);
            when(serializableJobExecutionParam.getSerializable()).thenReturn(request);
            when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.CENTRAL_RMS_G3_PERMISSIONS_ENABLED)).thenReturn(true);

            Set<String> permissions = new HashSet<>();
            permissions.add("pageCode=roomsConfiguration&access=read");

            when(userService.getAuthorizedPagePermissionsForProperty(userId, propertyId)).thenReturn(permissions);

            CentralRMSUserNotAuthorizedException ex = assertThrows(CentralRMSUserNotAuthorizedException.class, () -> step.invoke(stepExecution, null));

            assertEquals("You do not have the permissions to change overbooking overrides", ex.getMessage());
            verify(pacmanConfigParamsService).getBooleanParameterValue(FeatureTogglesConfigParamName.CENTRAL_RMS_G3_PERMISSIONS_ENABLED);
            verify(userService, times(1)).getAuthorizedPagePermissionsForProperty(userId, propertyId);
        }

        @Test
        void invoke_hasNavigatorConfigPermission() {
            String userId = "11403";
            String propertyId = "1000";
            Map<String, JobParameter> parameters = new HashMap<>();
            parameters.put(JobParameterKey.USER_ID, new JobParameter(userId));
            parameters.put(JobParameterKey.PROPERTY_ID, new JobParameter(propertyId));
            parameters.put(JobParameterKey.INCOMING_SERIALIZABLE, new JobParameter(1L));

            JobParameters jobParameters = new JobParameters(parameters);
            StepExecution stepExecution = MetaDataInstanceFactory.createStepExecution(jobParameters);

            UnconstrainOverbookingRequest request = UnconstrainOverbookingRequest.builder()
                    .unconstrainedOverbookingConfigs(Set.of(UnconstrainedOverbookingConfig.builder().build()))
                    .unconstrainedOverbookingSeasonConfigs(Collections.emptySet())
                    .unconstrainedOverbookingCeilingDefaultConfigs(Collections.emptySet())
                    .unconstrainedOverbookingOverrideConfigs(Collections.emptySet())
                    .build();

            SerializableJobExecutionParam serializableJobExecutionParam = mock(SerializableJobExecutionParam.class);
            when(serializableJobExecutionParamDao.getSerializableJobExecutionParam(1)).thenReturn(serializableJobExecutionParam);
            when(serializableJobExecutionParam.getSerializable()).thenReturn(request);
            when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.CENTRAL_RMS_G3_PERMISSIONS_ENABLED)).thenReturn(true);

            Set<String> permissions = new HashSet<>();
            permissions.add("portfolio-navigator-overbooking-configuration&access=readWrite");

            when(userService.getAuthorizedPagePermissionsForProperty(userId, propertyId)).thenReturn(permissions);

            assertDoesNotThrow(() -> step.invoke(stepExecution, null));

            verify(pacmanConfigParamsService).getBooleanParameterValue(FeatureTogglesConfigParamName.CENTRAL_RMS_G3_PERMISSIONS_ENABLED);
            verify(userService, times(1)).getAuthorizedPagePermissionsForProperty(userId, propertyId);
        }

        @Test
        void invoke_hasNavigatorOverridePermission() {
            String userId = "11403";
            String propertyId = "1000";
            Map<String, JobParameter> parameters = new HashMap<>();
            parameters.put(JobParameterKey.USER_ID, new JobParameter(userId));
            parameters.put(JobParameterKey.PROPERTY_ID, new JobParameter(propertyId));
            parameters.put(JobParameterKey.INCOMING_SERIALIZABLE, new JobParameter(1L));

            JobParameters jobParameters = new JobParameters(parameters);
            StepExecution stepExecution = MetaDataInstanceFactory.createStepExecution(jobParameters);

            UnconstrainOverbookingRequest request = UnconstrainOverbookingRequest.builder()
                    .unconstrainedOverbookingConfigs(Collections.emptySet())
                    .unconstrainedOverbookingSeasonConfigs(Collections.emptySet())
                    .unconstrainedOverbookingCeilingDefaultConfigs(Collections.emptySet())
                    .unconstrainedOverbookingOverrideConfigs(Set.of(UnconstrainedOverbookingOverrideConfig.builder().build()))
                    .build();

            SerializableJobExecutionParam serializableJobExecutionParam = mock(SerializableJobExecutionParam.class);
            when(serializableJobExecutionParamDao.getSerializableJobExecutionParam(1)).thenReturn(serializableJobExecutionParam);
            when(serializableJobExecutionParam.getSerializable()).thenReturn(request);
            when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.CENTRAL_RMS_G3_PERMISSIONS_ENABLED)).thenReturn(true);

            Set<String> permissions = new HashSet<>();
            permissions.add("portfolio-navigator-overbooking-override&access=readWrite");

            when(userService.getAuthorizedPagePermissionsForProperty(userId, propertyId)).thenReturn(permissions);

            assertDoesNotThrow(() -> step.invoke(stepExecution, null));

            verify(pacmanConfigParamsService).getBooleanParameterValue(FeatureTogglesConfigParamName.CENTRAL_RMS_G3_PERMISSIONS_ENABLED);
            verify(userService, times(1)).getAuthorizedPagePermissionsForProperty(userId, propertyId);
        }
    }

    @Test
    void invoke_noDefaultSeasonPermissions() {
        String userId = "11403";
        String propertyId = "1000";
        Map<String, JobParameter> parameters = new HashMap<>();
        parameters.put(JobParameterKey.USER_ID, new JobParameter(userId));
        parameters.put(JobParameterKey.PROPERTY_ID, new JobParameter(propertyId));
        parameters.put(JobParameterKey.INCOMING_SERIALIZABLE, new JobParameter(1L));

        JobParameters jobParameters = new JobParameters(parameters);
        StepExecution stepExecution = MetaDataInstanceFactory.createStepExecution(jobParameters);

        UnconstrainOverbookingRequest request = UnconstrainOverbookingRequest.builder()
                .unconstrainedOverbookingConfigs(Set.of(UnconstrainedOverbookingConfig.builder().build()))
                .unconstrainedOverbookingSeasonConfigs(Collections.emptySet())
                .unconstrainedOverbookingCeilingDefaultConfigs(Collections.emptySet())
                .unconstrainedOverbookingOverrideConfigs(Collections.emptySet())
                .build();

        SerializableJobExecutionParam serializableJobExecutionParam = mock(SerializableJobExecutionParam.class);
        when(serializableJobExecutionParamDao.getSerializableJobExecutionParam(1)).thenReturn(serializableJobExecutionParam);
        when(serializableJobExecutionParam.getSerializable()).thenReturn(request);
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.CENTRAL_RMS_G3_PERMISSIONS_ENABLED)).thenReturn(false);

        Set<String> permissions = new HashSet<>();
        permissions.add("pageCode=roomsConfiguration&access=read");

        when(userService.getAuthorizedPagePermissionsForProperty(userId, propertyId)).thenReturn(permissions);

        CentralRMSUserNotAuthorizedException ex = assertThrows(CentralRMSUserNotAuthorizedException.class, () -> step.invoke(stepExecution, null));

        assertEquals("You do not have the permissions to configure default or seasonal overbooking.", ex.getMessage());
        verify(userService, times(1)).getAuthorizedPagePermissionsForProperty(userId, propertyId);
    }

    @Test
    void invoke_noCeilingDefaultPermissions() {
        String userId = "11403";
        String propertyId = "1000";
        Map<String, JobParameter> parameters = new HashMap<>();
        parameters.put(JobParameterKey.USER_ID, new JobParameter(userId));
        parameters.put(JobParameterKey.PROPERTY_ID, new JobParameter(propertyId));
        parameters.put(JobParameterKey.INCOMING_SERIALIZABLE, new JobParameter(1L));

        JobParameters jobParameters = new JobParameters(parameters);
        StepExecution stepExecution = MetaDataInstanceFactory.createStepExecution(jobParameters);

        UnconstrainOverbookingRequest request = UnconstrainOverbookingRequest.builder()
                .unconstrainedOverbookingConfigs(Collections.emptySet())
                .unconstrainedOverbookingSeasonConfigs(Collections.emptySet())
                .unconstrainedOverbookingCeilingDefaultConfigs(Set.of(UnconstrainedOverbookingConfig.builder().build()))
                .unconstrainedOverbookingOverrideConfigs(Collections.emptySet())
                .build();

        SerializableJobExecutionParam serializableJobExecutionParam = mock(SerializableJobExecutionParam.class);
        when(serializableJobExecutionParamDao.getSerializableJobExecutionParam(1)).thenReturn(serializableJobExecutionParam);
        when(serializableJobExecutionParam.getSerializable()).thenReturn(request);
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.CENTRAL_RMS_G3_PERMISSIONS_ENABLED)).thenReturn(false);

        Set<String> permissions = new HashSet<>();
        permissions.add("pageCode=ceilingDefaultSettings&access=read");

        when(userService.getAuthorizedPagePermissionsForProperty(userId, propertyId)).thenReturn(permissions);

        CentralRMSUserNotAuthorizedException ex = assertThrows(CentralRMSUserNotAuthorizedException.class, () -> step.invoke(stepExecution, null));

        assertEquals("You do not have the permissions to set Ceiling Defaults.", ex.getMessage());
        verify(userService, times(1)).getAuthorizedPagePermissionsForProperty(userId, propertyId);
    }

    @Test
    void invoke_noPropertyOverridePermissions() {
        String userId = "11403";
        String propertyId = "1000";
        Map<String, JobParameter> parameters = new HashMap<>();
        parameters.put(JobParameterKey.USER_ID, new JobParameter(userId));
        parameters.put(JobParameterKey.PROPERTY_ID, new JobParameter(propertyId));
        parameters.put(JobParameterKey.INCOMING_SERIALIZABLE, new JobParameter(1L));

        JobParameters jobParameters = new JobParameters(parameters);
        StepExecution stepExecution = MetaDataInstanceFactory.createStepExecution(jobParameters);

        UnconstrainOverbookingRequest request = UnconstrainOverbookingRequest.builder()
                .unconstrainedOverbookingConfigs(Collections.emptySet())
                .unconstrainedOverbookingSeasonConfigs(Collections.emptySet())
                .unconstrainedOverbookingCeilingDefaultConfigs(Collections.emptySet())
                .unconstrainedOverbookingOverrideConfigs(Set.of(UnconstrainedOverbookingOverrideConfig.builder().build()))
                .build();

        SerializableJobExecutionParam serializableJobExecutionParam = mock(SerializableJobExecutionParam.class);
        when(serializableJobExecutionParamDao.getSerializableJobExecutionParam(1)).thenReturn(serializableJobExecutionParam);
        when(serializableJobExecutionParam.getSerializable()).thenReturn(request);
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.CENTRAL_RMS_G3_PERMISSIONS_ENABLED)).thenReturn(false);

        Set<String> permissions = new HashSet<>();
        permissions.add("pageCode=roomsConfiguration&access=read");

        when(userService.getAuthorizedPagePermissionsForProperty(userId, propertyId)).thenReturn(permissions);

        CentralRMSUserNotAuthorizedException ex = assertThrows(CentralRMSUserNotAuthorizedException.class, () -> step.invoke(stepExecution, null));

        assertEquals("You do not have the permissions to override overbooking at the property level.", ex.getMessage());
        verify(userService, times(1)).getAuthorizedPagePermissionsForProperty(userId, propertyId);
    }

    @Test
    void invoke_noRoomTypeOverridePermissions() {
        String userId = "11403";
        String propertyId = "1000";
        Map<String, JobParameter> parameters = new HashMap<>();
        parameters.put(JobParameterKey.USER_ID, new JobParameter(userId));
        parameters.put(JobParameterKey.PROPERTY_ID, new JobParameter(propertyId));
        parameters.put(JobParameterKey.INCOMING_SERIALIZABLE, new JobParameter(1L));

        JobParameters jobParameters = new JobParameters(parameters);
        StepExecution stepExecution = MetaDataInstanceFactory.createStepExecution(jobParameters);

        UnconstrainOverbookingRequest request = UnconstrainOverbookingRequest.builder()
                .unconstrainedOverbookingConfigs(Collections.emptySet())
                .unconstrainedOverbookingSeasonConfigs(Collections.emptySet())
                .unconstrainedOverbookingCeilingDefaultConfigs(Collections.emptySet())
                .unconstrainedOverbookingOverrideConfigs(Set.of(UnconstrainedOverbookingOverrideConfig.builder().roomType("QN").build()))
                .build();

        SerializableJobExecutionParam serializableJobExecutionParam = mock(SerializableJobExecutionParam.class);
        when(serializableJobExecutionParamDao.getSerializableJobExecutionParam(1)).thenReturn(serializableJobExecutionParam);
        when(serializableJobExecutionParam.getSerializable()).thenReturn(request);

        Set<String> permissions = new HashSet<>();
        permissions.add("pageCode=roomsConfiguration&access=read");

        when(userService.getAuthorizedPagePermissionsForProperty(userId, propertyId)).thenReturn(permissions);
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.CENTRAL_RMS_G3_PERMISSIONS_ENABLED)).thenReturn(false);

        CentralRMSUserNotAuthorizedException ex = assertThrows(CentralRMSUserNotAuthorizedException.class, () -> step.invoke(stepExecution, null));

        assertEquals("You do not have the permissions to override overbooking at the room type level.", ex.getMessage());
        verify(userService, times(1)).getAuthorizedPagePermissionsForProperty(userId, propertyId);
    }

    @Test
    void invoke_hasSufficientDefaultSeasonPermissions() {
        String userId = "11403";
        String propertyId = "1000";
        Map<String, JobParameter> parameters = new HashMap<>();
        parameters.put(JobParameterKey.USER_ID, new JobParameter(userId));
        parameters.put(JobParameterKey.PROPERTY_ID, new JobParameter(propertyId));
        parameters.put(JobParameterKey.INCOMING_SERIALIZABLE, new JobParameter(1L));

        JobParameters jobParameters = new JobParameters(parameters);
        StepExecution stepExecution = MetaDataInstanceFactory.createStepExecution(jobParameters);

        UnconstrainOverbookingRequest request = UnconstrainOverbookingRequest.builder()
                .unconstrainedOverbookingConfigs(Set.of(UnconstrainedOverbookingConfig.builder().build()))
                .unconstrainedOverbookingSeasonConfigs(Collections.emptySet())
                .unconstrainedOverbookingCeilingDefaultConfigs(Collections.emptySet())
                .unconstrainedOverbookingOverrideConfigs(Collections.emptySet())
                .build();

        SerializableJobExecutionParam serializableJobExecutionParam = mock(SerializableJobExecutionParam.class);
        when(serializableJobExecutionParamDao.getSerializableJobExecutionParam(1)).thenReturn(serializableJobExecutionParam);
        when(serializableJobExecutionParam.getSerializable()).thenReturn(request);
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.CENTRAL_RMS_G3_PERMISSIONS_ENABLED)).thenReturn(false);

        Set<String> permissions = new HashSet<>();
        permissions.add("extraFluff=roomsConfiguration:readWrite");

        when(userService.getAuthorizedPagePermissionsForProperty(userId, propertyId)).thenReturn(permissions);

        assertDoesNotThrow(() -> step.invoke(stepExecution, null));

        verify(userService, times(1)).getAuthorizedPagePermissionsForProperty(userId, propertyId);
    }

    @Test
    void invoke_hasSufficientCeilingDefaultPermissions() {
        String userId = "11403";
        String propertyId = "1000";
        Map<String, JobParameter> parameters = new HashMap<>();
        parameters.put(JobParameterKey.USER_ID, new JobParameter(userId));
        parameters.put(JobParameterKey.PROPERTY_ID, new JobParameter(propertyId));
        parameters.put(JobParameterKey.INCOMING_SERIALIZABLE, new JobParameter(1L));

        JobParameters jobParameters = new JobParameters(parameters);
        StepExecution stepExecution = MetaDataInstanceFactory.createStepExecution(jobParameters);

        UnconstrainOverbookingRequest request = UnconstrainOverbookingRequest.builder()
                .unconstrainedOverbookingConfigs(Collections.emptySet())
                .unconstrainedOverbookingSeasonConfigs(Collections.emptySet())
                .unconstrainedOverbookingCeilingDefaultConfigs(Set.of(UnconstrainedOverbookingConfig.builder().build()))
                .unconstrainedOverbookingOverrideConfigs(Collections.emptySet())
                .build();

        SerializableJobExecutionParam serializableJobExecutionParam = mock(SerializableJobExecutionParam.class);
        when(serializableJobExecutionParamDao.getSerializableJobExecutionParam(1)).thenReturn(serializableJobExecutionParam);
        when(serializableJobExecutionParam.getSerializable()).thenReturn(request);
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.CENTRAL_RMS_G3_PERMISSIONS_ENABLED)).thenReturn(false);

        Set<String> permissions = new HashSet<>();
        permissions.add("extraFluff=ceilingDefaultSettings:readWrite");

        when(userService.getAuthorizedPagePermissionsForProperty(userId, propertyId)).thenReturn(permissions);

        assertDoesNotThrow(() -> step.invoke(stepExecution, null));

        verify(userService, times(1)).getAuthorizedPagePermissionsForProperty(userId, propertyId);
    }

    @Test
    void invoke_hasSufficientPropertyOverridePermissions() {
        String userId = "11403";
        String propertyId = "1000";
        Map<String, JobParameter> parameters = new HashMap<>();
        parameters.put(JobParameterKey.USER_ID, new JobParameter(userId));
        parameters.put(JobParameterKey.PROPERTY_ID, new JobParameter(propertyId));
        parameters.put(JobParameterKey.INCOMING_SERIALIZABLE, new JobParameter(1L));

        JobParameters jobParameters = new JobParameters(parameters);
        StepExecution stepExecution = MetaDataInstanceFactory.createStepExecution(jobParameters);

        UnconstrainOverbookingRequest request = UnconstrainOverbookingRequest.builder()
                .unconstrainedOverbookingConfigs(Collections.emptySet())
                .unconstrainedOverbookingSeasonConfigs(Collections.emptySet())
                .unconstrainedOverbookingCeilingDefaultConfigs(Collections.emptySet())
                .unconstrainedOverbookingOverrideConfigs(Set.of(UnconstrainedOverbookingOverrideConfig.builder().build()))
                .build();

        SerializableJobExecutionParam serializableJobExecutionParam = mock(SerializableJobExecutionParam.class);
        when(serializableJobExecutionParamDao.getSerializableJobExecutionParam(1)).thenReturn(serializableJobExecutionParam);
        when(serializableJobExecutionParam.getSerializable()).thenReturn(request);
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.CENTRAL_RMS_G3_PERMISSIONS_ENABLED)).thenReturn(false);

        Set<String> permissions = new HashSet<>();
        permissions.add("extraFluff=singleDayOverrideAllRtpropertyLimit:readWrite");

        when(userService.getAuthorizedPagePermissionsForProperty(userId, propertyId)).thenReturn(permissions);

        assertDoesNotThrow(() -> step.invoke(stepExecution, null));

        verify(userService, times(1)).getAuthorizedPagePermissionsForProperty(userId, propertyId);
    }

    @Test
    void invoke_hasSufficientRoomTypeOverridePermissions() {
        String userId = "11403";
        String propertyId = "1000";
        Map<String, JobParameter> parameters = new HashMap<>();
        parameters.put(JobParameterKey.USER_ID, new JobParameter(userId));
        parameters.put(JobParameterKey.PROPERTY_ID, new JobParameter(propertyId));
        parameters.put(JobParameterKey.INCOMING_SERIALIZABLE, new JobParameter(1L));

        JobParameters jobParameters = new JobParameters(parameters);
        StepExecution stepExecution = MetaDataInstanceFactory.createStepExecution(jobParameters);

        UnconstrainOverbookingRequest request = UnconstrainOverbookingRequest.builder()
                .unconstrainedOverbookingConfigs(Collections.emptySet())
                .unconstrainedOverbookingSeasonConfigs(Collections.emptySet())
                .unconstrainedOverbookingCeilingDefaultConfigs(Collections.emptySet())
                .unconstrainedOverbookingOverrideConfigs(Set.of(UnconstrainedOverbookingOverrideConfig.builder().roomType("QN").build()))
                .build();

        SerializableJobExecutionParam serializableJobExecutionParam = mock(SerializableJobExecutionParam.class);
        when(serializableJobExecutionParamDao.getSerializableJobExecutionParam(1)).thenReturn(serializableJobExecutionParam);
        when(serializableJobExecutionParam.getSerializable()).thenReturn(request);
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.CENTRAL_RMS_G3_PERMISSIONS_ENABLED)).thenReturn(false);

        Set<String> permissions = new HashSet<>();
        permissions.add("extraFluff=singleDayOverrideByRtLimit:readWrite");

        when(userService.getAuthorizedPagePermissionsForProperty(userId, propertyId)).thenReturn(permissions);

        assertDoesNotThrow(() -> step.invoke(stepExecution, null));

        verify(userService, times(1)).getAuthorizedPagePermissionsForProperty(userId, propertyId);
    }
}
