package com.ideas.tetris.jems.job.common;

import com.ideas.tetris.jems.core.job.context.JobExecutionContextKey;
import com.ideas.tetris.jems.core.job.entity.JobInstanceWorkContext;
import com.ideas.tetris.platform.common.util.file.ZipUtil;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.io.TempDir;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.batch.core.JobExecution;
import org.springframework.batch.core.StepExecution;
import org.springframework.batch.item.ExecutionContext;

import java.io.File;
import java.io.IOException;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class WorkingDirectoryCleanupStepTest {

    private String REPORT_FOLDER_PATH;
    @TempDir
    File temporaryFolder;
    @Mock
    JobExecution jobExecution;
    @Mock
    JobInstanceWorkContext jobInstanceWorkContext;
    @Mock
    StepExecution stepExecution;
    @Mock
    ExecutionContext executionContext;

    @InjectMocks
    WorkingDirectoryCleanupStep step;

    @BeforeEach
    public void setUp() throws Exception {
        REPORT_FOLDER_PATH = temporaryFolder.getAbsolutePath();
        when(stepExecution.getJobExecution()).thenReturn(jobExecution);
        when(jobExecution.getExecutionContext()).thenReturn(executionContext);
        createDirectory(REPORT_FOLDER_PATH);
    }

    @AfterEach
    public void tearDown() throws Exception {
        deleteDirectory(REPORT_FOLDER_PATH);
    }

    @Test
    public void isStepAsync() {
        assertFalse(step.isStepAsync(null));
    }

    @Test
    public void doInvoke() throws Exception {
        //GIVEN
        Mockito.when(executionContext.getString(JobExecutionContextKey.WORKING_DIRECTORY))
                .thenReturn(REPORT_FOLDER_PATH);

        //THEN
        String expectedPath = "'" + StringUtils.removeEnd(REPORT_FOLDER_PATH, File.separator) + "' cleaned up successfully";
        assertEquals(expectedPath, step.doInvoke(stepExecution, jobInstanceWorkContext));
        assertEquals(0, new File(REPORT_FOLDER_PATH).listFiles().length);
    }

    private boolean createDirectory(String directoryPath) {
        try {
            ZipUtil.createDir(directoryPath);
        } catch (IOException e) {
            e.printStackTrace();
            return false;
        }
        return true;
    }

    private void deleteDirectory(String directoryPath) {
        try {
            FileUtils.deleteDirectory(new File(directoryPath));
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}
