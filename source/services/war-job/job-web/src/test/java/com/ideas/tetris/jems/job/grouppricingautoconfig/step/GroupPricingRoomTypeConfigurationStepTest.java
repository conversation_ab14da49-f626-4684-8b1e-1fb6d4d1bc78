package com.ideas.tetris.jems.job.grouppricingautoconfig.step;

import com.ideas.tetris.jems.core.test.StepJupiterTest;
import com.ideas.tetris.pacman.services.automateconfiguration.service.GroupPricingAutoConfigurationService;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.mockito.Mockito.verify;

class GroupPricingRoomTypeConfigurationStepTest extends StepJupiterTest {

    @InjectMocks
    private GroupPricingRoomTypeConfigurationStep groupPricingRoomTypeConfigurationStep;

    @Mock
    private GroupPricingAutoConfigurationService groupPricingAutoConfigurationService;

    @Test
    void stepShouldBeSynchronous() {
        assertFalse(groupPricingRoomTypeConfigurationStep.isStepAsync(stepExecution));
    }

    @Test
    void doInvoke() throws Exception {
        jobWorkContext.setPropertyId(5);

        String successMessage = (String) groupPricingRoomTypeConfigurationStep.doInvoke(stepExecution, jobWorkContext);

        assertEquals("Group Pricing Room Type Auto Configuration was successful for PropertyId: 5", successMessage);
        verify(groupPricingAutoConfigurationService).configureGroupPricingConfigAccomTypesForGroupEvaluation();
    }
}