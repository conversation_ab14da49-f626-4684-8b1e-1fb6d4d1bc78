package com.ideas.tetris.jems.job.moveproperty.step;

import com.ideas.infra.tetris.security.domain.PropertyRoleMapping;
import com.ideas.infra.tetris.security.domain.Role;
import com.ideas.tetris.jems.core.test.StepJupiterTest;
import com.ideas.tetris.pacman.services.crudservice.GlobalCrudServiceBean;
import com.ideas.tetris.pacman.services.security.GlobalRole;
import com.ideas.tetris.pacman.services.security.GlobalUser;
import com.ideas.tetris.pacman.services.security.RoleService;
import com.ideas.tetris.pacman.services.security.UserService;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.job.JobParameterKey;
import com.ideas.tetris.platform.services.daoandentities.entity.Client;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.Mockito.atLeastOnce;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

public class UpdateUserRoleReferencesStepTest extends StepJupiterTest {
    private final Integer PROPERTY_ID = 1234;
    private final Integer CLIENT_ID = 5678;
    private final Integer USER_ID = 0000;

    @InjectMocks
    UpdateUserRoleReferencesStep step;

    @Mock
    @GlobalCrudServiceBean.Qualifier
    CrudService globalCrudService;
    @Mock
    RoleService roleService;
    @Mock
    UserService userService;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        jobWorkContext.setPropertyId(PROPERTY_ID);
        jobWorkContext.setClientId(CLIENT_ID);
        jobWorkContext.setPropertyCode("Prop1234");
        jobWorkContext.setClientCode("Client5678");
        jobWorkContext.setUserId(USER_ID.toString());
        jobParametersBuilder.addString("clientId", CLIENT_ID.toString());
        jobParametersBuilder.addString(JobParameterKey.USER_ROLE_MAPPINGS, "\"[{\"globalUserId\":\"1\",\"roleId\":\"1\"}]\"");
        generateStepExecutionFromMetadata();
    }

    @Test
    public void isAsync() {
        Assertions.assertFalse(step.isStepAsync(null));
    }

    @Test
    public void doInvoke() throws Exception {
        GlobalUser globalUser = new GlobalUser();
        globalUser.setId(1);
        GlobalRole globalRole = new GlobalRole();
        globalRole.setClientCode(CLIENT_CODE);
        Client targetClient = new Client();
        targetClient.setId(CLIENT_ID);
        targetClient.setCode(CLIENT_CODE);
        Role role = globalRole.toRole();
        role.setUniqueIdentifier("roleId");
        role.setClientCode("HYATT");
        List<PropertyRoleMapping> propertyRoleMappings = new ArrayList<>();
        when(globalCrudService.find(GlobalUser.class, 1)).thenReturn(globalUser);
        when(globalCrudService.find(GlobalRole.class, 1)).thenReturn(globalRole);
        when(globalCrudService.find(Client.class, Integer.parseInt(stepExecution.getJobParameters().getString(JobParameterKey.CLIENT_ID)))).thenReturn(targetClient);
        when(roleService.createRoleWithProvidedClient(role, false)).thenReturn(role);
        when(userService.moveUserUnderNewClient(globalUser.getId().toString(), targetClient.getCode(), propertyRoleMappings, jobWorkContext.getPropertyId().toString())).thenReturn("");

        when(globalCrudService.executeUpdateByNativeQuery(String.format(UpdateUserRoleReferencesStep.UPDATE_INTO_USER_INDIVIDUAL_PROPERTY_ROLE_QUERY, role.getUniqueIdentifier(), globalUser.getId(), jobWorkContext.getPropertyId()))).thenReturn(0);
        when(globalCrudService.executeUpdateByNativeQuery(String.format(UpdateUserRoleReferencesStep.INSERT_INTO_USER_INDIVIDUAL_PROPERTY_ROLE_QUERY, globalUser.getId(), jobWorkContext.getPropertyId(), role.getUniqueIdentifier(), USER_ID, USER_ID))).thenReturn(0);
        when(globalCrudService.executeUpdateByNativeQuery(String.format(UpdateUserRoleReferencesStep.DELETE_FROM_USER_INDIVIDUAL_PROPERTY_ROLE_FOR_USERID_QUERY, globalUser.getId(), jobWorkContext.getPropertyId()))).thenReturn(0);
        when(globalCrudService.executeUpdateByNativeQuery(String.format(UpdateUserRoleReferencesStep.DELETE_FROM_USER_AUTH_GROUP_ROLE_QUERY, globalUser.getId()))).thenReturn(0);
        when(globalCrudService.executeUpdateByNativeQuery(String.format(UpdateUserRoleReferencesStep.UPDATE_INTO_CLIENT_USER_QUERY, targetClient.getId(), globalUser.getId()))).thenReturn(0);
        when(globalCrudService.executeUpdateByNativeQuery(String.format(UpdateUserRoleReferencesStep.DELETE_FROM_USER_INDIVIDUAL_PROPERTY_ROLE_FOR_USERID_NOT_IN_QUERY, "", jobWorkContext.getPropertyId()))).thenReturn(0);
        when(globalCrudService.executeUpdateByNativeQuery(String.format(UpdateUserRoleReferencesStep.UPDATE_INTO_ANNOUNCEMENT_CLIENT_QUERY, CLIENT_ID, USER_ID, jobWorkContext.getPropertyStage(), USER_ID, CLIENT_ID))).thenReturn(0);

        String result = (String) step.doInvoke(stepExecution, jobWorkContext);
        verify(globalCrudService, atLeastOnce()).find(GlobalUser.class, 1);
        verify(globalCrudService, atLeastOnce()).find(GlobalUser.class, 1);
        Assertions.assertTrue(result.equals("UserRole preferences updated successfully"));
    }
}
