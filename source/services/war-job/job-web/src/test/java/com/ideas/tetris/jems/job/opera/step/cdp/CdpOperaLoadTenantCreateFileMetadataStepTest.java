package com.ideas.tetris.jems.job.opera.step.cdp;

import com.ideas.tetris.jems.core.job.context.JobExecutionContextKey;
import com.ideas.tetris.jems.core.job.context.JobExecutionUtil;
import com.ideas.tetris.jems.core.job.entity.JobInstanceWorkContext;
import com.ideas.tetris.jems.core.step.tasklet.AbstractTaskletStep.ShouldExecuteStepResult;
import com.ideas.tetris.jems.core.test.StepJupiterTest;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.FileMetadata;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.RecordType;
import com.ideas.tetris.pacman.services.filemetadata.FileMetadataService;
import com.ideas.tetris.pacman.services.opera.OperaLoadTenantDataService;
import com.ideas.tetris.pacman.services.systemconfig.PacmanSystemConfigService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.batch.core.JobExecution;
import org.springframework.batch.core.JobInstance;
import org.springframework.batch.core.StepExecution;
import org.springframework.batch.item.ExecutionContext;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.reset;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

public class CdpOperaLoadTenantCreateFileMetadataStepTest extends StepJupiterTest {
    private static final String CORRELATION_ID = "123";
    @Mock
    StepExecution stepExecution;
    @Mock
    JobExecution jobExecution;
    @Mock
    ExecutionContext executionContext;
    @Mock
    JobInstanceWorkContext jobInstanceWorkContext;
    @Mock
    JobInstance jobInstance;

    @Mock
    OperaLoadTenantDataService operaLoadTenantDataService;

    @Mock
    PacmanSystemConfigService pacmanSystemConfigService;


    @Mock
    FileMetadataService fileMetadataService;

    @InjectMocks
    CdpOperaLoadTenantCreateFileMetadataStep instance;

    @BeforeEach
    public void setUp() {
        when(stepExecution.getJobExecution()).thenReturn(jobExecution);
        when(jobExecution.getExecutionContext()).thenReturn(executionContext);
        when(executionContext.get(JobExecutionContextKey.CORRELATION_ID)).thenReturn(CORRELATION_ID);
        when(executionContext.get(JobExecutionContextKey.FILE_METADATA_ID)).thenReturn(Integer.parseInt(CORRELATION_ID));
        when(fileMetadataService.isRecordTypePresent(Constants.OPERA_DATA_LOAD_CDP,
                CORRELATION_ID, RecordType.T2SNAP_RECORD_TYPE_ID)).thenReturn(false);
        when(jobExecution.getJobInstance()).thenReturn(jobInstance);
        when(operaLoadTenantDataService.createFileMetadata(anyString())).thenReturn(getFileMetadata());
        when(jobInstance.getJobName()).thenReturn("someJobName");
    }


    @Test
    public void testDoInvoke() throws Exception {
        JobInstanceWorkContext jobInstanceWorkContext = new JobInstanceWorkContext();
        String msg = (String) instance.doInvoke(stepExecution, jobInstanceWorkContext);
        verify(operaLoadTenantDataService).createFileMetadata(CORRELATION_ID);
        assertEquals("Correlation ID: " + CORRELATION_ID, msg);
    }

    @Test
    public void shouldExecuteStep_True() throws Exception {
        assertEquals(ShouldExecuteStepResult.TRUE, instance.shouldExecuteStep(jobExecution, jobInstanceWorkContext));
    }

    @Test
    public void shouldExecuteStep_False_ALREADY_PROCESSED() throws Exception {
        JobExecutionUtil.setCurrentLoopIsActivity(jobExecution, true);
        reset(fileMetadataService);
        when(fileMetadataService.isRecordTypePresent(Constants.OPERA_DATA_LOAD_CDP,
                CORRELATION_ID, RecordType.T2SNAP_RECORD_TYPE_ID)).thenReturn(true);
        when(fileMetadataService.findByFileNameAndLocationAndRecordType(Constants.OPERA_DATA_LOAD_CDP,
                CORRELATION_ID, RecordType.T2SNAP_RECORD_TYPE_ID)).thenReturn(getFileMetadata());
        ShouldExecuteStepResult result = instance.shouldExecuteStep(jobExecution, jobInstanceWorkContext);
        assertFalse(result.shouldExecute());
        assertTrue(result.getMessage().contains("The CDP step has already been processed for this load"));
        verify(fileMetadataService).isRecordTypePresent(Constants.OPERA_DATA_LOAD_CDP,
                CORRELATION_ID, RecordType.T2SNAP_RECORD_TYPE_ID);
        verify(fileMetadataService).findByFileNameAndLocationAndRecordType(Constants.OPERA_DATA_LOAD_CDP,
                CORRELATION_ID, RecordType.T2SNAP_RECORD_TYPE_ID);
    }

    @Test
    public void testIsStepAsync() throws Exception {
        assertFalse(instance.isStepAsync(null));
    }

    private FileMetadata getFileMetadata() {
        FileMetadata fileMetadata = new FileMetadata();
        fileMetadata.setId(1);
        return fileMetadata;
    }
}
