package com.ideas.tetris.jems.job.opera.step;

import com.ideas.tetris.jems.core.step.context.StepExecutionContextKey;
import com.ideas.tetris.jems.core.step.context.StepExecutionUtil;
import com.ideas.tetris.jems.core.step.tasklet.AbstractTaskletStep;
import com.ideas.tetris.jems.core.test.StepJupiterTest;
import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.services.marketsegment.service.AnalyticalMarketSegment;
import com.ideas.tetris.pacman.services.marketsegment.service.AnalyticalMarketSegmentService;
import com.ideas.tetris.pacman.services.opera.OperaAlertService;
import com.ideas.tetris.platform.common.job.JobName;
import com.ideas.tetris.platform.services.Stage;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.batch.core.ExitStatus;
import org.springframework.batch.core.JobInstance;
import org.springframework.batch.repeat.RepeatStatus;
import org.springframework.batch.test.MetaDataInstanceFactory;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;

import static com.ideas.tetris.pacman.services.marketsegment.service.AnalyticalMarketSegmentService.NO_AMS_MAPPINGS_FOUND;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;


public class OperaNewRateCodeAlertStepTest extends StepJupiterTest {

    @Mock
    OperaAlertService operaAlertService;
    @Mock
    AnalyticalMarketSegmentService analyticalMarketSegmentService;
    @Mock
    PacmanConfigParamsService configService;

    @InjectMocks
    OperaNewRateCodeAlertStep step;

    private JobInstance jobInstance;

    @BeforeEach
    public void setUp() throws Exception {
        super.setup();

        mockStepContextAndWorkContext(Stage.TWO_WAY);

        jobInstance = MetaDataInstanceFactory.createJobInstance(
                JobName.OperaDataLoad.toString(), MetaDataInstanceFactory.DEFAULT_JOB_INSTANCE_ID);
        jobExecution.setJobInstance(jobInstance);

        when(analyticalMarketSegmentService.getAssignedMarketSegments()).thenReturn(
                Arrays.asList(new AnalyticalMarketSegment()));
        when(configService.getBooleanParameterValue(FeatureTogglesConfigParamName.ANALYTICAL_MARKET_SEGMENT_ENABLED.value())).thenReturn(true);
    }

    @Test
    public void notAsync() {
        assertFalse(step.isStepAsync(null));
    }

    @Test
    public void createsAlerts() throws Exception {
        when(operaAlertService.checkForNewRateCodes()).thenReturn(3);
        when(analyticalMarketSegmentService.isAMSMappingPresent()).thenReturn(true);

        assertEquals(RepeatStatus.FINISHED, step.execute(stepContribution, chunkContext));
        assertEquals(ExitStatus.EXECUTING.getExitCode(), stepExecution.getExitStatus().getExitCode());

        verify(operaAlertService).checkForNewRateCodes();
        assertEquals("Found 3 new rate codes. Creating alert.", StepExecutionUtil.getFromExecutionContext(
                stepExecution, StepExecutionContextKey.RESPONSE));
    }

    @Test
    public void noAlertsCreated() throws Exception {
        when(operaAlertService.checkForNewRateCodes()).thenReturn(0);
        when(analyticalMarketSegmentService.isAMSMappingPresent()).thenReturn(true);

        assertEquals(RepeatStatus.FINISHED, step.execute(stepContribution, chunkContext));
        assertEquals(ExitStatus.EXECUTING.getExitCode(), stepExecution.getExitStatus().getExitCode());

        verify(operaAlertService).checkForNewRateCodes();
        assertEquals("No newrate codes found", StepExecutionUtil.getFromExecutionContext(
                stepExecution, StepExecutionContextKey.RESPONSE));
    }

    @Test
    public void skipIfNoAmsRules() throws Exception {
        when(analyticalMarketSegmentService.getAssignedMarketSegments()).thenReturn(
                new ArrayList<AnalyticalMarketSegment>());

        assertEquals(RepeatStatus.FINISHED, step.execute(stepContribution, chunkContext));
        assertEquals(ExitStatus.NOOP.getExitCode(), stepExecution.getExitStatus().getExitCode());
        assertEquals("No AMS mappings found", stepExecution.getExitStatus().getExitDescription());
    }

    @Test
    public void skipIfConfigParamEnabledFalse() throws Exception {
        when(configService.getBooleanParameterValue(FeatureTogglesConfigParamName.ANALYTICAL_MARKET_SEGMENT_ENABLED.value())).thenReturn(false);
        when(analyticalMarketSegmentService.isAMSMappingPresent()).thenReturn(true);

        assertEquals(RepeatStatus.FINISHED, step.execute(stepContribution, chunkContext));
        assertEquals(ExitStatus.NOOP.getExitCode(), stepExecution.getExitStatus().getExitCode());
        assertEquals("AMS feature is turned off", stepExecution.getExitStatus().getExitDescription());
    }

    @Test
    public void testShouldExecuteNoAmsMapping() {
        when(analyticalMarketSegmentService.isAMSMappingPresent()).thenReturn(false);
        final AbstractTaskletStep.ShouldExecuteStepResult shouldExecuteStepResult = step.shouldExecuteStep
                (jobExecution, jobWorkContext);
        assertFalse(shouldExecuteStepResult.shouldExecute());
        assertEquals(NO_AMS_MAPPINGS_FOUND, shouldExecuteStepResult.getMessage());
    }

    @Test
    public void testShouldExecuteAmsNotEnabled() {
        when(analyticalMarketSegmentService.getAssignedMarketSegments()).thenReturn(Collections.singletonList(new AnalyticalMarketSegment()));
        when(configService.getBooleanParameterValue(anyString())).thenReturn(false);
        assertFalse(step.shouldExecuteStep(jobExecution, jobWorkContext).shouldExecute());
    }

    @Test
    public void testShouldExecuteAmsEnabledMappingPresent() {
        when(analyticalMarketSegmentService.isAMSMappingPresent()).thenReturn(true);
        when(configService.getBooleanParameterValue(anyString())).thenReturn(true);
        assertTrue(step.shouldExecuteStep(jobExecution, jobWorkContext).shouldExecute());
        verify(analyticalMarketSegmentService).isAMSMappingPresent();
    }
}
