package com.ideas.tetris.jems.job.continuouspricing.step;

import com.ideas.tetris.jems.core.job.context.JobExecutionUtil;
import com.ideas.tetris.jems.core.job.entity.JobInstanceWorkContext;
import com.ideas.tetris.jems.core.step.tasklet.AbstractTaskletStep;
import com.ideas.tetris.jems.core.test.StepJupiterTest;
import com.ideas.tetris.pacman.services.hiltoncpmigration.service.HiltonExtendedStayCPMigrationService;
import com.ideas.tetris.pacman.services.hiltonipp.HiltonIppTransitionService;
import com.ideas.tetris.pacman.services.hiltonipp.enums.HiltonIppTransitionStage;
import com.ideas.tetris.pacman.services.job.entity.BatchStatus;
import com.ideas.tetris.platform.common.job.JobParameterKey;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;

import static com.ideas.tetris.jems.core.job.context.JobExecutionContextKey.CREATE_FG_EXECUTION_ID;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.*;

class ESCreateFGStepTest extends StepJupiterTest {
    public static final Long INSATNCE_ID = 100l;
    @InjectMocks
    private ESCreateFGStep step;
    @Mock
    private HiltonExtendedStayCPMigrationService hiltonExtendedStayCPMigrationService;
    @Mock
    private HiltonExtendedStayStepExecutionCriteria hiltonExtendedStayStepExecutionCriteria;
    @Mock
    HiltonIppTransitionService hiltonIppTransitionService;
    @Mock
    private AbstractTaskletStep.ShouldExecuteStepResult result;

    @Test
    void shouldExecuteStep() {
        jobParametersBuilder.addString(JobParameterKey.INVOKED_THROUGH_REST, "false");
        when(hiltonExtendedStayStepExecutionCriteria.shouldExecuteInAutomationFlow(jobExecution)).thenReturn(result);
        assertEquals(result, step.shouldExecuteStep(jobExecution, jobWorkContext));
        verify(hiltonExtendedStayStepExecutionCriteria).shouldExecuteInAutomationFlow(jobExecution);
    }

    @Test
    void shouldExecuteStepForHiltonIppEnabled() {
        when(hiltonIppTransitionService.isHiltonIppEnabledAndMigrationStatusIs(HiltonIppTransitionStage.TRANSITION_IN_PROGRESS)).thenReturn(Boolean.TRUE);
        AbstractTaskletStep.ShouldExecuteStepResult shouldExecuteStep = step.shouldExecuteStep(jobExecution, jobWorkContext);
        assertTrue(shouldExecuteStep.shouldExecute());
        verify(hiltonExtendedStayStepExecutionCriteria, Mockito.times(0)).shouldExecuteInAutomationFlow(jobExecution);
    }

    @Test
    void doInvokeFirstTime() throws Exception {
        JobExecutionUtil.setOnExecutionContext(stepExecution.getJobExecution(), CREATE_FG_EXECUTION_ID, null);
        step.doInvoke(stepExecution, new JobInstanceWorkContext());
        verify(hiltonExtendedStayCPMigrationService).startCreateFGJobForESMigration(false);
    }

    @Test
    void doInvokeDoNotCreateNewJobInstance() throws Exception {
        JobExecutionUtil.setOnExecutionContext(stepExecution.getJobExecution(), CREATE_FG_EXECUTION_ID, INSATNCE_ID);
        when(hiltonExtendedStayCPMigrationService.getJobExecutionStatusByJobExecutionId(Long.valueOf(INSATNCE_ID))).thenReturn(BatchStatus.UNKNOWN.toString());
        step.doInvoke(stepExecution, new JobInstanceWorkContext());
        verify(hiltonExtendedStayCPMigrationService, times(0)).startCreateFGJobForESMigration(false);
    }

    @Test
    void doInvokeStartJobIfPreviousAbandoned() throws Exception {
        JobExecutionUtil.setOnExecutionContext(stepExecution.getJobExecution(), CREATE_FG_EXECUTION_ID, INSATNCE_ID);
        when(hiltonExtendedStayCPMigrationService.getJobExecutionStatusByJobExecutionId(Long.valueOf(INSATNCE_ID))).thenReturn(BatchStatus.ABANDONED.toString());
        step.doInvoke(stepExecution, new JobInstanceWorkContext());
        verify(hiltonExtendedStayCPMigrationService, times(1)).startCreateFGJobForESMigration(false);
    }
}
