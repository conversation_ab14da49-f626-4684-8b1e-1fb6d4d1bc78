package com.ideas.tetris.jems.job.opera.step;

import com.ideas.tetris.jems.core.job.context.JobExecutionContextKey;
import com.ideas.tetris.jems.core.job.context.JobExecutionUtil;
import com.ideas.tetris.jems.core.job.entity.JobInstanceWorkContext;
import com.ideas.tetris.jems.core.step.tasklet.AbstractTaskletStep;
import com.ideas.tetris.jems.core.test.StepJupiterTest;
import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.services.opera.OperaPreProcessStageDataService;
import com.ideas.tetris.platform.common.contextholder.PlatformThreadLocalContextHolder;
import com.ideas.tetris.platform.common.job.JobStepContext;
import com.ideas.tetris.platform.common.xml.schema.workcontext.v1.WorkContextType;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.junit.jupiter.api.Test;
import org.mockito.Answers;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.batch.core.JobExecution;
import org.springframework.batch.core.JobInstance;
import org.springframework.batch.core.StepExecution;
import org.springframework.batch.item.ExecutionContext;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.ArgumentMatchers.nullable;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

public class OperaPreProcessStageGroupAnalyticalMarketSegmentsDefaultStepTest extends StepJupiterTest {

    public static final String CORRELATION_ID = "123";
    @Mock
    OperaPreProcessStageDataService operaPreProcessStageDataService;

    @Mock(answer = Answers.RETURNS_DEEP_STUBS)
    StepExecution stepExecution;
    @Mock
    JobExecution jobExecution;
    @Mock
    ExecutionContext executionContext;
    @Mock
    JobInstanceWorkContext jobInstanceWorkContext;
    @Mock
    PacmanConfigParamsService configService;

    @InjectMocks
    OperaPreProcessStageGroupAnalyticalMarketSegmentsDefaultStep step;

    @Test
    public void testIsStepAsync() {
        assertTrue(step.isStepAsync(stepExecution));
    }


    @Test
    public void testDoInvoke() throws Exception {
        JobInstanceWorkContext jobInstanceWorkContext = new JobInstanceWorkContext();

        when(stepExecution.getJobExecution()).thenReturn(jobExecution);
        when(jobExecution.getExecutionContext()).thenReturn(executionContext);
        when(executionContext.get(JobExecutionContextKey.CORRELATION_ID)).thenReturn(CORRELATION_ID);
        JobStepContext jobStepContext = new JobStepContext();
        PlatformThreadLocalContextHolder.setJobStepContext(jobStepContext);

        step.doInvoke(stepExecution, jobInstanceWorkContext);
        verify(operaPreProcessStageDataService).updateGroupAnalyticalMarketSegmentsDefault(any(JobStepContext.class), nullable(WorkContextType.class), eq(CORRELATION_ID));
    }

    @Test
    public void shouldExecuteStep_True() throws Exception {
        JobExecutionUtil.setCurrentLoopIsActivity(jobExecution, true);
        JobInstance jobInstance = Mockito.mock(JobInstance.class);

        when(stepExecution.getJobExecution()).thenReturn(jobExecution);
        when(jobExecution.getExecutionContext()).thenReturn(executionContext);
        when(executionContext.get(JobExecutionContextKey.LOOP_CURRENT_IS_ACTIVITY)).thenReturn(Boolean.TRUE);
        Mockito.when(jobExecution.getJobInstance()).thenReturn(jobInstance);
        Mockito.when(jobInstance.getJobName()).thenReturn("someJobName");
        when(configService.getBooleanParameterValue(FeatureTogglesConfigParamName.OPERA_TRANSFORM_TRANSACTIONS_MULTI_STEP)).thenReturn(true);

        assertEquals(AbstractTaskletStep.ShouldExecuteStepResult.TRUE, step.shouldExecuteStep(jobExecution, jobInstanceWorkContext));
    }

    @Test
    public void shouldExecuteStep_False() throws Exception {
        JobExecutionUtil.setCurrentLoopIsActivity(jobExecution, true);
        JobInstance jobInstance = Mockito.mock(JobInstance.class);

        when(stepExecution.getJobExecution()).thenReturn(jobExecution);
        when(jobExecution.getExecutionContext()).thenReturn(executionContext);
        when(executionContext.get(JobExecutionContextKey.LOOP_CURRENT_IS_ACTIVITY)).thenReturn(Boolean.FALSE);
        Mockito.when(jobExecution.getJobInstance()).thenReturn(jobInstance);
        Mockito.when(jobInstance.getJobName()).thenReturn("someJobName");
        when(configService.getBooleanParameterValue(FeatureTogglesConfigParamName.OPERA_TRANSFORM_TRANSACTIONS_MULTI_STEP)).thenReturn(true);

        AbstractTaskletStep.ShouldExecuteStepResult result = step.shouldExecuteStep(jobExecution, jobInstanceWorkContext);

        assertFalse(result.shouldExecute());
    }

    @Test
    public void shouldExecuteStep_Multi_Step_False() throws Exception {
        JobExecutionUtil.setCurrentLoopIsActivity(jobExecution, true);
        JobInstance jobInstance = Mockito.mock(JobInstance.class);

        when(stepExecution.getJobExecution()).thenReturn(jobExecution);
        when(jobExecution.getExecutionContext()).thenReturn(executionContext);
        when(executionContext.get(JobExecutionContextKey.LOOP_CURRENT_IS_ACTIVITY)).thenReturn(Boolean.TRUE);
        Mockito.when(jobExecution.getJobInstance()).thenReturn(jobInstance);
        Mockito.when(jobInstance.getJobName()).thenReturn("someJobName");
        when(configService.getBooleanParameterValue(FeatureTogglesConfigParamName.OPERA_TRANSFORM_TRANSACTIONS_MULTI_STEP)).thenReturn(false);

        AbstractTaskletStep.ShouldExecuteStepResult result = step.shouldExecuteStep(jobExecution, jobInstanceWorkContext);

        assertFalse(result.shouldExecute());
    }
}