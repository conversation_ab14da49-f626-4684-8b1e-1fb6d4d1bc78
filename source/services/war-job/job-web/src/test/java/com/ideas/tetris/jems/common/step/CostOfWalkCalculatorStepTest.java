package com.ideas.tetris.jems.common.step;

import com.ideas.tetris.jems.core.job.context.JobExecutionContextKey;
import com.ideas.tetris.jems.core.job.context.JobExecutionUtil;
import com.ideas.tetris.jems.core.job.entity.JobInstanceWorkContext;
import com.ideas.tetris.jems.core.step.tasklet.AbstractTaskletStep.ShouldExecuteStepResult;
import com.ideas.tetris.jems.core.test.StepJupiterTest;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.FileMetadata;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.RecordType;
import com.ideas.tetris.pacman.services.filemetadata.FileMetadataService;
import com.ideas.tetris.pacman.services.marketsegment.entity.ProcessStatus;
import com.ideas.tetris.pacman.services.opera.CostOfWalkCalculator;
import com.ideas.tetris.pacman.services.systemconfig.PacmanSystemConfigService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.batch.core.JobExecution;
import org.springframework.batch.core.JobInstance;
import org.springframework.batch.core.StepExecution;
import org.springframework.batch.item.ExecutionContext;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.reset;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoMoreInteractions;
import static org.mockito.Mockito.when;

public class CostOfWalkCalculatorStepTest extends StepJupiterTest {
    private static final String CORRELATION_ID = "123";
    public static final int FILE_METADATA_ID = 1;
    @Mock
    StepExecution stepExecution;
    @Mock
    JobExecution jobExecution;
    @Mock
    ExecutionContext executionContext;

    @Mock
    private CostOfWalkCalculator costOfWalkCalculator;

    @Mock
    private FileMetadataService fileMetadataService;

    @Mock
    private PacmanSystemConfigService pacmanSystemConfigService;

    @Mock
    private JobInstanceWorkContext jobInstanceWorkContext;

    @Mock
    private JobInstance jobInstance;

    @InjectMocks
    private CostOfWalkCalculatorStep instance;

    @BeforeEach
    public void setUp() {
        when(stepExecution.getJobExecution()).thenReturn(jobExecution);
        when(jobExecution.getExecutionContext()).thenReturn(executionContext);
        when(executionContext.get(JobExecutionContextKey.CORRELATION_ID)).thenReturn(CORRELATION_ID);
        when(executionContext.get(JobExecutionContextKey.FILE_METADATA_ID)).thenReturn(FILE_METADATA_ID);
        when(jobExecution.getJobInstance()).thenReturn(jobInstance);
        when(fileMetadataService.getFileMetadataById(FILE_METADATA_ID)).thenReturn(getIncompleteFileMetadata());
        when(jobInstance.getJobName()).thenReturn("someJobName");
    }


    @Test
    public void testDoInvoke() throws Exception {
        JobInstanceWorkContext jobInstanceWorkContext = new JobInstanceWorkContext();
        reset(fileMetadataService);
        when(fileMetadataService.deriveCompleteAndSaveFileMetadataFromAnother(FILE_METADATA_ID, RecordType.TOTAL_HOTEL_SUMMARY)).thenReturn(new FileMetadata());

        String msg = (String) instance.doInvoke(stepExecution, jobInstanceWorkContext);
        assertEquals("Correlation ID: " + CORRELATION_ID, msg);
        verify(costOfWalkCalculator).calculateCostOfWalk();
    }

    @Test
    public void shouldExecuteStep_True() throws Exception {
        JobExecutionUtil.setCurrentLoopIsActivity(jobExecution, true);
        when(executionContext.get(JobExecutionContextKey.LOOP_CURRENT_IS_ACTIVITY)).thenReturn(Boolean.TRUE);

        assertEquals(ShouldExecuteStepResult.TRUE, instance.shouldExecuteStep(jobExecution, jobInstanceWorkContext));
    }

    @Test
    public void shouldExecuteStep_False() throws Exception {
        JobExecutionUtil.setCurrentLoopIsActivity(jobExecution, true);
        when(executionContext.get(JobExecutionContextKey.LOOP_CURRENT_IS_ACTIVITY)).thenReturn(Boolean.FALSE);
        ShouldExecuteStepResult result = instance.shouldExecuteStep(jobExecution, jobInstanceWorkContext);

        verifyNoMoreInteractions(pacmanSystemConfigService);
        verifyNoMoreInteractions(fileMetadataService);
        assertFalse(result.shouldExecute());
    }

    @Test
    public void shouldExecuteStep_False_ALREADY_PROCESSED() throws Exception {
        JobExecutionUtil.setCurrentLoopIsActivity(jobExecution, true);
        reset(fileMetadataService);
        when(fileMetadataService.getFileMetadataById(FILE_METADATA_ID)).thenReturn(getCompleteFileMetadata());
        when(executionContext.get(JobExecutionContextKey.LOOP_CURRENT_IS_ACTIVITY)).thenReturn(Boolean.TRUE);
        ShouldExecuteStepResult result = instance.shouldExecuteStep(jobExecution, jobInstanceWorkContext);
        assertFalse(result.shouldExecute());
        assertTrue(result.getMessage().contains("This load has already been processed for file_metadata id"));
        verify(fileMetadataService).getFileMetadataById(FILE_METADATA_ID);
    }

    @Test
    public void testIsStepAsync() throws Exception {
        assertFalse(instance.isStepAsync(null));
    }

    private FileMetadata getIncompleteFileMetadata() {
        FileMetadata fileMetadata = getCompleteFileMetadata();
        fileMetadata.setProcessStatusId(ProcessStatus.IN_PROGRESS);
        return fileMetadata;
    }

    private FileMetadata getCompleteFileMetadata() {
        FileMetadata fileMetadata = new FileMetadata();
        fileMetadata.setId(1);
        fileMetadata.setProcessStatusId(ProcessStatus.SUCCESSFUL);
        return fileMetadata;
    }
}
