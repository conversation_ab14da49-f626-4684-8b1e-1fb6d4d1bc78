package com.ideas.tetris.jems.ngi.job.opera.step;

import com.ideas.tetris.jems.core.job.context.JobExecutionContextKey;
import com.ideas.tetris.jems.core.job.context.JobExecutionUtil;
import com.ideas.tetris.jems.core.step.context.StepExecutionContextKey;
import com.ideas.tetris.jems.core.step.tasklet.AbstractTaskletStep;
import com.ideas.tetris.jems.core.test.StepJupiterTest;
import com.ideas.tetris.jems.ngi.client.NGIRestClient;
import com.ideas.tetris.jems.ngi.dto.decision.NGIOperaDecisionDeliveryEvent;
import com.ideas.tetris.pacman.common.configparams.IntegrationConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.services.configsparam.service.ConfigParameterNameService;
import com.ideas.tetris.pacman.services.ngi.NGIDecisionService;
import com.ideas.tetris.pacman.services.opera.ProcessDecisionStatus;
import com.ideas.tetris.platform.common.job.JobParameterKey;
import com.ideas.tetris.platform.common.rest.mapper.RestEndpoints;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.junit.jupiter.api.AfterEach;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.batch.core.ExitStatus;
import org.testng.annotations.Test;

import java.util.Collections;
import java.util.List;

import static com.ideas.tetris.jems.ngi.job.opera.step.AbstractNGIOperaDecisionStep.EXTERNAL_SYSTEM_OPERA;
import static com.ideas.tetris.pacman.common.constants.Constants.LAST_ROOM_VALUE_BY_ROOM_CLASS;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyZeroInteractions;
import static org.mockito.Mockito.when;
import static org.testng.Assert.assertEquals;
import static org.testng.Assert.assertFalse;
import static org.testng.Assert.assertSame;
import static org.testng.Assert.assertTrue;

public class AbstractNGIOperaDecisionDeliverStepTest extends StepJupiterTest {
    @Mock
    private NGIDecisionService ngiDecisionService;
    @Mock
    private NGIRestClient ngiRestClient;
    @Mock
    private PacmanConfigParamsService configParamsService;
    @Mock
    private ProcessDecisionStatus processDecisionStatus;

    @InjectMocks
    private NGIOperaLRVDeliverStep step;

    private void setup(String destinationType, String deliveryType) {
        setup();
        jobWorkContext.setClientCode(CLIENT_CODE);
        jobWorkContext.setPropertyId(5);
        jobWorkContext.setPropertyCode(PROP_CODE);
        jobWorkContext.setJobInstanceId(1L);
        jobWorkContext.setPropertyStage("TwoWay");
        stepExecution.setExitStatus(ExitStatus.COMPLETED);
        jobParametersBuilder.addString(JobParameterKey.CLIENT_CODE, CLIENT_CODE);
        jobParametersBuilder.addString(JobParameterKey.PROPERTY_CODE, PROP_CODE);
        jobParametersBuilder.addString(JobParameterKey.OPERA_EVENT_OUTBOUND_USER, "user1");
        jobParametersBuilder.addString(JobParameterKey.OPERA_EVENT_URL, "URL");
        jobParametersBuilder.addString(JobParameterKey.OPERA_EVENT_OUTBOUND_USER, "user1");
        jobParametersBuilder.addString(JobParameterKey.OPERA_EVENT_OUTBOUND_PASSWORD, "password");
        jobParametersBuilder.addString(JobParameterKey.OPERA_DESTINATION_TYPE, destinationType);
        regenerateStepContext();
        stepExecution.setId(123L);
        stepExecution.getJobExecution().setId(456L);
        when(configParamsService.getParameterValue(CLIENT_CODE, PROP_CODE, IntegrationConfigParamName.DECISION_SYNC, EXTERNAL_SYSTEM_OPERA, LAST_ROOM_VALUE_BY_ROOM_CLASS)).thenReturn(Boolean.FALSE);
        when(configParamsService.getParameterValue(anyString())).thenReturn(destinationType);
        when(configParamsService.getBooleanParameterValue(IntegrationConfigParamName.OPTIMIZED_DAILY_BAR_AGILE_RATE_UPLOAD.value(Constants.OPERA))).thenReturn(false);
        JobExecutionUtil.setOnExecutionContext(jobExecution, JobExecutionContextKey.OPERATION_TYPE, "BDE");
    }

    @AfterEach
    public void tearDown() {
        System.setProperty("useNewNgiDecisionDeliveryJob", "false");
    }

    @Test
    public void testDoInvokeOXI() throws Exception {
        setup("OXI", "NGI");
        System.setProperty("useNewNgiDecisionDeliveryJob", "true");
        ArgumentCaptor<NGIOperaDecisionDeliveryEvent> ngiOperaDecisionDeliveryEventArgumentCaptor = ArgumentCaptor.forClass(NGIOperaDecisionDeliveryEvent.class);
        List<String> correlationList = Collections.singletonList("200");
        when(ngiRestClient.post(eq(RestEndpoints.POST_NGI_OPERA_DECISION_DELIVER_EVENT_V2), ngiOperaDecisionDeliveryEventArgumentCaptor.capture(), eq(List.class)))
                .thenReturn(correlationList);
        when(configParamsService.getBooleanParameterValue("pacman.feature.NGIDecisionDeliveryByDecisionType")).thenReturn(true);
        String actualResponseCode = (String) step.doInvoke(stepExecution, jobWorkContext);
        assertSame(correlationList.get(0), actualResponseCode);
        String expectedClientEnvironmentId = stepExecution.getJobExecutionId() + "-" + stepExecution.getId();
        assertEquals(ngiOperaDecisionDeliveryEventArgumentCaptor.getValue().getClientEnvironmentId(), expectedClientEnvironmentId);
    }

    @Test
    public void testShouldExecute() {
        setup("OXI", "NGI");
        ConfigParameterNameService configParameterNameService = new ConfigParameterNameService();
        configParameterNameService.setPacmanConfigParamsService(configParamsService);
        step.setConfigParameterNameService(configParameterNameService);
        when(configParamsService.getParameterValue(Constants.PACMAN_INTEGRATION + EXTERNAL_SYSTEM_OPERA + "." + step.getDecisionConstant() + Constants.UPLOAD_TYPE)).thenReturn(Constants.FULL);
        when(ngiDecisionService.getStatusForLastRoomValueDecisions(jobWorkContext.getPropertyId().toString(), EXTERNAL_SYSTEM_OPERA, "BDE")).thenReturn(true);

        AbstractTaskletStep.ShouldExecuteStepResult shouldExecuteResult = step.shouldExecuteStep(jobExecution, jobWorkContext);

        assertTrue(shouldExecuteResult.shouldExecute());
        verify(ngiDecisionService, times(1)).getStatusForLastRoomValueDecisions(jobWorkContext.getPropertyId().toString(), EXTERNAL_SYSTEM_OPERA, "BDE");
    }

    @Test
    public void testAfterStep() {
        setup("OXI", "NGI");

        stepExecution.setExitStatus(ExitStatus.COMPLETED);
        stepExecution.getExecutionContext().put(StepExecutionContextKey.RESPONSE, "response");

        step.afterStep(stepExecution);

        verify(processDecisionStatus, times(1)).success(step.getDecisionConstant(), EXTERNAL_SYSTEM_OPERA, "Success");
    }

    @Test
    public void testAfterStep_StepFailed() throws Exception {
        setup("OXI", "NGI");

        // given:
        stepExecution.setExitStatus(ExitStatus.FAILED);
        stepExecution.getExecutionContext().put(StepExecutionContextKey.RESPONSE, "response");

        // when:
        step.afterStep(stepExecution);

        // then:
        verifyZeroInteractions(processDecisionStatus);

    }

    @Test
    public void testIsStepAsyncWithSyncTrue() {
        when(configParamsService.getParameterValue(CLIENT_CODE, PROP_CODE, IntegrationConfigParamName.DECISION_SYNC, EXTERNAL_SYSTEM_OPERA, LAST_ROOM_VALUE_BY_ROOM_CLASS)).thenReturn(Boolean.TRUE);
        assertFalse(step.isStepAsync(stepExecution));
    }

    @Test
    public void testIsStepAsyncWithSyncFalse() {
        when(configParamsService.getParameterValue(CLIENT_CODE, PROP_CODE, IntegrationConfigParamName.DECISION_SYNC, EXTERNAL_SYSTEM_OPERA, LAST_ROOM_VALUE_BY_ROOM_CLASS)).thenReturn(Boolean.FALSE);
        assertTrue(step.isStepAsync(stepExecution));
    }

    @Test
    public void testAfterStepSync() {
        setup("OXI", "NGI");

        when(configParamsService.getParameterValue(CLIENT_CODE, PROP_CODE, IntegrationConfigParamName.DECISION_SYNC, EXTERNAL_SYSTEM_OPERA, LAST_ROOM_VALUE_BY_ROOM_CLASS)).thenReturn(Boolean.TRUE);
        stepExecution.setExitStatus(ExitStatus.COMPLETED);

        step.afterStep(stepExecution);

        verify(processDecisionStatus).success(step.getDecisionConstant(), EXTERNAL_SYSTEM_OPERA, "Success");
    }

}
