package com.ideas.tetris.jems.common.step.groupflooroverride;


import com.ideas.tetris.jems.core.job.context.JobExecutionUtil;
import com.ideas.tetris.jems.core.job.entity.JobInstanceWorkContext;
import com.ideas.tetris.jems.core.step.context.StepExecutionUtil;
import com.ideas.tetris.jems.core.step.tasklet.ChunkOrientedBusinessServiceStep;
import com.ideas.tetris.pacman.common.configparams.ConfigParamName;
import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.Decision;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.decision.DecisionService;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.joda.time.LocalDate;
import org.springframework.batch.core.ExitStatus;
import org.springframework.batch.core.JobExecution;
import org.springframework.batch.core.StepExecution;
import org.springframework.batch.core.StepExecutionListener;
import org.springframework.batch.item.ItemWriter;
import org.springframework.stereotype.Component;

import javax.inject.Inject;

import static com.ideas.tetris.jems.core.step.context.StepExecutionContextKey.BAR_OVERRIDE_DECISION;
import static com.ideas.tetris.jems.core.step.context.StepExecutionContextKey.CHUNKS_PROCESSED;
import static com.ideas.tetris.jems.core.step.context.StepExecutionContextKey.GROUP_FLOOR_OVERRIDE_DECISION;
import org.springframework.beans.factory.annotation.Autowired;

@Component
public abstract class AbstractGroupFloorOverrideEvaluationChunkOrientedStep extends ChunkOrientedBusinessServiceStep<LocalDate, LocalDate> implements StepExecutionListener {

    // Chunk size indicates number of days to process in a chunk as they must be processed together
    public static final int DEFAULT_CHUNK_SIZE = 90;
    @Autowired
	protected DateService dateService;
    @Autowired
	private DecisionService decisionService;
    @Autowired
	private GroupFloorOverrideEvaluationChunkOrientedItemWriter itemWriter;
    @Autowired
	private PacmanConfigParamsService configParamsService;

    @Override
    protected ShouldExecuteStepResult shouldExecuteStep(JobExecution jobExecution, JobInstanceWorkContext jobInstanceWorkContext) {
        if (!JobExecutionUtil.isStageAtLeastOneWay(jobInstanceWorkContext)) {
            return new ShouldExecuteStepResult(false, "Skipping execution step since stage is not at least one way");
        }
        if (parameterNotConfigured(FeatureTogglesConfigParamName.IS_GROUP_FLOOR_OVERRIDE_ENABLED)) {
            return new ShouldExecuteStepResult(false, "Skipping execution step since property is not configured for Group Floor Override Evaluation");
        }

        return ShouldExecuteStepResult.TRUE;
    }

    private Boolean parameterNotConfigured(ConfigParamName configParamName) {
        Boolean isConfigured = configParamsService.getParameterValue(configParamName);
        return !isConfigured;
    }

    public void setPacmanConfigParamsService(PacmanConfigParamsService pacmanConfigParamsService) {
        this.configParamsService = pacmanConfigParamsService;
    }

    @Override
    protected ItemWriter<LocalDate> itemWriter() {
        return itemWriter;
    }

    @Override
    protected int chunkSize() {
        return DEFAULT_CHUNK_SIZE;
    }

    @Override
    public void beforeStep(StepExecution stepExecution) {
        Integer chunksProcessed = (Integer) StepExecutionUtil.getFromExecutionContext(stepExecution, CHUNKS_PROCESSED);
        if (chunksProcessed == null) {
            StepExecutionUtil.setOnExecutionContext(stepExecution, CHUNKS_PROCESSED, 0);
        }

        Decision groupFloorOverrideDecision = decisionService.createGroupFloorOverrideDecision();
        StepExecutionUtil.setOnExecutionContext(stepExecution, GROUP_FLOOR_OVERRIDE_DECISION, groupFloorOverrideDecision.getId());
        Decision barOverrideDecision = decisionService.createBAROverrideDecision();
        StepExecutionUtil.setOnExecutionContext(stepExecution, BAR_OVERRIDE_DECISION, barOverrideDecision.getId());
    }

    @Override
    public ExitStatus afterStep(StepExecution stepExecution) {
        return stepExecution.getExitStatus();
    }

}
