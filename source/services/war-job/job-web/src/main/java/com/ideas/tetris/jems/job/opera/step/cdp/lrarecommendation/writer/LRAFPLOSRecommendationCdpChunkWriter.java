package com.ideas.tetris.jems.job.opera.step.cdp.lrarecommendation.writer;


import com.ideas.tetris.jems.common.step.chunk.DateChunkItemWriter;
import com.ideas.tetris.pacman.services.api.Chunkable;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.Decision;
import com.ideas.tetris.pacman.services.lra.fplos.LRAFPLOSRecommendationCdpService;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.stereotype.Component;

import java.lang.annotation.Retention;
import java.lang.annotation.Target;
import java.util.Date;

import static java.lang.annotation.ElementType.FIELD;
import static java.lang.annotation.ElementType.METHOD;
import static java.lang.annotation.ElementType.PARAMETER;
import static java.lang.annotation.ElementType.TYPE;
import static java.lang.annotation.RetentionPolicy.RUNTIME;
import org.springframework.beans.factory.annotation.Autowired;

@Component
@StepScope
@LRAFPLOSRecommendationCdpChunkWriter.Qualifier
public class LRAFPLOSRecommendationCdpChunkWriter extends DateChunkItemWriter<Decision> {
    @javax.inject.Qualifier
    @Retention(RUNTIME)
    @Target({TYPE, METHOD, FIELD, PARAMETER})
    public @interface Qualifier {
    }

    @Autowired
	private LRAFPLOSRecommendationCdpService recommendaitonService;

    @Override
    public Chunkable<Date, Decision> getChunkable() {
        return recommendaitonService;
    }
}
