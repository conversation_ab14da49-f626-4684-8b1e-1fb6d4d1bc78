package com.ideas.tetris.jems.job.rdl.step.focus;

import com.ideas.tetris.jems.core.step.context.StepExecutionUtil;
import com.ideas.tetris.rdl.model.RDLRate;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.stereotype.Component;

import java.util.Iterator;

import static com.ideas.tetris.jems.core.step.context.StepExecutionContextKey.CHUNK_SIZE;

@Component
@StepScope
public class FocusHistoryRateItemReader extends AbstractFocusRateItemReader<RDLRate> {

    private Iterator<RDLRate> itr;

    @Override
    public RDLRate doRead() {
        if (itr == null) {
            itr = readRdlRatesPageGroupsData().iterator();
            setTotalChunks(getRdlPullRateRequest(), getChunkSize());
        }
        return getNextChunk();
    }

    private RDLRate getNextChunk() {
        if (itr.hasNext()) {
            return itr.next();
        } else if (getRdlPullRateRequest().getTotalPages() > getRdlPullRateRequest().getCurrentPage()) {
            itr = readRdlRatesPageGroupsData().iterator();
            return getNextChunk();
        }
        return null;
    }

    private int getChunkSize() {
        return (Integer) StepExecutionUtil.getFromExecutionContext(stepExecution, CHUNK_SIZE);
    }

}
