package com.ideas.tetris.jems.core.job.flow;

import com.ideas.tetris.jems.core.step.status.ExtendedExitStatus;
import org.apache.log4j.Logger;
import org.springframework.batch.core.ExitStatus;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.job.builder.FlowBuilder;
import org.springframework.batch.core.job.builder.FlowJobBuilder;
import org.springframework.stereotype.Component;

@Component
public class FlowBuilderHelper {

    private static final Logger LOGGER = Logger.getLogger(FlowBuilderHelper.class.getName());
    private static final String NOOP_PATTERN = ExitStatus.NOOP.getExitCode(); // + "*";  // matches anything starting with NOOP

    /**
     * Since an asynchronous business service call was made, the Job should be
     * stopped while the job awaits an async response and the step's exit status
     * should be 'RUNNING_ASYNC'.  When it is 'COMPLETED', the next step should be executed
     * and if there is no next step, the job should end.  When the Step has a 'NOOP' exit status
     * the step didn't execute and we should move onto the next step if one exists or end the job.
     */
    public FlowBuilder<FlowJobBuilder> applyNextStepFlow(FlowBuilder<FlowJobBuilder> flowBuilder, Step currentStep, Step nextStep) {
        // If the Step has an ExitStatus of 'RUNNING_ASYNC', the job should stop so that we wait for an ASYNC response
        LOGGER.debug("Applying Stop Step when Step: " + currentStep.getName() + " finishing in " + ExtendedExitStatus.RUNNING_ASYNC);
        flowBuilder = flowBuilder.from(currentStep).on(ExtendedExitStatus.RUNNING_ASYNC).stop();

        // If the currentStep is in 'COMPLETED' state, continue on to the next step
        if (nextStep != null) {
            LOGGER.debug("Applying Next Step Flow from Step: " + currentStep.getName() + " to Step: " + nextStep.getName());
            flowBuilder = flowBuilder.from(currentStep).on(NOOP_PATTERN).to(nextStep);
            return flowBuilder.from(currentStep).on(ExitStatus.COMPLETED.getExitCode()).to(nextStep);
        }

        // If there isn't a nextStep, the job should end on 'NOOP'.
        LOGGER.debug("Applying End Job from Step: " + currentStep.getName() + " finishes in " + ExitStatus.NOOP.getExitCode() + " with no next step");
        flowBuilder = flowBuilder.from(currentStep).on(NOOP_PATTERN).end();

        // If there isn't a nextStep, the job should end on 'COMPLETED'.
        LOGGER.debug("Applying End Job from Step: " + currentStep.getName() + " finishes in " + ExitStatus.COMPLETED.getExitCode() + " with no next step");
        return flowBuilder.from(currentStep).on(ExitStatus.COMPLETED.getExitCode()).end();
    }

}
