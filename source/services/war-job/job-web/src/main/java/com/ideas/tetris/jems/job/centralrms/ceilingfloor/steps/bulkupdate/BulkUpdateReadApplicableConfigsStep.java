package com.ideas.tetris.jems.job.centralrms.ceilingfloor.steps.bulkupdate;


import com.ideas.tetris.jems.core.job.context.JobExecutionUtil;
import com.ideas.tetris.jems.core.job.entity.JobInstanceWorkContext;
import com.ideas.tetris.jems.core.job.entity.SerializableJobExecutionParam;
import com.ideas.tetris.jems.core.repository.dao.SerializableJobExecutionParamDao;
import com.ideas.tetris.jems.job.centralrms.abstraction.CentralRMSBusinessServiceStep;
import com.ideas.tetris.pacman.services.centralrms.models.pricing.bulkupdate.v2.PricingBulkUpdate;
import com.ideas.tetris.pacman.services.centralrms.services.pricing.CentralRMSPricingConfigurationService;
import com.ideas.tetris.pacman.services.pricingconfiguration.entity.TransientPricingBaseAccomType;
import com.ideas.tetris.platform.common.errorhandling.ErrorCode;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import org.springframework.batch.core.JobExecution;
import org.springframework.batch.core.StepExecution;
import org.springframework.stereotype.Component;

import javax.inject.Inject;
import java.util.List;

import static com.ideas.tetris.jems.core.job.context.JobExecutionContextKey.CONFIGURABLE_PRICING_TRANSIENT_CONFIGS;
import static com.ideas.tetris.pacman.services.specialevent.service.SpecialEventJobService.MISSING_JOB_PARAMETER;
import static com.ideas.tetris.platform.common.job.JobParameterKey.INCOMING_SERIALIZABLE;
import org.springframework.beans.factory.annotation.Autowired;

@Component
public class BulkUpdateReadApplicableConfigsStep extends CentralRMSBusinessServiceStep {

    @Autowired
	private CentralRMSPricingConfigurationService centralRMSPricingConfigurationService;

    @Autowired
	private SerializableJobExecutionParamDao serializableJobExecutionParamDao;

    @Override
    public Object invoke(StepExecution stepExecution, JobInstanceWorkContext jobInstanceWorkContext) throws Exception {
        final JobExecution jobExecution = stepExecution.getJobExecution();
        PricingBulkUpdate bulkUpdateSettings = getBulkUpdateSettings(jobExecution);

        List<TransientPricingBaseAccomType> configs = centralRMSPricingConfigurationService.getApplicablePricingConfigs(bulkUpdateSettings.getStartDate(), bulkUpdateSettings.getEndDate());
        JobExecutionUtil.setOnExecutionContext(jobExecution, CONFIGURABLE_PRICING_TRANSIENT_CONFIGS, configs);

        return null;
    }

    private PricingBulkUpdate getBulkUpdateSettings(JobExecution jobExecution) {
        Integer incomingSerializableId = JobExecutionUtil.getIntegerFromJobParameter(jobExecution, INCOMING_SERIALIZABLE);
        if (incomingSerializableId == null) {
            throw new TetrisException(ErrorCode.JOB_INVALID_JOB_PARAMETERS, MISSING_JOB_PARAMETER + INCOMING_SERIALIZABLE);
        }

        SerializableJobExecutionParam serializableJobExecutionParam = serializableJobExecutionParamDao.getSerializableJobExecutionParam(incomingSerializableId);
        return (PricingBulkUpdate) serializableJobExecutionParam.getSerializable();
    }
}
