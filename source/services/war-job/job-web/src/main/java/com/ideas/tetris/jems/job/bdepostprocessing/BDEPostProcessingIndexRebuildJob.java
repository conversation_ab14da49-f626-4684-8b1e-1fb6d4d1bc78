package com.ideas.tetris.jems.job.bdepostprocessing;

import com.ideas.tetris.jems.common.step.regulator.AcquireRegulatorRequestStep;
import com.ideas.tetris.jems.common.step.regulator.CompleteRegulatorRequestStep;
import com.ideas.tetris.jems.common.step.workcontext.DetermineWorkContextFromPropertyIdStep;
import com.ideas.tetris.jems.core.job.AbstractJob;
import com.ideas.tetris.jems.job.bdepostprocessing.listener.AutoAbandonIndexRebuildJobExecutionOnStopListener;
import com.ideas.tetris.jems.job.bdepostprocessing.step.PrunePropertyIndexesLogStep;
import com.ideas.tetris.jems.job.bdepostprocessing.step.RebuildPropertyIndexesStep;
import com.ideas.tetris.platform.common.job.JobParameterKey;
import org.springframework.stereotype.Component;

import javax.inject.Inject;
import org.springframework.beans.factory.annotation.Autowired;

@Component("BDEPostProcessingIndexRebuildJob")
public class BDEPostProcessingIndexRebuildJob extends AbstractJob {

    @Autowired
    AutoAbandonIndexRebuildJobExecutionOnStopListener autoAbandonJobExecutionOnStopListener;
    @Autowired
    DetermineWorkContextFromPropertyIdStep determineWorkContextFromPropertyIdStep;
    @Autowired
    AcquireRegulatorRequestStep addRegulatorRequestStep;
    @Autowired
    PrunePropertyIndexesLogStep prunePropertyIndexesLogStep;
    @Autowired
    RebuildPropertyIndexesStep rebuildPropertyIndexesStep;
    @Autowired
    CompleteRegulatorRequestStep completeRegulatorRequestStep;

    @Override
    public String[] requiredParameters() {
        return new String[]{JobParameterKey.PROPERTY_ID, JobParameterKey.DATE};
    }
}
