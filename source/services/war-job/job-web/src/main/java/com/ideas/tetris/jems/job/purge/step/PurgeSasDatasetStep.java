package com.ideas.tetris.jems.job.purge.step;


import com.ideas.tetris.jems.core.job.entity.JobInstanceWorkContext;
import com.ideas.tetris.jems.spring.aspect.jobstepcontext.beans.JobStepContextAwarePurgeService;
import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.springframework.batch.core.JobExecution;
import org.springframework.batch.core.StepExecution;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;

@Component
public class PurgeSasDatasetStep extends AbstractPurgeSasDatasetStep {

    @Autowired
	private JobStepContextAwarePurgeService purgeService;
    @Autowired
    private PacmanConfigParamsService pacmanConfigParamsService;
    @Autowired
	private DateService dateService;

    @Override
    public boolean isStepAsync(StepExecution stepExecution) {
        return true;
    }

    @Override
    public ShouldExecuteStepResult shouldExecuteStep(JobExecution jobExecution, JobInstanceWorkContext jobInstanceWorkContext) {
        if (pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.ENABLE_PURGE_SAS_OLD_DATASETS)) {
            return new ShouldExecuteStepResult(false, "Skipping this step because new purgeOldSasDatasetStep is enabled.");
        }
        return super.shouldExecuteStep(jobExecution,jobInstanceWorkContext);
    }

    @Override
    public Object doInvoke(StepExecution stepExecution,
                           JobInstanceWorkContext jobInstanceWorkContext) throws Exception {
        Date businessDate = dateService.getCaughtUpDate();
        purgeService.execute(businessDate);

        return null;
    }

}
