package com.ideas.tetris.jems.job.shelftransition.step;

import com.ideas.tetris.jems.core.job.entity.JobInstanceWorkContext;
import com.ideas.tetris.jems.core.step.tasklet.BusinessServiceStep;
import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.services.agilerates.configuration.service.ShelfStateTransitionJobService;
import com.ideas.tetris.pacman.services.agilerates.configuration.service.ShelfStateTransitionService;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.springframework.batch.core.JobExecution;
import org.springframework.batch.core.StepExecution;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class StartShelfStateTransitionJobStep extends BusinessServiceStep {
    @Autowired
    ShelfStateTransitionJobService shelfStateTransitionJobService;
    @Autowired
	private PacmanConfigParamsService pacmanConfigParamsService;
    @Autowired
	private ShelfStateTransitionService shelfStateTransitionService;

    @Override
    protected ShouldExecuteStepResult shouldExecuteStep(JobExecution jobExecution, JobInstanceWorkContext jobInstanceWorkContext) {
        if (!(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.USE_GENERIC_PRODUCT_FLOW))) {
            return new ShouldExecuteStepResult(false, "Product import toggle is not enabled");
        } else if (!hasValidConfiguration()) {
            return new ShouldExecuteStepResult(false, "Not having valid configuration or Date condition not met.");
        } else if (!isPropertyAttributesAssignmentCompleted()) {
            return new ShouldExecuteStepResult(false, "Shelf state transition skipped since Attribute assignment is missing.");
        }
        return ShouldExecuteStepResult.TRUE;
    }

    @Override
    public boolean isStepAsync(StepExecution stepExecution) {
        return false;
    }

    @Override
    public Object doInvoke(StepExecution stepExecution, JobInstanceWorkContext jobInstanceWorkContext) {
        shelfStateTransitionJobService.startShelfStateTransitionJob();
        return null;
    }

    private boolean hasValidConfiguration() {
        return shelfStateTransitionService.propertyHasValidConfiguration();
    }

    private boolean isPropertyAttributesAssignmentCompleted() {
        return shelfStateTransitionService.isAssignmentCompleted();
    }

}
