package com.ideas.tetris.jems.common.step.decision.tars;

import com.fasterxml.jackson.databind.ObjectMapper;

import com.ideas.tetris.jems.core.job.context.JobExecutionUtil;
import com.ideas.tetris.jems.core.step.context.StepExecutionContextKey;
import com.ideas.tetris.jems.core.step.context.StepExecutionUtil;
import com.ideas.tetris.jems.core.step.listener.AbstractStepExecutionListener;
import com.ideas.tetris.pacman.services.informationmanager.alert.service.AlertService;
import com.ideas.tetris.pacman.services.informationmanager.dto.AlertType;
import com.ideas.tetris.pacman.services.tars.TARSDecisionService;
import com.ideas.tetris.platform.common.entity.DecisionUploadType;
import com.ideas.tetris.platform.common.job.JobName;
import com.ideas.tetris.platform.common.job.JobParameterKey;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.batch.core.ExitStatus;
import org.springframework.batch.core.JobParameter;
import org.springframework.batch.core.StepExecution;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;

import static com.ideas.tetris.pacman.common.constants.Constants.BAR_DECISION_VALUE_RATEOFDAY;
import static com.ideas.tetris.pacman.common.constants.Constants.IS_DECISION_PRESENT;
import static com.ideas.tetris.pacman.common.constants.Constants.MIN_MAX_LOS;
import static com.ideas.tetris.pacman.common.constants.Constants.TARS_DECISION_XML_RATE_CODE_VALIDATION_MARKER;
import org.springframework.beans.factory.annotation.Autowired;

@Component
@Order(100)
public class TarsDecisionDeliveryListener extends AbstractStepExecutionListener {
    private static final Logger LOGGER = LoggerFactory.getLogger(TarsDecisionDeliveryListener.class);

    @Autowired
	private TARSDecisionService tarsDecisionService;

    @Autowired
	private AlertService alertService;

    @Override
    public ExitStatus afterStep(StepExecution stepExecution) {
        if (StepExecutionUtil.isCompleted(stepExecution)) {
            LOGGER.info("TarsDecisionDeliveryStep completed.");
            Boolean isDecisionPresent = JobExecutionUtil.getBooleanFromExecutionContext(stepExecution.getJobExecution(), IS_DECISION_PRESENT);
            if (isDecisionPresent) {
                LOGGER.info("Updating Decision Upload Type to Differential.");
                List<String> decisionList = new ArrayList<>(Arrays.asList(BAR_DECISION_VALUE_RATEOFDAY, MIN_MAX_LOS));
                JobParameter jobParameter_jobInvokedFrom = stepExecution.getJobExecution().getJobParameters().getParameters().get(JobParameterKey.JOB_INVOKED_FROM);
                if (null != jobParameter_jobInvokedFrom &&
                        ((String.valueOf(jobParameter_jobInvokedFrom.getValue())).equalsIgnoreCase(JobName.OperaManualUploadDecisionJob.name()))) {
                    decisionList.remove(MIN_MAX_LOS);
                }
                tarsDecisionService.setDecisionUploadType(DecisionUploadType.DIFFERENTIAL, decisionList);
            }
        } else if (StepExecutionUtil.isFailed(stepExecution)) {
            String response = stepExecution.getExecutionContext().get(StepExecutionContextKey.RESPONSE).toString();
            if (XMLValidationFailedForRateCodes(response)) {
                try {
                    HashMap<String, List<List<String>>> errors = buildErrorMap(response);
                    String errorDetails = tarsDecisionService.parseXMLValidationErrorsFor(TARS_DECISION_XML_RATE_CODE_VALIDATION_MARKER, errors);
                    alertService.createAlert(AlertType.DecisionUploadFailed.getName(), "decision.xml.validation.failed.description", errorDetails);
                } catch (IOException e) {
                    LOGGER.error("Decision XML validation: Could not create Decision Upload Failed Alert: error parsing NGI Response", e);
                }
            }
            stepExecution.getExecutionContext().put(StepExecutionContextKey.RESPONSE, stepExecution.getJobExecution().getExitStatus().getExitDescription());
        }
        return stepExecution.getExitStatus();
    }

    private HashMap<String, List<List<String>>> buildErrorMap(String response) throws IOException {
        HashMap<String, List<List<String>>> errors;
        ObjectMapper objectMapper = new ObjectMapper();
        errors = objectMapper.readValue(response, objectMapper.getTypeFactory().constructMapType(HashMap.class, String.class, List.class));
        return errors;
    }

    private boolean XMLValidationFailedForRateCodes(String response) {
        return null != response && response.contains(TARS_DECISION_XML_RATE_CODE_VALIDATION_MARKER);
    }
}
