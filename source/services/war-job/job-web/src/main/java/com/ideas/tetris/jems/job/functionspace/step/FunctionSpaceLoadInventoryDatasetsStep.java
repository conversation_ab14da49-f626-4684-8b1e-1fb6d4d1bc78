package com.ideas.tetris.jems.job.functionspace.step;


import com.ideas.tetris.jems.core.job.entity.JobInstanceWorkContext;
import com.ideas.tetris.jems.core.step.tasklet.BusinessServiceStep;
import com.ideas.tetris.pacman.common.configparams.IPConfigParamName;
import com.ideas.tetris.pacman.services.groupforecastpopulation.GroupForecastPopulationMacroService;
import com.ideas.tetris.pacman.services.grouppricing.evaluation.service.GroupEvaluationService;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.springframework.batch.core.JobExecution;
import org.springframework.batch.core.StepExecution;
import org.springframework.stereotype.Component;

import static com.ideas.tetris.pacman.services.groupforecastpopulation.common.ExtendedGPEInventoryDatasetOperationType.POPULATION;
import org.springframework.beans.factory.annotation.Autowired;

@Component
public class FunctionSpaceLoadInventoryDatasetsStep extends BusinessServiceStep {

    @Autowired
	private PacmanConfigParamsService configParamService;

    @Autowired
	private GroupForecastPopulationMacroService groupForecastPopulationMacroService;

    @Autowired
	private GroupEvaluationService groupEvaluationService;

    @Override
    public boolean isStepAsync(StepExecution stepExecution) {
        return true;
    }

    @Override
    protected ShouldExecuteStepResult shouldExecuteStep(JobExecution jobExecution, JobInstanceWorkContext jobInstanceWorkContext) {
        if (!configParamService.getBooleanParameterValue(IPConfigParamName.GP_USE_EXTENDED_WND)) {
            return new ShouldExecuteStepResult(false,
                    "Skipping execution step since " + IPConfigParamName.GP_USE_EXTENDED_WND + " toggle is disabled");
        }

        if (!groupEvaluationService.isPropertyOneWayOrTwoWay()) {
            return new ShouldExecuteStepResult(false,
                    "Skipping execution step since stage is not at least one way");
        }

        return ShouldExecuteStepResult.TRUE;
    }

    @Override
    public Object doInvoke(StepExecution stepExecution, JobInstanceWorkContext jobInstanceWorkContext) throws Exception {
        groupForecastPopulationMacroService.cleanUpOrReloadInventoryData(POPULATION.getValue());
        return "SAS Datasets Population is Successful";
    }
}
