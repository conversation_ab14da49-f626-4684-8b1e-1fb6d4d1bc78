package com.ideas.tetris.jems.job.complimentarycrosschargerate.step;


import com.ideas.tetris.jems.core.job.context.JobExecutionUtil;
import com.ideas.tetris.jems.core.job.entity.JobInstanceWorkContext;
import com.ideas.tetris.jems.core.step.tasklet.BusinessServiceStep;
import com.ideas.tetris.pacman.services.complimentarycrosschargerate.ComplimentaryCrossChargeRateService;
import org.springframework.batch.core.JobExecution;
import org.springframework.batch.core.StepExecution;
import org.springframework.stereotype.Component;
import org.springframework.beans.factory.annotation.Autowired;

@Component
public class ComplimentaryCrossChargeRateRegisterSyncEventStep extends BusinessServiceStep {
    @Autowired
    ComplimentaryCrossChargeRateService complimentaryCrossChargeRateService;

    @Override
    public boolean isStepAsync(StepExecution stepExecution) {
        return false;
    }

    @Override
    public Object doInvoke(StepExecution stepExecution, JobInstanceWorkContext jobInstanceWorkContext) throws Exception {
        complimentaryCrossChargeRateService.registerSyncEventForComplimentaryCrossChargeRateUpload();
        return null;
    }

    @Override
    protected ShouldExecuteStepResult shouldExecuteStep(JobExecution jobExecution, JobInstanceWorkContext jobInstanceWorkContext) {
        if (!JobExecutionUtil.isStageAtLeastOneWay(jobInstanceWorkContext)) {
            return new ShouldExecuteStepResult(false, "Stage should be at least ONE_WAY");
        }
        return ShouldExecuteStepResult.TRUE;
    }

}
