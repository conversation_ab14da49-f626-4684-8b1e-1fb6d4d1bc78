package com.ideas.tetris.jems.core.job;

import com.ideas.tetris.jems.core.job.context.JobExecutionContextKey;
import com.ideas.tetris.jems.core.job.context.JobExecutionUtil;
import com.ideas.tetris.jems.core.job.entity.JobInstanceWorkContext;
import com.ideas.tetris.jems.core.repository.dao.JobInstanceWorkContextDao;
import com.ideas.tetris.jems.core.step.listener.EndLoopStepExecutionListener;
import com.ideas.tetris.jems.core.step.loop.AbstractLoopStep;
import com.ideas.tetris.jems.core.step.status.ExtendedExitStatus;
import com.ideas.tetris.jems.core.step.tasklet.AbstractTaskletStep;
import com.ideas.tetris.platform.common.errorhandling.ErrorCode;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.common.utils.systemconfig.SystemConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.batch.core.BatchStatus;
import org.springframework.batch.core.ExitStatus;
import org.springframework.batch.core.JobExecution;
import org.springframework.batch.core.JobInterruptedException;
import org.springframework.batch.core.StartLimitExceededException;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.StepExecution;
import org.springframework.batch.core.StepExecutionListener;
import org.springframework.batch.core.job.SimpleStepHandler;
import org.springframework.batch.core.listener.CompositeStepExecutionListener;
import org.springframework.batch.core.repository.JobRepository;
import org.springframework.batch.core.repository.JobRestartException;
import org.springframework.batch.core.step.tasklet.TaskletStep;
import org.springframework.retry.RetryContext;
import org.springframework.retry.RetryPolicy;
import org.springframework.retry.backoff.BackOffContext;
import org.springframework.retry.backoff.BackOffInterruptedException;
import org.springframework.retry.backoff.BackOffPolicy;
import org.springframework.util.ReflectionUtils;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

import static com.ideas.tetris.jems.core.step.util.LoopIndexManager.isLoopIndexSet;
import static com.ideas.tetris.jems.core.step.util.LoopIndexManager.resetLoopIndexToZero;

public class JemsStepHandler extends SimpleStepHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(JemsStepHandler.class);
    private final JobInstanceWorkContextDao jobInstanceWorkContextDao;
    private final Field stepExecutionListenerField;
    private final Field orderedCompositeField;
    private final Method reverseOrderedComposite;

    public JemsStepHandler(JobRepository jobRepository, JobInstanceWorkContextDao jobInstanceWorkContextDao) {
        super(jobRepository);
        this.jobInstanceWorkContextDao = jobInstanceWorkContextDao;

        this.stepExecutionListenerField = ReflectionUtils.findField(org.springframework.batch.core.step.AbstractStep.class, "stepExecutionListener");
        this.stepExecutionListenerField.setAccessible(true);
        this.orderedCompositeField = ReflectionUtils.findField(CompositeStepExecutionListener.class, "list");
        this.orderedCompositeField.setAccessible(true);
        this.reverseOrderedComposite = ReflectionUtils.findMethod(findOrderedCompositeClass(), "reverse");
        this.reverseOrderedComposite.setAccessible(true);
    }

    private Class<?> findOrderedCompositeClass() {
        try {
            return Class.forName("org.springframework.batch.core.listener.OrderedComposite");
        } catch (ClassNotFoundException cnfe) {
            throw new TetrisException(ErrorCode.UNEXPECTED_ERROR, "OrderedComposite class not available", cnfe);
        }
    }

    @Override
    public StepExecution handleStep(final Step step, final JobExecution execution) throws JobInterruptedException, JobRestartException {
        if (SystemConfig.isAutoRetryEnabled()) {
            return handleStepWithRetry(step, execution);
        } else {
            return doHandleStep(step, execution);
        }
    }

    @SuppressWarnings("squid:S3776")
    StepExecution handleStepWithRetry(final Step step, final JobExecution execution) throws JobInterruptedException, JobRestartException {
        StepExecution stepExecution = null;
        RetryPolicy retryPolicy = StepRetryFactory.buildRetryPolicy();
        BackOffPolicy backOffPolicy = StepRetryFactory.buildBackoffPolicy();

        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("Handling " + step.getName() + " with retries enabled");
        }

        // Allow the retry policy to initialise itself...
        RetryContext context = retryPolicy.open(null);

        try {
            // Get or Start the backoff context...
            BackOffContext backOffContext = backOffPolicy.start(context);
            if (backOffContext != null) {
                context.setAttribute("backOffContext", backOffContext);
            }

            /*
             * We allow the whole loop to be skipped if the policy or context already
             * forbid the first try. This is used in the case of external retry to allow a
             * recovery in handleRetryExhausted without the callback processing (which
             * would throw an exception).
             */
            boolean retryAborted = false;
            List<String> exceptions = new ArrayList<>();
            while (retryPolicy.canRetry(context) && !context.isExhaustedOnly() && !retryAborted) {

                // execute the step, if there are no exceptions exit the retry loop
                stepExecution = doHandleStep(step, execution);
                if (stepExecution == null) {
                    return null;
                }

                if (stepExecution.getFailureExceptions().isEmpty()) {
                    if (context.getRetryCount() > 0) {
                        LOGGER.info(step.getName() + " successfully completed after " + context.getRetryCount() + " retries being recovered after: " + String.join(",", exceptions));
                    }
                    break;
                }

                Throwable e = stepExecution.getFailureExceptions().get(0);
                exceptions.add(e.getClass().getSimpleName());
                LOGGER.warn("Encountered exception of type " + e.getClass().getSimpleName() + " on step " + step.getName() + "  Retry count:" + context.getRetryCount());

                try {
                    retryPolicy.registerThrowable(context, e);
                } catch (Exception ex) {
                    LOGGER.warn("Could not register throwable.  Exiting retry loop", ex);
                    retryAborted = true;
                }

                if (retryPolicy.canRetry(context) && !context.isExhaustedOnly()) {
                    try {
                        backOffPolicy.backOff(backOffContext);
                    } catch (BackOffInterruptedException ex) {
                        LOGGER.warn("Backoff interrupted prematurely for step " + step.getName(), ex);
                        retryAborted = true;
                    }
                }
            }

            return stepExecution;
        } finally {
            retryPolicy.close(context);
        }
    }

    StepExecution doHandleStep(Step step, JobExecution execution) throws JobInterruptedException, JobRestartException {
        // check if we should skip this step because are in a loop and were restarted after an async step
        if (JobExecutionUtil.shouldSkipLoopStep(execution, step.getName()) && SystemConfig.isStepExecutionSaveSkippedAfterRestartOfAsyncStepEnabled()) {
            LOGGER.info("Step: " + step.getName() + " was skipped because flow was restarted after an async step");
            return null;
        }

        // Check the JobExecutionContext for the JobInstanceWorkContext, use it if it's there - otherwise query the DB
        JobInstanceWorkContext jobInstanceWorkContext = (JobInstanceWorkContext) JobExecutionUtil.getFromExecutionContext(execution, JobExecutionContextKey.JOB_INSTANCE_WORK_CONTEXT);
        if (jobInstanceWorkContext == null) {
            // Get it from the DB since it isn't set
            jobInstanceWorkContext = jobInstanceWorkContextDao.getJobInstanceWorkContext(JobExecutionUtil.getJobInstanceId(execution));
            if (jobInstanceWorkContext != null) {
                JobExecutionUtil.setOnExecutionContext(execution, JobExecutionContextKey.JOB_INSTANCE_WORK_CONTEXT, jobInstanceWorkContext);
            }
        }

        return super.handleStep(step, execution);
    }

    @SuppressWarnings("squid:RedundantThrowsDeclarationCheck")
    @Override
    protected boolean shouldStart(StepExecution lastStepExecution, JobExecution jobExecution, Step step) throws JobRestartException, StartLimitExceededException {
        boolean shouldStart = super.shouldStart(lastStepExecution, jobExecution, step);

        if (shouldStart && isAbstractTaskletStep(step) && SystemConfig.isShouldExecuteStepBeforeStepExecutionEnabled()) {

            // Check the JobExecutionContext for the JobInstanceWorkContext, it should be there as the doHandleStep put it there
            JobInstanceWorkContext jobInstanceWorkContext = (JobInstanceWorkContext) JobExecutionUtil.getFromExecutionContext(jobExecution, JobExecutionContextKey.JOB_INSTANCE_WORK_CONTEXT);

            // Check to see if the step should be skipped
            AbstractTaskletStep abstractTaskletStep = (AbstractTaskletStep) ((TaskletStep) step).getTasklet();

            // If the step is an AbstractLoopStep, the shouldExecute(...) may be dependent on the beforeStep(...) logic initializing the loop index
            if (abstractTaskletStep instanceof AbstractLoopStep && !isLoopIndexSet(jobExecution)) {
                resetLoopIndexToZero(jobExecution);
            }

            // Check to see if we will receive a noop exit status
            ExitStatus exitStatus = abstractTaskletStep.determineNoopExitStatus(jobExecution, step.getName(), jobInstanceWorkContext);
            if (exitStatus != null && (ExitStatus.NOOP.getExitCode().equals(exitStatus.getExitCode()) || ExtendedExitStatus.NOOP_LOOP.equals(exitStatus.getExitCode()))) {
                LOGGER.info("Step: " + step.getName() + " was skipped due to: " + exitStatus.getExitDescription());

                // For the flow to continue, you need to update this object in memory.  It won't be saved with these values
                if (lastStepExecution != null) {
                    lastStepExecution.setStatus(BatchStatus.COMPLETED);
                    lastStepExecution.setExitStatus(exitStatus);
                }

                // Create a StepExecution record to keep track of the skipped status
                StepExecution stepExecution = jobExecution.createStepExecution(step.getName());
                stepExecution.setStatus(BatchStatus.COMPLETED);
                stepExecution.setExitStatus(exitStatus);
                stepExecution.setEndTime(new Date());
                getJobRepository().add(stepExecution);

                if (SystemConfig.isAfterStepForOnlyLastStepInLoopEnabled()) {
                    afterStepForOnlyLastStepInLoop(jobExecution, step, stepExecution);
                } else {
                    afterStepForAllSkippedSteps(jobExecution, step, stepExecution);
                }
                return false;
            }
        }

        return shouldStart;
    }

    private void afterStepForOnlyLastStepInLoop(JobExecution jobExecution, Step step, StepExecution stepExecution) {
        // If it's a loop step, we may need to execute the listeners if on the last step within the loop
        if (JobExecutionUtil.inLoop(jobExecution)) {

            boolean isLastStepInLoop = false;

            // The try/catch logic matches how a step gets executed in the AbstractStep.execute(...) method
            try {
                CompositeStepExecutionListener compositeStepExecutionListener = (CompositeStepExecutionListener) ReflectionUtils.getField(stepExecutionListenerField, step);

                // Check if this is the last step in a loop
                isLastStepInLoop = isLastStepInLoop(compositeStepExecutionListener);

                // Execute the afterStep(...) logic if it is the last step in the loop
                if (isLastStepInLoop) {
                    compositeStepExecutionListener.afterStep(stepExecution);
                }
            } catch (Exception e) {
                LOGGER.error(String.format("Exception in afterStep callback in step %s in job %s", step.getName(), stepExecution.getJobExecution().getJobInstance().getJobName()), e);
            }

            // Update the contexts if this is the last step in the loop
            if (isLastStepInLoop) {
                // Insert/update the contexts as they might have changed
                getJobRepository().updateExecutionContext(stepExecution);
                getJobRepository().updateExecutionContext(jobExecution);
            }
        }
    }

    private void afterStepForAllSkippedSteps(JobExecution jobExecution, Step step, StepExecution stepExecution) {
        // Execute the afterStep(...) logic - the try/catch logic matches how a step gets executed in the AbstractStep.execute(...) method
        try {
            CompositeStepExecutionListener compositeStepExecutionListener = (CompositeStepExecutionListener) ReflectionUtils.getField(stepExecutionListenerField, step);
            compositeStepExecutionListener.afterStep(stepExecution);
        } catch (Exception e) {
            LOGGER.error(String.format("Exception in afterStep callback in step %s in job %s", step.getName(), stepExecution.getJobExecution().getJobInstance().getJobName()), e);
        }

        // Insert/update the contexts as they might have changed
        getJobRepository().updateExecutionContext(stepExecution);
        getJobRepository().updateExecutionContext(jobExecution);
    }

    private boolean isLastStepInLoop(CompositeStepExecutionListener compositeStepExecutionListener) throws Exception {
        Iterator listeners = (Iterator) reverseOrderedComposite.invoke(orderedCompositeField.get(compositeStepExecutionListener));
        while (listeners.hasNext()) {
            StepExecutionListener listener = (StepExecutionListener) listeners.next();
            if (listener instanceof EndLoopStepExecutionListener) {
                return true;
            }
        }

        return false;
    }

    private boolean isAbstractTaskletStep(Step step) {
        return step instanceof TaskletStep && ((TaskletStep) step).getTasklet() instanceof AbstractTaskletStep;
    }
}
