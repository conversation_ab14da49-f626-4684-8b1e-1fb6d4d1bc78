package com.ideas.tetris.jems.job.sasdatasetencoding.step;

import com.ideas.tetris.jems.core.job.entity.JobInstanceWorkContext;
import com.ideas.tetris.jems.core.step.tasklet.BusinessServiceStep;
import com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName;
import com.ideas.tetris.pacman.services.sasencoding.service.SasDatasetEncodingService;
import com.ideas.tetris.pacman.services.sasencoding.service.SasEncodingStatus;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.springframework.batch.core.JobExecution;
import org.springframework.batch.core.StepExecution;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


@Component
public class StartSasDatasetEncodingJobStep extends BusinessServiceStep {

    @Autowired
    private PacmanConfigParamsService configParamsService;
    @Autowired
    private SasDatasetEncodingService sasDatasetEncodeService;

    @Override
    public ShouldExecuteStepResult shouldExecuteStep(JobExecution jobExecution, JobInstanceWorkContext jobInstanceWorkContext) {
        SasEncodingStatus status = SasEncodingStatus.fromString(configParamsService.getParameterValue(PreProductionConfigParamName.SAS_ENCODING_STATUS));
        if (status == SasEncodingStatus.MIGRATE_W_TO_L) {
            return new ShouldExecuteStepResult(true, "Starting Dataset Migration from Windows to Linux");
        }
        return new ShouldExecuteStepResult(false, "Toggle value is not set to MigrateWToL");
    }

    @Override
    public boolean isStepAsync(StepExecution stepExecution) {
        return false;
    }

    @Override
    public Object doInvoke(StepExecution stepExecution, JobInstanceWorkContext jobInstanceWorkContext) throws Exception {
        sasDatasetEncodeService.startSasDatasetEncodingJob();
        return "Triggered SAS Dataset Encoding Job";
    }
}
