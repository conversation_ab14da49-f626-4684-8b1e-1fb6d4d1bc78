package com.ideas.tetris.jems.job.mktsegrecoding.step;


import com.ideas.tetris.jems.core.job.entity.JobInstanceWorkContext;
import com.ideas.tetris.jems.core.step.tasklet.BusinessServiceStep;
import com.ideas.tetris.pacman.services.mktsegrecoding.service.MktSegRecodingService;
import com.ideas.tetris.platform.services.Stage;
import org.springframework.batch.core.StepExecution;
import org.springframework.stereotype.Component;
import org.springframework.beans.factory.annotation.Autowired;

@Component
public class UpdatePropertyStageToDormantStep extends BusinessServiceStep {
    @Autowired
	private MktSegRecodingService mktSegRecodingService;

    @Override
    public boolean isStepAsync(StepExecution stepExecution) {
        return false;
    }

    @Override
    public Object doInvoke(StepExecution stepExecution, JobInstanceWorkContext jobInstanceWorkContext) throws Exception {
        mktSegRecodingService.changePropertyStageToDormant(jobInstanceWorkContext.getPropertyId());
        jobInstanceWorkContext.setPropertyStage(Stage.DORMANT.getCode());
        return "Property stage changed to DORMANT";
    }
}
