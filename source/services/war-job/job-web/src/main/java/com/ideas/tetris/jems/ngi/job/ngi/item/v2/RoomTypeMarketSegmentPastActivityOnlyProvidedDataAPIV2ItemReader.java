package com.ideas.tetris.jems.ngi.job.ngi.item.v2;

import com.ideas.api.client.activityRoomTypeMarketSegment.RoomTypeMarketSegmentActivityV2Api;
import com.ideas.api.client.activityRoomTypeMarketSegment.model.Link;
import com.ideas.api.client.activityRoomTypeMarketSegment.model.ResourceOfRoomTypeMarketSegmentActivity;
import com.ideas.api.client.activityRoomTypeMarketSegment.model.SlicedResourcesOfResourceOfRoomTypeMarketSegmentActivity;
import com.ideas.tetris.jems.core.step.context.StepExecutionContextKey;
import com.ideas.tetris.jems.core.step.context.StepExecutionUtil;
import com.ideas.tetris.jems.ngi.job.ngi.item.ClientPropertyCorrelationAwareItemReader;
import com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.platform.common.ngi.data.ActivityDataType;
import com.ideas.tetris.platform.common.rest.mapper.RestEndpoint;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static org.apache.commons.lang.StringUtils.isNotEmpty;

@Component
@StepScope
public class RoomTypeMarketSegmentPastActivityOnlyProvidedDataAPIV2ItemReader extends ClientPropertyCorrelationAwareItemReader<ResourceOfRoomTypeMarketSegmentActivity> {

    public static final String BASE_CURRENCY_CODE = "baseCurrencyCode";
    public static final String YIELD_CURRENCY_CODE = "yieldCurrencyCode";
    public static final String CLIENT_CODE = "clientCode";
    public static final String PROPERTY_CODE = "propertyCode";
    public static final String CORRELATION_ID = "correlationId";
    public static final String PSEUDO_ROOMS = "pseudoRooms";
    private Integer currentPage = 0;
    private Integer totalElementsFetched = 0;
    private Iterator<Map<String, ResourceOfRoomTypeMarketSegmentActivity>> currentActivityIterator = null;

    @Override
    protected ActivityDataType getActivityDataType() {
        return ActivityDataType.PAST_ROOM_TYPE_MARKET_SEGMENT;
    }

    @Override
    protected RestEndpoint getEndpoint() {
        return null;
    }

    @Override
    protected Map<String, Object> parameters() {
        Map<String, Object> parameters = super.parameters();
        if (getBooleanParameterValue(PreProductionConfigParamName.NGI_PSEUDO_ROOM_CLEAN_UP_ENABLED)) {
            parameters.put(PSEUDO_ROOMS, getPseudoRoomTypes());
        }
        return parameters;
    }

    @Override
    public Map<String, ResourceOfRoomTypeMarketSegmentActivity> doRead() {
        if (currentActivityIteratorHasMoreData()) {
            return currentActivityIterator.next();
        }

        setCurrentPage();
        if (currentPage == null) {
            return null;
        }

        return roomTypeMarketSegmentPastActivity();
    }

    private boolean currentActivityIteratorHasMoreData() {
        return currentActivityIterator != null && currentActivityIterator.hasNext();
    }

    private void setCurrentPage() {
        if (currentPage > 0) {
            currentPage = (Integer) StepExecutionUtil.getFromExecutionContext(stepExecution, StepExecutionContextKey.NGI_CURRENT_PAGE);
        } else if (currentPage < 0) {
            currentPage = null;
        }
    }

    private Map<String, ResourceOfRoomTypeMarketSegmentActivity> roomTypeMarketSegmentPastActivity() {
        final int pageSize = getPageSize();
        SlicedResourcesOfResourceOfRoomTypeMarketSegmentActivity roomTypeMarketSegmentPastActivities = getRoomTypeMarketSegmentPastActivities(pageSize, new ArrayList<>());

        if (CollectionUtils.isEmpty(roomTypeMarketSegmentPastActivities.getContent())) {
            StepExecutionUtil.setOnExecutionContext(stepExecution, StepExecutionContextKey.NGI_TOTAL_ELEMENTS, totalElementsFetched);
            return null;
        }
        currentActivityIterator = convert(roomTypeMarketSegmentPastActivities.getContent());
        totalElementsFetched = totalElementsFetched + roomTypeMarketSegmentPastActivities.getContent().size();
        StepExecutionUtil.setOnExecutionContext(stepExecution, StepExecutionContextKey.NGI_TOTAL_ELEMENTS, totalElementsFetched);
        setNextPageInContext(roomTypeMarketSegmentPastActivities);
        return currentActivityIterator.next();
    }

    private SlicedResourcesOfResourceOfRoomTypeMarketSegmentActivity getRoomTypeMarketSegmentPastActivities(int pageSize, List<String> sortBy) {
        RoomTypeMarketSegmentActivityV2Api apiInstance = new RoomTypeMarketSegmentActivityV2Api(ngiRestClient.getClientApi());
        final Map<String, Object> parameters = parameters();
        ngiRestClient.updateParametersBeforeNGICall(parameters);
        final Set<String> pseudoRooms = pseudoRoomCleanupEnabled() ? new HashSet<>(Arrays.asList(StringUtils.split(String.valueOf(parameters.get(PSEUDO_ROOMS)), Constants.COMMA))) : null;

        StepExecutionUtil.setOnExecutionContext(stepExecution, StepExecutionContextKey.NGI_CURRENT_PAGE, currentPage);
        StepExecutionUtil.setOnExecutionContext(stepExecution, StepExecutionContextKey.NGI_PAGE_SIZE, pageSize);
        StepExecutionUtil.setOnExecutionContext(stepExecution, StepExecutionContextKey.PSEUDO_ROOMS, pseudoRooms);
        StepExecutionUtil.setOnExecutionContext(stepExecution, StepExecutionContextKey.CORRELATION_ID, String.valueOf(parameters.get(CORRELATION_ID)));
        if (convertCurrency()) {
            return getProvidedCurrencyConvertedActivities(pageSize, sortBy, apiInstance, parameters, pseudoRooms);
        }
        return getProvidedActivities(pageSize, sortBy, apiInstance, parameters, pseudoRooms);
    }

    private SlicedResourcesOfResourceOfRoomTypeMarketSegmentActivity getProvidedActivities(int pageSize, List<String> sortBy, RoomTypeMarketSegmentActivityV2Api apiInstance, Map<String, Object> parameters, Set<String> pseudoRooms) {
        StepExecutionUtil.setOnExecutionContext(stepExecution, StepExecutionContextKey.NGI_REST_API_METHOD, "getProvidedActivities");
        return apiInstance.getProvidedActivities(
                String.valueOf(parameters.get(CLIENT_CODE)),
                String.valueOf(parameters.get(PROPERTY_CODE)),
                String.valueOf(parameters.get(CORRELATION_ID)),
                pseudoRooms,
                currentPage,
                pageSize,
                sortBy
        );
    }

    private SlicedResourcesOfResourceOfRoomTypeMarketSegmentActivity getProvidedCurrencyConvertedActivities(int pageSize, List<String> sortBy, RoomTypeMarketSegmentActivityV2Api apiInstance, Map<String, Object> parameters, Set<String> pseudoRooms) {
        StepExecutionUtil.setOnExecutionContext(stepExecution, StepExecutionContextKey.NGI_REST_API_METHOD, "getProvidedActivitiesWithCurrencyConversion");
        StepExecutionUtil.setOnExecutionContext(stepExecution, StepExecutionContextKey.BASE_CURRENCY_CODE, String.valueOf(parameters().get(BASE_CURRENCY_CODE)));
        StepExecutionUtil.setOnExecutionContext(stepExecution, StepExecutionContextKey.YIELD_CURRENCY_CODE, String.valueOf(parameters().get(YIELD_CURRENCY_CODE)));
        return apiInstance.getProvidedActivitiesWithCurrencyConversion(
                String.valueOf(parameters.get(CLIENT_CODE)),
                String.valueOf(parameters.get(PROPERTY_CODE)),
                String.valueOf(parameters.get(CORRELATION_ID)),
                String.valueOf(parameters.get(BASE_CURRENCY_CODE)),
                String.valueOf(parameters.get(YIELD_CURRENCY_CODE)),
                pseudoRooms,
                currentPage,
                pageSize,
                sortBy
        );
    }

    private boolean pseudoRoomCleanupEnabled() {
        return getBooleanParameterValue(PreProductionConfigParamName.NGI_PSEUDO_ROOM_CLEAN_UP_ENABLED);
    }

    private boolean convertCurrency() {
        return isNotEmpty(String.valueOf(parameters().get(BASE_CURRENCY_CODE))) && isNotEmpty(String.valueOf(parameters().get(YIELD_CURRENCY_CODE)));
    }

    private void setNextPageInContext(final SlicedResourcesOfResourceOfRoomTypeMarketSegmentActivity slicedResourcesOfResourceOfRoomTypeMarketSegmentActivity) {
        if (responseHasNextLink(slicedResourcesOfResourceOfRoomTypeMarketSegmentActivity.getLinks())) {
            StepExecutionUtil.setOnExecutionContext(stepExecution, StepExecutionContextKey.NGI_CURRENT_PAGE, ++currentPage);
        } else {
            StepExecutionUtil.removeFromExecutionContext(stepExecution, StepExecutionContextKey.NGI_CURRENT_PAGE);
            currentPage = -1;
        }
    }

    private boolean responseHasNextLink(List<Link> links) {
        for (Link link : links) {
            if (StringUtils.equalsIgnoreCase(org.springframework.hateoas.Link.REL_NEXT.value(), link.getRel())) {
                return true;
            }
        }
        return false;
    }
}
