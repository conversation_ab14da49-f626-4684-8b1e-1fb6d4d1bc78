package com.ideas.tetris.jems.common.step.componentroom;


import com.ideas.tetris.jems.core.step.context.StepExecutionUtil;
import com.ideas.tetris.jems.core.step.item.ChunkOrientedItemWriter;
import com.ideas.tetris.pacman.services.componentrooms.services.ComponentRoomService;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.StepExecution;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.ideas.tetris.jems.core.step.context.StepExecutionContextKey.CHUNKS_PROCESSED;

@Component
@StepScope
@Slf4j
class ComponentRoomSplitProfitItemWriter extends ChunkOrientedItemWriter<String> {
    @Autowired
	protected ComponentRoomService componentRoomService;
    @TenantCrudServiceBean.Qualifier

    private Date startDate;
    private Date endDate;
    private boolean isBde;

    @Override
    public void doWrite(StepExecution stepExecution, List<? extends String> tableNames) {
        String tableName = tableNames.get(0);
        updateProfitForTable(tableName);
        Integer chunksProcessed = (Integer) StepExecutionUtil.getFromExecutionContext(stepExecution, CHUNKS_PROCESSED);
        StepExecutionUtil.setOnExecutionContext(stepExecution, CHUNKS_PROCESSED, chunksProcessed + 1);
    }

    private void updateProfitForTable(String tableName) {
        Map<String, Object> params = new HashMap<>();
        params.put("startDate",startDate);
        params.put("endDate",endDate);
        switch (tableName.toUpperCase()){
            case "ACCOM_ACTIVITY":
            case "LAST_OPTIMIZATION_ACCOM_ACTIVITY":
                componentRoomService.updateProfitForAccomActivity(tableName,params);
                break;
            case "MKT_ACCOM_ACTIVITY":
            case "LAST_OPTIMIZATION_MKT_ACCOM_ACTIVITY":
                componentRoomService.updateProfitForMktAccomActivity(tableName,params);
                break;
            case "PACE_ACCOM_ACTIVITY":
                if(isBde){
                    componentRoomService.updateProfitForPaceAccomActivity(params);
                }
                break;
            default : break;
        }
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public void setIsBde(boolean isBde) {
        this.isBde = isBde;
    }
}