package com.ideas.tetris.jems.job.linkedsrp.step;


import com.ideas.tetris.jems.core.step.item.ListBasedChunkOrientedItemReader;
import com.ideas.tetris.pacman.services.qualifiedrate.repository.RateQualifiedFixedDetailsStageRepository;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.stereotype.Component;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;

@Component
@StepScope
public class RateQualifiedFixedDetailsStageReader extends ListBasedChunkOrientedItemReader<Integer> {

    @Autowired
    RateQualifiedFixedDetailsStageRepository repository;

    @Override
    protected List<Integer> getList() {
        return repository.findDistinctRateQualifiedIds();
    }
}
