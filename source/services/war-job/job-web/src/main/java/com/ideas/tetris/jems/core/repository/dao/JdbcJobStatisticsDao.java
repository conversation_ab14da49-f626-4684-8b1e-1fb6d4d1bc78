package com.ideas.tetris.jems.core.repository.dao;

import com.ideas.tetris.jems.core.job.service.JobDefinitionService;
import com.ideas.tetris.pacman.services.job.entity.ExecutionStatus;
import com.ideas.tetris.platform.common.job.HourlyJobProcessCounts;
import com.ideas.tetris.platform.common.job.JobDuration;
import com.ideas.tetris.platform.common.job.JobName;
import com.ideas.tetris.platform.common.job.StepDuration;
import com.ideas.tetris.platform.common.job.StepGroup;
import com.ideas.tetris.platform.common.xstream.SerializationUtil;
import com.ideas.tetris.platform.services.Stage;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.joda.time.Duration;
import org.joda.time.LocalDate;
import org.springframework.batch.core.StepExecution;
import org.springframework.batch.core.repository.dao.AbstractJdbcBatchMetadataDao;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class JdbcJobStatisticsDao extends AbstractJdbcBatchMetadataDao implements JobStatisticsDao {

    private static final String COMPLETED_JOBS_BY_STAGE_AND_DATE =
            "SELECT JOB_INSTANCE_ID FROM %PREFIX%JOB_VIEW " +
                    "WHERE JOB_NAME = :jobName AND STATUS = '" + ExecutionStatus.COMPLETED + "' AND " +
                    "PROPERTY_STAGE = :stage AND PROBLEM_COUNT = 0 AND START_TIME >= :startDate AND START_TIME <= :endDate";

    private static final String GET_TOTAL_DURATION_FOR_COMPLETED_JOBS_BY_STAGE_AND_DATE =
            "select COUNT(JOB_INSTANCE_ID) as JOB_COUNT, SUM(DURATION) as TOTAL_DURATION, AVG(DURATION) as AVG_DURATION," +
                    "MIN(DURATION) as MIN_DURATION, MAX(DURATION) as MAX_DURATION, STDEV(DURATION) as DURATION_STDEV from " +
                    "(SELECT JOB_INSTANCE_ID, DURATION = DATEDIFF(s, START_TIME, END_TIME) FROM %PREFIX%JOB_VIEW " +
                    "WHERE JOB_NAME = :jobName AND STATUS = '" + ExecutionStatus.COMPLETED + "' AND " +
                    "PROPERTY_STAGE = :stage AND PROBLEM_COUNT = 0 AND START_TIME >= :startDate AND START_TIME <= :endDate) jobs";

    private static final String STEP_DURATIONS =
            "SELECT SE.STEP_EXECUTION_ID, SE.STEP_NAME, DURATION = DATEDIFF(s, SE.START_TIME, SE.END_TIME), SAS_DURATION = DATEDIFF(s, SSE.START_TIME, SSE.END_TIME) " +
                    "FROM %PREFIX%STEP_EXECUTION SE LEFT JOIN SAS_STEP_EXECUTION SSE ON SE.STEP_EXECUTION_ID = SSE.STEP_EXECUTION_ID " +
                    "WHERE SE.JOB_EXECUTION_ID IN ( SELECT JOB_EXECUTION_ID FROM %PREFIX%JOB_EXECUTION WHERE JOB_INSTANCE_ID IN (" + COMPLETED_JOBS_BY_STAGE_AND_DATE + ")) ";

    private static final String STEP_EXECUTION_TIME_AND_CONTEXT =
            "SELECT SE.STEP_EXECUTION_ID, SE.STEP_NAME, SE.START_TIME, SEC.SERIALIZED_CONTEXT " +
                    "FROM %PREFIX%STEP_EXECUTION SE INNER JOIN %PREFIX%STEP_EXECUTION_CONTEXT SEC ON SE.STEP_EXECUTION_ID = SEC.STEP_EXECUTION_ID " +
                    "WHERE SE.JOB_EXECUTION_ID IN ( SELECT JOB_EXECUTION_ID FROM %PREFIX%JOB_EXECUTION WHERE JOB_INSTANCE_ID IN (" + COMPLETED_JOBS_BY_STAGE_AND_DATE + ")) ";


    private static final String GET_TOTAL_STEP_DURATIONS_FOR_COMPLETED_JOB_BY_STAGE_AND_DATE =
            "SELECT STEP_NAME, COUNT(*) STEP_COUNT, " +
                    "	SUM(DURATION) as STEP_TOTAL_DURATION, SUM(SAS_DURATION) as SAS_TOTAL_DURATION, " +
                    "	AVG(DURATION) as STEP_AVG_DURATION, AVG(SAS_DURATION) as SAS_AVG_DURATION, " +
                    "	MIN(DURATION) as STEP_MIN_DURATION, MIN(SAS_DURATION) as SAS_MIN_DURATION, " +
                    "	MAX(DURATION) as STEP_MAX_DURATION, MAX(SAS_DURATION) as SAS_MAX_DURATION, " +
                    "	STDEV(DURATION) as STEP_DURATION_STDEV, STDEV(SAS_DURATION) as SAS_DURATION_STDEV " +
                    "FROM (" + STEP_DURATIONS + ") steps GROUP BY STEP_NAME";

    private static final String GET_HOURLY_PROCESS_COUNTS_FOR_JOB =
            "SELECT " +
                    "	CONVERT(DATE, js.end_time) as date, " +
                    "	DATEPART(HOUR, js.end_time) as hour, " +
                    "	COUNT(*) count " +
                    "FROM %PREFIX%JOB_STATE js " +
                    "INNER JOIN %PREFIX%JOB_INSTANCE_WORK_CONTEXT jiwc ON jiwc.JOB_INSTANCE_ID = js.JOB_INSTANCE_ID " +
                    "WHERE js.JOB_NAME = :jobName AND js.END_TIME BETWEEN :startDate AND DATEADD(day, 1, :endDate) AND js.execution_status='" + ExecutionStatus.COMPLETED + "' " +
                    "%DB_SERVER_WHERE_CLAUSE_REPLACEMENT_TOKEN% " +
                    "%SAS_SERVER_WHERE_CLAUSE_REPLACEMENT_TOKEN% " +
                    "GROUP BY CONVERT(DATE, js.end_time), DATEPART(HOUR, js.end_time)" +
                    "ORDER BY date, hour";

    private static final String GET_HOURLY_PROCESS_COUNTS_FOR_STEP =
            "SELECT " +
                    "	CONVERT(DATE, se.end_time) as date, " +
                    "	DATEPART(HOUR, se.end_time) as hour, " +
                    "	COUNT(distinct ji.JOB_INSTANCE_ID) count " +
                    "FROM " +
                    "	%PREFIX%JOB_INSTANCE ji " +
                    "	INNER JOIN %PREFIX%JOB_EXECUTION je ON je.JOB_INSTANCE_ID = ji.JOB_INSTANCE_ID " +
                    "	INNER JOIN %PREFIX%STEP_EXECUTION se ON se.JOB_EXECUTION_ID = je.JOB_EXECUTION_ID " +
                    "   INNER JOIN %PREFIX%JOB_INSTANCE_WORK_CONTEXT jiwc ON jiwc.JOB_INSTANCE_ID = ji.JOB_INSTANCE_ID " +
                    "WHERE " +
                    "	ji.JOB_NAME = :jobName " +
                    "   AND se.step_name IN (:stepNames) " +
                    "%DB_SERVER_WHERE_CLAUSE_REPLACEMENT_TOKEN% " +
                    "%SAS_SERVER_WHERE_CLAUSE_REPLACEMENT_TOKEN% " +
                    "	AND se.end_time BETWEEN :startDate AND DATEADD(day, 1, :endDate) " +
                    "   AND se.status = '" + ExecutionStatus.COMPLETED + "' " +
                    "GROUP BY CONVERT(DATE, se.end_time), DATEPART(HOUR, se.end_time) " +
                    "ORDER BY date, hour";

    private static final String DB_SERVER_WHERE_CLAUSE_TOKEN = "AND jiwc.DB_SERVER_NAME = :dbServerName ";
    private static final String SAS_SERVER_WHERE_CLAUSE_TOKEN = "AND jiwc.SAS_SERVER_NAME = :sasServerName ";


    private JobDefinitionService jobDefinitionService;

    public void setJobDefinitionService(JobDefinitionService jobDefinitionService) {
        this.jobDefinitionService = jobDefinitionService;
    }

    @Override
    public JobDuration getTotalDurationForCompletedJobsByStage(JobName jobName, Date startDate, Date endDate, Stage stage) {
        NamedParameterJdbcTemplate namedParameterJdbcTemplate = new NamedParameterJdbcTemplate(getJdbcTemplate());
        MapSqlParameterSource parameters = new MapSqlParameterSource()
                .addValue("jobName", jobName.name())
                .addValue("startDate", startDate)
                .addValue("endDate", endDate)
                .addValue("stage", stage.getCode());

        List<JobDuration> jobDurations = namedParameterJdbcTemplate.query(getQuery(GET_TOTAL_DURATION_FOR_COMPLETED_JOBS_BY_STAGE_AND_DATE), parameters, new JobDurationRowMapper(jobName));
        if (CollectionUtils.isNotEmpty(jobDurations)) {
            return jobDurations.get(0);
        }

        return null;
    }

    @Override
    public List<StepDuration> getTotalStepDurationsForCompletedJobsByStage(JobName jobName, Date startDate, Date endDate, Stage stage) {
        // not sure how this will work for jobs that have loop steps
        // at a minimum, would want to filter out those steps with an exit status like 'NOOP_LOOP%'
        NamedParameterJdbcTemplate namedParameterJdbcTemplate = new NamedParameterJdbcTemplate(getJdbcTemplate());
        MapSqlParameterSource parameters = new MapSqlParameterSource()
                .addValue("jobName", jobName.name())
                .addValue("startDate", startDate)
                .addValue("endDate", endDate)
                .addValue("stage", stage.getCode());

        return namedParameterJdbcTemplate.query(getQuery(GET_TOTAL_STEP_DURATIONS_FOR_COMPLETED_JOB_BY_STAGE_AND_DATE), parameters, new StepDurationRowMapper(jobName));
    }

    @Override
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public List<StepExecution> getStepExecutionStartTimeAndContext(JobName jobName, Date startDate, Date endDate, Stage stage) {
        // We need to get
        NamedParameterJdbcTemplate namedParameterJdbcTemplate = new NamedParameterJdbcTemplate(getJdbcTemplate());
        MapSqlParameterSource parameters = new MapSqlParameterSource()
                .addValue("jobName", jobName.name())
                .addValue("startDate", startDate)
                .addValue("endDate", endDate)
                .addValue("stage", stage.getCode());

        return namedParameterJdbcTemplate.query(getQuery(STEP_EXECUTION_TIME_AND_CONTEXT), parameters, new StepExecutionRowMapper());
    }

    @Override
    public List<HourlyJobProcessCounts> getHourlyJobProcessCounts(final JobName jobName, final StepGroup stepGroup, final String dbServerName, String sasServerName, LocalDate startDate, LocalDate endDate) {
        String query = GET_HOURLY_PROCESS_COUNTS_FOR_JOB;
        MapSqlParameterSource parameters = new MapSqlParameterSource()
                .addValue("jobName", jobName.name())
                .addValue("startDate", startDate.toDate())
                .addValue("endDate", endDate.toDate());

        // If we are looking for a set of stepNames, we need to use a different query and add the stepNames as parameters
        if (stepGroup != null && !StepGroup.OTHER.equals(stepGroup)) {
            query = GET_HOURLY_PROCESS_COUNTS_FOR_STEP;
            parameters.addValue("stepNames", jobDefinitionService.getStepsForStepGroup(stepGroup));
        }

        // Add DB_SERVER_NAME to the the selection criteria if present
        if (StringUtils.isNotBlank(dbServerName)) {
            parameters.addValue("dbServerName", dbServerName);
            query = query.replace("%DB_SERVER_WHERE_CLAUSE_REPLACEMENT_TOKEN%", DB_SERVER_WHERE_CLAUSE_TOKEN);
        } else {
            query = query.replace("%DB_SERVER_WHERE_CLAUSE_REPLACEMENT_TOKEN%", "");
        }

        // Add SAS_SERVER_NAME to the the selection criteria if present
        if (StringUtils.isNotBlank(sasServerName)) {
            parameters.addValue("sasServerName", sasServerName);
            query = query.replace("%SAS_SERVER_WHERE_CLAUSE_REPLACEMENT_TOKEN%", SAS_SERVER_WHERE_CLAUSE_TOKEN);
        } else {
            query = query.replace("%SAS_SERVER_WHERE_CLAUSE_REPLACEMENT_TOKEN%", "");
        }

        // Build an HourlyJobProcessCounts object for every day, so that each day has an object even if it returns all zeros.
        Map<LocalDate, HourlyJobProcessCounts> hourlyJobProcessCountsDateMap = buildEmptyHourlyJobProcessCountsForDates(jobName, stepGroup, startDate, endDate);

        // Execute the HourlyJobProcessCount query, the query returns a row per day per hour and the count which then needs to get rolled up by date.
        NamedParameterJdbcTemplate namedParameterJdbcTemplate = new NamedParameterJdbcTemplate(getJdbcTemplate());
        namedParameterJdbcTemplate.query(getQuery(query), parameters, getHourlyJobProcessCountRowMapper(jobName, stepGroup, hourlyJobProcessCountsDateMap));

        // Return a sorted List of HourlyJobProcessCounts - ascending order
        List<HourlyJobProcessCounts> hourlyJobProcessCounts = new ArrayList<HourlyJobProcessCounts>(hourlyJobProcessCountsDateMap.values());
        Collections.sort(hourlyJobProcessCounts);
        return hourlyJobProcessCounts;
    }

    private Map<LocalDate, HourlyJobProcessCounts> buildEmptyHourlyJobProcessCountsForDates(final JobName jobName, final StepGroup stepGroup, LocalDate startDate, LocalDate endDate) {
        // Create a map that has a key of the Date and a value of the HourlyJobProcessCounts so that it will return an HourlyJobProcessCount for each date
        // selected, even if the SQL call doesn't return one.
        Map<LocalDate, HourlyJobProcessCounts> hourlyJobProcessCountsDateMap = new HashMap<LocalDate, HourlyJobProcessCounts>();

        // Iterate over all of the days from the start to the end date.
        LocalDate currentDate = startDate;
        while (!currentDate.isAfter(endDate)) {
            hourlyJobProcessCountsDateMap.put(currentDate, new HourlyJobProcessCounts(jobName, stepGroup, currentDate));
            currentDate = currentDate.plusDays(1);
        }
        return hourlyJobProcessCountsDateMap;
    }

    private RowMapper<HourlyJobProcessCounts> getHourlyJobProcessCountRowMapper(final JobName jobName, final StepGroup stepGroup, final Map<LocalDate, HourlyJobProcessCounts> hourlyJobProcessCountsDateMap) {
        return new RowMapper<HourlyJobProcessCounts>() {
            @Override
            public HourlyJobProcessCounts mapRow(ResultSet rs, int rowNum) throws SQLException {
                // Get the date field to look up the HourlyJobProcessCounts
                LocalDate date = new LocalDate(rs.getDate("date"));

                // Look up the HourlyJobProcessCounts object
                HourlyJobProcessCounts hourlyJobProcessCounts = hourlyJobProcessCountsDateMap.computeIfAbsent(date, v -> new HourlyJobProcessCounts(jobName, stepGroup, date));

                // The hourly process counts list will always have a number per hour, so set the count
                // on the index for the hour.
                hourlyJobProcessCounts.getHourlyProcessCounts().set(rs.getInt("hour"), rs.getInt("count"));
                return hourlyJobProcessCounts;
            }
        };
    }

    private Duration buildDuration(ResultSet rs, String columnName) throws SQLException {
        return new Duration(rs.getLong(columnName) * 1000);
    }

    private class JobDurationRowMapper implements RowMapper<JobDuration> {
        private JobName jobName;

        public JobDurationRowMapper(JobName jobName) {
            this.jobName = jobName;
        }

        @Override
        public JobDuration mapRow(ResultSet rs, int rowNum) throws SQLException {
            JobDuration jobDuration = new JobDuration();
            jobDuration.setJobName(jobName);
            jobDuration.setCount(rs.getInt("JOB_COUNT"));
            jobDuration.setTotalDuration(buildDuration(rs, "TOTAL_DURATION"));
            jobDuration.setAverageDuration(buildDuration(rs, "AVG_DURATION"));
            jobDuration.setMinDuration(buildDuration(rs, "MIN_DURATION"));
            jobDuration.setMaxDuration(buildDuration(rs, "MAX_DURATION"));
            jobDuration.setDurationStandardDeviation(buildDuration(rs, "DURATION_STDEV"));
            return jobDuration;
        }
    }

    private class StepDurationRowMapper implements RowMapper<StepDuration> {
        private JobName jobName;

        public StepDurationRowMapper(JobName jobName) {
            this.jobName = jobName;
        }

        @Override
        public StepDuration mapRow(ResultSet rs, int rowNum) throws SQLException {
            StepDuration stepDuration = new StepDuration();
            stepDuration.setJobName(jobName);
            stepDuration.setStepName(rs.getString("STEP_NAME"));
            stepDuration.setCount(rs.getInt("STEP_COUNT"));
            stepDuration.setTotalStepDuration(buildDuration(rs, "STEP_TOTAL_DURATION"));
            stepDuration.setAverageStepDuration(buildDuration(rs, "STEP_AVG_DURATION"));
            stepDuration.setMinStepDuration(buildDuration(rs, "STEP_MIN_DURATION"));
            stepDuration.setMaxStepDuration(buildDuration(rs, "STEP_MAX_DURATION"));
            stepDuration.setStepDurationStandardDeviation(buildDuration(rs, "STEP_DURATION_STDEV"));
            stepDuration.setTotalSASDuration(buildDuration(rs, "SAS_TOTAL_DURATION"));
            stepDuration.setAverageSASDuration(buildDuration(rs, "SAS_AVG_DURATION"));
            stepDuration.setMinSASDuration(buildDuration(rs, "SAS_MIN_DURATION"));
            stepDuration.setMaxSASDuration(buildDuration(rs, "SAS_MAX_DURATION"));
            stepDuration.setSASDurationStandardDeviation(buildDuration(rs, "SAS_DURATION_STDEV"));
            return stepDuration;
        }
    }

    private class StepExecutionRowMapper implements RowMapper<StepExecution> {

        @Override
        public StepExecution mapRow(ResultSet rs, int rowNum) throws SQLException {
            StepExecution stepExecution = new StepExecution(rs.getString("STEP_NAME"), null);
            stepExecution.setId(rs.getLong("STEP_EXECUTION_ID"));
            stepExecution.setStartTime(rs.getTimestamp("START_TIME"));

            Map<String, Object> serializedContext = SerializationUtil.deserializeMap(rs.getString("SERIALIZED_CONTEXT"));
            stepExecution.setExecutionContext(new ExecutionContext(serializedContext));
            return stepExecution;
        }
    }
}
