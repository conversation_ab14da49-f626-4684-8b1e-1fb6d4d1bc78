package com.ideas.tetris.jems.common.step.property;


import com.ideas.tetris.jems.job.clinet.syncflag.dto.PropertySyncFlagResultDto;
import com.ideas.tetris.pacman.common.configparams.SyncConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.eventaggregator.SyncEvent;
import com.ideas.tetris.pacman.services.eventaggregator.SyncEventAggregatorService;
import com.ideas.tetris.pacman.services.syncflags.service.SyncFlagService;
import com.ideas.tetris.platform.common.regulator.RegulatorConstants;
import com.ideas.tetris.platform.services.regulator.service.RegulatorService;
import com.ideas.tetris.platform.services.regulator.service.spring.RegulatorSpringService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Set;

import static com.ideas.tetris.jems.job.clinet.syncflag.dto.UpdateSyncFlagErrorCode.PROPERTY_PROCESSING_UNDERWAY;
import static com.ideas.tetris.jems.job.clinet.syncflag.dto.UpdateSyncFlagErrorCode.SYNC_EVENT_NOT_FOUND;
import static com.ideas.tetris.jems.job.clinet.syncflag.dto.UpdateSyncFlagErrorCode.SYNC_FLAG_ALREADY_UPDATED;
import static com.ideas.tetris.jems.job.clinet.syncflag.dto.UpdateSyncFlagErrorCode.UPDATE_FAILED;

@Component
@StepScope
public class PerPropertySyncFlagItemProcessor implements ItemProcessor<Integer, PropertySyncFlagResultDto> {

    private SyncConfigParamName syncConfigParamName;

    private Boolean syncFlagValue;

    private String note;

    @Autowired
	private RegulatorService regulatorService;
    @Autowired
    private RegulatorSpringService regulatorSpringService;

    @Autowired
	private SyncFlagService syncFlagService;

    @Autowired
	private SyncEventAggregatorService syncEventAggregatorService;

    @Override
    public PropertySyncFlagResultDto process(final Integer propertyID) {
        final String propertyCode = PacmanWorkContextHelper.getPropertyCode();

        if (isSyncFlagAlreadyUpdated()) {
            return new PropertySyncFlagResultDto(propertyID, propertyCode, false).setErrorCode(SYNC_FLAG_ALREADY_UPDATED);
        }

        if (isPropertyProcessingUnderway(PacmanWorkContextHelper.getClientCode(), propertyCode)) {
            return new PropertySyncFlagResultDto(propertyID, propertyCode, false).setErrorCode(PROPERTY_PROCESSING_UNDERWAY);
        }

        final SyncEvent syncEvent = getSyncEvent(syncConfigParamName);

        if (syncEvent == null) {
            return new PropertySyncFlagResultDto(propertyID, propertyCode, false).setErrorCode(SYNC_EVENT_NOT_FOUND);
        }

        final boolean success = registerOrClearSyncFlag(syncEvent);

        final PropertySyncFlagResultDto propertySyncFlagResultDto = new PropertySyncFlagResultDto(propertyID, propertyCode, success);

        return success ? propertySyncFlagResultDto : propertySyncFlagResultDto.setErrorCode(UPDATE_FAILED);
    }

    private boolean isSyncFlagAlreadyUpdated() {
        final boolean isSyncEnabled = syncFlagService.isSyncEnabledFor(syncConfigParamName);
        return (isSyncEnabled && syncFlagValue) || (!isSyncEnabled && !syncFlagValue);
    }

    private boolean isPropertyProcessingUnderway(final String clientCode, final String propertyCode) {
        final String context = Constants.getRegulatorContext(clientCode, propertyCode);
        if (regulatorService.isSpringTXEnableRegulatorService(clientCode, propertyCode)) {
            Set<String> runningJobs = regulatorSpringService.getRunningServiceNamesForContext(context);
            return isApplicationUpgradeUnderway(runningJobs);
        } else {
            Set<String> runningJobs = regulatorService.getRunningServiceNamesForContext(context);
            return isApplicationUpgradeUnderway(runningJobs);
        }
    }

    private boolean isApplicationUpgradeUnderway(Set<String> runningJobs) {
        return CollectionUtils.containsAny(runningJobs, Collections.singleton(RegulatorConstants.APPLICATION_UPGRADE));
    }

    private boolean registerOrClearSyncFlag(final SyncEvent syncEvent) {
        if (syncFlagValue) {
            return syncEventAggregatorService.registerSyncEvent(syncEvent, note);
        }

        syncEventAggregatorService.clearSyncEvent(syncEvent, note);
        return true;
    }

    private SyncEvent getSyncEvent(final SyncConfigParamName syncConfigParamName) {
        for (final SyncEvent syncEvent : SyncEvent.values()) {
            if (syncConfigParamName == SyncConfigParamName.from(syncEvent.getGlobalParameter())) {
                return syncEvent;
            }
        }
        return null;
    }

    public void setSyncConfigParamName(final String syncConfigParamName) {
        this.syncConfigParamName = SyncConfigParamName.from(syncConfigParamName);
    }

    public void setSyncFlagValue(final Boolean syncFlagValue) {
        this.syncFlagValue = Boolean.TRUE.equals(syncFlagValue);
    }

    public void setNote(final String note) {
        this.note = note;
    }
}