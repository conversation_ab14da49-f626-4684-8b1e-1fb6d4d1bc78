package com.ideas.tetris.jems.job.pmsmigration.step;

import com.ideas.tetris.jems.core.step.item.ChunkOrientedItemReader;
import com.ideas.tetris.jems.core.step.tasklet.ChunkOrientedBusinessServiceStep;
import com.ideas.tetris.jems.job.pmsmigration.chunk.reader.TotalActivityComparisonChunkReader;
import com.ideas.tetris.jems.job.pmsmigration.chunk.writer.TotalActivityComparisonChunkWriter;
import org.joda.time.LocalDate;
import org.springframework.batch.core.StepExecution;
import org.springframework.batch.item.ItemWriter;
import org.springframework.stereotype.Component;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

@Component
public class TotalActivityDataComparisonStep extends ChunkOrientedBusinessServiceStep<LocalDate, LocalDate> {
    @TotalActivityComparisonChunkReader.Qualifier
    @Autowired
	@Qualifier("totalActivityComparisonChunkReader")
	private ChunkOrientedItemReader<LocalDate> totalActivityComparisonChunkReader;

    @Autowired
    @TotalActivityComparisonChunkWriter.Qualifier
    private ItemWriter<LocalDate> totalActivityComparisonChunkWriter;

    @Override
    public boolean isStepAsync(StepExecution stepExecution) {
        return false;
    }

    @Override
    protected ChunkOrientedItemReader<LocalDate> itemReader() {
        return totalActivityComparisonChunkReader;
    }

    @Override
    protected ItemWriter<LocalDate> itemWriter() {
        return totalActivityComparisonChunkWriter;
    }

    @Override
    protected int chunkSize() {
        return Integer.parseInt(System.getProperty(getBeanName() + ".totalActivityComparisonChunkSize", "100"));
    }
}
