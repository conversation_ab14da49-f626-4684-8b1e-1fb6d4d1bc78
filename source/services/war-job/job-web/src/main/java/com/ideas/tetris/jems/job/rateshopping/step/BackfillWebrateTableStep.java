package com.ideas.tetris.jems.job.rateshopping.step;


import com.ideas.tetris.jems.core.job.context.JobExecutionUtil;
import com.ideas.tetris.jems.core.job.entity.JobInstanceWorkContext;
import com.ideas.tetris.jems.core.step.context.StepExecutionUtil;
import com.ideas.tetris.jems.core.step.item.ChunkOrientedItemReader;
import com.ideas.tetris.jems.core.step.tasklet.ChunkOrientedBusinessServiceStep;
import com.ideas.tetris.pacman.common.configparams.GUIConfigParamName;
import com.ideas.tetris.pacman.common.utils.pojo.Pair;
import com.ideas.tetris.platform.common.job.JobParameterKey;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.joda.time.LocalDate;
import org.springframework.batch.core.ExitStatus;
import org.springframework.batch.core.JobExecution;
import org.springframework.batch.core.StepExecution;
import org.springframework.batch.core.StepExecutionListener;
import org.springframework.batch.item.ItemWriter;
import org.springframework.stereotype.Component;

import javax.inject.Inject;

import static com.ideas.tetris.jems.core.job.context.JobExecutionContextKey.ADJUST_ORIGINAL_WEBRATE_VALUE_WITH_ROOM_REVENUE_TAX;
import static com.ideas.tetris.jems.core.job.context.JobExecutionContextKey.CONTINUOUS_PRICING;
import static com.ideas.tetris.jems.core.step.context.StepExecutionContextKey.CHUNKS_PROCESSED;
import static com.ideas.tetris.jems.core.step.context.StepExecutionContextKey.CHUNK_SIZE;
import static com.ideas.tetris.jems.core.step.context.StepExecutionContextKey.IS_WEBRATE;
import static com.ideas.tetris.jems.core.step.context.StepExecutionContextKey.TOTAL_CHUNKS;
import org.springframework.beans.factory.annotation.Autowired;

@Component
public class BackfillWebrateTableStep extends ChunkOrientedBusinessServiceStep<Pair<LocalDate, LocalDate>, Pair<LocalDate, LocalDate>> implements StepExecutionListener {
    private static int WEBRATE_DEFAULT_CHUNK_SIZE = 30;

    @Autowired
	private BackfillWebrateRatevalueDisplayChunkOrientedItemReader itemReader;
    @Autowired
	private BackfillWebrateRatevalueDisplayChunkOrientedItemWriter itemWriter;
    @Autowired
	private PacmanConfigParamsService configParamsService;

    @Override
    protected ChunkOrientedItemReader<Pair<LocalDate, LocalDate>> itemReader() {
        return itemReader;
    }

    @Override
    protected ItemWriter<Pair<LocalDate, LocalDate>> itemWriter() {
        return itemWriter;
    }

    @Override
    protected int chunkSize() {
        return WEBRATE_DEFAULT_CHUNK_SIZE;
    }

    @Override
    protected ShouldExecuteStepResult shouldExecuteStep(JobExecution jobExecution, JobInstanceWorkContext jobInstanceWorkContext) {
        if (Boolean.parseBoolean(jobExecution.getJobParameters().getString(JobParameterKey.SHOULD_CONTINUE))) {
            JobExecutionUtil.setOnExecutionContext(jobExecution, CONTINUOUS_PRICING, configParamsService.getBooleanParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED));
            JobExecutionUtil.setOnExecutionContext(jobExecution, ADJUST_ORIGINAL_WEBRATE_VALUE_WITH_ROOM_REVENUE_TAX, configParamsService.getBooleanParameterValue(GUIConfigParamName.ADJUST_ORIGINAL_WEBRATE_VALUE_WITH_ROOM_REVENUE_TAX));
            return ShouldExecuteStepResult.TRUE;
        }
        return new ShouldExecuteStepResult(false, "Skipping due to Non-CP property");
    }

    @Override
    public void beforeStep(StepExecution stepExecution) {
        StepExecutionUtil.setOnExecutionContext(stepExecution, CHUNK_SIZE, super.getChunkSize());
        StepExecutionUtil.setOnExecutionContext(stepExecution, TOTAL_CHUNKS, super.getChunkSize());
        StepExecutionUtil.setOnExecutionContext(stepExecution, CHUNKS_PROCESSED, -1);
        StepExecutionUtil.setOnExecutionContext(stepExecution, IS_WEBRATE, true);

    }

    @Override
    public ExitStatus afterStep(StepExecution stepExecution) {
        return stepExecution.getExitStatus();
    }
}
