package com.ideas.tetris.jems.ngi.job.ngi.step;

import com.ideas.tetris.jems.core.job.entity.JobInstanceWorkContext;
import com.ideas.tetris.jems.core.step.tasklet.NonAsyncBusinessServiceStep;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.filemetadata.FileMetadataService;
import com.ideas.tetris.pacman.services.qualifiedrate.entity.RateQualifiedDetails;
import com.ideas.tetris.pacman.services.qualifiedrate.service.RateQualifiedService;
import com.ideas.tetris.pacman.util.CollectionUtils;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.JobExecution;
import org.springframework.batch.core.StepExecution;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

import static java.util.Optional.ofNullable;


@Component
@Slf4j
public class AddMissingRoomTypesHiltonPropertyStep extends NonAsyncBusinessServiceStep {

    @Autowired
    private RateQualifiedService rateQualifiedService;

    @Autowired
    private FileMetadataService fileMetadataService;

    @TenantCrudServiceBean.Qualifier
    @Qualifier("tenantCrudServiceBean")
    @Autowired
    protected CrudService tenantCrudService;

    @Override
    protected ShouldExecuteStepResult shouldExecuteStep(JobExecution jobExecution, JobInstanceWorkContext jobInstanceWorkContext) {
        if (PacmanWorkContextHelper.isHiltonClientCode()) {
            return ShouldExecuteStepResult.TRUE;
        }
        return new ShouldExecuteStepResult(false, "Skipping as property is not Hilton.");
    }

    @Override
    public Object doInvoke(StepExecution stepExecution, JobInstanceWorkContext jobInstanceWorkContext) throws Exception {
        var missingRoomTypeList = rateQualifiedService.findAccomTypeIdMissingInRateQualified();

        if (CollectionUtils.isNotEmpty(missingRoomTypeList)) {
            log.debug("Missing room types: {}", missingRoomTypeList);
            var startDate = fileMetadataService.getLatestSnapShotDate();
            var rateQualifiedLv0 = rateQualifiedService.findRateQualifiedLV0();
            var rateQualifiedDetailsByMissingRoomTypeList = missingRoomTypeList.stream()
                    .map(missingRoomType -> {
                RateQualifiedDetails rateQualifiedDetails = new RateQualifiedDetails();
                rateQualifiedDetails.setRateQualifiedId(rateQualifiedLv0.getId());
                rateQualifiedDetails.setStartDate(startDate);
                rateQualifiedDetails.setEndDate(rateQualifiedLv0.getEndDate());
                rateQualifiedDetails.setAccomTypeId(missingRoomType);
                rateQualifiedDetails.setMonday(BigDecimal.ZERO);
                rateQualifiedDetails.setTuesday(BigDecimal.ZERO);
                rateQualifiedDetails.setWednesday(BigDecimal.ZERO);
                rateQualifiedDetails.setThursday(BigDecimal.ZERO);
                rateQualifiedDetails.setFriday(BigDecimal.ZERO);
                rateQualifiedDetails.setSaturday(BigDecimal.ZERO);
                rateQualifiedDetails.setSunday(BigDecimal.ZERO);
                return rateQualifiedDetails;
            }).collect(Collectors.toList());

            tenantCrudService.save(rateQualifiedDetailsByMissingRoomTypeList);
        }
        return String.format("%s missing room types are added", ofNullable(missingRoomTypeList).map(List::size).orElse(0));
    }
}
