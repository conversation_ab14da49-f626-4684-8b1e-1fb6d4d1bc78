package com.ideas.tetris.jems.job.processcrsfile.step;


import com.ideas.tetris.jems.core.job.context.JobExecutionUtil;
import com.ideas.tetris.jems.core.job.entity.JobInstanceWorkContext;
import com.ideas.tetris.jems.core.step.tasklet.BusinessServiceStep;
import com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName;
import com.ideas.tetris.platform.services.Stage;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.springframework.batch.core.JobExecution;
import org.springframework.batch.core.StepExecution;
import org.springframework.beans.factory.annotation.Autowired;

public abstract class AbstractPopulateMissingPacePointStep extends BusinessServiceStep {
    @Autowired
    PacmanConfigParamsService pacmanConfigParamsService;

    @Override
    public boolean isStepAsync(StepExecution stepExecution) {
        return false;
    }

    @Override
    protected ShouldExecuteStepResult shouldExecuteStep(JobExecution jobExecution, JobInstanceWorkContext jobInstanceWorkContext) {
        boolean parameterValue = pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.POPULATE_MISSING_PACE_POINT_ENABLED);
        final boolean stagePopulationOrHigher = JobExecutionUtil.isStageEqualOrHigher(jobInstanceWorkContext, Stage.POPULATION.getCode());
        return parameterValue && stagePopulationOrHigher ? new ShouldExecuteStepResult(parameterValue) :
                new ShouldExecuteStepResult(false,
                        String.format("Skipping step as %s is set to false or Stage is DataCapture or Lower", PreProductionConfigParamName.POPULATE_MISSING_PACE_POINT_ENABLED.getParameterName()));
    }
}
