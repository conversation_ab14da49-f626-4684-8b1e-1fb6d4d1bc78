package com.ideas.tetris.jems.job.opera.step;


import com.ideas.tetris.jems.core.job.context.JobExecutionContextKey;
import com.ideas.tetris.jems.core.job.context.JobExecutionUtil;
import com.ideas.tetris.jems.core.job.entity.JobInstanceWorkContext;
import com.ideas.tetris.jems.core.step.tasklet.BusinessServiceStep;
import com.ideas.tetris.pacman.common.configparams.IPConfigParamName;
import com.ideas.tetris.pacman.common.configparams.IntegrationConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.services.opera.ValidateOperaFeedService;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.springframework.batch.core.JobExecution;
import org.springframework.batch.core.StepExecution;
import org.springframework.stereotype.Component;
import org.springframework.beans.factory.annotation.Autowired;

@Component
public class OperaDataFeedValidationStep extends BusinessServiceStep {

    @Autowired
	private ValidateOperaFeedService validateOperaFeedService;
    @Autowired
	private PacmanConfigParamsService pacmanConfigParamsService;

    @Override
    public boolean isStepAsync(StepExecution stepExecution) {
        return false;
    }

    @Override
    protected ShouldExecuteStepResult shouldExecuteStep(JobExecution jobExecution, JobInstanceWorkContext jobInstanceWorkContext) {
        if (!JobExecutionUtil.currentLoopIsActivity(jobExecution)) {
            return new ShouldExecuteStepResult(false, "Current Loop is Rate Shopping");
        }
        if (pacmanConfigParamsService.getBooleanParameterValue(IPConfigParamName.CORE_LIMITED_DATA_BUILD_ENABLED.value())) {
            return new ShouldExecuteStepResult(true, "Validation can never be skipped as Property has LDB enabled");
        }

        if (!pacmanConfigParamsService.getBooleanParameterValue(IntegrationConfigParamName.VALIDATE_DATA_FEED.value(Constants.OPERA))) {
            return new ShouldExecuteStepResult(false, "Validation of Opera feed is turned OFF");
        }

        return ShouldExecuteStepResult.TRUE;
    }

    @Override
    public Object doInvoke(StepExecution stepExecution, JobInstanceWorkContext jobInstanceWorkContext) throws Exception {
        validateOperaFeedService.validateOperaFeed((String) JobExecutionUtil.getFromExecutionContext(
                stepExecution.getJobExecution(), JobExecutionContextKey.CORRELATION_ID));
        stepExecution.setWriteCount(0);
        return null;
    }

}
