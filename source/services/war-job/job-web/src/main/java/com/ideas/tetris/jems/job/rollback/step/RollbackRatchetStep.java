package com.ideas.tetris.jems.job.rollback.step;


import com.ideas.tetris.jems.core.job.context.JobExecutionUtil;
import com.ideas.tetris.jems.core.job.entity.JobInstanceWorkContext;
import com.ideas.tetris.jems.core.step.tasklet.BusinessServiceStep;
import com.ideas.tetris.pacman.integration.ratchet.services.RatchetService;
import com.ideas.tetris.pacman.services.property.configuration.dto.PropertyConfigurationRecordType;
import com.ideas.tetris.pacman.services.property.configuration.dto.impl.PropertySetupPropertyConfigurationDto;
import com.ideas.tetris.pacman.services.property.configuration.service.PropertyConfigurationLoaderService;
import com.ideas.tetris.platform.common.externalsystem.ReservationSystem;
import org.springframework.batch.core.JobExecution;
import org.springframework.batch.core.StepExecution;
import org.springframework.stereotype.Component;

import java.util.Objects;
import org.springframework.beans.factory.annotation.Autowired;

@Component
public class RollbackRatchetStep extends BusinessServiceStep {
    @Autowired
    RatchetService ratchetService;
    @Autowired
	private PropertyConfigurationLoaderService propertyConfigurationLoaderService;

    @Override
    public boolean isStepAsync(StepExecution stepExecution) {
        return false;
    }

    @Override
    protected ShouldExecuteStepResult shouldExecuteStep(JobExecution jobExecution, JobInstanceWorkContext jobInstanceWorkContext) {
        ReservationSystem reservationSystem = JobExecutionUtil.getReservationSystem(jobExecution, true);
        return new ShouldExecuteStepResult(reservationSystem.getIsRatchet());
    }

    @Override
    public Object doInvoke(StepExecution stepExecution, JobInstanceWorkContext jobInstanceWorkContext) throws Exception {
        Integer clientId = jobInstanceWorkContext.getClientId();
        final String clientCode = jobInstanceWorkContext.getClientCode();
        final String propertyCode = jobInstanceWorkContext.getPropertyCode();
        final boolean includeExtendedStayInRollBack = isReservationSystemPCRSOrHILSTAR(stepExecution)
                && isExtendedStayProperty(clientId, propertyCode) && hasPendingEXSTConfiguration(clientId, propertyCode);

        ratchetService.rollbackDatabaseForProperty(clientCode, propertyCode, includeExtendedStayInRollBack, false);
        ratchetService.rollbackFilesForProperty(clientCode, propertyCode);
        final StringBuilder response = new StringBuilder("Rolled back ratchet tables, datasets, and extracts - oh my.");
        response.append(includeExtendedStayInRollBack ? " ExtendedStay included in rollback." : " ExtendedStay not included in rollback.");
        return response;
    }

    private boolean hasPendingEXSTConfiguration(Integer clientId, String propertyCode) {
        return Objects.nonNull(propertyConfigurationLoaderService.getConfigurationFileRecord(clientId, propertyCode, PropertyConfigurationRecordType.EXST));
    }

    private boolean isExtendedStayProperty(Integer clientId, String propertyCode) {
        PropertySetupPropertyConfigurationDto config = propertyConfigurationLoaderService.getPropertySetupConfiguration(clientId, propertyCode);
        return Objects.nonNull(config) && config.isExtendedStay();
    }

    private boolean isReservationSystemPCRSOrHILSTAR(StepExecution stepExecution) {
        return JobExecutionUtil.hasReservationSystemPCRSOrHILSTAR(stepExecution.getJobExecution());
    }
}
