package com.ideas.tetris.jems.common.step.reports;


import com.ideas.tetris.jems.core.job.context.JobExecutionContextKey;
import com.ideas.tetris.jems.core.job.context.JobExecutionUtil;
import com.ideas.tetris.jems.core.job.entity.JobInstanceWorkContext;
import com.ideas.tetris.jems.core.step.tasklet.BusinessServiceStep;
import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.utils.systemconfig.SystemConfig;
import com.ideas.tetris.pacman.services.email.EmailService;
import com.ideas.tetris.pacman.services.reports.AsyncReportsService;
import com.ideas.tetris.pacman.services.reports.ReportsG3Service;
import com.ideas.tetris.pacman.services.reports.entity.AsyncReport;
import com.ideas.tetris.pacman.services.scheduledreport.resource.ResourceUtil;
import com.ideas.tetris.pacman.services.security.UserGlobalDBService;
import com.ideas.tetris.platform.common.job.JobParameterKey;
import com.ideas.tetris.platform.reports.ReportSource;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.apache.log4j.Logger;
import org.springframework.batch.core.StepExecution;
import org.springframework.stereotype.Component;

import java.text.MessageFormat;
import java.util.Arrays;
import java.util.List;
import java.util.Locale;
import java.util.Map;

import static com.ideas.tetris.platform.common.utils.systemconfig.SystemConfig.getReportSchedulerMailSenderFrom;
import static com.ideas.tetris.platform.reports.jasperreports.constants.ReportsConstants.ONLINE_REPORT_PARAM_SEPARATOR;
import org.springframework.beans.factory.annotation.Autowired;

@Component
public class OnlineReportGenerationStep extends BusinessServiceStep {

    private static final Logger LOGGER = Logger.getLogger(OnlineReportGenerationStep.class.getName());
    private static final String LINE_SEPARATOR = "</br>";

    @Autowired
	protected EmailService emailService;

    @Autowired
    ReportsG3Service reportsG3Service;

    @Autowired
    AsyncReportsService asyncReportsService;

    @Autowired
    UserGlobalDBService userGlobalDBService;

    @Autowired
    PacmanConfigParamsService pacmanConfigParamsService;

    @Override
    public boolean isStepAsync(StepExecution stepExecution) {
        return false;
    }

    @Override
    public Object doInvoke(StepExecution stepExecution, JobInstanceWorkContext jobInstanceWorkContext) throws Exception {
        String asyncReportId = stepExecution.getJobParameters().getString(JobParameterKey.ASYNC_REPORT_ID);
        AsyncReport asyncReport = asyncReportsService.findAsyncReportById(Integer.parseInt(asyncReportId));
        JobExecutionUtil.setOnExecutionContext(stepExecution.getJobExecution(), JobExecutionContextKey.REPORT_NAME, asyncReport.getFileName());
        String emailRecipient = getEmailRecipient(stepExecution, asyncReport);
        JobExecutionUtil.setOnExecutionContext(stepExecution.getJobExecution(), JobExecutionContextKey.USER_EMAIL, emailRecipient);
        String locale = stepExecution.getJobParameters().getString(JobParameterKey.LOCALE);
        String propertyId = jobInstanceWorkContext.getPropertyId().toString();
        boolean isHiltonClientCode = jobInstanceWorkContext.isHiltonClientCode();
        Map<String, String> reportParameters = reportsG3Service.parseReportParams(asyncReport.getParameters(), ONLINE_REPORT_PARAM_SEPARATOR);
        String fileName = asyncReport.getFileName();
        reportsG3Service.generateOnlineReport(reportParameters, SystemConfig.getOnlineReportServerLocation(), fileName, ReportSource.UI_ASYNC);
        reportEmailDelivery(asyncReport, locale, propertyId, isHiltonClientCode, emailRecipient);
        return "";
    }

    private String getEmailRecipient(StepExecution stepExecution, AsyncReport asyncReport) {
        if (Boolean.parseBoolean(pacmanConfigParamsService.getParameterValueByClientLevel(FeatureTogglesConfigParamName.USE_UNIQUE_USERID_INSTEADOF_EMAILID.getParameterName()))) {
            return (stepExecution.getJobParameters().getString(JobParameterKey.EMAIL_FOR_USER_WITH_UNIQUE_USER_ID));
        } else {
            return (userGlobalDBService.getGlobalUserById(asyncReport.getUserId()).getEmail());
        }
    }

    private void reportEmailDelivery(AsyncReport asyncReport, String locale, String propertyId, boolean isHiltonClientCode, String emailRecipient) {
        List<String> toAddresses = Arrays.asList(emailRecipient);
        String subject = MessageFormat.format(ResourceUtil.getText("onlineReport.subject", new Locale(locale)), asyncReport.getFileName());
        StringBuilder message = new StringBuilder();
        message.append(MessageFormat.format(ResourceUtil.getText("onlineReport.mailContent.line1", new Locale(locale)), asyncReport.getReportType()));
        message.append("<p style=\"font-size:18px\">");
        prepareLink(asyncReport, locale, propertyId, message);
        message.append("</p>");
        buildMailBody(locale, message, "onlineReport.mailContent.line3");
        message.append("<ul>");
        buildMailBody(locale, message, "onlineReport.mailContent.line4");
        buildMailBody(locale, message, "onlineReport.mailContent.line5");
        message.append("</ul>");
        buildMailBody(locale, message, "onlineReport.mailContent.line6");
        String supportUrl = getSupportUrl(isHiltonClientCode);
        prepareCommunityLink(supportUrl, locale, message);
        buildMailBody(locale, message, "onlineReport.mailContent.line8");
        addLineSeparator(message, 1, LINE_SEPARATOR);
        buildMailBody(locale, message, "onlineReport.mailContent.line9");
        addLineSeparator(message, 2, LINE_SEPARATOR);
        buildMailBody(locale, message, "onlineReport.mailContent.line10");
        addLineSeparator(message, 1, LINE_SEPARATOR);
        buildMailBody(locale, message, "onlineReport.mailContent.line11");

        LOGGER.info("Sending email for report '" + asyncReport.getFileName() + "for user " + asyncReport.getUserId());
        emailService.sendEmail(getReportSchedulerMailSenderFrom(), toAddresses, subject, message.toString(), null, true);
    }

    private void prepareCommunityLink(String supportUrl, String locale, StringBuilder message) {
        message.append(" <a href=\"");
        message.append(supportUrl);
        message.append("\">");
        message.append(ResourceUtil.getText("onlineReport.mailContent.line7", new Locale(locale)));
        message.append("</a>");
    }

    private void addLineSeparator(StringBuilder message, int repeat, String strSuffix) {
        for (int i = 0; i < repeat; i++) {
            message.append(strSuffix);
        }
    }

    private void buildMailBody(String locale, StringBuilder message, String key) {
        message.append(ResourceUtil.getText(key, new Locale(locale)));
    }

    private void prepareLink(AsyncReport asyncReport, String locale, String propertyId, StringBuilder message) {
        message.append("<a href=\"");
        message.append(SystemConfig.getG3ClientLink());
        message.append(asyncReport.getPageCode());
        message.append("?asyncreportid=");
        message.append(asyncReport.getId());
        message.append("&uid=");
        message.append(asyncReport.getUserId());
        message.append("#/p");
        message.append(propertyId);
        message.append("\">");
        message.append(ResourceUtil.getText("onlineReport.mailContent.line2", new Locale(locale)));
        message.append("</a>");
    }

    public String getSupportUrl(boolean isHiltonClientCode) {
        if (isHiltonClientCode) {
            return SystemConfig.getHiltonUserSupportUrl();
        }
        return SystemConfig.getSalesforceSigninUrl();
    }
}
