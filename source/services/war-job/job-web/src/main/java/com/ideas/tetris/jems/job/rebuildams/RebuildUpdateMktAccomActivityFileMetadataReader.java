package com.ideas.tetris.jems.job.rebuildams;

import com.ideas.tetris.pacman.services.bestavailablerate.entity.MktSegAccomActivity;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.stereotype.Component;

@Component
@StepScope
public class RebuildUpdateMktAccomActivityFileMetadataReader extends CreateHistoryExtractChunkedReader {
    @Override
    public String getTableName() {
        return MktSegAccomActivity.TABLE_NAME;
    }
}
