package com.ideas.tetris.jems.job.centralrms.demand.step;

import com.ideas.infra.tetris.security.domain.TetrisPermissionKey;
import com.ideas.tetris.jems.core.job.entity.JobInstanceWorkContext;
import com.ideas.tetris.jems.job.centralrms.abstraction.CentralRMSBusinessServiceStep;
import com.ideas.tetris.pacman.services.centralrms.exception.CentralRMSUserNotAuthorizedException;
import com.ideas.tetris.platform.common.job.JobName;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.JobExecution;
import org.springframework.batch.core.StepExecution;
import org.springframework.stereotype.Component;

import static com.ideas.tetris.platform.common.job.JobParameterKey.PROPERTY_ID;
import static com.ideas.tetris.platform.common.job.JobParameterKey.USER_ID;

@Component
@Slf4j
public class DemandAdjustmentPermissionStep extends CentralRMSBusinessServiceStep {
    static final String NAVIGATOR_DEMAND_ADJUSTMENT_READ_WRITE = TetrisPermissionKey.PORTFOLIO_NAVIGATOR_DEMAND_ADJUSTMENT + "&access=readWrite";

    @Override
    protected ShouldExecuteStepResult shouldExecuteStep(JobExecution jobExecution, JobInstanceWorkContext jobInstanceWorkContext) {
        return useNavigatorPermission() ? ShouldExecuteStepResult.TRUE : ShouldExecuteStepResult.FALSE;
    }

    @Override
    protected Object invoke(StepExecution stepExecution, JobInstanceWorkContext jobInstanceWorkContext) throws Exception {
        String userId = stepExecution.getJobExecution().getJobParameters().getString(USER_ID);
        String propertyId = stepExecution.getJobExecution().getJobParameters().getString(PROPERTY_ID);

        // Check to ensure User has Read/Write Permission to adjust demand for this property
        boolean hasReadWriteAccess = hasPermission(userId, propertyId, NAVIGATOR_DEMAND_ADJUSTMENT_READ_WRITE);

        if (hasReadWriteAccess) {
            log.info("Central RMS User {} access for {}", userId, JobName.CentralRMSAdjustDemandJob);
            return "User has Permission to adjust demand";
        } else {
            log.info("Central RMS User {} has no access for {}", userId, JobName.CentralRMSAdjustDemandJob);
            throw new CentralRMSUserNotAuthorizedException("User does not have permission to update demand");
        }
    }
}
