package com.ideas.g3.integration.htng.handler;

import com.ideas.g3.integration.htng.client.HTNG_ARIAndReservationPushResponse;
import com.ideas.g3.integration.htng.client.MessageComponent;
import com.ideas.tetris.pacman.services.informationmanager.alert.service.VendorWarningsService;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import javax.xml.namespace.QName;
import javax.xml.soap.MessageFactory;
import javax.xml.soap.SOAPBody;
import javax.xml.soap.SOAPBodyElement;
import javax.xml.soap.SOAPElement;
import javax.xml.soap.SOAPEnvelope;
import javax.xml.soap.SOAPException;
import javax.xml.soap.SOAPHeader;
import javax.xml.soap.SOAPMessage;
import javax.xml.soap.SOAPPart;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.fail;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class ARIAndReservationResponseHandlerTest {
    @Mock
    private VendorWarningsService vendorWarningsService;

    @InjectMocks
    private ARIAndReservationResponseHandler handler;
    private MessageComponent messageComponent;

    @BeforeEach
    public void setUp() throws Exception {
        messageComponent = new MessageComponent(null, "1000", "2000", "4000",
                "5000", false, false, true, false,
                true);
    }

    @Test
    public void testHandleMessage_success() throws Exception {
        MessageFactory messageFactory = MessageFactory.newInstance();

        SOAPMessage soapMessage = messageFactory.createMessage();
        createSuccessBody(getSoapBody(soapMessage));
        HTNG_ARIAndReservationPushResponse response = handler.handleMessage(soapMessage, messageComponent);
        assertNotNull(response);
        assertEquals("200", response.getCode());
    }

    @Test
    public void testHandleMessage_customSSLFactory() throws Exception {
        messageComponent = new MessageComponent(null, "1000", "2000", "4000",
                "5000", false, false, true, true,
                true);
        MessageFactory messageFactory = MessageFactory.newInstance();

        SOAPMessage soapMessage = messageFactory.createMessage();
        createSuccessBody(getSoapBody(soapMessage));
        HTNG_ARIAndReservationPushResponse response = handler.handleMessage(soapMessage, messageComponent);
        assertNotNull(response);
        assertEquals("200", response.getCode());
    }

    @Test
    public void testMessage_SuccessWithErrors() throws Exception {
        MessageFactory messageFactory = MessageFactory.newInstance();
        SOAPMessage soapMessage = messageFactory.createMessage();
        createSuccessErrorBody(getSoapBody(soapMessage));

        assertThrows(TetrisException.class, () -> handler.handleMessage(soapMessage, messageComponent));
    }

    @Test
    public void testMessage_ErrorsWithWarnings() throws Exception {
        MessageFactory messageFactory = MessageFactory.newInstance();

        SOAPMessage soapMessage = messageFactory.createMessage();
        createWarningsWithErrorsBody(getSoapBody(soapMessage));

        assertThrows(TetrisException.class, () -> handler.handleMessage(soapMessage, messageComponent));
    }

    @Test
    public void testMessage_JustWarnings() throws Exception {
        MessageFactory messageFactory = MessageFactory.newInstance();

        SOAPMessage soapMessage = messageFactory.createMessage();
        createWarningsBody(getSoapBody(soapMessage));
        HTNG_ARIAndReservationPushResponse response = handler.handleMessage(soapMessage, messageComponent);
        assertNotNull(response);
        assertEquals("320", response.getCode());
    }

    @Test
    public void testMessage_WithNoStatus() throws Exception {
        MessageFactory messageFactory = MessageFactory.newInstance();
        SOAPMessage soapMessage = messageFactory.createMessage();
        getSoapBodyElement(getSoapBody(soapMessage));

        assertThrows(TetrisException.class, () -> handler.handleMessage(soapMessage, messageComponent));
    }

    @Test
    public void testHandleMessage_nullMessageDoesNoteExpectNull() {
        HTNG_ARIAndReservationPushResponse response = handler.handleMessage(null, messageComponent);
        assertNull(response);
    }

    @Test
    public void testHandleMessage_nullMessageExpectsNull() {
        messageComponent.setResponseOptional(true);
        HTNG_ARIAndReservationPushResponse response = handler.handleMessage(null, messageComponent);
        assertNotNull(response);
        assertEquals("200", response.getCode());
    }

    private void createWarningsBody(SOAPBody soapBody) throws Exception {
        SOAPBodyElement bodyElement = getSoapBodyElement(soapBody);
        addWarningsNode(bodyElement);
    }

    private SOAPBodyElement getSoapBodyElement(SOAPBody soapBody) throws SOAPException {
        QName bodyName = new QName("http://www.opentravel.org/OTA/2003/05", "OTA_HotelAvailNotifRS", "ns1");
        SOAPBodyElement bodyElement = soapBody.addBodyElement(bodyName);
        addBoilerAttributes(bodyElement);
        return bodyElement;
    }

    @Test
    public void testHandleMessage_successWithWarnings() throws Exception {
        MessageFactory messageFactory = MessageFactory.newInstance();

        SOAPMessage soapMessage = messageFactory.createMessage();
        createSuccessWithWarningsBody(getSoapBody(soapMessage));
        HTNG_ARIAndReservationPushResponse response = handler.handleMessage(soapMessage, messageComponent);
        assertNotNull(response);
        assertEquals("320", response.getCode());
    }

    @Test
    public void testHandleMessage_successAsync_emptyBody() throws Exception {
        SOAPMessage soapMessage = createAsyncMessage();
        HTNG_ARIAndReservationPushResponse response = handler.handleMessage(soapMessage, messageComponent);
        assertNotNull(response);
        assertEquals("200", response.getCode());
        assertEquals("Asynchronous request successfully sent.", response.getDescription());
    }

    private SOAPMessage createAsyncMessage() throws Exception {
        MessageFactory messageFactory = MessageFactory.newInstance();
        SOAPMessage soapMessage = messageFactory.createMessage();
        createAsyncHeader(soapMessage);
        return soapMessage;
    }

    @Test
    public void testHandleMessage_successAsync_nullBody() throws Exception {
        SOAPMessage soapMessage = createAsyncMessage();
        soapMessage.getSOAPPart().getEnvelope().getBody().detachNode();
        HTNG_ARIAndReservationPushResponse response = handler.handleMessage(soapMessage, messageComponent);
        assertNotNull(response);
        assertEquals("200", response.getCode());
        assertEquals("Asynchronous request successfully sent.", response.getDescription());
    }

    private void createAsyncHeader(SOAPMessage soapMessage) throws Exception {
        SOAPHeader header = soapMessage.getSOAPHeader();
        QName actionHeader = new QName("http://schemas.xmlsoap.org/ws/2004/08/addressing", "Action", "wsa");
        header.addHeaderElement(actionHeader);
        QName timestampHeader = new QName("http://schemas.xmlsoap.org/ws/2004/08/addressing", "TimeStamp", "wsa");
        header.addHeaderElement(timestampHeader);
        QName relatesToHeader = new QName("http://schemas.xmlsoap.org/ws/2004/08/addressing", "RelatesTo", "wsa");
        header.addHeaderElement(relatesToHeader);
    }

    @Test
    public void testHandleMessage_fault() throws SOAPException {
        MessageFactory messageFactory = MessageFactory.newInstance();
        SOAPMessage soapMessage = messageFactory.createMessage();
        createFaultBody(getSoapBody(soapMessage));
        try {
            handler.handleMessage(soapMessage, messageComponent);
            fail("Should have thrown a tetris exception");
        } catch (TetrisException e) {
            // expected
        }
    }

    @Test
    public void testHandleMessage_error() throws SOAPException {
        MessageFactory messageFactory = MessageFactory.newInstance();
        SOAPMessage soapMessage = messageFactory.createMessage();
        createErrorBody(getSoapBody(soapMessage));
        try {
            handler.handleMessage(soapMessage, messageComponent);
            fail("Should have thrown a tetris exception");
        } catch (TetrisException e) {
            assertEquals("The web service returned a 450 error - Start date is more than 730 days in the future..", e.getBaseMessage());
        }
    }

    @Test
    public void testHandleMessage_errorDescriptionNotInShortText() throws SOAPException {
        MessageFactory messageFactory = MessageFactory.newInstance();
        SOAPMessage soapMessage = messageFactory.createMessage();
        SOAPBodyElement bodyElement = getSoapBodyElement(getSoapBody(soapMessage));
        addErrorsNode_errorNotInShortText(bodyElement);
        try {
            handler.handleMessage(soapMessage, messageComponent);
            fail("Should have thrown a tetris exception");
        } catch (TetrisException e) {
            assertEquals("The web service returned a 450 error - There is conflict of rate calendar value with earlier request.  Value=.", e.getBaseMessage());
        }
    }


    private SOAPBody getSoapBody(SOAPMessage soapMessage) throws SOAPException {
        SOAPPart soapPart = soapMessage.getSOAPPart();
        SOAPEnvelope envelope = soapPart.getEnvelope();
        return envelope.getBody();
    }

    private void createSuccessBody(SOAPBody soapBody) throws SOAPException {
        SOAPBodyElement bodyElement = getSoapBodyElement(soapBody);
        addSuccessNode(bodyElement);
    }

    private void createSuccessWithWarningsBody(SOAPBody soapBody) throws SOAPException {
        SOAPBodyElement bodyElement = getSoapBodyElement(soapBody);
        addSuccessNode(bodyElement);

        addWarningsNode(bodyElement);

    }

    private void addWarningsNode(SOAPBodyElement bodyElement) throws SOAPException {
        QName warningsName = new QName("http://www.opentravel.org/OTA/2003/05", "Warnings", "ns1");
        SOAPElement warningsElement = bodyElement.addChildElement(warningsName);
        SOAPElement warningElement = warningsElement.addChildElement(new QName("http://www.opentravel.org/OTA/2003/05", "Warning", "ns1"));
        warningElement.addAttribute(new QName("Code"), "320");
        warningElement.addAttribute(new QName("ShortText"), "InvTypeCode is invalid: QR");
        warningElement.addAttribute(new QName("Type"), "3");
    }

    private void createWarningsWithErrorsBody(SOAPBody soapBody) throws SOAPException {
        SOAPBodyElement bodyElement = getSoapBodyElement(soapBody);
        addWarningsNode(bodyElement);
        addErrorsNode(bodyElement);
    }

    private void createFaultBody(SOAPBody soapBody) throws SOAPException {
        QName bodyName = new QName("http://www.opentravel.org/OTA/2003/05", "OTA_HotelAvailNotifRS", "ns1");
        soapBody.addFault(bodyName, "");
    }

    private void createErrorBody(SOAPBody soapBody) throws SOAPException {
        SOAPBodyElement bodyElement = getSoapBodyElement(soapBody);
        addErrorsNode(bodyElement);
    }

    private void createSuccessErrorBody(SOAPBody soapBody) throws Exception {
        SOAPBodyElement bodyElement = getSoapBodyElement(soapBody);
        addErrorsNode(bodyElement);

        addSuccessNode(bodyElement);
    }

    private void addBoilerAttributes(SOAPBodyElement bodyElement) throws SOAPException {
        bodyElement.addAttribute(new QName("PrimaryLangID"), "en-us");
        bodyElement.addAttribute(new QName("Target"), "Production");
        bodyElement.addAttribute(new QName("Version"), "2.001");
    }

    private void addErrorsNode(SOAPBodyElement bodyElement) throws SOAPException {
        QName errorsName = new QName("http://www.opentravel.org/OTA/2003/05", "Errors", "ns1");
        SOAPElement errorsElement = bodyElement.addChildElement(errorsName);
        SOAPElement errorElement = errorsElement.addChildElement(new QName("http://www.opentravel.org/OTA/2003/05", "Error", "ns1"));
        errorElement.addAttribute(new QName("Code"), "450");
        errorElement.addAttribute(new QName("ShortText"), "Start date is more than 730 days in the future.");
        errorElement.addAttribute(new QName("Status"), "Incomplete");
        errorElement.addAttribute(new QName("Type"), "3");
    }

    private void addErrorsNode_errorNotInShortText(SOAPBodyElement bodyElement) throws SOAPException {
        QName errorsName = new QName("http://www.opentravel.org/OTA/2003/05", "Errors", "ns1");
        SOAPElement errorsElement = bodyElement.addChildElement(errorsName);
        SOAPElement errorElement = errorsElement.addChildElement(new QName("http://www.opentravel.org/OTA/2003/05", "Error", "ns1"));
        errorElement.addAttribute(new QName("Code"), "450");
        errorElement.addAttribute(new QName("ShortText"), "1057");
        errorElement.addTextNode("There is conflict of rate calendar value with earlier request.  Value=");
        errorElement.addAttribute(new QName("Status"), "Incomplete");
        errorElement.addAttribute(new QName("Type"), "3");
    }

    private void addSuccessNode(SOAPBodyElement bodyElement) throws SOAPException {
        QName name = new QName("http://www.opentravel.org/OTA/2003/05", "Success", "ns1");
        bodyElement.addChildElement(name);
    }
}