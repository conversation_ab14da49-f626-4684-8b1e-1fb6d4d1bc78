package com.ideas.g3.integration.htng.decision;

import com.ideas.g3.integration.htng.client.*;
import com.ideas.g3.integration.htng.service.connection.ConnectionService;
import com.ideas.g3.integration.htng.service.decision.BarByFplosDecisionService;
import com.ideas.g3.integration.htng.transform.FplosTransformer;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.services.configsparam.service.ConfigParameterNameService;
import com.ideas.tetris.pacman.services.decisiondelivery.DecisionContentToXmlStringConverter;
import com.ideas.tetris.pacman.services.decisiondelivery.DecisionDeliveredService;
import com.ideas.tetris.pacman.services.decisiondelivery.entity.DecisionUploadDateToExternalSystem;
import com.ideas.tetris.pacman.services.fplos.entity.FPLOSDecisions;
import com.ideas.tetris.pacman.services.job.JobMonitorService;
import com.ideas.tetris.pacman.services.outbounddecisiondelivery.ProcessOutboundDecisions;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.opentravel.ota._2003._05.OTAHotelAvailNotifRQ;
import org.opentravel.ota._2003._05.OTAHotelRatePlanNotifRQ;

import javax.xml.soap.SOAPMessage;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

import static com.ideas.g3.integration.htng.HtngIntegrationTestHelper.createConnectionInformation;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import static org.testng.Assert.assertEquals;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class DecisionTest {

    public static final String PROPERTY_ID = "011022";
    public static final String PARTNER = ".hbsi";
    private static final String CLIENT_CODE = "clientCode";
    private static final String JOB_EXECUTION_ID = "98789";
    private static final String STEP_EXECUTION_ID = "67854";
    public static final String CONFIG_ROOT = "pacman.integration";
    private HTNG_ConnectionInformation connectionInformation = createConnectionInformation();

    @Mock
    ConnectionService connectionService;
    @Mock
    ProcessOutboundDecisions processOutboundDecisions;
    @Mock
    private CrudService crudService;
    @Mock
    private PacmanConfigParamsService configParamsServiceLocal;
    @Mock
    private FplosTransformer fplosTransformer;
    @Mock
    private HTNG_ARIAndReservationPushClient client;
    @Mock
    private SOAPMessage soapMessage;
    @Mock
    private HTNG_ARIAndReservationPushResponse response;
    @Mock
    private DecisionDeliveredService decisionDeliveredService;
    @Mock
    private JobMonitorService jobMonitorService;
    @InjectMocks
    private BarByFpLos barByFpLos;
    @Mock
    private BarByFplosDecisionService barByFplosDecisionService;

    @InjectMocks
    private DailyBar dailyBar;

    @BeforeEach
    public void setUp() throws Exception {
        when(crudService.findByNamedQuery(DecisionUploadDateToExternalSystem.QUERY_FOR_DECISION_ID,
                QueryParameter.with("decisionName", Constants.BAR_FPLOS_BY_ROOM_TYPE)
                        .and("externalSystemName", PARTNER).parameters())).thenReturn(Arrays.asList(1, 2));
        when(client.createSOAPRequest(any(OTAHotelAvailNotifRQ.class), any(MessageComponent.class))).thenReturn(soapMessage);
        when(client.sendAndReceive(any(MessageWrapper.class))).thenReturn(response);
        ConfigParameterNameService configParameterNameService = new ConfigParameterNameService();
        configParameterNameService.setPacmanConfigParamsService(configParamsServiceLocal);
        dailyBar.setConfigParameterNameService(configParameterNameService);
        barByFpLos.setConfigParameterNameService(configParameterNameService);
    }

    @Test
    public void updateRecordForDecisionDeliveryTracking() {
        when(crudService.executeUpdateByNamedQuery(DecisionUploadDateToExternalSystem.QUERY_TO_UPDATE_LAST_UPDATE_DATE_AND_STATUS,
                QueryParameter.with("status", Decision.SUC).and("id", 1).and("decisionName", Constants.BAR_FPLOS_BY_ROOM_TYPE).parameters())).thenReturn(1);
        barByFpLos.updateRecordForDecisionDeliveryTracking(PARTNER, true);
        verify(crudService).findByNamedQuery(DecisionUploadDateToExternalSystem.QUERY_FOR_DECISION_ID,
                QueryParameter.with("decisionName", Constants.BAR_FPLOS_BY_ROOM_TYPE)
                        .and("externalSystemName", PARTNER).parameters());
    }

    @Test
    public void testCreateSoapRequestCustomAction() {
        when(configParamsServiceLocal.getParameterValue(CONFIG_ROOT + Constants.HTNG_CUSTOM_AVAIL_ACTION)).thenReturn("custom");
        when(fplosTransformer.mapDecisionsToRequest(nullable(HTNG_ConnectionInformation.class), anyListOf(FPLOSDecisions.class), anyString(), eq(7))).thenReturn(new OTAHotelAvailNotifRQ());
        when(barByFplosDecisionService.getBarByFPLOSByRoomTypeMaxLOS()).thenReturn(7);
        List<FPLOSDecisions> decisions = createDecisions();
        ArgumentCaptor<MessageComponent> messageComponentCaptor = ArgumentCaptor.forClass(MessageComponent.class);
        barByFpLos.createMessageWrapperFromProvidedDecisions(PROPERTY_ID, CLIENT_CODE, JOB_EXECUTION_ID, STEP_EXECUTION_ID, PARTNER, decisions);

        verify(client).createSOAPRequest(any(OTAHotelAvailNotifRQ.class), messageComponentCaptor.capture());
        assertEquals(messageComponentCaptor.getValue().getAction(), "custom");
    }

    @Test
    public void testActionLookupRateCustomConfigured() {
        when(configParamsServiceLocal.getParameterValue(CONFIG_ROOT + Constants.HTNG_CUSTOM_RATE_ACTION)).thenReturn("customRate");

        String action = dailyBar.determineAction(new OTAHotelRatePlanNotifRQ(), PARTNER);

        assertEquals(action, "customRate");
    }

    @Test
    public void testActionLookupRateNotConfigure() {
        when(configParamsServiceLocal.getParameterValue(CONFIG_ROOT + Constants.HTNG_CUSTOM_RATE_ACTION)).thenReturn(null);

        String action = dailyBar.determineAction(new OTAHotelRatePlanNotifRQ(), PARTNER);

        assertEquals(action, Decision.RATEPLAN_ACTION);
    }

    @Test
    public void testSendChunkedDecisions() {
        MessageComponent messageComponent = getMessageComponent();
        messageComponent.setDecisionType(".LRVatRoomType");
        when(fplosTransformer.mapDecisionsToRequest(any(HTNG_ConnectionInformation.class), anyListOf(FPLOSDecisions.class), anyString())).thenReturn(new OTAHotelAvailNotifRQ());
        when(client.sendAndReceive(any(MessageWrapper.class))).thenReturn(new HTNG_ARIAndReservationPushResponse());

        SOAPMessage mockSoap = mock(SOAPMessage.class);
        when(jobMonitorService.getJobInstanceId(anyLong())).thenReturn(1L);
        when(decisionDeliveredService.saveDecisionDelivered(anyList(), anyString(), eq(1L), any(DecisionContentToXmlStringConverter.class))).thenReturn(true);
        barByFpLos.sendDecisions(new MessageWrapper(mockSoap, messageComponent));

        verify(client).sendAndReceive(any(MessageWrapper.class));
        verify(jobMonitorService).getJobInstanceId(anyLong());
        verify(decisionDeliveredService).saveDecisionDelivered(anyList(), anyString(), eq(1L), any(DecisionContentToXmlStringConverter.class));
    }

    private MessageComponent getMessageComponent() {
        return new MessageComponent(connectionInformation, PROPERTY_ID, CLIENT_CODE, JOB_EXECUTION_ID, STEP_EXECUTION_ID,
                true, false, true, false,
                true);
    }

    private List<FPLOSDecisions> createDecisions() {
        List<FPLOSDecisions> decisions = new ArrayList<>();

        FPLOSDecisions dec1 = new FPLOSDecisions();
        dec1.setArrivalDate(new Date());
        dec1.setAccomTypeName("accom");
        dec1.setPropertyId(1);
        dec1.setFplos("fplos");
        dec1.setRateCodeName("rateCode");

        decisions.add(dec1);
        return decisions;
    }

    @Test
    public void updateRecordForDecisionDeliveryTracking_notSuccess() {
        when(crudService.executeUpdateByNamedQuery(DecisionUploadDateToExternalSystem.QUERY_TO_UPDATE_STATUS,
                QueryParameter.with("status", Decision.FAIL).and("id", 1).and("decisionName", Constants.BAR_FPLOS_BY_ROOM_TYPE).parameters())).thenReturn(1);

        barByFpLos.updateRecordForDecisionDeliveryTracking(PARTNER, false);
        verify(crudService).findByNamedQuery(DecisionUploadDateToExternalSystem.QUERY_FOR_DECISION_ID,
                QueryParameter.with("decisionName", Constants.BAR_FPLOS_BY_ROOM_TYPE)
                        .and("externalSystemName", PARTNER).parameters());

        verify(crudService).executeUpdateByNamedQuery(DecisionUploadDateToExternalSystem.QUERY_TO_UPDATE_STATUS,
                QueryParameter.with("status", Decision.FAIL).and("id", 1).and("decisionName", Constants.BAR_FPLOS_BY_ROOM_TYPE).parameters());
    }

}