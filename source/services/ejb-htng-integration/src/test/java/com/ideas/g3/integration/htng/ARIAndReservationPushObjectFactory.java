package com.ideas.g3.integration.htng;

import org.opentravel.ota._2003._05.AvailStatusMessageType;
import org.opentravel.ota._2003._05.BookingRulesType;
import org.opentravel.ota._2003._05.HotelRatePlanType;
import org.opentravel.ota._2003._05.LengthsOfStayType;
import org.opentravel.ota._2003._05.OTAHotelAvailNotifRQ;
import org.opentravel.ota._2003._05.OTAHotelRatePlanNotifRQ;
import org.opentravel.ota._2003._05.RateUploadType;
import org.opentravel.ota._2003._05.StatusApplicationControlType;
import org.opentravel.ota._2003._05.TimeUnitType;
import org.opentravel.ota._2003._05.UniqueIDType;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collection;
import java.util.Date;

public final class ARIAndReservationPushObjectFactory {
    public static final String HOTEL_CODE = "IGTRTP";

    public static OTAHotelAvailNotifRQ.AvailStatusMessages createLRVStatusMessages() {
        OTAHotelAvailNotifRQ.AvailStatusMessages availStatusMessages = new OTAHotelAvailNotifRQ.AvailStatusMessages();
        availStatusMessages.setHotelCode(HOTEL_CODE);
        availStatusMessages.getAvailStatusMessage().add(createAvailStatusMessage());
        return availStatusMessages;
    }

    public static OTAHotelAvailNotifRQ.AvailStatusMessages createLRVStatusMessagesInvalidStartDate() {
        OTAHotelAvailNotifRQ.AvailStatusMessages availStatusMessages = new OTAHotelAvailNotifRQ.AvailStatusMessages();
        availStatusMessages.setHotelCode(HOTEL_CODE);
        availStatusMessages.getAvailStatusMessage().add(createAvailStatusMessageInvalidStartDate());
        return availStatusMessages;
    }

    public static OTAHotelAvailNotifRQ.AvailStatusMessages createLRVStatusMessagesInvalidEndDate() {
        OTAHotelAvailNotifRQ.AvailStatusMessages availStatusMessages = new OTAHotelAvailNotifRQ.AvailStatusMessages();
        availStatusMessages.setHotelCode(HOTEL_CODE);
        availStatusMessages.getAvailStatusMessage().add(createAvailStatusMessageInvalidEndDate());
        return availStatusMessages;
    }

    public static OTAHotelAvailNotifRQ.AvailStatusMessages createLRVStatusMessagesFutureStartDate(int daysInFuture) {
        OTAHotelAvailNotifRQ.AvailStatusMessages availStatusMessages = new OTAHotelAvailNotifRQ.AvailStatusMessages();
        availStatusMessages.setHotelCode(HOTEL_CODE);
        availStatusMessages.getAvailStatusMessage().add(createAvailStatusMessageFutureStartDate(daysInFuture));
        return availStatusMessages;
    }

    public static OTAHotelAvailNotifRQ.AvailStatusMessages createLRVStatusMessagesInvalidRoomType() {
        OTAHotelAvailNotifRQ.AvailStatusMessages availStatusMessages = new OTAHotelAvailNotifRQ.AvailStatusMessages();
        availStatusMessages.setHotelCode(HOTEL_CODE);
        availStatusMessages.getAvailStatusMessage().add(createAvailStatusMessageInvalidRoomType());
        return availStatusMessages;
    }

    public static OTAHotelRatePlanNotifRQ.RatePlans createRatePlans() {
        OTAHotelRatePlanNotifRQ.RatePlans ratePlans = new OTAHotelRatePlanNotifRQ.RatePlans();
        ratePlans.setHotelCode(HOTEL_CODE);
        ratePlans.getRatePlan().addAll(createRatePlansList());
        return ratePlans;
    }

    public static OTAHotelRatePlanNotifRQ.RatePlans createRatePlansFutureStartDate(int daysInFuture) {
        OTAHotelRatePlanNotifRQ.RatePlans ratePlans = new OTAHotelRatePlanNotifRQ.RatePlans();
        ratePlans.setHotelCode(HOTEL_CODE);
        ratePlans.getRatePlan().addAll(createRatePlansListFutureStartDate(daysInFuture));
        return ratePlans;
    }

    public static OTAHotelAvailNotifRQ.AvailStatusMessages createBARByLOSStatusMessages() {
        OTAHotelAvailNotifRQ.AvailStatusMessages availStatusMessages = new OTAHotelAvailNotifRQ.AvailStatusMessages();
        availStatusMessages.setHotelCode(HOTEL_CODE);
        availStatusMessages.getAvailStatusMessage().add(createLOSAvailStatusMessage());
        return availStatusMessages;
    }

    public static OTAHotelAvailNotifRQ.AvailStatusMessages createBARByLOSStatusMessagesInvalidRateCode() {
        OTAHotelAvailNotifRQ.AvailStatusMessages availStatusMessages = new OTAHotelAvailNotifRQ.AvailStatusMessages();
        availStatusMessages.setHotelCode(HOTEL_CODE);
        availStatusMessages.getAvailStatusMessage().add(createLOSAvailStatusMessageInvalidRateCode());
        return availStatusMessages;
    }

    private static Collection<? extends HotelRatePlanType> createRatePlansList() {
        Collection<HotelRatePlanType> hotelRatePlans = new ArrayList<HotelRatePlanType>();
        hotelRatePlans.add(createHotelRatePlanType());
        return hotelRatePlans;
    }

    private static Collection<? extends HotelRatePlanType> createRatePlansListFutureStartDate(int daysInFuture) {
        Collection<HotelRatePlanType> hotelRatePlans = new ArrayList<HotelRatePlanType>();
        hotelRatePlans.add(createHotelRatePlanTypeFutureStartDate(daysInFuture));
        return hotelRatePlans;
    }

    private static HotelRatePlanType createHotelRatePlanType() {
        HotelRatePlanType hotelRatePlanType = new HotelRatePlanType();
        hotelRatePlanType.setCurrencyCode("USD");
        hotelRatePlanType.setEnd("2012-03-01");
        hotelRatePlanType.setRatePlanCode("BAR-1");
        hotelRatePlanType.setRatePlanNotifType("Overlay");
        hotelRatePlanType.setStart("2012-03-01");
        hotelRatePlanType.setBookingRules(createBookingRules());
        hotelRatePlanType.setRates(createRates());
        return hotelRatePlanType;
    }

    private static HotelRatePlanType createHotelRatePlanTypeFutureStartDate(int daysInFuture) {
        HotelRatePlanType hotelRatePlanType = new HotelRatePlanType();
        hotelRatePlanType.setCurrencyCode("USD");
        hotelRatePlanType.setEnd(dateToString(createFutureDate(daysInFuture)));
        hotelRatePlanType.setRatePlanCode("BAR-1");
        hotelRatePlanType.setRatePlanNotifType("Overlay");
        hotelRatePlanType.setStart(dateToString(createFutureDate(daysInFuture)));
        hotelRatePlanType.setBookingRules(createBookingRules());
        hotelRatePlanType.setRates(createRates());
        return hotelRatePlanType;
    }

    private static Date createFutureDate(int daysInFuture) {
        Calendar today = Calendar.getInstance();
        today.setTime(new Date());
        today.add(Calendar.DAY_OF_YEAR, daysInFuture);
        System.out.println("Setting date to " + dateToString(today.getTime()));
        return today.getTime();
    }

    private static HotelRatePlanType.BookingRules createBookingRules() {
        HotelRatePlanType.BookingRules bookingRules = new HotelRatePlanType.BookingRules();
        bookingRules.getBookingRule().addAll(createBookingRuleList());
        return bookingRules;
    }

    private static Collection<? extends BookingRulesType.BookingRule> createBookingRuleList() {
        Collection<BookingRulesType.BookingRule> list = new ArrayList<BookingRulesType.BookingRule>();
        list.add(createBookingRule());
        return list;
    }

    private static BookingRulesType.BookingRule createBookingRule() {
        BookingRulesType.BookingRule bookingRule = new BookingRulesType.BookingRule();
        bookingRule.setLengthsOfStay(createLengthOfStayType());
        return bookingRule;
    }

    private static LengthsOfStayType createLengthOfStayType() {
        LengthsOfStayType lengthsOfStayType = new LengthsOfStayType();
        lengthsOfStayType.getLengthOfStay().addAll(createLengthOfStayList());
        return lengthsOfStayType;
    }

    private static Collection<? extends LengthsOfStayType.LengthOfStay> createLengthOfStayList() {
        Collection<LengthsOfStayType.LengthOfStay> list = new ArrayList<LengthsOfStayType.LengthOfStay>();
        list.add(createLengthOfStay());
        return list;
    }

    private static LengthsOfStayType.LengthOfStay createLengthOfStay() {
        LengthsOfStayType.LengthOfStay lengthOfStay = new LengthsOfStayType.LengthOfStay();
        lengthOfStay.setMinMaxMessageType("SetMinLOS");
        lengthOfStay.setTime(BigInteger.valueOf(1));
        lengthOfStay.setTimeUnit(TimeUnitType.DAY);
        return lengthOfStay;
    }

    private static HotelRatePlanType.Rates createRates() {
        HotelRatePlanType.Rates rates = new HotelRatePlanType.Rates();
        rates.getRate().addAll(createRatesList());
        return rates;
    }

    private static Collection<? extends HotelRatePlanType.Rates.Rate> createRatesList() {
        Collection<HotelRatePlanType.Rates.Rate> rateList = new ArrayList<HotelRatePlanType.Rates.Rate>();
        rateList.add(createRate("SQ"));
        rateList.add(createRate("SK"));
        return rateList;
    }

    private static HotelRatePlanType.Rates.Rate createRate(String invTypeCode) {
        HotelRatePlanType.Rates.Rate rate = new HotelRatePlanType.Rates.Rate();
        rate.setInvTypeCode(invTypeCode);
        rate.setRateTimeUnit(TimeUnitType.DAY);
        rate.getStatus().add("Open");
        rate.setUnitMultiplier(1);
        rate.setBaseByGuestAmts(createBaseByGuestAmounts());
        return rate;
    }

    private static RateUploadType.BaseByGuestAmts createBaseByGuestAmounts() {
        RateUploadType.BaseByGuestAmts baseByGuestAmts = new RateUploadType.BaseByGuestAmts();
        baseByGuestAmts.getBaseByGuestAmt().addAll(createBaseByGuestAmountList());
        return baseByGuestAmts;
    }

    private static Collection<? extends RateUploadType.BaseByGuestAmts.BaseByGuestAmt> createBaseByGuestAmountList() {
        Collection<RateUploadType.BaseByGuestAmts.BaseByGuestAmt> list = new ArrayList<RateUploadType.BaseByGuestAmts.BaseByGuestAmt>();
        list.add(createBaseByGuestAmt("10", BigDecimal.valueOf(295.0), 1));
        list.add(createBaseByGuestAmt("10", BigDecimal.valueOf(295.0), 2));
        return list;
    }

    private static RateUploadType.BaseByGuestAmts.BaseByGuestAmt createBaseByGuestAmt(String age, BigDecimal amtBefTax, Integer numGuests) {
        RateUploadType.BaseByGuestAmts.BaseByGuestAmt amt = new RateUploadType.BaseByGuestAmts.BaseByGuestAmt();
        amt.setAgeQualifyingCode(age);
        amt.setAmountBeforeTax(amtBefTax);
        amt.setNumberOfGuests(numGuests);
        return amt;
    }

    private static AvailStatusMessageType createAvailStatusMessage() {
        AvailStatusMessageType messageType = new AvailStatusMessageType();
        messageType.setStatusApplicationControl(createStatusApplicationControlType());
        messageType.setHurdleRate(createHurdleRate());
        messageType.setDelta(createDelta());
        messageType.setUniqueID(createUniqueIdType());
        return messageType;
    }

    private static AvailStatusMessageType createAvailStatusMessageInvalidStartDate() {
        AvailStatusMessageType messageType = new AvailStatusMessageType();
        messageType.setStatusApplicationControl(createStatusApplicationControlTypeInvalidStartDate());
        messageType.setHurdleRate(createHurdleRate());
        messageType.setDelta(createDelta());
        messageType.setUniqueID(createUniqueIdType());
        return messageType;
    }

    private static AvailStatusMessageType createAvailStatusMessageInvalidEndDate() {
        AvailStatusMessageType messageType = new AvailStatusMessageType();
        messageType.setStatusApplicationControl(createStatusApplicationControlTypeInvalidEndDate());
        messageType.setHurdleRate(createHurdleRate());
        messageType.setDelta(createDelta());
        messageType.setUniqueID(createUniqueIdType());
        return messageType;
    }

    private static AvailStatusMessageType createAvailStatusMessageFutureStartDate(int daysInFuture) {
        AvailStatusMessageType messageType = new AvailStatusMessageType();
        messageType.setStatusApplicationControl(createStatusApplicationControlTypeFutureStartDate(daysInFuture));
        messageType.setHurdleRate(createHurdleRate());
        messageType.setDelta(createDelta());
        messageType.setUniqueID(createUniqueIdType());
        return messageType;
    }

    private static AvailStatusMessageType createAvailStatusMessageInvalidRoomType() {
        AvailStatusMessageType messageType = new AvailStatusMessageType();
        messageType.setStatusApplicationControl(createStatusApplicationControlTypeInvalidRoomType());
        messageType.setHurdleRate(createHurdleRate());
        messageType.setDelta(createDelta());
        messageType.setUniqueID(createUniqueIdType());
        return messageType;
    }

    private static AvailStatusMessageType createLOSAvailStatusMessage() {
        AvailStatusMessageType messageType = new AvailStatusMessageType();
        messageType.setStatusApplicationControl(createStatusApplicationControlType());
        messageType.setBestAvailableRates(createBestAvailableRates());
        messageType.setUniqueID(createUniqueIdType());
        return messageType;
    }

    private static AvailStatusMessageType createLOSAvailStatusMessageInvalidRateCode() {
        AvailStatusMessageType messageType = new AvailStatusMessageType();
        messageType.setStatusApplicationControl(createStatusApplicationControlType());
        messageType.setBestAvailableRates(createBestAvailableRatesInvalidRateCode());
        messageType.setUniqueID(createUniqueIdType());
        return messageType;
    }

    private static AvailStatusMessageType.BestAvailableRates createBestAvailableRates() {
        AvailStatusMessageType.BestAvailableRates rates = new AvailStatusMessageType.BestAvailableRates();
        rates.getBestAvailableRate().addAll(createBARList());
        return rates;
    }

    private static AvailStatusMessageType.BestAvailableRates createBestAvailableRatesInvalidRateCode() {
        AvailStatusMessageType.BestAvailableRates rates = new AvailStatusMessageType.BestAvailableRates();
        rates.getBestAvailableRate().addAll(createBARListInvalidRateCode());
        return rates;
    }

    private static Collection<? extends AvailStatusMessageType.BestAvailableRates.BestAvailableRate> createBARList() {
        Collection<AvailStatusMessageType.BestAvailableRates.BestAvailableRate> rates = new ArrayList<AvailStatusMessageType.BestAvailableRates.BestAvailableRate>();
        AvailStatusMessageType.BestAvailableRates.BestAvailableRate bar = new AvailStatusMessageType.BestAvailableRates.BestAvailableRate();
        bar.setAmount(BigDecimal.valueOf(111.00));
        bar.setLengthOfStayTime(BigInteger.valueOf(1));
        bar.setRatePlanCode("BAR-1");
        rates.add(bar);
        return rates;
    }

    private static Collection<? extends AvailStatusMessageType.BestAvailableRates.BestAvailableRate> createBARListInvalidRateCode() {
        Collection<AvailStatusMessageType.BestAvailableRates.BestAvailableRate> rates = new ArrayList<AvailStatusMessageType.BestAvailableRates.BestAvailableRate>();
        AvailStatusMessageType.BestAvailableRates.BestAvailableRate bar = new AvailStatusMessageType.BestAvailableRates.BestAvailableRate();
        bar.setAmount(BigDecimal.valueOf(111.00));
        bar.setLengthOfStayTime(BigInteger.valueOf(1));
        bar.setRatePlanCode("INVALID");
        rates.add(bar);
        return rates;
    }

    private static StatusApplicationControlType createStatusApplicationControlType() {
        StatusApplicationControlType statusApplicationControlType = new StatusApplicationControlType();
        statusApplicationControlType.setInvTypeCode("SQ");
        statusApplicationControlType.setStart("2014-01-01");
        statusApplicationControlType.setEnd("2014-01-01");
        return statusApplicationControlType;
    }

    private static StatusApplicationControlType createStatusApplicationControlTypeInvalidStartDate() {
        StatusApplicationControlType statusApplicationControlType = new StatusApplicationControlType();
        statusApplicationControlType.setInvTypeCode("SQ");
        statusApplicationControlType.setStart("2014-01-61");
        statusApplicationControlType.setEnd("2014-01-01");
        return statusApplicationControlType;
    }

    private static StatusApplicationControlType createStatusApplicationControlTypeInvalidEndDate() {
        StatusApplicationControlType statusApplicationControlType = new StatusApplicationControlType();
        statusApplicationControlType.setInvTypeCode("SQ");
        statusApplicationControlType.setStart("2014-01-01");
        statusApplicationControlType.setEnd("2014-01-61");
        return statusApplicationControlType;
    }

    private static StatusApplicationControlType createStatusApplicationControlTypeFutureStartDate(int daysInFuture) {
        StatusApplicationControlType statusApplicationControlType = new StatusApplicationControlType();
        statusApplicationControlType.setInvTypeCode("SQ");
        statusApplicationControlType.setStart(dateToString(createFutureDate(daysInFuture)));
        statusApplicationControlType.setEnd(dateToString(createFutureDate(daysInFuture)));
        return statusApplicationControlType;
    }

    private static String dateToString(Date date) {
        DateFormat df = new SimpleDateFormat("yyyy-MM-dd");
        return df.format(date);
    }

    private static StatusApplicationControlType createStatusApplicationControlTypeInvalidRoomType() {
        StatusApplicationControlType statusApplicationControlType = new StatusApplicationControlType();
        statusApplicationControlType.setInvTypeCode("INVALID");
        statusApplicationControlType.setStart("2014-01-01");
        statusApplicationControlType.setEnd("2014-01-01");
        return statusApplicationControlType;
    }

    private static AvailStatusMessageType.HurdleRate createHurdleRate() {
        AvailStatusMessageType.HurdleRate hurdleRate = new AvailStatusMessageType.HurdleRate();
        hurdleRate.setAmount(BigDecimal.valueOf(100.00));
        hurdleRate.setCurrencyCode("EUR");
        return hurdleRate;
    }

    private static AvailStatusMessageType.Delta createDelta() {
        AvailStatusMessageType.Delta delta = new AvailStatusMessageType.Delta();
        delta.setAmount(BigDecimal.valueOf(2.25));
        delta.setCeiling(BigInteger.valueOf(35));
        delta.setMaxSold(BigInteger.valueOf(123));
        return delta;
    }

    private static UniqueIDType createUniqueIdType() {
        UniqueIDType uniqueIDType = new UniqueIDType();
        uniqueIDType.setType("16");
        uniqueIDType.setID("1");
        return uniqueIDType;
    }
}
