package com.ideas.g3.integration.htng.decision;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.collect.Lists;
import com.ideas.g3.integration.htng.client.HTNG_ARIAndReservationPushResponse;
import com.ideas.g3.integration.htng.service.decision.HtngDailyBarDecisionService;
import com.ideas.g3.integration.htng.transform.DailyBarTransformer;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.dailybar.entity.DailyBarDecisions;
import com.ideas.tetris.platform.common.externalsystem.ExternalSystem;
import org.apache.log4j.Logger;
import org.opentravel.ota._2003._05.OTAHotelRatePlanNotifRQ;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Component
@Transactional
public class DailyBar extends Decision<OTAHotelRatePlanNotifRQ, DailyBarDecisions> {
    private static final Logger LOGGER = Logger.getLogger(DailyBar.class.getName());

    @Autowired
    HtngDailyBarDecisionService htngDailyBarDecisionService;
    @Autowired
    DailyBarTransformer dailyBarTransformer;


    public HTNG_ARIAndReservationPushResponse sendDecisions(String propertyId,
                                                            String clientCode,
                                                            String jobExecutionId,
                                                            String stepExecutionId,
                                                            String partner) {
        return buildAndSendDecisions(propertyId, clientCode, jobExecutionId, stepExecutionId, partner);
    }


    public Map<String, List<DailyBarDecisions>> getDailyBarDecisions(String propertyId, String partner) {
        return htngDailyBarDecisionService.getAllDailyBarDecisions(partner, true);
    }

    @VisibleForTesting
    public Map<String, List<DailyBarDecisions>> getDailyBarDecisionsByExternalSystem(String propertyId, String partner) {
        PacmanWorkContextHelper.setExternalSystem(ExternalSystem.OPTIMACONTROLS);
        return htngDailyBarDecisionService.getAllDailyBarDecisions(partner, true);
    }

    @Override
    public List<DailyBarDecisions> getDecisionsWithOptionalTracking(String partner, boolean insertDeliveryTrackingRecord) {
        return htngDailyBarDecisionService.getDailyBarDecisions(partner, insertDeliveryTrackingRecord);
    }

    @Override
    public OTAHotelRatePlanNotifRQ createHtngPayloadFromProvidedDecisions(String externalSystemName, List<DailyBarDecisions> decisions) {
        return dailyBarTransformer.mapDecisionsToRequest(
                connectionService.getHtngConnectionInformation(getConfigRoot(externalSystemName)),
                convertToRatePlanDecisionMap(decisions),
                getConfigRoot(externalSystemName),
                externalSystemName,
                false);
    }

    private Map<String, List<DailyBarDecisions>> convertToRatePlanDecisionMap(List<DailyBarDecisions> decisions) {
        Map<String, List<DailyBarDecisions>> decisionsRatePlanLevel = new HashMap<>();
        List<DailyBarDecisions> dailyBarDecisions;
        for (DailyBarDecisions dailyBarDecision : decisions) {
            String ratePlan = dailyBarDecision.getRatePlan();
            if (decisionsRatePlanLevel.containsKey(ratePlan)) {
                dailyBarDecisions = decisionsRatePlanLevel.get(ratePlan);
            } else {
                dailyBarDecisions = new ArrayList<>();
            }
            dailyBarDecisions.add(dailyBarDecision);
            decisionsRatePlanLevel.put(ratePlan, dailyBarDecisions);
        }
        return decisionsRatePlanLevel;
    }


    @Override
    public OTAHotelRatePlanNotifRQ createHtngPayload(String propertyId, String partner) {
        String configRoot = getConfigRoot(partner);
        Map<String, List<DailyBarDecisions>> decisions = getDailyBarDecisions(propertyId, partner);
        LOGGER.info(" Received decisions from service and ready to create message : " + decisions.size());
        return dailyBarTransformer.mapDecisionsToRequest(connectionService.getHtngConnectionInformation(configRoot),
                decisions, configRoot, partner, false);
    }

    @Override
    public boolean isConfigured(String externalSystemName) {
        return processOutboundDecisions.shouldSendDailyBar(externalSystemName);
    }

    @Override
    public boolean hasDecisions(String externalSystemName) {
        return htngDailyBarDecisionService.hasDecisions(externalSystemName);
    }

    @Override
    public String getDecisionSendingType() {
        return Constants.HTNG_DAILYBAR;
    }

    @Override
	public
    List<String> getTrackingNames(String externalSystem) {
        return Lists.newArrayList(Constants.DAILYBAR, Constants.ES_DAILYBAR);
    }

    @Override
	public
    String getDecisionTypeDisplayText() {
        return "Daily BAR";
    }

    @Override
    public String getDecisionType() {
        return Constants.DAILYBAR;
    }
}
