package com.ideas.tetris.ui.modules.roomconfiguration;

import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.configparams.GUIConfigParamName;
import com.ideas.tetris.pacman.common.configparams.IPConfigParamName;
import com.ideas.tetris.pacman.common.configparams.IntegrationConfigParamName;
import com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.accommodation.AccomTypeADR;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomClass;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomClassPriceRank;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomClassPriceRankNetworkArrow;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomClassPriceRankStatus;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomType;
import com.ideas.tetris.pacman.services.accommodation.entity.RoomConfigurationSelection;
import com.ideas.tetris.pacman.services.accommodation.service.AccomClassPriceRankService;
import com.ideas.tetris.pacman.services.accommodation.service.AccommodationService;
import com.ideas.tetris.pacman.services.agilerates.configuration.entity.ProductAccomType;
import com.ideas.tetris.pacman.services.agilerates.configuration.service.AgileRatesConfigurationService;
import com.ideas.tetris.pacman.services.bestavailablerate.CloseHighestBarService;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.AccomClassMinPriceDiff;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.AccomClassMinPriceDiffSeason;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.DailyMinPriceDiff;
import com.ideas.tetris.pacman.services.client.service.ClientService;
import com.ideas.tetris.pacman.services.componentrooms.dto.ComponentRoomsConfiguration;
import com.ideas.tetris.pacman.services.componentrooms.services.ComponentRoomService;
import com.ideas.tetris.pacman.services.costofwalk.entity.CostofWalk;
import com.ideas.tetris.pacman.services.costofwalk.service.CostofWalkService;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.eventaggregator.SyncEvent;
import com.ideas.tetris.pacman.services.eventaggregator.SyncEventAggregatorService;
import com.ideas.tetris.pacman.services.groupfinalforecast.GroupFinalForecastConfigService;
import com.ideas.tetris.pacman.services.grouppricing.configuration.entity.GroupPricingConfigAccomType;
import com.ideas.tetris.pacman.services.grouppricing.configuration.service.GroupPricingConfigurationService;
import com.ideas.tetris.pacman.services.hospitalityrooms.entity.HospitalityRoomsConfig;
import com.ideas.tetris.pacman.services.hospitalityrooms.service.HospitalityRoomsService;
import com.ideas.tetris.pacman.services.informationmanager.alert.service.AlertService;
import com.ideas.tetris.pacman.services.informationmanager.dto.AlertType;
import com.ideas.tetris.pacman.services.informationmanager.entity.InfoMgrInstanceEntity;
import com.ideas.tetris.pacman.services.informationmanager.entity.InfoMgrTypeEntity;
import com.ideas.tetris.pacman.services.informationmanager.exception.service.ExceptionConfigService;
import com.ideas.tetris.pacman.services.inventorygroup.service.InventoryGroupService;
import com.ideas.tetris.pacman.services.lrvdroprestriction.service.LrvDropRestrictionService;
import com.ideas.tetris.pacman.services.opera.CostOfWalkCalculator;
import com.ideas.tetris.pacman.services.overbooking.dto.OverbookingAccomTypeDto;
import com.ideas.tetris.pacman.services.overbooking.dto.OverbookingAccomTypeSeasonDto;
import com.ideas.tetris.pacman.services.overbooking.service.OverbookingService;
import com.ideas.tetris.pacman.services.override.InvalidateOverridesService;
import com.ideas.tetris.pacman.services.perpersonpricing.MaximumOccupantsEntity;
import com.ideas.tetris.pacman.services.perpersonpricing.PerPersonPricingService;
import com.ideas.tetris.pacman.services.pricingconfiguration.entity.PricingAccomClass;
import com.ideas.tetris.pacman.services.pricingconfiguration.service.PricingConfigurationLTBDEService;
import com.ideas.tetris.pacman.services.pricingconfiguration.service.PricingConfigurationService;
import com.ideas.tetris.pacman.services.product.Product;
import com.ideas.tetris.pacman.services.ratepopulation.QualifiedRateService;
import com.ideas.tetris.pacman.services.roa.attribute.entity.PropertyAttributeEnum;
import com.ideas.tetris.pacman.services.roa.attribute.service.ROAPropertyAttributeService;
import com.ideas.tetris.pacman.services.roomtyperecoding.services.RoomTypeRecodingService;
import com.ideas.tetris.pacman.services.tax.entity.Tax;
import com.ideas.tetris.pacman.services.tax.service.TaxService;
import com.ideas.tetris.pacman.services.webrate.service.AccommodationMappingService;
import com.ideas.tetris.pacman.util.SeasonService;
import com.ideas.tetris.platform.common.xml.schema.workcontext.v1.WorkContextType;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import com.ideas.tetris.ui.VaadinUIBaseJupiterTest;
import com.ideas.tetris.ui.common.cdi.cdiutils.Lang;
import com.ideas.tetris.ui.common.security.UiContext;
import com.ideas.tetris.ui.modules.roomconfiguration.mapping.views.RoomTypeProductWrapper;
import com.ideas.tetris.ui.modules.roomconfiguration.overbooking.OverbookingSeasonUIWrapper;
import com.ideas.tetris.ui.modules.roomconfiguration.pricediff.MinimumPriceDifferentialUIWrapper;
import com.ideas.tetris.ui.modules.roomconfiguration.views.RoomsConfigurationWizardView;
import com.ideas.tetris.ui.shell.sync.ForceSyncEvent;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.reflect.FieldUtils;
import org.joda.time.LocalDate;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.SortedSet;
import java.util.TreeSet;
import java.util.function.Predicate;

import static com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName.ENABLE_ROOM_CLASS_EXCLUSION_IN_ROH_GROUP_EVALUATION;
import static com.ideas.tetris.pacman.services.accommodation.entity.AccomClassPriceRankNetworkArrow.Direction;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.anyList;
import static org.mockito.Mockito.anyListOf;
import static org.mockito.Mockito.anyMap;
import static org.mockito.Mockito.anyObject;
import static org.mockito.Mockito.anySet;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyZeroInteractions;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class RoomsConfigurationPresenterTest extends VaadinUIBaseJupiterTest {

    private static final boolean IS_GROUP_EVALUATION_ENABLED = true;
    private static final Integer PROPERTY_ID = 5;
    @InjectMocks
    private RoomsConfigurationPresenter presenter = new RoomsConfigurationPresenter();

    @Mock
    AccommodationService accommodationService;

    @Mock
    AccommodationMappingService accommodationMappingService;

    @Mock
    InvalidateOverridesService invalidateOverridesService;

    @Mock
    private LrvDropRestrictionService lrvDropRestrictionService;

    @Mock
    CostofWalkService costofWalkService;

    @Mock
    OverbookingService overbookingService;

    @Mock
    RoomsConfigurationWizardView view;

    @Mock
    AccomClassPriceRankService accomClassPriceRankService;

    @Mock
    PricingConfigurationService pricingConfigurationService;

    @Mock
    PacmanConfigParamsService configParamsService;

    @Mock
    ComponentRoomService componentRoomService;

    @Mock
    javax.enterprise.event.Event<ForceSyncEvent> forceSyncEvent;

    @Mock
    Lang lang;

    @Mock
    SeasonService seasonService;

    @Mock
    ExceptionConfigService exceptionConfigService;

    @Mock
    CloseHighestBarService closeHighestBarService;

    @Captor
    ArgumentCaptor<List<AccomClassPriceRank>> priceRanksCaptor;

    @Mock
    InventoryGroupService inventoryGroupService;

    @Mock
    PerPersonPricingService perPersonPricingService;

    @Mock
    AlertService alertService;

    @Mock
    RoomTypeRecodingService roomTypeRecodingService;

    @Mock
    UiContext uiContext;

    @Mock
    DateService dateService;

    @Mock
    AgileRatesConfigurationService agileRatesConfigurationService;

    @Mock
    TaxService taxService;

    @Mock
    GroupFinalForecastConfigService groupFinalForecastConfigService;

    @Mock
    HospitalityRoomsService hospitalityRoomsService;

    @Mock
    SyncEventAggregatorService syncEventAggregatorService;

    @Mock
    QualifiedRateService qualifiedRateService;

    @Mock
    ClientService clientService;

    @Mock
    GroupPricingConfigurationService groupPricingConfigurationService;

    @Mock
    CostOfWalkCalculator costOfWalkCalculator;

    @Mock
    PricingConfigurationLTBDEService pricingConfigurationLTBDEService;

    @Mock
    ROAPropertyAttributeService propertyAttributeService;

    @BeforeEach
    public void setUp() {
        presenter.setCPProperty(true);
    }

    @Test
    public void getDataForRoomTypeMappings() throws Exception {
        AccomTypeADR accomTypeADR1 = new AccomTypeADR(new AccomType(), BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO);
        AccomTypeADR accomTypeADR2 = new AccomTypeADR(new AccomType(), BigDecimal.ONE, BigDecimal.TEN, BigDecimal.TEN);
        List<AccomTypeADR> accomTypeADRs = Arrays.asList(accomTypeADR1, accomTypeADR2);
        when(accommodationService.getAllActiveAccomTypeADRs()).thenReturn(accomTypeADRs);


        //Order is flipped
        List<AccomClass> accomClasses = buildAccomClasses();
        when(accommodationService.getAccomClassesByViewOrder()).thenReturn(accomClasses);

        presenter.getDataForRoomTypeMappings();

        SortedSet<AccomTypeADR> resultADRs = presenter.getAccomTypeADRs();
        assertEquals(2, resultADRs.size());
        assertTrue(resultADRs.containsAll(accomTypeADRs));

        SortedSet<AccomClass> resultAccomClasses = presenter.getAccomClasses();

        // Verify that the AccomClasses are in the right order and that the viewOrder got updated appropriately
        List<String> expectedOrder = Arrays.asList("IMPERIALS", "PREMIUMDBLS", "DOUBLES", "SPCSUITE", "SUITE", "PREMIUM", "DELUXE", "STANDARD", "UNASSIGNED");

        final int[] viewOrder = {0};
        resultAccomClasses.forEach(accomClass -> {
            assertEquals(expectedOrder.get(viewOrder[0]), accomClass.getName());
            if ("UNASSIGNED".equals(accomClass.getName())) {
                assertNull(accomClass.getViewOrder());
            } else {
                assertEquals(8 - viewOrder[0], accomClass.getViewOrder().intValue());
            }
            viewOrder[0]++;
        });
        verify(view).captureMappingViewOriginalData();
    }

    @Test
    void shouldPopulateInitialRoomClassesWhileLoadingDateForRoomTypeMapping() {
        AccomClass accomClass = new AccomClass();
        accomClass.setId(101);
        accomClass.setName("STANDARD");

        AccomType accomType = new AccomType();
        accomType.setId(10);
        accomType.setName("STD");
        accomType.setAccomClass(accomClass);

        accomClass.setAccomTypes(Set.of(accomType));

        when(accommodationService.getAllActiveAccomClasses()).thenReturn(List.of(accomClass));

        presenter.getDataForRoomTypeMappings();
        List<AccomClass> initialAccomClasses = presenter.getInitialAccomClasses();
        assertEquals(true, initialAccomClasses.contains(accomClass));
        assertEquals(101, initialAccomClasses.get(0).getId());
        assertEquals("STANDARD", initialAccomClasses.get(0).getName());
    }

    private List<AccomClass> buildAccomClasses() {
        return Arrays.asList(
                buildAccomClass("DELUXE", 0),
                buildAccomClass("UNASSIGNED", null),
                buildAccomClass("STANDARD", -1),
                buildAccomClass("PREMIUM", 1),
                buildAccomClass("SPCSUITE", 4),
                buildAccomClass("DOUBLES", 4),
                buildAccomClass("PREMIUMDBLS", 5),
                buildAccomClass("SUITE", 2),
                buildAccomClass("IMPERIALS", 6)
        );
    }

    private AccomClass buildAccomClass(String name, Integer viewOrder) {
        AccomClass accomClass = new AccomClass();
        accomClass.setName(name);
        accomClass.setViewOrder(viewOrder);
        accomClass.setSystemDefault(viewOrder == null ? 1 : 0);
        return accomClass;
    }

    private List<AccomType> getAccomTypes() {
        List<AccomType> accomTypesList = new ArrayList<>();
        AccomClass accomClass = new AccomClass(1, 1, null);
        AccomType accomType = new AccomType();
        accomType.setAccomClass(accomClass);
        accomType.setIsComponentRoom("N");
        accomTypesList.add(accomType);
        accomType = new AccomType();
        accomType.setAccomClass(accomClass);
        accomType.setIsComponentRoom("N");
        accomTypesList.add(accomType);
        accomClass = new AccomClass(2, 1, null);
        accomType = new AccomType();
        accomType.setAccomClass(accomClass);
        accomType.setIsComponentRoom("N");
        accomTypesList.add(accomType);
        accomClass = new AccomClass(3, 1, null);
        accomType = new AccomType();
        accomType.setAccomClass(accomClass);
        accomType.setIsComponentRoom("Y");
        accomTypesList.add(accomType);
        return accomTypesList;
    }

    @Test
    public void hydrateRoomTypeMappingsView() throws Exception {
        AccomTypeADR accomTypeADR1 = new AccomTypeADR(new AccomType(), BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO);
        AccomTypeADR accomTypeADR2 = new AccomTypeADR(new AccomType(), BigDecimal.ONE, BigDecimal.TEN, BigDecimal.TEN);
        SortedSet<AccomTypeADR> accomTypeADRs = new TreeSet<>(Arrays.asList(accomTypeADR1, accomTypeADR2));
        AccomClass accomClass1 = new AccomClass();
        accomClass1.setViewOrder(1);
        AccomClass accomClass2 = new AccomClass();
        accomClass2.setViewOrder(2);
        SortedSet<AccomClass> accomClasses = getAccomClassesSetForTesting(Arrays.asList(accomClass1, accomClass2));
        FieldUtils.writeField(presenter, "accomClasses", accomClasses, true);
        FieldUtils.writeField(presenter, "accomTypeADRs", accomTypeADRs, true);

        presenter.hydrateRoomTypeMappingsView();

        verify(view).refreshRoomTypeMappingView(accomTypeADRs, accomClasses);
    }

    @Test
    public void hyrdateCostOfWalkView() throws Exception {
        List<CostofWalk> costofWalks = Arrays.asList(new CostofWalk(), new CostofWalk());
        FieldUtils.writeField(presenter, "costOfWalk", costofWalks, true);

        presenter.hydrateCostOfWalkView();

        verify(view).refreshCostOfWalkView(costofWalks);
    }

    @Test
    public void getUnassignedAccomClass() throws Exception {
        AccomClass accomClass1 = new AccomClass();
        accomClass1.setViewOrder(1);
        AccomClass accomClass2 = new AccomClass();
        accomClass2.setSystemDefault(1);
        accomClass2.setViewOrder(2);
        AccomClass accomClass3 = new AccomClass();
        accomClass3.setViewOrder(3);
        SortedSet<AccomClass> accomClasses = getAccomClassesSetForTesting(Arrays.asList(accomClass1, accomClass2, accomClass3));
        FieldUtils.writeField(presenter, "accomClasses", accomClasses, true);

        assertEquals(accomClass2, presenter.getUnassignedAccomClass());
    }

    @Test
    public void getNonSystemDefaultAccomClasses() throws Exception {
        AccomClass accomClass1 = new AccomClass();
        accomClass1.setViewOrder(1);
        AccomClass accomClass2 = new AccomClass();
        accomClass2.setSystemDefault(1);
        accomClass2.setViewOrder(2);
        AccomClass accomClass3 = new AccomClass();
        accomClass3.setViewOrder(3);
        SortedSet<AccomClass> accomClasses = getAccomClassesSetForTesting(Arrays.asList(accomClass1, accomClass2, accomClass3));
        FieldUtils.writeField(presenter, "accomClasses", accomClasses, true);

        List<AccomClass> expectedResult = Arrays.asList(accomClass1, accomClass3);

        assertEquals(expectedResult, presenter.getNonSystemDefaultAccomClasses());
    }

    @Test
    public void saveRoomClasses_valid() throws Exception {
        SortedSet<AccomClass> accomClasses = setUpSaveRoomClasses();

        //This is done so that if we do try get gather text it will fail
        when(lang.getText(anyString())).thenReturn("message");
        presenter.setAgileRatesEnabled(false);
        presenter.setEnableLrvDropRestrictionEnabled(true);
        presenter.setGroupFinalForecastOverrideEnabled(true);

        boolean result = presenter.saveRoomClasses(true);

        assertTrue(result);
        assertFalse(presenter.hasChanges(RoomConfigurationSelection.ROOM_TYPE_MAPPING));
        assertFalse(presenter.hasChanges(RoomConfigurationSelection.MASTER_CLASS_MAPPING));
        verify(lrvDropRestrictionService).syncLrvDropRestrictionsWithAccomClassChange();
        verify(accommodationService).updateAccomClasses(new ArrayList<>(accomClasses), new ArrayList<>(), new ArrayList<>());
        verify(presenter).showSaveSuccessMessage();
        verify(presenter).sync();
        verify(agileRatesConfigurationService, never()).changedRoomClassConfig(any());
    }

    @Test
    public void saveRoomClasses_valid_agileRatesEnabled_NoRoomClassChanges() throws Exception {
        SortedSet<AccomClass> accomClasses = setUpSaveRoomClasses();

        //This is done so that if we do try get gather text it will fail
        when(lang.getText(anyString())).thenReturn("message");
        presenter.setAgileRatesEnabled(true);
        presenter.setEnableLrvDropRestrictionEnabled(true);
        presenter.setGroupFinalForecastOverrideEnabled(true);
        presenter.setHasRoomClassConfigChanged(false);

        boolean result = presenter.saveRoomClasses(true);

        assertTrue(result);
        assertFalse(presenter.hasChanges(RoomConfigurationSelection.ROOM_TYPE_MAPPING));
        assertFalse(presenter.hasChanges(RoomConfigurationSelection.MASTER_CLASS_MAPPING));
        verify(lrvDropRestrictionService).syncLrvDropRestrictionsWithAccomClassChange();
        verify(accommodationService).updateAccomClasses(new ArrayList<>(accomClasses), new ArrayList<>(), new ArrayList<>());
        verify(presenter).showSaveSuccessMessage();
        verify(presenter).sync();
        verify(agileRatesConfigurationService, never()).changedRoomClassConfig(any());
    }

    @Test
    public void saveRoomClasses_valid_agileRatesEnabled_HasRoomClassChanges() throws Exception {
        SortedSet<AccomClass> accomClasses = setUpSaveRoomClasses();

        //This is done so that if we do try get gather text it will fail
        when(lang.getText(anyString())).thenReturn("message");
        presenter.setAgileRatesEnabled(true);
        presenter.setEnableLrvDropRestrictionEnabled(true);
        presenter.setGroupFinalForecastOverrideEnabled(true);
        presenter.setHasRoomClassConfigChanged(true);

        boolean result = presenter.saveRoomClasses(true);

        assertTrue(result);
        assertFalse(presenter.hasChanges(RoomConfigurationSelection.ROOM_TYPE_MAPPING));
        assertFalse(presenter.hasChanges(RoomConfigurationSelection.MASTER_CLASS_MAPPING));
        verify(lrvDropRestrictionService).syncLrvDropRestrictionsWithAccomClassChange();
        verify(accommodationService).updateAccomClasses(new ArrayList<>(accomClasses), new ArrayList<>(), new ArrayList<>());
        verify(presenter).showSaveSuccessMessage();
        verify(presenter).sync();
        verify(agileRatesConfigurationService, times(1)).changedRoomClassConfig(any());
    }

    @Test
    public void saveRoomClasses_valid_agileRatesEnabled_HasRoomClassChanges_deleteOverridesToggleON() throws Exception {
        SortedSet<AccomClass> accomClasses = setUpSaveRoomClasses();

        //This is done so that if we do try get gather text it will fail
        when(lang.getText(anyString())).thenReturn("message");
        presenter.setAgileRatesEnabled(true);
        presenter.setEnableLrvDropRestrictionEnabled(true);
        presenter.setGroupFinalForecastOverrideEnabled(true);
        presenter.setHasRoomClassConfigChanged(true);

        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.DELETE_LINKED_PRODUCT_OVERRIDES_ON_ROOMS_CONFIGURATION_CHANGE)).thenReturn(true);

        boolean result = presenter.saveRoomClasses(true);

        assertTrue(result);
        assertFalse(presenter.hasChanges(RoomConfigurationSelection.ROOM_TYPE_MAPPING));
        assertFalse(presenter.hasChanges(RoomConfigurationSelection.MASTER_CLASS_MAPPING));
        verify(lrvDropRestrictionService).syncLrvDropRestrictionsWithAccomClassChange();
        verify(accommodationService).updateAccomClasses(new ArrayList<>(accomClasses), new ArrayList<>(), new ArrayList<>());
        verify(presenter).showSaveSuccessMessage();
        verify(presenter).sync();
        verify(agileRatesConfigurationService, times(1)).changedRoomClassConfig(any(), anySet(), anySet());
    }

    private SortedSet<AccomClass> setUpSaveRoomClasses() throws IllegalAccessException {
        presenter = spy(presenter);
        AccomClass accomClass1 = new AccomClass();
        accomClass1.setViewOrder(1);
        accomClass1.setMasterClassBoolean(true);
        AccomType accomType = new AccomType();
        accomType.setAccomTypeCapacity(100);
        accomType.setAccomClass(accomClass1);
        accomClass1.addAccomType(accomType);
        AccomClass accomClass2 = new AccomClass();
        accomClass2.setSystemDefault(1);
        accomClass2.setViewOrder(2);
        accomClass2.addAccomType(accomType);
        AccomClass accomClass3 = new AccomClass();
        accomClass3.setViewOrder(3);
        accomClass3.addAccomType(accomType);

        AccomTypeADR accomTypeADR = new AccomTypeADR(accomType, BigDecimal.ONE, BigDecimal.TEN, BigDecimal.TEN);
        SortedSet<AccomTypeADR> accomTypeADRs = new TreeSet<>(Collections.singletonList(accomTypeADR));
        FieldUtils.writeField(presenter, "accomTypeADRs", accomTypeADRs, true);

        SortedSet<AccomClass> accomClasses = getAccomClassesSetForTesting(Arrays.asList(accomClass1, accomClass2, accomClass3));
        FieldUtils.writeField(presenter, "accomClasses", accomClasses, true);
        doNothing().when(presenter).sync();
        return accomClasses;
    }

    @Test
    public void saveRoomClasses_skip_validation() throws Exception {
        presenter = spy(presenter);
        doNothing().when(presenter).sync();
        AccomClass accomClass1 = new AccomClass();
        accomClass1.setViewOrder(1);
        accomClass1.setMasterClassBoolean(true);

        SortedSet<AccomClass> accomClasses = getAccomClassesSetForTesting(Collections.singletonList(accomClass1));
        FieldUtils.writeField(presenter, "accomClasses", accomClasses, true);

        //This is done so that if we do try get gather text it will fail
        when(lang.getText(anyString())).thenReturn("message");

        boolean result = presenter.saveRoomClasses(false);

        assertTrue(result);
        verify(accommodationService).updateAccomClasses(new ArrayList<>(accomClasses), new ArrayList<>(), new ArrayList<>());
        verify(presenter).showSaveSuccessMessage();
        verify(presenter).sync();
        verify(agileRatesConfigurationService, never()).changedRoomClassConfig(any());
    }

    @Test
    public void saveRoomClasses_no_master_class() throws Exception {
        presenter = spy(presenter);
        doNothing().when(presenter).sync();
        AccomClass accomClass1 = new AccomClass();
        accomClass1.setViewOrder(1);
        accomClass1.setMasterClassBoolean(false);
        AccomClass accomClass2 = new AccomClass();
        accomClass2.setSystemDefault(1);
        accomClass2.setViewOrder(2);
        AccomClass accomClass3 = new AccomClass();
        accomClass3.setViewOrder(3);

        AccomType accomType = new AccomType();
        accomType.setAccomClass(accomClass1);
        AccomTypeADR accomTypeADR = new AccomTypeADR(accomType, BigDecimal.ONE, BigDecimal.TEN, BigDecimal.TEN);
        SortedSet<AccomTypeADR> accomTypeADRs = new TreeSet<>(Collections.singletonList(accomTypeADR));
        FieldUtils.writeField(presenter, "accomTypeADRs", accomTypeADRs, true);

        //Fill Accom Classes just to get to this validation failure
        accomClass1.addAccomType(accomType);
        accomClass2.addAccomType(accomType);
        accomClass3.addAccomType(accomType);

        SortedSet<AccomClass> accomClasses = getAccomClassesSetForTesting(Arrays.asList(accomClass1, accomClass2, accomClass3));
        FieldUtils.writeField(presenter, "accomClasses", accomClasses, true);
        when(lang.getText(anyString())).thenReturn("message");

        boolean result = presenter.saveRoomClasses(true);

        assertFalse(result);
        verify(lang).getText("configureRoomClassAdMasterClassAlertMessage");
        verify(presenter).showWarning("message");
        verify(accommodationService, Mockito.never()).updateAccomClasses(new ArrayList<>(accomClasses));
        verify(presenter, Mockito.never()).sync();
    }

    @Test
    public void saveRoomClasses_no_capacity_butHasHospitalityRoom() throws Exception {
        when(hospitalityRoomsService.getAllTypesOfHospitalityRooms()).thenReturn(Arrays.asList("test"));
        SortedSet<AccomClass> accomClasses = createAccomClasses();
        boolean result = presenter.saveRoomClasses(true);

        assertTrue(result);
        verify(lang, Mockito.never()).getText("roomClassWithZeroCapacity");
        verify(presenter, Mockito.never()).showWarning("message");
        verify(accommodationService).updateAccomClasses(eq(new ArrayList<>(accomClasses)), anyList(), anyList());
        verify(presenter).sync();
        verify(agileRatesConfigurationService, never()).changedRoomClassConfig(any());
    }

    @Test
    public void saveRoomClasses_no_capacity() throws Exception {
        SortedSet<AccomClass> accomClasses = createAccomClasses();
        boolean result = presenter.saveRoomClasses(true);

        assertFalse(result);
        verify(lang).getText("roomClassWithZeroCapacity");
        verify(presenter).showWarning("message");
        verify(accommodationService, Mockito.never()).updateAccomClasses(new ArrayList<>(accomClasses));
        verify(presenter, Mockito.never()).sync();
    }

    private SortedSet<AccomClass> createAccomClasses() throws IllegalAccessException {
        presenter = spy(presenter);
        doNothing().when(presenter).sync();
        AccomClass accomClass1 = new AccomClass();
        accomClass1.setViewOrder(1);
        accomClass1.setMasterClassBoolean(true);
        AccomType accomType = new AccomType();
        accomType.setAccomTypeCode("test");
        accomType.setAccomTypeCapacity(0);
        accomType.setAccomClass(accomClass1);
        accomClass1.addAccomType(accomType);
        AccomClass accomClass2 = new AccomClass();
        accomClass2.setSystemDefault(1);
        accomClass2.setViewOrder(2);
        AccomClass accomClass3 = new AccomClass();
        accomClass3.setViewOrder(3);

        AccomTypeADR accomTypeADR = new AccomTypeADR(accomType, BigDecimal.ONE, BigDecimal.TEN, BigDecimal.TEN);
        SortedSet<AccomTypeADR> accomTypeADRs = new TreeSet<>(Collections.singletonList(accomTypeADR));
        FieldUtils.writeField(presenter, "accomTypeADRs", accomTypeADRs, true);

        //Fill Accom Classes just to get to this validation failure
        accomClass1.addAccomType(accomType);
        accomClass2.addAccomType(accomType);
        accomClass3.addAccomType(accomType);

        SortedSet<AccomClass> accomClasses = getAccomClassesSetForTesting(Arrays.asList(accomClass1, accomClass2, accomClass3));
        FieldUtils.writeField(presenter, "accomClasses", accomClasses, true);
        when(lang.getText(anyString())).thenReturn("message");
        return accomClasses;
    }

    @Test
    public void saveRoomClasses_unmapped_room_type_previously_unassigned_Capacity_Zero() throws Exception {
        presenter = spy(presenter);
        doNothing().when(presenter).sync();
        AccomClass defaultRoomClass = new AccomClass();
        defaultRoomClass.setViewOrder(1);
        defaultRoomClass.setSystemDefault(1);
        defaultRoomClass.setMasterClass(1);
        AccomType accomType = new AccomType();
        accomType.setAccomClass(defaultRoomClass);
        accomType.setAccomTypeCapacity(0);
        defaultRoomClass.addAccomType(accomType);

        AccomTypeADR accomTypeADR = new AccomTypeADR(accomType, BigDecimal.ONE, BigDecimal.TEN, BigDecimal.TEN);
        SortedSet<AccomTypeADR> accomTypeADRs = new TreeSet<>(Collections.singletonList(accomTypeADR));
        FieldUtils.writeField(presenter, "accomTypeADRs", accomTypeADRs, true);

        List<AccomType> accomTypeList = new ArrayList<>(Collections.singletonList(accomType));
        when(accommodationService.getAllUnassignedAccomTypes()).thenReturn(accomTypeList);

        SortedSet<AccomClass> accomClasses = getAccomClassesSetForTesting(Collections.singletonList(defaultRoomClass));
        FieldUtils.writeField(presenter, "accomClasses", accomClasses, true);
        when(lang.getText(anyString())).thenReturn("message");

        boolean result = presenter.saveRoomClasses(true);

        assertTrue(result);
        verify(accommodationService).updateAccomClasses(new ArrayList<>(accomClasses), new ArrayList<>(), new ArrayList<>());
        verify(presenter).showSaveSuccessMessage();
        verify(presenter).sync();
        verify(agileRatesConfigurationService, never()).changedRoomClassConfig(any());
    }

    @Test
    public void saveRoomClasses_unmapped_room_type_previously_unassigned_Capacity_One() throws Exception {
        presenter = spy(presenter);
        doNothing().when(presenter).sync();
        AccomClass defaultRoomClass = new AccomClass();
        defaultRoomClass.setViewOrder(1);
        defaultRoomClass.setSystemDefault(1);
        AccomType accomType = new AccomType();
        accomType.setAccomClass(defaultRoomClass);
        accomType.setAccomTypeCapacity(1);
        defaultRoomClass.addAccomType(accomType);

        AccomTypeADR accomTypeADR = new AccomTypeADR(accomType, BigDecimal.ONE, BigDecimal.TEN, BigDecimal.TEN);
        SortedSet<AccomTypeADR> accomTypeADRs = new TreeSet<>(Collections.singletonList(accomTypeADR));
        FieldUtils.writeField(presenter, "accomTypeADRs", accomTypeADRs, true);

        List<AccomType> accomTypeList = new ArrayList<>(Collections.singletonList(accomType));
        when(accommodationService.getAllUnassignedAccomTypes()).thenReturn(accomTypeList);

        SortedSet<AccomClass> accomClasses = getAccomClassesSetForTesting(Collections.singletonList(defaultRoomClass));
        FieldUtils.writeField(presenter, "accomClasses", accomClasses, true);
        when(lang.getText(anyString())).thenReturn("message");

        boolean result = presenter.saveRoomClasses(true);

        assertFalse(result);
        verify(lang).getText("mapAllRoomType");
        verify(presenter).showWarning("message");
        verify(accommodationService, Mockito.never()).updateAccomClasses(new ArrayList<>(accomClasses));
        verify(presenter, Mockito.never()).sync();
    }

    @Test
    public void saveRoomClasses_unmapped_room_type_previously_assigned() throws Exception {
        presenter = spy(presenter);
        doNothing().when(presenter).sync();
        AccomClass defaultRoomClass = new AccomClass();
        defaultRoomClass.setViewOrder(1);
        defaultRoomClass.setSystemDefault(1);
        AccomType accomType = new AccomType();
        accomType.setAccomClass(defaultRoomClass);
        defaultRoomClass.addAccomType(accomType);

        AccomTypeADR accomTypeADR = new AccomTypeADR(accomType, BigDecimal.ONE, BigDecimal.TEN, BigDecimal.TEN);
        SortedSet<AccomTypeADR> accomTypeADRs = new TreeSet<>(Collections.singletonList(accomTypeADR));
        FieldUtils.writeField(presenter, "accomTypeADRs", accomTypeADRs, true);

        when(accommodationService.getAllUnassignedAccomTypes()).thenReturn(new ArrayList<AccomType>());

        SortedSet<AccomClass> accomClasses = getAccomClassesSetForTesting(Collections.singletonList(defaultRoomClass));
        FieldUtils.writeField(presenter, "accomClasses", accomClasses, true);
        when(lang.getText(anyString())).thenReturn("message");

        boolean result = presenter.saveRoomClasses(true);

        assertFalse(result);
        verify(lang).getText("mapAllRoomType");
        verify(presenter).showWarning("message");
        verify(accommodationService, Mockito.never()).updateAccomClasses(new ArrayList<>(accomClasses));
        verify(presenter, Mockito.never()).sync();
    }

    @Test
    public void saveRoomClasses_empty_room_class() throws Exception {
        presenter = spy(presenter);
        doNothing().when(presenter).sync();
        AccomClass emptyRoomClass = new AccomClass();
        emptyRoomClass.setViewOrder(1);
        AccomType accomType = new AccomType();
        accomType.setAccomClass(emptyRoomClass);

        AccomTypeADR accomTypeADR = new AccomTypeADR(accomType, BigDecimal.ONE, BigDecimal.TEN, BigDecimal.TEN);
        SortedSet<AccomTypeADR> accomTypeADRs = new TreeSet<>(Collections.singletonList(accomTypeADR));
        FieldUtils.writeField(presenter, "accomTypeADRs", accomTypeADRs, true);

        SortedSet<AccomClass> accomClasses = getAccomClassesSetForTesting(Collections.singletonList(emptyRoomClass));
        FieldUtils.writeField(presenter, "accomClasses", accomClasses, true);
        when(lang.getText(anyString())).thenReturn("message");

        boolean result = presenter.saveRoomClasses(true);

        assertFalse(result);
        verify(lang).getText("emptyRoomClass");
        verify(presenter).showWarning("message");
        verify(accommodationService, Mockito.never()).updateAccomClasses(new ArrayList<>(accomClasses));
        verify(presenter, Mockito.never()).sync();
    }

    private Set<String> addToSet(String... values) {
        Set<String> set = new HashSet<>();
        for (String s : values) {
            set.add(s);
        }
        return set;
    }

    private Map<String, Set<String>> getInvalidUpgradesMap() {
        Map<String, Set<String>> map = new HashMap<>();
        map.put("B0VD", addToSet("B2VKD", "B3VKD"));
        map.put("B1VKS", addToSet("B2VKD", "B3VKD"));
        map.put("B0SK", addToSet("B3VKD"));
        map.put("B2VKD", addToSet("B0VD", "B1VKS", "B3VKD"));
        map.put("B3VKD", addToSet("B2VKD", "B0VD", "B1VKS", "B0SK"));
        map.put("B2VKK", addToSet("B0VK", "B1VKK"));
        map.put("B0VK", addToSet("B2VKK"));
        map.put("B1VKK", addToSet("B2VKK"));

        return map;
    }

    @Test
    public void saveRoomClasses_accomClassesMarkedForDeletion() throws Exception {
        presenter = spy(presenter);

        AccomClass defaultAccomClass = new AccomClass();
        defaultAccomClass.setId(1);
        defaultAccomClass.setSystemDefault(1);
        AccomType accomType1 = new AccomType();
        accomType1.setName("roomType1");
        AccomClass roomClass1 = createRoomClass(accomType1, 11);
        roomClass1.setRankOrder(1);
        roomClass1.setViewOrder(1);
        AccomType accomType2 = new AccomType();
        accomType2.setName("roomType2");
        AccomClass roomClassMarkedForDeletion = createRoomClass(accomType2, 22);
        roomClassMarkedForDeletion.setRankOrder(2);
        roomClassMarkedForDeletion.setViewOrder(2);
        AccomType accomType3 = new AccomType();
        accomType3.setName("roomType3");
        AccomClass roomClass3 = createRoomClass(accomType3, 33);
        roomClass3.setRankOrder(3);
        roomClass3.setViewOrder(3);
        FieldUtils.writeField(presenter, "accomClasses", getAccomClassesSetForTesting(Arrays.asList(roomClass1, roomClassMarkedForDeletion, roomClass3, defaultAccomClass)), true);
        AccomTypeADR accomTypeADR1 = new AccomTypeADR(accomType1, BigDecimal.ONE, BigDecimal.TEN, BigDecimal.TEN);
        AccomTypeADR accomTypeADR2 = new AccomTypeADR(accomType2, BigDecimal.ONE, BigDecimal.TEN, BigDecimal.TEN);
        AccomTypeADR accomTypeADR3 = new AccomTypeADR(accomType3, BigDecimal.ONE, BigDecimal.TEN, BigDecimal.TEN);
        SortedSet<AccomTypeADR> accomTypeADRs = new TreeSet<>(Arrays.asList(accomTypeADR1, accomTypeADR2, accomTypeADR3));
        FieldUtils.writeField(presenter, "accomTypeADRs", accomTypeADRs, true);

        doNothing().when(presenter).sync();

        when(accommodationService.getAccomClassById(22)).thenReturn(roomClassMarkedForDeletion);

        presenter.updateAccomClassesMarkedForDeletion(roomClassMarkedForDeletion);

        boolean result = presenter.saveRoomClasses(true);

        assertTrue(result);
        verify(presenter).showSaveSuccessMessage();
        verify(presenter).sync();
        verify(agileRatesConfigurationService, never()).changedRoomClassConfig(any());
        verify(invalidateOverridesService).invalidateOverrides(anyList());
        verify(accommodationMappingService).deleteWebrateOverrideCompetitors(anyList());
        verify(accommodationService).deletePricingConfigurations(Collections.singletonList(accomType2));
        verify(accommodationService).deleteAccomClass(22);
        assertTrue(presenter.getAccomClasses().stream().noneMatch(roomClass -> Integer.valueOf(22).equals(roomClass.getId())));
        assertEquals(defaultAccomClass, accomType2.getAccomClass());
        assertEquals(Integer.valueOf(1), roomClass1.getRankOrder());
        assertEquals(Integer.valueOf(2), roomClass3.getRankOrder());
    }

    private AccomClass createRoomClass(AccomType roomType, int id) {
        AccomClass roomClass = new AccomClass();
        roomClass.setId(id);
        roomClass.setViewOrder(1);
        roomClass.addAccomType(roomType);
        roomClass.setSystemDefault(0);
        return roomClass;
    }

    @Test
    public void validateComponentRoomsConfigurationTest() {
        presenter.accomClasses = createAccomClass("B0VK", "B2VKK");
        validateAccomClassesCreated(false);

        presenter.accomClasses = createAccomClass("B1VKK", "B2VKK");
        validateAccomClassesCreated(false);

        presenter.accomClasses = createAccomClass("B2VKD", "B0VD");
        validateAccomClassesCreated(false);

        presenter.accomClasses = createAccomClass("B2VKD", "B1VKS");
        validateAccomClassesCreated(false);

        presenter.accomClasses = createAccomClass("B2VKD", "B3VKD");
        validateAccomClassesCreated(false);

        presenter.accomClasses = createAccomClass("B2VKD", "B0SK");
        validateAccomClassesCreated(true);

        presenter.accomClasses = createAccomClass("B0VD", "B0SK", "B1VKS");
        validateAccomClassesCreated(true);

        presenter.accomClasses = createAccomClass("B3VKD", "B2VKK");
        validateAccomClassesCreated(true);

        presenter.accomClasses = createAccomClass("B2VKD", "B2VKK");
        validateAccomClassesCreated(true);

    }

    private void setAsComponentRoom(String cr) {
        presenter.accomClasses.first().getAccomTypes().stream()
                .filter(at -> cr.equals(at.getAccomTypeCode()))
                .forEach(at -> at.setIsComponentRoom("Y"));
    }

    private String setUpDataAndExecute(boolean setAsSumOfParts, String... accomTypes) {
        presenter.setSumOfPartsEnabled(true);
        presenter.accomClasses = createAccomClass(accomTypes);
        setAsComponentRoom("CR1");
        when(pricingConfigurationService.getPricingAccomClasses()).thenReturn(getPricingAccomClasses(setAsSumOfParts));
        when(hospitalityRoomsService.getHospitalityRoomsConfigWithoutPseudoRooms()).thenReturn(getHospitalityRoomsConfig());
        when(lang.getText(eq("can.not.move.rt.to.rc.as.it.is.sumOfParts"), anyString(), anyString(), anyString())).thenReturn("RT Can not move to RC as it is configured as Sum of Parts");
        return presenter.validateComponentRoomsConfiguration();
    }

    @Test
    public void validateCompRoomConfigurationForAccomClassHavingSumOfParts_HappyPath() {
        String msg = setUpDataAndExecute(true, "SH1K", "CR1");
        verify(lang, times(0)).getText(eq("can.not.move.rt.to.rc.as.it.is.sumOfParts"), anyString(), anyString(), anyString());
        assertNull(msg);

        msg = setUpDataAndExecute(false, "SH1K", "CR1");
        assertNull(msg);
    }

    @Test
    public void validateCompRoomConfigurationForAccomClassHavingSumOfParts_withCrAndPhysicalRoom() {
        String msg = setUpDataAndExecute(true, "CR1", "P2");
        verify(lang).getText(eq("can.not.move.rt.to.rc.as.it.is.sumOfParts"), anyString(), anyString(), anyString());
        assertEquals("RT Can not move to RC as it is configured as Sum of Parts", msg);

        msg = setUpDataAndExecute(false, "CR1", "P2");
        assertNull(msg);
    }

    @Test
    public void validateCompRoomConfigurationForAccomClassHavingSumOfParts_withHrAndPhysicalRoom() {
        String msg = setUpDataAndExecute(true, "SH1K", "P2");
        verify(lang).getText(eq("can.not.move.rt.to.rc.as.it.is.sumOfParts"), anyString(), anyString(), anyString());
        assertEquals("RT Can not move to RC as it is configured as Sum of Parts", msg);

        msg = setUpDataAndExecute(false, "SH1K", "P2");
        assertNull(msg);
    }

    @Test
    public void saveRoomClasses_And_ProductAccomType() throws Exception {
        SortedSet<AccomClass> accomClasses = setUpSaveRoomClasses();

        //This is done so that if we do try get gather text it will fail
        when(lang.getText(anyString())).thenReturn("message");
        presenter.setAgileRatesEnabled(false);
        presenter.setEnableLrvDropRestrictionEnabled(true);
        presenter.setGroupFinalForecastOverrideEnabled(true);

        presenter.saveProductAccomTypes(createProductAccomType());

        verify(accommodationService, times(1)).saveProductAccomTypes(any());
    }

    public List<ProductAccomType> createProductAccomType() {
        ProductAccomType productAccomType = new ProductAccomType();
        productAccomType.setAccomType(createAccomType(1, "STD"));
        productAccomType.setProduct(createProduct());

        ProductAccomType productAccomType2 = new ProductAccomType();
        productAccomType2.setAccomType(createAccomType(2, "STT"));
        productAccomType2.setProduct(createProduct());

        return Arrays.asList(productAccomType, productAccomType2);
    }

    private AccomType createAccomType(Integer accomTypeId, String accomTypeName) {
        AccomType accomType = new AccomType();
        accomType.setId(1);
        accomType.setName(accomTypeName);
        return accomType;
    }

    private Product createProduct() {
        Product product = new Product();
        product.setId(1);
        product.setName("Product A");
        return product;
    }

    private List<PricingAccomClass> getPricingAccomClasses(boolean isSumOfParts) {
        List<PricingAccomClass> pricingAccomClasses = new ArrayList<>();
        PricingAccomClass pricingAccomClass1 = new PricingAccomClass();
        pricingAccomClass1.setAccomClass(presenter.accomClasses.first());
        pricingAccomClass1.setPriceAsSumOfParts(isSumOfParts);
        pricingAccomClasses.add(pricingAccomClass1);
        return pricingAccomClasses;
    }

    private void validateAccomClassesCreated(boolean isValidScenario) {
        presenter.setComponentRoomsEnabled(true);
        List<ComponentRoomsConfiguration> componentRoomsConfigurations = getConfigurationAsTree();
        when(componentRoomService.getComponentRoomsConfigurations()).thenReturn(componentRoomsConfigurations);
        when(lang.getText(anyString(), anyString(), anyString())).thenReturn("can not be part of same room class");
        String msg = presenter.validateComponentRoomsConfiguration();
        if (isValidScenario) {
            assertEquals(null, msg);
        } else {
            assertTrue(msg.contains("can not be part of same room class"));
        }
    }

    private SortedSet<AccomClass> createAccomClass(String... accomTypes) {
        SortedSet<AccomClass> acSet = new TreeSet<AccomClass>();
        AccomClass ac = new AccomClass();
        ac.setCode("AC");
        for (String str : accomTypes) {
            AccomType at = new AccomType();
            at.setAccomTypeCode(str);
            at.setAccomTypeCapacity(5);
            ac.addAccomType(at);
        }
        acSet.add(ac);
        return acSet;
    }

    @Test
    public void saveRoomClasses_ComponentRoom_PhysicalRoom_together_when_ComponentRoom_disabled() throws Exception {
        presenter = spy(presenter);
        doNothing().when(presenter).sync();
        doReturn(false).when(presenter).isComponentRoomsEnabled();
        AccomClass accomClass1 = getAccomClassWithComponentAndPhysicalRoom();

        AccomTypeADR accomTypeADR = new AccomTypeADR(accomClass1.getAccomTypes().iterator().next(), BigDecimal.ONE, BigDecimal.TEN, BigDecimal.TEN);
        SortedSet<AccomTypeADR> accomTypeADRs = new TreeSet<>(Collections.singletonList(accomTypeADR));
        FieldUtils.writeField(presenter, "accomTypeADRs", accomTypeADRs, true);
        SortedSet<AccomClass> accomClasses = getAccomClassesSetForTesting(Arrays.asList(accomClass1));
        FieldUtils.writeField(presenter, "accomClasses", accomClasses, true);

        boolean result = presenter.saveRoomClasses(true);

        assertTrue(result);
        verify(accommodationService).updateAccomClasses(new ArrayList<>(accomClasses), new ArrayList<>(), new ArrayList<>());
        verify(presenter).sync();
        verify(agileRatesConfigurationService, never()).changedRoomClassConfig(any());
    }

    @Test
    public void isUsingNewLoadFlowEnabledTest() {
        assertTrue(presenter.isUsingNewLoadFlow());
    }

    @Test
    public void isUsingNewLoadFlowDisabledTest() {
        System.setProperty("pacman.feature.priceRankingAndUpgradeViewLoadChange", "false");
        assertFalse(presenter.isUsingNewLoadFlow());
    }

    private AccomClass getAccomClassWithComponentRooms() {
        AccomClass accomClass1 = new AccomClass();
        accomClass1.setViewOrder(1);
        accomClass1.setMasterClassBoolean(true);
        AccomType accomType = new AccomType();
        accomType.setAccomTypeCapacity(3);
        accomType.setAccomClass(accomClass1);
        accomType.setIsComponentRoom("Y");
        accomType.setId(11);
        accomClass1.addAccomType(accomType);
        AccomType accomType1 = new AccomType();
        accomType1.setAccomTypeCapacity(3);
        accomType1.setAccomClass(accomClass1);
        accomType1.setIsComponentRoom("Y");
        accomType1.setId(12);
        accomClass1.addAccomType(accomType1);
        return accomClass1;
    }

    private AccomClass getAccomClassWithComponentAndPhysicalRoom() {
        AccomClass accomClass1 = new AccomClass();
        accomClass1.setViewOrder(1);
        accomClass1.setMasterClassBoolean(true);
        AccomType accomType = new AccomType();
        accomType.setAccomTypeCapacity(3);
        accomType.setAccomClass(accomClass1);
        accomType.setIsComponentRoom("Y");
        accomClass1.addAccomType(accomType);
        AccomType accomType1 = new AccomType();
        accomType1.setAccomTypeCapacity(3);
        accomType1.setAccomClass(accomClass1);
        accomType1.setIsComponentRoom("N");
        accomClass1.addAccomType(accomType1);
        return accomClass1;
    }

    @Test
    public void addRoomClass() throws Exception {
        AccomClass accomClass = new AccomClass();
        presenter.addRoomClass(accomClass);

        verify(accommodationService).persistAccomClassWithViewOrder(accomClass);
    }

    @Test
    public void addRestrictionForRoomClassesWithNoLrvDropRestrictions() {
        AccomClass accomClass = new AccomClass();
        accomClass.setId(6);
        presenter.syncLrvDropRestrictionsWithAccomClassChange();
        verify(lrvDropRestrictionService).syncLrvDropRestrictionsWithAccomClassChange();
    }

    @Test
    public void deleteRoomClass_withRoomTypes() throws Exception {
        AccomClass defaultAccomClass = new AccomClass();
        defaultAccomClass.setSystemDefault(1);
        AccomType accomType = new AccomType();
        AccomClass accomClass = Mockito.mock(AccomClass.class);
        when(accomClass.getId()).thenReturn(123);
        when(accomClass.getViewOrder()).thenReturn(1);
        Set<AccomType> accomTypesSet = new HashSet<>();
        accomTypesSet.add(accomType);
        when(accomClass.getAccomTypes()).thenReturn(accomTypesSet);
        accomType.setAccomClass(accomClass);

        FieldUtils.writeField(presenter, "accomClasses", getAccomClassesSetForTesting(Arrays.asList(accomClass, defaultAccomClass)), true);
        AccomTypeADR accomTypeADR = new AccomTypeADR(accomType, BigDecimal.ONE, BigDecimal.TEN, BigDecimal.TEN);
        SortedSet<AccomTypeADR> accomTypeADRs = new TreeSet<>(Collections.singletonList(accomTypeADR));
        FieldUtils.writeField(presenter, "accomTypeADRs", accomTypeADRs, true);
        List<AccomType> accomTypes = new ArrayList<>();
        accomTypes.add(accomType);

        when(accommodationService.getAccomClassById(123)).thenReturn(accomClass);

        presenter.deleteRoomClass(accomClass);

        verify(accomClass).setStatusId(Constants.INACTIVE_STATUS_ID);
        verify(accommodationService).deletePricingConfigurations(accomTypes);
        verify(accommodationService).deleteAccomClass(123);
        assertFalse(presenter.getAccomClasses().contains(accomClass));
        assertEquals(defaultAccomClass, accomType.getAccomClass());
    }

    @Test
    public void deleteRoomClass_withoutRoomTypes() throws Exception {
        AccomClass defaultAccomClass = new AccomClass();
        defaultAccomClass.setSystemDefault(1);
        AccomType accomType = new AccomType();
        AccomClass accomClass = new AccomClass();
        accomClass.setId(123);
        accomClass.setViewOrder(1);
        accomClass.setName("1");
        AccomClass accomClass2 = Mockito.mock(AccomClass.class);
        when(accomClass2.getId()).thenReturn(123);
        when(accomClass2.getViewOrder()).thenReturn(1);
        when(accomClass2.getName()).thenReturn("2");
        Set<AccomType> accomTypeSet = new HashSet<>();
        accomTypeSet.add(accomType);
        when(accomClass2.getAccomTypes()).thenReturn(accomTypeSet);
        accomType.setAccomClass(accomClass2);

        FieldUtils.writeField(presenter, "accomClasses", getAccomClassesSetForTesting(Arrays.asList(accomClass, defaultAccomClass)), true);
        AccomTypeADR accomTypeADR = new AccomTypeADR(accomType, BigDecimal.ONE, BigDecimal.TEN, BigDecimal.TEN);
        SortedSet<AccomTypeADR> accomTypeADRs = new TreeSet<>(Collections.singletonList(accomTypeADR));
        FieldUtils.writeField(presenter, "accomTypeADRs", accomTypeADRs, true);
        List<AccomType> accomTypes = new ArrayList<>();
        accomTypes.add(accomType);

        when(accommodationService.getAccomClassById(123)).thenReturn(accomClass2);

        presenter.deleteRoomClass(accomClass);

        verify(accomClass2).setStatusId(Constants.INACTIVE_STATUS_ID);
        verify(accommodationService).deletePricingConfigurations(accomTypes);
        verify(accommodationService).deleteAccomClass(123);
        assertFalse(presenter.getAccomClasses().contains(accomClass));
    }

    @Test
    void shouldDeletePricingConfigurationForRoomClassAndRoomTypeWhichAreMovedAndRenamed() {
        AccomClass initialAccomClass = new AccomClass();
        initialAccomClass.setId(10);

        AccomClass accomClass = new AccomClass();
        accomClass.setId(10);
        accomClass.setAccomTypes(Collections.emptySet());

        AccomType accomType = new AccomType();
        accomType.setId(101);
        accomType.setAccomClass(initialAccomClass);

        initialAccomClass.setAccomTypes(Set.of(accomType));

        presenter.setInitialAccomClasses(List.of(initialAccomClass));

        when(accommodationService.getAccomClassById(10)).thenReturn(accomClass);

        presenter.deleteRoomClass(accomClass);

        var accomTypes = Set.of(accomType);
        verify(accommodationService, times(0)).deletePricingConfigurations(List.of(accomType));
        verify(pricingConfigurationService).deletePricingAccomClass(initialAccomClass);
        verify(pricingConfigurationService).deleteTransientPricingConfigurationsByAccomTypes(accomTypes);
        verify(pricingConfigurationService).deleteGroupPricingConfigurationsByAccomTypes(accomTypes);
        verify(pricingConfigurationService).deletePricingOffsetAccomTypesByAccomTypes(accomTypes);
        verify(pricingConfigurationService).deletePricingConfigurationSupplementsForAccomTypes(accomTypes);
    }

    @Test
    void shouldDeletePricingConfigurationForRoomClassAndRoomTypeIsMovedAndRoomClassIsDeleted() {
        AccomClass accomClass = new AccomClass();
        accomClass.setId(10);

        AccomType accomType = new AccomType();
        accomType.setId(101);
        accomType.setAccomClass(accomClass);

        accomClass.setAccomTypes(Set.of(accomType));

       presenter.setInitialAccomClasses(List.of(accomClass));

        when(accommodationService.getAccomClassById(10)).thenReturn(accomClass);

        presenter.deleteRoomClass(accomClass);

        var accomTypes = Set.of(accomType);
        verify(accommodationService).deletePricingConfigurations(List.of(accomType));
        verify(pricingConfigurationService, times(0)).deletePricingAccomClass(accomClass);
        verify(pricingConfigurationService, times(0)).deleteTransientPricingConfigurationsByAccomTypes(accomTypes);
        verify(pricingConfigurationService, times(0)).deleteGroupPricingConfigurationsByAccomTypes(accomTypes);
        verify(pricingConfigurationService, times(0)).deletePricingOffsetAccomTypesByAccomTypes(accomTypes);
        verify(pricingConfigurationService, times(0)).deletePricingConfigurationSupplementsForAccomTypes(accomTypes);
    }

    @Test
    public void getAccomClass_AccomClassesMarkedForDeletion() {
        AccomClass accomClass1 = new AccomClass();
        accomClass1.setId(1);

        SortedSet<AccomClass> accomClasses = new TreeSet<>();
        accomClasses.add(accomClass1);
        presenter.setAccomClasses(accomClasses);

        presenter.setAccomClassesMarkedForDeletion(new ArrayList<>());

        presenter.updateAccomClassesMarkedForDeletion(accomClass1);

        assertTrue(presenter.getAccomClasses().size() == 0);
    }

    @Test
    public void updateAccomClassesMarkedForDeletion() throws Exception {
        presenter = spy(presenter);

        AccomClass defaultAccomClass = new AccomClass();
        defaultAccomClass.setSystemDefault(1);
        AccomType accomType = new AccomType();
        AccomClass accomClass = createRoomClass(accomType, 123);
        FieldUtils.writeField(presenter, "accomClasses", getAccomClassesSetForTesting(Arrays.asList(accomClass, defaultAccomClass)), true);
        AccomTypeADR accomTypeADR = new AccomTypeADR(accomType, BigDecimal.ONE, BigDecimal.TEN, BigDecimal.TEN);
        SortedSet<AccomTypeADR> accomTypeADRs = new TreeSet<>(Collections.singletonList(accomTypeADR));
        FieldUtils.writeField(presenter, "accomTypeADRs", accomTypeADRs, true);
        List<AccomType> accomTypes = new ArrayList<>();
        accomTypes.add(accomType);

        doNothing().when(presenter).sync();

        when(accommodationService.getAccomClassById(123)).thenReturn(accomClass);

        presenter.setAccomClassesMarkedForDeletion(new ArrayList<>());
        presenter.updateAccomClassesMarkedForDeletion(accomClass);

        assertTrue(presenter.getAccomClassesMarkedForDeletion().size() == 1);
    }

    @Test
    public void getCostOfWalkData() throws Exception {
        List<CostofWalk> costofWalks = Arrays.asList(new CostofWalk(), new CostofWalk());
        when(costofWalkService.getCostOfWalks()).thenReturn(costofWalks);

        presenter.getCostOfWalkData();

        assertEquals(costofWalks, presenter.getCostOfWalk());
    }

    private SortedSet<AccomClass> getAccomClassesSetForTesting(List<AccomClass> classes) {
        SortedSet<AccomClass> accomClasses = new TreeSet<>(new RoomTypeMappingComparator());

        accomClasses.addAll(classes);
        return accomClasses;
    }

    @Test
    public void nextState() throws Exception {
        RoomConfigurationSelection startingState = presenter.getState();
        presenter.setGroupPricingEnabled(true);

        presenter.nextState();

        assertEquals(startingState.next(!IS_GROUP_EVALUATION_ENABLED), presenter.getState());
    }

    @Test
    public void previousState() throws Exception {
        RoomConfigurationSelection startingState = presenter.getState();
        presenter.setGroupPricingEnabled(true);

        presenter.previousState();

        assertEquals(startingState.previous(!IS_GROUP_EVALUATION_ENABLED), presenter.getState());
    }

    @Test
    public void onViewOpened() throws Exception {
        AccomClass defaultAccomClass = new AccomClass();
        defaultAccomClass.setSystemDefault(1);
        when(accommodationService.getAccomClassesByViewOrder()).thenReturn(Arrays.asList(defaultAccomClass));

        presenter.onViewOpened(null);

        assertTrue(presenter.isInitialConfiguration());
        assertFalse(presenter.hasChanges(RoomConfigurationSelection.ROOM_TYPE_MAPPING));
        assertFalse(presenter.hasChanges(RoomConfigurationSelection.COST_OF_WALK));
        assertFalse(presenter.hasChanges(RoomConfigurationSelection.OVERBOOKING));
        assertFalse(presenter.hasChanges(RoomConfigurationSelection.PRICE_RANKING));
        assertFalse(presenter.hasChanges(RoomConfigurationSelection.PRICE_UPGRADE_PATH));
        assertFalse(presenter.hasChanges(RoomConfigurationSelection.MIN_PRICE_DIFFERENTIAL));
    }

    @Test
    public void setParameters() {
        presenter.setHasRoomClassConfigChanged(true);

        presenter.setParameters();

        assertFalse(presenter.isHasRoomClassConfigChanged());
        verify(configParamsService).getBooleanParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED);
        verify(configParamsService).getBooleanParameterValue(FeatureTogglesConfigParamName.COMPONENT_ROOMS_ENABLED.value());
        verify(configParamsService).getBooleanParameterValue(FeatureTogglesConfigParamName.IS_ADVANCED_PRICE_RANKING_ENABLED.value());
        verify(configParamsService).getBooleanParameterValue(IPConfigParamName.BAR_ENABLE_SINGLE_BAR_DECISION.value());
        verify(configParamsService).getBooleanParameterValue(FeatureTogglesConfigParamName.PER_PERSON_PRICING_ENABLED.value());
        verify(configParamsService).getBooleanParameterValue(FeatureTogglesConfigParamName.GROUP_FINAL_FORECAST_OVERRIDE_ENABLED.value());
        verify(configParamsService).getBooleanParameterValue(FeatureTogglesConfigParamName.ENABLE_LRV_DROP_RESTRICTION_FEATURE);
        verify(configParamsService).getBooleanParameterValue(FeatureTogglesConfigParamName.COMPONENT_ROOMS_ENABLED.value());
        verify(configParamsService).getBooleanParameterValue(FeatureTogglesConfigParamName.COMPONENT_ROOMS_PRICE_AS_SUM_OF_PARTS);
        verify(configParamsService).getBooleanParameterValue(IPConfigParamName.CORE_LIMITED_DATA_BUILD_ENABLED.value());
        verify(configParamsService).getBooleanParameterValue(FeatureTogglesConfigParamName.AGILE_RATES_ENABLED);
        verify(configParamsService).getBooleanParameterValue(FeatureTogglesConfigParamName.ROOM_TYPE_RECODING_UI_ENABLED.value());
        verify(configParamsService).getBooleanParameterValue(FeatureTogglesConfigParamName.GROUP_FINAL_FORECAST_OVERRIDE_ENABLED);
        verify(configParamsService).getBooleanParameterValue(FeatureTogglesConfigParamName.GROUP_PRICING_ENABLED.value());
        verify(configParamsService).getBooleanParameterValue(FeatureTogglesConfigParamName.FUNCTION_SPACE_ENABLED.value());
    }

    @Test
    public void onViewOpened_notIntial() throws Exception {
        AccomClass defaultAccomClass = new AccomClass();
        defaultAccomClass.setSystemDefault(1);
        AccomClass accomClass = new AccomClass();
        when(accommodationService.getAccomClassesByViewOrder()).thenReturn(Arrays.asList(defaultAccomClass, accomClass));

        presenter.onViewOpened(null);

        assertFalse(presenter.isInitialConfiguration());
        assertFalse(presenter.hasChanges(RoomConfigurationSelection.ROOM_TYPE_MAPPING));
        assertFalse(presenter.hasChanges(RoomConfigurationSelection.COST_OF_WALK));
        assertFalse(presenter.hasChanges(RoomConfigurationSelection.OVERBOOKING));
        assertFalse(presenter.hasChanges(RoomConfigurationSelection.PRICE_RANKING));
        assertFalse(presenter.hasChanges(RoomConfigurationSelection.PRICE_UPGRADE_PATH));
        assertFalse(presenter.hasChanges(RoomConfigurationSelection.MIN_PRICE_DIFFERENTIAL));
    }

    @Test
    public void onWorkContextChange() throws Exception {
        WorkContextType workContextType = mock(WorkContextType.class);
        AccomClass defaultAccomClass = new AccomClass();
        defaultAccomClass.setSystemDefault(1);
        when(accommodationService.getAccomClassesByViewOrder()).thenReturn(Arrays.asList(defaultAccomClass));
        presenter.setHasRoomClassConfigChanged(true);

        presenter.onWorkContextChange(workContextType);

        verify(view).resetWizard();
        assertEquals(RoomConfigurationSelection.ROOM_TYPE_MAPPING, presenter.getState());
    }

    @Test
    public void onWorkContextChange_notInitial() throws Exception {
        WorkContextType workContextType = mock(WorkContextType.class);
        AccomClass defaultAccomClass = new AccomClass();
        defaultAccomClass.setSystemDefault(1);
        AccomClass accomClass = new AccomClass();
        when(accommodationService.getAccomClassesByViewOrder()).thenReturn(Arrays.asList(defaultAccomClass, accomClass));
        presenter.setHasRoomClassConfigChanged(true);

        presenter.onWorkContextChange(workContextType);

        verify(view).resetWizard();
        assertEquals(RoomConfigurationSelection.ROOM_TYPE_MAPPING, presenter.getState());
    }

    @Test
    public void onViewOpenedFindTax() {
        //given
        //initialize param toggles
        presenter.setCPProperty(true);

        //when
        presenter.onViewOpened(null);

        //then
        verify(taxService).findTax();
    }

    @Test
    public void onViewOpenedNeverFindTax() {
        //given
        //initialize param toggles
        presenter.setCPProperty(false);

        //when
        presenter.onViewOpened(null);

        //then
        verify(taxService, never()).findTax();
    }

    @Test
    public void saveCostOfWalks() throws Exception {
        presenter = spy(presenter);

        List<CostofWalk> costofWalks = Arrays.asList(new CostofWalk(), new CostofWalk());
        FieldUtils.writeField(presenter, "costOfWalk", costofWalks, true);

        doNothing().when(presenter).sync();

        presenter.saveCostOfWalks();

        verify(costofWalkService).save(costofWalks);
    }

    @Test
    public void saveRunOfHouse_noChanges() throws Exception {
        presenter = spy(presenter);

        AccomClass accomClass1 = new AccomClass();
        accomClass1.setViewOrder(1);
        AccomClass accomClass2 = new AccomClass();
        accomClass2.setViewOrder(2);

        AccomType rohAccomType = new AccomType();
        rohAccomType.setRohType(0);
        rohAccomType.setId(1);
        accomClass1.addAccomType(rohAccomType);
        AccomType randomAccomType = new AccomType();
        randomAccomType.setRohType(0);
        randomAccomType.setId(2);
        accomClass1.addAccomType(randomAccomType);
        AccomType oldRohAccomType = new AccomType();
        oldRohAccomType.setRohType(1);
        oldRohAccomType.setId(3);
        accomClass2.addAccomType(oldRohAccomType);

        SortedSet<AccomClass> accomClasses = getAccomClassesSetForTesting(Arrays.asList(accomClass1, accomClass2));

        FieldUtils.writeField(presenter, "accomClasses", accomClasses, true);

        when(accomClassPriceRankService.makeAllRoomClassesUpgradable()).thenReturn(false);
        doNothing().when(presenter).sync();

        presenter.saveRunOfHouse(Arrays.asList(rohAccomType));

        verify(accommodationService).updateAccomClasses(new ArrayList<>(accomClasses));
        verify(accomClassPriceRankService).makeAllRoomClassesUpgradable();
        verify(overbookingService).deleteOverbookingConfiguration();
        verify(overbookingService).saveOverbookingAccomTypeDtos(presenter.getOverbookingAccomTypeDtos(), accomClasses);

        assertEquals(1, rohAccomType.getRohType().intValue());
        assertEquals(0, randomAccomType.getRohType().intValue());
        assertEquals(0, oldRohAccomType.getRohType().intValue());
        //I do this in an odd way because the value may be null
        assertTrue(!Boolean.TRUE.equals(presenter.hasChanges(RoomConfigurationSelection.PRICE_RANKING)));
    }

    @Test
    public void saveRunOfHouse_wasChanged() throws Exception {
        presenter = spy(presenter);

        AccomClass accomClass1 = new AccomClass();
        accomClass1.setViewOrder(1);
        AccomClass accomClass2 = new AccomClass();
        accomClass2.setViewOrder(2);

        AccomType rohAccomType = new AccomType();
        rohAccomType.setRohType(0);
        rohAccomType.setId(1);
        accomClass1.addAccomType(rohAccomType);
        AccomType randomAccomType = new AccomType();
        randomAccomType.setRohType(0);
        randomAccomType.setId(2);
        accomClass1.addAccomType(randomAccomType);
        AccomType oldRohAccomType = new AccomType();
        oldRohAccomType.setRohType(1);
        oldRohAccomType.setId(3);
        accomClass2.addAccomType(oldRohAccomType);

        SortedSet<AccomClass> accomClasses = getAccomClassesSetForTesting(Arrays.asList(accomClass1, accomClass2));

        FieldUtils.writeField(presenter, "accomClasses", accomClasses, true);

        doNothing().when(exceptionConfigService).clearConfigAndInstancesAfterROHChanges(true, false);
        when(accomClassPriceRankService.makeAllRoomClassesUpgradable()).thenReturn(true);
        doNothing().when(presenter).sync();

        presenter.saveRunOfHouse(Arrays.asList(rohAccomType));

        verify(accommodationService).updateAccomClasses(new ArrayList<>(accomClasses));
        verify(accomClassPriceRankService).makeAllRoomClassesUpgradable();
        verify(overbookingService).deleteOverbookingConfiguration();
        verify(overbookingService).saveOverbookingAccomTypeDtos(presenter.getOverbookingAccomTypeDtos(), accomClasses);

        assertEquals(1, rohAccomType.getRohType().intValue());
        assertEquals(0, randomAccomType.getRohType().intValue());
        assertEquals(0, oldRohAccomType.getRohType().intValue());
        assertTrue(presenter.hasChanges(RoomConfigurationSelection.PRICE_RANKING));
    }

    @Test
    public void getROHAccomType() throws Exception {
        AccomType accomType1 = new AccomType();
        accomType1.setRohType(0);
        AccomType accomType2 = new AccomType();
        accomType2.setRohType(0);
        AccomType accomType3 = new AccomType();
        accomType3.setRohType(0);

        OverbookingAccomTypeDto dto1 = new OverbookingAccomTypeDto(accomType1);
        OverbookingAccomTypeDto dto2 = new OverbookingAccomTypeDto(accomType2);
        OverbookingAccomTypeDto dto3 = new OverbookingAccomTypeDto(accomType3);
        List<OverbookingAccomTypeDto> dtos = Arrays.asList(dto1, dto2, dto3);
        FieldUtils.writeField(presenter, "overbookingAccomTypeDtos", dtos, true);

        List<AccomType> rohAccomTypes = presenter.getROHAccomTypes();
        assertFalse(CollectionUtils.isNotEmpty(rohAccomTypes));

        accomType2.setRohType(1);

        rohAccomTypes = presenter.getROHAccomTypes();
        assertTrue(CollectionUtils.isNotEmpty(rohAccomTypes));
    }

    @Test
    public void getOverbookingData() throws Exception {
        List<OverbookingAccomTypeDto> dtos = Arrays.asList(new OverbookingAccomTypeDto(new AccomType()), new OverbookingAccomTypeDto(new AccomType()));
        List<OverbookingAccomTypeSeasonDto> seasonDtos = Arrays.asList(new OverbookingAccomTypeSeasonDto(new AccomType()), new OverbookingAccomTypeSeasonDto(new AccomType()));
        when(overbookingService.getOverbookingAccomTypeDtos()).thenReturn(dtos);
        when(overbookingService.getOverbookingAccomTypeSeasonDtos()).thenReturn(seasonDtos);

        presenter.getOverbookingData();

        assertEquals(dtos, presenter.getOverbookingAccomTypeDtos());
        assertEquals(seasonDtos, presenter.getOverbookingAccomTypeSeasonDtos());
        verify(overbookingService).getOverbookingAccomTypeDtos();
        verify(overbookingService).getOverbookingAccomTypeSeasonDtos();
    }

    @Test
    public void shouldGetDataWithOverBookingAsFalseForCRAccomTypesIfComponentRoomIsEnabled() throws Exception {
        List<OverbookingAccomTypeDto> dtos = Arrays.asList(
                createOverbookingAccomTypeDto(false, false),
                createOverbookingAccomTypeDto(true, false),
                createOverbookingAccomTypeDto(false, true),
                createOverbookingAccomTypeDto(true, true));
        List<OverbookingAccomTypeSeasonDto> seasonDtos = Arrays.asList(
                createOverbookingAccomTypeSeasonDto(false, false),
                createOverbookingAccomTypeSeasonDto(true, false),
                createOverbookingAccomTypeSeasonDto(false, true),
                createOverbookingAccomTypeSeasonDto(true, true));
        when(overbookingService.getOverbookingAccomTypeDtos()).thenReturn(dtos);
        when(overbookingService.getOverbookingAccomTypeSeasonDtos()).thenReturn(seasonDtos);
        presenter.setComponentRoomsEnabled(true);

        presenter.getOverbookingData();

        assertEquals(dtos, presenter.getOverbookingAccomTypeDtos());
        assertEquals(seasonDtos, presenter.getOverbookingAccomTypeSeasonDtos());
        verify(overbookingService).getOverbookingAccomTypeDtos();
        verify(overbookingService).getOverbookingAccomTypeSeasonDtos();
        verifyOverBookingValueForAllComponentTypeDTO(presenter.getOverbookingAccomTypeDtos(), false);
        verifyOverBookingValueForAllComponentTypeDTO(presenter.getOverbookingAccomTypeSeasonDtos(), false);
    }

    @Test
    public void shouldGetUnmodifiedDataForCRAccomTypesIfComponentRoomIsDisabled() throws Exception {
        List<OverbookingAccomTypeDto> dtos = Arrays.asList(
                createOverbookingAccomTypeDto(true, true),
                createOverbookingAccomTypeDto(true, true));
        List<OverbookingAccomTypeSeasonDto> seasonDtos = Arrays.asList(
                createOverbookingAccomTypeSeasonDto(true, true),
                createOverbookingAccomTypeSeasonDto(true, true));
        when(overbookingService.getOverbookingAccomTypeDtos()).thenReturn(dtos);
        when(overbookingService.getOverbookingAccomTypeSeasonDtos()).thenReturn(seasonDtos);
        presenter.setComponentRoomsEnabled(false);

        presenter.getOverbookingData();

        assertEquals(dtos, presenter.getOverbookingAccomTypeDtos());
        assertEquals(seasonDtos, presenter.getOverbookingAccomTypeSeasonDtos());
        verify(overbookingService).getOverbookingAccomTypeDtos();
        verify(overbookingService).getOverbookingAccomTypeSeasonDtos();
        verifyOverBookingValueForAllComponentTypeDTO(presenter.getOverbookingAccomTypeDtos(), true);
        verifyOverBookingValueForAllComponentTypeDTO(presenter.getOverbookingAccomTypeSeasonDtos(), true);
    }

    private void verifyOverBookingValueForAllComponentTypeDTO(List<? extends OverbookingAccomTypeDto> overbookingAccomTypeDtos, boolean expectedValue) {
        overbookingAccomTypeDtos.stream().filter(dto -> dto.getAccomType().isComponentRoom()).forEach(dto -> assertEquals(expectedValue, dto.isOverbookingEveryDay()));
    }

    private OverbookingAccomTypeDto createOverbookingAccomTypeDto(boolean isOverBooked, boolean isComponentType) {
        AccomType accomType = new AccomType();
        accomType.setIsComponentRoom(isComponentType ? "Y" : "N");
        OverbookingAccomTypeDto overbookingAccomTypeDto = new OverbookingAccomTypeDto(accomType);
        overbookingAccomTypeDto.setOverbooking(isOverBooked);
        return overbookingAccomTypeDto;
    }

    private OverbookingAccomTypeSeasonDto createOverbookingAccomTypeSeasonDto(boolean isOverBooked, boolean isComponentType) {
        AccomType accomType = new AccomType();
        accomType.setIsComponentRoom(isComponentType ? "Y" : "N");
        OverbookingAccomTypeSeasonDto overbookingAccomTypeSeasonDto = new OverbookingAccomTypeSeasonDto(accomType);
        overbookingAccomTypeSeasonDto.setOverbooking(isOverBooked);
        return overbookingAccomTypeSeasonDto;
    }

    @Test
    public void isOverbookingPreviouslyConfigured() throws Exception {
        when(overbookingService.isOverbookingPreviouslyConfigured()).thenReturn(true);

        assertTrue(presenter.isOverbookingPreviouslyConfigured());

        verify(overbookingService).isOverbookingPreviouslyConfigured();
    }

    @Test
    public void saveOverbookingData_noROH() throws Exception {
        presenter = spy(presenter);

        AccomType accomType1 = new AccomType();
        accomType1.setRohType(0);
        AccomType accomType2 = new AccomType();
        accomType2.setRohType(0);
        AccomType accomType3 = new AccomType();
        accomType3.setRohType(0);

        OverbookingAccomTypeDto dto1 = new OverbookingAccomTypeDto(accomType1);
        OverbookingAccomTypeDto dto2 = new OverbookingAccomTypeDto(accomType2);
        OverbookingAccomTypeDto dto3 = new OverbookingAccomTypeDto(accomType3);
        List<OverbookingAccomTypeDto> dtos = Arrays.asList(dto1, dto2, dto3);
        FieldUtils.writeField(presenter, "overbookingAccomTypeDtos", dtos, true);

        doNothing().when(presenter).sync();

        presenter.saveOverbookingData(false);

        verify(overbookingService).saveOverbookingAccomTypeDtos(dtos, Collections.emptySet());
        verifyZeroInteractions(accommodationService);
    }

    @Test
    public void saveOverbookingData_withROH() throws Exception {
        presenter = spy(presenter);

        AccomType accomType1 = new AccomType();
        accomType1.setRohType(0);
        AccomType accomType2 = new AccomType();
        accomType2.setRohType(1);
        AccomType accomType3 = new AccomType();
        accomType3.setRohType(0);

        OverbookingAccomTypeDto dto1 = new OverbookingAccomTypeDto(accomType1);
        OverbookingAccomTypeDto dto2 = new OverbookingAccomTypeDto(accomType2);
        OverbookingAccomTypeDto dto3 = new OverbookingAccomTypeDto(accomType3);
        List<OverbookingAccomTypeDto> dtos = Arrays.asList(dto1, dto2, dto3);
        FieldUtils.writeField(presenter, "overbookingAccomTypeDtos", dtos, true);

        // Need to verify that notifications, etc get cleaned up
        doNothing().when(exceptionConfigService).clearConfigAndInstancesAfterROHChanges(false, true);
        doNothing().when(presenter).sync();

        presenter.saveOverbookingData(false);

        verify(overbookingService).saveOverbookingAccomTypeDtos(dtos, Collections.emptySet());
        verify(accommodationService).saveAccomType(accomType2);
        verify(accommodationService).getAccomClassesByViewOrder();
        assertEquals(0, accomType2.getRohType().intValue());
    }

    @Test
    public void saveRoomType() throws Exception {
        AccomType accomType = new AccomType();

        presenter.saveRoomType(accomType);

        verify(accommodationService).saveAccomType(accomType);
    }

    @Test
    public void getRoomClassAvgADR() throws Exception {
        AccomClass roomClass = new AccomClass();
        roomClass.setViewOrder(1);

        FieldUtils.writeField(presenter, "accomTypeADRs", Collections.emptySortedSet(), true);
        //No room type info
        BigDecimal roomClassAvgADR = presenter.getRoomClassAvgADR(roomClass, adr -> adr.getAccomType().getDisplayStatusId() != 2);
        assertEquals(BigDecimal.ZERO, roomClassAvgADR);

        //Setup Data
        AccomType accomType1 = new AccomType();
        accomType1.setDisplayStatusId(1);
        accomType1.setAccomClass(roomClass);
        roomClass.addAccomType(accomType1);
        AccomType accomType2 = new AccomType();
        accomType2.setDisplayStatusId(1);
        accomType2.setAccomClass(roomClass);
        roomClass.addAccomType(accomType2);

        AccomTypeADR accomTypeADR1 = new AccomTypeADR(accomType1, BigDecimal.ONE, BigDecimal.TEN, BigDecimal.TEN);
        AccomTypeADR accomTypeADR2 = new AccomTypeADR(accomType2, BigDecimal.ONE, BigDecimal.ONE, BigDecimal.ONE);


        SortedSet<AccomTypeADR> accomTypeADRs = new TreeSet<>(Arrays.asList(accomTypeADR1, accomTypeADR2));
        FieldUtils.writeField(presenter, "accomTypeADRs", accomTypeADRs, true);

        SortedSet<AccomClass> accomClasses = getAccomClassesSetForTesting(Collections.singletonList(roomClass));
        FieldUtils.writeField(presenter, "accomClasses", accomClasses, true);

        roomClassAvgADR = presenter.getRoomClassAvgADR(roomClass, adr -> adr.getAccomType().getDisplayStatusId() != 2);
        assertEquals(BigDecimal.valueOf(11).divide(BigDecimal.valueOf(2), 2, RoundingMode.HALF_UP), roomClassAvgADR);

        //Empty Accom Class
        roomClassAvgADR = presenter.getRoomClassAvgADR(new AccomClass(), adr -> adr.getAccomType().getDisplayStatusId() != 2);
        assertEquals(BigDecimal.ZERO, roomClassAvgADR);
    }

    @Test
    public void getRoomClassMinADR() throws Exception {
        AccomClass roomClass = new AccomClass();
        roomClass.setViewOrder(1);

        FieldUtils.writeField(presenter, "accomTypeADRs", Collections.emptySortedSet(), true);
        //No room type info
        final Predicate<AccomTypeADR> activeAccomTypesPredicate = adr -> adr.getAccomType().getDisplayStatusId() != 2;
        BigDecimal roomClassMinADR = presenter.getRoomClassMinADR(roomClass, activeAccomTypesPredicate);
        assertEquals(BigDecimal.ZERO, roomClassMinADR);

        //Setup Data
        AccomType accomType1 = new AccomType();
        accomType1.setDisplayStatusId(1);
        accomType1.setAccomClass(roomClass);
        roomClass.addAccomType(accomType1);
        AccomType accomType2 = new AccomType();
        accomType2.setDisplayStatusId(1);
        accomType2.setAccomClass(roomClass);
        roomClass.addAccomType(accomType2);

        AccomTypeADR accomTypeADR1 = new AccomTypeADR(accomType1, BigDecimal.ONE, BigDecimal.TEN, BigDecimal.TEN);
        AccomTypeADR accomTypeADR2 = new AccomTypeADR(accomType2, BigDecimal.ONE, BigDecimal.ONE, BigDecimal.ONE);


        SortedSet<AccomTypeADR> accomTypeADRs = new TreeSet<>(Arrays.asList(accomTypeADR1, accomTypeADR2));
        FieldUtils.writeField(presenter, "accomTypeADRs", accomTypeADRs, true);

        SortedSet<AccomClass> accomClasses = getAccomClassesSetForTesting(Collections.singletonList(roomClass));
        FieldUtils.writeField(presenter, "accomClasses", accomClasses, true);

        roomClassMinADR = presenter.getRoomClassMinADR(roomClass, activeAccomTypesPredicate);
        assertEquals(BigDecimal.ONE.setScale(2, BigDecimal.ROUND_HALF_UP), roomClassMinADR);

        //Empty Accom Class
        roomClassMinADR = presenter.getRoomClassMinADR(new AccomClass(), activeAccomTypesPredicate);
        assertEquals(BigDecimal.ZERO, roomClassMinADR);
    }

    @Test
    public void getRoomClassMaxADR() throws Exception {
        AccomClass roomClass = new AccomClass();
        roomClass.setViewOrder(1);

        FieldUtils.writeField(presenter, "accomTypeADRs", Collections.emptySortedSet(), true);
        //No room type info
        final Predicate<AccomTypeADR> activeAccomTypesPredicate = adr -> adr.getAccomType().getDisplayStatusId() != 2;
        BigDecimal roomClassMaxADR = presenter.getRoomClassMaxADR(roomClass, activeAccomTypesPredicate);
        assertEquals(BigDecimal.ZERO, roomClassMaxADR);

        //Setup Data
        AccomType accomType1 = new AccomType();
        accomType1.setDisplayStatusId(1);
        accomType1.setAccomClass(roomClass);
        roomClass.addAccomType(accomType1);
        AccomType accomType2 = new AccomType();
        accomType2.setDisplayStatusId(1);
        accomType2.setAccomClass(roomClass);
        roomClass.addAccomType(accomType2);

        AccomTypeADR accomTypeADR1 = new AccomTypeADR(accomType1, BigDecimal.ONE, BigDecimal.TEN, BigDecimal.TEN);
        AccomTypeADR accomTypeADR2 = new AccomTypeADR(accomType2, BigDecimal.ONE, BigDecimal.ONE, BigDecimal.ONE);


        SortedSet<AccomTypeADR> accomTypeADRs = new TreeSet<>(Arrays.asList(accomTypeADR1, accomTypeADR2));
        FieldUtils.writeField(presenter, "accomTypeADRs", accomTypeADRs, true);

        SortedSet<AccomClass> accomClasses = getAccomClassesSetForTesting(Collections.singletonList(roomClass));
        FieldUtils.writeField(presenter, "accomClasses", accomClasses, true);

        roomClassMaxADR = presenter.getRoomClassMaxADR(roomClass, adr -> adr.getAccomType().getDisplayStatusId() != 2);
        assertEquals(BigDecimal.TEN.setScale(2, BigDecimal.ROUND_HALF_UP), roomClassMaxADR);

        //Empty Accom Class
        roomClassMaxADR = presenter.getRoomClassMaxADR(new AccomClass(), adr -> adr.getAccomType().getDisplayStatusId() != 2);
        assertEquals(BigDecimal.ZERO, roomClassMaxADR);
    }

    @Test
    public void getPriceRankData() throws Exception {
        AccomClass accomClass1 = new AccomClass();
        accomClass1.setId(1);
        AccomClass accomClass2 = new AccomClass();
        accomClass2.setId(2);
        AccomClass accomClass3 = new AccomClass();
        accomClass3.setId(3);
        AccomClass accomClass4 = new AccomClass();
        accomClass4.setId(4);
        AccomClass accomClass5 = new AccomClass();
        accomClass5.setId(5);
        AccomClass accomClass6 = new AccomClass();
        accomClass6.setId(6);

        AccomClassPriceRank priceRank1 = new AccomClassPriceRank(accomClass1, accomClass2, true);
        priceRank1.setId(1);
        AccomClassPriceRank priceRank2 = new AccomClassPriceRank(accomClass2, accomClass3, false);
        priceRank2.setId(2);
        AccomClassPriceRank priceRank3 = new AccomClassPriceRank(accomClass3, accomClass4, true);
        priceRank3.setId(3);
        AccomClassPriceRank priceRank4 = new AccomClassPriceRank(accomClass4, accomClass5, true);
        priceRank4.setId(4);
        AccomClassPriceRank priceRank5 = new AccomClassPriceRank(accomClass5, accomClass6, true);
        priceRank5.setId(5);

        LinkedList<AccomClassPriceRank> priceRanks = new LinkedList<>(Arrays.asList(priceRank5, priceRank2, priceRank4, priceRank1, priceRank3));
        when(accomClassPriceRankService.getAccomClassPriceRankForAllAccomClasses()).thenReturn(priceRanks);

        // I didn't make these representative of a realistic scenario
        AccomClassPriceRankNetworkArrow arrow1 = new AccomClassPriceRankNetworkArrow(priceRank1, 0, 7, Direction.UP);
        AccomClassPriceRankNetworkArrow arrow2 = new AccomClassPriceRankNetworkArrow(priceRank2, 0, 5, Direction.UP);
        AccomClassPriceRankNetworkArrow arrow3 = new AccomClassPriceRankNetworkArrow(priceRank3, 0, 3, Direction.UP);
        List<AccomClassPriceRankNetworkArrow> expectedNetworkArrows = Arrays.asList(arrow1, arrow2, arrow3);
        when(accomClassPriceRankService.getNetworkPathArrows()).thenReturn(expectedNetworkArrows);

        presenter.getPriceRankData();

        List<AccomClassPriceRank> accomClassPriceRankings = presenter.getPriceRankings();
        List<AccomClassPriceRankNetworkArrow> advancedNetworkArrows = presenter.getAdvancedNetworkArrows();

        //Order is desc
        assertEquals(priceRanks, accomClassPriceRankings);
        assertEquals(expectedNetworkArrows, advancedNetworkArrows);
        verify(accomClassPriceRankService).getAccomClassPriceRankForAllAccomClasses();
        verify(accomClassPriceRankService).getNetworkPathArrows();
    }

    @Test
    public void isPriceRankingAdvanced_true() throws Exception {
        // I didn't make these representative of a realistic scenario
        AccomClassPriceRankNetworkArrow arrow1 = new AccomClassPriceRankNetworkArrow();
        AccomClassPriceRankNetworkArrow arrow2 = new AccomClassPriceRankNetworkArrow();
        AccomClassPriceRankNetworkArrow arrow3 = new AccomClassPriceRankNetworkArrow();
        List<AccomClassPriceRankNetworkArrow> expectedNetworkArrows = Arrays.asList(arrow1, arrow2, arrow3);
        FieldUtils.writeField(presenter, "advancedNetworkArrows", expectedNetworkArrows, true);

        assertTrue(presenter.isPriceRankingAdvanced());
    }

    @Test
    public void isPriceRankingAdvanced_false() throws Exception {
        assertFalse(presenter.isPriceRankingAdvanced());
    }

    @Test
    public void savePriceRankingData_notComplete() throws Exception {
        presenter = spy(presenter);
        LinkedList<AccomClassPriceRank> accomClassPriceRankings = new LinkedList<>();
        accomClassPriceRankings.add(new AccomClassPriceRank());
        AccomClass accomClass = new AccomClass();
        SortedSet<AccomClass> accomClasses = getAccomClassesSetForTesting(Collections.singletonList(accomClass));

        FieldUtils.writeField(presenter, "accomClasses", accomClasses, true);
        FieldUtils.writeField(presenter, "accomClassPriceRankings", accomClassPriceRankings, true);
        doNothing().when(presenter).sync();

        presenter.savePriceRankingData(false, accomClassPriceRankings, false);

        verify(accomClassPriceRankService).updateAccomClassPriceRanks(accomClassPriceRankings);
        verify(accommodationService, times(0)).updateAccomClasses(anyObject());
        assertTrue(accomClass.getIsPriceRankConfigured().equals(AccomClassPriceRankStatus.INCOMPLETE));
    }

    @Test
    public void savePriceRankingData_willNotOverwriteComplete() throws Exception {
        presenter = spy(presenter);
        AccomClass accomClass = new AccomClass();
        accomClass.setIsPriceRankConfigured(AccomClassPriceRankStatus.COMPLETE_RANKED);
        LinkedList<AccomClassPriceRank> accomClassPriceRankings = new LinkedList<>();
        AccomClassPriceRank accomClassPriceRank = new AccomClassPriceRank();
        accomClassPriceRank.setHigherRankAccomClass(accomClass);
        accomClassPriceRankings.add(accomClassPriceRank);
        SortedSet<AccomClass> accomClasses = getAccomClassesSetForTesting(Collections.singletonList(accomClass));

        FieldUtils.writeField(presenter, "accomClasses", accomClasses, true);
        FieldUtils.writeField(presenter, "accomClassPriceRankings", accomClassPriceRankings, true);
        doNothing().when(presenter).sync();

        presenter.savePriceRankingData(false, accomClassPriceRankings, false);

        verify(accomClassPriceRankService).updateAccomClassPriceRanks(accomClassPriceRankings);
        verify(accommodationService, never()).updateAccomClasses(anyObject());
        //Will not overwrite if it has been configured already
        assertTrue(accomClass.getIsPriceRankConfigured().equals(AccomClassPriceRankStatus.COMPLETE_RANKED));
    }

    @Test
    public void savePriceRankingData_complete() throws Exception {
        presenter = spy(presenter);
        AccomClass accomClass = new AccomClass();
        accomClass.setIsPriceRankConfigured(AccomClassPriceRankStatus.INCOMPLETE);
        LinkedList<AccomClassPriceRank> accomClassPriceRankings = new LinkedList<>();
        AccomClassPriceRank accomClassPriceRank = new AccomClassPriceRank();
        accomClassPriceRank.setHigherRankAccomClass(accomClass);
        accomClassPriceRankings.add(accomClassPriceRank);
        SortedSet<AccomClass> accomClasses = getAccomClassesSetForTesting(Collections.singletonList(accomClass));

        FieldUtils.writeField(presenter, "accomClasses", accomClasses, true);
        FieldUtils.writeField(presenter, "accomClassPriceRankings", accomClassPriceRankings, true);
        doNothing().when(presenter).sync();

        presenter.savePriceRankingData(true, accomClassPriceRankings, false);

        verify(accomClassPriceRankService, never()).deleteAccomClassPriceRanks(anyObject());
        verify(accomClassPriceRankService).updateAccomClassPriceRanks(accomClassPriceRankings);
        verify(accommodationService).updateAccomClasses(new ArrayList<>(accomClasses), new ArrayList<>(), new ArrayList<>());
        verify(agileRatesConfigurationService, never()).changedRoomClassConfig(any());
        assertTrue(accomClass.getIsPriceRankConfigured().equals(AccomClassPriceRankStatus.COMPLETE_RANKED));
    }

    @Test
    void shouldCallToEnableLTBDEForPricingWhenPriceRankOrUpgradePathHasBeenChanged() throws Exception {
        presenter = spy(presenter);
        AccomClass accomClass = new AccomClass();
        accomClass.setIsPriceRankConfigured(AccomClassPriceRankStatus.INCOMPLETE);
        LinkedList<AccomClassPriceRank> accomClassPriceRankings = new LinkedList<>();
        AccomClassPriceRank accomClassPriceRank = new AccomClassPriceRank();
        accomClassPriceRank.setHigherRankAccomClass(accomClass);
        accomClassPriceRankings.add(accomClassPriceRank);

        SortedSet<AccomClass> accomClasses = getAccomClassesSetForTesting(Collections.singletonList(accomClass));

        FieldUtils.writeField(presenter, "accomClasses", accomClasses, true);
        FieldUtils.writeField(presenter, "accomClassPriceRankings", accomClassPriceRankings, true);
        doNothing().when(presenter).sync();

        presenter.savePriceRankingData(true, accomClassPriceRankings, true);
        verify(pricingConfigurationLTBDEService).enableLTBDEOnIndirectConfigChangedIfApplicable();
    }

    @Test
    void shouldNotCallToEnableLTBDEForPricingWhenPriceRankOrUpgradePathHasBeenChanged() throws Exception {
        presenter = spy(presenter);
        AccomClass accomClass = new AccomClass();
        accomClass.setIsPriceRankConfigured(AccomClassPriceRankStatus.INCOMPLETE);
        LinkedList<AccomClassPriceRank> accomClassPriceRankings = new LinkedList<>();
        AccomClassPriceRank accomClassPriceRank = new AccomClassPriceRank();
        accomClassPriceRank.setHigherRankAccomClass(accomClass);
        accomClassPriceRankings.add(accomClassPriceRank);

        SortedSet<AccomClass> accomClasses = getAccomClassesSetForTesting(Collections.singletonList(accomClass));

        FieldUtils.writeField(presenter, "accomClasses", accomClasses, true);
        FieldUtils.writeField(presenter, "accomClassPriceRankings", accomClassPriceRankings, true);
        doNothing().when(presenter).sync();

        presenter.savePriceRankingData(true, accomClassPriceRankings, false);
        verify(pricingConfigurationLTBDEService, times(0)).enabledLTBDEIfApplicable();
    }

    @Test
    public void savePriceRankingData_complete_nuke_and_paves_if_different() throws Exception {
        presenter = spy(presenter);
        AccomClass accomClass = new AccomClass();
        accomClass.setIsPriceRankConfigured(AccomClassPriceRankStatus.INCOMPLETE);
        LinkedList<AccomClassPriceRank> accomClassPriceRankings = new LinkedList<>();
        AccomClassPriceRank accomClassPriceRank = new AccomClassPriceRank();
        accomClassPriceRank.setHigherRankAccomClass(accomClass);
        accomClassPriceRankings.add(accomClassPriceRank);
        SortedSet<AccomClass> accomClasses = getAccomClassesSetForTesting(Collections.singletonList(accomClass));

        FieldUtils.writeField(presenter, "accomClasses", accomClasses, true);
        //The list of price rankings on the presenter is different
        List<AccomClassPriceRank> originalPriceRanks = new LinkedList<>(Arrays.asList(new AccomClassPriceRank(), new AccomClassPriceRank()));
        FieldUtils.writeField(presenter, "accomClassPriceRankings", originalPriceRanks, true);
        doNothing().when(presenter).sync();

        presenter.savePriceRankingData(true, accomClassPriceRankings, false);

        verify(accomClassPriceRankService).deleteAccomClassPriceRanks(originalPriceRanks);
        verify(accomClassPriceRankService).updateAccomClassPriceRanks(accomClassPriceRankings);
        verify(accommodationService).updateAccomClasses(new ArrayList<>(accomClasses), new ArrayList<>(), new ArrayList<>());
        verify(agileRatesConfigurationService, never()).changedRoomClassConfig(any());
        assertTrue(accomClass.getIsPriceRankConfigured().equals(AccomClassPriceRankStatus.COMPLETE_RANKED));
    }

    @Test
    public void savePriceRankingData_complete_ContinuousPricingEnabled() throws Exception {
        presenter = spy(presenter);
        AccomClass accomClass = new AccomClass();
        accomClass.setIsPriceRankConfigured(AccomClassPriceRankStatus.INCOMPLETE);
        LinkedList<AccomClassPriceRank> accomClassPriceRankings = new LinkedList<>();
        AccomClassPriceRank accomClassPriceRank = new AccomClassPriceRank();
        accomClassPriceRank.setHigherRankAccomClass(accomClass);
        accomClassPriceRankings.add(accomClassPriceRank);
        SortedSet<AccomClass> accomClasses = getAccomClassesSetForTesting(Collections.singletonList(accomClass));
        presenter.setCPProperty(true);

        FieldUtils.writeField(presenter, "accomClasses", accomClasses, true);
        FieldUtils.writeField(presenter, "accomClassPriceRankings", accomClassPriceRankings, true);
        doNothing().when(presenter).sync();

        presenter.savePriceRankingData(true, accomClassPriceRankings, false);

        verify(accomClassPriceRankService).updateAccomClassPriceRanks(accomClassPriceRankings);
        verify(agileRatesConfigurationService, never()).changedRoomClassConfig(any());
        assertTrue(accomClass.getIsPriceRankConfigured().equals(AccomClassPriceRankStatus.COMPLETE_RANKED));
    }

    @Test
    public void createNewPriceRankingData_nothingChanged() throws Exception {
        AccomClass accomClass1 = new AccomClass();
        accomClass1.setId(1);
        accomClass1.setViewOrder(1);
        AccomClass accomClass2 = new AccomClass();
        accomClass2.setId(2);
        accomClass2.setViewOrder(2);
        AccomClass accomClass3 = new AccomClass();
        accomClass3.setId(3);
        accomClass3.setViewOrder(3);
        AccomClass accomClass4 = new AccomClass();
        accomClass4.setId(4);
        accomClass4.setViewOrder(4);

        AccomClassPriceRank priceRank1 = new AccomClassPriceRank(accomClass1, accomClass2, true);
        priceRank1.setId(1);
        AccomClassPriceRank priceRank2 = new AccomClassPriceRank(accomClass2, accomClass3, false);
        priceRank2.setId(2);
        AccomClassPriceRank priceRank3 = new AccomClassPriceRank(accomClass3, accomClass4, true);
        priceRank3.setId(3);

        //The order of the ranked room classes is the same as the links within the price rankings
        LinkedList<AccomClassPriceRank> accomClassPriceRankings = new LinkedList<>(Arrays.asList(priceRank3, priceRank2, priceRank1));
        LinkedList<AccomClass> rankedAccomClasses = new LinkedList<>(Arrays.asList(accomClass4, accomClass3, accomClass2, accomClass1));

        FieldUtils.writeField(presenter, "accomClassPriceRankings", accomClassPriceRankings, true);
        SortedSet<AccomClass> presenterAccomClasses = getAccomClassesSetForTesting(rankedAccomClasses);
        FieldUtils.writeField(presenter, "accomClasses", presenterAccomClasses, true);

        presenter.createNewPriceRankingData(rankedAccomClasses);
        verifyZeroInteractions(accomClassPriceRankService);
        assertEquals(accomClassPriceRankings, presenter.getPriceRankings());
        //I do this in an odd way because the value may be null
        assertTrue(!Boolean.TRUE.equals(presenter.hasChanges(RoomConfigurationSelection.PRICE_RANKING)));
        assertTrue(!Boolean.TRUE.equals(presenter.hasChanges(RoomConfigurationSelection.MIN_PRICE_DIFFERENTIAL)));
        verify(pricingConfigurationLTBDEService, times(0)).enabledLTBDEIfApplicable();

    }

    @Test
    public void createNewPriceRankingData_somethingChanged() throws Exception {
        presenter = spy(presenter);
        AccomClass accomClass1 = new AccomClass();
        accomClass1.setId(1);
        accomClass1.setViewOrder(3);
        AccomClass accomClass2 = new AccomClass();
        accomClass2.setId(2);
        accomClass2.setViewOrder(4);
        AccomClass accomClass3 = new AccomClass();
        accomClass3.setId(3);
        accomClass3.setViewOrder(1);
        AccomClass accomClass4 = new AccomClass();
        accomClass4.setId(4);
        accomClass4.setViewOrder(2);

        AccomClassPriceRank priceRank1 = new AccomClassPriceRank(accomClass1, accomClass2, true);
        priceRank1.setId(1);
        AccomClassPriceRank priceRank2 = new AccomClassPriceRank(accomClass2, accomClass3, false);
        priceRank2.setId(2);
        AccomClassPriceRank priceRank3 = new AccomClassPriceRank(accomClass3, accomClass4, true);
        priceRank3.setId(3);

        //The order of the ranked room classes is different from the links within the price rankings
        LinkedList<AccomClassPriceRank> accomClassPriceRankings = new LinkedList<>(Arrays.asList(priceRank3, priceRank2, priceRank1));
        LinkedList<AccomClass> rankedAccomClasses = new LinkedList<>(Arrays.asList(accomClass3, accomClass4, accomClass1, accomClass2));

        FieldUtils.writeField(presenter, "accomClassPriceRankings", accomClassPriceRankings, true);
        SortedSet<AccomClass> presenterAccomClasses = getAccomClassesSetForTesting(rankedAccomClasses);
        FieldUtils.writeField(presenter, "accomClasses", presenterAccomClasses, true);
        doNothing().when(presenter).sync();
        presenter.setCPProperty(false);
        presenter.setGroupPricingEnabled(false);

        presenter.createNewPriceRankingData(rankedAccomClasses);

        verify(accomClassPriceRankService).deleteAccomClassPriceRanks(accomClassPriceRankings);
        verify(accomClassPriceRankService).updateAccomClassPriceRanks(priceRanksCaptor.capture());
        verify(accommodationService, times(1)).updateAccomClasses(rankedAccomClasses);
        verify(accommodationService, times(1)).updateAccomClasses(rankedAccomClasses, new ArrayList<>(), new ArrayList<>());
        verify(agileRatesConfigurationService, never()).changedRoomClassConfig(any());
        List<AccomClassPriceRank> savedPriceRanks = priceRanksCaptor.getValue();

        assertEquals(3, savedPriceRanks.size());
        AccomClassPriceRank accomClassPriceRank = savedPriceRanks.get(0);
        assertEquals(accomClass4, accomClassPriceRank.getLowerRankAccomClass());
        assertEquals(accomClass3, accomClassPriceRank.getHigherRankAccomClass());
        assertTrue(accomClassPriceRank.isUpgradeAllowed());

        accomClassPriceRank = savedPriceRanks.get(1);
        assertEquals(accomClass1, accomClassPriceRank.getLowerRankAccomClass());
        assertEquals(accomClass4, accomClassPriceRank.getHigherRankAccomClass());
        assertTrue(accomClassPriceRank.isUpgradeAllowed());

        accomClassPriceRank = savedPriceRanks.get(2);
        assertEquals(accomClass2, accomClassPriceRank.getLowerRankAccomClass());
        assertEquals(accomClass1, accomClassPriceRank.getHigherRankAccomClass());
        assertTrue(accomClassPriceRank.isUpgradeAllowed());

        assertTrue(presenter.hasChanges(RoomConfigurationSelection.PRICE_RANKING));
        assertTrue(presenter.hasChanges(RoomConfigurationSelection.MIN_PRICE_DIFFERENTIAL));

        assertTrue(presenter.hasChanges(RoomConfigurationSelection.GROUP_PRICE_RANKING));
        verify(pricingConfigurationLTBDEService).enableLTBDEOnIndirectConfigChangedIfApplicable();
    }

    /*
    This is meant to test scenario where user changes price ranking and we want to make sure that any AccomClassPriceRank
    objects that weren't affected maintain their existing upgradeability.
     */
    @Test
    public void createNewPriceRankingData_onlyOnePriceRankingChanged() throws Exception {
        presenter = spy(presenter);
        AccomClass accomClass1 = new AccomClass();
        accomClass1.setId(1);
        accomClass1.setViewOrder(3);
        AccomClass accomClass2 = new AccomClass();
        accomClass2.setId(2);
        accomClass2.setViewOrder(4);
        AccomClass accomClass3 = new AccomClass();
        accomClass3.setId(3);
        accomClass3.setViewOrder(1);
        AccomClass accomClass4 = new AccomClass();
        accomClass4.setId(4);
        accomClass4.setViewOrder(2);

        AccomClassPriceRank priceRank1 = new AccomClassPriceRank(accomClass1, accomClass2, true);
        priceRank1.setId(1);
        AccomClassPriceRank priceRank2 = new AccomClassPriceRank(accomClass2, accomClass3, false);
        priceRank2.setId(2);
        AccomClassPriceRank priceRank3 = new AccomClassPriceRank(accomClass3, accomClass4, false);
        priceRank3.setId(3);

        //The order of the ranked room classes is different from the links within the price rankings
        LinkedList<AccomClassPriceRank> accomClassPriceRankings = new LinkedList<>(Arrays.asList(priceRank3, priceRank2, priceRank1));
        LinkedList<AccomClass> rankedAccomClasses = new LinkedList<>(Arrays.asList(accomClass4, accomClass3, accomClass1, accomClass2));

        FieldUtils.writeField(presenter, "accomClassPriceRankings", accomClassPriceRankings, true);
        SortedSet<AccomClass> presenterAccomClasses = getAccomClassesSetForTesting(rankedAccomClasses);
        FieldUtils.writeField(presenter, "accomClasses", presenterAccomClasses, true);
        doNothing().when(presenter).sync();

        presenter.createNewPriceRankingData(rankedAccomClasses);

        verify(accomClassPriceRankService).updateAccomClassPriceRanks(priceRanksCaptor.capture());
        verify(agileRatesConfigurationService, never()).changedRoomClassConfig(any());
        List<AccomClassPriceRank> savedPriceRanks = priceRanksCaptor.getValue();

        assertEquals(3, savedPriceRanks.size());
        AccomClassPriceRank accomClassPriceRank = savedPriceRanks.get(0);
        assertEquals(accomClass4, accomClassPriceRank.getHigherRankAccomClass());
        assertEquals(accomClass3, accomClassPriceRank.getLowerRankAccomClass());
        assertFalse(accomClassPriceRank.isUpgradeAllowed());

        accomClassPriceRank = savedPriceRanks.get(1);
        assertEquals(accomClass1, accomClassPriceRank.getLowerRankAccomClass());
        assertEquals(accomClass3, accomClassPriceRank.getHigherRankAccomClass());
        assertTrue(accomClassPriceRank.isUpgradeAllowed());

        accomClassPriceRank = savedPriceRanks.get(2);
        assertEquals(accomClass2, accomClassPriceRank.getLowerRankAccomClass());
        assertEquals(accomClass1, accomClassPriceRank.getHigherRankAccomClass());
        assertTrue(accomClassPriceRank.isUpgradeAllowed());
        verify(pricingConfigurationLTBDEService, times(1)).enableLTBDEOnIndirectConfigChangedIfApplicable();

    }

    @Test
    public void createNewPriceRankingData_noPriceRanks() throws Exception {
        presenter = spy(presenter);
        AccomClass accomClass1 = new AccomClass();
        accomClass1.setId(1);
        accomClass1.setViewOrder(1);
        AccomClass accomClass2 = new AccomClass();
        accomClass2.setId(2);
        accomClass2.setViewOrder(2);

        LinkedList<AccomClassPriceRank> accomClassPriceRankings = new LinkedList<>();
        LinkedList<AccomClass> rankedAccomClasses = new LinkedList<>(Arrays.asList(accomClass1, accomClass2));

        FieldUtils.writeField(presenter, "accomClassPriceRankings", accomClassPriceRankings, true);
        SortedSet<AccomClass> presenterAccomClasses = getAccomClassesSetForTesting(rankedAccomClasses);
        FieldUtils.writeField(presenter, "accomClasses", presenterAccomClasses, true);
        doNothing().when(presenter).sync();

        presenter.createNewPriceRankingData(rankedAccomClasses);

        verify(accomClassPriceRankService).updateAccomClassPriceRanks(priceRanksCaptor.capture());
        verify(agileRatesConfigurationService, never()).changedRoomClassConfig(any());
        List<AccomClassPriceRank> savedPriceRanks = priceRanksCaptor.getValue();

        assertEquals(1, savedPriceRanks.size());
        AccomClassPriceRank accomClassPriceRank = savedPriceRanks.get(0);
        assertEquals(accomClass1, accomClassPriceRank.getHigherRankAccomClass());
        assertEquals(accomClass2, accomClassPriceRank.getLowerRankAccomClass());
        assertTrue(accomClassPriceRank.isUpgradeAllowed());
        verify(pricingConfigurationLTBDEService, times(1)).enableLTBDEOnIndirectConfigChangedIfApplicable();

    }

    @Test
    public void willSplitOccur_Overbooking() throws Exception {
        List<OverbookingSeasonUIWrapper> existingSeasons = Arrays.asList(new OverbookingSeasonUIWrapper(), new OverbookingSeasonUIWrapper());
        OverbookingSeasonUIWrapper editSeason = new OverbookingSeasonUIWrapper();
        when(seasonService.willSplitOccur(existingSeasons, editSeason)).thenReturn(true);

        assertTrue(presenter.willSplitOccur(existingSeasons, editSeason));
    }

    @Test
    public void willSplitOccur_PriceDiff() throws Exception {
        List<AccomClassMinPriceDiffSeason> existingSeasons = Arrays.asList(new AccomClassMinPriceDiffSeason(), new AccomClassMinPriceDiffSeason());
        AccomClassMinPriceDiffSeason editSeason = new AccomClassMinPriceDiffSeason();
        when(seasonService.willSplitOccur(existingSeasons, editSeason)).thenReturn(true);

        assertTrue(presenter.willSplitOccur(existingSeasons, editSeason));
    }

    @Test
    public void applySplit_Overbooking() throws Exception {
        RoomsConfigurationPresenter presenter = spy(this.presenter);
        List<OverbookingSeasonUIWrapper> existingSeasons = Arrays.asList(new OverbookingSeasonUIWrapper(), new OverbookingSeasonUIWrapper());
        OverbookingSeasonUIWrapper editSeason = new OverbookingSeasonUIWrapper();
        LocalDate systemDate = new LocalDate();
        doReturn(systemDate).when(presenter).getSystemDateAsLocalDate();

        presenter.applySplit(existingSeasons, editSeason);

        verify(seasonService).applySplit(existingSeasons, editSeason, systemDate);
    }

    @Test
    public void applySplit_PriceDiff() throws Exception {
        RoomsConfigurationPresenter presenter = spy(this.presenter);
        List<AccomClassMinPriceDiffSeason> existingSeasons = Arrays.asList(new AccomClassMinPriceDiffSeason(), new AccomClassMinPriceDiffSeason());
        AccomClassMinPriceDiffSeason editSeason = new AccomClassMinPriceDiffSeason();
        LocalDate systemDate = new LocalDate();
        doReturn(systemDate).when(presenter).getSystemDateAsLocalDate();

        presenter.applySplit(existingSeasons, editSeason);

        verify(seasonService).applySplit(existingSeasons, editSeason, systemDate);
    }

    @Test
    public void saveOverbookingSeasons() throws Exception {
        AccomType accomType1 = new AccomType();
        AccomType accomType2 = new AccomType();
        OverbookingAccomTypeSeasonDto dto1 = new OverbookingAccomTypeSeasonDto(accomType1);
        OverbookingAccomTypeSeasonDto dto2 = new OverbookingAccomTypeSeasonDto(accomType2);
        OverbookingSeasonUIWrapper overbookingSeasonUIWrapper1 = new OverbookingSeasonUIWrapper(dto1);
        OverbookingSeasonUIWrapper overbookingSeasonUIWrapper2 = new OverbookingSeasonUIWrapper(dto2);
        List<OverbookingSeasonUIWrapper> existingSeasons = Arrays.asList(overbookingSeasonUIWrapper1, overbookingSeasonUIWrapper2);

        presenter.saveOverbookingSeasons(existingSeasons);

        verify(overbookingService).saveOverbookingAccomTypeSeasonDtos(Arrays.asList(dto1, dto2));
    }

    @Test
    public void isPriceRankingPreviouslyConfigured() throws Exception {
        when(accomClassPriceRankService.isPriceRankingPreviouslyConfigured()).thenReturn(true);

        assertTrue(presenter.isPriceRankingPreviouslyConfigured());
    }

    @Test
    public void isNewUnconfiguredRoomClassPresent() throws Exception {
        when(accomClassPriceRankService.isPriceRankingPreviouslyConfigured()).thenReturn(false);

        assertFalse(presenter.isNewUnconfiguredRoomClassPresent());

        when(accomClassPriceRankService.isPriceRankingPreviouslyConfigured()).thenReturn(true);

        AccomClass accomClass1 = new AccomClass();
        accomClass1.setId(1);
        accomClass1.setViewOrder(3);
        accomClass1.setIsPriceRankConfigured(AccomClassPriceRankStatus.COMPLETE_RANKED);
        AccomClass accomClass2 = new AccomClass();
        accomClass2.setId(2);
        accomClass2.setViewOrder(4);
        accomClass2.setIsPriceRankConfigured(AccomClassPriceRankStatus.COMPLETE_RANKED);
        AccomClass accomClass3 = new AccomClass();
        accomClass3.setId(3);
        accomClass3.setViewOrder(1);
        accomClass3.setSystemDefault(1);
        accomClass3.setIsPriceRankConfigured(AccomClassPriceRankStatus.INCOMPLETE);
        LinkedList<AccomClass> rankedAccomClasses = new LinkedList<>(Arrays.asList(accomClass3, accomClass1, accomClass2));
        SortedSet<AccomClass> presenterAccomClasses = getAccomClassesSetForTesting(rankedAccomClasses);
        FieldUtils.writeField(presenter, "accomClasses", presenterAccomClasses, true);

        //Default class is not considered
        assertFalse(presenter.isNewUnconfiguredRoomClassPresent());

        accomClass2.setIsPriceRankConfigured(AccomClassPriceRankStatus.INCOMPLETE);
        FieldUtils.writeField(presenter, "accomClasses", presenterAccomClasses, true);

        assertTrue(presenter.isNewUnconfiguredRoomClassPresent());
    }

    @Test
    public void isNewUnconfiguredRoomClassPresent_isInitialConfiguration() throws Exception {
        when(accomClassPriceRankService.isPriceRankingPreviouslyConfigured()).thenReturn(false);

        assertFalse(presenter.isNewUnconfiguredRoomClassPresent());

        when(accomClassPriceRankService.isPriceRankingPreviouslyConfigured()).thenReturn(true);

        AccomClass accomClass1 = new AccomClass();
        accomClass1.setId(1);
        accomClass1.setViewOrder(3);
        accomClass1.setIsPriceRankConfigured(AccomClassPriceRankStatus.COMPLETE_RANKED);
        AccomClass accomClass2 = new AccomClass();
        accomClass2.setId(2);
        accomClass2.setViewOrder(4);
        accomClass2.setIsPriceRankConfigured(AccomClassPriceRankStatus.COMPLETE_RANKED);
        AccomClass accomClass3 = new AccomClass();
        accomClass3.setId(3);
        accomClass3.setViewOrder(1);
        accomClass3.setSystemDefault(1);
        accomClass3.setIsPriceRankConfigured(AccomClassPriceRankStatus.INCOMPLETE);
        LinkedList<AccomClass> rankedAccomClasses = new LinkedList<>(Arrays.asList(accomClass3, accomClass1, accomClass2));
        SortedSet<AccomClass> presenterAccomClasses = getAccomClassesSetForTesting(rankedAccomClasses);
        FieldUtils.writeField(presenter, "accomClasses", presenterAccomClasses, true);

        FieldUtils.writeField(presenter, "isInitialConfiguration", true, true);

        //Default class is not considered
        assertFalse(presenter.isNewUnconfiguredRoomClassPresent());

        accomClass2.setIsPriceRankConfigured(AccomClassPriceRankStatus.INCOMPLETE);
        FieldUtils.writeField(presenter, "accomClasses", presenterAccomClasses, true);

        assertFalse(presenter.isNewUnconfiguredRoomClassPresent());
    }

    @Test
    public void getAccomClassFromPresenter() throws Exception {
        AccomClass accomClass1 = new AccomClass();
        accomClass1.setId(1);
        accomClass1.setViewOrder(3);
        accomClass1.setIsPriceRankConfigured(AccomClassPriceRankStatus.COMPLETE_RANKED);
        AccomClass accomClass2 = new AccomClass();
        accomClass2.setId(2);
        accomClass2.setViewOrder(4);
        accomClass2.setIsPriceRankConfigured(AccomClassPriceRankStatus.COMPLETE_RANKED);

        LinkedList<AccomClass> rankedAccomClasses = new LinkedList<>(Arrays.asList(accomClass1, accomClass2));
        SortedSet<AccomClass> presenterAccomClasses = getAccomClassesSetForTesting(rankedAccomClasses);

        FieldUtils.writeField(presenter, "accomClasses", presenterAccomClasses, true);

        AccomClass accomClassToFind = new AccomClass();
        accomClassToFind.setId(1);
        assertEquals(accomClass1, presenter.getAccomClassFromPresenter(accomClassToFind));

        accomClassToFind.setId(3);
        assertNull(presenter.getAccomClassFromPresenter(accomClassToFind));
    }

    @Test
    public void loadMinimumPriceDifferentialData_TaxInclusive() throws IllegalAccessException {
        //given
        presenter.setCPProperty(true);
        Tax tax = new Tax();
        when(taxService.findTax()).thenReturn(tax);
        presenter.onViewOpened(null);

        List<AccomClassMinPriceDiff> defaultPriceDiffs = Arrays.asList(new AccomClassMinPriceDiff(), new AccomClassMinPriceDiff());
        List<AccomClassMinPriceDiffSeason> seasonPriceDiffs = Arrays.asList(new AccomClassMinPriceDiffSeason(), new AccomClassMinPriceDiffSeason());
        when(pricingConfigurationService.getDefaultAccomClassMinPriceDiff()).thenReturn(defaultPriceDiffs);
        when(pricingConfigurationService.getSeasonAccomClassMinPriceDiffs()).thenReturn(seasonPriceDiffs);

        AccomClassPriceRank priceRank1 = new AccomClassPriceRank();
        AccomClassPriceRank priceRank2 = new AccomClassPriceRank();

        LinkedList<AccomClassPriceRank> accomClassPriceRankings = new LinkedList<>(Arrays.asList(priceRank1, priceRank2));
        FieldUtils.writeField(presenter, "accomClassPriceRankings", accomClassPriceRankings, true);

        //when
        presenter.loadMinimumPriceDifferentialData();

        //then
        List<MinimumPriceDifferentialUIWrapper> currentUIWrappers = presenter.getCurrentUIWrappers();
        assertTrue(currentUIWrappers.stream().allMatch(wrapper -> wrapper.getTax() != null));
    }

    @Test
    public void loadMinimumPriceDifferentialData_dataExists() throws Exception {
        List<AccomClassMinPriceDiff> defaultPriceDiffs = Arrays.asList(new AccomClassMinPriceDiff(), new AccomClassMinPriceDiff());
        List<AccomClassMinPriceDiffSeason> seasonPriceDiffs = Arrays.asList(new AccomClassMinPriceDiffSeason(), new AccomClassMinPriceDiffSeason());
        when(pricingConfigurationService.getDefaultAccomClassMinPriceDiff()).thenReturn(defaultPriceDiffs);
        when(pricingConfigurationService.getSeasonAccomClassMinPriceDiffs()).thenReturn(seasonPriceDiffs);

        AccomClassPriceRank priceRank1 = new AccomClassPriceRank();
        AccomClassPriceRank priceRank2 = new AccomClassPriceRank();

        LinkedList<AccomClassPriceRank> accomClassPriceRankings = new LinkedList<>(Arrays.asList(priceRank1, priceRank2));
        FieldUtils.writeField(presenter, "accomClassPriceRankings", accomClassPriceRankings, true);

        presenter.loadMinimumPriceDifferentialData();

        assertEquals(defaultPriceDiffs, presenter.getCurrentDefaultMinPriceDiffs());
        assertEquals(seasonPriceDiffs, presenter.getCurrentDefaultMinPriceDiffsSeasons());
        verify(pricingConfigurationService).getDefaultAccomClassMinPriceDiff();
        verify(pricingConfigurationService).getSeasonAccomClassMinPriceDiffs();
    }

    @Test
    public void loadMinimumPriceDifferentialData_PartialData() throws Exception {
        AccomClassMinPriceDiff accomClassMinPriceDiff = new AccomClassMinPriceDiff();

        List<AccomClassMinPriceDiff> defaultPriceDiffs = new ArrayList<>();
        defaultPriceDiffs.add(accomClassMinPriceDiff);
        when(pricingConfigurationService.getDefaultAccomClassMinPriceDiff()).thenReturn(defaultPriceDiffs);

        AccomClass accomClass1 = new AccomClass();
        accomClass1.setId(1);
        accomClass1.setViewOrder(3);
        AccomClass accomClass2 = new AccomClass();
        accomClass2.setId(2);
        accomClass2.setViewOrder(4);
        AccomClass accomClass3 = new AccomClass();
        accomClass3.setId(3);
        accomClass3.setViewOrder(1);

        AccomClassPriceRank priceRank1 = new AccomClassPriceRank(accomClass1, accomClass2, true);
        priceRank1.setId(1);
        AccomClassPriceRank priceRank2 = new AccomClassPriceRank(accomClass2, accomClass3, false);
        priceRank2.setId(2);
        accomClassMinPriceDiff.setAccomClassPriceRank(priceRank1);
        LinkedList<AccomClassPriceRank> accomClassPriceRankings = new LinkedList<>(Arrays.asList(priceRank1, priceRank2));

        FieldUtils.writeField(presenter, "accomClassPriceRankings", accomClassPriceRankings, true);

        List<AccomClassMinPriceDiffSeason> seasonPriceDiffs = Arrays.asList(new AccomClassMinPriceDiffSeason(), new AccomClassMinPriceDiffSeason());
        when(pricingConfigurationService.getSeasonAccomClassMinPriceDiffs()).thenReturn(seasonPriceDiffs);
        presenter.setCPProperty(true);

        presenter.loadMinimumPriceDifferentialData();

        assertEquals(2, presenter.getCurrentUIWrappers().size());
        assertEquals(accomClassMinPriceDiff, presenter.getCurrentDefaultMinPriceDiffs().get(0));
        assertEquals(priceRank2, presenter.getCurrentDefaultMinPriceDiffs().get(1).getAccomClassPriceRank());
        assertEquals(BigDecimal.ZERO, presenter.getCurrentUIWrappers().get(1).getSimplePathDiff());

        assertEquals(seasonPriceDiffs, presenter.getCurrentDefaultMinPriceDiffsSeasons());
        verify(pricingConfigurationService).getDefaultAccomClassMinPriceDiff();
        verify(pricingConfigurationService).getSeasonAccomClassMinPriceDiffs();
    }

    @Test
    public void loadMinimumPriceDifferentialData_NoDefaultData_CpEnabled() throws Exception {
        AccomClass accomClass1 = new AccomClass();
        accomClass1.setId(1);
        accomClass1.setViewOrder(3);
        AccomClass accomClass2 = new AccomClass();
        accomClass2.setId(2);
        accomClass2.setViewOrder(4);
        AccomClass accomClass3 = new AccomClass();
        accomClass3.setId(3);
        accomClass3.setViewOrder(1);

        AccomClassPriceRank priceRank1 = new AccomClassPriceRank(accomClass1, accomClass2, true);
        priceRank1.setId(1);
        AccomClassPriceRank priceRank2 = new AccomClassPriceRank(accomClass2, accomClass3, false);
        priceRank2.setId(2);

        LinkedList<AccomClassPriceRank> accomClassPriceRankings = new LinkedList<>(Arrays.asList(priceRank1, priceRank2));

        FieldUtils.writeField(presenter, "accomClassPriceRankings", accomClassPriceRankings, true);

        List<AccomClassMinPriceDiffSeason> seasonPriceDiffs = Arrays.asList(new AccomClassMinPriceDiffSeason(), new AccomClassMinPriceDiffSeason());
        when(pricingConfigurationService.getDefaultAccomClassMinPriceDiff()).thenReturn(new ArrayList<>());
        when(pricingConfigurationService.getSeasonAccomClassMinPriceDiffs()).thenReturn(seasonPriceDiffs);

        presenter.setCPProperty(true);

        presenter.loadMinimumPriceDifferentialData();

        assertEquals(2, presenter.getCurrentDefaultMinPriceDiffs().size());
        assertEquals(priceRank1, presenter.getCurrentDefaultMinPriceDiffs().get(0).getAccomClassPriceRank());
        assertEquals(BigDecimal.ZERO, presenter.getCurrentUIWrappers().get(0).getSimplePathDiff());
        assertEquals(priceRank2, presenter.getCurrentDefaultMinPriceDiffs().get(1).getAccomClassPriceRank());
        assertEquals(BigDecimal.ZERO, presenter.getCurrentUIWrappers().get(1).getSimplePathDiff());

        assertEquals(seasonPriceDiffs, presenter.getCurrentDefaultMinPriceDiffsSeasons());
        verify(pricingConfigurationService).getDefaultAccomClassMinPriceDiff();
        verify(pricingConfigurationService).getSeasonAccomClassMinPriceDiffs();
    }

    @Test
    public void loadMinimumPriceDifferentialData_NoDefaultData_CpDisabled() throws Exception {
        AccomClass accomClass1 = new AccomClass();
        accomClass1.setId(1);
        accomClass1.setViewOrder(3);
        AccomClass accomClass2 = new AccomClass();
        accomClass2.setId(2);
        accomClass2.setViewOrder(4);
        AccomClass accomClass3 = new AccomClass();
        accomClass3.setId(3);
        accomClass3.setViewOrder(1);

        AccomClassPriceRank priceRank1 = new AccomClassPriceRank(accomClass1, accomClass2, true);
        priceRank1.setId(1);
        AccomClassPriceRank priceRank2 = new AccomClassPriceRank(accomClass2, accomClass3, false);
        priceRank2.setId(2);

        LinkedList<AccomClassPriceRank> accomClassPriceRankings = new LinkedList<>(Arrays.asList(priceRank1, priceRank2));

        FieldUtils.writeField(presenter, "accomClassPriceRankings", accomClassPriceRankings, true);

        List<AccomClassMinPriceDiffSeason> seasonPriceDiffs = Arrays.asList(new AccomClassMinPriceDiffSeason(), new AccomClassMinPriceDiffSeason());
        when(pricingConfigurationService.getDefaultAccomClassMinPriceDiff()).thenReturn(new ArrayList<>());
        when(pricingConfigurationService.getSeasonAccomClassMinPriceDiffs()).thenReturn(seasonPriceDiffs);

        presenter.setCPProperty(false);

        presenter.loadMinimumPriceDifferentialData();

        assertEquals(2, presenter.getCurrentDefaultMinPriceDiffs().size());
        assertEquals(priceRank1, presenter.getCurrentDefaultMinPriceDiffs().get(0).getAccomClassPriceRank());
        assertEquals(new BigDecimal("0.01"), presenter.getCurrentUIWrappers().get(0).getSimplePathDiff());
        assertEquals(priceRank2, presenter.getCurrentDefaultMinPriceDiffs().get(1).getAccomClassPriceRank());
        assertEquals(new BigDecimal("0.01"), presenter.getCurrentUIWrappers().get(1).getSimplePathDiff());

        assertEquals(seasonPriceDiffs, presenter.getCurrentDefaultMinPriceDiffsSeasons());
        verify(pricingConfigurationService).getDefaultAccomClassMinPriceDiff();
        verify(pricingConfigurationService).getSeasonAccomClassMinPriceDiffs();
    }

    @Test
    public void saveMinimumPriceDifferentialData() throws Exception {
        //given
        presenter = spy(presenter);

        AccomClassMinPriceDiff accomClassMinPriceDiff1 = new AccomClassMinPriceDiff();
        AccomClassPriceRank acpr1 = new AccomClassPriceRank();
        acpr1.setId(1);
        accomClassMinPriceDiff1.setAccomClassPriceRank(acpr1);
        MinimumPriceDifferentialUIWrapper uiWrapper1 = new MinimumPriceDifferentialUIWrapper(accomClassMinPriceDiff1);

        AccomClassMinPriceDiff accomClassMinPriceDiff2 = new AccomClassMinPriceDiff();
        AccomClassPriceRank acpr2 = new AccomClassPriceRank();
        acpr2.setId(2);
        accomClassMinPriceDiff2.setAccomClassPriceRank(acpr2);
        MinimumPriceDifferentialUIWrapper uiWrapper2 = new MinimumPriceDifferentialUIWrapper(accomClassMinPriceDiff2);

        presenter.setDiffUiWrappers(Arrays.asList(uiWrapper1, uiWrapper2));
        presenter.setMinPriceDiffOriginalValues(Arrays.asList(uiWrapper1.getPriceDiff(), uiWrapper2.getPriceDiff()));

        AccomClass accomClass = new AccomClass();
        SortedSet<AccomClass> accomClasses = getAccomClassesSetForTesting(Collections.singletonList(accomClass));
        FieldUtils.writeField(presenter, "accomClasses", accomClasses, true);
        doNothing().when(presenter).sync();

        //when
        presenter.saveMinimumPriceDifferentialData();

        //then
        List<AccomClassMinPriceDiff> accomClassMinPriceDiffs = Arrays.asList((AccomClassMinPriceDiff) uiWrapper1.getPriceDiff(), (AccomClassMinPriceDiff) uiWrapper2.getPriceDiff());
        verify(pricingConfigurationService).saveAccomClassMinPriceDiffs(accomClassMinPriceDiffs);
        verify(agileRatesConfigurationService, never()).changedRoomClassConfig(any());
        assertEquals(accomClass.getIsPriceRankConfigured(), AccomClassPriceRankStatus.COMPLETE_RANKED);
    }

    @Test
    void shouldCallToEnableLTBDEForPricingFlagWhenThereAreNoExistingPriceDifferentialDataIsAvailable() throws Exception {
        presenter = spy(presenter);

        AccomClassMinPriceDiff accomClassMinPriceDiff1 = new AccomClassMinPriceDiff();
        AccomClassPriceRank acpr1 = new AccomClassPriceRank();
        acpr1.setId(1);
        accomClassMinPriceDiff1.setAccomClassPriceRank(acpr1);
        MinimumPriceDifferentialUIWrapper uiWrapper1 = new MinimumPriceDifferentialUIWrapper(accomClassMinPriceDiff1);

        AccomClassMinPriceDiff accomClassMinPriceDiff2 = new AccomClassMinPriceDiff();
        AccomClassPriceRank acpr2 = new AccomClassPriceRank();
        acpr2.setId(2);
        accomClassMinPriceDiff2.setAccomClassPriceRank(acpr2);
        MinimumPriceDifferentialUIWrapper uiWrapper2 = new MinimumPriceDifferentialUIWrapper(accomClassMinPriceDiff2);

        presenter.setDiffUiWrappers(Arrays.asList(uiWrapper1, uiWrapper2));

        presenter.setMinPriceDiffOriginalValues(Collections.emptyList());

        AccomClass accomClass = new AccomClass();
        SortedSet<AccomClass> accomClasses = getAccomClassesSetForTesting(Collections.singletonList(accomClass));
        FieldUtils.writeField(presenter, "accomClasses", accomClasses, true);
        doNothing().when(presenter).sync();
        when(pricingConfigurationLTBDEService.isLTBDEOnIndirectConfigChangedEnabled()).thenReturn(true);

        presenter.saveMinimumPriceDifferentialData();

        List<AccomClassMinPriceDiff> accomClassMinPriceDiffs = Arrays.asList((AccomClassMinPriceDiff) uiWrapper1.getPriceDiff(), (AccomClassMinPriceDiff) uiWrapper2.getPriceDiff());
        verify(pricingConfigurationService).saveAccomClassMinPriceDiffs(accomClassMinPriceDiffs);
        verify(agileRatesConfigurationService, never()).changedRoomClassConfig(any());
        verify(pricingConfigurationLTBDEService).enableLTBDEForPricing(true);
    }

    @Test
    void shouldNotCallToEnableLTBDEForPricingFlagWhenThereIsChangeMinPriceDiffForAnyRoomClassAndLTBDEConfigToggleIsNotEnabled() throws Exception {
        presenter = spy(presenter);

        AccomClassMinPriceDiff accomClassMinPriceDiff1 = new AccomClassMinPriceDiff();
        AccomClassPriceRank acpr1 = new AccomClassPriceRank();
        acpr1.setId(1);
        accomClassMinPriceDiff1.setAccomClassPriceRank(acpr1);
        accomClassMinPriceDiff1.setFridayDiffWithTax(BigDecimal.TEN);
        MinimumPriceDifferentialUIWrapper uiWrapper1 = new MinimumPriceDifferentialUIWrapper(accomClassMinPriceDiff1);

        AccomClassMinPriceDiff accomClassMinPriceDiff2 = new AccomClassMinPriceDiff();
        AccomClassPriceRank acpr2 = new AccomClassPriceRank();
        acpr2.setId(2);
        accomClassMinPriceDiff2.setAccomClassPriceRank(acpr2);
        accomClassMinPriceDiff2.setFridayDiffWithTax(BigDecimal.ONE);
        MinimumPriceDifferentialUIWrapper uiWrapper2 = new MinimumPriceDifferentialUIWrapper(accomClassMinPriceDiff2);

        presenter.setDiffUiWrappers(Arrays.asList(uiWrapper1, uiWrapper2));

        DailyMinPriceDiff minPriceDiff1 = new AccomClassMinPriceDiff();
        minPriceDiff1.setAccomClassPriceRank(acpr1);
        minPriceDiff1.setFridayDiffWithTax(BigDecimal.ONE);
        DailyMinPriceDiff minPriceDiff2 = new AccomClassMinPriceDiff();
        minPriceDiff2.setAccomClassPriceRank(acpr2);
        minPriceDiff2.setFridayDiffWithTax(BigDecimal.ONE);

        presenter.setMinPriceDiffOriginalValues(List.of(minPriceDiff1, minPriceDiff2));

        AccomClass accomClass = new AccomClass();
        SortedSet<AccomClass> accomClasses = getAccomClassesSetForTesting(Collections.singletonList(accomClass));
        FieldUtils.writeField(presenter, "accomClasses", accomClasses, true);
        doNothing().when(presenter).sync();
        when(pricingConfigurationLTBDEService.isLTBDEOnConfigChangeEnabled()).thenReturn(false);

        presenter.saveMinimumPriceDifferentialData();

        List<AccomClassMinPriceDiff> accomClassMinPriceDiffs = Arrays.asList((AccomClassMinPriceDiff) uiWrapper1.getPriceDiff(), (AccomClassMinPriceDiff) uiWrapper2.getPriceDiff());
        verify(pricingConfigurationService).saveAccomClassMinPriceDiffs(accomClassMinPriceDiffs);
        verify(agileRatesConfigurationService, never()).changedRoomClassConfig(any());
        verify(pricingConfigurationLTBDEService, times(0)).enableLTBDEForPricing(true);
    }

    @Test
    void shouldCallToEnableLTBDEForPricingFlagWhenThereIsChangeMinPriceDiffForAnyRoomClass() throws Exception {
        presenter = spy(presenter);

        AccomClassMinPriceDiff accomClassMinPriceDiff1 = new AccomClassMinPriceDiff();
        AccomClassPriceRank acpr1 = new AccomClassPriceRank();
        acpr1.setId(1);
        accomClassMinPriceDiff1.setAccomClassPriceRank(acpr1);
        accomClassMinPriceDiff1.setFridayDiffWithTax(BigDecimal.TEN);
        MinimumPriceDifferentialUIWrapper uiWrapper1 = new MinimumPriceDifferentialUIWrapper(accomClassMinPriceDiff1);

        AccomClassMinPriceDiff accomClassMinPriceDiff2 = new AccomClassMinPriceDiff();
        AccomClassPriceRank acpr2 = new AccomClassPriceRank();
        acpr2.setId(2);
        accomClassMinPriceDiff2.setAccomClassPriceRank(acpr2);
        accomClassMinPriceDiff2.setFridayDiffWithTax(BigDecimal.ONE);
        MinimumPriceDifferentialUIWrapper uiWrapper2 = new MinimumPriceDifferentialUIWrapper(accomClassMinPriceDiff2);

        presenter.setDiffUiWrappers(Arrays.asList(uiWrapper1, uiWrapper2));

        DailyMinPriceDiff minPriceDiff1 = new AccomClassMinPriceDiff();
        minPriceDiff1.setAccomClassPriceRank(acpr1);
        minPriceDiff1.setFridayDiffWithTax(BigDecimal.ONE);
        DailyMinPriceDiff minPriceDiff2 = new AccomClassMinPriceDiff();
        minPriceDiff2.setAccomClassPriceRank(acpr2);
        minPriceDiff2.setFridayDiffWithTax(BigDecimal.ONE);

        presenter.setMinPriceDiffOriginalValues(List.of(minPriceDiff1, minPriceDiff2));

        AccomClass accomClass = new AccomClass();
        SortedSet<AccomClass> accomClasses = getAccomClassesSetForTesting(Collections.singletonList(accomClass));
        FieldUtils.writeField(presenter, "accomClasses", accomClasses, true);
        doNothing().when(presenter).sync();
        when(pricingConfigurationLTBDEService.isLTBDEOnIndirectConfigChangedEnabled()).thenReturn(true);

        presenter.saveMinimumPriceDifferentialData();

        List<AccomClassMinPriceDiff> accomClassMinPriceDiffs = Arrays.asList((AccomClassMinPriceDiff) uiWrapper1.getPriceDiff(), (AccomClassMinPriceDiff) uiWrapper2.getPriceDiff());
        verify(pricingConfigurationService).saveAccomClassMinPriceDiffs(accomClassMinPriceDiffs);
        verify(agileRatesConfigurationService, never()).changedRoomClassConfig(any());
        verify(pricingConfigurationLTBDEService).enableLTBDEForPricing(true);
    }

    @Test
    void shouldNotCallToEnableLTBDEForPricingFlagWhenThereIsNoChangeMinPriceDiffForAnyRoomClass() throws Exception {

        presenter = spy(presenter);

        AccomClassMinPriceDiff accomClassMinPriceDiff1 = new AccomClassMinPriceDiff();
        AccomClassPriceRank acpr1 = new AccomClassPriceRank();
        acpr1.setId(1);
        accomClassMinPriceDiff1.setAccomClassPriceRank(acpr1);
        accomClassMinPriceDiff1.setFridayDiffWithTax(BigDecimal.ONE);
        MinimumPriceDifferentialUIWrapper uiWrapper1 = new MinimumPriceDifferentialUIWrapper(accomClassMinPriceDiff1);

        AccomClassMinPriceDiff accomClassMinPriceDiff2 = new AccomClassMinPriceDiff();
        AccomClassPriceRank acpr2 = new AccomClassPriceRank();
        acpr2.setId(2);
        accomClassMinPriceDiff2.setAccomClassPriceRank(acpr2);
        accomClassMinPriceDiff2.setFridayDiffWithTax(BigDecimal.ONE);
        MinimumPriceDifferentialUIWrapper uiWrapper2 = new MinimumPriceDifferentialUIWrapper(accomClassMinPriceDiff2);

        presenter.setDiffUiWrappers(Arrays.asList(uiWrapper1, uiWrapper2));

        DailyMinPriceDiff minPriceDiff1 = new AccomClassMinPriceDiff();
        minPriceDiff1.setAccomClassPriceRank(acpr1);
        minPriceDiff1.setFridayDiffWithTax(BigDecimal.ONE);
        DailyMinPriceDiff minPriceDiff2 = new AccomClassMinPriceDiff();
        minPriceDiff2.setAccomClassPriceRank(acpr2);
        minPriceDiff2.setFridayDiffWithTax(BigDecimal.ONE);

        presenter.setMinPriceDiffOriginalValues(List.of(minPriceDiff1, minPriceDiff2));

        AccomClass accomClass = new AccomClass();
        SortedSet<AccomClass> accomClasses = getAccomClassesSetForTesting(Collections.singletonList(accomClass));
        FieldUtils.writeField(presenter, "accomClasses", accomClasses, true);
        doNothing().when(presenter).sync();
        when(pricingConfigurationLTBDEService.isLTBDEOnConfigChangeEnabled()).thenReturn(true);

        presenter.saveMinimumPriceDifferentialData();

        List<AccomClassMinPriceDiff> accomClassMinPriceDiffs = Arrays.asList((AccomClassMinPriceDiff) uiWrapper1.getPriceDiff(), (AccomClassMinPriceDiff) uiWrapper2.getPriceDiff());
        verify(pricingConfigurationService).saveAccomClassMinPriceDiffs(accomClassMinPriceDiffs);
        verify(agileRatesConfigurationService, never()).changedRoomClassConfig(any());
        verify(pricingConfigurationLTBDEService, times(0)).enableLTBDEForPricing(true);
    }

    @Test
    public void saveMinimumPriceDifferentialDataMinimumPriceDecreased() throws Exception {
        //given
        presenter = spy(presenter);

        AccomClassMinPriceDiff accomClassMinPriceDiff1 = new AccomClassMinPriceDiff();
        AccomClassPriceRank acpr1 = new AccomClassPriceRank();
        acpr1.setId(1);
        accomClassMinPriceDiff1.setAccomClassPriceRank(acpr1);
        accomClassMinPriceDiff1.setSundayDiff(BigDecimal.TEN);
        MinimumPriceDifferentialUIWrapper uiWrapper1 = new MinimumPriceDifferentialUIWrapper(accomClassMinPriceDiff1);

        AccomClassMinPriceDiff accomClassMinPriceDiff2 = new AccomClassMinPriceDiff();
        AccomClassPriceRank acpr2 = new AccomClassPriceRank();
        acpr2.setId(2);
        accomClassMinPriceDiff2.setAccomClassPriceRank(acpr2);
        accomClassMinPriceDiff2.setSundayDiff(BigDecimal.TEN);
        MinimumPriceDifferentialUIWrapper uiWrapper2 = new MinimumPriceDifferentialUIWrapper(accomClassMinPriceDiff2);

        presenter.setDiffUiWrappers(Arrays.asList(uiWrapper1, uiWrapper2));

        MinimumPriceDifferentialUIWrapper uiWrapper1Copy = uiWrapper1.copy();
        MinimumPriceDifferentialUIWrapper uiWrapper2Copy = uiWrapper2.copy();
        presenter.setMinPriceDiffOriginalValues(Arrays.asList(uiWrapper1Copy.getPriceDiff(), uiWrapper2Copy.getPriceDiff()));
        uiWrapper1.getPriceDiff().setSundayDiff(BigDecimal.ONE);

        AccomClass accomClass = new AccomClass();
        SortedSet<AccomClass> accomClasses = getAccomClassesSetForTesting(Collections.singletonList(accomClass));
        FieldUtils.writeField(presenter, "accomClasses", accomClasses, true);
        doNothing().when(presenter).sync();
        doReturn(true).when(syncEventAggregatorService).registerSyncEvent(SyncEvent.MINIMUM_PRICE_DIFFERENTIAL_DECREASED);

        //when
        presenter.saveMinimumPriceDifferentialData();

        //then
        List<AccomClassMinPriceDiff> accomClassMinPriceDiffs = Arrays.asList((AccomClassMinPriceDiff) uiWrapper1.getPriceDiff(), (AccomClassMinPriceDiff) uiWrapper2.getPriceDiff());
        verify(pricingConfigurationService).saveAccomClassMinPriceDiffs(accomClassMinPriceDiffs);
        verify(syncEventAggregatorService).registerSyncEvent(SyncEvent.MINIMUM_PRICE_DIFFERENTIAL_DECREASED);
        verify(agileRatesConfigurationService, never()).changedRoomClassConfig(any());
        assertEquals(accomClass.getIsPriceRankConfigured(), AccomClassPriceRankStatus.COMPLETE_RANKED);
    }

    @Test
    public void saveMinimumPriceDifferentialDataMinimumPriceDecreasedSeason() throws Exception {
        //given
        presenter = spy(presenter);
        LocalDate today = LocalDate.now();
        LocalDate tomorrow = LocalDate.now().plusDays(1);

        AccomClassMinPriceDiffSeason accomClassMinPriceDiff1 = new AccomClassMinPriceDiffSeason();
        accomClassMinPriceDiff1.setStartDate(today);
        accomClassMinPriceDiff1.setEndDate(tomorrow);
        MinimumPriceDifferentialUIWrapper uiWrapper1 = new MinimumPriceDifferentialUIWrapper(accomClassMinPriceDiff1);

        AccomClassMinPriceDiffSeason accomClassMinPriceDiff2 = new AccomClassMinPriceDiffSeason();
        accomClassMinPriceDiff2.setStartDate(today);
        accomClassMinPriceDiff2.setEndDate(tomorrow);
        MinimumPriceDifferentialUIWrapper uiWrapper2 = new MinimumPriceDifferentialUIWrapper(accomClassMinPriceDiff2);

        presenter.setSeasonDiffUiWrappers(Arrays.asList(uiWrapper1, uiWrapper2));

        MinimumPriceDifferentialUIWrapper uiWrapper1Copy = uiWrapper1.copy();
        uiWrapper1Copy.getPriceDiff().setSundayDiffWithTax(BigDecimal.TEN);
        MinimumPriceDifferentialUIWrapper uiWrapper2Copy = uiWrapper2.copy();
        uiWrapper2Copy.getPriceDiff().setSundayDiffWithTax(BigDecimal.TEN);
        presenter.setSeasonMinPriceDiffOriginalValues(Arrays.asList((AccomClassMinPriceDiffSeason) uiWrapper1Copy.getPriceDiff(), (AccomClassMinPriceDiffSeason) uiWrapper2Copy.getPriceDiff()));
        uiWrapper1.getPriceDiff().setSundayDiffWithTax(BigDecimal.ONE);
        uiWrapper2.getPriceDiff().setSundayDiffWithTax(BigDecimal.ONE);

        AccomClass accomClass = new AccomClass();
        SortedSet<AccomClass> accomClasses = getAccomClassesSetForTesting(Collections.singletonList(accomClass));
        FieldUtils.writeField(presenter, "accomClasses", accomClasses, true);
        doNothing().when(presenter).sync();
        doReturn(true).when(syncEventAggregatorService).registerSyncEvent(SyncEvent.MINIMUM_PRICE_DIFFERENTIAL_DECREASED);

        //when
        presenter.saveMinimumPriceDifferentialSeasons(Arrays.asList(uiWrapper1, uiWrapper2));

        //then
        List<AccomClassMinPriceDiffSeason> accomClassMinPriceDiffs = Arrays.asList((AccomClassMinPriceDiffSeason) uiWrapper1.getPriceDiff(), (AccomClassMinPriceDiffSeason) uiWrapper2.getPriceDiff());
        verify(pricingConfigurationService).saveAccomClassMinPriceDiffSeasons(accomClassMinPriceDiffs);
        verify(syncEventAggregatorService).registerSyncEvent(SyncEvent.MINIMUM_PRICE_DIFFERENTIAL_DECREASED);
    }

    @Test
    public void completeWizard() throws Exception {
        presenter.completeWizard();

        verify(view).completeWizard();

        assertFalse(presenter.hasChanges(RoomConfigurationSelection.ROOM_TYPE_MAPPING));
        assertFalse(presenter.hasChanges(RoomConfigurationSelection.COST_OF_WALK));
        assertFalse(presenter.hasChanges(RoomConfigurationSelection.OVERBOOKING));
        assertFalse(presenter.hasChanges(RoomConfigurationSelection.PRICE_RANKING));
        assertFalse(presenter.hasChanges(RoomConfigurationSelection.PRICE_UPGRADE_PATH));
        assertFalse(presenter.hasChanges(RoomConfigurationSelection.MIN_PRICE_DIFFERENTIAL));
    }

    @Test
    public void updateHasChanges() throws Exception {
        // Initializing like would happen when the view inits
        Map<RoomConfigurationSelection, Boolean> hasChangesMap = new HashMap<>();
        hasChangesMap.put(RoomConfigurationSelection.OVERBOOKING, false);

        FieldUtils.writeField(presenter, "hasChangesMap", hasChangesMap, true);

        presenter.updateHasChanges(RoomConfigurationSelection.OVERBOOKING, false);

        assertFalse(presenter.hasChanges(RoomConfigurationSelection.OVERBOOKING));

        presenter.updateHasChanges(RoomConfigurationSelection.OVERBOOKING, true);

        assertTrue(presenter.hasChanges(RoomConfigurationSelection.OVERBOOKING));

        presenter.updateHasChanges(RoomConfigurationSelection.OVERBOOKING, false);

        assertTrue(presenter.hasChanges(RoomConfigurationSelection.OVERBOOKING));
    }

    @Test
    public void adjustPriceDiffSeasonsDayOfWeekData() throws Exception {
        AccomClassMinPriceDiffSeason season = new AccomClassMinPriceDiffSeason();
        season.setSundayDiff(BigDecimal.TEN);
        season.setMondayDiff(BigDecimal.TEN);
        season.setTuesdayDiff(BigDecimal.TEN);
        season.setWednesdayDiff(BigDecimal.TEN);
        season.setThursdayDiff(BigDecimal.TEN);
        season.setFridayDiff(BigDecimal.TEN);
        season.setSaturdayDiff(BigDecimal.TEN);
        season.setSundayDiffWithTax(BigDecimal.TEN);
        season.setMondayDiffWithTax(BigDecimal.TEN);
        season.setTuesdayDiffWithTax(BigDecimal.TEN);
        season.setWednesdayDiffWithTax(BigDecimal.TEN);
        season.setThursdayDiffWithTax(BigDecimal.TEN);
        season.setFridayDiffWithTax(BigDecimal.TEN);
        season.setSaturdayDiffWithTax(BigDecimal.TEN);

        LocalDate startDate = new LocalDate();
        season.setStartDate(startDate);
        season.setEndDate(startDate.plusDays(10));

        presenter.adjustPriceDiffSeasonsDayOfWeekData(Collections.singletonList(season));

        assertEquals(BigDecimal.TEN, season.getSundayDiff());
        assertEquals(BigDecimal.TEN, season.getMondayDiff());
        assertEquals(BigDecimal.TEN, season.getTuesdayDiff());
        assertEquals(BigDecimal.TEN, season.getWednesdayDiff());
        assertEquals(BigDecimal.TEN, season.getThursdayDiff());
        assertEquals(BigDecimal.TEN, season.getFridayDiff());
        assertEquals(BigDecimal.TEN, season.getSaturdayDiff());
        assertEquals(BigDecimal.TEN, season.getSundayDiffWithTax());
        assertEquals(BigDecimal.TEN, season.getMondayDiffWithTax());
        assertEquals(BigDecimal.TEN, season.getTuesdayDiffWithTax());
        assertEquals(BigDecimal.TEN, season.getWednesdayDiffWithTax());
        assertEquals(BigDecimal.TEN, season.getThursdayDiffWithTax());
        assertEquals(BigDecimal.TEN, season.getFridayDiffWithTax());
        assertEquals(BigDecimal.TEN, season.getSaturdayDiffWithTax());
    }

    @Test
    public void adjustPriceDiffSeasonsDayOfWeekData_OnlyFriday() throws Exception {
        AccomClassMinPriceDiffSeason season = new AccomClassMinPriceDiffSeason();
        season.setSundayDiff(BigDecimal.TEN);
        season.setMondayDiff(BigDecimal.TEN);
        season.setTuesdayDiff(BigDecimal.TEN);
        season.setWednesdayDiff(BigDecimal.TEN);
        season.setThursdayDiff(BigDecimal.TEN);
        season.setFridayDiff(BigDecimal.TEN);
        season.setSaturdayDiff(BigDecimal.TEN);
        season.setSundayDiffWithTax(BigDecimal.TEN);
        season.setMondayDiffWithTax(BigDecimal.TEN);
        season.setTuesdayDiffWithTax(BigDecimal.TEN);
        season.setWednesdayDiffWithTax(BigDecimal.TEN);
        season.setThursdayDiffWithTax(BigDecimal.TEN);
        season.setFridayDiffWithTax(BigDecimal.TEN);
        season.setSaturdayDiffWithTax(BigDecimal.TEN);

        LocalDate friday = new LocalDate().dayOfWeek().setCopy(5);
        season.setStartDate(friday);
        season.setEndDate(friday);

        presenter.adjustPriceDiffSeasonsDayOfWeekData(Collections.singletonList(season));

        assertNull(season.getSundayDiff());
        assertNull(season.getMondayDiff());
        assertNull(season.getTuesdayDiff());
        assertNull(season.getWednesdayDiff());
        assertNull(season.getThursdayDiff());
        assertEquals(BigDecimal.TEN, season.getFridayDiff());
        assertNull(season.getSaturdayDiffWithTax());
        assertNull(season.getSundayDiffWithTax());
        assertNull(season.getMondayDiffWithTax());
        assertNull(season.getTuesdayDiffWithTax());
        assertNull(season.getWednesdayDiffWithTax());
        assertNull(season.getThursdayDiffWithTax());
        assertEquals(BigDecimal.TEN, season.getFridayDiffWithTax());
        assertNull(season.getSaturdayDiffWithTax());
    }

    @Test
    public void adjustPriceDiffSeasonsDayOfWeekData_EverythingButFriday() throws Exception {
        AccomClassMinPriceDiffSeason season = new AccomClassMinPriceDiffSeason();
        season.setSundayDiff(BigDecimal.TEN);
        season.setMondayDiff(BigDecimal.TEN);
        season.setTuesdayDiff(BigDecimal.TEN);
        season.setWednesdayDiff(BigDecimal.TEN);
        season.setThursdayDiff(BigDecimal.TEN);
        season.setFridayDiff(BigDecimal.TEN);
        season.setSaturdayDiff(BigDecimal.TEN);
        season.setSundayDiffWithTax(BigDecimal.TEN);
        season.setMondayDiffWithTax(BigDecimal.TEN);
        season.setTuesdayDiffWithTax(BigDecimal.TEN);
        season.setWednesdayDiffWithTax(BigDecimal.TEN);
        season.setThursdayDiffWithTax(BigDecimal.TEN);
        season.setFridayDiffWithTax(BigDecimal.TEN);
        season.setSaturdayDiffWithTax(BigDecimal.TEN);

        LocalDate saturday = new LocalDate().dayOfWeek().setCopy(6);
        season.setStartDate(saturday);
        season.setEndDate(saturday.plusDays(5));

        presenter.adjustPriceDiffSeasonsDayOfWeekData(Collections.singletonList(season));

        assertEquals(BigDecimal.TEN, season.getSundayDiff());
        assertEquals(BigDecimal.TEN, season.getMondayDiff());
        assertEquals(BigDecimal.TEN, season.getTuesdayDiff());
        assertEquals(BigDecimal.TEN, season.getWednesdayDiff());
        assertEquals(BigDecimal.TEN, season.getThursdayDiff());
        assertNull(season.getFridayDiff());
        assertEquals(BigDecimal.TEN, season.getSaturdayDiff());
        assertEquals(BigDecimal.TEN, season.getSundayDiffWithTax());
        assertEquals(BigDecimal.TEN, season.getMondayDiffWithTax());
        assertEquals(BigDecimal.TEN, season.getTuesdayDiffWithTax());
        assertEquals(BigDecimal.TEN, season.getWednesdayDiffWithTax());
        assertEquals(BigDecimal.TEN, season.getThursdayDiffWithTax());
        assertNull(season.getFridayDiffWithTax());
        assertEquals(BigDecimal.TEN, season.getSaturdayDiffWithTax());
    }

    @Test
    public void adjustOverbookingSeasonsDayOfWeekData() throws Exception {
        OverbookingAccomTypeSeasonDto dto = new OverbookingAccomTypeSeasonDto(new AccomType());
        OverbookingSeasonUIWrapper season = new OverbookingSeasonUIWrapper(dto);
        season.setSunday(true);
        season.setMonday(true);
        season.setTuesday(true);
        season.setWednesday(true);
        season.setThursday(true);
        season.setFriday(true);
        season.setSaturday(true);

        LocalDate startDate = new LocalDate();
        season.setStartDate(startDate);
        season.setEndDate(startDate.plusDays(10));

        presenter.adjustOverbookingSeasonsDayOfWeekData(Collections.singletonList(season));

        assertTrue(season.isSunday());
        assertTrue(season.isMonday());
        assertTrue(season.isTuesday());
        assertTrue(season.isWednesday());
        assertTrue(season.isThursday());
        assertTrue(season.isFriday());
        assertTrue(season.isSaturday());
    }

    @Test
    public void isEnableLrvDropRestrictionEnabledTest() {
        presenter.setEnableLrvDropRestrictionEnabled(true);
        assertTrue(presenter.isEnableLrvDropRestrictionEnabled());
    }

    @Test
    public void adjustOverbookingSeasonsDayOfWeekData_OnlyFriday() throws Exception {
        OverbookingAccomTypeSeasonDto dto = new OverbookingAccomTypeSeasonDto(new AccomType());
        OverbookingSeasonUIWrapper season = new OverbookingSeasonUIWrapper(dto);
        season.setSunday(true);
        season.setMonday(true);
        season.setTuesday(true);
        season.setWednesday(true);
        season.setThursday(true);
        season.setFriday(true);
        season.setSaturday(true);

        LocalDate friday = new LocalDate().dayOfWeek().setCopy(5);
        season.setStartDate(friday);
        season.setEndDate(friday);

        presenter.adjustOverbookingSeasonsDayOfWeekData(Collections.singletonList(season));

        assertFalse(season.isSunday());
        assertFalse(season.isMonday());
        assertFalse(season.isTuesday());
        assertFalse(season.isWednesday());
        assertFalse(season.isThursday());
        assertTrue(season.isFriday());
        assertFalse(season.isSaturday());
    }

    @Test
    public void adjustOverbookingSeasonsDayOfWeekData_EverythingButFriday() throws Exception {
        OverbookingAccomTypeSeasonDto dto = new OverbookingAccomTypeSeasonDto(new AccomType());
        OverbookingSeasonUIWrapper season = new OverbookingSeasonUIWrapper(dto);
        season.setSunday(true);
        season.setMonday(true);
        season.setTuesday(true);
        season.setWednesday(true);
        season.setThursday(true);
        season.setFriday(true);
        season.setSaturday(true);

        LocalDate saturday = new LocalDate().dayOfWeek().setCopy(6);
        season.setStartDate(saturday);
        season.setEndDate(saturday.plusDays(5));

        presenter.adjustOverbookingSeasonsDayOfWeekData(Collections.singletonList(season));

        assertTrue(season.isSunday());
        assertTrue(season.isMonday());
        assertTrue(season.isTuesday());
        assertTrue(season.isWednesday());
        assertTrue(season.isThursday());
        assertFalse(season.isFriday());
        assertTrue(season.isSaturday());
    }

    @Test
    public void hasConflictingOverbookingCeilingDefaults() {
        when(overbookingService.hasConflictingOverbookingCeilingDefaults(presenter.getOverbookingAccomTypeDtos())).thenReturn(true);

        assertTrue(presenter.hasConflictingOverbookingCeilingDefaults());

        verify(overbookingService).hasConflictingOverbookingCeilingDefaults(presenter.getOverbookingAccomTypeDtos());
    }

    @Test
    public void hasRoomClassConfigChanged_noChanges() throws Exception {
        AccomClass accomClass1 = new AccomClass();
        accomClass1.setId(1);
        accomClass1.setViewOrder(1);
        AccomType accomType1 = new AccomType();
        accomType1.setId(1);
        accomClass1.addAccomType(accomType1);
        AccomType accomType2 = new AccomType();
        accomType2.setId(2);
        accomClass1.addAccomType(accomType2);
        AccomClass accomClass2 = new AccomClass();
        accomClass2.setId(2);
        accomClass2.setViewOrder(2);
        AccomType accomType3 = new AccomType();
        accomType3.setId(3);
        accomClass2.addAccomType(accomType3);
        AccomType accomType4 = new AccomType();
        accomType4.setId(4);
        accomClass2.addAccomType(accomType4);
        SortedSet<AccomClass> currentAccomClasses = getAccomClassesSetForTesting(Arrays.asList(accomClass1, accomClass2));
        FieldUtils.writeField(presenter, "accomClasses", currentAccomClasses, true);

        List<AccomClass> originalAccomClasses = Arrays.asList(accomClass2, accomClass1);
        assertFalse(presenter.hasRoomClassConfigChanged(originalAccomClasses));
    }

    @Test
    public void hasRoomClassConfigChanged_addAccomClass() throws Exception {
        AccomClass accomClass1 = new AccomClass();
        accomClass1.setId(1);
        accomClass1.setViewOrder(1);
        AccomType accomType1 = new AccomType();
        accomType1.setId(1);
        accomClass1.addAccomType(accomType1);
        AccomType accomType2 = new AccomType();
        accomType2.setId(2);
        accomClass1.addAccomType(accomType2);
        AccomClass accomClass2 = new AccomClass();
        accomClass2.setViewOrder(2);
        accomClass2.setId(2);
        AccomType accomType3 = new AccomType();
        accomType3.setId(3);
        accomClass2.addAccomType(accomType3);
        AccomType accomType4 = new AccomType();
        accomType4.setId(4);
        accomClass2.addAccomType(accomType4);
        AccomClass newAccomClass = new AccomClass();
        newAccomClass.setId(3);
        newAccomClass.setViewOrder(3);

        SortedSet<AccomClass> currentAccomClasses = getAccomClassesSetForTesting(Arrays.asList(accomClass1, accomClass2, newAccomClass));
        FieldUtils.writeField(presenter, "accomClasses", currentAccomClasses, true);

        List<AccomClass> originalAccomClasses = Arrays.asList(accomClass2, accomClass1);
        assertTrue(presenter.hasRoomClassConfigChanged(originalAccomClasses));
    }

    @Test
    public void hasRoomClassConfigChanged_removedAccomClass() throws Exception {
        AccomClass accomClass1 = new AccomClass();
        accomClass1.setId(1);
        accomClass1.setViewOrder(1);
        AccomType accomType1 = new AccomType();
        accomType1.setId(1);
        accomClass1.addAccomType(accomType1);
        AccomType accomType2 = new AccomType();
        accomType2.setId(2);
        accomClass1.addAccomType(accomType2);
        AccomClass accomClass2 = new AccomClass();
        accomClass2.setId(2);
        accomClass2.setViewOrder(2);
        AccomType accomType3 = new AccomType();
        accomType3.setId(3);
        accomClass2.addAccomType(accomType3);
        AccomType accomType4 = new AccomType();
        accomType4.setId(4);
        accomClass2.addAccomType(accomType4);
        AccomClass removedAccomClass = new AccomClass();
        removedAccomClass.setId(3);
        removedAccomClass.setViewOrder(3);

        SortedSet<AccomClass> currentAccomClasses = getAccomClassesSetForTesting(Arrays.asList(accomClass1, accomClass2));
        FieldUtils.writeField(presenter, "accomClasses", currentAccomClasses, true);

        List<AccomClass> originalAccomClasses = Arrays.asList(accomClass2, accomClass1, removedAccomClass);
        assertTrue(presenter.hasRoomClassConfigChanged(originalAccomClasses));
    }

    @Test
    public void hasRoomClassConfigChanged_movedAccomType() throws Exception {
        AccomClass newAccomClass1 = new AccomClass();
        newAccomClass1.setId(1);
        newAccomClass1.setViewOrder(1);
        AccomType accomType1 = new AccomType();
        accomType1.setId(1);
        newAccomClass1.addAccomType(accomType1);
        AccomType accomType2 = new AccomType();
        accomType2.setId(2);
        newAccomClass1.addAccomType(accomType2);

        AccomClass newAccomClass2 = new AccomClass();
        newAccomClass2.setId(2);
        newAccomClass2.setViewOrder(2);
        AccomType accomType3 = new AccomType();
        accomType3.setId(3);
        newAccomClass2.addAccomType(accomType3);
        AccomType accomType4 = new AccomType();
        accomType4.setId(4);
        newAccomClass2.addAccomType(accomType4);


        AccomClass originalAccomClass1 = createRoomClass(accomType1, 1);

        AccomClass originalAccomClass2 = createRoomClass(accomType2, 2);
        originalAccomClass2.addAccomType(accomType3);
        originalAccomClass2.addAccomType(accomType4);

        SortedSet<AccomClass> currentAccomClasses = getAccomClassesSetForTesting(Arrays.asList(newAccomClass1, newAccomClass2));
        FieldUtils.writeField(presenter, "accomClasses", currentAccomClasses, true);

        List<AccomClass> originalAccomClasses = Arrays.asList(originalAccomClass2, originalAccomClass1);
        assertTrue(presenter.hasRoomClassConfigChanged(originalAccomClasses));
    }

    @Test
    public void hasRoomClassConfigChanged_newAccomType() throws Exception {
        AccomClass newAccomClass1 = new AccomClass();
        newAccomClass1.setId(1);
        newAccomClass1.setViewOrder(1);
        AccomType accomType1 = new AccomType();
        accomType1.setId(1);
        newAccomClass1.addAccomType(accomType1);
        AccomType accomType2 = new AccomType();
        accomType2.setId(2);
        newAccomClass1.addAccomType(accomType2);

        AccomClass newAccomClass2 = new AccomClass();
        newAccomClass2.setId(2);
        newAccomClass2.setViewOrder(2);
        AccomType accomType3 = new AccomType();
        accomType3.setId(3);
        newAccomClass2.addAccomType(accomType3);
        AccomType accomType4 = new AccomType();
        accomType4.setId(4);
        newAccomClass2.addAccomType(accomType4);


        AccomType newAccomType = new AccomType();
        newAccomType.setId(5);
        newAccomClass1.addAccomType(newAccomType);

        AccomClass originalAccomClass1 = createRoomClass(accomType1, 1);
        originalAccomClass1.addAccomType(accomType2);

        AccomClass originalAccomClass2 = createRoomClass(accomType3, 2);
        originalAccomClass2.addAccomType(accomType4);

        SortedSet<AccomClass> currentAccomClasses = getAccomClassesSetForTesting(Arrays.asList(newAccomClass1, newAccomClass2));
        FieldUtils.writeField(presenter, "accomClasses", currentAccomClasses, true);

        List<AccomClass> originalAccomClasses = Arrays.asList(originalAccomClass2, originalAccomClass1);
        assertTrue(presenter.hasRoomClassConfigChanged(originalAccomClasses));
    }

    @Test
    public void saveNetworkPathArrows() throws Exception {
        List<AccomClassPriceRankNetworkArrow> networkPathArrows = Arrays.asList(new AccomClassPriceRankNetworkArrow(), new AccomClassPriceRankNetworkArrow());
        presenter.saveNetworkPathArrows(networkPathArrows);

        verify(accomClassPriceRankService).saveNetworkPathArrows(networkPathArrows);
    }

    @Test
    public void deleteAdvancedPriceRankingDataAndUpdateHasChanges_Changes() throws Exception {
        presenter = spy(presenter);
        LinkedList<AccomClassPriceRank> accomClassPriceRankings = new LinkedList<>();
        AccomClassPriceRank priceRank = new AccomClassPriceRank();
        priceRank.setId(1);
        accomClassPriceRankings.add(priceRank);
        AccomClass accomClass = new AccomClass();
        accomClass.setRankOrder(3);
        SortedSet<AccomClass> accomClasses = getAccomClassesSetForTesting(Collections.singletonList(accomClass));

        FieldUtils.writeField(presenter, "accomClasses", accomClasses, true);
        FieldUtils.writeField(presenter, "accomClassPriceRankings", accomClassPriceRankings, true);
        doNothing().when(presenter).sync();

        List<AccomClassPriceRank> existingPriceRankPaths = new ArrayList<>();
        AccomClassPriceRank accomClassPriceRank1 = new AccomClassPriceRank();
        AccomClass accomClass1 = new AccomClass();
        accomClass1.setId(7);
        accomClassPriceRank1.setLowerRankAccomClass(accomClass1);
        accomClassPriceRank1.setHigherRankAccomClass(accomClass1);
        existingPriceRankPaths.add(accomClassPriceRank1);
        when(accomClassPriceRankService.getAccomClassPriceRank()).thenReturn(existingPriceRankPaths);

        LinkedList<AccomClassPriceRank> defaultPriceRankPaths = new LinkedList<>();
        AccomClassPriceRank accomClassPriceRank2 = new AccomClassPriceRank();
        AccomClass accomClass2 = new AccomClass();
        accomClass2.setId(8);
        accomClassPriceRank2.setLowerRankAccomClass(accomClass2);
        accomClassPriceRank2.setHigherRankAccomClass(accomClass2);
        defaultPriceRankPaths.add(accomClassPriceRank2);
        when(accomClassPriceRankService.getAccomClassPriceRankForAllAccomClasses()).thenReturn(defaultPriceRankPaths);

        Map<RoomConfigurationSelection, Boolean> hasChangesMap = new HashMap<>();
        hasChangesMap.put(RoomConfigurationSelection.PRICE_RANKING, false);
        FieldUtils.writeField(presenter, "hasChangesMap", hasChangesMap, true);

        presenter.deleteAdvancedPriceRankingDataAndUpdateHasChanges();

        verify(accomClassPriceRankService).deleteAccomClassPriceRanks();
        assertTrue(presenter.hasChanges(RoomConfigurationSelection.PRICE_RANKING));
    }

    @Test
    public void deleteAdvancedPriceRankingDataAndUpdateHasChanges_NoChanges() throws Exception {
        presenter = spy(presenter);
        LinkedList<AccomClassPriceRank> accomClassPriceRankings = new LinkedList<>();
        AccomClassPriceRank priceRank = new AccomClassPriceRank();
        priceRank.setId(1);
        accomClassPriceRankings.add(priceRank);
        AccomClass accomClass = new AccomClass();
        accomClass.setRankOrder(3);
        SortedSet<AccomClass> accomClasses = getAccomClassesSetForTesting(Collections.singletonList(accomClass));

        FieldUtils.writeField(presenter, "accomClasses", accomClasses, true);
        FieldUtils.writeField(presenter, "accomClassPriceRankings", accomClassPriceRankings, true);
        doNothing().when(presenter).sync();

        Map<RoomConfigurationSelection, Boolean> hasChangesMap = new HashMap<>();
        hasChangesMap.put(RoomConfigurationSelection.PRICE_RANKING, false);
        FieldUtils.writeField(presenter, "hasChangesMap", hasChangesMap, true);

        presenter.deleteAdvancedPriceRankingDataAndUpdateHasChanges();

        verify(accomClassPriceRankService).deleteAccomClassPriceRanks();
        assertFalse(presenter.hasChanges(RoomConfigurationSelection.PRICE_RANKING));
    }

    @Test
    public void testNetworkPathHasPriceRankingChanges_Changes() throws Exception {
        AccomClass lowerRankAccomClass = new AccomClass();
        lowerRankAccomClass.setId(1);
        AccomClass higherRankAccomClass = new AccomClass();
        higherRankAccomClass.setId(3);
        AccomClassPriceRank priceRank1 = new AccomClassPriceRank(lowerRankAccomClass, higherRankAccomClass, true);

        AccomClass lowerRankAccomClassDifferent = new AccomClass();
        lowerRankAccomClass.setId(7);
        AccomClassPriceRank priceRank2 = new AccomClassPriceRank(lowerRankAccomClassDifferent, higherRankAccomClass, false);

        AccomClassPriceRankNetworkArrow arrow1 = new AccomClassPriceRankNetworkArrow();
        arrow1.setPriceRank(priceRank1);

        AccomClassPriceRankNetworkArrow arrow2 = new AccomClassPriceRankNetworkArrow();
        arrow2.setPriceRank(priceRank2);

        List<AccomClassPriceRankNetworkArrow> networkPathArrows = Arrays.asList(arrow1);

        FieldUtils.writeField(presenter, "advancedNetworkArrows", networkPathArrows, true);

        assertTrue(presenter.networkPathHasPriceRankingChanges(Arrays.asList(arrow2)));
    }

    @Test
    public void testNetworkPathHasPriceRankingChanges_NoChanges() throws Exception {
        AccomClass lowerRankAccomClass = new AccomClass();
        lowerRankAccomClass.setId(1);
        AccomClass middleAccomClass = new AccomClass();
        middleAccomClass.setId(2);
        AccomClass higherRankAccomClass = new AccomClass();
        higherRankAccomClass.setId(3);
        AccomClassPriceRank priceRank1 = new AccomClassPriceRank(lowerRankAccomClass, middleAccomClass, true);
        AccomClassPriceRank priceRank2 = new AccomClassPriceRank(middleAccomClass, higherRankAccomClass, false);

        AccomClassPriceRankNetworkArrow arrow1 = new AccomClassPriceRankNetworkArrow();
        arrow1.setPriceRank(priceRank1);

        AccomClassPriceRankNetworkArrow arrow2 = new AccomClassPriceRankNetworkArrow();
        arrow2.setPriceRank(priceRank2);

        AccomClassPriceRankNetworkArrow newArrow1 = new AccomClassPriceRankNetworkArrow();
        newArrow1.setPriceRank(priceRank1);

        AccomClassPriceRankNetworkArrow newArrow2 = new AccomClassPriceRankNetworkArrow();
        AccomClass lowerRankAccomClassNew = new AccomClass();
        lowerRankAccomClassNew.setId(7);
        lowerRankAccomClass.setId(1);
        newArrow2.setPriceRank(priceRank2);

        List<AccomClassPriceRankNetworkArrow> networkPathArrows = Arrays.asList(arrow1, arrow2);

        FieldUtils.writeField(presenter, "advancedNetworkArrows", networkPathArrows, true);

        assertFalse(presenter.networkPathHasPriceRankingChanges(Arrays.asList(newArrow1, newArrow2)));
    }

    @Test
    public void networkPathHasChanges_noChanges() throws Exception {
        AccomClass lowerRankAccomClass = new AccomClass();
        lowerRankAccomClass.setId(1);
        AccomClass middleAccomClass = new AccomClass();
        middleAccomClass.setId(2);
        AccomClass higherRankAccomClass = new AccomClass();
        higherRankAccomClass.setId(3);
        AccomClassPriceRank priceRank1 = new AccomClassPriceRank(lowerRankAccomClass, middleAccomClass, true);
        AccomClassPriceRank priceRank2 = new AccomClassPriceRank(middleAccomClass, higherRankAccomClass, false);

        AccomClassPriceRankNetworkArrow arrow1 = new AccomClassPriceRankNetworkArrow();
        arrow1.setXCoord(0);
        arrow1.setYCoord(3);
        arrow1.setDirection(Direction.UP);
        arrow1.setPriceRank(priceRank1);

        AccomClassPriceRankNetworkArrow arrow2 = new AccomClassPriceRankNetworkArrow();
        arrow2.setXCoord(1);
        arrow2.setYCoord(4);
        arrow2.setDirection(Direction.RIGHT);
        arrow2.setPriceRank(priceRank2);

        AccomClassPriceRankNetworkArrow newArrow1 = new AccomClassPriceRankNetworkArrow();
        newArrow1.setXCoord(0);
        newArrow1.setYCoord(3);
        newArrow1.setDirection(Direction.UP);
        newArrow1.setPriceRank(priceRank1);

        AccomClassPriceRankNetworkArrow newArrow2 = new AccomClassPriceRankNetworkArrow();
        newArrow2.setXCoord(1);
        newArrow2.setYCoord(4);
        newArrow2.setDirection(Direction.RIGHT);
        newArrow2.setPriceRank(priceRank2);

        List<AccomClassPriceRankNetworkArrow> networkPathArrows = Arrays.asList(arrow1, arrow2);

        FieldUtils.writeField(presenter, "advancedNetworkArrows", networkPathArrows, true);

        assertFalse(presenter.networkPathHasChanges(Arrays.asList(newArrow1, newArrow2)));
    }

    @Test
    public void networkPathHasChanges_differentXCoord() throws Exception {
        AccomClass lowerRankAccomClass = new AccomClass();
        lowerRankAccomClass.setId(1);
        AccomClass middleAccomClass = new AccomClass();
        middleAccomClass.setId(2);
        AccomClass higherRankAccomClass = new AccomClass();
        higherRankAccomClass.setId(3);
        AccomClassPriceRank priceRank1 = new AccomClassPriceRank(lowerRankAccomClass, middleAccomClass, true);
        AccomClassPriceRank priceRank2 = new AccomClassPriceRank(middleAccomClass, higherRankAccomClass, false);

        AccomClassPriceRankNetworkArrow arrow1 = new AccomClassPriceRankNetworkArrow();
        arrow1.setXCoord(0);
        arrow1.setYCoord(3);
        arrow1.setDirection(Direction.UP);
        arrow1.setPriceRank(priceRank1);

        AccomClassPriceRankNetworkArrow arrow2 = new AccomClassPriceRankNetworkArrow();
        arrow2.setXCoord(1);
        arrow2.setYCoord(4);
        arrow2.setDirection(Direction.RIGHT);
        arrow2.setPriceRank(priceRank2);

        AccomClassPriceRankNetworkArrow newArrow1 = new AccomClassPriceRankNetworkArrow();
        newArrow1.setXCoord(2);
        newArrow1.setYCoord(3);
        newArrow1.setDirection(Direction.UP);
        newArrow1.setPriceRank(priceRank1);

        AccomClassPriceRankNetworkArrow newArrow2 = new AccomClassPriceRankNetworkArrow();
        newArrow2.setXCoord(1);
        newArrow2.setYCoord(4);
        newArrow2.setDirection(Direction.RIGHT);
        newArrow2.setPriceRank(priceRank2);

        List<AccomClassPriceRankNetworkArrow> networkPathArrows = Arrays.asList(arrow1, arrow2);

        FieldUtils.writeField(presenter, "advancedNetworkArrows", networkPathArrows, true);

        assertTrue(presenter.networkPathHasChanges(Arrays.asList(newArrow1, newArrow2)));
    }

    @Test
    public void networkPathHasChanges_differentYCoord() throws Exception {
        AccomClass lowerRankAccomClass = new AccomClass();
        lowerRankAccomClass.setId(1);
        AccomClass middleAccomClass = new AccomClass();
        middleAccomClass.setId(2);
        AccomClass higherRankAccomClass = new AccomClass();
        higherRankAccomClass.setId(3);
        AccomClassPriceRank priceRank1 = new AccomClassPriceRank(lowerRankAccomClass, middleAccomClass, true);
        AccomClassPriceRank priceRank2 = new AccomClassPriceRank(middleAccomClass, higherRankAccomClass, false);

        AccomClassPriceRankNetworkArrow arrow1 = new AccomClassPriceRankNetworkArrow();
        arrow1.setXCoord(0);
        arrow1.setYCoord(3);
        arrow1.setDirection(Direction.UP);
        arrow1.setPriceRank(priceRank1);

        AccomClassPriceRankNetworkArrow arrow2 = new AccomClassPriceRankNetworkArrow();
        arrow2.setXCoord(1);
        arrow2.setYCoord(4);
        arrow2.setDirection(Direction.RIGHT);
        arrow2.setPriceRank(priceRank2);

        AccomClassPriceRankNetworkArrow newArrow1 = new AccomClassPriceRankNetworkArrow();
        newArrow1.setXCoord(0);
        newArrow1.setYCoord(3);
        newArrow1.setDirection(Direction.UP);
        newArrow1.setPriceRank(priceRank1);

        AccomClassPriceRankNetworkArrow newArrow2 = new AccomClassPriceRankNetworkArrow();
        newArrow2.setXCoord(1);
        newArrow2.setYCoord(2);
        newArrow2.setDirection(Direction.RIGHT);
        newArrow2.setPriceRank(priceRank2);

        List<AccomClassPriceRankNetworkArrow> networkPathArrows = Arrays.asList(arrow1, arrow2);

        FieldUtils.writeField(presenter, "advancedNetworkArrows", networkPathArrows, true);

        assertTrue(presenter.networkPathHasChanges(Arrays.asList(newArrow1, newArrow2)));
    }

    @Test
    public void networkPathHasChanges_differentDirection() throws Exception {
        AccomClass lowerRankAccomClass = new AccomClass();
        lowerRankAccomClass.setId(1);
        AccomClass middleAccomClass = new AccomClass();
        middleAccomClass.setId(2);
        AccomClass higherRankAccomClass = new AccomClass();
        higherRankAccomClass.setId(3);
        AccomClassPriceRank priceRank1 = new AccomClassPriceRank(lowerRankAccomClass, middleAccomClass, true);
        AccomClassPriceRank priceRank2 = new AccomClassPriceRank(middleAccomClass, higherRankAccomClass, false);

        AccomClassPriceRankNetworkArrow arrow1 = new AccomClassPriceRankNetworkArrow();
        arrow1.setXCoord(0);
        arrow1.setYCoord(3);
        arrow1.setDirection(Direction.UP);
        arrow1.setPriceRank(priceRank1);

        AccomClassPriceRankNetworkArrow arrow2 = new AccomClassPriceRankNetworkArrow();
        arrow2.setXCoord(1);
        arrow2.setYCoord(4);
        arrow2.setDirection(Direction.RIGHT);
        arrow2.setPriceRank(priceRank2);

        AccomClassPriceRankNetworkArrow newArrow1 = new AccomClassPriceRankNetworkArrow();
        newArrow1.setXCoord(0);
        newArrow1.setYCoord(3);
        newArrow1.setDirection(Direction.UP);
        newArrow1.setPriceRank(priceRank1);

        AccomClassPriceRankNetworkArrow newArrow2 = new AccomClassPriceRankNetworkArrow();
        newArrow2.setXCoord(1);
        newArrow2.setYCoord(4);
        newArrow2.setDirection(Direction.LEFT);
        newArrow2.setPriceRank(priceRank2);

        List<AccomClassPriceRankNetworkArrow> networkPathArrows = Arrays.asList(arrow1, arrow2);

        FieldUtils.writeField(presenter, "advancedNetworkArrows", networkPathArrows, true);

        assertTrue(presenter.networkPathHasChanges(Arrays.asList(newArrow1, newArrow2)));
    }

    @Test
    public void networkPathHasChanges_differentPriceRankAccomClasses() throws Exception {
        AccomClass lowerRankAccomClass = new AccomClass();
        lowerRankAccomClass.setId(1);
        AccomClass middleAccomClass = new AccomClass();
        middleAccomClass.setId(2);
        AccomClass higherRankAccomClass = new AccomClass();
        higherRankAccomClass.setId(3);
        AccomClassPriceRank priceRank1 = new AccomClassPriceRank(lowerRankAccomClass, middleAccomClass, true);
        AccomClassPriceRank priceRank2 = new AccomClassPriceRank(middleAccomClass, higherRankAccomClass, false);
        AccomClassPriceRank newPriceRank = new AccomClassPriceRank(lowerRankAccomClass, higherRankAccomClass, false);

        AccomClassPriceRankNetworkArrow arrow1 = new AccomClassPriceRankNetworkArrow();
        arrow1.setXCoord(0);
        arrow1.setYCoord(3);
        arrow1.setDirection(Direction.UP);
        arrow1.setPriceRank(priceRank1);

        AccomClassPriceRankNetworkArrow arrow2 = new AccomClassPriceRankNetworkArrow();
        arrow2.setXCoord(1);
        arrow2.setYCoord(4);
        arrow2.setDirection(Direction.RIGHT);
        arrow2.setPriceRank(priceRank2);

        AccomClassPriceRankNetworkArrow newArrow1 = new AccomClassPriceRankNetworkArrow();
        newArrow1.setXCoord(0);
        newArrow1.setYCoord(3);
        newArrow1.setDirection(Direction.UP);
        newArrow1.setPriceRank(priceRank1);

        AccomClassPriceRankNetworkArrow newArrow2 = new AccomClassPriceRankNetworkArrow();
        newArrow2.setXCoord(1);
        newArrow2.setYCoord(4);
        newArrow2.setDirection(Direction.RIGHT);
        newArrow2.setPriceRank(newPriceRank);

        List<AccomClassPriceRankNetworkArrow> networkPathArrows = Arrays.asList(arrow1, arrow2);

        FieldUtils.writeField(presenter, "advancedNetworkArrows", networkPathArrows, true);

        assertTrue(presenter.networkPathHasChanges(Arrays.asList(newArrow1, newArrow2)));
    }

    @Test
    public void networkPathHasChanges_differentPriceRankUpgrade() throws Exception {
        AccomClass lowerRankAccomClass = new AccomClass();
        lowerRankAccomClass.setId(1);
        AccomClass middleAccomClass = new AccomClass();
        middleAccomClass.setId(2);
        AccomClass higherRankAccomClass = new AccomClass();
        higherRankAccomClass.setId(3);
        AccomClassPriceRank priceRank1 = new AccomClassPriceRank(lowerRankAccomClass, middleAccomClass, true);
        AccomClassPriceRank priceRank2 = new AccomClassPriceRank(middleAccomClass, higherRankAccomClass, false);
        AccomClassPriceRank newPriceRank = new AccomClassPriceRank(middleAccomClass, higherRankAccomClass, true);

        AccomClassPriceRankNetworkArrow arrow1 = new AccomClassPriceRankNetworkArrow();
        arrow1.setXCoord(0);
        arrow1.setYCoord(3);
        arrow1.setDirection(Direction.UP);
        arrow1.setPriceRank(priceRank1);

        AccomClassPriceRankNetworkArrow arrow2 = new AccomClassPriceRankNetworkArrow();
        arrow2.setXCoord(1);
        arrow2.setYCoord(4);
        arrow2.setDirection(Direction.RIGHT);
        arrow2.setPriceRank(priceRank2);

        AccomClassPriceRankNetworkArrow newArrow1 = new AccomClassPriceRankNetworkArrow();
        newArrow1.setXCoord(0);
        newArrow1.setYCoord(3);
        newArrow1.setDirection(Direction.UP);
        newArrow1.setPriceRank(priceRank1);

        AccomClassPriceRankNetworkArrow newArrow2 = new AccomClassPriceRankNetworkArrow();
        newArrow2.setXCoord(1);
        newArrow2.setYCoord(4);
        newArrow2.setDirection(Direction.RIGHT);
        newArrow2.setPriceRank(newPriceRank);

        List<AccomClassPriceRankNetworkArrow> networkPathArrows = Arrays.asList(arrow1, arrow2);

        FieldUtils.writeField(presenter, "advancedNetworkArrows", networkPathArrows, true);

        assertTrue(presenter.networkPathHasChanges(Arrays.asList(newArrow1, newArrow2)));
    }

    @Test
    public void networkPathHasChanges_newArrow() throws Exception {
        AccomClass lowerRankAccomClass = new AccomClass();
        lowerRankAccomClass.setId(1);
        AccomClass middleAccomClass = new AccomClass();
        middleAccomClass.setId(2);
        AccomClass higherRankAccomClass = new AccomClass();
        higherRankAccomClass.setId(3);
        AccomClassPriceRank priceRank1 = new AccomClassPriceRank(lowerRankAccomClass, middleAccomClass, true);
        AccomClassPriceRank priceRank2 = new AccomClassPriceRank(middleAccomClass, higherRankAccomClass, false);
        AccomClassPriceRank newPriceRank = new AccomClassPriceRank(middleAccomClass, higherRankAccomClass, true);

        AccomClassPriceRankNetworkArrow arrow1 = new AccomClassPriceRankNetworkArrow();
        arrow1.setXCoord(0);
        arrow1.setYCoord(3);
        arrow1.setDirection(Direction.UP);
        arrow1.setPriceRank(priceRank1);

        AccomClassPriceRankNetworkArrow arrow2 = new AccomClassPriceRankNetworkArrow();
        arrow2.setXCoord(1);
        arrow2.setYCoord(4);
        arrow2.setDirection(Direction.RIGHT);
        arrow2.setPriceRank(priceRank2);

        AccomClassPriceRankNetworkArrow newArrow1 = new AccomClassPriceRankNetworkArrow();
        newArrow1.setXCoord(0);
        newArrow1.setYCoord(3);
        newArrow1.setDirection(Direction.UP);
        newArrow1.setPriceRank(priceRank1);

        AccomClassPriceRankNetworkArrow newArrow2 = new AccomClassPriceRankNetworkArrow();
        newArrow2.setXCoord(1);
        newArrow2.setYCoord(4);
        newArrow2.setDirection(Direction.RIGHT);
        newArrow2.setPriceRank(priceRank2);

        AccomClassPriceRankNetworkArrow newArrow3 = new AccomClassPriceRankNetworkArrow();
        newArrow2.setXCoord(2);
        newArrow2.setYCoord(2);
        newArrow2.setDirection(Direction.LEFT);
        newArrow2.setPriceRank(newPriceRank);

        List<AccomClassPriceRankNetworkArrow> networkPathArrows = Arrays.asList(arrow1, arrow2);

        FieldUtils.writeField(presenter, "advancedNetworkArrows", networkPathArrows, true);

        assertTrue(presenter.networkPathHasChanges(Arrays.asList(newArrow1, newArrow2, newArrow3)));
    }

    @Test
    public void networkPathHasChanges_arrowMissing() throws Exception {
        AccomClass lowerRankAccomClass = new AccomClass();
        lowerRankAccomClass.setId(1);
        AccomClass middleAccomClass = new AccomClass();
        middleAccomClass.setId(2);
        AccomClass higherRankAccomClass = new AccomClass();
        higherRankAccomClass.setId(3);
        AccomClassPriceRank priceRank1 = new AccomClassPriceRank(lowerRankAccomClass, middleAccomClass, true);
        AccomClassPriceRank priceRank2 = new AccomClassPriceRank(middleAccomClass, higherRankAccomClass, false);

        AccomClassPriceRankNetworkArrow arrow1 = new AccomClassPriceRankNetworkArrow();
        arrow1.setXCoord(0);
        arrow1.setYCoord(3);
        arrow1.setDirection(Direction.UP);
        arrow1.setPriceRank(priceRank1);

        AccomClassPriceRankNetworkArrow arrow2 = new AccomClassPriceRankNetworkArrow();
        arrow2.setXCoord(1);
        arrow2.setYCoord(4);
        arrow2.setDirection(Direction.RIGHT);
        arrow2.setPriceRank(priceRank2);

        AccomClassPriceRankNetworkArrow newArrow1 = new AccomClassPriceRankNetworkArrow();
        newArrow1.setXCoord(0);
        newArrow1.setYCoord(3);
        newArrow1.setDirection(Direction.UP);
        newArrow1.setPriceRank(priceRank1);

        List<AccomClassPriceRankNetworkArrow> networkPathArrows = Arrays.asList(arrow1, arrow2);

        FieldUtils.writeField(presenter, "advancedNetworkArrows", networkPathArrows, true);

        assertTrue(presenter.networkPathHasChanges(Arrays.asList(newArrow1)));
    }

    @Test
    public void priceRanksHasChanges_noChanges() throws Exception {
        AccomClass lowerRankAccomClass = new AccomClass();
        lowerRankAccomClass.setId(1);
        AccomClass middleAccomClass = new AccomClass();
        middleAccomClass.setId(2);
        AccomClass higherRankAccomClass = new AccomClass();
        higherRankAccomClass.setId(3);
        AccomClassPriceRank accomClassPriceRank1 = new AccomClassPriceRank(lowerRankAccomClass, middleAccomClass, true);
        AccomClassPriceRank accomClassPriceRank2 = new AccomClassPriceRank(middleAccomClass, higherRankAccomClass, false);
        List<AccomClassPriceRank> priceRankPaths = Arrays.asList(accomClassPriceRank1, accomClassPriceRank2);

        FieldUtils.writeField(presenter, "accomClassPriceRankings", priceRankPaths, true);

        assertFalse(presenter.priceRanksHasChanges(Arrays.asList(accomClassPriceRank1, accomClassPriceRank2)));
    }

    @Test
    public void priceRanksHasChanges_differentHigherAccomClass() throws Exception {
        AccomClass lowerRankAccomClass = new AccomClass();
        lowerRankAccomClass.setId(1);
        AccomClass middleAccomClass = new AccomClass();
        middleAccomClass.setId(2);
        AccomClass higherRankAccomClass = new AccomClass();
        higherRankAccomClass.setId(3);
        AccomClass newAccomClass = new AccomClass();
        newAccomClass.setId(4);

        AccomClassPriceRank accomClassPriceRank1 = new AccomClassPriceRank(lowerRankAccomClass, middleAccomClass, true);
        AccomClassPriceRank accomClassPriceRank2 = new AccomClassPriceRank(middleAccomClass, higherRankAccomClass, false);
        List<AccomClassPriceRank> priceRankPaths = Arrays.asList(accomClassPriceRank1, accomClassPriceRank2);

        AccomClassPriceRank newAccomClassPriceRank1 = new AccomClassPriceRank(lowerRankAccomClass, middleAccomClass, true);
        AccomClassPriceRank newAccomClassPriceRank2 = new AccomClassPriceRank(middleAccomClass, newAccomClass, false);
        List<AccomClassPriceRank> newPriceRankPaths = Arrays.asList(newAccomClassPriceRank1, newAccomClassPriceRank2);

        FieldUtils.writeField(presenter, "accomClassPriceRankings", priceRankPaths, true);

        assertTrue(presenter.priceRanksHasChanges(newPriceRankPaths));
    }

    @Test
    public void priceRanksHasChanges_differentLowerAccomClass() throws Exception {
        AccomClass lowerRankAccomClass = new AccomClass();
        lowerRankAccomClass.setId(1);
        AccomClass middleAccomClass = new AccomClass();
        middleAccomClass.setId(2);
        AccomClass higherRankAccomClass = new AccomClass();
        higherRankAccomClass.setId(3);
        AccomClass newAccomClass = new AccomClass();
        newAccomClass.setId(4);

        AccomClassPriceRank accomClassPriceRank1 = new AccomClassPriceRank(lowerRankAccomClass, middleAccomClass, true);
        AccomClassPriceRank accomClassPriceRank2 = new AccomClassPriceRank(middleAccomClass, higherRankAccomClass, false);
        List<AccomClassPriceRank> priceRankPaths = Arrays.asList(accomClassPriceRank1, accomClassPriceRank2);

        AccomClassPriceRank newAccomClassPriceRank1 = new AccomClassPriceRank(newAccomClass, middleAccomClass, true);
        AccomClassPriceRank newAccomClassPriceRank2 = new AccomClassPriceRank(middleAccomClass, higherRankAccomClass, false);
        List<AccomClassPriceRank> newPriceRankPaths = Arrays.asList(newAccomClassPriceRank1, newAccomClassPriceRank2);

        FieldUtils.writeField(presenter, "accomClassPriceRankings", priceRankPaths, true);

        assertTrue(presenter.priceRanksHasChanges(newPriceRankPaths));
    }

    @Test
    public void priceRanksHasChanges_differentUpgradeAllowed() throws Exception {
        AccomClass lowerRankAccomClass = new AccomClass();
        lowerRankAccomClass.setId(1);
        AccomClass middleAccomClass = new AccomClass();
        middleAccomClass.setId(2);
        AccomClass higherRankAccomClass = new AccomClass();
        higherRankAccomClass.setId(3);
        AccomClass newAccomClass = new AccomClass();
        newAccomClass.setId(4);

        AccomClassPriceRank accomClassPriceRank1 = new AccomClassPriceRank(lowerRankAccomClass, middleAccomClass, true);
        AccomClassPriceRank accomClassPriceRank2 = new AccomClassPriceRank(middleAccomClass, higherRankAccomClass, false);
        List<AccomClassPriceRank> priceRankPaths = Arrays.asList(accomClassPriceRank1, accomClassPriceRank2);

        AccomClassPriceRank newAccomClassPriceRank1 = new AccomClassPriceRank(newAccomClass, middleAccomClass, true);
        AccomClassPriceRank newAccomClassPriceRank2 = new AccomClassPriceRank(middleAccomClass, higherRankAccomClass, true);
        List<AccomClassPriceRank> newPriceRankPaths = Arrays.asList(newAccomClassPriceRank1, newAccomClassPriceRank2);

        FieldUtils.writeField(presenter, "accomClassPriceRankings", priceRankPaths, true);

        assertTrue(presenter.priceRanksHasChanges(newPriceRankPaths));
    }

    @Test
    public void getAccomClassesByADR() throws Exception {
        LinkedList<AccomClass> accomClasses = new LinkedList<>();
        accomClasses.add(new AccomClass());
        when(accomClassPriceRankService.getRoomClassesByADR()).thenReturn(accomClasses);

        LinkedList<AccomClass> result = presenter.getAccomClassesByADR();

        assertEquals(accomClasses, result);
    }

    @Test
    public void saveGroupPriceRankingData_configurationIsComplete() throws Exception {
        presenter = spy(presenter);
        doNothing().when(presenter).sync();

        LinkedList<AccomClass> originalAccomClasses = new LinkedList<>();
        AccomClass roomClass1 = new AccomClass();
        roomClass1.setViewOrder(1);
        roomClass1.setRankOrder(1);
        originalAccomClasses.add(roomClass1);
        AccomClass roomClass2 = new AccomClass();
        roomClass2.setViewOrder(2);
        roomClass2.setRankOrder(2);
        originalAccomClasses.add(roomClass2);
        AccomClass roomClass3 = new AccomClass();
        roomClass3.setViewOrder(3);
        roomClass3.setRankOrder(3);
        originalAccomClasses.add(roomClass3);

        FieldUtils.writeField(presenter, "accomClasses", getAccomClassesSetForTesting(originalAccomClasses), true);

        //Rank has changed
        LinkedList<AccomClass> newAccomClasses = new LinkedList<>();
        AccomClass newRoomClass1 = new AccomClass();
        newRoomClass1.setViewOrder(1);
        newRoomClass1.setRankOrder(3);
        newAccomClasses.add(newRoomClass1);
        AccomClass newRoomClass2 = new AccomClass();
        newRoomClass2.setViewOrder(2);
        newRoomClass2.setRankOrder(2);
        newAccomClasses.add(newRoomClass2);
        AccomClass newRoomClass3 = new AccomClass();
        newRoomClass3.setViewOrder(3);
        newRoomClass3.setRankOrder(1);
        newAccomClasses.add(newRoomClass3);

        presenter.saveGroupPriceRankingData(newAccomClasses, true);

        //Once to save group rankings and again to update IsPriceRankConfigured
        verify(accommodationService, times(1)).updateAccomClasses(newAccomClasses);
        verify(accommodationService, times(1)).updateAccomClasses(newAccomClasses, new ArrayList<>(), new ArrayList<>());
        verify(agileRatesConfigurationService, never()).changedRoomClassConfig(any());
        assertTrue(presenter.hasChanges(RoomConfigurationSelection.GROUP_PRICE_RANKING));
        assertTrue(newRoomClass1.getIsPriceRankConfigured().equals(AccomClassPriceRankStatus.COMPLETE_RANKED));
        assertTrue(newRoomClass2.getIsPriceRankConfigured().equals(AccomClassPriceRankStatus.COMPLETE_RANKED));
        assertTrue(newRoomClass3.getIsPriceRankConfigured().equals(AccomClassPriceRankStatus.COMPLETE_RANKED));
    }

    @Test
    public void saveGroupPriceRankingData_configurationIsNOTComplete() throws Exception {
        presenter = spy(presenter);
        doNothing().when(presenter).sync();

        LinkedList<AccomClass> originalAccomClasses = new LinkedList<>();
        AccomClass roomClass1 = new AccomClass();
        roomClass1.setViewOrder(1);
        roomClass1.setRankOrder(1);
        originalAccomClasses.add(roomClass1);
        AccomClass roomClass2 = new AccomClass();
        roomClass2.setViewOrder(2);
        roomClass2.setRankOrder(2);
        originalAccomClasses.add(roomClass2);
        AccomClass roomClass3 = new AccomClass();
        roomClass3.setViewOrder(3);
        roomClass3.setRankOrder(3);
        originalAccomClasses.add(roomClass3);

        FieldUtils.writeField(presenter, "accomClasses", getAccomClassesSetForTesting(originalAccomClasses), true);

        //Rank has changed
        LinkedList<AccomClass> newAccomClasses = new LinkedList<>();
        AccomClass newRoomClass1 = new AccomClass();
        newRoomClass1.setViewOrder(1);
        newRoomClass1.setRankOrder(3);
        newAccomClasses.add(newRoomClass1);
        AccomClass newRoomClass2 = new AccomClass();
        newRoomClass2.setViewOrder(2);
        newRoomClass2.setRankOrder(2);
        newAccomClasses.add(newRoomClass2);
        AccomClass newRoomClass3 = new AccomClass();
        newRoomClass3.setViewOrder(3);
        newRoomClass3.setRankOrder(1);
        newAccomClasses.add(newRoomClass3);

        presenter.saveGroupPriceRankingData(newAccomClasses, false);

        verify(accommodationService, times(1)).updateAccomClasses(newAccomClasses);

        assertTrue(presenter.hasChanges(RoomConfigurationSelection.GROUP_PRICE_RANKING));
        assertTrue(newRoomClass1.getIsPriceRankConfigured().equals(AccomClassPriceRankStatus.INCOMPLETE));
        assertTrue(newRoomClass2.getIsPriceRankConfigured().equals(AccomClassPriceRankStatus.INCOMPLETE));
        assertTrue(newRoomClass3.getIsPriceRankConfigured().equals(AccomClassPriceRankStatus.INCOMPLETE));
    }

    @Test
    public void saveSimplePriceRankingData() throws Exception {
        presenter = spy(presenter);
        doNothing().when(presenter).sync();

        AccomClass accomClass1 = new AccomClass();
        accomClass1.setId(1);
        accomClass1.setRankOrder(4);
        accomClass1.setViewOrder(1);
        AccomClass accomClass2 = new AccomClass();
        accomClass2.setId(2);
        accomClass2.setRankOrder(3);
        accomClass2.setViewOrder(2);
        AccomClass accomClass3 = new AccomClass();
        accomClass3.setId(3);
        accomClass3.setRankOrder(2);
        accomClass3.setViewOrder(3);
        AccomClass accomClass4 = new AccomClass();
        accomClass4.setId(4);
        accomClass4.setRankOrder(1);
        accomClass4.setViewOrder(4);

        AccomClassPriceRank priceRank1 = new AccomClassPriceRank(accomClass1, accomClass2, true);
        priceRank1.setId(1);
        AccomClassPriceRank priceRank2 = new AccomClassPriceRank(accomClass2, accomClass3, false);
        priceRank2.setId(2);
        AccomClassPriceRank priceRank3 = new AccomClassPriceRank(accomClass3, accomClass4, true);
        priceRank3.setId(3);

        //The order of the ranked room classes is the same as the links within the price rankings
        LinkedList<AccomClassPriceRank> accomClassPriceRankings = new LinkedList<>(Arrays.asList(priceRank3, priceRank2, priceRank1));
        SortedSet<AccomClass> rankedAccomClasses = new TreeSet<>(new RoomTypeMappingComparator().reversed());
        rankedAccomClasses.addAll(Arrays.asList(accomClass4, accomClass3, accomClass2, accomClass1));

        FieldUtils.writeField(presenter, "accomClassPriceRankings", accomClassPriceRankings, true);
        FieldUtils.writeField(presenter, "accomClasses", rankedAccomClasses, true);

        presenter.saveSimplePriceRankingData(true, accomClassPriceRankings, true);

        assertEquals(1, accomClass1.getRankOrder().intValue());
        assertEquals(2, accomClass2.getRankOrder().intValue());
        assertEquals(3, accomClass3.getRankOrder().intValue());
        assertEquals(4, accomClass4.getRankOrder().intValue());
        verify(agileRatesConfigurationService, never()).changedRoomClassConfig(any());
        verify(pricingConfigurationLTBDEService).enableLTBDEOnIndirectConfigChangedIfApplicable();
    }

    @Test
    public void getInvalidUpgradeConfigurationWhenNullTest() {
        when(componentRoomService.getComponentRoomsConfigurations()).thenReturn(new ArrayList<ComponentRoomsConfiguration>());

        Map<String, Set<String>> invalidUpgradeConfiguration = new HashMap<>();
        presenter.getInvalidUpgradeConfiguration();
        assertEquals(0, invalidUpgradeConfiguration.size());

        when(componentRoomService.getComponentRoomsConfigurations()).thenReturn(null);

        invalidUpgradeConfiguration = new HashMap<>();
        presenter.getInvalidUpgradeConfiguration();
        assertEquals(0, invalidUpgradeConfiguration.size());

    }

    @Test
    public void getInvalidUpgradeConfigurationTest() {
        List<ComponentRoomsConfiguration> componentRoomsConfigurations = getConfigurationAsTree();

        when(componentRoomService.getComponentRoomsConfigurations()).thenReturn(componentRoomsConfigurations);

        Map<String, Set<String>> invalidUpgradeConfiguration = new HashMap<>();

        invalidUpgradeConfiguration = presenter.getInvalidUpgradeConfiguration();
        assertEquals(8, invalidUpgradeConfiguration.size());
        assertTrue(invalidUpgradeConfiguration.get("B0VD").contains("B3VKD") && invalidUpgradeConfiguration.get("B0VD").contains("B2VKD"));
        assertTrue(invalidUpgradeConfiguration.get("B0SK").contains("B3VKD"));
        assertTrue(invalidUpgradeConfiguration.get("B3VKD").contains("B2VKD") && invalidUpgradeConfiguration.get("B3VKD").contains("B0VD")
                && invalidUpgradeConfiguration.get("B3VKD").contains("B1VKS") && invalidUpgradeConfiguration.get("B3VKD").contains("B0SK"));
        assertTrue(invalidUpgradeConfiguration.get("B2VKK").contains("B0VK") && invalidUpgradeConfiguration.get("B2VKK").contains("B1VKK"));
    }

    private List<ComponentRoomsConfiguration> getConfigurationAsTree() {
        List<ComponentRoomsConfiguration> componentRoomsConfigurations = new ArrayList<>();
        AccomType at = new AccomType();
        at.setAccomTypeCode("B2VKK");
        ComponentRoomsConfiguration compRoomsConfB2VKK = new ComponentRoomsConfiguration(at);

        at = new AccomType();
        at.setAccomTypeCode("B0VK");
        ComponentRoomsConfiguration compRoomsConfB0VK = new ComponentRoomsConfiguration(compRoomsConfB2VKK, at, 1);

        at = new AccomType();
        at.setAccomTypeCode("B1VKK");
        ComponentRoomsConfiguration compRoomsConfB1VKK = new ComponentRoomsConfiguration(compRoomsConfB2VKK, at, 1);

        compRoomsConfB2VKK.getChildren().add(compRoomsConfB0VK);
        compRoomsConfB2VKK.getChildren().add(compRoomsConfB1VKK);

        componentRoomsConfigurations.add(compRoomsConfB2VKK);

        at = new AccomType();
        at.setAccomTypeCode("B2VKD");
        ComponentRoomsConfiguration compRoomsConfB2VKD = new ComponentRoomsConfiguration(at);

        at = new AccomType();
        at.setAccomTypeCode("B0VD");
        ComponentRoomsConfiguration compRoomsConfB0VD = new ComponentRoomsConfiguration(compRoomsConfB2VKD, at, 1);

        at = new AccomType();
        at.setAccomTypeCode("B1VKS");
        ComponentRoomsConfiguration compRoomsConfB1VKS = new ComponentRoomsConfiguration(compRoomsConfB2VKD, at, 1);

        compRoomsConfB2VKD.getChildren().add(compRoomsConfB0VD);
        compRoomsConfB2VKD.getChildren().add(compRoomsConfB1VKS);

        at = new AccomType();
        at.setAccomTypeCode("B3VKD");
        ComponentRoomsConfiguration compRoomsConfB3VKD = new ComponentRoomsConfiguration(at);

        at = new AccomType();
        at.setAccomTypeCode("B0SK");
        ComponentRoomsConfiguration compRoomsConfB0SK = new ComponentRoomsConfiguration(compRoomsConfB3VKD, at, 1);

        compRoomsConfB2VKD.setParent(compRoomsConfB3VKD);
        compRoomsConfB3VKD.getChildren().add(compRoomsConfB0SK);
        compRoomsConfB3VKD.getChildren().add(compRoomsConfB2VKD);

        componentRoomsConfigurations.add(compRoomsConfB3VKD);
        return componentRoomsConfigurations;
    }

    @Test
    public void captureOldRoomClassConfig() {
        Integer accomClassId = 1;
        Integer accomTypeId1 = 11;
        Integer accomTypeId2 = 12;
        Set<Integer> expectedSetOfAccomTypes = new HashSet<>();
        expectedSetOfAccomTypes.add(accomTypeId1);
        expectedSetOfAccomTypes.add(accomTypeId2);
        presenter.captureImpactedRoomClassConfig(accomClassId, accomTypeId1);
        presenter.captureImpactedRoomClassConfig(accomClassId, accomTypeId2);
        Map<Integer, Set<Integer>> movedAccomTypes = presenter.getImpactedRoomClassConfigMap();
        assertTrue(movedAccomTypes.containsKey(accomClassId));
        assertEquals(expectedSetOfAccomTypes, movedAccomTypes.get(1));
    }

    @Test
    public void deleteCloseHighestBarOverrides() {
        Integer accomClassId = 1;
        Integer accomTypeId1 = 11;
        Integer accomTypeId2 = 12;
        Set<Integer> expectedSetOfAccomTypes = new HashSet<>();
        expectedSetOfAccomTypes.add(accomTypeId1);
        expectedSetOfAccomTypes.add(accomTypeId2);
        presenter.captureImpactedRoomClassConfig(accomClassId, accomTypeId1);
        presenter.captureImpactedRoomClassConfig(accomClassId, accomTypeId2);
        when(closeHighestBarService.deleteCloseHighestBarForAccomClasses(anyMap())).thenReturn(true);
        assertTrue(presenter.deleteCloseHighestBarOverrides(presenter.getImpactedRoomClassConfigMap()));
    }

    @Test
    public void ifAccomTypeIsMovedThenLV0OverrideOfSourceAndTargetAccomClassShouldConsideredInvalid() {
        Integer sourceAccomClassId = 1;
        Integer targetAccomClassId = 11;
        Integer movedAccomTypeId = 111;

        presenter.captureImpactedRoomClassConfig(sourceAccomClassId, movedAccomTypeId);
        presenter.captureImpactedRoomClassConfig(targetAccomClassId, movedAccomTypeId);

        Map<Integer, Set<Integer>> oldRoomClassConfigMap = presenter.getImpactedRoomClassConfigMap();
        assertTrue(oldRoomClassConfigMap.keySet().contains(sourceAccomClassId));
        assertTrue(oldRoomClassConfigMap.keySet().contains(targetAccomClassId));
    }

    @Test
    public void ifAccomTypeIsMovedBackToSourceThenLV0OverrideInSourceAndDestinationShouldNotGetConsideredInvalid() {
        Integer sourceAccomClassId = 1;
        Integer targetAccomClassId = 11;
        Integer movedAccomTypeId = 111;

        presenter.captureImpactedRoomClassConfig(sourceAccomClassId, movedAccomTypeId);
        presenter.captureImpactedRoomClassConfig(targetAccomClassId, movedAccomTypeId);
        presenter.captureImpactedRoomClassConfig(targetAccomClassId, movedAccomTypeId);
        presenter.captureImpactedRoomClassConfig(sourceAccomClassId, movedAccomTypeId);

        Map<Integer, Set<Integer>> oldRoomClassConfigMap = presenter.getImpactedRoomClassConfigMap();
        oldRoomClassConfigMap.keySet().removeIf(integer -> oldRoomClassConfigMap.get(integer).isEmpty());
        assertFalse(oldRoomClassConfigMap.keySet().contains(sourceAccomClassId));
        assertFalse(oldRoomClassConfigMap.keySet().contains(targetAccomClassId));
    }

    @Test
    public void ifOneRoomTypeOfMultipleMovedRoomTypeIsRemappedToSourceRoomClassThenAlsoLV0OverridesOfSourceRoomClassShouldConsideredInvalid() {
        Integer sourceAccomClassId = 1;
        Integer targetAccomClassId = 11;
        Integer movedAccomTypeId = 111;
        Integer movedAccomTypeId2 = 112;

        presenter.captureImpactedRoomClassConfig(sourceAccomClassId, movedAccomTypeId);
        presenter.captureImpactedRoomClassConfig(targetAccomClassId, movedAccomTypeId);
        presenter.captureImpactedRoomClassConfig(sourceAccomClassId, movedAccomTypeId2);
        presenter.captureImpactedRoomClassConfig(targetAccomClassId, movedAccomTypeId2);

        presenter.captureImpactedRoomClassConfig(targetAccomClassId, movedAccomTypeId);
        presenter.captureImpactedRoomClassConfig(sourceAccomClassId, movedAccomTypeId);


        Map<Integer, Set<Integer>> oldRoomClassConfigMap = presenter.getImpactedRoomClassConfigMap();
        assertTrue(oldRoomClassConfigMap.keySet().contains(sourceAccomClassId));
        assertTrue(oldRoomClassConfigMap.keySet().contains(targetAccomClassId));
        assertTrue(oldRoomClassConfigMap.get(sourceAccomClassId).contains(movedAccomTypeId2));
        assertFalse(oldRoomClassConfigMap.get(sourceAccomClassId).contains(movedAccomTypeId));
    }

    @Test
    public void deleteCloseHighestBarOverridesForEmptyMap() {
        assertFalse(presenter.deleteCloseHighestBarOverrides(new HashMap<>()));
    }

    @Test
    public void testIsBaseClassInInventory_true() {
        when(inventoryGroupService.isBaseClassInInventory(anyObject())).thenReturn(true);
        assertTrue(presenter.isBaseClassInInventory(new AccomClass()));
    }

    @Test
    public void testIsBaseClassInInventory_false() {
        when(inventoryGroupService.isBaseClassInInventory(anyObject())).thenReturn(false);
        assertFalse(presenter.isBaseClassInInventory(new AccomClass()));
    }

    @Test
    public void setAdvancePriceRankConfigurationParameter_Enabled() {
        presenter.setAdvancedPriceRankingEnabled(false);
        presenter.setAdvancePriceRankConfigurationParameter(true);
        verify(configParamsService).updateParameterValue(FeatureTogglesConfigParamName.IS_ADVANCED_PRICE_RANKING_ENABLED.value(), true);
        assertTrue(presenter.isAdvancedPriceRankingEnabled());
    }

    @Test
    public void setAdvancePriceRankConfigurationParameter_Disabled() {
        presenter.setAdvancedPriceRankingEnabled(true);
        presenter.setAdvancePriceRankConfigurationParameter(false);
        verify(configParamsService).updateParameterValue(FeatureTogglesConfigParamName.IS_ADVANCED_PRICE_RANKING_ENABLED.value(), false);
        assertFalse(presenter.isAdvancedPriceRankingEnabled());
    }

    @Test
    public void hasConfigChangedForInventoryGroupView_movedAccomType() throws Exception {
        AccomClass newAccomClass1 = new AccomClass();
        newAccomClass1.setId(1);
        newAccomClass1.setViewOrder(1);
        AccomType accomType1 = new AccomType();
        accomType1.setId(1);
        newAccomClass1.addAccomType(accomType1);
        AccomType accomType2 = new AccomType();
        accomType2.setId(2);
        newAccomClass1.addAccomType(accomType2);

        AccomClass newAccomClass2 = new AccomClass();
        newAccomClass2.setId(2);
        newAccomClass2.setViewOrder(2);
        AccomType accomType3 = new AccomType();
        accomType3.setId(3);
        newAccomClass2.addAccomType(accomType3);
        AccomType accomType4 = new AccomType();
        accomType4.setId(4);
        newAccomClass2.addAccomType(accomType4);


        AccomClass originalAccomClass1 = createRoomClass(accomType1, 1);

        AccomClass originalAccomClass2 = createRoomClass(accomType2, 2);
        originalAccomClass2.addAccomType(accomType3);
        originalAccomClass2.addAccomType(accomType4);

        SortedSet<AccomClass> currentAccomClasses = getAccomClassesSetForTesting(Arrays.asList(newAccomClass1, newAccomClass2));
        FieldUtils.writeField(presenter, "accomClasses", currentAccomClasses, true);

        List<AccomClass> originalAccomClasses = Arrays.asList(originalAccomClass2, originalAccomClass1);
        when(inventoryGroupService.getInventoryAccomClassFromList(anyListOf(AccomClass.class))).thenReturn(originalAccomClasses);

        assertTrue(presenter.hasConfigChangedForInventoryGroupView(new ArrayList()));
    }

    @Test
    public void saveMaximumOccupantsData() {
        List<MaximumOccupantsEntity> entityList = new ArrayList<>(Arrays.asList(new MaximumOccupantsEntity()));
        presenter.saveMaximumOccupantsData(entityList);
        Mockito.verify(perPersonPricingService).save(entityList);
    }

    @Test
    public void getAllAccomTypes() {
        List<AccomType> accomTypes = new ArrayList<>(Arrays.asList(new AccomType()));
        Mockito.when(accommodationService.getAllAccomTypes()).thenReturn(accomTypes);
        assertEquals(accomTypes, presenter.getAllAccomTypes());
        Mockito.verify(accommodationService).getAllAccomTypes();
    }

    @Test
    public void getAllActiveAccomTypes() {
        List<AccomType> accomTypes = new ArrayList<>(Arrays.asList(new AccomType()));
        Mockito.when(accommodationService.getAllActiveAccomTypes()).thenReturn(accomTypes);
        assertEquals(accomTypes, presenter.getAllActiveAccomTypes());
        Mockito.verify(accommodationService).getAllActiveAccomTypes();
    }

    @Test
    public void getMaximumOccupants() {
        List<MaximumOccupantsEntity> entityList = new ArrayList<>(Arrays.asList(new MaximumOccupantsEntity()));
        entityList.get(0).setStatus(1);
        Mockito.when(perPersonPricingService.getMaximumOccupants()).thenReturn(entityList);
        assertEquals(entityList, presenter.getActiveMaximumOccupants());
        Mockito.verify(perPersonPricingService).getMaximumOccupants();
    }

    @Test
    public void validateMaximumOccupantFields() {
        List<MaximumOccupantsEntity> entityList = new ArrayList<>();
        assertEquals(new ArrayList<>(), presenter.validateMaximumOccupantFields(entityList));

        MaximumOccupantsEntity entity = new MaximumOccupantsEntity();
        entity.setMax(100);
        entity.setAdults(1);
        entity.setChildren(1);
        entityList.add(entity);

        assertNotEquals(new ArrayList<>(), presenter.validateMaximumOccupantFields(entityList));
        entity.setAdults(100);
        assertEquals(new ArrayList<>(), presenter.validateMaximumOccupantFields(entityList));
        entity.setMax(0);
        entity.setAdults(0);
        entity.setChildren(0);
        assertNotEquals(new ArrayList<>(), presenter.validateMaximumOccupantFields(entityList));
    }

    @Test
    public void shouldCheckIfMissingRTAlertOpenForRoomTypeMapping() {
        WorkContextType wc = new WorkContextType();
        wc.setPropertyId(PROPERTY_ID);
        PacmanWorkContextHelper.setWorkContext(wc);
        when(roomTypeRecodingService.isExistingMissingRoomTypeAlertOpen()).thenReturn(true);
        assertTrue(presenter.isMissingRTAlertOpenForRoomTypeMapping());
        verify(roomTypeRecodingService).isExistingMissingRoomTypeAlertOpen();
    }

    @Test
    public void shouldCalculate_Min_Max_Avg_ADR_For_ActiveRoomTypes() {
        AccomClass roomClass = buildTestDataForAccomClass();
        final Predicate<AccomTypeADR> accomTypeADRPredicate = adr -> adr.getAccomType().getDisplayStatusId() != 2;
        BigDecimal roomClassMinADR = presenter.getRoomClassMinADR(roomClass, accomTypeADRPredicate);
        assertEquals(BigDecimal.valueOf(1.11), roomClassMinADR);
        BigDecimal roomClassMaxADR = presenter.getRoomClassMaxADR(roomClass, accomTypeADRPredicate);
        assertEquals(BigDecimal.valueOf(3.33), roomClassMaxADR);
        BigDecimal roomClassMinAvgADR = presenter.getRoomClassAvgADR(roomClass, accomTypeADRPredicate);
        assertEquals(BigDecimal.valueOf(4.48), roomClassMinAvgADR);
    }

    @Test
    public void shouldCalculate_Min_Max_Avg_ADR_For_All_Active_Inactive_RoomTypes() {
        AccomClass roomClass = buildTestDataForAccomClass();
        final Predicate<AccomTypeADR> accomTypeADRPredicate = adr -> adr.getAccomType().getDisplayStatusId() == 2 || adr.getAccomType().getDisplayStatusId() == 1;
        BigDecimal roomClassMinADR = presenter.getRoomClassMinADR(roomClass, accomTypeADRPredicate);
        assertEquals(BigDecimal.valueOf(1.05), roomClassMinADR);
        BigDecimal roomClassMaxADR = presenter.getRoomClassMaxADR(roomClass, accomTypeADRPredicate);
        assertEquals(BigDecimal.valueOf(4.44), roomClassMaxADR);
        BigDecimal roomClassMinAvgADR = presenter.getRoomClassAvgADR(roomClass, accomTypeADRPredicate);
        assertEquals(BigDecimal.valueOf(3.78), roomClassMinAvgADR);
    }

    @Test
    public void getGridSizeWhenAdvPathWithoutUpArrow() {
        AccomClass accomClass1 = new AccomClass();
        accomClass1.setId(1);
        AccomClass accomClass2 = new AccomClass();
        accomClass2.setId(2);
        SortedSet<AccomClass> accomClasses = new TreeSet<>();
        accomClasses.add(accomClass1);
        accomClasses.add(accomClass2);
        presenter.setAccomClasses(accomClasses);

        AccomClassPriceRank priceRank1 = new AccomClassPriceRank(accomClass1, accomClass2, true);
        priceRank1.setId(1);

        LinkedList<AccomClassPriceRank> priceRanks = new LinkedList<>(Arrays.asList(priceRank1));
        when(accomClassPriceRankService.getAccomClassPriceRankForAllAccomClasses()).thenReturn(priceRanks);

        // I didn't make these representative of a realistic scenario
        AccomClassPriceRankNetworkArrow arrow1 = new AccomClassPriceRankNetworkArrow(priceRank1, 0, 7, Direction.RIGHT);
        List<AccomClassPriceRankNetworkArrow> expectedNetworkArrows = Arrays.asList(arrow1);
        presenter.setAdvancedNetworkArrows(expectedNetworkArrows);

        int gridSize = presenter.getGridSize();

        assertEquals(1, gridSize);
    }

    @Test
    public void showDiscontinuedRTCheckBoxWhenToggleIsFalse() {
        presenter.setRoomTypeRecodingUIEnabled(false);
        when(accommodationService.getAllDiscontinuedRoomTypes()).thenReturn(new ArrayList<AccomType>());
        assertFalse(presenter.showDiscontinuedRTCheckBox());

        when(accommodationService.getAllDiscontinuedRoomTypes()).thenReturn(Arrays.asList(getAccomType("RT")));
        assertFalse(presenter.showDiscontinuedRTCheckBox());
    }

    @Test
    public void showDiscontinuedRTCheckBoxWhenToggleIsTrue() {
        presenter.setRoomTypeRecodingUIEnabled(true);
        when(accommodationService.getAllDiscontinuedRoomTypes()).thenReturn(new ArrayList<AccomType>());
        assertFalse(presenter.showDiscontinuedRTCheckBox());

        when(accommodationService.getAllDiscontinuedRoomTypes()).thenReturn(Arrays.asList(getAccomType("RT")));
        assertTrue(presenter.showDiscontinuedRTCheckBox());
    }

    private AccomClass buildTestDataForAccomClass() {
        AccomType accomType0 = getAccomType(2, "AT0");
        AccomType accomType1 = getAccomType(1, "AT1");
        AccomType accomType2 = getAccomType(1, "AT2");
        AccomType accomType3 = getAccomType(1, "AT3");
        AccomType accomType4 = getAccomType(2, "AT4");
        AccomTypeADR accomTypeADR0 = new AccomTypeADR(accomType0, BigDecimal.valueOf(3), BigDecimal.valueOf(11), BigDecimal.valueOf(1.05));//2
        AccomTypeADR accomTypeADR1 = new AccomTypeADR(accomType1, BigDecimal.valueOf(6), BigDecimal.valueOf(10), BigDecimal.valueOf(1.11));//2
        AccomTypeADR accomTypeADR2 = new AccomTypeADR(accomType2, BigDecimal.valueOf(5), BigDecimal.valueOf(28), BigDecimal.valueOf(2.22));//4
        AccomTypeADR accomTypeADR3 = new AccomTypeADR(accomType3, BigDecimal.valueOf(10), BigDecimal.valueOf(56), BigDecimal.valueOf(3.33));//5
        AccomTypeADR accomTypeADR4 = new AccomTypeADR(accomType4, BigDecimal.valueOf(30), BigDecimal.valueOf(99), BigDecimal.valueOf(4.44));//3
        List<AccomTypeADR> accomTypeADRs = Arrays.asList(accomTypeADR0, accomTypeADR1, accomTypeADR2, accomTypeADR3, accomTypeADR4);
        when(accommodationService.getAllActiveAccomTypeADRs()).thenReturn(accomTypeADRs);
        presenter.getDataForRoomTypeMappings();

        AccomClass roomClass = new AccomClass();
        roomClass.addAccomType(accomType0);
        roomClass.addAccomType(accomType1);
        roomClass.addAccomType(accomType2);
        roomClass.addAccomType(accomType3);
        roomClass.addAccomType(accomType4);
        return roomClass;
    }


    private AccomType getAccomType(Integer displayStatusId, String rtName) {
        final AccomType accomType = new AccomType();
        accomType.setDisplayStatusId(displayStatusId);
        accomType.setName(rtName);
        return accomType;
    }

    private InfoMgrInstanceEntity getAlertEntity(AlertType alertType) {
        InfoMgrInstanceEntity alertEntity = new InfoMgrInstanceEntity();
        InfoMgrTypeEntity typeEntity = new InfoMgrTypeEntity();
        typeEntity.setName(alertType.name());
        alertEntity.setAlertType(typeEntity);
        return alertEntity;
    }

    @Test
    public void getAllDiscontinuedRoomTypes() {
        List<AccomType> discontinuedAccomTypes = new ArrayList<>();
        discontinuedAccomTypes.add(getAccomType("RT1"));
        discontinuedAccomTypes.add(getAccomType("RT2"));
        when(accommodationService.getAllDiscontinuedRoomTypes()).thenReturn(discontinuedAccomTypes);
        List<String> list = presenter.getAllDiscontinuedRoomTypes();
        assertEquals(2, list.size());
        assertEquals("RT1", list.get(0));
        assertEquals("RT2", list.get(1));
    }

    @Test
    public void getUnplacedAccomClasses() throws IllegalAccessException {
        AccomClass accomClass1 = new AccomClass();
        accomClass1.setName("accomClass1");
        accomClass1.setViewOrder(1);
        AccomClass accomClass2 = new AccomClass();
        accomClass2.setName("accomClass2");
        accomClass2.setSystemDefault(1);
        accomClass2.setViewOrder(2);
        AccomClass accomClass3 = new AccomClass();
        accomClass3.setName("accomClass3");
        accomClass3.setViewOrder(3);
        SortedSet<AccomClass> accomClasses = getAccomClassesSetForTesting(Arrays.asList(accomClass1, accomClass2, accomClass3));
        FieldUtils.writeField(presenter, "accomClasses", accomClasses, true);

        AccomClassPriceRank accomClassPriceRank = new AccomClassPriceRank();
        accomClassPriceRank.setLowerRankAccomClass(accomClass2);
        accomClassPriceRank.setHigherRankAccomClass(accomClass1);

        AccomClassPriceRank accomClassPriceRank2 = new AccomClassPriceRank();
        accomClassPriceRank2.setLowerRankAccomClass(accomClass3);
        accomClassPriceRank2.setHigherRankAccomClass(accomClass2);

        assertEquals(Arrays.asList(accomClass3), presenter.getUnplacedAccomClasses(Arrays.asList(accomClassPriceRank)));
        assertEquals(new ArrayList<AccomClassPriceRank>(), presenter.getUnplacedAccomClasses(Arrays.asList(accomClassPriceRank, accomClassPriceRank2)));
    }

    @Test
    public void updatePriceRankConfiguredState() throws IllegalAccessException {
        AccomClass accomClass1 = new AccomClass();
        accomClass1.setName("accomClass1");
        accomClass1.setIsPriceRankConfigured(AccomClassPriceRankStatus.COMPLETE_RANKED);
        AccomClass accomClass2 = new AccomClass();
        accomClass2.setName("accomClass2");
        accomClass2.setIsPriceRankConfigured(AccomClassPriceRankStatus.COMPLETE_RANKED);
        AccomClass accomClass3 = new AccomClass();
        accomClass3.setName("accomClass3");
        accomClass3.setIsPriceRankConfigured(AccomClassPriceRankStatus.COMPLETE_RANKED);
        SortedSet<AccomClass> accomClasses = getAccomClassesSetForTesting(Arrays.asList(accomClass1, accomClass2, accomClass3));
        FieldUtils.writeField(presenter, "accomClasses", accomClasses, true);

        AccomClassPriceRank accomClassPriceRank = new AccomClassPriceRank();
        accomClassPriceRank.setLowerRankAccomClass(accomClass2);
        accomClassPriceRank.setHigherRankAccomClass(accomClass1);

        AccomClassPriceRank accomClassPriceRank2 = new AccomClassPriceRank();
        accomClassPriceRank2.setLowerRankAccomClass(accomClass3);
        accomClassPriceRank2.setHigherRankAccomClass(accomClass2);

        presenter.updatePriceRankConfiguredState(Arrays.asList(accomClassPriceRank));
        verify(forceSyncEvent).fire(any(ForceSyncEvent.class));
        verify(accommodationService).updateAccomClasses(Arrays.asList(accomClass3, accomClass2, accomClass1), new ArrayList<>(), new ArrayList<>());
        verify(agileRatesConfigurationService, never()).changedRoomClassConfig(any());
        assertEquals(AccomClassPriceRankStatus.COMPLETE_RANKED, accomClass1.getIsPriceRankConfigured());
        assertEquals(AccomClassPriceRankStatus.COMPLETE_RANKED, accomClass2.getIsPriceRankConfigured());
        assertEquals(AccomClassPriceRankStatus.COMPLETE_NOT_RANKED, accomClass3.getIsPriceRankConfigured());
    }

    @Test
    public void hasNewAccomClasses_True() throws IllegalAccessException {
        AccomClass accomClass1 = new AccomClass();
        accomClass1.setName("accomClass1");
        accomClass1.setIsPriceRankConfigured(AccomClassPriceRankStatus.COMPLETE_RANKED);
        AccomClass accomClass2 = new AccomClass();
        accomClass2.setName("accomClass2");
        accomClass2.setIsPriceRankConfigured(AccomClassPriceRankStatus.COMPLETE_NOT_RANKED);
        AccomClass accomClass3 = new AccomClass();
        accomClass3.setName("accomClass3");
        accomClass3.setIsPriceRankConfigured(AccomClassPriceRankStatus.INCOMPLETE);
        SortedSet<AccomClass> accomClasses = getAccomClassesSetForTesting(Arrays.asList(accomClass1, accomClass2, accomClass3));
        FieldUtils.writeField(presenter, "accomClasses", accomClasses, true);
        assertTrue(presenter.hasNewAccomClasses());
    }

    @Test
    public void hasNewAccomClasses_False() throws IllegalAccessException {
        AccomClass accomClass1 = new AccomClass();
        accomClass1.setName("accomClass1");
        accomClass1.setIsPriceRankConfigured(AccomClassPriceRankStatus.COMPLETE_RANKED);
        AccomClass accomClass2 = new AccomClass();
        accomClass2.setName("accomClass2");
        accomClass2.setIsPriceRankConfigured(AccomClassPriceRankStatus.COMPLETE_NOT_RANKED);
        AccomClass accomClass3 = new AccomClass();
        accomClass3.setName("accomClass3");
        accomClass3.setIsPriceRankConfigured(AccomClassPriceRankStatus.COMPLETE_NOT_RANKED);
        SortedSet<AccomClass> accomClasses = getAccomClassesSetForTesting(Arrays.asList(accomClass1, accomClass2, accomClass3));
        FieldUtils.writeField(presenter, "accomClasses", accomClasses, true);
        assertFalse(presenter.hasNewAccomClasses());
    }

    @Test
    public void resetAccomClasses() throws IllegalAccessException {
        AccomClass accomClass1 = new AccomClass();
        accomClass1.setName("accomClass1");
        accomClass1.setIsPriceRankConfigured(AccomClassPriceRankStatus.COMPLETE_RANKED);
        AccomClass accomClass2 = new AccomClass();
        accomClass2.setName("accomClass2");
        accomClass2.setIsPriceRankConfigured(AccomClassPriceRankStatus.COMPLETE_NOT_RANKED);
        AccomClass accomClass3 = new AccomClass();
        accomClass3.setName("accomClass3");
        accomClass3.setIsPriceRankConfigured(AccomClassPriceRankStatus.INCOMPLETE);
        SortedSet<AccomClass> accomClasses = getAccomClassesSetForTesting(Arrays.asList(accomClass1, accomClass2, accomClass3));
        FieldUtils.writeField(presenter, "accomClasses", accomClasses, true);

        presenter.resetAccomClasses();
        verify(accomClassPriceRankService, times(1)).resetAccomClasses(Arrays.asList(accomClass2));
    }

    @Test
    public void shouldNotDetectChangeInMasterClassIfRemappedToSameMasterClass() {
        presenter.accomClasses = new TreeSet<>();

        presenter.resetHasChangeMap();
        presenter.updateHasChanges(RoomConfigurationSelection.MASTER_CLASS_MAPPING, true);

        AccomClass dbMasterClass = createAccomClass(1, "AC1");
        dbMasterClass.setMasterClass(1);
        when(accommodationService.findMasterClass(PacmanWorkContextHelper.getPropertyId())).thenReturn(dbMasterClass);

        AccomClass ac1 = createAccomClass(1, "AC1");
        ac1.setMasterClass(1);
        presenter.accomClasses = new TreeSet<>(Collections.singletonList(ac1));

        assertFalse(presenter.hasOnlyMasterClassChange());
    }

    @Test
    void testSaveGroupPricingConfigAccomTypes_WithGroupPricingCheckboxUnchecked() {
        RoomTypeProductWrapper deluxeRoomTypeProductWrapper = new RoomTypeProductWrapper();

        AccomType deluxe = new AccomType();
        deluxe.setId(2);
        deluxe.setName("DLX");
        deluxe.setAccomTypeCapacity(30);
        deluxe.setDisplayStatusId(1);

        List<AccomType> accomTypes = new ArrayList<>();
        accomTypes.add(deluxe);

        deluxeRoomTypeProductWrapper.setAccomType(deluxe);
        deluxeRoomTypeProductWrapper.setGroupPricing(false);

        List<RoomTypeProductWrapper> roomTypeProductWrappers = new ArrayList<>();
        roomTypeProductWrappers.add(deluxeRoomTypeProductWrapper);

        when(groupPricingConfigurationService.getAllConfigAccomType()).thenReturn(new ArrayList<>());

        ArgumentCaptor<List<GroupPricingConfigAccomType>> captor = ArgumentCaptor.forClass(List.class);
        presenter.saveGroupPricingConfigAccomTypes(roomTypeProductWrappers);

        verify(groupPricingConfigurationService).saveConfigAccomType(captor.capture());

        List<GroupPricingConfigAccomType> actualGroupPricingConfigAccomTypes = captor.getValue();
        List<GroupPricingConfigAccomType> expectedGroupPricingConfigAccomTypes = new ArrayList<>();

        GroupPricingConfigAccomType deluxeGroupPricingConfigAccomType = new GroupPricingConfigAccomType();
        deluxeGroupPricingConfigAccomType.setActive(false);
        deluxeGroupPricingConfigAccomType.setAccomType(deluxe);

        expectedGroupPricingConfigAccomTypes.add(deluxeGroupPricingConfigAccomType);

        assertEquals(expectedGroupPricingConfigAccomTypes.get(0).getAccomType(), actualGroupPricingConfigAccomTypes.get(0).getAccomType());
        assertEquals(expectedGroupPricingConfigAccomTypes.get(0).isActive(), actualGroupPricingConfigAccomTypes.get(0).isActive());

    }

    @Test
    void testSaveGroupPricingConfigAccomTypes_WithGroupPricingCheckboxChecked() {
        RoomTypeProductWrapper standardRoomTypeProductWrapper = new RoomTypeProductWrapper();

        AccomType standard = new AccomType();
        standard.setId(2);
        standard.setName("STD");
        standard.setAccomTypeCapacity(30);
        standard.setDisplayStatusId(1);

        List<AccomType> accomTypes = new ArrayList<>();
        accomTypes.add(standard);

        standardRoomTypeProductWrapper.setAccomType(standard);
        standardRoomTypeProductWrapper.setGroupPricing(true);

        List<RoomTypeProductWrapper> roomTypeProductWrappers = new ArrayList<>();
        roomTypeProductWrappers.add(standardRoomTypeProductWrapper);

        when(groupPricingConfigurationService.getAllConfigAccomType()).thenReturn(new ArrayList<>());

        ArgumentCaptor<List<GroupPricingConfigAccomType>> captor = ArgumentCaptor.forClass(List.class);
        presenter.saveGroupPricingConfigAccomTypes(roomTypeProductWrappers);

        verify(groupPricingConfigurationService).saveConfigAccomType(captor.capture());

        List<GroupPricingConfigAccomType> actualGroupPricingConfigAccomTypes = captor.getValue();
        List<GroupPricingConfigAccomType> expectedGroupPricingConfigAccomTypes = new ArrayList<>();

        GroupPricingConfigAccomType standardGroupPricingConfigAccomType = new GroupPricingConfigAccomType();
        standardGroupPricingConfigAccomType.setActive(true);
        standardGroupPricingConfigAccomType.setAccomType(standard);

        expectedGroupPricingConfigAccomTypes.add(standardGroupPricingConfigAccomType);

        assertEquals(expectedGroupPricingConfigAccomTypes.get(0).getAccomType(), actualGroupPricingConfigAccomTypes.get(0).getAccomType());
        assertEquals(expectedGroupPricingConfigAccomTypes.get(0).isActive(), actualGroupPricingConfigAccomTypes.get(0).isActive());

    }

    @Test
    void testSaveGroupPricingConfigAccomTypes_WithExistingEntriesInTable() {
        RoomTypeProductWrapper standardRoomTypeProductWrapper = new RoomTypeProductWrapper();

        AccomType standard = new AccomType();
        standard.setId(2);
        standard.setName("STD");
        standard.setAccomTypeCapacity(30);
        standard.setDisplayStatusId(1);

        AccomType deluxe = new AccomType();
        deluxe.setId(1);
        deluxe.setName("DLX");
        deluxe.setAccomTypeCapacity(25);
        deluxe.setDisplayStatusId(1);

        GroupPricingConfigAccomType deluxeGroupPricingConfigAccomType = new GroupPricingConfigAccomType();
        deluxeGroupPricingConfigAccomType.setActive(true);
        deluxeGroupPricingConfigAccomType.setAccomType(deluxe);

        List<GroupPricingConfigAccomType> groupPricingConfigAccomTypes = new ArrayList<>();
        groupPricingConfigAccomTypes.add(deluxeGroupPricingConfigAccomType);

        standardRoomTypeProductWrapper.setAccomType(standard);
        standardRoomTypeProductWrapper.setGroupPricing(true);

        List<RoomTypeProductWrapper> roomTypeProductWrappers = new ArrayList<>();
        roomTypeProductWrappers.add(standardRoomTypeProductWrapper);

        when(groupPricingConfigurationService.getAllConfigAccomType()).thenReturn(groupPricingConfigAccomTypes);

        ArgumentCaptor<List<GroupPricingConfigAccomType>> captor = ArgumentCaptor.forClass(List.class);
        presenter.saveGroupPricingConfigAccomTypes(roomTypeProductWrappers);

        verify(groupPricingConfigurationService).saveConfigAccomType(captor.capture());

        List<GroupPricingConfigAccomType> actualGroupPricingConfigAccomTypes = captor.getValue();
        List<GroupPricingConfigAccomType> expectedGroupPricingConfigAccomTypes = new ArrayList<>();

        GroupPricingConfigAccomType standardGroupPricingConfigAccomType = new GroupPricingConfigAccomType();
        standardGroupPricingConfigAccomType.setActive(true);
        standardGroupPricingConfigAccomType.setAccomType(standard);

        expectedGroupPricingConfigAccomTypes.add(standardGroupPricingConfigAccomType);
        expectedGroupPricingConfigAccomTypes.add(deluxeGroupPricingConfigAccomType);

        assertEquals(expectedGroupPricingConfigAccomTypes.get(0).getAccomType(), actualGroupPricingConfigAccomTypes.get(0).getAccomType());
        assertEquals(expectedGroupPricingConfigAccomTypes.get(0).isActive(), actualGroupPricingConfigAccomTypes.get(0).isActive());

        assertEquals(expectedGroupPricingConfigAccomTypes.get(1).getAccomType(), actualGroupPricingConfigAccomTypes.get(1).getAccomType());
        assertEquals(expectedGroupPricingConfigAccomTypes.get(1).isActive(), actualGroupPricingConfigAccomTypes.get(1).isActive());

    }


    @Test
    public void shouldNotDetectChangeInMasterClassWhenMultipleMappingsAreDone() {
        presenter.accomClasses = new TreeSet<>();

        presenter.resetHasChangeMap();
        presenter.updateHasChanges(RoomConfigurationSelection.ROOM_TYPE_MAPPING, true);
        presenter.updateHasChanges(RoomConfigurationSelection.MASTER_CLASS_MAPPING, true);
        assertFalse(presenter.hasOnlyMasterClassChange());
    }

    @Test
    public void shouldNotDetectChangeInMasterClassWhenMappingIsDifferent() {
        presenter.accomClasses = new TreeSet<>();

        presenter.resetHasChangeMap();
        presenter.updateHasChanges(RoomConfigurationSelection.ROOM_TYPE_MAPPING, true);
        assertFalse(presenter.hasOnlyMasterClassChange());
    }

    @Test
    public void shouldDetectOnlyMasterClassChange() {
        presenter.resetHasChangeMap();
        presenter.updateHasChanges(RoomConfigurationSelection.MASTER_CLASS_MAPPING, true);

        AccomClass dbMasterClass = createAccomClass(2, "AC2");
        dbMasterClass.setMasterClass(1);
        when(accommodationService.findMasterClass(PacmanWorkContextHelper.getPropertyId())).thenReturn(dbMasterClass);

        AccomClass ac1 = createAccomClass(1, "AC1");
        ac1.setMasterClass(1);
        presenter.accomClasses = new TreeSet<>(Collections.singletonList(ac1));

        assertTrue(presenter.hasOnlyMasterClassChange());
    }

    @Test
    public void getPdfFileName() {
        LocalDate date = new LocalDate(2018, 03, 11);
        when(presenter.getSystemDateAsLocalDate()).thenReturn(date);
        when(uiContext.getPropertyId()).thenReturn(5);
        when(dateService.getUserDateFormat()).thenReturn("dd-MMM-yyyy");

        String pdfName = presenter.getPdfFileName();
        assertEquals("RoomsConfiguration_P5_11-Mar-2018", pdfName);
    }

    @Test
    public void hasRoomClassAgileRatesProducts() {
        when(agileRatesConfigurationService.hasRoomClassProducts()).thenReturn(true);
        assertTrue(presenter.hasRoomClassAgileRatesProducts());
        when(agileRatesConfigurationService.hasRoomClassProducts()).thenReturn(false);
        assertFalse(presenter.hasRoomClassAgileRatesProducts());
    }

    @Test
    public void resetMinimumPriceDifferentials() {
        //given
        MinimumPriceDifferentialUIWrapper uiWrapper1 = mock(MinimumPriceDifferentialUIWrapper.class);
        MinimumPriceDifferentialUIWrapper uiWrapper2 = mock(MinimumPriceDifferentialUIWrapper.class);
        presenter.setDiffUiWrappers(Arrays.asList(uiWrapper1, uiWrapper2));

        //when
        presenter.resetCurrentMinimumPriceDifferentials();

        //then
        verify(uiWrapper1).setSimplePathDiff(BigDecimal.ZERO);
        verify(uiWrapper2).setSimplePathDiff(BigDecimal.ZERO);
    }

    @Test
    public void hasAdvancedSettingsTrueOneDiffNotEqual() {
        //given
        AccomClassMinPriceDiff priceDiff1 = new AccomClassMinPriceDiff();
        priceDiff1.setSundayDiffWithTax(BigDecimal.TEN);
        AccomClassMinPriceDiff priceDiff2 = new AccomClassMinPriceDiff();
        MinimumPriceDifferentialUIWrapper uiWrapper1 = new MinimumPriceDifferentialUIWrapper(priceDiff1);
        MinimumPriceDifferentialUIWrapper uiWrapper2 = new MinimumPriceDifferentialUIWrapper(priceDiff2);
        presenter.setDiffUiWrappers(Arrays.asList(uiWrapper1, uiWrapper2));

        //when
        boolean hasAdvancedSettings = presenter.hasAdvancedSettings();

        //then
        assertTrue(hasAdvancedSettings);
    }

    @Test
    public void hasAdvancedSettingsTrueSeasonExists() {
        //given
        MinimumPriceDifferentialUIWrapper uiWrapper1 = mock(MinimumPriceDifferentialUIWrapper.class);
        MinimumPriceDifferentialUIWrapper uiWrapper2 = mock(MinimumPriceDifferentialUIWrapper.class);
        presenter.setDiffUiWrappers(Arrays.asList(uiWrapper1, uiWrapper2));

        //when
        presenter.hasAdvancedSettings();

        //then
        verify(uiWrapper1).doesNotHaveAllEqualValues();
        verify(uiWrapper2).doesNotHaveAllEqualValues();
    }

    @Test
    public void isTaxInclusivePropertyTrue_OnViewOpenedBothTogglesTrue() {
        //given
        presenter.setCPProperty(true);
        presenter.onViewOpened(null);

        //when
        boolean taxInclusiveProperty = presenter.isCPProperty();

        //then
        assertTrue(taxInclusiveProperty);
    }

    @Test
    public void isTaxInclusivePropertyFalse_OnViewOpenedBothTogglesFalse() {
        //given
        presenter.setCPProperty(false);
        presenter.onViewOpened(null);

        //when
        boolean taxInclusiveProperty = presenter.isCPProperty();

        //then
        assertFalse(taxInclusiveProperty);
    }

    @Test
    public void isTaxInclusivePropertyFalse_OnViewOpenedContinuousPricingTaxInclusiveTrue() {
        //given
        presenter.setCPProperty(false);
        presenter.onViewOpened(null);

        //when
        boolean taxInclusiveProperty = presenter.isCPProperty();

        //then
        assertFalse(taxInclusiveProperty);
    }

    private AccomClass createAccomClass(Integer id, String code) {
        AccomClass accomClass = new AccomClass();
        accomClass.setId(id);
        accomClass.setCode(code);
        accomClass.setName(code);
        return accomClass;
    }

    private AccomType getAccomType(String rt) {
        AccomType at1 = new AccomType();
        at1.setAccomTypeCode(rt);
        at1.setDisplayStatusId(2);
        return at1;
    }

    @Test
    public void validateSeasonForMultipleTaxesWhenSeasonDoNotSpanThroughMultipleTaxes() {
        LocalDate startDate = LocalDate.now();
        LocalDate endDate = startDate.plusDays(10);
        AccomClassMinPriceDiffSeason season = new AccomClassMinPriceDiffSeason();
        season.setStartDate(startDate);
        season.setEndDate(endDate);
        MinimumPriceDifferentialUIWrapper seasonWrapper = new MinimumPriceDifferentialUIWrapper(season);
        seasonWrapper.setSunday(BigDecimal.TEN);
        when(taxService.getPartialOverlappingTaxSeasons(startDate, endDate)).thenReturn(Collections.emptyList());
        when(lang.getText(anyString(), anyString())).thenReturn("some message");

        boolean isSeasonValid = presenter.validateSeasonForMultipleTaxes(seasonWrapper);

        assertTrue(isSeasonValid);
    }

    @Test
    public void validateSeasonForMultipleTaxesWhenSeasonSpansThroughMultipleTaxes() {
        LocalDate startDate = LocalDate.now();
        LocalDate endDate = startDate.plusDays(10);
        AccomClassMinPriceDiffSeason season = new AccomClassMinPriceDiffSeason();
        season.setStartDate(startDate);
        season.setEndDate(endDate);
        MinimumPriceDifferentialUIWrapper seasonWrapper = new MinimumPriceDifferentialUIWrapper(season);
        seasonWrapper.setSunday(BigDecimal.TEN);
        Tax tax = new Tax();
        tax.setStartDate(startDate);
        tax.setEndDate(startDate.plusDays(5));
        when(taxService.getPartialOverlappingTaxSeasons(startDate, endDate)).thenReturn(Arrays.asList(tax));
        when(lang.getText(anyString(), anyString())).thenReturn("some message");

        boolean isSeasonValid = presenter.validateSeasonForMultipleTaxes(seasonWrapper);

        assertFalse(isSeasonValid);
    }

    @Test
    void testInvocationSetLV0OffsetToZeroForNewRoomType_IsCp_And_NonHilton() throws IllegalAccessException {
        setUpSaveRoomClasses();
        presenter.saveRoomClasses(true);
        verify(qualifiedRateService, never()).setLV0OffsetToZeroForNewRoomType();
    }

    @Test
    void testInvocationSetLV0OffsetToZeroForNewRoomType_IsNonCp_And_Hilton() throws IllegalAccessException {
        setUpSaveRoomClasses();
        presenter.saveRoomClasses(true);
        presenter.setCPProperty(false);
        when(configParamsService.getParameterValue(IntegrationConfigParamName.CORE_PROPERTY_EXTERNAL_SYSTEM)).thenReturn("XYZ");
        verify(qualifiedRateService, never()).setLV0OffsetToZeroForNewRoomType();
    }

    @Test
    void testInvocationSetLV0OffsetToZeroForNewRoomType_IsCp_And_Hilton() throws IllegalAccessException {
        setUpSaveRoomClasses();
        presenter.setCPProperty(true);
        when(clientService.isHiltonByExternalSystem()).thenReturn(true);
        presenter.saveRoomClasses(true);
        verify(qualifiedRateService).setLV0OffsetToZeroForNewRoomType();
    }

    @Test
    public void testLV0OverridesNotRemovedWhenUnassignedRoomTypeMappedToExistingRoomClass() {
        AccomClass unassignedRC = new AccomClass();
        unassignedRC.setId(1);

        AccomClass roomClassWithUnassignedRoomType = new AccomClass();
        roomClassWithUnassignedRoomType.setId(2);

        AccomClass unaffectedRoomClass = new AccomClass();
        unaffectedRoomClass.setId(3);

        AccomType unassignedRT = new AccomType();
        unassignedRT.setId(1);

        AccomType unaffectedRoomType = new AccomType();
        unaffectedRoomType.setId(2);

        List<AccomType> allUnassignedAccomTypes = new ArrayList<>();
        allUnassignedAccomTypes.add(unassignedRT);

        when(accommodationService.getAllUnassignedRoomTypesIncludingZeroCapacity()).thenReturn(allUnassignedAccomTypes);
        presenter.captureImpactedRoomClassConfig(unassignedRC.getId(), unassignedRT.getId());
        presenter.captureImpactedRoomClassConfig(roomClassWithUnassignedRoomType.getId(), unassignedRT.getId());
        presenter.captureImpactedRoomClassConfig(unaffectedRoomClass.getId(), unaffectedRoomType.getId());

        Map<Integer, Set<Integer>> result = presenter.findRoomClassesWithoutUnassignedRoomTypes();
        assertEquals(2, result.size());
        assertNull(result.get(roomClassWithUnassignedRoomType.getId()));
        assertNotNull(result.get(unassignedRC.getId()));
        assertNotNull(result.get(unaffectedRoomClass.getId()));
    }

    @Test
    void deactivateAnalyticsRelatedTables() {
        presenter.deactivateOverridesFromAnalyticsTables();
        verify(closeHighestBarService).deactivateOverridesFromAnalyticsTables(any());
    }

    @Test
    void verifyRoomClassExclusionInROHGroupEvaluationEnabled() {
        presenter.isRoomClassExclusionInROHGroupEvaluationEnabled();

        verify(configParamsService).getBooleanParameterValue(ENABLE_ROOM_CLASS_EXCLUSION_IN_ROH_GROUP_EVALUATION);
    }

    @Test
    void verifyUpdateROHGroupEvaluationExclusionFlagFor() {
        AccomClass accomClass = new AccomClass();
        presenter.updateROHGroupEvaluationExclusionFlagFor(accomClass);

        verify(accommodationService).updateROHGroupEvaluationExclusionFlagFor(accomClass);
    }

    @Test
    public void shouldCallTheCostOfWalkCalculatorMethod() {
        when(syncEventAggregatorService.registerSyncEvent(SyncEvent.COST_OF_WALK_CONFIG_CHANGED)).thenReturn(true);
        presenter.calculateCostOfWalk();
        verify(costOfWalkCalculator, times(1)).refreshDefaultCostOfWalkValues();
        verify(costofWalkService, times(1)).turnOnCostOfWalkConfigChangedSyncFlag();
    }

    @Test
    void shouldEnableLTBDEForPricingWhenLTBDEProccessingToggleIsEnable() {
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_LTBDE_ON_CONFIG_CHANGE)).thenReturn(true);
        presenter.enableLTBDEForPricing();
        verify(pricingConfigurationLTBDEService).enableLTBDEForPricing(true);
    }

    @Test
    void shouldNotEnableLTBDEForPricingWhenLTBDEProccessingToggleIsDisabled() {
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_LTBDE_ON_CONFIG_CHANGE)).thenReturn(false);
        presenter.enableLTBDEForPricing();
        verify(pricingConfigurationLTBDEService, times(0)).enableLTBDEForPricing(true);
    }

    @Test
    void shouldNotEnableLTBDEForPricingWhenToggleIsOff() {
        presenter.onViewOpened(null);
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_LTBDE_ON_CONFIG_CHANGE)).thenReturn(false);

        presenter.enableLTBDEForRoomTypeMappingChanges();

        verify(pricingConfigurationLTBDEService, times(0)).enableLTBDEForPricing(true);


        presenter.updateHasChanges(RoomConfigurationSelection.ROOM_TYPE_MAPPING, true);
        presenter.enableLTBDEForRoomTypeMappingChanges();

        verify(pricingConfigurationLTBDEService, times(0)).enableLTBDEForPricing(true);
    }

    @Test
    void shouldNotEnableLTBDEForPricingWhenRoomTypeMappingHasNoChanges() {
        presenter.onViewOpened(null);
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_LTBDE_ON_CONFIG_CHANGE)).thenReturn(true);

        presenter.enableLTBDEForRoomTypeMappingChanges();

        verify(pricingConfigurationLTBDEService, times(0)).enableLTBDEForPricing(true);
    }

    @Test
    void shouldEnableLTBDEForPricingWhenRoomTypeMappingHasChanges() {
        presenter.onViewOpened(null);
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_LTBDE_ON_CONFIG_CHANGE)).thenReturn(true);

        presenter.updateHasChanges(RoomConfigurationSelection.ROOM_TYPE_MAPPING, true);
        presenter.enableLTBDEForRoomTypeMappingChanges();

        verify(pricingConfigurationLTBDEService).enabledLTBDEIfRoomTypeIsAttachedToActiveSmallGroup(anySet());
    }

    @Test
    void shouldCallEnableLTBDEForPricingIfApplicable() {
        presenter.enableLTBDEFlagForPricingOnIndirectConfigChangeIfApplicable();
        verify(pricingConfigurationLTBDEService).enableLTBDEOnIndirectConfigChangedIfApplicable();
    }

    @Test
    void shouldMakeAllRoomTypesAsNonSpecialRoomTypesWhenDeletingSpecialRoomTypeConfiguration() {
        presenter.deleteSpecialUseRoomTypeConfiguration();
        long count = presenter.getAccomClasses().stream().flatMap(accomClass -> accomClass.getAccomTypes().stream()
                .filter(accomType -> accomType.getExcludedFromSoldout() != 0)).count();
        assertEquals(0, count);

    }

    @Test
    void shouldReturnTrueIfPropertyIsLimitedEdition() {
        when(configParamsService.getParameterValue(FeatureTogglesConfigParamName.G3_RMS_CORE_SUBSCRIPTION_TYPE))
                .thenReturn("Limited Edition");
        presenter.setParameters();
        boolean isLimitedEditionProperty = presenter.isLimitedEditionProperty();
        assertTrue(isLimitedEditionProperty);
    }

    @Test
    void shouldReturnFalseIfPropertyIsNotLimitedEdition() {
        when(configParamsService.getParameterValue(FeatureTogglesConfigParamName.G3_RMS_CORE_SUBSCRIPTION_TYPE))
                .thenReturn("Not Defined");
        presenter.setParameters();
        boolean isLimitedEditionProperty = presenter.isLimitedEditionProperty();
        assertFalse(isLimitedEditionProperty);
    }

    @Test
    void shouldReturnTrueIfAutomatedOvbkReductionParamValueIsOne(){
        when(propertyAttributeService.getAttributeValueByAttributeName(PropertyAttributeEnum.ENABLED_AUTOMATED_OVBK_REDUCTION.getAttributeName())).thenReturn("1");
        boolean result = presenter.isAutomatedOvbkReductionEnabled();
        verify(propertyAttributeService).getAttributeValueByAttributeName(PropertyAttributeEnum.ENABLED_AUTOMATED_OVBK_REDUCTION.getAttributeName());
        assertTrue(result);
    }

    @Test
    void shouldReturnFalseIfAutomatedOvbkReductionParamValueIsZero(){
        when(propertyAttributeService.getAttributeValueByAttributeName(PropertyAttributeEnum.ENABLED_AUTOMATED_OVBK_REDUCTION.getAttributeName())).thenReturn("0");
        boolean result = presenter.isAutomatedOvbkReductionEnabled();
        verify(propertyAttributeService).getAttributeValueByAttributeName(PropertyAttributeEnum.ENABLED_AUTOMATED_OVBK_REDUCTION.getAttributeName());
        assertFalse(result);
    }

    @Test
    void shouldUpdateAutomatedObvkReductionParamValueWithOneWhenEnabled() {
        presenter.updateAutomatedOvbkReductionEnabledParam(true);
        verify(propertyAttributeService).updateDefaultPropertyAttributeValue(PropertyAttributeEnum.ENABLED_AUTOMATED_OVBK_REDUCTION.getAttributeName(), "1");
    }

    @Test
    void shouldUpdateAutomatedObvkReductionParamValueWithOneWhenDisabled() {
        presenter.updateAutomatedOvbkReductionEnabledParam(false);
        verify(propertyAttributeService).updateDefaultPropertyAttributeValue(PropertyAttributeEnum.ENABLED_AUTOMATED_OVBK_REDUCTION.getAttributeName(), "0");
    }

    @Test
    void shouldReturnCloseCompititorThresholdParamValue() {
        when(propertyAttributeService.getAttributeValueByAttributeName(PropertyAttributeEnum.CLOSED_COMPITITOR_RATIO.getAttributeName())).thenReturn(".20");
        Integer result = presenter.getCloseCompititorThreshold();
        verify(propertyAttributeService).getAttributeValueByAttributeName(PropertyAttributeEnum.CLOSED_COMPITITOR_RATIO.getAttributeName());
        assertEquals(20, result);
    }

    @Test
    void shouldUpdateCloseCompititorThresholdParamValueInRatioWhenThresholdIsNonZero() {
        presenter.updateThreshold(20);
        verify(propertyAttributeService).updateDefaultPropertyAttributeValue(PropertyAttributeEnum.CLOSED_COMPITITOR_RATIO.getAttributeName(), "0.2");
    }

    @Test
    void shouldUpdateCloseCompititorThresholdParamValueInRationWhenThresholdIsZero() {
        presenter.updateThreshold(0);
        verify(propertyAttributeService).updateDefaultPropertyAttributeValue(PropertyAttributeEnum.CLOSED_COMPITITOR_RATIO.getAttributeName(), "0");

    }

    @Test
    void shouldReturnTrueWhenAutomatedOverbookingReductionConfigParamIsTrue() {
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.AUTOMATED_OVERBOOKING_ENABLED)).thenReturn(true);
        assertTrue(presenter.isAutomatedOverbookingReductionEnabled());
    }

    @Test
    void shouldReturnFalseWhenAutomatedOverbookingReductionConfigParamIsFalse() {
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.AUTOMATED_OVERBOOKING_ENABLED)).thenReturn(false);
        assertFalse(presenter.isAutomatedOverbookingReductionEnabled());
    }

    private List<PricingAccomClass> getPricingAccomClass(boolean pricingAsSumOfParts) {
        List<PricingAccomClass> list = new ArrayList<>();
        PricingAccomClass pc = new PricingAccomClass();
        AccomClass ac = new AccomClass();
        AccomType at = new AccomType();
        at.setAccomTypeCode("SH1K");
        ac.setAccomTypes(new HashSet<>(Arrays.asList(at)));
        pc.setAccomClass(ac);
        pc.setPriceAsSumOfParts(pricingAsSumOfParts);
        list.add(pc);
        return list;
    }

    private SortedSet<AccomClass> getAccomClasses() {
        SortedSet<AccomClass> set = new TreeSet<>();
        set.add(getAccomClass(1, "SH1K", "DQQ"));
        set.add(getAccomClass(2, "SH1Q", "DK"));
        set.add(getAccomClass(3, "SH2KQ"));
        return set;
    }

    private AccomClass getAccomClass(int id, String... ats) {
        AccomClass ac = new AccomClass();
        ac.setRankOrder(id);
        ac.setCode("AC" + id);
        for (String atCode : ats) {
            AccomType at = new AccomType();
            at.setAccomTypeCode(atCode);
            ac.addAccomType(at);
        }
        return ac;
    }

    private List<HospitalityRoomsConfig> getHospitalityRoomsConfig() {
        List<HospitalityRoomsConfig> list = new ArrayList<>();
        list.add(new HospitalityRoomsConfig("SH1K", "DK", 1, false));
        list.add(new HospitalityRoomsConfig("SH1Q", "DQQ", 1, false));
        list.add(new HospitalityRoomsConfig("SH2KQ", "DK", 1, false));
        list.add(new HospitalityRoomsConfig("SH2KQ", "DQQ", 1, false));
        return list;
    }

    @Test
    public void shouldInvokeGetAllUnassignedRoomTypesIncludingZeroCapacity() {
        AccomType stdKingRoom = createNewAccomType("SKR","Standard King Room",  1);
        AccomType stdQueenRoom = createNewAccomType("SDQ","Two Queen Beds",  1);
        when(accommodationService.getAllUnassignedRoomTypesIncludingZeroCapacity()).thenReturn(List.of(stdKingRoom,stdQueenRoom));
        assertEquals(2, presenter.getAllUnassignedAccomTypesIncludingZeroCapacity().size());
        AccomType stdPremium = createNewAccomType("SPR", "Three Queen Beds",  2);
        when(accommodationService.getAllUnassignedRoomTypesIncludingZeroCapacity()).thenReturn(List.of(stdKingRoom,stdQueenRoom,stdPremium));
        assertEquals(2, presenter.getAllUnassignedAccomTypesIncludingZeroCapacity().size());
    }
    private AccomType createNewAccomType(String code, String name, int displayStatusId) {
        AccomType accomType = new AccomType();
        accomType.setPropertyId(PacmanWorkContextHelper.getPropertyId());
        accomType.setAccomTypeCode(code);
        accomType.setName(name);
        accomType.setDisplayStatusId(displayStatusId);
        return accomType;
    }
}
