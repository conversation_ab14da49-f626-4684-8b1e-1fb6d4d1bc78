package com.ideas.tetris.ui.modules.pricingoverrides;

import com.ideas.infra.tetris.security.domain.TetrisPermissionKey;
import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomClass;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomType;
import com.ideas.tetris.pacman.services.agilerates.configuration.entity.ProductGroup;
import com.ideas.tetris.pacman.services.agilerates.configuration.entity.ProductPackage;
import com.ideas.tetris.pacman.services.agilerates.configuration.entity.ProductRateOffsetOverride;
import com.ideas.tetris.pacman.services.agilerates.configuration.service.AgileRatesConfigurationService;
import com.ideas.tetris.pacman.services.bestavailablerate.*;
import com.ideas.tetris.pacman.services.bestavailablerate.dto.BAROverride;
import com.ideas.tetris.pacman.services.bestavailablerate.dto.CPBARDecisionDTO;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.*;
import com.ideas.tetris.pacman.services.businessanalysis.BusinessAnalysisDashboardService;
import com.ideas.tetris.pacman.services.businessanalysis.dto.BusinessAnalysisDailyDataDto;
import com.ideas.tetris.pacman.services.businessanalysis.dto.BusinessAnalysisDailyIndicatorDto;
import com.ideas.tetris.pacman.services.businessanalysis.dto.BusinessAnalysisSpecialEventDto;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.dateservice.dto.DateParameter;
import com.ideas.tetris.pacman.services.dateservice.dto.DateTimeParameter;
import com.ideas.tetris.pacman.services.decision.DecisionService;
import com.ideas.tetris.pacman.services.decisiondelivery.dto.InventoryLimitDecision;
import com.ideas.tetris.pacman.services.groupflooroverride.GroupFloorOverrideService;
import com.ideas.tetris.pacman.services.groupflooroverride.entity.GroupFloorOverride;
import com.ideas.tetris.pacman.services.groupflooroverride.entity.GroupFloorOverrideReason;
import com.ideas.tetris.pacman.services.hospitalityrooms.service.HospitalityRoomsService;
import com.ideas.tetris.pacman.services.inventorylimit.InventoryLimitDecisionService;
import com.ideas.tetris.pacman.services.inventorylimit.entity.DecisionGPInventoryLimit;
import com.ideas.tetris.pacman.services.inventorylimit.entity.DecisionGPInventoryLimitOverride;
import com.ideas.tetris.pacman.services.perpersonpricing.MaximumOccupantsEntity;
import com.ideas.tetris.pacman.services.perpersonpricing.OccupantBucketEntity;
import com.ideas.tetris.pacman.services.perpersonpricing.PerPersonPricingService;
import com.ideas.tetris.pacman.services.pricing.ProductManagementService;
import com.ideas.tetris.pacman.services.pricingconfiguration.entity.PricingAccomClass;
import com.ideas.tetris.pacman.services.pricingconfiguration.service.PricingConfigurationService;
import com.ideas.tetris.pacman.services.pricingconfiguration.service.Supplement;
import com.ideas.tetris.pacman.services.product.Product;
import com.ideas.tetris.pacman.services.property.PropertyService;
import com.ideas.tetris.pacman.services.sasoptimization.dto.SimplifiedWhatIfDateData;
import com.ideas.tetris.pacman.services.sasoptimization.dto.SimplifiedWhatIfResult;
import com.ideas.tetris.pacman.services.sasoptimization.service.SimplifiedWhatIfService;
import com.ideas.tetris.pacman.services.tax.entity.Tax;
import com.ideas.tetris.pacman.services.tax.service.TaxService;
import com.ideas.tetris.platform.common.entity.DayOfWeek;
import com.ideas.tetris.platform.services.Stage;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import com.ideas.tetris.ui.VaadinUIBaseJupiterTest;
import com.ideas.tetris.ui.common.security.UiContext;
import com.ideas.tetris.ui.common.util.DateUtil;
import com.ideas.tetris.ui.modules.ataglance.InventoryGroupDto;
import com.ideas.tetris.ui.modules.commons.continuouspricing.*;
import com.ideas.tetris.ui.modules.investigator.InvestigatorDto;
import com.ideas.tetris.ui.modules.pricing.views.AdditionalInformationFilterEnum;
import com.ideas.tetris.ui.modules.pricing.views.CPPricingManagementOverrideHtmlConverter;
import com.ideas.tetris.ui.modules.reports.usermodified.Status;
import com.vaadin.server.Page;
import com.vaadin.server.WebBrowser;
import org.apache.commons.lang3.tuple.Pair;
import org.joda.time.LocalDate;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName.ENABLE_UPLOAD_ON_OVERRIDE_FOR_EXTENDED_WINDOW;
import static com.ideas.tetris.pacman.services.bestavailablerate.entity.OccupancyType.SINGLE;
import static com.ideas.tetris.platform.common.utils.dateutil.DateUtil.DEFAULT_DATE_FORMAT;
import static com.ideas.tetris.ui.common.util.UiUtils.getText;
import static com.ideas.tetris.ui.modules.pricingoverrides.PricingOverrideManager.OVERRIDDEN;
import static java.util.Arrays.asList;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class PricingOverrideManagerTest extends VaadinUIBaseJupiterTest {
    private static final LocalDate LOCAL_DATE = new LocalDate();
    private static final LocalDate LOCAL_DATE_PLUS_2 = new LocalDate().plusDays(2);
    private static final LocalDate LOCAL_DATE_PLUS_1 = new LocalDate().plusDays(1);
    private static final BigDecimal CEILING_OVERRIDE = new BigDecimal("200.00");
    private static final BigDecimal ORIGINAL_CEILING_OVERRIDE = new BigDecimal("210.00");
    private static final BigDecimal FLOOR_OVERRIDE = new BigDecimal("180.00");
    private static final BigDecimal ORIGINAL_FLOOR_OVERRIDE = new BigDecimal("170.00");
    private static final BigDecimal SPECIFIC_OVERRIDE = new BigDecimal("195.00");
    private static final BigDecimal ORIGINAL_SPECIFIC_OVERRIDE = new BigDecimal("192.00");
    private static final BigDecimal ROUNDED_BAR = new BigDecimal(203.50);
    private static final BigDecimal ORIGINAL_ROUNDED_BAR = new BigDecimal(203.50);
    private static final String OVERRIDE_ICON_HTML = "<span>Image</span>";
    @Mock
    PacmanConfigParamsService configParamsService;
    @Mock
    HashMap<CPDecisionBAROutput, CPOverrideWrapper> overridesMap;
    @Mock
    private ProductManagementService service;
    @Mock
    private PropertyService propertyService;
    @Mock
    private BusinessAnalysisDashboardService businessAnalysisDashboardService;
    @Mock
    private CPPricingFilter cpPricingFilter;
    @Mock
    private PricingConfigurationService pricingConfigurationService;
    @InjectMocks
    private PricingOverrideManager pricingOverrideManager;
    @Mock
    private CPDecisionContext cpDecisionContext;
    @Mock
    private SimplifiedWhatIfService simplifiedWhatIfService;
    @Mock
    private AccomTypeSupplementService supplementService;
    @Mock
    private GroupFloorOverrideService groupFloorOverrideService;
    @Mock
    private PrettyPricingService prettyPricingService;
    @Mock
    private PerPersonPricingService perPersonPricingService;
    @Mock
    private TaxService taxService;
    @Mock
    private DateService dateService;
    @Mock
    private DecisionService decisionService;
    @Mock
    private AgileRatesConfigurationService agileRatesConfigurationService;
    @Mock
    private HospitalityRoomsService hospitalityRoomsService;
    @Mock
    private InventoryLimitDecisionService inventoryLimitDecisionService;

    @Mock
    private UiContext uiContext;

    @Test
    public void wrapDecisions() {
        final CPPricingFilter filter = setUpForWrapDecisionTest();
        pricingOverrideManager.setCpPricingFilter(filter);

        final List<CPBARDecisionUIWrapper> cpbarDecisionUIWrappers = pricingOverrideManager.wrapDecisions(true, false, false);

        assertEquals(1, cpbarDecisionUIWrappers.size());
        assertEquals(1, cpbarDecisionUIWrappers.get(0).getCpbarDecisionDTO().getDecisions().size());
    }

    @Test
    public void wrapDecisions_IndependentProducts() {
        final CPPricingFilter filter = setUpForWrapDecisionTest();
        pricingOverrideManager.setCpPricingFilter(filter);

        final List<CPBARDecisionUIWrapper> cpbarDecisionUIWrappers = pricingOverrideManager.wrapDecisions(true, false, true);

        assertEquals(1, cpbarDecisionUIWrappers.size());
        assertEquals(1, cpbarDecisionUIWrappers.get(0).getCpbarDecisionDTO().getDecisions().size());
        assertFalse(cpbarDecisionUIWrappers.get(0).getCpbarDecisionDTO().isLongTermBDEWindow());
    }

    @Test
    public void wrapDecisions_IndependentProductsForLongTerm() {
        final CPPricingFilter filter = setUpForWrapDecisionTest();
        pricingOverrideManager.setCpPricingFilter(filter);
        when(configParamsService.getBooleanParameterValue(ENABLE_UPLOAD_ON_OVERRIDE_FOR_EXTENDED_WINDOW)).thenReturn(true);
        when(dateService.getOptimizationWindowEndDateBDE()).thenReturn(DateUtil.addDaysToDate(new Date(), -1));
        final List<CPBARDecisionUIWrapper> cpbarDecisionUIWrappers = pricingOverrideManager.wrapDecisions(true, false, true);

        assertEquals(1, cpbarDecisionUIWrappers.size());
        assertEquals(1, cpbarDecisionUIWrappers.get(0).getCpbarDecisionDTO().getDecisions().size());
        assertTrue(cpbarDecisionUIWrappers.get(0).getCpbarDecisionDTO().isLongTermBDEWindow());
    }

    @Test
    public void verifyAddLrvAndCompetitorRateInfoToBaseRoomType() {
        final AccomClass accomClass = createAccomClass();
        AccomType accomType = createRoomType(1, accomClass);
        final CPPricingFilter filter = createFilter(accomClass, accomType);
        filter.setAdditionalInformationFilterEnum1(AdditionalInformationFilterEnum.COMPETITOR);
        accomType.setAccomClass(accomClass);
        CPDecisionBAROutput output1 = getCpDecisionBAROutput(accomType, filter.getProducts().iterator().next());
        when(service.getBaseAccomTypes()).thenReturn(Collections.singletonList(accomType));
        when(service.getCPDecisionsBetweenDatesForProductAndRoomTypes(filter.getProducts(), filter.getStartDate(), filter.getEndDate(), List.of(accomType)))
                .thenReturn(Collections.singletonList(output1));
        pricingOverrideManager.setBaseRoomTypeList(Collections.singletonList(accomType));
        filter.setSelectedRoomClass(createAllAccomClass());
        filter.setInventoryGroupDto(new InventoryGroupDto(-1, "Property", true));
        pricingOverrideManager.setCpPricingFilter(filter);

        pricingOverrideManager.wrapDecisions(true, false, false);

        verify(service).addCompetitorRateInfoToDecisions(Collections.singletonList(output1), filter.getStartDate().toDate()
                , filter.getEndDate().toDate(), new HashSet<>(List.of(1)));

        verify(service).addLRVInfoToDecisions(Collections.singletonList(output1), new ArrayList<>(), new HashSet<>(List.of(1)));
    }

    @Test
    public void verifyAddLrvAndCompetitorRateInfoToBaseRoomType_IndependentProducts() {
        final AccomClass accomClass = createAccomClass();
        AccomType accomType = createRoomType(1, accomClass);
        final CPPricingFilter filter = createFilter(accomClass, accomType);
        filter.setAdditionalInformationFilterEnum1(AdditionalInformationFilterEnum.COMPETITOR);
        accomType.setAccomClass(accomClass);
        CPDecisionBAROutput output1 = getCpDecisionBAROutput(accomType, filter.getProducts().iterator().next());
        when(service.getBaseAccomTypes()).thenReturn(Collections.singletonList(accomType));
        when(configParamsService.getBooleanParameterValue(ENABLE_UPLOAD_ON_OVERRIDE_FOR_EXTENDED_WINDOW)).thenReturn(true);
        when(dateService.getOptimizationWindowEndDateBDE()).thenReturn(new Date());
        when(service.getCPDecisionsBetweenDatesForProductAndRoomTypes(filter.getProducts(), filter.getStartDate(), filter.getEndDate(), List.of(accomType)))
                .thenReturn(Collections.singletonList(output1));
        pricingOverrideManager.setBaseRoomTypeList(Collections.singletonList(accomType));
        filter.setSelectedRoomClass(createAllAccomClass());
        filter.setInventoryGroupDto(new InventoryGroupDto(-1, "Property", true));
        pricingOverrideManager.setCpPricingFilter(filter);

        pricingOverrideManager.wrapDecisions(true, false, true);

        verify(service).addCompetitorRateInfoToDecisions(Collections.singletonList(output1), filter.getStartDate().toDate()
                , filter.getEndDate().toDate(), new HashSet<>(List.of(1)), new HashSet<>(List.of(1)));
        verify(dateService).getOptimizationWindowEndDateBDE();

        verify(service).addLRVInfoToDecisions(Collections.singletonList(output1), new ArrayList<>(), new HashSet<>(List.of(1)));
    }

    @Test
    public void isCompetitorRateSelectedInFilter() {
        CPPricingFilter cpPricingFilter = new CPPricingFilter();
        pricingOverrideManager.setCpPricingFilter(cpPricingFilter);

        assertFalse(pricingOverrideManager.isCompetitorRateSelectedInFilter());

        cpPricingFilter.setAdditionalInformationFilterEnum1(AdditionalInformationFilterEnum.AVAILABLE_CAPACITY);
        cpPricingFilter.setAdditionalInformationFilterEnum2(AdditionalInformationFilterEnum.AUTHORIZED_CAPACITY);
        cpPricingFilter.setAdditionalInformationFilterEnum3(AdditionalInformationFilterEnum.EFFECTIVE_CAPACITY);
        cpPricingFilter.setAdditionalInformationFilterEnum4(AdditionalInformationFilterEnum.ROOM_ONLY_PRICE);
        assertFalse(pricingOverrideManager.isCompetitorRateSelectedInFilter());

        cpPricingFilter.setAdditionalInformationFilterEnum1(AdditionalInformationFilterEnum.COMPETITOR);
        assertTrue(pricingOverrideManager.isCompetitorRateSelectedInFilter());

        cpPricingFilter.setAdditionalInformationFilterEnum1(AdditionalInformationFilterEnum.AVAILABLE_CAPACITY);
        cpPricingFilter.setAdditionalInformationFilterEnum2(AdditionalInformationFilterEnum.COMPETITOR);
        assertTrue(pricingOverrideManager.isCompetitorRateSelectedInFilter());

        cpPricingFilter.setAdditionalInformationFilterEnum2(AdditionalInformationFilterEnum.AUTHORIZED_CAPACITY);
        cpPricingFilter.setAdditionalInformationFilterEnum3(AdditionalInformationFilterEnum.COMPETITOR);
        assertTrue(pricingOverrideManager.isCompetitorRateSelectedInFilter());

        cpPricingFilter.setAdditionalInformationFilterEnum3(AdditionalInformationFilterEnum.EFFECTIVE_CAPACITY);
        cpPricingFilter.setAdditionalInformationFilterEnum4(AdditionalInformationFilterEnum.COMPETITOR);
        assertTrue(pricingOverrideManager.isCompetitorRateSelectedInFilter());
    }

    @Test
    public void verifyCPBARDecisionDTOCreationWithBusinessAnalysisDailyDataDTO() {
        final CPPricingFilter filter = setUpForWrapDecisionTest();
        setUpBusinessAnalysisDailyDataDTO(filter);
        pricingOverrideManager.setCpPricingFilter(filter);

        final List<CPBARDecisionUIWrapper> cpBarDecisionUIWrappers = pricingOverrideManager.wrapDecisions(true, false, false);

        assertEquals(50, cpBarDecisionUIWrappers.get(0).getCpbarDecisionDTO().getEffectiveCapacity().intValue());
        verify(businessAnalysisDashboardService).getBusinessAnalysisDailyDataDtos(filter.getStartDate().toDate(), filter.getEndDate().toDate());
    }

    @Test
    public void verifyCPBARDecisionDTOCreationWithBusinessAnalysisDailyDataDTO_IndependentProducts() {
        final CPPricingFilter filter = setUpForWrapDecisionTest();
        setUpBusinessAnalysisDailyDataDTO(filter);
        pricingOverrideManager.setCpPricingFilter(filter);

        final List<CPBARDecisionUIWrapper> cpBarDecisionUIWrappers = pricingOverrideManager.wrapDecisions(true, false, true);

        assertEquals(50, cpBarDecisionUIWrappers.get(0).getCpbarDecisionDTO().getEffectiveCapacity().intValue());
        verify(businessAnalysisDashboardService).getBusinessAnalysisDailyDataDtos(filter.getStartDate().toDate(), filter.getEndDate().toDate());
    }

    @Test
    public void verifyCPBARDecisionDTOCreationWithBusinessAnalysisDailyIndicatorDTO() {
        final CPPricingFilter filter = setUpForWrapDecisionTest();
        setUpBusinessAnalysisDailyIndicatorDto(filter);
        pricingOverrideManager.setCpPricingFilter(filter);

        final List<CPBARDecisionUIWrapper> cpbarDecisionUIWrappers = pricingOverrideManager.wrapDecisions(true, false, false);

        final List<BusinessAnalysisSpecialEventDto> specialEvents = cpbarDecisionUIWrappers.get(0).getCpbarDecisionDTO().getSpecialEvents();
        assertEquals(1, specialEvents.size());
        assertEquals("Special", specialEvents.get(0).getEventName());
    }

    @Test
    public void verifyCPBARDecisionDTOCreationWithBusinessAnalysisDailyIndicatorDTO_IndependentProducts() {
        final CPPricingFilter filter = setUpForWrapDecisionTest();
        setUpBusinessAnalysisDailyIndicatorDto(filter);
        pricingOverrideManager.setCpPricingFilter(filter);

        final List<CPBARDecisionUIWrapper> cpbarDecisionUIWrappers = pricingOverrideManager.wrapDecisions(true, false, true);

        final List<BusinessAnalysisSpecialEventDto> specialEvents = cpbarDecisionUIWrappers.get(0).getCpbarDecisionDTO().getSpecialEvents();
        assertEquals(1, specialEvents.size());
        assertEquals("Special", specialEvents.get(0).getEventName());
    }

    @Test
    public void filterByDayOfWeek() {
        CPDecisionBAROutput cpDecisionBAROutput1 = new CPDecisionBAROutput();
        cpDecisionBAROutput1.setArrivalDate(new LocalDate(2018, 8, 23));
        CPDecisionBAROutput cpDecisionBAROutput2 = new CPDecisionBAROutput();
        cpDecisionBAROutput2.setArrivalDate(new LocalDate(2018, 8, 24));
        CPDecisionBAROutput cpDecisionBAROutput3 = new CPDecisionBAROutput();
        cpDecisionBAROutput3.setArrivalDate(new LocalDate(2018, 8, 25));
        List<CPDecisionBAROutput> cpDecisionBAROutputList = new ArrayList<>(Arrays.asList(cpDecisionBAROutput1, cpDecisionBAROutput2, cpDecisionBAROutput3));

        CPPricingFilter cpPricingFilter1 = new CPPricingFilter();
        cpPricingFilter1.setSelectedDaysOfWeek(new HashSet<>(Arrays.asList(com.ideas.tetris.platform.common.entity.DayOfWeek.FRIDAY, com.ideas.tetris.platform.common.entity.DayOfWeek.SUNDAY)));
        pricingOverrideManager.setCpPricingFilter(cpPricingFilter1);

        List<CPDecisionBAROutput> results1 = pricingOverrideManager.filterByDayOfWeek(cpDecisionBAROutputList);
        assertEquals(1, results1.size());
        assertEquals(cpDecisionBAROutput2, results1.get(0));

        CPPricingFilter cpPricingFilter2 = new CPPricingFilter();
        cpPricingFilter2.setSelectedDaysOfWeek(new HashSet<>(Arrays.asList(com.ideas.tetris.platform.common.entity.DayOfWeek.MONDAY, com.ideas.tetris.platform.common.entity.DayOfWeek.TUESDAY)));
        pricingOverrideManager.setCpPricingFilter(cpPricingFilter2);

        List<CPDecisionBAROutput> results2 = pricingOverrideManager.filterByDayOfWeek(cpDecisionBAROutputList);
        assertEquals(0, results2.size());
    }

    @Test
    public void filterByOverrides_InlineEdit() {
        CPDecisionBAROutput specificOverride = new CPDecisionBAROutput();
        specificOverride.setSpecificOverride(new BigDecimal(1));

        CPDecisionBAROutput floorOverride = new CPDecisionBAROutput();
        floorOverride.setFloorOverride(new BigDecimal(2));

        CPDecisionBAROutput ceilOverride = new CPDecisionBAROutput();
        ceilOverride.setCeilingOverride(new BigDecimal(3));

        List<CPDecisionBAROutput> overrides = new ArrayList<>(Arrays.asList(specificOverride, floorOverride, ceilOverride));

        CPPricingFilter cpPricingFilter1 = new CPPricingFilter();
        cpPricingFilter1.setSelectedOverrides(new HashSet<>(List.of(CPOverrideType.SPECIFIC)));
        cpPricingFilter1.setProducts(createDefaultProduct());
        pricingOverrideManager.setCpPricingFilter(cpPricingFilter1);

        List<CPDecisionBAROutput> results = pricingOverrideManager.filterByOverrides(overrides, new HashSet<>(), true);
        assertEquals(3, results.size());
    }

    @Test
    public void filterByOverrides_SingleOverrides() {
        CPDecisionBAROutput cpDecisionBAROutput1 = new CPDecisionBAROutput();
        cpDecisionBAROutput1.setSpecificOverride(new BigDecimal(1));
        cpDecisionBAROutput1.setOverrideType(DecisionOverrideType.USER);
        CPDecisionBAROutput cpDecisionBAROutput2 = new CPDecisionBAROutput();
        cpDecisionBAROutput2.setCeilingOverride(new BigDecimal(2));
        cpDecisionBAROutput2.setOverrideType(DecisionOverrideType.CEIL);
        CPDecisionBAROutput cpDecisionBAROutput3 = new CPDecisionBAROutput();
        cpDecisionBAROutput3.setFloorOverride(new BigDecimal(3));
        cpDecisionBAROutput3.setOverrideType(DecisionOverrideType.FLOOR);
        CPDecisionBAROutput cpDecisionBAROutput4 = new CPDecisionBAROutput();
        List<CPDecisionBAROutput> cpDecisionBAROutputList = new ArrayList<>(Arrays.asList(cpDecisionBAROutput1, cpDecisionBAROutput2, cpDecisionBAROutput3, cpDecisionBAROutput4));

        CPPricingFilter cpPricingFilter1 = new CPPricingFilter();
        cpPricingFilter1.setSelectedOverrides(new HashSet<>(List.of(CPOverrideType.SPECIFIC)));
        cpPricingFilter1.setProducts(createDefaultProduct());
        pricingOverrideManager.setCpPricingFilter(cpPricingFilter1);

        List<CPDecisionBAROutput> results1 = pricingOverrideManager.filterByOverrides(cpDecisionBAROutputList, new HashSet<>(), false);
        assertEquals(1, results1.size());
        assertEquals(cpDecisionBAROutput1, results1.get(0));

        CPPricingFilter cpPricingFilter2 = new CPPricingFilter();
        cpPricingFilter2.setSelectedOverrides(new HashSet<>(List.of(CPOverrideType.CEILING)));
        cpPricingFilter2.setProducts(createDefaultProduct());
        pricingOverrideManager.setCpPricingFilter(cpPricingFilter2);

        List<CPDecisionBAROutput> results2 = pricingOverrideManager.filterByOverrides(cpDecisionBAROutputList, new HashSet<>(), false);
        assertEquals(1, results2.size());
        assertEquals(cpDecisionBAROutput2, results2.get(0));

        CPPricingFilter cpPricingFilter3 = new CPPricingFilter();
        cpPricingFilter3.setSelectedOverrides(new HashSet<>(List.of(CPOverrideType.FLOOR)));
        cpPricingFilter3.setProducts(createDefaultProduct());
        pricingOverrideManager.setCpPricingFilter(cpPricingFilter3);

        List<CPDecisionBAROutput> results3 = pricingOverrideManager.filterByOverrides(cpDecisionBAROutputList, new HashSet<>(), false);
        assertEquals(1, results3.size());
        assertEquals(cpDecisionBAROutput3, results3.get(0));

        CPPricingFilter cpPricingFilter4 = new CPPricingFilter();
        cpPricingFilter4.setSelectedOverrides(new HashSet<>(Arrays.asList(CPOverrideType.FLOOR, CPOverrideType.CEILING, CPOverrideType.SPECIFIC)));
        cpPricingFilter4.setProducts(createDefaultProduct());
        pricingOverrideManager.setCpPricingFilter(cpPricingFilter4);

        List<CPDecisionBAROutput> results4 = pricingOverrideManager.filterByOverrides(cpDecisionBAROutputList, new HashSet<>(), false);
        assertEquals(3, results4.size());
        assertFalse(results3.contains(cpDecisionBAROutput4));
    }

    @Test
    public void filterByOverrides_SingleAgileRateOverrides() {
        LocalDate localDate = new LocalDate();
        AccomClass accomClass1 = new AccomClass();
        accomClass1.setId(1);
        AccomClass accomClass2 = new AccomClass();
        accomClass2.setId(2);
        AccomType accomType1 = new AccomType();
        accomType1.setId(1);
        accomType1.setAccomClass(accomClass1);
        AccomType accomType2 = new AccomType();
        accomType2.setId(2);
        accomType2.setAccomClass(accomClass1);
        AccomType accomType3 = new AccomType();
        accomType3.setId(3);
        accomType3.setAccomClass(accomClass2);

        CPDecisionBAROutput cpDecisionBAROutput1 = new CPDecisionBAROutput();
        cpDecisionBAROutput1.setAccomType(accomType1);
        cpDecisionBAROutput1.setArrivalDate(localDate);
        CPDecisionBAROutput cpDecisionBAROutput2 = new CPDecisionBAROutput();
        cpDecisionBAROutput2.setAccomType(accomType2);
        cpDecisionBAROutput2.setArrivalDate(localDate);
        CPDecisionBAROutput cpDecisionBAROutput3 = new CPDecisionBAROutput();
        cpDecisionBAROutput3.setAccomType(accomType3);
        cpDecisionBAROutput3.setArrivalDate(localDate);
        List<CPDecisionBAROutput> cpDecisionBAROutputList = new ArrayList<>(Arrays.asList(cpDecisionBAROutput1, cpDecisionBAROutput2, cpDecisionBAROutput3));

        ProductRateOffsetOverride productRateOffsetOverride = new ProductRateOffsetOverride();
        productRateOffsetOverride.setAccomClass(accomClass1);
        productRateOffsetOverride.setOccupancyDate(localDate);
        Set<Product> products = createAgileRateProduct();
        cpDecisionBAROutput1.setProduct(products.iterator().next());
        cpDecisionBAROutput2.setProduct(products.iterator().next());
        cpDecisionBAROutput3.setProduct(products.iterator().next());
        productRateOffsetOverride.setProduct(products.iterator().next());

        Set<ProductRateOffsetOverride> productRateOffsetOverrides = new HashSet<>();
        productRateOffsetOverrides.add(productRateOffsetOverride);

        CPPricingFilter cpPricingFilter1 = new CPPricingFilter();
        cpPricingFilter1.setSelectedOverrides(new HashSet<>(List.of(CPOverrideType.SPECIFIC)));
        cpPricingFilter1.setProducts(products);
        pricingOverrideManager.setCpPricingFilter(cpPricingFilter1);

        List<CPDecisionBAROutput> results1 = pricingOverrideManager.filterByOverrides(cpDecisionBAROutputList, productRateOffsetOverrides, false);
        assertEquals(2, results1.size());
        assertEquals(cpDecisionBAROutput1, results1.get(0));
        assertEquals(cpDecisionBAROutput2, results1.get(1));

        CPPricingFilter cpPricingFilter2 = new CPPricingFilter();
        cpPricingFilter2.setSelectedOverrides(new HashSet<>(List.of(CPOverrideType.CEILING)));
        cpPricingFilter2.setProducts(createAgileRateProduct());
        pricingOverrideManager.setCpPricingFilter(cpPricingFilter2);

        List<CPDecisionBAROutput> results2 = pricingOverrideManager.filterByOverrides(cpDecisionBAROutputList, productRateOffsetOverrides, false);
        assertEquals(0, results2.size());

        CPPricingFilter cpPricingFilter3 = new CPPricingFilter();
        cpPricingFilter3.setSelectedOverrides(new HashSet<>(List.of(CPOverrideType.FLOOR)));
        cpPricingFilter3.setProducts(createAgileRateProduct());
        pricingOverrideManager.setCpPricingFilter(cpPricingFilter3);

        List<CPDecisionBAROutput> results3 = pricingOverrideManager.filterByOverrides(cpDecisionBAROutputList, productRateOffsetOverrides, false);
        assertEquals(0, results3.size());

        CPPricingFilter cpPricingFilter4 = new CPPricingFilter();
        cpPricingFilter4.setSelectedOverrides(new HashSet<>(Arrays.asList(CPOverrideType.FLOOR, CPOverrideType.CEILING, CPOverrideType.SPECIFIC)));
        cpPricingFilter4.setProducts(createAgileRateProduct());
        pricingOverrideManager.setCpPricingFilter(cpPricingFilter4);

        List<CPDecisionBAROutput> results4 = pricingOverrideManager.filterByOverrides(cpDecisionBAROutputList, productRateOffsetOverrides, false);
        assertEquals(2, results4.size());
        assertEquals(cpDecisionBAROutput1, results4.get(0));
        assertEquals(cpDecisionBAROutput2, results4.get(1));
    }

    @Test
    public void hasProductRateOffsetOverride() {
        Product product = new Product();
        LocalDate localDate = new LocalDate();
        AccomClass accomClass1 = new AccomClass();
        accomClass1.setId(1);
        AccomClass accomClass2 = new AccomClass();
        accomClass2.setId(2);
        AccomType accomType1 = new AccomType();
        accomType1.setId(1);
        accomType1.setAccomClass(accomClass1);
        AccomType accomType2 = new AccomType();
        accomType2.setId(2);
        accomType2.setAccomClass(accomClass1);
        AccomType accomType3 = new AccomType();
        accomType3.setId(3);
        accomType3.setAccomClass(accomClass2);

        CPDecisionBAROutput cpDecisionBAROutput1 = new CPDecisionBAROutput();
        cpDecisionBAROutput1.setAccomType(accomType1);
        cpDecisionBAROutput1.setArrivalDate(localDate);
        cpDecisionBAROutput1.setProduct(product);
        CPDecisionBAROutput cpDecisionBAROutput2 = new CPDecisionBAROutput();
        cpDecisionBAROutput2.setAccomType(accomType2);
        cpDecisionBAROutput2.setArrivalDate(localDate);
        cpDecisionBAROutput2.setProduct(product);
        CPDecisionBAROutput cpDecisionBAROutput3 = new CPDecisionBAROutput();
        cpDecisionBAROutput3.setAccomType(accomType3);
        cpDecisionBAROutput3.setArrivalDate(localDate);
        cpDecisionBAROutput3.setProduct(product);
        List<CPDecisionBAROutput> cpDecisionBAROutputList = new ArrayList<>(Arrays.asList(cpDecisionBAROutput1, cpDecisionBAROutput2, cpDecisionBAROutput3));

        ProductRateOffsetOverride productRateOffsetOverride = new ProductRateOffsetOverride();
        productRateOffsetOverride.setProduct(product);
        productRateOffsetOverride.setAccomClass(accomClass1);
        productRateOffsetOverride.setOccupancyDate(localDate);

        Set<ProductRateOffsetOverride> productRateOffsetOverrides = new HashSet<>();
        productRateOffsetOverrides.add(productRateOffsetOverride);

        assertFalse(pricingOverrideManager.hasProductRateOffsetOverride(cpDecisionBAROutput1, new HashSet<>()));
        assertFalse(pricingOverrideManager.hasProductRateOffsetOverride(cpDecisionBAROutput3, productRateOffsetOverrides));
        assertTrue(pricingOverrideManager.hasProductRateOffsetOverride(cpDecisionBAROutput1, productRateOffsetOverrides));
    }

    @Test
    public void filterByOverrides_MultipleOverrides() {
        CPDecisionBAROutput cpDecisionBAROutput1 = new CPDecisionBAROutput();
        cpDecisionBAROutput1.setSpecificOverride(new BigDecimal(1));
        cpDecisionBAROutput1.setCeilingOverride(new BigDecimal(1));
        cpDecisionBAROutput1.setFloorOverride(new BigDecimal(1));
        cpDecisionBAROutput1.setOverrideType(DecisionOverrideType.FLOOR);
        Product product = new Product();
        product.setCode(Product.AGILE_RATES_PRODUCT_CODE);
        product.setId(5);
        cpDecisionBAROutput1.setProduct(product);
        CPDecisionBAROutput cpDecisionBAROutput2 = new CPDecisionBAROutput();
        cpDecisionBAROutput2.setSpecificOverride(new BigDecimal(2));
        cpDecisionBAROutput2.setCeilingOverride(new BigDecimal(2));
        cpDecisionBAROutput2.setFloorOverride(new BigDecimal(2));
        cpDecisionBAROutput2.setOverrideType(DecisionOverrideType.FLOOR);
        cpDecisionBAROutput2.setProduct(product);
        CPDecisionBAROutput cpDecisionBAROutput3 = new CPDecisionBAROutput();
        cpDecisionBAROutput3.setSpecificOverride(new BigDecimal(3));
        cpDecisionBAROutput3.setCeilingOverride(new BigDecimal(3));
        cpDecisionBAROutput3.setFloorOverride(new BigDecimal(3));
        cpDecisionBAROutput3.setOverrideType(DecisionOverrideType.FLOOR);
        cpDecisionBAROutput3.setProduct(product);
        CPDecisionBAROutput cpDecisionBAROutput4 = new CPDecisionBAROutput();
        cpDecisionBAROutput4.setProduct(product);
        List<CPDecisionBAROutput> cpDecisionBAROutputList = new ArrayList<>(Arrays.asList(cpDecisionBAROutput1, cpDecisionBAROutput2, cpDecisionBAROutput3, cpDecisionBAROutput4));

        CPPricingFilter cpPricingFilter1 = new CPPricingFilter();
        cpPricingFilter1.setSelectedOverrides(new HashSet<>(List.of(CPOverrideType.SPECIFIC)));
        cpPricingFilter1.setProducts(createDefaultProduct());
        pricingOverrideManager.setCpPricingFilter(cpPricingFilter1);

        List<CPDecisionBAROutput> results1 = pricingOverrideManager.filterByOverrides(cpDecisionBAROutputList, new HashSet<>(), false);
        assertEquals(3, results1.size());
        assertFalse(results1.contains(cpDecisionBAROutput4));

        CPPricingFilter cpPricingFilter2 = new CPPricingFilter();
        cpPricingFilter2.setSelectedOverrides(new HashSet<>(List.of(CPOverrideType.CEILING)));
        cpPricingFilter2.setProducts(createDefaultProduct());
        pricingOverrideManager.setCpPricingFilter(cpPricingFilter2);

        List<CPDecisionBAROutput> results2 = pricingOverrideManager.filterByOverrides(cpDecisionBAROutputList, new HashSet<>(), false);
        assertEquals(3, results2.size());
        assertFalse(results2.contains(cpDecisionBAROutput4));

        CPPricingFilter cpPricingFilter3 = new CPPricingFilter();
        cpPricingFilter3.setSelectedOverrides(new HashSet<>(List.of(CPOverrideType.FLOOR)));
        cpPricingFilter3.setProducts(createDefaultProduct());
        pricingOverrideManager.setCpPricingFilter(cpPricingFilter3);

        List<CPDecisionBAROutput> results3 = pricingOverrideManager.filterByOverrides(cpDecisionBAROutputList, new HashSet<>(), false);
        assertEquals(3, results3.size());
        assertFalse(results3.contains(cpDecisionBAROutput4));

        CPPricingFilter cpPricingFilter4 = new CPPricingFilter();
        cpPricingFilter4.setSelectedOverrides(new HashSet<>(Arrays.asList(CPOverrideType.FLOOR, CPOverrideType.CEILING, CPOverrideType.SPECIFIC)));
        cpPricingFilter4.setProducts(createDefaultProduct());
        pricingOverrideManager.setCpPricingFilter(cpPricingFilter4);

        List<CPDecisionBAROutput> results4 = pricingOverrideManager.filterByOverrides(cpDecisionBAROutputList, new HashSet<>(), false);
        assertEquals(3, results4.size());
        assertFalse(results3.contains(cpDecisionBAROutput4));
    }

    @Test
    public void filterByDateWith_OccupancyForecastGreaterThan() {
        LocalDate localDate = new LocalDate().plusDays(1);
        CPDecisionBAROutput cpDecisionBAROutput1 = new CPDecisionBAROutput();
        cpDecisionBAROutput1.setArrivalDate(new LocalDate());
        CPDecisionBAROutput cpDecisionBAROutput2 = new CPDecisionBAROutput();
        cpDecisionBAROutput2.setArrivalDate(localDate);
        CPDecisionBAROutput cpDecisionBAROutput3 = new CPDecisionBAROutput();
        cpDecisionBAROutput3.setArrivalDate(new LocalDate().plusDays(2));
        CPDecisionBAROutput cpDecisionBAROutput4 = new CPDecisionBAROutput();
        cpDecisionBAROutput4.setArrivalDate(new LocalDate().plusDays(2));
        List<CPDecisionBAROutput> cpDecisionBAROutputList = new ArrayList<>(Arrays.asList(cpDecisionBAROutput1, cpDecisionBAROutput2, cpDecisionBAROutput3, cpDecisionBAROutput4));

        CPPricingFilter cpPricingFilter = new CPPricingFilter();
        cpPricingFilter.setDateWithFilterEnum(DateWithFilterEnum.OCCUPANCY_FORECAST_GREATER_THAN);
        cpPricingFilter.setDateWithInteger(10);
        pricingOverrideManager.setCpPricingFilter(cpPricingFilter);

        BusinessAnalysisDailyDataDto dailyDataDto1 = new BusinessAnalysisDailyDataDto();
        dailyDataDto1.setDate(DateParameter.fromDate(new LocalDate().toDate()));
        dailyDataDto1.setOccupancyForecastPerc(new BigDecimal(9));
        BusinessAnalysisDailyDataDto dailyDataDto2 = new BusinessAnalysisDailyDataDto();
        dailyDataDto2.setDate(DateParameter.fromDate(localDate.toDate()));
        dailyDataDto2.setOccupancyForecastPerc(new BigDecimal(10));
        BusinessAnalysisDailyDataDto dailyDataDto3 = new BusinessAnalysisDailyDataDto();
        dailyDataDto3.setDate(DateParameter.fromDate(new LocalDate().plusDays(2).toDate()));
        dailyDataDto3.setOccupancyForecastPerc(new BigDecimal(11));
        pricingOverrideManager.setBusinessAnalysisDailyDataDtos(Arrays.asList(dailyDataDto1, dailyDataDto2, dailyDataDto3));

        List<CPDecisionBAROutput> results = pricingOverrideManager.filterByDateWith(cpDecisionBAROutputList);
        assertEquals(2, results.size());
        assertTrue(results.contains(cpDecisionBAROutput3));
        assertTrue(results.contains(cpDecisionBAROutput4));
    }

    @Test
    public void filterByDateWith_OccupancyForecastLessThan() {
        LocalDate localDate = new LocalDate().plusDays(1);
        CPDecisionBAROutput cpDecisionBAROutput1 = new CPDecisionBAROutput();
        cpDecisionBAROutput1.setArrivalDate(new LocalDate());
        CPDecisionBAROutput cpDecisionBAROutput2 = new CPDecisionBAROutput();
        cpDecisionBAROutput2.setArrivalDate(localDate);
        CPDecisionBAROutput cpDecisionBAROutput3 = new CPDecisionBAROutput();
        cpDecisionBAROutput3.setArrivalDate(new LocalDate().plusDays(2));
        List<CPDecisionBAROutput> cpDecisionBAROutputList = new ArrayList<>(Arrays.asList(cpDecisionBAROutput1, cpDecisionBAROutput2, cpDecisionBAROutput3));

        CPPricingFilter cpPricingFilter = new CPPricingFilter();
        cpPricingFilter.setDateWithFilterEnum(DateWithFilterEnum.OCCUPANCY_FORECAST_LESS_THAN);
        cpPricingFilter.setDateWithInteger(10);
        pricingOverrideManager.setCpPricingFilter(cpPricingFilter);

        BusinessAnalysisDailyDataDto dailyDataDto1 = new BusinessAnalysisDailyDataDto();
        dailyDataDto1.setDate(DateParameter.fromDate(new LocalDate().toDate()));
        dailyDataDto1.setOccupancyForecastPerc(new BigDecimal(9));
        BusinessAnalysisDailyDataDto dailyDataDto2 = new BusinessAnalysisDailyDataDto();
        dailyDataDto2.setDate(DateParameter.fromDate(localDate.toDate()));
        dailyDataDto2.setOccupancyForecastPerc(new BigDecimal(10));
        BusinessAnalysisDailyDataDto dailyDataDto3 = new BusinessAnalysisDailyDataDto();
        dailyDataDto3.setDate(DateParameter.fromDate(new LocalDate().plusDays(2).toDate()));
        dailyDataDto3.setOccupancyForecastPerc(new BigDecimal(11));
        pricingOverrideManager.setBusinessAnalysisDailyDataDtos(Arrays.asList(dailyDataDto1, dailyDataDto2, dailyDataDto3));

        List<CPDecisionBAROutput> results = pricingOverrideManager.filterByDateWith(cpDecisionBAROutputList);
        assertEquals(1, results.size());
        assertTrue(results.contains(cpDecisionBAROutput1));
    }

    @Test
    public void filterByDateWith_FinalPriceEqualTo() {
        CPDecisionBAROutput cpDecisionBAROutput1 = new CPDecisionBAROutput();
        cpDecisionBAROutput1.setFinalBAR(new BigDecimal(1));
        CPDecisionBAROutput cpDecisionBAROutput2 = new CPDecisionBAROutput();
        cpDecisionBAROutput2.setFinalBAR(new BigDecimal(2));
        CPDecisionBAROutput cpDecisionBAROutput3 = new CPDecisionBAROutput();
        cpDecisionBAROutput3.setFinalBAR(new BigDecimal(3));
        List<CPDecisionBAROutput> cpDecisionBAROutputList = new ArrayList<>(Arrays.asList(cpDecisionBAROutput1, cpDecisionBAROutput2, cpDecisionBAROutput3));

        CPPricingFilter cpPricingFilter = new CPPricingFilter();
        cpPricingFilter.setDateWithFilterEnum(DateWithFilterEnum.FINAL_PRICE_EQUAL_TO);
        cpPricingFilter.setDateWithBigDecimal(new BigDecimal(2));
        pricingOverrideManager.setCpPricingFilter(cpPricingFilter);

        List<CPDecisionBAROutput> results = pricingOverrideManager.filterByDateWith(cpDecisionBAROutputList);
        assertEquals(1, results.size());
        assertEquals(cpDecisionBAROutput2, results.get(0));
    }

    @Test
    public void filterByDateWith_FinalPriceGreaterThan() {
        CPDecisionBAROutput cpDecisionBAROutput1 = new CPDecisionBAROutput();
        cpDecisionBAROutput1.setFinalBAR(new BigDecimal(1));
        CPDecisionBAROutput cpDecisionBAROutput2 = new CPDecisionBAROutput();
        cpDecisionBAROutput2.setFinalBAR(new BigDecimal(2));
        CPDecisionBAROutput cpDecisionBAROutput3 = new CPDecisionBAROutput();
        cpDecisionBAROutput3.setFinalBAR(new BigDecimal(3));
        List<CPDecisionBAROutput> cpDecisionBAROutputList = new ArrayList<>(Arrays.asList(cpDecisionBAROutput1, cpDecisionBAROutput2, cpDecisionBAROutput3));

        CPPricingFilter cpPricingFilter = new CPPricingFilter();
        cpPricingFilter.setDateWithFilterEnum(DateWithFilterEnum.FINAL_PRICE_GREATER_THAN);
        cpPricingFilter.setDateWithBigDecimal(new BigDecimal(2));
        pricingOverrideManager.setCpPricingFilter(cpPricingFilter);

        List<CPDecisionBAROutput> results = pricingOverrideManager.filterByDateWith(cpDecisionBAROutputList);
        assertEquals(1, results.size());
        assertEquals(cpDecisionBAROutput3, results.get(0));
    }

    @Test
    public void filterByDateWith_FinalPriceLessThan() {
        CPDecisionBAROutput cpDecisionBAROutput1 = new CPDecisionBAROutput();
        cpDecisionBAROutput1.setFinalBAR(new BigDecimal(1));
        CPDecisionBAROutput cpDecisionBAROutput2 = new CPDecisionBAROutput();
        cpDecisionBAROutput2.setFinalBAR(new BigDecimal(2));
        CPDecisionBAROutput cpDecisionBAROutput3 = new CPDecisionBAROutput();
        cpDecisionBAROutput3.setFinalBAR(new BigDecimal(3));
        List<CPDecisionBAROutput> cpDecisionBAROutputList = new ArrayList<>(Arrays.asList(cpDecisionBAROutput1, cpDecisionBAROutput2, cpDecisionBAROutput3));

        CPPricingFilter cpPricingFilter = new CPPricingFilter();
        cpPricingFilter.setDateWithFilterEnum(DateWithFilterEnum.FINAL_PRICE_LESS_THAN);
        cpPricingFilter.setDateWithBigDecimal(new BigDecimal(2));
        pricingOverrideManager.setCpPricingFilter(cpPricingFilter);

        List<CPDecisionBAROutput> results = pricingOverrideManager.filterByDateWith(cpDecisionBAROutputList);
        assertEquals(1, results.size());
        assertEquals(cpDecisionBAROutput1, results.get(0));
    }

    @Test
    public void filterDecisions() {
        List<CPDecisionBAROutput> cpDecisionBAROutputList = getCPDecisionBAROutputList();
        CPPricingFilter cpPricingFilter = new CPPricingFilter();
        cpPricingFilter.setDateWithFilterEnum(DateWithFilterEnum.FINAL_PRICE_EQUAL_TO);
        cpPricingFilter.setDateWithBigDecimal(new BigDecimal(1));
        cpPricingFilter.setSelectedDaysOfWeek(new HashSet<>(List.of(DayOfWeek.THURSDAY)));
        cpPricingFilter.setSelectedOverrides(new HashSet<>(List.of(CPOverrideType.FLOOR)));
        cpPricingFilter.setSelectedRestrictions(new HashSet<>(List.of(DecisionReasonType.LRV_GT_BAR)));
        cpPricingFilter.setProducts(createDefaultProduct());
        pricingOverrideManager.setCpPricingFilter(cpPricingFilter);

        List<CPDecisionBAROutput> results = pricingOverrideManager.filterDecisions(cpDecisionBAROutputList,
                new HashSet<>(),
                false);
        assertEquals(1, results.size());
        assertEquals(cpDecisionBAROutputList.get(0), results.get(0));
    }

    @Test
    public void filterGroupFloorDecisions() {
        PricingOverrideManager spy = Mockito.spy(pricingOverrideManager);
//        doReturn(true).when(spy).isGroupFloorEnabled();
        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.IS_GROUP_FLOOR_OVERRIDE_ENABLED))
                .thenReturn(true);
        List<CPDecisionBAROutput> cpDecisionBAROutputList = getCPDecisionBAROutputList();

        CPDecisionBAROutput cpDecisionBAROutput6 = new CPDecisionBAROutput();
        cpDecisionBAROutput6.setFinalBAR(new BigDecimal(100));
        cpDecisionBAROutput6.setArrivalDate(new LocalDate(2018, 8, 25));
        cpDecisionBAROutput6.setFloorOverride(new BigDecimal(100));
        cpDecisionBAROutput6.setOverrideType(DecisionOverrideType.GPFLOOR);
        cpDecisionBAROutput6.setDecisionReasonTypeId(7);

        cpDecisionBAROutputList.add(cpDecisionBAROutput6);

        CPPricingFilter cpPricingFilter = new CPPricingFilter();
        cpPricingFilter.setDateWithFilterEnum(DateWithFilterEnum.GROUP_FLOOR_VALUE_EQUAL_TO_FINAL_BAR);
        cpPricingFilter.setDateWithBigDecimal(new BigDecimal(1));
        cpPricingFilter.setSelectedDaysOfWeek(new HashSet<>());
        cpPricingFilter.setSelectedOverrides(new HashSet<>(List.of(CPOverrideType.GPFLOOR)));
        cpPricingFilter.setSelectedRestrictions(new HashSet<>());
        cpPricingFilter.setProducts(createDefaultProduct());
        spy.setCpPricingFilter(cpPricingFilter);

        List<CPDecisionBAROutput> results = spy.filterDecisions(cpDecisionBAROutputList, new HashSet<>(), false);
        assertEquals(1, results.size());
        assertEquals(cpDecisionBAROutputList.get(5), results.get(0));
    }

    @Test
    public void filterGroupFloorAndCeilDecisions() {
        PricingOverrideManager spy = Mockito.spy(pricingOverrideManager);
//        doReturn(true).when(spy).isGroupFloorEnabled();
        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.IS_GROUP_FLOOR_OVERRIDE_ENABLED))
                .thenReturn(true);
        List<CPDecisionBAROutput> cpDecisionBAROutputList = getCPDecisionBAROutputList();

        CPDecisionBAROutput cpDecisionBAROutput6 = new CPDecisionBAROutput();
        cpDecisionBAROutput6.setFinalBAR(new BigDecimal(100.0000));
        cpDecisionBAROutput6.setArrivalDate(new LocalDate(2018, 8, 25));
        cpDecisionBAROutput6.setFloorOverride(new BigDecimal(100));
        cpDecisionBAROutput6.setOverrideType(DecisionOverrideType.GPFLOORANDCEIL);
        cpDecisionBAROutput6.setDecisionReasonTypeId(7);

        cpDecisionBAROutputList.add(cpDecisionBAROutput6);

        CPPricingFilter cpPricingFilter = new CPPricingFilter();
        cpPricingFilter.setDateWithFilterEnum(DateWithFilterEnum.GROUP_FLOOR_VALUE_EQUAL_TO_FINAL_BAR);
        cpPricingFilter.setDateWithBigDecimal(new BigDecimal(1));
        cpPricingFilter.setSelectedDaysOfWeek(new HashSet<>());
        cpPricingFilter.setSelectedOverrides(new HashSet<>(List.of(CPOverrideType.GPFLOOR)));
        cpPricingFilter.setSelectedRestrictions(new HashSet<>());
        cpPricingFilter.setProducts(createDefaultProduct());
        spy.setCpPricingFilter(cpPricingFilter);

        List<CPDecisionBAROutput> results = spy.filterDecisions(cpDecisionBAROutputList, new HashSet<>(), false);
        assertEquals(1, results.size());
        assertEquals(cpDecisionBAROutputList.get(5), results.get(0));
    }

    @Test
    public void shouldFilterDecisionBasedOnInvetoryOverrideForGroupProduct() {
        LocalDate startDate = new LocalDate(2024, 1, 1);
        LocalDate endDate = new LocalDate(2024, 1, 10);

        List<CPDecisionBAROutput> cpDecisionBAROutputList = getCPDecisionBAROutputList();
        CPPricingFilter cpPricingFilter = new CPPricingFilter();
        cpPricingFilter.setDateWithFilterEnum(DateWithFilterEnum.FINAL_PRICE_EQUAL_TO);
        cpPricingFilter.setDateWithBigDecimal(new BigDecimal(1));
        cpPricingFilter.setSelectedDaysOfWeek(new HashSet<>(List.of(DayOfWeek.THURSDAY)));
        cpPricingFilter.setSelectedOverrides(new HashSet<>(List.of(CPOverrideType.GPINVENTORY)));
        cpPricingFilter.setStartDate(startDate);
        cpPricingFilter.setEndDate(endDate);

        LocalDate arrivalDate = new LocalDate(2024, 1, 4);

        Product sg1 = new Product();
        sg1.setId(12);
        sg1.setName("SG1");
        sg1.setCode(Product.GROUP_PRODUCT_CODE);
        Set<Product> products = createDefaultProduct();
        products.add(sg1);

        CPDecisionBAROutput cpDecisionBAROutput6 = new CPDecisionBAROutput();
        cpDecisionBAROutput6.setId(1L);
        cpDecisionBAROutput6.setFinalBAR(new BigDecimal(1));
        cpDecisionBAROutput6.setDecisionReasonTypeId(6);
        cpDecisionBAROutput6.setArrivalDate(arrivalDate);
        cpDecisionBAROutput6.setProduct(sg1);
        cpDecisionBAROutputList.add(cpDecisionBAROutput6);

        cpPricingFilter.setProducts(products);
        pricingOverrideManager.setCpPricingFilter(cpPricingFilter);

        DecisionGPInventoryLimitOverride decisionGPInventoryLimitOverride = new DecisionGPInventoryLimitOverride();
        decisionGPInventoryLimitOverride.setOccupancyDate(arrivalDate.toDate());

        List<DecisionGPInventoryLimitOverride> inventoryLimitOverrides = new ArrayList<>();
        inventoryLimitOverrides.add(decisionGPInventoryLimitOverride);

        when(inventoryLimitDecisionService.getDecisionInventoryLimitOverridesByOccupancyDateRange(startDate.toDate(), endDate.toDate())).thenReturn(inventoryLimitOverrides);


        List<CPDecisionBAROutput> results = pricingOverrideManager.filterDecisions(cpDecisionBAROutputList,
                new HashSet<>(),
                false);
        assertEquals(1, results.size());
        assertEquals(cpDecisionBAROutputList.get(5), results.get(0));
    }

    @Test
    public void shouldFilterDecisionBasedOnInvetoryOverrideForGroupProductWhenOnlyInventoryOverrideAndGroupProductIsSelected() {
        LocalDate startDate = new LocalDate(2024, 1, 1);
        LocalDate endDate = new LocalDate(2024, 1, 10);
        List<CPDecisionBAROutput> cpDecisionBAROutputList = getCPDecisionBAROutputList();
        CPPricingFilter cpPricingFilter = new CPPricingFilter();
        cpPricingFilter.setDateWithFilterEnum(DateWithFilterEnum.FINAL_PRICE_EQUAL_TO);
        cpPricingFilter.setDateWithBigDecimal(new BigDecimal(1));
        cpPricingFilter.setSelectedDaysOfWeek(new HashSet<>(List.of(DayOfWeek.THURSDAY)));
        cpPricingFilter.setSelectedOverrides(new HashSet<>(List.of(CPOverrideType.GPINVENTORY)));
        cpPricingFilter.setStartDate(startDate);
        cpPricingFilter.setEndDate(endDate);

        LocalDate arrivalDate = new LocalDate(2024, 1, 4);

        Product sg1 = new Product();
        sg1.setId(12);
        sg1.setName("SG1");
        sg1.setCode(Product.GROUP_PRODUCT_CODE);
        Set<Product> products = createDefaultProduct();
        products.add(sg1);

        Product ip1 = new Product();
        ip1.setId(12);
        ip1.setName("IP1");
        ip1.setCode(Product.INDEPENDENT_PRODUCT_CODE);
        products.add(ip1);

        CPDecisionBAROutput cpDecisionBAROutput6 = new CPDecisionBAROutput();
        cpDecisionBAROutput6.setId(1L);
        cpDecisionBAROutput6.setFinalBAR(new BigDecimal(1));
        cpDecisionBAROutput6.setDecisionReasonTypeId(6);
        cpDecisionBAROutput6.setArrivalDate(arrivalDate);
        cpDecisionBAROutput6.setProduct(sg1);
        cpDecisionBAROutputList.add(cpDecisionBAROutput6);

        CPDecisionBAROutput cpDecisionBAROutput7 = new CPDecisionBAROutput();
        cpDecisionBAROutput7.setId(3L);
        cpDecisionBAROutput7.setFinalBAR(new BigDecimal(1));
        cpDecisionBAROutput7.setDecisionReasonTypeId(6);
        cpDecisionBAROutput7.setArrivalDate(arrivalDate);
        cpDecisionBAROutput7.setProduct(ip1);
        cpDecisionBAROutputList.add(cpDecisionBAROutput7);

        cpPricingFilter.setProducts(products);
        pricingOverrideManager.setCpPricingFilter(cpPricingFilter);

        DecisionGPInventoryLimitOverride decisionGPInventoryLimitOverride = new DecisionGPInventoryLimitOverride();
        decisionGPInventoryLimitOverride.setOccupancyDate(arrivalDate.toDate());

        List<DecisionGPInventoryLimitOverride> inventoryLimitOverrides = new ArrayList<>();
        inventoryLimitOverrides.add(decisionGPInventoryLimitOverride);

        when(inventoryLimitDecisionService.getDecisionInventoryLimitOverridesByOccupancyDateRange(startDate.toDate(), endDate.toDate())).thenReturn(inventoryLimitOverrides);


        List<CPDecisionBAROutput> results = pricingOverrideManager.filterDecisions(cpDecisionBAROutputList,
                new HashSet<>(),
                false);
        assertEquals(1, results.size());
        assertEquals(cpDecisionBAROutputList.get(5), results.get(0));
    }

    @Test
    public void shouldFilterDecisionBasedOnInvetoryOverrideWhenNoGroupProductIsSelected() {
        LocalDate startDate = new LocalDate(2024, 1, 1);
        LocalDate endDate = new LocalDate(2024, 1, 10);
        List<CPDecisionBAROutput> cpDecisionBAROutputList = getCPDecisionBAROutputList();
        CPPricingFilter cpPricingFilter = new CPPricingFilter();
        cpPricingFilter.setDateWithFilterEnum(DateWithFilterEnum.FINAL_PRICE_EQUAL_TO);
        cpPricingFilter.setDateWithBigDecimal(new BigDecimal(1));
        cpPricingFilter.setSelectedDaysOfWeek(new HashSet<>(List.of(DayOfWeek.THURSDAY)));
        cpPricingFilter.setSelectedOverrides(new HashSet<>(List.of(CPOverrideType.GPINVENTORY)));
        cpPricingFilter.setStartDate(startDate);
        cpPricingFilter.setEndDate(endDate);

        LocalDate arrivalDate = new LocalDate(2024, 1, 4);
        Set<Product> products = createDefaultProduct();

        cpPricingFilter.setProducts(products);
        pricingOverrideManager.setCpPricingFilter(cpPricingFilter);


        DecisionGPInventoryLimitOverride decisionGPInventoryLimitOverride = new DecisionGPInventoryLimitOverride();
        decisionGPInventoryLimitOverride.setOccupancyDate(arrivalDate.toDate());

        List<DecisionGPInventoryLimitOverride> inventoryLimitOverrides = new ArrayList<>();
        inventoryLimitOverrides.add(decisionGPInventoryLimitOverride);

        when(inventoryLimitDecisionService.getDecisionInventoryLimitOverridesByOccupancyDateRange(startDate.toDate(), endDate.toDate())).thenReturn(inventoryLimitOverrides);

        List<CPDecisionBAROutput> results = pricingOverrideManager.filterDecisions(cpDecisionBAROutputList,
                new HashSet<>(),
                false);
        assertEquals(0, results.size());
    }

    @Test
    public void shouldFilterDecisionBasedOnInvetoryOverrideWhenInventoryOverrideIsForDifferntDate() {
        LocalDate startDate = new LocalDate(2024, 1, 1);
        LocalDate endDate = new LocalDate(2024, 1, 10);
        List<CPDecisionBAROutput> cpDecisionBAROutputList = getCPDecisionBAROutputList();
        CPPricingFilter cpPricingFilter = new CPPricingFilter();
        cpPricingFilter.setDateWithFilterEnum(DateWithFilterEnum.FINAL_PRICE_EQUAL_TO);
        cpPricingFilter.setDateWithBigDecimal(new BigDecimal(1));
        cpPricingFilter.setSelectedDaysOfWeek(new HashSet<>(List.of(DayOfWeek.THURSDAY)));
        cpPricingFilter.setSelectedOverrides(new HashSet<>(List.of(CPOverrideType.GPINVENTORY)));
        cpPricingFilter.setStartDate(startDate);
        cpPricingFilter.setEndDate(endDate);

        LocalDate arrivalDate = new LocalDate(2024, 2, 4);

        Product sg1 = new Product();
        sg1.setId(12);
        sg1.setName("SG1");
        sg1.setCode(Product.GROUP_PRODUCT_CODE);
        Set<Product> products = createDefaultProduct();
        products.add(sg1);

        CPDecisionBAROutput cpDecisionBAROutput6 = new CPDecisionBAROutput();
        cpDecisionBAROutput6.setId(1L);
        cpDecisionBAROutput6.setFinalBAR(new BigDecimal(1));
        cpDecisionBAROutput6.setDecisionReasonTypeId(6);
        cpDecisionBAROutput6.setArrivalDate(arrivalDate.plusDays(1));
        cpDecisionBAROutput6.setProduct(sg1);
        cpDecisionBAROutputList.add(cpDecisionBAROutput6);

        cpPricingFilter.setProducts(products);
        pricingOverrideManager.setCpPricingFilter(cpPricingFilter);

        DecisionGPInventoryLimitOverride decisionGPInventoryLimitOverride = new DecisionGPInventoryLimitOverride();
        decisionGPInventoryLimitOverride.setOccupancyDate(arrivalDate.toDate());

        List<DecisionGPInventoryLimitOverride> inventoryLimitOverrides = new ArrayList<>();
        inventoryLimitOverrides.add(decisionGPInventoryLimitOverride);

        when(inventoryLimitDecisionService.getDecisionInventoryLimitOverridesByOccupancyDateRange(startDate.toDate(), endDate.toDate())).thenReturn(inventoryLimitOverrides);

        List<CPDecisionBAROutput> results = pricingOverrideManager.filterDecisions(cpDecisionBAROutputList,
                new HashSet<>(),
                false);
        assertEquals(0, results.size());
    }

    @Test
    public void filterByDateWith_ChangesSince() {
        PricingOverrideManager spy = Mockito.spy(pricingOverrideManager);
        doReturn(-21600000).when(spy).getClientTimeZoneOffset();
        doReturn(-21600000).when(spy).getServerTimeZoneOffset();

        AccomClass accomClass = createAllAccomClass();
        AccomType accomType = new AccomType();
        accomType.setId(1);
        Set<AccomType> accomTypes = new HashSet<>(List.of(accomType));
        spy.setBaseRoomTypeList(List.of(accomType));

        AccomType accomType2 = new AccomType();
        accomType2.setId(2);

        Product product = new Product();
        product.setId(1);

        LocalDate localDate = new LocalDate();
        LocalDate localDateMinusOne = new LocalDate().minusDays(1);
        CPDecisionBAROutput cpDecisionBAROutput1 = new CPDecisionBAROutput();
        cpDecisionBAROutput1.setFinalBAR(new BigDecimal(10));
        cpDecisionBAROutput1.setArrivalDate(localDate.plusDays(1));
        cpDecisionBAROutput1.setAccomType(accomType);
        cpDecisionBAROutput1.setProduct(product);
        CPDecisionBAROutput cpDecisionBAROutput2 = new CPDecisionBAROutput();
        cpDecisionBAROutput2.setFinalBAR(new BigDecimal(10));
        cpDecisionBAROutput2.setArrivalDate(localDate);
        cpDecisionBAROutput2.setAccomType(accomType2);
        cpDecisionBAROutput2.setProduct(product);
        CPDecisionBAROutput cpDecisionBAROutput3 = new CPDecisionBAROutput();
        cpDecisionBAROutput3.setFinalBAR(new BigDecimal(10));
        cpDecisionBAROutput3.setArrivalDate(localDate);
        cpDecisionBAROutput3.setAccomType(accomType);
        cpDecisionBAROutput3.setProduct(product);
        List<CPDecisionBAROutput> cpDecisionBAROutputList = new ArrayList<>(Arrays.asList(cpDecisionBAROutput1, cpDecisionBAROutput2, cpDecisionBAROutput3));

        CPPricingFilter cpPricingFilter = new CPPricingFilter();
        cpPricingFilter.setDateWithFilterEnum(DateWithFilterEnum.CHANGES_SINCE);
        LocalDateTime localDateTime = LocalDateTime.of(localDateMinusOne.getYear(), localDateMinusOne.getMonthOfYear(), localDateMinusOne.getDayOfMonth(), 0, 0);
        cpPricingFilter.setDateWithDate(localDateTime);
        cpPricingFilter.setSelectedRoomClass(accomClass);
        cpPricingFilter.setSelectedRoomTypes(accomTypes);
        cpPricingFilter.setProducts(new HashSet<>(List.of(product)));
        cpPricingFilter.setStartDate(localDate);
        cpPricingFilter.setEndDate(localDate);
        cpPricingFilter.setInventoryGroupDto(new InventoryGroupDto(-1, "Property", true));
        spy.setCpPricingFilter(cpPricingFilter);

        CPPaceDecisionBAROutput paceDecisionBAROutput = new CPPaceDecisionBAROutput();
        paceDecisionBAROutput.setArrivalDate(localDate);
        paceDecisionBAROutput.setAccomType(accomType);
        paceDecisionBAROutput.setFinalBAR(new BigDecimal(11));
        paceDecisionBAROutput.setProduct(product);
        Mockito.when(service.getDecisionsFromSpecifiedDate(any(), any(), any(), any(), any())).thenReturn(List.of(paceDecisionBAROutput));

        List<CPDecisionBAROutput> results = spy.filterByDateWith(cpDecisionBAROutputList);
        assertEquals(1, results.size());
        assertEquals(cpDecisionBAROutput3, results.get(0));
    }

    @Test
    public void getPreviousBar() {
        LocalDate localDate = new LocalDate();
        AccomType accomType = new AccomType();
        accomType.setId(1);

        CPPaceDecisionBAROutput paceDecisionBAROutput = new CPPaceDecisionBAROutput();
        paceDecisionBAROutput.setArrivalDate(localDate);
        paceDecisionBAROutput.setAccomType(accomType);
        paceDecisionBAROutput.setFinalBAR(new BigDecimal(11));
        Product product = new Product();
        paceDecisionBAROutput.setProduct(product);
        pricingOverrideManager.setPaceDecisions(List.of(paceDecisionBAROutput));

        CPDecisionBAROutput cpDecisionBAROutput = new CPDecisionBAROutput();
        cpDecisionBAROutput.setArrivalDate(localDate);
        cpDecisionBAROutput.setAccomType(accomType);
        cpDecisionBAROutput.setProduct(product);
        CPOverrideWrapper cpOverrideWrapper = new CPOverrideWrapper(cpDecisionBAROutput, true);
        cpOverrideWrapper.setPreviousBAR(new BigDecimal(10));

        CPPricingFilter cpPricingFilter = new CPPricingFilter();
        cpPricingFilter.setDateWithFilterEnum(DateWithFilterEnum.ALL_DECISIONS);
        pricingOverrideManager.setCpPricingFilter(cpPricingFilter);
        assertEquals(new BigDecimal(10), pricingOverrideManager.getPreviousBar(cpOverrideWrapper));

        CPPricingFilter cpPricingFilter2 = new CPPricingFilter();
        cpPricingFilter2.setDateWithFilterEnum(DateWithFilterEnum.CHANGES_SINCE);
        pricingOverrideManager.setCpPricingFilter(cpPricingFilter2);
        assertEquals(new BigDecimal(11), pricingOverrideManager.getPreviousBar(cpOverrideWrapper));

        CPPricingFilter cpPricingFilter3 = new CPPricingFilter();
        cpPricingFilter3.setDateWithFilterEnum(DateWithFilterEnum.CHANGES_SINCE_LAST_BDE);
        pricingOverrideManager.setCpPricingFilter(cpPricingFilter3);
        assertEquals(new BigDecimal(11), pricingOverrideManager.getPreviousBar(cpOverrideWrapper));

        CPPricingFilter cpPricingFilter4 = new CPPricingFilter();
        cpPricingFilter4.setDateWithFilterEnum(DateWithFilterEnum.CHANGES_SINCE_LAST_IDP);
        pricingOverrideManager.setCpPricingFilter(cpPricingFilter4);
        assertEquals(new BigDecimal(11), pricingOverrideManager.getPreviousBar(cpOverrideWrapper));
    }

    @Test
    public void testGetAveragePriceForPresentAndFutureDates() {

        AccomType accomType1 = new AccomType();
        accomType1.setId(1);

        AccomType accomType2 = new AccomType();
        accomType2.setId(2);

        Product product1 = new Product();
        product1.setId(1);
        product1.setType("BAR");
        product1.setCode("BAR");

        Product product2 = new Product();
        product2.setId(2);
        product2.setType("INDEPENDENTLY");
        product2.setMinLOS(3);
        product2.setCode("INDEPENDENT");

        AccomClass accomClass = new AccomClass();
        accomClass.setName("All");

        Set<AccomType> accomTypes = new HashSet<>();
        accomTypes.add(accomType1);
        accomTypes.add(accomType2);
        CPPricingFilter cpPricingFilter1 = new CPPricingFilter();
        cpPricingFilter1.setSelectedRoomClass(accomClass);
        cpPricingFilter1.setSelectedRoomTypes(accomTypes);
        cpPricingFilter1.setStartDate(new LocalDate(2018, 8, 23));
        cpPricingFilter1.setEndDate(new LocalDate(2018, 8, 24));
        pricingOverrideManager.setCpPricingFilter(cpPricingFilter1);

        CPDecisionBAROutput cpDecisionBAROutput1 = new CPDecisionBAROutput();
        cpDecisionBAROutput1.setFinalBAR(new BigDecimal(100));
        cpDecisionBAROutput1.setArrivalDate(new LocalDate(2018, 8, 23));
        cpDecisionBAROutput1.setProduct(product1);
        cpDecisionBAROutput1.setAccomType(accomType1);

        CPDecisionBAROutput cpDecisionBAROutput2 = new CPDecisionBAROutput();
        cpDecisionBAROutput2.setFinalBAR(new BigDecimal(150));
        cpDecisionBAROutput2.setArrivalDate(new LocalDate(2018, 8, 23));
        cpDecisionBAROutput2.setProduct(product1);
        cpDecisionBAROutput2.setAccomType(accomType2);

        CPDecisionBAROutput cpDecisionBAROutput3 = new CPDecisionBAROutput();
        cpDecisionBAROutput3.setFinalBAR(new BigDecimal(160));
        cpDecisionBAROutput3.setArrivalDate(new LocalDate(2018, 8, 23));
        cpDecisionBAROutput3.setProduct(product2);
        cpDecisionBAROutput3.setAccomType(accomType1);

        CPDecisionBAROutput cpDecisionBAROutput4 = new CPDecisionBAROutput();
        cpDecisionBAROutput4.setFinalBAR(new BigDecimal(105));
        cpDecisionBAROutput4.setArrivalDate(new LocalDate(2018, 8, 23));
        cpDecisionBAROutput4.setProduct(product2);
        cpDecisionBAROutput4.setAccomType(accomType2);

        CPDecisionBAROutput cpDecisionBAROutput5 = new CPDecisionBAROutput();
        cpDecisionBAROutput5.setFinalBAR(new BigDecimal(99));
        cpDecisionBAROutput5.setArrivalDate(new LocalDate(2018, 8, 24));
        cpDecisionBAROutput5.setProduct(product1);
        cpDecisionBAROutput5.setAccomType(accomType1);

        CPDecisionBAROutput cpDecisionBAROutput6 = new CPDecisionBAROutput();
        cpDecisionBAROutput6.setFinalBAR(new BigDecimal(111));
        cpDecisionBAROutput6.setArrivalDate(new LocalDate(2018, 8, 24));
        cpDecisionBAROutput6.setProduct(product1);
        cpDecisionBAROutput6.setAccomType(accomType2);

        CPDecisionBAROutput cpDecisionBAROutput7 = new CPDecisionBAROutput();
        cpDecisionBAROutput7.setFinalBAR(new BigDecimal(200));
        cpDecisionBAROutput7.setArrivalDate(new LocalDate(2018, 8, 24));
        cpDecisionBAROutput7.setProduct(product2);
        cpDecisionBAROutput7.setAccomType(accomType1);

        CPDecisionBAROutput cpDecisionBAROutput8 = new CPDecisionBAROutput();
        cpDecisionBAROutput8.setFinalBAR(new BigDecimal(199));
        cpDecisionBAROutput8.setArrivalDate(new LocalDate(2018, 8, 24));
        cpDecisionBAROutput8.setProduct(product2);
        cpDecisionBAROutput8.setAccomType(accomType2);

        CPDecisionBAROutput cpDecisionBAROutput9 = new CPDecisionBAROutput();
        cpDecisionBAROutput9.setFinalBAR(new BigDecimal(119));
        cpDecisionBAROutput9.setArrivalDate(new LocalDate(2018, 8, 25));
        cpDecisionBAROutput9.setProduct(product1);
        cpDecisionBAROutput9.setAccomType(accomType1);

        CPDecisionBAROutput cpDecisionBAROutput10 = new CPDecisionBAROutput();
        cpDecisionBAROutput10.setFinalBAR(new BigDecimal(123));
        cpDecisionBAROutput10.setArrivalDate(new LocalDate(2018, 8, 25));
        cpDecisionBAROutput10.setProduct(product1);
        cpDecisionBAROutput10.setAccomType(accomType2);

        CPDecisionBAROutput cpDecisionBAROutput11 = new CPDecisionBAROutput();
        cpDecisionBAROutput11.setFinalBAR(new BigDecimal(205));
        cpDecisionBAROutput11.setArrivalDate(new LocalDate(2018, 8, 25));
        cpDecisionBAROutput11.setProduct(product2);
        cpDecisionBAROutput11.setAccomType(accomType1);

        CPDecisionBAROutput cpDecisionBAROutput12 = new CPDecisionBAROutput();
        cpDecisionBAROutput12.setFinalBAR(new BigDecimal(188));
        cpDecisionBAROutput12.setArrivalDate(new LocalDate(2018, 8, 25));
        cpDecisionBAROutput12.setProduct(product2);
        cpDecisionBAROutput12.setAccomType(accomType2);

        List<CPDecisionBAROutput> cpDecisionBAROutputList = new ArrayList<>(Arrays.asList(cpDecisionBAROutput1, cpDecisionBAROutput2, cpDecisionBAROutput3, cpDecisionBAROutput4,
                cpDecisionBAROutput5, cpDecisionBAROutput6, cpDecisionBAROutput7, cpDecisionBAROutput8, cpDecisionBAROutput9,
                cpDecisionBAROutput10, cpDecisionBAROutput11, cpDecisionBAROutput12));

        when(service.getCPDecisionsBetweenDatesForProductAndRoomTypes(any(), any(), any(), anyList())).thenReturn(cpDecisionBAROutputList);
        when(dateService.getCaughtUpLocalDate()).thenReturn(new LocalDate(2018, 8, 23));
        when(service.getMaxDecisionDate()).thenReturn(new LocalDate(2018, 8, 25));
        Map<LocalDate, List<CPDecisionBAROutput>> averagePriceMap = pricingOverrideManager.getAveragePriceDecisionMap(cpDecisionBAROutputList);

        List<CPDecisionBAROutput> cpDecisionBAROutputCheckList = averagePriceMap.get(new LocalDate(2018, 8, 23));
        assertEquals(accomType1, cpDecisionBAROutputCheckList.get(0).getAccomType());
        assertEquals(product2, cpDecisionBAROutputCheckList.get(0).getProduct());
        assertEquals(BigDecimal.valueOf(188.33), cpDecisionBAROutputCheckList.get(0).getAveragePrice());

        assertEquals(accomType2, cpDecisionBAROutputCheckList.get(1).getAccomType());
        assertEquals(product2, cpDecisionBAROutputCheckList.get(1).getProduct());
        assertEquals(BigDecimal.valueOf(164.00).setScale(2), cpDecisionBAROutputCheckList.get(1).getAveragePrice());

        List<CPDecisionBAROutput> cpDecisionBAROutputCheckListForFuture = averagePriceMap.get(new LocalDate(2018, 8, 24));
        assertEquals(accomType1, cpDecisionBAROutputCheckListForFuture.get(0).getAccomType());
        assertEquals(product2, cpDecisionBAROutputCheckListForFuture.get(0).getProduct());
        assertNull(cpDecisionBAROutputCheckListForFuture.get(0).getAveragePrice());

        assertEquals(accomType2, cpDecisionBAROutputCheckListForFuture.get(1).getAccomType());
        assertEquals(product2, cpDecisionBAROutputCheckListForFuture.get(1).getProduct());
        assertNull(cpDecisionBAROutputCheckListForFuture.get(1).getAveragePrice());

        assertEquals(product1, cpDecisionBAROutputList.get(0).getProduct());
        assertEquals(accomType1, cpDecisionBAROutputList.get(0).getAccomType());
        assertNull(cpDecisionBAROutputList.get(0).getAveragePrice());
    }

    @Test
    public void testAveragePriceForPastDate() {
        AccomType accomType1 = new AccomType();
        accomType1.setId(1);

        AccomType accomType2 = new AccomType();
        accomType2.setId(2);

        Product product1 = new Product();
        product1.setId(1);
        product1.setType("BAR");
        product1.setCode("BAR");

        Product product2 = new Product();
        product2.setId(2);
        product2.setType("INDEPENDENTLY");
        product2.setMinLOS(3);
        product2.setCode("INDEPENDENT");

        AccomClass accomClass = new AccomClass();
        accomClass.setName("All");

        Set<AccomType> accomTypes = new HashSet<>();
        accomTypes.add(accomType1);
        accomTypes.add(accomType2);
        CPPricingFilter cpPricingFilter1 = new CPPricingFilter();
        cpPricingFilter1.setSelectedRoomClass(accomClass);
        cpPricingFilter1.setSelectedRoomTypes(accomTypes);
        cpPricingFilter1.setStartDate(new LocalDate(2018, 8, 23));
        cpPricingFilter1.setEndDate(new LocalDate(2018, 8, 25));
        pricingOverrideManager.setCpPricingFilter(cpPricingFilter1);

        CPDecisionBAROutput cpDecisionBAROutput1 = new CPDecisionBAROutput();
        cpDecisionBAROutput1.setFinalBAR(new BigDecimal(100));
        cpDecisionBAROutput1.setArrivalDate(new LocalDate(2018, 8, 23));
        cpDecisionBAROutput1.setProduct(product1);
        cpDecisionBAROutput1.setAccomType(accomType1);

        CPDecisionBAROutput cpDecisionBAROutput2 = new CPDecisionBAROutput();
        cpDecisionBAROutput2.setFinalBAR(new BigDecimal(150));
        cpDecisionBAROutput2.setArrivalDate(new LocalDate(2018, 8, 23));
        cpDecisionBAROutput2.setProduct(product1);
        cpDecisionBAROutput2.setAccomType(accomType2);

        CPDecisionBAROutput cpDecisionBAROutput3 = new CPDecisionBAROutput();
        cpDecisionBAROutput3.setFinalBAR(new BigDecimal(160));
        cpDecisionBAROutput3.setArrivalDate(new LocalDate(2018, 8, 23));
        cpDecisionBAROutput3.setProduct(product2);
        cpDecisionBAROutput3.setAccomType(accomType1);

        CPDecisionBAROutput cpDecisionBAROutput4 = new CPDecisionBAROutput();
        cpDecisionBAROutput4.setFinalBAR(new BigDecimal(105));
        cpDecisionBAROutput4.setArrivalDate(new LocalDate(2018, 8, 23));
        cpDecisionBAROutput4.setProduct(product2);
        cpDecisionBAROutput4.setAccomType(accomType2);

        CPDecisionBAROutput cpDecisionBAROutput5 = new CPDecisionBAROutput();
        cpDecisionBAROutput5.setFinalBAR(new BigDecimal(99));
        cpDecisionBAROutput5.setArrivalDate(new LocalDate(2018, 8, 24));
        cpDecisionBAROutput5.setProduct(product1);
        cpDecisionBAROutput5.setAccomType(accomType1);

        CPDecisionBAROutput cpDecisionBAROutput6 = new CPDecisionBAROutput();
        cpDecisionBAROutput6.setFinalBAR(new BigDecimal(111));
        cpDecisionBAROutput6.setArrivalDate(new LocalDate(2018, 8, 24));
        cpDecisionBAROutput6.setProduct(product1);
        cpDecisionBAROutput6.setAccomType(accomType2);

        CPDecisionBAROutput cpDecisionBAROutput7 = new CPDecisionBAROutput();
        cpDecisionBAROutput7.setFinalBAR(new BigDecimal(200));
        cpDecisionBAROutput7.setArrivalDate(new LocalDate(2018, 8, 24));
        cpDecisionBAROutput7.setProduct(product2);
        cpDecisionBAROutput7.setAccomType(accomType1);

        CPDecisionBAROutput cpDecisionBAROutput8 = new CPDecisionBAROutput();
        cpDecisionBAROutput8.setFinalBAR(new BigDecimal(199));
        cpDecisionBAROutput8.setArrivalDate(new LocalDate(2018, 8, 24));
        cpDecisionBAROutput8.setProduct(product2);
        cpDecisionBAROutput8.setAccomType(accomType2);

        CPDecisionBAROutput cpDecisionBAROutput9 = new CPDecisionBAROutput();
        cpDecisionBAROutput9.setFinalBAR(new BigDecimal(119));
        cpDecisionBAROutput9.setArrivalDate(new LocalDate(2018, 8, 25));
        cpDecisionBAROutput9.setProduct(product1);
        cpDecisionBAROutput9.setAccomType(accomType1);

        CPDecisionBAROutput cpDecisionBAROutput10 = new CPDecisionBAROutput();
        cpDecisionBAROutput10.setFinalBAR(new BigDecimal(123));
        cpDecisionBAROutput10.setArrivalDate(new LocalDate(2018, 8, 25));
        cpDecisionBAROutput10.setProduct(product1);
        cpDecisionBAROutput10.setAccomType(accomType2);

        CPDecisionBAROutput cpDecisionBAROutput11 = new CPDecisionBAROutput();
        cpDecisionBAROutput11.setFinalBAR(new BigDecimal(205));
        cpDecisionBAROutput11.setArrivalDate(new LocalDate(2018, 8, 25));
        cpDecisionBAROutput11.setProduct(product2);
        cpDecisionBAROutput11.setAccomType(accomType1);

        CPDecisionBAROutput cpDecisionBAROutput12 = new CPDecisionBAROutput();
        cpDecisionBAROutput12.setFinalBAR(new BigDecimal(188));
        cpDecisionBAROutput12.setArrivalDate(new LocalDate(2018, 8, 25));
        cpDecisionBAROutput12.setProduct(product2);
        cpDecisionBAROutput12.setAccomType(accomType2);

        CPDecisionBAROutput cpDecisionBAROutput13 = new CPDecisionBAROutput();
        cpDecisionBAROutput13.setFinalBAR(new BigDecimal(299));
        cpDecisionBAROutput13.setArrivalDate(new LocalDate(2018, 8, 26));
        cpDecisionBAROutput13.setProduct(product1);
        cpDecisionBAROutput13.setAccomType(accomType1);

        CPDecisionBAROutput cpDecisionBAROutput14 = new CPDecisionBAROutput();
        cpDecisionBAROutput14.setFinalBAR(new BigDecimal(121));
        cpDecisionBAROutput14.setArrivalDate(new LocalDate(2018, 8, 26));
        cpDecisionBAROutput14.setProduct(product1);
        cpDecisionBAROutput14.setAccomType(accomType2);

        CPDecisionBAROutput cpDecisionBAROutput15 = new CPDecisionBAROutput();
        cpDecisionBAROutput15.setFinalBAR(new BigDecimal(185));
        cpDecisionBAROutput15.setArrivalDate(new LocalDate(2018, 8, 26));
        cpDecisionBAROutput15.setProduct(product2);
        cpDecisionBAROutput15.setAccomType(accomType1);

        CPDecisionBAROutput cpDecisionBAROutput16 = new CPDecisionBAROutput();
        cpDecisionBAROutput16.setFinalBAR(new BigDecimal(210));
        cpDecisionBAROutput16.setArrivalDate(new LocalDate(2018, 8, 26));
        cpDecisionBAROutput16.setProduct(product2);
        cpDecisionBAROutput16.setAccomType(accomType2);

        List<CPDecisionBAROutput> cpDecisionBAROutputList = new ArrayList<>(Arrays.asList(cpDecisionBAROutput1, cpDecisionBAROutput2, cpDecisionBAROutput3, cpDecisionBAROutput4,
                cpDecisionBAROutput5, cpDecisionBAROutput6, cpDecisionBAROutput7, cpDecisionBAROutput8, cpDecisionBAROutput9,
                cpDecisionBAROutput10, cpDecisionBAROutput11, cpDecisionBAROutput12, cpDecisionBAROutput13, cpDecisionBAROutput14,
                cpDecisionBAROutput15, cpDecisionBAROutput16));

        when(service.getCPDecisionsBetweenDatesForProductAndRoomTypes(any(), any(), any(), anyList())).thenReturn(cpDecisionBAROutputList);
        when(dateService.getCaughtUpLocalDate()).thenReturn(new LocalDate(2018, 8, 24));
        when(service.getMaxDecisionDate()).thenReturn(new LocalDate(2018, 8, 26));
        Map<LocalDate, List<CPDecisionBAROutput>> averagePriceMap = pricingOverrideManager.getAveragePriceDecisionMap(cpDecisionBAROutputList);

        List<CPDecisionBAROutput> cpDecisionBAROutputCheckListForPast = averagePriceMap.get(new LocalDate(2018, 8, 23));
        assertEquals(accomType1, cpDecisionBAROutputCheckListForPast.get(0).getAccomType());
        assertEquals(product2, cpDecisionBAROutputCheckListForPast.get(0).getProduct());
        assertNull(cpDecisionBAROutputCheckListForPast.get(0).getAveragePrice());

        assertEquals(accomType2, cpDecisionBAROutputCheckListForPast.get(1).getAccomType());
        assertEquals(product2, cpDecisionBAROutputCheckListForPast.get(1).getProduct());
        assertNull(cpDecisionBAROutputCheckListForPast.get(1).getAveragePrice());

        List<CPDecisionBAROutput> cpDecisionBAROutputCheckList = averagePriceMap.get(new LocalDate(2018, 8, 24));
        assertEquals(accomType1, cpDecisionBAROutputCheckList.get(0).getAccomType());
        assertEquals(product2, cpDecisionBAROutputCheckList.get(0).getProduct());
        assertEquals(BigDecimal.valueOf(196.67), cpDecisionBAROutputCheckList.get(0).getAveragePrice());

        assertEquals(accomType2, cpDecisionBAROutputCheckList.get(1).getAccomType());
        assertEquals(product2, cpDecisionBAROutputCheckList.get(1).getProduct());
        assertEquals(BigDecimal.valueOf(199.00).setScale(2), cpDecisionBAROutputCheckList.get(1).getAveragePrice());

        assertNull(averagePriceMap.get(new LocalDate(2018, 8, 28)));

    }

    @Test
    public void testAveragePriceForFiveMinLos() {
        AccomType accomType1 = new AccomType();
        accomType1.setId(1);

        AccomType accomType2 = new AccomType();
        accomType2.setId(2);

        Product product1 = new Product();
        product1.setId(2);
        product1.setType("INDEPENDENTLY");
        product1.setMinLOS(5);
        product1.setCode("INDEPENDENT");

        AccomClass accomClass = new AccomClass();
        accomClass.setName("All");

        Set<AccomType> accomTypes = new HashSet<>();
        accomTypes.add(accomType1);
        accomTypes.add(accomType2);
        CPPricingFilter cpPricingFilter1 = new CPPricingFilter();
        cpPricingFilter1.setSelectedRoomClass(accomClass);
        cpPricingFilter1.setSelectedRoomTypes(accomTypes);
        cpPricingFilter1.setStartDate(new LocalDate(2018, 8, 23));
        cpPricingFilter1.setEndDate(new LocalDate(2018, 8, 30));
        pricingOverrideManager.setCpPricingFilter(cpPricingFilter1);


        CPDecisionBAROutput cpDecisionBAROutput1 = new CPDecisionBAROutput();
        cpDecisionBAROutput1.setFinalBAR(new BigDecimal(150));
        cpDecisionBAROutput1.setArrivalDate(new LocalDate(2018, 8, 23));
        cpDecisionBAROutput1.setProduct(product1);
        cpDecisionBAROutput1.setAccomType(accomType1);

        CPDecisionBAROutput cpDecisionBAROutput2 = new CPDecisionBAROutput();
        cpDecisionBAROutput2.setFinalBAR(new BigDecimal(160));
        cpDecisionBAROutput2.setArrivalDate(new LocalDate(2018, 8, 23));
        cpDecisionBAROutput2.setProduct(product1);
        cpDecisionBAROutput2.setAccomType(accomType2);

        CPDecisionBAROutput cpDecisionBAROutput3 = new CPDecisionBAROutput();
        cpDecisionBAROutput3.setFinalBAR(new BigDecimal(110));
        cpDecisionBAROutput3.setArrivalDate(new LocalDate(2018, 8, 24));
        cpDecisionBAROutput3.setProduct(product1);
        cpDecisionBAROutput3.setAccomType(accomType1);

        CPDecisionBAROutput cpDecisionBAROutput4 = new CPDecisionBAROutput();
        cpDecisionBAROutput4.setFinalBAR(new BigDecimal(125));
        cpDecisionBAROutput4.setArrivalDate(new LocalDate(2018, 8, 24));
        cpDecisionBAROutput4.setProduct(product1);
        cpDecisionBAROutput4.setAccomType(accomType2);

        CPDecisionBAROutput cpDecisionBAROutput5 = new CPDecisionBAROutput();
        cpDecisionBAROutput5.setFinalBAR(new BigDecimal(99));
        cpDecisionBAROutput5.setArrivalDate(new LocalDate(2018, 8, 25));
        cpDecisionBAROutput5.setProduct(product1);
        cpDecisionBAROutput5.setAccomType(accomType1);

        CPDecisionBAROutput cpDecisionBAROutput6 = new CPDecisionBAROutput();
        cpDecisionBAROutput6.setFinalBAR(new BigDecimal(89));
        cpDecisionBAROutput6.setArrivalDate(new LocalDate(2018, 8, 25));
        cpDecisionBAROutput6.setProduct(product1);
        cpDecisionBAROutput6.setAccomType(accomType2);

        CPDecisionBAROutput cpDecisionBAROutput7 = new CPDecisionBAROutput();
        cpDecisionBAROutput7.setFinalBAR(new BigDecimal(189));
        cpDecisionBAROutput7.setArrivalDate(new LocalDate(2018, 8, 26));
        cpDecisionBAROutput7.setProduct(product1);
        cpDecisionBAROutput7.setAccomType(accomType1);

        CPDecisionBAROutput cpDecisionBAROutput8 = new CPDecisionBAROutput();
        cpDecisionBAROutput8.setFinalBAR(new BigDecimal(129));
        cpDecisionBAROutput8.setArrivalDate(new LocalDate(2018, 8, 26));
        cpDecisionBAROutput8.setProduct(product1);
        cpDecisionBAROutput8.setAccomType(accomType2);

        CPDecisionBAROutput cpDecisionBAROutput9 = new CPDecisionBAROutput();
        cpDecisionBAROutput9.setFinalBAR(new BigDecimal(195));
        cpDecisionBAROutput9.setArrivalDate(new LocalDate(2018, 8, 27));
        cpDecisionBAROutput9.setProduct(product1);
        cpDecisionBAROutput9.setAccomType(accomType1);

        CPDecisionBAROutput cpDecisionBAROutput10 = new CPDecisionBAROutput();
        cpDecisionBAROutput10.setFinalBAR(new BigDecimal(182));
        cpDecisionBAROutput10.setArrivalDate(new LocalDate(2018, 8, 27));
        cpDecisionBAROutput10.setProduct(product1);
        cpDecisionBAROutput10.setAccomType(accomType2);

        CPDecisionBAROutput cpDecisionBAROutput11 = new CPDecisionBAROutput();
        cpDecisionBAROutput11.setFinalBAR(new BigDecimal(118));
        cpDecisionBAROutput11.setArrivalDate(new LocalDate(2018, 8, 28));
        cpDecisionBAROutput11.setProduct(product1);
        cpDecisionBAROutput11.setAccomType(accomType1);

        CPDecisionBAROutput cpDecisionBAROutput12 = new CPDecisionBAROutput();
        cpDecisionBAROutput12.setFinalBAR(new BigDecimal(119));
        cpDecisionBAROutput12.setArrivalDate(new LocalDate(2018, 8, 28));
        cpDecisionBAROutput12.setProduct(product1);
        cpDecisionBAROutput12.setAccomType(accomType2);


        CPDecisionBAROutput cpDecisionBAROutput13 = new CPDecisionBAROutput();
        cpDecisionBAROutput13.setFinalBAR(new BigDecimal(129));
        cpDecisionBAROutput13.setArrivalDate(new LocalDate(2018, 8, 29));
        cpDecisionBAROutput13.setProduct(product1);
        cpDecisionBAROutput13.setAccomType(accomType1);

        CPDecisionBAROutput cpDecisionBAROutput14 = new CPDecisionBAROutput();
        cpDecisionBAROutput14.setFinalBAR(new BigDecimal(113));
        cpDecisionBAROutput14.setArrivalDate(new LocalDate(2018, 8, 29));
        cpDecisionBAROutput14.setProduct(product1);
        cpDecisionBAROutput14.setAccomType(accomType2);

        List<CPDecisionBAROutput> cpDecisionBAROutputList = new ArrayList<>(Arrays.asList(cpDecisionBAROutput1, cpDecisionBAROutput2, cpDecisionBAROutput3, cpDecisionBAROutput4,
                cpDecisionBAROutput5, cpDecisionBAROutput6, cpDecisionBAROutput7, cpDecisionBAROutput8, cpDecisionBAROutput9,
                cpDecisionBAROutput10, cpDecisionBAROutput11, cpDecisionBAROutput12, cpDecisionBAROutput13, cpDecisionBAROutput14));

        when(service.getCPDecisionsBetweenDatesForProductAndRoomTypes(any(), any(), any(), anyList())).thenReturn(cpDecisionBAROutputList);
        when(dateService.getCaughtUpLocalDate()).thenReturn(new LocalDate(2018, 8, 24));
        when(service.getMaxDecisionDate()).thenReturn(new LocalDate(2018, 8, 29));
        Map<LocalDate, List<CPDecisionBAROutput>> averagePriceMap = pricingOverrideManager.getAveragePriceDecisionMap(cpDecisionBAROutputList);

        List<CPDecisionBAROutput> cpDecisionBAROutputCheckList = averagePriceMap.get(new LocalDate(2018, 8, 24));
        assertEquals(accomType1, cpDecisionBAROutputCheckList.get(0).getAccomType());
        assertEquals(product1, cpDecisionBAROutputCheckList.get(0).getProduct());
        assertEquals(BigDecimal.valueOf(142.20).setScale(2), cpDecisionBAROutputCheckList.get(0).getAveragePrice());

        assertEquals(accomType2, cpDecisionBAROutputCheckList.get(1).getAccomType());
        assertEquals(product1, cpDecisionBAROutputList.get(1).getProduct());
        assertEquals(BigDecimal.valueOf(128.80).setScale(2), cpDecisionBAROutputCheckList.get(1).getAveragePrice());

        List<CPDecisionBAROutput> cpDecisionBAROutputCheckListFor25 = averagePriceMap.get(new LocalDate(2018, 8, 25));
        assertEquals(accomType1, cpDecisionBAROutputCheckListFor25.get(0).getAccomType());
        assertEquals(product1, cpDecisionBAROutputCheckListFor25.get(0).getProduct());
        assertEquals(BigDecimal.valueOf(146.00).setScale(2), cpDecisionBAROutputCheckListFor25.get(0).getAveragePrice());

        assertEquals(accomType2, cpDecisionBAROutputCheckListFor25.get(1).getAccomType());
        assertEquals(product1, cpDecisionBAROutputCheckListFor25.get(1).getProduct());
        assertEquals(BigDecimal.valueOf(126.40).setScale(2), cpDecisionBAROutputCheckListFor25.get(1).getAveragePrice());

        List<CPDecisionBAROutput> cpDecisionBAROutputCheckListFor26 = averagePriceMap.get(new LocalDate(2018, 8, 26));
        assertEquals(accomType1, cpDecisionBAROutputCheckListFor26.get(0).getAccomType());
        assertEquals(product1, cpDecisionBAROutputCheckListFor26.get(0).getProduct());
        assertNull(cpDecisionBAROutputCheckListFor26.get(0).getAveragePrice());

        assertEquals(accomType2, cpDecisionBAROutputCheckListFor26.get(1).getAccomType());
        assertEquals(product1, cpDecisionBAROutputCheckListFor26.get(1).getProduct());
        assertNull(cpDecisionBAROutputCheckListFor26.get(1).getAveragePrice());

        assertNull(averagePriceMap.get(new LocalDate(2018, 8, 30)));
    }

    @Test
    public void testAveragePriceForOnlyBAR() {
        AccomType accomType1 = new AccomType();
        accomType1.setId(1);

        AccomType accomType2 = new AccomType();
        accomType2.setId(2);

        Product product1 = new Product();
        product1.setId(1);
        product1.setType("BAR");
        product1.setCode("BAR");

        AccomClass accomClass = new AccomClass();
        accomClass.setName("All");

        Set<AccomType> accomTypes = new HashSet<>();
        accomTypes.add(accomType1);
        accomTypes.add(accomType2);
        CPPricingFilter cpPricingFilter1 = new CPPricingFilter();
        cpPricingFilter1.setSelectedRoomClass(accomClass);
        cpPricingFilter1.setSelectedRoomTypes(accomTypes);
        cpPricingFilter1.setStartDate(new LocalDate(2018, 8, 23));
        cpPricingFilter1.setEndDate(new LocalDate(2018, 8, 25));
        pricingOverrideManager.setCpPricingFilter(cpPricingFilter1);

        CPDecisionBAROutput cpDecisionBAROutput1 = new CPDecisionBAROutput();
        cpDecisionBAROutput1.setFinalBAR(new BigDecimal(100));
        cpDecisionBAROutput1.setArrivalDate(new LocalDate(2018, 8, 23));
        cpDecisionBAROutput1.setProduct(product1);
        cpDecisionBAROutput1.setAccomType(accomType1);

        CPDecisionBAROutput cpDecisionBAROutput2 = new CPDecisionBAROutput();
        cpDecisionBAROutput2.setFinalBAR(new BigDecimal(150));
        cpDecisionBAROutput2.setArrivalDate(new LocalDate(2018, 8, 23));
        cpDecisionBAROutput2.setProduct(product1);
        cpDecisionBAROutput2.setAccomType(accomType2);

        CPDecisionBAROutput cpDecisionBAROutput3 = new CPDecisionBAROutput();
        cpDecisionBAROutput3.setFinalBAR(new BigDecimal(110));
        cpDecisionBAROutput3.setArrivalDate(new LocalDate(2018, 8, 24));
        cpDecisionBAROutput3.setProduct(product1);
        cpDecisionBAROutput3.setAccomType(accomType1);

        CPDecisionBAROutput cpDecisionBAROutput4 = new CPDecisionBAROutput();
        cpDecisionBAROutput4.setFinalBAR(new BigDecimal(99));
        cpDecisionBAROutput4.setArrivalDate(new LocalDate(2018, 8, 24));
        cpDecisionBAROutput4.setProduct(product1);
        cpDecisionBAROutput4.setAccomType(accomType2);

        CPDecisionBAROutput cpDecisionBAROutput5 = new CPDecisionBAROutput();
        cpDecisionBAROutput5.setFinalBAR(new BigDecimal(199));
        cpDecisionBAROutput5.setArrivalDate(new LocalDate(2018, 8, 25));
        cpDecisionBAROutput5.setProduct(product1);
        cpDecisionBAROutput5.setAccomType(accomType1);

        CPDecisionBAROutput cpDecisionBAROutput6 = new CPDecisionBAROutput();
        cpDecisionBAROutput6.setFinalBAR(new BigDecimal(190));
        cpDecisionBAROutput6.setArrivalDate(new LocalDate(2018, 8, 25));
        cpDecisionBAROutput6.setProduct(product1);
        cpDecisionBAROutput6.setAccomType(accomType2);

        CPDecisionBAROutput cpDecisionBAROutput7 = new CPDecisionBAROutput();
        cpDecisionBAROutput7.setFinalBAR(new BigDecimal(129));
        cpDecisionBAROutput7.setArrivalDate(new LocalDate(2018, 8, 26));
        cpDecisionBAROutput7.setProduct(product1);
        cpDecisionBAROutput7.setAccomType(accomType1);

        CPDecisionBAROutput cpDecisionBAROutput8 = new CPDecisionBAROutput();
        cpDecisionBAROutput8.setFinalBAR(new BigDecimal(129));
        cpDecisionBAROutput8.setArrivalDate(new LocalDate(2018, 8, 26));
        cpDecisionBAROutput8.setProduct(product1);
        cpDecisionBAROutput8.setAccomType(accomType2);


        List<CPDecisionBAROutput> cpDecisionBAROutputList = new ArrayList<>(Arrays.asList(cpDecisionBAROutput1, cpDecisionBAROutput2, cpDecisionBAROutput3, cpDecisionBAROutput4,
                cpDecisionBAROutput5, cpDecisionBAROutput6, cpDecisionBAROutput7, cpDecisionBAROutput8));

        when(service.getCPDecisionsBetweenDatesForProductAndRoomTypes(any(), any(), any(), anyList())).thenReturn(cpDecisionBAROutputList);
        when(dateService.getCaughtUpLocalDate()).thenReturn(new LocalDate(2018, 8, 24));
        when(service.getMaxDecisionDate()).thenReturn(new LocalDate(2018, 8, 26));
        Map<LocalDate, List<CPDecisionBAROutput>> averagePriceMap = pricingOverrideManager.getAveragePriceDecisionMap(cpDecisionBAROutputList);

        assertNull(averagePriceMap.get(new LocalDate(2018, 8, 24)));
        assertNull(averagePriceMap.get(new LocalDate(2018, 8, 26)));
        assertNull(averagePriceMap.get(new LocalDate(2018, 8, 28)));
        assertNull(cpDecisionBAROutputList.get(0).getAveragePrice());
        assertNull(cpDecisionBAROutputList.get(1).getAveragePrice());
        assertNull(cpDecisionBAROutputList.get(5).getAveragePrice());
    }

    @Test
    public void testAveragePriceWhenAllSelectedInAccomClasses() {

        AccomType accomType1 = new AccomType();
        accomType1.setId(1);

        AccomType accomType2 = new AccomType();
        accomType2.setId(2);

        Product product1 = new Product();
        product1.setId(2);
        product1.setType("INDEPENDENTLY");
        product1.setMinLOS(3);
        product1.setCode("INDEPENDENT");

        AccomClass accomClass = new AccomClass();
        accomClass.setName("All");

        List<AccomType> accomTypes = new ArrayList<>();
        accomTypes.add(accomType1);
        accomTypes.add(accomType2);
        CPPricingFilter cpPricingFilter1 = new CPPricingFilter();
        cpPricingFilter1.setSelectedRoomClass(accomClass);
        cpPricingFilter1.setStartDate(new LocalDate(2018, 8, 23));
        cpPricingFilter1.setEndDate(new LocalDate(2018, 8, 30));
        cpPricingFilter1.setSelectedRoomTypes(new HashSet<>());
        pricingOverrideManager.setCpPricingFilter(cpPricingFilter1);
        pricingOverrideManager.setBaseRoomTypeList(accomTypes);

        CPDecisionBAROutput cpDecisionBAROutput1 = new CPDecisionBAROutput();
        cpDecisionBAROutput1.setFinalBAR(new BigDecimal(150));
        cpDecisionBAROutput1.setArrivalDate(new LocalDate(2018, 8, 23));
        cpDecisionBAROutput1.setProduct(product1);
        cpDecisionBAROutput1.setAccomType(accomType1);

        CPDecisionBAROutput cpDecisionBAROutput2 = new CPDecisionBAROutput();
        cpDecisionBAROutput2.setFinalBAR(new BigDecimal(160));
        cpDecisionBAROutput2.setArrivalDate(new LocalDate(2018, 8, 23));
        cpDecisionBAROutput2.setProduct(product1);
        cpDecisionBAROutput2.setAccomType(accomType2);

        CPDecisionBAROutput cpDecisionBAROutput3 = new CPDecisionBAROutput();
        cpDecisionBAROutput3.setFinalBAR(new BigDecimal(110));
        cpDecisionBAROutput3.setArrivalDate(new LocalDate(2018, 8, 24));
        cpDecisionBAROutput3.setProduct(product1);
        cpDecisionBAROutput3.setAccomType(accomType1);

        CPDecisionBAROutput cpDecisionBAROutput4 = new CPDecisionBAROutput();
        cpDecisionBAROutput4.setFinalBAR(new BigDecimal(125));
        cpDecisionBAROutput4.setArrivalDate(new LocalDate(2018, 8, 24));
        cpDecisionBAROutput4.setProduct(product1);
        cpDecisionBAROutput4.setAccomType(accomType2);

        CPDecisionBAROutput cpDecisionBAROutput5 = new CPDecisionBAROutput();
        cpDecisionBAROutput5.setFinalBAR(new BigDecimal(99));
        cpDecisionBAROutput5.setArrivalDate(new LocalDate(2018, 8, 25));
        cpDecisionBAROutput5.setProduct(product1);
        cpDecisionBAROutput5.setAccomType(accomType1);

        CPDecisionBAROutput cpDecisionBAROutput6 = new CPDecisionBAROutput();
        cpDecisionBAROutput6.setFinalBAR(new BigDecimal(89));
        cpDecisionBAROutput6.setArrivalDate(new LocalDate(2018, 8, 25));
        cpDecisionBAROutput6.setProduct(product1);
        cpDecisionBAROutput6.setAccomType(accomType2);

        CPDecisionBAROutput cpDecisionBAROutput7 = new CPDecisionBAROutput();
        cpDecisionBAROutput7.setFinalBAR(new BigDecimal(189));
        cpDecisionBAROutput7.setArrivalDate(new LocalDate(2018, 8, 26));
        cpDecisionBAROutput7.setProduct(product1);
        cpDecisionBAROutput7.setAccomType(accomType1);

        CPDecisionBAROutput cpDecisionBAROutput8 = new CPDecisionBAROutput();
        cpDecisionBAROutput8.setFinalBAR(new BigDecimal(129));
        cpDecisionBAROutput8.setArrivalDate(new LocalDate(2018, 8, 26));
        cpDecisionBAROutput8.setProduct(product1);
        cpDecisionBAROutput8.setAccomType(accomType2);

        CPDecisionBAROutput cpDecisionBAROutput9 = new CPDecisionBAROutput();
        cpDecisionBAROutput9.setFinalBAR(new BigDecimal(195));
        cpDecisionBAROutput9.setArrivalDate(new LocalDate(2018, 8, 27));
        cpDecisionBAROutput9.setProduct(product1);
        cpDecisionBAROutput9.setAccomType(accomType1);

        CPDecisionBAROutput cpDecisionBAROutput10 = new CPDecisionBAROutput();
        cpDecisionBAROutput10.setFinalBAR(new BigDecimal(182));
        cpDecisionBAROutput10.setArrivalDate(new LocalDate(2018, 8, 27));
        cpDecisionBAROutput10.setProduct(product1);
        cpDecisionBAROutput10.setAccomType(accomType2);

        CPDecisionBAROutput cpDecisionBAROutput11 = new CPDecisionBAROutput();
        cpDecisionBAROutput11.setFinalBAR(new BigDecimal(118));
        cpDecisionBAROutput11.setArrivalDate(new LocalDate(2018, 8, 28));
        cpDecisionBAROutput11.setProduct(product1);
        cpDecisionBAROutput11.setAccomType(accomType1);

        CPDecisionBAROutput cpDecisionBAROutput12 = new CPDecisionBAROutput();
        cpDecisionBAROutput12.setFinalBAR(new BigDecimal(119));
        cpDecisionBAROutput12.setArrivalDate(new LocalDate(2018, 8, 28));
        cpDecisionBAROutput12.setProduct(product1);
        cpDecisionBAROutput12.setAccomType(accomType2);


        List<CPDecisionBAROutput> cpDecisionBAROutputList = new ArrayList<>(Arrays.asList(cpDecisionBAROutput1, cpDecisionBAROutput2, cpDecisionBAROutput3, cpDecisionBAROutput4,
                cpDecisionBAROutput5, cpDecisionBAROutput6, cpDecisionBAROutput7, cpDecisionBAROutput8, cpDecisionBAROutput9,
                cpDecisionBAROutput10, cpDecisionBAROutput11, cpDecisionBAROutput12));

        when(service.getCPDecisionsBetweenDatesForProductAndRoomTypes(any(), any(), any(), anyList())).thenReturn(cpDecisionBAROutputList);
        when(dateService.getCaughtUpLocalDate()).thenReturn(new LocalDate(2018, 8, 24));
        when(service.getMaxDecisionDate()).thenReturn(new LocalDate(2018, 8, 28));
        Map<LocalDate, List<CPDecisionBAROutput>> averagePriceMap = pricingOverrideManager.getAveragePriceDecisionMap(cpDecisionBAROutputList);

        List<CPDecisionBAROutput> cpDecisionBAROutputCheckListFor24 = averagePriceMap.get(new LocalDate(2018, 8, 24));
        assertEquals(accomType1, cpDecisionBAROutputCheckListFor24.get(0).getAccomType());
        assertEquals(product1, cpDecisionBAROutputCheckListFor24.get(0).getProduct());
        assertEquals(BigDecimal.valueOf(132.67).setScale(2), cpDecisionBAROutputCheckListFor24.get(0).getAveragePrice());

        assertEquals(accomType2, cpDecisionBAROutputCheckListFor24.get(1).getAccomType());
        assertEquals(product1, cpDecisionBAROutputCheckListFor24.get(1).getProduct());
        assertEquals(BigDecimal.valueOf(114.33).setScale(2), cpDecisionBAROutputCheckListFor24.get(1).getAveragePrice());
    }

    @Test
    public void testAveragePriceWhenOneRoomClassSelected() {
        AccomType accomType1 = new AccomType();
        accomType1.setId(1);

        AccomType accomType2 = new AccomType();
        accomType2.setId(2);

        Set<AccomType> accomTypes = new HashSet<>();
        accomTypes.add(accomType1);
        accomTypes.add(accomType2);

        Product product1 = new Product();
        product1.setId(2);
        product1.setType("INDEPENDENTLY");
        product1.setMinLOS(3);
        product1.setCode("INDEPENDENT");

        AccomClass accomClass = new AccomClass();
        accomClass.setName("STANDARD");
        accomClass.setAccomTypes(accomTypes);

        CPPricingFilter cpPricingFilter1 = new CPPricingFilter();
        cpPricingFilter1.setSelectedRoomClass(accomClass);
        cpPricingFilter1.setStartDate(new LocalDate(2018, 8, 23));
        cpPricingFilter1.setEndDate(new LocalDate(2018, 8, 30));
        cpPricingFilter1.setSelectedRoomTypes(new HashSet<>());
        pricingOverrideManager.setCpPricingFilter(cpPricingFilter1);

        CPDecisionBAROutput cpDecisionBAROutput1 = new CPDecisionBAROutput();
        cpDecisionBAROutput1.setFinalBAR(new BigDecimal(150));
        cpDecisionBAROutput1.setArrivalDate(new LocalDate(2018, 8, 23));
        cpDecisionBAROutput1.setProduct(product1);
        cpDecisionBAROutput1.setAccomType(accomType1);

        CPDecisionBAROutput cpDecisionBAROutput2 = new CPDecisionBAROutput();
        cpDecisionBAROutput2.setFinalBAR(new BigDecimal(160));
        cpDecisionBAROutput2.setArrivalDate(new LocalDate(2018, 8, 23));
        cpDecisionBAROutput2.setProduct(product1);
        cpDecisionBAROutput2.setAccomType(accomType2);

        CPDecisionBAROutput cpDecisionBAROutput3 = new CPDecisionBAROutput();
        cpDecisionBAROutput3.setFinalBAR(new BigDecimal(110));
        cpDecisionBAROutput3.setArrivalDate(new LocalDate(2018, 8, 24));
        cpDecisionBAROutput3.setProduct(product1);
        cpDecisionBAROutput3.setAccomType(accomType1);

        CPDecisionBAROutput cpDecisionBAROutput4 = new CPDecisionBAROutput();
        cpDecisionBAROutput4.setFinalBAR(new BigDecimal(125));
        cpDecisionBAROutput4.setArrivalDate(new LocalDate(2018, 8, 24));
        cpDecisionBAROutput4.setProduct(product1);
        cpDecisionBAROutput4.setAccomType(accomType2);

        CPDecisionBAROutput cpDecisionBAROutput5 = new CPDecisionBAROutput();
        cpDecisionBAROutput5.setFinalBAR(new BigDecimal(99));
        cpDecisionBAROutput5.setArrivalDate(new LocalDate(2018, 8, 25));
        cpDecisionBAROutput5.setProduct(product1);
        cpDecisionBAROutput5.setAccomType(accomType1);

        CPDecisionBAROutput cpDecisionBAROutput6 = new CPDecisionBAROutput();
        cpDecisionBAROutput6.setFinalBAR(new BigDecimal(89));
        cpDecisionBAROutput6.setArrivalDate(new LocalDate(2018, 8, 25));
        cpDecisionBAROutput6.setProduct(product1);
        cpDecisionBAROutput6.setAccomType(accomType2);

        CPDecisionBAROutput cpDecisionBAROutput7 = new CPDecisionBAROutput();
        cpDecisionBAROutput7.setFinalBAR(new BigDecimal(189));
        cpDecisionBAROutput7.setArrivalDate(new LocalDate(2018, 8, 26));
        cpDecisionBAROutput7.setProduct(product1);
        cpDecisionBAROutput7.setAccomType(accomType1);

        CPDecisionBAROutput cpDecisionBAROutput8 = new CPDecisionBAROutput();
        cpDecisionBAROutput8.setFinalBAR(new BigDecimal(129));
        cpDecisionBAROutput8.setArrivalDate(new LocalDate(2018, 8, 26));
        cpDecisionBAROutput8.setProduct(product1);
        cpDecisionBAROutput8.setAccomType(accomType2);

        CPDecisionBAROutput cpDecisionBAROutput9 = new CPDecisionBAROutput();
        cpDecisionBAROutput9.setFinalBAR(new BigDecimal(195));
        cpDecisionBAROutput9.setArrivalDate(new LocalDate(2018, 8, 27));
        cpDecisionBAROutput9.setProduct(product1);
        cpDecisionBAROutput9.setAccomType(accomType1);

        CPDecisionBAROutput cpDecisionBAROutput10 = new CPDecisionBAROutput();
        cpDecisionBAROutput10.setFinalBAR(new BigDecimal(182));
        cpDecisionBAROutput10.setArrivalDate(new LocalDate(2018, 8, 27));
        cpDecisionBAROutput10.setProduct(product1);
        cpDecisionBAROutput10.setAccomType(accomType2);

        CPDecisionBAROutput cpDecisionBAROutput11 = new CPDecisionBAROutput();
        cpDecisionBAROutput11.setFinalBAR(new BigDecimal(118));
        cpDecisionBAROutput11.setArrivalDate(new LocalDate(2018, 8, 28));
        cpDecisionBAROutput11.setProduct(product1);
        cpDecisionBAROutput11.setAccomType(accomType1);

        CPDecisionBAROutput cpDecisionBAROutput12 = new CPDecisionBAROutput();
        cpDecisionBAROutput12.setFinalBAR(new BigDecimal(119));
        cpDecisionBAROutput12.setArrivalDate(new LocalDate(2018, 8, 28));
        cpDecisionBAROutput12.setProduct(product1);
        cpDecisionBAROutput12.setAccomType(accomType2);


        List<CPDecisionBAROutput> cpDecisionBAROutputList = new ArrayList<>(Arrays.asList(cpDecisionBAROutput1, cpDecisionBAROutput2, cpDecisionBAROutput3, cpDecisionBAROutput4,
                cpDecisionBAROutput5, cpDecisionBAROutput6, cpDecisionBAROutput7, cpDecisionBAROutput8, cpDecisionBAROutput9,
                cpDecisionBAROutput10, cpDecisionBAROutput11, cpDecisionBAROutput12));

        when(service.getCPDecisionsBetweenDatesForProductAndRoomTypes(any(), any(), any(), anyList())).thenReturn(cpDecisionBAROutputList);
        when(dateService.getCaughtUpLocalDate()).thenReturn(new LocalDate(2018, 8, 24));
        when(service.getMaxDecisionDate()).thenReturn(new LocalDate(2018, 8, 28));
        Map<LocalDate, List<CPDecisionBAROutput>> averagePriceMap = pricingOverrideManager.getAveragePriceDecisionMap(cpDecisionBAROutputList);

        List<CPDecisionBAROutput> cpDecisionBAROutputCheckListFor25 = averagePriceMap.get(new LocalDate(2018, 8, 25));
        assertEquals(accomType1, cpDecisionBAROutputCheckListFor25.get(0).getAccomType());
        assertEquals(product1, cpDecisionBAROutputCheckListFor25.get(0).getProduct());
        assertEquals(BigDecimal.valueOf(161.00).setScale(2), cpDecisionBAROutputCheckListFor25.get(0).getAveragePrice());

        assertEquals(accomType2, cpDecisionBAROutputCheckListFor25.get(1).getAccomType());
        assertEquals(product1, cpDecisionBAROutputCheckListFor25.get(1).getProduct());
        assertEquals(BigDecimal.valueOf(133.33).setScale(2), cpDecisionBAROutputCheckListFor25.get(1).getAveragePrice());
    }

    private List<CPDecisionBAROutput> getCPDecisionBAROutputList() {
        Product bar = createBARProduct();
        //does not filter out
        CPDecisionBAROutput cpDecisionBAROutput1 = new CPDecisionBAROutput();
        cpDecisionBAROutput1.setFinalBAR(new BigDecimal(1));
        cpDecisionBAROutput1.setArrivalDate(new LocalDate(2018, 8, 23));
        cpDecisionBAROutput1.setFloorOverride(new BigDecimal(3));
        cpDecisionBAROutput1.setDecisionReasonTypeId(2);
        cpDecisionBAROutput1.setOverrideType(DecisionOverrideType.FLOOR);
        cpDecisionBAROutput1.setProduct(bar);
        //filter out by filterByDateWith
        CPDecisionBAROutput cpDecisionBAROutput2 = new CPDecisionBAROutput();
        cpDecisionBAROutput2.setFinalBAR(new BigDecimal(2));
        cpDecisionBAROutput2.setArrivalDate(new LocalDate(2018, 8, 23));
        cpDecisionBAROutput2.setFloorOverride(new BigDecimal(3));
        cpDecisionBAROutput2.setDecisionReasonTypeId(2);
        cpDecisionBAROutput2.setOverrideType(DecisionOverrideType.FLOOR);
        cpDecisionBAROutput2.setProduct(bar);
        //filter out by filterByDayOfWeek
        CPDecisionBAROutput cpDecisionBAROutput3 = new CPDecisionBAROutput();
        cpDecisionBAROutput3.setFinalBAR(new BigDecimal(1));
        cpDecisionBAROutput3.setArrivalDate(new LocalDate(2018, 8, 24));
        cpDecisionBAROutput3.setFloorOverride(new BigDecimal(3));
        cpDecisionBAROutput3.setDecisionReasonTypeId(2);
        cpDecisionBAROutput3.setOverrideType(DecisionOverrideType.FLOOR);
        cpDecisionBAROutput3.setProduct(bar);

        //filter out by filterByOverrides
        CPDecisionBAROutput cpDecisionBAROutput4 = new CPDecisionBAROutput();
        cpDecisionBAROutput4.setFinalBAR(new BigDecimal(1));
        cpDecisionBAROutput4.setArrivalDate(new LocalDate(2018, 8, 23));
        cpDecisionBAROutput4.setCeilingOverride(new BigDecimal(3));
        cpDecisionBAROutput4.setDecisionReasonTypeId(2);
        cpDecisionBAROutput4.setOverrideType(DecisionOverrideType.CEIL);
        cpDecisionBAROutput4.setProduct(bar);

        //filter out by filterByRestrictions
        CPDecisionBAROutput cpDecisionBAROutput5 = new CPDecisionBAROutput();
        cpDecisionBAROutput5.setFinalBAR(new BigDecimal(1));
        cpDecisionBAROutput5.setArrivalDate(new LocalDate(2018, 8, 23));
        cpDecisionBAROutput5.setFloorOverride(new BigDecimal(3));
        cpDecisionBAROutput5.setDecisionReasonTypeId(6);
        cpDecisionBAROutput5.setOverrideType(DecisionOverrideType.FLOOR);
        cpDecisionBAROutput5.setProduct(bar);


        List<CPDecisionBAROutput> cpDecisionBAROutputList = new ArrayList<>(Arrays.asList(cpDecisionBAROutput1, cpDecisionBAROutput2, cpDecisionBAROutput3, cpDecisionBAROutput4, cpDecisionBAROutput5));
        return cpDecisionBAROutputList;
    }

    private Product createBARProduct() {
        Product product = new Product();
        product.setId(1);
        product.setName("1");
        product.setCode(Product.BAR);
        product.setSystemDefault(true);
        product.setRateShoppingLOSMin(-1);
        return product;
    }

    private CPPricingFilter setUpForWrapDecisionTest() {
        final AccomClass accomClass = createAccomClass();
        AccomType accomType = createRoomType(1, accomClass);
        final CPPricingFilter filter = createFilter(accomClass, accomType);
        pricingOverrideManager.setCpPricingFilter(filter);

        when(service.getBaseAccomTypes()).thenReturn(Collections.singletonList(accomType));
        when(service.getCPDecisionsBetweenDatesForProductAndRoomTypes(filter.getProducts(), filter.getStartDate(), filter.getEndDate(), List.of(accomType)))
                .thenReturn(createCpDecisionBAROutputs(accomClass, filter, accomType));
        pricingOverrideManager.setBaseRoomTypeList(Collections.singletonList(accomType));
        return filter;
    }

    @Test
    public void fixTimeZone() {
        PricingOverrideManager spy = Mockito.spy(pricingOverrideManager);
        doReturn(-21600000).when(spy).getClientTimeZoneOffset();
        doReturn(-21600000).when(spy).getServerTimeZoneOffset();
        final Page currentPage = Mockito.mock(Page.getCurrent().getClass());
        final WebBrowser webBrowser = Mockito.mock(WebBrowser.class);
        when(currentPage.getWebBrowser()).thenReturn(webBrowser);
        when(webBrowser.getTimezoneOffset()).thenReturn(-21600000);

        LocalDate localDate = new LocalDate(2015, 1, 1);
        LocalDateTime localDateTime = LocalDateTime.of(localDate.getYear(), localDate.getMonthOfYear(), localDate.getDayOfMonth(), 10, 5, 30);

        Date result = spy.fixTimeZone(localDateTime);
        assertEquals(result, Timestamp.valueOf(localDateTime));

        doReturn(-21600000).when(spy).getClientTimeZoneOffset();
        doReturn(3600000).when(spy).getServerTimeZoneOffset();

        long expectedResult2 = Timestamp.valueOf(localDateTime).getTime() + 25200000;
        Date result2 = spy.fixTimeZone(localDateTime);
        assertEquals(new Timestamp(expectedResult2), result2);

        doReturn(3600000).when(spy).getClientTimeZoneOffset();
        doReturn(-21600000).when(spy).getServerTimeZoneOffset();

        long expectedResult3 = Timestamp.valueOf(localDateTime).getTime() - 25200000;
        Date result3 = spy.fixTimeZone(localDateTime);
        assertEquals(new Timestamp(expectedResult3), result3);
    }

    @Test
    public void filterByRestrictions() {
        CPDecisionBAROutput cpDecisionBAROutput1 = new CPDecisionBAROutput();
        cpDecisionBAROutput1.setDecisionReasonTypeId(2);
        CPDecisionBAROutput cpDecisionBAROutput2 = new CPDecisionBAROutput();
        cpDecisionBAROutput2.setDecisionReasonTypeId(6);
        CPDecisionBAROutput cpDecisionBAROutput3 = new CPDecisionBAROutput();
        CPDecisionBAROutput cpDecisionBAROutput4 = new CPDecisionBAROutput();
        cpDecisionBAROutput4.setDecisionReasonTypeId(19);
        List<CPDecisionBAROutput> cpDecisionBAROutputList = new ArrayList<>(Arrays.asList(cpDecisionBAROutput1, cpDecisionBAROutput2, cpDecisionBAROutput3, cpDecisionBAROutput4));

        CPPricingFilter cpPricingFilter1 = new CPPricingFilter();
        HashSet<DecisionReasonType> selectedRestrictions = new HashSet<>(asList(DecisionReasonType.LRV_GT_BAR, DecisionReasonType.SUBOPTIMAL_OPTIMAL_BAR_DUE_TO_LRA));
        cpPricingFilter1.setSelectedRestrictions(selectedRestrictions);
        pricingOverrideManager.setCpPricingFilter(cpPricingFilter1);

        List<CPDecisionBAROutput> results1 = pricingOverrideManager.filterByRestrictions(cpDecisionBAROutputList, selectedRestrictions);
        assertEquals(2, results1.size());
        assertFalse(results1.contains(cpDecisionBAROutput3));

        CPPricingFilter cpPricingFilter2 = new CPPricingFilter();
        HashSet<DecisionReasonType> selectedRestrictions1 = new HashSet<>(List.of(DecisionReasonType.LRV_GT_BAR));
        cpPricingFilter2.setSelectedRestrictions(selectedRestrictions1);
        pricingOverrideManager.setCpPricingFilter(cpPricingFilter2);

        List<CPDecisionBAROutput> results2 = pricingOverrideManager.filterByRestrictions(cpDecisionBAROutputList, selectedRestrictions1);
        assertEquals(1, results2.size());
        assertEquals(cpDecisionBAROutput1, results2.get(0));

        CPPricingFilter cpPricingFilter3 = new CPPricingFilter();
        HashSet<DecisionReasonType> selectedRestrictions2 = new HashSet<>(List.of(DecisionReasonType.SUBOPTIMAL_OPTIMAL_BAR_DUE_TO_LRA));
        cpPricingFilter3.setSelectedRestrictions(selectedRestrictions2);
        pricingOverrideManager.setCpPricingFilter(cpPricingFilter3);

        List<CPDecisionBAROutput> results3 = pricingOverrideManager.filterByRestrictions(cpDecisionBAROutputList, selectedRestrictions2);
        assertEquals(1, results3.size());
        assertEquals(cpDecisionBAROutput2, results3.get(0));

        CPPricingFilter cpPricingFilter4 = new CPPricingFilter();
        HashSet<DecisionReasonType> selectedRestrictions4 = new HashSet<>(asList(DecisionReasonType.BASE_PRODUCT_PRICE_BELOW_FAB_PRODUCTS_RATE,
                DecisionReasonType.SUBOPTIMAL_OPTIMAL_BAR_DUE_TO_LRA));
        cpPricingFilter4.setSelectedRestrictions(selectedRestrictions);
        pricingOverrideManager.setCpPricingFilter(cpPricingFilter4);

        List<CPDecisionBAROutput> results4 = pricingOverrideManager.filterByRestrictions(cpDecisionBAROutputList, selectedRestrictions4);
        assertEquals(2, results4.size());
        assertFalse(results4.contains(cpDecisionBAROutput1));
        assertTrue(results4.contains(cpDecisionBAROutput2));
        assertTrue(results4.contains(cpDecisionBAROutput4));
        assertFalse(results4.contains(cpDecisionBAROutput3));
    }

    @Test
    public void filterByRestrictions_forRateProtectRestriction() {
        CPDecisionBAROutput decisionForLRV = new CPDecisionBAROutput();
        decisionForLRV.setDecisionReasonTypeId(2);
        CPDecisionBAROutput decisionForLRA = new CPDecisionBAROutput();
        decisionForLRA.setDecisionReasonTypeId(6);
        CPDecisionBAROutput decision = new CPDecisionBAROutput();
        CPDecisionBAROutput decisionForFAB = new CPDecisionBAROutput();
        decisionForFAB.setDecisionReasonTypeId(19);
        List<CPDecisionBAROutput> cpDecisionBAROutputList = new ArrayList<>(Arrays.asList(decisionForLRV, decisionForLRA, decision, decisionForFAB));

        CPPricingFilter cpPricingFilter = new CPPricingFilter();
        HashSet<DecisionReasonType> fixedAboveBARRestriction = new HashSet<>(List.of(DecisionReasonType.BASE_PRODUCT_PRICE_BELOW_FAB_PRODUCTS_RATE));
        cpPricingFilter.setSelectedRestrictions(fixedAboveBARRestriction);
        pricingOverrideManager.setCpPricingFilter(cpPricingFilter);

        List<CPDecisionBAROutput> results = pricingOverrideManager.filterByRestrictions(cpDecisionBAROutputList, fixedAboveBARRestriction);
        assertEquals(1, results.size());
        assertTrue(results.contains(decisionForFAB));
        assertFalse(results.contains(decisionForLRA));
        assertFalse(results.contains(decisionForLRV));
    }

    @Test
    public void filterByRestrictions_forRateProtectAndOtherRestrictions() {
        CPDecisionBAROutput decisionForLRV = new CPDecisionBAROutput();
        decisionForLRV.setDecisionReasonTypeId(2);
        CPDecisionBAROutput decisionForLRA = new CPDecisionBAROutput();
        decisionForLRA.setDecisionReasonTypeId(6);
        CPDecisionBAROutput decision = new CPDecisionBAROutput();
        CPDecisionBAROutput decisionForFAB = new CPDecisionBAROutput();
        decisionForFAB.setDecisionReasonTypeId(19);
        List<CPDecisionBAROutput> cpDecisionBAROutputList = new ArrayList<>(Arrays.asList(decisionForLRV, decisionForLRA, decision, decisionForFAB));

        CPPricingFilter cpPricingFilter = new CPPricingFilter();
        HashSet<DecisionReasonType> fixedAboveBARRestriction = new HashSet<>(asList(DecisionReasonType.BASE_PRODUCT_PRICE_BELOW_FAB_PRODUCTS_RATE,
                DecisionReasonType.SUBOPTIMAL_OPTIMAL_BAR_DUE_TO_LRA, DecisionReasonType.LRV_GT_BAR));
        cpPricingFilter.setSelectedRestrictions(fixedAboveBARRestriction);
        pricingOverrideManager.setCpPricingFilter(cpPricingFilter);

        List<CPDecisionBAROutput> results = pricingOverrideManager.filterByRestrictions(cpDecisionBAROutputList, fixedAboveBARRestriction);
        assertEquals(3, results.size());
        assertTrue(results.contains(decisionForFAB));
        assertTrue(results.contains(decisionForLRA));
        assertTrue(results.contains(decisionForLRV));
    }

    @Test
    public void filterByRestrictions_forNoRateProtectRestriction() {
        CPDecisionBAROutput decisionForLRV = new CPDecisionBAROutput();
        decisionForLRV.setDecisionReasonTypeId(2);
        CPDecisionBAROutput decisionForLRA = new CPDecisionBAROutput();
        decisionForLRA.setDecisionReasonTypeId(6);
        CPDecisionBAROutput decision = new CPDecisionBAROutput();
        CPDecisionBAROutput decisionForFAB = new CPDecisionBAROutput();
        decisionForFAB.setDecisionReasonTypeId(19);
        List<CPDecisionBAROutput> cpDecisionBAROutputList = new ArrayList<>(Arrays.asList(decisionForLRV, decisionForLRA, decision, decisionForFAB));

        CPPricingFilter cpPricingFilter = new CPPricingFilter();
        HashSet<DecisionReasonType> LRVRestriction = new HashSet<>(List.of(DecisionReasonType.LRV_GT_BAR));
        cpPricingFilter.setSelectedRestrictions(LRVRestriction);
        pricingOverrideManager.setCpPricingFilter(cpPricingFilter);

        List<CPDecisionBAROutput> results = pricingOverrideManager.filterByRestrictions(cpDecisionBAROutputList, LRVRestriction);
        assertEquals(1, results.size());
        assertFalse(results.contains(decisionForFAB));
    }

    @Test
    public void getFinalPrice() {
        CPDecisionBAROutput cpDecisionBAROutput1 = new CPDecisionBAROutput();
        cpDecisionBAROutput1.setFinalBAR(new BigDecimal(365.74).setScale(2, RoundingMode.HALF_UP));

        CPDecisionBAROutput cpDecisionBAROutput2 = new CPDecisionBAROutput();

        CPDecisionBAROutput cpDecisionBAROutput3 = new CPDecisionBAROutput();
        cpDecisionBAROutput3.setFinalBAR(new BigDecimal(365.74).setScale(2, RoundingMode.HALF_UP));
        cpDecisionBAROutput3.setSpecificOverride(new BigDecimal(385.74).setScale(2, RoundingMode.HALF_UP));

        assertEquals(new BigDecimal(365.74).setScale(2, RoundingMode.HALF_UP), pricingOverrideManager.getFinalPrice(cpDecisionBAROutput1));
        assertEquals(new BigDecimal(0).setScale(2, RoundingMode.HALF_UP), pricingOverrideManager.getFinalPrice(cpDecisionBAROutput2));
        assertEquals(new BigDecimal(385.74).setScale(2, RoundingMode.HALF_UP), pricingOverrideManager.getFinalPrice(cpDecisionBAROutput3));
    }

    @Test
    public void isDisplayOccupancyForecastInWhatIfEnabled() {
        pricingOverrideManager.isDisplayOccupancyForecastInWhatIfEnabled();
        verify(simplifiedWhatIfService).isDisplayOccupancyForecastInWhatIfToggleEnabled();
    }

    @Test
    public void getSelectedAccomTypes() {
        AccomClass accomClass = createAllAccomClass();
        AccomType accomType = new AccomType();
        accomType.setId(1);
        accomType.setAccomTypeCapacity(1);
        accomType.setStatusId(Status.ACTIVE.getId());
        pricingOverrideManager.setBaseRoomTypeList(List.of(accomType));

        AccomClass accomClass2 = createAccomClass("test", 2);
        AccomType accomType2 = new AccomType();
        accomType2.setId(2);
        accomType2.setAccomTypeCapacity(1);
        accomType2.setStatusId(Status.ACTIVE.getId());
        Set<AccomType> accomTypes2 = new HashSet<>(List.of(accomType2));

        CPPricingFilter cpPricingFilter = new CPPricingFilter();
        cpPricingFilter.setSelectedRoomClass(accomClass);
        cpPricingFilter.setInventoryGroupDto(new InventoryGroupDto(-1, "Property", true));

        pricingOverrideManager.setCpPricingFilter(cpPricingFilter);

        AccomClass allRoomClass = new AccomClass();
        allRoomClass.setName(getText("all"));
        allRoomClass.setId(-1);

        List<AccomType> results = pricingOverrideManager.getSelectedAccomTypes();
        assertEquals(1, results.size());
        assertEquals(accomType, results.get(0));

        CPPricingFilter cpPricingFilter2 = new CPPricingFilter();
        cpPricingFilter2.setInventoryGroupDto(new InventoryGroupDto(-1, "Property", true));
        cpPricingFilter2.setSelectedRoomClass(accomClass2);
        cpPricingFilter2.setSelectedRoomTypes(accomTypes2);
        pricingOverrideManager.setCpPricingFilter(cpPricingFilter2);

        List<AccomType> results2 = pricingOverrideManager.getSelectedAccomTypes();
        assertEquals(1, results2.size());
        assertEquals(accomType2, results2.get(0));

        CPPricingFilter cpPricingFilter3 = new CPPricingFilter();
        cpPricingFilter3.setSelectedRoomClass(accomClass2);
        pricingOverrideManager.setCpPricingFilter(cpPricingFilter3);

        accomClass2.setAccomTypes(accomTypes2);

        List<AccomType> results3 = pricingOverrideManager.getSelectedAccomTypes();
        assertEquals(1, results3.size());
        assertEquals(accomType2, results3.get(0));

        // test if the passed accom-type filtration criteria gets honored

        CPPricingFilter cpPricingFilter4 = new CPPricingFilter();
        cpPricingFilter4.setSelectedRoomClass(accomClass2);
        pricingOverrideManager.setCpPricingFilter(cpPricingFilter4);

        accomClass2.setAccomTypes(new HashSet<>(Arrays.asList(accomType, accomType2)));

        pricingOverrideManager.setAccomTypeFiltrationCriteria((at) -> at.getId() == 1);
        List<AccomType> results4 = pricingOverrideManager.getSelectedAccomTypes();
        assertEquals(1, results4.size());
        assertEquals(accomType, results4.get(0));
    }

    @Test
    public void filterByDateWith_ChangesSinceLastBDE() {

        AccomClass accomClass = createAllAccomClass();
        AccomType accomType = new AccomType();
        accomType.setId(1);
        Set<AccomType> accomTypes = new HashSet<>(List.of(accomType));
        pricingOverrideManager.setBaseRoomTypeList(List.of(accomType));

        AccomType accomType2 = new AccomType();
        accomType2.setId(2);

        Product product = new Product();
        product.setId(1);

        LocalDate localDate = new LocalDate();
        CPDecisionBAROutput cpDecisionBAROutput1 = new CPDecisionBAROutput();
        cpDecisionBAROutput1.setFinalBAR(new BigDecimal(10));
        cpDecisionBAROutput1.setArrivalDate(localDate.plusDays(1));
        cpDecisionBAROutput1.setAccomType(accomType);
        cpDecisionBAROutput1.setProduct(product);
        CPDecisionBAROutput cpDecisionBAROutput2 = new CPDecisionBAROutput();
        cpDecisionBAROutput2.setFinalBAR(new BigDecimal(10));
        cpDecisionBAROutput2.setArrivalDate(localDate);
        cpDecisionBAROutput2.setAccomType(accomType2);
        cpDecisionBAROutput2.setProduct(product);
        CPDecisionBAROutput cpDecisionBAROutput3 = new CPDecisionBAROutput();
        cpDecisionBAROutput3.setFinalBAR(new BigDecimal(10));
        cpDecisionBAROutput3.setArrivalDate(localDate);
        cpDecisionBAROutput3.setAccomType(accomType);
        cpDecisionBAROutput3.setProduct(product);
        List<CPDecisionBAROutput> cpDecisionBAROutputList = new ArrayList<>(Arrays.asList(cpDecisionBAROutput1, cpDecisionBAROutput2, cpDecisionBAROutput3));

        CPPricingFilter cpPricingFilter = new CPPricingFilter();
        cpPricingFilter.setDateWithFilterEnum(DateWithFilterEnum.CHANGES_SINCE_LAST_BDE);
        cpPricingFilter.setSelectedRoomClass(accomClass);
        cpPricingFilter.setSelectedRoomTypes(accomTypes);
        cpPricingFilter.setProducts(new HashSet<>(List.of(product)));
        cpPricingFilter.setStartDate(localDate);
        cpPricingFilter.setEndDate(localDate);
        cpPricingFilter.setInventoryGroupDto(new InventoryGroupDto(-1, "Property", true));
        pricingOverrideManager.setCpPricingFilter(cpPricingFilter);

        CPPaceDecisionBAROutput paceDecisionBAROutput = new CPPaceDecisionBAROutput();
        paceDecisionBAROutput.setArrivalDate(localDate);
        paceDecisionBAROutput.setAccomType(accomType);
        paceDecisionBAROutput.setFinalBAR(new BigDecimal(11));
        paceDecisionBAROutput.setProduct(product);
        Mockito.when(service.getDecisionsFromLastBDE(any(), any(), any(), any())).thenReturn(List.of(paceDecisionBAROutput));

        List<CPDecisionBAROutput> results = pricingOverrideManager.filterByDateWith(cpDecisionBAROutputList);
        assertEquals(1, results.size());
        assertEquals(cpDecisionBAROutput3, results.get(0));
    }

    @Test
    public void filterByDateWith_ChangesSinceLastIDP() {

        AccomClass accomClass = createAllAccomClass();
        AccomType accomType = new AccomType();
        accomType.setId(1);
        Set<AccomType> accomTypes = new HashSet<>(List.of(accomType));
        pricingOverrideManager.setBaseRoomTypeList(List.of(accomType));

        AccomType accomType2 = new AccomType();
        accomType2.setId(2);

        Product product = new Product();
        product.setId(1);

        LocalDate localDate = new LocalDate();
        CPDecisionBAROutput cpDecisionBAROutput1 = new CPDecisionBAROutput();
        cpDecisionBAROutput1.setFinalBAR(new BigDecimal(10));
        cpDecisionBAROutput1.setArrivalDate(localDate.plusDays(1));
        cpDecisionBAROutput1.setAccomType(accomType);
        cpDecisionBAROutput1.setProduct(product);
        CPDecisionBAROutput cpDecisionBAROutput2 = new CPDecisionBAROutput();
        cpDecisionBAROutput2.setFinalBAR(new BigDecimal(10));
        cpDecisionBAROutput2.setArrivalDate(localDate);
        cpDecisionBAROutput2.setAccomType(accomType2);
        cpDecisionBAROutput2.setProduct(product);
        CPDecisionBAROutput cpDecisionBAROutput3 = new CPDecisionBAROutput();
        cpDecisionBAROutput3.setFinalBAR(new BigDecimal(10));
        cpDecisionBAROutput3.setArrivalDate(localDate);
        cpDecisionBAROutput3.setAccomType(accomType);
        cpDecisionBAROutput3.setProduct(product);
        List<CPDecisionBAROutput> cpDecisionBAROutputList = new ArrayList<>(Arrays.asList(cpDecisionBAROutput1, cpDecisionBAROutput2, cpDecisionBAROutput3));

        CPPricingFilter cpPricingFilter = new CPPricingFilter();
        cpPricingFilter.setDateWithFilterEnum(DateWithFilterEnum.CHANGES_SINCE_LAST_IDP);
        cpPricingFilter.setSelectedRoomClass(accomClass);
        cpPricingFilter.setSelectedRoomTypes(accomTypes);
        cpPricingFilter.setProducts(new HashSet<>(List.of(product)));
        cpPricingFilter.setStartDate(localDate);
        cpPricingFilter.setEndDate(localDate);
        cpPricingFilter.setInventoryGroupDto(new InventoryGroupDto(-1, "Property", true));
        pricingOverrideManager.setCpPricingFilter(cpPricingFilter);

        CPPaceDecisionBAROutput paceDecisionBAROutput = new CPPaceDecisionBAROutput();
        paceDecisionBAROutput.setArrivalDate(localDate);
        paceDecisionBAROutput.setAccomType(accomType);
        paceDecisionBAROutput.setFinalBAR(new BigDecimal(11));
        paceDecisionBAROutput.setProduct(product);
        Mockito.when(service.getDecisionsFromLastCDP(any(), any(), any(), any())).thenReturn(List.of(paceDecisionBAROutput));

        List<CPDecisionBAROutput> results = pricingOverrideManager.filterByDateWith(cpDecisionBAROutputList);
        assertEquals(1, results.size());
        assertEquals(cpDecisionBAROutput3, results.get(0));
    }

    @Test
    public void updateDecisions() {
        Product product = new Product();
        product.setId(1);

        Product product2 = new Product();
        product.setId(2);

        CPPricingFilter filter = new CPPricingFilter();
        filter.setProducts(new HashSet<>(List.of(product)));
        filter.setStartDate(new LocalDate());
        filter.setEndDate(filter.getStartDate().plusDays(1));

        AccomClass accomClass = createAccomClass();

        AccomType accomType = new AccomType();
        accomType.setId(1);
        accomType.setStatusId(Status.ACTIVE.getId());
        accomType.setAccomTypeCapacity(1);
        accomType.setAccomClass(accomClass);
        pricingOverrideManager.setBaseRoomTypeList(Collections.singletonList(accomType));

        AccomClass allAccomClass = new AccomClass();
        allAccomClass.setId(2);
        allAccomClass.setName("2");

        AccomType altAccomType = new AccomType();
        altAccomType.setId(2);
        altAccomType.setStatusId(Status.ACTIVE.getId());
        altAccomType.setAccomTypeCapacity(1);
        altAccomType.setAccomClass(allAccomClass);
        allAccomClass.setAccomTypes((Collections.singleton(altAccomType)));

        filter.setSelectedRoomClass(accomClass);
        filter.setSelectedRoomTypes(Collections.singleton(accomType));

        CPDecisionBAROutput output = new CPDecisionBAROutput();
        output.setAccomType(accomType);
        output.setProduct(product);

        when(service.getBaseAccomTypes()).thenReturn(Collections.singletonList(accomType));
        when(service.getCPDecisionsBetweenDatesForProductAndRoomTypes(any(), any(), any(), Mockito.anyList()))
                .thenReturn(Collections.singletonList(output));
        pricingOverrideManager.setCpPricingFilter(filter);

        //First case
        assertEquals(Collections.singletonList(output), pricingOverrideManager.getUpdatedDecisions(true));
        output.setAccomType(altAccomType);
        assertEquals(new ArrayList<>(), pricingOverrideManager.getUpdatedDecisions(true));

        //Second case
        pricingOverrideManager.setShowOnlyBaseRoomTypesFlag(true);
        output.setAccomType(accomType);
        assertEquals(Collections.singletonList(output), pricingOverrideManager.getUpdatedDecisions(true));
        output.setAccomType(altAccomType);
        assertEquals(new ArrayList<>(), pricingOverrideManager.getUpdatedDecisions(true));
        cpPricingFilter.setProducts(new HashSet<>(List.of(product2)));
        assertEquals(new ArrayList<>(), pricingOverrideManager.getUpdatedDecisions(true));
        cpPricingFilter.setProducts(new HashSet<>(List.of(product)));
        pricingOverrideManager.setBaseRoomTypeList(Collections.singletonList(altAccomType));
        assertEquals(new ArrayList<>(), pricingOverrideManager.getUpdatedDecisions(true));

        //Third case
        pricingOverrideManager.setBaseRoomTypeList(Collections.singletonList(accomType));
        filter.setSelectedRoomClass(allAccomClass);
        pricingOverrideManager.setShowOnlyBaseRoomTypesFlag(false);
        output.setAccomType(altAccomType);
        filter.setSelectedRoomTypes(new HashSet<>());
        assertEquals(Collections.singletonList(output), pricingOverrideManager.getUpdatedDecisions(true));
        output.setAccomType(accomType);
        assertEquals(new ArrayList<>(), pricingOverrideManager.getUpdatedDecisions(true));

        //Fourth case
        filter.setSelectedRoomTypes(Collections.singleton(altAccomType));
        assertEquals(new ArrayList<>(), pricingOverrideManager.getUpdatedDecisions(true));
        output.setAccomType(altAccomType);
        assertEquals(Collections.singletonList(output), pricingOverrideManager.getUpdatedDecisions(true));

        // Product group case
        cpPricingFilter.setProducts(new HashSet<>(List.of(product)));
        ProductGroup group = new ProductGroup();
        group.setProduct(product);
        when(agileRatesConfigurationService.getProductGroups()).thenReturn(List.of(group));
        when(agileRatesConfigurationService.getAllProductsInProductGroup(any(), any())).thenReturn(List.of(group));
        assertEquals(1, pricingOverrideManager.getUpdatedDecisions(true).size());
    }

    @Test
    public void getInvestigatorDtosByDate() {
        LocalDate startDate = new LocalDate();
        LocalDate endDate = startDate.plusDays(1);

        AccomType accomType = createRoomType(1, createAccomClass());

        CPDecisionBAROutput cpDecisionBAROutput = new CPDecisionBAROutput();
        cpDecisionBAROutput.setAccomType(accomType);
        cpDecisionBAROutput.setArrivalDate(startDate);

        CPBARDecisionDTO cpbarDecisionDTO = new CPBARDecisionDTO();
        cpbarDecisionDTO.setDate(startDate);
        cpbarDecisionDTO.setDecisions(Collections.singletonList(cpDecisionBAROutput));

        CPBARDecisionDTO cpbarDecisionDTO2 = new CPBARDecisionDTO();
        cpbarDecisionDTO2.setDate(endDate);
        cpbarDecisionDTO2.setDecisions(Collections.singletonList(cpDecisionBAROutput));

        CPBARDecisionUIWrapper uiWrapper = new CPBARDecisionUIWrapper(cpbarDecisionDTO, false);
        CPBARDecisionUIWrapper uiWrapper2 = new CPBARDecisionUIWrapper(cpbarDecisionDTO2, false);

        PricingAccomClass pricingAccomClass = new PricingAccomClass();
        pricingAccomClass.setAccomClass(accomType.getAccomClass());
        pricingAccomClass.setAccomType(accomType);

        pricingOverrideManager.setResults(new ArrayList<>());
        Map<LocalDate, List<InvestigatorDto>> investigatorDtosByDate1 = pricingOverrideManager.getInvestigatorDtosByDate(true, List.of(pricingAccomClass));
        assertEquals(0, investigatorDtosByDate1.size());

        pricingOverrideManager.setResults(Arrays.asList(uiWrapper, uiWrapper2));
        Map<LocalDate, List<InvestigatorDto>> investigatorDtosByDate2 = pricingOverrideManager.getInvestigatorDtosByDate(true, List.of(pricingAccomClass));
        assertEquals(2, investigatorDtosByDate2.size());
        InvestigatorDto investigatorDto1 = investigatorDtosByDate2.get(startDate).get(0);
        assertEquals(TetrisPermissionKey.PRICING, investigatorDto1.getModuleOrigin());
        assertTrue(investigatorDto1.isOverride());
        assertEquals(startDate, investigatorDto1.getSelectedDate());
        assertEquals(accomType.getId(), investigatorDto1.getSelectedAccomClassId());
        assertEquals(accomType.getAccomClass().getId(), investigatorDto1.getSelectedAccomTypeId());
        assertEquals(startDate, investigatorDto1.getStartDate());
        assertEquals(endDate, investigatorDto1.getEndDate());
        assertEquals(startDate, investigatorDto1.getWrapper().getStartDate());
        assertEquals(startDate, investigatorDto1.getWrapper().getEndDate());
        assertFalse(investigatorDto1.isBaseRoomType());
        assertFalse(investigatorDto1.isRoomClassPriceExcluded());

        InvestigatorDto investigatorDto2 = investigatorDtosByDate2.get(endDate).get(0);
        assertEquals(TetrisPermissionKey.PRICING, investigatorDto2.getModuleOrigin());
        assertTrue(investigatorDto2.isOverride());
        assertEquals(startDate, investigatorDto2.getSelectedDate());
        assertEquals(accomType.getId(), investigatorDto2.getSelectedAccomClassId());
        assertEquals(accomType.getAccomClass().getId(), investigatorDto2.getSelectedAccomTypeId());
        assertEquals(startDate, investigatorDto2.getStartDate());
        assertEquals(endDate, investigatorDto2.getEndDate());
        assertEquals(startDate, investigatorDto2.getWrapper().getStartDate());
        assertEquals(startDate, investigatorDto2.getWrapper().getEndDate());
        assertFalse(investigatorDto2.isBaseRoomType());
        assertFalse(investigatorDto2.isRoomClassPriceExcluded());
    }

    @Test
    public void isRoomClassPriceExcluded() {
        AccomClass accomClass1 = new AccomClass();
        accomClass1.setId(1);
        AccomClass accomClass2 = new AccomClass();
        accomClass2.setId(2);
        AccomClass accomClass3 = new AccomClass();
        accomClass3.setId(3);

        PricingAccomClass pricingAccomClass1 = new PricingAccomClass();
        pricingAccomClass1.setAccomClass(accomClass1);
        pricingAccomClass1.setPriceExcluded(true);

        PricingAccomClass pricingAccomClass2 = new PricingAccomClass();
        pricingAccomClass2.setAccomClass(accomClass2);
        pricingAccomClass2.setPriceExcluded(false);

        assertTrue(pricingOverrideManager.isRoomClassPriceExcluded(accomClass1, Arrays.asList(pricingAccomClass1, pricingAccomClass2)));
        assertFalse(pricingOverrideManager.isRoomClassPriceExcluded(accomClass2, Arrays.asList(pricingAccomClass1, pricingAccomClass2)));
        assertFalse(pricingOverrideManager.isRoomClassPriceExcluded(accomClass3, Arrays.asList(pricingAccomClass1, pricingAccomClass2)));
    }

    @Test
    public void revertChange() {
        Product product = new Product();
        product.setSystemDefault(false);
        product.setCode(Product.AGILE_RATES_PRODUCT_CODE);
        CPDecisionBAROutput cpDecisionBAROutput = new CPDecisionBAROutput();
        LocalDate localDate = new LocalDate();
        cpDecisionBAROutput.setProduct(product);
        cpDecisionBAROutput.setArrivalDate(localDate);
        cpDecisionBAROutput.setOverrideType(DecisionOverrideType.PENDING);
        cpDecisionBAROutput.setSpecificOverride(SPECIFIC_OVERRIDE);
        cpDecisionBAROutput.setCeilingOverride(CEILING_OVERRIDE);
        cpDecisionBAROutput.setFloorOverride(FLOOR_OVERRIDE);

        CPOverrideWrapper overrideWrapper = buildCpOverrideWrapperWithOverrides(cpDecisionBAROutput);
        overrideWrapper.setOriginalOverrideType(DecisionOverrideType.FLOORANDCEIL);

        HashMap<CPDecisionBAROutput, CPOverrideWrapper> overridesMap = new HashMap<>();
        overridesMap.put(cpDecisionBAROutput, overrideWrapper);

        pricingOverrideManager.setOverridesMap(overridesMap);

        //TODO test product overrides
        pricingOverrideManager.setProductOverrides(new ArrayList<>());

        pricingOverrideManager.revertChange(overrideWrapper);
        assertEquals(0, overridesMap.size());
        assertEquals(ORIGINAL_CEILING_OVERRIDE, overrideWrapper.getCeilingOverride());
        assertEquals(ORIGINAL_FLOOR_OVERRIDE, overrideWrapper.getFloorOverride());
        assertEquals(ORIGINAL_SPECIFIC_OVERRIDE, overrideWrapper.getSpecificOverride());
        assertEquals(ROUNDED_BAR, overrideWrapper.getRoundedBAR());
        assertFalse(overrideWrapper.isPendingSave());
        assertFalse(overrideWrapper.isPendingDelete());
        assertEquals(DecisionOverrideType.FLOORANDCEIL, overrideWrapper.getCpDecisionBAROutput().getOverrideType());
        assertEquals(ORIGINAL_CEILING_OVERRIDE, overrideWrapper.getCpDecisionBAROutput().getCeilingOverride());
        assertEquals(ORIGINAL_FLOOR_OVERRIDE, overrideWrapper.getCpDecisionBAROutput().getFloorOverride());
        assertEquals(ORIGINAL_SPECIFIC_OVERRIDE, overrideWrapper.getCpDecisionBAROutput().getSpecificOverride());
    }

    @Test
    public void revertChangesWhenGroupFloorIsPresent() {
        BigDecimal groupFloorOverride = new BigDecimal("180.00");
        BigDecimal originalGroupFloorOverride = new BigDecimal("170.00");

        Product product = new Product();
        product.setSystemDefault(false);
        product.setCode(Product.BAR);
        CPDecisionBAROutput cpDecisionBAROutput = new CPDecisionBAROutput();
        cpDecisionBAROutput.setProduct(product);
        LocalDate localDate = new LocalDate();
        cpDecisionBAROutput.setArrivalDate(localDate);
        cpDecisionBAROutput.setOverrideType(DecisionOverrideType.PENDING);
        cpDecisionBAROutput.setSpecificOverride(SPECIFIC_OVERRIDE);
        cpDecisionBAROutput.setCeilingOverride(CEILING_OVERRIDE);
        cpDecisionBAROutput.setFloorOverride(groupFloorOverride);

        CPOverrideWrapper overrideWrapper = buildCpOverrideWrapperWithOverrides(cpDecisionBAROutput);
        overrideWrapper.setFloorOverride(null);
        overrideWrapper.setOriginalFloorOverride(null);
        overrideWrapper.setGroupFloorOverride(FLOOR_OVERRIDE);
        overrideWrapper.setOriginalGroupFloorOverride(ORIGINAL_FLOOR_OVERRIDE);
        overrideWrapper.setOriginalOverrideType(DecisionOverrideType.GPFLOORANDCEIL);

        HashMap<CPDecisionBAROutput, CPOverrideWrapper> overridesMap = new HashMap<>();
        overridesMap.put(cpDecisionBAROutput, overrideWrapper);
        pricingOverrideManager.setOverridesMap(overridesMap);
        pricingOverrideManager.setProductOverrides(new ArrayList<>());

        pricingOverrideManager.revertChange(overrideWrapper);
        assertEquals(0, overridesMap.size());
        assertEquals(ORIGINAL_CEILING_OVERRIDE, overrideWrapper.getCeilingOverride());
        assertEquals(originalGroupFloorOverride, overrideWrapper.getGroupFloorOverride());
        assertEquals(ORIGINAL_SPECIFIC_OVERRIDE, overrideWrapper.getSpecificOverride());
        assertEquals(ROUNDED_BAR, overrideWrapper.getRoundedBAR());
        assertFalse(overrideWrapper.isPendingSave());
        assertFalse(overrideWrapper.isPendingDelete());
        assertEquals(DecisionOverrideType.GPFLOORANDCEIL, overrideWrapper.getCpDecisionBAROutput().getOverrideType());
        assertEquals(ORIGINAL_CEILING_OVERRIDE, overrideWrapper.getCpDecisionBAROutput().getCeilingOverride());
        assertEquals(originalGroupFloorOverride, overrideWrapper.getCpDecisionBAROutput().getFloorOverride());
        assertEquals(ORIGINAL_SPECIFIC_OVERRIDE, overrideWrapper.getCpDecisionBAROutput().getSpecificOverride());
    }

    @Test
    public void getSelectedAccomClassesBasedOnSearchCriteria() {
        CPPricingFilter cpPricingFilter = new CPPricingFilter();
        AccomClass accomClass = createAccomClass();
        AccomType accomType = createRoomType(1, createAccomClass());
        cpPricingFilter.setSelectedRoomTypes(new HashSet<>(Collections.singletonList(accomType)));
        AccomClass allAccomClass = new AccomClass();
        allAccomClass.setId(-1);
        pricingOverrideManager.setCpPricingFilter(cpPricingFilter);
        List<Integer> selectedAccomClassesBasedOnSearchCriteria = pricingOverrideManager.getSelectedAccomClassesBasedOnSearchCriteria(Arrays.asList(accomClass, allAccomClass));
        assertEquals(1, selectedAccomClassesBasedOnSearchCriteria.size());
        assertTrue(selectedAccomClassesBasedOnSearchCriteria.contains(1));

        cpPricingFilter.setSelectedRoomTypes(new HashSet<>(Collections.emptyList()));
        cpPricingFilter.setSelectedRoomClass(accomClass);
        List<Integer> selectedAccomClassesBasedOnSearchCriteria2 = pricingOverrideManager.getSelectedAccomClassesBasedOnSearchCriteria(Arrays.asList(accomClass, allAccomClass));
        assertEquals(1, selectedAccomClassesBasedOnSearchCriteria2.size());
        assertTrue(selectedAccomClassesBasedOnSearchCriteria2.contains(1));

        cpPricingFilter.setSelectedRoomClass(allAccomClass);
        when(service.getSortedAccomClasses(false)).thenReturn(Collections.singletonList(accomClass));
        List<Integer> selectedAccomClassesBasedOnSearchCriteria3 = pricingOverrideManager.getSelectedAccomClassesBasedOnSearchCriteria(Arrays.asList(accomClass, allAccomClass));
        assertEquals(1, selectedAccomClassesBasedOnSearchCriteria3.size());
        assertTrue(selectedAccomClassesBasedOnSearchCriteria3.contains(1));
    }

    @Test
    public void isRTSelected() {
        CPPricingFilter cpPricingFilter = new CPPricingFilter();
        assertFalse(pricingOverrideManager.isRTSelected(cpPricingFilter));

        cpPricingFilter.setSelectedRoomTypes(new HashSet<>(Collections.singletonList(createRoomType(1, createAccomClass()))));
        assertTrue(pricingOverrideManager.isRTSelected(cpPricingFilter));
    }


    @Test
    public void isSpecificRCSelected() {
        AccomClass accomClass = new AccomClass();
        accomClass.setId(-1);
        CPPricingFilter cpPricingFilter = new CPPricingFilter();
        cpPricingFilter.setSelectedRoomClass(accomClass);
        assertFalse(pricingOverrideManager.isSpecificRCSelected(cpPricingFilter));

        cpPricingFilter.setSelectedRoomClass(createAccomClass());
        assertTrue(pricingOverrideManager.isSpecificRCSelected(cpPricingFilter));
    }

    private CPOverrideWrapper buildCpOverrideWrapperWithOverrides(CPDecisionBAROutput cpDecisionBAROutput) {
        CPOverrideWrapper overrideWrapper = new CPOverrideWrapper(cpDecisionBAROutput, false);
        overrideWrapper.setIsBaseRoomType(true);
        overrideWrapper.setCeilingOverride(CEILING_OVERRIDE);
        overrideWrapper.setOriginalCeilingOverride(ORIGINAL_CEILING_OVERRIDE);
        overrideWrapper.setFloorOverride(FLOOR_OVERRIDE);
        overrideWrapper.setOriginalFloorOverride(ORIGINAL_FLOOR_OVERRIDE);
        overrideWrapper.setSpecificOverride(SPECIFIC_OVERRIDE);
        overrideWrapper.setOriginalSpecificOverride(ORIGINAL_SPECIFIC_OVERRIDE);
        overrideWrapper.setRoundedBAR(ROUNDED_BAR);
        overrideWrapper.setOriginalRoundedBAR(ORIGINAL_ROUNDED_BAR);
        overrideWrapper.setIsPendingDelete(true);
        overrideWrapper.setIsPendingSave(true);
        return overrideWrapper;
    }

    private AccomClass createAccomClass(String name, int id) {
        AccomClass masterClass = new AccomClass();
        masterClass.setId(id);
        masterClass.setName(name);
        return masterClass;
    }

    private List<CPDecisionBAROutput> createCpDecisionBAROutputs(AccomClass accomClass, CPPricingFilter filter, AccomType accomType) {
        CPDecisionBAROutput output1 = getCpDecisionBAROutput(accomType, filter.getProducts().iterator().next());
        CPDecisionBAROutput output2 = getCpDecisionBAROutput(createRoomType(2, accomClass), filter.getProducts().iterator().next());
        return Arrays.asList(output1, output2);
    }

    private void setUpBusinessAnalysisDailyDataDTO(CPPricingFilter filter) {
        BusinessAnalysisDailyDataDto dto = new BusinessAnalysisDailyDataDto();
        dto.setOccupancyForecastPerc(BigDecimal.TEN);
        dto.setCapacity(100L);
        dto.setOutOfOrder(50L);
        dto.setDate(createDateParameter());
        when(businessAnalysisDashboardService.getBusinessAnalysisDailyDataDtos(filter.getStartDate().toDate(), filter.getEndDate().toDate()))
                .thenReturn(Collections.singletonList(dto));
    }

    private DateParameter createDateParameter() {
        DateParameter date = new DateTimeParameter();
        date.setDate(LOCAL_DATE.getDayOfMonth());
        date.setMonth(LOCAL_DATE.getMonthOfYear() - 1);
        date.setYear(LOCAL_DATE.getYear());
        return date;
    }

    private void setUpBusinessAnalysisDailyIndicatorDto(CPPricingFilter filter) {
        BusinessAnalysisDailyIndicatorDto indicatorDto = new BusinessAnalysisDailyIndicatorDto();
        DateParameter date = createDateParameter();
        indicatorDto.setDate(date);
        indicatorDto.setSpecialEventImpactFCST(true);

        when(businessAnalysisDashboardService.getBusinessAnalysisSpecialEventDtos(filter.getStartDate().toDate()))
                .thenReturn(Collections.singletonList(createSpecialEvent("Special", date, date)));
        when(businessAnalysisDashboardService.getBusinessAnalysisDailyIndicatorDtos(filter.getStartDate().toDate(), filter.getEndDate().toDate()))
                .thenReturn(Collections.singletonList(indicatorDto));
    }

    private CPDecisionBAROutput getCpDecisionBAROutput(AccomType accomType, Product product) {
        CPDecisionBAROutput output = new CPDecisionBAROutput();
        output.setAccomType(accomType);
        output.setProduct(product);
        output.setArrivalDate(LOCAL_DATE);
        return output;
    }

    private CPPricingFilter createFilter(AccomClass accomClass, AccomType accomType) {
        CPPricingFilter filter = new CPPricingFilter();
        filter.setStartDate(LOCAL_DATE);
        filter.setEndDate(LOCAL_DATE);
        filter.setProducts(createDefaultProduct());
        filter.setSelectedRoomClass(accomClass);
        Set<AccomType> accomTypes = new HashSet<>();
        accomTypes.add(accomType);
        filter.setSelectedRoomTypes(accomTypes);
        filter.setDateWithFilterEnum(DateWithFilterEnum.ALL_DECISIONS);
        return filter;
    }

    private AccomType createRoomType(int id, AccomClass accomClass) {
        AccomType accomType = new AccomType();
        accomType.setId(id);
        accomType.setAccomClass(accomClass);
        return accomType;
    }

    private AccomClass createAccomClass() {
        AccomClass accomClass = new AccomClass();
        accomClass.setId(1);
        accomClass.setName("1");
        return accomClass;
    }

    private Set<Product> createDefaultProduct() {
        Product product = createBARProduct();
        return new HashSet<>(List.of(product));
    }

    private Set<Product> createAgileRateProduct() {
        Product product = new Product();
        product.setId(2);
        product.setName("2");
        product.setSystemDefault(false);
        product.setCode(Product.AGILE_RATES_PRODUCT_CODE);
        return new HashSet<>(List.of(product));
    }

    private AccomClass createAllAccomClass() {
        AccomClass allAccomClass = new AccomClass();
        allAccomClass.setId(-1);
        allAccomClass.setName("all");
        return allAccomClass;
    }

    private BusinessAnalysisSpecialEventDto createSpecialEvent(String eventName, DateParameter startDate, DateParameter endDate) {
        BusinessAnalysisSpecialEventDto se = new BusinessAnalysisSpecialEventDto();
        se.setEventName(eventName);
        se.setStartDate(startDate);
        se.setEndDate(endDate);
        return se;
    }


    @Test
    public void testApplyRemove_BaseRoomType() {
        Product product = new Product();
        CPDecisionBAROutput cpDecisionBAROutput = new CPDecisionBAROutput();
        cpDecisionBAROutput.setProduct(product);
        LocalDate localDate = new LocalDate();
        cpDecisionBAROutput.setArrivalDate(localDate);
        int accomTypeId = 1;
        AccomClass accomClass = new AccomClass();
        AccomType accomType = new AccomType();
        accomType.setAccomClass(accomClass);
        accomType.setId(accomTypeId);
        accomClass.setAccomTypes(Arrays.asList(accomType).stream().collect(Collectors.toSet()));
        cpDecisionBAROutput.setAccomType(accomType);

        CPOverrideWrapper overrideWrapper = new CPOverrideWrapper(cpDecisionBAROutput, false);
        overrideWrapper.setIsBaseRoomType(true);
        BigDecimal ceilingOverride = new BigDecimal("200.00");
        overrideWrapper.setCeilingOverride(ceilingOverride);
        overrideWrapper.setApplyOverrideAcrossRoomTypes(false);
        overrideWrapper.setIsOverridePersisted(true);

        BigDecimal prettyPrice = new BigDecimal("350.74");
        when(pricingConfigurationService.getCPDecisionContext(localDate, localDate, false)).thenReturn(cpDecisionContext);
        when(cpDecisionContext.calculateRoundedRate(cpDecisionBAROutput)).thenReturn(prettyPrice);

        CPBARDecisionDTO cpbarDecisionDTO = new CPBARDecisionDTO();
        List<CPDecisionBAROutput> decisions = new ArrayList<>();
        CPDecisionBAROutput cpDecisionBAROutputForNonBase = new CPDecisionBAROutput();
        cpDecisionBAROutputForNonBase.setProduct(product);
        int nonBaseAccomTypeId = 2;
        AccomType nonBaseAccomType = new AccomType();
        nonBaseAccomType.setId(nonBaseAccomTypeId);
        cpDecisionBAROutputForNonBase.setAccomType(nonBaseAccomType);
        cpDecisionBAROutputForNonBase.setArrivalDate(localDate);
        decisions.add(cpDecisionBAROutputForNonBase);
        cpbarDecisionDTO.setDecisions(decisions);

        CPBARDecisionUIWrapper cpbarDecisionUIWrapper = new CPBARDecisionUIWrapper(cpbarDecisionDTO, false);
        overrideWrapper.setParent(cpbarDecisionUIWrapper);
        when(service.searchForBarDecisionDTO(anyBoolean(), any(), anyBoolean(), any(), any())).thenReturn(List.of(cpbarDecisionDTO));
        pricingOverrideManager.setAccomTypeFiltrationCriteria(item -> true);
        BigDecimal nonBaseRTSupplementValue = new BigDecimal("10.00");
        when(supplementService.getSupplementValue(1, localDate, nonBaseAccomTypeId)).thenReturn(nonBaseRTSupplementValue);
        Optional<AccomTypeSupplementValue> supplement = Optional.of(new AccomTypeSupplementValue());
        when(cpDecisionContext.getSupplementFor(any(CPDecisionBAROutput.class))).thenReturn(supplement);

        pricingOverrideManager.setOverridesMap(overridesMap);

        List<PricingAccomClass> pricingAccomClasses = new ArrayList<>();
        PricingAccomClass pricingAccomClass = new PricingAccomClass();
        pricingAccomClass.setAccomClass(accomClass);
        pricingAccomClass.setAccomType(accomType);
        pricingAccomClasses.add(pricingAccomClass);
        pricingOverrideManager.removeOverrides(overrideWrapper, pricingAccomClasses, null, null, false);

        verify(overridesMap).put(cpDecisionBAROutput, overrideWrapper);
        verify(pricingConfigurationService).getCPDecisionContext(localDate, localDate, false);
        verify(cpDecisionContext).calculateRoundedRate(cpDecisionBAROutputForNonBase);
    }

    @Test
    public void verifyFindWrapper() {
        LocalDate startDate = new LocalDate();
        AccomType accomType = new AccomType();
        accomType.setId(1);

        CPDecisionBAROutput cpDecisionBAROutput = new CPDecisionBAROutput();
        cpDecisionBAROutput.setArrivalDate(startDate);
        cpDecisionBAROutput.setAccomType(accomType);
        Product product = new Product();
        cpDecisionBAROutput.setProduct(product);

        CPBARDecisionDTO cpbarDecisionDTO = new CPBARDecisionDTO();
        cpbarDecisionDTO.setDate(startDate);
        cpbarDecisionDTO.setDecisions(Collections.singletonList(cpDecisionBAROutput));
        CPBARDecisionUIWrapper uiWrapper = new CPBARDecisionUIWrapper(cpbarDecisionDTO, false);
        pricingOverrideManager.setResults(Collections.singletonList(uiWrapper));

        CPOverrideWrapper overrideWrapper = buildCPOverrideWrapper();
        overrideWrapper.setSpecificOverride(BigDecimal.TEN);
        overrideWrapper.setGroupFloorOverride(BigDecimal.TEN);
        overrideWrapper.getCpDecisionBAROutput().setProduct(product);

        pricingOverrideManager.findWrapper(overrideWrapper);

        final CPOverrideWrapper cpOverrideWrapper = uiWrapper.getCpOverrideWrappers().get(0);
        assertTrue(cpOverrideWrapper.isPendingSave());
        assertEquals(BigDecimal.TEN, cpOverrideWrapper.getSpecificOverride());
        assertEquals(BigDecimal.TEN, cpOverrideWrapper.getGroupFloorOverride());
    }


    @Test
    public void getAllProductRateOffsetOverrides() {
        CPPricingFilter cpPricingFilter = new CPPricingFilter();
        Product product = new Product();
        product.setId(1);
        cpPricingFilter.setProducts(new HashSet<>(List.of(product)));
        LocalDate localDate = new LocalDate();
        cpPricingFilter.setStartDate(localDate);
        cpPricingFilter.setEndDate(localDate);
        pricingOverrideManager.setCpPricingFilter(cpPricingFilter);

        List<ProductRateOffsetOverride> overrides = new ArrayList<>();
        ProductRateOffsetOverride productRateOffsetOverride = new ProductRateOffsetOverride();
        overrides.add(productRateOffsetOverride);
        when(service.getProductRateOffsetOverrides(cpPricingFilter.getProducts(), cpPricingFilter.getStartDate(), cpPricingFilter.getEndDate())).thenReturn(overrides);

        //if not null, return list and do not make service call
        pricingOverrideManager.setAllProductRateOffsetOverrides(new HashSet<>(overrides));
        Set<ProductRateOffsetOverride> overridesSet = new HashSet<>();
        overridesSet.add(productRateOffsetOverride);
        assertEquals(overridesSet, pricingOverrideManager.getAllProductRateOffsetOverrides());
        verify(service, never()).getProductRateOffsetOverrides(cpPricingFilter.getProducts(), cpPricingFilter.getStartDate(), cpPricingFilter.getEndDate());

        //if null, make service call
        pricingOverrideManager.setAllProductRateOffsetOverrides(null);
        assertEquals(overridesSet, pricingOverrideManager.getAllProductRateOffsetOverrides());
        verify(service, times(1)).getProductRateOffsetOverrides(cpPricingFilter.getProducts(), cpPricingFilter.getStartDate(), cpPricingFilter.getEndDate());
    }

    @Test
    public void showInvestigator() {
        when(uiContext.getPropertyId()).thenReturn(5);
        when(propertyService.isStageAtLeast(Stage.ONE_WAY, 5)).thenReturn(true);
        assertTrue(pricingOverrideManager.showInvestigator());

        when(propertyService.isStageAtLeast(Stage.ONE_WAY, 5)).thenReturn(false);
        assertFalse(pricingOverrideManager.showInvestigator());
    }

    @Test
    public void getPricingAccomClasses() {
        pricingOverrideManager.getPricingAccomClasses();
        verify(pricingConfigurationService).getPricingAccomClasses();
    }

    @Test
    public void applyOverrideSpecificOverride() {
        CPOverrideWrapper overrideWrapper = buildCPOverrideWrapper();
        overrideWrapper.setSpecificOverride(BigDecimal.TEN);

        when(pricingOverrideManager.getPendingBAROverrides().get(overrideWrapper.getCpDecisionBAROutput())).thenReturn(overrideWrapper);
        pricingOverrideManager.applyOverride(overrideWrapper, null);

        // Verify the wrapper conditions
        assertFalse(overrideWrapper.isPendingDelete());
        assertTrue(overrideWrapper.isPendingSave());
        assertEquals(pricingOverrideManager.getPendingBAROverrides().get(overrideWrapper.getCpDecisionBAROutput()), overrideWrapper);
        assertEquals(BigDecimal.TEN, overrideWrapper.getRoundedBAR());

        // Verify the CPDecisionBarOutput has the override
        assertEquals(DecisionOverrideType.USER, overrideWrapper.getCpDecisionBAROutput().getOverrideType());
        assertEquals(BigDecimal.TEN, overrideWrapper.getCpDecisionBAROutput().getSpecificOverride());
    }

    @Test
    public void applyOverrideFloorOverride() {
        CPOverrideWrapper overrideWrapper = buildCPOverrideWrapper();
        overrideWrapper.setFloorOverride(BigDecimal.TEN);

        when(pricingConfigurationService.getCPDecisionContext(overrideWrapper.getStartDate(), overrideWrapper.getEndDate())).thenReturn(cpDecisionContext);
        when(cpDecisionContext.calculateRoundedRate(overrideWrapper.getCpDecisionBAROutput())).thenReturn(BigDecimal.TEN);
        when(pricingOverrideManager.getPendingBAROverrides().get(overrideWrapper.getCpDecisionBAROutput())).thenReturn(overrideWrapper);

        pricingOverrideManager.applyOverride(overrideWrapper, null);

        // Verify the wrapper conditions
        assertFalse(overrideWrapper.isPendingDelete());
        assertTrue(overrideWrapper.isPendingSave());
        assertEquals(pricingOverrideManager.getPendingBAROverrides().get(overrideWrapper.getCpDecisionBAROutput()), overrideWrapper);
        assertEquals(BigDecimal.TEN, overrideWrapper.getFloorOverride());

        // Verify the CPDecisionBarOutput has the override
        assertEquals(DecisionOverrideType.FLOOR, overrideWrapper.getCpDecisionBAROutput().getOverrideType());
        assertEquals(BigDecimal.TEN, overrideWrapper.getCpDecisionBAROutput().getFloorOverride());
    }

    @Test
    public void applySpecificOverrideWithDifferentSupplementValues() {
        CPOverrideWrapper overrideWrapper = createWrapperForBaseAndNonBaseRoomTypes(BigDecimal.TEN, true);
        when(uiContext.getSystemCaughtUpDate()).thenReturn(new Date());
        when(pricingConfigurationService.getCPDecisionContext(overrideWrapper.getStartDate(), overrideWrapper.getEndDate())).thenReturn(cpDecisionContext);
        CPDecisionBAROutput baseCPDecisionBAROutput = overrideWrapper.getCpDecisionBAROutput();
        CPDecisionBAROutput nonBaseCPDecisionBAROutput = overrideWrapper.getNonBaseRoomTypeOverrideWrappers().get(0).getCpDecisionBAROutput();
        when(cpDecisionContext.applyOffset(nonBaseCPDecisionBAROutput, BigDecimal.TEN)).thenReturn(BigDecimal.valueOf(15.0));
        when(cpDecisionContext.getSupplement(baseCPDecisionBAROutput)).thenReturn(BigDecimal.ONE);
        when(cpDecisionContext.getSupplement(nonBaseCPDecisionBAROutput)).thenReturn(BigDecimal.TEN);
        when(cpDecisionContext.getPrimaryProduct(baseCPDecisionBAROutput.getProduct())).thenReturn(baseCPDecisionBAROutput.getProduct());
        when(cpDecisionContext.calculatePrettyPrice(1, BigDecimal.valueOf(24.00))).thenReturn(BigDecimal.valueOf(24.00));
        pricingOverrideManager.setShowOnlyBaseRoomTypesFlag(true);

        pricingOverrideManager.applyOverride(overrideWrapper, null);

        verifySpecficOverrideValuesFor(overrideWrapper, BigDecimal.TEN);
        verifySpecficOverrideValuesFor(overrideWrapper.getNonBaseRoomTypeOverrideWrappers().get(0), BigDecimal.valueOf(24.00));
        verifyMockInteractions(baseCPDecisionBAROutput, nonBaseCPDecisionBAROutput, 1, BigDecimal.valueOf(24.00), BigDecimal.TEN);
    }

    @Test
    public void applySpecificOverrideWithDifferentSupplementValuesAdjustSupplementsOnlyWhenNonBaseSupplementIsMoreThanBaseSupplement() {
        CPOverrideWrapper overrideWrapper = createWrapperForBaseAndNonBaseRoomTypes(BigDecimal.valueOf(15.0), true);
        when(uiContext.getSystemCaughtUpDate()).thenReturn(new Date());
        when(pricingConfigurationService.getCPDecisionContext(overrideWrapper.getStartDate(), overrideWrapper.getEndDate())).thenReturn(cpDecisionContext);
        CPDecisionBAROutput baseCPDecisionBAROutput = overrideWrapper.getCpDecisionBAROutput();
        CPDecisionBAROutput nonBaseCPDecisionBAROutput = overrideWrapper.getNonBaseRoomTypeOverrideWrappers().get(0).getCpDecisionBAROutput();
        when(cpDecisionContext.applyOffset(nonBaseCPDecisionBAROutput, BigDecimal.valueOf(15.0))).thenReturn(BigDecimal.valueOf(20.0));
        when(cpDecisionContext.getSupplement(baseCPDecisionBAROutput)).thenReturn(BigDecimal.TEN);
        when(cpDecisionContext.getSupplement(nonBaseCPDecisionBAROutput)).thenReturn(BigDecimal.ONE);
        when(cpDecisionContext.getPrimaryProduct(baseCPDecisionBAROutput.getProduct())).thenReturn(baseCPDecisionBAROutput.getProduct());
        when(cpDecisionContext.calculatePrettyPrice(1, BigDecimal.valueOf(20.00))).thenReturn(BigDecimal.valueOf(20.00));
        pricingOverrideManager.setShowOnlyBaseRoomTypesFlag(true);

        pricingOverrideManager.applyOverride(overrideWrapper, null);

        verifySpecficOverrideValuesFor(overrideWrapper, BigDecimal.valueOf(15.0));
        verifySpecficOverrideValuesFor(overrideWrapper.getNonBaseRoomTypeOverrideWrappers().get(0), BigDecimal.valueOf(20.00));
        verifyMockInteractions(baseCPDecisionBAROutput, nonBaseCPDecisionBAROutput, 1, BigDecimal.valueOf(20.00), BigDecimal.valueOf(15.00));
    }

    @Test
    public void applySpecificOverrideWithDifferentSupplementValuesWhenShowBaseRoomTypeOnlyIsFalseAndApplySpecificOverrideAcrossRoomTypesIsTrue() {
        CPOverrideWrapper overrideWrapper = createWrapperForBaseAndNonBaseRoomTypes(BigDecimal.TEN, true);
        when(pricingConfigurationService.getCPDecisionContext(overrideWrapper.getStartDate(), overrideWrapper.getEndDate())).thenReturn(cpDecisionContext);
        Product product = new Product();
        product.setId(1);
        product.setSystemDefault(true);
        CPDecisionBAROutput baseCPDecisionBAROutput = overrideWrapper.getCpDecisionBAROutput();
        baseCPDecisionBAROutput.setProduct(product);
        CPDecisionBAROutput nonBaseCPDecisionBAROutput = overrideWrapper.getNonBaseRoomTypeOverrideWrappers().get(0).getCpDecisionBAROutput();
        nonBaseCPDecisionBAROutput.setProduct(product);
        when(cpDecisionContext.applyOffset(nonBaseCPDecisionBAROutput, BigDecimal.TEN)).thenReturn(BigDecimal.valueOf(15.0));
        when(cpDecisionContext.getSupplement(baseCPDecisionBAROutput)).thenReturn(BigDecimal.ONE);
        when(cpDecisionContext.getSupplement(nonBaseCPDecisionBAROutput)).thenReturn(BigDecimal.TEN);
        when(cpDecisionContext.getPrimaryProduct(baseCPDecisionBAROutput.getProduct())).thenReturn(baseCPDecisionBAROutput.getProduct());
        when(cpDecisionContext.calculatePrettyPrice(product.getId(), BigDecimal.valueOf(24.00))).thenReturn(BigDecimal.valueOf(24.00));
        pricingOverrideManager.setShowOnlyBaseRoomTypesFlag(false);

        pricingOverrideManager.applyOverride(overrideWrapper, null);

        verifySpecficOverrideValuesFor(overrideWrapper, BigDecimal.TEN);
        verifySpecficOverrideValuesFor(overrideWrapper.getNonBaseRoomTypeOverrideWrappers().get(0), BigDecimal.valueOf(24.00));
    }

    private void verifySpecficOverrideValuesFor(CPOverrideWrapper overrideWrapper, BigDecimal specificOverrideValue) {
        assertEquals(specificOverrideValue, overrideWrapper.getSpecificOverride());
        assertEquals(specificOverrideValue, overrideWrapper.getCpDecisionBAROutput().getSpecificOverride());
        assertEquals(DecisionOverrideType.USER, overrideWrapper.getCpDecisionBAROutput().getOverrideType());
        assertNull(overrideWrapper.getCpDecisionBAROutput().getFloorOverride());
        assertNull(overrideWrapper.getCpDecisionBAROutput().getCeilingOverride());
    }

    @Test
    public void applySpecificOverrideWithDifferentSupplementValuesWhenShowBaseRoomTypeOnlyIsFalseAndApplySpecificOverrideAcrossRoomTypesIsAlsoFalse() {
        CPOverrideWrapper overrideWrapper = createWrapperForBaseAndNonBaseRoomTypes(BigDecimal.TEN, false);
        when(pricingConfigurationService.getCPDecisionContext(overrideWrapper.getStartDate(), overrideWrapper.getEndDate())).thenReturn(cpDecisionContext);
        CPDecisionBAROutput baseCPDecisionBAROutput = overrideWrapper.getCpDecisionBAROutput();
        CPDecisionBAROutput nonBaseCPDecisionBAROutput = overrideWrapper.getNonBaseRoomTypeOverrideWrappers().get(0).getCpDecisionBAROutput();
        when(cpDecisionContext.calculateRoundedRate(baseCPDecisionBAROutput)).thenReturn(BigDecimal.TEN);
        when(cpDecisionContext.applyOffset(nonBaseCPDecisionBAROutput, BigDecimal.TEN)).thenReturn(BigDecimal.valueOf(15.0));
        when(cpDecisionContext.getSupplement(baseCPDecisionBAROutput)).thenReturn(BigDecimal.ONE);
        when(cpDecisionContext.getSupplement(nonBaseCPDecisionBAROutput)).thenReturn(BigDecimal.TEN);
        when(cpDecisionContext.calculatePrettyPrice(1, BigDecimal.valueOf(24.00))).thenReturn(BigDecimal.valueOf(24.00));
        pricingOverrideManager.setShowOnlyBaseRoomTypesFlag(false);

        pricingOverrideManager.applyOverride(overrideWrapper, null);

        // Verify the wrapper conditions
        verifySpecficOverrideValuesFor(overrideWrapper, BigDecimal.TEN);

        //verify non-base roomType override values
        assertNull(overrideWrapper.getNonBaseRoomTypeOverrideWrappers().get(0).getSpecificOverride());
        assertNull(overrideWrapper.getNonBaseRoomTypeOverrideWrappers().get(0).getSpecificOverride());
        verifyMockInteractions(baseCPDecisionBAROutput, nonBaseCPDecisionBAROutput, 0, BigDecimal.valueOf(20.00), BigDecimal.TEN);
    }

    private void verifyMockInteractions(CPDecisionBAROutput baseCPDecisionBAROutput, CPDecisionBAROutput nonBaseCPDecisionBAROutput, int wantedNumberOfInvocations, BigDecimal expectedFinalValue, BigDecimal specificOverrideForBase) {
        verify(cpDecisionContext, times(wantedNumberOfInvocations)).calculatePrettyPrice(baseCPDecisionBAROutput.getProduct().getId(), expectedFinalValue);
        verify(cpDecisionContext, times(wantedNumberOfInvocations)).applyOffset(nonBaseCPDecisionBAROutput, specificOverrideForBase);
        verify(cpDecisionContext, times(wantedNumberOfInvocations)).getSupplement(baseCPDecisionBAROutput);
        verify(cpDecisionContext, times(wantedNumberOfInvocations)).getSupplement(nonBaseCPDecisionBAROutput);
    }

    private CPOverrideWrapper createWrapperForBaseAndNonBaseRoomTypes(BigDecimal newSpecificOverride, boolean applyOverrideAcrossRoomTypes) {
        CPOverrideWrapper overrideWrapper = buildCPOverrideWrapper();
        overrideWrapper.setIsBaseRoomType(true);
        overrideWrapper.setSpecificOverride(newSpecificOverride);
        overrideWrapper.setApplyOverrideAcrossRoomTypes(applyOverrideAcrossRoomTypes);
        CPOverrideWrapper nonBaseCPOverrideWrapper = buildCPOverrideWrapper();
        nonBaseCPOverrideWrapper.getCpDecisionBAROutput().getAccomType().setId(2);
        CPBARDecisionDTO cpbarDecisionDTO = new CPBARDecisionDTO();
        cpbarDecisionDTO.setDecisions(Arrays.asList(overrideWrapper.getCpDecisionBAROutput(), nonBaseCPOverrideWrapper.getCpDecisionBAROutput()));
        CPBARDecisionUIWrapper cpBARDecisionUIWrapper = new CPBARDecisionUIWrapper(cpbarDecisionDTO, false);
        cpBARDecisionUIWrapper.getCpOverrideWrappers().add(nonBaseCPOverrideWrapper);
        overrideWrapper.setParent(cpBARDecisionUIWrapper);
        overrideWrapper.setNonBaseRoomTypeOverrideWrappers(Collections.singletonList(nonBaseCPOverrideWrapper));
        CPOverrideWrapper nonBaseRTCPOverrideWrapper = cpBARDecisionUIWrapper.getCpOverrideWrappers().get(1);
        nonBaseRTCPOverrideWrapper.setSpecificOverride(BigDecimal.ONE);
        return overrideWrapper;
    }


    @Test
    public void applyGroupFloorAndCeilingOverride() {
        CPOverrideWrapper overrideWrapper = buildCPOverrideWrapper();
        overrideWrapper.setGroupFloorOverride(new BigDecimal("100.0"));
        overrideWrapper.setCeilingOverride(new BigDecimal("200.0"));

        when(pricingConfigurationService.getCPDecisionContext(overrideWrapper.getStartDate(), overrideWrapper.getEndDate())).thenReturn(cpDecisionContext);
        when(cpDecisionContext.calculateRoundedRate(overrideWrapper.getCpDecisionBAROutput())).thenReturn(BigDecimal.TEN);
        when(pricingOverrideManager.getPendingBAROverrides().get(overrideWrapper.getCpDecisionBAROutput())).thenReturn(overrideWrapper);

        pricingOverrideManager.applyOverride(overrideWrapper, null);

        assertFalse(overrideWrapper.isPendingDelete());
        assertTrue(overrideWrapper.isPendingSave());
        assertEquals(pricingOverrideManager.getPendingBAROverrides().get(overrideWrapper.getCpDecisionBAROutput()), overrideWrapper);
        assertEquals(BigDecimal.valueOf(100.00), overrideWrapper.getGroupFloorOverride());
        assertEquals(BigDecimal.valueOf(200.00), overrideWrapper.getCeilingOverride());

        assertEquals(DecisionOverrideType.GPFLOORANDCEIL, overrideWrapper.getCpDecisionBAROutput().getOverrideType());
        assertEquals(BigDecimal.valueOf(100.00), overrideWrapper.getCpDecisionBAROutput().getFloorOverride());
        assertEquals(BigDecimal.valueOf(200.00), overrideWrapper.getCpDecisionBAROutput().getCeilingOverride());
    }

    @Test
    public void applyOverrideSpecificOverrideThenFloorOverride() {
        CPOverrideWrapper overrideWrapper = buildCPOverrideWrapper();
        overrideWrapper.getCpDecisionBAROutput().setSpecificOverride(BigDecimal.TEN);
        overrideWrapper.setIsBaseRoomType(true);
        overrideWrapper.setFloorOverride(BigDecimal.TEN);

        CPOverrideWrapper nonBaseCPOverrideWrapper = buildCPOverrideWrapper();
        CPBARDecisionDTO cpbarDecisionDTO = new CPBARDecisionDTO();
        cpbarDecisionDTO.setDecisions(Arrays.asList(overrideWrapper.getCpDecisionBAROutput(), nonBaseCPOverrideWrapper.getCpDecisionBAROutput()));
        CPBARDecisionUIWrapper cpBARDecisionUIWrapper = new CPBARDecisionUIWrapper(cpbarDecisionDTO, false);
        nonBaseCPOverrideWrapper.getCpDecisionBAROutput().getAccomType().setId(2);
        cpBARDecisionUIWrapper.getCpOverrideWrappers().add(nonBaseCPOverrideWrapper);
        overrideWrapper.setParent(cpBARDecisionUIWrapper);

        nonBaseCPOverrideWrapper.setSpecificOverride(BigDecimal.TEN);
        overrideWrapper.setNonBaseRoomTypeOverrideWrappers(Collections.singletonList(nonBaseCPOverrideWrapper));

        // Set a previous specific override value
        CPOverrideWrapper nonBaseRTCPOverrideWrapper = cpBARDecisionUIWrapper.getCpOverrideWrappers().get(1);
        nonBaseRTCPOverrideWrapper.setSpecificOverride(BigDecimal.TEN);
        when(uiContext.getSystemCaughtUpDate()).thenReturn(new Date());
        when(pricingConfigurationService.getCPDecisionContext(overrideWrapper.getStartDate(), overrideWrapper.getEndDate())).thenReturn(cpDecisionContext);
        when(cpDecisionContext.getPrimaryProduct(overrideWrapper.getCpDecisionBAROutput().getProduct())).thenReturn(overrideWrapper.getCpDecisionBAROutput().getProduct());
        when(cpDecisionContext.calculateRoundedRate(overrideWrapper.getCpDecisionBAROutput())).thenReturn(BigDecimal.TEN);
        when(pricingOverrideManager.getPendingBAROverrides().get(overrideWrapper.getCpDecisionBAROutput())).thenReturn(overrideWrapper);
        Optional<AccomTypeSupplementValue> supplement = Optional.of(new AccomTypeSupplementValue());
        when(cpDecisionContext.getSupplementFor(nonBaseRTCPOverrideWrapper.getCpDecisionBAROutput())).thenReturn(supplement);

        pricingOverrideManager.setShowOnlyBaseRoomTypesFlag(true);

        pricingOverrideManager.applyOverride(overrideWrapper, null);

        // Verify the wrapper conditions
        assertFalse(cpBARDecisionUIWrapper.getCpOverrideWrappers().get(2).isPendingDelete());
        assertTrue(overrideWrapper.isPendingSave());
        assertEquals(pricingOverrideManager.getPendingBAROverrides().get(overrideWrapper.getCpDecisionBAROutput()), overrideWrapper);
        assertEquals(BigDecimal.TEN, overrideWrapper.getFloorOverride());

        // Verify the CPDecisionBarOutput has the override
        assertEquals(DecisionOverrideType.FLOOR, overrideWrapper.getCpDecisionBAROutput().getOverrideType());
        assertEquals(BigDecimal.TEN, overrideWrapper.getCpDecisionBAROutput().getFloorOverride());
        assertNull(overrideWrapper.getCpDecisionBAROutput().getSpecificOverride());
    }


    @Test
    public void testAccomTypesFiltrationCriteria() {
        when(hospitalityRoomsService.getAllTypesOfHospitalityRooms()).thenReturn(Arrays.asList("H", "F"));
        List<Pair<AccomType, Boolean>> accomTypeFiltrationResultPairs =
                asList(Pair.of(createAccomType("A", 1, 10, 1), true),
                        Pair.of(createAccomType("B", 2, 10, 1), false),
                        Pair.of(createAccomType("C", 1, 0, 1), false),
                        Pair.of(createAccomType("H", 1, 0, 1), true),
                        Pair.of(createAccomType("E", 1, 10, 2), false),
                        Pair.of(createAccomType("F", 1, 0, 2), false));
        accomTypeFiltrationResultPairs.forEach(accomTypeFiltrationResultPair -> assertEquals(accomTypeFiltrationResultPair.getRight(),
                pricingOverrideManager.getAccomTypesFiltrationCriteria().test(accomTypeFiltrationResultPair.getLeft())));
    }

    @Test
    public void testAccomTypesFiltrationCriteriaWithOptimizedHotelRoomFetch() {
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.OPTIMIZE_HOSPITALITY_ROOM_FETCH)).thenReturn(true);
        when(hospitalityRoomsService.getAllTypesOfHospitalityRooms()).thenReturn(Arrays.asList("H", "F"));
        pricingOverrideManager.setupHospitalityRooms();
        List<Pair<AccomType, Boolean>> accomTypeFiltrationResultPairs =
                asList(Pair.of(createAccomType("A", 1, 10, 1), true),
                        Pair.of(createAccomType("B", 2, 10, 1), false),
                        Pair.of(createAccomType("C", 1, 0, 1), false),
                        Pair.of(createAccomType("H", 1, 0, 1), true),
                        Pair.of(createAccomType("E", 1, 10, 2), false),
                        Pair.of(createAccomType("F", 1, 0, 2), false));
        accomTypeFiltrationResultPairs.forEach(accomTypeFiltrationResultPair -> assertEquals(accomTypeFiltrationResultPair.getRight(),
                pricingOverrideManager.getAccomTypesFiltrationCriteria().test(accomTypeFiltrationResultPair.getLeft())));
    }

    @Test
    public void shouldFetchHospitalityRooms_WhenOptimizeHospitalityRoomFetchIsTrue() {
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.OPTIMIZE_HOSPITALITY_ROOM_FETCH))
                .thenReturn(true);
        pricingOverrideManager.setAvailableCapacityToSellEnabled(false);
        when(hospitalityRoomsService.getAllTypesOfHospitalityRooms()).thenReturn(Arrays.asList("abc", "def"));
        pricingOverrideManager.setupHospitalityRooms();
        assertTrue(pricingOverrideManager.isHospitalityRoom("abc"));
        assertFalse(pricingOverrideManager.isHospitalityRoom("a"));
        verify(hospitalityRoomsService).getAllTypesOfHospitalityRooms();
    }

    @Test
    public void shouldFetchHospitalityRooms_WhenAvailableCapacityToSellEnabledIsTrue() {
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.OPTIMIZE_HOSPITALITY_ROOM_FETCH))
                .thenReturn(false);
        pricingOverrideManager.setAvailableCapacityToSellEnabled(true);
        when(hospitalityRoomsService.getAllTypesOfHospitalityRooms()).thenReturn(Arrays.asList("abc", "def"));
        pricingOverrideManager.setupHospitalityRooms();
        assertTrue(pricingOverrideManager.isHospitalityRoom("abc"));
        assertFalse(pricingOverrideManager.isHospitalityRoom("a"));
        verify(hospitalityRoomsService).getAllTypesOfHospitalityRooms();
    }

    @Test
    public void shouldNotFetchHospitalityRooms_WhenBothFlagsAreFalse() {
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.OPTIMIZE_HOSPITALITY_ROOM_FETCH))
                .thenReturn(false);
        pricingOverrideManager.setAvailableCapacityToSellEnabled(false);
        when(hospitalityRoomsService.getAllTypesOfHospitalityRooms()).thenReturn(Arrays.asList("abc", "def"));
        pricingOverrideManager.setupHospitalityRooms();
        assertFalse(pricingOverrideManager.isHospitalityRoom("abc"));
        verify(hospitalityRoomsService, never()).getAllTypesOfHospitalityRooms();
    }

    @Test
    public void getNonBaseRoomTypes() {
        LocalDate localDate = new LocalDate();
        Date caughtUpDate = localDate.toDate();
        CPDecisionBAROutput cpDecisionBAROutput = new CPDecisionBAROutput();
        cpDecisionBAROutput.setArrivalDate(localDate);
        Product product = new Product();
        product.setSystemDefault(true);
        cpDecisionBAROutput.setProduct(product);
        AccomClass accomClass = createAccomClass();
        AccomType roomType = createRoomType(5, accomClass);
        cpDecisionBAROutput.setAccomType(roomType);
        CPOverrideWrapper wrapper = new CPOverrideWrapper();
        wrapper.setCpDecisionBAROutput(cpDecisionBAROutput);
        CPOverrideWrapper resultWrapper = new CPOverrideWrapper();
        PricingAccomClass masterPricingAccomClass = new PricingAccomClass();

        PricingManagementCPSearchCriteria criteria = pricingOverrideManager.newGroupSearchCriteria();
        criteria.setRoomClass(accomClass);
        criteria.setEndDate(localDate);
        criteria.setStartDate(localDate);

        pricingOverrideManager.setShowOnlyBaseRoomTypesFlag(false);

        CPBARDecisionDTO cpbarDecisionDTO = new CPBARDecisionDTO();
        cpbarDecisionDTO.setDate(localDate);
        CPDecisionBAROutput cpDecisionBAROutput2 = new CPDecisionBAROutput();
        AccomType roomType2 = createRoomType(10, accomClass);
        cpDecisionBAROutput2.setAccomType(roomType2);
        cpDecisionBAROutput2.setArrivalDate(localDate);
        cpDecisionBAROutput2.setProduct(product);
        cpbarDecisionDTO.setDecisions(Collections.singletonList(cpDecisionBAROutput2));
        Set<AccomType> accomTypeSet = new HashSet<>();
        accomTypeSet.add(roomType);
        accomTypeSet.add(roomType2);
        accomClass.setAccomTypes(accomTypeSet);
        pricingOverrideManager.setAccomTypeFiltrationCriteria((at) -> at.getId() == 1);

        when(service.searchForBarDecisionDTO(anyBoolean(), any(), anyBoolean(), any(), any())).thenReturn(List.of(cpbarDecisionDTO));
        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.LRAENABLED)).thenReturn(false);

        //wrapper.getNonBaseRoomTypeOverrideWrappers() == null
        wrapper.setNonBaseRoomTypeOverrideWrappers(null);
        List<CPOverrideWrapper> results1 = pricingOverrideManager.getNonBaseRoomTypes(wrapper, masterPricingAccomClass, caughtUpDate);
        assertEquals(1, results1.size());
        assertEquals(cpDecisionBAROutput2, results1.get(0).getCpDecisionBAROutput());

        //wrapper.getNonBaseRoomTypeOverrideWrappers() != null
        wrapper.setNonBaseRoomTypeOverrideWrappers(List.of(resultWrapper));
        List<CPOverrideWrapper> results2 = pricingOverrideManager.getNonBaseRoomTypes(wrapper, masterPricingAccomClass, caughtUpDate);
        assertEquals(1, results2.size());
        assertEquals(resultWrapper, results2.get(0));
    }

    @Test
    public void testApplyRemove_UnpersistedOverride() {
        Product product = new Product();
        CPDecisionBAROutput cpDecisionBAROutput = new CPDecisionBAROutput();
        cpDecisionBAROutput.setProduct(product);
        LocalDate localDate = new LocalDate();
        cpDecisionBAROutput.setArrivalDate(localDate);
        int accomTypeId = 1;
        AccomClass accomClass = new AccomClass();
        AccomType accomType = new AccomType();
        accomType.setAccomClass(accomClass);
        accomType.setId(accomTypeId);
        cpDecisionBAROutput.setAccomType(accomType);

        CPOverrideWrapper overrideWrapper = new CPOverrideWrapper(cpDecisionBAROutput, false);
        BigDecimal ceilingOverride = new BigDecimal("200.00");
        overrideWrapper.setCeilingOverride(ceilingOverride);
        overrideWrapper.setApplyOverrideAcrossRoomTypes(false);

        PricingAccomClass pricingAccomClass = new PricingAccomClass();
        pricingAccomClass.setAccomClass(accomClass);
        pricingAccomClass.setAccomType(accomType);
        List<PricingAccomClass> pricingAccomClasses = new ArrayList<>();
        pricingAccomClasses.add(pricingAccomClass);
        //set the parent of the overrideWrapper
        CPBARDecisionDTO cpbarDecisionDTO = new CPBARDecisionDTO();
        List<CPDecisionBAROutput> decisions = new ArrayList<>();
        CPDecisionBAROutput cpDecisionBAROutputBase = new CPDecisionBAROutput();
        cpDecisionBAROutputBase.setProduct(product);
        cpDecisionBAROutputBase.setAccomType(accomType);
        decisions.add(cpDecisionBAROutputBase);
        cpbarDecisionDTO.setDecisions(decisions);
        CPBARDecisionUIWrapper parent = new CPBARDecisionUIWrapper(cpbarDecisionDTO, false);
        overrideWrapper.setParent(parent);

        CPOverrideWrapper parentBaseRoomTypeWrapper = parent.getCpOverrideWrappers().get(0);
        when(overridesMap.get(parentBaseRoomTypeWrapper.getCpDecisionBAROutput())).thenReturn(parentBaseRoomTypeWrapper);

        pricingOverrideManager.setOverridesMap(overridesMap);

        when(pricingConfigurationService.getCPDecisionContext(localDate, localDate, false)).thenReturn(cpDecisionContext);
        pricingOverrideManager.setPricingConfigurationService(pricingConfigurationService);
        pricingOverrideManager.setOverridesMap(overridesMap);

        Optional<AccomTypeSupplementValue> supplement = Optional.of(new AccomTypeSupplementValue());
        when(cpDecisionContext.getSupplementFor(cpDecisionBAROutput)).thenReturn(supplement);

        pricingOverrideManager.removeOverrides(overrideWrapper, pricingAccomClasses, null, null, false);

        verify(cpDecisionContext).calculateRoundedRate(overrideWrapper.getCpDecisionBAROutput());
        verify(overridesMap, times(2)).get(parentBaseRoomTypeWrapper.getCpDecisionBAROutput());
    }

    @Test
    public void isBaseRoomType() {
        List<PricingAccomClass> pricingAccomClasses = new ArrayList<>();
        AccomClass accomClass = createAccomClass("test", 1);
        AccomType accomType = createRoomType(1, accomClass);
        AccomClass accomClass2 = createAccomClass("test2", 2);
        AccomType accomType2 = createRoomType(2, accomClass2);

        //pricingAccomClasses is null; accomType is null
        assertFalse(pricingOverrideManager.isBaseRoomType(null, null));

        //pricingAccomClasses is empty
        assertFalse(pricingOverrideManager.isBaseRoomType(pricingAccomClasses, accomType));

        //accom classes dont match
        PricingAccomClass pricingAccomClass = new PricingAccomClass();
        pricingAccomClass.setAccomClass(accomClass2);
        pricingAccomClasses.add(pricingAccomClass);
        assertFalse(pricingOverrideManager.isBaseRoomType(pricingAccomClasses, accomType));

        //accom types dont match
        accomClass.setAccomTypes(new HashSet<>(List.of(accomType2)));
        pricingAccomClass.setAccomClass(accomClass);
        pricingAccomClass.setAccomType(accomType2);
        assertFalse(pricingOverrideManager.isBaseRoomType(pricingAccomClasses, accomType));

        //accom types match
        accomClass.setAccomTypes(new HashSet<>(List.of(accomType)));
        pricingAccomClass.setAccomType(accomType);
        assertTrue(pricingOverrideManager.isBaseRoomType(pricingAccomClasses, accomType));
    }

    @Test
    public void isBaseRoomTypeCeilingAndOrFloorOverride() {
        CPOverrideWrapper overrideWrapper = new CPOverrideWrapper();

        //not base room type
        overrideWrapper.setIsBaseRoomType(false);
        assertFalse(pricingOverrideManager.isBaseRoomTypeCeilingAndOrFloorOverride(overrideWrapper));

        //base room type; no overrides
        overrideWrapper.setIsBaseRoomType(true);
        assertFalse(pricingOverrideManager.isBaseRoomTypeCeilingAndOrFloorOverride(overrideWrapper));

        //base room type; ceiling override
        overrideWrapper.setCeilingOverride(BigDecimal.TEN);
        assertTrue(pricingOverrideManager.isBaseRoomTypeCeilingAndOrFloorOverride(overrideWrapper));

        //base room type; floor override
        overrideWrapper.setCeilingOverride(null);
        overrideWrapper.setFloorOverride(BigDecimal.TEN);
        assertTrue(pricingOverrideManager.isBaseRoomTypeCeilingAndOrFloorOverride(overrideWrapper));

        //base room type; ceiling and floor override
        overrideWrapper.setCeilingOverride(BigDecimal.TEN);
        assertTrue(pricingOverrideManager.isBaseRoomTypeCeilingAndOrFloorOverride(overrideWrapper));
    }

    @Test
    public void isNotSameRoomType() {
        AccomClass accomClass = createAccomClass();
        AccomType roomType1 = createRoomType(1, accomClass);
        AccomType roomType2 = createRoomType(2, accomClass);

        CPOverrideWrapper overrideWrapper = new CPOverrideWrapper();
        CPDecisionBAROutput cpDecisionBAROutput1 = new CPDecisionBAROutput();
        cpDecisionBAROutput1.setAccomType(roomType1);
        overrideWrapper.setCpDecisionBAROutput(cpDecisionBAROutput1);
        CPOverrideWrapper cpOverrideWrapper = new CPOverrideWrapper();
        CPDecisionBAROutput cpDecisionBAROutput2 = new CPDecisionBAROutput();
        cpDecisionBAROutput2.setAccomType(roomType2);
        cpOverrideWrapper.setCpDecisionBAROutput(cpDecisionBAROutput2);

        assertTrue(pricingOverrideManager.isNotSameRoomType(overrideWrapper, cpOverrideWrapper));

        cpDecisionBAROutput2.setAccomType(roomType1);
        assertFalse(pricingOverrideManager.isNotSameRoomType(overrideWrapper, cpOverrideWrapper));
    }

    @Test
    public void createOverride_USER() {
        int propertyId = 123;
        when(uiContext.getPropertyId()).thenReturn(propertyId);
        int principalId = 567;
        when(uiContext.getUserPrincipalId()).thenReturn(principalId);

        CPDecisionBAROutput decisionBARCPOutput = new CPDecisionBAROutput();
        Product product = new Product();
        decisionBARCPOutput.setProduct(product);
        int decisionId = 111;
        decisionBARCPOutput.setDecisionId(decisionId);
        int lengthOfStay = 1;
        decisionBARCPOutput.setLengthOfStay(lengthOfStay);
        BigDecimal finalBar = new BigDecimal(100);
        decisionBARCPOutput.setFinalBAR(finalBar);
        AccomType accomType = new AccomType();
        decisionBARCPOutput.setAccomType(accomType);
        LocalDate arrivalDate = new LocalDate();
        decisionBARCPOutput.setArrivalDate(arrivalDate);

        CPOverrideWrapper overrideWrapper = new CPOverrideWrapper();
        DecisionOverrideType overrideType = DecisionOverrideType.USER;
        overrideWrapper.setOriginalOverrideType(overrideType);
        BigDecimal originalSpecificOverride = new BigDecimal(101);
        overrideWrapper.setOriginalSpecificOverride(originalSpecificOverride);
        BigDecimal specificOverride = new BigDecimal(105);
        overrideWrapper.setSpecificOverride(specificOverride);

        //Supplement is 0
        when(cpDecisionContext.getSupplement(decisionBARCPOutput)).thenReturn(BigDecimal.ZERO);

        CPDecisionBAROutputOverride result = pricingOverrideManager.createOverride(decisionBARCPOutput, overrideWrapper, cpDecisionContext);
        assertEquals(propertyId, result.getPropertyId());
        assertEquals(product, result.getProduct());
        assertEquals(principalId, result.getUser());
        assertEquals(decisionId, result.getDecisionId());
        assertEquals(lengthOfStay, result.getLengthOfStay());
        assertEquals(DecisionOverrideType.USER, result.getNewOverrideType());
        assertEquals(overrideType, result.getOldOverrideType());
        assertEquals(finalBar, result.getOldUserOverride());
        assertNull(result.getOldFloorRate());
        assertNull(result.getOldCeilingRate());
        assertEquals(specificOverride, result.getNewUserOverride());
        assertNull(result.getNewFloorRate());
        assertNull(result.getNewCeilingRate());
        assertEquals(accomType, result.getAccomType());
        assertEquals(arrivalDate, result.getArrivalDate());

        //Supplement is 10
        BigDecimal supplement = BigDecimal.TEN;
        when(cpDecisionContext.getSupplement(decisionBARCPOutput)).thenReturn(supplement);
        BigDecimal finalBarMinusSupplement = finalBar.subtract(supplement);
        BigDecimal specificOverrideMinusSupplement = specificOverride.subtract(supplement);

        CPDecisionBAROutputOverride result2 = pricingOverrideManager.createOverride(decisionBARCPOutput, overrideWrapper, cpDecisionContext);
        assertEquals(propertyId, result2.getPropertyId());
        assertEquals(product, result2.getProduct());
        assertEquals(principalId, result2.getUser());
        assertEquals(decisionId, result2.getDecisionId());
        assertEquals(lengthOfStay, result2.getLengthOfStay());
        assertEquals(DecisionOverrideType.USER, result2.getNewOverrideType());
        assertEquals(overrideType, result2.getOldOverrideType());
        assertEquals(finalBarMinusSupplement, result2.getOldUserOverride());
        assertNull(result2.getOldFloorRate());
        assertNull(result2.getOldCeilingRate());
        assertEquals(specificOverrideMinusSupplement, result2.getNewUserOverride());
        assertNull(result2.getNewFloorRate());
        assertNull(result2.getNewCeilingRate());
        assertEquals(accomType, result2.getAccomType());
        assertEquals(arrivalDate, result2.getArrivalDate());
    }

    @Test
    public void createOverride_FLOOR() {
        int propertyId = 123;
        when(uiContext.getPropertyId()).thenReturn(propertyId);
        int principalId = 567;
        when(uiContext.getUserPrincipalId()).thenReturn(principalId);

        CPDecisionBAROutput decisionBARCPOutput = new CPDecisionBAROutput();
        Product product = new Product();
        decisionBARCPOutput.setProduct(product);
        int decisionId = 111;
        decisionBARCPOutput.setDecisionId(decisionId);
        int lengthOfStay = 1;
        decisionBARCPOutput.setLengthOfStay(lengthOfStay);
        BigDecimal finalBar = new BigDecimal(100);
        decisionBARCPOutput.setFinalBAR(finalBar);
        AccomType accomType = new AccomType();
        decisionBARCPOutput.setAccomType(accomType);
        LocalDate arrivalDate = new LocalDate();
        decisionBARCPOutput.setArrivalDate(arrivalDate);

        CPOverrideWrapper overrideWrapper = new CPOverrideWrapper();
        DecisionOverrideType overrideType = DecisionOverrideType.FLOOR;
        overrideWrapper.setOriginalOverrideType(overrideType);
        BigDecimal originalFloorOverride = new BigDecimal(103);
        overrideWrapper.setOriginalFloorOverride(originalFloorOverride);
        BigDecimal floorOverride = new BigDecimal(107);
        overrideWrapper.setFloorOverride(floorOverride);

        //Supplement is 0
        when(cpDecisionContext.getSupplement(decisionBARCPOutput)).thenReturn(BigDecimal.ZERO);

        CPDecisionBAROutputOverride result = pricingOverrideManager.createOverride(decisionBARCPOutput, overrideWrapper, cpDecisionContext);
        assertEquals(propertyId, result.getPropertyId());
        assertEquals(product, result.getProduct());
        assertEquals(principalId, result.getUser());
        assertEquals(decisionId, result.getDecisionId());
        assertEquals(lengthOfStay, result.getLengthOfStay());
        assertEquals(DecisionOverrideType.FLOOR, result.getNewOverrideType());
        assertEquals(overrideType, result.getOldOverrideType());
        assertEquals(finalBar, result.getOldUserOverride());
        assertEquals(originalFloorOverride, result.getOldFloorRate());
        assertNull(result.getOldCeilingRate());
        assertNull(result.getNewUserOverride());
        assertEquals(floorOverride, result.getNewFloorRate());
        assertNull(result.getNewCeilingRate());
        assertEquals(accomType, result.getAccomType());
        assertEquals(arrivalDate, result.getArrivalDate());

        //Supplement is 10
        BigDecimal supplement = BigDecimal.TEN;
        when(cpDecisionContext.getSupplement(decisionBARCPOutput)).thenReturn(supplement);
        BigDecimal finalBarMinusSupplement = finalBar.subtract(supplement);
        BigDecimal originalFloorOverrideMinusSupplement = originalFloorOverride.subtract(supplement);
        BigDecimal floorOverrideMinusSupplement = floorOverride.subtract(supplement);

        CPDecisionBAROutputOverride result2 = pricingOverrideManager.createOverride(decisionBARCPOutput, overrideWrapper, cpDecisionContext);
        assertEquals(propertyId, result2.getPropertyId());
        assertEquals(product, result2.getProduct());
        assertEquals(principalId, result2.getUser());
        assertEquals(decisionId, result2.getDecisionId());
        assertEquals(lengthOfStay, result2.getLengthOfStay());
        assertEquals(DecisionOverrideType.FLOOR, result2.getNewOverrideType());
        assertEquals(overrideType, result2.getOldOverrideType());
        assertEquals(finalBarMinusSupplement, result2.getOldUserOverride());
        assertEquals(originalFloorOverrideMinusSupplement, result2.getOldFloorRate());
        assertNull(result2.getOldCeilingRate());
        assertNull(result2.getNewUserOverride());
        assertEquals(floorOverrideMinusSupplement, result2.getNewFloorRate());
        assertNull(result2.getNewCeilingRate());
        assertEquals(accomType, result2.getAccomType());
        assertEquals(arrivalDate, result2.getArrivalDate());
    }

    @Test
    public void createOverride_CEILING() {
        int propertyId = 123;
        when(uiContext.getPropertyId()).thenReturn(propertyId);
        int principalId = 567;
        when(uiContext.getUserPrincipalId()).thenReturn(principalId);

        CPDecisionBAROutput decisionBARCPOutput = new CPDecisionBAROutput();
        Product product = new Product();
        decisionBARCPOutput.setProduct(product);
        int decisionId = 111;
        decisionBARCPOutput.setDecisionId(decisionId);
        int lengthOfStay = 1;
        decisionBARCPOutput.setLengthOfStay(lengthOfStay);
        BigDecimal finalBar = new BigDecimal(100);
        decisionBARCPOutput.setFinalBAR(finalBar);
        AccomType accomType = new AccomType();
        decisionBARCPOutput.setAccomType(accomType);
        LocalDate arrivalDate = new LocalDate();
        decisionBARCPOutput.setArrivalDate(arrivalDate);

        CPOverrideWrapper overrideWrapper = new CPOverrideWrapper();
        DecisionOverrideType overrideType = DecisionOverrideType.CEIL;
        overrideWrapper.setOriginalOverrideType(overrideType);
        BigDecimal originalCeilingOverride = new BigDecimal(104);
        overrideWrapper.setOriginalCeilingOverride(originalCeilingOverride);
        BigDecimal ceilingOverride = new BigDecimal(108);
        overrideWrapper.setCeilingOverride(ceilingOverride);

        //Supplement is 0
        when(cpDecisionContext.getSupplement(decisionBARCPOutput)).thenReturn(BigDecimal.ZERO);

        CPDecisionBAROutputOverride result = pricingOverrideManager.createOverride(decisionBARCPOutput, overrideWrapper, cpDecisionContext);
        assertEquals(propertyId, result.getPropertyId());
        assertEquals(product, result.getProduct());
        assertEquals(principalId, result.getUser());
        assertEquals(decisionId, result.getDecisionId());
        assertEquals(lengthOfStay, result.getLengthOfStay());
        assertEquals(DecisionOverrideType.CEIL, result.getNewOverrideType());
        assertEquals(overrideType, result.getOldOverrideType());
        assertEquals(finalBar, result.getOldUserOverride());
        assertNull(result.getOldFloorRate());
        assertEquals(originalCeilingOverride, result.getOldCeilingRate());
        assertNull(result.getNewUserOverride());
        assertNull(result.getNewFloorRate());
        assertEquals(ceilingOverride, result.getNewCeilingRate());
        assertEquals(accomType, result.getAccomType());
        assertEquals(arrivalDate, result.getArrivalDate());

        //Supplement is 10
        BigDecimal supplement = BigDecimal.TEN;
        when(cpDecisionContext.getSupplement(decisionBARCPOutput)).thenReturn(supplement);
        BigDecimal finalBarMinusSupplement = finalBar.subtract(supplement);
        BigDecimal originalCeilingOverrideMinusSupplement = originalCeilingOverride.subtract(supplement);
        BigDecimal ceilingOverrideMinusSupplement = ceilingOverride.subtract(supplement);

        CPDecisionBAROutputOverride result2 = pricingOverrideManager.createOverride(decisionBARCPOutput, overrideWrapper, cpDecisionContext);
        assertEquals(propertyId, result2.getPropertyId());
        assertEquals(product, result2.getProduct());
        assertEquals(principalId, result2.getUser());
        assertEquals(decisionId, result2.getDecisionId());
        assertEquals(lengthOfStay, result2.getLengthOfStay());
        assertEquals(DecisionOverrideType.CEIL, result2.getNewOverrideType());
        assertEquals(overrideType, result2.getOldOverrideType());
        assertEquals(finalBarMinusSupplement, result2.getOldUserOverride());
        assertNull(result2.getOldFloorRate());
        assertEquals(originalCeilingOverrideMinusSupplement, result2.getOldCeilingRate());
        assertNull(result2.getNewUserOverride());
        assertNull(result2.getNewFloorRate());
        assertEquals(ceilingOverrideMinusSupplement, result2.getNewCeilingRate());
        assertEquals(accomType, result2.getAccomType());
        assertEquals(arrivalDate, result2.getArrivalDate());
    }

    @Test
    public void createOverride_FLOORANDCEIL() {
        int propertyId = 123;
        when(uiContext.getPropertyId()).thenReturn(propertyId);
        int principalId = 567;
        when(uiContext.getUserPrincipalId()).thenReturn(principalId);

        CPDecisionBAROutput decisionBARCPOutput = new CPDecisionBAROutput();
        Product product = new Product();
        decisionBARCPOutput.setProduct(product);
        int decisionId = 111;
        decisionBARCPOutput.setDecisionId(decisionId);
        int lengthOfStay = 1;
        decisionBARCPOutput.setLengthOfStay(lengthOfStay);
        BigDecimal finalBar = new BigDecimal(100);
        decisionBARCPOutput.setFinalBAR(finalBar);
        AccomType accomType = new AccomType();
        decisionBARCPOutput.setAccomType(accomType);
        LocalDate arrivalDate = new LocalDate();
        decisionBARCPOutput.setArrivalDate(arrivalDate);

        CPOverrideWrapper overrideWrapper = new CPOverrideWrapper();
        DecisionOverrideType overrideType = DecisionOverrideType.FLOORANDCEIL;
        overrideWrapper.setOriginalOverrideType(overrideType);
        BigDecimal originalFloorOverride = new BigDecimal(103);
        overrideWrapper.setOriginalFloorOverride(originalFloorOverride);
        BigDecimal originalCeilingOverride = new BigDecimal(104);
        overrideWrapper.setOriginalCeilingOverride(originalCeilingOverride);
        BigDecimal floorOverride = new BigDecimal(107);
        overrideWrapper.setFloorOverride(floorOverride);
        BigDecimal ceilingOverride = new BigDecimal(108);
        overrideWrapper.setCeilingOverride(ceilingOverride);

        //Supplement is 0
        when(cpDecisionContext.getSupplement(decisionBARCPOutput)).thenReturn(BigDecimal.ZERO);

        CPDecisionBAROutputOverride result = pricingOverrideManager.createOverride(decisionBARCPOutput, overrideWrapper, cpDecisionContext);
        assertEquals(propertyId, result.getPropertyId());
        assertEquals(product, result.getProduct());
        assertEquals(principalId, result.getUser());
        assertEquals(decisionId, result.getDecisionId());
        assertEquals(lengthOfStay, result.getLengthOfStay());
        assertEquals(DecisionOverrideType.FLOORANDCEIL, result.getNewOverrideType());
        assertEquals(overrideType, result.getOldOverrideType());
        assertEquals(finalBar, result.getOldUserOverride());
        assertEquals(originalFloorOverride, result.getOldFloorRate());
        assertEquals(originalCeilingOverride, result.getOldCeilingRate());
        assertNull(result.getNewUserOverride());
        assertEquals(floorOverride, result.getNewFloorRate());
        assertEquals(ceilingOverride, result.getNewCeilingRate());
        assertEquals(accomType, result.getAccomType());
        assertEquals(arrivalDate, result.getArrivalDate());

        //Supplement is 10
        BigDecimal supplement = BigDecimal.TEN;
        when(cpDecisionContext.getSupplement(decisionBARCPOutput)).thenReturn(supplement);
        BigDecimal finalBarMinusSupplement = finalBar.subtract(supplement);
        BigDecimal originalFloorOverrideMinusSupplement = originalFloorOverride.subtract(supplement);
        BigDecimal originalCeilingOverrideMinusSupplement = originalCeilingOverride.subtract(supplement);
        BigDecimal floorOverrideMinusSupplement = floorOverride.subtract(supplement);
        BigDecimal ceilingOverrideMinusSupplement = ceilingOverride.subtract(supplement);

        CPDecisionBAROutputOverride result2 = pricingOverrideManager.createOverride(decisionBARCPOutput, overrideWrapper, cpDecisionContext);
        assertEquals(propertyId, result2.getPropertyId());
        assertEquals(product, result2.getProduct());
        assertEquals(principalId, result2.getUser());
        assertEquals(decisionId, result2.getDecisionId());
        assertEquals(lengthOfStay, result2.getLengthOfStay());
        assertEquals(DecisionOverrideType.FLOORANDCEIL, result2.getNewOverrideType());
        assertEquals(overrideType, result2.getOldOverrideType());
        assertEquals(finalBarMinusSupplement, result2.getOldUserOverride());
        assertEquals(originalFloorOverrideMinusSupplement, result2.getOldFloorRate());
        assertEquals(originalCeilingOverrideMinusSupplement, result2.getOldCeilingRate());
        assertNull(result2.getNewUserOverride());
        assertEquals(floorOverrideMinusSupplement, result2.getNewFloorRate());
        assertEquals(ceilingOverrideMinusSupplement, result2.getNewCeilingRate());
        assertEquals(accomType, result2.getAccomType());
        assertEquals(arrivalDate, result2.getArrivalDate());
    }

    @Test
    public void createOverride_GPFLOOR() {
        int propertyId = 123;
        when(uiContext.getPropertyId()).thenReturn(propertyId);
        int principalId = 567;
        when(uiContext.getUserPrincipalId()).thenReturn(principalId);

        CPDecisionBAROutput decisionBARCPOutput = new CPDecisionBAROutput();
        Product product = new Product();
        decisionBARCPOutput.setProduct(product);
        int decisionId = 111;
        decisionBARCPOutput.setDecisionId(decisionId);
        int lengthOfStay = 1;
        decisionBARCPOutput.setLengthOfStay(lengthOfStay);
        BigDecimal finalBar = new BigDecimal(100);
        decisionBARCPOutput.setFinalBAR(finalBar);
        AccomType accomType = new AccomType();
        decisionBARCPOutput.setAccomType(accomType);
        LocalDate arrivalDate = new LocalDate();
        decisionBARCPOutput.setArrivalDate(arrivalDate);

        CPOverrideWrapper overrideWrapper = new CPOverrideWrapper();
        DecisionOverrideType overrideType = DecisionOverrideType.GPFLOOR;
        overrideWrapper.setOriginalOverrideType(overrideType);
        BigDecimal originalGroupFloorOverride = new BigDecimal(102);
        overrideWrapper.setOriginalGroupFloorOverride(originalGroupFloorOverride);
        BigDecimal groupFloorOverride = new BigDecimal(106);
        overrideWrapper.setGroupFloorOverride(groupFloorOverride);

        //Supplement is 0
        when(cpDecisionContext.getSupplement(decisionBARCPOutput)).thenReturn(BigDecimal.ZERO);

        CPDecisionBAROutputOverride result = pricingOverrideManager.createOverride(decisionBARCPOutput, overrideWrapper, cpDecisionContext);
        assertEquals(propertyId, result.getPropertyId());
        assertEquals(product, result.getProduct());
        assertEquals(principalId, result.getUser());
        assertEquals(decisionId, result.getDecisionId());
        assertEquals(lengthOfStay, result.getLengthOfStay());
        assertEquals(DecisionOverrideType.GPFLOOR, result.getNewOverrideType());
        assertEquals(overrideType, result.getOldOverrideType());
        assertEquals(finalBar, result.getOldUserOverride());
        assertEquals(originalGroupFloorOverride, result.getOldFloorRate());
        assertNull(result.getOldCeilingRate());
        assertNull(result.getNewUserOverride());
        assertEquals(groupFloorOverride, result.getNewFloorRate());
        assertNull(result.getNewCeilingRate());
        assertEquals(accomType, result.getAccomType());
        assertEquals(arrivalDate, result.getArrivalDate());

        //Supplement is 10
        BigDecimal supplement = BigDecimal.TEN;
        when(cpDecisionContext.getSupplement(decisionBARCPOutput)).thenReturn(supplement);
        BigDecimal finalBarMinusSupplement = finalBar.subtract(supplement);
        BigDecimal originalGroupFloorOverrideMinusSupplement = originalGroupFloorOverride.subtract(supplement);
        BigDecimal groupFloorOverrideMinusSupplement = groupFloorOverride.subtract(supplement);

        CPDecisionBAROutputOverride result2 = pricingOverrideManager.createOverride(decisionBARCPOutput, overrideWrapper, cpDecisionContext);
        assertEquals(propertyId, result2.getPropertyId());
        assertEquals(product, result2.getProduct());
        assertEquals(principalId, result2.getUser());
        assertEquals(decisionId, result2.getDecisionId());
        assertEquals(lengthOfStay, result2.getLengthOfStay());
        assertEquals(DecisionOverrideType.GPFLOOR, result2.getNewOverrideType());
        assertEquals(overrideType, result2.getOldOverrideType());
        assertEquals(finalBarMinusSupplement, result2.getOldUserOverride());
        assertEquals(originalGroupFloorOverrideMinusSupplement, result2.getOldFloorRate());
        assertNull(result2.getOldCeilingRate());
        assertNull(result2.getNewUserOverride());
        assertEquals(groupFloorOverrideMinusSupplement, result2.getNewFloorRate());
        assertNull(result2.getNewCeilingRate());
        assertEquals(accomType, result2.getAccomType());
        assertEquals(arrivalDate, result2.getArrivalDate());
    }

    @Test
    public void createOverride_GPFLOORANDCEIL() {
        int propertyId = 123;
        when(uiContext.getPropertyId()).thenReturn(propertyId);
        int principalId = 567;
        when(uiContext.getUserPrincipalId()).thenReturn(principalId);

        CPDecisionBAROutput decisionBARCPOutput = new CPDecisionBAROutput();
        Product product = new Product();
        decisionBARCPOutput.setProduct(product);
        int decisionId = 111;
        decisionBARCPOutput.setDecisionId(decisionId);
        int lengthOfStay = 1;
        decisionBARCPOutput.setLengthOfStay(lengthOfStay);
        BigDecimal finalBar = new BigDecimal(100);
        decisionBARCPOutput.setFinalBAR(finalBar);
        AccomType accomType = new AccomType();
        decisionBARCPOutput.setAccomType(accomType);
        LocalDate arrivalDate = new LocalDate();
        decisionBARCPOutput.setArrivalDate(arrivalDate);

        CPOverrideWrapper overrideWrapper = new CPOverrideWrapper();
        DecisionOverrideType overrideType = DecisionOverrideType.GPFLOORANDCEIL;
        overrideWrapper.setOriginalOverrideType(overrideType);
        BigDecimal originalGroupFloorOverride = new BigDecimal(102);
        overrideWrapper.setOriginalGroupFloorOverride(originalGroupFloorOverride);
        BigDecimal originalCeilingOverride = new BigDecimal(104);
        overrideWrapper.setOriginalCeilingOverride(originalCeilingOverride);
        BigDecimal groupFloorOverride = new BigDecimal(106);
        overrideWrapper.setGroupFloorOverride(groupFloorOverride);
        BigDecimal ceilingOverride = new BigDecimal(108);
        overrideWrapper.setCeilingOverride(ceilingOverride);

        //Supplement is 0
        when(cpDecisionContext.getSupplement(decisionBARCPOutput)).thenReturn(BigDecimal.ZERO);

        CPDecisionBAROutputOverride result = pricingOverrideManager.createOverride(decisionBARCPOutput, overrideWrapper, cpDecisionContext);
        assertEquals(propertyId, result.getPropertyId());
        assertEquals(product, result.getProduct());
        assertEquals(principalId, result.getUser());
        assertEquals(decisionId, result.getDecisionId());
        assertEquals(lengthOfStay, result.getLengthOfStay());
        assertEquals(DecisionOverrideType.GPFLOORANDCEIL, result.getNewOverrideType());
        assertEquals(overrideType, result.getOldOverrideType());
        assertEquals(finalBar, result.getOldUserOverride());
        assertEquals(originalGroupFloorOverride, result.getOldFloorRate());
        assertEquals(originalCeilingOverride, result.getOldCeilingRate());
        assertNull(result.getNewUserOverride());
        assertEquals(groupFloorOverride, result.getNewFloorRate());
        assertEquals(ceilingOverride, result.getNewCeilingRate());
        assertEquals(accomType, result.getAccomType());
        assertEquals(arrivalDate, result.getArrivalDate());

        //Supplement is 10
        BigDecimal supplement = BigDecimal.TEN;
        when(cpDecisionContext.getSupplement(decisionBARCPOutput)).thenReturn(supplement);
        BigDecimal finalBarMinusSupplement = finalBar.subtract(supplement);
        BigDecimal originalGroupFloorOverrideMinusSupplement = originalGroupFloorOverride.subtract(supplement);
        BigDecimal groupFloorOverrideMinusSupplement = groupFloorOverride.subtract(supplement);
        BigDecimal originalCeilingOverrideMinusSupplement = originalCeilingOverride.subtract(supplement);
        BigDecimal ceilingOverrideMinusSupplement = ceilingOverride.subtract(supplement);

        CPDecisionBAROutputOverride result2 = pricingOverrideManager.createOverride(decisionBARCPOutput, overrideWrapper, cpDecisionContext);
        assertEquals(propertyId, result2.getPropertyId());
        assertEquals(product, result2.getProduct());
        assertEquals(principalId, result2.getUser());
        assertEquals(decisionId, result2.getDecisionId());
        assertEquals(lengthOfStay, result2.getLengthOfStay());
        assertEquals(DecisionOverrideType.GPFLOORANDCEIL, result2.getNewOverrideType());
        assertEquals(overrideType, result2.getOldOverrideType());
        assertEquals(finalBarMinusSupplement, result2.getOldUserOverride());
        assertEquals(originalGroupFloorOverrideMinusSupplement, result2.getOldFloorRate());
        assertEquals(originalCeilingOverrideMinusSupplement, result2.getOldCeilingRate());
        assertNull(result2.getNewUserOverride());
        assertEquals(groupFloorOverrideMinusSupplement, result2.getNewFloorRate());
        assertEquals(ceilingOverrideMinusSupplement, result2.getNewCeilingRate());
        assertEquals(accomType, result2.getAccomType());
        assertEquals(arrivalDate, result2.getArrivalDate());
    }

    @Test
    void testCreateOverride_User_WithSupplementMethodPercentage() {
        int propertyId = 123;
        when(uiContext.getPropertyId()).thenReturn(propertyId);
        int principalId = 567;
        when(uiContext.getUserPrincipalId()).thenReturn(principalId);

        CPDecisionBAROutput decisionBARCPOutput = new CPDecisionBAROutput();
        Product product = new Product();
        decisionBARCPOutput.setProduct(product);
        int decisionId = 111;
        decisionBARCPOutput.setDecisionId(decisionId);
        int lengthOfStay = 1;
        decisionBARCPOutput.setLengthOfStay(lengthOfStay);
        BigDecimal finalBar = new BigDecimal(100);
        decisionBARCPOutput.setFinalBAR(finalBar);
        AccomType accomType = new AccomType();
        decisionBARCPOutput.setAccomType(accomType);
        LocalDate arrivalDate = new LocalDate();
        decisionBARCPOutput.setArrivalDate(arrivalDate);

        CPOverrideWrapper overrideWrapper = new CPOverrideWrapper();
        DecisionOverrideType overrideType = DecisionOverrideType.USER;
        overrideWrapper.setOriginalOverrideType(overrideType);
        BigDecimal originalSpecificOverride = new BigDecimal(101);
        overrideWrapper.setOriginalSpecificOverride(originalSpecificOverride);
        BigDecimal specificOverride = new BigDecimal(200);
        overrideWrapper.setSpecificOverride(specificOverride);
        overrideWrapper.setRoundedBAR(specificOverride);

        AccomTypeSupplementValue accomTypeSupplementValue = new AccomTypeSupplementValue();
        accomTypeSupplementValue.setValue(BigDecimal.valueOf(50));
        accomTypeSupplementValue.setOffsetMethod(OffsetMethod.PERCENTAGE);

        BigDecimal specificOverrideWithoutSupplement = Supplement.removeSupplementFrom(specificOverride, accomTypeSupplementValue.getValue(), true);
        BigDecimal finalBarWithoutSupplement = Supplement.removeSupplementFrom(finalBar, accomTypeSupplementValue.getValue(), true);

        //Supplement is 50%
        when(cpDecisionContext.getSupplement(decisionBARCPOutput)).thenReturn(BigDecimal.valueOf(50));
        when(cpDecisionContext.getAccomTypeSupplementValue(decisionBARCPOutput)).thenReturn(accomTypeSupplementValue);

        CPDecisionBAROutputOverride result = pricingOverrideManager.createOverride(decisionBARCPOutput, overrideWrapper, cpDecisionContext);
        assertEquals(propertyId, result.getPropertyId());
        assertEquals(product, result.getProduct());
        assertEquals(principalId, result.getUser());
        assertEquals(decisionId, result.getDecisionId());
        assertEquals(lengthOfStay, result.getLengthOfStay());
        assertEquals(DecisionOverrideType.USER, result.getNewOverrideType());
        assertEquals(overrideType, result.getOldOverrideType());
        assertEquals(finalBarWithoutSupplement, result.getOldUserOverride().setScale(2, RoundingMode.CEILING));
        assertNull(result.getOldFloorRate());
        assertNull(result.getOldCeilingRate());
        assertEquals(specificOverrideWithoutSupplement, result.getNewUserOverride().setScale(2, RoundingMode.HALF_UP));
        assertNull(result.getNewFloorRate());
        assertNull(result.getNewCeilingRate());
        assertEquals(accomType, result.getAccomType());
        assertEquals(arrivalDate, result.getArrivalDate());

        //Supplement is 10%
        BigDecimal supplement = BigDecimal.TEN;
        when(cpDecisionContext.getSupplement(decisionBARCPOutput)).thenReturn(supplement);
        AccomTypeSupplementValue accomTypeSupplementValueFor10Percent = new AccomTypeSupplementValue();
        accomTypeSupplementValueFor10Percent.setValue(BigDecimal.TEN);
        accomTypeSupplementValueFor10Percent.setOffsetMethod(OffsetMethod.PERCENTAGE);

        BigDecimal specificOverrideMinusSupplement = Supplement.removeSupplementFrom(specificOverride, accomTypeSupplementValueFor10Percent.getValue(), true);
        BigDecimal finalBarMinusSupplement = Supplement.removeSupplementFrom(finalBar, accomTypeSupplementValueFor10Percent.getValue(), true);

        CPDecisionBAROutputOverride result2 = pricingOverrideManager.createOverride(decisionBARCPOutput, overrideWrapper, cpDecisionContext);
        assertEquals(propertyId, result2.getPropertyId());
        assertEquals(product, result2.getProduct());
        assertEquals(principalId, result2.getUser());
        assertEquals(decisionId, result2.getDecisionId());
        assertEquals(lengthOfStay, result2.getLengthOfStay());
        assertEquals(DecisionOverrideType.USER, result2.getNewOverrideType());
        assertEquals(overrideType, result2.getOldOverrideType());
        assertEquals(finalBarMinusSupplement.setScale(1, RoundingMode.HALF_UP), result2.getOldUserOverride().setScale(1, RoundingMode.HALF_UP));
        assertNull(result2.getOldFloorRate());
        assertNull(result2.getOldCeilingRate());
        assertEquals(specificOverrideMinusSupplement, result2.getNewUserOverride().setScale(2, RoundingMode.HALF_UP));
        assertNull(result2.getNewFloorRate());
        assertNull(result2.getNewCeilingRate());
        assertEquals(accomType, result2.getAccomType());
        assertEquals(arrivalDate, result2.getArrivalDate());
    }

    @Test
    void testCreateOverride_CeilFloor_WithPercentageSupplement() {
        int propertyId = 123;
        when(uiContext.getPropertyId()).thenReturn(propertyId);
        int principalId = 567;
        when(uiContext.getUserPrincipalId()).thenReturn(principalId);

        CPDecisionBAROutput decisionBARCPOutput = new CPDecisionBAROutput();
        Product product = new Product();
        decisionBARCPOutput.setProduct(product);
        int decisionId = 111;
        decisionBARCPOutput.setDecisionId(decisionId);
        int lengthOfStay = 1;
        decisionBARCPOutput.setLengthOfStay(lengthOfStay);
        BigDecimal finalBar = new BigDecimal(100);
        decisionBARCPOutput.setFinalBAR(finalBar);
        AccomType accomType = new AccomType();
        decisionBARCPOutput.setAccomType(accomType);
        LocalDate arrivalDate = new LocalDate();
        decisionBARCPOutput.setArrivalDate(arrivalDate);

        CPOverrideWrapper overrideWrapper = new CPOverrideWrapper();
        DecisionOverrideType overrideType = DecisionOverrideType.FLOORANDCEIL;
        overrideWrapper.setOriginalOverrideType(overrideType);
        BigDecimal originalFloorOverride = new BigDecimal(103);
        overrideWrapper.setOriginalFloorOverride(originalFloorOverride);
        BigDecimal originalCeilingOverride = new BigDecimal(104);
        overrideWrapper.setOriginalCeilingOverride(originalCeilingOverride);
        BigDecimal floorOverride = new BigDecimal(100);
        overrideWrapper.setFloorOverride(floorOverride);
        BigDecimal ceilingOverride = new BigDecimal(150);
        overrideWrapper.setCeilingOverride(ceilingOverride);
        overrideWrapper.setRoundedBAR(ceilingOverride);

        AccomTypeSupplementValue accomTypeSupplementValue = new AccomTypeSupplementValue();
        accomTypeSupplementValue.setValue(BigDecimal.valueOf(50));
        accomTypeSupplementValue.setOffsetMethod(OffsetMethod.PERCENTAGE);

        BigDecimal ceilingOverrideWithoutSupplement = Supplement.removeSupplementFrom(ceilingOverride, accomTypeSupplementValue.getValue(), true);
        BigDecimal floorOverrideWithoutSupplement = Supplement.removeSupplementFrom(floorOverride, accomTypeSupplementValue.getValue(), true);
        BigDecimal finalBarWithoutSupplement = Supplement.removeSupplementFrom(finalBar, accomTypeSupplementValue.getValue(), true);
        BigDecimal originalCeilingOverrideWithoutSupplement = Supplement.removeSupplementFrom(originalCeilingOverride, accomTypeSupplementValue.getValue(), true);
        BigDecimal originalFloorOverrideWithoutSupplement = Supplement.removeSupplementFrom(originalFloorOverride, accomTypeSupplementValue.getValue(), true);

        //Supplement is 50%
        when(cpDecisionContext.getSupplement(decisionBARCPOutput)).thenReturn(BigDecimal.valueOf(50));
        when(cpDecisionContext.getAccomTypeSupplementValue(decisionBARCPOutput)).thenReturn(accomTypeSupplementValue);

        CPDecisionBAROutputOverride result = pricingOverrideManager.createOverride(decisionBARCPOutput, overrideWrapper, cpDecisionContext);
        assertEquals(propertyId, result.getPropertyId());
        assertEquals(product, result.getProduct());
        assertEquals(principalId, result.getUser());
        assertEquals(decisionId, result.getDecisionId());
        assertEquals(lengthOfStay, result.getLengthOfStay());
        assertEquals(DecisionOverrideType.FLOORANDCEIL, result.getNewOverrideType());
        assertEquals(overrideType, result.getOldOverrideType());
        assertEquals(finalBarWithoutSupplement, result.getOldUserOverride().setScale(2, RoundingMode.HALF_UP));
        assertEquals(originalFloorOverrideWithoutSupplement, result.getOldFloorRate().setScale(2, RoundingMode.HALF_UP));
        assertEquals(originalCeilingOverrideWithoutSupplement, result.getOldCeilingRate().setScale(2, RoundingMode.HALF_UP));
        assertNull(result.getNewUserOverride());
        assertEquals(floorOverrideWithoutSupplement, result.getNewFloorRate().setScale(2, RoundingMode.HALF_UP));
        assertEquals(ceilingOverrideWithoutSupplement, result.getNewCeilingRate().setScale(2, RoundingMode.HALF_UP));
        assertEquals(accomType, result.getAccomType());
        assertEquals(arrivalDate, result.getArrivalDate());

        //Supplement is 10%
        accomTypeSupplementValue.setValue(BigDecimal.valueOf(10));
        accomTypeSupplementValue.setOffsetMethod(OffsetMethod.PERCENTAGE);

        BigDecimal ceilingOverrideMinusSupplement = Supplement.removeSupplementFrom(ceilingOverride, accomTypeSupplementValue.getValue(), true);
        BigDecimal floorOverrideMinusSupplement = Supplement.removeSupplementFrom(floorOverride, accomTypeSupplementValue.getValue(), true);
        BigDecimal finalBarMinusSupplement = Supplement.removeSupplementFrom(finalBar, accomTypeSupplementValue.getValue(), true);
        BigDecimal originalCeilingOverrideMinusSupplement = Supplement.removeSupplementFrom(originalCeilingOverride, accomTypeSupplementValue.getValue(), true);
        BigDecimal originalFloorOverrideMinusSupplement = Supplement.removeSupplementFrom(originalFloorOverride, accomTypeSupplementValue.getValue(), true);

        when(cpDecisionContext.getSupplement(decisionBARCPOutput)).thenReturn(BigDecimal.valueOf(10));
        when(cpDecisionContext.getAccomTypeSupplementValue(decisionBARCPOutput)).thenReturn(accomTypeSupplementValue);

        CPDecisionBAROutputOverride result2 = pricingOverrideManager.createOverride(decisionBARCPOutput, overrideWrapper, cpDecisionContext);
        assertEquals(propertyId, result2.getPropertyId());
        assertEquals(product, result2.getProduct());
        assertEquals(principalId, result2.getUser());
        assertEquals(decisionId, result2.getDecisionId());
        assertEquals(lengthOfStay, result2.getLengthOfStay());
        assertEquals(DecisionOverrideType.FLOORANDCEIL, result2.getNewOverrideType());
        assertEquals(overrideType, result2.getOldOverrideType());
        assertEquals(finalBarMinusSupplement.setScale(1, RoundingMode.HALF_UP), result2.getOldUserOverride().setScale(1, RoundingMode.HALF_UP));
        assertEquals(originalFloorOverrideMinusSupplement, result2.getOldFloorRate().setScale(2, RoundingMode.HALF_UP));
        assertEquals(originalCeilingOverrideMinusSupplement, result2.getOldCeilingRate().setScale(2, RoundingMode.HALF_UP));
        assertNull(result2.getNewUserOverride());
        assertEquals(floorOverrideMinusSupplement, result2.getNewFloorRate().setScale(2, RoundingMode.HALF_UP));
        assertEquals(ceilingOverrideMinusSupplement, result2.getNewCeilingRate().setScale(2, RoundingMode.HALF_UP));
        assertEquals(accomType, result2.getAccomType());
        assertEquals(arrivalDate, result2.getArrivalDate());
    }

    @Test
    public void clearPendingOverrides() {
        HashMap<CPDecisionBAROutput, CPOverrideWrapper> map = new HashMap<>();
        CPDecisionBAROutput cpDecisionBAROutput = new CPDecisionBAROutput();
        CPOverrideWrapper overrideWrapper = buildCpOverrideWrapperWithOverrides(cpDecisionBAROutput);
        map.put(cpDecisionBAROutput, overrideWrapper);

        pricingOverrideManager.setOverridesMap(map);

        List<CPOverrideWrapper> wrapperList = new ArrayList<>();
        wrapperList.add(overrideWrapper);
        pricingOverrideManager.setProductOverrides(wrapperList);

        pricingOverrideManager.clearPendingOverrides();

        assertTrue(pricingOverrideManager.getOverridesMap().isEmpty());
        assertTrue(pricingOverrideManager.getProductOverrides().isEmpty());
    }

    @Test
    public void getCPBarDecisionUIWrappers() {
        List<CPBARDecisionUIWrapper> list = new ArrayList<>();
        pricingOverrideManager.setResults(null);
        assertEquals(list, pricingOverrideManager.getCPBarDecisionUIWrappers());

        pricingOverrideManager.setResults(list);
        assertEquals(list, pricingOverrideManager.getCPBarDecisionUIWrappers());

        CPBARDecisionDTO cpbarDecisionDTO = new CPBARDecisionDTO();
        CPBARDecisionUIWrapper uiWrapper = new CPBARDecisionUIWrapper(cpbarDecisionDTO, false);
        list.add(uiWrapper);
        assertEquals(list, pricingOverrideManager.getCPBarDecisionUIWrappers());
    }

    @Test
    public void getOrderedAndFilteredTypes() {
        CPPricingFilter cpPricingFilter = new CPPricingFilter();
        AccomType accomType = createRoomType(1, createAccomClass());
        accomType.setDisplayStatusId(1);
        cpPricingFilter.setSelectedRoomTypes(new HashSet<>(Collections.singletonList(accomType)));
        pricingOverrideManager.setCpPricingFilter(cpPricingFilter);
        pricingOverrideManager.setShowOnlyBaseRoomTypesFlag(true);
        pricingOverrideManager.setBaseRoomTypeList(Collections.singletonList(accomType));
        assertEquals(accomType, pricingOverrideManager.getOrderedAndFilteredTypes().iterator().next());

        CPPricingFilter cpPricingFilter2 = new CPPricingFilter();
        pricingOverrideManager.setCpPricingFilter(cpPricingFilter2);
        pricingOverrideManager.setBaseRoomTypeList(Collections.singletonList(accomType));
        assertEquals(accomType, pricingOverrideManager.getOrderedAndFilteredTypes().iterator().next());

        CPPricingFilter cpPricingFilter3 = new CPPricingFilter();
        AccomClass accomClass = createAccomClass();
        accomClass.setAccomTypes(new HashSet<>(Collections.singletonList(accomType)));
        cpPricingFilter3.setSelectedRoomClass(accomClass);
        pricingOverrideManager.setCpPricingFilter(cpPricingFilter3);
        pricingOverrideManager.setShowOnlyBaseRoomTypesFlag(false);
        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.ROOM_TYPE_RECODING_UI_ENABLED)).thenReturn(true);

        assertEquals(accomType, pricingOverrideManager.getOrderedAndFilteredTypes().iterator().next());

        CPPricingFilter cpPricingFilter4 = new CPPricingFilter();
        cpPricingFilter4.setSelectedRoomTypes(new HashSet<>(Collections.singletonList(accomType)));
        pricingOverrideManager.setCpPricingFilter(cpPricingFilter4);
        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.ROOM_TYPE_RECODING_UI_ENABLED)).thenReturn(false);
        assertEquals(accomType, pricingOverrideManager.getOrderedAndFilteredTypes().iterator().next());
    }

    @Test
    public void getOrderedFilteredBaseRoomTypes() {
        CPPricingFilter cpPricingFilter1 = new CPPricingFilter();
        AccomType accomType = createRoomType(1, createAccomClass());
        AccomType accomType2 = createRoomType(2, createAccomClass());
        cpPricingFilter1.setSelectedRoomTypes(new HashSet<>(Arrays.asList(accomType2, accomType)));

        Set<AccomType> orderedFilteredBaseRoomTypes1 = pricingOverrideManager.getOrderedFilteredBaseRoomTypes(cpPricingFilter1, Collections.singletonList(accomType));
        assertEquals(2, orderedFilteredBaseRoomTypes1.size());
        assertTrue(orderedFilteredBaseRoomTypes1.contains(accomType));
        assertTrue(orderedFilteredBaseRoomTypes1.contains(accomType2));

        CPPricingFilter cpPricingFilter2 = new CPPricingFilter();
        Set<AccomType> orderedFilteredBaseRoomTypes2 = pricingOverrideManager.getOrderedFilteredBaseRoomTypes(cpPricingFilter2, Collections.singletonList(accomType));
        assertEquals(1, orderedFilteredBaseRoomTypes2.size());
        assertTrue(orderedFilteredBaseRoomTypes2.contains(accomType));
    }


    @Test
    public void getOrderedAndFilteredAccomTypes() {
        AccomType accomType = createRoomType(1, createAccomClass());
        AccomType accomType2 = createRoomType(2, createAccomClass());
        accomType2.setDisplayStatusId(2);
        CPPricingFilter cpPricingFilter1 = new CPPricingFilter();
        AccomClass accomClass = createAccomClass();
        accomClass.setAccomTypes(new HashSet<>(Arrays.asList(accomType, accomType2)));
        cpPricingFilter1.setSelectedRoomClass(accomClass);
        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.ROOM_TYPE_RECODING_UI_ENABLED)).thenReturn(true);
        Set<AccomType> orderedAndFilteredAccomTypes = pricingOverrideManager.getOrderedAndFilteredAccomTypes(cpPricingFilter1);
        assertEquals(1, orderedAndFilteredAccomTypes.size());
        assertTrue(orderedAndFilteredAccomTypes.contains(accomType));

        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.ROOM_TYPE_RECODING_UI_ENABLED)).thenReturn(false);
        Set<AccomType> orderedAndFilteredAccomTypes2 = pricingOverrideManager.getOrderedAndFilteredAccomTypes(cpPricingFilter1);
        assertEquals(2, orderedAndFilteredAccomTypes2.size());
        assertTrue(orderedAndFilteredAccomTypes2.contains(accomType));
        assertTrue(orderedAndFilteredAccomTypes2.contains(accomType2));
    }

    @Test
    public void getOrderedFilteredRoomTypes() {
        CPPricingFilter cpPricingFilter1 = new CPPricingFilter();
        AccomType accomType = createRoomType(1, createAccomClass());
        AccomType accomType2 = createRoomType(2, createAccomClass());
        cpPricingFilter1.setSelectedRoomTypes(new HashSet<>(Arrays.asList(accomType2, accomType)));
        Set<AccomType> orderedFilteredRoomTypes = pricingOverrideManager.getOrderedFilteredRoomTypes(cpPricingFilter1);
        assertEquals(2, orderedFilteredRoomTypes.size());
        assertEquals(accomType, orderedFilteredRoomTypes.iterator().next());
    }

    @Test
    public void getFilteredRoomTypes() {
        CPPricingFilter cpPricingFilter1 = new CPPricingFilter();
        AccomType accomType = createRoomType(1, createAccomClass());
        AccomType accomType2 = createRoomType(2, createAccomClass());
        cpPricingFilter1.setSelectedRoomTypes(new HashSet<>(Arrays.asList(accomType, accomType2)));
        Set<AccomType> filteredRoomTypes1 = pricingOverrideManager.getFilteredRoomTypes(cpPricingFilter1);
        assertEquals(2, filteredRoomTypes1.size());
        assertTrue(filteredRoomTypes1.contains(accomType));
        assertTrue(filteredRoomTypes1.contains(accomType2));

        CPPricingFilter cpPricingFilter2 = new CPPricingFilter();
        AccomClass accomClass = new AccomClass();
        accomClass.setAccomTypes(new HashSet<>(Arrays.asList(accomType, accomType2)));
        cpPricingFilter2.setSelectedRoomClass(accomClass);
        Set<AccomType> filteredRoomTypes2 = pricingOverrideManager.getFilteredRoomTypes(cpPricingFilter2);
        assertEquals(2, filteredRoomTypes2.size());
        assertTrue(filteredRoomTypes2.contains(accomType));
        assertTrue(filteredRoomTypes2.contains(accomType2));

        CPPricingFilter cpPricingFilter3 = new CPPricingFilter();
        assertEquals(new HashSet<>(), pricingOverrideManager.getFilteredRoomTypes(cpPricingFilter3));
    }

    @Test
    public void testSimplifiedAnalyzeChanges() {
        PricingOverrideManager pricingOverrideManager = Mockito.spy(this.pricingOverrideManager);

        Map<CPDecisionBAROutput, CPOverrideWrapper> samplePendingOverrides = getSamplePendingOverrides();
        Mockito.when(pricingOverrideManager.getPendingBAROverrides()).thenReturn(samplePendingOverrides);
        Mockito.when(pricingOverrideManager.createWhatIfOverrides(anyMapOf(CPDecisionBAROutput.class, CPOverrideWrapper.class))).thenReturn(new ArrayList<>());
        SimplifiedWhatIfResult result = new SimplifiedWhatIfResult();
        Mockito.when(simplifiedWhatIfService.analyzeChanges(anyListOf(BAROverride.class))).thenReturn(result);
        doReturn(new HashMap<Date, String>()).when(pricingOverrideManager).getDateOverrideIconHtmlMap(samplePendingOverrides);

        pricingOverrideManager.simplifiedAnalyzeChanges();

        verify(simplifiedWhatIfService, times(1)).analyzeChanges(anyListOf(BAROverride.class));
    }

    @Test
    public void testSimplifiedAnalyzeChangesPopulatesOverrideIconHtmlInformation() {
        PricingOverrideManager pricingOverrideManager = Mockito.spy(this.pricingOverrideManager);

        CPDecisionBAROutput cpDecisionBAROutput = new CPDecisionBAROutput();
        cpDecisionBAROutput.setArrivalDate(LOCAL_DATE);

        CPOverrideWrapper cpOverrideWrapper = new CPOverrideWrapper();
        Map<CPDecisionBAROutput, CPOverrideWrapper> pendingOverrides = new HashMap<>();
        pendingOverrides.put(cpDecisionBAROutput, cpOverrideWrapper);

        Map<Date, String> expectedDateOverRideIconHtmlMap = new HashMap<>();
        expectedDateOverRideIconHtmlMap.put(LOCAL_DATE.toDate(), OVERRIDE_ICON_HTML);
        List<BAROverride> whatIfEligibleOverrides = new ArrayList<>();
        BAROverride barOverride = new BAROverride();
        barOverride.setArrivalDate(LOCAL_DATE.toDate());
        whatIfEligibleOverrides.add(barOverride);

        doReturn(pendingOverrides).when(pricingOverrideManager).getPendingBAROverrides();
        doReturn(whatIfEligibleOverrides).when(pricingOverrideManager).createWhatIfOverrides(pendingOverrides);
        doReturn(expectedDateOverRideIconHtmlMap).when(pricingOverrideManager).getDateOverrideIconHtmlMap(pendingOverrides);

        SimplifiedWhatIfResult simplifiedWhatIfResult = new SimplifiedWhatIfResult();
        List<SimplifiedWhatIfDateData> dateDataList = new ArrayList<>();
        SimplifiedWhatIfDateData dateData = new SimplifiedWhatIfDateData();
        dateData.setDate(LOCAL_DATE.toDate());
        dateDataList.add(dateData);
        simplifiedWhatIfResult.setDateData(dateDataList);
        when(simplifiedWhatIfService.analyzeChanges(whatIfEligibleOverrides)).thenReturn(simplifiedWhatIfResult);

        SimplifiedWhatIfResult result = pricingOverrideManager.simplifiedAnalyzeChanges();

        assertEquals(OVERRIDE_ICON_HTML, result.getDateData().get(0).getOverrideIconsHtml());
    }

    @Test
    public void testCreateWhatIfOverrides() {
        LocalDate arrivalDate = new LocalDate();
        CPDecisionBAROutput output = createCpDecisionBAROutput(arrivalDate);
        CPOverrideWrapper cpOverrideWrapper = createCpOverrideWrapper();
        Map<CPDecisionBAROutput, CPOverrideWrapper> outputMap = new HashMap<>();
        outputMap.put(output, cpOverrideWrapper);
        AccomTypeSupplementValue accomTypeSupplementValue = getAccomTypeSupplementValue(DateUtil.convertJodaLocalDateToLocalDate(arrivalDate), OffsetMethod.FIXED_OFFSET , new BigDecimal("5.0"));
        when(supplementService.getSupplementValueFor(output.getProduct().getId(), output.getArrivalDate(), output.getAccomType().getId())).thenReturn(accomTypeSupplementValue);
        List<BAROverride> results = pricingOverrideManager.createWhatIfOverrides(outputMap);

        assertEquals(1, results.size());
        BAROverride result = results.get(0);
        assertEquals(arrivalDate.toDate(), result.getArrivalDate());
        assertEquals(new BigDecimal("95.0"), result.getFloorRate());
        assertEquals(new BigDecimal("195.0"), result.getCeilRate());
        assertEquals(new BigDecimal("145.0"), result.getSpecificRate());
        assertEquals(1, result.getAccomTypeId());
        assertFalse(result.isRemove());
    }
    @Test
    public void testCreateWhatIfOverridesWhenSupplementPercentageEnabledTrueAndPercentageSupplementAdded() {
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_PERCENTAGE_FOR_SUPPLEMENT)).thenReturn(true);
        java.time.LocalDate arrivalDate = java.time.LocalDate.now();
        CPDecisionBAROutput output = createCpDecisionBAROutput(DateUtil.convertJavaToJodaLocalDate(arrivalDate));
        CPOverrideWrapper cpOverrideWrapper = createCpOverrideWrapper();
        Map<CPDecisionBAROutput, CPOverrideWrapper> outputMap = new HashMap<>();
        outputMap.put(output, cpOverrideWrapper);
        AccomTypeSupplementValue accomTypeSupplementValue = getAccomTypeSupplementValue(arrivalDate, OffsetMethod.PERCENTAGE, new BigDecimal("5.0"));
        when(supplementService.getSupplementValueFor(output.getProduct().getId(), output.getArrivalDate(), output.getAccomType().getId())).thenReturn(accomTypeSupplementValue);
        List<BAROverride> results = pricingOverrideManager.createWhatIfOverrides(outputMap);

        assertEquals(1, results.size());
        BAROverride result = results.get(0);
        assertEquals(Date.from(arrivalDate.atStartOfDay(ZoneId.systemDefault()).toInstant()), result.getArrivalDate());
        assertEquals(new BigDecimal("95.24"), result.getFloorRate());
        assertEquals(new BigDecimal("190.48"), result.getCeilRate());
        assertEquals(new BigDecimal("142.86"), result.getSpecificRate());
        assertEquals(1, result.getAccomTypeId());
        assertFalse(result.isRemove());
    }
    @Test
    public void testCreateWhatIfOverridesWhenSupplementPercentageEnabledTrueAndFixedOffsetSupplementAdded() {
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_PERCENTAGE_FOR_SUPPLEMENT)).thenReturn(true);
        java.time.LocalDate arrivalDate = java.time.LocalDate.now();
        CPDecisionBAROutput output = createCpDecisionBAROutput(DateUtil.convertJavaToJodaLocalDate(arrivalDate));
        CPOverrideWrapper cpOverrideWrapper = createCpOverrideWrapper();
        Map<CPDecisionBAROutput, CPOverrideWrapper> outputMap = new HashMap<>();
        outputMap.put(output, cpOverrideWrapper);

        AccomTypeSupplementValue accomTypeSupplementValue = getAccomTypeSupplementValue(arrivalDate, OffsetMethod.FIXED_OFFSET , new BigDecimal("5.0"));
        when(supplementService.getSupplementValueFor(output.getProduct().getId(), output.getArrivalDate(), output.getAccomType().getId())).thenReturn(accomTypeSupplementValue);

        List<BAROverride> results = pricingOverrideManager.createWhatIfOverrides(outputMap);

        assertEquals(1, results.size());
        BAROverride result = results.get(0);
        assertEquals(Date.from(arrivalDate.atStartOfDay(ZoneId.systemDefault()).toInstant()), result.getArrivalDate());
        assertEquals(new BigDecimal("95.0"), result.getFloorRate());
        assertEquals(new BigDecimal("195.0"), result.getCeilRate());
        assertEquals(new BigDecimal("145.0"), result.getSpecificRate());
        assertEquals(1, result.getAccomTypeId());
        assertFalse(result.isRemove());
    }
    @Test
    public void testCreateWhatIfOverridesWhenSupplementPercentageEnabledTrueAndSupplementNotAdded() {
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_PERCENTAGE_FOR_SUPPLEMENT)).thenReturn(true);
        java.time.LocalDate arrivalDate = java.time.LocalDate.now();
        CPDecisionBAROutput output = createCpDecisionBAROutput(DateUtil.convertJavaToJodaLocalDate(arrivalDate));
        CPOverrideWrapper cpOverrideWrapper = createCpOverrideWrapper();
        Map<CPDecisionBAROutput, CPOverrideWrapper> outputMap = new HashMap<>();
        outputMap.put(output, cpOverrideWrapper);
        when(supplementService.getSupplementValueFor(output.getProduct().getId(), output.getArrivalDate(), output.getAccomType().getId())).thenReturn(null);
        List<BAROverride> results = pricingOverrideManager.createWhatIfOverrides(outputMap);

        assertEquals(1, results.size());
        BAROverride result = results.get(0);
        assertEquals(Date.from(arrivalDate.atStartOfDay(ZoneId.systemDefault()).toInstant()), result.getArrivalDate());
        assertEquals(new BigDecimal(100), result.getFloorRate());
        assertEquals(new BigDecimal(200), result.getCeilRate());
        assertEquals(new BigDecimal(150), result.getSpecificRate());
        assertEquals(1, result.getAccomTypeId());
        assertFalse(result.isRemove());
    }

    @Test
    public void testCreateWhatIfOverridesForGroupFloor() {
        LocalDate arrivalDate = new LocalDate();
        CPDecisionBAROutput output = createCpDecisionBAROutput(arrivalDate);
        CPDecisionBAROutput barOutput = mock(CPDecisionBAROutput.class);
        when(barOutput.getAccomType()).thenReturn(createRoomType(1, createAccomClass()));
        CPOverrideWrapper cpOverrideWrapper = new CPOverrideWrapper(barOutput, true);
        cpOverrideWrapper.setGroupFloorOverride(BigDecimal.ZERO);
        cpOverrideWrapper.setCeilingOverride(BigDecimal.ONE);
        cpOverrideWrapper.setStartDate(arrivalDate);
        cpOverrideWrapper.setEndDate(arrivalDate);
        Map<CPDecisionBAROutput, CPOverrideWrapper> outputMap = new HashMap<>();
        outputMap.put(output, cpOverrideWrapper);
        AccomTypeSupplementValue accomTypeSupplementValue = getAccomTypeSupplementValue(DateUtil.convertJodaLocalDateToLocalDate(arrivalDate), OffsetMethod.FIXED_OFFSET , new BigDecimal("5.0"));
        when(supplementService.getSupplementValueFor(output.getProduct().getId(), output.getArrivalDate(), output.getAccomType().getId())).thenReturn(accomTypeSupplementValue);
        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.IS_GROUP_FLOOR_OVERRIDE_ENABLED)).thenReturn(true);
        GroupFloorOverride groupFloorOverride = new GroupFloorOverride();
        GroupFloorOverrideReason overrideReason = mock(GroupFloorOverrideReason.class);
        when(overrideReason.getId()).thenReturn(2);
        groupFloorOverride.setOverrideReason(overrideReason);
        groupFloorOverride.setPrettyGroupRate(BigDecimal.TEN);
        when(groupFloorOverrideService.getGroupFloorOverridesFor(createAccomClass(), arrivalDate, arrivalDate)).
                thenReturn(Collections.singletonList(groupFloorOverride));

        List<BAROverride> results = pricingOverrideManager.createWhatIfOverrides(outputMap);

        assertEquals(1, results.size());
        BAROverride result = results.get(0);
        assertTrue(result.isGPFloor());
        assertEquals(arrivalDate.toDate(), result.getArrivalDate());
        assertEquals(new BigDecimal(5).setScale(2, RoundingMode.HALF_UP), result.getFloorRate());
        assertEquals(1, result.getAccomTypeId());
        assertFalse(result.isRemove());
    }

    @Test
    public void saveOrDeleteProductOverrides_pendingSave() {
        ProductRateOffsetOverride pendingSaveOverride1 = new ProductRateOffsetOverride();
        pendingSaveOverride1.setId(1);
        ProductRateOffsetOverride pendingDeleteOverride1 = new ProductRateOffsetOverride();
        pendingDeleteOverride1.setId(2);
        ProductRateOffsetOverride pendingSaveOverride2 = new ProductRateOffsetOverride();
        pendingSaveOverride2.setId(3);
        ProductRateOffsetOverride pendingDeleteOverride2 = new ProductRateOffsetOverride();
        pendingDeleteOverride2.setId(4);

        CPDecisionBAROutput cpDecisionBAROutput = new CPDecisionBAROutput();

        Product agileRatesProduct = new Product();
        agileRatesProduct.setCode(Product.AGILE_RATES_PRODUCT_CODE);
        cpDecisionBAROutput.setProduct(agileRatesProduct);

        CPOverrideWrapper pendingSaveOverrideWrapper = buildCpOverrideWrapperWithOverrides(cpDecisionBAROutput);
        pendingSaveOverrideWrapper.setIsPendingSave(true);
        pendingSaveOverrideWrapper.setIsPendingDelete(false);
        pendingSaveOverrideWrapper.setOriginalProductRateOffsetOverrides(List.of(pendingDeleteOverride1));
        pendingSaveOverrideWrapper.setProductRateOffsetOverrides(List.of(pendingSaveOverride1));
        CPOverrideWrapper notPendingOverrideWrapper = buildCpOverrideWrapperWithOverrides(cpDecisionBAROutput);
        notPendingOverrideWrapper.setIsPendingSave(false);
        notPendingOverrideWrapper.setIsPendingDelete(false);
        notPendingOverrideWrapper.setOriginalProductRateOffsetOverrides(List.of(pendingDeleteOverride2));
        notPendingOverrideWrapper.setProductRateOffsetOverrides(List.of(pendingSaveOverride2));


        List<CPOverrideWrapper> wrapperList = new ArrayList<>();
        wrapperList.add(pendingSaveOverrideWrapper);
        wrapperList.add(notPendingOverrideWrapper);

        //productOverrides empty
        pricingOverrideManager.setProductOverrides(new ArrayList<>());
        pricingOverrideManager.saveOrDeleteProductOverrides();
        verify(service, never()).deleteProductRateOffsetOverride(anyList());
        verify(service, never()).saveProductRateOffsetOverride(anyList());

        //productOverrides pending save
        pricingOverrideManager.setProductOverrides(wrapperList);
        pricingOverrideManager.saveOrDeleteProductOverrides();
        verify(service, times(1)).deleteProductRateOffsetOverride(List.of(pendingDeleteOverride1));
        verify(service, times(1)).saveProductRateOffsetOverride(List.of(pendingSaveOverride1));
    }

    @Test
    public void saveOrDeleteProductOverrides_pendingDelete_BAR() {
        CPPricingFilter cpPricingFilter = new CPPricingFilter();
        cpPricingFilter.setApplyOverridesToAllRoomClasses(true);
        pricingOverrideManager.setCpPricingFilter(cpPricingFilter);

        ProductRateOffsetOverride pendingSaveOverride1 = new ProductRateOffsetOverride();
        pendingSaveOverride1.setId(1);
        ProductRateOffsetOverride pendingDeleteOverride1 = new ProductRateOffsetOverride();
        pendingDeleteOverride1.setId(2);
        ProductRateOffsetOverride pendingSaveOverride2 = new ProductRateOffsetOverride();
        pendingSaveOverride2.setId(3);
        ProductRateOffsetOverride pendingDeleteOverride2 = new ProductRateOffsetOverride();
        pendingDeleteOverride2.setId(4);

        CPDecisionBAROutput cpDecisionBAROutput = new CPDecisionBAROutput();
        Product product = new Product();
        product.setSystemDefault(true);
        product.setOptimized(false);
        product.setCode(Product.BAR);
        cpDecisionBAROutput.setProduct(product);

        CPOverrideWrapper pendingDeleteOverrideWrapper = buildCpOverrideWrapperWithOverrides(cpDecisionBAROutput);
        pendingDeleteOverrideWrapper.setIsPendingSave(false);
        pendingDeleteOverrideWrapper.setIsPendingDelete(true);
        pendingDeleteOverrideWrapper.setOriginalProductRateOffsetOverrides(List.of(pendingDeleteOverride1));
        pendingDeleteOverrideWrapper.setProductRateOffsetOverrides(List.of(pendingSaveOverride1));
        CPOverrideWrapper notPendingOverrideWrapper = buildCpOverrideWrapperWithOverrides(cpDecisionBAROutput);
        notPendingOverrideWrapper.setIsPendingSave(false);
        notPendingOverrideWrapper.setIsPendingDelete(false);
        notPendingOverrideWrapper.setOriginalProductRateOffsetOverrides(List.of(pendingDeleteOverride2));
        notPendingOverrideWrapper.setProductRateOffsetOverrides(List.of(pendingSaveOverride2));

        List<CPOverrideWrapper> wrapperList = new ArrayList<>();
        wrapperList.add(pendingDeleteOverrideWrapper);
        wrapperList.add(notPendingOverrideWrapper);


        //productOverrides empty
        pricingOverrideManager.setProductOverrides(new ArrayList<>());
        pricingOverrideManager.saveOrDeleteProductOverrides();
        verify(service, never()).deleteProductRateOffsetOverride(anyList());
        verify(service, never()).saveProductRateOffsetOverride(anyList());

        //productOverrides pending delete
        pricingOverrideManager.setProductOverrides(wrapperList);
        pricingOverrideManager.saveOrDeleteProductOverrides();
        verify(service, times(1)).deleteProductRateOffsetOverride(List.of(pendingSaveOverride1));
        verify(service, never()).saveProductRateOffsetOverride(anyList());
    }

    @Test
    public void saveOrDeleteProductOverrides_pendingDelete_NonOptimized_ApplyToAll() {
        CPPricingFilter cpPricingFilter = new CPPricingFilter();
        cpPricingFilter.setApplyOverridesToAllRoomClasses(true);
        pricingOverrideManager.setCpPricingFilter(cpPricingFilter);

        ProductRateOffsetOverride pendingSaveOverride1 = new ProductRateOffsetOverride();
        pendingSaveOverride1.setId(1);
        ProductRateOffsetOverride pendingDeleteOverride1 = new ProductRateOffsetOverride();
        pendingDeleteOverride1.setId(2);
        ProductRateOffsetOverride pendingSaveOverride2 = new ProductRateOffsetOverride();
        pendingSaveOverride2.setId(3);
        ProductRateOffsetOverride pendingDeleteOverride2 = new ProductRateOffsetOverride();
        pendingDeleteOverride2.setId(4);

        CPDecisionBAROutput cpDecisionBAROutput = new CPDecisionBAROutput();
        Product product = new Product();
        product.setSystemDefault(false);
        product.setOptimized(false);
        product.setCode(Product.AGILE_RATES_PRODUCT_CODE);
        cpDecisionBAROutput.setProduct(product);

        CPOverrideWrapper pendingDeleteOverrideWrapper = buildCpOverrideWrapperWithOverrides(cpDecisionBAROutput);
        pendingDeleteOverrideWrapper.setIsPendingSave(false);
        pendingDeleteOverrideWrapper.setIsPendingDelete(true);
        pendingDeleteOverrideWrapper.setOriginalProductRateOffsetOverrides(List.of(pendingDeleteOverride1));
        pendingDeleteOverrideWrapper.setProductRateOffsetOverrides(List.of(pendingSaveOverride1));
        CPOverrideWrapper notPendingOverrideWrapper = buildCpOverrideWrapperWithOverrides(cpDecisionBAROutput);
        notPendingOverrideWrapper.setIsPendingSave(false);
        notPendingOverrideWrapper.setIsPendingDelete(false);
        notPendingOverrideWrapper.setOriginalProductRateOffsetOverrides(List.of(pendingDeleteOverride2));
        notPendingOverrideWrapper.setProductRateOffsetOverrides(List.of(pendingSaveOverride2));

        List<CPOverrideWrapper> wrapperList = new ArrayList<>();
        wrapperList.add(pendingDeleteOverrideWrapper);
        wrapperList.add(notPendingOverrideWrapper);


        //productOverrides empty
        pricingOverrideManager.setProductOverrides(new ArrayList<>());
        pricingOverrideManager.saveOrDeleteProductOverrides();
        verify(service, never()).deleteProductRateOffsetOverride(anyList());
        verify(service, never()).saveProductRateOffsetOverride(anyList());

        //productOverrides pending delete
        pricingOverrideManager.setProductOverrides(wrapperList);
        pricingOverrideManager.saveOrDeleteProductOverrides();
        verify(service, times(1)).deleteProductRateOffsetOverride(List.of(pendingDeleteOverride1));
        verify(service, never()).saveProductRateOffsetOverride(anyList());
    }

    @Test
    public void saveOrDeleteProductOverrides_pendingDelete_NonOptimized_NotApplyToAll() {
        CPPricingFilter cpPricingFilter = new CPPricingFilter();
        cpPricingFilter.setApplyOverridesToAllRoomClasses(false);
        pricingOverrideManager.setCpPricingFilter(cpPricingFilter);

        ProductRateOffsetOverride pendingSaveOverride1 = new ProductRateOffsetOverride();
        pendingSaveOverride1.setId(1);
        ProductRateOffsetOverride pendingDeleteOverride1 = new ProductRateOffsetOverride();
        pendingDeleteOverride1.setId(2);
        ProductRateOffsetOverride pendingSaveOverride2 = new ProductRateOffsetOverride();
        pendingSaveOverride2.setId(3);
        ProductRateOffsetOverride pendingDeleteOverride2 = new ProductRateOffsetOverride();
        pendingDeleteOverride2.setId(4);

        CPDecisionBAROutput cpDecisionBAROutput = new CPDecisionBAROutput();
        Product product = new Product();
        product.setSystemDefault(false);
        product.setOptimized(false);
        product.setCode(Product.AGILE_RATES_PRODUCT_CODE);
        cpDecisionBAROutput.setProduct(product);

        CPOverrideWrapper pendingDeleteOverrideWrapper = buildCpOverrideWrapperWithOverrides(cpDecisionBAROutput);
        pendingDeleteOverrideWrapper.setIsPendingSave(false);
        pendingDeleteOverrideWrapper.setIsPendingDelete(true);
        pendingDeleteOverrideWrapper.setOriginalProductRateOffsetOverrides(List.of(pendingDeleteOverride1));
        pendingDeleteOverrideWrapper.setProductRateOffsetOverrides(List.of(pendingSaveOverride1));
        CPOverrideWrapper notPendingOverrideWrapper = buildCpOverrideWrapperWithOverrides(cpDecisionBAROutput);
        notPendingOverrideWrapper.setIsPendingSave(false);
        notPendingOverrideWrapper.setIsPendingDelete(false);
        notPendingOverrideWrapper.setOriginalProductRateOffsetOverrides(List.of(pendingDeleteOverride2));
        notPendingOverrideWrapper.setProductRateOffsetOverrides(List.of(pendingSaveOverride2));

        List<CPOverrideWrapper> wrapperList = new ArrayList<>();
        wrapperList.add(pendingDeleteOverrideWrapper);
        wrapperList.add(notPendingOverrideWrapper);


        //productOverrides empty
        pricingOverrideManager.setProductOverrides(new ArrayList<>());
        pricingOverrideManager.saveOrDeleteProductOverrides();
        verify(service, never()).deleteProductRateOffsetOverride(anyList());
        verify(service, never()).saveProductRateOffsetOverride(anyList());

        //productOverrides pending delete
        pricingOverrideManager.setProductOverrides(wrapperList);
        pricingOverrideManager.saveOrDeleteProductOverrides();
        verify(service, times(1)).deleteProductRateOffsetOverride(List.of(pendingSaveOverride1));
        verify(service, never()).saveProductRateOffsetOverride(anyList());
    }

    @Test
    public void saveOrDeleteProductOverrides_pendingDelete_Optimized() {
        CPPricingFilter cpPricingFilter = new CPPricingFilter();
        cpPricingFilter.setApplyOverridesToAllRoomClasses(true);
        pricingOverrideManager.setCpPricingFilter(cpPricingFilter);

        ProductRateOffsetOverride pendingSaveOverride1 = new ProductRateOffsetOverride();
        pendingSaveOverride1.setId(1);
        ProductRateOffsetOverride pendingDeleteOverride1 = new ProductRateOffsetOverride();
        pendingDeleteOverride1.setId(2);
        ProductRateOffsetOverride pendingSaveOverride2 = new ProductRateOffsetOverride();
        pendingSaveOverride2.setId(3);
        ProductRateOffsetOverride pendingDeleteOverride2 = new ProductRateOffsetOverride();
        pendingDeleteOverride2.setId(4);

        CPDecisionBAROutput cpDecisionBAROutput = new CPDecisionBAROutput();
        Product product = new Product();
        product.setSystemDefault(false);
        product.setOptimized(true);
        cpDecisionBAROutput.setProduct(product);

        CPOverrideWrapper pendingDeleteOverrideWrapper = buildCpOverrideWrapperWithOverrides(cpDecisionBAROutput);
        pendingDeleteOverrideWrapper.setIsPendingSave(false);
        pendingDeleteOverrideWrapper.setIsPendingDelete(true);
        pendingDeleteOverrideWrapper.setOriginalProductRateOffsetOverrides(List.of(pendingDeleteOverride1));
        pendingDeleteOverrideWrapper.setProductRateOffsetOverrides(List.of(pendingSaveOverride1));
        CPOverrideWrapper notPendingOverrideWrapper = buildCpOverrideWrapperWithOverrides(cpDecisionBAROutput);
        notPendingOverrideWrapper.setIsPendingSave(false);
        notPendingOverrideWrapper.setIsPendingDelete(false);
        notPendingOverrideWrapper.setOriginalProductRateOffsetOverrides(List.of(pendingDeleteOverride2));
        notPendingOverrideWrapper.setProductRateOffsetOverrides(List.of(pendingSaveOverride2));

        List<CPOverrideWrapper> wrapperList = new ArrayList<>();
        wrapperList.add(pendingDeleteOverrideWrapper);
        wrapperList.add(notPendingOverrideWrapper);


        //productOverrides empty
        pricingOverrideManager.setProductOverrides(new ArrayList<>());
        pricingOverrideManager.saveOrDeleteProductOverrides();
        verify(service, never()).deleteProductRateOffsetOverride(anyList());
        verify(service, never()).saveProductRateOffsetOverride(anyList());

        //productOverrides pending delete
        pricingOverrideManager.setProductOverrides(wrapperList);
        pricingOverrideManager.saveOrDeleteProductOverrides();
        verify(service, times(1)).deleteProductRateOffsetOverride(List.of(pendingDeleteOverride1));
        verify(service, never()).saveProductRateOffsetOverride(anyList());
    }

    @Test
    public void saveOverrides_multiProduct() {
        PricingOverrideManager pricingOverrideManager = Mockito.spy(this.pricingOverrideManager);

        LocalDate localDate = new LocalDate();
        Date caughtUpDate = localDate.toDate();
        Date businessDate = localDate.toDate();
        Date unqualifiedRateCaughtUpDate = localDate.toDate();
        Date webrateShoppingDate = localDate.toDate();
        CPDecisionBAROutput cpDecisionBAROutput = new CPDecisionBAROutput();
        CPOverrideWrapper overrideWrapper = new CPOverrideWrapper();
        CPDecisionBAROutputOverride override = new CPDecisionBAROutputOverride();

        doReturn(override).when(pricingOverrideManager).createOverride(cpDecisionBAROutput, overrideWrapper, cpDecisionContext);

        pricingOverrideManager.saveOverrides(true, caughtUpDate, businessDate, unqualifiedRateCaughtUpDate, webrateShoppingDate, cpDecisionContext, cpDecisionBAROutput, overrideWrapper);
        verify(service, times(1)).saveOverrideForCPDecision(cpDecisionContext, override, cpDecisionBAROutput, false, caughtUpDate, businessDate, unqualifiedRateCaughtUpDate, webrateShoppingDate);
        //We're not testing the useMultiProductProductPerformanceChanges = false path because its being removed in the near future
        verify(service, never()).saveOverrideForCPDecision(cpDecisionContext, override, cpDecisionBAROutput, false);
    }

    @Test
    public void saveOverrides() {
        PricingOverrideManager pricingOverrideManager = Mockito.spy(this.pricingOverrideManager);

        LocalDate localDate = new LocalDate();
        Date caughtUpDate = localDate.toDate();
        Date businessDate = localDate.toDate();
        Date unqualifiedRateCaughtUpDate = localDate.toDate();
        Date webrateShoppingDate = localDate.toDate();
        CPDecisionBAROutput cpDecisionBAROutput = new CPDecisionBAROutput();
        List<CPDecisionBAROutput> cpDecisionBAROutputList = new ArrayList<>();
        cpDecisionBAROutputList.add(cpDecisionBAROutput);

        CPDecisionBAROutputOverride override = new CPDecisionBAROutputOverride();
        CPOverrideWrapper overrideWrapper = new CPOverrideWrapper();
        Map<CPDecisionBAROutput, CPOverrideWrapper> cpOverrideWrapperMap = new HashMap<>();
        cpOverrideWrapperMap.put(cpDecisionBAROutput, overrideWrapper);

        doReturn(override).when(pricingOverrideManager).createOverride(cpDecisionBAROutput, overrideWrapper, cpDecisionContext);
        Map<CPDecisionBAROutput, CPDecisionBAROutputOverride> cpDecisionBAROutputOverrideMap = new HashMap<>();
        cpDecisionBAROutputOverrideMap.put(cpDecisionBAROutput, override);

        pricingOverrideManager.saveOverrides(caughtUpDate, businessDate, unqualifiedRateCaughtUpDate,
                webrateShoppingDate, cpDecisionContext,
                cpDecisionBAROutputList, cpOverrideWrapperMap);
        verify(service, times(1)).saveOverrideForCPDecisions(cpDecisionContext, cpDecisionBAROutputOverrideMap, caughtUpDate, businessDate, unqualifiedRateCaughtUpDate, webrateShoppingDate);
    }

    @Test
    public void hasChangesFor() {
        HashMap<CPDecisionBAROutput, CPOverrideWrapper> map = new HashMap<>();
        CPDecisionBAROutput cpDecisionBAROutput = new CPDecisionBAROutput();
        CPOverrideWrapper overrideWrapper = buildCpOverrideWrapperWithOverrides(cpDecisionBAROutput);

        Predicate<CPOverrideWrapper> cpOverrideWrapperPredicate = p -> true;
        pricingOverrideManager.setOverridesMap(map);

        //empty overridesMap
        assertFalse(pricingOverrideManager.hasChangesFor(cpOverrideWrapperPredicate));

        //no match
        cpOverrideWrapperPredicate = p -> false;
        map.put(cpDecisionBAROutput, overrideWrapper);
        pricingOverrideManager.setOverridesMap(map);
        assertFalse(pricingOverrideManager.hasChangesFor(cpOverrideWrapperPredicate));

        //match
        cpOverrideWrapperPredicate = p -> true;
        assertTrue(pricingOverrideManager.hasChangesFor(cpOverrideWrapperPredicate));
    }

    @Test
    public void isOptimizedAgileRateProduct() {
        Product barProduct = new Product();
        barProduct.setSystemDefault(true);
        barProduct.setOptimized(false);
        Product optimizedProduct = new Product();
        optimizedProduct.setSystemDefault(false);
        optimizedProduct.setOptimized(true);
        Product nonOptimizedProduct = new Product();
        nonOptimizedProduct.setSystemDefault(false);
        nonOptimizedProduct.setOptimized(false);

        assertFalse(pricingOverrideManager.isOptimizedAgileRateProduct(barProduct));
        assertTrue(pricingOverrideManager.isOptimizedAgileRateProduct(optimizedProduct));
        assertFalse(pricingOverrideManager.isOptimizedAgileRateProduct(nonOptimizedProduct));
    }

    @Test
    public void isNonOptimizedAndApplyRemoveForAllRoomClasses() {
        Product barProduct = new Product();
        barProduct.setSystemDefault(true);
        barProduct.setOptimized(false);
        barProduct.setCode(Product.BAR);
        Product optimizedProduct = new Product();
        optimizedProduct.setSystemDefault(false);
        optimizedProduct.setOptimized(true);
        optimizedProduct.setCode(Product.AGILE_RATES_PRODUCT_CODE);
        Product nonOptimizedProduct = new Product();
        nonOptimizedProduct.setSystemDefault(false);
        nonOptimizedProduct.setOptimized(false);
        nonOptimizedProduct.setCode(Product.AGILE_RATES_PRODUCT_CODE);

        CPPricingFilter cpPricingFilter = new CPPricingFilter();
        cpPricingFilter.setApplyOverridesToAllRoomClasses(false);
        pricingOverrideManager.setCpPricingFilter(cpPricingFilter);

        assertFalse(pricingOverrideManager.isNonOptimizedAndApplyRemoveForAllRoomClasses(barProduct));
        assertFalse(pricingOverrideManager.isNonOptimizedAndApplyRemoveForAllRoomClasses(optimizedProduct));
        assertFalse(pricingOverrideManager.isNonOptimizedAndApplyRemoveForAllRoomClasses(nonOptimizedProduct));

        cpPricingFilter.setApplyOverridesToAllRoomClasses(true);

        assertFalse(pricingOverrideManager.isNonOptimizedAndApplyRemoveForAllRoomClasses(barProduct));
        assertFalse(pricingOverrideManager.isNonOptimizedAndApplyRemoveForAllRoomClasses(optimizedProduct));
        assertTrue(pricingOverrideManager.isNonOptimizedAndApplyRemoveForAllRoomClasses(nonOptimizedProduct));
    }

    public CPDecisionBAROutput createCpDecisionBAROutput(LocalDate arrivalDate) {
        CPDecisionBAROutput output = new CPDecisionBAROutput();
        output.setArrivalDate(arrivalDate);
        output.setLengthOfStay(5);
        AccomType accomType = createRoomType(1, createAccomClass());
        output.setAccomType(accomType);
        Product product = new Product();
        product.setId(1);
        product.setSystemDefault(true);
        product.setCode(Product.BAR);
        output.setProduct(product);
        return output;
    }

    public CPOverrideWrapper createCpOverrideWrapper() {
        CPOverrideWrapper cpOverrideWrapper = new CPOverrideWrapper();
        cpOverrideWrapper.setFloorOverride(new BigDecimal(100.0));
        cpOverrideWrapper.setCeilingOverride(new BigDecimal(200.0));
        cpOverrideWrapper.setSpecificOverride(new BigDecimal(150.0));
        cpOverrideWrapper.setIsPendingDelete(false);
        return cpOverrideWrapper;
    }

    @Test
    public void testGetDateOverrideIconHtmlMap() {
        PricingOverrideManager pricingOverrideManager = Mockito.spy(this.pricingOverrideManager);

        CPDecisionBAROutput cpDecisionBAROutput = new CPDecisionBAROutput();
        cpDecisionBAROutput.setArrivalDate(LOCAL_DATE);
        CPOverrideWrapper cpOverrideWrapper = new CPOverrideWrapper();
        Map<CPDecisionBAROutput, CPOverrideWrapper> whatIfEligibleOverrides = new HashMap<>();
        whatIfEligibleOverrides.put(cpDecisionBAROutput, cpOverrideWrapper);
        Map<Date, String> expectedDateOverRideIconHtmlMap = new HashMap<>();
        expectedDateOverRideIconHtmlMap.put(LOCAL_DATE.toDate(), OVERRIDE_ICON_HTML);

        CPPricingManagementOverrideHtmlConverter mockCpPricingManagementOverrideHtmlConverter = mock(CPPricingManagementOverrideHtmlConverter.class);
        when(mockCpPricingManagementOverrideHtmlConverter.convertToPresentation(any(), any(), any(), anyBoolean(), anyBoolean())).thenReturn(OVERRIDE_ICON_HTML);

        doReturn(mockCpPricingManagementOverrideHtmlConverter).when(pricingOverrideManager).getCpPricingManagementOverrideHtmlConverter();
        doReturn(LOCAL_DATE).when(pricingOverrideManager).getSystemDateAsLocalDate();

        Map<Date, String> actualDateOverRideIconHtmlMap = pricingOverrideManager.getDateOverrideIconHtmlMap(whatIfEligibleOverrides);

        assertEquals(expectedDateOverRideIconHtmlMap, actualDateOverRideIconHtmlMap);
    }

    @Test
    public void testDoNotShowDuplicateOverrideIconsForSameArrivalDate() {
        PricingOverrideManager pricingOverrideManager = Mockito.spy(this.pricingOverrideManager);

        Map<CPDecisionBAROutput, CPOverrideWrapper> whatIfEligibleOverrides = new HashMap<>();
        CPOverrideWrapper cpOverrideWrapper = new CPOverrideWrapper();
        CPDecisionBAROutput cpDecisionBAROutput1 = new CPDecisionBAROutput();
        cpDecisionBAROutput1.setArrivalDate(LOCAL_DATE);
        cpDecisionBAROutput1.setSpecificOverride(BigDecimal.valueOf(100));
        whatIfEligibleOverrides.put(cpDecisionBAROutput1, cpOverrideWrapper);
        CPDecisionBAROutput cpDecisionBAROutput2 = new CPDecisionBAROutput();
        cpDecisionBAROutput2.setArrivalDate(LOCAL_DATE);
        cpDecisionBAROutput2.setSpecificOverride(BigDecimal.valueOf(101));
        whatIfEligibleOverrides.put(cpDecisionBAROutput2, cpOverrideWrapper);

        Map<Date, String> expectedDateOverRideIconHtmlMap = new HashMap<>();
        expectedDateOverRideIconHtmlMap.put(LOCAL_DATE.toDate(), OVERRIDE_ICON_HTML);

        CPPricingManagementOverrideHtmlConverter mockCpPricingManagementOverrideHtmlConverter = mock(CPPricingManagementOverrideHtmlConverter.class);
        when(mockCpPricingManagementOverrideHtmlConverter.convertToPresentation(any(), any(), any(), anyBoolean(), anyBoolean())).thenReturn(OVERRIDE_ICON_HTML);

        doReturn(mockCpPricingManagementOverrideHtmlConverter).when(pricingOverrideManager).getCpPricingManagementOverrideHtmlConverter();
        doReturn(LOCAL_DATE).when(pricingOverrideManager).getSystemDateAsLocalDate();

        Map<Date, String> actualDateOverRideIconHtmlMap = pricingOverrideManager.getDateOverrideIconHtmlMap(whatIfEligibleOverrides);

        assertEquals(expectedDateOverRideIconHtmlMap, actualDateOverRideIconHtmlMap);
    }

    @Test
    public void getSystemDateAsLocalDate() {
        LocalDate localDate = new LocalDate();
        when(uiContext.getSystemCaughtUpDateAsLocalDate()).thenReturn(localDate);
        assertEquals(localDate, pricingOverrideManager.getSystemDateAsLocalDate());
    }

    @Test
    public void testPopulateIfAbsent() {
        CPConfiguration cpConfiguration = new CPConfiguration();
        when(pricingConfigurationService.findCPConfiguration()).thenReturn(cpConfiguration);
        when(pricingConfigurationService.getBaseOccupancyType()).thenReturn(OccupancyType.DOUBLE);

        PricingRule pricingRule = new PricingRule();
        Map<Integer, PricingRule> pricingRules = new HashMap<>();
        pricingRules.put(1, pricingRule);
        when(prettyPricingService.getPricingRules()).thenReturn(pricingRules);

        Tax tax = new Tax();
        when(taxService.findTax()).thenReturn(tax);

        List<OccupantBucketEntity> occupantBucketEntities = new ArrayList();
        when(perPersonPricingService.getOccupantBuckets()).thenReturn(occupantBucketEntities);

        List<MaximumOccupantsEntity> maximumOccupantsEntities = new ArrayList<>();
        when(pricingConfigurationService.getMaximumEntitiesList()).thenReturn(maximumOccupantsEntities);

        List<ProductPackage> productPackages = new ArrayList<>();
        when(pricingConfigurationService.getActiveProductPackages()).thenReturn(productPackages);

        pricingOverrideManager.populateIfAbsent();

        assertEquals(cpConfiguration, pricingOverrideManager.getCpConfiguration());
        assertEquals(OccupancyType.DOUBLE, pricingOverrideManager.getBaseOccupancyType());
        assertEquals(pricingRules, pricingOverrideManager.getPricingRules());
        assertEquals(tax, pricingOverrideManager.getTax());
        assertEquals(occupantBucketEntities, pricingOverrideManager.getOccupantBucketEntities());
        assertEquals(maximumOccupantsEntities, pricingOverrideManager.getMaximumOccupantsEntitiesList());
        assertEquals(productPackages, pricingOverrideManager.getUngroupedProductPackages());
    }

    @Test
    public void testCPConfigurationGetterAndSetter() {
        final CPConfiguration cpConfiguration = new CPConfiguration();
        pricingOverrideManager.setCpConfiguration(cpConfiguration);
        assertEquals(cpConfiguration, pricingOverrideManager.getCpConfiguration());
    }

    @Test
    public void testBaseOccupancyTypeGetterAndSetter() {
        pricingOverrideManager.setBaseOccupancyType(OccupancyType.DOUBLE);
        assertEquals(OccupancyType.DOUBLE, pricingOverrideManager.getBaseOccupancyType());
    }

    @Test
    public void testPricingRuleGetterAndSetter() {
        final Map<Integer, PricingRule> pricingRules = new HashMap<>();
        final PricingRule pricingRule = new PricingRule();
        pricingRules.put(1, pricingRule);
        pricingOverrideManager.setPricingRules(pricingRules);
        assertEquals(pricingRules, pricingOverrideManager.getPricingRules());
    }

    @Test
    public void testTaxGetterAndSetter() {
        final Tax tax = new Tax();
        pricingOverrideManager.setTax(tax);
        assertEquals(tax, pricingOverrideManager.getTax());
    }

    @Test
    public void testOccupantBucketEntitiesGetterAndSetter() {
        final ArrayList<OccupantBucketEntity> occupantBucketEntities = new ArrayList<>();
        pricingOverrideManager.setOccupantBucketEntities(occupantBucketEntities);
        assertEquals(occupantBucketEntities, pricingOverrideManager.getOccupantBucketEntities());
    }

    @Test
    public void testMaximumOccupantsEntitiesGetterAndSetter() {
        final ArrayList<MaximumOccupantsEntity> maximumOccupantsEntitiesList = new ArrayList<>();
        pricingOverrideManager.setMaximumOccupantsEntitiesList(maximumOccupantsEntitiesList);
        assertEquals(maximumOccupantsEntitiesList, pricingOverrideManager.getMaximumOccupantsEntitiesList());
    }

    @Test
    public void testUngroupedProductPackagesGetterAndSetter() {
        final ArrayList<ProductPackage> ungroupedProductPackages = new ArrayList<>();
        pricingOverrideManager.setUngroupedProductPackages(ungroupedProductPackages);
        assertEquals(ungroupedProductPackages, pricingOverrideManager.getUngroupedProductPackages());
    }

    @Test
    public void testSaveOverrideChangesForRemove() {
        PricingAccomClass masterPricingAccomClass = new PricingAccomClass();
        LocalDate date = new LocalDate();
        CPDecisionBAROutput cpDecisionBAROutput = mock(CPDecisionBAROutput.class);
        CPDecisionContext cpDecisionContext = mock(CPDecisionContext.class);
        CPOverrideWrapper wrapper = mock(CPOverrideWrapper.class);
        Decision decision = new Decision();
        decision.setId(1);

        when(wrapper.getOriginalOverrideType()).thenReturn(DecisionOverrideType.FLOORANDCEIL);
        when(cpDecisionBAROutput.getArrivalDate()).thenReturn(date);
        when(pricingConfigurationService.getCPDecisionContext(date, date, false)).thenReturn(cpDecisionContext);
        when(wrapper.getCpDecisionBAROutput()).thenReturn(cpDecisionBAROutput);
        when(wrapper.isPendingDelete()).thenReturn(true);
        Product product = new Product();
        product.setId(1);
        product.setSystemDefault(true);
        product.setCode(Product.BAR);
        when(wrapper.getProduct()).thenReturn(product);
        when(cpDecisionBAROutput.getProduct()).thenReturn(product);
        when(decisionService.createBAROverrideDecision()).thenReturn(decision);

        pricingOverrideManager.setOverridesMap(Stream.of(wrapper).collect(Collectors.toMap(item -> cpDecisionBAROutput, item -> item)));

        pricingOverrideManager.setProductOverrides(Collections.emptyList());
        pricingOverrideManager.saveOverrideChanges(masterPricingAccomClass);

        verify(service).removeOverride(cpDecisionContext, cpDecisionBAROutput, 1, 0, false, true, true);
    }

    @Test
    public void testApplyOverride() {
        CPOverrideWrapper cpOverrideWrapper = mock(CPOverrideWrapper.class);
        CPOverrideWrapper nonBaseRTWrapper = mock(CPOverrideWrapper.class);
        CPDecisionBAROutput nonBaseRTBAROutput = mock(CPDecisionBAROutput.class);
        CPDecisionContext cpDecisionContext = mock(CPDecisionContext.class);
        CPDecisionBAROutput cpDecisionBAROutput = mock(CPDecisionBAROutput.class);
        CPConfigMergedCeilingAndFloor cpConfigMergedCeilingAndFloor = mock(CPConfigMergedCeilingAndFloor.class);
        PricingAccomClass masterPricingAccomClass = new PricingAccomClass();
        LocalDate localDate = new LocalDate();
        AccomClass accomClass = new AccomClass();
        accomClass.setMasterClass(1);
        Product product = new Product();
        product.setId(1);

        when(dateService.getBusinessDate()).thenReturn(localDate.toDate());
        when(dateService.getUnqualifiedRateCaughtUpDate()).thenReturn(localDate.toDate());
        when(cpOverrideWrapper.getSpecificOverride()).thenReturn(null);
        when(cpOverrideWrapper.getFloorOverride()).thenReturn(BigDecimal.TEN);
        when(cpOverrideWrapper.getCeilingOverride()).thenReturn(BigDecimal.TEN);
        when(cpOverrideWrapper.getCpDecisionBAROutput()).thenReturn(cpDecisionBAROutput);
        when(cpDecisionContext.getPricingAccomClasses()).thenReturn(Stream.of(masterPricingAccomClass).collect(Collectors.toMap(item -> accomClass, item -> item)));
        when(cpOverrideWrapper.isBaseRoomType()).thenReturn(true);
        when(cpOverrideWrapper.getNonBaseRoomTypeOverrideWrappers()).thenReturn(Collections.singletonList(nonBaseRTWrapper));
        when(cpDecisionContext.applyOffset(any(), any())).thenReturn(BigDecimal.TEN);
        when(cpDecisionContext.getPrimaryProduct(any())).thenReturn(product);
        when(cpDecisionContext.calculatePrettyPrice(any(), any())).thenReturn(BigDecimal.TEN);
        when(cpDecisionContext.getCeilingAndFloor(any(CPDecisionBAROutput.class))).thenReturn(cpConfigMergedCeilingAndFloor);
        when(cpConfigMergedCeilingAndFloor.getFloorRate()).thenReturn(BigDecimal.TEN);
        when(cpConfigMergedCeilingAndFloor.getCeilingRate()).thenReturn(BigDecimal.TEN);
        when(nonBaseRTWrapper.getCpDecisionBAROutput()).thenReturn(nonBaseRTBAROutput);
        when(nonBaseRTWrapper.getSpecificOverride()).thenReturn(BigDecimal.TEN);
        pricingOverrideManager.setShowOnlyBaseRoomTypesFlag(true);

        Optional<AccomTypeSupplementValue> supplement = Optional.of(new AccomTypeSupplementValue());
        when(cpDecisionContext.getSupplementFor(nonBaseRTBAROutput)).thenReturn(supplement);

        pricingOverrideManager.applyOverride(cpOverrideWrapper, Collections.emptyList(),
                false, false, false, false,
                localDate, cpDecisionContext, null);
        verify(nonBaseRTWrapper).setRoundedBAR(any());
        verify(nonBaseRTBAROutput).setPrettyBAR(any());
        verify(nonBaseRTBAROutput).setRoomsOnlyBAR(any());
        verify(nonBaseRTBAROutput).setFinalBAR(any());
    }

    @Test
    public void testApplyOverrideInlineEdit() {
        CPOverrideWrapper cpOverrideWrapper = mock(CPOverrideWrapper.class);
        CPOverrideWrapper nonBaseRTWrapper = mock(CPOverrideWrapper.class);
        CPDecisionBAROutput nonBaseRTBAROutput = mock(CPDecisionBAROutput.class);
        CPDecisionContext cpDecisionContext = mock(CPDecisionContext.class);
        CPDecisionBAROutput cpDecisionBAROutput = mock(CPDecisionBAROutput.class);
        CPConfigMergedCeilingAndFloor cpConfigMergedCeilingAndFloor = mock(CPConfigMergedCeilingAndFloor.class);
        PricingAccomClass masterPricingAccomClass = new PricingAccomClass();
        LocalDate localDate = new LocalDate();
        AccomClass accomClass = new AccomClass();
        accomClass.setMasterClass(1);
        AccomType accomType = new AccomType();
        AccomType nonBaseAccomType = new AccomType();
        Product product = new Product();
        product.setId(1);

        when(dateService.getBusinessDate()).thenReturn(localDate.toDate());
        when(dateService.getUnqualifiedRateCaughtUpDate()).thenReturn(localDate.toDate());
        when(cpOverrideWrapper.getSpecificOverride()).thenReturn(null);
        when(cpOverrideWrapper.getFloorOverride()).thenReturn(BigDecimal.TEN);
        when(cpOverrideWrapper.getCeilingOverride()).thenReturn(BigDecimal.TEN);
        when(cpOverrideWrapper.getCpDecisionBAROutput()).thenReturn(cpDecisionBAROutput);
        when(cpDecisionBAROutput.getAccomType()).thenReturn(accomType);
        when(cpDecisionContext.getPricingAccomClasses()).thenReturn(Stream.of(masterPricingAccomClass).collect(Collectors.toMap(item -> accomClass, item -> item)));
        when(cpOverrideWrapper.isBaseRoomType()).thenReturn(true);
        when(cpOverrideWrapper.getNonBaseRoomTypeOverrideWrappers()).thenReturn(Collections.singletonList(nonBaseRTWrapper));
        when(cpDecisionContext.applyOffset(any(), any())).thenReturn(BigDecimal.TEN);
        when(cpDecisionContext.getPrimaryProduct(any())).thenReturn(product);
        when(cpDecisionContext.calculatePrettyPrice(any(), any())).thenReturn(BigDecimal.TEN);
        when(cpDecisionContext.getCeilingAndFloor(any(CPDecisionBAROutput.class))).thenReturn(cpConfigMergedCeilingAndFloor);
        when(cpConfigMergedCeilingAndFloor.getFloorRate()).thenReturn(BigDecimal.TEN);
        when(cpConfigMergedCeilingAndFloor.getCeilingRate()).thenReturn(BigDecimal.TEN);
        when(nonBaseRTWrapper.getCpDecisionBAROutput()).thenReturn(nonBaseRTBAROutput);
        when(nonBaseRTWrapper.getSpecificOverride()).thenReturn(BigDecimal.TEN);
        when(nonBaseRTBAROutput.getAccomType()).thenReturn(nonBaseAccomType);
        pricingOverrideManager.setShowOnlyBaseRoomTypesFlag(true);

        Optional<AccomTypeSupplementValue> supplement = Optional.of(new AccomTypeSupplementValue());
        when(cpDecisionContext.getSupplementFor(nonBaseRTBAROutput)).thenReturn(supplement);

        pricingOverrideManager.applyOverride(cpOverrideWrapper, Collections.emptyList(),
                false, false, false, false,
                localDate, cpDecisionContext, null);
        verify(cpDecisionContext, times(3)).applyOffset(any(), any());
        verify(cpDecisionContext).getSupplementFor(any(CPDecisionBAROutput.class));
        verify(cpDecisionContext).calculatePrettyPrice(any(), any());
    }

    @Test
    public void testFindWrapperWhenNoResultsFound() {
        CPOverrideWrapper cpOverrideWrapper = new CPOverrideWrapper();
        CPOverrideWrapper wrapper = pricingOverrideManager.findWrapper(cpOverrideWrapper);
        assertEquals(cpOverrideWrapper, wrapper);
        assertTrue(cpOverrideWrapper.isPendingSave());
    }

    @Test
    public void testApplyRemove_shouldRemoveOverrideFromNonBaseRoomsTypeAlso() {
        Product product = new Product();
        CPDecisionBAROutput cpDecisionBAROutput = new CPDecisionBAROutput();
        cpDecisionBAROutput.setProduct(product);
        LocalDate localDate = new LocalDate();
        cpDecisionBAROutput.setArrivalDate(localDate);
        int accomTypeId = 1;
        AccomClass accomClass = new AccomClass();
        AccomType accomType = new AccomType();
        accomType.setAccomClass(accomClass);
        accomType.setId(accomTypeId);
        accomClass.setAccomTypes(Arrays.asList(accomType).stream().collect(Collectors.toSet()));
        cpDecisionBAROutput.setAccomType(accomType);

        CPOverrideWrapper overrideWrapper = new CPOverrideWrapper(cpDecisionBAROutput, false);
        overrideWrapper.setIsBaseRoomType(true);
        BigDecimal ceilingOverride = new BigDecimal("200.00");
        overrideWrapper.setCeilingOverride(ceilingOverride);
        overrideWrapper.setApplyOverrideAcrossRoomTypes(true);
        overrideWrapper.setIsOverridePersisted(true);

        BigDecimal prettyPrice = new BigDecimal("350.74");
        when(pricingConfigurationService.getCPDecisionContext(localDate, localDate, false)).thenReturn(cpDecisionContext);
        when(cpDecisionContext.calculateRoundedRate(cpDecisionBAROutput)).thenReturn(prettyPrice);

        CPBARDecisionDTO cpbarDecisionDTO = new CPBARDecisionDTO();
        List<CPDecisionBAROutput> decisions = new ArrayList<>();
        CPDecisionBAROutput cpDecisionBAROutputForNonBase = new CPDecisionBAROutput();
        cpDecisionBAROutputForNonBase.setProduct(product);
        int nonBaseAccomTypeId = 2;
        AccomType nonBaseAccomType = new AccomType();
        nonBaseAccomType.setId(nonBaseAccomTypeId);
        cpDecisionBAROutputForNonBase.setAccomType(nonBaseAccomType);
        cpDecisionBAROutputForNonBase.setArrivalDate(localDate);
        decisions.add(cpDecisionBAROutputForNonBase);
        cpbarDecisionDTO.setDecisions(decisions);

        CPBARDecisionUIWrapper cpbarDecisionUIWrapper = new CPBARDecisionUIWrapper(cpbarDecisionDTO, false);
        overrideWrapper.setParent(cpbarDecisionUIWrapper);
        when(service.searchForBarDecisionDTO(anyBoolean(), any(), anyBoolean(), any(), any())).thenReturn(List.of(cpbarDecisionDTO));
        pricingOverrideManager.setAccomTypeFiltrationCriteria(item -> true);
        BigDecimal nonBaseRTSupplementValue = new BigDecimal("10.00");
        when(supplementService.getSupplementValue(1, localDate, nonBaseAccomTypeId)).thenReturn(nonBaseRTSupplementValue);

        pricingOverrideManager.setOverridesMap(overridesMap);

        List<PricingAccomClass> pricingAccomClasses = new ArrayList<>();
        PricingAccomClass pricingAccomClass = new PricingAccomClass();
        pricingAccomClass.setAccomClass(accomClass);
        pricingAccomClass.setAccomType(accomType);
        pricingAccomClasses.add(pricingAccomClass);

        CPOverrideWrapper nonBaseRoomTypeWrapper = new CPOverrideWrapper(cpDecisionBAROutput, false);
        nonBaseRoomTypeWrapper.setIsBaseRoomType(false);
        nonBaseRoomTypeWrapper.setCeilingOverride(new BigDecimal("200.00"));
        nonBaseRoomTypeWrapper.setApplyOverrideAcrossRoomTypes(true);
        nonBaseRoomTypeWrapper.setIsOverridePersisted(true);
        nonBaseRoomTypeWrapper.setSpecificOverride(new BigDecimal(24));
        nonBaseRoomTypeWrapper.setCpDecisionBAROutput(cpDecisionBAROutputForNonBase);
        nonBaseRoomTypeWrapper.setParent(cpbarDecisionUIWrapper);

        overrideWrapper.setNonBaseRoomTypeOverrideWrappers(Collections.singletonList(nonBaseRoomTypeWrapper));

        pricingOverrideManager.removeOverrides(overrideWrapper, pricingAccomClasses, null, null, false);

        assertNull(nonBaseRoomTypeWrapper.getSpecificOverride());
    }
    @Test
    void testBuildMapOfInventoryLimit() throws ParseException {
        SimpleDateFormat dateFormat = new SimpleDateFormat(DEFAULT_DATE_FORMAT);
        Date startDate = dateFormat.parse("2024-07-01");
        Date dateBetweenRange = dateFormat.parse("2024-07-02");
        Date anotherDateBetweenRange = dateFormat.parse("2024-07-03");
        Date endDate = dateFormat.parse("2024-07-04");

        List<InventoryLimitDecision> inventoryLimits = new ArrayList<>();
        inventoryLimits.add(getDecisionInventoryLimit(startDate, 10));
        inventoryLimits.add(getDecisionInventoryLimit(dateBetweenRange, 20));
        inventoryLimits.add(getDecisionInventoryLimit(anotherDateBetweenRange, 30));
        inventoryLimits.add(getDecisionInventoryLimit(endDate, 40));

        List<InventoryLimitDecision> overriddenInventoryLimits = new ArrayList<>();
        overriddenInventoryLimits.add(getDecisionInventoryLimitOverride(dateBetweenRange, 200));
        overriddenInventoryLimits.add(getDecisionInventoryLimitOverride(endDate, 400));

        when(inventoryLimitDecisionService.getInventoryLimitDecisionsFromNonPace(startDate, endDate)).thenReturn(inventoryLimits);
        when(inventoryLimitDecisionService.getInventoryLimitDecisionsFromOverride(startDate, endDate)).thenReturn(overriddenInventoryLimits);

        Map<Date, String> actualInventoryLimitMap = pricingOverrideManager.buildMapOfInventoryLimit(startDate, endDate);

        assertEquals(4, actualInventoryLimitMap.size());
        assertEquals("10", actualInventoryLimitMap.get(startDate));
        assertEquals("20" + OVERRIDDEN, actualInventoryLimitMap.get(dateBetweenRange));
        assertEquals("30", actualInventoryLimitMap.get(anotherDateBetweenRange));
        assertEquals("40" + OVERRIDDEN, actualInventoryLimitMap.get(endDate));
    }

    private Map<CPDecisionBAROutput, CPOverrideWrapper> getSamplePendingOverrides() {
        Map<CPDecisionBAROutput, CPOverrideWrapper> map = new HashMap<>();
        addEntry(map, LOCAL_DATE);
        addEntry(map, LOCAL_DATE_PLUS_1);
        addEntry(map, LOCAL_DATE_PLUS_2);
        return map;
    }

    private void addEntry(Map<CPDecisionBAROutput, CPOverrideWrapper> map, LocalDate arrivalDate) {
        CPDecisionBAROutput cpDecisionBAROutput = new CPDecisionBAROutput();
        cpDecisionBAROutput.setArrivalDate(arrivalDate);
        CPOverrideWrapper cpOverrideWrapper = new CPOverrideWrapper();
        map.put(cpDecisionBAROutput, cpOverrideWrapper);
    }

    private CPOverrideWrapper buildCPOverrideWrapper() {
        CPDecisionBAROutput cpDecisionBAROutput = new CPDecisionBAROutput();
        LocalDate localDate = new LocalDate();
        cpDecisionBAROutput.setArrivalDate(localDate);
        int accomTypeId = 1;
        AccomClass accomClass = new AccomClass();
        AccomType accomType = new AccomType();
        accomType.setAccomClass(accomClass);
        accomType.setId(accomTypeId);
        cpDecisionBAROutput.setAccomType(accomType);
        Product product = new Product();
        product.setId(1);
        product.setSystemDefault(true);
        cpDecisionBAROutput.setProduct(product);
        return new CPOverrideWrapper(cpDecisionBAROutput, false);
    }

    private AccomType createAccomType(String code, int status, int capacity, Integer displayStatusId) {
        AccomType at = new AccomType();
        at.setAccomTypeCode(code);
        at.setStatusId(status);
        at.setAccomTypeCapacity(capacity);
        at.setDisplayStatusId(displayStatusId);
        return at;
    }

    @Test
    public void saveOrDeleteProductOverrides_pendingSave_SmallGroup() {
        ProductRateOffsetOverride pendingSaveOverride1 = new ProductRateOffsetOverride();
        pendingSaveOverride1.setId(1);
        ProductRateOffsetOverride pendingDeleteOverride1 = new ProductRateOffsetOverride();
        pendingDeleteOverride1.setId(2);
        ProductRateOffsetOverride pendingSaveOverride2 = new ProductRateOffsetOverride();
        pendingSaveOverride2.setId(3);
        ProductRateOffsetOverride pendingDeleteOverride2 = new ProductRateOffsetOverride();
        pendingDeleteOverride2.setId(4);

        CPDecisionBAROutput cpDecisionBAROutput = new CPDecisionBAROutput();

        Product smallGroupProduct = new Product();
        smallGroupProduct.setOptimized(true);
        smallGroupProduct.setCode(Product.GROUP_PRODUCT_CODE);
        cpDecisionBAROutput.setProduct(smallGroupProduct);

        CPOverrideWrapper pendingSaveOverrideWrapper = buildCpOverrideWrapperWithOverrides(cpDecisionBAROutput);
        pendingSaveOverrideWrapper.setIsPendingSave(true);
        pendingSaveOverrideWrapper.setIsPendingDelete(false);
        pendingSaveOverrideWrapper.setOriginalProductRateOffsetOverrides(List.of(pendingDeleteOverride1));
        pendingSaveOverrideWrapper.setProductRateOffsetOverrides(List.of(pendingSaveOverride1));
        CPOverrideWrapper notPendingOverrideWrapper = buildCpOverrideWrapperWithOverrides(cpDecisionBAROutput);
        notPendingOverrideWrapper.setIsPendingSave(false);
        notPendingOverrideWrapper.setIsPendingDelete(false);
        notPendingOverrideWrapper.setOriginalProductRateOffsetOverrides(List.of(pendingDeleteOverride2));
        notPendingOverrideWrapper.setProductRateOffsetOverrides(List.of(pendingSaveOverride2));


        List<CPOverrideWrapper> wrapperList = new ArrayList<>();
        wrapperList.add(pendingSaveOverrideWrapper);
        wrapperList.add(notPendingOverrideWrapper);

        //productOverrides empty
        pricingOverrideManager.setProductOverrides(new ArrayList<>());
        pricingOverrideManager.saveOrDeleteProductOverrides();
        verify(service, never()).deleteSmallGroupProductRateOffsetOverride(anyList());
        verify(service, never()).saveSmallGroupProductRateOffsetOverride(anyList());

        //productOverrides pending save
        pricingOverrideManager.setProductOverrides(wrapperList);
        pricingOverrideManager.saveOrDeleteProductOverrides();
        verify(service, times(1)).deleteSmallGroupProductRateOffsetOverride(List.of(pendingDeleteOverride1));
        verify(service, times(1)).saveSmallGroupProductRateOffsetOverride(List.of(pendingSaveOverride1));
    }

    @Test
    public void saveOrDeleteProductOverrides_pendingDelete_SmallGroup() {
        CPPricingFilter cpPricingFilter = new CPPricingFilter();
        cpPricingFilter.setApplyOverridesToAllRoomClasses(true);
        pricingOverrideManager.setCpPricingFilter(cpPricingFilter);

        ProductRateOffsetOverride pendingSaveOverride1 = new ProductRateOffsetOverride();
        pendingSaveOverride1.setId(1);
        ProductRateOffsetOverride pendingDeleteOverride1 = new ProductRateOffsetOverride();
        pendingDeleteOverride1.setId(2);
        ProductRateOffsetOverride pendingSaveOverride2 = new ProductRateOffsetOverride();
        pendingSaveOverride2.setId(3);
        ProductRateOffsetOverride pendingDeleteOverride2 = new ProductRateOffsetOverride();
        pendingDeleteOverride2.setId(4);

        CPDecisionBAROutput cpDecisionBAROutput = new CPDecisionBAROutput();
        Product product = new Product();
        product.setSystemDefault(false);
        product.setOptimized(true);
        product.setCode(Product.GROUP_PRODUCT_CODE);
        cpDecisionBAROutput.setProduct(product);

        CPOverrideWrapper pendingDeleteOverrideWrapper = buildCpOverrideWrapperWithOverrides(cpDecisionBAROutput);
        pendingDeleteOverrideWrapper.setIsPendingSave(false);
        pendingDeleteOverrideWrapper.setIsPendingDelete(true);
        pendingDeleteOverrideWrapper.setOriginalProductRateOffsetOverrides(List.of(pendingDeleteOverride1));
        pendingDeleteOverrideWrapper.setProductRateOffsetOverrides(List.of(pendingSaveOverride1));
        CPOverrideWrapper notPendingOverrideWrapper = buildCpOverrideWrapperWithOverrides(cpDecisionBAROutput);
        notPendingOverrideWrapper.setIsPendingSave(false);
        notPendingOverrideWrapper.setIsPendingDelete(false);
        notPendingOverrideWrapper.setOriginalProductRateOffsetOverrides(List.of(pendingDeleteOverride2));
        notPendingOverrideWrapper.setProductRateOffsetOverrides(List.of(pendingSaveOverride2));

        List<CPOverrideWrapper> wrapperList = new ArrayList<>();
        wrapperList.add(pendingDeleteOverrideWrapper);
        wrapperList.add(notPendingOverrideWrapper);


        //productOverrides empty
        pricingOverrideManager.setProductOverrides(new ArrayList<>());
        pricingOverrideManager.saveOrDeleteProductOverrides();
        verify(service, never()).deleteSmallGroupProductRateOffsetOverride(anyList());
        verify(service, never()).saveSmallGroupProductRateOffsetOverride(anyList());

        //productOverrides pending delete
        pricingOverrideManager.setProductOverrides(wrapperList);
        pricingOverrideManager.saveOrDeleteProductOverrides();
        verify(service, times(1)).deleteSmallGroupProductRateOffsetOverride(List.of(pendingDeleteOverride1));
        verify(service, never()).saveSmallGroupProductRateOffsetOverride(anyList());
    }

    @Test
    public void test_getNOVRDecisionsMap() {
        final AccomClass accomClass = createAccomClass();
        AccomType accomType = createRoomType(1, accomClass);
        final CPPricingFilter filter = createFilter(accomClass, accomType);
        filter.setAdditionalInformationFilterEnum1(AdditionalInformationFilterEnum.COMPETITOR);
        accomType.setAccomClass(accomClass);
        CPDecisionBARNOVRDetails output1 = getCPDecisionBARNOVRDetails(accomType, filter.getProducts().iterator().next(), LOCAL_DATE);
        CPDecisionBARNOVRDetails output2 = getCPDecisionBARNOVRDetails(accomType, filter.getProducts().iterator().next(), LOCAL_DATE);
        CPDecisionBARNOVRDetails output3 = getCPDecisionBARNOVRDetails(accomType, filter.getProducts().iterator().next(), LOCAL_DATE);
        CPDecisionBARNOVRDetails output4 = getCPDecisionBARNOVRDetails(accomType, filter.getProducts().iterator().next(), LOCAL_DATE_PLUS_1);
        CPDecisionBARNOVRDetails output5 = getCPDecisionBARNOVRDetails(accomType, filter.getProducts().iterator().next(), LOCAL_DATE_PLUS_1);
        CPDecisionBARNOVRDetails output6 = getCPDecisionBARNOVRDetails(accomType, filter.getProducts().iterator().next(), LOCAL_DATE_PLUS_2);
        when(service.getBaseAccomTypes()).thenReturn(List.of(accomType));
        when(service.getCPDecisionsNOVRBetweenDatesForProductAndRoomTypes(filter.getProducts(), filter.getStartDate(), filter.getEndDate(), List.of(accomType)))
                .thenReturn(List.of(output1, output2, output3, output4, output5, output6));
        pricingOverrideManager.setBaseRoomTypeList(Collections.singletonList(accomType));
        filter.setSelectedRoomClass(createAllAccomClass());
        filter.setInventoryGroupDto(new InventoryGroupDto(-1, "Property", true));
        pricingOverrideManager.setCpPricingFilter(filter);
        Map<LocalDate, List<CPDecisionBARNOVRDetails>> dateMap = pricingOverrideManager.getNOVRDecisionsMap(true);
        assertEquals(3, dateMap.size());
        assertEquals(3, dateMap.get(LOCAL_DATE).size());
        assertEquals(2, dateMap.get(LOCAL_DATE_PLUS_1).size());
        assertEquals(1, dateMap.get(LOCAL_DATE_PLUS_2).size());
    }
    @Test
    void testSaveOrDeleteInventoryLimitOverrides_DeleteInventoryLimit(){
        CPDecisionBAROutput cpDecisionBAROutput = new CPDecisionBAROutput();
        Product smallGroupProduct = new Product();
        smallGroupProduct.setOptimized(true);
        smallGroupProduct.setCode(Product.GROUP_PRODUCT_CODE);
        cpDecisionBAROutput.setProduct(smallGroupProduct);
        CPOverrideWrapper overrideWrapper = buildCpOverrideWrapperWithOverrides(cpDecisionBAROutput);
        overrideWrapper.setInventoryLimitMarkedForDeletion(true);
        Date date = new GregorianCalendar(2022, Calendar.FEBRUARY, 11).getTime();
        overrideWrapper.setInventoryLimitOverrideOccupancyDt(date);
        List<CPOverrideWrapper> wrapperList = new ArrayList<>();
        wrapperList.add(overrideWrapper);
        when(inventoryLimitDecisionService.getExistingActiveDecisionInventoryLimitOverride(date)).thenReturn(new DecisionGPInventoryLimitOverride());
        pricingOverrideManager.setInventoryLimitOverrides(wrapperList);
        pricingOverrideManager.saveOrDeleteInventoryLimitOverrides();
        verify(inventoryLimitDecisionService).getExistingActiveDecisionInventoryLimitOverride(date);
        verify(inventoryLimitDecisionService, times(1)).saveOrUpdateDecisionInventoryLimitOverrides(anyList());
        verify(inventoryLimitDecisionService).registerGroupProductChangedEvent();
    }
    @Test
    void testSaveOrDeleteInventoryLimitOverrides_SaveInventoryLimit(){
        CPDecisionBAROutput cpDecisionBAROutput = new CPDecisionBAROutput();
        Product smallGroupProduct = new Product();
        smallGroupProduct.setOptimized(true);
        smallGroupProduct.setCode(Product.GROUP_PRODUCT_CODE);
        cpDecisionBAROutput.setProduct(smallGroupProduct);
        CPOverrideWrapper overrideWrapper = buildCpOverrideWrapperWithOverrides(cpDecisionBAROutput);
        overrideWrapper.setOriginalInventoryLimitOverride(null);
        overrideWrapper.setInventoryLimit(10);
        overrideWrapper.setInventoryLimitOverride(20);
        overrideWrapper.setInventoryLimitMarkedForDeletion(false);
        Date date = new GregorianCalendar(2022, Calendar.FEBRUARY, 11).getTime();
        overrideWrapper.setInventoryLimitOverrideOccupancyDt(date);
        List<CPOverrideWrapper> wrapperList = new ArrayList<>();
        wrapperList.add(overrideWrapper);
        pricingOverrideManager.setInventoryLimitOverrides(wrapperList);
        Decision decision = new Decision();
        decision.setId(1);
        when(decisionService.createInventoryLimitOverrideDecision()).thenReturn(decision);
        pricingOverrideManager.saveOrDeleteInventoryLimitOverrides();
        verify(inventoryLimitDecisionService).createDecisionInventoryLimitOverride(date, 10,20, 1);
        verify(inventoryLimitDecisionService, times(1)).saveOrUpdateDecisionInventoryLimitOverrides(anyList());
        verify(inventoryLimitDecisionService).registerGroupProductChangedEvent();
    }
    @Test
    void testSaveOrDeleteInventoryLimitOverrides_UpdateInventoryLimit(){
        CPDecisionBAROutput cpDecisionBAROutput = new CPDecisionBAROutput();
        Product smallGroupProduct = new Product();
        smallGroupProduct.setOptimized(true);
        smallGroupProduct.setCode(Product.GROUP_PRODUCT_CODE);
        cpDecisionBAROutput.setProduct(smallGroupProduct);
        CPOverrideWrapper overrideWrapper = buildCpOverrideWrapperWithOverrides(cpDecisionBAROutput);
        overrideWrapper.setOriginalInventoryLimitOverride(15);
        overrideWrapper.setInventoryLimit(10);
        overrideWrapper.setInventoryLimitOverride(20);
        overrideWrapper.setInventoryLimitMarkedForDeletion(false);
        Date date = new GregorianCalendar(2022, Calendar.FEBRUARY, 11).getTime();
        overrideWrapper.setInventoryLimitOverrideOccupancyDt(date);
        List<CPOverrideWrapper> wrapperList = new ArrayList<>();
        wrapperList.add(overrideWrapper);
        pricingOverrideManager.setInventoryLimitOverrides(wrapperList);
        Decision decision = new Decision();
        decision.setId(1);
        when(decisionService.createInventoryLimitOverrideDecision()).thenReturn(decision);
        when(inventoryLimitDecisionService.getExistingActiveDecisionInventoryLimitOverride(date)).thenReturn(new DecisionGPInventoryLimitOverride());
        pricingOverrideManager.saveOrDeleteInventoryLimitOverrides();
        verify(inventoryLimitDecisionService).getExistingActiveDecisionInventoryLimitOverride(date);
        verify(inventoryLimitDecisionService).createDecisionInventoryLimitOverride(date, 10,20, 1);
        verify(inventoryLimitDecisionService, times(2)).saveOrUpdateDecisionInventoryLimitOverrides(anyList());
        verify(inventoryLimitDecisionService).registerGroupProductChangedEvent();
    }
    @Test
    void testGetOverriddenInventoryLimits() {
        Date startDate = new GregorianCalendar(2022, Calendar.FEBRUARY, 11).getTime();
        Date endDate = new GregorianCalendar(2022, Calendar.FEBRUARY, 15).getTime();
        pricingOverrideManager.getOverriddenInventoryLimits(startDate, endDate);
        verify(inventoryLimitDecisionService).getInventoryLimitDecisionsFromOverride(startDate, endDate);
    }
    @Test
    void testGetInventoryLimitDecisions() {
        Date startDate = new GregorianCalendar(2022, Calendar.FEBRUARY, 11).getTime();
        Date endDate = new GregorianCalendar(2022, Calendar.FEBRUARY, 15).getTime();
        pricingOverrideManager.getInventoryLimitDecisions(startDate, endDate);
        verify(inventoryLimitDecisionService).getInventoryLimitDecisionsFromNonPace(startDate, endDate);
    }
    @Test
    void testGetBusinessAnalysisDailyDataDtos() {
        PricingAccomClass masterPricingAccomClass = new PricingAccomClass();
        when(service.getMasterPricingAccomClass()).thenReturn(masterPricingAccomClass);
        Date startDate = new GregorianCalendar(2022, Calendar.FEBRUARY, 11).getTime();
        Date endDate = new GregorianCalendar(2022, Calendar.FEBRUARY, 15).getTime();
        Date caughtUpDate = new GregorianCalendar(2022, Calendar.FEBRUARY, 12).getTime();
        pricingOverrideManager.getBusinessAnalysisDailyDataDtos(caughtUpDate, startDate, endDate);
        verify(service).getBusinessAnalysisDailyDataDtos(masterPricingAccomClass,caughtUpDate, startDate, endDate);
    }
    private CPDecisionBARNOVRDetails getCPDecisionBARNOVRDetails(AccomType accomType, Product product, LocalDate arrivalDate) {
        CPDecisionBARNOVRDetails output = new CPDecisionBARNOVRDetails();
        output.setAccomType(accomType);
        output.setProduct(product);
        output.setArrivalDate(arrivalDate);
        return output;
    }
    InventoryLimitDecision getDecisionInventoryLimit (Date date, int limit) {
        DecisionGPInventoryLimit decisionGPInventoryLimit = new DecisionGPInventoryLimit();
        decisionGPInventoryLimit.setId(1L);
        decisionGPInventoryLimit.setDecisionId(100);
        decisionGPInventoryLimit.setPropertyId(PacmanWorkContextHelper.getPropertyId());
        decisionGPInventoryLimit.setOccupancyDate(date);
        decisionGPInventoryLimit.setInventoryLimit(limit);
        decisionGPInventoryLimit.setCreateDate(date);
        return new InventoryLimitDecision(decisionGPInventoryLimit);
    }
    InventoryLimitDecision getDecisionInventoryLimitOverride(Date date, int limit){
        DecisionGPInventoryLimitOverride decisionGPInventoryLimitOverride = new DecisionGPInventoryLimitOverride();
        decisionGPInventoryLimitOverride.setId(1L);
        decisionGPInventoryLimitOverride.setDecisionId(100);
        decisionGPInventoryLimitOverride.setPropertyId(PacmanWorkContextHelper.getPropertyId());
        decisionGPInventoryLimitOverride.setOccupancyDate(date);
        decisionGPInventoryLimitOverride.setInventoryLimit(20);
        decisionGPInventoryLimitOverride.setCreateDate(date);
        decisionGPInventoryLimitOverride.setInventoryLimitOverride(limit);
        return new InventoryLimitDecision(decisionGPInventoryLimitOverride);
    }
    public static AccomTypeSupplement buildCPConfigSupplementAccomType(Integer productId) {
        AccomTypeSupplement accomTypeSupplement = new AccomTypeSupplement();
        accomTypeSupplement.setPropertyId(6);
        accomTypeSupplement.setProductID(productId);
        accomTypeSupplement.setOffsetMethod(OffsetMethod.FIXED_PRICE);
        accomTypeSupplement.setOccupancyType(OccupancyType.SINGLE);
        accomTypeSupplement.setSundaySupplementValue(new BigDecimal(25));
        accomTypeSupplement.setMondaySupplementValue(new BigDecimal(30));
        accomTypeSupplement.setTuesdaySupplementValue(new BigDecimal(35));
        accomTypeSupplement.setWednesdaySupplementValue(new BigDecimal(30));
        accomTypeSupplement.setThursdaySupplementValue(new BigDecimal(30));
        accomTypeSupplement.setFridaySupplementValue(new BigDecimal(20));
        accomTypeSupplement.setSaturdaySupplementValue(new BigDecimal(20));
        AccomType accomType = new AccomType();
        accomType.setId(1);
        accomTypeSupplement.setAccomType(accomType);
        return accomTypeSupplement;
    }
    private static AccomTypeSupplementValue getAccomTypeSupplementValue(java.time.LocalDate arrivalDate, OffsetMethod offsetMethod, BigDecimal supplementValue) {
        AccomTypeSupplementValuePK key1 = new AccomTypeSupplementValuePK(1, DateUtil.convertJavaToJodaLocalDate(arrivalDate), 1, SINGLE);
        AccomTypeSupplementValue accomTypeSupplementValue = new AccomTypeSupplementValue();
        accomTypeSupplementValue.setId(key1);
        accomTypeSupplementValue.setValue(supplementValue);
        accomTypeSupplementValue.setOffsetMethod(offsetMethod);
        return accomTypeSupplementValue;
    }


}