package com.ideas.tetris.ui.modules.propertyspecificconfiguration.optimizationsettings;

import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.OffsetMethod;
import com.ideas.tetris.pacman.services.lrvdroprestriction.entity.LrvDropRestrictionConfiguration;
import com.ideas.tetris.pacman.services.lrvdroprestriction.service.LrvDropRestrictionService;
import com.ideas.tetris.pacman.services.optimizationsettings.OptimizationSettingsEnum;
import com.ideas.tetris.pacman.services.optimizationsettings.PriceDropOccupancyType;
import com.ideas.tetris.pacman.services.optimizationsettings.PriceDropRestrictionConfig;
import com.ideas.tetris.pacman.services.optimizationsettings.PriceDropRestrictionConfigAudit;
import com.ideas.tetris.pacman.services.pricedroprestriction.PriceDropRestrictionService;
import com.ideas.tetris.pacman.services.roa.attribute.entity.PropertyAttribute;
import com.ideas.tetris.pacman.services.roa.attribute.entity.PropertyAttributeEnum;
import com.ideas.tetris.pacman.services.roa.attribute.service.ROAPropertyAttributeService;
import com.ideas.tetris.pacman.services.roa.dto.PropertyAttributeRevision;
import com.ideas.tetris.pacman.util.SeasonService;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import com.ideas.tetris.ui.VaadinUIBaseJupiterTest;
import com.ideas.tetris.ui.common.security.UiContext;
import com.ideas.tetris.ui.common.util.models.SingleValueModel;
import com.ideas.tetris.ui.modules.propertyspecificconfiguration.optimizationsettings.lrvdroprestriction.LrvDropRestrictionDto;
import com.ideas.tetris.ui.modules.propertyspecificconfiguration.optimizationsettings.lrvdroprestriction.LrvRoomClassRemainingCapacity;
import com.ideas.tetris.ui.test.LangTester;
import org.joda.time.DateTimeZone;
import org.joda.time.LocalDateTime;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Mockito;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.ideas.tetris.pacman.common.constants.Constants.ACTIVE_STATUS_ID;
import static com.ideas.tetris.pacman.common.constants.Constants.INACTIVE_STATUS_ID;
import static com.ideas.tetris.pacman.services.roa.attribute.entity.PropertyAttributeEnum.LRV_DROP_MIN_DTA;
import static com.ideas.tetris.ui.modules.propertyspecificconfiguration.optimizationsettings.OptimizationSettingsPresenter.PRICE_DROP_ATTRIBUTE_NAMES;
import static com.ideas.tetris.ui.modules.propertyspecificconfiguration.optimizationsettings.OptimizationSettingsPresenter.PRICE_DROP_MAX_VALUE;
import static com.ideas.tetris.ui.modules.propertyspecificconfiguration.optimizationsettings.OptimizationSettingsPresenter.PRICE_DROP_MIN_DTA;
import static com.ideas.tetris.ui.modules.propertyspecificconfiguration.optimizationsettings.OptimizationSettingsPresenter.PRICE_DROP_MIN_REV_GAIN;
import static com.ideas.tetris.ui.modules.propertyspecificconfiguration.optimizationsettings.OptimizationSettingsPresenter.PRICE_DROP_RESTRICTION;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.anyBoolean;
import static org.mockito.Mockito.anyList;
import static org.mockito.Mockito.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

class OptimizationSettingsPresenterTest extends VaadinUIBaseJupiterTest {
    private static final String PACMAN_FEATURE_DISPLAY_CANCEL_REBOOK_PERCENTAGE_OPTION = "pacman.feature.DisplayCancelRebookPercentageOption";
    private OptimizationSettingsPresenter presenter;
    private OptimizationSettingsView view;
    private PacmanConfigParamsService pacmanConfigParamsService;
    private ROAPropertyAttributeService roaPropertyAttributeService;
    private LrvDropRestrictionService lrvDropRestrictionService;
    private PriceDropRestrictionService priceDropRestrictionService;
    private PriceDropRestrictionDowLayout priceDropRestrictionDowLayout;
    private static final String SSO_USER = "SSO User";
    private static final LocalDateTime jodaDateTime = LocalDateTime.now();
    private static final java.time.LocalDateTime javaDateTime = convertJodaLocalDateTimeToJavaLocalDateTime();

    @BeforeEach
    public void setUp() {
        view = mock(OptimizationSettingsView.class);
        pacmanConfigParamsService = mock(PacmanConfigParamsService.class);
        roaPropertyAttributeService = mock(ROAPropertyAttributeService.class);
        lrvDropRestrictionService = mock(LrvDropRestrictionService.class);
        priceDropRestrictionService = mock(PriceDropRestrictionService.class);
        priceDropRestrictionDowLayout = mock(PriceDropRestrictionDowLayout.class);
        presenter = new OptimizationSettingsPresenter();
        presenter.uiWrapper = new OptimizationSettingsUIWrapper();
        presenter.roaPropertyAttributeService = roaPropertyAttributeService;
        presenter.lrvDropRestrictionService = lrvDropRestrictionService;
        presenter.priceDropRestrictionService = priceDropRestrictionService;
        presenter.seasonService = new SeasonService();
        view.priceDropRestrictionDowLayout = priceDropRestrictionDowLayout;
        presenter.setViewForTesting(view);
        presenter.setLangForTesting(new LangTester());
    }

    @Test
    public void saveCancelRebookPercentage() {
        String optimizationMethodOptionValue = "12";
        preSave(optimizationMethodOptionValue, "11");
        PriceDropRestrictionLayout priceDropRestrictionLayout = mock(PriceDropRestrictionLayout.class);
        when(priceDropRestrictionLayout.hasChanges()).thenReturn(false);
        when(view.priceDropRestrictionLayoutHasChanges()).thenReturn(false);
        UiContext uiContext = mock(UiContext.class);
        when(uiContext.getWorkContextType()).thenReturn(PacmanWorkContextHelper.getWorkContext());
        inject(presenter, "uiContext", uiContext);

        presenter.save();
        verify(view, Mockito.atLeastOnce()).showSaveSuccessMessage();
        verify(roaPropertyAttributeService, Mockito.atLeastOnce()).getPropertyAttribute(PropertyAttributeEnum.CANCEL_REBOOK_PCT);
    }

    @Test
    public void saveNullCancelRebookPercentage() {
        String optimizationMethodOptionValue = "12";
        preSave(optimizationMethodOptionValue, null);
        presenter.save();
        verify(view, Mockito.atLeastOnce()).showValidationMessage(Mockito.anyString());
    }

    private void preSave(String optimizationMethodOptionValue, String cancelRebookPercentageValue) {
        when(pacmanConfigParamsService.getBooleanParameterValue(PACMAN_FEATURE_DISPLAY_CANCEL_REBOOK_PERCENTAGE_OPTION)).thenReturn(true);
        PropertyAttribute propertyAttribute = new PropertyAttribute();
        propertyAttribute.setValue(optimizationMethodOptionValue);
        propertyAttribute.setId(1);
        when(roaPropertyAttributeService.getPropertyAttribute(PropertyAttributeEnum.OPT_DYNAMIC_PRICE)).thenReturn(propertyAttribute);
        presenter.uiWrapper.setOptimizationMethodOption(new OptimizationMethodOption(Integer.parseInt(optimizationMethodOptionValue), ""));
        PropertyAttribute cancelRebookPercentage = new PropertyAttribute();
        cancelRebookPercentage.setValue(cancelRebookPercentageValue);
        cancelRebookPercentage.setId(2);
        when(roaPropertyAttributeService.getPropertyAttribute(PropertyAttributeEnum.CANCEL_REBOOK_PCT)).thenReturn(cancelRebookPercentage);
        presenter.uiWrapper.setCancelRebookPercentage(cancelRebookPercentage);
        when(roaPropertyAttributeService.getAttributeValue(propertyAttribute.getId())).thenReturn("1");
        when(roaPropertyAttributeService.getAttributeValue(cancelRebookPercentage.getId())).thenReturn("2");
        presenter.optDynamicPrice = optimizationMethodOptionValue;
        presenter.cancelRebookAllowed = true;
    }

    @Test
    public void testPriceDropRestrictionInitOnViewOpened() {
        //GIVEN
        Void v = null;
        when(pacmanConfigParamsService.getParameterValueByClientLevel(PACMAN_FEATURE_DISPLAY_CANCEL_REBOOK_PERCENTAGE_OPTION))
                .thenReturn("false");
        when(pacmanConfigParamsService.getParameterValueByClientLevel(FeatureTogglesConfigParamName.DISPLAY_OPTIMIZATION_SETTINGS_TAB.value()))
                .thenReturn("false");
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.ENABLE_PRICE_DROP_RESTRICTION_FEATURE))
                .thenReturn(true);
        mockOptDynamicPricePropertyAttribute();
        mockCancelRebookPctAttribute();
        HashMap<String, String> attributeValueWithNameByAttributeNames = new HashMap<>();
        attributeValueWithNameByAttributeNames.put(PRICE_DROP_MIN_DTA, "5");
        attributeValueWithNameByAttributeNames.put(PRICE_DROP_MIN_REV_GAIN, "100");
        attributeValueWithNameByAttributeNames.put(PRICE_DROP_MAX_VALUE, "0");
        when(roaPropertyAttributeService.getAttributeValueWithNameByAttributeNames(PRICE_DROP_ATTRIBUTE_NAMES))
                .thenReturn(attributeValueWithNameByAttributeNames);
        presenter.pacmanConfigParamsService = pacmanConfigParamsService;
        presenter.roaPropertyAttributeService = roaPropertyAttributeService;
        ArgumentCaptor<PriceDropRestrictionDto> priceDropRestrictionDtoArgumentCaptor = ArgumentCaptor.forClass(PriceDropRestrictionDto.class);
        //WHEN
        presenter.onViewOpened(v);
        //THEN
        verify(view).showCancelRebookPercentage(false);
        verify(view).showOptimizationMethodOption(false);
        verify(view).showPriceDropRestrictionOption(true);
        verify(view).showPriceDropRestrictionDOW(false);
        verify(view).initPriceDropRestriction(priceDropRestrictionDtoArgumentCaptor.capture());
        PriceDropRestrictionDto result = priceDropRestrictionDtoArgumentCaptor.getValue();
        assertTrue(result.isEnablePriceDropRestriction());
        assertEquals(5, result.getDaysToArrival().intValue());
        assertEquals(new BigDecimal("100"), result.getRevenueThresholdForPrice());
        assertEquals(BigDecimal.ZERO, result.getMaximumPriceDrop());
    }

    @Test
    public void testPriceDropRestrictionDowAndSeasonsInitOnViewOpened() {
        //GIVEN
        Void v = null;
        when(pacmanConfigParamsService.getParameterValueByClientLevel(PACMAN_FEATURE_DISPLAY_CANCEL_REBOOK_PERCENTAGE_OPTION))
                .thenReturn("false");
        when(pacmanConfigParamsService.getParameterValueByClientLevel(FeatureTogglesConfigParamName.DISPLAY_OPTIMIZATION_SETTINGS_TAB.value()))
                .thenReturn("false");
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.ENABLE_PRICE_DROP_RESTRICTION_FEATURE))
                .thenReturn(true);
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.ENABLE_PRICE_DROP_RESTRICTION_FEATURE_DOW_AND_SEASONS))
                .thenReturn(true);
        mockOptDynamicPricePropertyAttribute();
        mockCancelRebookPctAttribute();

        PriceDropRestrictionConfig priceDropRestrictionConfig = PriceDropRestrictionConfig.builder().id(1).build();
        priceDropRestrictionConfig.setMondayDTA(BigDecimal.ONE);
        priceDropRestrictionConfig.setMondayMaxPrcDrp(BigDecimal.TEN);
        priceDropRestrictionConfig.setMondayPrcRevThreshold(BigDecimal.ZERO);

        when(priceDropRestrictionService.findDefault()).thenReturn(priceDropRestrictionConfig);

        presenter.pacmanConfigParamsService = pacmanConfigParamsService;
        presenter.roaPropertyAttributeService = roaPropertyAttributeService;
        ArgumentCaptor<PriceDropRestrictionDowDto> priceDropRestrictionDtoArgumentCaptor = ArgumentCaptor.forClass(PriceDropRestrictionDowDto.class);
        //WHEN
        presenter.onViewOpened(v);
        //THEN
        verify(view).showCancelRebookPercentage(false);
        verify(view).showOptimizationMethodOption(false);
        verify(view).showPriceDropRestrictionOption(false);
        verify(view).showPriceDropRestrictionDOW(true);
        verify(view).initPriceDropRestrictionConfigs(priceDropRestrictionDtoArgumentCaptor.capture(), eq(true));
        PriceDropRestrictionDowDto result = priceDropRestrictionDtoArgumentCaptor.getValue();
        assertTrue(result.isEnablePriceDropRestriction());
        assertEquals(BigDecimal.ONE, result.getPriceDropRestrictionConfigWrapperList().get(1).getMonday());
        assertEquals(BigDecimal.ZERO, result.getPriceDropRestrictionConfigWrapperList().get(2).getMonday());
        assertEquals(BigDecimal.TEN, result.getPriceDropRestrictionConfigWrapperList().get(3).getMonday());
    }

    @Test
    public void testShouldNotShowPriceDropRestrictionDowAndSeasonsInitOnViewOpenedWhenMainToggleOff() {
        //GIVEN
        Void v = null;
        when(pacmanConfigParamsService.getParameterValueByClientLevel(PACMAN_FEATURE_DISPLAY_CANCEL_REBOOK_PERCENTAGE_OPTION))
                .thenReturn("false");
        when(pacmanConfigParamsService.getParameterValueByClientLevel(FeatureTogglesConfigParamName.DISPLAY_OPTIMIZATION_SETTINGS_TAB.value()))
                .thenReturn("false");
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.ENABLE_PRICE_DROP_RESTRICTION_FEATURE))
                .thenReturn(false);
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.ENABLE_PRICE_DROP_RESTRICTION_FEATURE_DOW_AND_SEASONS))
                .thenReturn(true);
        mockOptDynamicPricePropertyAttribute();
        mockCancelRebookPctAttribute();

        PriceDropRestrictionConfig priceDropRestrictionConfig = PriceDropRestrictionConfig.builder().id(1).build();
        priceDropRestrictionConfig.setMondayDTA(BigDecimal.ONE);
        priceDropRestrictionConfig.setMondayMaxPrcDrp(BigDecimal.TEN);
        priceDropRestrictionConfig.setMondayPrcRevThreshold(BigDecimal.ZERO);

        when(priceDropRestrictionService.findDefault()).thenReturn(priceDropRestrictionConfig);

        presenter.pacmanConfigParamsService = pacmanConfigParamsService;
        presenter.roaPropertyAttributeService = roaPropertyAttributeService;
        //WHEN
        presenter.onViewOpened(v);
        //THEN
        verify(view).showCancelRebookPercentage(false);
        verify(view).showOptimizationMethodOption(false);
        verify(view).showPriceDropRestrictionOption(false);
        verify(view).showPriceDropRestrictionDOW(false);
        verify(view, never()).initPriceDropRestrictionConfigs(any(PriceDropRestrictionDowDto.class), anyBoolean());
    }

    @Test
    void shouldEnableLrvDropRestrictionCheckboxOnViewOpened_forCoupledLrvDrop_whenRoomClassMinCapacityConfigsPresent(){
        //GIVEN
        Void v = null;
        when(pacmanConfigParamsService.getParameterValueByClientLevel(PACMAN_FEATURE_DISPLAY_CANCEL_REBOOK_PERCENTAGE_OPTION))
                .thenReturn("false");
        when(pacmanConfigParamsService.getParameterValueByClientLevel(FeatureTogglesConfigParamName.DISPLAY_OPTIMIZATION_SETTINGS_TAB.value()))
                .thenReturn("false");
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.ENABLE_PRICE_DROP_RESTRICTION_FEATURE))
                .thenReturn(true);
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.ENABLE_LRV_DROP_RESTRICTION_FEATURE))
                .thenReturn(true);
        mockOptDynamicPricePropertyAttribute();
        mockCancelRebookPctAttribute();
        HashMap<String, String> attributeValueWithNameByAttributeNames = new HashMap<>();
        attributeValueWithNameByAttributeNames.put(PRICE_DROP_MIN_DTA, "5");
        attributeValueWithNameByAttributeNames.put(PRICE_DROP_MIN_REV_GAIN, "100");
        attributeValueWithNameByAttributeNames.put(PRICE_DROP_MAX_VALUE, "0");
        when(roaPropertyAttributeService.getAttributeValueWithNameByAttributeNames(PRICE_DROP_ATTRIBUTE_NAMES))
                .thenReturn(attributeValueWithNameByAttributeNames);
        LrvDropRestrictionService mockLrvDropRestrictionService = mock(LrvDropRestrictionService.class);
        List<LrvDropRestrictionConfiguration> lrvDropConfigurations = getLrvDropRestrictionConfigurations(20, 30, 50);
        when(mockLrvDropRestrictionService.getConfiguration()).thenReturn(lrvDropConfigurations);
        presenter.pacmanConfigParamsService = pacmanConfigParamsService;
        presenter.roaPropertyAttributeService = roaPropertyAttributeService;
        presenter.lrvDropRestrictionService = mockLrvDropRestrictionService;
        ArgumentCaptor<PriceDropRestrictionDto> priceDropRestrictionDtoArgumentCaptor = ArgumentCaptor.forClass(PriceDropRestrictionDto.class);
        ArgumentCaptor<LrvDropRestrictionDto> lrvDropRestrictionDtoArgumentCaptor = ArgumentCaptor.forClass(LrvDropRestrictionDto.class);
        //WHEN
        presenter.onViewOpened(v);
        //THEN
        verify(view).showCancelRebookPercentage(false);
        verify(view).showOptimizationMethodOption(false);
        verify(view).showPriceDropRestrictionOption(true);
        verify(view).showLrvDropRestrictionOption(true);
        verify(view).initPriceDropRestriction(priceDropRestrictionDtoArgumentCaptor.capture());
        PriceDropRestrictionDto result = priceDropRestrictionDtoArgumentCaptor.getValue();
        assertTrue(result.isEnablePriceDropRestriction());
        assertEquals(5, result.getDaysToArrival().intValue());
        assertEquals(new BigDecimal("100"), result.getRevenueThresholdForPrice());
        assertEquals(BigDecimal.ZERO, result.getMaximumPriceDrop());
        verify(view).initLrvDropRestriction(lrvDropRestrictionDtoArgumentCaptor.capture());
        LrvDropRestrictionDto lrvDropConfigResult = lrvDropRestrictionDtoArgumentCaptor.getValue();
        assertTrue(lrvDropConfigResult.isEnableLrvDropRestriction());
        verifyLrvRoomClassData(lrvDropConfigResult.getRoomClassCapacities().get(0), 2, "STD", 20, 120);
        verifyLrvRoomClassData(lrvDropConfigResult.getRoomClassCapacities().get(1), 3, "DLX", 30, 100);
        verifyLrvRoomClassData(lrvDropConfigResult.getRoomClassCapacities().get(2), 4, "STE", 50, 80);
    }

    @Test
    void shouldEnableLrvDropRestrictionCheckboxOnViewOpened_whenDtaIsActiveAndMinCapConfigsPresent_forDecoupleLrvConfigs(){
        Void v = null;
        enableDecoupleLrvRelatedToggles();
        mockOptDynamicPricePropertyAttribute();
        mockCancelRebookPctAttribute();

        List<LrvDropRestrictionConfiguration> lrvDropConfigurations = getLrvDropRestrictionConfigurations(0, 0, 0);
        when(lrvDropRestrictionService.getConfiguration()).thenReturn(lrvDropConfigurations);
        when(roaPropertyAttributeService.getOverridenAttributeValueForAttributeName(LRV_DROP_MIN_DTA.getAttributeName())).thenReturn("5");
        ArgumentCaptor<LrvDropRestrictionDto> lrvDropRestrictionDtoArgumentCaptor = ArgumentCaptor.forClass(LrvDropRestrictionDto.class);

        presenter.onViewOpened(v);

        verify(view).showLrvDropRestrictionOption(true);
        verify(view).initLrvDropRestriction(lrvDropRestrictionDtoArgumentCaptor.capture());
        LrvDropRestrictionDto lrvDropConfigDto = lrvDropRestrictionDtoArgumentCaptor.getValue();
        assertTrue(lrvDropConfigDto.isEnableLrvDropRestriction());
        assertTrue(lrvDropConfigDto.isDecoupleLrvDropRestrictionEnabled());
        assertEquals(3, lrvDropConfigDto.getRoomClassCapacities().size());
        assertEquals(5, lrvDropConfigDto.getDaysToArrival());
    }

    @Test
    void shouldNotEnableLrvDropRestrictionCheckboxOnViewOpened_whenDtaIsInactive_forDecoupleLrvConfigs(){
        Void v = null;
        enableDecoupleLrvRelatedToggles();
        mockOptDynamicPricePropertyAttribute();
        mockCancelRebookPctAttribute();

        List<LrvDropRestrictionConfiguration> lrvDropConfigurations = getLrvDropRestrictionConfigurations(0, 0,0);
        when(lrvDropRestrictionService.getConfiguration()).thenReturn(lrvDropConfigurations);
        when(roaPropertyAttributeService.getOverridenAttributeValueForAttributeName(LRV_DROP_MIN_DTA.getAttributeName())).thenReturn(null);
        ArgumentCaptor<LrvDropRestrictionDto> lrvDropRestrictionDtoArgumentCaptor = ArgumentCaptor.forClass(LrvDropRestrictionDto.class);

        presenter.onViewOpened(v);

        verify(view).showLrvDropRestrictionOption(true);
        verify(view).initLrvDropRestriction(lrvDropRestrictionDtoArgumentCaptor.capture());
        LrvDropRestrictionDto lrvDropConfigDto = lrvDropRestrictionDtoArgumentCaptor.getValue();
        assertFalse(lrvDropConfigDto.isEnableLrvDropRestriction());
        assertTrue(lrvDropConfigDto.isDecoupleLrvDropRestrictionEnabled());
        assertEquals(3, lrvDropConfigDto.getRoomClassCapacities().size());
    }

    private LrvDropRestrictionConfiguration createLrvDropConfig(int accomClassId, String accomClassName,
                                                                int minRemainingCapacity, int maxAllowedCapacity) {
        LrvDropRestrictionConfiguration lrvDropConfig = new LrvDropRestrictionConfiguration();
        lrvDropConfig.setAccomClassId(accomClassId);
        lrvDropConfig.setAccomClassName(accomClassName);
        lrvDropConfig.setMinRemainingCapacity(minRemainingCapacity);
        lrvDropConfig.setMaxAllowedRoomCapacity(maxAllowedCapacity);
        return lrvDropConfig;
    }

    @Test
    public void shouldNotCallInitPriceDropRestrictionWhenToggleIsOff() {
        //GIVEN
        Void v = null;
        when(pacmanConfigParamsService.getParameterValueByClientLevel(PACMAN_FEATURE_DISPLAY_CANCEL_REBOOK_PERCENTAGE_OPTION))
                .thenReturn("false");
        when(pacmanConfigParamsService.getParameterValueByClientLevel(FeatureTogglesConfigParamName.DISPLAY_OPTIMIZATION_SETTINGS_TAB.value()))
                .thenReturn("false");
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.ENABLE_PRICE_DROP_RESTRICTION_FEATURE))
                .thenReturn(false);
        mockOptDynamicPricePropertyAttribute();
        mockCancelRebookPctAttribute();
        presenter.pacmanConfigParamsService = pacmanConfigParamsService;
        presenter.roaPropertyAttributeService = roaPropertyAttributeService;
        //WHEN
        presenter.onViewOpened(v);
        //THEN
        verify(view).showCancelRebookPercentage(false);
        verify(view).showOptimizationMethodOption(false);
        verify(view).showPriceDropRestrictionOption(false);
        verify(view, never()).initPriceDropRestriction(any());
    }

    private void mockCancelRebookPctAttribute() {
        PropertyAttribute cancelRebookPctAttribute = new PropertyAttribute();
        cancelRebookPctAttribute.setId(2);
        when(roaPropertyAttributeService.getPropertyAttribute(PropertyAttributeEnum.CANCEL_REBOOK_PCT)).thenReturn(cancelRebookPctAttribute);
    }

    private void mockOptDynamicPricePropertyAttribute() {
        PropertyAttribute optDynamicPricePropertyAttribute = getPropertyAttribute(1);
        when(roaPropertyAttributeService.getPropertyAttribute(PropertyAttributeEnum.OPT_DYNAMIC_PRICE)).thenReturn(optDynamicPricePropertyAttribute);
        when(roaPropertyAttributeService.getAttributeValue(optDynamicPricePropertyAttribute.getId())).thenReturn("10");
    }

    @Test
    public void shouldGetPriceDropRestrictionHistory() {
        //WHEN
        List<PropertyAttribute> propertyAttributes = new ArrayList<>();
        propertyAttributes.add(createPropertyAttribute(PRICE_DROP_RESTRICTION.PRICE_DROP_MIN_REV_GAIN, 1));
        propertyAttributes.add(createPropertyAttribute(PRICE_DROP_RESTRICTION.PRICE_DROP_MIN_DTA, 2));
        propertyAttributes.add(createPropertyAttribute(PRICE_DROP_RESTRICTION.PRICE_DROP_MAX_VALUE, 3));
        when(roaPropertyAttributeService.fetchPropertyAttributesByName(
                Arrays.asList(PRICE_DROP_RESTRICTION.PRICE_DROP_MIN_REV_GAIN.toString(),
                        PRICE_DROP_RESTRICTION.PRICE_DROP_MAX_VALUE.toString(),
                        PRICE_DROP_RESTRICTION.PRICE_DROP_MIN_DTA.toString()))).thenReturn(propertyAttributes);
        List<PropertyAttributeRevision> propertyAttributeRevisions = new ArrayList<>();
        LocalDateTime nowTime = LocalDateTime.now();
        propertyAttributeRevisions.add(createRevision(nowTime, new BigDecimal("100"), ACTIVE_STATUS_ID, 0, PRICE_DROP_RESTRICTION.PRICE_DROP_MIN_REV_GAIN.name()));
        propertyAttributeRevisions.add(createRevision(nowTime, new BigDecimal("3"), ACTIVE_STATUS_ID, 0, PRICE_DROP_RESTRICTION.PRICE_DROP_MIN_DTA.name()));
        propertyAttributeRevisions.add(createRevision(nowTime, new BigDecimal("50"), ACTIVE_STATUS_ID, 0, PRICE_DROP_RESTRICTION.PRICE_DROP_MAX_VALUE.name()));
        propertyAttributeRevisions.add(createRevision(nowTime.plusMinutes(10), new BigDecimal("120"), ACTIVE_STATUS_ID, 1, PRICE_DROP_RESTRICTION.PRICE_DROP_MIN_REV_GAIN.name()));
        propertyAttributeRevisions.add(createRevision(nowTime.plusMinutes(10), new BigDecimal("5"), ACTIVE_STATUS_ID, 1, PRICE_DROP_RESTRICTION.PRICE_DROP_MIN_DTA.name()));
        propertyAttributeRevisions.add(createRevision(nowTime.plusMinutes(10), new BigDecimal("70"), ACTIVE_STATUS_ID, 1, PRICE_DROP_RESTRICTION.PRICE_DROP_MAX_VALUE.name()));
        propertyAttributeRevisions.add(createRevision(nowTime.plusMinutes(20), new BigDecimal("120"), INACTIVE_STATUS_ID, 1, PRICE_DROP_RESTRICTION.PRICE_DROP_MIN_REV_GAIN.name()));
        propertyAttributeRevisions.add(createRevision(nowTime.plusMinutes(20), new BigDecimal("5"), INACTIVE_STATUS_ID, 1, PRICE_DROP_RESTRICTION.PRICE_DROP_MIN_DTA.name()));
        propertyAttributeRevisions.add(createRevision(nowTime.plusMinutes(20), new BigDecimal("70"), INACTIVE_STATUS_ID, 1, PRICE_DROP_RESTRICTION.PRICE_DROP_MAX_VALUE.name()));
        propertyAttributeRevisions.add(createRevision(nowTime.plusMinutes(40), new BigDecimal("110"), ACTIVE_STATUS_ID, 0, PRICE_DROP_RESTRICTION.PRICE_DROP_MIN_REV_GAIN.name()));
        propertyAttributeRevisions.add(createRevision(nowTime.plusMinutes(40), new BigDecimal("2"), ACTIVE_STATUS_ID, 0, PRICE_DROP_RESTRICTION.PRICE_DROP_MIN_DTA.name()));
        propertyAttributeRevisions.add(createRevision(nowTime.plusMinutes(40), new BigDecimal("0"), ACTIVE_STATUS_ID, 0, PRICE_DROP_RESTRICTION.PRICE_DROP_MAX_VALUE.name()));
        when(roaPropertyAttributeService.fetchAttributeRevisionsForMultipleAttributes(Arrays.asList(1, 2, 3))).thenReturn(propertyAttributeRevisions);
        //WHEN
        List<PropertyAttributeRevisionUIWrapper> priceDropRestrictionHistory = presenter.getPriceDropRestrictionHistory();
        //THEN
        assertPriceDropRestrictionHistory(priceDropRestrictionHistory.get(0), "enable.price.drop.restrictions", "True", "SSO User", 0, 1);
        assertPriceDropRestrictionHistory(priceDropRestrictionHistory.get(1), PRICE_DROP_RESTRICTION.PRICE_DROP_MIN_REV_GAIN.getKey(), "100", "SSO User", 0, 1);
        assertPriceDropRestrictionHistory(priceDropRestrictionHistory.get(2), PRICE_DROP_RESTRICTION.PRICE_DROP_MIN_DTA.getKey(), "3", "SSO User", 0, 1);
        assertPriceDropRestrictionHistory(priceDropRestrictionHistory.get(3), PRICE_DROP_RESTRICTION.PRICE_DROP_MAX_VALUE.getKey(), "50", "SSO User", 0, 1);
        assertPriceDropRestrictionHistory(priceDropRestrictionHistory.get(4), PRICE_DROP_RESTRICTION.PRICE_DROP_MIN_REV_GAIN.getKey(), "120", "SSO User", 1, 1);
        assertPriceDropRestrictionHistory(priceDropRestrictionHistory.get(5), PRICE_DROP_RESTRICTION.PRICE_DROP_MIN_DTA.getKey(), "5", "SSO User", 1, 1);
        assertPriceDropRestrictionHistory(priceDropRestrictionHistory.get(6), PRICE_DROP_RESTRICTION.PRICE_DROP_MAX_VALUE.getKey(), "70", "SSO User", 1, 1);
        assertPriceDropRestrictionHistory(priceDropRestrictionHistory.get(7), "enable.price.drop.restrictions", "False", "SSO User", 1, 2);
        assertPriceDropRestrictionHistory(priceDropRestrictionHistory.get(8), "enable.price.drop.restrictions", "True", "SSO User", 0, 1);
        assertPriceDropRestrictionHistory(priceDropRestrictionHistory.get(9), PRICE_DROP_RESTRICTION.PRICE_DROP_MIN_REV_GAIN.getKey(), "110", "SSO User", 0, 1);
        assertPriceDropRestrictionHistory(priceDropRestrictionHistory.get(10), PRICE_DROP_RESTRICTION.PRICE_DROP_MIN_DTA.getKey(), "2", "SSO User", 0, 1);
        assertPriceDropRestrictionHistory(priceDropRestrictionHistory.get(11), PRICE_DROP_RESTRICTION.PRICE_DROP_MAX_VALUE.getKey(), "0", "SSO User", 0, 1);
    }

    private void assertPriceDropRestrictionHistory(PropertyAttributeRevisionUIWrapper priceDropRestrictionHistory, String expectedAttrName, String expectedValue, String expectedUserName, int expectedRevType, int expectedStatusId) {
        assertEquals(expectedAttrName, priceDropRestrictionHistory.getPropertyAttributeName());
        assertEquals(expectedValue, priceDropRestrictionHistory.getValue());
        assertEquals(expectedUserName, priceDropRestrictionHistory.getPropertyAttributeRevision().getUserName());
        assertEquals(expectedRevType, priceDropRestrictionHistory.getRevType().intValue());
        assertEquals(expectedStatusId, priceDropRestrictionHistory.getStatusId().intValue());
    }

    private PropertyAttributeRevision createRevision(LocalDateTime nowTime, BigDecimal value, int statusId, int revType, String attributeName) {
        PropertyAttributeRevision revision = new PropertyAttributeRevision();
        revision.setAttributeName(attributeName);
        revision.setUserName("SSO User");
        revision.setValue(value);
        revision.setLastUpdatedDateTime(nowTime);
        revision.setStatusId(statusId);
        revision.setRevType(revType);
        return revision;
    }

    private PropertyAttribute createPropertyAttribute(PRICE_DROP_RESTRICTION priceDropRestriction, int id) {
        PropertyAttribute propertyAttribute = new PropertyAttribute();
        propertyAttribute.setAttributeName(priceDropRestriction.toString());
        propertyAttribute.setId(id);
        return propertyAttribute;
    }

    @Test
    public void shouldBeAbleToSavePriceDropRestrictionSettings() {
        //GIVEN
        presenter.optDynamicPrice = "1";
        presenter.uiWrapper.setOptimizationMethodOption(new OptimizationMethodOption(Integer.valueOf(1), ""));
        presenter.cancelRebookAllowed = false;
        when(view.priceDropRestrictionLayoutHasChanges()).thenReturn(true);
        when(view.isPriceDropRestrictionLayoutValid()).thenReturn(true);
        PriceDropRestrictionDto priceDropRestrictionDto = getPriceDropRestrictionDto(50, true);
        when(view.getPriceDropRestrictionDto()).thenReturn(priceDropRestrictionDto);
        ArgumentCaptor<Map> mapArgumentCaptor = ArgumentCaptor.forClass(Map.class);
        PropertyAttribute propertyAttribute = getPropertyAttribute(1);
        when(roaPropertyAttributeService.getPropertyAttribute(PropertyAttributeEnum.OPT_DYNAMIC_PRICE)).thenReturn(propertyAttribute);
        when(roaPropertyAttributeService.getAttributeValue(1)).thenReturn("1");
        PropertyAttribute cancelRebookPropertyAttribute = new PropertyAttribute();
        cancelRebookPropertyAttribute.setId(2);
        when(roaPropertyAttributeService.getPropertyAttribute(PropertyAttributeEnum.CANCEL_REBOOK_PCT)).thenReturn(cancelRebookPropertyAttribute);
        //WHEN
        presenter.save();
        //THEN
        verify(roaPropertyAttributeService).saveMultiplePropertyAttributes(mapArgumentCaptor.capture());
        HashMap<String, String> attributesWithValue = (HashMap<String, String>) mapArgumentCaptor.getValue();
        verifyPriceDropSettings(attributesWithValue);
    }

    @Test
    public void shouldBeAbleToDeletePriceDropRestrictionSettings() {
        //GIVEN
        presenter.optDynamicPrice = "1";
        presenter.uiWrapper.setOptimizationMethodOption(new OptimizationMethodOption(Integer.valueOf(1), ""));
        presenter.cancelRebookAllowed = false;
        when(view.priceDropRestrictionLayoutHasChanges()).thenReturn(true);
        when(view.isPriceDropRestrictionLayoutValid()).thenReturn(true);
        PriceDropRestrictionDto priceDropRestrictionDto = getPriceDropRestrictionDto(50, false);
        when(view.getPriceDropRestrictionDto()).thenReturn(priceDropRestrictionDto);
        ArgumentCaptor<List> listArgumentCaptor = ArgumentCaptor.forClass(List.class);
        PropertyAttribute propertyAttribute = getPropertyAttribute(1);
        when(roaPropertyAttributeService.getPropertyAttribute(PropertyAttributeEnum.OPT_DYNAMIC_PRICE)).thenReturn(propertyAttribute);
        when(roaPropertyAttributeService.getAttributeValue(1)).thenReturn("1");
        PropertyAttribute cancelRebookPropertyAttribute = new PropertyAttribute();
        cancelRebookPropertyAttribute.setId(2);
        when(roaPropertyAttributeService.getPropertyAttribute(PropertyAttributeEnum.CANCEL_REBOOK_PCT)).thenReturn(cancelRebookPropertyAttribute);
        //WHEN
        presenter.save();
        //THEN
        verify(roaPropertyAttributeService).deletePropertyAttribute(listArgumentCaptor.capture());
        List<String> attributesWithValue = listArgumentCaptor.getValue();
        assertTrue(attributesWithValue.contains(PRICE_DROP_MIN_DTA));
        assertTrue(attributesWithValue.contains(PRICE_DROP_MIN_REV_GAIN));
        assertTrue(attributesWithValue.contains(PRICE_DROP_MAX_VALUE));
    }

    @Test
    public void shouldBeAbleSaveLrvDropProtection() {
        //GIVEN
        presenter.optDynamicPrice = "1";
        presenter.uiWrapper.setOptimizationMethodOption(new OptimizationMethodOption(Integer.valueOf(1), ""));
        presenter.cancelRebookAllowed = false;
        when(view.priceDropRestrictionLayoutHasChanges()).thenReturn(true);
        when(view.isPriceDropRestrictionLayoutValid()).thenReturn(true);
        PriceDropRestrictionDto priceDropRestrictionDto = getPriceDropRestrictionDto(0, true);
        when(view.getPriceDropRestrictionDto()).thenReturn(priceDropRestrictionDto);
        ArgumentCaptor<List> listArgumentCaptor = ArgumentCaptor.forClass(List.class);
        PropertyAttribute propertyAttribute = getPropertyAttribute(1);
        when(roaPropertyAttributeService.getPropertyAttribute(PropertyAttributeEnum.OPT_DYNAMIC_PRICE)).thenReturn(propertyAttribute);
        when(roaPropertyAttributeService.getAttributeValue(1)).thenReturn("1");
        PropertyAttribute cancelRebookPropertyAttribute = new PropertyAttribute();
        cancelRebookPropertyAttribute.setId(2);
        when(roaPropertyAttributeService.getPropertyAttribute(PropertyAttributeEnum.CANCEL_REBOOK_PCT)).thenReturn(cancelRebookPropertyAttribute);
        when(view.lrvDropRestrictionLayoutHasChanges()).thenReturn(true);
        when(view.isLrvDropRestrictionLayoutValid()).thenReturn(true);
        LrvDropRestrictionDto dto = new LrvDropRestrictionDto();
        List<LrvRoomClassRemainingCapacity> roomClassCapacities = new ArrayList<>();
        LrvRoomClassRemainingCapacity rcRemCap = getLrvRoomClassRemainingCapacity();
        roomClassCapacities.add(rcRemCap);
        dto.setRoomClassCapacities(roomClassCapacities);
        dto.setEnableLrvDropRestriction(true);
        when(view.getLrvDropRestrictionLayoutBean()).thenReturn(dto);
        when(view.isLrvRestrictionCheckBoxEnabled()).thenReturn(true);
        ArgumentCaptor<Map> mapArgumentCaptor = ArgumentCaptor.forClass(Map.class);
        ArgumentCaptor<Map> mapArgumentCaptorForLrvDrop = ArgumentCaptor.forClass(Map.class);
        //WHEN
        presenter.save();
        //THEN
        verify(roaPropertyAttributeService).saveMultiplePropertyAttributes(mapArgumentCaptor.capture());
        HashMap<String, String> attributesWithValue = (HashMap<String, String>) mapArgumentCaptor.getValue();
        verifyPriceDropSettings(attributesWithValue);
        verify(lrvDropRestrictionService).saveConfiguration(mapArgumentCaptorForLrvDrop.capture());
        Map<Integer, Integer> rcWithCapacity = mapArgumentCaptorForLrvDrop.getValue();
        assertTrue(rcWithCapacity.containsKey(1));
        assertEquals(12, rcWithCapacity.get(1).intValue());
    }

    @Test
    public void shouldBeAbleDeleteLrvDropProtection() {
        //GIVEN
        presenter.optDynamicPrice = "1";
        presenter.uiWrapper.setOptimizationMethodOption(new OptimizationMethodOption(1, ""));
        presenter.cancelRebookAllowed = false;
        when(view.priceDropRestrictionLayoutHasChanges()).thenReturn(false);
        PropertyAttribute propertyAttribute = getPropertyAttribute(1);
        when(roaPropertyAttributeService.getPropertyAttribute(PropertyAttributeEnum.OPT_DYNAMIC_PRICE)).thenReturn(propertyAttribute);
        when(roaPropertyAttributeService.getAttributeValue(1)).thenReturn("1");
        PropertyAttribute cancelRebookPropertyAttribute = new PropertyAttribute();
        cancelRebookPropertyAttribute.setId(2);
        when(roaPropertyAttributeService.getPropertyAttribute(PropertyAttributeEnum.CANCEL_REBOOK_PCT)).thenReturn(cancelRebookPropertyAttribute);
        when(view.lrvDropRestrictionLayoutHasChanges()).thenReturn(true);
        when(view.isLrvDropRestrictionLayoutValid()).thenReturn(true);
        LrvDropRestrictionDto dto = new LrvDropRestrictionDto();
        List<LrvRoomClassRemainingCapacity> roomClassCapacities = new ArrayList<>();
        LrvRoomClassRemainingCapacity rcRemCap = getLrvRoomClassRemainingCapacity();
        roomClassCapacities.add(rcRemCap);
        dto.setRoomClassCapacities(roomClassCapacities);
        dto.setEnableLrvDropRestriction(false);
        when(view.getLrvDropRestrictionLayoutBean()).thenReturn(dto);
        when(view.isLrvRestrictionCheckBoxEnabled()).thenReturn(false);
        //WHEN
        presenter.save();
        //THEN
        verify(lrvDropRestrictionService).deleteConfiguration();
    }

    @Test
    public void shouldBeAbleDeleteLrvDropProtectionWhenPriceDropProtectionGetsDeleted() {
        //GIVEN
        presenter.optDynamicPrice = "1";
        presenter.uiWrapper.setOptimizationMethodOption(new OptimizationMethodOption(Integer.valueOf(1), ""));
        presenter.cancelRebookAllowed = false;
        when(view.priceDropRestrictionLayoutHasChanges()).thenReturn(true);
        when(view.isPriceDropRestrictionLayoutValid()).thenReturn(true);
        PriceDropRestrictionDto priceDropRestrictionDto = getPriceDropRestrictionDto(0, false);
        when(view.getPriceDropRestrictionDto()).thenReturn(priceDropRestrictionDto);
        PropertyAttribute propertyAttribute = getPropertyAttribute(1);
        when(roaPropertyAttributeService.getPropertyAttribute(PropertyAttributeEnum.OPT_DYNAMIC_PRICE)).thenReturn(propertyAttribute);
        when(roaPropertyAttributeService.getAttributeValue(1)).thenReturn("1");
        PropertyAttribute cancelRebookPropertyAttribute = new PropertyAttribute();
        cancelRebookPropertyAttribute.setId(2);
        when(roaPropertyAttributeService.getPropertyAttribute(PropertyAttributeEnum.CANCEL_REBOOK_PCT)).thenReturn(cancelRebookPropertyAttribute);
        when(view.lrvDropRestrictionLayoutHasChanges()).thenReturn(true);
        when(view.isLrvDropRestrictionLayoutValid()).thenReturn(true);
        LrvDropRestrictionDto dto = new LrvDropRestrictionDto();
        List<LrvRoomClassRemainingCapacity> roomClassCapacities = new ArrayList<>();
        LrvRoomClassRemainingCapacity rcRemCap = getLrvRoomClassRemainingCapacity();
        roomClassCapacities.add(rcRemCap);
        dto.setRoomClassCapacities(roomClassCapacities);
        dto.setEnableLrvDropRestriction(true);
        when(view.getLrvDropRestrictionLayoutBean()).thenReturn(dto);
        when(view.isLrvRestrictionCheckBoxEnabled()).thenReturn(true);
        ArgumentCaptor<List> listArgumentCaptor = ArgumentCaptor.forClass(List.class);
        //WHEN
        presenter.save();
        //THEN
        verify(roaPropertyAttributeService).deletePropertyAttribute(listArgumentCaptor.capture());
        List<String> attributesWithValue = listArgumentCaptor.getValue();
        assertTrue(attributesWithValue.contains(PRICE_DROP_MIN_DTA));
        assertTrue(attributesWithValue.contains(PRICE_DROP_MIN_REV_GAIN));
        assertTrue(attributesWithValue.contains(PRICE_DROP_MAX_VALUE));
        verify(lrvDropRestrictionService).deleteConfiguration();
    }

    @Test
    public void shouldBeAbleToSavePriceDropRestrictionTableSettings() {
        when(priceDropRestrictionService.findDefault()).thenReturn(null);
        List<PriceDropRestrictionConfigWrapper> priceDropRestrictionConfigWrappers = getMockPriceDropRestrictionConfigWrappers();
        boolean saved = presenter.saveOrDeleteRecord(priceDropRestrictionConfigWrappers, true);
        verify(priceDropRestrictionService).save(priceDropRestrictionConfigWrappers.get(0).getPriceDropRestrictionConfig());
        assertTrue(saved);
    }

    @Test
    public void shouldNotBeAbleToSavePriceDropRestrictionTableSettings() {
        when(priceDropRestrictionService.findDefault()).thenReturn(PriceDropRestrictionConfig.builder().id(1).build());
        List<PriceDropRestrictionConfigWrapper> priceDropRestrictionConfigWrappers = getMockPriceDropRestrictionConfigWrappers();
        boolean saved = presenter.saveOrDeleteRecord(priceDropRestrictionConfigWrappers, true);
        assertFalse(saved);
    }

    @Test
    public void shouldDeletePriceDropRestrictionTableSettingsWhenFeatureDisabled() {
        when(priceDropRestrictionService.findDefault()).thenReturn(PriceDropRestrictionConfig.builder().id(1).build());
        List<PriceDropRestrictionConfigWrapper> priceDropRestrictionConfigWrappers = getMockPriceDropRestrictionConfigWrappers();
        boolean saved = presenter.saveOrDeleteRecord(priceDropRestrictionConfigWrappers, false);
        verify(priceDropRestrictionService).delete(anyList());
        assertTrue(saved);
    }

    private List<PriceDropRestrictionConfigWrapper> getMockPriceDropRestrictionConfigWrappers() {
        List<PriceDropRestrictionConfigWrapper> priceDropRestrictionConfigWrappers = new ArrayList<>();
        PriceDropRestrictionConfigWrapper priceDropRestrictionConfigWrapper = new PriceDropRestrictionConfigWrapper();
        priceDropRestrictionConfigWrapper.setPriceDropRestrictionConfig(PriceDropRestrictionConfig.builder().id(1).build());
        priceDropRestrictionConfigWrappers.add(priceDropRestrictionConfigWrapper);
        return priceDropRestrictionConfigWrappers;
    }

    @Test
    public void testAddOptimizationSettingsHistoryToExcelForPriceDropDefaultAddOrUpdateConfig() {
        Integer ADD_REVTYPE = 0;
        when(priceDropRestrictionService.getPriceDropRestrictionConfigHistory(OptimizationSettingsEnum.PRICE_DROP_DEFAULT)).thenReturn(getMockDefaultPriceDropRestrictionConfigAuditList(ADD_REVTYPE));
        List<OptimizationSettingExcelDto> optimizationSettingExcelDtoList = presenter.addOptimizationSettingsHistoryToExcel(OptimizationSettingsEnum.PRICE_DROP_DEFAULT);
        assertNotNull(optimizationSettingExcelDtoList);
        assertEquals(4, optimizationSettingExcelDtoList.size());
        OptimizationSettingExcelDto enableRestrictionsExcelDTO = optimizationSettingExcelDtoList.get(0);
        OptimizationSettingExcelDto dtaExcelDTO = optimizationSettingExcelDtoList.get(1);
        OptimizationSettingExcelDto revThresholdExcelDTO = optimizationSettingExcelDtoList.get(2);
        OptimizationSettingExcelDto maxPrcDrpExcelDTO = optimizationSettingExcelDtoList.get(3);

        assertEquals(PriceDropOccupancyType.ENABLE_RESTRICTIONS.getOccupancyType(), enableRestrictionsExcelDTO.getSetting());
        assertEquals("TRUE", enableRestrictionsExcelDTO.getSunday());
        assertEquals("TRUE", enableRestrictionsExcelDTO.getMonday());
        assertEquals("TRUE", enableRestrictionsExcelDTO.getTuesday());
        assertEquals("TRUE", enableRestrictionsExcelDTO.getWednesday());
        assertEquals("TRUE", enableRestrictionsExcelDTO.getThursday());
        assertEquals("TRUE", enableRestrictionsExcelDTO.getFriday());
        assertEquals("FALSE", enableRestrictionsExcelDTO.getSaturday());

        assertEquals(PriceDropOccupancyType.DAYS_TO_ARRIVAL.getOccupancyType(), dtaExcelDTO.getSetting());
        assertEquals("1", dtaExcelDTO.getSunday());
        assertEquals("1", dtaExcelDTO.getMonday());
        assertEquals("1", dtaExcelDTO.getTuesday());
        assertEquals("1", dtaExcelDTO.getWednesday());
        assertEquals("1", dtaExcelDTO.getThursday());
        assertEquals("1", dtaExcelDTO.getFriday());
        assertNull(dtaExcelDTO.getSaturday());

        assertEquals(PriceDropOccupancyType.REVENUE_THRESHOLD_FOR_PRICE.getOccupancyType(), revThresholdExcelDTO.getSetting());
        assertEquals("10", revThresholdExcelDTO.getSunday());
        assertEquals("10", revThresholdExcelDTO.getMonday());
        assertEquals("10", revThresholdExcelDTO.getTuesday());
        assertEquals("10", revThresholdExcelDTO.getWednesday());
        assertEquals("10", revThresholdExcelDTO.getThursday());
        assertEquals("10", revThresholdExcelDTO.getFriday());
        assertNull(revThresholdExcelDTO.getSaturday());

        assertEquals(PriceDropOccupancyType.MAXIMUM_PRICE_DROP.getOccupancyType(), maxPrcDrpExcelDTO.getSetting());
        assertEquals("10", maxPrcDrpExcelDTO.getSunday());
        assertEquals("10", maxPrcDrpExcelDTO.getMonday());
        assertEquals("10", maxPrcDrpExcelDTO.getTuesday());
        assertEquals("10", maxPrcDrpExcelDTO.getWednesday());
        assertEquals("10", maxPrcDrpExcelDTO.getThursday());
        assertEquals("10", maxPrcDrpExcelDTO.getFriday());
        assertNull(maxPrcDrpExcelDTO.getSaturday());
    }

    @Test
    public void testAddOptimizationSettingsHistoryToExcelForPriceDropDefaultDeleteConfig() {
        Integer DELETE_REVTYPE = 2;
        when(priceDropRestrictionService.getPriceDropRestrictionConfigHistory(OptimizationSettingsEnum.PRICE_DROP_DEFAULT)).thenReturn(getMockDefaultPriceDropRestrictionConfigAuditList(DELETE_REVTYPE));
        List<OptimizationSettingExcelDto> optimizationSettingExcelDtoList = presenter.addOptimizationSettingsHistoryToExcel(OptimizationSettingsEnum.PRICE_DROP_DEFAULT);
        assertNotNull(optimizationSettingExcelDtoList);
        assertEquals(1, optimizationSettingExcelDtoList.size());
        OptimizationSettingExcelDto enableRestrictionsExcelDTO = optimizationSettingExcelDtoList.get(0);
        assertEquals(PriceDropOccupancyType.ENABLE_RESTRICTIONS.getOccupancyType(), enableRestrictionsExcelDTO.getSetting());
        assertEquals("FALSE", enableRestrictionsExcelDTO.getSunday());
        assertEquals("FALSE", enableRestrictionsExcelDTO.getMonday());
        assertEquals("FALSE", enableRestrictionsExcelDTO.getTuesday());
        assertEquals("FALSE", enableRestrictionsExcelDTO.getWednesday());
        assertEquals("FALSE", enableRestrictionsExcelDTO.getThursday());
        assertEquals("FALSE", enableRestrictionsExcelDTO.getFriday());
        assertEquals("FALSE", enableRestrictionsExcelDTO.getSaturday());
    }

    @Test
    public void testAddOptimizationSettingsHistoryToExcelForPriceDropSeasonAddOrUpdateConfig() {
        Integer ADD_REVTYPE = 0;
        when(priceDropRestrictionService.getPriceDropRestrictionConfigHistory(OptimizationSettingsEnum.PRICE_DROP_SEASON)).thenReturn(getMockSeasonsPriceDropRestrictionConfigAuditList(ADD_REVTYPE));
        List<OptimizationSettingExcelDto> optimizationSettingExcelDtoList = presenter.addOptimizationSettingsHistoryToExcel(OptimizationSettingsEnum.PRICE_DROP_SEASON);
        assertNotNull(optimizationSettingExcelDtoList);
        assertEquals(4, optimizationSettingExcelDtoList.size());
        OptimizationSettingExcelDto enableRestrictionsExcelDTO = optimizationSettingExcelDtoList.get(0);
        OptimizationSettingExcelDto dtaExcelDTO = optimizationSettingExcelDtoList.get(1);
        OptimizationSettingExcelDto revThresholdExcelDTO = optimizationSettingExcelDtoList.get(2);
        OptimizationSettingExcelDto maxPrcDrpExcelDTO = optimizationSettingExcelDtoList.get(3);

        assertEquals(PriceDropOccupancyType.ENABLE_RESTRICTIONS.getOccupancyType(), enableRestrictionsExcelDTO.getSetting());
        assertNotNull(enableRestrictionsExcelDTO.getStartDate());
        assertNotNull(enableRestrictionsExcelDTO.getEndDate());
        assertEquals("TRUE", enableRestrictionsExcelDTO.getSunday());
        assertEquals("TRUE", enableRestrictionsExcelDTO.getMonday());
        assertEquals("TRUE", enableRestrictionsExcelDTO.getTuesday());
        assertEquals("TRUE", enableRestrictionsExcelDTO.getWednesday());
        assertEquals("TRUE", enableRestrictionsExcelDTO.getThursday());
        assertEquals("TRUE", enableRestrictionsExcelDTO.getFriday());
        assertEquals("FALSE", enableRestrictionsExcelDTO.getSaturday());

        assertEquals(PriceDropOccupancyType.DAYS_TO_ARRIVAL.getOccupancyType(), dtaExcelDTO.getSetting());
        assertNotNull(dtaExcelDTO.getStartDate());
        assertNotNull(dtaExcelDTO.getEndDate());
        assertEquals("1", dtaExcelDTO.getSunday());
        assertEquals("1", dtaExcelDTO.getMonday());
        assertEquals("1", dtaExcelDTO.getTuesday());
        assertEquals("1", dtaExcelDTO.getWednesday());
        assertEquals("1", dtaExcelDTO.getThursday());
        assertEquals("1", dtaExcelDTO.getFriday());
        assertNull(dtaExcelDTO.getSaturday());

        assertEquals(PriceDropOccupancyType.REVENUE_THRESHOLD_FOR_PRICE.getOccupancyType(), revThresholdExcelDTO.getSetting());
        assertNotNull(revThresholdExcelDTO.getStartDate());
        assertNotNull(revThresholdExcelDTO.getEndDate());
        assertEquals("10", revThresholdExcelDTO.getSunday());
        assertEquals("10", revThresholdExcelDTO.getMonday());
        assertEquals("10", revThresholdExcelDTO.getTuesday());
        assertEquals("10", revThresholdExcelDTO.getWednesday());
        assertEquals("10", revThresholdExcelDTO.getThursday());
        assertEquals("10", revThresholdExcelDTO.getFriday());
        assertNull(revThresholdExcelDTO.getSaturday());

        assertEquals(PriceDropOccupancyType.MAXIMUM_PRICE_DROP.getOccupancyType(), maxPrcDrpExcelDTO.getSetting());
        assertNotNull(maxPrcDrpExcelDTO.getStartDate());
        assertNotNull(maxPrcDrpExcelDTO.getEndDate());
        assertEquals("10", maxPrcDrpExcelDTO.getSunday());
        assertEquals("10", maxPrcDrpExcelDTO.getMonday());
        assertEquals("10", maxPrcDrpExcelDTO.getTuesday());
        assertEquals("10", maxPrcDrpExcelDTO.getWednesday());
        assertEquals("10", maxPrcDrpExcelDTO.getThursday());
        assertEquals("10", maxPrcDrpExcelDTO.getFriday());
        assertNull(maxPrcDrpExcelDTO.getSaturday());
    }

    @Test
    public void testAddOptimizationSettingsHistoryToExcelForPriceDropSeasonDeleteConfig() {
        Integer DELETE_REVTYPE = 2;
        when(priceDropRestrictionService.getPriceDropRestrictionConfigHistory(OptimizationSettingsEnum.PRICE_DROP_SEASON)).thenReturn(getMockSeasonsPriceDropRestrictionConfigAuditList(DELETE_REVTYPE));
        List<OptimizationSettingExcelDto> optimizationSettingExcelDtoList = presenter.addOptimizationSettingsHistoryToExcel(OptimizationSettingsEnum.PRICE_DROP_SEASON);
        assertNotNull(optimizationSettingExcelDtoList);
        assertEquals(1, optimizationSettingExcelDtoList.size());
        OptimizationSettingExcelDto enableRestrictionsExcelDTO = optimizationSettingExcelDtoList.get(0);
        assertEquals("delete.price.drop.setting.excel.row", enableRestrictionsExcelDTO.getSetting());
        assertNotNull(enableRestrictionsExcelDTO.getStartDate());
        assertNotNull(enableRestrictionsExcelDTO.getEndDate());
        assertEquals("SSO_User", enableRestrictionsExcelDTO.getUpdatedBy());
    }

    @Test
    public void testAddOptimizationSettingsHistoryToExcelForPriceDropOld() {
        List<PropertyAttribute> propertyAttributes = new ArrayList<>();
        propertyAttributes.add(createPropertyAttribute(PRICE_DROP_RESTRICTION.PRICE_DROP_MIN_REV_GAIN, 1));
        propertyAttributes.add(createPropertyAttribute(PRICE_DROP_RESTRICTION.PRICE_DROP_MIN_DTA, 2));
        propertyAttributes.add(createPropertyAttribute(PRICE_DROP_RESTRICTION.PRICE_DROP_MAX_VALUE, 3));
        when(roaPropertyAttributeService.fetchPropertyAttributesByName(
                Arrays.asList(PRICE_DROP_RESTRICTION.PRICE_DROP_MIN_REV_GAIN.toString(),
                        PRICE_DROP_RESTRICTION.PRICE_DROP_MAX_VALUE.toString(),
                        PRICE_DROP_RESTRICTION.PRICE_DROP_MIN_DTA.toString()))).thenReturn(propertyAttributes);
        List<PropertyAttributeRevision> propertyAttributeRevisions = new ArrayList<>();
        LocalDateTime nowTime = LocalDateTime.now();
        propertyAttributeRevisions.add(createRevision(nowTime, new BigDecimal("100"), ACTIVE_STATUS_ID, 0, PRICE_DROP_RESTRICTION.PRICE_DROP_MIN_REV_GAIN.name()));
        propertyAttributeRevisions.add(createRevision(nowTime, new BigDecimal("3"), ACTIVE_STATUS_ID, 0, PRICE_DROP_RESTRICTION.PRICE_DROP_MIN_DTA.name()));
        propertyAttributeRevisions.add(createRevision(nowTime, new BigDecimal("50"), ACTIVE_STATUS_ID, 0, PRICE_DROP_RESTRICTION.PRICE_DROP_MAX_VALUE.name()));
        when(roaPropertyAttributeService.fetchAttributeRevisionsForMultipleAttributes(Arrays.asList(1, 2, 3))).thenReturn(propertyAttributeRevisions);
        List<OptimizationSettingExcelDto> optimizationSettingExcelDtoList = presenter.addOptimizationSettingsHistoryToExcel(OptimizationSettingsEnum.PRICE_DROP_OLD);
        assertNotNull(optimizationSettingExcelDtoList);
        assertEquals(4, optimizationSettingExcelDtoList.size());
        assertEquals("enable.price.drop.restrictions", optimizationSettingExcelDtoList.get(0).getSetting());
        assertEquals("True", optimizationSettingExcelDtoList.get(0).getValue());
        assertEquals("SSO User", optimizationSettingExcelDtoList.get(0).getUpdatedBy());

        assertEquals("revenue.threshold.for.price", optimizationSettingExcelDtoList.get(1).getSetting());
        assertEquals("100", optimizationSettingExcelDtoList.get(1).getValue());
        assertEquals("SSO User", optimizationSettingExcelDtoList.get(1).getUpdatedBy());

        assertEquals("daysToArrival", optimizationSettingExcelDtoList.get(2).getSetting());
        assertEquals("3", optimizationSettingExcelDtoList.get(2).getValue());
        assertEquals("SSO User", optimizationSettingExcelDtoList.get(2).getUpdatedBy());

        assertEquals("maximum.price.drop", optimizationSettingExcelDtoList.get(3).getSetting());
        assertEquals("50", optimizationSettingExcelDtoList.get(3).getValue());
        assertEquals("SSO User", optimizationSettingExcelDtoList.get(3).getUpdatedBy());
    }

    @Test
    public void testAddOptimizationSettingsHistoryToExcelForCancelRebookSetting() {
        PropertyAttribute cancelRebookPercentage = new PropertyAttribute();
        cancelRebookPercentage.setValue("33");
        cancelRebookPercentage.setId(2);

        List<PropertyAttributeRevision> propertyAttributeRevisions = new ArrayList<>();
        LocalDateTime nowTime = LocalDateTime.now();
        propertyAttributeRevisions.add(createRevision(nowTime, new BigDecimal("100"), ACTIVE_STATUS_ID, 0, PropertyAttributeEnum.CANCEL_REBOOK_PCT.name()));
        propertyAttributeRevisions.add(createRevision(nowTime, new BigDecimal("3"), ACTIVE_STATUS_ID, 0, PropertyAttributeEnum.CANCEL_REBOOK_PCT.name()));

        when(roaPropertyAttributeService.getPropertyAttribute(PropertyAttributeEnum.CANCEL_REBOOK_PCT)).thenReturn(cancelRebookPercentage);
        when(roaPropertyAttributeService.fetchAttributeRevisions(2)).thenReturn(propertyAttributeRevisions);
        List<OptimizationSettingExcelDto> optimizationSettingExcelDtoList = presenter.addOptimizationSettingsHistoryToExcel(OptimizationSettingsEnum.CANCEL_REBOOK);
        assertNotNull(optimizationSettingExcelDtoList);
        assertEquals(2, optimizationSettingExcelDtoList.size());
        assertEquals(PropertyAttributeEnum.CANCEL_REBOOK_PCT.getAttributeName(), optimizationSettingExcelDtoList.get(0).getSetting());
        assertEquals("100", optimizationSettingExcelDtoList.get(0).getValue());
        assertEquals("SSO User", optimizationSettingExcelDtoList.get(0).getUpdatedBy());
        assertEquals(PropertyAttributeEnum.CANCEL_REBOOK_PCT.getAttributeName(), optimizationSettingExcelDtoList.get(1).getSetting());
        assertEquals("3", optimizationSettingExcelDtoList.get(1).getValue());
        assertEquals("SSO User", optimizationSettingExcelDtoList.get(1).getUpdatedBy());
    }

    @Test
    public void testAddOptimizationSettingsHistoryToExcelForOptimizationMethod() {
        PropertyAttribute propertyAttribute = new PropertyAttribute();
        propertyAttribute.setValue("1");
        propertyAttribute.setId(1);
        List<PropertyAttributeRevision> propertyAttributeRevisions = new ArrayList<>();
        LocalDateTime nowTime = LocalDateTime.now();
        propertyAttributeRevisions.add(createRevision(nowTime, BigDecimal.ONE, ACTIVE_STATUS_ID, 0, PropertyAttributeEnum.OPT_DYNAMIC_PRICE.name()));
        propertyAttributeRevisions.add(createRevision(nowTime, BigDecimal.ZERO, ACTIVE_STATUS_ID, 0, PropertyAttributeEnum.OPT_DYNAMIC_PRICE.name()));

        when(roaPropertyAttributeService.getPropertyAttribute(PropertyAttributeEnum.OPT_DYNAMIC_PRICE)).thenReturn(propertyAttribute);
        when(roaPropertyAttributeService.fetchAttributeRevisions(1)).thenReturn(propertyAttributeRevisions);
        List<OptimizationSettingExcelDto> optimizationSettingExcelDtoList = presenter.addOptimizationSettingsHistoryToExcel(OptimizationSettingsEnum.OPTIMIZATION_METHOD);

        assertNotNull(optimizationSettingExcelDtoList);
        assertEquals(2, optimizationSettingExcelDtoList.size());
        assertEquals(PropertyAttributeEnum.OPT_DYNAMIC_PRICE.getAttributeName(), optimizationSettingExcelDtoList.get(0).getSetting());
        assertEquals("Enable", optimizationSettingExcelDtoList.get(0).getValue());
        assertEquals("SSO User", optimizationSettingExcelDtoList.get(0).getUpdatedBy());
        assertEquals(PropertyAttributeEnum.OPT_DYNAMIC_PRICE.getAttributeName(), optimizationSettingExcelDtoList.get(1).getSetting());
        assertEquals("Disable", optimizationSettingExcelDtoList.get(1).getValue());
        assertEquals("SSO User", optimizationSettingExcelDtoList.get(1).getUpdatedBy());
    }

    @Test
    void addOptimizationExcel_forDecoupledLrv_withOnlyMinCapacityRoomClasses() {
        presenter.decoupleLrvDropRestrictionEnabled = true;
        List<PropertyAttributeRevision> lrvDropRevisions = new ArrayList<>();
        addNewRoomClassForLrvMinCapacityConfigs(lrvDropRevisions, jodaDateTime);

        when(lrvDropRestrictionService.fetchAllLrvDropRestrictionConfigurationRevisions()).thenReturn(lrvDropRevisions);
        when(roaPropertyAttributeService.getPropertyAttribute(LRV_DROP_MIN_DTA)).thenReturn(getLrvDropMinDta());
        when(roaPropertyAttributeService.fetchAttributeRevisionsForMultipleAttributes(Collections.singletonList(1))).thenReturn(null);

        List<OptimizationSettingExcelDto> optimizationSettingExcelDtoList = presenter.addOptimizationSettingsHistoryToExcel(OptimizationSettingsEnum.LRV_DROP_RESTRICTION);
        assertEquals(3, optimizationSettingExcelDtoList.size());
    }

    @Test
    void addOptimizationExcel_forDecoupledLrv_withValidLrvConfigs() {
        presenter.decoupleLrvDropRestrictionEnabled = true;
        List<PropertyAttributeRevision> lrvDropRevisions = new ArrayList<>();
        addNewRoomClassForLrvMinCapacityConfigs(lrvDropRevisions, jodaDateTime);
        PropertyAttributeRevision addedDta = createRevision(jodaDateTime.plusSeconds(5), new BigDecimal("12"),
                ACTIVE_STATUS_ID, 0, LRV_DROP_MIN_DTA.getAttributeName());

        when(lrvDropRestrictionService.fetchAllLrvDropRestrictionConfigurationRevisions()).thenReturn(lrvDropRevisions);
        when(roaPropertyAttributeService.getPropertyAttribute(LRV_DROP_MIN_DTA)).thenReturn(getLrvDropMinDta());
        when(roaPropertyAttributeService.fetchAttributeRevisionsForMultipleAttributes(Collections.singletonList(1))).thenReturn(List.of(addedDta));

        List<OptimizationSettingExcelDto> optimizationSettingExcelDto = presenter.addOptimizationSettingsHistoryToExcel(OptimizationSettingsEnum.LRV_DROP_RESTRICTION);
        verifyLrvOptimizationSettingsHistoryForDecoupledLrvValidConfigs(optimizationSettingExcelDto);
    }

    @Test
    void addOptimizationExcel_forDecoupledLrv() {
        presenter.decoupleLrvDropRestrictionEnabled = true;
        List<PropertyAttributeRevision> lrvDropRevisions = new ArrayList<>();
        PropertyAttributeRevision addedDta = createActiveLrvDtaRevision(jodaDateTime);
        addNewRoomClassForLrvMinCapacityConfigs(lrvDropRevisions, jodaDateTime);

        addRoomClassMinCapacityConfigs(lrvDropRevisions, jodaDateTime.plusSeconds(1), ACTIVE_STATUS_ID, 1);
        PropertyAttributeRevision deletedDta = createInactiveLrvDtaRevision(jodaDateTime.plusSeconds(4));
        addRoomClassMinCapacityConfigs(lrvDropRevisions, jodaDateTime.plusSeconds(7), INACTIVE_STATUS_ID, 2);

        addNewRoomClassForLrvMinCapacityConfigs(lrvDropRevisions, jodaDateTime.plusSeconds(10));
        PropertyAttributeRevision activeLrvDta = createActiveLrvDtaRevision(jodaDateTime.plusSeconds(13));

        when(lrvDropRestrictionService.fetchAllLrvDropRestrictionConfigurationRevisions()).thenReturn(lrvDropRevisions);
        when(roaPropertyAttributeService.getPropertyAttribute(LRV_DROP_MIN_DTA)).thenReturn(getLrvDropMinDta());
        when(roaPropertyAttributeService.fetchAttributeRevisionsForMultipleAttributes(Collections.singletonList(1))).thenReturn(List.of(addedDta, deletedDta, activeLrvDta));

        List<OptimizationSettingExcelDto> optimizationSettingExcelDto = presenter.addOptimizationSettingsHistoryToExcel(OptimizationSettingsEnum.LRV_DROP_RESTRICTION);
        verifyLrvDropSettingsExcelDto(optimizationSettingExcelDto);
    }

    private List<PriceDropRestrictionConfigAudit> getMockSeasonsPriceDropRestrictionConfigAuditList(Integer revtype) {
        List<PriceDropRestrictionConfigAudit> priceDropRestrictionConfigAuditList = new ArrayList<>();
        PriceDropRestrictionConfigAudit audit1 = getMockPriceDropRestrictionConfigAudit(revtype);
        audit1.setStartDate(LocalDate.now());
        audit1.setEndDate(LocalDate.now());
        priceDropRestrictionConfigAuditList.add(audit1);
        return priceDropRestrictionConfigAuditList;
    }

    private List<PriceDropRestrictionConfigAudit> getMockDefaultPriceDropRestrictionConfigAuditList(Integer revtype) {
        List<PriceDropRestrictionConfigAudit> priceDropRestrictionConfigAuditList = new ArrayList<>();
        priceDropRestrictionConfigAuditList.add(getMockPriceDropRestrictionConfigAudit(revtype));
        return priceDropRestrictionConfigAuditList;
    }

    private PriceDropRestrictionConfigAudit getMockPriceDropRestrictionConfigAudit(Integer revtype) {
        return PriceDropRestrictionConfigAudit.builder().id(1).revision(1).revType(revtype)
                .prcRevThresholdMethodType(OffsetMethod.FIXED_OFFSET).maxPrcDrpMethodType(OffsetMethod.PERCENTAGE)
                .sundayDTA(BigDecimal.ONE).sundayMaxPrcDrp(BigDecimal.TEN).sundayPrcRevThreshold(BigDecimal.TEN)
                .mondayDTA(BigDecimal.ONE).mondayMaxPrcDrp(BigDecimal.TEN).mondayPrcRevThreshold(BigDecimal.TEN)
                .tuesdayDTA(BigDecimal.ONE).tuesdayMaxPrcDrp(BigDecimal.TEN).tuesdayPrcRevThreshold(BigDecimal.TEN)
                .wednesdayDTA(BigDecimal.ONE).wednesdayMaxPrcDrp(BigDecimal.TEN).wednesdayPrcRevThreshold(BigDecimal.TEN)
                .thursdayDTA(BigDecimal.ONE).thursdayMaxPrcDrp(BigDecimal.TEN).thursdayPrcRevThreshold(BigDecimal.TEN)
                .fridayDTA(BigDecimal.ONE).fridayMaxPrcDrp(BigDecimal.TEN).fridayPrcRevThreshold(BigDecimal.TEN)
                .lastUpdatedByUserId(1).lastUpdatedDate(java.time.LocalDateTime.now()).username("SSO_User").build();
    }

    @Test
    void testDeleteOutOfRangeDOWData() {

        PriceDropRestrictionSeasonUiWrapper season = getPriceDropRestrictionSeasonUiWrapper(new Date("01/11/2024"), new Date("01/12/2024"));

        presenter.deleteOutOfRangeDOWData(season);
        PriceDropRestrictionConfig priceDropRestrictionConfig = season.getPriceDropRestrictionDowDto().getPriceDropRestrictionConfigWrapperList().get(0).getPriceDropRestrictionConfig();

        assertEquals(priceDropRestrictionConfig.getSundayDTA(), BigDecimal.valueOf(-1));
        assertNull(priceDropRestrictionConfig.getSundayPrcRevThreshold());
        assertNull(priceDropRestrictionConfig.getSaturdayMaxPrcDrp());
        assertEquals(priceDropRestrictionConfig.getMondayDTA(), BigDecimal.valueOf(-1));
        assertNull(priceDropRestrictionConfig.getMondayPrcRevThreshold());
        assertNull(priceDropRestrictionConfig.getMondayMaxPrcDrp());
        assertEquals(priceDropRestrictionConfig.getTuesdayDTA(), BigDecimal.valueOf(-1));
        assertNull(priceDropRestrictionConfig.getTuesdayPrcRevThreshold());
        assertNull(priceDropRestrictionConfig.getTuesdayMaxPrcDrp());
        assertEquals(priceDropRestrictionConfig.getWednesdayDTA(), BigDecimal.valueOf(-1));
        assertNull(priceDropRestrictionConfig.getWednesdayPrcRevThreshold());
        assertNull(priceDropRestrictionConfig.getWednesdayMaxPrcDrp());
        assertEquals(priceDropRestrictionConfig.getSaturdayDTA(), BigDecimal.valueOf(-1));
        assertNull(priceDropRestrictionConfig.getSaturdayPrcRevThreshold());
        assertNull(priceDropRestrictionConfig.getSaturdayMaxPrcDrp());

    }

    @Test
    void testApplyOverlappingSeasons() {

        UiContext uiContext = mock(UiContext.class);
        when(uiContext.getSystemCaughtUpDate()).thenReturn(new Date("10/25/2023"));
        inject(presenter, "uiContext", uiContext);

        ArgumentCaptor<List> listArgumentCaptor = ArgumentCaptor.forClass(List.class);
        ArgumentCaptor<PriceDropRestrictionConfig> priceDropRestrictionConfigCapture = ArgumentCaptor.forClass(PriceDropRestrictionConfig.class);

        List<PriceDropRestrictionSeasonUiWrapper> existingSeasons = new ArrayList<>();
        existingSeasons.add(getPriceDropRestrictionSeasonUiWrapper(new Date("11/01/2023"), new Date("11/30/2023")));
        presenter.setPriceDropRestrictionSeasons(existingSeasons);

        PriceDropRestrictionSeasonUiWrapper newSeason = getPriceDropRestrictionSeasonUiWrapper(new Date("11/10/2023"), new Date("11/20/2023"));
        presenter.applyOverlappingSeasons(newSeason);

        verify(priceDropRestrictionService, times(1)).delete(listArgumentCaptor.capture());
        List<PriceDropRestrictionConfig> deletedSeasons = listArgumentCaptor.getValue();

        assertEquals(1, deletedSeasons.size());
        assertEquals(new Date("11/01/2023"), deletedSeasons.get(0).getStartDate());
        assertEquals(new Date("11/30/2023"), deletedSeasons.get(0).getEndDate());

        assertEquals(3, existingSeasons.size());

        verify(priceDropRestrictionService, times(3)).save(priceDropRestrictionConfigCapture.capture());
        List<PriceDropRestrictionConfig> savedConfigs = priceDropRestrictionConfigCapture.getAllValues();
        assertEquals(new Date("11/01/2023"), savedConfigs.get(0).getStartDate());
        assertEquals(new Date("11/09/2023"), savedConfigs.get(0).getEndDate());
        assertEquals(new Date("11/10/2023"), savedConfigs.get(1).getStartDate());
        assertEquals(new Date("11/20/2023"), savedConfigs.get(1).getEndDate());
        assertEquals(new Date("11/21/2023"), savedConfigs.get(2).getStartDate());
        assertEquals(new Date("11/30/2023"), savedConfigs.get(2).getEndDate());

    }

    @Test
    void shouldDecoupleLrvDropRestrictions() {
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.DECOUPLE_LRV_DROP_RESTRICTION_ENABLED)).thenReturn(true);
        presenter.pacmanConfigParamsService = pacmanConfigParamsService;
        assertTrue(presenter.isDecoupleLrvDropRestrictionEnabled());
    }

    @Test
    void shouldNotDecoupleLrvDropRestrictions() {
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.DECOUPLE_LRV_DROP_RESTRICTION_ENABLED)).thenReturn(false);
        presenter.pacmanConfigParamsService = pacmanConfigParamsService;
        assertFalse(presenter.isDecoupleLrvDropRestrictionEnabled());
    }

    @Test
    void shouldBeAbleSavePriceDropAndLrvDropConfigsWhenDecoupleEnabled() {
        presenter.optDynamicPrice = "1";
        presenter.decoupleLrvDropRestrictionEnabled = true;
        presenter.uiWrapper.setOptimizationMethodOption(new OptimizationMethodOption(1, ""));
        setPropertyAttributes_optDynamicPrice_And_cancelRebook();

        PriceDropRestrictionDto priceDropRestrictionDto = getPriceDropRestrictionDto(0, true);
        priceDropRestrictionDto.setDecoupleLrvDropRestriction(true);
        when(view.getPriceDropRestrictionDto()).thenReturn(priceDropRestrictionDto);
        when(view.priceDropRestrictionLayoutHasChanges()).thenReturn(true);
        when(view.isPriceDropRestrictionLayoutValid()).thenReturn(true);
        when(view.lrvDropRestrictionLayoutHasChanges()).thenReturn(true);
        when(view.isLrvDropRestrictionLayoutValid()).thenReturn(true);
        when(view.lrvMinDtaHasChanges()).thenReturn(true);

        LrvDropRestrictionDto dto = getLrvDropRestrictionDtoWithDTA();
        when(view.getLrvDropRestrictionLayoutBean()).thenReturn(dto);
        when(view.isLrvRestrictionCheckBoxEnabled()).thenReturn(true);
        ArgumentCaptor<Map> mapArgumentCaptor = ArgumentCaptor.forClass(Map.class);
        ArgumentCaptor<Map> mapArgumentCaptorForLrvDrop = ArgumentCaptor.forClass(Map.class);

        presenter.save();

        verify(roaPropertyAttributeService).saveMultiplePropertyAttributes(mapArgumentCaptor.capture());
        HashMap<String, String> attributesWithValue = (HashMap<String, String>) mapArgumentCaptor.getValue();
        verifyPriceDropSettings(attributesWithValue);

        verify(lrvDropRestrictionService).saveConfiguration(mapArgumentCaptorForLrvDrop.capture());
        Map<Integer, Integer> rcWithCapacity = mapArgumentCaptorForLrvDrop.getValue();
        assertTrue(rcWithCapacity.containsKey(1));
        assertEquals(12, rcWithCapacity.get(1).intValue());
        verify(view, Mockito.atLeastOnce()).showSaveSuccessMessage();
        verify(roaPropertyAttributeService, Mockito.atLeastOnce()).saveOverrideByAttributeName(LRV_DROP_MIN_DTA.getAttributeName(), "20");
    }

    @Test
    void shouldShowValidationMessage_whenSavingDecoupledPriceDropConfigs_forInvalidLayout() {
        presenter.optDynamicPrice = "1";
        presenter.decoupleLrvDropRestrictionEnabled = true;
        presenter.uiWrapper.setOptimizationMethodOption(new OptimizationMethodOption(1, ""));
        setPropertyAttributes_optDynamicPrice_And_cancelRebook();

        when(view.getPriceDropRestrictionDto()).thenReturn(getPriceDropRestrictionDto(0, true));
        when(view.priceDropRestrictionLayoutHasChanges()).thenReturn(true);
        when(view.isPriceDropRestrictionLayoutValid()).thenReturn(false);
        when(view.isPriceRestrictionCheckBoxEnabled()).thenReturn(true);

        presenter.save();
        verify(view, Mockito.atLeastOnce()).showValidationMessage(Mockito.anyString());
    }

    @Test
    void shouldBeAbleToDeletePriceDropSettingsWhenDecoupleEnabled() {
        presenter.optDynamicPrice = "1";
        presenter.decoupleLrvDropRestrictionEnabled = true;
        presenter.uiWrapper.setOptimizationMethodOption(new OptimizationMethodOption(1, ""));
        setPropertyAttributes_optDynamicPrice_And_cancelRebook();

        PriceDropRestrictionDto priceDropRestrictionDto = getPriceDropRestrictionDto(0, false);
        priceDropRestrictionDto.setDecoupleLrvDropRestriction(true);
        when(view.getPriceDropRestrictionDto()).thenReturn(priceDropRestrictionDto);
        when(view.priceDropRestrictionLayoutHasChanges()).thenReturn(true);
        when(view.isPriceDropRestrictionLayoutValid()).thenReturn(true);

        when(view.lrvDropRestrictionLayoutHasChanges()).thenReturn(false);
        when(view.isLrvDropRestrictionLayoutValid()).thenReturn(true);
        ArgumentCaptor<ArrayList> mapArgumentCaptor = ArgumentCaptor.forClass(ArrayList.class);

        presenter.save();

        verify(roaPropertyAttributeService).deletePropertyAttribute(mapArgumentCaptor.capture());
        verify(view, Mockito.atLeastOnce()).showSaveSuccessMessage();
    }

    @Test
    void shouldBeAbleSaveOnlyLrvDropConfigsWhenDecoupleEnabled() {
        presenter.optDynamicPrice = "1";
        presenter.decoupleLrvDropRestrictionEnabled = true;
        presenter.uiWrapper.setOptimizationMethodOption(new OptimizationMethodOption(1, ""));
        setPropertyAttributes_optDynamicPrice_And_cancelRebook();

        when(view.priceDropRestrictionLayoutHasChanges()).thenReturn(false);
        when(view.isPriceDropRestrictionLayoutValid()).thenReturn(true);
        when(view.lrvDropRestrictionLayoutHasChanges()).thenReturn(true);
        when(view.isLrvDropRestrictionLayoutValid()).thenReturn(true);
        when(view.lrvMinDtaHasChanges()).thenReturn(true);

        LrvDropRestrictionDto dto = getLrvDropRestrictionDtoWithDTA();
        when(view.getLrvDropRestrictionLayoutBean()).thenReturn(dto);
        when(view.isLrvRestrictionCheckBoxEnabled()).thenReturn(true);
        ArgumentCaptor<Map> mapArgumentCaptorForLrvDrop = ArgumentCaptor.forClass(Map.class);

        presenter.save();

        verify(lrvDropRestrictionService).saveConfiguration(mapArgumentCaptorForLrvDrop.capture());
        Map<Integer, Integer> rcWithCapacity = mapArgumentCaptorForLrvDrop.getValue();
        assertTrue(rcWithCapacity.containsKey(1));
        assertEquals(12, rcWithCapacity.get(1).intValue());

        verify(view, Mockito.atLeastOnce()).showSaveSuccessMessage();
        verify(roaPropertyAttributeService, Mockito.atLeastOnce()).saveOverrideByAttributeName(LRV_DROP_MIN_DTA.getAttributeName(), "20");
    }

    @Test
    void shouldShowValidationMessage_whenSavingDecoupledLrvConfigs_forInvalidLayout() {
        presenter.optDynamicPrice = "1";
        presenter.decoupleLrvDropRestrictionEnabled = true;
        presenter.uiWrapper.setOptimizationMethodOption(new OptimizationMethodOption(1, ""));
        setPropertyAttributes_optDynamicPrice_And_cancelRebook();

        when(view.lrvDropRestrictionLayoutHasChanges()).thenReturn(true);
        when(view.isLrvDropRestrictionLayoutValid()).thenReturn(false);
        when(view.getLrvDropRestrictionLayoutBean()).thenReturn(getLrvDropRestrictionDtoWithDTA());
        when(view.isLrvRestrictionCheckBoxEnabled()).thenReturn(true);

        presenter.save();
        verify(view, Mockito.atLeastOnce()).showValidationMessage(Mockito.anyString());
    }

    @Test
    void shouldBeAbleDeleteLrvDrop() {
        presenter.optDynamicPrice = "1";
        presenter.uiWrapper.setOptimizationMethodOption(new OptimizationMethodOption(1, ""));
        presenter.decoupleLrvDropRestrictionEnabled = true;
        presenter.enablePriceDropRestriction = false;
        setPropertyAttributes_optDynamicPrice_And_cancelRebook();

        LrvDropRestrictionDto dto = getLrvDropRestrictionDtoWithDTA();
        when(view.lrvDropRestrictionLayoutHasChanges()).thenReturn(true);
        when(view.isLrvDropRestrictionLayoutValid()).thenReturn(true);
        when(view.getLrvDropRestrictionLayoutBean()).thenReturn(dto);
        when(view.isLrvRestrictionCheckBoxEnabled()).thenReturn(false);

        presenter.save();

        verify(lrvDropRestrictionService).deleteConfiguration();
        verify(roaPropertyAttributeService).deletePropertyAttribute(Collections.singletonList(LRV_DROP_MIN_DTA.getAttributeName()));
    }

    @Test
    void shouldEnableLrvDropRestriction() {
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.ENABLE_LRV_DROP_RESTRICTION_FEATURE)).thenReturn(true);
        presenter.pacmanConfigParamsService = pacmanConfigParamsService;
        assertTrue(presenter.isLrvDropRestrictionEnabled());
    }

    @Test
    void shouldDisableLrvDropRestriction() {
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.ENABLE_LRV_DROP_RESTRICTION_FEATURE)).thenReturn(false);
        presenter.pacmanConfigParamsService = pacmanConfigParamsService;
        assertFalse(presenter.isLrvDropRestrictionEnabled());
    }

    private PropertyAttribute getLrvDropMinDta() {
        PropertyAttribute lrvDropMinDta = new PropertyAttribute();
        lrvDropMinDta.setAttributeName(LRV_DROP_MIN_DTA.getAttributeName());
        lrvDropMinDta.setId(1);
        lrvDropMinDta.setValue("12");
        return lrvDropMinDta;
    }

    private PropertyAttributeRevision createActiveLrvDtaRevision(LocalDateTime localDateTime) {
        return createRevision(localDateTime, new BigDecimal("12"), ACTIVE_STATUS_ID, 0, LRV_DROP_MIN_DTA.getAttributeName());
    }

    private PropertyAttributeRevision createInactiveLrvDtaRevision(LocalDateTime localDateTime) {
        return createRevision(localDateTime, new BigDecimal("12"), INACTIVE_STATUS_ID, 2, LRV_DROP_MIN_DTA.getAttributeName());
    }

    private void addRoomClassMinCapacityConfigs(List<PropertyAttributeRevision> lrvDropRevisions, LocalDateTime updatedDateTime, Integer statusId, int revType) {
        lrvDropRevisions.add(createRevision(updatedDateTime, new BigDecimal("100"), statusId, revType, "STD"));
        lrvDropRevisions.add(createRevision(updatedDateTime, new BigDecimal("3"), statusId, revType, "DLX"));
        lrvDropRevisions.add(createRevision(updatedDateTime, new BigDecimal("50"), statusId, revType, "SUITE"));
    }

    private void addNewRoomClassForLrvMinCapacityConfigs(List<PropertyAttributeRevision> lrvDropRevisions, LocalDateTime localDateTime) {
        lrvDropRevisions.add(createRevision(localDateTime, new BigDecimal("0"), ACTIVE_STATUS_ID, 0, "STD"));
        lrvDropRevisions.add(createRevision(localDateTime, new BigDecimal("0"), ACTIVE_STATUS_ID, 0, "DLX"));
        lrvDropRevisions.add(createRevision(localDateTime, new BigDecimal("0"), ACTIVE_STATUS_ID, 0, "SUITE"));
    }

    private void verifyLrvOptimizationSettingsHistoryForDecoupledLrvValidConfigs(List<OptimizationSettingExcelDto> optimizationSettingExcelDto) {
        assertEquals(5, optimizationSettingExcelDto.size());
        assertLrvHistory(optimizationSettingExcelDto.get(4), "Added Capacity for SUITE", javaDateTime, "0", SSO_USER);
        assertLrvHistory(optimizationSettingExcelDto.get(3), "Added Capacity for DLX", javaDateTime, "0", SSO_USER);
        assertLrvHistory(optimizationSettingExcelDto.get(2), "Added Capacity for STD", javaDateTime, "0", SSO_USER);
        assertLrvHistory(optimizationSettingExcelDto.get(1), "enable.lrv.drop.restrictions",
                javaDateTime.plusSeconds(5).minus(1, ChronoUnit.MILLIS),"True", SSO_USER);
        assertLrvHistory(optimizationSettingExcelDto.get(0), "daysToArrival", javaDateTime.plusSeconds(5), "12", SSO_USER);
    }

    private void verifyLrvDropSettingsExcelDto(List<OptimizationSettingExcelDto> optimizationSettingExcelDto) {
        assertEquals(14, optimizationSettingExcelDto.size());
        assertLrvHistory(optimizationSettingExcelDto.get(13), "enable.lrv.drop.restrictions",
                javaDateTime.minus(1, ChronoUnit.MILLIS), "True", SSO_USER);
        assertLrvHistory(optimizationSettingExcelDto.get(12), "daysToArrival", javaDateTime, "12", SSO_USER);
        assertLrvHistory(optimizationSettingExcelDto.get(11), "Added Capacity for SUITE", javaDateTime, "0", SSO_USER);
        assertLrvHistory(optimizationSettingExcelDto.get(10), "Added Capacity for DLX", javaDateTime, "0", SSO_USER);
        assertLrvHistory(optimizationSettingExcelDto.get(9), "Added Capacity for STD", javaDateTime, "0", SSO_USER);
        assertLrvHistory(optimizationSettingExcelDto.get(8), "Updated Capacity for SUITE", javaDateTime.plusSeconds(1), "50", SSO_USER);
        assertLrvHistory(optimizationSettingExcelDto.get(7), "Updated Capacity for DLX", javaDateTime.plusSeconds(1), "3", SSO_USER);
        assertLrvHistory(optimizationSettingExcelDto.get(6), "Updated Capacity for STD", javaDateTime.plusSeconds(1), "100", SSO_USER);
        assertLrvHistory(optimizationSettingExcelDto.get(5), "enable.lrv.drop.restrictions",
                javaDateTime.plusSeconds(4).minus(1, ChronoUnit.MILLIS), "False", SSO_USER);
        assertLrvHistory(optimizationSettingExcelDto.get(4), "Added Capacity for SUITE", javaDateTime.plusSeconds(10), "0", SSO_USER);
        assertLrvHistory(optimizationSettingExcelDto.get(3), "Added Capacity for DLX", javaDateTime.plusSeconds(10), "0", SSO_USER);
        assertLrvHistory(optimizationSettingExcelDto.get(2), "Added Capacity for STD", javaDateTime.plusSeconds(10), "0", SSO_USER);
        assertLrvHistory(optimizationSettingExcelDto.get(1), "enable.lrv.drop.restrictions",
                javaDateTime.plusSeconds(13).minus(1, ChronoUnit.MILLIS), "True", SSO_USER);
        assertLrvHistory(optimizationSettingExcelDto.get(0), "daysToArrival", javaDateTime.plusSeconds(13), "12", SSO_USER);
    }

    private void assertLrvHistory(OptimizationSettingExcelDto lrvDropRestrictionHistory, String expectedAttrName, java.time.LocalDateTime dateTime, String expectedValue, String expectedUserName) {
        assertEquals(expectedAttrName, lrvDropRestrictionHistory.getSetting());
        assertEquals(dateTime, lrvDropRestrictionHistory.getUpdatedOn());
        assertEquals(expectedValue, lrvDropRestrictionHistory.getValue());
        assertEquals(expectedUserName, lrvDropRestrictionHistory.getUpdatedBy());
    }

    private PropertyAttribute getPropertyAttribute(Integer id) {
        PropertyAttribute propertyAttribute = new PropertyAttribute();
        propertyAttribute.setId(id);
        return propertyAttribute;
    }

    private void setPropertyAttributes_optDynamicPrice_And_cancelRebook() {
        PropertyAttribute propertyAttribute = getPropertyAttribute(1);
        when(roaPropertyAttributeService.getPropertyAttribute(PropertyAttributeEnum.OPT_DYNAMIC_PRICE)).thenReturn(propertyAttribute);
        when(roaPropertyAttributeService.getAttributeValue(1)).thenReturn("1");

        PropertyAttribute cancelRebookPropertyAttribute = getPropertyAttribute(2);
        when(roaPropertyAttributeService.getPropertyAttribute(PropertyAttributeEnum.CANCEL_REBOOK_PCT)).thenReturn(cancelRebookPropertyAttribute);
    }

    private PriceDropRestrictionDto getPriceDropRestrictionDto(int val, boolean enablePriceDropRestriction) {
        PriceDropRestrictionDto priceDropRestrictionDto = new PriceDropRestrictionDto();
        priceDropRestrictionDto.setDaysToArrival(2);
        priceDropRestrictionDto.setMaximumPriceDrop(new BigDecimal(val));
        priceDropRestrictionDto.setRevenueThresholdForPrice(new BigDecimal(100));
        priceDropRestrictionDto.setEnablePriceDropRestriction(enablePriceDropRestriction);
        return priceDropRestrictionDto;
    }

    private LrvRoomClassRemainingCapacity getLrvRoomClassRemainingCapacity() {
        LrvRoomClassRemainingCapacity rcRemCap = new LrvRoomClassRemainingCapacity();
        rcRemCap.setAccomClassId(1);
        rcRemCap.setAccomClassName("STD");
        rcRemCap.setMaxAllowedRoomCapacity(50);
        rcRemCap.setRemainingCapacity(new SingleValueModel<>() {
            @Override
            public void setValue(Integer integer) {
            }

            @Override
            public Integer getValue() {
                return 12;
            }
        });
        return rcRemCap;
    }

    private LrvDropRestrictionDto getLrvDropRestrictionDtoWithDTA() {
        LrvDropRestrictionDto dto = new LrvDropRestrictionDto();
        List<LrvRoomClassRemainingCapacity> roomClassCapacities = new ArrayList<>();
        LrvRoomClassRemainingCapacity rcRemCap = getLrvRoomClassRemainingCapacity();
        roomClassCapacities.add(rcRemCap);
        dto.setRoomClassCapacities(roomClassCapacities);
        dto.setEnableLrvDropRestriction(true);
        dto.setDaysToArrival(20);
        dto.setEnableLrvDropRestriction(true);
        return dto;
    }

    private void verifyPriceDropSettings(HashMap<String, String> attributesWithValue) {
        assertTrue(attributesWithValue.containsKey(PRICE_DROP_MIN_DTA));
        assertTrue(attributesWithValue.containsKey(PRICE_DROP_MIN_REV_GAIN));
        assertTrue(attributesWithValue.containsKey(PRICE_DROP_MAX_VALUE));
    }

    private void verifyLrvRoomClassData(LrvRoomClassRemainingCapacity lrvRoomClassRemainingCapacity, int accomClassId, String accomClassName,
                                        int remainingCap, int maxCap) {
        assertEquals(accomClassId, lrvRoomClassRemainingCapacity.getAccomClassId().intValue());
        assertEquals(accomClassName, lrvRoomClassRemainingCapacity.getAccomClassName());
        assertEquals(remainingCap, lrvRoomClassRemainingCapacity.getRemainingCapacity().getValue().intValue());
        assertEquals(maxCap, lrvRoomClassRemainingCapacity.getMaxAllowedRoomCapacity().intValue());
    }

    private List<LrvDropRestrictionConfiguration> getLrvDropRestrictionConfigurations(int stdMinCap, int dlxMinCap, int steMinCap) {
        List<LrvDropRestrictionConfiguration> lrvDropConfigurations = new ArrayList<>();
        lrvDropConfigurations.add(createLrvDropConfig(2, "STD", stdMinCap, 120));
        lrvDropConfigurations.add(createLrvDropConfig(3, "DLX", dlxMinCap, 100));
        lrvDropConfigurations.add(createLrvDropConfig(4, "STE", steMinCap, 80));
        return lrvDropConfigurations;
    }

    private void enableDecoupleLrvRelatedToggles() {
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.ENABLE_PRICE_DROP_RESTRICTION_FEATURE))
                .thenReturn(false);
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.ENABLE_LRV_DROP_RESTRICTION_FEATURE))
                .thenReturn(true);
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.DECOUPLE_LRV_DROP_RESTRICTION_ENABLED))
                .thenReturn(true);
        presenter.pacmanConfigParamsService = pacmanConfigParamsService;
    }

    private PriceDropRestrictionSeasonUiWrapper getPriceDropRestrictionSeasonUiWrapper(Date startDate, Date endDate) {
        PriceDropRestrictionSeasonUiWrapper season = new PriceDropRestrictionSeasonUiWrapper();
        season.setPriceDropRestrictionDowDto(new PriceDropRestrictionDowDto());
        PriceDropRestrictionConfig mockConfig = getMockConfig();
        mockConfig.setStartDate(startDate);
        mockConfig.setEndDate(endDate);

        PriceDropRestrictionConfigWrapper wrapper1 = getMockWrapper(PriceDropOccupancyType.DAYS_TO_ARRIVAL);
        PriceDropRestrictionConfigWrapper wrapper2 = getMockWrapper(PriceDropOccupancyType.ENABLE_RESTRICTIONS);
        PriceDropRestrictionConfigWrapper wrapper3 = getMockWrapper(PriceDropOccupancyType.MAXIMUM_PRICE_DROP);
        PriceDropRestrictionConfigWrapper wrapper4 = getMockWrapper(PriceDropOccupancyType.REVENUE_THRESHOLD_FOR_PRICE);

        wrapper1.setPriceDropRestrictionConfig(mockConfig);
        wrapper2.setPriceDropRestrictionConfig(mockConfig);
        wrapper3.setPriceDropRestrictionConfig(mockConfig);
        wrapper4.setPriceDropRestrictionConfig(mockConfig);


        season.getPriceDropRestrictionDowDto().setPriceDropRestrictionConfigWrapperList(new ArrayList<PriceDropRestrictionConfigWrapper>(List.of(wrapper1, wrapper2, wrapper3, wrapper4)));
        season.setStartDate(new org.joda.time.LocalDate(startDate));
        season.setEndDate(new org.joda.time.LocalDate(endDate));
        return season;
    }

    private PriceDropRestrictionConfig getMockConfig() {
        PriceDropRestrictionConfig priceDropRestrictionConfig = PriceDropRestrictionConfig.builder().id(1)
                .maxPrcDrpMethodType(OffsetMethod.FIXED_OFFSET)
                .prcRevThresholdMethodType(OffsetMethod.PERCENTAGE)
                .sundayDTA(new BigDecimal(12))
                .sundayMaxPrcDrp(new BigDecimal(54))
                .sundayPrcRevThreshold(new BigDecimal(12))
                .mondayDTA(new BigDecimal(24))
                .mondayMaxPrcDrp(new BigDecimal(23))
                .mondayPrcRevThreshold(new BigDecimal(45))
                .tuesdayDTA(new BigDecimal(20))
                .tuesdayMaxPrcDrp(new BigDecimal(47))
                .tuesdayPrcRevThreshold(new BigDecimal(45))
                .wednesdayDTA(new BigDecimal(15))
                .wednesdayMaxPrcDrp(new BigDecimal(42))
                .wednesdayPrcRevThreshold(new BigDecimal(25))
                .saturdayDTA(new BigDecimal(30))
                .saturdayMaxPrcDrp(new BigDecimal(25))
                .saturdayPrcRevThreshold(new BigDecimal(26)).build();
        return priceDropRestrictionConfig;
    }

    private PriceDropRestrictionConfigWrapper getMockWrapper(PriceDropOccupancyType priceDropOccupancyType) {
        PriceDropRestrictionConfigWrapper element = new PriceDropRestrictionConfigWrapper();
        element.setPriceDropOccupancyType(priceDropOccupancyType);
        return element;
    }

    private static java.time.LocalDateTime convertJodaLocalDateTimeToJavaLocalDateTime() {
        return java.time.LocalDateTime.ofInstant(
                java.time.Instant.ofEpochMilli(jodaDateTime.toDateTime(DateTimeZone.forID(ZoneId.systemDefault().getId())).getMillis()), ZoneId.systemDefault());
    }
}
