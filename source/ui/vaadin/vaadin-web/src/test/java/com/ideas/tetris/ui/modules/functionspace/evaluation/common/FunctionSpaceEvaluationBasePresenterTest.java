package com.ideas.tetris.ui.modules.functionspace.evaluation.common;

import com.ideas.infra.tetris.security.domain.TetrisPermissionKey;
import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.configparams.GUIConfigParamName;
import com.ideas.tetris.pacman.common.configparams.IPConfigParamName;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomClass;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomType;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.OccupancyDateCapacity;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.TenantStatusEnum;
import com.ideas.tetris.pacman.services.crudservice.MultiPropertyCrudServiceBean;
import com.ideas.tetris.pacman.services.dashboard.DashboardService;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.dateservice.dto.DateParameter;
import com.ideas.tetris.pacman.services.functionspace.configuration.entity.FunctionSpaceCombinationFunctionRoom;
import com.ideas.tetris.pacman.services.functionspace.configuration.entity.FunctionSpaceEvalPackagePricing;
import com.ideas.tetris.pacman.services.functionspace.configuration.entity.FunctionSpaceEvalPackagePricingDOS;
import com.ideas.tetris.pacman.services.functionspace.configuration.entity.FunctionSpaceFunctionRoom;
import com.ideas.tetris.pacman.services.functionspace.configuration.entity.FunctionSpacePackage;
import com.ideas.tetris.pacman.services.functionspace.configuration.entity.FunctionSpacePackageType;
import com.ideas.tetris.pacman.services.functionspace.configuration.entity.FunctionSpaceResourceType;
import com.ideas.tetris.pacman.services.functionspace.configuration.entity.FunctionSpaceRevenueGroup;
import com.ideas.tetris.pacman.services.functionspace.configuration.packaging.dto.RevenueGroupDto;
import com.ideas.tetris.pacman.services.functionspace.configuration.service.FunctionSpaceConfigurationService;
import com.ideas.tetris.pacman.services.functionspace.configuration.service.FunctionSpacePackageEvalService;
import com.ideas.tetris.pacman.services.functionspace.configuration.service.FunctionSpacePackageService;
import com.ideas.tetris.pacman.services.functionspace.demandcalendar.service.FunctionSpaceDemandCalendarService;
import com.ideas.tetris.pacman.services.functionspace.service.FunctionSpaceService;
import com.ideas.tetris.pacman.services.groupblock.GroupBlockDetail;
import com.ideas.tetris.pacman.services.groupblock.GroupBlockMaster;
import com.ideas.tetris.pacman.services.grouppricing.configuration.dto.ConferenceAndBanquetDto;
import com.ideas.tetris.pacman.services.grouppricing.configuration.entity.GroupPricingConfigurationAncillaryAssignment;
import com.ideas.tetris.pacman.services.grouppricing.configuration.entity.GroupPricingConfigurationAncillaryStream;
import com.ideas.tetris.pacman.services.grouppricing.configuration.entity.GroupPricingConfigurationConferenceAndBanquet;
import com.ideas.tetris.pacman.services.grouppricing.configuration.entity.GroupPricingEvaluationMethod;
import com.ideas.tetris.pacman.services.grouppricing.configuration.entity.packaging.GroupPricingConfigurationPackage;
import com.ideas.tetris.pacman.services.grouppricing.configuration.service.ConferenceAndBanquetService;
import com.ideas.tetris.pacman.services.grouppricing.configuration.service.GroupPricingConfigurationAncillaryService;
import com.ideas.tetris.pacman.services.grouppricing.configuration.service.GroupPricingConfigurationService;
import com.ideas.tetris.pacman.services.grouppricing.configuration.service.packaging.GroupPricingPackageService;
import com.ideas.tetris.pacman.services.grouppricing.evaluation.dto.AlternateDateDto;
import com.ideas.tetris.pacman.services.grouppricing.evaluation.dto.CurrentBarDTO;
import com.ideas.tetris.pacman.services.grouppricing.evaluation.dto.CurrentBarDetails;
import com.ideas.tetris.pacman.services.grouppricing.evaluation.dto.GroupEvaluationOnBooksGroupDetail;
import com.ideas.tetris.pacman.services.grouppricing.evaluation.dto.GroupEvaluationOnBooksGroupRoomClassDetail;
import com.ideas.tetris.pacman.services.grouppricing.evaluation.dto.GroupEvaluationRevision;
import com.ideas.tetris.pacman.services.grouppricing.evaluation.dto.dto.GroupEvaluationConferenceAndBanquetDto;
import com.ideas.tetris.pacman.services.grouppricing.evaluation.entity.*;
import com.ideas.tetris.pacman.services.grouppricing.evaluation.entity.packaging.GroupEvaluationGroupPricingPackageDetail;
import com.ideas.tetris.pacman.services.grouppricing.evaluation.entity.packaging.GroupEvaluationGroupPricingPackageRevenueByArrivalDate;
import com.ideas.tetris.pacman.services.grouppricing.evaluation.entity.packaging.GroupEvaluationGroupPricingPackageRevenueByRevenueGroup;
import com.ideas.tetris.pacman.services.grouppricing.evaluation.service.ConferenceAndBanquetEvaluationService;
import com.ideas.tetris.pacman.services.grouppricing.evaluation.service.GroupEvaluationAlternateDateService;
import com.ideas.tetris.pacman.services.grouppricing.evaluation.service.GroupEvaluationMultiPropertyService;
import com.ideas.tetris.pacman.services.grouppricing.evaluation.service.GroupEvaluationOnBooksService;
import com.ideas.tetris.pacman.services.grouppricing.evaluation.service.GroupEvaluationService;
import com.ideas.tetris.pacman.services.marketsegment.entity.MarketSegmentSummary;
import com.ideas.tetris.pacman.services.pricingconfiguration.service.PricingConfigurationService;
import com.ideas.tetris.pacman.services.property.PropertyService;
import com.ideas.tetris.pacman.services.specialevent.dto.SpecialEventSummaryDto;
import com.ideas.tetris.pacman.services.specialevent.service.SpecialEventService;
import com.ideas.tetris.pacman.services.tax.entity.Tax;
import com.ideas.tetris.pacman.services.tax.service.TaxService;
import com.ideas.tetris.pacman.util.BigDecimalUtil;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import com.ideas.tetris.platform.services.daoandentities.entity.Property;
import com.ideas.tetris.ui.VaadinUIBaseJupiterTest;
import com.ideas.tetris.ui.common.cdi.cdiutils.Lang;
import com.ideas.tetris.ui.common.component.date.mulitdateselector.MultiDateSelections;
import com.ideas.tetris.ui.common.data.fieldgroup.TetrisBeanFieldGroup;
import com.ideas.tetris.ui.common.security.UiContext;
import com.ideas.tetris.ui.common.util.UiUtils;
import com.ideas.tetris.ui.modules.functionspace.evaluation.EvaluationConstants;
import com.ideas.tetris.ui.modules.functionspace.evaluation.FunctionSpaceEvaluationTestData;
import com.ideas.tetris.ui.modules.functionspace.evaluation.GroupEvaluationUiWrapper;
import com.ideas.tetris.ui.modules.functionspace.evaluation.PropertyUiWrapper;
import com.ideas.tetris.ui.modules.functionspace.evaluation.details.*;
import com.ideas.tetris.ui.modules.functionspace.evaluation.details.packagepricing.PackageDto;
import com.ideas.tetris.ui.modules.functionspace.evaluation.details.packagepricing.PackagePricingRow;
import com.ideas.tetris.ui.modules.grouppricing.configuration.general.GroupPricingGeneralMeasurementEnum;
import com.ideas.tetris.ui.modules.grouppricing.evaluation.GroupEvaluationDateOfStay;
import com.ideas.tetris.ui.modules.grouppricing.evaluation.GroupEvaluationInputWrapper;
import com.ideas.tetris.ui.modules.grouppricing.evaluation.GroupEvaluationRevisionWrapper;
import org.apache.commons.lang.StringUtils;
import org.joda.time.LocalDate;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.Spy;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedHashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Stream;

import static com.ideas.tetris.pacman.services.functionspace.configuration.entity.FunctionSpaceResourceType.OTHER_RESOURCE_TYPE;
import static com.ideas.tetris.pacman.services.functionspace.configuration.entity.FunctionSpaceResourceType.RENTAL_RESOURCE_TYPE;
import static com.ideas.tetris.ui.modules.functionspace.evaluation.EvaluationConstants.*;
import static java.util.Collections.emptyList;
import static java.util.Collections.singleton;
import static java.util.Collections.singletonList;
import static java.util.stream.Collectors.toList;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertSame;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.fail;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.anyInt;
import static org.mockito.Mockito.anyList;
import static org.mockito.Mockito.anyMap;
import static org.mockito.Mockito.anySet;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyZeroInteractions;
import static org.mockito.Mockito.when;

public class FunctionSpaceEvaluationBasePresenterTest extends VaadinUIBaseJupiterTest {
    private static final String TEST_FUNCTIN_ROOM = "Test Function Room";
    private static final String TEST_MARKETSEGMENT_CODE = "MARKET_SEGMENT1";

    private static final boolean READ_ONLY_TRUE = true;
    private static final boolean IS_GROUP_PRICING_TRUE = true;
    private static final Integer GROUP_EVALUATION_ID_10 = 10;
    private static final Integer MULTI_GROUP_EVALUATION_ID_15 = 15;
    private static final Integer USER_ID_TEST = 1;


    @Mock
    GroupEvaluationService groupEvaluationService;

    @Mock
    GroupEvaluationMultiPropertyService groupEvaluationMultiPropertyService;

    @Mock
    FunctionSpaceService functionSpaceService;

    @Mock
    FunctionSpaceConfigurationService functionSpaceConfigurationService;

    @Mock
    TetrisBeanFieldGroup<GroupEvaluationUiWrapper> fieldGroup;

    @InjectMocks
    @Spy
    FunctionSpaceEvaluationBasePresenter presenter;

    @Mock
    FunctionSpaceEvaluationBaseView view;

    @Mock
    FunctionSpacePackageEvalService functionSpacePackageEvalService;

    @Mock
    FunctionSpaceEvaluationDetailsLayout functionSpaceEvaluationDetailsLayout;

    @Mock
    UiContext uiContext;

    @Mock
    PacmanConfigParamsService pacmanConfigParamsService;

    @Mock
    GroupEvaluationAlternateDateService alternateDateService;

    @Mock
    private GroupPricingConfigurationAncillaryService groupPricingConfigurationAncillaryService;

    @Mock
    GroupPricingConfigurationService groupPricingConfigurationService;

    @Mock
    GroupEvaluationOnBooksService groupEvaluationOnBooksService;

    @Mock
    PricingConfigurationService pricingConfigurationService;

    @Mock
    PropertyService propertyService;

    @Mock
    private MultiPropertyCrudServiceBean multiPropertyCrudService;

    @Mock
    private TaxService taxService;

    @Mock
    Lang lang;

    @Mock
    DateService dateService;
    @Mock
    FunctionSpaceDemandCalendarService functionSpaceDemandCalendarService;

    @Mock
    DashboardService dashboardService;

    @Mock
    GroupEvaluationUiWrapper groupEvaluationUiWrapper;

    @Mock
    GroupEvaluationInputWrapper groupEvaluationInputWrapper;

    @Mock
    FunctionSpacePackageService functionSpacePackageService;

    @Mock
    GroupPricingPackageService groupPricingPackageService;

    @Captor
    ArgumentCaptor<ArrayList<String>> rohEvaluationTypesCaptor;

    @Captor
    ArgumentCaptor<ArrayList<String>> rohSearchByTypesCaptor;

    @Captor
    ArgumentCaptor<ArrayList<GroupEvaluationDateOfStay>> dateOfStayCaptor;

    @Mock
    SpecialEventService specialEventService;

    @Mock
    ConferenceAndBanquetService conferenceAndBanquetService;

    @Mock
    ConferenceAndBanquetEvaluationService conferenceAndBanquetEvaluationService;

    @BeforeEach
    public void setUp() throws Exception {
        presenter = createFunctionSpaceEvaluationBasePresenterStub();

        presenter.setViewForTesting(view);
        MockitoAnnotations.initMocks(this);

        List<GroupEvaluationRevision> groupEvaluationRevisions = getGroupEvaluationRevisions();
        when(groupEvaluationService.getGroupEvaluationRevisions(GROUP_EVALUATION_ID_10)).thenReturn(groupEvaluationRevisions);
    }

    private FunctionSpaceEvaluationBasePresenter createFunctionSpaceEvaluationBasePresenterStub() {

        return new FunctionSpaceEvaluationBasePresenter() {
            @Override
            public TetrisBeanFieldGroup<GroupEvaluationUiWrapper> getFieldGroup() {
                return fieldGroup;
            }

            @Override
            public void populateGroupEvaluationParameters() {
            }

            @Override
            public void generateEvaluation() {
            }

            @Override
            public void saveResults(Object resultGroupEvaluation) {
            }

            @Override
            public boolean isReevaluate() {
                return false;
            }

            @Override
            protected void loadMarketSegments() {
            }

            @Override
            protected List<OccupancyDateCapacity> getGroupEvaluationCapacitiesForDate(LocalDate startDate, LocalDate endDate) {
                return null;
            }

            @Override
            protected Map<Integer, List<GroupPricingConfigurationAncillaryAssignment>> getConfiguredAncillaryAssignments(String marketSegmentCode, LocalDate preferredDate) {
                return null;
            }

            @Override
            public GroupEvaluationArrivalDate makeCopyForEdit(GroupEvaluationArrivalDate arrivalDate) {
                return null;
            }

            @Override
            public Map<String, String[]> getUrlParameters() {
                return new HashMap<>();
            }
        };
    }

    private List<GroupEvaluationRevision> getGroupEvaluationRevisions() {
        List<GroupEvaluationRevision> groupEvaluationRevisions = new ArrayList<>();

        GroupEvaluationRevision groupEvaluationRevision1 = new GroupEvaluationRevision();
        groupEvaluationRevision1.setRevisionId(200);
        groupEvaluationRevisions.add(groupEvaluationRevision1);

        return groupEvaluationRevisions;
    }

    @Test
    public void init() throws Exception {
        FunctionSpaceEvaluationBasePresenter presenterSpy = Mockito.spy(presenter);
        Mockito.doNothing().when(presenterSpy).initParametersData();
        presenterSpy.init(new GroupEvaluationInputWrapper(false, new GroupEvaluation(), true));
        Mockito.verify(pacmanConfigParamsService).getBooleanParameterValue(FeatureTogglesConfigParamName.UI_THREADING_GROUP_EVALUATION.value());
    }

    @Test
    public void testOverMaxTotalRoomsException() {
        try {
            presenter.onNumberOfRoomsValueChange("100000000000000000000");
        } catch (NumberFormatException e) {
            fail("The NumberFormatException should have been caught by the code in the presenter.");
        }
    }

    @Test
    public void testOnViewOpenedRevisionLoadingMultiAndSingle() {

        GroupEvaluation groupEvaluation = new GroupEvaluation();
        groupEvaluation.setId(GROUP_EVALUATION_ID_10);
        groupEvaluation.setGroupEvaluationMultiId(MULTI_GROUP_EVALUATION_ID_15);
        groupEvaluation.setCreatedByUserId(USER_ID_TEST);
        groupEvaluation.setEvaluationMethod(GroupPricingEvaluationMethod.ROH);

        GroupEvaluationInputWrapper groupEvaluationInputWrapper = new GroupEvaluationInputWrapper(READ_ONLY_TRUE, groupEvaluation, IS_GROUP_PRICING_TRUE);

        FunctionSpaceEvaluationBasePresenter presenterSpy = Mockito.spy(presenter);

        Mockito.doNothing().when(presenterSpy).initParametersData();
        when(presenterSpy.isReevaluate()).thenReturn(Boolean.TRUE);

        presenterSpy.onViewOpened(groupEvaluationInputWrapper);

        List<GroupEvaluationRevisionWrapper> groupEvaluationRevisionWrappers = presenterSpy.getGroupEvaluationRevisions();

        assertNotNull(groupEvaluationRevisionWrappers, "groupEvaluationRevisionWrappers should not be null");
        Assertions.assertTrue(groupEvaluationRevisionWrappers.isEmpty(), "groupEvaluationRevisionWrappers should be empty");

        // set the groupEvaluationMultiId to null so it is not part of a multi-property group evaluation
        groupEvaluation.setGroupEvaluationMultiId(null);

        // Try the initialization with the GroupEvaluation not part of a multi-property GroupEvaluation
        presenterSpy.onViewOpened(groupEvaluationInputWrapper);

        groupEvaluationRevisionWrappers = presenterSpy.getGroupEvaluationRevisions();
        assertNotNull(groupEvaluationRevisionWrappers, "groupEvaluationRevisionWrappers should not be null");
        Assertions.assertEquals(1, groupEvaluationRevisionWrappers.size(), "groupEvaluationRevisionWrappers is wrong size");
    }


    @Test
    public void getIndivisibleRooms() throws Exception {
        when(groupEvaluationService.getGroupEvaluationFunctionSpaceRooms()).thenReturn(getGroupEvaluationFunctionSpaceFunctionRoom());
        List<GroupEvaluationFunctionSpaceFunctionRoom> indivisibleRooms = presenter.getIndivisibleRooms();
        GroupEvaluationFunctionSpaceFunctionRoom groupEvaluationFunctionSpaceFunctionRoom = indivisibleRooms.get(0);
        Assertions.assertEquals(TEST_FUNCTIN_ROOM, groupEvaluationFunctionSpaceFunctionRoom.getFunctionSpaceFunctionRoom().getName());
    }

    private List<FunctionSpaceFunctionRoom> getGroupEvaluationFunctionSpaceFunctionRoom() {
        FunctionSpaceFunctionRoom functionSpaceFunctionRoom = new FunctionSpaceFunctionRoom();
        functionSpaceFunctionRoom.setName(TEST_FUNCTIN_ROOM);
        return Arrays.asList(functionSpaceFunctionRoom);
    }

    @Test
    public void onNumberOfRoomsValueChange() {
        presenter.dayOfStays = FunctionSpaceEvaluationTestData.getDateOfStay();
        presenter.onNumberOfRoomsValueChange("30");
        for (GroupEvaluationDateOfStay dayOfStay : (List<GroupEvaluationDateOfStay>) presenter.dayOfStays) {
            Assertions.assertEquals(30, dayOfStay.getNumberOfRooms().intValue());
        }
    }

    @Test
    public void onNumberOfNightsValueChange() {
        presenter.parametersGroupEvaluationWrapper = FunctionSpaceEvaluationTestData.getParameterGroupEvaluationWrapper();
        presenter.arrivalRangeEndDate = new LocalDate();
        presenter.onNumberOfNightsValueChange("5");
        Assertions.assertEquals(5, presenter.parametersGroupEvaluationWrapper.getNumberOfNights().intValue());
    }

    @Test
    public void getTotalGuestRoomCount_ROH() throws Exception {
        presenter.parametersGroupEvaluationWrapper = FunctionSpaceEvaluationTestData.getParameterGroupEvaluationWrapper();
        when(view.getDetailsLayout()).thenReturn(functionSpaceEvaluationDetailsLayout);
        when(functionSpaceEvaluationDetailsLayout.getDateOfStays()).thenReturn(FunctionSpaceEvaluationTestData.getDateOfStay());

        Assertions.assertEquals(25, presenter.getTotalGuestRoomCount());
    }

    @Test
    public void getTotalGuestRoomCount_RC() throws Exception {
        presenter.parametersGroupEvaluationWrapper = FunctionSpaceEvaluationTestData.getParameterGroupEvaluationWrapper();
        presenter.parametersGroupEvaluationWrapper.setSelectedMethodType(GroupPricingEvaluationMethod.RC);
        when(view.getDetailsLayout()).thenReturn(functionSpaceEvaluationDetailsLayout);
        when(functionSpaceEvaluationDetailsLayout.getDateOfStayByRoomType()).thenReturn(getDateOfStayByRoomType());
        Assertions.assertEquals(10, presenter.getTotalGuestRoomCount());
    }

    private List<GroupEvaluationRoomType> getDateOfStayByRoomType() {
        GroupEvaluationRoomType groupEvaluationRoomType = new GroupEvaluationRoomType();
        groupEvaluationRoomType.setGroupEvaluationRoomTypeDayOfStays(getGroupEvalutionRoomTypeDayOfStays());
        return Arrays.asList(groupEvaluationRoomType);
    }

    private List<GroupEvaluationRoomTypeDayOfStay> getGroupEvalutionRoomTypeDayOfStays() {
        GroupEvaluationRoomTypeDayOfStay groupEvaluationRoomTypeDayOfStay = new GroupEvaluationRoomTypeDayOfStay();
        groupEvaluationRoomTypeDayOfStay.setNumberOfRooms(10);
        return Arrays.asList(groupEvaluationRoomTypeDayOfStay);
    }

    @Test
    public void isROHEvaluation() throws Exception {
        GroupEvaluation groupEvaluation = new GroupEvaluation();
        groupEvaluation.setEvaluationMethod(GroupPricingEvaluationMethod.ROH);

        GroupEvaluationUiWrapper parameterGroupEvaluationWrapper = FunctionSpaceEvaluationTestData.getParameterGroupEvaluationWrapper();
        parameterGroupEvaluationWrapper.setGroupEvaluation(groupEvaluation);
        presenter.setParametersGroupEvaluationWrapper(parameterGroupEvaluationWrapper);

        assertTrue(presenter.isROHEvaluation());
    }

    @Test
    public void isAllowTbaConfiguredFunctionRooms() {
        GroupEvaluationInputWrapper parameterGroupEvaluationWrapper = FunctionSpaceEvaluationTestData.getGroupEvaluationInputWrapper();
        parameterGroupEvaluationWrapper.getGroupEvaluation().setFromSalesAndCatering(true);
        presenter.setGroupEvaluationInputWrapper(parameterGroupEvaluationWrapper);
        FunctionSpaceUiWrapper wrapper = new FunctionSpaceUiWrapper(getGroupEvlFunctionSpaceWithTbaRoom());
        assertTrue(presenter.isFunctionRoomConfiguredAsTBA(Arrays.asList(wrapper)));
    }

    private GroupEvaluationFunctionSpace getGroupEvlFunctionSpaceWithTbaRoom() {
        GroupEvaluationFunctionSpace functionSpace = new GroupEvaluationFunctionSpace();
        GroupEvaluationFunctionSpaceFunctionRoom gpFunSpaceFunRoom = new GroupEvaluationFunctionSpaceFunctionRoom();
        gpFunSpaceFunRoom.setGroupEvaluationFunctionSpace(functionSpace);
        FunctionSpaceFunctionRoom room = new FunctionSpaceFunctionRoom();
        room.setTba(true);
        gpFunSpaceFunRoom.setFunctionSpaceFunctionRoom(room);
        functionSpace.addGroupEvaluationFunctionSpaceFunctionRoom(gpFunSpaceFunRoom);
        return functionSpace;
    }

    @Test
    public void addMultiPropertyInformation() throws Exception {
        GroupEvaluationMulti groupEvaluationMulti = new GroupEvaluationMulti();
        List<GroupEvaluation> groupEvaluations = getGroupEvaluationList();
        groupEvaluationMulti.setGroupEvaluations(groupEvaluations);
        presenter.parametersGroupEvaluationWrapper = FunctionSpaceEvaluationTestData.getParameterGroupEvaluationWrapper();

        presenter.addMultiPropertyInformation(presenter.parametersGroupEvaluationWrapper, groupEvaluationMulti);

        Assertions.assertEquals(groupEvaluations.get(0).getGroupEvaluationAncillaries().size(),
                presenter.parametersGroupEvaluationWrapper.getMultiPropertyGroupAncillary().get(GroupEvaluationUiWrapper.SINGLE_PROPERTY).size());
        Assertions.assertEquals(groupEvaluations.get(0).getGroupEvaluationConferenceAndBanquets().size(),
                presenter.parametersGroupEvaluationWrapper.getMultiPropertyGroupConfAndBanq().get(GroupEvaluationUiWrapper.SINGLE_PROPERTY).size());
    }

    private List<GroupEvaluation> getGroupEvaluationList() {
        GroupEvaluation groupEvaluation = getGroupEvaluation("TestGroupName", 1, 5);
        return Arrays.asList(groupEvaluation);
    }

    private GroupEvaluation getGroupEvaluation(String testGroupName, int id, int propertyId) {
        MarketSegmentSummary marketSegmentSummary = new MarketSegmentSummary();
        marketSegmentSummary.setCode(TEST_MARKETSEGMENT_CODE);
        GroupEvaluation groupEvaluation = new GroupEvaluation();
        groupEvaluation.setId(id);
        groupEvaluation.setPropertyId(propertyId);
        groupEvaluation.setGroupName(testGroupName);
        groupEvaluation.setMarketSegment(marketSegmentSummary);
        groupEvaluation.setGroupEvaluationAncillaries(new HashSet<>());
        groupEvaluation.setGroupEvaluationArrivalDates(getArrivalDates());
        groupEvaluation.setGroupEvaluationConferenceAndBanquets(new HashSet<>());
        return groupEvaluation;
    }

    private Set<GroupEvaluationArrivalDate> getArrivalDates() {
        Set<GroupEvaluationArrivalDate> arrivalDates = new LinkedHashSet<>();
        GroupEvaluationArrivalDate date1 = new GroupEvaluationArrivalDate();
        GroupEvaluationArrivalDate date2 = new GroupEvaluationArrivalDate();
        arrivalDates.add(date1);
        arrivalDates.add(date2);
        return arrivalDates;
    }
    private List<Property> getPropertyList() {
        Property property1 = new Property();
        property1.setId(5);
        Property property2 = new Property();
        property2.setId(6);
        return Arrays.asList(property1, property2);
    }

    @Test
    public void updateAncillaryView_isPersisted() throws Exception {
        FunctionSpaceEvaluationBasePresenter spy = Mockito.spy(presenter);
        GroupEvaluationUiWrapper wrapperNew = Mockito.mock(GroupEvaluationUiWrapper.class);

        GroupEvaluation groupEvaluation = Mockito.mock(GroupEvaluation.class);
        when(wrapperNew.getGroupEvaluation()).thenReturn(groupEvaluation);
        when(wrapperNew.isPersisted()).thenReturn(true);
        FunctionSpaceEvaluationDetailsLayout functionSpaceEvaluationDetailsLayout = Mockito.mock(FunctionSpaceEvaluationDetailsLayout.class);
        when(view.getDetailsLayout()).thenReturn(functionSpaceEvaluationDetailsLayout);

        presenter.parametersGroupEvaluationWrapper = wrapperNew;
        presenter.updateAncillaryView();
        Mockito.verify(functionSpaceEvaluationDetailsLayout).updateAncillaryView(wrapperNew);
    }

    @Test
    public void updateConferenceAndBanquetView() throws Exception {
        FunctionSpaceEvaluationDetailsLayout functionSpaceEvaluationDetailsLayout = Mockito.mock(FunctionSpaceEvaluationDetailsLayout.class);
        when(view.getDetailsLayout()).thenReturn(functionSpaceEvaluationDetailsLayout);

        FunctionSpaceEvaluationBasePresenter spy = Mockito.spy(presenter);
        Mockito.doReturn(true).when(spy).isGroupPricing();
        spy.updateConferenceAndBanquetView();
        Mockito.verify(functionSpaceEvaluationDetailsLayout).updateConferenceAndBanquetView(Mockito.any());
    }

    @Test
    public void updateAncillaryView_notPersisted() throws Exception {
        GroupEvaluationUiWrapper wrapperNew = Mockito.mock(GroupEvaluationUiWrapper.class);
        GroupEvaluation groupEvaluation = Mockito.mock(GroupEvaluation.class);
        when(wrapperNew.getGroupEvaluation()).thenReturn(groupEvaluation);
        when(wrapperNew.isPersisted()).thenReturn(false);
        when(wrapperNew.getMarketSegment()).thenReturn("MKT");

        FunctionSpaceEvaluationDetailsLayout functionSpaceEvaluationDetailsLayout = Mockito.mock(FunctionSpaceEvaluationDetailsLayout.class);
        when(view.getDetailsLayout()).thenReturn(functionSpaceEvaluationDetailsLayout);

        MultiDateSelections multiDateSelections = Mockito.mock(MultiDateSelections.class);
        when(wrapperNew.getMultiDateSelections()).thenReturn(multiDateSelections);
        when(multiDateSelections.getPreferredDate()).thenReturn(new LocalDate());

        FunctionSpaceEvaluationBasePresenter spy = Mockito.spy(presenter);
        when(spy.getConfiguredAncillaryAssignments(Mockito.any(), Mockito.any())).thenReturn(getAncillaryAssignments());
        spy.parametersGroupEvaluationWrapper = wrapperNew;

        spy.updateAncillaryView();
        Mockito.verify(functionSpaceEvaluationDetailsLayout).updateAncillaryView(wrapperNew);
    }

    public Map<Integer, List<GroupPricingConfigurationAncillaryAssignment>> getAncillaryAssignments() {
        GroupPricingConfigurationAncillaryAssignment ancillaryAssignment = new GroupPricingConfigurationAncillaryAssignment();
        GroupPricingConfigurationAncillaryStream ancillaryStream = new GroupPricingConfigurationAncillaryStream();
        ancillaryStream.setProfitPercentage(BigDecimal.ONE);
        ancillaryAssignment.setGroupPricingConfigurationAncillaryStream(ancillaryStream);
        Map<Integer, List<GroupPricingConfigurationAncillaryAssignment>> test = new HashMap<>();
        test.put(5, Arrays.asList(ancillaryAssignment));
        return test;
    }

    @Test
    public void getGroupPricingConfigurationAncillaries() throws Exception {
        presenter.getGroupPricingConfigurationAncillaries(null);
        Mockito.verify(groupPricingConfigurationAncillaryService).getGroupPricingConfigurationAncillaries();
    }

    @Test
    public void hasPreferredDateAndNumberOfNights() {
        GroupEvaluationUiWrapper wrapperNew = Mockito.mock(GroupEvaluationUiWrapper.class);

        MultiDateSelections multiDateSelections = Mockito.mock(MultiDateSelections.class);
        when(wrapperNew.getMultiDateSelections()).thenReturn(multiDateSelections);
        when(multiDateSelections.getPreferredDate()).thenReturn(new LocalDate());
        when(wrapperNew.getNumberOfNights()).thenReturn(5);

        presenter.parametersGroupEvaluationWrapper = wrapperNew;

        Assertions.assertEquals(true, presenter.hasPreferredDateAndNumberOfNights());
    }

    @Test
    public void getEvaluationEndDate() {
        GroupEvaluationUiWrapper wrapperNew = Mockito.mock(GroupEvaluationUiWrapper.class);

        MultiDateSelections multiDateSelections = Mockito.mock(MultiDateSelections.class);
        when(wrapperNew.getMultiDateSelections()).thenReturn(multiDateSelections);
        LocalDate localDate = new LocalDate();
        when(multiDateSelections.getPreferredDate()).thenReturn(localDate);
        when(wrapperNew.getNumberOfNights()).thenReturn(5);

        presenter.parametersGroupEvaluationWrapper = wrapperNew;
        localDate = localDate.plusDays(5);
        LocalDate result = new LocalDate(presenter.getEvaluationEndDate());
        Assertions.assertEquals(localDate.compareTo(result), 0);
    }

    @Test
    public void getEvaluationStartDate() {
        GroupEvaluationUiWrapper wrapperNew = Mockito.mock(GroupEvaluationUiWrapper.class);

        MultiDateSelections multiDateSelections = Mockito.mock(MultiDateSelections.class);
        when(wrapperNew.getMultiDateSelections()).thenReturn(multiDateSelections);
        LocalDate localDate = new LocalDate();
        when(multiDateSelections.getPreferredDate()).thenReturn(localDate);
        when(wrapperNew.getNumberOfNights()).thenReturn(5);

        presenter.parametersGroupEvaluationWrapper = wrapperNew;

        LocalDate result = new LocalDate(presenter.getEvaluationStartDate());

        presenter.getEvaluationStartDate();
        Assertions.assertEquals(localDate.compareTo(result), 0);
    }

    @Test
    public void getPermissionKey_isGroupPricing() {
        FunctionSpaceEvaluationBasePresenter spy = Mockito.spy(presenter);
        Mockito.doReturn(false).when(spy).isFunctionSpaceGroupView();
        Mockito.doReturn(false).when(spy).isFunctionSpace();
        Assertions.assertEquals(TetrisPermissionKey.GROUP_PRICING_EVALUATION, spy.getPermissionKey());
    }

    @Test
    public void getPermissionKey_isFunctionSpace() {
        FunctionSpaceEvaluationBasePresenter spy = Mockito.spy(presenter);
        Mockito.doReturn(false).when(spy).isFunctionSpaceGroupView();
        Mockito.doReturn(true).when(spy).isFunctionSpace();
        Assertions.assertEquals(TetrisPermissionKey.FUNCTION_SPACE_EVALUATION, spy.getPermissionKey());
    }

    @Test
    public void getPermissionKey_isFunctionSpaceGroupView() {
        FunctionSpaceEvaluationBasePresenter spy = Mockito.spy(presenter);
        Mockito.doReturn(true).when(spy).isFunctionSpaceGroupView();
        Assertions.assertEquals(TetrisPermissionKey.FUNCTION_SPACE_EVALUATION, spy.getPermissionKey());
    }

    @Test
    public void groupPricingGeneralMeasurementEnum() throws Exception {
        when(pacmanConfigParamsService.getParameterValue(GUIConfigParamName.CORE_PROPERTY_FUNCTION_SPACE_SQFEET.value())).thenReturn("true");
        Assertions.assertEquals(GroupPricingGeneralMeasurementEnum.FEET, presenter.getUnitOfMeasure());
    }


    @Test
    public void groupPricingGeneralMeasurementEnum_Meters() throws Exception {
        when(pacmanConfigParamsService.getParameterValue(GUIConfigParamName.CORE_PROPERTY_FUNCTION_SPACE_SQFEET.value())).thenReturn("false");
        Assertions.assertEquals(GroupPricingGeneralMeasurementEnum.METERS, presenter.getUnitOfMeasure());
    }

    @Test
    public void saveButtonEnabled() {
        GroupEvaluationUiWrapper wrapperNew = Mockito.mock(GroupEvaluationUiWrapper.class);

        GroupEvaluation groupEvaluation = Mockito.mock(GroupEvaluation.class);
        when(wrapperNew.getGroupEvaluation()).thenReturn(groupEvaluation);
        when(wrapperNew.isPersisted()).thenReturn(true);
        FunctionSpaceEvaluationDetailsLayout functionSpaceEvaluationDetailsLayout = Mockito.mock(FunctionSpaceEvaluationDetailsLayout.class);
        when(view.getDetailsLayout()).thenReturn(functionSpaceEvaluationDetailsLayout);//        detailsLayout.createViewElements();

        presenter.showSaveButton(true);
    }

    @Test
    public void populateROHDayOfStay_null() throws Exception {
        GroupEvaluationUiWrapper groupEvaluationUiWrapper = Mockito.mock(GroupEvaluationUiWrapper.class);
        when(groupEvaluationUiWrapper.getMultiDateSelections()).thenReturn(new MultiDateSelections());
        presenter.setParametersGroupEvaluationWrapper(groupEvaluationUiWrapper);
        FunctionSpaceEvaluationDetailsLayout detailsLayout = Mockito.mock(FunctionSpaceEvaluationDetailsLayout.class);
        when(view.getDetailsLayout()).thenReturn(detailsLayout);
        presenter.populateROHDayOfStay();
        Mockito.verify(detailsLayout).populateDateOfStays(Mockito.anyList());
    }

    @Test
    public void populateROHDayOfStayWithOnBooks() throws Exception {
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.GROUP_EVALUATION_ON_BOOKS_GROUP_ENABLED)).thenReturn(true);
        OccupancyDateCapacity occupancyDateCapacity = new OccupancyDateCapacity();
        occupancyDateCapacity.setOccupancyDate(LocalDate.now());
        occupancyDateCapacity.setAvailableCapacity(100);
        occupancyDateCapacity.setPhysicalCapacity(150);
        when(presenter.getGroupEvaluationCapacitiesForDate(any(LocalDate.class), any(LocalDate.class))).thenReturn(singletonList(occupancyDateCapacity));
        GroupEvaluationInputWrapper groupEvaluationInputWrapper = mock(GroupEvaluationInputWrapper.class);
        when(groupEvaluationInputWrapper.isGroupPricing()).thenReturn(true);
        presenter.setGroupEvaluationInputWrapper(groupEvaluationInputWrapper);
        GroupEvaluationUiWrapper groupEvaluationUiWrapper = Mockito.mock(GroupEvaluationUiWrapper.class);
        GroupEvaluationDayOfStay groupEvaluationDayOfStay = new GroupEvaluationDayOfStay();
        groupEvaluationDayOfStay.setDayOfStay(1);
        groupEvaluationDayOfStay.setNumberOfRooms(50);
        groupEvaluationDayOfStay.setCurrentRate(BigDecimalUtil.ONE_HUNDRED);
        MultiDateSelections multiDateSelections = new MultiDateSelections();
        multiDateSelections.setPreferredDate(LocalDate.now());
        multiDateSelections.setDateSelections(singletonList(LocalDate.now()));
        when(groupEvaluationUiWrapper.getGroupEvaluationDayOfStays()).thenReturn(singleton(groupEvaluationDayOfStay));
        when(groupEvaluationUiWrapper.getNumberOfNights()).thenReturn(1);
        when(groupEvaluationUiWrapper.getOnBooksDetailsList()).thenReturn(emptyList());
        when(groupEvaluationUiWrapper.getMultiDateSelections()).thenReturn(multiDateSelections);
        presenter.setParametersGroupEvaluationWrapper(groupEvaluationUiWrapper);
        FunctionSpaceEvaluationDetailsLayout detailsLayout = Mockito.mock(FunctionSpaceEvaluationDetailsLayout.class);
        when(view.getDetailsLayout()).thenReturn(detailsLayout);

        presenter.populateROHDayOfStay();

        verify(detailsLayout).populateDateOfStays(dateOfStayCaptor.capture());
        List<GroupEvaluationDateOfStay> dateOfStays = dateOfStayCaptor.getValue();
        assertEquals(1, dateOfStays.size());
        assertEquals(BigDecimalUtil.ONE_HUNDRED, dateOfStays.get(0).getCurrentRate());
    }

    @Test
    public void populateROHDayOfStay() throws Exception {
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.GROUP_EVALUATION_ON_BOOKS_GROUP_ENABLED)).thenReturn(true);
        OccupancyDateCapacity occupancyDateCapacity = new OccupancyDateCapacity();
        occupancyDateCapacity.setOccupancyDate(LocalDate.now());
        occupancyDateCapacity.setAvailableCapacity(100);
        occupancyDateCapacity.setPhysicalCapacity(150);
        when(presenter.getGroupEvaluationCapacitiesForDate(any(LocalDate.class), any(LocalDate.class))).thenReturn(singletonList(occupancyDateCapacity));
        GroupEvaluationInputWrapper groupEvaluationInputWrapper = mock(GroupEvaluationInputWrapper.class);
        when(groupEvaluationInputWrapper.isGroupPricing()).thenReturn(true);
        presenter.setGroupEvaluationInputWrapper(groupEvaluationInputWrapper);
        GroupEvaluationUiWrapper groupEvaluationUiWrapper = Mockito.mock(GroupEvaluationUiWrapper.class);
        GroupEvaluationDayOfStay groupEvaluationDayOfStay = new GroupEvaluationDayOfStay();
        groupEvaluationDayOfStay.setDayOfStay(1);
        groupEvaluationDayOfStay.setNumberOfRooms(50);
        MultiDateSelections multiDateSelections = new MultiDateSelections();
        multiDateSelections.setPreferredDate(LocalDate.now());
        multiDateSelections.setDateSelections(singletonList(LocalDate.now()));
        when(groupEvaluationUiWrapper.getGroupEvaluationDayOfStays()).thenReturn(singleton(groupEvaluationDayOfStay));
        when(groupEvaluationUiWrapper.getNumberOfNights()).thenReturn(1);
        when(groupEvaluationUiWrapper.getOnBooksDetailsList()).thenReturn(emptyList());
        when(groupEvaluationUiWrapper.getMultiDateSelections()).thenReturn(multiDateSelections);
        presenter.setParametersGroupEvaluationWrapper(groupEvaluationUiWrapper);
        FunctionSpaceEvaluationDetailsLayout detailsLayout = Mockito.mock(FunctionSpaceEvaluationDetailsLayout.class);
        when(view.getDetailsLayout()).thenReturn(detailsLayout);

        presenter.populateROHDayOfStay();

        verify(detailsLayout).populateDateOfStays(dateOfStayCaptor.capture());
        List<GroupEvaluationDateOfStay> dateOfStays = dateOfStayCaptor.getValue();
        assertEquals(1, dateOfStays.size());
        assertNull(dateOfStays.get(0).getCurrentRate());
    }

    @Test
    public void populateROHEvaluationTypes() {
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.GROUP_EVALUATION_RESULTS_BY_OCCUPANCY_DATE)).thenReturn(true);
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.GROUP_EVALUATION_PREVIOUS_STAY_RATE_ENABLED)).thenReturn(true);
        when(view.getDetailsLayout()).thenReturn(functionSpaceEvaluationDetailsLayout);

        presenter.populateROHEvaluationTypes();

        verify(functionSpaceEvaluationDetailsLayout).populateROHEvaluationTypes(rohEvaluationTypesCaptor.capture());
        List<String> populatedROHEvaluationTypes = rohEvaluationTypesCaptor.getValue();
        assertEquals(2, populatedROHEvaluationTypes.size());
        assertTrue(populatedROHEvaluationTypes.contains(ROHGroupEvaluationType.NEW_EVALUATION.getCaption()));
        assertTrue(populatedROHEvaluationTypes.contains(ROHGroupEvaluationType.PREVIOUS_STAY.getCaption()));
    }

    @Test
    public void populateAccomTypeDayOfStayTest() {
        LocalDate dt = new LocalDate();
        List<LocalDate> dtList = new ArrayList<>();
        dtList.add(dt);
        MultiDateSelections mds = new MultiDateSelections();
        mds.setDateSelections(dtList);
        mds.setPreferredDate(new LocalDate());
        GroupEvaluationUiWrapper parametersGroupEvaluationWrapper = Mockito.mock(GroupEvaluationUiWrapper.class);
        presenter.setParametersGroupEvaluationWrapper(parametersGroupEvaluationWrapper);
        when(parametersGroupEvaluationWrapper.getMultiDateSelections()).thenReturn(mds);
        when(parametersGroupEvaluationWrapper.getNumberOfNights()).thenReturn(1);
        FunctionSpaceEvaluationDetailsLayout detailsLayout = Mockito.mock(FunctionSpaceEvaluationDetailsLayout.class);
        when(view.getDetailsLayout()).thenReturn(detailsLayout);

        presenter.populateAccomTypeDayOfStay(new ArrayList<>());
        Mockito.verify(groupEvaluationService).getRoomTypeCapacitiesForDateRangeDetails(new ArrayList<>(), dt, dt);

        presenter.setComponentRoomsEnabled(true);
        presenter.populateAccomTypeDayOfStay(new ArrayList<>());
        Mockito.verify(groupEvaluationService, times(2)).getRoomTypeCapacitiesForDateRangeDetails(new ArrayList<>(), dt, dt);
    }

    @Test
    public void getGroupEvaluationArrivalDateGuestRoomRates() {
        GroupEvaluationArrivalDate groupEvaluationArrivalDate = new GroupEvaluationArrivalDate();
        GroupEvaluationArrivalDateGuestRoomRates guestRoomRates = getGroupEvaluationArrivalDateGuestRoomRates(groupEvaluationArrivalDate);
        assertEquals(singletonList(guestRoomRates), presenter.getArrivalDateGuestRoomRates(groupEvaluationArrivalDate));
    }

    @Test
    public void getGroupEvaluationArrivalDateGuestRoomRates_emptyList() {
        GroupEvaluationArrivalDate groupEvaluationArrivalDate = new GroupEvaluationArrivalDate();
        List<GroupEvaluationArrivalDateGuestRoomRates> guestRoomRatesList = presenter.getArrivalDateGuestRoomRates(groupEvaluationArrivalDate);
        assertNotNull(guestRoomRatesList);
        assertEquals(0, guestRoomRatesList.size());
    }

    @Test
    public void onPreferredDateChangedPersisted() throws Exception {
        when(groupEvaluationUiWrapper.isPersisted()).thenReturn(true);
        when(lang.getText("groupEvaluation.warning.ancillarystreamreset.preferredDate")).thenReturn("this");

        FunctionSpaceEvaluationBasePresenter spy = Mockito.spy(presenter);
        spy.formBindingComplete = true;
        spy.onPreferredDateChanged();
        Mockito.verify(spy).updateAncillaryView();
        Mockito.verify(spy).showWarning(Mockito.anyString());
    }

    @Test
    public void onPreferredDateChangedNotPersisted() throws Exception {
        GroupEvaluationUiWrapper wrapper = Mockito.mock(GroupEvaluationUiWrapper.class);
        when(groupEvaluationUiWrapper.isPersisted()).thenReturn(false);
//        presenter.setParametersGroupEvaluationWrapper(wrapper);

        FunctionSpaceEvaluationBasePresenter spy = Mockito.spy(presenter);
        Mockito.doNothing().when(spy).updateAncillaryView();
        spy.formBindingComplete = true;
        spy.onPreferredDateChanged();
        Mockito.verify(spy).updateAncillaryView();
        Mockito.verify(spy, never()).showWarning(Mockito.anyString());
    }

    @Test
    public void onMarketSegmentChangedPersisted() throws Exception {
        when(groupEvaluationUiWrapper.isPersisted()).thenReturn(true);
        when(lang.getText("groupEvaluation.warning.ancillarystreamreset")).thenReturn("this");

        FunctionSpaceEvaluationBasePresenter spy = Mockito.spy(presenter);
        Mockito.doNothing().when(spy).updateAncillaryView();
        Mockito.doNothing().when(spy).showWarning(Mockito.anyString());
        spy.formBindingComplete = true;
        spy.onMarketSegmentChange();
        Mockito.verify(spy).updateAncillaryView();
        Mockito.verify(spy).showWarning(Mockito.anyString());
    }

    @Test
    public void onMarketSegmentChangedNotPersisted() throws Exception {
        GroupEvaluationUiWrapper wrapper = Mockito.mock(GroupEvaluationUiWrapper.class);
        when(wrapper.isPersisted()).thenReturn(false);
        presenter.setParametersGroupEvaluationWrapper(wrapper);

        FunctionSpaceEvaluationBasePresenter spy = Mockito.spy(presenter);
        Mockito.doNothing().when(spy).updateAncillaryView();
        Mockito.doNothing().when(spy).showWarning(Mockito.anyString());
        spy.formBindingComplete = true;
        spy.onMarketSegmentChange();
        Mockito.verify(spy).updateAncillaryView();
        Mockito.verify(spy, never()).showWarning(Mockito.anyString());
    }

    @Test
    public void generateFlexibleDates() throws Exception {
        List<AlternateDateDto> alternateDateDtos = Arrays.asList(new AlternateDateDto(new LocalDate(), BigDecimal.ONE), new AlternateDateDto(new LocalDate().plusDays(1), BigDecimal.TEN));
        when(alternateDateService.getROHAlternateDates(Mockito.anyList(), Mockito.anyList(), Mockito.anyList()))
                .thenReturn(alternateDateDtos);
        List<LocalDate> dates = Arrays.asList(new LocalDate().plusDays(1), new LocalDate().plusDays(2));
        List<GroupEvaluationDayOfStay> dayOfStay = new ArrayList<>();
        List<Integer> propertyIds = new ArrayList<>();
        List<AlternateDateUiWrapper> list = presenter.generateROHAlternateDates(dates, dayOfStay, propertyIds);
        assertEquals(alternateDateDtos.size(), list.size());
    }

    @Test
    public void setHasUserAdjustedChanges() throws Exception {
        presenter.setHasUserAdjustedChanges(new GroupEvaluation());
        Mockito.verify(view).enableResultSaveButton();

    }

    @Test
    public void generateFlexibleDatesForRoomTypeEvaluation() throws Exception {
        List<AlternateDateDto> alternateDateDtos = Arrays.asList(new AlternateDateDto(new LocalDate(), BigDecimal.ONE), new AlternateDateDto(new LocalDate().plusDays(1), BigDecimal.TEN));
        when(alternateDateService.getRCAlternateDates(Mockito.anyList(), Mockito.anyMap())).thenReturn(alternateDateDtos);
        List<LocalDate> dates = Arrays.asList(new LocalDate().plusDays(1), new LocalDate().plusDays(2));

        List<AlternateDateDto> list = presenter.generateRoomTypeAlternateDates(dates, new HashMap<>());
        assertEquals(alternateDateDtos.size(), list.size());
    }

    @Test
    public void isRevisionView() throws Exception {
        Assertions.assertEquals(false, presenter.isRevisionView());
    }

    @Test
    public void isRevisionView_true() throws Exception {
        GroupEvaluationRevisionWrapper groupEvaluationRevisionWrapper = new GroupEvaluationRevisionWrapper() {
            @Override
            public GroupEvaluationRevision getGroupEvaluationRevision() {
                return new GroupEvaluationRevision() {
                    @Override
                    public Integer getRevisionId() {
                        return 200;
                    }
                };
            }
        };

        FunctionSpaceEvaluationDetailsLayout functionSpaceEvaluationDetailsLayout = Mockito.mock(FunctionSpaceEvaluationDetailsLayout.class);
        when(view.getDetailsLayout()).thenReturn(functionSpaceEvaluationDetailsLayout);
        FunctionSpaceEvaluationBasePresenter spy = Mockito.spy(presenter);
        Mockito.doReturn(true).when(spy).isGroupPricing();
        Mockito.doNothing().when(spy).init(Mockito.any(GroupEvaluationInputWrapper.class), Mockito.anyBoolean());
        spy.selectRevision(groupEvaluationRevisionWrapper);
        Assertions.assertEquals(true, spy.isRevisionView());
    }

    @Test
    public void getInactiveAccomTypes() throws Exception {
        when(groupPricingConfigurationService.getAccomTypesConfiguredForRoomClassEvaluations()).thenReturn(getGroupPricingAccomTypes());
        List<AccomType> inactiveAccomTypes = presenter.getInactiveAccomTypes(getGroupPricingAccomTypes());
        Assertions.assertEquals(0, inactiveAccomTypes.size());
    }

    @Test
    public void getInactiveAccomTypes_someInactive() throws Exception {
        when(groupPricingConfigurationService.getAccomTypesConfiguredForRoomClassEvaluations()).thenReturn(Arrays.asList(getAccomType("AccomType1", 1)));
        List<AccomType> inactiveAccomTypes = presenter.getInactiveAccomTypes(getGroupPricingAccomTypes());
        Assertions.assertEquals(1, inactiveAccomTypes.size());
    }

    private List<AccomType> getGroupPricingAccomTypes() {
        return Arrays.asList(getAccomType("AccomType1", 1), getAccomType("AccomType2", 2));
    }

    private AccomType getAccomType(String name, int id) {
        AccomType accomType = new AccomType();
        accomType.setName(name);
        accomType.setId(id);
        return accomType;
    }

    @Test
    public void getGroupEvaluationInput_isRoomTypeEvaluation() throws Exception {
        GroupEvaluationUiWrapper parameterGroupEvaluationWrapper = FunctionSpaceEvaluationTestData.getParameterGroupEvaluationWrapper();
        parameterGroupEvaluationWrapper.setSelectedMethodType(GroupPricingEvaluationMethod.RC);
        HashSet<GroupEvaluationType> groupEvaluationTypes = new HashSet<>();
        groupEvaluationTypes.add(GroupEvaluationType.GUEST_ROOM_AND_FUNCTION_SPACE);
        parameterGroupEvaluationWrapper.setSelectedEvaluationTypes(groupEvaluationTypes);

        FunctionSpaceEvaluationDetailsLayout detailsLayout = Mockito.mock(FunctionSpaceEvaluationDetailsLayout.class);
        when(view.getDetailsLayout()).thenReturn(detailsLayout);
        when(detailsLayout.getDateOfStays()).thenReturn(getDateOfStays(false));
        when(detailsLayout.getGroupEvaluationAncillaries()).thenReturn(getAncillaryByProperty());

        when(detailsLayout.getFunctionRooms()).thenReturn(getFunctionRooms());
        when(detailsLayout.getFunctionSpaceConfAndBanquets()).thenReturn(Arrays.asList(new GroupEvaluationFunctionSpaceConfAndBanq()));

        FunctionSpaceEvaluationBasePresenter spy = Mockito.spy(presenter);
        Mockito.doReturn(true).when(spy).isFunctionSpace();
        Mockito.doReturn(false).when(spy).isGroupPricing();
        spy.setParametersGroupEvaluationWrapper(parameterGroupEvaluationWrapper);
        GroupEvaluation groupEvaluation = spy.getGroupEvaluationInput();
        Assertions.assertEquals("TEST", groupEvaluation.getGroupName());
        Assertions.assertEquals(GroupEvaluationType.GUEST_ROOM_ONLY, groupEvaluation.getEvaluationType());
        Assertions.assertEquals(1, groupEvaluation.getGroupEvaluationAncillaries().size());
        Assertions.assertEquals(1, groupEvaluation.getGroupEvaluationFunctionSpaces().size());
    }

    @Test
    public void getGroupEvaluationInput() throws Exception {
        GroupEvaluationUiWrapper parameterGroupEvaluationWrapper = FunctionSpaceEvaluationTestData.getParameterGroupEvaluationWrapper();
        FunctionSpaceEvaluationDetailsLayout detailsLayout = Mockito.mock(FunctionSpaceEvaluationDetailsLayout.class);
        when(view.getDetailsLayout()).thenReturn(detailsLayout);
        when(detailsLayout.getDateOfStays()).thenReturn(getDateOfStays(false));
        when(detailsLayout.getGroupEvaluationAncillaries()).thenReturn(getAncillaryByProperty());
        when(detailsLayout.getGroupEvaluationConferenceAndBanquets()).thenReturn(getConfAndBanqByProperty());

        FunctionSpaceEvaluationBasePresenter spy = Mockito.spy(presenter);
        Mockito.doReturn(false).when(spy).isFunctionSpace();
        Mockito.doReturn(true).when(spy).isGroupPricing();
        spy.setParametersGroupEvaluationWrapper(parameterGroupEvaluationWrapper);
        GroupEvaluation groupEvaluation = spy.getGroupEvaluationInput();
        Assertions.assertEquals("TEST", groupEvaluation.getGroupName());
        Assertions.assertEquals(GroupEvaluationType.GUEST_ROOM_ONLY, groupEvaluation.getEvaluationType());
        Assertions.assertEquals(1, groupEvaluation.getGroupEvaluationConferenceAndBanquets().size());
        Assertions.assertEquals(1, groupEvaluation.getGroupEvaluationAncillaries().size());
    }

    @Test
    public void shouldClearPreviousEvaluationResultsWhenNewEvaluationIsRequested() throws Exception {
        GroupEvaluationUiWrapper parameterGroupEvaluationWrapper = FunctionSpaceEvaluationTestData.getParameterGroupEvaluationWrapper();
        FunctionSpaceEvaluationDetailsLayout detailsLayout = Mockito.mock(FunctionSpaceEvaluationDetailsLayout.class);
        parameterGroupEvaluationWrapper.getGroupEvaluation().setGroupEvaluationGroupPricingPackageDetails(new HashSet<>(Set.of(new GroupEvaluationGroupPricingPackageDetail())));
        when(view.getDetailsLayout()).thenReturn(detailsLayout);
        when(detailsLayout.getDateOfStays()).thenReturn(getDateOfStays(false));
        when(detailsLayout.getGroupEvaluationAncillaries()).thenReturn(getAncillaryByProperty());
        when(detailsLayout.getGroupEvaluationConferenceAndBanquets()).thenReturn(getConfAndBanqByProperty());
        FunctionSpaceEvaluationBasePresenter spy = Mockito.spy(presenter);
        Mockito.doReturn(false).when(spy).isFunctionSpace();
        Mockito.doReturn(true).when(spy).isGroupPricing();
        spy.setParametersGroupEvaluationWrapper(parameterGroupEvaluationWrapper);

        GroupEvaluation groupEvaluation = spy.getGroupEvaluationInput();

        Assertions.assertEquals("TEST", groupEvaluation.getGroupName());
        Assertions.assertEquals(GroupEvaluationType.GUEST_ROOM_ONLY, groupEvaluation.getEvaluationType());
        Assertions.assertEquals(1, groupEvaluation.getGroupEvaluationConferenceAndBanquets().size());
        Assertions.assertEquals(1, groupEvaluation.getGroupEvaluationAncillaries().size());
        assertEquals(0, groupEvaluation.getGroupEvaluationGroupPricingPackageDetails().size());
    }

    @Test
    void testGetGroupEvaluationInputForFunctionSpacePackagePricing() {
        GroupEvaluationUiWrapper parameterGroupEvaluationWrapper = FunctionSpaceEvaluationTestData.getParameterGroupEvaluationWrapper();
        parameterGroupEvaluationWrapper.setSelectedMethodType(GroupPricingEvaluationMethod.ROH);
        HashSet<GroupEvaluationType> groupEvaluationTypes = new HashSet<>();
        groupEvaluationTypes.add(GroupEvaluationType.GUEST_ROOM_AND_FUNCTION_SPACE);
        parameterGroupEvaluationWrapper.setSelectedEvaluationTypes(groupEvaluationTypes);
        FunctionSpacePackageType functionSpacePackageType = buildFunctionSpacePackageType("packageType");
        FunctionSpacePackage functionSpacePackage = buildFunctionSpacePackage("packageName", functionSpacePackageType);
        functionSpacePackage.setId(1);
        functionSpacePackage.setGuestRoomIncluded(false);
        List<PackagePricingRow> packagePricingRows = buildFunctionSpacePackagePricingRows(functionSpacePackage, functionSpacePackageType);
        FunctionSpaceEvaluationDetailsLayout detailsLayout = Mockito.mock(FunctionSpaceEvaluationDetailsLayout.class);
        when(view.getDetailsLayout()).thenReturn(detailsLayout);
        when(detailsLayout.getDateOfStays()).thenReturn(getDateOfStays(false));
        when(detailsLayout.getGroupEvaluationAncillaries()).thenReturn(getAncillaryByProperty());
        when(detailsLayout.getFunctionRooms()).thenReturn(getFunctionRooms());
        when(detailsLayout.getFunctionSpaceConfAndBanquets()).thenReturn(Arrays.asList(new GroupEvaluationFunctionSpaceConfAndBanq()));
        when(detailsLayout.getPackagePricingRows()).thenReturn(packagePricingRows);
        when(functionSpacePackageService.getFunctionSpacePackageDetails(anySet())).thenReturn(Map.of(1, functionSpacePackage));
        FunctionSpaceEvaluationBasePresenter spy = Mockito.spy(presenter);
        Mockito.doReturn(true).when(spy).isFunctionSpace();
        Mockito.doReturn(false).when(spy).isGroupPricing();
        Mockito.doReturn(true).when(spy).isPackageEnabled();
        spy.setParametersGroupEvaluationWrapper(parameterGroupEvaluationWrapper);

        GroupEvaluation groupEvaluation = spy.getGroupEvaluationInput();

        assertEquals(1, groupEvaluation.getFunctionSpaceEvalPackagePricings().size());
        FunctionSpaceEvalPackagePricing functionSpaceEvalPackagePricing = groupEvaluation.getFunctionSpaceEvalPackagePricings().iterator().next();
        List<FunctionSpaceEvalPackagePricingDOS> functionSpaceEvalPackagePricingDOS = new ArrayList<>(functionSpaceEvalPackagePricing.getPackagePricingDOSList());
        assertEquals(10, functionSpaceEvalPackagePricingDOS.get(0).getNoOfDelegates());
        assertEquals(20, functionSpaceEvalPackagePricingDOS.get(1).getNoOfDelegates());
    }

    @Test
    void shouldGetGroupEvaluationInputWithGroupPricingPackages() {
        GroupEvaluationUiWrapper parameterGroupEvaluationWrapper = FunctionSpaceEvaluationTestData.getParameterGroupEvaluationWrapper();
        parameterGroupEvaluationWrapper.setSelectedMethodType(GroupPricingEvaluationMethod.ROH);
        parameterGroupEvaluationWrapper.setSelectedEvaluationTypes(Set.of(GroupEvaluationType.GUEST_ROOM_ONLY));
        FunctionSpacePackageType functionSpacePackageType = buildFunctionSpacePackageType("packageType");
        GroupPricingConfigurationPackage groupPricingPackage = buildGroupPricingPackage("packageName", functionSpacePackageType);
        List<PackagePricingRow> packagePricingRows = buildGroupPricingPackageRows(groupPricingPackage, functionSpacePackageType);
        FunctionSpaceEvaluationDetailsLayout detailsLayout = Mockito.mock(FunctionSpaceEvaluationDetailsLayout.class);
        when(view.getDetailsLayout()).thenReturn(detailsLayout);
        when(detailsLayout.getDateOfStays()).thenReturn(getDateOfStays(false));
        when(detailsLayout.getGroupEvaluationAncillaries()).thenReturn(getAncillaryByProperty());
        when(detailsLayout.getGroupEvaluationConferenceAndBanquets()).thenReturn(getConfAndBanqByProperty());
        when(detailsLayout.getPackagePricingRows()).thenReturn(packagePricingRows);
        when(groupPricingPackageService.getGroupPricingPackageDetails(anySet())).thenReturn(Map.of(1, groupPricingPackage));
        FunctionSpaceEvaluationBasePresenter spy = Mockito.spy(presenter);
        Mockito.doReturn(false).when(spy).isFunctionSpace();
        Mockito.doReturn(true).when(spy).isGroupPricing();
        Mockito.doReturn(true).when(spy).isGroupPricingPackageEnabled();
        Mockito.doReturn(true).when(spy).isPackageEnabled();
        spy.setParametersGroupEvaluationWrapper(parameterGroupEvaluationWrapper);

        GroupEvaluation groupEvaluation = spy.getGroupEvaluationInput();

        assertEquals(1, groupEvaluation.getGroupPricingEvalPackagePricings().size());
        GroupPricingEvalPackagePricing groupPricingEvalPackagePricing = groupEvaluation.getGroupPricingEvalPackagePricings().iterator().next();
        List<GroupPricingEvalPackagePricingDOS> groupPricingEvalPackagePricingDOS = new ArrayList<>(groupPricingEvalPackagePricing.getPackagePricingDOSList());
        assertEquals(10, groupPricingEvalPackagePricingDOS.get(0).getNoOfDelegates());
        assertEquals(20, groupPricingEvalPackagePricingDOS.get(1).getNoOfDelegates());
    }

    @Test
    void shouldGetGroupEvaluationInputWithFunctionSpacePricingPackages() {
        GroupEvaluationUiWrapper parameterGroupEvaluationWrapper = FunctionSpaceEvaluationTestData.getParameterGroupEvaluationWrapper();
        parameterGroupEvaluationWrapper.setSelectedMethodType(GroupPricingEvaluationMethod.ROH);
        parameterGroupEvaluationWrapper.setSelectedEvaluationTypes(Set.of(GroupEvaluationType.GUEST_ROOM_ONLY));
        FunctionSpacePackageType functionSpacePackageType = buildFunctionSpacePackageType("packageType");
        FunctionSpacePackage functionSpacePricingPackage = buildFunctionSpacePackage("packageName", functionSpacePackageType);
        functionSpacePricingPackage.setId(1);
        List<PackagePricingRow> packagePricingRows = buildFunctionSpacePackagePricingRows(functionSpacePricingPackage, functionSpacePackageType);
        FunctionSpaceEvaluationDetailsLayout detailsLayout = Mockito.mock(FunctionSpaceEvaluationDetailsLayout.class);
        when(view.getDetailsLayout()).thenReturn(detailsLayout);
        when(detailsLayout.getDateOfStays()).thenReturn(getDateOfStays(false));
        when(detailsLayout.getGroupEvaluationAncillaries()).thenReturn(getAncillaryByProperty());
        when(detailsLayout.getGroupEvaluationConferenceAndBanquets()).thenReturn(getConfAndBanqByProperty());
        when(detailsLayout.getPackagePricingRows()).thenReturn(packagePricingRows);
        when(conferenceAndBanquetEvaluationService.buildGroupPricingEvaluationWithFSConfBanqListByProperty(anyMap())).thenReturn(buildGroupEvalFunctionSpaceConfBanqByPropertyMap());
        when(functionSpacePackageService.getFunctionSpacePackageDetails(anySet())).thenReturn(Map.of(1, functionSpacePricingPackage));
        when(conferenceAndBanquetService.shouldUseFSRevenueStreams()).thenReturn(true);
        FunctionSpaceEvaluationBasePresenter spy = Mockito.spy(presenter);
        Mockito.doReturn(false).when(spy).isFunctionSpace();
        Mockito.doReturn(true).when(spy).isGroupPricing();
        Mockito.doReturn(true).when(spy).isGroupPricingPackageEnabled();
        Mockito.doReturn(true).when(spy).isPackageEnabled();
        spy.setParametersGroupEvaluationWrapper(parameterGroupEvaluationWrapper);

        GroupEvaluation groupEvaluation = spy.getGroupEvaluationInput();

        assertEquals(1, groupEvaluation.getFunctionSpaceEvalPackagePricings().size());
        FunctionSpaceEvalPackagePricing functionSpaceEvalPackagePricing = groupEvaluation.getFunctionSpaceEvalPackagePricings().iterator().next();
        List<FunctionSpaceEvalPackagePricingDOS> functionSpaceEvalPackagePricingDOS = new ArrayList<>(functionSpaceEvalPackagePricing.getPackagePricingDOSList());
        assertEquals(10, functionSpaceEvalPackagePricingDOS.get(0).getNoOfDelegates());
        assertEquals(20, functionSpaceEvalPackagePricingDOS.get(1).getNoOfDelegates());
    }

    @Test
    public void getGroupEvaluationInputWithPreviousStayRate() {
        GroupEvaluationUiWrapper parameterGroupEvaluationWrapper = FunctionSpaceEvaluationTestData.getParameterGroupEvaluationWrapper();
        parameterGroupEvaluationWrapper.setRohEvaluationType(ROHGroupEvaluationType.PREVIOUS_STAY.getCaption());
        parameterGroupEvaluationWrapper.setPreviousStayRate(BigDecimalUtil.ONE_HUNDRED);
        parameterGroupEvaluationWrapper.setExpectedBudgetRate(BigDecimal.valueOf(200.00));
        FunctionSpaceEvaluationDetailsLayout detailsLayout = Mockito.mock(FunctionSpaceEvaluationDetailsLayout.class);
        when(view.getDetailsLayout()).thenReturn(detailsLayout);
        when(detailsLayout.getDateOfStays()).thenReturn(getDateOfStays(false));
        when(detailsLayout.getGroupEvaluationAncillaries()).thenReturn(getAncillaryByProperty());
        when(detailsLayout.getGroupEvaluationConferenceAndBanquets()).thenReturn(getConfAndBanqByProperty());

        FunctionSpaceEvaluationBasePresenter spy = Mockito.spy(presenter);
        Mockito.doReturn(false).when(spy).isFunctionSpace();
        Mockito.doReturn(true).when(spy).isGroupPricing();
        spy.setParametersGroupEvaluationWrapper(parameterGroupEvaluationWrapper);
        GroupEvaluation groupEvaluation = spy.getGroupEvaluationInput();

        assertEquals("TEST", groupEvaluation.getGroupName());
        assertEquals(GroupEvaluationType.GUEST_ROOM_ONLY, groupEvaluation.getEvaluationType());
        assertEquals(BigDecimalUtil.ONE_HUNDRED, groupEvaluation.getPreviousStayRate());
        assertEquals(BigDecimal.valueOf(200.00), groupEvaluation.getExpectedBudgetRate());
    }

    @Test
    public void getGroupEvaluationInputForNewEvaluationWithSavedPreviousStayRate() {
        GroupEvaluationUiWrapper parameterGroupEvaluationWrapper = FunctionSpaceEvaluationTestData.getParameterGroupEvaluationWrapper();
        parameterGroupEvaluationWrapper.getGroupEvaluation().setPreviousStayRate(BigDecimalUtil.ONE_HUNDRED);
        parameterGroupEvaluationWrapper.getGroupEvaluation().setExpectedBudgetRate(BigDecimalUtil.ONE_HUNDRED);
        parameterGroupEvaluationWrapper.setRohEvaluationType(ROHGroupEvaluationType.NEW_EVALUATION.getCaption());
        FunctionSpaceEvaluationDetailsLayout detailsLayout = Mockito.mock(FunctionSpaceEvaluationDetailsLayout.class);
        when(view.getDetailsLayout()).thenReturn(detailsLayout);
        when(detailsLayout.getDateOfStays()).thenReturn(getDateOfStays(false));
        when(detailsLayout.getGroupEvaluationAncillaries()).thenReturn(getAncillaryByProperty());
        when(detailsLayout.getGroupEvaluationConferenceAndBanquets()).thenReturn(getConfAndBanqByProperty());

        FunctionSpaceEvaluationBasePresenter spy = Mockito.spy(presenter);
        Mockito.doReturn(false).when(spy).isFunctionSpace();
        Mockito.doReturn(true).when(spy).isGroupPricing();
        spy.setParametersGroupEvaluationWrapper(parameterGroupEvaluationWrapper);
        GroupEvaluation groupEvaluation = spy.getGroupEvaluationInput();

        assertNull(groupEvaluation.getPreviousStayRate());
        assertNull(groupEvaluation.getExpectedBudgetRate());
    }

    @Test
    public void getGroupEvaluationInputForOnBooksGroupEvaluation() {
        GroupEvaluationUiWrapper parameterGroupEvaluationWrapper = FunctionSpaceEvaluationTestData.getParameterGroupEvaluationWrapper();
        parameterGroupEvaluationWrapper.setRohEvaluationType(ROHGroupEvaluationType.EXISTING_GROUP.getCaption());
        FunctionSpaceEvaluationDetailsLayout detailsLayout = Mockito.mock(FunctionSpaceEvaluationDetailsLayout.class);
        when(view.getDetailsLayout()).thenReturn(detailsLayout);
        when(detailsLayout.getDateOfStays()).thenReturn(getDateOfStays(true));
        when(detailsLayout.getGroupEvaluationAncillaries()).thenReturn(getAncillaryByProperty());
        when(detailsLayout.getGroupEvaluationConferenceAndBanquets()).thenReturn(getConfAndBanqByProperty());

        FunctionSpaceEvaluationBasePresenter spy = Mockito.spy(presenter);
        Mockito.doReturn(false).when(spy).isFunctionSpace();
        Mockito.doReturn(true).when(spy).isGroupPricing();
        spy.setParametersGroupEvaluationWrapper(parameterGroupEvaluationWrapper);
        GroupEvaluation groupEvaluation = spy.getGroupEvaluationInput();

        groupEvaluation.getGroupEvaluationDayOfStays().forEach(evaluationDayOfStay -> assertEquals(BigDecimalUtil.ONE_HUNDRED, evaluationDayOfStay.getCurrentRate()));
    }

    @Test
    public void getGroupEvaluationInputWithoutDayOfStayZero() {
        GroupEvaluationUiWrapper parameterGroupEvaluationWrapper = FunctionSpaceEvaluationTestData.getParameterGroupEvaluationWrapper();
        parameterGroupEvaluationWrapper.setRohEvaluationType(ROHGroupEvaluationType.EXISTING_GROUP.getCaption());
        FunctionSpaceEvaluationDetailsLayout detailsLayout = Mockito.mock(FunctionSpaceEvaluationDetailsLayout.class);
        when(view.getDetailsLayout()).thenReturn(detailsLayout);
        when(detailsLayout.getDateOfStays()).thenReturn(getDateOfStays(true));
        when(detailsLayout.getGroupEvaluationAncillaries()).thenReturn(getAncillaryByProperty());
        when(detailsLayout.getGroupEvaluationConferenceAndBanquets()).thenReturn(getConfAndBanqByProperty());

        FunctionSpaceEvaluationBasePresenter spy = Mockito.spy(presenter);
        Mockito.doReturn(false).when(spy).isFunctionSpace();
        Mockito.doReturn(true).when(spy).isGroupPricing();
        spy.setParametersGroupEvaluationWrapper(parameterGroupEvaluationWrapper);
        GroupEvaluation groupEvaluation = spy.getGroupEvaluationInput();

        assertEquals(1, groupEvaluation.getGroupEvaluationDayOfStays().size());

        groupEvaluation.getGroupEvaluationDayOfStays().forEach(evaluationDayOfStay -> assertEquals(BigDecimalUtil.ONE_HUNDRED, evaluationDayOfStay.getCurrentRate()));
    }

    @Test
    void addFSConferenceBanquetToGroupPricingEvaluation() {
        FunctionSpaceEvaluationDetailsLayout detailsLayout = Mockito.mock(FunctionSpaceEvaluationDetailsLayout.class);
        when(view.getDetailsLayout()).thenReturn(detailsLayout);
        when(detailsLayout.getGroupEvaluationConferenceAndBanquets()).thenReturn(getConfAndBanqByProperty());
        when(conferenceAndBanquetEvaluationService.buildGroupPricingEvaluationWithFSConfBanqListByProperty(anyMap())).thenReturn(buildGroupEvalFunctionSpaceConfBanqByPropertyMap());
        GroupEvaluation groupEvaluation = new GroupEvaluation();

        presenter.addFSConferenceBanquetToGroupPricingEvaluation(groupEvaluation);

        assertEquals(1, groupEvaluation.getGroupEvaluationFunctionSpaceConfAndBanquets().size());
        GroupEvaluationFunctionSpaceConfAndBanq confAndBanq = groupEvaluation.getGroupEvaluationFunctionSpaceConfAndBanquets().iterator().next();
        assertEquals(groupEvaluation, confAndBanq.getGroupEvaluation());
        assertTrue(BigDecimalUtil.equals(BigDecimal.valueOf(100.00), confAndBanq.getRevenue()));
        assertTrue(BigDecimalUtil.equals(BigDecimal.valueOf(10.00), confAndBanq.getCommissionPercentage()));
        assertTrue(BigDecimalUtil.equals(BigDecimal.valueOf(30.00), confAndBanq.getProfitPercentage()));
    }

    private Map<Property, List<GroupEvaluationFunctionSpaceConfAndBanq>> buildGroupEvalFunctionSpaceConfBanqByPropertyMap() {
        HashMap<Property, List<GroupEvaluationFunctionSpaceConfAndBanq>> confBanqListByProperty = new HashMap<>();
        FunctionSpaceRevenueGroup revenueGroup = buildFunctionSpaceRevenueGroup("F&B");
        revenueGroup.setProfitPercent(BigDecimal.valueOf(0.30));
        confBanqListByProperty.put(GroupEvaluationUiWrapper.SINGLE_PROPERTY,
                List.of(getGroupEvalFunctionSpaceConfBanq(100.00, 10.00, revenueGroup)));
        return confBanqListByProperty;
    }

    private GroupEvaluationFunctionSpaceConfAndBanq getGroupEvalFunctionSpaceConfBanq(double revenue, double commission, FunctionSpaceRevenueGroup revenueGroup) {
        GroupEvaluationFunctionSpaceConfAndBanq confAndBanq = new GroupEvaluationFunctionSpaceConfAndBanq();
        confAndBanq.setFunctionSpaceRevenueGroup(revenueGroup);
        confAndBanq.setRevenue(BigDecimal.valueOf(revenue));
        confAndBanq.setCommissionPercentage(BigDecimal.valueOf(commission));
        return confAndBanq;
    }

    private List<GroupEvaluationFunctionSpace> getFunctionRooms() {
        GroupEvaluationFunctionSpace groupEvaluationFunctionSpace = new GroupEvaluationFunctionSpace() {
            @Override
            public Set<GroupEvaluationFunctionSpaceFunctionRoom> getGroupEvaluationFunctionSpaceFunctionRooms() {
                HashSet<GroupEvaluationFunctionSpaceFunctionRoom> groupEvaluationFunctionSpaceFunctionRooms = new HashSet<>();
                groupEvaluationFunctionSpaceFunctionRooms.add(new GroupEvaluationFunctionSpaceFunctionRoom());
                return groupEvaluationFunctionSpaceFunctionRooms;
            }
        };

        return Arrays.asList(groupEvaluationFunctionSpace);
    }

    private List<GroupEvaluationDateOfStay> getDateOfStays(boolean addCurrentRate) {
        return Arrays.asList(getDateOfStay(0, addCurrentRate), getDateOfStay(1, addCurrentRate));
    }

    private GroupEvaluationDateOfStay getDateOfStay(int day, boolean addCurrentRate) {
        GroupEvaluationDateOfStay groupEvaluationDateOfStay = new GroupEvaluationDateOfStay();
        groupEvaluationDateOfStay.setDayOfStay(day);
        if (addCurrentRate) {
            groupEvaluationDateOfStay.setCurrentRate(BigDecimalUtil.ONE_HUNDRED);
        }
        return groupEvaluationDateOfStay;
    }

    private Map<Property, List<GroupEvaluationConferenceAndBanquetDto>> getConfAndBanqByProperty() {
        Map<Property, List<GroupEvaluationConferenceAndBanquetDto>> confAndBanqByProperty = new HashMap<>();
        confAndBanqByProperty.put(GroupEvaluationUiWrapper.SINGLE_PROPERTY, Arrays.asList(getConfAndBanq(BigDecimal.TEN)));
        return confAndBanqByProperty;
    }

    private GroupEvaluationConferenceAndBanquetDto getConfAndBanq(BigDecimal profitPercent) {
        GroupEvaluationConferenceAndBanquetDto groupEvaluationConferenceAndBanquet = new GroupEvaluationConferenceAndBanquetDto();
        groupEvaluationConferenceAndBanquet.setConferenceAndBanquet(new ConferenceAndBanquetDto() {
            @Override
            public BigDecimal getProfitPercentage() {
                return profitPercent;
            }
        });
        return groupEvaluationConferenceAndBanquet;
    }

    private Map<Property, List<GroupEvaluationAncillary>> getAncillaryByProperty() {
        Map<Property, List<GroupEvaluationAncillary>> ancillaryByProperty = new HashMap<>();
        ancillaryByProperty.put(GroupEvaluationUiWrapper.SINGLE_PROPERTY, singletonList(getGroupEvaluationAncillary(BigDecimal.TEN)));
        return ancillaryByProperty;
    }

    private GroupEvaluationAncillary getGroupEvaluationAncillary(BigDecimal profitPercent) {
        GroupEvaluationAncillary groupEvaluationAncillary = new GroupEvaluationAncillary();
        groupEvaluationAncillary.setGroupPricingConfigurationAncillaryStream(new GroupPricingConfigurationAncillaryStream() {
            @Override
            public BigDecimal getProfitPercentage() {
                return profitPercent;
            }
        });
        return groupEvaluationAncillary;
    }

    @Test
    public void getStyleNameForGuestRoomData() throws Exception {
        Assertions.assertEquals("one", presenter.getStyleNameForGuestRoomData(BigDecimal.ZERO));
        Assertions.assertEquals("two", presenter.getStyleNameForGuestRoomData(BigDecimal.valueOf(31)));
        Assertions.assertEquals("three", presenter.getStyleNameForGuestRoomData(BigDecimal.valueOf(41)));
        Assertions.assertEquals("four", presenter.getStyleNameForGuestRoomData(BigDecimal.valueOf(51)));
        Assertions.assertEquals("five", presenter.getStyleNameForGuestRoomData(BigDecimal.valueOf(61)));
        Assertions.assertEquals("six", presenter.getStyleNameForGuestRoomData(BigDecimal.valueOf(71)));
        Assertions.assertEquals("seven", presenter.getStyleNameForGuestRoomData(BigDecimal.valueOf(81)));
        Assertions.assertEquals("eight", presenter.getStyleNameForGuestRoomData(BigDecimal.valueOf(91)));
    }

    @Test
    public void markIsMostOptimal() throws Exception {
        List<GroupEvaluationArrivalDate> groupEvaluationArrivalDates = Arrays.asList(getArrivalDate(3000), getArrivalDate(5000), getArrivalDate(100));
        presenter.markIsMostOptimal(groupEvaluationArrivalDates);
        GroupEvaluationArrivalDate groupEvaluationArrivalDate = groupEvaluationArrivalDates.get(0);
        assertEquals(BigDecimal.valueOf(5000), groupEvaluationArrivalDate.getTotalNetProfit());
        assertEquals(true, groupEvaluationArrivalDate.isMostOptimal());
    }

    private GroupEvaluationArrivalDate getArrivalDate(int roomGrossProfit) {
        GroupEvaluationArrivalDate groupEvaluationArrivalDate = new GroupEvaluationArrivalDate();
        groupEvaluationArrivalDate.setRoomGrossProfit(BigDecimal.valueOf(roomGrossProfit));
        groupEvaluationArrivalDate.setIncrementalRoomProfit(BigDecimal.valueOf(roomGrossProfit));
        GroupEvaluation groupEvaluation = new GroupEvaluation();
        groupEvaluation.setEvaluationType(GroupEvaluationType.GUEST_ROOM_ONLY);
        groupEvaluationArrivalDate.setGroupEvaluation(groupEvaluation);
        return groupEvaluationArrivalDate;
    }

    private GroupEvaluationArrivalDateGuestRoomRates getGroupEvaluationArrivalDateGuestRoomRates(GroupEvaluationArrivalDate arrivalDate) {
        GroupEvaluationArrivalDateGuestRoomRates guestRoomRates = new GroupEvaluationArrivalDateGuestRoomRates();
        guestRoomRates.setGroupEvaluationArrivalDate(arrivalDate);
        arrivalDate.addGroupEvaluationArrivalDateGuestRoomRates(guestRoomRates);
        return guestRoomRates;
    }

    @Test
    public void doNotMarkIsMostOptimal() throws Exception {
        List<GroupEvaluationArrivalDate> groupEvaluationArrivalDates = Arrays.asList(getArrivalDate(-200), getArrivalDate(-50), getArrivalDate(0));
        presenter.markIsMostOptimal(groupEvaluationArrivalDates);
        GroupEvaluationArrivalDate groupEvaluationArrivalDate = groupEvaluationArrivalDates.get(0);
        assertEquals(BigDecimal.valueOf(0), groupEvaluationArrivalDate.getTotalNetProfit());
        assertEquals(false, groupEvaluationArrivalDate.isMostOptimal());
    }

    @Test
    public void loadArrivalDateRange_DoNotSetRangeForFollowUpDate() throws Exception {
        FunctionSpaceEvaluationBasePresenter spy = Mockito.spy(presenter);
        LocalDate startDate = new LocalDate(2016, 7, 1);
        LocalDate endDate = new LocalDate(2016, 12, 1);

        when(uiContext.getSystemCaughtUpDateAsLocalDate()).thenReturn(startDate);
        when(groupEvaluationService.getEvaluationWindowEndDate()).thenReturn(endDate);
        when(pacmanConfigParamsService.getBooleanParameterValue(IPConfigParamName.GP_USE_EXTENDED_WND)).thenReturn(false);
        Mockito.doReturn(true).when(spy).isGroupPricing();
        FunctionSpaceEvaluationDetailsLayout detailsLayout = Mockito.mock(FunctionSpaceEvaluationDetailsLayout.class);
        when(view.getDetailsLayout()).thenReturn(detailsLayout).thenReturn(detailsLayout);

        spy.loadArrivalDateRange();

        Mockito.verify(detailsLayout).setArrivalDateRange(startDate, endDate);
    }

    @Test
    public void loadArrivalDateRange_ExtendedWindowIsEnabled() {
        FunctionSpaceEvaluationDetailsLayout detailsLayout = Mockito.mock(FunctionSpaceEvaluationDetailsLayout.class);
        LocalDate startDate = new LocalDate(2016, 7, 1);
        LocalDate endDate = startDate.plusDays(1094).minusDays(14);
        LocalDate forecastEndDate = new LocalDate(2016, 12, 1);

        when(uiContext.getSystemCaughtUpDateAsLocalDate()).thenReturn(startDate);
        when(groupEvaluationService.getEvaluationWindowEndDate()).thenReturn(forecastEndDate);
        when(pacmanConfigParamsService.getBooleanParameterValue(IPConfigParamName.GP_USE_EXTENDED_WND)).thenReturn(true);
        when(pacmanConfigParamsService.getIntegerParameterValue(
                FeatureTogglesConfigParamName.GROUP_PRICING_EXTENDED_WINDOW_DAYS.value())).thenReturn(1094);
        doReturn(true).when(presenter).isGroupPricing();
        when(view.getDetailsLayout()).thenReturn(detailsLayout);

        presenter.loadArrivalDateRange();

        verify(detailsLayout).setArrivalDateRange(startDate, endDate);
    }

    @Test
    public void getHeatMapStartDate_systemDate() throws Exception {
        FunctionSpaceEvaluationBasePresenter spy = Mockito.spy(presenter);
        LocalDate systemDate = new LocalDate(2016, 7, 1);

        when(uiContext.getSystemCaughtUpDateAsLocalDate()).thenReturn(systemDate);
        Mockito.doReturn(false).when(spy).isReevaluate();

        LocalDate heatMapStartDate = spy.getHeatMapStartDate();

        assertEquals(systemDate, heatMapStartDate);
    }

    @Test
    public void getHeatMapStartDate_multiPropertySystemDate() throws Exception {
        FunctionSpaceEvaluationBasePresenter spy = Mockito.spy(presenter);
        LocalDate systemDate = new LocalDate(2016, 8, 1);

        when(uiContext.getSystemCaughtUpDateAsLocalDate()).thenReturn(systemDate);
        Mockito.doReturn(false).when(spy).isReevaluate();

        LocalDate heatMapStartDate = spy.getHeatMapStartDate();

        assertEquals(systemDate, heatMapStartDate);
    }

    @Test
    public void getHeatMapStartDate_preferredDate() throws Exception {
        FunctionSpaceEvaluationBasePresenter spy = Mockito.spy(presenter);
        LocalDate preferredDate = new LocalDate(2016, 7, 1);

        GroupEvaluation groupEvaluation = new GroupEvaluation();
        GroupEvaluationArrivalDate arrivalDate = new GroupEvaluationArrivalDate();
        arrivalDate.setArrivalDate(preferredDate);
        arrivalDate.setPreferredDate(true);
        groupEvaluation.addGroupEvaluationArrivalDate(arrivalDate);

        GroupEvaluationInputWrapper groupEvaluationInputWrapper = new GroupEvaluationInputWrapper(READ_ONLY_TRUE,
                groupEvaluation, IS_GROUP_PRICING_TRUE);

        Mockito.doNothing().when(spy).initParametersData();
        when(spy.isReevaluate()).thenReturn(Boolean.TRUE);

        spy.onViewOpened(groupEvaluationInputWrapper);

        Mockito.doReturn(true).when(spy).isReevaluate();

        LocalDate heatMapStartDate = spy.getHeatMapStartDate();

        assertEquals(preferredDate, heatMapStartDate);
    }

    @Test
    public void getHeatMapStartDate_multiPropertyPreferredDate() throws Exception {
        FunctionSpaceEvaluationBasePresenter spy = Mockito.spy(presenter);
        LocalDate preferredDate = new LocalDate(2016, 8, 1);

        GroupEvaluationMulti groupEvaluationMulti = new GroupEvaluationMulti();
        List<GroupEvaluation> groupEvaluations = getGroupEvaluationList();
        groupEvaluationMulti.setGroupEvaluations(groupEvaluations);

        GroupEvaluationArrivalDate arrivalDate = new GroupEvaluationArrivalDate();
        arrivalDate.setArrivalDate(preferredDate);
        arrivalDate.setPreferredDate(true);

        groupEvaluations.get(0).addGroupEvaluationArrivalDate(arrivalDate);

        GroupEvaluationInputWrapper groupEvaluationInputWrapper = new GroupEvaluationInputWrapper(READ_ONLY_TRUE,
                groupEvaluationMulti, IS_GROUP_PRICING_TRUE);

        Mockito.doNothing().when(spy).initParametersData();

        spy.onViewOpened(groupEvaluationInputWrapper);

        Mockito.doReturn(true).when(spy).isReevaluate();
        Mockito.doReturn(true).when(spy).isPropertyGroupView();

        LocalDate heatMapStartDate = spy.getHeatMapStartDate();

        assertEquals(preferredDate, heatMapStartDate);
    }

    @Test
    public void isPropertyOneWayOrTwoWay() throws Exception {
        when(uiContext.getPropertyGroupId()).thenReturn(null).thenReturn(null);
        when(groupEvaluationService.isPropertyOneWayOrTwoWay()).thenReturn(true).thenReturn(false);

        assertTrue(presenter.isPropertyOneWayOrTwoWay());
        assertFalse(presenter.isPropertyOneWayOrTwoWay());
    }

    @Test
    public void isPropertyOneWayOrTwoWay_notValidatedForMultiProperty() throws Exception {
        when(uiContext.getPropertyGroupId()).thenReturn(1);

        assertTrue(presenter.isPropertyOneWayOrTwoWay());
        verifyZeroInteractions(groupEvaluationService);
    }

    @Test
    public void previousStayRateOrOnBooksGroupEvaluationAreEnabled_SinglePropertyEvaluation() {
        when(uiContext.isPropertyGroupView()).thenReturn(false);
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.GROUP_EVALUATION_RESULTS_BY_OCCUPANCY_DATE)).thenReturn(true);
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.GROUP_EVALUATION_PREVIOUS_STAY_RATE_ENABLED)).thenReturn(true);
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.GROUP_EVALUATION_ON_BOOKS_GROUP_ENABLED)).thenReturn(true);
        assertTrue(presenter.previousStayRateOrOnBooksGroupEvaluationAreEnabled());
    }

    @Test
    public void previousStayRateOrOnBooksGroupEvaluationAreEnabled_MultiPropertyEvaluation() {
        when(uiContext.isPropertyGroupView()).thenReturn(true);
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.GROUP_EVALUATION_RESULTS_BY_OCCUPANCY_DATE)).thenReturn(true);
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.GROUP_EVALUATION_PREVIOUS_STAY_RATE_ENABLED)).thenReturn(true);
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.GROUP_EVALUATION_ON_BOOKS_GROUP_ENABLED)).thenReturn(true);
        assertFalse(presenter.previousStayRateOrOnBooksGroupEvaluationAreEnabled());
    }

    @Test
    public void shouldCheckComponentRoomsEnabledFlag() {
        FunctionSpaceEvaluationBasePresenter presenterSpy = Mockito.spy(presenter);
        Mockito.doNothing().when(presenterSpy).initParametersData();
        FunctionSpaceEvaluationDetailsLayout functionSpaceEvaluationDetailsLayout = Mockito.mock(FunctionSpaceEvaluationDetailsLayout.class);
        presenterSpy.init(new GroupEvaluationInputWrapper(false, new GroupEvaluation(), true), false);
        Mockito.verify(pacmanConfigParamsService).getBooleanParameterValue(FeatureTogglesConfigParamName.COMPONENT_ROOMS_ENABLED.value());
    }

    @Test
    public void addAccomType() throws Exception {
        FunctionSpaceEvaluationBasePresenter spy = Mockito.spy(presenter);
        Mockito.doNothing().when(spy).initParametersData();
        AccomType accomType = new AccomType();

        spy.init(new GroupEvaluationInputWrapper(true, new GroupEvaluation(), true), false);
        spy.addAccomType(accomType);

        List<AccomType> retAccomTypes = spy.getAccomTypesList();

        assertTrue(retAccomTypes.size() == 1);
        assertSame(accomType, retAccomTypes.get(0));
    }

    @Test
    public void testPopulateROHSearchByType() {
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.GROUP_EVALUATION_RESULTS_BY_OCCUPANCY_DATE)).thenReturn(true);
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.GROUP_EVALUATION_PREVIOUS_STAY_RATE_ENABLED)).thenReturn(true);
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.GROUP_EVALUATION_ON_BOOKS_GROUP_ENABLED)).thenReturn(true);
        when(view.getDetailsLayout()).thenReturn(functionSpaceEvaluationDetailsLayout);
        presenter.populateROHSearchByType();
        verify(functionSpaceEvaluationDetailsLayout).populateROHactiveROHSearchBy(rohSearchByTypesCaptor.capture());
        List<String> populatedROHSearchByTypes = rohSearchByTypesCaptor.getValue();
        assertEquals(3, populatedROHSearchByTypes.size());
        assertTrue(populatedROHSearchByTypes.contains(ROHGroupSearchByType.BLOCK_CODE.getCaption()));
        assertTrue(populatedROHSearchByTypes.contains(ROHGroupSearchByType.GROUP_NAME.getCaption()));
        assertTrue(populatedROHSearchByTypes.contains(ROHGroupSearchByType.OCCUPANCY_DATES.getCaption()));
    }

    @Test
    public void testSearchForGroupEvaluationDetailsByCode() {
        LocalDate startDate = new LocalDate();
        when(uiContext.getSystemCaughtUpDateAsLocalDate()).thenReturn(startDate);
        final List<GroupBlockMaster> groupMasterData = getGroupBlockMastersList(2);
        when(groupEvaluationOnBooksService.getOnBooksGroupDetails(groupMasterData.get(0).getGroupBlockDetails())).thenReturn(getOnBooksGroupDetails(2));
        Mockito.when(groupEvaluationService.getGroupMasterDataByCode("282", startDate)).thenReturn(groupMasterData);
        GroupEvaluationUiWrapper groupEvaluationUiWrapper = (GroupEvaluationUiWrapper) presenter.searchForGroupEvaluationDetailsBy("282", "blockCode").get(0);
        assertGroupEvaluationUiWrapperData(groupEvaluationUiWrapper);
    }

    @Test
    public void testSearchForGroupEvaluationDetailsByOccupancyDate() {
        final List<GroupBlockMaster> groupMasterData = getGroupBlockMastersList(12);
        LocalDate startDate = new LocalDate();
        when(uiContext.getSystemCaughtUpDateAsLocalDate()).thenReturn(startDate);
        when(groupEvaluationOnBooksService.getOnBooksGroupDetails(groupMasterData.get(0).getGroupBlockDetails())).thenReturn(getOnBooksGroupDetails(12));
        final LocalDate localDate = startDate.plusDays(1);
        Mockito.when(groupEvaluationService.getGroupMasterDataByDate(localDate, startDate)).thenReturn(groupMasterData);
        String dateString = localDate.getMonthOfYear() + "/" + localDate.getDayOfMonth() + "/" + localDate.getYear();
        GroupEvaluationUiWrapper groupEvaluationUiWrapper = (GroupEvaluationUiWrapper) presenter.searchForGroupEvaluationDetailsBy(dateString, "occupancyDate").get(0);
        assertGroupEvaluationUiWrapperData(groupEvaluationUiWrapper);
    }

    @Test
    public void testSearchForGroupEvaluationDetailsByName() {
        LocalDate startDate = new LocalDate();
        when(uiContext.getSystemCaughtUpDateAsLocalDate()).thenReturn(startDate);
        final List<GroupBlockMaster> groupMasterData = getGroupBlockMastersList(10);
        when(groupEvaluationOnBooksService.getOnBooksGroupDetails(groupMasterData.get(0).getGroupBlockDetails())).thenReturn(getOnBooksGroupDetails(10));
        Mockito.when(groupEvaluationService.getGroupMasterDataByName("Group_Test_001", startDate)).thenReturn(groupMasterData);
        GroupEvaluationUiWrapper groupEvaluationUiWrapper = (GroupEvaluationUiWrapper) presenter.searchForGroupEvaluationDetailsBy("Group_Test_001", "groupName").get(0);
        assertGroupEvaluationUiWrapperData(groupEvaluationUiWrapper);
    }

    @Test
    public void testSearchForGroupEvaluationDetailsByNameWhenBlocksZero() {
        final List<GroupBlockMaster> groupMasterData = getGroupBlockMastersList(0);
        when(groupEvaluationOnBooksService.getOnBooksGroupDetails(groupMasterData.get(0).getGroupBlockDetails())).thenReturn(getOnBooksGroupDetails(10));
        Mockito.when(groupEvaluationService.getGroupMasterDataByName("Group_Test_001", new LocalDate())).thenReturn(groupMasterData);
        final List groupEvaluationUiWrapperlist = presenter.searchForGroupEvaluationDetailsBy("Group_Test_001", "groupName");
        assertEquals(0, groupEvaluationUiWrapperlist.size());
    }

    @Test
    public void testSearchForGroupEvaluationDetailsByCodeWhenBlocksZero() {
        final List<GroupBlockMaster> groupMasterData = getGroupBlockMastersList(0);
        Mockito.when(groupEvaluationService.getGroupMasterDataByCode("282", new LocalDate())).thenReturn(groupMasterData);
        final List groupEvaluationUiWrapperlist = presenter.searchForGroupEvaluationDetailsBy("282", "blockCode");
        assertEquals(0, groupEvaluationUiWrapperlist.size());
    }

    @Test
    public void testSearchForGroupEvaluationDetailsByOccupancyDateWhenBlocksZero() {
        final List<GroupBlockMaster> groupMasterData = getGroupBlockMastersList(0);
        LocalDate startDate = new LocalDate();
        when(uiContext.getSystemCaughtUpDateAsLocalDate()).thenReturn(startDate);
        final LocalDate localDate = startDate.plusDays(1);
        Mockito.when(groupEvaluationService.getGroupMasterDataByDate(localDate, startDate)).thenReturn(groupMasterData);
        String dateString = localDate.getMonthOfYear() + "/" + localDate.getDayOfMonth() + "/" + localDate.getYear();
        final List groupEvaluationUiWrapperlist = presenter.searchForGroupEvaluationDetailsBy(dateString, "occupancyDate");
        assertEquals(0, groupEvaluationUiWrapperlist.size());
    }

    @Test
    void getCurrentBarDTOListShouldReturnDataUsingGroupEvaluationOnBooksCurrentBARForExistingEvaluation() {
        GroupEvaluation groupEvaluation = mock(GroupEvaluation.class);
        final int los = 1;
        when(groupEvaluation.getNumberOfNights()).thenReturn(los);
        LocalDate startDate = new LocalDate();
        LocalDate endDate = startDate.plusDays(los);
        List<CurrentBarDTO> currentBarDTOList = new ArrayList<>();

        final List<CurrentBarDetails> currentBarDetails = Arrays.asList(new CurrentBarDetails(startDate, BigDecimal.valueOf(10.00)), new CurrentBarDetails(endDate, BigDecimal.valueOf(10.00)));
        currentBarDTOList.add((new CurrentBarDTO("DELUXE", 1, 1,
                currentBarDetails)));
        when(groupEvaluation.getEarliestArrivalDate()).thenReturn(startDate);
        when(groupEvaluation.isPersisted()).thenReturn(Boolean.TRUE);

        doReturn(currentBarDTOList).when(presenter).populateCurrentBarDTOListUsingGroupEvaluationOnBooksCurrentBAR(anySet());
        final List currentBarDTOList1 = presenter.getCurrentBarDTOList(groupEvaluation);
        assertEquals(1, currentBarDTOList1.size());
    }

    @Test
    void getCurrentBarDTOListShouldReturnDataUsingCPDecisionBarOutputForNewEvaluation() {
        GroupEvaluation groupEvaluation = mock(GroupEvaluation.class);
        final int los = 1;
        when(groupEvaluation.getNumberOfNights()).thenReturn(los);
        LocalDate startDate = new LocalDate();
        LocalDate endDate = startDate.plusDays(los);
        List<CurrentBarDTO> currentBarDTOList = new ArrayList<>();

        final List<CurrentBarDetails> currentBarDetails = Arrays.asList(new CurrentBarDetails(startDate, BigDecimal.valueOf(10.00)), new CurrentBarDetails(endDate, BigDecimal.valueOf(10.00)));
        currentBarDTOList.add((new CurrentBarDTO("DELUXE", 1, 1,
                currentBarDetails)));
        when(groupEvaluation.getEarliestArrivalDate()).thenReturn(startDate);
        when(groupEvaluation.isPersisted()).thenReturn(Boolean.FALSE);

        when(groupEvaluationService.populateCurrentBarDTOListUsingCPDecisionBAROutputByDateRange(any(LocalDate.class), any(LocalDate.class)))
                .thenReturn(currentBarDTOList);
        final List<CurrentBarDTO> currentBarDTOList1 = presenter.getCurrentBarDTOList(groupEvaluation);
        assertEquals(1, currentBarDTOList1.size());
        assertEquals(2, currentBarDTOList1.get(0).getCurrentBarDetailsList().size());
        verify(groupEvaluationService).addCurrentBarDataToGroupEvaluation(currentBarDTOList, groupEvaluation);
    }

    @Test
    public void shouldFetchPackagePricingRows() {
        List<FunctionSpaceEvalPackagePricing> evalPackagePricings = new ArrayList<>();
        evalPackagePricings.add(new FunctionSpaceEvalPackagePricing());
        when(functionSpacePackageEvalService.getAllPackagesForEvalPricing(anyInt())).thenReturn(evalPackagePricings);
        List allPackagesForEvalPricing = presenter.getAllPackagesForEvalPricing(1);
        verify(functionSpacePackageEvalService).getAllPackagesForEvalPricing(1);
    }

    @Test
    public void shouldFetchFunctionSpacePackages() {
        FunctionSpaceEvaluationBasePresenter spy = Mockito.spy(presenter);
        Map<FunctionSpacePackageType, List<FunctionSpacePackage>> packageTypeListMap = new HashMap<>();
        List<FunctionSpacePackage> functionSpacePackages = new ArrayList<>();
        FunctionSpacePackage functionSpacePackage = new FunctionSpacePackage();
        String packageName = "packageName";
        functionSpacePackage.setName(packageName);
        functionSpacePackages.add(functionSpacePackage);
        FunctionSpacePackageType packageType = buildFunctionSpacePackageType("pkgType");
        packageTypeListMap.put(packageType, functionSpacePackages);
        when(functionSpacePackageEvalService.getAllPackagesByPackageType()).thenReturn(packageTypeListMap);
        Mockito.doReturn(true).when(spy).isFunctionSpace();

        Map<FunctionSpacePackageType, List<PackageDto>> allPackagesByPackageType = spy.getAllPackagesByPackageType();

        verify(functionSpacePackageEvalService).getAllPackagesByPackageType();
        verify(groupPricingPackageService, never()).getAllPackagesByPackageType();
        List<PackageDto> packageList = allPackagesByPackageType.get(packageType);
        assertEquals(packageName, packageList.get(0).getName());
    }

    @Test
    void shouldFetchGroupPricingPackages() {
        FunctionSpaceEvaluationBasePresenter spy = Mockito.spy(presenter);
        Map<FunctionSpacePackageType, List<GroupPricingConfigurationPackage>> packageTypeListMap = new HashMap<>();
        List<GroupPricingConfigurationPackage> groupPricingConfigurationPackages = new ArrayList<>();
        GroupPricingConfigurationPackage groupPricingConfigurationPackage = new GroupPricingConfigurationPackage();
        String packageName = "packageName";
        groupPricingConfigurationPackage.setName(packageName);
        groupPricingConfigurationPackages.add(groupPricingConfigurationPackage);
        FunctionSpacePackageType packageType = buildFunctionSpacePackageType("pkgType");
        packageTypeListMap.put(packageType, groupPricingConfigurationPackages);
        when(groupPricingPackageService.getAllPackagesByPackageType()).thenReturn(packageTypeListMap);
        Mockito.doReturn(false).when(spy).isFunctionSpace();

        Map<FunctionSpacePackageType, List<PackageDto>> allPackagesByPackageType = spy.getAllPackagesByPackageType();

        verify(groupPricingPackageService).getAllPackagesByPackageType();
        verify(functionSpacePackageEvalService, never()).getAllPackagesByPackageType();
        List<PackageDto> packageList = allPackagesByPackageType.get(packageType);
        assertEquals(packageName, packageList.get(0).getName());
    }

    @Test
    public void shouldPrepareListForUiDisplay() {
        List<FunctionSpaceEvalPackagePricing> evalPackagePricings = new ArrayList<>();
        FunctionSpacePackageType functionSpacePackageType1 = buildFunctionSpacePackageType("packageType1");
        FunctionSpacePackageType functionSpacePackageType2 = buildFunctionSpacePackageType("packageType2");
        FunctionSpacePackage functionSpacePackage1 = buildFunctionSpacePackage("packageName1", functionSpacePackageType1);
        FunctionSpacePackage functionSpacePackage2 = buildFunctionSpacePackage("packageName2", functionSpacePackageType1);
        FunctionSpacePackage functionSpacePackage3 = buildFunctionSpacePackage("packageName3", functionSpacePackageType2);
        FunctionSpacePackage functionSpacePackage4 = buildFunctionSpacePackage("packageName4", functionSpacePackageType2);
        FunctionSpaceEvalPackagePricing pricing1 = buiildFunctionSpaceEvalPackagePricing(functionSpacePackageType1, functionSpacePackage1, 1, 10, 2, 11, 3, 12, SINGLE_OCCUPANCY);
        FunctionSpaceEvalPackagePricing pricing2 = buiildFunctionSpaceEvalPackagePricing(functionSpacePackageType1, functionSpacePackage1, 1, 20, 2, 21, 3, 22, DOUBLE_OCCUPANCY);
        FunctionSpaceEvalPackagePricing pricing3 = buiildFunctionSpaceEvalPackagePricing(functionSpacePackageType2, functionSpacePackage3, 1, 90, 2, 91, 3, 92, EvaluationConstants.NON_GUEST_ROOM_TYPE);
        evalPackagePricings.addAll(Arrays.asList(pricing1, pricing2, pricing3));
        Map<FunctionSpacePackageType, List<FunctionSpacePackage>> allPackagesByPackageType = new HashMap<>();
        allPackagesByPackageType.put(functionSpacePackageType1, Arrays.asList(functionSpacePackage1, functionSpacePackage2));
        allPackagesByPackageType.put(functionSpacePackageType2, Arrays.asList(functionSpacePackage3, functionSpacePackage4));
        doReturn(false).when(presenter).checkIfSelectedRevisionIsLatest();
        doReturn(true).when(presenter).isRevisionView();

        List<PackagePricingRow> listForDisplay = presenter.prepareListForDisplay(evalPackagePricings, allPackagesByPackageType);
        assertEquals(4, listForDisplay.size());
        PackagePricingRow parentRow = listForDisplay.get(0);
        assertEquals(30, parentRow.getMapOfDosNDelegates().get(1));
        assertEquals(32, parentRow.getMapOfDosNDelegates().get(2));
        assertEquals(34, parentRow.getMapOfDosNDelegates().get(3));
        PackagePricingRow packagePricingRow = listForDisplay.get(3);
        assertEquals(90, packagePricingRow.getMapOfDosNDelegates().get(1));
        assertEquals(91, packagePricingRow.getMapOfDosNDelegates().get(2));
        assertEquals(92, packagePricingRow.getMapOfDosNDelegates().get(3));
    }

    @Test
    public void prepareListForUiDisplayWithPkgCommissionPercent() {
        // GIVEN
        List<FunctionSpaceEvalPackagePricing> evalPackagePricings = new ArrayList<>();
        FunctionSpacePackageType functionSpacePackageType1 = buildFunctionSpacePackageType("packageType1");
        FunctionSpacePackageType functionSpacePackageType2 = buildFunctionSpacePackageType("packageType2");
        FunctionSpacePackage functionSpacePackage1 = buildFunctionSpacePackage("packageName1", functionSpacePackageType1);
        FunctionSpacePackage functionSpacePackage2 = buildFunctionSpacePackage("packageName2", functionSpacePackageType1);
        FunctionSpacePackage functionSpacePackage3 = buildFunctionSpacePackage("packageName3", functionSpacePackageType2);
        FunctionSpacePackage functionSpacePackage4 = buildFunctionSpacePackage("packageName4", functionSpacePackageType2);
        FunctionSpaceEvalPackagePricing pricing1 = buiildFunctionSpaceEvalPackagePricing(functionSpacePackageType1, functionSpacePackage1, 1, 10, 2, 11, 3, 12, SINGLE_OCCUPANCY);
        FunctionSpaceEvalPackagePricing pricing2 = buiildFunctionSpaceEvalPackagePricing(functionSpacePackageType1, functionSpacePackage1, 1, 20, 2, 21, 3, 22, DOUBLE_OCCUPANCY);
        FunctionSpaceEvalPackagePricing pricing3 = buiildFunctionSpaceEvalPackagePricing(functionSpacePackageType2, functionSpacePackage3, 1, 90, 2, 91, 3, 92, EvaluationConstants.NON_GUEST_ROOM_TYPE);
        pricing1.setCommissionPercent(BigDecimal.valueOf(10));
        pricing2.setCommissionPercent(BigDecimal.valueOf(10));
        pricing3.setCommissionPercent(BigDecimal.valueOf(20));
        evalPackagePricings.addAll(Arrays.asList(pricing1, pricing2, pricing3));
        Map<FunctionSpacePackageType, List<FunctionSpacePackage>> allPackagesByPackageType = new HashMap<>();
        allPackagesByPackageType.put(functionSpacePackageType1, Arrays.asList(functionSpacePackage1, functionSpacePackage2));
        allPackagesByPackageType.put(functionSpacePackageType2, Arrays.asList(functionSpacePackage3, functionSpacePackage4));
        doReturn(false).when(presenter).checkIfSelectedRevisionIsLatest();
        doReturn(true).when(presenter).isRevisionView();

        // WHEN
        List<PackagePricingRow> listForDisplay = presenter.prepareListForDisplay(evalPackagePricings, allPackagesByPackageType);

        // THEN
        assertEquals(4, listForDisplay.size());
        PackagePricingRow parentRow = listForDisplay.get(0);
        assertEquals(30, parentRow.getMapOfDosNDelegates().get(1));
        assertEquals(32, parentRow.getMapOfDosNDelegates().get(2));
        assertEquals(34, parentRow.getMapOfDosNDelegates().get(3));
        assertEquals(0, parentRow.getCommissionPercent().compareTo(BigDecimal.valueOf(10)));
        PackagePricingRow packagePricingRow = listForDisplay.get(3);
        assertEquals(90, packagePricingRow.getMapOfDosNDelegates().get(1));
        assertEquals(91, packagePricingRow.getMapOfDosNDelegates().get(2));
        assertEquals(92, packagePricingRow.getMapOfDosNDelegates().get(3));
        assertEquals(0, packagePricingRow.getCommissionPercent().compareTo(BigDecimal.valueOf(20)));
    }

    @Test
    public void shouldPrepareListForUiDisplayForGroupPricing() {
        List<GroupPricingEvalPackagePricing> evalPackagePricings = new ArrayList<>();
        FunctionSpacePackageType functionSpacePackageType1 = buildFunctionSpacePackageType("packageType1");
        FunctionSpacePackageType functionSpacePackageType2 = buildFunctionSpacePackageType("packageType2");
        GroupPricingConfigurationPackage groupPricingConfigurationPackage1 = buildGroupPricingPackage("packageName1", functionSpacePackageType1);
        GroupPricingConfigurationPackage groupPricingConfigurationPackage2 = buildGroupPricingPackage("packageName2", functionSpacePackageType1);
        GroupPricingConfigurationPackage groupPricingConfigurationPackage3 = buildGroupPricingPackage("packageName3", functionSpacePackageType2);
        GroupPricingConfigurationPackage groupPricingConfigurationPackage4 = buildGroupPricingPackage("packageName4", functionSpacePackageType2);
        GroupPricingEvalPackagePricing pricing1 = buildGroupPricingEvalPackagePricing(functionSpacePackageType1, groupPricingConfigurationPackage1, 1, 10, 2, 11, 3, 12, SINGLE_OCCUPANCY);
        GroupPricingEvalPackagePricing pricing2 = buildGroupPricingEvalPackagePricing(functionSpacePackageType1, groupPricingConfigurationPackage1, 1, 20, 2, 21, 3, 22, DOUBLE_OCCUPANCY);
        GroupPricingEvalPackagePricing pricing3 = buildGroupPricingEvalPackagePricing(functionSpacePackageType2, groupPricingConfigurationPackage3, 1, 90, 2, 91, 3, 92, EvaluationConstants.NON_GUEST_ROOM_TYPE);
        evalPackagePricings.addAll(Arrays.asList(pricing1, pricing2, pricing3));
        Map<FunctionSpacePackageType, List<GroupPricingConfigurationPackage>> allPackagesByPackageType = new HashMap<>();
        allPackagesByPackageType.put(functionSpacePackageType1, Arrays.asList(groupPricingConfigurationPackage1, groupPricingConfigurationPackage2));
        allPackagesByPackageType.put(functionSpacePackageType2, Arrays.asList(groupPricingConfigurationPackage3, groupPricingConfigurationPackage4));
        doReturn(false).when(presenter).checkIfSelectedRevisionIsLatest();
        doReturn(true).when(presenter).isRevisionView();

        List<PackagePricingRow> listForDisplay = presenter.prepareListForDisplayForGroupPricing(evalPackagePricings, allPackagesByPackageType);
        assertEquals(4, listForDisplay.size());
        PackagePricingRow parentRow = listForDisplay.get(0);
        assertEquals(30, parentRow.getMapOfDosNDelegates().get(1));
        assertEquals(32, parentRow.getMapOfDosNDelegates().get(2));
        assertEquals(34, parentRow.getMapOfDosNDelegates().get(3));
        PackagePricingRow packagePricingRow = listForDisplay.get(3);
        assertEquals(90, packagePricingRow.getMapOfDosNDelegates().get(1));
        assertEquals(91, packagePricingRow.getMapOfDosNDelegates().get(2));
        assertEquals(92, packagePricingRow.getMapOfDosNDelegates().get(3));
    }

    @Test
    public void prepareListForUiDisplayWithPkgCommissionPercentForGroupPricing() {
        // GIVEN
        List<GroupPricingEvalPackagePricing> evalPackagePricings = new ArrayList<>();
        FunctionSpacePackageType functionSpacePackageType1 = buildFunctionSpacePackageType("packageType1");
        FunctionSpacePackageType functionSpacePackageType2 = buildFunctionSpacePackageType("packageType2");
        GroupPricingConfigurationPackage groupPricingConfigurationPackage1 = buildGroupPricingPackage("packageName1", functionSpacePackageType1);
        GroupPricingConfigurationPackage groupPricingConfigurationPackage2 = buildGroupPricingPackage("packageName2", functionSpacePackageType1);
        GroupPricingConfigurationPackage groupPricingConfigurationPackage3 = buildGroupPricingPackage("packageName3", functionSpacePackageType2);
        GroupPricingConfigurationPackage groupPricingConfigurationPackage4 = buildGroupPricingPackage("packageName4", functionSpacePackageType2);
        GroupPricingEvalPackagePricing pricing1 = buildGroupPricingEvalPackagePricing(functionSpacePackageType1, groupPricingConfigurationPackage1, 1, 10, 2, 11, 3, 12, SINGLE_OCCUPANCY);
        GroupPricingEvalPackagePricing pricing2 = buildGroupPricingEvalPackagePricing(functionSpacePackageType1, groupPricingConfigurationPackage1, 1, 20, 2, 21, 3, 22, DOUBLE_OCCUPANCY);
        GroupPricingEvalPackagePricing pricing3 = buildGroupPricingEvalPackagePricing(functionSpacePackageType2, groupPricingConfigurationPackage3, 1, 90, 2, 91, 3, 92, EvaluationConstants.NON_GUEST_ROOM_TYPE);
        pricing1.setCommissionPercent(BigDecimal.valueOf(10));
        pricing2.setCommissionPercent(BigDecimal.valueOf(10));
        pricing3.setCommissionPercent(BigDecimal.valueOf(20));
        evalPackagePricings.addAll(Arrays.asList(pricing1, pricing2, pricing3));
        Map<FunctionSpacePackageType, List<GroupPricingConfigurationPackage>> allPackagesByPackageType = new HashMap<>();
        allPackagesByPackageType.put(functionSpacePackageType1, Arrays.asList(groupPricingConfigurationPackage1, groupPricingConfigurationPackage2));
        allPackagesByPackageType.put(functionSpacePackageType2, Arrays.asList(groupPricingConfigurationPackage3, groupPricingConfigurationPackage4));
        doReturn(false).when(presenter).checkIfSelectedRevisionIsLatest();
        doReturn(true).when(presenter).isRevisionView();

        // WHEN
        List<PackagePricingRow> listForDisplay = presenter.prepareListForDisplayForGroupPricing(evalPackagePricings, allPackagesByPackageType);

        // THEN
        assertEquals(4, listForDisplay.size());
        PackagePricingRow parentRow = listForDisplay.get(0);
        assertEquals(30, parentRow.getMapOfDosNDelegates().get(1));
        assertEquals(32, parentRow.getMapOfDosNDelegates().get(2));
        assertEquals(34, parentRow.getMapOfDosNDelegates().get(3));
        assertEquals(0, parentRow.getCommissionPercent().compareTo(BigDecimal.valueOf(10)));
        PackagePricingRow packagePricingRow = listForDisplay.get(3);
        assertEquals(90, packagePricingRow.getMapOfDosNDelegates().get(1));
        assertEquals(91, packagePricingRow.getMapOfDosNDelegates().get(2));
        assertEquals(92, packagePricingRow.getMapOfDosNDelegates().get(3));
        assertEquals(0, packagePricingRow.getCommissionPercent().compareTo(BigDecimal.valueOf(20)));
    }

    @Test
    void getIdToFunctionSpacePackageMap() {
        // GIVEN
        FunctionSpacePackageType functionSpacePackageType1 = buildFunctionSpacePackageType("packageType1");
        FunctionSpacePackageType functionSpacePackageType2 = buildFunctionSpacePackageType("packageType2");
        functionSpacePackageType1.setId(1);
        functionSpacePackageType2.setId(2);
        FunctionSpacePackage functionSpacePackage1 = buildFunctionSpacePackage("packageName1", functionSpacePackageType1);
        FunctionSpacePackage functionSpacePackage2 = buildFunctionSpacePackage("packageName2", functionSpacePackageType2);
        functionSpacePackage1.setId(1);
        functionSpacePackage2.setId(2);
        FunctionSpaceEvalPackagePricing pricing1 = buiildFunctionSpaceEvalPackagePricing(functionSpacePackageType1, functionSpacePackage1, 1, 10, 2, 11, 3, 12, NON_GUEST_ROOM_TYPE);
        FunctionSpaceEvalPackagePricing pricing2 = buiildFunctionSpaceEvalPackagePricing(functionSpacePackageType2, functionSpacePackage2, 1, 20, 2, 21, 3, 22, NON_GUEST_ROOM_TYPE);
        List<FunctionSpaceEvalPackagePricing> evalPackagePricings = new ArrayList<>(Arrays.asList(pricing1, pricing2));
        when(functionSpacePackageService.getFunctionSpacePackages(anySet())).thenReturn(Map.ofEntries(Map.entry(1, functionSpacePackage1), Map.entry(2, functionSpacePackage2)));

        // WHEN
        Map<Integer, FunctionSpacePackage> idToPackageMap = presenter.getIdToFunctionSpacePackageMap(evalPackagePricings);

        // THEN
        assertEquals(2, idToPackageMap.size());
        assertTrue(idToPackageMap.containsKey(1));
        assertTrue(idToPackageMap.containsKey(2));
    }

    @Test
    void shouldGetIdToGroupPricingConfigurationPackageMap() {
        // GIVEN
        FunctionSpacePackageType functionSpacePackageType1 = buildFunctionSpacePackageType("packageType1");
        FunctionSpacePackageType functionSpacePackageType2 = buildFunctionSpacePackageType("packageType2");
        functionSpacePackageType1.setId(1);
        functionSpacePackageType2.setId(2);
        GroupPricingConfigurationPackage groupPricingConfigurationPackage1 = buildGroupPricingPackage("packageName1", functionSpacePackageType1);
        GroupPricingConfigurationPackage groupPricingConfigurationPackage2 = buildGroupPricingPackage("packageName1", functionSpacePackageType2);
        groupPricingConfigurationPackage1.setId(1);
        groupPricingConfigurationPackage2.setId(2);
        GroupPricingEvalPackagePricing pricing1 = buildGroupPricingEvalPackagePricing(functionSpacePackageType1, groupPricingConfigurationPackage1, 1, 10, 2, 11, 3, 12, NON_GUEST_ROOM_TYPE);
        GroupPricingEvalPackagePricing pricing2 = buildGroupPricingEvalPackagePricing(functionSpacePackageType2, groupPricingConfigurationPackage2, 1, 20, 2, 21, 3, 22, NON_GUEST_ROOM_TYPE);
        List<GroupPricingEvalPackagePricing> evalPackagePricings = new ArrayList<>(Arrays.asList(pricing1, pricing2));
        when(groupPricingPackageService.getGroupPricingConfigurationPackages(anySet())).thenReturn(Map.ofEntries(Map.entry(1, groupPricingConfigurationPackage1), Map.entry(2, groupPricingConfigurationPackage2)));

        // WHEN
        Map<Integer, GroupPricingConfigurationPackage> idToPackageMap = presenter.getIdToGroupPricingConfigurationPackageMap(evalPackagePricings);

        // THEN
        assertEquals(2, idToPackageMap.size());
        assertTrue(idToPackageMap.containsKey(1));
        assertTrue(idToPackageMap.containsKey(2));
    }

    @Test
    void getIdToFunctionSpacePackageMapForNullValue() {
        // WHEN
        Map<Integer, FunctionSpacePackage> idToPackageMap = presenter.getIdToFunctionSpacePackageMap(null);

        // THEN
        assertEquals(0, idToPackageMap.size());
    }

    @Test
    void shouldgetIdToGroupPricingPackageMapForNullValue() {
        // WHEN
        Map<Integer, GroupPricingConfigurationPackage> idToPackageMap = presenter.getIdToGroupPricingConfigurationPackageMap(null);

        // THEN
        assertEquals(0, idToPackageMap.size());
    }

    @Test
    void filterFunctionSpaceEvalPackagePricingNonModified() {
        // GIVEN
        FunctionSpacePackageType functionSpacePackageType1 = buildFunctionSpacePackageType("packageType1");
        FunctionSpacePackageType functionSpacePackageType2 = buildFunctionSpacePackageType("packageType2");
        functionSpacePackageType1.setId(1);
        functionSpacePackageType2.setId(2);
        LocalDateTime now = LocalDateTime.now();
        FunctionSpacePackage functionSpacePackage1 = buildFunctionSpacePackage(1, "packageName1", functionSpacePackageType1, now, now);
        FunctionSpacePackage functionSpacePackage2 = buildFunctionSpacePackage(2, "packageName2", functionSpacePackageType2, now, now.plusDays(1));
        FunctionSpaceEvalPackagePricing pricing1 = buildFunctionSpaceEvalPackagePricing(functionSpacePackageType1, functionSpacePackage1, 1, 10, NON_GUEST_ROOM_TYPE, now, now);
        FunctionSpaceEvalPackagePricing pricing2 = buildFunctionSpaceEvalPackagePricing(functionSpacePackageType2, functionSpacePackage2, 1, 20, NON_GUEST_ROOM_TYPE, now, now);
        List<FunctionSpaceEvalPackagePricing> evalPackagePricings = new ArrayList<>(Arrays.asList(pricing1, pricing2));
        Map<Integer, FunctionSpacePackage> idToPackageMap = Map.ofEntries(Map.entry(1, functionSpacePackage1), Map.entry(2, functionSpacePackage2));

        // WHEN
        List<FunctionSpaceEvalPackagePricing> filteredEvalPackagePricing = presenter.filterFunctionSpaceEvalPackagePricing(evalPackagePricings, idToPackageMap, false);

        // THEN
        assertEquals(1, filteredEvalPackagePricing.size());
        assertEquals(1, filteredEvalPackagePricing.get(0).getFunctionSpacePackage().getId());
    }

    @Test
    void filterFunctionSpaceEvalPackagePricingModified() {
        // GIVEN
        FunctionSpacePackageType functionSpacePackageType1 = buildFunctionSpacePackageType("packageType1");
        FunctionSpacePackageType functionSpacePackageType2 = buildFunctionSpacePackageType("packageType2");
        functionSpacePackageType1.setId(1);
        functionSpacePackageType2.setId(2);
        LocalDateTime now = LocalDateTime.now();
        FunctionSpacePackage functionSpacePackage1 = buildFunctionSpacePackage(1, "packageName1", functionSpacePackageType1, now, now);
        FunctionSpacePackage functionSpacePackage2 = buildFunctionSpacePackage(2, "packageName2", functionSpacePackageType2, now, now.plusDays(1));
        FunctionSpaceEvalPackagePricing pricing1 = buildFunctionSpaceEvalPackagePricing(functionSpacePackageType1, functionSpacePackage1, 1, 10, NON_GUEST_ROOM_TYPE, now, now);
        FunctionSpaceEvalPackagePricing pricing2 = buildFunctionSpaceEvalPackagePricing(functionSpacePackageType2, functionSpacePackage2, 1, 20, NON_GUEST_ROOM_TYPE, now, now);
        List<FunctionSpaceEvalPackagePricing> evalPackagePricings = new ArrayList<>(Arrays.asList(pricing1, pricing2));
        Map<Integer, FunctionSpacePackage> idToPackageMap = Map.ofEntries(Map.entry(1, functionSpacePackage1), Map.entry(2, functionSpacePackage2));

        // WHEN
        List<FunctionSpaceEvalPackagePricing> filteredEvalPackagePricing = presenter.filterFunctionSpaceEvalPackagePricing(evalPackagePricings, idToPackageMap, true);

        // THEN
        assertEquals(1, filteredEvalPackagePricing.size());
        assertEquals(2, filteredEvalPackagePricing.get(0).getFunctionSpacePackage().getId());
    }

    @Test
    void shouldFilterGroupPricingEvalPackagePricingNonModified() {
        // GIVEN
        FunctionSpacePackageType functionSpacePackageType1 = buildFunctionSpacePackageType("packageType1");
        FunctionSpacePackageType functionSpacePackageType2 = buildFunctionSpacePackageType("packageType2");
        functionSpacePackageType1.setId(1);
        functionSpacePackageType2.setId(2);
        LocalDateTime now = LocalDateTime.now();
        GroupPricingConfigurationPackage groupPricingConfigurationPackage1 = buildGroupPricingPackage(1, "packageName1", functionSpacePackageType1, now, now);
        GroupPricingConfigurationPackage groupPricingConfigurationPackage2 = buildGroupPricingPackage(2, "packageName2", functionSpacePackageType2, now, now.plusDays(1));
        GroupPricingEvalPackagePricing pricing1 = buildGroupPricingEvalPackagePricing(functionSpacePackageType1, groupPricingConfigurationPackage1, 1, 10, NON_GUEST_ROOM_TYPE, now, now);
        GroupPricingEvalPackagePricing pricing2 = buildGroupPricingEvalPackagePricing(functionSpacePackageType2, groupPricingConfigurationPackage2, 1, 20, NON_GUEST_ROOM_TYPE, now, now);
        List<GroupPricingEvalPackagePricing> evalPackagePricings = new ArrayList<>(Arrays.asList(pricing1, pricing2));
        Map<Integer, GroupPricingConfigurationPackage> idToPackageMap = Map.ofEntries(Map.entry(1, groupPricingConfigurationPackage1), Map.entry(2, groupPricingConfigurationPackage2));

        // WHEN
        List<GroupPricingEvalPackagePricing> filteredEvalPackagePricing = presenter.filterGroupPricingEvalPackagePricing(evalPackagePricings, idToPackageMap, false);

        // THEN
        assertEquals(1, filteredEvalPackagePricing.size());
        assertEquals(1, filteredEvalPackagePricing.get(0).getGroupPricingConfigurationPackage().getId());
    }

    @Test
    void shouldFilterGroupPricingEvalPackagePricingModified() {
        // GIVEN
        FunctionSpacePackageType functionSpacePackageType1 = buildFunctionSpacePackageType("packageType1");
        FunctionSpacePackageType functionSpacePackageType2 = buildFunctionSpacePackageType("packageType2");
        functionSpacePackageType1.setId(1);
        functionSpacePackageType2.setId(2);
        LocalDateTime now = LocalDateTime.now();
        GroupPricingConfigurationPackage groupPricingConfigurationPackage1 = buildGroupPricingPackage(1, "packageName1", functionSpacePackageType1, now, now);
        GroupPricingConfigurationPackage groupPricingConfigurationPackage2 = buildGroupPricingPackage(2, "packageName2", functionSpacePackageType2, now, now.plusDays(1));
        GroupPricingEvalPackagePricing pricing1 = buildGroupPricingEvalPackagePricing(functionSpacePackageType1, groupPricingConfigurationPackage1, 1, 10, NON_GUEST_ROOM_TYPE, now, now);
        GroupPricingEvalPackagePricing pricing2 = buildGroupPricingEvalPackagePricing(functionSpacePackageType2, groupPricingConfigurationPackage2, 1, 20, NON_GUEST_ROOM_TYPE, now, now);
        List<GroupPricingEvalPackagePricing> evalPackagePricings = new ArrayList<>(Arrays.asList(pricing1, pricing2));
        Map<Integer, GroupPricingConfigurationPackage> idToPackageMap = Map.ofEntries(Map.entry(1, groupPricingConfigurationPackage1), Map.entry(2, groupPricingConfigurationPackage2));

        // WHEN
        List<GroupPricingEvalPackagePricing> filteredEvalPackagePricing = presenter.filterGroupPricingEvalPackagePricing(evalPackagePricings, idToPackageMap, true);

        // THEN
        assertEquals(1, filteredEvalPackagePricing.size());
        assertEquals(2, filteredEvalPackagePricing.get(0).getGroupPricingConfigurationPackage().getId());
    }

    @Test
    void getIdToPackageNameMap() {
        // GIVEN
        FunctionSpacePackageType functionSpacePackageType1 = buildFunctionSpacePackageType("packageType1");
        FunctionSpacePackageType functionSpacePackageType2 = buildFunctionSpacePackageType("packageType2");
        functionSpacePackageType1.setId(1);
        functionSpacePackageType2.setId(2);
        LocalDateTime now = LocalDateTime.now();
        FunctionSpacePackage functionSpacePackage1 = buildFunctionSpacePackage(1, "packageName1", functionSpacePackageType1, now, now);
        FunctionSpacePackage functionSpacePackage2 = buildFunctionSpacePackage(2, "packageName2", functionSpacePackageType2, now, now.plusDays(1));
        GroupEvaluation groupEvaluation = new GroupEvaluation();
        groupEvaluation.setGroupEvaluationFunctionSpacePackageDetails(Set.of(
                buildGroupEvaluationFunctionSpacePackageDetail("packageName1", functionSpacePackage1),
                buildGroupEvaluationFunctionSpacePackageDetail("packageName2", functionSpacePackage2)
        ));

        // WHEN
        Map<Integer, String> idToPackageNameMap = presenter.getIdToPackageNameMapForFunctionSpace(groupEvaluation);

        // THEN
        assertEquals(2, idToPackageNameMap.size());
        assertEquals("packageName1", idToPackageNameMap.get(1));
        assertEquals("packageName2", idToPackageNameMap.get(2));
    }

    @Test
    void getPackageMappingForModifiedPackages() {
        // GIVEN
        Map<Integer, String> modifiedPackageNames = Map.ofEntries(Map.entry(1, "packageName2"));
        doReturn(Map.ofEntries(Map.entry(1, "packageName1"), Map.entry(2, "packageName2"))).when(presenter).getIdToPackageNameMapForFunctionSpace(any());

        // WHEN
        List<String> packageMapping = presenter.getPackageMappingForModifiedPackages(modifiedPackageNames, new GroupEvaluation(), false);

        // THEN
        assertEquals(1, packageMapping.size());
    }


    @Test
    void getPackageMappingForModifiedPackagesForGroupPricing() {
        // GIVEN
        Map<Integer, String> modifiedPackageNames = Map.ofEntries(Map.entry(1, "packageName2"));
        doReturn(Map.ofEntries(Map.entry(1, "packageName1"), Map.entry(2, "packageName2"))).when(presenter).getIdToPackageNameMapForGroupPricing(any());

        // WHEN
        List<String> packageMapping = presenter.getPackageMappingForModifiedPackages(modifiedPackageNames, new GroupEvaluation(), true);

        // THEN
        assertEquals(1, packageMapping.size());
    }

    @Test
    void getPackageConfigurationChangedErrorMessageForModifiedPackages() {
        // GIVEN
        FunctionSpacePackageType functionSpacePackageType1 = buildFunctionSpacePackageType("packageType1");
        FunctionSpacePackageType functionSpacePackageType2 = buildFunctionSpacePackageType("packageType2");
        functionSpacePackageType1.setId(1);
        functionSpacePackageType2.setId(2);
        LocalDateTime now = LocalDateTime.now();
        FunctionSpacePackage functionSpacePackage1 = buildFunctionSpacePackage(1, "packageName1", functionSpacePackageType1, now, now);
        FunctionSpacePackage functionSpacePackage2 = buildFunctionSpacePackage(2, "packageName2", functionSpacePackageType2, now, now.plusDays(1));
        FunctionSpaceEvalPackagePricing pricing1 = buildFunctionSpaceEvalPackagePricing(functionSpacePackageType1, functionSpacePackage1, 1, 10, NON_GUEST_ROOM_TYPE, now, now);
        FunctionSpaceEvalPackagePricing pricing2 = buildFunctionSpaceEvalPackagePricing(functionSpacePackageType2, functionSpacePackage2, 1, 20, NON_GUEST_ROOM_TYPE, now, now);
        Set<FunctionSpaceEvalPackagePricing> evalPackagePricings = Set.of(pricing1, pricing2);
        Map<Integer, FunctionSpacePackage> idToPackageMap = Map.ofEntries(Map.entry(1, functionSpacePackage1), Map.entry(2, functionSpacePackage2));

        GroupEvaluation groupEvaluation = new GroupEvaluation();
        groupEvaluation.setFunctionSpaceEvalPackagePricings(evalPackagePricings);

        doReturn(true).when(presenter).checkIfSelectedRevisionIsLatest();
        doReturn(false).when(presenter).isRevisionView();
        doReturn(idToPackageMap).when(presenter).getIdToFunctionSpacePackageMap(anyList());
        doReturn(Map.ofEntries(Map.entry(1, "packageName1"), Map.entry(2, "packageName2"))).when(presenter).getIdToPackageNameMapForFunctionSpace(any());

        // WHEN
        String errorMessage = presenter.getPackageConfigurationChangedErrorMessage(groupEvaluation);

        // THEN
        assertTrue(StringUtils.isNotEmpty(errorMessage));
    }

    @Test
    void shouldGetPackageConfigurationChangedErrorMessageForGroupPricingModifiedPackages() {
        // GIVEN
        FunctionSpacePackageType functionSpacePackageType1 = buildFunctionSpacePackageType("packageType1");
        FunctionSpacePackageType functionSpacePackageType2 = buildFunctionSpacePackageType("packageType2");
        functionSpacePackageType1.setId(1);
        functionSpacePackageType2.setId(2);
        LocalDateTime now = LocalDateTime.now();
        GroupPricingConfigurationPackage groupPricingConfigurationPackage1 = buildGroupPricingPackage(1, "packageName1", functionSpacePackageType1, now, now);
        GroupPricingConfigurationPackage groupPricingConfigurationPackage2 = buildGroupPricingPackage(2, "packageName2", functionSpacePackageType2, now, now.plusDays(1));
        GroupPricingEvalPackagePricing pricing1 = buildGroupPricingEvalPackagePricing(functionSpacePackageType1, groupPricingConfigurationPackage1, 1, 10, NON_GUEST_ROOM_TYPE, now, now);
        GroupPricingEvalPackagePricing pricing2 = buildGroupPricingEvalPackagePricing(functionSpacePackageType2, groupPricingConfigurationPackage2, 1, 20, NON_GUEST_ROOM_TYPE, now, now);
        Set<GroupPricingEvalPackagePricing> evalPackagePricings = Set.of(pricing1, pricing2);
        Map<Integer, GroupPricingConfigurationPackage> idToPackageMap = Map.ofEntries(Map.entry(1, groupPricingConfigurationPackage1), Map.entry(2, groupPricingConfigurationPackage2));

        GroupEvaluation groupEvaluation = new GroupEvaluation();
        groupEvaluation.setGroupPricingEvalPackagePricings(evalPackagePricings);

        doReturn(true).when(presenter).checkIfSelectedRevisionIsLatest();
        doReturn(false).when(presenter).isRevisionView();
        doReturn(idToPackageMap).when(presenter).getIdToGroupPricingConfigurationPackageMap(anyList());
        doReturn(true).when(groupEvaluationInputWrapper).isGroupPricing();
        doReturn(Map.ofEntries(Map.entry(1, "packageName1"), Map.entry(2, "packageName2"))).when(presenter).getIdToPackageNameMapForGroupPricing(any());

        // WHEN
        String errorMessage = presenter.getPackageConfigurationChangedErrorMessage(groupEvaluation);

        // THEN
        assertTrue(StringUtils.isNotEmpty(errorMessage));

    }

    @Test
    void shouldGetPackageConfigurationChangedErrorMessageForDeletedGroupPricingPackages() {
        // GIVEN
        FunctionSpacePackageType functionSpacePackageType1 = buildFunctionSpacePackageType("packageType1");
        FunctionSpacePackageType functionSpacePackageType2 = buildFunctionSpacePackageType("packageType2");
        functionSpacePackageType1.setId(1);
        functionSpacePackageType2.setId(2);
        LocalDateTime now = LocalDateTime.now();
        GroupPricingConfigurationPackage groupPricingConfigurationPackage = buildGroupPricingPackage(2, "packageName2", functionSpacePackageType2, now, now.plusDays(1));
        groupPricingConfigurationPackage.setStatus(TenantStatusEnum.DELETED);

        GroupPricingEvalPackagePricing pricing2 = buildGroupPricingEvalPackagePricing(functionSpacePackageType2, groupPricingConfigurationPackage, 1, 20, NON_GUEST_ROOM_TYPE, now, now);
        Set<GroupPricingEvalPackagePricing> evalPackagePricings = Set.of(pricing2);
        Map<Integer, GroupPricingConfigurationPackage> idToPackageMap = Map.ofEntries(Map.entry(2, groupPricingConfigurationPackage));

        GroupEvaluation groupEvaluation = new GroupEvaluation();
        groupEvaluation.setGroupPricingEvalPackagePricings(evalPackagePricings);

        doReturn(true).when(presenter).checkIfSelectedRevisionIsLatest();
        doReturn(false).when(presenter).isRevisionView();
        doReturn(idToPackageMap).when(presenter).getIdToGroupPricingConfigurationPackageMap(anyList());
        doReturn(true).when(groupEvaluationInputWrapper).isGroupPricing();
        doReturn(Map.ofEntries(Map.entry(2, "packageName2"))).when(presenter).getIdToPackageNameMapForFunctionSpace(any());

        // WHEN
        String errorMessage = presenter.getPackageConfigurationChangedErrorMessage(groupEvaluation);

        // THEN
        assertTrue(StringUtils.isNotEmpty(errorMessage));

    }

    @Test
    void shouldGetPackageConfigurationChangedErrorMessageForInactiveGroupPricingPackages() {
        // GIVEN
        FunctionSpacePackageType functionSpacePackageType1 = buildFunctionSpacePackageType("packageType1");
        FunctionSpacePackageType functionSpacePackageType2 = buildFunctionSpacePackageType("packageType2");
        functionSpacePackageType1.setId(1);
        functionSpacePackageType2.setId(2);
        LocalDateTime now = LocalDateTime.now();
        GroupPricingConfigurationPackage groupPricingConfigurationPackage = buildGroupPricingPackage(2, "packageName2", functionSpacePackageType2, now, now.plusDays(1));
        groupPricingConfigurationPackage.setStatus(TenantStatusEnum.INACTIVE);

        GroupPricingEvalPackagePricing pricing2 = buildGroupPricingEvalPackagePricing(functionSpacePackageType2, groupPricingConfigurationPackage, 1, 20, NON_GUEST_ROOM_TYPE, now, now);
        Set<GroupPricingEvalPackagePricing> evalPackagePricings = Set.of(pricing2);
        Map<Integer, GroupPricingConfigurationPackage> idToPackageMap = Map.ofEntries(Map.entry(2, groupPricingConfigurationPackage));

        GroupEvaluation groupEvaluation = new GroupEvaluation();
        groupEvaluation.setGroupPricingEvalPackagePricings(evalPackagePricings);

        doReturn(true).when(presenter).checkIfSelectedRevisionIsLatest();
        doReturn(false).when(presenter).isRevisionView();
        doReturn(true).when(groupEvaluationInputWrapper).isGroupPricing();
        doReturn(idToPackageMap).when(presenter).getIdToGroupPricingConfigurationPackageMap(anyList());
        doReturn(true).when(groupEvaluationInputWrapper).isGroupPricing();
        doReturn(Map.ofEntries(Map.entry(2, "packageName2"))).when(presenter).getIdToPackageNameMapForFunctionSpace(any());

        // WHEN
        String errorMessage = presenter.getPackageConfigurationChangedErrorMessage(groupEvaluation);

        // THEN
        assertTrue(StringUtils.isNotEmpty(errorMessage));

    }

    @Test
    void shouldGetPackageConfigurationChangedErrorMessageForModifiedInactiveAndDeletedGroupPricingPackages() {
        // GIVEN
        FunctionSpacePackageType functionSpacePackageType1 = buildFunctionSpacePackageType("packageType1");
        FunctionSpacePackageType functionSpacePackageType2 = buildFunctionSpacePackageType("packageType2");
        FunctionSpacePackageType functionSpacePackageType3 = buildFunctionSpacePackageType("packageType3");
        functionSpacePackageType1.setId(1);
        functionSpacePackageType2.setId(2);
        functionSpacePackageType3.setId(3);
        LocalDateTime now = LocalDateTime.now();
        GroupPricingConfigurationPackage groupPricingConfigurationPackage1 = buildGroupPricingPackage(1, "packageName1", functionSpacePackageType1, now, now);
        GroupPricingConfigurationPackage groupPricingConfigurationPackage2 = buildGroupPricingPackage(2, "packageName2", functionSpacePackageType2, now, now.plusDays(1));
        groupPricingConfigurationPackage2.setStatus(TenantStatusEnum.DELETED);
        GroupPricingConfigurationPackage groupPricingConfigurationPackage3 = buildGroupPricingPackage(3, "packageName3", functionSpacePackageType2, now, now.plusDays(2));
        groupPricingConfigurationPackage3.setStatus(TenantStatusEnum.INACTIVE);
        GroupPricingEvalPackagePricing pricing1 = buildGroupPricingEvalPackagePricing(functionSpacePackageType1, groupPricingConfigurationPackage1, 1, 10, NON_GUEST_ROOM_TYPE, now, now);
        GroupPricingEvalPackagePricing pricing2 = buildGroupPricingEvalPackagePricing(functionSpacePackageType2, groupPricingConfigurationPackage2, 1, 20, NON_GUEST_ROOM_TYPE, now, now);
        GroupPricingEvalPackagePricing pricing3 = buildGroupPricingEvalPackagePricing(functionSpacePackageType3, groupPricingConfigurationPackage3, 1, 20, NON_GUEST_ROOM_TYPE, now, now);
        Set<GroupPricingEvalPackagePricing> evalPackagePricings = Set.of(pricing1, pricing2, pricing3);
        Map<Integer, GroupPricingConfigurationPackage> idToPackageMap = Map.ofEntries(Map.entry(1, groupPricingConfigurationPackage1), Map.entry(2, groupPricingConfigurationPackage2), Map.entry(3, groupPricingConfigurationPackage3));

        GroupEvaluation groupEvaluation = new GroupEvaluation();
        groupEvaluation.setGroupPricingEvalPackagePricings(evalPackagePricings);

        doReturn(true).when(presenter).checkIfSelectedRevisionIsLatest();
        doReturn(false).when(presenter).isRevisionView();
        doReturn(idToPackageMap).when(presenter).getIdToGroupPricingConfigurationPackageMap(anyList());
        doReturn(true).when(groupEvaluationInputWrapper).isGroupPricing();
        doReturn(Map.ofEntries(Map.entry(1, "packageName1"), Map.entry(2, "packageName2"), Map.entry(3, "packageName3"))).when(presenter).getIdToPackageNameMapForFunctionSpace(any());

        // WHEN
        String errorMessage = presenter.getPackageConfigurationChangedErrorMessage(groupEvaluation);

        // THEN
        assertTrue(StringUtils.isNotEmpty(errorMessage));

    }

    @Test
    void getPackageConfigurationChangedErrorMessageForNonModifiedPackages() {
        // GIVEN
        FunctionSpacePackageType functionSpacePackageType1 = buildFunctionSpacePackageType("packageType1");
        FunctionSpacePackageType functionSpacePackageType2 = buildFunctionSpacePackageType("packageType2");
        functionSpacePackageType1.setId(1);
        functionSpacePackageType2.setId(2);
        LocalDateTime now = LocalDateTime.now();
        FunctionSpacePackage functionSpacePackage1 = buildFunctionSpacePackage(1, "packageName1", functionSpacePackageType1, now, now);
        FunctionSpacePackage functionSpacePackage2 = buildFunctionSpacePackage(2, "packageName2", functionSpacePackageType2, now, now);
        FunctionSpaceEvalPackagePricing pricing1 = buildFunctionSpaceEvalPackagePricing(functionSpacePackageType1, functionSpacePackage1, 1, 10, NON_GUEST_ROOM_TYPE, now, now);
        FunctionSpaceEvalPackagePricing pricing2 = buildFunctionSpaceEvalPackagePricing(functionSpacePackageType2, functionSpacePackage2, 1, 20, NON_GUEST_ROOM_TYPE, now, now);
        Set<FunctionSpaceEvalPackagePricing> evalPackagePricings = Set.of(pricing1, pricing2);
        Map<Integer, FunctionSpacePackage> idToPackageMap = Map.ofEntries(Map.entry(1, functionSpacePackage1), Map.entry(2, functionSpacePackage2));

        GroupEvaluation groupEvaluation = new GroupEvaluation();
        groupEvaluation.setFunctionSpaceEvalPackagePricings(evalPackagePricings);

        doReturn(true).when(presenter).checkIfSelectedRevisionIsLatest();
        doReturn(false).when(presenter).isRevisionView();
        doReturn(idToPackageMap).when(presenter).getIdToFunctionSpacePackageMap(anyList());
        doReturn(Map.ofEntries(Map.entry(1, "packageName1"), Map.entry(2, "packageName2"))).when(presenter).getIdToPackageNameMapForFunctionSpace(any());

        // WHEN
        String errorMessage = presenter.getPackageConfigurationChangedErrorMessage(groupEvaluation);

        // THEN
        assertTrue(StringUtils.isEmpty(errorMessage));
    }

    @Test
    void shouldNotGetAnyErrorMessageForNonModifiedPackagesForGroupPricing() {
        // GIVEN
        FunctionSpacePackageType functionSpacePackageType1 = buildFunctionSpacePackageType("packageType1");
        FunctionSpacePackageType functionSpacePackageType2 = buildFunctionSpacePackageType("packageType2");
        functionSpacePackageType1.setId(1);
        functionSpacePackageType2.setId(2);
        LocalDateTime now = LocalDateTime.now();
        GroupPricingConfigurationPackage groupPricingConfigurationPackage1 = buildGroupPricingPackage(1, "packageName1", functionSpacePackageType1, now, now);
        GroupPricingConfigurationPackage groupPricingConfigurationPackage2 = buildGroupPricingPackage(2, "packageName2", functionSpacePackageType2, now, now);
        GroupPricingEvalPackagePricing pricing1 = buildGroupPricingEvalPackagePricing(functionSpacePackageType1, groupPricingConfigurationPackage1, 1, 10, NON_GUEST_ROOM_TYPE, now, now);
        GroupPricingEvalPackagePricing pricing2 = buildGroupPricingEvalPackagePricing(functionSpacePackageType2, groupPricingConfigurationPackage2, 1, 20, NON_GUEST_ROOM_TYPE, now, now);
        Set<GroupPricingEvalPackagePricing> evalPackagePricings = Set.of(pricing1, pricing2);
        Map<Integer, GroupPricingConfigurationPackage> idToPackageMap = Map.ofEntries(Map.entry(1, groupPricingConfigurationPackage1), Map.entry(2, groupPricingConfigurationPackage2));

        GroupEvaluation groupEvaluation = new GroupEvaluation();
        groupEvaluation.setGroupPricingEvalPackagePricings(evalPackagePricings);

        doReturn(true).when(presenter).checkIfSelectedRevisionIsLatest();
        doReturn(false).when(presenter).isRevisionView();
        doReturn(idToPackageMap).when(presenter).getIdToGroupPricingConfigurationPackageMap(anyList());
        doReturn(Map.ofEntries(Map.entry(1, "packageName1"), Map.entry(2, "packageName2"))).when(presenter).getIdToPackageNameMapForFunctionSpace(any());

        // WHEN
        String errorMessage = presenter.getPackageConfigurationChangedErrorMessage(groupEvaluation);

        // THEN
        assertTrue(StringUtils.isEmpty(errorMessage));
    }


    @Test
    void createModifiedPackagePricingRowsForGRNotIncluded() {
        // GIVEN
        FunctionSpacePackageType functionSpacePackageType1 = buildFunctionSpacePackageType("packageType1");
        FunctionSpacePackageType functionSpacePackageType2 = buildFunctionSpacePackageType("packageType2");
        functionSpacePackageType1.setId(1);
        functionSpacePackageType2.setId(2);
        LocalDateTime now = LocalDateTime.now();
        FunctionSpacePackage functionSpacePackage1 = buildFunctionSpacePackage(1, "packageName1", functionSpacePackageType1, now, now);
        FunctionSpacePackage functionSpacePackage2 = buildFunctionSpacePackage(2, "packageName2", functionSpacePackageType2, now, now);
        FunctionSpaceEvalPackagePricing pricing1 = buildFunctionSpaceEvalPackagePricing(functionSpacePackageType1, functionSpacePackage1, 1, 10, NON_GUEST_ROOM_TYPE, now, now);
        FunctionSpaceEvalPackagePricing pricing2 = buildFunctionSpaceEvalPackagePricing(functionSpacePackageType2, functionSpacePackage2, 1, 20, NON_GUEST_ROOM_TYPE, now, now);
        List<FunctionSpaceEvalPackagePricing> evalPackagePricings = List.of(pricing1, pricing2);

        // WHEN
        List<PackagePricingRow> pricingRows = presenter.createModifiedPackagePricingRows(evalPackagePricings);

        // THEN
        assertEquals(2, pricingRows.size());
        assertEquals(1, pricingRows.get(0).getPackageType().getId());
        assertEquals(1, pricingRows.get(0).getPackageName().getId());
        assertEquals(2, pricingRows.get(1).getPackageType().getId());
        assertEquals(2, pricingRows.get(1).getPackageName().getId());
    }

    @Test
    void createModifiedPackagePricingRowsForGRIncluded() {
        // GIVEN
        FunctionSpacePackageType functionSpacePackageType1 = buildFunctionSpacePackageType("packageType1");
        FunctionSpacePackageType functionSpacePackageType2 = buildFunctionSpacePackageType("packageType2");
        functionSpacePackageType1.setId(1);
        functionSpacePackageType2.setId(2);
        LocalDateTime now = LocalDateTime.now();
        FunctionSpacePackage functionSpacePackage1 = buildFunctionSpacePackage(1, "packageName1", functionSpacePackageType1, now, now);
        functionSpacePackage1.setGuestRoomIncluded(true);
        FunctionSpaceEvalPackagePricing pricing1 = buildFunctionSpaceEvalPackagePricing(functionSpacePackageType1, functionSpacePackage1, 1, 10, SINGLE_OCCUPANCY, now, now);
        FunctionSpaceEvalPackagePricing pricing2 = buildFunctionSpaceEvalPackagePricing(functionSpacePackageType1, functionSpacePackage1, 1, 20, DOUBLE_OCCUPANCY, now, now);
        List<FunctionSpaceEvalPackagePricing> evalPackagePricings = List.of(pricing1, pricing2);

        // WHEN
        List<PackagePricingRow> pricingRows = presenter.createModifiedPackagePricingRows(evalPackagePricings);

        // THEN
        assertEquals(3, pricingRows.size());
        assertEquals(1, pricingRows.get(0).getPackageType().getId());
        assertEquals(1, pricingRows.get(0).getPackageName().getId());
        assertEquals(1, pricingRows.get(1).getPackageType().getId());
        assertEquals(1, pricingRows.get(1).getPackageName().getId());
        assertEquals(1, pricingRows.get(2).getPackageType().getId());
        assertEquals(1, pricingRows.get(2).getPackageName().getId());
    }

    @Test
    void createModifiedPackagePricingRows() {
        // GIVEN
        FunctionSpacePackageType functionSpacePackageType1 = buildFunctionSpacePackageType("packageType1");
        FunctionSpacePackageType functionSpacePackageType2 = buildFunctionSpacePackageType("packageType2");
        functionSpacePackageType1.setId(1);
        functionSpacePackageType2.setId(2);
        LocalDateTime now = LocalDateTime.now();
        FunctionSpacePackage functionSpacePackage1 = buildFunctionSpacePackage(1, "packageName1", functionSpacePackageType1, now, now);
        FunctionSpacePackage functionSpacePackage2 = buildFunctionSpacePackage(2, "packageName2", functionSpacePackageType2, now, now);
        FunctionSpacePackage functionSpacePackage3 = buildFunctionSpacePackage(3, "packageName3", functionSpacePackageType2, now, now);
        functionSpacePackage1.setGuestRoomIncluded(true);
        functionSpacePackage3.setStatus(TenantStatusEnum.INACTIVE);
        FunctionSpaceEvalPackagePricing pricing1 = buildFunctionSpaceEvalPackagePricing(functionSpacePackageType1, functionSpacePackage1, 1, 10, SINGLE_OCCUPANCY, now, now);
        FunctionSpaceEvalPackagePricing pricing2 = buildFunctionSpaceEvalPackagePricing(functionSpacePackageType1, functionSpacePackage1, 1, 20, DOUBLE_OCCUPANCY, now, now);
        FunctionSpaceEvalPackagePricing pricing3 = buildFunctionSpaceEvalPackagePricing(functionSpacePackageType2, functionSpacePackage2, 1, 10, NON_GUEST_ROOM_TYPE, now, now);
        FunctionSpaceEvalPackagePricing pricing4 = buildFunctionSpaceEvalPackagePricing(functionSpacePackageType2, functionSpacePackage3, 1, 20, NON_GUEST_ROOM_TYPE, now, now);
        List<FunctionSpaceEvalPackagePricing> evalPackagePricings = List.of(pricing1, pricing2, pricing3, pricing4);


        // WHEN
        List<PackagePricingRow> pricingRows = presenter.createModifiedPackagePricingRows(evalPackagePricings);

        // THEN
        assertEquals(4, pricingRows.size());
        assertEquals(1, pricingRows.get(0).getPackageType().getId());
        assertEquals(1, pricingRows.get(0).getPackageName().getId());
        assertEquals(1, pricingRows.get(1).getPackageType().getId());
        assertEquals(1, pricingRows.get(1).getPackageName().getId());
        assertEquals(1, pricingRows.get(2).getPackageType().getId());
        assertEquals(1, pricingRows.get(2).getPackageName().getId());
        assertEquals(2, pricingRows.get(3).getPackageType().getId());
        assertEquals(2, pricingRows.get(3).getPackageName().getId());
    }

    @Test
    void shouldCreateModifiedPackagePricingRowsForGRNotIncludedForGroupPricing() {
        // GIVEN
        FunctionSpacePackageType functionSpacePackageType1 = buildFunctionSpacePackageType("packageType1");
        FunctionSpacePackageType functionSpacePackageType2 = buildFunctionSpacePackageType("packageType2");
        functionSpacePackageType1.setId(1);
        functionSpacePackageType2.setId(2);
        LocalDateTime now = LocalDateTime.now();
        GroupPricingConfigurationPackage groupPricingConfigurationPackage1 = buildGroupPricingPackage(1, "packageName1", functionSpacePackageType1, now, now);
        GroupPricingConfigurationPackage groupPricingConfigurationPackage2 = buildGroupPricingPackage(2, "packageName2", functionSpacePackageType2, now, now);
        GroupPricingEvalPackagePricing pricing1 = buildGroupPricingEvalPackagePricing(functionSpacePackageType1, groupPricingConfigurationPackage1, 1, 10, NON_GUEST_ROOM_TYPE, now, now);
        GroupPricingEvalPackagePricing pricing2 = buildGroupPricingEvalPackagePricing(functionSpacePackageType2, groupPricingConfigurationPackage2, 1, 20, NON_GUEST_ROOM_TYPE, now, now);
        List<GroupPricingEvalPackagePricing> evalPackagePricings = List.of(pricing1, pricing2);

        // WHEN
        List<PackagePricingRow> pricingRows = presenter.createModifiedPackagePricingRowsForGroupPricing(evalPackagePricings);

        // THEN
        assertEquals(2, pricingRows.size());
        assertEquals(1, pricingRows.get(0).getPackageType().getId());
        assertEquals(1, pricingRows.get(0).getPackageName().getId());
        assertEquals(2, pricingRows.get(1).getPackageType().getId());
        assertEquals(2, pricingRows.get(1).getPackageName().getId());
    }

    @Test
    void shouldCreateModifiedPackagePricingRowsForGRIncludedForGroupPricing() {
        // GIVEN
        FunctionSpacePackageType functionSpacePackageType1 = buildFunctionSpacePackageType("packageType1");
        FunctionSpacePackageType functionSpacePackageType2 = buildFunctionSpacePackageType("packageType2");
        functionSpacePackageType1.setId(1);
        functionSpacePackageType2.setId(2);
        LocalDateTime now = LocalDateTime.now();
        GroupPricingConfigurationPackage groupPricingConfigurationPackage = buildGroupPricingPackage(1, "packageName1", functionSpacePackageType1, now, now);
        groupPricingConfigurationPackage.setGuestRoomIncluded(true);
        GroupPricingEvalPackagePricing pricing1 = buildGroupPricingEvalPackagePricing(functionSpacePackageType1, groupPricingConfigurationPackage, 1, 10, SINGLE_OCCUPANCY, now, now);
        GroupPricingEvalPackagePricing pricing2 = buildGroupPricingEvalPackagePricing(functionSpacePackageType1, groupPricingConfigurationPackage, 1, 20, DOUBLE_OCCUPANCY, now, now);
        List<GroupPricingEvalPackagePricing> evalPackagePricings = List.of(pricing1, pricing2);

        // WHEN
        List<PackagePricingRow> pricingRows = presenter.createModifiedPackagePricingRowsForGroupPricing(evalPackagePricings);

        // THEN
        assertEquals(3, pricingRows.size());
        assertEquals(1, pricingRows.get(0).getPackageType().getId());
        assertEquals(1, pricingRows.get(0).getPackageName().getId());
        assertEquals(1, pricingRows.get(1).getPackageType().getId());
        assertEquals(1, pricingRows.get(1).getPackageName().getId());
        assertEquals(1, pricingRows.get(2).getPackageType().getId());
        assertEquals(1, pricingRows.get(2).getPackageName().getId());
    }

    @Test
    void shouldCreateModifiedPackagePricingRowsForGroupPricing() {
        // GIVEN
        FunctionSpacePackageType functionSpacePackageType1 = buildFunctionSpacePackageType("packageType1");
        FunctionSpacePackageType functionSpacePackageType2 = buildFunctionSpacePackageType("packageType2");
        functionSpacePackageType1.setId(1);
        functionSpacePackageType2.setId(2);
        LocalDateTime now = LocalDateTime.now();
        GroupPricingConfigurationPackage groupPricingConfigurationPackage1 = buildGroupPricingPackage(1, "packageName1", functionSpacePackageType1, now, now);
        GroupPricingConfigurationPackage groupPricingConfigurationPackage2 = buildGroupPricingPackage(2, "packageName2", functionSpacePackageType2, now, now);
        GroupPricingConfigurationPackage groupPricingConfigurationPackage3 = buildGroupPricingPackage(3, "packageName3", functionSpacePackageType2, now, now);
        groupPricingConfigurationPackage1.setGuestRoomIncluded(true);
        groupPricingConfigurationPackage3.setStatus(TenantStatusEnum.INACTIVE);
        GroupPricingEvalPackagePricing pricing1 = buildGroupPricingEvalPackagePricing(functionSpacePackageType1, groupPricingConfigurationPackage1, 1, 10, SINGLE_OCCUPANCY, now, now);
        GroupPricingEvalPackagePricing pricing2 = buildGroupPricingEvalPackagePricing(functionSpacePackageType1, groupPricingConfigurationPackage1, 1, 20, DOUBLE_OCCUPANCY, now, now);
        GroupPricingEvalPackagePricing pricing3 = buildGroupPricingEvalPackagePricing(functionSpacePackageType2, groupPricingConfigurationPackage2, 1, 10, NON_GUEST_ROOM_TYPE, now, now);
        GroupPricingEvalPackagePricing pricing4 = buildGroupPricingEvalPackagePricing(functionSpacePackageType2, groupPricingConfigurationPackage3, 1, 20, NON_GUEST_ROOM_TYPE, now, now);
        List<GroupPricingEvalPackagePricing> evalPackagePricings = List.of(pricing1, pricing2, pricing3, pricing4);


        // WHEN
        List<PackagePricingRow> pricingRows = presenter.createModifiedPackagePricingRowsForGroupPricing(evalPackagePricings);

        // THEN
        assertEquals(4, pricingRows.size());
        assertEquals(1, pricingRows.get(0).getPackageType().getId());
        assertEquals(1, pricingRows.get(0).getPackageName().getId());
        assertEquals(1, pricingRows.get(1).getPackageType().getId());
        assertEquals(1, pricingRows.get(1).getPackageName().getId());
        assertEquals(1, pricingRows.get(2).getPackageType().getId());
        assertEquals(1, pricingRows.get(2).getPackageName().getId());
        assertEquals(2, pricingRows.get(3).getPackageType().getId());
        assertEquals(2, pricingRows.get(3).getPackageName().getId());
    }

    GroupEvaluationFunctionSpacePackageDetail buildGroupEvaluationFunctionSpacePackageDetail(String packageName,
                                                                                             FunctionSpacePackage functionSpacePackage) {
        GroupEvaluationFunctionSpacePackageDetail packageDetail = new GroupEvaluationFunctionSpacePackageDetail();
        packageDetail.setPackageName(packageName);
        packageDetail.setFunctionSpacePackage(functionSpacePackage);
        return packageDetail;
    }

    FunctionSpacePackage buildFunctionSpacePackage(Integer id,
                                                   String functionSpacePackageName,
                                                   FunctionSpacePackageType packageType,
                                                   LocalDateTime creationDate,
                                                   LocalDateTime lastUpdatedDate) {
        FunctionSpacePackage functionSpacePackage = new FunctionSpacePackage();
        functionSpacePackage.setId(id);
        functionSpacePackage.setName(functionSpacePackageName);
        functionSpacePackage.setPackageType(packageType);
        functionSpacePackage.setStatus(TenantStatusEnum.ACTIVE);
        functionSpacePackage.setCreateDate(creationDate);
        functionSpacePackage.setLastUpdatedDate(lastUpdatedDate);
        return functionSpacePackage;
    }

    GroupPricingConfigurationPackage buildGroupPricingPackage(Integer id,
                                                              String packageName,
                                                              FunctionSpacePackageType packageType,
                                                              LocalDateTime creationDate,
                                                              LocalDateTime lastUpdatedDate) {
        GroupPricingConfigurationPackage groupPricingConfigurationPackage = new GroupPricingConfigurationPackage();
        groupPricingConfigurationPackage.setId(id);
        groupPricingConfigurationPackage.setName(packageName);
        groupPricingConfigurationPackage.setPackageType(packageType);
        groupPricingConfigurationPackage.setStatus(TenantStatusEnum.ACTIVE);
        groupPricingConfigurationPackage.setCreateDate(creationDate);
        groupPricingConfigurationPackage.setLastUpdatedDate(lastUpdatedDate);
        return groupPricingConfigurationPackage;
    }

    FunctionSpaceEvalPackagePricing buildFunctionSpaceEvalPackagePricing(FunctionSpacePackageType packageType,
                                                                         FunctionSpacePackage packageName,
                                                                         int dos1, int nod1,
                                                                         String occupancy,
                                                                         LocalDateTime creationDate,
                                                                         LocalDateTime lastUpdatedDate) {
        FunctionSpaceEvalPackagePricing pricing = new FunctionSpaceEvalPackagePricing();
        pricing.setFunctionSpacePackageType(packageType);
        pricing.setFunctionSpacePackage(packageName);
        pricing.setPackageNameCategory(occupancy);
        FunctionSpaceEvalPackagePricingDOS pricingDOS1 = buildFunctionSpaceEvalPackagePricingDOS(dos1, nod1);
        Set<FunctionSpaceEvalPackagePricingDOS> dosSet = new LinkedHashSet<>();
        dosSet.add(pricingDOS1);
        pricing.setPackagePricingDOSList(dosSet);
        pricing.setCreateDate(creationDate);
        pricing.setLastUpdatedDate(lastUpdatedDate);
        return pricing;
    }

    GroupPricingEvalPackagePricing buildGroupPricingEvalPackagePricing(FunctionSpacePackageType packageType,
                                                                       GroupPricingConfigurationPackage packageName,
                                                                       int dos1, int nod1,
                                                                       String occupancy,
                                                                       LocalDateTime creationDate,
                                                                       LocalDateTime lastUpdatedDate) {
        GroupPricingEvalPackagePricing pricing = new GroupPricingEvalPackagePricing();
        pricing.setFunctionSpacePackageType(packageType);
        pricing.setGroupPricingConfigurationPackage(packageName);
        pricing.setPackageNameCategory(occupancy);
        GroupPricingEvalPackagePricingDOS pricingDOS1 = buildGroupPricingEvalPackagePricingDOS(dos1, nod1);
        Set<GroupPricingEvalPackagePricingDOS> dosSet = new LinkedHashSet<>();
        dosSet.add(pricingDOS1);
        pricing.setPackagePricingDOSList(dosSet);
        pricing.setCreateDate(creationDate);
        pricing.setLastUpdatedDate(lastUpdatedDate);
        return pricing;
    }

    @Test
    public void shouldMatch() {
        PackagePricingRow pricingRow = new PackagePricingRow();
        pricingRow.setCategory(SINGLE_OCCUPANCY);
        assertTrue(presenter.isMatch(pricingRow, SINGLE_OCCUPANCY));
        assertFalse(presenter.isMatch(pricingRow, DOUBLE_OCCUPANCY));
        pricingRow.setCategory(DOUBLE_OCCUPANCY);
        assertTrue(presenter.isMatch(pricingRow, DOUBLE_OCCUPANCY));
    }

    FunctionSpaceEvalPackagePricing buiildFunctionSpaceEvalPackagePricing(FunctionSpacePackageType packageType, FunctionSpacePackage packageName, int dos1, int nod1, int dos2, int nod2, int dos3, int nod3, String occupancy) {
        FunctionSpaceEvalPackagePricing pricing = new FunctionSpaceEvalPackagePricing();
        pricing.setFunctionSpacePackageType(packageType);
        pricing.setFunctionSpacePackage(packageName);
        pricing.setPackageNameCategory(occupancy);
        FunctionSpaceEvalPackagePricingDOS pricingDOS1 = buildFunctionSpaceEvalPackagePricingDOS(dos1, nod1);
        FunctionSpaceEvalPackagePricingDOS pricingDOS2 = buildFunctionSpaceEvalPackagePricingDOS(dos2, nod2);
        FunctionSpaceEvalPackagePricingDOS pricingDOS3 = buildFunctionSpaceEvalPackagePricingDOS(dos3, nod3);
        Set<FunctionSpaceEvalPackagePricingDOS> dosSet = new LinkedHashSet<>();
        dosSet.add(pricingDOS1);
        dosSet.add(pricingDOS2);
        dosSet.add(pricingDOS3);
        pricing.setPackagePricingDOSList(dosSet);
        return pricing;
    }

    GroupPricingEvalPackagePricing buildGroupPricingEvalPackagePricing(FunctionSpacePackageType packageType, GroupPricingConfigurationPackage packageName, int dos1, int nod1, int dos2, int nod2, int dos3, int nod3, String occupancy) {
        GroupPricingEvalPackagePricing pricing = new GroupPricingEvalPackagePricing();
        pricing.setFunctionSpacePackageType(packageType);
        pricing.setGroupPricingConfigurationPackage(packageName);
        pricing.setPackageNameCategory(occupancy);
        GroupPricingEvalPackagePricingDOS pricingDOS1 = buildGroupPricingEvalPackagePricingDOS(dos1, nod1);
        GroupPricingEvalPackagePricingDOS pricingDOS2 = buildGroupPricingEvalPackagePricingDOS(dos2, nod2);
        GroupPricingEvalPackagePricingDOS pricingDOS3 = buildGroupPricingEvalPackagePricingDOS(dos3, nod3);
        Set<GroupPricingEvalPackagePricingDOS> dosSet = new LinkedHashSet<>();
        dosSet.add(pricingDOS1);
        dosSet.add(pricingDOS2);
        dosSet.add(pricingDOS3);
        pricing.setPackagePricingDOSList(dosSet);
        return pricing;
    }

    private FunctionSpaceEvalPackagePricingDOS buildFunctionSpaceEvalPackagePricingDOS(int dos, int noOfDelegates) {
        FunctionSpaceEvalPackagePricingDOS pricingDOS = new FunctionSpaceEvalPackagePricingDOS();
        pricingDOS.setDayOfStay(dos);
        pricingDOS.setNoOfDelegates(noOfDelegates);
        return pricingDOS;
    }

    private GroupPricingEvalPackagePricingDOS buildGroupPricingEvalPackagePricingDOS(int dos, int noOfDelegates) {
        GroupPricingEvalPackagePricingDOS pricingDOS = new GroupPricingEvalPackagePricingDOS();
        pricingDOS.setDayOfStay(dos);
        pricingDOS.setNoOfDelegates(noOfDelegates);
        return pricingDOS;
    }

    FunctionSpacePackage buildFunctionSpacePackage(String functionSpacePackageName, FunctionSpacePackageType packageType) {
        FunctionSpacePackage functionSpacePackage = new FunctionSpacePackage();
        functionSpacePackage.setName(functionSpacePackageName);
        functionSpacePackage.setPackageType(packageType);
        functionSpacePackage.setStatus(TenantStatusEnum.ACTIVE);
        return functionSpacePackage;
    }

    GroupPricingConfigurationPackage buildGroupPricingPackage(String packageName, FunctionSpacePackageType packageType) {
        GroupPricingConfigurationPackage groupPricingPackage = new GroupPricingConfigurationPackage();
        groupPricingPackage.setId(1);
        groupPricingPackage.setName(packageName);
        groupPricingPackage.setPackageType(packageType);
        groupPricingPackage.setStatus(TenantStatusEnum.ACTIVE);
        groupPricingPackage.setGuestRoomIncluded(false);
        return groupPricingPackage;
    }

    FunctionSpacePackageType buildFunctionSpacePackageType(String packageTypeName) {
        FunctionSpacePackageType packageType = new FunctionSpacePackageType();
        packageType.setName(packageTypeName);
        return packageType;
    }

    @Test
    void testConvertPackagePricingRowToFunctionSpaceEvalPackagePricingForNonGuestRoomFunctionSpacePackage() {
        FunctionSpacePackageType functionSpacePackageType = buildFunctionSpacePackageType("packageType");
        FunctionSpacePackage functionSpacePackage = buildFunctionSpacePackage("packageName", functionSpacePackageType);
        functionSpacePackage.setId(1);
        functionSpacePackage.setGuestRoomIncluded(false);
        List<PackagePricingRow> packagePricingRows = buildFunctionSpacePackagePricingRows(functionSpacePackage, functionSpacePackageType);
        Map<Integer, FunctionSpacePackage> initializedFunctionSpacePackages = Map.ofEntries(Map.entry(functionSpacePackage.getId(), functionSpacePackage));

        Set<FunctionSpaceEvalPackagePricing> functionSpaceEvalPackagePricingSet = presenter.convertPackagePricingRowToFunctionSpaceEvalPackagePricing(packagePricingRows, initializedFunctionSpacePackages);

        assertEquals(1, functionSpaceEvalPackagePricingSet.size());

        FunctionSpaceEvalPackagePricing functionSpaceEvalPackagePricing = functionSpaceEvalPackagePricingSet.iterator().next();
        assertEquals(0, functionSpaceEvalPackagePricing.getCommissionPercent().compareTo(BigDecimal.ZERO));
        List<FunctionSpaceEvalPackagePricingDOS> functionSpaceEvalPackagePricingDOS = new ArrayList<>(functionSpaceEvalPackagePricing.getPackagePricingDOSList());
        assertEquals(10, functionSpaceEvalPackagePricingDOS.get(0).getNoOfDelegates());
        assertEquals(20, functionSpaceEvalPackagePricingDOS.get(1).getNoOfDelegates());
    }

    @Test
    void testConvertPackagePricingRowToFunctionSpaceEvalPackagePricingForGuestRoomFunctionSpacePackage() {
        FunctionSpacePackageType functionSpacePackageType = buildFunctionSpacePackageType("packageType");
        FunctionSpacePackage functionSpacePackage = buildFunctionSpacePackage("packageName", functionSpacePackageType);
        functionSpacePackage.setId(1);
        functionSpacePackage.setGuestRoomIncluded(true);
        List<PackagePricingRow> packagePricingRows = buildFunctionSpacePackagePricingRows(functionSpacePackage, functionSpacePackageType);
        Map<Integer, FunctionSpacePackage> initializedFunctionSpacePackages = Map.ofEntries(Map.entry(functionSpacePackage.getId(), functionSpacePackage));

        Set<FunctionSpaceEvalPackagePricing> functionSpaceEvalPackagePricingSet = presenter.convertPackagePricingRowToFunctionSpaceEvalPackagePricing(packagePricingRows, initializedFunctionSpacePackages);

        assertEquals(2, functionSpaceEvalPackagePricingSet.size());

        FunctionSpaceEvalPackagePricing functionSpaceEvalPackagePricing = functionSpaceEvalPackagePricingSet.iterator().next();
        assertEquals(0, functionSpaceEvalPackagePricing.getCommissionPercent().compareTo(BigDecimal.ZERO));
        List<FunctionSpaceEvalPackagePricingDOS> functionSpaceEvalPackagePricingDOS1 = new ArrayList<>(functionSpaceEvalPackagePricing.getPackagePricingDOSList());
        assertEquals(10, functionSpaceEvalPackagePricingDOS1.get(0).getNoOfDelegates());
        assertEquals(15, functionSpaceEvalPackagePricingDOS1.get(1).getNoOfDelegates());
    }

    @Test
    void shouldConvertPackagePricingRowToGroupPricingEvalPackagePricingForNonGuestRoomPackage() {
        FunctionSpacePackageType functionSpacePackageType = buildFunctionSpacePackageType("packageType");
        GroupPricingConfigurationPackage groupPricingPackage1 = buildGroupPricingPackage("packageName1", functionSpacePackageType);
        GroupPricingConfigurationPackage groupPricingPackage2 = buildGroupPricingPackage("packageName2", functionSpacePackageType);
        groupPricingPackage2.setId(2);
        List<PackagePricingRow> packagePricingRows1 = buildGroupPricingPackageRows(groupPricingPackage1, functionSpacePackageType);
        List<PackagePricingRow> packagePricingRows2 = buildGroupPricingPackageRows(groupPricingPackage2, functionSpacePackageType);
        Map<Integer, GroupPricingConfigurationPackage> initializedGroupPricingPackages = Map.of(groupPricingPackage1.getId(), groupPricingPackage1, groupPricingPackage2.getId(), groupPricingPackage2);

        Set<GroupPricingEvalPackagePricing> groupPricingEvalPackagePricingSet = presenter.convertPackagePricingRowToGroupPricingEvalPackagePricing(
                Stream.concat(packagePricingRows1.stream(), packagePricingRows2.stream()).collect(toList()),
                initializedGroupPricingPackages);

        assertEquals(2, groupPricingEvalPackagePricingSet.size());
        GroupPricingEvalPackagePricing groupPricingEvalPackagePricing = groupPricingEvalPackagePricingSet.iterator().next();
        assertEquals(0, groupPricingEvalPackagePricing.getCommissionPercent().compareTo(BigDecimal.ZERO));
        List<GroupPricingEvalPackagePricingDOS> groupPricingEvalPackagePricingDOS1 = new ArrayList<>(groupPricingEvalPackagePricing.getPackagePricingDOSList());
        assertEquals(10, groupPricingEvalPackagePricingDOS1.get(0).getNoOfDelegates());
        assertEquals(20, groupPricingEvalPackagePricingDOS1.get(1).getNoOfDelegates());
        groupPricingEvalPackagePricing = groupPricingEvalPackagePricingSet.iterator().next();
        List<GroupPricingEvalPackagePricingDOS> groupPricingEvalPackagePricingDOS2 = new ArrayList<>(groupPricingEvalPackagePricing.getPackagePricingDOSList());
        assertEquals(10, groupPricingEvalPackagePricingDOS2.get(0).getNoOfDelegates());
        assertEquals(20, groupPricingEvalPackagePricingDOS2.get(1).getNoOfDelegates());
    }

    @Test
    void shouldConvertPackagePricingRowToGroupPricingEvalPackagePricingForGuestRoomPackage() {
        FunctionSpacePackageType functionSpacePackageType = buildFunctionSpacePackageType("packageType");
        GroupPricingConfigurationPackage groupPricingPackage = buildGroupPricingPackage("packageName", functionSpacePackageType);
        groupPricingPackage.setGuestRoomIncluded(true);
        List<PackagePricingRow> packagePricingRows = buildGroupPricingPackageRows(groupPricingPackage, functionSpacePackageType);
        Map<Integer, GroupPricingConfigurationPackage> initializedGroupPricingPackages = Map.ofEntries(Map.entry(groupPricingPackage.getId(), groupPricingPackage));

        Set<GroupPricingEvalPackagePricing> groupPricingEvalPackagePricingSet = presenter.convertPackagePricingRowToGroupPricingEvalPackagePricing(packagePricingRows, initializedGroupPricingPackages);

        assertEquals(2, groupPricingEvalPackagePricingSet.size());
        GroupPricingEvalPackagePricing groupPricingEvalPackagePricing = groupPricingEvalPackagePricingSet.iterator().next();
        assertEquals(0, groupPricingEvalPackagePricing.getCommissionPercent().compareTo(BigDecimal.ZERO));
        List<GroupPricingEvalPackagePricingDOS> groupPricingEvalPackagePricingDOS = new ArrayList<>(groupPricingEvalPackagePricing.getPackagePricingDOSList());
        assertEquals(10, groupPricingEvalPackagePricingDOS.get(0).getNoOfDelegates());
        assertEquals(15, groupPricingEvalPackagePricingDOS.get(1).getNoOfDelegates());
    }

    @Test
    void extractCommissionPercent() {
        // GIVEN
        BigDecimal expectedCommissionPercent = BigDecimal.valueOf(10);
        FunctionSpacePackageType functionSpacePackageType1 = buildFunctionSpacePackageType("packageType");
        FunctionSpacePackage functionSpacePackage1 = buildFunctionSpacePackage("packageName", functionSpacePackageType1);
        FunctionSpaceEvalPackagePricing pricing = buiildFunctionSpaceEvalPackagePricing(functionSpacePackageType1, functionSpacePackage1, 1, 10, 2, 11, 3, 12, SINGLE_OCCUPANCY);
        pricing.setCommissionPercent(expectedCommissionPercent);
        Set<FunctionSpaceEvalPackagePricing> evalPackagePricings = Set.of(pricing);

        // WHEN
        BigDecimal actualCommissionPercent = presenter.extractCommissionPercent(evalPackagePricings);

        // THEN
        assertEquals(0, expectedCommissionPercent.compareTo(actualCommissionPercent));
    }

    private List<PackagePricingRow> buildFunctionSpacePackagePricingRows(FunctionSpacePackage functionSpacePackage, FunctionSpacePackageType functionSpacePackageType) {
        PackageDto functionSpacePackageDto = convertToFunctionSpacePackageDto(functionSpacePackage);
        if (functionSpacePackage.isGuestRoomIncluded()) {
            PackagePricingRow parentPackagePricingRow = buildPackagePricingRow(functionSpacePackageDto, functionSpacePackageType,
                    Map.ofEntries(Map.entry(1, 20), Map.entry(2, 30)), EvaluationConstants.PARENT, BigDecimal.ZERO);
            PackagePricingRow singleOccupancyPackagePricingRow = buildPackagePricingRow(functionSpacePackageDto, functionSpacePackageType,
                    Map.ofEntries(Map.entry(1, 10), Map.entry(2, 15)), EvaluationConstants.SINGLE_OCCUPANCY, BigDecimal.ZERO);
            PackagePricingRow doubleOccupancyPackagePricingRow = buildPackagePricingRow(functionSpacePackageDto, functionSpacePackageType,
                    Map.ofEntries(Map.entry(1, 10), Map.entry(2, 15)), EvaluationConstants.DOUBLE_OCCUPANCY, BigDecimal.ZERO);

            return List.of(parentPackagePricingRow, singleOccupancyPackagePricingRow, doubleOccupancyPackagePricingRow);
        } else {
            PackagePricingRow packagePricingRow = buildPackagePricingRow(functionSpacePackageDto, functionSpacePackageType,
                    Map.ofEntries(Map.entry(1, 10), Map.entry(2, 20)), "", BigDecimal.ZERO);
            return List.of(packagePricingRow);
        }
    }

    private List<PackagePricingRow> buildGroupPricingPackageRows(GroupPricingConfigurationPackage groupPricingPackage, FunctionSpacePackageType functionSpacePackageType) {
        PackageDto groupPricingPackageDto = convertToGroupPricingPackageDto(groupPricingPackage);
        if (groupPricingPackage.isGuestRoomIncluded()) {
            PackagePricingRow packagePricingRow = buildPackagePricingRow(groupPricingPackageDto, functionSpacePackageType,
                    Map.ofEntries(Map.entry(1, 20), Map.entry(2, 30)), EvaluationConstants.PARENT, BigDecimal.ZERO);
            PackagePricingRow singleOccupancyPackagePricingRow = buildPackagePricingRow(groupPricingPackageDto, functionSpacePackageType,
                    Map.ofEntries(Map.entry(1, 10), Map.entry(2, 15)), EvaluationConstants.SINGLE_OCCUPANCY, BigDecimal.ZERO);
            PackagePricingRow doubleOccupancyPackagePricingRow = buildPackagePricingRow(groupPricingPackageDto, functionSpacePackageType,
                    Map.ofEntries(Map.entry(1, 10), Map.entry(2, 15)), EvaluationConstants.DOUBLE_OCCUPANCY, BigDecimal.ZERO);
            return List.of(packagePricingRow, singleOccupancyPackagePricingRow, doubleOccupancyPackagePricingRow);
        } else {
            PackagePricingRow packagePricingRow = buildPackagePricingRow(groupPricingPackageDto, functionSpacePackageType,
                    Map.ofEntries(Map.entry(1, 10), Map.entry(2, 20)), "", BigDecimal.ZERO);
            return List.of(packagePricingRow);
        }
    }

    private PackagePricingRow buildPackagePricingRow(PackageDto functionSpacePackage, FunctionSpacePackageType functionSpacePackageType,
                                                     Map<Integer, Integer> mapOfDosNDelegates, String category, BigDecimal commissionPercent) {
        PackagePricingRow packagePricingRow = new PackagePricingRow();
        packagePricingRow.setCategory(category);
        packagePricingRow.setPackageName(functionSpacePackage);
        packagePricingRow.setOtherPackageNames(List.of());
        packagePricingRow.setPackageType(functionSpacePackageType);
        packagePricingRow.setMapOfDosNDelegates(mapOfDosNDelegates);
        packagePricingRow.setCommissionPercent(commissionPercent);
        return packagePricingRow;
    }

    private List<GroupEvaluationOnBooksGroupDetail> getOnBooksGroupDetails(Integer numberOfBlocks) {
        GroupEvaluationOnBooksGroupDetail onBooksGroupDetail = new GroupEvaluationOnBooksGroupDetail();
        onBooksGroupDetail.setOccupancyDate(new LocalDate());
        GroupEvaluationOnBooksGroupRoomClassDetail roomClassDetail = new GroupEvaluationOnBooksGroupRoomClassDetail();
        roomClassDetail.setRoomsSold(BigDecimal.valueOf(numberOfBlocks));
        onBooksGroupDetail.setRoomClassDetailList(singletonList(roomClassDetail));
        return singletonList(onBooksGroupDetail);
    }

    private List<GroupBlockMaster> getGroupBlockMastersList(Integer blocks) {
        GroupBlockMaster groupBlockMaster = new GroupBlockMaster();
        groupBlockMaster.setName("Group_Test_001");
        groupBlockMaster.setCode("282");
        GroupBlockDetail groupBlockDetail = new GroupBlockDetail();
        groupBlockDetail.setBlocks(blocks);
        groupBlockDetail.setOccupancyDate(new LocalDate());
        groupBlockMaster.setGroupBlockDetails(singletonList(groupBlockDetail));
        MarketSegmentSummary marketSegmentSummary = new MarketSegmentSummary();
        marketSegmentSummary.setCode("Tour");
        groupBlockMaster.setMarketSegment(marketSegmentSummary);
        return singletonList(groupBlockMaster);
    }

    private void assertGroupEvaluationUiWrapperData(GroupEvaluationUiWrapper groupEvaluationUiWrapper) {
        assertNotNull(groupEvaluationUiWrapper);
        assertEquals(groupEvaluationUiWrapper.getSearchGroupName(), "Group_Test_001");
        assertEquals(groupEvaluationUiWrapper.getSearchMktSeg(), "Tour");
        assertEquals(1, groupEvaluationUiWrapper.getOnBooksDetailsList().size());
    }

    @Test
    void testGeneratePackageDisplayNameWithNonGuestRoomType() {
        String packageName = "Full Day";
        String packageCategory = EvaluationConstants.NON_GUEST_ROOM_TYPE;
        String packageDisplayName = presenter.generatePackageDisplayName(packageName, packageCategory);
        assertEquals(packageName, packageDisplayName);
    }

    @Test
    void testGeneratePackageDisplayNameWithGuestRoomType() {
        String packageName = "Full Board";
        String packageCategory = EvaluationConstants.DOUBLE_OCCUPANCY;
        String packageDisplayName = presenter.generatePackageDisplayName(packageName, packageCategory);
        assertEquals(packageName + " - " + UiUtils.getText(EvaluationConstants.occupancyKey + packageCategory), packageDisplayName);
    }

    @Test
    void shouldReturnPackageRateWithTaxIncludedWhenShouldIncludeTaxAndIsSystemFlagIsTrue() {
        GroupEvaluationGroupPricingPackageRevenueByArrivalDate groupPricingPackageRevenueByArrivalDate = new GroupEvaluationGroupPricingPackageRevenueByArrivalDate();
        groupPricingPackageRevenueByArrivalDate.setPackageRate(BigDecimal.TEN);

        String packageRate = presenter.calculatePackageRate(groupPricingPackageRevenueByArrivalDate, true, true);

        assertEquals("10.00", packageRate);
    }

    @Test
    void shouldReturnAdjustedPackageRateWithTaxIncludedWhenShouldIncludeTaxIsTrueAndIsSystemFlagIsFalse() {
        GroupEvaluationGroupPricingPackageRevenueByArrivalDate groupPricingPackageRevenueByArrivalDate = new GroupEvaluationGroupPricingPackageRevenueByArrivalDate();
        groupPricingPackageRevenueByArrivalDate.setUserAdjustmentPackageRate(BigDecimal.TEN);

        String packageRate = presenter.calculatePackageRate(groupPricingPackageRevenueByArrivalDate, false, true);

        assertEquals("10.00", packageRate);
    }

    @Test
    void shouldReturnPackageRateWhenShouldIncludeTaxIsFalseAndIsSystemFlagIsTrue() {
        GroupEvaluation groupEvaluation = buildGroupEvaluationWithGroupPricingPackageResult();
        groupEvaluation.setTaxRate(null);
        GroupEvaluationGroupPricingPackageRevenueByArrivalDate groupPricingPackageRevenueByArrivalDate = groupEvaluation.getGroupEvaluationArrivalDates().iterator().next()
                .getGroupEvaluationGroupPricingPackageRevenueByArrivalDates().get(0);

        String packageRate = presenter.calculatePackageRate(groupPricingPackageRevenueByArrivalDate, true, false);

        assertEquals("175.50", packageRate);
    }

    @Test
    void shouldReturnPackageRateAfterGrpEvlTaxDeductionWhenShouldIncludeTaxIsFalseAndIsSystemFlagIsTrue() {
        GroupEvaluation groupEvaluation = buildGroupEvaluationWithGroupPricingPackageResult();
        GroupEvaluationGroupPricingPackageRevenueByArrivalDate groupPricingPackageRevenueByArrivalDate = groupEvaluation.getGroupEvaluationArrivalDates().iterator().next()
                .getGroupEvaluationGroupPricingPackageRevenueByArrivalDates().get(0);

        String packageRate = presenter.calculatePackageRate(groupPricingPackageRevenueByArrivalDate, true, false);

        assertEquals("165.50", packageRate);
    }

    @Test
    void shouldReturnPackageRateAfterGrpEvlTaxDeductionWhenShouldIncludeTaxIsFalseAndIsSystemFlagIsFalse() {
        GroupEvaluation groupEvaluation = buildGroupEvaluationWithGroupPricingPackageResult();
        GroupEvaluationArrivalDate groupEvaluationArrivalDate = groupEvaluation.getGroupEvaluationArrivalDates().iterator().next();
        groupEvaluationArrivalDate.setUserAdjustedRoomRate(BigDecimal.valueOf(120));
        GroupEvaluationGroupPricingPackageRevenueByArrivalDate groupPricingPackageRevenueByArrivalDate = groupEvaluationArrivalDate
                .getGroupEvaluationGroupPricingPackageRevenueByArrivalDates().get(0);

        String packageRate = presenter.calculatePackageRate(groupPricingPackageRevenueByArrivalDate, false, false);

        assertEquals("174.59", packageRate);
    }

    @Test
    void shouldReturnPackageRateForGrpEvlWithNullGuestRoomRateWhenShouldIncludeTaxIsFalseAndIsSystemFlagIsFalse() {
        GroupEvaluation groupEvaluation = buildGroupEvaluationWithGroupPricingPackageResult();
        GroupEvaluationArrivalDate groupEvaluationArrivalDate = groupEvaluation.getGroupEvaluationArrivalDates().iterator().next();
        groupEvaluationArrivalDate.setSuggestedRate(null);
        GroupEvaluationGroupPricingPackageRevenueByArrivalDate groupPricingPackageRevenueByArrivalDate = groupEvaluationArrivalDate
                .getGroupEvaluationGroupPricingPackageRevenueByArrivalDates().get(0);

        String packageRate = presenter.calculatePackageRate(groupPricingPackageRevenueByArrivalDate, false, false);

        assertEquals("65.50", packageRate);
    }

    @Test
    void shouldReturnPackageRateAfterSeasonalTaxDeductionWhenShouldIncludeTaxIsFalseAndIsSystemFlagIsTrue() {
        GroupEvaluation groupEvaluation = buildGroupEvaluationWithGroupPricingPackageResult();
        groupEvaluation.setTaxRate(null);
        GroupEvaluationArrivalDate groupEvaluationArrivalDate = groupEvaluation.getGroupEvaluationArrivalDates().iterator().next();
        LocalDate today = LocalDate.now();
        groupEvaluationArrivalDate.setTax(getTax(today.minusDays(10), today.plusDays(10), BigDecimal.TEN));
        GroupEvaluationGroupPricingPackageRevenueByArrivalDate groupPricingPackageRevenueByArrivalDate = groupEvaluationArrivalDate
                .getGroupEvaluationGroupPricingPackageRevenueByArrivalDates().get(0);

        String packageRate = presenter.calculatePackageRate(groupPricingPackageRevenueByArrivalDate, true, false);

        assertEquals("165.50", packageRate);
    }

    public static GroupEvaluation buildGroupEvaluationWithGroupPricingPackageResult() {
        GroupEvaluation groupEvaluation = new GroupEvaluation();
        groupEvaluation.setPropertyId(PacmanWorkContextHelper.getPropertyId());
        groupEvaluation.setTaxRate(BigDecimal.valueOf(10));

        GroupEvaluationArrivalDate groupEvaluationArrivalDate = new GroupEvaluationArrivalDate();
        groupEvaluation.addGroupEvaluationArrivalDate(groupEvaluationArrivalDate);
        groupEvaluationArrivalDate.setPreferredDate(true);
        groupEvaluationArrivalDate.setSuggestedRate(BigDecimal.valueOf(110));

        GroupPricingEvalPackagePricing groupPricingEvalPackagePricing = new GroupPricingEvalPackagePricing();
        groupPricingEvalPackagePricing.setPackagePricingDOSList(Set.of(new GroupPricingEvalPackagePricingDOS(1, 2),
                new GroupPricingEvalPackagePricingDOS(2, 8)));
        GroupEvaluationGroupPricingPackageRevenueByRevenueGroup revenueByRevenueGroup1 = new GroupEvaluationGroupPricingPackageRevenueByRevenueGroup();
        revenueByRevenueGroup1.setRevenueWithoutTax(BigDecimal.valueOf(22.00));
        GroupEvaluationGroupPricingPackageRevenueByRevenueGroup revenueByRevenueGroup2 = new GroupEvaluationGroupPricingPackageRevenueByRevenueGroup();
        revenueByRevenueGroup2.setRevenueWithoutTax(BigDecimal.valueOf(43.50));
        GroupEvaluationGroupPricingPackageRevenueByArrivalDate groupPricingPackageRevenueByArrivalDate = new GroupEvaluationGroupPricingPackageRevenueByArrivalDate();
        GroupEvaluationGroupPricingPackageDetail groupPricingPackageDetail = new GroupEvaluationGroupPricingPackageDetail();
        groupPricingPackageDetail.setGuestRoomIncluded(true);
        groupPricingPackageDetail.setPackageCategory(SINGLE_OCCUPANCY);
        groupPricingPackageDetail.addGroupEvaluationGroupPricingPackageRevenueByArrivalDate(groupPricingPackageRevenueByArrivalDate);
        groupPricingPackageDetail.setGroupPricingEvalPackagePricing(groupPricingEvalPackagePricing);
        groupPricingPackageDetail.setRevenueByRevenueGroups(Set.of(revenueByRevenueGroup1, revenueByRevenueGroup2));
        groupEvaluationArrivalDate.addGroupEvaluationGroupPricingPackageRevenueByArrivalDate(groupPricingPackageRevenueByArrivalDate);
        groupEvaluation.addGroupEvaluationGroupPricingPackageDetail(groupPricingPackageDetail);

        return groupEvaluation;
    }

    @Test
    void testGeneratePackageNameToPerDayPriceMap() {
        List<GroupEvaluationFunctionSpaceArrivalDatePackage> packages = generateGroupEvalFSArrivalDatePackageList();
        Map<String, BigDecimal> packageNameToPerDayPriceMap = presenter.generatePackageNameToPerDayPriceMap(packages);
        assertEquals(packages.size(), packageNameToPerDayPriceMap.size());
    }

    private List<GroupEvaluationFunctionSpaceArrivalDatePackage> generateGroupEvalFSArrivalDatePackageList() {
        List<GroupEvaluationFunctionSpaceArrivalDatePackage> groupEvalFSArrivalDatePackageList = new LinkedList<>();
        groupEvalFSArrivalDatePackageList.add(generateGroupEvalFSArrivalDatePackageObject("Full Day", "", BigDecimal.valueOf(105)));
        groupEvalFSArrivalDatePackageList.add(generateGroupEvalFSArrivalDatePackageObject("Full Board", EvaluationConstants.SINGLE_OCCUPANCY, BigDecimal.valueOf(200)));
        groupEvalFSArrivalDatePackageList.add(generateGroupEvalFSArrivalDatePackageObject("Full Board", EvaluationConstants.DOUBLE_OCCUPANCY, BigDecimal.valueOf(300)));
        groupEvalFSArrivalDatePackageList.add(generateGroupEvalFSArrivalDatePackageObject("Lunch Mtng", "", BigDecimal.valueOf(50)));
        groupEvalFSArrivalDatePackageList.add(generateGroupEvalFSArrivalDatePackageObject("Dinner", "", BigDecimal.valueOf(42)));
        return groupEvalFSArrivalDatePackageList;

    }

    private GroupEvaluationFunctionSpaceArrivalDatePackage generateGroupEvalFSArrivalDatePackageObject(String packageName, String packageCategory, BigDecimal perAttendeePerdayPrice) {
        GroupEvaluationFunctionSpaceArrivalDatePackage groupEvalFSArrivalDatePackage = new GroupEvaluationFunctionSpaceArrivalDatePackage();
        groupEvalFSArrivalDatePackage.setPackageRate(perAttendeePerdayPrice);
        GroupEvaluationFunctionSpacePackageDetail groupEvalFSPackageDetail = new GroupEvaluationFunctionSpacePackageDetail();
        groupEvalFSPackageDetail.setPackageName(packageName);
        groupEvalFSPackageDetail.setPackageCategory(packageCategory);
        groupEvalFSArrivalDatePackage.setGroupEvaluationFunctionSpacePackageDetail(groupEvalFSPackageDetail);
        return groupEvalFSArrivalDatePackage;
    }

    @Test
    void getAllFunctionSpacePackageRevenueGroups() {
        FunctionSpaceRevenueGroup functionSpaceRevenueGroup1 = buildFunctionSpaceRevenueGroup("fsgroup1");
        FunctionSpaceRevenueGroup functionSpaceRevenueGroup2 = buildFunctionSpaceRevenueGroup("fsgroup2");
        FunctionSpaceRevenueGroup functionSpaceRevenueGroup3 = buildFunctionSpaceRevenueGroup("fsgroup3");
        Mockito.when(functionSpacePackageService.getActiveRevenueGroups()).thenReturn(Arrays.asList(functionSpaceRevenueGroup1,functionSpaceRevenueGroup2,functionSpaceRevenueGroup3));

        List<FunctionSpaceRevenueGroup> revenueGroups = presenter.getAllFunctionSpacePackageRevenueGroups();
        Assertions.assertEquals(3, revenueGroups.size());
        Assertions.assertTrue(revenueGroups.contains(functionSpaceRevenueGroup1));
        Assertions.assertTrue(revenueGroups.contains(functionSpaceRevenueGroup2));
        Assertions.assertTrue(revenueGroups.contains(functionSpaceRevenueGroup3));
    }

    private FunctionSpaceRevenueGroup buildFunctionSpaceRevenueGroup(String revenueGroupName) {
        FunctionSpaceRevenueGroup functionSpaceRevenueGroup = new FunctionSpaceRevenueGroup();
        functionSpaceRevenueGroup.setName(revenueGroupName);
        functionSpaceRevenueGroup.setAbbreviation(revenueGroupName);
        functionSpaceRevenueGroup.setPropertyId(5);
        FunctionSpaceResourceType resourceType = createFunctionSpaceResourceType(RENTAL_RESOURCE_TYPE);
        functionSpaceRevenueGroup.setResourceType(resourceType);
        return functionSpaceRevenueGroup;
    }

    private FunctionSpaceResourceType createFunctionSpaceResourceType(String resourceTypeName) {
        FunctionSpaceResourceType resourceType = new FunctionSpaceResourceType();
        resourceType.setPropertyId(5);
        resourceType.setName(resourceTypeName);
        resourceType.setId(2);
        return resourceType;
    }

    @Test
    void shouldGeneratePackageRevenueGroupNameListForFunctionSpace() {
        FunctionSpaceRevenueGroup functionSpaceRevenueGroup1 = buildFunctionSpaceRevenueGroup("fsgroup1");
        FunctionSpaceRevenueGroup functionSpaceRevenueGroup2 = buildFunctionSpaceRevenueGroup("fsgroup2");
        FunctionSpaceRevenueGroup functionSpaceRevenueGroup3 = buildFunctionSpaceRevenueGroup("fsgroup3");
        List<FunctionSpaceRevenueGroup> fsRevGroupList = Arrays.asList(functionSpaceRevenueGroup1,functionSpaceRevenueGroup2,functionSpaceRevenueGroup3);
        when(functionSpacePackageService.getActiveRevenueGroups()).thenReturn(fsRevGroupList);
        when(groupEvaluationInputWrapper.isGroupPricing()).thenReturn(false);

        List<String> revenueGroupNameList = presenter.generatePackageRevenueGroupNameList();
        Assertions.assertEquals(3, revenueGroupNameList.size());
        int index = 0;
        for (FunctionSpaceRevenueGroup revGroup : fsRevGroupList) {
            Assertions.assertTrue(revGroup.getName().equals(revenueGroupNameList.get(index++)));
        }
    }

    @Test
    void shouldGeneratePackageRevenueGroupNameListForGroupPricing() {
        RevenueGroupDto revGroupDto1 = new RevenueGroupDto();
        revGroupDto1.setName("revGroup1");
        RevenueGroupDto revGroupDto2 = new RevenueGroupDto();
        revGroupDto2.setName("revGroup2");
        RevenueGroupDto revGroupDto3 = new RevenueGroupDto();
        revGroupDto3.setName("revGroup3");
        when(groupPricingPackageService.getRevenueGroupDtoSortedByName()).thenReturn(Arrays.asList(revGroupDto1, revGroupDto2, revGroupDto3));
        when(groupEvaluationInputWrapper.isGroupPricing()).thenReturn(true);

        var result = presenter.generatePackageRevenueGroupNameList();
        assertEquals(3, result.size());
        assertEquals("revGroup1", result.get(0));
        assertEquals("revGroup2", result.get(1));
        assertEquals("revGroup3", result.get(2));
    }

    @Test
    void getTaxRateTest() {
        LocalDate today = LocalDate.now();
        Tax defaultTax = getTax(null, null, BigDecimal.ONE);
        Tax seasonalTax = getTax(today.minusDays(10), today.plusDays(10), BigDecimal.TEN);

        List<Tax> tax = Arrays.asList(defaultTax, seasonalTax);
        List<List<Tax>> seasonTaxList = new ArrayList();
        seasonTaxList.add(tax);
        when(multiPropertyCrudService.findByNamedQuery(Arrays.asList(1), Tax.TAX_FOR_A_DATE, QueryParameter.with("occupancyDate", today).parameters())).thenReturn(seasonTaxList);

        List<List<Tax>> defaultTaxList = new ArrayList();
        defaultTaxList.add(Arrays.asList(defaultTax));
        when(multiPropertyCrudService.findByNamedQuery(Arrays.asList(2), Tax.TAX_FOR_A_DATE, QueryParameter.with("occupancyDate", today.plusDays(30)).parameters())).thenReturn(defaultTaxList);

        when(taxService.findTaxFor(today, 1)).thenReturn(seasonalTax);
        when(taxService.findTaxFor(today.plusDays(30), 2)).thenReturn(defaultTax);

        assertEquals(seasonalTax, presenter.getTax(today, 1));
        assertEquals(seasonalTax.getRoomTaxRate(), presenter.getTax(today, 1).getRoomTaxRate());
        assertEquals(defaultTax, presenter.getTax(today.plusDays(30), 2));
    }

    @Test
    void checkIfSeasonalTaxIsPresentTest() {
        LocalDate today = LocalDate.now();
        Tax defaultTax = getTax(null, null, BigDecimal.ONE);
        Tax seasonalTax = getTax(today.minusDays(10), today.plusDays(10), BigDecimal.TEN);

        assertEquals(false, presenter.checkIfSeasonalTaxIsPresent(defaultTax));
        assertEquals(true, presenter.checkIfSeasonalTaxIsPresent(seasonalTax));

    }

    private Tax getTax(LocalDate startDate, LocalDate endDate, BigDecimal taxRate) {
        Tax seasonalTax = new Tax();
        seasonalTax.setRoomTaxRate(taxRate);
        seasonalTax.setStartDate(startDate);
        seasonalTax.setEndDate(endDate);
        seasonalTax.setSeasonName("Tax Season");
        return seasonalTax;
    }

    @Test
    void hasSpecialEvents_true() {
        //Given
        LocalDate date = LocalDate.fromDateFields(new Date());
        List<SpecialEventSummaryDto> specialEventInstance = new ArrayList<>();
        SpecialEventSummaryDto eventInstance = new SpecialEventSummaryDto();
        eventInstance.setEventName("Diwali");
        eventInstance.setStartDate(DateParameter.fromDate(date.toDate()));
        eventInstance.setEndDate(DateParameter.fromDate(date.toDate()));
        specialEventInstance.add(eventInstance);

        //When
        when(specialEventService.findSpecialEventSummaryDtos(date)).thenReturn(specialEventInstance);

        //Then
        assertTrue(presenter.hasSpecialEvents(date));
    }

    @Test
    void hasSpecialEvents_false() {
        //Given
        LocalDate date = LocalDate.fromDateFields(new Date());
        List<SpecialEventSummaryDto> specialEventInstance = new ArrayList<>();

        //When
        when(specialEventService.findSpecialEventSummaryDtos(date)).thenReturn(specialEventInstance);

        //Then
        assertFalse(presenter.hasSpecialEvents(date));
    }

    @Test
    void urlRedirectIsValid_happyPath() {
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.SALES_AND_CATERING_URL_REDIRECT_ENABLED)).thenReturn(true);
        GroupEvaluation groupEvaluation = new GroupEvaluation();
        groupEvaluation.setCallbackUrl("https://salesAndCatering.qa.com");
        assertTrue(presenter.urlRedirectIsValid(groupEvaluation));
    }

    @Test
    void urlRedirectIsValid_groupEvaluationIsNull() {
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.SALES_AND_CATERING_URL_REDIRECT_ENABLED)).thenReturn(true);
        assertFalse(presenter.urlRedirectIsValid(null));
    }

    @Test
    void urlRedirectIsValid_RedirectIsDisabled() {
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.SALES_AND_CATERING_URL_REDIRECT_ENABLED)).thenReturn(false);
        GroupEvaluation groupEvaluation = new GroupEvaluation();
        groupEvaluation.setCallbackUrl("https://salesAndCatering.qa.com");
        assertFalse(presenter.urlRedirectIsValid(groupEvaluation));
    }

    @Test
    void urlRedirectIsValid_CallbackUrlIsAbsent() {
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.SALES_AND_CATERING_URL_REDIRECT_ENABLED)).thenReturn(true);
        assertFalse(presenter.urlRedirectIsValid(new GroupEvaluation()));
    }

    @Test
    void redirectCancelToCallbackUrl() {
        GroupEvaluation groupEvaluation = new GroupEvaluation();
        groupEvaluation.setEvaluationRequestId("123456");
        groupEvaluation.setCallbackUrl("https://salesAndCatering.qa.com");
        when(groupEvaluationInputWrapper.getGroupEvaluation()).thenReturn(groupEvaluation);
        presenter.setGroupEvaluationInputWrapper(groupEvaluationInputWrapper);
        presenter.redirectCancelToCallbackUrl();
        ArgumentCaptor<String> captor = ArgumentCaptor.forClass(String.class);
        verify(presenter).redirectPage(captor.capture());
        assertEquals(captor.getValue(), "https://salesAndCatering.qa.com?ratecallstatus=failed&Statusconstant=500&EvaluationID=123456");
    }

    @Test
    void getTotalEffectiveAccomClassCapacityTest() {
        AccomType accomType = getAccomType("room1", 1);
        AccomClass ac = new AccomClass();
        ac.setId(1);
        accomType.setAccomClass(ac);
        when(groupEvaluationService.getTotalRoomClassCapacityForOccupancyDate(1, new Date(2023, 10, 10))).thenReturn(new BigDecimal(50));
        assertEquals(50, presenter.getTotalEffectiveAccomClassCapacity(accomType, new Date(2023, 10, 10)));
    }

    @Test
    void showTotalAccomClassCapacityForRCEvaluationTest() {
        when(pacmanConfigParamsService.getBooleanParameterValue(GUIConfigParamName.IS_SHOW_ACCOM_CLASS_CAPACITY_FOR_RC_EVAL)).thenReturn(true);
        assertTrue(presenter.showTotalAccomClassCapacityForRCEvaluation());
        when(pacmanConfigParamsService.getBooleanParameterValue(GUIConfigParamName.IS_SHOW_ACCOM_CLASS_CAPACITY_FOR_RC_EVAL)).thenReturn(false);
        assertFalse(presenter.showTotalAccomClassCapacityForRCEvaluation());
    }

    @Test
    void testIsRestrictROHTORCEnabled() {
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.RESTRICT_ROH_TO_RC_EVALUATION_METHOD)).thenReturn(true);
        assertTrue(presenter.isRestrictROHtoRCEvaluationMethod());
    }

    @Test
    void createParentUIWrapperForCurrentGroupPricingPackageWithTax() {
        var result = presenter.createParentUIWrapperForCurrentGroupPricingPackage(buildArrivalDatePackages().subList(0, 2), "Package 1", true);
        assertEquals("Package 1", result.getPackageName());
        assertEquals(NON_GUEST_ROOM_TYPE, result.getPackageCategory());
        assertEquals("Package 1", result.getPackageDisplayName());
        assertEquals(80, result.getTotalDelegates());
        assertEquals(BigDecimal.valueOf(3400), result.getTotalProfit());
        assertEquals(BigDecimal.valueOf(2700), result.getGuestRoomRevenue());
        assertEquals(BigDecimal.valueOf(19600), result.getTotalRevenue());
        assertTrue(result.isParentPackage());
        assertFalse(result.isChildPackage());
    }

    @Test
    void createParentUIWrapperForCurrentGroupPricingPackageWithoutTax() {
        var result = presenter.createParentUIWrapperForCurrentGroupPricingPackage(buildArrivalDatePackages().subList(0, 2), "Package 1", false);
        assertEquals("Package 1", result.getPackageName());
        assertEquals(NON_GUEST_ROOM_TYPE, result.getPackageCategory());
        assertEquals("Package 1", result.getPackageDisplayName());
        assertEquals(80, result.getTotalDelegates());
        assertEquals(BigDecimal.valueOf(3400), result.getTotalProfit());
        assertEquals(BigDecimal.valueOf(2547.17), result.getGuestRoomRevenue());
        assertEquals(BigDecimal.valueOf(18490.57), result.getTotalRevenue());
        assertTrue(result.isParentPackage());
        assertFalse(result.isChildPackage());
    }

    @Test
    void createParentUIWrapperConfBanqNameToPackageRevenueMapWithTax() {
        var result = presenter.createParentUIWrapperConfBanqNameToPackageRevenueMap(buildArrivalDatePackages().subList(0, 2), true);

        assertEquals(2, result.size());
        assertEquals(new BigDecimal(8600), result.get("Revenue Group 1"));
        assertEquals(new BigDecimal(11000), result.get("Revenue Group 2"));
    }

    @Test
    void createParentUIWrapperConfBanqNameToPackageRevenueMapWithoutTax() {
        var result = presenter.createParentUIWrapperConfBanqNameToPackageRevenueMap(buildArrivalDatePackages().subList(0, 2), false);

        assertEquals(2, result.size());
        assertEquals(new BigDecimal(7500), result.get("Revenue Group 1"));
        assertEquals(new BigDecimal(10200), result.get("Revenue Group 2"));
    }

    @Test
    void createDayOfStayToDelegateDetailSet() {
        Set<GroupEvaluationFunctionSpaceDayOfStayToDelegateDetailResultUIWrapper> resultSet = presenter.createDayOfStayToDelegateDetailSet(buildArrivalDatePackages().get(0));

        assertEquals(3, resultSet.size());
        Iterator<GroupEvaluationFunctionSpaceDayOfStayToDelegateDetailResultUIWrapper> resultSetIterator = resultSet.iterator();
        var result = resultSetIterator.next();
        assertEquals(LocalDate.now().toDate(), result.getDate());
        assertEquals(10, result.getNumberOfDelegates());
        result = resultSetIterator.next();
        assertEquals(LocalDate.now().plusDays(1).toDate(), result.getDate());
        assertEquals(20, result.getNumberOfDelegates());
        result = resultSetIterator.next();
        assertEquals(LocalDate.now().plusDays(2).toDate(), result.getDate());
        assertEquals(20, result.getNumberOfDelegates());
    }

    @Test
    void shouldCreateConfBanqNameToPackageRevenueMapForTaxInclusiveRates() {
        var result = presenter.createConfBanqNameToPackageRevenueMap(buildArrivalDatePackages().get(0).getGroupEvaluationGroupPricingPackageDetail().getRevenueByRevenueGroups(), true);
        assertEquals(2, result.size());
        assertEquals(BigDecimal.valueOf(5000), result.get("Revenue Group 1"));
        assertEquals(BigDecimal.valueOf(6500), result.get("Revenue Group 2"));
    }

    @Test
    void shouldCreateConfBanqNameToPackageRevenueMapForTaxExclusiveRates() {
        var result = presenter.createConfBanqNameToPackageRevenueMap(buildArrivalDatePackages().get(0).getGroupEvaluationGroupPricingPackageDetail().getRevenueByRevenueGroups(), false);
        assertEquals(2, result.size());
        assertEquals(BigDecimal.valueOf(4500), result.get("Revenue Group 1"));
        assertEquals(BigDecimal.valueOf(6000), result.get("Revenue Group 2"));
    }

    @Test
    void shouldReturnGroupPricingConfigurationConferenceAndBanquetDtosWithoutRentalStreams() {
        when(conferenceAndBanquetService.getConferenceAndBanquets()).thenReturn(List.of(
                buildConferenceBanquetDto("F&B", 10.10, false),
                buildConferenceBanquetDto("AV Services", 15.50, false),
                buildConferenceBanquetDto("FS Rental", 20.00, true)
        ));

        List<ConferenceAndBanquetDto> conferenceAndBanquetDtos = presenter.getGroupPricingConfigurationConferenceAndBanquets(5);

        assertEquals(3, conferenceAndBanquetDtos.size());
        assertEquals("F&B", conferenceAndBanquetDtos.get(0).getRevenueStream());
        assertEquals(BigDecimal.valueOf(10.10), conferenceAndBanquetDtos.get(0).getProfitPercentage());
        assertEquals("AV Services", conferenceAndBanquetDtos.get(1).getRevenueStream());
        assertEquals(BigDecimal.valueOf(15.50), conferenceAndBanquetDtos.get(1).getProfitPercentage());
        assertEquals("FS Rental", conferenceAndBanquetDtos.get(2).getRevenueStream());
        assertEquals(BigDecimal.valueOf(20.00), conferenceAndBanquetDtos.get(2).getProfitPercentage());
    }

    @Test
    void shouldReturnEmptyGroupPricingConfigurationConferenceAndBanquetDtosWhenNoGroupPricingRevenueStreamsExistForProperty() {
        when(conferenceAndBanquetService.getConferenceAndBanquets()).thenReturn(Collections.emptyList());

        List<ConferenceAndBanquetDto> conferenceAndBanquetDtos = presenter.getGroupPricingConfigurationConferenceAndBanquets(5);

        assertEquals(0, conferenceAndBanquetDtos.size());
    }

    private GroupPricingConfigurationConferenceAndBanquet buildGroupPricingConfigurationConferenceAndBanquet(String revenueStreamName,
                                                                                                             BigDecimal profitPercentage) {
        GroupPricingConfigurationConferenceAndBanquet request = new GroupPricingConfigurationConferenceAndBanquet();
        request.setRevenueStream(revenueStreamName);
        request.setProfitPercentage(profitPercentage);
        return request;
    }

    private ConferenceAndBanquetDto buildConferenceBanquetDto(String name, double profit, boolean isRental) {
        ConferenceAndBanquetDto dto = new ConferenceAndBanquetDto();
        dto.setRevenueStream(name);
        dto.setProfitPercentage(BigDecimal.valueOf(profit));
        dto.setRental(isRental);
        dto.setStatusId(1);
        return dto;
    }

    private List<GroupEvaluationGroupPricingPackageRevenueByArrivalDate> buildArrivalDatePackages() {
        GroupEvaluationGroupPricingPackageRevenueByArrivalDate arrivalDatePackage1 = new GroupEvaluationGroupPricingPackageRevenueByArrivalDate();
        arrivalDatePackage1.setPackageRate(BigDecimal.valueOf(230));
        arrivalDatePackage1.setTotalRevenue(BigDecimal.valueOf(11500));
        arrivalDatePackage1.setTotalProfit(BigDecimal.valueOf(2000));
        arrivalDatePackage1.setGuestRoomRevenue(BigDecimal.valueOf(1500));
        GroupEvaluation groupEvaluation = new GroupEvaluation();
        groupEvaluation.setTaxRate(BigDecimal.valueOf(6));
        GroupEvaluationArrivalDate groupEvaluationArrivalDate1 = new GroupEvaluationArrivalDate();
        groupEvaluationArrivalDate1.setArrivalDate(LocalDate.now());
        groupEvaluationArrivalDate1.setGroupEvaluation(groupEvaluation);
        arrivalDatePackage1.setGroupEvaluationArrivalDate(groupEvaluationArrivalDate1);
        GroupEvaluationGroupPricingPackageDetail pkgDetail1 = new GroupEvaluationGroupPricingPackageDetail();
        pkgDetail1.setPackageName("Package 1");
        pkgDetail1.setPackageCategory(SINGLE_OCCUPANCY);
        GroupPricingEvalPackagePricing pkgPricing1 = new GroupPricingEvalPackagePricing();
        pkgPricing1.setPackagePricingDOSList(new LinkedHashSet<>(Arrays.asList(
                new GroupPricingEvalPackagePricingDOS(1, 10),
                new GroupPricingEvalPackagePricingDOS(2, 20),
                new GroupPricingEvalPackagePricingDOS(3, 20))));
        pkgDetail1.setGroupPricingEvalPackagePricing(pkgPricing1);
        pkgDetail1.setRevenueByRevenueGroups(new LinkedHashSet<>(Arrays.asList(
                createPackageRevenueGroup("Revenue Group 1", BigDecimal.valueOf(100), BigDecimal.valueOf(90), pkgDetail1),
                createPackageRevenueGroup("Revenue Group 2", BigDecimal.valueOf(130), BigDecimal.valueOf(120), pkgDetail1))));
        arrivalDatePackage1.setGroupEvaluationGroupPricingPackageDetail(pkgDetail1);

        GroupEvaluationGroupPricingPackageRevenueByArrivalDate arrivalDatePackage2 = new GroupEvaluationGroupPricingPackageRevenueByArrivalDate();
        arrivalDatePackage2.setPackageRate(BigDecimal.valueOf(270));
        arrivalDatePackage2.setTotalRevenue(BigDecimal.valueOf(8100));
        arrivalDatePackage2.setTotalProfit(BigDecimal.valueOf(1400));
        arrivalDatePackage2.setGuestRoomRevenue(BigDecimal.valueOf(1200));
        arrivalDatePackage2.setGroupEvaluationArrivalDate(groupEvaluationArrivalDate1);
        GroupEvaluationGroupPricingPackageDetail pkgDetail2 = new GroupEvaluationGroupPricingPackageDetail();
        pkgDetail2.setPackageName("Package 1");
        pkgDetail2.setPackageCategory(DOUBLE_OCCUPANCY);
        GroupPricingEvalPackagePricing pkgPricing2 = new GroupPricingEvalPackagePricing();
        pkgPricing2.setPackagePricingDOSList(new LinkedHashSet<>(Arrays.asList(
                new GroupPricingEvalPackagePricingDOS(1, 10),
                new GroupPricingEvalPackagePricingDOS(2, 10),
                new GroupPricingEvalPackagePricingDOS(3, 10))));
        pkgDetail2.setGroupPricingEvalPackagePricing(pkgPricing2);
        pkgDetail2.setRevenueByRevenueGroups(new LinkedHashSet<>(Arrays.asList(
                createPackageRevenueGroup("Revenue Group 1", BigDecimal.valueOf(120), BigDecimal.valueOf(100), pkgDetail2),
                createPackageRevenueGroup("Revenue Group 2", BigDecimal.valueOf(150), BigDecimal.valueOf(140), pkgDetail2))));
        arrivalDatePackage2.setGroupEvaluationGroupPricingPackageDetail(pkgDetail2);

        GroupEvaluationGroupPricingPackageRevenueByArrivalDate arrivalDatePackage3 = new GroupEvaluationGroupPricingPackageRevenueByArrivalDate();
        arrivalDatePackage3.setPackageRate(BigDecimal.valueOf(220));
        arrivalDatePackage3.setTotalRevenue(BigDecimal.valueOf(6600));
        arrivalDatePackage3.setGroupEvaluationArrivalDate(groupEvaluationArrivalDate1);
        GroupEvaluationGroupPricingPackageDetail pkgDetail3 = new GroupEvaluationGroupPricingPackageDetail();
        pkgDetail3.setPackageName("Package 2");
        pkgDetail3.setPackageCategory(NON_GUEST_ROOM_TYPE);
        GroupPricingEvalPackagePricing pkgPricing3 = new GroupPricingEvalPackagePricing();
        pkgPricing3.setPackagePricingDOSList(new LinkedHashSet<>(Arrays.asList(
                new GroupPricingEvalPackagePricingDOS(1, 10),
                new GroupPricingEvalPackagePricingDOS(2, 20),
                new GroupPricingEvalPackagePricingDOS(3, 0))));
        pkgDetail3.setGroupPricingEvalPackagePricing(pkgPricing3);
        pkgDetail3.setRevenueByRevenueGroups(new LinkedHashSet<>(Arrays.asList(
                createPackageRevenueGroup("Revenue Group 2", BigDecimal.valueOf(70), BigDecimal.valueOf(50), pkgDetail3),
                createPackageRevenueGroup("Revenue Group 3", BigDecimal.valueOf(150), BigDecimal.valueOf(135), pkgDetail3))));
        arrivalDatePackage3.setGroupEvaluationGroupPricingPackageDetail(pkgDetail3);

        return List.of(arrivalDatePackage1, arrivalDatePackage2, arrivalDatePackage3);
    }

    private GroupEvaluationGroupPricingPackageRevenueByRevenueGroup createPackageRevenueGroup(
            String name, BigDecimal revenue, BigDecimal revenueWithoutTax, GroupEvaluationGroupPricingPackageDetail packageDetail) {
        GroupEvaluationGroupPricingPackageRevenueByRevenueGroup revGrp = new GroupEvaluationGroupPricingPackageRevenueByRevenueGroup();
        revGrp.setName(name);
        revGrp.setRevenue(revenue);
        revGrp.setRevenueWithoutTax(revenueWithoutTax);
        revGrp.setGroupEvaluationGroupPricingPackageDetail(packageDetail);
        return revGrp;
    }

    private PackageDto convertToFunctionSpacePackageDto(FunctionSpacePackage functionSpacePackage) {
        PackageDto functionSpacePackageDto = new PackageDto();
        functionSpacePackageDto.setId(functionSpacePackage.getId());
        functionSpacePackageDto.setName(functionSpacePackage.getName());
        functionSpacePackageDto.setPackageType(functionSpacePackage.getPackageType());
        functionSpacePackageDto.setGuestRoomIncluded(functionSpacePackage.isGuestRoomIncluded());
        functionSpacePackageDto.setStatus(functionSpacePackage.getStatus());
        return functionSpacePackageDto;
    }

    private PackageDto convertToGroupPricingPackageDto(GroupPricingConfigurationPackage groupPricingPackage) {
        PackageDto packageDto = new PackageDto();
        packageDto.setId(groupPricingPackage.getId());
        packageDto.setName(groupPricingPackage.getName());
        packageDto.setPackageType(groupPricingPackage.getPackageType());
        packageDto.setGuestRoomIncluded(groupPricingPackage.isGuestRoomIncluded());
        packageDto.setStatus(groupPricingPackage.getStatus());
        return packageDto;
    }

    @Test
    public void checkDependentFunctionSpacesIncludeInPackageTestForSingleRoom() {
        List<FunctionSpaceUiWrapper> list = new ArrayList<>();
        FunctionSpaceUiWrapper checkedWrapper = getFunctionSpaceUiWrapper();
        checkedWrapper.getGroupEvaluationFunctionSpace().setRentalIncludedInPackage(false);
        list.add(checkedWrapper);

        FunctionSpaceUiWrapper wrapperA = getFunctionSpaceUiWrapper();
        list.add(wrapperA);

        FunctionSpaceUiWrapper wrapperB = getFunctionSpaceUiWrapper();
        wrapperB.getGroupEvaluationFunctionSpace().getGroupEvaluationFunctionSpaceFunctionRooms().iterator().next().getFunctionSpaceFunctionRoom().setId(2);
        wrapperB.getGroupEvaluationFunctionSpace().getGroupEvaluationFunctionSpaceFunctionRooms().iterator().next().getFunctionSpaceFunctionRoom().setName("Vaibhav");
        list.add(wrapperB);

        presenter.checkDependentFunctionSpaceForIncludeInPackage(checkedWrapper, list);
        assertFalse(list.get(0).getGroupEvaluationFunctionSpace().getRentalIncludedInPackage());
        assertFalse(list.get(1).getGroupEvaluationFunctionSpace().getRentalIncludedInPackage());
        assertTrue(list.get(2).getGroupEvaluationFunctionSpace().getRentalIncludedInPackage());
    }

    @Test
    public void checkDependentFunctionSpacesIncludeInPackageTestForCombinationRooms() {
        List<FunctionSpaceUiWrapper> list = new ArrayList<>();
        FunctionSpaceUiWrapper checkedWrapper = getFunctionSpaceUiWrapper();
        checkedWrapper.getGroupEvaluationFunctionSpace().setRentalIncludedInPackage(false);
        list.add(checkedWrapper);

        FunctionSpaceUiWrapper commaSeperatedRoomAB = getFunctionSpaceUiWrapper();
        commaSeperatedRoomAB.getGroupEvaluationFunctionSpace().getGroupEvaluationFunctionSpaceFunctionRooms().iterator().next().getFunctionSpaceFunctionRoom().setName("Room B");
        commaSeperatedRoomAB.getGroupEvaluationFunctionSpace().getGroupEvaluationFunctionSpaceFunctionRooms().iterator().next().getFunctionSpaceFunctionRoom().setId(2);
        commaSeperatedRoomAB.getGroupEvaluationFunctionSpace().getGroupEvaluationFunctionSpaceFunctionRooms().add(createGroupEvaluationFunctionSpaceFunctionRoom());
        list.add(commaSeperatedRoomAB);

        FunctionSpaceUiWrapper wrapperB = getFunctionSpaceUiWrapper();
        wrapperB.getGroupEvaluationFunctionSpace().getGroupEvaluationFunctionSpaceFunctionRooms().iterator().next().getFunctionSpaceFunctionRoom().setName("Room B");
        wrapperB.getGroupEvaluationFunctionSpace().getGroupEvaluationFunctionSpaceFunctionRooms().iterator().next().getFunctionSpaceFunctionRoom().setId(2);
        list.add(wrapperB);

        FunctionSpaceUiWrapper wrapperC = getFunctionSpaceUiWrapper();
        wrapperC.getGroupEvaluationFunctionSpace().getGroupEvaluationFunctionSpaceFunctionRooms().iterator().next().getFunctionSpaceFunctionRoom().setName("Room C");
        wrapperC.getGroupEvaluationFunctionSpace().getGroupEvaluationFunctionSpaceFunctionRooms().iterator().next().getFunctionSpaceFunctionRoom().setId(3);
        list.add(wrapperC);

        presenter.checkDependentFunctionSpaceForIncludeInPackage(checkedWrapper, list);

        assertFalse(list.get(0).getGroupEvaluationFunctionSpace().getRentalIncludedInPackage());
        assertFalse(list.get(1).getGroupEvaluationFunctionSpace().getRentalIncludedInPackage());
        assertFalse(list.get(2).getGroupEvaluationFunctionSpace().getRentalIncludedInPackage());
        assertTrue(list.get(3).getGroupEvaluationFunctionSpace().getRentalIncludedInPackage());


    }

    @Test
    public void checkDependentFunctionSpacesIncludeInPackageTestForCommaSeperatedRooms() {
        List<FunctionSpaceUiWrapper> list = new ArrayList<>();
        FunctionSpaceUiWrapper checkedWrapper = getFunctionSpaceUiWrapper();
        checkedWrapper.getGroupEvaluationFunctionSpace().setRentalIncludedInPackage(false);
        list.add(checkedWrapper);

        GroupEvaluationFunctionSpaceFunctionRoom newGrpEvalFunctionSpace = createGroupEvaluationFunctionSpaceFunctionRoom();

        FunctionSpaceFunctionRoom comboRoom = createCombinationRoom();
        comboRoom.setCombo(true);
        newGrpEvalFunctionSpace.setFunctionSpaceFunctionRoom(comboRoom);
        FunctionSpaceUiWrapper combinationFunctionSpace = getFunctionSpaceUiWrapper();
        combinationFunctionSpace.getGroupEvaluationFunctionSpace().getGroupEvaluationFunctionSpaceFunctionRooms().clear();
        combinationFunctionSpace.getGroupEvaluationFunctionSpace().getGroupEvaluationFunctionSpaceFunctionRooms().add(newGrpEvalFunctionSpace);
        list.add(combinationFunctionSpace);

        presenter.checkDependentFunctionSpaceForIncludeInPackage(checkedWrapper, list);

        assertFalse(list.get(0).getGroupEvaluationFunctionSpace().getRentalIncludedInPackage());
        assertFalse(list.get(1).getGroupEvaluationFunctionSpace().getRentalIncludedInPackage());

    }

    @Test
    void shouldReturnGroupEvaluationConferenceAndBanquetDtoListByPropertyWhenUseFSRevenueStreamsIsDisabled() {
        GroupPricingConfigurationConferenceAndBanquet foodAndBeverageRevenueStream = buildGroupPricingConfigurationConferenceAndBanquet("F&B", new BigDecimal("10.20"));
        GroupPricingConfigurationConferenceAndBanquet avRevenueStream = buildGroupPricingConfigurationConferenceAndBanquet("AV", new BigDecimal("10.20"));
        GroupEvaluationMulti groupEvaluationMulti = new GroupEvaluationMulti();
        List<GroupEvaluation> groupEvaluations = getGroupEvaluationList();
        groupEvaluations.get(0).setGroupEvaluationConferenceAndBanquets(Set.of(buildGroupEvaluationConferenceAndBanquet(1, foodAndBeverageRevenueStream),
                buildGroupEvaluationConferenceAndBanquet(2, avRevenueStream)));
        groupEvaluationMulti.setGroupEvaluations(groupEvaluations);
        GroupEvaluationUiWrapper parameterGroupEvaluationWrapper = FunctionSpaceEvaluationTestData.getParameterGroupEvaluationWrapper();
        parameterGroupEvaluationWrapper.addGroupPricingConfAndBanqByProperty(groupEvaluations.get(0).getPropertyId(), groupEvaluations.get(0).getGroupEvaluationConferenceAndBanquets());

        Map<Property, List<GroupEvaluationConferenceAndBanquetDto>> groupEvaluationConferenceBanquetDtosByProperty =
                presenter.buildGroupEvaluationConferenceAndBanquetDtoListByProperty(parameterGroupEvaluationWrapper);

        assertEquals(1, groupEvaluationConferenceBanquetDtosByProperty.size());
        assertNotNull(groupEvaluationConferenceBanquetDtosByProperty.get(GroupEvaluationUiWrapper.SINGLE_PROPERTY));
        List<GroupEvaluationConferenceAndBanquetDto> groupEvaluationConferenceBanquetDtos =
                groupEvaluationConferenceBanquetDtosByProperty.get(GroupEvaluationUiWrapper.SINGLE_PROPERTY);
        groupEvaluationConferenceBanquetDtos.sort(Comparator.comparing(GroupEvaluationConferenceAndBanquetDto::getId));
        assertEquals(1, groupEvaluationConferenceBanquetDtos.get(0).getId());
        assertEquals(foodAndBeverageRevenueStream.getRevenueStream(), groupEvaluationConferenceBanquetDtos.get(0).getConferenceAndBanquet().getRevenueStream());
        assertEquals(0, new BigDecimal(100).compareTo(groupEvaluationConferenceBanquetDtos.get(0).getRevenue()));
        assertEquals(0, foodAndBeverageRevenueStream.getProfitPercentage().compareTo(groupEvaluationConferenceBanquetDtos.get(0).getProfitPercentage()));
        assertEquals(0, new BigDecimal(5).compareTo(groupEvaluationConferenceBanquetDtos.get(0).getCommissionPercentage()));
        assertEquals(2, groupEvaluationConferenceBanquetDtos.get(1).getId());
        assertEquals(avRevenueStream.getRevenueStream(), groupEvaluationConferenceBanquetDtos.get(1).getConferenceAndBanquet().getRevenueStream());
        assertEquals(0, new BigDecimal(100).compareTo(groupEvaluationConferenceBanquetDtos.get(1).getRevenue()));
        assertEquals(0, avRevenueStream.getProfitPercentage().compareTo(groupEvaluationConferenceBanquetDtos.get(1).getProfitPercentage()));
        assertEquals(0, new BigDecimal(5).compareTo(groupEvaluationConferenceBanquetDtos.get(1).getCommissionPercentage()));
    }

    @Test
    public void shouldBuildCommonEntityForConfAndBanquetDuringEvaluationWhenUseFSRevenueStreamsForPropertyIsONForMultipleProperties() {
        //GIVEN
        Map<Property, List<GroupEvaluationConferenceAndBanquetDto>> conferenceAndBanquetDtosByProperty = new HashMap<>();
        List<Property> propertyList = getPropertyList();
        conferenceAndBanquetDtosByProperty.put(propertyList.get(0),
                Arrays.asList(createGroupEvaluationConferenceAndBanquetDto(new BigDecimal("200.00"), new BigDecimal("10.00"), new BigDecimal("50.00"))));
        conferenceAndBanquetDtosByProperty.put(propertyList.get(1),
                Arrays.asList(createGroupEvaluationConferenceAndBanquetDto(new BigDecimal("300.00"), new BigDecimal("15.00"), new BigDecimal("50.00"))));
        when(conferenceAndBanquetService.shouldUseFSRevenueStreamsForProperty(5)).thenReturn(true);
        when(conferenceAndBanquetService.shouldUseFSRevenueStreamsForProperty(6)).thenReturn(true);
        when(functionSpaceConfigurationService.findFunctionSpaceResourceType(RENTAL_RESOURCE_TYPE)).thenReturn(createFunctionSpaceResourceType(RENTAL_RESOURCE_TYPE));
        when(functionSpaceConfigurationService.findFunctionSpaceResourceType(OTHER_RESOURCE_TYPE)).thenReturn(createFunctionSpaceResourceType(OTHER_RESOURCE_TYPE));
        //WHEN
        Map<Property, List<GroupEvaluationFunctionSpaceConfAndBanq>> groupEvaluationConferenceAndBanquets = presenter.buildGroupEvaluationFunctionSpaceConfBanqListByProperty(conferenceAndBanquetDtosByProperty);
        //THEN
        assertEquals(new BigDecimal("0.50000"), groupEvaluationConferenceAndBanquets.get(propertyList.get(0)).get(0).getFunctionSpaceRevenueGroup().getProfitPercent());
        assertEquals(new BigDecimal("200.00"), groupEvaluationConferenceAndBanquets.get(propertyList.get(0)).get(0).getRevenue());
        assertEquals(new BigDecimal("0.10000"), groupEvaluationConferenceAndBanquets.get(propertyList.get(0)).get(0).getCommissionPercentage());
        assertEquals(new BigDecimal("90.00"), groupEvaluationConferenceAndBanquets.get(propertyList.get(0)).get(0).getProfit());

        assertEquals(new BigDecimal("0.50000"), groupEvaluationConferenceAndBanquets.get(propertyList.get(1)).get(0).getFunctionSpaceRevenueGroup().getProfitPercent());
        assertEquals(new BigDecimal("300.00"), groupEvaluationConferenceAndBanquets.get(propertyList.get(1)).get(0).getRevenue());
        assertEquals(new BigDecimal("0.15000"), groupEvaluationConferenceAndBanquets.get(propertyList.get(1)).get(0).getCommissionPercentage());
        assertEquals(new BigDecimal("127.50"), groupEvaluationConferenceAndBanquets.get(propertyList.get(1)).get(0).getProfit());
    }

    @Test
    public void shouldBuildCommonEntityForConfAndBanquetDuringEvaluationWhenUseFSRevenueStreamsForPropertyIsONForOnePropertyButNotBuildForAnotherPropertyWhichIsOff() {
        //GIVEN
        Map<Property, List<GroupEvaluationConferenceAndBanquetDto>> conferenceAndBanquetDtosByProperty = new HashMap<>();
        List<Property> propertyList = getPropertyList();
        conferenceAndBanquetDtosByProperty.put(propertyList.get(0),
                Arrays.asList(createGroupEvaluationConferenceAndBanquetDto(new BigDecimal("200.00"), new BigDecimal("10.00"), new BigDecimal("50.00"))));
        conferenceAndBanquetDtosByProperty.put(propertyList.get(1),
                Arrays.asList(createGroupEvaluationConferenceAndBanquetDto(new BigDecimal("300.00"), new BigDecimal("15.00"), new BigDecimal("50.00"))));
        when(conferenceAndBanquetService.shouldUseFSRevenueStreamsForProperty(5)).thenReturn(true);
        when(conferenceAndBanquetService.shouldUseFSRevenueStreamsForProperty(6)).thenReturn(false);
        when(functionSpaceConfigurationService.findFunctionSpaceResourceType(RENTAL_RESOURCE_TYPE)).thenReturn(createFunctionSpaceResourceType(RENTAL_RESOURCE_TYPE));
        when(functionSpaceConfigurationService.findFunctionSpaceResourceType(OTHER_RESOURCE_TYPE)).thenReturn(createFunctionSpaceResourceType(OTHER_RESOURCE_TYPE));
        //WHEN
        Map<Property, List<GroupEvaluationFunctionSpaceConfAndBanq>> groupEvaluationConferenceAndBanquets = presenter.buildGroupEvaluationFunctionSpaceConfBanqListByProperty(conferenceAndBanquetDtosByProperty);
        //THEN
        assertEquals(1, groupEvaluationConferenceAndBanquets.size());
        assertEquals(new BigDecimal("0.50000"), groupEvaluationConferenceAndBanquets.get(propertyList.get(0)).get(0).getFunctionSpaceRevenueGroup().getProfitPercent());
        assertEquals(new BigDecimal("200.00"), groupEvaluationConferenceAndBanquets.get(propertyList.get(0)).get(0).getRevenue());
        assertEquals(new BigDecimal("0.10000"), groupEvaluationConferenceAndBanquets.get(propertyList.get(0)).get(0).getCommissionPercentage());
        assertEquals(new BigDecimal("90.00"), groupEvaluationConferenceAndBanquets.get(propertyList.get(0)).get(0).getProfit());
    }

    @Test
    void shouldReturnGroupEvaluationConferenceAndBanquetDtoListByPropertyWhenUseFSRevenueStreamsIsEnabled() {
        FunctionSpaceRevenueGroup foodAndBeverageRevenueStream = buildFunctionSpaceRevenueGroup("F&B");
        foodAndBeverageRevenueStream.setProfitPercent(new BigDecimal("0.09"));
        FunctionSpaceRevenueGroup avRevenueStream = buildFunctionSpaceRevenueGroup("AV");
        avRevenueStream.setProfitPercent(new BigDecimal("0.21"));
        GroupEvaluationMulti groupEvaluationMulti = new GroupEvaluationMulti();
        List<GroupEvaluation> groupEvaluations = getGroupEvaluationList();
        GroupEvaluationFunctionSpaceConfAndBanq foodAndBeverageEvaluationConfBanq = getGroupEvalFunctionSpaceConfBanq(100.00, 0.2, foodAndBeverageRevenueStream);
        foodAndBeverageEvaluationConfBanq.setId(1);
        foodAndBeverageEvaluationConfBanq.setProfitPercentage(BigDecimalUtil.multiply(foodAndBeverageRevenueStream.getProfitPercent(), BigDecimalUtil.ONE_HUNDRED));
        GroupEvaluationFunctionSpaceConfAndBanq avEvaluationConfBanq = getGroupEvalFunctionSpaceConfBanq(100.00, 0.1, avRevenueStream);
        avEvaluationConfBanq.setId(2);
        avEvaluationConfBanq.setProfitPercentage(BigDecimalUtil.multiply(avRevenueStream.getProfitPercent(), BigDecimalUtil.ONE_HUNDRED));
        groupEvaluations.get(0).setGroupEvaluationFunctionSpaceConfAndBanquets(Set.of(foodAndBeverageEvaluationConfBanq, avEvaluationConfBanq));
        groupEvaluationMulti.setGroupEvaluations(groupEvaluations);
        GroupEvaluationUiWrapper parameterGroupEvaluationWrapper = FunctionSpaceEvaluationTestData.getParameterGroupEvaluationWrapper();
        parameterGroupEvaluationWrapper.addGroupEvaluationConfAndBanqByProperty(groupEvaluations.get(0).getPropertyId(), groupEvaluations.get(0).getGroupEvaluationFunctionSpaceConfAndBanquets());
        when(conferenceAndBanquetService.shouldUseFSRevenueStreamsForProperty(-1)).thenReturn(true);

        Map<Property, List<GroupEvaluationConferenceAndBanquetDto>> groupEvaluationConferenceBanquetDtosByProperty =
                presenter.buildGroupEvaluationConferenceAndBanquetDtoListByProperty(parameterGroupEvaluationWrapper);

        assertEquals(1, groupEvaluationConferenceBanquetDtosByProperty.size());
        assertNotNull(groupEvaluationConferenceBanquetDtosByProperty.get(GroupEvaluationUiWrapper.SINGLE_PROPERTY));
        List<GroupEvaluationConferenceAndBanquetDto> groupEvaluationConferenceBanquetDtos =
                groupEvaluationConferenceBanquetDtosByProperty.get(GroupEvaluationUiWrapper.SINGLE_PROPERTY);
        groupEvaluationConferenceBanquetDtos.sort(Comparator.comparing(GroupEvaluationConferenceAndBanquetDto::getId));
        assertEquals(1, groupEvaluationConferenceBanquetDtos.get(0).getId());
        assertEquals(foodAndBeverageRevenueStream.getName(), groupEvaluationConferenceBanquetDtos.get(0).getConferenceAndBanquet().getRevenueStream());
        assertEquals(0, new BigDecimal(100).compareTo(groupEvaluationConferenceBanquetDtos.get(0).getRevenue()));
        assertTrue(BigDecimalUtil.equals(BigDecimalUtil.multiply(foodAndBeverageRevenueStream.getProfitPercent(), BigDecimalUtil.ONE_HUNDRED), groupEvaluationConferenceBanquetDtos.get(0).getProfitPercentage()));
        assertEquals(0, new BigDecimal(20).compareTo(groupEvaluationConferenceBanquetDtos.get(0).getCommissionPercentage()));
        assertEquals(2, groupEvaluationConferenceBanquetDtos.get(1).getId());
        assertEquals(avRevenueStream.getName(), groupEvaluationConferenceBanquetDtos.get(1).getConferenceAndBanquet().getRevenueStream());
        assertEquals(0, new BigDecimal(100).compareTo(groupEvaluationConferenceBanquetDtos.get(1).getRevenue()));
        assertTrue(BigDecimalUtil.equals(BigDecimalUtil.multiply(avRevenueStream.getProfitPercent(), BigDecimalUtil.ONE_HUNDRED), groupEvaluationConferenceBanquetDtos.get(1).getProfitPercentage()));
        assertEquals(0, new BigDecimal(10).compareTo(groupEvaluationConferenceBanquetDtos.get(1).getCommissionPercentage()));
    }

    @Test
    void shouldReturnGroupEvaluationConferenceAndBanquetDtoListByPropertyWhenUseFSRevenueStreamsIsEnabledForOnePropertyAndDisabledForAnother() {
        //Revenue Streams
        FunctionSpaceRevenueGroup foodAndBeverageRevenueStream = buildFunctionSpaceRevenueGroup("F&B");
        foodAndBeverageRevenueStream.setProfitPercent(new BigDecimal("0.09"));
        FunctionSpaceRevenueGroup avRevenueStream = buildFunctionSpaceRevenueGroup("AV");
        avRevenueStream.setProfitPercent(new BigDecimal("0.21"));

        GroupEvaluationMulti groupEvaluationMulti = new GroupEvaluationMulti();
        //Set conference and banquets revenue streams for FS in group evaluation 1
        GroupEvaluation groupEvaluation1 = getGroupEvaluation("Group1", 1, 5);
        GroupEvaluationFunctionSpaceConfAndBanq foodAndBeverageEvaluationConfBanq = getGroupEvalFunctionSpaceConfBanq(100.00, 0.2, foodAndBeverageRevenueStream);
        foodAndBeverageEvaluationConfBanq.setId(1);
        foodAndBeverageEvaluationConfBanq.setProfitPercentage(BigDecimalUtil.multiply(foodAndBeverageRevenueStream.getProfitPercent(), BigDecimalUtil.ONE_HUNDRED));
        GroupEvaluationFunctionSpaceConfAndBanq avEvaluationConfBanq = getGroupEvalFunctionSpaceConfBanq(100.00, 0.1, avRevenueStream);
        avEvaluationConfBanq.setId(2);
        avEvaluationConfBanq.setProfitPercentage(BigDecimalUtil.multiply(avRevenueStream.getProfitPercent(), BigDecimalUtil.ONE_HUNDRED));
        groupEvaluation1.setGroupEvaluationFunctionSpaceConfAndBanquets(Set.of(foodAndBeverageEvaluationConfBanq, avEvaluationConfBanq));
        //Set conference and banquets revenue streams for GP in group evaluation 2
        GroupEvaluation groupEvaluation2 = getGroupEvaluation("Group2", 2, 6);
        GroupPricingConfigurationConferenceAndBanquet foodAndBeverageRevenueStreamGp = buildGroupPricingConfigurationConferenceAndBanquet("F&B", new BigDecimal("10.20"));
        GroupPricingConfigurationConferenceAndBanquet avRevenueStreamGp = buildGroupPricingConfigurationConferenceAndBanquet("AV", new BigDecimal("10.20"));
        groupEvaluation2.setGroupEvaluationConferenceAndBanquets(Set.of(buildGroupEvaluationConferenceAndBanquet(1, foodAndBeverageRevenueStreamGp),
                buildGroupEvaluationConferenceAndBanquet(2, avRevenueStreamGp)));

        List<GroupEvaluation> groupEvaluations = Arrays.asList(groupEvaluation1, groupEvaluation2);
        groupEvaluationMulti.setGroupEvaluations(groupEvaluations);
        GroupEvaluationUiWrapper parameterGroupEvaluationWrapper = FunctionSpaceEvaluationTestData.getParameterGroupEvaluationWrapper();
        List<PropertyUiWrapper> authorisedProperties = new ArrayList<>();
        authorisedProperties.add(new PropertyUiWrapper(new Property(5), BigDecimal.TEN));
        authorisedProperties.add(new PropertyUiWrapper(new Property(6), BigDecimal.TEN));
        parameterGroupEvaluationWrapper.setAuthorizedPropertiesForPropertyGroup(authorisedProperties);
        parameterGroupEvaluationWrapper.addGroupEvaluationConfAndBanqByProperty(5, groupEvaluations.get(0).getGroupEvaluationFunctionSpaceConfAndBanquets());
        parameterGroupEvaluationWrapper.addGroupPricingConfAndBanqByProperty(6, groupEvaluations.get(1).getGroupEvaluationConferenceAndBanquets());
        when(conferenceAndBanquetService.shouldUseFSRevenueStreamsForProperty(5)).thenReturn(true);
        when(conferenceAndBanquetService.shouldUseFSRevenueStreamsForProperty(6)).thenReturn(false);

        Map<Property, List<GroupEvaluationConferenceAndBanquetDto>> groupEvaluationConferenceBanquetDtosByProperty =
                presenter.buildGroupEvaluationConferenceAndBanquetDtoListByProperty(parameterGroupEvaluationWrapper);

        assertEquals(2, groupEvaluationConferenceBanquetDtosByProperty.size());
        //Assert for Function Space Property 5
        assertNotNull(groupEvaluationConferenceBanquetDtosByProperty.get(new Property(5)));
        List<GroupEvaluationConferenceAndBanquetDto> groupEvaluationConferenceBanquetDtos =
                groupEvaluationConferenceBanquetDtosByProperty.get(new Property(5));
        groupEvaluationConferenceBanquetDtos.sort(Comparator.comparing(GroupEvaluationConferenceAndBanquetDto::getId));
        assertEquals(1, groupEvaluationConferenceBanquetDtos.get(0).getId());
        assertEquals(foodAndBeverageRevenueStream.getName(), groupEvaluationConferenceBanquetDtos.get(0).getConferenceAndBanquet().getRevenueStream());
        assertEquals(0, new BigDecimal(100).compareTo(groupEvaluationConferenceBanquetDtos.get(0).getRevenue()));
        assertTrue(BigDecimalUtil.equals(BigDecimalUtil.multiply(foodAndBeverageRevenueStream.getProfitPercent(), BigDecimalUtil.ONE_HUNDRED), groupEvaluationConferenceBanquetDtos.get(0).getProfitPercentage()));
        assertEquals(0, new BigDecimal(20).compareTo(groupEvaluationConferenceBanquetDtos.get(0).getCommissionPercentage()));
        assertEquals(2, groupEvaluationConferenceBanquetDtos.get(1).getId());
        assertEquals(avRevenueStream.getName(), groupEvaluationConferenceBanquetDtos.get(1).getConferenceAndBanquet().getRevenueStream());
        assertEquals(0, new BigDecimal(100).compareTo(groupEvaluationConferenceBanquetDtos.get(1).getRevenue()));
        assertTrue(BigDecimalUtil.equals(BigDecimalUtil.multiply(avRevenueStream.getProfitPercent(), BigDecimalUtil.ONE_HUNDRED), groupEvaluationConferenceBanquetDtos.get(1).getProfitPercentage()));
        assertEquals(0, new BigDecimal(10).compareTo(groupEvaluationConferenceBanquetDtos.get(1).getCommissionPercentage()));
        //Assert for Function Space Property 6
        assertNotNull(groupEvaluationConferenceBanquetDtosByProperty.get(new Property(6)));
        List<GroupEvaluationConferenceAndBanquetDto> groupEvaluationConferenceBanquetDtosProperty6 =
                groupEvaluationConferenceBanquetDtosByProperty.get(new Property(6));
        groupEvaluationConferenceBanquetDtosProperty6.sort(Comparator.comparing(GroupEvaluationConferenceAndBanquetDto::getId));
        assertEquals(1, groupEvaluationConferenceBanquetDtosProperty6.get(0).getId());
        assertEquals("F&B", groupEvaluationConferenceBanquetDtosProperty6.get(0).getConferenceAndBanquet().getRevenueStream());
        assertEquals(0, new BigDecimal(100).compareTo(groupEvaluationConferenceBanquetDtosProperty6.get(0).getRevenue()));
        assertEquals(new BigDecimal("10.20"), groupEvaluationConferenceBanquetDtosProperty6.get(0).getProfitPercentage());
        assertEquals(0, new BigDecimal(5).compareTo(groupEvaluationConferenceBanquetDtosProperty6.get(0).getCommissionPercentage()));
        assertEquals(2, groupEvaluationConferenceBanquetDtosProperty6.get(1).getId());
        assertEquals("AV", groupEvaluationConferenceBanquetDtosProperty6.get(1).getConferenceAndBanquet().getRevenueStream());
        assertEquals(0, new BigDecimal(100).compareTo(groupEvaluationConferenceBanquetDtosProperty6.get(1).getRevenue()));
        assertEquals(new BigDecimal("10.20"), groupEvaluationConferenceBanquetDtosProperty6.get(1).getProfitPercentage());
        assertEquals(0, new BigDecimal(5).compareTo(groupEvaluationConferenceBanquetDtosProperty6.get(1).getCommissionPercentage()));
    }

    @Test
    void shouldAddGroupEvaluationConferenceAndBanquetWhenUseFSRevenueStreamsIsEnabled() {
        FunctionSpaceRevenueGroup foodAndBeverageRevenueStream = buildFunctionSpaceRevenueGroup("F&B");
        foodAndBeverageRevenueStream.setProfitPercent(new BigDecimal("0.09"));
        FunctionSpaceRevenueGroup avRevenueStream = buildFunctionSpaceRevenueGroup("AV");
        avRevenueStream.setProfitPercent(new BigDecimal("0.21"));
        GroupEvaluationMulti groupEvaluationMulti = new GroupEvaluationMulti();
        List<GroupEvaluation> groupEvaluations = getGroupEvaluationList();
        GroupEvaluationFunctionSpaceConfAndBanq foodAndBeverageEvaluationConfBanq = getGroupEvalFunctionSpaceConfBanq(100.00, 0.2, foodAndBeverageRevenueStream);
        foodAndBeverageEvaluationConfBanq.setId(1);
        GroupEvaluationFunctionSpaceConfAndBanq avEvaluationConfBanq = getGroupEvalFunctionSpaceConfBanq(100.00, 0.1, avRevenueStream);
        avEvaluationConfBanq.setId(2);
        groupEvaluations.get(0).setGroupEvaluationFunctionSpaceConfAndBanquets(Set.of(foodAndBeverageEvaluationConfBanq, avEvaluationConfBanq));
        groupEvaluationMulti.setGroupEvaluations(groupEvaluations);
        presenter.parametersGroupEvaluationWrapper = FunctionSpaceEvaluationTestData.getParameterGroupEvaluationWrapper();
        presenter.parametersGroupEvaluationWrapper.getGroupEvaluation().setGroupEvaluationFunctionSpaceConfAndBanquets(Set.of(foodAndBeverageEvaluationConfBanq, avEvaluationConfBanq));
        when(conferenceAndBanquetService.shouldUseFSRevenueStreams()).thenReturn(true);

        presenter.addGroupEvaluationConfBanq(presenter.parametersGroupEvaluationWrapper);

        Map<Property, List<GroupEvaluationFunctionSpaceConfAndBanq>> multiPropertyGroupConfAndBanq = presenter.parametersGroupEvaluationWrapper.getMultiPropertyGroupEvaluationConfAndBanq();
        assertEquals(1, multiPropertyGroupConfAndBanq.size());
        assertNotNull(multiPropertyGroupConfAndBanq.get(GroupEvaluationUiWrapper.SINGLE_PROPERTY));
        List<GroupEvaluationFunctionSpaceConfAndBanq> groupEvaluationConferenceBanquets =
                multiPropertyGroupConfAndBanq.get(GroupEvaluationUiWrapper.SINGLE_PROPERTY);
        groupEvaluationConferenceBanquets.sort(Comparator.comparing(GroupEvaluationFunctionSpaceConfAndBanq::getId));
        assertEquals(1, groupEvaluationConferenceBanquets.get(0).getId());
        assertEquals(foodAndBeverageRevenueStream.getName(), groupEvaluationConferenceBanquets.get(0).getFunctionSpaceRevenueGroup().getName());
        assertEquals(0, new BigDecimal(100).compareTo(groupEvaluationConferenceBanquets.get(0).getRevenue()));
        assertEquals(foodAndBeverageRevenueStream, groupEvaluationConferenceBanquets.get(0).getFunctionSpaceRevenueGroup());
        assertEquals(2, groupEvaluationConferenceBanquets.get(1).getId());
        assertTrue(BigDecimalUtil.equals(new BigDecimal("0.2"), groupEvaluationConferenceBanquets.get(0).getCommissionPercentage()));
        assertEquals(avRevenueStream.getName(), groupEvaluationConferenceBanquets.get(1).getFunctionSpaceRevenueGroup().getName());
        assertEquals(0, new BigDecimal(100).compareTo(groupEvaluationConferenceBanquets.get(1).getRevenue()));
        assertEquals(avRevenueStream, groupEvaluationConferenceBanquets.get(1).getFunctionSpaceRevenueGroup());
        assertTrue(BigDecimalUtil.equals(new BigDecimal("0.1"), groupEvaluationConferenceBanquets.get(1).getCommissionPercentage()));
    }

    @Test
    void shouldAddGroupEvaluationConferenceAndBanquetWhenUseFSRevenueStreamsIsDisabled() {
        GroupPricingConfigurationConferenceAndBanquet foodAndBeverageRevenueStream = buildGroupPricingConfigurationConferenceAndBanquet("F&B", new BigDecimal("10.20"));
        GroupPricingConfigurationConferenceAndBanquet avRevenueStream = buildGroupPricingConfigurationConferenceAndBanquet("AV", new BigDecimal("10.20"));
        GroupEvaluationMulti groupEvaluationMulti = new GroupEvaluationMulti();
        List<GroupEvaluation> groupEvaluations = getGroupEvaluationList();
        Set<GroupEvaluationConferenceAndBanquet> groupEvaluationConferenceAndBanquets = Set.of(buildGroupEvaluationConferenceAndBanquet(1, foodAndBeverageRevenueStream),
                buildGroupEvaluationConferenceAndBanquet(2, avRevenueStream));
        groupEvaluations.get(0).setGroupEvaluationConferenceAndBanquets(groupEvaluationConferenceAndBanquets);
        groupEvaluationMulti.setGroupEvaluations(groupEvaluations);
        presenter.parametersGroupEvaluationWrapper = FunctionSpaceEvaluationTestData.getParameterGroupEvaluationWrapper();
        presenter.parametersGroupEvaluationWrapper.getGroupEvaluation().setGroupEvaluationConferenceAndBanquets(groupEvaluationConferenceAndBanquets);
        when(conferenceAndBanquetService.shouldUseFSRevenueStreams()).thenReturn(false);

        presenter.addGroupEvaluationConfBanq(presenter.parametersGroupEvaluationWrapper);

        Map<Property, List<GroupEvaluationConferenceAndBanquet>> groupEvaluationConferenceBanquetsByProperty = presenter.parametersGroupEvaluationWrapper.getMultiPropertyGroupConfAndBanq();
        assertEquals(1, groupEvaluationConferenceBanquetsByProperty.size());
        assertNotNull(groupEvaluationConferenceBanquetsByProperty.get(GroupEvaluationUiWrapper.SINGLE_PROPERTY));
        List<GroupEvaluationConferenceAndBanquet> groupEvaluationConferenceBanquets =
                groupEvaluationConferenceBanquetsByProperty.get(GroupEvaluationUiWrapper.SINGLE_PROPERTY);
        groupEvaluationConferenceBanquets.sort(Comparator.comparing(GroupEvaluationConferenceAndBanquet::getId));
        assertEquals(1, groupEvaluationConferenceBanquets.get(0).getId());
        assertEquals(foodAndBeverageRevenueStream, groupEvaluationConferenceBanquets.get(0).getGroupPricingConfigurationConferenceAndBanquet());
        assertEquals(0, new BigDecimal(100).compareTo(groupEvaluationConferenceBanquets.get(0).getRevenue()));
        assertEquals(0, foodAndBeverageRevenueStream.getProfitPercentage().compareTo(groupEvaluationConferenceBanquets.get(0).getProfitPercentage()));
        assertEquals(0, new BigDecimal(5).compareTo(groupEvaluationConferenceBanquets.get(0).getCommissionPercentage()));
        assertEquals(2, groupEvaluationConferenceBanquets.get(1).getId());
        assertEquals(avRevenueStream, groupEvaluationConferenceBanquets.get(1).getGroupPricingConfigurationConferenceAndBanquet());
        assertEquals(0, new BigDecimal(100).compareTo(groupEvaluationConferenceBanquets.get(1).getRevenue()));
        assertEquals(0, avRevenueStream.getProfitPercentage().compareTo(groupEvaluationConferenceBanquets.get(1).getProfitPercentage()));
        assertEquals(0, new BigDecimal(5).compareTo(groupEvaluationConferenceBanquets.get(1).getCommissionPercentage()));
    }

    @Test
    void shouldSetGroupEvaluationFunctionSpacePackageDetailsInPackageResultUIWrapperWhenToggleToUseFSDataInGroupPricingIsEnabled() {
        GroupEvaluationFunctionSpaceArrivalDatePackage groupEvaluationFunctionSpaceArrivalDatePackage = generateGroupEvalFSArrivalDatePackageObject("package 1", EvaluationConstants.SINGLE_OCCUPANCY, BigDecimal.valueOf(200));
        GroupEvaluation groupEvaluation = new GroupEvaluation();
        groupEvaluation.setTaxRate(BigDecimal.valueOf(6));
        GroupEvaluationArrivalDate groupEvaluationArrivalDate1 = new GroupEvaluationArrivalDate();
        groupEvaluationArrivalDate1.setGroupEvaluation(groupEvaluation);
        groupEvaluationFunctionSpaceArrivalDatePackage.setGroupEvaluationArrivalDate(groupEvaluationArrivalDate1);
        groupEvaluationFunctionSpaceArrivalDatePackage.setPackageRate(BigDecimal.valueOf(230));
        groupEvaluationFunctionSpaceArrivalDatePackage.setTotalRevenue(BigDecimal.valueOf(11500));
        groupEvaluationFunctionSpaceArrivalDatePackage.setTotalProfit(BigDecimal.valueOf(2000));
        groupEvaluationFunctionSpaceArrivalDatePackage.setGuestRoomRevenue(BigDecimal.valueOf(1500));
        groupEvaluationFunctionSpaceArrivalDatePackage.setFunctionSpaceRentalRevenue(BigDecimal.ZERO);
        GroupEvaluationFunctionSpacePackageDayOfStay groupEvaluationFunctionSpacePackageDayOfStay = new GroupEvaluationFunctionSpacePackageDayOfStay();
        groupEvaluationFunctionSpacePackageDayOfStay.setNoOfDelegates(50);
        groupEvaluationFunctionSpacePackageDayOfStay.setDayOfStay(1);
        Set<GroupEvaluationFunctionSpacePackageDayOfStay> groupEvaluationFunctionSpacePackageDayOfStaySet = new HashSet<>();
        groupEvaluationFunctionSpacePackageDayOfStaySet.add(groupEvaluationFunctionSpacePackageDayOfStay);
        FunctionSpacePackageType functionSpacePackageType1 = buildFunctionSpacePackageType("packageType1");
        LocalDateTime now = LocalDateTime.now();
        FunctionSpacePackage functionSpacePackage1 = buildFunctionSpacePackage(1, "package 1", functionSpacePackageType1, now, now);
        GroupEvaluationFunctionSpacePackageDetail groupEvaluationFunctionSpacePackageDetail = buildGroupEvaluationFunctionSpacePackageDetail("package 1",functionSpacePackage1);
        GroupEvaluationFunctionSpacePackageRevenueGroup revenueGroup = new GroupEvaluationFunctionSpacePackageRevenueGroup();
        revenueGroup.setName("Revenue Group 1");
        revenueGroup.setRevenue(BigDecimal.valueOf(100));
        revenueGroup.setRevenueWithoutTax(BigDecimal.valueOf(120));
        Set<GroupEvaluationFunctionSpacePackageRevenueGroup> groupEvaluationFunctionSpacePackageRevenueGroups = new HashSet<>();
        groupEvaluationFunctionSpacePackageRevenueGroups.add(revenueGroup);
        groupEvaluationFunctionSpacePackageDetail.setPackageCategory(SINGLE_OCCUPANCY);
        groupEvaluationFunctionSpacePackageDetail.setGroupEvaluationFunctionSpacePackageRevenueGroups(groupEvaluationFunctionSpacePackageRevenueGroups);
        groupEvaluationFunctionSpacePackageDetail.setGroupEvaluationFunctionSpacePackageDayOfStays(groupEvaluationFunctionSpacePackageDayOfStaySet);
        groupEvaluationFunctionSpaceArrivalDatePackage.setGroupEvaluationFunctionSpacePackageDetail(groupEvaluationFunctionSpacePackageDetail);

        GroupEvaluationGroupPricingPackageRevenueByArrivalDate groupEvaluationGroupPricingPackageRevenueByArrivalDate = buildArrivalDatePackages().get(0);
        GroupEvaluationArrivalDate groupEvaluationArrivalDate = new GroupEvaluationArrivalDate();
        groupEvaluationArrivalDate.setArrivalDate(LocalDate.now());
        groupEvaluationArrivalDate.setGroupEvaluationGroupPricingPackageRevenueByArrivalDates(Collections.singletonList(groupEvaluationGroupPricingPackageRevenueByArrivalDate));
        groupEvaluationArrivalDate.setGroupEvaluationFunctionSpaceArrivalDatePackages(Collections.singletonList(groupEvaluationFunctionSpaceArrivalDatePackage));
        boolean includeTax = true;
        when(conferenceAndBanquetService.shouldUseFSRevenueStreams()).thenReturn(true);
        FunctionSpaceEvaluationBasePresenter spy = Mockito.spy(presenter);
        Mockito.doReturn(false).when(spy).isFunctionSpace();
        Mockito.doReturn(true).when(spy).isGroupPricing();
        Mockito.doReturn(true).when(spy).isGroupPricingPackageEnabled();
        Mockito.doReturn(true).when(spy).isPackageEnabled();

        List<GroupEvaluationFunctionSpacePackageResultUIWrapper> packageResultUIWrapper = spy.generatePackageDetailUIWrapperList(groupEvaluationArrivalDate, includeTax);

        assertEquals("package 1",packageResultUIWrapper.get(0).getPackageName());
        assertEquals(BigDecimal.valueOf(1500),packageResultUIWrapper.get(0).getGuestRoomRevenue());
        assertEquals(NON_GUEST_ROOM_TYPE,packageResultUIWrapper.get(0).getPackageCategory());
        assertTrue(packageResultUIWrapper.get(0).isParentPackage());
        assertEquals("package 1",packageResultUIWrapper.get(1).getPackageName());
        assertEquals(SINGLE_OCCUPANCY, packageResultUIWrapper.get(1).getPackageCategory());
        assertEquals(BigDecimal.valueOf(1500),packageResultUIWrapper.get(1).getGuestRoomRevenue());
        assertEquals(BigDecimal.valueOf(5000),packageResultUIWrapper.get(1).getRevenueGroupNameToRevenue().get("Revenue Group 1"));
        assertEquals(50, packageResultUIWrapper.get(1).getTotalDelegates());
        assertEquals(BigDecimal.valueOf(11500), packageResultUIWrapper.get(1).getTotalRevenue());
        assertEquals(BigDecimal.valueOf(2000), packageResultUIWrapper.get(1).getTotalProfit());
    }

    @Test
    void shouldSetGroupEvaluationGroupPricingPackagesInPackageResultUIWrapperWhenToggleToUseFSDataInGroupPricingIsDisabled() {
        GroupEvaluationGroupPricingPackageRevenueByArrivalDate groupEvaluationGroupPricingPackageRevenueByArrivalDate = buildArrivalDatePackages().get(0);
        GroupEvaluationArrivalDate groupEvaluationArrivalDate = new GroupEvaluationArrivalDate();
        groupEvaluationArrivalDate.setGroupEvaluationGroupPricingPackageRevenueByArrivalDates(Collections.singletonList(groupEvaluationGroupPricingPackageRevenueByArrivalDate));
        boolean includeTax = true;
        when(conferenceAndBanquetService.shouldUseFSRevenueStreams()).thenReturn(false);
        FunctionSpaceEvaluationBasePresenter spy = Mockito.spy(presenter);
        Mockito.doReturn(false).when(spy).isFunctionSpace();
        Mockito.doReturn(true).when(spy).isGroupPricing();
        Mockito.doReturn(true).when(spy).isGroupPricingPackageEnabled();
        Mockito.doReturn(true).when(spy).isPackageEnabled();

        List<GroupEvaluationFunctionSpacePackageResultUIWrapper> packageResultUIWrapper = spy.generatePackageDetailUIWrapperList(groupEvaluationArrivalDate, includeTax);

        assertEquals("Package 1",packageResultUIWrapper.get(0).getPackageName());
        assertEquals(BigDecimal.valueOf(1500),packageResultUIWrapper.get(0).getGuestRoomRevenue());
        assertEquals(NON_GUEST_ROOM_TYPE,packageResultUIWrapper.get(0).getPackageCategory());
        assertTrue(packageResultUIWrapper.get(0).isParentPackage());
        assertEquals("Package 1",packageResultUIWrapper.get(1).getPackageName());
        assertEquals(SINGLE_OCCUPANCY, packageResultUIWrapper.get(1).getPackageCategory());
        assertEquals(BigDecimal.valueOf(1500),packageResultUIWrapper.get(1).getGuestRoomRevenue());
        assertEquals(BigDecimal.valueOf(5000),packageResultUIWrapper.get(1).getRevenueGroupNameToRevenue().get("Revenue Group 1"));
        assertEquals(50, packageResultUIWrapper.get(1).getTotalDelegates());
        assertEquals(BigDecimal.valueOf(11500), packageResultUIWrapper.get(1).getTotalRevenue());
        assertEquals(BigDecimal.valueOf(2000), packageResultUIWrapper.get(1).getTotalProfit());
    }

    @Test
    void shouldReturnTrueWhenPackageAreUsedForFunctionSpaceEvaluation() {
        GroupEvaluation groupEvaluation = mock(GroupEvaluation.class);
        GroupEvaluationArrivalDate groupEvaluationArrivalDate = new GroupEvaluationArrivalDate();
        groupEvaluationArrivalDate.setGroupEvaluation(groupEvaluation);
        groupEvaluationArrivalDate.setGroupEvaluationFunctionSpaceArrivalDatePackages(generateGroupEvalFSArrivalDatePackageList());
        when(groupEvaluation.isNotGroupPricingEvaluation()).thenReturn(true);
        when(presenter.isPackageEnabled()).thenReturn(true);

        assertTrue(presenter.isPackageResultAvailableForFunctionSpaceEvaluation(groupEvaluationArrivalDate));
    }

    @Test
    void shouldReturnFalseWhenPackageFeatureIsDisabled() {
        when(presenter.isPackageEnabled()).thenReturn(false);
        assertFalse(presenter.isPackageResultAvailableForFunctionSpaceEvaluation(new GroupEvaluationArrivalDate()));
    }

    @Test
    void shouldReturnFalseWhenPackageAreUsedForGroupPricingEvaluation() {
        GroupEvaluation groupEvaluation = mock(GroupEvaluation.class);
        GroupEvaluationArrivalDate groupEvaluationArrivalDate = new GroupEvaluationArrivalDate();
        groupEvaluationArrivalDate.setGroupEvaluation(groupEvaluation);
        when(groupEvaluation.isNotGroupPricingEvaluation()).thenReturn(false);
        when(presenter.isPackageEnabled()).thenReturn(true);

        assertFalse(presenter.isPackageResultAvailableForFunctionSpaceEvaluation(groupEvaluationArrivalDate));
    }

    @Test
    void shouldReturnFalseWhenPackagesAreNotIncludedInFunctionSpaceEvaluation() {
        GroupEvaluation groupEvaluation = mock(GroupEvaluation.class);
        GroupEvaluationArrivalDate groupEvaluationArrivalDate = new GroupEvaluationArrivalDate();
        groupEvaluationArrivalDate.setGroupEvaluation(groupEvaluation);
        when(groupEvaluation.isNotGroupPricingEvaluation()).thenReturn(true);
        when(presenter.isPackageEnabled()).thenReturn(true);

        assertFalse(presenter.isPackageResultAvailableForFunctionSpaceEvaluation(groupEvaluationArrivalDate));
    }

    private GroupEvaluationConferenceAndBanquet buildGroupEvaluationConferenceAndBanquet(Integer id,
                                                                                         GroupPricingConfigurationConferenceAndBanquet conferenceAndBanquet) {
        GroupEvaluationConferenceAndBanquet groupEvaluationConferenceAndBanquet = new GroupEvaluationConferenceAndBanquet();
        groupEvaluationConferenceAndBanquet.setId(id);
        groupEvaluationConferenceAndBanquet.setGroupPricingConfigurationConferenceAndBanquet(conferenceAndBanquet);
        groupEvaluationConferenceAndBanquet.setRevenue(new BigDecimal(100));
        groupEvaluationConferenceAndBanquet.setProfitPercentage(conferenceAndBanquet.getProfitPercentage());
        groupEvaluationConferenceAndBanquet.setCommissionPercentage(new BigDecimal(5));
        return groupEvaluationConferenceAndBanquet;
    }

    private FunctionSpaceCombinationFunctionRoom createCombinationRoom() {
        FunctionSpaceCombinationFunctionRoom functionSpaceCombinationFunctionRoom = new FunctionSpaceCombinationFunctionRoom();
        functionSpaceCombinationFunctionRoom.setCombo(true);
        functionSpaceCombinationFunctionRoom.setName("Room A/Room B");
        functionSpaceCombinationFunctionRoom.setId(10);
        FunctionSpaceFunctionRoom roomA = createFunctionSpaceFunctionRoom();
        roomA.setIncludeForPricing(true);
        FunctionSpaceFunctionRoom roomB = createFunctionSpaceFunctionRoom();
        roomB.setName("Room B");
        roomB.setIncludeForPricing(true);
        roomB.setId(2);
        List<FunctionSpaceFunctionRoom> rooms = new ArrayList<>();
        rooms.add(roomA);
        rooms.add(roomB);
        functionSpaceCombinationFunctionRoom.setIndivisibleFunctionRoomParts(rooms);
        return functionSpaceCombinationFunctionRoom;
    }

    private FunctionSpaceUiWrapper getFunctionSpaceUiWrapper() {
        FunctionSpaceUiWrapper uiWrapper = new FunctionSpaceUiWrapper();
        uiWrapper.getGroupEvaluationFunctionSpace().getGroupEvaluationFunctionSpaceFunctionRooms().add(createGroupEvaluationFunctionSpaceFunctionRoom());
        return uiWrapper;
    }

    private GroupEvaluationFunctionSpaceFunctionRoom createGroupEvaluationFunctionSpaceFunctionRoom() {
        GroupEvaluationFunctionSpaceFunctionRoom newGrpEvalFunctionSpace = new GroupEvaluationFunctionSpaceFunctionRoom();
        newGrpEvalFunctionSpace.setGroupEvaluationFunctionSpace(createGroupEvaluationFunctionSpace());
        newGrpEvalFunctionSpace.setFunctionSpaceFunctionRoom(createFunctionSpaceFunctionRoom());
        return newGrpEvalFunctionSpace;
    }

    private FunctionSpaceFunctionRoom createFunctionSpaceFunctionRoom() {
        FunctionSpaceFunctionRoom room = new FunctionSpaceFunctionRoom();
        room.setName("Room A");
        room.setId(1);
        return room;
    }


    private GroupEvaluationFunctionSpace createGroupEvaluationFunctionSpace() {
        GroupEvaluationFunctionSpace fs = new GroupEvaluationFunctionSpace();
        return fs;
    }

    public Map<Property, List<GroupEvaluationConferenceAndBanquetDto>> getGroupEvaluationConferenceAndBanquetDtos(List<Property> propertyList) {
        Map<Property, List<GroupEvaluationConferenceAndBanquetDto>> hashMap = new HashMap<>();
        for (Property property : propertyList) {
            hashMap.put(property, getGroupEvaluationConferenceAndBanquetList());
        }

        return hashMap;
    }

    private List<GroupEvaluationConferenceAndBanquetDto> getGroupEvaluationConferenceAndBanquetList() {
        GroupEvaluationConferenceAndBanquetDto groupEvaluationConferenceAndBanquet = createGroupEvaluationConferenceAndBanquetDto(new BigDecimal("200.00"), new BigDecimal("10.00"), new BigDecimal("20.00"));
        return List.of(groupEvaluationConferenceAndBanquet);
    }

    private GroupEvaluationConferenceAndBanquetDto createGroupEvaluationConferenceAndBanquetDto(BigDecimal revenue, BigDecimal commissionPercentage, BigDecimal profitPercentage) {
        GroupEvaluationConferenceAndBanquetDto groupEvaluationConferenceAndBanquet = new GroupEvaluationConferenceAndBanquetDto();
        groupEvaluationConferenceAndBanquet.setConferenceAndBanquet(getConferenceAndBanquetDto(profitPercentage));
        groupEvaluationConferenceAndBanquet.setRevenue(revenue);
        groupEvaluationConferenceAndBanquet.setCommissionPercentage(commissionPercentage);
        groupEvaluationConferenceAndBanquet.setProfitPercentage(profitPercentage);
        return groupEvaluationConferenceAndBanquet;
    }

    private ConferenceAndBanquetDto getConferenceAndBanquetDto(BigDecimal profitPercentage) {
        ConferenceAndBanquetDto groupPricingConfigurationConferenceAndBanquet = new ConferenceAndBanquetDto();
        groupPricingConfigurationConferenceAndBanquet.setProfitPercentage(profitPercentage);
        groupPricingConfigurationConferenceAndBanquet.setIncluded(false);
        groupPricingConfigurationConferenceAndBanquet.setStatusId(1);
        return groupPricingConfigurationConferenceAndBanquet;
    }
}

