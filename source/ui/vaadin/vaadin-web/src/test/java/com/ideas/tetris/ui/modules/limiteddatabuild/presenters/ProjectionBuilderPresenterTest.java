package com.ideas.tetris.ui.modules.limiteddatabuild.presenters;

import com.ideas.tetris.pacman.services.accommodation.entity.AccomClass;
import com.ideas.tetris.pacman.services.distributionoptimizerservice.CMAESDistributionOptimizerService;
import com.ideas.tetris.pacman.services.limiteddatabuild.LDBService;
import com.ideas.tetris.pacman.services.limiteddatabuild.ProjectionDataService;
import com.ideas.tetris.pacman.services.limiteddatabuild.cloneProjectionBuilder.CloneProjectionBuilderService;
import com.ideas.tetris.pacman.services.limiteddatabuild.cloneProjectionBuilder.dto.MonthlyADRDto;
import com.ideas.tetris.pacman.services.limiteddatabuild.cloneProjectionBuilder.dto.MonthlyMSADRDto;
import com.ideas.tetris.pacman.services.limiteddatabuild.dto.Projection;
import com.ideas.tetris.pacman.services.limiteddatabuild.entity.*;
import com.ideas.tetris.pacman.services.limiteddatabuild.projectionbuilder.LDBADRByRCProjectionsService;
import com.ideas.tetris.pacman.services.limiteddatabuild.projectionbuilder.LDBMonthlyProjectionService;
import com.ideas.tetris.pacman.services.limiteddatabuild.projectionbuilder.MonthlyProjectionsService;
import com.ideas.tetris.pacman.util.Runner;
import com.ideas.tetris.platform.common.time.JavaLocalDateUtils;
import com.ideas.tetris.platform.common.xml.schema.workcontext.v1.WorkContextType;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import com.ideas.tetris.ui.VaadinUIBaseJupiterTest;
import com.ideas.tetris.ui.common.util.models.SingleValueModel;
import com.ideas.tetris.ui.modules.limiteddatabuild.views.ProjectionBuilderView;
import com.ideas.tetris.ui.modules.reports.util.DateRange;
import org.joda.time.LocalDate;
import org.joda.time.format.DateTimeFormat;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.math.BigDecimal;
import java.time.Month;
import java.time.YearMonth;
import java.util.*;
import java.util.stream.Collectors;

import static com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName.COMPONENT_ROOMS_ENABLED;
import static com.ideas.tetris.pacman.common.configparams.IPConfigParamName.CORE_LIMITED_DATA_BUILD_ENABLED;
import static com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName.ENABLE_RC_FOR_PROJECTION_BUILDER;
import static com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName.GENERATE_PROJECTIONS_FROM_CLONE_PROPERTIES;
import static com.ideas.tetris.platform.common.utils.dateutil.DateUtil.*;
import static com.ideas.tetris.ui.modules.limiteddatabuild.presenters.ProjectionBuilderPresenter.TOTAL_ADR;
import static com.ideas.tetris.ui.modules.limiteddatabuild.presenters.ProjectionBuilderPresenter.TOTAL_OCCUPANCY_PERCENTAGE;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class ProjectionBuilderPresenterTest extends VaadinUIBaseJupiterTest {

    public static final String JAN_2024 = "Jan-2024";
    public static final String FEB_2024 = "Feb-2024";
    @InjectMocks
    private ProjectionBuilderPresenter presenter;

    @InjectMocks
    CMAESDistributionOptimizerService optimizerService;

    @InjectMocks
    LDBMonthlyProjectionService ldbMonthlyProjectionService;

    @Mock
    private ProjectionBuilderView view;

    @Mock
    private ProjectionDataService projectionDataService;

    @Mock
    private PacmanConfigParamsService pacmanConfigParamsService;

    @Mock
    private MonthlyProjectionsService monthlyProjectionsService;

    @Mock
    private LDBADRByRCProjectionsService ldbadrByRCProjectionsService;

    @Mock
    LDBService ldbService;

    @Mock
    CloneProjectionBuilderService cloneProjectionBuilderService;

    @Captor
    private ArgumentCaptor<ArrayList<LDBMonthlyProjections>> monthlyProjectionsCaptor;

    @Captor
    private ArgumentCaptor<ArrayList<Projection>> generatedMonthlyProjectionsCaptor;

    @BeforeEach
    public void setUp() {
        presenter.setLangForTesting(tetrisUi.getLang());
    }

    @Test
    public void onViewInit() {
        // WHEN
        presenter.onViewInit();

        // THEN
        verify(view).onCancel(any(Runner.class));
        verify(view).onSave(any(Runner.class));
        verify(view).onGenerate(any(Runner.class));
    }

    @Test
    public void onWorkContextChange() {
        presenter.onWorkContextChange(new WorkContextType());

        verify(view).setRoomClasses(anyList(), anyBoolean());
    }

    @Test
    void onViewOpened_WhenCloneProjectionsEnabled() {
        //Given
        LocalDate startDate = LocalDate.parse("2024-01-01");
        LocalDate endDate = LocalDate.parse("2024-04-30");
        List<String> marketSegments = Arrays.asList("MS1", "MS2");
        DateRange dateRange = new DateRange(startDate, endDate);
        Map<String,Object> dataMap = new HashMap<>();
        dataMap.put("isCloneSourceSelected",false);
        dataMap.put("dateRange",dateRange);
        Map<YearMonth, Integer> monthlyOccupancyPercentageMap = getMonthlyOccupancyPercentagesMap();
        List<MonthlyADRDto> monthlyADRDtos = List.of(
                getMonthlyADRDto(java.time.LocalDate.of(2024,1,1),200,190,BigDecimal.valueOf(19000),BigDecimal.valueOf(100.00)),
                getMonthlyADRDto(java.time.LocalDate.of(2024,2,1),290,255,BigDecimal.valueOf(23000),BigDecimal.valueOf(90.20)),
                getMonthlyADRDto(java.time.LocalDate.of(2024,3,1),310,299,BigDecimal.valueOf(33000),BigDecimal.valueOf(110.37)),
                getMonthlyADRDto(java.time.LocalDate.of(2024,4,1),300,110,BigDecimal.valueOf(14000),BigDecimal.valueOf(127.27))
        );

        when(pacmanConfigParamsService.getBooleanParameterValue(GENERATE_PROJECTIONS_FROM_CLONE_PROPERTIES)).thenReturn(true);
        doReturn(marketSegments).when(projectionDataService).getMarketSegments();
        doReturn(getPatterns(marketSegments)).when(monthlyProjectionsService).getAllDOWProjectionsEntities();
        doReturn(10).when(projectionDataService).getCapacity(false);
        doReturn(monthlyOccupancyPercentageMap).when(cloneProjectionBuilderService).getMonthlyTotalOccupancyPercentages();
        doReturn(monthlyADRDtos).when(cloneProjectionBuilderService).getMonthlyADRDtos();
        presenter.addDowColumns();

        //When
        presenter.onViewOpened(dataMap);

        //Then
        view.setDateRange(any());
        view.setADRModel(any());
        view.setRoomClasses(anyList(), anyBoolean());
        view.setDateRange(dateRange);
        view.setRoomsModel(any());
        view.setMonthlyCapacityHandler(any());
        view.setADRModel(any());
        view.setDOWModel(any());
        view.refresh(any());
        view.setTotalOccupancyPercentageModel(any(),eq(false));
        view.setTotalADRModel(any(),eq(false));
        view.setMonthlyMSOccupancyHandler(any());
        view.setMonthlyMSADRHandler(any());
        view.setMonthlyTotalRoomSoldForADR(any());
        view.setMonthlyMSRoomSoldForADR(any());

        assertFalse(presenter.totalOccupancyPercentageGridData.isEmpty());
        assertFalse(presenter.totalADRGridData.isEmpty());
        assertTrue(presenter.totalOccupancyPercentageGridData.containsKey(TOTAL_OCCUPANCY_PERCENTAGE));
        assertTrue(presenter.totalADRGridData.containsKey(TOTAL_ADR));
        assertEquals(1, presenter.totalOccupancyPercentageGridData.size());
        assertEquals(1, presenter.totalADRGridData.size());
        assertEquals(4, presenter.totalOccupancyPercentageGridData.get(TOTAL_OCCUPANCY_PERCENTAGE).size());
        assertEquals(4, presenter.totalADRGridData.get(TOTAL_ADR).size());

        assertTrue(presenter.totalOccupancyPercentageGridData.get(TOTAL_OCCUPANCY_PERCENTAGE).containsKey(JAN_2024));
        assertEquals(10,presenter.totalOccupancyPercentageGridData.get(TOTAL_OCCUPANCY_PERCENTAGE).get(JAN_2024).getValue().intValue());

        assertTrue(presenter.totalOccupancyPercentageGridData.get(TOTAL_OCCUPANCY_PERCENTAGE).containsKey(FEB_2024));
        assertEquals(15,presenter.totalOccupancyPercentageGridData.get(TOTAL_OCCUPANCY_PERCENTAGE).get(FEB_2024).getValue().intValue());

        assertTrue(presenter.totalOccupancyPercentageGridData.get(TOTAL_OCCUPANCY_PERCENTAGE).containsKey("Mar-2024"));
        assertEquals(20,presenter.totalOccupancyPercentageGridData.get(TOTAL_OCCUPANCY_PERCENTAGE).get("Mar-2024").getValue().intValue());

        assertTrue(presenter.totalOccupancyPercentageGridData.get(TOTAL_OCCUPANCY_PERCENTAGE).containsKey("Apr-2024"));
        assertEquals(25,presenter.totalOccupancyPercentageGridData.get(TOTAL_OCCUPANCY_PERCENTAGE).get("Apr-2024").getValue().intValue());

        assertTrue(presenter.totalADRGridData.get(TOTAL_ADR).containsKey(JAN_2024));
        assertEquals(BigDecimal.valueOf(100.00),presenter.totalADRGridData.get(TOTAL_ADR).get(JAN_2024).getValue());

        assertTrue(presenter.totalADRGridData.get(TOTAL_ADR).containsKey(FEB_2024));
        assertEquals(BigDecimal.valueOf(90.20),presenter.totalADRGridData.get(TOTAL_ADR).get(FEB_2024).getValue());

        assertTrue(presenter.totalADRGridData.get(TOTAL_ADR).containsKey("Mar-2024"));
        assertEquals(BigDecimal.valueOf(110.37),presenter.totalADRGridData.get(TOTAL_ADR).get("Mar-2024").getValue());

        assertTrue(presenter.totalADRGridData.get(TOTAL_ADR).containsKey("Apr-2024"));
        assertEquals(BigDecimal.valueOf(127.27),presenter.totalADRGridData.get(TOTAL_ADR).get("Apr-2024").getValue());
    }


    private MonthlyADRDto getMonthlyADRDto(java.time.LocalDate projectionMonthYear, Integer monthlyCapacity, Integer monthlyRoomSold, BigDecimal monthlyRevenue, BigDecimal monthlyADR){
        MonthlyADRDto monthlyADRDto = new MonthlyADRDto();
        monthlyADRDto.setProjectionMonthYear(projectionMonthYear);
        monthlyADRDto.setMonthlyCapacity(monthlyCapacity);
        monthlyADRDto.setMonthlyRoomSold(monthlyRoomSold);
        monthlyADRDto.setMonthlyRevenue(monthlyRevenue);
        monthlyADRDto.setMonthlyADR(monthlyADR);
        return monthlyADRDto;
    }

    private Map<YearMonth, Integer> getMonthlyOccupancyPercentagesMap(){
        Map<YearMonth, Integer> monthlyOccupancyPercentages = new HashMap<>();
        monthlyOccupancyPercentages.put(YearMonth.of(2024,Month.JANUARY),10);
        monthlyOccupancyPercentages.put(YearMonth.of(2024,Month.FEBRUARY),15);
        monthlyOccupancyPercentages.put(YearMonth.of(2024,Month.MARCH),20);
        monthlyOccupancyPercentages.put(YearMonth.of(2024,Month.APRIL),25);
        monthlyOccupancyPercentages.put(YearMonth.of(2024,Month.MAY),30);
        monthlyOccupancyPercentages.put(YearMonth.of(2024,Month.JUNE),35);
        monthlyOccupancyPercentages.put(YearMonth.of(2024,Month.JULY),40);
        monthlyOccupancyPercentages.put(YearMonth.of(2024,Month.AUGUST),45);
        monthlyOccupancyPercentages.put(YearMonth.of(2024,Month.SEPTEMBER),50);
        monthlyOccupancyPercentages.put(YearMonth.of(2024,Month.OCTOBER),55);
        monthlyOccupancyPercentages.put(YearMonth.of(2024,Month.NOVEMBER),60);
        monthlyOccupancyPercentages.put(YearMonth.of(2024,Month.DECEMBER),65);
        return monthlyOccupancyPercentages;
    }


    @Test
    public void onViewOpenedWithRoomClassEnabled() {
        //Given
        LocalDate startDate = LocalDate.parse("2021-01-01");
        LocalDate endDate = LocalDate.parse("2022-04-01");
        List<String> marketSegments = Arrays.asList("M1", "M2");
        List<AccomClass> accomClasses = new ArrayList<>();
        AccomClass standard = new AccomClass();
        standard.setId(1);
        accomClasses.add(standard);
        Map<Integer, BigDecimal> weightedAverage = new HashMap<>();
        weightedAverage.put(1, BigDecimal.ONE);

        Map<Integer, BigDecimal> capacityByRoomClass = new HashMap<>();
        capacityByRoomClass.put(1, BigDecimal.valueOf(10));


        when(pacmanConfigParamsService.getBooleanParameterValue(ENABLE_RC_FOR_PROJECTION_BUILDER)).thenReturn(true);
        when(pacmanConfigParamsService.getBooleanParameterValue(CORE_LIMITED_DATA_BUILD_ENABLED)).thenReturn(true);
        when(pacmanConfigParamsService.getBooleanParameterValue(COMPONENT_ROOMS_ENABLED)).thenReturn(true);
        List<LDBADRByRCProjections> ldbADRByRCProjections = getLDBADRByRCProjections(startDate.toDate(), endDate.toDate(), marketSegments, accomClasses);

        DateRange dateRange = new DateRange(startDate, endDate);
        Map<String,Object> dataMap = new HashMap<>();
        dataMap.put("isCloneSourceSelected",false);
        dataMap.put("dateRange",dateRange);
        doReturn(marketSegments).when(projectionDataService).getMarketSegments();
        doReturn(accomClasses).when(projectionDataService).getAccomClasses();
        doReturn(ldbADRByRCProjections).when(ldbadrByRCProjectionsService).fetchADRByRCProjectionsByDate(eq(startDate.toDate()), eq(endDate.toDate()));
        doReturn(getPatterns(marketSegments)).when(monthlyProjectionsService).getAllDOWProjectionsEntities();
        doReturn(capacityByRoomClass).when(projectionDataService).getCapacityByRoomClass();
        doReturn(10).when(projectionDataService).getCapacity(false);
        presenter.accomClasses = accomClasses;
        presenter.capacityByRoomClass = capacityByRoomClass;
        presenter.addDowColumns();

        //When
        presenter.onViewOpened(dataMap);

        //Then
        view.setDateRange(any());
        view.setADRModel(any());
        view.setRoomClasses(anyList(), anyBoolean());
        view.setDateRange(dateRange);
        view.setRoomsModel(any());
        view.setMonthlyCapacityHandler(any());
        view.setADRModel(any());
        view.setDOWModel(any());
        view.refresh(any());

        assertFalse(presenter.adrDataForRoomClass.isEmpty());
        assertTrue(presenter.adrDataForRoomClass.keySet().contains("1_M1"));
        assertEquals(2, presenter.adrDataForRoomClass.size());
        assertEquals(16, presenter.adrDataForRoomClass.get("1_M1").size());
        assertEquals(16, presenter.adrDataForRoomClass.get("1_M2").size());

        assertTrue(presenter.adrDataForRoomClass.get("1_M1").containsKey("Jan-2022"));
        assertTrue(presenter.adrDataForRoomClass.get("1_M1").containsKey("Feb-2022"));
        assertTrue(presenter.adrDataForRoomClass.get("1_M1").containsKey("Mar-2022"));

        assertTrue(presenter.adrDataForRoomClass.get("1_M2").containsKey("Apr-2021"));
        assertTrue(presenter.adrDataForRoomClass.get("1_M2").containsKey("Mar-2021"));
        assertTrue(presenter.adrDataForRoomClass.get("1_M2").containsKey("Feb-2021"));
    }

    private List<LDBADRByRCProjections> getLDBADRByRCProjections(Date startDate, Date endDate, List<String> marketSegments, List<AccomClass> accomClasses) {
        List<LDBADRByRCProjections> ldbadrByRCProjections = new ArrayList<>();
        accomClasses.stream().forEach(accomClass -> {
            marketSegments.stream().forEach(marketSegment -> {
                Date current = startDate;
                int id = 0;
                do {
                    LDBADRByRCProjections ldbadrByRCProjection = new LDBADRByRCProjections();
                    ldbadrByRCProjections.add(ldbadrByRCProjection);
                    ldbadrByRCProjection.setId(id++);
                    ldbadrByRCProjection.setProjectionMonth(current);
                    ldbadrByRCProjection.setMarketSegmentName(marketSegment);
                    ldbadrByRCProjection.setProjectedADR(BigDecimal.valueOf(10));
                    ldbadrByRCProjection.setAccomClassId(accomClass.getId());
                    current = addMonthsToDate(current, 1);
                } while (!current.after(endDate));
            });
        });
        return ldbadrByRCProjections;
    }

    @Test
    public void onViewOpened() {

        // GIVEN
        LocalDate startDate = LocalDate.parse("2018-01-01");
        LocalDate endDate = LocalDate.parse("2018-04-01");
        List<String> marketSegments = Arrays.asList("M1", "M2");
        List<LDBMonthlyProjections> projections = getProjections(startDate, endDate, marketSegments);
        DateRange dateRange = new DateRange(startDate, endDate);
        Map<String,Object> dataMap = new HashMap<>();
        dataMap.put("isCloneSourceSelected",false);
        dataMap.put("dateRange",dateRange);
        LDBConfig entity = new LDBConfig();
        entity.setPatternSource(PatternSource.CLONE);
        entity.setLocked(false);
        Mockito.when(ldbService.getLDBConfig()).thenReturn(entity);

        doReturn(marketSegments).when(projectionDataService).getMarketSegments();
        doReturn(projections).when(monthlyProjectionsService).getMonthlyProjectionsEntitiesByDateRange(eq(startDate), eq(endDate));
        doReturn(getPatterns(marketSegments)).when(monthlyProjectionsService).getAllDOWProjectionsEntities();
        presenter.addDowColumns();

        // WHEN
        presenter.onViewOpened(dataMap);

        // THEN
        verify(view).setDateRange(eq(dateRange));
        verify(view).setRoomsModel(any());
        verify(view).setADRModel(any());
        verify(view).setRoomClasses(anyList(), anyBoolean());
        assertFalse(presenter.roomsData.isEmpty());
        assertEquals(2, presenter.roomsData.size());
        assertTrue(presenter.roomsData.keySet().contains("M1"));
        assertEquals(4, presenter.roomsData.get("M1").size());
        assertTrue(presenter.roomsData.get("M1").containsKey("Jan-2018"));
        assertTrue(presenter.roomsData.get("M1").containsKey("Feb-2018"));
        assertTrue(presenter.roomsData.get("M1").containsKey("Mar-2018"));
        assertTrue(presenter.roomsData.get("M1").containsKey("Apr-2018"));

        assertTrue(presenter.roomsData.keySet().contains("M2"));
        assertEquals(4, presenter.roomsData.get("M2").size());
        assertTrue(presenter.roomsData.get("M2").containsKey("Jan-2018"));
        assertTrue(presenter.roomsData.get("M2").containsKey("Feb-2018"));
        assertTrue(presenter.roomsData.get("M2").containsKey("Mar-2018"));
        assertTrue(presenter.roomsData.get("M2").containsKey("Apr-2018"));


        assertFalse(presenter.adrData.isEmpty());
        assertEquals(2, presenter.adrData.size());
        assertTrue(presenter.adrData.keySet().contains("M1"));
        assertEquals(4, presenter.adrData.get("M1").size());
        assertTrue(presenter.adrData.get("M1").containsKey("Jan-2018"));
        assertTrue(presenter.adrData.get("M1").containsKey("Feb-2018"));
        assertTrue(presenter.adrData.get("M1").containsKey("Mar-2018"));
        assertTrue(presenter.adrData.get("M1").containsKey("Apr-2018"));

        assertTrue(presenter.adrData.keySet().contains("M2"));
        assertEquals(4, presenter.adrData.get("M2").size());
        assertTrue(presenter.adrData.get("M2").containsKey("Jan-2018"));
        assertTrue(presenter.adrData.get("M2").containsKey("Feb-2018"));
        assertTrue(presenter.adrData.get("M2").containsKey("Mar-2018"));
        assertTrue(presenter.adrData.get("M2").containsKey("Apr-2018"));


        assertFalse(presenter.dowData.isEmpty());
        assertEquals(2, presenter.dowData.size());
        assertTrue(presenter.dowData.keySet().contains("M1"));
        assertEquals(7, presenter.dowData.get("M1").size());
        assertTrue(presenter.dowData.get("M1").containsKey("Sunday"));
        assertTrue(presenter.dowData.get("M1").containsKey("Monday"));
        assertTrue(presenter.dowData.get("M1").containsKey("Tuesday"));
        assertTrue(presenter.dowData.get("M1").containsKey("Wednesday"));
        assertTrue(presenter.dowData.get("M1").containsKey("Thursday"));
        assertTrue(presenter.dowData.get("M1").containsKey("Friday"));
        assertTrue(presenter.dowData.get("M1").containsKey("Saturday"));

        assertTrue(presenter.dowData.keySet().contains("M2"));
        assertEquals(7, presenter.dowData.get("M2").size());
        assertTrue(presenter.dowData.get("M2").containsKey("Sunday"));
        assertTrue(presenter.dowData.get("M2").containsKey("Monday"));
        assertTrue(presenter.dowData.get("M2").containsKey("Tuesday"));
        assertTrue(presenter.dowData.get("M2").containsKey("Wednesday"));
        assertTrue(presenter.dowData.get("M2").containsKey("Thursday"));
        assertTrue(presenter.dowData.get("M2").containsKey("Friday"));
        assertTrue(presenter.dowData.get("M2").containsKey("Saturday"));
    }

    @Test
    public void saveAsDraftShouldCreateNewEntitiesTest() {
        //GIVEN
        presenter.marketSegments = Arrays.asList("M1", "M2");
        LocalDate localDate = new LocalDate();
        presenter.months = Arrays.asList(localDate, localDate.plusMonths(1));
        presenter.dateRange = new DateRange(localDate, localDate.plusMonths(1));
        presenter.addDowColumns();
        presenter.loadZeroFilledData();
        presenter.roomsData.get("M1").get(formatDate(localDate.toDate(), DATE_FORMAT_MONTH_YEAR)).setValue(10);
        presenter.adrData.get("M1").get(formatDate(localDate.toDate(), DATE_FORMAT_MONTH_YEAR)).setValue(new BigDecimal(5));

        //WHEN
        presenter.saveGridData();

        //THEN
        verify(monthlyProjectionsService).saveMonthlyProjectionsEntities(monthlyProjectionsCaptor.capture());
        ArrayList<LDBMonthlyProjections> savedResult = monthlyProjectionsCaptor.getValue();
        assertNull(savedResult.get(0).getId());
        assertEquals(10, savedResult.get(0).getProjectedRooms().intValue());
        assertEquals(5, savedResult.get(0).getProjectedADR().intValue());
    }

    @Test
    public void saveAsDraftShouldUpdateOldEntitiesTest() {
        //GIVEN
        List<String> marketSegments = Arrays.asList("M1", "M2");
        presenter.marketSegments = marketSegments;
        LocalDate localDate = new LocalDate();
        presenter.months = Arrays.asList(localDate, localDate.plusMonths(1));
        presenter.dateRange = new DateRange(localDate, localDate.plusMonths(1));
        presenter.addDowColumns();
        presenter.loadZeroFilledData();
        presenter.roomsData.get("M1").get(formatDate(localDate.toDate(), DATE_FORMAT_MONTH_YEAR)).setValue(50);
        presenter.adrData.get("M1").get(formatDate(localDate.toDate(), DATE_FORMAT_MONTH_YEAR)).setValue(new BigDecimal(25));
        List<LDBMonthlyProjections> ldbMonthlyProjections = getProjections(localDate, localDate.plusMonths(1), marketSegments);
        List<LDBDOWProjections> ldbDOWProjections = getPatterns(marketSegments);
        presenter.monthlyProjectionsEntitiesMap = ldbMonthlyProjections.stream().collect(Collectors.toMap(dto -> dto.getMarketSegmentName()
                + "_" + formatDate(dto.getProjectionMonth().toDate(), DATE_FORMAT_MONTH_YEAR), dto -> dto));
        presenter.dowProjectionsEntitiesMap = ldbDOWProjections.stream().collect(Collectors.toMap(LDBDOWProjections::getMarketSegmentName, dto -> dto));

        //WHEN
        presenter.saveGridData();

        //THEN
        verify(monthlyProjectionsService).saveMonthlyProjectionsEntities(monthlyProjectionsCaptor.capture());
        ArrayList<LDBMonthlyProjections> savedResult = monthlyProjectionsCaptor.getValue();
        assertNotNull(savedResult.get(0).getId());
        assertEquals(50, savedResult.get(0).getProjectedRooms().intValue());
        assertEquals(25, savedResult.get(0).getProjectedADR().intValue());
        assertNotNull(savedResult.get(1).getId());
        assertEquals(0, savedResult.get(1).getProjectedRooms().intValue());
        assertEquals(0, savedResult.get(1).getProjectedADR().intValue());
    }

    @Test
    public void testGenerateProjections() {
        //GIVEN
        List<String> marketSegments = Arrays.asList("M1", "M2");
        presenter.marketSegments = marketSegments;
        LocalDate localDate = new LocalDate("2019-08-29");
        presenter.months = Arrays.asList(localDate, localDate.plusMonths(1));
        presenter.dateRange = new DateRange(localDate, localDate.plusMonths(1));
        presenter.dailyHotelCapacity = 400;
        presenter.addDowColumns();
        presenter.loadZeroFilledData();
        presenter.roomsData.get("M1").get(formatDate(localDate.toDate(), DATE_FORMAT_MONTH_YEAR)).setValue(250);
        presenter.adrData.get("M1").get(formatDate(localDate.toDate(), DATE_FORMAT_MONTH_YEAR)).setValue(new BigDecimal(25));
        presenter.roomsData.get("M2").get(formatDate(localDate.toDate(), DATE_FORMAT_MONTH_YEAR)).setValue(250);
        presenter.adrData.get("M2").get(formatDate(localDate.toDate(), DATE_FORMAT_MONTH_YEAR)).setValue(new BigDecimal(25));
        List<LDBMonthlyProjections> ldbMonthlyProjections = getProjections(localDate, localDate.plusMonths(1), marketSegments);
        List<LDBDOWProjections> ldbDOWProjections = getPatterns(marketSegments);
        presenter.monthlyProjectionsEntitiesMap = ldbMonthlyProjections.stream().collect(Collectors.toMap(dto -> dto.getMarketSegmentName()
                + "_" + formatDate(dto.getProjectionMonth().toDate(), DATE_FORMAT_MONTH_YEAR), dto -> dto));
        presenter.dowProjectionsEntitiesMap = ldbDOWProjections.stream().collect(Collectors.toMap(LDBDOWProjections::getMarketSegmentName, dto -> dto));
        presenter.distributionOptimizerService = optimizerService;
        presenter.createDayofWeekCountMap();
        presenter.ldbMonthlyProjectionService = ldbMonthlyProjectionService;
        //WHEN
        ProjectionBuilderMetaData metadata = new ProjectionBuilderMetaData();
        metadata.setProjectionSource("MANUAL");
        Mockito.when(ldbService.getProjectionBuilderMetaData()).thenReturn(Optional.of(metadata));
        presenter.generate();

        //THEN
        verify(ldbService).loadProjectionsIntoPacman(generatedMonthlyProjectionsCaptor.capture());
        List<Projection> generatedResult = generatedMonthlyProjectionsCaptor.getValue().stream()
                .sorted(Comparator.comparing(Projection::getMarketSegment).thenComparing(Projection::getOccupancyDate)).collect(Collectors.toList());
        assertNotNull(generatedResult.get(0).getMarketSegment());
        assertEquals(64, generatedResult.size());
        assertEquals(125, generatedResult.get(0).getRoomsSold());
        assertEquals(3125, generatedResult.get(0).getRoomRevenue().intValue());
        assertEquals(251, generatedResult.get(0).getRoomsSold() + generatedResult.get(1).getRoomsSold() + generatedResult.get(2).getRoomsSold(), "Total Distributed: ");
        verify(view).closeProjectionBuilder();
    }

    @Test
    public void testGetMonthlyHotelCapacity() {
        presenter.dailyHotelCapacity = 400;
        LocalDate startOfMonth = new LocalDate().dayOfYear().withMinimumValue();
        presenter.dateRange = new DateRange(startOfMonth, startOfMonth.dayOfMonth().withMaximumValue());
        int monthlyHotelCapacity = presenter.getMonthlyHotelCapacity(startOfMonth);
        assertEquals(12400, monthlyHotelCapacity);
    }

    private List<LDBMonthlyProjections> getProjections(LocalDate startDate, LocalDate endDate, List<String> marketSegments) {
        List<LDBMonthlyProjections> projections = new ArrayList<>();
        marketSegments.stream().forEach(marketSegment -> {
            LocalDate current = startDate;
            int id = 0;
            do {
                LDBMonthlyProjections projection = new LDBMonthlyProjections();
                projections.add(projection);
                projection.setId(id++);
                projection.setProjectionMonth(current);
                projection.setMarketSegmentName(marketSegment);
                projection.setProjectedRooms(10);
                projection.setProjectedADR(BigDecimal.valueOf(10));
                current = current.plusMonths(1);
            } while (!current.isAfter(endDate));
        });
        return projections;
    }

    private List<LDBDOWProjections> getPatterns(List<String> marketSegments) {
        return marketSegments.stream().map(marketSegment -> {
            LDBDOWProjections pattern = new LDBDOWProjections();
            pattern.setMarketSegmentName(marketSegment);
            pattern.setSundayProjection(10);
            pattern.setMondayProjection(10);
            pattern.setTuesdayProjection(20);
            pattern.setWednesdayProjection(20);
            pattern.setThursdayProjection(20);
            pattern.setFridayProjection(10);
            pattern.setSaturdayProjection(10);
            return pattern;
        }).collect(Collectors.toList());
    }

    @Test
    void testGeneratedLDBByRCProjections() {
        LocalDate localDate = new LocalDate("2023-01-26");
        List<String> marketSegments = List.of("M1", "M2");
        List<AccomClass> accomClasses = new ArrayList<>();
        AccomClass standard = new AccomClass();
        standard.setId(1);
        standard.setName("STANDARD");
        AccomClass deluxe = new AccomClass();
        deluxe.setId(2);
        deluxe.setName("DELUXE");
        accomClasses.add(standard);
        accomClasses.add(deluxe);
        HashMap<Integer, BigDecimal> weightedAverage = new HashMap<>();
        weightedAverage.put(1, new BigDecimal(0.5));
        weightedAverage.put(2, new BigDecimal(0.5));
        presenter.isLDBAndComponentAndRCEnabled = true;
        presenter.ldbMonthlyProjectionService = ldbMonthlyProjectionService;

        presenter.months = Arrays.asList(localDate, localDate.plusMonths(1));
        presenter.dateRange = new DateRange(localDate, localDate.plusMonths(1));
        presenter.marketSegments = marketSegments;
        presenter.weightedAverageByRoomClass = weightedAverage;
        presenter.accomClasses = accomClasses;
        presenter.dailyHotelCapacity = 200;
        presenter.addDowColumns();
        presenter.loadZeroFilledData();
        presenter.createDayofWeekCountMap();

        List<LDBDOWProjections> ldbDOWProjections = getPatterns(marketSegments);
        presenter.dowProjectionsEntitiesMap = ldbDOWProjections.stream().collect(Collectors.toMap(LDBDOWProjections::getMarketSegmentName, dto -> dto));
        presenter.distributionOptimizerService = optimizerService;

        presenter.roomsData.get("M1").get(formatDate(localDate.toDate(), DATE_FORMAT_MONTH_YEAR)).setValue(100);
        presenter.adrDataForRoomClass.get("1_M1").get(formatDate(localDate.toDate(), DATE_FORMAT_MONTH_YEAR)).setValue(new BigDecimal(125));
        presenter.roomsData.get("M2").get(formatDate(localDate.toDate(), DATE_FORMAT_MONTH_YEAR)).setValue(100);
        presenter.adrDataForRoomClass.get("1_M2").get(formatDate(localDate.toDate(), DATE_FORMAT_MONTH_YEAR)).setValue(new BigDecimal(150));

        //When
        ProjectionBuilderMetaData metadata = new ProjectionBuilderMetaData();
        metadata.setProjectionSource("MANUAL");
        Mockito.when(ldbService.getProjectionBuilderMetaData()).thenReturn(Optional.of(metadata));
        presenter.generate();

        //Then
        verify(ldbService).loadProjectionsIntoPacman(generatedMonthlyProjectionsCaptor.capture());
        List<Projection> generatedResult = generatedMonthlyProjectionsCaptor.getValue().stream()
                .sorted(Comparator.comparing(Projection::getMarketSegment).thenComparing(Projection::getOccupancyDate)).collect(Collectors.toList());

        assertEquals(128, generatedResult.size());
        assertEquals(13, generatedResult.get(0).getRoomsSold());
        assertEquals("STANDARD", generatedResult.get(0).getRoomClassName());
        assertEquals("M1", generatedResult.get(0).getMarketSegment());
        assertEquals(BigDecimal.valueOf(1625), generatedResult.get(0).getRoomRevenue());
        assertEquals(13, generatedResult.get(1).getRoomsSold());
        assertEquals("DELUXE", generatedResult.get(1).getRoomClassName());
        assertEquals("M1", generatedResult.get(1).getMarketSegment());
        assertEquals(BigDecimal.valueOf(0), generatedResult.get(1).getRoomRevenue());

        assertEquals(13, generatedResult.get(64).getRoomsSold());
        assertEquals("STANDARD", generatedResult.get(64).getRoomClassName());
        assertEquals("M2", generatedResult.get(64).getMarketSegment());
        assertEquals(BigDecimal.valueOf(1950), generatedResult.get(64).getRoomRevenue());

        verify(view).closeProjectionBuilder();

    }

    @Test
    void testUpdateMSOccupancyAndRevenueRatioCalculationOnMSOccupancyChange() {
        String ms1 = "MS1";
        String ms2 = "MS2";
        createMockOccupancyAndRevenueRatio(ms1, ms2);

        LocalDate localDate = LocalDate.parse(JAN_2024, DateTimeFormat.forPattern(DATE_FORMAT_MONTH_YEAR));
        MonthlyMSADRDto janMsADRDto1 = getMonthlyMSADRDto(localDate, ms1, 50, 100);
        MonthlyMSADRDto janMsADRDto2 = getMonthlyMSADRDto(localDate, ms2, 10, 160);
        MonthlyADRDto janMonthlyADR = getMonthlyADRDto(localDate, 60, 110);

        LocalDate febMonth = localDate.plusMonths(1);
        MonthlyMSADRDto febMonthlyMSADRDto1 = getMonthlyMSADRDto(febMonth, ms1, 10, 110);
        MonthlyMSADRDto febMonthlyMSADRDto2 = getMonthlyMSADRDto(febMonth, ms2, 5, 140);
        MonthlyADRDto febMonthlyADR = getMonthlyADRDto(febMonth, 15, 120);

        presenter.totalADRGridData.put(TOTAL_ADR, Map.of(JAN_2024, SingleValueModel.createSingleValueModel(BigDecimal.valueOf(10)),
                FEB_2024, SingleValueModel.createSingleValueModel(BigDecimal.valueOf(15))));
        presenter.monthlyADRDtoMap = Map.of(localDate, janMonthlyADR, febMonth, febMonthlyADR);
        presenter.monthlyMSADRDtoMap = Map.of(ms1, Map.of(JAN_2024, janMsADRDto1, FEB_2024, febMonthlyMSADRDto1), ms2, Map.of(JAN_2024, janMsADRDto2, FEB_2024, febMonthlyMSADRDto2));

        presenter.updateAllMSMonthlyOccupancyAndRevenueRatios(localDate, Map.of(ms1, 57, ms2, 43));
        Double msRatioValue1 = presenter.getMktSegMonthOccupancyRatio(ms1, localDate);
        assertEquals(0.57, msRatioValue1);
        Double msRatioValue2 = presenter.getMktSegMonthOccupancyRatio(ms2, localDate);
        assertEquals(0.43, msRatioValue2);

        Double msRevenueRatioValue1 = presenter.getMktSegMonthRevenueRatio(ms1, localDate);
        assertEquals(0.4531, msRevenueRatioValue1);
        Double msRevenueRatioValue2 = presenter.getMktSegMonthRevenueRatio(ms2, localDate);
        assertEquals(0.5469, msRevenueRatioValue2);

        presenter.updateAllMSMonthlyOccupancyAndRevenueRatios(febMonth, Map.of(ms1, 0, ms2, 0));
        msRatioValue1 = presenter.getMktSegMonthOccupancyRatio(ms1, febMonth);
        assertEquals(0.0, msRatioValue1);
        msRatioValue2 = presenter.getMktSegMonthOccupancyRatio(ms2, febMonth);
        assertEquals(0.0, msRatioValue2);

        msRevenueRatioValue1 = presenter.getMktSegMonthRevenueRatio(ms1, febMonth);
        assertEquals(0.0, msRevenueRatioValue1);
        msRevenueRatioValue2 = presenter.getMktSegMonthRevenueRatio(ms2, febMonth);
        assertEquals(0.0, msRevenueRatioValue2);
    }

    @Test
    void testUpdateMSOccupancyAndRevenueDTOCalculationOnTotalOccupancyPercentageChange() {
        String ms1 = "MS1";
        String ms2 = "MS2";
        createMockOccupancyAndRevenueRatio(ms1, ms2);

        LocalDate janMonth = LocalDate.parse(JAN_2024, DateTimeFormat.forPattern(DATE_FORMAT_MONTH_YEAR));
        MonthlyMSADRDto janMsADRDto1 = getMonthlyMSADRDto(janMonth, ms1, 50, 100);
        MonthlyMSADRDto janMsADRDto2 = getMonthlyMSADRDto(janMonth, ms2, 10, 160);
        MonthlyADRDto janMonthlyADR = getMonthlyADRDto(janMonth, 60, 110);

        LocalDate febMonth = janMonth.plusMonths(1);
        MonthlyMSADRDto febMonthlyMSADRDto1 = getMonthlyMSADRDto(febMonth, ms1, 10, 110);
        MonthlyMSADRDto febMonthlyMSADRDto2 = getMonthlyMSADRDto(febMonth, ms2, 5, 140);
        MonthlyADRDto febMonthlyADR = getMonthlyADRDto(febMonth, 15, 120);

        presenter.totalADRGridData.put(TOTAL_ADR, Map.of(JAN_2024, SingleValueModel.createSingleValueModel(BigDecimal.valueOf(10)),
                FEB_2024, SingleValueModel.createSingleValueModel(BigDecimal.valueOf(15))));
        presenter.monthlyADRDtoMap = Map.of(janMonth, janMonthlyADR, febMonth, febMonthlyADR);
        presenter.monthlyMSADRDtoMap = Map.of(ms1, Map.of(JAN_2024, janMsADRDto1, FEB_2024, febMonthlyMSADRDto1), ms2, Map.of(JAN_2024, janMsADRDto2, FEB_2024, febMonthlyMSADRDto2));
        presenter.marketSegments = List.of(ms1, ms2);

        presenter.updateMonthlyAdrDtoWithMSRevenueRatios(janMonth, Map.of(ms1, BigDecimal.valueOf(20000), ms2, BigDecimal.valueOf(30000)));
        Double msRatioValue1 = presenter.getMktSegMonthOccupancyRatio(ms1, janMonth);
        assertEquals(0.83333, msRatioValue1);
        Double msRatioValue2 = presenter.getMktSegMonthOccupancyRatio(ms2, janMonth);
        assertEquals(0.16667, msRatioValue2);

        Double msRevenueRatioValue1 = presenter.getMktSegMonthRevenueRatio(ms1, janMonth);
        assertEquals(0.4, msRevenueRatioValue1);
        Double msRevenueRatioValue2 = presenter.getMktSegMonthRevenueRatio(ms2, janMonth);
        assertEquals(0.6, msRevenueRatioValue2);

        MonthlyADRDto monthlyADRDto = presenter.monthlyADRDtoMap.get(janMonth);
        assertEquals(833.33, monthlyADRDto.getMonthlyADR().doubleValue());
    }


    @Test
    void testZeroFillMonthlyADRMapOnInitializingForManual() {
        presenter.isCloneProjectionsEnabled = true;
        presenter.marketSegments = List.of("ms1", "ms2");
        LocalDate janMonth = LocalDate.parse(JAN_2024, DateTimeFormat.forPattern(DATE_FORMAT_MONTH_YEAR));
        LocalDate febMonth = LocalDate.parse(FEB_2024, DateTimeFormat.forPattern(DATE_FORMAT_MONTH_YEAR));
        presenter.dateRange = new DateRange(janMonth, febMonth.plusMonths(1));
        presenter.dailyHotelCapacity = 62;
        presenter.months = List.of(janMonth, febMonth);

        presenter.initializeMonthlyADRMap();
        Map<LocalDate, MonthlyADRDto> monthlyADRDtoMap = presenter.monthlyADRDtoMap;
        assertEquals(2, monthlyADRDtoMap.size());
        assertEquals(1922, monthlyADRDtoMap.get(janMonth).getMonthlyCapacity());
        assertData(janMonth, monthlyADRDtoMap);
        assertData(febMonth, monthlyADRDtoMap);

        Map<String, Map<String, MonthlyMSADRDto>> monthlyMSADRDtoMap = presenter.monthlyMSADRDtoMap;
        assertEquals(2, monthlyADRDtoMap.size());
        assertData("ms1", JAN_2024, monthlyMSADRDtoMap);
        assertData("ms1", FEB_2024, monthlyMSADRDtoMap);
        assertData("ms2", JAN_2024, monthlyMSADRDtoMap);
        assertData("ms2", FEB_2024, monthlyMSADRDtoMap);
    }

    private void assertData(LocalDate month, Map<LocalDate, MonthlyADRDto> monthlyADRDtoMap) {
        assertTrue(monthlyADRDtoMap.containsKey(month));
        assertEquals(0, monthlyADRDtoMap.get(month).getMonthlyRoomSold());
        assertEquals(0.0, monthlyADRDtoMap.get(month).getMonthlyADR().doubleValue());
        assertEquals(0.0, monthlyADRDtoMap.get(month).getMonthlyRevenue().doubleValue());
    }

    private void assertData(String mktSeg, String month, Map<String, Map<String, MonthlyMSADRDto>> monthlyMSADRDtoMap) {
        assertTrue(monthlyMSADRDtoMap.containsKey(mktSeg));
        Map<String, MonthlyMSADRDto> monthlyADRDtoMap = monthlyMSADRDtoMap.get(mktSeg);
        assertTrue(monthlyADRDtoMap.containsKey(month));
        assertEquals(0, monthlyADRDtoMap.get(month).getMonthlyRoomSold());
        assertEquals(0.0, monthlyADRDtoMap.get(month).getMonthlyADR().doubleValue());
        assertEquals(0.0, monthlyADRDtoMap.get(month).getMonthlyRevenue().doubleValue());
    }

    private void createMockOccupancyAndRevenueRatio(String ms1, String ms2) {
        Map<String, Double> msRatio1 = new HashMap<>(Map.of(JAN_2024, 0.83333, FEB_2024, 0.75));
        Map<String, Double> msRatio2 = new HashMap<>(Map.of(JAN_2024, 0.16667, FEB_2024, 0.25));
        Map<String, Double> msRevenueRatio1 = new HashMap<>(Map.of(JAN_2024, 0.75757, FEB_2024, 0.61111));
        Map<String, Double> msRevenueRatio2 = new HashMap<>(Map.of(JAN_2024, 0.24242, FEB_2024, 0.38889));
        presenter.monthwiseMSOccupancyRatios = Map.of(ms1, msRatio1, ms2, msRatio2);
        presenter.monthwiseMSRevenueRatios = Map.of(ms1, msRevenueRatio1, ms2, msRevenueRatio2);
    }

    private static MonthlyADRDto getMonthlyADRDto(LocalDate localDate, Integer totalRoomsSold, Integer totalADR) {
        MonthlyADRDto monthlyADRDto = new MonthlyADRDto();
        monthlyADRDto.setProjectionMonthYear(JavaLocalDateUtils.toJavaLocalDate(localDate));
        monthlyADRDto.setMonthlyRoomSold(totalRoomsSold);
        monthlyADRDto.setMonthlyRevenue(BigDecimal.valueOf(totalADR*totalRoomsSold));
        monthlyADRDto.setMonthlyADR(BigDecimal.valueOf(totalADR));
        monthlyADRDto.setMonthlyCapacity(1900);
        return monthlyADRDto;
    }

    private static MonthlyMSADRDto getMonthlyMSADRDto(LocalDate localDate, String mktSegCode, Integer rooms, Integer adr) {
        MonthlyMSADRDto msADRDto2 = new MonthlyMSADRDto();
        msADRDto2.setMonthlyRoomSold(rooms);
        msADRDto2.setProjectionMonthYear(JavaLocalDateUtils.toJavaLocalDate(localDate));
        msADRDto2.setMktSegCode("MS2");
        msADRDto2.setMonthlyADR(BigDecimal.valueOf(adr));
        msADRDto2.setMonthlyRevenue(BigDecimal.valueOf(adr*rooms));
        return msADRDto2;
    }
}