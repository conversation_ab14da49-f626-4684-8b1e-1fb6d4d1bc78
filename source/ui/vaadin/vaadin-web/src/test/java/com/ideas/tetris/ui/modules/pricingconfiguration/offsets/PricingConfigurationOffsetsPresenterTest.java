package com.ideas.tetris.ui.modules.pricingconfiguration.offsets;

import com.ideas.infra.tetris.security.domain.TetrisPermissionKey;
import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.configparams.GUIConfigParamName;
import com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomClass;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomType;
import com.ideas.tetris.pacman.services.accommodation.service.AccomClassPriceRankService;
import com.ideas.tetris.pacman.services.accommodation.service.AccommodationService;
import com.ideas.tetris.pacman.services.agilerates.configuration.dto.IndependentProductConfigurationDTO;
import com.ideas.tetris.pacman.services.agilerates.configuration.service.AgileRatesConfigurationService;
import com.ideas.tetris.pacman.services.bestavailablerate.AccomTypeSupplementService;
import com.ideas.tetris.pacman.services.bestavailablerate.PricingConfigurationValidationService;
import com.ideas.tetris.pacman.services.bestavailablerate.dto.CPOffsetConfigDTO;
import com.ideas.tetris.pacman.services.bestavailablerate.dto.CPOffsetConfigPeriodDTO;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.AccomTypeSupplement;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.CPConfigOffsetAccomType;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.OccupancyType;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.OffsetMethod;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.perpersonpricing.MaximumOccupantsEntity;
import com.ideas.tetris.pacman.services.perpersonpricing.OccupantBucketEntity;
import com.ideas.tetris.pacman.services.perpersonpricing.PerPersonPricingService;
import com.ideas.tetris.pacman.services.pricingconfiguration.dto.PricingConfigurationDTO;
import com.ideas.tetris.pacman.services.pricingconfiguration.entity.PricingAccomClass;
import com.ideas.tetris.pacman.services.pricingconfiguration.entity.PricingBaseAccomType;
import com.ideas.tetris.pacman.services.pricingconfiguration.service.PricingConfigurationLTBDEService;
import com.ideas.tetris.pacman.services.pricingconfiguration.service.PricingConfigurationOffsetService;
import com.ideas.tetris.pacman.services.pricingconfiguration.service.PricingConfigurationService;
import com.ideas.tetris.pacman.services.pricingconfiguration.service.SeasonNameTooLargeException;
import com.ideas.tetris.pacman.services.product.Product;
import com.ideas.tetris.pacman.services.property.PropertyService;
import com.ideas.tetris.pacman.services.roomtyperecoding.services.RoomTypeRecodingService;
import com.ideas.tetris.pacman.services.tax.entity.Tax;
import com.ideas.tetris.pacman.services.tax.service.TaxService;
import com.ideas.tetris.pacman.util.SeasonService;
import com.ideas.tetris.platform.common.contextholder.PacmanThreadLocalContextHolder;
import com.ideas.tetris.platform.common.xml.schema.workcontext.v1.WorkContextType;
import com.ideas.tetris.platform.services.Stage;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import com.ideas.tetris.ui.VaadinUIBaseJupiterTest;
import com.ideas.tetris.ui.common.cdi.cdiutils.Lang;
import com.ideas.tetris.ui.common.component.SeasonsFilterBar;
import com.ideas.tetris.ui.common.security.PropertyState;
import com.ideas.tetris.ui.common.security.UiContext;
import com.ideas.tetris.ui.common.util.UiUtils;
import com.ideas.tetris.ui.modules.agilerates.AgileRatesPresenter;
import com.ideas.tetris.ui.modules.reports.usermodified.Status;
import org.apache.commons.collections.CollectionUtils;
import org.apache.log4j.Logger;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.joda.time.LocalDate;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InOrder;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.mockito.stubbing.Answer;

import java.io.FileInputStream;
import java.io.IOException;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import static com.ideas.tetris.pacman.services.bestavailablerate.entity.OffsetMethod.FIXED_OFFSET;
import static com.ideas.tetris.pacman.services.bestavailablerate.entity.OffsetMethod.PERCENTAGE;
import static com.ideas.tetris.ui.common.util.UiUtils.getText;
import static java.math.BigDecimal.ONE;
import static java.math.BigDecimal.TEN;
import static java.math.BigDecimal.ZERO;
import static java.math.BigDecimal.valueOf;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.hasEntry;
import static org.hamcrest.Matchers.hasKey;
import static org.hamcrest.Matchers.is;
import static org.hamcrest.Matchers.not;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.anyBoolean;
import static org.mockito.Mockito.anySet;
import static org.mockito.Mockito.atLeastOnce;
import static org.mockito.Mockito.doAnswer;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class PricingConfigurationOffsetsPresenterTest extends VaadinUIBaseJupiterTest {

    @InjectMocks
    private PricingConfigurationOffsetsPresenter pricingConfigurationOffsetsPresenter;

    @Mock
    private AgileRatesPresenter agileRatesPresenter;
    @Mock
    private PricingConfigurationOffsetsView view;
    @Mock
    PacmanConfigParamsService pacmanConfigParamsService;
    @Mock
    PricingConfigurationOffsetService offsetService;
    @Mock
    AgileRatesConfigurationService agileRatesConfigurationService;
    @Mock
    UiContext uiContext;
    @Mock
    SeasonService seasonService;
    @Mock
    PricingConfigurationValidationService pricingConfigurationValidationService;
    @Mock
    PricingConfigurationService pricingConfigurationService;
    @Mock
    PropertyService propertyService;
    @Mock
    Lang lang;
    @Mock
    PerPersonPricingService perPersonPricingService;
    @Mock
    RoomTypeRecodingService roomTypeRecodingService;
    @Mock
    DateService dateService;
    @Mock
    AccomClassPriceRankService accomClassPriceRankService;
    @Mock
    AccommodationService roomClassService;
    @Mock
    AccomTypeSupplementService accomTypeSupplementService;

    @Mock
    private PricingConfigurationLTBDEService pricingConfigurationLTBDEService;

    @Mock
    private TaxService taxService;
    private ExcelOffsetUploadValidator excelOffsetUploadValidator;
    private CPConfigOffsetAccomType cpConfigOffsetAccomType;
    private AccomType baseAccomType;
    private Set<String> roomClassesWithOffsetViolation;
    private String warningMessage = null;
    private PricingConfigurationOffsetWrapper pricingConfigurationOffsetWrapper;
    private BigDecimal negative99 = valueOf(-99);
    private BigDecimal negative100 = valueOf(-100);
    private LocalDate localDate = new LocalDate();
    private static final String PAST_SEASON = "pastSeason";
    private static final String PRESENT_SEASON = "presentSeason";
    private static final String FUTURE_SEASON = "futureSeason";

    @BeforeEach
    public void setUp() throws Exception {
        pricingConfigurationOffsetsPresenter = new PricingConfigurationOffsetsPresenter();
        pricingConfigurationOffsetsPresenter.setViewForTesting(view);
        MockitoAnnotations.initMocks(this);
        WorkContextType workContextType = new WorkContextType();
        workContextType.setClientCode("BSTN");
        PacmanThreadLocalContextHolder.setWorkContext(workContextType);

        pricingConfigurationOffsetsPresenter.setPerPersonPricingEnabled(false);
        pricingConfigurationOffsetsPresenter.setChildAgeBucketsEnabled(false);
        pricingConfigurationOffsetsPresenter.setBaseOccupancyType(OccupancyType.SINGLE);
        pricingConfigurationOffsetsPresenter.setRoomTypeRecodingUIEnabled(true);
        pricingConfigurationOffsetsPresenter.setIndependentProductsEnabled(false);

        when(accomClassPriceRankService.getAccomClassPriceRank()).thenReturn(new ArrayList<>());
        when(roomClassService.getAllActiveAccomTypes()).thenReturn(new ArrayList<>());
        when(uiContext.getSystemCaughtUpDateAsLocalDate()).thenReturn(localDate);
        pricingConfigurationOffsetsPresenter.systemDateAsLocalDate = localDate;
        when(pricingConfigurationService.getBaseOccupancyType()).thenReturn(OccupancyType.SINGLE);
        when(offsetService.retrieveOffsetConfigDTO(new ArrayList<>(), OccupancyType.SINGLE, new ArrayList<>(), null)).thenReturn(getOffsetConfigDTO());
        setUpValidationTests();
        PropertyState propertyState = new PropertyState();
        propertyState.setReadOnly(true);
        when(uiContext.getPropertyState()).thenReturn(propertyState);
    }

    private CPOffsetConfigDTO getOffsetConfigDTO() {
        CPOffsetConfigDTO cpOffsetConfigDTO = new CPOffsetConfigDTO();
        return cpOffsetConfigDTO;
    }

    @Test
    public void testOnViewOpenedTrue() {
        pricingConfigurationOffsetsPresenter.setSeasons(new ArrayList<>());
        pricingConfigurationOffsetsPresenter.setPricingAccomClasses(new ArrayList<>());
        pricingConfigurationOffsetsPresenter.setCpConfigOffsetAccomTypes(new ArrayList<>());

        pricingConfigurationOffsetsPresenter.onViewOpened(createPricingConfigurationDTOWithPrimaryPricedProduct(true));
        assertTrue(pricingConfigurationOffsetsPresenter.getIsGroupPricing());
    }

    @Test
    public void testOnViewOpenedFalse() {
        pricingConfigurationOffsetsPresenter.setSeasons(new ArrayList<>());
        pricingConfigurationOffsetsPresenter.setPricingAccomClasses(new ArrayList<>());
        pricingConfigurationOffsetsPresenter.setCpConfigOffsetAccomTypes(new ArrayList<>());

        pricingConfigurationOffsetsPresenter.onViewOpened(createPricingConfigurationDTOWithPrimaryPricedProduct(false));
        assertFalse(pricingConfigurationOffsetsPresenter.getIsGroupPricing());
    }

    @Test
    public void testOnViewOpenedNull() {
        pricingConfigurationOffsetsPresenter.setSeasons(new ArrayList<>());
        pricingConfigurationOffsetsPresenter.setPricingAccomClasses(new ArrayList<>());
        pricingConfigurationOffsetsPresenter.setCpConfigOffsetAccomTypes(new ArrayList<>());

        pricingConfigurationOffsetsPresenter.onViewOpened(createPricingConfigurationDTOWithPrimaryPricedProduct(null));
        assertNull(pricingConfigurationOffsetsPresenter.getIsGroupPricing());
    }

    @Test
    public void shouldKeepSaveButtonOutOfTheReadOnlyStateWhenMissingRTAlertIsOpenAndGroupPricingIsAbsent() {
        pricingConfigurationOffsetsPresenter.setSeasons(new ArrayList<>());
        pricingConfigurationOffsetsPresenter.setPricingAccomClasses(new ArrayList<>());
        pricingConfigurationOffsetsPresenter.setCpConfigOffsetAccomTypes(new ArrayList<>());
        when(roomTypeRecodingService.isExistingMissingRoomTypeAlertOpen()).thenReturn(Boolean.TRUE);

        pricingConfigurationOffsetsPresenter.onViewOpened(createPricingConfigurationDTO(null));
        assertNull(pricingConfigurationOffsetsPresenter.getIsGroupPricing());
    }

    @Test
    public void shouldKeepSaveButtonOutOfTheReadOnlyStateWhenMissingRTAlertIsOpenAndGroupPricingIsEnabled() {
        pricingConfigurationOffsetsPresenter.setSeasons(new ArrayList<>());
        pricingConfigurationOffsetsPresenter.setPricingAccomClasses(new ArrayList<>());
        pricingConfigurationOffsetsPresenter.setCpConfigOffsetAccomTypes(new ArrayList<>());
        when(roomTypeRecodingService.isExistingMissingRoomTypeAlertOpen()).thenReturn(Boolean.TRUE);

        pricingConfigurationOffsetsPresenter.onViewOpened(createPricingConfigurationDTOWithPrimaryPricedProduct(true));
        assertTrue(pricingConfigurationOffsetsPresenter.getIsGroupPricing());
    }

    @Test
    public void shouldKeepSaveButtonOutOfTheReadOnlyStateWhenMissingRTAlertIsOpenAndGroupPricingIsDisabled() {
        pricingConfigurationOffsetsPresenter.setSeasons(new ArrayList<>());
        pricingConfigurationOffsetsPresenter.setPricingAccomClasses(new ArrayList<>());
        pricingConfigurationOffsetsPresenter.setCpConfigOffsetAccomTypes(new ArrayList<>());
        when(roomTypeRecodingService.isExistingMissingRoomTypeAlertOpen()).thenReturn(Boolean.TRUE);

        pricingConfigurationOffsetsPresenter.onViewOpened(createPricingConfigurationDTOWithPrimaryPricedProduct(false));
        assertFalse(pricingConfigurationOffsetsPresenter.getIsGroupPricing());
    }

    @Test
    public void getPermissionForPage() {
        pricingConfigurationOffsetsPresenter.setGroupPricing(true);
        assertEquals(TetrisPermissionKey.FUNCTION_GROUP_PRICING_CONFIG_RATE, pricingConfigurationOffsetsPresenter.getPermissionForPage());

        pricingConfigurationOffsetsPresenter.setGroupPricing(false);
        assertEquals(TetrisPermissionKey.FUNCTION_SPACE_CONFIGURATION_RATE, pricingConfigurationOffsetsPresenter.getPermissionForPage());

        pricingConfigurationOffsetsPresenter.setGroupPricing(null);
        when(pacmanConfigParamsService.getParameterValueByClientLevel(FeatureTogglesConfigParamName.PRICING_CONFIGURATION_PERMISSIONS_ENABLED.value())).thenReturn("true");
        assertEquals(TetrisPermissionKey.PRICING_CONFIGURATION_OFFSETS, pricingConfigurationOffsetsPresenter.getPermissionForPage());

        when(pacmanConfigParamsService.getParameterValueByClientLevel(FeatureTogglesConfigParamName.PRICING_CONFIGURATION_PERMISSIONS_ENABLED.value())).thenReturn("false");
        assertEquals(TetrisPermissionKey.PRICING_CONFIGURATION_TRANSIENT_CEIL_AND_FLOOR, pricingConfigurationOffsetsPresenter.getPermissionForPage());
    }

    @Test
    public void isEffectiveReadOnlyAndMissingRoomTypeAlertNotOpen() {
        //isEffectiveReadOnly = False
        PropertyState propertyState = new PropertyState();
        propertyState.setReadOnly(false);
        propertyState.setReadOnlyOverride(false);
        when(uiContext.getPropertyState()).thenReturn(propertyState);
        pricingConfigurationOffsetsPresenter.setMissingRoomTypeAlertOpen(false);

        assertFalse(pricingConfigurationOffsetsPresenter.isEffectiveReadOnlyAndMissingRoomTypeAlertNotOpen());

        pricingConfigurationOffsetsPresenter.setMissingRoomTypeAlertOpen(true);
        assertFalse(pricingConfigurationOffsetsPresenter.isEffectiveReadOnlyAndMissingRoomTypeAlertNotOpen());

        //isEffectiveReadOnly = true
        PropertyState propertyState2 = new PropertyState();
        propertyState2.setReadOnly(true);
        propertyState2.setReadOnlyOverride(false);
        when(uiContext.getPropertyState()).thenReturn(propertyState2);

        assertFalse(pricingConfigurationOffsetsPresenter.isEffectiveReadOnlyAndMissingRoomTypeAlertNotOpen());

        pricingConfigurationOffsetsPresenter.setMissingRoomTypeAlertOpen(false);
        assertTrue(pricingConfigurationOffsetsPresenter.isEffectiveReadOnlyAndMissingRoomTypeAlertNotOpen());
    }

    @Test
    public void testCancel() throws Exception {
        pricingConfigurationOffsetsPresenter.setPricingConfigurationDTO(createPricingConfigurationDTOWithPrimaryPricedProduct(null));
        pricingConfigurationOffsetsPresenter.setSeasons(new ArrayList<>());
        pricingConfigurationOffsetsPresenter.setPricingAccomClasses(new ArrayList<>());

        pricingConfigurationOffsetsPresenter.cancel();
        verify(offsetService, Mockito.times(1)).retrieveOffsetConfigNoPriceExcluded(0, true, 1);
        verify(offsetService, Mockito.times(1)).retrieveOffsetConfigDTO(new ArrayList<>(), OccupancyType.SINGLE, new ArrayList<>(), null);
    }

    @Test
    public void testSave() throws Exception {
        pricingConfigurationOffsetsPresenter.setPricingConfigurationDTO(createPricingConfigurationDTOWithPrimaryPricedProduct(null));
        pricingConfigurationOffsetsPresenter.setupParameters();
        pricingConfigurationOffsetsPresenter.loadData();
        pricingConfigurationOffsetsPresenter.init(false);
        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(java.time.LocalDate.parse("2023-12-01"));

        PricingConfigurationOffsetWrapper pricingConfigurationOffsetWrapper = new PricingConfigurationOffsetWrapper();
        CPConfigOffsetAccomType cpConfigOffsetAccomType = new CPConfigOffsetAccomType();
        cpConfigOffsetAccomType.setId(99);
        pricingConfigurationOffsetWrapper.setCpConfigOffsetAccomType(cpConfigOffsetAccomType);
        pricingConfigurationOffsetsPresenter.save(Arrays.asList(pricingConfigurationOffsetWrapper));
        verify(offsetService, Mockito.times(1)).updatePricingOffsets(anyList(), anyList(), anyList(), anyList(), anySet(), any(), anyBoolean());
        verify(pricingConfigurationLTBDEService).enabledLTBDEIfApplicableForOffsetChange(anyList(), eq(java.time.LocalDate.parse("2023-12-01")));
    }

    @Test
    public void testSaveWhenIsCPFloorAndCeilingSoftWarningEnabledOn() {
        pricingConfigurationOffsetsPresenter.setPricingConfigurationDTO(createPricingConfigurationDTOWithPrimaryPricedProduct(null));
        pricingConfigurationOffsetsPresenter.setupParameters();
        pricingConfigurationOffsetsPresenter.loadData();
        pricingConfigurationOffsetsPresenter.init(false);
        PricingConfigurationOffsetWrapper pricingConfigurationOffsetWrapper = new PricingConfigurationOffsetWrapper();
        CPConfigOffsetAccomType cpConfigOffsetAccomType = new CPConfigOffsetAccomType();
        cpConfigOffsetAccomType.setId(99);
        pricingConfigurationOffsetWrapper.setCpConfigOffsetAccomType(cpConfigOffsetAccomType);
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_CP_FLOOR_AND_CEILING_SOFT_WARNING)).thenReturn(true);
        pricingConfigurationOffsetsPresenter.save(Arrays.asList(pricingConfigurationOffsetWrapper));
        verify(offsetService, Mockito.times(1)).updatePricingOffsets(anyList(), anyList(), anyList(), anyList(), anySet(), any(), anyBoolean());
    }

    @Test
    public void save_priceExcludedWithTaxConfiguration() {
        pricingConfigurationOffsetsPresenter.setPricingConfigurationDTO(createPricingConfigurationDTOWithPrimaryPricedProduct(null));
        pricingConfigurationOffsetsPresenter.setupParameters();
        pricingConfigurationOffsetsPresenter.loadData();
        pricingConfigurationOffsetsPresenter.init(false);

        PricingConfigurationOffsetWrapper wrapper = new PricingConfigurationOffsetWrapper();

        CPConfigOffsetAccomType offsetAccomType = new CPConfigOffsetAccomType();
        wrapper.setCpConfigOffsetAccomType(offsetAccomType);
        pricingConfigurationOffsetsPresenter.save(Collections.singletonList(wrapper));

        verify(offsetService).updateForTax(anyList(), anyBoolean());
        verify(offsetService, Mockito.times(1)).updatePricingOffsets(anyList(), anyList(), anyList(), anyList(), anySet(), any(), anyBoolean());
    }

    @Test
    public void save_priceExcludedWithTaxConfigurationWhenIsCPFloorAndCeilingSoftWarningEnabledOn() {
        pricingConfigurationOffsetsPresenter.setPricingConfigurationDTO(createPricingConfigurationDTOWithPrimaryPricedProduct(null));
        pricingConfigurationOffsetsPresenter.setupParameters();
        pricingConfigurationOffsetsPresenter.loadData();
        pricingConfigurationOffsetsPresenter.init(false);

        PricingConfigurationOffsetWrapper wrapper = new PricingConfigurationOffsetWrapper();

        CPConfigOffsetAccomType offsetAccomType = new CPConfigOffsetAccomType();
        wrapper.setCpConfigOffsetAccomType(offsetAccomType);
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_CP_FLOOR_AND_CEILING_SOFT_WARNING)).thenReturn(true);
        pricingConfigurationOffsetsPresenter.save(Collections.singletonList(wrapper));

        verify(offsetService).updateForTax(anyList(), anyBoolean());
        verify(offsetService, Mockito.times(1)).updatePricingOffsets(anyList(), anyList(), anyList(), anyList(), anySet(), any(), anyBoolean());
    }

    @Test
    public void testGetHelpId_functionSpace() {
        pricingConfigurationOffsetsPresenter.setSeasons(new ArrayList<>());
        pricingConfigurationOffsetsPresenter.setPricingAccomClasses(new ArrayList<>());
        pricingConfigurationOffsetsPresenter.setCpConfigOffsetAccomTypes(new ArrayList<>());
        pricingConfigurationOffsetsPresenter.onViewOpened(createPricingConfigurationDTO(false));
        assertEquals(PricingConfigurationOffsetsPresenter.OFFSETS_FUNCTION_SPACE_HELP_ID, pricingConfigurationOffsetsPresenter.getHelpId());
    }

    @Test
    public void testGetHelpId_groupPricing() {
        pricingConfigurationOffsetsPresenter.setSeasons(new ArrayList<>());
        pricingConfigurationOffsetsPresenter.setPricingAccomClasses(new ArrayList<>());
        pricingConfigurationOffsetsPresenter.setCpConfigOffsetAccomTypes(new ArrayList<>());

        pricingConfigurationOffsetsPresenter.onViewOpened(createPricingConfigurationDTO(true));
        assertEquals(PricingConfigurationOffsetsPresenter.OFFSETS_GROUP_HELP_ID, pricingConfigurationOffsetsPresenter.getHelpId());
    }

    @Test
    public void testOffsetNegativePercentageValidation_negative100Percentage() {

        Set<String> roomClassesWithOffsetViolation = new HashSet<>();
        roomClassesWithOffsetViolation.add("STANDARD");
        BigDecimal negative100 = new BigDecimal(-100);
        AccomType lowestAccomType2 = new AccomType();
        lowestAccomType2.setName("STANDARD");

        CPConfigOffsetAccomType lessThanFloorPercentageAccomType = new CPConfigOffsetAccomType();
        lessThanFloorPercentageAccomType.setAccomType(lowestAccomType2);
        lessThanFloorPercentageAccomType.setSundayOffsetValue(negative100);
        lessThanFloorPercentageAccomType.setMondayOffsetValue(negative100);
        lessThanFloorPercentageAccomType.setTuesdayOffsetValue(negative100);
        lessThanFloorPercentageAccomType.setWednesdayOffsetValue(negative100);
        lessThanFloorPercentageAccomType.setThursdayOffsetValue(negative100);
        lessThanFloorPercentageAccomType.setFridayOffsetValue(negative100);
        lessThanFloorPercentageAccomType.setSaturdayOffsetValue(negative100);
        lessThanFloorPercentageAccomType.setOffsetMethod(OffsetMethod.PERCENTAGE);

        List<CPConfigOffsetAccomType> offsetsToSave = Arrays.asList(lessThanFloorPercentageAccomType);

        assertEquals(pricingConfigurationOffsetsPresenter.offsetNegativePercentageValidation(offsetsToSave), roomClassesWithOffsetViolation);
    }

    @Test
    public void testOffsetNegativePercentageValidation_negative99Percentage() throws Exception {

        Set<String> roomClassesWithOffsetViolation = new HashSet<>();
        BigDecimal negative99 = new BigDecimal(-99);
        AccomType lowestAccomType2 = new AccomType();
        lowestAccomType2.setName("STANDARD");

        CPConfigOffsetAccomType lessThanFloorPercentageAccomType = new CPConfigOffsetAccomType();
        lessThanFloorPercentageAccomType.setAccomType(lowestAccomType2);
        lessThanFloorPercentageAccomType.setSundayOffsetValue(null);
        lessThanFloorPercentageAccomType.setMondayOffsetValue(negative99);
        lessThanFloorPercentageAccomType.setTuesdayOffsetValue(negative99);
        lessThanFloorPercentageAccomType.setWednesdayOffsetValue(negative99);
        lessThanFloorPercentageAccomType.setThursdayOffsetValue(negative99);
        lessThanFloorPercentageAccomType.setFridayOffsetValue(negative99);
        lessThanFloorPercentageAccomType.setSaturdayOffsetValue(negative99);
        lessThanFloorPercentageAccomType.setOffsetMethod(OffsetMethod.PERCENTAGE);

        List<CPConfigOffsetAccomType> offsetsToSave = Arrays.asList(lessThanFloorPercentageAccomType);

        assertEquals(pricingConfigurationOffsetsPresenter.offsetNegativePercentageValidation(offsetsToSave), roomClassesWithOffsetViolation);
    }

    @Test
    public void testOffsetNegativePercentageValidation_negative100Fixed() throws Exception {

        Set<String> roomClassesWithOffsetViolation = new HashSet<>();
        BigDecimal negative100 = new BigDecimal(-100);
        AccomType lowestAccomType2 = new AccomType();
        lowestAccomType2.setName("STANDARD");

        CPConfigOffsetAccomType lessThanFloorPercentageAccomType = new CPConfigOffsetAccomType();
        lessThanFloorPercentageAccomType.setAccomType(lowestAccomType2);
        lessThanFloorPercentageAccomType.setSundayOffsetValue(negative100);
        lessThanFloorPercentageAccomType.setMondayOffsetValue(negative100);
        lessThanFloorPercentageAccomType.setTuesdayOffsetValue(negative100);
        lessThanFloorPercentageAccomType.setWednesdayOffsetValue(negative100);
        lessThanFloorPercentageAccomType.setThursdayOffsetValue(negative100);
        lessThanFloorPercentageAccomType.setFridayOffsetValue(negative100);
        lessThanFloorPercentageAccomType.setSaturdayOffsetValue(negative100);
        lessThanFloorPercentageAccomType.setOffsetMethod(FIXED_OFFSET);

        List<CPConfigOffsetAccomType> offsetsToSave = Arrays.asList(lessThanFloorPercentageAccomType);

        assertEquals(pricingConfigurationOffsetsPresenter.offsetNegativePercentageValidation(offsetsToSave), roomClassesWithOffsetViolation);
    }

    @Test
    public void testNegativePercentageOffsetWarningMessage() {
        pricingConfigurationOffsetsPresenter.setPricingConfigurationDTO(createPricingConfigurationDTOWithPrimaryPricedProduct(null));
        when(pacmanConfigParamsService.getBooleanParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED)).thenReturn(true);
        pricingConfigurationOffsetsPresenter.setupParameters();
        pricingConfigurationOffsetsPresenter.loadData();
        pricingConfigurationOffsetsPresenter.init(false);
        BigDecimal negative100 = new BigDecimal(-100);
        cpConfigOffsetAccomType.setOffsetMethod(OffsetMethod.PERCENTAGE);
        cpConfigOffsetAccomType.setSundayOffsetValue(negative100);
        when(lang.getText("breaksRoomTypeOffsetLowerThanFloor", warningMessage)).thenReturn(getText("breaksRoomTypeOffsetLowerThanFloor", warningMessage));
        when(pricingConfigurationService.getBaseOccupancyType()).thenReturn(OccupancyType.SINGLE);

        pricingConfigurationOffsetsPresenter.save(Arrays.asList(pricingConfigurationOffsetWrapper));
        verify(view).showWarning(getText("breaksRoomTypeOffsetLowerThanFloor", warningMessage));
    }

    @Test
    public void testNegativePercentageOffsetWarningMessageWhenIsCPFloorAndCeilingSoftWarningEnabledOn() {
        when(pacmanConfigParamsService.getBooleanParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED)).thenReturn(true);
        pricingConfigurationOffsetsPresenter.setPricingConfigurationDTO(createPricingConfigurationDTOWithPrimaryPricedProduct(null));
        pricingConfigurationOffsetsPresenter.setupParameters();
        pricingConfigurationOffsetsPresenter.loadData();
        pricingConfigurationOffsetsPresenter.init(false);
        BigDecimal negative100 = new BigDecimal(-100);
        cpConfigOffsetAccomType.setOffsetMethod(OffsetMethod.PERCENTAGE);
        cpConfigOffsetAccomType.setSundayOffsetValue(negative100);
        when(lang.getText("breaksRoomTypeOffsetLowerThanFloor", warningMessage)).thenReturn(getText("breaksRoomTypeOffsetLowerThanFloor", warningMessage));
        when(pricingConfigurationService.getBaseOccupancyType()).thenReturn(OccupancyType.SINGLE);
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_CP_FLOOR_AND_CEILING_SOFT_WARNING)).thenReturn(true);

        pricingConfigurationOffsetsPresenter.save(Arrays.asList(pricingConfigurationOffsetWrapper));
        verify(view).showWarning(getText("breaksRoomTypeOffsetLowerThanFloor", warningMessage));
    }

    @Test
    public void testTransientOffsetWarningMessage() {
        pricingConfigurationOffsetsPresenter.setPricingConfigurationDTO(createPricingConfigurationDTOWithPrimaryPricedProduct(null));
        when(pacmanConfigParamsService.getBooleanParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED)).thenReturn(true);
        pricingConfigurationOffsetsPresenter.setupParameters();
        pricingConfigurationOffsetsPresenter.loadData();
        pricingConfigurationOffsetsPresenter.init(false);
        when(pricingConfigurationValidationService.validateSaveOffsetLowerThanTransientFloor(any(), any())).thenReturn(roomClassesWithOffsetViolation);
        when(lang.getText("breaksRoomTypeOffsetLowerThanFloor", warningMessage)).thenReturn(getText("breaksRoomTypeOffsetLowerThanFloor", warningMessage));
        when(pricingConfigurationService.getBaseOccupancyType()).thenReturn(OccupancyType.SINGLE);

        pricingConfigurationOffsetsPresenter.save(Arrays.asList(pricingConfigurationOffsetWrapper));
        verify(view).showWarning(getText("breaksRoomTypeOffsetLowerThanFloor", warningMessage));
    }

    @Test
    public void testTransientOffsetWarningMessageWhenIsCPFloorAndCeilingSoftWarningEnabledOn() {
        pricingConfigurationOffsetsPresenter.setPricingConfigurationDTO(createPricingConfigurationDTOWithPrimaryPricedProduct(null));
        when(pacmanConfigParamsService.getBooleanParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED)).thenReturn(true);
        pricingConfigurationOffsetsPresenter.setupParameters();
        pricingConfigurationOffsetsPresenter.loadData();
        pricingConfigurationOffsetsPresenter.init(false);
        when(pricingConfigurationValidationService.validateSaveOffsetLowerThanTransientFloor(any(), any())).thenReturn(roomClassesWithOffsetViolation);
        when(lang.getText("breaksRoomTypeOffsetLowerThanFloor", warningMessage)).thenReturn(getText("breaksRoomTypeOffsetLowerThanFloor", warningMessage));
        when(pricingConfigurationService.getBaseOccupancyType()).thenReturn(OccupancyType.SINGLE);
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_CP_FLOOR_AND_CEILING_SOFT_WARNING)).thenReturn(true);
        pricingConfigurationOffsetsPresenter.save(Arrays.asList(pricingConfigurationOffsetWrapper));
        verify(view).showWarning(getText("breaksRoomTypeOffsetLowerThanFloor", warningMessage));
    }

    @Test
    public void tesDefaultOffsetWarningMessage() {
        pricingConfigurationOffsetsPresenter.setPricingConfigurationDTO(createPricingConfigurationDTOWithPrimaryPricedProduct(null));
        when(pacmanConfigParamsService.getBooleanParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED)).thenReturn(true);
        when(pricingConfigurationValidationService.validateSaveDefaultOffsets(any(), any(), any(), any(), any())).thenReturn(roomClassesWithOffsetViolation);
        when(lang.getText("breaksRoomClassRank")).thenReturn(getText("breaksRoomClassRank"));

        pricingConfigurationOffsetsPresenter.setAdvancedPriceRankingEnabled(false);
        pricingConfigurationOffsetsPresenter.setupParameters();
        pricingConfigurationOffsetsPresenter.loadData();
        pricingConfigurationOffsetsPresenter.init(false);

        pricingConfigurationOffsetsPresenter.save(Arrays.asList(pricingConfigurationOffsetWrapper));
        verify(view).showWarning(getText("breaksRoomClassRank") + "\n" + warningMessage);

        pricingConfigurationOffsetsPresenter.setAdvancedPriceRankingEnabled(true);

        pricingConfigurationOffsetsPresenter.save(Arrays.asList(pricingConfigurationOffsetWrapper));
        verify(view).showSaveSuccessMessage();
    }

    @Test
    public void tesDefaultOffsetWarningMessageWhenIsCPFloorAndCeilingSoftWarningEnabledOn() {
        pricingConfigurationOffsetsPresenter.setPricingConfigurationDTO(createPricingConfigurationDTOWithPrimaryPricedProduct(null));
        when(pacmanConfigParamsService.getBooleanParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED)).thenReturn(true);
        when(pricingConfigurationValidationService.validateSaveDefaultOffsets(any(), any(), any(), any(), any())).thenReturn(roomClassesWithOffsetViolation);
        when(lang.getText("breaksRoomClassRank")).thenReturn(getText("breaksRoomClassRank"));

        pricingConfigurationOffsetsPresenter.setAdvancedPriceRankingEnabled(false);
        pricingConfigurationOffsetsPresenter.setupParameters();
        pricingConfigurationOffsetsPresenter.loadData();
        pricingConfigurationOffsetsPresenter.init(false);
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_CP_FLOOR_AND_CEILING_SOFT_WARNING)).thenReturn(true);

        pricingConfigurationOffsetsPresenter.setAdvancedPriceRankingEnabled(true);

        pricingConfigurationOffsetsPresenter.save(Arrays.asList(pricingConfigurationOffsetWrapper));
        verify(view).showSaveSuccessMessage();
    }

    @Test
    public void testValidateOffsetSeasons() {
        //create season
        PricingConfigurationOffsetWrapper pricingConfigurationOffsetWrapper = new PricingConfigurationOffsetWrapper();
        pricingConfigurationOffsetWrapper.setName("test");

        List<PricingConfigurationOffsetWrapper> pricingConfigurationOffsetWrappers = new ArrayList<>();
        pricingConfigurationOffsetWrappers.add(pricingConfigurationOffsetWrapper);

        PricingConfigurationOffsetSeasonWrapper season = new PricingConfigurationOffsetSeasonWrapper();
        season.setStartDate(localDate.minusDays(1));
        season.setEndDate(localDate.plusDays(10));
        season.setName("season");
        season.setOffsets(pricingConfigurationOffsetWrappers);

        pricingConfigurationOffsetsPresenter.setPricingConfigurationDTO(createPricingConfigurationDTOWithPrimaryPricedProduct(null));
        when(pacmanConfigParamsService.getBooleanParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED)).thenReturn(true);
        pricingConfigurationOffsetsPresenter.setAdvancedPriceRankingEnabled(false);
        pricingConfigurationOffsetsPresenter.setupParameters();
        pricingConfigurationOffsetsPresenter.loadData();
        pricingConfigurationOffsetsPresenter.init(false);
        when(pricingConfigurationValidationService.validateSaveSeasonOffsets(any(), any(), any(), any(), any(), any())).thenReturn(roomClassesWithOffsetViolation);
        when(lang.getText("breaksRoomClassRank")).thenReturn(getText("breaksRoomClassRank"));

        pricingConfigurationOffsetsPresenter.validateOffsetSeasons(season);
        verify(view).showWarning(getText("breaksRoomClassRank") + "\n" + warningMessage);

        pricingConfigurationOffsetsPresenter.setAdvancedPriceRankingEnabled(true);

        assertTrue(pricingConfigurationOffsetsPresenter.validateOffsetSeasons(season));
    }

    @Test
    public void testValidateOffsetSeasonsHavingSoftWarning() {
        //create season
        pricingConfigurationOffsetsPresenter.setPricingConfigurationDTO(createPricingConfigurationDTOWithPrimaryPricedProduct(null));
        PricingConfigurationOffsetWrapper pricingConfigurationOffsetWrapper = new PricingConfigurationOffsetWrapper();
        pricingConfigurationOffsetWrapper.setName("test");

        List<PricingConfigurationOffsetWrapper> pricingConfigurationOffsetWrappers = new ArrayList<>();
        pricingConfigurationOffsetWrappers.add(pricingConfigurationOffsetWrapper);

        PricingConfigurationOffsetSeasonWrapper season = new PricingConfigurationOffsetSeasonWrapper();
        season.setStartDate(localDate.minusDays(1));
        season.setEndDate(localDate.plusDays(10));
        season.setName("season");
        season.setOffsets(pricingConfigurationOffsetWrappers);

        when(pacmanConfigParamsService.getBooleanParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED)).thenReturn(true);
        pricingConfigurationOffsetsPresenter.setAdvancedPriceRankingEnabled(false);
        pricingConfigurationOffsetsPresenter.setupParameters();
        pricingConfigurationOffsetsPresenter.loadData();
        pricingConfigurationOffsetsPresenter.init(false);
        when(pricingConfigurationValidationService.validateSaveSeasonOffsets(any(), any(), any(), any(), any(), any())).thenReturn(roomClassesWithOffsetViolation);
        when(lang.getText("breaksRoomClassRank")).thenReturn(getText("breaksRoomClassRank"));
        assertEquals(Optional.of(getText("breaksRoomClassRank") + "\n" + warningMessage), pricingConfigurationOffsetsPresenter.validateOffsetSeasonsHavingSoftWarning(season));

        pricingConfigurationOffsetsPresenter.setAdvancedPriceRankingEnabled(true);
        assertEquals(Optional.empty(), pricingConfigurationOffsetsPresenter.validateOffsetSeasonsHavingSoftWarning(season));
    }

    @Test
    public void testValidateOffsetsWithValueMissing() {

        LinkedHashMap<PricingAccomClass, LinkedHashMap<AccomType, LinkedList<CPConfigOffsetAccomType>>> offsets = new LinkedHashMap<>();
        // Create instances of PricingAccomClass
        AccomType accomType1 = new AccomType();
        accomType1.setName("STD");
        AccomType accomType2 = new AccomType();
        PricingAccomClass pricingClass1 = new PricingAccomClass();
        pricingClass1.setAccomType(accomType1);
        PricingAccomClass pricingClass2 = new PricingAccomClass();

        CPConfigOffsetAccomType offsetAccomType1 = new CPConfigOffsetAccomType();
        offsetAccomType1.setMondayOffsetValueWithTax(new BigDecimal(12.0));
        offsetAccomType1.setTuesdayOffsetValueWithTax(new BigDecimal(12.0));
        offsetAccomType1.setWednesdayOffsetValueWithTax(new BigDecimal(12.0));
        offsetAccomType1.setThursdayOffsetValueWithTax(new BigDecimal(12.0));

        CPConfigOffsetAccomType offsetAccomType2 = new CPConfigOffsetAccomType();
        CPConfigOffsetAccomType offsetAccomType3 = new CPConfigOffsetAccomType();

        // Create the nested LinkedHashMaps and LinkedLists
        LinkedHashMap<AccomType, LinkedList<CPConfigOffsetAccomType>> innerMap1 = new LinkedHashMap<>();
        LinkedList<CPConfigOffsetAccomType> list1 = new LinkedList<>();
        list1.add(offsetAccomType1);
        list1.add(offsetAccomType2);
        innerMap1.put(accomType1, list1);

        LinkedHashMap<AccomType, LinkedList<CPConfigOffsetAccomType>> innerMap2 = new LinkedHashMap<>();
        LinkedList<CPConfigOffsetAccomType> list2 = new LinkedList<>();
        list2.add(offsetAccomType3);
        innerMap2.put(accomType2, list2);

        // Populate the top-level LinkedHashMap
        offsets.put(pricingClass1, innerMap1);
        offsets.put(pricingClass2, innerMap2);

        PricingConfigurationOffsetSeasonWrapper season = new PricingConfigurationOffsetSeasonWrapper();
        season.setStartDate(localDate.minusDays(1));
        season.setEndDate(localDate.plusDays(10));
        season.setName("season");
        season.setStartDate(localDate.minusDays(1));
        season.setEndDate(localDate.plusDays(10));
        assertEquals(true, pricingConfigurationOffsetsPresenter.isValueMissingWhileEditingSeason(offsets, season, false));

    }

    @Test
    public void testValidateOffsetsWithAllValuesPresentInAWeek() {
        LinkedHashMap<PricingAccomClass, LinkedHashMap<AccomType, LinkedList<CPConfigOffsetAccomType>>> offsets = new LinkedHashMap<>();
        // Create instances of PricingAccomClass
        AccomType accomType1 = new AccomType();
        accomType1.setName("STD");
        AccomType accomType2 = new AccomType();
        PricingAccomClass pricingClass1 = new PricingAccomClass();
        pricingClass1.setAccomType(accomType1);
        PricingAccomClass pricingClass2 = new PricingAccomClass();

        CPConfigOffsetAccomType offsetAccomType1 = new CPConfigOffsetAccomType();
        offsetAccomType1.setMondayOffsetValueWithTax(new BigDecimal(12.0));
        offsetAccomType1.setTuesdayOffsetValueWithTax(new BigDecimal(12.0));
        offsetAccomType1.setWednesdayOffsetValueWithTax(new BigDecimal(12.0));
        offsetAccomType1.setThursdayOffsetValueWithTax(new BigDecimal(12.0));
        offsetAccomType1.setFridayOffsetValueWithTax(new BigDecimal(12.0));
        offsetAccomType1.setSaturdayOffsetValueWithTax(new BigDecimal(12.0));
        offsetAccomType1.setSundayOffsetValueWithTax(new BigDecimal(12.0));

        CPConfigOffsetAccomType offsetAccomType2 = new CPConfigOffsetAccomType();
        CPConfigOffsetAccomType offsetAccomType3 = new CPConfigOffsetAccomType();

        // Create the nested LinkedHashMaps and LinkedLists
        LinkedHashMap<AccomType, LinkedList<CPConfigOffsetAccomType>> innerMap1 = new LinkedHashMap<>();
        LinkedList<CPConfigOffsetAccomType> list1 = new LinkedList<>();
        list1.add(offsetAccomType1);
        list1.add(offsetAccomType2);
        innerMap1.put(accomType1, list1);

        LinkedHashMap<AccomType, LinkedList<CPConfigOffsetAccomType>> innerMap2 = new LinkedHashMap<>();
        LinkedList<CPConfigOffsetAccomType> list2 = new LinkedList<>();
        list2.add(offsetAccomType3);
        innerMap2.put(accomType2, list2);

        // Populate the top-level LinkedHashMap
        offsets.put(pricingClass1, innerMap1);
        offsets.put(pricingClass2, innerMap2);

        PricingConfigurationOffsetSeasonWrapper season = new PricingConfigurationOffsetSeasonWrapper();
        season.setStartDate(localDate.minusDays(1));
        season.setEndDate(localDate.plusDays(10));
        season.setName("season");
        season.setStartDate(localDate.minusDays(1));
        season.setEndDate(localDate.plusDays(10));
        assertEquals(false, pricingConfigurationOffsetsPresenter.isValueMissingWhileEditingSeason(offsets, season, false));

    }

    @Test
    public void testValidateOffsetsWithLessThanSevenValuesPresentInAWeekAndNoOfDaysLessThanNonNullOffset() {
        LinkedHashMap<PricingAccomClass, LinkedHashMap<AccomType, LinkedList<CPConfigOffsetAccomType>>> offsets = new LinkedHashMap<>();
        // Create instances of PricingAccomClass
        AccomType accomType1 = new AccomType();
        accomType1.setName("STD");
        AccomType accomType2 = new AccomType();
        PricingAccomClass pricingClass1 = new PricingAccomClass();
        pricingClass1.setAccomType(accomType1);
        PricingAccomClass pricingClass2 = new PricingAccomClass();

        CPConfigOffsetAccomType offsetAccomType1 = new CPConfigOffsetAccomType();
        offsetAccomType1.setMondayOffsetValueWithTax(new BigDecimal(12.0));
        offsetAccomType1.setTuesdayOffsetValueWithTax(new BigDecimal(12.0));
        offsetAccomType1.setWednesdayOffsetValueWithTax(new BigDecimal(12.0));
        offsetAccomType1.setThursdayOffsetValueWithTax(new BigDecimal(12.0));
        offsetAccomType1.setFridayOffsetValueWithTax(new BigDecimal(12.0));
        offsetAccomType1.setSaturdayOffsetValueWithTax(new BigDecimal(12.0));

        CPConfigOffsetAccomType offsetAccomType2 = new CPConfigOffsetAccomType();
        CPConfigOffsetAccomType offsetAccomType3 = new CPConfigOffsetAccomType();

        // Create the nested LinkedHashMaps and LinkedLists
        LinkedHashMap<AccomType, LinkedList<CPConfigOffsetAccomType>> innerMap1 = new LinkedHashMap<>();
        LinkedList<CPConfigOffsetAccomType> list1 = new LinkedList<>();
        list1.add(offsetAccomType1);
        list1.add(offsetAccomType2);
        innerMap1.put(accomType1, list1);

        LinkedHashMap<AccomType, LinkedList<CPConfigOffsetAccomType>> innerMap2 = new LinkedHashMap<>();
        LinkedList<CPConfigOffsetAccomType> list2 = new LinkedList<>();
        list2.add(offsetAccomType3);
        innerMap2.put(accomType2, list2);

        // Populate the top-level LinkedHashMap
        offsets.put(pricingClass1, innerMap1);
        offsets.put(pricingClass2, innerMap2);

        PricingConfigurationOffsetSeasonWrapper season = new PricingConfigurationOffsetSeasonWrapper();
        season.setStartDate(localDate.minusDays(1));
        season.setEndDate(localDate.plusDays(3));
        assertEquals(true, pricingConfigurationOffsetsPresenter.isValueMissingWhileEditingSeason(offsets, season, false));

    }

   @Test
    public void testValidateOffsetsWithValueMissingForOnlyNewAddSeasons() {

        List<CPConfigOffsetAccomType> cpConfigOffsetAccomTypes = new ArrayList<>();
        CPConfigOffsetAccomType offsetAccomType1 = new CPConfigOffsetAccomType();
        offsetAccomType1.setMondayOffsetValueWithTax(new BigDecimal(12.0));
        offsetAccomType1.setTuesdayOffsetValueWithTax(new BigDecimal(12.0));
        offsetAccomType1.setWednesdayOffsetValueWithTax(new BigDecimal(12.0));
        offsetAccomType1.setThursdayOffsetValueWithTax(new BigDecimal(12.0));
        cpConfigOffsetAccomTypes.add(offsetAccomType1);
        offsetAccomType1.setStartDate(localDate);
        offsetAccomType1.setEndDate(localDate.plusDays(8));

       PricingConfigurationOffsetSeasonWrapper season = new PricingConfigurationOffsetSeasonWrapper();
       season.setStartDate(localDate.plusDays(1));
       season.setEndDate(localDate.plusDays(10));

        assertTrue(pricingConfigurationOffsetsPresenter.isValueMissingWhileFormingSeason(offsetAccomType1,season,false));
    }

    @Test
    public void testValidateOffsetsWithNoValueMissingForTheDaysBetween() {

        List<CPConfigOffsetAccomType> cpConfigOffsetAccomTypes = new ArrayList<>();
        CPConfigOffsetAccomType offsetAccomType1 = new CPConfigOffsetAccomType();
        offsetAccomType1.setMondayOffsetValueWithTax(new BigDecimal(12.0));
        offsetAccomType1.setTuesdayOffsetValueWithTax(new BigDecimal(12.0));
        offsetAccomType1.setWednesdayOffsetValueWithTax(new BigDecimal(12.0));
        offsetAccomType1.setThursdayOffsetValueWithTax(new BigDecimal(12.0));
        offsetAccomType1.setFridayOffsetValueWithTax(new BigDecimal(12.0));
        cpConfigOffsetAccomTypes.add(offsetAccomType1);
        offsetAccomType1.setStartDate(localDate);
        offsetAccomType1.setEndDate(localDate.plusDays(3));

        PricingConfigurationOffsetSeasonWrapper season = new PricingConfigurationOffsetSeasonWrapper();
        season.setStartDate(localDate.plusDays(1));
        season.setEndDate(localDate.plusDays(5));

        assertFalse(pricingConfigurationOffsetsPresenter.isValueMissingWhileFormingSeason(offsetAccomType1,season,false));
    }

    @Test
    public void testValidateOffsetsWithNoValueMissingForTheDaysBetweenWhileEditingSeason()
    {
        LinkedHashMap<PricingAccomClass, LinkedHashMap<AccomType, LinkedList<CPConfigOffsetAccomType>>> offsets = new LinkedHashMap<>();
        // Create instances of PricingAccomClass
        AccomType accomType1 = new AccomType();
        accomType1.setName("STD");
        AccomType accomType2 = new AccomType();
        PricingAccomClass pricingClass1 = new PricingAccomClass();
        pricingClass1.setAccomType(accomType1);
        PricingAccomClass pricingClass2 = new PricingAccomClass();

        CPConfigOffsetAccomType offsetAccomType1 = new CPConfigOffsetAccomType();
        offsetAccomType1.setMondayOffsetValueWithTax(new BigDecimal(12.0));
        offsetAccomType1.setTuesdayOffsetValueWithTax(new BigDecimal(12.0));
        offsetAccomType1.setWednesdayOffsetValueWithTax(new BigDecimal(12.0));
        offsetAccomType1.setThursdayOffsetValueWithTax(new BigDecimal(12.0));
        offsetAccomType1.setFridayOffsetValueWithTax(new BigDecimal(12.0));

        CPConfigOffsetAccomType offsetAccomType2 = new CPConfigOffsetAccomType();
        CPConfigOffsetAccomType offsetAccomType3 = new CPConfigOffsetAccomType();

        // Create the nested LinkedHashMaps and LinkedLists
        LinkedHashMap<AccomType, LinkedList<CPConfigOffsetAccomType>> innerMap1 = new LinkedHashMap<>();
        LinkedList<CPConfigOffsetAccomType> list1 = new LinkedList<>();
        list1.add(offsetAccomType1);
        list1.add(offsetAccomType2);
        innerMap1.put(accomType1, list1);

        LinkedHashMap<AccomType, LinkedList<CPConfigOffsetAccomType>> innerMap2 = new LinkedHashMap<>();
        LinkedList<CPConfigOffsetAccomType> list2 = new LinkedList<>();
        list2.add(offsetAccomType3);
        innerMap2.put(accomType2, list2);

        // Populate the top-level LinkedHashMap
        offsets.put(pricingClass1, innerMap1);
        offsets.put(pricingClass2, innerMap2);

        PricingConfigurationOffsetSeasonWrapper season = new PricingConfigurationOffsetSeasonWrapper();
        season.setStartDate(localDate.minusDays(1));
        season.setEndDate(localDate.plusDays(3));
        assertEquals(false, pricingConfigurationOffsetsPresenter.isValueMissingWhileEditingSeason(offsets, season, false));


    }

    @Test
    public void testValidateOffsetSeasonsHavingSoftWarningForDailyPricingRuleTypes() {
        //create season
        pricingConfigurationOffsetsPresenter.setPricingConfigurationDTO(createPricingConfigurationDTOWithPrimaryPricedProduct(null));
        PricingConfigurationOffsetWrapper pricingConfigurationOffsetWrapper = new PricingConfigurationOffsetWrapper();
        pricingConfigurationOffsetWrapper.setName("test");

        List<PricingConfigurationOffsetWrapper> pricingConfigurationOffsetWrappers = new ArrayList<>();
        pricingConfigurationOffsetWrappers.add(pricingConfigurationOffsetWrapper);

        PricingConfigurationOffsetSeasonWrapper season = new PricingConfigurationOffsetSeasonWrapper();
        season.setStartDate(localDate.minusDays(1));
        season.setEndDate(localDate.plusDays(10));
        season.setName("season");
        season.setOffsets(pricingConfigurationOffsetWrappers);

        when(pacmanConfigParamsService.getBooleanParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED)).thenReturn(true);
        pricingConfigurationOffsetsPresenter.setAdvancedPriceRankingEnabled(false);
        pricingConfigurationOffsetsPresenter.setupParameters();
        pricingConfigurationOffsetsPresenter.loadData();
        pricingConfigurationOffsetsPresenter.init(false);
        // when(pricingConfigurationValidationService.validateSaveSeasonOffsets(any(), any(), any(), any(), any(), any())).thenReturn(roomClassesWithOffsetViolation);
        when(pricingConfigurationValidationService.validateSaveSeasonOffsetsByDailyPricingBarRuleType(any(), any(), any(), any(), any(), any())).thenReturn(roomClassesWithOffsetViolation);


        when(lang.getText("breaksRoomClassRankBydailyBarPricingRuleTypePreMsg")).thenReturn(getText("breaksRoomClassRankBydailyBarPricingRuleTypePreMsg"));
        when(lang.getText("breaksRoomClassRankBydailyBarPricingRuleType2Msg")).thenReturn(getText("breaksRoomClassRankBydailyBarPricingRuleType2Msg"));
        when(lang.getText("breaksRoomClassRankBydailyBarPricingRuleType3Msg")).thenReturn(getText("breaksRoomClassRankBydailyBarPricingRuleType3Msg"));
        when(lang.getText("breaksRoomClassRankBydailyBarPricingRuleTypePostMsg")).thenReturn(getText("breaksRoomClassRankBydailyBarPricingRuleTypePostMsg"));

        when(pricingConfigurationOffsetsPresenter.isCPFloorAndCeilingSoftWarningByPricingRuleTypeEnable()).thenReturn(true);

        // Rule type 0
        when(pricingConfigurationOffsetsPresenter.getDailyBarPricingRuleType()).thenReturn(0);
        assertEquals(Optional.empty(), pricingConfigurationOffsetsPresenter.validateOffsetSeasonsHavingSoftWarning(season));

        // Rule type 1
        when(pricingConfigurationOffsetsPresenter.getDailyBarPricingRuleType()).thenReturn(1);
        assertEquals(Optional.empty(), pricingConfigurationOffsetsPresenter.validateOffsetSeasonsHavingSoftWarning(season));

        // Rule type 2
        when(pricingConfigurationOffsetsPresenter.getDailyBarPricingRuleType()).thenReturn(2);
        assertEquals(Optional.of("This change breaks the Price Ranking between the following Room Classes:<BR/>DELUXE<BR/>The lowest priced room type of the higher ranked Room Class should be higher than the highest priced room type of the lower ranked Room Class.<BR/>Review your Offsets, Minimum Price Differential, and Floors and Ceilings for the periods above.<br/> <br/> Click Cancel to change the values. Click Continue to save."), pricingConfigurationOffsetsPresenter.validateOffsetSeasonsHavingSoftWarning(season));

        // Rule type 3
        when(pricingConfigurationOffsetsPresenter.getDailyBarPricingRuleType()).thenReturn(3);
        assertEquals(Optional.of("This change breaks the Price Ranking between the following Room Classes:<BR/>DELUXE<BR/>The lowest priced room type of the higher ranked Room Class should be higher than the lowest priced room type of the lower ranked Room Class.<BR/>Review your Offsets, Minimum Price Differential, and Floors and Ceilings for the periods above.<br/> <br/> Click Cancel to change the values. Click Continue to save."), pricingConfigurationOffsetsPresenter.validateOffsetSeasonsHavingSoftWarning(season));

        pricingConfigurationOffsetsPresenter.setAdvancedPriceRankingEnabled(true);
        assertEquals(Optional.empty(), pricingConfigurationOffsetsPresenter.validateOffsetSeasonsHavingSoftWarning(season));
    }

    @Test
    public void testTransientSeasonOffsetWarningMessage() {
        pricingConfigurationOffsetsPresenter.setPricingConfigurationDTO(createPricingConfigurationDTOWithPrimaryPricedProduct(null));
        when(pacmanConfigParamsService.getBooleanParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED)).thenReturn(true);
        pricingConfigurationOffsetsPresenter.setupParameters();
        pricingConfigurationOffsetsPresenter.loadData();
        pricingConfigurationOffsetsPresenter.init(false);
        when(pricingConfigurationValidationService.validateSaveOffsetLowerThanTransientSeasonFloor(any(), any(), any())).thenReturn(roomClassesWithOffsetViolation);
        when(lang.getText("breaksRoomTypeOffsetLowerThanSeasonFloor", warningMessage)).thenReturn(getText("breaksRoomTypeOffsetLowerThanSeasonFloor", warningMessage));
        when(pricingConfigurationService.getBaseOccupancyType()).thenReturn(OccupancyType.SINGLE);

        pricingConfigurationOffsetsPresenter.save(Arrays.asList(pricingConfigurationOffsetWrapper));
        verify(view).showWarning(getText("breaksRoomTypeOffsetLowerThanSeasonFloor", warningMessage));
    }

    @Test
    public void testTransientSeasonOffsetWarningMessageWhenIsCPFloorAndCeilingSoftWarningEnabledOn() {
        pricingConfigurationOffsetsPresenter.setPricingConfigurationDTO(createPricingConfigurationDTOWithPrimaryPricedProduct(null));
        when(pacmanConfigParamsService.getBooleanParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED)).thenReturn(true);
        pricingConfigurationOffsetsPresenter.setupParameters();
        pricingConfigurationOffsetsPresenter.loadData();
        pricingConfigurationOffsetsPresenter.init(false);
        when(pricingConfigurationValidationService.validateSaveOffsetLowerThanTransientSeasonFloor(any(), any(), any())).thenReturn(roomClassesWithOffsetViolation);
        when(lang.getText("breaksRoomTypeOffsetLowerThanSeasonFloor", warningMessage)).thenReturn(getText("breaksRoomTypeOffsetLowerThanSeasonFloor", warningMessage));
        when(pricingConfigurationService.getBaseOccupancyType()).thenReturn(OccupancyType.SINGLE);
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_CP_FLOOR_AND_CEILING_SOFT_WARNING)).thenReturn(true);
        pricingConfigurationOffsetsPresenter.save(Arrays.asList(pricingConfigurationOffsetWrapper));
        verify(view).showWarning(getText("breaksRoomTypeOffsetLowerThanSeasonFloor", warningMessage));
    }

    @Test
    public void testPriceExcludedOffsetWarningMessageWithPriceExcludedToggle() {
        when(pacmanConfigParamsService.getBooleanParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED)).thenReturn(true);
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.IS_ADVANCED_PRICE_RANKING_ENABLED)).thenReturn(true);
        pricingConfigurationOffsetsPresenter.setPricingConfigurationDTO(createPricingConfigurationDTOWithPrimaryPricedProduct(null));
        pricingConfigurationOffsetsPresenter.setupParameters();
        pricingConfigurationOffsetsPresenter.loadData();
        pricingConfigurationOffsetsPresenter.init(false);

        pricingConfigurationOffsetsPresenter.save(Arrays.asList(pricingConfigurationOffsetWrapper));
        verify(pricingConfigurationValidationService, never()).validatePriceExcludedOffsets(any(), any(), any());
        verify(view).showSaveSuccessMessage();
    }

    @Test
    public void testUpdatePreviousSeason() {

        //create season
        PricingConfigurationOffsetWrapper pricingConfigurationOffsetWrapper = new PricingConfigurationOffsetWrapper();
        pricingConfigurationOffsetWrapper.setName("test");

        List<PricingConfigurationOffsetWrapper> pricingConfigurationOffsetWrappers = new ArrayList<>();
        pricingConfigurationOffsetWrappers.add(pricingConfigurationOffsetWrapper);

        PricingConfigurationOffsetSeasonWrapper season = new PricingConfigurationOffsetSeasonWrapper();
        season.setStartDate(localDate.minusDays(1));
        season.setEndDate(localDate.plusDays(10));
        season.setName("season");
        season.setOffsets(pricingConfigurationOffsetWrappers);

        List<PricingConfigurationOffsetSeasonWrapper> seasonsList = new ArrayList<>();
        seasonsList.add(season);

        //create previous season
        PricingConfigurationOffsetWrapper pricingConfigurationOffsetWrapper1 = new PricingConfigurationOffsetWrapper();
        pricingConfigurationOffsetWrapper1.setName("test1");

        List<PricingConfigurationOffsetWrapper> pricingConfigurationOffsetWrappers1 = new ArrayList<>();
        pricingConfigurationOffsetWrappers1.add(pricingConfigurationOffsetWrapper1);

        PricingConfigurationOffsetSeasonWrapper season1 = new PricingConfigurationOffsetSeasonWrapper();
        season1.setStartDate(localDate.minusDays(1));
        season1.setEndDate(localDate.plusDays(1));
        season1.setName("previous");
        season1.setOffsets(pricingConfigurationOffsetWrappers1);

        List<PricingConfigurationOffsetSeasonWrapper> previousSeasons = new ArrayList<>();
        previousSeasons.add(season1);

        PricingConfigurationOffsetsPresenter presenterSpy = Mockito.spy(pricingConfigurationOffsetsPresenter);
        doReturn(previousSeasons).when(presenterSpy).getSeasons();

        presenterSpy.setSeasons(seasonsList);

        assertTrue(presenterSpy.getSeasons().get(0).getName().equals("previous"));
    }

    @Test
    public void testOffsetValidation() {
        assertFalse(pricingConfigurationOffsetsPresenter.offsetValidation(negative99));
        assertTrue(pricingConfigurationOffsetsPresenter.offsetValidation(negative100));
    }

    @Test
    public void isSeasonEmpty_false_NoChildren() {
        CPConfigOffsetAccomType cpConfigOffsetAccomType = new CPConfigOffsetAccomType();
        cpConfigOffsetAccomType.setFridayOffsetValueWithTax(new BigDecimal(1));
        PricingConfigurationOffsetWrapper childOffsetWrapper = new PricingConfigurationOffsetWrapper();
        childOffsetWrapper.setCpConfigOffsetAccomType(cpConfigOffsetAccomType);
        PricingConfigurationOffsetWrapper offsetWrapper = new PricingConfigurationOffsetWrapper();
        childOffsetWrapper.setParent(offsetWrapper);
        offsetWrapper.addChild(childOffsetWrapper);
        List<PricingConfigurationOffsetWrapper> offsetWrappers = new ArrayList<>();
        offsetWrappers.add(offsetWrapper);

        assertFalse(pricingConfigurationOffsetsPresenter.isSeasonEmpty(offsetWrappers));
    }

    @Test
    public void isSeasonEmpty_false_Children() {
        CPConfigOffsetAccomType cpConfigOffsetAccomTypeChild = new CPConfigOffsetAccomType();
        cpConfigOffsetAccomTypeChild.setFridayOffsetValueWithTax(new BigDecimal(1));
        PricingConfigurationOffsetWrapper childOffsetWrapper = new PricingConfigurationOffsetWrapper();
        childOffsetWrapper.setCpConfigOffsetAccomType(cpConfigOffsetAccomTypeChild);
        CPConfigOffsetAccomType cpConfigOffsetAccomTypeParent = new CPConfigOffsetAccomType();
        PricingConfigurationOffsetWrapper parentOffsetWrapper = new PricingConfigurationOffsetWrapper();
        parentOffsetWrapper.setCpConfigOffsetAccomType(cpConfigOffsetAccomTypeParent);
        PricingConfigurationOffsetWrapper offsetWrapper = new PricingConfigurationOffsetWrapper();
        childOffsetWrapper.setParent(parentOffsetWrapper);
        parentOffsetWrapper.addChild(childOffsetWrapper);
        parentOffsetWrapper.setParent(offsetWrapper);
        offsetWrapper.addChild(parentOffsetWrapper);
        List<PricingConfigurationOffsetWrapper> offsetWrappers = new ArrayList<>();
        offsetWrappers.add(offsetWrapper);

        assertFalse(pricingConfigurationOffsetsPresenter.isSeasonEmpty(offsetWrappers));
    }

    @Test
    public void isSeasonEmpty_true() {
        CPConfigOffsetAccomType cpConfigOffsetAccomType = new CPConfigOffsetAccomType();
        PricingConfigurationOffsetWrapper childOffsetWrapper = new PricingConfigurationOffsetWrapper();
        childOffsetWrapper.setCpConfigOffsetAccomType(cpConfigOffsetAccomType);
        PricingConfigurationOffsetWrapper offsetWrapper = new PricingConfigurationOffsetWrapper();
        childOffsetWrapper.setParent(offsetWrapper);
        offsetWrapper.addChild(childOffsetWrapper);
        List<PricingConfigurationOffsetWrapper> offsetWrappers = new ArrayList<>();
        offsetWrappers.add(offsetWrapper);

        assertTrue(pricingConfigurationOffsetsPresenter.isSeasonEmpty(offsetWrappers));
    }

    @Test
    public void addRoomType_true() throws Exception {

        AccomType accomType = new AccomType();
        accomType.setName("test");
        AccomType accomType2 = new AccomType();
        accomType.setName("test2");

        AccomClass accomClass = new AccomClass();
        accomClass.setName("accom class");
        accomClass.addAccomType(accomType);

        PricingAccomClass pricingAccomClass = new PricingAccomClass();
        pricingAccomClass.setAccomClass(accomClass);
        pricingAccomClass.setAccomType(accomType);

        pricingConfigurationOffsetsPresenter.setCPBaseRoomTypeOnlyEnabled(false);
        assertTrue(pricingConfigurationOffsetsPresenter.addRoomType(pricingAccomClass, accomType));

        pricingConfigurationOffsetsPresenter.setCPBaseRoomTypeOnlyEnabled(true);
        assertTrue(pricingConfigurationOffsetsPresenter.addRoomType(pricingAccomClass, accomType));

        pricingConfigurationOffsetsPresenter.setCPBaseRoomTypeOnlyEnabled(false);
        assertTrue(pricingConfigurationOffsetsPresenter.addRoomType(pricingAccomClass, accomType2));
    }

    @Test
    public void addRoomType_false() {
        AccomType accomType = new AccomType();
        accomType.setName("test");
        AccomType accomType2 = new AccomType();
        accomType.setName("test2");

        AccomClass accomClass = new AccomClass();
        accomClass.setName("accom class");
        accomClass.addAccomType(accomType);

        PricingAccomClass pricingAccomClass = new PricingAccomClass();
        pricingAccomClass.setAccomClass(accomClass);
        pricingAccomClass.setAccomType(accomType);

        pricingConfigurationOffsetsPresenter.setCPBaseRoomTypeOnlyEnabled(true);
        assertFalse(pricingConfigurationOffsetsPresenter.addRoomType(pricingAccomClass, accomType2));
    }

    @Test
    public void getCorrespondingOffsetEntity() throws Exception {
        AccomType accomType = new AccomType();
        accomType.setName("test");

        AccomClass accomClass = new AccomClass();
        accomClass.setName("accom class");
        accomClass.addAccomType(accomType);

        PricingAccomClass pricingAccomClass = new PricingAccomClass();
        pricingAccomClass.setAccomClass(accomClass);
        pricingAccomClass.setAccomType(accomType);

        pricingConfigurationOffsetsPresenter.setPricingConfigurationDTO(createPricingConfigurationDTOWithPrimaryPricedProduct(false));

        CPConfigOffsetAccomType cpConfigOffsetAccomType = pricingConfigurationOffsetsPresenter.getCorrespondingOffsetEntity(pricingAccomClass, accomType, null, OccupancyType.SINGLE);

        assertNotNull(cpConfigOffsetAccomType);
        assertEquals(FIXED_OFFSET, cpConfigOffsetAccomType.getOffsetMethod());
    }

    @Test
    public void getCorrespondingOffsetEntity_priceExcludedAndSingleOccupancy() {
        PricingConfigurationDTO dto = createPricingConfigurationDTOWithPrimaryPricedProduct(false);
        pricingConfigurationOffsetsPresenter.setPricingConfigurationDTO(dto);

        when(pricingConfigurationService.getBaseOccupancyType()).thenReturn(OccupancyType.SINGLE);

        AccomType accomType = new AccomType();
        accomType.setName("test");

        AccomClass accomClass = new AccomClass();
        accomClass.setName("accom class");
        accomClass.addAccomType(accomType);

        PricingAccomClass pricingAccomClass = new PricingAccomClass();
        pricingAccomClass.setAccomClass(accomClass);
        pricingAccomClass.setAccomType(accomType);
        pricingAccomClass.setPriceExcluded(true);

        CPConfigOffsetAccomType cpConfigOffsetAccomType = pricingConfigurationOffsetsPresenter.getCorrespondingOffsetEntity(pricingAccomClass, accomType, null, OccupancyType.SINGLE);

        assertNotNull(cpConfigOffsetAccomType);
        assertEquals(OffsetMethod.FIXED_PRICE, cpConfigOffsetAccomType.getOffsetMethod());

        CPConfigOffsetAccomType cpConfigOffsetAccomTypeDouble = pricingConfigurationOffsetsPresenter.getCorrespondingOffsetEntity(pricingAccomClass, accomType, null, OccupancyType.DOUBLE);

        assertNotNull(cpConfigOffsetAccomTypeDouble);
        assertEquals(FIXED_OFFSET, cpConfigOffsetAccomTypeDouble.getOffsetMethod());

        pricingConfigurationOffsetsPresenter.setBaseOccupancyType(OccupancyType.DOUBLE);

        CPConfigOffsetAccomType pppCpConfigOffsetAccomTypeSingle = pricingConfigurationOffsetsPresenter.getCorrespondingOffsetEntity(pricingAccomClass, accomType, null, OccupancyType.SINGLE);

        assertNotNull(pppCpConfigOffsetAccomTypeSingle);
        assertEquals(FIXED_OFFSET, pppCpConfigOffsetAccomTypeSingle.getOffsetMethod());

        CPConfigOffsetAccomType pppCpConfigOffsetAccomTypeDouble = pricingConfigurationOffsetsPresenter.getCorrespondingOffsetEntity(pricingAccomClass, accomType, null, OccupancyType.DOUBLE);

        assertNotNull(pppCpConfigOffsetAccomTypeDouble);
        assertEquals(OffsetMethod.FIXED_PRICE, pppCpConfigOffsetAccomTypeDouble.getOffsetMethod());
    }

    @Test
    public void getCorrespondingOffsetEntity_offsetExists() throws Exception {
        LinkedList<CPConfigOffsetAccomType> offsetTypes = new LinkedList<>();
        CPConfigOffsetAccomType cpConfigOffsetAccomType = new CPConfigOffsetAccomType();
        cpConfigOffsetAccomType.setOccupancyType(OccupancyType.SINGLE);
        offsetTypes.add(cpConfigOffsetAccomType);

        CPConfigOffsetAccomType retOffsetAccomType = pricingConfigurationOffsetsPresenter.getCorrespondingOffsetEntity(null, null, offsetTypes, OccupancyType.SINGLE);

        assertNotNull(retOffsetAccomType);
        assertEquals(cpConfigOffsetAccomType, retOffsetAccomType);
    }

    @Test
    public void isRowNotEditable() {
        PricingConfigurationOffsetWrapper testOffsetWrapper = new PricingConfigurationOffsetWrapper();
        CPConfigOffsetAccomType cpConfigOffsetAccomType = new CPConfigOffsetAccomType();
        AccomType accomType = new AccomType();
        PricingAccomClass accomClass = new PricingAccomClass();

        accomClass.setAccomType(accomType);
        accomClass.setPriceExcluded(false);
        testOffsetWrapper.setAccomType(accomType);
        testOffsetWrapper.setAccomClass(accomClass);
        testOffsetWrapper.setCpConfigOffsetAccomType(cpConfigOffsetAccomType);

        //If Occupancy Type = Single && PerPersonPricingEnabled
        pricingConfigurationOffsetsPresenter.setBaseOccupancyType(OccupancyType.DOUBLE);
        cpConfigOffsetAccomType.setOccupancyType(OccupancyType.SINGLE);
        assertFalse(pricingConfigurationOffsetsPresenter.isRowNotEditable(testOffsetWrapper));

        //If Occupancy Type = Double && PerPersonPricingEnabled
        cpConfigOffsetAccomType.setOccupancyType(OccupancyType.DOUBLE);
        when(pricingConfigurationService.getBaseOccupancyType()).thenReturn(OccupancyType.DOUBLE);
        assertTrue(pricingConfigurationOffsetsPresenter.isRowNotEditable(testOffsetWrapper));

        //If Occupancy Type = Double && !PerPersonPricingEnabled
        pricingConfigurationOffsetsPresenter.setPerPersonPricingEnabled(false);
        pricingConfigurationOffsetsPresenter.setBaseOccupancyType(OccupancyType.SINGLE);
        assertFalse(pricingConfigurationOffsetsPresenter.isRowNotEditable(testOffsetWrapper));

        //If Occupancy Type = Single && !PerPersonPricingEnabled
        cpConfigOffsetAccomType.setOccupancyType(OccupancyType.SINGLE);
        when(pricingConfigurationService.getBaseOccupancyType()).thenReturn(OccupancyType.SINGLE);
        assertTrue(pricingConfigurationOffsetsPresenter.isRowNotEditable(testOffsetWrapper));
    }

    @Test
    public void isRowNotEditablePriceExcludedToggle() {
        PricingConfigurationOffsetWrapper testOffsetWrapper = new PricingConfigurationOffsetWrapper();
        CPConfigOffsetAccomType cpConfigOffsetAccomType = new CPConfigOffsetAccomType();
        AccomType accomType = new AccomType();
        PricingAccomClass accomClass = new PricingAccomClass();

        accomClass.setAccomType(accomType);
        accomClass.setPriceExcluded(true);
        testOffsetWrapper.setAccomType(accomType);
        testOffsetWrapper.setAccomClass(accomClass);
        testOffsetWrapper.setCpConfigOffsetAccomType(cpConfigOffsetAccomType);

        pricingConfigurationOffsetsPresenter.setBaseOccupancyType(OccupancyType.DOUBLE);
        cpConfigOffsetAccomType.setOccupancyType(OccupancyType.DOUBLE);
        when(pricingConfigurationService.getBaseOccupancyType()).thenReturn(OccupancyType.DOUBLE);
        assertTrue(pricingConfigurationOffsetsPresenter.isRowNotEditable(testOffsetWrapper));
    }

    @Test
    public void getPerPersonPricingTreeStructure() {
        pricingConfigurationOffsetsPresenter.setPricingConfigurationDTO(createPricingConfigurationDTOWithPrimaryPricedProduct(null));
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.PER_PERSON_PRICING_ENABLED)).thenReturn(true);
        pricingConfigurationOffsetsPresenter.setupParameters();
        pricingConfigurationOffsetsPresenter.loadData();
        pricingConfigurationOffsetsPresenter.init(false);

        AccomType accomType = new AccomType();
        accomType.setId(1);
        PricingAccomClass pricingAccomClass = new PricingAccomClass();
        LinkedList<CPConfigOffsetAccomType> offsetTypes = new LinkedList<>();
        PricingConfigurationOffsetWrapper roomTypeWrapper = new PricingConfigurationOffsetWrapper();

        //These dictate the tree structure
        List<OccupantBucketEntity> occupantBucketEntities = new LinkedList<>();
        MaximumOccupantsEntity maximumOccupantsEntity = new MaximumOccupantsEntity();
        maximumOccupantsEntity.setAccomTypeId(1);
        OccupancyType currentAdultBucket = OccupancyType.FIVE_ADULTS;

        //No maximum occupants but occupant value grouping configured
        //Testing 5th adult and 5th child
        when(perPersonPricingService.getMaximumOccupants()).thenReturn(new ArrayList<>());

        OccupantBucketEntity childBucket = new OccupantBucketEntity();
        childBucket.setOccupancyType(OccupancyType.FIVE_CHILDREN);
        occupantBucketEntities.add(childBucket);
        when(perPersonPricingService.getOccupantBuckets()).thenReturn(occupantBucketEntities);

        pricingConfigurationOffsetsPresenter.setupParameters();
        pricingConfigurationOffsetsPresenter.loadData();
        pricingConfigurationOffsetsPresenter.init(false);
        pricingConfigurationOffsetsPresenter.getPerPersonPricingTreeStructure(accomType, pricingAccomClass, currentAdultBucket, offsetTypes, roomTypeWrapper, new ArrayList<>());
        assertEquals(11, roomTypeWrapper.getChildren().size());
        roomTypeWrapper = new PricingConfigurationOffsetWrapper();

        childBucket.setOccupancyType(OccupancyType.THREE_CHILDREN);
        currentAdultBucket = OccupancyType.THREE_ADULTS;
        pricingConfigurationOffsetsPresenter.setupParameters();
        pricingConfigurationOffsetsPresenter.loadData();
        pricingConfigurationOffsetsPresenter.init(false);
        pricingConfigurationOffsetsPresenter.getPerPersonPricingTreeStructure(accomType, pricingAccomClass, currentAdultBucket, offsetTypes, roomTypeWrapper, new ArrayList<>());
        assertEquals(7, roomTypeWrapper.getChildren().size());
        roomTypeWrapper = new PricingConfigurationOffsetWrapper();

        //Case occupant value grouping < maximum occupants
        when(perPersonPricingService.getMaximumOccupants()).thenReturn(Arrays.asList(maximumOccupantsEntity));
        maximumOccupantsEntity.setAdults(8);
        maximumOccupantsEntity.setChildren(8);
        pricingConfigurationOffsetsPresenter.setupParameters();
        pricingConfigurationOffsetsPresenter.loadData();
        pricingConfigurationOffsetsPresenter.init(false);
        pricingConfigurationOffsetsPresenter.getPerPersonPricingTreeStructure(accomType, pricingAccomClass, currentAdultBucket, offsetTypes, roomTypeWrapper, new ArrayList<>());
        assertEquals(7, roomTypeWrapper.getChildren().size());
        roomTypeWrapper = new PricingConfigurationOffsetWrapper();

        //Case maximum occupants < occupant value grouping
        maximumOccupantsEntity.setAdults(4);
        maximumOccupantsEntity.setChildren(3);
        childBucket.setOccupancyType(OccupancyType.FIVE_CHILDREN);
        currentAdultBucket = OccupancyType.FIVE_CHILDREN;
        pricingConfigurationOffsetsPresenter.setupParameters();
        pricingConfigurationOffsetsPresenter.loadData();
        pricingConfigurationOffsetsPresenter.init(false);
        pricingConfigurationOffsetsPresenter.getPerPersonPricingTreeStructure(accomType, pricingAccomClass, currentAdultBucket, offsetTypes, roomTypeWrapper, new ArrayList<>());
        assertEquals(8, roomTypeWrapper.getChildren().size());
    }

    @Test
    public void getTreeStructure_CPChildAgeBucketsEnabled_IndependentProductsEnabledTrue_BAR() {
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.PER_PERSON_PRICING_ENABLED)).thenReturn(false);
        when(pacmanConfigParamsService.getBooleanParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED)).thenReturn(true);
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.CP_CHILD_AGE_BUCKETS_ENABLED)).thenReturn(true);
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.INDEPENDENT_PRODUCTS_ENABLED)).thenReturn(true);

        AccomType accomType = new AccomType();
        accomType.setId(1);
        AccomClass accomClass = new AccomClass();
        accomClass.setName("Test");
        PricingAccomClass pricingAccomClass = new PricingAccomClass();
        pricingAccomClass.setAccomType(accomType);
        pricingAccomClass.setAccomClass(accomClass);

        LinkedList<CPConfigOffsetAccomType> offsetTypes = new LinkedList<>();

        LinkedHashMap<AccomType, LinkedList<CPConfigOffsetAccomType>> cpOffsetTypes = new LinkedHashMap<>();
        cpOffsetTypes.put(accomType, offsetTypes);
        LinkedHashMap<PricingAccomClass, LinkedHashMap<AccomType, LinkedList<CPConfigOffsetAccomType>>> offsets = new LinkedHashMap<>();

        offsets.put(pricingAccomClass, cpOffsetTypes);

        // Setup child age buckets
        List<OccupantBucketEntity> childAgeBuckets = new LinkedList<>();

        OccupantBucketEntity childBucket_1 = new OccupantBucketEntity();
        childBucket_1.setMinAge(0);
        childBucket_1.setMaxAge(5);
        childBucket_1.setOccupancyType(OccupancyType.CHILD_BUCKET_1);
        childAgeBuckets.add(childBucket_1);

        OccupantBucketEntity childBucket_2 = new OccupantBucketEntity();
        childBucket_2.setMinAge(6);
        childBucket_2.setMaxAge(12);
        childBucket_2.setOccupancyType(OccupancyType.CHILD_BUCKET_2);
        childAgeBuckets.add(childBucket_2);

        when(perPersonPricingService.getOccupantBuckets()).thenReturn(childAgeBuckets);
        pricingConfigurationOffsetsPresenter.setPricingConfigurationDTO(createPricingConfigurationDTOWithPrimaryPricedProduct(null));
        pricingConfigurationOffsetsPresenter.setupParameters();
        pricingConfigurationOffsetsPresenter.loadData();
        pricingConfigurationOffsetsPresenter.init(false);
        List<PricingConfigurationOffsetWrapper> roomClassWrapper = pricingConfigurationOffsetsPresenter.getTreeStructure(false, offsets);

        assertTrue(CollectionUtils.isNotEmpty(roomClassWrapper));
        assertEquals(1, roomClassWrapper.get(0).getChildren().size());
        List<PricingConfigurationOffsetWrapper> roomTypeWrapper = roomClassWrapper.get(0).getChildren().get(0).getChildren();
        assertTrue(CollectionUtils.isNotEmpty(roomTypeWrapper));
        assertEquals(5, roomTypeWrapper.size());
        verify(accomTypeSupplementService).findSupplementsByAccomTypesForBAR(Arrays.asList(1));
    }

    @Test
    public void getTreeStructure_CPChildAgeBucketsEnabled_IndependentProductsEnabledTrue_IndependentProduct() {
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.PER_PERSON_PRICING_ENABLED)).thenReturn(false);
        when(pacmanConfigParamsService.getBooleanParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED)).thenReturn(true);
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.CP_CHILD_AGE_BUCKETS_ENABLED)).thenReturn(true);
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.INDEPENDENT_PRODUCTS_ENABLED)).thenReturn(true);

        AccomType accomType = new AccomType();
        accomType.setId(1);
        AccomClass accomClass = new AccomClass();
        accomClass.setName("Test");
        PricingAccomClass pricingAccomClass = new PricingAccomClass();
        pricingAccomClass.setAccomType(accomType);
        pricingAccomClass.setAccomClass(accomClass);

        LinkedList<CPConfigOffsetAccomType> offsetTypes = new LinkedList<>();

        LinkedHashMap<AccomType, LinkedList<CPConfigOffsetAccomType>> cpOffsetTypes = new LinkedHashMap<>();
        cpOffsetTypes.put(accomType, offsetTypes);
        LinkedHashMap<PricingAccomClass, LinkedHashMap<AccomType, LinkedList<CPConfigOffsetAccomType>>> offsets = new LinkedHashMap<>();

        offsets.put(pricingAccomClass, cpOffsetTypes);

        // Setup child age buckets
        List<OccupantBucketEntity> childAgeBuckets = new LinkedList<>();

        OccupantBucketEntity childBucket_1 = new OccupantBucketEntity();
        childBucket_1.setMinAge(0);
        childBucket_1.setMaxAge(5);
        childBucket_1.setOccupancyType(OccupancyType.CHILD_BUCKET_1);
        childAgeBuckets.add(childBucket_1);

        OccupantBucketEntity childBucket_2 = new OccupantBucketEntity();
        childBucket_2.setMinAge(6);
        childBucket_2.setMaxAge(12);
        childBucket_2.setOccupancyType(OccupancyType.CHILD_BUCKET_2);
        childAgeBuckets.add(childBucket_2);

        when(offsetService.retrieveOffsetConfigDTO(any(), any(), any(), any())).thenReturn(new CPOffsetConfigDTO());
        when(perPersonPricingService.getOccupantBuckets()).thenReturn(childAgeBuckets);
        PricingConfigurationDTO pricingConfigurationDTOWithIndependentProduct = createPricingConfigurationDTOWithIndependentProduct();
        pricingConfigurationOffsetsPresenter.setPricingConfigurationDTO(pricingConfigurationDTOWithIndependentProduct);
        pricingConfigurationOffsetsPresenter.setupParameters();
        pricingConfigurationOffsetsPresenter.loadData();
        pricingConfigurationOffsetsPresenter.init(false);
        List<PricingConfigurationOffsetWrapper> roomClassWrapper = pricingConfigurationOffsetsPresenter.getTreeStructure(false, offsets);

        assertTrue(CollectionUtils.isNotEmpty(roomClassWrapper));
        assertEquals(1, roomClassWrapper.get(0).getChildren().size());
        List<PricingConfigurationOffsetWrapper> roomTypeWrapper = roomClassWrapper.get(0).getChildren().get(0).getChildren();
        assertTrue(CollectionUtils.isNotEmpty(roomTypeWrapper));
        assertEquals(5, roomTypeWrapper.size());
        verify(accomTypeSupplementService).findSupplementsByAccomTypesAndProduct(Arrays.asList(1), pricingConfigurationDTOWithIndependentProduct.getProduct());
    }

    @Test
    public void getTreeStructure_shouldReturnOnlyFixedValueSupplementsForValidations() {
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.PER_PERSON_PRICING_ENABLED)).thenReturn(false);
        when(pacmanConfigParamsService.getBooleanParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED)).thenReturn(true);
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.CP_CHILD_AGE_BUCKETS_ENABLED)).thenReturn(true);
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.INDEPENDENT_PRODUCTS_ENABLED)).thenReturn(true);

        AccomType accomType = new AccomType();
        accomType.setId(1);
        AccomClass accomClass = new AccomClass();
        accomClass.setName("Test");
        accomType.setAccomClass(accomClass);
        PricingAccomClass pricingAccomClass = new PricingAccomClass();
        pricingAccomClass.setAccomType(accomType);
        pricingAccomClass.setAccomClass(accomClass);
        when(accomTypeSupplementService.findSupplementsByAccomTypesAndProduct(eq(List.of(1)), any())).thenReturn(List.of(supplement(FIXED_OFFSET, accomType), supplement(PERCENTAGE, accomType)));

        LinkedList<CPConfigOffsetAccomType> offsetTypes = new LinkedList<>();

        LinkedHashMap<AccomType, LinkedList<CPConfigOffsetAccomType>> cpOffsetTypes = new LinkedHashMap<>();
        cpOffsetTypes.put(accomType, offsetTypes);
        LinkedHashMap<PricingAccomClass, LinkedHashMap<AccomType, LinkedList<CPConfigOffsetAccomType>>> offsets = new LinkedHashMap<>();

        offsets.put(pricingAccomClass, cpOffsetTypes);

        // Setup child age buckets
        List<OccupantBucketEntity> childAgeBuckets = new LinkedList<>();

        OccupantBucketEntity childBucket_1 = new OccupantBucketEntity();
        childBucket_1.setMinAge(0);
        childBucket_1.setMaxAge(5);
        childBucket_1.setOccupancyType(OccupancyType.CHILD_BUCKET_1);
        childAgeBuckets.add(childBucket_1);

        OccupantBucketEntity childBucket_2 = new OccupantBucketEntity();
        childBucket_2.setMinAge(6);
        childBucket_2.setMaxAge(12);
        childBucket_2.setOccupancyType(OccupancyType.CHILD_BUCKET_2);
        childAgeBuckets.add(childBucket_2);

        when(offsetService.retrieveOffsetConfigDTO(any(), any(), any(), any())).thenReturn(new CPOffsetConfigDTO());
        when(perPersonPricingService.getOccupantBuckets()).thenReturn(childAgeBuckets);
        PricingConfigurationDTO pricingConfigurationDTOWithIndependentProduct = createPricingConfigurationDTOWithIndependentProduct();
        pricingConfigurationOffsetsPresenter.setPricingConfigurationDTO(pricingConfigurationDTOWithIndependentProduct);
        pricingConfigurationOffsetsPresenter.setupParameters();
        pricingConfigurationOffsetsPresenter.loadData();
        pricingConfigurationOffsetsPresenter.init(false);
        List<PricingConfigurationOffsetWrapper> roomClassWrapper = pricingConfigurationOffsetsPresenter.getTreeStructure(false, offsets);

        assertTrue(CollectionUtils.isNotEmpty(roomClassWrapper));
        assertEquals(1, roomClassWrapper.get(0).getAccomTypeSupplementList().size());
        assertEquals(FIXED_OFFSET, roomClassWrapper.get(0).getAccomTypeSupplementList().get(0).getOffsetMethod());
        verify(accomTypeSupplementService).findSupplementsByAccomTypesAndProduct(Arrays.asList(1), pricingConfigurationDTOWithIndependentProduct.getProduct());
    }

    @Test
    public void getTreeStructure_CPChildAgeBucketsEnabled_IndependentProductsEnabledFalse() {
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.PER_PERSON_PRICING_ENABLED)).thenReturn(false);
        when(pacmanConfigParamsService.getBooleanParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED)).thenReturn(true);
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.CP_CHILD_AGE_BUCKETS_ENABLED)).thenReturn(true);

        AccomType accomType = new AccomType();
        accomType.setId(1);
        AccomClass accomClass = new AccomClass();
        accomClass.setName("Test");
        PricingAccomClass pricingAccomClass = new PricingAccomClass();
        pricingAccomClass.setAccomType(accomType);
        pricingAccomClass.setAccomClass(accomClass);

        LinkedList<CPConfigOffsetAccomType> offsetTypes = new LinkedList<>();

        LinkedHashMap<AccomType, LinkedList<CPConfigOffsetAccomType>> cpOffsetTypes = new LinkedHashMap<>();
        cpOffsetTypes.put(accomType, offsetTypes);
        LinkedHashMap<PricingAccomClass, LinkedHashMap<AccomType, LinkedList<CPConfigOffsetAccomType>>> offsets = new LinkedHashMap<>();

        offsets.put(pricingAccomClass, cpOffsetTypes);

        // Setup child age buckets
        List<OccupantBucketEntity> childAgeBuckets = new LinkedList<>();

        OccupantBucketEntity childBucket_1 = new OccupantBucketEntity();
        childBucket_1.setMinAge(0);
        childBucket_1.setMaxAge(5);
        childBucket_1.setOccupancyType(OccupancyType.CHILD_BUCKET_1);
        childAgeBuckets.add(childBucket_1);

        OccupantBucketEntity childBucket_2 = new OccupantBucketEntity();
        childBucket_2.setMinAge(6);
        childBucket_2.setMaxAge(12);
        childBucket_2.setOccupancyType(OccupancyType.CHILD_BUCKET_2);
        childAgeBuckets.add(childBucket_2);

        when(perPersonPricingService.getOccupantBuckets()).thenReturn(childAgeBuckets);
        pricingConfigurationOffsetsPresenter.setPricingConfigurationDTO(createPricingConfigurationDTOWithPrimaryPricedProduct(null));
        pricingConfigurationOffsetsPresenter.setupParameters();
        pricingConfigurationOffsetsPresenter.loadData();
        pricingConfigurationOffsetsPresenter.init(false);
        List<PricingConfigurationOffsetWrapper> roomClassWrapper = pricingConfigurationOffsetsPresenter.getTreeStructure(false, offsets);

        assertTrue(CollectionUtils.isNotEmpty(roomClassWrapper));
        assertEquals(1, roomClassWrapper.get(0).getChildren().size());
        List<PricingConfigurationOffsetWrapper> roomTypeWrapper = roomClassWrapper.get(0).getChildren().get(0).getChildren();
        assertTrue(CollectionUtils.isNotEmpty(roomTypeWrapper));
        assertEquals(5, roomTypeWrapper.size());
        verify(accomTypeSupplementService).findSupplementsByAccomTypesForBAR(Arrays.asList(1));
    }

    public void setUpValidationTests() {
        roomClassesWithOffsetViolation = new HashSet<>();
        roomClassesWithOffsetViolation.add("DELUXE");
        baseAccomType = new AccomType();
        baseAccomType.setName("DELUXE");
        cpConfigOffsetAccomType = new CPConfigOffsetAccomType();
        cpConfigOffsetAccomType.setId(99);
        cpConfigOffsetAccomType.setAccomType(baseAccomType);
        cpConfigOffsetAccomType.setOffsetMethod(FIXED_OFFSET);
        cpConfigOffsetAccomType.setSundayOffsetValue(null);
        cpConfigOffsetAccomType.setMondayOffsetValue(negative99);
        cpConfigOffsetAccomType.setTuesdayOffsetValue(negative99);
        cpConfigOffsetAccomType.setWednesdayOffsetValue(negative99);
        cpConfigOffsetAccomType.setThursdayOffsetValue(negative99);
        cpConfigOffsetAccomType.setFridayOffsetValue(negative99);
        cpConfigOffsetAccomType.setSaturdayOffsetValue(negative99);
        warningMessage = String.join("\n", roomClassesWithOffsetViolation);
        pricingConfigurationOffsetWrapper = new PricingConfigurationOffsetWrapper();
        pricingConfigurationOffsetWrapper.setCpConfigOffsetAccomType(cpConfigOffsetAccomType);
    }

    @Test
    public void isBaseOccupancyTest_isBaseAccomType() throws NoSuchFieldException {
        List<String> baseAccomTypeNames = Arrays.asList("TC", "BS", "GR");
        setField(pricingConfigurationOffsetsPresenter, PricingConfigurationOffsetsPresenter.class.getDeclaredField("baseRoomTypes"), baseAccomTypeNames);
        setField(pricingConfigurationOffsetsPresenter, PricingConfigurationOffsetsPresenter.class.getDeclaredField("excludedPricingAccomClasses"), new ArrayList<>());

        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.PER_PERSON_PRICING_ENABLED)).thenReturn(false);
        when(pricingConfigurationService.getBaseOccupancyType()).thenReturn(OccupancyType.SINGLE);

        CPConfigOffsetAccomType offset = new CPConfigOffsetAccomType();
        AccomType accomType = new AccomType();
        accomType.setName("TC");
        offset.setAccomType(accomType);
        offset.setOccupancyType(OccupancyType.SINGLE);

        assertTrue(pricingConfigurationOffsetsPresenter.isBaseOccupancyType(offset));
    }

    @Test
    public void isBaseOccupancyTest_notBaseAccomType() throws NoSuchFieldException {
        List<String> baseAccomTypeNames = Arrays.asList("TC", "BS", "GR");
        setField(pricingConfigurationOffsetsPresenter, PricingConfigurationOffsetsPresenter.class.getDeclaredField("baseRoomTypes"), baseAccomTypeNames);

        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.PER_PERSON_PRICING_ENABLED)).thenReturn(false);
        when(pricingConfigurationService.getBaseOccupancyType()).thenReturn(OccupancyType.SINGLE);

        CPConfigOffsetAccomType offset = new CPConfigOffsetAccomType();
        AccomType accomType = new AccomType();
        accomType.setName("AF");
        offset.setAccomType(accomType);
        offset.setOccupancyType(OccupancyType.SINGLE);

        assertFalse(pricingConfigurationOffsetsPresenter.isBaseOccupancyType(offset));
    }

    @Test
    public void isBaseOccupancyTest_priceExcluded() throws NoSuchFieldException {
        List<String> baseAccomTypeNames = Arrays.asList("TC", "BS", "GR");

        AccomType excludedAccomType = new AccomType();
        excludedAccomType.setName("TC");
        AccomType nonExcludedAccomType = new AccomType();
        nonExcludedAccomType.setName("BS");

        PricingAccomClass excludedAccomClass = new PricingAccomClass();
        excludedAccomClass.setPriceExcluded(true);
        excludedAccomClass.setAccomType(excludedAccomType);

        List<PricingAccomClass> excludedPricingAccomClasses = Arrays.asList(excludedAccomClass);

        setField(pricingConfigurationOffsetsPresenter, PricingConfigurationOffsetsPresenter.class.getDeclaredField("baseRoomTypes"), baseAccomTypeNames);
        setField(pricingConfigurationOffsetsPresenter, PricingConfigurationOffsetsPresenter.class.getDeclaredField("excludedPricingAccomClasses"), excludedPricingAccomClasses);

        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.PER_PERSON_PRICING_ENABLED)).thenReturn(false);
        when(pricingConfigurationService.getBaseOccupancyType()).thenReturn(OccupancyType.SINGLE);

        CPConfigOffsetAccomType excludedOffset = new CPConfigOffsetAccomType();
        excludedOffset.setAccomType(excludedAccomType);
        excludedOffset.setOccupancyType(OccupancyType.SINGLE);

        CPConfigOffsetAccomType nonExcludedOffset = new CPConfigOffsetAccomType();
        nonExcludedOffset.setAccomType(nonExcludedAccomType);
        nonExcludedOffset.setOccupancyType(OccupancyType.SINGLE);

        assertFalse(pricingConfigurationOffsetsPresenter.isBaseOccupancyType(excludedOffset));
        assertTrue(pricingConfigurationOffsetsPresenter.isBaseOccupancyType(nonExcludedOffset));
    }

    @Test
    public void validateOneAdultOffsets() {
        AccomType accomType = new AccomType();
        accomType.setName("bad offset");
        List<CPConfigOffsetAccomType> offsets = getCpConfigOffsetAccomTypes(accomType);
        assertTrue(CollectionUtils.isEqualCollection(new HashSet(Arrays.asList(accomType.getName())), pricingConfigurationOffsetsPresenter.validateOneAdultOffsets(offsets)));
    }

    @Test
    public void validateAdultHierarchyOffsets() {
        AccomType typeOne = new AccomType();
        AccomType typeTwo = new AccomType();

        CPConfigOffsetAccomType threeAdultOffset = new CPConfigOffsetAccomType();
        CPConfigOffsetAccomType fourAdultOffset = new CPConfigOffsetAccomType();
        CPConfigOffsetAccomType oneChildOffset = new CPConfigOffsetAccomType();
        CPConfigOffsetAccomType alternateThreeAdultOffset = new CPConfigOffsetAccomType();
        CPConfigOffsetAccomType alternateFourAdultOffset = new CPConfigOffsetAccomType();
        CPConfigOffsetAccomType alternateOneChildOffset = new CPConfigOffsetAccomType();

        typeOne.setId(1);
        typeTwo.setId(2);
        typeOne.setName("typeOne");
        typeTwo.setName("typeTwo");

        threeAdultOffset.setOccupancyType(OccupancyType.THREE_ADULTS);
        fourAdultOffset.setOccupancyType(OccupancyType.FOUR_ADULTS);
        oneChildOffset.setOccupancyType(OccupancyType.ONE_CHILD);
        alternateThreeAdultOffset.setOccupancyType(OccupancyType.THREE_ADULTS);
        alternateFourAdultOffset.setOccupancyType(OccupancyType.FOUR_ADULTS);
        alternateOneChildOffset.setOccupancyType(OccupancyType.ONE_CHILD);

        threeAdultOffset.setAccomType(typeOne);
        fourAdultOffset.setAccomType(typeOne);
        oneChildOffset.setAccomType(typeOne);
        alternateThreeAdultOffset.setAccomType(typeTwo);
        alternateFourAdultOffset.setAccomType(typeTwo);
        alternateOneChildOffset.setAccomType(typeTwo);

        List<CPConfigOffsetAccomType> offsets = Arrays.asList(threeAdultOffset, fourAdultOffset, oneChildOffset, alternateThreeAdultOffset, alternateFourAdultOffset, alternateOneChildOffset);
        offsets.forEach(offset -> {
            offset.setMondayOffsetValue(ZERO);
            offset.setTuesdayOffsetValue(ZERO);
            offset.setWednesdayOffsetValue(ZERO);
            offset.setThursdayOffsetValue(ZERO);
            offset.setFridayOffsetValue(ZERO);
            offset.setSaturdayOffsetValue(ZERO);
            offset.setSundayOffsetValue(ZERO);
        });

        assertTrue(CollectionUtils.isEqualCollection(new HashSet(), pricingConfigurationOffsetsPresenter.validateAdultHierarchyOffsets(offsets)));

        alternateThreeAdultOffset.setMondayOffsetValue(ONE);
        assertTrue(CollectionUtils.isEqualCollection(new HashSet(Arrays.asList("typeTwo")), pricingConfigurationOffsetsPresenter.validateAdultHierarchyOffsets(offsets)));
    }

    @Test
    public void validateChildHierarchyOffsets() {
        AccomType typeOne = new AccomType();
        AccomType typeTwo = new AccomType();

        CPConfigOffsetAccomType threeAdultOffset = new CPConfigOffsetAccomType();
        CPConfigOffsetAccomType oneChildOffset = new CPConfigOffsetAccomType();
        CPConfigOffsetAccomType twoChildOffset = new CPConfigOffsetAccomType();
        CPConfigOffsetAccomType alternateThreeAdultOffset = new CPConfigOffsetAccomType();
        CPConfigOffsetAccomType alternateOneChildOffset = new CPConfigOffsetAccomType();
        CPConfigOffsetAccomType alternateTwoChildOffset = new CPConfigOffsetAccomType();

        typeOne.setId(1);
        typeTwo.setId(2);
        typeOne.setName("typeOne");
        typeTwo.setName("typeTwo");

        threeAdultOffset.setOccupancyType(OccupancyType.THREE_ADULTS);
        oneChildOffset.setOccupancyType(OccupancyType.ONE_CHILD);
        twoChildOffset.setOccupancyType(OccupancyType.TWO_CHILDREN);
        alternateThreeAdultOffset.setOccupancyType(OccupancyType.THREE_ADULTS);
        alternateOneChildOffset.setOccupancyType(OccupancyType.ONE_CHILD);
        alternateTwoChildOffset.setOccupancyType(OccupancyType.TWO_CHILDREN);

        threeAdultOffset.setAccomType(typeOne);
        oneChildOffset.setAccomType(typeOne);
        twoChildOffset.setAccomType(typeOne);
        alternateThreeAdultOffset.setAccomType(typeTwo);
        alternateOneChildOffset.setAccomType(typeTwo);
        alternateTwoChildOffset.setAccomType(typeTwo);

        List<CPConfigOffsetAccomType> offsets = Arrays.asList(threeAdultOffset, oneChildOffset, twoChildOffset, alternateThreeAdultOffset, alternateOneChildOffset, alternateTwoChildOffset);
        offsets.forEach(offset -> {
            offset.setMondayOffsetValue(ZERO);
            offset.setTuesdayOffsetValue(ZERO);
            offset.setWednesdayOffsetValue(ZERO);
            offset.setThursdayOffsetValue(ZERO);
            offset.setFridayOffsetValue(ZERO);
            offset.setSaturdayOffsetValue(ZERO);
            offset.setSundayOffsetValue(ZERO);
        });

        assertTrue(CollectionUtils.isEqualCollection(new HashSet(), pricingConfigurationOffsetsPresenter.validateChildHierarchyOffsets(offsets)));

        alternateOneChildOffset.setMondayOffsetValue(ONE);
        assertTrue(CollectionUtils.isEqualCollection(new HashSet(Arrays.asList("typeTwo")), pricingConfigurationOffsetsPresenter.validateChildHierarchyOffsets(offsets)));
    }

    @Test
    public void validateChildAgeHierarchyOffsets() {
        AccomType typeOne = new AccomType();
        AccomType typeTwo = new AccomType();

        CPConfigOffsetAccomType threeAdultOffset = new CPConfigOffsetAccomType();
        CPConfigOffsetAccomType childBucketOne = new CPConfigOffsetAccomType();
        CPConfigOffsetAccomType childBucketTwo = new CPConfigOffsetAccomType();
        CPConfigOffsetAccomType alternateThreeAdultOffset = new CPConfigOffsetAccomType();
        CPConfigOffsetAccomType alternateChildBucketOne = new CPConfigOffsetAccomType();
        CPConfigOffsetAccomType alternateChildBucketTwo = new CPConfigOffsetAccomType();

        typeOne.setId(1);
        typeTwo.setId(2);
        typeOne.setName("typeOne");
        typeTwo.setName("typeTwo");

        threeAdultOffset.setOccupancyType(OccupancyType.THREE_ADULTS);
        childBucketOne.setOccupancyType(OccupancyType.CHILD_BUCKET_1);
        childBucketTwo.setOccupancyType(OccupancyType.CHILD_BUCKET_2);
        alternateThreeAdultOffset.setOccupancyType(OccupancyType.THREE_ADULTS);
        alternateChildBucketOne.setOccupancyType(OccupancyType.CHILD_BUCKET_1);
        alternateChildBucketTwo.setOccupancyType(OccupancyType.CHILD_BUCKET_2);

        threeAdultOffset.setAccomType(typeOne);
        childBucketOne.setAccomType(typeOne);
        childBucketTwo.setAccomType(typeOne);
        alternateThreeAdultOffset.setAccomType(typeTwo);
        alternateChildBucketOne.setAccomType(typeTwo);
        alternateChildBucketTwo.setAccomType(typeTwo);

        List<CPConfigOffsetAccomType> offsets = Arrays.asList(threeAdultOffset, childBucketOne, childBucketTwo, alternateThreeAdultOffset, alternateChildBucketOne, alternateChildBucketTwo);
        offsets.forEach(offset -> {
            offset.setMondayOffsetValue(ZERO);
            offset.setTuesdayOffsetValue(ZERO);
            offset.setWednesdayOffsetValue(ZERO);
            offset.setThursdayOffsetValue(ZERO);
            offset.setFridayOffsetValue(ZERO);
            offset.setSaturdayOffsetValue(ZERO);
            offset.setSundayOffsetValue(ZERO);
        });

        assertTrue(CollectionUtils.isEqualCollection(new HashSet(), pricingConfigurationOffsetsPresenter.validateChildAgeHierarchyOffsets(offsets)));

        alternateChildBucketOne.setMondayOffsetValue(ONE);
        assertTrue(CollectionUtils.isEqualCollection(new HashSet(Arrays.asList("typeTwo")), pricingConfigurationOffsetsPresenter.validateChildAgeHierarchyOffsets(offsets)));
    }

    @Test
    public void validateChildNegativeOffsets() {
        CPConfigOffsetAccomType childQuantity = new CPConfigOffsetAccomType();
        CPConfigOffsetAccomType childAge = new CPConfigOffsetAccomType();

        AccomType accomTypeOne = new AccomType();
        AccomType accomTypeTwo = new AccomType();

        accomTypeOne.setName("accomTypeOne");
        accomTypeTwo.setName("accomTypeTwo");

        childQuantity.setOccupancyType(OccupancyType.ONE_CHILD);
        childAge.setOccupancyType(OccupancyType.CHILD_BUCKET_1);

        childQuantity.setAccomType(accomTypeOne);
        childAge.setAccomType(accomTypeTwo);

        List<CPConfigOffsetAccomType> offsets = Arrays.asList(childQuantity, childAge);
        offsets.forEach(offset -> {
            offset.setMondayOffsetValue(ONE);
            offset.setTuesdayOffsetValue(ONE);
            offset.setWednesdayOffsetValue(ONE);
            offset.setThursdayOffsetValue(ONE);
            offset.setFridayOffsetValue(ONE);
            offset.setSaturdayOffsetValue(ONE);
            offset.setSundayOffsetValue(ONE);
        });

        assertTrue(CollectionUtils.isEqualCollection(new HashSet(), pricingConfigurationOffsetsPresenter.validateChildNegativeOffsets(offsets)));

        childQuantity.setMondayOffsetValue(valueOf(-1));
        assertTrue(CollectionUtils.isEqualCollection(new HashSet(Arrays.asList("accomTypeOne")), pricingConfigurationOffsetsPresenter.validateChildNegativeOffsets(offsets)));

        childAge.setTuesdayOffsetValue(valueOf(-1));
        assertTrue(CollectionUtils.isEqualCollection(new HashSet(Arrays.asList("accomTypeOne", "accomTypeTwo")), pricingConfigurationOffsetsPresenter.validateChildNegativeOffsets(offsets)));
    }

    @Test
    public void perPersonPricingOffsetCheck() {
        CPConfigOffsetAccomType offset = new CPConfigOffsetAccomType();
        CPConfigOffsetAccomType higherOffset = new CPConfigOffsetAccomType();

        offset.setMondayOffsetValue(ZERO);
        offset.setTuesdayOffsetValue(ZERO);
        offset.setWednesdayOffsetValue(ZERO);
        offset.setThursdayOffsetValue(ZERO);
        offset.setFridayOffsetValue(ZERO);
        offset.setSaturdayOffsetValue(ZERO);
        offset.setSundayOffsetValue(ZERO);

        higherOffset.setMondayOffsetValue(ZERO);
        higherOffset.setTuesdayOffsetValue(ZERO);
        higherOffset.setWednesdayOffsetValue(ZERO);
        higherOffset.setThursdayOffsetValue(ZERO);
        higherOffset.setFridayOffsetValue(ZERO);
        higherOffset.setSaturdayOffsetValue(ZERO);
        higherOffset.setSundayOffsetValue(ZERO);

        assertTrue(pricingConfigurationOffsetsPresenter.perPersonPricingOffsetCheck(offset, higherOffset));

        higherOffset.setMondayOffsetValue(TEN);
        assertTrue(pricingConfigurationOffsetsPresenter.perPersonPricingOffsetCheck(offset, higherOffset));

        offset.setTuesdayOffsetValue(ONE);
        assertFalse(pricingConfigurationOffsetsPresenter.perPersonPricingOffsetCheck(offset, higherOffset));
    }

    @Test
    public void shouldFilterOffsetsAgainstDiscontinuedAccomTypesIfRecodingUIEnabled() {
        AccomType activeAccomType1 = getAccomType(Status.ACTIVE, "Active_RT_1");
        AccomType activeAccomType2 = getAccomType(Status.ACTIVE, "Active_RT_2");
        AccomType discontinuedAccomType = getAccomType(Status.INACTIVE, "Discontinued_RT");

        LinkedHashMap<AccomType, LinkedList<CPConfigOffsetAccomType>> accomTypeOffsets = new LinkedHashMap<>();
        LinkedList<CPConfigOffsetAccomType> cpConfigOffsetsForActiveRT1 = new LinkedList<>(getCpConfigOffsetAccomTypes(activeAccomType1));
        accomTypeOffsets.put(activeAccomType1, cpConfigOffsetsForActiveRT1);
        accomTypeOffsets.put(activeAccomType2, null);
        accomTypeOffsets.put(discontinuedAccomType, new LinkedList<>(getCpConfigOffsetAccomTypes(discontinuedAccomType)));

        LinkedHashMap<PricingAccomClass, LinkedHashMap<AccomType, LinkedList<CPConfigOffsetAccomType>>> offsets = new LinkedHashMap<>();
        PricingAccomClass pricingAccomClass = new PricingAccomClass();
        offsets.put(pricingAccomClass, accomTypeOffsets);

        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.ROOM_TYPE_RECODING_UI_ENABLED)).thenReturn(true);

        pricingConfigurationOffsetsPresenter.filterOffsetsAgainstDiscontinuedAccomTypes(offsets);

        LinkedHashMap<AccomType, LinkedList<CPConfigOffsetAccomType>> filteredAccomTypeOffsets = offsets.get(pricingAccomClass);
        assertThat(filteredAccomTypeOffsets.size(), is(2));
        assertThat(filteredAccomTypeOffsets, not(hasKey(discontinuedAccomType)));
        assertThat(filteredAccomTypeOffsets, hasEntry(activeAccomType1, cpConfigOffsetsForActiveRT1));
        assertThat(filteredAccomTypeOffsets, hasEntry(activeAccomType2, null));
    }

    private AccomType getAccomType(Status displayStatus, String accomCode) {
        AccomType accomType = new AccomType();
        accomType.setDisplayStatusId(displayStatus.getId());
        accomType.setAccomTypeCode(accomCode);
        return accomType;
    }

    private List<CPConfigOffsetAccomType> getCpConfigOffsetAccomTypes(AccomType accomType) {
        CPConfigOffsetAccomType oneAdultOffset = new CPConfigOffsetAccomType();
        CPConfigOffsetAccomType badOneAdultOffset = new CPConfigOffsetAccomType();
        CPConfigOffsetAccomType extraOffset = new CPConfigOffsetAccomType();

        extraOffset.setOccupancyType(OccupancyType.EXTRA_ADULT);

        oneAdultOffset.setOccupancyType(OccupancyType.SINGLE);
        oneAdultOffset.setMondayOffsetValue(ZERO);
        oneAdultOffset.setTuesdayOffsetValue(valueOf(-1));
        oneAdultOffset.setWednesdayOffsetValue(ZERO);
        oneAdultOffset.setThursdayOffsetValue(ZERO);
        oneAdultOffset.setFridayOffsetValue(ZERO);
        oneAdultOffset.setSaturdayOffsetValue(ZERO);
        oneAdultOffset.setSundayOffsetValue(ZERO);

        badOneAdultOffset.setOccupancyType(OccupancyType.SINGLE);
        badOneAdultOffset.setMondayOffsetValue(ONE);
        badOneAdultOffset.setTuesdayOffsetValue(valueOf(-1));
        badOneAdultOffset.setWednesdayOffsetValue(ZERO);
        badOneAdultOffset.setThursdayOffsetValue(ZERO);
        badOneAdultOffset.setFridayOffsetValue(ZERO);
        badOneAdultOffset.setSaturdayOffsetValue(ZERO);
        badOneAdultOffset.setSundayOffsetValue(ZERO);

        badOneAdultOffset.setAccomType(accomType);

        return Arrays.asList(oneAdultOffset, badOneAdultOffset, extraOffset);
    }

    @Test
    public void testSeasonsFilterWithPastUnchecked() {
        pricingConfigurationOffsetsPresenter.setSeasons(createSeasonWrapperList());
        List<PricingConfigurationOffsetSeasonWrapper> filteredSeasons = pricingConfigurationOffsetsPresenter.getSeasonsOnChangeEvent(SeasonsFilterBar.SeasonsFilterBarType.PAST, false);
        assertEquals(2, filteredSeasons.size());
        assertEquals(PRESENT_SEASON, filteredSeasons.get(0).getName());
        assertEquals(FUTURE_SEASON, filteredSeasons.get(1).getName());
    }

    @Test
    public void testSeasonsFilterWithPresentUnchecked() {
        pricingConfigurationOffsetsPresenter.setSeasons(createSeasonWrapperList());
        List<PricingConfigurationOffsetSeasonWrapper> filteredSeasons = pricingConfigurationOffsetsPresenter.getSeasonsOnChangeEvent(SeasonsFilterBar.SeasonsFilterBarType.PRESENT, false);
        assertEquals(2, filteredSeasons.size());
        assertEquals(PAST_SEASON, filteredSeasons.get(0).getName());
        assertEquals(FUTURE_SEASON, filteredSeasons.get(1).getName());
    }

    @Test
    public void testSeasonsFilterWithFutureUnchecked() {
        pricingConfigurationOffsetsPresenter.setSeasons(createSeasonWrapperList());
        List<PricingConfigurationOffsetSeasonWrapper> filteredSeasons = pricingConfigurationOffsetsPresenter.getSeasonsOnChangeEvent(SeasonsFilterBar.SeasonsFilterBarType.FUTURE, false);
        assertEquals(2, filteredSeasons.size());
        assertEquals(PAST_SEASON, filteredSeasons.get(0).getName());
        assertEquals(PRESENT_SEASON, filteredSeasons.get(1).getName());
    }


    @Test
    public void testDeleteSeason() {
        // Given
        List<PricingConfigurationOffsetSeasonWrapper> existingSeasons = createSeasonWrapperList();
        pricingConfigurationOffsetsPresenter.setSeasons(existingSeasons);
        PricingConfigurationOffsetSeasonWrapper seasonToDelete = existingSeasons.get(2);
        pricingConfigurationOffsetsPresenter.setPricingAccomClasses(new ArrayList<>());
        pricingConfigurationOffsetsPresenter.setCpConfigOffsetAccomTypes(new ArrayList<>());
        pricingConfigurationOffsetsPresenter.setPricingConfigurationDTO(createPricingConfigurationDTOWithPrimaryPricedProduct(null));

        // When
        when(uiContext.getSystemCaughtUpDateAsLocalDate()).thenReturn(localDate);
        doAnswer(new Answer() {
            @Override
            public Void answer(InvocationOnMock invocation) {
                existingSeasons.remove(2);
                return null;
            }
        }).when(seasonService).removeAndJoin(existingSeasons, seasonToDelete, localDate);
        pricingConfigurationOffsetsPresenter.deleteSeason(seasonToDelete);

        // Then
        assertEquals(2, existingSeasons.size());
    }

    @Test
    public void testUpdateSeason() {
        // Given
        pricingConfigurationOffsetsPresenter.setPricingConfigurationDTO(createPricingConfigurationDTOWithPrimaryPricedProduct(null));
        List<PricingConfigurationOffsetSeasonWrapper> existingSeasons = createSeasonWrapperList();
        PricingConfigurationOffsetSeasonWrapper seasonToDelete = existingSeasons.get(1);
        PricingConfigurationOffsetWrapper pricingConfigurationOffsetWrapper = new PricingConfigurationOffsetWrapper();
        CPConfigOffsetAccomType cpConfigOffsetAccomType = new CPConfigOffsetAccomType();
        cpConfigOffsetAccomType.setEndDate(seasonToDelete.getEndDate());
        cpConfigOffsetAccomType.setId(99);
        pricingConfigurationOffsetWrapper.setCpConfigOffsetAccomType(cpConfigOffsetAccomType);
        seasonToDelete.getOffsets().add(pricingConfigurationOffsetWrapper);
        List<PricingAccomClass> excludedPricingAccomClasses = new ArrayList<>();

        // When
        when(dateService.getCaughtUpLocalDate()).thenReturn(localDate);
        pricingConfigurationOffsetsPresenter.setupParameters();
        pricingConfigurationOffsetsPresenter.loadData();
        pricingConfigurationOffsetsPresenter.init(false);
        pricingConfigurationOffsetsPresenter.setSeasons(existingSeasons);
        doAnswer(invocation -> {
            seasonToDelete.setEndDate(localDate.minusDays(1));
            return null;
        }).when(offsetService).updateOffsetsForSeason(Collections.singletonList(pricingConfigurationOffsetWrapper.getCpConfigOffsetAccomType()), excludedPricingAccomClasses);

        when(pricingConfigurationService.getBaseOccupancyType()).thenReturn(OccupancyType.SINGLE);
        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(java.time.LocalDate.parse("2023-03-03"));
        pricingConfigurationOffsetsPresenter.deleteSeason(seasonToDelete);

        // Then
        assertEquals(localDate.minusDays(1), seasonToDelete.getEndDate());
        verify(offsetService, times(1)).
                updateOffsetsForSeason(Mockito.anyListOf(CPConfigOffsetAccomType.class), Mockito.anyListOf(PricingAccomClass.class));

        verify(pricingConfigurationLTBDEService).enabledLTBDEForOffsetDeleteIfApplicable(anyList(), any(java.time.LocalDate.class), eq(java.time.LocalDate.parse("2023-03-03")));
    }

    @Test
    public void persistSeasonImmediatelyFutureSeasonTest() {
        // Given
        pricingConfigurationOffsetsPresenter.setPricingConfigurationDTO(createPricingConfigurationDTOWithPrimaryPricedProduct(null));
        List<PricingConfigurationOffsetSeasonWrapper> existingSeasons = new ArrayList<>();
        pricingConfigurationOffsetsPresenter.setupParameters();
        pricingConfigurationOffsetsPresenter.loadData();
        pricingConfigurationOffsetsPresenter.init(false);
        pricingConfigurationOffsetsPresenter.setSeasons(existingSeasons);

        PricingConfigurationOffsetSeasonWrapper seasonToBeAdded = createSeasonWrapper(localDate.plusDays(10), localDate.plusDays(30), "dummy");
        List<PricingConfigurationOffsetSeasonWrapper> updatedSeasons = new ArrayList<>();
        updatedSeasons.add(seasonToBeAdded);

        // When
        when(seasonService.applySplit(existingSeasons, seasonToBeAdded, localDate)).thenReturn(updatedSeasons);
        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(java.time.LocalDate.parse("2023-12-01"));
        pricingConfigurationOffsetsPresenter.saveSeason(seasonToBeAdded, false);

        // Then
        verify(offsetService, times(1)).
                updatePricingOffsets(Mockito.anyListOf(CPConfigOffsetAccomType.class), Mockito.anyListOf(CPConfigOffsetAccomType.class), Mockito.anyListOf(PricingAccomClass.class), Mockito.anyListOf(AccomType.class), Mockito.anySetOf(CPConfigOffsetAccomType.class), Mockito.any(), Mockito.anyBoolean());

        verify(pricingConfigurationLTBDEService).enabledLTBDEIfApplicableForOffsetChange(anyList(), eq(java.time.LocalDate.parse("2023-12-01")));
    }

    @Test
    public void persistSeasonShowErrorWhenSeasonNameIsTooLarge() {
        // Given
        pricingConfigurationOffsetsPresenter.setPricingConfigurationDTO(createPricingConfigurationDTOWithPrimaryPricedProduct(null));
        List<PricingConfigurationOffsetSeasonWrapper> existingSeasons = new ArrayList<>();
        pricingConfigurationOffsetsPresenter.setLogger(mock(Logger.class));
        pricingConfigurationOffsetsPresenter.setupParameters();
        pricingConfigurationOffsetsPresenter.loadData();
        pricingConfigurationOffsetsPresenter.init(false);
        pricingConfigurationOffsetsPresenter.setSeasons(existingSeasons);

        PricingConfigurationOffsetSeasonWrapper seasonToBeAdded = createSeasonWrapper(localDate.plusDays(10), localDate.plusDays(30), "dummy");
        List<PricingConfigurationOffsetSeasonWrapper> updatedSeasons = new ArrayList<>();
        updatedSeasons.add(seasonToBeAdded);

        // When
        when(seasonService.applySplit(existingSeasons, seasonToBeAdded, localDate)).thenReturn(updatedSeasons);
        Exception seasonNameTooLargeException = new SeasonNameTooLargeException("VeryLargeName");

        doThrow(seasonNameTooLargeException).when(offsetService).updatePricingOffsets(Mockito.anyListOf(CPConfigOffsetAccomType.class), Mockito.anyListOf(CPConfigOffsetAccomType.class), Mockito.anyListOf(PricingAccomClass.class), Mockito.anyListOf(AccomType.class),
                Mockito.anySetOf(CPConfigOffsetAccomType.class), Mockito.any(), Mockito.anyBoolean());

        pricingConfigurationOffsetsPresenter.saveSeason(seasonToBeAdded, false);

        verify(view).showError("New Season to be created after splitting existing season may exceed limit of 255 characters. Please shorten existing season name:\nVeryLargeName.");
        verify(view, atLeastOnce()).afterPresenterInit();
    }

    @Test
    public void persistSeasonImmediatelyFutureSeasonWithNoRankSoftWarningTest() {
        // Given
        List<PricingConfigurationOffsetSeasonWrapper> existingSeasons = new ArrayList<>();
        pricingConfigurationOffsetsPresenter.setPricingConfigurationDTO(createPricingConfigurationDTOWithPrimaryPricedProduct(null));
        pricingConfigurationOffsetsPresenter.setupParameters();
        pricingConfigurationOffsetsPresenter.loadData();
        pricingConfigurationOffsetsPresenter.init(false);
        pricingConfigurationOffsetsPresenter.setSeasons(existingSeasons);

        PricingConfigurationOffsetSeasonWrapper seasonToBeAdded = createSeasonWrapper(localDate.plusDays(10), localDate.plusDays(30), "dummy");
        List<PricingConfigurationOffsetSeasonWrapper> updatedSeasons = new ArrayList<>();
        updatedSeasons.add(seasonToBeAdded);

        // When
        when(seasonService.applySplit(existingSeasons, seasonToBeAdded, localDate)).thenReturn(updatedSeasons);
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_CP_FLOOR_AND_CEILING_SOFT_WARNING)).thenReturn(true);
        pricingConfigurationOffsetsPresenter.saveSeason(seasonToBeAdded, false);

        // Then
        verify(offsetService, times(1)).
                updatePricingOffsets(Mockito.anyListOf(CPConfigOffsetAccomType.class), Mockito.anyListOf(CPConfigOffsetAccomType.class), Mockito.anyListOf(PricingAccomClass.class), Mockito.anyListOf(AccomType.class), Mockito.anySetOf(CPConfigOffsetAccomType.class), Mockito.any(), Mockito.anyBoolean());
    }

 @Test
    public void showPPPMessageWhenOverlapOccurs() throws NoSuchFieldException, IllegalAccessException
    {
        pricingConfigurationOffsetsPresenter.setPricingConfigurationDTO(createPricingConfigurationDTOWithPrimaryPricedProduct(null));
        PricingConfigurationOffsetWrapper pricingConfigurationOffsetWrapperExisting = new PricingConfigurationOffsetWrapper();
        CPConfigOffsetAccomType cpConfigOffsetAccomTypeExisting = new CPConfigOffsetAccomType();
        cpConfigOffsetAccomTypeExisting.setEndDate(localDate.plusDays(30));
        cpConfigOffsetAccomTypeExisting.setId(99);
        pricingConfigurationOffsetWrapperExisting.setCpConfigOffsetAccomType(cpConfigOffsetAccomTypeExisting);

        List<CPConfigOffsetAccomType> cpConfigOffsetAccomTypes = new ArrayList<>();
        List<PricingConfigurationOffsetSeasonWrapper> existingSeasons = createSeasonWrapperList();
        existingSeasons.get(2).getOffsets().add(pricingConfigurationOffsetWrapperExisting);

        PricingConfigurationOffsetWrapper pricingConfigurationOffsetWrapperNew = new PricingConfigurationOffsetWrapper();
        CPConfigOffsetAccomType cpConfigOffsetAccomTypeNew = new CPConfigOffsetAccomType();
        AccomClass accomClass = new AccomClass();
        cpConfigOffsetAccomTypeNew.setEndDate(localDate.plusDays(40));
        AccomType accomType = new AccomType();
        accomType.setId(1);
        accomType.setName("STD");
        cpConfigOffsetAccomTypeNew.setId(199);
        cpConfigOffsetAccomTypeNew.setOffsetMethod(PERCENTAGE);
        cpConfigOffsetAccomTypeNew.setAccomType(accomType);
        cpConfigOffsetAccomTypeNew.setMondayOffsetValueWithTax(new BigDecimal(12.0));
        cpConfigOffsetAccomTypeNew.setTuesdayOffsetValueWithTax(new BigDecimal(12.0));
        cpConfigOffsetAccomTypeNew.setWednesdayOffsetValueWithTax(new BigDecimal(12.0));
        cpConfigOffsetAccomTypeNew.setThursdayOffsetValueWithTax(new BigDecimal(12.0));
        cpConfigOffsetAccomTypeNew.setFridayOffsetValueWithTax(new BigDecimal(12.0));
        cpConfigOffsetAccomTypeNew.setSaturdayOffsetValueWithTax(new BigDecimal(12.0));
        cpConfigOffsetAccomTypeNew.setSundayOffsetValueWithTax(new BigDecimal(12.0));
        cpConfigOffsetAccomTypeNew.setMondayOffsetValue(new BigDecimal(12.0));
        cpConfigOffsetAccomTypeNew.setTuesdayOffsetValue(new BigDecimal(12.0));
        cpConfigOffsetAccomTypeNew.setWednesdayOffsetValue(new BigDecimal(12.0));
        cpConfigOffsetAccomTypeNew.setThursdayOffsetValue(new BigDecimal(12.0));
        cpConfigOffsetAccomTypeNew.setFridayOffsetValue(new BigDecimal(-100.0));
        cpConfigOffsetAccomTypeNew.setSaturdayOffsetValue(new BigDecimal(-100.0));
        cpConfigOffsetAccomTypeNew.setSundayOffsetValue(new BigDecimal(12.0));
        cpConfigOffsetAccomTypeNew.setOccupancyType(OccupancyType.SINGLE);
        cpConfigOffsetAccomTypes.add(cpConfigOffsetAccomTypeNew);

        pricingConfigurationOffsetWrapperNew.setCpConfigOffsetAccomType(cpConfigOffsetAccomTypeNew);

        List<PricingConfigurationOffsetSeasonWrapper> updatedSeasons = new ArrayList<>();
        PricingConfigurationOffsetSeasonWrapper seasonToBeAdded = createSeasonWrapper(localDate.plusDays(20), localDate.plusDays(40), "dummy");
        seasonToBeAdded.getOffsets().add(pricingConfigurationOffsetWrapperNew);
        updatedSeasons.add(seasonToBeAdded);

        List<PricingConfigurationOffsetSeasonWrapper> overlappingSeasons = new ArrayList<>();
        overlappingSeasons.add(existingSeasons.get(2));

        pricingConfigurationOffsetsPresenter.setupParameters();
        pricingConfigurationOffsetsPresenter.loadData();
        pricingConfigurationOffsetsPresenter.init(false);
        pricingConfigurationOffsetsPresenter.setSeasons(existingSeasons);



        // Access the private field using reflection
        Field privateField = PricingConfigurationOffsetsPresenter.class.getDeclaredField("isContinuousPricingEnabled");
        privateField.setAccessible(true);

        // Set the value of the private field
        privateField.set(pricingConfigurationOffsetsPresenter, true);
        // When
        when(pricingConfigurationService.getBaseOccupancyType()).thenReturn(OccupancyType.SINGLE);
        when(seasonService.applySplit(existingSeasons, seasonToBeAdded, localDate)).thenReturn(updatedSeasons);
        Set<String> pppWarningMessage = new HashSet<>(Collections.singleton("STD"));
        when(seasonService.getOverlappingSeasons(existingSeasons, seasonToBeAdded)).thenReturn(overlappingSeasons);
        pricingConfigurationOffsetsPresenter.setPerPersonPricingEnabled(true);
        pricingConfigurationOffsetsPresenter.saveSeason(seasonToBeAdded, false);
        // Then
        assertNotNull(pppWarningMessage);
    }

    @Test
    public void persistSeasonImmediatelyFutureSeasonWithOverlapTest() {
        // Given
        pricingConfigurationOffsetsPresenter.setPricingConfigurationDTO(createPricingConfigurationDTOWithPrimaryPricedProduct(null));
        PricingConfigurationOffsetWrapper pricingConfigurationOffsetWrapperExisting = new PricingConfigurationOffsetWrapper();
        CPConfigOffsetAccomType cpConfigOffsetAccomTypeExisting = new CPConfigOffsetAccomType();
        cpConfigOffsetAccomTypeExisting.setEndDate(localDate.plusDays(30));
        cpConfigOffsetAccomTypeExisting.setId(99);
        pricingConfigurationOffsetWrapperExisting.setCpConfigOffsetAccomType(cpConfigOffsetAccomTypeExisting);

        List<PricingConfigurationOffsetSeasonWrapper> existingSeasons = createSeasonWrapperList();
        existingSeasons.get(2).getOffsets().add(pricingConfigurationOffsetWrapperExisting);

        PricingConfigurationOffsetWrapper pricingConfigurationOffsetWrapperNew = new PricingConfigurationOffsetWrapper();
        CPConfigOffsetAccomType cpConfigOffsetAccomTypeNew = new CPConfigOffsetAccomType();
        cpConfigOffsetAccomTypeNew.setEndDate(localDate.plusDays(40));
        cpConfigOffsetAccomTypeNew.setId(199);
        pricingConfigurationOffsetWrapperNew.setCpConfigOffsetAccomType(cpConfigOffsetAccomTypeNew);

        List<PricingConfigurationOffsetSeasonWrapper> updatedSeasons = new ArrayList<>();
        PricingConfigurationOffsetSeasonWrapper seasonToBeAdded = createSeasonWrapper(localDate.plusDays(20), localDate.plusDays(40), "dummy");
        seasonToBeAdded.getOffsets().add(pricingConfigurationOffsetWrapperNew);
        updatedSeasons.add(seasonToBeAdded);

        List<PricingConfigurationOffsetSeasonWrapper> overlappingSeasons = new ArrayList<>();
        overlappingSeasons.add(existingSeasons.get(2));

        pricingConfigurationOffsetsPresenter.setupParameters();
        pricingConfigurationOffsetsPresenter.loadData();
        pricingConfigurationOffsetsPresenter.init(false);
        pricingConfigurationOffsetsPresenter.setSeasons(existingSeasons);

        // When
        when(pricingConfigurationService.getBaseOccupancyType()).thenReturn(OccupancyType.SINGLE);
        when(seasonService.applySplit(existingSeasons, seasonToBeAdded, localDate)).thenReturn(updatedSeasons);
        when(seasonService.getOverlappingSeasons(existingSeasons, seasonToBeAdded)).thenReturn(overlappingSeasons);

        pricingConfigurationOffsetsPresenter.saveSeason(seasonToBeAdded, false);

        // Then
        verify(offsetService, times(1))
                .updatePricingOffsets(Mockito.anyListOf(CPConfigOffsetAccomType.class), Mockito.anyListOf(CPConfigOffsetAccomType.class), Mockito.anyListOf(PricingAccomClass.class), Mockito.anyListOf(AccomType.class), Mockito.anySetOf(CPConfigOffsetAccomType.class), Mockito.any(), Mockito.anyBoolean());
    }

    @Test
    public void testIsSeasonPriceExcludedRateGreaterThanSupplement() {
        assertTrue(pricingConfigurationOffsetsPresenter.isSeasonPriceExcludedRateGreaterThanSupplement(null, null, null));
        assertTrue(pricingConfigurationOffsetsPresenter.isSeasonPriceExcludedRateGreaterThanSupplement(BigDecimal.ZERO, null, null));
        assertTrue(pricingConfigurationOffsetsPresenter.isSeasonPriceExcludedRateGreaterThanSupplement(BigDecimal.ZERO, null, null));
        assertTrue(pricingConfigurationOffsetsPresenter.isSeasonPriceExcludedRateGreaterThanSupplement(BigDecimal.valueOf(10), BigDecimal.valueOf(5), null));
        assertFalse(pricingConfigurationOffsetsPresenter.isSeasonPriceExcludedRateGreaterThanSupplement(BigDecimal.valueOf(11), BigDecimal.valueOf(13), BigDecimal.valueOf(11)));
        assertFalse(pricingConfigurationOffsetsPresenter.isSeasonPriceExcludedRateGreaterThanSupplement(BigDecimal.valueOf(12), BigDecimal.valueOf(12), BigDecimal.valueOf(11)));
        assertFalse(pricingConfigurationOffsetsPresenter.isSeasonPriceExcludedRateGreaterThanSupplement(BigDecimal.valueOf(9), null, BigDecimal.valueOf(15)));
        assertFalse(pricingConfigurationOffsetsPresenter.isSeasonPriceExcludedRateGreaterThanSupplement(BigDecimal.valueOf(9), null, BigDecimal.valueOf(9)));
    }

    @Test
    public void testIsDateBetweenRange() {
        LocalDate today = LocalDate.now();
        assertTrue(pricingConfigurationOffsetsPresenter.isDateBetweenRange(today, today.plusDays(10), today.plusDays(2)));
        assertTrue(pricingConfigurationOffsetsPresenter.isDateBetweenRange(today, today.plusDays(10), today));
        assertTrue(pricingConfigurationOffsetsPresenter.isDateBetweenRange(today, today.plusDays(10), today.plusDays(10)));

        assertFalse(pricingConfigurationOffsetsPresenter.isDateBetweenRange(today, today.plusDays(10), today.plusDays(11)));
        assertFalse(pricingConfigurationOffsetsPresenter.isDateBetweenRange(today, today.plusDays(10), today.minusDays(1)));

        assertTrue(pricingConfigurationOffsetsPresenter.isDateBetweenRange(today, today.plusDays(10), today.plusDays(9)));
        assertTrue(pricingConfigurationOffsetsPresenter.isDateBetweenRange(today, today.plusDays(10), today.plusDays(1)));
    }

    @Test
    public void testIsPriceExcludedSeasonValid() {
        PricingAccomClass pricingAccomClass = createDummyPricingAccomClass("Test");
        assertFalse(pricingConfigurationOffsetsPresenter.isPriceExcludedSeasonValid(
                createPricingConfigurationOffsetWrapperForTaxInclusive(5, 5, 5, 5, 5, 5, 5, pricingAccomClass, new AccomType(), PricingConfigurationNodeLevel.ROOM_CLASS, new ArrayList<>()),
                createAccomTypeSupplement(5, 5, 5, 5, 5, 5, 5),
                createAccomTypeSupplement(5, 5, 5, 5, 5, 5, 5)));

        assertTrue(pricingConfigurationOffsetsPresenter.isPriceExcludedSeasonValid(
                createPricingConfigurationOffsetWrapperForTaxInclusive(5, 5, 5, 5, 5, 5, 5, pricingAccomClass, new AccomType(), PricingConfigurationNodeLevel.ROOM_CLASS, new ArrayList<>()),
                createAccomTypeSupplement(4, 4, 4, 4, 4, 4, 4),
                createAccomTypeSupplement(6, 6, 6, 6, 6, 6, 6)));

        assertFalse(pricingConfigurationOffsetsPresenter.isPriceExcludedSeasonValid(
                createPricingConfigurationOffsetWrapperForTaxInclusive(2, 2, 2, 2, 2, 2, 2, pricingAccomClass, new AccomType(), PricingConfigurationNodeLevel.ROOM_CLASS, new ArrayList<>()),
                createAccomTypeSupplement(4, 4, 4, 4, 4, 4, 4),
                createAccomTypeSupplement(6, 6, 6, 6, 6, 6, 6)));
    }

    @Test
    public void testIsWrapperValidForSupplementRestriction() {
        LocalDate startDate = LocalDate.now();
        LocalDate endDate = startDate.plusDays(10);

        AccomType accomType = new AccomType();
        accomType.setId(8);
        PricingConfigurationOffsetWrapper test = createPricingConfigurationOffsetWrapperForTaxInclusive(7, 7, 7, 7, 7, 7, 7,
                createDummyPricingAccomClass("Test"),
                accomType,
                PricingConfigurationNodeLevel.ROOM_CLASS,
                new ArrayList<>());

        AccomTypeSupplement accomTypeSupplementDefault = createAccomTypeSupplement(5, 5, 5, 5, 5, 5, 5);
        accomTypeSupplementDefault.setAccomType(accomType);
        AccomTypeSupplement accomTypeSupplementSeason = createAccomTypeSupplement(8, 8, 8, 8, 8, 8, 8);
        accomTypeSupplementSeason.setAccomType(accomType);
        accomTypeSupplementSeason.setStartDate(startDate);
        accomTypeSupplementSeason.setEndDate(endDate);

        pricingConfigurationOffsetsPresenter.setFixedValueAccomTypeSupplements(Arrays.asList(accomTypeSupplementDefault));

        assertTrue(pricingConfigurationOffsetsPresenter.isWrapperValidForSupplementRestriction(startDate, endDate, test));

        pricingConfigurationOffsetsPresenter.setFixedValueAccomTypeSupplements(Arrays.asList(accomTypeSupplementDefault, accomTypeSupplementSeason));
        assertFalse(pricingConfigurationOffsetsPresenter.isWrapperValidForSupplementRestriction(startDate, endDate, test));
    }

    @Test
    public void testIsWrapperValidForSupplementRestriction_NullDefault_WrapperPositive() {
        LocalDate startDate = LocalDate.now();
        LocalDate endDate = startDate.plusDays(10);

        AccomType accomType = new AccomType();
        accomType.setId(8);
        PricingConfigurationOffsetWrapper test = createPricingConfigurationOffsetWrapperForTaxInclusive(1, 1, 1, 1, 1, 1, 1,
                createDummyPricingAccomClass("Test"),
                accomType,
                PricingConfigurationNodeLevel.ROOM_CLASS,
                new ArrayList<>());

        AccomTypeSupplement accomTypeSupplementSeason = createAccomTypeSupplement(8, 8, 8, 8, 8, 8, 8);
        accomTypeSupplementSeason.setAccomType(accomType);
        accomTypeSupplementSeason.setStartDate(startDate);
        accomTypeSupplementSeason.setEndDate(endDate);

        pricingConfigurationOffsetsPresenter.setFixedValueAccomTypeSupplements(new ArrayList<>());

        assertTrue(pricingConfigurationOffsetsPresenter.isWrapperValidForSupplementRestriction(startDate, endDate, test));

        pricingConfigurationOffsetsPresenter.setFixedValueAccomTypeSupplements(Arrays.asList(accomTypeSupplementSeason));
        assertFalse(pricingConfigurationOffsetsPresenter.isWrapperValidForSupplementRestriction(startDate, endDate, test));
    }

    @Test
    public void testIsWrapperValidForSupplementRestriction_NullDefault_WrapperNegative() {
        LocalDate startDate = LocalDate.now();
        LocalDate endDate = startDate.plusDays(10);

        AccomType accomType = new AccomType();
        accomType.setId(8);
        PricingConfigurationOffsetWrapper test = createPricingConfigurationOffsetWrapperForTaxInclusive(-1, -1, -1, -1, -1, -1, -1,
                createDummyPricingAccomClass("Test"),
                accomType,
                PricingConfigurationNodeLevel.ROOM_CLASS,
                new ArrayList<>());

        AccomTypeSupplement accomTypeSupplementSeason = createAccomTypeSupplement(8, 8, 8, 8, 8, 8, 8);
        accomTypeSupplementSeason.setAccomType(accomType);
        accomTypeSupplementSeason.setStartDate(startDate);
        accomTypeSupplementSeason.setEndDate(endDate);

        pricingConfigurationOffsetsPresenter.setFixedValueAccomTypeSupplements(new ArrayList<>());

        assertFalse(pricingConfigurationOffsetsPresenter.isWrapperValidForSupplementRestriction(startDate, endDate, test));

        pricingConfigurationOffsetsPresenter.setFixedValueAccomTypeSupplements(Arrays.asList(accomTypeSupplementSeason));
        assertFalse(pricingConfigurationOffsetsPresenter.isWrapperValidForSupplementRestriction(startDate, endDate, test));
    }

    @Test
    public void testIsWrapperValidForSupplementRestriction_NullDefault_WrapperZero() {
        LocalDate startDate = LocalDate.now();
        LocalDate endDate = startDate.plusDays(10);

        AccomType accomType = new AccomType();
        accomType.setId(8);
        PricingConfigurationOffsetWrapper test = createPricingConfigurationOffsetWrapperForTaxInclusive(0, 0, 0, 0, 0, 0, 0,
                createDummyPricingAccomClass("Test"),
                accomType,
                PricingConfigurationNodeLevel.ROOM_CLASS,
                new ArrayList<>());

        AccomTypeSupplement accomTypeSupplementSeason = createAccomTypeSupplement(8, 8, 8, 8, 8, 8, 8);
        accomTypeSupplementSeason.setAccomType(accomType);
        accomTypeSupplementSeason.setStartDate(startDate);
        accomTypeSupplementSeason.setEndDate(endDate);

        pricingConfigurationOffsetsPresenter.setFixedValueAccomTypeSupplements(new ArrayList<>());

        assertFalse(pricingConfigurationOffsetsPresenter.isWrapperValidForSupplementRestriction(startDate, endDate, test));

        pricingConfigurationOffsetsPresenter.setFixedValueAccomTypeSupplements(Arrays.asList(accomTypeSupplementSeason));
        assertFalse(pricingConfigurationOffsetsPresenter.isWrapperValidForSupplementRestriction(startDate, endDate, test));
    }

    @Test
    public void testIsSeasonValidComparingSupplementsForPriceExcludedValues() {
        LocalDate startDate = LocalDate.now();
        LocalDate endDate = startDate.plusDays(10);

        AccomType accomType = new AccomType();
        accomType.setId(8);
        PricingAccomClass pricingAccomClass = createDummyPricingAccomClass("Test");
        pricingAccomClass.setPriceExcluded(true);
        PricingConfigurationOffsetWrapper test = createPricingConfigurationOffsetWrapperForTaxInclusive(9, 9, 9, 9, 9, 9, 9,
                pricingAccomClass,
                accomType,
                PricingConfigurationNodeLevel.ROOM_CLASS,
                new ArrayList<>());
        PricingConfigurationOffsetWrapper childWrapper = createPricingConfigurationOffsetWrapperForTaxInclusive(9, 9, 9, 9, 9, 9, 9,
                pricingAccomClass,
                accomType,
                PricingConfigurationNodeLevel.ROOM_CLASS,
                new ArrayList<>());
        test.children = Arrays.asList(childWrapper);

        AccomTypeSupplement accomTypeSupplementDefault = createAccomTypeSupplement(5, 5, 5, 5, 5, 5, 5);
        accomTypeSupplementDefault.setAccomType(accomType);
        AccomTypeSupplement accomTypeSupplementSeason = createAccomTypeSupplement(8, 8, 8, 8, 8, 8, 8);
        accomTypeSupplementSeason.setAccomType(accomType);
        accomTypeSupplementSeason.setStartDate(startDate);
        accomTypeSupplementSeason.setEndDate(endDate);
        pricingConfigurationOffsetsPresenter.setFixedValueAccomTypeSupplements(Arrays.asList(accomTypeSupplementDefault, accomTypeSupplementSeason));

        PricingConfigurationOffsetSeasonWrapper pricingConfigurationOffsetSeasonWrapper = new PricingConfigurationOffsetSeasonWrapper();
        pricingConfigurationOffsetSeasonWrapper.setStartDate(startDate);
        pricingConfigurationOffsetSeasonWrapper.setEndDate(endDate);
        pricingConfigurationOffsetSeasonWrapper.setOffsets(Arrays.asList(test));

        assertTrue(pricingConfigurationOffsetsPresenter.isSeasonValidComparingSupplementsForPriceExcludedValues(pricingConfigurationOffsetSeasonWrapper));
    }

    private PricingConfigurationOffsetWrapper createPricingConfigurationOffsetWrapperForTaxInclusive(
            int sundayValue,
            int mondayValue,
            int tuesdayValue,
            int wednesdayValue,
            int thursdayValue,
            int fridayValue,
            int saturdayValue,
            PricingAccomClass accomClass,
            AccomType accomType,
            PricingConfigurationNodeLevel nodeLevel,
            List<AccomTypeSupplement> accomTypeSupplementList) {

        CPConfigOffsetAccomType cpConfigOffsetAccomType = new CPConfigOffsetAccomType();
        cpConfigOffsetAccomType.setSundayOffsetValueWithTax(BigDecimal.valueOf(sundayValue));
        cpConfigOffsetAccomType.setMondayOffsetValueWithTax(BigDecimal.valueOf(mondayValue));
        cpConfigOffsetAccomType.setTuesdayOffsetValueWithTax(BigDecimal.valueOf(tuesdayValue));
        cpConfigOffsetAccomType.setWednesdayOffsetValueWithTax(BigDecimal.valueOf(wednesdayValue));
        cpConfigOffsetAccomType.setThursdayOffsetValueWithTax(BigDecimal.valueOf(thursdayValue));
        cpConfigOffsetAccomType.setFridayOffsetValueWithTax(BigDecimal.valueOf(fridayValue));
        cpConfigOffsetAccomType.setSaturdayOffsetValueWithTax(BigDecimal.valueOf(saturdayValue));
        cpConfigOffsetAccomType.setAccomType(accomType);
        PricingConfigurationOffsetWrapper wrapper = new PricingConfigurationOffsetWrapper(accomClass, accomType, nodeLevel, accomTypeSupplementList);
        wrapper.setCpConfigOffsetAccomType(cpConfigOffsetAccomType);
        return wrapper;
    }

    private AccomTypeSupplement createAccomTypeSupplement(
            int sundayValue,
            int mondayValue,
            int tuesdayValue,
            int wednesdayValue,
            int thursdayValue,
            int fridayValue,
            int saturdayValue) {
        AccomTypeSupplement accomTypeSupplement = new AccomTypeSupplement();
        accomTypeSupplement.setSundaySupplementValue(BigDecimal.valueOf(sundayValue));
        accomTypeSupplement.setMondaySupplementValue(BigDecimal.valueOf(mondayValue));
        accomTypeSupplement.setTuesdaySupplementValue(BigDecimal.valueOf(tuesdayValue));
        accomTypeSupplement.setWednesdaySupplementValue(BigDecimal.valueOf(wednesdayValue));
        accomTypeSupplement.setThursdaySupplementValue(BigDecimal.valueOf(thursdayValue));
        accomTypeSupplement.setFridaySupplementValue(BigDecimal.valueOf(fridayValue));
        accomTypeSupplement.setSaturdaySupplementValue(BigDecimal.valueOf(saturdayValue));
        return accomTypeSupplement;
    }

    private PricingAccomClass createDummyPricingAccomClass(String accomClassName) {
        PricingAccomClass pricingAccomClass = new PricingAccomClass();
        AccomClass accomClass = new AccomClass();
        accomClass.setName(accomClassName);
        pricingAccomClass.setAccomClass(accomClass);
        return pricingAccomClass;
    }

    private List<PricingConfigurationOffsetSeasonWrapper> createSeasonWrapperList() {
        PricingConfigurationOffsetSeasonWrapper pastSeason = new PricingConfigurationOffsetSeasonWrapper();
        pastSeason.setStartDate(localDate.minusDays(30));
        pastSeason.setEndDate(localDate.minusDays(10));
        pastSeason.setName(PAST_SEASON);
        pastSeason.setOffsets(new ArrayList<>());
        PricingConfigurationOffsetSeasonWrapper presentSeason = new PricingConfigurationOffsetSeasonWrapper();
        presentSeason.setStartDate(localDate.minusDays(1));
        presentSeason.setEndDate(localDate.plusDays(10));
        presentSeason.setName(PRESENT_SEASON);
        presentSeason.setOffsets(new ArrayList<>());
        PricingConfigurationOffsetSeasonWrapper futureSeason = new PricingConfigurationOffsetSeasonWrapper();
        futureSeason.setStartDate(localDate.plusDays(10));
        futureSeason.setEndDate(localDate.plusDays(30));
        futureSeason.setName(FUTURE_SEASON);
        futureSeason.setOffsets(new ArrayList<>());

        List<PricingConfigurationOffsetSeasonWrapper> seasonsList = new ArrayList<>();
        seasonsList.add(pastSeason);
        seasonsList.add(presentSeason);
        seasonsList.add(futureSeason);
        return seasonsList;
    }

    private PricingConfigurationOffsetSeasonWrapper createSeasonWrapper(LocalDate startDate, LocalDate endDate, String seasonName) {
        PricingConfigurationOffsetSeasonWrapper seasonWrapper = new PricingConfigurationOffsetSeasonWrapper();
        seasonWrapper.setStartDate(startDate);
        seasonWrapper.setEndDate(endDate);
        seasonWrapper.setName(seasonName);
        seasonWrapper.setOffsets(new ArrayList<>());
        return seasonWrapper;
    }

    @Test
    public void validateSeasonForMultipleTaxesWhenSeasonDoNotSpanThroughMultipleTaxes() {
        LocalDate startDate = LocalDate.now();
        LocalDate endDate = startDate.plusDays(10);
        PricingConfigurationOffsetSeasonWrapper seasonToBeAdded = createSeasonWrapper(startDate, endDate, "dummy");
        Tax tax = new Tax();
        tax.setStartDate(startDate.plusDays(30));
        tax.setEndDate(startDate.plusDays(45));
        when(taxService.getPartialOverlappingTaxSeasons(startDate, endDate)).thenReturn(Collections.emptyList());
        when(lang.getText(anyString(), anyString())).thenReturn("some message");

        boolean isSeasonValid = pricingConfigurationOffsetsPresenter.validateSeasonForMultipleTaxes(seasonToBeAdded);

        assertTrue(isSeasonValid);
    }

    @Test
    public void getPermissionForSelectedPage() {
        pricingConfigurationOffsetsPresenter.onViewOpened(createPricingConfigurationDTOWithPrimaryPricedProduct(null));
        assertEquals(TetrisPermissionKey.PRICING_CONFIGURATION_TRANSIENT_CEIL_AND_FLOOR, pricingConfigurationOffsetsPresenter.getPermissionForPage());

        pricingConfigurationOffsetsPresenter.onViewOpened(createPricingConfigurationDTOWithPrimaryPricedProduct(true));
        assertEquals(TetrisPermissionKey.FUNCTION_GROUP_PRICING_CONFIG_RATE, pricingConfigurationOffsetsPresenter.getPermissionForPage());

        pricingConfigurationOffsetsPresenter.onViewOpened(createPricingConfigurationDTOWithPrimaryPricedProduct(false));
        assertEquals(TetrisPermissionKey.FUNCTION_SPACE_CONFIGURATION_RATE, pricingConfigurationOffsetsPresenter.getPermissionForPage());
    }

    @Test
    public void validateSeasonForMultipleTaxesWhenSeasonSpansThroughMultipleTaxes() {
        LocalDate startDate = LocalDate.now();
        LocalDate endDate = startDate.plusDays(10);
        PricingConfigurationOffsetSeasonWrapper seasonToBeAdded = createSeasonWrapper(startDate, endDate, "dummy");
        Tax tax = new Tax();
        tax.setStartDate(startDate);
        tax.setEndDate(startDate.plusDays(5));
        when(taxService.getPartialOverlappingTaxSeasons(startDate, endDate)).thenReturn(Arrays.asList(tax));
        when(lang.getText(anyString(), anyString())).thenReturn("some message");

        boolean isSeasonValid = pricingConfigurationOffsetsPresenter.validateSeasonForMultipleTaxes(seasonToBeAdded);

        assertFalse(isSeasonValid);
    }

    @Test
    public void testIsDateBeforeSystemDate() {
        LocalDate today = LocalDate.now();
        pricingConfigurationOffsetsPresenter.systemDateAsLocalDate = today;
        assertFalse(pricingConfigurationOffsetsPresenter.isDateBeforeSystemDate(today));
        assertFalse(pricingConfigurationOffsetsPresenter.isDateBeforeSystemDate(today.plusDays(1)));
        assertTrue(pricingConfigurationOffsetsPresenter.isDateBeforeSystemDate(today.minusDays(1)));
    }

    @Test
    public void testGetDefaultSeasonPopulationWrapper() {
        CPOffsetConfigDTO offsetConfigDTO = getOffsetConfigDTO();
        CPOffsetConfigPeriodDTO defaultOffsets = new CPOffsetConfigPeriodDTO();
        CPConfigOffsetAccomType offset = new CPConfigOffsetAccomType();
        AccomClass accomClass = new AccomClass();
        AccomType accomType = new AccomType();
        PricingAccomClass pricingAccomClass = new PricingAccomClass();

        accomType.setAccomClass(accomClass);
        offset.setAccomType(accomType);
        offset.setOccupancyType(OccupancyType.SINGLE);

        pricingAccomClass.setPriceExcluded(false);
        pricingAccomClass.setAccomClass(accomClass);

        defaultOffsets.addOffset(offset, Arrays.asList(pricingAccomClass));
        offsetConfigDTO.setDefaultOffsets(defaultOffsets);

        PricingConfigurationDTO dto = createPricingConfigurationDTOWithPrimaryPricedProduct(false);
        pricingConfigurationOffsetsPresenter.setPricingConfigurationDTO(dto);

        when(offsetService.retrieveOffsetConfigDTO(new ArrayList<>(), OccupancyType.SINGLE, new ArrayList<>(), null)).thenReturn(offsetConfigDTO);
        pricingConfigurationOffsetsPresenter.onViewOpened(createPricingConfigurationDTOWithPrimaryPricedProduct(false));

        PricingConfigurationOffsetSeasonWrapper resultWrapper = pricingConfigurationOffsetsPresenter.getDefaultSeasonPopulationWrapper();

        assertNotNull(resultWrapper.getOffsets());
        assertTrue(resultWrapper.getOffsets().get(0)
                .getChildren().get(0)
                .getCpConfigOffsetAccomType().getAccomType().equals(accomType));
    }

    @Test
    public void testOnViewOpenedWithIndependentProduct() {
        pricingConfigurationOffsetsPresenter.setSeasons(new ArrayList<>());
        pricingConfigurationOffsetsPresenter.setPricingAccomClasses(new ArrayList<>());
        pricingConfigurationOffsetsPresenter.setCpConfigOffsetAccomTypes(new ArrayList<>());

        PricingConfigurationDTO dto = createPricingConfigurationDTOWithIndependentProduct();
        pricingConfigurationOffsetsPresenter.setIndependentProductsEnabled(true);

        when(pricingConfigurationService.getRoomTypesForProduct(dto.getProduct())).thenReturn(new ArrayList<>());
        when(offsetService.retrieveOffsetConfigDTO(any(), any(), any(), any())).thenReturn(new CPOffsetConfigDTO());

        pricingConfigurationOffsetsPresenter.onViewOpened(dto);

        verify(pricingConfigurationService).getPricingAccomClassesForProduct(dto.getProduct());
    }

    @Test
    public void testDefaultOffsetExcelUploadValidations() throws IOException {

        Integer productId = 1;
        OccupancyType baseOccupancyType = OccupancyType.DOUBLE;
        Boolean isPerPersonPricingEnabled = true;
        Set<OccupancyType> occupancyTypesSet = new HashSet<>();
        Map<Integer, Set<OccupancyType>> accomTypeOccupancyTypeAdultsMap = new LinkedHashMap<>();
        Map<Integer, Set<OccupancyType>> accomTypeOccupancyTypeChildrenMap = new LinkedHashMap<>();
        Set<OccupancyType> occupancyTypeSetAdults = new HashSet<>();
        Set<OccupancyType> occupancyTypeSetChildren = new HashSet<>();
        List<PricingAccomClass> pricingAccomClassList = new ArrayList<>();
        List<AccomType> accomTypeList = new ArrayList<>();
        PricingAccomClass pricingAccomClass = new PricingAccomClass();
        AccomType accomType1 = new AccomType();
        AccomType accomType2 = new AccomType();
        AccomType accomType3 = new AccomType();
        Set<AccomType> accomTypeSet = new HashSet<>();
        AccomClass accomClass1 = new AccomClass();
        List<String> baseAccomTypeNames = new ArrayList<>();
        List<OccupantBucketEntity> occupantBucketEntities = new ArrayList<>();
        LocalDate caughtDate = null;
        Map<Integer, Set<OccupancyType>> accomTypeOccupancyTypeSeasonMap = new LinkedHashMap<>();
        Set<AccomType> accomTypeSetByProduct = new HashSet<>();
        Boolean isChildAgeBucketsEnabled = false;
        LinkedHashMap<PricingAccomClass, LinkedHashMap<AccomType, LinkedList<CPConfigOffsetAccomType>>> excelOffsets = new LinkedHashMap<>();
        List<AccomTypeSupplement> baseSupplements = new ArrayList<>();
        accomTypeOccupancyTypeAdultsMap.clear();
        accomTypeOccupancyTypeChildrenMap.clear();
        occupancyTypesSet.clear();
        occupancyTypeSetAdults.clear();
        occupancyTypeSetChildren.clear();

        populateDataForExcelUpload(occupancyTypesSet, accomTypeOccupancyTypeAdultsMap, accomTypeOccupancyTypeChildrenMap,
                occupancyTypeSetAdults, occupancyTypeSetChildren, pricingAccomClassList, accomTypeList, pricingAccomClass,
                accomType1, accomType2, accomType3, accomTypeSet, accomClass1, baseAccomTypeNames, occupantBucketEntities,
                caughtDate, accomTypeOccupancyTypeSeasonMap, accomTypeSetByProduct);

        excelOffsetUploadValidator = new ExcelOffsetUploadValidator(excelOffsets, offsetService, productId, isPerPersonPricingEnabled,
                occupantBucketEntities, accomTypeOccupancyTypeAdultsMap, accomTypeOccupancyTypeChildrenMap, isChildAgeBucketsEnabled,
                accomTypeSetByProduct, baseSupplements);

        String fileName = "src/test/resources/pricingConfigurationOffsets/Pricing_Configuration_Offsets_Validations.xlsx";
        Workbook workbook1 = createWorkbook(fileName);

        when(perPersonPricingService.getAdultBucketType(occupantBucketEntities)).thenReturn(OccupancyType.THREE_ADULTS);
        when(pacmanConfigParamsService.getBooleanParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED)).thenReturn(true);
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.PER_PERSON_PRICING_ENABLED)).thenReturn(true);
        pricingConfigurationOffsetsPresenter.setPricingConfigurationDTO(createPricingConfigurationDTOWithPrimaryPricedProduct(null));
        pricingConfigurationOffsetsPresenter.setupParameters();
        pricingConfigurationOffsetsPresenter.loadData();

        List<OffsetUploadValidationError> offsetUploadValidationErrorList = excelOffsetUploadValidator.offsetDefaultExcelUploadValidation(workbook1, baseOccupancyType,
                baseAccomTypeNames, false);
        List<PricingConfigurationOffsetWrapper> offsetWrappersDefault = pricingConfigurationOffsetsPresenter.getTreeStructure(true, excelOffsets);
        List<CPConfigOffsetAccomType> offsetsToSave = pricingConfigurationOffsetsPresenter.findOffsetsToSave(offsetWrappersDefault).
                stream().filter(offsetAccomType -> !pricingConfigurationOffsetsPresenter.isBaseOccupancyType(offsetAccomType)).collect(Collectors.toList());

        for (CPConfigOffsetAccomType cpConfigOffsetAccomType : offsetsToSave) {

            cpConfigOffsetAccomType.setSundayOffsetValue(new BigDecimal(0));
            cpConfigOffsetAccomType.setMondayOffsetValue(new BigDecimal(0));
            cpConfigOffsetAccomType.setTuesdayOffsetValue(new BigDecimal(0));
            cpConfigOffsetAccomType.setWednesdayOffsetValue(new BigDecimal(0));
            cpConfigOffsetAccomType.setThursdayOffsetValue(new BigDecimal(0));
            cpConfigOffsetAccomType.setFridayOffsetValue(new BigDecimal(0));
            cpConfigOffsetAccomType.setSaturdayOffsetValue(new BigDecimal(0));

            if (cpConfigOffsetAccomType.getAccomType().equals(accomType1) && cpConfigOffsetAccomType.getOccupancyType().equals(OccupancyType.SINGLE)) {
                cpConfigOffsetAccomType.setSundayOffsetValue(new BigDecimal(0.91743));
            } else if (cpConfigOffsetAccomType.getAccomType().equals(accomType2) && cpConfigOffsetAccomType.getOccupancyType().equals(OccupancyType.DOUBLE)) {
                cpConfigOffsetAccomType.setMondayOffsetValue(new BigDecimal(2.75229));
            } else if (cpConfigOffsetAccomType.getAccomType().equals(accomType2) && cpConfigOffsetAccomType.getOccupancyType().equals(OccupancyType.THREE_ADULTS)) {
                cpConfigOffsetAccomType.setMondayOffsetValue(new BigDecimal(1.83486));
            } else if (cpConfigOffsetAccomType.getAccomType().equals(accomType3) && cpConfigOffsetAccomType.getOccupancyType().equals(OccupancyType.CHILD_BUCKET_1)) {
                cpConfigOffsetAccomType.setSaturdayOffsetValue(new BigDecimal(-3));
            }
        }
        List<OffsetUploadValidationError> offsetUploadValidationErrors = pricingConfigurationOffsetsPresenter.validateDefaultOffsets(offsetsToSave, false, offsetUploadValidationErrorList);
        assertEquals(3, offsetUploadValidationErrors.size());

        assertEquals("The one Adult occupancy in STD is set higher than higher occupancies", offsetUploadValidationErrors.get(0).getError());
        assertEquals("The lower Adult occupancies in STD, STF are set higher than higher occupancies", offsetUploadValidationErrors.get(1).getError());
        assertEquals("Child Offset cannot be negative in STT", offsetUploadValidationErrors.get(2).getError());
    }

    @Test
    public void testSeasonOffsetExcelUploadValidations() throws IOException {

        Integer productId = 1;
        OccupancyType baseOccupancyType = OccupancyType.DOUBLE;
        Boolean isPerPersonPricingEnabled = true;
        Set<OccupancyType> occupancyTypesSet = new HashSet<>();
        Map<Integer, Set<OccupancyType>> accomTypeOccupancyTypeAdultsMap = new LinkedHashMap<>();
        Map<Integer, Set<OccupancyType>> accomTypeOccupancyTypeChildrenMap = new LinkedHashMap<>();
        Set<OccupancyType> occupancyTypeSetAdults = new HashSet<>();
        Set<OccupancyType> occupancyTypeSetChildren = new HashSet<>();
        List<PricingAccomClass> pricingAccomClassList = new ArrayList<>();
        List<AccomType> accomTypeList = new ArrayList<>();
        PricingAccomClass pricingAccomClass = new PricingAccomClass();
        AccomType accomType1 = new AccomType();
        AccomType accomType2 = new AccomType();
        AccomType accomType3 = new AccomType();
        Set<AccomType> accomTypeSet = new HashSet<>();
        AccomClass accomClass1 = new AccomClass();
        List<String> baseAccomTypeNames = new ArrayList<>();
        List<OccupantBucketEntity> occupantBucketEntities = new ArrayList<>();
        LocalDate caughtDate = null;
        Map<Integer, Set<OccupancyType>> accomTypeOccupancyTypeSeasonMap = new LinkedHashMap<>();
        List<PricingConfigurationOffsetSeasonWrapper> seasonsList = new ArrayList<>();
        Set<AccomType> accomTypeSetByProduct = new HashSet<>();
        Boolean isChildAgeBucketsEnabled = false;
        LinkedHashMap<PricingAccomClass, LinkedHashMap<AccomType, LinkedList<CPConfigOffsetAccomType>>> excelOffsets = new LinkedHashMap<>();
        LinkedHashMap<String, LinkedHashMap<PricingAccomClass, LinkedHashMap<AccomType, LinkedList<CPConfigOffsetAccomType>>>>
                excelSeasonOffsets = new LinkedHashMap<>();
        Map<String, LocalDate> nameStartDateMap = new LinkedHashMap<>();
        Map<String, LocalDate> nameEndDateMap = new LinkedHashMap<>();
        String DD_MMM_YYYY = "dd-MMM-yyyy";
        DateTimeFormatter dateTimeFormatter = DateTimeFormat.forPattern(DD_MMM_YYYY);
        List<CPConfigOffsetAccomType> seasonOffsetsList = new ArrayList<>();
        List<AccomTypeSupplement> baseSupplements = new ArrayList<>();

        String dateString = "10-Oct-2017";
        caughtDate = LocalDate.parse(dateString, dateTimeFormatter);

        when(dateService.getCaughtUpLocalDate()).thenReturn(caughtDate);
        accomTypeOccupancyTypeAdultsMap.clear();
        accomTypeOccupancyTypeChildrenMap.clear();
        occupancyTypesSet.clear();
        occupancyTypeSetAdults.clear();
        occupancyTypeSetChildren.clear();

        populateDataForExcelUpload(occupancyTypesSet, accomTypeOccupancyTypeAdultsMap, accomTypeOccupancyTypeChildrenMap,
                occupancyTypeSetAdults, occupancyTypeSetChildren, pricingAccomClassList, accomTypeList, pricingAccomClass,
                accomType1, accomType2, accomType3, accomTypeSet, accomClass1, baseAccomTypeNames, occupantBucketEntities,
                caughtDate, accomTypeOccupancyTypeSeasonMap, accomTypeSetByProduct);

        excelOffsetUploadValidator = new ExcelOffsetUploadValidator(excelOffsets, offsetService, productId, isPerPersonPricingEnabled,
                occupantBucketEntities, accomTypeOccupancyTypeAdultsMap, accomTypeOccupancyTypeChildrenMap, isChildAgeBucketsEnabled,
                accomTypeSetByProduct, baseSupplements);

        String fileName = "src/test/resources/pricingConfigurationOffsets/Pricing_Configuration_Offsets_Seasons_Validations.xlsx";
        Workbook workbook1 = createWorkbook(fileName);

        when(perPersonPricingService.getAdultBucketType(occupantBucketEntities)).thenReturn(OccupancyType.THREE_ADULTS);
        when(pacmanConfigParamsService.getBooleanParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED)).thenReturn(true);
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.PER_PERSON_PRICING_ENABLED)).thenReturn(true);
        pricingConfigurationOffsetsPresenter.setPricingConfigurationDTO(createPricingConfigurationDTOWithPrimaryPricedProduct(null));
        pricingConfigurationOffsetsPresenter.setupParameters();
        pricingConfigurationOffsetsPresenter.loadData();

        List<OffsetUploadValidationError> offsetUploadValidationErrorList = excelOffsetUploadValidator.offsetSeasonUploadValidation(workbook1, baseOccupancyType,
                baseAccomTypeNames, excelSeasonOffsets, nameStartDateMap, nameEndDateMap, accomTypeOccupancyTypeSeasonMap, caughtDate, seasonOffsetsList, false);

        LinkedHashMap<PricingAccomClass, LinkedHashMap<AccomType, LinkedList<CPConfigOffsetAccomType>>> innerSeasonsMap = excelSeasonOffsets.get("Season2");
        LocalDate startDate = null;
        LocalDate endDate = null;
        for (Map.Entry<String, LinkedHashMap<PricingAccomClass, LinkedHashMap<AccomType,
                LinkedList<CPConfigOffsetAccomType>>>> entry : excelSeasonOffsets.entrySet()) {

            innerSeasonsMap = entry.getValue();
            startDate = nameStartDateMap.get(entry.getKey());
            endDate = nameEndDateMap.get(entry.getKey());
            List<PricingConfigurationOffsetWrapper> seasonOffsetWrappers = pricingConfigurationOffsetsPresenter.getTreeStructure(true, innerSeasonsMap);
            PricingConfigurationOffsetSeasonWrapper seasonWrapper = new PricingConfigurationOffsetSeasonWrapper();
            seasonWrapper.setOffsets(seasonOffsetWrappers);
            seasonWrapper.retrieveNonBaseRoomTypeOffset(innerSeasonsMap);
            seasonWrapper.setName(entry.getKey());
            seasonWrapper.setStartDate(startDate);
            seasonWrapper.setEndDate(endDate);
            seasonsList.add(seasonWrapper);
        }

        List<PricingConfigurationOffsetWrapper> offsetWrappersDefault = pricingConfigurationOffsetsPresenter.getTreeStructure(true, innerSeasonsMap);
        List<CPConfigOffsetAccomType> offsetsToSave = pricingConfigurationOffsetsPresenter.findOffsetsToSave(offsetWrappersDefault).
                stream().filter(offsetAccomType -> !pricingConfigurationOffsetsPresenter.isBaseOccupancyType(offsetAccomType)).collect(Collectors.toList());

        for (CPConfigOffsetAccomType cpConfigOffsetAccomType : offsetsToSave) {

            cpConfigOffsetAccomType.setId(1);
            cpConfigOffsetAccomType.setSundayOffsetValue(new BigDecimal(0));
            cpConfigOffsetAccomType.setMondayOffsetValue(new BigDecimal(0));
            cpConfigOffsetAccomType.setTuesdayOffsetValue(new BigDecimal(0));
            cpConfigOffsetAccomType.setWednesdayOffsetValue(new BigDecimal(0));
            cpConfigOffsetAccomType.setThursdayOffsetValue(new BigDecimal(0));
            cpConfigOffsetAccomType.setFridayOffsetValue(new BigDecimal(0));
            cpConfigOffsetAccomType.setSaturdayOffsetValue(new BigDecimal(0));

            if (cpConfigOffsetAccomType.getAccomType().equals(accomType1) && cpConfigOffsetAccomType.getOccupancyType().equals(OccupancyType.SINGLE)) {
                cpConfigOffsetAccomType.setSundayOffsetValue(new BigDecimal(0.91743));
            } else if (cpConfigOffsetAccomType.getAccomType().equals(accomType2) && cpConfigOffsetAccomType.getOccupancyType().equals(OccupancyType.DOUBLE)) {
                cpConfigOffsetAccomType.setMondayOffsetValue(new BigDecimal(2.75229));
            } else if (cpConfigOffsetAccomType.getAccomType().equals(accomType2) && cpConfigOffsetAccomType.getOccupancyType().equals(OccupancyType.THREE_ADULTS)) {
                cpConfigOffsetAccomType.setMondayOffsetValue(new BigDecimal(1.83486));
            } else if (cpConfigOffsetAccomType.getAccomType().equals(accomType3) && cpConfigOffsetAccomType.getOccupancyType().equals(OccupancyType.CHILD_BUCKET_1)) {
                cpConfigOffsetAccomType.setSaturdayOffsetValue(new BigDecimal(-3));
            }
        }

        List<PricingBaseAccomType> ceilingAndfloorConfigSeasons = null;
        Tax defaultTax = null;
        CPConfigOffsetAccomType newOffset = new CPConfigOffsetAccomType();
        List<CPConfigOffsetAccomType> existingOffsets = new ArrayList<>();
        existingOffsets.add(newOffset);

        List<OffsetUploadValidationError> offsetUploadValidationErrors = pricingConfigurationOffsetsPresenter.validateSeasonsOffsets(offsetsToSave, true,
                offsetUploadValidationErrorList, "Season2", startDate, endDate, ceilingAndfloorConfigSeasons, defaultTax, existingOffsets);

        assertEquals(3, offsetUploadValidationErrors.size());

        assertEquals("The one Adult occupancy in STD is set higher than higher occupancies", offsetUploadValidationErrors.get(0).getError());
        assertEquals("The lower Adult occupancies in STD, STF are set higher than higher occupancies", offsetUploadValidationErrors.get(1).getError());
        assertEquals("Child Offset cannot be negative in STT", offsetUploadValidationErrors.get(2).getError());

        assertEquals("Season2", offsetUploadValidationErrors.get(0).getSeasonName());
        assertEquals("Season2", offsetUploadValidationErrors.get(1).getSeasonName());
        assertEquals("Season2", offsetUploadValidationErrors.get(2).getSeasonName());
    }

    @Test
    public void testSeasonConfiguration () throws IOException {

        Integer productId = 1;
        OccupancyType baseOccupancyType = OccupancyType.DOUBLE;
        Boolean isPerPersonPricingEnabled = true;
        Set<OccupancyType> occupancyTypesSet = new HashSet<>();
        Map<Integer, Set<OccupancyType>> accomTypeOccupancyTypeAdultsMap = new LinkedHashMap<>();
        Map<Integer, Set<OccupancyType>> accomTypeOccupancyTypeChildrenMap = new LinkedHashMap<>();
        Set<OccupancyType> occupancyTypeSetAdults = new HashSet<>();
        Set<OccupancyType> occupancyTypeSetChildren = new HashSet<>();
        List<PricingAccomClass> pricingAccomClassList = new ArrayList<>();
        List<AccomType> accomTypeList = new ArrayList<>();
        PricingAccomClass pricingAccomClass = new PricingAccomClass();
        AccomType accomType1 = new AccomType();
        AccomType accomType2 = new AccomType();
        AccomType accomType3 = new AccomType();
        Set<AccomType> accomTypeSet = new HashSet<>();
        AccomClass accomClass1 = new AccomClass();
        List<String> baseAccomTypeNames = new ArrayList<>();
        List<OccupantBucketEntity> occupantBucketEntities = new ArrayList<>();
        LocalDate caughtDate = null;
        Map<Integer, Set<OccupancyType>> accomTypeOccupancyTypeSeasonMap = new LinkedHashMap<>();
        List<PricingConfigurationOffsetSeasonWrapper> seasonsList = new ArrayList<>();
        Set<AccomType> accomTypeSetByProduct = new HashSet<>();
        Boolean isChildAgeBucketsEnabled = false;
        LinkedHashMap<PricingAccomClass, LinkedHashMap<AccomType, LinkedList<CPConfigOffsetAccomType>>> excelOffsets = new LinkedHashMap<>();
        LinkedHashMap<String, LinkedHashMap<PricingAccomClass, LinkedHashMap<AccomType, LinkedList<CPConfigOffsetAccomType>>>>
                excelSeasonOffsets = new LinkedHashMap<>();
        Map<String, LocalDate> nameStartDateMap = new LinkedHashMap<>();
        Map<String, LocalDate> nameEndDateMap = new LinkedHashMap<>();
        String DD_MMM_YYYY = "dd-MMM-yyyy";
        DateTimeFormatter dateTimeFormatter = DateTimeFormat.forPattern(DD_MMM_YYYY);
        List<CPConfigOffsetAccomType> seasonOffsetsList = new ArrayList<>();
        List<AccomTypeSupplement> baseSupplements = new ArrayList<>();

        String dateString = "10-Oct-2017";
        caughtDate = LocalDate.parse(dateString, dateTimeFormatter);

        when(dateService.getCaughtUpLocalDate()).thenReturn(caughtDate);
        accomTypeOccupancyTypeAdultsMap.clear();
        accomTypeOccupancyTypeChildrenMap.clear();
        occupancyTypesSet.clear();
        occupancyTypeSetAdults.clear();
        occupancyTypeSetChildren.clear();

        populateDataForExcelUpload(occupancyTypesSet, accomTypeOccupancyTypeAdultsMap, accomTypeOccupancyTypeChildrenMap,
                occupancyTypeSetAdults, occupancyTypeSetChildren, pricingAccomClassList, accomTypeList, pricingAccomClass,
                accomType1, accomType2, accomType3, accomTypeSet, accomClass1, baseAccomTypeNames, occupantBucketEntities,
                caughtDate, accomTypeOccupancyTypeSeasonMap, accomTypeSetByProduct);

        excelOffsetUploadValidator = new ExcelOffsetUploadValidator(excelOffsets, offsetService, productId, isPerPersonPricingEnabled,
                occupantBucketEntities, accomTypeOccupancyTypeAdultsMap, accomTypeOccupancyTypeChildrenMap, isChildAgeBucketsEnabled,
                accomTypeSetByProduct, baseSupplements);

        String fileName = "src/test/resources/pricingConfigurationOffsets/Pricing_Configuration_Offsets_Seasons_Configuration.xlsx";
        Workbook workbook1 = createWorkbook(fileName);

        when(perPersonPricingService.getAdultBucketType(occupantBucketEntities)).thenReturn(OccupancyType.THREE_ADULTS);
        when(pacmanConfigParamsService.getBooleanParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED)).thenReturn(true);
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.PER_PERSON_PRICING_ENABLED)).thenReturn(true);
        pricingConfigurationOffsetsPresenter.setPricingConfigurationDTO(createPricingConfigurationDTOWithPrimaryPricedProduct(null));
        pricingConfigurationOffsetsPresenter.setupParameters();
        pricingConfigurationOffsetsPresenter.loadData();

        List<OffsetUploadValidationError> offsetUploadValidationErrorList =  excelOffsetUploadValidator.offsetSeasonUploadValidation(workbook1, baseOccupancyType,
                baseAccomTypeNames, excelSeasonOffsets, nameStartDateMap, nameEndDateMap, accomTypeOccupancyTypeSeasonMap, caughtDate, seasonOffsetsList, false);

        LinkedHashMap<PricingAccomClass, LinkedHashMap<AccomType, LinkedList<CPConfigOffsetAccomType>>> innerSeasonsMap = excelSeasonOffsets.get("Season2");
        LocalDate startDate = null;
        LocalDate endDate = null;
        for (Map.Entry<String, LinkedHashMap<PricingAccomClass, LinkedHashMap<AccomType,
                LinkedList<CPConfigOffsetAccomType>>>> entry : excelSeasonOffsets.entrySet()) {

            innerSeasonsMap = entry.getValue();
            startDate = nameStartDateMap.get(entry.getKey());
            endDate = nameEndDateMap.get(entry.getKey());
            List<PricingConfigurationOffsetWrapper> seasonOffsetWrappers = pricingConfigurationOffsetsPresenter.getTreeStructure(true, innerSeasonsMap);
            PricingConfigurationOffsetSeasonWrapper seasonWrapper = new PricingConfigurationOffsetSeasonWrapper();
            seasonWrapper.setOffsets(seasonOffsetWrappers);
            seasonWrapper.retrieveNonBaseRoomTypeOffset(innerSeasonsMap);
            seasonWrapper.setName(entry.getKey());
            seasonWrapper.setStartDate(startDate);
            seasonWrapper.setEndDate(endDate);
            seasonsList.add(seasonWrapper);
        }

        PricingConfigurationOffsetSeasonWrapper seasonWrapper = seasonsList.get(0);
        List<PricingBaseAccomType> ceilingAndfloorConfigSeasons = null;
        Tax defaultTax = null;
        CPConfigOffsetAccomType newOffset = new CPConfigOffsetAccomType();
        List<CPConfigOffsetAccomType> existingOffsets = new ArrayList<>();
        existingOffsets.add(newOffset);

        when(lang.getText("offset.upload.incomplete.configuration")).thenReturn(UiUtils.getText("offset.upload.incomplete.configuration"));
        pricingConfigurationOffsetsPresenter.saveOffsetExcelSeasons(seasonWrapper, true, offsetUploadValidationErrorList,
                "Season2", startDate, endDate, ceilingAndfloorConfigSeasons, defaultTax, existingOffsets);

        assertEquals(1, offsetUploadValidationErrorList.size());
        assertEquals(getText("offset.upload.incomplete.configuration"), offsetUploadValidationErrorList.get(0).getError());
        assertEquals("Season2", offsetUploadValidationErrorList.get(0).getSeasonName());

    }

    private void populateDataForExcelUpload(Set<OccupancyType> occupancyTypesSet, Map<Integer, Set<OccupancyType>> accomTypeOccupancyTypeAdultsMap, Map<Integer, Set<OccupancyType>> accomTypeOccupancyTypeChildrenMap, Set<OccupancyType> occupancyTypeSetAdults, Set<OccupancyType> occupancyTypeSetChildren, List<PricingAccomClass> pricingAccomClassList, List<AccomType> accomTypeList, PricingAccomClass pricingAccomClass, AccomType accomType1, AccomType accomType2, AccomType accomType3, Set<AccomType> accomTypeSet, AccomClass accomClass1, List<String> baseAccomTypeNames, List<OccupantBucketEntity> occupantBucketEntities, LocalDate caughtDate, Map<Integer, Set<OccupancyType>> accomTypeOccupancyTypeSeasonMap, Set<AccomType> accomTypeSetByProduct) {
        occupancyTypesSet.add(OccupancyType.SINGLE);
        occupancyTypesSet.add(OccupancyType.DOUBLE);
        occupancyTypesSet.add(OccupancyType.EXTRA_ADULT);
        occupancyTypesSet.add(OccupancyType.THREE_ADULTS);
        occupancyTypesSet.add(OccupancyType.EXTRA_CHILD);
        occupancyTypesSet.add(OccupancyType.CHILD_BUCKET_1);
        occupancyTypesSet.add(OccupancyType.CHILD_BUCKET_2);

        accomType1.setName("STD");
        accomType1.setId(5);
        accomType2.setName("STF");
        accomType2.setId(9);
        accomType3.setName("STT");
        accomType3.setId(6);

        accomTypeSet.add(accomType1);
        accomTypeSet.add(accomType2);
        accomTypeSet.add(accomType3);
        accomTypeSetByProduct.add(accomType1);
        accomTypeSetByProduct.add(accomType2);
        accomTypeSetByProduct.add(accomType3);

        accomClass1.setName("STANDARD");
        accomClass1.setAccomTypes(accomTypeSet);

        pricingAccomClass.setAccomClass(accomClass1);
        pricingAccomClass.setAccomType(accomType1);
        pricingAccomClassList.add(pricingAccomClass);
        accomTypeList.add(accomType1);
        accomTypeList.add(accomType2);
        accomTypeList.add(accomType3);

        baseAccomTypeNames.add(accomType1.getName());
        baseAccomTypeNames.add(accomType2.getName());
        baseAccomTypeNames.add(accomType3.getName());

        when(offsetService.getAllPricingAccomClasses()).thenReturn(pricingAccomClassList);
        when(offsetService.getAllAccomTypes()).thenReturn(accomTypeList);

        OccupancyType childBucket1 = OccupancyType.CHILD_BUCKET_1;
        OccupancyType childBucket2 = OccupancyType.CHILD_BUCKET_2;

        OccupantBucketEntity childAgeBucket1 = new OccupantBucketEntity();
        OccupantBucketEntity childAgeBucket2 = new OccupantBucketEntity();

        childAgeBucket1.setOccupancyType(childBucket1);
        childAgeBucket1.setMinAge(1);
        childAgeBucket1.setMaxAge(4);
        childAgeBucket2.setOccupancyType(childBucket2);
        childAgeBucket2.setMinAge(5);
        childAgeBucket2.setMaxAge(7);

        occupantBucketEntities.add(childAgeBucket1);
        occupantBucketEntities.add(childAgeBucket2);

        occupancyTypeSetAdults.add(OccupancyType.SINGLE);
        occupancyTypeSetAdults.add(OccupancyType.DOUBLE);
        occupancyTypeSetAdults.add(OccupancyType.EXTRA_ADULT);
        occupancyTypeSetAdults.add(OccupancyType.EXTRA_CHILD);
        occupancyTypeSetAdults.add(OccupancyType.THREE_ADULTS);

        occupancyTypeSetChildren.clear();
        occupancyTypeSetChildren.add(childBucket1);
        occupancyTypeSetChildren.add(childBucket2);

        accomTypeOccupancyTypeAdultsMap.put(accomType1.getId(), occupancyTypeSetAdults);
        accomTypeOccupancyTypeAdultsMap.put(accomType2.getId(), occupancyTypeSetAdults);
        accomTypeOccupancyTypeAdultsMap.put(accomType3.getId(), occupancyTypeSetAdults);

        accomTypeOccupancyTypeChildrenMap.put(accomType1.getId(), occupancyTypeSetChildren);
        accomTypeOccupancyTypeChildrenMap.put(accomType2.getId(), occupancyTypeSetChildren);
        accomTypeOccupancyTypeChildrenMap.put(accomType3.getId(), occupancyTypeSetChildren);

        when(perPersonPricingService.getOccupantBuckets()).thenReturn(occupantBucketEntities);
        when(dateService.getCaughtUpLocalDate()).thenReturn(caughtDate);

        accomTypeOccupancyTypeSeasonMap.putAll(accomTypeOccupancyTypeAdultsMap);
        for (Map.Entry<Integer, Set<OccupancyType>> entry : accomTypeOccupancyTypeChildrenMap.entrySet()) {
            if (accomTypeOccupancyTypeSeasonMap.containsKey(entry.getKey())) {
                accomTypeOccupancyTypeSeasonMap.get(entry.getKey()).addAll(entry.getValue());
            }
        }
    }

    private Workbook createWorkbook(String fileName) throws IOException {
        FileInputStream fileInputStream = new FileInputStream(fileName);
        return new XSSFWorkbook(fileInputStream);
    }

    public PricingConfigurationDTO createPricingConfigurationDTO(Boolean isGroupPricing) {
        Product product = new Product();
        product.setId(2);
        product.setCode(Product.INDEPENDENT_PRODUCT_CODE);
        product.setSystemDefault(false);
        IndependentProductConfigurationDTO indDto = new IndependentProductConfigurationDTO(product);
        return new PricingConfigurationDTO(indDto, isGroupPricing);
    }

    public PricingConfigurationDTO createPricingConfigurationDTOWithPrimaryPricedProduct(Boolean isGroupPricing) {
        Product product = new Product();
        product.setId(1);
        product.setCode(Product.BAR);
        product.setSystemDefault(true);
        IndependentProductConfigurationDTO indDto = new IndependentProductConfigurationDTO(product);
        return new PricingConfigurationDTO(indDto, isGroupPricing);
    }

    public PricingConfigurationDTO createPricingConfigurationDTOWithIndependentProduct() {
        Product product = new Product();
        product.setId(2);
        product.setCode(Product.INDEPENDENT_PRODUCT_CODE);
        product.setSystemDefault(true);
        IndependentProductConfigurationDTO indDto = new IndependentProductConfigurationDTO(product);
        return new PricingConfigurationDTO(indDto, false);
    }

    private AccomTypeSupplement supplement(OffsetMethod offsetMethod, AccomType accomType) {
        AccomTypeSupplement accomTypeSupplement = new AccomTypeSupplement();
        accomTypeSupplement.setOffsetMethod(offsetMethod);
        accomTypeSupplement.setAccomType(accomType);
        return accomTypeSupplement;
    }
}