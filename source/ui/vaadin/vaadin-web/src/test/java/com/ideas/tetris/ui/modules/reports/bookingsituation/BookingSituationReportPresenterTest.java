package com.ideas.tetris.ui.modules.reports.bookingsituation;

import com.ideas.tetris.pacman.services.businessgroup.service.BusinessGroupService;
import com.ideas.tetris.pacman.services.marketsegment.dto.ForecastGroupSummary;
import com.ideas.tetris.pacman.services.marketsegment.entity.BusinessGroup;
import com.ideas.tetris.pacman.services.marketsegment.service.forecastgroup.service.ForecastGroupFinalService;
import com.ideas.tetris.platform.common.xml.schema.workcontext.v1.WorkContextType;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import com.ideas.tetris.ui.VaadinUIBaseJupiterTest;
import com.ideas.tetris.ui.common.cdi.cdiutils.Lang;
import com.ideas.tetris.ui.common.component.date.customdateselector.JavaDateSelectorBean;
import com.ideas.tetris.ui.common.component.date.customdateselector.LocalizationKeyConstants;
import com.ideas.tetris.ui.common.component.date.customdateselector.RollingDateOptionBean;
import com.ideas.tetris.ui.common.security.UiContext;
import com.ideas.tetris.ui.common.util.DateFormatUtil;
import com.ideas.tetris.ui.common.util.SharedSessionState;
import com.ideas.tetris.ui.common.util.UiUtils;
import com.ideas.tetris.ui.modules.reports.reportscheduler.JasperReportTypeEnum;
import com.ideas.tetris.ui.modules.reports.util.ReportFormatType;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static com.ideas.tetris.ui.common.component.date.customdateselector.LocalizationKeyConstants.END_OF_MONTH_MINUS;
import static com.ideas.tetris.ui.common.component.date.customdateselector.LocalizationKeyConstants.END_OF_MONTH_PLUS;
import static com.ideas.tetris.ui.common.component.date.customdateselector.LocalizationKeyConstants.LAST_UPDATED_DATE;
import static com.ideas.tetris.ui.common.component.date.customdateselector.LocalizationKeyConstants.LAST_UPDATED_DATE_MINUS;
import static com.ideas.tetris.ui.common.component.date.customdateselector.LocalizationKeyConstants.LAST_YEAR_END_OF_MONTH_MINUS;
import static com.ideas.tetris.ui.common.component.date.customdateselector.LocalizationKeyConstants.LAST_YEAR_END_OF_MONTH_PLUS;
import static com.ideas.tetris.ui.common.component.date.customdateselector.LocalizationKeyConstants.LAST_YEAR_LAST_UPDATED_DATE;
import static com.ideas.tetris.ui.common.component.date.customdateselector.LocalizationKeyConstants.LAST_YEAR_LAST_UPDATED_DATE_MINUS;
import static com.ideas.tetris.ui.common.component.date.customdateselector.LocalizationKeyConstants.LAST_YEAR_START_OF_MONTH;
import static com.ideas.tetris.ui.common.component.date.customdateselector.LocalizationKeyConstants.LAST_YEAR_START_OF_MONTH_MINUS;
import static com.ideas.tetris.ui.common.component.date.customdateselector.LocalizationKeyConstants.LAST_YEAR_START_OF_MONTH_PLUS;
import static com.ideas.tetris.ui.common.component.date.customdateselector.LocalizationKeyConstants.LAST_YEAR_THIS_DAY;
import static com.ideas.tetris.ui.common.component.date.customdateselector.LocalizationKeyConstants.LAST_YEAR_THIS_DAY_MINUS;
import static com.ideas.tetris.ui.common.component.date.customdateselector.LocalizationKeyConstants.LAST_YEAR_THIS_DAY_PLUS;
import static com.ideas.tetris.ui.common.component.date.customdateselector.LocalizationKeyConstants.START_OF_MONTH_MINUS;
import static com.ideas.tetris.ui.common.component.date.customdateselector.LocalizationKeyConstants.START_OF_MONTH_PLUS;
import static com.ideas.tetris.ui.common.component.date.customdateselector.LocalizationKeyConstants.SYSTEM_DATE;
import static com.ideas.tetris.ui.common.component.date.customdateselector.LocalizationKeyConstants.SYSTEM_DATE_MINUS;
import static com.ideas.tetris.ui.common.component.date.customdateselector.LocalizationKeyConstants.SYSTEM_DATE_PLUS;
import static com.ideas.tetris.ui.common.util.UiUtils.getText;
import static com.ideas.tetris.ui.modules.reports.bookingsituation.BookingSituationReportPresenter.WARNING;
import static com.ideas.tetris.ui.modules.reports.reportscheduler.JasperReportConstants.BLANK;
import static com.ideas.tetris.ui.modules.reports.reportscheduler.JasperReportConstants.FALSE_VALUE;
import static com.ideas.tetris.ui.modules.reports.reportscheduler.JasperReportConstants.HYPEN;
import static com.ideas.tetris.ui.modules.reports.reportscheduler.JasperReportConstants.IS_IGNORE_PAGINATION;
import static com.ideas.tetris.ui.modules.reports.reportscheduler.JasperReportConstants.IS_PHYSICAL_CAPACITY;
import static com.ideas.tetris.ui.modules.reports.reportscheduler.JasperReportConstants.OUTPUT;
import static com.ideas.tetris.ui.modules.reports.reportscheduler.JasperReportConstants.PARAM_ANALYSISASOFDATE;
import static com.ideas.tetris.ui.modules.reports.reportscheduler.JasperReportConstants.PARAM_ANALYSISENDDATE;
import static com.ideas.tetris.ui.modules.reports.reportscheduler.JasperReportConstants.PARAM_ANALYSISSTARTDATE;
import static com.ideas.tetris.ui.modules.reports.reportscheduler.JasperReportConstants.PARAM_BASE_CURRENCY;
import static com.ideas.tetris.ui.modules.reports.reportscheduler.JasperReportConstants.PARAM_BUSINESSVIEWS;
import static com.ideas.tetris.ui.modules.reports.reportscheduler.JasperReportConstants.PARAM_COMPARISIONASOFDATE;
import static com.ideas.tetris.ui.modules.reports.reportscheduler.JasperReportConstants.PARAM_COMPARISIONENDDATE;
import static com.ideas.tetris.ui.modules.reports.reportscheduler.JasperReportConstants.PARAM_COMPARISIONSTARTDATE;
import static com.ideas.tetris.ui.modules.reports.reportscheduler.JasperReportConstants.PARAM_FORECASTGROUPS;
import static com.ideas.tetris.ui.modules.reports.reportscheduler.JasperReportConstants.PARAM_ISADRCHECKED_GROUP;
import static com.ideas.tetris.ui.modules.reports.reportscheduler.JasperReportConstants.PARAM_ISADRCHECKED_HOTEL;
import static com.ideas.tetris.ui.modules.reports.reportscheduler.JasperReportConstants.PARAM_ISADRCHECKED_TRANS;
import static com.ideas.tetris.ui.modules.reports.reportscheduler.JasperReportConstants.PARAM_ISBVCHECKED;
import static com.ideas.tetris.ui.modules.reports.reportscheduler.JasperReportConstants.PARAM_ISFGCHECKED;
import static com.ideas.tetris.ui.modules.reports.reportscheduler.JasperReportConstants.PARAM_ISGROUPCHECKED;
import static com.ideas.tetris.ui.modules.reports.reportscheduler.JasperReportConstants.PARAM_ISHOTELCHECKED;
import static com.ideas.tetris.ui.modules.reports.reportscheduler.JasperReportConstants.PARAM_ISREVPARCHECKED_HOTEL;
import static com.ideas.tetris.ui.modules.reports.reportscheduler.JasperReportConstants.PARAM_ISROLLING_DATE;
import static com.ideas.tetris.ui.modules.reports.reportscheduler.JasperReportConstants.PARAM_ISROOMREVENUECHECKED_GROUP;
import static com.ideas.tetris.ui.modules.reports.reportscheduler.JasperReportConstants.PARAM_ISROOMREVENUECHECKED_HOTEL;
import static com.ideas.tetris.ui.modules.reports.reportscheduler.JasperReportConstants.PARAM_ISROOMREVENUECHECKED_TRANS;
import static com.ideas.tetris.ui.modules.reports.reportscheduler.JasperReportConstants.PARAM_ISROOMSSOLDCHECKED_GROUP;
import static com.ideas.tetris.ui.modules.reports.reportscheduler.JasperReportConstants.PARAM_ISROOMSSOLDCHECKED_HOTEL;
import static com.ideas.tetris.ui.modules.reports.reportscheduler.JasperReportConstants.PARAM_ISROOMSSOLDCHECKED_TRANS;
import static com.ideas.tetris.ui.modules.reports.reportscheduler.JasperReportConstants.PARAM_ISSPECIALEVENTCHECKED_GROUP;
import static com.ideas.tetris.ui.modules.reports.reportscheduler.JasperReportConstants.PARAM_ISSPECIALEVENTCHECKED_HOTEL;
import static com.ideas.tetris.ui.modules.reports.reportscheduler.JasperReportConstants.PARAM_ISSPECIALEVENTCHECKED_TRANS;
import static com.ideas.tetris.ui.modules.reports.reportscheduler.JasperReportConstants.PARAM_ISTRANSIENTCHECKED;
import static com.ideas.tetris.ui.modules.reports.reportscheduler.JasperReportConstants.PARAM_ROLLINGANALYSISBUSINESSDATE;
import static com.ideas.tetris.ui.modules.reports.reportscheduler.JasperReportConstants.PARAM_ROLLINGANALYSISENDDATE;
import static com.ideas.tetris.ui.modules.reports.reportscheduler.JasperReportConstants.PARAM_ROLLINGANALYSISSTARTDATE;
import static com.ideas.tetris.ui.modules.reports.reportscheduler.JasperReportConstants.PARAM_ROLLINGCOMPARISIONBUSINESSDATE;
import static com.ideas.tetris.ui.modules.reports.reportscheduler.JasperReportConstants.PARAM_ROLLINGCOMPARISIONENDDATE;
import static com.ideas.tetris.ui.modules.reports.reportscheduler.JasperReportConstants.PARAM_ROLLINGCOMPARISIONSTARTDATE;
import static com.ideas.tetris.ui.modules.reports.reportscheduler.JasperReportConstants.TRUE_VALUE;
import static com.ideas.tetris.ui.modules.reports.reportscheduler.JasperReportConstants.USD;
import static com.ideas.tetris.ui.modules.reports.reportscheduler.JasperReportConstants.ZERO_AS_STRING;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyObject;
import static org.mockito.Mockito.anyBoolean;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class BookingSituationReportPresenterTest extends VaadinUIBaseJupiterTest {
    private static final int DAYS_IN_ONE_MONTH = 30;
    private static final int DEFAULT_DAYS_BETWEEN_START_END = 7;
    private static final int WEEKS_IN_YEAR = 52;
    private static final String DEFAULT_DATE_FOR_REPORTS = "19700101";
    public static final String TODAY = "TODAY";
    public static final String LAST_UPDATED = "LAST_UPDATED";
    public static final String LY_THIS_DAY = "LY_THIS_DAY";
    public static final String LY_LAST_UPDATED = "LY_LAST_UPDATED";
    public static final String START_OF_MONTH = "START_OF_MONTH";
    public static final String END_OF_MONTH = "END_OF_MONTH";
    public static final String LY_START_OF_MONTH = "LY_START_OF_MONTH";
    public static final String LY_END_OF_MONTH = "LY_END_OF_MONTH";
    public static final int BDE_DAYS = 365;
    public static final String Report_Type = "booking-situation-report";
    private HashMap<String, String> reportParamMapFromURL;

    @Spy
    @InjectMocks
    private BookingSituationReportPresenter presenter;
    @Mock
    private PacmanConfigParamsService configParamsService;
    @Mock
    private UiContext uiContext;
    @Mock
    private SharedSessionState sharedSessionState;
    @Mock
    private ForecastGroupFinalService forecastGroupComponentService;
    @Mock
    private BusinessGroupService businessGroupService;
    @Mock
    private BookingSituationReportView view;
    @Mock
    private Lang lang;
    private final LocalDate currentDate = LocalDate.now();

    @BeforeEach
    public void setUp() throws Exception {
        when(uiContext.getSystemCaughtUpDateAsJavaLocalDate()).thenReturn(currentDate);
        when(uiContext.getForecastWindowOffsetBDE()).thenReturn(BDE_DAYS);
        when(uiContext.getPropertyId()).thenReturn(5);
        when(view.getSelectedReportType()).thenReturn(ReportFormatType.EXCEL);
        doNothing().when(view).initForm(any(), any(), any(), any());
        when(forecastGroupComponentService.getAllForecastGroupSummariesOrderedByRank()).thenReturn(getForeCastSummaries(4));
        when(businessGroupService.getAllBusinessGroupDetails()).thenReturn(getBusinessViews(4));
        when(lang.getText("comparision.as.of.date")).thenReturn("Comparison as of Date");
        when(lang.getText("analysis.as.of.date")).thenReturn("Analysis as of Date");
        when(lang.getText("analysisStartDateLabel")).thenReturn("Analysis Start Date");
        when(lang.getText("analysis.end.date")).thenReturn("Analysis End Date");
        when(lang.getText("comparision.start.date")).thenReturn("Comparison Start Date");
        when(lang.getText("comparision.end.date")).thenReturn("Comparison End Date");
        when(lang.getText("last.updated.date")).thenReturn("Last updated date ");
        when(lang.getText("last.updated.date.minus")).thenReturn("Last updated date - ");
        when(lang.getText("last.year.this.day.plus")).thenReturn("Last year this day + ");
        when(lang.getText("last.year.this.day")).thenReturn("Last year this day ");
        when(lang.getText("last.year.this.day.minus")).thenReturn("Last year this day - ");
        when(lang.getText("last.year.start.of.month.plus")).thenReturn("Last year start of month + ");
        when(lang.getText("last.year.start.of.month")).thenReturn("Last year start of month ");
        when(lang.getText("last.year.start.of.month.minus")).thenReturn("Last year start of month - ");
        when(lang.getText("last.year.end.of.month.plus")).thenReturn("Last year end of month + ");
        when(lang.getText("last.year.end.of.month")).thenReturn("Last year end of month ");
        when(lang.getText("last.year.end.of.month.minus")).thenReturn("Last year end of month - ");
        when(lang.getText("last.year.last.updated.date")).thenReturn("Last year last updated date ");
        when(lang.getText("last.year.last.updated.date.minus")).thenReturn("Last year last updated date - ");
        when(lang.getText("system.date")).thenReturn("System Date ");
        when(lang.getText("system.date.plus")).thenReturn("System Date + ");
        when(lang.getText("system.date.minus")).thenReturn("System Date - ");
        when(lang.getText("start.of.month.plus")).thenReturn("Start of Month +");
        when(lang.getText("start.of.month")).thenReturn("Start of Month");
        when(lang.getText("start.of.month.minus")).thenReturn("Start of Month - ");
        when(lang.getText("end.of.month.plus")).thenReturn("End of Month + ");
        when(lang.getText("end.of.month")).thenReturn("End of Month");
        when(lang.getText("end.of.month.minus")).thenReturn("End of Month - ");
    }

    private List<BusinessGroup> getBusinessViews(int size) {
        List<BusinessGroup> list = new ArrayList<>();
        for (int i = 0; i < size; i++) {
            BusinessGroup businessGroup = new BusinessGroup();
            businessGroup.setName("BG" + i);
            businessGroup.setId(i);
            list.add(businessGroup);
        }
        return list;
    }

    private List<ForecastGroupSummary> getForeCastSummaries(int size) {
        List<ForecastGroupSummary> list = new ArrayList<>();
        for (int i = 0; i < size; i++) {
            ForecastGroupSummary fg = new ForecastGroupSummary();
            fg.setName("FG" + i);
            fg.setId(i);
            list.add(fg);
        }
        return list;
    }

    private void mockAdditionalData() {
        doReturn(reportParamMapFromURL).when(presenter).getParameterMap();
        Mockito.doNothing().when(view).initForm(anyObject(), any(), any(), any());
    }

    private HashMap<String, String> getReportParamMapFromURL(String level, String subLevel, String subType) {
        HashMap<String, String> paramMap = new HashMap<String, String>();
        paramMap.put("timeframe", "03/23/2016:05/12/2016");
        paramMap.put("businessdt", "03/22/2016");
        paramMap.put("lybusinessdt", "03/24/2015");
        paramMap.put("lystartdt", "03/25/2015");
        paramMap.put("lyenddt", "05/14/2015");
        paramMap.put("level", level);
        paramMap.put("sublevel", subLevel);
        paramMap.put("subtype", subType);
        return paramMap;
    }

    @Test
    public void loadInfoFromURLWithBVAndBG1() {
        reportParamMapFromURL = getReportParamMapFromURL("PROPERTY_BUSINESS_VIEW", "BG1", "SOLD");
        mockAdditionalData();
        presenter.loadInfoFromUrl();
        BookingSituationFilterDTO dto = presenter.getFilterDTO();
        assertNotNull(dto);
        assertDates(dto);
        assertEquals(BookingSituationType.BUSINESS_VIEW, dto.getBookingSituationType());
        assertEquals(1, dto.getBusinessView().size());
        dto.getBusinessView().forEach(a -> assertTrue(a.getName().equalsIgnoreCase("BG1")));
    }

    @Test
    public void loadInfoFromURLWithFGAndFG1() {
        reportParamMapFromURL = getReportParamMapFromURL("FORECAST_GROUP", "FG1", "SOLD");
        mockAdditionalData();
        presenter.loadInfoFromUrl();
        BookingSituationFilterDTO dto = presenter.getFilterDTO();
        assertEquals(BookingSituationType.FORECAST_GROUPS, dto.getBookingSituationType());
        assertEquals(1, dto.getForecastGroup().size());
        dto.getForecastGroup().forEach(a -> assertTrue(a.getName().equalsIgnoreCase("FG1")));
    }

    @Test
    public void loadInfoFromURLWithPropertyAndADR() {
        reportParamMapFromURL = getReportParamMapFromURL("PROPERTY", "", "ADR");
        mockAdditionalData();
        presenter.loadInfoFromUrl();
        BookingSituationFilterDTO dto = presenter.getFilterDTO();
        assertEquals(BookingSituationType.TOTAL_PROPERTY, dto.getBookingSituationType());
        assertEquals(1, dto.getDataElements().size());
        dto.getDataElements().forEach(a -> assertTrue(a.getCaption().equalsIgnoreCase("ADR")));
    }

    @Test
    public void loadInfoFromURLWithGroupAndSold() {
        reportParamMapFromURL = getReportParamMapFromURL("BUSINESS_TYPE", "Group", "SOLD");
        mockAdditionalData();
        presenter.loadInfoFromUrl();
        BookingSituationFilterDTO dto = presenter.getFilterDTO();
        assertEquals(BookingSituationType.TOTAL_GROUP, dto.getBookingSituationType());
        assertEquals(1, dto.getDataElements().size());
        dto.getDataElements().forEach(a -> assertTrue(a.getCaption().equalsIgnoreCase("Occupancy On Books")));
    }

    @Test
    public void loadInfoFromURLWithTransientAndRevpar() {
        reportParamMapFromURL = getReportParamMapFromURL("BUSINESS_TYPE", "Transient", "REVPAR");
        mockAdditionalData();
        presenter.loadInfoFromUrl();
        BookingSituationFilterDTO dto = presenter.getFilterDTO();
        assertEquals(BookingSituationType.TOTAL_TRANSIENT, dto.getBookingSituationType());
        assertEquals(1, dto.getDataElements().size());
        dto.getDataElements().forEach(a -> assertTrue(a.getCaption().equalsIgnoreCase("revpar")));
    }

    private void assertDates(BookingSituationFilterDTO dto) {
        assertEquals(LocalDate.parse("2016-03-23"), dto.getAnalysisDateSelectorBean().getSpecificStartDate());
        assertEquals(LocalDate.parse("2016-05-12"), dto.getAnalysisDateSelectorBean().getSpecificEndDate());
        assertEquals(LocalDate.parse("2016-03-22"), dto.getAnalysisAsOfDateSelectorBean().getSpecificStartDate());
        assertEquals(LocalDate.parse("2015-03-25"), dto.getComparisionDateSelectorBean().getSpecificStartDate());
        assertEquals(LocalDate.parse("2015-05-14"), dto.getComparisionDateSelectorBean().getSpecificEndDate());
        assertEquals(LocalDate.parse("2015-03-24"), dto.getComparisionAsOfDateSelectorBean().getSpecificStartDate());
    }

    @Test
    public void resetFilterDataTest() {
        presenter.resetFilterData();
        verifyResetFilterData();
    }

    private void verifyResetFilterData() {
        verify(view).setDataElementsData(Arrays.asList(BookingSituationDataElementsEnum.values()));
        verify(forecastGroupComponentService).getAllForecastGroupSummariesOrderedByRank();
        verify(view).setForecastGroupData(anyList());
        verify(businessGroupService).getAllBusinessGroupDetails();
        verify(view).setBusinessViewData(anyList());

        BookingSituationFilterDTO dto = presenter.getFilterDTO();
        assertEquals(BookingSituationType.TOTAL_PROPERTY, dto.getBookingSituationType());
        dto.setPropertyID(uiContext.getPropertyId());

        JavaDateSelectorBean anayDateBean = dto.getAnalysisDateSelectorBean();
        assertNotNull(anayDateBean);
        String date = DateFormatUtil.formatDateFor(currentDate, UiUtils.getLocale());
        assertEquals(date, anayDateBean.getStartDate());
        date = DateFormatUtil.formatDateFor(currentDate.plusDays(DEFAULT_DAYS_BETWEEN_START_END), UiUtils.getLocale());
        assertEquals(date, anayDateBean.getEndDate());

        JavaDateSelectorBean anayAsOfDateBean = dto.getAnalysisAsOfDateSelectorBean();
        assertNotNull(anayAsOfDateBean);
        date = DateFormatUtil.formatDateFor(currentDate.minusDays(DAYS_IN_ONE_MONTH), UiUtils.getLocale());
        assertEquals(date, anayAsOfDateBean.getStartDate());

        JavaDateSelectorBean compDateBean = dto.getComparisionDateSelectorBean();
        assertNotNull(compDateBean);
        date = DateFormatUtil.formatDateFor(currentDate.minusWeeks(WEEKS_IN_YEAR), UiUtils.getLocale());
        assertEquals(date, compDateBean.getStartDate());
        date = DateFormatUtil.formatDateFor(currentDate.minusWeeks(WEEKS_IN_YEAR).plusDays(DEFAULT_DAYS_BETWEEN_START_END), UiUtils.getLocale());
        assertEquals(date, compDateBean.getEndDate());

        JavaDateSelectorBean compAsOfDateBean = dto.getComparisionAsOfDateSelectorBean();
        assertNotNull(compAsOfDateBean);
        date = DateFormatUtil.formatDateFor(currentDate.minusWeeks(WEEKS_IN_YEAR).minusDays(DAYS_IN_ONE_MONTH), UiUtils.getLocale());
        assertEquals(date, compAsOfDateBean.getStartDate());

        verify(view).initForm(any(), any(), any(), any());
    }

    @Test
    public void shouldSaveSharedSessionStateTest() {
        presenter.saveFilterDataToSession();
        Mockito.verify(sharedSessionState, times(1)).setStoredBookingSituationReportFilterData(anyObject());
    }

    @Test
    public void loadSharedSessionData() {
        presenter.loadSharedSessionStateData();
        verify(view).setDataElementsData(Arrays.asList(BookingSituationDataElementsEnum.values()));
        verify(forecastGroupComponentService).getAllForecastGroupSummariesOrderedByRank();
        verify(view).setForecastGroupData(anyList());
        verify(businessGroupService).getAllBusinessGroupDetails();
        verify(view).setBusinessViewData(anyList());
        verify(sharedSessionState).getStoredBookingSituationReportFilterDTO();
        verify(view).initForm(any(), any(), any(), any());
    }

    @Test
    public void onWorkContextChangeTest() {
        presenter.onWorkContextChange(new WorkContextType());
        verifyResetFilterData();
        verify(view).resetView(anyBoolean());
        verify(view).removeTable();
    }

    @Test
    public void getReportCriteriaTest() {
        assertNull(presenter.getReportCriteria(null));
    }

    @Test
    public void getReportType() {
        assertEquals(Report_Type, presenter.getReportType().getReportTitle());
    }

    @Test
    public void getJasperReportTypeTest() {
        assertEquals(JasperReportTypeEnum.BOOKING_SITUATION_REPORT, presenter.getJasperReportType());
    }

    @Test
    public void sharedSessionStateDataExistTest() {
        when(sharedSessionState.getStoredBookingSituationReportFilterDTO()).thenReturn(null);
        assertFalse(presenter.sharedSessionStateDataExist());
        BookingSituationFilterDTO dto = new BookingSituationFilterDTO();
        dto.setPropertyID(5);
        when(sharedSessionState.getStoredBookingSituationReportFilterDTO()).thenReturn(dto);
        assertTrue(presenter.sharedSessionStateDataExist());
    }

    @Test
    public void isRollingDateTest() {
        presenter.resetFilterData();
        presenter.getFilterDTO().getAnalysisDateSelectorBean().setRollingDate(true);
        assertTrue(presenter.isRollingDate());
    }

    @Test
    public void inputsValidForSchedulingForStaticDates() {
        presenter.resetFilterData();
        presenter.getFilterDTO().getAnalysisDateSelectorBean().setRollingDate(false);
        boolean result = presenter.inputsValidForScheduling();
        assertFalse(result);
    }

    @Test
    public void isValidForStaticDates() {
        presenter.resetFilterData();
        presenter.getFilterDTO().getAnalysisDateSelectorBean().setRollingDate(false);
        boolean result = presenter.isValid();
        assertTrue(result);
    }

    private void setRollingDates(String anaStartDate, String anaEndDate, String anaAsOfDate, String compStartDate, String compEndDate, String compAsOfDate) {
        JavaDateSelectorBean analysisDateSelectorBean = presenter.getFilterDTO().getAnalysisDateSelectorBean();
        JavaDateSelectorBean analysisAsOfDateSelectorBean = presenter.getFilterDTO().getAnalysisAsOfDateSelectorBean();
        JavaDateSelectorBean comparisionDateSelectorBean = presenter.getFilterDTO().getComparisionDateSelectorBean();
        JavaDateSelectorBean comparisionAsOfDateSelectorBean = presenter.getFilterDTO().getComparisionAsOfDateSelectorBean();
        analysisDateSelectorBean.setRollingDateString(anaStartDate, anaEndDate);
        analysisAsOfDateSelectorBean.setRollingDateString(anaAsOfDate);
        comparisionDateSelectorBean.setRollingDateString(compStartDate, compEndDate);
        comparisionAsOfDateSelectorBean.setRollingDateString(compAsOfDate);
    }

    private void setStaticDates(String anaStartDate, String anaEndDate, String anaAsOfDate, String compStartDate, String compEndDate, String compAsOfDate) {
        JavaDateSelectorBean analysisDateSelectorBean = presenter.getFilterDTO().getAnalysisDateSelectorBean();
        JavaDateSelectorBean analysisAsOfDateSelectorBean = presenter.getFilterDTO().getAnalysisAsOfDateSelectorBean();
        JavaDateSelectorBean comparisionDateSelectorBean = presenter.getFilterDTO().getComparisionDateSelectorBean();
        JavaDateSelectorBean comparisionAsOfDateSelectorBean = presenter.getFilterDTO().getComparisionAsOfDateSelectorBean();
        analysisDateSelectorBean.setStartDate("10-oct-2020");
        analysisDateSelectorBean.setEndDate("11-oct-2020");
        analysisAsOfDateSelectorBean.setStartDate("10-sep-2020");
        comparisionDateSelectorBean.setStartDate("11-oct-2020");
        comparisionDateSelectorBean.setEndDate("10-oct-2020");
        comparisionAsOfDateSelectorBean.setStartDate("11-sep-2020");
    }

    @Test
    public void inputsValidForSchedulingHappyPath() {
        presenter.resetFilterData();
        setRollingDates(getText(SYSTEM_DATE), getText(SYSTEM_DATE), getText(LAST_UPDATED_DATE),
                getText(LAST_YEAR_THIS_DAY), getText(LAST_YEAR_THIS_DAY), getText(LAST_YEAR_LAST_UPDATED_DATE));
        boolean result = presenter.inputsValidForScheduling();
        assertTrue(result);
    }

    private void validateWarningDisplayed(String msgKey, boolean expectedResult) {
        boolean result = presenter.inputsValidForScheduling();
        assertEquals(expectedResult, result);
        verify(view).showAlert(any(), any());
        verify(lang).getText(WARNING);
        verify(lang).getText(msgKey);
    }

    @Test
    public void inputsValidForSchedulingAnalysisEndDateShouldBeGreaterThanAnalysisAsOFDate() {
        presenter.resetFilterData();
        setRollingDates(getText(START_OF_MONTH_MINUS) + "2", getText(END_OF_MONTH_MINUS) + "2", getText(LAST_UPDATED_DATE),
                getText(LAST_YEAR_START_OF_MONTH_MINUS) + "2", getText(LAST_YEAR_END_OF_MONTH_MINUS) + "2", getText(LAST_YEAR_LAST_UPDATED_DATE));
        validateWarningDisplayed("analysisAsOfStartDateShouldBeLessThanOrEqualAnalysisEndDate", false);
    }

    @Test
    public void inputsValidForSchedulingCompEndDateShouldBeGreaterThanCompAsOFDate() {
        presenter.resetFilterData();
        setRollingDates(getText(START_OF_MONTH_MINUS) + "2", getText(END_OF_MONTH_MINUS) + "2", getText(LAST_UPDATED_DATE_MINUS) + "90",
                getText(LAST_YEAR_START_OF_MONTH_MINUS) + "2", getText(LAST_YEAR_END_OF_MONTH_MINUS) + "2", getText(LAST_YEAR_LAST_UPDATED_DATE));
        validateWarningDisplayed("comparisonAsOfStartDateShouldBeLessThanOrEqualComparisonEndDate", false);
    }

    @Test
    public void inputsValidForSchedulingAnalysisStartDateShouldBeGreaterThanCompStartDate() {
        presenter.resetFilterData();
        setRollingDates(getText(START_OF_MONTH_MINUS) + "2", getText(END_OF_MONTH_MINUS) + "2", getText(LAST_UPDATED_DATE_MINUS) + "90",
                getText(LAST_YEAR_START_OF_MONTH_PLUS) + "15", getText(LAST_YEAR_END_OF_MONTH_PLUS) + "15", getText(LAST_YEAR_LAST_UPDATED_DATE));
        validateWarningDisplayed("comparisonStartDateShouldBeLessThanAnalysisStartDate", false);
    }

    @Test
    public void inputsValidForSchedulingEndDateShouldNotExceedLimit() {
        presenter.resetFilterData();
        setRollingDates(getText(LocalizationKeyConstants.START_OF_MONTH), getText(END_OF_MONTH_PLUS) + "13", getText(LAST_UPDATED_DATE_MINUS) + "90",
                getText(LAST_YEAR_START_OF_MONTH), getText(LAST_YEAR_END_OF_MONTH_PLUS) + "12", getText(LAST_YEAR_LAST_UPDATED_DATE));
        validateWarningDisplayed("endDateShouldNotExceedLimit", false);
    }

    @Test
    public void inputsValidForSchedulingBVSelectionRestriction() {
        presenter.resetFilterData();
        setRollingDates(getText(LocalizationKeyConstants.START_OF_MONTH), getText(END_OF_MONTH_PLUS) + "7", getText(LAST_UPDATED_DATE_MINUS) + "90",
                getText(LAST_YEAR_START_OF_MONTH), getText(LAST_YEAR_END_OF_MONTH_PLUS) + "7", getText(LAST_YEAR_LAST_UPDATED_DATE));
        boolean result = presenter.inputsValidForScheduling();
        assertEquals(true, result);
    }

    @Test
    public void validateFGSelectionRestrictionWith25FGs() {
        presenter.resetFilterData();
        BookingSituationFilterDTO filterDTO = presenter.getFilterDTO();
        List<ForecastGroupSummary> list = getForeCastSummaries(25);
        filterDTO.setForecastGroup(new HashSet<ForecastGroupSummary>(list));
        boolean result = presenter.isValid();
        assertTrue(result);
    }

    @Test
    public void validateFGSelectionRestrictionWith26FGs() {
        presenter.resetFilterData();
        BookingSituationFilterDTO filterDTO = presenter.getFilterDTO();
        List<ForecastGroupSummary> list = getForeCastSummaries(26);
        filterDTO.setForecastGroup(new HashSet<ForecastGroupSummary>(list));
        boolean result = presenter.isValid();
        assertFalse(result);
        verify(view).showAlert(any(), any());
        verify(lang).getText(WARNING);
        verify(lang).getText("warningOnSelectingMoreThan25FGs");
    }

    @Test
    public void validateBVSelectionRestrictionWithRollingDates() {
        presenter.resetFilterData();
        setRollingDates(getText(LocalizationKeyConstants.START_OF_MONTH), getText(END_OF_MONTH_PLUS) + "7", getText(LAST_UPDATED_DATE_MINUS) + "90",
                getText(LAST_YEAR_START_OF_MONTH), getText(LAST_YEAR_END_OF_MONTH_PLUS) + "7", getText(LAST_YEAR_LAST_UPDATED_DATE));
        validateBVSelectionRestriction();
    }

    @Test
    public void validateBVSelectionRestrictionWithSpecificDates() {
        presenter.resetFilterData();
        presenter.getFilterDTO().getAnalysisDateSelectorBean().setSpecificDateWith(181L, uiContext.getSystemCaughtUpDateAsJavaLocalDate());
        validateBVSelectionRestriction();
    }

    private void validateBVSelectionRestriction() {
        List<BusinessGroup> list = getBusinessViews(26);
        BookingSituationFilterDTO filterDTO = presenter.getFilterDTO();
        filterDTO.setBusinessView(new HashSet<>(list));
        boolean result = presenter.isValid();
        assertFalse(result);
        verify(view).showAlert(any(), any());
    }

    private void populateData(BookingSituationType type) {
        presenter.resetFilterData();
        BookingSituationFilterDTO filterDTO = presenter.getFilterDTO();
        filterDTO.setBookingSituationType(type);
        Set<BookingSituationDataElementsEnum> dataElements = new HashSet<>();
        dataElements.add(BookingSituationDataElementsEnum.ADR);
        dataElements.add(BookingSituationDataElementsEnum.SPECIAL_EVENT);
        dataElements.add(BookingSituationDataElementsEnum.REVENUE);
        filterDTO.setDataElements(dataElements);
    }

    private void validateValuesForRollingDates(Map<String, String> params, String anaStDt, String anaEndDt, String anaBusDt, String compStDt, String compEnDt, String compBusDt) {
        assertEquals(anaStDt, params.get(PARAM_ROLLINGANALYSISSTARTDATE));
        assertEquals(anaEndDt, params.get(PARAM_ROLLINGANALYSISENDDATE));
        assertEquals(anaBusDt, params.get(PARAM_ROLLINGANALYSISBUSINESSDATE));
        assertEquals(compStDt, params.get(PARAM_ROLLINGCOMPARISIONSTARTDATE));
        assertEquals(compEnDt, params.get(PARAM_ROLLINGCOMPARISIONENDDATE));
        assertEquals(compBusDt, params.get(PARAM_ROLLINGCOMPARISIONBUSINESSDATE));
    }

    @Test
    public void getReportParametersWithRollingDateAndPropertySelected() {
        populateData(BookingSituationType.TOTAL_PROPERTY);
        setRollingDates(getText(SYSTEM_DATE), getText(SYSTEM_DATE), getText(LAST_UPDATED_DATE),
                getText(LAST_YEAR_THIS_DAY), getText(LAST_YEAR_THIS_DAY), getText(LAST_YEAR_LAST_UPDATED_DATE));

        Map<String, String> params = presenter.getReportParameters();
        assertEquals(37, params.size());
        validateDateValuesForRollingDates(params);
        validateValuesForRollingDates(params, TODAY, TODAY, LAST_UPDATED, LY_THIS_DAY, LY_THIS_DAY, LY_LAST_UPDATED);
    }

    @Test
    public void getReportParametersWithRollingDateWithDateOptions() {
        populateData(BookingSituationType.TOTAL_PROPERTY);
        setRollingDates(getText(SYSTEM_DATE_MINUS) + "2", getText(SYSTEM_DATE_PLUS) + "2", getText(LAST_UPDATED_DATE_MINUS) + "3",
                getText(LAST_YEAR_THIS_DAY_MINUS) + "2", getText(LAST_YEAR_THIS_DAY_PLUS) + "2", getText(LAST_YEAR_LAST_UPDATED_DATE_MINUS) + "3");

        Map<String, String> params = presenter.getReportParameters();
        assertEquals(37, params.size());
        validateDateValuesForRollingDates(params);
        validateValuesForRollingDates(params, TODAY + "-2", TODAY + "+2", LAST_UPDATED + "-3", LY_THIS_DAY + "-2", LY_THIS_DAY + "+2", LY_LAST_UPDATED + "-3");
    }

    @Test
    public void getReportParametersWithRollingDateWithMonthOptions() {
        populateData(BookingSituationType.TOTAL_PROPERTY);
        setRollingDates(getText(START_OF_MONTH_MINUS) + "2", getText(END_OF_MONTH_PLUS) + "2", getText(LAST_UPDATED_DATE_MINUS) + "3",
                getText(LAST_YEAR_START_OF_MONTH_MINUS) + "2", getText(LAST_YEAR_END_OF_MONTH_PLUS) + "2", getText(LAST_YEAR_LAST_UPDATED_DATE_MINUS) + "3");

        Map<String, String> params = presenter.getReportParameters();
        assertEquals(37, params.size());
        validateDateValuesForRollingDates(params);
        validateValuesForRollingDates(params, START_OF_MONTH + "-2", END_OF_MONTH + "+2", LAST_UPDATED + "-3", LY_START_OF_MONTH + "-2", LY_END_OF_MONTH + "+2", LY_LAST_UPDATED + "-3");
    }


    private void validateDateValuesForRollingDates(Map<String, String> params) {
        assertEquals(DEFAULT_DATE_FOR_REPORTS, params.get(PARAM_ANALYSISSTARTDATE));
        assertEquals(DEFAULT_DATE_FOR_REPORTS, params.get(PARAM_ANALYSISENDDATE));
        assertEquals(DEFAULT_DATE_FOR_REPORTS, params.get(PARAM_ANALYSISASOFDATE));
        assertEquals(DEFAULT_DATE_FOR_REPORTS, params.get(PARAM_COMPARISIONSTARTDATE));
        assertEquals(DEFAULT_DATE_FOR_REPORTS, params.get(PARAM_COMPARISIONENDDATE));
        assertEquals(DEFAULT_DATE_FOR_REPORTS, params.get(PARAM_COMPARISIONASOFDATE));
    }

    @Test
    public void getReportParametersWithStaticDatesAndPropertySelected() {
        populateData(BookingSituationType.TOTAL_PROPERTY);
        Map<String, String> params = presenter.getReportParameters();
        assertEquals(37, params.size());
        assertEquals(TRUE_VALUE, params.get(PARAM_ISHOTELCHECKED));
        assertEquals(FALSE_VALUE, params.get(PARAM_ISREVPARCHECKED_HOTEL));
        assertEquals(FALSE_VALUE, params.get(PARAM_ISROOMSSOLDCHECKED_HOTEL));
        assertEquals(TRUE_VALUE, params.get(PARAM_ISSPECIALEVENTCHECKED_HOTEL));
        assertEquals(TRUE_VALUE, params.get(PARAM_ISROOMREVENUECHECKED_HOTEL));
        assertEquals(TRUE_VALUE, params.get(PARAM_ISADRCHECKED_HOTEL));

        validateTransientParams(params, FALSE_VALUE);
        validateGroupParams(params, FALSE_VALUE);

        assertEquals(BLANK, params.get(PARAM_FORECASTGROUPS));
        assertEquals(FALSE_VALUE, params.get(PARAM_ISFGCHECKED));
        assertEquals(BLANK, params.get(PARAM_BUSINESSVIEWS));
        assertEquals(FALSE_VALUE, params.get(PARAM_ISBVCHECKED));

        assertEquals(ZERO_AS_STRING, params.get(PARAM_ISROLLING_DATE));
        assertEquals("xlsx", params.get(OUTPUT));
        assertEquals(TRUE_VALUE, params.get(IS_IGNORE_PAGINATION));

        assertEquals(getFormattedDate(currentDate), params.get(PARAM_ANALYSISSTARTDATE));
        assertEquals(getFormattedDate(currentDate.plusDays(7)), params.get(PARAM_ANALYSISENDDATE));
        assertEquals(getFormattedDate(currentDate.minusDays(30)), params.get(PARAM_ANALYSISASOFDATE));
        assertEquals(getFormattedDate(currentDate.minusWeeks(52)), params.get(PARAM_COMPARISIONSTARTDATE));
        assertEquals(getFormattedDate(currentDate.minusWeeks(52).plusDays(7)), params.get(PARAM_COMPARISIONENDDATE));
        assertEquals(getFormattedDate(currentDate.minusWeeks(52).minusDays(30)), params.get(PARAM_COMPARISIONASOFDATE));
        validateValuesForRollingDates(params, null, null, null, null, null, null);

        assertEquals(ZERO_AS_STRING, params.get(IS_PHYSICAL_CAPACITY));
        assertEquals(USD, params.get(PARAM_BASE_CURRENCY));

    }

    @Test
    public void getReportParametersWithStaticDatesAndTransientSelected() {
        populateData(BookingSituationType.TOTAL_TRANSIENT);
        Map<String, String> params = presenter.getReportParameters();
        assertEquals(37, params.size());
        assertEquals(TRUE_VALUE, params.get(PARAM_ISTRANSIENTCHECKED));
        assertEquals(FALSE_VALUE, params.get(PARAM_ISROOMSSOLDCHECKED_TRANS));
        assertEquals(TRUE_VALUE, params.get(PARAM_ISSPECIALEVENTCHECKED_TRANS));
        assertEquals(TRUE_VALUE, params.get(PARAM_ISROOMREVENUECHECKED_TRANS));
        assertEquals(TRUE_VALUE, params.get(PARAM_ISADRCHECKED_TRANS));

        assertEquals(BLANK, params.get(PARAM_FORECASTGROUPS));
        assertEquals(FALSE_VALUE, params.get(PARAM_ISFGCHECKED));
        assertEquals(BLANK, params.get(PARAM_BUSINESSVIEWS));
        assertEquals(FALSE_VALUE, params.get(PARAM_ISBVCHECKED));

        validatePropertyParams(params, FALSE_VALUE);
        validateGroupParams(params, FALSE_VALUE);
    }

    @Test
    public void getReportParametersWithStaticDatesAndGroupSelected() {
        populateData(BookingSituationType.TOTAL_GROUP);
        Map<String, String> params = presenter.getReportParameters();
        assertEquals(37, params.size());
        assertEquals(TRUE_VALUE, params.get(PARAM_ISGROUPCHECKED));
        assertEquals(FALSE_VALUE, params.get(PARAM_ISROOMSSOLDCHECKED_GROUP));
        assertEquals(TRUE_VALUE, params.get(PARAM_ISSPECIALEVENTCHECKED_GROUP));
        assertEquals(TRUE_VALUE, params.get(PARAM_ISROOMREVENUECHECKED_GROUP));
        assertEquals(TRUE_VALUE, params.get(PARAM_ISADRCHECKED_GROUP));

        assertEquals(BLANK, params.get(PARAM_FORECASTGROUPS));
        assertEquals(FALSE_VALUE, params.get(PARAM_ISFGCHECKED));
        assertEquals(BLANK, params.get(PARAM_BUSINESSVIEWS));
        assertEquals(FALSE_VALUE, params.get(PARAM_ISBVCHECKED));

        validatePropertyParams(params, FALSE_VALUE);
        validateTransientParams(params, FALSE_VALUE);
    }

    @Test
    public void getReportParametersWithStaticDatesAndForecastGroupSelected() {
        presenter.resetFilterData();
        BookingSituationFilterDTO filterDTO = presenter.getFilterDTO();
        filterDTO.setBookingSituationType(BookingSituationType.FORECAST_GROUPS);
        List<ForecastGroupSummary> fgList = getForeCastSummaries(4);
        fgList.remove(2);
        Set<ForecastGroupSummary> fgSummarySet = new HashSet<>(fgList);
        filterDTO.setForecastGroup(fgSummarySet);
        Map<String, String> params = presenter.getReportParameters();
        assertEquals(37, params.size());

        assertEquals("0,1,3", params.get(PARAM_FORECASTGROUPS));
        assertEquals(TRUE_VALUE, params.get(PARAM_ISFGCHECKED));
        assertEquals(BLANK, params.get(PARAM_BUSINESSVIEWS));
        assertEquals(FALSE_VALUE, params.get(PARAM_ISBVCHECKED));

        validatePropertyParams(params, FALSE_VALUE);
        validateTransientParams(params, FALSE_VALUE);
        validateGroupParams(params, FALSE_VALUE);
    }

    @Test
    public void getReportParametersWithStaticDatesAndBusinessViewsSelected() {
        presenter.resetFilterData();
        BookingSituationFilterDTO filterDTO = presenter.getFilterDTO();
        filterDTO.setBookingSituationType(BookingSituationType.BUSINESS_VIEW);
        List<BusinessGroup> bgList = getBusinessViews(4);
        bgList.remove(2);
        Set<BusinessGroup> bgSummarySet = new HashSet<>(bgList);
        filterDTO.setBusinessView(bgSummarySet);
        Map<String, String> params = presenter.getReportParameters();
        assertEquals(37, params.size());

        assertEquals(BLANK, params.get(PARAM_FORECASTGROUPS));
        assertEquals(FALSE_VALUE, params.get(PARAM_ISFGCHECKED));
        assertEquals("0,1,3", params.get(PARAM_BUSINESSVIEWS));
        assertEquals(TRUE_VALUE, params.get(PARAM_ISBVCHECKED));

        validatePropertyParams(params, FALSE_VALUE);
        validateTransientParams(params, FALSE_VALUE);
        validateGroupParams(params, FALSE_VALUE);
    }

    private void validatePropertyParams(Map<String, String> params, String value) {
        assertEquals(value, params.get(PARAM_ISHOTELCHECKED));
        assertEquals(value, params.get(PARAM_ISREVPARCHECKED_HOTEL));
        assertEquals(value, params.get(PARAM_ISROOMSSOLDCHECKED_HOTEL));
        assertEquals(value, params.get(PARAM_ISSPECIALEVENTCHECKED_HOTEL));
        assertEquals(value, params.get(PARAM_ISROOMREVENUECHECKED_HOTEL));
        assertEquals(value, params.get(PARAM_ISADRCHECKED_HOTEL));
    }

    private void validateGroupParams(Map<String, String> params, String value) {
        assertEquals(value, params.get(PARAM_ISGROUPCHECKED));
        assertEquals(value, params.get(PARAM_ISROOMSSOLDCHECKED_GROUP));
        assertEquals(value, params.get(PARAM_ISSPECIALEVENTCHECKED_GROUP));
        assertEquals(value, params.get(PARAM_ISROOMREVENUECHECKED_GROUP));
        assertEquals(value, params.get(PARAM_ISADRCHECKED_GROUP));
    }

    private void validateTransientParams(Map<String, String> params, String value) {
        assertEquals(value, params.get(PARAM_ISTRANSIENTCHECKED));
        assertEquals(value, params.get(PARAM_ISROOMSSOLDCHECKED_TRANS));
        assertEquals(value, params.get(PARAM_ISSPECIALEVENTCHECKED_TRANS));
        assertEquals(value, params.get(PARAM_ISROOMREVENUECHECKED_TRANS));
        assertEquals(value, params.get(PARAM_ISADRCHECKED_TRANS));
    }

    private String getFormattedDate(LocalDate ld) {
        return ld.toString().replaceAll(HYPEN, BLANK);
    }

    private void validateDataElementsInEditCase(BookingSituationType type) {
        BookingSituationFilterDTO filterDTO = presenter.getFilterDTO();
        assertEquals(type, filterDTO.getBookingSituationType());
        Set<BookingSituationDataElementsEnum> dataElements = filterDTO.getDataElements();
        assertEquals(3, dataElements.size());
        assertTrue(dataElements.contains(BookingSituationDataElementsEnum.ADR));
        assertTrue(dataElements.contains(BookingSituationDataElementsEnum.SPECIAL_EVENT));
        assertTrue(dataElements.contains(BookingSituationDataElementsEnum.REVENUE));
        assertNull(filterDTO.getForecastGroup());
        assertNull(filterDTO.getBusinessView());
    }

    @Test
    public void editScheduleDataWhenPropertyIsSelectedWithDateOption() {
        populateData(BookingSituationType.TOTAL_PROPERTY);
        setRollingDates(getText(SYSTEM_DATE), getText(SYSTEM_DATE), getText(LAST_UPDATED_DATE),
                getText(LAST_YEAR_THIS_DAY), getText(LAST_YEAR_THIS_DAY), getText(LAST_YEAR_LAST_UPDATED_DATE));
        Map<String, String> params = presenter.getReportParameters();
        presenter.editScheduleData(params, true, null);
        validateRollingDatesInCaseOfEdit(TODAY, TODAY, LAST_UPDATED, LY_THIS_DAY, LY_THIS_DAY, LY_LAST_UPDATED);
        validateDataElementsInEditCase(BookingSituationType.TOTAL_PROPERTY);
        BookingSituationFilterDTO filterDTO = presenter.getFilterDTO();
        JavaDateSelectorBean comparisionDateSelectorBean = filterDTO.getComparisionDateSelectorBean();
        Set<RollingDateOptionBean> startSet = comparisionDateSelectorBean.getStartRollingDateOptions();
        assertEquals(3, startSet.size());
        Set<RollingDateOptionBean> endSet = comparisionDateSelectorBean.getEndRollingDateOptions();
        assertEquals(3, endSet.size());
        assertEquals(startSet, endSet);
        assertEquals("Analysis Start Date", filterDTO.getAnalysisDateSelectorBean().getStartDateCaption());
        assertEquals("Analysis End Date", filterDTO.getAnalysisDateSelectorBean().getEndDateCaption());
        assertEquals("Analysis as of Date", filterDTO.getAnalysisAsOfDateSelectorBean().getStartDateCaption());
        assertEquals("Comparison Start Date", comparisionDateSelectorBean.getStartDateCaption());
        assertEquals("Comparison End Date", comparisionDateSelectorBean.getEndDateCaption());
        assertEquals("Comparison as of Date", filterDTO.getComparisionAsOfDateSelectorBean().getStartDateCaption());
    }

    @Test
    public void editScheduleDataWhenPropertyIsSelectedForOnlineReport() {
        populateData(BookingSituationType.TOTAL_PROPERTY);
        setStaticDates(getText(SYSTEM_DATE), getText(SYSTEM_DATE), getText(LAST_UPDATED_DATE),
                getText(LAST_YEAR_THIS_DAY), getText(LAST_YEAR_THIS_DAY), getText(LAST_YEAR_LAST_UPDATED_DATE));
        Map<String, String> params = presenter.getReportParameters();
        presenter.editScheduleData(params, false, null);
        BookingSituationFilterDTO filterDTO = presenter.getFilterDTO();
        JavaDateSelectorBean comparisionDateSelectorBean = filterDTO.getComparisionDateSelectorBean();
        JavaDateSelectorBean comparisionAsOfDateSelectorBean = filterDTO.getComparisionAsOfDateSelectorBean();
        JavaDateSelectorBean analysisDateSelectorBean = filterDTO.getAnalysisDateSelectorBean();
        JavaDateSelectorBean analysisAsOfDateSelectorBean = filterDTO.getAnalysisAsOfDateSelectorBean();

        assertEquals("2020-10-10", analysisDateSelectorBean.getSpecificStartDate().toString());
        assertEquals("2020-10-11", analysisDateSelectorBean.getSpecificEndDate().toString());
        assertEquals("2020-09-10", analysisAsOfDateSelectorBean.getSpecificStartDate().toString());
        assertEquals("2020-10-11", comparisionDateSelectorBean.getSpecificStartDate().toString());
        assertEquals("2020-10-10", comparisionDateSelectorBean.getSpecificEndDate().toString());
        assertEquals("2020-09-11", comparisionAsOfDateSelectorBean.getSpecificStartDate().toString());


        assertEquals("Analysis Start Date", analysisDateSelectorBean.getStartDateCaption());
        assertEquals("Analysis End Date", analysisDateSelectorBean.getEndDateCaption());
        assertEquals("Analysis as of Date", analysisAsOfDateSelectorBean.getStartDateCaption());
        assertEquals("Comparison Start Date", comparisionDateSelectorBean.getStartDateCaption());
        assertEquals("Comparison End Date", comparisionDateSelectorBean.getEndDateCaption());
        assertEquals("Comparison as of Date", comparisionAsOfDateSelectorBean.getStartDateCaption());
    }

    @Test
    public void editScheduleDataWhenTransientIsSelectedWithMonthOption() {
        populateData(BookingSituationType.TOTAL_TRANSIENT);
        setRollingDates(getText(START_OF_MONTH_MINUS) + "2", getText(END_OF_MONTH_PLUS) + "2", getText(LAST_UPDATED_DATE_MINUS) + "3",
                getText(LAST_YEAR_START_OF_MONTH_MINUS) + "2", getText(LAST_YEAR_END_OF_MONTH_PLUS) + "2", getText(LAST_YEAR_LAST_UPDATED_DATE_MINUS) + "3");
        Map<String, String> params = presenter.getReportParameters();
        presenter.editScheduleData(params, true, null);
        validateRollingDatesInCaseOfEdit(START_OF_MONTH + "-2", END_OF_MONTH + "+2", LAST_UPDATED + "-3",
                LY_START_OF_MONTH + "-2", LY_END_OF_MONTH + "+2", LY_LAST_UPDATED + "-3");
        validateDataElementsInEditCase(BookingSituationType.TOTAL_TRANSIENT);
        BookingSituationFilterDTO filterDTO = presenter.getFilterDTO();
        JavaDateSelectorBean comparisionDateSelectorBean = filterDTO.getComparisionDateSelectorBean();
        Set<RollingDateOptionBean> startSet = comparisionDateSelectorBean.getStartRollingDateOptions();
        assertEquals(3, startSet.size());
        Set<RollingDateOptionBean> endSet = comparisionDateSelectorBean.getEndRollingDateOptions();
        assertEquals(3, endSet.size());
        assertNotEquals(startSet, endSet);
    }

    @Test
    public void editScheduleDataWhenGroupIsSelectedWithDateOption() {
        populateData(BookingSituationType.TOTAL_GROUP);
        setRollingDates(getText(SYSTEM_DATE), getText(SYSTEM_DATE), getText(LAST_UPDATED_DATE),
                getText(LAST_YEAR_THIS_DAY), getText(LAST_YEAR_THIS_DAY), getText(LAST_YEAR_LAST_UPDATED_DATE));
        Map<String, String> params = presenter.getReportParameters();
        presenter.editScheduleData(params, true, null);

        validateDataElementsInEditCase(BookingSituationType.TOTAL_GROUP);
    }

    @Test
    public void editScheduleDataWhenForecastGroupIsSelectedWithDateOption() {
        presenter.resetFilterData();
        BookingSituationFilterDTO filterDTO = presenter.getFilterDTO();
        filterDTO.setBookingSituationType(BookingSituationType.FORECAST_GROUPS);
        List<ForecastGroupSummary> fgList = getForeCastSummaries(4);
        fgList.remove(2);
        Set<ForecastGroupSummary> fgSummarySet = new HashSet<>(fgList);
        filterDTO.setForecastGroup(fgSummarySet);

        setRollingDates(getText(SYSTEM_DATE), getText(SYSTEM_DATE), getText(LAST_UPDATED_DATE),
                getText(LAST_YEAR_THIS_DAY), getText(LAST_YEAR_THIS_DAY), getText(LAST_YEAR_LAST_UPDATED_DATE));
        Map<String, String> params = presenter.getReportParameters();
        presenter.editScheduleData(params, true, null);
        filterDTO = presenter.getFilterDTO();
        assertEquals(BookingSituationType.FORECAST_GROUPS, filterDTO.getBookingSituationType());
        assertEquals(3, filterDTO.getForecastGroup().size());
        assertTrue(filterDTO.getForecastGroup().contains(fgList.get(0)));
        assertTrue(filterDTO.getForecastGroup().contains(fgList.get(1)));
        assertTrue(filterDTO.getForecastGroup().contains(fgList.get(2)));
        assertNull(filterDTO.getDataElements());
        assertNull(filterDTO.getBusinessView());
    }

    @Test
    public void editScheduleDataWhenBusinessViewIsSelectedWithDateOption() {
        presenter.resetFilterData();
        BookingSituationFilterDTO filterDTO = presenter.getFilterDTO();
        filterDTO.setBookingSituationType(BookingSituationType.BUSINESS_VIEW);
        List<BusinessGroup> bgList = getBusinessViews(4);
        bgList.remove(2);
        Set<BusinessGroup> bvSummarySet = new HashSet<>(bgList);
        filterDTO.setBusinessView(bvSummarySet);

        setRollingDates(getText(SYSTEM_DATE), getText(SYSTEM_DATE), getText(LAST_UPDATED_DATE),
                getText(LAST_YEAR_THIS_DAY), getText(LAST_YEAR_THIS_DAY), getText(LAST_YEAR_LAST_UPDATED_DATE));
        Map<String, String> params = presenter.getReportParameters();
        presenter.editScheduleData(params, true, null);
        filterDTO = presenter.getFilterDTO();
        assertEquals(BookingSituationType.BUSINESS_VIEW, filterDTO.getBookingSituationType());
        assertEquals(3, filterDTO.getBusinessView().size());
        assertTrue(filterDTO.getBusinessView().contains(bgList.get(0)));
        assertTrue(filterDTO.getBusinessView().contains(bgList.get(1)));
        assertTrue(filterDTO.getBusinessView().contains(bgList.get(2)));
        assertNull(filterDTO.getDataElements());
        assertNull(filterDTO.getForecastGroup());
    }

    private void validateRollingDatesInCaseOfEdit(String anaStDt, String anaEndDt, String anaAsOfStDt, String compStDt, String compEndDt, String compAsOfStDt) {
        BookingSituationFilterDTO filterDTO = presenter.getFilterDTO();

        JavaDateSelectorBean analysisDateSelectorBean = filterDTO.getAnalysisDateSelectorBean();
        JavaDateSelectorBean analysisAsOfDateSelectorBean = filterDTO.getAnalysisAsOfDateSelectorBean();
        JavaDateSelectorBean comparisionDateSelectorBean = filterDTO.getComparisionDateSelectorBean();
        JavaDateSelectorBean comparisionAsOfDateSelectorBean = filterDTO.getComparisionAsOfDateSelectorBean();

        assertEquals(anaStDt, analysisDateSelectorBean.getRollingStartDate());
        assertEquals(anaEndDt, analysisDateSelectorBean.getRollingEndDate());
        assertEquals(anaAsOfStDt, analysisAsOfDateSelectorBean.getRollingStartDate());
        assertEquals(compStDt, comparisionDateSelectorBean.getRollingStartDate());
        assertEquals(compEndDt, comparisionDateSelectorBean.getRollingEndDate());
        assertEquals(compAsOfStDt, comparisionAsOfDateSelectorBean.getRollingStartDate());
    }

    @Test
    public void getReportViewByListLabel() {
        presenter.resetFilterData();
        BookingSituationFilterDTO filterDTO = presenter.getFilterDTO();
        filterDTO.setBookingSituationType(BookingSituationType.TOTAL_PROPERTY);
        presenter.getReportViewByListLabel();
        verify(lang).getText("dataElements");

        filterDTO.setBookingSituationType(BookingSituationType.FORECAST_GROUPS);
        presenter.getReportViewByListLabel();
        verify(lang).getText("common.groups");

        filterDTO.setBookingSituationType(BookingSituationType.BUSINESS_VIEW);
        presenter.getReportViewByListLabel();
        verify(lang).getText("business.views");
    }

    @Test
    public void dateChangeSpecificToSpecific() {
        presenter.resetFilterData();
        BookingSituationFilterDTO filterDTO = presenter.getFilterDTO();
        JavaDateSelectorBean anaDateBean = filterDTO.getAnalysisDateSelectorBean();
        JavaDateSelectorBean anaAsOfDateBean = filterDTO.getAnalysisAsOfDateSelectorBean();
        JavaDateSelectorBean compDateBean = filterDTO.getComparisionDateSelectorBean();
        JavaDateSelectorBean compAsOfDateBean = filterDTO.getComparisionAsOfDateSelectorBean();

        List<String> expectedDates = new ArrayList<>(Arrays.asList(anaDateBean.getSpecificStartDate().minusDays(367).toString(), anaDateBean.getSpecificEndDate().toString(),
                anaAsOfDateBean.getSpecificStartDate().minusDays(367).toString(), anaDateBean.getSpecificStartDate().minusDays(367).toString(),
                anaDateBean.getSpecificEndDate().toString(), anaAsOfDateBean.getSpecificStartDate().minusDays(367).toString()));
        anaDateBean.setStartDate(DateFormatUtil.formatDateFor(uiContext.getSystemCaughtUpDateAsJavaLocalDate().minusDays(367), UiUtils.getLocale()));
        presenter.notifyAnalysisDateSelectorChange(filterDTO, anaDateBean);
        assertEquals(expectedDates, getActualDatesSpecific());

        expectedDates = new ArrayList<>(Arrays.asList(anaDateBean.getSpecificStartDate().toString(), anaDateBean.getSpecificEndDate().minusDays(2).toString(),
                anaAsOfDateBean.getSpecificStartDate().toString(), compDateBean.getSpecificStartDate().toString(),
                compDateBean.getSpecificEndDate().minusDays(2).toString(), compAsOfDateBean.getSpecificStartDate().toString()));
        anaDateBean.setEndDate(DateFormatUtil.formatDateFor(anaDateBean.getSpecificEndDate().minusDays(2), UiUtils.getLocale()));
        presenter.notifyAnalysisDateSelectorChange(filterDTO, anaDateBean);
        assertEquals(expectedDates, getActualDatesSpecific());

        expectedDates = new ArrayList<>(Arrays.asList(anaDateBean.getSpecificStartDate().toString(), anaDateBean.getSpecificEndDate().toString(),
                anaAsOfDateBean.getSpecificStartDate().minusDays(4).toString(), compDateBean.getSpecificStartDate().toString(),
                compDateBean.getSpecificEndDate().toString(), compAsOfDateBean.getSpecificStartDate().toString()));
        anaAsOfDateBean.setStartDate(DateFormatUtil.formatDateFor(anaAsOfDateBean.getSpecificStartDate().minusDays(4), UiUtils.getLocale()));
        presenter.notifyAnalysisDateSelectorChange(filterDTO, anaAsOfDateBean);
        assertEquals(expectedDates, getActualDatesSpecific());

        expectedDates = new ArrayList<>(Arrays.asList(anaDateBean.getSpecificStartDate().toString(), anaDateBean.getSpecificEndDate().toString(),
                anaAsOfDateBean.getSpecificStartDate().toString(), compDateBean.getSpecificStartDate().minusDays(6).toString(),
                compDateBean.getSpecificEndDate().minusDays(6).toString(), compAsOfDateBean.getSpecificStartDate().minusDays(6).toString()));
        compDateBean.setStartDate(DateFormatUtil.formatDateFor(compDateBean.getSpecificStartDate().minusDays(6), UiUtils.getLocale()));
        presenter.notifyComparisionDateSelectorChange(filterDTO, compDateBean);
        assertEquals(expectedDates, getActualDatesSpecific());

        expectedDates = new ArrayList<>(Arrays.asList(anaDateBean.getSpecificStartDate().toString(), anaDateBean.getSpecificEndDate().toString(),
                anaAsOfDateBean.getSpecificStartDate().toString(), compDateBean.getSpecificStartDate().toString(),
                compDateBean.getSpecificEndDate().toString(), compAsOfDateBean.getSpecificStartDate().plusDays(6).toString()));
        compAsOfDateBean.setStartDate(DateFormatUtil.formatDateFor(compAsOfDateBean.getSpecificStartDate().plusDays(6), UiUtils.getLocale()));
        presenter.notifyComparisionDateSelectorChange(filterDTO, compAsOfDateBean);
        assertEquals(expectedDates, getActualDatesSpecific());
    }

    private List<String> getActualDatesSpecific() {
        BookingSituationFilterDTO filterDTO = presenter.getFilterDTO();
        JavaDateSelectorBean anaDateBean = filterDTO.getAnalysisDateSelectorBean();
        JavaDateSelectorBean anaAsOfDateBean = filterDTO.getAnalysisAsOfDateSelectorBean();
        JavaDateSelectorBean compDateBean = filterDTO.getComparisionDateSelectorBean();
        JavaDateSelectorBean compAsOfDateBean = filterDTO.getComparisionAsOfDateSelectorBean();

        return new ArrayList<>(Arrays.asList(anaDateBean.getSpecificStartDate().toString(), anaDateBean.getSpecificEndDate().toString(),
                anaAsOfDateBean.getSpecificStartDate().toString(), compDateBean.getSpecificStartDate().toString(),
                compDateBean.getSpecificEndDate().toString(), compAsOfDateBean.getSpecificStartDate().toString()));
    }

    private List<String> getActualDatesRolling() {
        BookingSituationFilterDTO filterDTO = presenter.getFilterDTO();
        JavaDateSelectorBean anaDateBean = filterDTO.getAnalysisDateSelectorBean();
        JavaDateSelectorBean anaAsOfDateBean = filterDTO.getAnalysisAsOfDateSelectorBean();
        JavaDateSelectorBean compDateBean = filterDTO.getComparisionDateSelectorBean();
        JavaDateSelectorBean compAsOfDateBean = filterDTO.getComparisionAsOfDateSelectorBean();

        return new ArrayList<>(Arrays.asList(anaDateBean.getRollingStartDate(), anaDateBean.getRollingEndDate(),
                anaAsOfDateBean.getRollingStartDate(), compDateBean.getRollingStartDate(),
                compDateBean.getRollingEndDate(), compAsOfDateBean.getRollingStartDate()));
    }

    @Test
    public void dateChangeRollingToSpecificForAnalysisDate() {
        presenter.resetFilterData();
        BookingSituationFilterDTO filterDTO = presenter.getFilterDTO();
        JavaDateSelectorBean anaDateBean = filterDTO.getAnalysisDateSelectorBean();
        List<String> expectedDates = new ArrayList<>(Arrays.asList(anaDateBean.getSpecificStartDate().toString(), anaDateBean.getSpecificStartDate().plusDays(7).toString(),
                anaDateBean.getSpecificStartDate().minusDays(30).toString(), anaDateBean.getSpecificStartDate().minusWeeks(52).toString(),
                anaDateBean.getSpecificStartDate().minusWeeks(52).plusDays(7).toString(), anaDateBean.getSpecificStartDate().minusWeeks(52).minusDays(30).toString()));
        anaDateBean.setRollingDateString(getText(SYSTEM_DATE_MINUS) + "2", getText(SYSTEM_DATE_MINUS) + "2");
        presenter.notifyAnalysisDateSelectorChange(filterDTO, anaDateBean);

        anaDateBean.setRollingDate(false);
        anaDateBean.setSpecificDateWith(7L, uiContext.getSystemCaughtUpDateAsJavaLocalDate());
        presenter.notifyAnalysisDateSelectorChange(filterDTO, anaDateBean);
        assertEquals(expectedDates, getActualDatesSpecific());
    }

    @Test
    public void dateChangeRollingToSpecificForAnaAsOfDate() {
        presenter.resetFilterData();
        BookingSituationFilterDTO filterDTO = presenter.getFilterDTO();
        JavaDateSelectorBean anaDateBean = filterDTO.getAnalysisDateSelectorBean();
        JavaDateSelectorBean anaAsOfDateBean = filterDTO.getAnalysisAsOfDateSelectorBean();
        List<String> expectedDates = new ArrayList<>(Arrays.asList(anaDateBean.getSpecificStartDate().toString(), anaDateBean.getSpecificStartDate().plusDays(7).toString(),
                anaDateBean.getSpecificStartDate().minusDays(35).toString(), anaDateBean.getSpecificStartDate().minusWeeks(52).toString(),
                anaDateBean.getSpecificStartDate().minusWeeks(52).plusDays(7).toString(), anaDateBean.getSpecificStartDate().minusWeeks(52).minusDays(30).toString()));
        anaDateBean.setRollingDateString(getText(SYSTEM_DATE_MINUS) + "2", getText(SYSTEM_DATE_MINUS) + "2");
        presenter.notifyAnalysisDateSelectorChange(filterDTO, anaDateBean);

        anaAsOfDateBean.setRollingDate(false);
        anaAsOfDateBean.setSpecificDateWith(7L, uiContext.getSystemCaughtUpDateAsJavaLocalDate().minusDays(35));
        presenter.notifyAnalysisDateSelectorChange(filterDTO, anaAsOfDateBean);
        assertEquals(expectedDates, getActualDatesSpecific());
    }

    @Test
    public void dateChangeRollingToRollingForCompDate() {
        presenter.resetFilterData();
        BookingSituationFilterDTO filterDTO = presenter.getFilterDTO();
        JavaDateSelectorBean anaDateBean = filterDTO.getAnalysisDateSelectorBean();
        JavaDateSelectorBean compDateBean = filterDTO.getComparisionDateSelectorBean();

        anaDateBean.setRollingDateString(getText(SYSTEM_DATE_MINUS) + "10", getText(SYSTEM_DATE_PLUS) + "2");
        presenter.notifyAnalysisDateSelectorChange(filterDTO, anaDateBean);

        List<String> expectedDates = new ArrayList<>(Arrays.asList("TODAY-10", "TODAY+2", "LAST_UPDATED-11", "LY_THIS_DAY-15", "LY_THIS_DAY-3", "LY_LAST_UPDATED-16"));
        compDateBean.setRollingDateString(getText(LAST_YEAR_THIS_DAY_MINUS) + "15");
        presenter.notifyComparisionDateSelectorChange(filterDTO, compDateBean);
        assertEquals(expectedDates, getActualDatesRolling());

        expectedDates = new ArrayList<>(Arrays.asList("TODAY-10", "TODAY+2", "LAST_UPDATED-11", "LY_THIS_DAY-10", "LY_THIS_DAY+2", "LY_LAST_UPDATED-11"));
        compDateBean.setRollingDateString(getText(LAST_YEAR_THIS_DAY_MINUS) + "10");
        presenter.notifyComparisionDateSelectorChange(filterDTO, compDateBean);
        assertEquals(expectedDates, getActualDatesRolling());

        expectedDates = new ArrayList<>(Arrays.asList("TODAY-10", "TODAY+2", "LAST_UPDATED-11", "LY_THIS_DAY-5", "LY_THIS_DAY+7", "LY_LAST_UPDATED-6"));
        compDateBean.setRollingDateString(getText(LAST_YEAR_THIS_DAY_MINUS) + "5");
        presenter.notifyComparisionDateSelectorChange(filterDTO, compDateBean);
        assertEquals(expectedDates, getActualDatesRolling());

        expectedDates = new ArrayList<>(Arrays.asList("TODAY-10", "TODAY+2", "LAST_UPDATED-11", "LY_THIS_DAY", "LY_THIS_DAY+12", "LY_LAST_UPDATED"));
        compDateBean.setRollingDateString(getText(LAST_YEAR_THIS_DAY));
        presenter.notifyComparisionDateSelectorChange(filterDTO, compDateBean);
        assertEquals(expectedDates, getActualDatesRolling());

        expectedDates = new ArrayList<>(Arrays.asList("TODAY-10", "TODAY+2", "LAST_UPDATED-11", "LY_THIS_DAY+2", "LY_THIS_DAY+14", "LY_LAST_UPDATED"));
        compDateBean.setRollingDateString(getText(LAST_YEAR_THIS_DAY_PLUS) + "2");
        presenter.notifyComparisionDateSelectorChange(filterDTO, compDateBean);
        assertEquals(expectedDates, getActualDatesRolling());

    }

    @Test
    public void dateChangeRollingToRollingForCompDateMonthOption() {
        presenter.resetFilterData();
        BookingSituationFilterDTO filterDTO = presenter.getFilterDTO();
        JavaDateSelectorBean anaDateBean = filterDTO.getAnalysisDateSelectorBean();
        JavaDateSelectorBean compDateBean = filterDTO.getComparisionDateSelectorBean();

        anaDateBean.setRollingDateString(getText(START_OF_MONTH_MINUS) + "2", getText(END_OF_MONTH_PLUS) + "2");
        presenter.notifyAnalysisDateSelectorChange(filterDTO, anaDateBean);

        List<String> expectedDates = new ArrayList<>(Arrays.asList("START_OF_MONTH-2", "END_OF_MONTH+2", "LAST_UPDATED", "LY_START_OF_MONTH-5", "LY_END_OF_MONTH-1", "LY_LAST_UPDATED"));
        compDateBean.setRollingDateString(getText(LAST_YEAR_START_OF_MONTH_MINUS) + "5");
        presenter.notifyComparisionDateSelectorChange(filterDTO, compDateBean);
        assertEquals(expectedDates, getActualDatesRolling());

        expectedDates = new ArrayList<>(Arrays.asList("START_OF_MONTH-2", "END_OF_MONTH+2", "LAST_UPDATED", "LY_START_OF_MONTH-3", "LY_END_OF_MONTH+1", "LY_LAST_UPDATED"));
        compDateBean.setRollingDateString(getText(LAST_YEAR_START_OF_MONTH_MINUS) + "3");
        presenter.notifyComparisionDateSelectorChange(filterDTO, compDateBean);
        assertEquals(expectedDates, getActualDatesRolling());

        expectedDates = new ArrayList<>(Arrays.asList("START_OF_MONTH-2", "END_OF_MONTH+2", "LAST_UPDATED", "LY_START_OF_MONTH", "LY_END_OF_MONTH+4", "LY_LAST_UPDATED"));
        compDateBean.setRollingDateString(getText(LAST_YEAR_START_OF_MONTH));
        presenter.notifyComparisionDateSelectorChange(filterDTO, compDateBean);
        assertEquals(expectedDates, getActualDatesRolling());

        expectedDates = new ArrayList<>(Arrays.asList("START_OF_MONTH-2", "END_OF_MONTH+2", "LAST_UPDATED", "LY_START_OF_MONTH+2", "LY_END_OF_MONTH+6", "LY_LAST_UPDATED"));
        compDateBean.setRollingDateString(getText(LAST_YEAR_START_OF_MONTH_PLUS) + "2");
        presenter.notifyComparisionDateSelectorChange(filterDTO, compDateBean);
        assertEquals(expectedDates, getActualDatesRolling());
    }

    @Test
    public void dateChangeRollingToSpecificForCompDate() {
        presenter.resetFilterData();
        BookingSituationFilterDTO filterDTO = presenter.getFilterDTO();
        JavaDateSelectorBean anaDateBean = filterDTO.getAnalysisDateSelectorBean();
        JavaDateSelectorBean compDateBean = filterDTO.getComparisionDateSelectorBean();
        List<String> expectedDates = new ArrayList<>(Arrays.asList(anaDateBean.getSpecificStartDate().toString(), anaDateBean.getSpecificStartDate().plusDays(7).toString(),
                anaDateBean.getSpecificStartDate().minusDays(30).toString(), anaDateBean.getSpecificStartDate().minusWeeks(52).toString(),
                anaDateBean.getSpecificStartDate().minusWeeks(52).plusDays(7).toString(), anaDateBean.getSpecificStartDate().minusWeeks(52).minusDays(30).toString()));
        anaDateBean.setRollingDateString(getText(SYSTEM_DATE_MINUS) + "2", getText(SYSTEM_DATE_MINUS) + "2");
        presenter.notifyAnalysisDateSelectorChange(filterDTO, anaDateBean);

        compDateBean.setRollingDate(false);
        compDateBean.setSpecificDateWith(7L, uiContext.getSystemCaughtUpDateAsJavaLocalDate().minusWeeks(52));
        presenter.notifyComparisionDateSelectorChange(filterDTO, compDateBean);
        assertEquals(expectedDates, getActualDatesSpecific());
    }

    @Test
    public void dateChangeRollingToSpecificForCompAsOfDate() {
        presenter.resetFilterData();
        BookingSituationFilterDTO filterDTO = presenter.getFilterDTO();
        JavaDateSelectorBean anaDateBean = filterDTO.getAnalysisDateSelectorBean();
        JavaDateSelectorBean compAsOfDateBean = filterDTO.getComparisionAsOfDateSelectorBean();
        List<String> expectedDates = new ArrayList<>(Arrays.asList(anaDateBean.getSpecificStartDate().toString(), anaDateBean.getSpecificStartDate().plusDays(7).toString(),
                anaDateBean.getSpecificStartDate().minusDays(30).toString(), anaDateBean.getSpecificStartDate().minusWeeks(52).toString(),
                anaDateBean.getSpecificStartDate().minusWeeks(52).plusDays(7).toString(), anaDateBean.getSpecificStartDate().minusWeeks(52).minusDays(35).toString()));
        anaDateBean.setRollingDateString(getText(SYSTEM_DATE_MINUS) + "2", getText(SYSTEM_DATE_MINUS) + "2");
        presenter.notifyAnalysisDateSelectorChange(filterDTO, anaDateBean);

        compAsOfDateBean.setRollingDate(false);
        compAsOfDateBean.setSpecificDateWith(7L, uiContext.getSystemCaughtUpDateAsJavaLocalDate().minusWeeks(52).minusDays(35));
        presenter.notifyComparisionDateSelectorChange(filterDTO, compAsOfDateBean);
        assertEquals(expectedDates, getActualDatesSpecific());
    }

    @Test
    public void dateChangeRollingForDateOption() {
        List<String> expectedDates = new ArrayList<>(Arrays.asList("TODAY-2", "TODAY-2", "LAST_UPDATED-3", "LY_THIS_DAY-2", "LY_THIS_DAY-2", "LY_LAST_UPDATED-3"));
        presenter.resetFilterData();
        BookingSituationFilterDTO filterDTO = presenter.getFilterDTO();
        JavaDateSelectorBean anaDateBean = filterDTO.getAnalysisDateSelectorBean();
        anaDateBean.setRollingDateString(getText(SYSTEM_DATE_MINUS) + "2");
        presenter.notifyAnalysisDateSelectorChange(filterDTO, anaDateBean);
        assertEquals(expectedDates, getActualDatesRolling());

        presenter.resetFilterData();
        filterDTO = presenter.getFilterDTO();
        anaDateBean = filterDTO.getAnalysisDateSelectorBean();
        expectedDates = new ArrayList<>(Arrays.asList("TODAY", "TODAY", "LAST_UPDATED", "LY_THIS_DAY", "LY_THIS_DAY", "LY_LAST_UPDATED"));
        anaDateBean.setRollingDateString(getText(SYSTEM_DATE));
        presenter.notifyAnalysisDateSelectorChange(filterDTO, anaDateBean);
        assertEquals(expectedDates, getActualDatesRolling());

        presenter.resetFilterData();
        filterDTO = presenter.getFilterDTO();
        anaDateBean = filterDTO.getAnalysisDateSelectorBean();
        expectedDates = new ArrayList<>(Arrays.asList("TODAY+2", "TODAY+2", "LAST_UPDATED", "LY_THIS_DAY+2", "LY_THIS_DAY+2", "LY_LAST_UPDATED"));
        anaDateBean.setRollingDateString(getText(SYSTEM_DATE_PLUS) + "2");
        presenter.notifyAnalysisDateSelectorChange(filterDTO, anaDateBean);
        assertEquals(expectedDates, getActualDatesRolling());

        presenter.resetFilterData();
        filterDTO = presenter.getFilterDTO();
        JavaDateSelectorBean anaAsOfDateBean = filterDTO.getAnalysisAsOfDateSelectorBean();
        expectedDates = new ArrayList<>(Arrays.asList("TODAY", "TODAY", "LAST_UPDATED-4", "LY_THIS_DAY", "LY_THIS_DAY", "LY_LAST_UPDATED"));
        anaAsOfDateBean.setRollingDateString(getText(LAST_UPDATED_DATE_MINUS) + "4");
        presenter.notifyAnalysisDateSelectorChange(filterDTO, anaAsOfDateBean);
        assertEquals(expectedDates, getActualDatesRolling());

        presenter.resetFilterData();
        filterDTO = presenter.getFilterDTO();
        JavaDateSelectorBean compBean = filterDTO.getComparisionDateSelectorBean();
        expectedDates = new ArrayList<>(Arrays.asList("TODAY", "TODAY", "LAST_UPDATED", "LY_THIS_DAY-2", "LY_THIS_DAY-2", "LY_LAST_UPDATED-3"));
        compBean.setRollingDateString(getText(LAST_YEAR_THIS_DAY_MINUS) + "2");
        presenter.notifyComparisionDateSelectorChange(filterDTO, compBean);
        assertEquals(expectedDates, getActualDatesRolling());

        presenter.resetFilterData();
        filterDTO = presenter.getFilterDTO();
        compBean = filterDTO.getComparisionDateSelectorBean();
        expectedDates = new ArrayList<>(Arrays.asList("TODAY", "TODAY", "LAST_UPDATED", "LY_THIS_DAY+2", "LY_THIS_DAY+2", "LY_LAST_UPDATED"));
        compBean.setRollingDateString(getText(LAST_YEAR_THIS_DAY_PLUS) + "2");
        presenter.notifyComparisionDateSelectorChange(filterDTO, compBean);
        assertEquals(expectedDates, getActualDatesRolling());

        presenter.resetFilterData();
        filterDTO = presenter.getFilterDTO();
        JavaDateSelectorBean compAsOfBean = filterDTO.getComparisionAsOfDateSelectorBean();
        expectedDates = new ArrayList<>(Arrays.asList("TODAY", "TODAY", "LAST_UPDATED", "LY_THIS_DAY", "LY_THIS_DAY", "LY_LAST_UPDATED-2"));
        compAsOfBean.setRollingDateString(getText(LAST_YEAR_LAST_UPDATED_DATE_MINUS) + "2");
        presenter.notifyComparisionDateSelectorChange(filterDTO, compAsOfBean);
        assertEquals(expectedDates, getActualDatesRolling());
    }

    @Test
    public void dateChangeRollingForMonthOption() {
        presenter.resetFilterData();
        BookingSituationFilterDTO filterDTO = presenter.getFilterDTO();
        JavaDateSelectorBean anaDateBean = filterDTO.getAnalysisDateSelectorBean();
        List<String> expectedDates = new ArrayList<>(Arrays.asList("START_OF_MONTH-2", "END_OF_MONTH-2", "LAST_UPDATED", "LY_START_OF_MONTH-2", "LY_END_OF_MONTH-2", "LY_LAST_UPDATED"));
        anaDateBean.setRollingDateString(getText(START_OF_MONTH_MINUS) + "2", getText(END_OF_MONTH_MINUS) + "2");
        presenter.notifyAnalysisDateSelectorChange(filterDTO, anaDateBean);
        assertEquals(expectedDates, getActualDatesRolling());

        presenter.resetFilterData();
        filterDTO = presenter.getFilterDTO();
        anaDateBean = filterDTO.getAnalysisDateSelectorBean();
        expectedDates = new ArrayList<>(Arrays.asList("START_OF_MONTH", "END_OF_MONTH", "LAST_UPDATED", "LY_START_OF_MONTH", "LY_END_OF_MONTH", "LY_LAST_UPDATED"));
        anaDateBean.setRollingDateString(getText(LocalizationKeyConstants.START_OF_MONTH), getText(LocalizationKeyConstants.END_OF_MONTH));
        presenter.notifyAnalysisDateSelectorChange(filterDTO, anaDateBean);
        assertEquals(expectedDates, getActualDatesRolling());

        presenter.resetFilterData();
        filterDTO = presenter.getFilterDTO();
        anaDateBean = filterDTO.getAnalysisDateSelectorBean();
        expectedDates = new ArrayList<>(Arrays.asList("START_OF_MONTH+2", "END_OF_MONTH+2", "LAST_UPDATED", "LY_START_OF_MONTH+2", "LY_END_OF_MONTH+2", "LY_LAST_UPDATED"));
        anaDateBean.setRollingDateString(getText(START_OF_MONTH_PLUS) + "2", getText(END_OF_MONTH_PLUS) + "2");
        presenter.notifyAnalysisDateSelectorChange(filterDTO, anaDateBean);
        assertEquals(expectedDates, getActualDatesRolling());
    }
}