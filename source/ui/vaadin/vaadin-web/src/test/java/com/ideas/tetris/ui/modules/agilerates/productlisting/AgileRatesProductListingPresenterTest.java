package com.ideas.tetris.ui.modules.agilerates.productlisting;

import com.google.common.collect.Sets;
import com.ideas.infra.tetris.security.domain.TetrisPermissionKey;
import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.configparams.IntegrationConfigParamName;
import com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.agilerates.configuration.dto.AgileRatesProductConfigurationDTO;
import com.ideas.tetris.pacman.services.agilerates.configuration.dto.AgileRatesProductTypeEnum;
import com.ideas.tetris.pacman.services.agilerates.configuration.dto.IndependentProductConfigurationDTO;
import com.ideas.tetris.pacman.services.agilerates.configuration.entity.AgileRatesOffsetMethod;
import com.ideas.tetris.pacman.services.agilerates.configuration.entity.AgileRatesProductGroup;
import com.ideas.tetris.pacman.services.agilerates.configuration.entity.ProductGroup;
import com.ideas.tetris.pacman.services.agilerates.configuration.entity.ProductHierarchy;
import com.ideas.tetris.pacman.services.agilerates.configuration.service.AgileRatesConfigurationService;
import com.ideas.tetris.pacman.services.automateconfiguration.service.AutomateConfigurationService;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.TenantStatusEnum;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.pricingconfiguration.service.PricingConfigurationService;
import com.ideas.tetris.pacman.services.product.InvalidReason;
import com.ideas.tetris.pacman.services.product.Product;
import com.ideas.tetris.pacman.services.product.ProductCode;
import com.ideas.tetris.pacman.services.roomtyperecoding.services.RoomTypeRecodingService;
import com.ideas.tetris.pacman.services.webrate.service.AccommodationMappingService;
import com.ideas.tetris.platform.common.configparams.entities.ConfigParameter;
import com.ideas.tetris.platform.common.configparams.entities.ConfigParameterValue;
import com.ideas.tetris.platform.common.contextholder.PacmanThreadLocalContextHolder;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.common.license.LicenseService;
import com.ideas.tetris.platform.common.time.JavaLocalDateUtils;
import com.ideas.tetris.platform.common.xml.schema.workcontext.v1.WorkContextType;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import com.ideas.tetris.ui.VaadinUIBaseJupiterTest;
import com.ideas.tetris.ui.common.cdi.cdiutils.Lang;
import com.ideas.tetris.ui.common.security.PropertyState;
import com.ideas.tetris.ui.common.security.UiContext;
import com.ideas.tetris.ui.modules.agilerates.AgileRatesPresenter;
import com.ideas.tetris.ui.modules.agilerates.productlisting.views.AgileRatesProductListingView;
import com.ideas.tetris.ui.modules.agilerates.smallgroup.SmallGroupWizardPresenter;
import com.ideas.tetris.ui.shell.sync.ForceSyncEvent;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.time.LocalDate;
import java.util.*;
import java.util.function.Consumer;

import static com.ideas.tetris.pacman.common.constants.Constants.ADVANCED_PRICING_CONFIGURATION;
import static com.ideas.tetris.pacman.services.product.Product.GROUP_PRODUCT_CODE;
import static com.ideas.tetris.ui.common.component.fontawesome.TetrisFontAwesome.TARGET_SMALL;
import static com.ideas.tetris.ui.common.component.fontawesome.TetrisFontAwesome.USERS;
import static com.ideas.tetris.ui.common.component.fontawesome.TetrisFontAwesome.LINK;
import static com.ideas.tetris.ui.modules.agilerates.AgileRatesTestUtils.verifyPresenterMethodCalled;
import static com.ideas.tetris.ui.modules.agilerates.AgileRatesTestUtils.verifySync;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class AgileRatesProductListingPresenterTest extends VaadinUIBaseJupiterTest {

    @Mock
    Lang lang;

    @Mock
    AgileRatesPresenter agileRatesPresenter;

    @Mock
    private AgileRatesConfigurationService agileRatesConfigurationService;

    @Mock
    private AccommodationMappingService accommodationMappingService;

    @Mock
    AgileRatesProductListingView view;

    @Mock
    PropertyState propertyState;

    @Mock
    PacmanConfigParamsService pacmanConfigParamsService;

    @Mock
    protected UiContext uiContext;

    @Mock
    private RoomTypeRecodingService roomTypeRecodingService;

    @Mock
    private DateService dateService;

    @Mock
    javax.enterprise.event.Event<ForceSyncEvent> forceSyncEvent;

    @Mock
    private PricingConfigurationService pricingConfigurationService;

    @Mock
    private AutomateConfigurationService automateConfigurationService;

    @Mock
    private SmallGroupWizardPresenter smallGroupWizardPresenter;
    @Mock
    private LicenseService licenseService;
    @InjectMocks
    AgileRatesProductListingPresenter presenter;

    @Captor
    ArgumentCaptor<List<Product>> productArgumentCaptor;

    private org.joda.time.LocalDate localDate;

    @BeforeEach
    public void setUp() throws Exception {
        localDate = new org.joda.time.LocalDate();
        when(uiContext.getSystemCaughtUpDateAsLocalDate()).thenReturn(localDate);
    }

    @Test
    public void init() {
        List<Product> products = new ArrayList<>();
        when(agileRatesConfigurationService.findAgileRatesProducts()).thenReturn(products);
        doNothing().when(view).loadProducts(products);

        presenter.init();

        verify(pricingConfigurationService).isBaseRoomTypeConfigurationComplete();
    }

    @Test
    public void isReadMissingRoomTrueWriteAccessFalse() {
        when(roomTypeRecodingService.isExistingMissingRoomTypeAlertOpen()).thenReturn(true);
        when(presenter.hasWriteAccess(TetrisPermissionKey.AGILE_RATES_CONFIGURATION)).thenReturn(false);
        presenter.onViewOpened(null);

        verify(view).setPermissions(true);
    }

    @Test
    public void isReadMissingRoomFalseIsEnabledFalse() {
        when(uiContext.getPropertyState()).thenReturn(propertyState);
        when(propertyState.isEffectiveReadOnly()).thenReturn(true);

        when(roomTypeRecodingService.isExistingMissingRoomTypeAlertOpen()).thenReturn(false);
        when(presenter.isEnabledPerRequirements(false, TetrisPermissionKey.AGILE_RATES_CONFIGURATION)).thenReturn(false);
        presenter.onViewOpened(null);

        verify(view).setPermissions(true);
    }

    @Test
    public void goodRunWhenBothAgileRatesValid() {

        when(uiContext.getPropertyState()).thenReturn(propertyState);
        when(propertyState.isEffectiveReadOnly()).thenReturn(true);

        when(pacmanConfigParamsService.getIntegerParameterValue(FeatureTogglesConfigParamName.MAX_ACTIVE_AGILE_RATES.value())).thenReturn(1);
        when(pacmanConfigParamsService.getIntegerParameterValue(FeatureTogglesConfigParamName.MAX_UPLOADED_AGILE_RATES.value())).thenReturn(1);

        presenter.onViewOpened(null);

        verify(view).setPermissions(true);
    }

    @Test
    public void testMaxAgileRatesProductGroups() {
        when(uiContext.getPropertyState()).thenReturn(propertyState);
        when(propertyState.isEffectiveReadOnly()).thenReturn(true);

        when(pacmanConfigParamsService.getIntegerParameterValue(FeatureTogglesConfigParamName.MAX_AGILE_RATES_PRODUCT_GROUPS.value())).thenReturn(1);
        presenter.onViewOpened(null);
        int result = presenter.getMaxAgileRatesProductGroups();
        assertEquals(1, result);

        when(pacmanConfigParamsService.getIntegerParameterValue(FeatureTogglesConfigParamName.MAX_AGILE_RATES_PRODUCT_GROUPS.value())).thenReturn(-25);
        presenter.onViewOpened(null);
        result = presenter.getMaxAgileRatesProductGroups();
        assertEquals(1, result);
    }

    @Test
    public void badRunWhenActiveAgileRateValidUploadAgileInvalid() {
        when(uiContext.getPropertyState()).thenReturn(propertyState);
        when(propertyState.isEffectiveReadOnly()).thenReturn(true);

        when(pacmanConfigParamsService.getIntegerParameterValue(FeatureTogglesConfigParamName.MAX_ACTIVE_AGILE_RATES.value())).thenReturn(1);
        try {
            presenter.onViewOpened(null);
        } catch (TetrisException MaxActiveException) {
            fail();
        }
        when(pacmanConfigParamsService.getIntegerParameterValue(FeatureTogglesConfigParamName.MAX_UPLOADED_AGILE_RATES.value())).thenReturn(null);
        try {
            presenter.onViewOpened(null);
            fail();
        } catch (TetrisException MaxUploadException) {
            assertEquals("!!! Max Uploaded Agile Rates is returning null resulting in Null Pointer Exception!!!", MaxUploadException.getBaseMessage());
        }
    }

    @Test
    public void badRunWhenActiveAgileRateInvalidUploadAgileInvalid() {
        when(uiContext.getPropertyState()).thenReturn(propertyState);
        when(propertyState.isEffectiveReadOnly()).thenReturn(true);

        when(pacmanConfigParamsService.getIntegerParameterValue(FeatureTogglesConfigParamName.MAX_ACTIVE_AGILE_RATES.value())).thenReturn(null);
        try {
            presenter.onViewOpened(null);
            fail();
        } catch (TetrisException MaxActiveException) {
            assertEquals("!!! Max Active Agile Rates is returning null resulting in Null Pointer Exception!!!", MaxActiveException.getBaseMessage());
        }

        when(pacmanConfigParamsService.getIntegerParameterValue(FeatureTogglesConfigParamName.MAX_ACTIVE_AGILE_RATES.value())).thenReturn(1);
        when(pacmanConfigParamsService.getIntegerParameterValue(FeatureTogglesConfigParamName.MAX_UPLOADED_AGILE_RATES.value())).thenReturn(null);
        try {
            presenter.onViewOpened(null);
            fail();
        } catch (TetrisException MaxUploadException) {
            assertEquals("!!! Max Uploaded Agile Rates is returning null resulting in Null Pointer Exception!!!", MaxUploadException.getBaseMessage());
        }
    }

    @Test
    public void onWorkContext() {
        when(uiContext.getPropertyState()).thenReturn(new PropertyState());
        verifyInitCalledOndMethod(presenter, presenter1 -> presenter1.onWorkContextChange(null));
    }

    @Test
    public void onDTASave() {
        verifyInitCalledOndMethod(presenter, presenter1 -> presenter1.onDTASave(null));
    }

    @Test
    public void addProduct() {
        presenter.setMaxAgileRates(1);
        presenter.addProduct();
        verify(agileRatesPresenter).addAgileProduct(false);
    }

    @Test
    public void editProduct() {
        Product agileProduct = new Product();
        agileProduct.setId(1234);
        AgileRatesProductConfigurationDTO dto = new AgileRatesProductConfigurationDTO();
        when(agileRatesConfigurationService.loadAgileRatesProductConfigurationByProductId(agileProduct.getId())).thenReturn(dto);

        Product barProduct = new Product();
        barProduct.setSystemDefault(true);
        barProduct.setId(1);
        IndependentProductConfigurationDTO barDTO = new IndependentProductConfigurationDTO();
        barDTO.setProduct(barProduct);
        when(agileRatesConfigurationService.loadIndependentProductConfigurationByProductId(barProduct.getId())).thenReturn(barDTO);

        Product independentProduct = new Product();
        independentProduct.setSystemDefault(true);
        independentProduct.setId(100);
        IndependentProductConfigurationDTO independentDTO = new IndependentProductConfigurationDTO();
        independentDTO.setProduct(independentProduct);
        when(agileRatesConfigurationService.loadIndependentProductConfigurationByProductId(independentProduct.getId())).thenReturn(independentDTO);

        Product smallGroupProduct = new Product();
        smallGroupProduct.setId(500);
        AgileRatesProductConfigurationDTO smallGroupDTO = new AgileRatesProductConfigurationDTO();
        smallGroupDTO.setProduct(smallGroupProduct);
        when(agileRatesConfigurationService.loadSmallGroupProductConfigurationByProductId(smallGroupProduct.getId())).thenReturn(smallGroupDTO);

        // BAR Product Flow
        barProduct.setType(AgileRatesProductTypeEnum.DAILY.getValue());
        presenter.editProduct(barProduct);

        verify(agileRatesPresenter).editIndependentProduct(barDTO);

        // Independent Product Flow
        independentProduct.setType(AgileRatesProductTypeEnum.INDEPENDENTLY.getValue());
        presenter.editProduct(independentProduct);

        verify(agileRatesPresenter).editIndependentProduct(independentDTO);

        // Agile Product Flow
        agileProduct.setType(AgileRatesProductTypeEnum.FENCED_AND_PACKAGED.getValue());
        presenter.editProduct(agileProduct);

        verify(agileRatesPresenter).editAgileProduct(dto);

        // Small Group Product Flow
        smallGroupProduct.setType(AgileRatesProductTypeEnum.SMALL_GROUP.getValue());
        presenter.editProduct(smallGroupProduct);

        verify(agileRatesPresenter).editGroupProduct(smallGroupDTO);
    }

    @Test
    public void copyLinkedProduct() {
        Product product = new Product();
        product.setDisplayOrder(0);
        presenter.setProducts(new ArrayList<>(Arrays.asList(product)));
        String name = "copyName";
        AgileRatesProductConfigurationDTO dtoCopy = new AgileRatesProductConfigurationDTO();
        when(agileRatesConfigurationService.copyLinkedProduct(product, name, 1, LocalDate.now())).thenReturn(dtoCopy);
        Date date = org.joda.time.LocalDate.now().toDate();
        when(dateService.getCaughtUpDate()).thenReturn(date);

        presenter.copyProduct(product, name);

        verify(agileRatesConfigurationService).saveAgileRatesProductConfiguration(dtoCopy);
        verify(agileRatesConfigurationService, never()).copyDefaultOffsetMappings(product, dtoCopy.getProduct());
        final Consumer<AgileRatesProductListingPresenter> methodConsumer = presenter1 -> presenter1.copyProduct(product, name);
        verify(forceSyncEvent).fire(any(ForceSyncEvent.class));
        verifySync(presenter, methodConsumer);
        verifyInitCalledOndMethod(presenter, methodConsumer);
    }

    @Test
    public void copyLinkedProduct_withCustomChildPricingType() {
        Product product = new Product();
        product.setDisplayOrder(0);
        product.setChildPricingType(2);
        presenter.setProducts(new ArrayList<>(Arrays.asList(product)));
        String name = "copyName";
        AgileRatesProductConfigurationDTO dtoCopy = new AgileRatesProductConfigurationDTO();

        when(agileRatesConfigurationService.copyLinkedProduct(product, name, 1, LocalDate.now())).thenReturn(dtoCopy);
        doNothing().when(agileRatesConfigurationService).copyDefaultOffsetMappings(product, dtoCopy.getProduct());
        Date date = org.joda.time.LocalDate.now().toDate();
        when(dateService.getCaughtUpDate()).thenReturn(date);

        presenter.copyProduct(product, name);

        verify(agileRatesConfigurationService).saveAgileRatesProductConfiguration(dtoCopy);
        verify(agileRatesConfigurationService).copyOffsetMappings(product, dtoCopy.getProduct());
        verify(forceSyncEvent).fire(any(ForceSyncEvent.class));
    }

    @Test
    public void deleteLinkedProduct() {
        Product product = new Product();
        presenter.setProducts(new ArrayList<>());
        product.setDisplayOrder(1);
        Date date = org.joda.time.LocalDate.now().toDate();
        when(dateService.getCaughtUpDate()).thenReturn(date);
        List<ProductHierarchy> hierarchies = Collections.emptyList();
        when(agileRatesConfigurationService.getProductHierarchies()).thenReturn(hierarchies);
        doNothing().when(view).loadProducts(Arrays.asList(product));

        presenter.deleteProduct(product);

        verify(agileRatesConfigurationService).getAllHierarchiesForProduct(product);
        verify(agileRatesConfigurationService).saveAgileRatesProductHierarchies(Collections.emptyList(), Collections.emptyList());
        verify(agileRatesConfigurationService).deleteLinkedProduct(product, new org.joda.time.LocalDate(date));
        verify(view).loadProducts(new ArrayList<>());
        final Consumer<AgileRatesProductListingPresenter> methodConsumer = presenter1 -> presenter1.deleteProduct(product);
        verify(forceSyncEvent).fire(any(ForceSyncEvent.class));
        verifySync(presenter, methodConsumer);
        verifyInitCalledOndMethod(presenter, methodConsumer);
    }

    @Test
    public void deleteProductGroup() {
        Product productA = new Product();
        ProductGroup productGroupA = new ProductGroup();
        ProductGroup productGroupB = new ProductGroup();
        AgileRatesProductGroup agileRatesProductGroup = new AgileRatesProductGroup();

        productGroupA.setProduct(productA);
        productGroupB.setProduct(new Product());

        productGroupA.setAgileRatesProductGroup(agileRatesProductGroup);
        presenter.deleteProductFromProductGroup(productA, Collections.singletonList(productGroupA));
        verify(agileRatesConfigurationService).deleteProductFromProductGroup(productA, Collections.singletonList(productGroupA));

        productGroupB.setAgileRatesProductGroup(agileRatesProductGroup);
        presenter.deleteProductFromProductGroup(productA, Arrays.asList(productGroupA, productGroupB));
        verify(agileRatesConfigurationService).deleteProductFromProductGroup(productA, Arrays.asList(productGroupA, productGroupB));
    }

    @Test
    public void deleteProductAssociatedRestrictionsWhenProductGetsDeleted() {
        Product product = new Product();
        presenter.setProducts(new ArrayList<>());
        product.setDisplayOrder(1);
        Date date = org.joda.time.LocalDate.now().toDate();
        when(dateService.getCaughtUpDate()).thenReturn(date);
        presenter.deleteProduct(product);
        doNothing().when(view).loadProducts(Arrays.asList(product));

        verify(agileRatesConfigurationService).deleteLinkedProduct(product, new org.joda.time.LocalDate(date));
        verify(agileRatesConfigurationService).saveAgileProductRestrictionAssociationForFPLOS(product.getId(), Collections.EMPTY_SET);
        final Consumer<AgileRatesProductListingPresenter> methodConsumer = presenter1 -> presenter1.save(product, false);
        verify(forceSyncEvent).fire(any(ForceSyncEvent.class));
        verifySync(presenter, methodConsumer);
        verifyInitCalledOndMethod(presenter, methodConsumer);
        verify(view).loadProducts(new ArrayList<>());
    }

    @Test
    public void swapDisplayOrder() {
        Product prodA = new Product(), prodB = new Product();
        prodA.setDisplayOrder(1);
        prodB.setDisplayOrder(2);
        prodA.setName("Product A");
        prodB.setName("Product B");
        List<Product> products = new ArrayList<>();
        products.add(prodA);
        products.add(prodB);
        presenter.setProducts(products);

        // decrease rank test
        presenter.swapDisplayOrder(prodA, false);
        verify(agileRatesConfigurationService).swapProductDisplayOrder(prodA, prodB);
        final Consumer<AgileRatesProductListingPresenter> methodConsumer = presenter1 -> presenter1.save(prodA, false);
        verifyInitCalledOndMethod(presenter, methodConsumer);
        // increase rank test
        presenter.swapDisplayOrder(prodA, true);
        verify(agileRatesConfigurationService).swapProductDisplayOrder(prodA, prodB);
        assertEquals(2, prodB.getDisplayOrder().intValue());
        assertEquals(1, prodA.getDisplayOrder().intValue());
    }

    @Test
    public void saveUploadChange() {
        Product product = new Product();

        product.setCode(Product.AGILE_RATES_PRODUCT_CODE);
        presenter.setIndependentProductsEnabled(true);
        presenter.save(product, false);
        verify(agileRatesConfigurationService, times(1)).saveProduct(product, false);
        verify(agileRatesConfigurationService, times(1)).deleteFutureDecisionsForSmallGroupProductOrWhenAgileRateUploadIsChangedToDisabled(product);

        presenter.setIndependentProductsEnabled(true);
        product.setCode(Product.INDEPENDENT_PRODUCT_CODE);
        presenter.save(product, false);
        verify(agileRatesConfigurationService, times(2)).saveProduct(product, false);
        verify(agileRatesConfigurationService, times(1)).deleteFutureDecisionsForSmallGroupProductOrWhenAgileRateUploadIsChangedToDisabled(product);

        presenter.save(product, false);
        verify(agileRatesConfigurationService, times(3)).saveProduct(product, false);
        final Consumer<AgileRatesProductListingPresenter> methodConsumer = presenter1 -> presenter1.save(product, false);
        verify(agileRatesConfigurationService, times(2)).deleteFutureDecisionsForSmallGroupProductOrWhenAgileRateUploadIsChangedToDisabled(product);
        verify(forceSyncEvent, times(3)).fire(any(ForceSyncEvent.class));
        verifySync(presenter, methodConsumer);
        verifyInitCalledOndMethod(presenter, methodConsumer);
    }

    @Test
    public void testRemoveHierarchiesForProduct() {
        Product productToRemove = new Product();
        presenter.removeHierarchiesForProduct(productToRemove);
        verify(agileRatesConfigurationService).removeHierarchiesForProduct(productToRemove);
    }

    @Test
    public void getSingleProductHierarchies() {
        Product product = new Product();
        // no hierarchies exist
        when(agileRatesConfigurationService.getProductHierarchies()).thenReturn(Collections.emptyList());
        List<ProductHierarchy> hierarchyList = presenter.getHierarchiesForProduct(product);
        assertEquals(Collections.emptyList(), hierarchyList);
        verify(agileRatesConfigurationService).getAllHierarchiesForProduct(product);

        // hierarchies exist
        List<ProductHierarchy> hierarchies = new ArrayList<>();

        ProductHierarchy hierarchy = new ProductHierarchy();
        hierarchy.setFromProduct(product);
        hierarchy.setToProduct(new Product());
        hierarchies.add(hierarchy);

        ProductHierarchy hierarchyB = new ProductHierarchy();
        hierarchyB.setFromProduct(new Product());
        hierarchyB.setToProduct(product);
        hierarchies.add(hierarchyB);

        when(agileRatesConfigurationService.getProductHierarchies()).thenReturn(hierarchies);
        when(agileRatesConfigurationService.getAllHierarchiesForProduct(product)).thenReturn(hierarchies);
        assertEquals(hierarchies, presenter.getHierarchiesForProduct(product));
    }

    @Test
    public void isUniqueProductNameSaveExistingCaseInsensitive() {
        presenter.products = createProducts();
        assertFalse(presenter.isUniqueProductName("ProDuct2"));
        assertFalse(presenter.isUniqueProductName("ProDuct3"));
        assertFalse(presenter.isUniqueProductName("ProDuct4"));
        assertFalse(presenter.isUniqueProductName("ProDuct5"));
        assertFalse(presenter.isUniqueProductName("ProDuct1"));
    }

    @Test
    public void isUniqueProductNameExistingWithChange() {
        presenter.products = createProducts();
        assertFalse(presenter.isUniqueProductName("product5"));
        assertFalse(presenter.isUniqueProductName("product5"));
        assertFalse(presenter.isUniqueProductName("product5"));
        assertFalse(presenter.isUniqueProductName("product5"));
        assertFalse(presenter.isUniqueProductName("product1"));
        assertTrue(presenter.isUniqueProductName("product1Edited"));
        assertTrue(presenter.isUniqueProductName("product2Edited"));
        assertTrue(presenter.isUniqueProductName("product3Edited"));
        assertTrue(presenter.isUniqueProductName("product4Edited"));
        assertTrue(presenter.isUniqueProductName("product5Edited"));
    }

    @Test
    public void isMaxUploadProductCountWithinSubscription() {
        Product uploadedProduct = new Product();
        uploadedProduct.setUpload(true);
        Product nonUploadedProduct = new Product();
        nonUploadedProduct.setUpload(false);
        presenter.setProducts(Arrays.asList(uploadedProduct, nonUploadedProduct));
        presenter.setMaxUploadedAgileRates(0);
        assertFalse(presenter.isMaxUploadProductCountWithinSubscription(uploadedProduct));
        presenter.setMaxUploadedAgileRates(1);
        assertTrue(presenter.isMaxUploadProductCountWithinSubscription(uploadedProduct));
        presenter.setMaxUploadedAgileRates(2);
        assertTrue(presenter.isMaxUploadProductCountWithinSubscription(uploadedProduct));
    }

    @Test
    public void isMaxUploadProductCountWithinSubscription_FirstProductUploaded() {
        Product nonUploadedProduct = new Product();
        nonUploadedProduct.setUpload(false);
        presenter.setProducts(Arrays.asList(nonUploadedProduct));
        presenter.setMaxUploadedAgileRates(0);
        assertFalse(presenter.isMaxUploadProductCountWithinSubscription(nonUploadedProduct));
        presenter.setMaxUploadedAgileRates(1);
        assertTrue(presenter.isMaxUploadProductCountWithinSubscription(nonUploadedProduct));
    }

    @Test
    public void isMissingRoomTypeAlertOpenWhenAlertIsOpen() {
        when(roomTypeRecodingService.isExistingMissingRoomTypeAlertOpen()).thenReturn(true);
        assertTrue(presenter.isMissingRoomTypeAlertOpen());
    }

    @Test
    public void getDeleteMessage() {
        // delete message with hierarchy, product group, and children
        Product product = new Product();
        product.setName("Basic");
        String expected = "This Base Product is linked to other Products. Deleting this Base Product will inactivate the Products listed below. Are you sure you want to continue?\n Basic\n" +
                "This Product is attached to one or more hierarchies, deleting this Product will remove all associated hierarchies.\n" +
                "This Product is attached to a Product Group, and updating this Product may remove the Product from its Product Group.\n";
        String answer = presenter.getDeleteMessageForLinkedProducts(product, "Basic", true, true, false);
        assertEquals(expected, answer);

        // delete message with product group
        expected = "Are you sure you want to delete the Basic product?\n" +
                "This Product is attached to a Product Group, and updating this Product may remove the Product from its Product Group.\n";
        answer = presenter.getDeleteMessageForLinkedProducts(product, "", false, true, false);
        assertEquals(expected, answer);

        // delete message with Product Group and Hierarchy (no children)
        expected = "Are you sure you want to delete the Basic product?\n" +
                "This Product is attached to one or more hierarchies, deleting this Product will remove all associated hierarchies.\n" +
                "This Product is attached to a Product Group, and updating this Product may remove the Product from its Product Group.\n";
        answer = presenter.getDeleteMessageForLinkedProducts(product, "", true, true, false);
        assertEquals(expected, answer);

        // delete message with hierarchy
        expected = "Are you sure you want to delete the Basic product?\n" +
                "This Product is attached to one or more hierarchies, deleting this Product will remove all associated hierarchies.\n";
        answer = presenter.getDeleteMessageForLinkedProducts(product, "", true, false, false);
        assertEquals(expected, answer);

        // delete message with children
        expected = "This Base Product is linked to other Products. Deleting this Base Product will inactivate the Products listed below. Are you sure you want to continue?\n Basic\n";
        answer = presenter.getDeleteMessageForLinkedProducts(product, "Basic", false, false, false);
        assertEquals(expected, answer);

        // delete message with no children
        expected = "Are you sure you want to delete the Basic product?\n";
        answer = presenter.getDeleteMessageForLinkedProducts(product, "", false, false, false);
        assertEquals(expected, answer);

        // delete message if rate protect product is dependant
        expected = "Are you sure you want to delete the Basic product?\n" +
                "This Product is linked to a Rate Protect Product. Deleting it will make the Rate Protect Product(s) Invalid.";
        answer = presenter.getDeleteMessageForLinkedProducts(product, "", false, false, true);
        assertEquals(expected, answer);

    }

    @Test
    public void isMissingRoomTypeAlertOpenWhenALertIsNotOpen() {
        assertFalse(presenter.isMissingRoomTypeAlertOpen());
    }

    private void verifyInitCalledOndMethod(AgileRatesProductListingPresenter presenter, Consumer<AgileRatesProductListingPresenter> methodConsumer) {
        verifyPresenterMethodCalled(presenter, methodConsumer, presenter1 -> verify(presenter1).init());
    }

    @Test
    public void getProductNameHtmlNotOptimized() {
        Product product = new Product();
        product.setName("My Great Product");
        product.setOptimized(false);
        String productNameHtml = presenter.getProductNameHtml(product);
        assertTrue(productNameHtml.startsWith("My Great Product " + LINK.getHtmlWithTooltip("Linked")));
    }

    @Test
    public void getProductNameHtmlOptimized() {
        Product product = new Product();
        product.setName("My Great Product");
        product.setOptimized(true);
        String productNameHtml = presenter.getProductNameHtml(product);
        assertTrue(productNameHtml.startsWith("My Great Product " + TARGET_SMALL.getHtmlWithTooltip("Linked - Optimized")));
    }

    @Test
    public void getProductNameHtmlSmallGroup() {
        Product product = new Product();
        product.setName("My Great Product");
        product.setCode("SMALL_GROUP");
        product.setOptimized(true);
        ProductCode pc = new ProductCode(3);
        product.setProductCode(pc);
        String productNameHtml = presenter.getProductNameHtml(product);
        assertTrue(productNameHtml.startsWith("My Great Product " + USERS.getHtmlWithTooltip("Group Product")));
        assertFalse(productNameHtml.startsWith("My Great Product " + TARGET_SMALL.getHtmlWithTooltip("Optimized")));
    }

    @Test
    public void testGetDirectChildProducts() {
        List<Product> products = createProducts(10);
        Product parent = products.get(0);
        parent.setDependentProductId(-1);

        Set<Product> expectedChildren = new HashSet<>();
        for (int i = 1; i < products.size(); i++) {
            expectedChildren.add(products.get(i));
            products.get(i).setDependentProductId(parent.getId());
        }
        presenter.setProducts(products);
        Set<Product> directChildren = presenter.getDirectChildProducts(parent);
        assertTrue(Sets.difference(expectedChildren, directChildren).isEmpty());

        // Independent Product Flow
        presenter.setIndependentProductsEnabled(true);
        products = createProducts(5);

        parent = products.get(0);
        parent.setDependentProductId(-1);

        expectedChildren = new HashSet<>();
        for (int i = 1; i < products.size(); i++) {
            expectedChildren.add(products.get(i));
            products.get(i).setDependentProductId(parent.getId());
        }

        Product indProduct = new Product();
        indProduct.setId(5);
        indProduct.setCode(Product.INDEPENDENT_PRODUCT_CODE);
        indProduct.setDependentProductId(-1);
        products.add(indProduct);

        presenter.setProducts(products);
        directChildren = presenter.getDirectChildProducts(parent);
        assertTrue(Sets.difference(expectedChildren, directChildren).isEmpty());
    }

    @Test
    public void testGetDirectChildProducts_ExcludeCousins() {
        List<Product> products = createProducts(10);
        Product parent = products.get(0);
        Product sibling = products.get(1);

        parent.setDependentProductId(-1);
        sibling.setDependentProductId(-1);

        Set<Product> expectedChildren = new HashSet<>();
        for (int i = 2; i < products.size() - 1; i++) {
            expectedChildren.add(products.get(i));

            products.get(i).setDependentProductId(parent.getId());
            products.get(i + 1).setDependentProductId(sibling.getId());
        }

        presenter.setProducts(products);
        Set<Product> directChildren = presenter.getDirectChildProducts(parent);
        assertTrue(Sets.difference(expectedChildren, directChildren).isEmpty());
    }

    @Test
    public void testGetDirectChildProducts_NoChildren() {
        List<Product> products = createProducts();
        Product parent = products.get(0);
        for (Product product : products) {
            product.setDependentProductId(-1);
        }
        presenter.setProducts(products);
        Set<Product> directChildren = presenter.getDirectChildProducts(parent);
        assertTrue(directChildren.isEmpty());
    }

    @Test
    public void getHierarchyProducts() {
        Product barProduct = new Product();
        Product percentageProduct = new Product();
        Product productInProductGroup = new Product();
        Product smallGroupProduct = new Product();
        Product freeNightProduct = new Product();

        barProduct.setCode(Product.BAR);
        percentageProduct.setCode(Product.AGILE_RATES_PRODUCT_CODE);
        productInProductGroup.setCode(Product.AGILE_RATES_PRODUCT_CODE);
        smallGroupProduct.setCode(Product.GROUP_PRODUCT_CODE);
        freeNightProduct.setCode(Product.AGILE_RATES_PRODUCT_CODE);
        freeNightProduct.setFreeNightEnabled(true);

        barProduct.setSystemDefault(true);
        percentageProduct.setSystemDefault(false);
        productInProductGroup.setSystemDefault(false);
        smallGroupProduct.setSystemDefault(false);
        freeNightProduct.setSystemDefault(false);

        percentageProduct.setOptimized(true);
        productInProductGroup.setOptimized(true);
        smallGroupProduct.setOptimized(true);
        freeNightProduct.setOptimized(false);

        percentageProduct.setOffsetMethod(AgileRatesOffsetMethod.PERCENTAGE);
        productInProductGroup.setOffsetMethod(AgileRatesOffsetMethod.PERCENTAGE);
        smallGroupProduct.setOffsetMethod(AgileRatesOffsetMethod.PERCENTAGE);
        freeNightProduct.setOffsetMethod(AgileRatesOffsetMethod.PERCENTAGE);

        barProduct.setActive(true);
        percentageProduct.setActive(true);
        productInProductGroup.setActive(true);
        smallGroupProduct.setActive(true);
        freeNightProduct.setActive(true);

        ProductGroup productGroup = new ProductGroup();
        productGroup.setProduct(productInProductGroup);
        List<ProductGroup> productGroups = new ArrayList<>();
        productGroups.add(productGroup);
        when(agileRatesConfigurationService.getProductGroups()).thenReturn(productGroups);

        // with bar test + no percentage
        presenter.setAllProducts(Collections.singletonList(barProduct));
        assertEquals(presenter.getHierarchyProducts(), Collections.singletonList(barProduct));

        // no bar test + with percentage
        presenter.setAllProducts(Collections.singletonList(percentageProduct));
        assertEquals(presenter.getHierarchyProducts(), Collections.singletonList(percentageProduct)); // with percentage

        // with bar + with percentage
        presenter.setAllProducts(Arrays.asList(barProduct, percentageProduct));
        assertEquals(presenter.getHierarchyProducts(), Arrays.asList(barProduct, percentageProduct));

        // with bar + group product
        presenter.setAllProducts(Arrays.asList(barProduct, smallGroupProduct));
        assertEquals(presenter.getHierarchyProducts(), Arrays.asList(barProduct));

        // with bar + free night product
        presenter.setAllProducts(Arrays.asList(barProduct, freeNightProduct));
        assertEquals(presenter.getHierarchyProducts(), Arrays.asList(barProduct));

        // with bar + group product
        presenter.setAllProducts(Arrays.asList(barProduct, smallGroupProduct, freeNightProduct));
        assertEquals(presenter.getHierarchyProducts(), Arrays.asList(barProduct));

        // fixed parent -> percent child -> percent grandchild
        Product fixedParent = new Product();
        Product percentChild = new Product();
        Product percentGrandChild = new Product();
        fixedParent.setOffsetMethod(AgileRatesOffsetMethod.FIXED);
        percentChild.setOffsetMethod(AgileRatesOffsetMethod.PERCENTAGE);
        percentGrandChild.setOffsetMethod(AgileRatesOffsetMethod.PERCENTAGE);
        fixedParent.setCode(Product.AGILE_RATES_PRODUCT_CODE);
        percentChild.setCode(Product.AGILE_RATES_PRODUCT_CODE);
        percentGrandChild.setCode(Product.AGILE_RATES_PRODUCT_CODE);
        barProduct.setId(0);
        productInProductGroup.setDependentProductId(0);
        fixedParent.setDependentProductId(0);
        fixedParent.setId(1);
        percentChild.setId(2);
        percentGrandChild.setId(3);
        percentChild.setDependentProductId(1);
        percentGrandChild.setDependentProductId(2);
        presenter.setAllProducts(Arrays.asList(fixedParent, percentChild, percentGrandChild, barProduct, productInProductGroup));
        assertEquals(presenter.getHierarchyProducts(), Collections.singletonList(barProduct));

        // percent parent -> fixed child
        Product percentParent = new Product();
        Product fixedChild = new Product();
        percentParent.setCode(Product.AGILE_RATES_PRODUCT_CODE);
        fixedChild.setCode(Product.AGILE_RATES_PRODUCT_CODE);
        percentParent.setOffsetMethod(AgileRatesOffsetMethod.PERCENTAGE);
        fixedChild.setOffsetMethod(AgileRatesOffsetMethod.FIXED);
        percentParent.setOptimized(true);
        fixedChild.setOptimized(false);
        percentParent.setId(1);
        fixedChild.setId(2);
        percentParent.setDependentProductId(0);
        fixedChild.setDependentProductId(1);
        percentParent.setActive(true);
        percentParent.setCode(Product.AGILE_RATES_PRODUCT_CODE);
        presenter.setAllProducts(Arrays.asList(percentParent, fixedChild, barProduct, productInProductGroup));
        assertEquals(Arrays.asList(percentParent, barProduct), presenter.getHierarchyProducts());
    }

    @Test
    public void testProductsForProductGroups() {
        // we only allow active + percentage + children + optimized products (agile only)
        Product barProduct = new Product();
        Product parentProduct = new Product();
        Product childProductPercentage = new Product();
        Product childProductFixed = new Product();
        Product inactiveChildProduct = new Product();
        Product nonOptChildProduct = new Product();
        Product independentProduct = new Product();

        barProduct.setSystemDefault(true);
        barProduct.setActive(true);
        parentProduct.setActive(true);
        childProductPercentage.setActive(true);
        nonOptChildProduct.setActive(true);
        independentProduct.setActive(true);

        parentProduct.setOffsetMethod(AgileRatesOffsetMethod.PERCENTAGE);
        childProductPercentage.setOffsetMethod(AgileRatesOffsetMethod.PERCENTAGE);
        inactiveChildProduct.setOffsetMethod(AgileRatesOffsetMethod.PERCENTAGE);
        childProductFixed.setOffsetMethod(AgileRatesOffsetMethod.PERCENTAGE);
        nonOptChildProduct.setOffsetMethod(AgileRatesOffsetMethod.PERCENTAGE);

        barProduct.setId(0);
        parentProduct.setId(1);
        childProductPercentage.setId(2);
        childProductFixed.setId(4);
        inactiveChildProduct.setId(3);
        nonOptChildProduct.setId(5);
        independentProduct.setId(6);

        parentProduct.setOptimized(true);
        childProductPercentage.setOptimized(true);
        childProductFixed.setOptimized(true);
        inactiveChildProduct.setOptimized(true);
        nonOptChildProduct.setOptimized(false);

        childProductPercentage.setDependentProductId(1);
        childProductFixed.setDependentProductId(1);
        inactiveChildProduct.setDependentProductId(1);
        nonOptChildProduct.setDependentProductId(1);

        independentProduct.setCode(Product.INDEPENDENT_PRODUCT_CODE);
        barProduct.setCode(Product.BAR);
        parentProduct.setCode(Product.AGILE_RATES_PRODUCT_CODE);
        childProductPercentage.setCode(Product.AGILE_RATES_PRODUCT_CODE);
        childProductFixed.setCode(Product.AGILE_RATES_PRODUCT_CODE);
        inactiveChildProduct.setCode(Product.AGILE_RATES_PRODUCT_CODE);
        nonOptChildProduct.setCode(Product.AGILE_RATES_PRODUCT_CODE);
        when(agileRatesConfigurationService.getProductHierarchies()).thenReturn(Collections.emptyList());

        List<Product> results = presenter.getProductGroupProducts(Arrays.asList(barProduct, parentProduct, childProductPercentage, inactiveChildProduct, nonOptChildProduct, independentProduct));
        assertEquals(results, Collections.singletonList(childProductPercentage));
    }

    @Test
    public void getProductGroupProducts() {
        Product productA = new Product();
        ProductGroup productGroupA = new ProductGroup();
        ProductGroup productGroupB = new ProductGroup();
        ProductGroup productGroupC = new ProductGroup();
        AgileRatesProductGroup agileRatesProductGroupA = new AgileRatesProductGroup();

        productGroupA.setProduct(productA);
        productGroupA.setAgileRatesProductGroup(agileRatesProductGroupA);
        productGroupB.setAgileRatesProductGroup(agileRatesProductGroupA);
        productGroupC.setAgileRatesProductGroup(new AgileRatesProductGroup());

        presenter.getProductGroupProductsForProduct(productA);
        verify(agileRatesConfigurationService).getAllProductsInProductGroup(productA, new ArrayList<>());
    }

    @Test
    public void getProductTypeCaption() {
        Product barProduct = new Product();
        barProduct.setSystemDefault(true);
        when(lang.getText("primary.priced")).thenReturn("Primary Priced");

        Product independentProduct = new Product();
        independentProduct.setSystemDefault(false);
        independentProduct.setDependentProductId(null);
        independentProduct.setCode(Product.INDEPENDENT_PRODUCT_CODE);
        when(lang.getText("independently.priced")).thenReturn("Independently Priced");

        Product optimizedProduct = new Product();
        optimizedProduct.setSystemDefault(false);
        optimizedProduct.setDependentProductId(1);
        optimizedProduct.setOptimized(true);
        when(lang.getText("linked.product.optimized")).thenReturn("Linked Product - Optimized");

        Product optimizedUnassignedProduct = new Product();
        optimizedUnassignedProduct.setSystemDefault(false);
        optimizedUnassignedProduct.setDependentProductId(null);
        optimizedUnassignedProduct.setOptimized(true);

        Product nonOptimizedProduct = new Product();
        nonOptimizedProduct.setSystemDefault(false);
        nonOptimizedProduct.setDependentProductId(1);
        nonOptimizedProduct.setOptimized(false);
        when(lang.getText("linked.product")).thenReturn("Linked Product");

        Product nonOptimizedUnassignedProduct = new Product();
        nonOptimizedUnassignedProduct.setSystemDefault(false);
        nonOptimizedUnassignedProduct.setDependentProductId(null);
        nonOptimizedUnassignedProduct.setOptimized(false);

        Product smallGroupProduct = new Product();
        smallGroupProduct.setSystemDefault(false);
        smallGroupProduct.setOptimized(true);
        smallGroupProduct.setCode(GROUP_PRODUCT_CODE);
        when(lang.getText("group.product")).thenReturn("Group Product");

        Product freeNightProduct = new Product();
        freeNightProduct.setSystemDefault(false);
        freeNightProduct.setOptimized(false);
        freeNightProduct.setFreeNightEnabled(true);
        when(lang.getText("free.night.product")).thenReturn("Linked Product - Free Night");

        assertEquals("Primary Priced", presenter.getProductTypeCaption(barProduct));
        assertEquals("Independently Priced", presenter.getProductTypeCaption(independentProduct));
        assertEquals("Linked Product - Optimized", presenter.getProductTypeCaption(optimizedProduct));
        assertEquals("Linked Product - Optimized", presenter.getProductTypeCaption(optimizedUnassignedProduct));
        assertEquals("Linked Product", presenter.getProductTypeCaption(nonOptimizedProduct));
        assertEquals("Linked Product", presenter.getProductTypeCaption(nonOptimizedUnassignedProduct));
        assertEquals("Group Product", presenter.getProductTypeCaption(smallGroupProduct));
        assertEquals("Linked Product - Free Night", presenter.getProductTypeCaption(freeNightProduct));
    }

    private List<Product> createProducts(int quantity) {
        List<Product> products = new ArrayList<>(quantity);
        for (int i = 0; i < quantity; i++) {
            Product product = new Product();
            product.setId(i);
            product.setName("product" + i);
            products.add(product);
        }
        return products;
    }

    private List<Product> createProducts() {
        Product product1 = new Product();
        product1.setName("product1");
        product1.setId(0);
        Product product2 = new Product();
        product2.setName("product2");
        product2.setId(1);
        Product product3 = new Product();
        product3.setName("product3");
        product3.setId(2);
        Product product4 = new Product();
        product4.setName("product4");
        product4.setId(3);
        Product product5 = new Product();
        product5.setName("product5");
        product5.setId(4);
        return Arrays.asList(
                product1,
                product2,
                product3,
                product4,
                product5
        );
    }

    @Test
    public void setBaseRoomTypeConfigurationComplete() {
        presenter.setBaseRoomTypeConfigurationComplete();
        verify(pricingConfigurationService).isBaseRoomTypeConfigurationComplete();
    }

    @Test
    public void getBARProduct() {
        Product barProduct = new Product();
        barProduct.setSystemDefault(true);
        Product product2 = new Product();
        Product product3 = new Product();

        presenter.setProducts(Arrays.asList());
        assertNull(presenter.getBARProduct());

        presenter.setProducts(Arrays.asList(barProduct, product2, product3));
        assertEquals(barProduct, presenter.getBARProduct());
    }

    @Test
    public void testCopyIndependentProduct() {
        presenter.setIndependentProductsEnabled(true);
        presenter.setProducts(new ArrayList<>());
        presenter.setAllProducts(new ArrayList<>());

        Product originalProduct = new Product();
        originalProduct.setCode(Product.INDEPENDENT_PRODUCT_CODE);

        Date date = org.joda.time.LocalDate.now().toDate();
        when(dateService.getCaughtUpDate()).thenReturn(date);

        String newName = "newProductName";
        IndependentProductConfigurationDTO copiedDTO = new IndependentProductConfigurationDTO();

        when(agileRatesConfigurationService.copyIndependentProduct(originalProduct, newName, 0)).thenReturn(copiedDTO);
        presenter.copyProduct(originalProduct, newName);

        verify(agileRatesConfigurationService).copyIndependentProduct(originalProduct, newName, 0);
        verify(agileRatesConfigurationService).saveIndependentProductConfiguration(copiedDTO, new ArrayList<>());
        verify(agileRatesConfigurationService).copyDefaultOffsetMappings(originalProduct, copiedDTO.getProduct());
        verify(agileRatesConfigurationService).copyDefaultCeilingFloorMappings(originalProduct, copiedDTO.getProduct());

        final Consumer<AgileRatesProductListingPresenter> methodConsumer = presenter1 -> presenter1.copyProduct(originalProduct, newName);
        verify(forceSyncEvent).fire(any(ForceSyncEvent.class));
        verifySync(presenter, methodConsumer);
        verifyInitCalledOndMethod(presenter, methodConsumer);
    }

    @Test
    public void testDeleteIndependentProduct() {
        presenter.setIndependentProductsEnabled(true);

        presenter.setProducts(new ArrayList<>());
        Product product = new Product();
        product.setCode(Product.INDEPENDENT_PRODUCT_CODE);
        product.setDisplayOrder(1);
        doNothing().when(view).loadProducts(Arrays.asList(product));
        presenter.setAllProducts(Arrays.asList(product));

        org.joda.time.LocalDate localDate = org.joda.time.LocalDate.now();
        Date date = localDate.toDate();
        when(dateService.getCaughtUpDate()).thenReturn(date);

        presenter.deleteProduct(product);

        verify(agileRatesConfigurationService).deleteIndependentProduct(product, Arrays.asList(), localDate);
        verify(agileRatesConfigurationService).shiftProductDisplayOrder(new ArrayList<>());
        verify(view).loadProducts(new ArrayList<>());
        final Consumer<AgileRatesProductListingPresenter> methodConsumer = presenter1 -> presenter1.deleteProduct(product);
        verify(forceSyncEvent).fire(any(ForceSyncEvent.class));
        verifySync(presenter, methodConsumer);
        verifyInitCalledOndMethod(presenter, methodConsumer);
    }

    @Test
    public void testAddIndependentProduct() {
        presenter.setMaxAgileRates(1);
        presenter.addIndependentProduct();
        verify(agileRatesPresenter).addIndependentProduct();
    }

    @Test
    public void testAddGroupProduct() {
        presenter.setMaxGroupProducts(3);
        presenter.addGroupProduct();
        verify(agileRatesPresenter).addGroupProduct();
    }

    @Test
    public void testAddFreeNightProduct() {
        presenter.setMaxAgileRates(25);
        presenter.setFreeNightProduct(true);
        presenter.addProduct();
        verify(agileRatesPresenter).addAgileProduct(true);
    }

    @Test
    public void testIsMaxIndependentUploadCountWithinSubscription() {
        Product uploadedProduct = new Product();
        uploadedProduct.setId(1);
        uploadedProduct.setUpload(true);
        Product nonUploadedProduct = new Product();
        nonUploadedProduct.setId(2);
        nonUploadedProduct.setUpload(false);

        uploadedProduct.setCode(Product.INDEPENDENT_PRODUCT_CODE);
        nonUploadedProduct.setCode(Product.INDEPENDENT_PRODUCT_CODE);
        presenter.setProducts(Arrays.asList(uploadedProduct, nonUploadedProduct));
        presenter.setMaxUploadedIndependentProducts(0);
        assertFalse(presenter.isMaxUploadProductCountWithinSubscription(uploadedProduct));
        presenter.setMaxUploadedIndependentProducts(1);
        assertTrue(presenter.isMaxUploadProductCountWithinSubscription(uploadedProduct));
        presenter.setMaxUploadedIndependentProducts(2);
        assertTrue(presenter.isMaxUploadProductCountWithinSubscription(uploadedProduct));
    }

    @Test
    public void testReloadProductList() {
        presenter.reloadProductList();
        verify(agileRatesConfigurationService).findAgileAndSystemDefaultProducts();

        presenter.setIndependentProductsEnabled(true);
        presenter.reloadProductList();
        verify(agileRatesConfigurationService).findAgileAndSystemDefaultProductsAndIndependentProducts();

        presenter.setIndependentProductsEnabled(false);
        presenter.setGroupProductsEnabled(true);
        presenter.reloadProductList();
        verify(agileRatesConfigurationService).findAgileAndSystemDefaultProductsAndSmallGroupProducts();

        presenter.setGroupProductsEnabled(true);
        presenter.setIndependentProductsEnabled(true);
        presenter.reloadProductList();
        verify(agileRatesConfigurationService).findAgileAndSystemDefaultProductsAndIndependentAndSmallGroupProducts();

        verify(view, times(4)).loadProducts(anyList());
    }

    @Test
    public void testIfProductHasEditPermission() {
        Product product = new Product();

        when(presenter.hasWriteAccess(TetrisPermissionKey.PRICING_CONFIG_CENTRALLY_MANAGED)).thenReturn(false);
        when(presenter.hasWriteAccess(TetrisPermissionKey.PRICING_CONFIGURATION)).thenReturn(true);
        init();
        presenter.setIndependentProductsEnabled(true);
        product.setCode(Product.INDEPENDENT_PRODUCT_CODE);
        product.setCentrallyManaged(true);

        // false as Independent product does not have centrally managed permission
        assertFalse(presenter.doesProductHaveEditPermission(product));

        // true as Independent product does not have centrally managed permission and is not centrally managed
        product.setCentrallyManaged(false);
        assertTrue(presenter.doesProductHaveEditPermission(product));

        // false as Linked product does not have centrally managed permission
        presenter.setIsReadOnly(false);
        product.setCode(Product.AGILE_RATES_PRODUCT_CODE);
        product.setCentrallyManaged(true);
        assertFalse(presenter.doesProductHaveEditPermission(product));

        // false as Linked product does not have centrally managed permission, and is not centrally managed, but does not have access
        // to definition steps
        product.setCentrallyManaged(false);
        assertFalse(presenter.doesProductHaveEditPermission(product));
    }

    @Test
    public void shouldReturnTrueForSystemDefaultProductWhenIndependentProductPermissionIsTrue() {
        Product product = new Product();
        product.setSystemDefault(true);

        when(presenter.hasWriteAccess(TetrisPermissionKey.PRICING_CONFIGURATION)).thenReturn(true);
        when(presenter.hasWriteAccess(TetrisPermissionKey.AGILE_RATES_DEFINITION)).thenReturn(false);
        init();
        presenter.setIndependentProductsEnabled(true);

        assertTrue(presenter.doesProductHaveEditPermission(product));
    }

    @Test
    public void testIfProductHasDeletePermission() {
        Product product = new Product();

        when(presenter.hasWriteAccess(TetrisPermissionKey.PRICING_CONFIGURATION)).thenReturn(true);
        when(presenter.hasWriteAccess(TetrisPermissionKey.PRICING_PRODUCT_LIST)).thenReturn(false);
        init();

        product.setCode(Product.AGILE_RATES_PRODUCT_CODE);
        presenter.setIndependentProductsEnabled(true);

        // false as grid permission is set to false
        assertFalse(presenter.doesProductHaveDeletePermission(product));

        // true as independent product permission is set to true
        product.setCode(Product.INDEPENDENT_PRODUCT_CODE);
        assertTrue(presenter.doesProductHaveDeletePermission(product));
    }

    @Test
    public void isFirstESAIndependentProductUploaded() {
        Product barProduct = new Product();
        barProduct.setUpload(true);
        barProduct.setCode("BAR");
        Product uploadedAgileProduct = new Product();
        uploadedAgileProduct.setUpload(true);
        uploadedAgileProduct.setCode(Product.AGILE_RATES_PRODUCT_CODE);
        Product nonUploadedAgileProduct = new Product();
        nonUploadedAgileProduct.setUpload(false);
        nonUploadedAgileProduct.setCode(Product.AGILE_RATES_PRODUCT_CODE);
        Product nonUploadedIndependentProduct = new Product();
        nonUploadedIndependentProduct.setUpload(false);
        nonUploadedIndependentProduct.setCode(Product.INDEPENDENT_PRODUCT_CODE);
        Product uploadedIndependentProduct = new Product();
        uploadedIndependentProduct.setUpload(true);
        uploadedIndependentProduct.setCode(Product.INDEPENDENT_PRODUCT_CODE);
        Product uploadedIndependentProduct2 = new Product();
        uploadedIndependentProduct2.setUpload(true);
        uploadedIndependentProduct2.setCode(Product.INDEPENDENT_PRODUCT_CODE);

        when(automateConfigurationService.isESAProperty()).thenReturn(false);

        presenter.setProducts(Arrays.asList(barProduct, uploadedAgileProduct, nonUploadedAgileProduct, nonUploadedIndependentProduct, uploadedIndependentProduct));
        assertFalse(presenter.isFirstESAIndependentProductUploaded(uploadedIndependentProduct));

        presenter.setProducts(Arrays.asList(barProduct, uploadedAgileProduct, nonUploadedAgileProduct, nonUploadedIndependentProduct, uploadedIndependentProduct, uploadedIndependentProduct2));
        assertFalse(presenter.isFirstESAIndependentProductUploaded(uploadedIndependentProduct2));

        when(automateConfigurationService.isESAProperty()).thenReturn(true);

        presenter.setProducts(Arrays.asList(barProduct, uploadedAgileProduct, nonUploadedAgileProduct, nonUploadedIndependentProduct, uploadedIndependentProduct));
        assertTrue(presenter.isFirstESAIndependentProductUploaded(uploadedIndependentProduct));

        presenter.setProducts(Arrays.asList(barProduct, uploadedAgileProduct, nonUploadedAgileProduct, nonUploadedIndependentProduct, uploadedIndependentProduct, uploadedIndependentProduct2));
        assertFalse(presenter.isFirstESAIndependentProductUploaded(uploadedIndependentProduct2));
    }

    @Test
    public void disableExtendedStayUnqualifiedManagementToggle() {
        WorkContextType workContext = new WorkContextType();
        workContext.setClientCode("ESA");
        workContext.setPropertyCode("TEST");
        PacmanThreadLocalContextHolder.put(Constants.CONTEXT_KEY, workContext);

        ConfigParameter configParameter1 = new ConfigParameter();
        configParameter1.setName(FeatureTogglesConfigParamName.EXTENDED_STAY_UNQUALIFIED_RATE_MANAGEMENT_ENABLED.value());
        ConfigParameter configParameter3 = new ConfigParameter();
        configParameter3.setName(PreProductionConfigParamName.EXTENDED_STAY_PRODUCT_CONFIGURATION_ENABLED.value());
        ConfigParameter configParameter4 = new ConfigParameter();
        configParameter4.setName(PreProductionConfigParamName.EXTENDED_STAY_PRODUCT_PRICING_RECOMMENDATION_ENABLED.value());

        ConfigParameterValue existingParameterValue1 = new ConfigParameterValue();
        ConfigParameterValue existingParameterValue3 = new ConfigParameterValue();
        ConfigParameterValue existingParameterValue4 = new ConfigParameterValue();
        String context = "pacman.ESA.TEST";

        existingParameterValue1.setValue(Boolean.TRUE.toString());
        when(pacmanConfigParamsService.getParameter(FeatureTogglesConfigParamName.EXTENDED_STAY_UNQUALIFIED_RATE_MANAGEMENT_ENABLED.value())).thenReturn(configParameter1);
        when(pacmanConfigParamsService.getParameterValue(context, configParameter1, true)).thenReturn(existingParameterValue1);
        existingParameterValue3.setValue(Boolean.TRUE.toString());
        when(pacmanConfigParamsService.getParameter(PreProductionConfigParamName.EXTENDED_STAY_PRODUCT_CONFIGURATION_ENABLED.value())).thenReturn(configParameter3);
        when(pacmanConfigParamsService.getParameterValue(context, configParameter3, true)).thenReturn(existingParameterValue3);
        existingParameterValue4.setValue(Boolean.TRUE.toString());
        when(pacmanConfigParamsService.getParameter(PreProductionConfigParamName.EXTENDED_STAY_PRODUCT_PRICING_RECOMMENDATION_ENABLED.value())).thenReturn(configParameter4);
        when(pacmanConfigParamsService.getParameterValue(context, configParameter4, true)).thenReturn(existingParameterValue4);

        presenter.disableExtendedStayUnqualifiedManagementToggles();

        verify(pacmanConfigParamsService).updateParameterValue(IntegrationConfigParamName.GENERATE_RATE_EXTRACT.value(Constants.RATCHET), "QRates");
        verify(pacmanConfigParamsService).updateParameterValue(FeatureTogglesConfigParamName.EXTENDED_STAY_UNQUALIFIED_RATE_MANAGEMENT_ENABLED.getParameterName(), Boolean.FALSE);
        verify(pacmanConfigParamsService).updateParameterValue(PreProductionConfigParamName.EXTENDED_STAY_PRODUCT_CONFIGURATION_ENABLED.getParameterName(), Boolean.FALSE);
        verify(pacmanConfigParamsService).updateParameterValue(PreProductionConfigParamName.EXTENDED_STAY_PRODUCT_PRICING_RECOMMENDATION_ENABLED.getParameterName(), Boolean.FALSE);
    }

    @Test
    public void cleanUpFutureDecisionsAndOverridesForOldExtendedStayProducts() {
        Product product = new Product();
        Product product2 = new Product();
        org.joda.time.LocalDate caughtUpLocalDate = new org.joda.time.LocalDate();

        when(agileRatesConfigurationService.findOldExtendedStayProducts()).thenReturn(Arrays.asList(product, product2));
        when(dateService.getCaughtUpLocalDate()).thenReturn(caughtUpLocalDate);

        presenter.cleanUpFutureDecisionsAndOverridesForOldExtendedStayProducts();

        verify(agileRatesConfigurationService).deleteFutureDecisionDailyBarOutput(product, caughtUpLocalDate);
        verify(agileRatesConfigurationService).deleteFutureDecisionDailyBarOutput(product2, caughtUpLocalDate);
        verify(agileRatesConfigurationService).deleteFutureESRateUnqualifiedOverrides(caughtUpLocalDate);
        verify(agileRatesConfigurationService).deleteDailyBarConfiguration();
    }

    @Test
    public void isIndependentProductsOrLinkedProductHierarchyEnabled() {
        presenter.setIndependentProductsEnabled(false);
        presenter.setLinkedProductHierarchyEnabled(false);
        assertFalse(presenter.isIndependentProductsOrLinkedProductHierarchyEnabled());

        presenter.setIndependentProductsEnabled(true);
        presenter.setLinkedProductHierarchyEnabled(false);
        assertTrue(presenter.isIndependentProductsOrLinkedProductHierarchyEnabled());

        presenter.setIndependentProductsEnabled(false);
        presenter.setLinkedProductHierarchyEnabled(true);
        assertTrue(presenter.isIndependentProductsOrLinkedProductHierarchyEnabled());
    }

    @Test
    public void testCopySmallGroupProduct() {
        presenter.setGroupProductsEnabled(true);

        Product originalProduct = new Product();
        originalProduct.setDisplayOrder(0);
        originalProduct.setCode(GROUP_PRODUCT_CODE);
        ProductCode productCode = new ProductCode(3);
        originalProduct.setProductCode(productCode);
        presenter.setProducts(new ArrayList<>(Arrays.asList(originalProduct)));

        String newName = "newSmallGroupProduct";
        AgileRatesProductConfigurationDTO copiedDTO = new AgileRatesProductConfigurationDTO();
        Date date = org.joda.time.LocalDate.now().toDate();

        when(agileRatesConfigurationService.copySmallGroupProduct(originalProduct, newName, 1, LocalDate.now())).thenReturn(copiedDTO);
        when(dateService.getCaughtUpDate()).thenReturn(date);

        presenter.copyProduct(originalProduct, newName);

        verify(agileRatesConfigurationService).saveSmallGroupProductConfiguration(copiedDTO);
        verify(agileRatesConfigurationService, never()).copyDefaultOffsetMappings(originalProduct, copiedDTO.getProduct());
        verify(agileRatesConfigurationService, never()).copyDefaultCeilingFloorMappings(originalProduct, copiedDTO.getProduct());
        verify(agileRatesConfigurationService, never()).copyOffsetMappings(originalProduct, copiedDTO.getProduct());

        final Consumer<AgileRatesProductListingPresenter> methodConsumer = presenter1 -> presenter1.copyProduct(originalProduct, newName);
        verify(forceSyncEvent).fire(any(ForceSyncEvent.class));
        verifySync(presenter, methodConsumer);
        verifyInitCalledOndMethod(presenter, methodConsumer);
    }

    @Test
    public void testDeleteSmallGroupProduct_deleteRestrictionAssociation() {
        presenter.setGroupProductsEnabled(true);
        presenter.setProducts(new ArrayList<>());

        Product product = new Product();
        product.setCode(GROUP_PRODUCT_CODE);
        ProductCode productCode = new ProductCode(3);
        product.setProductCode(productCode);
        product.setDisplayOrder(1);

        Date date = org.joda.time.LocalDate.now().toDate();
        when(dateService.getCaughtUpDate()).thenReturn(date);
        doNothing().when(view).loadProducts(Arrays.asList(product));

        presenter.deleteProduct(product);

        verify(agileRatesConfigurationService).deleteSmallGroupProduct(product, localDate);
        verify(agileRatesConfigurationService).saveAgileProductRestrictionAssociationForFPLOS(product.getId(), Collections.EMPTY_SET);
        verify(agileRatesConfigurationService).shiftProductDisplayOrder(new ArrayList<>());
        verify(view).loadProducts(new ArrayList<>());
        final Consumer<AgileRatesProductListingPresenter> methodConsumer = presenter1 -> presenter1.deleteProduct(product);
        verify(forceSyncEvent).fire(any(ForceSyncEvent.class));
        verifySync(presenter, methodConsumer);
        verifyInitCalledOndMethod(presenter, methodConsumer);
    }

    @Test
    public void testDeleteSmallGroupProduct_notDeleteRestrictionAssociation() {
        presenter.setGroupProductsEnabled(true);
        presenter.setProducts(new ArrayList<>());

        Product product = new Product();
        product.setCode(GROUP_PRODUCT_CODE);
        ProductCode productCode = new ProductCode(3);
        product.setProductCode(productCode);
        product.setDisplayOrder(1);

        Date date = org.joda.time.LocalDate.now().toDate();
        when(dateService.getCaughtUpDate()).thenReturn(date);
        doNothing().when(view).loadProducts(Arrays.asList(product));

        presenter.deleteProduct(product);

        verify(agileRatesConfigurationService).deleteSmallGroupProduct(product, localDate);
        verify(agileRatesConfigurationService).shiftProductDisplayOrder(new ArrayList<>());
        verify(view).loadProducts(new ArrayList<>());
        final Consumer<AgileRatesProductListingPresenter> methodConsumer = presenter1 -> presenter1.deleteProduct(product);
        verify(forceSyncEvent).fire(any(ForceSyncEvent.class));
        verifySync(presenter, methodConsumer);
        verifyInitCalledOndMethod(presenter, methodConsumer);
    }

    @Test
    void shouldReturnTrueWhenEnableSmallGroupMinMaxRoomPopupToggleIsEnabled() {
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_SMALL_GRP_MIN_MAX_ROOM_POPUP)).thenReturn(true);

        assertTrue(presenter.isSmallGroupProductMinMaxRoomPopUpEnabled());
    }

    @Test
    void testIsSmallGroupProductMinMaxRoomPopUpEnabled_WhenParamIsFalse_ReturnsFalse() {
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_SMALL_GRP_MIN_MAX_ROOM_POPUP)).thenReturn(false);

        assertFalse(presenter.isSmallGroupProductMinMaxRoomPopUpEnabled());
    }

    @Test
    void shouldReturnEmptyProductListWhenThereIsOnlyOneSmallGroupProductAndDeletingTheSame() {
        Product productToDelete = getSmallGroupProduct(1, 10, 30);

        List<Product> allProducts = List.of(productToDelete);
        presenter.setAllProducts(allProducts);

        List<Product> result = presenter.getAllInvalidSmallGroupProductsBasedOnMinMaxRoomAfterDelete(productToDelete);

        assertTrue(result.isEmpty());
    }

    @Test
    void shouldReturnEmptyProductListWhenThereIsOnlyOneSmallGroupProductAndDeletingTheSameWhenOtherProductsAreAlsoAvailable() {
        Product productToDelete = getSmallGroupProduct(1, 10, 30);
        Product bar = getBarProduct();
        Product independentProduct = getIndependentProduct();

        List<Product> allProducts = List.of(productToDelete, bar, independentProduct);
        presenter.setAllProducts(allProducts);

        List<Product> result = presenter.getAllInvalidSmallGroupProductsBasedOnMinMaxRoomAfterDelete(productToDelete);

        assertTrue(result.isEmpty());
    }

    @Test
    void shouldReturnEmptyProductListWhenDeletingSmallGroupProductHavingSmallestMinRoom() {
        Product sg1 = getSmallGroupProduct(1, 10, 30);
        Product sg2 = getSmallGroupProduct(11, 20, 31);
        Product sg3 = getSmallGroupProduct(21, 30, 31);
        Product bar = getBarProduct();
        Product independentProduct = getIndependentProduct();
        List<Product> allProducts = List.of(sg1, sg2, sg3, bar, independentProduct);

        presenter.setAllProducts(allProducts);
        Map<Product, Integer> minRoomMap = new HashMap<>();
        minRoomMap.put(sg2, 11);

        Map<Product, Integer> maxRoomMap = new HashMap<>();
        maxRoomMap.put(sg2, 20);
        when(smallGroupWizardPresenter.validateSequenceOfMinMaxRoomValues(minRoomMap, maxRoomMap)).thenReturn(true);

        List<Product> result = presenter.getAllInvalidSmallGroupProductsBasedOnMinMaxRoomAfterDelete(sg1);

        assertTrue(result.isEmpty());
    }

    @Test
    void shouldReturnEmptyProductListWhenDeletingSmallGroupProductHavingLargestMaxRoom() {
        Product sg1 = getSmallGroupProduct(1, 10, 30);
        Product sg2 = getSmallGroupProduct(11, 20, 31);
        Product sg3 = getSmallGroupProduct(21, 30, 32);
        Product bar = getBarProduct();
        Product independentProduct = getIndependentProduct();
        List<Product> allProducts = List.of(sg1, sg2, sg3, bar, independentProduct);

        presenter.setAllProducts(allProducts);
        Map<Product, Integer> minRoomMap = new HashMap<>();
        minRoomMap.put(sg2, 11);

        Map<Product, Integer> maxRoomMap = new HashMap<>();
        maxRoomMap.put(sg2, 20);
        when(smallGroupWizardPresenter.validateSequenceOfMinMaxRoomValues(minRoomMap, maxRoomMap)).thenReturn(true);

        List<Product> result = presenter.getAllInvalidSmallGroupProductsBasedOnMinMaxRoomAfterDelete(sg3);

        assertTrue(result.isEmpty());
    }

    @Test
    void shouldReturnOnlyTwoNearestMinMaxRoomProductWhenDeletingSmallGroupProduct() {
        Product sg1 = getSmallGroupProduct(1, 10, 30);
        Product sg2 = getSmallGroupProduct(11, 20, 31);
        Product sg3 = getSmallGroupProduct(21, 30, 32);
        Product sg4 = getSmallGroupProduct(31, 40, 33);
        Product bar = getBarProduct();
        Product independentProduct = getIndependentProduct();
        List<Product> allProducts = List.of(sg1, sg2, sg3, sg4, bar, independentProduct);

        presenter.setAllProducts(allProducts);
        Map<Product, Integer> minRoomMap = new HashMap<>();
        minRoomMap.put(sg1, 1);
        minRoomMap.put(sg3, 21);

        Map<Product, Integer> maxRoomMap = new HashMap<>();
        maxRoomMap.put(sg1, 10);
        maxRoomMap.put(sg3, 30);
        when(smallGroupWizardPresenter.validateSequenceOfMinMaxRoomValues(minRoomMap, maxRoomMap)).thenReturn(false);

        List<Product> result = presenter.getAllInvalidSmallGroupProductsBasedOnMinMaxRoomAfterDelete(sg2);

        assertFalse(result.isEmpty());
        assertEquals(2, result.size());
        assertEquals(sg1, result.get(0));
        assertEquals(sg3, result.get(1));
    }

    @Test
    void shouldReturnOnlyTwoNearestMinMaxRoomProductEvenWhenProductsAreInvalidWhenDeletingSmallGroupProduct() {
        Product sg1 = getSmallGroupProduct(1, 10, 30);
        sg1.setStatus(TenantStatusEnum.INVALID);
        Product sg2 = getSmallGroupProduct(11, 20, 31);
        Product sg3 = getSmallGroupProduct(21, 30, 32);
        sg3.setStatus(TenantStatusEnum.INVALID);
        Product sg4 = getSmallGroupProduct(31, 40, 33);
        Product bar = getBarProduct();
        Product independentProduct = getIndependentProduct();
        List<Product> allProducts = List.of(sg1, sg2, sg3, sg4, bar, independentProduct);

        presenter.setAllProducts(allProducts);
        Map<Product, Integer> minRoomMap = new HashMap<>();
        minRoomMap.put(sg1, 1);
        minRoomMap.put(sg3, 21);

        Map<Product, Integer> maxRoomMap = new HashMap<>();
        maxRoomMap.put(sg1, 10);
        maxRoomMap.put(sg3, 30);
        when(smallGroupWizardPresenter.validateSequenceOfMinMaxRoomValues(minRoomMap, maxRoomMap)).thenReturn(false);

        List<Product> result = presenter.getAllInvalidSmallGroupProductsBasedOnMinMaxRoomAfterDelete(sg2);

        assertFalse(result.isEmpty());
        assertEquals(2, result.size());
        assertEquals(sg1, result.get(0));
        assertEquals(sg3, result.get(1));
    }

    @Test
    void shouldReturnOnlyTwoNearestMinMaxRoomProductEvenWhenProductsAreInactiveWhenDeletingSmallGroupProduct() {
        Product sg1 = getSmallGroupProduct(1, 10, 30);
        sg1.setStatus(TenantStatusEnum.INACTIVE);
        Product sg2 = getSmallGroupProduct(11, 20, 31);
        Product sg3 = getSmallGroupProduct(21, 30, 32);
        sg3.setStatus(TenantStatusEnum.INACTIVE);
        Product sg4 = getSmallGroupProduct(31, 40, 33);
        Product bar = getBarProduct();
        Product independentProduct = getIndependentProduct();
        List<Product> allProducts = List.of(sg1, sg2, sg3, sg4, bar, independentProduct);

        presenter.setAllProducts(allProducts);

        Map<Product, Integer> minRoomMap = new HashMap<>();
        minRoomMap.put(sg1, 1);
        minRoomMap.put(sg3, 21);

        Map<Product, Integer> maxRoomMap = new HashMap<>();
        maxRoomMap.put(sg1, 10);
        maxRoomMap.put(sg3, 30);
        when(smallGroupWizardPresenter.validateSequenceOfMinMaxRoomValues(minRoomMap, maxRoomMap)).thenReturn(false);

        List<Product> result = presenter.getAllInvalidSmallGroupProductsBasedOnMinMaxRoomAfterDelete(sg2);

        assertFalse(result.isEmpty());
        assertEquals(2, result.size());
        assertEquals(sg1, result.get(0));
        assertEquals(sg3, result.get(1));
    }

    @Test
    void shouldReturnOnlyTwoNearestMinMaxRoomProductEvenWhenProductsAreInactiveOrInvalidWhenDeletingSmallGroupProduct() {
        Product sg1 = getSmallGroupProduct(1, 10, 30);
        sg1.setStatus(TenantStatusEnum.INVALID);
        Product sg2 = getSmallGroupProduct(11, 20, 31);
        Product sg3 = getSmallGroupProduct(21, 30, 32);
        sg3.setStatus(TenantStatusEnum.INACTIVE);
        Product sg4 = getSmallGroupProduct(31, 40, 33);
        Product bar = getBarProduct();
        Product independentProduct = getIndependentProduct();
        List<Product> allProducts = List.of(sg1, sg2, sg3, sg4, bar, independentProduct);

        Map<Product, Integer> minRoomMap = new HashMap<>();
        minRoomMap.put(sg1, 1);
        minRoomMap.put(sg3, 21);

        Map<Product, Integer> maxRoomMap = new HashMap<>();
        maxRoomMap.put(sg1, 10);
        maxRoomMap.put(sg3, 30);


        presenter.setAllProducts(allProducts);
        when(smallGroupWizardPresenter.validateSequenceOfMinMaxRoomValues(minRoomMap, maxRoomMap)).thenReturn(false);

        List<Product> result = presenter.getAllInvalidSmallGroupProductsBasedOnMinMaxRoomAfterDelete(sg2);

        assertFalse(result.isEmpty());
        assertEquals(2, result.size());
        assertEquals(sg1, result.get(0));
        assertEquals(sg3, result.get(1));
    }

    @Test
    void shouldReturnOnlyTwoNearestMinMaxRoomProductWhenDeletingSmallGroupProduct_WithLargestSGProduct() {
        Product sg1 = getSmallGroupProduct(1, 10, 30);
        Product sg2 = getSmallGroupProduct(11, 20, 31);
        Product sg3 = getSmallGroupProduct(21, 30, 32);
        Product sg4 = getSmallGroupProduct(31, null, 33);
        Product bar = getBarProduct();
        Product independentProduct = getIndependentProduct();
        List<Product> allProducts = List.of(sg1, sg2, sg3, sg4, bar, independentProduct);

        presenter.setAllProducts(allProducts);
        Map<Product, Integer> minRoomMap = new HashMap<>();
        minRoomMap.put(sg1, 1);
        minRoomMap.put(sg3, 21);

        Map<Product, Integer> maxRoomMap = new HashMap<>();
        maxRoomMap.put(sg1, 10);
        maxRoomMap.put(sg3, 30);
        when(smallGroupWizardPresenter.validateSequenceOfMinMaxRoomValues(minRoomMap, maxRoomMap)).thenReturn(false);

        List<Product> result = presenter.getAllInvalidSmallGroupProductsBasedOnMinMaxRoomAfterDelete(sg2);

        assertFalse(result.isEmpty());
        assertEquals(2, result.size());
        assertEquals(sg1, result.get(0));
        assertEquals(sg3, result.get(1));
    }

    @Test
    void shouldReturnOnlyTwoNearestMinMaxRoomProductWhenDeletingSmallGroupProduct_WithLargestSGProductAtTheNearest() {
        Product sg1 = getSmallGroupProduct(1, 10, 30);
        Product sg2 = getSmallGroupProduct(11, 20, 31);
        Product sg3 = getSmallGroupProduct(21, 30, 32);
        Product sg4 = getSmallGroupProduct(31, null, 33);
        Product bar = getBarProduct();
        Product independentProduct = getIndependentProduct();
        List<Product> allProducts = List.of(sg1, sg2, sg3, sg4, bar, independentProduct);

        presenter.setAllProducts(allProducts);
        Map<Product, Integer> minRoomMap = new HashMap<>();
        minRoomMap.put(sg1, 1);
        minRoomMap.put(sg3, 21);

        Map<Product, Integer> maxRoomMap = new HashMap<>();
        maxRoomMap.put(sg1, 10);
        maxRoomMap.put(sg3, 30);
        when(smallGroupWizardPresenter.validateSequenceOfMinMaxRoomValues(minRoomMap, maxRoomMap)).thenReturn(false);

        List<Product> result = presenter.getAllInvalidSmallGroupProductsBasedOnMinMaxRoomAfterDelete(sg3);

        assertFalse(result.isEmpty());
        assertEquals(2, result.size());
        assertEquals(sg2, result.get(0));
        assertEquals(sg4, result.get(1));
    }

    @Test
    void shouldReturnEmptyListWhenDeletingLargestSmallGroupProduct() {
        Product sg1 = getSmallGroupProduct(1, 10, 30);
        Product sg2 = getSmallGroupProduct(11, 20, 31);
        Product sg3 = getSmallGroupProduct(21, null, 32);
        Product bar = getBarProduct();
        Product independentProduct = getIndependentProduct();
        List<Product> allProducts = List.of(sg1, sg2, sg3, bar, independentProduct);

        presenter.setAllProducts(allProducts);
        Map<Product, Integer> minRoomMap = new HashMap<>();
        minRoomMap.put(sg2, 11);

        Map<Product, Integer> maxRoomMap = new HashMap<>();
        maxRoomMap.put(sg2, 20);
        when(smallGroupWizardPresenter.validateSequenceOfMinMaxRoomValues(minRoomMap, maxRoomMap)).thenReturn(true);

        List<Product> result = presenter.getAllInvalidSmallGroupProductsBasedOnMinMaxRoomAfterDelete(sg3);

        assertTrue(result.isEmpty());
    }


    @Test
    void shouldDeleteGroupProductAndInvalidateOtherGroupProductsWhenSmallGroupMinMaxRoomPopUpIsToggleEnabled() {

        Product sg1 = getSmallGroupProduct(1, 10, 30);
        sg1.setStatus(TenantStatusEnum.ACTIVE);
        Product sg2 = getSmallGroupProduct(11, 20, 31);
        Product sg3 = getSmallGroupProduct(21, 30, 32);
        sg3.setStatus(TenantStatusEnum.ACTIVE);
        Product sg4 = getSmallGroupProduct(31, 40, 33);
        Product bar = getBarProduct();
        Product independentProduct = getIndependentProduct();
        List<Product> allProducts = List.of(sg1, sg2, sg3, sg4, bar, independentProduct);

        Map<Product, Integer> minRoomMap = new HashMap<>();
        minRoomMap.put(sg1, 1);
        minRoomMap.put(sg3, 21);

        Map<Product, Integer> maxRoomMap = new HashMap<>();
        maxRoomMap.put(sg1, 10);
        maxRoomMap.put(sg3, 30);

        presenter.setProducts(new ArrayList<>());
        presenter.setGroupProductsEnabled(true);
        presenter.setAllProducts(allProducts);
        when(smallGroupWizardPresenter.validateSequenceOfMinMaxRoomValues(minRoomMap, maxRoomMap)).thenReturn(false);
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_SMALL_GRP_MIN_MAX_ROOM_POPUP)).thenReturn(true);
        Date caughtUpDate = JavaLocalDateUtils.toJodaLocalDate(LocalDate.parse("2024-01-01")).toDate();
        when(dateService.getCaughtUpDate()).thenReturn(caughtUpDate);

        presenter.deleteProduct(sg2);

        verify(agileRatesConfigurationService).saveProducts(productArgumentCaptor.capture());
        List<Product> invalidatedProducts = productArgumentCaptor.getValue();
        assertEquals(2, invalidatedProducts.size());
        assertEquals(InvalidReason.INVALID_SEQUENCE, invalidatedProducts.get(0).getInvalidReason());
        assertEquals(TenantStatusEnum.INVALID, invalidatedProducts.get(0).getStatus());
        assertEquals(InvalidReason.INVALID_SEQUENCE, invalidatedProducts.get(1).getInvalidReason());
        assertEquals(TenantStatusEnum.INVALID, invalidatedProducts.get(1).getStatus());
        verify(agileRatesConfigurationService).deleteSmallGroupProduct(sg2, JavaLocalDateUtils.toJodaLocalDate(LocalDate.parse("2024-01-01")));
    }

    @Test
    void shouldDeleteGroupProductAndButNotInvalidateOtherGroupProductsWhenSmallGroupMinMaxRoomPopUpIsToggleDisabled() {
        Product sg1 = getSmallGroupProduct(1, 10, 30);
        sg1.setStatus(TenantStatusEnum.ACTIVE);
        Product sg2 = getSmallGroupProduct(11, 20, 31);
        Product sg3 = getSmallGroupProduct(21, 30, 32);
        sg3.setStatus(TenantStatusEnum.ACTIVE);
        Product sg4 = getSmallGroupProduct(31, 40, 33);
        Product bar = getBarProduct();
        Product independentProduct = getIndependentProduct();
        List<Product> allProducts = List.of(sg1, sg2, sg3, sg4, bar, independentProduct);

        Map<Product, Integer> minRoomMap = new HashMap<>();
        minRoomMap.put(sg1, 1);
        minRoomMap.put(sg3, 21);

        Map<Product, Integer> maxRoomMap = new HashMap<>();
        maxRoomMap.put(sg1, 10);
        maxRoomMap.put(sg3, 30);

        presenter.setProducts(new ArrayList<>());
        presenter.setGroupProductsEnabled(true);
        presenter.setAllProducts(allProducts);
        when(smallGroupWizardPresenter.validateSequenceOfMinMaxRoomValues(minRoomMap, maxRoomMap)).thenReturn(false);
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_SMALL_GRP_MIN_MAX_ROOM_POPUP)).thenReturn(false);
        Date caughtUpDate = JavaLocalDateUtils.toJodaLocalDate(LocalDate.parse("2024-01-01")).toDate();
        when(dateService.getCaughtUpDate()).thenReturn(caughtUpDate);

        presenter.deleteProduct(sg2);

        verify(agileRatesConfigurationService, times(0)).saveProducts(List.of(sg1, sg3));
        verify(agileRatesConfigurationService).deleteSmallGroupProduct(sg2, JavaLocalDateUtils.toJodaLocalDate(LocalDate.parse("2024-01-01")));
    }

    @Test
    void shouldReturnEmptyProductListWhenSequenceIsNotCorrectAndProductsAreInvalid() {
        Product sg1 = getSmallGroupProduct(1, 8, 30);
        sg1.setStatus(TenantStatusEnum.INVALID);
        Product sg2 = getSmallGroupProduct(11, 20, 31);
        Product sg3 = getSmallGroupProduct(23, 30, 32);
        sg3.setStatus(TenantStatusEnum.INVALID);
        Product sg4 = getSmallGroupProduct(31, 40, 33);
        Product bar = getBarProduct();
        Product independentProduct = getIndependentProduct();
        List<Product> allProducts = List.of(sg1, sg2, sg3, sg4, bar, independentProduct);

        presenter.setAllProducts(allProducts);
        Map<Product, Integer> minRoomMap = new HashMap<>();
        minRoomMap.put(sg1, 1);
        minRoomMap.put(sg3, 21);

        Map<Product, Integer> maxRoomMap = new HashMap<>();
        maxRoomMap.put(sg1, 10);
        maxRoomMap.put(sg3, 30);
        when(smallGroupWizardPresenter.validateSequenceOfMinMaxRoomValues(minRoomMap, maxRoomMap)).thenReturn(false);

        List<Product> result = presenter.getAllInvalidSmallGroupProductsBasedOnMinMaxRoomAfterDelete(sg2);

        assertTrue(result.isEmpty());
    }

    @Test
    void isAdvancedPricingConfigurationEnabledInLicensePackage(){
        WorkContextType workContextType = new WorkContextType();
        workContextType.setPropertyId(5);
        PacmanWorkContextHelper.setWorkContext(workContextType);
        presenter.isAdvancedPricingConfigurationEnabledInLicensePackage();

        verify(licenseService).isLicenseFeatureValueConfigured(5, ADVANCED_PRICING_CONFIGURATION);
    }

    private Product getIndependentProduct() {
        Product independentProduct = new Product();
        independentProduct.setId(20);
        independentProduct.setCode(Product.INDEPENDENT_PRODUCT_CODE);
        return independentProduct;
    }

    private Product getBarProduct() {
        Product bar = new Product();
        bar.setId(1);
        bar.setSystemDefault(true);
        return bar;
    }

    private Product getSmallGroupProduct(Integer minRoom, Integer maxRoom, Integer productId) {
        Product smallGroupProduct = new Product();
        smallGroupProduct.setCode(GROUP_PRODUCT_CODE);
        smallGroupProduct.setId(productId);
        smallGroupProduct.setMinRooms(minRoom);
        smallGroupProduct.setMaxRooms(maxRoom);
        return smallGroupProduct;
    }

}