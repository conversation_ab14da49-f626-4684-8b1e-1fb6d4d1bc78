package com.ideas.tetris.ui.modules.internalalert;

import com.ideas.tetris.ui.common.cdi.TetrisNavigatorView;
import com.ideas.tetris.ui.common.component.CheckboxColumnSupport;
import com.ideas.tetris.ui.common.component.TetrisBeanItemContainer;
import com.ideas.tetris.ui.common.component.button.TetrisActionButton;
import com.ideas.tetris.ui.common.component.button.TetrisButton;
import com.ideas.tetris.ui.common.component.button.TetrisExportXlsImageButton;
import com.ideas.tetris.ui.common.component.button.TetrisImageButton;
import com.ideas.tetris.ui.common.component.filter.TetrisFilterPopup;
import com.ideas.tetris.ui.common.component.fontawesome.TetrisFontAwesome;
import com.ideas.tetris.ui.common.component.table.TetrisTable;
import com.ideas.tetris.ui.common.util.RefreshAware;
import com.ideas.tetris.ui.modules.reports.BaseVaadinExporter;
import com.ideas.tetris.ui.modules.reports.ExportProvider;
import com.ideas.tetris.ui.modules.reports.util.ReportCriteria;
import com.vaadin.annotations.Title;
import com.vaadin.ui.Alignment;
import com.vaadin.ui.Component;
import com.vaadin.v7.data.Property;
import com.vaadin.v7.ui.HorizontalLayout;
import com.vaadin.v7.ui.VerticalLayout;
import de.steinwedel.messagebox.ButtonId;
import de.steinwedel.messagebox.MessageBoxListener;

import java.util.ArrayList;
import java.util.List;

@Title("Alert Details")
public class AlertDetailsView extends TetrisNavigatorView<AlertDetailsPresenter, Void> implements RefreshAware {
    TetrisTable alertTable;
    private TetrisBeanItemContainer<InternalAlertDisplayDto> tetrisBeanItemContainer;
    private CheckboxColumnSupport checkboxColumnSupport;
    private TetrisFilterPopup filterPopup;
    private InternalAlertFilterPopup filter;

    @Override
    protected void initView() {
        // letting presenter control view
    }

    protected void initViewInternal() {
        setSizeFull();
        VerticalLayout mainLayout = new VerticalLayout();
        mainLayout.setSizeFull();
        mainLayout.setMargin(true);
        mainLayout.setSpacing(true);

        HorizontalLayout summaryLayout = new HorizontalLayout();
        summaryLayout.setWidth(100, Unit.PERCENTAGE);
        summaryLayout.setSpacing(true);
        addBulkActionButton(summaryLayout);
        TetrisImageButton filterButton = getFilterButton();
        summaryLayout.addComponent(filterButton);
        summaryLayout.setComponentAlignment(filterButton, Alignment.TOP_LEFT);
        summaryLayout.setExpandRatio(filterButton, 0.1f);

        TetrisImageButton exportButton = getExportButton();
        summaryLayout.addComponent(exportButton);
        summaryLayout.setComponentAlignment(exportButton, Alignment.MIDDLE_RIGHT);
        summaryLayout.setExpandRatio(exportButton, 0.9f);

        mainLayout.addComponent(summaryLayout);

        HorizontalLayout horizontalLayout = new HorizontalLayout();
        horizontalLayout.setSpacing(true);
        horizontalLayout.setSizeFull();
        addTable(horizontalLayout);
        mainLayout.addComponent(horizontalLayout);
        mainLayout.setExpandRatio(horizontalLayout, 1.0f);
        setCompositionRoot(mainLayout);

        // Populate table with default filter
        filter = new InternalAlertFilterPopup(presenter);
        presenter.setFilterDto((InternalAlertFilterDto) filter.getFilterResult());
    }

    private TetrisImageButton getExportButton() {
        return new TetrisExportXlsImageButton(new ExportProvider() {
            @Override
            public TetrisTable getComponent() {
                return alertTable;
            }

            @Override
            public BaseVaadinExporter getExporter() {
                return new InternalAlertExporter();
            }

            @Override
            public ReportCriteria getReportCriteria() {
                return null;
            }
        }, "Internal Alerts");
    }

    private TetrisImageButton getFilterButton() {
        TetrisImageButton filterButton = new TetrisImageButton(TetrisFontAwesome.FILTER, new TetrisButton.TetrisPopupContentProvider() {
            @Override
            public Component getContent(TetrisButton parentButton) {
                if (filterPopup == null) {
                    filterPopup = new TetrisFilterPopup(parentButton, filter);
                    filterPopup.addFilterChangeListener(new TetrisFilterPopup.FilterChangedListener() {
                        @Override
                        public void filterChanged(TetrisFilterPopup.FilterChangeEvent event) {
                            InternalAlertFilterDto filterDto = (InternalAlertFilterDto) filter.getFilterResult();
                            presenter.setFilterDto(filterDto);
                        }
                    });
                }
                filter.updateFilters(presenter.getFilterDto());
                return filterPopup;
            }
        });
        filterButton.removeButtonBorder();
        filterButton.setAutoClose(false);
        return filterButton;
    }

    private void addBulkActionButton(HorizontalLayout mainLayout) {
        ArrayList<TetrisActionButton.Action> actions = new ArrayList<TetrisActionButton.Action>();
        actions.add(new TetrisActionButton.Action("Resolve", presenter.RESOLVE_ACTION));

        TetrisActionButton actionButton = new TetrisActionButton("Actions", actions);
        actionButton.addClickListener(new TetrisActionButton.ClickListener() {
            @Override
            public void onClicked(final TetrisActionButton.ClickedEvent event) {
                final String action = (String) event.getAction().getData();
                openConfirmationDialog(action);
            }
        });

        mainLayout.addComponent(actionButton);
    }


    @SuppressWarnings("unchecked")
    private void openConfirmationDialog(final String action) {
        final List<InternalAlertDisplayDto> alerts = (List<InternalAlertDisplayDto>) (List<?>) checkboxColumnSupport.getSelectedItems();
        String message = action;
        message += (alerts.size() > 1 ? " internal alerts?" : " internal alert?");

        showAlert("Confirm Internal Alert Action", message, true, new MessageBoxListener() {
            @Override
            public void buttonClicked(ButtonId buttonId) {
                if (buttonId.equals(ButtonId.OK)) {
                    performBulk(action);
                }
            }
        });
    }

    @SuppressWarnings("unchecked")
    private void performBulk(String action) {
        final List<InternalAlertDisplayDto> alerts = (List<InternalAlertDisplayDto>) (List<?>) checkboxColumnSupport.getSelectedItems();
        presenter.performBulkAction(action, alerts);
    }


    protected void addTable(HorizontalLayout layout) {
        alertTable = new TetrisTable() {
            @SuppressWarnings("rawtypes")
            @Override
            protected String formatPropertyValue(Object rowId, Object colId, Property property) {
                Object value = property.getValue();
                if ("stage".equals(colId)) {
                    String stage = (String) property.getValue();
                    if (stage != null) {
                        return getText(stage.toLowerCase());
                    }
                }
                return super.formatPropertyValue(rowId, colId, property);
            }
        };
        alertTable.setDisplayRowCount(true);

        tetrisBeanItemContainer = new TetrisBeanItemContainer<InternalAlertDisplayDto>(InternalAlertDisplayDto.class);

        alertTable.setContainerDataSource(tetrisBeanItemContainer);

        String internalAlertIdCol = "internalAlertId";
        String alertStatusCol = "alertStatus";
        String detailsCol = "details";
        String createdDateCol = "createdDate";
        String notifiedDateCol = "notifiedDate";
        String resolvedDateCol = "resolvedDate";
        String stageCol = "stage";
        String alertTypeCol = "alertType";
        String propertyCodeCol = "propertyCode";
        String propertyNameCol = "propertyName";
        String clientCodeCol = "clientCode";
        String clientNameCol = "clientName";

        checkboxColumnSupport = new CheckboxColumnSupport(alertTable);
        alertTable.setVisibleColumns(new Object[]{CheckboxColumnSupport.CHECKBOX_PROPERTY_ID, internalAlertIdCol, alertTypeCol, detailsCol, alertStatusCol, createdDateCol,
                clientNameCol, clientCodeCol, propertyNameCol, propertyCodeCol, stageCol, notifiedDateCol, resolvedDateCol});
        alertTable.setColumnHeaders("", "Internal Alert ID", "Alert Type", "Details", "Alert Status", "Created Date", "Client Name", "Client Code", "PropertyName", "PropertyCode", "System Mode", "Notified Date", "Resolved Date");
        alertTable.setSizeFull();
        alertTable.setColumnCollapsingAllowed(true);
        alertTable.setColumnCollapsed(internalAlertIdCol, true);
        alertTable.setColumnCollapsed(clientCodeCol, true);
        alertTable.setColumnCollapsed(propertyCodeCol, true);
        alertTable.setColumnCollapsed(notifiedDateCol, true);
        alertTable.setColumnCollapsed(resolvedDateCol, true);

        layout.addComponent(alertTable);
    }

    @Override
    public void refresh() {
        presenter.updateData();
    }

    public void updateData(List<InternalAlertDisplayDto> results) {
        tetrisBeanItemContainer.removeAllItems();
        tetrisBeanItemContainer.addAll(results);
    }

}
