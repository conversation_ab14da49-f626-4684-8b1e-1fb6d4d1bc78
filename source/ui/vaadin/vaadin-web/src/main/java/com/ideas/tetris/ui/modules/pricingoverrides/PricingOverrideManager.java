package com.ideas.tetris.ui.modules.pricingoverrides;

import com.ideas.infra.tetris.security.domain.TetrisPermissionKey;
import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.common.utils.systemconfig.SystemConfig;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomClass;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomType;
import com.ideas.tetris.pacman.services.agilerates.configuration.entity.ProductGroup;
import com.ideas.tetris.pacman.services.agilerates.configuration.entity.ProductPackage;
import com.ideas.tetris.pacman.services.agilerates.configuration.entity.ProductRateOffsetOverride;
import com.ideas.tetris.pacman.services.agilerates.configuration.service.AgileRatesConfigurationService;
import com.ideas.tetris.pacman.services.bestavailablerate.*;
import com.ideas.tetris.pacman.services.bestavailablerate.dto.BAROverride;
import com.ideas.tetris.pacman.services.bestavailablerate.dto.CPBARDecisionDTO;
import com.ideas.tetris.pacman.services.bestavailablerate.dto.CPRoomClassDTO;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.*;
import com.ideas.tetris.pacman.services.businessanalysis.BusinessAnalysisDashboardService;
import com.ideas.tetris.pacman.services.businessanalysis.dto.BusinessAnalysisDailyDataDto;
import com.ideas.tetris.pacman.services.businessanalysis.dto.BusinessAnalysisDailyIndicatorDto;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.TenantStatusEnum;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.dateservice.dto.DateParameter;
import com.ideas.tetris.pacman.services.decision.DecisionService;
import com.ideas.tetris.pacman.services.decisiondelivery.dto.InventoryLimitDecision;
import com.ideas.tetris.pacman.services.demandoverride.entity.LastRoomValue;
import com.ideas.tetris.pacman.services.groupflooroverride.GroupFloorOverrideService;
import com.ideas.tetris.pacman.services.groupflooroverride.entity.GroupFloorOverride;
import com.ideas.tetris.pacman.services.hospitalityrooms.service.HospitalityRoomsService;
import com.ideas.tetris.pacman.services.inventorylimit.InventoryLimitDecisionService;
import com.ideas.tetris.pacman.services.inventorylimit.entity.DecisionGPInventoryLimitOverride;
import com.ideas.tetris.pacman.services.perpersonpricing.MaximumOccupantsEntity;
import com.ideas.tetris.pacman.services.perpersonpricing.OccupantBucketEntity;
import com.ideas.tetris.pacman.services.perpersonpricing.PerPersonPricingService;
import com.ideas.tetris.pacman.services.pricing.ProductManagementService;
import com.ideas.tetris.pacman.services.pricingconfiguration.entity.PricingAccomClass;
import com.ideas.tetris.pacman.services.pricingconfiguration.service.PricingConfigurationService;
import com.ideas.tetris.pacman.services.pricingconfiguration.service.Supplement;
import com.ideas.tetris.pacman.services.product.Product;
import com.ideas.tetris.pacman.services.property.PropertyService;
import com.ideas.tetris.pacman.services.restfortesting.ForTesting;
import com.ideas.tetris.pacman.services.sasoptimization.dto.SimplifiedWhatIfResult;
import com.ideas.tetris.pacman.services.sasoptimization.service.SimplifiedWhatIfService;
import com.ideas.tetris.pacman.services.tax.entity.Tax;
import com.ideas.tetris.pacman.services.tax.service.TaxService;
import com.ideas.tetris.pacman.util.BigDecimalUtil;
import com.ideas.tetris.platform.common.entity.DayOfWeek;
import com.ideas.tetris.platform.common.time.LocalDateUtils;
import com.ideas.tetris.platform.services.Stage;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import com.ideas.tetris.platform.services.daoandentities.entity.Status;
import com.ideas.tetris.spring.SpringAutowired;
import com.ideas.tetris.ui.common.cdi.TetrisDataThreadPool;
import com.ideas.tetris.ui.common.security.UiContext;
import com.ideas.tetris.ui.modules.commons.continuouspricing.*;
import com.ideas.tetris.ui.modules.investigator.InvestigatorDto;
import com.ideas.tetris.ui.modules.pricing.PricingToggleView;
import com.ideas.tetris.ui.modules.pricing.views.AdditionalInformationFilterEnum;
import com.ideas.tetris.ui.modules.pricing.views.CPPricingManagementOverrideHtmlConverter;
import com.vaadin.cdi.UIScoped;
import com.vaadin.server.Page;
import com.vaadin.ui.UI;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.ObjectUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.glassfish.grizzly.utils.ArraySet;
import org.joda.time.LocalDate;
import org.springframework.beans.factory.annotation.Autowired;

import javax.enterprise.inject.Instance;
import javax.inject.Inject;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Predicate;
import java.util.function.Supplier;
import java.util.stream.Collectors;

import static com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName.ENABLE_UPLOAD_ON_OVERRIDE_FOR_EXTENDED_WINDOW;
import static com.ideas.tetris.pacman.services.bestavailablerate.DecisionReasonType.*;
import static com.ideas.tetris.pacman.util.BigDecimalUtil.isGreaterThan;
import static com.ideas.tetris.platform.common.utils.systemconfig.SystemConfig.isMultiThreadingEnabledOnPricingScreen;
import static com.ideas.tetris.ui.common.util.UiUtils.getText;

@SpringAutowired
public class PricingOverrideManager {
    @Inject
    protected UiContext uiContext;
    @Autowired
    ProductManagementService service;
    @Autowired
    DateService dateService;
    @Autowired
	private PacmanConfigParamsService configParamsService;
    @Autowired
	private BusinessAnalysisDashboardService businessAnalysisDashboardService;
    @Autowired
	private PropertyService propertyService;
    @Autowired
	private PricingConfigurationService pricingConfigurationService;
    @Autowired
	private AccomTypeSupplementService accomTypeSupplementService;
    @Autowired
	private DecisionService decisionService;
    @Autowired
	private GroupFloorOverrideService groupFloorOverrideService;
    @Autowired
	private SimplifiedWhatIfService simplifiedWhatIfService;
    @Autowired
	private PrettyPricingService prettyPricingService;
    @Autowired
	private TaxService taxService;
    @Autowired
	private PerPersonPricingService perPersonPricingService;
    @Autowired
	private AgileRatesConfigurationService agileRatesConfigurationService;
    @Autowired
	private HospitalityRoomsService hospitalityRoomsService;
    @Autowired
    private InventoryLimitDecisionService inventoryLimitDecisionService;

    private List<BusinessAnalysisDailyDataDto> businessAnalysisDailyDataDtos;
    private List<CPDecisionBAROutput> storedDecisions = new ArrayList<>();
    private List<CPDecisionBARNOVRDetails> storedDecisionsNovr = new ArrayList<>();
    private List<AccomType> baseRoomTypeList;
    private CPPricingFilter cpPricingFilter;
    private List<LastRoomValue> lastRoomValues;
    private List<BusinessAnalysisDailyIndicatorDto> businessAnalysisDailyIndicatorDtos;
    private Set<ProductRateOffsetOverride> allProductRateOffsetOverrides = new HashSet<>();
    private Set<ProductRateOffsetOverride> allProductRateOffsetOverridesWithProductGroups = new HashSet<>();
    private List<CPPaceDecisionBAROutput> paceDecisions;
    private boolean pseudoShowOnlyBaseRoomTypesFlag;
    private Map<LocalDate, CPRoomClassDTO> roomClassDetailsMap;
    private Map<LocalDate, Map<Integer, CPRoomClassDTO>> roomClassDetailsMapById;
    private Map<LocalDate, Map<Integer, CPRoomClassDTO>> accomTypeDetailsMapById;
    private List<CPBARDecisionUIWrapper> results;
    private Map<CPDecisionBAROutput, CPOverrideWrapper> overridesMap = new HashMap<>();
    private List<CPOverrideWrapper> productOverrides;
    private List<CPOverrideWrapper> inventoryLimitOverrides = new ArrayList<>();
    private Locale locale;
    private Predicate<AccomType> accomTypeFiltrationCriteria;
    private CPConfiguration cpConfiguration;
    private OccupancyType baseOccupancyType;
    private boolean showOnlyBaseRoomTypesFlag;
    private Map<Integer, PricingRule> pricingRules;
    private Tax tax;
    private List<OccupantBucketEntity> occupantBucketEntities;
    private List<MaximumOccupantsEntity> maximumOccupantsEntitiesList;
    private List<ProductPackage> ungroupedProductPackages;
    private List<AccomType> selectedAccomTypes;
    private List<Integer> accomClassIds;
    private List<String> hospitalityRooms;
    private boolean optimizeHospitalityRoomFetch;
    private boolean uiOptimizationEnabled;
    private boolean availableCapacityToSellEnabled;
    private PricingToggleView selectedView;
    public static final String OVERRIDDEN = "Overridden";
    public static final Logger LOGGER = Logger.getLogger(PricingOverrideManager.class);

    public void setupHospitalityRooms() {
        optimizeHospitalityRoomFetch = configParamsService.getBooleanParameterValue(PreProductionConfigParamName.OPTIMIZE_HOSPITALITY_ROOM_FETCH);
        if (optimizeHospitalityRoomFetch || availableCapacityToSellEnabled) {
            hospitalityRooms = hospitalityRoomsService.getAllTypesOfHospitalityRooms();
        }
    }

    public void setUIOptimizationEnabled(boolean uiOptimizationEnabled) {
        this.uiOptimizationEnabled = uiOptimizationEnabled;
    }

    public List<CPBARDecisionUIWrapper> wrapDecisions(boolean dateChange, boolean isInlineEditViewSelected, boolean isIndependentProductsEnabled) {
        if (isMultiThreadingEnabledOnPricingScreen()) {
            fetchRequiredDataUsingThreadPool(dateChange);
        } else {
            fetchRequiredData(dateChange);
        }
        Map<LocalDate, List<CPDecisionBAROutput>> dateMap = createDateToDecisionsMap(dateChange, isInlineEditViewSelected);
        if (dateChange && getCpPricingFilter().getStartDate() != null && getCpPricingFilter().getEndDate() != null) {
            lastRoomValues = service.getLastRoomValues(getCpPricingFilter().getStartDate().toDate(), getCpPricingFilter().getEndDate().toDate());
        }

        List<CPDecisionBAROutput> cpBARDecisionOutputs = getCPBarDecisionOutputs(dateMap);
        Set<Integer> roomClassIds = new HashSet<>();
        roomClassIds.addAll(cpBARDecisionOutputs.stream().map(cpDecisionBAROutput -> cpDecisionBAROutput.getAccomType().getAccomClass().getId()).collect(Collectors.toSet()));

        //only add competitor rate if UI needs it
        if (isCompetitorRateSelectedInFilter()) {
            if (isIndependentProductsEnabled) {
                //Add Competitor Rate Info
                service.addCompetitorRateInfoToDecisions(cpBARDecisionOutputs
                        , getCpPricingFilter().getStartDate().toDate()
                        , getCpPricingFilter().getEndDate().toDate()
                        , roomClassIds
                        , cpPricingFilter.getSelectedProductsRateShoppingLOS());
            } else {
                //Add Competitor Rate Info
                service.addCompetitorRateInfoToDecisions(cpBARDecisionOutputs
                        , getCpPricingFilter().getStartDate().toDate()
                        , getCpPricingFilter().getEndDate().toDate()
                        , roomClassIds);
            }
        }
        //Add LRV Info
        service.addLRVInfoToDecisions(cpBARDecisionOutputs
                , lastRoomValues
                , roomClassIds);

        // create system price map
        Map<LocalDate, List<CPDecisionBARNOVRDetails>> dateMapNOVR = new HashMap<>();
        if (isSystemPriceEnabled()) {
            dateMapNOVR.putAll(getNOVRDecisionsMap(dateChange));
        }

        Map<LocalDate, List<CPDecisionBAROutput>> dateAveragePriceMap = new LinkedHashMap<>();
        if (isAveragePriceEnabled()) {
            dateAveragePriceMap.putAll(getAveragePriceDecisionMap(cpBARDecisionOutputs));
        }

        results = createCPBarDecisionUIWrappersFrom(dateMap, dateMapNOVR);

        return results;
    }

    public Map<LocalDate, List<CPDecisionBAROutput>> getAveragePriceDecisionMap(List<CPDecisionBAROutput> cpDecisionBAROutputList) {
        Map<LocalDate, List<CPDecisionBAROutput>> dateListMap = new LinkedHashMap<>();
        Set<Product> independentProductSet = cpDecisionBAROutputList.stream().map(CPDecisionBAROutput::getProduct).filter(Product::isIndependentProduct).collect(Collectors.toSet());
        cpDecisionBAROutputList = cpDecisionBAROutputList.stream().filter(p -> p.getProduct().isIndependentProduct()).collect(Collectors.toList());
        LocalDate currentSystemDate = dateService.getCaughtUpLocalDate();
        LocalDate maxDecisionDate = service.getMaxDecisionDate();

        if (CollectionUtils.isEmpty(independentProductSet)) {
            return dateListMap;
        }

        Integer addedDays = independentProductSet.stream().map(Product::getMinLOS).max(Integer::compareTo).get();
        List<AccomType> accomTypeList = new ArrayList<>(getCpPricingFilter().getSelectedRoomTypes());
        if (getCpPricingFilter().getSelectedRoomClass().getName().equals(getText("pricing.all.room.classes")) && CollectionUtils.isEmpty(accomTypeList)) {
            accomTypeList.addAll(baseRoomTypeList);
        } else if (getCpPricingFilter().getSelectedRoomClass() != null && CollectionUtils.isEmpty(accomTypeList)) {
            accomTypeList.addAll(getCpPricingFilter().getSelectedRoomClass().getAccomTypes());
        }
        List<CPDecisionBAROutput> cpDecisionBAROutputsForAdditionalDays = service.getCPDecisionsBetweenDatesForProductAndRoomTypes(independentProductSet,
                getCpPricingFilter().getStartDate(), getCpPricingFilter().getEndDate().plusDays(addedDays), accomTypeList);
        Map<Product, Map<AccomType, Map<LocalDate, CPDecisionBAROutput>>> decisionMap = new HashMap<>();

        for (CPDecisionBAROutput cpDecisionBAROutput : cpDecisionBAROutputsForAdditionalDays) {
            Product product = cpDecisionBAROutput.getProduct();
            AccomType accomType = cpDecisionBAROutput.getAccomType();
            LocalDate arrivalDate = cpDecisionBAROutput.getArrivalDate();

            if (!decisionMap.containsKey(product)) {
                decisionMap.put(product, new HashMap<>());
            }

            Map<AccomType, Map<LocalDate, CPDecisionBAROutput>> innerMap = decisionMap.get(product);
            if (!innerMap.containsKey(accomType)) {
                innerMap.put(accomType, new HashMap<>());
            }

            Map<LocalDate, CPDecisionBAROutput> dateMap = innerMap.get(accomType);
            dateMap.put(arrivalDate, cpDecisionBAROutput);
        }

        for (CPDecisionBAROutput cpDecisionBAROutput : cpDecisionBAROutputList) {
            Integer minLOS = cpDecisionBAROutput.getProduct().getMinLOS();
            LocalDate startDate = cpDecisionBAROutput.getArrivalDate().minusDays(1);
            LocalDate endDate = cpDecisionBAROutput.getArrivalDate().plusDays(minLOS);

            Map<LocalDate, CPDecisionBAROutput> innerMap = decisionMap.get(cpDecisionBAROutput.getProduct()).get(cpDecisionBAROutput.getAccomType());

            if (!cpDecisionBAROutput.getArrivalDate().isBefore(currentSystemDate) &&
                    cpDecisionBAROutput.getArrivalDate().plusDays(cpDecisionBAROutput.getProduct().getMinLOS()).isBefore(maxDecisionDate.plusDays(2))) {
                BigDecimal sum = innerMap.entrySet().stream().filter(entry -> entry.getKey().isAfter(startDate) && entry.getKey().isBefore(endDate))
                        .map(entry -> entry.getValue().getFinalBAR()).reduce(BigDecimal.ZERO, BigDecimal::add);

                cpDecisionBAROutput.setAveragePrice(sum.divide(BigDecimal.valueOf(minLOS), 2, RoundingMode.HALF_UP));
            }

            LocalDate arrivalDate = cpDecisionBAROutput.getArrivalDate();
            dateListMap.computeIfAbsent(arrivalDate, k -> new ArrayList<>()).add(cpDecisionBAROutput);
        }

        return dateListMap;
    }

    private Boolean isAveragePriceEnabled() {
        return configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.AVERAGE_PRICE_ENABLED);
    }

    private boolean isSystemPriceEnabled() {
        return configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.SYSTEM_PRICE_ENABLED);
    }

    protected boolean isCompetitorRateSelectedInFilter() {
        return AdditionalInformationFilterEnum.COMPETITOR.equals(getCpPricingFilter().getAdditionalInformationFilterEnum1()) ||
                AdditionalInformationFilterEnum.COMPETITOR.equals(getCpPricingFilter().getAdditionalInformationFilterEnum2()) ||
                AdditionalInformationFilterEnum.COMPETITOR.equals(getCpPricingFilter().getAdditionalInformationFilterEnum3()) ||
                AdditionalInformationFilterEnum.COMPETITOR.equals(getCpPricingFilter().getAdditionalInformationFilterEnum4());
    }

    public void populateIfAbsent() {
        if (cpConfiguration == null) {
            cpConfiguration = pricingConfigurationService.findCPConfiguration();
        }
        if (baseOccupancyType == null) {
            baseOccupancyType = pricingConfigurationService.getBaseOccupancyType();
        }
        if (pricingRules == null) {
            pricingRules = prettyPricingService.getPricingRules();
        }
        if (tax == null) {
            tax = taxService.findTax();
        }
        if (null == occupantBucketEntities) {
            occupantBucketEntities = perPersonPricingService.getOccupantBuckets();
        }
        if (null == maximumOccupantsEntitiesList) {
            maximumOccupantsEntitiesList = pricingConfigurationService.getMaximumEntitiesList();
        }
        if (null == ungroupedProductPackages) {
            ungroupedProductPackages = pricingConfigurationService.getActiveProductPackages();
        }
    }

    public Map<LocalDate, List<InvestigatorDto>> getInvestigatorDtosByDate(boolean showOverrideTab, List<PricingAccomClass> pricingAccomClasses) {
        if (CollectionUtils.isEmpty(results)) {
            return new HashMap<>();
        }
        return results.stream()
                .collect(Collectors.toMap(cpbarDecisionUIWrapper -> cpbarDecisionUIWrapper.getCpbarDecisionDTO().getDate()
                        , cpbarDecisionUIWrapper -> getInvestigatorDtos(showOverrideTab, cpbarDecisionUIWrapper, pricingAccomClasses), (a, b) -> b));
    }

    private List<InvestigatorDto> getInvestigatorDtos(boolean showOverrideTab, CPBARDecisionUIWrapper cpbarDecisionUIWrapper, List<PricingAccomClass> pricingAccomClasses) {
        return cpbarDecisionUIWrapper.getCpOverrideWrappers().stream()
                .map(cpOverrideWrapper -> createInvestigatorDto(cpOverrideWrapper, cpOverrideWrapper.getCpDecisionBAROutput(), showOverrideTab, pricingAccomClasses))
                .collect(Collectors.toList());
    }

    public boolean isRoomClassPriceExcluded(AccomClass accomClass, List<PricingAccomClass> pricingAccomClasses) {
        return pricingAccomClasses.stream()
                .filter(pricingAccomClass -> pricingAccomClass.getAccomClass().equals(accomClass))
                .anyMatch(PricingAccomClass::isPriceExcluded);
    }

    private InvestigatorDto createInvestigatorDto(CPOverrideWrapper wrapper, CPDecisionBAROutput output, boolean showOverrideTab, List<PricingAccomClass> pricingAccomClasses) {
        InvestigatorDto investigatorDto = new InvestigatorDto();
        investigatorDto.setModuleOrigin(TetrisPermissionKey.PRICING);
        investigatorDto.setOverride(showOverrideTab);
        investigatorDto.setSelectedDate(output.getArrivalDate());
        investigatorDto.setSelectedAccomClassId(output.getAccomType().getAccomClass().getId());
        investigatorDto.setSelectedAccomTypeId(output.getAccomType().getId());
        wrapper.setStartDate(output.getArrivalDate());
        wrapper.setEndDate(output.getArrivalDate());
        investigatorDto.setWrapper(wrapper);
        investigatorDto.setBaseRoomType(wrapper.isBaseRoomType());
        investigatorDto.setRoomClassPriceExcluded(isRoomClassPriceExcluded(output.getAccomType().getAccomClass(), pricingAccomClasses));
        investigatorDto.setStartDate(results.get(0).getCpbarDecisionDTO().getDate()); // TODO handle empty results
        investigatorDto.setEndDate(results.get(results.size() - 1).getCpbarDecisionDTO().getDate());
        return investigatorDto;
    }

    public CPOverrideWrapper findWrapper(CPOverrideWrapper cpOverrideWrapper) {
        CPOverrideWrapper wrapper = findCpOverrideWrapperFor(cpOverrideWrapper);
        if (wrapper != null) {
            wrapper.setSpecificOverride(cpOverrideWrapper.getSpecificOverride());
            wrapper.setFloorOverride(cpOverrideWrapper.getFloorOverride());
            wrapper.setCeilingOverride(cpOverrideWrapper.getCeilingOverride());
            wrapper.setGroupFloorOverride(cpOverrideWrapper.getGroupFloorOverride());
            wrapper.setIsPendingSave(true);
            wrapper.setApplyOverrideAcrossRoomTypes(cpOverrideWrapper.isApplyOverrideAcrossRoomTypes());
            return wrapper;
        } else {
            cpOverrideWrapper.setIsPendingSave(true);
            return cpOverrideWrapper;
        }
    }

    private CPOverrideWrapper findCpOverrideWrapperFor(CPOverrideWrapper cpOverrideWrapper) {
        CPBARDecisionUIWrapper cpbarUIWrapper = findCpbarDecisionUIWrapperFor(cpOverrideWrapper);
        if (cpbarUIWrapper != null) {
            return cpbarUIWrapper.getCpOverrideWrappers().stream()
                    .filter(overrideWrapper -> overrideWrapper.getProduct().equals(cpOverrideWrapper.getProduct()))
                    .filter(overrideWrapper -> isAccomTypeEqualsFor(cpOverrideWrapper, overrideWrapper))
                    .findFirst()
                    .orElse(null);
        } else {
            return null;
        }
    }

    private CPBARDecisionUIWrapper findCpbarDecisionUIWrapperFor(CPOverrideWrapper cpOverrideWrapper) {
        if (results != null) {
            return results.stream()
                    .filter(uiWrapper -> isArrivalDateEqualsFor(cpOverrideWrapper, uiWrapper))
                    .findFirst()
                    .orElse(null);
        } else {
            return null;
        }
    }

    private boolean isAccomTypeEqualsFor(CPOverrideWrapper cpOverrideWrapper, CPOverrideWrapper cpOverrideWrapper1) {
        return cpOverrideWrapper1.getCpDecisionBAROutput().getAccomType().getId().equals(cpOverrideWrapper.getCpDecisionBAROutput().getAccomType().getId());
    }

    private boolean isArrivalDateEqualsFor(CPOverrideWrapper cpOverrideWrapper, CPBARDecisionUIWrapper cpbarDecisionUIWrapper) {
        return cpbarDecisionUIWrapper.getCpbarDecisionDTO().getDate().compareTo(cpOverrideWrapper.getCpDecisionBAROutput().getArrivalDate()) == 0;
    }

    public void applyOverride(CPOverrideWrapper overrideWrapper, PricingAccomClass masterPricingAccomClass) {
        CPDecisionContext cpDecisionContext = pricingConfigurationService.getCPDecisionContext(overrideWrapper.getStartDate(), overrideWrapper.getEndDate());
        overrideWrapper.setIsPendingDelete(false);
        overrideWrapper.setIsPendingSave(true);

        DecisionOverrideType overrideType = getDecisionOverrideTypeFromOverrideWrapper(overrideWrapper);

        overrideWrapper.getCpDecisionBAROutput().setOverrideType(overrideType);
        overrideWrapper.getCpDecisionBAROutput().setSpecificOverride(overrideWrapper.getSpecificOverride());
        overrideWrapper.getCpDecisionBAROutput().setCeilingOverride(overrideWrapper.getCeilingOverride());
        if (DecisionOverrideType.GPFLOOR.equals(overrideType) || DecisionOverrideType.GPFLOORANDCEIL.equals(overrideType)) {
            overrideWrapper.getCpDecisionBAROutput().setFloorOverride(overrideWrapper.getGroupFloorOverride());
        } else {
            overrideWrapper.getCpDecisionBAROutput().setFloorOverride(overrideWrapper.getFloorOverride());
        }

        if (overrideWrapper.getSpecificOverride() != null) {
            //if its a specific override, we use the the value for pretty bar
            overrideWrapper.setRoundedBAR(overrideWrapper.getSpecificOverride());
        } else {
            //if its a floor, ceiling or both, we need to calculate the pretty bar price
            BigDecimal roundedBARPrice = cpDecisionContext.calculateRoundedRate(overrideWrapper.getCpDecisionBAROutput());
            overrideWrapper.setRoundedBAR(roundedBARPrice);
        }

        //put it in the map so we can later save it
        overridesMap.put(overrideWrapper.getCpDecisionBAROutput(), overrideWrapper);

        //check to see if we need to apply the override across the room types. Note this only happens when doing a specific override
        //This will also happen when we are in showOnlyBaseRoomTypes mode.
        if (overrideWrapper.getSpecificOverride() != null && shouldApplyOverridesAcrossRoomTypes(overrideWrapper)) {
            //get a reference to the other room types that we need to apply the overrides to
            List<CPOverrideWrapper> cpOverrideWrappers = getNonBaseRoomTypes(overrideWrapper, masterPricingAccomClass, uiContext.getSystemCaughtUpDate());

            for (CPOverrideWrapper cpOverrideWrapper : cpOverrideWrappers) {
                //loop over the wrappers, but don't include the original override as it has been already modified above
                applySpecificOverrideAcrossRoomTypes(cpDecisionContext, overrideWrapper, cpOverrideWrapper);
            }
        } else if (isBaseRoomTypeCeilingAndOrFloorOverride(overrideWrapper)) {
            // Get the CPOverrideWrappers from the parent
            List<CPOverrideWrapper> cpOverrideWrappers = null;
            if (isShowOnlyBaseRoomTypesFlag() || isPseudoShowOnlyBaseRoomTypesFlag()) {
                cpOverrideWrappers = getNonBaseRoomTypes(overrideWrapper, masterPricingAccomClass, uiContext.getSystemCaughtUpDate());
            } else {
                cpOverrideWrappers = getCPOverrideWrappersNotForAccomTypeWithoutSpecificOverrides(overrideWrapper, masterPricingAccomClass);
            }

            for (CPOverrideWrapper cpOverrideWrapper : cpOverrideWrappers) {
                applyOffsetsToOverrideValues(cpDecisionContext, overrideWrapper, cpOverrideWrapper);
            }
        }
    }

    private boolean shouldApplyOverridesAcrossRoomTypes(CPOverrideWrapper overrideWrapper) {
        return overrideWrapper.isApplyOverrideAcrossRoomTypes() || isShowOnlyBaseRoomTypesFlag();
    }

    public void applyOverride(CPOverrideWrapper overrideWrapper, List<PricingAccomClass> pricingAccomClasses,
                              boolean isPerPersonPricingEnabled, boolean isChildAgeBucketsEnabled, boolean isChildAgeBucketPackagesEnabled,
                              boolean isAgileRatesEnabled, LocalDate caughtUpDate, CPDecisionContext cpDecisionContext, PricingAccomClass masterPricingAccomClass) {

        cpDecisionContext = getCpDecisionContext(overrideWrapper, pricingAccomClasses, isPerPersonPricingEnabled, isChildAgeBucketsEnabled, isChildAgeBucketPackagesEnabled, isAgileRatesEnabled, caughtUpDate, cpDecisionContext);
        overrideWrapper.setIsPendingDelete(false);
        overrideWrapper.setIsPendingSave(true);

        DecisionOverrideType overrideType = getDecisionOverrideTypeFromOverrideWrapper(overrideWrapper);

        overrideWrapper.getCpDecisionBAROutput().setOverrideType(overrideType);
        overrideWrapper.getCpDecisionBAROutput().setSpecificOverride(overrideWrapper.getSpecificOverride());
        overrideWrapper.getCpDecisionBAROutput().setCeilingOverride(overrideWrapper.getCeilingOverride());
        if (DecisionOverrideType.GPFLOOR.equals(overrideType) || DecisionOverrideType.GPFLOORANDCEIL.equals(overrideType)) {
            overrideWrapper.getCpDecisionBAROutput().setFloorOverride(overrideWrapper.getGroupFloorOverride());
        } else {
            overrideWrapper.getCpDecisionBAROutput().setFloorOverride(overrideWrapper.getFloorOverride());
        }

        if (overrideWrapper.getSpecificOverride() != null) {
            //if its a specific override, we use the the value for pretty bar
            overrideWrapper.setRoundedBAR(overrideWrapper.getSpecificOverride());
        } else {
            //if its a floor, ceiling or both, we need to calculate the pretty bar price
            BigDecimal roundedBARPrice = cpDecisionContext.calculateRoundedRate(overrideWrapper.getCpDecisionBAROutput());
            overrideWrapper.setRoundedBAR(roundedBARPrice);
        }

        //put it in the map so we can later save it
        overridesMap.put(overrideWrapper.getCpDecisionBAROutput(), overrideWrapper);

        //If the masterPrigingAccomClass is not passed in, find it from the cpDecisionContext
        if (masterPricingAccomClass == null) {
            Map<AccomClass, PricingAccomClass> pricingAccomClassesMap = cpDecisionContext.getPricingAccomClasses();
            AccomClass accomClass = pricingAccomClassesMap.keySet()
                    .stream()
                    .filter(pac -> pac.getMasterClass() == 1)
                    .findFirst()
                    .orElse(null);
            masterPricingAccomClass = pricingAccomClassesMap.get(accomClass);
        }
        //check to see if we need to apply the override across the room types. Note this only happens when doing a specific override
        //This will also happen when we are in showOnlyBaseRoomTypes mode.
        if (overrideWrapper.getSpecificOverride() != null && shouldApplyOverridesAcrossRoomTypes(overrideWrapper)) {
            //get a reference to the other room types that we need to apply the overrides to
            List<CPOverrideWrapper> cpOverrideWrappers = getNonBaseRoomTypes(overrideWrapper, masterPricingAccomClass, uiContext.getSystemCaughtUpDate());

            for (CPOverrideWrapper cpOverrideWrapper : cpOverrideWrappers) {
                //loop over the wrappers, but don't include the original override as it has been already modified above
                applySpecificOverrideAcrossRoomTypes(cpDecisionContext, overrideWrapper, cpOverrideWrapper);
            }
        } else if (isBaseRoomTypeCeilingAndOrFloorOverride(overrideWrapper)) {
            // Get the CPOverrideWrappers from the parent
            List<CPOverrideWrapper> cpOverrideWrappers = null;
            if (isShowOnlyBaseRoomTypesFlag() || isPseudoShowOnlyBaseRoomTypesFlag()) {
                cpOverrideWrappers = getNonBaseRoomTypes(overrideWrapper, masterPricingAccomClass, uiContext.getSystemCaughtUpDate());
            } else {
                cpOverrideWrappers = getNonBaseRoomTypes(overrideWrapper, masterPricingAccomClass, uiContext.getSystemCaughtUpDate())
                        .stream()
                        .filter(cpOverrideWrapper -> !overridesMap.containsKey(cpOverrideWrapper.getCpDecisionBAROutput()))
                        .filter(Objects::nonNull)
                        .map(cpOverrideWrapper -> findWrapper(cpOverrideWrapper))
                        .collect(Collectors.toList());
            }

            for (CPOverrideWrapper cpOverrideWrapper : cpOverrideWrappers) {
                applyOffsetsToOverrideValues(cpDecisionContext, overrideWrapper, cpOverrideWrapper);
            }
        }
    }

    private CPDecisionContext getCpDecisionContext(CPOverrideWrapper overrideWrapper, List<PricingAccomClass> pricingAccomClasses, boolean isPerPersonPricingEnabled,
                                                   boolean isChildAgeBucketsEnabled, boolean isChildAgeBucketPackagesEnabled,
                                                   boolean isAgileRatesEnabled, LocalDate caughtUpDate, CPDecisionContext cpDecisionContext) {
        if (null == cpDecisionContext || !SystemConfig.usePerformanceImprovementChangesPricingMultiProductDisplayChanges()) {
            cpDecisionContext = pricingConfigurationService.getCPDecisionContext(
                    overrideWrapper.getStartDate(), overrideWrapper.getEndDate(), true,
                    getCpConfiguration(), getBaseOccupancyType(),
                    isShowOnlyBaseRoomTypesFlag(), getPricingRules(),
                    getTax(), pricingAccomClasses,
                    getOccupantBucketEntities(), getMaximumOccupantsEntitiesList(),
                    isPerPersonPricingEnabled, isChildAgeBucketsEnabled,
                    isChildAgeBucketPackagesEnabled,
                    isAgileRatesEnabled, caughtUpDate,
                    getUngroupedProductPackages());
        }
        return cpDecisionContext;
    }

    private DecisionOverrideType getDecisionOverrideTypeFromOverrideWrapper(CPOverrideWrapper overrideWrapper) {
        DecisionOverrideType overrideType = DecisionOverrideType.NONE;

        if (overrideWrapper.getSpecificOverride() == null) {
            if (overrideWrapper.getCeilingOverride() != null) {
                if (overrideWrapper.getFloorOverride() != null) {
                    overrideType = DecisionOverrideType.FLOORANDCEIL;
                } else if (overrideWrapper.getGroupFloorOverride() != null) {
                    overrideType = DecisionOverrideType.GPFLOORANDCEIL;
                } else {
                    overrideType = DecisionOverrideType.CEIL;
                }
            } else {
                if (overrideWrapper.getFloorOverride() != null) {
                    overrideType = DecisionOverrideType.FLOOR;
                } else if (overrideWrapper.getGroupFloorOverride() != null) {
                    overrideType = DecisionOverrideType.GPFLOOR;
                }
            }
        } else {
            overrideType = DecisionOverrideType.USER;
        }
        return overrideType;
    }

    public List<CPOverrideWrapper> getNonBaseRoomTypes(CPOverrideWrapper wrapper, PricingAccomClass masterPricingAccomClass, Date caughtUpDate) {
        //check to see if we have already loaded the non base room types
        if (wrapper.getNonBaseRoomTypeOverrideWrappers() != null) {
            return wrapper.getNonBaseRoomTypeOverrideWrappers();
        } else {
            //we haven't loaded the non base room types, so load them up
            List<CPOverrideWrapper> cpOverrideWrappersForNonBaseRoomTypes = getCPOverrideWrappersForNonBaseRoomTypes(wrapper, masterPricingAccomClass, caughtUpDate);

            List<CPOverrideWrapper> finalOverrideWrappersIncludingExisting = cpOverrideWrappersForNonBaseRoomTypes
                    .stream()
                    .map(cpOverrideWrapper -> {
                        CPOverrideWrapper overrideWrapper = findCpOverrideWrapperFor(cpOverrideWrapper);
                        return overrideWrapper != null ? overrideWrapper : cpOverrideWrapper;
                    })
                    .collect(Collectors.toList());

            //set our reference to the non base room types so we don't keep re-loading them
            wrapper.setNonBaseRoomTypeOverrideWrappers(finalOverrideWrappersIncludingExisting);
            return finalOverrideWrappersIncludingExisting;
        }
    }

    private List<CPOverrideWrapper> getCPOverrideWrappersForNonBaseRoomTypes(CPOverrideWrapper overrideWrapper, PricingAccomClass masterPricingAccomClass, Date caughtUpDate) {
        PricingManagementCPSearchCriteria criteria = newGroupSearchCriteria();

        LocalDate overrideDate = overrideWrapper.getCpDecisionBAROutput().getArrivalDate();

        criteria.setRoomClass(overrideWrapper.getCpDecisionBAROutput().getAccomType().getAccomClass());
        Set<AccomType> accomTypes = overrideWrapper.getCpDecisionBAROutput().getAccomType().getAccomClass().getAccomTypes();
        List<AccomType> activeAndNonZeroCapacityRoomTypes = accomTypes.stream().filter(rt -> accomTypeFiltrationCriteria.test(rt)).collect(Collectors.toList());
        criteria.setEndDate(overrideDate);
        criteria.setStartDate(overrideDate);
        criteria.setBaseRoomTypes(activeAndNonZeroCapacityRoomTypes);
        criteria.setProductId(overrideWrapper.getProduct().getId());

        List<CPBARDecisionDTO> dtoResults = service.searchForBarDecisionDTO(isShowOnlyBaseRoomTypesFlag(), criteria, false, masterPricingAccomClass, caughtUpDate);
        CPBARDecisionDTO cpbarDecisionDTO = dtoResults.get(0);//our start and end date are the same date, so we expect only 1 row back
        List<CPOverrideWrapper> cpOverrideWrappers = new CPBARDecisionUIWrapper(cpbarDecisionDTO, isLraEnabled()).getCpOverrideWrappers();

        //lastly strip out the accom type represented by the passed in overrideWrapper
        return cpOverrideWrappers.stream().filter(cpOverrideWrapper -> isNotSameRoomType(overrideWrapper, cpOverrideWrapper))
                .collect(Collectors.toList());
    }

    protected PricingManagementCPSearchCriteria newGroupSearchCriteria() {
        PricingManagementCPSearchCriteria pricingManagementCPSearchCriteria =
                new PricingManagementCPSearchCriteria();
        pricingManagementCPSearchCriteria.setPropertyId(uiContext.getPropertyId());

        return pricingManagementCPSearchCriteria;
    }

    private List<CPOverrideWrapper> getCPOverrideWrappersNotForAccomTypeWithoutSpecificOverrides(CPOverrideWrapper overrideWrapper, PricingAccomClass masterPricingAccomClass) {
        return getNonBaseRoomTypes(overrideWrapper, masterPricingAccomClass, uiContext.getSystemCaughtUpDate()).stream()
                .filter(cpOverrideWrapper -> isNotSameRoomTypeAndDoesNotHaveSpecificOverride(overrideWrapper, cpOverrideWrapper))
                .collect(Collectors.toList());
    }

    private boolean isNotSameRoomTypeAndDoesNotHaveSpecificOverride(CPOverrideWrapper overrideWrapper, CPOverrideWrapper cpOverrideWrapper) {
        return !overrideWrapper.getCpDecisionBAROutput().getAccomType().equals(cpOverrideWrapper.getCpDecisionBAROutput().getAccomType())
                && (cpOverrideWrapper.isPendingDelete() || cpOverrideWrapper.getSpecificOverride() == null);
    }

    private void applyOffsetsToOverrideValues(CPDecisionContext cpDecisionContext, CPOverrideWrapper baseRoomOverrideWrapper, CPOverrideWrapper nonBaseOverrideWrapper) {
        BigDecimal valueWithOffsetApplied = cpDecisionContext.applyOffset(nonBaseOverrideWrapper.getCpDecisionBAROutput(), baseRoomOverrideWrapper.getRoundedBAR());
        Integer primaryProductId = cpDecisionContext.getPrimaryProduct(baseRoomOverrideWrapper.getProduct()).getId();
        BigDecimal prettyPrice = cpDecisionContext.calculatePrettyPrice(primaryProductId, valueWithOffsetApplied);
        CPConfigMergedCeilingAndFloor ceilingAndFloor = cpDecisionContext.getCeilingAndFloor(baseRoomOverrideWrapper.getCpDecisionBAROutput());

        BigDecimal floorRate = cpDecisionContext.applyOffset(baseRoomOverrideWrapper.getCpDecisionBAROutput(), baseRoomOverrideWrapper.getFloorOverride());
        BigDecimal ceilingRate = cpDecisionContext.applyOffset(baseRoomOverrideWrapper.getCpDecisionBAROutput(), baseRoomOverrideWrapper.getCeilingOverride());
        if (ceilingAndFloor != null) {
            if (floorRate == null) {
                floorRate = ceilingAndFloor.getFloorRate();
            }
            if (ceilingRate == null) {
                ceilingRate = ceilingAndFloor.getCeilingRate();
            }
        }

        nonBaseOverrideWrapper.setRoundedBAR(prettyPrice);
        nonBaseOverrideWrapper.getCpDecisionBAROutput().setPrettyBAR(prettyPrice);
        nonBaseOverrideWrapper.getCpDecisionBAROutput().setRoomsOnlyBAR(Supplement.removeSupplementFrom(prettyPrice, cpDecisionContext.getSupplementFor(nonBaseOverrideWrapper.getCpDecisionBAROutput()).orElse(Supplement.zeroSupplement())));
        nonBaseOverrideWrapper.getCpDecisionBAROutput().setFinalBAR(prettyPrice);
        baseRoomOverrideWrapper.setIsPendingSaveNonBaseRoomTypes(true);
        if (nonBaseOverrideWrapper.getOriginalSpecificOverride() != null &&
                ((this.getSelectedView() != null && PricingToggleView.GRID.equals(this.getSelectedView())) || baseRoomOverrideWrapper.isApplyOverrideAcrossRoomTypes())) {
            nonBaseOverrideWrapper.setSpecificOverride(null);
            nonBaseOverrideWrapper.setIsPendingDelete(true);
            overridesMap.put(nonBaseOverrideWrapper.getCpDecisionBAROutput(), nonBaseOverrideWrapper);
        }
    }

    public void removeOverrides(CPOverrideWrapper overrideWrapper, List<PricingAccomClass> pricingAccomClasses, CPDecisionContext cpDecisionContext, PricingAccomClass masterPricingAccomClass, boolean isInlineEditMode) {
        if (null == cpDecisionContext) {
            cpDecisionContext = pricingConfigurationService.getCPDecisionContext(
                    overrideWrapper.getCpDecisionBAROutput().getArrivalDate(),
                    overrideWrapper.getCpDecisionBAROutput().getArrivalDate(),
                    false);
        }
        if (null == masterPricingAccomClass) {
            Map<AccomClass, PricingAccomClass> pricingAccomClassesMap = cpDecisionContext.getPricingAccomClasses();
            AccomClass accomClass = pricingAccomClassesMap.keySet()
                    .stream()
                    .filter(pac -> pac.getMasterClass() == 1)
                    .findFirst()
                    .orElse(null);
            masterPricingAccomClass = pricingAccomClassesMap.get(accomClass);
        }

        remove(cpDecisionContext, overrideWrapper, overrideWrapper, pricingAccomClasses, masterPricingAccomClass);
        //Check if we are removing across room types.
        //If we are in showOnlyBaseRoomTypes and the original override that we are removing was a specific, remove the overrides from our non base room types
        if ((isShowOnlyBaseRoomTypesFlag() || (isInlineEditMode && isPseudoShowOnlyBaseRoomTypesFlag() && overrideWrapper.getOriginalSpecificOverride() != null)) || overrideWrapper.isApplyOverrideAcrossRoomTypes()) {

            List<CPOverrideWrapper> cpOverrideWrappers = null;
            if (isShowOnlyBaseRoomTypesFlag() || isPseudoShowOnlyBaseRoomTypesFlag()) {
                cpOverrideWrappers = getNonBaseRoomTypes(overrideWrapper, masterPricingAccomClass, uiContext.getSystemCaughtUpDate());
            } else {
                cpOverrideWrappers = getNonBaseRoomTypes(overrideWrapper, masterPricingAccomClass, uiContext.getSystemCaughtUpDate());
            }

            for (CPOverrideWrapper cpOverrideWrapper : cpOverrideWrappers) {
                //don't remove the original override as that already happened above
                if (!overrideWrapper.getCpDecisionBAROutput().getAccomType().equals(cpOverrideWrapper.getCpDecisionBAROutput().getAccomType())) {
                    remove(cpDecisionContext, cpOverrideWrapper, overrideWrapper, pricingAccomClasses, masterPricingAccomClass);
                }
            }
        }
    }

    private void applySpecificOverrideAcrossRoomTypes(CPDecisionContext cpDecisionContext, CPOverrideWrapper baseRoomTypeWrapper, CPOverrideWrapper otherRoomTypeWrapper) {
        if (!baseRoomTypeWrapper.getCpDecisionBAROutput().getAccomType().equals(otherRoomTypeWrapper.getCpDecisionBAROutput().getAccomType())) {
            otherRoomTypeWrapper.setIsPendingDelete(false);
            otherRoomTypeWrapper.setIsPendingSave(true);

            otherRoomTypeWrapper.setSpecificOverride(baseRoomTypeWrapper.getSpecificOverride());
            otherRoomTypeWrapper.setCeilingOverride(null);
            otherRoomTypeWrapper.setFloorOverride(null);

            otherRoomTypeWrapper.getCpDecisionBAROutput().setOverrideType(DecisionOverrideType.USER);
            otherRoomTypeWrapper.getCpDecisionBAROutput().setSpecificOverride(baseRoomTypeWrapper.getSpecificOverride());
            otherRoomTypeWrapper.getCpDecisionBAROutput().setCeilingOverride(null);
            otherRoomTypeWrapper.getCpDecisionBAROutput().setFloorOverride(null);

            BigDecimal offsetAppliedValue = cpDecisionContext.applyOffset(otherRoomTypeWrapper.getCpDecisionBAROutput(), baseRoomTypeWrapper.getSpecificOverride());
            BigDecimal supplementBase = cpDecisionContext.getSupplement(baseRoomTypeWrapper.getCpDecisionBAROutput());
            BigDecimal supplementNonBase = cpDecisionContext.getSupplement(otherRoomTypeWrapper.getCpDecisionBAROutput());
            BigDecimal supplementDifference = BigDecimalUtil.subtract(supplementNonBase, supplementBase);
            BigDecimal supplementToBeAdjusted = BigDecimal.ZERO;
            if (isGreaterThan(supplementDifference, BigDecimal.ZERO)) {
                supplementToBeAdjusted = supplementDifference;
            }
            Integer primaryProductId = cpDecisionContext.getPrimaryProduct(baseRoomTypeWrapper.getProduct()).getId();
            BigDecimal specificOverridePrettyPriceForNonBase = cpDecisionContext.calculatePrettyPrice(primaryProductId, BigDecimalUtil.add(offsetAppliedValue, supplementToBeAdjusted));

            otherRoomTypeWrapper.setSpecificOverride(specificOverridePrettyPriceForNonBase);
            otherRoomTypeWrapper.setRoundedBAR(specificOverridePrettyPriceForNonBase);
            otherRoomTypeWrapper.getCpDecisionBAROutput().setSpecificOverride(specificOverridePrettyPriceForNonBase);

            overridesMap.put(otherRoomTypeWrapper.getCpDecisionBAROutput(), otherRoomTypeWrapper);
        }
    }

    public void setPricingConfigurationService(PricingConfigurationService pricingConfigurationService) {
        this.pricingConfigurationService = pricingConfigurationService;
    }


    private void remove(CPDecisionContext cpDecisionContext, CPOverrideWrapper overrideWrapper, CPOverrideWrapper baseOverrideWrapper, List<PricingAccomClass> pricingAccomClasses, PricingAccomClass masterPricingAccomClass) {
        BigDecimal prettyPrice = getPrettyPriceAfterRemove(cpDecisionContext, overrideWrapper);
        overrideWrapper.setRoundedBAR(prettyPrice);
        overrideWrapper.setIsPendingSave(false);
        if (overrideWrapper.isOverridePersisted()) {
            if (baseOverrideWrapper.isApplyOverrideAcrossRoomTypes()) {
                overrideWrapper.setSpecificOverride(baseOverrideWrapper.getSpecificOverride());
            }
            overrideWrapper.setIsPendingDelete(true);
            overridesMap.put(overrideWrapper.getCpDecisionBAROutput(), overrideWrapper);

            //Base Room type + Ceiling/Floor Override || Base Room Type  + Not Specific Override
            if (isBaseRoomTypeCeilingAndOrFloorOverride(overrideWrapper) || isBaseRoomTypeAndTrashIconToggleForCeilingAndOrFloorOverride(overrideWrapper)) {
                // Get the CPOverrideWrappers from the parent
                List<CPOverrideWrapper> cpOverrideWrappers = null;

                if (isShowOnlyBaseRoomTypesFlag() || isPseudoShowOnlyBaseRoomTypesFlag()) {
                    cpOverrideWrappers = getNonBaseRoomTypes(overrideWrapper, masterPricingAccomClass, uiContext.getSystemCaughtUpDate());
                } else {
                    cpOverrideWrappers = getCPOverrideWrappersNotForAccomTypeWithoutSpecificOverrides(overrideWrapper, masterPricingAccomClass);
                }

                for (CPOverrideWrapper cpOverrideWrapper : cpOverrideWrappers) {
                    BigDecimal newBar = cpDecisionContext.calculateRoundedRate(cpOverrideWrapper.getCpDecisionBAROutput());
                    cpOverrideWrapper.setRoundedBAR(newBar);
                    cpOverrideWrapper.getCpDecisionBAROutput().setPrettyBAR(newBar);
                    cpOverrideWrapper.getCpDecisionBAROutput().setRoomsOnlyBAR(Supplement.removeSupplementFrom(newBar, cpDecisionContext.getSupplementFor(cpOverrideWrapper.getCpDecisionBAROutput()).orElse(Supplement.zeroSupplement())));
                    cpOverrideWrapper.getCpDecisionBAROutput().setFinalBAR(newBar);
                    overrideWrapper.setIsPendingSaveNonBaseRoomTypes(true);
                }
            } else {
                if (overrideWrapper.getOriginalSpecificOverride() == null && (!isShowOnlyBaseRoomTypesFlag() || !isPseudoShowOnlyBaseRoomTypesFlag())) {
                    //only execute this code in non showOnlyBaseRoomType mode as you can't remove a non base room type override because
                    //the non base room types never get displayed in the UI.
                    CPOverrideWrapper parentBaseRoomType = findParentBaseRoomTypeWithCeilingAndFloorOverride(overrideWrapper);
                    if (parentBaseRoomType != null) {
                        //TODO Get newBar from base room type, this is to remove a saved override when a saved and/or pending ceiling exists
                        CPDecisionBAROutput cpDecisionBAROutput = overrideWrapper.getCpDecisionBAROutput();

                        BigDecimal valueWithOffsetApplied = cpDecisionContext.applyOffset(cpDecisionBAROutput, parentBaseRoomType.getRoundedBAR());
                        overrideWrapper.setRoundedBAR(valueWithOffsetApplied);
                        overrideWrapper.getCpDecisionBAROutput().setPrettyBAR(valueWithOffsetApplied);
                        overrideWrapper.getCpDecisionBAROutput().setRoomsOnlyBAR(Supplement.removeSupplementFrom(valueWithOffsetApplied, cpDecisionContext.getSupplementFor(overrideWrapper.getCpDecisionBAROutput()).orElse(Supplement.zeroSupplement())));
                        overrideWrapper.getCpDecisionBAROutput().setFinalBAR(valueWithOffsetApplied);
                    }
                }
            }
        } else {
            removeNonPersistedOverrides(cpDecisionContext, overrideWrapper, pricingAccomClasses);
        }
    }

    private void removeNonPersistedOverrides(CPDecisionContext cpDecisionContext, CPOverrideWrapper overrideWrapper, List<PricingAccomClass> pricingAccomClasses) {
        if (overridesMap.containsKey(overrideWrapper.getCpDecisionBAROutput())) {
            overridesMap.remove(overrideWrapper.getCpDecisionBAROutput());
        } else { //we know that this particular overrideWrapper probably isn't even an override, but a product of the fact that user checked "span across room types"
            removeOverridesFromNonBaseRoomTypes(cpDecisionContext, overrideWrapper, pricingAccomClasses);
        }

        overrideWrapper.setSpecificOverride(null);
        overrideWrapper.setFloorOverride(null);
        overrideWrapper.setCeilingOverride(null);
        overrideWrapper.setIsPendingDelete(false);
    }

    public BigDecimal getPrettyPriceAfterRemove(CPDecisionContext cpDecisionContext, CPOverrideWrapper overrideWrapper) {
        CPDecisionBAROutput clone = overrideWrapper.getCpDecisionBAROutput().clone();
        clone.setSpecificOverride(null);
        clone.setFloorOverride(null);
        clone.setCeilingOverride(null);
        clone.setOverrideType(DecisionOverrideType.NONE);

        // Look up the supplement value to set the roomsOnlyBAR, and add the supplement back into the pretty price
        return cpDecisionContext.calculateRoundedRate(clone);
    }

    private void removeOverridesFromNonBaseRoomTypes(CPDecisionContext cpDecisionContext, CPOverrideWrapper overrideWrapper, List<PricingAccomClass> pricingAccomClasses) {
        if (!isShowOnlyBaseRoomTypesFlag() || !isPseudoShowOnlyBaseRoomTypesFlag()) {
            CPOverrideWrapper parentBaseRoomTypeWrapper = overrideWrapper.getParent().getCpOverrideWrappers().stream()
                    .filter(cpOverrideWrapper -> isBaseRoomType(pricingAccomClasses, cpOverrideWrapper.getCpDecisionBAROutput().getAccomType()))
                    .filter(cpOverrideWrapper -> cpOverrideWrapper.getProduct().equals(overrideWrapper.getProduct()))
                    .findFirst()
                    .orElse(null);
            if (parentBaseRoomTypeWrapper != null && overridesMap.get(parentBaseRoomTypeWrapper.getCpDecisionBAROutput()) != null) {
                Optional<AccomTypeSupplementValue> nonBaseRTSupplementValue = cpDecisionContext.getSupplementFor(overrideWrapper.getCpDecisionBAROutput());
                BigDecimal newBar = cpDecisionContext.calculateRoundedRate(overrideWrapper.getCpDecisionBAROutput());
                overrideWrapper.getCpDecisionBAROutput().setPrettyBAR(newBar);
                overrideWrapper.getCpDecisionBAROutput().setFinalBAR(newBar);
                overrideWrapper.getCpDecisionBAROutput().setRoomsOnlyBAR(Supplement.removeSupplementFrom(newBar, nonBaseRTSupplementValue.orElse(Supplement.zeroSupplement())));
                overridesMap.get(parentBaseRoomTypeWrapper.getCpDecisionBAROutput()).setIsPendingSaveNonBaseRoomTypes(true);
            }
        }
    }

    private boolean isBaseRoomTypeAndTrashIconToggleForCeilingAndOrFloorOverride(CPOverrideWrapper overrideWrapper) {
        return overrideWrapper.isBaseRoomType() && !overrideWrapper.getOriginalOverrideType().equals(DecisionOverrideType.USER);
    }

    private CPOverrideWrapper findParentBaseRoomTypeWithCeilingAndFloorOverride(CPOverrideWrapper overrideWrapper) {
        return overrideWrapper.getParent().getCpOverrideWrappers().stream()
                .filter(this::isBaseRoomTypeCeilingAndOrFloorOverride)
                .filter(cpOverrideWrapper -> cpOverrideWrapper.getProduct().equals(overrideWrapper.getProduct()))
                .findFirst()
                .orElse(null);
    }

    public boolean isBaseRoomType(List<PricingAccomClass> pricingAccomClasses, AccomType accomType) {
        if (accomType != null && CollectionUtils.isNotEmpty(pricingAccomClasses)) {
            for (PricingAccomClass pricingAccomClass : pricingAccomClasses) {
                if (pricingAccomClass.getAccomClass().equals(accomType.getAccomClass())) {
                    return pricingAccomClass.getAccomType().equals(accomType);
                }
            }
        }
        return false;
    }

    protected boolean isBaseRoomTypeCeilingAndOrFloorOverride(CPOverrideWrapper overrideWrapper) {
        return overrideWrapper.isBaseRoomType() && (overrideWrapper.getCeilingOverride() != null || overrideWrapper.getFloorOverride() != null);
    }

    protected boolean isNotSameRoomType(CPOverrideWrapper overrideWrapper, CPOverrideWrapper cpOverrideWrapper) {
        return !overrideWrapper.getCpDecisionBAROutput().getAccomType().equals(cpOverrideWrapper.getCpDecisionBAROutput().getAccomType());
    }

    @ForTesting
    public Map<CPDecisionBAROutput, CPOverrideWrapper> getOverridesMap() {
        return overridesMap;
    }

    @ForTesting
    public void setOverridesMap(Map<CPDecisionBAROutput, CPOverrideWrapper> overridesMap) {
        this.overridesMap = overridesMap;
    }

    private void fetchRequiredData(boolean dateChange) {
        if (isSpecificRoomClassSelected()) {
            roomClassDetailsMap = service.getRoomClassDetails(getCpPricingFilter().getSelectedRoomClass().getId(), getCpPricingFilter().getStartDate(), getCpPricingFilter().getEndDate(), uiOptimizationEnabled);
        }
        if (availableCapacityToSellEnabled) {
            loadDataForAvailableCapaictyTosell();
        }
        if (dateChange && getCpPricingFilter().getStartDate() != null && getCpPricingFilter().getEndDate() != null) {
            businessAnalysisDailyDataDtos = businessAnalysisDashboardService.getBusinessAnalysisDailyDataDtos(getCpPricingFilter().getStartDate().toDate(), getCpPricingFilter().getEndDate().toDate());
            businessAnalysisDailyIndicatorDtos = businessAnalysisDashboardService.getBusinessAnalysisDailyIndicatorDtos(getCpPricingFilter().getStartDate().toDate(), getCpPricingFilter().getEndDate().toDate());
        }
    }

    private void loadDataForAvailableCapaictyTosell() {
        if (isSpecificRoomClassSelected()) {
            accomTypeDetailsMapById = service.getAllRoomTypeDetails(getCpPricingFilter().getSelectedRoomClass().getId(), getCpPricingFilter().getStartDate(), getCpPricingFilter().getEndDate());
        } else {
            roomClassDetailsMapById = service.getAllRoomClassDetails(getAccomClassIds(), getCpPricingFilter().getStartDate(), getCpPricingFilter().getEndDate());
        }
    }

    private void fetchRequiredDataUsingThreadPool(boolean dateChange) {
        Map<String, Supplier<Object>> executableItemsMap = new HashMap<>();
        if (isSpecificRoomClassSelected()) {
            executableItemsMap.put(
                    "roomClassDetailsMap", () -> service.getRoomClassDetails(getCpPricingFilter().getSelectedRoomClass().getId(),
                            getCpPricingFilter().getStartDate(), getCpPricingFilter().getEndDate(), uiOptimizationEnabled));
        }
        if (availableCapacityToSellEnabled) {
            loadDataForAvailableCapacityToSell(executableItemsMap);
        }

        if (dateChange && getCpPricingFilter().getStartDate() != null && getCpPricingFilter().getEndDate() != null) {
            executableItemsMap.put(
                    "businessAnalysisDailyDataDtos", () -> businessAnalysisDashboardService.getBusinessAnalysisDailyDataDtos(
                            getCpPricingFilter().getStartDate().toDate(), getCpPricingFilter().getEndDate().toDate()));
            executableItemsMap.put(
                    "businessAnalysisDailyIndicatorDtos",
                    () -> businessAnalysisDashboardService.getBusinessAnalysisDailyIndicatorDtos(
                            getCpPricingFilter().getStartDate().toDate(), getCpPricingFilter().getEndDate().toDate()));
        }

        if (executableItemsMap.size() > 0) {
            TetrisDataThreadPool<Object> dataThreadPool = new TetrisDataThreadPool<>(TetrisDataThreadPool.getThreadPoolExecutor(executableItemsMap.size(), "prc-vw-fth-req-dt"), UI.getCurrent());
            Map<String, Object> result = dataThreadPool.executeSupplierMap(executableItemsMap);
            if (result.containsKey("roomClassDetailsMap")) {
                roomClassDetailsMap = (Map<LocalDate, CPRoomClassDTO>) result.get("roomClassDetailsMap");
            }
            if (result.containsKey("accomTypesDetailsMap")) {
                accomTypeDetailsMapById = (Map<LocalDate, Map<Integer, CPRoomClassDTO>>) result.get("accomTypesDetailsMap");
            }
            if (result.containsKey("allRoomClassDetailsMap")) {
                roomClassDetailsMapById = (Map<LocalDate, Map<Integer, CPRoomClassDTO>>) result.get("allRoomClassDetailsMap");
            }
            if (result.containsKey("businessAnalysisDailyDataDtos")) {
                businessAnalysisDailyDataDtos = (List<BusinessAnalysisDailyDataDto>) result.get("businessAnalysisDailyDataDtos");
            }
            if (result.containsKey("businessAnalysisDailyIndicatorDtos")) {
                businessAnalysisDailyIndicatorDtos = (List<BusinessAnalysisDailyIndicatorDto>) result.get(
                        "businessAnalysisDailyIndicatorDtos");
            }
        }
    }

    private void loadDataForAvailableCapacityToSell(Map<String, Supplier<Object>> executableItemsMap) {
        if (isSpecificRoomClassSelected()) {
            executableItemsMap.put(
                    "accomTypesDetailsMap", () -> service.getAllRoomTypeDetails(getCpPricingFilter().getSelectedRoomClass().getId(),
                            getCpPricingFilter().getStartDate(), getCpPricingFilter().getEndDate()));
        } else {
            executableItemsMap.put(
                    "allRoomClassDetailsMap", () -> service.getAllRoomClassDetails(getAccomClassIds(),
                            getCpPricingFilter().getStartDate(), getCpPricingFilter().getEndDate()));
        }
    }

    private String getAccomClassIds() {
        return StringUtils.join(accomClassIds, ",");
    }

    private List<CPBARDecisionUIWrapper> createCPBarDecisionUIWrappersFrom(Map<LocalDate, List<CPDecisionBAROutput>> dateMap,
                                                                           Map<LocalDate, List<CPDecisionBARNOVRDetails>> dateMapNOVR) {
        Date optimizationWindowEndDate = null;
        boolean isLongTermBDEWindowOn = configParamsService.getBooleanParameterValue(ENABLE_UPLOAD_ON_OVERRIDE_FOR_EXTENDED_WINDOW);
        if (isLongTermBDEWindowOn) {
            optimizationWindowEndDate = dateService.getOptimizationWindowEndDateBDE();
        }
        return createCPBarDecisionDtosFrom(dateMap, dateMapNOVR, isLongTermBDEWindowOn, LocalDateUtils.fromDate(optimizationWindowEndDate)).stream()
                .map(this::createCPBarDecisionUIWrapper)
                .collect(Collectors.toList());
    }

    private CPBARDecisionUIWrapper createCPBarDecisionUIWrapper(CPBARDecisionDTO dto) {
        CPBARDecisionUIWrapper cpbarDecisionUIWrapper = new CPBARDecisionUIWrapper(dto, isLraEnabled());
        //lets up front set if a CPOverrideWrapper represents a base room type or has a product rate offset override
        List<CPOverrideWrapper> cpOverrideWrappers = cpbarDecisionUIWrapper.getCpOverrideWrappers();
        for (CPOverrideWrapper cpOverrideWrapper : cpOverrideWrappers) {
            cpOverrideWrapper.setIsBaseRoomType(baseRoomTypeList.contains(cpOverrideWrapper.getCpDecisionBAROutput().getAccomType()));
            cpOverrideWrapper.setProductRateOffsetOverrides(getProductRateOffsetOverrideFor(cpOverrideWrapper));
            cpOverrideWrapper.setOriginalProductRateOffsetOverrides(cpOverrideWrapper.getProductRateOffsetOverrides());
        }
        return cpbarDecisionUIWrapper;
    }

    private List<ProductRateOffsetOverride> getProductRateOffsetOverrideFor(CPOverrideWrapper cpOverrideWrapper) {
        return getActiveProductRateOffsetOverrides().stream()
                .filter(override -> override.getStatusId().equals(Status.ACTIVE.getId()) &&
                        override.getProduct().equals(cpOverrideWrapper.getCpDecisionBAROutput().getProduct()) &&
                        override.getAccomClass().equals(cpOverrideWrapper.getAccomClass()) &&
                        override.getOccupancyDate().equals(cpOverrideWrapper.getCpDecisionBAROutput().getArrivalDate()))
                .collect(Collectors.toList());
    }

    private List<CPBARDecisionDTO> createCPBarDecisionDtosFrom(Map<LocalDate, List<CPDecisionBAROutput>> dateMap,
                                                               Map<LocalDate, List<CPDecisionBARNOVRDetails>> dateMapNOVR,
                                                               boolean isLongTermBDEWindowOn, LocalDate optimizationWindowEndDate) {
        return dateMap.entrySet().stream()
                .map(dto -> createCpBarDecisionDto(dto, isLongTermBDEWindowOn, optimizationWindowEndDate))
                .peek(dto -> dto.setDecisionsNovr(dateMapNOVR.get(dto.getDate())))
                .collect(Collectors.toList());
    }

    private CPBARDecisionDTO createCpBarDecisionDto(Map.Entry<LocalDate, List<CPDecisionBAROutput>> entry,
                                                    boolean isLongTermBDEWindowOn, LocalDate optimizationWindowEndDateJoda) {
        CPBARDecisionDTO dto = new CPBARDecisionDTO();
        dto.setDate(entry.getKey());
        dto.setLongTermBDEWindow(isLongTermBDEWindowOn && entry.getKey().isAfter(optimizationWindowEndDateJoda));
        dto.setDecisions(entry.getValue());
        if (isSpecificRoomClassSelected()) {
            dto.setLrv(getLrvFor(entry));
        }
        updateDtoWithBusinessAnalysisDailyData(entry, dto);
        updateDtoWithSpecialEventsData(entry, dto);
        return dto;
    }

    private void updateDtoWithBusinessAnalysisDailyData(Map.Entry<LocalDate, List<CPDecisionBAROutput>> entry, CPBARDecisionDTO dto) {
        BusinessAnalysisDailyDataDto dailyDataDto = getDailyDataDTO(entry.getKey());
        if (dailyDataDto != null) {
            dto.setOccupancyForecast(dailyDataDto.getOccupancyForecast());
            dto.setOccupancyForecastPercentage(dailyDataDto.getOccupancyForecastPerc());
            dto.setOutOfOrder(dailyDataDto.getOutOfOrder());
            dto.setRoomsOnBooks(dailyDataDto.getOnBooks());
            dto.setOnBooksPerc(dailyDataDto.getOnBooksPerc());
            dto.setAuthorizedCapacity(dailyDataDto.getAuthorizedCapacity());
            dto.setEffectiveCapacity(dailyDataDto.getEffectiveCapacity());
            dto.setAvailableCapacityToSell(service.calculateCapacityToSell(dailyDataDto));
        }
    }

    private void updateDtoWithSpecialEventsData(Map.Entry<LocalDate, List<CPDecisionBAROutput>> entry, CPBARDecisionDTO dto) {
        BusinessAnalysisDailyIndicatorDto dailyIndicatorDto = getDailyIndicatorDTO(entry.getKey());
        if (dailyIndicatorDto != null && hasSpecialEvent(dailyIndicatorDto)) {
            dto.setSpecialEvents(businessAnalysisDashboardService.getBusinessAnalysisSpecialEventDtos(entry.getKey().toDate()));
        }
    }

    private boolean hasSpecialEvent(BusinessAnalysisDailyIndicatorDto dailyIndicatorDto) {
        return dailyIndicatorDto.isSpecialEventImpactFCST() || dailyIndicatorDto.isSpecialEventInfoOnly();
    }

    private BigDecimal getLrvFor(Map.Entry<LocalDate, List<CPDecisionBAROutput>> entry) {
        return lastRoomValues.stream()
                .filter(lastRoomValue -> lastRoomValue.getOccupancyDate().equals(entry.getKey().toDate())
                        && lastRoomValue.getAccomClassID().equals(getCpPricingFilter().getSelectedRoomClass().getId()))
                .findFirst()
                .map(LastRoomValue::getValue)
                .orElse(null);
    }

    public BigDecimal getMasterClassLrvForOccupancyDate(LocalDate occupancyDate) {
        AccomClass masterAccomClass = businessAnalysisDashboardService.getMasterAccomClass();
        if (masterAccomClass == null || occupancyDate == null) {
            return null;
        }
        return lastRoomValues.stream()
                .filter(lrv -> occupancyDate.toDate().equals(lrv.getOccupancyDate())
                        && lrv.getAccomClassID().equals(masterAccomClass.getId()))
                .findFirst()
                .map(LastRoomValue::getValue)
                .orElse(null);
    }

    private List<CPDecisionBAROutput> getCPBarDecisionOutputs(Map<LocalDate, List<CPDecisionBAROutput>> dateMap) {
        return dateMap.values().stream()
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
    }

    private Map<LocalDate, List<CPDecisionBAROutput>> createDateToDecisionsMap(boolean dateChange, boolean isInlineEditViewSelected) {
        List<CPDecisionBAROutput> decisions = filterDecisions(getUpdatedDecisions(dateChange), getActiveProductRateOffsetOverrides(), isInlineEditViewSelected);
        Map<LocalDate, List<CPDecisionBAROutput>> dateMap = new HashMap<>();
        for (CPDecisionBAROutput decision : decisions) {
            if (dateMap.containsKey(decision.getArrivalDate())) {
                dateMap.get(decision.getArrivalDate()).add(decision);
            } else {
                dateMap.put(decision.getArrivalDate(), new ArrayList<>(Collections.singletonList(decision)));
            }
        }
        return dateMap;
    }

    List<CPDecisionBAROutput> filterDecisions(List<CPDecisionBAROutput> cpDecisionBAROutputs,
                                              Set<ProductRateOffsetOverride> productRateOffsetOverrides,
                                              boolean isInlineEditViewSelected) {
        if (CollectionUtils.isEmpty(cpDecisionBAROutputs)) {
            return cpDecisionBAROutputs;
        }

        CPPricingFilter filter = getCpPricingFilter();
        List<CPDecisionBAROutput> filteredCpDecisionBAROutputs = cpDecisionBAROutputs;

        // filter for room class ranking violation, here we want to show all room class for that date and not just that room class
        // so that user can see reason for that warning . If overide is present then only these's chance to have that warning so
        // using that to filter out all data first.
        if (CollectionUtils.isNotEmpty(filter.getSelectedRestrictions()) && filter.getSelectedRestrictions().contains(OVERRIDE_VIOLATES_RANKING_RULE)) {
            filteredCpDecisionBAROutputs = filterDatesWithOverrides(filteredCpDecisionBAROutputs);
        }

        if (!DateWithFilterEnum.ALL_DECISIONS.equals(filter.getDateWithFilterEnum())) {
            filteredCpDecisionBAROutputs = filterByDateWith(filteredCpDecisionBAROutputs);
        }

        if (CollectionUtils.isNotEmpty(filter.getSelectedDaysOfWeek())) {
            filteredCpDecisionBAROutputs = filterByDayOfWeek(filteredCpDecisionBAROutputs);
        }

        if (CollectionUtils.isNotEmpty(filter.getSelectedOverrides())) {
            filteredCpDecisionBAROutputs = filterByOverrides(filteredCpDecisionBAROutputs, productRateOffsetOverrides, isInlineEditViewSelected);
        }

        filteredCpDecisionBAROutputs = filterRestrictions(filteredCpDecisionBAROutputs);

        return filteredCpDecisionBAROutputs;
    }

    private List<CPDecisionBAROutput> filterRestrictions(List<CPDecisionBAROutput> filteredCpDecisionBAROutputs) {
        Set<DecisionReasonType> selectedRestrictions = getCpPricingFilter().getSelectedRestrictions();
        if (CollectionUtils.isNotEmpty(selectedRestrictions)) {
            Set<DecisionReasonType> restrictions = new HashSet<>();
            if (selectedRestrictions.contains(LRV_GT_BAR)) {
                restrictions.add(LRV_GT_BAR);
            }
            if (selectedRestrictions.contains(SUBOPTIMAL_OPTIMAL_BAR_DUE_TO_LRA)) {
                restrictions.add(SUBOPTIMAL_OPTIMAL_BAR_DUE_TO_LRA);
            }
            if (selectedRestrictions.contains(BASE_PRODUCT_PRICE_BELOW_FAB_PRODUCTS_RATE)) {
                restrictions.add(BASE_PRODUCT_PRICE_BELOW_FAB_PRODUCTS_RATE);
            }
            if (restrictions.size() > 0) {
                filteredCpDecisionBAROutputs = filterByRestrictions(filteredCpDecisionBAROutputs, restrictions);
            }
        }
        return filteredCpDecisionBAROutputs;
    }

    private List<CPDecisionBAROutput> filterDatesWithOverrides(List<CPDecisionBAROutput> filteredCpDecisionBAROutputs) {
        Set<LocalDate> overriddenDates = filteredCpDecisionBAROutputs.stream().filter(decision -> null != decision.getFloorOverride() ||
                null != decision.getSpecificOverride() ||
                null != decision.getCeilingOverride()).map(CPDecisionBAROutput::getArrivalDate).collect(Collectors.toSet());
        List<CPDecisionBAROutput> cpDecisionBAROutputs = filteredCpDecisionBAROutputs.stream()
                .filter(rec -> overriddenDates.contains(rec.getArrivalDate()) && rec.getProduct().isSystemDefaultOrIndependentProduct())
                .collect(Collectors.toList());
        return cpDecisionBAROutputs;
    }

    List<CPDecisionBAROutput> filterByDayOfWeek(List<CPDecisionBAROutput> storedDecisions) {
        Set<DayOfWeek> selectedDaysOfWeek = getCpPricingFilter().getSelectedDaysOfWeek();
        return selectedDaysOfWeek.stream()
                .map(selectedDayOfWeek -> getDecisionsFor(selectedDayOfWeek, storedDecisions))
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
    }

    private List<CPDecisionBAROutput> getDecisionsFor(DayOfWeek selectedDayOfWeek, List<CPDecisionBAROutput> storedDecisions) {
        return storedDecisions.stream()
                .filter(cpDecisionBAROutput -> selectedDayOfWeek.equals(getDayOfWeekFor(cpDecisionBAROutput)))
                .collect(Collectors.toList());
    }

    private DayOfWeek getDayOfWeekFor(CPDecisionBAROutput cpDecisionBAROutput) {
        return LocalDateUtils.getDayOfWeek(LocalDateUtils.fromDate(cpDecisionBAROutput.getArrivalDate().toDate()));
    }

    List<CPDecisionBAROutput> filterByOverrides(List<CPDecisionBAROutput> storedDecisions,
                                                Set<ProductRateOffsetOverride> productRateOffsetOverrides,
                                                boolean isInlineEditViewSelected) {
        Set<CPOverrideType> selectedOverrides = getCpPricingFilter().getSelectedOverrides();
        Set<CPDecisionBAROutput> filteredStoredDecisions = new HashSet<>();

        for (Product product : getCpPricingFilter().getProducts()) {
            Set<ProductRateOffsetOverride> filteredProductRateOffsetOverrides = productRateOffsetOverrides.stream().filter(productRateOffsetOverride -> productRateOffsetOverride.getProduct().equals(product)).collect(Collectors.toSet());
            if (isInlineEditViewSelected) {
                filterOverridesByType(storedDecisions, filteredStoredDecisions, CPOverrideType.SPECIFIC);
                filterOverridesByType(storedDecisions, filteredStoredDecisions, CPOverrideType.CEILING);
                filterOverridesByType(storedDecisions, filteredStoredDecisions, CPOverrideType.FLOOR);
                filteredStoredDecisions.addAll(storedDecisions.stream().filter(cpDecisionBAROutput -> hasProductRateOffsetOverride(cpDecisionBAROutput, filteredProductRateOffsetOverrides)).collect(Collectors.toList()));
            } else if (!product.isSystemDefaultOrIndependentProduct() && CollectionUtils.isNotEmpty(filteredProductRateOffsetOverrides)) {
                //If agile rates product and Specific is selected, return all ceiling/floor overrides
                if (selectedOverrides.contains(CPOverrideType.SPECIFIC)) {
                    filteredStoredDecisions.addAll(storedDecisions.stream().filter(cpDecisionBAROutput -> hasProductRateOffsetOverride(cpDecisionBAROutput, filteredProductRateOffsetOverrides)).collect(Collectors.toList()));
                }
            } else if(selectedOverrides.contains(CPOverrideType.GPINVENTORY) && product.isGroupProduct()) {
                List<DecisionGPInventoryLimitOverride> inventoryLimitOverrides = inventoryLimitDecisionService.getDecisionInventoryLimitOverridesByOccupancyDateRange(getCpPricingFilter().getStartDate().toDate(),
                        getCpPricingFilter().getEndDate().toDate());
                filteredStoredDecisions.addAll(storedDecisions.stream().filter(cpDecisionBAROutput -> cpDecisionBAROutput.getProduct().isGroupProduct())
                        .filter(cpDecisionBAROutput -> inventoryLimitOverrides.stream().map(DecisionGPInventoryLimitOverride::getOccupancyDate)
                                .anyMatch(occupancyDate -> occupancyDate.equals(cpDecisionBAROutput.getArrivalDate().toDate())))
                        .collect(Collectors.toList()));
            }

            else {
                for (CPOverrideType selectedOverride : selectedOverrides) {
                    filterOverridesByType(storedDecisions, filteredStoredDecisions, selectedOverride);
                }
            }
        }
        return new ArrayList<>(filteredStoredDecisions);
    }

    private void filterOverridesByType(List<CPDecisionBAROutput> storedDecisions, Set<CPDecisionBAROutput> filteredStoredDecisions, CPOverrideType selectedOverride) {
        if (selectedOverride.equals(CPOverrideType.CEILING)) {
            filteredStoredDecisions.addAll(storedDecisions.stream().filter(CPDecisionBAROutput::hasCeilingOverride).collect(Collectors.toList()));
        } else if (selectedOverride.equals(CPOverrideType.FLOOR)) {
            filteredStoredDecisions.addAll(storedDecisions.stream().filter(CPDecisionBAROutput::hasFloorOverride).collect(Collectors.toList()));
        } else if (selectedOverride.equals(CPOverrideType.SPECIFIC)) {
            filteredStoredDecisions.addAll(storedDecisions.stream().filter(CPDecisionBAROutput::hasSpecificOverride).collect(Collectors.toList()));
        } else if (isGroupFloorEnabled() && selectedOverride.equals(CPOverrideType.GPFLOOR)) {
            filteredStoredDecisions.addAll(storedDecisions.stream().filter(cpDecisionBAROutput -> (DecisionOverrideType.GPFLOOR.equals(cpDecisionBAROutput.getOverrideType())
                    || DecisionOverrideType.GPFLOORANDCEIL.equals(cpDecisionBAROutput.getOverrideType()))
                    && cpDecisionBAROutput.hasFloorOverride()).collect(Collectors.toList()));
        }
    }

    protected boolean hasProductRateOffsetOverride(CPDecisionBAROutput cpDecisionBAROutput, Set<ProductRateOffsetOverride> productRateOffsetOverrides) {
        return productRateOffsetOverrides.stream()
                .filter(rateOffset -> rateOffset.getProduct().equals(cpDecisionBAROutput.getProduct()))
                .anyMatch(rateOffset -> rateOffset.getAccomClass().equals(cpDecisionBAROutput.getAccomType().getAccomClass()) &&
                        rateOffset.getOccupancyDate().equals(cpDecisionBAROutput.getArrivalDate()));
    }

    List<CPDecisionBAROutput> filterByRestrictions(List<CPDecisionBAROutput> filteredCpDecisionBAROutputs, Set<DecisionReasonType> selectedRestrictions) {
        return filteredCpDecisionBAROutputs.stream().filter(cpDecisionBAROutput -> selectedRestrictions.contains(DecisionReasonType.fromId(cpDecisionBAROutput.getDecisionReasonTypeId()))).collect(Collectors.toList());
    }

    List<CPDecisionBAROutput> filterByDateWith(List<CPDecisionBAROutput> storedDecisions) {
        CPPricingFilter filter = getCpPricingFilter();
        DateWithFilterEnum dateWithFilterEnum = filter.getDateWithFilterEnum();
        if (dateWithFilterEnum.equals(DateWithFilterEnum.CHANGES_SINCE)) {
            paceDecisions = service.getDecisionsFromSpecifiedDate(filter.getProducts(), filter.getStartDate(), filter.getEndDate(), getSelectedAccomTypes(), fixTimeZone(filter.getDateWithDate()));
            return filterOutDecisionsByPaceData(storedDecisions, paceDecisions);
        }

        if (dateWithFilterEnum.equals(DateWithFilterEnum.CHANGES_SINCE_LAST_BDE)) {
            paceDecisions = service.getDecisionsFromLastBDE(filter.getProducts(), filter.getStartDate(), filter.getEndDate(), getSelectedAccomTypes());
            return filterOutDecisionsByPaceData(storedDecisions, paceDecisions);
        }

        if (dateWithFilterEnum.equals(DateWithFilterEnum.CHANGES_SINCE_LAST_IDP)) {
            paceDecisions = service.getDecisionsFromLastCDP(filter.getProducts(), filter.getStartDate(), filter.getEndDate(), getSelectedAccomTypes());
            return filterOutDecisionsByPaceData(storedDecisions, paceDecisions);
        }

        if (dateWithFilterEnum.equals(DateWithFilterEnum.FINAL_PRICE_EQUAL_TO)) {
            return storedDecisions.stream().filter(cpDecisionBAROutput -> cpDecisionBAROutput.getFinalBAR() != null && getFinalPrice(cpDecisionBAROutput).equals(filter.getDateWithBigDecimal().setScale(2))).collect(Collectors.toList());
        }

        if (dateWithFilterEnum.equals(DateWithFilterEnum.FINAL_PRICE_GREATER_THAN)) {
            return storedDecisions.stream().filter(cpDecisionBAROutput -> cpDecisionBAROutput.getFinalBAR() != null && getFinalPrice(cpDecisionBAROutput).compareTo(filter.getDateWithBigDecimal().setScale(2)) > 0).collect(Collectors.toList());
        }

        if (dateWithFilterEnum.equals(DateWithFilterEnum.FINAL_PRICE_LESS_THAN)) {
            return storedDecisions.stream().filter(cpDecisionBAROutput -> cpDecisionBAROutput.getFinalBAR() != null && getFinalPrice(cpDecisionBAROutput).compareTo(filter.getDateWithBigDecimal().setScale(2)) < 0).collect(Collectors.toList());
        }

        if (dateWithFilterEnum.equals(DateWithFilterEnum.OCCUPANCY_FORECAST_GREATER_THAN)) {
            return getCpDecisionBAROutputsForOccupancyForecastGreaterThanFilter(storedDecisions);
        }

        if (dateWithFilterEnum.equals(DateWithFilterEnum.OCCUPANCY_FORECAST_LESS_THAN)) {
            return getCpDecisionBAROutputsForOccupancyForecastLessThanFilter(storedDecisions);
        }

        if (isGroupFloorEnabled() && dateWithFilterEnum.equals(DateWithFilterEnum.GROUP_FLOOR_VALUE_EQUAL_TO_FINAL_BAR)) {
            return getCpDecisionBarOutputsForGroupFloor(storedDecisions);
        }

        return new ArrayList<>();
    }

    private List<CPDecisionBAROutput> getCpDecisionBarOutputsForGroupFloor(List<CPDecisionBAROutput> storedDecisions) {
        return storedDecisions.stream()
                .filter(cpDecisionBAROutput -> isValidDecisionOverrideType(cpDecisionBAROutput)
                        && isEquals(cpDecisionBAROutput.getFinalBAR(), cpDecisionBAROutput.getFloorOverride()))
                .collect(Collectors.toList());
    }

    private boolean isEquals(BigDecimal finalBAR, BigDecimal floorOverride) {
        return finalBAR.setScale(2).equals(floorOverride.setScale(2))
                && finalBAR.compareTo(floorOverride) == 0;
    }

    private boolean isValidDecisionOverrideType(CPDecisionBAROutput cpDecisionBAROutput) {
        return DecisionOverrideType.GPFLOOR.equals(cpDecisionBAROutput.getOverrideType())
                || DecisionOverrideType.GPFLOORANDCEIL.equals(cpDecisionBAROutput.getOverrideType());
    }

    private List<CPDecisionBAROutput> getCpDecisionBAROutputsForOccupancyForecastLessThanFilter(List<CPDecisionBAROutput> storedDecisions) {
        List<CPDecisionBAROutput> filteredStoredDecisions = new ArrayList<>();
        for (CPDecisionBAROutput decision : storedDecisions) {
            BusinessAnalysisDailyDataDto dailyDataDto = getDailyDataDTO(decision.getArrivalDate());
            if (dailyDataDto != null && dailyDataDto.getOccupancyForecastPerc() != null && isOccupancyForecastLessFor(dailyDataDto, getCpPricingFilter().getDateWithInteger())) {
                filteredStoredDecisions.add(decision);
            }
        }
        return filteredStoredDecisions;
    }

    private boolean isOccupancyForecastLessFor(BusinessAnalysisDailyDataDto dailyDataDto, Integer cpPricingFilterDate) {
        return dailyDataDto.getOccupancyForecastPerc().compareTo(new BigDecimal(cpPricingFilterDate).setScale(2)) < 0;
    }

    private List<CPDecisionBAROutput> getCpDecisionBAROutputsForOccupancyForecastGreaterThanFilter(List<CPDecisionBAROutput> storedDecisions) {
        List<CPDecisionBAROutput> filteredStoredDecisions = new ArrayList<>();
        for (CPDecisionBAROutput decision : storedDecisions) {
            BusinessAnalysisDailyDataDto dailyDataDto = getDailyDataDTO(decision.getArrivalDate());
            if (dailyDataDto != null && dailyDataDto.getOccupancyForecastPerc() != null
                    && dailyDataDto.getOccupancyForecastPerc().compareTo(new BigDecimal(getCpPricingFilter().getDateWithInteger()).setScale(2)) > 0) {
                filteredStoredDecisions.add(decision);
            }
        }
        return filteredStoredDecisions;
    }

    public boolean isGroupFloorEnabled() {
        return configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.IS_GROUP_FLOOR_OVERRIDE_ENABLED);
    }

    BigDecimal getFinalPrice(CPDecisionBAROutput cpDecisionBAROutput) {
        BigDecimal barValue = cpDecisionBAROutput.getSpecificOverride() != null ?
                cpDecisionBAROutput.getSpecificOverride() : cpDecisionBAROutput.getFinalBAR();
        return barValue != null ? barValue.setScale(2, RoundingMode.HALF_UP) : BigDecimal.valueOf(0.00).setScale(2, RoundingMode.HALF_UP);
    }

    List<AccomType> getSelectedAccomTypes() {
        if (!isSpecificRoomClassSelected() && getInventoryGroupDto() == -1) {
            return getBaseRoomTypeList();
        } else if (!isSpecificRoomClassSelected()) {
            List<AccomType> baseRoomTypeList = getBaseRoomTypeList();
            return this.selectedAccomTypes.stream().filter(accomType -> baseRoomTypeList.contains(accomType)).collect(Collectors.toList());
        }
        final Set<AccomType> selectedRoomTypes = getCpPricingFilter().getSelectedRoomTypes();
        return CollectionUtils.isEmpty(selectedRoomTypes) ?
                new ArrayList<>(getAccomTypes()) : new ArrayList<>(selectedRoomTypes);
    }

    public int getInventoryGroupDto() {
        return (getCpPricingFilter().getInventoryGroupDto() == null) ? -1 : getCpPricingFilter().getInventoryGroupDto().getId();
    }

    private List<AccomType> getAccomTypes() {
        if (accomTypeFiltrationCriteria == null) {
            return getCpPricingFilter().getSelectedRoomClass().getActiveWithCapacityAccomTypes();
        }
        return getCpPricingFilter().getSelectedRoomClass().filterAndGetAccomTypes(accomTypeFiltrationCriteria);
    }

    private List<CPDecisionBAROutput> filterOutDecisionsByPaceData(List<CPDecisionBAROutput> storedDecisions, List<CPPaceDecisionBAROutput> paceDecisions) {
        List<CPDecisionBAROutput> finalDecisions = new ArrayList<>();
        for (CPDecisionBAROutput decision : storedDecisions) {
            CPPaceDecisionBAROutput paceOutput = getPaceDecisionBarOutputFor(decision, paceDecisions);
            //Only return decisions if their final bar value is different than the pace final bar value
            if (paceOutput != null && ObjectUtils.compare(decision.getFinalBAR(), paceOutput.getFinalBAR()) != 0) {
                finalDecisions.add(decision);
            }
        }
        return finalDecisions;
    }

    private CPPaceDecisionBAROutput getPaceDecisionBarOutputFor(CPDecisionBAROutput decision, List<CPPaceDecisionBAROutput> paceDecisions) {
        return paceDecisions.stream()
                .filter(item -> item.getProduct().equals(decision.getProduct()))
                .filter(output -> output.getAccomType().getId().equals(decision.getAccomType().getId())
                        && output.getArrivalDate().equals(decision.getArrivalDate()))
                .findFirst()
                .orElse(null);
    }

    Date fixTimeZone(LocalDateTime timestamp) {
        Timestamp date = Timestamp.valueOf(timestamp);
        // Client timestamp comes with client-local time but server-local time zone => fail
        // We must count the correct time with time zone offsets
        int clientTimeZoneOffset = getClientTimeZoneOffset();
        int serverTimeZoneOffset = getServerTimeZoneOffset();
        int difference = Math.abs(clientTimeZoneOffset - serverTimeZoneOffset);
        long timestampMilliSeconds = date.getTime();
        if (clientTimeZoneOffset > serverTimeZoneOffset) {
            timestampMilliSeconds -= difference;
        } else if (clientTimeZoneOffset < serverTimeZoneOffset) {
            timestampMilliSeconds += difference;
        }
        return new Timestamp(timestampMilliSeconds);
    }

    public int getClientTimeZoneOffset() {
        return Page.getCurrent().getWebBrowser().getTimezoneOffset();
    }

    int getServerTimeZoneOffset() {
        return TimeZone.getDefault().getOffset(System.currentTimeMillis());
    }

    private boolean isLraEnabled() {
        return configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.LRAENABLED);
    }

    private BusinessAnalysisDailyIndicatorDto getDailyIndicatorDTO(LocalDate date) {
        return businessAnalysisDailyIndicatorDtos.stream()
                .filter(businessAnalysisDailyIndicatorDto -> isDatesEqual(date, businessAnalysisDailyIndicatorDto.getDate()))
                .findFirst().orElse(null);
    }

    public CPPricingFilter getCpPricingFilter() {
        return cpPricingFilter;
    }

    public void setCpPricingFilter(CPPricingFilter cpPricingFilter) {
        this.cpPricingFilter = cpPricingFilter;
    }

    public boolean isSpecificRoomClassSelected() {
        return !isAllRoomClassSelected();
    }

    private boolean isAllRoomClassSelected() {
        return getCpPricingFilter().getSelectedRoomClass().getId().equals(-1);
    }

    private BusinessAnalysisDailyDataDto getDailyDataDTO(LocalDate date) {
        return businessAnalysisDailyDataDtos.stream()
                .filter(businessAnalysisDailyDataDto -> isDatesEqual(date, businessAnalysisDailyDataDto.getDate()))
                .findFirst()
                .orElse(null);
    }

    private boolean isDatesEqual(LocalDate date, DateParameter dtoDate) {
        return DateParameter.fromDate(date.toDate()).equals(dtoDate);
    }

    @ForTesting
    List<CPDecisionBAROutput> getUpdatedDecisions(boolean dateChange) {
        updateDecisions(dateChange);
        return getDecisions();
    }

    Map<LocalDate, List<CPDecisionBARNOVRDetails>> getNOVRDecisionsMap(boolean dateChange) {
        if (dateChange) {
            storedDecisionsNovr = getCPDecisionsNOVRBetweenDatesForProductAndSelectedRoomTypes();
        }
        List<CPDecisionBARNOVRDetails> decisionsNovr = new ArrayList<>();
        if (!isSpecificRoomClassSelected()) {
            decisionsNovr.addAll(getDecisionsForAllClassesNovr());
        } else if (isShowOnlyBaseRoomTypesFlag()) {
            decisionsNovr.addAll(getDecisionsWhenShowOnlyBaseRoomTypeFlagIsSetNovr());
        } else {
            if (getCpPricingFilter().getSelectedRoomTypes() == null || getCpPricingFilter().getSelectedRoomTypes().isEmpty()) {
                decisionsNovr.addAll(getDecisionsForSelectedRoomClassNovr());
            } else {
                decisionsNovr.addAll(getDecisionsForSelectedRoomTypeNovr());
            }
        }

        Map<LocalDate, List<CPDecisionBARNOVRDetails>> dateMap = new HashMap<>();
        for (CPDecisionBARNOVRDetails decision : decisionsNovr) {
            if (dateMap.containsKey(decision.getArrivalDate())) {
                dateMap.get(decision.getArrivalDate()).add(decision);
            } else {
                dateMap.put(decision.getArrivalDate(), new ArrayList<>(List.of(decision)));
            }
        }
        return dateMap;
    }

    private List<CPDecisionBARNOVRDetails> getDecisionsForSelectedRoomTypeNovr() {
        return storedDecisionsNovr.stream()
                .filter(cpDecisionBAROutputNovr -> getCpPricingFilter().getSelectedRoomTypes().contains(cpDecisionBAROutputNovr.getAccomType()) &&
                        getCpPricingFilter().getProducts().contains(cpDecisionBAROutputNovr.getProduct()))
                .collect(Collectors.toList());
    }

    private List<CPDecisionBARNOVRDetails> getDecisionsForSelectedRoomClassNovr() {
        return storedDecisionsNovr.stream()
                .filter(cpDecisionBAROutputNovr -> cpDecisionBAROutputNovr.getAccomType().getAccomClass().equals(getCpPricingFilter().getSelectedRoomClass()) &&
                        getCpPricingFilter().getProducts().contains(cpDecisionBAROutputNovr.getProduct()))
                .collect(Collectors.toList());
    }

    private List<CPDecisionBARNOVRDetails> getDecisionsWhenShowOnlyBaseRoomTypeFlagIsSetNovr() {
        return storedDecisionsNovr.stream()
                .filter(cpDecisionBAROutputNovr -> getBaseRoomTypeList().contains(cpDecisionBAROutputNovr.getAccomType()) &&
                        cpDecisionBAROutputNovr.getAccomType().getAccomClass().equals(getCpPricingFilter().getSelectedRoomClass()) &&
                        getCpPricingFilter().getProducts().contains(cpDecisionBAROutputNovr.getProduct()))
                .collect(Collectors.toList());
    }

    private List<CPDecisionBARNOVRDetails> getDecisionsForAllClassesNovr() {
        return storedDecisionsNovr.stream()
                .filter(cpDecisionBAROutputNovr -> getBaseRoomTypeList().contains(cpDecisionBAROutputNovr.getAccomType()) &&
                        getCpPricingFilter().getProducts().contains(cpDecisionBAROutputNovr.getProduct()))
                .collect(Collectors.toList());
    }


    private List<CPDecisionBAROutput> getDecisions() {
        if (!isSpecificRoomClassSelected()) {
            return getDecisionsForAllClasses();
        } else if (isShowOnlyBaseRoomTypesFlag()) {
            return getDecisionsWhenShowOnlyBaseRoomTypeFlagIsSet();
        } else {
            if (getCpPricingFilter().getSelectedRoomTypes() == null || getCpPricingFilter().getSelectedRoomTypes().isEmpty()) {
                return getDecisionsForSelectedRoomClass();
            } else {
                return getDecisionsForSelectedRoomType();
            }
        }
    }

    private List<CPDecisionBAROutput> getDecisionsForSelectedRoomType() {
        return storedDecisions.stream()
                .filter(cpDecisionBAROutput -> getCpPricingFilter().getSelectedRoomTypes().contains(cpDecisionBAROutput.getAccomType()) &&
                        getCpPricingFilter().getProducts().contains(cpDecisionBAROutput.getProduct()))
                .collect(Collectors.toList());
    }

    private List<CPDecisionBAROutput> getDecisionsForSelectedRoomClass() {
        return storedDecisions.stream()
                .filter(cpDecisionBAROutput -> cpDecisionBAROutput.getAccomType().getAccomClass().equals(getCpPricingFilter().getSelectedRoomClass()) &&
                        getCpPricingFilter().getProducts().contains(cpDecisionBAROutput.getProduct()))
                .collect(Collectors.toList());
    }

    private List<CPDecisionBAROutput> getDecisionsWhenShowOnlyBaseRoomTypeFlagIsSet() {
        return storedDecisions.stream()
                .filter(cpDecisionBAROutput -> getBaseRoomTypeList().contains(cpDecisionBAROutput.getAccomType()) &&
                        cpDecisionBAROutput.getAccomType().getAccomClass().equals(getCpPricingFilter().getSelectedRoomClass()) &&
                        getCpPricingFilter().getProducts().contains(cpDecisionBAROutput.getProduct()))
                .collect(Collectors.toList());
    }

    private List<CPDecisionBAROutput> getDecisionsForAllClasses() {
        return storedDecisions.stream()
                .filter(cpDecisionBAROutput -> getBaseRoomTypeList().contains(cpDecisionBAROutput.getAccomType()) &&
                        getCpPricingFilter().getProducts().contains(cpDecisionBAROutput.getProduct()))
                .collect(Collectors.toList());
    }

    private void updateDecisions(boolean dateChange) {
        if (dateChange) {
            storedDecisions = getCPDecisionsBetweenDatesForProductAndSelectedRoomTypes();
            allProductRateOffsetOverrides = findAllProductRateOffsetOverrides();
            if (isAgileRatesEnabled()) {
                allProductRateOffsetOverridesWithProductGroups = findAllProductRateOffsetOverridesWithProductGroups();
            }
        }

        if (getCpPricingFilter().getSelectedRoomClass() != null) {
            pseudoShowOnlyBaseRoomTypesFlag = isAllRoomClassSelected();
        }
    }

    private List<CPDecisionBAROutput> getCPDecisionsBetweenDatesForProductAndSelectedRoomTypes() {
        List<AccomType> selectedAccomTypes = getSelectedAccomTypes();
        if (!selectedAccomTypes.isEmpty()) {
            return service.getCPDecisionsBetweenDatesForProductAndRoomTypes(getCpPricingFilter().getProducts(),
                    getCpPricingFilter().getStartDate(), getCpPricingFilter().getEndDate(), selectedAccomTypes);
        }
        return new ArrayList<>();
    }

    private List<CPDecisionBARNOVRDetails> getCPDecisionsNOVRBetweenDatesForProductAndSelectedRoomTypes() {
        List<AccomType> selectedAccomTypes = getSelectedAccomTypes();
        if (!selectedAccomTypes.isEmpty()) {
            return service.getCPDecisionsNOVRBetweenDatesForProductAndRoomTypes(getCpPricingFilter().getProducts(),
                    getCpPricingFilter().getStartDate(), getCpPricingFilter().getEndDate(), selectedAccomTypes);
        }
        return new ArrayList<>();
    }

    private Set<ProductRateOffsetOverride> findAllProductRateOffsetOverrides() {
        return new HashSet<>(service.getProductRateOffsetOverrides(getCpPricingFilter().getProducts(),
                getCpPricingFilter().getStartDate(), getCpPricingFilter().getEndDate()));
    }

    private Set<ProductRateOffsetOverride> findAllProductRateOffsetOverridesWithProductGroups() {
        List<ProductGroup> allProductGroups = agileRatesConfigurationService.getProductGroups();
        Set<Product> products = new HashSet<>(getCpPricingFilter().getProducts());
        getCpPricingFilter().getProducts().forEach(product -> products.addAll(agileRatesConfigurationService.getAllProductsInProductGroup(product, allProductGroups)
                .stream().map(ProductGroup::getProduct).collect(Collectors.toList())));
        return new HashSet<>(service.getProductRateOffsetOverrides(products, cpPricingFilter.getStartDate(), cpPricingFilter.getEndDate()));
    }

    private Set<ProductRateOffsetOverride> findActiveProductRateOffsetOverrides() {
        return allProductRateOffsetOverrides.stream().filter(override -> override.getStatusId() == 1).collect(Collectors.toSet());
    }

    @ForTesting
    public void setAllProductRateOffsetOverrides(Set<ProductRateOffsetOverride> overrides) {
        allProductRateOffsetOverrides = overrides;
    }

    public List<AccomType> getBaseRoomTypeList() {
        return new ArrayList<>(baseRoomTypeList);
    }

    public void setBaseRoomTypeList(List<AccomType> baseRoomTypeList) {
        this.baseRoomTypeList = baseRoomTypeList;
    }

    public PricingToggleView getSelectedView() {
        return selectedView;
    }

    public void setSelectedView(PricingToggleView selectedView) {
        this.selectedView = selectedView;
    }

    public boolean isShowOnlyBaseRoomTypesFlag() {
        return showOnlyBaseRoomTypesFlag;
    }

    public void setShowOnlyBaseRoomTypesFlag(boolean showOnlyBaseRoomTypesFlag) {
        this.showOnlyBaseRoomTypesFlag = showOnlyBaseRoomTypesFlag;
    }

    public BigDecimal getPreviousBar(CPOverrideWrapper overrideWrapper) {
        if (getCpPricingFilter().getDateWithFilterEnum().equals(DateWithFilterEnum.CHANGES_SINCE) ||
                getCpPricingFilter().getDateWithFilterEnum().equals(DateWithFilterEnum.CHANGES_SINCE_LAST_BDE) ||
                getCpPricingFilter().getDateWithFilterEnum().equals(DateWithFilterEnum.CHANGES_SINCE_LAST_IDP)) {
            CPPaceDecisionBAROutput paceDecisionBAROutput = getPaceDecisions().stream()
                    .filter(output -> output.getProduct().equals(overrideWrapper.getProduct()))
                    .filter(output -> output.getArrivalDate().equals(overrideWrapper.getCpDecisionBAROutput().getArrivalDate()) && output.getAccomType().getId().equals(overrideWrapper.getAccomType().getId()))
                    .findFirst()
                    .orElse(null);
            return paceDecisionBAROutput != null ? paceDecisionBAROutput.getFinalBAR() : null;
        }
        return overrideWrapper.getPreviousBAR();
    }

    public List<CPPaceDecisionBAROutput> getPaceDecisions() {
        return new ArrayList<>(paceDecisions);
    }

    void setPaceDecisions(List<CPPaceDecisionBAROutput> paceDecisions) {
        this.paceDecisions = paceDecisions;
    }

    void setBusinessAnalysisDailyDataDtos(List<BusinessAnalysisDailyDataDto> businessAnalysisDailyDataDtos) {
        this.businessAnalysisDailyDataDtos = businessAnalysisDailyDataDtos;
    }

    public boolean isPseudoShowOnlyBaseRoomTypesFlag() {
        return pseudoShowOnlyBaseRoomTypesFlag;
    }

    public void setPseudoShowOnlyBaseRoomTypesFlag(boolean pseudoShowOnlyBaseRoomTypesFlag) {
        this.pseudoShowOnlyBaseRoomTypesFlag = pseudoShowOnlyBaseRoomTypesFlag;
    }

    public Map<LocalDate, CPRoomClassDTO> getRoomClassDetailsMap() {
        return roomClassDetailsMap;
    }

    public void setRoomClassDetailsMap(Map<LocalDate, CPRoomClassDTO> roomClassDetailsMap) {
        this.roomClassDetailsMap = roomClassDetailsMap;
    }

    public Set<ProductRateOffsetOverride> getActiveProductRateOffsetOverrides() {
        return findActiveProductRateOffsetOverrides();
    }

    public Set<ProductRateOffsetOverride> getActiveProductRateOffsetOverridesWithProductGroups() {
        return allProductRateOffsetOverridesWithProductGroups.stream().filter(override -> override.getStatusId() == 1).collect(Collectors.toSet());
    }

    public Set<ProductRateOffsetOverride> getAllProductRateOffsetOverrides() {
        if (allProductRateOffsetOverrides == null) {
            allProductRateOffsetOverrides = findAllProductRateOffsetOverrides();
        }
        return allProductRateOffsetOverrides;
    }

    public boolean showInvestigator() {
        return isPropertyIsOneWayOrAbove();
    }

    private boolean isPropertyIsOneWayOrAbove() {
        return propertyService.isStageAtLeast(Stage.ONE_WAY, uiContext.getPropertyId());
    }

    public List<PricingAccomClass> getPricingAccomClasses() {
        return pricingConfigurationService.getPricingAccomClasses();
    }

    public Map<CPDecisionBAROutput, CPOverrideWrapper> getPendingBAROverrides() {
        return overridesMap;
    }

    public void setResults(List<CPBARDecisionUIWrapper> results) {
        this.results = results;
    }

    public void revertChange(CPOverrideWrapper overrideWrapper) {
        revertOverrideWrapper(overrideWrapper);
        overridesMap.remove(overrideWrapper.getCpDecisionBAROutput());
        productOverrides.remove(overrideWrapper);
        inventoryLimitOverrides.remove(overrideWrapper);

        List<CPOverrideWrapper> cpOverrideWrappers = new ArrayList<>();
        if ((isShowOnlyBaseRoomTypesFlag() || isPseudoShowOnlyBaseRoomTypesFlag()) || overrideWrapper.isApplyOverrideAcrossRoomTypes()) {
            cpOverrideWrappers = getNonBaseRoomTypes(overrideWrapper, service.getMasterPricingAccomClass(), uiContext.getSystemCaughtUpDate());
        } else if (overrideWrapper.getProduct().isSystemDefaultOrIndependentProduct() && overrideWrapper.isBaseRoomType()) {
            // Get the CPOverrideWrappers from the parent
            cpOverrideWrappers = getCPOverrideWrappersNotForAccomTypeWithoutSpecificOverrides(overrideWrapper, service.getMasterPricingAccomClass());
        }
        for (CPOverrideWrapper wrapper : cpOverrideWrappers) {
            revertOverrideWrapper(wrapper);
            overridesMap.remove(wrapper.getCpDecisionBAROutput(), wrapper);
            productOverrides.remove(wrapper);
            inventoryLimitOverrides.remove(overrideWrapper);
        }
    }

    public void revertOverrideWrapper(CPOverrideWrapper overrideWrapper) {
        overrideWrapper.setIsPendingSave(false);
        overrideWrapper.setIsPendingDelete(false);
        overrideWrapper.setIsPendingSaveNonBaseRoomTypes(false);
        overrideWrapper.setFloorOverridePendingDelete(false);
        overrideWrapper.setCeilingOverridePendingDelete(false);
        overrideWrapper.setSpecificOverride(overrideWrapper.getOriginalSpecificOverride());
        overrideWrapper.setCeilingOverride(overrideWrapper.getOriginalCeilingOverride());
        overrideWrapper.setFloorOverride(overrideWrapper.getOriginalFloorOverride());
        overrideWrapper.setGroupFloorOverride(overrideWrapper.getOriginalGroupFloorOverride());
        overrideWrapper.getCpDecisionBAROutput().setOverrideType(overrideWrapper.getOriginalOverrideType());
        overrideWrapper.getCpDecisionBAROutput().setSpecificOverride(overrideWrapper.getSpecificOverride());
        overrideWrapper.getCpDecisionBAROutput().setCeilingOverride(overrideWrapper.getCeilingOverride());
        if (DecisionOverrideType.GPFLOOR.equals(overrideWrapper.getOriginalOverrideType()) || DecisionOverrideType.GPFLOORANDCEIL.equals(overrideWrapper.getOriginalOverrideType())) {
            overrideWrapper.getCpDecisionBAROutput().setFloorOverride(overrideWrapper.getGroupFloorOverride());
        } else {
            overrideWrapper.getCpDecisionBAROutput().setFloorOverride(overrideWrapper.getFloorOverride());
        }
        overrideWrapper.setProductRateOffsetOverrides(overrideWrapper.getOriginalProductRateOffsetOverrides());
        overrideWrapper.setRoundedBAR(overrideWrapper.getOriginalRoundedBAR());
        overridesMap.remove(overrideWrapper.getCpDecisionBAROutput());
    }

    @ForTesting
    public List<CPOverrideWrapper> getProductOverrides() {
        return productOverrides;
    }

    public void setProductOverrides(List<CPOverrideWrapper> productOverrides) {
        this.productOverrides = productOverrides;
    }

    public void saveOverrideChanges(PricingAccomClass masterPricingAccomClass) {
        boolean useMultiProductProductPerformanceChanges = SystemConfig.usePerformanceImprovementChangesPricingMultiProductDisplayChanges();
        boolean useBulkSaveMultiProductPerformanceChanges = SystemConfig.usePerformanceImprovementPricingMultiProductBulkSaveChanges();
        Date caughtUpDate = uiContext.getSystemCaughtUpDate();
        Date businessDate = dateService.getBusinessDate();
        Date unqualifiedRateCaughtUpDate = dateService.getUnqualifiedRateCaughtUpDate();
        Date webrateShoppingDate = dateService.getWebRateShoppingDate();
        List<CPOverrideWrapper> groupFloorRemoveList = overridesMap.values().stream()
                .filter(wrapper -> (wrapper.getOriginalOverrideType().equals(DecisionOverrideType.GPFLOOR) && wrapper.isPendingDelete()) ||
                        (wrapper.getOriginalOverrideType().equals(DecisionOverrideType.GPFLOORANDCEIL) && wrapper.getCpDecisionBAROutput().getOverrideType().equals(DecisionOverrideType.CEIL)))
                .collect(Collectors.toList());
        overridesMap.keySet().removeAll(
                groupFloorRemoveList.stream()
                        .map(CPOverrideWrapper::getCpDecisionBAROutput)
                        .collect(Collectors.toList())
        );
        groupFloorRemoveList.forEach(this::revertOverrideWrapper);

        List<CPOverrideWrapper> groupFloorAndCeilRemoveList = overridesMap.values().stream()
                .filter(wrapper -> wrapper.getOriginalOverrideType().equals(DecisionOverrideType.GPFLOORANDCEIL) && wrapper.isPendingDelete())
                .collect(Collectors.toList());
        groupFloorAndCeilRemoveList.forEach(wrapper -> {
            overridesMap.remove(wrapper.getCpDecisionBAROutput());
            revertOverrideWrapper(wrapper);
            wrapper.setCeilingOverride(null);
            wrapper.setStartDate(wrapper.getCpDecisionBAROutput().getArrivalDate());
            wrapper.setEndDate(wrapper.getCpDecisionBAROutput().getArrivalDate());
            applyOverride(wrapper, masterPricingAccomClass);
            overridesMap.put(wrapper.getCpDecisionBAROutput(), wrapper);
        });

        //Collect all non base room type decisions which will be changed to accommodate any
        //base room type ceiling or floor overrides
        List<CPDecisionBAROutput> nonBaseRoomTypeDecisions = null;

        if (isShowOnlyBaseRoomTypesFlag() || isPseudoShowOnlyBaseRoomTypesFlag()) {
            nonBaseRoomTypeDecisions = overridesMap.values().stream()
                    .filter(CPOverrideWrapper::isPendingSaveNonBaseRoomTypes)
                    .map(CPOverrideWrapper::getNonBaseRoomTypeOverrideWrappers)
                    .filter(Objects::nonNull)
                    .flatMap(Collection::stream)
                    .map(CPOverrideWrapper::getCpDecisionBAROutput)
                    .collect(Collectors.toList());
        } else {
            nonBaseRoomTypeDecisions = overridesMap.values().stream()
                    .filter(CPOverrideWrapper::isPendingSaveNonBaseRoomTypes)
                    .map(wrapper -> getCPOverrideWrappersNotForAccomTypeWithoutSpecificOverrides(wrapper, masterPricingAccomClass))
                    .flatMap(Collection::stream)
                    .map(CPOverrideWrapper::getCpDecisionBAROutput)
                    .collect(Collectors.toList());
        }

        // Create a new decisionId if we are need to remove an override
        Integer decisionId = overridesMap.values().stream().anyMatch(CPOverrideWrapper::isPendingDelete) ? decisionService.createBAROverrideDecision().getId() : null;

        //Save or delete BAR overrides
        if (!overridesMap.isEmpty()) {
            LocalDate startDate = overridesMap.keySet().stream().min(Comparator.comparing(CPDecisionBAROutput::getArrivalDate)).get().getArrivalDate();
            LocalDate endDate = overridesMap.keySet().stream().max(Comparator.comparing(CPDecisionBAROutput::getArrivalDate)).get().getArrivalDate();
            CPDecisionContext cpDecisionContext = pricingConfigurationService.getCPDecisionContext(startDate, endDate, false);
            List<CPDecisionBAROutput> finalNonBaseRoomTypeDecisions = nonBaseRoomTypeDecisions;

            if (useMultiProductProductPerformanceChanges && useBulkSaveMultiProductPerformanceChanges) {
                List<CPDecisionBAROutput> decisionsToBeDeleted = overridesMap
                        .keySet()
                        .stream()
                        .filter(dto -> dto.getProduct().isSystemDefaultOrIndependentProduct())
                        .filter(cpDecisionBAROutput -> overridesMap.get(cpDecisionBAROutput).isPendingDelete())
                        .collect(Collectors.toList());

                if (CollectionUtils.isNotEmpty(decisionsToBeDeleted)) {
                    service.removeOverrides(cpDecisionContext, decisionsToBeDeleted, decisionId, uiContext.getUserPrincipalId(),
                            finalNonBaseRoomTypeDecisions, true);
                }

                List<CPDecisionBAROutput> decisionsToBeSaved = overridesMap
                        .keySet()
                        .stream()
                        .filter(dto -> dto.getProduct().isSystemDefaultOrIndependentProduct())
                        .filter(cpDecisionBAROutput -> !overridesMap.get(cpDecisionBAROutput).isPendingDelete())
                        .collect(Collectors.toList());

                if (CollectionUtils.isNotEmpty(decisionsToBeSaved)) {
                    saveOverrides(caughtUpDate, businessDate, unqualifiedRateCaughtUpDate, webrateShoppingDate,
                            cpDecisionContext, decisionsToBeSaved, overridesMap);
                }
            } else {
                overridesMap.forEach((CPDecisionBAROutput cpDecisionBAROutput, CPOverrideWrapper overrideWrapper) -> {
                    if (overrideWrapper.getProduct().isSystemDefaultOrIndependentProduct()) {
                        if (overrideWrapper.isPendingDelete()) {
                            service.removeOverride(cpDecisionContext,
                                    cpDecisionBAROutput, decisionId, uiContext.getUserPrincipalId(), false,
                                    !finalNonBaseRoomTypeDecisions.contains(cpDecisionBAROutput), true);
                        } else {
                            saveOverrides(useMultiProductProductPerformanceChanges, caughtUpDate, businessDate,
                                    unqualifiedRateCaughtUpDate, webrateShoppingDate,
                                    cpDecisionContext,
                                    cpDecisionBAROutput, overrideWrapper);
                        }
                    }
                });
            }
        }

        //Saving or deleting all override for agile rates products applied to grid
        if (!productOverrides.isEmpty()) {
            saveOrDeleteProductOverrides();
        }
        if(!inventoryLimitOverrides.isEmpty()){
            saveOrDeleteInventoryLimitOverrides();
        }

        // If there is a pending change that includes modifications on non-base room types
        // save the updated rounded bar values
        service.save(nonBaseRoomTypeDecisions);

        //clear our pending overrides map, since now they are no longer considered pending
        clearPendingOverrides();
    }

    public List<BusinessAnalysisDailyDataDto> getBusinessAnalysisDailyDataDtos(Date caughtUpDate, Date startDate, Date endDate) {
        return service.getBusinessAnalysisDailyDataDtos(service.getMasterPricingAccomClass(),caughtUpDate, startDate, endDate);
    }
    protected void saveOrDeleteInventoryLimitOverrides() {
        List<DecisionGPInventoryLimitOverride> decisionGPInventoryLimitOverridesRemoval = new ArrayList<>();
        inventoryLimitOverrides.stream()
            .filter(CPOverrideWrapper::isInventoryLimitMarkedForDeletion)
            .filter(CPOverrideWrapper::isSmallGroupProduct)
            .forEach(cpOverrideWrapper -> {
                DecisionGPInventoryLimitOverride decisionGPInventoryLimitOverride = inventoryLimitDecisionService.getExistingActiveDecisionInventoryLimitOverride(cpOverrideWrapper.getInventoryLimitOverrideOccupancyDt());
                if(null != decisionGPInventoryLimitOverride){
                    decisionGPInventoryLimitOverridesRemoval.add(decisionGPInventoryLimitOverride);
                }
            });
        List<DecisionGPInventoryLimitOverride> decisionGPInventoryLimitOverridesAddition = new ArrayList<>();
        if(isInsertOrEditInventoryLimitOverrideOperation()) {
            Decision decision = decisionService.createInventoryLimitOverrideDecision();
            inventoryLimitOverrides.stream()
                .filter(cpOverrideWrapper -> !cpOverrideWrapper.isInventoryLimitMarkedForDeletion())
                .filter(CPOverrideWrapper::isSmallGroupProduct)
                .forEach(cpOverrideWrapper -> {
                    populateDecisionInventoryLimitOverrideLists(cpOverrideWrapper, decisionGPInventoryLimitOverridesAddition, decisionGPInventoryLimitOverridesRemoval, decision.getId());
                });
        }
        if(!decisionGPInventoryLimitOverridesRemoval.isEmpty()) {
            decisionGPInventoryLimitOverridesRemoval.forEach(decisionGPInventoryLimitOverride -> decisionGPInventoryLimitOverride.setStatus(TenantStatusEnum.DELETED));
            inventoryLimitDecisionService.saveOrUpdateDecisionInventoryLimitOverrides(decisionGPInventoryLimitOverridesRemoval);
        }
        if(!decisionGPInventoryLimitOverridesAddition.isEmpty()) {
            inventoryLimitDecisionService.saveOrUpdateDecisionInventoryLimitOverrides(decisionGPInventoryLimitOverridesAddition);
        }
        if(!decisionGPInventoryLimitOverridesRemoval.isEmpty() || !decisionGPInventoryLimitOverridesAddition.isEmpty()) {
            inventoryLimitDecisionService.registerGroupProductChangedEvent();
        }
    }

    private boolean isInsertOrEditInventoryLimitOverrideOperation() {
        return inventoryLimitOverrides.stream()
                .filter(CPOverrideWrapper::isSmallGroupProduct).anyMatch(cpOverrideWrapper -> !cpOverrideWrapper.isInventoryLimitMarkedForDeletion());
    }

    protected void populateDecisionInventoryLimitOverrideLists(CPOverrideWrapper cpOverrideWrapper, List<DecisionGPInventoryLimitOverride> decisionGPInventoryLimitOverridesAddition, List<DecisionGPInventoryLimitOverride> decisionGPInventoryLimitOverridesRemoval, Integer decisionId) {
        if(cpOverrideWrapper.getOriginalInventoryLimitOverride() == null) {
            decisionGPInventoryLimitOverridesAddition.add(inventoryLimitDecisionService.createDecisionInventoryLimitOverride(cpOverrideWrapper.getInventoryLimitOverrideOccupancyDt(), cpOverrideWrapper.getInventoryLimit(), cpOverrideWrapper.getInventoryLimitOverride(), decisionId));
        } else {
            DecisionGPInventoryLimitOverride decisionGPInventoryLimitOverride = inventoryLimitDecisionService.getExistingActiveDecisionInventoryLimitOverride(cpOverrideWrapper.getInventoryLimitOverrideOccupancyDt());
            if(null != decisionGPInventoryLimitOverride){
                decisionGPInventoryLimitOverridesRemoval.add(decisionGPInventoryLimitOverride);
            }
            decisionGPInventoryLimitOverridesAddition.add(inventoryLimitDecisionService.createDecisionInventoryLimitOverride(cpOverrideWrapper.getInventoryLimitOverrideOccupancyDt(), cpOverrideWrapper.getInventoryLimit(), cpOverrideWrapper.getInventoryLimitOverride(), decisionId));
        }
    }

    public List<DecisionGPInventoryLimitOverride> getInventoryForAllDates(Date startDate, Date endDate)
    {
        return inventoryLimitDecisionService.getAllInventoryOverridesForDateRange(startDate,endDate);
    }

    public void updateInventoryLimitDecisions(Integer decisionId,Integer inventoryLimit, Date occupancyDate)
    {
        inventoryLimitDecisionService.updateDecisonLimitOverride(decisionId,inventoryLimit,occupancyDate);
    }

    public List<InventoryLimitDecision> getOverriddenInventoryLimits(Date startDate, Date endDate) {
        return inventoryLimitDecisionService.getInventoryLimitDecisionsFromOverride(startDate, endDate);
    }
    public List<InventoryLimitDecision> getInventoryLimitDecisions(Date startDate, Date endDate) {
        return inventoryLimitDecisionService.getInventoryLimitDecisionsFromNonPace(startDate, endDate);
    }
    public void setInventoryLimitOverrides(List<CPOverrideWrapper> inventoryLimitOverrides) {
        this.inventoryLimitOverrides = inventoryLimitOverrides;
    }
    @ForTesting
    public void setInventoryLimitDecisionService(InventoryLimitDecisionService inventoryLimitDecisionService) {
        this.inventoryLimitDecisionService = inventoryLimitDecisionService;
    }
    protected void saveOrDeleteProductOverrides() {
        List<ProductRateOffsetOverride> productRateOffsetOverridesToDelete = new ArrayList<>();
        List<ProductRateOffsetOverride> productRateOffsetOverridesToSave = new ArrayList<>();
        List<ProductRateOffsetOverride> smallGroupProductRateOffsetOverridesToDelete = new ArrayList<>();
        List<ProductRateOffsetOverride> smallGroupProductRateOffsetOverridesToSave = new ArrayList<>();

        productOverrides.stream()
                .filter(CPOverrideWrapper::isPendingSave)
                .forEach(wrapper -> {
                    if (wrapper.isSmallGroupProduct()) {
                        smallGroupProductRateOffsetOverridesToDelete.addAll(wrapper.getOriginalProductRateOffsetOverrides());
                        smallGroupProductRateOffsetOverridesToSave.addAll(wrapper.getProductRateOffsetOverrides());
                    } else {
                        productRateOffsetOverridesToDelete.addAll(wrapper.getOriginalProductRateOffsetOverrides());
                        productRateOffsetOverridesToSave.addAll(wrapper.getProductRateOffsetOverrides());
                    }
                });

        productOverrides.stream()
                .filter(CPOverrideWrapper::isPendingDelete)
                .forEach(wrapper -> {
                    if (wrapper.isSmallGroupProduct()) {
                        smallGroupProductRateOffsetOverridesToDelete.addAll(wrapper.getOriginalProductRateOffsetOverrides());
                    } else if (((isOptimizedAgileRateProduct(wrapper.getCpDecisionBAROutput().getProduct())) && !wrapper.isSmallGroupProduct())
                            || isNonOptimizedAndApplyRemoveForAllRoomClasses(wrapper.getCpDecisionBAROutput().getProduct())) {
                        productRateOffsetOverridesToDelete.addAll(wrapper.getOriginalProductRateOffsetOverrides());
                    } else {
                        productRateOffsetOverridesToDelete.addAll(wrapper.getProductRateOffsetOverrides());
                    }
                });

        //Agile Rates Products
        if (CollectionUtils.isNotEmpty(productRateOffsetOverridesToDelete)) {
            service.deleteProductRateOffsetOverride(productRateOffsetOverridesToDelete);
        }
        if (CollectionUtils.isNotEmpty(productRateOffsetOverridesToSave)) {
            service.saveProductRateOffsetOverride(productRateOffsetOverridesToSave);
        }

        //Small Group Products
        if (CollectionUtils.isNotEmpty(smallGroupProductRateOffsetOverridesToDelete)) {
            service.deleteSmallGroupProductRateOffsetOverride(smallGroupProductRateOffsetOverridesToDelete);
        }
        if (CollectionUtils.isNotEmpty(smallGroupProductRateOffsetOverridesToSave)) {
            service.saveSmallGroupProductRateOffsetOverride(smallGroupProductRateOffsetOverridesToSave);
        }
    }

    protected void saveOverrides(boolean useMultiProductProductPerformanceChanges, Date caughtUpDate, Date businessDate, Date unqualifiedRateCaughtUpDate, Date webrateShoppingDate, CPDecisionContext cpDecisionContext, CPDecisionBAROutput cpDecisionBAROutput, CPOverrideWrapper overrideWrapper) {
        CPDecisionBAROutputOverride override = createOverride(cpDecisionBAROutput, overrideWrapper, cpDecisionContext);
        if (useMultiProductProductPerformanceChanges) {
            service.saveOverrideForCPDecision(cpDecisionContext, override, cpDecisionBAROutput, false, caughtUpDate, businessDate, unqualifiedRateCaughtUpDate, webrateShoppingDate);
        } else {
            service.saveOverrideForCPDecision(cpDecisionContext, override, cpDecisionBAROutput, false);
        }
    }

    protected void saveOverrides(Date caughtUpDate, Date businessDate, Date unqualifiedRateCaughtUpDate,
                                 Date webrateShoppingDate, CPDecisionContext cpDecisionContext,
                                 List<CPDecisionBAROutput> cpDecisionBAROutputList, Map<CPDecisionBAROutput, CPOverrideWrapper> cpOverrideWrapperMap) {
        Map<CPDecisionBAROutput, CPDecisionBAROutputOverride> cpDecisionBAROutputOverrideMap = new HashMap<>();

        cpDecisionBAROutputList.stream().forEach(cpDecisionBAROutput -> {
            CPDecisionBAROutputOverride override = createOverride(cpDecisionBAROutput, cpOverrideWrapperMap.get(cpDecisionBAROutput), cpDecisionContext);
            cpDecisionBAROutputOverrideMap.put(cpDecisionBAROutput, override);
        });
        service.saveOverrideForCPDecisions(cpDecisionContext, cpDecisionBAROutputOverrideMap, caughtUpDate, businessDate, unqualifiedRateCaughtUpDate, webrateShoppingDate);
    }

    public boolean hasChangesFor(Predicate<CPOverrideWrapper> predicate) {
        if (MapUtils.isEmpty(overridesMap)) {
            return false;
        }
        return overridesMap.values().stream()
                .anyMatch(predicate);
    }

    protected boolean isOptimizedAgileRateProduct(Product product) {
        return product.isOptimized();
    }

    protected boolean isNonOptimizedAndApplyRemoveForAllRoomClasses(Product product) {
        return !product.isSystemDefaultOrIndependentProduct()
                && !product.isOptimized()
                && getCpPricingFilter().isApplyOverridesToAllRoomClasses();
    }

    protected boolean isAgileRatesEnabled() {
        return configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.AGILE_RATES_ENABLED);
    }

    protected CPDecisionBAROutputOverride createOverride(CPDecisionBAROutput decisionBARCPOutput, CPOverrideWrapper overrideWrapper, CPDecisionContext cpDecisionContext) {
        CPDecisionBAROutputOverride decisionBARCPOutputOverride = new CPDecisionBAROutputOverride();
        decisionBARCPOutputOverride.setPropertyId(uiContext.getPropertyId());
        decisionBARCPOutputOverride.setProduct(decisionBARCPOutput.getProduct());
        decisionBARCPOutputOverride.setUser(uiContext.getUserPrincipalId());
        decisionBARCPOutputOverride.setDecisionId(decisionBARCPOutput.getDecisionId());
        decisionBARCPOutputOverride.setLengthOfStay(decisionBARCPOutput.getLengthOfStay());

        DecisionOverrideType overrideType = getDecisionOverrideTypeFromOverrideWrapper(overrideWrapper);

        BigDecimal newSpecificSupplementValue;
        BigDecimal oldSpeicifcSupplementValue;
        BigDecimal oldCeilingSupplementValue;
        BigDecimal oldFloorSupplementValue;
        BigDecimal newCeilingSupplementValue;
        BigDecimal newFloorSupplementValue;
        BigDecimal oldGroupFloorSupplementValue;
        BigDecimal newGroupFloorSupplementValue;

        AccomTypeSupplementValue accomTypeSupplementValue = cpDecisionContext.getAccomTypeSupplementValue(decisionBARCPOutput);
        BigDecimal specificOverrideValue = overrideWrapper.getRoundedBAR();
        BigDecimal ceilingOverrideValue = (overrideWrapper.getCeilingOverride()) != null ? overrideWrapper.getCeilingOverride() : BigDecimal.ZERO;
        BigDecimal floorOverrideValue = overrideWrapper.getFloorOverride() != null ? overrideWrapper.getFloorOverride() : BigDecimal.ZERO;
        BigDecimal oldCeilingOverrideValue = overrideWrapper.getOriginalCeilingOverride() != null ? overrideWrapper.getOriginalCeilingOverride() : BigDecimal.ZERO;
        BigDecimal oldFloorOverrideValue = overrideWrapper.getOriginalFloorOverride() != null ? overrideWrapper.getOriginalFloorOverride() : BigDecimal.ZERO;
        BigDecimal groupFloorOverrideValue = overrideWrapper.getGroupFloorOverride() != null ? overrideWrapper.getGroupFloorOverride() : BigDecimal.ZERO;
        BigDecimal oldGroupFloorOverrideValue = overrideWrapper.getOriginalGroupFloorOverride() != null ? overrideWrapper.getOriginalGroupFloorOverride() : BigDecimal.ZERO;

        cpDecisionContext.setSpecificOverrideRate(overrideWrapper.getSpecificOverride());
        cpDecisionContext.setCeilingOverrideRate(overrideWrapper.getCeilingOverride());
        cpDecisionContext.setFloorOverrideRate(overrideWrapper.getFloorOverride());

        if (accomTypeSupplementValue != null && OffsetMethod.PERCENTAGE.equals(accomTypeSupplementValue.getOffsetMethod())) {
            if (SystemConfig.usePerformanceImprovementChangesPricingMultiProductDisplayChanges()) {
                oldSpeicifcSupplementValue = Supplement.calculateSupplementInPercentage(decisionBARCPOutput.getFinalBAR(), cpDecisionContext.getSupplement(decisionBARCPOutput));
                newSpecificSupplementValue = Supplement.calculateSupplementInPercentage(specificOverrideValue, cpDecisionContext.getSupplement(decisionBARCPOutput));
                oldCeilingSupplementValue = Supplement.calculateSupplementInPercentage(oldCeilingOverrideValue, cpDecisionContext.getSupplement(decisionBARCPOutput));
                oldFloorSupplementValue = Supplement.calculateSupplementInPercentage(oldFloorOverrideValue, cpDecisionContext.getSupplement(decisionBARCPOutput));
                newCeilingSupplementValue = Supplement.calculateSupplementInPercentage(ceilingOverrideValue, cpDecisionContext.getSupplement(decisionBARCPOutput));
                newFloorSupplementValue = Supplement.calculateSupplementInPercentage(floorOverrideValue, cpDecisionContext.getSupplement(decisionBARCPOutput));
                oldGroupFloorSupplementValue = Supplement.calculateSupplementInPercentage(oldGroupFloorOverrideValue, cpDecisionContext.getSupplement(decisionBARCPOutput));
                newGroupFloorSupplementValue = Supplement.calculateSupplementInPercentage(groupFloorOverrideValue, cpDecisionContext.getSupplement(decisionBARCPOutput));
            } else {
                BigDecimal supplement = accomTypeSupplementService.getSupplementValue(decisionBARCPOutput.getProduct().getId(), decisionBARCPOutput.getArrivalDate(), decisionBARCPOutput.getAccomType().getId());
                oldSpeicifcSupplementValue = Supplement.calculateSupplementInPercentage(decisionBARCPOutput.getFinalBAR(), supplement);
                newSpecificSupplementValue = Supplement.calculateSupplementInPercentage(specificOverrideValue, supplement);
                oldCeilingSupplementValue = Supplement.calculateSupplementInPercentage(oldCeilingOverrideValue, supplement);
                oldFloorSupplementValue = Supplement.calculateSupplementInPercentage(oldFloorOverrideValue, supplement);
                newCeilingSupplementValue = Supplement.calculateSupplementInPercentage(ceilingOverrideValue, supplement);
                newFloorSupplementValue = Supplement.calculateSupplementInPercentage(floorOverrideValue, supplement);
                oldGroupFloorSupplementValue = Supplement.calculateSupplementInPercentage(oldGroupFloorOverrideValue, supplement);
                newGroupFloorSupplementValue = Supplement.calculateSupplementInPercentage(groupFloorOverrideValue, supplement);
            }

        } else {
            if (SystemConfig.usePerformanceImprovementChangesPricingMultiProductDisplayChanges()) {
                oldSpeicifcSupplementValue = cpDecisionContext.getSupplement(decisionBARCPOutput);
                newSpecificSupplementValue = cpDecisionContext.getSupplement(decisionBARCPOutput);
                oldCeilingSupplementValue = cpDecisionContext.getSupplement(decisionBARCPOutput);
                oldFloorSupplementValue = cpDecisionContext.getSupplement(decisionBARCPOutput);
                newCeilingSupplementValue = cpDecisionContext.getSupplement(decisionBARCPOutput);
                newFloorSupplementValue = cpDecisionContext.getSupplement(decisionBARCPOutput);
                oldGroupFloorSupplementValue = cpDecisionContext.getSupplement(decisionBARCPOutput);
                newGroupFloorSupplementValue = cpDecisionContext.getSupplement(decisionBARCPOutput);
            } else {
                oldSpeicifcSupplementValue = accomTypeSupplementService.getSupplementValue(decisionBARCPOutput.getProduct().getId(), decisionBARCPOutput.getArrivalDate(), decisionBARCPOutput.getAccomType().getId());
                newSpecificSupplementValue = accomTypeSupplementService.getSupplementValue(decisionBARCPOutput.getProduct().getId(), decisionBARCPOutput.getArrivalDate(), decisionBARCPOutput.getAccomType().getId());
                oldCeilingSupplementValue = accomTypeSupplementService.getSupplementValue(decisionBARCPOutput.getProduct().getId(), decisionBARCPOutput.getArrivalDate(), decisionBARCPOutput.getAccomType().getId());
                oldFloorSupplementValue = accomTypeSupplementService.getSupplementValue(decisionBARCPOutput.getProduct().getId(), decisionBARCPOutput.getArrivalDate(), decisionBARCPOutput.getAccomType().getId());
                newCeilingSupplementValue = accomTypeSupplementService.getSupplementValue(decisionBARCPOutput.getProduct().getId(), decisionBARCPOutput.getArrivalDate(), decisionBARCPOutput.getAccomType().getId());
                newFloorSupplementValue = accomTypeSupplementService.getSupplementValue(decisionBARCPOutput.getProduct().getId(), decisionBARCPOutput.getArrivalDate(), decisionBARCPOutput.getAccomType().getId());
                oldGroupFloorSupplementValue = accomTypeSupplementService.getSupplementValue(decisionBARCPOutput.getProduct().getId(), decisionBARCPOutput.getArrivalDate(), decisionBARCPOutput.getAccomType().getId());
                newGroupFloorSupplementValue = accomTypeSupplementService.getSupplementValue(decisionBARCPOutput.getProduct().getId(), decisionBARCPOutput.getArrivalDate(), decisionBARCPOutput.getAccomType().getId());
            }
        }

        decisionBARCPOutputOverride.setOldOverrideType(overrideWrapper.getOriginalOverrideType());
        decisionBARCPOutputOverride.setOldUserOverride(BigDecimalUtil.subtractReturnsNull(decisionBARCPOutput.getFinalBAR(), oldSpeicifcSupplementValue));
        if (DecisionOverrideType.GPFLOOR.equals(overrideWrapper.getOriginalOverrideType()) || DecisionOverrideType.GPFLOORANDCEIL.equals(overrideWrapper.getOriginalOverrideType())) {
            decisionBARCPOutputOverride.setOldFloorRate(BigDecimalUtil.subtractReturnsNull(overrideWrapper.getOriginalGroupFloorOverride(), oldGroupFloorSupplementValue));
        } else {
            decisionBARCPOutputOverride.setOldFloorRate(BigDecimalUtil.subtractReturnsNull(overrideWrapper.getOriginalFloorOverride(), oldFloorSupplementValue));
        }
        decisionBARCPOutputOverride.setOldCeilingRate(BigDecimalUtil.subtractReturnsNull(overrideWrapper.getOriginalCeilingOverride(), oldCeilingSupplementValue));

        decisionBARCPOutputOverride.setNewUserOverride(BigDecimalUtil.subtractReturnsNull(overrideWrapper.getSpecificOverride(), newSpecificSupplementValue));
        if (DecisionOverrideType.GPFLOOR.equals(overrideType) || DecisionOverrideType.GPFLOORANDCEIL.equals(overrideType)) {
            decisionBARCPOutputOverride.setNewFloorRate(BigDecimalUtil.subtractReturnsNull(overrideWrapper.getGroupFloorOverride(), newGroupFloorSupplementValue));
        } else {
            decisionBARCPOutputOverride.setNewFloorRate(BigDecimalUtil.subtractReturnsNull(overrideWrapper.getFloorOverride(), newFloorSupplementValue));
        }
        decisionBARCPOutputOverride.setNewCeilingRate(BigDecimalUtil.subtractReturnsNull(overrideWrapper.getCeilingOverride(), newCeilingSupplementValue));
        decisionBARCPOutputOverride.setNewOverrideType(overrideType);

        decisionBARCPOutputOverride.setAccomType(decisionBARCPOutput.getAccomType());
        decisionBARCPOutputOverride.setArrivalDate(decisionBARCPOutput.getArrivalDate());
        return decisionBARCPOutputOverride;
    }

    public void clearPendingOverrides() {
        overridesMap.clear();
        productOverrides.clear();
        inventoryLimitOverrides.clear();
    }

    public List<CPBARDecisionUIWrapper> getCPBarDecisionUIWrappers() {
        return Optional.ofNullable(results).orElse(Collections.emptyList());
    }

    public Set<AccomType> getOrderedAndFilteredTypes() {
        CPPricingFilter filter = getCpPricingFilter();
        boolean baseRoomTypesFlag = isShowOnlyBaseRoomTypesFlag() || isPseudoShowOnlyBaseRoomTypesFlag();
        Set<AccomType> orderedAndFilteredRoomTypes;
        if (baseRoomTypesFlag) {
            orderedAndFilteredRoomTypes = getOrderedFilteredBaseRoomTypes(filter, getBaseRoomTypeList());
        } else {
            orderedAndFilteredRoomTypes = getOrderedAndFilteredAccomTypes(filter);
        }
        return orderedAndFilteredRoomTypes;
    }

    Set<AccomType> getOrderedFilteredBaseRoomTypes(CPPricingFilter filter, List<AccomType> baseRoomTypes) {
        if (CollectionUtils.isNotEmpty(filter.getSelectedRoomTypes())) {
            return filter.getSelectedRoomTypes();
        }
        return new HashSet<>(baseRoomTypes);
    }

    Set<AccomType> getOrderedAndFilteredAccomTypes(CPPricingFilter filter) {
        if (isRoomTypeRecodingUIEnabled()) {
            return filter.getSelectedRoomClass().getAccomTypes().stream()
                    .filter(accomType -> accomType.getDisplayStatusId().equals(Constants.ACTIVE_DISPLAY_STATUS_ID))
                    .collect(Collectors.toCollection(LinkedHashSet::new));
        }
        return getOrderedFilteredRoomTypes(filter);
    }

    private boolean isRoomTypeRecodingUIEnabled() {
        return configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.ROOM_TYPE_RECODING_UI_ENABLED);
    }

    Set<AccomType> getOrderedFilteredRoomTypes(CPPricingFilter filter) {
        Set<AccomType> filteredRoomTypes = getFilteredRoomTypes(filter);
        if (CollectionUtils.isNotEmpty(filteredRoomTypes)) {
            LinkedList<AccomType> list = new LinkedList<>(filteredRoomTypes);
            Collections.sort(list);
            filteredRoomTypes = new LinkedHashSet<>(list);
        }
        return filteredRoomTypes;
    }

    Set<AccomType> getFilteredRoomTypes(CPPricingFilter filter) {
        if (CollectionUtils.isNotEmpty(filter.getSelectedRoomTypes())) {
            return filter.getSelectedRoomTypes();
        } else if (filter.getSelectedRoomClass() != null) {
            return filter.getSelectedRoomClass().getAccomTypes();
        }
        return new ArraySet<>(AccomType.class);
    }

    public List<Integer> getSelectedAccomClassesBasedOnSearchCriteria(List<AccomClass> accomClassList) {
        CPPricingFilter currentSearchCriteria = getCpPricingFilter();
        if (isRTSelected(currentSearchCriteria)) {
            return getCpPricingFilter().getSelectedRoomTypes().stream().map(accomType -> accomType.getAccomClass().getId()).collect(Collectors.toList());
        } else if (isSpecificRCSelected(currentSearchCriteria)) {
            return Collections.singletonList(currentSearchCriteria.getSelectedRoomClass().getId());
        }
        return accomClassList.stream()
                .filter(accomClass -> accomClass.getId() != -1)
                .map(AccomClass::getId)
                .collect(Collectors.toList());
    }

    boolean isRTSelected(CPPricingFilter currentSearchCriteria) {
        return CollectionUtils.isNotEmpty(currentSearchCriteria.getSelectedRoomTypes());
    }

    boolean isSpecificRCSelected(CPPricingFilter currentSearchCriteria) {
        return currentSearchCriteria.getSelectedRoomClass().getId() != -1;
    }

    public SimplifiedWhatIfResult simplifiedAnalyzeChanges() {
        List<BAROverride> whatIfEligibleOverrides = createWhatIfOverrides(getPendingBAROverrides());
        SimplifiedWhatIfResult simplifiedWhatIfResult = simplifiedWhatIfService.analyzeChanges(whatIfEligibleOverrides);
        updateOverrideIconsFor(simplifiedWhatIfResult);
        return simplifiedWhatIfResult;
    }

    private void updateOverrideIconsFor(SimplifiedWhatIfResult simplifiedWhatIfResult) {
        if (simplifiedWhatIfResult == null) {
            return;
        }
        Map<Date, String> dateOverRideIconHtmlMap = getDateOverrideIconHtmlMap(getPendingBAROverrides());
        simplifiedWhatIfResult.getDateData().forEach(dateData ->
                dateData.setOverrideIconsHtml(dateOverRideIconHtmlMap.get(dateData.getDate()))
        );
    }

    List<BAROverride> createWhatIfOverrides(Map<CPDecisionBAROutput, CPOverrideWrapper> pendingOverrides) {
        return pendingOverrides.entrySet().stream()
                .map(this::crateBarOverrideFor)
                .collect(Collectors.toList());
    }

    private BAROverride crateBarOverrideFor(Map.Entry<CPDecisionBAROutput, CPOverrideWrapper> entry) {
        CPDecisionBAROutput output = entry.getKey();
        CPOverrideWrapper wrapper = entry.getValue();
        AccomTypeSupplementValue supplement = accomTypeSupplementService.getSupplementValueFor(output.getProduct().getId(), output.getArrivalDate(), output.getAccomType().getId());
        BigDecimal supplementValue = BigDecimal.ZERO;
        if (supplement != null) {
            supplementValue = supplement.getValue();
        }
        BAROverride override = new BAROverride();
        override.setArrivalDate(output.getArrivalDate().toDate());
        override.setLengthOfStay(output.getLengthOfStay());
        if (configParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_PERCENTAGE_FOR_SUPPLEMENT) && null != supplement) {
            override.setSpecificRate(Supplement.removeSupplementFrom(wrapper.getSpecificOverride(), supplementValue, supplement.getOffsetMethod().equals(OffsetMethod.PERCENTAGE)));
            override.setFloorRate(Supplement.removeSupplementFrom(wrapper.getFloorOverride(), supplementValue, supplement.getOffsetMethod().equals(OffsetMethod.PERCENTAGE)));
            override.setCeilRate(Supplement.removeSupplementFrom(wrapper.getCeilingOverride(), supplementValue, supplement.getOffsetMethod().equals(OffsetMethod.PERCENTAGE)));
        } else  {
            subtractSupplementValueFromOverrides(wrapper, supplementValue, override);
        }
        override.setAccomTypeId(output.getAccomType().getId());
        override.setRemove(wrapper.isPendingDelete());
        if (isGroupFloorEnabled() && isGroupFloorApplicable(wrapper)) {
            final GroupFloorOverride groupFloorOverride = getGroupFloorOverrideVO(wrapper);
            if (null != groupFloorOverride) {
                override.setGPFloor(true);
                override.setFloorRate(BigDecimalUtil.subtractReturnsNull(groupFloorOverride.getPrettyGroupRate(), supplementValue).setScale(2, RoundingMode.HALF_UP));
            }
        }
        return override;
    }

    private static void subtractSupplementValueFromOverrides(CPOverrideWrapper wrapper, BigDecimal supplementValue, BAROverride override) {
        override.setSpecificRate(BigDecimalUtil.subtractReturnsNull(wrapper.getSpecificOverride(), supplementValue));
        override.setCeilRate(BigDecimalUtil.subtractReturnsNull(wrapper.getCeilingOverride(), supplementValue));
        override.setFloorRate(BigDecimalUtil.subtractReturnsNull(wrapper.getFloorOverride(), supplementValue));
    }

    private boolean isGroupFloorApplicable(CPOverrideWrapper wrapper) {
        return ((null == wrapper.getSpecificOverride() && null == wrapper.getCeilingOverride() && null == wrapper.getFloorOverride() && !wrapper.getOriginalOverrideType().equals(DecisionOverrideType.GPFLOOR)) ||
                (null != wrapper.getGroupFloorOverride() && null != wrapper.getCeilingOverride()));
    }

    private GroupFloorOverride getGroupFloorOverrideVO(CPOverrideWrapper wrapper) {
        List<GroupFloorOverride> groupFloorOverridesList = getGroupFloorOverridesList(wrapper.getAccomClass(), wrapper.getStartDate(), wrapper.getEndDate());
        if (!groupFloorOverridesList.isEmpty()) {
            final List<Integer> allowedReasonIds = Arrays.asList(1, 2, 5);
            final GroupFloorOverride groupFloorOverride = groupFloorOverridesList.get(0);
            if (allowedReasonIds.stream().anyMatch(x -> x.equals(groupFloorOverride.getOverrideReason().getId()))) {
                return groupFloorOverride;
            }
        }
        return null;
    }

    private List<GroupFloorOverride> getGroupFloorOverridesList(AccomClass selectedAccomClass, LocalDate startDate, LocalDate endDate) {
        return groupFloorOverrideService.getGroupFloorOverridesFor(selectedAccomClass, startDate, endDate);
    }

    Map<Date, String> getDateOverrideIconHtmlMap(Map<CPDecisionBAROutput, CPOverrideWrapper> whatIfEligibleOverrides) {
        CPPricingManagementOverrideHtmlConverter cpOverrideHtmlConverter = getCpPricingManagementOverrideHtmlConverter();
        Map<Date, String> dateOverRideIconHtmlMap = new HashMap<>();
        whatIfEligibleOverrides.entrySet()
                .forEach(e -> extractOverrideIconHtml(cpOverrideHtmlConverter, dateOverRideIconHtmlMap, e));
        return dateOverRideIconHtmlMap;
    }

    CPPricingManagementOverrideHtmlConverter getCpPricingManagementOverrideHtmlConverter() {
        return new CPPricingManagementOverrideHtmlConverter(getSystemDateAsLocalDate());
    }

    public LocalDate getSystemDateAsLocalDate() {
        return uiContext.getSystemCaughtUpDateAsLocalDate();
    }

    private void extractOverrideIconHtml(CPPricingManagementOverrideHtmlConverter cpOverrideHtmlConverter, Map<Date, String> dateOverRideIconHtmlMap, Map.Entry<CPDecisionBAROutput, CPOverrideWrapper> e) {
        StringBuilder iconHtmlBuiler = new StringBuilder();
        final Date arrivalDate = e.getKey().getArrivalDate().toDate();
        final CPOverrideWrapper wrapper = e.getValue();
        boolean isGFOApplied = isGroupFloorEnabled() && null != wrapper && isGroupFloorApplicable(wrapper) && null != getGroupFloorOverrideVO(wrapper);
        if (dateOverRideIconHtmlMap.containsKey(arrivalDate)) {
            iconHtmlBuiler.append(dateOverRideIconHtmlMap.get(arrivalDate));
        }
        String currentIconHtml = cpOverrideHtmlConverter.convertToPresentation(e.getValue(), String.class, locale, true, isGFOApplied);
        if (!iconHtmlBuiler.toString().contains(currentIconHtml)) {
            iconHtmlBuiler.append(currentIconHtml);
        }
        dateOverRideIconHtmlMap.put(arrivalDate, iconHtmlBuiler.toString());
    }

    public void setLocale(Locale locale) {
        this.locale = locale;
    }

    public boolean isDisplayOccupancyForecastInWhatIfEnabled() {
        return simplifiedWhatIfService.isDisplayOccupancyForecastInWhatIfToggleEnabled();
    }
    public Map<Date, String> buildMapOfInventoryLimit(Date startDate, Date endDate){
        Map<Date, String> inventoryLimitMap = new HashMap<>();
        List<InventoryLimitDecision> inventoryLimits = getInventoryLimitDecisions(startDate, endDate);
        List<InventoryLimitDecision> overriddenInventoryLimits = getOverriddenInventoryLimits(startDate, endDate);
        List<DecisionGPInventoryLimitOverride> inventoryLimitOverrideList = new ArrayList<>();
        for (InventoryLimitDecision inventoryLimit : inventoryLimits) {
            inventoryLimitMap.put(inventoryLimit.getOccupancyDate(), String.valueOf(inventoryLimit.getInventoryLimit()));
        }
        for (InventoryLimitDecision overriddenInventory : overriddenInventoryLimits) {
            if(inventoryLimitMap.containsKey(overriddenInventory.getOccupancyDate())) {
                if(Integer.parseInt(inventoryLimitMap.get(overriddenInventory.getOccupancyDate())) > (overriddenInventory.getInventoryLimit())) {
                    Optional<Integer> decisionIdOptional = getInventoryForAllDates(startDate, endDate).stream()
                            .filter(inventoryLimitOverride -> inventoryLimitOverride.getOccupancyDate().equals(overriddenInventory.getOccupancyDate())
                                    && inventoryLimitOverride.getStatus().equals(TenantStatusEnum.ACTIVE))
                            .map(inventoryLimit -> inventoryLimit.getDecisionId())
                            .findFirst();
                    if(decisionIdOptional.isPresent())
                        updateInventoryLimitDecisions(decisionIdOptional.get(),overriddenInventory.getInventoryLimit(),overriddenInventory.getOccupancyDate());
                    inventoryLimitMap.replace(overriddenInventory.getOccupancyDate(), overriddenInventory.getInventoryLimit() + OVERRIDDEN);
                }
                else{
                    inventoryLimitMap.replace(overriddenInventory.getOccupancyDate(), inventoryLimitMap.get(overriddenInventory.getOccupancyDate()) + OVERRIDDEN);
                }
            } else {
                inventoryLimitMap.put(overriddenInventory.getOccupancyDate(), overriddenInventory.getInventoryLimit() + OVERRIDDEN);
            }
        }
        return inventoryLimitMap;
    }

    @ForTesting
    public void setUiContext(UiContext uiContext) {
        this.uiContext = uiContext;
    }

    public CPConfiguration getCpConfiguration() {
        return cpConfiguration;
    }

    public OccupancyType getBaseOccupancyType() {
        return baseOccupancyType;
    }

    public Map<Integer, PricingRule> getPricingRules() {
        return pricingRules;
    }

    public Tax getTax() {
        return tax;
    }

    public List<OccupantBucketEntity> getOccupantBucketEntities() {
        return occupantBucketEntities;
    }

    public List<MaximumOccupantsEntity> getMaximumOccupantsEntitiesList() {
        return maximumOccupantsEntitiesList;
    }

    public List<ProductPackage> getUngroupedProductPackages() {
        return ungroupedProductPackages;
    }

    @ForTesting
    public void setPrettyPricingService(PrettyPricingService prettyPricingService) {
        this.prettyPricingService = prettyPricingService;
    }

    @ForTesting
    public void setTaxService(TaxService taxService) {
        this.taxService = taxService;
    }

    @ForTesting
    public void setDateService(DateService dateService) {
        this.dateService = dateService;
    }

    @ForTesting
    public void setPerPersonPricingService(PerPersonPricingService perPersonPricingService) {
        this.perPersonPricingService = perPersonPricingService;
    }

    @ForTesting
    public void setCpConfiguration(CPConfiguration cpConfiguration) {
        this.cpConfiguration = cpConfiguration;
    }

    @ForTesting
    public void setBaseOccupancyType(OccupancyType baseOccupancyType) {
        this.baseOccupancyType = baseOccupancyType;
    }

    @ForTesting
    public void setPricingRules(Map<Integer, PricingRule> pricingRules) {
        this.pricingRules = pricingRules;
    }

    @ForTesting
    public void setTax(Tax tax) {
        this.tax = tax;
    }

    @ForTesting
    public void setOccupantBucketEntities(List<OccupantBucketEntity> occupantBucketEntities) {
        this.occupantBucketEntities = occupantBucketEntities;
    }

    @ForTesting
    public void setMaximumOccupantsEntitiesList(List<MaximumOccupantsEntity> maximumOccupantsEntitiesList) {
        this.maximumOccupantsEntitiesList = maximumOccupantsEntitiesList;
    }

    @ForTesting
    public void setUngroupedProductPackages(List<ProductPackage> ungroupedProductPackages) {
        this.ungroupedProductPackages = ungroupedProductPackages;
    }

    @ForTesting
    public void setProductManagementService(ProductManagementService productManagementService) {
        this.service = productManagementService;
    }

    public void setSelectedAccomTypes(List<AccomType> selectedAccomTypes) {
        this.selectedAccomTypes = selectedAccomTypes;
    }

    public void setAccomClassIds(List<Integer> accomClassIds) {
        this.accomClassIds = accomClassIds;
    }

    public Map<LocalDate, Map<Integer, CPRoomClassDTO>> getRoomClassDetailsMapById() {
        return roomClassDetailsMapById;
    }

    public Map<LocalDate, Map<Integer, CPRoomClassDTO>> getAccomTypeDetailsMapById() {
        return accomTypeDetailsMapById;
    }

    public void setAvailableCapacityToSellEnabled(boolean availableCapacityToSellEnabled) {
        this.availableCapacityToSellEnabled = availableCapacityToSellEnabled;
    }

    public boolean isHospitalityRoom(String accomTypeCode) {
        return hospitalityRooms != null && hospitalityRooms.contains(accomTypeCode);
    }

    @UIScoped
    public static class Factory {
        @Inject
        protected Instance<PricingOverrideManager> instance;

        public PricingOverrideManager create() {
            PricingOverrideManager pricingOverrideManager = instance.get();
            pricingOverrideManager.setAccomTypeFiltrationCriteria();
            return pricingOverrideManager;
        }
    }

    @ForTesting
    public void setAccomTypeFiltrationCriteria(Predicate<AccomType> accomTypeFiltrationCriteria) {
        this.accomTypeFiltrationCriteria = accomTypeFiltrationCriteria;
    }

    @ForTesting
    public Predicate<AccomType> getAccomTypeFiltrationCriteria() {
        return this.accomTypeFiltrationCriteria;
    }

    public Predicate<AccomType> getAccomTypesFiltrationCriteria() {
        Predicate<AccomType> isActiveAccomType = accomType -> accomType.getStatusId() == 1;
        Predicate<AccomType> isValidCapacityAccomType = accomType -> accomType.getAccomTypeCapacity() != 0;
        Predicate<AccomType> isNotDiscontinuedRoomType = accomType -> accomType.getDisplayStatusId() != 2;
        Predicate<AccomType> isHospitalityRoom = accomType -> {
            if(optimizeHospitalityRoomFetch) {
                return hospitalityRooms.contains(accomType.getAccomTypeCode());
            }
            return hospitalityRoomsService.getAllTypesOfHospitalityRooms().contains(accomType.getAccomTypeCode());
        };
        return isActiveAccomType.and(isNotDiscontinuedRoomType).and(isValidCapacityAccomType.or(isHospitalityRoom));
    }

    private void setAccomTypeFiltrationCriteria() {
        this.accomTypeFiltrationCriteria = getAccomTypesFiltrationCriteria();
    }
}
