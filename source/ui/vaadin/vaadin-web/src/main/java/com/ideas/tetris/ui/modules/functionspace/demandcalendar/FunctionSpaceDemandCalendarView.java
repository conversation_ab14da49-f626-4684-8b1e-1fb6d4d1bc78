package com.ideas.tetris.ui.modules.functionspace.demandcalendar;

import com.ideas.infra.tetris.security.domain.TetrisPermissionKey;
import com.ideas.tetris.ui.common.cdi.TetrisNavigatorView;
import com.ideas.tetris.ui.modules.functionspace.demandcalendar.bookingsummary.BookingSummaryUiWrapper;
import com.ideas.tetris.ui.modules.functionspace.demandcalendar.bookingsummary.FunctionSpaceBookingSummaryComponent;
import com.ideas.tetris.ui.modules.functionspace.demandcalendar.calendar.DemandCalendarUIWrapper;
import com.ideas.tetris.ui.modules.functionspace.demandcalendar.calendar.FunctionSpaceDemandCalendarComponent;
import com.vaadin.cdi.CDIView;
import com.vaadin.ui.HorizontalSplitPanel;
import com.vaadin.ui.UI;
import org.joda.time.LocalDate;

import java.util.Map;

@CDIView(uis = UI.class, value = TetrisPermissionKey.FUNCTION_SPACE_DEMAND_CALENDAR)
public class FunctionSpaceDemandCalendarView extends TetrisNavigatorView<FunctionSpaceDemandCalendarPresenter, Void> {

    private HorizontalSplitPanel root;
    private FunctionSpaceDemandCalendarComponent calendarComponent;
    private FunctionSpaceBookingSummaryComponent bookingSummaryComponent;

    @Override
    protected void initView() {
        setSizeFull();

        root = new HorizontalSplitPanel();
        root.setSizeFull();
        root.setSplitPosition(21, Unit.PERCENTAGE);

        setCompositionRoot(this.root);
    }

    @Override
    public void onPresenterInit() {
        bookingSummaryComponent = new FunctionSpaceBookingSummaryComponent(presenter);
        root.addComponent(bookingSummaryComponent);

        calendarComponent = new FunctionSpaceDemandCalendarComponent(presenter);
        root.addComponent(calendarComponent);
    }

    public void updateCalendar(LocalDate selectedMonth, Map<LocalDate, DemandCalendarUIWrapper> calendarData) {
        calendarComponent.updateCalendar(selectedMonth, calendarData);
    }

    public void updateSummary(BookingSummaryUiWrapper summaryUiWrapper) {
        bookingSummaryComponent.updateSummary(summaryUiWrapper);
    }


    public void updateHeatMap(LocalDate selectedDate) {
        calendarComponent.updateHeatMap(selectedDate);
    }
}
