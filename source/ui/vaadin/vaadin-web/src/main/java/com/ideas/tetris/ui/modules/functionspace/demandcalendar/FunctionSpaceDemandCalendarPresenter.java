package com.ideas.tetris.ui.modules.functionspace.demandcalendar;

import com.ideas.tetris.pacman.services.functionspace.configuration.entity.FunctionSpaceDayPart;
import com.ideas.tetris.pacman.services.functionspace.configuration.entity.FunctionSpaceFunctionRoom;
import com.ideas.tetris.pacman.services.functionspace.demandcalendar.service.FunctionSpaceDemandCalendarService;
import com.ideas.tetris.pacman.services.functionspace.dto.FunctionSpaceDemandCalendarDateDto;
import com.ideas.tetris.pacman.services.functionspace.dto.FunctionSpaceDemandCalendarDatePartDto;
import com.ideas.tetris.pacman.services.functionspace.dto.FunctionSpaceDemandCalendarDateStatus;
import com.ideas.tetris.pacman.services.functionspace.dto.FunctionSpaceDemandCalendarRoomSummaryDto;
import com.ideas.tetris.pacman.services.functionspace.dto.FunctionSpaceDemandCalendarSummaryDto;
import com.ideas.tetris.pacman.services.specialevent.dto.SpecialEventSummaryDto;
import com.ideas.tetris.pacman.services.specialevent.service.SpecialEventService;
import com.ideas.tetris.platform.common.xml.schema.workcontext.v1.WorkContextType;
import com.ideas.tetris.ui.common.cdi.TetrisPresenter;
import com.ideas.tetris.ui.common.util.DateFormatUtil;
import com.ideas.tetris.ui.common.util.DateUtil;
import com.ideas.tetris.ui.modules.functionspace.demandcalendar.bookingsummary.BookingSummaryUiWrapper;
import com.ideas.tetris.ui.modules.functionspace.demandcalendar.calendar.DemandCalendarUIWrapper;
import com.ideas.tetris.ui.modules.functionspace.demandcalendar.calendar.FunctionSpaceDemandCalendarComponent;
import com.ideas.tetris.ui.modules.functionspace.demandcalendar.calendar.FunctionSpaceDemandCalendarMockData;
import com.ideas.tetris.ui.modules.functionspace.demandcalendar.calendar.FunctionSpaceDetailsUiWrapper;
import org.joda.time.LocalDate;
import org.joda.time.LocalDateTime;

import javax.inject.Inject;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import com.ideas.tetris.spring.SpringAutowired;

@SpringAutowired
public class FunctionSpaceDemandCalendarPresenter extends TetrisPresenter<FunctionSpaceDemandCalendarView, Void> {

    private LocalDate selectedMonth;
    @Autowired
	private FunctionSpaceDemandCalendarService functionSpaceDemandCalendarService;

    @Autowired
	private SpecialEventService specialEventService;

    private Map<LocalDate, DemandCalendarUIWrapper> heatMapData;

    @Override
    public void onViewOpened(Void aVoid) {
        init();
    }

    @Override
    public void onWorkContextChange(WorkContextType workContextType) {
        init();
        //view.updateHeatMap(selectedMonth);
    }

    private void init() {
        //get the current system
        selectedMonth = getSystemDateAsLocalDate().withDayOfMonth(1);
        loadCalendarData();
        loadHeatMapData();
    }

    private void loadCalendarData() {
        LocalDate startDate = selectedMonth.dayOfMonth().withMinimumValue();
        LocalDate endDate = selectedMonth.dayOfMonth().withMaximumValue();

        //get data from service for calendar and summary
        //public Map<Date, String> dateStyles = new HashMap<Date, String>();

        Map<LocalDate, DemandCalendarUIWrapper> calendarData = getCalendarData();

        view.updateCalendar(selectedMonth, calendarData);

        BookingSummaryUiWrapper bookingSummaryData = getBookingSummaryData(calendarData);
        view.updateSummary(bookingSummaryData);
    }

    private void loadHeatMapData() {
        heatMapData = getHeatMapData(selectedMonth);
        view.updateHeatMap(selectedMonth);
    }

    private BookingSummaryUiWrapper getBookingSummaryData(Map<LocalDate, DemandCalendarUIWrapper> calendarData) {
        FunctionSpaceDemandCalendarSummaryDto data;
        if (mockModeEnabled) {
            data = FunctionSpaceDemandCalendarMockData.getBookingSummaryMockData();
        } else {
            LocalDate maximumValue = getSelectedMonth().dayOfMonth().withMinimumValue();
            data = functionSpaceDemandCalendarService.getDemandCalendartSummaryDto(maximumValue, getSystemDateAsLocalDate());
        }
        calculateMonthAverage(data, calendarData);
        return new BookingSummaryUiWrapper(data);
    }

    protected void calculateMonthAverage(FunctionSpaceDemandCalendarSummaryDto data, Map<LocalDate, DemandCalendarUIWrapper> calendarData) {
        BigDecimal utilizationForecast = BigDecimal.ZERO;
        BigDecimal onBooksUtilization = BigDecimal.ZERO;
        BigDecimal utilization = BigDecimal.ZERO;
        int open = 0;
        int closed = 0;
        int evaluate = 0;

        for (LocalDate localDate : calendarData.keySet()) {
            DemandCalendarUIWrapper demandCalendarUIWrapper = calendarData.get(localDate);

            if (!isPast(localDate)) {
                utilizationForecast = sumNullSafeBigDecimal(utilizationForecast, demandCalendarUIWrapper.getUtilizationForecast());
                onBooksUtilization = sumNullSafeBigDecimal(onBooksUtilization, demandCalendarUIWrapper.getOnBooksUtilization());

                if (demandCalendarUIWrapper.getStatus().equals(FunctionSpaceDemandCalendarDateStatus.CLOSED)) {
                    closed++;
                } else if (demandCalendarUIWrapper.getStatus().equals(FunctionSpaceDemandCalendarDateStatus.OPEN)) {
                    open++;
                } else if (demandCalendarUIWrapper.getStatus().equals(FunctionSpaceDemandCalendarDateStatus.EVALUATE)) {
                    evaluate++;
                }
            } else {
                utilization = sumNullSafeBigDecimal(utilization, demandCalendarUIWrapper.getActualUtilization());
            }
        }
        //if current month is system date month, calculate days after system date.
        int numberOfDays = getNumberOfDaysInMonthAfterSystemDate();
        int numberOfPastDays = selectedMonth.dayOfMonth().getMaximumValue() - numberOfDays;

        if (numberOfDays != 0) {
            data.setAvgUtilizationForecast(utilizationForecast.divide(BigDecimal.valueOf(numberOfDays), BigDecimal.ROUND_HALF_UP));
            data.setAvgUtilizationOTBs(onBooksUtilization.divide(BigDecimal.valueOf(numberOfDays), BigDecimal.ROUND_HALF_UP));
        }
        if (numberOfPastDays != 0) {
            data.setAvgUtilization(utilization.divide(BigDecimal.valueOf(numberOfPastDays), BigDecimal.ROUND_HALF_UP));
        }
        data.setOpen(open);
        data.setClosed(closed);
        data.setEvaluate(evaluate);
    }

    public boolean isPast(LocalDate date) {
        return DateUtil.isDateInPast(getSystemDateAsLocalDate(), date);
    }


    protected int getNumberOfDaysInMonthAfterSystemDate() {
        if (getSelectedMonth().getMonthOfYear() == getSystemDateAsLocalDate().getMonthOfYear()) {
            return getSelectedMonth().dayOfMonth().getMaximumValue() - getSystemDateAsLocalDate().getDayOfMonth() + 1;
        } else if (getSelectedMonth().compareTo(getSystemDateAsLocalDate()) >= 0) {
            return getSelectedMonth().dayOfMonth().getMaximumValue();
        }
        return 0;

    }

    private BigDecimal sumNullSafeBigDecimal(BigDecimal utilizationForecast, BigDecimal value) {
        if (value != null) {
            return utilizationForecast.add(value);
        }
        return utilizationForecast;
    }

    private Map<LocalDate, DemandCalendarUIWrapper> getCalendarData() {
        if (mockModeEnabled) {
            return FunctionSpaceDemandCalendarMockData.getData(selectedMonth, getSystemDateAsLocalDate());
        } else {
            //call out to service to get the data
            LocalDate startDate = selectedMonth.dayOfMonth().withMinimumValue();
            LocalDate endDate = selectedMonth.dayOfMonth().withMaximumValue();
            Map<LocalDate, FunctionSpaceDemandCalendarDateDto> dtos = functionSpaceDemandCalendarService.findCalendarDateDtos(startDate, endDate);
            return convertToWrapper(dtos, true);
        }
    }


    public LocalDate getSelectedMonth() {
        return selectedMonth;
    }

    public void onMonthSelected(LocalDate selectedDay) {
        selectedMonth = selectedDay;
        loadCalendarData();
    }

    public Map<LocalDate, DemandCalendarUIWrapper> getHeatMapData(LocalDate selectedMonth) {
        if (mockModeEnabled) {
            return FunctionSpaceDemandCalendarMockData.getHeatMapData(selectedMonth, getSystemDateAsLocalDate());
        } else {
            //service call
            selectedMonth = selectedMonth.withDayOfMonth(1);
            LocalDate endMonth = new LocalDate(selectedMonth).plusMonths(FunctionSpaceDemandCalendarComponent.DEFAULT_HEATMAP_CALENDAR_COUNT - 1);
            endMonth = endMonth.dayOfMonth().withMaximumValue();
            Map<LocalDate, FunctionSpaceDemandCalendarDateDto> dtos = functionSpaceDemandCalendarService.findCalendarDateDtos(selectedMonth, endMonth);
            return convertToWrapper(dtos, false);
        }
    }

    public void changeCalendarMonth(LocalDate localDate) {
        selectedMonth = localDate;
        onMonthSelected(localDate);
    }

    private Map<LocalDate, DemandCalendarUIWrapper> convertToWrapper(Map<LocalDate, FunctionSpaceDemandCalendarDateDto> dtoMap, boolean loadSpecialEventDetails) {
        Map<LocalDate, DemandCalendarUIWrapper> wrapperHashMap = new HashMap<LocalDate, DemandCalendarUIWrapper>();
        for (LocalDate localDate : dtoMap.keySet()) {
            DemandCalendarUIWrapper wrapper = new DemandCalendarUIWrapper(dtoMap.get(localDate));

            //check if this date has special events and if it does, we need the special event details
            if (loadSpecialEventDetails && wrapper.isHasSpecialEvent()) {
                List<SpecialEventSummaryDto> specialEventSummaryDtos = specialEventService.findSpecialEventSummaryDtos(localDate);
                wrapper.setSpecialEvents(specialEventSummaryDtos);
            }
            wrapperHashMap.put(localDate, wrapper);
        }
        return wrapperHashMap;
    }

    //For testing
    public void setSelectedMonth(LocalDate selectedMonth) {
        this.selectedMonth = selectedMonth;
    }

    public FunctionSpaceDetailsUiWrapper getDetails(LocalDate date, DemandCalendarUIWrapper wrapper) {
        if (mockModeEnabled) {
            return FunctionSpaceDemandCalendarMockData.getDetails(false);
        } else {
            LocalDate systemDate = getSystemDateAsLocalDate();
            FunctionSpaceDemandCalendarRoomSummaryDto calendarRoomSummaryDto = functionSpaceDemandCalendarService.findCalendarRoomSummaryDto(date, systemDate);
            List<SpecialEventSummaryDto> specialEvents = functionSpaceDemandCalendarService.findSpecialEventSummaryDtos(date);
            List<FunctionSpaceDemandCalendarDatePartDto> dayParts = wrapper.getDto().getFunctionSpaceDemandCalendarDatePartDtos();
            LocalDateTime localDateTime = new LocalDateTime(date.toDateTimeAtStartOfDay().getMillis());
            Map<FunctionSpaceDayPart, List<FunctionSpaceFunctionRoom>> functionSpaceRoomsBooked = functionSpaceDemandCalendarService.findFunctionSpaceRoomsBooked(localDateTime);
            return new FunctionSpaceDetailsUiWrapper(dayParts, specialEvents, calendarRoomSummaryDto, functionSpaceRoomsBooked, false, systemDate);
        }
    }

    public FunctionSpaceDetailsUiWrapper getLastYearDetails(LocalDate lastYearDate, FunctionSpaceDetailsUiWrapper detailsUiWrapper) {
        if (mockModeEnabled) {
            return FunctionSpaceDemandCalendarMockData.getDetails(true);
        } else {
            Map<LocalDate, FunctionSpaceDemandCalendarDateDto> calendarDateDtos = functionSpaceDemandCalendarService.findCalendarDateDtos(lastYearDate, lastYearDate);
            List<SpecialEventSummaryDto> specialEvents = functionSpaceDemandCalendarService.findSpecialEventSummaryDtos(lastYearDate);
            List<FunctionSpaceDemandCalendarDatePartDto> dayParts = calendarDateDtos.get(lastYearDate).getFunctionSpaceDemandCalendarDatePartDtos();
            LocalDateTime localDateTime = new LocalDateTime(lastYearDate.toDateTimeAtStartOfDay().getMillis());
            Map<FunctionSpaceDayPart, List<FunctionSpaceFunctionRoom>> functionSpaceRoomsBooked = functionSpaceDemandCalendarService.findFunctionSpaceRoomsBooked(localDateTime);
            return new FunctionSpaceDetailsUiWrapper(dayParts, specialEvents, detailsUiWrapper.getRoomDto(), functionSpaceRoomsBooked, true, getSystemDateAsLocalDate());
        }

    }

    public String getStyleForHeatMapDate(LocalDate date) {
        //check to see if we have the data for this date, and if we don't, that means the user has navigated
        //outside our initial start and end range for the heatmap.
        if (!heatMapData.containsKey(date)) {
            Map<LocalDate, DemandCalendarUIWrapper> heatMapData1 = getHeatMapData(date);
            heatMapData.putAll(heatMapData1);
        }

        String style = null;
        DemandCalendarUIWrapper demandCalendarUIWrapper = heatMapData.get(date);
        if (demandCalendarUIWrapper != null) {
            style = demandCalendarUIWrapper.getForecastLevel().getStyleName();
        }

        return style;
    }

    public String getToolTipForHeatMapDate(LocalDate date) {
        DemandCalendarUIWrapper wrapper = heatMapData.get(date);
        String htmlToolTip = wrapper != null ? wrapper.getDate().toString(DateFormatUtil.DEFAULT_FORMAT) : "";
        if (isPast(date)) {
            htmlToolTip += "\nActual Utilization: " + (wrapper != null ? wrapper.getActualUtilization() : "N/A");
        } else {
            htmlToolTip += "\nForecasted Utilization: " + (wrapper != null ? wrapper.getUtilizationForecast() : "N/A");
        }
        return htmlToolTip;
    }

    //for testing
    public void setHeatMapData(Map<LocalDate, DemandCalendarUIWrapper> heatMapData) {
        this.heatMapData = heatMapData;
    }

    public List<SpecialEventSummaryDto> getSpecialEvents(LocalDate localDate) {
        return specialEventService.findSpecialEventSummaryDtos(localDate);
    }
}
