package com.ideas.tetris.ui.modules.internalalert;

import com.ideas.tetris.pacman.services.internalalert.InternalAlertService;
import com.ideas.tetris.pacman.services.internalalert.InternalAlertType;
import com.ideas.tetris.ui.common.cdi.TetrisPresenter;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

public class AlertSubscriptionPresenter extends TetrisPresenter<AlertSubscriptionView, Void> {

    @Autowired
    InternalAlertService internalAlertService;

    @Override
    public void onViewOpened(Void aVoid) {
        view.initViewInternal();
        view.updateSubscriptionTable(getSubscriptions());
    }

    protected List<SubscriptionDto> getSubscriptions() {
        List<InternalAlertType> currentSubscriptions = internalAlertService.getSubscriptions();
        return Arrays.asList(InternalAlertType.values()).stream()
                .map(type -> new SubscriptionDto(type, currentSubscriptions.contains(type)))
                .collect(Collectors.toList());
    }

    protected void updateSubscriptions(List<SubscriptionDto> subscriptions) {
        List<InternalAlertType> subscribedTypes = subscriptions.stream()
                .filter(SubscriptionDto::isSubscribed)
                .map(SubscriptionDto::getAlertType)
                .collect(Collectors.toList());
        internalAlertService.updateSubscriptions(subscribedTypes);
    }

}
