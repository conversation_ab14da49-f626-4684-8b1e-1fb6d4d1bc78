package com.ideas.tetris.ui.modules.marketsegment.common;


import com.ideas.tetris.pacman.services.marketsegment.service.AnalyticalMarketSegment;
import com.ideas.tetris.pacman.services.marketsegment.service.AnalyticalMarketSegmentAttribute;
import com.ideas.tetris.pacman.services.marketsegment.service.AnalyticalMarketSegmentSummary;
import com.ideas.tetris.pacman.services.marketsegment.service.RateCodeSummary;
import com.ideas.tetris.pacman.services.marketsegment.service.RateCodeTypeEnum;
import org.apache.commons.lang.RandomStringUtils;
import org.apache.commons.lang.math.RandomUtils;
import org.hamcrest.Matcher;

import javax.inject.Singleton;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.stream.Collectors;

import static org.hamcrest.Matchers.containsString;
import static org.hamcrest.Matchers.endsWith;
import static org.hamcrest.Matchers.equalToIgnoringCase;
import static org.hamcrest.Matchers.startsWith;

@Singleton
public class AttributeAssignmentMockData {
    private List<AnalyticalMarketSegment> assignedMarketSegments;
    private List<AnalyticalMarketSegmentSummary> unassignedMarketSegments;
    private List<AnalyticalMarketSegmentSummary> groupMarketSegments;
    private List<AnalyticalMarketSegmentSummary> rateCodeLevel;
    private List<AnalyticalMarketSegmentSummary> marketSegmentLevel;
    private Map<String, List<RateCodeSummary>> marketSegmentRateCodeHashMap;
    private Map<String, List<RateCodeSummary>> assignedMarketSegmentRateCodeHashMap;

    AttributeAssignmentMockData() {
        loadData();
    }

    private void loadData() {
        marketSegmentRateCodeHashMap = new HashMap<String, List<RateCodeSummary>>();
        assignedMarketSegmentRateCodeHashMap = new HashMap<String, List<RateCodeSummary>>();
        unassignedMarketSegments = getAnalyticalMarketSegmentSummaries();
        assignedMarketSegments = new ArrayList<AnalyticalMarketSegment>();
        reShuffle();
    }

    public List<AnalyticalMarketSegmentSummary> getAnalyticalMarketSegmentSummaries() {
        ArrayList<AnalyticalMarketSegmentSummary> data = new ArrayList<AnalyticalMarketSegmentSummary>();
        for (int i = 0; i < 20; i++) {
            data.add(createAnalyticalMarketSegmentSummary(i));
        }
        return data;
    }

    private void reShuffle() {
        groupMarketSegments = new ArrayList<AnalyticalMarketSegmentSummary>();
        marketSegmentLevel = new ArrayList<AnalyticalMarketSegmentSummary>();
        rateCodeLevel = new ArrayList<AnalyticalMarketSegmentSummary>();

        for (AnalyticalMarketSegmentSummary marketSegment : unassignedMarketSegments) {
            if (marketSegment.isGroupMs()) {
                groupMarketSegments.add(marketSegment);
            }
            marketSegmentLevel.add(marketSegment);
            rateCodeLevel.add(marketSegment);

        }
    }

    public List<AnalyticalMarketSegmentSummary> getUnassignedMarketSegmentLevel() {
        if (marketSegmentLevel != null) return marketSegmentLevel;
        reShuffle();
        return marketSegmentLevel;
    }

    public List<AnalyticalMarketSegmentSummary> getUnassignedRateCodeLevel() {
        if (rateCodeLevel != null) return rateCodeLevel;
        reShuffle();
        return rateCodeLevel;
    }

    public List<AnalyticalMarketSegmentSummary> getGroupMarketSegments() {
        if (groupMarketSegments != null) return groupMarketSegments;
        reShuffle();
        return groupMarketSegments;
    }

    public List<AnalyticalMarketSegment> getAssignedMarketSegments() {
        return assignedMarketSegments;
    }

    private AnalyticalMarketSegmentSummary createAnalyticalMarketSegmentSummary(int i) {
        AnalyticalMarketSegmentSummary analyticalMarketSegmentSummary = new AnalyticalMarketSegmentSummary();
        analyticalMarketSegmentSummary.setId(RandomStringUtils.randomAlphabetic(10));
        analyticalMarketSegmentSummary.setMarketCode("MC-" + i);
        analyticalMarketSegmentSummary.setGroupMs(i % 2 == 0);
        if (analyticalMarketSegmentSummary.isGroupMs()) {
            analyticalMarketSegmentSummary.setAssigned(i % 4 == 0);
        }
        analyticalMarketSegmentSummary.setRoomsSold(i);
        analyticalMarketSegmentSummary.setRoomRevenue(i * 2000.00);
        analyticalMarketSegmentSummary.setAverageDailyRate(200.00);
        analyticalMarketSegmentSummary.setHotelPercent(0.2);
        analyticalMarketSegmentSummary.setBlock(i * 2);
        analyticalMarketSegmentSummary.setPickup(i);

        marketSegmentRateCodeHashMap.put(analyticalMarketSegmentSummary.getMarketCode(), createRateCodes(analyticalMarketSegmentSummary));

        return analyticalMarketSegmentSummary;
    }

    public void unassignMarketSegments(List<AnalyticalMarketSegment> analyticalMarketSegments) {
        for (AnalyticalMarketSegment analyticalMarketSegment : analyticalMarketSegments) {
            unassignMarketSegment(analyticalMarketSegment);
        }
    }

    public void unassignMarketSegment(AnalyticalMarketSegment analyticalMarketSegment) {
        groupMarketSegments = marketSegmentLevel = rateCodeLevel = null;
        assignedMarketSegments.remove(analyticalMarketSegment);

        if (analyticalMarketSegment.getRateCode() == null) {
            unassignedMarketSegments.add(createUnassignedMarketSegment(analyticalMarketSegment));
            assignedMarketSegmentRateCodeHashMap.remove(analyticalMarketSegment.getMarketCode());
        } else {
            List<RateCodeSummary> rateCodeSummaries = assignedMarketSegmentRateCodeHashMap.get(analyticalMarketSegment.getMarketCode());
            List<RateCodeSummary> allRateCodesThatMatch = getAllRateCodesThatMatch(analyticalMarketSegment.getMarketCode(), analyticalMarketSegment.getRateCode(), analyticalMarketSegment.getRateCodeType());
            for (RateCodeSummary rateCodeSummary : allRateCodesThatMatch) {
                rateCodeSummaries.remove(rateCodeSummary);
            }

            if (rateCodeSummaries.isEmpty()) {
                assignedMarketSegmentRateCodeHashMap.remove(analyticalMarketSegment.getMarketCode());
            }

            List<AnalyticalMarketSegmentSummary> filter = unassignedMarketSegments.stream()
                    .filter(item -> analyticalMarketSegment.getMarketCode().equalsIgnoreCase(item.getMarketCode()))
                    .collect(Collectors.toList());
            if (filter.isEmpty()) {
                unassignedMarketSegments.add(createUnassignedMarketSegment(analyticalMarketSegment));
            }

        }
        reShuffle();
    }

    public Matcher<String> getMatcher(RateCodeTypeEnum myenum, String rateCode) {
        if (myenum == null) return equalToIgnoringCase(rateCode);

        if (myenum.equals(RateCodeTypeEnum.CONTAINS)) {
            return containsString(rateCode);
        } else if (myenum.equals(RateCodeTypeEnum.STARTS_WITH)) {
            return startsWith(rateCode);
        } else if (myenum.equals(RateCodeTypeEnum.ENDS_WITH)) {
            return endsWith(rateCode);
        }
        return null;
    }

    private AnalyticalMarketSegment createAssignedMarkeSegment(AnalyticalMarketSegmentSummary marketSegment, AnalyticalMarketSegmentAttribute attribute) {
        AnalyticalMarketSegment analyticalMarketSegment = new AnalyticalMarketSegment();
        analyticalMarketSegment.setRateCode(marketSegment.getRateCode());
        analyticalMarketSegment.setMappedMarketCode(marketSegment.getMarketCode() + "_" + attribute.getSuffix());
        analyticalMarketSegment.setAttribute(attribute);
        analyticalMarketSegment.setMarketCode(marketSegment.getMarketCode());
        analyticalMarketSegment.setRateCodeType(Enum.valueOf(RateCodeTypeEnum.class, marketSegment.getRateCodeType()));
        analyticalMarketSegment.setId(RandomUtils.nextInt());
        return analyticalMarketSegment;
    }

    private AnalyticalMarketSegmentSummary createUnassignedMarketSegment(AnalyticalMarketSegment analyticalMarketSegment) {
        AnalyticalMarketSegmentSummary summary = new AnalyticalMarketSegmentSummary();
        summary.setId(RandomStringUtils.randomAlphabetic(10));
        summary.setMappedCode(analyticalMarketSegment.getMappedMarketCode());
        summary.setMarketCode(analyticalMarketSegment.getMarketCode());
        summary.setGroupMs(analyticalMarketSegment.getAttribute().equals(AnalyticalMarketSegmentAttribute.GROUP));
        return summary;
    }

    public void assignMarketSegments(List<AnalyticalMarketSegmentSummary> segments, AnalyticalMarketSegmentAttribute attribute) {
        groupMarketSegments = marketSegmentLevel = rateCodeLevel = null;
        for (AnalyticalMarketSegmentSummary marketSegment : segments) {
            assignedMarketSegments.add(createAssignedMarkeSegment(marketSegment, attribute));
            if (marketSegment.getRateCode() == null) {
                unassignedMarketSegments.remove(marketSegment);
                assignedMarketSegmentRateCodeHashMap.put(marketSegment.getMarketCode(), marketSegmentRateCodeHashMap.get(marketSegment.getMarketCode()));
            } else {
                List<RateCodeSummary> rateCodeSummaries = assignedMarketSegmentRateCodeHashMap.get(marketSegment.getMarketCode());
                if (rateCodeSummaries == null) {
                    rateCodeSummaries = new ArrayList<RateCodeSummary>();
                }
                rateCodeSummaries.addAll(getAllRateCodesThatMatch(marketSegment.getMarketCode(), marketSegment.getRateCode(),
                        Enum.valueOf(RateCodeTypeEnum.class, marketSegment.getRateCodeType())));
                assignedMarketSegmentRateCodeHashMap.put(marketSegment.getMarketCode(), rateCodeSummaries);

                if (rateCodeSummaries.containsAll(marketSegmentRateCodeHashMap.get(marketSegment.getMarketCode()))) {
                    for (AnalyticalMarketSegmentSummary unassignedMarketSegment : unassignedMarketSegments) {
                        if (unassignedMarketSegment.getMarketCode().equalsIgnoreCase(marketSegment.getMarketCode())) {
                            unassignedMarketSegments.remove(unassignedMarketSegment);
                            break;
                        }
                    }
                }
            }
        }
        reShuffle();
    }

    private List<RateCodeSummary> getAllRateCodesThatMatch(String marketCode, String rateCode, RateCodeTypeEnum rateCodeType) {
        List<RateCodeSummary> rateCodeSummaries = marketSegmentRateCodeHashMap.get(marketCode);
        if (rateCodeType != null) {
            rateCode = rateCodeType.getExpression(rateCode);
        }

        String finalRateCode = rateCode;
        List<RateCodeSummary> filteredList = rateCodeSummaries.stream()
                .filter(item -> getMatcher(rateCodeType, finalRateCode).matches(item.getRateCode()))
                .collect(Collectors.toList());
        return filteredList;
    }

    private ArrayList<RateCodeSummary> createRateCodes(AnalyticalMarketSegmentSummary summary) {
        ArrayList<RateCodeSummary> rateCodeSummaries = new ArrayList<RateCodeSummary>();
        Random rand = new Random(1000);
        double v = rand.nextDouble();
        for (int i = 0; i < 5; i++) {
            RateCodeSummary rateCodeSummary = new RateCodeSummary();
            rateCodeSummary.setRateCode(RandomStringUtils.randomAlphabetic(3).toUpperCase());
            rateCodeSummary.setAverageDailyRate(v * rand.nextInt());
            rateCodeSummary.setPercent(.12 * i);
            rateCodeSummary.setRoomRevenue(v * i);
            rateCodeSummary.setRoomsSold(i * 5);
            rateCodeSummary.setMarketSegmentCount(Integer.valueOf(i));
            rateCodeSummaries.add(rateCodeSummary);
        }
        return rateCodeSummaries;
    }

    public List<RateCodeSummary> getRateCodes(String marketCode) {
        List<RateCodeSummary> rateCodeSummaries = new ArrayList<RateCodeSummary>(marketSegmentRateCodeHashMap.get(marketCode));
        List<RateCodeSummary> assignedRateCodeSummary = assignedMarketSegmentRateCodeHashMap.get(marketCode);
        if (assignedRateCodeSummary != null) {
            rateCodeSummaries.removeAll(assignedRateCodeSummary);
        }
        return rateCodeSummaries;

    }


}
