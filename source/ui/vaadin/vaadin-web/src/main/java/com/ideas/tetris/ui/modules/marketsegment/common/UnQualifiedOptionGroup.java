package com.ideas.tetris.ui.modules.marketsegment.common;

import com.ideas.tetris.pacman.services.marketsegment.service.AnalyticalMarketSegmentAttribute;
import com.ideas.tetris.pacman.services.product.Product;
import com.ideas.tetris.ui.common.TetrisComponentFactory;
import com.ideas.tetris.ui.common.component.TetrisCaption;
import com.ideas.tetris.ui.common.component.TetrisLabel;
import com.ideas.tetris.ui.common.component.select.TetrisComboBoxV8;
import com.ideas.tetris.ui.common.util.UiUtils;
import com.vaadin.shared.ui.ContentMode;
import com.vaadin.ui.Alignment;
import com.vaadin.ui.Component;
import com.vaadin.ui.GridLayout;
import com.vaadin.ui.VerticalLayout;
import org.vaadin.hene.flexibleoptiongroup.v7.FlexibleOptionGroup;
import org.vaadin.hene.flexibleoptiongroup.v7.FlexibleOptionGroupItemComponent;

import java.util.LinkedList;
import java.util.List;

import static com.ideas.tetris.pacman.services.marketsegment.service.AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR;
import static com.ideas.tetris.pacman.services.marketsegment.service.AnalyticalMarketSegmentAttribute.FENCED;
import static com.ideas.tetris.pacman.services.marketsegment.service.AnalyticalMarketSegmentAttribute.FENCED_AND_PACKAGED;
import static com.ideas.tetris.pacman.services.marketsegment.service.AnalyticalMarketSegmentAttribute.PACKAGED;
import static com.ideas.tetris.pacman.services.marketsegment.service.AnalyticalMarketSegmentAttribute.UNFENCED_NON_PACKAGED;
import static com.ideas.tetris.pacman.util.Executor.executeIfFalse;
import static com.ideas.tetris.ui.common.util.UiUtils.getText;
import static java.util.Objects.nonNull;

public class UnQualifiedOptionGroup extends VerticalLayout {

    private static final String TOOLTIP_CLASS_WITH_BOLD_TEXT = "<p class='attributeTooltip'><b>";
    private static final String CLOSING_BOLD_TAG = ":</b> ";

    private List<Product> existingProducts;

    private FlexibleOptionGroup group;
    private GridLayout groupLayout;

    private TetrisComboBoxV8<Product> products;
    private Product selectedProduct;
    private TetrisCaption caption;
    private boolean required;
    private int maxProducts;

    public UnQualifiedOptionGroup(List<Product> existingProducts, int maxIndependentProducts) {
        init()
                .setMaxProducts(maxIndependentProducts)
                .addCaption()
                .setProducts(existingProducts)
                .addProducts()
                .addGroup();
    }

    public UnQualifiedOptionGroup setMaxProducts(int maxIndependentProducts) {
        this.maxProducts = maxIndependentProducts + 1; // Include BAR
        return this;
    }

    private UnQualifiedOptionGroup addProducts() {
        products.setItems(existingProducts);
        products.setItemCaptionGenerator(Product::getName);
        products.setEnabled(false);
        return this;
    }

    private UnQualifiedOptionGroup init() {
        setDescription(
                "<div class='v-errormessage v-errormessage-error'>" + UiUtils.getText("attribute.assignment.warning.selectAttribute") + "</div><br>" +
                        TOOLTIP_CLASS_WITH_BOLD_TEXT + UiUtils.getText("equalToBar") + CLOSING_BOLD_TAG + UiUtils.getText("attribute.equal.to.bar.description") + "</p>" +
                        TOOLTIP_CLASS_WITH_BOLD_TEXT + UiUtils.getText("fenced.caption") + CLOSING_BOLD_TAG + UiUtils.getText("attribute.fenced.description") + "</p>" +
                        TOOLTIP_CLASS_WITH_BOLD_TEXT + UiUtils.getText("packaged") + CLOSING_BOLD_TAG + UiUtils.getText("attribute.packaged.description") + "</p>", ContentMode.HTML
        );
        groupLayout = new GridLayout(3, 5);
        group = new FlexibleOptionGroup();
        products = TetrisComponentFactory.createComboBoxV8("products");
        products.setWidth("150px");
        products.setEmptySelectionAllowed(true);
        products.addValueChangeListener(event -> selectedProduct = products.getValue());
        setSpacing(false);
        setMargin(false);
        return this;
    }

    private UnQualifiedOptionGroup addCaption() {
        this.caption = new TetrisCaption(getText("unqualified.attribute"));
        addComponent(caption);
        return this;
    }

    private UnQualifiedOptionGroup addGroup() {
        this.
                addOptionGroupItem(EQUAL_TO_BAR, getText("equalTo"), 0, products).
                addOptionGroupItem(FENCED, getText("fenced.caption"), 1).
                addOptionGroupItem(PACKAGED, getText("packaged"), 2).
                addOptionGroupItem(FENCED_AND_PACKAGED, getText("fenced.and.packaged.caption"), 3).
                addOptionGroupItem(UNFENCED_NON_PACKAGED, getText("unfenced.and.nonpackaged"), 4);
        group.addValueChangeListener(event -> {
            products.setEnabled(EQUAL_TO_BAR.equals(group.getValue()));
            if (!EQUAL_TO_BAR.equals(group.getValue())) {
                products.setValue(null);
            }
        });
        addComponent(groupLayout);
        return this;
    }

    private UnQualifiedOptionGroup addOptionGroupItem(AnalyticalMarketSegmentAttribute attribute, String caption, int row, Component... components) {
        group.addItem(attribute);
        group.setItemCaption(attribute, caption);
        FlexibleOptionGroupItemComponent itemComponent = group.getItemComponent(attribute);
        itemComponent.addStyleName("condition-option");
        itemComponent.setHeight(26, Unit.PIXELS);
        groupLayout.addComponent(itemComponent, 0, row);
        TetrisLabel label = new TetrisLabel(caption);
        label.setWidth(null);
        groupLayout.addComponent(label, 1, row);
        groupLayout.setComponentAlignment(itemComponent, Alignment.MIDDLE_LEFT);
        groupLayout.setComponentAlignment(label, Alignment.MIDDLE_LEFT);
        if (nonNull(components)) {
            int nextColumn = 2;
            for (Component component : components) {
                groupLayout.addComponent(component, nextColumn++, row);
                groupLayout.setComponentAlignment(label, Alignment.MIDDLE_LEFT);
            }
        }
        return this;
    }

    public UnQualifiedOptionGroup setProducts(List<Product> products) {
        this.existingProducts = products == null ? new LinkedList<>() : new LinkedList<>(products);
        if (existingProducts.size() < maxProducts) {
            Product placeholderToCreateNewProduct = new Product();
            placeholderToCreateNewProduct.setId(-1);
            placeholderToCreateNewProduct.setName(UiUtils.getText("independent.products.new"));
            existingProducts.add(placeholderToCreateNewProduct);
        }
        this.products.setItems(this.existingProducts);
        return this;
    }

    @Override
    public void setEnabled(boolean enabled) {
        super.setEnabled(enabled);
        group.setEnabled(enabled);
        groupLayout.setEnabled(enabled);
        executeIfFalse(enabled, group::unselect, group.getValue());
    }

    public void setRequired(boolean required) {
        this.required = required;
        caption.setRequired(required);
    }

    public void setValue(AnalyticalMarketSegmentAttribute attribute, Object subValue) {
        if (attribute == null) {
            group.unselect(group.getValue());
            products.setValue(null);
        } else {
            group.setValue(attribute);
            if (EQUAL_TO_BAR.equals(attribute) && subValue != null && subValue instanceof Product) {
                products.setValue((Product) subValue);
            }
        }
    }

    public AnalyticalMarketSegmentAttribute getValue() {
        return (AnalyticalMarketSegmentAttribute) group.getValue();
    }

    public boolean isValid() {
        if (!required) {
            return true;
        }
        if (group.getValue() != null) {
            if (!EQUAL_TO_BAR.equals(group.getValue()) || selectedProduct != null) {
                return true;
            }
        }
        return false;
    }

    public boolean shouldCreateNewProduct() {
        return isValid() && EQUAL_TO_BAR.equals(group.getValue()) && selectedProduct.getId() == null;
    }

    public Product getSelectedProduct() {
        return this.selectedProduct;
    }
}
