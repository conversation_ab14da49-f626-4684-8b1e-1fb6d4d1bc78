package com.ideas.tetris.ui.modules.functionspace.forecastreview;

import com.ideas.tetris.ui.common.component.ReadOnlyComponentAware;
import com.ideas.tetris.ui.common.component.ValueChangeEventDisabledAware;
import com.ideas.tetris.ui.common.component.table.TetrisChangeAwareColumnComponentProvider;
import com.ideas.tetris.ui.common.util.ChangeAware;
import com.ideas.tetris.ui.common.util.UiUtils;
import com.vaadin.v7.data.Property;
import com.vaadin.v7.data.util.BeanItem;
import com.vaadin.v7.ui.Field;
import com.vaadin.v7.ui.Table;
import org.apache.commons.lang3.StringUtils;

abstract public class ForecastReviewTreeTableColumnGenerator implements Table.ColumnGenerator {

    abstract public ChangeAware generateComponent(Table source, Object itemId, Object columnId);

    @Override
    public Object generateCell(Table table, Object itemId, Object columnId) {
        ChangeAware component = null;
        BeanItem item = (BeanItem) table.getItem(itemId);
        Property itemProperty = item.getItemProperty(columnId);
        Object bean = item.getBean();

        if (bean instanceof TetrisChangeAwareColumnComponentProvider) {
            component = ((TetrisChangeAwareColumnComponentProvider) bean).getComponent(columnId);
            if (component == null) {
                component = generateComponent(table, itemId, columnId);
                ((TetrisChangeAwareColumnComponentProvider) bean).addComponent(columnId, component);
                if (component instanceof ValueChangeEventDisabledAware) {
                    ((ValueChangeEventDisabledAware) component).setValueChangeEventDisabled(true);
                }

                if (component instanceof Field) {
                    Field field = (Field) component;
                    field.setPropertyDataSource(itemProperty);
                    String columnHeader = table.getColumnHeader(columnId);
                    field.setRequiredError((StringUtils.isEmpty(columnHeader) ? columnHeader : columnHeader.replace("<br />", " ")) + " " + UiUtils.getText("common.isrequired"));
                }

                if (component instanceof ValueChangeEventDisabledAware) {
                    ((ValueChangeEventDisabledAware) component).setValueChangeEventDisabled(false);
                }

                /*
                if(component instanceof Property.ValueChangeNotifier){
                    if (table instanceof TetrisChangeAwareTable) {
                        ((Property.ValueChangeNotifier)component).addValueChangeListener(new Property.ValueChangeListener(){
                            @Override
                            public void valueChange(Property.ValueChangeEvent event) {
                                ((TetrisChangeAwareTable)table).processChange(event);
                            }
                        });
                    }
                }
                else {
                    throw new RuntimeException("Component must implement Property.ValueChangeNotifier for column id "+columnId);
                }*/

//                if (table instanceof TetrisChangeAwareTable) {
//                    boolean hasAllColumnsGenerated = true;
//                    if (!ArrayUtils.isEmpty(table.getVisibleColumns())) {
//                        for (Object propertyId : table.getVisibleColumns()) {
//                            Table.ColumnGenerator columnGenerator = table.getColumnGenerator(propertyId);
//                            if (columnGenerator instanceof TetrisChangeAwareComponentColumnGenerator) {
//                                if (((TetrisChangeAwareColumnComponentProvider) bean).getComponent(propertyId) == null) {
//                                    hasAllColumnsGenerated = false;
//                                    break;
//                                }
//                            }
//                        }
//                    }
//                    if (hasAllColumnsGenerated) {
//                        ((TetrisChangeAwareTable)table).fireRowGenerationCompleteEvent((TetrisChangeAwareColumnComponentProvider)bean);
//                    }
//                    ((TetrisChangeAwareTable)table).addColumnComponentProvider((TetrisChangeAwareColumnComponentProvider) bean);
//                    if(!((TetrisChangeAwareColumnComponentProvider)bean).isPersisted()){
//                        ((TetrisChangeAwareTable)table).processChange(null);
//                    }
//                }
            }
        } else {
            throw new RuntimeException("Bean needs to be an instance of TetrisChangeAwareColumnComponentProvider.");
        }

        if (component instanceof Field) {
            Field field = (Field) component;
            if (table.isReadOnly()) {
                if (field instanceof ReadOnlyComponentAware) {
                    component = ((ReadOnlyComponentAware) field).getReadOnlyComponent();
                }
                field.setReadOnly(true);
            } else {
                field.setReadOnly(((TetrisChangeAwareColumnComponentProvider) bean).isReadOnly());
            }
        }
        return component;
    }
}
