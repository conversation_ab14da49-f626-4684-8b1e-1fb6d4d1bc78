package com.ideas.tetris.ui.modules.functionspace.demandcalendar.calendar;

import com.ideas.tetris.pacman.services.specialevent.dto.SpecialEventSummaryDto;
import com.ideas.tetris.ui.common.component.TetrisBeanItemContainer;
import com.ideas.tetris.ui.common.component.calendar.TetrisCalendar;
import com.ideas.tetris.ui.common.component.calendar.TetrisHeatMapNavigator;
import com.ideas.tetris.ui.common.component.dayselector.DateChangedEvent;
import com.ideas.tetris.ui.common.component.dayselector.DateChangedListener;
import com.ideas.tetris.ui.common.component.dayselector.TetrisMonthSelector;
import com.ideas.tetris.ui.common.component.panel.PanelBar;
import com.ideas.tetris.ui.common.data.util.converter.ScaleAwareStringToBigDecimalConverter;
import com.ideas.tetris.ui.common.util.DateFormatUtil;
import com.ideas.tetris.ui.common.util.UiUtils;
import com.ideas.tetris.ui.modules.functionspace.demandcalendar.FunctionSpaceDemandCalendarPresenter;
import com.ideas.tetris.ui.widget.CustomCalendar;
import com.vaadin.ui.Component;
import com.vaadin.ui.CssLayout;
import com.vaadin.ui.Panel;
import com.vaadin.v7.shared.ui.label.ContentMode;
import com.vaadin.v7.ui.HorizontalLayout;
import com.vaadin.v7.ui.Label;
import com.vaadin.v7.ui.VerticalLayout;
import com.vaadin.v7.ui.components.calendar.CalendarComponentEvents;
import org.joda.time.LocalDate;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

public class FunctionSpaceDemandCalendarComponent extends VerticalLayout {

    private FunctionSpaceDemandCalendarPresenter presenter;
    private TetrisCalendar tetrisCalendar;
    private TetrisMonthSelector monthSelector;
    private LocalDate selectedMonth;
    private Map<LocalDate, DemandCalendarUIWrapper> calendarData;
    private TetrisBeanItemContainer<FunctionSpaceDemandCalendarEvent> container;

    public static final int DEFAULT_HEATMAP_CALENDAR_COUNT = 6;
    private static final String METRIC_STYLE = "metric";
    private static final String EVALUATE_STYLE = "evaluate";

    private boolean evaluateClickFlag = false;
    private PanelBar panelBar;
    private TetrisHeatMapNavigator heatMapComponent;

    public FunctionSpaceDemandCalendarComponent(final FunctionSpaceDemandCalendarPresenter presenter) {
        this.presenter = presenter;

        setSizeFull();
        setMargin(true);
        setSpacing(true);

        addHeatMapPanel();
        addMonthSelector();

        VerticalLayout verticalLayout = new VerticalLayout();
        addCalendar(verticalLayout);
        Panel panel = new Panel();
        panel.setContent(verticalLayout);
        panel.setSizeFull();
        addComponent(panel);

        addCalendarLegend();
        setExpandRatio(panel, 1);
    }

    private void addHeatMapPanel() {
        panelBar = new PanelBar(PanelBar.PanelBarPosition.HEADER);
        panelBar.setTitle(UiUtils.getText("groupPricing.demandCalendar.calendarHeatmap"));
        panelBar.setContent(addHeatMap());
        panelBar.setOpen(true);

        addComponent(panelBar);
    }

    private Component addHeatMap() {
        HorizontalLayout heatMapLayout = new HorizontalLayout();
        heatMapLayout.setHeight(130, Unit.PIXELS);
        heatMapLayout.setWidth(100, Unit.PERCENTAGE);

        heatMapComponent = new TetrisHeatMapNavigator(DEFAULT_HEATMAP_CALENDAR_COUNT) {
            @Override
            protected void onDateClicked(LocalDate date) {
                presenter.changeCalendarMonth(date);
            }

            @Override
            protected String getStyle(LocalDate date) {
                return presenter.getStyleForHeatMapDate(date);
            }

            @Override
            protected String getToolTip(LocalDate date) {
                return presenter.getToolTipForHeatMapDate(date);
            }
        };
        heatMapComponent.addStyleName("functionspace-heatmap");
        heatMapLayout.addComponent(heatMapComponent);

        return heatMapLayout;
    }

    private void addCalendarLegend() {
        CssLayout grid = new CssLayout();

        grid.setWidth(100, Unit.PERCENTAGE);
        grid.addStyleName("functionspace-legend");

        Label specialEvent = new Label(" " + UiUtils.getText("groupPricing.demandCalendar.specialEvent"));
        specialEvent.addStyleName("specialEvent");
        grid.addComponent(specialEvent);

        Label forecastOverride = new Label(" " + UiUtils.getText("forecast"));
        forecastOverride.addStyleName("forecastOverride");
        grid.addComponent(forecastOverride);

        Label ufHigh = new Label(" " + UiUtils.getText("groupPricing.demandCalendar.ufHigh"));
        ufHigh.addStyleName("ufHigh");
        grid.addComponent(ufHigh);

        Label ufMedium = new Label(" " + UiUtils.getText("groupPricing.demandCalendar.ufMedium"));
        ufMedium.addStyleName("ufMedium");
        grid.addComponent(ufMedium);

        Label ufLow = new Label(" " + UiUtils.getText("groupPricing.demandCalendar.ufLow"));
        ufLow.addStyleName("ufLow");
        grid.addComponent(ufLow);

        Label systemDate = new Label(" " + UiUtils.getText("systemDate"));
        systemDate.addStyleName("systemDate");
        grid.addComponent(systemDate);

        String actualUtilization = " " + UiUtils.getText("groupPricing.demandCalendar.actualUtilization");
        Label au = new Label("<span style='font-weight:bold;'>UTIL</span>:" + actualUtilization, ContentMode.HTML);
        au.addStyleName("legendItem");
        grid.addComponent(au);

        String utilizationForecast = " " + UiUtils.getText("groupEvaluation.utilization.forecast");
        Label uf = new Label("<span style='font-weight:bold;'>UF</span>:" + utilizationForecast, ContentMode.HTML);
        uf.addStyleName("legendItem");
        grid.addComponent(uf);

        String onBooksUtilization = " " + UiUtils.getText("groupPricing.demandCalendar.UtilizationOnbooks");
        Label onBooks = new Label("<span style='font-weight:bold;'>" + UiUtils.getText("common.OBU") + "</span>:" + onBooksUtilization, ContentMode.HTML);
        onBooks.addStyleName("legendItem");
        grid.addComponent(onBooks);

        addComponent(grid);
    }

    private void addCalendar(VerticalLayout verticalLayout) {
        container = new TetrisBeanItemContainer<FunctionSpaceDemandCalendarEvent>(FunctionSpaceDemandCalendarEvent.class);

        tetrisCalendar = new TetrisCalendar();
        tetrisCalendar.setContainerDataSource(container);
        tetrisCalendar.addStyleName("functionspace-demandcalendar");
        tetrisCalendar.setSizeFull();
        tetrisCalendar.setCellStyleGenerator(new CustomCalendar.CellStyleGenerator() {
            @Override
            public String getStyle(Date date) {
                LocalDate localDate = new LocalDate(date);
                String style = null;
                if (selectedMonth != null && calendarData != null) {
                    DemandCalendarUIWrapper demandCalendarUIWrapper = calendarData.get(localDate);
                    if (demandCalendarUIWrapper != null) {
                        style = demandCalendarUIWrapper.getForecastLevel().getStyleName();
                    }
                }
                return style;
            }
        });
        tetrisCalendar.setHandler(new CalendarComponentEvents.DateClickHandler() {
            @Override
            public void dateClick(CalendarComponentEvents.DateClickEvent event) {
                showSummaryPopup(event.getDate());
            }
        });

        tetrisCalendar.setHandler(new CalendarComponentEvents.EventClickHandler() {
            @Override
            public void eventClick(CalendarComponentEvents.EventClick event) {
                FunctionSpaceDemandCalendarEvent calendarEvent = (FunctionSpaceDemandCalendarEvent) event.getCalendarEvent();
                if (calendarEvent.isEvaluate()) {
                    evaluateClickFlag = true;
                    goToEvaluateScreen(calendarEvent.getStart());
                }
            }
        });
        tetrisCalendar.addDateClickListener(new CustomCalendar.DateClickListener() {
            @Override
            public void dateClicked(CustomCalendar.DateClickEvent event) {
                if (evaluateClickFlag) {
                    evaluateClickFlag = false;
                    return;
                }

                showSummaryPopup(event.getDate());
            }
        });
        verticalLayout.addComponent(tetrisCalendar);
    }

    private void addMonthSelector() {
        monthSelector = new TetrisMonthSelector();
        monthSelector.addDayChangedEventListener(new DateChangedListener() {
            @Override
            public void dayChanged(DateChangedEvent dayChangedEvent) {
                presenter.onMonthSelected(dayChangedEvent.getSelectedDay());
            }
        });
        addComponent(monthSelector);
    }

    private void showSummaryPopup(Date date) {
        DemandCalendarUIWrapper demandCalendarUIWrapper = calendarData.get(new LocalDate(date));
        FunctionSpaceCalendarSummaryComponent.show(date, presenter, demandCalendarUIWrapper);
    }

    private void goToEvaluateScreen(Date date) {
        FunctionSpaceEvaluateLinkUtil.goToEvaluation(date);
    }

    public void updateHeatMap(LocalDate startDate) {
        heatMapComponent.setStartDate(startDate);
    }

    public void updateCalendar(LocalDate selectedMonth, Map<LocalDate, DemandCalendarUIWrapper> calendarData) {
        this.calendarData = calendarData;
        this.selectedMonth = selectedMonth;
        tetrisCalendar.setSystemDate(presenter.getSystemDateAsLocalDate().toDate());

        monthSelector.updateMonth(selectedMonth.dayOfMonth().withMinimumValue());
        tetrisCalendar.setDate(selectedMonth);

        ArrayList<FunctionSpaceDemandCalendarEvent> eventList = new ArrayList<FunctionSpaceDemandCalendarEvent>();

        //loop
        if (calendarData != null) {
            for (LocalDate localDate : calendarData.keySet()) {
                DemandCalendarUIWrapper demandCalendarUIWrapper = calendarData.get(localDate);
                Date date = localDate.toDate();

                //if Actual Utilization value is not null, then this means the date is before the system/caught-up date
                //actual utilization is not null, could be because of mock data, right now we don't know how NGI is going to populate this
                //so adding SystemDateAfter check
                if (demandCalendarUIWrapper.getActualUtilization() != null && presenter.isPast(localDate)) {
                    //When date is before system date, we only show the Util metric
                    FunctionSpaceDemandCalendarEvent actualUtilizationEvent = new FunctionSpaceDemandCalendarEvent("UTIL:" + new ScaleAwareStringToBigDecimalConverter()
                            .convertToPresentation(demandCalendarUIWrapper.getActualUtilization(), String.class, UiUtils.getLocale()) + "%", "", date);
                    actualUtilizationEvent.setStyleName(METRIC_STYLE);
                    eventList.add(actualUtilizationEvent);
                } else {
                    //When the date is on or after system date, we show multiple metrics plus an evaluate button
                    //Evaluate
                    FunctionSpaceDemandCalendarEvent evaluateEvent;
                    if (demandCalendarUIWrapper.getStatus().getName() == "Evaluate") {
                        evaluateEvent = new FunctionSpaceDemandCalendarEvent(UiUtils.getText("EVALUATE"), "", date);
                    } else if (demandCalendarUIWrapper.getStatus().getName() == "Open") {
                        evaluateEvent = new FunctionSpaceDemandCalendarEvent(UiUtils.getText("OPEN"), "", date);
                    } else {
                        evaluateEvent = new FunctionSpaceDemandCalendarEvent(UiUtils.getText("CLOSED"), "", date);
                    }
                    //FunctionSpaceDemandCalendarEvent evaluateEvent = new FunctionSpaceDemandCalendarEvent(demandCalendarUIWrapper.getStatus().getName(),"",date);
                    evaluateEvent.setStyleName(EVALUATE_STYLE);
                    evaluateEvent.setEvaluate(true);
                    eventList.add(evaluateEvent);

                    //OBU
                    FunctionSpaceDemandCalendarEvent obuEvent = new FunctionSpaceDemandCalendarEvent(UiUtils.getText("common.OBU") + ":" + new ScaleAwareStringToBigDecimalConverter()
                            .convertToPresentation(demandCalendarUIWrapper.getOnBooksUtilization(), String.class, UiUtils.getLocale()) + "%", "", date);
                    obuEvent.setStyleName(METRIC_STYLE);
                    eventList.add(obuEvent);

                    //UF
                    FunctionSpaceDemandCalendarEvent ufEvent = new FunctionSpaceDemandCalendarEvent("UF:" + new ScaleAwareStringToBigDecimalConverter()
                            .convertToPresentation(demandCalendarUIWrapper.getUtilizationForecast(), String.class, UiUtils.getLocale()) + "%", "", date);
                    ufEvent.setStyleName(METRIC_STYLE);
                    eventList.add(ufEvent);
                }

                //always add in the special event and override event, even if the date contains neither to keep the positioning across the cells consistent
                FunctionSpaceDemandCalendarEvent specialOrOverrideEvent = new FunctionSpaceDemandCalendarEvent("", "", date);

                if (demandCalendarUIWrapper.isHasSpecialEvent() && demandCalendarUIWrapper.isHasOverride()) {
                    specialOrOverrideEvent.setStyleName("specialAndOverride");
                    specialOrOverrideEvent.setDescription(getToolTipForSpecialEvents(demandCalendarUIWrapper));
                } else if (demandCalendarUIWrapper.isHasSpecialEvent()) {
                    specialOrOverrideEvent.setStyleName("special");
                    specialOrOverrideEvent.setDescription(getToolTipForSpecialEvents(demandCalendarUIWrapper));
                } else if (demandCalendarUIWrapper.isHasOverride()) {
                    specialOrOverrideEvent.setStyleName("override");
                } else {
                    //no special event or override for this date, so just style it like we do the other metrics
                    specialOrOverrideEvent.setStyleName(METRIC_STYLE);
                }
                eventList.add(specialOrOverrideEvent);
            }
        }
        container.removeAllItems();
        container.addAll(eventList);
    }

    private String getToolTipForSpecialEvents(DemandCalendarUIWrapper demandCalendarUIWrapper) {
        List<SpecialEventSummaryDto> specialEvents = demandCalendarUIWrapper.getSpecialEvents();
        String toolTip = "";

        int i = 0;
        for (SpecialEventSummaryDto specialEvent : specialEvents) {
            toolTip += "<b>" + specialEvent.getEventName() + "</b><br>";
            LocalDate startDate = new LocalDate(specialEvent.getStartDate().getTime());
            LocalDate endDate = new LocalDate(specialEvent.getEndDate().getTime());
            toolTip += DateFormatUtil.formatShort(startDate) + " - " + DateFormatUtil.formatShort(endDate) + "<br>";
            toolTip += SpecialEventLayout.getSpecialEventDescription(specialEvent);
            //impacts function space and guest rooms
            i++;
            if (i < specialEvents.size()) {
                toolTip += "<hr>";
            }
        }
        return toolTip;
    }
}
