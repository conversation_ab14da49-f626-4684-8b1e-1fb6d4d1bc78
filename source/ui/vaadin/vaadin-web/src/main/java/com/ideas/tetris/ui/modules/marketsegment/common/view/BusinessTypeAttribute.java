package com.ideas.tetris.ui.modules.marketsegment.common.view;

import static com.ideas.tetris.ui.common.util.UiUtils.getText;

public enum BusinessTypeAttribute {
    GROUP("group", "attribute.group.business.description"),
    UNQUALIFIED("unqualified", "attribute.unqualified.description"),
    QUALIFIED_NON_BLOCK("qualified.nonblock.caption", "attribute.qualified.description"),
    TRANSIENT_BLOCK("common.transient.block", "attribute.transient.description");

    private final String caption;
    private final String description;

    BusinessTypeAttribute(String caption, String description) {
        this.caption = caption;
        this.description = description;
    }

    public String getCaption() {
        return getText(caption);
    }

    public String getDescription() {
        return getText(description);
    }
}
