package com.ideas.tetris.ui.modules.functionspace.performancetrend;

import com.ideas.tetris.pacman.services.functionspace.configuration.entity.FunctionSpaceDayPart;
import org.joda.time.LocalDate;

public class PerformanceTrendWrapper {
    private LocalDate startDate;
    private LocalDate endDate;
    private FunctionSpaceDayPart dayPart;

    public FunctionSpaceDayPart getDayPart() {
        return dayPart;
    }

    public void setDayPart(FunctionSpaceDayPart dayPart) {
        this.dayPart = dayPart;
    }

    public LocalDate getEndDate() {
        return endDate;
    }

    public void setEndDate(LocalDate endDate) {
        this.endDate = endDate;
    }

    public LocalDate getStartDate() {
        return startDate;
    }

    public void setStartDate(LocalDate startDate) {
        this.startDate = startDate;
    }
}
