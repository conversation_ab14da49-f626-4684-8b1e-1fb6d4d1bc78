package com.ideas.tetris.ui.modules.functionspace.configuration;

import com.ideas.infra.tetris.security.domain.TetrisPermissionKey;
import com.ideas.tetris.pacman.services.eventaggregator.SyncEvent;
import com.ideas.tetris.pacman.services.eventaggregator.SyncEventAggregatorService;
import com.ideas.tetris.pacman.services.functionspace.configuration.entity.FunctionSpaceStatus;
import com.ideas.tetris.pacman.services.functionspace.configuration.service.FunctionSpaceConfigurationService;
import com.ideas.tetris.platform.common.xml.schema.workcontext.v1.WorkContextType;
import com.ideas.tetris.ui.common.cdi.TetrisPresenter;

import javax.inject.Inject;
import java.util.List;

import static java.util.stream.Collectors.toList;
import org.springframework.beans.factory.annotation.Autowired;
import com.ideas.tetris.spring.SpringAutowired;

@SpringAutowired
public class FunctionSpaceConfigurationStatusPresenter extends TetrisPresenter<FunctionSpaceConfigurationStatusView, Boolean> {

    @Autowired
	private FunctionSpaceConfigurationService functionSpaceConfigurationService;

    @Autowired
	private SyncEventAggregatorService syncEventAggregatorService;
    private boolean isGroupPricing = true;

    @Override
    public void onViewOpened(Boolean isGroupPricing) {
        this.isGroupPricing = isGroupPricing;
        super.onViewOpened(isGroupPricing);
        loadFunctionSpaceData();
    }

    @Override
    public void onWorkContextChange(WorkContextType workContextType) {
        super.onWorkContextChange(workContextType);
        loadFunctionSpaceData();
    }

    public void save(List<FunctionSpaceStatus> functionSpaceConfigStatusDTO) {
        updateStatusCodeTabPermission();
        showSaveSuccessMessage();
        functionSpaceConfigurationService.saveFunctionSpaceStatuses(functionSpaceConfigStatusDTO);
    }

    public void reset(List<FunctionSpaceStatus> functionSpaceConfigStatusDTO) {
        loadFunctionSpaceData();
    }

    private void loadFunctionSpaceData() {
        List<FunctionSpaceStatus> dto = functionSpaceConfigurationService.getFunctionSpaceStatuses();
        view.refresh(dto);
        updateStatusCodeTabPermission();
    }

    private void updateStatusCodeTabPermission() {
        if (isGroupPricing) {
            view.updatePermission(TetrisPermissionKey.FUNCTION_GROUP_PRICING_CONFIG_STATUS_CODES);
        } else {
            view.updatePermission(TetrisPermissionKey.FUNCTION_SPACE_CONFIGURATION_STATUS_CODES);
        }
    }

    public boolean shouldEnableProspectOrTentative(FunctionSpaceStatus dto) {
        return !dto.isInventoryDeducted() && !dto.isFinal();
    }

    public boolean shouldEnableFinalOrInventoryDeduct(FunctionSpaceStatus dto) {
        return !dto.isProspect() && !dto.isTentative();
    }

    public List<FunctionSpaceStatus> findUpdatedStatuses(List<FunctionSpaceStatus> functionSpaceConfigStatusDTO) {
        List<FunctionSpaceStatus> functionSpaceDataDB = functionSpaceConfigurationService.getFunctionSpaceStatuses();
        return functionSpaceConfigStatusDTO.stream()
                .filter(inputStatus -> functionSpaceDataDB.stream().anyMatch(statusInDB -> isUpdated(inputStatus, statusInDB)))
                .collect(toList());
    }

    private static boolean isUpdated(FunctionSpaceStatus first, FunctionSpaceStatus second) {
        return first.getStatusCode().equals(second.getStatusCode()) && (first.isFinal() != second.isFinal() || first.isTentative() != second.isTentative() || first.isProspect() != second.isProspect() || first.isInventoryDeducted() != second.isInventoryDeducted());
    }

    @Override
    public void sync() {
        syncEventAggregatorService.registerSyncEvent(SyncEvent.FUNCTION_SPACE_CONFIG_CHANGED);
        super.sync();
    }
}
