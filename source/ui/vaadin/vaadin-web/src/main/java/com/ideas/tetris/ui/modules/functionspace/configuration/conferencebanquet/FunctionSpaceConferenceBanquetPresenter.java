package com.ideas.tetris.ui.modules.functionspace.configuration.conferencebanquet;


import com.ideas.tetris.pacman.common.configparams.GUIConfigParamName;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.TenantStatusEnum;
import com.ideas.tetris.pacman.services.functionspace.configuration.entity.FunctionSpaceResourceType;
import com.ideas.tetris.pacman.services.functionspace.configuration.service.FunctionSpaceConfigurationService;
import com.ideas.tetris.pacman.services.grouppricing.configuration.dto.ConferenceAndBanquetDto;
import com.ideas.tetris.pacman.services.grouppricing.configuration.service.ConferenceAndBanquetService;
import com.ideas.tetris.pacman.util.BigDecimalUtil;
import com.ideas.tetris.platform.common.xml.schema.workcontext.v1.WorkContextType;
import com.ideas.tetris.spring.SpringAutowired;
import com.ideas.tetris.ui.common.cdi.TetrisPresenter;
import com.ideas.tetris.ui.common.util.mock.MockData;
import org.apache.commons.lang.math.RandomUtils;
import org.springframework.beans.factory.annotation.Autowired;

import javax.inject.Inject;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@SpringAutowired
public class FunctionSpaceConferenceBanquetPresenter extends TetrisPresenter<FunctionSpaceConferenceBanquetView, Boolean> {

    @Autowired
    FunctionSpaceConfigurationService configurationService;

    @Autowired
    ConferenceAndBanquetService conferenceAndBanquetService;

    @Inject
    MockData mockData;
    private FunctionSpaceResourceType otherResourceType;
    private FunctionSpaceResourceType rentalResourceType;
    private List<ConferenceAndBanquetDto> conferenceAndBanquets;


    @Override
    public void onWorkContextChange(WorkContextType workContextType) {
        super.onWorkContextChange(workContextType);
        initData();
    }

    @Override
    public void onViewOpened(Boolean isGroupPricing) {
        initData();
    }

    private void initData() {
        otherResourceType = configurationService.findFunctionSpaceResourceType(FunctionSpaceResourceType.OTHER_RESOURCE_TYPE);
        rentalResourceType = configurationService.findFunctionSpaceResourceType(FunctionSpaceResourceType.RENTAL_RESOURCE_TYPE);

        List<FunctionSpaceConferenceBanquetUiWrapper> conferenceAndBanquets = loadRevenueStreams();
        view.initRevenueStreamTable(conferenceAndBanquets);
    }

    private List<FunctionSpaceConferenceBanquetUiWrapper> loadRevenueStreams() {
        if (mockModeEnabled) {
            conferenceAndBanquets = new ArrayList<>();
            conferenceAndBanquets.add(mock_newConferenceAndBanquet());
            conferenceAndBanquets.add(mock_newConferenceAndBanquet());
            conferenceAndBanquets.add(mock_newConferenceAndBanquet());
            conferenceAndBanquets.add(mock_newConferenceAndBanquet());
            conferenceAndBanquets.add(mock_newConferenceAndBanquet());
            conferenceAndBanquets.add(mock_newConferenceAndBanquet());
            conferenceAndBanquets.add(mock_newConferenceAndBanquet());
        } else {
            conferenceAndBanquets = getConferenceAndBanquetDtos();
        }

        List<FunctionSpaceConferenceBanquetUiWrapper> wrapperList;
        wrapperList = new ArrayList<>();
        for (int i = 0; i < conferenceAndBanquets.size(); i++) {
            ConferenceAndBanquetDto conferenceAndBanquet = conferenceAndBanquets.get(i);
            FunctionSpaceConferenceBanquetUiWrapper functionSpaceConferenceBanquetUiWrapper = new FunctionSpaceConferenceBanquetUiWrapper();
            functionSpaceConferenceBanquetUiWrapper.setConferenceAndBanquetDto(conferenceAndBanquet);
            functionSpaceConferenceBanquetUiWrapper.setRentalResourceType(rentalResourceType);
            functionSpaceConferenceBanquetUiWrapper.setOtherResourceType(otherResourceType);

            wrapperList.add(functionSpaceConferenceBanquetUiWrapper);
        }

        return wrapperList;
    }

    private List<ConferenceAndBanquetDto> getConferenceAndBanquetDtos() {
        return conferenceAndBanquetService.getFunctionSpaceRevenueGroups().stream()
                .map(conferenceAndBanquetDto -> {
                    conferenceAndBanquetDto.setProfitPercentage(BigDecimalUtil.divide(conferenceAndBanquetDto.getProfitPercentage(), BigDecimalUtil.ONE_HUNDRED));
                    return conferenceAndBanquetDto;
                })
                .collect(Collectors.toList());
    }

    private int count = 1;

    private ConferenceAndBanquetDto mock_newConferenceAndBanquet() {
        ConferenceAndBanquetDto dto = new ConferenceAndBanquetDto();
        dto.setId(count++);
        dto.setPropertyId(uiContext.getPropertyId());
        dto.setProfitPercentage(new BigDecimal(RandomUtils.nextInt(100)));
        dto.setRevenueStream(mockData.getRandomPhraseExact(2));
        return dto;
    }

    public void save() {
        //loop over our streams and save them
        List<ConferenceAndBanquetDto> conferenceAndBanquetsToSave = getConferenceAndBanquetsToSave();
        List<ConferenceAndBanquetDto> conferenceAndBanquetsToDelete = getConferenceAndBanquetsToDelete();
        delete(conferenceAndBanquetsToDelete);
        saveConferenceAndBanquets(conferenceAndBanquetsToSave);
    }

    private List<ConferenceAndBanquetDto> getConferenceAndBanquetsToSave() {
        if (shouldDisplayRevenueStreamDeleteButton()) {
            return conferenceAndBanquets.stream()
                    .filter(ConferenceAndBanquetDto::isActive)
                    .collect(Collectors.toList());
        }

        return conferenceAndBanquets;
    }

    private List<ConferenceAndBanquetDto> getConferenceAndBanquetsToDelete() {
        if (shouldDisplayRevenueStreamDeleteButton()) {
            return conferenceAndBanquets.stream()
                    .filter(conferenceAndBanquetDto -> Objects.equals(TenantStatusEnum.DELETED.getId(), conferenceAndBanquetDto.getStatusId()))
                    .collect(Collectors.toList());
        }

        return Collections.emptyList();
    }

    private void saveConferenceAndBanquets(List<ConferenceAndBanquetDto> conferenceAndBanquetsToSave) {
        conferenceAndBanquetService.saveFunctionSpaceRevenueGroups(conferenceAndBanquetsToSave);
        sync();
        showSaveSuccessMessage();
        initData();
    }

    private void delete(List<ConferenceAndBanquetDto> conferenceAndBanquetsToDelete) {
        conferenceAndBanquetsToDelete.forEach(conferenceAndBanquetDto -> {
            conferenceAndBanquetService.delete(conferenceAndBanquetDto);
        });
    }

    public boolean isRevenueStreamUsedInPackageConfiguration(ConferenceAndBanquetDto conferenceAndBanquet) {
        return isPackageEnabled() &&
                conferenceAndBanquetService.arePackageElementsConfiguredUsingConfBanqDtos(List.of(conferenceAndBanquet));
    }

    private boolean isPackageEnabled() {
        return pacmanConfigParamsService.getBooleanParameterValue(GUIConfigParamName.IS_FUNCTION_SPACE_PACKAGE_ENABLED);
    }

    public boolean shouldDisplayRevenueStreamDeleteButton() {
        return conferenceAndBanquetService.shouldUseFSRevenueStreams() &&
                pacmanConfigParamsService.getBooleanParameterValue(GUIConfigParamName.ALLOW_CONFERENCE_BANQUET_REVENUE_STREAM_DELETION_IN_FS);
    }

    public void cancel() {
        initData();
    }
}
