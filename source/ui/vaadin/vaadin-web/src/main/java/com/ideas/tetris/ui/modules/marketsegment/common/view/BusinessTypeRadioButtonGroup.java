package com.ideas.tetris.ui.modules.marketsegment.common.view;

import com.ideas.tetris.ui.common.util.UiUtils;
import com.ideas.tetris.ui.modules.marketsegment.common.AmsView;

import java.util.Collection;
import java.util.LinkedList;

import static com.ideas.tetris.pacman.util.Runner.runIfTrue;
import static com.ideas.tetris.ui.modules.marketsegment.common.view.BusinessTypeAttribute.GROUP;
import static com.ideas.tetris.ui.modules.marketsegment.common.view.BusinessTypeAttribute.QUALIFIED_NON_BLOCK;
import static com.ideas.tetris.ui.modules.marketsegment.common.view.BusinessTypeAttribute.TRANSIENT_BLOCK;
import static com.ideas.tetris.ui.modules.marketsegment.common.view.BusinessTypeAttribute.UNQUALIFIED;

public class BusinessTypeRadioButtonGroup extends MarketSegmentAttributionRadioButtonGroup<BusinessTypeAttribute> {

    private LinkedList<BusinessTypeAttribute> items;

    public BusinessTypeRadioButtonGroup(AttributeAssignmentConfig config) {
        super("businessTypeOptionGroup", UiUtils.getText("business.type"), config);
        setRequiredIndicatorVisible(true);
        runIfTrue(items.size() == 1, () -> setSelectedItem(items.get(0)));
    }

    @Override
    protected String getCaption(BusinessTypeAttribute item) {
        return item.getCaption();
    }

    @Override
    protected Collection<BusinessTypeAttribute> getItems() {
        this.items = new LinkedList<>();
        if (getConfig().isGroupOptionEnabled()) {
            items.add(GROUP);
        }
        if (AmsView.AMS_VIEW_GROUP != getConfig().getAmsView()) {
            items.add(UNQUALIFIED);
            items.add(QUALIFIED_NON_BLOCK);
            if (getConfig().isGroupOptionEnabled()) {
                items.add(TRANSIENT_BLOCK);
            }
        }
        return items;
    }

    @Override
    public String getDescription() {
        return DescriptionBuilder.buildForBusinessTypeAttributes(items);
    }
}
