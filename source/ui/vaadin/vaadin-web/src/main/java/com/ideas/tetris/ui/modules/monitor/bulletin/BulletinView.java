package com.ideas.tetris.ui.modules.monitor.bulletin;

import com.ideas.infra.tetris.security.domain.TetrisPermissionKey;
import com.ideas.tetris.pacman.services.problem.dto.Bulletin;
import com.ideas.tetris.pacman.services.problem.entity.SupportBulletinAction;
import com.ideas.tetris.ui.common.cdi.TetrisView;
import com.ideas.tetris.ui.common.component.TetrisBeanItemContainer;
import com.ideas.tetris.ui.common.component.TetrisSaveCancelButtonBar;
import com.ideas.tetris.ui.common.component.button.TetrisButton;
import com.ideas.tetris.ui.common.component.button.TetrisButtonBar;
import com.ideas.tetris.ui.common.component.button.TetrisImageButton;
import com.ideas.tetris.ui.common.component.button.TetrisLinkButton;
import com.ideas.tetris.ui.common.component.filter.TetrisFilterPopup;
import com.ideas.tetris.ui.common.component.fontawesome.TetrisFontAwesome;
import com.ideas.tetris.ui.common.component.label.TetrisSpacer;
import com.ideas.tetris.ui.common.component.select.TetrisComboBox;
import com.ideas.tetris.ui.common.component.table.TetrisTable;
import com.ideas.tetris.ui.common.component.table.TetrisTableColumnPopupViewGenerator;
import com.ideas.tetris.ui.common.component.textfield.TetrisTextArea;
import com.ideas.tetris.ui.common.component.textfield.TetrisTextField;
import com.ideas.tetris.ui.common.component.window.TetrisWindow;
import com.ideas.tetris.ui.common.data.fieldgroup.TetrisBeanFieldGroup;
import com.vaadin.annotations.Title;
import com.vaadin.ui.Button;
import com.vaadin.ui.Component;
import com.vaadin.ui.FormLayout;
import com.vaadin.ui.Window;
import com.vaadin.v7.data.Property;
import com.vaadin.v7.ui.AbstractSelect;
import com.vaadin.v7.ui.HorizontalLayout;
import com.vaadin.v7.ui.Table;
import com.vaadin.v7.ui.VerticalLayout;

import java.util.Arrays;
import java.util.List;

@SuppressWarnings("serial")
@Title("Solutions")
public class BulletinView extends TetrisView<BulletinPresenter, Void> {
    private static final String ACTION_COLUMN_ID = "ActionColumn";

    private TetrisBeanItemContainer<Bulletin> bulletinContainer;
    private TetrisWindow bulletinWindow;
    private TetrisTable table;
    private TetrisButtonBar buttonBar;
    private TetrisFilterPopup filterPopup;
    private BulletinFilterView bulletinFilterView;
    private TetrisBeanFieldGroup<Bulletin> bulletinFieldGroup;

    @Override
    protected void initView() {
        setSizeFull();
        VerticalLayout layout = new VerticalLayout();
        layout.setMargin(true);
        layout.setSpacing(true);
        layout.setSizeFull();

        addHeader(layout);
        addTable(layout);

        setCompositionRoot(layout);
    }

    private void addTable(VerticalLayout layout) {
        bulletinContainer = new TetrisBeanItemContainer<Bulletin>(Bulletin.class);
        table = new TetrisTable();
        table.setColumnCollapsingAllowed(true);
        table.setContainerDataSource(bulletinContainer);
        table.setDisplayRowCount(true);
        table.setSizeFull();

        table.addGeneratedColumn("text", new TetrisTableColumnPopupViewGenerator("text", 50));

        table.addGeneratedColumn(ACTION_COLUMN_ID, new Table.ColumnGenerator() {
            @Override
            public Object generateCell(Table source, final Object itemId, Object columnId) {
                HorizontalLayout horizontalLayout = new HorizontalLayout();
                horizontalLayout.setSpacing(true);

                String buttonLabel = getText("OPEN");
                if (((Bulletin) itemId).isActive()) {
                    TetrisLinkButton edit = new TetrisLinkButton(getText("common.edit"), new Button.ClickListener() {
                        @Override
                        public void buttonClick(Button.ClickEvent event) {
                            showBulletinPopUp((Bulletin) itemId);
                        }
                    });
                    edit.setEnabledRequirements(true, TetrisPermissionKey.ALLOWS_TO_CONFIGURE_ANY_TYPE_OF_SOLUTION_TO_ANY_ISSUES);
                    horizontalLayout.addComponent(edit);
                    buttonLabel = getText("close");
                }
                TetrisLinkButton openClose = new TetrisLinkButton(buttonLabel, new Button.ClickListener() {
                    @Override
                    public void buttonClick(Button.ClickEvent event) {
                        presenter.openCloseBulletin((Bulletin) itemId);
                    }
                });
                openClose.setEnabledRequirements(true, TetrisPermissionKey.ALLOWS_TO_CONFIGURE_ANY_TYPE_OF_SOLUTION_TO_ANY_ISSUES);
                horizontalLayout.addComponent(openClose);

                return horizontalLayout;
            }
        });

        table.setVisibleColumns(new Object[]{
                "action"
                , "text"
                , "jobName"
                , "stepName"
                , "contactPerson"
                , "estimatedResolutionTime"
                , "lastModifiedDate"
                , "lastModifiedBy"
                , "closedDate"
                , "closedBy"
                , ACTION_COLUMN_ID
        });

        table.setColumnHeaders(new String[]{
                "Action"
                , getText("common.details")
                , getText("common.jobName")
                , getText("common.stepName")
                , getText("monitor.bulletin.contact.person")
                , getText("monitor.bulletin.estimated.resolution.time")
                , getText("updatedOn")
                , getText("common.updatedBy")
                , getText("common.closedOn")
                , getText("common.closedBy")
                , getText("actions")
        });

//      table.setColumnWidth("text", 100);
        table.setColumnWidth("action", 100);
        table.setColumnWidth("jobName", 150);
        table.setColumnWidth("stepName", 200);
        table.setColumnWidth("contactPerson", 150);
        table.setColumnWidth("estimatedResolutionTime", 200);
        table.setColumnWidth("lastModifiedDate", 115);
        table.setColumnWidth("closedDate", 115);
        table.setColumnWidth("lastModifiedBy", 150);
        table.setColumnWidth("closedBy", 150);
        table.setColumnWidth(ACTION_COLUMN_ID, 100);

        layout.addComponent(table);
        layout.setExpandRatio(table, 1.0F);
    }

    private void updateTableColumnVisibility(BulletinFilterEnum filterEnum) {
        if (filterEnum.equals(BulletinFilterEnum.OPEN)) {
            table.setColumnCollapsed("lastModifiedBy", false);
            table.setColumnCollapsed("lastModifiedDate", false);
            table.setColumnCollapsed("closedBy", true);
            table.setColumnCollapsed("closedDate", true);
        } else if (filterEnum.equals(BulletinFilterEnum.CLOSED)) {
            table.setColumnCollapsed("closedBy", false);
            table.setColumnCollapsed("closedDate", false);
            table.setColumnCollapsed("lastModifiedBy", true);
            table.setColumnCollapsed("lastModifiedDate", true);
        }
    }

    private void addHeader(VerticalLayout layout) {
        HorizontalLayout header = new HorizontalLayout();
        header.setWidth("100%");
        header.setSpacing(true);
        buttonBar = new TetrisButtonBar();
        buttonBar.addButton(BulletinFilterEnum.OPEN, getText("OPEN"));
        buttonBar.addButton(BulletinFilterEnum.CLOSED, getText("CLOSED"));
        buttonBar.setSelected(BulletinFilterEnum.OPEN);
        buttonBar.addSelectedListener(new TetrisButtonBar.SelectedChangeListener() {
            @Override
            public void onSelected(TetrisButtonBar.SelectedEvent event) {
                presenter.setFilter((BulletinFilterEnum) event.getSelected());
            }
        });
        header.addComponent(buttonBar);

        TetrisImageButton filterButton = new TetrisImageButton(TetrisFontAwesome.FILTER, new TetrisButton.TetrisPopupContentProvider() {
            @Override
            public Component getContent(TetrisButton parentButton) {
                return createFilterPopup(parentButton);
            }
        });
        filterButton.setAutoClose(false);
        header.addComponent(filterButton);

        TetrisSpacer spacer = new TetrisSpacer();
        spacer.setWidth(100, Unit.PIXELS);
        header.addComponent(spacer);
        header.setExpandRatio(spacer, 1.0F);

        TetrisButton addButton = new TetrisButton("Add Solution", new Button.ClickListener() {
            @Override
            public void buttonClick(Button.ClickEvent event) {
                showBulletinPopUp(new Bulletin());
            }
        });
        addButton.setEnabledRequirements(true, TetrisPermissionKey.ALLOWS_TO_CONFIGURE_ANY_TYPE_OF_SOLUTION_TO_ANY_ISSUES);
        header.addComponent(addButton);

        layout.addComponent(header);
    }

    private Component createFilterPopup(TetrisButton parentButton) {
        if (filterPopup == null) {
            bulletinFilterView = new BulletinFilterView(null, presenter);
            if (presenter.fixFilterWidth()) {
                bulletinFilterView.setWidth(500, Unit.PIXELS);
            } else {
                bulletinFilterView.setWidth(1480, Unit.PIXELS);
            }
            filterPopup = new TetrisFilterPopup(parentButton, bulletinFilterView);
            filterPopup.addFilterChangeListener(new TetrisFilterPopup.FilterChangedListener() {
                @Override
                public void filterChanged(TetrisFilterPopup.FilterChangeEvent event) {
                    Bulletin bulletin = bulletinFilterView.getSelectedFilters();
                    presenter.filterBulletins(bulletin);
                }
            });
        }
        return filterPopup;
    }

    private void showBulletinPopUp(Bulletin bulletin) {
        bulletinWindow = new TetrisWindow();
        bulletinWindow.addCloseListener(new Window.CloseListener() {
            @Override
            public void windowClose(Window.CloseEvent e) {
                bulletinFieldGroup.reset();
            }
        });
        if (bulletin.getId() == null) {
            bulletinWindow.setCaption("Add New Solution");
        } else {
            bulletinWindow.setCaption("Update Solution");
        }
        bulletinWindow.setContent(createBulletinPopUpContent(bulletin));
        bulletinWindow.show();
        bulletinWindow.center();
        bulletinWindow.setModal(true);
        bulletinWindow.setWidth(800, Unit.PIXELS);
        bulletinWindow.setResizable(false);
    }

    private Component createBulletinPopUpContent(final Bulletin bulletin) {
        bulletinFieldGroup = new TetrisBeanFieldGroup<Bulletin>(Bulletin.class);

        FormLayout formLayout = new FormLayout();
        formLayout.setMargin(true);

        TetrisTextArea detailsField = new TetrisTextArea(getText("common.details"));
        detailsField.focus();
        detailsField.setWidth(100, Unit.PERCENTAGE);
        detailsField.setMaxLength(2000);
        formLayout.addComponent(detailsField);
        bulletinFieldGroup.bind(detailsField, "text");

        final TetrisBeanItemContainer<String> jobStepContainer = new TetrisBeanItemContainer<String>(String.class);
        final TetrisComboBox jobStepField = new TetrisComboBox(getText("common.stepName"));
        jobStepField.setEnabled(false);
        jobStepField.setRequired(true);
        jobStepField.setTextInputAllowed(true);
        jobStepField.setNewItemsAllowed(true);
        jobStepField.setItemCaptionMode(AbstractSelect.ItemCaptionMode.ID);
        jobStepField.setContainerDataSource(jobStepContainer);

        TetrisBeanItemContainer<String> jobNameContainer = new TetrisBeanItemContainer<String>(String.class);
        jobNameContainer.addAll(presenter.getJobNames());
        TetrisComboBox jobNameField = new TetrisComboBox(getText("common.jobName"));
        jobNameField.setRequired(true);
        jobNameField.setItemCaptionMode(AbstractSelect.ItemCaptionMode.ID);
        jobNameField.setContainerDataSource(jobNameContainer);

        bulletinFieldGroup.bind(jobNameField, "jobName");
        jobNameField.addValueChangeListener(new Property.ValueChangeListener() {
            @Override
            public void valueChange(Property.ValueChangeEvent event) {
                jobStepContainer.removeAllItems();
                jobStepField.setValue(null);
                jobStepField.setEnabled(bulletin.getJobName() != null);

                if (bulletin.getJobName() != null) {
                    jobStepContainer.addAll(presenter.getJobSteps(bulletin.getJobName()));
                }
            }
        });
        formLayout.addComponent(jobNameField);
        bulletinFieldGroup.bind(jobStepField, "stepName");
        formLayout.addComponent(jobStepField);
        TetrisTextField contactPerson = new TetrisTextField(getText("monitor.bulletin.contact.person"));
        contactPerson.setWidth("200");
        contactPerson.setRequired(false);
        formLayout.addComponent(contactPerson);
        bulletinFieldGroup.bind(contactPerson, "contactPerson");

        TetrisTextField estimatedResolutionTime = new TetrisTextField(getText("monitor.bulletin.estimated.resolution.time"));
        estimatedResolutionTime.setWidth("200");
        estimatedResolutionTime.setRequired(false);
        formLayout.addComponent(estimatedResolutionTime);
        bulletinFieldGroup.bind(estimatedResolutionTime, "estimatedResolutionTime");

        final TetrisBeanItemContainer<SupportBulletinAction> actionContainer = new TetrisBeanItemContainer<SupportBulletinAction>(SupportBulletinAction.class);
        actionContainer.addAll(Arrays.asList(SupportBulletinAction.values()));
        final TetrisComboBox actionComboField = new TetrisComboBox("Action");
        actionComboField.setEnabled(true);
        actionComboField.setRequired(true);
        actionComboField.setNullSelectionAllowed(false);
        actionComboField.setItemCaptionMode(AbstractSelect.ItemCaptionMode.ID);
        actionComboField.setContainerDataSource(actionContainer);
        actionComboField.setValue(SupportBulletinAction.MANUAL);
        formLayout.addComponent(actionComboField);
        bulletinFieldGroup.bind(actionComboField, "action");

        TetrisSaveCancelButtonBar saveCancelButtonBar = new TetrisSaveCancelButtonBar();
        saveCancelButtonBar.addValidSaveListener(new TetrisSaveCancelButtonBar.ValidSaveListener() {
            @Override
            public void onValidSave(TetrisSaveCancelButtonBar.ValidSaveEvent event) {
                boolean foundRequiredField = (bulletin.getJobName() != null);
                if (foundRequiredField) {
                    presenter.saveBulletin(bulletin);
                } else {
                    showWarning("You must specify a Job Name and Step Name in order to save.");
                }
            }
        });
        saveCancelButtonBar.getCancelButton().addClickListener(new Button.ClickListener() {
            @Override
            public void buttonClick(Button.ClickEvent event) {
                bulletinFieldGroup.reset(); //reset the field group so any changes that were made to the Bulletin get updated back to there original values
                closePopup();
            }
        });
        bulletinFieldGroup.setSaveCancelButtonBar(saveCancelButtonBar);
        formLayout.addComponent(saveCancelButtonBar);

        bulletinFieldGroup.setItemDataSource(bulletin);

        return formLayout;
    }

    public void updateBulletinTable(List<Bulletin> bulletins) {
        bulletinContainer.removeAllItems();
        bulletinContainer.addAll(bulletins);
        updateTableColumnVisibility((BulletinFilterEnum) buttonBar.getSelected());
    }

    public void closePopup() {
        bulletinWindow.close();
    }

    public void resetFilters() {
        if (bulletinFilterView != null) {
            bulletinFilterView.reset();
        }
    }
}
