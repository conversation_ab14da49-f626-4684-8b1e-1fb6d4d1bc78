package com.ideas.tetris.ui.common.util;

import org.apache.commons.lang.ObjectUtils;

public enum LocalizationDimensionSpecifier {

    ENGLISH("en", "", 250, 160, 530, 75, 0, 500, 100, 100, 90, 125, 75, 35, 125, 142, 65, 125, 110, 200, "767px", 600, 82, 50, 874, 125, 150, 80, 65, 100, 281, "", "", 75, 130, 95, 115, 100, 105, 99, 105, 160, 100, 75, 99, 845, 620, 130, 120, 140, 60, 600, 600, 110, 105, 350, 821),
    JAPANESE("ja", "5px", 180, 170, 557, 100, 349, 538, 140, 140, 90, 125, 75, 35, 125, 172, 65, 125, 130, 200, "767px", 600, 82, 50, 1400, 125, 150, 80, 65, 100, 400, "520", "", 75, 160, 160, 158, 150, 220, 157, 130, 160, 100, 75, 99, 845, 620, 130, 120, 140, 60, 600, 600, 110, 105, 350, 821),
    FRENCH("fr", "", 160, 160, 557, 95, 349, 530, 140, 140, 140, 160, 120, 65, 150, 170, 80, 150, 180, 250, "1065px", 715, 95, 70, 1420, 132, 138, 85, 95, 150, 530, "520px", "520px", 84, 160, 200, 158, 170, 180, 157, 130, 242, 415, 75, 99, 845, 620, 130, 120, 140, 60, 600, 600, 160, 105, 350, 821),
    SPANISH("es", "15px", 195, 195, 530, 75, 0, 530, 100, 100, 115, 160, 93, 82, 138, 170, 80, 150, 160, 245, "1065px", 715, 95, 70, 1220, 132, 138, 85, 65, 150, 405, "520px", "", 84, 160, 145, 158, 155, 160, 145, 125, 242, 415, 190, 112, 960, 770, 170, 119, 180, 150, 935, 740, 160, 120, 375, 980);


    private String language;
    private float dayOfWeekFilterWidth;
    private int availableCapacityToSellSelectedWidth;
    private int availableCapacityToSellUnselectedWidth;
    private String optimizedOffsetFieldsLayoutSpacer;
    private int pricingViewUploadButtonWidth;
    private float overbookingHeaderLayoutWidth;
    private float pricingManagementDayOfWeekFilterWidth;
    private int attributePopupMappedCodeWidth;
    private int attributeAssignmentMarketSegmentWidth;
    private int pricingConfigRoomClassWidth;
    private int pricingConfigBaseRoomTypeWidth;
    private int pricingConfigCeilingFloorWidth;
    private float pricingConfigAddButtonWidth;
    private double competitorDetailsViewAccomClassWidth;
    private int pricingMultidayOverrideAdjustmentTypeWidth;
    private int attributeAssignmentUnassignWidth;
    private int pricingConfigOffsetMethodWidth;
    private float ceilingDayFieldWidth;
    private double pricingRoomTypeWidth;
    private String warningMessageWidth;
    private float overbookingSeasonWindowWidth;
    private int groupPricingProfitPercentWidth;
    private int demandWashOnBooksWidth;
    private float overbookingCeilingWindowWidth;
    private int multidayOverrideRoomClassWidth;
    private int multidayOverrideRoomTypeWidth;
    private int attributeAssignmentEditColumnWidth;
    private int forecastGroupMarketSegmentWidth;
    private int pricingConfigOccupancyTypeWidth;
    private double pricingMultidayOverrideSpecificWidth;
    private String pricingMultidayOverrideSpecificFieldWidth;
    private String pricingAppliedEquallyLabelWidth;
    private float pricingSaveButtonWidth;
    private int pricingMultidayOverrideRoomCLassWidth;
    private double pricingMultidayOverrideFloorFieldWidth;
    private double groupFloorRemoveColumnWidth;
    private double pricingFloorRemoveColumn;
    private double pricingCeilingRemoveColumnWidth;
    private double pricingCeilingAddColumnWidth;
    private double pricingSpecificRemoveColumnWidth;
    private double competitorDetailsRateShoppingWidth;
    private double competitorDetailsPriceWidth;
    private float overbookingOverrideTypeCellWidth;
    private double competitorDetailsLOSWidth;
    private int roomsConfigOverbookingViewStartDateWidth;
    private float overbookingMultiDayOverrideWindowWidth;
    private float pricingRulesLayoutWidth;
    private double investigatorOverrideOccupancyDemand;
    private int investigatorOverrideSpecificColumnWidth;
    private int overbookingMultidayOverrideCOWWidth;
    private int overbookingMultidayOverrideRemoveCOWWidth;
    private float overbookingMultidayOverrideTableWidth;
    private float CPPricingRulesLayoutWidth;
    private int minimumPriceDiffViewStartDateWidth;
    private float optimizationLevelPopupWidth;
    private float multidayOverridesTableWidth;

    LocalizationDimensionSpecifier(String language, String optimizedOffsetFieldsLayoutSpacer, int availableCapacityToSellSelected, int availableCapacityToSellUnselectedWidth,
                                   float dayOfWeekFilterWidth, int pricingViewUploadButtonWidth, float overbookingHeaderLayoutWidth,
                                   float pricingManagementDayOfWeekFilterWidth, int attributePopupMappedCodeWidth, int attributeAssignmentMarketSegmentWidth,
                                   int pricingConfigRoomClassWidth, int pricingConfigBaseRoomTypeWidth, int pricingConfigCeilingFloorWidth,
                                   float pricingConfigAddButtonWidth, double competitorDetailsViewAccomClassWidth,
                                   int pricingMultidayOverrideAdjustmentTypeWidth, int attributeAssignmentUnassignWidth,
                                   int pricingConfigOffsetMethodWidth, float ceilingDayFieldWidth, double pricingRoomTypeWidth, String warningMessageWidth,
                                   float overbookingSeasonWindowWidth, int groupPricingProfitPercentWidth, int demandWashOnBooksWidth, float overbookingCeilingWindowWidth,
                                   int multidayOverrideRoomClassWidth, int multidayOverrideRoomTypeWidth, int attributeAssignmentEditColumnWidth,
                                   int forecastGroupMarketSegmentWidth, int pricingConfigOccupancyTypeWidth, double pricingMultidayOverrideSpecificWidth,
                                   String pricingMultidayOverrideSpecificFieldWidth, String pricingAppliedEquallyLabelWidth, float pricingSaveButtonWidth,
                                   int pricingMultidayOverrideRoomCLassWidth, double pricingMultidayOverrideFloorFieldWidth, double groupFloorRemoveColumnWidth,
                                   double pricingFloorRemoveColumn, double pricingCeilingRemoveColumnWidth, double pricingCeilingAddColumnWidth,
                                   double pricingSpecificRemoveColumnWidth, double competitorDetailsRateShoppingWidth, double competitorDetailsPriceWidth,
                                   double competitorDetailsLOSWidth, int roomsConfigOverbookingViewStartDateWidth, float overbookingMultiDayOverrideWindowWidth,
                                   float pricingRulesLayoutWidth, double investigatorOverrideOccupancyDemand, int investigatorOverrideSpecificColumnWidth,
                                   int overbookingMultidayOverrideCOWWidth, int overbookingMultidayOverrideRemoveCOWWidth,
                                   float overbookingMultidayOverrideTableWidth, float cpPricingRulesLayoutWidth, float overbookingOverrideTypeCellWidth,
                                   int minimumPriceDiffViewStartDateWidth, float optimizationLevelPopupWidth, float multidayOverridesTableWidth) {
        this.language = language;
        this.availableCapacityToSellSelectedWidth = availableCapacityToSellSelected;
        this.availableCapacityToSellUnselectedWidth = availableCapacityToSellUnselectedWidth;
        this.dayOfWeekFilterWidth = dayOfWeekFilterWidth;
        this.optimizedOffsetFieldsLayoutSpacer = optimizedOffsetFieldsLayoutSpacer;
        this.pricingViewUploadButtonWidth = pricingViewUploadButtonWidth;
        this.overbookingHeaderLayoutWidth = overbookingHeaderLayoutWidth;
        this.pricingManagementDayOfWeekFilterWidth = pricingManagementDayOfWeekFilterWidth;
        this.attributePopupMappedCodeWidth = attributePopupMappedCodeWidth;
        this.attributeAssignmentMarketSegmentWidth = attributeAssignmentMarketSegmentWidth;
        this.pricingConfigRoomClassWidth = pricingConfigRoomClassWidth;
        this.pricingConfigBaseRoomTypeWidth = pricingConfigBaseRoomTypeWidth;
        this.pricingConfigCeilingFloorWidth = pricingConfigCeilingFloorWidth;
        this.pricingConfigAddButtonWidth = pricingConfigAddButtonWidth;
        this.competitorDetailsViewAccomClassWidth = competitorDetailsViewAccomClassWidth;
        this.pricingMultidayOverrideAdjustmentTypeWidth = pricingMultidayOverrideAdjustmentTypeWidth;
        this.attributeAssignmentUnassignWidth = attributeAssignmentUnassignWidth;
        this.pricingConfigOffsetMethodWidth = pricingConfigOffsetMethodWidth;
        this.ceilingDayFieldWidth = ceilingDayFieldWidth;
        this.pricingRoomTypeWidth = pricingRoomTypeWidth;
        this.warningMessageWidth = warningMessageWidth;
        this.overbookingSeasonWindowWidth = overbookingSeasonWindowWidth;
        this.groupPricingProfitPercentWidth = groupPricingProfitPercentWidth;
        this.demandWashOnBooksWidth = demandWashOnBooksWidth;
        this.overbookingCeilingWindowWidth = overbookingCeilingWindowWidth;
        this.multidayOverrideRoomClassWidth = multidayOverrideRoomClassWidth;
        this.multidayOverrideRoomTypeWidth = multidayOverrideRoomTypeWidth;
        this.attributeAssignmentEditColumnWidth = attributeAssignmentEditColumnWidth;
        this.forecastGroupMarketSegmentWidth = forecastGroupMarketSegmentWidth;
        this.pricingConfigOccupancyTypeWidth = pricingConfigOccupancyTypeWidth;
        this.pricingMultidayOverrideSpecificWidth = pricingMultidayOverrideSpecificWidth;
        this.pricingMultidayOverrideSpecificFieldWidth = pricingMultidayOverrideSpecificFieldWidth;
        this.pricingAppliedEquallyLabelWidth = pricingAppliedEquallyLabelWidth;
        this.pricingSaveButtonWidth = pricingSaveButtonWidth;
        this.pricingMultidayOverrideRoomCLassWidth = pricingMultidayOverrideRoomCLassWidth;
        this.pricingMultidayOverrideFloorFieldWidth = pricingMultidayOverrideFloorFieldWidth;
        this.groupFloorRemoveColumnWidth = groupFloorRemoveColumnWidth;
        this.pricingFloorRemoveColumn = pricingFloorRemoveColumn;
        this.pricingCeilingRemoveColumnWidth = pricingCeilingRemoveColumnWidth;
        this.pricingCeilingAddColumnWidth = pricingCeilingAddColumnWidth;
        this.pricingSpecificRemoveColumnWidth = pricingSpecificRemoveColumnWidth;
        this.competitorDetailsRateShoppingWidth = competitorDetailsRateShoppingWidth;
        this.competitorDetailsPriceWidth = competitorDetailsPriceWidth;
        this.overbookingMultidayOverrideCOWWidth = overbookingMultidayOverrideCOWWidth;
        this.overbookingMultidayOverrideRemoveCOWWidth = overbookingMultidayOverrideRemoveCOWWidth;
        this.overbookingMultidayOverrideTableWidth = overbookingMultidayOverrideTableWidth;
        CPPricingRulesLayoutWidth = cpPricingRulesLayoutWidth;
        this.overbookingOverrideTypeCellWidth = overbookingOverrideTypeCellWidth;
        this.competitorDetailsLOSWidth = competitorDetailsLOSWidth;
        this.roomsConfigOverbookingViewStartDateWidth = roomsConfigOverbookingViewStartDateWidth;
        this.overbookingMultiDayOverrideWindowWidth = overbookingMultiDayOverrideWindowWidth;
        this.pricingRulesLayoutWidth = pricingRulesLayoutWidth;
        this.investigatorOverrideOccupancyDemand = investigatorOverrideOccupancyDemand;
        this.investigatorOverrideSpecificColumnWidth = investigatorOverrideSpecificColumnWidth;
        this.minimumPriceDiffViewStartDateWidth = minimumPriceDiffViewStartDateWidth;
        this.optimizationLevelPopupWidth = optimizationLevelPopupWidth;
        this.multidayOverridesTableWidth = multidayOverridesTableWidth;
    }

    public String getOptimizedOffsetFieldsLayoutSpacer() {
        return optimizedOffsetFieldsLayoutSpacer;
    }

    public String getLanguage() {
        return language;
    }

    public int getAvailableCapacityToSellSelectedWidth() {
        return availableCapacityToSellSelectedWidth;
    }

    public int getAvailableCapacityToSellUnselectedWidth() {
        return availableCapacityToSellUnselectedWidth;
    }

    public float getDayOfWeekFilterWidth() {
        return dayOfWeekFilterWidth;
    }

    public int getPricingViewUploadButtonWidth() {
        return pricingViewUploadButtonWidth;
    }

    public float getOverbookingHeaderLayoutWidth() {
        return overbookingHeaderLayoutWidth;
    }

    public float getPricingManagementDayOfWeekFilterWidth() {
        return pricingManagementDayOfWeekFilterWidth;
    }

    public int getAttributePopupMappedCodeWidth() {
        return attributePopupMappedCodeWidth;
    }

    public int getAttributeAssignmentMarketSegmentWidth() {
        return attributeAssignmentMarketSegmentWidth;
    }

    public int getPricingConfigRoomClassWidth() {
        return pricingConfigRoomClassWidth;
    }

    public int getPricingConfigBaseRoomTypeWidth() {
        return pricingConfigBaseRoomTypeWidth;
    }

    public int getPricingConfigCeilingFloorWidth() {
        return pricingConfigCeilingFloorWidth;
    }

    public float getPricingConfigAddButtonWidth() {
        return pricingConfigAddButtonWidth;
    }

    public double getCompetitorDetailsViewAccomClassWidth() {
        return competitorDetailsViewAccomClassWidth;
    }

    public int getPricingMultidayOverrideAdjustmentTypeWidth() {
        return pricingMultidayOverrideAdjustmentTypeWidth;
    }

    public int getAttributeAssignmentUnassignWidth() {
        return attributeAssignmentUnassignWidth;
    }

    public int getPricingConfigOffsetMethodWidth() {
        return pricingConfigOffsetMethodWidth;
    }

    public int getGroupPricingProfitPercentWidth() {
        return groupPricingProfitPercentWidth;
    }

    public float getCeilingDayFieldWidth() {
        return ceilingDayFieldWidth;
    }

    public double getPricingRoomTypeWidth() {
        return pricingRoomTypeWidth;
    }

    public String getWarningMessageWidth() {
        return warningMessageWidth;
    }

    public float getOverbookingSeasonWindowWidth() {
        return overbookingSeasonWindowWidth;
    }

    public int getDemandWashOnBooksWidth() {
        return demandWashOnBooksWidth;
    }

    public float getOverbookingCeilingWindowWidth() {
        return overbookingCeilingWindowWidth;
    }

    public int getMultidayOverrideRoomClassWidth() {
        return multidayOverrideRoomClassWidth;
    }

    public int getMultidayOverrideRoomTypeWidth() {
        return multidayOverrideRoomTypeWidth;
    }

    public int getAttributeAssignmentEditColumnWidth() {
        return attributeAssignmentEditColumnWidth;
    }

    public int getForecastGroupMarketSegmentWidth() {
        return forecastGroupMarketSegmentWidth;
    }

    public int getPricingConfigOccupancyTypeWidth() {
        return pricingConfigOccupancyTypeWidth;
    }

    public double getPricingMultidayOverrideSpecificWidth() {
        return pricingMultidayOverrideSpecificWidth;
    }

    public String getPricingMultidayOverrideSpecificFieldWidth() {
        return pricingMultidayOverrideSpecificFieldWidth;
    }

    public String getPricingAppliedEquallyLabelWidth() {
        return pricingAppliedEquallyLabelWidth;
    }

    public float getPricingSaveButtonWidth() {
        return pricingSaveButtonWidth;
    }

    public int getPricingMultidayOverrideRoomCLassWidth() {
        return pricingMultidayOverrideRoomCLassWidth;
    }

    public double getPricingMultidayOverrideFloorFieldWidth() {
        return pricingMultidayOverrideFloorFieldWidth;
    }

    public double getPricingGroupFloorRemoveColumnWidth() {
        return groupFloorRemoveColumnWidth;
    }

    public double getPricingFloorRemoveColumnWidth() {
        return pricingFloorRemoveColumn;
    }

    public double getPricingCeilingRemoveColumnWidth() {
        return pricingCeilingRemoveColumnWidth;
    }

    public double getPricingCeilingAddColumnWidth() {
        return pricingCeilingAddColumnWidth;
    }

    public double getPricingSpecificRemoveColumnWidth() {
        return pricingSpecificRemoveColumnWidth;
    }

    public double getCompetitorDetailsRateShoppingWidth() {
        return competitorDetailsRateShoppingWidth;
    }

    public double getCompetitorDetailsPriceWidth() {
        return competitorDetailsPriceWidth;
    }

    public double getCompetitorDetailsLOSWidth() {
        return competitorDetailsLOSWidth;
    }

    public float getPricingRulesLayoutWidth() {
        return pricingRulesLayoutWidth;
    }

    public int getRoomsConfigOverbookingViewStartDateWidth() {
        return roomsConfigOverbookingViewStartDateWidth;
    }

    public float getOverbookingMultiDayOverrideWindowWidth() {
        return overbookingMultiDayOverrideWindowWidth;
    }

    public double getInvestigatorOverrideOccupancyDemand() {
        return investigatorOverrideOccupancyDemand;
    }

    public int getOverbookingMultidayOverrideCOWWidth() {
        return overbookingMultidayOverrideCOWWidth;
    }

    public int getInvestigatorOverrideSpecificColumnWidth() {
        return investigatorOverrideSpecificColumnWidth;
    }

    public int getOverbookingMultidayOverrideRemoveCOWWidth() {
        return overbookingMultidayOverrideRemoveCOWWidth;
    }

    public float getOverbookingOverrideTypeCellWidth() {
        return overbookingOverrideTypeCellWidth;
    }

    public float getOverbookingMultidayOverrideTableWidth() {
        return overbookingMultidayOverrideTableWidth;
    }

    public float getCPPricingRulesLayoutWidth() {
        return CPPricingRulesLayoutWidth;
    }

    public int getMinimumPriceDiffViewStartDateWidth() {
        return minimumPriceDiffViewStartDateWidth;
    }

    public float getOptimizationLevelPopupWidth() {
        return optimizationLevelPopupWidth;
    }

    public float getMultidayOverridesTableWidth() {
        return multidayOverridesTableWidth;
    }

    public static LocalizationDimensionSpecifier forCurrentLocale() {
        String lang = UiUtils.getLocale().getLanguage();
        for (LocalizationDimensionSpecifier value : LocalizationDimensionSpecifier.values()) {
            if (ObjectUtils.equals(value.getLanguage(), lang)) {
                return value;
            }
        }
        return LocalizationDimensionSpecifier.ENGLISH;
    }

}
