package com.ideas.tetris.ui.modules.installation.genericagile.importexcel;

import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.services.agileproductconfig.entity.*;
import com.ideas.tetris.pacman.services.agilerates.configuration.dto.AgileRatesProductTypeEnum;
import com.ideas.tetris.pacman.services.agilerates.configuration.entity.AgileRatesDecisionsSentBy;
import com.ideas.tetris.pacman.services.agilerates.configuration.entity.AgileRatesOffsetMethod;
import com.ideas.tetris.pacman.services.product.*;
import com.ideas.tetris.platform.services.daoandentities.entity.Status;
import com.ideas.tetris.ui.modules.channelcosts.simplified.validation.Errors;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.log4j.Logger;
import org.apache.poi.ss.usermodel.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;

import static com.ideas.tetris.ui.common.util.UiUtils.getText;
import static com.ideas.tetris.ui.modules.channelcosts.simplified.SimplifiedChannelCostConstants.FORMAT_ERROR;
import static com.ideas.tetris.ui.modules.channelcosts.simplified.SimplifiedChannelCostConstants.GENERIC_ERROR_CODE;

public class GenericAgileProductImportValidator {


    private static final Logger LOGGER = Logger.getLogger(GenericAgileProductImportValidator.class);

    private static final List<AgileRatesProductTypeEnum> AGILE_RATES_PACKAGED_PRODUCT_TYPE_ENUM_LIST = Arrays.asList(AgileRatesProductTypeEnum.FENCED_AND_PACKAGED, AgileRatesProductTypeEnum.UNFENCED_AND_PACKAGED);
    public static final String INVALID_COLUMNS_ERROR = "invalid.columns.error";

    protected Map<Integer, Errors> errors;
    protected Map<String, Set<String>> masterRoomClassOffsetMap;
    protected Map<String,Set<String>> currentRoomClassOffsets;
    protected Map<String, Set<String>> masterDtaOffsetMap;
    protected Map<String,Set<String>> currentDtaOffsets;
    protected Map<String, Integer> seasonsColNameToIndexMap;

    public static final String NAME = "Name";
    public static final String SYSTEM_DEFAULT = "System_Default";
    public static final String CODE = "Code";
    public static final String TYPE = "Type";
    public static final String DEPENDENT_PRODUCT_NAME = "Dependent_Product_Name";
    public static final String DESCRIPTION = "Description";
    public static final String RATE_CODES = "Rate_Codes";
    public static final String EXTRA_RATE_CODES_FOR_RESTRICTIONS = "Extra_Rate_Codes_For_Restrictions";
    public static final String TO_PRODUCT_HIERARCHY = "Relates To";
    public static final String MIN_DTA = "Min_DTA";
    public static final String MAX_DTA = "Max_DTA";
    public static final String MIN_LOS = "Min_LOS";
    public static final String MAX_LOS = "MAX_LOS";
    public static final String MIN_OFFSET = "Min_Offset";
    public static final String MAX_OFFSET = "Max_Offset";
    public static final String PRODUCT_FLOOR_RATE = "Product_Floor_Rate";
    public static final String MIN_ROOMS = "Min_Rooms";
    public static final String MAX_ROOMS = "Max_Rooms";
    public static final String RATE_SHOPPING_LOS_MIN = "Rate_Shopping_LOS_Min";
    public static final String RATE_SHOPPING_LOS_MAX = "Rate_Shopping_LOS_Max";
    public static final String CHILD_PRICING_TYPE = "Child_Pricing_Type";
    public static final String ROOM_TYPES = "Room_Types";
    public static final String IS_SEASONAL_PRODUCT_ONLY = "Is_Seasonal_Product_Only";
    public static final String PACKAGES = "Packages";
    public static final String PACKAGES_AMOUNT = "Packages_Amount";
    public static final String PRODUCT_CODE_ID = "Product_Code_ID";
    public static final String WEBRATE_TYPE = "Webrate_Type";
    public static final String WEBRATE_LOS_TYPE = "Webrate_LOS_Type";
    public static final String WEBRATE_LOS_MIN = "Webrate_LOS_Min";
    public static final String WEBRATE_LOS_MAX = "Webrate_LOS_Max";
    public static final String SET_RESTRICTIONS = "Set_Restrictions";
    public static final String IS_OFFSET_FOR_EXTRA_ADULT = "Is_Offset_For_Extra_Adult";
    public static final String IS_OFFSET_FOR_EXTRA_CHILD = "Is_Offset_For_Extra_Child";
    public static final String IS_DOW_OFFSET = "Is_DOW_Offset";
    public static final String IS_RC_OFFSET = "Is_RC_Offset";
    public static final String IS_DTA_OFFSET = "Is_DTA_Offset";
    public static final String IS_UPLOAD = "Is_Upload";
    public static final String STATUS_ID = "Status_ID";
    public static final String IS_DEFAULT_INACTIVE = "Is_Default_Inactive";
    public static final String INVALID_REASON_ID = "Invalid_Reason_ID";
    public static final String IS_OPTIMIZED = "Is_Optimized";
    public static final String PRICE_ROUNDING_RULE = "Price_Rounding_Rule";
    public static final String IS_PUBLICALLY_AVAILABLE = "Is_Publically_Available";
    public static final String CURRENCY_CODE = "Currency_Code";
    public static final String MINIMUM_PRICE_CHANGE = "Minimum_Price_Change";
    public static final String OFFSET_METHOD = "Offset_Method";
    public static final String IS_FIXED_ABOVE_BAR = "Is_Fixed_Above_Bar";
    public static final String SEND_ADJUSTMENT = "Send_Adjustment";
    public static final String CENTRALLY_MANAGED = "Centrally_Managed";
    public static final String IS_OVERRIDABLE = "Is_Overridable";
    public static final String FLOOR_TYPE = "Floor_Type";
    public static final String FLOOR_PERCENTAGE = "Floor_Percentage";
    public static final String OFFSET_VALUE = "Offset_Value";
    public static final String OFFSET_CF = "Offset_CF";
    public static final String IS_USE_IN_SG_EVALUATION = "Is_Use_In_SG_Evaluation";
    public static final String CHARGE_TYPE = "Charge_Type";

    public static final String SEASON_NAME = "Season Name";
    public static final String START_DATE = "Start Date";
    public static final String END_DATE = "End Date";

    public static final String OCCUPANCY_TYPE = "Occupancy Type";
    public static final String ROOM_CLASS = "Room Class";
    public static final String ROOM_TYPE = "Room Type";

    public static final String MIN_VAL = "MIN_val";
    public static final String MAX_VAL = "Max_val";
    public static final String MAX_MON = "Max Mon";
    public static final String MIN_MON = "Min Mon";
    public static final String MAX_TUES = "Max Tues";
    public static final String MIN_TUES = "Min Tues";
    public static final String MAX_WED = "Max Wed";
    public static final String MIN_WED = "Min Wed";
    public static final String MAX_THUR = "Max Thur";
    public static final String MIN_THUR = "Min Thur";
    public static final String MAX_FRI = "Max Fri";
    public static final String MIN_FRI = "Min Fri";
    public static final String MAX_SAT = "Max Sat";
    public static final String MIN_SAT = "Min Sat";
    public static final String MAX_SUN = "Max Sun";
    public static final String MIN_SUN = "Min Sun";
    public static final String PRODUCT_NAME = "Product_Name";


    public GenericAgileProductImportValidator() {
        this.errors = new LinkedHashMap<>();
        this.masterDtaOffsetMap = new HashMap<>();
        this.masterRoomClassOffsetMap = new HashMap<>();
        this.seasonsColNameToIndexMap = new HashMap<>();
        this.currentDtaOffsets = new HashMap<>();
        this.currentRoomClassOffsets = new HashMap<>();
    }


    public Set<String> createDelimiters(Workbook workbook, String[] dynamicHeaders, int sheetNum) {
        Set<String> delimiters = new HashSet<>();
        Sheet sheet = workbook.getSheetAt(sheetNum);

        for (Row row: sheet) {
            if (row.getRowNum() > 0 && row.getPhysicalNumberOfCells() > ((dynamicHeaders.length)+2)) {
                delimiters.add(getDelimiterString(dynamicHeaders, sheetNum, row, 1));
            }
        }
        return delimiters;
    }

    private String getDelimiterString(String[] dynamicHeaders, int sheetNum, Row row, int shouldIncludeProduct) {
        String delimiter = "$#$";

        for (int col = 0; col<((dynamicHeaders.length)+shouldIncludeProduct); col++) {
            String cellData = getCellDataAsString(row, col, sheetNum);
            if(cellData != null){
            delimiter = delimiter.concat(cellData);
            delimiter = delimiter.concat("$#$");
            }
        }
        return delimiter;
    }

    private String formDtaOffsetString(String minDta, String maxDta) {

        String minDtaKey = Objects.isNull(minDta) ? "0000" : String.format("%04d", Integer.parseInt(minDta));
        String maxDtaKey = Objects.isNull(maxDta) ? "9999" : String.format("%04d", Integer.parseInt(maxDta));
        return minDtaKey + "-" + maxDtaKey;
    }


    public boolean validateDelimiters(Set<String> delimiterForProduct, Set<String> delimiterForExtra, int sheetNum) {
        if (!delimiterForProduct.containsAll(delimiterForExtra)) {
            addError(sheetNum, GENERIC_ERROR_CODE, getText("unknown.combination.provided"));
            return false;
        }
        return true;
    }

    private void checkEmptyProductSheet(Map<String, ProductConfig> uploadedProductList, int sheetNum) {
        if (CollectionUtils.isEmpty(Collections.singleton(uploadedProductList))) {
            addError(sheetNum, GENERIC_ERROR_CODE, getText("empty.excel.sheet.can.not.be.uploaded"));
        }
    }

    public Map<String, ProductConfig>  iterateOverDataAndValidateProductSheet(Workbook workbook, String[] dynamicHeaders, Set<String> delimiterForProduct, int sheetNum) {
        Map<String, ProductConfig>  uploadedProductConfigList = new LinkedHashMap<>();
        Sheet sheet = workbook.getSheetAt(0);

        for (Row row : sheet) {
            int rowNum = row.getRowNum();
            if (rowNum == 0) {
                validateHeader(row, dynamicHeaders, sheetNum);
            } else if (rowNum > 0 && row.getPhysicalNumberOfCells() > ((dynamicHeaders.length) + 2)) {
                populateAgileProductConfig(row, dynamicHeaders, uploadedProductConfigList, delimiterForProduct, sheetNum);
            }
        }
        checkEmptyProductSheet(uploadedProductConfigList, sheetNum);
        return uploadedProductConfigList;
    }

    public Map<String, List<ProductPackages>> iterateOverDataAndValidatePackageSheet(Workbook workbook, String[] dynamicHeaders, Set<String> delimiterForPackages, int sheetNum) {
        Map<String, List<ProductPackages>> uploadedProductPackageList = new LinkedHashMap<>();
        Sheet sheet = workbook.getSheetAt(2);

        for (Row row : sheet) {
            int rowNum = row.getRowNum();
            if (rowNum == 0) {
                validatePackageHeader(row, dynamicHeaders, sheetNum);
            } else if (rowNum > 0 && row.getPhysicalNumberOfCells() > ((dynamicHeaders.length) + 2)) {
                populateProductPackages(row, dynamicHeaders, uploadedProductPackageList, delimiterForPackages, sheetNum);
            }
        }
        return uploadedProductPackageList;

    }

    public Map<String, List<ProductSeedAttributeValues>> iterateOverDataAndValidateSeedValues(Workbook workbook, String[] dynamicHeaders, List<ProductSeedAttribute> seedAttributes, Set<String> delimiterForProduct, int sheetNum) {
        Map<String, List<ProductSeedAttributeValues>> uploadedSeedValuesList = new LinkedHashMap<>();
        Sheet sheet = workbook.getSheetAt(0);

        for (Row row : sheet) {
            int rowNum = row.getRowNum();
            if (rowNum > 0) {
                validateSeedRowData(uploadedSeedValuesList, row, dynamicHeaders, seedAttributes, delimiterForProduct, sheetNum);
            }
        }
        return uploadedSeedValuesList;
    }

    public Map<String, List<ProductOffset>> iterateOverDataAndValidateOffsetSheet(Workbook workbook, String[] dynamicHeaders, int sheetNum, Set<String> delimiterForSeason) {
        Map<String, List<ProductOffset>> uploadedProductOffsetList = new LinkedHashMap<>();
        Sheet sheet = workbook.getSheetAt(1);

        for (Row row : sheet) {
            int rowNum = row.getRowNum();
            if (rowNum == 0) {
                validateOffsetHeader(row, dynamicHeaders, sheetNum);
                row.cellIterator().forEachRemaining(cell -> seasonsColNameToIndexMap.put(cell.getStringCellValue(), cell.getColumnIndex()));
            } else if(rowNum > 0 && row.getPhysicalNumberOfCells() > ((dynamicHeaders.length)+2)) {
                populateProductOffset(row, dynamicHeaders, uploadedProductOffsetList, sheetNum, delimiterForSeason);
            }
        }
        return uploadedProductOffsetList;
    }

    private void addErrorIfNonNumeric(int rowNum, String field, String input, int sheetNum) {
        if (!isNumericValueProvided(input)) {
            addError(sheetNum, rowNum, String.format(getText("only.numeric.field"), field));
        }
    }

    private void addErrorIfEmpty(int rowNum, String field, String input, int sheetNum) {
        if (StringUtils.isEmpty(input)) {
            addError(sheetNum, rowNum, String.format(getText("mandatory.field.not.provided"), field));
        }
    }

    private void addErrorIfNonBooleanValueProvided(int rowNum, String field, String input, int sheetNum) {
        if (!isBooleanValueProvided(input)) {
            addError(sheetNum, rowNum, String.format(getText("only.0or1.input"), field));
        }
    }

    private void addErrorIfAdjustmentSetForPackagedProduct(int rowNum, String field, String input, ProductConfig productConfig, int sheetNum) {
        if ("1".equalsIgnoreCase(input) && productContainsPackage(productConfig)) {
            addError(sheetNum, rowNum, String.format(getText("invalid.adjustment.set"), field));
        }
    }

    private void addErrorBasedOnLosType(int rowNum, String webrateLosVal, String webrateLosType, int sheetNum) {
        if (webrateLosType != null) {
            RateTypeLOS rateTypeLOS = RateTypeLOS.valueOfKey(webrateLosType);
            if (rateTypeLOS.getId() == 1 && webrateLosVal != null) {
                addError(sheetNum, rowNum, String.format(getText("webratelos.all.range.notempty")));
            } else if (rateTypeLOS.getId() == 2 && webrateLosVal == null) {
                addError(sheetNum, rowNum, String.format(getText("webratelos.custom.range.empty")));
            }
        }
    }

    private void addErrorIfEmptyAndWebrateTypeProvided(int rowNum, String field, String webrateLosType, String webrateType, int sheetNum) {
        if (webrateType != null && webrateLosType == null) {
            addError(sheetNum, rowNum, String.format(getText("webratelostype.empty.webrate.not.empty"), field));
        }
    }

    private void addErrorIfWrongType(int rowNum, String text, String type, String code, int sheetNum) {
        if (code.equalsIgnoreCase("INDEPENDENT") && !type.equalsIgnoreCase("INDEPENDENTLY")) {
            addError(sheetNum, rowNum, String.format(getText("wrong.type.added"), text));
        } else if (code.equalsIgnoreCase("AGILE_RATES")) {
            if (!(type.equalsIgnoreCase("FENCED_AND_PACKAGED") || type.equalsIgnoreCase("UNFENCED_PACKAGED") || type.equalsIgnoreCase("FENCED_NO_PACKAGE") || type.equalsIgnoreCase("UNFENCED_AND_NO_PACKAGED"))) {
                addError(sheetNum, rowNum, String.format(getText("wrong.type.added"), text));
            }
        } else if (code.equalsIgnoreCase("SMALL_GROUP") && !(type.equalsIgnoreCase("SMALL_GROUP"))) {
                addError(sheetNum, rowNum, String.format(getText("wrong.type.added"), text));
            }

    }

    private void addErrorIfLimitExceeded(int rowNum, String field, String input, int length, int sheetNum) {
        if (input != null && input.length() > length) {
            addError(sheetNum, rowNum, String.format(getText("word.limit.exceeded"), field));
        }
    }

    private void addErrorIfRestrictionsNull(int rowNum, String restrictions, String extraRateCodesForRestrictions, int sheetNum) {
        if (restrictions == null && extraRateCodesForRestrictions != null) {
            addError(sheetNum, rowNum, String.format(getText("resriction.not.set"), restrictions));
        } else if (extraRateCodesForRestrictions != null && !("1".equalsIgnoreCase(restrictions))) {
            addError(sheetNum, rowNum, String.format(getText("resriction.not.set"), restrictions));
        }
    }

    private void addErrorIfNotIndependent(int rowNum, String offsetCF, String code, int sheetNum) {
        if (!(code.equalsIgnoreCase("INDEPENDENT")) && offsetCF != null) {
            addError(sheetNum, rowNum, String.format(getText("not.independent.product.but.cf.provided")));
        }
    }

    private void populateAgileProductConfig(Row row, String[] dynamicHeaders, Map<String, ProductConfig> uploadedProductConfigList, Set<String> delimiterForProduct, int sheetNum) {
        int columnNo = dynamicHeaders.length;
        ProductConfig agileProductConfigGeneric = new ProductConfig();

        String name = getCellDataAsString(row, columnNo++, sheetNum);
        addErrorIfEmpty(row.getRowNum(), getText(NAME), name, sheetNum);
        addErrorIfLimitExceeded(row.getRowNum(), getText(NAME), name, 100, sheetNum);
        agileProductConfigGeneric.setName(name);

        String code = getCellDataAsString(row, columnNo++, sheetNum);
        addErrorIfEmpty(row.getRowNum(), getText(CODE), code, sheetNum);
        addErrorIfLimitExceeded(row.getRowNum(), getText(CODE), code, 50, sheetNum);
        agileProductConfigGeneric.setCode(code);

        String statusId = getCellDataAsString(row, columnNo++, sheetNum);
        addErrorIfNonNumeric(row.getRowNum(), getText(STATUS_ID), statusId, sheetNum);
        agileProductConfigGeneric.setStatus(Status.valueOfId(Integer.parseInt(statusId)));

        String isUpload = getCellDataAsString(row, columnNo++, sheetNum);
        addErrorIfNonBooleanValueProvided(row.getRowNum(), getText(IS_UPLOAD), isUpload, sheetNum);
        agileProductConfigGeneric.setUpload(isUpload != null && "1".equalsIgnoreCase(isUpload));

        String centrallyManaged = getCellDataAsString(row, columnNo++, sheetNum);
        addErrorIfNonBooleanValueProvided(row.getRowNum(), getText(CENTRALLY_MANAGED), centrallyManaged, sheetNum);
        agileProductConfigGeneric.setCentrallyManaged(centrallyManaged != null && "1".equalsIgnoreCase(centrallyManaged));

        String description = getCellDataAsString(row, columnNo++, sheetNum);
        addErrorIfEmpty(row.getRowNum(), getText(DESCRIPTION), description, sheetNum);
        addErrorIfLimitExceeded(row.getRowNum(), getText(DESCRIPTION), description, 250, sheetNum);
        agileProductConfigGeneric.setDescription(description);

        String dependentProductName = getCellDataAsString(row, columnNo++, sheetNum);
        addErrorIfLimitExceeded(row.getRowNum(), getText(DEPENDENT_PRODUCT_NAME), dependentProductName, 100, sheetNum);
        agileProductConfigGeneric.setDependentProductName(dependentProductName);

        String type = getCellDataAsString(row, columnNo++, sheetNum);
        addErrorIfEmpty(row.getRowNum(), getText(TYPE), type, sheetNum);
        addErrorIfWrongType(row.getRowNum(), getText(TYPE), type, code, sheetNum);
        addErrorIfLimitExceeded(row.getRowNum(), getText(TYPE), type, 50, sheetNum);
        agileProductConfigGeneric.setType(type);

        String isOptimized = getCellDataAsString(row, columnNo++, sheetNum);
        addErrorIfNonBooleanValueProvided(row.getRowNum(), getText(IS_OPTIMIZED), isOptimized, sheetNum);
        agileProductConfigGeneric.setOptimized(isOptimized != null && "1".equalsIgnoreCase(isOptimized));

        String isSeasonalProductOnly = getCellDataAsString(row, columnNo++, sheetNum);
        addErrorIfNonBooleanValueProvided(row.getRowNum(), getText(IS_SEASONAL_PRODUCT_ONLY), isSeasonalProductOnly, sheetNum);
        agileProductConfigGeneric.setSeasonalProductOnly(isSeasonalProductOnly != null && "1".equalsIgnoreCase(isSeasonalProductOnly));

        String priceRoundingRule = getCellDataAsString(row, columnNo++, sheetNum);
        addErrorIfNonNumeric(row.getRowNum(), getText(PRICE_ROUNDING_RULE), priceRoundingRule, sheetNum);
        try {
            agileProductConfigGeneric.setRoundingRule(priceRoundingRule != null ? RoundingRule.valueOfId(Integer.parseInt(priceRoundingRule)) : RoundingRule.valueOfId(1));
        } catch (Exception e) {
            addError(sheetNum, row.getRowNum(), e.getMessage());
        }

        String floorType = getCellDataAsString(row, columnNo++, sheetNum);
        addErrorIfNonNumeric(row.getRowNum(), getText(FLOOR_TYPE), floorType, sheetNum);
        try {
            agileProductConfigGeneric.setFloorType(floorType != null ? FloorType.valueOfId(Integer.parseInt(floorType)) : FloorType.valueOfId(2));
        } catch (Exception e) {
            addError(sheetNum, row.getRowNum(), e.getMessage());
        }

        String productFloorRate = getCellDataAsString(row, columnNo++, sheetNum);
        addErrorIfNonNumeric(row.getRowNum(), getText(PRODUCT_FLOOR_RATE), productFloorRate, sheetNum);
        agileProductConfigGeneric.setProductFloorRate(productFloorRate != null ? new BigDecimal(productFloorRate) : null);

        String floorPercentage = getCellDataAsString(row, columnNo++, sheetNum);
        addErrorIfNonNumeric(row.getRowNum(), getText(FLOOR_PERCENTAGE), floorPercentage, sheetNum);
        agileProductConfigGeneric.setFloorPercentage(floorPercentage != null ? new BigDecimal(floorPercentage) : null);

        String currencyCode = getCellDataAsString(row, columnNo++, sheetNum);
        addErrorIfLimitExceeded(row.getRowNum(), getText(CURRENCY_CODE), currencyCode, 3, sheetNum);
        agileProductConfigGeneric.setCurrencyCode(currencyCode);

        String minPriceChange = getCellDataAsString(row, columnNo++, sheetNum);
        addErrorIfNonNumeric(row.getRowNum(), getText(MINIMUM_PRICE_CHANGE), minPriceChange, sheetNum);
        agileProductConfigGeneric.setMinimumPriceChange(minPriceChange != null ? new BigDecimal(minPriceChange) : null);

        String isOverridable = getCellDataAsString(row, columnNo++, sheetNum);
        addErrorIfNonNumeric(row.getRowNum(), getText(IS_OVERRIDABLE), isOverridable, sheetNum);
        try {
            agileProductConfigGeneric.setIsOverridable(isOverridable != null ? OverridableProductEnum.valueOfId(Integer.parseInt(isOverridable)) : OverridableProductEnum.valueOfId(1));
        } catch (Exception e) {
            addError(sheetNum, row.getRowNum(), e.getMessage());
        }

        String isPublic = getCellDataAsString(row, columnNo++, sheetNum);
        addErrorIfNonBooleanValueProvided(row.getRowNum(), getText(IS_PUBLICALLY_AVAILABLE), isPublic, sheetNum);
        agileProductConfigGeneric.setPubliclyAvailable(isPublic != null ? "1".equalsIgnoreCase(isPublic) : true);

        String isOffsetForExtraAdult = getCellDataAsString(row, columnNo++, sheetNum);
        addErrorIfNonBooleanValueProvided(row.getRowNum(), getText(IS_OFFSET_FOR_EXTRA_ADULT), isOffsetForExtraAdult, sheetNum);
        agileProductConfigGeneric.setOffsetForExtraAdult(isOffsetForExtraAdult != null && "1".equalsIgnoreCase(isOffsetForExtraAdult));

        String isOffsetForExtraChild = getCellDataAsString(row, columnNo++, sheetNum);
        addErrorIfNonBooleanValueProvided(row.getRowNum(), getText(IS_OFFSET_FOR_EXTRA_CHILD), isOffsetForExtraChild, sheetNum);
        agileProductConfigGeneric.setOffsetForExtraChild(isOffsetForExtraChild != null && "1".equalsIgnoreCase(isOffsetForExtraChild));

        String rateCodes = getCellDataAsString(row, columnNo++, sheetNum);
        addErrorIfEmpty(row.getRowNum(), getText(RATE_CODES), rateCodes, sheetNum);
        addErrorIfLimitExceeded(row.getRowNum(), getText(RATE_CODES), rateCodes, 3000, sheetNum);
        agileProductConfigGeneric.setRateCodes(rateCodes);

        String roomTypes = getCellDataAsString(row, columnNo++, sheetNum);
        addErrorIfEmpty(row.getRowNum(), getText(ROOM_TYPES), roomTypes, sheetNum);
        addErrorIfLimitExceeded(row.getRowNum(), getText(ROOM_TYPES), roomTypes, 1000, sheetNum);
        agileProductConfigGeneric.setRoomTypes(roomTypes.toUpperCase());

        String minDTA = getCellDataAsString(row, columnNo++, sheetNum);
        addErrorIfNonNumeric(row.getRowNum(), getText(MIN_DTA), minDTA, sheetNum);
        agileProductConfigGeneric.setMinDTA((minDTA == null) ? 0 : Integer.parseInt(minDTA));

        String maxDTA = getCellDataAsString(row, columnNo++, sheetNum);
        addErrorIfNonNumeric(row.getRowNum(), getText(MAX_DTA), maxDTA, sheetNum);
        agileProductConfigGeneric.setMaxDTA((maxDTA == null) ? null : Integer.parseInt(maxDTA));

        String minLOS = getCellDataAsString(row, columnNo++, sheetNum);
        addErrorIfNonNumeric(row.getRowNum(), getText(MIN_LOS), minLOS, sheetNum);
        agileProductConfigGeneric.setMinLOS((minLOS == null) ? 1 : Integer.parseInt(minLOS));

        String maxLOS = getCellDataAsString(row, columnNo++, sheetNum);
        addErrorIfNonNumeric(row.getRowNum(), getText(MAX_LOS), maxLOS, sheetNum);
        agileProductConfigGeneric.setMaxLOS((maxLOS == null) ? null : Integer.parseInt(maxLOS));

        String toProductHierarchy = getCellDataAsString(row, columnNo++, sheetNum);
        addErrorIfLimitExceeded(row.getRowNum(), getText(TO_PRODUCT_HIERARCHY), toProductHierarchy, 100, sheetNum);
        agileProductConfigGeneric.setToProductHierarchy(toProductHierarchy);

        String minRooms = getCellDataAsString(row, columnNo++, sheetNum);
        addErrorIfNonNumeric(row.getRowNum(), getText(MIN_ROOMS), minRooms, sheetNum);
        agileProductConfigGeneric.setMinRooms((minRooms == null) ? -1 : Integer.parseInt(minRooms));

        String maxRooms = getCellDataAsString(row, columnNo++, sheetNum);
        addErrorIfNonNumeric(row.getRowNum(), getText(MAX_ROOMS), maxRooms, sheetNum);
        agileProductConfigGeneric.setMaxRooms((maxRooms == null) ? -1 : Integer.parseInt(maxRooms));

        String childPricingType = getCellDataAsString(row, columnNo++, sheetNum);
        addErrorIfNonNumeric(row.getRowNum(), getText(CHILD_PRICING_TYPE), childPricingType, sheetNum);
        agileProductConfigGeneric.setChildPricingType((childPricingType == null) ? 1 : Integer.parseInt(childPricingType));

        String rateShoppingLosMin = getCellDataAsString(row, columnNo++, sheetNum);
        addErrorIfNonNumeric(row.getRowNum(), getText(RATE_SHOPPING_LOS_MIN), rateShoppingLosMin, sheetNum);
        agileProductConfigGeneric.setRateShoppingLOSMin((rateShoppingLosMin == null) ? -1 : Integer.parseInt(rateShoppingLosMin));

        String rateShoppingLosMax = getCellDataAsString(row, columnNo++, sheetNum);
        addErrorIfNonNumeric(row.getRowNum(), getText(RATE_SHOPPING_LOS_MAX), rateShoppingLosMax, sheetNum);
        agileProductConfigGeneric.setRateShoppingLOSMax((rateShoppingLosMax == null) ? -1 : Integer.parseInt(rateShoppingLosMax));

        String webrateType = getCellDataAsString(row, columnNo++, sheetNum);
        addErrorIfLimitExceeded(row.getRowNum(), getText(WEBRATE_TYPE), webrateType, 500, sheetNum);
        try {
            agileProductConfigGeneric.setWebrateType((webrateType == null) ? WebrateTypeEnum.WEBRATE_NA : WebrateTypeEnum.valueOfNameOrCode(webrateType));
        } catch (Exception e) {
            addError(sheetNum, row.getRowNum(), e.getMessage());
        }

        String webrateLosType = getCellDataAsString(row, columnNo++, sheetNum);
        addErrorIfLimitExceeded(row.getRowNum(), getText(WEBRATE_LOS_TYPE), webrateLosType, 15, sheetNum);
        addErrorIfEmptyAndWebrateTypeProvided(row.getRowNum(), getText(WEBRATE_LOS_TYPE), webrateLosType, webrateType, sheetNum);
        try {
            agileProductConfigGeneric.setWebrateLosType(webrateLosType != null ? RateTypeLOS.valueOfKey(webrateLosType) : RateTypeLOS.RATE_SHOPPING_DATA_NA);
        } catch (Exception e) {
            addError(sheetNum, row.getRowNum(), e.getMessage());
        }

        String webrateLosMin = getCellDataAsString(row, columnNo++, sheetNum);
        addErrorIfNonNumeric(row.getRowNum(), getText(WEBRATE_LOS_MIN), webrateLosMin, sheetNum);
        addErrorBasedOnLosType(row.getRowNum(), webrateLosMin, webrateLosType, sheetNum);
        agileProductConfigGeneric.setWebrateLosMin((webrateLosMin == null)? null : Integer.parseInt(webrateLosMin));

        String webrateLosMax = getCellDataAsString(row, columnNo++, sheetNum);
        addErrorIfNonNumeric(row.getRowNum(), getText(WEBRATE_LOS_MAX), webrateLosMax, sheetNum);
        addErrorBasedOnLosType(row.getRowNum(), webrateLosMax, webrateLosType, sheetNum);
        agileProductConfigGeneric.setWebrateLosMax((webrateLosMax == null)? null : Integer.parseInt(webrateLosMax));

        if (agileProductConfigGeneric.getWebrateLosType().getKey().equalsIgnoreCase("ALL")) {
            agileProductConfigGeneric.setWebrateLosMin(-1);
            agileProductConfigGeneric.setWebrateLosMax(-1);
        }

        String sendAdjustment = getCellDataAsString(row, columnNo++, sheetNum);
        addErrorIfNonBooleanValueProvided(row.getRowNum(), getText(SEND_ADJUSTMENT), sendAdjustment, sheetNum);
        addErrorIfAdjustmentSetForPackagedProduct(row.getRowNum(), getText(SEND_ADJUSTMENT), sendAdjustment, agileProductConfigGeneric, sheetNum);
        agileProductConfigGeneric.setDecisionsSentBy(sendAdjustment != null ? AgileRatesDecisionsSentBy.valueOfId(Integer.parseInt(sendAdjustment)) : AgileRatesDecisionsSentBy.valueOfId(0));

        String systemDefault = getCellDataAsString(row, columnNo++, sheetNum);
        addErrorIfNonBooleanValueProvided(row.getRowNum(), getText(SYSTEM_DEFAULT), systemDefault, sheetNum);
        agileProductConfigGeneric.setSystemDefault(systemDefault != null && "1".equalsIgnoreCase(systemDefault));

        String restrictions = getCellDataAsString(row, columnNo++, sheetNum);
        addErrorIfNonBooleanValueProvided(row.getRowNum(), getText(SET_RESTRICTIONS), restrictions, sheetNum);
        agileProductConfigGeneric.setRestrictions(restrictions != null && "1".equalsIgnoreCase(restrictions));

        String extraRateCodesForRestrictions = getCellDataAsString(row, columnNo++, sheetNum);
        addErrorIfRestrictionsNull(row.getRowNum(), restrictions, extraRateCodesForRestrictions, sheetNum);
        addErrorIfLimitExceeded(row.getRowNum(), getText(EXTRA_RATE_CODES_FOR_RESTRICTIONS), extraRateCodesForRestrictions, 3000, sheetNum);
        agileProductConfigGeneric.setExtraRateCodesForRestrictions(extraRateCodesForRestrictions);

        String isFixedAboveBar = getCellDataAsString(row, columnNo++, sheetNum);
        addErrorIfNonBooleanValueProvided(row.getRowNum(), getText(IS_FIXED_ABOVE_BAR), isFixedAboveBar, sheetNum);
        agileProductConfigGeneric.setFixedAboveBar(isFixedAboveBar != null && "1".equalsIgnoreCase(isFixedAboveBar));

        String offsetCF = getCellDataAsString(row, columnNo++, sheetNum);
        addErrorIfNonNumeric(row.getRowNum(), getText(OFFSET_CF), offsetCF, sheetNum);
        addErrorIfNotIndependent(row.getRowNum(), offsetCF, code, sheetNum);
        if (code.equalsIgnoreCase(Product.INDEPENDENT_PRODUCT_CODE)) {
            agileProductConfigGeneric.setOffsetCF((offsetCF == null) ? BigDecimal.ZERO : new BigDecimal(offsetCF));
        } else {
            agileProductConfigGeneric.setOffsetCF(null);
        }

        String isUseInSmallGroupEval = getCellDataAsString(row, columnNo, sheetNum);
        addErrorIfNonBooleanValueProvided(row.getRowNum(), getText(IS_USE_IN_SG_EVALUATION), isUseInSmallGroupEval, sheetNum);
        agileProductConfigGeneric.setUseInSmallGroupEval("1".equalsIgnoreCase(isUseInSmallGroupEval));

        agileProductConfigGeneric.setDowOffset("1".equalsIgnoreCase("1"));

        agileProductConfigGeneric.setRoomClassOffset("1".equalsIgnoreCase("0"));

        agileProductConfigGeneric.setDtaOffset("1".equalsIgnoreCase("0"));

        try {
            agileProductConfigGeneric.setProductCode(ProductCodeID.valueOfKey(code));
        } catch (Exception e) {
            addError(sheetNum, row.getRowNum(), String.format(getText("no.such.code.found"), code));
        }

        String delimiter = getDelimiter(row, dynamicHeaders, sheetNum);

        if (delimiterForProduct.contains(delimiter)) {
            uploadedProductConfigList.putIfAbsent(delimiter, agileProductConfigGeneric);
        }
    }


    private void populateProductPackages(Row row, String[] dynamicHeaders, Map<String, List<ProductPackages>> uploadedProductPackageList, Set<String> delimiterForPackages, int sheetNum) {
        int columnNo = dynamicHeaders.length + 1;
        ProductPackages productPackage = new ProductPackages();

        String name = getCellDataAsString(row, columnNo++, sheetNum);
        addErrorIfEmpty(row.getRowNum(), getText(NAME), name, sheetNum);
        productPackage.setName(name);

        String description = getCellDataAsString(row, columnNo++, sheetNum);
        addErrorIfEmpty(row.getRowNum(), getText(DESCRIPTION), description, sheetNum);
        productPackage.setDescription(description);

        String chargeType = getCellDataAsString(row, columnNo++, sheetNum);
        addErrorIfNonNumeric(row.getRowNum(), getText(CHARGE_TYPE), chargeType, sheetNum);
        productPackage.setChargeType(Integer.parseInt(chargeType));

        String offsetMethod = getCellDataAsString(row, columnNo++, sheetNum);
        addErrorIfNonNumeric(row.getRowNum(), getText(OFFSET_METHOD), offsetMethod, sheetNum);
        productPackage.setOffsetMethod(AgileRatesOffsetMethod.valueOfId(Integer.parseInt(offsetMethod)));

        String offsetValue = getCellDataAsString(row, columnNo, sheetNum);
        addErrorIfNonNumeric(row.getRowNum(), getText(OFFSET_VALUE), offsetValue, sheetNum);
        addErrorIfEmpty(row.getRowNum(), getText(OFFSET_VALUE), offsetValue, sheetNum);
        productPackage.setOffsetValue(offsetValue != null ? new BigDecimal(offsetValue) : null);

        String delimiter = getDelimiter(row, dynamicHeaders, sheetNum);
        uploadedProductPackageList.computeIfAbsent(delimiter, key -> new ArrayList<>()).add(productPackage);
    }

    private String getDelimiter(Row row, String[] dynamicHeaders, int sheetNum) {
        String delimiter = "$#$";
        for (int col = 0; col < ((dynamicHeaders.length) + 1); col++) {
            delimiter = delimiter.concat(getCellDataAsString(row, col, sheetNum));
            delimiter = delimiter.concat("$#$");
        }
        return delimiter;
    }

    private void validateSeedRowData(Map<String, List<ProductSeedAttributeValues>> uploadedSeedValuesList, Row row, String[] dynamicHeaders, List<ProductSeedAttribute> seedAttributes, Set<String> delimiterForProduct, int sheetNum){
        if (!isEmptyRow(row)) {
            int colNo = 0;
            String delimiter = "$#$";
            for (colNo = 0; colNo < ((dynamicHeaders.length) + 1); colNo++) {
                delimiter = delimiter.concat(getCellDataAsString(row, colNo, sheetNum));
                delimiter = delimiter.concat("$#$");
            }
            colNo = 0;
            for (String dynamicHeader : dynamicHeaders) {
                ProductSeedAttributeValues seedAttributeValue = new ProductSeedAttributeValues();
                for (ProductSeedAttribute seedAttribute : seedAttributes) {
                    if (seedAttribute.getClientAttributeName().equals(dynamicHeader)) {
                        String cellData = getCellDataAsString(row, colNo++, sheetNum);
                        addErrorIfEmpty(row.getRowNum(), getText(dynamicHeader), cellData, sheetNum);
                        seedAttributeValue.setClientAttributeValue(cellData);
                        seedAttributeValue.setProductSeedAttributeID(seedAttribute.getId());
                    }
                }
                if (delimiterForProduct.contains(delimiter)) {
                    if (uploadedSeedValuesList.containsKey(delimiter)) {
                        uploadedSeedValuesList.get(delimiter).add(seedAttributeValue);
                    } else {
                        uploadedSeedValuesList.put(delimiter, new ArrayList<>());
                        uploadedSeedValuesList.get(delimiter).add(seedAttributeValue);
                    }
                }
            }
        }
    }

    private void populateProductOffset(Row row, String[] dynamicHeaders, Map<String, List<ProductOffset>> uploadedProductOffsetList, int sheetNum, Set<String> delimiterForSeason) {
        int columnNo = dynamicHeaders.length + 1;
        ProductOffset productOffset = new ProductOffset();
        String delimiter = getDelimiter(row, dynamicHeaders, sheetNum);

        String seasonName = getCellDataAsString(row, columnNo++, sheetNum);
        productOffset.setSeasonName(seasonName);

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd-MM-yyyy");

        String startDate = getCellDataAsString(row, columnNo++, sheetNum);
        if (startDate != null) {
            try {
                productOffset.setStartDate(LocalDate.parse(startDate, formatter));
            } catch (Exception e) {
                addError(sheetNum, row.getRowNum(), String.format(getText("enter.date.in.format"), startDate));
            }
        }

        String endDate = getCellDataAsString(row, columnNo++, sheetNum);
        if (endDate != null) {
            try {
                productOffset.setEndDate(LocalDate.parse(endDate, formatter));
            } catch (Exception e) {
                addError(sheetNum, row.getRowNum(), String.format(getText("enter.date.in.format"), endDate));
            }
        }

        String offsetMethod = getCellDataAsString(row, columnNo++, sheetNum);
        addErrorIfNonNumeric(row.getRowNum(), getText(OFFSET_METHOD), offsetMethod, sheetNum);
        try {
            productOffset.setOffsetMethod(AgileRatesOffsetMethod.valueOfId(Integer.parseInt(offsetMethod)));
        } catch (Exception e) {
            addError(sheetNum, row.getRowNum(), e.getMessage());
        }

        String isDowOffset = getCellDataAsString(row, columnNo, sheetNum);
        addErrorIfNonBooleanValueProvided(row.getRowNum(), getText(IS_DOW_OFFSET), isDowOffset, sheetNum);
        productOffset.setDowOffset("1".equalsIgnoreCase(isDowOffset));

        setRcAndDtaOffset(row, sheetNum, productOffset, getDelimiterString(dynamicHeaders, sheetNum, row, 0), delimiter);
        columnNo = seasonsColNameToIndexMap.get(MIN_SUN);

        String minSunday = getCellDataAsString(row, columnNo++, sheetNum);
        addErrorIfNonNumeric(row.getRowNum(), getText(MIN_SUN), minSunday, sheetNum);
        productOffset.setSundayFloor(minSunday != null ? new BigDecimal(minSunday) : null);

        String minMonday = getCellDataAsString(row, columnNo++, sheetNum);
        addErrorIfNonNumeric(row.getRowNum(), getText(MIN_MON), minMonday, sheetNum);
        productOffset.setMondayFloor(minMonday != null ? new BigDecimal(minMonday) : null);

        String minTuesday = getCellDataAsString(row, columnNo++, sheetNum);
        addErrorIfNonNumeric(row.getRowNum(), getText(MIN_TUES), minTuesday, sheetNum);
        productOffset.setTuesdayFloor(minTuesday != null ? new BigDecimal(minTuesday) : null);

        String minWednesday = getCellDataAsString(row, columnNo++, sheetNum);
        addErrorIfNonNumeric(row.getRowNum(), getText(MIN_WED), minWednesday, sheetNum);
        productOffset.setWednesdayFloor(minWednesday != null ? new BigDecimal(minWednesday) : null);

        String minThursday = getCellDataAsString(row, columnNo++, sheetNum);
        addErrorIfNonNumeric(row.getRowNum(), getText(MIN_THUR), minThursday, sheetNum);
        productOffset.setThursdayFloor(minThursday != null ? new BigDecimal(minThursday) : null);

        String minFriday = getCellDataAsString(row, columnNo++, sheetNum);
        addErrorIfNonNumeric(row.getRowNum(), getText(MIN_FRI), minFriday, sheetNum);
        productOffset.setFridayFloor(minFriday != null ? new BigDecimal(minFriday) : null);

        String minSaturday = getCellDataAsString(row, columnNo++, sheetNum);
        addErrorIfNonNumeric(row.getRowNum(), getText(MIN_SAT), minSaturday, sheetNum);
        productOffset.setSaturdayFloor(minSaturday != null ? new BigDecimal(minSaturday) : null);

        String maxSunday = getCellDataAsString(row, columnNo++, sheetNum);
        addErrorIfNonNumeric(row.getRowNum(), getText(MAX_SUN), maxSunday, sheetNum);
        productOffset.setSundayCeil(maxSunday != null ? new BigDecimal(maxSunday) : null);

        String maxMonday = getCellDataAsString(row, columnNo++, sheetNum);
        addErrorIfNonNumeric(row.getRowNum(), getText(MAX_MON), maxMonday, sheetNum);
        productOffset.setMondayCeil(maxMonday != null ? new BigDecimal(maxMonday) : null);

        String maxTuesday = getCellDataAsString(row, columnNo++, sheetNum);
        addErrorIfNonNumeric(row.getRowNum(), getText(MAX_TUES), maxTuesday, sheetNum);
        productOffset.setTuesdayCeil(maxTuesday != null ? new BigDecimal(maxTuesday) : null);

        String maxWednesday = getCellDataAsString(row, columnNo++, sheetNum);
        addErrorIfNonNumeric(row.getRowNum(), getText(MAX_WED), maxWednesday, sheetNum);
        productOffset.setWednesdayCeil(maxWednesday != null ? new BigDecimal(maxWednesday) : null);

        String maxThursday = getCellDataAsString(row, columnNo++, sheetNum);
        addErrorIfNonNumeric(row.getRowNum(), getText(MAX_THUR), maxThursday, sheetNum);
        productOffset.setThursdayCeil(maxThursday != null ? new BigDecimal(maxThursday) : null);

        String maxFriday = getCellDataAsString(row, columnNo++, sheetNum);
        addErrorIfNonNumeric(row.getRowNum(), getText(MAX_FRI), maxFriday, sheetNum);
        productOffset.setFridayCeil(maxFriday != null ? new BigDecimal(maxFriday) : null);

        String maxSaturday = getCellDataAsString(row, columnNo, sheetNum);
        addErrorIfNonNumeric(row.getRowNum(), getText(MAX_SAT), maxSaturday, sheetNum);
        productOffset.setSaturdayCeil(maxSaturday != null ? new BigDecimal(maxSaturday) : null);

        delimiterForSeason.add(delimiter);
        uploadedProductOffsetList.computeIfAbsent(delimiter, key -> new ArrayList<>()).add(productOffset);
    }

    private void setRcAndDtaOffset(Row row, int sheetNum, ProductOffset productOffset, String masterDelimiter, String delimiter) {
        String isRoomClassOffset = getCellDataAsString(row, seasonsColNameToIndexMap.get(IS_RC_OFFSET), sheetNum);
        addErrorIfNonNumeric(row.getRowNum(), getText(IS_RC_OFFSET), isRoomClassOffset, sheetNum);
        productOffset.setRoomClassOffset("1".equalsIgnoreCase(isRoomClassOffset));

        String isDtaOffset = getCellDataAsString(row, seasonsColNameToIndexMap.get(IS_DTA_OFFSET), sheetNum);
        addErrorIfNonNumeric(row.getRowNum(), getText(IS_DTA_OFFSET), isDtaOffset, sheetNum);
        productOffset.setDtaOffset("1".equalsIgnoreCase(isDtaOffset));

        String roomClass = getCellDataAsString(row, seasonsColNameToIndexMap.get(ROOM_CLASS), sheetNum);
        String minDta = getCellDataAsString(row, seasonsColNameToIndexMap.get(MIN_DTA), sheetNum);
        String maxDta = getCellDataAsString(row, seasonsColNameToIndexMap.get(MAX_DTA), sheetNum);

        if (productOffset.isRoomClassOffset() && Objects.isNull(roomClass)) {
            addError(sheetNum, row.getRowNum(), String.format(getText("room.class.not.found.for.rc.offset")));
        } else if (!productOffset.isRoomClassOffset() && Objects.nonNull(roomClass)) {
            addError(sheetNum, row.getRowNum(), String.format(getText("extra.room.class")));
        } else if (productOffset.isRoomClassOffset()) {
            productOffset.setAccomClass(roomClass);
            masterRoomClassOffsetMap.computeIfAbsent(masterDelimiter, key -> new HashSet<>()).add(roomClass);
            currentRoomClassOffsets.computeIfAbsent(getDelimiterPerSeasonAndOtherOffsets(delimiter, productOffset.getSeasonName(), Objects.isNull(minDta) ? "NULLDTA" : minDta),
                    key -> new HashSet<>()).add(roomClass);
        }

        if (productOffset.isDtaOffset() && Objects.isNull(minDta)) {
            addError(sheetNum, row.getRowNum(), String.format(getText("min.dta.not.found.for.dta.offset")));
        } else if (!productOffset.isDtaOffset() && Objects.nonNull(minDta)) {
            addError(sheetNum, row.getRowNum(), String.format(getText("extra.min.dta")));
        } else if (productOffset.isDtaOffset()) {
            productOffset.setMinDTA(Integer.valueOf(minDta));
            productOffset.setMaxDTA(Objects.isNull(maxDta) ? null : Integer.valueOf(maxDta));
            String dtaOffsetString = formDtaOffsetString(minDta, maxDta);
            masterDtaOffsetMap.computeIfAbsent(masterDelimiter, key -> new HashSet<>())
                    .add(dtaOffsetString);
            currentDtaOffsets.computeIfAbsent(getDelimiterPerSeasonAndOtherOffsets(delimiter, productOffset.getSeasonName(), Objects.isNull(roomClass) ? "NULLRC" : roomClass),
                    key -> new HashSet<>()).add(dtaOffsetString);
        }

    }

    private String getDelimiterPerSeasonAndOtherOffsets(String delimiter, String seasonName, String offsetString) {
        return delimiter + (Objects.isNull(seasonName) ? "NULLSEASON" : seasonName) + "$#$" +
                offsetString + "$#$";
    }

    public void checkIfAllOffsetsAreProvided(int sheetNum) {
            validateDtaRcOffsets(sheetNum, currentDtaOffsets, masterDtaOffsetMap);
            validateDtaRcOffsets(sheetNum, currentRoomClassOffsets, masterRoomClassOffsetMap);
    }

    private void validateDtaRcOffsets(int sheetNum, Map<String, Set<String>> currentOffsetsMap, Map<String, Set<String>> masterOffsetsMap) {
        currentOffsetsMap.forEach((key, value) -> {
            String trimKey = trimCurrentDelimiterToDynamicHeader(key);
            Set<String> masterDtas = masterOffsetsMap.get(trimKey);
            if (Objects.isNull(masterDtas) || !masterDtas.equals(value)) {
                addError(sheetNum, 99999, getText("inconsistent.rc.dta.offset",
                        key.replace("$#$", ", "), masterRoomClassOffsetMap.get(trimKey), masterDtaOffsetMap.get(trimKey)));
            }
        });
    }

    private String trimCurrentDelimiterToDynamicHeader(String key) {
        String reverseDelimiter = StringUtils.reverse(key);
        return StringUtils.reverse(reverseDelimiter.substring(StringUtils.ordinalIndexOf(reverseDelimiter, "$#$", 4), key.length()));
    }

    private boolean isBooleanValueProvided(String value) {
        return Objects.isNull(value) || Integer.toString(Constants.ZERO).equalsIgnoreCase(value) || Integer.toString(Constants.ONE).equalsIgnoreCase(value);
    }

    private boolean isNumericValueProvided(String value) {
        return Objects.isNull(value) || NumberUtils.isCreatable(value);
    }

    private boolean isEmptyRow(Row row) {
        for (Cell cell : row) {
            if (!isCellEmptyOrNull(cell)) {
                return false;
            }
        }
        return true;
    }

    private boolean isCellEmptyOrNull(Cell cell) {
        return (null == cell || StringUtils.isEmpty(cell.toString()));
    }

    private void validateHeader(Row row, String[] dynamicHeaders, int sheetNum) {
        String[] allowedHeaders = getProductHeaders(dynamicHeaders);
        int columnsNum = 0;
        if (row.getPhysicalNumberOfCells() != allowedHeaders.length) {
            addError(sheetNum, row.getRowNum(), getText(INVALID_COLUMNS_ERROR, allowedHeaders.length));
            return;
        }
        for (String header : allowedHeaders) {
            boolean validateColumnHeading = validateColumnHeading(header, getCellDataAsString(row, columnsNum++, sheetNum), row.getRowNum(), sheetNum);
            if (!validateColumnHeading) {
                break;
            }
        }
    }

    private void validatePackageHeader(Row row, String[] dynamicHeaders, int sheetNum) {
        String[] allowedHeaders = getPackageHeaders(dynamicHeaders);
        int columnsNum = 0;
        if (row.getPhysicalNumberOfCells() != allowedHeaders.length) {
            addError(sheetNum, row.getRowNum(), getText(INVALID_COLUMNS_ERROR, allowedHeaders.length));
            return;
        }
        for (String header : allowedHeaders) {
            boolean validateColumnHeading = validateColumnHeading(header, getCellDataAsString(row, columnsNum++, sheetNum), row.getRowNum(), sheetNum);
            if (!validateColumnHeading) {
                break;
            }
        }
    }

    private void validateOffsetHeader(Row row, String[] dynamicHeaders, int sheetNum) {
        String[] allowedHeaders = getOffsetHeaders(dynamicHeaders);
        int columnsNum = 0;
        if (row.getPhysicalNumberOfCells() != allowedHeaders.length) {
            addError(sheetNum, row.getRowNum(), getText(INVALID_COLUMNS_ERROR, allowedHeaders.length));
            return;
        }
        for (String header : allowedHeaders) {
            boolean validateColumnHeading = validateColumnHeading(header, getCellDataAsString(row, columnsNum++, sheetNum), row.getRowNum(), sheetNum);
            if (!validateColumnHeading) {
                break;
            }
        }
    }

    private boolean validateColumnHeading(String expectedValue, String actualValue, int rowNum, int sheetNum) {
        if (expectedValue.equalsIgnoreCase(actualValue)) {
            return true;
        }
        addError(sheetNum, rowNum, getText("column.headings.incorrect"));
        return false;
    }

    private String getCellDataAsString(Row row, int columnNo, int sheetNum) {
        try {
            Cell cell = row.getCell(columnNo, Row.RETURN_BLANK_AS_NULL);
            if (isCellEmptyOrNull(cell)) {
                return null;
            }
            cell.setCellType(Cell.CELL_TYPE_STRING);
            DataFormatter formatter = new DataFormatter();
            String cellValue = formatter.formatCellValue(cell).trim();
            if (cellValue.equalsIgnoreCase("NULL")) {
                return null;
            }
            return cellValue;
        } catch (Exception e) {
            addError(sheetNum, row.getRowNum(), getText(FORMAT_ERROR));
            LOGGER.error(getText(FORMAT_ERROR) + row.getRowNum() + e.getMessage(), e);
            return null;
        }
    }


    public Map<Integer, List<String>> getErrors(int i) {
        if (errors.get(i) != null) {
            return errors.get(i).getAllErrors();
        }
        return null;
    }

    public void addError(int sheetNum, Integer rowNum, String errorDescription) {
        errors.putIfAbsent(sheetNum, new Errors());

        errors.get(sheetNum).getAllErrors().putIfAbsent(rowNum, new ArrayList<>());
        if (!errors.get(sheetNum).getAllErrors().get(rowNum).contains(errorDescription)) {
            errors.get(sheetNum).getAllErrors().get(rowNum).add(errorDescription);
        }
    }

    public void createErrorMap(int sheetNum) {
        errors.putIfAbsent(sheetNum, new Errors());
    }

    public String[] getProductHeaders(String[] dynamicHeaders) {

        String[] headerPart1 = dynamicHeaders;

        String[] headerPart2 = new String[]{NAME, CODE, STATUS_ID, IS_UPLOAD, CENTRALLY_MANAGED, DESCRIPTION, DEPENDENT_PRODUCT_NAME, TYPE,
                IS_OPTIMIZED, IS_SEASONAL_PRODUCT_ONLY, PRICE_ROUNDING_RULE, FLOOR_TYPE, PRODUCT_FLOOR_RATE, FLOOR_PERCENTAGE, CURRENCY_CODE, MINIMUM_PRICE_CHANGE,
                IS_OVERRIDABLE, IS_PUBLICALLY_AVAILABLE, IS_OFFSET_FOR_EXTRA_ADULT, IS_OFFSET_FOR_EXTRA_CHILD, RATE_CODES, ROOM_TYPES, MIN_DTA, MAX_DTA,
                MIN_LOS, MAX_LOS, TO_PRODUCT_HIERARCHY, MIN_ROOMS, MAX_ROOMS, CHILD_PRICING_TYPE, RATE_SHOPPING_LOS_MIN, RATE_SHOPPING_LOS_MAX, WEBRATE_TYPE,
                WEBRATE_LOS_TYPE, WEBRATE_LOS_MIN, WEBRATE_LOS_MAX, SEND_ADJUSTMENT, SYSTEM_DEFAULT, SET_RESTRICTIONS, EXTRA_RATE_CODES_FOR_RESTRICTIONS,
                IS_FIXED_ABOVE_BAR, OFFSET_CF, IS_USE_IN_SG_EVALUATION};


        String[] headers = new String[headerPart1.length + headerPart2.length];
        System.arraycopy(headerPart1, 0, headers, 0, headerPart1.length);
        System.arraycopy(headerPart2, 0, headers, headerPart1.length, headerPart2.length);

        return headers;
    }

    public String[] getPackageHeaders(String[] dynamicHeaders) {

        String[] headerPart1 = dynamicHeaders;

        String[] headerPart2 = new String[]{PRODUCT_NAME, NAME, DESCRIPTION, CHARGE_TYPE, OFFSET_METHOD, OFFSET_VALUE};

        String[] headers = new String[headerPart1.length + headerPart2.length];
        System.arraycopy(headerPart1, 0, headers, 0, headerPart1.length);
        System.arraycopy(headerPart2, 0, headers, headerPart1.length, headerPart2.length);

        return headers;
    }

    public String[] getOffsetHeaders(String[] dynamicHeaders) {

        String[] headerPart1 = dynamicHeaders;

        String[] headerPart2 =  new String[]{ PRODUCT_NAME, SEASON_NAME, START_DATE,END_DATE,OFFSET_METHOD,IS_DOW_OFFSET,
                IS_RC_OFFSET, IS_DTA_OFFSET, ROOM_CLASS, MIN_DTA, MAX_DTA, MIN_SUN,
                MIN_MON,MIN_TUES,MIN_WED,MIN_THUR,MIN_FRI,MIN_SAT,MAX_SUN,MAX_MON,MAX_TUES,MAX_WED,MAX_THUR, MAX_FRI,MAX_SAT};

        String[] headers = new String[headerPart1.length + headerPart2.length];
        System.arraycopy(headerPart1, 0, headers, 0, headerPart1.length);
        System.arraycopy(headerPart2, 0, headers, headerPart1.length, headerPart2.length);

        return headers;
    }

    private boolean productContainsPackage(ProductConfig productConfig) {
        return AGILE_RATES_PACKAGED_PRODUCT_TYPE_ENUM_LIST.contains(AgileRatesProductTypeEnum.fromValue(productConfig.getType()));
    }


}
