package com.ideas.tetris.ui.modules.functionspace.configuration.forecastlevels;

import com.ideas.tetris.pacman.services.functionspace.configuration.entity.FunctionSpaceForecastLevel;
import com.ideas.tetris.pacman.services.functionspace.configuration.service.FunctionSpaceConfigurationService;
import com.ideas.tetris.platform.common.xml.schema.workcontext.v1.WorkContextType;
import com.ideas.tetris.ui.common.cdi.TetrisPresenter;

import javax.inject.Inject;
import org.springframework.beans.factory.annotation.Autowired;
import com.ideas.tetris.spring.SpringAutowired;

@SpringAutowired
public class GroupPricingForecastLevelsPresenter extends TetrisPresenter<GroupPricingForecastLevelsView, Boolean> {

    @Autowired
	private FunctionSpaceConfigurationService service;
    private FunctionSpaceForecastLevel forecastLevel;
    private ForecastLevelMinMaxBean forecastLevelMinMaxBean;

    @Override
    public void onViewOpened(Boolean isGroupPricing) {
        init();
    }

    @Override
    public void onWorkContextChange(WorkContextType workContextType) {
        init();
    }

    private void init() {
        //retrieve the forecast min and max values
        view.populateSliderValue(getForecastLevels());
    }

    private ForecastLevelBean getForecastLevels() {
        ForecastLevelBean forecastLevelBean = new ForecastLevelBean();
        forecastLevelMinMaxBean = new ForecastLevelMinMaxBean();

        if (!mockModeEnabled) {
            //go out to the service and find min and max and then populate min and max on the forecastLevelMinMaxBean
            forecastLevel = service.getForecastLevel();
            if (forecastLevel != null) {
                forecastLevelMinMaxBean.setMin(forecastLevel.getMin());
                forecastLevelMinMaxBean.setMax(forecastLevel.getMax());
            }
        }

        forecastLevelBean.setForecastLevelMinMaxBean(forecastLevelMinMaxBean);

        return forecastLevelBean;
    }

    public void save() {
        if (mockModeEnabled) {
            showSaveSuccessMessage();
            init();
        } else {
            //pull the min and max from the forecastLevelBean and pass it to the service
            if (forecastLevel == null) {
                forecastLevel = new FunctionSpaceForecastLevel();
            }

            forecastLevel.setMin(forecastLevelMinMaxBean.getMin());
            forecastLevel.setMax(forecastLevelMinMaxBean.getMax());

            service.saveForecestLevel(forecastLevel);
            showSaveSuccessMessage();
            init();
        }
    }

}
