package com.ideas.tetris.ui.modules.functionspace.configuration;


import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.configparams.GUIConfigParamName;
import com.ideas.tetris.pacman.common.configparams.IPConfigParamName;
import com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.services.pricingconfiguration.service.PricingConfigurationService;
import com.ideas.tetris.pacman.services.servicingcostbylos.service.ServicingCostByLOSService;
import com.ideas.tetris.platform.common.xml.schema.workcontext.v1.WorkContextType;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import com.ideas.tetris.ui.common.cdi.TetrisPresenter;
import com.ideas.tetris.ui.modules.pricingconfiguration.PricingConfigurationChangedEvent;
import org.apache.commons.lang.StringUtils;

import javax.enterprise.event.Observes;
import javax.enterprise.event.Reception;
import javax.inject.Inject;
import org.springframework.beans.factory.annotation.Autowired;
import com.ideas.tetris.spring.SpringAutowired;

@SpringAutowired
public class FunctionSpaceConfigurationPresenter extends TetrisPresenter<FunctionSpaceConfigurationView, Void> {

    @Autowired
    PacmanConfigParamsService configParamsService;

    @Autowired
	private PricingConfigurationService pricingConfigurationService;

    @Autowired
	private ServicingCostByLOSService servicingCostByLOSService;
    private boolean isBaseAccomTypeSetupComplete;


    @Override
    public void onViewInit() {
    }

    @Override
    public void onViewOpened(Void aVoid) {
        init();
    }

    @Override
    public void onWorkContextChange(WorkContextType workContextType) {
        init();
    }

    private void init() {
        getBaseAccomTypeSetupComplete();
        view.afterPresenterInit();
        //load data from backend here
        view.updatePackageVisibility(isPackageEnabled());
        view.setAccomTypeConfigTabVisibility(true, !isCPEnabled());
        view.enableTabs(isBaseAccomTypeSetupComplete);
        view.setMinimumProfitMarginTabVisibility(isMinimumProfitMarginEnabled());
        view.ancillaryTabHide(isAncillaryTabHide());

    }

    protected boolean isMinimumProfitMarginEnabled() {
        return configParamsService.getBooleanParameterValue(PreProductionConfigParamName.GROUP_PRICING_MIN_PROFIT_ENABLED.value());
    }

    public boolean isGpHideAncillaryEnabled() {
        return configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.GP_HIDE_ANCILLIARY_TABS_CONFIGURATION_PROFIT_OPTIMIZATION);
    }

    public boolean isCPEnabled() {
        return configParamsService.getBooleanParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED.value());
    }

    public boolean isDailyBAREnabled() {
        return Constants.BAR_DECISION_VALUE_LOS.equals(configParamsService.getParameterValue(IPConfigParamName.BAR_BAR_DECISION.value()));
    }

    public boolean isBARByLOSEnabled() {
        return configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.DAILY_BAR_CONFIGURATION_ENABLED.value());
    }

    public boolean isPackageEnabled() {
        return configParamsService.getBooleanParameterValue(GUIConfigParamName.IS_FUNCTION_SPACE_PACKAGE_ENABLED.value());
    }

    public void refreshTabs(@Observes(notifyObserver = Reception.IF_EXISTS) PricingConfigurationChangedEvent event) {
        getBaseAccomTypeSetupComplete();
        view.enableTabs(isBaseAccomTypeSetupComplete);
    }

    private void getBaseAccomTypeSetupComplete() {
        isBaseAccomTypeSetupComplete = pricingConfigurationService.isBaseAccomTypeSetupComplete();
    }

    public boolean isAncillaryTabHide() {
        if (isGpHideAncillaryEnabled() && servicingCostByLOSService.isProfitOptimizationEnabled()) {
            return true;
        }
        return false;
    }

    public boolean isFunctionSpaceStatusCodesTabEnabled() {
        return StringUtils.isNotEmpty(configParamsService.getParameterValue(IPConfigParamName.SALES_AND_CATERING_FUNCTION_SPACE_CLIENT_ALIAS));
    }

    @Override
    public boolean hasChanges() {
        return view.tabHasChanges();
    }
}
