package com.ideas.tetris.ui.modules.marketsegment.common;

import com.google.common.annotations.VisibleForTesting;
import com.ideas.infra.tetris.security.domain.TetrisPermissionKey;
import com.ideas.tetris.pacman.services.marketsegment.entity.ForecastActivityType;
import com.ideas.tetris.pacman.services.marketsegment.entity.MktSeg;
import com.ideas.tetris.pacman.services.marketsegment.service.AnalyticalMarketSegment;
import com.ideas.tetris.pacman.services.marketsegment.service.AnalyticalMarketSegmentAttribute;
import com.ideas.tetris.pacman.services.product.Product;
import com.ideas.tetris.ui.common.TetrisComponentFactory;
import com.ideas.tetris.ui.common.component.TetrisBeanItemContainer;
import com.ideas.tetris.ui.common.component.TetrisHierarchicalBeanItemContainer;
import com.ideas.tetris.ui.common.component.table.TetrisChangeAwareComponentColumnGenerator;
import com.ideas.tetris.ui.common.component.table.TetrisRowEditColumnComponentProvider;
import com.ideas.tetris.ui.common.component.table.TetrisRowEditTable;
import com.ideas.tetris.ui.common.component.textfield.TetrisTextField;
import com.ideas.tetris.ui.common.component.window.TetrisWindow;
import com.ideas.tetris.ui.common.util.ChangeAware;
import com.ideas.tetris.ui.common.util.LocalizationDimensionSpecifier;
import com.ideas.tetris.ui.common.util.UiUtils;
import com.ideas.tetris.ui.modules.marketsegment.AttributeAssignmentNonAmsPresenter;
import com.ideas.tetris.ui.modules.marketsegment.AttributeAssignmentPresenter;
import com.ideas.tetris.ui.modules.marketsegment.MarketSegmentDetailColumnRenderer;
import com.ideas.tetris.ui.modules.marketsegment.common.view.AttributeAssignmentConfig;
import com.ideas.tetris.ui.modules.marketsegment.common.view.AttributeAssignmentLayout;
import com.ideas.tetris.ui.modules.marketsegment.common.view.AttributeBean;
import com.ideas.tetris.ui.modules.marketsegment.common.view.ProductNameConfigurationLayout;
import com.ideas.tetris.ui.modules.marketsegment.forecastgroup.MktSegDetailsDto;
import com.ideas.tetris.ui.modules.marketsegment.ratecode.UnassignAttributeConfirmationPopup;
import com.vaadin.ui.HorizontalLayout;
import com.vaadin.v7.ui.Table;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static java.util.Objects.nonNull;

public class EditAttributeAssignmentPopup extends HorizontalLayout {

    private static final String DEF = "_DEF";
    private static TetrisWindow popup;
    private static final int BOOKING_BLOCK_PCT_IS_BLOCK = 100;
    private boolean independentProductsEnabled;

    public EditAttributeAssignmentPopup(final AttributeAssignmentPresenter presenter, final AMSAssignedSummaryUIWrapper wrapper) {
        setSpacing(true);
        addStyleName("tetris-top-padding-off");

        addAttributeAssignmentTreeTable(wrapper);

        Set<String> rateCodes = wrapper.getChildren().stream().map(AMSAssignedSummaryUIWrapper::getRateCode).collect(Collectors.toSet());

        AttributeBean bean = new AttributeBean();
        bean.setAttribute(wrapper.getAttribute());
        bean.setForecastActivityType(wrapper.getForecastActivityType());
        bean.setComplimentary(wrapper.isComplimentary());
        bean.setProduct(wrapper.getBaseProduct());

        independentProductsEnabled = presenter.isIndependentProductsEnabled();
        AttributeAssignmentConfig config = AttributeAssignmentConfig.builder()
                .withAmsView(AmsView.AMS_VIEW_EDIT)
                .withComplimentaryFeatureEnabled(presenter.isComplimentaryAttributeEnabled())
                .withForecastActivityTypes(presenter.getForecastActivityTypes())
                .withIndependentProductsEnabled(independentProductsEnabled)
                .withMaxProducts(presenter.getMaxIndependentProducts())
                .withGroupOptionEnabled(presenter.isGroupOptionEnabled())
                .withEditable(true)
                .withMarketSegmentUnderEdit(wrapper.getAnalyticalMarketSegment().getMarketCode())
                .withOriginallySelectedRateCodes(rateCodes)
                .withOriginallySelectedAttribute(wrapper.getAttribute())
                .withOriginallySelectedBaseProduct(wrapper.getBaseProduct() == null ? "" : wrapper.getBaseProduct().getName())
                .build();
        AttributeAssignmentLayout attributeAssignmentLayout = createAttributeAssignmentLayout(presenter, bean, config);
        addComponent(attributeAssignmentLayout);
        attributeAssignmentLayout.onAssign(attributeBean -> {
            if (nonNull(attributeBean.getProduct()) && !attributeBean.getProduct().isPersisted()) {
                ProductNameConfigurationLayout.show(attributeBean.getProduct(), attributeAssignmentLayout.getProducts(), () -> assign(presenter, wrapper, config, attributeBean));
            } else {
                assign(presenter, wrapper, config, attributeBean);
            }
        });
    }

    private void assign(AttributeAssignmentPresenter presenter, AMSAssignedSummaryUIWrapper wrapper, AttributeAssignmentConfig config, AttributeBean attributeBean) {
        List<AnalyticalMarketSegment> analyticalMarketSegmentList = new ArrayList<>();
        ForecastActivityType forecastActivityType = attributeBean.getForecastActivityType();
        wrapper.setForecastActivityType(forecastActivityType);

        for (AMSAssignedSummaryUIWrapper amsAssignedSummaryUIWrapper : wrapper.getChildren()) {
            AnalyticalMarketSegment analyticalMarketSegmentChild = amsAssignedSummaryUIWrapper.getAnalyticalMarketSegment();
            analyticalMarketSegmentChild.setForecastActivityType(forecastActivityType);
            analyticalMarketSegmentList.add(analyticalMarketSegmentChild);
            analyticalMarketSegmentChild.setComplimentary(attributeBean.isComplimentary());
        }
        AnalyticalMarketSegment analyticalMarketSegment = wrapper.getAnalyticalMarketSegment();
        analyticalMarketSegment.setForecastActivityType(forecastActivityType);
        analyticalMarketSegment.setBlockPercent(BOOKING_BLOCK_PCT_IS_BLOCK);
        analyticalMarketSegment.setComplimentary(attributeBean.isComplimentary());

        analyticalMarketSegmentList.add(analyticalMarketSegment);
        final boolean separableIdenticalMsAttributesEnabled = presenter.isSeparableIdenticalMsAttributesEnabled();
        final boolean hasChildren = wrapper.hasChildren();
        final boolean identicalMsAttributeAlreadyPresent = presenter.isIdenticalMsAttributeAlreadyPresent(wrapper, attributeBean.getAttribute());
        if (hasChildren && identicalMsAttributeAlreadyPresent && !analyticalMarketSegment.getAttribute().equals(attributeBean.getAttribute())) {
            if (separableIdenticalMsAttributesEnabled) {
                EditAssignIdenticalMarketSegmentAttributesPopup identicalMarketSegmentAttributesPopup = new EditAssignIdenticalMarketSegmentAttributesPopup(
                        presenter, analyticalMarketSegmentList, attributeBean.getAttribute(), attributeBean.isNone() && presenter.isWashDataPresentFor(config.getMarketSegmentUnderEdit()), attributeBean.getProduct());
                TetrisWindow tetrisWindow = new TetrisWindow(identicalMarketSegmentAttributesPopup.getWindowCaption());
                identicalMarketSegmentAttributesPopup.showPopup(tetrisWindow);
            } else {
                List<AMSAssignedSummaryUIWrapper> values = new ArrayList<>();
                if (wrapper.getRateCode() == null) {
                    values.add(wrapper);
                    values.addAll(wrapper.getChildren());
                    UnassignAttributeConfirmationPopup.showConfirmation(presenter, values, wrapper, true, true, false, attributeBean.getAttribute(), analyticalMarketSegmentList);
                } else {
                    values.add(wrapper);
                    UnassignAttributeConfirmationPopup.showConfirmation(presenter, values, wrapper, true, false, false, attributeBean.getAttribute(), analyticalMarketSegmentList);
                }
            }
        } else {
            presenter.updateMappedCode(analyticalMarketSegmentList, attributeBean.getAttribute(), attributeBean.getProduct());
            if (attributeBean.isNone() && presenter.isWashDataPresentFor(config.getMarketSegmentUnderEdit())) {
                presenter.deleteFutureWashDataForMarketSegment(analyticalMarketSegment.getMarketCode());
            }
        }
        if (presenter.isIndependentProductsEnabled()) {
            presenter.createWebRateMappingForNewIP(attributeBean.getProduct());
        }
        popup.close();
    }


    private void addAttributeAssignmentTreeTable(AMSAssignedSummaryUIWrapper wrapper) {
        AttributeAssignmentTreeTable assignedTable = new AttributeAssignmentTreeTable();
        TetrisHierarchicalBeanItemContainer<AMSAssignedSummaryUIWrapper> container = new TetrisHierarchicalBeanItemContainer<>(AMSAssignedSummaryUIWrapper.class);
        container.addBean(wrapper);
        if (wrapper.hasChildren()) {
            container.addAll(wrapper.getChildren());
        }

        assignedTable.setContainerDataSource(container);

        String mappedCodeId = "mappedCode";
        String attributeId = "attributeDescription";
        String marketSegmentId = "marketSegment";
        String rateCodeId = "rateCode";

        assignedTable.setBlankInChildrenColumns(new String[]{
                mappedCodeId,
                attributeId,
                marketSegmentId
        });

        assignedTable.setVisibleColumns(new String[]{
                mappedCodeId,
                attributeId,
                marketSegmentId,
                rateCodeId
        });

        assignedTable.addGeneratedColumn(attributeId, (Table.ColumnGenerator) (source, itemId, columnId) -> {
            AMSAssignedSummaryUIWrapper summaryUIWrapper = (AMSAssignedSummaryUIWrapper) itemId;
            String attributeDescription = summaryUIWrapper.getAttributeDescription();
            if (attributeDescription != null) {
                attributeDescription = AttributeDetails.getAttributeValue(attributeDescription, independentProductsEnabled);
            }
            return attributeDescription;
        });

        assignedTable.addGeneratedColumn(rateCodeId, (Table.ColumnGenerator) (source, itemId, columnId) -> {
            AMSAssignedSummaryUIWrapper summaryUIWrapper = (AMSAssignedSummaryUIWrapper) itemId;
            if (summaryUIWrapper.getRateCodeType() != null && summaryUIWrapper.getRateCode() != null) {
                return summaryUIWrapper.getRateCodeType() + " '" + summaryUIWrapper.getRateCode() + "'";
            }
            return summaryUIWrapper.getRateCode();
        });

        assignedTable.setColumnHeaders(UiUtils.getText("market.segment.header"), UiUtils.getText("attributes.header"), UiUtils.getText("original.newline.market.segment.header"), UiUtils.getText("rate.code.newline.header"));

        assignedTable.setColumnWidth(mappedCodeId, LocalizationDimensionSpecifier.forCurrentLocale().getAttributePopupMappedCodeWidth());
        assignedTable.setColumnWidth(marketSegmentId, LocalizationDimensionSpecifier.forCurrentLocale().getAttributeAssignmentMarketSegmentWidth());
        assignedTable.setColumnWidth(rateCodeId, 100);

        addComponent(assignedTable);
    }


    public static TetrisWindow show(AttributeAssignmentPresenter presenter, AMSAssignedSummaryUIWrapper wrapper) {
        popup = new TetrisWindow(UiUtils.getText("edit.assigned.market.segment"));
        popup.setId("editAssignedMarketSegmentPopupWindow");
        popup.center();
        boolean isMarketSegmentSplit = wrapper.hasChildren() || wrapper.getMappedCode().endsWith(DEF);
        presenter.setEditableMarketSegmentSplit(isMarketSegmentSplit);
        popup.setContent(new EditAttributeAssignmentPopup(presenter, wrapper));
        popup.show();
        return popup;
    }

    public static TetrisWindow show(AttributeAssignmentNonAmsPresenter presenter, MktSegDetailsDto mktSegDetailsDto) {
        popup = new TetrisWindow(UiUtils.getText("editMS") + " " + mktSegDetailsDto.getName());
        popup.center();
        popup.setContent(new EditAttributeAssignmentPopup(presenter, mktSegDetailsDto));
        popup.show();
        return popup;
    }

    private AttributeAssignmentLayout createAttributeAssignmentLayout(AttributeAssignmentComponentAware presenter, AttributeBean bean, AttributeAssignmentConfig config) {
        AttributeAssignmentLayout layout = new AttributeAssignmentLayout(config, presenter);
        layout.setProducts(presenter.getExistingIndependentProducts());
        layout.setAttribute(bean);
        layout.setWidth(300, Unit.PIXELS);
        return layout;
    }


    private EditAttributeAssignmentPopup(AttributeAssignmentNonAmsPresenter presenter, MktSegDetailsDto mktSegDetailsDto) {
        AnalyticalMarketSegmentAttribute attribute = MarketSegmentDetailColumnRenderer.getAttribute(mktSegDetailsDto);
        attribute = isEqualToBaseProductAttribute(presenter, mktSegDetailsDto, attribute);

        TetrisRowEditTable rowEditTable = getTetrisRowEditTable(mktSegDetailsDto, presenter);
        rowEditTable.setEnabledRequirements(true, TetrisPermissionKey.MARKET_SEGMENTS);
        AttributeBean bean = new AttributeBean();
        bean.setAttribute(attribute);
        bean.setForecastActivityType(mktSegDetailsDto.getForecastActivityType());
        bean.setComplimentary(mktSegDetailsDto.isComplimentary());
        bean.setProduct(mktSegDetailsDto.getProduct());

        AttributeAssignmentConfig config = AttributeAssignmentConfig.builder()
                .withAmsView(AmsView.AMS_VIEW_EDIT)
                .withComplimentaryFeatureEnabled(presenter.isComplimentaryAttributeEnabled())
                .withForecastActivityTypes(presenter.getForecastActivityTypes())
                .withIndependentProductsEnabled(presenter.isIndependentProductsEnabled())
                .withMaxProducts(presenter.getMaxIndependentProducts())
                .withGroupOptionEnabled(presenter.isGroupOptionEnabled())
                .withEditable(mktSegDetailsDto.isEditable())
                .withOriginallySelectedBaseProduct(mktSegDetailsDto.getProduct() == null ?
                        "" : mktSegDetailsDto.getProduct().getName())
                .build();

        AttributeAssignmentLayout attributeAssignmentLayout = createAttributeAssignmentLayout(presenter, bean, config);

        attributeAssignmentLayout.onAssign(attributeToSave -> {
            if (nonNull(attributeToSave.getProduct()) && !attributeToSave.getProduct().isPersisted()) {
                ProductNameConfigurationLayout.show(attributeToSave.getProduct(), attributeAssignmentLayout.getProducts(), () -> assign(presenter, mktSegDetailsDto, rowEditTable, attributeToSave));
            } else {
                assign(presenter, mktSegDetailsDto, rowEditTable, attributeToSave);
            }
        });

        HorizontalLayout horizontalLayout = TetrisComponentFactory.createHorizontalLayout(rowEditTable, attributeAssignmentLayout);
        horizontalLayout.setMargin(true);
        horizontalLayout.setSpacing(true);
        addComponent(horizontalLayout);
    }

    private void assign(AttributeAssignmentNonAmsPresenter presenter, MktSegDetailsDto mktSegDetailsDto, TetrisRowEditTable rowEditTable, AttributeBean attributeToSave) {
        List<String> rateCodesForMktSeg = presenter.isIndependentProductsEnabled() &&
                shouldTransferRateCodes(mktSegDetailsDto, attributeToSave) ?
                presenter.getRateCodesForMktSeg(mktSegDetailsDto.getCode()) : null;

        if (rateCodesForMktSeg != null && !rateCodesForMktSeg.isEmpty()) {
            UnassignAttributeConfirmationPopup.showConfirmation(rateCodesForMktSeg,
                    attributeToSave.getProduct() != null ? attributeToSave.getProduct().getName() : null,
                    () -> saveAttribute(presenter, rowEditTable, attributeToSave, mktSegDetailsDto, rateCodesForMktSeg), presenter.isAmsRebuildAndBackfillInSync());
        } else {
            saveAttribute(presenter, rowEditTable, attributeToSave, mktSegDetailsDto, null);
        }
    }

    protected boolean shouldTransferRateCodes(MktSegDetailsDto mktSegDetailsDto, AttributeBean attributeToSave) {
        if (mktSegDetailsDto.getProduct() == null && attributeToSave.getProduct() == null)
            return false;

        if (mktSegDetailsDto.getProduct() == null && attributeToSave.getProduct() != null && attributeToSave.getProduct().isSystemDefault())
            return false;

        if (mktSegDetailsDto.getProduct() != null && mktSegDetailsDto.getProduct().isSystemDefault() && attributeToSave.getProduct() == null)
            return false;

        if (mktSegDetailsDto.getProduct() != null && attributeToSave.getProduct() != null &&
                mktSegDetailsDto.getProduct().getName().equals(attributeToSave.getProduct().getName()))
            return false;

        return true;
    }

    private static void saveAttribute(AttributeAssignmentNonAmsPresenter presenter, TetrisRowEditTable rowEditTable,
                                      AttributeBean attributeToSave, MktSegDetailsDto mktSegDetailsDto,
                                      List<String> rateCodes) {
        if (rateCodes != null && !rateCodes.isEmpty()) {
            Product product = attributeToSave.getProduct();
            if (product != null && !product.isPersisted()) {
                Product persistedProduct = presenter.createProduct(product);
                attributeToSave.setProduct(persistedProduct);
                product = persistedProduct;
            }

            if (product != null && product.isIndependentProduct()) {
                presenter.updateRateCodes(mktSegDetailsDto.getCode(), product, rateCodes);
            } else
                presenter.updateRateCodes(mktSegDetailsDto.getCode(), null, rateCodes);
        }
        if (attributeToSave.isNone() && presenter.isWashDataPresentFor(mktSegDetailsDto.getCode())) {
            presenter.deleteFutureWashDataForMarketSegment(mktSegDetailsDto.getMarketSegment().getCode());
        }
        mktSegDetailsDto.setComplimentary(attributeToSave.isComplimentary());
        mktSegDetailsDto.setProduct(attributeToSave.getProduct());
        mktSegDetailsDto.getMarketSegment().setComplimentary(mktSegDetailsDto.isComplimentary());
        presenter.save(mktSegDetailsDto, attributeToSave.getAttribute(), attributeToSave.getForecastActivityType());
        rowEditTable.reset();
        popup.close();
    }

    private AnalyticalMarketSegmentAttribute isEqualToBaseProductAttribute(AttributeAssignmentNonAmsPresenter presenter, MktSegDetailsDto mktSegDetailsDto, AnalyticalMarketSegmentAttribute attribute) {
        Integer fencedPackagedValue = Integer.valueOf(1);
        if ((mktSegDetailsDto.getFenced() == fencedPackagedValue) || (mktSegDetailsDto.getPackageValue() == fencedPackagedValue)) {
            return attribute;
        }
        if (presenter.isEqualToBaseProduct(mktSegDetailsDto.getMarketSegment().getCode())) {
            attribute = AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR;
            mktSegDetailsDto.setIsIP(1);
        }
        return attribute;
    }

    private TetrisRowEditTable getTetrisRowEditTable(MktSegDetailsDto mktSegDetailsDto, AttributeAssignmentNonAmsPresenter presenter) {

        TetrisRowEditTable rowEditTable = new TetrisRowEditTable(new TetrisRowEditTable.RowEditActionHandler() {
            @Override
            public boolean onPreConfirmedDelete(TetrisRowEditColumnComponentProvider columnComponentProvider) {
                return false;
            }

            @Override
            public void onConfirmedDelete(TetrisRowEditColumnComponentProvider columnComponentProvider) {

            }

            @Override
            public void onValidSave(TetrisRowEditColumnComponentProvider columnComponentProvider) {
                MktSeg marketSegment = ((MktSegDetailsDtoWrapper) columnComponentProvider).getMktSegDetailsDto().getMarketSegment();
                presenter.save(marketSegment);
                //update table as well.
                presenter.refreshTable();
            }

            @Override
            public TetrisRowEditColumnComponentProvider newItem() {
                return null;
            }
        });
        TetrisBeanItemContainer<MktSegDetailsDtoWrapper> container = new TetrisBeanItemContainer<>(MktSegDetailsDtoWrapper.class);
        container.addItem(new MktSegDetailsDtoWrapper(mktSegDetailsDto));

        String name = "mktSegDetailsDto.marketSegment.name";
        String attribute = "ATTRIBUTE";
        String originalMarketSegment = "mktSegDetailsDto.marketSegment.code";
        container.addNestedContainerBean("mktSegDetailsDto");
        container.addNestedContainerBean("mktSegDetailsDto.marketSegment");
        rowEditTable.setContainerDataSource(container);

        rowEditTable.addGeneratedColumn(attribute, (Table.ColumnGenerator) (table, o, o1) -> {
            MktSegDetailsDtoWrapper wrapper = (MktSegDetailsDtoWrapper) o;
            String attributeValue = MarketSegmentDetailColumnRenderer.renderAttribute(wrapper.getMktSegDetailsDto());
            return AttributeDetails.getAttributeValue(attributeValue, presenter.isIndependentProductsEnabled());

        });

        rowEditTable.addGeneratedColumn(name, new TetrisChangeAwareComponentColumnGenerator() {
            @Override
            public ChangeAware generateComponent(Table source, Object itemId, Object columnId) {
                TetrisTextField tetrisTextField = new TetrisTextField();
                tetrisTextField.addValidator(new MarketSegmentNameValidator(((MktSegDetailsDtoWrapper) itemId).getMktSegDetailsDto().getMarketSegment().getName(), presenter.getMarketSegmentList()));
                return tetrisTextField;
            }
        });
        rowEditTable.addRowEditActionColumn();
        rowEditTable.setVisibleColumns(new String[]{name, attribute, originalMarketSegment, TetrisRowEditTable.ROW_EDIT_ACTION_COLUMN_ID});
        rowEditTable.setRowDeletable(false);
        rowEditTable.setColumnHeaders(UiUtils.getText("msName"), UiUtils.getText("attributes.header"), UiUtils.getText("msCode"), "");
        rowEditTable.setSizeFull();
        return rowEditTable;
    }

    public class MktSegDetailsDtoWrapper extends TetrisRowEditColumnComponentProvider {
        private final MktSegDetailsDto mktSegDetailsDto;

        public MktSegDetailsDtoWrapper(MktSegDetailsDto mktSegDetailsDto) {
            this.mktSegDetailsDto = mktSegDetailsDto;
        }

        public MktSegDetailsDto getMktSegDetailsDto() {
            return mktSegDetailsDto;
        }

        @Override
        public boolean isPersisted() {
            return true;
        }
    }

}
