package com.ideas.tetris.ui.modules.marketsegment.common;

import com.ideas.tetris.pacman.services.marketsegment.service.AnalyticalMarketSegmentAttribute.MarketSegmentLinkType;
import com.ideas.tetris.pacman.services.product.Product;
import com.ideas.tetris.ui.common.TetrisComponentFactory;
import com.ideas.tetris.ui.common.component.TetrisCaption;
import com.ideas.tetris.ui.common.component.TetrisLabel;
import com.ideas.tetris.ui.common.component.label.TetrisSpacer;
import com.ideas.tetris.ui.common.component.select.TetrisComboBoxV8;
import com.ideas.tetris.ui.common.util.UiUtils;
import com.vaadin.shared.ui.ContentMode;
import com.vaadin.ui.Alignment;
import com.vaadin.ui.Component;
import com.vaadin.ui.GridLayout;
import com.vaadin.ui.VerticalLayout;
import org.vaadin.hene.flexibleoptiongroup.v7.FlexibleOptionGroup;
import org.vaadin.hene.flexibleoptiongroup.v7.FlexibleOptionGroupItemComponent;

import java.util.LinkedList;
import java.util.List;
import java.util.function.Consumer;

import static com.ideas.tetris.pacman.util.Executor.executeIfFalse;
import static java.util.Objects.nonNull;

public class LinkedOptionGroup extends VerticalLayout {

    private static final String TOOLTIP_CLASS_WITH_BOLD_TEXT = "<p class='attributeTooltip'><b>";
    private static final String CLOSING_BOLD_TAG = ":</b> ";


    private List<Product> existingProducts;

    private FlexibleOptionGroup group;
    private GridLayout groupLayout;

    private TetrisComboBoxV8<Product> products;
    private TetrisCaption caption;

    private boolean required;
    private boolean enabled;
    private Product selectedProduct;
    private List<Consumer<MarketSegmentLinkType>> valueChangeHandlers = new LinkedList<>();

    public LinkedOptionGroup(List<Product> existingProducts) {
        init().addCaption().addProducts(existingProducts).addGroup();
        this.existingProducts = existingProducts;
    }

    private LinkedOptionGroup addGroup() {
        this
                .addOptionGroupItem(MarketSegmentLinkType.LINKED, UiUtils.getText("linked.to.BAR.caption"), products)
                .addOptionGroupItem(MarketSegmentLinkType.NONLINKED, UiUtils.getText("nonlinked.caption"))
                .handleValueChanged()
                .addComponent(groupLayout);
        return this;
    }

    private LinkedOptionGroup handleValueChanged() {
        group.addValueChangeListener(event -> {
            products.setEnabled(isLinked());
            executeIfFalse(isLinked(), products::setValue, null);
            valueChangeHandlers.forEach(handler -> handler.accept((MarketSegmentLinkType) group.getValue()));
        });
        return this;
    }

    private LinkedOptionGroup addOptionGroupItem(MarketSegmentLinkType item, String caption, Component... components) {
        group.addItem(item);
        group.setItemCaption(item, caption);
        FlexibleOptionGroupItemComponent itemComponent = group.getItemComponent(item);
        itemComponent.addStyleName("condition-option");
        itemComponent.setHeight(26, Unit.PIXELS);
        int row = getRowNumber(item);
        groupLayout.addComponent(itemComponent, 0, row);
        TetrisLabel label = new TetrisLabel(caption);
        label.setWidth(null);
        groupLayout.addComponent(label, 1, row);
        groupLayout.setComponentAlignment(itemComponent, Alignment.MIDDLE_LEFT);
        groupLayout.setComponentAlignment(label, Alignment.MIDDLE_LEFT);
        if (nonNull(components)) {
            groupLayout.addComponent(new TetrisSpacer(), 2, row);
            int nextColumn = 3;
            for (Component component : components) {
                groupLayout.addComponent(component, nextColumn++, row);
                groupLayout.setComponentAlignment(label, Alignment.MIDDLE_LEFT);
            }
        }
        return this;
    }

    private int getRowNumber(MarketSegmentLinkType item) {
        return MarketSegmentLinkType.LINKED.equals(item) ? 0 : 1;
    }

    private LinkedOptionGroup addProducts(List<Product> existingProducts) {
        setProducts(existingProducts);
        products.setItemCaptionGenerator(Product::getName);
        products.setEnabled(false);
        return this;
    }

    public void setProducts(List<Product> existingProducts) {
        this.existingProducts = existingProducts == null ? new LinkedList<>() : new LinkedList<>(existingProducts);
        products.setItems(this.existingProducts);
    }

    private LinkedOptionGroup addCaption() {
        this.caption = new TetrisCaption(UiUtils.getText("linked"));
        addComponent(caption);
        return this;
    }

    private LinkedOptionGroup init() {
        setDescription(
                TOOLTIP_CLASS_WITH_BOLD_TEXT + UiUtils.getText("linked.to.BAR.caption") + CLOSING_BOLD_TAG + UiUtils.getText("attribute.linked.to.bar.description") + "</p>" +
                        TOOLTIP_CLASS_WITH_BOLD_TEXT + UiUtils.getText("nonlinked.caption") + CLOSING_BOLD_TAG + UiUtils.getText("attribute.non.linked.description")
                , ContentMode.HTML
        );
        groupLayout = new GridLayout(4, 5);
        group = new FlexibleOptionGroup();
        products = TetrisComponentFactory.createComboBoxV8("products");
        products.setWidth("150px");
        products.setEmptySelectionAllowed(true);
        products.addValueChangeListener(event -> selectedProduct = products.getValue());
        setSpacing(false);
        setMargin(false);
        return this;
    }

    public void setRequired(boolean required) {
        this.required = required;
        caption.setRequired(required);
    }

    public boolean isLinked() {
        return MarketSegmentLinkType.LINKED.equals(group.getValue());
    }

    public Product linkedTo() {
        return selectedProduct;
    }

    @Override
    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
        super.setEnabled(enabled);
        group.setEnabled(enabled);
        groupLayout.setEnabled(enabled);
        executeIfFalse(enabled, group::unselect, group.getValue());
    }

    @Override
    public boolean isEnabled() {
        return enabled;
    }

    public boolean isValid() {
        if (!required) {
            return true;
        }
        return group.getValue() != null && (!isLinked() || selectedProduct != null);
    }

    public void onValueChange(Consumer<MarketSegmentLinkType> valueChangeHandler) {
        valueChangeHandlers.add(valueChangeHandler);
    }

    public void reset() {
        group.setValue(null);
        products.setValue(null);
    }

    public MarketSegmentLinkType getValue() {
        Object value = group.getValue();
        return null == value ? null : (MarketSegmentLinkType) value;
    }

    public void setValue(MarketSegmentLinkType linkType) {
        group.select(linkType);
    }
}
