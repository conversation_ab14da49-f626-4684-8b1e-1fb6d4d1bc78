package com.ideas.tetris.ui.modules.marketsegment.common.view;

import com.ideas.tetris.pacman.services.marketsegment.entity.ForecastActivityType;
import com.ideas.tetris.ui.common.util.UiUtils;

import java.util.Collection;

public class ForecastActivityTypeRadioButtonGroup extends MarketSegmentAttributionRadioButtonGroup<ForecastActivityType> {

    public ForecastActivityTypeRadioButtonGroup(AttributeAssignmentConfig config) {
        super("forecastActivityTypeOptionGroup", UiUtils.getText("forecastType"), config);
        setRequired(true);
        setEnabled(true);
    }

    public void setRequired(boolean required) {
        setRequiredIndicatorVisible(required);
    }

    @Override
    protected Collection<ForecastActivityType> getItems() {
        return getConfig().getForecastActivityTypes();
    }

    @Override
    protected String getCaption(ForecastActivityType item) {
        return item.getName();
    }
}
