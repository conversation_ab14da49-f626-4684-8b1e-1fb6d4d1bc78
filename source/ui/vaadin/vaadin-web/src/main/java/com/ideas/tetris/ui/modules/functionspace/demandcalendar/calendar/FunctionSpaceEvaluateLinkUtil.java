package com.ideas.tetris.ui.modules.functionspace.demandcalendar.calendar;

import com.ideas.tetris.ui.common.util.DateFormatUtil;
import com.ideas.tetris.ui.common.util.PageUtil;

import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class FunctionSpaceEvaluateLinkUtil {

    public static void goToEvaluation(Date date) {
        Map<String, List<String>> urlParams = new HashMap<String, List<String>>();
        urlParams.put("date", Arrays.asList(DateFormatUtil.formatServerDate(date)));
        PageUtil.goTo("function-space-evaluation", null, urlParams);
    }
}
