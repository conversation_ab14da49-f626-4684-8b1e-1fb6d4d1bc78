package com.ideas.tetris.ui.modules.internalalert;

import com.ideas.tetris.pacman.services.internalalert.entity.InternalAlert;
import org.joda.time.LocalDateTime;

public class InternalAlertDisplayDto {
    private Integer internalAlertId;
    private String details;
    private LocalDateTime createdDate;
    private LocalDateTime notifiedDate;
    private LocalDateTime resolvedDate;
    private String alertStatus;
    private String alertType;
    private String propertyCode;
    private String propertyName;
    private Integer propertyId;
    private String clientCode;
    private String clientName;
    private Integer clientId;
    private String stage;

    public InternalAlertDisplayDto() {

    }

    public InternalAlertDisplayDto(InternalAlert entity) {
        internalAlertId = entity.getId();
        details = entity.getDetails();
        createdDate = entity.getCreatedDate();
        notifiedDate = entity.getNotifiedDate();
        resolvedDate = entity.getResolvedDate();
        alertStatus = entity.getAlertStatus().toString();
        alertType = entity.getAlertType().toString();
        if (entity.getProperty() != null) {
            propertyCode = entity.getProperty().getCode();
            propertyName = entity.getProperty().getName();
            propertyId = entity.getProperty().getId();
        }
        if (entity.getClient() != null) {
            clientCode = entity.getClient().getCode();
            clientName = entity.getClient().getName();
            clientId = entity.getClient().getId();
        }
        if (entity.getStage() != null) {
            stage = entity.getStage().getCode();
        }
    }


    public String getAlertType() {
        return alertType;
    }

    public void setAlertType(String alertType) {
        this.alertType = alertType;
    }

    public String getPropertyCode() {
        return propertyCode;
    }

    public void setPropertyCode(String propertyCode) {
        this.propertyCode = propertyCode;
    }

    public String getPropertyName() {
        return propertyName;
    }

    public void setPropertyName(String propertyName) {
        this.propertyName = propertyName;
    }

    public Integer getPropertyId() {
        return propertyId;
    }

    public void setPropertyId(Integer propertyId) {
        this.propertyId = propertyId;
    }

    public String getClientCode() {
        return clientCode;
    }

    public void setClientCode(String clientCode) {
        this.clientCode = clientCode;
    }

    public String getClientName() {
        return clientName;
    }

    public void setClientName(String clientName) {
        this.clientName = clientName;
    }

    public Integer getClientId() {
        return clientId;
    }

    public void setClientId(Integer clientId) {
        this.clientId = clientId;
    }

    public String getAlertStatus() {
        return alertStatus;
    }

    public void setAlertStatus(String alertStatus) {
        this.alertStatus = alertStatus;
    }

    public String getDetails() {
        return details;
    }

    public void setDetails(String details) {
        this.details = details;
    }


    public LocalDateTime getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(LocalDateTime createdDate) {
        this.createdDate = createdDate;
    }

    public LocalDateTime getNotifiedDate() {
        return notifiedDate;
    }

    public void setNotifiedDate(LocalDateTime notifiedDate) {
        this.notifiedDate = notifiedDate;
    }

    public LocalDateTime getResolvedDate() {
        return resolvedDate;
    }

    public void setResolvedDate(LocalDateTime resolvedDate) {
        this.resolvedDate = resolvedDate;
    }

    public Integer getInternalAlertId() {
        return internalAlertId;
    }

    public void setInternalAlertId(Integer internalAlertId) {
        this.internalAlertId = internalAlertId;
    }

    public String getStage() {
        return stage;
    }

    public void setStage(String stage) {
        this.stage = stage;
    }
}
