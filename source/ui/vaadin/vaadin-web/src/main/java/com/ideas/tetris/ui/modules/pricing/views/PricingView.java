package com.ideas.tetris.ui.modules.pricing.views;

import com.ideas.infra.tetris.security.domain.TetrisPermissionKey;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomClass;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomType;
import com.ideas.tetris.pacman.services.eventaggregator.SyncEvent;
import com.ideas.tetris.pacman.services.informationmanager.alert.AlertStepType;
import com.ideas.tetris.pacman.services.product.Product;
import com.ideas.tetris.pacman.services.sasoptimization.dto.SimplifiedWhatIfResult;
import com.ideas.tetris.pacman.services.userfilteroptions.dto.PricingCalendarFilter;
import com.ideas.tetris.pacman.services.userfilteroptions.dto.PricingTabularFilter;
import com.ideas.tetris.platform.common.entity.FilterView;
import com.ideas.tetris.platform.common.utils.systemconfig.SystemConfig;
import com.ideas.tetris.platform.services.daoandentities.entity.LockingEnabledScreensAndButtons;
import com.ideas.tetris.ui.common.TetrisUi;
import com.ideas.tetris.ui.common.cdi.TetrisNavigatorView;
import com.ideas.tetris.ui.common.component.TetrisLabel;
import com.ideas.tetris.ui.common.component.TetrisNotification;
import com.ideas.tetris.ui.common.component.button.TetrisButton;
import com.ideas.tetris.ui.common.component.button.TetrisImageButton;
import com.ideas.tetris.ui.common.component.button.TetrisLinkButton;
import com.ideas.tetris.ui.common.component.button.TetrisLinkPlainButton;
import com.ideas.tetris.ui.common.component.checkbox.TetrisCheckBoxGroupV8;
import com.ideas.tetris.ui.common.component.checkbox.TetrisCheckBoxV8;
import com.ideas.tetris.ui.common.component.errorbox.TetrisErrorBox;
import com.ideas.tetris.ui.common.component.filter.TetrisFilterWindow;
import com.ideas.tetris.ui.common.component.fontawesome.TetrisFontAwesome;
import com.ideas.tetris.ui.common.component.label.TetrisSpacer;
import com.ideas.tetris.ui.common.component.messagebox.TetrisMessageBox;
import com.ideas.tetris.ui.common.component.panel.TetrisScrollablePanel;
import com.ideas.tetris.ui.common.component.select.TetrisComboBoxV8;
import com.ideas.tetris.ui.common.component.textfield.TetrisDateFieldV8;
import com.ideas.tetris.ui.common.component.window.TetrisUnsavedChangesWindow;
import com.ideas.tetris.ui.common.component.window.TetrisWindow;
import com.ideas.tetris.ui.common.googleanalytics.GoogleTrackerEnum;
import com.ideas.tetris.ui.common.googleanalytics.TetrisGoogleAnalyticsTracker;
import com.ideas.tetris.ui.common.util.LocalizationDimensionSpecifier;
import com.ideas.tetris.ui.common.util.TetrisTheme;
import com.ideas.tetris.ui.common.util.UiUtils;
import com.ideas.tetris.ui.modules.agilerates.packages.AgileRatesUiUtils;
import com.ideas.tetris.ui.modules.ataglance.InventoryGroupDto;
import com.ideas.tetris.ui.modules.commons.continuouspricing.CPBARDecisionUIWrapper;
import com.ideas.tetris.ui.modules.commons.continuouspricing.CPOverrideWrapper;
import com.ideas.tetris.ui.modules.commons.continuouspricing.CPPricingFilter;
import com.ideas.tetris.ui.modules.investigator.events.ApplyCPOverrideChangesEvent;
import com.ideas.tetris.ui.modules.investigator.events.ApplyCPOverrideEvent;
import com.ideas.tetris.ui.modules.investigator.events.RemoveCPOverrideEvent;
import com.ideas.tetris.ui.modules.pricing.PricingAgileRatesOverrideDTO;
import com.ideas.tetris.ui.modules.pricing.PricingPresenter;
import com.ideas.tetris.ui.modules.pricing.PricingToggleView;
import com.ideas.tetris.ui.modules.pricing.multiproductmultiday.PricingMultiProductMultidayFilterDTO;
import com.ideas.tetris.ui.modules.pricing.multiproductmultiday.PricingMultiProductMultidayOverrideLayout;
import com.ideas.tetris.ui.modules.pricing.multiproductmultiday.PricingMultidayGridDTO;
import com.ideas.tetris.ui.modules.pricing.multiproductmultiday.PricingMultiDayInventoryLimitOverrideLayout;
import com.ideas.tetris.ui.modules.pricing.views.inlineedit.PricingInlineEditGrid;
import com.ideas.tetris.ui.modules.reports.util.URLParamUtil;
import com.ideas.tetris.ui.modules.whatif.SimplifiedWhatIfLayout;
import com.ideas.tetris.ui.modules.whatif.WhatIfWindow;
import com.ideas.tetris.ui.shell.navigation.PropertyStateChangedEvent;
import com.ideas.tetris.ui.widget.AdvancedPopupButton;
import com.vaadin.cdi.CDIView;
import com.vaadin.data.Binder;
import com.vaadin.data.ValidationResult;
import com.vaadin.server.Responsive;
import com.vaadin.shared.ui.MarginInfo;
import com.vaadin.ui.Alignment;
import com.vaadin.ui.CssLayout;
import com.vaadin.ui.HorizontalLayout;
import com.vaadin.ui.Label;
import com.vaadin.ui.Notification;
import com.vaadin.ui.TabSheet;
import com.vaadin.ui.UI;
import com.vaadin.ui.VerticalLayout;
import com.vaadin.ui.Window;
import com.vaadin.ui.themes.ValoTheme;
import com.vaadin.v7.shared.ui.label.ContentMode;
import org.apache.commons.collections.CollectionUtils;

import javax.enterprise.event.Observes;
import javax.enterprise.event.Reception;
import javax.inject.Inject;
import java.time.LocalDate;
import java.util.Comparator;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import static com.ideas.tetris.ui.common.util.UiUtils.getTetrisUi;

@CDIView(uis = UI.class, value = TetrisPermissionKey.PRICING)
public class PricingView extends TetrisNavigatorView<PricingPresenter, Void> {
    public static final String PRICING_FILTER_BUTTON_ID = "pricingFilterButtonId";
    public static final String MULTIDAY_OVERRIDE_BUTTON_ID = "multidayOverrideButtonId";
    public static final String PRICING_WHAT_IF = "pricingWhatIf";
    static final String PRICING_PAGE_STYLE = "pricingPage";
    private static final String BACKGROUND_COLOR_GRAY = "background-color-gray";
    private static final String SAVE_BUTTON = "save-button";
    @Inject
    private TetrisGoogleAnalyticsTracker tracker;
    private VerticalLayout layout;
    private Binder<CPPricingFilter> filterBinder;
    private TetrisDateFieldV8 startDate;
    private TetrisDateFieldV8 endDate;
    private TetrisButton saveButton;
    private VerticalLayout productsLayout;
    private AdvancedPopupButton productsButton;
    private TetrisCheckBoxGroupV8<Product> productsCheckBoxes;
    private TetrisComboBoxV8<AccomClass> roomClass;
    private TetrisComboBoxV8<InventoryGroupDto> inventoryGroupSelection;
    private AdvancedPopupButton roomTypeButton;
    private TetrisCheckBoxGroupV8<AccomType> roomTypeCheckBoxes;
    private PricingDayDetailsLayout pricingDayDetailsLayout;
    private PricingInlineEditGrid pricingInlineEditGrid;
    private TetrisLinkButton legendLinkButton;
    private TetrisButton uploadButton;
    private TetrisButton whatIfButton;
    private TetrisButton cancelButton;
    private Label incorrectDateLabel;
    private TetrisSpacer incorrectDateLabelSpacer;
    private HorizontalLayout incorrectDateLayout;
    private PricingFilter pricingFilter;
    private TetrisFilterWindow filterWindow;
    private TetrisButton overrideButton;
    private boolean unsavedChangesWindowShowing = false;
    private VerticalLayout roomTypeLayout;
    private HorizontalLayout alertLayout;
    private HorizontalLayout buttonBarLayout;
    private TetrisCheckBoxV8 applyOverridesToAllRoomClassesCheckbox;
    private TetrisImageButton dayCardModeButton;
    private TetrisImageButton inlineModeButton;
    private HorizontalLayout buttonBarRight;
    private TetrisErrorBox errorBox;

    TabSheet multiDayOverrideTabSheet;
    @Override
    protected void initView() {
        addStyleName(PRICING_PAGE_STYLE);

        layout = new VerticalLayout();
        layout.setSizeFull();
        layout.setMargin(false);
        layout.setSpacing(true);

        layout.addComponent(createFilter());
        layout.addComponent(createButtonBar());
        setCompositionRoot(layout);

        filterBinder = new Binder<>();
        setUpBinder();
        createErrorBox();

        pricingDayDetailsLayout = new PricingDayDetailsLayout();
        Responsive.makeResponsive(pricingDayDetailsLayout);
        layout.addComponent(pricingDayDetailsLayout);
        layout.addComponent(errorBox);
        layout.setComponentAlignment(errorBox, Alignment.MIDDLE_CENTER);
    }

    @Override
    public void onPresenterInit() {
        initFilterComponents();
        setFilterButton(createFilterButton());

        addDateValueChangeListeners();
        addFilterBarChangeListeners();
        if (presenter.hideDatesWithDropdownDataAsPerExtendedWindow()) {
            pricingFilter.resetToDefault();
        }
        updateDecisions(true);
        displayReadOnlyNotification();
        openCompetitorPopUp();
    }

    void displayReadOnlyNotification() {
        TetrisUi tetrisUi = getTetrisUi();
        Optional<LockingEnabledScreensAndButtons> lockingEnabledScreensAndButtonsOptional = Optional.ofNullable(tetrisUi.ReadOnlyScreenUrlAndButtonCaptionCache().get(UiUtils.getCurrentPage().getCode()));
        if (!tetrisUi.hasReadOnlyWarningDisplayedFor(UiUtils.getCurrentPage().getCode()) && presenter.shouldDisplayReadOnlyNotification() && !presenter.getIsReadOnlyNotificationDisplayed()
                && lockingEnabledScreensAndButtonsOptional.isPresent()
                && !lockingEnabledScreensAndButtonsOptional.get().isAllTimeLocked()) {
            presenter.setIsReadOnlyNotificationDisplayed(true);
            showWarningPromptWithoutNoLabel(getText("read.only.notification.title"),
                    getText("read.only.notification.message"), "OK", null

            );
            tetrisUi.updateDisplayedReadOnlyWarnings(UiUtils.getCurrentPage().getCode());
        }
    }

    public void hideIncorrectDateLayout() {
        incorrectDateLayout.setVisible(false);
    }

    private void openCompetitorPopUp() {
        if (AlertStepType.INVESTIGATE.toString().equalsIgnoreCase(URLParamUtil.getStepTypeParamValue(presenter.getUrlParameters()))) {
            pricingDayDetailsLayout.openCompetitorDetailsWindow(presenter.getSelectedDateFromURL());
        }
    }

    public void createErrorBox() {
        Label errorLabel = new Label(UiUtils.getText("pricing.no.data.available"));
        TetrisLinkPlainButton refreshFilterButton = new TetrisLinkPlainButton(UiUtils.getText("common.resetFilter"));
        refreshFilterButton.addClickListener(clickEvent -> {
            resetFilter();
        });
        HorizontalLayout errorContent = new HorizontalLayout();
        errorContent.setSpacing(false);
        errorContent.addComponents(errorLabel, refreshFilterButton);
        errorContent.setComponentAlignment(errorLabel, Alignment.MIDDLE_LEFT);
        errorContent.setComponentAlignment(refreshFilterButton, Alignment.MIDDLE_LEFT);
        errorBox = new TetrisErrorBox(errorContent);
        errorBox.setVisible(false);
        errorBox.setId("errorBox");
    }

    String getProductNameWithIcon(Product product) {
        return product.isSystemDefault() ?
            presenter.truncateLabel(product.getName(), presenter.MAX_PRODUCT_CHECKBOX_LENGTH) :
            presenter.truncateLabel(product.getName(), presenter.MAX_PRODUCT_CHECKBOX_LENGTH - 2) + " " + AgileRatesUiUtils.getProductIcon(product).getHtml();
    }

    private HorizontalLayout createFilter() {
        VerticalLayout filter = new VerticalLayout();
        filter.setMargin(new MarginInfo(true, false));
        filter.setSpacing(false);
        filter.setWidthUndefined();

        //Put fields into filterConfiguration
        HorizontalLayout filterConfiguration = new HorizontalLayout();

        productsLayout = new VerticalLayout();
        productsLayout.setMargin(false);
        productsLayout.setSpacing(false);

        Label productsLabel = new Label(getText("common.product"));
        productsLabel.setStyleName("v-caption");

        productsButton = new AdvancedPopupButton();
        productsButton.setId("productsAdvanceButton");
        productsButton.addPopupVisibilityListener(event -> {
            if (!event.isPopupVisible() && !productsCheckBoxes.getValue().equals(filterBinder.getBean().getProducts())) {
                if (productsCheckBoxes.getValue().isEmpty()) {
                    productsCheckBoxes.setValue(filterBinder.getBean().getProducts());
                    TetrisNotification.showWarningMessage(getText("pricing.product.selector.invalid"));
                } else if (productsCheckBoxes.getValue().size() < 6) {
                    if (!presenter.isCardViewSelected() && pricingInlineEditGrid != null && pricingInlineEditGrid.gridHasChanges()) {
                        productsCheckBoxes.setValue(filterBinder.getBean().getProducts());
                        presenter.showWarning(getText("unsavedChangesCommonWarningMessage"));
                    } else if (filterBinder.isValid()) {
                        filterBinder.getBean().setProducts(presenter.sortProducts(productsCheckBoxes.getValue()));
                        if (presenter.isIndependentProductsEnabled() || presenter.isSmallGroupProductsEnabled()
                                || presenter.isRDLEnabled()) {
                            presenter.setAllCompetitorDetailsRows(pricingFilter.getSelectedCompetitors());
                        }
                        pricingFilter.updateFilterProducts(productsCheckBoxes.getValue());
                        updateAfterProductsChanged();
                    }
                } else {
                    //Do not allow more than 5 products to be selected at once
                    productsCheckBoxes.setValue(filterBinder.getBean().getProducts());
                    TetrisNotification.showWarningMessage(getText("pricing.product.selector.warning"));
                }
            }
        });
        productsCheckBoxes = new TetrisCheckBoxGroupV8<>();
        productsCheckBoxes.setId("productsCheckBoxes");
        productsCheckBoxes.setWidth(150, Unit.PIXELS);
        productsCheckBoxes.setStyleName("v-scrollable");
        productsCheckBoxes.setValueChangeEventDisabled(true);
        productsCheckBoxes.setHtmlContentAllowed(true);
        productsCheckBoxes.setItemCaptionGenerator(this::getProductNameWithIcon);
        productsCheckBoxes.setItemDescriptionGenerator(product -> presenter.getDescription(product.getName(), presenter.MAX_PRODUCT_CHECKBOX_LENGTH));
        productsButton.setContent(productsCheckBoxes);
        productsLayout.addComponents(productsLabel, productsButton);

        startDate = new TetrisDateFieldV8();
        startDate.setCaption(getText("from"));
        startDate.setWidth(130, Unit.PIXELS);
        endDate = new TetrisDateFieldV8();
        endDate.setCaption(getText("to"));
        endDate.setWidth(130, Unit.PIXELS);

        inventoryGroupSelection = new TetrisComboBoxV8<>();
        inventoryGroupSelection.setCaption(getText("inventory.group"));
        inventoryGroupSelection.setId("inventoryGroupComboBox");
        inventoryGroupSelection.setEmptySelectionAllowed(false);
        inventoryGroupSelection.setItemCaptionGenerator(InventoryGroupDto::getName);
        inventoryGroupSelection.addValueChangeListener(valueChangeEvent -> {
            if (valueChangeEvent.getValue() != null) {
                roomClass.setItems(presenter.getAccomClassByInventoryGroup(valueChangeEvent.getValue().getId()));
                roomClass.setValue(presenter.getAllRoomClasses());
            }
        });

        roomClass = new TetrisComboBoxV8<>();
        roomClass.setCaption(getText("room.class"));
        roomClass.setId("roomClassComboBox");
        roomClass.setEmptySelectionAllowed(false);
        roomClass.setItemCaptionGenerator(AccomClass::getName);
        roomClass.addValueChangeListener(valueChangeEvent -> {
            if (valueChangeEvent.getValue() != null && valueChangeEvent.getValue().getAccomTypes() != null) {
                roomTypeCheckBoxes.setItems(presenter.getSortedAccomTypes(valueChangeEvent.getValue(), valueChangeEvent.getValue().filterAndGetAccomTypes(presenter.accomTypesFiltrationCriteria())));
            }

            if (presenter.getAllRoomClasses().equals(roomClass.getValue())) {
                roomTypeButton.setEnabled(false);
                roomTypeButton.setCaption(getText("common.baseRoomType"));
            } else {
                roomTypeButton.setEnabled(true);
                roomTypeButton.setCaption(getText("all"));
            }
        });

        roomTypeLayout = new VerticalLayout();
        roomTypeLayout.setMargin(false);
        roomTypeLayout.setSpacing(false);

        Label roomTypeLabel = new Label(getText("roomType"));
        roomTypeLabel.setStyleName("v-caption");

        roomTypeButton = new AdvancedPopupButton();
        roomTypeButton.setId("roomTypeAdvanceButton");

        roomTypeCheckBoxes = new TetrisCheckBoxGroupV8<>();
        roomTypeCheckBoxes.setId("roomTypeCheckbox");
        roomTypeCheckBoxes.setWidth(150, Unit.PIXELS);
        roomTypeCheckBoxes.setItemCaptionGenerator(accomType -> {
            //need to adjust truncation for the size of the icon if base room type
            return presenter.truncateLabel(accomType.getName(), presenter.isBaseRoomType(accomType) ? (presenter.MAX_ROOM_CLASS_TYPE_CHECKBOX_LENGTH - 2) : presenter.MAX_ROOM_CLASS_TYPE_CHECKBOX_LENGTH);
        });
        roomTypeCheckBoxes.setItemDescriptionGenerator(accomType -> presenter.getDescription(accomType.getName(), presenter.MAX_ROOM_CLASS_TYPE_CHECKBOX_LENGTH));
        roomTypeCheckBoxes.setItemIconGenerator(accomType -> {
            if (presenter.isBaseRoomType(accomType)) {
                return TetrisFontAwesome.BASE_ROOM_TYPE;
            }
            return null;
        });
        roomTypeCheckBoxes.addValueChangeListener(valueChangeEvent -> {
            if (!presenter.getAllRoomClasses().equals(roomClass.getValue()) &&
                    (roomTypeCheckBoxes.getValue().isEmpty() || roomTypeCheckBoxes.getValue().size() == roomClass.getValue().getAccomTypes().size())) {
                roomTypeButton.setCaption(getText("all"));
            } else if (!presenter.getAllRoomClasses().equals(roomClass.getValue())) {
                roomTypeButton.setCaption(getText("Selected") + " " + roomTypeCheckBoxes.getValue().size());
            }
        });

        roomTypeButton.setContent(roomTypeCheckBoxes);
        roomTypeLayout.addComponents(roomTypeLabel, roomTypeButton);

        applyOverridesToAllRoomClassesCheckbox = new TetrisCheckBoxV8();
        applyOverridesToAllRoomClassesCheckbox.setCaptionAsHtml(true);
        applyOverridesToAllRoomClassesCheckbox.setCaption(getText("common.apply.to.all.room.classes"));
        applyOverridesToAllRoomClassesCheckbox.setId("applyOverridesToAllRoomClassesCheckbox");


        VerticalLayout toggleViewLayout = new VerticalLayout();
        toggleViewLayout.setHeight(100, Unit.PERCENTAGE);
        toggleViewLayout.setMargin(new MarginInfo(true, false, false, false));

        filterConfiguration.addComponents(productsLayout, startDate, endDate, inventoryGroupSelection, roomClass, roomTypeLayout, applyOverridesToAllRoomClassesCheckbox);
        filterConfiguration.setComponentAlignment(applyOverridesToAllRoomClassesCheckbox, Alignment.MIDDLE_LEFT);

        incorrectDateLayout = new HorizontalLayout();
        incorrectDateLayout.setMargin(false);
        incorrectDateLayout.setSpacing(false);
        incorrectDateLayout.setVisible(false);

        incorrectDateLabel = new Label();
        incorrectDateLabel.addStyleNames("error-label", ValoTheme.LABEL_SMALL);

        incorrectDateLabelSpacer = new TetrisSpacer(140, Unit.PIXELS);

        incorrectDateLayout.addComponents(incorrectDateLabelSpacer, incorrectDateLabel);

        filter.addComponents(filterConfiguration, incorrectDateLayout);
        filter.setExpandRatio(filterConfiguration, 1);

        HorizontalLayout filterWrapper = new HorizontalLayout();
        filterWrapper.addStyleNames("filter-header", BACKGROUND_COLOR_GRAY);
        filterWrapper.setMargin(false);
        filterWrapper.setSpacing(false);
        filterWrapper.setWidth(100, Unit.PERCENTAGE);
        filterWrapper.addComponent(filter);
        filterWrapper.setComponentAlignment(filter, Alignment.MIDDLE_CENTER);
        filterWrapper.setExpandRatio(filter, 1);

        return filterWrapper;
    }

    private void updateApplyOverridesToAllRoomClassesCheckbox() {
        if (!presenter.getCpPricingFilter().isAgileRateProductSelected()) {
            applyOverridesToAllRoomClassesCheckbox.setValue(false);
        }
        //disable applyOverridesToAllRoomClassesCheckbox if there are products selected
        applyOverridesToAllRoomClassesCheckbox.setEnabled(presenter.getCpPricingFilter().isAgileRateProductSelected());
    }

    public boolean isApplyOverridesToAllRoomClassesChecked() {
        return applyOverridesToAllRoomClassesCheckbox.getValue();
    }

    private TetrisImageButton createFilterButton() {
        final TetrisImageButton button =
                new TetrisImageButton(TetrisFontAwesome.FILTER, TetrisFontAwesome.FontSize.MEDIUM, listener -> {
                    filterWindow.show();
                });
        button.setId(PRICING_FILTER_BUTTON_ID);
        return button;
    }

    private boolean isProductFilterChanged(Set<Product> productsInAdvanceFilter) {
        Set<Product> productsInFilter = productsCheckBoxes.getValue();
        return !Objects.equals(productsInFilter, productsInAdvanceFilter);
    }

    public void initFilterComponents() {
        pricingFilter = new PricingFilter(this, presenter, filterBinder.getBean());
        if (!presenter.isCardViewSelected()) {
            pricingFilter.setInlineEditFilterItems(presenter.getWebrateCompetitors());
        }
        filterWindow = new TetrisFilterWindow(pricingFilter);
        filterWindow.getFilterButtonBar().addPreValidationListener(listener -> {
            pricingFilter.setApplyClicked(true);
            if (pricingFilter.isDatesWithComboBoxValid()) {
                boolean isProductFilterChanged = isProductFilterChanged(pricingFilter.getSelectedProducts());
                if (!presenter.isCardViewSelected()) {
                    if (presenter.isIndependentProductsEnabled() || presenter.isRDLEnabled()) {
                        presenter.setAllCompetitorDetailsRows(pricingFilter.getSelectedCompetitors());
                    } else {
                        presenter.setCompetitorDetailsRows(pricingFilter.getSelectedCompetitors());
                    }
                    if (!pricingInlineEditGridHasChanges()) {
                        pricingFilter.updateCopyFilter();
                        pricingFilter.saveFilterSettings();
                        if (isProductFilterChanged) {
                            productsCheckBoxes.setValue(pricingFilter.getSelectedProducts());
                            filterBinder.getBean().setProducts(pricingFilter.getSelectedProducts());
                            updateAfterProductsChanged();
                        } else {
                            PricingView.this.updateDecisions(false);
                        }
                    } else {
                        pricingFilter.revertValuesOnCancel();
                        presenter.showWarning(getText("unsavedChangesCommonWarningMessage"));
                    }
                } else {
                    pricingFilter.updateCopyFilter();
                    pricingFilter.saveFilterSettings();
                    if (isProductFilterChanged) {
                        productsCheckBoxes.setValue(pricingFilter.getSelectedProducts());
                        filterBinder.getBean().setProducts(pricingFilter.getSelectedProducts());
                        updateAfterProductsChanged();
                    } else {
                        PricingView.this.updateDecisions(false);
                    }
                }
            } else {
                Notification.show(getText("common.reviewselection"));
                listener.cancel();
            }
        });

        filterWindow.addCloseListener(listener -> {
            if (!pricingFilter.isApplyClicked()) {
                pricingFilter.revertValuesOnCancel();
            } else {
                pricingFilter.setApplyClicked(false);
            }
        });
        pricingFilter.setFilterAsPerExtendedWindow();
    }

    private void updateAfterProductsChanged() {
        updateDecisions(true);
        updateWhatIfButtonVisibility();
        updateProductsButtonCaption(productsCheckBoxes.getValue());
        updateApplyOverridesToAllRoomClassesCheckbox();
    }

    private void updateWhatIfButtonVisibility() {
        Set<Product> selectedProducts = productsCheckBoxes.getValue();
        whatIfButton.setVisible(
                selectedProducts.size() == 1 && selectedProducts.iterator().next().isSystemDefault()
                        && presenter.isWhatIfButtonVisible()
        );
    }


    public void enableApplyOverrideToAllOnContextChange() {
        applyOverridesToAllRoomClassesCheckbox.setEnabled(true);
    }

    public void setApplyOverrideToAllOnContextChange() {
        applyOverridesToAllRoomClassesCheckbox.setValue(true);
    }

    public void setProductOnContextChange(List<Product> products) {
        productsCheckBoxes.setValue(new HashSet<>(products));
        updateProductsButtonCaption(productsCheckBoxes.getValue());
    }

    public void setAccomTypesOnContextChange(List<AccomType> accomTypes) {
        roomTypeCheckBoxes.setValue(new HashSet<>(accomTypes));
    }

    private HorizontalLayout createAlertLayout() {
        TetrisImageButton alertImage = new TetrisImageButton(TetrisFontAwesome.ALERT_TRIANGLE, TetrisFontAwesome.FontSize.SMALL);
        TetrisLabel warningLabel = new TetrisLabel(UiUtils.getText("pricing.sync.warning", TetrisFontAwesome.SYNC_FLAG.getHtml()), ContentMode.HTML);
        warningLabel.addStyleName("warning-label");
        alertLayout = new HorizontalLayout(alertImage, warningLabel, new TetrisSpacer(5, Unit.PIXELS));
        alertLayout.addStyleName("alert-layout");
        alertLayout.setVisible(false);
        return alertLayout;
    }

    private HorizontalLayout createButtonBar() {
        HorizontalLayout buttonBar = new HorizontalLayout();
        buttonBar.setMargin(new MarginInfo(false, true));

        saveButton = new TetrisButton(UiUtils.getText("common.save"));
        saveButton.setId("pricingSaveButton");
        saveButton.addStyleName(SAVE_BUTTON);
        saveButton.setWidth(LocalizationDimensionSpecifier.forCurrentLocale().getPricingSaveButtonWidth(), Unit.PIXELS);
        saveButton.setEnabled(false);
        saveButton.addClickListener(clickEvent -> {
            if (PricingToggleView.GRID.equals(presenter.getSelectedView())) {
                presenter.saveInlineEditGrid();
            } else {
                if (presenter.hasChanges()) {
                    presenter.save();
                    enableButtons(false, false);
                    updateUploadEnabled(presenter.isManualUploadEnabled(), presenter.isPropertyStageTwoWay(), presenter.isBarOverridePendingForUpload(), presenter.isAgileRatesPendingForUpload(), presenter.isInventoryLimitPendingForUpload());
                    showSaveSuccessMessage();
                } else {
                    showWarning(getText("no.pending.override"));
                }
            }
        });

        uploadButton = new TetrisButton(UiUtils.getText("upload"));
        uploadButton.addStyleName("button-bar");
        uploadButton.setWidth(LocalizationDimensionSpecifier.forCurrentLocale().
                getPricingViewUploadButtonWidth(), Unit.PIXELS);
        uploadButton.addClickListener(clickEvent -> {
            presenter.uploadOverrides();
            updateUploadEnabled(presenter.isManualUploadEnabled(), presenter.isPropertyStageTwoWay(), presenter.isBarOverridePendingForUpload(), presenter.isAgileRatesPendingForUpload(), presenter.isInventoryLimitPendingForUpload());
        });

        overrideButton = new TetrisButton(UiUtils.getText("multiDayOverride"));
        overrideButton.setId(MULTIDAY_OVERRIDE_BUTTON_ID);
        overrideButton.addStyleName("button-bar");
        overrideButton.addClickListener(clickEvent -> {
            TetrisWindow window = new TetrisWindow();
            window.setId("multiDayOverrideWindow");
            window.setCaption(presenter.getText("multiDayOverride"));
            PricingMultiProductMultidayOverrideLayout pricing = new PricingMultiProductMultidayOverrideLayout(presenter, presenter.createMultiProductMultidayFilterDTO());
            if (presenter.isCardViewSelected() && presenter.isGroupProductInventoryLimitEnabled() && presenter.atLeastOneGroupProductSelected()) {
                CssLayout tabSheetLayout = getMultiDayOverrideTabSheet(pricing);
                window.setContent(tabSheetLayout);
            }
            else {
                window.setContent(pricing);
            }
            window.setId("multiDayMultiProductOverrideWindow");
            window.removeAllCloseShortcuts();
            window.setHelpId("1080");
            window.show();
        });

        whatIfButton = new TetrisButton(UiUtils.getText("continuousPricingManagementWhatIf"));
        whatIfButton.addStyleName("button-bar");
        whatIfButton.setEnabled(false);
        whatIfButton.addClickListener(clickEvent -> {
            if (!presenter.isCardViewSelected()) {
                //Inline Edit Flow
                List<CPOverrideWrapper> changedWrappersFromGrid = getCPOverrideMapsFromGrid()
                        .values()
                        .stream()
                        .filter(item -> !item.isMatchingExistingOverride(presenter.getHighestBarRestrictedEnabled()))
                        .collect(Collectors.toList());
                if (CollectionUtils.isEmpty(changedWrappersFromGrid)) {
                    presenter.showWarning(getText("no.override.what.if"));
                } else {
                    tracker.trackEvent(GoogleTrackerEnum.WHAT_IF_PRICING);

                    handleSimplifiedWhatIfResponse();
                }
            } else {
                //Non-inline edit flow
                if (!presenter.hasChanges()) {
                    presenter.showWarning(getText("no.override.what.if"));
                } else if (presenter.hasCPOverrideChanges()) {
                    tracker.trackEvent(GoogleTrackerEnum.WHAT_IF_PRICING);
                    handleSimplifiedWhatIfResponse();
                } else if (presenter.hasLV0Changes()) {
                    presenter.showWarning(getText("no.override.what.if.closeLvo"));
                }
            }
        });

        cancelButton = new TetrisButton(UiUtils.getText("cancel"));
        cancelButton.setId("pricingCancelButton");
        cancelButton.addStyleName("button-bar");
        cancelButton.setEnabled(false);
        cancelButton.addClickListener(clickEvent -> {
            enableButtons(false, false);
            presenter.cancel();
        });

        alertLayout = createAlertLayout();

        buttonBar.addComponents(saveButton, uploadButton, overrideButton, whatIfButton, cancelButton, new TetrisSpacer(5, Unit.PIXELS), alertLayout);

        buttonBarRight = new HorizontalLayout();

        dayCardModeButton = new TetrisImageButton(TetrisFontAwesome.CALENDAR);
        dayCardModeButton.addStyleName(TetrisFontAwesome.FontSize.SMALL.getStyle());
        dayCardModeButton.setDescription(UiUtils.getText("calender.view"));
        dayCardModeButton.setId("dayCardModeButton");
        dayCardModeButton.addStyleName("view-button-daycard");
        dayCardModeButton.setVisible(false);
        dayCardModeButton.setEnabled(false);
        dayCardModeButton.addClickListener(event -> {
            if (presenter.hasUnsavedChanges()) {
                presenter.showWarning(getText("unsavedChangesCommonWarningMessage"));
            } else if (filterBinder.isValid()) {
                presenter.toggleView();
                updateProductsAfterViewSwitch();
                pricingFilter.updateOverrideCheckBoxGroup(false);
            }
        });

        inlineModeButton = new TetrisImageButton(TetrisFontAwesome.TABLE);
        inlineModeButton.addStyleName(TetrisFontAwesome.FontSize.SMALL.getStyle());
        inlineModeButton.setDescription(UiUtils.getText("inline.edit"));
        inlineModeButton.setId("inlineModeButton");
        inlineModeButton.addStyleName("view-button-inline");
        inlineModeButton.setVisible(false);
        inlineModeButton.setEnabled(false);
        inlineModeButton.addClickListener(event -> {
            if (presenter.hasUnsavedChanges()) {
                presenter.showWarning(getText("unsavedChangesCommonWarningMessage"));
            } else if (filterBinder.isValid()) {
                presenter.toggleView();
                updateProductsAfterViewSwitch();
                pricingFilter.updateOverrideCheckBoxGroup(true);
            }
        });

        legendLinkButton = addLegendButton(false);

        buttonBarRight.addComponents(dayCardModeButton, inlineModeButton, legendLinkButton);
        buttonBarRight.setComponentAlignment(dayCardModeButton, Alignment.MIDDLE_RIGHT);
        buttonBarRight.setComponentAlignment(inlineModeButton, Alignment.MIDDLE_RIGHT);
        buttonBarRight.setComponentAlignment(legendLinkButton, Alignment.MIDDLE_RIGHT);
        buttonBarRight.setSpacing(false);

        buttonBarLayout = new HorizontalLayout();
        buttonBarLayout.setWidth(100, Unit.PERCENTAGE);
        buttonBarLayout.setMargin(new MarginInfo(false, true, false, false));
        buttonBarLayout.setSpacing(true);
        buttonBarLayout.setHeight(30, Unit.PIXELS);

        buttonBarLayout.addComponent(buttonBar);
        buttonBarLayout.addComponent(buttonBarRight);
        buttonBarLayout.setComponentAlignment(buttonBarRight, Alignment.TOP_RIGHT);
        buttonBarLayout.setComponentAlignment(buttonBar, Alignment.TOP_LEFT);
        return buttonBarLayout;
    }
    private CssLayout getMultiDayOverrideTabSheet(PricingMultiProductMultidayOverrideLayout pricing) {
        CssLayout tabSheetLayout = new CssLayout();
        tabSheetLayout.setId("agileRatesOverridePopupTabSheet");
        tabSheetLayout.setWidth(pricing.getWidth(), Unit.PIXELS);
        multiDayOverrideTabSheet = new TabSheet();
        multiDayOverrideTabSheet.addTab(pricing, getText("pricing"));
        PricingMultiDayInventoryLimitOverrideLayout inventory = new PricingMultiDayInventoryLimitOverrideLayout(presenter, presenter.createMultiProductMultidayFilterDTO());
        multiDayOverrideTabSheet.addTab(inventory, getText("inventory"));
        tabSheetLayout.addComponent(multiDayOverrideTabSheet);
        return tabSheetLayout;
    }

    public void updateCompetitorsAfterViewSwitch() {
        if (PricingToggleView.GRID == presenter.getSelectedView()) {
            if (presenter.isIndependentProductsEnabled() || presenter.isRDLEnabled()) {
                presenter.setAllCompetitorDetailsRows(pricingFilter.getSelectedCompetitors());
            } else {
                presenter.setCompetitorDetailsRows(pricingFilter.getSelectedCompetitors());
            }
        }
    }

    private void updateProductsAfterViewSwitch() {
        PricingCalendarFilter savedCalendarFilter =
                (PricingCalendarFilter) presenter.getSavedFilterSettings(FilterView.PRICINGCALENDAR.getViewString());
        PricingTabularFilter savedTabularFilter =
                (PricingTabularFilter) presenter.getSavedFilterSettings(FilterView.PRICINGTABULAR.getViewString());
        Set<Integer> productIds = new HashSet<>();
        if (PricingToggleView.CARD == presenter.getSelectedView()) {
            if (savedCalendarFilter == null || savedCalendarFilter.getProductIds() == null || savedCalendarFilter.getProductIds().isEmpty()) {
                productIds.addAll(productsCheckBoxes.getValue().stream().map(Product::getId).collect(Collectors.toSet()));
            } else {
                productIds.addAll(savedCalendarFilter.getProductIds());
            }
        } else {
            if (savedTabularFilter == null || savedTabularFilter.getProductIds() == null || savedTabularFilter.getProductIds().isEmpty()) {
                productIds.addAll(productsCheckBoxes.getValue().stream().map(Product::getId).collect(Collectors.toSet()));
            } else {
                productIds.addAll(savedTabularFilter.getProductIds());
            }
        }
        Set<Product> selectedProducts = presenter.getAllProducts().stream()
                .filter(product -> productIds.contains(product.getId())).collect(Collectors.toSet());
        productsCheckBoxes.setValue(selectedProducts);
        updateProductsButtonCaption(selectedProducts);
        filterBinder.getBean().setProducts(selectedProducts);
        pricingFilter.updateOverrideCheckBoxGroup(true);

        if (PricingToggleView.CARD == presenter.getSelectedView() && savedCalendarFilter == null) {
            pricingFilter.updateFilterProducts(selectedProducts);
        } else if (PricingToggleView.GRID == presenter.getSelectedView() && savedTabularFilter == null) {
            pricingFilter.updateFilterProducts(selectedProducts);
        }
        updateCompetitorsAfterViewSwitch();
        updateDecisions(true);
    }

    public TetrisLinkButton addLegendButton(boolean isInlineEditingMode) {
        legendLinkButton = new TetrisLinkButton(UiUtils.getText("pricing.legend"));
        legendLinkButton.setPopupContentProvider(ignored -> {
            VerticalLayout legendLayout = createLegendLayout(isInlineEditingMode);
            legendLayout.setStyleName(PRICING_PAGE_STYLE);
            return legendLayout;
        });
        return legendLinkButton;
    }

    public void updateUploadEnabled(boolean isManualUploadEnabled, boolean isPropertyStageTwoWay, boolean isBarOverridePendingForUpload, boolean isAgileRatesOverridePendingForUpload, boolean isInventoryLimitPendingForUpload) {
        boolean isEnabled = isManualUploadEnabled && isPropertyStageTwoWay && (isBarOverridePendingForUpload || isAgileRatesOverridePendingForUpload || isInventoryLimitPendingForUpload);
        uploadButton.setEnabled(isEnabled);

        if (isEnabled) {
            uploadButton.setDescription(getText("pricingManagementManualUpload"));
        } else if (!isManualUploadEnabled && isPropertyStageTwoWay && (isBarOverridePendingForUpload || isAgileRatesOverridePendingForUpload || isInventoryLimitPendingForUpload)) {
            uploadButton.setDescription(getText("pricingManagementManualUploadDisabled"));
        } else {
            uploadButton.setDescription("");
        }

        if (isManualUploadEnabled) {
            uploadButton.setEnabledDuringIDP(Boolean.TRUE, TetrisPermissionKey.PRICING_MANUAL_UPLOAD, SyncEvent.ACCOMMODATION_CONFIG_CHANGED, SyncEvent.CONTINUOUS_PRICING_FLOOR_CEILING_OVERRIDE_CHANGED, SyncEvent.AGILE_RATES_CHANGED, SyncEvent.AGILE_RATES_OVERRIDE_CHANGED, SyncEvent.CONTINUOUS_PRICING_SPECIFIC_OVERRIDE_REMOVED, SyncEvent.GROUP_PRODUCT_CHANGED, SyncEvent.GROUP_PRODUCT_OVERRIDE_CHANGED,SyncEvent.PRICING_CONFIG_CHANGED);
        }
    }

    public void disableUploadButtonWhenDecisionUploadIsInProgress() {
        uploadButton.setEnabled(false);
        uploadButton.setDescription(getText("client.dashboard.upload.inprogress"));
    }

    public void updateWhatIfEnabled(boolean isWhatIfEnabled) {
        whatIfButton.setDescription(isWhatIfEnabled ? getText(PRICING_WHAT_IF) : getText("whatIfDisabledFromGlobalParam"));
    }

    private VerticalLayout createLegendLayout(boolean isInlineEditing) {
        HorizontalLayout headerLayout = new HorizontalLayout();
        headerLayout.setSizeFull();
        headerLayout.addStyleName("legend-header");
        TetrisLabel headerLabel = new TetrisLabel(UiUtils.getText("pricing.legend"));
        headerLabel.addStyleName("legend-header-label");

        TetrisImageButton closeButton = new TetrisImageButton(TetrisFontAwesome.CLOSE_X);
        closeButton.addStyleName("legend-header-close");
        closeButton.addClickListener(clickEvent -> legendLinkButton.hidePopup());

        headerLayout.addComponents(headerLabel, closeButton);
        headerLayout.setComponentAlignment(headerLabel, Alignment.MIDDLE_LEFT);
        headerLayout.setComponentAlignment(closeButton, Alignment.MIDDLE_RIGHT);

        TetrisLabel pricingAdjustmentOverrideIcon = new TetrisLabel();
        TetrisLabel specificOverrideIcon = new TetrisLabel();
        TetrisLabel floorOverrideIcon = new TetrisLabel();
        TetrisLabel ceilingOverrideIcon = new TetrisLabel();
        TetrisLabel pricingRuleTypeWarningIcon = new TetrisLabel();
        TetrisLabel lrvGreaterThanPriceIcon = new TetrisLabel();

        createLegendIcon(lrvGreaterThanPriceIcon, TetrisFontAwesome.LRV_GREATER_THAN_PRICE.getHtml() + "&nbsp" + UiUtils.getText("pricing.lrv.greater.than.price"));

        if (isInlineEditing) {
            createLegendIcon(pricingAdjustmentOverrideIcon, TetrisFontAwesome.BAR_OVERRIDE_PRICINGADJUSTMENT_INLINE.getHtml() + "&nbsp" + (presenter.isAgileRatesEnabled() ? UiUtils.getText("pricing") + " / " + UiUtils.getText("pricing.product.offset.override") : UiUtils.getText("pricing.override")));
        }

        createLegendIcon(specificOverrideIcon, TetrisFontAwesome.BAR_OVERRIDE_SPECIFIC.getHtml() + "&nbsp" + (presenter.isAgileRatesEnabled() ? UiUtils.getText("specific") + " / " + UiUtils.getText("pricing.product.offset.override") : UiUtils.getText("specificOverride")));
        createLegendIcon(floorOverrideIcon, TetrisFontAwesome.BAR_OVERRIDE_FLOOR.getHtml() + "&nbsp" + UiUtils.getText("floorOverride"));
        createLegendIcon(ceilingOverrideIcon, TetrisFontAwesome.BAR_OVERRIDE_CEILING.getHtml() + "&nbsp" + UiUtils.getText("ceilingOverride"));
        createLegendIcon(pricingRuleTypeWarningIcon, TetrisFontAwesome.RED_ALERT_TRIANGLE.getHtml() + "&nbsp" + UiUtils.getText("pricing.pricing.rule.type.warning"));

        TetrisLabel priceIncreaseIcon = new TetrisLabel();
        createLegendIcon(priceIncreaseIcon, TetrisFontAwesome.PRICE_INCREASE.getHtml() + "&nbsp" + UiUtils.getText("pricing.price.increase"));

        TetrisLabel priceDecreaseIcon = new TetrisLabel();
        createLegendIcon(priceDecreaseIcon, TetrisFontAwesome.PRICE_DECREASE.getHtml() + "&nbsp" + UiUtils.getText("pricing.price.decrease"));

        TetrisLabel priceImpactedIcon = new TetrisLabel();
        createLegendIcon(priceImpactedIcon, TetrisFontAwesome.PRICE_IMPACTED.getHtml() + "&nbsp" + UiUtils.getText("pricing.price.impacted"));

        TetrisLabel priceRestrictedIcon = new TetrisLabel();
        createLegendIcon(priceRestrictedIcon, TetrisFontAwesome.PRICE_RESTRICTED.getHtml() + "&nbsp" + UiUtils.getText("pricing.price.restricted.by.lra"));

        TetrisLabel rateProtectedIcon = new TetrisLabel();
        createLegendIcon(rateProtectedIcon, TetrisFontAwesome.RATE_PROTECTED.getHtml() + "&nbsp" + UiUtils.getText("pricing.rate.protected"));

        TetrisLabel unsavedChangesIcon = new TetrisLabel();
        createLegendIcon(unsavedChangesIcon, TetrisFontAwesome.UNDO_CHANGES.getHtml() + "&nbsp" + UiUtils.getText("unsaved.changes"));
        unsavedChangesIcon.setId("unssavedChangesIcon");

        TetrisLabel specialEventsIcon = new TetrisLabel();
        createLegendIcon(specialEventsIcon, TetrisFontAwesome.SPECIAL_EVENTS_STAR.getHtml() + "&nbsp" + UiUtils.getText("specialEvent"));
        specialEventsIcon.addStyleName("legend-icon");

        TetrisLabel baseRoomTypeIcon = new TetrisLabel();
        createLegendIcon(baseRoomTypeIcon, TetrisFontAwesome.BASE_ROOM_TYPE.getHtml() + "&nbsp" + UiUtils.getText("common.baseRoomType"));
        baseRoomTypeIcon.addStyleName("legend-icon");

        TetrisLabel groupFloorTypeIcon = new TetrisLabel();
        createLegendIcon(groupFloorTypeIcon, TetrisFontAwesome.BAR_OVERRIDE_GROUP_FLOOR.getHtml() + "&nbsp" + UiUtils.getText("groupfloorOverride"));
        groupFloorTypeIcon.addStyleName("legend-icon");

        TetrisLabel closeLv0Icon = new TetrisLabel();
        createLegendIcon(closeLv0Icon, TetrisFontAwesome.BAR_OVERRIDE_CLOSE_LV0.getHtml() + "&nbsp" + UiUtils.getText("barOverride.restrictHighestBar"));
        closeLv0Icon.addStyleName("legend-icon");

        TetrisLabel optimizedProducticon = new TetrisLabel();
        if (presenter.isIndependentProductsEnabled()) {
            createLegendIcon(optimizedProducticon, TetrisFontAwesome.TARGET_SMALL.getHtml() + "&nbsp" + UiUtils.getText("agile.rates.product.optimized.icon.description"));

        } else {
            createLegendIcon(optimizedProducticon, TetrisFontAwesome.TARGET_SMALL.getHtml() + "&nbsp" + UiUtils.getText("agile.rates.product.listing.is.optimized.icon.description"));
        }

        TetrisLabel linkedProductIcon = new TetrisLabel();
        createLegendIcon(linkedProductIcon, TetrisFontAwesome.LINK.getHtml() + "&nbsp" + UiUtils.getText("linked"));

        TetrisLabel independentProductIcon = new TetrisLabel();
        createLegendIcon(independentProductIcon, TetrisFontAwesome.CUBE.getHtml() + "&nbsp" + UiUtils.getText("agile.rates.product.independent.icon.description"));

        TetrisLabel manualRestrictionIcon = new TetrisLabel();
        createLegendIcon(manualRestrictionIcon, TetrisFontAwesome.USER_CHECK.getHtml() + "&nbsp" + UiUtils.getText("manual.restriction.present"));

        TetrisLabel smallGroupProductIcon = new TetrisLabel();
        createLegendIcon(smallGroupProductIcon, TetrisFontAwesome.USERS.getHtml() + "&nbsp" + UiUtils.getText("small.group.product"));

        TetrisLabel inventoryLimitIcon = new TetrisLabel();
        createLegendIcon(inventoryLimitIcon, TetrisFontAwesome.INVENTORY_LIMIT_OVERRIDE.getHtml() + "&nbsp" + UiUtils.getText("inventory.limit.override"));
        inventoryLimitIcon.addStyleName("inventory-limit-icon");

        VerticalLayout leftLayout = new VerticalLayout();
        leftLayout.setSpacing(false);
        leftLayout.setSizeUndefined();
        leftLayout.addStyleName("legend-layout");

        VerticalLayout rightLayout = new VerticalLayout();
        rightLayout.setSpacing(false);
        rightLayout.setSizeUndefined();
        rightLayout.addStyleName("legend-layout");

        if (!isInlineEditing) {
            leftLayout.addComponents(specificOverrideIcon, floorOverrideIcon, ceilingOverrideIcon, pricingRuleTypeWarningIcon);

            if (presenter.isGroupFloorEnabled()) {
                leftLayout.addComponent(groupFloorTypeIcon);
            }

            leftLayout.addComponents(lrvGreaterThanPriceIcon, baseRoomTypeIcon, specialEventsIcon);

            if (presenter.isLraEnabled()) {
                rightLayout.addComponents(priceIncreaseIcon, priceDecreaseIcon, priceRestrictedIcon, unsavedChangesIcon);
            } else {
                rightLayout.addComponents(priceIncreaseIcon, priceDecreaseIcon, priceImpactedIcon, unsavedChangesIcon);
            }

            rightLayout.addComponent(optimizedProducticon);

            if (presenter.isIndependentProductsEnabled() && presenter.isSmallGroupProductsEnabled()) {
                rightLayout.addComponents(linkedProductIcon, independentProductIcon, smallGroupProductIcon);
            }

            if (presenter.isIndependentProductsEnabled()) {
                rightLayout.addComponents(linkedProductIcon, independentProductIcon);
            }

            rightLayout.addComponent(manualRestrictionIcon);

            if (presenter.isFixedAboveBARProductEnabled()) {
                rightLayout.addComponent(rateProtectedIcon);
            }

            if (presenter.getHighestBarRestrictedEnabled()) {
                rightLayout.addComponent(closeLv0Icon);
            }

            if (presenter.isSmallGroupProductsEnabled()) {
                rightLayout.addComponents(smallGroupProductIcon);
            }
            if (presenter.isGroupProductInventoryLimitEnabled()){
                leftLayout.addComponent(inventoryLimitIcon);
            }

        } else {
            leftLayout.addComponents(pricingAdjustmentOverrideIcon, specificOverrideIcon, floorOverrideIcon, ceilingOverrideIcon, pricingRuleTypeWarningIcon);

            if (presenter.isGroupFloorEnabled()) {
                leftLayout.addComponent(groupFloorTypeIcon);
            }

            leftLayout.addComponents(lrvGreaterThanPriceIcon, baseRoomTypeIcon);

            if (presenter.isIndependentProductsEnabled() && presenter.isSmallGroupProductsEnabled()) {
                rightLayout.addComponents(optimizedProducticon, linkedProductIcon, independentProductIcon, smallGroupProductIcon);
            } else if (presenter.isIndependentProductsEnabled()) {
                rightLayout.addComponents(optimizedProducticon, linkedProductIcon, independentProductIcon);
            } else if (presenter.isSmallGroupProductsEnabled()) {
                rightLayout.addComponents(optimizedProducticon, smallGroupProductIcon);
            } else {
                leftLayout.addComponent(optimizedProducticon);
            }

            rightLayout.addComponents(priceIncreaseIcon, priceDecreaseIcon, unsavedChangesIcon, specialEventsIcon);
            if (presenter.isFixedAboveBARProductEnabled()) {
                rightLayout.addComponent(rateProtectedIcon);
            }

        }

        HorizontalLayout iconLayout = new HorizontalLayout();
        iconLayout.addComponents(leftLayout, rightLayout);

        VerticalLayout popUpLayout = new VerticalLayout();
        popUpLayout.setMargin(false);
        popUpLayout.addComponents(headerLayout, iconLayout);

        return popUpLayout;
    }

    private void createLegendIcon(TetrisLabel icon, String caption) {
        icon.setSizeUndefined();
        icon.setCaptionAsHtml(true);
        icon.setCaption(caption);
    }

    private void handleSimplifiedWhatIfResponse() {
        SimplifiedWhatIfResult response = presenter.simplifiedAnalyzeChanges();
        if (response != null) {
            WhatIfWindow window = new WhatIfWindow();
            window.setHeight(85, Unit.PERCENTAGE);
            window.setWidth(85, Unit.PERCENTAGE);
            window.setCaption(getText(PRICING_WHAT_IF));
            final SimplifiedWhatIfLayout whatIfLayout = new SimplifiedWhatIfLayout(window, response, createSimplifiedWhatIfLayoutDataParameter());
            whatIfLayout.setSizeUndefined();
            whatIfLayout.setMargin(true);
            //TODO: remove when grails removed and whatif officially redesigned
            whatIfLayout.addStyleName("pricing-whatif");
            window.setContent(new TetrisScrollablePanel(whatIfLayout));
            window.show();
        }
    }

    private SimplifiedWhatIfLayout.SimplifiedWhatIfLayoutDataParameter createSimplifiedWhatIfLayoutDataParameter() {
        return new SimplifiedWhatIfLayout.SimplifiedWhatIfLayoutDataParameter(
                presenter.isContinuousPricing(),
                presenter.isBarByLOS(),
                presenter.isDisplayOccupancyForecastInWhatIfEnabled(),
                presenter.isEnablePhysicalCapacityConsideration(),
                null,
                presenter.getMaxLos(), presenter.isDisplayProfitMetricsEnabled());
    }

    public void enableButtons(boolean enableWhatIfAndCancelButtons, boolean enableSaveButton) {
        //TODO Check if what if should be tied to save rather than cancel as it needs a pending override which follows that save should be enabled to run a what if
        saveButton.setEnabled(enableSaveButton);
        alertLayout.setVisible(presenter.isSyncVisibleForPricingStaleness());
        if (presenter.isWhatIfEnabled()) {
            whatIfButton.setEnabled(enableWhatIfAndCancelButtons);
        }
        cancelButton.setEnabled(enableWhatIfAndCancelButtons);
    }

    public void enableButtons(boolean enableCancelButton, boolean enableSaveButton, boolean enableWhatIfButton) {
        saveButton.setEnabled(enableSaveButton);
        alertLayout.setVisible(presenter.isSyncVisibleForPricingStaleness());
        if (presenter.isWhatIfEnabled()) {
            whatIfButton.setEnabled(enableWhatIfButton);
        }
        cancelButton.setEnabled(enableCancelButton);
    }

    private void setUpBinder() {
        filterBinder.forField(productsCheckBoxes)
                .bind(CPPricingFilter::getProducts, CPPricingFilter::setProducts);

        filterBinder.forField(startDate)
                .asRequired(getText("date.empty.not.allowed"))
                .bind(CPPricingFilter::getJavaStartDate, CPPricingFilter::setStartDate);

        filterBinder.forField(endDate)
                .asRequired(getText("date.empty.not.allowed"))
                .withValidator((event, context) -> {
                    if (presenter.dateOutOfRange(startDate.getValue(), event)) {
                        incorrectDateLayout.setVisible(true);
                        incorrectDateLabel.setValue(getText("pricing.filter.max.date.range.error"));
                        return ValidationResult.error("");
                    } else if (!endDate.isValueChangeEventDisabled() && presenter.startDateAfterEndDate(startDate.getValue(), event)) {
                        incorrectDateLayout.setVisible(true);
                        incorrectDateLabel.setValue(getText("common.date.validation.ToBeforeFrom"));
                        return ValidationResult.error("");
                    } else {
                        incorrectDateLayout.setVisible(false);
                        presenter.getCpPricingFilter().setEndDate(event);
                        return ValidationResult.ok();
                    }
                })
                .bind(CPPricingFilter::getJavaEndDate, CPPricingFilter::setEndDate);

        filterBinder.forField(roomClass)
                .bind(CPPricingFilter::getSelectedRoomClass, CPPricingFilter::setSelectedRoomClass);

        filterBinder.forField(roomTypeCheckBoxes)
                .bind(CPPricingFilter::getSelectedRoomTypes, CPPricingFilter::setSelectedRoomTypes);

        filterBinder.forField(applyOverridesToAllRoomClassesCheckbox)
                .bind(CPPricingFilter::isApplyOverridesToAllRoomClasses, CPPricingFilter::setApplyOverridesToAllRoomClasses);

        filterBinder.forField(inventoryGroupSelection)
                .bind(CPPricingFilter::getInventoryGroupDto, CPPricingFilter::setInventoryGroupDto);
    }

    private void addDateValueChangeListeners() {
        startDate.addValueChangeListener(valueChangeEvent -> {
            if (valueChangeEvent.isUserOriginated()) {
                if (!presenter.isCardViewSelected() && pricingInlineEditGrid != null && pricingInlineEditGrid.gridHasChanges()) {
                    startDate.setValue(valueChangeEvent.getOldValue());
                    presenter.showWarning(getText("unsavedChangesCommonWarningMessage"));
                } else if (!filterBinder.isValid() && valueChangeEvent.getValue() != null) {
                    endDate.setValueChangeEventDisabled(true);
                    int pricingLoadWindowDays = presenter.getPricingLoadWindowDays();
                    LocalDate newEndDate = valueChangeEvent.getValue().plusDays(pricingLoadWindowDays);
                    endDate.setValue(newEndDate);
                    presenter.getCpPricingFilter().setEndDate(newEndDate);
                    endDate.setValueChangeEventDisabled(false);
                    filterBinder.validate();
                    if (!presenter.isCardViewSelected() && !pricingFilter.getSelectedCompetitors().isEmpty()) {
                        if (presenter.isIndependentProductsEnabled() || presenter.isRDLEnabled()) {
                            presenter.setAllCompetitorDetailsRows(pricingFilter.getSelectedCompetitors());
                        } else {
                            presenter.setCompetitorDetailsRows(pricingFilter.getSelectedCompetitors());
                        }
                    }
                    updateWhatIfButtonVisibility();
                    pricingFilter.setFilterAsPerExtendedWindow();
                    filterBinder.getBean().setProducts(productsCheckBoxes.getValue());
                    updateDecisions(true);
                } else if (filterBinder.isValid()) {
                    endDate.setValueChangeEventDisabled(true);
                    int pricingLoadWindowDays = presenter.getPricingLoadWindowDays();
                    LocalDate newEndDate = valueChangeEvent.getValue().plusDays(pricingLoadWindowDays);
                    endDate.setValue(newEndDate);
                    presenter.getCpPricingFilter().setEndDate(newEndDate);
                    endDate.setValueChangeEventDisabled(false);
                    if (!presenter.isCardViewSelected() && !pricingFilter.getSelectedCompetitors().isEmpty()) {
                        if (presenter.isIndependentProductsEnabled() || presenter.isRDLEnabled()) {
                            presenter.setAllCompetitorDetailsRows(pricingFilter.getSelectedCompetitors());
                        } else {
                            presenter.setCompetitorDetailsRows(pricingFilter.getSelectedCompetitors());
                        }
                    }
                    updateWhatIfButtonVisibility();
                    pricingFilter.setFilterAsPerExtendedWindow();
                    filterBinder.getBean().setProducts(productsCheckBoxes.getValue());
                    updateDecisions(true);
                }
            }
        });
        endDate.addValueChangeListener(valueChangeEvent -> {
            if (valueChangeEvent.isUserOriginated()) {
                if (!presenter.isCardViewSelected() && pricingInlineEditGrid != null && pricingInlineEditGrid.gridHasChanges()) {
                    endDate.setValue(valueChangeEvent.getOldValue());
                    presenter.showWarning(getText("unsavedChangesCommonWarningMessage"));
                } else if (!filterBinder.isValid() && valueChangeEvent.getValue() != null) {
                    if (startDate.getValue() != null && presenter.dateOutOfRange(startDate.getValue(), valueChangeEvent.getValue())) {
                        //Pop up here
                        TetrisMessageBox notificationDialogue = presenter.createWarningDialogue(UiUtils.getText("notification"), UiUtils.getText("pricing.filter.max.date.range.error"));
                        notificationDialogue.setNoButtonVisible(false);
                        notificationDialogue.setYesClickListener(ignored -> {
                            endDate.setValue(startDate.getValue().plusDays(91));
                            filterBinder.validate();
                            if (!presenter.isCardViewSelected() && !pricingFilter.getSelectedCompetitors().isEmpty()) {
                                if (presenter.isIndependentProductsEnabled() || presenter.isRDLEnabled()) {
                                    presenter.setAllCompetitorDetailsRows(pricingFilter.getSelectedCompetitors());
                                } else {
                                    presenter.setCompetitorDetailsRows(pricingFilter.getSelectedCompetitors());
                                }
                            }
                            updateWhatIfButtonVisibility();
                            pricingFilter.setFilterAsPerExtendedWindow();
                            filterBinder.getBean().setProducts(productsCheckBoxes.getValue());
                            updateDecisions(true);
                        });
                        notificationDialogue.setWidth(430, Unit.PIXELS);
                        notificationDialogue.setYesLabel(UiUtils.getText("ok"));
                        notificationDialogue.show();
                    } else {
                        startDate.setValueChangeEventDisabled(true);
                        int pricingLoadWindowDays = presenter.getPricingLoadWindowDays();
                        LocalDate newStartDate = valueChangeEvent.getValue().minusDays(pricingLoadWindowDays);
                        startDate.setValue(newStartDate);
                        presenter.getCpPricingFilter().setStartDate(newStartDate);
                        startDate.setValueChangeEventDisabled(false);
                        filterBinder.validate();
                        if (!presenter.isCardViewSelected() && !pricingFilter.getSelectedCompetitors().isEmpty()) {
                            if (presenter.isIndependentProductsEnabled() || presenter.isRDLEnabled()) {
                                presenter.setAllCompetitorDetailsRows(pricingFilter.getSelectedCompetitors());
                            } else {
                                presenter.setCompetitorDetailsRows(pricingFilter.getSelectedCompetitors());
                            }
                        }
                        pricingFilter.setFilterAsPerExtendedWindow();
                        filterBinder.getBean().setProducts(productsCheckBoxes.getValue());
                        updateDecisions(true);
                    }
                } else if (filterBinder.isValid()) {
                    if (!presenter.isCardViewSelected() && !pricingFilter.getSelectedCompetitors().isEmpty()) {
                        if (presenter.isIndependentProductsEnabled() || presenter.isRDLEnabled()) {
                            presenter.setAllCompetitorDetailsRows(pricingFilter.getSelectedCompetitors());
                        } else {
                            presenter.setCompetitorDetailsRows(pricingFilter.getSelectedCompetitors());
                        }
                    }
                    updateWhatIfButtonVisibility();
                    pricingFilter.setFilterAsPerExtendedWindow();
                    filterBinder.getBean().setProducts(productsCheckBoxes.getValue());
                    updateDecisions(true);
                }
            }
        });
    }

    private void addFilterBarChangeListeners() {
        roomClass.addValueChangeListener(valueChangeEvent -> {
            if (valueChangeEvent.isUserOriginated()) {
                if (!presenter.isCardViewSelected() && pricingInlineEditGrid != null && pricingInlineEditGrid.gridHasChanges()) {
                    roomClass.setValue(valueChangeEvent.getOldValue());
                    presenter.showWarning(getText("unsavedChangesCommonWarningMessage"));
                } else if (filterBinder.isValid()) {
                    if (!presenter.isCardViewSelected() && !pricingFilter.getSelectedCompetitors().isEmpty()) {
                        if (presenter.isIndependentProductsEnabled() || presenter.isRDLEnabled()) {
                            presenter.setAllCompetitorDetailsRows(pricingFilter.getSelectedCompetitors());
                        } else {
                            presenter.setCompetitorDetailsRows(pricingFilter.getSelectedCompetitors());
                        }
                    }
                    //We want to get updateDecisions due not getting all room type data upfront now
                    updateDecisions(true);
                }
            }
        });
        inventoryGroupSelection.addValueChangeListener(valueChangeEvent -> {
            if (valueChangeEvent.isUserOriginated()) {
                if (!presenter.isCardViewSelected() && pricingInlineEditGrid != null && pricingInlineEditGrid.gridHasChanges()) {
                    inventoryGroupSelection.setValue(valueChangeEvent.getOldValue());
                    presenter.showWarning(getText("unsavedChangesCommonWarningMessage"));
                } else if (filterBinder.isValid()) {
                    if (!presenter.isCardViewSelected() && !pricingFilter.getSelectedCompetitors().isEmpty()) {
                        if (presenter.isIndependentProductsEnabled() || presenter.isRDLEnabled()) {
                            presenter.setAllCompetitorDetailsRows(pricingFilter.getSelectedCompetitors());
                        } else {
                            presenter.setCompetitorDetailsRows(pricingFilter.getSelectedCompetitors());
                        }
                    }
                    updateDecisions(true);
                }
            }
        });
        roomTypeCheckBoxes.addValueChangeListener(valueChangeEvent -> {
            if (valueChangeEvent.isUserOriginated()) {
                if (!presenter.isCardViewSelected() && pricingInlineEditGrid != null && pricingInlineEditGrid.gridHasChanges()) {
                    roomTypeCheckBoxes.setValue(valueChangeEvent.getOldValue());
                    presenter.showWarning(getText("unsavedChangesCommonWarningMessage"));
                } else if (filterBinder.isValid()) {
                    if (!presenter.isCardViewSelected() && !pricingFilter.getSelectedCompetitors().isEmpty()) {
                        if (presenter.isIndependentProductsEnabled() || presenter.isRDLEnabled()) {
                            presenter.setAllCompetitorDetailsRows(pricingFilter.getSelectedCompetitors());
                        } else {
                            presenter.setCompetitorDetailsRows(pricingFilter.getSelectedCompetitors());
                        }
                    }
                    updateDecisions(true);
                }
            }
        });
        applyOverridesToAllRoomClassesCheckbox.addValueChangeListener(event -> {
            if (pricingInlineEditGrid != null) {
                pricingInlineEditGrid.setIsSameForAllRoomClasses(event.getValue());
            }
        });
    }

    public void updateDecisions(boolean dateChange) {
        if (presenter.hasUnsavedChanges()) {
            showUnsavedChangesWindow();
        } else {
            updateLayout(dateChange);
        }
    }

    public void showRoomTypeLayout(boolean isShowOnlyBaseRoomTypesFlag) {
        roomTypeLayout.setVisible(!isShowOnlyBaseRoomTypesFlag);
    }

    public void showUnsavedChangesWindow() {
        if (!unsavedChangesWindowShowing) {
            TetrisUnsavedChangesWindow unsavedChangesWindow = new TetrisUnsavedChangesWindow("pricingPage", UiUtils.getText("alert"), false);
            unsavedChangesWindow.addSaveClickListener(event -> {
                saveUnsavedOverrides();
                unsavedChangesWindow.close();
            });
            unsavedChangesWindow.addContinueWithoutSavingClickListener(event -> {
                clearUnsavedOverrides();
                unsavedChangesWindow.close();
            });
            unsavedChangesWindow.addCloseListener((Window.CloseListener) e -> clearUnsavedOverrides());
            getUI().addWindow(unsavedChangesWindow);
            unsavedChangesWindowShowing = true;
        }
    }

    public void saveUnsavedOverrides() {
        presenter.save();
        enableButtons(false, false);
        updateUploadEnabled(presenter.isManualUploadEnabled(), presenter.isPropertyStageTwoWay(), presenter.isBarOverridePendingForUpload(), presenter.isAgileRatesPendingForUpload(), presenter.isInventoryLimitPendingForUpload());
        unsavedChangesWindowShowing = false;
        updateLayout(true);
    }

    public void clearUnsavedOverrides() {
        presenter.clearPendingOverrides();
        unsavedChangesWindowShowing = false;
        updateLayout(true);
    }

    public void updateLayout(boolean dateChange) {
        if (presenter.isCardViewSelected()) {
            UiUtils.updatePageHelpId("1012");
            updatePricingDayDetailsLayout(dateChange);
        } else {
            UiUtils.updatePageHelpId("1091");
            updateGridView(dateChange);
        }
    }

    public void updatePricingDayDetailsLayout(boolean dateChange) {
        long t1 = System.currentTimeMillis();
        List<CPBARDecisionUIWrapper> wrappers = presenter.wrapDecisions(dateChange);
        long t2 = System.currentTimeMillis();
        if (CollectionUtils.isNotEmpty(wrappers)) {
            if (dateChange) {
                if (SystemConfig.isMultiThreadingEnabledOnPricingScreen()) {
                    presenter.updateNotesAndManualRestrictionsDtoInParallel();
                } else {
                    presenter.updateNotesAndManualRestrictionsDto();
                }
            }
            long t3 = System.currentTimeMillis();
            pricingDayDetailsLayout.createDayCards(wrappers, presenter);
            long t4 = System.currentTimeMillis();
            pricingDayDetailsLayout.setVisible(true);
            errorBox.setVisible(false);
            pricingDayDetailsLayout.displayDayCards();
            updateMultiDayOverrideButton();
            long t5 = System.currentTimeMillis();
            logger.info("PricingDataFetch=" + (t2 - t1) + ":PricingUpdateNotes=" + (t3 - t2) + ":PricingCreateDayCards=" + (t4 - t3) + ":PricingDisplayCards=" + (t5 - t4) + ":PricingScreenTotal=" + (t5 - t1));
        } else {
            pricingDayDetailsLayout.setVisible(false);
            errorBox.setVisible(true);
            pricingDayDetailsLayout.setSizeFull();
            pricingDayDetailsLayout.addStyleName(TetrisTheme.LIGHT_BLUE_BACKGROUND);
            overrideButton.setEnabled(false);
        }
    }

    private void updateGridView(boolean dateChange) {
        List<CPBARDecisionUIWrapper> wrappers = presenter.wrapDecisions(dateChange);
        if (CollectionUtils.isNotEmpty(wrappers)) {
            pricingInlineEditGrid.setVisible(true);
            errorBox.setVisible(false);
            if (dateChange) {
                if (SystemConfig.isMultiThreadingEnabledOnPricingScreen()) {
                    presenter.updateNotesAndManualRestrictionsDtoInParallel();
                } else {
                    presenter.updateNotesAndManualRestrictionsDto();
                }
            }
            updateMultiDayOverrideButton();
            pricingInlineEditGrid.updateData(wrappers);
        } else {
            overrideButton.setEnabled(false);
            pricingInlineEditGrid.setVisible(false);
            errorBox.setVisible(true);
        }
    }

    private void updateMultiDayOverrideButton() {
        overrideButton.setEnabled(!endDate.getValue().isBefore(LocalDate.parse(presenter.getSystemDateAsLocalDate().toString())));
    }

    public void refreshView() {
        pricingDayDetailsLayout.createDayCards(presenter.getResults(), presenter);
        pricingDayDetailsLayout.displayDayCards();
    }

    public void setSearchCriteria(CPPricingFilter criteria) {
        startDate.setValueChangeEventDisabled(true);
        endDate.setValueChangeEventDisabled(true);
        filterBinder.setBean(criteria);
        initFilterComponents();
        startDate.setValueChangeEventDisabled(false);
        endDate.setValueChangeEventDisabled(false);

        updateProductsButtonCaption(criteria.getProducts());
        productsCheckBoxes.setValue(criteria.getProducts());
    }

    private void updateProductsButtonCaption(Set<Product> selectedProducts) {
        if (selectedProducts.size() == 1) {
            String productName = presenter.truncateLabel(selectedProducts.iterator().next().getName(), presenter.MAX_PRODUCT_LABEL_LENGTH);
            productsButton.setCaption(productName);
        } else {
            productsButton.setCaption(getText("Selected") + " " + selectedProducts.size());
        }
    }

    public void setAccomClasses(List<AccomClass> accomClasses) {
        roomClass.setItems(accomClasses);
    }

    public void setInventoryGroups(List<InventoryGroupDto> inventoryGroups) {
        inventoryGroupSelection.clear();
        inventoryGroupSelection.setItems(inventoryGroups);
        inventoryGroupSelection.setValue(inventoryGroups.stream().filter(inventoryGroupDto -> inventoryGroupDto.isDefaultPropertyInventoryGroup()).findFirst().get());
    }

    public void setProducts(List<Product> productList) {
        productList = productList.stream()
                .sorted(Comparator.comparing(Product::getDisplayOrder))
                .collect(Collectors.toList());
        productsCheckBoxes.setItems(productList);
        if (CollectionUtils.isNotEmpty(productList) && productList.size() > 20) {
            productsCheckBoxes.setWidth(160, Unit.PIXELS);
            productsCheckBoxes.setHeight(450, Unit.PIXELS);
        }
    }

    public void showProducts(boolean show) {
        productsLayout.setVisible(show);

        //Agile rates enabled = show
        if (show) {
            incorrectDateLabelSpacer.setWidth(330, Unit.PIXELS);
        }
    }

    public void onApplyOverrideEvent(@Observes(notifyObserver = Reception.IF_EXISTS) ApplyCPOverrideEvent event) {
        applySingleDayOverrideFromInvestigatorPopUp(event.getOverride());
    }

    public void applySingleDayOverrideFromInvestigatorPopUp(CPOverrideWrapper cpOverrideWrapper) {
        presenter.applySingleDayOverrideFromInvestigatorPopUp(cpOverrideWrapper);
    }

    public void onApplySingleDayOverrideChangesEvent(@Observes(notifyObserver = Reception.IF_EXISTS) ApplyCPOverrideChangesEvent event) {
        applySingleDayOverrideChanges(event.getOverride());
    }

    public void applySingleDayOverrideChanges(CPOverrideWrapper cpOverrideWrapper) {
        presenter.applySingleDayOverrideChanges(cpOverrideWrapper, null, null);
    }

    public void onRemoveOverrideEvent(@Observes(notifyObserver = Reception.IF_EXISTS) RemoveCPOverrideEvent event) {
        removeSingleDayOverrideFromInvestigatorPopUp(event.getOverride());
    }

    public void removeSingleDayOverrideFromInvestigatorPopUp(CPOverrideWrapper cpOverrideWrapper) {
        presenter.removeSingleDayOverrideFromInvestigatorPopUp(cpOverrideWrapper);
    }

    public void resetFilter() {
        startDate.setValueChangeEventDisabled(true);
        endDate.setValueChangeEventDisabled(true);

        int pricingLoadWindowDays = presenter.getPricingLoadWindowDays();
        CPPricingFilter defaultCPPricingFilter = presenter.createDefaultCPPricingFilter(presenter.getSystemDateAsLocalDate(), presenter.getSystemDateAsLocalDate().plusDays(pricingLoadWindowDays), presenter.getAllRoomClasses(), presenter.getDefaultInventoryGroup());
        setSearchCriteria(defaultCPPricingFilter);
        roomTypeCheckBoxes.setItems(presenter.getBaseRoomTypeList());
        presenter.setCpPricingFilter(defaultCPPricingFilter);
        updateWhatIfButtonVisibility();
        updateCompetitorsAfterViewSwitch();
        updateDecisions(true);

        startDate.setValueChangeEventDisabled(false);
        endDate.setValueChangeEventDisabled(false);
    }

    public void setButtonEnableRequirements(boolean effectiveReadOnlyEnhanced) {
        saveButton.setEnabledDuringIDP(effectiveReadOnlyEnhanced, TetrisPermissionKey.PRICING, SyncEvent.ACCOMMODATION_CONFIG_CHANGED);
        whatIfButton.setEnabledDuringIDP(Boolean.TRUE, TetrisPermissionKey.PRICING_WHAT_IF, SyncEvent.ACCOMMODATION_CONFIG_CHANGED);
    }

    public void adjustFilterAndLegendForInlineEdit(boolean isDayCardView) {
        buttonBarRight.removeComponent(legendLinkButton);
        legendLinkButton = addLegendButton(!isDayCardView);
        buttonBarRight.addComponent((legendLinkButton));
        buttonBarRight.setComponentAlignment(legendLinkButton, Alignment.MIDDLE_RIGHT);
        buttonBarLayout.setComponentAlignment(buttonBarRight, Alignment.TOP_RIGHT);
        alertLayout.setVisible(presenter.isSyncVisibleForPricingStaleness());
        initFilterComponents();
    }

    public void displayView(PricingToggleView selectedView) {
        if (PricingToggleView.CARD.equals(selectedView)) {
            dayCardModeButton.setEnabled(false);
            dayCardModeButton.setVisible(false);
            inlineModeButton.setEnabled(true);
            inlineModeButton.setVisible(true);
            if (pricingDayDetailsLayout != null) {
                pricingDayDetailsLayout.setVisible(true);
            }

            //set grid view to false
            if (pricingInlineEditGrid != null) {
                pricingInlineEditGrid.setVisible(false);
            }
        } else {
            inlineModeButton.setEnabled(false);
            inlineModeButton.setVisible(false);
            dayCardModeButton.setEnabled(true);
            dayCardModeButton.setVisible(true);

            if (pricingDayDetailsLayout != null) {
                pricingDayDetailsLayout.setVisible(false);
            }

            //instantiate the grid if need be
            if (pricingInlineEditGrid == null) {
                pricingInlineEditGrid = new PricingInlineEditGrid(this.presenter);
                layout.addComponent(pricingInlineEditGrid);
            } else {
                pricingInlineEditGrid.clearOverrideWrappers();
            }
            pricingInlineEditGrid.setVisible(true);

        }
        adjustFilterAndLegendForInlineEdit(PricingToggleView.CARD.equals(selectedView));
        inventoryGroupSelection.setVisible(presenter.isInventoryGroupSelectionShown());
    }

    public void showApplyOverridesToAllRoomClassesCheckbox() {
        //only display applyOverridesToAllRoomClassesCheckbox if agile rates are enabled
        applyOverridesToAllRoomClassesCheckbox.setVisible(presenter.isAgileRatesEnabled());
        //enable applyOverridesToAllRoomClassesCheckbox if agile rates product is selected
        applyOverridesToAllRoomClassesCheckbox.setEnabled(presenter.getCpPricingFilter().isAgileRateProductSelected());
    }

    public boolean isApplyOverridesToAllRoomClassesCheckboxEnabled() {
        return applyOverridesToAllRoomClassesCheckbox.isEnabled();
    }

    public Map<String, CPOverrideWrapper> getCPOverrideMapsFromGrid() {
        return pricingInlineEditGrid.getOverrideWrapperMap();
    }

    public Map<String, PricingAgileRatesOverrideDTO> getAgileRatesFromInlineGrid() {
        return pricingInlineEditGrid.getAgileRatesOverrideMap();
    }

    public boolean pricingInlineEditGridHasChanges() {
        return pricingInlineEditGrid.gridHasChanges();
    }

    public void clearPricingInlineEditGridOverrideWrappers() {
        pricingInlineEditGrid.clearOverrideWrappers();
    }

    public void applyMultidayOptimizedProductsToInlineEditGrid(List<PricingMultidayGridDTO> changedDTOs, org.joda.time.LocalDate startDate, org.joda.time.LocalDate endDate, List<Integer> calendarDaysOfWeek) {
        pricingInlineEditGrid.applyOptimizedProductsToGrid(changedDTOs, startDate, endDate, calendarDaysOfWeek);
    }

    public void applyMultidayNonOptimizedProductsToInlineEditGrid(List<PricingMultidayGridDTO> changedDTOs, org.joda.time.LocalDate startDate, org.joda.time.LocalDate endDate, List<Integer> calendarDaysOfWeek) {
        pricingInlineEditGrid.applyNonOptimizedProductsToGrid(changedDTOs, startDate, endDate, calendarDaysOfWeek);
    }

    public void applyMultidayBARProductsToInlineEditGrid(List<PricingMultidayGridDTO> changedDTOs, org.joda.time.LocalDate startDate, org.joda.time.LocalDate endDate, List<Integer> calendarDaysOfWeek, PricingMultiProductMultidayFilterDTO filterDTO) {
        pricingInlineEditGrid.applyBARProductsToGrid(changedDTOs, startDate, endDate, calendarDaysOfWeek, filterDTO);
    }

    public void observePropertyStateChangedEvent(@Observes(notifyObserver = Reception.IF_EXISTS) PropertyStateChangedEvent propertyStateChangedEvent) {
        if (propertyStateChangedEvent.getPropertyState().isShowReadOnlyNotification() && !presenter.getIsReadOnlyNotificationDisplayed()) {
            presenter.setIsReadOnlyNotificationDisplayed(true);
            showWarningPromptWithoutNoLabel(getText("read.only.notification.title"),
                    getText("read.only.notification.message"), "OK", null);
        } else if (!propertyStateChangedEvent.getPropertyState().isShowReadOnlyNotification()) {
            presenter.setIsReadOnlyNotificationDisplayed(false);
        }
    }
}