package com.ideas.tetris.ui.modules.roomconfiguration;

import com.google.common.annotations.VisibleForTesting;
import com.ideas.infra.tetris.security.domain.TetrisPermissionKey;
import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.configparams.GUIConfigParamName;
import com.ideas.tetris.pacman.common.configparams.IPConfigParamName;
import com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.common.utils.systemconfig.SystemConfig;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.accommodation.AccomTypeADR;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomClass;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomClassPriceRank;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomClassPriceRankNetworkArrow;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomClassPriceRankStatus;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomType;
import com.ideas.tetris.pacman.services.accommodation.entity.RoomConfigurationSelection;
import com.ideas.tetris.pacman.services.accommodation.service.AccomClassPriceRankService;
import com.ideas.tetris.pacman.services.accommodation.service.AccommodationService;
import com.ideas.tetris.pacman.services.agilerates.configuration.entity.ProductAccomType;
import com.ideas.tetris.pacman.services.agilerates.configuration.service.AgileRatesConfigurationService;
import com.ideas.tetris.pacman.services.bestavailablerate.CloseHighestBarService;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.AccomClassMinPriceDiff;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.AccomClassMinPriceDiffSeason;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.DailyMinPriceDiff;
import com.ideas.tetris.pacman.services.client.service.ClientService;
import com.ideas.tetris.pacman.services.componentrooms.dto.ComponentRoomsConfiguration;
import com.ideas.tetris.pacman.services.componentrooms.services.ComponentRoomService;
import com.ideas.tetris.pacman.services.configautomation.ConfigAutomationService;
import com.ideas.tetris.pacman.services.costofwalk.entity.CostofWalk;
import com.ideas.tetris.pacman.services.costofwalk.service.CostofWalkService;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.decisionconfig.SubscriptionEnum;
import com.ideas.tetris.pacman.services.eventaggregator.SyncEvent;
import com.ideas.tetris.pacman.services.eventaggregator.SyncEventAggregatorService;
import com.ideas.tetris.pacman.services.groupfinalforecast.GroupFinalForecastConfigService;
import com.ideas.tetris.pacman.services.groupfinalforecast.entity.GroupFinalForecastConfig;
import com.ideas.tetris.pacman.services.grouppricing.configuration.entity.GroupPricingConfigAccomType;
import com.ideas.tetris.pacman.services.grouppricing.configuration.service.GroupPricingConfigurationService;
import com.ideas.tetris.pacman.services.hospitalityrooms.entity.HospitalityRoomsConfig;
import com.ideas.tetris.pacman.services.hospitalityrooms.service.HospitalityRoomsService;
import com.ideas.tetris.pacman.services.informationmanager.exception.service.ExceptionConfigService;
import com.ideas.tetris.pacman.services.inventorygroup.service.InventoryGroupService;
import com.ideas.tetris.pacman.services.lrvdroprestriction.service.LrvDropRestrictionService;
import com.ideas.tetris.pacman.services.opera.CostOfWalkCalculator;
import com.ideas.tetris.pacman.services.overbooking.dto.OverbookingAccomTypeDto;
import com.ideas.tetris.pacman.services.overbooking.dto.OverbookingAccomTypeSeasonDto;
import com.ideas.tetris.pacman.services.overbooking.service.OverbookingService;
import com.ideas.tetris.pacman.services.override.InvalidateOverridesService;
import com.ideas.tetris.pacman.services.perpersonpricing.MaximumOccupantsEntity;
import com.ideas.tetris.pacman.services.perpersonpricing.PerPersonPricingService;
import com.ideas.tetris.pacman.services.pricingconfiguration.entity.PricingAccomClass;
import com.ideas.tetris.pacman.services.pricingconfiguration.service.PricingConfigurationLTBDEService;
import com.ideas.tetris.pacman.services.pricingconfiguration.service.PricingConfigurationService;
import com.ideas.tetris.pacman.services.product.Product;
import com.ideas.tetris.pacman.services.ratepopulation.QualifiedRateService;
import com.ideas.tetris.pacman.services.restfortesting.ForTesting;
import com.ideas.tetris.pacman.services.roa.attribute.entity.PropertyAttributeEnum;
import com.ideas.tetris.pacman.services.roa.attribute.service.ROAPropertyAttributeService;
import com.ideas.tetris.pacman.services.roomtyperecoding.services.RoomTypeRecodingService;
import com.ideas.tetris.pacman.services.security.GlobalUser;
import com.ideas.tetris.pacman.services.security.UserGlobalDBService;
import com.ideas.tetris.pacman.services.tax.entity.Tax;
import com.ideas.tetris.pacman.services.tax.service.TaxService;
import com.ideas.tetris.pacman.services.webrate.service.AccommodationMappingService;
import com.ideas.tetris.pacman.util.Runner;
import com.ideas.tetris.pacman.util.Season;
import com.ideas.tetris.pacman.util.SeasonService;
import com.ideas.tetris.platform.common.entity.DayOfWeek;
import com.ideas.tetris.platform.common.license.LicenseService;
import com.ideas.tetris.platform.common.license.util.Operator;
import com.ideas.tetris.platform.common.time.LocalDateUtils;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.common.xml.schema.workcontext.v1.WorkContextType;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import com.ideas.tetris.platform.services.daoandentities.entity.Status;
import com.ideas.tetris.ui.common.cdi.ReportURLUsageLinkCreator;
import com.ideas.tetris.ui.common.cdi.TetrisPresenter;
import com.ideas.tetris.ui.common.component.wizard.TetrisWizard;
import com.ideas.tetris.ui.common.security.UiContext;
import com.ideas.tetris.ui.common.util.UiUtils;
import com.ideas.tetris.ui.modules.pricingconfiguration.layout.InvalidSeasonDatesLayout;
import com.ideas.tetris.ui.modules.roomconfiguration.mapping.views.RoomTypeProductWrapper;
import com.ideas.tetris.ui.modules.roomconfiguration.overbooking.OverbookingSeasonUIWrapper;
import com.ideas.tetris.ui.modules.roomconfiguration.pricediff.MinimumPriceDifferentialUIWrapper;
import com.ideas.tetris.ui.modules.roomconfiguration.roomclassratioconfig.RoomClassRatioConfigDto;
import com.ideas.tetris.ui.modules.roomconfiguration.views.RoomsConfigurationWizardView;
import com.ideas.tetris.ui.shell.HelpUtil;
import com.rits.cloning.Cloner;
import com.vaadin.v7.ui.Label;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.vaadin.teemu.wizards.WizardStep;

import javax.inject.Inject;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.SortedSet;
import java.util.TreeSet;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import static com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName.FEATURE_LICENSING_ENABLED;
import static com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName.RE_CONFIGURE_SELECTIVE_AUTOMATION_ENABLED;
import static com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName.ENABLE_ROOM_CLASS_EXCLUSION_IN_ROH_GROUP_EVALUATION;
import static java.util.Objects.nonNull;
import org.springframework.beans.factory.annotation.Autowired;
import com.ideas.tetris.spring.SpringAutowired;

@SpringAutowired
public class RoomsConfigurationPresenter extends TetrisPresenter<RoomsConfigurationWizardView, Void> {
    @Autowired
	private AccommodationService accommodationService;
    @Autowired
	private CostofWalkService costofWalkService;
    @Autowired
	private OverbookingService overbookingService;
    @Autowired
	private AccomClassPriceRankService accomClassPriceRankService;
    @Autowired
	private InvalidateOverridesService invalidateOverridesService;
    @Autowired
	private AccommodationMappingService accommodationMappingService;
    @Autowired
	private SeasonService seasonService;
    @Autowired
	private PricingConfigurationService pricingConfigurationService;
    @Autowired
	private ExceptionConfigService exceptionConfigService;
    @Autowired
	private ComponentRoomService componentRoomService;
    @Autowired
	private HospitalityRoomsService hospitalityRoomsService;
    @Autowired
	private CloseHighestBarService closeHighestBarService;
    @Autowired
	private InventoryGroupService inventoryGroupService;
    @Autowired
	private PerPersonPricingService perPersonPricingService;
    @Autowired
	private RoomTypeRecodingService roomTypeRecodingService;
    @Autowired
	private DateService dateService;
    @Autowired
	private AgileRatesConfigurationService agileRatesConfigurationService;
    @Autowired
	private LrvDropRestrictionService lrvDropRestrictionService;
    @Autowired
	private SyncEventAggregatorService syncEventAggregatorService;
    @Autowired
	private QualifiedRateService qualifiedRateService;

    @Autowired
    PacmanConfigParamsService configParamsService;

    @Autowired
	private TaxService taxService;
    @Autowired
    private ROAPropertyAttributeService propertyAttributeService;

    @Inject
    ReportURLUsageLinkCreator reportURLUsageLinkCreator;

    @Autowired
    ClientService clientService;

    @Autowired
    PacmanConfigParamsService configService;

    @Autowired
    CostOfWalkCalculator costOfWalkCalculator;

    @Autowired
    PricingConfigurationLTBDEService pricingConfigurationLTBDEService;

    @Autowired
	private LicenseService licenseService;
    @Autowired
	private UserGlobalDBService userGlobalDBService;

    public ReportURLUsageLinkCreator getReportURLUsageLinkCreator() {
        return reportURLUsageLinkCreator;
    }

    public List<RoomClassRatioConfigDto> roomClassRatioConfigDtos;

    SortedSet<AccomTypeADR> accomTypeADRs;
    protected SortedSet<AccomClass> accomClasses = new TreeSet<>();
    private List<CostofWalk> costOfWalk;
    private List<OverbookingAccomTypeDto> overbookingAccomTypeDtos;
    private List<OverbookingAccomTypeSeasonDto> overbookingAccomTypeSeasonDtos;
    private List<AccomClassPriceRank> accomClassPriceRankings = new ArrayList<>();
    private List<AccomClassPriceRankNetworkArrow> advancedNetworkArrows;
    private RoomConfigurationSelection state = RoomConfigurationSelection.ROOM_TYPE_MAPPING;
    private List<MinimumPriceDifferentialUIWrapper> diffUiWrappers = new ArrayList<>();
    private List<DailyMinPriceDiff> minPriceDiffOriginalValues;
    private List<MinimumPriceDifferentialUIWrapper> seasonDiffUiWrappers = new ArrayList<>();
    private List<AccomClassMinPriceDiffSeason> seasonMinPriceDiffOriginalValues;
    private boolean isInitialConfiguration = false;
    private Map<RoomConfigurationSelection, Boolean> hasChangesMap = new HashMap<>();
    private Boolean groupPricingEnabled;
    private boolean roomClassMoved = false;
    private boolean pricingManagementInvalidedByMinPriceDiffIncrease;
    private boolean hasRoomClassConfigChanged;
    private boolean hasRoomClassConfigChangedForSelectiveAutomation;
    private Map<Integer, Set<Integer>> impactedRoomClassConfigMap = new HashMap<>();
    private static final int UNASSIGNED_ROOM_TYPE_ID = 1;
    private List<AccomClass> accomClassesMarkedForDeletion = new ArrayList<>();
    private static final String PDF_FILE_NAME = "RoomsConfiguration";
    private boolean isCPProperty;
    private boolean isComponentRoomsEnabled;
    private boolean isAdvancedPriceRankingEnabled;
    private boolean isMasterClassPricingEnabled;
    private boolean isPerPersonPricingEnabled;
    private boolean isGroupFinalForecastOverrideEnabled;
    private boolean isEnableLrvDropRestrictionEnabled;
    private boolean isSumOfPartsEnabled;
    private boolean isLimitedDataBuildEnabled;
    private boolean isAgileRatesEnabled;
    private boolean isRoomTypeRecodingUIEnabled;
    private boolean isGFFOverrideAtFGLevelEnabled;
    private boolean isHiltonUnassignedRoomTypeNoOverrideRemovalEnabled;
    private boolean isHideAdvancedSettingsAndRTOverbookingTypeEnabled;
    private boolean isReConfigureSelectiveAutomationEnabled;
    private boolean isAddNewRoomTypeToProductEnabled;
    private boolean isSpecialUseRoomTypesEnabled;
    private boolean isLimitedEditionProperty;
    private List<GroupFinalForecastConfig> originalGffConfigs = new ArrayList<>();
    private Tax defaultTax;
    private Map<Integer, Product> productMap;
    private Map<Integer, Set<String>> productAccomClassMap;
    private List<AccomClass> initialAccomClasses = new ArrayList<>();
    public static final Integer MIN_THRESHOLD = 0;
    public static final Integer MAX_THRESHOLD = 100;
    private static final String ENABLED = "1";
    private static final String NOT_ENABLED = "0";


    @Autowired
	private GroupFinalForecastConfigService groupFinalForecastConfigService;

    @Autowired
    ConfigAutomationService configAutomationService;

    @Autowired
	private GroupPricingConfigurationService groupPricingConfigurationService;

    @Override
    public void onViewOpened(Void aVoid) {
        init();
    }

    @Override
    public void onWorkContextChange(WorkContextType workContextType) {
        //This causes the entire page to reload, so we dont have to reset params here
        view.resetWizard();
    }

    private void init() {
        if (isCPProperty) {
            defaultTax = taxService.findTax();
        }

        isInitialConfiguration = CollectionUtils.isEmpty(accommodationService.getAccomClassesByViewOrder().stream()
                .filter(accomClass -> !Objects.equals(accomClass.getSystemDefault(), 1))
                .collect(Collectors.toList()));
        hasChangesMap.clear();
        for (RoomConfigurationSelection roomConfigurationSelection : RoomConfigurationSelection.values()) {
            hasChangesMap.put(roomConfigurationSelection, false);
        }
    }

    public void setParameters() {
        hasRoomClassConfigChanged = false;
        isCPProperty = configParamsService.getBooleanParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED);
        isComponentRoomsEnabled = configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.COMPONENT_ROOMS_ENABLED.value());
        isAdvancedPriceRankingEnabled = configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.IS_ADVANCED_PRICE_RANKING_ENABLED.value());
        isMasterClassPricingEnabled = configParamsService.getBooleanParameterValue(IPConfigParamName.BAR_ENABLE_SINGLE_BAR_DECISION.value());
        isPerPersonPricingEnabled = configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.PER_PERSON_PRICING_ENABLED.value());
        isGroupFinalForecastOverrideEnabled = configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.GROUP_FINAL_FORECAST_OVERRIDE_ENABLED.value());
        isEnableLrvDropRestrictionEnabled = configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.ENABLE_LRV_DROP_RESTRICTION_FEATURE);
        isSumOfPartsEnabled = configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.COMPONENT_ROOMS_PRICE_AS_SUM_OF_PARTS);
        isLimitedDataBuildEnabled = configParamsService.getBooleanParameterValue(IPConfigParamName.CORE_LIMITED_DATA_BUILD_ENABLED.value());
        isAgileRatesEnabled = configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.AGILE_RATES_ENABLED);
        isRoomTypeRecodingUIEnabled = configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.ROOM_TYPE_RECODING_UI_ENABLED.value());
        isGFFOverrideAtFGLevelEnabled = configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.GROUP_FINAL_FORECAST_OVERRIDE_ENABLED);
        groupPricingEnabled = configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.GROUP_PRICING_ENABLED.value())
                || configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.FUNCTION_SPACE_ENABLED.value());
        isHiltonUnassignedRoomTypeNoOverrideRemovalEnabled = configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.HILTON_UNASSIGNED_ROOM_TYPE_NO_OVERRIDE_REMOVAL_ENABLED);
        isHideAdvancedSettingsAndRTOverbookingTypeEnabled = configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.HIDE_ADVANCED_SETTINGS_AND_RTO_OVERBOOKING_TYPE);
        isReConfigureSelectiveAutomationEnabled = configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.RE_CONFIGURE_SELECTIVE_AUTOMATION_ENABLED);
        isAddNewRoomTypeToProductEnabled = configParamsService.getBooleanParameterValue(PreProductionConfigParamName.ADD_NEW_ROOM_TYPE_TO_PRODUCT_ENABLED);
        isSpecialUseRoomTypesEnabled = configParamsService.getBooleanParameterValue(PreProductionConfigParamName.SPECIAL_USE_ROOM_TYPES_ENABLED);
        isLimitedEditionProperty = SubscriptionEnum.LIMITED_EDITION.getSubscriptionType().equals(
                configParamsService.getParameterValue(FeatureTogglesConfigParamName.G3_RMS_CORE_SUBSCRIPTION_TYPE));
    }

    public Map<Integer, Set<Integer>> getImpactedRoomClassConfigMap() {
        return impactedRoomClassConfigMap;
    }

    public void getDataForRoomTypeMappings() {
        accomTypeADRs = getRoomTypesWithADR();

        accomClasses = new TreeSet<>(new RoomTypeMappingComparator().reversed());
        List<AccomClass> accomClassesByViewOrder = accommodationService.getAccomClassesByViewOrder();
        accomClasses.addAll(accomClassesByViewOrder);
        populateInitialAccomClasses();

        accomClassesMarkedForDeletion = new ArrayList<>();

        productMap = getAllProductsExcludingBarMap();
        productAccomClassMap = createProductAccomClassMap();

        resetViewOrder();
        view.captureMappingViewOriginalData();
    }

    public BigDecimal getRoomClassAvgADR(AccomClass accomClass, Predicate<AccomTypeADR> adrZeroCapacityRtsPredicate) {
        List<BigDecimal> roomTypesRoomsSold = getAccomTypeADRs().stream()
                .filter(adrZeroCapacityRtsPredicate)
                .filter(adr -> adr.getRoomsSold() != null)
                .filter(adr -> accomClass.getAccomTypes() != null)
                .filter(adr -> accomClass.getAccomTypes().contains(adr.getAccomType()))
                .map(AccomTypeADR::getRoomsSold)
                .collect(Collectors.toList());

        BigDecimal totalRoomsSold = roomTypesRoomsSold.isEmpty() ? BigDecimal.ZERO : roomTypesRoomsSold.stream().reduce(BigDecimal.ZERO, BigDecimal::add);
        if (totalRoomsSold.compareTo(BigDecimal.ZERO) > 0) {

            List<BigDecimal> roomTypesRoomRevenue = getAccomTypeADRs().stream()
                    .filter(adrZeroCapacityRtsPredicate)
                    .filter(adr -> adr.getRoomRevenue() != null)
                    .filter(adr -> accomClass.getAccomTypes() != null)
                    .filter(adr -> accomClass.getAccomTypes().contains(adr.getAccomType()))
                    .map(AccomTypeADR::getRoomRevenue)
                    .collect(Collectors.toList());

            BigDecimal totalRoomRevenue = roomTypesRoomRevenue.isEmpty() ? BigDecimal.ZERO : roomTypesRoomRevenue.stream().reduce(BigDecimal.ZERO, BigDecimal::add);
            return totalRoomRevenue.divide(totalRoomsSold, 2, BigDecimal.ROUND_HALF_UP);
        }

        return BigDecimal.ZERO;
    }

    public BigDecimal getRoomClassMinADR(AccomClass accomClass, Predicate<AccomTypeADR> adrZeroCapacityRtsPredicate) {
        List<BigDecimal> roomClassADRs = getAccomTypeADRs().stream()
                .filter(adrZeroCapacityRtsPredicate)
                .filter(adr -> adr.getBookingADR() != null)
                .filter(adr -> accomClass.getAccomTypes() != null)
                .filter(adr -> accomClass.getAccomTypes().contains(adr.getAccomType()))
                .map(AccomTypeADR::getBookingADR)
                .collect(Collectors.toList());

        return roomClassADRs.isEmpty() ? BigDecimal.ZERO :
                roomClassADRs.stream()
                        .min(BigDecimal::compareTo)
                        .orElse(BigDecimal.ZERO)
                        .setScale(2, BigDecimal.ROUND_HALF_UP);
    }

    public BigDecimal getRoomClassMaxADR(AccomClass accomClass, Predicate<AccomTypeADR> adrZeroCapacityRtsPredicate) {
        List<BigDecimal> roomClassADRs = getAccomTypeADRs().stream()
                .filter(adrZeroCapacityRtsPredicate)
                .filter(adr -> adr.getBookingADR() != null)
                .filter(adr -> accomClass.getAccomTypes() != null)
                .filter(adr -> accomClass.getAccomTypes().contains(adr.getAccomType()))
                .map(AccomTypeADR::getBookingADR)
                .collect(Collectors.toList());

        return roomClassADRs.isEmpty() ? BigDecimal.ZERO :
                roomClassADRs.stream()
                        .max(BigDecimal::compareTo)
                        .orElse(BigDecimal.ZERO)
                        .setScale(2, BigDecimal.ROUND_HALF_UP);
    }

    //This currently exists to sanitize some data that doesn't start from one
    public void resetViewOrder() {
        //To appease the lambda final requirement
        final int[] i = {accomClasses.size() - 1};

        accomClasses.stream()
                .filter(accomClass -> accomClass.getSystemDefault() != 1)
                .forEach(accomClass -> {
                    accomClass.setViewOrder(i[0]);
                    i[0]--;
                });
    }

    public void hydrateRoomTypeMappingsView() {
        view.refreshRoomTypeMappingView(accomTypeADRs, accomClasses);
    }

    public void hydrateCostOfWalkView() {
        view.refreshCostOfWalkView(costOfWalk);
    }

    public void hydrateRoomClassRatioConfig() {
        view.refreshRoomClassRatioConfig(roomClassRatioConfigDtos);
    }

    private SortedSet<AccomTypeADR> getRoomTypesWithADR() {
        return new TreeSet<>(accommodationService.getAllActiveAccomTypeADRs());
    }

    public AccomClass getUnassignedAccomClass() {
        return accomClasses.stream()
                .filter(accomClass -> Integer.valueOf(1).equals(accomClass.getSystemDefault()))
                .findAny()
                .get();
    }

    public List<AccomClass> getNonSystemDefaultAccomClasses() {
        return accomClasses.stream()
                .filter(accomClass -> !Integer.valueOf(1).equals(accomClass.getSystemDefault()))
                .collect(Collectors.toList());
    }

    public List<AccomClass> getUnplacedAccomClasses(List<AccomClassPriceRank> accomClassPriceRanks) {
        Set<AccomClass> rankedAccomClasses = new HashSet<>();
        rankedAccomClasses.addAll(accomClassPriceRanks.stream().map(AccomClassPriceRank::getLowerRankAccomClass).collect(Collectors.toSet()));
        rankedAccomClasses.addAll(accomClassPriceRanks.stream().map(AccomClassPriceRank::getHigherRankAccomClass).collect(Collectors.toSet()));
        List<AccomClass> unplacedRoomClasses = new ArrayList<>(getNonSystemDefaultAccomClasses().stream().filter(accomClass -> !rankedAccomClasses.contains(accomClass)).collect(Collectors.toList()));
        if (CollectionUtils.isNotEmpty(unplacedRoomClasses)) {
            unplacedRoomClasses.sort((o1, o2) -> Integer.compare(o1.getIsPriceRankConfigured().getId(), o2.getIsPriceRankConfigured().getId()));
        }
        return unplacedRoomClasses;
    }

    public int getGridSize() {
        // If a network path is already configured, there could be one or multiple deleted AccomClass records are
        // replaced by a pass-thrus and those pass-thrus can only be determined by the arrows - thus, use the Y-cord
        // of the saved cards to determine the size of the grid and that pass-thru
        int maxYCord = 0;
        List<AccomClassPriceRankNetworkArrow> networkArrows = accomClassPriceRankService.getNetworkPathArrowsWithLowestFirst();
        if (CollectionUtils.isNotEmpty(networkArrows) && hasNetworkArrowPointedUp(networkArrows)) {
            maxYCord = networkArrows.stream().filter(networkArrow -> networkArrow.getDirection().equals(AccomClassPriceRankNetworkArrow.Direction.UP)).mapToInt(networkArrow -> networkArrow.getYCoord()).max().getAsInt() + 1;
        }

        int maxHeight = maxYCord + 1;

        // Get the number of non-default AccomClasses
        int accomClassCount = getNonSystemDefaultAccomClasses().size();

        // Multiply the number of accom classes by 2 since each card needs an arrow over above the card
        // once you get that total, subtract '1' since the top card doesn't need an arrow over the top
        accomClassCount = (accomClassCount * 2) - 1;

        return accomClassCount > maxHeight ? accomClassCount : maxHeight;
    }

    private boolean hasNetworkArrowPointedUp(List<AccomClassPriceRankNetworkArrow> networkArrows) {
        return networkArrows.stream().filter(networkArrow -> networkArrow.getDirection().equals(AccomClassPriceRankNetworkArrow.Direction.UP)).count() > 0;
    }

    public LinkedList<AccomClass> getAccomClassesByADR() {
        return accomClassPriceRankService.getRoomClassesByADR();
    }

    public SortedSet<AccomTypeADR> getAccomTypeADRs() {
        return accomTypeADRs;
    }

    public SortedSet<AccomClass> getAccomClasses() {
        return accomClasses;
    }

    public List<CostofWalk> getCostOfWalk() {
        return costOfWalk;
    }

    public void updateAccomClassesMarkedForDeletion(AccomClass accomClass) {
        accomClassesMarkedForDeletion.add(accomClass);
        accomClasses.remove(accomClass);
        if (accomTypeADRs != null) {
            accomTypeADRs.stream()
                    .filter(adr -> adr.getAccomType().getAccomClass().getId().equals(accomClass.getId()))
                    .forEach(adr -> adr.getAccomType().setAccomClass(getUnassignedAccomClass()));
        }
    }

    public boolean saveRoomClasses(boolean requiresValidation) {
        Optional<String> warningMessage = requiresValidation ? validateRoomTypeMappingSave() : Optional.empty();

        if (warningMessage.isPresent()) {
            showWarning(warningMessage.get());
            return false;
        } else {
            List<AccomClass> allExistingAccomClasses = accommodationService.getAllActiveAccomClasses();
            List<AccomType> allExistingAccomTypes = accommodationService.getAllAccomTypes();
            if (isCPProperty() && clientService.isHiltonByExternalSystem()) {
                qualifiedRateService.setLV0OffsetToZeroForNewRoomType();
            }
            deleteRoomClasses();
            accommodationService.updateAccomClasses(new ArrayList<>(accomClasses), allExistingAccomClasses, allExistingAccomTypes);

            if (isAgileRatesEnabled() && isHasRoomClassConfigChanged()) {
                if (deleteLinkedProductOverridesOnRoomClassConfigChange()) {
                    agileRatesConfigurationService.changedRoomClassConfig(getSystemDateAsLocalDate(),
                            new HashSet<>(allExistingAccomClasses), accomClasses);
                } else {
                    agileRatesConfigurationService.changedRoomClassConfig(getSystemDateAsLocalDate());
                }
            }
            Runner.runIfTrue(isEnableLrvDropRestrictionEnabled(), this::syncLrvDropRestrictionsWithAccomClassChange);
            sync();
            showSaveSuccessMessage();
            resetMasterClassChange();
            agileRatesConfigurationService.validateIndependentProducts();
            return true;
        }
    }

    public void resetImpactedRoomClassConfigMap() {
        impactedRoomClassConfigMap.clear();
    }

    void resetMasterClassChange() {
        if (isGroupFinalForecastOverrideEnabled()) {
            hasChangesMap.put(RoomConfigurationSelection.ROOM_TYPE_MAPPING, false);
            hasChangesMap.put(RoomConfigurationSelection.MASTER_CLASS_MAPPING, false);
        }
    }

    private void deleteRoomClasses() {
        if (roomClassesAreMarkedForDeletion()) {
            invalidateOverridesService.invalidateOverrides(accomClassesMarkedForDeletion);
            accommodationMappingService.deleteWebrateOverrideCompetitors(accomClassesMarkedForDeletion);
            accomClassesMarkedForDeletion.forEach(rc -> {
                deleteRoomClass(rc);
                accomClassPriceRankService.deleteAndCleanupForAccomClass(rc);
                groupFinalForecastConfigService.deleteAndCleanupForAccomClass(rc.getId());
            });
            accomClassesMarkedForDeletion.clear();
            updateRoomClassRanking();
            populateInitialAccomClasses();
        }
    }

    private boolean roomClassesAreMarkedForDeletion() {
        return !accomClassesMarkedForDeletion.isEmpty();
    }

    private void updateRoomClassRanking() {
        List<AccomClass> sortedRoomClasses = sortRoomClassesByRankOrder();
        sortedRoomClasses.forEach(rc -> rc.setRankOrder(sortedRoomClasses.indexOf(rc) + 1));
    }

    private List<AccomClass> sortRoomClassesByRankOrder() {
        return accomClasses.stream()
                .filter(rc -> rc.getRankOrder() != null)
                .sorted(Comparator.comparingInt(AccomClass::getRankOrder))
                .collect(Collectors.toList());
    }

    public void saveCostOfWalks() {
        costofWalkService.save(costOfWalk);
        sync();
    }

    public Optional<String> validateRoomTypeMappingSave() {
        String message = null;
        Optional<AccomClass> masterClass = accomClasses.stream()
                .filter(AccomClass::getMasterClassBoolean)
                .findAny();

        //Do not allow the user to proceed if previously assigned Accom Types are unassigned and not reassigned.
        //The user should be able to proceed with Accom Types that have never been assigned and have a capacity of zero.
        List<AccomTypeADR> unassignedRoomTypes = accomTypeADRs.stream().filter(adr -> Integer.valueOf(UNASSIGNED_ROOM_TYPE_ID).equals(adr.getAccomType().getAccomClass().getSystemDefault())).collect(Collectors.toList());
        if (!unassignedRoomTypes.isEmpty()) {
            List<AccomType> allUnassignedAccomTypes = accommodationService.getAllUnassignedAccomTypes();
            //Check to see if the pending unassigned Accom Types were previously assigned to a Accom Class or have a capacity > 0
            if (unassignedRoomTypes.stream().anyMatch(unassignedRoomType -> !allUnassignedAccomTypes.contains(unassignedRoomType.getAccomType()) || unassignedRoomType.getAccomType().getAccomTypeCapacity() > 0)) {
                message = getText("mapAllRoomType");
                return Optional.ofNullable(message);
            }
        }

        if (accomClasses.stream().filter(ac -> !Integer.valueOf(1).equals(ac.getSystemDefault())).anyMatch(ac -> CollectionUtils.isEmpty(ac.getAccomTypes()))) {
            message = getText("emptyRoomClass");
        } else if (!masterClass.isPresent()) {
            message = getText("configureRoomClassAdMasterClassAlertMessage");
        } else {
            List<String> hospitalityRooms = hospitalityRoomsService.getAllTypesOfHospitalityRooms();
            //Do not allow user to proceed if any Accom Class has zero capacity
            List<AccomClass> accomClassList = accomClasses.stream().filter(ac -> !Integer.valueOf(1).equals(ac.getSystemDefault())).collect(Collectors.toList());
            for (AccomClass accomClass : accomClassList) {
                Integer capacity = 0;
                boolean containsHospitalityRoom = false;
                for (AccomType accomType : accomClass.getAccomTypes()) {
                    capacity = capacity + accomType.getAccomTypeCapacity();
                    if (hospitalityRooms.contains(accomType.getAccomTypeCode())) {
                        containsHospitalityRoom = true;
                        break;
                    }
                }
                if (capacity.equals(0) && !containsHospitalityRoom) {
                    message = getText("roomClassWithZeroCapacity");
                    break;
                }
            }
        }
        if ((message == null || "".equals(message)) && isComponentRoomsEnabled()) {
            message = validateComponentRoomsConfiguration();
        }

        return Optional.ofNullable(message);
    }

    private void addToMsgList(List<String> msgList, String parent, List<String> children) {
        if (children.size() > 1) {
            msgList.add(getText("hospitality.rooms.should.be.part.of.same.rc.as.children", parent, children.toString()));
        } else {
            msgList.add(getText("hospitality.rooms.should.be.part.of.same.rc.as.child", parent, children.toString()));
        }
    }

    private Map<String, List<String>> getParentChildMapFromConfig(List<HospitalityRoomsConfig> hospitalityRoomsConfigs) {
        Map<String, List<String>> map = new HashMap<>();
        hospitalityRoomsConfigs.stream().forEach(config -> {
            map.putIfAbsent(config.getParentRoomTypeCode(), new ArrayList<>());
            map.get(config.getParentRoomTypeCode()).add(config.getChildRoomTypeCode());
        });
        return map;
    }

    public String validateComponentRoomsConfiguration() {
        List<AccomClass> accomClassesWithSumOfParts = new ArrayList<>();
        List<String> hospitalityRooms = new ArrayList<>();
        if (isSumOfPartsEnabled()) {
            accomClassesWithSumOfParts = pricingConfigurationService.getPricingAccomClasses().stream()
                    .filter(PricingAccomClass::isPriceAsSumOfParts)
                    .map(PricingAccomClass::getAccomClass)
                    .collect(Collectors.toList());
            hospitalityRooms = hospitalityRoomsService.getHospitalityRoomsConfigWithoutPseudoRooms().stream()
                    .map(HospitalityRoomsConfig::getParentRoomTypeCode).collect(Collectors.toList());
        }
        Map<String, Set<String>> invalidUpgradeConfiguration;
        invalidUpgradeConfiguration = getInvalidUpgradeConfiguration();
        Set<String> msgSet = new HashSet<>();
        Map<String, String> invalidAtToAcMap = new HashMap<>();
        for (AccomClass accomClass : accomClasses) {
            Set<AccomType> currentAccomTypes = accomClass.getAccomTypes();
            for (AccomType currAccomType : currentAccomTypes) {
                if (isSumOfPartsEnabled() && accomClassesWithSumOfParts.contains(accomClass)
                        && !currAccomType.isComponentRoom()
                        && !hospitalityRooms.contains(currAccomType.getAccomTypeCode())) {
                    invalidAtToAcMap.put(currAccomType.getAccomTypeCode(), accomClass.getCode());
                }
                Set<AccomType> otherAccomTypes = new HashSet<>();
                otherAccomTypes.addAll(currentAccomTypes);
                otherAccomTypes.remove(currAccomType);
                String currAccomTypeStr = currAccomType.getAccomTypeCode();
                if (invalidUpgradeConfiguration.get(currAccomTypeStr) != null) {
                    for (AccomType destAccomType : otherAccomTypes) {
                        String destAccomTypeStr = destAccomType.getAccomTypeCode();
                        for (String invalidAccomTypeStr : invalidUpgradeConfiguration.get(currAccomTypeStr)) {
                            if (invalidAccomTypeStr.equalsIgnoreCase(destAccomTypeStr)) {
                                List<String> list = new ArrayList<>(Arrays.asList(currAccomTypeStr, destAccomTypeStr));
                                Collections.sort(list);
                                msgSet.add(getText("crInvalidRoomClassCreated", list.get(0), list.get(1)));
                            }
                        }
                    }
                }
            }
        }

        if (!invalidAtToAcMap.isEmpty()) {
            String hspRoomsString = hospitalityRooms.isEmpty() ? "" : "or Hospitality Rooms";
            StringBuilder sb = new StringBuilder();
            for (Map.Entry<String, String> entry : invalidAtToAcMap.entrySet()) {
                sb.append(getText("can.not.move.rt.to.rc.as.it.is.sumOfParts", entry.getKey(), entry.getValue(), hspRoomsString));
                return sb.toString();
            }
        }

        String msg = "";
        for (String str : msgSet) {
            msg = msg + "\n" + str;
        }
        if ("".equals(msg)) {
            return null;
        } else {
            return msg;
        }
    }

    public void addRoomClass(AccomClass accomClass) {
        accommodationService.persistAccomClassWithViewOrder(accomClass);
        //create default inventory group
        if (isAutoCreateInventoryGroupsEnabled()) {
            inventoryGroupService.saveInventoryGroup(accomClass, inventoryGroupService.getInventoryGroups().size() + 1);
        }
    }

    private boolean isAutoCreateInventoryGroupsEnabled() {
        return configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.INVENTORY_GROUP_AUTO_CREATION_ENABLED);
    }

    public void syncLrvDropRestrictionsWithAccomClassChange() {
        lrvDropRestrictionService.syncLrvDropRestrictionsWithAccomClassChange();
    }

    void deleteRoomClass(AccomClass accomClass) {
        List<AccomType> accomTypes = new ArrayList(accommodationService.getAccomClassById(accomClass.getId()).getAccomTypes());
        if (!CollectionUtils.isEmpty(accomTypes)) {
            //we need to know whether or not a remapped accom type's accom class was deleted when we remove pricing configurations for the accom type
            accomTypes.forEach(accomType -> accomType.getAccomClass().setStatusId(Constants.INACTIVE_STATUS_ID));
            accommodationService.deletePricingConfigurations(accomTypes);
        }

        Optional<AccomClass> deletedAccomClass = initialAccomClasses.stream().filter(initialRoomClass -> initialRoomClass.getId().equals(accomClass.getId())).findFirst();

        if(deletedAccomClass.isPresent() && deletedAccomClass.get().getAccomTypes() != null) {
            Set<AccomType> accomTypesToDelete = deletedAccomClass.get().getAccomTypes()
                    .stream().filter(accomType -> !accomTypes.contains(accomType)).collect(Collectors.toSet());

            if(!accomTypesToDelete.isEmpty()) {
                pricingConfigurationService.deletePricingAccomClass(deletedAccomClass.get());
                pricingConfigurationService.deleteTransientPricingConfigurationsByAccomTypes(accomTypesToDelete);
                pricingConfigurationService.deleteGroupPricingConfigurationsByAccomTypes(accomTypesToDelete);
                pricingConfigurationService.deletePricingOffsetAccomTypesByAccomTypes(accomTypesToDelete);
                pricingConfigurationService.deletePricingConfigurationSupplementsForAccomTypes(accomTypesToDelete);
            }
        }


        accommodationService.deleteAccomClass(accomClass.getId());
        accomClasses.remove(accomClass);
        resetViewOrder();

        if (accomTypeADRs != null) {
            accomTypeADRs.stream()
                    .filter(adr -> adr.getAccomType().getAccomClass().getId().equals(accomClass.getId()))
                    .forEach(adr -> adr.getAccomType().setAccomClass(getUnassignedAccomClass()));
        }

    }

    public void getCostOfWalkData() {
        costOfWalk = costofWalkService.getCostOfWalks();
    }

    public void nextState() {
        state = state.next(isGroupPricingEnabled());
    }

    public void previousState() {
        state = state.previous(isGroupPricingEnabled());
    }

    public RoomConfigurationSelection getState() {
        return state;
    }

    public void saveRunOfHouse(List<AccomType> rohAccomTypes) {
        boolean hasChangeInROHType = false;
        for (AccomClass accomClass : accomClasses) {

            Set<AccomType> accomTypes = accomClass.getAccomTypes();
            if (CollectionUtils.isNotEmpty(accomTypes)) {
                for (AccomType accomType : accomTypes) {

                    if (rohAccomTypes.contains(accomType)) {

                        // Need to know if there was a change in ROH
                        if (!accomType.getRohType().equals(Integer.valueOf(1))) {
                            hasChangeInROHType = true;
                        }

                        accomType.setRohType(1);
                    } else {
                        accomType.setRohType(0);
                    }
                }
            }
        }

        accommodationService.updateAccomClasses(new ArrayList<>(accomClasses));

        boolean roomClassWasAltered = accomClassPriceRankService.makeAllRoomClassesUpgradable();
        if (roomClassWasAltered) {
            updateHasChanges(RoomConfigurationSelection.PRICE_RANKING, true);
        }

        if (hasChangeInROHType || overbookingService.isOverbookingPreviouslyConfigured()) {
            // Need to clear config and instances since we are changing either the ROH Type or from Accom Type -> ROH
            exceptionConfigService.clearConfigAndInstancesAfterROHChanges(true, false);
        }

        deleteOverbookingConfiguration();
        updateROHConfigParameter(true);

        sync();
        showSaveSuccessMessage();
    }

    public void deleteOverbookingConfiguration() {
        //Remove any existing overbooking config
        overbookingService.deleteOverbookingConfiguration();
        overbookingService.deleteSpecialUseRoomTypeConfiguration();
        // Refresh data
        getOverbookingData();

        //After we delete, save the default configuration so the user isn't left with nothing.
        overbookingService.saveOverbookingAccomTypeDtos(getOverbookingAccomTypeDtos(), accomClasses);
    }

    public void deleteSpecialUseRoomTypeConfiguration() {
        if (!isSpecialUseRoomTypesEnabled) {
            return;
        }

        overbookingService.deleteSpecialUseRoomTypeConfiguration();
        accomClasses.stream().filter(accomClass -> accomClass.getAccomTypes() != null)
                .forEach(accomClass -> accomClass.getAccomTypes().forEach(accomType -> accomType.setExcludedFromSoldout(0)));

        // Refresh data
        getOverbookingData();
    }

    public void disableDistributeUnsoldCapacity() {
        overbookingService.disableDistributeUnsoldCapacity();
    }

    public boolean isUsingNewLoadFlow() {
        return SystemConfig.roomsConfigurationPriceRankingAndUpgradeIsUsingNewFlow();
    }

    public boolean isRoomsConfigUpgradeRelaxValidationEnabled() {
        return configService.getBooleanParameterValue(PreProductionConfigParamName.ROOMS_CONFIGURATION_COMPONENT_ROOM_UPGRADE_RELAX_VALIDATION_ENABLED);
    }

    private boolean deleteLinkedProductOverridesOnRoomClassConfigChange() {
        return configParamsService.getBooleanParameterValue(PreProductionConfigParamName.DELETE_LINKED_PRODUCT_OVERRIDES_ON_ROOMS_CONFIGURATION_CHANGE);
    }

    private void updateROHConfigParameter(boolean isROHEnabled) {
        configParamsService.updateParameterValue(IPConfigParamName.ROH_ROHENABLED.value(), isROHEnabled);
    }

    public boolean isROHEnabled() {
        return CollectionUtils.isNotEmpty(getROHAccomTypes());
    }

    public List<AccomType> getROHAccomTypes() {
        return overbookingAccomTypeDtos.stream()
                .map(OverbookingAccomTypeDto::getAccomType)
                .filter(accomType -> Integer.valueOf(1).equals(accomType.getRohType()))
                .sorted(AccomType::compareTo).collect(Collectors.toList());
    }

    public void getOverbookingData() {
        overbookingAccomTypeDtos = overbookingService.getOverbookingAccomTypeDtos();
        overbookingAccomTypeSeasonDtos = overbookingService.getOverbookingAccomTypeSeasonDtos();

        if (isComponentRoomsEnabled()) {
            setComponentTypeOverbookingToFalse(overbookingAccomTypeDtos);
            setComponentTypeOverbookingToFalse(overbookingAccomTypeSeasonDtos);
        }
    }

    private void setComponentTypeOverbookingToFalse(List<? extends OverbookingAccomTypeDto> overbookingAccomTypeDtos) {
        overbookingAccomTypeDtos.stream()
                .filter(dto -> dto.getAccomType().isComponentRoom())
                .forEach(dto -> dto.setOverbooking(false));
    }


    public boolean isOverbookingPreviouslyConfigured() {
        return overbookingService.isOverbookingPreviouslyConfigured();
    }

    public boolean isPriceRankingPreviouslyConfigured() {
        return accomClassPriceRankService.isPriceRankingPreviouslyConfigured();
    }

    public List<OverbookingAccomTypeDto> getOverbookingAccomTypeDtos() {
        return overbookingAccomTypeDtos;
    }

    public List<OverbookingAccomTypeSeasonDto> getOverbookingAccomTypeSeasonDtos() {
        return overbookingAccomTypeSeasonDtos;
    }

    public void saveRoomType(AccomType accomType) {
        accommodationService.saveAccomType(accomType);
    }

    public void saveOverbookingData(boolean distributeUnsoldCapacity) {
        overbookingService.saveOverbookingAccomTypeDtos(overbookingAccomTypeDtos, accomClasses);
        if (distributeUnsoldCapacity) {
            overbookingService.enableDistributeUnsoldCapacity();
        }

        List<AccomType> rohAccomTypes = getROHAccomTypes();
        if (CollectionUtils.isNotEmpty(rohAccomTypes)) {
            rohAccomTypes.forEach(rohAccomType -> {
                rohAccomType.setRohType(0);
                saveRoomType(rohAccomType);

                // Need to clear config and instances since we are changing from ROH -> AccomType
                exceptionConfigService.clearConfigAndInstancesAfterROHChanges(false, true);

                //Update AccomClasses in memory otherwise price ranking will overwrite ROH type
                getDataForRoomTypeMappings();
            });
        }

        updateROHConfigParameter(false);

        sync();

        showSaveSuccessMessage();
    }

    public void saveMaximumOccupantsData(List<MaximumOccupantsEntity> maximumOccupantsList) {
        perPersonPricingService.save(maximumOccupantsList);
        showSaveSuccessMessage();
    }

    public void getPriceRankData() {
        accomClassPriceRankings = new ArrayList<>(accomClassPriceRankService.getAccomClassPriceRankForAllAccomClasses());
        advancedNetworkArrows = accomClassPriceRankService.getNetworkPathArrows();
    }

    public Map<String, Set<String>> getInvalidUpgradeConfiguration() {
        Map<String, Set<String>> invalidUpgradeConfiguration = new HashMap<>();
        List<ComponentRoomsConfiguration> componentRoomsConfigurations = componentRoomService.getComponentRoomsConfigurations();

        if (CollectionUtils.isEmpty(componentRoomsConfigurations)) {
            return invalidUpgradeConfiguration;
        }
        for (ComponentRoomsConfiguration compRoomsConfiguration : componentRoomsConfigurations) {
            getInvalidUpgradeConfigurationDown(compRoomsConfiguration, invalidUpgradeConfiguration);
        }
        getInvalidUpgradeConfigurationUp(invalidUpgradeConfiguration);
        return invalidUpgradeConfiguration;
    }

    private void getInvalidUpgradeConfigurationDown(ComponentRoomsConfiguration compRoomsConfiguration, Map<String, Set<String>> invalidUpgradeConfiguration) {
        String key = compRoomsConfiguration.getAccomType().getAccomTypeCode();
        if (compRoomsConfiguration.isComponent()) {
            for (ComponentRoomsConfiguration compConfig : compRoomsConfiguration.getChildren()) {
                String child = compConfig.getAccomType().getAccomTypeCode();
                Set<String> valueSet = invalidUpgradeConfiguration.get(key) == null ? new HashSet<>() : invalidUpgradeConfiguration.get(key);
                valueSet.add(child);
                getInvalidUpgradeConfigurationDown(compConfig, invalidUpgradeConfiguration);
                Set<String> childValueSet = invalidUpgradeConfiguration.get(child);
                if (childValueSet != null) {
                    valueSet.addAll(childValueSet);
                }
                invalidUpgradeConfiguration.put(key, valueSet);
            }
        }
    }

    private void getInvalidUpgradeConfigurationUp(Map<String, Set<String>> invalidUpgradeConfiguration) {
        Set<String> keySet = invalidUpgradeConfiguration.keySet();
        List<String> keyList = new ArrayList<>();
        keyList.addAll(keySet);
        for (String key : keyList) {
            Set<String> valueSet = invalidUpgradeConfiguration.get(key);
            for (String newKey : valueSet) {
                Set<String> newValueSet = invalidUpgradeConfiguration.get(newKey) == null ? new HashSet<>() : invalidUpgradeConfiguration.get(newKey);
                newValueSet.add(key);
                invalidUpgradeConfiguration.put(newKey, newValueSet);
            }
        }
    }

    public List<AccomClassPriceRankNetworkArrow> getAdvancedNetworkArrows() {
        return advancedNetworkArrows;
    }

    public boolean isPriceRankingAdvanced() {
        return CollectionUtils.isNotEmpty(advancedNetworkArrows);
    }

    public List<AccomClassPriceRank> getPriceRankings() {
        return accomClassPriceRankings;
    }

    //Determine if price rank of room classes needs to rewrite Upgrade Path screen and data
    public void createNewPriceRankingData(List<AccomClass> rankedAccomClasses) {
        LinkedList<AccomClassPriceRank> tempList = new LinkedList<>();
        boolean hasPriceRankingChanged = false;

        for (int i = 0; i < rankedAccomClasses.size() - 1; i++) {
            AccomClass higherRankAccomClass = rankedAccomClasses.get(i);
            AccomClass lowerRankAccomClass = rankedAccomClasses.get(i + 1);

            tempList.add(new AccomClassPriceRank(lowerRankAccomClass, higherRankAccomClass, true));

            if (!accomClassPriceRankings.isEmpty()) {
                AccomClassPriceRank accomClassPriceRank = accomClassPriceRankings.get(i);
                hasPriceRankingChanged = hasPriceRankingChanged || !accomClassPriceRank.getLowerRankAccomClass().equals(lowerRankAccomClass) || !accomClassPriceRank.getHigherRankAccomClass().equals(higherRankAccomClass);
            } else if (rankedAccomClasses.size() > 1) {
                //Accom Class Price Rankings needs to be created if there is more than one room class
                hasPriceRankingChanged = true;
            }
        }

        if (hasPriceRankingChanged) {
            //Delete current ranking to be replaced by new ranking, but first make sure to preserve any existing upgrade paths that weren't affected by the price ranking change
            tempList.forEach(tempListAccomClassPriceRank -> {
                List<AccomClassPriceRank> matchingAccomClassPriceRank = accomClassPriceRankings.stream().filter
                        (existingAccomClassPriceRank -> accomClassPriceRankEquals(tempListAccomClassPriceRank, existingAccomClassPriceRank, false)).collect(Collectors.toList());
                //we have a match and must preserve upgradeability
                if (!matchingAccomClassPriceRank.isEmpty()) {
                    tempListAccomClassPriceRank.setUpgradeAllowed(matchingAccomClassPriceRank.get(0).isUpgradeAllowed());
                }
            });
            accomClassPriceRankService.deleteAccomClassPriceRanks(accomClassPriceRankings);
            accomClassPriceRankings = tempList;

            savePriceRankingData(false, accomClassPriceRankings, true);
            updateHasChanges(RoomConfigurationSelection.PRICE_RANKING, true);
            updateHasChanges(RoomConfigurationSelection.MIN_PRICE_DIFFERENTIAL, true);
            // Save Group Ranking to match linear Price Ranking
            saveGroupPriceRankingData(rankedAccomClasses, !isCPProperty() && !isGroupPricingEnabled());
            sync();
        }
    }

    public void deleteAdvancedPriceRankingDataAndUpdateHasChanges() {
        List<AccomClassPriceRank> existingPriceRankPaths = accomClassPriceRankService.getAccomClassPriceRank();
        accomClassPriceRankService.deleteAccomClassPriceRanks();
        List<AccomClassPriceRank> defaultPriceRanks = accomClassPriceRankService.getAccomClassPriceRankForAllAccomClasses();
        final boolean[] defaultPriceRanksSameAsExistingPriceRanks = {true};
        if (defaultPriceRanks.size() == existingPriceRankPaths.size()) {
            defaultPriceRanks.forEach(defaultPriceRank -> {
                boolean anyMatch = existingPriceRankPaths.stream().anyMatch(existingPriceRankPath -> accomClassPriceRankEquals(defaultPriceRank, existingPriceRankPath, true));
                if (!anyMatch) {
                    defaultPriceRanksSameAsExistingPriceRanks[0] = false;
                }
            });
        } else {
            defaultPriceRanksSameAsExistingPriceRanks[0] = false;
        }

        //if rows from Accom_Class_PriceRank_Path were actually deleted and those rows were different from the default rows, we mark that as a change
        boolean hasChanges = !existingPriceRankPaths.isEmpty() && !defaultPriceRanksSameAsExistingPriceRanks[0];

        updateHasChanges(RoomConfigurationSelection.PRICE_RANKING, hasChanges);
    }

    /*
    An equality check that only looks at lowerRankAccomClass, higherRankAccomClass, and isUpgradeAllowed (if specified) for 2 AccomClassPriceRank objects.
    */
    private boolean accomClassPriceRankEquals(AccomClassPriceRank accomClassPriceRank1, AccomClassPriceRank accomClassPriceRank2, boolean checkUpgradeAllowed) {
        boolean lowerRankAccomClassesEqual = accomClassPriceRank1.getLowerRankAccomClass().getId().equals(accomClassPriceRank2.getLowerRankAccomClass().getId());
        boolean higherRankAccomClassesEqual = accomClassPriceRank1.getHigherRankAccomClass().getId().equals(accomClassPriceRank2.getHigherRankAccomClass().getId());
        boolean upgradeabilityEqual = accomClassPriceRank1.isUpgradeAllowed() == accomClassPriceRank2.isUpgradeAllowed();
        return checkUpgradeAllowed ? isUpgradeAllowed(lowerRankAccomClassesEqual, higherRankAccomClassesEqual, upgradeabilityEqual) :
                lowerRankAccomClassesEqual && higherRankAccomClassesEqual;
    }

    private boolean isUpgradeAllowed(boolean lowerRankAccomClassesEqual, boolean higherRankAccomClassesEqual, boolean upgradeabilityEqual) {
        return lowerRankAccomClassesEqual && higherRankAccomClassesEqual && upgradeabilityEqual;
    }

    public void saveSimplePriceRankingData(boolean isUpgadePathCompleted, List<AccomClassPriceRank> priceRanks, boolean hasPricingRankAndUpgradePathChanged) {
        // Since we are in the simple path, we always want to make sure the 'rankOrder'
        // matches the priceRank paths so that group's linear ranking matches the transient
        // upgrade path
        if (CollectionUtils.isNotEmpty(priceRanks)) {

            // The determine the highest rank number
            int rank = priceRanks.size() + 1;

            // Find the highest ranking accomClass and update the number
            accomClasses.stream().filter(accomClass -> accomClass.equals(priceRanks.get(0).getHigherRankAccomClass())).findFirst().get().setRankOrder(rank--);

            // Since we always have a linear path, we can always update the lower ranked accom class as the high one will already be set
            for (AccomClassPriceRank accomClassPriceRank : priceRanks) {
                accomClasses.stream().filter(accomClass -> accomClass.equals(accomClassPriceRank.getLowerRankAccomClass())).findFirst().get().setRankOrder(rank--);
            }
        }

        // Update the AccomClasses
        updatePriceRankConfiguredState();

        // Save the paths
        savePriceRankingData(isUpgadePathCompleted, priceRanks, hasPricingRankAndUpgradePathChanged);
    }

    public void savePriceRankingData(boolean isUpgadePathCompleted, List<AccomClassPriceRank> priceRanks, boolean isPricingRankChanged) {
        if (!accomClassPriceRankings.equals(priceRanks)) {
            //Delete current ranking to be replaced by new ranking
            accomClassPriceRankService.deleteAccomClassPriceRanks(accomClassPriceRankings);
        }

        accomClassPriceRankService.updateAccomClassPriceRanks(priceRanks);

        if (isUpgadePathCompleted) {
            updatePriceRankConfiguredState(priceRanks);
        }

        accomClassPriceRankings = new LinkedList<>(priceRanks);
        if (isPricingRankChanged) {
            pricingConfigurationLTBDEService.enableLTBDEOnIndirectConfigChangedIfApplicable();
        }
        sync();
        showSaveSuccessMessage();
    }

    private void updatePriceRankConfiguredState() {
        List<AccomClass> incompleteRoomClasses = accomClasses.stream().filter(accomClass -> accomClass.getIsPriceRankConfigured().equals(AccomClassPriceRankStatus.INCOMPLETE)).collect(Collectors.toList());
        incompleteRoomClasses.forEach(accomClass -> accomClass.setIsPriceRankConfigured(AccomClassPriceRankStatus.COMPLETE_RANKED));
        saveRoomClasses(false);
    }

    protected void updatePriceRankConfiguredState(List<AccomClassPriceRank> priceRanks) {
        List<AccomClass> unplacedAccomClasses = getUnplacedAccomClasses(priceRanks);
        for (AccomClass accomClass : accomClasses) {
            if (unplacedAccomClasses.contains(accomClass)) {
                accomClass.setIsPriceRankConfigured(AccomClassPriceRankStatus.COMPLETE_NOT_RANKED);
            } else {
                accomClass.setIsPriceRankConfigured(AccomClassPriceRankStatus.COMPLETE_RANKED);
            }
        }
        saveRoomClasses(false);
    }

    public <S extends Season> boolean willSplitOccur(List<S> seasons, S changedSeason) {
        return seasonService.willSplitOccur(seasons, changedSeason);
    }

    public <S extends Season> List<S> applySplit(List<S> seasons, S changedSeason) {
        return seasonService.applySplit(seasons, changedSeason, getSystemDateAsLocalDate());
    }

    public void saveOverbookingSeasons(List<OverbookingSeasonUIWrapper> seasons) {
        overbookingService.saveOverbookingAccomTypeSeasonDtos(seasons.stream()
                .map(OverbookingSeasonUIWrapper::getDto)
                .collect(Collectors.toList()));
    }

    public boolean isNewUnconfiguredRoomClassPresent() {
        return isPriceRankingPreviouslyConfigured()
                && accomClasses.stream()
                .filter(accomClass -> !Integer.valueOf(1).equals(accomClass.getSystemDefault()))
                .anyMatch(accomClass -> accomClass.getIsPriceRankConfigured().equals(AccomClassPriceRankStatus.INCOMPLETE) && !isInitialConfiguration());
    }

    public boolean isNewUnconfiguredRoomClass(AccomClass accomClass) {
        return accomClass.getIsPriceRankConfigured().equals(AccomClassPriceRankStatus.INCOMPLETE) && !isInitialConfiguration();
    }

    //Since these are the classes that are saved sometimes we need to find the presenter class so we can alter it and it will be saved
    public AccomClass getAccomClassFromPresenter(AccomClass accomClass) {
        return accomClasses.stream()
                .filter(presenterAccomClass -> presenterAccomClass.getId().equals(accomClass.getId()))
                .findFirst()
                .orElse(null);
    }

    public boolean isComponentRoomsEnabled() {
        return isComponentRoomsEnabled;
    }

    @ForTesting
    public void setComponentRoomsEnabled(boolean componentRoomsEnabled) {
        isComponentRoomsEnabled = componentRoomsEnabled;
    }

    public boolean isAdvancedPriceRankingEnabled() {
        return isAdvancedPriceRankingEnabled;
    }

    @ForTesting
    public void setAdvancedPriceRankingEnabled(boolean advancedPriceRankingEnabled) {
        isAdvancedPriceRankingEnabled = advancedPriceRankingEnabled;
    }

    public boolean isMasterClassPricingEnabled() {
        return isMasterClassPricingEnabled;
    }

    public boolean isPerPersonPricingEnabled() {
        return isPerPersonPricingEnabled;
    }

    public boolean isGroupFinalForecastOverrideEnabled() {
        return isGroupFinalForecastOverrideEnabled;
    }

    @ForTesting
    public void setGroupFinalForecastOverrideEnabled(boolean groupFinalForecastOverrideEnabled) {
        isGroupFinalForecastOverrideEnabled = groupFinalForecastOverrideEnabled;
    }

    public boolean isEnableLrvDropRestrictionEnabled() {
        return isEnableLrvDropRestrictionEnabled;
    }

    @ForTesting
    public void setEnableLrvDropRestrictionEnabled(boolean enableLrvDropRestrictionEnabled) {
        isEnableLrvDropRestrictionEnabled = enableLrvDropRestrictionEnabled;
    }

    public boolean isSumOfPartsEnabled() {
        return isSumOfPartsEnabled;
    }

    @ForTesting
    public void setSumOfPartsEnabled(boolean sumOfPartsEnabled) {
        isSumOfPartsEnabled = sumOfPartsEnabled;
    }

    public void loadMinimumPriceDifferentialData() {
        loadDefaultDifferentialUiWrappers();
        loadSeasonDifferentialUiWrappers();
    }

    private void loadDefaultDifferentialUiWrappers() {
        List<MinimumPriceDifferentialUIWrapper> uiWrappers =
                pricingConfigurationService.getDefaultAccomClassMinPriceDiff().stream()
                        .map(this::createWrapper)
                        .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(uiWrappers) || uiWrappers.size() != accomClassPriceRankings.size()) {
            uiWrappers.addAll(createWrapperForEachPriceRanking(uiWrappers));
        }

        this.diffUiWrappers = uiWrappers;
        this.minPriceDiffOriginalValues = new ArrayList<>();

        diffUiWrappers.forEach(uiWrapper -> {
            MinimumPriceDifferentialUIWrapper copy = uiWrapper.copy();
            this.minPriceDiffOriginalValues.add(copy.getPriceDiff());
        });
    }

    private void loadSeasonDifferentialUiWrappers() {
        this.seasonDiffUiWrappers =
                pricingConfigurationService.getSeasonAccomClassMinPriceDiffs().stream()
                        .map(this::createWrapper)
                        .collect(Collectors.toList());
        this.seasonMinPriceDiffOriginalValues = new ArrayList<>();

        seasonDiffUiWrappers.forEach(uiWrapper -> {
            MinimumPriceDifferentialUIWrapper copy = uiWrapper.copy();
            if (copy.getPriceDiff() instanceof AccomClassMinPriceDiffSeason) {
                this.seasonMinPriceDiffOriginalValues.add((AccomClassMinPriceDiffSeason) copy.getPriceDiff());
            }
        });
    }

    public MinimumPriceDifferentialUIWrapper createWrapper(DailyMinPriceDiff diff) {
        if (isCPProperty()) {
            Tax tax = defaultTax;
            if (diff instanceof AccomClassMinPriceDiffSeason) {
                AccomClassMinPriceDiffSeason priceDiffSeason = (AccomClassMinPriceDiffSeason) diff;
                tax = taxService.findTaxForDateRange(priceDiffSeason.getStartDate(), priceDiffSeason.getEndDate());
            }
            return new MinimumPriceDifferentialUIWrapper(diff, tax);
        }
        return new MinimumPriceDifferentialUIWrapper(diff);
    }

    private List<MinimumPriceDifferentialUIWrapper> createWrapperForEachPriceRanking(List<MinimumPriceDifferentialUIWrapper> uiWrappers) {
        return accomClassPriceRankings.stream()
                .filter(priceRanking -> priceRanking.getId() != null)
                .filter(priceRanking -> uiWrappers.stream().noneMatch(wrapper -> wrapper.getPriceDiff().getAccomClassPriceRank().getId().equals(priceRanking.getId())))
                .map(priceRanking -> {
                    MinimumPriceDifferentialUIWrapper wrapper = createWrapper(new AccomClassMinPriceDiff());
                    wrapper.setAccomClassPriceRank(priceRanking);
                    wrapper.setSimplePathDiff(isCPProperty() ? BigDecimal.ZERO : new BigDecimal("0.01"));
                    return wrapper;
                })
                .collect(Collectors.toList());
    }

    public void saveMinimumPriceDifferentialData() {
        List<AccomClassMinPriceDiff> accomClassMinPriceDiffsToBeSaved =
                getCurrentDefaultMinPriceDiffs().stream()
                        .filter(priceDiff -> priceDiff.getAccomClassPriceRank().getId() != null)
                        .collect(Collectors.toList());
        boolean minDiffReduction = accomClassMinPriceDiffsToBeSaved.stream().anyMatch(dto -> {
            AccomClassMinPriceDiff originalMinDiff = (AccomClassMinPriceDiff) minPriceDiffOriginalValues.stream()
                    .filter(item -> item instanceof AccomClassMinPriceDiff)
                    .filter(minDiff -> minDiff.getAccomClassPriceRank().getId()
                            .equals(dto.getAccomClassPriceRank().getId()))
                    .findFirst()
                    .orElse(null);
            if (originalMinDiff != null) {
                return dto.decreasedInValue(originalMinDiff);
            }
            return false;
        });
        if (minDiffReduction) {
            syncEventAggregatorService.registerSyncEvent(SyncEvent.MINIMUM_PRICE_DIFFERENTIAL_DECREASED);
        }

        pricingConfigurationService.saveAccomClassMinPriceDiffs(accomClassMinPriceDiffsToBeSaved);
        if (pricingConfigurationLTBDEService.isLTBDEOnIndirectConfigChangedEnabled()) {
            boolean isMinPriceDiffChanged = isMinPriceDiffChanged(accomClassMinPriceDiffsToBeSaved, getMinPriceDiffOriginalValues());
            if (isMinPriceDiffChanged) {
                pricingConfigurationLTBDEService.enableLTBDEForPricing(true);
            }
        }
        updatePriceRankConfiguredState();
    }

    private boolean isMinPriceDiffChanged(List<AccomClassMinPriceDiff> minPriceDiffToBeSaved, List<DailyMinPriceDiff> existingMinPriceDiffs) {
        if (existingMinPriceDiffs == null || existingMinPriceDiffs.isEmpty()) return true;
        boolean isMinPriceDiffChanged = false;
        Optional<DailyMinPriceDiff> existingMinPriceDiff;
        for (AccomClassMinPriceDiff minPriceDiffToSave : minPriceDiffToBeSaved) {
            existingMinPriceDiff = existingMinPriceDiffs.stream().filter(existingPriceDiff -> existingPriceDiff.getAccomClassPriceRank().getId().equals(minPriceDiffToSave.getAccomClassPriceRank().getId())).findAny();
            if (existingMinPriceDiff.isEmpty()) {
                return true;
            }
            isMinPriceDiffChanged = isPriceDifferenceChanged(minPriceDiffToSave, existingMinPriceDiff.get());
            if (isMinPriceDiffChanged) {
                return true;
            }
        }
        ;
        return isMinPriceDiffChanged;
    }

    private boolean isPriceDifferenceChanged(AccomClassMinPriceDiff newPriceDiff, DailyMinPriceDiff existingPriceDiff) {
        return !Objects.equals(newPriceDiff.getMondayDiffWithTax(), existingPriceDiff.getMondayDiffWithTax()) ||
                !Objects.equals(newPriceDiff.getTuesdayDiffWithTax(), existingPriceDiff.getTuesdayDiffWithTax()) ||
                !Objects.equals(newPriceDiff.getWednesdayDiffWithTax(), existingPriceDiff.getWednesdayDiffWithTax()) ||
                !Objects.equals(newPriceDiff.getThursdayDiffWithTax(), existingPriceDiff.getThursdayDiffWithTax()) ||
                !Objects.equals(newPriceDiff.getFridayDiffWithTax(), existingPriceDiff.getFridayDiffWithTax()) ||
                !Objects.equals(newPriceDiff.getSaturdayDiffWithTax(), existingPriceDiff.getSaturdayDiffWithTax()) ||
                !Objects.equals(newPriceDiff.getSundayDiffWithTax(), existingPriceDiff.getSundayDiffWithTax());
    }

    public void saveMinimumPriceDifferentialSeasons(List<MinimumPriceDifferentialUIWrapper> seasons) {
        List<AccomClassMinPriceDiffSeason> accomClassMinPriceDiffSeasonsToBeSaved = extractSeasons(seasons);

        enableSyncFlagIfMinDiffReductionInSeasons(accomClassMinPriceDiffSeasonsToBeSaved);

        pricingConfigurationService.saveAccomClassMinPriceDiffSeasons(accomClassMinPriceDiffSeasonsToBeSaved);
    }

    public void enableSyncFlagIfMinDiffReductionInSeasons(List<AccomClassMinPriceDiffSeason> accomClassMinPriceDiffSeasonsToBeSaved) {
        List<AccomClassMinPriceDiffSeason> currentSeasonMinDiffs =
                accomClassMinPriceDiffSeasonsToBeSaved.stream()
                        .filter(wrapper -> wrapper.getStartDate() != null && wrapper.getEndDate() != null)
                        .collect(Collectors.toList());

        seasonMinPriceDiffOriginalValues.sort(Comparator.comparing(AccomClassMinPriceDiffSeason::getStartDate));
        boolean seasonMinDiffReduction = seasonMinPriceDiffOriginalValues.stream().anyMatch(dto -> {
            List<AccomClassMinPriceDiffSeason> overlappingDates = findOverlappingAccomClassMinPriceDiffSeason(dto, currentSeasonMinDiffs);
            return anyMinPriceDiffSeasonDecreasedInValue(dto, overlappingDates);
        });

        if (seasonMinDiffReduction) {
            syncEventAggregatorService.registerSyncEvent(SyncEvent.MINIMUM_PRICE_DIFFERENTIAL_DECREASED);
        }
    }

    private List<AccomClassMinPriceDiffSeason> findOverlappingAccomClassMinPriceDiffSeason(AccomClassMinPriceDiffSeason season, List<AccomClassMinPriceDiffSeason> currentSeasonMinDiffs) {
        return currentSeasonMinDiffs
                .stream()
                .filter(item -> isDateWithinBounds(item, season))
                .collect(Collectors.toList());
    }

    private boolean isDateWithinBounds(AccomClassMinPriceDiffSeason newItem, AccomClassMinPriceDiffSeason originalItem) {
        if (newItem.getStartDate() == null || newItem.getEndDate() == null || originalItem.getStartDate() == null || originalItem == null) {
            return false;
        }
        return !newItem.getStartDate().isAfter(originalItem.getEndDate()) && !newItem.getStartDate().isBefore(originalItem.getStartDate()) ||
                !newItem.getEndDate().isAfter(originalItem.getEndDate()) && newItem.getEndDate().isBefore(originalItem.getStartDate());
    }

    private boolean anyMinPriceDiffSeasonDecreasedInValue(AccomClassMinPriceDiffSeason oldSeason, List<AccomClassMinPriceDiffSeason> overlappingSeasons) {
        return overlappingSeasons.stream().anyMatch(currentSeason -> currentSeason.decreasedInValue(oldSeason));
    }


    public void deleteMinimumPriceDifferentialSeasons() {
        pricingConfigurationService.saveAccomClassMinPriceDiffSeasons(Collections.emptyList());
    }

    public void completeWizard() {
        view.completeWizard();
        init();
    }

    public boolean isInitialConfiguration() {
        return isInitialConfiguration;
    }

    public Boolean hasChanges(RoomConfigurationSelection state) {
        return hasChangesMap.get(state);
    }

    public Boolean hasOnlyMasterClassChange() {
        AccomClass originalMasterClass = accommodationService.findMasterClass(PacmanWorkContextHelper.getPropertyId());
        Optional<AccomClass> updatedMasterClass = accomClasses.stream().filter(AccomClass::getMasterClassBoolean).findAny();

        return hasChangesMap.values().stream().filter(Boolean::booleanValue).count() == 1
                && hasChangesMap.getOrDefault(RoomConfigurationSelection.MASTER_CLASS_MAPPING, false)
                && updatedMasterClass.isPresent()
                && !updatedMasterClass.get().equals(originalMasterClass);
    }

    public void updateHasChanges(RoomConfigurationSelection state, boolean hasChanges) {
        hasChangesMap.put(state, hasChanges || hasChangesMap.get(state));
    }

    public UiContext getContext() {
        return uiContext;
    }

    public boolean hasConflictingOverbookingCeilingDefaults() {
        return overbookingService.hasConflictingOverbookingCeilingDefaults(overbookingAccomTypeDtos);
    }

    public void adjustPriceDiffSeasonsDayOfWeekData(List<AccomClassMinPriceDiffSeason> seasons) {
        seasons.forEach(this::setDOWDifferenceForSeason);
    }

    private void setDOWDifferenceForSeason(AccomClassMinPriceDiffSeason season) {
        List<DayOfWeek> outOfRangeDayOfWeeks = LocalDateUtils.getOutOfRangeDayOfWeeks(season.getStartDate(), season.getEndDate());
        if (outOfRangeDayOfWeeks.contains(DayOfWeek.SUNDAY)) {
            season.setSundayDiff(null);
            season.setSundayDiffWithTax(null);
        }
        if (outOfRangeDayOfWeeks.contains(DayOfWeek.MONDAY)) {
            season.setMondayDiff(null);
            season.setMondayDiffWithTax(null);
        }
        if (outOfRangeDayOfWeeks.contains(DayOfWeek.TUESDAY)) {
            season.setTuesdayDiff(null);
            season.setTuesdayDiffWithTax(null);
        }
        if (outOfRangeDayOfWeeks.contains(DayOfWeek.WEDNESDAY)) {
            season.setWednesdayDiff(null);
            season.setWednesdayDiffWithTax(null);
        }
        if (outOfRangeDayOfWeeks.contains(DayOfWeek.THURSDAY)) {
            season.setThursdayDiff(null);
            season.setThursdayDiffWithTax(null);
        }
        if (outOfRangeDayOfWeeks.contains(DayOfWeek.FRIDAY)) {
            season.setFridayDiff(null);
            season.setFridayDiffWithTax(null);
        }
        if (outOfRangeDayOfWeeks.contains(DayOfWeek.SATURDAY)) {
            season.setSaturdayDiff(null);
            season.setSaturdayDiffWithTax(null);
        }
    }

    public void adjustOverbookingSeasonsDayOfWeekData(List<OverbookingSeasonUIWrapper> seasons) {
        seasons.forEach(this::setOutOfRangeForDOWForSeason);
    }

    private void setOutOfRangeForDOWForSeason(OverbookingSeasonUIWrapper season) {
        List<DayOfWeek> outOfRangeDayOfWeeks = LocalDateUtils.getOutOfRangeDayOfWeeks(season.getStartDate(), season.getEndDate());

        if (outOfRangeDayOfWeeks.contains(DayOfWeek.SUNDAY)) {
            season.setSunday(false);
        }
        if (outOfRangeDayOfWeeks.contains(DayOfWeek.MONDAY)) {
            season.setMonday(false);
        }
        if (outOfRangeDayOfWeeks.contains(DayOfWeek.TUESDAY)) {
            season.setTuesday(false);
        }
        if (outOfRangeDayOfWeeks.contains(DayOfWeek.WEDNESDAY)) {
            season.setWednesday(false);
        }
        if (outOfRangeDayOfWeeks.contains(DayOfWeek.THURSDAY)) {
            season.setThursday(false);
        }
        if (outOfRangeDayOfWeeks.contains(DayOfWeek.FRIDAY)) {
            season.setFriday(false);
        }
        if (outOfRangeDayOfWeeks.contains(DayOfWeek.SATURDAY)) {
            season.setSaturday(false);
        }
    }

    public boolean hasRoomClassConfigChanged(List<AccomClass> originalRoomClasses) {
        List<AccomClass> currentRoomClasses = getNonSystemDefaultAccomClasses();
        if (!currentRoomClasses.containsAll(originalRoomClasses) || !originalRoomClasses.containsAll(currentRoomClasses)) {
            return true;
        }

        return isRoomClassConfigChanged(currentRoomClasses, originalRoomClasses, getAllOriginalAccomTypes(originalRoomClasses));
    }

    private List<AccomType> getAllOriginalAccomTypes(List<AccomClass> originalRoomClasses) {
        return originalRoomClasses.stream().map(AccomClass::getAccomTypes).flatMap(Collection::stream).collect(Collectors.toList());
    }

    private boolean isRoomClassConfigChanged(List<AccomClass> currentRoomClasses, List<AccomClass> originalRoomClasses, List<AccomType> allOriginalAccomTypes) {
        boolean[] hasChanged = {false};

        currentRoomClasses.forEach(currentRoomClass -> {
            Optional<AccomClass> originalRoomClassOpt = originalRoomClasses.stream().filter(originalRoomClass -> originalRoomClass.getId().equals(currentRoomClass.getId())).findAny();
            if (originalRoomClassOpt.isPresent()) {
                AccomClass originalRoomClass = originalRoomClassOpt.get();

                List<AccomType> currentAccomTypes = currentRoomClass.getAccomTypes().stream().collect(Collectors.toList());

                Set<AccomType> originalAccomTypes = originalRoomClass.getAccomTypes();

                //If any AT is in this room class that wasn't before or if any AT is not in this room class that was before
                if (currentAccomTypes.stream().anyMatch(currentAT -> originalAccomTypes.stream().noneMatch(origAT -> currentAT.getId().equals(origAT.getId())))
                        || originalAccomTypes.stream().anyMatch(origAT -> currentAccomTypes.stream().noneMatch(currentAT -> currentAT.getId().equals(origAT.getId())))) {
                    hasChanged[0] = true;
                }
            }
        });

        return hasChanged[0];

    }

    public boolean hasConfigChangedForInventoryGroupView(List<AccomClass> roomClasses) {

        return isRoomClassConfigChanged(getNonSystemDefaultAccomClasses(), inventoryGroupService.getInventoryAccomClassFromList(roomClasses), getAllOriginalAccomTypes(roomClasses));
    }


    public void saveNetworkPathArrows(List<AccomClassPriceRankNetworkArrow> arrows) {
        accomClassPriceRankService.saveNetworkPathArrows(arrows);
        advancedNetworkArrows = arrows;
    }

    public boolean networkPathHasChanges(List<AccomClassPriceRankNetworkArrow> viewNetworkArrows) {
        // Use final array to appease lambda
        final boolean[] hasChanges = {false};

        if (viewNetworkArrows.size() != advancedNetworkArrows.size()) {
            hasChanges[0] = true;
        } else {
            viewNetworkArrows.forEach(viewNetworkArrow -> {
                hasChanges[0] = hasChanges[0] || advancedNetworkArrows.stream().noneMatch(arrow -> arrow.getYCoord().equals(viewNetworkArrow.getYCoord())
                        && arrow.getXCoord().equals(viewNetworkArrow.getXCoord())
                        && arrow.getDirection().equals(viewNetworkArrow.getDirection())
                        && arrow.getPriceRank().getLowerRankAccomClass().getId().equals(viewNetworkArrow.getPriceRank().getLowerRankAccomClass().getId())
                        && arrow.getPriceRank().getHigherRankAccomClass().getId().equals(viewNetworkArrow.getPriceRank().getHigherRankAccomClass().getId())
                        && arrow.getPriceRank().isUpgradeAllowed() == viewNetworkArrow.getPriceRank().isUpgradeAllowed());

            });
        }

        return hasChanges[0];
    }

    public boolean priceRanksHasChanges(List<AccomClassPriceRank> viewPriceRanks) {
        // Use final array to appease lambda
        final boolean[] hasChanges = {false};

        if (viewPriceRanks.size() != accomClassPriceRankings.size()) {
            hasChanges[0] = true;
        } else {
            viewPriceRanks.forEach(viewPriceRank -> {
                hasChanges[0] = hasChanges[0] || accomClassPriceRankings.stream().noneMatch(rank -> rank.getHigherRankAccomClass().getId().equals(viewPriceRank.getHigherRankAccomClass().getId())
                        && rank.getLowerRankAccomClass().getId().equals(viewPriceRank.getLowerRankAccomClass().getId())
                        && rank.isUpgradeAllowed() == viewPriceRank.isUpgradeAllowed());
            });
        }

        return hasChanges[0];
    }

    public boolean networkPathHasPriceRankingChanges(List<AccomClassPriceRankNetworkArrow> viewNetworkArrows) {
        // Use final array to appease lambda
        final boolean[] hasChanges = {false};

        if (viewNetworkArrows.size() != advancedNetworkArrows.size()) {
            hasChanges[0] = true;
        } else {
            viewNetworkArrows.forEach(viewNetworkArrow -> {
                hasChanges[0] = hasChanges[0] || advancedNetworkArrows.stream().noneMatch(arrow -> arrow.getPriceRank().getLowerRankAccomClass().getId().equals(viewNetworkArrow.getPriceRank().getLowerRankAccomClass().getId())
                        && arrow.getPriceRank().getHigherRankAccomClass().getId().equals(viewNetworkArrow.getPriceRank().getHigherRankAccomClass().getId()));
            });
        }

        return hasChanges[0];
    }

    public boolean isRoomClassMoved() {
        return roomClassMoved;
    }

    public void setRoomClassMoved(boolean roomClassMoved) {
        this.roomClassMoved = roomClassMoved;
    }

    public boolean isGroupPricingEnabled() {
        return groupPricingEnabled;
    }

    @ForTesting
    public void setGroupPricingEnabled(Boolean groupPricingEnabled) {
        this.groupPricingEnabled = groupPricingEnabled;
    }

    public void saveGroupPriceRankingData(List<AccomClass> rankedRoomClasses, boolean isConfigurationComplete) {
        accommodationService.updateAccomClasses(rankedRoomClasses);

        //Update Presenter variables for potential future updates
        accomClasses.clear();
        accomClasses.addAll(rankedRoomClasses);

        if (isConfigurationComplete) {
            updatePriceRankConfiguredState();
        }

        updateHasChanges(RoomConfigurationSelection.GROUP_PRICE_RANKING, true);
        sync();
        //Reset variables in presenter
        getDataForRoomTypeMappings();
    }

    public List<MinimumPriceDifferentialUIWrapper> getCurrentUIWrappers() {
        return diffUiWrappers;
    }


    public List<MinimumPriceDifferentialUIWrapper> getCurrentSeasonUIWrappers() {
        return seasonDiffUiWrappers;
    }

    public List<AccomClassMinPriceDiff> getCurrentDefaultMinPriceDiffs() {
        return diffUiWrappers.stream()
                .map(MinimumPriceDifferentialUIWrapper::getPriceDiff)
                .filter(priceDiff -> priceDiff instanceof AccomClassMinPriceDiff)
                .map(priceDiff -> (AccomClassMinPriceDiff) priceDiff)
                .collect(Collectors.toList());

    }

    public List<AccomClassMinPriceDiffSeason> extractSeasons(List<MinimumPriceDifferentialUIWrapper> seasonWrappers) {
        return seasonWrappers.stream()
                .filter(priceDiff -> priceDiff.getPriceDiff() instanceof AccomClassMinPriceDiffSeason)
                .map(this::extractSeason)
                .collect(Collectors.toList());
    }

    public AccomClassMinPriceDiffSeason extractSeason(MinimumPriceDifferentialUIWrapper wrapper) {
        refreshValues(wrapper);
        return (AccomClassMinPriceDiffSeason) wrapper.getPriceDiff();
    }

    private void refreshValues(MinimumPriceDifferentialUIWrapper wrapper) {
        wrapper.setSunday(wrapper.getSunday());
        wrapper.setMonday(wrapper.getMonday());
        wrapper.setTuesday(wrapper.getTuesday());
        wrapper.setWednesday(wrapper.getWednesday());
        wrapper.setThursday(wrapper.getThursday());
        wrapper.setFriday(wrapper.getFriday());
        wrapper.setSaturday(wrapper.getSaturday());
    }

    @Override
    public void showWarning(String message) {
        view.showAlertWarning(getText("Information"), StringUtils.EMPTY, new Label(message));
    }

    public List<AccomClass> getRankedAccomClasses() {
        List<AccomClassPriceRank> accomClassPriceRanks = getPriceRankings();
        //Use array as item needs to be final for lambda
        boolean isFirstPriceRank = true;
        int rankOrder = accomClassPriceRanks.size() + 1;

        List<AccomClass> rankedRoomClasses;
        if (CollectionUtils.isNotEmpty(accomClassPriceRanks)) {
            rankedRoomClasses = new ArrayList<>();
            for (AccomClassPriceRank priceRank : accomClassPriceRanks) {
                //Don't add each class more than once
                if (isFirstPriceRank) {
                    isFirstPriceRank = false;
                    AccomClass firstAccomClass = priceRank.getHigherRankAccomClass();
                    firstAccomClass.setRankOrder(rankOrder);
                    rankedRoomClasses.add(priceRank.getHigherRankAccomClass());
                    rankOrder--;
                }
                AccomClass accomClass = priceRank.getLowerRankAccomClass();
                accomClass.setRankOrder(rankOrder);
                rankedRoomClasses.add(accomClass);
                rankOrder--;
            }
        } else {
            rankedRoomClasses = getNonSystemDefaultAccomClasses();
        }

        return rankedRoomClasses;
    }

    public List<AccomType> getAllAccomTypes() {
        return accommodationService.getAllAccomTypes();
    }

    public List<AccomType> getAllActiveAccomTypes() {
        return accommodationService.getAllActiveAccomTypes();
    }

    public void updateAccomClasses(List<AccomClass> accomClasses) {
        accommodationService.updateAccomClasses(accomClasses);
    }

    public boolean isPricingManagementInvalidedByMinPriceDiffIncrease() {
        return pricingManagementInvalidedByMinPriceDiffIncrease;
    }

    public void setPricingManagementInvalidedByMinPriceDiffIncrease(boolean pricingManagementInvalidedByMinPriceDiffIncrease) {
        this.pricingManagementInvalidedByMinPriceDiffIncrease = pricingManagementInvalidedByMinPriceDiffIncrease;
    }

    public boolean isHasRoomClassConfigChanged() {
        return hasRoomClassConfigChanged;
    }

    public void setHasRoomClassConfigChanged(boolean hasRoomClassConfigChanged) {
        this.hasRoomClassConfigChanged = hasRoomClassConfigChanged;
    }

    public boolean isHasRoomClassConfigChangedForSelectiveAutomation() {
        return hasRoomClassConfigChangedForSelectiveAutomation;
    }

    public void setHasRoomClassConfigChangedForSelectiveAutomation(boolean hasRoomClassConfigChangedForSelectiveAutomation) {
        this.hasRoomClassConfigChangedForSelectiveAutomation = hasRoomClassConfigChangedForSelectiveAutomation;
    }

    public boolean isLimitedDataBuildEnabled() {
        return isLimitedDataBuildEnabled;
    }

    public boolean hasRoomClassAgileRatesProducts() {
        return agileRatesConfigurationService.hasRoomClassProducts();
    }

    public void captureImpactedRoomClassConfig(Integer impactedRoomClassId, Integer movedAccomTypeId) {
        Set<Integer> roomTypes = getImpactedRoomClassConfigMap().get(impactedRoomClassId);

        if (roomTypes == null) {
            getImpactedRoomClassConfigMap().put(impactedRoomClassId, new HashSet<>() {{
                add(movedAccomTypeId);
            }});
        } else {
            if (roomTypes.contains(movedAccomTypeId)) {
                roomTypes.remove(movedAccomTypeId);
            } else {
                roomTypes.add(movedAccomTypeId);
            }
        }
    }

    public boolean deleteCloseHighestBarOverrides(Map<Integer, Set<Integer>> impactedAccomClassesTorRemoveOverrides) {
        if (impactedAccomClassesTorRemoveOverrides.isEmpty()) {
            return false;
        }
        return closeHighestBarService.deleteCloseHighestBarForAccomClasses(impactedAccomClassesTorRemoveOverrides);
    }

    public void deactivateOverridesFromAnalyticsTables() {
        List<Integer> accomClasses = impactedRoomClassConfigMap.keySet().stream()
                .filter(accomClassId -> !impactedRoomClassConfigMap.get(accomClassId).isEmpty()).collect(Collectors.toList());
        closeHighestBarService.deactivateOverridesFromAnalyticsTables(accomClasses);
    }

    public boolean isBaseClassInInventory(AccomClass accomClass) {
        return inventoryGroupService.isBaseClassInInventory(accomClass);
    }

    public void setAdvancePriceRankConfigurationParameter(boolean value) {
        configParamsService.updateParameterValue(FeatureTogglesConfigParamName.IS_ADVANCED_PRICE_RANKING_ENABLED.value(), value);
        isAdvancedPriceRankingEnabled = value;
    }

    public List<MaximumOccupantsEntity> getActiveMaximumOccupants() {
        return perPersonPricingService.getMaximumOccupants()
                .stream()
                .filter(maximumOccupantsEntity -> maximumOccupantsEntity.getStatus().equals(1))
                .collect(Collectors.toList());
    }

    public List<MaximumOccupantsEntity> validateMaximumOccupantFields(List<MaximumOccupantsEntity> maximumOccupantsEntities) {
        List<MaximumOccupantsEntity> invalidEntities = new ArrayList<>();
        for (MaximumOccupantsEntity maxOccupants : maximumOccupantsEntities) {
            if (maxOccupants.getMax().compareTo(maxOccupants.getAdults() + maxOccupants.getChildren()) > 0 ||
                    maxOccupants.getMax().compareTo(1) < 0) {
                invalidEntities.add(maxOccupants);
            }
        }
        return invalidEntities;
    }

    public boolean isMaximumOccupancyConfigured() {
        return getAllActiveAccomTypes().size() == getActiveMaximumOccupants().size();
    }

    protected boolean isAgileRatesEnabled() {
        return isAgileRatesEnabled;
    }

    @ForTesting
    public void setAgileRatesEnabled(boolean agileRatesEnabled) {
        isAgileRatesEnabled = agileRatesEnabled;
    }

    @ForTesting
    public void setAccomClassesMarkedForDeletion(List<AccomClass> accomClassesMarkedForDeletion) {
        this.accomClassesMarkedForDeletion = accomClassesMarkedForDeletion;
    }

    @ForTesting
    public List<AccomClass> getAccomClassesMarkedForDeletion() {
        return accomClassesMarkedForDeletion;
    }

    @ForTesting
    public void setAccomClasses(SortedSet<AccomClass> accomClasses) {
        this.accomClasses = accomClasses;
    }

    public boolean isMissingRTAlertOpenForRoomTypeMapping() {
        return roomTypeRecodingService.isExistingMissingRoomTypeAlertOpen();
    }

    public boolean isRoomTypeRecodingUIEnabled() {
        return isRoomTypeRecodingUIEnabled;
    }

    @ForTesting
    public void setRoomTypeRecodingUIEnabled(boolean roomTypeRecodingUIEnabled) {
        isRoomTypeRecodingUIEnabled = roomTypeRecodingUIEnabled;
    }

    public boolean showDiscontinuedRTCheckBox() {
        return isRoomTypeRecodingUIEnabled() && !getAllDiscontinuedRoomTypes().isEmpty();
    }

    public boolean isGFFOverrideAtFGLevelEnabled() {
        return isGFFOverrideAtFGLevelEnabled;
    }

    @ForTesting
    void setAdvancedNetworkArrows(List<AccomClassPriceRankNetworkArrow> advancedNetworkArrows) {
        this.advancedNetworkArrows = advancedNetworkArrows;
    }

    public List<String> getAllDiscontinuedRoomTypes() {
        return accommodationService.getAllDiscontinuedRoomTypes().stream()
                .filter(accomType -> accomType.getDisplayStatusId().equals(Status.INACTIVE.getId()))
                .map(AccomType::getAccomTypeCode)
                .collect(Collectors.toList());
    }

    public boolean hasNewAccomClasses() {
        //Return any accom classes that are AccomClassPriceRankStatus.INCOMPLETE
        return accomClasses.stream().anyMatch(accomClass -> accomClass.getIsPriceRankConfigured().equals(AccomClassPriceRankStatus.INCOMPLETE));
    }

    public void resetAccomClasses() {
        //Reset AccomClassPriceRankStatus.COMPLETE_NOT_RANKED accom classes to AccomClassPriceRankStatus.INCOMPLETE
        accomClassPriceRankService.resetAccomClasses(accomClasses.stream().filter(accomClass -> accomClass.getIsPriceRankConfigured().equals(AccomClassPriceRankStatus.COMPLETE_NOT_RANKED)).collect(Collectors.toList()));
    }

    void resetHasChangeMap() {
        hasChangesMap.clear();
    }

    public String getPdfFileName() {
        return String.format("%s_P%d_%s", PDF_FILE_NAME, uiContext.getPropertyId(), DateUtil.formatDate(getSystemDateAsLocalDate().toDate(), dateService.getUserDateFormat()));
    }

    public void resetCurrentMinimumPriceDifferentials() {
        getCurrentUIWrappers().forEach(this::resetDifferential);
    }

    private void resetDifferential(MinimumPriceDifferentialUIWrapper wrapper) {
        wrapper.setSimplePathDiff(BigDecimal.ZERO);
    }

    public boolean isCPProperty() {
        return this.isCPProperty;
    }

    @ForTesting
    public void setCPProperty(boolean CPProperty) {
        isCPProperty = CPProperty;
    }

    public boolean hasAdvancedSettings() {
        return getCurrentUIWrappers().stream().anyMatch(MinimumPriceDifferentialUIWrapper::doesNotHaveAllEqualValues);
    }

    public List<MinimumPriceDifferentialUIWrapper> createUiWrappers(List<AccomClassMinPriceDiffSeason> seasonDiffs) {
        return seasonDiffs.stream()
                .map(this::createWrapper)
                .collect(Collectors.toList());
    }

    @VisibleForTesting
    public void setDiffUiWrappers(List<MinimumPriceDifferentialUIWrapper> diffUiWrappers) {
        this.diffUiWrappers = diffUiWrappers;
    }

    @ForTesting
    public void setSeasonDiffUiWrappers(List<MinimumPriceDifferentialUIWrapper> seasonDiffUiWrappers) {
        this.seasonDiffUiWrappers = seasonDiffUiWrappers;
    }

    @ForTesting
    public void setSeasonMinPriceDiffOriginalValues(List<AccomClassMinPriceDiffSeason> seasonMinPriceDiffOriginalValues) {
        this.seasonMinPriceDiffOriginalValues = seasonMinPriceDiffOriginalValues;
    }

    @VisibleForTesting
    public List<AccomClassMinPriceDiffSeason> getCurrentDefaultMinPriceDiffsSeasons() {
        return extractSeasons(seasonDiffUiWrappers);

    }

    public List<RoomClassRatioConfigDto> getRoomClassRatioConfigDtos() {
        List<GroupFinalForecastConfig> gffConfigs = getGffConfig();
        Cloner clone = new Cloner();
        originalGffConfigs = clone.deepClone(gffConfigs);
        boolean isGffConfigured = gffConfigs != null && !gffConfigs.isEmpty();
        List<AccomClass> filteredAccomClass = groupFinalForecastConfigService.getAccomClasses(PacmanWorkContextHelper.getPropertyId());
        roomClassRatioConfigDtos = new ArrayList<>();
        filteredAccomClass.forEach(accomClass -> {
            GroupFinalForecastConfig gffConfig = null;
            if (isGffConfigured) {
                gffConfig = gffConfigs.stream().filter(config -> accomClass.getId().equals(config.getAccomClassId())).findFirst().orElse(null);
            }
            RoomClassRatioConfigDto dto = new RoomClassRatioConfigDto();
            dto.setId(gffConfig != null ? gffConfig.getId() : null);
            dto.setRatioDistribution(gffConfig != null ? gffConfig.getRatioDistribution() : getDefaultCapacityRatio(accomClass.getCode(), isGffConfigured));
            dto.setIncludedInWashDistribution((gffConfig == null || gffConfig.getIncludeInWashDistribution() == 1));
            dto.setRoomClassId(accomClass.getId());
            dto.setRoomClassName(accomClass.getCode());
            dto.setCapacity(groupFinalForecastConfigService.getRoomTypeCapacity(accomClass, true));
            roomClassRatioConfigDtos.add(dto);
        });
        return roomClassRatioConfigDtos;
    }

    private Double getDefaultCapacityRatio(String code, boolean isGffConfigured) {
        Double defaultCapacityRatio = null;
        if (!isGffConfigured) {
            Map<String, Double> defaultCapacityRatios = groupFinalForecastConfigService.getDefaultCapacityRatios(PacmanWorkContextHelper.getPropertyId());
            defaultCapacityRatio = defaultCapacityRatios.get(code);
        }
        return defaultCapacityRatio;
    }

    public long getTotalAccomCapacity() {
        return groupFinalForecastConfigService.getTotalAccomCapacity(PacmanWorkContextHelper.getPropertyId());
    }

    public List<GroupFinalForecastConfig> getGffConfig() {
        return groupFinalForecastConfigService.getAllConfig();
    }

    public List<GroupFinalForecastConfig> saveRoomClassRatioConfiguration(List<RoomClassRatioConfigDto> roomClassConfigsDto) {
        List<GroupFinalForecastConfig> groupFinalForecastConfigs = dtoToEntity(roomClassConfigsDto);
        List<GroupFinalForecastConfig> differentialConfigs = groupFinalForecastConfigs.stream().filter(config -> !originalGffConfigs.contains(config)).collect(Collectors.toList());
        return groupFinalForecastConfigService.saveGffConfigs(differentialConfigs, PacmanWorkContextHelper.getPropertyId());
    }


    public List<GroupFinalForecastConfig> dtoToEntity(List<RoomClassRatioConfigDto> roomClassConfigsDto) {
        List<GroupFinalForecastConfig> gffConfigs = new ArrayList<>();
        roomClassConfigsDto.forEach(dto -> {
            GroupFinalForecastConfig gffConfig = new GroupFinalForecastConfig();
            if (dto.getId() != null) {
                gffConfig.setId(dto.getId());
            }
            gffConfig.setAccomClassId(dto.getRoomClassId());
            gffConfig.setIncludeInWashDistribution(dto.getIncludedInWashDistribution() ? 1 : 0);
            gffConfig.setRatioDistribution(dto.getRatioDistribution());
            gffConfigs.add(gffConfig);
        });
        return gffConfigs;
    }

    public boolean validateSeasonForMultipleTaxes(MinimumPriceDifferentialUIWrapper seasonToValidate) {
        List<Tax> overlappingTaxSeasons = taxService.getPartialOverlappingTaxSeasons(seasonToValidate.getStartDate(), seasonToValidate.getEndDate());
        if (CollectionUtils.isNotEmpty(overlappingTaxSeasons)) {
            InvalidSeasonDatesLayout layout = new InvalidSeasonDatesLayout(overlappingTaxSeasons, getText("minPriceDiff"));
            layout.showWarningAboutSeasonConsumingMultipleTaxes();
        }
        return CollectionUtils.isEmpty(overlappingTaxSeasons);
    }

    public void populateGffConfigWithDefaults() {
        groupFinalForecastConfigService.populateGffConfigWithDefaults(PacmanWorkContextHelper.getPropertyId());
    }

    public List<DailyMinPriceDiff> getMinPriceDiffOriginalValues() {
        return minPriceDiffOriginalValues;
    }

    public void setMinPriceDiffOriginalValues(List<DailyMinPriceDiff> minPriceDiffOriginalValues) {
        this.minPriceDiffOriginalValues = minPriceDiffOriginalValues;
    }

    public String getPermissionKeyForMinPriceDiff() {
        boolean isMinPriceDiffPermissionEnabled = !isMasterClassPricingEnabled();

        return isMinPriceDiffPermissionEnabled ? TetrisPermissionKey.ROOMS_CONFIGURATION_MIN_PRICE_DIFFERENTIAL : TetrisPermissionKey.ROOMS_CONFIGURATION_SUBMODULE_PERMISSION;
    }

    public boolean isAccorSelectSubscriptionEnable() {
        return configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.ACCOR_SELECT_SUBSCRIPTION_ENABLE_FEATURE);
    }

    public boolean isHiltonUnassignedRoomTypeNoOverrideRemovalEnabled() {
        return isHiltonUnassignedRoomTypeNoOverrideRemovalEnabled;
    }

    public boolean isHideAdvancedSettingsAndRTOverbookingTypeEnabled() {
        return isHideAdvancedSettingsAndRTOverbookingTypeEnabled;
    }

    public Map<Integer, Set<Integer>> findRoomClassesWithoutUnassignedRoomTypes() {
        Map<Integer, Set<Integer>> roomClassMapWithoutUnassignedRoomTypes = new HashMap<>(impactedRoomClassConfigMap);
        List<Integer> allUnassignedRoomTypeIDs = accommodationService.getAllUnassignedRoomTypesIncludingZeroCapacity()
                .stream()
                .map(AccomType::getId)
                .collect(Collectors.toList());
        impactedRoomClassConfigMap.entrySet().stream()
                .filter(ac -> ac.getKey() != 1)
                .forEach(ac -> {
                    if (ac.getValue().stream().anyMatch(allUnassignedRoomTypeIDs::contains)) {
                        roomClassMapWithoutUnassignedRoomTypes.remove(ac.getKey());
                    }
                });
        return roomClassMapWithoutUnassignedRoomTypes;
    }

    public void reTriggerSelectiveAutomation() {
        configAutomationService.reTriggerSelectiveAutomation();
    }

    public boolean isReConfigureSelectiveAutomationEnabled() {
        return isReConfigureSelectiveAutomationEnabled;
    }

    public void updateReConfigureSelectiveAutomationToFalse() {
        configParamsService.updateParameterValue(RE_CONFIGURE_SELECTIVE_AUTOMATION_ENABLED.value(), false);
    }

    public boolean isRoomClassExclusionInROHGroupEvaluationEnabled() {
        return configParamsService.getBooleanParameterValue(ENABLE_ROOM_CLASS_EXCLUSION_IN_ROH_GROUP_EVALUATION);
    }

    public void updateROHGroupEvaluationExclusionFlagFor(AccomClass accomClass) {
        accommodationService.updateROHGroupEvaluationExclusionFlagFor(accomClass);
    }

    private Map<Integer, Product> getAllProductsExcludingBarMap() {
        return accommodationService.getAllProductsExcludingBar().stream()
                .collect(Collectors.toMap(Product::getId, product -> product,
                        (oldValue, newValue) -> newValue));
    }

    public Map<Integer, Product> getProductMap() {
        return productMap;
    }

    public List<AccomType> getAllUnassignedAccomTypesIncludingZeroCapacity() {
        List<AccomType> allUnassignedAccomTypes = accommodationService.getAllUnassignedRoomTypesIncludingZeroCapacity().stream().filter(at -> !at.isDiscontinued()).collect(Collectors.toList());
        return allUnassignedAccomTypes == null ? List.of() : allUnassignedAccomTypes;
    }

    private Map<Integer, Set<String>> createProductAccomClassMap() {
        List<ProductAccomType> productAccomTypes = accommodationService.getProductAccomTypes();
        Map<Integer, Set<String>> productAccomClassMap = new HashMap<>();
        productAccomTypes.forEach(productAccomType -> {
            if (!productAccomClassMap.containsKey(productAccomType.getProduct().getId())) {
                productAccomClassMap.put(productAccomType.getProduct().getId(), new HashSet<>());
            }
            productAccomClassMap.get(productAccomType.getProduct().getId())
                    .add(productAccomType.getAccomType().getAccomClass().getName());
        });
        return productAccomClassMap;
    }

    public Map<Integer, Set<String>> getProductAccomClassMap() {
        return productAccomClassMap;
    }

    public void saveProductAccomTypes(List<ProductAccomType> productAccomTypes) {
        accommodationService.saveProductAccomTypes(productAccomTypes);
    }

    public void saveGroupPricingConfigAccomTypes(List<RoomTypeProductWrapper> roomTypeProductWrappers) {
        List<GroupPricingConfigAccomType> groupPricingConfigAccomTypes = new ArrayList<>();
        List<GroupPricingConfigAccomType> existingGroupPricingConfigAccomTypes = groupPricingConfigurationService.getAllConfigAccomType();

        for (GroupPricingConfigAccomType groupPricingConfigAccomType : existingGroupPricingConfigAccomTypes) {
            if (!roomTypeProductWrappers.stream().anyMatch(roomTypeProductWrapper -> roomTypeProductWrapper.getAccomType()
                    .equals(groupPricingConfigAccomType.getAccomType()))) {

                RoomTypeProductWrapper roomTypeProductWrapper = new RoomTypeProductWrapper();
                roomTypeProductWrapper.setAccomType(groupPricingConfigAccomType.getAccomType());
                roomTypeProductWrapper.setGroupPricing(groupPricingConfigAccomType.isActive());
                roomTypeProductWrappers.add(roomTypeProductWrapper);
            }
        }

        for (RoomTypeProductWrapper roomTypeProductWrapper : roomTypeProductWrappers) {
            GroupPricingConfigAccomType groupPricingConfigAccomType = new GroupPricingConfigAccomType();
            groupPricingConfigAccomType.setAccomType(roomTypeProductWrapper.getAccomType());
            groupPricingConfigAccomType.setActive(roomTypeProductWrapper.isGroupPricing());
            groupPricingConfigAccomType.setPropertyId(PacmanWorkContextHelper.getPropertyId());
            groupPricingConfigAccomTypes.add(groupPricingConfigAccomType);
        }

        groupPricingConfigurationService.saveConfigAccomType(groupPricingConfigAccomTypes);
    }

    public boolean isAddNewRoomTypeToProductEnabled() {
        return isAddNewRoomTypeToProductEnabled;
    }

    public boolean isSpecialUseRoomTypesEnabled() {
        return isSpecialUseRoomTypesEnabled;
    }

    public boolean isLimitedEditionProperty() {
        return isLimitedEditionProperty;
    }

    public Product getBarProduct() {
        return accommodationService.getBARProduct();
    }

    public boolean isUpdateCOWValueOnDemandEnabled() {
        return configParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_ON_DEMAND_REFRESH_FOR_COW);
    }

    public void enableLTBDEForRoomTypeMappingChanges() {
        if (hasChanges(RoomConfigurationSelection.ROOM_TYPE_MAPPING)) {
            pricingConfigurationLTBDEService.enabledLTBDEIfRoomTypeIsAttachedToActiveSmallGroup(getAllImpactedRoomTypeId());
        }
    }

    private Set<Integer> getAllImpactedRoomTypeId() {
        Set<Integer> impactedRoomTypeIds = new HashSet<>();
        impactedRoomClassConfigMap.values().forEach(impactedRoomTypeIds::addAll);
        return impactedRoomTypeIds;
    }

    public String createHelpLink(int helpId) {
        String g3Token = HelpUtil.createG3HelpToken(uiContext.getUserPrincipal().getEmail(), uiContext.getClientCode());
        return HelpUtil.getSecureHelpUrl(helpId, UiUtils.getLocale(), UiUtils.getTetrisUi().isContinuousPricingEnabled(), g3Token, uiContext.isHiltonClientCode());
    }

    public void calculateCostOfWalk() {
        costOfWalkCalculator.refreshDefaultCostOfWalkValues();
        costofWalkService.turnOnCostOfWalkConfigChangedSyncFlag();
    }

    public void enableLTBDEForPricing() {
        if (configParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_LTBDE_ON_CONFIG_CHANGE)) {
            pricingConfigurationLTBDEService.enableLTBDEForPricing(true);
        }
    }

    public void enableLTBDEFlagForPricingOnIndirectConfigChangeIfApplicable() {
        pricingConfigurationLTBDEService.enableLTBDEOnIndirectConfigChangedIfApplicable();
    }

    public boolean isAddRoomClassAllowedInLicensePackage(String cacheLicenseKey, int parameterValue, Operator operator) {
        return licenseService.validateChangedParameter(cacheLicenseKey, parameterValue, PacmanWorkContextHelper.getPropertyId(), operator);
    }

    public boolean isLicensePackageEnabled() {
        return pacmanConfigParamsService.getBooleanParameterValue(FEATURE_LICENSING_ENABLED);
    }

    public boolean isInternalUser() {
        String userId = PacmanWorkContextHelper.getUserId();
        GlobalUser globalUser = userGlobalDBService.getGlobalUserById(Integer.parseInt(userId));
        return nonNull(globalUser) && globalUser.isInternal();
    }

    public boolean hasSpecialUseRoomType() {
        return overbookingService.hasSpecialUseRoomType();
    }

    public boolean isDistributeUnsoldCapacityEnabled() {
        return overbookingService.isDistributeUnsoldCapacityEnabled();
    }

    public void disableOverbookingForAccomType(AccomType accomType) {
        overbookingAccomTypeDtos.stream()
                .filter(dto -> dto.getAccomType().equals(accomType))
                .forEach(dto -> {
                    dto.setOverbooking(false);
                    dto.setSunday(false);
                    dto.setMonday(false);
                    dto.setTuesday(false);
                    dto.setWednesday(false);
                    dto.setThursday(false);
                    dto.setFriday(false);
                    dto.setSaturday(false);
                });
    }

    public boolean completeWizardAfterLastStep(TetrisWizard wizard, WizardStep step) {
        if (wizard.isLastStep(step)) {
            completeWizard();
            return false;
        }
        return true;
    }

    public boolean isAutomatedOvbkReductionEnabled() {
        return propertyAttributeService.getAttributeValueByAttributeName(PropertyAttributeEnum.ENABLED_AUTOMATED_OVBK_REDUCTION.getAttributeName()).equals(ENABLED);
    }

    public void updateAutomatedOvbkReductionEnabledParam(boolean isEnabled) {
        propertyAttributeService.updateDefaultPropertyAttributeValue(PropertyAttributeEnum.ENABLED_AUTOMATED_OVBK_REDUCTION.getAttributeName(),
                isEnabled ? ENABLED: NOT_ENABLED);
    }

    public void updateThreshold(Integer threshold) {
        String thresholdValue = threshold.equals(MIN_THRESHOLD) ? MIN_THRESHOLD.toString() : Double.toString((double) threshold / 100);
        propertyAttributeService.updateDefaultPropertyAttributeValue(
                PropertyAttributeEnum.CLOSED_COMPITITOR_RATIO.getAttributeName(), thresholdValue);
    }

    public Integer getCloseCompititorThreshold() {
        BigDecimal value = new BigDecimal(propertyAttributeService.getAttributeValueByAttributeName(PropertyAttributeEnum.CLOSED_COMPITITOR_RATIO.getAttributeName()))
                .multiply(BigDecimal.valueOf(100)).setScale(0, RoundingMode.HALF_UP);
        return value.intValue();
    }

    public boolean isAutomatedOverbookingReductionEnabled() {
        return pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.AUTOMATED_OVERBOOKING_ENABLED);
    }

    private void populateInitialAccomClasses() {
        initialAccomClasses = accommodationService.getAllActiveAccomClasses();
    }

    @ForTesting
    public List<AccomClass> getInitialAccomClasses() {
        return initialAccomClasses;
    }

    @ForTesting
    public void setInitialAccomClasses(List<AccomClass> initialAccomClasses) {
        this.initialAccomClasses = initialAccomClasses;
    }


}
