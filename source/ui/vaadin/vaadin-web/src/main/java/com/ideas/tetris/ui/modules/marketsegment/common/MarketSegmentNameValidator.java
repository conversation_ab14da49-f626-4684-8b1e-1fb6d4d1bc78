package com.ideas.tetris.ui.modules.marketsegment.common;

import com.ideas.tetris.ui.common.data.validator.AlphaNumericValidator;
import com.ideas.tetris.ui.common.util.UiUtils;
import com.vaadin.data.ValueContext;
import com.vaadin.data.validator.RegexpValidator;
import com.vaadin.v7.data.Validator;

import java.util.List;
import java.util.stream.Collectors;

public class MarketSegmentNameValidator implements Validator {
    private final List<String> marketSegmentNames;
    private final String name;

    public MarketSegmentNameValidator(String originalValue, List<String> presenter) {
        this.name = originalValue;
        this.marketSegmentNames = presenter;
    }

    @Override
    public void validate(Object o) throws Validator.InvalidValueException {
        RegexpValidator invalidMarketSegmentName = new RegexpValidator(UiUtils.getText("invalidMarketSegmentName"), AlphaNumericValidator.REG_EXP_ALPHA_NUMERIC_WITH_SPACE);
        if (invalidMarketSegmentName.apply((String) o, new ValueContext()).isError()) {
            throw new InvalidValueException(UiUtils.getText("invalidMarketSegmentName"));
        }
        //tetrisTextField.addValidator(new AlphaNumericValidator( AlphaNumericValidator.REG_EXP_ALPHA_NUMERIC_WITH_SPACE, UiUtils.getText("invalidMarketSegmentName")));
        List<String> filterList = marketSegmentNames.stream().filter(s -> s !=null && s.equalsIgnoreCase((String) o) && !s.equals(name)).collect(Collectors.toList());
        if (!filterList.isEmpty()) {
            throw new Validator.InvalidValueException(UiUtils.getText("duplicate.name.not"));
        }
    }
}
