package com.ideas.tetris.ui.modules.internalalert;

import com.ideas.tetris.ui.common.cdi.TetrisNavigatorView;
import com.ideas.tetris.ui.common.component.TetrisBeanItemContainer;
import com.ideas.tetris.ui.common.component.TetrisSaveCancelButtonBar;
import com.ideas.tetris.ui.common.component.checkbox.TetrisCheckBox;
import com.ideas.tetris.ui.common.component.table.TetrisChangeAwareComponentColumnGenerator;
import com.ideas.tetris.ui.common.component.table.TetrisChangeAwareTable;
import com.ideas.tetris.ui.common.util.ChangeAware;
import com.vaadin.annotations.Title;
import com.vaadin.ui.Alignment;
import com.vaadin.ui.Button;
import com.vaadin.v7.ui.Table;
import com.vaadin.v7.ui.VerticalLayout;

import java.util.List;

@Title("My Subscriptions")
public class AlertSubscriptionView extends TetrisNavigatorView<AlertSubscriptionPresenter, Void> {
    private TetrisChangeAwareTable table;
    private TetrisBeanItemContainer<SubscriptionDto> tetrisBeanItemContainer;


    @Override
    protected void initView() {
        // letting presenter control view
    }

    protected void initViewInternal() {
        setSizeFull();
        VerticalLayout layout = new VerticalLayout();
        layout.setMargin(true);
        layout.setSpacing(true);
        layout.setSizeFull();
        layout.addComponent(createSubcriptionTable());
        TetrisSaveCancelButtonBar buttonBar = createSaveCancelButtonBar();
        layout.addComponent(buttonBar);
        layout.setComponentAlignment(buttonBar, Alignment.MIDDLE_RIGHT);
        layout.setExpandRatio(table, 1.0f);
        setCompositionRoot(layout);
    }

    private TetrisChangeAwareTable createSubcriptionTable() {
        table = new TetrisChangeAwareTable();
        tetrisBeanItemContainer = new TetrisBeanItemContainer<SubscriptionDto>(SubscriptionDto.class);
        table.setContainerDataSource(tetrisBeanItemContainer);

        String alertTypeCol = "alertType";
        String isSubscribedCol = "subscribed";

        table.addGeneratedColumn(isSubscribedCol, new TetrisChangeAwareComponentColumnGenerator() {
            @Override
            public ChangeAware generateComponent(Table source, Object itemId, Object columnId) {
                SubscriptionDto subscription = (SubscriptionDto) itemId;
                TetrisCheckBox checkbox = new TetrisCheckBox();
                checkbox.setValue(subscription.isSubscribed());
                return checkbox;
            }
        });

        table.setVisibleColumns(alertTypeCol, isSubscribedCol);

        table.setColumnHeaders("Alert Type", "Is Subscribed");

        table.setSizeFull();
        table.setColumnCollapsingAllowed(false);
        return table;
    }

    private TetrisSaveCancelButtonBar createSaveCancelButtonBar() {
        TetrisSaveCancelButtonBar saveCancelButtonBar = new TetrisSaveCancelButtonBar();
        saveCancelButtonBar.setSizeUndefined();
        table.setSaveCancelButtonBar(saveCancelButtonBar);

        saveCancelButtonBar.addValidSaveListener(new TetrisSaveCancelButtonBar.ValidSaveListener() {
            @Override
            public void onValidSave(TetrisSaveCancelButtonBar.ValidSaveEvent event) {
                presenter.updateSubscriptions(tetrisBeanItemContainer.getItems());
                showSaveSuccessMessage();
            }
        });
        saveCancelButtonBar.getCancelButton().addClickListener(new Button.ClickListener() {
            @Override
            public void buttonClick(Button.ClickEvent event) {
                updateSubscriptionTable(presenter.getSubscriptions());
            }
        });
        return saveCancelButtonBar;

    }

    protected void updateSubscriptionTable(List<SubscriptionDto> subscriptions) {
        tetrisBeanItemContainer.removeAllItems();
        tetrisBeanItemContainer.addAll(subscriptions);
    }


}
