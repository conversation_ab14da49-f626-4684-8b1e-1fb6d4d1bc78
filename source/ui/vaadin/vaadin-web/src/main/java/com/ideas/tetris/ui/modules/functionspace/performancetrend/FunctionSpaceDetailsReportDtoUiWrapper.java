package com.ideas.tetris.ui.modules.functionspace.performancetrend;

import com.ideas.tetris.pacman.services.functionspace.report.dto.FunctionSpaceDetailsReportDto;
import com.ideas.tetris.platform.common.time.JavaLocalDateUtils;
import com.ideas.tetris.ui.common.util.UiUtils;

import java.time.format.TextStyle;

public class FunctionSpaceDetailsReportDtoUiWrapper {
    public FunctionSpaceDetailsReportDtoUiWrapper() {
    }

    private FunctionSpaceDetailsReportDto functionSpaceDetailsReportDto;

    public FunctionSpaceDetailsReportDtoUiWrapper(FunctionSpaceDetailsReportDto functionSpaceDetailsReportDto) {
        this.functionSpaceDetailsReportDto = functionSpaceDetailsReportDto;
    }

    public FunctionSpaceDetailsReportDto getDto() {
        return functionSpaceDetailsReportDto;
    }


    public String getDayOfWeek() {
       return JavaLocalDateUtils.toJavaLocalDate(functionSpaceDetailsReportDto.getOccupancyDate()).getDayOfWeek().getDisplayName(TextStyle.SHORT,UiUtils.getLocale());
    }
}
