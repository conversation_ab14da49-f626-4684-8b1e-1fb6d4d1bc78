package com.ideas.tetris.ui.modules.marketsegment.common;

import com.ideas.tetris.pacman.services.marketsegment.MarketSegmentCasinoCategoryType;
import com.ideas.tetris.pacman.services.marketsegment.entity.ForecastActivityType;
import com.ideas.tetris.pacman.services.marketsegment.service.AnalyticalMarketSegmentAttribute;
import com.ideas.tetris.pacman.services.marketsegment.service.AnalyticalMarketSegmentSummary;
import com.ideas.tetris.pacman.services.product.Product;
import com.ideas.tetris.ui.common.component.TetrisBeanItemContainer;
import com.ideas.tetris.ui.common.component.TetrisSaveCancelButtonBar;
import com.ideas.tetris.ui.common.component.table.TetrisTable;
import com.ideas.tetris.ui.common.component.window.TetrisWindow;
import com.ideas.tetris.ui.common.util.UiUtils;
import com.ideas.tetris.ui.modules.marketsegment.popupchain.DelayedPopup;
import com.ideas.tetris.ui.modules.marketsegment.ratecode.RateCodePresenter;
import com.vaadin.server.FontAwesome;
import com.vaadin.ui.Alignment;
import com.vaadin.v7.shared.ui.label.ContentMode;
import com.vaadin.v7.ui.HorizontalLayout;
import com.vaadin.v7.ui.Label;
import com.vaadin.v7.ui.Table;
import com.vaadin.v7.ui.VerticalLayout;
import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class AssignRuleConfirmationPopup extends VerticalLayout implements DelayedPopup {

    private static TetrisWindow confirmationPopup;
    private String mappedMarketCode = "";
    private RateCodePresenter presenter;
    HorizontalLayout tableHLayout = new HorizontalLayout();
    AnalyticalMarketSegmentSummary summary;
    AnalyticalMarketSegmentAttribute selectedAttribute;
    ForecastActivityType forecastActivityType;
    private boolean complimentary = false;

    public AssignRuleConfirmationPopup(RateCodePresenter assignmentPresenter, AnalyticalMarketSegmentSummary summary,
                                       AttributeDetails attributeDetails) {
        createNewAssignRuleConfirmationPopup(assignmentPresenter, summary, attributeDetails);
    }

    public AssignRuleConfirmationPopup(RateCodePresenter assignmentPresenter, AnalyticalMarketSegmentSummary summary,
                                       AttributeDetails attributeDetails, String mappedMarketCode, Product product) {
        attributeDetails.setProduct(product);
        createNewAssignRuleConfirmationPopup(assignmentPresenter, summary, attributeDetails);

        this.mappedMarketCode = StringUtils.isEmpty(mappedMarketCode) ? presenter.getNewMappedMarketCode(selectedAttribute,
                summary.getMarketCode(), MarketSegmentCasinoCategoryType.NONE, product) : mappedMarketCode;
    }

    private void createNewAssignRuleConfirmationPopup(final RateCodePresenter assignmentPresenter, final AnalyticalMarketSegmentSummary summary, final AttributeDetails attributeDetails) {
        presenter = assignmentPresenter;
        this.summary = summary;
        this.selectedAttribute = attributeDetails.getAttribute();
        this.complimentary = attributeDetails.isComplimentary();
        this.forecastActivityType = presenter.getAdvancedMappingPopup().getForecastActivityType();
        setMargin(true);
        HorizontalLayout header = new HorizontalLayout();
        header.setMargin(true);
        header.setSpacing(true);
        Label iconLabel = new Label("<span style='color:#339900;font-size: 48px;line-height: 48px;'>" + FontAwesome.CHECK.getHtml() + "</span>", ContentMode.HTML);
        iconLabel.addStyleName("tetris-messagebox-icon");
        header.addComponent(iconLabel);
        Label textLabel = new Label(UiUtils.getText("text.label"));
        header.addComponent(textLabel);
        header.setComponentAlignment(textLabel, Alignment.MIDDLE_LEFT);

        addComponent(header);
        addComponent(tableHLayout);
        setComponentAlignment(header, Alignment.MIDDLE_CENTER);
        setExpandRatio(tableHLayout, 1.0f);
        setSpacing(true);

        addSaveCancelButtonBar(assignmentPresenter, summary, attributeDetails);
    }

    public static AssignRuleConfirmationPopup showConfirmation(RateCodePresenter presenter, AnalyticalMarketSegmentSummary summary,
                                                               AnalyticalMarketSegmentAttribute attribute) {
        AssignRuleConfirmationPopup ruleConfirmationPopup = new AssignRuleConfirmationPopup(presenter, summary, new AttributeDetails(Collections.emptyList(), attribute));
        TetrisWindow tetrisWindow = new TetrisWindow(ruleConfirmationPopup.getWindowCaption());
        ruleConfirmationPopup.showPopup(tetrisWindow);
        return ruleConfirmationPopup;
    }

    private void addSaveCancelButtonBar(final RateCodePresenter assignmentPresenter, final AnalyticalMarketSegmentSummary summary, final AttributeDetails attributeDetails) {
        TetrisSaveCancelButtonBar saveCancelButtonBar = new TetrisSaveCancelButtonBar();
        saveCancelButtonBar.getSaveButton().setCaption(UiUtils.getText("ok"));

        saveCancelButtonBar.addValidSaveListener(event -> {
            try {
                if (StringUtils.isNotEmpty(this.mappedMarketCode)) {
                    summary.setMappedCode(this.mappedMarketCode);
                }
                assignmentPresenter.assignMarketSegment(Collections.singletonList(summary), attributeDetails, forecastActivityType.getId());
                if (assignmentPresenter.isIndependentProductsEnabled()) {
                    assignmentPresenter.createWebRateMappingForNewIP(attributeDetails.getProduct());
                }
            } finally {
                confirmationPopup.close();
            }
        });

        saveCancelButtonBar.getCancelButton().addClickListener(clickEvent -> {
            confirmationPopup.close();
            assignmentPresenter.resetDefaultSegments();
        });
        addComponent(saveCancelButtonBar);
        setComponentAlignment(saveCancelButtonBar, Alignment.BOTTOM_RIGHT);
    }

    private boolean isComplimentary(AMSSummaryUIWrapper wrapper) {
        return presenter.getComplimentaryFor(wrapper, this.complimentary);
    }

    private void addMarketSegmentTable(HorizontalLayout horizontalLayout, AnalyticalMarketSegmentSummary marketSegmentSummary, final AnalyticalMarketSegmentAttribute selectedAttribute) {
        List<AMSSummaryUIWrapper> summaryUIWrappers = new ArrayList<>();
        AMSSummaryUIWrapper ruleSummaryUIWrapper = new AMSSummaryUIWrapper(marketSegmentSummary, false);
        String description = selectedAttribute.getDescription();
        ruleSummaryUIWrapper.setAssignedAttributeDescriptionKey(presenter.getAttributeDescriptionText(presenter.isIndependentProductsEnabled(), description));
        summaryUIWrappers.add(ruleSummaryUIWrapper);
        summaryUIWrappers.addAll(presenter.getDefaultSegmentSummaries());

        TetrisBeanItemContainer<AMSSummaryUIWrapper> container = new TetrisBeanItemContainer<>(AMSSummaryUIWrapper.class);
        container.addAll(summaryUIWrappers);
        String marketSegmentId = "marketSegment";
        String rateCodeId = "rateCode";
        String attributeId = "assignedAttributeText";

        Table marketSegmentTable = new TetrisTable();
        marketSegmentTable.setHeight("200px");
        marketSegmentTable.setContainerDataSource(container);

        marketSegmentTable.setVisibleColumns(new String[]{marketSegmentId, rateCodeId, attributeId});
        marketSegmentTable.setColumnHeaders(UiUtils.getText("original.newline.market.segment.header"), UiUtils.getText("rate.code.newline.header"), UiUtils.getText("attributes.header"));
        marketSegmentTable.addGeneratedColumn(attributeId, (table, item, column) -> {
            AMSSummaryUIWrapper wrapper = (AMSSummaryUIWrapper) item;
            String assignedAttributeText = wrapper.getAssignedAttributeText(isComplimentary(wrapper));
            return AttributeDetails.getAttributeValue(assignedAttributeText, presenter.isIndependentProductsEnabled());
        });
        marketSegmentTable.addGeneratedColumn(marketSegmentId, (table, item, column) -> {
            AMSSummaryUIWrapper wrapper = (AMSSummaryUIWrapper) item;
            return wrapper.getMarketSegmentName();
        });

        marketSegmentTable.addGeneratedColumn(rateCodeId, (source, itemId, columnId) -> {
            AMSSummaryUIWrapper wrapper = (AMSSummaryUIWrapper) itemId;
            if (wrapper.getRateCode() != null) {
                return UiUtils.getText(marketSegmentSummary.getRateCodeType()) + " '" + wrapper.getRateCode() + "'";
            }
            return wrapper.getRateCode();
        });

        marketSegmentTable.addStyleName("two-header-row");
        horizontalLayout.addComponent(marketSegmentTable);
        horizontalLayout.setExpandRatio(marketSegmentTable, 1.0f);
    }

    @Override
    public void showPopup(TetrisWindow tetrisWindow) {
        confirmationPopup = tetrisWindow;
        addMarketSegmentTable(tableHLayout, summary, selectedAttribute);
        confirmationPopup.setWidth(800, Unit.PIXELS);
        confirmationPopup.center();
        confirmationPopup.setContent(this);
        confirmationPopup.show();
        confirmationPopup.addChangeAwareCloseListener(event -> presenter.resetDefaultSegments());
    }

    @Override
    public String getWindowCaption() {
        return UiUtils.getText("create.attribute.assignment.rule");
    }

    @Override
    public boolean continueOnClose() {
        return true;
    }
}
