package com.ideas.tetris.ui.modules.functionspace.performancetrend;


import com.ideas.tetris.pacman.services.functionspace.configuration.entity.FunctionSpaceDayPart;
import com.ideas.tetris.pacman.services.functionspace.configuration.service.FunctionSpaceConfigurationService;
import com.ideas.tetris.pacman.services.functionspace.report.dto.FunctionSpaceDetailsReportDto;
import com.ideas.tetris.pacman.services.functionspace.report.service.FunctionSpaceReportService;
import com.ideas.tetris.platform.common.xml.schema.workcontext.v1.WorkContextType;
import com.ideas.tetris.ui.common.cdi.TetrisPresenter;
import com.ideas.tetris.ui.common.data.fieldgroup.TetrisBeanFieldGroup;

import javax.inject.Inject;
import java.util.ArrayList;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import com.ideas.tetris.spring.SpringAutowired;

@SpringAutowired
public class FunctionSpacePerformanceTrendPresenter extends TetrisPresenter<FunctionSpacePerformanceTrendView, Void> {

    @Autowired
    FunctionSpaceConfigurationService configurationService;
    private TetrisBeanFieldGroup<PerformanceTrendWrapper> fieldGroup = new TetrisBeanFieldGroup<PerformanceTrendWrapper>(PerformanceTrendWrapper.class);

    @Autowired
    FunctionSpaceReportService reportService;

    @Override
    public void onViewInit() {
        PerformanceTrendWrapper performanceTrendWrapper = new PerformanceTrendWrapper();
        performanceTrendWrapper.setStartDate(getSystemDateAsLocalDate());
        performanceTrendWrapper.setEndDate(getSystemDateAsLocalDate().plusMonths(1));

        fieldGroup.setItemDataSource(performanceTrendWrapper);
    }

    @Override
    public void onViewOpened(Void aVoid) {
        init();
    }

    @Override
    public void onWorkContextChange(WorkContextType workContextType) {
        init();
        view.reLodData();
    }

    private void init() {
        List<FunctionSpaceDayPart> dayParts = configurationService.getDayParts();
        view.setContainerDataSource(dayParts);
    }

    public ArrayList<FunctionSpaceDetailsReportDtoUiWrapper> getData(boolean isUtilizationTab) {
        List<FunctionSpaceDetailsReportDto> functionSpaceDetailsReportDtos;
        PerformanceTrendWrapper bean = fieldGroup.getBean();
        FunctionSpaceDayPart dayPart = bean.getDayPart();
        if (isUtilizationTab && dayPart != null) {
            functionSpaceDetailsReportDtos = reportService.buildFunctionSpaceReportDtoForDayPart(bean.getStartDate(), bean.getEndDate(), dayPart.getId());
        } else {
            functionSpaceDetailsReportDtos = reportService.buildFunctionSpaceDetailsReportDto(bean.getStartDate(), bean.getEndDate());
        }
        ArrayList<FunctionSpaceDetailsReportDtoUiWrapper> wrapperArrayList = new ArrayList<FunctionSpaceDetailsReportDtoUiWrapper>();
        for (FunctionSpaceDetailsReportDto functionSpaceDetailsReportDto : functionSpaceDetailsReportDtos) {
            wrapperArrayList.add(new FunctionSpaceDetailsReportDtoUiWrapper(functionSpaceDetailsReportDto));
        }
        return wrapperArrayList;
    }

    public TetrisBeanFieldGroup<PerformanceTrendWrapper> getFieldGroup() {
        return fieldGroup;
    }
}
