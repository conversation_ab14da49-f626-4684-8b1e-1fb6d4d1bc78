package com.ideas.tetris.ui.modules.functionspace.demandcalendar.calendar;

import com.ideas.tetris.pacman.services.functionspace.configuration.entity.FunctionSpaceDayPart;
import com.ideas.tetris.pacman.services.functionspace.configuration.entity.FunctionSpaceFunctionRoom;
import com.ideas.tetris.pacman.services.functionspace.dto.FunctionSpaceDemandCalendarDatePartDto;
import com.ideas.tetris.pacman.services.functionspace.dto.FunctionSpaceDemandCalendarRoomSummaryDto;
import com.ideas.tetris.pacman.services.specialevent.dto.SpecialEventSummaryDto;
import com.ideas.tetris.ui.common.data.util.converter.ScaleAwareStringToBigDecimalConverter;
import com.ideas.tetris.ui.common.util.UiUtils;
import com.ideas.tetris.ui.modules.functionspace.demandcalendar.bookingsummary.RowData;
import org.apache.commons.lang.StringUtils;
import org.joda.time.LocalDate;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class FunctionSpaceDetailsUiWrapper {
    private final FunctionSpaceDemandCalendarRoomSummaryDto roomDto;
    List<RowData> utilizationForecast;
    List<RowData> actualOccupancy;
    List<RowData> forecast;
    List<SpecialEventSummaryDto> specialEvents;
    List<RowData> indivisibleRooms;
    int indivisibleRoomsCount;

    public FunctionSpaceDetailsUiWrapper(List<FunctionSpaceDemandCalendarDatePartDto> dayParts, List<SpecialEventSummaryDto> specialEvents, FunctionSpaceDemandCalendarRoomSummaryDto roomDto, Map<FunctionSpaceDayPart, List<FunctionSpaceFunctionRoom>> functionSpaceRoomsBooked, boolean isLastYear, LocalDate systemDate) {
        utilizationForecast = new ArrayList<RowData>();
        for (FunctionSpaceDemandCalendarDatePartDto dayPart : dayParts) {
            boolean isBeforeSystemDate = isOccupancyDateBeforeSystemDate(dayPart.getOccupancyDate(), systemDate);
            utilizationForecast.add(new RowData(dayPart.getDayPartName(),
                    getPercent(determineUtilization(isBeforeSystemDate ? dayPart.getOnBooksUtilization() : dayPart.getForecastUtilization(), dayPart.getUserUtilization())),
                    dayPart.getFunctionSpaceDemandCalendarDateStatus() == null ? "" : UiUtils.getText(dayPart.getFunctionSpaceDemandCalendarDateStatus().toString()),
                    dayPart.getUserUtilization() != null
            ));
        }
        this.specialEvents = (specialEvents == null) ? new ArrayList<SpecialEventSummaryDto>() : specialEvents;

        actualOccupancy = new ArrayList<RowData>();
        forecast = new ArrayList<RowData>();
        actualOccupancy.add(new RowData(UiUtils.getText("group"), isLastYear ? getPlainValue(roomDto.getLastYearForecastGroupOccupancy()) : getPlainValue(roomDto.getActualGroupOccupancy())));
        actualOccupancy.add(new RowData(UiUtils.getText("common.transient"), isLastYear ? getPlainValue(roomDto.getLastYearForecstTransientOccupancy()) : getPlainValue(roomDto.getActualTransientOccupancy())));
        actualOccupancy.add(new RowData(UiUtils.getText("common.total"), isLastYear ? getTotalPercent(roomDto.getLastYearTotalForecastOccupancy(), roomDto.getLastYearTotalForecastOccupancyPct())
                : getTotalPercent(roomDto.getTotalActualOccupancy(), roomDto.getTotalActualOccupancyPct())));

        forecast.add(new RowData(UiUtils.getText("group"), isLastYear ? getPlainValue(roomDto.getLastYearActualGroupOccupancy()) : getPlainValue(roomDto.getForecastGroupOccupancy())));
        forecast.add(new RowData(UiUtils.getText("common.transient"), isLastYear ? getPlainValue(roomDto.getLastYearActualTransientOccupancy()) : getPlainValue(roomDto.getForecstTransientOccupancy())));
        forecast.add(new RowData(UiUtils.getText("common.total"), isLastYear ? getTotalPercent(roomDto.getLastYearTotalActualOccupancy(), roomDto.getLastYearTotalActualOccupancyPct())
                : getTotalPercent(roomDto.getTotalForecastOccupancy(), roomDto.getTotalForecastOccupancyPct())));

        this.roomDto = roomDto;

        this.indivisibleRooms = new ArrayList<RowData>();
        for (FunctionSpaceDayPart functionSpaceDayPart : functionSpaceRoomsBooked.keySet()) {
            List<FunctionSpaceFunctionRoom> functionSpaceFunctionRooms = functionSpaceRoomsBooked.get(functionSpaceDayPart)
                    .stream()
                    .sorted(Comparator.comparing(FunctionSpaceFunctionRoom::getName))
                    .collect(Collectors.toList());
            String rooms="";
            for (FunctionSpaceFunctionRoom functionSpaceFunctionRoom : functionSpaceFunctionRooms) {
                rooms+= functionSpaceFunctionRoom.getName()+", ";
            }
            indivisibleRooms.add(new RowData(functionSpaceDayPart.getName(), StringUtils.chop(rooms.trim())));
            indivisibleRoomsCount += functionSpaceFunctionRooms.size();
        }
    }

    private boolean isOccupancyDateBeforeSystemDate(LocalDate occupancyDate, LocalDate systemDate) {
        return occupancyDate.isBefore(systemDate);
    }

    private BigDecimal determineUtilization(BigDecimal forecastUtilization, BigDecimal userUtilization) {
        return (userUtilization != null) ? userUtilization : forecastUtilization;
    }

    private String getTotalPercent(BigDecimal total, BigDecimal percent) {
        return getPlainValue(total) + "(" + getPercent(percent) + "%)";
    }

    private String getPlainValue(BigDecimal value) {
        return new ScaleAwareStringToBigDecimalConverter().convertToPresentation(value, String.class, UiUtils.getLocale());
    }

    private String getPercent(BigDecimal value) {
        BigDecimal num = getBigDecimal(value).multiply(new BigDecimal(100));
        return new ScaleAwareStringToBigDecimalConverter().convertToPresentation(num, String.class, UiUtils.getLocale());
    }

    private BigDecimal getBigDecimal(BigDecimal b) {
        if (b == null) {
            return BigDecimal.ZERO;
        }
        return b;
    }

    public List<RowData> getUtilizationForecast() {
        return utilizationForecast;
    }

    public void setUtilizationForecast(List<RowData> utilizationForecast) {
        this.utilizationForecast = utilizationForecast;
    }

    public List<RowData> getActualOccupancy() {
        return actualOccupancy;
    }

    public void setActualOccupancy(List<RowData> actualOccupancy) {
        this.actualOccupancy = actualOccupancy;
    }

    public List<RowData> getForecast() {
        return forecast;
    }

    public void setForecast(List<RowData> forecast) {
        this.forecast = forecast;
    }

    public List<SpecialEventSummaryDto> getSpecialEvents() {
        return specialEvents;
    }

    public void setSpecialEvents(List<SpecialEventSummaryDto> specialEvents) {
        this.specialEvents = specialEvents;
    }

    public List<RowData> getIndivisibleRooms() {
        return indivisibleRooms;
    }

    public void setIndivisibleRooms(List<RowData> indivisibleRooms) {
        this.indivisibleRooms = indivisibleRooms;
    }

    public FunctionSpaceDemandCalendarRoomSummaryDto getRoomDto() {
        return roomDto;
    }

    public int getIndivisibleRoomsCount() {
        return indivisibleRoomsCount;
    }
}
