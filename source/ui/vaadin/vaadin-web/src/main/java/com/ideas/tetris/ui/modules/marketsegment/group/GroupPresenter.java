package com.ideas.tetris.ui.modules.marketsegment.group;

import com.ideas.tetris.pacman.services.marketsegment.entity.ForecastActivityType;
import com.ideas.tetris.pacman.services.marketsegment.entity.ProcessStatus;
import com.ideas.tetris.pacman.services.marketsegment.service.AnalyticalMarketSegmentService;
import com.ideas.tetris.pacman.services.marketsegment.service.AnalyticalMarketSegmentSummary;
import com.ideas.tetris.pacman.services.marketsegment.service.RateCodeSummary;
import com.ideas.tetris.pacman.services.marketsegment.service.proposedforecastgroup.service.ProposedForecastGroupService;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import com.ideas.tetris.ui.common.cdi.TetrisPresenter;
import com.ideas.tetris.ui.common.util.UiUtils;
import com.ideas.tetris.ui.modules.marketsegment.common.AMSSummaryUIWrapper;
import com.ideas.tetris.ui.modules.marketsegment.common.AssignAware;
import com.ideas.tetris.ui.modules.marketsegment.common.AssignMarketSegmentEvent;
import com.ideas.tetris.ui.modules.marketsegment.common.AttributeAssignmentComponentAware;
import com.ideas.tetris.ui.modules.marketsegment.common.AttributeAssignmentMockData;
import com.ideas.tetris.ui.modules.marketsegment.common.AttributeDetails;

import javax.enterprise.event.Event;
import javax.inject.Inject;
import java.util.ArrayList;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import com.ideas.tetris.spring.SpringAutowired;

@SpringAutowired
public class GroupPresenter extends TetrisPresenter<GroupView, String> implements AssignAware, AttributeAssignmentComponentAware {

    @Inject
    Event<AssignMarketSegmentEvent> assignMarketSegmentEventEvent;

    @Autowired
    AnalyticalMarketSegmentService analyticalMarketSegmentService;
    @Inject
    AttributeAssignmentMockData mockData;
    private List<AnalyticalMarketSegmentSummary> groupMarketSegments;
    @Autowired
	private PacmanConfigParamsService configService;
    @Autowired
	private ProposedForecastGroupService proposedForecastGroupService;

    @Override
    public void onViewOpened(String permission) {
        loadData();
        view.updatePermission(permission);
    }

    private void loadData() {
        if (groupMarketSegments == null) {
            if (mockModeEnabled) {
                groupMarketSegments = mockData.getGroupMarketSegments();
            } else {
                groupMarketSegments = analyticalMarketSegmentService.getUnassignedGroupMarketSegments();
            }
            view.updateTable(wrapMarketSegments(groupMarketSegments));
        }
    }

    private List<AMSSummaryUIWrapper> wrapMarketSegments(List<AnalyticalMarketSegmentSummary> groupMarketSegments) {
        ArrayList<AMSSummaryUIWrapper> amsSummaryUIWrappers = new ArrayList<AMSSummaryUIWrapper>();
        for (AnalyticalMarketSegmentSummary groupMarketSegment : groupMarketSegments) {
            AMSSummaryUIWrapper amsSummaryUIWrapper = new AMSSummaryUIWrapper(groupMarketSegment, false);
            amsSummaryUIWrappers.add(amsSummaryUIWrapper);
        }

        return amsSummaryUIWrappers;
    }

    public void reload() {
        groupMarketSegments = null;
        loadData();
    }

    public void assignMarketSegment(AttributeDetails attributeDetails, int block) {
        ArrayList<AnalyticalMarketSegmentSummary> analyticalMarketSegmentSummaries = new ArrayList<>();

        for (AMSSummaryUIWrapper amsSummary : attributeDetails.getSelectedItems()) {
            analyticalMarketSegmentSummaries.add(amsSummary.getSummary());
        }

        assignMarketSegment(analyticalMarketSegmentSummaries, attributeDetails);
    }

    public void assignMarketSegment(List<AnalyticalMarketSegmentSummary> marketSegmentSummaryList, AttributeDetails attributeDetails) {
        if (mockModeEnabled) {
            mockData.assignMarketSegments(marketSegmentSummaryList, attributeDetails.getAttribute());
        } else {
            analyticalMarketSegmentService.assignMarketSegments(marketSegmentSummaryList, attributeDetails.getAttribute(),
                    attributeDetails.getForecastActivityType().getId(), false, null);
        }

        assignMarketSegmentEventEvent.fire(new AssignMarketSegmentEvent());
        loadData();
    }

    public void resetData() {
        groupMarketSegments = null;
    }

    public List<RateCodeSummary> getRateCodes(String marketCode) {
        if (mockModeEnabled) {
            return mockData.getRateCodes(marketCode);
        }
        return analyticalMarketSegmentService.getRateCodes(marketCode);

    }

    public List<ForecastActivityType> getForecastActivityTypes() {
        return analyticalMarketSegmentService.getForecastActivityTypes();
    }

    @Override
    public String getLowVolumeWarningMessage() {
        return UiUtils.getText("attribute.assignment.warning.lowVolume", configService.getBigDecimalParameterValue("pacman.feature.lowVolumeRateCode"),
                configService.getBigDecimalParameterValue("pacman.feature.lowVolumeMarketSegment"));
    }

    @Override
    public boolean isSelectionEmpty() {
        return view.getCheckboxColumnSupport().getSelectedItems().isEmpty();
    }

    @Override
    public boolean hasUncommittedForecastGroup() {
        return proposedForecastGroupService.hasProcessStatus(ProcessStatus.SUBMITTED_TO_SAS);
    }
}
