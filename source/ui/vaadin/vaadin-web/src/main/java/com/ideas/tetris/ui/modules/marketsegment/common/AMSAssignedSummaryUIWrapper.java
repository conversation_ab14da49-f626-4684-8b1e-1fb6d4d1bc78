package com.ideas.tetris.ui.modules.marketsegment.common;

import com.ideas.tetris.pacman.services.marketsegment.entity.ForecastActivityType;
import com.ideas.tetris.pacman.services.marketsegment.service.AnalyticalMarketSegment;
import com.ideas.tetris.pacman.services.marketsegment.service.AnalyticalMarketSegmentAttribute;
import com.ideas.tetris.pacman.services.marketsegment.service.RateCodeTypeEnum;
import com.ideas.tetris.pacman.services.product.Product;
import com.ideas.tetris.ui.common.data.hierarchical.HierarchicalWithParent;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import static com.ideas.tetris.ui.modules.marketsegment.MarketSegmentDetailColumnRenderer.renderAttributeColumn;

public class AMSAssignedSummaryUIWrapper implements HierarchicalWithParent<AMSAssignedSummaryUIWrapper> {
    private LocalDateTime lastUpdatedDate;
    private RateCodeTypeEnum rateCodeType;
    private AMSAssignedSummaryUIWrapper parent;
    private List<AMSAssignedSummaryUIWrapper> children = new ArrayList<>();

    private AnalyticalMarketSegment analyticalMarketSegment;
    private String mappedCode;
    private String marketSegment;
    private String rateCode;
    private ForecastActivityType forecastActivityType;
    private AnalyticalMarketSegmentAttribute attribute;
    private boolean complimentary;
    private boolean preserved;
    private Product baseProduct;

    public AMSAssignedSummaryUIWrapper() {
    }

    public AMSAssignedSummaryUIWrapper(AnalyticalMarketSegment analyticalMarketSegment) {
        this.analyticalMarketSegment = analyticalMarketSegment;
        marketSegment = analyticalMarketSegment.getMarketCode();
        rateCode = analyticalMarketSegment.getRateCode();
        mappedCode = analyticalMarketSegment.getMappedMarketCode();
        attribute = analyticalMarketSegment.getAttribute();
        rateCodeType = analyticalMarketSegment.getRateCodeType();
        lastUpdatedDate = analyticalMarketSegment.getLastUpdatedDate();
        complimentary = analyticalMarketSegment.isComplimentary();
        preserved = analyticalMarketSegment.isPreserved();
    }

    public AnalyticalMarketSegment getAnalyticalMarketSegment() {
        return analyticalMarketSegment;
    }

    public String getMappedCode() {
        return mappedCode;
    }

    public String getMarketSegment() {
        return marketSegment;
    }

    public String getRateCode() {
        return rateCode;
    }

    public String getAttributeDescription() {
        return renderAttributeColumn(attribute, baseProduct != null, analyticalMarketSegment.isComplimentary());
    }

    public void setAnalyticalMarketSegment(AnalyticalMarketSegment analyticalMarketSegment) {
        this.analyticalMarketSegment = analyticalMarketSegment;
    }

    public void setMappedCode(String mappedCode) {
        this.mappedCode = mappedCode;
    }

    public void setMarketSegmentCode(String marketSegmentCode) {
        this.marketSegment = marketSegmentCode;
    }

    public void setRateCode(String rateCode) {
        this.rateCode = rateCode;
    }

    @Override
    public AMSAssignedSummaryUIWrapper getParent() {
        return parent;
    }

    public void setParent(AMSAssignedSummaryUIWrapper parent) {
        this.parent = parent;
    }

    @Override
    public List<AMSAssignedSummaryUIWrapper> getChildren() {
        return children;
    }

    public void setChildren(List<AMSAssignedSummaryUIWrapper> children) {
        this.children = children;
    }

    @Override
    public boolean hasChildren() {
        return !children.isEmpty();
    }

    public RateCodeTypeEnum getRateCodeType() {
        return rateCodeType;
    }

    public void setRateCodeType(RateCodeTypeEnum rateCodeType) {
        this.rateCodeType = rateCodeType;
    }

    public LocalDateTime getLastUpdatedDate() {
        return lastUpdatedDate;
    }

    public void setLastUpdatedDate(LocalDateTime lastUpdatedDate) {
        this.lastUpdatedDate = lastUpdatedDate;
    }

    public AnalyticalMarketSegmentAttribute getAttribute() {
        return attribute;
    }

    public void setAttribute(AnalyticalMarketSegmentAttribute attribute) {
        this.attribute = attribute;
    }

    public ForecastActivityType getForecastActivityType() {
        return forecastActivityType;
    }

    public void setForecastActivityType(ForecastActivityType forecastActivityType) {
        this.forecastActivityType = forecastActivityType;
    }

    public boolean isComplimentary() {
        return complimentary;
    }

    public boolean isPreserved() {
        return preserved;
    }

    public Product getBaseProduct() {
        return baseProduct;
    }

    public void setBaseProduct(Product baseProduct) {
        this.baseProduct = baseProduct;
    }
}
