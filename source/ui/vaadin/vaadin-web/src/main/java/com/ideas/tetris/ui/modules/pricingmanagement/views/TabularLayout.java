package com.ideas.tetris.ui.modules.pricingmanagement.views;

import com.ideas.infra.tetris.security.domain.TetrisPermissionKey;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.services.accommodation.dto.AccomTypeSummary;
import com.ideas.tetris.pacman.services.bestavailablerate.dto.BARDecisionInfo;
import com.ideas.tetris.pacman.services.bestavailablerate.dto.BARDetails;
import com.ideas.tetris.pacman.services.bestavailablerate.dto.BAROverride;
import com.ideas.tetris.pacman.services.bestavailablerate.dto.CompetitorInfo;
import com.ideas.tetris.pacman.services.manualrestrictions.control.dto.ManualRestrictionRoomTypeIdentifier;
import com.ideas.tetris.pacman.services.manualrestrictions.control.dto.ManualRestrictionTypeDto;
import com.ideas.tetris.pacman.services.notes.entity.DateNote;
import com.ideas.tetris.pacman.services.unqualifiedrate.entity.RateUnqualifiedAccomClass;
import com.ideas.tetris.platform.common.Justification;
import com.ideas.tetris.platform.common.time.LocalDateInterval;
import com.ideas.tetris.ui.common.component.button.TetrisImageButton;
import com.ideas.tetris.ui.common.component.fontawesome.TetrisFontAwesome;
import com.ideas.tetris.ui.common.component.label.TetrisSpacer;
import com.ideas.tetris.ui.common.component.layouts.TetrisLoadingLayout;
import com.ideas.tetris.ui.common.component.note.TetrisNoteImageButton;
import com.ideas.tetris.ui.common.component.textfield.TetrisDateField;
import com.ideas.tetris.ui.common.data.util.converter.ScaleAwareStringToBigDecimalConverter;
import com.ideas.tetris.ui.common.data.util.converter.StringToBigDecimalConverter;
import com.ideas.tetris.ui.common.util.FormatterUtil;
import com.ideas.tetris.ui.common.util.UiUtils;
import com.ideas.tetris.ui.modules.investigator.InvestigatorDto;
import com.ideas.tetris.ui.modules.investigator.InvestigatorUtil;
import com.ideas.tetris.ui.modules.pricingmanagement.PricingManagementPresenter;
import com.ideas.tetris.ui.modules.pricingmanagement.PricingManagementView;
import com.vaadin.server.Page;
import com.vaadin.server.Responsive;
import com.vaadin.ui.Alignment;
import com.vaadin.ui.Button;
import com.vaadin.ui.Component;
import com.vaadin.ui.CssLayout;
import com.vaadin.ui.Layout;
import com.vaadin.ui.PopupView;
import com.vaadin.ui.UI;
import com.vaadin.v7.data.Item;
import com.vaadin.v7.data.Validator;
import com.vaadin.v7.data.util.IndexedContainer;
import com.vaadin.v7.shared.ui.label.ContentMode;
import com.vaadin.v7.ui.ComboBox;
import com.vaadin.v7.ui.HorizontalLayout;
import com.vaadin.v7.ui.Label;
import com.vaadin.v7.ui.Table;
import com.vaadin.v7.ui.VerticalLayout;
import com.vaadin.v7.ui.themes.Reindeer;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.joda.time.LocalDate;

import java.math.BigDecimal;
import java.text.NumberFormat;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

@SuppressWarnings("squid:S1948")
@Justification("Following warning generally does not apply for Vaadin: Fields in a \"Serializable\" class should either be transient or serializable")
public class TabularLayout extends VerticalLayout {

    public static final String DAY_OF_WEEK = "report.dow";
    public static final String DATES_AND_DETAILS = "dates.and.details";
    public static final String OOO = "ooo";
    public static final String OOB = "roomsOnBooks";
    public static final String OF = "of";
    public static final String LRV = "LRV";
    public static final String OVERRIDES = "roa.overrides";
    public static final String BAR = "BAR";
    public static final String LOS = "LOS";
    public static final String COMPETITOR = "Competitor";
    private final TetrisLoadingLayout tabularLoadingLayout;

    private Table dataTable;
    private TetrisDateField tableStartDateSelector, tableEndDateSelector;
    private ComboBox tableRoomClassSelector;

    private PricingManagementPresenter presenter;
    private PricingManagementTableFilter tableFilter;
    private PricingManagementTableDisplayCriteriaFilter displayCriteriaFilter;
    private IndexedContainer tableDataSource;

    private final boolean lraEnabled;
    private final boolean enableSingleBarDecision;
    private final boolean webRateShoppingEnabled;
    private final boolean closeLV0Enabled;
    private DisplayCriteria displayCriteria;
    private Date startDate, endDate;
    private final String userDateFormat;

    private NumberFormat formatter;
    private static final Logger logger = Logger.getLogger(TabularLayout.class);
    private boolean isBarValueSelected;

    public TabularLayout(PricingManagementPresenter presenter, PricingManagementView parent) {
        addStyleName("tabular-layout");
        addStyleAtPage();
        setSizeFull();
        setSpacing(true);
        setMargin(true);
        formatter = NumberFormat.getInstance(UiUtils.getLocale());
        formatter.setMaximumFractionDigits(2);
        formatter.setMinimumFractionDigits(2);
        formatter.setGroupingUsed(true);

        this.presenter = presenter;
        this.isBarValueSelected = parent.isBarValueSelected();
        userDateFormat = presenter.getUserDateFormat();
        lraEnabled = presenter.getParameterDTO().isLRAEnabled();
        webRateShoppingEnabled = presenter.getParameterDTO().isWebRateShoppingEnabled();
        enableSingleBarDecision = presenter.getParameterDTO().isEnableSingleBarDecision();
        closeLV0Enabled = presenter.getParameterDTO().isCloseLVOEnabled();
        CssLayout tableControlsLayout = new CssLayout();
        tableControlsLayout.setId("tableControlsLayout");
        tableRoomClassSelector = buildRoomClassSelectComponent();
        tableRoomClassSelector.setRequired(false);
        tableStartDateSelector = new TetrisDateField();
        tableStartDateSelector.setTextFieldEnabled(false);
        tableStartDateSelector.setRequired(false);
        tableEndDateSelector = new TetrisDateField();
        tableEndDateSelector.setTextFieldEnabled(false);
        tableEndDateSelector.setRequired(false);
        tableRoomClassSelector.addValueChangeListener(event -> {
            if (PricingManagementView.ViewMode.TABLE_MODE.equals(parent.getCurrentViewMode())) {
                parent.setLeftRoomClassValue((RateUnqualifiedAccomClass) event.getProperty().getValue());
                updateTableData();
            }
        });
        tableStartDateSelector.addValueChangeListener(event -> {
            startValueChanged((Date) event.getProperty().getValue());
            if (PricingManagementView.ViewMode.TABLE_MODE.equals(parent.getCurrentViewMode())) {
                if (checkStartDateAndEndDatesAreSet()) {
                    updateTableData(tableStartDateSelector.getValue(), tableEndDateSelector.getValue(), true);
                }
            }
        });
        tableEndDateSelector.addValueChangeListener(event -> {
            endValueChanged((Date) event.getProperty().getValue());
            if (PricingManagementView.ViewMode.TABLE_MODE.equals(parent.getCurrentViewMode())) {
                if (checkStartDateAndEndDatesAreSet()) {
                    updateTableData(tableStartDateSelector.getValue(), tableEndDateSelector.getValue(), true);
                }
            }
        });

        Label roomClassCaption = new Label(UiUtils.getText("roomClass") + ":");
        Label startDateCaption = new Label(UiUtils.getText("roomtype.table.header.startdate") + ":");
        Label endDateCaption = new Label(UiUtils.getText("roomtype.table.header.enddate") + ":");
        tableControlsLayout.addComponents(roomClassCaption, startDateCaption, endDateCaption,
                tableRoomClassSelector, tableStartDateSelector, tableEndDateSelector);
        createTable();
        tabularLoadingLayout = new TetrisLoadingLayout();
        tabularLoadingLayout.setId("tabularLoadingLayout");
        Responsive.makeResponsive(tabularLoadingLayout);
        tabularLoadingLayout.setMainContent(dataTable);
        tabularLoadingLayout.setComponentAlignment(dataTable, Alignment.TOP_CENTER);
        addComponents(tableControlsLayout, tabularLoadingLayout);
        setComponentAlignment(tableControlsLayout, Alignment.TOP_CENTER);
        setComponentAlignment(tabularLoadingLayout, Alignment.TOP_CENTER);
        setExpandRatio(tabularLoadingLayout, 1);
        buildTableColumns();
        final LocalDate systemDate = presenter.getSystemDateAsLocalDate();
        dataTable.setCellStyleGenerator(new Table.CellStyleGenerator() {
            @Override
            public String getStyle(Table source, Object itemId, Object propertyId) {
                if (propertyId == null && systemDate.isAfter((LocalDate) itemId)) {
                    return "past-date-row";
                } else if (propertyId != null && propertyId instanceof String
                        && (((String) propertyId).startsWith(LOS) || ((String) propertyId).startsWith(BAR))) {
                    if (DayFilter.BAR_EQ.equals(displayCriteria.getDayFilterValue()) &&
                            barValueMatches((String) propertyId, displayCriteria.getSelectedBARValue(), ((LocalDate) itemId))) {
                        return "matching-bar-value";
                    } else {
                        return "los-cell";
                    }
                } else {
                    return null;
                }
            }
        });
    }

    private boolean checkStartDateAndEndDatesAreSet() {
        return null != tableStartDateSelector.getValue() && null != tableEndDateSelector.getValue();
    }

    public void addStyleAtPage() {
        Page.getCurrent().getStyles().add(".tetris .v-scrollable {overflow-y: auto !important; overflow-x: auto !important; font-size: 0; line-height: normal;}");
    }

    private boolean barValueMatches(String los, String selectedBARValue, LocalDate localDate) {
        Date date = localDate.toDate();
        if (presenter.getParameterDTO().isBARByLOS()) {
            if (displayCriteriaFilter.getBarDecisionsByLos() == null || !displayCriteriaFilter.getBarDecisionsByLos().containsKey(date)) {
                return false;
            }
            Map<Integer, BARDecisionInfo> losToDecisionMap = displayCriteriaFilter.getBarDecisionsByLos().get(date);
            if (losToDecisionMap != null) {
                BARDecisionInfo barDecisionInfo = losToDecisionMap.get(Integer.parseInt(los.replace(LOS, "")));
                if (barDecisionInfo != null && selectedBARValue.equals(barDecisionInfo.getRatePlanName())) {
                    return true;
                }
            }
            return false;
        } else {
            if (displayCriteriaFilter.getBarDecisions() == null || !displayCriteriaFilter.getBarDecisions().containsKey(date)) {
                return false;
            }
            return selectedBARValue.equals(displayCriteriaFilter.getBarDecisions().get(date).getRatePlanName());
        }
    }

    private void endValueChanged(final Date endValue) {
        endDate = endValue;
        resetStartDateValidator(endValue);
    }

    private void resetStartDateValidator(Date endValue) {
        if (endValue == null) {
            return;
        }
        tableStartDateSelector.removeAllValidators();
        tableStartDateSelector.addValidator((Validator) startValue -> {
            Date date = (Date) startValue;
            if (date.compareTo(endValue) > 0) {
                throw new Validator.InvalidValueException(UiUtils.getText("dateStartEndErrorMessage"));
            }
        });
        if (!tableStartDateSelector.isValid()) {
            tableEndDateSelector.setValue(tableStartDateSelector.getValue());
        }
    }

    private void startValueChanged(Date value) {
        startDate = value;
        tableEndDateSelector.setRangeStart(value);
        resetStartDateValidator(tableEndDateSelector.getValue());
    }

    private void buildTableColumns() {
        boolean isBARByLOS = presenter.getParameterDTO().isBARByLOS();
        dataTable.addContainerProperty(DATES_AND_DETAILS, Component.class, null);
        dataTable.setColumnHeader(DATES_AND_DETAILS, UiUtils.getText(DATES_AND_DETAILS));
        dataTable.addContainerProperty(OOO, Integer.class, 0);
        dataTable.setColumnHeader(OOO, UiUtils.getText(OOO));
        dataTable.setColumnAlignment(OOO, Table.Align.CENTER);
        dataTable.addContainerProperty(OOB, BigDecimal.class, BigDecimal.ZERO);
        dataTable.setColumnHeader(OOB, UiUtils.getText(OOB));
        dataTable.setColumnAlignment(OOB, Table.Align.CENTER);
        dataTable.addContainerProperty(OF, String.class, "0%");
        dataTable.setColumnHeader(OF, UiUtils.getText(OF));
        dataTable.setColumnAlignment(OF, Table.Align.RIGHT);
        dataTable.addContainerProperty(LRV, BigDecimal.class, BigDecimal.ZERO);
        if (isBARByLOS) {
            dataTable.setColumnHeader(LRV, UiUtils.getText("LRV.LOS1"));
        } else {
            dataTable.setColumnHeader(LRV, UiUtils.getText(LRV));
        }
        dataTable.setConverter(LRV, new StringToBigDecimalConverter());
        dataTable.setColumnAlignment(LRV, Table.Align.RIGHT);
        if (isBARByLOS) {
            List<Integer> losList = presenter.getLOSList();
            List<Integer> filteredLOSList = null;
            if (displayCriteria != null) {
                filteredLOSList = displayCriteria.getFilteredLOSList();
            }
            List<String> filteredPropertyIds = new ArrayList<>();
            for (Integer los : losList) {
                String propertyId = LOS + los;
                if (filteredLOSList != null && filteredLOSList.contains(los)) {
                    filteredPropertyIds.add(propertyId);
                }
                dataTable.addContainerProperty(propertyId, Component.class, null);
                dataTable.setColumnHeader(propertyId, UiUtils.getText("los") + " " + los);
            }
            dataTable.setColumnWidth(DATES_AND_DETAILS, 150);
            setLOSColumnsVisibility(filteredPropertyIds);

        } else {
            dataTable.addContainerProperty(BAR, Component.class, null);
            dataTable.setColumnHeader(BAR, UiUtils.getText(BAR));
            dataTable.setColumnAlignment(BAR, Table.Align.LEFT);
            dataTable.addContainerProperty(COMPETITOR, Component.class, null);
            dataTable.setColumnHeader(COMPETITOR, COMPETITOR);
        }
    }

    private void setLOSColumnsVisibility(List<String> filteredPropertyIds) {
        List columnsList = new ArrayList<>();
        for (Object col : dataTable.getContainerPropertyIds()) {
            if (!filteredPropertyIds.contains(col)) {
                columnsList.add(col);
            }
        }
        dataTable.setVisibleColumns(columnsList.toArray());
    }

    private boolean hasFilter() {
        return tableFilter != null && tableFilter.getSelectedValues() != null && !tableFilter
                .getSelectedValues().isEmpty();
    }

    public void updateTableData(Date start, Date end, boolean updateSavedDates) {
        setStartDate(start, updateSavedDates);
        setEndDate(end, updateSavedDates);

        if (hasFilter()) {
            tableDataSource.removeContainerFilter(tableFilter);
        }
        tableDataSource.removeContainerFilter(displayCriteriaFilter);
        RateUnqualifiedAccomClass selectedRoomClass = getSelectedRoomClass();
        if (selectedRoomClass == null) {
            return;
        }

        displayCriteriaFilter.setBarDecisions(null);
        displayCriteriaFilter.setBarDecisionsByLos(null);
        displayCriteriaFilter.setDailyPriceMaps(null);
        displayCriteriaFilter.setBarDecisionsByLos(null);
        displayCriteriaFilter.setEarlierBarDecisions(null);
        displayCriteriaFilter.setEarlierBarDecisionsByLos(null);
        displayCriteriaFilter.setCompetitorInfo(null);
        displayCriteriaFilter.setCompetitorInfosByLos(null);
        displayCriteriaFilter.setPropertyManualRestrictions(null);
        displayCriteriaFilter.setAccomManualRestrictions(null);

        presenter.loadTabularData(displayCriteriaFilter, displayCriteria, start, end, selectedRoomClass
                .getAccomClassId(), tabularLoadingLayout);

    }

    public boolean hasData() {
        return tableDataSource.size() > 0;
    }

    private void fetchPendingOverrides(boolean isBARByLOS) {
        displayCriteriaFilter.setPendingBAROverridesAsDateMap(null);
        displayCriteriaFilter.setPendingBAROverridesAsDateToLOSMap(null);
        if (isBARByLOS) {
            displayCriteriaFilter.setPendingBAROverridesAsDateToLOSMap(presenter
                    .getPendingBAROverridesAsDateToLOSMap());
        } else {
            displayCriteriaFilter.setPendingBAROverridesAsDateMap(presenter.getPendingBAROverridesAsDateMap());
        }
        tableFilter.setPendingOverrideMaps(displayCriteriaFilter.getPendingBAROverridesAsDateToLOSMap(),
                displayCriteriaFilter.getPendingBAROverridesAsDateMap());
    }

    private void updateBARColumns(boolean isBARByLOS, LocalDate day) {
        Date dayAsDate = day.toDate();
        Item dayItem = dataTable.getItem(day);
        if (dayItem == null) {
            return;
        }

        // BAR
        if (isBARByLOS) {
            Map<Integer, BAROverride> losToPendingOverrideMap = displayCriteriaFilter
                    .getPendingBAROverridesAsDateToLOSMap().get(dayAsDate);
            if (losToPendingOverrideMap == null) {
                losToPendingOverrideMap = new HashMap<>();
            }
            handleBarByLosColumnValues(displayCriteriaFilter.getBarDecisionsByLos(), displayCriteriaFilter.getWashOverrides(),
                    displayCriteriaFilter.getDailyPriceMapsPerLos(), dayAsDate,
                    dayItem,
                    losToPendingOverrideMap, displayCriteriaFilter.getConflictingOverrides(), displayCriteriaFilter.getPropertyManualRestrictions(), displayCriteriaFilter.getAccomManualRestrictions());
        } else {
            handleBarByDayColumnValue(displayCriteriaFilter.getDailyPriceMaps(), displayCriteriaFilter.getWashOverrides(),
                    displayCriteriaFilter.getCompetitorInfo(), day,
                    displayCriteriaFilter.getBarDecisions().get(dayAsDate), dayItem,
                    displayCriteriaFilter.getPendingBAROverridesAsDateMap().get(dayAsDate), displayCriteriaFilter.getConflictingOverrides(), displayCriteriaFilter.getPropertyManualRestrictions(), displayCriteriaFilter.getAccomManualRestrictions());
        }
    }

    private void handleBarByLosColumnValues(Map<Date, Map<Integer, BARDecisionInfo>> barDecisionsPerLOS,
                                            Map<Date, BARDecisionInfo> washOverrides,
                                            Map<Date, Map<Integer, Map<String, BigDecimal>>> dailyPriceMapsPerLos,
                                            Date dayAsDate,
                                            Item dayItem,
                                            Map<Integer, BAROverride> losToPendingOverrideMap,
                                            Map<Date, BARDecisionInfo> conflictingOverrides,
                                            Map<LocalDate, ManualRestrictionTypeDto> propertyManualRestrictions,
                                            Map<LocalDate, Map<ManualRestrictionRoomTypeIdentifier, ManualRestrictionTypeDto>> accomManualRestrictions) {
        List<Integer> losList = presenter.getLOSList();
        Map<Integer, BARDecisionInfo> losToBarDecision = barDecisionsPerLOS.get(dayAsDate);
        Map<Integer, Map<String, BigDecimal>> losToPriceMap = dailyPriceMapsPerLos.get(dayAsDate);
        BARDecisionInfo conflict = null;
        if (conflictingOverrides.containsKey(dayAsDate)) {
            conflict = conflictingOverrides.get(dayAsDate);
        }
        for (Integer los : losList) {
            if (losToBarDecision == null || !losToBarDecision.containsKey(los)) {
                continue;
            }
            BARDecisionInfo losDecision = losToBarDecision.get(los);
            String propertyId = LOS + los;

            boolean addAllowedDueToLRA = true;
            if (PricingManagementPresenter.LRA_REASON == losDecision.getReasonTypeId()) {
                if ((enableSingleBarDecision && lraEnabled) || (!enableSingleBarDecision &&
                        (presenter.getParameterDTO().isLRA_MINLOS() || presenter.getParameterDTO().isLRA_FPLOS()))) {
                    addAllowedDueToLRA = false;
                }
            }

            HorizontalLayout barByLosLayout = new HorizontalLayout();
            barByLosLayout.setSpacing(false);
            barByLosLayout.addStyleName("table-override-container");

            BAROverride pendingOverride = losToPendingOverrideMap.get(los);
            if (addAllowedDueToLRA) {
                Label losValueLabel = getLosValueLabel(losToPriceMap, los, losDecision, pendingOverride, dayAsDate);
                barByLosLayout.addComponent(losValueLabel);
            }

            boolean addAllowed = addAllowedDueToLRA;
            LocalDate day = new LocalDate(dayAsDate);
            if (!day.isBefore(presenter.getSystemDateAsLocalDate()) && addAllowed) {
                TetrisImageButton overrideButton = new TetrisImageButton(TetrisFontAwesome.OVERRIDE_ICON);
                overrideButton.setId("overrideButton");
                overrideButton.addClickListener(event -> showPricingOverrideWindow(day, los, losDecision));
                barByLosLayout.addComponent(overrideButton);
            }
            if (!PricingManagementPresenter.RATE_PLAN_NONE.equals(losDecision.getRatePlanName())
                    && !day.isBefore(presenter.getSystemDateAsLocalDate())
                    && rateHasChanged(day.toDate(), los)) {
                String html = rateChangeUp(day.toDate(), los) ? TetrisFontAwesome.SMALL_GREEN_UP_TRIANGLE.getHtml() :
                        TetrisFontAwesome.SMALL_RED_DOWN_TRIANGLE.getHtml();
                String content;
                if (displayCriteriaFilter.getEarlierBarDecisionsByLos().containsKey(dayAsDate) &&
                        displayCriteriaFilter.getEarlierBarDecisionsByLos().get(dayAsDate)
                                .containsKey(los)) {
                    BARDecisionInfo earlierBarDecision = displayCriteriaFilter.getEarlierBarDecisionsByLos().get(dayAsDate).get(los);
                    String earlierRate = earlierBarDecision.getRatePlanName();
                    content = "<span class=\"rate-change-arrow\" title=\"" + earlierRate + "\">" + html + "</span>";
                } else {
                    content = "<span class=\"rate-change-arrow\">" + html + "</span>";
                }

                Label upDownLabel = new Label(content, ContentMode.HTML);
                upDownLabel.setSizeUndefined();
                barByLosLayout.addComponent(upDownLabel);
            }
            String overrideIcon = getOverrideIcon(losDecision, true);
            if (pendingOverride != null) {
                overrideIcon = getPendingOverrideIcon(pendingOverride, losDecision, false);
            }
            boolean removeAllowed = UiUtils.isEnabledPerRequirements(true,
                    TetrisPermissionKey.FUNCTION_REMOVE_SINGLE_DAY_BAR_OVERRIDE_USER_AND_FLOOR)
                    && (pendingOverride == null || !pendingOverride.isRemove())
                    && !day.isBefore(presenter.getSystemDateAsLocalDate());

            if (overrideIcon != null) {
                addOverrideIcon(dayAsDate, losDecision, barByLosLayout, overrideIcon, removeAllowed, pendingOverride);
            }
            addBarByLosStatusIcons(washOverrides, dayAsDate, conflict, los, losDecision, barByLosLayout, propertyManualRestrictions, accomManualRestrictions);
            if (webRateShoppingEnabled) {
                VerticalLayout barLayout = new VerticalLayout();
                barLayout.setSizeUndefined();
                barLayout.addComponent(barByLosLayout);
                addCompetitorInfo(dayAsDate, los, barLayout);
                dayItem.getItemProperty(propertyId).setValue(barLayout);
            } else {
                dayItem.getItemProperty(propertyId).setValue(barByLosLayout);
            }
        }
    }

    //specific check for Close LV0 flag
    private boolean isEligibleForRemove(BAROverride pendingOverride, BARDecisionInfo losDecision) {
        if (pendingOverride == null) {
            return !(StringUtils.equalsIgnoreCase(Constants.BARDECISIONOVERRIDE_PENDING, losDecision.getOverride())
                    || StringUtils.equalsIgnoreCase(Constants.BARDECISIONOVERRIDE_NONE, losDecision.getOverride()));
        } else {
            return pendingOverride.getFloorRateUnqualifiedId() > 0 ||
                    pendingOverride.getSpecificRateUnqualifiedId() > 0 ||
                    pendingOverride.getCeilingRateUnqualifiedId() > 0 ||
                    (closeLV0Enabled && pendingOverride.isRestrictHighestBarEnabled());
        }
    }

    private void addCompetitorInfo(Date dayAsDate, Integer los, VerticalLayout barByLosLayout) {
        Map<Integer, CompetitorInfo> losCompetitorInfoMap = displayCriteriaFilter.getCompetitorInfosByLos().get
                (dayAsDate);
        CompetitorInfo competitorInfo = null;
        if (losCompetitorInfoMap != null) {
            competitorInfo = losCompetitorInfoMap.get(los);
        }
        if (competitorInfo != null) {
            Button compButton = getCompetitorButton(competitorInfo, los, new LocalDate(dayAsDate));
            barByLosLayout.addComponent(compButton);
        } else {
            barByLosLayout.addComponent(new TetrisSpacer());
        }
    }

    private void addBarByLosStatusIcons(Map<Date, BARDecisionInfo> washOverrides, Date dayAsDate, BARDecisionInfo conflict, Integer los, BARDecisionInfo losDecision, Layout barByLosLayout, Map<LocalDate, ManualRestrictionTypeDto> propertyManualRestrictions, Map<LocalDate, Map<ManualRestrictionRoomTypeIdentifier, ManualRestrictionTypeDto>> accomManualRestrictions) {
        if (PricingManagementPresenter.LRA_REASON == losDecision.getReasonTypeId()) {
            if (presenter.showHighestBarRestrictedDueToLra()) {
                Label l = new Label(TetrisFontAwesome.PRICE_RESTRICTED.getHtml(), ContentMode.HTML);
                l.setWidth(16, Unit.PIXELS);
                barByLosLayout.addComponent(l);
            } else {
                Label l = new Label(TetrisFontAwesome.PRICE_IMPACTED.getHtml(), ContentMode.HTML);
                l.setWidth(16, Unit.PIXELS);
                barByLosLayout.addComponent(l);
            }
        }
        if (PricingManagementPresenter.HIGHEST_BAR_RESTRICTED_REASON == losDecision.getReasonTypeId()) {
            Label l = new Label(TetrisFontAwesome.LRV_GREATER_THAN_PRICE.getHtml(), ContentMode.HTML);
            l.setWidth(12, Unit.PIXELS);
            barByLosLayout.addComponent(l);
        }
        if (washOverrides.containsKey(dayAsDate)) {
            Label l = new Label(TetrisFontAwesome.OVERRIDE_WASH.getHtml(), ContentMode.HTML);
            l.setWidth(12, Unit.PIXELS);
            barByLosLayout.addComponent(l);
        }
        if (conflict != null && los.equals(conflict.getLengthOfStay())) {
            Label l = new Label(TetrisFontAwesome.OVERRIDE_CONFLICT.getHtml(), ContentMode.HTML);
            l.setWidth(12, Unit.PIXELS);
            barByLosLayout.addComponent(l);
        }
        LocalDate localDate = new LocalDate(dayAsDate);
        if (propertyManualRestrictions.containsKey(localDate) || accomManualRestrictions.containsKey(localDate)) {
            Label l = new Label(TetrisFontAwesome.USER_CHECK.getHtml(), ContentMode.HTML);
            String description = presenter.getMRIconDescription(propertyManualRestrictions.get(localDate), accomManualRestrictions.get(localDate));
            l.setDescription(description);
            l.setWidth(12, Unit.PIXELS);
            barByLosLayout.addComponent(l);
        }
    }


    private void addOverrideIcon(Date dayAsDate, BARDecisionInfo losDecision, Layout barByLosLayout, String overrideIcon, boolean removeAllowed, BAROverride pendingOverride) {
        if (isEligibleForRemove(pendingOverride, losDecision)
                && (enableSingleBarDecision ? presenter.isMasterRoomClassId(pendingOverride == null ? losDecision.getAccomClassId() : pendingOverride.getAccomClassId()) : true)) {
            if (pendingOverride == null || pendingOverride.isRemove()) {
                Button removeButton = new Button(UiUtils.getText("remove.override"));
                removeButton.addStyleName(Reindeer.BUTTON_LINK);
                removeButton.setId("removeButton");
                VerticalLayout wrapper = new VerticalLayout(removeButton);
                wrapper.setSizeUndefined();
                wrapper.setMargin(true);
                PopupView overridePopup = new PopupView(overrideIcon, wrapper);
                overridePopup.setWidth(100, Unit.PERCENTAGE);
                overridePopup.setHideOnMouseOut(false);
                barByLosLayout.addComponent(overridePopup);
                if (!removeAllowed) {
                    removeButton.setEnabled(false);
                    wrapper.setDescription(UiUtils.getText("permOverrideMessageNone"));
                }
                removeButton.addClickListener(event -> {
                    overridePopup.setPopupVisible(false);
                    removeBarOverride(dayAsDate, losDecision);
                });
            } else {
                addUndoChangesButton(barByLosLayout, removeAllowed, pendingOverride);
            }
        } else {
            Label label = new Label(overrideIcon, ContentMode.HTML);
            label.setWidth(12, Unit.PIXELS);
            barByLosLayout.addComponent(label);
        }
    }

    private void addUndoChangesButton(Layout barByLosLayout, boolean removeAllowed, BAROverride pendingOverride) {
        TetrisImageButton undoChanges = new TetrisImageButton(TetrisFontAwesome.UNDO_CHANGES, UiUtils.getText("common.revert.override"), TetrisFontAwesome.FontSize.SMALL);
        undoChanges.setId("undoChangesButton");
        undoChanges.addStyleName("unsaved-changes-button");
        undoChanges.addClickListener(clickEvent -> {
            presenter.removePendingBAROverride(pendingOverride);
            presenter.checkPendingOverrideChanges();
            presenter.requestPendingEventRefresh();
        });

        if (!removeAllowed) {
            undoChanges.setEnabled(false);
        }
        barByLosLayout.addComponent(undoChanges);
    }

    private void removeBarOverride(Date dayAsDate, BARDecisionInfo losDecision) {
        int currentPageFirstItemIndex = dataTable.getCurrentPageFirstItemIndex();
        BAROverride pendingBAROverride = presenter.findPendingBAROverride(losDecision.getAccomClassId(),
                losDecision.getLengthOfStay(), dayAsDate);
        boolean hasPersistedOverride = (losDecision.getCeilingRatePlanId() != null) || (losDecision.getSpecificRatePlanId() != null) || (losDecision.getFloorRatePlanId() != null);

        boolean hasPendingOverride =
                (pendingBAROverride == null) ? false : (pendingBAROverride.getCeilingRateUnqualifiedId() > 0 || pendingBAROverride.getFloorRateUnqualifiedId() > 0 || pendingBAROverride.getSpecificRateUnqualifiedId() > 0);
        if (pendingBAROverride != null) {
            presenter.removePendingBAROverride(pendingBAROverride);
        }

        if (hasPersistedOverride || hasPendingOverride) {
            BAROverride override = new BAROverride();
            override.setAccomClassId(losDecision.getAccomClassId());
            override.setArrivalDate(losDecision.getArrivalDate());
            override.setLengthOfStay(losDecision.getLengthOfStay());

            if (hasPersistedOverride) {
                override.setCeilingRateUnqualifiedId(losDecision.getCeilingRatePlanId() == null ?
                        Integer.valueOf(0) : losDecision.getCeilingRatePlanId());
                override.setFloorRateUnqualifiedId(losDecision.getFloorRatePlanId() == null ?
                        Integer.valueOf(0) : losDecision.getFloorRatePlanId());
                override.setSpecificRateUnqualifiedId(losDecision.getSpecificRatePlanId() == null ?
                        Integer.valueOf(0) : losDecision.getSpecificRatePlanId());
            } else if (hasPendingOverride) {
                override.setCeilingRateUnqualifiedId(pendingBAROverride.getCeilingRateUnqualifiedId());
                override.setFloorRateUnqualifiedId(pendingBAROverride.getFloorRateUnqualifiedId());
                override.setSpecificRateUnqualifiedId(pendingBAROverride.getSpecificRateUnqualifiedId());
            }

            override.setRemove(true);
            override.setRestrictHighestBarEnabled(pendingBAROverride != null ? pendingBAROverride.isRestrictHighestBarEnabled() : losDecision.isRestrictHighestBarOverride());
            override.setRemoveRestrictHighestBarOverride(pendingBAROverride != null ? pendingBAROverride.isRemoveRestrictHighestBarOverride() : false);
            if (override.isRestrictHighestBarEnabled()) {
                List<AccomTypeSummary> selectedAccomTypeSummaryList = pendingBAROverride == null ? losDecision.getClosedRoomTypes() == null ? new ArrayList<>() : losDecision.getClosedRoomTypes() : pendingBAROverride.getAccomTypeSummaryList().stream().filter(summary -> summary.isSelected()).collect(Collectors.toList());
                BARDetails barDetails = (losDecision.getLengthOfStay() < 0) ? presenter.getBARDetails(losDecision.getAccomClassId(), dayAsDate) : presenter.getBARDetails(losDecision.getAccomClassId(), dayAsDate, losDecision.getLengthOfStay());
                List<AccomTypeSummary> roomTypes = (barDetails != null) ? barDetails.getAccomTypeSummaryList() : new ArrayList<>();
                roomTypes.forEach(accomTypeSummary -> accomTypeSummary.setIsSelected(selectedAccomTypeSummaryList.contains(accomTypeSummary)));
                override.setAccomTypeSummaryList(roomTypes);
            }
            presenter.addPendingBAROverride(override);
        }
        presenter.checkPendingOverrideChanges();
        updateTableData();
        dataTable.setCurrentPageFirstItemIndex(currentPageFirstItemIndex);
    }

    private void handleBarByDayColumnValue(Map<Date, Map<String, BigDecimal>> dailyPriceMaps,
                                           Map<Date, BARDecisionInfo> washOverrides,
                                           Map<Date, CompetitorInfo> competitorInfos,
                                           LocalDate day,
                                           BARDecisionInfo barDecisionInfo,
                                           Item dayItem,
                                           BAROverride pendingOverride,
                                           Map<Date, BARDecisionInfo> conflictingOverrides,
                                           Map<LocalDate, ManualRestrictionTypeDto> propertyManualRestrictions,
                                           Map<LocalDate, Map<ManualRestrictionRoomTypeIdentifier, ManualRestrictionTypeDto>> accomManualRestrictions) {
        Map<String, BigDecimal> priceMap = dailyPriceMaps.get(day.toDate());
        String ratePlanName = barDecisionInfo.getRatePlanName();
        VerticalLayout barLayout = new VerticalLayout();
        barLayout.setWidthUndefined();
        barLayout.setId("barLayout");
        HorizontalLayout barRateLayout = new HorizontalLayout();
        barRateLayout.setHeight(20, Unit.PIXELS);
        barRateLayout.setSpacing(false);
        Label barRateLabel;


        if (!PricingManagementPresenter.RATE_PLAN_NONE.equals(ratePlanName)) {
            if (pendingOverride != null) {
                ratePlanName = presenter.getPendingRatePlanName(barDecisionInfo, ratePlanName, pendingOverride);
            }
            barRateLabel = getBARRateLabel(priceMap, ratePlanName);
        } else {
            barRateLabel = generateNoBarDecisionWarningLabel(barDecisionInfo);
        }
        if (isBarValueSelected) {
            barRateLabel = getBarValueLabel(day, barDecisionInfo, pendingOverride, priceMap, ratePlanName);
        } else {
            String priceMapString = getPriceWithRoomTypeMapString(priceMap);
            barRateLabel.setDescription(priceMapString, com.vaadin.shared.ui.ContentMode.HTML);
        }
        barRateLayout.addComponent(barRateLabel);
        if (!day.isBefore(presenter.getSystemDateAsLocalDate())) {
            TetrisImageButton overrideButton = new TetrisImageButton(TetrisFontAwesome.OVERRIDE_ICON);
            overrideButton.setId("overrideButton");
            overrideButton.addStyleName("pricing-management-override-button");
            overrideButton.addClickListener(event -> showPricingOverrideWindow(day, -1, barDecisionInfo));
            barRateLayout.addComponent(overrideButton);
        }
        if (!PricingManagementPresenter.RATE_PLAN_NONE.equals(ratePlanName)
                && !day.isBefore(presenter.getSystemDateAsLocalDate())
                && rateHasChanged(day.toDate(), -1)) {
            String html = rateChangeUp(day.toDate(), -1) ? TetrisFontAwesome.SMALL_GREEN_UP_TRIANGLE.getHtml() :
                    TetrisFontAwesome.SMALL_RED_DOWN_TRIANGLE.getHtml();
            String content;
            if (displayCriteriaFilter.getEarlierBarDecisions().containsKey(day.toDate())) {
                BARDecisionInfo earlierDecision = displayCriteriaFilter.getEarlierBarDecisions().get(day.toDate());
                String earlierRate = earlierDecision.getRatePlanName();
                content = "<span class=\"rate-change-arrow\" title=\"" + earlierRate + "\">" + html + "</span>";
            } else {
                content = "<span class=\"rate-change-arrow\">" + html + "</span>";
            }

            Label upDownLabel = new Label(content, ContentMode.HTML);
            upDownLabel.setSizeUndefined();
            barRateLayout.addComponent(upDownLabel);
        }

        String overrideIcon = getOverrideIcon(barDecisionInfo, true);
        if (pendingOverride != null) {
            overrideIcon = getPendingOverrideIcon(pendingOverride, barDecisionInfo, false);
        }
        boolean removeAllowed = UiUtils.isEnabledPerRequirements(true,
                TetrisPermissionKey.FUNCTION_REMOVE_SINGLE_DAY_BAR_OVERRIDE_USER_AND_FLOOR)
                && (pendingOverride == null || !pendingOverride.isRemove())
                && !day.isBefore(presenter.getSystemDateAsLocalDate());
        if (overrideIcon != null) {
            addOverrideIcon(day.toDate(), barDecisionInfo, barRateLayout, overrideIcon, removeAllowed, pendingOverride);
        }
        addBarByDayStatusIcons(washOverrides, day, barDecisionInfo, conflictingOverrides, barRateLayout, propertyManualRestrictions, accomManualRestrictions);
        // Competitor info for BAR column
        CompetitorInfo competitorInfo = competitorInfos.get(day.toDate());
        barLayout.addComponent(barRateLayout);
        barLayout.setComponentAlignment(barRateLayout, Alignment.MIDDLE_RIGHT);
        dayItem.getItemProperty(BAR).setValue(barLayout);
        if (webRateShoppingEnabled && competitorInfo != null) {
            Button compButton = getCompetitorButton(competitorInfo, -1, day);
            VerticalLayout compInfoLayout = new VerticalLayout(compButton);
            compInfoLayout.setComponentAlignment(compButton, Alignment.MIDDLE_RIGHT);
            dayItem.getItemProperty(COMPETITOR).setValue(compInfoLayout);
        }
    }

    private Label getBARRateLabel(Map<String, BigDecimal> priceMap, String ratePlanName) {
        return null == priceMap ? new Label(PricingManagementPresenter.NO_RATE_PLAN_VALUE) : new Label(ratePlanName);
    }

    private Label getBarValueLabel(LocalDate day, BARDecisionInfo barDecisionInfo, BAROverride pendingOverride, Map<String, BigDecimal> priceMap, String ratePlanName) {
        Label barRateLabel;
        if (null != pendingOverride) {
            barRateLabel = getBarRateValuePendingOverrideLabel(day, barDecisionInfo, ratePlanName);
        } else {
            barRateLabel = getBarRateFromPriceMap(priceMap, barDecisionInfo);
        }
        return barRateLabel;
    }

    private Label getBarRateValuePendingOverrideLabel(LocalDate day, BARDecisionInfo barDecisionInfo, String ratePlanName) {
        BARDetails barDetails = presenter.getBARDetails(barDecisionInfo.getAccomClassId(), day.toDate());
        Map<String, BigDecimal> priceMap = barDetails.getPriceMapByRateUnqualifiedName().get(ratePlanName);
        String firstKey = priceMap.keySet().stream().findFirst().get();
        ScaleAwareStringToBigDecimalConverter converter = new ScaleAwareStringToBigDecimalConverter();
        String barRateValue = converter.convertToPresentation(priceMap.get(firstKey), String.class, UiUtils.getLocale());
        return new Label(barRateValue);
    }

    private Label getBarRateValuePendingOverrideLabelForBarByLos(Date day, BARDecisionInfo barDecisionInfo, String ratePlanName, int los) {
        String barRateValue = getFirstRTBarRateValue(day, barDecisionInfo, ratePlanName, los);
        return new Label(barRateValue);
    }

    private String getFirstRTBarRateValue(Date day, BARDecisionInfo barDecisionInfo, String ratePlanName, int los) {
        BARDetails barDetails = presenter.getBARDetails(barDecisionInfo.getAccomClassId(), day, los);
        Map<String, BigDecimal> priceMap = barDetails.getPriceMapByRateUnqualifiedName().get(ratePlanName);
        ScaleAwareStringToBigDecimalConverter converter = new ScaleAwareStringToBigDecimalConverter();
        if (null == priceMap) {
            return converter.convertToPresentation(BigDecimal.ZERO, String.class, UiUtils.getLocale());
        }
        priceMap = orderPriceMap(priceMap);
        String firstKey = priceMap.keySet().stream().findFirst().get();

        return converter.convertToPresentation(priceMap.get(firstKey), String.class, UiUtils.getLocale());
    }

    private Map<String, BigDecimal> orderPriceMap(Map<String, BigDecimal> priceMap) {
        priceMap = priceMap.entrySet()
                .stream()
                .sorted(Map.Entry.comparingByValue(Comparator.naturalOrder()))
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        Map.Entry::getValue,
                        (e1, e2) -> e1,
                        LinkedHashMap::new
                ));
        return priceMap;
    }

    private Label generateNoBarDecisionWarningLabel(BARDecisionInfo barDecisionInfo) {
        Label barRateLabel;
        StringBuilder ratePlanSB = new StringBuilder("<span class=\"no-bar-decision-marker\" title=\"").append(
                        UiUtils.getText("noBarDecision.reasonTypeId." + barDecisionInfo.getReasonTypeId()))
                .append("\">").append(TetrisFontAwesome.WARNING.getHtml()).append("</span>");
        barRateLabel = new Label(ratePlanSB.toString(), ContentMode.HTML);
        return barRateLabel;
    }

    private void addBarByDayStatusIcons(Map<Date, BARDecisionInfo> washOverrides, LocalDate day, BARDecisionInfo barDecisionInfo,
                                        Map<Date, BARDecisionInfo> conflictingOverrides, HorizontalLayout barRateLayout, Map<LocalDate, ManualRestrictionTypeDto> propertyManualRestrictions, Map<LocalDate, Map<ManualRestrictionRoomTypeIdentifier, ManualRestrictionTypeDto>> accomManualRestrictions) {
        boolean isHiltonSpecificEnabled = presenter.isHiltonSpecificEnabled();
        if (washOverrides.containsKey(day.toDate())) {
            barRateLayout.addComponent(new Label(TetrisFontAwesome.OVERRIDE_WASH.getHtml(), ContentMode.HTML));
        }
        if (PricingManagementPresenter.LRA_REASON == barDecisionInfo.getReasonTypeId() && !(isHiltonSpecificEnabled && !Constants.RATE_LEVEL_0.equals(barDecisionInfo.getRatePlanName()))) {
            if (enableSingleBarDecision && lraEnabled) {
                barRateLayout.addComponent(new Label(TetrisFontAwesome.PRICE_RESTRICTED.getHtml(), ContentMode.HTML));
            } else if (!enableSingleBarDecision &&
                    (presenter.getParameterDTO().isLRA_MINLOS() || presenter.getParameterDTO().isLRA_FPLOS())) {
                barRateLayout.addComponent(new Label(TetrisFontAwesome.PRICE_RESTRICTED.getHtml(), ContentMode.HTML));
            } else {
                barRateLayout.addComponent(new Label(TetrisFontAwesome.PRICE_IMPACTED.getHtml(), ContentMode.HTML));
            }
        }
        if (PricingManagementPresenter.HIGHEST_BAR_RESTRICTED_REASON == barDecisionInfo.getReasonTypeId() && !(isHiltonSpecificEnabled && !Constants.RATE_LEVEL_0.equals(barDecisionInfo.getRatePlanName()))) {
            barRateLayout.addComponent(new Label(TetrisFontAwesome.LRV_GREATER_THAN_PRICE.getHtml(), ContentMode.HTML));
        }
        if (conflictingOverrides.containsKey(day.toDate())) {
            barRateLayout.addComponent(new Label(TetrisFontAwesome.OVERRIDE_CONFLICT.getHtml(), ContentMode.HTML));
        }
        if (propertyManualRestrictions.containsKey(day) || accomManualRestrictions.containsKey(day)) {
            Label label = new Label(TetrisFontAwesome.USER_CHECK.getHtml(), ContentMode.HTML);
            String description = presenter.getMRIconDescription(propertyManualRestrictions.get(day), accomManualRestrictions.get(day));
            label.setDescription(description);
            barRateLayout.addComponent(label);
        }
    }

    private Button getCompetitorButton(CompetitorInfo competitorInfo, int los, LocalDate date) {
        String compPrice;
        String compName = "";
        Button compButton;
        Set<String> competitorNames = competitorInfo.getCompetitorNames();
        if (competitorNames != null) {
            String defaultCompetitor = "";
            if (PricingManagementPresenter.SPECIFIC_COMPETITOR.equals(presenter.getParameterDTO().getBarOvrdDisplayCompetitor())) {
                defaultCompetitor = presenter.getParameterDTO().getBarOvrdAbsoluteCompetitor();
                if (defaultCompetitor == null) {
                    defaultCompetitor = "";
                }
            }
            for (Iterator<String> iter = competitorNames.iterator(); iter.hasNext(); ) {
                String name = iter.next();
                if (defaultCompetitor.equals(name)) {
                    name = "<b><i>" + name + "</i></b>";
                }
                compName += name;
                if (iter.hasNext()) {
                    compName += "<br />";
                }
            }
        }
        compPrice = UiUtils.numberFormat(formatter, competitorInfo.getCompetitorPrice());
        compButton = new Button(compPrice);
        compButton.setId("compButton");
        compButton.setDescription(UiUtils.getText("competitor") + ": " + compName, com.vaadin.shared.ui.ContentMode.HTML);
        compButton.addStyleName(Reindeer.BUTTON_LINK);
        compButton.addStyleName(Reindeer.BUTTON_SMALL);
        compButton.addStyleName("competitor-button");
        compButton.addClickListener(event -> showCompetitorDetailsWindow(los, date, false));
        return compButton;
    }

    public void showCompetitorDetailsWindow(int los, LocalDate date, boolean isSourceInfoMgr) {
        if (isSourceInfoMgr && presenter.getParameterDTO().isBARByLOS()) {
            los = displayCriteriaFilter.getCompetitorInfosByLos().get(date.toDate()).keySet().stream().findFirst().orElse(-1);
        }
        CompetitorDetailsWindow detailsWindow = new CompetitorDetailsWindow(date, presenter, los,
                startDate, endDate, displayCriteriaFilter.getCompetitorInfo(), displayCriteriaFilter
                .getCompetitorInfosByLos(), formatter);
        UI.getCurrent().addWindow(detailsWindow);
    }

    private String getOverrideHtml(BARDecisionInfo override) {
        String html = "";
        if (override != null) {
            if (override.isExistingOccupancyDemandOverride()) {
                html = TetrisFontAwesome.OVERRIDE_OCCUPANCY_DATE.getHtml();
            }
            if (override.isExistingArrivalDemandOverride()) {
                html += " " + TetrisFontAwesome.OVERRIDE_ARRIVAL_LOS.getHtml();
            }
        } else {
            html = "-";
        }
        return html;
    }

    private void createTable() {
        dataTable = new Table();
        tableDataSource = new IndexedContainer();
        tableFilter = new PricingManagementTableFilter();
        displayCriteriaFilter = new PricingManagementTableDisplayCriteriaFilter();
        dataTable.setContainerDataSource(tableDataSource);
        dataTable.setId("dataTable");
        dataTable.setWidth(100, Unit.PERCENTAGE);
        dataTable.setHeight(100, Unit.PERCENTAGE);
        dataTable.setSortEnabled(false);
    }

    private void showPricingOverrideWindow(LocalDate bubbleDate, int lenghtOfStay, BARDecisionInfo losDecision) {
        InvestigatorDto investigatorDtoForPricingManagementOverride = createInvestigatorDtoForPricingManagementOverride(bubbleDate, losDecision, lenghtOfStay);
        InvestigatorUtil.openPopUpForBAR(investigatorDtoForPricingManagementOverride, presenter.getOverridePopUpBarByLosViewFactory());
    }

    public InvestigatorDto createInvestigatorDtoForPricingManagementOverride(LocalDate date, BARDecisionInfo losDecision, int lengthOfStay) {
        InvestigatorDto investigatorDto = new InvestigatorDto();
        investigatorDto.setModuleOrigin(TetrisPermissionKey.PRICING_MANAGEMENT);
        investigatorDto.setOverride(true);
        investigatorDto.setSelectedDate(date);
        investigatorDto.setSelectedAccomClassId(losDecision.getAccomClassId());
        Map<String, BigDecimal> priceMap = getPriceMapBy(date, losDecision, lengthOfStay);
        if (null != priceMap) {
            Optional<AccomTypeSummary> lowestRtOptional = getLowestRtOptional(priceMap);
            if (lowestRtOptional.isPresent()) {
                investigatorDto.setSelectedAccomTypeId(lowestRtOptional.get().getId());
            }
        }
        investigatorDto.setLos(lengthOfStay);
        investigatorDto.setBarName(losDecision.getRatePlanName());
        investigatorDto.setBarValue(getFirstRTBarRateValue(date.toDate(), losDecision, losDecision.getRatePlanName(), lengthOfStay));
        investigatorDto.setMasterRoomClass(presenter.getMasterClass().getId().equals(losDecision.getAccomClassId()));
        investigatorDto.setPendingOverride(presenter.findPendingBAROverride(losDecision.getAccomClassId(), lengthOfStay, date.toDate()));
        investigatorDto.setUserOverride(createUserOverride(date, losDecision, lengthOfStay));
        investigatorDto.setBarValueSelected(isBarValueSelected);
        return investigatorDto;
    }

    private Map<String, BigDecimal> getPriceMapBy(LocalDate date, BARDecisionInfo losDecision, int lengthOfStay) {
        BARDetails barDetails = presenter.getBARDetails(losDecision.getAccomClassId(), date.toDate(), lengthOfStay);
        return barDetails.getPriceMapByRateUnqualifiedName().get(losDecision.getRatePlanName());
    }

    private BAROverride createUserOverride(LocalDate date, BARDecisionInfo losDecision, int lengthOfStay) {
        BAROverride userOverride = new BAROverride();
        userOverride.setLengthOfStay(lengthOfStay);
        userOverride.setArrivalDate(date.toDate());
        userOverride.setSpecificRateUnqualifiedId(null != losDecision.getSpecificRatePlanId() ? losDecision.getSpecificRatePlanId() : 0);
        userOverride.setFloorRateUnqualifiedId(null != losDecision.getFloorRatePlanId() ? losDecision.getFloorRatePlanId() : 0);
        userOverride.setCeilingRateUnqualifiedId(null != losDecision.getCeilingRatePlanId() ? losDecision.getCeilingRatePlanId() : 0);
        userOverride.setRestrictHighestBarEnabled(losDecision.isRestrictHighestBarOverride());
        List<AccomTypeSummary> closedRoomTypes = losDecision.getClosedRoomTypes();
        userOverride.setAccomTypeSummaryList(closedRoomTypes);
        return userOverride;
    }

    private Optional<AccomTypeSummary> getLowestRtOptional(Map<String, BigDecimal> priceMap) {
        Map<String, BigDecimal> rtWithRate = orderPriceMap(priceMap);
        String lowestRtName = rtWithRate.keySet().stream().findFirst().get();
        RateUnqualifiedAccomClass rateUnqualifiedAccomClass = (RateUnqualifiedAccomClass) tableRoomClassSelector.getValue();
        return rateUnqualifiedAccomClass.getAccomTypeSummaryList().stream()
                .filter(accomTypeSummary -> accomTypeSummary.getName().equals(lowestRtName)).findFirst();
    }

    private String getPriceWithRoomTypeMapString(Map<String, BigDecimal> priceMap) {
        StringBuilder priceMapString = new StringBuilder();
        if (priceMap != null) {
            for (Map.Entry<String, BigDecimal> entry : priceMap.entrySet()) {
                priceMapString.append(entry.getKey()).append(" ").append(entry.getValue());
            }
        }
        return priceMapString.toString();
    }

    private Label getBarRateFromPriceMap(Map<String, BigDecimal> priceMap, BARDecisionInfo barDecisionInfo) {
        if (PricingManagementPresenter.RATE_PLAN_NONE.equals(barDecisionInfo.getRatePlanName())) {
            return generateNoBarDecisionWarningLabel(barDecisionInfo);
        }

        String barValue = PricingManagementPresenter.NO_RATE_PLAN_VALUE;
        if (MapUtils.isNotEmpty(priceMap)) {
            final List<Map.Entry<String, BigDecimal>> entryList = priceMap.entrySet().stream().sorted(Map.Entry.<String, BigDecimal>comparingByValue()).collect(Collectors.toList());
            barValue = FormatterUtil.formatDecimalValue(entryList.get(0).getValue(), 2);
        }

        return new Label(barValue);
    }

    private Label getLosValueLabel(Map<Integer, Map<String, BigDecimal>> losToPriceMap, Integer
            los, BARDecisionInfo losDecision, BAROverride pendingOverride, Date dayAsDate) {
        String ratePlanName = losDecision.getRatePlanName();
        Label losValueLabel = null;
        if (!PricingManagementPresenter.RATE_PLAN_NONE.equals(ratePlanName)) {
            if (pendingOverride != null) {
                ratePlanName = presenter.getPendingRatePlanName(losDecision, ratePlanName, pendingOverride);
            }
            losValueLabel = new Label(ratePlanName);
        } else {
            losValueLabel = generateNoBarDecisionWarningLabel(losDecision);
        }
        if (isBarValueSelected) {
            losValueLabel = getBarValueLabel(losToPriceMap, los, losDecision, pendingOverride, dayAsDate, ratePlanName);
        }
        if (losToPriceMap != null && losToPriceMap.containsKey(los)) {
            Map<String, BigDecimal> price = losToPriceMap.get(los);
            if (price != null && !price.isEmpty()) {
                StringBuilder sb = new StringBuilder();
                price.entrySet().stream().sorted(Map.Entry.<String, BigDecimal>comparingByValue()).forEach((e) -> {
                            sb.append(e.getKey()).append(" ").append(e.getValue().toString()).append("<br/>");
                        }
                );
                losValueLabel.setDescription(sb.toString(), com.vaadin.shared.ui.ContentMode.HTML);
            } else if (price != null && price.isEmpty() && !PricingManagementPresenter.RATE_PLAN_NONE.equals(ratePlanName)) {
                losValueLabel = new Label(PricingManagementPresenter.NO_RATE_PLAN_VALUE);
            }
        }
        losValueLabel.setSizeUndefined();
        return losValueLabel;
    }

    private Label getBarValueLabel(Map<Integer, Map<String, BigDecimal>> losToPriceMap, Integer los, BARDecisionInfo losDecision, BAROverride pendingOverride, Date dayAsDate, String ratePlanName) {
        Label losValueLabel;
        if (null != pendingOverride) {
            losValueLabel = getBarRateValuePendingOverrideLabelForBarByLos(dayAsDate, losDecision, ratePlanName, los);
        } else {
            losValueLabel = getBarRateValueForBarByLos(losToPriceMap, los);
        }
        return losValueLabel;
    }

    private Label getBarRateValueForBarByLos(Map<Integer, Map<String, BigDecimal>> losToPriceMap, Integer los) {
        ScaleAwareStringToBigDecimalConverter converter = new ScaleAwareStringToBigDecimalConverter();
        if (losToPriceMap != null && losToPriceMap.containsKey(los)) {
            Map<String, BigDecimal> price = losToPriceMap.get(los);
            if (price != null && !price.isEmpty()) {
                List<Map.Entry<String, BigDecimal>> priceMap = price.entrySet().stream().sorted(Map.Entry.<String, BigDecimal>comparingByValue()).collect(Collectors.toList());
                return new Label(converter.convertToPresentation(priceMap.get(0).getValue(), String.class, UiUtils.getLocale()));
            }
        }
        return new Label(converter.convertToPresentation(BigDecimal.ZERO, String.class, UiUtils.getLocale()));
    }

    public void setStartDate(Date start, boolean updateSavedDate) {
        if (updateSavedDate) {
            presenter.setTableStart(start);
        }
        tableStartDateSelector.setValue(start);
    }

    public void setEndDate(Date end, boolean updateSavedDate) {
        if (updateSavedDate) {
            presenter.setTableEnd(end);
        }
        tableEndDateSelector.setValue(end);
    }

    public Date getStartDate() {
        return startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void updateTableData() {
        updateTableData(startDate, endDate, false);
    }

    public void setSelectedRoomClass(RateUnqualifiedAccomClass roomClass) {
        tableRoomClassSelector.setValue(roomClass);
    }

    public RateUnqualifiedAccomClass getSelectedRoomClass() {
        return (RateUnqualifiedAccomClass) tableRoomClassSelector.getValue();
    }

    public void updateTableFilter(Set<PricingManagementPresenter.ShowOverrides> selectedValues) {
        tableFilter.setSelectedValues(selectedValues);
        if (selectedValues.isEmpty()) {
            tableDataSource.removeContainerFilter(tableFilter);
        } else {
            tableDataSource.addContainerFilter(tableFilter);
        }
    }

    private ComboBox buildRoomClassSelectComponent() {
        List<RateUnqualifiedAccomClass> roomClasses = presenter.getRoomClasses();
        RateUnqualifiedAccomClass masterClass = presenter.getMasterClass();
        ComboBox roomClassSelector = new ComboBox();
        roomClassSelector.setTextInputAllowed(false);
        roomClassSelector.setNullSelectionAllowed(false);
        RateUnqualifiedAccomClass first = null;
        for (RateUnqualifiedAccomClass roomClass : roomClasses) {
            if (first == null) {
                first = roomClass;
            }
            roomClassSelector.addItem(roomClass);
            roomClassSelector.setItemCaption(roomClass, roomClass.getAccomClassName());
        }
        if (masterClass != null) {
            roomClassSelector.select(masterClass);
        } else if (first != null) {
            roomClassSelector.select(first);
        }
        return roomClassSelector;
    }

    public String getOverrideIcon(BARDecisionInfo override, boolean isCloseLV0FlagRequired) {
        StringBuilder iconHtml = new StringBuilder();
        if ("USER".equalsIgnoreCase(override.getOverride())) {
            iconHtml.append(TetrisFontAwesome.BAR_OVERRIDE_SPECIFIC.getHtml());
        }
        if ("FLOOR".equalsIgnoreCase(override.getOverride())) {
            iconHtml.append(TetrisFontAwesome.BAR_OVERRIDE_FLOOR.getHtml());
        }
        if ("CEIL".equalsIgnoreCase(override.getOverride())) {
            iconHtml.append(TetrisFontAwesome.BAR_OVERRIDE_CEILING.getHtml());
        }
        if ("FLOORANDCEIL".equalsIgnoreCase(override.getOverride())) {
            iconHtml.append(TetrisFontAwesome.BAR_OVERRIDE_FLOOR_AND_CEILING.getHtml());
        }
        if (closeLV0Enabled && isCloseLV0FlagRequired && override.isRestrictHighestBarOverride()) {
            iconHtml.append(presenter.appendToolTip(TetrisFontAwesome.BAR_OVERRIDE_CLOSE_LV0.getHtml(), override));
        }
        return iconHtml.length() > 0 ? iconHtml.toString() : null;
    }

    public String getPendingOverrideIcon(BAROverride barOverride) {
        BARDecisionInfo barDecisionInfo;
        if (presenter.getParameterDTO().isBARByLOS()) {
            barDecisionInfo = (BARDecisionInfo) dataTable.getItem(barOverride.getArrivalDate());
        } else {
            barDecisionInfo = displayCriteriaFilter.getBarDecisions().get(barOverride.getArrivalDate());
        }
        return getPendingOverrideIcon(barOverride, barDecisionInfo, true);
    }

    public String getPendingOverrideIcon(BAROverride barOverride, BARDecisionInfo losDecision, boolean showPendingOverrideIconOnWhatIf) {
        StringBuilder iconHtml = new StringBuilder();
        if (barOverride.isSelected() || barOverride.isRemove()) {
            if (barOverride.getSpecificRateUnqualifiedId() > 0) {
                iconHtml.append(getSpecificOverride(barOverride));
            } else if (barOverride.getFloorRateUnqualifiedId() > 0 && barOverride.getCeilingRateUnqualifiedId() > 0) {
                iconHtml.append(getFloorAndCeilingOverride(barOverride));
            } else if (barOverride.getFloorRateUnqualifiedId() > 0) {
                iconHtml.append(getFloorOverride(barOverride));
            } else if (barOverride.getCeilingRateUnqualifiedId() > 0) {
                iconHtml.append(getCeilingOverride(barOverride));
            }
        } else {
            String overrideIcon = getOverrideIcon(losDecision, false);
            if (StringUtils.isNotEmpty(overrideIcon)) {
                iconHtml.append(overrideIcon);
            }

        }
        if (closeLV0Enabled && !showPendingOverrideIconOnWhatIf) {
            String closeLV0Override = getCloseLV0Override(barOverride, losDecision);
            if (StringUtils.isNotEmpty(closeLV0Override)) {
                iconHtml.append(closeLV0Override);
            }
        }
        return iconHtml.length() > 0 ? iconHtml.toString() : null;
    }

    private String getCeilingOverride(BAROverride barOverride) {
        if (barOverride.isRemove()) {
            return TetrisFontAwesome.BAR_OVERRIDE_CEILING_PENDING_REMOVE.getHtml();
        } else {
            return TetrisFontAwesome.BAR_OVERRIDE_CEILING_PENDING_SAVE.getHtml();
        }
    }

    private String getCloseLV0Override(BAROverride barOverride, BARDecisionInfo losDecision) {
        if (barOverride.isRemoveRestrictHighestBarOverride()) {
            return TetrisFontAwesome.BAR_OVERRIDE_CLOSE_LV0_PENDING_REMOVE.getHtml();
        } else if (presenter.hasLV0ClosedRoomTypes(barOverride) && !presenter.hasChangesForAccomTypeList(barOverride, losDecision)) {
            return presenter.appendToolTip(TetrisFontAwesome.BAR_OVERRIDE_CLOSE_LV0.getHtml(), losDecision);
        } else if (barOverride.isRestrictHighestBarEnabled()) {
            return TetrisFontAwesome.BAR_OVERRIDE_CLOSE_LV0_PENDING_SAVE.getHtml();
        }
        return null;
    }

    private String getFloorOverride(BAROverride barOverride) {
        if (barOverride.isRemove()) {
            return TetrisFontAwesome.BAR_OVERRIDE_FLOOR_PENDING_REMOVE.getHtml();
        } else {
            return TetrisFontAwesome.UNDO_CHANGES.getHtml();
        }
    }

    private String getFloorAndCeilingOverride(BAROverride barOverride) {
        if (barOverride.isRemove()) {
            return TetrisFontAwesome.BAR_OVERRIDE_FLOOR_AND_CEILING_PENDING_REMOVE.getHtml();
        } else {
            return TetrisFontAwesome.BAR_OVERRIDE_FLOOR_AND_CEILING_PENDING_SAVE.getHtml();
        }
    }

    private String getSpecificOverride(BAROverride barOverride) {
        if (barOverride.isRemove()) {
            return TetrisFontAwesome.BAR_OVERRIDE_SPECIFIC_PENDING_REMOVE.getHtml();
        } else {
            return TetrisFontAwesome.UNDO_CHANGES.getHtml();
        }
    }

    public Map<Date, Map<Integer, BARDecisionInfo>> getBarDecisionsByLOS() {
        return displayCriteriaFilter.getBarDecisionsByLos();
    }

    public Map<Date, BARDecisionInfo> getBarDecisionsByDay() {
        return displayCriteriaFilter.getBarDecisions();
    }

    public void refreshPendingTableEvents() {
        LocalDateInterval interval = new LocalDateInterval(new LocalDate(tableStartDateSelector.getValue()),
                new LocalDate(tableEndDateSelector.getValue()));
        boolean isBARByLOS = presenter.getParameterDTO().isBARByLOS();
        fetchPendingOverrides(isBARByLOS);
        for (LocalDate day : interval) {
            Date dayAsDate = day.toDate();
            if ((isBARByLOS && (displayCriteriaFilter.getBarDecisionsByLos() == null || !displayCriteriaFilter.getBarDecisionsByLos().containsKey(dayAsDate)))
                    || (!isBARByLOS && (displayCriteriaFilter.getBarDecisions() == null || !displayCriteriaFilter.getBarDecisions().containsKey(dayAsDate)))) {
                continue;
            }
            updateBARColumns(presenter.getParameterDTO().isBARByLOS(), day);
        }
    }

    public void updateDisplayCriteria(DisplayCriteria displayCriteria) {
        this.displayCriteria = displayCriteria;
        if (DayFilter.CHANGES_AFTER.equals(displayCriteria.getDayFilterValue())) {
            updateTableData();
        }
        displayCriteriaFilter.setDisplayCriteria(displayCriteria);
        tableFilter.setFilteredLosList(displayCriteria.getFilteredLOSList());
        tableDataSource.removeContainerFilter(displayCriteriaFilter);
        tableDataSource.addContainerFilter(displayCriteriaFilter);

        List<String> filteredPropertyIds = new ArrayList<>();
        for (int los : displayCriteria.getFilteredLOSList()) {
            filteredPropertyIds.add(LOS + los);
        }
        setLOSColumnsVisibility(filteredPropertyIds);
        dataTable.refreshRowCache();
    }

    private boolean rateChangeUp(Date date, int los) {
        String earlierRate = getEarlierBarRate(date, los);
        String currentRate = getCurrentBarRate(date, los);
        return displayCriteriaFilter.getOrderedRatePlanNames().indexOf(earlierRate) > displayCriteriaFilter.getOrderedRatePlanNames().indexOf
                (currentRate);
    }

    private String getEarlierBarRate(Date date, int los) {
        if (presenter.getParameterDTO().isBARByLOS()) {
            if (displayCriteriaFilter.getEarlierBarDecisionsByLos() != null && displayCriteriaFilter.getEarlierBarDecisionsByLos().containsKey(date)
                    && displayCriteriaFilter.getEarlierBarDecisionsByLos().get(date).get(los) != null) {
                logger.error(displayCriteriaFilter.getEarlierBarDecisionsByLos().get(date));
                return displayCriteriaFilter.getEarlierBarDecisionsByLos().get(date).get(los).getRatePlanName();
            }
        } else if (displayCriteriaFilter.getEarlierBarDecisions() != null && displayCriteriaFilter.getEarlierBarDecisions().containsKey(date)) {
            return displayCriteriaFilter.getEarlierBarDecisions().get(date).getRatePlanName();
        }
        return null;
    }

    private String getCurrentBarRate(Date date, int los) {
        if (presenter.getParameterDTO().isBARByLOS()) {
            if (displayCriteriaFilter.getEarlierBarDecisionsByLos() != null
                    && displayCriteriaFilter.getEarlierBarDecisionsByLos().containsKey(date)
                    && displayCriteriaFilter.getBarDecisionsByLos().get(date).get(los) != null) {
                return displayCriteriaFilter.getBarDecisionsByLos().get(date).get(los).getRatePlanName();
            }
        } else if (displayCriteriaFilter.getEarlierBarDecisions() != null
                && displayCriteriaFilter.getEarlierBarDecisions().containsKey(date)) {
            return displayCriteriaFilter.getBarDecisions().get(date).getRatePlanName();
        }
        return null;
    }

    private boolean rateHasChanged(Date date, int los) {
        String earlierRate = getEarlierBarRate(date, los);
        String currentRate = getCurrentBarRate(date, los);
        return currentRate != null && earlierRate != null && !currentRate.equals(earlierRate);
    }

    public void updateTabularUI(Date start, Date end, PricingManagementTableDisplayCriteriaFilter displayCriteriaFilter) {
        logger.info("loading data on pricing management tabular view for startDate [" + start + "] , endDate [" + end + "], accomClassCode[" + getSelectedRoomClass().getAccomClassCode() + "]");

        dataTable.removeAllItems();

        boolean isBARByLOS = presenter.getParameterDTO().isBARByLOS();
        if (isBARByLOS && null == displayCriteriaFilter.getBarDecisionsByLos()) {
            logger.warn("BarDecisions By LOS are not available for selected criteria: startDate[" + start + "], endDate[" + end + "], accomClassCode[" + getSelectedRoomClass().getAccomClassCode() + "]");
            return;
        }
        fetchPendingOverrides(isBARByLOS);
        displayCriteriaFilter.setBarByLos(isBARByLOS);
        tableFilter.setOverrideMaps(displayCriteriaFilter.getWashOverrides(), displayCriteriaFilter
                        .getArrivalAndOccupancyOverrides(),
                displayCriteriaFilter
                        .getBarDecisionsByLos(),
                displayCriteriaFilter.getBarDecisions(),
                displayCriteriaFilter.getConflictingOverrides(), isBARByLOS, displayCriteriaFilter
                        .getPendingBAROverridesAsDateMap(),
                displayCriteriaFilter.getPendingBAROverridesAsDateToLOSMap());
        LocalDateInterval interval = new LocalDateInterval(new LocalDate(start), new LocalDate(end));
        for (LocalDate day : interval) {
            Date dayAsDate = day.toDate();
            if ((isBARByLOS && !displayCriteriaFilter.getBarDecisionsByLos().containsKey(dayAsDate))
                    || (!isBARByLOS && !displayCriteriaFilter.getBarDecisions().containsKey(dayAsDate))) {
                continue;
            }

            Item dayItem = dataTable.addItem(day);
            dataTable.setStyleName("pricingManagementTable");
            dataTable.setId("pricingManagementTable");

            // Dates and Details
            Label datesAndDetailsLabel = new Label(day.toString("EEE", UiUtils.getLocale()) + ", " + day.toString(userDateFormat, UiUtils.getLocale()));

            TetrisImageButton detailsButton = new TetrisImageButton(TetrisFontAwesome.SEARCH_PLUS);
            detailsButton.setDescription(UiUtils.getText("common.details"));
            detailsButton.setId("detailsButton");
            detailsButton.addClickListener(event -> {
                PricingManagementDetailsWindow managementDetailsWindow = new PricingManagementDetailsWindow(day,
                        presenter.getFactData(day, -1),
                        presenter.getUserDateFormat(),
                        (RateUnqualifiedAccomClass) tableRoomClassSelector.getValue());
                UI.getCurrent().addWindow(managementDetailsWindow);
            });
            TetrisNoteImageButton noteImageButton = new TetrisNoteImageButton(day, "Pricing", "", false) {
                @Override
                public void updateNoteIcon(LocalDate localDate) {
                    //don't make service call see if the notes for the date is available.
                    List<DateNote> dateNotes = displayCriteriaFilter.getNotesByDate().get(localDate.toDate());
                    setHasExistingNotes(dateNotes != null);
                }
            };
            noteImageButton.setId("tableNoteImageButton");
            noteImageButton.addStyleName("tabular-layout-note-image-button");

            HorizontalLayout noteAndDetailsLayout = new HorizontalLayout(detailsButton, noteImageButton);
            noteAndDetailsLayout.setSpacing(true);
            noteAndDetailsLayout.setComponentAlignment(detailsButton, Alignment.MIDDLE_RIGHT);
            noteAndDetailsLayout.setComponentAlignment(noteImageButton, Alignment.MIDDLE_RIGHT);

            CssLayout columnLayout = new CssLayout(datesAndDetailsLabel, noteAndDetailsLayout);
            columnLayout.addStyleName("dates-And-Details-Column");
            columnLayout.setWidth(100, Unit.PERCENTAGE);
            dayItem.getItemProperty(DATES_AND_DETAILS).setValue(columnLayout);

            // OOO
            Integer ooo = displayCriteriaFilter.getRoomsOutOfOrder().get(dayAsDate);
            dayItem.getItemProperty(OOO).setValue(ooo);

            // Occupancy on books
            BigDecimal oob = displayCriteriaFilter.getRoomsOnBooks().get(dayAsDate);
            dayItem.getItemProperty(OOB).setValue(oob);

            // Occupancy forecast
            Float ofPercentage = displayCriteriaFilter.getOccupancyForecast().get(dayAsDate);
            String ofFormatted = getOccupancyForecast(ofPercentage);
            dayItem.getItemProperty(OF).setValue(ofFormatted);

            // LRV
            BigDecimal lrv = displayCriteriaFilter.getLastRoomValue().get(dayAsDate);
            dayItem.getItemProperty(LRV).setValue(lrv);

            // Overrides
            BARDecisionInfo override = displayCriteriaFilter.getArrivalAndOccupancyOverrides().get(dayAsDate);
            String html = getOverrideHtml(override);

            updateBARColumns(isBARByLOS, day);
        }

        if (hasFilter()) {
            tableDataSource.addContainerFilter(tableFilter);
        }
        tableDataSource.addContainerFilter(displayCriteriaFilter);
        presenter.requestUpdateOverrideButtonStates();

    }

    private String getOccupancyForecast(Float occupancyForecast) {
        if (occupancyForecast == null) {
            return null;
        }
        String of = String.format("%.2f", occupancyForecast);
        String percentage = "%";
        return UiUtils.formatValue(new BigDecimal(of)) + percentage;

    }

    public void setBarValueSelected(boolean barValueSelected) {
        this.isBarValueSelected = barValueSelected;
    }

    public boolean isBarValueSelected() {
        return isBarValueSelected;
    }
}
