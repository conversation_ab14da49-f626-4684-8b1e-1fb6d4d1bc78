package com.ideas.tetris.ui.modules.pricing;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.collect.Sets;
import com.ideas.infra.tetris.security.domain.LDAPUser;
import com.ideas.infra.tetris.security.domain.TetrisPermissionKey;
import com.ideas.tetris.pacman.common.configparams.*;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.common.utils.systemconfig.SystemConfig;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.accommodation.dto.AccomTypeSummary;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomClass;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomClassPriceRank;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomType;
import com.ideas.tetris.pacman.services.accommodation.service.AccommodationService;
import com.ideas.tetris.pacman.services.agilerates.configuration.dto.ProductHierarchyDto;
import com.ideas.tetris.pacman.services.agilerates.configuration.entity.*;
import com.ideas.tetris.pacman.services.agilerates.configuration.service.AgileRatesConfigurationService;
import com.ideas.tetris.pacman.services.agilerates.configuration.service.ProductHierarchyValidationService;
import com.ideas.tetris.pacman.services.bestavailablerate.*;
import com.ideas.tetris.pacman.services.bestavailablerate.dto.*;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.*;
import com.ideas.tetris.pacman.services.businessanalysis.dto.BusinessAnalysisDailyDataDto;
import com.ideas.tetris.pacman.services.businessanalysis.dto.BusinessAnalysisSpecialEventDto;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.TenantStatusEnum;
import com.ideas.tetris.pacman.services.dashboard.mapper.NumberUtils;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.dateservice.dto.DateParameter;
import com.ideas.tetris.pacman.services.decisiondelivery.dto.InventoryLimitDecision;
import com.ideas.tetris.pacman.services.eventaggregator.StalenessFlag;
import com.ideas.tetris.pacman.services.filemetadata.FileMetadataService;
import com.ideas.tetris.pacman.services.inventorygroup.entity.InventoryGroup;
import com.ideas.tetris.pacman.services.inventorygroup.entity.InventoryGroupDetails;
import com.ideas.tetris.pacman.services.inventorygroup.service.InventoryGroupService;
import com.ideas.tetris.pacman.services.inventorylimit.entity.DecisionGPInventoryLimitOverride;
import com.ideas.tetris.pacman.services.manualrestrictions.control.ManualRestrictionService;
import com.ideas.tetris.pacman.services.manualrestrictions.control.dto.ManualRestrictionRoomTypeIdentifier;
import com.ideas.tetris.pacman.services.manualrestrictions.control.dto.ManualRestrictionTypeDto;
import com.ideas.tetris.pacman.services.notes.NotesService;
import com.ideas.tetris.pacman.services.notes.entity.DateNote;
import com.ideas.tetris.pacman.services.pricing.ProductManagementService;
import com.ideas.tetris.pacman.services.pricingconfiguration.entity.PricingAccomClass;
import com.ideas.tetris.pacman.services.pricingconfiguration.service.PricingConfigurationLTBDEService;
import com.ideas.tetris.pacman.services.pricingconfiguration.service.PricingConfigurationService;
import com.ideas.tetris.pacman.services.product.OverridableProductEnum;
import com.ideas.tetris.pacman.services.product.Product;
import com.ideas.tetris.pacman.services.property.PropertyService;
import com.ideas.tetris.pacman.services.rdl.entity.WebrateTypeProduct;
import com.ideas.tetris.pacman.services.restfortesting.ForTesting;
import com.ideas.tetris.pacman.services.sasoptimization.dto.SimplifiedWhatIfResult;
import com.ideas.tetris.pacman.services.sasoptimization.service.SimplifiedWhatIfService;
import com.ideas.tetris.pacman.services.security.User;
import com.ideas.tetris.pacman.services.security.UserService;
import com.ideas.tetris.pacman.services.tax.entity.Tax;
import com.ideas.tetris.pacman.services.userfilteroptions.dto.FilterOptions;
import com.ideas.tetris.pacman.services.userfilteroptions.dto.PricingCalendarFilter;
import com.ideas.tetris.pacman.services.userfilteroptions.dto.PricingTabularFilter;
import com.ideas.tetris.pacman.services.userfilteroptions.service.UserFilterOptionsService;
import com.ideas.tetris.pacman.services.userpreferences.UserPreferences;
import com.ideas.tetris.pacman.services.webrate.entity.WebrateCompetitors;
import com.ideas.tetris.pacman.services.webrate.entity.WebrateView;
import com.ideas.tetris.pacman.util.BigDecimalUtil;
import com.ideas.tetris.platform.common.entity.AuditableEntity;
import com.ideas.tetris.platform.common.entity.DayOfWeek;
import com.ideas.tetris.platform.common.entity.FilterView;
import com.ideas.tetris.platform.common.errorhandling.ErrorCode;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.common.externalsystem.ExternalSystem;
import com.ideas.tetris.platform.common.time.JavaLocalDateUtils;
import com.ideas.tetris.platform.common.time.LocalDateUtils;
import com.ideas.tetris.platform.common.xml.schema.workcontext.v1.WorkContextType;
import com.ideas.tetris.platform.services.Stage;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import com.ideas.tetris.platform.services.daoandentities.entity.Status;
import com.ideas.tetris.spring.SpringAutowired;
import com.ideas.tetris.ui.common.cdi.TetrisDataThreadPool;
import com.ideas.tetris.ui.common.cdi.TetrisPresenter;
import com.ideas.tetris.ui.common.component.fontawesome.TetrisFontAwesome;
import com.ideas.tetris.ui.common.component.textfield.TetrisTextFieldV8;
import com.ideas.tetris.ui.common.data.util.converter.StringToBigDecimalConverter;
import com.ideas.tetris.ui.common.security.PropertyState;
import com.ideas.tetris.ui.common.util.DateUtil;
import com.ideas.tetris.ui.common.util.InventoryGroupUtil;
import com.ideas.tetris.ui.common.util.PageUtil;
import com.ideas.tetris.ui.common.util.UiUtils;
import com.ideas.tetris.ui.modules.ataglance.InventoryGroupDto;
import com.ideas.tetris.ui.modules.commons.continuouspricing.CPBARDecisionUIWrapper;
import com.ideas.tetris.ui.modules.commons.continuouspricing.CPOverrideWrapper;
import com.ideas.tetris.ui.modules.commons.continuouspricing.CPPricingFilter;
import com.ideas.tetris.ui.modules.commons.continuouspricing.DateWithFilterEnum;
import com.ideas.tetris.ui.modules.investigator.InvestigatorDto;
import com.ideas.tetris.ui.modules.investigator.InvestigatorUtil;
import com.ideas.tetris.ui.modules.investigator.popup.override.cp.views.InvestigatorOverridePopUpCPView;
import com.ideas.tetris.ui.modules.pricing.events.ApplyMultiProductOverrideEvent;
import com.ideas.tetris.ui.modules.pricing.events.SendCancelMultiProductOverrideEvent;
import com.ideas.tetris.ui.modules.pricing.events.StoreMultiProductOverrideEvent;
import com.ideas.tetris.ui.modules.pricing.multiproductmultiday.MultiProductMultidayRowType;
import com.ideas.tetris.ui.modules.pricing.multiproductmultiday.PricingMultiProductMultidayFilterDTO;
import com.ideas.tetris.ui.modules.pricing.multiproductmultiday.PricingMultidayGridDTO;
import com.ideas.tetris.ui.modules.pricing.views.*;
import com.ideas.tetris.ui.modules.pricing.views.inlineedit.PricingInlineEditColumn;
import com.ideas.tetris.ui.modules.pricingconfiguration.supplements.PricingConfigValidatorAgainstSupplement;
import com.ideas.tetris.ui.modules.pricingoverrides.PricingOverrideManager;
import com.ideas.tetris.ui.modules.reports.util.URLParamUtil;
import com.vaadin.data.Binder;
import com.vaadin.server.Page;
import com.vaadin.ui.UI;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.joda.time.LocalDate;
import org.springframework.beans.factory.annotation.Autowired;

import javax.enterprise.event.Observes;
import javax.enterprise.event.Reception;
import javax.enterprise.inject.Instance;
import javax.inject.Inject;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.function.Predicate;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;

import static com.ideas.tetris.pacman.services.agilerates.configuration.dto.AgileRatesProductTypeEnum.FENCED_AND_PACKAGED;
import static com.ideas.tetris.pacman.services.agilerates.configuration.dto.AgileRatesProductTypeEnum.UNFENCED_AND_PACKAGED;
import static com.ideas.tetris.pacman.services.bestavailablerate.DecisionReasonType.OVERRIDE_VIOLATES_RANKING_RULE;
import static com.ideas.tetris.pacman.services.product.Product.AGILE_RATES_PRODUCT_CODE;
import static com.ideas.tetris.pacman.services.product.Product.GROUP_PRODUCT_CODE;
import static com.ideas.tetris.platform.common.utils.systemconfig.SystemConfig.useOptimizedLoopCodePricing;
import static com.ideas.tetris.ui.common.util.UiUtils.hasWriteAccess;
import static com.ideas.tetris.ui.modules.commons.continuouspricing.DateWithFilterEnum.*;
import static java.util.Arrays.asList;
import static org.apache.commons.collections.CollectionUtils.isNotEmpty;
import static org.apache.commons.lang3.math.NumberUtils.isNumber;

@SpringAutowired
public class PricingPresenter extends TetrisPresenter<PricingView, Void> {
    public static final int MAX_ROOM_CLASS_TYPE_LABEL_LENGTH = 18;
    public static final int MAX_ROOM_CLASS_TYPE_CHECKBOX_LENGTH = 9;
    public static final int MAX_PRODUCT_LABEL_LENGTH = 19;
    public static final int MAX_PRODUCT_CHECKBOX_LENGTH = 15;
    public static final String PRODUCT_WITH_PACKAGE = "PRODUCT_WITH_PACKAGE";
    protected static final String NULL_STRING = " --";
    private static final int ALL_ROOM_CLASS_ID = -1;
    private static final String AGILE_RATES_ADJUSTMENTS_SEPARATOR = " to ";
    private static final Logger log = Logger.getLogger(PricingPresenter.class);

    public static final String ROUNDING_ROLE_NOT_SATISFIED = "Rounding Role not Satisfied";

    private static final String ALL_RULES_SATISFIED = "All Rules Satisfied";
    private static final String EXCEPTION_WHILE_LOADING_WHAT_IF_DATA = "Exception while loading what if data.";
    private static final String FAILED_LOADING_WHAT_IF = "failedLoadingWhatIf";
    private static final String SELECTED_DATE = "selectedDate";
    private static final float MAX_DATE_RANGE = 91;
    private List<PricingInventoryLimitOverrideHistoryRow> inventoryLimitHistoryOverrides = new ArrayList<>();
    private static final String PERCENTAGE = "%";
    private static final String DEFAULT_ADJUSTMENT = "0.00";
    public static final String HYPHEN = "--";
    public static final String PRODUCT_ID = "productId";
    private static final Integer PRIMARY_PRODUCT_ID = 1;

    @Autowired
    ProductManagementService service;
    @Autowired
	private PacmanConfigParamsService configParamsService;
    @Autowired
	private SimplifiedWhatIfService simplifiedWhatIfService;
    @Autowired
	private AccomTypeSupplementService accomTypeSupplementService;
    @Autowired
	private AccommodationService roomClassService;
    @Autowired
	private NotesService notesService;
    @Autowired
	private ManualRestrictionService manualRestrictionService;
    @Autowired
	private PricingConfigurationService pricingConfigurationService;
    @Autowired
	private OverrideService overrideService;
    @Autowired
	private PropertyService propertyService;
    @Autowired
	private FileMetadataService fileMetadataService;
    @Autowired
	private PrettyPricingService prettyPricingService;
    private String  message;
    @Autowired
	private DateService dateService;
    @Inject
    private Instance<InvestigatorOverridePopUpCPView> investigatorOverridePopUpCPViewFactory;
    @Autowired
	protected CloseHighestBarService closeHighestBarService;
    @Inject
    private MultiProductOverridePopUpView multiProductOverridePopUpView;
    @Autowired
	private UserService userService;
    @Autowired
	private AgileRatesConfigurationService agileRatesConfigurationService;
    @Autowired
	private CPManagementService cpManagementService;
    @Inject
    private PricingOverrideManager.Factory factory;
    @Autowired
	private InventoryGroupService inventoryGroupService;
    @Autowired
	private UserFilterOptionsService userFilterOptionsService;

    @Autowired
	private ProductHierarchyValidationService hierarchyValidationService;

    @Autowired
	private PricingConfigurationLTBDEService pricingConfigurationLTBDEService;

    @Inject
    private PricingConfigValidatorAgainstSupplement pricingConfigValidatorAgainstSupplement;

//    @Inject
//    private UiContext uiContext;

    private UserPreferences userPreferences;

    private String valueOfValidation;
    private PricingOverrideManager overrideManager;
    private List<AccomClass> roomClassList;
    private AccomClass allRoomClasses;
    private AccomType baseRoomType;
    private List<ProductAccomType> productATMapping;
    private List<CPOverrideWrapper> productOverrides;
    private List<CPOverrideWrapper> inventoryLimitOverrides;
    private boolean isLraEnabled;
    private boolean isAgileRatesEnabled;

    private boolean showAdjustments;
    private boolean isWebRateShoppingEnabled;
    private boolean isWhatIfEnabled;
    private boolean isSupplementalEnabled;
    private boolean isPropertyIsOneWayOrAbove;
    private boolean isChildAgeBucketsEnabled;
    private boolean isChildAgeBucketPackagesEnabled;
    private boolean isPerPersonPricingEnabled;
    private boolean isBarByLOS;
    private boolean isDisplayOccupancyForecastInWhatIfEnabled;
    private boolean isEnablePhysicalCapacityConsideration;
    private boolean isContinuousPricing;
    private boolean isGroupFloorEnabled;
    private boolean isManualUploadEnabled;
    private boolean isPropertyStageTwoWay;
    private Integer qualifiedFplosMaxLos;
    private boolean isHighestBarRestrictedEnabled;
    private boolean isPricingScreenOptimizationEnabled;
    private boolean isUseExtendedStayMapping;
    private int dailyBARPricingRule;
    private int maxLos;
    private List<CPBARDecisionUIWrapper> results;
    private boolean isIdpEnabled;
    private List<Product> allProducts;
    private Product barProduct;
    private List<DateNote> notes;
    private Map<LocalDate, ManualRestrictionTypeDto> manualRestrictionsPropertyDto;
    private Map<LocalDate, Map<ManualRestrictionRoomTypeIdentifier, ManualRestrictionTypeDto>> manualRestrictionsAccomDto;
    List<AccomType> highestBarClosedForAccomTypes;
    private List<PricingAccomClass> pricingAccomClasses;
    private List<BAROverride> pendingCloseLv0BarOverrides;
    private final StringToBigDecimalConverter strToDecimalConverter = new StringToBigDecimalConverter();
    private OptimizationLevel optimizationLevel;
    private List<CompetitorDetailsRow> allCompetitorDetailsRows;
    private List<WebrateView> allVwCompetitorDetailsRows;
    private List<PricingAgileRatesOverrideDTO> multiProductNonOptimizedDTOs;
    private List<PricingAgileRatesOverrideDTO> multiProductOptimizedDTOs;
    List<CloseLV0RowModel> rowModelListForCloseLv0;
    List<CloseLV0RowModel> originalRowModelListForCloseLv0;
    private List<CPOverrideWrapper> multiProductBARDTOs;
    private List<CPOverrideWrapper> appliedMultiProductBARDTOs;
    private List<PricingMultiProductOverrideDTOWrapper> multiProductAgileRatesDTOs;
    private List<WebrateTypeProduct> webrateTypeProducts;
    private boolean multiProductUnsavedChanges = false;
    private PricingToggleView selectedView = PricingToggleView.CARD;
    List<AgileRatesDTARange> allDTARanges;
    private AccomClass masterAccomClass;
    private OccupancyType baseOccupancyType;
    private List<ProductHierarchy> allHierarchies;
    private List<ProductRateOffset> allProductRateOffsets;
    private List<WebrateCompetitors> webrateCompetitors;
    private List<ProductGroup> allProductGroups;
    private String clientCode;
    private List<InventoryGroupDto> inventoryGroups;
    InventoryGroupDto defaultInventoryGroup;
    private List<InventoryGroupDetails> inventoryGroupDetails;
    private InventoryGroupDto lastSelectedInventoryGroup;
    private CPDecisionContext cpDecisionContext;
    private List<AccomClassPriceRank> allAccomClassPriceRanks;
    private boolean hasCentrallyManagedPermission;
    private boolean isReadOnly;
    private boolean isIndependentProductsEnabled;
    private boolean isSmallGroupProductsEnabled;
    private boolean isReadOnlyNotificationDisplayed = false;
    private boolean isSystemPriceEnabled;
    private boolean isAveragePriceEnabled;
    private boolean isRDLEnabled;
    private boolean isExtendedWindowEnabled;
    private boolean isFixedAboveBARProductEnabled;
    private Date optimizationWindowEndDateBDE;
    private boolean isOptimizedWarningFetch;

    private List<AccomType> allRoomTypes;
    private boolean isManualRestrictionEnabled;

    private boolean isGroupProductInventoryLimitEnabled;
    Map<Date, String> inventoryLimitMap;

    private List<CPOverrideWrapper> overrideWrapperForBar;

    Map<Date, String> inventoryLimitHistoryOverrideMapping = new HashMap<>();
    List<InventoryLimitDecision> inventoryLimits;
    List<InventoryLimitDecision> overriddenInventoryLimits;
     List<BusinessAnalysisDailyDataDto> businessAnalysisDailyDataDtos;
     private int pricingLoadWindowDays = 14;
    private boolean isAvailableCapacityToSellEnabled;

    @Override
    public void onViewInit() {
        userPreferences = getUserPreferences();
        if(SystemConfig.usePerformanceImprovementChangesToGetPageOnLoadDataInParallel()) {
            updateParametersEfficiently();
        } else {
            updateParameters();
        }
        LocalDate selectedStartDate = getSelectedDateFromURL();
        AccomClass selectedRoomClass = getSelectedRoomClassFromURL();
        determineView(true);
        pricingLoadWindowDays = getPricingLoadWindowDays();
        setViewCriteria(selectedStartDate, selectedStartDate.plusDays(pricingLoadWindowDays), selectedRoomClass);
        view.displayView(selectedView);
    }

    public int getPricingLoadWindowDays() {
        try {
            return pacmanConfigParamsService.getIntegerParameterValue(FeatureTogglesConfigParamName.DEFAULT_PRICING_WINDOW_THRESHOLD_FOR_LARGE_PROPERTIES.value());
        }catch (Exception e){
            log.warn("Exception occurred while fetching Pricing window. Hence defaulting to the original value for PropertyId  : " + PacmanWorkContextHelper.getPropertyId() + e);
            return pricingLoadWindowDays;
        }
    }

    public boolean isPricingOverrideManagerOverrideMapEmpty() {
        return overrideManager.getOverridesMap().isEmpty();
    }

    public boolean getIsReadOnlyNotificationDisplayed() {
        return isReadOnlyNotificationDisplayed;
    }

    public void setIsReadOnlyNotificationDisplayed(boolean isNotificationDisplayed) {
        this.isReadOnlyNotificationDisplayed = isNotificationDisplayed;
    }

    private void determineView(boolean loadViewingPreference) {
        if (loadViewingPreference) {
            selectedView = getViewingPreference();
        }
    }

    @Override
    public void onWorkContextChange(WorkContextType workContextType) {
        refreshUri();
        Optional<InventoryGroupDto> defaultInventoryGroupCheck = inventoryGroups.stream().filter(InventoryGroupDto::isDefaultPropertyInventoryGroup).findFirst();
        defaultInventoryGroup = defaultInventoryGroupCheck.orElseGet(() -> new InventoryGroupDto(-1, "Property", true));
        lastSelectedInventoryGroup = getCpPricingFilter().getInventoryGroupDto();
        if (workContextType.getClientCode().equals(clientCode)) {
            List<String> productNames = getCpPricingFilter().getProducts().stream().map(Product::getName).collect(Collectors.toList());
            String roomClassName = getCpPricingFilter().getSelectedRoomClass().getName();
            List<String> roomTypeNames = getCpPricingFilter().getSelectedRoomTypes().stream().map(AccomType::getName).collect(Collectors.toList());
            LocalDate startDate = getCpPricingFilter().getStartDate();
            LocalDate endDate = getCpPricingFilter().getEndDate();
            boolean isApplyToAllChecked = view.isApplyOverridesToAllRoomClassesChecked();
            if(SystemConfig.usePerformanceImprovementChangesToGetPageOnLoadDataInParallel()) {
                updateParametersEfficiently();
            } else {
                updateParameters();
            }
            setViewCriteriaOnWorkContextChange(startDate, endDate, productNames, roomClassName, roomTypeNames, isApplyToAllChecked);
        } else {
            if(SystemConfig.usePerformanceImprovementChangesToGetPageOnLoadDataInParallel()) {
                updateParametersEfficiently();
            } else {
                updateParameters();
            }
            pricingLoadWindowDays = getPricingLoadWindowDays();
            setViewCriteria(getSystemDateAsLocalDate(), getSystemDateAsLocalDate().plusDays(pricingLoadWindowDays), allRoomClasses);
        }
        determineView(false);
        view.displayView(selectedView);
        view.hideIncorrectDateLayout();
        view.initFilterComponents();
        view.updateCompetitorsAfterViewSwitch();
        view.updateDecisions(true);
    }
    public PricingToggleView getSelectedView() {
        return selectedView;
    }

    public LocalDate getSelectedDateFromURL() {
        //TODO update Investigator to use timeFrame instead of selectedDate
        if (urlParameters.containsKey(SELECTED_DATE)) {
            return new LocalDate(urlParameters.get(SELECTED_DATE)[0]);
        } else if (URLParamUtil.containsTimeFrame(getUrlParameters())) {
            String[] dates = URLParamUtil.getTimeFrameParamValue(getUrlParameters());
            try {
                return JavaLocalDateUtils.toJodaLocalDate(URLParamUtil.extractStartDateFromTimeFrame(dates[0]));
            } catch (IllegalArgumentException iae) {
                logger.error("Illegal formatting in timeframe parameter in URL", iae);
            }
        }
        return getSystemDateAsLocalDate();
    }

    public AccomClass getSelectedRoomClassFromURL() {
        if (URLParamUtil.containsSubLevel(urlParameters)) {
            String roomClassName = URLParamUtil.getSubLevelParamValue(urlParameters);
            return roomClassList.stream().filter(accomClass -> accomClass.getName().equalsIgnoreCase(roomClassName)).findFirst().orElse(allRoomClasses);
        }
        return allRoomClasses;
    }

    private void updateParameters() {
        long s1 = System.currentTimeMillis();
        overrideManager = factory.create();
        isFixedAboveBARProductEnabled = configParamsService.getBooleanParameterValue(GUIConfigParamName.FIXED_ABOVE_BAR_PRODUCT_ENABLED);
        isLraEnabled = configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.LRAENABLED.value());
        isAgileRatesEnabled = configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.AGILE_RATES_ENABLED.value());
        isIndependentProductsEnabled = configParamsService.getBooleanParameterValue(PreProductionConfigParamName.INDEPENDENT_PRODUCTS_ENABLED);
        isSmallGroupProductsEnabled = configParamsService.getBooleanParameterValue(PreProductionConfigParamName.SMALL_GROUP_PRODUCT_ENABLED);
        isWebRateShoppingEnabled = configParamsService.getBooleanParameterValue(IPConfigParamName.BAR_WEB_RATE_SHOPPING_ENABLED.value());
        isWhatIfEnabled = configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.WHAT_IF_ENABLED.value());
        isSupplementalEnabled = isSupplementalEnabled();
        isAvailableCapacityToSellEnabled = configParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_AVAILABLE_CAPACITY_TO_SELL_AT_ROOM_TYPE.value());
        overrideManager.setAvailableCapacityToSellEnabled(isAvailableCapacityToSellEnabled);
        overrideManager.setupHospitalityRooms();
        overrideManager.setShowOnlyBaseRoomTypesFlag(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.CPBASE_ROOM_TYPE_ONLY_ENABLED));
        maxLos = Integer.parseInt(configParamsService.getParameterValue(IPConfigParamName.BAR_MAX_LOS.value()));
        isPropertyIsOneWayOrAbove = propertyService.isStageAtLeast(Stage.ONE_WAY, uiContext.getPropertyId());
        isBarByLOS = Constants.BAR_DECISION_VALUE_LOS.equals(configParamsService.getParameterValue(IPConfigParamName.BAR_BAR_DECISION.value()));
        isDisplayOccupancyForecastInWhatIfEnabled = simplifiedWhatIfService.isDisplayOccupancyForecastInWhatIfToggleEnabled();
        isEnablePhysicalCapacityConsideration = configParamsService.isEnablePhysicalCapacityConsideration();
        isContinuousPricing = configParamsService.getBooleanParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED.value());
        isGroupFloorEnabled = configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.IS_GROUP_FLOOR_OVERRIDE_ENABLED);
        isManualUploadEnabled = configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.ENABLE_MANUAL_BARUPLOAD.value());
        isManualRestrictionEnabled = configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.MANUAL_RESTRICTION_ENABLED);
        isPropertyStageTwoWay = Stage.TWO_WAY.equals(propertyService.getPropertyStage(uiContext.getPropertyId()));
        qualifiedFplosMaxLos = configParamsService.getIntegerParameterValue(IntegrationConfigParamName.CORE_PROPERTY_QUALIFIED_FPLOSMAX_LOS.value());
        isHighestBarRestrictedEnabled = configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.HIGHEST_BAR_RESTRICTED_ENABLED.value());
        isPricingScreenOptimizationEnabled = configParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_PRICING_SCREEN_OPTIMIZATION.value());
        isUseExtendedStayMapping = Constants.TRUE.equalsIgnoreCase(configParamsService.getParameterValue(IntegrationConfigParamName.USE_EXTENDED_STAY_MAPPING, ExternalSystem.RATCHET));
        isIdpEnabled = fileMetadataService.getLastIDPDate() != null;
        overrideManager.setPseudoShowOnlyBaseRoomTypesFlag(false);
        overrideManager.setLocale(view.getLocale());
        allProducts = findAllActiveProducts();
        showAdjustments = isAgileRatesEnabled && agileProductsPresent();
        productATMapping = agileRatesConfigurationService.findProductRoomTypeByProducts(new HashSet<>(allProducts), isPricingScreenOptimizationEnabled);
        allRoomClasses = new AccomClass();
        allRoomClasses.setName(getText("all"));
        allRoomClasses.setId(ALL_ROOM_CLASS_ID);
        baseRoomType = new AccomType();
        baseRoomType.setName(getText("common.baseRoomType"));
        baseRoomType.setStatusId(1);
        baseRoomType.setAccomTypeCapacity(1);
        baseRoomType.setAccomClass(allRoomClasses);
        allRoomClasses.setAccomTypes(Collections.singleton(baseRoomType));
        roomClassList = removeRoomClassesWithoutRoomTypes(service.getSortedAccomClasses(isPricingScreenOptimizationEnabled));
        setupRoomClasses(roomClassList);
        roomClassList.add(0, allRoomClasses);
        pricingAccomClasses = overrideManager.getPricingAccomClasses();
        optimizationLevel = service.getOptimizationLevel();
        view.setButtonEnableRequirements(isEffectiveReadOnlyEnhance());
        isPerPersonPricingEnabled = configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.PER_PERSON_PRICING_ENABLED.value());
        isChildAgeBucketsEnabled = configParamsService.getBooleanParameterValue(PreProductionConfigParamName.CP_CHILD_AGE_BUCKETS_ENABLED.value());
        isChildAgeBucketPackagesEnabled = configParamsService.getBooleanParameterValue(PreProductionConfigParamName.CHILD_AGE_BUCKET_PACKAGES_ENABLED.value());
        multiProductBARDTOs = new ArrayList<>();
        appliedMultiProductBARDTOs = new ArrayList<>();
        multiProductNonOptimizedDTOs = new ArrayList<>();
        multiProductOptimizedDTOs = new ArrayList<>();
        multiProductAgileRatesDTOs = new ArrayList<>();
        allDTARanges = new ArrayList<>();
        pendingCloseLv0BarOverrides = new ArrayList<>();
        masterAccomClass = null;
        allCompetitorDetailsRows = new ArrayList<>();
        allVwCompetitorDetailsRows = new ArrayList<>();
        webrateTypeProducts = agileRatesConfigurationService.findAllWebrateTypeProduct();
        baseOccupancyType = pricingConfigurationService.getBaseOccupancyType();
        allHierarchies = new ArrayList<>();
        allProductRateOffsets = new ArrayList<>();
        webrateCompetitors = new ArrayList<>();
        allProductGroups = new ArrayList<>();
        clientCode = PacmanWorkContextHelper.getClientCode();
        dailyBARPricingRule = configParamsService.getIntegerParameterValue(IPConfigParamName.DAILY_BAR_PRICING_RULE_TYPE.value());
        cpDecisionContext = null;
        allAccomClassPriceRanks = new ArrayList<>();
        hasCentrallyManagedPermission = hasWriteAccess(TetrisPermissionKey.PRICING_CENTRALLY_MANAGED);
        isReadOnly = !hasWriteAccess(TetrisPermissionKey.PRICING);
        barProduct = allProducts.stream().filter(p -> p.isSystemDefault()).findAny().get();
        isSystemPriceEnabled = configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.SYSTEM_PRICE_ENABLED);
        isAveragePriceEnabled = configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.AVERAGE_PRICE_ENABLED);
        isRDLEnabled = configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.IS_RDL_ENABLED);
        isOptimizedWarningFetch = configParamsService.getBooleanParameterValue(PreProductionConfigParamName.IS_OPTIMIZED_WARNING_FETCH_FOR_PRICING_SCREEN.value());
        isExtendedWindowEnabled = configParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_UPLOAD_ON_OVERRIDE_FOR_EXTENDED_WINDOW);
        allRoomTypes = null;
        if (isExtendedWindowEnabled) {
            optimizationWindowEndDateBDE = dateService.getOptimizationWindowEndDateBDE();
        }
        isGroupProductInventoryLimitEnabled = configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.IS_GROUP_PRODUCT_INVENTORY_LIMIT_ENABLED);
        long s2 = System.currentTimeMillis();
        log.info("updateParameters=" + (s2 - s1));
    }

    private void setupRoomClasses(List<AccomClass> roomClassList) {
        overrideManager.setAccomClassIds(roomClassList.stream().map(AccomClass::getId).collect(Collectors.toList()));
    }

    private void updateParametersEfficiently() {
        long s1 = System.currentTimeMillis();
        overrideManager = factory.create();
        isFixedAboveBARProductEnabled = configParamsService.getBooleanParameterValue(GUIConfigParamName.FIXED_ABOVE_BAR_PRODUCT_ENABLED);
        isLraEnabled = configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.LRAENABLED.value());
        isAgileRatesEnabled = configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.AGILE_RATES_ENABLED.value());
        isIndependentProductsEnabled = configParamsService.getBooleanParameterValue(PreProductionConfigParamName.INDEPENDENT_PRODUCTS_ENABLED);
        isSmallGroupProductsEnabled = configParamsService.getBooleanParameterValue(PreProductionConfigParamName.SMALL_GROUP_PRODUCT_ENABLED);
        isWebRateShoppingEnabled = configParamsService.getBooleanParameterValue(IPConfigParamName.BAR_WEB_RATE_SHOPPING_ENABLED.value());
        isWhatIfEnabled = configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.WHAT_IF_ENABLED.value());
        overrideManager.setShowOnlyBaseRoomTypesFlag(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.CPBASE_ROOM_TYPE_ONLY_ENABLED));
        isPricingScreenOptimizationEnabled = configParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_PRICING_SCREEN_OPTIMIZATION.value());
        isAvailableCapacityToSellEnabled = configParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_AVAILABLE_CAPACITY_TO_SELL_AT_ROOM_TYPE.value());
        overrideManager.setAvailableCapacityToSellEnabled(isAvailableCapacityToSellEnabled);
        overrideManager.setupHospitalityRooms();
        maxLos = Integer.parseInt(configParamsService.getParameterValue(IPConfigParamName.BAR_MAX_LOS.value()));
        isBarByLOS = Constants.BAR_DECISION_VALUE_LOS.equals(configParamsService.getParameterValue(IPConfigParamName.BAR_BAR_DECISION.value()));
        isDisplayOccupancyForecastInWhatIfEnabled = simplifiedWhatIfService.isDisplayOccupancyForecastInWhatIfToggleEnabled();
        isEnablePhysicalCapacityConsideration = configParamsService.isEnablePhysicalCapacityConsideration();
        isContinuousPricing = configParamsService.getBooleanParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED.value());
        isGroupFloorEnabled = configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.IS_GROUP_FLOOR_OVERRIDE_ENABLED);
        isManualUploadEnabled = configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.ENABLE_MANUAL_BARUPLOAD.value());
        isManualRestrictionEnabled = configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.MANUAL_RESTRICTION_ENABLED);
        qualifiedFplosMaxLos = configParamsService.getIntegerParameterValue(IntegrationConfigParamName.CORE_PROPERTY_QUALIFIED_FPLOSMAX_LOS.value());
        isHighestBarRestrictedEnabled = configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.HIGHEST_BAR_RESTRICTED_ENABLED.value());
        isUseExtendedStayMapping = Constants.TRUE.equalsIgnoreCase(configParamsService.getParameterValue(IntegrationConfigParamName.USE_EXTENDED_STAY_MAPPING, ExternalSystem.RATCHET));
        isPerPersonPricingEnabled = configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.PER_PERSON_PRICING_ENABLED.value());
        isChildAgeBucketsEnabled = configParamsService.getBooleanParameterValue(PreProductionConfigParamName.CP_CHILD_AGE_BUCKETS_ENABLED.value());
        isChildAgeBucketPackagesEnabled = configParamsService.getBooleanParameterValue(PreProductionConfigParamName.CHILD_AGE_BUCKET_PACKAGES_ENABLED.value());
        dailyBARPricingRule = configParamsService.getIntegerParameterValue(IPConfigParamName.DAILY_BAR_PRICING_RULE_TYPE.value());
        isSystemPriceEnabled = configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.SYSTEM_PRICE_ENABLED);
        isAveragePriceEnabled = configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.AVERAGE_PRICE_ENABLED);
        isRDLEnabled = configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.IS_RDL_ENABLED);
        isOptimizedWarningFetch = configParamsService.getBooleanParameterValue(PreProductionConfigParamName.IS_OPTIMIZED_WARNING_FETCH_FOR_PRICING_SCREEN.value());
        isExtendedWindowEnabled = configParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_UPLOAD_ON_OVERRIDE_FOR_EXTENDED_WINDOW);
        isGroupProductInventoryLimitEnabled = configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.IS_GROUP_PRODUCT_INVENTORY_LIMIT_ENABLED);

        isPropertyStageTwoWay = Stage.TWO_WAY.equals(propertyService.getPropertyStage(uiContext.getPropertyId()));
        isPropertyIsOneWayOrAbove = propertyService.isStageAtLeast(Stage.ONE_WAY, uiContext.getPropertyId());
        isSupplementalEnabled = isSupplementalEnabled();
        isIdpEnabled = fileMetadataService.getLastIDPDate() != null;
        overrideManager.setPseudoShowOnlyBaseRoomTypesFlag(false);
        overrideManager.setLocale(view.getLocale());

        allProducts = findAllActiveProducts();

        getPageOnLoadDataInParallel();

        allRoomClasses = new AccomClass();
        allRoomClasses.setName(getText("all"));
        allRoomClasses.setId(ALL_ROOM_CLASS_ID);
        baseRoomType = new AccomType();
        baseRoomType.setName(getText("common.baseRoomType"));
        baseRoomType.setStatusId(1);
        baseRoomType.setAccomTypeCapacity(1);
        baseRoomType.setAccomClass(allRoomClasses);
        allRoomClasses.setAccomTypes(Collections.singleton(baseRoomType));
        roomClassList.add(0, allRoomClasses);
        view.setButtonEnableRequirements(isEffectiveReadOnlyEnhance());
        multiProductBARDTOs = new ArrayList<>();
        appliedMultiProductBARDTOs = new ArrayList<>();
        multiProductNonOptimizedDTOs = new ArrayList<>();
        multiProductOptimizedDTOs = new ArrayList<>();
        multiProductAgileRatesDTOs = new ArrayList<>();
        allDTARanges = new ArrayList<>();
        pendingCloseLv0BarOverrides = new ArrayList<>();
        masterAccomClass = null;
        allCompetitorDetailsRows = new ArrayList<>();
        allVwCompetitorDetailsRows = new ArrayList<>();
        allHierarchies = new ArrayList<>();
        allProductRateOffsets = new ArrayList<>();
        webrateCompetitors = new ArrayList<>();
        allProductGroups = new ArrayList<>();
        clientCode = PacmanWorkContextHelper.getClientCode();
        cpDecisionContext = null;
        allAccomClassPriceRanks = new ArrayList<>();
        hasCentrallyManagedPermission = hasWriteAccess(TetrisPermissionKey.PRICING_CENTRALLY_MANAGED);
        isReadOnly = !hasWriteAccess(TetrisPermissionKey.PRICING);
        barProduct = allProducts.stream().filter(p -> p.isSystemDefault()).findAny().get();
        allRoomTypes = null;
        long s2 = System.currentTimeMillis();
        log.info("updateParametersEfficiently=" + (s2 - s1));
    }

    public void getPageOnLoadDataInParallel() {
        Map<String, Supplier<Object>> requestMap = new HashMap<>();
        requestMap.put("showAdjustments", () -> isAgileRatesEnabled && agileProductsPresent());
        requestMap.put("productATMapping", () -> agileRatesConfigurationService.findProductRoomTypeByProducts(new HashSet<>(allProducts), isPricingScreenOptimizationEnabled));
        requestMap.put("roomClassList", () -> removeRoomClassesWithoutRoomTypes(service.getSortedAccomClasses(isPricingScreenOptimizationEnabled)));
        requestMap.put("pricingAccomClasses", () -> overrideManager.getPricingAccomClasses());
        requestMap.put("optimizationLevel", () -> service.getOptimizationLevel());
        requestMap.put("baseOccupancyType", () -> pricingConfigurationService.getBaseOccupancyType());
        requestMap.put("webrateTypeProducts", () -> agileRatesConfigurationService.findAllWebrateTypeProduct());

        if (isExtendedWindowEnabled) {
            requestMap.put("optimizationWindowEndDateBDE", () -> dateService.getOptimizationWindowEndDateBDE());
        }

        TetrisDataThreadPool<Object> dataThreadPool = new TetrisDataThreadPool<>(TetrisDataThreadPool.getThreadPoolExecutor(requestMap.size(), "prc-page-load-data"), UI.getCurrent());
        Map<String, Object> resultMap = dataThreadPool.executeSupplierMap(requestMap, true);

        showAdjustments = (boolean) resultMap.get("showAdjustments");
        productATMapping = (List<ProductAccomType>) resultMap.get("productATMapping");
        roomClassList = (List<AccomClass>) resultMap.get("roomClassList");
        pricingAccomClasses = (List<PricingAccomClass>) resultMap.get("pricingAccomClasses");
        optimizationLevel = (OptimizationLevel) resultMap.get("optimizationLevel");
        baseOccupancyType = (OccupancyType) resultMap.get("baseOccupancyType");
        webrateTypeProducts = (List<WebrateTypeProduct>) resultMap.get("webrateTypeProducts");

        if (isExtendedWindowEnabled) {
            (optimizationWindowEndDateBDE) = (Date) resultMap.get("optimizationWindowEndDateBDE");
        }
    }

    public boolean isWhatIfButtonVisible() {
        if (null != getCpPricingFilter() && null != getCpPricingFilter().getJavaEndDate()) {
            return !isDateInExtendedWindow(getCpPricingFilter().getJavaEndDate());
        }
        return true;
    }

    public boolean isDateInExtendedWindow(java.time.LocalDate localDate) {
        if (null != localDate) {
            return pricingConfigurationLTBDEService.isDateInExtendedWindowForOverrides(localDate, getSystemDateAsJavaLocalDate());
        }
        return false;
    }

    @VisibleForTesting
    List<AccomClass> removeRoomClassesWithoutRoomTypes(List<AccomClass> accomClasses) {
        return accomClasses.stream()
                .filter(accomClass -> CollectionUtils.isNotEmpty(accomClass.getAccomTypes()))
                .collect(Collectors.toList());
    }

    public List<CPOverrideWrapper> getOverrideWrapperForBar() {
        return overrideWrapperForBar;
    }

    public void setOverrideWrapperForBar(List<CPOverrideWrapper> overrideWrapperForBar) {
        this.overrideWrapperForBar = overrideWrapperForBar;
    }

    private boolean agileProductsPresent() {
        return allProducts.stream().filter(p -> p.getCode().equals(AGILE_RATES_PRODUCT_CODE)).findAny().isPresent();
    }

    private void setViewCriteriaOnWorkContextChange(LocalDate startDate, LocalDate endDate, List<String> productNames, String accomClassName,
                                                    List<String> accomTypeNames, boolean isApplyToAllChecked) {
        inventoryLimitMap = new HashMap<>();
        inventoryLimits = new ArrayList<>();
        overriddenInventoryLimits = new ArrayList<>();
        productOverrides = new ArrayList<>();
        overrideManager.setProductOverrides(productOverrides);
        inventoryLimitOverrides = new ArrayList<>();
        overrideManager.setInventoryLimitOverrides(inventoryLimitOverrides);

        view.setProducts(allProducts);
        view.showProducts(isAgileRatesEnabled());

        view.showRoomTypeLayout(isShowOnlyBaseRoomTypesFlag());

        overrideManager.setBaseRoomTypeList(pricingAccomClasses.stream().map(PricingAccomClass::getAccomType).collect(Collectors.toList()));

        view.setAccomClasses(roomClassList);

        setInventoryGroups();
        checkLastSelectedInventoryGroup();

        List<Product> sharedProducts = allProducts.stream()
                .filter(p -> productNames.contains(p.getName()))
                .collect(Collectors.toList());
        AccomClass sharedAccomClass = getRoomClassListExcludingAll().stream()
                .filter(accomClass -> accomClass.getName().equals(accomClassName))
                .findAny().orElse(null);
        overrideManager.setCpPricingFilter(createCPPricingFilterOnWorkContextChange(startDate, endDate, sharedProducts, sharedAccomClass, defaultInventoryGroup));
        List<AccomType> sharedAccomTypes = getRoomTypesForSelectedRoomClass().stream()
                .filter(accomType -> accomTypeNames.contains(accomType.getName()))
                .collect(Collectors.toList());
        view.updateUploadEnabled(isManualUploadEnabled(), isPropertyStageTwoWay(), isBarOverridePendingForUpload(), isAgileRatesPendingForUpload(), isInventoryLimitPendingForUpload());
        view.updateWhatIfEnabled(isWhatIfEnabled());

        view.showApplyOverridesToAllRoomClassesCheckbox();
        boolean isAgileRatesProductInSharedProducts = sharedProducts.stream().anyMatch(p -> !p.isSystemDefaultOrIndependentProduct());
        if (isAgileRatesProductInSharedProducts) {
            view.enableApplyOverrideToAllOnContextChange();
        }
        if (isApplyToAllChecked && isAgileRatesProductInSharedProducts) {
            view.setApplyOverrideToAllOnContextChange();
            getCpPricingFilter().setApplyOverridesToAllRoomClasses(true);
        }

        //Set search criteria
        // if we have products saved in advance filter, use those, else if there are shared products, use those, else use BAR
        Set<Integer> savedProductIds = getProductIdsSavedAsDefaultSettings();
        if (savedProductIds.isEmpty()) {
            view.setSearchCriteria(getCpPricingFilter());
            if (!sharedProducts.isEmpty()) {
                view.setProductOnContextChange(sharedProducts);
                getCpPricingFilter().setProducts(new HashSet<>(sharedProducts));
            }
        } else {
            CPPricingFilter filter = getCpPricingFilter();
            addSavedProductsFromDefaultSettings(filter, allProducts, savedProductIds);
            view.setSearchCriteria(filter);
        }
        if (!sharedAccomTypes.isEmpty() && !isShowOnlyBaseRoomTypesFlag()) {
            view.setAccomTypesOnContextChange(sharedAccomTypes);
            getCpPricingFilter().setSelectedRoomTypes(new HashSet<>(sharedAccomTypes));
        }
        if (isGroupProductInventoryLimitEnabled) {
            Date sDate = startDate.toDate();
            Date eDate = endDate.toDate();
            inventoryLimitMap = overrideManager.buildMapOfInventoryLimit(sDate, eDate);
            inventoryLimits = overrideManager.getInventoryLimitDecisions(sDate, eDate);
            overriddenInventoryLimits = overrideManager.getOverriddenInventoryLimits(sDate, eDate);
            businessAnalysisDailyDataDtos = overrideManager.getBusinessAnalysisDailyDataDtos(getCaughtUpDate(), sDate, eDate);
        }
    }

    public String getInventoryLimitHistoryOverrideMapping(Date date) {
        return overriddenInventoryLimits.stream()
            .filter(override -> date.equals(override.getOccupancyDate()))
            .map(override -> String.valueOf(override.getInventoryLimit()))
            .findFirst()
            .orElse(HYPHEN);
    }

    private void setViewCriteria(LocalDate startDate, LocalDate endDate, AccomClass accomClass) {
        inventoryLimitMap = new HashMap<>();
        inventoryLimits = new ArrayList<>();
        overriddenInventoryLimits = new ArrayList<>();
        productOverrides = new ArrayList<>();
        overrideManager.setProductOverrides(productOverrides);
        inventoryLimitOverrides = new ArrayList<>();
        overrideManager.setInventoryLimitOverrides(inventoryLimitOverrides);

        view.setProducts(allProducts);

        view.showProducts(isAgileRatesEnabled());

        view.showRoomTypeLayout(isShowOnlyBaseRoomTypesFlag());

        overrideManager.setBaseRoomTypeList(pricingAccomClasses.stream().map(PricingAccomClass::getAccomType).collect(Collectors.toList()));

        view.setAccomClasses(roomClassList);
        setInventoryGroups();
        checkLastSelectedInventoryGroup();

        overrideManager.setCpPricingFilter(createDefaultCPPricingFilter(startDate, endDate, accomClass, defaultInventoryGroup));

        view.updateUploadEnabled(isManualUploadEnabled(), isPropertyStageTwoWay(), isBarOverridePendingForUpload(), isAgileRatesPendingForUpload(), isInventoryLimitPendingForUpload());
        view.updateWhatIfEnabled(isWhatIfEnabled());

        view.showApplyOverridesToAllRoomClassesCheckbox();

        //Set search criteria
        CPPricingFilter filter = getCpPricingFilter();
        Set<Integer> savedProductIds = getProductIdsSavedAsDefaultSettings();
        if (!savedProductIds.isEmpty()) {
            addSavedProductsFromDefaultSettings(filter, allProducts, savedProductIds);
        }
        view.setSearchCriteria(filter);
        if (isGroupProductInventoryLimitEnabled) {
            Date sDate = startDate.toDate();
            Date eDate = endDate.toDate();
            inventoryLimitMap = overrideManager.buildMapOfInventoryLimit(sDate, eDate);
            inventoryLimits = overrideManager.getInventoryLimitDecisions(sDate, eDate);
            overriddenInventoryLimits = overrideManager.getOverriddenInventoryLimits(sDate, eDate);
            businessAnalysisDailyDataDtos = overrideManager.getBusinessAnalysisDailyDataDtos(getCaughtUpDate(), sDate, eDate);
        }
    }

    private void addSavedProductsFromDefaultSettings(CPPricingFilter filter, List<Product> products, Set<Integer> defaultSettingProducts) {
        Set<Product> selectedProducts = products.stream()
                .filter(product -> defaultSettingProducts.contains(product.getId())).collect(Collectors.toSet());
        // this check takes care of scenario where all saved products were deleted after saving in default setting
        if (!selectedProducts.isEmpty()) {
            filter.setProducts(selectedProducts);
        }
    }

    @VisibleForTesting
    Set<Integer> getProductIdsSavedAsDefaultSettings() {
        Set<Integer> savedProductIds = new HashSet<>();
        if (PricingToggleView.CARD == selectedView) {
            PricingCalendarFilter savedCalendarFilter =
                    (PricingCalendarFilter) getSavedFilterSettings(FilterView.PRICINGCALENDAR.getViewString());
            if (savedCalendarFilter != null && savedCalendarFilter.getProductIds() != null) {
                savedProductIds.addAll(savedCalendarFilter.getProductIds());
            }
        } else {
            PricingTabularFilter savedTabularFilter =
                    (PricingTabularFilter) getSavedFilterSettings(FilterView.PRICINGTABULAR.getViewString());
            if (savedTabularFilter != null && savedTabularFilter.getProductIds() != null) {
                savedProductIds.addAll(savedTabularFilter.getProductIds());
            }
        }
        return savedProductIds;
    }


    public boolean isInventoryLimitOverrideDifferent(boolean hasOriginalOverride,boolean hasCurrentOverride,boolean hasCurrentLimit,CPOverrideWrapper inventoryOverride)
    {
        return (hasOriginalOverride && hasCurrentOverride &&
                !Objects.equals(inventoryOverride.getOriginalInventoryLimitOverride(),
                        inventoryOverride.getInventoryLimitOverride())) ||
                (hasCurrentLimit && hasCurrentOverride &&
                        !Objects.equals(inventoryOverride.getInventoryLimit(),
                                inventoryOverride.getInventoryLimitOverride())) ||
                (hasCurrentOverride && !hasCurrentLimit && !hasOriginalOverride) ||
                (!hasOriginalOverride && !hasCurrentOverride) ||
                (hasOriginalOverride && !hasCurrentOverride);
    }

    public CPPricingFilter createDefaultCPPricingFilter(LocalDate startDate, LocalDate endDate, AccomClass accomClass, InventoryGroupDto defaultInventoryGroup) {
        CPPricingFilter filter = new CPPricingFilter();
        filter.setStartDate(startDate);
        filter.setEndDate(endDate);
        filter.setProducts(new HashSet<>(asList(allProducts.get(getSelectedProductIndex()))));
        filter.setSelectedRoomClass(accomClass);
        filter.setSelectedRoomTypes(new HashSet<>());
        filter.setDateWithFilterEnum(DateWithFilterEnum.ALL_DECISIONS);
        filter.setInventoryGroupDto(defaultInventoryGroup);
        return filter;
    }

    public CPPricingFilter createCPPricingFilterOnWorkContextChange(LocalDate startDate, LocalDate endDate, List<Product> products,
                                                                    AccomClass accomClass, InventoryGroupDto defaultInventoryGroup) {
        CPPricingFilter filter = new CPPricingFilter();
        filter.setStartDate(startDate);
        filter.setEndDate(endDate);
        if (products.isEmpty()) {
            filter.setProducts(new HashSet<>(asList(allProducts.get(0))));
        } else {
            filter.setProducts(new HashSet<>(products));
        }
        filter.setSelectedRoomClass(accomClass == null ? allRoomClasses : accomClass);
        filter.setSelectedRoomTypes(new HashSet<>());
        filter.setDateWithFilterEnum(DateWithFilterEnum.ALL_DECISIONS);
        filter.setInventoryGroupDto(defaultInventoryGroup);
        return filter;
    }

    public List<PricingInventoryLimitOverrideHistoryRow> getInventoryLimitHistoryOverrides() {
        return inventoryLimitHistoryOverrides;
    }

    public void setInventoryLimitHistoryOverrides(List<PricingInventoryLimitOverrideHistoryRow> inventoryLimitHistoryOverrides) {
        this.inventoryLimitHistoryOverrides = inventoryLimitHistoryOverrides;
    }

    protected void refreshUri() {
        Page currentPage = Page.getCurrent();
        if (StringUtils.containsAny(String.valueOf(currentPage.getLocation()), "timeframe", PRODUCT_ID)) {
            String url = PageUtil.getUrl(TetrisPermissionKey.PRICING);
            currentPage.setLocation(url + "#/p" + uiContext.getPropertyId());
        }
    }

    protected Integer getSelectedProductIdFromURL() {
        return urlParameters.containsKey(PRODUCT_ID) ? Integer.parseInt(urlParameters.get(PRODUCT_ID)[0]) :
                service.getSystemDefaultProduct().getId();
    }

    protected int getSelectedProductIndex() {
        Integer selectedProductIdFromURL = getSelectedProductIdFromURL();
        return IntStream.range(0, allProducts.size())
                .filter(i -> Objects.equals(allProducts.get(i).getId(), selectedProductIdFromURL))
                .findFirst()
                .orElse(0);
    }

    protected List<Product> findAllActiveProducts() {
        List<Product> products;
        if (isIndependentProductsEnabled && isSmallGroupProductsEnabled) {
            products = service.findAllActiveProducts();
        } else if (isIndependentProductsEnabled) {
            products = service.findSystemDefaultAndAllActiveLinkedAndIndependentProducts();
        } else if (isSmallGroupProductsEnabled) {
            products = service.findSystemDefaultAndAllActiveLinkedAndSmallGroupProducts();
        } else {
            products = service.findSystemDefaultAndAllActiveLinkedProducts();
        }
        products.sort(Comparator.comparing(Product::getDisplayOrder));
        return products;
    }

    public Set<Product> sortProducts(Set<Product> selectedProducts) {
        List<Product> products = new ArrayList<>(selectedProducts);
        products.sort(Comparator.comparing(Product::getDisplayOrder));
        return new LinkedHashSet<>(products);
    }

    @ForTesting
    public void setClientCode(String clientCode) {
        this.clientCode = clientCode;
    }

    private Set<ProductRateOffsetOverride> getProductRateOffsetOverrides() {
        return overrideManager.getActiveProductRateOffsetOverrides();
    }

    private Set<ProductRateOffsetOverride> getProductRateOffsetOverridesWithProductGroups() {
        return overrideManager.getActiveProductRateOffsetOverridesWithProductGroups();
    }

    @ForTesting
    public void setProductRateOffsetOverrides(Set<ProductRateOffsetOverride> overrides) {
        overrideManager.setAllProductRateOffsetOverrides(overrides);
    }

    public boolean showInvestigator() {
        return isPropertyIsOneWayOrAbove;
    }

    public boolean isAgileRatesEnabled() {
        return isAgileRatesEnabled;
    }

    @ForTesting
    public void setAgileRatesEnabled(boolean agileRatesEnabled) {
        isAgileRatesEnabled = agileRatesEnabled;
    }

    public boolean isWebRateShoppingEnabled() {
        return isWebRateShoppingEnabled;
    }

    @ForTesting
    public void setWebRateShoppingEnabled(boolean webRateShoppingEnabled) {
        isWebRateShoppingEnabled = webRateShoppingEnabled;
    }

    public boolean hasCentrallyManagedPermission() {
        return hasCentrallyManagedPermission;
    }

    public boolean isWhatIfEnabled() {
        return isWhatIfEnabled;
    }

    public boolean dateOutOfRange(java.time.LocalDate startDate, java.time.LocalDate endDate) {
        if (startDate != null && endDate != null) {
            return ChronoUnit.DAYS.between(startDate, endDate) > MAX_DATE_RANGE;
        } else {
            return false;
        }
    }

    public boolean startDateAfterEndDate(java.time.LocalDate startDate, java.time.LocalDate endDate) {
        if (startDate != null && endDate != null) {
            return startDate.isAfter(endDate);
        } else {
            return false;
        }
    }

    public List<AccomClass> getRoomClassListExcludingAll() {
        return roomClassList.stream().filter(accomClass -> !accomClass.equals(allRoomClasses)).collect(Collectors.toList());
    }

    @ForTesting
    public void setRoomClassList(List<AccomClass> roomClassList) {
        this.roomClassList = roomClassList;
    }

    @ForTesting
    public void setProductATMapping(List<ProductAccomType> productATMapping) {
        this.productATMapping = productATMapping;
    }

    public AccomClass getAllRoomClasses() {
        return allRoomClasses;
    }

    public InventoryGroupDto getDefaultInventoryGroup() {
        return defaultInventoryGroup;
    }

    @ForTesting
    void setAllRoomClasses(AccomClass allRoomClasses) {
        this.allRoomClasses = allRoomClasses;
    }

    @ForTesting
    void setAllRoomType(List<AccomType> allRoomTypes) {
        this.allRoomTypes = allRoomTypes;
    }

    public List<Integer> getSelectedAccomClassesBasedOnSearchCriteria() {
        return overrideManager.getSelectedAccomClassesBasedOnSearchCriteria(roomClassList);
    }

    public List<AccomType> getBaseRoomTypeList() {
        return overrideManager.getBaseRoomTypeList();
    }

    @ForTesting
    void setBaseRoomTypeList(List<AccomType> accomTypes) {
        overrideManager.setBaseRoomTypeList(accomTypes);
    }

    public Set<AccomType> getOrderedAndFilteredTypes() {
        return overrideManager.getOrderedAndFilteredTypes();
    }

    @ForTesting
    public void setAllProducts(List<Product> allProducts) {
        this.allProducts = allProducts;
    }

    public List<Product> getAllProducts() {
        return allProducts;
    }

    public CPPricingFilter getCpPricingFilter() {
        return overrideManager.getCpPricingFilter();
    }

    public void setCpPricingFilter(CPPricingFilter cpPricingFilter) {
        overrideManager.setCpPricingFilter(cpPricingFilter);
    }

    public boolean isLraEnabled() {
        return isLraEnabled;
    }

    @ForTesting
    public void setIsLraEnabled(boolean isLraEnabled) {
        this.isLraEnabled = isLraEnabled;
    }

    public boolean isSupplementalEnabled() {
        return pricingConfigurationService.isSupplementEnabled();
    }

    @ForTesting
    public void setSupplementalEnabled(boolean supplementalEnabled) {
        isSupplementalEnabled = supplementalEnabled;
    }

    public boolean isIdpEnabled() {
        return isIdpEnabled;
    }

    @ForTesting
    public void setIsIdpEnabled(boolean isIdpEnabled) {
        this.isIdpEnabled = isIdpEnabled;
    }

    public List<AdditionalInformationFilterEnum> filterAdditionalInformationFilterEnumList(List<AdditionalInformationFilterEnum> enumsToRemove) {
        List<AdditionalInformationFilterEnum> enumsToRemoveList = new ArrayList<>(enumsToRemove);
        if (!isSupplementalEnabled) {
            //Only display Room Only Price when is Supplemental is Enabled
            enumsToRemoveList.add(AdditionalInformationFilterEnum.ROOM_ONLY_PRICE);
        }
        if (!isWebRateShoppingEnabled) {
            //Only display Competitor when Web Rate Shipping is Enabled
            enumsToRemoveList.add(AdditionalInformationFilterEnum.COMPETITOR);
        }
        if (!showAdjustments) {
            enumsToRemoveList.add(AdditionalInformationFilterEnum.ADJUSTMENT);
        }
        if (!isSystemPriceEnabled) {
            enumsToRemoveList.add(AdditionalInformationFilterEnum.SYSTEM_PRICE);
        }
        if (!isAveragePriceEnabled) {
            enumsToRemoveList.add(AdditionalInformationFilterEnum.AVERAGE_PRICE);
        }

        List<AdditionalInformationFilterEnum> filtersToDisplay = PricingToggleView.CARD == selectedView ?
                AdditionalInformationFilterEnum.getItemsForCalendarView() :
                AdditionalInformationFilterEnum.getItemsForTabularView();

        return filtersToDisplay.stream()
                .filter(additionalInformationFilterEnum -> !enumsToRemoveList.contains(additionalInformationFilterEnum))
                .sorted(Comparator.comparing(Enum::toString))
                .collect(Collectors.toList());
    }

    public boolean showDateTimeFilter(DateWithFilterEnum selectedFilter) {
        return DateWithFilterEnum.CHANGES_SINCE.equals(selectedFilter);
    }

    public boolean showCriteriaBigDecimalFilter(DateWithFilterEnum selectedFilter) {
        return DateWithFilterEnum.FINAL_PRICE_EQUAL_TO.equals(selectedFilter) ||
                DateWithFilterEnum.FINAL_PRICE_GREATER_THAN.equals(selectedFilter) ||
                DateWithFilterEnum.FINAL_PRICE_LESS_THAN.equals(selectedFilter);
    }

    public boolean showCriteriaIntegerFilterAndPercentLabel(DateWithFilterEnum selectedFilter) {
        return DateWithFilterEnum.OCCUPANCY_FORECAST_GREATER_THAN.equals(selectedFilter) ||
                DateWithFilterEnum.OCCUPANCY_FORECAST_LESS_THAN.equals(selectedFilter);
    }

    public List<CPBARDecisionUIWrapper> wrapDecisions(boolean dateChange) {
        results = overrideManager.wrapDecisions(dateChange, !isCardViewSelected(), isIndependentProductsEnabled());
        results.stream()
                .flatMap(cpbarDecisionUIWrapper -> cpbarDecisionUIWrapper.getCpOverrideWrappers().stream())
                .forEach(wrapper -> wrapper.getSelectedRoomType().setBaseRoomType(isBaseRoomType(wrapper.getSelectedRoomType())));

        if (useOptimizedLoopCodePricing()) {
            boolean isAddLOS1CompetitorData = !isCardViewSelected() && !this.allCompetitorDetailsRows.isEmpty();
            boolean isAddAllLOSCompetitorData = !isCardViewSelected() && CollectionUtils.isNotEmpty(allVwCompetitorDetailsRows);
            boolean isAddAdjustment = showAdjustments && getCpPricingFilter().getProducts().stream().filter(p -> isAgileRatesProductOrSmallGroupProduct(p))
                    .findAny().orElse(null) != null;
            optimizedLoopWrapDecisions(getHighestBarRestrictedEnabled(), isAddAdjustment, isAddLOS1CompetitorData, isAddAllLOSCompetitorData);

            if (!isAddLOS1CompetitorData && !isAddAllLOSCompetitorData) {
                emptyCompetitorDetailsRows();
            }
        } else {
            checkForRoomClassRankHierarchyWarnings();
            if (getHighestBarRestrictedEnabled()) {
                addLv0Overrides();
            }
            if (!isCardViewSelected() && !this.allCompetitorDetailsRows.isEmpty()) {
                addLOS1CompetitorData();
            } else if (!isCardViewSelected() && CollectionUtils.isNotEmpty(allVwCompetitorDetailsRows)) {
                //Temporary measure as we transition to utilizing the view as the primary source of competitor data
                //Separated to continue usage of old flow until we finalize the view's accuracy
                addAllLOSCompetitorData();
            } else {
                removeAllLOSAndLOS1CompetitorData();
            }
            addAdjustments();
        }
        return results;
    }

    private void optimizedLoopWrapDecisions(final boolean highestBarRestrictedEnabled, final boolean addAdjustments,
                                            final boolean isAddLOS1CompetitorData, final boolean isAddAllLOSCompetitorData) {

        Date startDate;
        Date endDate;
        List<CloseHighestBar> closedHighestBarsForDateRange = null;
        CPPricingFilter cpPricingFilter = getCpPricingFilter();
        if (highestBarRestrictedEnabled) {
            startDate = cpPricingFilter.getStartDate().toDateTimeAtStartOfDay().toDate();
            endDate = cpPricingFilter.getEndDate().toDateTimeAtStartOfDay().toDate();
            closedHighestBarsForDateRange = closeHighestBarService.getClosedHighestBarsForDateRange(startDate, endDate);
        }

        boolean isProductSystemDefault = false;
        if (isAddLOS1CompetitorData) {
            isProductSystemDefault = (cpPricingFilter.getProducts().stream().filter(Product::isSystemDefault).findAny().orElse(
                    null) != null);
        }

        boolean isBarOrIndependentProductOrAgileProductSelected = false;
        if (isAddAllLOSCompetitorData) {
            isBarOrIndependentProductOrAgileProductSelected = (cpPricingFilter.isBARProductSelected() || cpPricingFilter.isIndependentProductSelected() || cpPricingFilter.isAgileRateProductSelected());
        }

        Map<CPBarDecisionChangeKey, CPDecisionBAROutput> decisionMap = new HashMap<>();
        long s1 = System.currentTimeMillis();
        if (isOptimizedWarningFetch && !results.isEmpty()) {
            decisionMap = fetchCpDecisionBarOutputListForResult(results);
        }
        long s2 = System.currentTimeMillis();
        for (CPBARDecisionUIWrapper result : results) {
            if (isOptimizedWarningFetch) {
                checkForRoomClassRankHierarchyWarningsOptimizedLoopWithDecisionData(result, decisionMap);
            } else {
                checkForRoomClassRankHierarchyWarningsOptimizedLoop(result);
            }
            if (CollectionUtils.isNotEmpty(cpPricingFilter.getSelectedRestrictions()) && cpPricingFilter.getSelectedRestrictions().contains(OVERRIDE_VIOLATES_RANKING_RULE) && !result.isRoomClassRankHierarchyInvalid()) {
                continue;
            }

            if (highestBarRestrictedEnabled) {
                addLv0OverridesOptimizedLoop(result, closedHighestBarsForDateRange);
            }

            if (isAddLOS1CompetitorData) {
                if (isProductSystemDefault) {
                    addLOS1CompetitorDataOptimizedLoop(result);
                }
            } else if (isAddAllLOSCompetitorData) {
                if (isBarOrIndependentProductOrAgileProductSelected) {
                    addAllLOSCompetitorDataOptimizedLoop(result);
                }
            } else {
                result.setCompetitorDetailsWrapper(new ArrayList<>());
            }

            if (addAdjustments) {
                addAdjustmentsOptimizedLoop(result);
            }

        }
        long s3 = System.currentTimeMillis();

        log.info("fetchCpDecisionBarOutputListForResult=" + (s2 - s1) + ":ResultOptimizedLoop=" + (s3 - s2) + ":isOptimizedWarningFetchFlag=" + isOptimizedWarningFetch);

        if (CollectionUtils.isNotEmpty(cpPricingFilter.getSelectedRestrictions()) && cpPricingFilter.getSelectedRestrictions()
                .contains(
                        OVERRIDE_VIOLATES_RANKING_RULE)) {
            results = results.stream().filter(CPBARDecisionUIWrapper::isRoomClassRankHierarchyInvalid).collect(
                    Collectors.toList());
        }
    }

    private void checkForRoomClassRankHierarchyWarningsOptimizedLoopWithDecisionData(CPBARDecisionUIWrapper result, Map<CPBarDecisionChangeKey, CPDecisionBAROutput> decisionMap) {
        List<CPOverrideWrapper> cpOverrideWrappers = result.getCpOverrideWrappers();
        boolean roomClassRankInvalid = false;
        for (CPOverrideWrapper cpOverrideWrapper : cpOverrideWrappers) {
            if (cpOverrideWrapper.hasOverride()) {
                List<String> errorMessagesForPricingRuleTypeValidation = getErrorMessagesForPricingRuleTypeValidationWithDecisionData(
                        cpOverrideWrapper, decisionMap);
                if (!errorMessagesForPricingRuleTypeValidation.isEmpty()) {
                    roomClassRankInvalid = true;
                    cpOverrideWrapper.setRoomClassRankHierarchyInvalidErrorMessages(
                            errorMessagesForPricingRuleTypeValidation);
                }
            }
        }
        result.setRoomClassRankHierarchyInvalid(roomClassRankInvalid);
    }

    private List<String> getErrorMessagesForPricingRuleTypeValidationWithDecisionData(CPOverrideWrapper wrapper, Map<CPBarDecisionChangeKey, CPDecisionBAROutput> decisionMap) {
        List<String> errorMessages = new ArrayList<>();
        if (dailyBARPricingRule == 2 || dailyBARPricingRule == 3) {
            if (cpDecisionContext == null || (!DateUtil.isDateBetween(cpDecisionContext.getStartDate().toDate(),
                    cpDecisionContext.getEndDate().toDate(), wrapper.getCpDecisionBAROutput().getArrivalDate().toDate()))) {
                cpDecisionContext = pricingConfigurationService.getCPDecisionContext(getCpPricingFilter().getStartDate(), getCpPricingFilter().getEndDate(), true);
            }
            if (allAccomClassPriceRanks.isEmpty()) {
                allAccomClassPriceRanks = pricingConfigurationService.getAllAccomClassPriceRanks();
            }
            List<AccomClassPriceRank> accomClassPriceRanks = allAccomClassPriceRanks.stream().filter(ac -> ac.getLowerRankAccomClass().equals(wrapper.getAccomClass()) || ac.getHigherRankAccomClass().equals(wrapper.getAccomClass())).collect(Collectors.toList());
            List<AccomClass> lowerRankedAccomClasses = getLowerRankedAccomClasses(accomClassPriceRanks, wrapper.getAccomClass());
            List<AccomClass> higherRankedAccomClasses = getHigherRankedAccomClasses(accomClassPriceRanks, wrapper.getAccomClass());
            if (allRoomTypes == null || allRoomTypes.isEmpty()) {
                allRoomTypes = getAllRoomTypes();
            }
            List<AccomType> accomTypesToQuery = allRoomTypes.stream().filter(at -> lowerRankedAccomClasses.contains(at.getAccomClass())
                    || higherRankedAccomClasses.contains(at.getAccomClass())
                    || wrapper.getAccomClass().equals(at.getAccomClass())).collect(Collectors.toList());
            List<CPDecisionBAROutput> decisions = filterDecisionsFromList(decisionMap, wrapper.getProduct(), accomTypesToQuery,
                    wrapper.getCpDecisionBAROutput().getArrivalDate());

            List<BigDecimal> lowestFinalPrices = new ArrayList<>();
            if (wrapper.getSpecificOverride() != null) {
                lowestFinalPrices = findFinalPricesIfSpecificOverride(wrapper, decisions, wrapper.getSpecificOverride(), cpDecisionContext, new HashSet<>());
            } else if (!(wrapper.getCeilingOverride() == null && wrapper.getFloorOverride() == null)) {
                lowestFinalPrices = findFinalPricesIfFloorCeilingOverride(wrapper, decisions,
                        wrapper.getFloorOverride(), null, wrapper.getCeilingOverride(),
                        getDecisionOverrideTypeFromOverrideWrapper(wrapper), cpDecisionContext);
            }

            if (CollectionUtils.isNotEmpty(lowestFinalPrices)) {
                // list of all room classes that are directly higher and lower
                HashMap<AccomClass, List<BigDecimal>> lowerACMapFinalPrices = getFinalPricesForRelatedAccomClasses(decisions,
                        lowerRankedAccomClasses, wrapper.getCpDecisionBAROutput().getArrivalDate(), cpDecisionContext);
                HashMap<AccomClass, List<BigDecimal>> higherACMapFinalPrices = getFinalPricesForRelatedAccomClasses(decisions,
                        higherRankedAccomClasses, wrapper.getCpDecisionBAROutput().getArrivalDate(), cpDecisionContext);

                if (dailyBARPricingRule == 2) {
                    errorMessages.addAll(getErrorMessageForLowerFinalPriceComparison(wrapper, Collections.singletonList(lowestFinalPrices.get(0)), filterPricesByRuleType(lowerACMapFinalPrices, false)));
                    errorMessages.addAll(getErrorMessageForHigherFinalPriceComparison(wrapper, Collections.singletonList(lowestFinalPrices.get(lowestFinalPrices.size() - 1)), filterPricesByRuleType(higherACMapFinalPrices, true)));
                } else {
                    errorMessages.addAll(getErrorMessageForLowerFinalPriceComparison(wrapper, Collections.singletonList(lowestFinalPrices.get(0)), filterPricesByRuleType(lowerACMapFinalPrices, false)));
                    errorMessages.addAll(getErrorMessageForHigherFinalPriceComparison(wrapper, Collections.singletonList(lowestFinalPrices.get(0)), filterPricesByRuleType(higherACMapFinalPrices, false)));
                }
            }
        }
        return errorMessages;
    }

    private List<CPDecisionBAROutput> filterDecisionsFromList(Map<CPBarDecisionChangeKey, CPDecisionBAROutput> decisionMap, Product product, List<AccomType> accomTypesToQuery, LocalDate arrivalDate) {
        List<CPBarDecisionChangeKey> keys = new ArrayList<>();
        for (AccomType accomType : accomTypesToQuery) {
            keys.add(new CPBarDecisionChangeKey(product.getId(), arrivalDate, accomType.getId(), -1));
        }

        // Fetch data from the map using the created keys
        List<CPDecisionBAROutput> filteredDecisions = new ArrayList<>();
        for (CPBarDecisionChangeKey key : keys) {
            CPDecisionBAROutput decision = decisionMap.get(key);
            if (decision != null) {
                filteredDecisions.add(decision);
            }
        }
        return filteredDecisions;
    }

    private Map<CPBarDecisionChangeKey, CPDecisionBAROutput> fetchCpDecisionBarOutputListForResult(List<CPBARDecisionUIWrapper> results) {
        Set<Product> allProducts = results.stream()
                .flatMap(result -> result.getCpOverrideWrappers().stream())
                .map(CPOverrideWrapper::getProduct)
                .collect(Collectors.toSet());

        Set<LocalDate> datesWithOverrides = results.stream()
                .flatMap(result -> result.getCpOverrideWrappers().stream())
                .map(CPOverrideWrapper::getCpDecisionBAROutput)
                .filter(CPDecisionBAROutput::hasOverride)
                .map(CPDecisionBAROutput::getArrivalDate)
                .collect(Collectors.toSet());

        if (datesWithOverrides.isEmpty()) {
            return Collections.emptyMap();
        }
        Set<AccomType> accomTypesToQuery = getAllAccomTypeToQuery(results);
        if (accomTypesToQuery.isEmpty()) {
            return Collections.emptyMap();
        }
        List<CPDecisionBAROutput> decisions = pricingConfigurationService
                .fetchCPDecisionBAROutputs(allProducts,
                        datesWithOverrides, accomTypesToQuery);

        // Create a map using CPBarDecisionChangeKey as keys and CPDecisionBAROutput as values
        Map<CPBarDecisionChangeKey, CPDecisionBAROutput> decisionMap = new HashMap<>();
        for (CPDecisionBAROutput decision : decisions) {
            CPBarDecisionChangeKey key = new CPBarDecisionChangeKey(decision);
            decisionMap.put(key, decision);
        }
        return decisionMap;

    }

    private Set<AccomType> getAllAccomTypeToQuery(List<CPBARDecisionUIWrapper> results) {
        Set<AccomType> accomTypesToQuery = new HashSet<>();

        if (dailyBARPricingRule == 2 || dailyBARPricingRule == 3) {
            results.forEach(result -> processResult(result, accomTypesToQuery));
        }

        return accomTypesToQuery;
    }

    private void processResult(CPBARDecisionUIWrapper result, Set<AccomType> accomTypesToQuery) {
        result.getCpOverrideWrappers().forEach(wrapper -> {
            if (wrapper.hasOverride()) {
                if (shouldUpdateCPDecisionContext(wrapper)) {
                    updateCPDecisionContext();
                }

                if (allAccomClassPriceRanks.isEmpty()) {
                    updateAllAccomClassPriceRanks();
                }
                List<AccomType> accomTypes = getAccomTypes(wrapper);
                accomTypesToQuery.addAll(accomTypes);
            }
        });
    }

    private boolean shouldUpdateCPDecisionContext(CPOverrideWrapper wrapper) {
        return cpDecisionContext == null || !DateUtil.isDateBetween(cpDecisionContext.getStartDate().toDate(),
                cpDecisionContext.getEndDate().toDate(), wrapper.getCpDecisionBAROutput().getArrivalDate().toDate());
    }

    private void updateCPDecisionContext() {
        cpDecisionContext = pricingConfigurationService.getCPDecisionContext(getCpPricingFilter().getStartDate(), getCpPricingFilter().getEndDate(), true);
    }

    private void updateAllAccomClassPriceRanks() {
        allAccomClassPriceRanks = pricingConfigurationService.getAllAccomClassPriceRanks();
    }

    private List<AccomType> getAccomTypes(CPOverrideWrapper wrapper) {
        List<AccomClassPriceRank> accomClassPriceRanks = allAccomClassPriceRanks.stream()
                .filter(ac -> ac.getLowerRankAccomClass().equals(wrapper.getAccomClass()) || ac.getHigherRankAccomClass().equals(wrapper.getAccomClass()))
                .collect(Collectors.toList());

        List<AccomClass> lowerRankedAccomClasses = getLowerRankedAccomClasses(accomClassPriceRanks, wrapper.getAccomClass());

        List<AccomClass> higherRankedAccomClasses = getHigherRankedAccomClasses(accomClassPriceRanks, wrapper.getAccomClass());
        if (allRoomTypes == null || allRoomTypes.isEmpty()) {
            allRoomTypes = getAllRoomTypes();
        }
        return allRoomTypes.stream()
                .filter(at -> lowerRankedAccomClasses.contains(at.getAccomClass())
                        || higherRankedAccomClasses.contains(at.getAccomClass())
                        || wrapper.getAccomClass().equals(at.getAccomClass()))
                .collect(Collectors.toList());
    }


    private void removeAllLOSAndLOS1CompetitorData() {
        emptyCompetitorDetailsRows();
        results.forEach(w -> w.setCompetitorDetailsWrapper(new ArrayList<>()));
    }

    private void emptyCompetitorDetailsRows() {
        this.allCompetitorDetailsRows = new ArrayList<>();
        this.allVwCompetitorDetailsRows = new ArrayList<>();
    }

    private void checkForRoomClassRankHierarchyWarnings() {
        for (CPBARDecisionUIWrapper result : results) {
            List<CPOverrideWrapper> cpOverrideWrappers = result.getCpOverrideWrappers();
            boolean roomClassRankInvalid = false;
            for (CPOverrideWrapper cpOverrideWrapper : cpOverrideWrappers) {
                if (cpOverrideWrapper.hasOverride()) {
                    List<String> errorMessagesForPricingRuleTypeValidation = getErrorMessagesForPricingRuleTypeValidation(cpOverrideWrapper);
                    if (!errorMessagesForPricingRuleTypeValidation.isEmpty()) {
                        roomClassRankInvalid = true;
                        cpOverrideWrapper.setRoomClassRankHierarchyInvalidErrorMessages(errorMessagesForPricingRuleTypeValidation);
                    }
                }
            }
            result.setRoomClassRankHierarchyInvalid(roomClassRankInvalid);
        }
        CPPricingFilter cpPricingFilter = getCpPricingFilter();
        if (CollectionUtils.isNotEmpty(cpPricingFilter.getSelectedRestrictions()) && cpPricingFilter.getSelectedRestrictions().contains(OVERRIDE_VIOLATES_RANKING_RULE)) {
            results = results.stream().filter(result -> result.isRoomClassRankHierarchyInvalid()).collect(Collectors.toList());
        }
    }

    private void checkForRoomClassRankHierarchyWarningsOptimizedLoop(CPBARDecisionUIWrapper result) {
        List<CPOverrideWrapper> cpOverrideWrappers = result.getCpOverrideWrappers();
        boolean roomClassRankInvalid = false;
        for (CPOverrideWrapper cpOverrideWrapper : cpOverrideWrappers) {
            if (cpOverrideWrapper.hasOverride()) {
                List<String> errorMessagesForPricingRuleTypeValidation = getErrorMessagesForPricingRuleTypeValidation(
                        cpOverrideWrapper);
                if (!errorMessagesForPricingRuleTypeValidation.isEmpty()) {
                    roomClassRankInvalid = true;
                    cpOverrideWrapper.setRoomClassRankHierarchyInvalidErrorMessages(
                            errorMessagesForPricingRuleTypeValidation);
                }
            }
        }
        result.setRoomClassRankHierarchyInvalid(roomClassRankInvalid);
    }

    void addAdjustments() {
        if (showAdjustments && getCpPricingFilter().getProducts().stream().filter(p -> isAgileRatesProductOrSmallGroupProduct(p))
                .findAny().orElse(null) != null) {
            for (CPBARDecisionUIWrapper uiWrapper : results) {
                List<CPOverrideWrapper> cpOverrideWrappers = uiWrapper.getCpOverrideWrappers();
                for (CPOverrideWrapper cpOverrideWrapper : cpOverrideWrappers) {
                    if (!cpOverrideWrapper.isNotAgileRatesProduct() || cpOverrideWrapper.isSmallGroupProduct()) {
                        cpOverrideWrapper.setAdjustmentDisplayType(AGILE_RATES_PRODUCT_CODE);
                        Product product = cpOverrideWrapper.getProduct();
                        if (isPackagedProduct(product)) {
                            cpOverrideWrapper.setAdjustmentDisplayType(PRODUCT_WITH_PACKAGE);
                        } else if (product.isOptimized()) {
                            cpOverrideWrapper.setAdjustmentType(AgileRatesOffsetMethod.PERCENTAGE);
                        } else {
                            cpOverrideWrapper.setAdjustmentType(product.getOffsetMethod());
                        }
                    }
                }
            }
        }
    }

    private boolean isAgileRatesProductOrSmallGroupProduct(Product product) {
        String code = product.getCode();
        return AGILE_RATES_PRODUCT_CODE.equals(code) || GROUP_PRODUCT_CODE.equals(code);
    }

    private void addAdjustmentsOptimizedLoop(CPBARDecisionUIWrapper result) {
        List<CPOverrideWrapper> cpOverrideWrappers = result.getCpOverrideWrappers();
        for (CPOverrideWrapper cpOverrideWrapper : cpOverrideWrappers) {
            if (!cpOverrideWrapper.isNotAgileRatesProduct() || cpOverrideWrapper.isSmallGroupProduct()) {
                cpOverrideWrapper.setAdjustmentDisplayType(AGILE_RATES_PRODUCT_CODE);
                Product product = cpOverrideWrapper.getProduct();
                if (isPackagedProduct(product)) {
                    cpOverrideWrapper.setAdjustmentDisplayType(PRODUCT_WITH_PACKAGE);
                } else if (product.isOptimized()) {
                    cpOverrideWrapper.setAdjustmentType(AgileRatesOffsetMethod.PERCENTAGE);
                } else {
                    cpOverrideWrapper.setAdjustmentType(product.getOffsetMethod());
                }
            }
        }
    }

    private boolean isPackagedProduct(Product product) {
        return FENCED_AND_PACKAGED.getValue().equals(product.getType()) || UNFENCED_AND_PACKAGED.getValue().equals(product.getType());
    }

    private void addLv0Overrides() {
        Date startDate = overrideManager.getCpPricingFilter().getStartDate().toDateTimeAtStartOfDay().toDate();
        Date endDate = overrideManager.getCpPricingFilter().getEndDate().toDateTimeAtStartOfDay().toDate();
        List<CloseHighestBar> closedHighestBarsForDateRange = closeHighestBarService.getClosedHighestBarsForDateRange(startDate, endDate);
        for (CPBARDecisionUIWrapper uiWrapper : results) {
            highestBarClosedForAccomTypes = new ArrayList<>();
            Date arrivalDate = uiWrapper.getCpbarDecisionDTO().getDate().toDateTimeAtStartOfDay().toDate();
            rowModelListForCloseLv0 = new ArrayList<>();
            originalRowModelListForCloseLv0 = new ArrayList<>();
            createExistingModelsForCloseLv0(arrivalDate, closedHighestBarsForDateRange);
            uiWrapper.getCpOverrideWrappers().forEach(cpOverrideWrapper -> {
                cpOverrideWrapper.setLv0Override(highestBarClosedForAccomTypes.contains(cpOverrideWrapper.getAccomType()));
                cpOverrideWrapper.setCloseLv0RowModels(rowModelListForCloseLv0);
                cpOverrideWrapper.setOriginalCloseLv0RowModels(originalRowModelListForCloseLv0);
            });
        }
    }

    private void addLv0OverridesOptimizedLoop(CPBARDecisionUIWrapper result, List<CloseHighestBar> closedHighestBarsForDateRange) {
        highestBarClosedForAccomTypes = new ArrayList<>();
        Date arrivalDate = result.getCpbarDecisionDTO().getDate().toDateTimeAtStartOfDay().toDate();
        rowModelListForCloseLv0 = new ArrayList<>();
        originalRowModelListForCloseLv0 = new ArrayList<>();
        createExistingModelsForCloseLv0(arrivalDate, closedHighestBarsForDateRange);
        result.getCpOverrideWrappers().forEach(cpOverrideWrapper -> {
            cpOverrideWrapper.setLv0Override(highestBarClosedForAccomTypes.contains(cpOverrideWrapper.getAccomType()));
            cpOverrideWrapper.setCloseLv0RowModels(rowModelListForCloseLv0);
            cpOverrideWrapper.setOriginalCloseLv0RowModels(originalRowModelListForCloseLv0);
        });
    }

    private void addLOS1CompetitorData() {
        // only set competitor data in wrapper if BAR product is on screen
        if (getCpPricingFilter().getProducts().stream().filter(Product::isSystemDefault).findAny().orElse(null) != null) {
            for (CPBARDecisionUIWrapper uiWrapper : results) {
                LocalDate date = uiWrapper.getCpbarDecisionDTO().getDate();
                HashMap<String, String> wrapperAccomPairs = new HashMap<>();

                for (CPOverrideWrapper wrapper : uiWrapper.getCpOverrideWrappers()) {
                    wrapperAccomPairs.put(wrapper.getAccomType().getName(), wrapper.getAccomType().toString());
                }

                List<CompetitorDetailsRow> competitorData = this.allCompetitorDetailsRows.stream()
                        .filter(o -> o.getPropertyRoomTypeName().equals(wrapperAccomPairs.get(o.getPropertyRoomTypeName())))
                        .filter(o -> {
                            LocalDate competitorDate = new LocalDate(o.getOccupancyDate());
                            return competitorDate.equals(date);
                        })
                        .collect(Collectors.toList());

                uiWrapper.setCompetitorDetailsWrapper(competitorData);
            }
        }
    }

    private void addLOS1CompetitorDataOptimizedLoop(CPBARDecisionUIWrapper result) {

        LocalDate date = result.getCpbarDecisionDTO().getDate();
        HashMap<String, String> wrapperAccomPairs = new HashMap<>();

        result.getCpOverrideWrappers().forEach(wrapper -> wrapperAccomPairs.put(wrapper.getAccomType().getName(), wrapper.getAccomType().toString()));

        List<CompetitorDetailsRow> competitorData = this.allCompetitorDetailsRows.stream()
                .filter(o -> o.getPropertyRoomTypeName()
                        .equals(wrapperAccomPairs.get(
                                o.getPropertyRoomTypeName())))
                .filter(o -> {
                    LocalDate competitorDate = new LocalDate(
                            o.getOccupancyDate());
                    return competitorDate.equals(date);
                })
                .collect(Collectors.toList());

        result.setCompetitorDetailsWrapper(competitorData);
    }

    private void addAllLOSCompetitorData() {
        // only set competitor data in wrapper if BAR or independent product is on screen
        if (getCpPricingFilter().isBARProductSelected() || getCpPricingFilter().isIndependentProductSelected()
                || getCpPricingFilter().isAgileRateProductSelected()) {
            for (CPBARDecisionUIWrapper uiWrapper : results) {
                java.time.LocalDate date = DateUtil.convertJodaLocalDateToLocalDate(uiWrapper.getCpbarDecisionDTO().getDate());
                List<Integer> accomClassesForUiWrapper = uiWrapper
                        .getCpOverrideWrappers()
                        .stream()
                        .map(item -> item.getAccomClass().getId())
                        .collect(Collectors.toList());

                List<WebrateView> uiWrapperAssociatedWebrateViews = this.allVwCompetitorDetailsRows.stream()
                        .filter(item -> accomClassesForUiWrapper.contains(item.getAccomClassId()))
                        .filter(item -> item.getOccupancyDate().equals(date))
                        .collect(Collectors.toList());

                uiWrapper.setCompetitorVwDetailsWrapper(uiWrapperAssociatedWebrateViews);
            }
        }
    }

    private void addAllLOSCompetitorDataOptimizedLoop(CPBARDecisionUIWrapper result) {
        java.time.LocalDate date = DateUtil.convertJodaLocalDateToLocalDate(result.getCpbarDecisionDTO().getDate());
        List<Integer> accomClassesForUiWrapper = result
                .getCpOverrideWrappers()
                .stream()
                .map(item -> item.getAccomClass().getId())
                .collect(Collectors.toList());

        List<WebrateView> uiWrapperAssociatedWebrateViews = this.allVwCompetitorDetailsRows.stream()
                .filter(item -> accomClassesForUiWrapper.contains(item.getAccomClassId()))
                .filter(item -> item.getOccupancyDate().equals(date))
                .collect(Collectors.toList());

        result.setCompetitorVwDetailsWrapper(uiWrapperAssociatedWebrateViews);
    }

    private void createExistingModelsForCloseLv0(Date date, List<CloseHighestBar> closedHighestBarsForDateRange) {
        for (String lengthOfStay : createLOSListForCloseLV0()) {
            createRowModels(date, lengthOfStay, closedHighestBarsForDateRange);
        }
    }

    private void createRowModels(Date date, String lengthOfStay, List<CloseHighestBar> closedHighestBarsForDateRange) {
        CloseLV0RowModel rowModel = new CloseLV0RowModel(lengthOfStay);
        CloseLV0RowModel originalRowModel = new CloseLV0RowModel(lengthOfStay);
        int los = getLosValueFromRowHeaderString(lengthOfStay);
        setRowModelValues(date, closedHighestBarsForDateRange, rowModel, originalRowModel, los);
        rowModelListForCloseLv0.add(rowModel);
        originalRowModelListForCloseLv0.add(originalRowModel);
    }

    private void setRowModelValues(Date date, List<CloseHighestBar> closedHighestBarsForDateRange, CloseLV0RowModel rowModel, CloseLV0RowModel originalRowModel, int los) {
        for (AccomType accomType : getRoomTypesForSelectedRoomClass()) {
            boolean closedForAccomType = isClosedForAccomType(accomType, date, los, closedHighestBarsForDateRange);
            if (closedForAccomType) {
                highestBarClosedForAccomTypes.add(accomType);
            }
            rowModel.setCheckBoxValue(accomType, closedForAccomType);
            originalRowModel.setCheckBoxValue(accomType, closedForAccomType);
        }
    }

    private boolean isClosedForAccomType(AccomType accomType, Date date, int los, List<CloseHighestBar> closedHighestBarsForDateRange) {
        return closedHighestBarsForDateRange.stream().anyMatch(closeHighestBar -> compareClosedHighestBarsWithCPOverrideWrappers(date, accomType, closeHighestBar, los));
    }

    public List<AccomType> getRoomTypesForSelectedRoomClass() {
        AccomClass selectedRoomClass = getCpPricingFilter().getSelectedRoomClass();
        return selectedRoomClass.getId() == ALL_ROOM_CLASS_ID ? getAllRoomTypes() : getSelectedRoomTypes();
    }

    public List<AccomType> getRoomTypesForSelectedRoomClass(Integer roomClassId) {
        return getAllRoomTypes()
                .stream()
                .filter(roomType -> roomType.getAccomClass().getId().equals(roomClassId))
                .collect(Collectors.toList());
    }

    private boolean compareClosedHighestBarsWithCPOverrideWrappers(Date arrivalDate, AccomType accomType, CloseHighestBar closeHighestBar, int los) {
        return isSameArrivalDate(arrivalDate, closeHighestBar) && isAccomClassEqual(accomType.getAccomClass().getId(), closeHighestBar) && isAccomTypeEqual(accomType.getId(), closeHighestBar) && isSameLengthOfStay(closeHighestBar, los);
    }

    private boolean isSameLengthOfStay(CloseHighestBar closeHighestBar, int los) {
        return closeHighestBar.getLengthOfStay().equals(los);
    }

    private boolean isSameArrivalDate(Date arrivalDate, CloseHighestBar closeHighestBar) {
        return closeHighestBar.getArrivalDate().equals(arrivalDate);
    }

    private boolean isAccomTypeEqual(Integer accomTypeId, CloseHighestBar closeHighestBar) {
        return closeHighestBar.getAccomTypeId().equals(accomTypeId);
    }

    private boolean isAccomClassEqual(Integer accomClassId, CloseHighestBar closeHighestBar) {
        return closeHighestBar.getAccomClassId().equals(accomClassId);
    }

    public void uploadOverrides() {
        overrideService.uploadBAROverrides();
        showSuccessMessage(getText("upload.was.successful"));
    }

    public void cancel() {
        clearPendingOverrides();
        if (selectedView.equals(PricingToggleView.CARD)) {
            clearPendingCloseLv0Overrides();
            clearMultiProductOverrides();
            appliedMultiProductBARDTOs = new ArrayList<>();
        }
        view.updateDecisions(true);
    }

    public void clearPendingOverrides() {
        if (selectedView.equals(PricingToggleView.CARD)) {
            overrideManager.clearPendingOverrides();
            productOverrides.clear();
            inventoryLimitOverrides.clear();
        } else if (selectedView.equals(PricingToggleView.GRID)) {
            view.clearPricingInlineEditGridOverrideWrappers();
            overrideManager.clearPendingOverrides();
        }
    }

    public void clearPendingCloseLv0Overrides() {
        pendingCloseLv0BarOverrides.clear();
    }

    public void refreshView() {
        view.refreshView();
    }

    public boolean isBarByLOS() {
        return isBarByLOS;
    }

    @ForTesting
    public void setIsBarByLOS(boolean isBarByLOS) {
        this.isBarByLOS = isBarByLOS;
    }

    public boolean isDisplayOccupancyForecastInWhatIfEnabled() {
        return isDisplayOccupancyForecastInWhatIfEnabled;
    }

    @ForTesting
    public void setIsDisplayOccupancyForecastInWhatIfEnabled(boolean isDisplayOccupancyForecastInWhatIfEnabled) {
        this.isDisplayOccupancyForecastInWhatIfEnabled = isDisplayOccupancyForecastInWhatIfEnabled;
    }

    public boolean isEnablePhysicalCapacityConsideration() {
        return isEnablePhysicalCapacityConsideration;
    }

    @ForTesting
    public void setIsEnablePhysicalCapacityConsideration(boolean isEnablePhysicalCapacityConsideration) {
        this.isEnablePhysicalCapacityConsideration = isEnablePhysicalCapacityConsideration;
    }

    public boolean isContinuousPricing() {
        return isContinuousPricing;
    }

    @ForTesting
    public void setIsContinuousPricing(boolean isContinuousPricing) {
        this.isContinuousPricing = isContinuousPricing;
    }

    @ForTesting
    public void setGroupFloorEnabled(boolean groupFloorEnabled) {
        isGroupFloorEnabled = groupFloorEnabled;
    }

    public boolean isGroupFloorEnabled() {
        return isGroupFloorEnabled;
    }

    public int getMaxLos() {
        return maxLos;
    }

    @ForTesting
    public void setMaxLos(int maxLos) {
        this.maxLos = maxLos;
    }

    public SimplifiedWhatIfResult simplifiedAnalyzeChanges() {
        SimplifiedWhatIfResult simplifiedWhatIfResult = null;
        try {
            simplifiedWhatIfResult = overrideManager.simplifiedAnalyzeChanges();
        } catch (TetrisException e) {
            log.error(EXCEPTION_WHILE_LOADING_WHAT_IF_DATA, e);
            if (e.getErrorCode() == ErrorCode.DENIED_BY_REGULATOR_SERVICE) {
                showWarning(getText("deniedByWhatIfRegulatorService"));
            } else {
                showError(getText(FAILED_LOADING_WHAT_IF));
            }
        } catch (Exception e) {
            log.error(EXCEPTION_WHILE_LOADING_WHAT_IF_DATA, e);
            showError(getText(FAILED_LOADING_WHAT_IF));
        }

        return simplifiedWhatIfResult;
    }

    @Override
    public boolean hasChanges() {
        if (selectedView.equals(PricingToggleView.GRID)) {
            return view.pricingInlineEditGridHasChanges();
        }
        return MapUtils.isNotEmpty(overrideManager.getPendingBAROverrides()) || isNotEmpty(productOverrides) || isNotEmpty(inventoryLimitOverrides) || isNotEmpty(getPendingOverridesToSave());
    }

    public boolean hasLV0Changes() {
        return pendingCloseLv0BarOverrides.stream().anyMatch(BAROverride::isCloseLV0Override);
    }

    public boolean hasCPOverrideChanges() {
        if (selectedView.equals(PricingToggleView.GRID)) {
            return view.pricingInlineEditGridHasChanges();
        }
        return MapUtils.isNotEmpty(overrideManager.getPendingBAROverrides()) || isNotEmpty(productOverrides) || isNotEmpty(inventoryLimitOverrides);
    }

    public String getFinalPriceString(CPOverrideWrapper overrideWrapper) {
        BigDecimal barValue;
        if (overrideWrapper.getSpecificOverride() != null) {
            //if its a specific override, we use the value for pretty bar
            barValue = overrideWrapper.getSpecificOverride();
        } else {
            barValue = overrideWrapper.getRoundedBAR();
        }
        BigDecimal value = barValue != null ? new BigDecimal(NumberUtils.numberFormatter(barValue, 2, false)) : BigDecimal.ZERO;
        return barValue != null ? UiUtils.formatValue(value) : "";
    }

    public AccomClass getMasterAccomClass() {
        if (null == masterAccomClass) {
            masterAccomClass = roomClassService.findMasterClass(PacmanWorkContextHelper.getPropertyId());
        }
        return masterAccomClass;
    }

    public AccomClass getSelectedRoomClassForInvestigatorPopUp() {
        if (shouldSelectMasterRoomClassForInvestigatorPopUp()) {
            return getMasterAccomClass();
        }
        return getCpPricingFilter().getSelectedRoomClass();
    }

    private boolean shouldSelectMasterRoomClassForInvestigatorPopUp() {
        return isAllRoomClassesWithBaseRoomType();
    }

    public boolean isShowOnlyBaseRoomTypesFlag() {
        return overrideManager.isShowOnlyBaseRoomTypesFlag();
    }

    public boolean isAllRoomClassesWithBaseRoomType() {
        return getCpPricingFilter() != null && getCpPricingFilter().getSelectedRoomClass().getId().equals(ALL_ROOM_CLASS_ID);
    }

    public AccomType getBaseRoomTypeForRoomClass(AccomClass selectedAccomClass) {
        return getBaseRoomTypeList().stream().filter(accomType -> accomType.getAccomClass().getId().equals(selectedAccomClass.getId())).findFirst().get();
    }

    public boolean isBaseRoomType(AccomType accomType) {
        if (accomType != null && isNotEmpty(pricingAccomClasses)) {
            for (PricingAccomClass pricingAccomClass : pricingAccomClasses) {
                if (pricingAccomClass.getAccomClass().equals(accomType.getAccomClass())) {
                    return pricingAccomClass.getAccomType().equals(accomType);
                }
            }
        }
        return false;
    }

    public boolean isBarOrIndependentProductNonBaseRoomType(PricingMultidayGridDTO dto) {
        return dto.isBarProductOrIndependentProduct() && !isBaseRoomType(dto.getRoomType());
    }

    public boolean filterContainsOnlyBaseRoomType(Binder<PricingMultiProductMultidayFilterDTO> filterBinder) {
        for (AccomType accomType : filterBinder.getBean().getRoomTypes()) {
            if (!isBaseRoomType(accomType)) {
                return false;
            }
        }
        return true;
    }

    public boolean hasPropertyLevelManualRestrictions(LocalDate date) {
        return manualRestrictionsPropertyDto != null && isManualRestrictionEnabled && manualRestrictionsPropertyDto.containsKey(date);
    }

    public boolean hasAccomLevelManualRestrictions(LocalDate date, Integer accomClassId, Integer accomTypeId) {
        return manualRestrictionsAccomDto != null && hasAccomLevelRestrictionsPresentAndToggleEnabled(date, accomClassId, accomTypeId);
    }

    private boolean hasAccomLevelRestrictionsPresentAndToggleEnabled(LocalDate date, Integer accomClassId, Integer accomTypeId) {
        return isManualRestrictionEnabled && manualRestrictionsAccomDto.containsKey(date) &&
                manualRestrictionsAccomDto.get(date).containsKey(new ManualRestrictionRoomTypeIdentifier(accomClassId, accomTypeId));
    }

    public void populateManualRestrictionsDto() {
        CPPricingFilter cpPricingFilter = getCpPricingFilter();
        manualRestrictionsPropertyDto = manualRestrictionService.getActivePropertyLevelManualRestrictionsForDateRange(cpPricingFilter.getStartDate(), cpPricingFilter.getEndDate());
        manualRestrictionsAccomDto = manualRestrictionService.getActiveAccomLevelManualRestrictionsForDateRange(cpPricingFilter.getStartDate(), cpPricingFilter.getEndDate());
    }

    public boolean hasNotes(LocalDate localDate) {
        return notes != null && notes.stream().anyMatch(dateNote -> dateNote.getArrivalDate().equals(localDate.toDate()));
    }

    public void updateNotes() {
        CPPricingFilter cpPricingFilter = getCpPricingFilter();
        notes = notesService.getNotes(cpPricingFilter.getStartDate().toDate(), cpPricingFilter.getEndDate().toDate());
    }
    public void updateInventoryLimit(){
        CPPricingFilter cpPricingFilter = getCpPricingFilter();
        Date sDate = cpPricingFilter.getStartDate().toDate();
        Date eDate =  cpPricingFilter.getEndDate().toDate();
        inventoryLimitMap = overrideManager.buildMapOfInventoryLimit(sDate, eDate);
        inventoryLimits = overrideManager.getInventoryLimitDecisions(sDate, eDate);
        overriddenInventoryLimits = overrideManager.getOverriddenInventoryLimits(sDate, eDate);
    }

    public void updateNotesAndManualRestrictionsDto() {
        updateNotes();
        populateManualRestrictionsDto();
        if(isGroupProductInventoryLimitEnabled) {
            updateInventoryLimit();
        }
    }

    public void updateNotesAndManualRestrictionsDtoInParallel() {
        CPPricingFilter cpPricingFilter = getCpPricingFilter();

        Map<String, Supplier<Object>> requestMap = new HashMap<>();
        requestMap.put("notes", () -> notesService.getNotes(cpPricingFilter.getStartDate().toDate(), cpPricingFilter.getEndDate().toDate()));
        requestMap.put("manualRestrictionsPropertyDto", () -> manualRestrictionService.getActivePropertyLevelManualRestrictionsForDateRange(cpPricingFilter.getStartDate(), cpPricingFilter.getEndDate()));
        requestMap.put("manualRestrictionsAccomDto", () -> manualRestrictionService.getActiveAccomLevelManualRestrictionsForDateRange(cpPricingFilter.getStartDate(), cpPricingFilter.getEndDate()));
        if(isGroupProductInventoryLimitEnabled) {
            requestMap.put("inventoryLimitMap", () -> overrideManager.buildMapOfInventoryLimit(cpPricingFilter.getStartDate().toDate(), cpPricingFilter.getEndDate().toDate()));
            requestMap.put("inventoryLimits", () -> overrideManager.getInventoryLimitDecisions(cpPricingFilter.getStartDate().toDate(), cpPricingFilter.getEndDate().toDate()));
            requestMap.put("overriddenInventoryLimits", () -> overrideManager.getOverriddenInventoryLimits(cpPricingFilter.getStartDate().toDate(), cpPricingFilter.getEndDate().toDate()));
        }
        TetrisDataThreadPool<Object> dataThreadPool = new TetrisDataThreadPool<>(TetrisDataThreadPool.getThreadPoolExecutor(requestMap.size(), "prc-vw-updt-nts-mr"), UI.getCurrent());
        Map<String, Object> resultMap = dataThreadPool.executeSupplierMap(requestMap, true);

        notes = (List<DateNote>) resultMap.get("notes");
        manualRestrictionsPropertyDto = (Map<LocalDate, ManualRestrictionTypeDto>) resultMap.get("manualRestrictionsPropertyDto");
        manualRestrictionsAccomDto = (Map<LocalDate, Map<ManualRestrictionRoomTypeIdentifier, ManualRestrictionTypeDto>>) resultMap.get("manualRestrictionsAccomDto");
        if(isGroupProductInventoryLimitEnabled) {
            inventoryLimitMap = (Map<Date, String>) resultMap.get("inventoryLimitMap");
            inventoryLimits = (List<InventoryLimitDecision>) resultMap.get("inventoryLimits");
            overriddenInventoryLimits = (List<InventoryLimitDecision>) resultMap.get("overriddenInventoryLimits");
        }
    }

    @ForTesting
    public void setNotes(List<DateNote> notes) {
        this.notes = notes;
    }

    public boolean isRoomClassPriceExcluded(AccomClass accomClass) {
        return overrideManager.isRoomClassPriceExcluded(accomClass, pricingAccomClasses);
    }

    public void applySingleDayOverrideFromInvestigatorPopUp(CPOverrideWrapper cpOverrideWrapper) {
        CPOverrideWrapper wrapper = findWrapper(cpOverrideWrapper);
        CPDecisionContext cpDecisionContext = getCpDecisionContext(wrapper.getCpDecisionBAROutput().getArrivalDate(), wrapper.getCpDecisionBAROutput().getArrivalDate(), true);
        applyOverride(wrapper, true, cpDecisionContext, getMasterPricingAccomClassFromCPDecisionContext(cpDecisionContext));
        enableSaveCancelButtons(true);
    }

    public CPOverrideWrapper findWrapper(CPOverrideWrapper cpOverrideWrapper) {
        return overrideManager.findWrapper(cpOverrideWrapper);
    }

    public void removeSingleDayOverrideFromAgileRatesPopUp(CPOverrideWrapper cpOverrideWrapper, boolean refreshData) {
        Product currentProduct = cpOverrideWrapper.getCpDecisionBAROutput().getProduct();
        if (isNotEmpty(cpOverrideWrapper.getProductRateOffsetOverrides())) {
            LocalDate arrivalDate = cpOverrideWrapper.getCpDecisionBAROutput().getArrivalDate();
            if (isAgileRatesAndApplyToAllRoomClasses(currentProduct)) {
                //On screen cards delete all
                List<ProductRateOffsetOverride> allOffsetsToRemove = getProductRateOffsetOverrides()
                        .stream()
                        .filter(proo -> arrivalDate.equals(proo.getOccupancyDate()))
                        .filter(proo -> proo.getProduct().equals(cpOverrideWrapper.getCpDecisionBAROutput().getProduct()))
                        .collect(Collectors.toList());
                List<ProductRateOffsetOverride> coveredRemovedOffsets = new ArrayList<>();
                getOverrideWrappersForDate(arrivalDate, cpOverrideWrapper.getCpDecisionBAROutput().getProduct())
                        .filter(wrapper -> wrapper.getCpDecisionBAROutput().getProduct().equals(currentProduct))
                        .forEach(wrapper -> {
                            wrapper.setIsPendingDelete(true);
                            wrapper.setIsPendingSave(false);
                            productOverrides.add(wrapper);
                            coveredRemovedOffsets.addAll(wrapper.getProductRateOffsetOverrides());
                        });
                allOffsetsToRemove.removeAll(coveredRemovedOffsets);
                //Add leftover needed to delete
                final CPOverrideWrapper wrapperToRemoveProductRateOffsets = getOverrideWrappersForDate(arrivalDate, cpOverrideWrapper.getCpDecisionBAROutput().getProduct())
                        .filter(wrapper -> wrapper.isPendingDelete())
                        .findFirst()
                        .orElse(null);
                if (wrapperToRemoveProductRateOffsets != null) {
                    wrapperToRemoveProductRateOffsets.getProductRateOffsetOverrides().addAll(allOffsetsToRemove);
                }
            } else {
                getOverrideWrappersForDateAndAccomClass(arrivalDate, cpOverrideWrapper.getAccomClass(), cpOverrideWrapper.getCpDecisionBAROutput().getProduct())
                        .filter(wrapper -> wrapper.getCpDecisionBAROutput().getProduct().equals(currentProduct))
                        .forEach(wrapper -> {
                            wrapper.setIsPendingDelete(true);
                            wrapper.setIsPendingSave(false);
                        });
            }
            cpOverrideWrapper.setIsPendingDelete(true);
            cpOverrideWrapper.setIsPendingSave(false);

            productOverrides.add(cpOverrideWrapper);

            enableSaveCancelButtons(hasChanges());
        }

        if (refreshData) {
            refreshView();
        }
    }

    public void removeMultiDayOverrideFromAgileRatesPopUp(CPOverrideWrapper cpOverrideWrapper, boolean refreshData) {
        if (isNotEmpty(cpOverrideWrapper.getProductRateOffsetOverrides())) {
            LocalDate arrivalDate = cpOverrideWrapper.getCpDecisionBAROutput().getArrivalDate();
            if (isAgileRatesAndApplyToAllRoomClasses(cpOverrideWrapper.getCpDecisionBAROutput().getProduct())) {
                //On screen cards delete all
                List<ProductRateOffsetOverride> allOffsetsToRemove = getProductRateOffsetOverrides()
                        .stream()
                        .filter(proo -> arrivalDate.equals(proo.getOccupancyDate()))
                        .filter(proo -> proo.getProduct().equals(cpOverrideWrapper.getCpDecisionBAROutput().getProduct()))
                        .collect(Collectors.toList());
                List<ProductRateOffsetOverride> coveredRemovedOffsets = new ArrayList<>();
                getOverrideWrappersForDate(arrivalDate, cpOverrideWrapper.getCpDecisionBAROutput().getProduct())
                        .forEach(wrapper -> {
                            if (wrapper.hasOriginalAgileRatesOverride()) {
                                productOverrides.add(wrapper);
                                coveredRemovedOffsets.addAll(wrapper.getProductRateOffsetOverrides());
                            } else {
                                if (productOverrides.contains(wrapper)) {
                                    productOverrides.remove(wrapper);
                                }
                                if (coveredRemovedOffsets.contains(wrapper.getProductRateOffsetOverrides())) {
                                    coveredRemovedOffsets.removeAll(wrapper.getProductRateOffsetOverrides());
                                }
                                wrapper.setProductRateOffsetOverrides(wrapper.getOriginalProductRateOffsetOverrides());
                            }
                            wrapper.setIsPendingDelete(wrapper.hasOriginalAgileRatesOverride());
                            wrapper.setIsPendingSave(false);
                        });
                allOffsetsToRemove.removeAll(coveredRemovedOffsets);
                //Add leftover needed to delete
                final CPOverrideWrapper wrapperToRemoveProductRateOffsets = getOverrideWrappersForDate(arrivalDate, cpOverrideWrapper.getCpDecisionBAROutput().getProduct())
                        .filter(wrapper -> wrapper.isPendingDelete())
                        .findFirst()
                        .orElse(null);
                if (wrapperToRemoveProductRateOffsets != null) {
                    wrapperToRemoveProductRateOffsets.getProductRateOffsetOverrides().addAll(allOffsetsToRemove);
                }
            } else {
                //Normal Flow
                getOverrideWrappersForDateAndAccomClass(arrivalDate,
                        cpOverrideWrapper.getAccomClass(),
                        cpOverrideWrapper.getCpDecisionBAROutput().getProduct())
                        .forEach(wrapper -> {
                            wrapper.setProductRateOffsetOverrides(wrapper.getOriginalProductRateOffsetOverrides());
                            wrapper.setIsPendingDelete(wrapper.hasOriginalAgileRatesOverride());
                            wrapper.setIsPendingSave(false);
                        });
                if (cpOverrideWrapper.hasOriginalAgileRatesOverride()) {
                    productOverrides.add(cpOverrideWrapper);
                } else {
                    cpOverrideWrapper.setProductRateOffsetOverrides(cpOverrideWrapper.getOriginalProductRateOffsetOverrides());
                    if (productOverrides.contains(cpOverrideWrapper)) {
                        productOverrides.remove(cpOverrideWrapper);
                    }
                }
                cpOverrideWrapper.setIsPendingDelete(cpOverrideWrapper.hasOriginalAgileRatesOverride());
                cpOverrideWrapper.setIsPendingSave(false);
            }
            enableSaveCancelButtons(true);
        }

        if (refreshData) {
            refreshView();
        }
    }

    public void removeSingleDayOverrideFromInvestigatorPopUp(CPOverrideWrapper cpOverrideWrapper) {
        CPOverrideWrapper wrapper = findWrapper(cpOverrideWrapper);
        applyRemove(wrapper, true, null, null, false);
        enableSaveCancelButtons(true);
    }

    private void applyRemove(CPOverrideWrapper overrideWrapper, boolean refreshData, CPDecisionContext cpDecisionContext, PricingAccomClass masterPricingAccomClass, boolean isInlineEditMode) {
        overrideManager.removeOverrides(overrideWrapper, pricingAccomClasses, cpDecisionContext, masterPricingAccomClass, isInlineEditMode);
        if (refreshData) {
            refreshView();
        }
    }

    public Map<LocalDate, List<InvestigatorDto>> getInvestigatorDtosByDate(boolean showOverrideTab) {
        return overrideManager.getInvestigatorDtosByDate(showOverrideTab, pricingAccomClasses);
    }

    public void applyOverride(CPOverrideWrapper overrideWrapper, boolean refreshData, CPDecisionContext cpDecisionContext, PricingAccomClass masterPricingAccomClass) {
        overrideManager.populateIfAbsent();
        overrideManager.applyOverride(overrideWrapper, pricingAccomClasses, isPerPersonPricingEnabled(),
                isChildAgeBucketsEnabled(), isChildAgeBucketPackagesEnabled, isAgileRatesEnabled(), uiContext.getSystemCaughtUpDateAsLocalDate(),
                cpDecisionContext, masterPricingAccomClass);
        if (refreshData) {
            refreshView();
        }
    }

    public CPDecisionContext getCpDecisionContext(LocalDate startDate, LocalDate endDate, boolean useOverrides) {
        if (SystemConfig.usePerformanceImprovementChangesPricingMultiProductDisplayChanges()) {
            LocalDate caughtUpDate = uiContext.getSystemCaughtUpDateAsLocalDate();
            overrideManager.populateIfAbsent();
            return pricingConfigurationService.getCPDecisionContext(
                    startDate, endDate, useOverrides,
                    overrideManager.getCpConfiguration(), overrideManager.getBaseOccupancyType(),
                    isShowOnlyBaseRoomTypesFlag(), getPricingRules(),
                    overrideManager.getTax(), pricingAccomClasses,
                    overrideManager.getOccupantBucketEntities(), overrideManager.getMaximumOccupantsEntitiesList(),
                    isPerPersonPricingEnabled, isChildAgeBucketsEnabled, isChildAgeBucketPackagesEnabled,
                    isAgileRatesEnabled, caughtUpDate,
                    overrideManager.getUngroupedProductPackages());
        }

        return null;
    }

    public void applySingleDayOverrideChanges(CPOverrideWrapper overrideWrapper, CPDecisionContext cpDecisionContext, PricingAccomClass masterPricingAccomClass) {
        CPOverrideWrapper wrapper = findWrapper(overrideWrapper);
        applyOverrideChanges(wrapper, cpDecisionContext, masterPricingAccomClass, true);
        enableSaveCancelButtons(hasChanges());
    }

    private void applyOverrideChanges(CPOverrideWrapper overrideWrapper, CPDecisionContext cpDecisionContext, PricingAccomClass pricingMasterAccomClass, boolean shouldRefreshView) {
        if (willApplyRemoveFromBARProduct(overrideWrapper)) {
            applyRemove(overrideWrapper, shouldRefreshView, cpDecisionContext, pricingMasterAccomClass, false);
        } else {
            overrideManager.populateIfAbsent();
            overrideManager.setSelectedView(this.selectedView);
            overrideManager.applyOverride(overrideWrapper, pricingAccomClasses, isPerPersonPricingEnabled(), isChildAgeBucketsEnabled(), isChildAgeBucketPackagesEnabled,
                    isAgileRatesEnabled(), uiContext.getSystemCaughtUpDateAsLocalDate(), cpDecisionContext, pricingMasterAccomClass);
            if (shouldRefreshView) {
                refreshView();
            }
        }
    }

    @VisibleForTesting
    public boolean willApplyRemoveFromBARProduct(CPOverrideWrapper overrideWrapper) {
        return overrideWrapper.getSpecificOverride() == null && overrideWrapper.getFloorOverride() == null
                && overrideWrapper.getCeilingOverride() == null && overrideWrapper.getGroupFloorOverride() == null;
    }

    public boolean hasGroupFloorInDateRange(PricingMultidayGridDTO dto, PricingMultiProductMultidayFilterDTO filterDTO) {
        return results.stream()
                .filter(wrapper -> !wrapper.getCpbarDecisionDTO().getDate().isBefore(new LocalDate(filterDTO.getStartDate().toString())) &&
                        !wrapper.getCpbarDecisionDTO().getDate().isAfter(new LocalDate(filterDTO.getEndDate().toString())))
                .flatMap(wrapper -> wrapper.getCpOverrideWrappers().stream())
                .filter(wrapper -> wrapper.getAccomType().equals(dto.getRoomType()))
                .anyMatch(this::wrapperHasGroupFloorOverride);
    }

    public boolean hasGroupFloorInDateRange(PricingMultiProductMultidayFilterDTO filterDTO) {
        return results.stream()
                .filter(wrapper -> !wrapper.getCpbarDecisionDTO().getDate().isBefore(new LocalDate(filterDTO.getStartDate().toString())) &&
                        !wrapper.getCpbarDecisionDTO().getDate().isAfter(new LocalDate(filterDTO.getEndDate().toString())))
                .flatMap(wrapper -> wrapper.getCpOverrideWrappers().stream())
                .filter(wrapper -> filterDTO.getRoomTypes().contains(wrapper.getAccomType()))
                .anyMatch(this::wrapperHasGroupFloorOverride);
    }

    public boolean wrapperHasGroupFloorOverride(CPOverrideWrapper wrapper) {
        return DecisionOverrideType.GPFLOOR.equals(wrapper.getOriginalOverrideType()) || DecisionOverrideType.GPFLOORANDCEIL.equals(wrapper.getOriginalOverrideType());
    }

    public void save() {
        PricingAccomClass pricingAccomClass = pricingAccomClasses.stream().filter(pac -> pac.getAccomClass().equals(getMasterAccomClass())).findFirst().orElse(null);
        overrideManager.saveOverrideChanges(pricingAccomClass);
        if (!pendingCloseLv0BarOverrides.isEmpty()) {
            saveLV0Overrides();
        }
        showSaveSuccessMessage();
        sync();
        view.updateLayout(true);
        clearMultiProductOverrides();
        appliedMultiProductBARDTOs = new ArrayList<>();
    }

    public boolean isManualUploadEnabled() {
        return isManualUploadEnabled;
    }

    public boolean isPropertyStageTwoWay() {
        return isPropertyStageTwoWay;
    }

    public boolean isBarOverridePendingForUpload() {
        return overrideService.isBarOverridePendingForUpload();
    }

    public boolean isAgileRatesPendingForUpload() {
        return overrideService.isAgileRatesOverridesPendingForUpload();
    }

    public boolean isInventoryLimitPendingForUpload() {
        return overrideService.isInventoryLimitOverridesPendingForUpload();
    }

    public String createSpecialEventsIconDescription(CPBARDecisionDTO dto) {
        String nonInfoTitle = UiUtils.getText("SPECIAL_EVENTS") + "\n\n";
        String InfoOnlyTitle = UiUtils.getText("SPECIAL_EVENTS") + " - " + UiUtils.getText("informationOnly") + "\n\n";
        StringBuilder nonInfoBuilder = new StringBuilder(nonInfoTitle);
        StringBuilder infoOnlyBuilder = new StringBuilder(InfoOnlyTitle);

        for (BusinessAnalysisSpecialEventDto specialEventDto : dto.getSpecialEvents()) {
            //We build two sections for info only and normal
            StringBuilder builder = nonInfoBuilder;
            if (specialEventDto.getInformationUseOnly()) {
                builder = infoOnlyBuilder;
            }

            builder.append(specialEventDto.getEventName());
            builder.append("\n");
            builder.append(specialEventDto.getStartDate().toFormattedString(UiUtils.getDateFormatString()));
            builder.append(" to ");
            builder.append(specialEventDto.getEndDate().toFormattedString(UiUtils.getDateFormatString()));
            builder.append("\n\n");
        }

        StringBuilder titleBuilder = new StringBuilder();
        if (nonInfoBuilder.length() > nonInfoTitle.length()) {
            titleBuilder.append(nonInfoBuilder);
        }

        if (infoOnlyBuilder.length() > InfoOnlyTitle.length()) {
            titleBuilder.append(infoOnlyBuilder);
        }

        return titleBuilder.toString();
    }

    public void revertChange(CPOverrideWrapper overrideWrapper) {
        if (isNotEmpty(overrideWrapper.getCloseLv0RowModels()) || overrideWrapper.isRemoveAllLv0Override()) {
            revertCloseLv0OverrideChange(overrideWrapper);
        }
        overrideManager.revertChange(overrideWrapper);
        refreshView();
        enableSaveCancelButtons(hasPendingChanges() && hasPendingCloseLv0Overrides(overrideWrapper));
    }

    public boolean hasPendingCloseLv0Overrides(CPOverrideWrapper overrideWrapper) {
        if (pendingCloseLv0BarOverrides.isEmpty()) {
            return true;
        }
        return hasPendingRemoveOverrides(overrideWrapper) || hasPendingAddOverrides(overrideWrapper);
    }

    private boolean hasPendingAddOverrides(CPOverrideWrapper overrideWrapper) {
        return pendingCloseLv0BarOverrides.stream().filter(BAROverride::isRestrictHighestBarEnabled).anyMatch(barOverride -> getSelectedAccomTypeSummaryStream(barOverride).anyMatch(accomTypeSummary -> isPendingForAdd(accomTypeSummary, barOverride.getLengthOfStay(), overrideWrapper)));
    }

    private boolean isPendingForAdd(AccomTypeSummary accomTypeSummary, Integer lengthOfStay, CPOverrideWrapper overrideWrapper) {
        return getSelectedAccomTypeStreamForLos(lengthOfStay, overrideWrapper).noneMatch(accomType -> accomType.getName().equals(accomTypeSummary.getName()));
    }

    private Stream<AccomTypeSummary> getSelectedAccomTypeSummaryStream(BAROverride barOverride) {
        return barOverride.getAccomTypeSummaryList().stream().filter(accomTypeSummary -> accomTypeSummary.isSelected());
    }

    private boolean hasPendingRemoveOverrides(CPOverrideWrapper overrideWrapper) {
        return pendingCloseLv0BarOverrides.stream().filter(BAROverride::isRemoveRestrictHighestBarOverride).anyMatch(barOverride -> getNonSelectedAccomTypeSummaryStream(barOverride).anyMatch(accomTypeSummary -> isPendingForRemove(accomTypeSummary, barOverride.getLengthOfStay(), overrideWrapper)));
    }

    private Stream<AccomTypeSummary> getNonSelectedAccomTypeSummaryStream(BAROverride barOverride) {
        return barOverride.getAccomTypeSummaryList().stream().filter(accomTypeSummary -> !accomTypeSummary.isSelected());
    }

    private boolean isPendingForRemove(AccomTypeSummary accomTypeSummary, Integer lengthOfStay, CPOverrideWrapper overrideWrapper) {
        return getSelectedAccomTypeStreamForLos(lengthOfStay, overrideWrapper).anyMatch(accomType -> accomType.getName().equals(accomTypeSummary.getName()));
    }

    private Stream<AccomType> getSelectedAccomTypeStreamForLos(Integer lengthOfStay, CPOverrideWrapper overrideWrapper) {
        return overrideWrapper.getOriginalCloseLv0RowModels().get(lengthOfStay - 1).getAccomTypesSelected().stream();
    }

    public void revertCloseLv0OverrideChange(CPOverrideWrapper overrideWrapper) {
        if (overrideWrapper.isRemoveAllLv0Override()) {
            revertPendingRemoveLv0BarOverrides(overrideWrapper);
        } else if (overrideWrapper.getCloseLv0RowModels() != null) {
            revertPendingAddCloseLv0BarOverrides(overrideWrapper);
        }
    }

    private void revertPendingAddCloseLv0BarOverrides(CPOverrideWrapper overrideWrapper) {
        List<CloseLV0RowModel> closeLv0RowModels = overrideWrapper.getCloseLv0RowModels();
        closeLv0RowModels.forEach(closeLV0RowModel -> {
            AccomType accomType = overrideWrapper.getAccomType();
            boolean valueAfterRevert = getValueAfterRevert(overrideWrapper, closeLV0RowModel.getRowHeader(), accomType);
            closeLV0RowModel.setCheckBoxValue(accomType, valueAfterRevert);
            revertPendingAddLv0BarOverrides(overrideWrapper, valueAfterRevert, closeLV0RowModel.getRowHeader());
        });
    }

    private void revertPendingAddLv0BarOverrides(CPOverrideWrapper overrideWrapper, boolean valueAfterRevert, String rowHeader) {
        Integer lengthOfStay = getLosValueFromRowHeaderString(rowHeader);
        pendingCloseLv0BarOverrides.forEach(barOverride -> {
            if (barOverride.getArrivalDate().equals(overrideWrapper.getStartDate().toDateTimeAtStartOfDay().toDate()) && barOverride.getLengthOfStay().equals(lengthOfStay)) {
                setAccomSummaryValue(overrideWrapper, barOverride, valueAfterRevert);
            }
        });
    }

    private void setAccomSummaryValue(CPOverrideWrapper overrideWrapper, BAROverride barOverride, boolean value) {
        barOverride.getAccomTypeSummaryList().forEach(accomTypeSummary -> {
            if (accomTypeSummary.getName().equals(overrideWrapper.getAccomType().getName())) {
                accomTypeSummary.setIsSelected(value);
            }
        });
    }

    private int getLosValueFromRowHeaderString(String rowHeader) {
        return Integer.parseInt(rowHeader.substring(rowHeader.length() - 1));
    }

    private boolean getValueAfterRevert(CPOverrideWrapper overrideWrapper, String rowHeader, AccomType accomType) {
        Stream<CloseLV0RowModel> lv0RowModel = overrideWrapper.getOriginalCloseLv0RowModels().stream().filter(closeLV0RowModel -> closeLV0RowModel.getRowHeader().equals(rowHeader));
        return lv0RowModel.anyMatch(closeLV0RowModel -> closeLV0RowModel.getAccomTypesSelected().contains(accomType));
    }

    private void revertPendingRemoveLv0BarOverrides(CPOverrideWrapper overrideWrapper) {
        pendingCloseLv0BarOverrides.forEach(barOverride -> {
            if (barOverride.getArrivalDate().equals(overrideWrapper.getStartDate().toDateTimeAtStartOfDay().toDate()) && barOverride.isRemoveRestrictHighestBarOverride()) {
                setAccomSummaryValue(overrideWrapper, barOverride, true);
            }
        });
    }

    public void revertAgileRatesChange(CPOverrideWrapper overrideWrapper) {
        LocalDate arrivalDate = overrideWrapper.getCpDecisionBAROutput().getArrivalDate();
        Product currentProduct = overrideWrapper.getCpDecisionBAROutput().getProduct();
        Optional<PricingMultiProductOverrideDTOWrapper> existingDTO = multiProductAgileRatesDTOs
                .stream()
                .filter(item -> item.getPricingAgileRatesOverrideDTO().getDate().equals(overrideWrapper.getCpDecisionBAROutput().getArrivalDate()))
                .filter(item -> item.getPricingAgileRatesOverrideDTO().getProduct().equals(overrideWrapper.getCpDecisionBAROutput().getProduct()))
                .filter(item -> item.getPricingAgileRatesOverrideDTO().getRoomType().equals(overrideWrapper.getAccomType()))
                .findFirst();
        if (existingDTO.isPresent()) {
            multiProductAgileRatesDTOs.remove(existingDTO.get());
        }

        if (isOptimizedAndInProductGroup(asList(currentProduct))) {
            List<Product> productsInProductGroup = findProductsWithSameAgileRatesProductGroup(currentProduct);
            List<CPOverrideWrapper> overrideWrappers = results.stream()
                    .filter(wrapper -> wrapper.getCpbarDecisionDTO().getDate().equals(arrivalDate))
                    .flatMap(wrapper -> wrapper.getCpOverrideWrappers().stream())
                    .filter(wrapper -> productsInProductGroup.contains(wrapper.getCpDecisionBAROutput().getProduct()))
                    .collect(Collectors.toList());
            if (!isAgileRatesAndApplyToAllRoomClasses(currentProduct)) {
                overrideWrappers.removeIf(wrapper -> wrapper.getSelectedRoomType().getAccomClass().equals(overrideWrapper.getAccomClass()));
            }
            overrideWrappers.forEach(wrapper -> {
                revertOverrideWrapper(wrapper);
                productOverrides.remove(wrapper);
            });
        } else if (isAgileRatesAndApplyToAllRoomClasses(overrideWrapper.getCpDecisionBAROutput().getProduct())) {
            getOverrideWrappersForDate(arrivalDate, overrideWrapper.getCpDecisionBAROutput().getProduct())
                    .filter(wrapper -> wrapper.getCpDecisionBAROutput().getProduct().equals(currentProduct))
                    .forEach(wrapper -> {
                        revertOverrideWrapper(wrapper);
                        productOverrides.remove(wrapper);
                    });
        } else {
            getOverrideWrappersForDateAndAccomClass(arrivalDate, overrideWrapper.getAccomClass(), overrideWrapper.getCpDecisionBAROutput().getProduct())
                    .filter(wrapper -> wrapper.getCpDecisionBAROutput().getProduct().equals(currentProduct))
                    .forEach(wrapper -> {
                        revertOverrideWrapper(wrapper);
                        productOverrides.remove(wrapper);
                    });
        }

        refreshView();
        enableSaveCancelButtons(hasPendingChanges());
    }

    private void revertOverrideWrapper(CPOverrideWrapper overrideWrapper) {
        overrideManager.revertOverrideWrapper(overrideWrapper);
    }

    public List<CPBARDecisionUIWrapper> getResults() {
        return results;
    }

    @ForTesting
    public void setResults(List<CPBARDecisionUIWrapper> results) {
        this.results = results;
    }

    public boolean hasPendingChanges() {
        return hasChanges();
    }

    public boolean detailsButtonIsVisible(LocalDate date) {
        return (showInvestigator() && !isAgileRatesEnabled() && !date.isBefore(getSystemDateAsLocalDate()))
                || (showInvestigator() && isAgileRatesEnabled() && (isOnlyBARProductSelected()) && !date.isBefore(getSystemDateAsLocalDate()));
    }

    public boolean productLevelDetailsButtonIsVisible(LocalDate date) {
        return showInvestigator() && !date.isBefore(getSystemDateAsLocalDate());
    }

    public boolean showAgileRatesOverridePopup() {
        return isAgileRatesEnabled() && !isOnlyBARProductSelected() && !isOnlyIndependentProductSelected();
    }

    public boolean isAgileRatesProduct(Product product) {
        return isAgileRatesEnabled() && !product.isSystemDefaultOrIndependentProduct();
    }

    public boolean isOnlyBARProductSelected() {
        return getCpPricingFilter().getProducts().size() == 1 && getCpPricingFilter().getProducts().iterator().next().isSystemDefault();
    }

    public boolean isOnlyIndependentProductSelected() {
        return getCpPricingFilter().getProducts().size() == 1 && getCpPricingFilter().getProducts().iterator().next().isIndependentProduct();
    }

    public boolean isBarProduct(Product product) {
        return product.getId().equals(1);
    }

    public String truncateLabel(String value, int maxLength) {
        if (value.length() > maxLength) {
            return value.substring(0, maxLength) + "...";
        }
        return value;
    }

    public String getAccomTypeCaption(AccomType accomType, int maxLength) {
        if (isBaseRoomType(accomType)) {
            //need to adjust for the size of the icon
            int adjustedMaxLength = maxLength - 2;
            if (accomType.getName().length() > adjustedMaxLength) {
                return TetrisFontAwesome.BASE_ROOM_TYPE.getHtml() + " " + accomType.getName().substring(0, adjustedMaxLength) + "...";
            }
            return TetrisFontAwesome.BASE_ROOM_TYPE.getHtml() + " " + accomType.getName();
        }
        return truncateLabel(accomType.getName(), maxLength);
    }

    public String getDescription(String value, int maxLength) {
        if (value.length() > maxLength) {
            return value;
        }
        return null;
    }

    public CPOverrideWrapper findOverrideWrapper(LocalDate date, AccomType accomType, Product product) {
        List<CPOverrideWrapper> overrideWrappers = results.stream()
                .filter(wrapper -> wrapper.getCpbarDecisionDTO().getDate().equals(date))
                .findFirst()
                .map(CPBARDecisionUIWrapper::getCpOverrideWrappers)
                .orElse(new ArrayList<>());
        return overrideWrappers.stream()
                .filter(wrapper -> wrapper.getCpDecisionBAROutput().getProduct().equals(product))
                .filter(wrapper -> wrapper.getCpDecisionBAROutput().getAccomType().equals(accomType))
                .findFirst()
                .orElse(null);
    }

    public PricingAgileRatesOverrideDTO getAgileRatesOverrideDTO(LocalDate date, AccomClass roomClass, AccomType roomType, CPOverrideWrapper overrideWrapper) {
        if (overrideWrapper != null) {
            return getAgileRatesOverrideDTO(overrideWrapper, roomClass, roomType);
        } else {
            return createAgileRatesOverrideDTO(date, roomClass, roomType);
        }
    }

    protected PricingAgileRatesOverrideDTO createAgileRatesOverrideDTO(LocalDate date, AccomClass roomClass, AccomType roomType) {
        return createPricingAgileRatesOverrideDTO(date, roomClass, roomType);
    }

    public PricingAgileRatesOverrideDTO getAgileRatesOverrideDTO(CPOverrideWrapper wrapper, AccomClass roomClass, AccomType roomType) {
        PricingAgileRatesOverrideDTO dto = getAgileRatesOverrideDTOWithoutBaseProductRate(wrapper, roomClass, roomType, new HashMap<>(), true);

        LocalDate arrivalDate = wrapper.getCpDecisionBAROutput().getArrivalDate();
        CPDecisionBAROutput baseProductDecision = service.getProductDecision(arrivalDate, dto.getBaseProduct(), wrapper.getSelectedRoomType());
        if (baseProductDecision != null) {
            dto.setBaseProductValue(baseProductDecision.getSpecificOverride() != null ? baseProductDecision.getSpecificOverride() : baseProductDecision.getFinalBAR());
        }
        dto.setCalculatedAdjustment(null != dto.getAdjustment() && null != dto.getBaseProductValue() ? dto.getBaseProductValue().subtract(dto.getAdjustment()) : null);
        if(isGroupProductInventoryLimitEnabled && atLeastOneGroupProductSelected()) {
            InventoryLimitDecision inventoryLimitDecision = inventoryLimits.stream().filter(ild-> ild.getOccupancyDate().equals(arrivalDate.toDate())).findFirst().orElse(null);
            dto.setInventoryLimit(inventoryLimitDecision == null ? null : inventoryLimitDecision.getInventoryLimit());
            InventoryLimitDecision inventoryLimitOverrideDecision = overriddenInventoryLimits.stream().filter(ilod -> ilod.getOccupancyDate().equals(arrivalDate.toDate())).findFirst().orElse(null);
            dto.setOriginalInventoryLimitOverride(inventoryLimitOverrideDecision == null ? null : inventoryLimitOverrideDecision.getInventoryLimit());
        }
        return dto;
    }

    public PricingAgileRatesOverrideDTO getAgileRatesOverrideDTOWithoutBaseProductRate(CPOverrideWrapper wrapper, AccomClass roomClass, AccomType roomType, Map<Product, List<ProductRateOffset>> rateOffsetsByProducts, boolean includeOverrideHistory) {
        final LocalDate date = wrapper.getCpDecisionBAROutput().getArrivalDate();

        PricingAgileRatesOverrideDTO dto = createPricingAgileRatesOverrideDTO(wrapper, date, roomClass, roomType);
        dto.setFinalPrice(wrapper.getSpecificOverride() != null ? wrapper.getSpecificOverride() : wrapper.getRoundedBAR());
        dto.setOverrideWrapper(wrapper);

        ProductRateOffset discount = getDiscountValue(wrapper, rateOffsetsByProducts);
        dto.setFloorDiscount(discount != null ? discount.getFloorValueForDate(date) : null);
        dto.setCeilingDiscount(discount != null ? discount.getCeilingValueForDate(date) : null);
        dto.setDiscountOffsetMethod(discount != null ? discount.getOffsetMethod() : null);

        ProductRateOffsetOverride existingOverride = null;

        if (CollectionUtils.isNotEmpty(wrapper.getProductRateOffsetOverrides())) {
            boolean pendingOverridesExist = wrapper.getProductRateOffsetOverrides().stream()
                    .anyMatch(proo -> proo.getCreateDate() == null);
            if (pendingOverridesExist) {
                existingOverride = wrapper.getProductRateOffsetOverrides().stream()
                        .filter(proo -> proo.getCreateDate() == null)
                        .findFirst()
                        .orElse(null);
            } else {
                existingOverride = wrapper.getProductRateOffsetOverrides().stream()
                        .filter(productRateOffsetOverride -> productRateOffsetOverride.getStatusId().equals(Status.ACTIVE.getId()))
                        .max(Comparator.comparing(ProductRateOffsetOverride::getCreateDate))
                        .orElse(null);
            }
        }

        ProductRateOffsetOverride savedOverride = CollectionUtils.isEmpty(wrapper.getOriginalProductRateOffsetOverrides()) ? null :
                wrapper.getOriginalProductRateOffsetOverrides().stream()
                        .filter(productRateOffsetOverride -> productRateOffsetOverride.getStatusId().equals(Status.ACTIVE.getId()))
                        .max(Comparator.comparing(ProductRateOffsetOverride::getCreateDate))
                        .orElse(null);


        if (existingOverride != null) {
            dto.setAdjustment(existingOverride.getOffsetValue());
            dto.setOriginalAdjustment(existingOverride.getOffsetValue());
            dto.setFloorOffset(existingOverride.getFloorValue());
            dto.setOriginalFloorOffset(existingOverride.getFloorValue());
            dto.setCeilingOffset(existingOverride.getCeilingValue());
            dto.setOriginalCeilingOffset(existingOverride.getCeilingValue());
            dto.setOverride(savedOverride);

            dto.setFloorMarkedForDeletion(wrapper.isFloorOverridePendingDelete());
            dto.setCeilingMarkedForDeletion(wrapper.isCeilingOverridePendingDelete());
        }
        if (includeOverrideHistory) {
            dto.setOverrideHistory(getAgileRatesOverrideHistory(wrapper));
        }
        if(isGroupProductInventoryLimitEnabled && atLeastOneGroupProductSelected()) {
        List<DecisionGPInventoryLimitOverride> inventoryOverrideHistory = overrideManager.getInventoryForAllDates(getCpPricingFilter().getStartDate().toDate(), getCpPricingFilter().getEndDate().toDate());
        setInventoryLimitHistoryOverrides(getAgileRatesInventoryLimitOverrideHistory(wrapper.getCpDecisionBAROutput().getArrivalDate().toDate(), inventoryOverrideHistory));
        }
        return dto;
    }

    private PricingMultiProductOverrideDTOWrapper createMultiProductDTOAgileRates(PricingAgileRatesOverrideDTO agileRatesOverrideDTO) {
        if (agileRatesOverrideDTO.getProduct().isOptimized()) {
            PricingMultiProductOverrideDTOWrapper optimizedWrapper = new PricingMultiProductOverrideDTOWrapper(null, agileRatesOverrideDTO, MultiProductEnum.AGILE_RATES_OPTIMIZED);
            multiProductAgileRatesDTOs.add(optimizedWrapper);
            return optimizedWrapper;
        } else {
            PricingMultiProductOverrideDTOWrapper nonOptimizedWrapper = new PricingMultiProductOverrideDTOWrapper(null, agileRatesOverrideDTO, MultiProductEnum.AGILE_RATES_NON_OPTIMIZED);
            multiProductAgileRatesDTOs.add(nonOptimizedWrapper);
            return nonOptimizedWrapper;
        }
    }

    private PricingMultiProductOverrideDTOWrapper createMultiProductDTOBarProduct(CPBARDecisionUIWrapper wrapper, CPOverrideWrapper overrideWrapper) {
        InvestigatorDto investigatorDTO = createInvestigatorDTO(wrapper, overrideWrapper);
        return new PricingMultiProductOverrideDTOWrapper(investigatorDTO, null, MultiProductEnum.INVESTIGATOR);
    }

    public PricingMultiProductOverrideDTOWrapper createMultiProductDTO(CPBARDecisionUIWrapper wrapper, CPOverrideWrapper overrideWrapper) {
        //Selected Product to determine Product/Optimized status
        Product selectedProduct = overrideWrapper.getCpDecisionBAROutput().getProduct();

        if (selectedProduct.isSystemDefaultOrIndependentProduct()) {
            return createMultiProductDTOBarProduct(wrapper, overrideWrapper);
        } else {
            Optional<PricingMultiProductOverrideDTOWrapper> existingDTO = multiProductAgileRatesDTOs
                    .stream()
                    .filter(item -> item.getPricingAgileRatesOverrideDTO().getDate().equals(overrideWrapper.getCpDecisionBAROutput().getArrivalDate()))
                    .filter(item -> item.getPricingAgileRatesOverrideDTO().getProduct().equals(overrideWrapper.getCpDecisionBAROutput().getProduct()))
                    .filter(item -> item.getPricingAgileRatesOverrideDTO().getRoomType().equals(overrideWrapper.getAccomType()))
                    .findFirst();
            if (existingDTO.isPresent()) {
                return existingDTO.get();
            } else {
                PricingAgileRatesOverrideDTO agileRatesOverrideDTO = getAgileRatesOverrideDTO(overrideWrapper, overrideWrapper.getAccomClass(), overrideWrapper.getAccomType());
                return createMultiProductDTOAgileRates(agileRatesOverrideDTO);
            }
        }
    }

    private PricingAgileRatesOverrideDTO createPricingAgileRatesOverrideDTO(LocalDate date, AccomClass roomClass, AccomType roomType) {
        PricingAgileRatesOverrideDTO dto = new PricingAgileRatesOverrideDTO();
        //This is only used in the old flow with only one product selected
        Product product = getCpPricingFilter().getProducts().iterator().next();
        dto.setProduct(product);
        dto.setDate(date);
        dto.setRoomClass(roomClass);
        dto.setRoomType(roomType);
        Product baseProduct = getBaseProduct(product);
        setIsBaseRoomType(roomType);
        dto.setBaseProduct(baseProduct);
        dto.setOverrideWrapper(new CPOverrideWrapper());
        if(isGroupProductInventoryLimitEnabled && atLeastOneGroupProductSelected()) {
            InventoryLimitDecision inventoryLimitDecision = inventoryLimits.stream().filter(ild -> ild.getOccupancyDate().equals(date.toDate())).findFirst().orElse(null);
            dto.setInventoryLimit(inventoryLimitDecision == null ? null : inventoryLimitDecision.getInventoryLimit());
            InventoryLimitDecision inventoryLimitOverrideDecision = overriddenInventoryLimits.stream().filter(ilod -> ilod.getOccupancyDate().equals(date.toDate())).findFirst().orElse(null);
            dto.setOriginalInventoryLimitOverride(inventoryLimitOverrideDecision == null ? null : inventoryLimitOverrideDecision.getInventoryLimit());
        }
        return dto;
    }

    private Product getBaseProduct(Product product) {
        return allProducts.stream().filter(p -> p.getId().equals(product.getDependentProductId())).findFirst().orElse(null);
    }

    private PricingAgileRatesOverrideDTO createPricingAgileRatesOverrideDTO(CPOverrideWrapper overrideWrapper, LocalDate date, AccomClass roomClass, AccomType roomType) {
        PricingAgileRatesOverrideDTO dto = new PricingAgileRatesOverrideDTO();
        Product product = overrideWrapper.getCpDecisionBAROutput().getProduct();
        dto.setProduct(product);
        dto.setDate(date);
        dto.setRoomClass(roomClass);
        dto.setRoomType(roomType);
        Product baseProduct = getBaseProduct(product);
        setIsBaseRoomType(roomType);
        dto.setBaseProduct(baseProduct);
        dto.setOverrideWrapper(new CPOverrideWrapper());
        if(isGroupProductInventoryLimitEnabled && atLeastOneGroupProductSelected()) {
            InventoryLimitDecision inventoryLimitDecision = inventoryLimits.stream().filter(ild -> ild.getOccupancyDate().equals(date.toDate())).findFirst().orElse(null);
            dto.setInventoryLimit(inventoryLimitDecision == null ? null : inventoryLimitDecision.getInventoryLimit());
            InventoryLimitDecision inventoryLimitOverrideDecision = overriddenInventoryLimits.stream().filter(ilod -> ilod.getOccupancyDate().equals(date.toDate())).findFirst().orElse(null);
            dto.setOriginalInventoryLimitOverride(inventoryLimitOverrideDecision == null ? null : inventoryLimitOverrideDecision.getInventoryLimit());
        }
        return dto;
    }

    public InvestigatorDto createInvestigatorDTO(CPBARDecisionUIWrapper cpbarDecisionUIWrapper, CPOverrideWrapper overrideWrapper) {
        CPBARDecisionDTO dto = cpbarDecisionUIWrapper.getCpbarDecisionDTO();

        InvestigatorDto investigatorDto = new InvestigatorDto();
        investigatorDto.setModuleOrigin(TetrisPermissionKey.PRICING);
        investigatorDto.setOverride(true);
        investigatorDto.setSelectedDate(dto.getDate());
        investigatorDto.setStartDate(getCpPricingFilter().getStartDate());
        investigatorDto.setEndDate(getCpPricingFilter().getEndDate());
        investigatorDto.setSelectedAccomClassId(overrideWrapper.getAccomClass().getId());
        investigatorDto.setSelectedAccomTypeId(overrideWrapper.getSelectedRoomType().getId());
        overrideWrapper.setStartDate(dto.getDate());
        overrideWrapper.setEndDate(dto.getDate());
        investigatorDto.setWrapper(overrideWrapper);
        investigatorDto.setBaseRoomType(overrideWrapper.isBaseRoomType());
        investigatorDto.setRoomClassPriceExcluded(isRoomClassPriceExcluded(overrideWrapper.getAccomClass()));
        investigatorDto.setSelectedRoomClasses(getSelectedAccomClassesBasedOnSearchCriteria());
        investigatorDto.setSelectedRoomTypes(getOrderedAndFilteredTypes().stream().map(AccomType::getId).collect(Collectors.toList()));
        Map<LocalDate, List<InvestigatorDto>> investigatorDtoByDate = getInvestigatorDtosByDate(true);
        investigatorDto.setOverrides(investigatorDtoByDate);
        investigatorDto.setInvestigatorEnabled(showInvestigator());
        return investigatorDto;
    }

    private void setIsBaseRoomType(AccomType roomType) {
        if (isNotEmpty(pricingAccomClasses)) {
            for (PricingAccomClass pricingAccomClass : pricingAccomClasses) {
                if (pricingAccomClass.getAccomType().equals(roomType)) {
                    roomType.setBaseRoomType(true);
                }
            }
        }
    }

    protected ProductRateOffset getDiscountValue(CPOverrideWrapper wrapper, Map<Product, List<ProductRateOffset>> rateOffsetsByProducts) {
        LocalDate arrivalDate = wrapper.getCpDecisionBAROutput().getArrivalDate();
        Product product = wrapper.getCpDecisionBAROutput().getProduct();
        List<ProductRateOffset> productRateOffsets;
        if (rateOffsetsByProducts.containsKey(product)) {
            productRateOffsets = rateOffsetsByProducts.get(product);
        } else {
            productRateOffsets = service.getProductDiscount(product);
        }
        //first check seasons
        List<ProductRateOffset> discounts = productRateOffsets.stream()
                .filter(offset -> offset.getStartDate() != null &&
                        offset.getEndDate() != null &&
                        !arrivalDate.isBefore(offset.getStartDate()) &&
                        !arrivalDate.isAfter(offset.getEndDate()) &&
                        offset.getFloorValueForDate(arrivalDate) != null)
                .collect(Collectors.toList());

        //if no seasons then check defaults
        if (discounts.isEmpty()) {
            discounts = productRateOffsets.stream()
                    .filter(offset -> offset.getStartDate() == null &&
                            offset.getEndDate() == null &&
                            offset.getFloorValueForDate(arrivalDate) != null)
                    .collect(Collectors.toList());
        }

        //if no defaults then return null
        if (discounts.isEmpty()) {
            return null;
        }

        //check for room classes
        if (discounts.stream()
                .anyMatch(discount -> wrapper.getSelectedRoomType().getAccomClass().equals(discount.getAccomClass()))) {
            discounts = discounts.stream()
                    .filter(discount -> wrapper.getSelectedRoomType().getAccomClass().equals(discount.getAccomClass()))
                    .collect(Collectors.toList());
        }

        //discounts is not empty so find correct offset for dta range
        //adding one to the difference as SAS considers the day of arrival as a value of one
        //so we need to account for one less decision in the first bucket
        int dta = getDaysBetweenSystemDateAndArrivalDate(wrapper) + 1;

        return discounts.stream()
                .filter(pro -> {
                    AgileRatesDTARange range = pro.getAgileRatesDTARange();
                    return range != null &&
                            range.getMinDaysToArrival().compareTo(dta) <= 0 &&
                            range.getMaxDaysToArrival() != null &&
                            range.getMaxDaysToArrival().compareTo(dta) >= 0;
                })
                .findFirst()
                .orElse(discounts.stream()
                        .filter(pro -> pro.getAgileRatesDTARange() != null && pro.getAgileRatesDTARange().getMaxDaysToArrival() == null)
                        .findFirst()
                        .orElse(discounts.stream()
                                .findFirst()
                                .orElse(null)));
    }

    private int getDaysBetweenSystemDateAndArrivalDate(CPOverrideWrapper wrapper) {
        return (int) DateUtil.daysBetween(getSystemDateAsLocalDate().toDate(), wrapper.getCpDecisionBAROutput().getArrivalDate().toDate());
    }

    public void applyProductRateOffsetOverrideWithProductGroups(PricingAgileRatesOverrideDTO dto, Boolean isAgileRatesAndApplyToAllRoomClasses) {
        List<ProductRateOffsetOverride> allOverrides = new ArrayList<>();
        List<Product> productsWithSameParentGroup = findProductsWithSameAgileRatesProductGroup(dto.getProduct());
        final CPBARDecisionUIWrapper cpbarDecisionUIWrapper = results.stream().filter(resultsDTO ->
                resultsDTO.getCpbarDecisionDTO().getDate().equals(dto.getDate())).findFirst().orElse(null);
        List<CPOverrideWrapper> wrappers = cpbarDecisionUIWrapper.getCpOverrideWrappers().stream().filter(w -> productsWithSameParentGroup.contains(w.getProduct())).collect(Collectors.toList());
        if (!dto.isFullyDeleted()) {
            if (isAgileRatesAndApplyToAllRoomClasses) {
                productsWithSameParentGroup.forEach(product -> allOverrides.addAll(createOverrideByDimensionsProductGroups(dto, product, findSortedRoomClassesForProduct(product))));
            } else {
                List<AccomClass> roomClasses = new ArrayList<>();
                roomClasses.add(dto.getRoomClass());
                productsWithSameParentGroup.forEach(product -> allOverrides.addAll(createOverrideByDimensionsProductGroups(dto, product, roomClasses)));
            }
        } else {
            // DELETE PATH
            productsWithSameParentGroup.forEach(product -> {
                if (!isAgileRatesAndApplyToAllRoomClasses) {
                    allOverrides.addAll(getProductRateOffsetOverridesWithProductGroups().stream()
                            .filter(proo -> proo.getOccupancyDate().equals(dto.getDate()))
                            .filter(proo -> proo.getProduct().equals(product))
                            .filter(proo -> proo.getAccomClass().equals(dto.getRoomClass()))
                            .collect(Collectors.toList()));
                } else {
                    allOverrides.addAll(getProductRateOffsetOverridesWithProductGroups().stream()
                            .filter(proo -> proo.getOccupancyDate().equals(dto.getDate()))
                            .filter(proo -> proo.getProduct().equals(product))
                            .collect(Collectors.toList()));
                }
            });
        }
        if (!dto.isFullyDeleted()) {
            setOverrideOffsetValues(dto, allOverrides);
        }

        if (cpbarDecisionUIWrapper != null) {
            wrappers = applyProductGroupOverrides(dto, productsWithSameParentGroup, wrappers, allOverrides);
        }
        productOverrides.addAll(wrappers);
    }

    public List<CPOverrideWrapper> applyProductGroupOverrides(PricingAgileRatesOverrideDTO dto, List<Product> productsWithSameParentGroup, List<CPOverrideWrapper> wrappers, List<ProductRateOffsetOverride> allOverrides) {
        if (!isAgileRatesAndApplyToAllRoomClasses(dto.getProduct())) {
            wrappers.removeIf(w -> !w.getAccomClass().equals(dto.getRoomClass()));
        }
        final List<ProductRateOffsetOverride>[] overridesToDelete = new List[]{new ArrayList<>()};
        wrappers.forEach(wrapper -> {
            setWrapperSaveDeleteFlagsWithProductGroups(dto, wrapper, allOverrides);
            if (overridesToDelete[0].isEmpty() && !dto.isFullyDeleted()) {
                overridesToDelete[0] = getProductRateOffsetOverridesToDelete(getProductRateOffsetOverridesWithProductGroups(),
                        productsWithSameParentGroup, wrapper.getCpDecisionBAROutput().getArrivalDate());
                if (!isAgileRatesAndApplyToAllRoomClasses(dto.getProduct())) {
                    overridesToDelete[0].removeIf(o -> !o.getAccomClass().equals(dto.getRoomClass()));
                }
                wrapper.getOriginalProductRateOffsetOverrides().addAll(overridesToDelete[0]);
            }
            if (dto.isFullyDeleted()) {
                wrapper.getOriginalProductRateOffsetOverrides().addAll(allOverrides);
            }
        });
        return wrappers;
    }

    public void applyProductRateOffsetOverride(PricingAgileRatesOverrideDTO dto) {
        boolean isAgileRatesAndApplyToAllRoomClasses = isAgileRatesAndApplyToAllRoomClasses(dto.getProduct());
        boolean isOptimizedAndInProductGroup = isOptimizedAndInProductGroup(asList(dto.getProduct()));
        List<AccomClass> roomClasses = new ArrayList<>();
        if (isAgileRatesAndApplyToAllRoomClasses) {
            roomClasses.addAll(findRoomClassesByProduct(dto.getProduct()));
        } else {
            roomClasses.add(dto.getRoomClass());
        }

        if (isOptimizedAndInProductGroup) {
            applyProductRateOffsetOverrideWithProductGroups(dto, isAgileRatesAndApplyToAllRoomClasses);
        } else {
            List<ProductRateOffsetOverride> overrides = new ArrayList<>();
            if (!dto.isFullyDeleted()) {
                //Save Path or needs all room classes added in rather than single filter
                overrides = createOverridesByDimensions(dto, roomClasses);
            } else {
                //Delete Path
                if (isAgileRatesAndApplyToAllRoomClasses) {
                    //Collect all Product Rate Offset Overrides ignoring room class card filtering
                    overrides.addAll(getProductRateOffsetOverrides().stream()
                            .filter(proo -> proo.getOccupancyDate().equals(dto.getDate()))
                            .filter(proo -> proo.getProduct().equals(dto.getProduct()))
                            .collect(Collectors.toList()));
                } else {
                    overrides.addAll(dto.getOverrideWrapper().getOriginalProductRateOffsetOverrides());
                }
            }
            setOverrideOffsetValues(dto, overrides);

            applyProductRateOffsetOverrides(dto, overrides);
        }

        enableSaveCancelButtons(true);
        refreshView();
    }

    public void applyInventoryLimitOverrides(PricingAgileRatesOverrideDTO dto) {
        filterInventoryLimitOverrides(dto.getDate());
        if(dto.isInventoryLimitMarkedForDeletion()) {
            dto.getOverrideWrapper().setInventoryLimitMarkedForDeletion(dto.isInventoryLimitMarkedForDeletion());
            dto.getOverrideWrapper().setInventoryLimitOverrideOccupancyDt(dto.getDate().toDate());
            inventoryLimitOverrides.add(dto.getOverrideWrapper());
        }
        if(!dto.isInventoryLimitMarkedForDeletion() && null != dto.getInventoryLimitOverride() && !Objects.equals(dto.getOriginalInventoryLimitOverride(), dto.getInventoryLimitOverride())) {
            dto.getOverrideWrapper().setInventoryLimitMarkedForDeletion(dto.isInventoryLimitMarkedForDeletion());
            dto.getOverrideWrapper().setInventoryLimit(dto.getInventoryLimit());
            dto.getOverrideWrapper().setInventoryLimitOverride(dto.getInventoryLimitOverride());
            dto.getOverrideWrapper().setOriginalInventoryLimitOverride(dto.getOriginalInventoryLimitOverride());
            dto.getOverrideWrapper().setInventoryLimitOverrideOccupancyDt(dto.getDate().toDate());
            inventoryLimitOverrides.add(dto.getOverrideWrapper());
        }
        enableSaveCancelButtons(true);
        refreshView();
    }
    private void filterInventoryLimitOverrides(LocalDate dto) {
        for (CPOverrideWrapper overrideWrapper : inventoryLimitOverrides) {
            if (overrideWrapper.getCpDecisionBAROutput().getArrivalDate().equals(dto)) {
                inventoryLimitOverrides.remove(overrideWrapper);
                break;
            }
        }
    }
    @ForTesting
    public void setInventoryLimits(List<InventoryLimitDecision> inventoryLimits) {
        this.inventoryLimits = inventoryLimits;
    }
    @ForTesting
    public void setOverriddenInventoryLimits(List<InventoryLimitDecision> overriddenInventoryLimits) {
        this.overriddenInventoryLimits = overriddenInventoryLimits;
    }
    public List<BusinessAnalysisDailyDataDto> getBusinessAnalysisDailyDataDtos() {
        return businessAnalysisDailyDataDtos;
    }
    @ForTesting
    public void setBusinessAnalysisDailyDataDtos(List<BusinessAnalysisDailyDataDto> businessAnalysisDailyDataDtos) {
        this.businessAnalysisDailyDataDtos = businessAnalysisDailyDataDtos;
    }
    @ForTesting
    public List<CPOverrideWrapper> getInventoryLimitOverrides() {
        return inventoryLimitOverrides;
    }
    public Integer getPhysicalCapacityByOccupancyDate(Date occupancyDate) {
        for (BusinessAnalysisDailyDataDto dto : getBusinessAnalysisDailyDataDtos()) {
            if (DateParameter.fromDate(occupancyDate).equals(dto.getDate())) {
                return Math.toIntExact(dto.getCapacity());
            }
        }
        return null;
    }
    public Integer getLowestPhysicalCapacityForSelectedDows(Set<DayOfWeek> dayOfWeeks) {
        List<Integer> capacityList = new ArrayList<>();
        businessAnalysisDailyDataDtos.stream().forEach(dto-> {
            if(dayOfWeeks.contains(DayOfWeek.forShortCaption(dto.getDayOfWeek()))) {
                capacityList.add(Math.toIntExact(dto.getCapacity()));
            }
        });
        return (capacityList.isEmpty()) ? 0 : Collections.min(capacityList);
    }
    public void applyProductRateOffsetOverrides(PricingAgileRatesOverrideDTO dto, List<ProductRateOffsetOverride> overrides) {
        if (isAgileRatesAndApplyToAllRoomClasses(dto.getProduct())) {
            //Wrapper with all roomClass data
            final CPBARDecisionUIWrapper cpbarDecisionUIWrapper = results.stream().filter(resultsDTO -> resultsDTO.getCpbarDecisionDTO().getDate().equals(dto.getDate())).findFirst().orElse(null);
            final List<ProductRateOffsetOverride>[] overridesToDelete = new List[]{new ArrayList<>()};
            if (cpbarDecisionUIWrapper != null) {
                List<ProductRateOffsetOverride> finalOverrides = overrides;
                cpbarDecisionUIWrapper.getCpOverrideWrappers().stream()
                        .filter(wrapper -> wrapper.getCpDecisionBAROutput().getProduct().equals(dto.getProduct()))
                        .forEach(wrapper -> {
                            if (overridesToDelete[0].isEmpty() && !dto.isFullyDeleted()) {
                                overridesToDelete[0] = getProductRateOffsetOverridesToDelete(getProductRateOffsetOverrides(), wrapper);
                                wrapper.setOriginalProductRateOffsetOverrides(overridesToDelete[0]);
                            }
                            setWrapperSaveDeleteFlags(dto, wrapper, finalOverrides);
                            if (dto.isFullyDeleted()) {
                                wrapper.getOriginalProductRateOffsetOverrides().addAll(finalOverrides);
                            }
                        });
            }
            productOverrides.addAll(cpbarDecisionUIWrapper.getCpOverrideWrappers());
        } else {
            setWrapperSaveDeleteFlags(dto, dto.getOverrideWrapper(), overrides);
            productOverrides.add(dto.getOverrideWrapper());
        }
    }

    protected List<AccomClass> findRoomClassesByProduct(Product product) {
        List<ProductAccomType> productAccomTypesForProduct = productATMapping.stream()
                .filter(productAccomType -> productAccomType.getProduct().equals(product))
                .collect(Collectors.toList());
        return roomClassList.stream()
                .filter(accomClass -> accomClass.getAccomTypes().stream()
                        .anyMatch(productAccomTypesForProduct.stream()
                                .map(ProductAccomType::getAccomType)
                                .collect(Collectors.toSet())::contains))
                .collect(Collectors.toList());
    }

    public List<AccomClass> findSortedRoomClassesForProduct(Product product) {
        List<AccomClass> roomClassesByProduct = findRoomClassesByProduct(product);
        return PricingUtil.sortAccomClassesByAccomClassOrder(roomClassesByProduct);
    }

    //Saving process for inline edit save for agile rates products
    public void agileRatesInlineEditingSave(Set<Product> products, List<PricingAgileRatesOverrideDTO> dtosToApply, List<PricingAgileRatesOverrideDTO> revertAgileRatesDTOList) {
        //Apply revert for optimized reverted wrappers
        if (CollectionUtils.isNotEmpty(revertAgileRatesDTOList)) {
            revertAgileRatesDTOList.forEach(dto -> {
                revertAgileRatesChange(dto.getOverrideWrapper());
            });
        }
        //Apply step simulating applying to grid followed by save step
        if (CollectionUtils.isNotEmpty(dtosToApply)) {
            agileRatesInlineEditApplyStep(products, dtosToApply);
        }
    }

    public void agileRatesInlineEditApplyStep(Set<Product> products, List<PricingAgileRatesOverrideDTO> dtosToApply) {
        List<ProductAccomType> productRoomTypeByProducts = agileRatesConfigurationService.findProductRoomTypeByProducts(products);
        //If all room classes are selected Product Rate Offsets aren't required to search for other classes due to it being a 1 to 1 mapping
        if (isAllRoomClassesSelected()) {
            dtosToApply.forEach(dto -> {
                if (isOptimizedAndInProductGroup(asList(dto.getProduct()))) {
                    applyProductRateOffsetOverrideWithProductGroups(dto, isAgileRatesAndApplyToAllRoomClasses(dto.getProduct()));
                } else {
                    List<AccomClass> accomClassesByProduct = productRoomTypeByProducts.stream()
                            .filter(item -> item.getProduct().equals(dto.getProduct()))
                            .map(item -> item.getAccomType().getAccomClass())
                            .distinct()
                            .collect(Collectors.toList());
                    applyProductRateOffsetOverrideInlineEdit(dto, accomClassesByProduct);
                }
            });
        } else {
            dtosToApply.forEach(dto -> {
                //If the filter is down to a single room class, we will need to query all room classes to apply Product Rate Offset Overrides to
                //If apply to all room classes, then ensure we apply to other non-visible classes
                if (isOptimizedAndInProductGroup(asList(dto.getProduct()))) {
                    applyProductRateOffsetOverrideWithProductGroups(dto, isAgileRatesAndApplyToAllRoomClasses(dto.getProduct()));
                } else {
                    if (isAgileRatesAndApplyToAllRoomClasses(dto.getProduct())) {
                        List<AccomClass> accomClassesByProduct = productRoomTypeByProducts.stream()
                                .filter(item -> item.getProduct().equals(dto.getProduct()))
                                .map(item -> item.getAccomType().getAccomClass())
                                .collect(Collectors.toList());
                        applyProductRateOffsetOverrideInlineEdit(dto, accomClassesByProduct);
                    } else {
                        applyProductRateOffsetOverrideInlineEdit(dto, asList(dto.getRoomClass()));
                    }
                }
            });
        }
    }

    public boolean isAllRoomClassesSelected() {
        return getCpPricingFilter().getSelectedRoomClass().equals(getAllRoomClasses());
    }

    //Differentiating this since the apply to all will be built into the grid so the process will be simplified slightly
    public void applyProductRateOffsetOverrideInlineEdit(PricingAgileRatesOverrideDTO dto, List<AccomClass> roomClasses) {
        List<ProductRateOffsetOverride> overrides = new ArrayList<>();
        if (!isAgileRatesAndApplyToAllRoomClasses(dto.getProduct())) {
            roomClasses.removeIf(rc -> !dto.getRoomClass().equals(rc));
        }
        if (!dto.isFullyDeleted()) {
            //Save Path or needs all room classes added in rather than single filter
            overrides = createOverridesByDimensions(dto, roomClasses);
        } else {
            //Delete Path
            if (roomClasses.size() > 1) {
                //Collect all Product Rate Offset Overrides ignoring room class card filtering
                overrides.addAll(getProductRateOffsetOverrides()
                        .stream()
                        .filter(proo -> proo.getProduct().equals(dto.getProduct()))
                        .filter(proo -> roomClasses.contains(proo.getAccomClass()))
                        .filter(proo -> proo.getOccupancyDate().equals(dto.getDate()))
                        .collect(Collectors.toList()));
            } else {
                overrides.addAll(dto.getOverrideWrapper().getOriginalProductRateOffsetOverrides());
            }
        }

        //Sets the values based on the DTOs provided per the product rate offset override generated
        setOverrideOffsetValues(dto, overrides);

        if (roomClasses.size() > 1) {
            //Wrapper with all roomClass data
            final CPBARDecisionUIWrapper cpbarDecisionUIWrapper = results.stream()
                    .filter(resultsDTO -> resultsDTO.getCpbarDecisionDTO().getDate().equals(dto.getDate()))
                    .findFirst()
                    .orElse(null);
            final List<ProductRateOffsetOverride>[] overridesToDelete = new List[]{new ArrayList<>()};
            if (cpbarDecisionUIWrapper != null) {
                List<ProductRateOffsetOverride> finalOverrides = overrides;
                cpbarDecisionUIWrapper.getCpOverrideWrappers().stream()
                        .filter(wrapper -> wrapper.getCpDecisionBAROutput().getProduct().equals(dto.getProduct()))
                        .forEach(wrapper -> {
                            if (overridesToDelete[0].isEmpty() && !dto.isFullyDeleted()) {
                                overridesToDelete[0] = getProductRateOffsetOverridesToDelete(getProductRateOffsetOverrides(), wrapper);
                                wrapper.setOriginalProductRateOffsetOverrides(overridesToDelete[0]);
                            }
                            setWrapperSaveDeleteFlags(dto, wrapper, finalOverrides);
                            if (dto.isFullyDeleted()) {
                                wrapper.getOriginalProductRateOffsetOverrides().addAll(finalOverrides);
                            }
                        });

            }
            productOverrides.addAll(cpbarDecisionUIWrapper.getCpOverrideWrappers());
        } else {
            setWrapperSaveDeleteFlags(dto, dto.getOverrideWrapper(), overrides);
            productOverrides.add(dto.getOverrideWrapper());
        }
    }

    public List<ProductRateOffsetOverride> createOverridesByDimensions(PricingAgileRatesOverrideDTO dto, List<AccomClass> roomClasses) {
        if (CollectionUtils.isEmpty(allDTARanges)) {
            allDTARanges = service.findAllDTARanges();
        }
        List<AgileRatesDTARange> productDTARanges = agileRatesConfigurationService.getAdjustedDTARanges(dto.getProduct(), allDTARanges);
        List<ProductRateOffsetOverride> productRateOffsetOverrides = new ArrayList<>();
        for (AgileRatesDTARange range : productDTARanges) {
            for (AccomClass roomClass : roomClasses) {
                productRateOffsetOverrides.add(createProductRateOffsetOverride(dto, roomClass, range));
            }
        }
        return productRateOffsetOverrides;
    }

    public List<ProductRateOffsetOverride> createOverrideByDimensionsProductGroups(PricingAgileRatesOverrideDTO dto, Product product, List<AccomClass> roomClasses) {
        roomClasses.removeIf(rc -> !findSortedRoomClassesForProduct(product).contains(rc)); // remove room classes that don't apply to the product
        if (CollectionUtils.isEmpty(allDTARanges)) {
            allDTARanges = service.findAllDTARanges();
        }
        List<AgileRatesDTARange> productDTARanges = agileRatesConfigurationService.getAdjustedDTARanges(dto.getProduct(), allDTARanges);
        List<ProductRateOffsetOverride> productRateOffsetOverrides = new ArrayList<>();
        for (AgileRatesDTARange range : productDTARanges) {
            for (AccomClass roomClass : roomClasses) {
                productRateOffsetOverrides.add(createProductRateOffsetOverride(dto, product, roomClass, range));
            }
        }
        return productRateOffsetOverrides;
    }

    public List<ProductRateOffsetOverride> createOverridesByDimensionsMultiday(PricingMultidayGridDTO dto, List<AccomClass> roomClassList, PricingMultiProductMultidayFilterDTO filterDTO) {
        if (CollectionUtils.isEmpty(allDTARanges)) {
            allDTARanges = service.findAllDTARanges();
        }
        List<AgileRatesDTARange> productDTARanges = agileRatesConfigurationService.getAdjustedDTARanges(dto.getProduct(), allDTARanges);
        List<ProductRateOffsetOverride> productRateOffsetOverrides = new ArrayList<>();
        for (AgileRatesDTARange range : productDTARanges) {
            for (AccomClass roomClass : roomClassList) {
                productRateOffsetOverrides.addAll(createProductRateOffsetOverrideMultiday(dto, roomClass, range, dto.getProduct(), filterDTO.getStartDate(), filterDTO.getEndDate(), filterDTO.getDaysOfWeek()));
            }
        }
        return productRateOffsetOverrides;
    }

    public List<ProductRateOffsetOverride> createOverridesByDimensionsMultiday(AgileRatesOffsetMethod offsetMethod, Product product, List<AccomClass> roomClassList, PricingMultiProductMultidayFilterDTO filterDTO) {
        if (CollectionUtils.isEmpty(allDTARanges)) {
            allDTARanges = service.findAllDTARanges();
        }
        List<AgileRatesDTARange> productDTARanges = agileRatesConfigurationService.getAdjustedDTARanges(product, allDTARanges);
        List<ProductRateOffsetOverride> productRateOffsetOverrides = new ArrayList<>();
        for (AgileRatesDTARange range : productDTARanges) {
            for (AccomClass roomClass : roomClassList) {
                productRateOffsetOverrides.addAll(createProductRateOffsetOverrideMultiday(offsetMethod, roomClass, range, product, filterDTO.getStartDate(), filterDTO.getEndDate(), filterDTO.getDaysOfWeek()));
            }
        }
        return productRateOffsetOverrides;
    }

    private ProductRateOffsetOverride createProductRateOffsetOverride(PricingAgileRatesOverrideDTO dto, AccomClass roomClass, AgileRatesDTARange range) {
        ProductRateOffsetOverride override = new ProductRateOffsetOverride();
        override.setProduct(dto.getProduct());
        override.setAccomClass(roomClass);
        override.setAgileRatesDTARange(range);
        override.setOccupancyDate(dto.getDate());
        override.setStatusId(Status.ACTIVE.getId());
        override.setOffsetMethod(dto.getOffsetMethod());
        return override;
    }

    private ProductRateOffsetOverride createProductRateOffsetOverride(PricingAgileRatesOverrideDTO dto, Product product, AccomClass roomClass, AgileRatesDTARange range) {
        ProductRateOffsetOverride override = new ProductRateOffsetOverride();
        override.setProduct(product);
        override.setAccomClass(roomClass);
        override.setAgileRatesDTARange(range);
        override.setOccupancyDate(dto.getDate());
        override.setStatusId(Status.ACTIVE.getId());
        override.setOffsetMethod(product.getOffsetMethod());
        return override;
    }

    private List<ProductRateOffsetOverride> createProductRateOffsetOverrideMultiday(PricingMultidayGridDTO dto, AccomClass roomClass, AgileRatesDTARange range, Product product, java.time.LocalDate startDate, java.time.LocalDate endDate, Set<DayOfWeek> daysOfWeek) {
        List<ProductRateOffsetOverride> resultList = new ArrayList<>();
        final List<Integer> dayOfWeekIntegerValues = daysOfWeek.stream().map(DayOfWeek::getCalendarDayOfWeek).collect(Collectors.toList());
        for (java.time.LocalDate date = startDate; date.isBefore(endDate.plusDays(1)); date = date.plusDays(1)) {
            if (dayOfWeekIntegerValues.contains(date.getDayOfWeek().getValue())) {
                ProductRateOffsetOverride override = new ProductRateOffsetOverride();
                override.setProduct(product);
                override.setAccomClass(roomClass);
                override.setAgileRatesDTARange(range);
                override.setOccupancyDate(new LocalDate(date.getYear(), date.getMonthValue(), date.getDayOfMonth()));
                override.setStatusId(Status.ACTIVE.getId());
                override.setOffsetMethod(dto.getAgileRatesOffsetMethod());
                resultList.add(override);
            }
        }
        return resultList;
    }

    private List<ProductRateOffsetOverride> createProductRateOffsetOverrideMultiday(AgileRatesOffsetMethod offsetMethod, AccomClass roomClass, AgileRatesDTARange range, Product product, java.time.LocalDate startDate, java.time.LocalDate endDate, Set<DayOfWeek> daysOfWeek) {
        List<ProductRateOffsetOverride> resultList = new ArrayList<>();
        final List<Integer> dayOfWeekIntegerValues = daysOfWeek.stream().map(DayOfWeek::getCalendarDayOfWeek).collect(Collectors.toList());
        for (java.time.LocalDate date = startDate; date.isBefore(endDate.plusDays(1)); date = date.plusDays(1)) {
            if (dayOfWeekIntegerValues.contains(date.getDayOfWeek().getValue())) {
                ProductRateOffsetOverride override = new ProductRateOffsetOverride();
                override.setProduct(product);
                override.setAccomClass(roomClass);
                override.setAgileRatesDTARange(range);
                override.setOccupancyDate(new LocalDate(date.getYear(), date.getMonthValue(), date.getDayOfMonth()));
                override.setStatusId(Status.ACTIVE.getId());
                override.setOffsetMethod(offsetMethod);
                resultList.add(override);
            }
        }
        return resultList;
    }

    private void setOverrideOffsetValues(AgileRatesOffsetFieldContainer dto, List<ProductRateOffsetOverride> overrides) {
        for (ProductRateOffsetOverride override : overrides) {
            if (override.getProduct().isOptimized()) {
                //set specific offset value to floor or ceiling value for Optimized products
                override.setOffsetValue(dto.getFloorOffset() != null ? dto.getFloorOffset() : dto.getCeilingOffset());
                override.setFloorValue(dto.getFloorOffset());
                override.setCeilingValue(dto.getCeilingOffset());
            } else {
                //assign specific offset value to floor and ceiling for non-Optimized products
                override.setOffsetValue(dto.getAdjustment());
                override.setFloorValue(dto.getAdjustment());
                override.setCeilingValue(dto.getAdjustment());
            }
        }
    }

    private void setOverrideOffsetValues(PricingMultidayGridDTO dto, List<ProductRateOffsetOverride> overrides) {
        overrides.forEach(override -> {
            if (override.getProduct().isOptimized()) {
                //set specific offset value to floor or ceiling value for Optimized products
                override.setOffsetValue(dto.getSpecificOverride() != null ? dto.getSpecificOverride() : (dto.getFloorOverride() != null ? dto.getFloorOverride() : dto.getCeilingOverride()));
                override.setFloorValue(dto.getFloorOverride());
                override.setCeilingValue(dto.getCeilingOverride());
            } else {
                //assign specific offset value to floor and ceiling for non-Optimized products
                override.setOffsetValue(dto.getSpecificOverride());
                override.setFloorValue(dto.getSpecificOverride());
                override.setCeilingValue(dto.getSpecificOverride());
            }
        });
    }

    private void setWrapperSaveDeleteFlagsWithProductGroups(PricingAgileRatesOverrideDTO dto, CPOverrideWrapper overrideWrapper, List<ProductRateOffsetOverride> overrides) {
        if (!dto.isFullyDeleted()) {
            overrideWrapper.setProductRateOffsetOverrides(overrides);
        }

        overrideWrapper.setFloorOverridePendingDelete(dto.isFloorMarkedForDeletion());
        overrideWrapper.setCeilingOverridePendingDelete(dto.isCeilingMarkedForDeletion());

        overrideWrapper.setIsPendingDelete(dto.isFullyDeleted());
        overrideWrapper.setIsPendingSave(!dto.isFullyDeleted());
    }

    private void setWrapperSaveDeleteFlags(PricingAgileRatesOverrideDTO dto, CPOverrideWrapper overrideWrapper, List<ProductRateOffsetOverride> overrides) {
        overrideWrapper.setProductRateOffsetOverrides(overrides);

        overrideWrapper.setFloorOverridePendingDelete(dto.isFloorMarkedForDeletion());
        overrideWrapper.setCeilingOverridePendingDelete(dto.isCeilingMarkedForDeletion());

        overrideWrapper.setIsPendingDelete(dto.isFullyDeleted());
        overrideWrapper.setIsPendingSave(!dto.isFullyDeleted());
    }

    public boolean isAgileRatesAndApplyToAllRoomClasses(Product product) {
        return isAgileRateOptimizedAndSameForAllRoomClasses(product) || isNonOptimizedAndApplyForAllRoomClasses(product);
    }

    public boolean isAgileRateOptimizedAndSameForAllRoomClasses(Product product) {
        return product.isOptimized()
                && !product.isIndependentProduct()
                && (OptimizationLevel.SAME_FOR_ALL_ROOM_CLASSES.equals(optimizationLevel)
                || getCpPricingFilter().isApplyOverridesToAllRoomClasses());
    }

    public boolean isOptimizedAndInProductGroup(List<Product> products) {
        if (allProductGroups.isEmpty()) {
            allProductGroups = agileRatesConfigurationService.getProductGroups();
        }
        return products
                .stream()
                .anyMatch(product -> product.isOptimized() && !agileRatesConfigurationService.getAllProductsInProductGroup(product, allProductGroups).isEmpty());
    }

    public List<ProductGroup> getAllProductGroups() {
        if (allProductGroups.isEmpty()) {
            allProductGroups = agileRatesConfigurationService.getProductGroups();
        }
        return allProductGroups;
    }

    public List<ProductGroup> findAllProductsInProductGroup(Product product) {
        return agileRatesConfigurationService.getAllProductsInProductGroup(product, getAllProductGroups());
    }

    public boolean isNonOptimizedAndApplyForAllRoomClasses(Product product) {
        return !product.isSystemDefaultOrIndependentProduct()
                && !product.isOptimized()
                && getCpPricingFilter().isApplyOverridesToAllRoomClasses();
    }

    public boolean isNonOptimizedProductAndSameForAllRoomClassesSelected() {
        return getCpPricingFilter().isNonOptimizedAgileRateProductSelected() && getCpPricingFilter().isApplyOverridesToAllRoomClasses();
    }

    public boolean isOptimizedProductAndSameForAllRoomClassesSelected() {
        return getCpPricingFilter().isOptimizedAgileRateProductSelected()
                && (getCpPricingFilter().isApplyOverridesToAllRoomClasses()
                || OptimizationLevel.SAME_FOR_ALL_ROOM_CLASSES.equals(optimizationLevel));
    }

    public List<AccomType> getSelectedRoomTypes() {
        return CollectionUtils.isEmpty(getCpPricingFilter().getSelectedRoomTypes()) ? new ArrayList<>(filterAndGetRoomTypesForRoomClass(getCpPricingFilter().getSelectedRoomClass())) : new ArrayList<>(getCpPricingFilter().getSelectedRoomTypes());
    }

    public void cleanMultidayOverrideWrappers(List<CPOverrideWrapper> wrappers) {
        wrappers.forEach(wrapper -> {
            wrapper.setSpecificOverride(null);
            wrapper.setFloorOverride(null);
            wrapper.setCeilingOverride(null);
        });
    }

    public List<CPOverrideWrapper> getMultidayOverrideWrappers(PricingMultidayGridDTO dto, PricingMultiProductMultidayFilterDTO filterDTO, Date caughtUpDate) {
        if (dto.isApplyToAllRoomTypes()) {
            if (getCpPricingFilter().getSelectedRoomClass().equals(allRoomClasses)) {
                List<CPOverrideWrapper> nonBaseRoomTypeWrappers = new ArrayList<>();
                List<CPOverrideWrapper> filteredWrappers = getCpOverrideWrapperStream(filterDTO, dto.getProduct())
                        .filter(overrideWrapper -> overrideWrapper.getAccomClass().equals(dto.getRoomClass()))
                        .collect(Collectors.toList());
                AccomClass masterAccomClass = getMasterAccomClass();
                PricingAccomClass masterPricingAccomClass = pricingAccomClasses.stream().filter(pac -> pac.getAccomClass().equals(masterAccomClass)).findFirst().orElse(null);
                filteredWrappers.forEach(wrapper -> nonBaseRoomTypeWrappers.addAll(overrideManager.getNonBaseRoomTypes(wrapper, masterPricingAccomClass, caughtUpDate)));
                List<CPOverrideWrapper> allWrappers = new ArrayList<>();
                allWrappers.addAll(nonBaseRoomTypeWrappers);
                allWrappers.addAll(filteredWrappers);
                return allWrappers;
            }
            List<CPOverrideWrapper> allWrappers = new ArrayList<>();
            List<CPOverrideWrapper> baseRTWrappers = getCpOverrideWrapperStream(filterDTO, dto.getProduct())
                    .filter(overrideWrapper -> overrideWrapper.getAccomClass().equals(dto.getRoomClass()))
                    .filter(overrideWrapper -> overrideWrapper.isBaseRoomType())
                    .collect(Collectors.toList());
            List<CPOverrideWrapper> nonBaseRTWrappers = getCpOverrideWrapperStream(filterDTO, dto.getProduct())
                    .filter(overrideWrapper -> overrideWrapper.getAccomClass().equals(dto.getRoomClass()))
                    .filter(overrideWrapper -> !overrideWrapper.isBaseRoomType())
                    .collect(Collectors.toList());

            allWrappers.addAll(nonBaseRTWrappers);
            allWrappers.addAll(baseRTWrappers);

            return allWrappers;
        }
        return getCpOverrideWrapperStream(filterDTO, dto.getProduct())
                .filter(overrideWrapper -> overrideWrapper.getSelectedRoomType().equals(dto.getRoomType()))
                .collect(Collectors.toList());
    }

    public List<CPOverrideWrapper> getMultidayOverrideWrappersForAgileRates(PricingMultidayGridDTO dto, PricingMultiProductMultidayFilterDTO filterDTO) {
        return getCpOverrideWrapperStream(filterDTO, dto.getProduct())
                .filter(overrideWrapper -> overrideWrapper.getSelectedRoomType().getAccomClass().equals(dto.getRoomClass()))
                .collect(Collectors.toList());
    }

    public Stream<CPOverrideWrapper> getCpOverrideWrapperStreamForDate(LocalDate date) {
        return results.stream()
                .filter(wrapper -> wrapper.getCpbarDecisionDTO().getDate().equals(date))
                .flatMap(wrapper -> wrapper.getCpOverrideWrappers().stream());
    }

    public Stream<CPOverrideWrapper> getCpOverrideWrapperStream(PricingMultiProductMultidayFilterDTO dto, Product selectedProduct) {
        return results.stream()
                .filter(wrapper -> !wrapper.getCpbarDecisionDTO().getDate().isBefore(new LocalDate(dto.getStartDate().toString())))
                .filter(wrapper -> !wrapper.getCpbarDecisionDTO().getDate().isAfter(new LocalDate(dto.getEndDate().toString())))
                .filter(wrapper -> dto.getDaysOfWeek().contains(DayOfWeek.valueOf(wrapper.getCpbarDecisionDTO().getDate().getDayOfWeek())))
                .flatMap(wrapper -> wrapper.getCpOverrideWrappers().stream())
                .filter(wrapper -> wrapper.getCpDecisionBAROutput().getProduct().equals(selectedProduct));
    }

    private Stream<CPOverrideWrapper> getOverrideWrapperStream(PricingMultiProductMultidayFilterDTO dto, List<Product> productsWithinProductGroup) {
        return results.stream()
                .filter(wrapper -> !wrapper.getCpbarDecisionDTO().getDate().isBefore(new LocalDate(dto.getStartDate().toString())))
                .filter(wrapper -> !wrapper.getCpbarDecisionDTO().getDate().isAfter(new LocalDate(dto.getEndDate().toString())))
                .filter(wrapper -> dto.getDaysOfWeek().contains(DayOfWeek.valueOf(wrapper.getCpbarDecisionDTO().getDate().getDayOfWeek())))
                .flatMap(wrapper -> wrapper.getCpOverrideWrappers().stream())
                .filter(wrapper -> productsWithinProductGroup.contains(wrapper.getCpDecisionBAROutput().getProduct()));
    }

    public void applyMultidayAgileRatesOverridesWithProductGroups(PricingMultidayGridDTO dto, PricingMultiProductMultidayFilterDTO filterDTO) {
        List<Product> productsWithSameParentGroup = findProductsWithSameAgileRatesProductGroup(dto.getProduct());
        List<CPOverrideWrapper> overrideWrappers = getOverrideWrapperStream(filterDTO, productsWithSameParentGroup).collect(Collectors.toList());
        Set<ProductRateOffsetOverride> allProductRateOffsetOverrides = new HashSet<>(getProductRateOffsetOverridesWithProductGroups());
        List<ProductRateOffsetOverride> allOverrides = new ArrayList<>();
        Set<AccomClass> accomClassesWithWrappers = overrideWrappers.stream().map(CPOverrideWrapper::getAccomClass).collect(Collectors.toSet());
        if (isAgileRatesAndApplyToAllRoomClasses(dto.getProduct())) {
            //Apply to all room classes flow
            productsWithSameParentGroup.forEach(product -> allOverrides
                    .addAll(createOverridesByDimensionsMultiday(product.getOffsetMethod(), product, findSortedRoomClassesForProduct(product), filterDTO)));
            List<CPOverrideWrapper> wrappersForProduct = overrideWrappers.stream().filter(o -> o.getProduct().equals(dto.getProduct())).collect(Collectors.toList());
            for (CPOverrideWrapper wrapper : wrappersForProduct) {
                List<ProductRateOffsetOverride> currentWrapperOverrides =
                        allOverrides.stream()
                                .filter(overrides -> overrides.getOccupancyDate().equals(wrapper.getCpDecisionBAROutput().getArrivalDate()))
                                .filter(overrides -> overrides.getAccomClass().equals(wrapper.getAccomClass())
                                        || !accomClassesWithWrappers.contains(overrides.getAccomClass()))
                                .collect(Collectors.toList());

                setOverrideOffsetValues(dto, currentWrapperOverrides);

                if (!currentWrapperOverrides.isEmpty()) {
                    wrapper.getOriginalProductRateOffsetOverrides().addAll(getProductRateOffsetOverridesToDelete(allProductRateOffsetOverrides,
                            productsWithSameParentGroup, wrapper.getCpDecisionBAROutput().getArrivalDate()));
                    wrapper.setProductRateOffsetOverrides(currentWrapperOverrides);
                    wrapper.setIsPendingSave(true);
                }

                productOverrides.add(wrapper);
            }
        } else {
            // Normal flow
            productsWithSameParentGroup.forEach(product -> allOverrides
                    .addAll(createOverridesByDimensionsMultiday(product.getOffsetMethod(), product, asList(dto.getRoomClass()), filterDTO)));
            setOverrideOffsetValues(dto, allOverrides);
            for (CPOverrideWrapper wrapper : overrideWrappers) {
                wrapper.setIsPendingSave(true);
                wrapper.setProductRateOffsetOverrides(allOverrides);
                List<ProductRateOffsetOverride> overridesToDelete = getProductRateOffsetOverridesToDelete(allProductRateOffsetOverrides,
                        productsWithSameParentGroup, wrapper.getCpDecisionBAROutput().getArrivalDate());
                overridesToDelete.removeIf(override -> !override.getAccomClass().equals(dto.getRoomClass()));
                wrapper.getOriginalProductRateOffsetOverrides().addAll(overridesToDelete);

                productOverrides.add(wrapper);
            }
        }
    }

    public void applyMultidayAgileRatesOverrides(PricingMultidayGridDTO dto, List<CPOverrideWrapper> overrideWrappers, PricingMultiProductMultidayFilterDTO filterDTO) {
        Set<AccomClass> accomClassesWithWrappers = overrideWrappers.stream().map(CPOverrideWrapper::getAccomClass).collect(Collectors.toSet());
        if (isOptimizedAndInProductGroup(asList(dto.getProduct()))) {
            applyMultidayAgileRatesOverridesWithProductGroups(dto, filterDTO);
        } else {
            if (isAgileRatesAndApplyToAllRoomClasses(dto.getProduct())) {
                //Apply to all room classes flow
                final List<AccomClass> allRoomClassesWithoutAll = findSortedRoomClassesForProduct(dto.getProduct());
                Set<ProductRateOffsetOverride> allProductRateOffsetOverrides = new HashSet<>(getProductRateOffsetOverrides());
                List<ProductRateOffsetOverride> allOverridesToSaveOrDelete = createOverridesByDimensionsMultiday(dto, allRoomClassesWithoutAll, filterDTO);
                for (CPOverrideWrapper wrapper : overrideWrappers) {
                    //Find all overrides associated to that wrapper and overrides that don't have a wrapper but have the same date
                    List<ProductRateOffsetOverride> currentWrapperOverrides =
                            allOverridesToSaveOrDelete.stream()
                                    .filter(overrides -> overrides.getOccupancyDate().equals(wrapper.getCpDecisionBAROutput().getArrivalDate()))
                                    .filter(overrides -> overrides.getAccomClass().equals(wrapper.getAccomClass())
                                            || !accomClassesWithWrappers.contains(overrides.getAccomClass()))
                                    .collect(Collectors.toList());
                    allOverridesToSaveOrDelete.removeAll(currentWrapperOverrides);

                    if (!currentWrapperOverrides.isEmpty()) {
                        setOverrideOffsetValues(dto, currentWrapperOverrides);
                        wrapper.getOriginalProductRateOffsetOverrides().addAll(getProductRateOffsetOverridesToDelete(allProductRateOffsetOverrides, wrapper));
                        wrapper.setProductRateOffsetOverrides(currentWrapperOverrides);
                        wrapper.setIsPendingSave(true);
                    }

                    productOverrides.add(wrapper);
                }
            } else {
                //Normal flow
                if (CollectionUtils.isEmpty(allDTARanges)) {
                    allDTARanges = service.findAllDTARanges();
                }
                for (CPOverrideWrapper wrapper : overrideWrappers) {
                    List<ProductRateOffsetOverride> wrapperOverrides = new ArrayList<>();

                    List<AgileRatesDTARange> productDTARanges = agileRatesConfigurationService.getAdjustedDTARanges(wrapper.getCpDecisionBAROutput().getProduct(), allDTARanges);
                    for (AgileRatesDTARange range : productDTARanges) {
                        ProductRateOffsetOverride override = new ProductRateOffsetOverride();
                        override.setProduct(wrapper.getCpDecisionBAROutput().getProduct());
                        override.setAccomClass(dto.getRoomClass());
                        override.setAgileRatesDTARange(range);
                        override.setOccupancyDate(wrapper.getCpDecisionBAROutput().getArrivalDate());
                        override.setStatusId(Status.ACTIVE.getId());
                        override.setOffsetMethod(dto.getAgileRatesOffsetMethod());
                        wrapperOverrides.add(override);
                    }

                    setOverrideOffsetValues(dto, wrapperOverrides);

                    wrapper.setIsPendingSave(true);
                    wrapper.setProductRateOffsetOverrides(wrapperOverrides);
                    productOverrides.add(wrapper);
                }
            }
        }
    }

    public List<ProductRateOffsetOverride> getProductRateOffsetOverridesToDelete(Set<ProductRateOffsetOverride> allActiveOverrides, CPOverrideWrapper wrapper) {
        return allActiveOverrides.stream()
                .filter(override -> override.getOccupancyDate().equals(wrapper.getCpDecisionBAROutput().getArrivalDate()))
                .filter(override -> override.getProduct().equals(wrapper.getProduct()))
                .collect(Collectors.toList());
    }

    public List<ProductRateOffsetOverride> getProductRateOffsetOverridesToDelete(Set<ProductRateOffsetOverride> allActiveOverrides, List<Product> products, LocalDate arrivalDate) {
        return allActiveOverrides.stream()
                .filter(override -> override.getOccupancyDate().equals(arrivalDate))
                .filter(override -> products.contains(override.getProduct()))
                .collect(Collectors.toList());
    }

    @VisibleForTesting
    public LocalDate getStartDateFromOverrideWrappers(List<CPOverrideWrapper> overrideWrappers) {
        return overrideWrappers.stream()
                .map(wrapper -> wrapper.getCpDecisionBAROutput().getArrivalDate())
                .min(LocalDate::compareTo)
                .orElse(null);
    }

    @VisibleForTesting
    public LocalDate getEndDateFromOverrideWrappers(List<CPOverrideWrapper> wrappers) {
        return wrappers.stream()
                .map(wrapper -> wrapper.getCpDecisionBAROutput().getArrivalDate())
                .max(LocalDate::compareTo)
                .orElse(null);
    }

    public void applyMultidayCPOverrides(PricingMultidayGridDTO dto, List<CPOverrideWrapper> overrideWrappers, CPDecisionContext cpDecisionContext) {
        if (CollectionUtils.isNotEmpty(overrideWrappers)) {
            PricingAccomClass masterPricingAccomClass = getMasterPricingAccomClassFromCPDecisionContext(cpDecisionContext);
            overrideWrappers
                    .forEach(wrapper -> {
                        if (wrapper.isBaseRoomType() && dto.isApplyToAllRoomTypes() && !isRoomClassPriceExcluded(wrapper.getAccomClass())) {
                            wrapper.setApplyOverrideAcrossRoomTypes(true);
                        }

                        if (dto.getSpecificOverride() != null) {
                            wrapper.setSpecificOverride(dto.getSpecificOverride());
                        }

                        if (dto.getFloorOverride() != null) {
                            wrapper.setFloorOverride(dto.getFloorOverride());
                        }

                        if (dto.getCeilingOverride() != null) {
                            wrapper.setCeilingOverride(dto.getCeilingOverride());
                        }

                        wrapper.setStartDate(wrapper.getCpDecisionBAROutput().getArrivalDate());
                        wrapper.setEndDate(wrapper.getCpDecisionBAROutput().getArrivalDate());

                        applyOverride(wrapper, false, cpDecisionContext, masterPricingAccomClass);
                    });
        }
    }

    public void removeMultidayOverrideWithAgileRatesPopupInProductGroup(CPOverrideWrapper cpOverrideWrapper, Set<ProductRateOffsetOverride> allOverrides,
                                                                        List<Product> productsInProductGroup, boolean refreshData) {
        if (isNotEmpty(cpOverrideWrapper.getProductRateOffsetOverrides())) {
            LocalDate arrivalDate = cpOverrideWrapper.getCpDecisionBAROutput().getArrivalDate();
            if (isAgileRatesAndApplyToAllRoomClasses(cpOverrideWrapper.getCpDecisionBAROutput().getProduct())) {
                //On screen cards delete all
                List<ProductRateOffsetOverride> allOffsetsToRemove = allOverrides
                        .stream()
                        .filter(proo -> arrivalDate.equals(proo.getOccupancyDate()))
                        .collect(Collectors.toList());
                List<ProductRateOffsetOverride> coveredRemovedOffsets = new ArrayList<>();
                getOverrideWrappersForDate(arrivalDate, cpOverrideWrapper.getCpDecisionBAROutput().getProduct())
                        .forEach(wrapper -> {
                            if (wrapper.hasOriginalAgileRatesOverride()) {
                                productOverrides.add(wrapper);
                                coveredRemovedOffsets.addAll(wrapper.getProductRateOffsetOverrides());
                            } else {
                                productOverrides.remove(wrapper);
                                if (coveredRemovedOffsets.contains(wrapper.getProductRateOffsetOverrides())) {
                                    coveredRemovedOffsets.removeAll(wrapper.getProductRateOffsetOverrides());
                                }
                                wrapper.setProductRateOffsetOverrides(wrapper.getOriginalProductRateOffsetOverrides());
                            }
                            wrapper.setIsPendingDelete(wrapper.hasOriginalAgileRatesOverride());
                            wrapper.setIsPendingSave(false);
                        });
                allOffsetsToRemove.removeAll(coveredRemovedOffsets);
                //Add leftover needed to delete
                getOverrideWrappersForDate(arrivalDate, cpOverrideWrapper.getCpDecisionBAROutput().getProduct())
                        .filter(CPOverrideWrapper::isPendingDelete)
                        .findFirst().ifPresent(wrapperToRemoveProductRateOffsets -> wrapperToRemoveProductRateOffsets.getProductRateOffsetOverrides().addAll(allOffsetsToRemove));
            } else {
                //Normal Flow
                getOverrideWrappersForDateAndAccomClass(arrivalDate,
                        cpOverrideWrapper.getAccomClass(),
                        cpOverrideWrapper.getCpDecisionBAROutput().getProduct())
                        .forEach(wrapper -> {
                            List<ProductRateOffsetOverride> otherOverrides = allOverrides.stream()
                                    .filter(o -> o.getOccupancyDate().equals(wrapper.getCpDecisionBAROutput().getArrivalDate()))
                                    .filter(o -> productsInProductGroup.contains(o.getProduct()) && !o.getProduct().equals(wrapper.getProduct()))
                                    .filter(o -> o.getAccomClass().equals(wrapper.getAccomClass()))
                                    .collect(Collectors.toList());
                            wrapper.setProductRateOffsetOverrides(wrapper.getOriginalProductRateOffsetOverrides());
                            wrapper.getProductRateOffsetOverrides().addAll(otherOverrides);
                            wrapper.setIsPendingDelete(wrapper.hasOriginalAgileRatesOverride());
                            wrapper.setIsPendingSave(false);
                        });
                if (cpOverrideWrapper.hasOriginalAgileRatesOverride()) {
                    productOverrides.add(cpOverrideWrapper);
                } else {
                    cpOverrideWrapper.setProductRateOffsetOverrides(cpOverrideWrapper.getOriginalProductRateOffsetOverrides());
                    productOverrides.remove(cpOverrideWrapper);
                }
                cpOverrideWrapper.setIsPendingDelete(cpOverrideWrapper.hasOriginalAgileRatesOverride());
                cpOverrideWrapper.setIsPendingSave(false);
            }
        }

        if (refreshData) {
            refreshView();
        }
    }

    public void removeMultidayOverrideWithProductGroups(PricingMultidayGridDTO dto, PricingMultiProductMultidayFilterDTO filterDTO) {
        List<Product> productsInSameProductGroup = findProductsWithSameAgileRatesProductGroup(dto.getProduct());
        List<CPOverrideWrapper> wrappers = getOverrideWrapperStream(filterDTO, productsInSameProductGroup).collect(Collectors.toList());
        if (!isAgileRatesAndApplyToAllRoomClasses(dto.getProduct())) {
            wrappers.removeIf(overrideWrapper -> !overrideWrapper.getSelectedRoomType().getAccomClass().equals(dto.getRoomClass()));
        }
        Set<ProductRateOffsetOverride> allProductRateOffsetOverrides = new HashSet<>(getProductRateOffsetOverridesWithProductGroups());

        wrappers.forEach(wrapper -> {
            removeMultidayOverrideWithAgileRatesPopupInProductGroup(wrapper, allProductRateOffsetOverrides, productsInSameProductGroup, false);
        });
    }

    public void removeMultidayOverride(PricingMultidayGridDTO dto, PricingMultiProductMultidayFilterDTO filterDTO, CPDecisionContext cpDecisionContext, PricingAccomClass masterPricingAccomClass) {
        if (filterDTO.getDaysOfWeek().isEmpty()) {
            filterDTO.setDaysOfWeek(new HashSet<>(asList(DayOfWeek.values())));
        }
        if (isOptimizedAndInProductGroup(asList(dto.getProduct()))) {
            removeMultidayOverrideWithProductGroups(dto, filterDTO);
        } else {
            removeMultidayOverrides(dto, filterDTO, cpDecisionContext, masterPricingAccomClass);
        }
        enableSaveCancelButtons(true);
        refreshView();
    }

    public void removeMultidayOverrides(PricingMultidayGridDTO dto, PricingMultiProductMultidayFilterDTO filterDTO,
                                        CPDecisionContext cpDecisionContext, PricingAccomClass masterPricingAccomClass) {
        List<CPOverrideWrapper> wrappers = new ArrayList<>();
        if (isAgileRatesProduct(dto.getProduct())) {
            if (isAgileRatesAndApplyToAllRoomClasses(dto.getProduct())) {
                //Grab all wrappers and ensure multiday will grab Product Rate Offsets only for the specified wrapper dates.
                wrappers = getCpOverrideWrapperStream(filterDTO, dto.getProduct()).filter(overrideWrapperItem -> isDateAfter(filterDTO.getStartDate(), overrideWrapperItem.getCpDecisionBAROutput().getArrivalDate()) ||
                        isDateEqual(filterDTO.getStartDate(), overrideWrapperItem.getCpDecisionBAROutput().getArrivalDate()) ||
                        isDateBefore(filterDTO.getEndDate(), overrideWrapperItem.getCpDecisionBAROutput().getArrivalDate()) ||
                        isDateEqual(filterDTO.getEndDate(), overrideWrapperItem.getCpDecisionBAROutput().getArrivalDate())).collect(Collectors.toList());
            } else {
                wrappers = getMultidayOverrideWrappersForAgileRates(dto, filterDTO);
            }
        } else {
            wrappers = getMultidayOverrideWrappers(dto, filterDTO, dateService.getCaughtUpDate());
        }

        wrappers.forEach(wrapper -> {
            if (dto.getProduct().isSystemDefaultOrIndependentProduct() &&
                    wrapper.isBaseRoomType() &&
                    dto.isRemoveSpecific()) {
                wrapper.setApplyOverrideAcrossRoomTypes(true);
            }

            if (isAgileRatesProduct(dto.getProduct())) {
                removeMultiDayOverrideFromAgileRatesPopUp(wrapper, false);
            } else {
                applyRemove(wrapper, true, cpDecisionContext, masterPricingAccomClass, false);
            }
        });
    }

    public boolean isDateEqual(java.time.LocalDate dtoDate, LocalDate date) {
        java.time.LocalDate localDate = getJavaTimeLocalDateFromJodaTime(date);
        return localDate.isEqual(dtoDate);
    }

    public boolean isDateAfter(java.time.LocalDate dtoDate, LocalDate date) {
        java.time.LocalDate localDate = getJavaTimeLocalDateFromJodaTime(date);
        return localDate.isAfter(dtoDate);
    }

    public boolean isDateBefore(java.time.LocalDate dtoDate, LocalDate date) {
        java.time.LocalDate localDate = getJavaTimeLocalDateFromJodaTime(date);
        return localDate.isBefore(dtoDate);
    }

    public java.time.LocalDate getJavaTimeLocalDateFromJodaTime(LocalDate date) {
        final int day = date.getDayOfMonth();
        final int month = date.getMonthOfYear();
        final int year = date.getYear();
        return java.time.LocalDate.of(year, month, day);
    }

    public void applyMultidayOverrideChanges(List<PricingMultidayGridDTO> dtoList, PricingMultiProductMultidayFilterDTO filterDTO) {
        if (filterDTO.getDaysOfWeek().isEmpty()) {
            filterDTO.setDaysOfWeek(new HashSet<>(asList(DayOfWeek.values())));
        }

        CPDecisionContext cpDecisionContextRemove = getCpDecisionContext(DateUtil.convertLocalDateToJodaLocalDate(filterDTO.getStartDate()), DateUtil.convertLocalDateToJodaLocalDate(filterDTO.getEndDate()), false);
        CPDecisionContext cpDecisionContextApply = getCpDecisionContext(DateUtil.convertLocalDateToJodaLocalDate(filterDTO.getStartDate()), DateUtil.convertLocalDateToJodaLocalDate(filterDTO.getEndDate()), true);

        Date caughtUpDate = dateService.getCaughtUpDate();
        List<PricingMultidayGridDTO> filteredDtos = new ArrayList<>();
        // if apply to all filter out excess dtos with the same product to prevent duplicate overrides from being made
        for (PricingMultidayGridDTO dto : dtoList) {
            if (isAgileRatesAndApplyToAllRoomClasses(dto.getProduct()) && filteredDtos.stream().noneMatch(item -> item.getProduct().equals(dto.getProduct()))) {
                filteredDtos.add(dto);
            }
        }
        dtoList.removeIf(dto -> isAgileRatesAndApplyToAllRoomClasses(dto.getProduct()));
        dtoList.addAll(filteredDtos);
        for (PricingMultidayGridDTO dto : dtoList) {
            if (isAgileRatesProduct(dto.getProduct())) {
                //Agile Rates Flow
                applyAgileRatesProductFromMultiday(filterDTO, dto);
            } else {
                //BAR Product Flow
                PricingAccomClass masterPricingAccomClass = getMasterPricingAccomClassFromCPDecisionContext(cpDecisionContextRemove);
                applyMultidayBARDTOs(filterDTO, cpDecisionContextRemove, cpDecisionContextApply, masterPricingAccomClass, caughtUpDate, dto);
            }
        }

        enableSaveCancelButtons(hasPendingChanges());
        if (hasPendingChanges()) {
            refreshView();
        }
    }

    public void applyMultiDayInventoryLimitOverrideChanges(PricingMultidayGridDTO pricingMultidayGridDTO, PricingMultiProductMultidayFilterDTO filterDTO) {
        Product groupProduct = filterDTO.getProducts().stream().filter(Product::isGroupProduct).collect(Collectors.toList()).get(0);
        List<CPOverrideWrapper> overrideWrappers = getCpOverrideWrapperStream(filterDTO, groupProduct).collect(Collectors.toList());
        for (CPOverrideWrapper wrapper : overrideWrappers) {
            wrapper.setInventoryLimitMarkedForDeletion(pricingMultidayGridDTO.isRemoveInventoryLimit());
            wrapper.setInventoryLimitOverride(pricingMultidayGridDTO.getInventoryLimit());
            wrapper.setInventoryLimitOverrideOccupancyDt(wrapper.getCpDecisionBAROutput().getArrivalDate().toDate());
            InventoryLimitDecision inventoryLimitDecision = inventoryLimits.stream().filter(ild -> ild.getOccupancyDate().equals(wrapper.getCpDecisionBAROutput().getArrivalDate().toDate())).findFirst().orElse(null);
            wrapper.setInventoryLimit(inventoryLimitDecision == null ? null : inventoryLimitDecision.getInventoryLimit());
            InventoryLimitDecision inventoryLimitOverrideDecision = overriddenInventoryLimits.stream().filter(ilod -> ilod.getOccupancyDate().equals(wrapper.getCpDecisionBAROutput().getArrivalDate().toDate())).findFirst().orElse(null);
            wrapper.setOriginalInventoryLimitOverride(inventoryLimitOverrideDecision == null ? null : inventoryLimitOverrideDecision.getInventoryLimit());
            filterInventoryLimitOverrides(wrapper.getCpDecisionBAROutput().getArrivalDate());
            inventoryLimitOverrides.add(wrapper);
        }
        inventoryLimitOverrides.removeIf(markedToBeRemoved ->
                markedToBeRemoved.isInventoryLimitMarkedForDeletion() && markedToBeRemoved.getOriginalInventoryLimitOverride() == null
        );
        enableSaveCancelButtons(hasPendingChanges());
        if (hasPendingChanges()) {
            refreshView();
        }
    }
    public void applyMultidayBARDTOs(PricingMultiProductMultidayFilterDTO filterDTO, CPDecisionContext cpDecisionContextRemove, CPDecisionContext cpDecisionContextApply, PricingAccomClass masterPricingAccomClass, Date caughtUpDate, PricingMultidayGridDTO dto) {
        List<CPOverrideWrapper> wrappers = getMultidayOverrideWrappers(dto, filterDTO, caughtUpDate);

        //Remove Overrides if declared
        removeMultidayOverridesByType(dto, wrappers, cpDecisionContextRemove, cpDecisionContextApply, masterPricingAccomClass);

        //Apply Overrides
        if (dto.getSpecificOverride() != null || dto.getFloorOverride() != null || dto.getCeilingOverride() != null) {
            applyMultidayCPOverrides(dto, wrappers, cpDecisionContextApply);
        }
        List<CPOverrideWrapper> resultsWrapper = getResults().stream()
                .flatMap(item -> item.getCpOverrideWrappers().stream())
                .filter(item -> item.getCpDecisionBAROutput().getProduct().equals(dto.getProduct()))
                .filter(item -> item.getAccomType().equals(dto.getRoomType()))
                .filter(item -> !DateUtil.convertLocalDateToJodaLocalDate(filterDTO.getStartDate()).isAfter(item.getCpDecisionBAROutput().getArrivalDate()))
                .filter(item -> !DateUtil.convertLocalDateToJodaLocalDate(filterDTO.getEndDate()).isBefore(item.getCpDecisionBAROutput().getArrivalDate()))
                .collect(Collectors.toList());
        appliedMultiProductBARDTOs.removeAll(resultsWrapper);
        appliedMultiProductBARDTOs.addAll(resultsWrapper);
    }

    public void applyAgileRatesProductFromMultiday(PricingMultiProductMultidayFilterDTO filterDTO, PricingMultidayGridDTO dto) {
        if (dto.hasValidOverrideValue()) {
            if (isAgileRatesAndApplyToAllRoomClasses(dto.getProduct())) {
                applyMultidayAgileRatesOverrides(dto, getCpOverrideWrapperStream(filterDTO, dto.getProduct()).collect(Collectors.toList()), filterDTO);
            } else {
                applyMultidayAgileRatesOverrides(dto, getMultidayOverrideWrappersForAgileRates(dto, filterDTO), filterDTO);
            }
        }
    }

    public void removeLv0OverridesFromWrappers(LocalDate date, AccomClass selectedAccomClass) {
        getCpOverrideWrapperStreamForDate(date)
                .filter(wrapper -> selectedAccomClass.getId().equals(ALL_ROOM_CLASS_ID) || wrapper.getAccomClass().equals(selectedAccomClass))
                .forEach(cpOverrideWrapper -> {
                    cpOverrideWrapper.setStartDate(cpOverrideWrapper.getCpDecisionBAROutput().getArrivalDate());
                    cpOverrideWrapper.setIsPendingSave(true);
                    cpOverrideWrapper.setRemoveAllLv0Override(true);
                });
    }

    public void addMultiDayLv0OverridesToWrappers(List<CloseLV0RowModel> multiDayCloseLv0RowModels, LocalDate date) {
        getCpOverrideWrapperStreamForDate(date).forEach(cpOverrideWrapper -> {
            cpOverrideWrapper.getCloseLv0RowModels().forEach(closeLV0RowModel -> addMultiDayOverrideValues(closeLV0RowModel, multiDayCloseLv0RowModels));
            cpOverrideWrapper.setStartDate(cpOverrideWrapper.getCpDecisionBAROutput().getArrivalDate());
            if (isAccomTypeSelected(multiDayCloseLv0RowModels, cpOverrideWrapper)) {
                cpOverrideWrapper.setIsPendingSave(true);
            }
        });
    }

    private void addMultiDayOverrideValues(CloseLV0RowModel closeLV0RowModel, List<CloseLV0RowModel> multiDayCloseLv0RowModels) {
        multiDayCloseLv0RowModels.forEach(rowModel -> {
            if (closeLV0RowModel.getRowHeader().equals(rowModel.getRowHeader())) {
                rowModel.getAccomTypesSelected().forEach(accomType -> closeLV0RowModel.setCheckBoxValue(accomType, true));
            }
        });
    }

    public void addSingleDayLv0OverridesToWrappers(List<CloseLV0RowModel> closeLv0RowModels, List<CloseLV0RowModel> sourceModels, LocalDate date) {
        getCpOverrideWrapperStreamForDate(date).forEach(cpOverrideWrapper -> {
            cpOverrideWrapper.setStartDate(cpOverrideWrapper.getCpDecisionBAROutput().getArrivalDate());
            cpOverrideWrapper.setEndDate(cpOverrideWrapper.getCpDecisionBAROutput().getArrivalDate());
            if (isAccomTypeModified(closeLv0RowModels, cpOverrideWrapper.getAccomType(), sourceModels)) {
                cpOverrideWrapper.setIsPendingSave(true);
            }
        });
    }

    private boolean isAccomTypeModified(List<CloseLV0RowModel> closeLv0RowModels, AccomType accomType, List<CloseLV0RowModel> sourceModels) {
        return closeLv0RowModels.stream().anyMatch(rowModel -> isAccomTypeModifiedForRowModel(accomType, sourceModels, rowModel));
    }

    private boolean isAccomTypeModifiedForRowModel(AccomType accomType, List<CloseLV0RowModel> sourceModels, CloseLV0RowModel rowModel) {
        return sourceModels.stream().anyMatch(sourceRowModel -> isSameRowHeader(rowModel, sourceRowModel) && !bothModelsContainAccomType(accomType, rowModel, sourceRowModel));
    }

    private boolean bothModelsContainAccomType(AccomType accomType, CloseLV0RowModel rowModel, CloseLV0RowModel sourceRowModel) {
        return rowModel.getAccomTypesSelected().contains(accomType) == sourceRowModel.getAccomTypesSelected().contains(accomType);
    }

    private boolean isSameRowHeader(CloseLV0RowModel rowModel, CloseLV0RowModel sourceRowModel) {
        return sourceRowModel.getRowHeader().equals(rowModel.getRowHeader());
    }

    private boolean isAccomTypeSelected(List<CloseLV0RowModel> closeLV0RowModels, CPOverrideWrapper cpOverrideWrapper) {
        return closeLV0RowModels.stream().anyMatch(closeLV0RowModel -> closeLV0RowModel.getAccomTypesSelected().contains(cpOverrideWrapper.getAccomType()));
    }

    private void removeMultidayOverridesByType(PricingMultidayGridDTO dto, List<CPOverrideWrapper> wrappers, CPDecisionContext cpDecisionContextRemove,
                                               CPDecisionContext cpDecisionContextApply, PricingAccomClass masterPricingAccomClass) {
        if (dto.isRemoveGroupFloor()) {
            wrappers.stream()
                    .filter(wrapper -> DecisionOverrideType.GPFLOOR.equals(wrapper.getOriginalOverrideType()) || DecisionOverrideType.NONE.equals(wrapper.getOriginalOverrideType()))
                    .forEach(wrapper -> applyRemoveMultidayOverrides(dto, wrapper, cpDecisionContextRemove, masterPricingAccomClass));
            if (dto.isRemoveCeiling()) {
                wrappers.stream()
                        .filter(wrapper -> DecisionOverrideType.GPFLOORANDCEIL.equals(wrapper.getOriginalOverrideType()) || DecisionOverrideType.NONE.equals(wrapper.getOriginalOverrideType()))
                        .forEach(wrapper -> applyRemoveMultidayOverrides(dto, wrapper, cpDecisionContextRemove, masterPricingAccomClass));
            } else {
                wrappers.stream()
                        .filter(wrapper -> DecisionOverrideType.GPFLOORANDCEIL.equals(wrapper.getOriginalOverrideType()))
                        .forEach(wrapper -> {
                            wrapper.setGroupFloorOverride(null);
                            wrapper.setStartDate(wrapper.getCpDecisionBAROutput().getArrivalDate());
                            wrapper.setEndDate(wrapper.getCpDecisionBAROutput().getArrivalDate());
                            applyOverride(wrapper, false, cpDecisionContextApply, masterPricingAccomClass);
                        });
            }
        }
        if (dto.isRemoveSpecific()) {
            wrappers.stream()
                    .filter(wrapper -> DecisionOverrideType.USER.equals(wrapper.getOriginalOverrideType()) || DecisionOverrideType.NONE.equals(wrapper.getOriginalOverrideType()))
                    .forEach(wrapper -> applyRemoveMultidayOverrides(dto, wrapper, cpDecisionContextRemove, masterPricingAccomClass));
        }
        if (dto.isRemoveFloor()) {
            wrappers.stream()
                    .filter(wrapper -> DecisionOverrideType.FLOOR.equals(wrapper.getOriginalOverrideType()) || DecisionOverrideType.NONE.equals(wrapper.getOriginalOverrideType()))
                    .forEach(wrapper -> applyRemoveMultidayOverrides(dto, wrapper, cpDecisionContextRemove, masterPricingAccomClass));
            if (dto.isRemoveCeiling()) {
                wrappers.stream()
                        .filter(wrapper -> DecisionOverrideType.FLOORANDCEIL.equals(wrapper.getOriginalOverrideType()) || DecisionOverrideType.NONE.equals(wrapper.getOriginalOverrideType()))
                        .forEach(wrapper -> applyRemoveMultidayOverrides(dto, wrapper, cpDecisionContextRemove, masterPricingAccomClass));
            } else {
                wrappers.stream()
                        .filter(wrapper -> DecisionOverrideType.FLOORANDCEIL.equals(wrapper.getOriginalOverrideType()))
                        .forEach(wrapper -> {
                            wrapper.setFloorOverride(null);
                            wrapper.setStartDate(wrapper.getCpDecisionBAROutput().getArrivalDate());
                            wrapper.setEndDate(wrapper.getCpDecisionBAROutput().getArrivalDate());
                            applyOverride(wrapper, false, cpDecisionContextApply, masterPricingAccomClass);
                        });
            }
        }
        if (dto.isRemoveCeiling()) {
            wrappers.stream()
                    .filter(wrapper -> DecisionOverrideType.CEIL.equals(wrapper.getOriginalOverrideType()) || DecisionOverrideType.NONE.equals(wrapper.getOriginalOverrideType()))
                    .forEach(wrapper -> applyRemoveMultidayOverrides(dto, wrapper, cpDecisionContextRemove, masterPricingAccomClass));
            if (!dto.isRemoveFloor()) {
                wrappers.stream()
                        .filter(wrapper -> DecisionOverrideType.FLOORANDCEIL.equals(wrapper.getOriginalOverrideType()))
                        .forEach(wrapper -> {
                            wrapper.setCeilingOverride(null);
                            wrapper.setStartDate(wrapper.getCpDecisionBAROutput().getArrivalDate());
                            wrapper.setEndDate(wrapper.getCpDecisionBAROutput().getArrivalDate());
                            applyOverride(wrapper, false, cpDecisionContextApply, masterPricingAccomClass);
                        });
            }
            if (!dto.isRemoveGroupFloor()) {
                wrappers.stream()
                        .filter(wrapper -> DecisionOverrideType.GPFLOORANDCEIL.equals(wrapper.getOriginalOverrideType()))
                        .forEach(wrapper -> {
                            wrapper.setCeilingOverride(null);
                            wrapper.setStartDate(wrapper.getCpDecisionBAROutput().getArrivalDate());
                            wrapper.setEndDate(wrapper.getCpDecisionBAROutput().getArrivalDate());
                            applyOverride(wrapper, false, cpDecisionContextApply, masterPricingAccomClass);
                        });
            }
        }
    }

    private void applyRemoveMultidayOverrides(PricingMultidayGridDTO dto, CPOverrideWrapper wrapper, CPDecisionContext cpDecisionContext, PricingAccomClass masterPricingAccomClass) {
        if (wrapper.isBaseRoomType() && dto.isApplyToAllRoomTypes()) {
            wrapper.setApplyOverrideAcrossRoomTypes(true);
        }
        wrapper.setSpecificOverride(null);
        wrapper.setGroupFloorOverride(null);
        wrapper.setFloorOverride(null);
        wrapper.setCeilingOverride(null);
        applyRemove(wrapper, false, cpDecisionContext, masterPricingAccomClass, false);
    }

    private void enableSaveCancelButtons(boolean enableButtons) {
        if (enableButtons && isGroupFloorEnabled() && !showAgileRatesOverridePopup()) {
            boolean enableSaveButton = overrideManager.getPendingBAROverrides().values().stream()
                    .anyMatch(changedWrapper -> (changedWrapper.isPendingSave() && !(changedWrapper.getOriginalOverrideType().equals(DecisionOverrideType.GPFLOORANDCEIL) && changedWrapper.getCpDecisionBAROutput().getOverrideType().equals(DecisionOverrideType.CEIL))) ||
                            (changedWrapper.isPendingDelete() && !changedWrapper.getOriginalOverrideType().equals(DecisionOverrideType.GPFLOOR)))
                    || CollectionUtils.isNotEmpty(getPendingCloseLv0BarOverrides());
            view.enableButtons(true, enableSaveButton);
        } else {
            view.enableButtons(enableButtons, enableButtons);
        }
    }

    public boolean isSpecificRoomClassSelected() {
        return overrideManager.isSpecificRoomClassSelected();
    }

    public boolean displayCompetitorButton() {
        return isWebRateShoppingEnabled() && (isOnlyBARProductSelected() || isOnlyIndependentProductSelected());
    }

    public boolean displayCompetitorButtonForRdl(Integer productId) {
        return productId != null && (productId.equals(PRIMARY_PRODUCT_ID) || webrateTypeProducts.stream().anyMatch(p -> p.getProduct().getId().equals(productId)));
    }

    public boolean isAvailableCapacityToSellSelected() {
        return AdditionalInformationFilterEnum.AVAILABLE_CAPACITY.equals(getCpPricingFilter().getAdditionalInformationFilterEnum1()) ||
                AdditionalInformationFilterEnum.AVAILABLE_CAPACITY.equals(getCpPricingFilter().getAdditionalInformationFilterEnum2()) ||
                AdditionalInformationFilterEnum.AVAILABLE_CAPACITY.equals(getCpPricingFilter().getAdditionalInformationFilterEnum3()) ||
                AdditionalInformationFilterEnum.AVAILABLE_CAPACITY.equals(getCpPricingFilter().getAdditionalInformationFilterEnum4());
    }

    public boolean isMultidayOverrideValuesPrettyPriced(PricingMultidayGridDTO dto) {
        boolean overrideValid = true;

        if (cpDecisionContext == null || (!DateUtil.isDateBetween(cpDecisionContext.getStartDate().toDate(),
                cpDecisionContext.getEndDate().toDate(), getCpPricingFilter().getStartDate().toDate())) || (!DateUtil.isDateBetween(cpDecisionContext.getStartDate().toDate(),
                cpDecisionContext.getEndDate().toDate(), getCpPricingFilter().getEndDate().toDate()))) {
            cpDecisionContext = pricingConfigurationService.getCPDecisionContext(getCpPricingFilter().getStartDate(), getCpPricingFilter().getEndDate(), true);
        }

        Integer primaryProductId = cpDecisionContext.getPrimaryProduct(dto.getProduct()).getId();
        if (dto.getSpecificOverride() != null) {
            overrideValid = prettyPricingService.doesNumberMeetPricingRuleForProduct(dto.getSpecificOverride(), primaryProductId);
        }

        if (overrideValid && dto.getCeilingOverride() != null) {
            overrideValid = prettyPricingService.doesNumberMeetPricingRuleForProduct(dto.getCeilingOverride(), primaryProductId);
        }

        if (overrideValid && dto.getFloorOverride() != null) {
            overrideValid = prettyPricingService.doesNumberMeetPricingRuleForProduct(dto.getFloorOverride(), primaryProductId);
        }

        return overrideValid;
    }

    public Map<Integer, PricingRule> getPricingRules() {
        return prettyPricingService.getPricingRules();
    }

    public PricingRule getPricingRule(Integer productId) {
        return prettyPricingService.getPricingRule(productId);
    }


    public boolean isMultidayOverrideBetweenCeilingAndFloor(PricingMultidayGridDTO dto, PricingMultiProductMultidayFilterDTO filterDTO, List<CPUnqualifedDemandForecastPrice> demandForecastBetweenDatesForAccomClass, Map<LocalDate, Tax> taxesByDate, Map<AccomTypeSupplementValuePK, AccomTypeSupplementValue> supplement, Map<CPConfigMergedOffsetPK, CPConfigMergedOffset> offsetsForDates) {
        BigDecimal upperBoundOverride, lowerBoundOverride;
        if (CollectionUtils.isEmpty(filterDTO.getDaysOfWeek())) {
            filterDTO.setDaysOfWeek(new HashSet<>(asList(DayOfWeek.values())));
        }

        if (dto.getSpecificOverride() != null) {
            upperBoundOverride = dto.getSpecificOverride();
            lowerBoundOverride = dto.getSpecificOverride();
        } else {
            upperBoundOverride = dto.getCeilingOverride();
            lowerBoundOverride = dto.getFloorOverride();
        }

        if (isRoomClassPriceExcluded(dto.getRoomClass())) {
            return true;
        }
        return service.isPriceBetweenFloorAndCeilingMultiDay(demandForecastBetweenDatesForAccomClass,
                dto.getProduct().getId(),
                dto.getRoomType(),
                upperBoundOverride, lowerBoundOverride,
                filterDTO.getDaysOfWeek(), taxesByDate, supplement, offsetsForDates, filterDTO.getStartDate(), filterDTO.getEndDate());
    }

    public BigDecimal getSupplementValueFor(PricingMultidayGridDTO dto, PricingMultiProductMultidayFilterDTO filterDTO, Map<AccomTypeSupplementValuePK, AccomTypeSupplementValue> supplementValueMap) {
        LocalDate startDate = new LocalDate(filterDTO.getStartDate().toString());
        return accomTypeSupplementService.getSupplementValue(dto.getProduct().getId(), supplementValueMap, startDate, dto.getRoomType().getId(), getBaseOccupancyType());
    }

    public boolean agileRatesOverrideTabIsVisible(PricingAgileRatesOverrideDTO dto) {
        return !dto.getDate().isBefore(getSystemDateAsLocalDate());
    }

    public LocalDate getRangeStartDate() {
        return getCpPricingFilter().getStartDate().isBefore(getSystemDateAsLocalDate()) ? getSystemDateAsLocalDate() : getCpPricingFilter().getStartDate();
    }

    public int getPropertyId() {
        return uiContext.getPropertyId();
    }

    public List<AccomType> getSortedAccomTypes(AccomClass accomClass, List<AccomType> accomTypes) {
        List<AccomType> accomTypeList = accomTypes.stream().collect(Collectors.toList());
        if (accomClass.getId() == ALL_ROOM_CLASS_ID) {
            return accomTypeList;
        }

        AccomType baseRoomTypeForSelectedRoomClass = getBaseRoomTypeForRoomClass(accomClass);
        if (isShowOnlyBaseRoomTypesFlag()) {
            return asList(baseRoomTypeForSelectedRoomClass);
        }

        PricingUtil.sortAccomTypesByBaseRoomTypeAndCapacity(accomTypeList, baseRoomTypeForSelectedRoomClass);
        return accomTypeList;
    }

    public boolean enableOverrideTabSaveCancelButtonBar(PricingAgileRatesOverrideDTO dto) {
        return dto.getFinalPrice() != null;
    }

    private Stream<CPOverrideWrapper> getOverrideWrappersForDateAndAccomClass(LocalDate date, AccomClass accomClass, Product product) {
        return results.stream()
                .filter(wrapper -> wrapper.getCpbarDecisionDTO().getDate().equals(date))
                .flatMap(wrapper -> wrapper.getCpOverrideWrappers().stream())
                .filter(wrapper -> wrapper.getCpDecisionBAROutput().getProduct().equals(product))
                .filter(overrideWrapper -> overrideWrapper.getSelectedRoomType().getAccomClass().equals(accomClass));
    }

    private Stream<CPOverrideWrapper> getOverrideWrappersForDate(LocalDate date, Product product) {
        return results.stream()
                .filter(wrapper -> wrapper.getCpbarDecisionDTO().getDate().equals(date))
                .flatMap(wrapper -> wrapper.getCpOverrideWrappers().stream())
                .filter(wrapper -> wrapper.getCpDecisionBAROutput().getProduct().equals(product));
    }

    public CPBARDecisionUIWrapper getCPBARDecisionUIWrapperForSelectedDate(LocalDate date) {
        CPBARDecisionUIWrapper cpbarDecisionUIWrapper = results
                .stream()
                .filter(wrapper -> wrapper.getCpbarDecisionDTO().getDate().equals(date))
                .findFirst()
                .orElse(null);
        return cpbarDecisionUIWrapper;
    }

    public CPOverrideWrapper getCPOverrideWrapperForSelectedRoomType(CPBARDecisionUIWrapper dto, AccomType accomType, Product product) {
        if (dto != null && accomType != null) {
            CPOverrideWrapper overrideWrapper = dto
                    .getCpOverrideWrappers()
                    .stream()
                    .filter(wrapper -> wrapper.getCpDecisionBAROutput().getProduct().equals(product))
                    .filter(wrapper -> wrapper.getAccomType().equals(accomType))
                    .findFirst().orElse(null);
            return overrideWrapper;
        }
        return null;
    }

    public CPOverrideWrapper getCPOverrideWrapperForSelectedRoomClass(CPBARDecisionUIWrapper dto, AccomClass accomClass, Product product) {
        if (dto != null && accomClass != null) {
            CPOverrideWrapper overrideWrapper = dto
                    .getCpOverrideWrappers()
                    .stream()
                    .filter(wrapper -> wrapper.getCpDecisionBAROutput().getProduct().equals(product))
                    .filter(wrapper -> wrapper.getAccomClass().equals(accomClass))
                    .findFirst().orElse(null);
            return overrideWrapper;
        }
        return null;
    }

    public List<PricingOverrideHistoryRow> getAgileRatesOverrideHistory(CPOverrideWrapper wrapper) {
        //TODO Extract this for performance later
        List<ProductRateOffsetOverride> overrides = overrideManager.getAllProductRateOffsetOverrides()
                .stream()
                .filter(override -> override.getProduct().equals(wrapper.getCpDecisionBAROutput().getProduct()))
                .filter(override -> override.getOccupancyDate().equals(wrapper.getCpDecisionBAROutput().getArrivalDate()))
                .filter(override -> override.getAccomClass().equals(wrapper.getAccomClass()))
                .collect(Collectors.toList());
        List<User> users = getUsers(overrides);

        overrides.sort(Comparator.comparing(AuditableEntity::getLastUpdatedDate));
        Collections.reverse(overrides);
        int minimumDTARangeID = 0;
        List<Integer> agileRatesDTARangeIDsFromOverrides = overrides.stream().map(override -> override.getAgileRatesDTARange().getId()).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(agileRatesDTARangeIDsFromOverrides)) {
            minimumDTARangeID = agileRatesDTARangeIDsFromOverrides.stream().min(Integer::compare).get();
        }

        List<PricingOverrideHistoryRow> rows = new ArrayList<>();
        for (ProductRateOffsetOverride override : overrides) {

            PricingOverrideHistoryRow row = new PricingOverrideHistoryRow();
            row.setOffsetType(override.getOffsetMethod());
            row.setAdjustedOffset(getAdjustedOffset(override));
            row.setFloorOffset(getFloorOffset(override));
            row.setCeilingOffset(getCeilingOffset(override));
            User userUpdatedBy = users.stream().filter(user -> user.getId().equals(override.getLastUpdatedByUserId())).findFirst().orElse(null);
            row.setUpdatedBy(userUpdatedBy != null ? userUpdatedBy.getName() : "");
            row.setUpdatedOn(override.getLastUpdatedDate() != null ? getFormattedDate(LocalDateUtils.toDate(override.getLastUpdatedDate())) : "");

            if (rows.stream().noneMatch(pricingOverrideHistoryRow -> pricingOverrideHistoryRow.getAdjustedOffset().equals(row.getAdjustedOffset()) &&
                    pricingOverrideHistoryRow.getOffsetType().equals(row.getOffsetType()) &&
                    pricingOverrideHistoryRow.getUpdatedBy().equals(row.getUpdatedBy()) &&
                    pricingOverrideHistoryRow.getUpdatedOn().equals(row.getUpdatedOn())) &&
                    override.getAgileRatesDTARange().getId().equals(minimumDTARangeID)) {
                rows.add(row);
                //If ROW is Inactive, add another row for when the override was created
                if (override.getStatusId().equals(Status.INACTIVE.getId())) {
                    PricingOverrideHistoryRow addedRow = new PricingOverrideHistoryRow();
                    addedRow.setOffsetType(override.getOffsetMethod());
                    addedRow.setAdjustedOffset(override.getOffsetValue().setScale(2, RoundingMode.HALF_UP).toString());
                    addedRow.setFloorOffset(override.getFloorValue() == null ? "" : override.getFloorValue().setScale(2, RoundingMode.HALF_UP).toString());
                    addedRow.setCeilingOffset(override.getCeilingValue() == null ? "" : override.getCeilingValue().setScale(2, RoundingMode.HALF_UP).toString());
                    User userCreatedBy = users.stream().filter(user -> user.getId().equals(override.getCreatedByUserId())).findFirst().orElse(null);
                    addedRow.setUpdatedBy(userCreatedBy != null ? userCreatedBy.getName() : "");
                    addedRow.setUpdatedOn(override.getCreateDate() != null ? getFormattedDate(LocalDateUtils.toDate(override.getCreateDate())) : "");
                    rows.add(addedRow);
                }
            }
        }
        return rows;
    }

    public List<PricingInventoryLimitOverrideHistoryRow> getAgileRatesInventoryLimitOverrideHistory(Date arrivalDate,List<DecisionGPInventoryLimitOverride> overrides) {
        List<PricingInventoryLimitOverrideHistoryRow> rows = new ArrayList<>();
        overrides = overrides.stream()
                .filter(override ->
                        override.getOccupancyDate() != null &&
                                override.getOccupancyDate().equals(arrivalDate))
                .collect(Collectors.toList());
        if(!overrides.isEmpty()) {
            List<User> users = getUsersOfDecisionGPInventoryLimitOverride(overrides);
            overrides.sort(Comparator.comparing(AuditableEntity::getLastUpdatedDate));
            Collections.reverse(overrides);
            for (DecisionGPInventoryLimitOverride override : overrides) {
                PricingInventoryLimitOverrideHistoryRow row = new PricingInventoryLimitOverrideHistoryRow();
                row.setInventoryLimit(getInventoryLimit(override));
                row.setInventoryLimitOverride(getInventoryLimitOverride(override));
                User userUpdatedBy = users.stream().filter(user -> user.getId().equals(override.getLastUpdatedByUserId())).findFirst().orElse(null);
                row.setUpdatedBy(userUpdatedBy != null ? userUpdatedBy.getName() : "");
                row.setUpdatedOn(override.getLastUpdatedDate() != null ? getFormattedDate(LocalDateUtils.toDate(override.getLastUpdatedDate())) : "");
                if (rows.stream().noneMatch(pricingOverrideHistoryRow ->
                        pricingOverrideHistoryRow.getInventoryLimit().equals(row.getInventoryLimit()) &&
                                pricingOverrideHistoryRow.getInventoryLimitOverride().equals(row.getInventoryLimitOverride()))) {
                    rows.add(row);
                    //If ROW is Inactive, add another row for when the override was created
                    if (override.getStatus().equals(TenantStatusEnum.DELETED)) {
                        PricingInventoryLimitOverrideHistoryRow addedRow = new PricingInventoryLimitOverrideHistoryRow();
                        addedRow.setInventoryLimit(override.getInventoryLimit() == null ? "" : override.getInventoryLimit().toString());
                        addedRow.setInventoryLimitOverride(override.getInventoryLimitOverride() == null ? "" : override.getInventoryLimitOverride().toString());
                        User userCreatedBy = users.stream().filter(user -> user.getId().equals(override.getCreatedByUserId())).findFirst().orElse(null);
                        addedRow.setUpdatedBy(userCreatedBy != null ? userCreatedBy.getName() : "");
                        addedRow.setUpdatedOn(override.getCreateDate() != null ? getFormattedDate(LocalDateUtils.toDate(override.getCreateDate())) : "");
                        rows.add(addedRow);
                    }
                }
            }
        }
          return rows;
    }

    protected List<User> getUsers(List<ProductRateOffsetOverride> overrides) {
        Set<Integer> userIdsSet = new HashSet<>();
        userIdsSet.addAll(overrides.stream().map(ProductRateOffsetOverride::getLastUpdatedByUserId).collect(Collectors.toSet()));
        userIdsSet.addAll(overrides.stream().map(ProductRateOffsetOverride::getCreatedByUserId).collect(Collectors.toSet()));
        if (CollectionUtils.isNotEmpty(userIdsSet)) {
            return userService.getTenantUsersById(userIdsSet);
        } else {
            return new ArrayList<>();
        }
    }
    protected List<User> getUsersOfDecisionGPInventoryLimitOverride(List<DecisionGPInventoryLimitOverride> overrides) {
        Set<Integer> userIdsSet = new HashSet<>();
        userIdsSet.addAll(overrides.stream().map(DecisionGPInventoryLimitOverride::getLastUpdatedByUserId).collect(Collectors.toSet()));
        userIdsSet.addAll(overrides.stream().map(DecisionGPInventoryLimitOverride::getCreatedByUserId).collect(Collectors.toSet()));
        if (CollectionUtils.isNotEmpty(userIdsSet)) {
            return userService.getTenantUsersById(userIdsSet);
        } else {
            return new ArrayList<>();
        }
    }

    String getFloorOffset(ProductRateOffsetOverride override) {
        if (override.getStatusId().equals(Status.INACTIVE.getId()) && override.getFloorValue() != null) {
            return UiUtils.getText("removed") + " " + override.getFloorValue().setScale(2, RoundingMode.HALF_UP);
        } else {
            return override.getFloorValue() == null ? "" : override.getFloorValue().setScale(2, RoundingMode.HALF_UP).toString();
        }
    }

    String getInventoryLimit(DecisionGPInventoryLimitOverride override) {
                return override.getInventoryLimit() == null ? "" : override.getInventoryLimit().toString();
    }

    String getInventoryLimitOverride(DecisionGPInventoryLimitOverride override) {
        if (override.getStatus().equals(TenantStatusEnum.DELETED) && override.getInventoryLimit() != null) {
            return UiUtils.getText("removed") + " " + override.getInventoryLimitOverride().toString();
        } else {
            return override.getInventoryLimitOverride() == null ? "" : override.getInventoryLimitOverride().toString();
        }
    }

    String getCeilingOffset(ProductRateOffsetOverride override) {
        if (override.getStatusId().equals(Status.INACTIVE.getId()) && override.getCeilingValue() != null) {
            return UiUtils.getText("removed") + " " + override.getCeilingValue().setScale(2, RoundingMode.HALF_UP);
        } else {
            return override.getCeilingValue() == null ? "" : override.getCeilingValue().setScale(2, RoundingMode.HALF_UP).toString();
        }
    }

    String getAdjustedOffset(ProductRateOffsetOverride override) {
        return override.getStatusId().equals(Status.INACTIVE.getId()) ?
                UiUtils.getText("removed") + " " + override.getOffsetValue().setScale(2, RoundingMode.HALF_UP) :
                override.getOffsetValue().setScale(2, RoundingMode.HALF_UP).toString();
    }

    public String getFormattedDate(Date date) {
        return dateService.formatDateToPropertyTimeZone(date);
    }

    public BigDecimal getOccupancyForecastPercentage(CPBARDecisionDTO cpbarDecisionDTO) {
        if (!isAllRoomClassesSelected()) {
            CPRoomClassDTO cpRoomClassDTO = getRoomClassDetailsMap().get(cpbarDecisionDTO.getDate());
            return cpRoomClassDTO != null ? cpRoomClassDTO.getOccupancyForecast() : null;
        }
        return cpbarDecisionDTO.getOccupancyForecastPercentage();
    }

    public String getAvailableCapacityToSell(CPBARDecisionDTO cpbarDecisionDTO, CPRoomClassDTO cpRoomClassDTO) {
        if (cpbarDecisionDTO.isLongTermBDEWindow()) {
            return HYPHEN;
        } else if (!isAllRoomClassesSelected()) {
            return cpRoomClassDTO != null && cpRoomClassDTO.getAvailableCapacityToSell() != null ? NumberUtils.numberFormatter(cpRoomClassDTO.getAvailableCapacityToSell(), 0) : NULL_STRING;
        }
        return cpbarDecisionDTO.getAvailableCapacityToSell() != null ? NumberUtils.numberFormatter(BigDecimal.valueOf(cpbarDecisionDTO.getAvailableCapacityToSell()), 0) : NULL_STRING;
    }

    public String getAvailableCapacityToSellForRC(CPBARDecisionDTO cpbarDecisionDTO, Integer accomClassId) {
        if (cpbarDecisionDTO.isLongTermBDEWindow()) {
            return HYPHEN;
        }
        CPRoomClassDTO cpRoomClassDTO = getRoomClassDetails(cpbarDecisionDTO.getDate(), accomClassId);
        return cpRoomClassDTO != null && cpRoomClassDTO.getAvailableCapacityToSell() != null ?
                NumberUtils.numberFormatter(cpRoomClassDTO.getAvailableCapacityToSell(), 0) : NULL_STRING;
    }

    public String getAvailableCapacityToSellForRT(CPBARDecisionDTO cpbarDecisionDTO, AccomType accomType) {
        if (cpbarDecisionDTO.isLongTermBDEWindow() || overrideManager.isHospitalityRoom(accomType.getAccomTypeCode())) {
            return HYPHEN;
        }
        CPRoomClassDTO cpRoomClassDTO = getAccomTypeDetails(cpbarDecisionDTO.getDate(), accomType.getId());
        return cpRoomClassDTO != null && cpRoomClassDTO.getAvailableCapacityToSell() != null ?
                NumberUtils.numberFormatter(cpRoomClassDTO.getAvailableCapacityToSell(), 0) : NULL_STRING;
    }

    private CPRoomClassDTO getAccomTypeDetails(LocalDate date, Integer accomTypeId) {
        return overrideManager.getAccomTypeDetailsMapById().getOrDefault(date, Collections.emptyMap()).get(accomTypeId);
    }

    public String getAuthorizedCapacity(CPBARDecisionDTO cpbarDecisionDTO, CPRoomClassDTO cpRoomClassDTO) {
        if (cpbarDecisionDTO.isLongTermBDEWindow()) {
            return HYPHEN;
        } else if (!isAllRoomClassesSelected()) {
            return cpRoomClassDTO != null && cpRoomClassDTO.getAuthorizedCapacity() != null ? NumberUtils.numberFormatter(cpRoomClassDTO.getAuthorizedCapacity(), 0) : NULL_STRING;
        }
        return cpbarDecisionDTO.getAuthorizedCapacity() != null ? NumberUtils.numberFormatter(BigDecimal.valueOf(cpbarDecisionDTO.getAuthorizedCapacity()), 0) : NULL_STRING;
    }

    public String getEffectiveCapacity(CPBARDecisionDTO cpbarDecisionDTO, CPRoomClassDTO cpRoomClassDTO) {
        if (cpbarDecisionDTO.isLongTermBDEWindow()) {
            return HYPHEN;
        } else if (!isAllRoomClassesSelected()) {
            return cpRoomClassDTO != null && cpRoomClassDTO.getEffectiveCapacity() != null ? NumberUtils.numberFormatter(cpRoomClassDTO.getEffectiveCapacity(), 0) : NULL_STRING;
        }
        return cpbarDecisionDTO.getEffectiveCapacity() != null ? NumberUtils.numberFormatter(BigDecimal.valueOf(cpbarDecisionDTO.getEffectiveCapacity()), 0) : NULL_STRING;
    }

    public String getRoomsOnBooks(CPBARDecisionDTO cpbarDecisionDTO, CPRoomClassDTO cpRoomClassDTO) {
        if (cpbarDecisionDTO.isLongTermBDEWindow()) {
            return HYPHEN;
        } else if (!isAllRoomClassesSelected()) {
            return cpRoomClassDTO != null && cpRoomClassDTO.getRoomsOnBooks() != null ? NumberUtils.numberFormatter(cpRoomClassDTO.getRoomsOnBooks(), 0) : NULL_STRING;
        }
        return cpbarDecisionDTO.getRoomsOnBooks() != null ? NumberUtils.numberFormatter(BigDecimal.valueOf(cpbarDecisionDTO.getRoomsOnBooks()), 0) : NULL_STRING;
    }

    public String getOnBooksPercentage(CPBARDecisionDTO cpbarDecisionDTO, CPRoomClassDTO cpRoomClassDTO) {
        boolean isPhysical = configParamsService.getBooleanParameterValue(
                FeatureTogglesConfigParamName.ENABLE_PHYSICAL_CAPACITY_CONSIDERATION
        );
        if (cpbarDecisionDTO.isLongTermBDEWindow()) {
            return HYPHEN;
        } else if (!isAllRoomClassesSelected()) {
            if (cpRoomClassDTO != null && cpRoomClassDTO.getRoomsOnBooks() != null) {
                BigDecimal onBooksLong = cpRoomClassDTO.getRoomsOnBooks();

                BigDecimal capacity = isPhysical
                        ? cpRoomClassDTO.getCapacity()
                        : cpRoomClassDTO.getEffectiveCapacity();

                if (capacity != null && capacity.compareTo(BigDecimal.ZERO) != 0) {
                    BigDecimal onBooksPerc = onBooksLong
                            .divide(capacity, 4, RoundingMode.HALF_UP)
                            .multiply(BigDecimal.valueOf(100))
                            .setScale(2, RoundingMode.HALF_UP);
                    return "OB - " + onBooksPerc.toPlainString() + "%";
                }
            }
            return "OB - 0.00%";
        }
        return cpbarDecisionDTO.getOnBooksPerc() != null
                ? "OB - " + cpbarDecisionDTO.getOnBooksPerc().setScale(2, RoundingMode.HALF_UP).toPlainString() + "%"
                : "OB - 0.00%";
    }

    public String getOutOfOrder(CPBARDecisionDTO cpbarDecisionDTO, CPRoomClassDTO cpRoomClassDTO) {
        if (cpbarDecisionDTO.isLongTermBDEWindow()) {
            return HYPHEN;
        } else if (!isAllRoomClassesSelected()) {
            return cpRoomClassDTO != null && cpRoomClassDTO.getOutOfOrder() != null ? NumberUtils.numberFormatter(cpRoomClassDTO.getOutOfOrder(), 0) : NULL_STRING;
        }
        return cpbarDecisionDTO.getOutOfOrder() != null ? NumberUtils.numberFormatter(BigDecimal.valueOf(cpbarDecisionDTO.getOutOfOrder()), 0) : NULL_STRING;
    }

    public String getOptimalPrice(CPBARDecisionDTO cpbarDecisionDTO, boolean isOnlyBARProductSelected, boolean isOnlyIndependentProductSelected, AccomClass selectedRoomClass, List<AccomType> selectedRoomTypes) {
        CPDecisionBAROutput cpDecisionBAROutput = getCpDecisionBAROutput(cpbarDecisionDTO, isOnlyBARProductSelected, isOnlyIndependentProductSelected, selectedRoomClass, selectedRoomTypes);
        return getOptimalPriceForSelection(cpDecisionBAROutput);
    }
    private String getOptimalPriceForSelection(CPDecisionBAROutput cpDecisionBAROutput) {
        if (cpDecisionBAROutput == null || cpDecisionBAROutput.getOptimalBAR() == null) {
            return NULL_STRING;
        }
        return NumberUtils.numberFormatter(cpDecisionBAROutput.getOptimalBAR(), 2);
    }
    public String getRoomOnlyPrice(CPBARDecisionDTO cpbarDecisionDTO, boolean isOnlyBARProductSelected, boolean isOnlyIndependentProductSelected, AccomClass selectedRoomClass, List<AccomType> selectedRoomTypes) {
        CPDecisionBAROutput cpDecisionBAROutput = getCpDecisionBAROutput(cpbarDecisionDTO, isOnlyBARProductSelected, isOnlyIndependentProductSelected, selectedRoomClass, selectedRoomTypes);
        return getRoomOnlyPriceForSelection(cpDecisionBAROutput);
    }
    private String getRoomOnlyPriceForSelection(CPDecisionBAROutput cpDecisionBAROutput) {
        if (cpDecisionBAROutput == null || cpDecisionBAROutput.getRoomsOnlyBAR() == null) {
            return NULL_STRING;
        }
        return NumberUtils.numberFormatter(cpDecisionBAROutput.getRoomsOnlyBAR(), 2);
    }
    private CPDecisionBAROutput getCpDecisionBAROutput(CPBARDecisionDTO cpbarDecisionDTO, boolean isOnlyBARProductSelected, boolean isOnlyIndependentProductSelected, AccomClass selectedRoomClass, List<AccomType> selectedRoomTypes) {
        CPDecisionBAROutput cpDecisionBAROutput = null;
        if(isOnlyBARProductSelected && !isOnlyIndependentProductSelected) {
            if(isAllRoomClassesWithBaseRoomType()) {
                cpDecisionBAROutput = getBAROutputByAllRCs(cpbarDecisionDTO);
            } else if(isSpecificRoomClassSelected() && selectedRoomTypes !=null && selectedRoomTypes.size() == 1) {
                cpDecisionBAROutput = getBAROutputBySingleRCAndSingleRT(cpbarDecisionDTO, selectedRoomClass, selectedRoomTypes);
            } else if(isSpecificRoomClassSelected() && selectedRoomTypes !=null && selectedRoomTypes.size() > 1 && selectedRoomTypes.stream().anyMatch(this::isBaseRoomType)) {
                cpDecisionBAROutput = getBAROutputBySingleRCAndMultipleRTs(cpbarDecisionDTO, selectedRoomClass);
            }
        } else if(isOnlyIndependentProductSelected && !isOnlyBARProductSelected) {
            if(isAllRoomClassesWithBaseRoomType()) {
                cpDecisionBAROutput = getIPPOutputByAllRCs(cpbarDecisionDTO);
            } else if(isSpecificRoomClassSelected() && selectedRoomTypes !=null && selectedRoomTypes.size() == 1) {
                cpDecisionBAROutput = getIPPOutputBySingleRCAndSingleRT(cpbarDecisionDTO, selectedRoomClass, selectedRoomTypes);
            } else if(isSpecificRoomClassSelected() && selectedRoomTypes !=null && selectedRoomTypes.size() > 1 && selectedRoomTypes.stream().anyMatch(this::isBaseRoomType)) {
                cpDecisionBAROutput = getIPPOutputBySingleRCAndMultipleRTs(cpbarDecisionDTO, selectedRoomClass);
            }
        }
        return cpDecisionBAROutput;
    }
    private CPDecisionBAROutput getBAROutputBySingleRCAndMultipleRTs(CPBARDecisionDTO cpbarDecisionDTO, AccomClass selectedRoomClass) {
        return cpbarDecisionDTO.getDecisions().stream()
            .filter(cpDecisionBAROutput -> cpDecisionBAROutput.getProduct().isSystemDefault())
            .filter(cpDecisionBAROutput -> cpDecisionBAROutput.getAccomType().getAccomClass().getStatusId() == 1)
            .filter(cpDecisionBAROutput -> cpDecisionBAROutput.getAccomType().getAccomClass().equals(selectedRoomClass))
            .filter(cpDecisionBAROutput -> cpDecisionBAROutput.getAccomType().isBaseRoomType())
            .findFirst().orElse(null);
    }
    private CPDecisionBAROutput getBAROutputBySingleRCAndSingleRT(CPBARDecisionDTO cpbarDecisionDTO, AccomClass selectedRoomClass, List<AccomType> selectedRoomTypes) {
        return cpbarDecisionDTO.getDecisions().stream()
            .filter(cpDecisionBAROutput -> cpDecisionBAROutput.getProduct().isSystemDefault())
            .filter(cpDecisionBAROutput -> cpDecisionBAROutput.getAccomType().getAccomClass().getStatusId() == 1)
            .filter(cpDecisionBAROutput -> cpDecisionBAROutput.getAccomType().getAccomClass().equals(selectedRoomClass))
            .filter(cpDecisionBAROutput -> cpDecisionBAROutput.getAccomType().equals(selectedRoomTypes.stream().findFirst().orElse(null)))
            .findFirst().orElse(null);
    }

    private CPDecisionBAROutput getBAROutputByAllRCs(CPBARDecisionDTO cpbarDecisionDTO) {
        return cpbarDecisionDTO.getDecisions().stream()
            .filter(cpDecisionBAROutput -> cpDecisionBAROutput.getProduct().isSystemDefault())
            .filter(cpDecisionBAROutput -> cpDecisionBAROutput.getAccomType().getAccomClass().getStatusId() == 1)
            .filter(cpDecisionBAROutput -> cpDecisionBAROutput.getAccomType().getAccomClass().getSystemDefault() == 0)
            .filter(cpDecisionBAROutput -> cpDecisionBAROutput.getAccomType().getAccomClass().getMasterClassBoolean())
            .filter(cpDecisionBAROutput -> cpDecisionBAROutput.getAccomType().isBaseRoomType())
            .findFirst().orElse(null);
    }
    private CPDecisionBAROutput getIPPOutputBySingleRCAndMultipleRTs(CPBARDecisionDTO cpbarDecisionDTO, AccomClass selectedRoomClass) {
        return cpbarDecisionDTO.getDecisions().stream()
            .filter(cpDecisionBAROutput -> cpDecisionBAROutput.getProduct().isIndependentProduct())
            .filter(cpDecisionBAROutput -> cpDecisionBAROutput.getAccomType().getAccomClass().getStatusId() == 1)
            .filter(cpDecisionBAROutput -> cpDecisionBAROutput.getAccomType().getAccomClass().equals(selectedRoomClass))
            .filter(cpDecisionBAROutput -> cpDecisionBAROutput.getAccomType().isBaseRoomType())
            .findFirst().orElse(null);
    }
    private CPDecisionBAROutput getIPPOutputBySingleRCAndSingleRT(CPBARDecisionDTO cpbarDecisionDTO, AccomClass selectedRoomClass, List<AccomType> selectedRoomTypes) {
        return cpbarDecisionDTO.getDecisions().stream()
            .filter(cpDecisionBAROutput -> cpDecisionBAROutput.getProduct().isIndependentProduct())
            .filter(cpDecisionBAROutput -> cpDecisionBAROutput.getAccomType().getAccomClass().getStatusId() == 1)
            .filter(cpDecisionBAROutput -> cpDecisionBAROutput.getAccomType().getAccomClass().equals(selectedRoomClass))
            .filter(cpDecisionBAROutput -> cpDecisionBAROutput.getAccomType().equals(selectedRoomTypes.stream().findFirst().orElse(null)))
            .findFirst().orElse(null);
    }
    private CPDecisionBAROutput getIPPOutputByAllRCs(CPBARDecisionDTO cpbarDecisionDTO) {
        return cpbarDecisionDTO.getDecisions().stream()
            .filter(cpDecisionBAROutput -> cpDecisionBAROutput.getProduct().isIndependentProduct())
            .filter(cpDecisionBAROutput -> cpDecisionBAROutput.getAccomType().getAccomClass().getStatusId() == 1)
            .filter(cpDecisionBAROutput -> cpDecisionBAROutput.getAccomType().getAccomClass().getSystemDefault() == 0)
            .filter(cpDecisionBAROutput -> cpDecisionBAROutput.getAccomType().getAccomClass().getMasterClassBoolean())
            .filter(cpDecisionBAROutput -> cpDecisionBAROutput.getAccomType().isBaseRoomType())
            .findFirst().orElse(null);
    }
    public String getLRVForMasterClass(CPBARDecisionUIWrapper wrapper) {
        if (wrapper.isLongTermBDEWindow()) {
            return HYPHEN;
        }
        BigDecimal lrv = overrideManager.getMasterClassLrvForOccupancyDate(wrapper.getCpbarDecisionDTO().getDate());
        return lrv == null ? HYPHEN : NumberUtils.numberFormatter(lrv, 2);
    }

    public CPRoomClassDTO getRoomClassDetails(LocalDate date, Integer roomClassId) {
        return overrideManager.getRoomClassDetailsMapById().getOrDefault(date, Collections.emptyMap()).get(roomClassId);
    }

    public Map<LocalDate, CPRoomClassDTO> getRoomClassDetailsMap() {
        return overrideManager.getRoomClassDetailsMap();
    }

    @ForTesting
    public void setRoomClassDetailsMap(Map<LocalDate, CPRoomClassDTO> roomClassDetailsMap) {
        overrideManager.setRoomClassDetailsMap(roomClassDetailsMap);
    }

    public BigDecimal getPreviousBar(CPOverrideWrapper overrideWrapper) {
        if (getCpPricingFilter().getDateWithFilterEnum().equals(DateWithFilterEnum.CHANGES_SINCE) ||
                getCpPricingFilter().getDateWithFilterEnum().equals(DateWithFilterEnum.CHANGES_SINCE_LAST_BDE) ||
                getCpPricingFilter().getDateWithFilterEnum().equals(DateWithFilterEnum.CHANGES_SINCE_LAST_IDP)) {
            CPPaceDecisionBAROutput paceDecisionBAROutput = overrideManager.getPaceDecisions().stream()
                    .filter(output -> output.getProduct().equals(overrideWrapper.getProduct()))
                    .filter(output -> output.getArrivalDate().equals(overrideWrapper.getCpDecisionBAROutput().getArrivalDate()) && output.getAccomType().getId().equals(overrideWrapper.getAccomType().getId()))
                    .findFirst()
                    .orElse(null);
            return paceDecisionBAROutput != null ? paceDecisionBAROutput.getFinalBAR() : null;
        }
        return overrideWrapper.getPreviousBAR();
    }

    public String getOFPercentageLabelCaption(BigDecimal occupancyForecastPercentage, CPBARDecisionUIWrapper wrapper) {
        if (wrapper.isLongTermBDEWindow()) {
            return UiUtils.getText("common.occ.fcst") + " " + HYPHEN;
        } else if (isSpecificRoomClassSelected()) {
            return UiUtils.getText("common.occ.fcst") + " - " + (occupancyForecastPercentage != null ? occupancyForecastPercentage.setScale(2, RoundingMode.HALF_UP) : "");
        }
        return UiUtils.getText("common.occ.fcst") + " - " + (occupancyForecastPercentage != null ? occupancyForecastPercentage.setScale(2, RoundingMode.HALF_UP) + "%" : "");
    }

    public Instance<InvestigatorOverridePopUpCPView> getOverridePopUpViewFactory() {
        return investigatorOverridePopUpCPViewFactory;
    }

    public MultiProductOverridePopUpView getMultiProductOverridePopUpView() {
        return multiProductOverridePopUpView;
    }

    public boolean isOptimizedProduct(CPOverrideWrapper wrapper) {
        return wrapper.getCpDecisionBAROutput().getProduct().isOptimized();
    }

    public boolean hasFloorOverride(CPOverrideWrapper overrideWrapper) {
        if (isOnlyBARProductSelected() || isOptimizedProduct(overrideWrapper)) {
            if (overrideWrapper.getFloorOverride() != null) {
                return true;
            } else if (overrideWrapper.getProductRateOffsetOverrides() != null) {
                return overrideWrapper.getProductRateOffsetOverrides().stream().anyMatch(override -> override.getFloorValue() != null);
            }
        }
        return false;
    }

    public boolean hasCeilingOverride(CPOverrideWrapper overrideWrapper) {
        if (isOnlyBARProductSelected() || isOptimizedProduct(overrideWrapper)) {
            if (overrideWrapper.getCeilingOverride() != null) {
                return true;
            } else if (overrideWrapper.getProductRateOffsetOverrides() != null) {
                return overrideWrapper.getProductRateOffsetOverrides().stream().anyMatch(override -> override.getCeilingValue() != null);
            }
        }
        return false;
    }

    public boolean areFloorAndCeilingValid(TetrisTextFieldV8 floorField, TetrisTextFieldV8 ceilField) {
        BigDecimal floor = floorField.isEmpty() ? BigDecimal.valueOf(Integer.MIN_VALUE) : toBigDecimal(floorField.getValue());
        BigDecimal ceil = ceilField.isEmpty() ? BigDecimal.valueOf(Integer.MAX_VALUE) : toBigDecimal(ceilField.getValue());
        return floor.compareTo(ceil) <= 0;
    }

    public boolean isOffsetValidGivenLowerBounds(AgileRatesOffsetMethod offsetMethod, BigDecimal value, BigDecimal lowerBound) {
        if (AgileRatesOffsetMethod.PERCENTAGE.equals(offsetMethod)) {
            return !BigDecimalUtil.isLessThan(value, lowerBound);
        }
        return true;
    }

    public void onDeleteFloorOverride(PricingAgileRatesOverrideDTO dto) {
        dto.setFloorMarkedForDeletion(true);
    }

    public void onDeleteCeilingOverride(PricingAgileRatesOverrideDTO dto) {
        dto.setCeilingMarkedForDeletion(true);
    }

    public void onRevertFloorOverride(PricingAgileRatesOverrideDTO dto) {
        dto.setFloorMarkedForDeletion(false);

        if (dto.hasExistingFloorOverride()) {
            dto.setFloorOffset(dto.getOverride().getFloorValue());
        } else {
            dto.setFloorOffset(null);
        }
    }

    public void onRevertCeilingOverride(PricingAgileRatesOverrideDTO dto) {
        dto.setCeilingMarkedForDeletion(false);

        if (dto.hasExistingCeilingOverride()) {
            dto.setCeilingOffset(dto.getOverride().getCeilingValue());
        } else {
            dto.setCeilingOffset(null);
        }
    }

    public void onApplyOverrideChangesEvent(@Observes(notifyObserver = Reception.IF_EXISTS) ApplyMultiProductOverrideEvent event) {
        if (MultiProductEnum.INVESTIGATOR.equals(event.getMultiProductEnum())) {
            multiProductBARDTOs = event.getBAROverrides();
            applyMultiProductBARRemoveWrappers();
            applyMultiProductBARSaveWrappers();
            appliedMultiProductBARDTOs.addAll(multiProductBARDTOs);
            multiProductBARDTOs = new ArrayList<>();
            applyAgileRatesWrappers();
        } else if (MultiProductEnum.AGILE_RATES_OPTIMIZED.equals(event.getMultiProductEnum())) {
            storeAgileRatesDTO(event.getAgileRatesOverrideDTO(), true);
            applyAgileRatesWrappers();
            applyMultiProductBARWrappers();
        } else {
            storeAgileRatesDTO(event.getAgileRatesOverrideDTO(), false);
            applyAgileRatesWrappers();
            applyMultiProductBARWrappers();
        }
    }

    public void applyAgileRatesWrappers() {
        applyOptimizedAgileRatesWrappers();
        applyNonOptimizedAgileRatesWrappers();
        multiProductAgileRatesDTOs = new ArrayList<>();
    }

    private void applyMultiProductBARWrappers() {
        InvestigatorUtil.storeBARProductOverrideChanges();
        applyMultiProductBARRemoveWrappers();
        applyMultiProductBARSaveWrappers();
        appliedMultiProductBARDTOs.addAll(multiProductBARDTOs);
        multiProductBARDTOs = new ArrayList<>();
    }

    @VisibleForTesting
    public void applyMultiProductBARRemoveWrappers() {
        List<CPOverrideWrapper> wrappersToRemove = multiProductBARDTOs.stream()
                .filter(dto -> dto.getProduct().isSystemDefaultOrIndependentProduct())
                .filter(this::willApplyRemoveFromBARProduct)
                .collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(wrappersToRemove)) {
            List<Product> productsToRemove = wrappersToRemove.stream()
                    .map(CPOverrideWrapper::getProduct)
                    .distinct()
                    .collect(Collectors.toList());
            for (Product product : productsToRemove) {
                List<CPOverrideWrapper> wrappers = wrappersToRemove.stream()
                        .filter(dto -> dto.getProduct().isSystemDefaultOrIndependentProduct())
                        .filter(dto -> dto.getProduct().equals(product))
                        .map(this::findWrapper)
                        .collect(Collectors.toList());
                LocalDate startDate = getStartDateFromOverrideWrappers(wrappers);
                LocalDate endDate = getEndDateFromOverrideWrappers(wrappers);

                CPDecisionContext cpDecisionContext = getCpDecisionContext(startDate, endDate, false);
                PricingAccomClass masterPricingAccomClass = getMasterPricingAccomClassFromCPDecisionContext(cpDecisionContext);

                wrappersToRemove.forEach(dto -> {
                    applySingleDayOverrideChanges(dto, cpDecisionContext, masterPricingAccomClass);
                });
            }
        }
    }

    @VisibleForTesting
    public void applyMultiProductBARSaveWrappers() {
        List<CPOverrideWrapper> wrappersToSave = multiProductBARDTOs.stream()
                .filter(dto -> dto.getProduct().isSystemDefaultOrIndependentProduct())
                .filter(dto -> !willApplyRemoveFromBARProduct(dto))
                .collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(wrappersToSave)) {
            List<Product> productsToSave = wrappersToSave.stream()
                    .map(CPOverrideWrapper::getProduct)
                    .distinct()
                    .collect(Collectors.toList());
            for (Product product : productsToSave) {
                List<CPOverrideWrapper> wrappers = wrappersToSave.stream()
                        .filter(dto -> dto.getProduct().isSystemDefaultOrIndependentProduct())
                        .filter(dto -> dto.getProduct().equals(product))
                        .map(this::findWrapper)
                        .collect(Collectors.toList());
                LocalDate startDate = getStartDateFromOverrideWrappers(wrappers);
                LocalDate endDate = getEndDateFromOverrideWrappers(wrappers);

                CPDecisionContext cpDecisionContext = getCpDecisionContext(startDate, endDate, true);
                PricingAccomClass masterPricingAccomClass = getMasterPricingAccomClassFromCPDecisionContext(cpDecisionContext);

                wrappersToSave.forEach(dto -> {
                    applySingleDayOverrideChanges(dto, cpDecisionContext, masterPricingAccomClass);
                });
            }
        }
    }

    @VisibleForTesting
    public void applyNonOptimizedAgileRatesWrappers() {
        multiProductNonOptimizedDTOs.stream()
                .filter(dto -> !dto.getProduct().isSystemDefaultOrIndependentProduct())
                .filter(dto -> !dto.getProduct().isOptimized())
                .filter(dto -> dto.isAdjustmentMarkedForDeletion())
                .forEach(dto -> removeSingleDayOverrideFromAgileRatesPopUp(dto.getOverrideWrapper(), true));
        multiProductNonOptimizedDTOs.stream()
                .filter(dto -> !dto.getProduct().isSystemDefaultOrIndependentProduct())
                .filter(dto -> !dto.getProduct().isOptimized())
                .filter(dto -> (dto.getAdjustment() != null && dto.getOriginalAdjustment() == null) ||
                        !BigDecimalUtil.equals(dto.getAdjustment(), dto.getOriginalAdjustment()))
                .forEach(dto -> applyProductRateOffsetOverride(dto));
        if (isGroupProductInventoryLimitEnabled) {
            multiProductNonOptimizedDTOs.stream()
                    .filter(dto -> dto.isInventoryLimitMarkedForDeletion() ||
                            (!dto.isInventoryLimitMarkedForDeletion() && null != dto.getInventoryLimitOverride() && !Objects.equals(dto.getInventoryLimitOverride(), dto.getOriginalInventoryLimitOverride())))
                    .forEach(this::applyInventoryLimitOverrides);
        }
        multiProductNonOptimizedDTOs = new ArrayList<>();
    }

    @VisibleForTesting
    public void applyOptimizedAgileRatesWrappers() {
        multiProductOptimizedDTOs.stream()
                .filter(dto -> !dto.getProduct().isSystemDefaultOrIndependentProduct())
                .filter(dto -> dto.getProduct().isOptimized())
                .filter(dto -> dto.getFloorOffset() != null && dto.getCeilingOffset() != null)
                .filter(dto -> (dto.getFloorOffset() != null && dto.getOriginalFloorOffset() == null) ||
                        (dto.getCeilingOffset() != null && dto.getOriginalCeilingOffset() == null) ||
                        !BigDecimalUtil.equals(dto.getFloorOffset(), dto.getOriginalFloorOffset()) ||
                        !BigDecimalUtil.equals(dto.getCeilingOffset(), dto.getOriginalCeilingOffset()) ||
                        dto.isFloorMarkedForDeletion() || dto.isCeilingMarkedForDeletion())
                .forEach(dto -> {
                    if (dto.isFullyReverted()) {
                        revertAgileRatesChange(dto.getOverrideWrapper());
                    } else {
                        applyProductRateOffsetOverride(dto);
                    }
                });
        if (isGroupProductInventoryLimitEnabled) {
            multiProductOptimizedDTOs.stream()
                .filter(dto -> dto.isInventoryLimitMarkedForDeletion() ||
                        (!dto.isInventoryLimitMarkedForDeletion() && null != dto.getInventoryLimitOverride() && !Objects.equals(dto.getInventoryLimitOverride(), dto.getOriginalInventoryLimitOverride())))
                .forEach(this::applyInventoryLimitOverrides);
        }
        multiProductOptimizedDTOs = new ArrayList<>();
    }

    public void onStoreOverrideChangesEvent(@Observes(notifyObserver = Reception.IF_EXISTS) StoreMultiProductOverrideEvent event) {
        if (MultiProductEnum.INVESTIGATOR.equals(event.getMultiProductEnum())) {
            multiProductBARDTOs = event.getInvestigatorOverrideDTOs();
        } else storeAgileRatesDTO(event.getAgileRatesOverrideDTO(), MultiProductEnum.AGILE_RATES_OPTIMIZED.equals(event.getMultiProductEnum()));
        updateMultiProductUnsavedChanges();
        InvestigatorUtil.sendUpdateMultiProductUnsavedChangesEvent(multiProductDtosHaveChanges());
    }

    public boolean multiProductDtosHaveChanges() {
        return multiProductUnsavedChanges;
    }

    public void updateMultiProductUnsavedChanges() {
        multiProductUnsavedChanges = multiProductBARDtosHaveChanges() || multiProductAgileRatesNonOptimizedDtosHaveChanges() || multiProductAgileRatesOptimizedDtosHaveChanges();
    }

    protected boolean multiProductAgileRatesOptimizedDtosHaveChanges() {
        return multiProductOptimizedDTOs.stream()
                .filter(dto -> !dto.getProduct().isSystemDefaultOrIndependentProduct())
                .filter(dto -> dto.getProduct().isOptimized())
                .anyMatch(dto -> optimizedProductHasChanges(dto));
    }

    protected boolean multiProductAgileRatesNonOptimizedDtosHaveChanges() {
        return multiProductNonOptimizedDTOs.stream()
                .filter(dto -> !dto.getProduct().isSystemDefaultOrIndependentProduct())
                .filter(dto -> !dto.getProduct().isOptimized())
                .anyMatch(dto -> nonOptimizedProductHasChanges(dto));
    }

    protected boolean multiProductBARDtosHaveChanges() {
        return multiProductBARDTOs.stream()
                .filter(dto -> dto.getProduct().isSystemDefaultOrIndependentProduct())
                .anyMatch(dto -> !dto.isMatchingExistingOverride(getHighestBarRestrictedEnabled()) || appliedMultiProductBARDTOs.contains(dto));
    }

    public void onCancelMultiProductOverrideEvent(@Observes(notifyObserver = Reception.IF_EXISTS) SendCancelMultiProductOverrideEvent ignored) {
        clearMultiProductOverrides();
    }

    private void clearMultiProductOverrides() {
        multiProductNonOptimizedDTOs = new ArrayList<>();
        multiProductOptimizedDTOs = new ArrayList<>();
        multiProductBARDTOs = new ArrayList<>();
        multiProductAgileRatesDTOs = new ArrayList<>();
        multiProductUnsavedChanges = false;
    }

    protected void storeAgileRatesDTO(PricingAgileRatesOverrideDTO dto, boolean isOptimized) {
        List<PricingAgileRatesOverrideDTO> dtoList = isOptimized ? multiProductOptimizedDTOs : multiProductNonOptimizedDTOs;
        PricingAgileRatesOverrideDTO pricingAgileRatesOverrideDTO = dtoList
                .stream()
                .filter(item -> item.getDate().equals(dto.getDate()))
                .filter(item -> item.getProduct().equals(dto.getProduct()))
                .filter(item -> item.getRoomType().equals(dto.getRoomType()))
                .findFirst()
                .orElse(null);
        if (pricingAgileRatesOverrideDTO != null) {
            pricingAgileRatesOverrideDTO.setAdjustment(dto.getAdjustment());
            pricingAgileRatesOverrideDTO.setCeilingOffset(dto.getCeilingOffset());
            pricingAgileRatesOverrideDTO.setFloorOffset(dto.getFloorOffset());
            if(isGroupProductInventoryLimitEnabled) {
                pricingAgileRatesOverrideDTO.setInventoryLimitOverride(dto.getInventoryLimitOverride());
                pricingAgileRatesOverrideDTO.setInventoryLimit(dto.getInventoryLimit());
                pricingAgileRatesOverrideDTO.setOriginalInventoryLimitOverride(dto.getOriginalInventoryLimitOverride());
                pricingAgileRatesOverrideDTO.setInventoryLimitMarkedForDeletion(dto.isInventoryLimitMarkedForDeletion());
                pricingAgileRatesOverrideDTO.setDate(dto.getDate());
            }

        } else {
            if(isGroupProductInventoryLimitEnabled) {
                dto.setInventoryLimitOverride(dto.getInventoryLimitOverride());
                dto.setInventoryLimit(dto.getInventoryLimit());
                dto.setOriginalInventoryLimitOverride(dto.getOriginalInventoryLimitOverride());
                dto.setInventoryLimitMarkedForDeletion(dto.isInventoryLimitMarkedForDeletion());
                dto.setDate(dto.getDate());
            }
            dtoList.add(dto);
        }
    }

    private BigDecimal toBigDecimal(String strRepresentation) {
        return strToDecimalConverter.convertToModel(strRepresentation, null, UiUtils.getLocale());
    }

    @ForTesting
    public void setPropertyIsOneWayOrAbove(boolean propertyIsOneWayOrAbove) {
        isPropertyIsOneWayOrAbove = propertyIsOneWayOrAbove;
    }

    @ForTesting
    public void setOverridesMap(Map<CPDecisionBAROutput, CPOverrideWrapper> overridesMap) {
        overrideManager.setOverridesMap(overridesMap);
    }

    @ForTesting
    List<CPOverrideWrapper> getProductOverrides() {
        return productOverrides;
    }

    @ForTesting
    public void setProductOverrides(List<CPOverrideWrapper> productOverrides) {
        this.productOverrides = productOverrides;
    }

    public boolean areAnyPresentAdjustmentsNotEqual(BigDecimal adjustment, BigDecimal originalAdjustment) {
        if (anyAdjustmentsNull(adjustment, originalAdjustment)) {
            return false;
        }
        return !adjustment.equals(originalAdjustment);
    }

    private boolean anyAdjustmentsNull(BigDecimal adjustment, BigDecimal originalAdjustment) {
        return originalAdjustment == null || adjustment == null;
    }

    public String getCompetitorRateValue(CPDecisionBAROutput cpDecisionBAROutput) {
        if (cpDecisionBAROutput != null && cpDecisionBAROutput.getCompetitorRate() != null) {
            //The competitor rate could also be a non-number status code
            if (isNumber(cpDecisionBAROutput.getCompetitorRate())) {
                return NumberUtils.numberFormatter(new BigDecimal(cpDecisionBAROutput.getCompetitorRate()), 2);
            }
            return cpDecisionBAROutput.getCompetitorRate();
        }
        return NULL_STRING;
    }

    public String getAdjustmentValue(CPOverrideWrapper cpOverrideWrapper) {
        if (null != cpOverrideWrapper && null != cpOverrideWrapper.getAdjustmentValue()) {
            String ext = AgileRatesOffsetMethod.PERCENTAGE.equals(cpOverrideWrapper.getAdjustmentType()) ? PERCENTAGE : StringUtils.EMPTY;
            return NumberUtils.numberFormatter(cpOverrideWrapper.getAdjustmentValue(), 2) + ext;
        }
        return DEFAULT_ADJUSTMENT;
    }

    @ForTesting
    void setPricingAccomClasses(List<PricingAccomClass> pricingAccomClasses) {
        this.pricingAccomClasses = pricingAccomClasses;
    }

    protected void onPropertyStateChange(PropertyState propertyState) {
        if (propertyState.isDecisionUploadInProgress()) {
            view.disableUploadButtonWhenDecisionUploadIsInProgress();
        } else {
            view.updateUploadEnabled(isManualUploadEnabled(), isPropertyStageTwoWay(), isBarOverridePendingForUpload(), isAgileRatesPendingForUpload(), isInventoryLimitPendingForUpload());
        }
    }

    public String getAdjustmentValueString(PricingAgileRatesOverrideDTO dto) {
        if (dto.getProduct().isOptimized()) {
            return (dto.getFloorDiscount() != null ? NumberUtils.numberFormatter(dto.getFloorDiscount(), 2) + (AgileRatesOffsetMethod.PERCENTAGE.equals(dto.getDiscountOffsetMethod()) ? "%" : "") : "")
                    +
                    (dto.getCeilingDiscount() != null ? AGILE_RATES_ADJUSTMENTS_SEPARATOR + NumberUtils.numberFormatter(dto.getCeilingDiscount(), 2) + (AgileRatesOffsetMethod.PERCENTAGE.equals(dto.getDiscountOffsetMethod()) ? "%" : "") : "");
        } else {
            return dto.getFloorDiscount() != null ? NumberUtils.numberFormatter(dto.getFloorDiscount(), 2) + (AgileRatesOffsetMethod.PERCENTAGE.equals(dto.getDiscountOffsetMethod()) ? "%" : "") : "";
        }
    }

    public boolean isFloorMatchingEmptyOrFilledValue(TetrisTextFieldV8 floor, TetrisTextFieldV8 ceiling) {
        if (!"".equals(floor.getValue()) && !"".equals(ceiling.getValue())) {
            return true;
        } else if ("".equals(floor.getValue()) && "".equals(ceiling.getValue())) {
            return true;
        }
        return !"".equals(ceiling.getValue());
    }

    public boolean isCeilingMatchingEmptyOrFilledValue(TetrisTextFieldV8 floor, TetrisTextFieldV8 ceiling) {
        if (!"".equals(floor.getValue()) && !"".equals(ceiling.getValue())) {
            return true;
        } else if ("".equals(floor.getValue()) && "".equals(ceiling.getValue())) {
            return true;
        }
        return !"".equals(floor.getValue());
    }

    public PricingMultiProductFilterDTO createMultiProductFilterDTO(PricingMultiProductOverrideDTOWrapper dto) {
        if (dto.getPricingMode().equals(MultiProductEnum.INVESTIGATOR)) {
            return new PricingMultiProductFilterDTO(
                    dto.getInvestigatorDto().getWrapper().getCpDecisionBAROutput().getProduct(),
                    dto.getInvestigatorDto().getWrapper().getAccomClass(),
                    dto.getInvestigatorDto().getWrapper().getAccomType());
        } else {
            return new PricingMultiProductFilterDTO(
                    dto.getPricingAgileRatesOverrideDTO().getProduct(),
                    dto.getPricingAgileRatesOverrideDTO().getRoomClass(),
                    dto.getPricingAgileRatesOverrideDTO().getRoomType());
        }
    }

    public List<Product> getProductsUsingDTO(PricingMultiProductOverrideDTOWrapper dto) {
        CPBARDecisionUIWrapper wrapper = getCPBARDecisionUIWrapperForSelectedDate(dto.getLocalDateFromDTO());
        if (wrapper != null) {
            List<CPOverrideWrapper> cpOverrideWrappers = wrapper.getCpOverrideWrappers();
            return cpOverrideWrappers.stream().map(dateWrapper -> dateWrapper.getCpDecisionBAROutput().getProduct()).distinct().sorted(Comparator.comparing(Product::getDisplayOrder)).collect(Collectors.toList());
        } else {
            return new ArrayList<>();
        }
    }

    public List<AccomClass> getRoomClassesUsingDTO(PricingMultiProductOverrideDTOWrapper dto, Product comparedProduct) {
        CPBARDecisionUIWrapper wrapper = getCPBARDecisionUIWrapperForSelectedDate(dto.getLocalDateFromDTO());
        if (wrapper != null) {
            List<CPOverrideWrapper> cpOverrideWrappers = wrapper.getCpOverrideWrappers();
            return cpOverrideWrappers.stream()
                    .filter(item -> item.getCpDecisionBAROutput().getProduct().equals(comparedProduct))
                    .map(CPOverrideWrapper::getAccomClass)
                    .sorted(Comparator.comparing(AccomClass::getViewOrder))
                    .distinct().collect(Collectors.toList());
        } else {
            return new ArrayList<>();
        }
    }

    public List<AccomType> getRoomTypesUsingDTO(PricingMultiProductOverrideDTOWrapper dto, Product comparedProduct) {
        CPBARDecisionUIWrapper wrapper = getCPBARDecisionUIWrapperForSelectedDate(dto.getLocalDateFromDTO());
        if (wrapper != null) {
            List<CPOverrideWrapper> cpOverrideWrappers = wrapper.getCpOverrideWrappers();
            return cpOverrideWrappers.stream()
                    .filter(item -> item.getCpDecisionBAROutput().getProduct().equals(comparedProduct))
                    .map(CPOverrideWrapper::getAccomType)
                    .sorted(Comparator.comparing(AccomType::getAccomClassViewOrder))
                    .distinct().collect(Collectors.toList());
        } else {
            return new ArrayList<>();
        }
    }

    public boolean hasUnsavedChanges() {
        return hasPendingChanges() && !isEffectiveReadOnly() && isWriteAuthorizedForUser(Sets.newHashSet(TetrisPermissionKey.PRICING));
    }

    PricingToggleView getViewingPreference() {
        PricingToggleView toReturn;
        try {
            LDAPUser user = userService.getById(uiContext.getWorkContextType().getUserId());
            String viewingPreference = user.getViewingPreference();
            if ("tabular".equals(viewingPreference)) {
                toReturn = PricingToggleView.GRID;
            } else {
                toReturn = PricingToggleView.CARD;
            }
        } catch (Exception e) {
            toReturn = PricingToggleView.CARD;
        }
        return toReturn;
    }

    @ForTesting
    public void setOptimizationLevel(OptimizationLevel optimizationLevel) {
        this.optimizationLevel = optimizationLevel;
    }

    public Predicate<AccomType> accomTypesFiltrationCriteria() {
        return overrideManager.getAccomTypesFiltrationCriteria();
    }

    public boolean isPerPersonPricingEnabled() {
        return isPerPersonPricingEnabled;
    }

    public boolean isChildAgeBucketsEnabled() {
        return isChildAgeBucketsEnabled;
    }

    public boolean isChildAgeBucketPackagesEnabled() {
        return isChildAgeBucketPackagesEnabled;
    }

    @ForTesting
    public void setChildAgeBucketPackagesEnabled(boolean childAgeBucketPackagesEnabled) {
        isChildAgeBucketPackagesEnabled = childAgeBucketPackagesEnabled;
    }

    @ForTesting
    public void setPerPersonPricingEnabled(boolean perPersonPricingEnabled) {
        isPerPersonPricingEnabled = perPersonPricingEnabled;
    }

    @ForTesting
    public void setChildAgeBucketsEnabled(boolean childAgeBucketsEnabled) {
        isChildAgeBucketsEnabled = childAgeBucketsEnabled;
    }

    @ForTesting
    public List<PricingAgileRatesOverrideDTO> getMultiProductNonOptimizedDTOs() {
        return multiProductNonOptimizedDTOs;
    }

    @ForTesting
    public List<PricingAgileRatesOverrideDTO> getMultiProductOptimizedDTOs() {
        return multiProductOptimizedDTOs;
    }

    @ForTesting
    public List<CPOverrideWrapper> getMultiProductBARDTOs() {
        return multiProductBARDTOs;
    }

    @ForTesting
    public List<PricingMultiProductOverrideDTOWrapper> getMultiProductAgileRatesDTOs() {
        return multiProductAgileRatesDTOs;
    }

    @ForTesting
    public void setMultiProductNonOptimizedDTOs(List<PricingAgileRatesOverrideDTO> multiProductNonOptimizedDTOs) {
        this.multiProductNonOptimizedDTOs = multiProductNonOptimizedDTOs;
    }

    @ForTesting
    public void setMultiProductOptimizedDTOs(List<PricingAgileRatesOverrideDTO> multiProductOptimizedDTOs) {
        this.multiProductOptimizedDTOs = multiProductOptimizedDTOs;
    }

    @ForTesting
    public void setMultiProductBARDTOs(List<CPOverrideWrapper> multiProductBARDTOs) {
        this.multiProductBARDTOs = multiProductBARDTOs;
    }

    @ForTesting
    public void setMultiProductAgileRatesDTOs(List<PricingMultiProductOverrideDTOWrapper> multiProductAgileRatesDTOs) {
        this.multiProductAgileRatesDTOs = multiProductAgileRatesDTOs;
    }

    public boolean getOptimizedSaveEnabledStatus(PricingAgileRatesOverrideDTO dto) {
        return UiUtils.isEnabledPerRequirements(isEffectiveReadOnly(), TetrisPermissionKey.PRICING) && (optimizedProductHasChanges(dto) || hasMultiProductUnsavedChanges());
    }

    public boolean getNonOptimizedSaveEnabledStatus(PricingAgileRatesOverrideDTO dto) {
        return UiUtils.isEnabledPerRequirements(isEffectiveReadOnly(), TetrisPermissionKey.PRICING) && dto.getFinalPrice() != null && (nonOptimizedProductHasChanges(dto) || hasMultiProductUnsavedChanges());
    }

    public boolean optimizedProductHasChanges(PricingAgileRatesOverrideDTO dto) {
        return (dto.getFloorOffset() != null && dto.getOriginalFloorOffset() == null) ||
                (dto.getCeilingOffset() != null && dto.getOriginalCeilingOffset() == null) ||
                !BigDecimalUtil.equals(dto.getFloorOffset(), dto.getOriginalFloorOffset()) ||
                !BigDecimalUtil.equals(dto.getCeilingOffset(), dto.getOriginalCeilingOffset()) ||
                dto.isFloorMarkedForDeletion() || dto.isCeilingMarkedForDeletion();
    }

    public boolean nonOptimizedProductHasChanges(PricingAgileRatesOverrideDTO dto) {
        return (dto.getAdjustment() != null && dto.getOriginalAdjustment() == null) ||
                !BigDecimalUtil.equals(dto.getAdjustment(), dto.getOriginalAdjustment()) ||
                dto.isAdjustmentMarkedForDeletion()
                ||dto.isInventoryLimitMarkedForDeletion()
                ||(null != dto.getInventoryLimitOverride() && !Objects.equals(dto.getInventoryLimitOverride(), dto.getOriginalInventoryLimitOverride()));
    }

    public boolean hasMultiProductUnsavedChanges() {
        return multiProductUnsavedChanges;
    }

    @ForTesting
    public void setMultiProductUnsavedChanges(boolean multiProductUnsavedChanges) {
        this.multiProductUnsavedChanges = multiProductUnsavedChanges;
    }

    public PricingMultiProductMultidayFilterDTO createMultiProductMultidayFilterDTO() {
        java.time.LocalDate startDate = getJavaTimeLocalDateFromJodaTime(getRangeStartDate());
        java.time.LocalDate endDate = getJavaTimeLocalDateFromJodaTime(getCpPricingFilter().getEndDate());
        Set<Product> products = new HashSet<>(getCpPricingFilter().getProducts());
        List<Product> productGroupProductsToRemove = new ArrayList<>();
        boolean areProductsFiltered = false;
        List<Product> optimizedProducts = products.stream().filter(Product::isOptimized).collect(Collectors.toList());
        optimizedProducts.forEach(product -> {
            if (!productGroupProductsToRemove.contains(product)) {
                List<ProductGroup> relatedProductGroups = findAllProductsInProductGroup(product);
                boolean isCentrallyManagedProductInPG = relatedProductGroups.stream().anyMatch(pg -> pg.getProduct().isCentrallyManaged());
                boolean isNonOverridableProductInPG = relatedProductGroups.stream().anyMatch(pg -> pg.getProduct().getIsOverridable().equals(OverridableProductEnum.NO_OVERRIDES));
                if ((isCentrallyManagedProductInPG && !hasCentrallyManagedPermission()) || isNonOverridableProductInPG) {
                    productGroupProductsToRemove.addAll(relatedProductGroups.stream().map(ProductGroup::getProduct).collect(Collectors.toList()));
                }
            }
        });
        areProductsFiltered = products.stream().anyMatch(p -> p.getIsOverridable().equals(OverridableProductEnum.NO_OVERRIDES) || (p.isCentrallyManaged() && !hasCentrallyManagedPermission()));
        products.removeIf(p -> p.getIsOverridable().equals(OverridableProductEnum.NO_OVERRIDES) || (p.isCentrallyManaged() && !hasCentrallyManagedPermission()) || productGroupProductsToRemove.contains(p));
        return new PricingMultiProductMultidayFilterDTO(products,
                getRoomClassesForDateRange(), getRoomTypesForDateRange(),
                getCpPricingFilter().getSelectedDaysOfWeek(),
                startDate, endDate,
                getCpPricingFilter().getSelectedRoomClass(), !productGroupProductsToRemove.isEmpty(), areProductsFiltered);
    }

    public List<PricingMultidayGridDTO> createMultiProductMultidayGridDTOs(PricingMultiProductMultidayFilterDTO dto) {
        Set<Product> productsExcludingBAR = dto
                .getProducts()
                .stream()
                .filter(item -> !item.isSystemDefaultOrIndependentProduct())
                .collect(Collectors.toSet());

        List<PricingMultidayGridDTO> barGridDTO = createMultidayGridBARDTOs();

        //Find Product -> Find Accom Types Associated with them -> Find Accom Class Associated with it
        List<PricingMultidayGridDTO> dtoList = new ArrayList<>(barGridDTO);

        dtoList.addAll(createMultidayGridIndependentProductDTOs());

        productsExcludingBAR.forEach(product -> dtoList.addAll(createAgileRatesMultidayGridDTOs(product)));
        dtoList.sort((dto1, dto2) -> {
            int productCompare = dto1.getProduct().getDisplayOrder().compareTo(dto2.getProduct().getDisplayOrder());
            int accomClassCompare = dto1.getRoomClass().getViewOrder().compareTo(dto2.getRoomClass().getViewOrder());
            if (productCompare == 0) {
                return (accomClassCompare == 0) ? productCompare : accomClassCompare;
            } else {
                return productCompare;
            }
        });
        return dtoList;
    }

    private List<PricingMultidayGridDTO> createMultidayGridBARDTOs() {
        List<PricingMultidayGridDTO> dtoList = new ArrayList<>();
        Product barProduct = allProducts.stream().filter(Product::isSystemDefault).findFirst().orElse(null);
        List<AccomType> accomTypes = results.stream().flatMap(wrapper -> wrapper.getCpOverrideWrappers().stream()).map(dto -> dto.getCpDecisionBAROutput().getAccomType()).distinct().collect(Collectors.toList());
        accomTypes = getSortedAccomTypes(getCpPricingFilter().getSelectedRoomClass(), accomTypes);
        accomTypes.forEach(accomType -> {
            dtoList.add(new PricingMultidayGridDTO(barProduct, accomType.getAccomClass(), accomType, null, getMultiProductMultidayRowType(barProduct)));
        });
        return dtoList;
    }


    private List<PricingMultidayGridDTO> createMultidayGridIndependentProductDTOs() {
        List<PricingMultidayGridDTO> dtoList = new ArrayList<>();
        List<Product> independentProducts = allProducts
                .stream()
                .filter(Product::isIndependentProduct)
                .collect(Collectors.toList());

        independentProducts.forEach(product -> {
            List<AccomType> relatedAccomTypes = results
                    .stream()
                    .flatMap(wrapper -> wrapper.getCpOverrideWrappers().stream())
                    .filter(wrapper -> wrapper.getProduct().getId().equals(product.getId()))
                    .map(dto -> dto.getCpDecisionBAROutput().getAccomType())
                    .distinct()
                    .collect(Collectors.toList());
            relatedAccomTypes = getSortedAccomTypes(getCpPricingFilter().getSelectedRoomClass(), relatedAccomTypes);
            relatedAccomTypes.forEach(accomType -> {
                dtoList.add(new PricingMultidayGridDTO(product, accomType.getAccomClass(), accomType, null, getMultiProductMultidayRowType(product)));
            });
        });

        return dtoList;
    }

    private MultiProductMultidayRowType getMultiProductMultidayRowType(Product product) {
        if (product.isSystemDefault()) {
            return MultiProductMultidayRowType.BAR;
        } else if (product.isIndependentProduct()) {
            return MultiProductMultidayRowType.INDEPENDENT_PRODUCT;
        } else if (product.isOptimized()) {
            return MultiProductMultidayRowType.AGILE_RATES_OPTIMIZED;
        } else if (product.getOffsetMethod().equals(AgileRatesOffsetMethod.PERCENTAGE)) {
            return MultiProductMultidayRowType.AGILE_RATES_NON_OPTIMIZED_PERCENTAGE;
        } else {
            return MultiProductMultidayRowType.AGILE_RATES_NON_OPTIMIZED_FIXED;
        }
    }

    private List<PricingMultidayGridDTO> createAgileRatesMultidayGridDTOs(Product product) {
        List<PricingMultidayGridDTO> dtoList = new ArrayList<>();
        List<AccomType> accomTypes = results
                .stream()
                .flatMap(wrapper -> wrapper.getCpOverrideWrappers().stream())
                .filter(wrapper -> wrapper.getCpDecisionBAROutput().getProduct().equals(product))
                .map(dto -> dto.getCpDecisionBAROutput().getAccomType())
                .distinct().collect(Collectors.toList());
        if (!accomTypes.isEmpty()) {
            accomTypes = getSortedAccomTypes(getCpPricingFilter().getSelectedRoomClass(), accomTypes);
        }
        accomTypes.forEach(accomType ->
                dtoList.add(
                        new PricingMultidayGridDTO(product, accomType.getAccomClass(),
                                accomType, product.getOffsetMethod(),
                                getMultiProductMultidayRowType(product))));
        return dtoList;
    }

    private List<AccomClass> getRoomClassesForDateRange() {
        List<AccomClass> roomClasses = results
                .stream()
                .flatMap(wrapper -> wrapper.getCpOverrideWrappers().stream())
                .map(CPOverrideWrapper::getAccomClass)
                .sorted(Comparator.comparing(AccomClass::getViewOrder))
                .distinct()
                .collect(Collectors.toList());
        //Include all so I can select it within the filter
        roomClasses.add(0, getAllRoomClasses());
        return roomClasses;
    }

    private Set<AccomType> getRoomTypesForDateRange() {
        return results
                .stream()
                .flatMap(wrapper -> wrapper.getCpOverrideWrappers().stream())
                .map(CPOverrideWrapper::getAccomType)
                .collect(Collectors.toSet());
    }

    public void toggleView() {
        if (PricingToggleView.CARD.equals(selectedView)) {
            this.selectedView = PricingToggleView.GRID;
        } else {
            this.selectedView = PricingToggleView.CARD;
        }

        view.displayView(selectedView);
    }

    public boolean isCardViewSelected() {
        return PricingToggleView.CARD.equals(selectedView);
    }


    public void savePricingFilter(FilterOptions pricingFilter) {
        userFilterOptionsService.saveUserFilterOptions(pricingFilter);
    }

    public FilterOptions getSavedFilterSettings(String view) {
        return userFilterOptionsService.getUserFilterOptionsByView(view);
    }

    public boolean getHighestBarRestrictedEnabled() {
        return isHighestBarRestrictedEnabled;
    }

    @ForTesting
    public void setHighestBarRestrictedEnabled(boolean highestBarRestrictedEnabled) {
        isHighestBarRestrictedEnabled = highestBarRestrictedEnabled;
    }

    public void addPendingCloseLv0BAROverride(BAROverride override) {
        pendingCloseLv0BarOverrides.add(override);
        enableSaveCancelButtons(true);
    }

    void saveLV0Overrides() {
        List<BAROverride> lv0Overrides = getPendingOverridesToSave();
        overrideService.saveBAROverrides(lv0Overrides);
        pendingCloseLv0BarOverrides.clear();
    }

    private List<BAROverride> getPendingOverridesToSave() {
        return pendingCloseLv0BarOverrides.stream().filter(b -> (b.isRestrictHighestBarEnabled() || b.isRemoveRestrictHighestBarOverride())).collect(Collectors.toList());
    }

    public void applyLV0Override(List<CloseLV0RowModel> lv0GridRowModels, List<CloseLV0RowModel> sourceCloseLv0GridModels, AccomClass selectedAccomClass, boolean isRemoveOverride, List<AccomType> accomTypes, LocalDate date) {
        for (String losValue : createLOSListForCloseLV0()) {
            if (selectedAccomClass.getId() == ALL_ROOM_CLASS_ID) {
                applyOverrideChangesForAllRoomClass(lv0GridRowModels, sourceCloseLv0GridModels, isRemoveOverride, date, losValue);
            } else {
                applyOverrideChangesForFilteredRoomClass(lv0GridRowModels, sourceCloseLv0GridModels, isRemoveOverride, date, losValue, selectedAccomClass);
            }
        }
    }

    private void applyOverrideChangesForSpecificRoomClass(List<CloseLV0RowModel> lv0GridRowModels, List<CloseLV0RowModel> sourceCloseLv0GridModels, AccomClass selectedAccomClass, boolean isRemoveOverride, List<AccomType> accomTypes, LocalDate date, String losValue) {
        List<AccomTypeSummary> allAccomTypeSummaryList = getAccomTypeSummaryListForLos(losValue, lv0GridRowModels, sourceCloseLv0GridModels, accomTypes, isRemoveOverride);
        if (!allAccomTypeSummaryList.isEmpty()) {
            createAndAddPendingCloseLv0Override(isRemoveOverride, date, losValue, selectedAccomClass, allAccomTypeSummaryList);
        }
    }

    private void applyOverrideChangesForFilteredRoomClass(List<CloseLV0RowModel> lv0GridRowModels, List<CloseLV0RowModel> sourceCloseLv0GridModels, boolean isRemoveOverride, LocalDate date, String losValue, AccomClass selectedAccomClass) {
        List<AccomType> accomTypes = filterAndGetRoomTypesForRoomClass(selectedAccomClass);
        applyOverrideChangesForSpecificRoomClass(lv0GridRowModels, sourceCloseLv0GridModels, selectedAccomClass, isRemoveOverride, accomTypes, date, losValue);
    }

    private void applyOverrideChangesForAllRoomClass(List<CloseLV0RowModel> lv0GridRowModels, List<CloseLV0RowModel> sourceCloseLv0GridModels, boolean isRemoveOverride, LocalDate date, String losValue) {
        for (PricingAccomClass pricingAccomClass : pricingAccomClasses) {
            List<AccomType> accomTypes = filterAndGetRoomTypesForRoomClass(pricingAccomClass.getAccomClass());
            applyOverrideChangesForSpecificRoomClass(lv0GridRowModels, sourceCloseLv0GridModels, pricingAccomClass.getAccomClass(), isRemoveOverride, accomTypes, date, losValue);
        }
    }

    private void createAndAddPendingCloseLv0Override(boolean isRemoveOverride, LocalDate date, String losValue, AccomClass accomClass, List<AccomTypeSummary> allAccomTypeSummaryList) {
        BAROverride override = createOverrideForCloseLv0(date, accomClass, losValue, isRemoveOverride);
        setAccomTypeSummaryListForCloseLv0Override(override, allAccomTypeSummaryList);
        addPendingCloseLv0BAROverride(override);
    }

    private void setAccomTypeSummaryListForCloseLv0Override(BAROverride override, List<AccomTypeSummary> allAccomTypeSummaryList) {
        override.setAccomTypeSummaryList(new ArrayList<>(allAccomTypeSummaryList));
        override.setRestrictHighestBarEnabled(false);
        for (AccomTypeSummary accomTypeSummary : allAccomTypeSummaryList) {
            if (accomTypeSummary.isSelected()) {
                override.setRestrictHighestBarEnabled(true);
            }
        }
    }

    private List<AccomTypeSummary> getAccomTypeSummaryListForLos(String losValue, List<CloseLV0RowModel> modelList, List<CloseLV0RowModel> sourceModelList, List<AccomType> roomTypesForSelectedRoomClass, boolean isRemoveOverride) {
        int los = getLosValueFromRowHeaderString(losValue);
        List<AccomTypeSummary> accomTypeSummaryList = new ArrayList<>();
        for (AccomType roomType : roomTypesForSelectedRoomClass) {
            boolean value = modelList.get(los - 1).getAccomTypesSelected().contains(roomType);
            AccomTypeSummary accomTypeSummary = new AccomTypeSummary(roomType.getId(), roomType.getName(), value);
            if (sourceModelList.isEmpty()) {
                if (value != isRemoveOverride) {
                    accomTypeSummaryList.add(accomTypeSummary);
                }
            } else {
                boolean sourceValue = sourceModelList.get(los - 1).getAccomTypesSelected().contains(roomType);
                if ((value != sourceValue) && (value != isRemoveOverride)) {
                    accomTypeSummaryList.add(accomTypeSummary);
                }
            }
        }
        return accomTypeSummaryList;
    }

    public List<String> createLOSListForCloseLV0() {
        List<String> los = new ArrayList<>();
        IntStream.range(1, getMaxLOSForLV0() + 1).forEach(integer -> los.add("LOS" + integer));
        return los;
    }

    public List<AccomType> getAllRoomTypes() {
        List<AccomType> allAccomTypeList = new ArrayList<>();
        for (PricingAccomClass accomClass : pricingAccomClasses) {
            List<AccomType> accomTypes = filterAndGetRoomTypesForRoomClass(accomClass.getAccomClass());
            allAccomTypeList.addAll(accomTypes);
        }
        return allAccomTypeList;
    }

    private List<AccomType> filterAndGetRoomTypesForRoomClass(AccomClass accomClass) {
        return accomClass.filterAndGetAccomTypes(this.accomTypesFiltrationCriteria());
    }

    public int getMaxLOSForLV0() {
        if (qualifiedFplosMaxLos != null) {
            return getExtendedStayValue(qualifiedFplosMaxLos);
        }
        return getExtendedStayValue(7);
    }

    private int getExtendedStayValue(Integer qualifiedFplosMaxLos) {
        return isUseExtendedStayMapping ? qualifiedFplosMaxLos - 1 : qualifiedFplosMaxLos;
    }

    @ForTesting
    public void setQualifiedFplosMaxLos(Integer qualifiedFplosMaxLos) {
        this.qualifiedFplosMaxLos = qualifiedFplosMaxLos;
    }

    @ForTesting
    public void setUseExtendedStayMapping(boolean useExtendedStayMapping) {
        isUseExtendedStayMapping = useExtendedStayMapping;
    }

    private BAROverride createOverrideForCloseLv0(LocalDate currentDate, AccomClass accomClass, String losValue, boolean isRemoveRestrictHighestBar) {
        final BAROverride override = new BAROverride();
        override.setAccomClassId(accomClass.getId());
        override.setLengthOfStay(Integer.valueOf(losValue.substring(losValue.length() - 1)));
        override.setArrivalDate(currentDate.toDateTimeAtStartOfDay().toDate());
        override.setRestrictHighestBarEnabled(!isRemoveRestrictHighestBar);
        override.setRemoveRestrictHighestBarOverride(isRemoveRestrictHighestBar);
        return override;
    }

    public boolean isCloseLv0OverridePresent(PricingMultiProductMultidayFilterDTO dto) {
        Stream<CPOverrideWrapper> cpOverrideWrapperStream = results.stream()
                .filter(wrapper -> !wrapper.getCpbarDecisionDTO().getDate().isBefore(new LocalDate(dto.getStartDate().toString())) &&
                        !wrapper.getCpbarDecisionDTO().getDate().isAfter(new LocalDate(dto.getEndDate().toString())) &&
                        dto.getDaysOfWeek().contains(DayOfWeek.valueOf(wrapper.getCpbarDecisionDTO().getDate().getDayOfWeek())))
                .flatMap(wrapper -> wrapper.getCpOverrideWrappers().stream());
        return cpOverrideWrapperStream.anyMatch(CPOverrideWrapper::getLv0Override);
    }

    public void openInvestigatorDetailsWindow(CPBARDecisionUIWrapper cpbarDecisionUIWrapper) {
        CPBARDecisionDTO dto = cpbarDecisionUIWrapper.getCpbarDecisionDTO();

        InvestigatorDto investigatorDto = new InvestigatorDto();
        investigatorDto.setModuleOrigin(TetrisPermissionKey.PRICING);
        investigatorDto.setOverride(false);
        investigatorDto.setSelectedDate(dto.getDate());
        investigatorDto.setStartDate(getCpPricingFilter().getStartDate());
        investigatorDto.setEndDate(getCpPricingFilter().getEndDate());
        investigatorDto.setSelectedRoomClasses(getSelectedAccomClassesBasedOnSearchCriteria());
        List<Integer> selectedRoomTypeIds = getOrderedAndFilteredTypes().stream().map(AccomType::getId).collect(Collectors.toList());
        investigatorDto.setSelectedRoomTypes(selectedRoomTypeIds);
        AccomClass selectedAccomClass = getSelectedRoomClassForInvestigatorPopUp();
        investigatorDto.setSelectedAccomClassId(selectedAccomClass.getId());
        AccomType baseRoomTypeForRoomClass = getBaseRoomTypeForRoomClass(selectedAccomClass);
        investigatorDto.setSelectedAccomTypeId(null != baseRoomTypeForRoomClass ? baseRoomTypeForRoomClass.getId() : -1);
        if (!selectedRoomTypeIds.contains(investigatorDto.getSelectedAccomTypeId())) {
            investigatorDto.setSelectedAccomTypeId(selectedRoomTypeIds.get(0));
        }
        boolean showOverrideTab = false;
        Map<LocalDate, List<InvestigatorDto>> investigatorDtoByDate = getInvestigatorDtosByDate(showOverrideTab);
        investigatorDto.setOverrides(investigatorDtoByDate);
        InvestigatorUtil.openPopUpForCP(investigatorDto, getOverridePopUpViewFactory());
    }

    public List<BAROverride> getPendingCloseLv0BarOverrides() {
        return pendingCloseLv0BarOverrides;
    }


    public boolean areFloorAndCeilingValid(BigDecimal floorOverride, BigDecimal ceilingOverride) {
        return ceilingOverride == null || !BigDecimalUtil.isGreaterThan(floorOverride, ceilingOverride);
    }

    public boolean areFloorAndCeilingWithinValidRanges(BigDecimal floorOverride, BigDecimal ceilingOverride, boolean nonOptimizedFixedProduct) {
        return nonOptimizedFixedProduct || BigDecimalUtil.isLessThan(BigDecimal.valueOf(-100), floorOverride) &&
                BigDecimalUtil.isLessThan(BigDecimal.valueOf(-100), ceilingOverride) &&
                BigDecimalUtil.isLessThan(floorOverride, BigDecimal.valueOf(Integer.MAX_VALUE)) &&
                BigDecimalUtil.isLessThan(ceilingOverride, BigDecimal.valueOf(Integer.MAX_VALUE));
    }

    public boolean isSpecificProductBetweenBounds(BigDecimal value, BigDecimal lowerBound, boolean nonOptimizedFixedProduct) {
        return nonOptimizedFixedProduct ? BigDecimalUtil.isLessThan(BigDecimal.valueOf(Integer.MIN_VALUE), value) &&
                BigDecimalUtil.isLessThan(value, BigDecimal.valueOf(Integer.MAX_VALUE)) :
                BigDecimalUtil.isLessThan(lowerBound, value) &&
                        BigDecimalUtil.isLessThan(value, BigDecimal.valueOf(Integer.MAX_VALUE));
    }

    public List<LocalDate> getValidDaysForProductAndSelectedRoomType(Product product, AccomType roomType) {
        return results
                .stream()
                .flatMap(item -> item.getCpOverrideWrappers().stream())
                .filter(item -> item.getCpDecisionBAROutput().getProduct().equals(product))
                .filter(item -> item.getAccomType().equals(roomType))
                .map(item -> item.getCpDecisionBAROutput().getArrivalDate())
                .sorted(Comparator.naturalOrder())
                .collect(Collectors.toList());
    }

    public void setCompetitorDetailsRows(List<WebrateCompetitors> selectedCompetitors) {
        //Only get Competitor Details if BAR is selected
        if (getCpPricingFilter().isBARProductSelected()) {
            List<PricingCompetitorRates> competitorRatesForBar = getCompetitorRates(selectedCompetitors);
            List<CompetitorDetailsRow> competitorDetailsRowsList = new ArrayList<>();

            HashMap<String, String> competitorAliasMap = new HashMap<>();
            HashMap<String, Integer> competitorIDNameMap = new HashMap<>();
            for (WebrateCompetitors webrate : selectedCompetitors) {
                competitorAliasMap.put(webrate.getWebrateCompetitorsName(), webrate.getWebrateCompetitorsAlias());
                competitorIDNameMap.put(webrate.getWebrateCompetitorsAlias(), webrate.getId());
            }

            if (competitorRatesForBar != null) {
                for (PricingCompetitorRates competitorPrice : competitorRatesForBar) {
                    competitorDetailsRowsList.addAll(createCompetitorDetailsRowList(competitorPrice, competitorAliasMap, competitorIDNameMap));
                }
            }

            this.allCompetitorDetailsRows = competitorDetailsRowsList;
        }
    }

    public void setAllCompetitorDetailsRows(List<WebrateCompetitors> selectedCompetitors) {
        if (getCpPricingFilter().isBARProductSelected() || getCpPricingFilter().isIndependentProductSelected()
                || getCpPricingFilter().isAgileRateProductSelected()) {
            if (CollectionUtils.isEmpty(selectedCompetitors)) {
                this.allVwCompetitorDetailsRows = new ArrayList<>();
            } else {
                List<Integer> selectedCompetitorsIDs = selectedCompetitors
                        .stream()
                        .map(WebrateCompetitors::getId)
                        .collect(Collectors.toList());
                List<WebrateView> webrateViewForProductsInDateRange = isRDLEnabled ? service.getWebrateViewV2ForProductsInDateRangeForRDLProperties(getCpPricingFilter().getSelectedProductIds(),
                        selectedCompetitorsIDs,
                        JavaLocalDateUtils.toJavaLocalDate(getCpPricingFilter().getStartDate()),
                        JavaLocalDateUtils.toJavaLocalDate(getCpPricingFilter().getEndDate()))
                        : service.getWebrateViewForProductsInDateRange(
                        getCpPricingFilter().getSelectedProductIds(),
                        selectedCompetitorsIDs,
                        getCpPricingFilter().getStartDate(),
                        getCpPricingFilter().getEndDate());

                this.allVwCompetitorDetailsRows = webrateViewForProductsInDateRange;
            }
        }
    }

    private List<CompetitorDetailsRow> createCompetitorDetailsRowList(PricingCompetitorRates competitorRatesForDay, HashMap<String, String> compAliasMap, HashMap<String, Integer> competitorIDNameMap) {
        List<CompetitorDetailsRow> rows = new ArrayList<>();
        List<String> compNames = competitorRatesForDay.getCompNames();
        List<BigDecimal> compRates = competitorRatesForDay.getCompRates();
        List<String> compStatuses = competitorRatesForDay.getCompStatuses();

        for (int i = 0; i < compNames.size(); i++) {
            String compName = compNames.get(i);
            if (StringUtils.isBlank(compName)) {
                break;
            }

            String compStatus = compStatuses.get(i) != null ? compStatuses.get(i) : NULL_STRING;
            String compRate = compRates.get(i) != null ? compRates.get(i).toString() : NULL_STRING;

            CompetitorDetailsRow newRow = new CompetitorDetailsRow();
            // should store their alias
            newRow.setCompetitorName(compAliasMap.get(compName));

            //This is the same logic used in the BARDecisionService
            if (compStatus.equals(Constants.WEBRATE_ACTIVE_STATUS_CODE)) {
                newRow.setRate(compRate);
            } else if (compStatus.equals(Constants.WEBRATE_CLOSED_STATUS_CODE)) {
                newRow.setRate(Constants.WEBRATE_CLOSED_STATUS);
            } else if (compStatus.equals(Constants.WEBRATE_FENCED_STATUS_CODE)) {
                newRow.setRate(Constants.WEBRATE_RATE_RESTRICTED_STATUS);
            } else {
                newRow.setRate(compStatus);
            }
            newRow.setCompetitorID(competitorIDNameMap.get(newRow.getCompetitorName()));
            newRow.setOccupancyDate(competitorRatesForDay.getArrivalDate());
            newRow.setPropertyAccomClassName(competitorRatesForDay.getAccomClassName());
            newRow.setPropertyRoomTypeName(competitorRatesForDay.getAccomTypeName());
            rows.add(newRow);
        }

        return rows;
    }

    public List<WebrateCompetitors> getWebrateCompetitors() {
        if (CollectionUtils.isEmpty(this.webrateCompetitors)) {
            this.webrateCompetitors = pricingConfigurationService.getWebrateCompetitors();
        }
        return this.webrateCompetitors;
    }

    public List<PricingCompetitorRates> getCompetitorRates(List<WebrateCompetitors> selectedCompetitors) {
        List<Integer> competitorIDs = selectedCompetitors.stream()
                .map(WebrateCompetitors::getId)
                .collect(Collectors.toList());

        //The function requires 7 competitor IDs, so if needed add -1 to get to a total of 7
        while (competitorIDs.size() < 7) {
            competitorIDs.add(-1);
        }

        StringBuilder accomTypesIDString = new StringBuilder();
        StringBuilder accomClassIDString = new StringBuilder();

        CPPricingFilter filter = getCpPricingFilter();
        if (filter.getSelectedRoomClass().getId() == -1) {
            for (AccomType accomType : getBaseRoomTypeList()) {
                accomTypesIDString.append(accomType.getId()).append(",");
            }
            for (AccomClass accomClass : getRoomClassListExcludingAll()) {
                accomClassIDString.append(accomClass.getId()).append(",");
            }
        } else if (filter.getSelectedRoomClass().getId() != -1
                && !filter.getSelectedRoomTypes().isEmpty()) {
            accomClassIDString.append(filter.getSelectedRoomClass().getId());
            for (AccomType accomType : filter.getSelectedRoomTypes()) {
                accomTypesIDString.append(accomType.getId()).append(",");
            }
        } else {
            accomClassIDString.append(filter.getSelectedRoomClass().getId());
            for (AccomType accomType : filter.getSelectedRoomClass().getAccomTypes()) {
                accomTypesIDString.append(accomType.getId()).append(",");
            }
        }

        return service.getPricingCompetitorRates(getCpPricingFilter().getStartDate(), getCpPricingFilter().getEndDate(),
                accomClassIDString.toString(), accomTypesIDString.toString(), competitorIDs);
    }

    public boolean isPriceAsSumOfPartsEnabledFor(AccomType accomType) {
        if (!accomType.isComponentRoom()) {
            return false;
        }
        return isPriceAsSumOfPartsEnabledFor(accomType.getAccomClass());
    }

    public boolean isPriceAsSumOfPartsEnabledFor(AccomClass accomClass) {
        return pricingAccomClasses.stream()
                .filter(pac -> pac.getAccomClass().equals(accomClass))
                .anyMatch(PricingAccomClass::isPriceAsSumOfParts);
    }

    public boolean valueLessThanZero(String value) {
        return org.apache.commons.lang.math.NumberUtils.isNumber(value) && new BigDecimal(value).compareTo(BigDecimal.ZERO) < 0;
    }

    public boolean valueEqualToZero(String value) {
        return org.apache.commons.lang.math.NumberUtils.isNumber(value) && BigDecimalUtil.equals(new BigDecimal(value), BigDecimal.ZERO);
    }

    public boolean valueLessThanOrEqualToZero(String value) {
        return valueLessThanZero(value) || valueEqualToZero(value);
    }

    public boolean valueLessThanMax(String value) {
        return org.apache.commons.lang.math.NumberUtils.isNumber(value) && new BigDecimal(value).compareTo(new BigDecimal(999999999)) < 0;
    }

    public BigDecimal getPrettyPricingAfterRemove(CPOverrideWrapper wrapper, CPDecisionContext cpDecisionContext) {
        return overrideManager.getPrettyPriceAfterRemove(cpDecisionContext,
                wrapper);
    }


    public ProductRateOffsetOverride getAgileRateOffsetSpecificOverride(CPOverrideWrapper wrapper, AccomClass accomClass) {
        ProductRateOffsetOverride offsetOverrides = getProductRateOffsetOverrides().stream()
                .filter(dto -> dto.getProduct().equals(wrapper.getCpDecisionBAROutput().getProduct()))
                .filter(dto -> dto.getAccomClass().equals(accomClass))
                .filter(dto -> dto.getOccupancyDate().equals(wrapper.getCpDecisionBAROutput().getArrivalDate()))
                .findFirst()
                .orElse(null);
        return offsetOverrides != null ? offsetOverrides : new ProductRateOffsetOverride();
    }

    public ProductRateOffsetOverride getAgileRateOffsetOverride(CPOverrideWrapper wrapper, Product product, AccomClass accomClass) {
        return getProductRateOffsetOverrides().stream()
                .filter(dto -> dto.getProduct().equals(product))
                .filter(dto -> dto.getOccupancyDate().equals(wrapper.getCpDecisionBAROutput().getArrivalDate()))
                .filter(dto -> dto.getAccomClass().equals(accomClass))
                .findFirst()
                .orElse(null);
    }

    public boolean isUserOverrideBetweenCeilingAndFloor(CPOverrideWrapper overrideWrapper, BigDecimal specificOverride) {
        BigDecimal upperBoundOverride;
        BigDecimal lowerBoundOverride;

        upperBoundOverride = specificOverride;
        lowerBoundOverride = specificOverride;

        if (overrideWrapper.getCpDecisionBAROutput() != null) {
            AccomType accomType = overrideWrapper.getCpDecisionBAROutput().getAccomType();
            if (isRoomClassPriceExcluded(accomType.getAccomClass())) {
                return true;
            }
            return cpManagementService.isPriceBetweenFloorAndCeiling(overrideWrapper.getProduct(), overrideWrapper.getCpDecisionBAROutput().getArrivalDate(),
                    accomType,
                    upperBoundOverride, lowerBoundOverride);
        } else {
            if (isRoomClassPriceExcluded(overrideWrapper.getAccomClass())) {
                return true;
            }
            return cpManagementService.isPriceBetweenFloorAndCeilingMultiDay(overrideWrapper.getProduct(), overrideWrapper.getStartDate(),
                    overrideWrapper.getEndDate(),
                    overrideWrapper.getSelectedRoomType(),
                    upperBoundOverride, lowerBoundOverride,
                    overrideWrapper.getDaysOfTheWeek());
        }
    }

    public BigDecimal getSupplementValue(CPDecisionBAROutput barOutput) {
        return accomTypeSupplementService.getSupplementValue(barOutput.getProduct().getId(), barOutput.getArrivalDate(), barOutput.getAccomType().getId());
    }

    public AccomTypeSupplement getAccomTypeSupplement(CPDecisionBAROutput cpDecisionBAROutput) {
        return accomTypeSupplementService.getSupplement(cpDecisionBAROutput);
    }

    public AccomTypeSupplementValue getAccomTypeSupplementValueOf(PricingMultidayGridDTO dto, PricingMultiProductMultidayFilterDTO filterDTO, Map<AccomTypeSupplementValuePK, AccomTypeSupplementValue> supplementValueMap) {
        LocalDate startDate = new LocalDate(filterDTO.getStartDate().toString());
        java.time.LocalDate javaTimeStartDate = java.time.LocalDate.of(startDate.getYear(), startDate.getMonthOfYear(), startDate.getDayOfMonth());
        return accomTypeSupplementService.getAccomTypeSupplementFor(dto.getProduct().getId(), supplementValueMap, javaTimeStartDate, dto.getRoomType().getId(), getBaseOccupancyType());
    }

    public void enableSaveAndCancelButtons(boolean enableWhatIfAndCancelButtons, boolean enableSaveButton) {
        view.enableButtons(enableWhatIfAndCancelButtons, enableSaveButton);
    }

    public void enableSaveAndCancelButtonsWithWhatIf(boolean enableCancelButton, boolean enableSaveButton) {
        view.enableButtons(enableCancelButton, enableSaveButton, enableSaveButton);
    }

    public void saveInlineEditGrid() {
        //Filter to only PricingAgileRatesOverrideDTOs that have been changed.
        Map<String, PricingAgileRatesOverrideDTO> agileRatesFromInlineGrid = view.getAgileRatesFromInlineGrid();
        List<PricingAgileRatesOverrideDTO> agileRatesOverrideDTOs = agileRatesFromInlineGrid.values().stream().collect(Collectors.toList());
        Set<Product> productSet = agileRatesOverrideDTOs.stream().map(PricingAgileRatesOverrideDTO::getProduct).collect(Collectors.toSet());

        List<PricingAgileRatesOverrideDTO> applyAgileRatesDTOList = new ArrayList<>();
        List<PricingAgileRatesOverrideDTO> revertAgileRatesDTOList = new ArrayList<>();

        //Optimized Flow from multiProduct
        agileRatesOverrideDTOs.stream()
                .filter(dto -> dto.getProduct().isOptimized())
                .filter(dto -> dto.getFloorOffset() != null && dto.getCeilingOffset() != null)
                .filter(dto -> (dto.getFloorOffset() != null && dto.getOriginalFloorOffset() == null) ||
                        (dto.getCeilingOffset() != null && dto.getOriginalCeilingOffset() == null) ||
                        !BigDecimalUtil.equals(dto.getFloorOffset(), dto.getOriginalFloorOffset()) ||
                        !BigDecimalUtil.equals(dto.getCeilingOffset(), dto.getOriginalCeilingOffset()) ||
                        dto.isFloorMarkedForDeletion() || dto.isCeilingMarkedForDeletion())
                .forEach(dto -> {
                    if (dto.isFullyReverted()) {
                        revertAgileRatesDTOList.add(dto);
                    } else {
                        applyAgileRatesDTOList.add(dto);
                    }
                });

        //Non-Optimized has changes from multiProduct exclude wrappers to be deleted as the apply will not have the proper flags
        //Flags must be set on the CPOverride wrapper beforehand to have this done
        applyAgileRatesDTOList.addAll(
                agileRatesOverrideDTOs.stream()
                        .filter(dto -> !dto.getProduct().isOptimized())
                        .filter(dto -> dto.getAdjustment() != null)
                        .filter(dto -> (dto.getAdjustment() != null && dto.getOriginalAdjustment() == null) ||
                                !BigDecimalUtil.equals(dto.getAdjustment(), dto.getOriginalAdjustment()))
                        .collect(Collectors.toList()));

        agileRatesOverrideDTOs.stream()
                .filter(dto -> !dto.getProduct().isOptimized())
                .filter(dto -> dto.getOverrideWrapper().isPendingDelete())
                .forEach(dto -> productOverrides.add(dto.getOverrideWrapper()));

        agileRatesInlineEditingSave(productSet, applyAgileRatesDTOList, revertAgileRatesDTOList);

        save();
        view.enableButtons(false, false);
        view.updateUploadEnabled(isManualUploadEnabled(), isPropertyStageTwoWay(), isBarOverridePendingForUpload(), isAgileRatesPendingForUpload(), isInventoryLimitPendingForUpload());
    }

    public void applyBAROverrideWrappersInlineEdit(CPOverrideWrapper overrideWrapper) {
        CPOverrideWrapper wrapper = findWrapper(overrideWrapper);
        wrapper.setStartDate(wrapper.getCpDecisionBAROutput().getArrivalDate());
        wrapper.setEndDate(wrapper.getCpDecisionBAROutput().getArrivalDate());

        if (willApplyRemoveFromBARProduct(overrideWrapper)) {
            //Remove Path
            CPDecisionContext cpDecisionContext = getCpDecisionContext(wrapper.getCpDecisionBAROutput().getArrivalDate(),
                    wrapper.getCpDecisionBAROutput().getArrivalDate(), false);
            PricingAccomClass pricingMasterAccomClass = getMasterPricingAccomClassFromCPDecisionContext(cpDecisionContext);
            applyOverrideChanges(wrapper, cpDecisionContext, pricingMasterAccomClass, false);
        } else {
            //Apply Path
            CPDecisionContext cpDecisionContext = getCpDecisionContext(wrapper.getCpDecisionBAROutput().getArrivalDate(),
                    wrapper.getCpDecisionBAROutput().getArrivalDate(), true);
            PricingAccomClass pricingMasterAccomClass = getMasterPricingAccomClassFromCPDecisionContext(cpDecisionContext);
            applyOverrideChanges(wrapper, cpDecisionContext, pricingMasterAccomClass, false);
        }
    }

    public void setAllDTARanges(List<AgileRatesDTARange> allDTARanges) {
        this.allDTARanges = allDTARanges;
    }

    public PricingAccomClass getMasterPricingAccomClassFromCPDecisionContext(CPDecisionContext cpDecisionContext) {
        if (cpDecisionContext == null) {
            return null;
        }

        Map<AccomClass, PricingAccomClass> pricingAccomClasses = cpDecisionContext.getPricingAccomClasses();
        AccomClass accomClass = pricingAccomClasses.keySet()
                .stream()
                .filter(pac -> pac.getMasterClass() == 1)
                .findFirst()
                .orElse(null);
        if (accomClass != null) {
            return pricingAccomClasses.get(accomClass);
        } else {
            return null;
        }
    }

    public List<Product> findProductsWithSameAgileRatesProductGroup(Product product) {
        if (allProductGroups.isEmpty()) {
            allProductGroups = agileRatesConfigurationService.getProductGroups();
        }
        AgileRatesProductGroup parentGroup = allProductGroups.stream()
                .filter(pg -> pg.getProduct().equals(product))
                .map(ProductGroup::getAgileRatesProductGroup)
                .findFirst().orElse(null);
        return allProductGroups.stream()
                .filter(pg -> pg.getAgileRatesProductGroup().equals(parentGroup))
                .map(ProductGroup::getProduct).collect(Collectors.toList());
    }

    public Set<PricingMultidayGridDTO> findDtosInProductGroup(List<PricingMultidayGridDTO> dtoList, Product product) {
        List<Product> productsWithSameParentGroup = findProductsWithSameAgileRatesProductGroup(product);
        return dtoList.stream().filter(dto -> productsWithSameParentGroup.contains(dto.getProduct())).collect(Collectors.toSet());
    }

    public Map<String, PricingAgileRatesOverrideDTO> findDtosInProductGroup(Map<String, PricingAgileRatesOverrideDTO> dtoMap, Product product) {
        List<Product> productsWithSameParentGroup = findProductsWithSameAgileRatesProductGroup(product);
        Map<String, PricingAgileRatesOverrideDTO> filteredDtoMap = new HashMap<>();
        dtoMap.forEach((key, value) -> {
            if (productsWithSameParentGroup.contains(value.getProduct())) {
                filteredDtoMap.put(key, value);
            }
        });
        return filteredDtoMap;
    }

    @ForTesting
    public void setAllHierarchies(List<ProductHierarchy> allHierarchies) {
        this.allHierarchies = allHierarchies;
    }

    @ForTesting
    public void setAllProductRateOffsets(List<ProductRateOffset> allProductRateOffsets) {
        this.allProductRateOffsets = allProductRateOffsets;
    }

    public Set<AccomClass> isHierarchyValidationValidWithMultiday(BigDecimal floorOverride, BigDecimal ceilingOverride,
                                                                  Product product, List<Integer> selectedDOWs, AccomClass selectedAccomClass,
                                                                  LocalDate startDate, LocalDate endDate) {
        if (allHierarchies.isEmpty()) {
            allHierarchies = agileRatesConfigurationService.getProductHierarchies();
        }
        List<AccomClass> roomClasses = isAgileRatesAndApplyToAllRoomClasses(product) ? findSortedRoomClassesForProduct(product) : asList(selectedAccomClass);
        List<ProductHierarchy> existingHierarchies = allHierarchies.stream().
                filter(p -> p.getToProduct().equals(product) ||
                        p.getFromProduct().equals(product))
                .collect(Collectors.toList());

        Set<AccomClass> invalidHierarchyAccomClasses = new HashSet<>();
        if (!existingHierarchies.isEmpty()) {
            if (allProductRateOffsets.isEmpty()) {
                allProductRateOffsets = agileRatesConfigurationService.findAllProductRateOffsets();
            }
            List<ProductRateOffset> copyProductRateOffsets = deepCopyAllProductRateOffsets(allProductRateOffsets);
            List<AgileRatesDTARange> productDTARanges = getDtaRangesForSelectedDatesMultiday(startDate, endDate, product);

            product.setRoomClassOffset(true);
            product.setDtaOffset(!productDTARanges.isEmpty());
            product.setDowOffset(true);

            for (AgileRatesDTARange dta : productDTARanges) {
                for (AccomClass roomClass : roomClasses) {
                    for (Integer day : selectedDOWs) {
                        List<ProductRateOffset> newOffsets = generateOffsetsForHierarchyValidation(floorOverride, ceilingOverride,
                                day, roomClass, dta, product);
                        List<ProductHierarchy> hierarchiesToRemove = new ArrayList<>();
                        existingHierarchies.forEach(h -> {
                            if ((!h.getToProduct().isSystemDefaultOrIndependentProduct() && !h.getFromProduct().isSystemDefaultOrIndependentProduct())
                                    && (!findSortedRoomClassesForProduct(h.getToProduct()).contains(roomClass)
                                    || !findSortedRoomClassesForProduct(h.getFromProduct()).contains(roomClass))) {
                                hierarchiesToRemove.add(h);
                            }
                        });
                        existingHierarchies.removeAll(hierarchiesToRemove);
                        if (!agileRatesConfigurationService.findInvalidExistingHierarchies(existingHierarchies, product, newOffsets, copyProductRateOffsets, allProducts).isEmpty()) {
                            invalidHierarchyAccomClasses.add(roomClass);
                        }
                        existingHierarchies.addAll(hierarchiesToRemove);
                    }
                }
            }
            product.setRoomClassOffset(false);
            product.setDtaOffset(false);
            product.setDowOffset(false);

            return invalidHierarchyAccomClasses;
        }
        return invalidHierarchyAccomClasses;
    }

    public Set<AccomClass> isHierarchyValidationValid(BigDecimal floorOverride, BigDecimal ceilingOverride,
                                                      Product product, LocalDate selectedDate, AccomClass selectedAccomClass) {

        List<ProductHierarchy> existingHierarchies = agileRatesConfigurationService.findImpactedProductHierarchies(product.getId());
        Set<AccomClass> invalidHierarchyMap = new HashSet<>();
        if (!existingHierarchies.isEmpty()) {
            // determine if all room classes need to be considered
            List<AccomClass> roomClasses = isAgileRatesAndApplyToAllRoomClasses(product) ? findSortedRoomClassesForProduct(product) : asList(selectedAccomClass);
            if (allProductRateOffsets.isEmpty()) {
                allProductRateOffsets = agileRatesConfigurationService.findAllProductRateOffsets();
            }
            List<ProductRateOffset> copyProductRateOffsets = deepCopyAllProductRateOffsets(allProductRateOffsets);
            // find all DTAs for date
            AgileRatesDTARange dtaRangeForDate = getDtaRangeForSelectedDate(selectedDate);

            // determine DOW
            Integer dayOfWeek = DayOfWeek.forShortCaption(DateUtil.getDayOfWeek(selectedDate)).getCalendarDayOfWeek();

            product.setRoomClassOffset(true);
            product.setDtaOffset(dtaRangeForDate != null);
            product.setDowOffset(true);

            for (AccomClass roomClass : roomClasses) {
                List<ProductRateOffset> newOffsets = generateOffsetsForHierarchyValidation(floorOverride, ceilingOverride,
                        dayOfWeek, roomClass, dtaRangeForDate, product);
                List<ProductHierarchy> hierarchiesToRemove = new ArrayList<>();
                existingHierarchies.forEach(h -> {
                    if ((!h.getFromProduct().isSystemDefaultOrIndependentProduct())
                            && (!findSortedRoomClassesForProduct(h.getToProduct()).contains(roomClass)
                            || !findSortedRoomClassesForProduct(h.getFromProduct()).contains(roomClass))) {
                        hierarchiesToRemove.add(h);
                    }
                });
                existingHierarchies.removeAll(hierarchiesToRemove);

                if (SystemConfig.isComprehensiveProductHierarchyValidationEnabled()) {
                    existingHierarchies.forEach(productHierarchy -> {
                        Product selectedProduct, relatedProduct;
                        if (productHierarchy.getFromProduct().equals(product)) {
                            selectedProduct = product;
                            relatedProduct = productHierarchy.getToProduct();
                        } else {
                            selectedProduct = productHierarchy.getFromProduct();
                            relatedProduct = product;
                        }
                        ProductHierarchyDto productHierarchyDto = getProductHierarchyDto(productHierarchy, selectedProduct, relatedProduct);
                        List<String> warnings = new ArrayList<>();
                        boolean isValid = hierarchyValidationService.validateHierarchyWhenOverrideChanges(productHierarchyDto, newOffsets, selectedDate, warnings);
                        if (!isValid) {
                            invalidHierarchyMap.add(roomClass);
                        }
                    });

                } else if (!agileRatesConfigurationService.findInvalidExistingHierarchies(existingHierarchies, product,
                        newOffsets, copyProductRateOffsets, allProducts).isEmpty()) {
                    invalidHierarchyMap.add(roomClass);
                }
                existingHierarchies.addAll(hierarchiesToRemove);
            }
            product.setRoomClassOffset(false);
            product.setDtaOffset(false);
            product.setDowOffset(false);
        }
        return invalidHierarchyMap;
    }

    private ProductHierarchyDto getProductHierarchyDto(ProductHierarchy productHierarchy,
                                                       Product selectedProduct, Product relatedProduct) {
        ProductHierarchyDto productHierarchyDto = new ProductHierarchyDto();
        productHierarchyDto.setSelectedProduct(selectedProduct);
        productHierarchyDto.setRelatedProduct(relatedProduct);
        productHierarchyDto.setMinimumDifference(productHierarchy.getMinimumDifference());
        return productHierarchyDto;
    }

    private List<ProductRateOffset> deepCopyAllProductRateOffsets(List<ProductRateOffset> offsetsToCopy) {
        List<ProductRateOffset> newOffsets = new ArrayList<>();
        offsetsToCopy.forEach(offset -> newOffsets.add(new ProductRateOffset(offset)));
        return newOffsets;
    }

    public List<ProductRateOffset> generateOffsetsForHierarchyValidation(BigDecimal floorOverride, BigDecimal ceilingOverride, Integer dayOfWeek,
                                                                         AccomClass roomClass, AgileRatesDTARange dtaRange, Product product) {
        List<ProductRateOffset> newOffsets = new ArrayList<>();
        newOffsets.add(setProductRateOffsetValues(floorOverride, ceilingOverride, dayOfWeek, dtaRange, roomClass, product));
        return newOffsets;
    }

    public AgileRatesDTARange getDtaRangeForSelectedDate(LocalDate selectedDate) {
        if (allDTARanges.isEmpty()) {
            allDTARanges = service.findAllDTARanges();
        }
        int daysBetween = (int) DateUtil.daysBetween(getSystemDateAsLocalDate().toDate(), selectedDate.toDate());
        for (AgileRatesDTARange dta : allDTARanges) {
            if (dta.getMinDaysToArrival().compareTo(daysBetween) <= 0 &&
                    (dta.getMaxDaysToArrival() == null ||
                            dta.getMaxDaysToArrival().compareTo(daysBetween) >= 0)) {
                return dta;
            }
        }
        return null;
    }

    public List<AgileRatesDTARange> getDtaRangesForSelectedDatesMultiday(LocalDate startDate, LocalDate endDate, Product product) {
        List<AgileRatesDTARange> productDTARange = new ArrayList<>();
        if (allDTARanges.isEmpty()) {
            allDTARanges = service.findAllDTARanges();
        }
        List<AgileRatesDTARange> productDTARanges = agileRatesConfigurationService.getAdjustedDTARanges(product, allDTARanges);
        for (; !startDate.isAfter(endDate); startDate = startDate.plusDays(1)) {
            int daysBetween = (int) DateUtil.daysBetween(getSystemDateAsLocalDate().toDate(), startDate.toDate());
            for (AgileRatesDTARange dta : productDTARanges) {
                if (!productDTARange.contains(dta) && dta.getMinDaysToArrival().compareTo(daysBetween) <= 0 &&
                        (dta.getMaxDaysToArrival() == null ||
                                dta.getMaxDaysToArrival().compareTo(daysBetween) >= 0)) {
                    productDTARange.add(dta);
                }
            }
        }

        return productDTARange;
    }

    private ProductRateOffset setProductRateOffsetValues(BigDecimal floorOverride, BigDecimal ceilingOverride,
                                                         Integer daysOfWeek, AgileRatesDTARange dta, AccomClass roomClass,
                                                         Product product) {
        ProductRateOffset newOffset = new ProductRateOffset();
        newOffset.setAgileRatesDTARange(dta);
        newOffset.setAccomClass(roomClass);
        newOffset.setProduct(product);
        if (daysOfWeek == 1) {
            newOffset.setMondayOffsetValueFloor(floorOverride);
            newOffset.setMondayOffsetValueCeiling(ceilingOverride);
        }
        if (daysOfWeek == 2) {
            newOffset.setTuesdayOffsetValueFloor(floorOverride);
            newOffset.setTuesdayOffsetValueCeiling(ceilingOverride);
        }
        if (daysOfWeek == 3) {
            newOffset.setWednesdayOffsetValueFloor(floorOverride);
            newOffset.setWednesdayOffsetValueCeiling(ceilingOverride);
        }
        if (daysOfWeek == 4) {
            newOffset.setThursdayOffsetValueFloor(floorOverride);
            newOffset.setThursdayOffsetValueCeiling(ceilingOverride);
        }
        if (daysOfWeek == 5) {
            newOffset.setFridayOffsetValueFloor(floorOverride);
            newOffset.setFridayOffsetValueCeiling(ceilingOverride);
        }
        if (daysOfWeek == 6) {
            newOffset.setSaturdayOffsetValueFloor(floorOverride);
            newOffset.setSaturdayOffsetValueCeiling(ceilingOverride);
        }
        if (daysOfWeek == 7) {
            newOffset.setSundayOffsetValueFloor(floorOverride);
            newOffset.setSundayOffsetValueCeiling(ceilingOverride);
        }
        return newOffset;
    }

    public boolean isOverrideValidInlineEdit(CPOverrideWrapper overrideWrapper, BigDecimal specificOverride) {
        // we do not validate rounding rules for price excluded, so just return true here
        if (isRoomClassPriceExcluded(overrideWrapper.getAccomClass())) {
            return true;
        }

        if (cpDecisionContext == null || (!DateUtil.isDateBetween(cpDecisionContext.getStartDate().toDate(),
                cpDecisionContext.getEndDate().toDate(), overrideWrapper.getCpDecisionBAROutput().getArrivalDate().toDate()))) {
            cpDecisionContext = pricingConfigurationService.getCPDecisionContext(getCpPricingFilter().getStartDate(), getCpPricingFilter().getEndDate(), true);
        }

        Integer primaryProductId = cpDecisionContext.getPrimaryProduct(overrideWrapper.getProduct()).getId();
        boolean overrideValid = prettyPricingService.doesNumberMeetPricingRuleForProduct(specificOverride.abs(), primaryProductId);

        if (overrideValid && overrideWrapper.getCeilingOverride() != null) {
            overrideValid = prettyPricingService.doesNumberMeetPricingRuleForProduct(overrideWrapper.getCeilingOverride().abs(), primaryProductId);
        }

        if (overrideValid && overrideWrapper.getFloorOverride() != null) {
            overrideValid = prettyPricingService.doesNumberMeetPricingRuleForProduct(overrideWrapper.getFloorOverride().abs(), primaryProductId);
        }

        return overrideValid;
    }

    public void applyMultidayToInlineEditGrid(List<PricingMultidayGridDTO> changedDTOs, LocalDate startDate, LocalDate endDate, List<Integer> calendarDaysOfWeek, PricingMultiProductMultidayFilterDTO filterDTO) {
        List<PricingMultidayGridDTO> optimizedDTOs = changedDTOs.stream()
                .filter(dto -> dto.getProduct().isOptimized())
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(optimizedDTOs)) {
            view.applyMultidayOptimizedProductsToInlineEditGrid(optimizedDTOs, startDate, endDate, calendarDaysOfWeek);
        }

        List<PricingMultidayGridDTO> nonOptimizedDTOs = changedDTOs.stream()
                .filter(dto -> !dto.getProduct().isSystemDefaultOrIndependentProduct() &&
                        !dto.getProduct().isOptimized())
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(nonOptimizedDTOs)) {
            view.applyMultidayNonOptimizedProductsToInlineEditGrid(nonOptimizedDTOs, startDate, endDate, calendarDaysOfWeek);
        }

        List<PricingMultidayGridDTO> barDTOs = changedDTOs.stream()
                .filter(dto -> dto.getProduct().isSystemDefaultOrIndependentProduct())
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(barDTOs)) {
            view.applyMultidayBARProductsToInlineEditGrid(barDTOs, startDate, endDate, calendarDaysOfWeek, filterDTO);
        }
    }

    @ForTesting
    public void setSelectedView(PricingToggleView selectedView) {
        this.selectedView = selectedView;
    }

    public List<String> getPricingSyncFlagsNames() {
        List<SyncConfigParamName> pricingSyncFlags = asList(
                SyncConfigParamName.CONTINUOUS_PRICING_SPECIFIC_OVERRIDE_REMOVED,
                SyncConfigParamName.CONTINUOUS_PRICING_FLOOR_CEILING_OVERRIDE_CHANGED,
                SyncConfigParamName.AGILE_RATES_CHANGED,
                SyncConfigParamName.AGILE_RATES_OVERRIDE_CHANGED,
                SyncConfigParamName.GROUP_PRODUCT_CHANGED,
                SyncConfigParamName.GROUP_PRODUCT_OVERRIDE_CHANGED);
        return pricingSyncFlags.stream()
                .map(SyncConfigParamName::toString)
                .collect(Collectors.toList());
    }

    public boolean isSyncVisibleForPricingStaleness() {
        return uiContext.getPropertyState().getStaleness().stream().map(StalenessFlag::getId).anyMatch(stalenessId -> getPricingSyncFlagsNames().contains(stalenessId));
    }

    public Map<Product, List<ProductRateOffset>> getRateOffsetsForSelectedProducts() {
        if (!getCpPricingFilter().isAgileRateProductSelected()) {
            return new HashMap<>();
        }

        Set<Product> agileProducts = getCpPricingFilter().getProducts()
                .stream()
                .filter(product -> !product.isSystemDefaultOrIndependentProduct())
                .collect(Collectors.toSet());
        List<ProductRateOffset> productDiscounts = service.getProductDiscountsFor(agileProducts);
        return productDiscounts.stream().collect(Collectors.groupingBy(ProductRateOffset::getProduct));
    }

    public Map<AccomTypeSupplementValuePK, AccomTypeSupplementValue> getSupplementValuesForSelectedDates() {
        if (!isSupplementalEnabled) {
            return new HashMap<>();
        }
        LocalDate startDate = getCpPricingFilter().getStartDate();
        LocalDate endDate = getCpPricingFilter().getEndDate();
        return accomTypeSupplementService.getSupplementValueMap(startDate, endDate);
    }

    @ForTesting
    public void setBaseOccupancyType(OccupancyType baseOccupancyType) {
        this.baseOccupancyType = baseOccupancyType;
    }

    public OccupancyType getBaseOccupancyType() {
        if (null == baseOccupancyType) {
            baseOccupancyType = pricingConfigurationService.getBaseOccupancyType();
        }
        return baseOccupancyType;
    }

    public Map<LocalDate, Tax> getTaxesForDateSelectedDates() {
        LocalDate startDate = getCpPricingFilter().getStartDate();
        LocalDate endDate = getCpPricingFilter().getEndDate();
        return service.getTaxesForDateRange(startDate, endDate);
    }

    public Map<CPConfigMergedOffsetPK, CPConfigMergedOffset> getOffsetsForSelectedDates() {
        LocalDate startDate = getCpPricingFilter().getStartDate();
        LocalDate endDate = getCpPricingFilter().getEndDate();
        return service.getOffsetsForDates(startDate, endDate);
    }

    public Map<AccomClass, List<CPUnqualifedDemandForecastPrice>> getDemandForecastBetweenDatesForSelectedDates(Product product) {
        LocalDate startDate = getCpPricingFilter().getStartDate();
        LocalDate endDate = getCpPricingFilter().getEndDate();
        return getRoomClassListExcludingAll().stream()
                .flatMap(accomClass -> service.findDemandForecastBetweenDatesForAccomClass(product, accomClass, startDate, endDate).stream())
                .collect(Collectors.groupingBy(CPUnqualifedDemandForecastPrice::getAccomClass));

    }

    public Date getCaughtUpDate() {
        return getSystemDateAsLocalDate().toDate();
    }

    @ForTesting
    public List<CompetitorDetailsRow> getAllCompetitorDetailsRows() {
        return allCompetitorDetailsRows;
    }

    @ForTesting
    public void setWebrateCompetitors(List<WebrateCompetitors> webrateCompetitors) {
        this.webrateCompetitors = webrateCompetitors;
    }

    @ForTesting
    public void setManualRestrictionsPropertyDto(Map<LocalDate, ManualRestrictionTypeDto> manualRestrictionsPropertyDto) {
        this.manualRestrictionsPropertyDto = manualRestrictionsPropertyDto;
    }

    @ForTesting
    public void setManualRestrictionsAccomDto(Map<LocalDate, Map<ManualRestrictionRoomTypeIdentifier, ManualRestrictionTypeDto>> manualRestrictionsAccomDto) {
        this.manualRestrictionsAccomDto = manualRestrictionsAccomDto;
    }

    @ForTesting
    public void setManualRestrictionEnabled(boolean manualRestrictionEnabled) {
        this.isManualRestrictionEnabled = manualRestrictionEnabled;
    }

    public boolean isPriceIncrease(BigDecimal roundedBAR, BigDecimal previousBAR) {
        return null != roundedBAR &&
                null != previousBAR &&
                roundedBAR.compareTo(previousBAR) > 0;

    }

    public boolean isPriceDecrease(BigDecimal roundedBAR, BigDecimal previousBAR) {
        return null != roundedBAR &&
                null != previousBAR &&
                roundedBAR.compareTo(previousBAR) < 0;
    }

    public boolean isDisplayProfitMetricsEnabled() {
        return simplifiedWhatIfService.isDisplayProfitMetricsEnabled();
    }

    public String createMultidayErrorMessageString(HashMap<String, Boolean> errorMessageMap,
                                                   String invalidHierarchyProductString,
                                                   String invalidHierarchyBecauseOfCeilingFloorOverride,
                                                   int errorMessageTotals) {
        String message = "";
        if (errorMessageTotals > 1) {
            //Add bullet points
            boolean firstItem = true;
            for (Map.Entry<String, Boolean> entry : errorMessageMap.entrySet()) {
                String key = entry.getKey();
                Boolean value = entry.getValue();
                if (value) {
                    if (!firstItem) {
                        message += "<br/>";
                    }
                    message += "<li>" + getText(key);
                    if (key.equals("continuousPricing.adjustment.multiDay.multiProduct.invalid.warning")) {
                        message += invalidHierarchyProductString;
                    }
                    if (key.equals("hierarchy.was.broken.because.of.ceiling.floor.overrides")) {
                        message += invalidHierarchyBecauseOfCeilingFloorOverride;
                    }
                    firstItem = false;
                }
            }
        } else {
            //Do not add bullet points
            for (Map.Entry<String, Boolean> entry : errorMessageMap.entrySet()) {
                String key = entry.getKey();
                Boolean value = entry.getValue();
                if (value) {
                    message += getText(key);
                    if (key.equals("continuousPricing.adjustment.multiDay.multiProduct.invalid.warning")) {
                        message += invalidHierarchyProductString;
                    }
                    if (key.equals("hierarchy.was.broken.because.of.ceiling.floor.overrides")) {
                        message += invalidHierarchyBecauseOfCeilingFloorOverride;
                    }
                }
            }
        }
        return message;
    }

    public boolean isPriceDecrease(PricingAgileRatesOverrideDTO agileRatesOverrideDTO) {
        return null != agileRatesOverrideDTO.getOverrideWrapper().getRoundedBAR() &&
                null != agileRatesOverrideDTO.getOverrideWrapper().getPreviousBAR() &&
                agileRatesOverrideDTO.getOverrideWrapper().getRoundedBAR().compareTo(agileRatesOverrideDTO.getOverrideWrapper().getPreviousBAR()) < 0;
    }

    public boolean isPriceIncrease(PricingAgileRatesOverrideDTO agileRatesOverrideDTO) {
        return null != agileRatesOverrideDTO.getOverrideWrapper().getRoundedBAR() &&
                null != agileRatesOverrideDTO.getOverrideWrapper().getPreviousBAR() &&
                agileRatesOverrideDTO.getOverrideWrapper().getRoundedBAR().compareTo(agileRatesOverrideDTO.getOverrideWrapper().getPreviousBAR()) > 0;
    }

    public String getHeaderName(Product product) {
        if (!product.isSystemDefaultOrIndependentProduct() && !product.isOptimized()) {
            if (AgileRatesOffsetMethod.PERCENTAGE.equals(product.getOffsetMethod())) {
                return product.getName() + "<br/>(" + UiUtils.getText("percentage.text") + ")";
            } else {
                return product.getName() + "<br/>(" + UiUtils.getText("fixed.text") + ")";
            }
        } else {
            return product.getName();
        }
    }

    public String generateColumnKey(String productName, String accomClassCode, String accomTypeCode) {
        String colKey = productName + "_" + accomClassCode + "_" + accomTypeCode;
        colKey = colKey.replaceAll("\\s+", "");
        return colKey;
    }

    public String generateCompetitorColumnKey(String competitorName, String productName, String accomClassCode, String accomTypeCode) {
        String colKey = competitorName + "_" + generateColumnKey(productName, accomClassCode, accomTypeCode);
        colKey = colKey.replaceAll("\\s+", "");
        return colKey;
    }

    public String generateRowColumnKey(String rowId, String columnId) {
        return rowId + "_" + columnId;
    }

    public void setRevertOrDeleteFlagsForOptimizedProducts(boolean isRemove, PricingAgileRatesOverrideDTO optimizedDTO) {
        optimizedDTO.setFloorMarkedForDeletion(isRemove);
        optimizedDTO.setCeilingMarkedForDeletion(isRemove);
        optimizedDTO.setFloorOffset(optimizedDTO.getOriginalFloorOffset());
        optimizedDTO.setCeilingOffset(optimizedDTO.getOriginalCeilingOffset());
    }

    public void removeBarOverride(boolean isRemove, CPOverrideWrapper wrapper, CPDecisionContext cpDecisionContext, PricingAccomClass masterPricingAccomClass, boolean isInlineEditMode) {
        if (isRemove) {
            //Remove override
            wrapper.setSpecificOverride(null);
            wrapper.setFloorOverride(null);
            wrapper.setCeilingOverride(null);
            applyRemove(findWrapper(wrapper), false, cpDecisionContext, masterPricingAccomClass, isInlineEditMode);
        } else {
            //Revert our overrideWrapper
            revertOverrideWrapper(wrapper);
            //Revert the PricingOverride Manager's overrideWrapper
            revertOverrideWrapper(findWrapper(wrapper));
        }
    }

    public void clearOverrideWrapper(CPOverrideWrapper wrapper) {
        wrapper.setSpecificOverride(wrapper.getOriginalSpecificOverride());
        wrapper.setFloorOverride(wrapper.getOriginalFloorOverride());
        wrapper.setCeilingOverride(wrapper.getOriginalCeilingOverride());
    }

    public boolean wrapperHasSavedOverride(CPOverrideWrapper wrapper) {
        return wrapper.getOriginalSpecificOverride() != null ||
                wrapper.getOriginalFloorOverride() != null ||
                wrapper.getOriginalCeilingOverride() != null;
    }

    public List<String> optimizedProductValidationMessages(BigDecimal floorValue, BigDecimal ceilingValue, BigDecimal lowerBound, PricingAgileRatesOverrideDTO dto) {
        List<String> validationMessages = new ArrayList<>();
        if (!BigDecimalUtil.isLessThan(lowerBound, floorValue) ||
                !BigDecimalUtil.isLessThan(lowerBound, ceilingValue) ||
                !BigDecimalUtil.isLessThan(floorValue, BigDecimal.valueOf(Integer.MAX_VALUE)) ||
                !BigDecimalUtil.isLessThan(ceilingValue, BigDecimal.valueOf(Integer.MAX_VALUE))) {
            validationMessages.add(UiUtils.getText("fieldValueRangeValidation",
                    UiUtils.getText("Percentage") + " " + UiUtils.getText("common.offset").toLowerCase(),
                    -100, Integer.MAX_VALUE));
        }
        if (BigDecimalUtil.isGreaterThan(floorValue, ceilingValue)) {
            validationMessages.add(UiUtils.getText("pricing.optimized.override.floor.less.than.or.equal.to.ceiling"));
        }
        if (!isOverrideWithinRestrictedRange(dto, floorValue, ceilingValue, isAgileRatesAndApplyToAllRoomClasses(dto.getProduct()))) {
            validationMessages.add(UiUtils.getText("pricing.optimized.override.restrict.overrides.warning"));
        }
        if (dto.getProduct().isGroupProduct()) {
            if (!isNumberNegative(floorValue) || !isNumberNegative(ceilingValue)) {
                validationMessages.add(UiUtils.getText("negativePercentageRangeValidation",
                        UiUtils.getText("common.offset").toLowerCase(),
                        -100,
                        0));
            }
        }
        return validationMessages;
    }

    public List<String> nonOptimizedProductValidationMessages(BigDecimal adjustmentValue, BigDecimal lowerBound) {
        List<String> validationMessages = new ArrayList<>();
        if (!BigDecimalUtil.isLessThan(lowerBound, adjustmentValue) ||
                !BigDecimalUtil.isLessThan(adjustmentValue, BigDecimal.valueOf(Integer.MAX_VALUE))) {
            validationMessages.add(UiUtils.getText("fieldValueRangeValidation",
                    UiUtils.getText("Percentage") + " " + UiUtils.getText("common.offset").toLowerCase(),
                    -100, Integer.MAX_VALUE));
        }
        return validationMessages;
    }

    public void setBARDTOOverrides(BigDecimal specificOverrideValue, BigDecimal floorOverrideValue, BigDecimal ceilingOverrideValue,
                                   CPOverrideWrapper wrapper, CPOverrideWrapper baseRoomTypeWrapper, CPDecisionContext cpDecisionContext,
                                   boolean isMultiday, boolean isPriceLock) {
        if (isPriceLock) {
            if (isBaseRoomType(wrapper.getAccomType()) && !isRoomClassPriceExcluded(wrapper.getAccomClass())) {
                wrapper.setSpecificOverride(null);
                wrapper.setFloorOverride(wrapper.getRoundedBAR());
                wrapper.setCeilingOverride(wrapper.getRoundedBAR());
            } else {
                wrapper.setSpecificOverride(wrapper.getRoundedBAR());
                wrapper.setFloorOverride(null);
                wrapper.setCeilingOverride(null);
            }
        } else {
            if (baseRoomTypeWrapper == null && !wrapperHasSavedOverride(wrapper) &&
                    BigDecimalUtil.equals(wrapper.getRoundedBAR(), specificOverrideValue) &&
                    !isMultiday) {
                //If there was never a specific override and it matched the previous roundedBAR
                wrapper.setSpecificOverride(wrapper.getOriginalSpecificOverride());
                wrapper.setFloorOverride(wrapper.getOriginalFloorOverride());
                wrapper.setCeilingOverride(wrapper.getOriginalCeilingOverride());
            } else {
                //If there was a saved override, determine what type of override was saved to overwrite the values
                if (isBaseRoomType(wrapper.getAccomType()) && !isRoomClassPriceExcluded(wrapper.getAccomClass())) {
                    wrapper.setSpecificOverride(null);
                    wrapper.setFloorOverride(floorOverrideValue);
                    wrapper.setCeilingOverride(ceilingOverrideValue);
                } else {
                    if (baseRoomTypeWrapper != null) {
                        BigDecimal offset = BigDecimalUtil.round(cpDecisionContext.getOffset(wrapper.getCpDecisionBAROutput(), baseRoomTypeWrapper.getRoundedBAR()), 2);
                        Product product = wrapper.getCpDecisionBAROutput().getProduct();
                        Product primaryProduct = cpDecisionContext.getPrimaryProduct(product);
                        Integer primaryProductId = primaryProduct.getId();
                        BigDecimal prettyPrice = cpDecisionContext.calculatePrettyPrice(primaryProductId, specificOverrideValue.add(offset));
                        wrapper.setSpecificOverride(prettyPrice);
                    } else {
                        wrapper.setSpecificOverride(specificOverrideValue);
                    }
                    wrapper.setFloorOverride(null);
                    wrapper.setCeilingOverride(null);
                }
            }
        }
    }

    public void updateOptimizedDTOBasedOnOriginalValues(BigDecimal floorOverrideValue, BigDecimal ceilingOverrideValue, PricingAgileRatesOverrideDTO item) {
        if (item.hasExistingCeilingOverride() || item.hasExistingFloorOverride()) {
            item.setFloorOffset(floorOverrideValue);
            item.setCeilingOffset(ceilingOverrideValue);
            item.setFloorMarkedForDeletion(false);
            item.setCeilingMarkedForDeletion(false);
        } else {
            if (!BigDecimalUtil.equals(item.getFloorDiscount(), floorOverrideValue) ||
                    !BigDecimalUtil.equals(item.getCeilingDiscount(), ceilingOverrideValue)) {
                item.setFloorOffset(floorOverrideValue);
                item.setCeilingOffset(ceilingOverrideValue);
                item.setFloorMarkedForDeletion(false);
                item.setCeilingMarkedForDeletion(false);
            } else {
                item.setFloorOffset(item.getOriginalFloorOffset());
                item.setCeilingOffset(item.getOriginalCeilingOffset());
                item.setFloorMarkedForDeletion(false);
                item.setCeilingMarkedForDeletion(false);
            }
        }
    }

    public void updateNonOptimizedDTOBasedOnOriginalValue(BigDecimal overrideValue, PricingAgileRatesOverrideDTO item) {
        if (item.getOriginalAdjustment() != null) {
            item.setAdjustment(overrideValue);
        } else {
            //If there is no override and it is equal to the original adjustment value, clear the adjustment with the original data
            if (!BigDecimalUtil.equals(item.getFloorDiscount(), overrideValue)) {
                item.setAdjustment(overrideValue);
            } else {
                item.setAdjustment(item.getOriginalAdjustment());
            }
        }
    }

    public void nonOptimizedRevertOrDelete(boolean isRemove, PricingAgileRatesOverrideDTO nonOptimizedDTO) {
        nonOptimizedDTO.getOverrideWrapper().setIsPendingDelete(isRemove);
        nonOptimizedDTO.getOverrideWrapper().setIsPendingSave(false);
        nonOptimizedDTO.setAdjustment(nonOptimizedDTO.getOriginalAdjustment());
    }

    public boolean isAppliedToPricingOverrideManager(CPOverrideWrapper wrapper) {
        return appliedMultiProductBARDTOs.stream()
                .filter(dto -> dto.getProduct().equals(wrapper.getProduct()))
                .filter(dto -> dto.getAccomType().equals(wrapper.getAccomType()))
                .filter(dto -> dto.getStartDate().equals(wrapper.getStartDate()))
                .anyMatch(dto -> dto.getEndDate().equals(wrapper.getEndDate()));
    }

    public boolean isMatchingAppliedWrapperInPricingOverrideManager(CPOverrideWrapper wrapper) {
        return appliedMultiProductBARDTOs.stream()
                .filter(dto -> dto.getProduct().equals(wrapper.getProduct()))
                .filter(dto -> dto.getAccomType().equals(wrapper.getAccomType()))
                .filter(dto -> dto.getStartDate().equals(wrapper.getStartDate()))
                .filter(dto -> dto.getEndDate().equals(wrapper.getEndDate()))
                .anyMatch(dto -> BigDecimalUtil.equals(dto.getSpecificOverride(), wrapper.getSpecificOverride()) &&
                        BigDecimalUtil.equals(dto.getFloorOverride(), wrapper.getFloorOverride()) &&
                        BigDecimalUtil.equals(dto.getCeilingOverride(), wrapper.getCeilingOverride()));
    }

    public void setAppliedMultiProductBARDTOs(List<CPOverrideWrapper> appliedMultiProductBARDTOs) {
        this.appliedMultiProductBARDTOs = appliedMultiProductBARDTOs;
    }

    public boolean areBAROverridesPositiveValues(PricingMultidayGridDTO dto) {
        boolean overrideValid = true;

        if (dto.getSpecificOverride() != null) {
            overrideValid = !BigDecimalUtil.isLessThan(dto.getSpecificOverride(), BigDecimal.ZERO);
        }

        if (overrideValid && dto.getCeilingOverride() != null) {
            overrideValid = !BigDecimalUtil.isLessThan(dto.getCeilingOverride(), BigDecimal.ZERO);
        }

        if (overrideValid && dto.getFloorOverride() != null) {
            overrideValid = !BigDecimalUtil.isLessThan(dto.getFloorOverride(), BigDecimal.ZERO);
        }

        return overrideValid;
    }

    public void revertUnchangedOverrideWrapper(CPOverrideWrapper wrapper) {
        //Find the in the applied wrapper list
        CPOverrideWrapper cpOverrideWrapper = appliedMultiProductBARDTOs.stream()
                .filter(dto -> dto.getProduct().equals(wrapper.getProduct()))
                .filter(dto -> dto.getAccomType().equals(wrapper.getAccomType()))
                .filter(dto -> dto.getStartDate().equals(wrapper.getStartDate()))
                .filter(dto -> dto.getEndDate().equals(wrapper.getEndDate()))
                .findFirst()
                .orElse(null);
        if (cpOverrideWrapper != null) {
            //Find the wrapper from the results page directly displayed on the day card layout
            CPOverrideWrapper resultsWrapper = getResults().stream()
                    .flatMap(item -> item.getCpOverrideWrappers().stream())
                    .filter(item -> item.getCpDecisionBAROutput().getProduct().equals(wrapper.getCpDecisionBAROutput().getProduct()))
                    .filter(item -> item.getAccomType().equals(wrapper.getAccomType()))
                    .filter(item -> item.getStartDate().equals(wrapper.getStartDate()))
                    .filter(item -> item.getEndDate().equals(wrapper.getEndDate()))
                    .findFirst()
                    .orElse(null);
            if (resultsWrapper != null) {
                revertChange(resultsWrapper);
                appliedMultiProductBARDTOs.remove(cpOverrideWrapper);
            }
        }
    }

    public BigDecimal applyOffsetsToOverrideValuesAndReturnPrettyPrice(CPDecisionContext cpDecisionContext, CPOverrideWrapper baseRoomOverrideWrapper, CPOverrideWrapper nonBaseOverrideWrapper) {
        BigDecimal valueWithOffsetApplied = cpDecisionContext.applyOffset(nonBaseOverrideWrapper.getCpDecisionBAROutput(), baseRoomOverrideWrapper.getRoundedBAR());
        Integer primaryProductId = cpDecisionContext.getPrimaryProduct(baseRoomOverrideWrapper.getCpDecisionBAROutput().getProduct()).getId();
        BigDecimal prettyPrice = cpDecisionContext.calculatePrettyPrice(primaryProductId, valueWithOffsetApplied);
        CPConfigMergedCeilingAndFloor ceilingAndFloor = cpDecisionContext.getCeilingAndFloor(baseRoomOverrideWrapper.getCpDecisionBAROutput());

        BigDecimal floorRate = cpDecisionContext.applyOffset(baseRoomOverrideWrapper.getCpDecisionBAROutput(), baseRoomOverrideWrapper.getFloorOverride());
        BigDecimal ceilingRate = cpDecisionContext.applyOffset(baseRoomOverrideWrapper.getCpDecisionBAROutput(), baseRoomOverrideWrapper.getCeilingOverride());
        if (ceilingAndFloor != null) {
            if (floorRate == null) {
                floorRate = ceilingAndFloor.getFloorRate();
            }
            if (ceilingRate == null) {
                ceilingRate = ceilingAndFloor.getCeilingRate();
            }
        }

        if (isValueLessThanOrEqualToFloor(floorRate, prettyPrice) || isValueGreaterThanOrEqualToCeiling(ceilingRate, prettyPrice)) {
            nonBaseOverrideWrapper.setRoundedBAR(prettyPrice);
            nonBaseOverrideWrapper.getCpDecisionBAROutput().setPrettyBAR(prettyPrice);
            nonBaseOverrideWrapper.getCpDecisionBAROutput().setRoomsOnlyBAR(BigDecimalUtil.subtract(prettyPrice, cpDecisionContext.getSupplement(nonBaseOverrideWrapper.getCpDecisionBAROutput())));
            nonBaseOverrideWrapper.getCpDecisionBAROutput().setFinalBAR(prettyPrice);

            if (nonBaseOverrideWrapper.getOriginalSpecificOverride() != null) {
                nonBaseOverrideWrapper.setSpecificOverride(null);
                nonBaseOverrideWrapper.setIsPendingDelete(true);
            }
        }
        return prettyPrice;
    }

    public CPConfigMergedCeilingAndFloor getCeilingAndFloor(CPDecisionBAROutput cpDecisionBAROutput) {
        if (cpDecisionContext == null || (!DateUtil.isDateBetween(cpDecisionContext.getStartDate().toDate(),
                cpDecisionContext.getEndDate().toDate(), cpDecisionBAROutput.getArrivalDate().toDate()))) {
            cpDecisionContext = pricingConfigurationService.getCPDecisionContext(getCpPricingFilter().getStartDate(), getCpPricingFilter().getEndDate(), true);
        }
        return cpDecisionContext.getCeilingAndFloor(cpDecisionBAROutput);
    }

    private boolean isValueLessThanOrEqualToFloor(BigDecimal floorRate, BigDecimal valueWithOffsetApplied) {
        return floorRate != null && valueWithOffsetApplied.compareTo(floorRate) <= 0;
    }

    private boolean isValueGreaterThanOrEqualToCeiling(BigDecimal ceilingRate, BigDecimal valueWithOffsetApplied) {
        return ceilingRate != null && valueWithOffsetApplied.compareTo(ceilingRate) >= 0;
    }

    public DecisionOverrideType getDecisionOverrideTypeFromOverrideWrapper(CPOverrideWrapper overrideWrapper) {
        DecisionOverrideType overrideType = DecisionOverrideType.NONE;

        if (overrideWrapper.getSpecificOverride() == null) {
            if (overrideWrapper.getCeilingOverride() != null) {
                if (overrideWrapper.getFloorOverride() != null) {
                    overrideType = DecisionOverrideType.FLOORANDCEIL;
                } else if (overrideWrapper.getGroupFloorOverride() != null) {
                    overrideType = DecisionOverrideType.GPFLOORANDCEIL;
                } else {
                    overrideType = DecisionOverrideType.CEIL;
                }
            } else {
                if (overrideWrapper.getFloorOverride() != null) {
                    overrideType = DecisionOverrideType.FLOOR;
                } else if (overrideWrapper.getGroupFloorOverride() != null) {
                    overrideType = DecisionOverrideType.GPFLOOR;
                }
            }
        } else {
            overrideType = DecisionOverrideType.USER;
        }
        return overrideType;
    }

    @ForTesting
    public void setDailyBARPricingRule(Integer dailyBARPricingRule) {
        this.dailyBARPricingRule = dailyBARPricingRule;
    }

    public List<AccomClassPriceRank> getAllAccomClassPriceRanks() {
        return allAccomClassPriceRanks;
    }

    public List<String> getErrorMessagesForPricingRuleTypeValidation(CPOverrideWrapper wrapper) {
        List<String> errorMessages = new ArrayList<>();
        if (dailyBARPricingRule == 2 || dailyBARPricingRule == 3) {
            if (cpDecisionContext == null || (!DateUtil.isDateBetween(cpDecisionContext.getStartDate().toDate(),
                    cpDecisionContext.getEndDate().toDate(), wrapper.getCpDecisionBAROutput().getArrivalDate().toDate()))) {
                cpDecisionContext = pricingConfigurationService.getCPDecisionContext(getCpPricingFilter().getStartDate(), getCpPricingFilter().getEndDate(), true);
            }
            if (allAccomClassPriceRanks.isEmpty()) {
                allAccomClassPriceRanks = pricingConfigurationService.getAllAccomClassPriceRanks();
            }
            List<AccomClassPriceRank> accomClassPriceRanks = allAccomClassPriceRanks.stream().filter(ac -> ac.getLowerRankAccomClass().equals(wrapper.getAccomClass()) || ac.getHigherRankAccomClass().equals(wrapper.getAccomClass())).collect(Collectors.toList());
            List<AccomClass> lowerRankedAccomClasses = getLowerRankedAccomClasses(accomClassPriceRanks, wrapper.getAccomClass());
            List<AccomClass> higherRankedAccomClasses = getHigherRankedAccomClasses(accomClassPriceRanks, wrapper.getAccomClass());

            List<AccomType> accomTypesToQuery = getAllRoomTypes().stream().filter(at -> lowerRankedAccomClasses.contains(at.getAccomClass())
                    || higherRankedAccomClasses.contains(at.getAccomClass())
                    || wrapper.getAccomClass().equals(at.getAccomClass())).collect(Collectors.toList());

            // decisions for all room types
            List<CPDecisionBAROutput> decisions = pricingConfigurationService
                    .getCpDecisionBAROutputForAccomTypes(wrapper.getProduct(), accomTypesToQuery,
                            wrapper.getCpDecisionBAROutput().getArrivalDate(), wrapper.getCpDecisionBAROutput().getArrivalDate());

            List<BigDecimal> lowestFinalPrices = new ArrayList<>();
            if (wrapper.getSpecificOverride() != null) {
                lowestFinalPrices = findFinalPricesIfSpecificOverride(wrapper, decisions, wrapper.getSpecificOverride(), cpDecisionContext, new HashSet<>());
            } else if (!(wrapper.getCeilingOverride() == null && wrapper.getFloorOverride() == null)) {
                lowestFinalPrices = findFinalPricesIfFloorCeilingOverride(wrapper, decisions,
                        wrapper.getFloorOverride(), null, wrapper.getCeilingOverride(),
                        getDecisionOverrideTypeFromOverrideWrapper(wrapper), cpDecisionContext);
            }

            if (CollectionUtils.isNotEmpty(lowestFinalPrices)) {
                // list of all room classes that are directly higher and lower
                HashMap<AccomClass, List<BigDecimal>> lowerACMapFinalPrices = getFinalPricesForRelatedAccomClasses(decisions,
                        lowerRankedAccomClasses, wrapper.getCpDecisionBAROutput().getArrivalDate(), cpDecisionContext);
                HashMap<AccomClass, List<BigDecimal>> higherACMapFinalPrices = getFinalPricesForRelatedAccomClasses(decisions,
                        higherRankedAccomClasses, wrapper.getCpDecisionBAROutput().getArrivalDate(), cpDecisionContext);

                if (dailyBARPricingRule == 2) {
                    errorMessages.addAll(getErrorMessageForLowerFinalPriceComparison(wrapper, Collections.singletonList(lowestFinalPrices.get(0)), filterPricesByRuleType(lowerACMapFinalPrices, false)));
                    errorMessages.addAll(getErrorMessageForHigherFinalPriceComparison(wrapper, Collections.singletonList(lowestFinalPrices.get(lowestFinalPrices.size() - 1)), filterPricesByRuleType(higherACMapFinalPrices, true)));
                } else {
                    errorMessages.addAll(getErrorMessageForLowerFinalPriceComparison(wrapper, Collections.singletonList(lowestFinalPrices.get(0)), filterPricesByRuleType(lowerACMapFinalPrices, false)));
                    errorMessages.addAll(getErrorMessageForHigherFinalPriceComparison(wrapper, Collections.singletonList(lowestFinalPrices.get(0)), filterPricesByRuleType(higherACMapFinalPrices, false)));
                }
            }
        }
        return errorMessages;
    }

    private HashMap<AccomClass, BigDecimal> filterPricesByRuleType(HashMap<AccomClass, List<BigDecimal>> roomPriceMap, boolean isHigherAC) {
        HashMap<AccomClass, BigDecimal> filteredMap = new HashMap<>();
        for (Map.Entry<AccomClass, List<BigDecimal>> entry : roomPriceMap.entrySet()) {
            List<BigDecimal> finalPrices = entry.getValue();
            if (dailyBARPricingRule == 2) {
                // use lowest final price for higher ranked RC and highest final price for lower ranked RC
                if (isHigherAC) {
                    filteredMap.put(entry.getKey(), finalPrices.get(0));
                } else {
                    filteredMap.put(entry.getKey(), finalPrices.get(finalPrices.size() - 1));
                }
            } else {
                // rule 3 only uses lowest final prices
                filteredMap.put(entry.getKey(), finalPrices.get(0));
            }
        }
        return filteredMap;
    }

    private List<BigDecimal> findFinalPricesIfSpecificOverride(CPOverrideWrapper wrapper, List<CPDecisionBAROutput> decisions, BigDecimal override, CPDecisionContext cpDecisionContext, Set<AccomType> accomTypesWithOverrides) {
        List<AccomType> wrapperAccomTypes = filterAndGetRoomTypesForRoomClass(wrapper.getAccomClass());
        wrapperAccomTypes.remove(wrapper.getAccomType());
        wrapperAccomTypes.removeAll(accomTypesWithOverrides);
        List<BigDecimal> allFinalPrices = new ArrayList<>();
        allFinalPrices.add(override);
        Integer primaryProductId = cpDecisionContext.getPrimaryProduct(wrapper.getCpDecisionBAROutput().getProduct()).getId();
        if (wrapper.isApplyOverrideAcrossRoomTypes()) {
            wrapperAccomTypes.forEach(at -> {
                BigDecimal priceWithOffset = override.add(cpDecisionContext.getOffset(wrapper.getCpDecisionBAROutput().getArrivalDate(), wrapper.getProduct(), at, wrapper.getSpecificOverride()));
                BigDecimal prettyPrice = cpDecisionContext.calculatePrettyPrice(primaryProductId, priceWithOffset);
                allFinalPrices.add(prettyPrice);
            });
        } else {
            List<BigDecimal> existingFinalPrices = decisions.stream()
                    .filter(decision -> wrapperAccomTypes.contains(decision.getAccomType()) && !accomTypesWithOverrides.contains(decision.getAccomType()))
                    .filter(decision -> wrapper.getCpDecisionBAROutput().getArrivalDate().equals(decision.getArrivalDate()))
                    .map(CPDecisionBAROutput::getFinalBAR)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            existingFinalPrices.forEach(price -> allFinalPrices.add(cpDecisionContext.calculatePrettyPrice(primaryProductId, price)));
        }
        Collections.sort(allFinalPrices);
        return allFinalPrices;
    }

    private List<BigDecimal> findFinalPricesIfFloorCeilingOverride(CPOverrideWrapper wrapper, List<CPDecisionBAROutput> decisions,
                                                                   BigDecimal floorOverride, BigDecimal groupFloorOverride, BigDecimal ceilingOverride,
                                                                   DecisionOverrideType overrideType, CPDecisionContext cpDecisionContext) {
        boolean isGroupFloorOverride = DecisionOverrideType.GPFLOOR.equals(overrideType) || DecisionOverrideType.GPFLOORANDCEIL.equals(overrideType);
        CPOverrideWrapper copyOfWrapper = new CPOverrideWrapper(wrapper);
        copyOfWrapper.getCpDecisionBAROutput().setCeilingOverride(ceilingOverride);
        if (isGroupFloorOverride) {
            copyOfWrapper.getCpDecisionBAROutput().setFloorOverride(groupFloorOverride);
        } else {
            copyOfWrapper.getCpDecisionBAROutput().setFloorOverride(floorOverride);
        }

        BigDecimal roundedBARPrice = cpDecisionContext.calculateRoundedRate(copyOfWrapper.getCpDecisionBAROutput());
        copyOfWrapper.setRoundedBAR(roundedBARPrice);

        List<BigDecimal> allFinalPrices = new ArrayList<>();
        List<AccomType> wrapperAccomTypes = filterAndGetRoomTypesForRoomClass(copyOfWrapper.getAccomClass());
        Integer primaryProductId = cpDecisionContext.getPrimaryProduct(copyOfWrapper.getCpDecisionBAROutput().getProduct()).getId();
        allFinalPrices.add(cpDecisionContext.calculatePrettyPrice(primaryProductId, copyOfWrapper.getRoundedBAR()));
        wrapperAccomTypes.remove(copyOfWrapper.getAccomType());

        if (isBaseRoomType(copyOfWrapper.getAccomType())) {
            allFinalPrices.addAll(calculateFinalPricesForNonBaseRoomTypes(decisions, copyOfWrapper, cpDecisionContext, wrapperAccomTypes));
        }
        Collections.sort(allFinalPrices);

        return allFinalPrices;
    }

    private List<BigDecimal> calculateFinalPricesForNonBaseRoomTypes(List<CPDecisionBAROutput> decisions, CPOverrideWrapper copyOfWrapper,
                                                                     CPDecisionContext cpDecisionContext, List<AccomType> wrapperAccomTypes) {
        List<BigDecimal> allFinalPrices = new ArrayList<>();

        if (!isShowOnlyBaseRoomTypesFlag() || !overrideManager.isPseudoShowOnlyBaseRoomTypesFlag()) {
            List<AccomType> accomTypesWithOverrides = decisions.stream()
                    .filter(decision -> wrapperAccomTypes.contains(decision.getAccomType()) && decision.getOverrideType() != DecisionOverrideType.NONE)
                    .filter(decision -> copyOfWrapper.getCpDecisionBAROutput().getArrivalDate().equals(decision.getArrivalDate()))
                    .map(CPDecisionBAROutput::getAccomType).collect(Collectors.toList());
            wrapperAccomTypes.removeIf(accomTypesWithOverrides::contains);
        }

        Integer primaryProductId = cpDecisionContext.getPrimaryProduct(copyOfWrapper.getCpDecisionBAROutput().getProduct()).getId();
        wrapperAccomTypes.forEach(at -> {
            BigDecimal offset = cpDecisionContext.getOffset(copyOfWrapper.getCpDecisionBAROutput().getArrivalDate(), copyOfWrapper.getProduct(), at, copyOfWrapper.getRoundedBAR());
            BigDecimal prettyPrice = cpDecisionContext.calculatePrettyPrice(primaryProductId, copyOfWrapper.getRoundedBAR().add(offset));
            allFinalPrices.add(prettyPrice);
        });
        return allFinalPrices;
    }

    private List<String> getErrorMessageForLowerFinalPriceComparison(CPOverrideWrapper wrapper, List<BigDecimal> overrideValues, HashMap<AccomClass, BigDecimal> lowerFinalPriceMap) {
        List<String> errorMessages = new ArrayList<>();
        for (Map.Entry<AccomClass, BigDecimal> entry : lowerFinalPriceMap.entrySet()) {
            for (BigDecimal overrideValue : overrideValues) {
                if (overrideValue.compareTo(entry.getValue()) < 0) {
                    errorMessages.add(getText("pricing.override.breaks.price.rank.above", wrapper.getAccomClass().getName(), entry.getKey().getName()));
                    return errorMessages;
                }
            }
        }
        return errorMessages;
    }

    private List<String> getErrorMessageForHigherFinalPriceComparison(CPOverrideWrapper wrapper, List<BigDecimal> overrideValues, HashMap<AccomClass, BigDecimal> higherFinalPriceMap) {
        List<String> errorMessages = new ArrayList<>();
        for (Map.Entry<AccomClass, BigDecimal> entry : higherFinalPriceMap.entrySet()) {
            for (BigDecimal overrideValue : overrideValues) {
                if (overrideValue.compareTo(entry.getValue()) > 0) {
                    errorMessages.add(getText("pricing.override.breaks.price.rank.below", wrapper.getAccomClass().getName(), entry.getKey().getName()));
                    return errorMessages;
                }
            }
        }
        return errorMessages;
    }

    public List<AccomClass> getLowerRankedAccomClasses(List<AccomClassPriceRank> accomClassPriceRanks, AccomClass accomClass) {
        return accomClassPriceRanks.stream()
                .map(AccomClassPriceRank::getLowerRankAccomClass)
                .filter(lowerRankAccomClass -> !lowerRankAccomClass.equals(accomClass))
                .distinct()
                .collect(Collectors.toList());
    }

    public List<AccomClass> getHigherRankedAccomClasses(List<AccomClassPriceRank> accomClassPriceRanks, AccomClass accomClass) {
        return accomClassPriceRanks.stream()
                .map(AccomClassPriceRank::getHigherRankAccomClass)
                .filter(higherRankAccomClass -> !higherRankAccomClass.equals(accomClass))
                .distinct()
                .collect(Collectors.toList());
    }

    private HashMap<List<AccomType>, List<BigDecimal>> getFinalPricesForPendingApplyBAROverrides(LocalDate date, List<AccomType> accomTypes, List<CPDecisionBAROutput> decisions, CPDecisionContext cpDecisionContext) {
        HashMap<List<AccomType>, List<BigDecimal>> map = new HashMap<>();
        List<CPOverrideWrapper> unsavedOverrides = new ArrayList<>(multiProductBARDTOs);
        unsavedOverrides.removeIf(override -> !override.getCpDecisionBAROutput().getArrivalDate().equals(date) || !accomTypes.contains(override.getAccomType()));
        List<BigDecimal> finalPrices = new ArrayList<>();
        List<AccomType> accomTypesToRemove = new ArrayList<>();
        unsavedOverrides.forEach(wrapper -> {
            if (wrapper.getSpecificOverride() != null) {
                finalPrices.addAll(findFinalPricesIfSpecificOverride(wrapper, decisions, wrapper.getSpecificOverride(), cpDecisionContext, new HashSet<>()));
                if (wrapper.isApplyOverrideAcrossRoomTypes()) {
                    accomTypesToRemove.addAll(accomTypes.stream()
                            .filter(at -> at.getAccomClass().equals(wrapper.getAccomClass()))
                            .collect(Collectors.toList()));
                } else {
                    accomTypesToRemove.add(wrapper.getAccomType());
                }
            } else {
                List<BigDecimal> finalPriceList = findFinalPricesIfFloorCeilingOverride(wrapper, decisions, wrapper.getFloorOverride(),
                        wrapper.getGroupFloorOverride(), wrapper.getCeilingOverride(),
                        getDecisionOverrideTypeFromOverrideWrapper(wrapper), cpDecisionContext);
                if (!finalPriceList.isEmpty()) {
                    finalPrices.addAll(finalPriceList);
                    accomTypesToRemove.addAll(accomTypes.stream()
                            .filter(at -> at.getAccomClass().equals(wrapper.getAccomClass()))
                            .collect(Collectors.toList()));
                }
            }
        });
        map.put(accomTypesToRemove, finalPrices);
        return map;
    }

    private List<BigDecimal> updateFinalPricesWithPendingOverrides(HashMap<List<AccomType>, List<BigDecimal>> finalPriceMap) {
        List<BigDecimal> finalPrices = new ArrayList<>();
        for (Map.Entry<List<AccomType>, List<BigDecimal>> entry : finalPriceMap.entrySet()) {
            finalPrices.addAll(entry.getValue());
        }
        return finalPrices;
    }

    private List<AccomType> removeAccomTypesUsedInPendingOverrides(HashMap<List<AccomType>, List<BigDecimal>> finalPriceMap, List<AccomType> currentAccomTypesList) {
        for (Map.Entry<List<AccomType>, List<BigDecimal>> entry : finalPriceMap.entrySet()) {
            currentAccomTypesList.removeAll(entry.getKey());
        }
        return currentAccomTypesList;
    }

    private HashMap<AccomClass, List<BigDecimal>> getFinalPricesForRelatedAccomClasses(List<CPDecisionBAROutput> decisions, List<AccomClass> accomClasses, LocalDate date, CPDecisionContext cpDecisionContext) {
        HashMap<AccomClass, List<BigDecimal>> finalPriceMap = new HashMap<>();
        accomClasses.forEach(accomClass -> {
            List<BigDecimal> finalPrices = new ArrayList<>();
            List<AccomType> accomTypes = filterAndGetRoomTypesForRoomClass(accomClass);

            // pending overrides that haven't been applied yet take precedence
            HashMap<List<AccomType>, List<BigDecimal>> map = getFinalPricesForPendingApplyBAROverrides(date, accomTypes, decisions, cpDecisionContext);
            accomTypes = removeAccomTypesUsedInPendingOverrides(map, accomTypes);
            finalPrices = updateFinalPricesWithPendingOverrides(map);

            // pending overrides that haven't been saved yet will be considered next
            List<CPOverrideWrapper> pendingOverrides = new ArrayList<>(overrideManager.getOverridesMap().values());
            Set<AccomType> accomTypesWithPendingOverrides = pendingOverrides.stream().filter(cpOverrideWrapper -> cpOverrideWrapper.getCpDecisionBAROutput().getArrivalDate().equals(date)).map(CPOverrideWrapper::getAccomType).collect(Collectors.toSet());
            List<AccomType> finalAccomTypes = accomTypes;
            pendingOverrides.removeIf(override -> !override.getProduct().isSystemDefaultOrIndependentProduct()
                    || !finalAccomTypes.contains(override.getAccomType()) || !date.equals(override.getCpDecisionBAROutput().getArrivalDate()));
            List<BigDecimal> finalPrices1 = finalPrices;
            List<AccomType> finalAccomTypes1 = accomTypes;
            pendingOverrides.forEach(wrapper -> {
                if (wrapper.getSpecificOverride() != null) {
                    finalPrices1.addAll(findFinalPricesIfSpecificOverride(wrapper, decisions, wrapper.getSpecificOverride(), cpDecisionContext, accomTypesWithPendingOverrides));
                    if (wrapper.isApplyOverrideAcrossRoomTypes()) {
                        finalAccomTypes1.removeIf(at -> at.getAccomClass().equals(wrapper.getAccomClass()));
                    } else {
                        finalAccomTypes1.remove(wrapper.getAccomType());
                    }
                } else {
                    List<BigDecimal> finalPriceList = findFinalPricesIfFloorCeilingOverride(wrapper, decisions, wrapper.getFloorOverride(),
                            wrapper.getGroupFloorOverride(), wrapper.getCeilingOverride(),
                            getDecisionOverrideTypeFromOverrideWrapper(wrapper), cpDecisionContext);
                    if (!finalPriceList.isEmpty()) {
                        finalPrices1.addAll(finalPriceList);
                        finalAccomTypes1.removeIf(at -> at.getAccomClass().equals(wrapper.getAccomClass()));
                    }
                }
            });

            // finally, if no overrides exist look at existing decision's final price
            List<AccomType> finalAccomTypes2 = accomTypes;
            finalPrices.addAll(decisions.stream()
                    .filter(decision -> finalAccomTypes2.contains(decision.getAccomType()))
                    .filter(decision -> decision.getArrivalDate().equals(date))
                    .map(CPDecisionBAROutput::getFinalBAR)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList()));
            Collections.sort(finalPrices);
            if (CollectionUtils.isNotEmpty(finalPrices)) {
                finalPriceMap.put(accomClass, finalPrices);
            }
        });
        return finalPriceMap;
    }

    public boolean isShowAdjustments() {
        return showAdjustments;
    }

    public void setShowAdjustments(boolean showAdjustments) {
        this.showAdjustments = showAdjustments;
    }

    public List<CPOverrideWrapper> getNonBaseRoomTypes(CPOverrideWrapper wrapper, PricingAccomClass masterPricingAccomClass) {
        return overrideManager.getNonBaseRoomTypes(wrapper, masterPricingAccomClass, uiContext.getSystemCaughtUpDate());
    }

    public List<InventoryGroupDto> getInventoryGroups() {
        if(isPricingScreenOptimizationEnabled){
            List<InventoryGroup> igList = inventoryGroupService.getInventoryGroupsWithDetailsAndAccomClasses();
            inventoryGroupDetails = igList.stream()
                    .flatMap(ig -> ig.getInventoryGroupDetails().stream())
                    .collect(Collectors.toList());
            return InventoryGroupUtil.getInventoryGroups(userPreferences, igList);
        }
        inventoryGroupDetails = inventoryGroupService.getInventoryGroupDetails();
        return InventoryGroupUtil.getInventoryGroups(userPreferences, inventoryGroupService.getInventoryGroups());
    }

    public boolean isInventoryGroupSelectionShown() {
        return !inventoryGroupService.getInventoryGroups().isEmpty();
    }

    public List<AccomClass> getAccomClassByInventoryGroup(int inventoryGroupId) {
        if (inventoryGroupId == -1) {
            return roomClassList;
        } else {
            List<AccomClass> selectedAccomClasses = inventoryGroupDetails.stream().filter(inventoryGroupDetail -> inventoryGroupDetail.getInventoryGroup().getId() == inventoryGroupId).map(InventoryGroupDetails::getAccomClass).collect(Collectors.toList());
            overrideManager.setSelectedAccomTypes(selectedAccomClasses.stream().map(AccomClass::getAccomTypes).flatMap(Collection::stream).collect(Collectors.toList()));
            selectedAccomClasses.add(0, allRoomClasses);
            return selectedAccomClasses;
        }
    }

    public void setInventoryGroups() {
        inventoryGroups = getInventoryGroups();
        Optional<InventoryGroupDto> defaultInventoryGroupCheck = inventoryGroups.stream().filter(inventoryGroupDto -> inventoryGroupDto.isDefaultPropertyInventoryGroup()).findFirst();
        defaultInventoryGroup = (defaultInventoryGroupCheck.isPresent()) ? defaultInventoryGroupCheck.get() : new InventoryGroupDto(-1, "Property", true);
        view.setInventoryGroups(inventoryGroups);
    }

    public void checkLastSelectedInventoryGroup() {
        if (lastSelectedInventoryGroup != null) {
            Optional<InventoryGroupDto> matchingInventoryGroup = inventoryGroups.stream().filter(ig -> ig.getName().equals(lastSelectedInventoryGroup.getName())).findFirst();
            if (matchingInventoryGroup.isPresent()) {
                defaultInventoryGroup = matchingInventoryGroup.get();
            }
        }
    }

    @ForTesting
    public void setInventoryGroups(List<InventoryGroupDto> inventoryGroupDtos) {
        this.inventoryGroups = inventoryGroupDtos;
    }

    @ForTesting
    public void setInventoryGroupDetails(List<InventoryGroupDetails> inventoryGroupDetails) {
        this.inventoryGroupDetails = inventoryGroupDetails;
    }

    @ForTesting
    public void setCpDecisionContext(CPDecisionContext cpDecisionContext) {
        this.cpDecisionContext = cpDecisionContext;
    }

    public boolean isReadOnly() {
        return isReadOnly;
    }

    public boolean anyPendingOverridesForSameDay(CPOverrideWrapper overrideWrapper, List<CPOverrideWrapper> cpOverrideWrappers) {
        return cpOverrideWrappers.stream()
                .filter(wrapper -> wrapper != overrideWrapper)
                .anyMatch(wrapper -> wrapper.getCpDecisionBAROutput().getArrivalDate().equals(overrideWrapper.getCpDecisionBAROutput().getArrivalDate()));
    }

    public boolean isOverrideWithinRestrictedRange(PricingAgileRatesOverrideDTO dto, BigDecimal floorValue, BigDecimal ceilingValue, boolean isApplyToAllRoomClasses) {
        if (!dto.getProduct().getIsOverridable().equals(OverridableProductEnum.RESTRICT_OVERRIDES)) {
            return true;
        }
        if(dto.isCeilingMarkedForDeletion() || dto.isFloorMarkedForDeletion()) {
            return true;
        }
        if (allProductRateOffsets.isEmpty()) {
            allProductRateOffsets = agileRatesConfigurationService.findAllProductRateOffsets();
        }
        List<ProductRateOffset> offsetsForProduct = allProductRateOffsets.stream()
                .filter(offset -> offset.getProduct().equals(dto.getProduct()))
                .collect(Collectors.toList());

        if (dto.getProduct().isRoomClassOffset() && !isApplyToAllRoomClasses) {
            offsetsForProduct = offsetsForProduct.stream()
                    .filter(offset -> offset.getAccomClass().equals(dto.getRoomClass()))
                    .collect(Collectors.toList());
        }
        if (dto.getProduct().isDtaOffset()) {
            AgileRatesDTARange dtaRangeForDate = getDtaRangeForSelectedDate(dto.getDate());
            offsetsForProduct = offsetsForProduct.stream()
                    .filter(offset -> offset.getAgileRatesDTARange().equals(dtaRangeForDate))
                    .collect(Collectors.toList());
        }

        List<ProductRateOffset> filterOffsetsForProduct = offsetsForProduct.stream().filter(o -> this.isAfterOrEqual(o.getStartDate(), dto.getDate())
                        && this.isBeforeOrEqual(o.getEndDate(), dto.getDate()))
                .collect(Collectors.toList());

        if (filterOffsetsForProduct.isEmpty()) {
            filterOffsetsForProduct = offsetsForProduct.stream().filter(o -> o.getStartDate() == null)
                    .collect(Collectors.toList());
        }

        if (isApplyToAllRoomClasses) {
            for (ProductRateOffset offset : filterOffsetsForProduct) {
                List<BigDecimal> floorAndCeilingOffsets = getFloorAndCeilingOffsetGivenDOW(dto.getDate().getDayOfWeek(), offset);
                if ((!floorAndCeilingOffsets.isEmpty() && filterOffsetsForProduct.get(0) != null) && !(BigDecimalUtil.isGreaterThanOrEqualTo(floorValue, floorAndCeilingOffsets.get(0))
                        && BigDecimalUtil.isLessThanOrEqualTo(ceilingValue, floorAndCeilingOffsets.get(1)))) {
                    return false;
                }
            }
            return true;
        }

        List<BigDecimal> floorAndCeilingOffsets = getFloorAndCeilingOffsetGivenDOW(dto.getDate().getDayOfWeek(), filterOffsetsForProduct.get(0));
        if (floorValue != null && ceilingValue != null) {
            return BigDecimalUtil.isLessThanOrEqualTo(floorAndCeilingOffsets.get(0), floorValue) && BigDecimalUtil.isGreaterThanOrEqualTo(floorAndCeilingOffsets.get(1), ceilingValue);
        } else if (floorValue == null) {
            return BigDecimalUtil.isLessThanOrEqualTo(floorAndCeilingOffsets.get(0), ceilingValue) && BigDecimalUtil.isGreaterThanOrEqualTo(floorAndCeilingOffsets.get(1), ceilingValue);
        } else {
            return BigDecimalUtil.isLessThanOrEqualTo(floorAndCeilingOffsets.get(0), floorValue) && BigDecimalUtil.isGreaterThanOrEqualTo(floorAndCeilingOffsets.get(1), floorValue);
        }
    }

    public boolean areOverridesWithinRestrictedRangeMultiday(PricingMultidayGridDTO dto, BigDecimal floorOverride, BigDecimal ceilingOverride, List<Integer> selectedDOWs, PricingMultiProductMultidayFilterDTO filterBinder) {
        if (!dto.getProduct().getIsOverridable().equals(OverridableProductEnum.RESTRICT_OVERRIDES)) {
            return true;
        }
        if (allProductRateOffsets.isEmpty()) {
            allProductRateOffsets = agileRatesConfigurationService.findAllProductRateOffsets();
        }

        List<ProductRateOffset> offsetsForProduct = allProductRateOffsets.stream()
                .filter(offset -> offset.getProduct().equals(dto.getProduct()))
                .collect(Collectors.toList());

        List<AccomClass> roomClasses = isAgileRatesAndApplyToAllRoomClasses(dto.getProduct()) ? findSortedRoomClassesForProduct(dto.getProduct()) : asList(dto.getRoomClass());
        List<AgileRatesDTARange> productDTARanges = getDtaRangesForSelectedDatesMultiday(DateUtil.convertLocalDateToJodaLocalDate(filterBinder.getStartDate()),
                DateUtil.convertLocalDateToJodaLocalDate(filterBinder.getEndDate()), dto.getProduct());

        if (dto.getProduct().isRoomClassOffset() && !isAgileRatesAndApplyToAllRoomClasses(dto.getProduct())) {
            offsetsForProduct = offsetsForProduct.stream()
                    .filter(offset -> offset.getAccomClass().equals(dto.getRoomClass()))
                    .collect(Collectors.toList());
        }
        if (dto.getProduct().isDtaOffset()) {
            offsetsForProduct = offsetsForProduct.stream()
                    .filter(offset -> productDTARanges.contains(offset.getAgileRatesDTARange()))
                    .collect(Collectors.toList());
        }

        LocalDate stDate = DateUtil.convertLocalDateToJodaLocalDate(filterBinder.getStartDate());
        LocalDate edDate = DateUtil.convertLocalDateToJodaLocalDate(filterBinder.getEndDate());

        //find all season which are overlapping with the provided date range
        List<ProductRateOffset> filterOffsetsForProduct = offsetsForProduct.stream().filter(o -> this.isAfterOrEqual(stDate, o.getEndDate())
                        && this.isBeforeOrEqual(edDate, o.getStartDate()))
                .collect(Collectors.toList());
        //if no overlapping then default will be in effect
        if (filterOffsetsForProduct.isEmpty()) {
            filterOffsetsForProduct = offsetsForProduct.stream().filter(o -> o.getStartDate() == null)
                    .collect(Collectors.toList());
        }

        for (ProductRateOffset offset : filterOffsetsForProduct) {
            for (Integer dow : selectedDOWs) {
                List<BigDecimal> floorAndCeilingOffsets = getFloorAndCeilingOffsetGivenDOW(dow, offset);
                if ((!floorAndCeilingOffsets.isEmpty() && floorAndCeilingOffsets.get(0) != null) && !(BigDecimalUtil.isGreaterThanOrEqualTo(floorOverride, floorAndCeilingOffsets.get(0))
                        && BigDecimalUtil.isLessThanOrEqualTo(ceilingOverride, floorAndCeilingOffsets.get(1)))) {
                    return false;
                }
            }
        }
        return true;
    }

    private boolean isBeforeOrEqual(LocalDate date, LocalDate compareToDate) {
        if (date == null || compareToDate == null) {
            return false;
        }
        return compareToDate.isBefore(date) || compareToDate.isEqual(date);
    }

    private boolean isAfterOrEqual(LocalDate date, LocalDate compareToDate) {
        if (date == null || compareToDate == null) {
            return false;
        }
        return compareToDate.isAfter(date) || compareToDate.isEqual(date);
    }

    public List<BigDecimal> getFloorAndCeilingOffsetGivenDOW(Integer dow, ProductRateOffset offset) {
        BigDecimal floorOffset;
        BigDecimal ceilingOffset;
        switch (dow) {
            case (1):
                floorOffset = offset.getMondayOffsetValueFloor();
                ceilingOffset = offset.getMondayOffsetValueCeiling();
                break;
            case (2):
                floorOffset = offset.getTuesdayOffsetValueFloor();
                ceilingOffset = offset.getTuesdayOffsetValueCeiling();
                break;
            case (3):
                floorOffset = offset.getWednesdayOffsetValueFloor();
                ceilingOffset = offset.getWednesdayOffsetValueCeiling();
                break;
            case (4):
                floorOffset = offset.getThursdayOffsetValueFloor();
                ceilingOffset = offset.getThursdayOffsetValueCeiling();
                break;
            case (5):
                floorOffset = offset.getFridayOffsetValueFloor();
                ceilingOffset = offset.getFridayOffsetValueCeiling();
                break;
            case (6):
                floorOffset = offset.getSaturdayOffsetValueFloor();
                ceilingOffset = offset.getSaturdayOffsetValueCeiling();
                break;
            case (7):
                floorOffset = offset.getSundayOffsetValueFloor();
                ceilingOffset = offset.getSundayOffsetValueCeiling();
                break;
            default:
                floorOffset = new BigDecimal(0);
                ceilingOffset = new BigDecimal(0);
        }
        return (asList(floorOffset, ceilingOffset));
    }

    public boolean isIndependentProductsEnabled() {
        return isIndependentProductsEnabled;
    }

    public boolean isRDLEnabled() {
        return configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.IS_RDL_ENABLED);
    }

    public boolean isSmallGroupProductsEnabled() {
        return isSmallGroupProductsEnabled;
    }
    public boolean isGroupProductInventoryLimitEnabled() {
        return isGroupProductInventoryLimitEnabled;
    }
    public boolean atLeastOneGroupProductSelected(){
        List<Product> groupProductList = getCpPricingFilter().getProducts()
                .stream()
                .filter(Product::isGroupProduct).collect(Collectors.toList());
        return !groupProductList.isEmpty();
    }
    public boolean atLeastOneSmallGroupWrapperAvailable(CPBARDecisionUIWrapper wrapper) {
        return wrapper.getCpOverrideWrappers().stream().anyMatch(CPOverrideWrapper::isSmallGroupProduct);
    }
    public String getInventoryLimitValueByDate(Date date){
        return inventoryLimitMap.getOrDefault(date, HYPHEN);
    }
    @ForTesting
    public void setInventoryLimitMap(Map<Date, String> inventoryLimitMap) {
        this.inventoryLimitMap = inventoryLimitMap;
    }

    public Product getBARProduct() {
        return barProduct;
    }

    public Product getPrimaryProduct(Product product) {
        if (product.isSystemDefaultOrIndependentProduct()) {
            return product;
        }
        Product parentProduct = allProducts
                .stream()
                .filter(p -> p.getId().equals(product.getDependentProductId()))
                .findFirst()
                .orElse(barProduct);
        return getPrimaryProduct(parentProduct);
    }

    public String getAccomClassNameById(Integer accomClassId) {
        AccomClass accomClass = roomClassList.stream()
                .filter(item -> item.getId().equals(accomClassId))
                .findFirst()
                .orElse(null);

        return accomClass != null ? accomClass.getName() : "Accom Class Not Found";
    }

    public String getProductNameById(Integer productId) {
        Product product = allProducts.stream()
                .filter(item -> item.getId().equals(productId))
                .findFirst()
                .orElse(null);

        return product != null ? product.getName() : "Product Not Found";
    }

    public String getBaseRoomTypeByAccomClassId(Integer accomClassId) {
        PricingAccomClass pricingAccomClass = pricingAccomClasses.stream()
                .filter(item -> item.getAccomClass().getId().equals(accomClassId))
                .findFirst()
                .orElse(null);
        return pricingAccomClass != null ? pricingAccomClass.getAccomType().getName() : "Base Room Type Not Found";
    }

    public void sortPricingInlineEditColumns(ArrayList<PricingInlineEditColumn> columns) {
        columns.sort(Comparator.comparing(PricingInlineEditColumn::getDisplayOrder));

        for (PricingInlineEditColumn column : columns) {
            List<PricingInlineEditColumn> children = column.getChildren();
            if (CollectionUtils.isNotEmpty(children)) {
                children.sort(Comparator.comparing(PricingInlineEditColumn::getProductDisplayOrder)
                        .thenComparing(PricingInlineEditColumn::isCompetitor)
                        .thenComparing(PricingInlineEditColumn::getDisplayOrder));
            }
        }
    }

    public boolean shouldDisplayReadOnlyNotification() {
        return uiContext.getPropertyState().isShowReadOnlyNotification();
    }

    public boolean isSystemDefaultProductSelected(PricingMultiProductMultidayFilterDTO dto) {
        return dto.getProducts().stream().anyMatch(product -> product.isSystemDefault());
    }

    public List<BrokenHierarchyDetails> retrieveBrokenHierarchies(List<PricingMultidayGridDTO> overrides,
                                                                  Set<DayOfWeek> selectedDOW,
                                                                  java.time.LocalDate startDate,
                                                                  java.time.LocalDate endDate) {
        Set<Integer> productIds = overrides.stream()
                .filter(dto -> !isPricingMultidayGridDTOEmpty(dto))
                .map(PricingMultidayGridDTO::getProduct)
                .filter(Product::isSystemDefaultOrIndependentProduct)
                .map(Product::getId)
                .collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(productIds)) {
            return Collections.emptyList();
        }
        List<CeilingFloorDetails> specificCeilingFloors = mapOverridesToSpecificCeilingFloor(overrides);
        List<java.time.LocalDate> selectedDates = constructDatesAccordingToSelectedDOW(startDate, endDate, selectedDOW);
        return agileRatesConfigurationService.handleBrokenProductHierarchies(productIds, specificCeilingFloors, selectedDates);
    }

    private List<java.time.LocalDate> constructDatesAccordingToSelectedDOW(java.time.LocalDate startDate,
                                                                           java.time.LocalDate endDate,
                                                                           Set<DayOfWeek> selectedDOW) {
        return startDate.datesUntil(endDate.plusDays(1))
                .filter(date -> {
                    if (CollectionUtils.isEmpty(selectedDOW)) {
                        return true;
                    }
                    return selectedDOW.stream()
                            .map(DayOfWeek::getCalendarDayOfWeek)
                            .anyMatch(ordinalDOW -> ordinalDOW.equals(date.getDayOfWeek().getValue()));
                })
                .collect(Collectors.toList());
    }

    private List<CeilingFloorDetails> mapOverridesToSpecificCeilingFloor(List<PricingMultidayGridDTO> overrides) {
        return overrides.stream()
                .filter(dto -> !isPricingMultidayGridDTOEmpty(dto))
                .map(this::constructSpecificCeilingFloorOverride)
                .collect(Collectors.toList());
    }

    private boolean isPricingMultidayGridDTOEmpty(PricingMultidayGridDTO pricingMultidayGridDTO) {
        return Objects.isNull(pricingMultidayGridDTO.getCeilingOverride()) &&
                Objects.isNull(pricingMultidayGridDTO.getFloorOverride()) &&
                Objects.isNull(pricingMultidayGridDTO.getSpecificOverride());
    }

    private CeilingFloorDetails constructSpecificCeilingFloorOverride(PricingMultidayGridDTO override) {
        CeilingFloorDetails specificCeilingFloor = new CeilingFloorDetails();
        specificCeilingFloor.setAccomType(override.getRoomType());
        specificCeilingFloor.setProductId(override.getProduct().getId());
        specificCeilingFloor.setCeiling(override.getCeilingOverride());
        specificCeilingFloor.setFloor(override.getFloorOverride());
        return specificCeilingFloor;
    }

    public void removeHierarchies(List<ProductHierarchy> hierarchies) {
        agileRatesConfigurationService.removeHierarchies(hierarchies);
    }

    public boolean valueMustBeNegative(String value) {
        //(-100, 0]
        return org.apache.commons.lang.math.NumberUtils.isNumber(value) && (new BigDecimal(value).compareTo(new BigDecimal(0)) <= 0)
                && (new BigDecimal(value).compareTo(new BigDecimal(-100)) > 0);
    }

    public boolean isNumberNegative(BigDecimal number) {
        //(-100, 0]
        if (number == null) {
            return false;
        }
        return (number.compareTo(new BigDecimal(0)) <= 0) && (number.compareTo(new BigDecimal(-100)) > 0);
    }

    public boolean isAveragePriceEnabled() {
        return isAveragePriceEnabled;
    }

    public void setAveragePriceEnabled(boolean averagePriceEnabled) {
        isAveragePriceEnabled = averagePriceEnabled;
    }


    @ForTesting
    void setSystemPriceEnabled(boolean systemPriceEnabled) {
        isSystemPriceEnabled = systemPriceEnabled;
    }

    @ForTesting
    public void setExtendedWindowEnabled(boolean extendedWindowEnabled) {
        isExtendedWindowEnabled = extendedWindowEnabled;
    }

    @ForTesting
    public void setOptimizationWindowEndDateBDE(Date optimizationWindowEndDateBDE) {
        this.optimizationWindowEndDateBDE = optimizationWindowEndDateBDE;
    }

    public boolean isSystemPriceEnabled() {
        return isSystemPriceEnabled;
    }

    public boolean isFixedAboveBARProductEnabled() {
        return isFixedAboveBARProductEnabled;
    }

    public void setFixedAboveBARProductEnabled(boolean fixedAboveBARProductEnabled) {
        this.isFixedAboveBARProductEnabled = fixedAboveBARProductEnabled;
    }

    public boolean isRateProtectedPrice(CPOverrideWrapper overrideWrapper) {
        return overrideWrapper.getDecisionReasonType() != null
                && DecisionReasonType.BASE_PRODUCT_PRICE_BELOW_FAB_PRODUCTS_RATE.equals(overrideWrapper.getDecisionReasonType());
    }

    public boolean hideDatesWithDropdownDataAsPerExtendedWindow() {
        if (isExtendedWindowEnabled) {
            CPPricingFilter cpPricingFilter = getCpPricingFilter();
            LocalDate startDate = cpPricingFilter.getStartDate();
            LocalDate endDate = cpPricingFilter.getEndDate();
            return ObjectUtils.isNotEmpty(optimizationWindowEndDateBDE) && (DateUtil.isDateInFuture(LocalDateUtils.fromDate(optimizationWindowEndDateBDE), startDate) || DateUtil.isDateInFuture(LocalDateUtils.fromDate(optimizationWindowEndDateBDE), endDate));
        }
        return false;
    }

    public List<DateWithFilterEnum> removeItemsToMeetExtendedWindowCriteria(List<DateWithFilterEnum> dateWithFilterEnums) {
        if (!dateWithFilterEnums.isEmpty()) {
            return filterOutItemsNotEligibleForExtendedWindow(dateWithFilterEnums);
        }
        return dateWithFilterEnums;
    }

    private List<DateWithFilterEnum> filterOutItemsNotEligibleForExtendedWindow(List<DateWithFilterEnum> dateWithFilterEnums) {
        return dateWithFilterEnums.stream()
                .filter(
                        getDateWithFilter(CHANGES_SINCE)
                                .and(getDateWithFilter(CHANGES_SINCE_LAST_IDP))
                                .and(getDateWithFilter(CHANGES_SINCE_LAST_BDE))
                                .and(getDateWithFilter(OCCUPANCY_FORECAST_GREATER_THAN))
                                .and(getDateWithFilter(OCCUPANCY_FORECAST_LESS_THAN))
                ).collect(Collectors.toList());

    }

    private Predicate<DateWithFilterEnum> getDateWithFilter(DateWithFilterEnum filterOutThisItem) {
        return dateWithFilterEnum -> !dateWithFilterEnum.equals(filterOutThisItem);
    }

    public boolean isSelectedItemNotEligibleForExtendedWindow(DateWithFilterEnum dateWithFilterEnum) {
        List<DateWithFilterEnum> selectedItemNotEligibleForExtendedWindow = removeItemsToMeetExtendedWindowCriteria(List.of(dateWithFilterEnum));
        return selectedItemNotEligibleForExtendedWindow.isEmpty();
    }

    public boolean isSupplementValueGreaterThanOverrideValue(Map<AccomTypeSupplementValuePK, AccomTypeSupplementValue> supplementValues,
                                                             java.time.LocalDate overrideStartDate,
                                                             java.time.LocalDate overrideEndDate,
                                                             List<Integer> selectedDayOfWeek,
                                                             Integer accomTypeId,
                                                             BigDecimal value,
                                                             Integer productId) {
        return pricingConfigValidatorAgainstSupplement.isSupplementGreaterThanOverrideValue(supplementValues,
                overrideStartDate,
                overrideEndDate,
                selectedDayOfWeek,
                accomTypeId,
                value,
                productId);
    }

    public PricingRule getPricingRuleForWrapper(CPOverrideWrapper overrrideWrapper)
    {
        return prettyPricingService.getPricingRule(overrrideWrapper.getProduct().getId());
    }
    @VisibleForTesting
    public void setIndependentProductsEnabled(boolean isIndependentProductEnabled)
    {
        isIndependentProductsEnabled = isIndependentProductEnabled;
    }

    @VisibleForTesting
    public void setSmallGroupProductsEnabled(boolean isSmallGroupProductEnabled)
    {
        isSmallGroupProductsEnabled = isSmallGroupProductEnabled;
    }

    public String getRoundingRoleValidation() {
        return ROUNDING_ROLE_NOT_SATISFIED;
    }

    public String getRulesSatisfied() {
        return ALL_RULES_SATISFIED;
    }

    public boolean isAvailableCapacityToSellEnabled() {
        return isAvailableCapacityToSellEnabled;
    }
}
