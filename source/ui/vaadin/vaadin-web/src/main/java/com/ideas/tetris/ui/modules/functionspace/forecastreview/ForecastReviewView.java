package com.ideas.tetris.ui.modules.functionspace.forecastreview;


import com.ideas.infra.tetris.security.domain.TetrisPermissionKey;
import com.ideas.tetris.pacman.services.functionspace.dto.FunctionSpaceDemandCalendarDateStatus;
import com.ideas.tetris.pacman.services.functionspace.dto.FunctionSpaceDemandCalendarForecastLevel;
import com.ideas.tetris.ui.common.cdi.TetrisNavigatorView;
import com.ideas.tetris.ui.common.component.TetrisEnumContainer;
import com.ideas.tetris.ui.common.component.TetrisHierarchicalBeanItemContainer;
import com.ideas.tetris.ui.common.component.TetrisLabel;
import com.ideas.tetris.ui.common.component.TetrisNotification;
import com.ideas.tetris.ui.common.component.button.TetrisButton;
import com.ideas.tetris.ui.common.component.button.TetrisEditButton;
import com.ideas.tetris.ui.common.component.button.TetrisExportXlsImageButton;
import com.ideas.tetris.ui.common.component.button.TetrisImageButton;
import com.ideas.tetris.ui.common.component.button.TetrisLinkButton;
import com.ideas.tetris.ui.common.component.fontawesome.TetrisFontAwesome;
import com.ideas.tetris.ui.common.component.note.TetrisNoteImageButton;
import com.ideas.tetris.ui.common.component.panel.PanelBar;
import com.ideas.tetris.ui.common.component.select.ItemCaptionGenerator;
import com.ideas.tetris.ui.common.component.select.TetrisEnumCombobox;
import com.ideas.tetris.ui.common.component.textfield.TetrisBigDecimalField;
import com.ideas.tetris.ui.common.util.ChangeAware;
import com.ideas.tetris.ui.common.util.DateFormatUtil;
import com.ideas.tetris.ui.common.util.UiUtils;
import com.ideas.tetris.ui.modules.functionspace.forecastreview.filter.ForecastReviewFilterComponent;
import com.ideas.tetris.ui.modules.functionspace.forecastreview.filter.ForecastReviewTreeTable;
import com.ideas.tetris.ui.modules.reports.BaseVaadinExporter;
import com.ideas.tetris.ui.modules.reports.ExportProvider;
import com.ideas.tetris.ui.modules.reports.util.ReportCriteria;
import com.vaadin.cdi.CDIView;
import com.vaadin.ui.AbstractOrderedLayout;
import com.vaadin.ui.Alignment;
import com.vaadin.ui.Button;
import com.vaadin.ui.CssLayout;
import com.vaadin.ui.UI;
import com.vaadin.ui.themes.ValoTheme;
import com.vaadin.v7.data.Property;
import com.vaadin.v7.ui.AbstractSelect;
import com.vaadin.v7.ui.HorizontalLayout;
import com.vaadin.v7.ui.Label;
import com.vaadin.v7.ui.Table;
import com.vaadin.v7.ui.VerticalLayout;

import java.util.ArrayList;
import java.util.List;

@CDIView(uis = UI.class, value = TetrisPermissionKey.FUNCTION_SPACE_FORECAST_REVIEW)
public class ForecastReviewView extends TetrisNavigatorView<ForecastReviewPresenter, Void> {

    private TetrisHierarchicalBeanItemContainer<ForecastReviewUiWrapper> container;
    private PanelBar panelBar;
    private boolean isRowEditable = true;
    private ForecastReviewTreeTable table;
    private ForecastReviewFilterComponent filterComponent;

    @Override
    protected void initView() {
        this.addStyleName("functionspace-functionspace-forecastreview");
        setSizeFull();
        VerticalLayout layout = new VerticalLayout();
        layout.setMargin(true);
        layout.setSpacing(true);
        layout.setSizeFull();

        panelBar = new PanelBar(PanelBar.PanelBarPosition.HEADER);
        panelBar.setTitle(UiUtils.getText("search"));
        layout.addComponent(panelBar);
        addResultTable(layout);
        addLegend(layout);
        setCompositionRoot(layout);
    }

    private void addLegend(VerticalLayout layout) {
        CssLayout grid = new CssLayout();
        grid.setWidth(100, Unit.PERCENTAGE);

        Label forecastOverride = new Label(" " + UiUtils.getText("forecast"));
        forecastOverride.addStyleName("forecastOverride");
        grid.addComponent(forecastOverride);
        layout.addComponent(grid);
    }

    @Override
    public void onPresenterInit() {
        filterComponent = new ForecastReviewFilterComponent(presenter);
        panelBar.setContent(filterComponent);
        panelBar.setOpen(true);
    }

    private void addResultTable(VerticalLayout layout) {
        container = new TetrisHierarchicalBeanItemContainer<ForecastReviewUiWrapper>(ForecastReviewUiWrapper.class);

        String dow = "dow";
        String occupancyDate = "occupancyDate";
        String forecastVariance = "forecastVariance";
        String dayPartName = "functionSpaceDayPartName";
        String otbUtilization = "otbUtilization";
        String status = "status";
        String userAdjustedUtilization = "userAdjustedUtilization";
        String userAdjustedForecastLevel = "userAdjustedLevel";

        //additional columns
        String totalGuestRoomUnconstraintedDemand = "totalGuestRoomUnconstraintedDemand";
        String groupGuestRoomForecast = "groupGuestRoomForecast";
        String groupGuestRoomOnBooks = "groupGuestRoomOnBooks";
        String groupGuestRoomRemainingDemand = "groupGuestRoomRemainingDemand";
        String transientGuestRoomForecast = "transientGuestRoomForecast";
        String transientGuestRoomOnBooks = "transientGuestRoomOnBooks";
        String transientGuestRoomRemainingDemand = "transientGuestRoomRemainingDemand";
        String groupForecastAchievedPercentage = "groupForecastAchievedPercentage";
        String totalGuestRoomOnBooks = "totalGuestRoomOnBooks";
        String totalGuestRoomForecast = "totalGuestRoomForecast";
        String onTheBooksUtilizationWithProspects = "onTheBooksUtilizationWithProspects";
        String numberOfIndivisibleRoomsOnBooks = "numberOfIndivisibleRoomsOnBooks";

        String notesId = "notes";
        String rowActionId = "ROW_EDIT_ACTION_COLUMN_ID";

        table = new ForecastReviewTreeTable();
        table.setId("forecastReviewTreeTable");
        table.setContainerDataSource(container);
        table.setSizeFull();
        table.setTwoHeaderRow();

        table.addGeneratedColumn(occupancyDate, new Table.ColumnGenerator() {
            @Override
            public Object generateCell(Table source, Object itemId, Object columnId) {
                final ForecastReviewUiWrapper wrapper = (ForecastReviewUiWrapper) itemId;
                final HorizontalLayout horizontalLayout = new HorizontalLayout();
                String dateString = DateFormatUtil.formatDateFor(wrapper.getOccupancyDate(), UiUtils.getTetrisUi().getLocale());
                TetrisLabel dateLabel = new TetrisLabel(dateString);
                horizontalLayout.addComponent(dateLabel);

                if (!wrapper.isParent()) {
                    dateLabel.addStyleName("hideDate");//we want to hide the date, but keep its occupied space so the remove button aligns with the day level remove
                }

                if (wrapper.isHasUserForecastOverride()) {
                    Label forecastOverrideLabel = getOverrideIcon(wrapper);
                    horizontalLayout.addComponent(forecastOverrideLabel);
                    wrapper.setOverrideIcon(forecastOverrideLabel);
                    final TetrisLinkButton linkButton = new TetrisLinkButton(UiUtils.getText("common.remove"), new Button.ClickListener() {
                        @Override
                        public void buttonClick(Button.ClickEvent event) {
                            ForecastReviewUiWrapper parent = wrapper;
                            if (hasChanges()) {
                                TetrisNotification.showWarningMessage(UiUtils.getText("common.warning.msg.unsaved.changes.before.edit"));
                                return; //do nothing if there is change
                            }
                            if (!wrapper.isParent()) {
                                parent = wrapper.getParent();
                            }
                            if (parent.isReadOnly()) {
                                parent.setReadOnly(false); //make it editable
                                table.setCollapsed(parent, false);//expand
                                addSaveCancelButtons(parent);
                            }
                            processRemoveOverride(wrapper);
                        }
                    });
                    linkButton.setId("linkButton");
                    wrapper.setRemoveLinkButton(linkButton);

                    linkButton.setEnabledRequirements(true, TetrisPermissionKey.FUNCTION_SPACE_FORECAST_REVIEW);

                    if (presenter.isFutureDate(wrapper.getOccupancyDate())) {
                        horizontalLayout.addComponent(linkButton);
                    }
                }
                return horizontalLayout;
            }
        });

        table.addGeneratedColumn(userAdjustedUtilization, new ForecastReviewTreeTableColumnGenerator() {
            @Override
            public ChangeAware generateComponent(Table source, Object itemId, Object columnId) {
                final ForecastReviewUiWrapper wrapper = (ForecastReviewUiWrapper) itemId;
                final TetrisBigDecimalField tetrisTextField = new TetrisBigDecimalField();
                tetrisTextField.setRequired(true);
                tetrisTextField.addStyleName(ValoTheme.TEXTFIELD_ALIGN_RIGHT);
                tetrisTextField.setWidth(100, Unit.PIXELS);

                tetrisTextField.addValueChangeListener(new Property.ValueChangeListener() {
                    @Override
                    public void valueChange(Property.ValueChangeEvent valueChangeEvent) {
                        boolean isParent = wrapper.isParent();
                        if (tetrisTextField.getValue() == null) {
                            return;
                        }

                        if (isParent) {
                            wrapper.updateForecastedValueByParent();
                        } else {
                            wrapper.updateForecastedValueByChild();
                        }
                    }
                });
                return tetrisTextField;
            }
        });

        table.addGeneratedColumn(userAdjustedForecastLevel, new ForecastReviewTreeTableColumnGenerator() {
            @Override
            public ChangeAware generateComponent(Table source, Object itemId, Object columnId) {
                final ForecastReviewUiWrapper wrapper = (ForecastReviewUiWrapper) itemId;
                TetrisEnumCombobox combobox = new TetrisEnumCombobox(FunctionSpaceDemandCalendarForecastLevel.class, TetrisEnumContainer.ORDINAL_PROPERTY_NAME);
                combobox.setItemCaptionGenerator(new ItemCaptionGenerator() {
                    @Override
                    public String getItemCaption(AbstractSelect source, Object itemId) {
                        FunctionSpaceDemandCalendarForecastLevel forecastLevel = (FunctionSpaceDemandCalendarForecastLevel) itemId;
                        if (FunctionSpaceDemandCalendarForecastLevel.HIGH.equals(forecastLevel)) {
                            return UiUtils.getText("common.high");
                        } else if (FunctionSpaceDemandCalendarForecastLevel.MEDIUM.equals(forecastLevel)) {
                            return UiUtils.getText("common.medium");
                        } else if (FunctionSpaceDemandCalendarForecastLevel.LOW.equals(forecastLevel)) {
                            return UiUtils.getText("common.low");
                        }
                        return null;
                    }
                });
                combobox.setWidth(100, Unit.PIXELS);
                combobox.addValueChangeListener(new Property.ValueChangeListener() {
                    @Override
                    public void valueChange(Property.ValueChangeEvent event) {
                        wrapper.updateForecastLevel();
                    }
                });
                return combobox;
            }
        });

        table.addGeneratedColumn(status, new ForecastReviewTreeTableColumnGenerator() {
            @Override
            public ChangeAware generateComponent(Table source, Object itemId, Object columnId) {
                final ForecastReviewUiWrapper wrapper = (ForecastReviewUiWrapper) itemId;
                TetrisEnumCombobox combobox = new TetrisEnumCombobox(FunctionSpaceDemandCalendarDateStatus.class);
                combobox.setWidth(100, Unit.PIXELS);
                combobox.addValueChangeListener(new Property.ValueChangeListener() {
                    @Override
                    public void valueChange(Property.ValueChangeEvent event) {
                        wrapper.updateStatus();
                    }
                });
                combobox.setItemCaptionGenerator(new ItemCaptionGenerator() {
                    @Override
                    public String getItemCaption(AbstractSelect source, Object itemId) {
                        FunctionSpaceDemandCalendarDateStatus caption = (FunctionSpaceDemandCalendarDateStatus) itemId;
                        if (FunctionSpaceDemandCalendarDateStatus.CLOSED.equals(caption)) {
                            return UiUtils.getText("CLOSED");
                        } else if (FunctionSpaceDemandCalendarDateStatus.EVALUATE.equals(caption)) {
                            return UiUtils.getText("EVALUATE");
                        } else if (FunctionSpaceDemandCalendarDateStatus.OPEN.equals(caption)) {
                            return UiUtils.getText("OPEN");
                        } else {
                            return null;
                        }
                    }
                });
                return combobox;
            }
        });

        table.addGeneratedColumn(notesId, new Table.ColumnGenerator() {
            @Override
            public Object generateCell(Table table, Object o, Object o1) {
                final ForecastReviewUiWrapper wrapper = (ForecastReviewUiWrapper) o;
                if (wrapper.parent == null) {
                    TetrisNoteImageButton tetrisNoteImageButton = new TetrisNoteImageButton(wrapper.getOccupancyDate(), "Function Space", null, false, true, false);
                    tetrisNoteImageButton.setHasExistingNotes(presenter.hasNotes(wrapper.getOccupancyDate()));
                    tetrisNoteImageButton.setId("forecastReviewNoteImageButton");
                    tetrisNoteImageButton.addStyleName("forecast-review-buttons");
                    return tetrisNoteImageButton;
                }
                return null;
            }
        });

        table.addGeneratedColumn(rowActionId, new Table.ColumnGenerator() {
            @Override
            public Object generateCell(Table sourceTable, Object itemId, Object columnId) {
                final ForecastReviewUiWrapper wrapper = (ForecastReviewUiWrapper) itemId;

                HorizontalLayout layout = new HorizontalLayout();

                //only show the edit button if this is a parent row and the rows date is on or after the system date
                //we don't allow any overrides on dates in the past
                if (wrapper.isParent() && presenter.isFutureDate(wrapper.getOccupancyDate())) {
                    wrapper.setActionLayout(layout);

                    if (!wrapper.isReadOnly()) {
                        addSaveCancelButtons(wrapper);
                    } else {
                        addEditDeleteButtons(wrapper);
                    }
                    layout.setSpacing(true);

                    return layout;
                }

                return null;
            }
        });
        table.setColumnCollapsingAllowed(true);

        table.setBlankInChildrenColumns(new String[]{dow});

        table.setVisibleColumns(new String[]{dow, occupancyDate, dayPartName, userAdjustedUtilization, userAdjustedForecastLevel, forecastVariance, otbUtilization, status, notesId,
                totalGuestRoomUnconstraintedDemand, groupGuestRoomForecast, groupGuestRoomOnBooks, groupGuestRoomRemainingDemand, transientGuestRoomForecast, transientGuestRoomOnBooks,
                transientGuestRoomRemainingDemand, groupForecastAchievedPercentage, totalGuestRoomOnBooks, totalGuestRoomForecast, onTheBooksUtilizationWithProspects, numberOfIndivisibleRoomsOnBooks,
                rowActionId});

        table.setColumnHeader(dow, UiUtils.getText("report.dow"));
        table.setColumnWidth(dow, 85);
        table.setColumnHeader(occupancyDate, UiUtils.getText("date"));
        table.setColumnHeader(dayPartName, UiUtils.getText("groupPricing.forecastReview.dayPart"));
        table.setColumnHeader(userAdjustedUtilization, UiUtils.getText("groupPricing.forecastReview.forecastedUtilization"));
        table.setColumnWidth(userAdjustedUtilization, 100);
        table.setColumnHeader(userAdjustedForecastLevel, UiUtils.getText("forecast") + "\n " + UiUtils.getText("level"));
        table.setColumnWidth(userAdjustedForecastLevel, 100);
        table.setColumnHeader(forecastVariance, UiUtils.getText("forecast") + "\n " + UiUtils.getText("variance") + " %");
        table.setColumnHeader(otbUtilization, UiUtils.getText("groupEvaluation.utilization%") + "\n " + UiUtils.getText("common.on.books"));
        table.setColumnHeader(status, UiUtils.getText("groupPricing.forecastReview.functionOnlyBusiness"));
        table.setColumnWidth(status, 100);
        table.setColumnHeader(notesId, UiUtils.getText("notes.label"));
        table.setColumnWidth(notesId, 75);
        table.setColumnHeader(rowActionId, UiUtils.getText("actions"));
        table.setColumnWidth(rowActionId, 60);

        table.setColumnAlignment(userAdjustedUtilization, Table.Align.RIGHT);
        table.setColumnAlignment(forecastVariance, Table.Align.RIGHT);
        table.setColumnAlignment(otbUtilization, Table.Align.RIGHT);
        table.setColumnAlignment(forecastVariance, Table.Align.RIGHT);
        table.setColumnAlignment(notesId, Table.Align.CENTER);
        table.setColumnAlignment(rowActionId, Table.Align.CENTER);

        //additional columns
        table.setColumnHeader(totalGuestRoomUnconstraintedDemand, UiUtils.getText("groupPricing.forecastReview.totalGuestRoomUnconstrainedDemand"));
        table.setColumnCollapsed(totalGuestRoomUnconstraintedDemand, true);
        table.setColumnAlignment(totalGuestRoomUnconstraintedDemand, Table.Align.RIGHT);
        table.setColumnHeader(groupGuestRoomForecast, UiUtils.getText("groupPricing.forecastReview.groupGuestRoomForecast") + " ");
        table.setColumnCollapsed(groupGuestRoomForecast, true);
        table.setColumnAlignment(groupGuestRoomForecast, Table.Align.RIGHT);
        table.setColumnHeader(groupGuestRoomOnBooks, UiUtils.getText("groupPricing.forecastReview.groupGuestRoomOnBooks"));
        table.setColumnCollapsed(groupGuestRoomOnBooks, true);
        table.setColumnAlignment(groupGuestRoomOnBooks, Table.Align.RIGHT);
        table.setColumnHeader(groupGuestRoomRemainingDemand, UiUtils.getText("groupPricing.forecastReview.guestRoomRemainingDemand"));
        table.setColumnCollapsed(groupGuestRoomRemainingDemand, true);
        table.setColumnAlignment(groupGuestRoomRemainingDemand, Table.Align.RIGHT);
        table.setColumnHeader(transientGuestRoomForecast, UiUtils.getText("common.occupancyForecast") + "- " + UiUtils.getText("common.transient"));
        table.setColumnCollapsed(transientGuestRoomForecast, true);
        table.setColumnAlignment(transientGuestRoomForecast, Table.Align.RIGHT);
        table.setColumnHeader(transientGuestRoomOnBooks, UiUtils.getText("occupancy.on.books") + "\n -" + UiUtils.getText("common.transient"));
        table.setColumnCollapsed(transientGuestRoomOnBooks, true);
        table.setColumnAlignment(transientGuestRoomOnBooks, Table.Align.RIGHT);
        table.setColumnHeader(transientGuestRoomRemainingDemand, UiUtils.getText("remainingDemand") + "\n " + UiUtils.getText("common.transient"));
        table.setColumnCollapsed(transientGuestRoomRemainingDemand, true);
        table.setColumnAlignment(transientGuestRoomRemainingDemand, Table.Align.RIGHT);
        table.setColumnHeader(groupForecastAchievedPercentage, UiUtils.getText("groupPricing.forecastReview.groupForecastAchievedPercentage"));
        table.setColumnCollapsed(groupForecastAchievedPercentage, true);
        table.setColumnAlignment(groupForecastAchievedPercentage, Table.Align.RIGHT);
        table.setColumnHeader(totalGuestRoomOnBooks, UiUtils.getText("groupPricing.forecastReview.occupancyOnBooksTotal"));
        table.setColumnCollapsed(totalGuestRoomOnBooks, true);
        table.setColumnAlignment(totalGuestRoomOnBooks, Table.Align.RIGHT);
        table.setColumnHeader(totalGuestRoomForecast, UiUtils.getText("report.column.occupancyForecastTotal"));
        table.setColumnCollapsed(totalGuestRoomForecast, true);
        table.setColumnAlignment(totalGuestRoomForecast, Table.Align.RIGHT);
        table.setColumnHeader(onTheBooksUtilizationWithProspects, UiUtils.getText("groupPricing.forecastReview.onBooksUtilizationWithProspects"));
        table.setColumnCollapsed(onTheBooksUtilizationWithProspects, true);
        table.setColumnAlignment(onTheBooksUtilizationWithProspects, Table.Align.RIGHT);
        table.setColumnHeader(numberOfIndivisibleRoomsOnBooks, UiUtils.getText("groupPricing.forecastReview.numberOfIndivisibleRoomsOnBooks"));
        table.setColumnCollapsed(numberOfIndivisibleRoomsOnBooks, true);
        table.setColumnAlignment(numberOfIndivisibleRoomsOnBooks, Table.Align.RIGHT);

        HorizontalLayout horizontalLayout = new HorizontalLayout();
        horizontalLayout.setMargin(true);
        horizontalLayout.setSpacing(true);

        TetrisButton expandAllButton = new TetrisButton(UiUtils.getText("groupPricing.forecastReview.expandAll"), new Button.ClickListener() {
            @Override
            public void buttonClick(Button.ClickEvent event) {
                Button button = event.getButton();
                table.toggleExpandCollapse();
                if (table.isExpanded()) {
                    button.setCaption(UiUtils.getText("groupPricing.forecastReview.collapseAll"));
                } else {
                    button.setCaption(UiUtils.getText("groupPricing.forecastReview.expandAll"));
                }
            }
        });
        expandAllButton.setIsSecondary(true);
        horizontalLayout.addComponent(expandAllButton);

        TetrisExportXlsImageButton exportXlsButton = new TetrisExportXlsImageButton(new ExportProvider() {
            @Override
            public Table getComponent() {
                return table;
            }

            @Override
            public BaseVaadinExporter getExporter() {
                ForecastReviewExporter forecastReviewExporter = new ForecastReviewExporter();
                forecastReviewExporter.setSheetNames(UiUtils.getText("common.report"), null);
                return forecastReviewExporter;
            }

            @Override
            public ReportCriteria getReportCriteria() {
                return null;
            }
        }, UiUtils.getText("function-space-forecast-review"));
        exportXlsButton.setIcon(TetrisFontAwesome.EXCEL_ICON);
        exportXlsButton.removeButtonBorder();
        exportXlsButton.setId("exportXlsButton");

        horizontalLayout.addComponent(exportXlsButton);
        layout.addComponent(horizontalLayout);
        layout.setComponentAlignment(horizontalLayout, Alignment.BOTTOM_RIGHT);

        layout.addComponent(table);
        layout.setExpandRatio(table, 1.0f);
    }

    private void processRemoveOverride(ForecastReviewUiWrapper columnComponentProvider) {
        if (columnComponentProvider.isParent()) {
            columnComponentProvider.removeParentOverrideIcons();
            for (ForecastReviewUiWrapper child : columnComponentProvider.getAllChildren()) {
                if (child.isHasUserForecastOverride()) {
                    child.removeUserOverride();
                }
            }

            columnComponentProvider.refreshComputedWeightAvg();

        } else {
            columnComponentProvider.removeUserOverride();

            //remove parent icon if child is the only one that causes override icon to be displayed in parent
            ForecastReviewUiWrapper parent = columnComponentProvider.getParent();
            boolean canRemoveParentOverrideIcon = true;
            for (ForecastReviewUiWrapper child : parent.getAllChildren()) {
                if (child.isHasUserForecastOverride() && !child.isDeleted()) {
                    canRemoveParentOverrideIcon = false;
                    break;
                }
            }

            if (canRemoveParentOverrideIcon) {
                parent.removeParentOverrideIcons();
            }
        }
    }

    private Label getOverrideIcon(ForecastReviewUiWrapper wrapper) {
        Label forecastOverride = new Label("");
        forecastOverride.addStyleName("forecastOverride");
        forecastOverride.setDescription(getDescription(wrapper));
        return forecastOverride;
    }

    private String getDescription(ForecastReviewUiWrapper wrapper) {
        return "System Forecasted Utilization :" + wrapper.getForecastedUtilization() + "<br /> System Forecast Level: " + wrapper.getForecastedLevel().toString();
    }

    private void addEditDeleteButtons(final ForecastReviewUiWrapper columnComponentProvider) {
        AbstractOrderedLayout actionLayout = columnComponentProvider.getActionLayout();
        actionLayout.removeAllComponents();
        if (isRowEditable()) {
            TetrisImageButton editButton = new TetrisEditButton(new Button.ClickListener() {
                @Override
                public void buttonClick(Button.ClickEvent event) {
                    if (hasChanges()) {
                        TetrisNotification.showWarningMessage(UiUtils.getText("common.warning.msg.unsaved.changes.before.edit"));
                        return;
                    }
                    table.setCollapsed(columnComponentProvider, false);
                    cancelAllEdits();
                    columnComponentProvider.setReadOnly(false);
                    addSaveCancelButtons(columnComponentProvider);
                }
            });
            editButton.setId("forecastReviewEditButton");
            editButton.addStyleName("forecast-review-buttons");
            editButton.setIcon(TetrisFontAwesome.EDIT);
            editButton.setEnabledRequirements(true, TetrisPermissionKey.FUNCTION_SPACE_FORECAST_REVIEW);
            actionLayout.addComponent(editButton);
        }
    }

    @Override
    public boolean hasChanges() {
        for (Object object : new ArrayList<Object>(container.getItemsExcludingNoDataFoundItem())) { //copied to new list to avoid concurrent mod.
            ForecastReviewUiWrapper componentProvider = (ForecastReviewUiWrapper) object;
            if (componentProvider.hasChanges()) {
                return true;
            }
        }
        return false;
    }

    private void cancelAllEdits() {
        if (container != null) {
            for (Object object : new ArrayList<Object>(container.getItemsExcludingNoDataFoundItem())) { //copied to new list to avoid concurrent mod.
                ForecastReviewUiWrapper componentProvider = (ForecastReviewUiWrapper) object;
                handleCancel(componentProvider);
            }
        }
    }

    private void handleCancel(ForecastReviewUiWrapper columnComponentProvider) {
        columnComponentProvider.resetFields();
        columnComponentProvider.setReadOnly(true);
        addEditDeleteButtons(columnComponentProvider);
    }

    public void updateContainer(List<ForecastReviewUiWrapper> values) {
        container.removeAllItems();

        for (ForecastReviewUiWrapper parent : values) {
            container.addItem(parent);
            List<ForecastReviewUiWrapper> children = parent.getChildren();
            container.addAll(children);
        }
    }

    public boolean isRowEditable() {
        return isRowEditable;
    }

    public void setRowEditable(boolean isRowEditable) {
        this.isRowEditable = isRowEditable;
    }

    private void addSaveCancelButtons(final ForecastReviewUiWrapper columnComponentProvider) {
        columnComponentProvider.getActionLayout().removeAllComponents();
        TetrisImageButton saveButton = new TetrisImageButton(TetrisFontAwesome.SAVE, new Button.ClickListener() {
            @SuppressWarnings("unchecked")
            @Override
            public void buttonClick(Button.ClickEvent event) {
                boolean isValid = columnComponentProvider.isValid();

                if (!isValid) {
                    TetrisNotification.showWarningMessage(UiUtils.getText("common.reviewselection"));
                } else {
                    presenter.onSave(columnComponentProvider);
                    columnComponentProvider.rebind();
                    columnComponentProvider.setReadOnly(true);
                    addEditDeleteButtons(columnComponentProvider);
                }
            }
        });
        saveButton.setDescription(getText("common.save"));
        saveButton.addStyleName("forecast-review-buttons");
        TetrisImageButton cancelButton = new TetrisImageButton(TetrisFontAwesome.REMOVE_CIRCLE_MEDIUM, new Button.ClickListener() {
            @Override
            public void buttonClick(Button.ClickEvent event) {
                handleCancel(columnComponentProvider);
            }
        });
        cancelButton.setDescription(getText("cancel"));
        cancelButton.setId("cancelButton");
        cancelButton.addStyleName("forecast-review-buttons");
        columnComponentProvider.getActionLayout().addComponents(saveButton, cancelButton);
    }

    public void resetFilter() {
        filterComponent.setDefault();
    }
}


