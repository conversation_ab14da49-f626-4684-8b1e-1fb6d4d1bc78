package com.ideas.tetris.ui.modules.functionspace.configuration.forecastlevels;

import com.ideas.tetris.ui.common.component.TetrisCustomField;
import com.ideas.tetris.ui.common.component.slider.dualslider.DualSlider;
import com.vaadin.ui.Component;

public class ForecastLevelSliderField extends TetrisCustomField<ForecastLevelMinMaxBean> {

    private int initialMinValue;
    private int initialMaxValue;
    private DualSlider dualSlider;
    private ForecastLevelMinMaxBean bean;

    @Override
    public void onInitialValue(ForecastLevelMinMaxBean bean) {
        initialMinValue = bean.getMin();
        initialMaxValue = bean.getMax();
        this.bean = bean;

        if (dualSlider != null) {
            dualSlider.setMinValueRestriction(5);
            dualSlider.setMaxValueRestriction(95);
            dualSlider.setMinMaxDifference(5);
            dualSlider.setValues(initialMinValue, initialMaxValue);
        }
    }

    @Override
    public boolean hasChanges() {
        if (initialMinValue != bean.getMin() || initialMaxValue != bean.getMax()) {
            return true;
        }
        return false;
    }

    @Override
    protected Component initContent() {
        dualSlider = new DualSlider();
        dualSlider.setWidth(500, Unit.PIXELS);
        dualSlider.setMin(1);
        dualSlider.setMax(100);
        dualSlider.setMinValueRestriction(5);
        dualSlider.setMaxValueRestriction(95);
        dualSlider.setMinMaxDifference(5);

//        dualSlider.addStyleName();

        dualSlider.setValues(initialMinValue, initialMaxValue);
        dualSlider.addValuesChangeListener(new DualSlider.ValuesChangeListener() {
            @Override
            public void onValuesChange(DualSlider.ValuesChangeEvent event) {
                bean.setMin(event.getValue1());
                bean.setMax(event.getValue2());
            }
        });
        return dualSlider;
    }

    @Override
    public void reset() {
        dualSlider.setValues(initialMinValue, initialMaxValue);
        bean.setMin(initialMinValue);
        bean.setMax(initialMaxValue);
    }
}
