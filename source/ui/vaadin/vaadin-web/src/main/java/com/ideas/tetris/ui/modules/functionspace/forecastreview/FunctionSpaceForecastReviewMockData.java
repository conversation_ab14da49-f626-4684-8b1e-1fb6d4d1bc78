package com.ideas.tetris.ui.modules.functionspace.forecastreview;

import com.ideas.tetris.pacman.services.functionspace.configuration.entity.FunctionSpaceForecastLevel;
import com.ideas.tetris.pacman.services.functionspace.dto.FunctionSpaceDemandCalendarDateDto;
import com.ideas.tetris.pacman.services.functionspace.dto.FunctionSpaceDemandCalendarDatePartDto;
import com.ideas.tetris.pacman.services.functionspace.dto.FunctionSpaceDemandCalendarDateStatus;
import com.ideas.tetris.pacman.services.functionspace.forecastreview.service.ForecastReviewCriteria;
import org.joda.time.LocalDate;
import org.joda.time.LocalTime;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

public class FunctionSpaceForecastReviewMockData {

    private static String[] DAY_PARTS = {"Morning", "Noon", "Evening", "Night"};

    public static Map<LocalDate, FunctionSpaceDemandCalendarDateDto> getData(ForecastReviewCriteria criteria) {
        Map<LocalDate, FunctionSpaceDemandCalendarDateDto> map = new HashMap<LocalDate, FunctionSpaceDemandCalendarDateDto>();
        LocalDate occupancyDate = new LocalDate();
        FunctionSpaceDemandCalendarDateDto dateDto = new FunctionSpaceDemandCalendarDateDto(occupancyDate, getForecastConfigurationLevel());
        addDayParts(dateDto);
        map.put(occupancyDate, dateDto);
        return map;
    }

    private static void addDayParts(FunctionSpaceDemandCalendarDateDto dateDto) {
        FunctionSpaceDemandCalendarDatePartDto dayPartDto = new FunctionSpaceDemandCalendarDatePartDto();
        dayPartDto.setDayPartId(881);
        dayPartDto.setDayPartName("Morning");
        dayPartDto.setBeginTime(new LocalTime(6, 30, 0));
        dayPartDto.setEndTime(new LocalTime(10, 0, 0));
        dayPartDto.setForecastUtilization(new BigDecimal(.30));
        dayPartDto.setUserUtilization(new BigDecimal(.35));
        dayPartDto.setFunctionSpaceDemandCalendarDateStatus(FunctionSpaceDemandCalendarDateStatus.EVALUATE);
        dateDto.addDatePartDto(dayPartDto);

        FunctionSpaceDemandCalendarDatePartDto dayPartDto1 = new FunctionSpaceDemandCalendarDatePartDto();
        dayPartDto1.setDayPartId(882);
        dayPartDto1.setDayPartName("Noon");
        dayPartDto1.setBeginTime(new LocalTime(10, 0, 0));
        dayPartDto1.setEndTime(new LocalTime(16, 0, 0));
        dayPartDto1.setForecastUtilization(new BigDecimal(.15));
        dayPartDto1.setUserUtilization(new BigDecimal(.20));
        dayPartDto1.setFunctionSpaceDemandCalendarDateStatus(FunctionSpaceDemandCalendarDateStatus.EVALUATE);
        dateDto.addDatePartDto(dayPartDto1);

        FunctionSpaceDemandCalendarDatePartDto dayPartDto2 = new FunctionSpaceDemandCalendarDatePartDto();
        dayPartDto2.setDayPartId(883);
        dayPartDto2.setDayPartName("Evening");
        dayPartDto2.setBeginTime(new LocalTime(16, 0, 0));
        dayPartDto2.setEndTime(new LocalTime(6, 30, 0));
        dayPartDto2.setForecastUtilization(new BigDecimal(.55));
        dayPartDto2.setUserUtilization(new BigDecimal(.60));
        dayPartDto2.setFunctionSpaceDemandCalendarDateStatus(FunctionSpaceDemandCalendarDateStatus.EVALUATE);
        dateDto.addDatePartDto(dayPartDto2);

    }

    private static FunctionSpaceForecastLevel getForecastConfigurationLevel() {
        FunctionSpaceForecastLevel functionSpaceForecastLevel = new FunctionSpaceForecastLevel();
        functionSpaceForecastLevel.setId(9999);
        functionSpaceForecastLevel.setMin(25);
        functionSpaceForecastLevel.setMax(50);
        return functionSpaceForecastLevel;
    }
}
