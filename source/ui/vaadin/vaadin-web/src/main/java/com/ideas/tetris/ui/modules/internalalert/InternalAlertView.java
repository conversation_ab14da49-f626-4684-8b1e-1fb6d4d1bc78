package com.ideas.tetris.ui.modules.internalalert;

import com.ideas.infra.tetris.security.domain.TetrisPermissionKey;
import com.ideas.tetris.ui.common.cdi.TetrisNavigatorView;
import com.ideas.tetris.ui.common.component.tabsheet.TetrisTabSheet;
import com.vaadin.cdi.CDIView;
import com.vaadin.ui.UI;

import javax.inject.Inject;

@SuppressWarnings("serial")
@CDIView(uis = UI.class, value = TetrisPermissionKey.INTERNAL_ALERTS)
public class InternalAlertView extends TetrisNavigatorView<InternalAlertPresenter, Void> {

    @Inject
    private TetrisTabSheet tabSheet;

    @Override
    protected void initView() {
        tabSheet.setSizeFull();
        tabSheet.setBookmarkingEnabled(true);
        tabSheet.addTab(AlertSummaryView.class);
        tabSheet.addTab(AlertDetailsView.class);
        tabSheet.addTab(AlertSubscriptionView.class);

        setSizeFull();
        setCompositionRoot(tabSheet);
        tabSheet.focus();
    }

    public TetrisTabSheet getTabSheet() {
        return tabSheet;
    }

}
