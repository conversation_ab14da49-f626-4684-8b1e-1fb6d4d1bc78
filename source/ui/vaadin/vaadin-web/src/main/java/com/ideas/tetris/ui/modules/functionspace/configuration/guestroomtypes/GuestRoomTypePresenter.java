package com.ideas.tetris.ui.modules.functionspace.configuration.guestroomtypes;

import com.ideas.infra.tetris.security.domain.TetrisPermissionKey;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomType;
import com.ideas.tetris.pacman.services.functionspace.configuration.entity.FunctionSpaceGuestRoomCategory;
import com.ideas.tetris.pacman.services.functionspace.configuration.service.FunctionSpaceConfigurationService;
import com.ideas.tetris.pacman.services.roomtyperecoding.services.RoomTypeRecodingService;
import com.ideas.tetris.platform.common.xml.schema.workcontext.v1.WorkContextType;
import com.ideas.tetris.ui.common.cdi.TetrisPresenter;

import javax.inject.Inject;
import java.util.ArrayList;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import com.ideas.tetris.spring.SpringAutowired;

@SpringAutowired
public class GuestRoomTypePresenter extends TetrisPresenter<GuestRoomTypeView, Boolean> {
    @Autowired
    FunctionSpaceConfigurationService functionSpaceConfigurationService;
    @Autowired
    RoomTypeRecodingService roomTypeRecodingService;
    private List<AccomType> rmsAccomTypes;

    private Boolean isGroupPricing = true;

    @Override
    public void onViewOpened(Boolean isGroupPricing) {
        this.isGroupPricing = isGroupPricing;

        refresh();
    }

    public void refresh() {
        rmsAccomTypes = functionSpaceConfigurationService.getAssignedAccomTypes();
        List<FunctionSpaceGuestRoomCategory> guestRoomCategories = functionSpaceConfigurationService
                .getGuestRoomCategories();

        ArrayList<GuestRoomTypeMappingUiWrapper> wrappers = new ArrayList<>();

        for (FunctionSpaceGuestRoomCategory guestRoomCategory : guestRoomCategories) {
            GuestRoomTypeMappingUiWrapper guestRoomTypeMappingUiWrapper = new GuestRoomTypeMappingUiWrapper(
                    guestRoomCategory);
            wrappers.add(guestRoomTypeMappingUiWrapper);
        }

        view.update(wrappers);

        boolean missingRoomTypeAlertOpen = isMissingRoomTypeAlertOpen();

        if (isGroupPricing) {
            //point to group pricing permission
            view.updatePermission(missingRoomTypeAlertOpen, TetrisPermissionKey.FUNCTION_GROUP_PRICING_CONFIG_GUEST_ROOM_TYPE);
        } else {
            //point to function space permission
            view.updatePermission(missingRoomTypeAlertOpen, TetrisPermissionKey.FUNCTION_SPACE_CONFIGURATION_GUEST_ROOM_TYPE);
        }
    }

    @Override
    public void onWorkContextChange(WorkContextType workContextType) {
        refresh();
    }

    public void save(List<GuestRoomTypeMappingUiWrapper> items) {
        ArrayList<FunctionSpaceGuestRoomCategory> functionSpaceGuestRoomCategories = new ArrayList<>();
        for (GuestRoomTypeMappingUiWrapper item : items) {

            if (item.selectedAccomType != null) {
                item.getGuestRoomCategory().setAccomTypeId((item.selectedAccomType.getId()));
            } else {
                item.getGuestRoomCategory().setAccomTypeId(null);
            }

            functionSpaceGuestRoomCategories.add(item.getGuestRoomCategory());
        }

        functionSpaceConfigurationService.saveGuestRoomCategories(functionSpaceGuestRoomCategories);
        refresh();
        showSaveSuccessMessage();
        sync();
    }

    List<AccomType> getRmsAccomTypes() {
        return rmsAccomTypes;
    }

    AccomType getAccomTypeById(Integer accomTypeId) {
        for (AccomType rmsAccomType : rmsAccomTypes) {
            if (rmsAccomType.getId().equals(accomTypeId)) {
                return rmsAccomType;
            }
        }
        return null;
    }

    public boolean isMissingRoomTypeAlertOpen() {
        return roomTypeRecodingService.isExistingMissingRoomTypeAlertOpen();
    }

    public Boolean isGroupPricing() {
        return isGroupPricing;
    }
}
