package com.ideas.tetris.ui.modules.functionspace.forecastreview;

import com.ideas.tetris.ui.common.component.TetrisBeanItemContainer;
import com.ideas.tetris.ui.common.component.table.TetrisTreeTable;
import com.ideas.tetris.ui.common.util.TetrisJRBeanCollectionDataSource;
import com.ideas.tetris.ui.modules.reports.BaseTableExporter;
import com.vaadin.v7.ui.Table;
import net.sf.dynamicreports.jasper.builder.JasperReportBuilder;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;

import static net.sf.dynamicreports.report.builder.DynamicReports.col;
import static net.sf.dynamicreports.report.builder.DynamicReports.type;

public class ForecastReviewExporter extends BaseTableExporter {
    @Override
    public JasperReportBuilder createReportBuilder(Table table2, String reportTitle) {

        JRBeanCollectionDataSource dataSource = new TetrisJRBeanCollectionDataSource(table2.getContainerDataSource());

        TetrisTreeTable table = (TetrisTreeTable) table2;
        TetrisBeanItemContainer container = table.getTetrisBeanItemContainer();

        String dowId = "dow";
        String dateId = "occupancyDate";
        String dayPartId = "functionSpaceDayPartName";
        String forecastedUtilizationId = "userAdjustedUtilization";
        String forecastLevelReportId = "forecastLevelForReports";
        String forecastLevelId = "userAdjustedLevel";
        String forecastVarianceId = "forecastVariance";
        String otbUtilizationId = "otbUtilization";
        String functionOnlyBusinessId = "status";
        String functionOnlyBusinessReportId = "statusForReports";

        //Additional columns that are hidden in the table by default
        String totalGuestRoomUnconstraintedDemand = "totalGuestRoomUnconstraintedDemand";
        String groupGuestRoomForecast = "groupGuestRoomForecast";
        String groupGuestRoomOnBooks = "groupGuestRoomOnBooks";
        String groupGuestRoomRemainingDemand = "groupGuestRoomRemainingDemand";
        String transientGuestRoomForecast = "transientGuestRoomForecast";
        String transientGuestRoomOnBooks = "transientGuestRoomOnBooks";
        String transientGuestRoomRemainingDemand = "transientGuestRoomRemainingDemand";
        String groupForecastAchievedPercentage = "groupForecastAchievedPercentage";
        String totalGuestRoomOnBooks = "totalGuestRoomOnBooks";
        String totalGuestRoomForecast = "totalGuestRoomForecast";
        String onTheBooksUtilizationWithProspects = "onTheBooksUtilizationWithProspects";
        String numberOfIndivisibleRoomsOnBooks = "numberOfIndivisibleRoomsOnBooks";


        JasperReportBuilder reportBuilder =
                tetrisReport(reportTitle).columns(
                        col.column(table.getColumnHeaderNonHtml(dowId), dowId, type.stringType())
                        , col.column(table.getColumnHeaderNonHtml(dateId), dateId, type.dateType())
                        , col.column(table.getColumnHeaderNonHtml(dayPartId), dayPartId, type.stringType())
                        , col.column(table.getColumnHeaderNonHtml(forecastedUtilizationId), forecastedUtilizationId, type.bigDecimalType())
                        , col.column(table.getColumnHeaderNonHtml(forecastLevelId), forecastLevelReportId, type.stringType())
                        , col.column(table.getColumnHeaderNonHtml(forecastVarianceId), forecastVarianceId, type.bigDecimalType())
                        , col.column(table.getColumnHeaderNonHtml(otbUtilizationId), otbUtilizationId, type.bigDecimalType())
                        , col.column(table.getColumnHeaderNonHtml(functionOnlyBusinessId), functionOnlyBusinessReportId, type.stringType())


                        , col.column(table.getColumnHeaderNonHtml(totalGuestRoomUnconstraintedDemand), totalGuestRoomUnconstraintedDemand, type.bigDecimalType())
                        , col.column(table.getColumnHeaderNonHtml(groupGuestRoomForecast), groupGuestRoomForecast, type.bigDecimalType())
                        , col.column(table.getColumnHeaderNonHtml(groupGuestRoomOnBooks), groupGuestRoomOnBooks, type.bigDecimalType())
                        , col.column(table.getColumnHeaderNonHtml(groupGuestRoomRemainingDemand), groupGuestRoomRemainingDemand, type.bigDecimalType())
                        , col.column(table.getColumnHeaderNonHtml(transientGuestRoomForecast), transientGuestRoomForecast, type.bigDecimalType())
                        , col.column(table.getColumnHeaderNonHtml(transientGuestRoomOnBooks), transientGuestRoomOnBooks, type.bigDecimalType())
                        , col.column(table.getColumnHeaderNonHtml(transientGuestRoomRemainingDemand), transientGuestRoomRemainingDemand, type.bigDecimalType())
                        , col.column(table.getColumnHeaderNonHtml(groupForecastAchievedPercentage), groupForecastAchievedPercentage, type.bigDecimalType())
                        , col.column(table.getColumnHeaderNonHtml(totalGuestRoomOnBooks), totalGuestRoomOnBooks, type.bigDecimalType())
                        , col.column(table.getColumnHeaderNonHtml(totalGuestRoomForecast), totalGuestRoomForecast, type.bigDecimalType())
                        , col.column(table.getColumnHeaderNonHtml(onTheBooksUtilizationWithProspects), onTheBooksUtilizationWithProspects, type.bigDecimalType())
                        , col.column(table.getColumnHeaderNonHtml(numberOfIndivisibleRoomsOnBooks), numberOfIndivisibleRoomsOnBooks, type.integerType())
                );

        reportBuilder.setDataSource(dataSource);
        return reportBuilder;
    }
}
