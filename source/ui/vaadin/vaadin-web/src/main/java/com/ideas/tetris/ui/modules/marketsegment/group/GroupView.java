package com.ideas.tetris.ui.modules.marketsegment.group;

import com.ideas.tetris.pacman.services.marketsegment.service.AnalyticalMarketSegmentSummary;
import com.ideas.tetris.ui.common.cdi.TetrisNavigatorView;
import com.ideas.tetris.ui.common.component.CheckboxColumnSupport;
import com.ideas.tetris.ui.common.component.TetrisBeanItemContainer;
import com.ideas.tetris.ui.common.component.table.TetrisTable;
import com.ideas.tetris.ui.modules.marketsegment.common.AMSSummaryUIWrapper;
import com.ideas.tetris.ui.modules.marketsegment.common.AmsView;
import com.ideas.tetris.ui.modules.marketsegment.common.AssignAttributeConfirmationPopup;
import com.ideas.tetris.ui.modules.marketsegment.common.AssignMarketSegmentEvent;
import com.ideas.tetris.ui.modules.marketsegment.common.AttributeDetails;
import com.ideas.tetris.ui.modules.marketsegment.common.UnassignAware;
import com.ideas.tetris.ui.modules.marketsegment.common.UnassignMarketSegmentEvent;
import com.ideas.tetris.ui.modules.marketsegment.common.view.AttributeAssignmentConfig;
import com.ideas.tetris.ui.modules.marketsegment.common.view.AttributeAssignmentLayout;
import com.ideas.tetris.ui.modules.marketsegment.common.view.AttributeBean;
import com.vaadin.annotations.Title;
import com.vaadin.v7.ui.HorizontalLayout;
import com.vaadin.v7.ui.Table;

import javax.enterprise.event.Observes;
import javax.enterprise.event.Reception;
import java.text.NumberFormat;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Title("1. Group Business Level")
public class GroupView extends TetrisNavigatorView<GroupPresenter, String> implements UnassignAware {
    protected TetrisBeanItemContainer<AMSSummaryUIWrapper> container;
    protected CustomTable table;
    protected CheckboxColumnSupport checkboxColumnSupport;
    private AttributeAssignmentLayout assignmentLayout;
    private HorizontalLayout mainLayout;

    @Override
    protected void initView() {
        setSizeFull();
        container = new TetrisBeanItemContainer<AMSSummaryUIWrapper>(AMSSummaryUIWrapper.class);
        container.addNestedContainerBean("summary");

        addStyleName("tabView");

        table = new CustomTable();
        table.setId("groupBusinessTable");
        table.setContainerDataSource(container);
        table.setTwoHeaderRow();
        table.setWidth(100, Unit.PERCENTAGE);
        table.setHeight(594, Unit.PIXELS);

        String marketSegmentId = "marketSegment";
        String blockId = "summary.block";
        String pickupId = "summary.pickup";

        checkboxColumnSupport = new CheckboxColumnSupport(table);

        table.setVisibleColumns(new String[]{
                CheckboxColumnSupport.CHECKBOX_PROPERTY_ID,
                marketSegmentId,
                blockId,
                pickupId
        });
        table.setColumnHeaders(new String[]{"", getText("original.newline.market.segment.header"), getText("block"), getText("common.pickup")});
        table.setColumnAlignment(blockId, Table.Align.RIGHT);
        table.setColumnAlignment(pickupId, Table.Align.RIGHT);

        mainLayout = new HorizontalLayout(table);
        mainLayout.setSizeFull();
        mainLayout.setMargin(true);
        mainLayout.setSpacing(true);
        mainLayout.setExpandRatio(table, 1.0f);

        setCompositionRoot(mainLayout);
    }

    @Override
    public void onPresenterInit() {
        super.onPresenterInit();
        addAttributeAssignmentComponent();
    }

    private void addAttributeAssignmentComponent() {
        AttributeAssignmentConfig config = AttributeAssignmentConfig.builder()
                .withAmsView(AmsView.AMS_VIEW_GROUP)
                .withComplimentaryFeatureEnabled(presenter.isComplimentaryAttributeEnabled())
                .withForecastActivityTypes(presenter.getForecastActivityTypes())
                .withIndependentProductsEnabled(false)
                .withMaxProducts(presenter.getMaxIndependentProducts())
                .withGroupOptionEnabled(presenter.isGroupOptionEnabled())
                .withEditable(true)
                .build();
        assignmentLayout = new AttributeAssignmentLayout(config, presenter);
        assignmentLayout.onAssign(this::handleAssign);
        assignmentLayout.setWidth("215px");
        mainLayout.addComponent(assignmentLayout);
    }

    private void handleAssign(AttributeBean attribute) {
        List<AMSSummaryUIWrapper> selectedItems = (List<AMSSummaryUIWrapper>) (List<?>) checkboxColumnSupport.getSelectedItems();
        AttributeDetails attributeDetails = new AttributeDetails(selectedItems, attribute.getAttribute());
        attributeDetails.setForecastActivityType(attribute.getForecastActivityType());
        AssignAttributeConfirmationPopup.showConfirmation(presenter, attributeDetails, false);
    }

    private void createTableFooter() {
        table.setFooterVisible(true);
        table.setColumnFooter("marketSegment", getText("common.total") + " " + container.size());

        TetrisBeanItemContainer<AMSSummaryUIWrapper> tetrisBeanItemContainer = table.getTetrisBeanItemContainer();
        List<AMSSummaryUIWrapper> dtos = tetrisBeanItemContainer.getItemsExcludingNoDataFoundItem();

        table.setColumnFooter("summary.block", NumberFormat.getIntegerInstance().format(dtos.stream().map(AMSSummaryUIWrapper::getSummary).map(AnalyticalMarketSegmentSummary::getBlock).filter(Objects::nonNull).collect(Collectors.summingInt(Integer::intValue))));
        table.setColumnFooter("summary.pickup", NumberFormat.getIntegerInstance().format(dtos.stream().map(AMSSummaryUIWrapper::getSummary).map(AnalyticalMarketSegmentSummary::getPickup).filter(Objects::nonNull).collect(Collectors.summingInt(Integer::intValue))));
    }

    public void updateTable(List<AMSSummaryUIWrapper> groupMarketSegments) {
        container.removeAllItems();
        container.addAll(groupMarketSegments);
        createTableFooter();
    }

    @Override
    public void refreshData() {
        presenter.reload();
    }

    protected void onAssignedEvent(@Observes(notifyObserver = Reception.IF_EXISTS) AssignMarketSegmentEvent event) {
        presenter.resetData();
    }

    protected void onUnassignEvent(@Observes(notifyObserver = Reception.IF_EXISTS) UnassignMarketSegmentEvent event) {
        presenter.resetData();
    }

    public void updatePermission(String permission) {
        assignmentLayout.updatePermission(permission);
    }


    class CustomTable extends TetrisTable {

        //hide the no data row
        @Override
        public void addNoDataFoundRowIfNeeded() {
        }
    }

    public CheckboxColumnSupport getCheckboxColumnSupport() {
        return checkboxColumnSupport;
    }

    public void setCheckboxColumnSupport(CheckboxColumnSupport checkboxColumnSupport) {
        this.checkboxColumnSupport = checkboxColumnSupport;
    }
}
