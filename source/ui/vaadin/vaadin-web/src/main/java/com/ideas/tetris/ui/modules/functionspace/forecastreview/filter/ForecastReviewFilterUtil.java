package com.ideas.tetris.ui.modules.functionspace.forecastreview.filter;

import com.ideas.tetris.pacman.services.functionspace.configuration.entity.FunctionSpaceForecastLevel;
import com.ideas.tetris.pacman.services.functionspace.dto.FunctionSpaceDemandCalendarDateDto;
import com.ideas.tetris.pacman.services.functionspace.dto.FunctionSpaceDemandCalendarDatePartDto;
import com.ideas.tetris.pacman.services.functionspace.dto.FunctionSpaceDemandCalendarForecastLevel;
import com.ideas.tetris.pacman.services.functionspace.forecastreview.service.ForecastReviewCriteria;
import com.ideas.tetris.ui.common.util.DateUtil;
import com.ideas.tetris.ui.modules.functionspace.forecastreview.ForecastReviewUiWrapper;
import org.joda.time.LocalDate;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class ForecastReviewFilterUtil {

    public static List<ForecastReviewUiWrapper> filterForecasts(Map<LocalDate, FunctionSpaceDemandCalendarDateDto> forecasts, FilterDto filter) {

        ArrayList<ForecastReviewUiWrapper> forecastReviewUiWrappers = new ArrayList<ForecastReviewUiWrapper>();

        ForecastReviewCriteria criteria = filter.getCriteria();
        boolean anyCriteriaSet = filter.isAnyCriteriaSet();

        for (LocalDate date : forecasts.keySet()) {
            FunctionSpaceDemandCalendarDateDto parent = forecasts.get(date);
            List<FunctionSpaceDemandCalendarDatePartDto> children = parent.getFunctionSpaceDemandCalendarDatePartDtos();

            List<FunctionSpaceDemandCalendarDatePartDto> passingChildren = new ArrayList<FunctionSpaceDemandCalendarDatePartDto>();
            List<FunctionSpaceDemandCalendarDatePartDto> failingChildren = new ArrayList<FunctionSpaceDemandCalendarDatePartDto>();

            for (FunctionSpaceDemandCalendarDatePartDto child : children) {
                //check to see if this child passes or fails our filter criteria
                if (anyCriteriaSet) {
                    if (passed(child, criteria, parent.getFunctionSpaceForecastLevel())) {
                        passingChildren.add(child);
                    } else {
                        failingChildren.add(child);
                    }
                } else {
                    //no criteria was set, so just pass the child
                    passingChildren.add(child);
                }
            }

            //if we have any children that pass our filter criteria, then that means we need to show this forecasted date in the table, along with its passing children
            if (!passingChildren.isEmpty()) {
                forecastReviewUiWrappers.add(new ForecastReviewUiWrapper(parent, passingChildren, failingChildren, DateUtil.getDayOfWeek(date)));
            }
        }

        return forecastReviewUiWrappers;
    }

    private static boolean passed(FunctionSpaceDemandCalendarDatePartDto child, ForecastReviewCriteria criteria, FunctionSpaceForecastLevel functionSpaceForecastLevel) {
        boolean passed = true;

        //forecast level
        if (criteria.getForecastLevel() != null) {
            FunctionSpaceDemandCalendarForecastLevel level;
            if (child.hasOverride()) {
                level = ForecastReviewUiWrapper.getDemandCalendarForecastLevel(functionSpaceForecastLevel, child.getUserUtilizationAsPercent());
            } else {
                level = ForecastReviewUiWrapper.getDemandCalendarForecastLevel(functionSpaceForecastLevel, child.getForecastUtilizationAsPercent());
            }

            if (!criteria.getForecastLevel().equals(level)) {
                passed = false;
            }
        }

        //day part
        if (criteria.getDayPart() != null) {
            if (!child.getDayPartName().equals(criteria.getDayPart().getName())) {
                passed = false;
            }
        }

        //function only business
        if (criteria.getFunctionSpaceDemandCalendarDateStatus() != null) {
            if (!child.getFunctionSpaceDemandCalendarDateStatus().equals(criteria.getFunctionSpaceDemandCalendarDateStatus())) {
                passed = false;
            }
        }

        //overrides only
        if (criteria.isOverrideOnly()) {
            if (!child.hasOverride()) {
                passed = false;
            }
        }

        //forecastVariance
        if (criteria.getForecastVariance() != null) {
            BigDecimal childValue = child.getForecastVarianceAsPercent().abs();
            if (childValue.compareTo(criteria.getForecastVariance().abs()) < 0) {
                passed = false;
            }
        }

        return passed;
    }
}
