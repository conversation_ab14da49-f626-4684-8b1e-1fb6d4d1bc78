package com.ideas.tetris.ui.modules.marketsegment.common;

import com.ideas.tetris.ui.common.util.UiUtils;
import org.apache.commons.lang.StringUtils;

public class ComplimentaryAttribute {
    private static final String ATTRIBUTE_SEPARATOR = "> ";

    public String getComplimentaryDescription(boolean complimentary) {
        return complimentary ? ComplimentaryAttribute.ATTRIBUTE_SEPARATOR + UiUtils.getText("complimentary") : StringUtils.EMPTY;
    }
}
