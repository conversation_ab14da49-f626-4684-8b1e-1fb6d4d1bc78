package com.ideas.tetris.ui.modules.marketsegment.common.view;

import com.ideas.tetris.ui.common.util.UiUtils;
import com.ideas.tetris.ui.shell.HelpUtil;

import java.util.LinkedList;

import static com.ideas.tetris.pacman.util.Streams.stream;
import static com.ideas.tetris.ui.common.util.UiUtils.getText;

public class DescriptionBuilder {

    private static final String TOOLTIP_CLASS_WITH_BOLD_TEXT = "<p class='attributeTooltip'><b>";
    private static final String CLOSING_BOLD_TAG = ":</b> ";
    public static final String END_ITEM = "</p>";

    public static String buildForBusinessTypeAttributes(LinkedList<BusinessTypeAttribute> items) {
        StringBuilder description = new StringBuilder();
        stream(items).forEach(item -> addItem(description, item.getCaption(), item.getDescription()));
        return description.toString();
    }

    public static String buildForUnQualifiedAttributes(boolean independentProductsEnabled) {
        StringBuilder description = new StringBuilder();
        if (independentProductsEnabled) {
            addItem(description, getText("equalToBaseProduct"), getText("attribute.equal.to.base.product.description",
                    HelpUtil.createHelpUrl(1056, UiUtils.getLocale(), true)));
        } else {
            addItem(description, getText("equalToBar"), getText("attribute.equal.to.bar.description"));
        }
        addItem(description, getText("fenced.caption"), getText("attribute.fenced.description"));
        addItem(description, getText("packaged"), getText("attribute.packaged.description"));
        return description.toString();
    }

    public static String buildForLinkedAttributes(boolean independentProductsEnabled) {
        StringBuilder description = new StringBuilder();
        if (independentProductsEnabled) {
            addItem(description, getText("linked.to.primary.priced.product.caption"), getText("attribute.linked.to.primary.priced.product.description"));
            addItem(description, getText("nonlinked.caption"), getText("attribute.non.linked.base.product.description"));
        } else {
            addItem(description, getText("linked.to.BAR.caption"), getText("attribute.linked.to.bar.description"));
            addItem(description, getText("nonlinked.caption"), getText("attribute.non.linked.description"));
        }
        return description.toString();
    }

    public static String buildForYieldableAttributes() {
        StringBuilder description = new StringBuilder();
        addItem(description, getText("yieldable"), getText("attribute.yieldable.description"));
        addItem(description, getText("semi.yieldable.caption"), getText("attribute.semi.yieldable.description"));
        addItem(description, getText("nonyieldable.caption"), getText("attribute.non.yieldable.description"));
        return description.toString();
    }

    public static String buildForBaseProduct() {
        return getText("independent.products.combobox.description");
    }

    private static void addItem(StringBuilder description, String caption, String info) {
        description
                .append(TOOLTIP_CLASS_WITH_BOLD_TEXT).append(caption).append(CLOSING_BOLD_TAG)
                .append(info).append(END_ITEM);
    }
}
