package com.ideas.tetris.ui.modules.functionspace.demandcalendar.calendar;

import com.ideas.tetris.pacman.services.functionspace.configuration.entity.FunctionSpaceDayPart;
import com.ideas.tetris.pacman.services.functionspace.configuration.entity.FunctionSpaceFunctionRoom;
import com.ideas.tetris.pacman.services.functionspace.dto.FunctionSpaceDemandCalendarDatePartDto;
import com.ideas.tetris.pacman.services.functionspace.dto.FunctionSpaceDemandCalendarDateStatus;
import com.ideas.tetris.pacman.services.functionspace.dto.FunctionSpaceDemandCalendarRoomSummaryDto;
import com.ideas.tetris.pacman.services.functionspace.dto.FunctionSpaceDemandCalendarSummaryDto;
import com.ideas.tetris.pacman.services.specialevent.dto.SpecialEventSummaryDto;
import org.apache.commons.lang.math.RandomUtils;
import org.joda.time.LocalDate;
import org.joda.time.LocalTime;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;

public class FunctionSpaceDemandCalendarMockData {


    public static Map<LocalDate, DemandCalendarUIWrapper> getData(LocalDate date, LocalDate systemDate) {
        Map<LocalDate, DemandCalendarUIWrapper> dates = new LinkedHashMap<LocalDate, DemandCalendarUIWrapper>();
        FunctionSpaceDemandCalendarDateStatus statuses[] = {FunctionSpaceDemandCalendarDateStatus.CLOSED, FunctionSpaceDemandCalendarDateStatus.EVALUATE, FunctionSpaceDemandCalendarDateStatus.OPEN};

        int numberOfDaysInMonth = date.dayOfMonth().getMaximumValue();
        for (int i = 0; i < numberOfDaysInMonth; i++) {
            LocalDate localDate = new LocalDate(date.getYear(), date.getMonthOfYear(), i + 1);
            DemandCalendarUIWrapper uiWrapper = new DemandCalendarUIWrapper(localDate, getRandomForecastLevel(localDate));

            uiWrapper.setHasSpecialEvent(RandomUtils.nextBoolean());
            uiWrapper.setHasOverride(RandomUtils.nextBoolean());

            if (localDate.isBefore(systemDate)) {
                uiWrapper.setActualUtilization(getRandomValue());
            } else {
                uiWrapper.setOnBooksUtilization(getRandomValue());
                uiWrapper.setUtilizationForecast(getRandomValue());
            }
            uiWrapper.setStatus(statuses[localDate.getDayOfMonth() % statuses.length]);

            dates.put(localDate, uiWrapper);
        }

        return dates;
    }


    public static Map<LocalDate, DemandCalendarUIWrapper> getHeatMapData(LocalDate date, LocalDate systemDate) {
        Map<LocalDate, DemandCalendarUIWrapper> dates = new LinkedHashMap<LocalDate, DemandCalendarUIWrapper>();
        //6 months
        for (int i = 0; i < 6; i++) {
            dates.putAll(getData(date.plusMonths(i), systemDate));
        }

        return dates;
    }

    private static FunctionSpaceForecastLevel getRandomForecastLevel(LocalDate localDate) {
        int randomInt = localDate.getDayOfMonth() % 3;
        if (randomInt == 1) {
            return FunctionSpaceForecastLevel.LOW;
        } else if (randomInt == 2) {
            return FunctionSpaceForecastLevel.MEDIUM;
        }

        return FunctionSpaceForecastLevel.HIGH;
    }

    private static BigDecimal getRandomValue() {
        int rangeMin = 0;
        int rangeMax = 100;
        Random r = new Random();
        double randomValue = rangeMin + (rangeMax - rangeMin) * r.nextDouble();

        return new BigDecimal(randomValue).setScale(2, RoundingMode.HALF_UP);
    }

    private static int getRandomInt(int min, int max) {
        int rangeMin = min;
        int rangeMax = max;
        return rangeMin + (int) (Math.random() * ((rangeMax - rangeMin) + 1));
    }

    public static FunctionSpaceDetailsUiWrapper getDetails(boolean isLastYear) {
        return new FunctionSpaceDetailsUiWrapper(getDatePartDto(), getSpecialEvents(), getRoomDto(), getFunctionSpaceRoomsBooked(), isLastYear, LocalDate.now());
    }

    private static Map<FunctionSpaceDayPart, List<FunctionSpaceFunctionRoom>> getFunctionSpaceRoomsBooked() {
        HashMap<FunctionSpaceDayPart, List<FunctionSpaceFunctionRoom>> map = new HashMap<FunctionSpaceDayPart, List<FunctionSpaceFunctionRoom>>();
        FunctionSpaceDayPart functionSpaceDayPart = new FunctionSpaceDayPart();
        functionSpaceDayPart.setName("TEST");

        FunctionSpaceFunctionRoom room1 = new FunctionSpaceFunctionRoom();
        room1.setName("Room 1");
        FunctionSpaceFunctionRoom room2 = new FunctionSpaceFunctionRoom();
        room2.setName("Room 2");

        map.put(functionSpaceDayPart, Arrays.asList(room1, room2));
        return map;
    }

    private static FunctionSpaceDemandCalendarRoomSummaryDto getRoomDto() {
        FunctionSpaceDemandCalendarRoomSummaryDto dto = new FunctionSpaceDemandCalendarRoomSummaryDto();
        dto.setActualGroupOccupancy(new BigDecimal(23));
        dto.setActualTransientOccupancy(new BigDecimal(26));
        dto.setTotalActualOccupancy(new BigDecimal(49));
        dto.setTotalActualOccupancyPct(new BigDecimal(.23));

        dto.setForecastGroupOccupancy(new BigDecimal(30));
        dto.setForecstTransientOccupancy(new BigDecimal(33));
        dto.setTotalForecastOccupancy(new BigDecimal(63));
        dto.setTotalForecastOccupancyPct(new BigDecimal(.56));

        dto.setLastYearActualGroupOccupancy(new BigDecimal(23));
        dto.setLastYearActualTransientOccupancy(new BigDecimal(26));
        dto.setLastYearTotalActualOccupancy(new BigDecimal(49));
        dto.setLastYearTotalActualOccupancyPct(new BigDecimal(.23));

        dto.setLastYearForecastGroupOccupancy(new BigDecimal(30));
        dto.setLastYearForecstTransientOccupancy(new BigDecimal(33));
        dto.setLastYearTotalForecastOccupancy(new BigDecimal(63));
        dto.setLastYearTotalForecastOccupancyPct(new BigDecimal(.56));
        return dto;
    }

    private static List<SpecialEventSummaryDto> getSpecialEvents() {
        ArrayList<SpecialEventSummaryDto> dtos = new ArrayList<SpecialEventSummaryDto>();
        SpecialEventSummaryDto dto = new SpecialEventSummaryDto();
        dto.setEventName("Winter Event");
        dto.setInformationUseOnly(false);
        dtos.add(dto);
        return dtos;
    }

    private static String[] dayParts = {"Morning", "Noon", "Evening", "AfterDark"};

    private static List<FunctionSpaceDemandCalendarDatePartDto> getDatePartDto() {
        ArrayList<FunctionSpaceDemandCalendarDatePartDto> dayPartDtos = new ArrayList<FunctionSpaceDemandCalendarDatePartDto>();
        for (String dayPart : dayParts) {
            FunctionSpaceDemandCalendarDatePartDto dayPartDto = new FunctionSpaceDemandCalendarDatePartDto();
            dayPartDto.setDayPartName(dayPart);
            dayPartDto.setForecastUtilization(new BigDecimal(.23));
            dayPartDto.setFunctionSpaceDemandCalendarDateStatus(FunctionSpaceDemandCalendarDateStatus.EVALUATE);
            dayPartDtos.add(dayPartDto);
        }
        return dayPartDtos;
    }

    public static List<FunctionSpaceDayPart> getDayParts() {
        List<FunctionSpaceDayPart> functionSpaceDayParts = new ArrayList<FunctionSpaceDayPart>();
        for (String name : dayParts) {
            FunctionSpaceDayPart functionSpaceDayPart = new FunctionSpaceDayPart();
            functionSpaceDayPart.setName(name);
            functionSpaceDayPart.setBeginTime(new LocalTime());
            functionSpaceDayParts.add(functionSpaceDayPart);
            functionSpaceDayPart.setIncluded(true);
        }
        return functionSpaceDayParts;
    }

    public static FunctionSpaceDemandCalendarSummaryDto getBookingSummaryMockData() {
        FunctionSpaceDemandCalendarSummaryDto dto = new FunctionSpaceDemandCalendarSummaryDto();

        dto.setFunctionSpaceDayParts(getDayParts());

        dto.setAvgUtilization(BigDecimal.valueOf(5.0));
        dto.setAvgRevPost(BigDecimal.valueOf(5.0));
        dto.setAvgRevPast(BigDecimal.valueOf(5.0));
        dto.setAvgProPost(BigDecimal.valueOf(5.0));
        dto.setAvgProPast(BigDecimal.valueOf(5.0));

        dto.setAvgUtilizationForecast(BigDecimal.valueOf(5.0));
        dto.setAvgUtilizationOTBs(BigDecimal.valueOf(5.0));
        dto.setOpen(5);
        dto.setClosed(2);
        dto.setEvaluate(13);

        return dto;
    }
}
