package com.ideas.tetris.ui.modules.reports.pickupandchange;

import com.google.common.annotations.VisibleForTesting;
import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.configparams.IPConfigParamName;
import com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.common.utils.systemconfig.SystemConfig;
import com.ideas.tetris.pacman.services.accommodation.dto.AccomClassSummary;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomType;
import com.ideas.tetris.pacman.services.agilerates.configuration.service.AgileRatesConfigurationService;
import com.ideas.tetris.pacman.services.businessgroup.service.BusinessGroupService;
import com.ideas.tetris.pacman.services.marketsegment.dto.ForecastGroupSummary;
import com.ideas.tetris.pacman.services.marketsegment.entity.BusinessGroup;
import com.ideas.tetris.pacman.services.marketsegment.entity.MktSeg;
import com.ideas.tetris.pacman.services.marketsegment.service.MarketSegmentService;
import com.ideas.tetris.pacman.services.marketsegment.service.forecastgroup.service.ForecastGroupFinalService;
import com.ideas.tetris.pacman.services.product.Product;
import com.ideas.tetris.pacman.services.reports.dto.ScheduleReportDTO;
import com.ideas.tetris.pacman.services.reports.dto.ViewByOption;
import com.ideas.tetris.pacman.services.scheduledreport.entity.ReportType;
import com.ideas.tetris.pacman.services.scheduledreport.entity.criteria.ReportCriteria;
import com.ideas.tetris.pacman.services.webrate.entity.WebrateCompetitors;
import com.ideas.tetris.pacman.services.webrate.service.WebrateShoppingDataService;
import com.ideas.tetris.platform.common.xml.schema.workcontext.v1.WorkContextType;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import com.ideas.tetris.ui.common.component.date.customdateselector.JavaDateSelectorBean;
import com.ideas.tetris.ui.common.component.date.customdateselector.RollingDateOptionsChoice;
import com.ideas.tetris.ui.common.component.date.customdateselector.RollingDateWithMonthAndEndYearOption;
import com.ideas.tetris.ui.common.component.date.customdateselector.RollingDatesWithLastOptimization;
import com.ideas.tetris.ui.common.component.date.customdateselector.RollingDatesWithLastUpdatedDate;
import com.ideas.tetris.ui.common.component.date.customdateselector.RollingJavaDates;
import com.ideas.tetris.ui.common.util.DateFormatUtil;
import com.ideas.tetris.ui.common.util.DateUtil;
import com.ideas.tetris.ui.common.util.SharedSessionState;
import com.ideas.tetris.ui.common.util.UiUtils;
import com.ideas.tetris.ui.modules.reports.AbstractReportPresenter;
import com.ideas.tetris.ui.modules.reports.common.DataLevel;
import com.ideas.tetris.ui.modules.reports.common.DataLevelUIWrapper;
import com.ideas.tetris.ui.modules.reports.dataextraction.AgileProductSelectionWrapper;
import com.ideas.tetris.ui.modules.reports.reportscheduler.JasperReportTypeEnum;
import com.ideas.tetris.ui.modules.reports.reportscheduler.dto.ScheduledReportDisplayDTO;
import com.ideas.tetris.ui.modules.reports.util.JavaDateRange;
import com.ideas.tetris.ui.modules.reports.util.ReportFormatType;
import com.ideas.tetris.ui.modules.reports.util.RollingDateRange;
import com.ideas.tetris.ui.modules.reports.util.URLParamUtil;
import io.netty.util.internal.StringUtil;

import javax.inject.Inject;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

import static com.ideas.tetris.ui.common.component.date.customdateselector.LocalizationKeyConstants.ACTIVITY_END_DATE;
import static com.ideas.tetris.ui.common.component.date.customdateselector.LocalizationKeyConstants.ACTIVITY_START_DATE;
import static com.ideas.tetris.ui.common.component.date.customdateselector.LocalizationKeyConstants.ALERT;
import static com.ideas.tetris.ui.common.component.date.customdateselector.LocalizationKeyConstants.ANALYSIS_END_DATE;
import static com.ideas.tetris.ui.common.component.date.customdateselector.LocalizationKeyConstants.ANALYSIS_START_DATE_LABEL;
import static com.ideas.tetris.ui.common.component.date.customdateselector.LocalizationKeyConstants.BAR_CANNOT_BE_SELECTED_FOR_AGGREGATED_DATA_MESSAGE;
import static com.ideas.tetris.ui.common.component.date.customdateselector.LocalizationKeyConstants.BUSINESS_TYPES;
import static com.ideas.tetris.ui.common.component.date.customdateselector.LocalizationKeyConstants.BUSINESS_VIEWS;
import static com.ideas.tetris.ui.common.component.date.customdateselector.LocalizationKeyConstants.COMMON_TOTAL_HOTEL;
import static com.ideas.tetris.ui.common.component.date.customdateselector.LocalizationKeyConstants.FORECASTGROUPS;
import static com.ideas.tetris.ui.common.component.date.customdateselector.LocalizationKeyConstants.LAST_UPDATED_DATE;
import static com.ideas.tetris.ui.common.component.date.customdateselector.LocalizationKeyConstants.MARKET_SEGMENTS;
import static com.ideas.tetris.ui.common.component.date.customdateselector.LocalizationKeyConstants.MAXIMUM_ROOM_CLASSES_ALLOWED_FOR_CHANGE_REPORT;
import static com.ideas.tetris.ui.common.component.date.customdateselector.LocalizationKeyConstants.MORE_THAN_44_ELEMENT_CANT_BE_SELECTED_UNDER_DATA_SELECTION_CRITERIA;
import static com.ideas.tetris.ui.common.component.date.customdateselector.LocalizationKeyConstants.MORE_THAN_8_ELEMENT_CANT_BE_SELECTED_UNDER_DATA_SELECTION_CRITERIA;
import static com.ideas.tetris.ui.common.component.date.customdateselector.LocalizationKeyConstants.MUST_SELECT_AT_LEAST_ONE_FILTER;
import static com.ideas.tetris.ui.common.component.date.customdateselector.LocalizationKeyConstants.ONLY_ONE_PRODUCT_CHECKBOX_SHOULD_BE_SELECTED_ON_SELECTING_PARAMETER_PRODUCT_MESSAGE;
import static com.ideas.tetris.ui.common.component.date.customdateselector.LocalizationKeyConstants.ROLLING_DATE_IN_FUTURE_VALIDATION_MSG;
import static com.ideas.tetris.ui.common.component.date.customdateselector.LocalizationKeyConstants.ROOM_CLASS;
import static com.ideas.tetris.ui.common.component.date.customdateselector.LocalizationKeyConstants.ROOM_TYPE;
import static com.ideas.tetris.ui.common.component.date.customdateselector.LocalizationKeyConstants.SYSTEM_DATE;
import static com.ideas.tetris.ui.common.component.date.customdateselector.LocalizationKeyConstants.TOTAL_GROUP;
import static com.ideas.tetris.ui.common.component.date.customdateselector.LocalizationKeyConstants.TOTAL_TRANSIENT;
import static com.ideas.tetris.ui.modules.reports.reportscheduler.JasperReportConstants.BT_PREFIX;
import static com.ideas.tetris.ui.modules.reports.reportscheduler.JasperReportConstants.BV_PREFIX;
import static com.ideas.tetris.ui.modules.reports.reportscheduler.JasperReportConstants.COMP;
import static com.ideas.tetris.ui.modules.reports.reportscheduler.JasperReportConstants.DEFAULT_DATE_FOR_REPORTS;
import static com.ideas.tetris.ui.modules.reports.reportscheduler.JasperReportConstants.FALSE_VALUE;
import static com.ideas.tetris.ui.modules.reports.reportscheduler.JasperReportConstants.FG_PREFIX;
import static com.ideas.tetris.ui.modules.reports.reportscheduler.JasperReportConstants.INCLUDEINACTIVERT;
import static com.ideas.tetris.ui.modules.reports.reportscheduler.JasperReportConstants.IS_CHANGE_REPORT_COMPARATIVE_VIEW_BV_OPTIMIZATION_ENABLED;
import static com.ideas.tetris.ui.modules.reports.reportscheduler.JasperReportConstants.IS_CP_PACE_DIFFERENTIAL_TABLE_USE_ENABLED;
import static com.ideas.tetris.ui.modules.reports.reportscheduler.JasperReportConstants.IS_IGNORE_PAGINATION;
import static com.ideas.tetris.ui.modules.reports.reportscheduler.JasperReportConstants.IS_PHYSICAL_CAPACITY;
import static com.ideas.tetris.ui.modules.reports.reportscheduler.JasperReportConstants.MAXIMUM_ROOM_CLASS_SELECTION_ALLOWED_FOR_CHANGE_REPROT;
import static com.ideas.tetris.ui.modules.reports.reportscheduler.JasperReportConstants.MAX_LOS;
import static com.ideas.tetris.ui.modules.reports.reportscheduler.JasperReportConstants.MS_PREFIX;
import static com.ideas.tetris.ui.modules.reports.reportscheduler.JasperReportConstants.OUTPUT;
import static com.ideas.tetris.ui.modules.reports.reportscheduler.JasperReportConstants.PACMAN_BAR_MAX_LOS;
import static com.ideas.tetris.ui.modules.reports.reportscheduler.JasperReportConstants.PARAM_BASE_CURRENCY;
import static com.ideas.tetris.ui.modules.reports.reportscheduler.JasperReportConstants.PARAM_BUSINESS_ENDDATE;
import static com.ideas.tetris.ui.modules.reports.reportscheduler.JasperReportConstants.PARAM_BUSINESS_ROLLING_END_DATE;
import static com.ideas.tetris.ui.modules.reports.reportscheduler.JasperReportConstants.PARAM_BUSINESS_ROLLING_START_DATE;
import static com.ideas.tetris.ui.modules.reports.reportscheduler.JasperReportConstants.PARAM_BUSINESS_STARTDATE;
import static com.ideas.tetris.ui.modules.reports.reportscheduler.JasperReportConstants.PARAM_BUSINESS_TYPES;
import static com.ideas.tetris.ui.modules.reports.reportscheduler.JasperReportConstants.PARAM_BUSINESS_VIEWS;
import static com.ideas.tetris.ui.modules.reports.reportscheduler.JasperReportConstants.PARAM_DATA_SELECTION_TYPE;
import static com.ideas.tetris.ui.modules.reports.reportscheduler.JasperReportConstants.PARAM_ENDDATE;
import static com.ideas.tetris.ui.modules.reports.reportscheduler.JasperReportConstants.PARAM_FORECAST_GROUPS;
import static com.ideas.tetris.ui.modules.reports.reportscheduler.JasperReportConstants.PARAM_INCLUDE_DISCONTINUED_MS;
import static com.ideas.tetris.ui.modules.reports.reportscheduler.JasperReportConstants.PARAM_ISHOTELCHECKED;
import static com.ideas.tetris.ui.modules.reports.reportscheduler.JasperReportConstants.PARAM_ISROLLING_DATE;
import static com.ideas.tetris.ui.modules.reports.reportscheduler.JasperReportConstants.PARAM_IS_ADR_CHECKED;
import static com.ideas.tetris.ui.modules.reports.reportscheduler.JasperReportConstants.PARAM_IS_BAR_BY_DAY;
import static com.ideas.tetris.ui.modules.reports.reportscheduler.JasperReportConstants.PARAM_IS_BAR_CHECKED;
import static com.ideas.tetris.ui.modules.reports.reportscheduler.JasperReportConstants.PARAM_IS_LRV_CHECKED;
import static com.ideas.tetris.ui.modules.reports.reportscheduler.JasperReportConstants.PARAM_IS_OCCUPANCY_FCST_CHECKED;
import static com.ideas.tetris.ui.modules.reports.reportscheduler.JasperReportConstants.PARAM_IS_OOO_CHECKED;
import static com.ideas.tetris.ui.modules.reports.reportscheduler.JasperReportConstants.PARAM_IS_OVERBOOKING_CHECKED;
import static com.ideas.tetris.ui.modules.reports.reportscheduler.JasperReportConstants.PARAM_IS_PROFIT_CHECKED;
import static com.ideas.tetris.ui.modules.reports.reportscheduler.JasperReportConstants.PARAM_IS_PROPAR_CHECKED;
import static com.ideas.tetris.ui.modules.reports.reportscheduler.JasperReportConstants.PARAM_IS_PROPOR_CHECKED;
import static com.ideas.tetris.ui.modules.reports.reportscheduler.JasperReportConstants.PARAM_IS_REVENUE_CHECKED;
import static com.ideas.tetris.ui.modules.reports.reportscheduler.JasperReportConstants.PARAM_IS_REVPAR_CHECKED;
import static com.ideas.tetris.ui.modules.reports.reportscheduler.JasperReportConstants.PARAM_IS_SPECIAL_EVENT_CHECKED;
import static com.ideas.tetris.ui.modules.reports.reportscheduler.JasperReportConstants.PARAM_MARKET_SEGMENTS;
import static com.ideas.tetris.ui.modules.reports.reportscheduler.JasperReportConstants.PARAM_PRODUCTS;
import static com.ideas.tetris.ui.modules.reports.reportscheduler.JasperReportConstants.PARAM_PRODUCT_NAMES;
import static com.ideas.tetris.ui.modules.reports.reportscheduler.JasperReportConstants.PARAM_ROLLING_END_DATE;
import static com.ideas.tetris.ui.modules.reports.reportscheduler.JasperReportConstants.PARAM_ROLLING_START_DATE;
import static com.ideas.tetris.ui.modules.reports.reportscheduler.JasperReportConstants.PARAM_ROOM_CLASS;
import static com.ideas.tetris.ui.modules.reports.reportscheduler.JasperReportConstants.PARAM_ROOM_CLASSES;
import static com.ideas.tetris.ui.modules.reports.reportscheduler.JasperReportConstants.PARAM_ROOM_TYPES;
import static com.ideas.tetris.ui.modules.reports.reportscheduler.JasperReportConstants.PARAM_SHOW_AGGREGATED;
import static com.ideas.tetris.ui.modules.reports.reportscheduler.JasperReportConstants.PARAM_STARTDATE;
import static com.ideas.tetris.ui.modules.reports.reportscheduler.JasperReportConstants.RC_PREFIX;
import static com.ideas.tetris.ui.modules.reports.reportscheduler.JasperReportConstants.REPORT_UNIT;
import static com.ideas.tetris.ui.modules.reports.reportscheduler.JasperReportConstants.RT_PREFIX;
import static com.ideas.tetris.ui.modules.reports.reportscheduler.JasperReportConstants.TRUE_VALUE;
import static com.ideas.tetris.ui.modules.reports.reportscheduler.JasperReportConstants.USE_COMPACT_WEBRATE_PACE;
import static com.ideas.tetris.ui.modules.reports.reportscheduler.JasperReportConstants.PARAM_ROOM_TYPE;
import static com.ideas.tetris.ui.modules.reports.reportscheduler.JasperReportConstants.JASPER_REPORT_URI;
import static com.ideas.tetris.ui.modules.reports.util.URLParamUtil.TIME_FRAME_PARAMETER_LOWERCASE;
import org.springframework.beans.factory.annotation.Autowired;
import com.ideas.tetris.spring.SpringAutowired;

@SpringAutowired
public class PickUpAndChangeReportPresenter extends AbstractReportPresenter<PickUpAndChangeReportView, Void> {
    private static final int FIRST_ITEM = 0;
    private static final String MINUS = "-";
    private static final String ZERO = "0";
    private static final String ONE = "1";
    private static final String DEFAULT_CURRENCY = "USD";
    private static final int SECOND_ITEM = 1;
    static final int PARENT_SELECTION_ID = -1;
    private static final int MAX_ITEMS_FOR_SIDE_BY_SIDE_VIEW = 8;
    private static final int MAX_ITEMS_FOR_SIDE_BY_SIDE_VIEW_FOR_INCREASE_MS_LIMIT_ENABLED = 44;
    private static final int TRANSIENT_ID = 2;
    private static final int GROUP_ID = 1;
    private static final Integer PICK_UP_REPORT_ID = 1;
    private static final Integer CHANGE_REPORT_ID = 2;
    public static final String PICK_UP = "pickUp";
    public static final String REPORTTYPE = "reporttype";
    private static HashMap<String, String> urlLevelValuesMap = new HashMap<>();
    private static final String BAR_PRODUCT_ID = "1";
    private static final String BAR_PRODUCT_NAME = "BAR";

    static {
        urlLevelValuesMap.put("PROPERTY", PARAM_ISHOTELCHECKED);
        urlLevelValuesMap.put("BUSINESS_TYPE", PARAM_BUSINESS_TYPES);
        urlLevelValuesMap.put("ROOM_CLASS", PARAM_ROOM_CLASSES);
        urlLevelValuesMap.put("ROOM_TYPE", PARAM_ROOM_TYPES);
        urlLevelValuesMap.put("FORECAST_GROUP", PARAM_FORECAST_GROUPS);
        urlLevelValuesMap.put("PROPERTY_BUSINESS_VIEW", PARAM_BUSINESS_VIEWS);
        urlLevelValuesMap.put("MARKET_SEGMENT", PARAM_MARKET_SEGMENTS);
    }

    private boolean pageLoadSuccessful = false;
    private PickUpAndChangeFilterDTO filterDTO;
    @Autowired
	private ForecastGroupFinalService forecastGroupFinalService;
    @Inject
    private SharedSessionState sharedSessionState;
    @Autowired
	private WebrateShoppingDataService webrateShoppingDataService;
    @Autowired
	private MarketSegmentService service;
    @Autowired
	private BusinessGroupService businessGroupService;
    @Autowired
	private PacmanConfigParamsService configParamsService;
    @Autowired
	private AgileRatesConfigurationService agileRatesConfigurationService;
    private DataLevelUIWrapper totalHotel;

    @Override
    public void onWorkContextChange(WorkContextType workContextType) {
        super.onWorkContextChange(workContextType);
        resetFilterData();
        view.resetPickUpAndChangeView(isScheduleEnabled());
    }

    @Override
    protected void loadInfoFromUrl() {
        Map<String, String> paramMap = getParameterMap();
        if (PICK_UP.equalsIgnoreCase(paramMap.get(REPORTTYPE))) {
            createFilterDTOWithDefaultValues(false, false, false);
            setAnalysisDatesFromURLParameters(paramMap);
            setActivityDatesFromURLParameters(paramMap);
        } else {
            createFilterDTOWithDefaultValues(true, false, false);
            setAnalysisDatesFromURLParameters(paramMap);
            String dateFormat = filterDTO.getActivityDateSelectorBean().getDateFormat();
            filterDTO.getActivityDateSelectorBean().setStartDate(getFormattedDate(filterDTO.getAnalysisDateSelectorBean().getSpecificStartDate().minusDays(1), dateFormat));
        }
        view.initForm(filterDTO, uiContext.getSystemCaughtUpDateAsJavaLocalDate(), false);
        view.setLevelAndSubLevel(filterDTO, urlLevelValuesMap.get(paramMap.get("level")), paramMap.get("sublevel"), paramMap.get("subtype"));
    }

    @Override
    public void onViewOpened(Void aVoid) {
        Map<String, String> params = URLParamUtil.getParameterMap();
        if (enableSavedConfigInReports() && Objects.nonNull(params) && "true".equalsIgnoreCase(params.get("issavedreport"))) {
            isSavedReports = true;
            initDataForSavedReports();
        } else {
            isSavedReports = false;
            initData();
        }
    }

    protected Map<String, String> getParameterMap() {
        return URLParamUtil.getParameterMap();
    }

    private void setActivityDatesFromURLParameters(Map<String, String> paramMap) {
        LocalDate activityEndDate = URLParamUtil.extractDateFromString(paramMap.get("businessdt"));
        LocalDate activityStartDate = URLParamUtil.extractDateFromString(paramMap.get("lastbusinessdt"));
        String dateFormat = filterDTO.getActivityDateSelectorBean().getDateFormat();
        filterDTO.getActivityDateSelectorBean().setStartDate(getFormattedDate(activityStartDate, dateFormat));
        filterDTO.getActivityDateSelectorBean().setEndDate(getFormattedDate(activityEndDate, dateFormat));
    }

    private void setAnalysisDatesFromURLParameters(Map<String, String> paramMap) {
        String timeFrameValue = paramMap.get(TIME_FRAME_PARAMETER_LOWERCASE);
        LocalDate analysisStartDate = URLParamUtil.getDateRangeFromParam(timeFrameValue).getStartDate();
        LocalDate analysisEndDate = URLParamUtil.getDateRangeFromParam(timeFrameValue).getEndDate();
        String dateFormat = filterDTO.getAnalysisDateSelectorBean().getDateFormat();
        filterDTO.getAnalysisDateSelectorBean().setStartDate(getFormattedDate(analysisStartDate, dateFormat));
        filterDTO.getAnalysisDateSelectorBean().setEndDate(getFormattedDate(analysisEndDate, dateFormat));
    }

    private static String getFormattedDate(LocalDate activityStartDate, String dateFormat) {
        return DateFormatUtil.formatDate(activityStartDate, dateFormat);
    }

    @Override
    protected void resetFilterData() {
        view.toggleSaveButtonVisibility();
        sharedSessionState.setStoredPickUpAndChangeFilterDTO(null);
        filterDTO = createFilterDTOWithDefaultValues(false, false, false);
        view.setWebRateCompetitorsData(getAllCompetitorsByAccomClass());
        view.setSelectedCompetitors(filterDTO.getWebrateCompetitors());
        view.initForm(filterDTO, uiContext.getSystemCaughtUpDateAsJavaLocalDate(), false);
        view.setAgileProductSelectionWrapper(getApplicableProducts(isChangeReport() && PARAM_ROOM_TYPES.equals(filterDTO.getDataSelectionType())));
    }

    public boolean enableSavedConfigInReports() {
        return configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.ENABLE_SAVE_CONFIG_IN_REPORTS);
    }

    public boolean isScheduleReportFlow() {
        return !isSavedReports;
    }

    public void regenerateRoomTypeOrMktSegDataSelection(boolean includeDiscontinuedRTs, boolean includeDisconMS) {
        filterDTO.setDataLevelUIWrappers(generateDataSelections(includeDiscontinuedRTs, includeDisconMS));
    }

    private PickUpAndChangeFilterDTO createFilterDTOWithDefaultValues(boolean isChangeReport, boolean includeDiscontinuedRTs, boolean includeDisconMS) {
        filterDTO = new PickUpAndChangeFilterDTO();
        ViewByOption viewByOption = isChangeReport ? view.getViewByOptions().get(SECOND_ITEM) : view.getViewByOptions().get(FIRST_ITEM);
        filterDTO.setDataLevelUIWrappers(generateDataSelections(includeDiscontinuedRTs, includeDisconMS));
        filterDTO.setPropertyID(uiContext.getPropertyId());
        filterDTO.setReportType(viewByOption);
        filterDTO.getParameters().enableRequiredParametersFor(PARAM_ISHOTELCHECKED, isPickUpReport());
        filterDTO.setAnalysisDateSelectorBean(createAnalysisDateSelectorBean());
        RollingDateOptionsChoice rollingDateOptionsChoice = (isChangeReport) ? new RollingDatesWithLastOptimization() : new RollingDatesWithLastUpdatedDate();
        JavaDateSelectorBean activityDateSelectorBean = new JavaDateSelectorBean(uiContext.getSystemCaughtUpDateAsJavaLocalDate(), rollingDateOptionsChoice);
        filterDTO.setActivityDateSelectorBean(getActivityDateSelectorBean(activityDateSelectorBean));
        return filterDTO;
    }


    private JavaDateSelectorBean createAnalysisDateSelectorBean() {
        Integer maxNegativeOffset = getMaxPacePointAllowedForReport();
        LocalDate maxPastDate = uiContext.getSystemCaughtUpDateAsJavaLocalDate().minusDays(maxNegativeOffset).minusDays(1);
        LocalDate maxFutureDate = uiContext.getSystemCaughtUpDateAsJavaLocalDate().plusDays(uiContext.getForecastWindowOffsetBDE());
        JavaDateSelectorBean analysisDates = new JavaDateSelectorBean(uiContext.getSystemCaughtUpDateAsJavaLocalDate(), new RollingDateWithMonthAndEndYearOption());
        setDefaultAnalysisDates(analysisDates);
        analysisDates.setStartStaticDateRange(new JavaDateRange(maxPastDate, maxFutureDate));
        analysisDates.setEndStaticDateRange(new JavaDateRange(analysisDates.getSpecificStartDate(), maxFutureDate));
        analysisDates.setStartRollingDateRange(new RollingDateRange(maxNegativeOffset * -1, uiContext.getForecastWindowOffsetBDE()));
        analysisDates.setEndRollingDateRange(new RollingDateRange(null, uiContext.getForecastWindowOffsetBDE()));
        analysisDates.setStartDateCaption(getTextFromResourceBundle(ANALYSIS_START_DATE_LABEL));
        analysisDates.setEndDateCaption(getTextFromResourceBundle(ANALYSIS_END_DATE));
        return analysisDates;
    }

    private void setDefaultAnalysisDates(JavaDateSelectorBean analysisDates) {
        analysisDates.setRollingDate(false);
        analysisDates.setStartDate(formatDateFor(uiContext.getSystemCaughtUpDateAsJavaLocalDate()));
        analysisDates.setEndDate(formatDateFor(uiContext.getSystemCaughtUpDateAsJavaLocalDate().plusDays(THIRTY_DAYS)));
    }

    protected String formatDateFor(LocalDate systemCaughtUpDateAsLocalDate) {
        return DateFormatUtil.formatDateFor(systemCaughtUpDateAsLocalDate, UiUtils.getLocale());
    }

    private JavaDateSelectorBean getActivityDateSelectorBean(JavaDateSelectorBean activityDates) {
        setDefaultActivityDates(activityDates);
        activityDates.setStartRollingDateRange(new RollingDateRange(null, 0));
        activityDates.setEndRollingDateRange(new RollingDateRange(null, 0));
        activityDates.setStartDateCaption(getTextFromResourceBundle(ACTIVITY_START_DATE));
        activityDates.setEndDateCaption(getTextFromResourceBundle(ACTIVITY_END_DATE));
        return activityDates;
    }

    private void setDefaultActivityDates(JavaDateSelectorBean activityDates) {
        LocalDate maxActivityEndDate = uiContext.getSystemCaughtUpDateAsJavaLocalDate().minusDays(1);
        LocalDate initialActivityStartDate = uiContext.getSystemCaughtUpDateAsJavaLocalDate().minusDays(7);
        activityDates.setStartDate(formatDateFor(initialActivityStartDate));
        activityDates.setEndDate(formatDateFor(maxActivityEndDate));
        activityDates.setStartStaticDateRange(new JavaDateRange(null, maxActivityEndDate));
        activityDates.setEndStaticDateRange(new JavaDateRange(activityDates.getSpecificStartDate(), maxActivityEndDate));
        activityDates.setRollingDate(false);
    }

    private Set<WebrateCompetitors> getAllCompetitorsByAccomClass() {
        List<AccomClassSummary> accomClassByProperty = getAccomClassByProperty();
        return getWebrateCompetitors(accomClassByProperty);
    }

    private Set<WebrateCompetitors> getWebrateCompetitors(List<AccomClassSummary> selectedAccomClasses) {
        List<Integer> accomClassIds = selectedAccomClasses.stream().map(p -> p.getId()).collect(Collectors.toList());
        return new HashSet<>(getCompetitorsByAccomClass(accomClassIds));
    }

    protected Set<WebrateCompetitors> getCompetitorsByAccomClass(List<Integer> listRoomClassIds) {
        if (null != listRoomClassIds && !listRoomClassIds.isEmpty()) {
            return new HashSet<>(webrateShoppingDataService.getAllCompetitorsByRoomClass(listRoomClassIds));
        }
        return new HashSet<>();
    }


    private List<AccomClassSummary> getAccomClassByProperty() {
        List<AccomClassSummary> roomClasses = new ArrayList<>();
        for (AccomClassSummary accomClassSummary : accommodationServiceLocal.getAllAccomClassSummaries()) {
            if (accomClassSummary.isAccomTypesAssigned()) {
                roomClasses.add(accomClassSummary);
            }
        }
        return roomClasses;
    }

    @Override
    protected void editScheduleData(Map<String, String> scheduleParams, Boolean isEdit, ScheduleReportDTO scheduleReportDTO) {
        sharedSessionState.setStoredPickUpAndChangeFilterDTO(null);
        String reportJasperURI = scheduleReportDTO.getReportJasperURI();
        boolean isChangeReport = false;
        if (null != reportJasperURI && reportJasperURI.startsWith("Change")) {
            isChangeReport = true;
        }
        reportJasperURI = scheduleParams.get(REPORT_UNIT);
        if (null != reportJasperURI && reportJasperURI.contains("Change")) {
            isChangeReport = true;
        }

        createFilterDTOForScheduleData(scheduleParams, isChangeReport);
        filterDTO.setParameters(readValuesFromParameterMap(scheduleParams));

        Set<WebrateCompetitors> allCompetitors = getAllCompetitorsByAccomClass();
        if (Boolean.TRUE.equals(isEdit)) {
            filterDTO.setWebrateCompetitors(getWebrateCompetitorsById(scheduleParams, new ArrayList<>(allCompetitors)));
        } else {
            List<String> compList = new ArrayList<>();
            for (int counter = 1; counter <= 15; counter++) {
                compList.add(scheduleParams.get(COMP + counter));
            }
            filterDTO.setWebrateCompetitors(getWebrateCompetitors(new ArrayList<>(allCompetitors), compList));
        }
        setDateRange(scheduleParams);
        filterDTO.getParameters().setAggregationSelected(Boolean.valueOf(scheduleParams.get(PARAM_SHOW_AGGREGATED)));

        String dataSelectionTypeForEditFlow = getDataSelectionTypeForEditFlow(scheduleParams);
        if (!PARAM_ISHOTELCHECKED.equals(dataSelectionTypeForEditFlow)) {
            setSelectedUIWrappersInEditFlow(scheduleParams);
        }
        view.initForm(filterDTO, uiContext.getSystemCaughtUpDateAsJavaLocalDate(), isEdit);

    }

    private void setDateRange(Map<String, String> scheduleParams) {
        if (ONE.equals(scheduleParams.get(PARAM_ISROLLING_DATE))) {
            setStartAndEndDatesFromSchedule(scheduleParams, filterDTO.getAnalysisDateSelectorBean(), PARAM_ROLLING_START_DATE, PARAM_ROLLING_END_DATE);
            setStartAndEndDatesFromSchedule(scheduleParams, filterDTO.getActivityDateSelectorBean(), PARAM_BUSINESS_ROLLING_START_DATE, PARAM_BUSINESS_ROLLING_END_DATE);
        } else {
            filterDTO.getAnalysisDateSelectorBean().setRollingDate(false);
            filterDTO.getActivityDateSelectorBean().setRollingDate(false);
            setStartAndEndDatesForOnlineReport(filterDTO.getAnalysisDateSelectorBean(), scheduleParams.get(PARAM_START_DATE), scheduleParams.get(PARAM_END_DATE));
            setStartAndEndDatesForOnlineReport(filterDTO.getActivityDateSelectorBean(), scheduleParams.get(PARAM_BUSINESS_START_DATE), scheduleParams.get(PARAM_BUSINESS_END_DATE));

        }
    }

    private void createFilterDTOForScheduleData(Map<String, String> scheduleParams, boolean isChangeReport) {
        boolean includeDiscontinuedRTs = !showDiscontinuedRTsCheckbox() || ONE.equals(scheduleParams.getOrDefault(INCLUDEINACTIVERT, ONE));

        boolean includeDiscontinuedMS = !showDiscontinuedMSCheckbox() || ONE.equals(scheduleParams.getOrDefault(PARAM_INCLUDE_DISCONTINUED_MS, ONE));

        filterDTO = createFilterDTOWithDefaultValues(isChangeReport, includeDiscontinuedRTs, includeDiscontinuedMS);
        filterDTO.setShowDiscontinuedRTs(includeDiscontinuedRTs);
        filterDTO.setShowDiscontinuedMS(includeDiscontinuedMS);
    }


    private void setStartAndEndDatesFromSchedule(Map<String, String> parameters, JavaDateSelectorBean dateSelectorBean, String startDateParam, String endDateParam) {
        dateSelectorBean.setRollingDate(true);
        dateSelectorBean.setStartDate(dateSelectorBean.convertSelectedRollingDateToRollingDateCaption(parameters.get(startDateParam), dateSelectorBean.getStartRollingDateOptions()));
        dateSelectorBean.setEndDate(dateSelectorBean.convertSelectedRollingDateToRollingDateCaption(parameters.get(endDateParam), dateSelectorBean.getEndRollingDateOptions()));
        dateSelectorBean.setDateFormat(DateFormatUtil.getDateFormatString());
    }

    private void setStartAndEndDatesForOnlineReport(JavaDateSelectorBean dateSelectorBean, String startDateParam, String endDateParam) {
        dateSelectorBean.setRollingDate(false);
        dateSelectorBean.setStartDate(formatDate(startDateParam));
        dateSelectorBean.setEndDate(formatDate(endDateParam));
        dateSelectorBean.setDateFormat(DateFormatUtil.getDateFormatString());
    }

    @Override
    protected void loadSharedSessionStateData() {
        filterDTO = sharedSessionState.getStoredPickUpAndChangeFilterDTO();
        filterDTO.getDataLevelUIWrappers().forEach(dataLevelUIWrapper -> dataLevelUIWrapper.setSelected(false));
        DateFormatUtil.formatDateInBeanAndSetDateFormat(filterDTO.getAnalysisDateSelectorBean());
        DateFormatUtil.formatDateInBeanAndSetDateFormat(filterDTO.getActivityDateSelectorBean());

        view.setWebRateCompetitorsData(getAllCompetitorsByAccomClass());
        view.setSelectedCompetitors(filterDTO.getWebrateCompetitors());
        view.initForm(filterDTO, uiContext.getSystemCaughtUpDateAsJavaLocalDate(), false);
    }


    @Override
    protected void saveFilterDataToSession() {
        sharedSessionState.setStoredPickUpAndChangeFilterDTO(filterDTO);
    }

    @Override
    protected ReportCriteria getReportCriteria(ScheduledReportDisplayDTO scheduledReportDisplayDTO) {
        return null;
    }

    @Override
    protected ReportType getReportType() {
        return ReportType.PICKUP_AND_CHANGE;
    }

    @Override
    protected Map<String, String> getReportParameters() {
        Map<String, String> parameterMap = new HashMap<>();
        JavaDateSelectorBean activityDates = filterDTO.getActivityDateSelectorBean();
        parameterMap.put(IS_PHYSICAL_CAPACITY, isEnablePhysicalCapacityConsideration() ? ONE : ZERO);
        parameterMap.put(IS_CP_PACE_DIFFERENTIAL_TABLE_USE_ENABLED, ONE);
        parameterMap.put(IS_CHANGE_REPORT_COMPARATIVE_VIEW_BV_OPTIMIZATION_ENABLED, SystemConfig.isChangeReportComparativeViewBvOptimisationEnabled() ? ONE : ZERO);
        parameterMap.put(MAX_LOS, configParamsService.getParameterValue(PACMAN_BAR_MAX_LOS));
        parameterMap.put(IS_IGNORE_PAGINATION, FALSE_VALUE);
        parameterMap.put(PARAM_BASE_CURRENCY, DEFAULT_CURRENCY);
        parameterMap.put(PARAM_IS_BAR_BY_DAY, String.valueOf(isBarByDay()));
        List<Product> selectedProducts = view.getSelectedProducts();
        parameterMap.put(PARAM_PRODUCTS, retrieveSelectedProductIds(selectedProducts));
        parameterMap.put(PARAM_PRODUCT_NAMES, retrieveSelectedProductNames(selectedProducts));

        if (showDiscontinuedRTsCheckbox()) {
            parameterMap.put(INCLUDEINACTIVERT, getZeroCapacityRtValue());
        }

        if (showDiscontinuedMSCheckbox()) {
            parameterMap.put(PARAM_INCLUDE_DISCONTINUED_MS, getDiscontinuedMktSegValue());
        }

        setSelectedParameters(parameterMap, filterDTO.getParameters());

        setDateValues(parameterMap, activityDates);

        setCompetitorParameters(parameterMap);

        if (ReportFormatType.EXCEL.equals(view.getSelectedReportType())) {
            parameterMap.put(OUTPUT, getOutputFormat().getType());
            parameterMap.put(IS_IGNORE_PAGINATION, TRUE_VALUE);
        }

        setParameterIDsAndNames(parameterMap);

        if (Boolean.TRUE.equals(configParamsService.getParameterValue(IPConfigParamName.USE_COMPACT_WEBRATE_PACE))) {
            parameterMap.put(USE_COMPACT_WEBRATE_PACE, TRUE_VALUE);
        } else {
            parameterMap.put(USE_COMPACT_WEBRATE_PACE, FALSE_VALUE);
        }
        parameterMap.put(JASPER_REPORT_URI, getJasperReportType().getReportJasperURI());
        return parameterMap;
    }

    private void setParameterIDsAndNames(Map<String, String> parameterMap) {
        String dataSelectionType = filterDTO.getDataSelectionType();
        List<DataLevelUIWrapper> dataLevelUIWrappers = getSelectedDataChildren();
        dataLevelUIWrappers.sort((dataLevelUIWrapper, t1) -> dataLevelUIWrapper.getId().compareTo(t1.getId()));
        List<String> selectedDataIDs = new ArrayList<>();
        List<String> selectedDataNames = new ArrayList<>();

        dataLevelUIWrappers.stream().forEach(dataLevelUIWrapper -> {
            if (dataLevelUIWrapper.isSelected()) {
                selectedDataIDs.add(dataLevelUIWrapper.getId().toString());
                selectedDataNames.add(dataLevelUIWrapper.getName());
            }
        });

        Map<String, DataSelectionParameters> parametersMap = DataSelectionParameters.getParametersMap();
        if (parametersMap.containsKey(dataSelectionType)) {
            parameterMap.put(parametersMap.get(dataSelectionType).getParamIds(), String.join(",", selectedDataIDs));
            parameterMap.put(parametersMap.get(dataSelectionType).getParamNames(), String.join(",", selectedDataNames));
        }

        if (isChangeReport() && PARAM_ROOM_CLASSES.equals(dataSelectionType)) {
            parameterMap.put(PARAM_ROOM_CLASS, parameterMap.get(PARAM_ROOM_CLASSES));
        }
    }

    public String retrieveSelectedProductIds(List<Product> selectedProducts) {
        if (filterDTO.getParameters().isBARSelected() && !selectedProducts.isEmpty()) {
            return selectedProducts.stream().map(product -> String.valueOf(product.getId())).collect(Collectors.joining(","));
        } else if (filterDTO.getParameters().isBARSelected()) {
            return BAR_PRODUCT_ID;
        } else {
            return StringUtil.EMPTY_STRING;
        }
    }

    public String retrieveSelectedProductNames(List<Product> selectedProducts) {
        if (filterDTO.getParameters().isBARSelected() && !selectedProducts.isEmpty()) {
            return selectedProducts.stream().map(product -> String.valueOf(product.getName())).collect(Collectors.joining(","));
        } else if (filterDTO.getParameters().isBARSelected()) {
            return BAR_PRODUCT_NAME;
        } else {
            return StringUtil.EMPTY_STRING;
        }
    }

    private void setCompetitorParameters(Map<String, String> parameterMap) {
        int counter = 1;
        for (Integer id : getSelectedCompetitors()) {
            parameterMap.put(COMP + (counter), String.valueOf(id));
            counter++;
        }
    }

    private String getZeroCapacityRtValue() {
        return filterDTO.isShowDiscontinuedRTs() ? ONE : ZERO;
    }

    private String getDiscontinuedMktSegValue() {
        return filterDTO.isShowDiscontinuedMS() ? ONE : ZERO;
    }

    private void setDateValues(Map<String, String> parameterMap, JavaDateSelectorBean activityDates) {
        parameterMap.put(PARAM_ISROLLING_DATE, filterDTO.getAnalysisDateSelectorBean().isRollingDate() ? ONE : ZERO);
        if (filterDTO.getAnalysisDateSelectorBean().isRollingDate()) {
            parameterMap.put(PARAM_STARTDATE, DEFAULT_DATE_FOR_REPORTS);
            parameterMap.put(PARAM_ENDDATE, DEFAULT_DATE_FOR_REPORTS);
            parameterMap.put(PARAM_BUSINESS_STARTDATE, DEFAULT_DATE_FOR_REPORTS);
            parameterMap.put(PARAM_BUSINESS_ENDDATE, DEFAULT_DATE_FOR_REPORTS);
            parameterMap.put(PARAM_ROLLING_START_DATE, filterDTO.getAnalysisDateSelectorBean().getRollingStartDate());
            parameterMap.put(PARAM_ROLLING_END_DATE, filterDTO.getAnalysisDateSelectorBean().getRollingEndDate());
            parameterMap.put(PARAM_BUSINESS_ROLLING_START_DATE, activityDates.getRollingStartDate());
            parameterMap.put(PARAM_BUSINESS_ROLLING_END_DATE, activityDates.getRollingEndDate());
            if (isChangeReport()) {
                parameterMap.put(PARAM_BUSINESS_ROLLING_END_DATE, activityDates.getRollingStartDate());
            }

        } else {
            parameterMap.put(PARAM_STARTDATE, filterDTO.getAnalysisDateSelectorBean().getSpecificStartDate().toString());
            parameterMap.put(PARAM_ENDDATE, filterDTO.getAnalysisDateSelectorBean().getSpecificEndDate().toString());
            parameterMap.put(PARAM_BUSINESS_STARTDATE, activityDates.getSpecificStartDate().toString().replace("-", ""));
            parameterMap.put(PARAM_BUSINESS_ENDDATE, activityDates.getSpecificEndDate().toString().replace("-", ""));
            if (isChangeReport()) {
                parameterMap.put(PARAM_BUSINESS_ENDDATE, activityDates.getSpecificStartDate().toString().replace("-", ""));
            }
            parameterMap.put(PARAM_ROLLING_START_DATE, null);
            parameterMap.put(PARAM_ROLLING_END_DATE, null);
            parameterMap.put(PARAM_BUSINESS_ROLLING_START_DATE, null);
            parameterMap.put(PARAM_BUSINESS_ROLLING_END_DATE, null);
        }

    }

    private void setSelectedParameters(Map<String, String> parameterMap, PickUpAndChangeParameters parameters) {
        parameterMap.put(PARAM_IS_ADR_CHECKED, parameters.isADRSelected().toString());
        parameterMap.put(PARAM_IS_BAR_CHECKED, parameters.isBARSelected().toString());
        parameterMap.put(PARAM_IS_OCCUPANCY_FCST_CHECKED, parameters.isOccupancySelected().toString());
        parameterMap.put(PARAM_IS_LRV_CHECKED, parameters.isLrvSelected().toString());
        parameterMap.put(PARAM_IS_OOO_CHECKED, parameters.isOOOSelected().toString());
        parameterMap.put(PARAM_IS_OVERBOOKING_CHECKED, parameters.isOverbookingSelected().toString());
        parameterMap.put(PARAM_IS_REVPAR_CHECKED, parameters.isRevPARSelected().toString());
        parameterMap.put(PARAM_IS_REVENUE_CHECKED, parameters.isRevenueSelected().toString());
        parameterMap.put(PARAM_IS_SPECIAL_EVENT_CHECKED, parameters.isSpecialEventSelected().toString());
        if (isProfitMetricsEnabledForPickUpAndChange()) {
            parameterMap.put(PARAM_IS_PROFIT_CHECKED, parameters.isProfitSelected().toString());
            parameterMap.put(PARAM_IS_PROPOR_CHECKED, parameters.isProPORSelected().toString());
            parameterMap.put(PARAM_IS_PROPAR_CHECKED, parameters.isProPARSelected().toString());
        } else {
            parameterMap.put(PARAM_IS_PROFIT_CHECKED, "false");
            parameterMap.put(PARAM_IS_PROPOR_CHECKED, "false");
            parameterMap.put(PARAM_IS_PROPAR_CHECKED, "false");
        }
    }

    protected PickUpAndChangeParameters readValuesFromParameterMap(Map<String, String> parameterMap) {
        PickUpAndChangeParameters parameters = new PickUpAndChangeParameters();
        parameters.setADRSelected(Boolean.valueOf(parameterMap.get(PARAM_IS_ADR_CHECKED)));
        parameters.setBARSelected(Boolean.valueOf(parameterMap.get(PARAM_IS_BAR_CHECKED)));
        parameters.setOccupancySelected(Boolean.valueOf(parameterMap.get(PARAM_IS_OCCUPANCY_FCST_CHECKED)));
        parameters.setLrvSelected(Boolean.valueOf(parameterMap.get(PARAM_IS_LRV_CHECKED)));
        parameters.setOOOSelected(Boolean.valueOf(parameterMap.get(PARAM_IS_OOO_CHECKED)));
        parameters.setOverbookingSelected(Boolean.valueOf(parameterMap.get(PARAM_IS_OVERBOOKING_CHECKED)));
        parameters.setRevPARSelected(Boolean.valueOf(parameterMap.get(PARAM_IS_REVPAR_CHECKED)));
        parameters.setRevenueSelected(Boolean.valueOf(parameterMap.get(PARAM_IS_REVENUE_CHECKED)));
        parameters.setSpecialEventSelected(Boolean.valueOf(parameterMap.get(PARAM_IS_SPECIAL_EVENT_CHECKED)));
        parameters.setProfitSelected(Boolean.valueOf(parameterMap.get(PARAM_IS_PROFIT_CHECKED)));
        parameters.setProPORSelected(Boolean.valueOf(parameterMap.get(PARAM_IS_PROPOR_CHECKED)));
        parameters.setProPARSelected(Boolean.valueOf(parameterMap.get(PARAM_IS_PROPAR_CHECKED)));
        String productsParam = parameterMap.get(PARAM_PRODUCTS);
        if (parameters.isBARSelected() && null == productsParam) {
            productsParam = BAR_PRODUCT_ID;
        }
        parameters.setSelectedProducts(getProductsById(getApplicableProducts(isChangeReport()
                && PARAM_ROOM_TYPE.equalsIgnoreCase(parameterMap.get(PARAM_DATA_SELECTION_TYPE))), productsParam));
        return parameters;
    }

    private List<AgileProductSelectionWrapper> getProductsById(List<Product> products, String productIdCommaSeparated) {
        List<String> productList = new ArrayList<>();
        if (null != productIdCommaSeparated) {
            productIdCommaSeparated = productIdCommaSeparated.replaceAll("\\[", "");
            productIdCommaSeparated = productIdCommaSeparated.replaceAll("\\]", "");
            productIdCommaSeparated = productIdCommaSeparated.replaceAll(" ", "");
            productList = Arrays.asList(productIdCommaSeparated.split(","));
        }
        List<AgileProductSelectionWrapper> productItems = view.createAgileProductSelectionWrapper(products);
        List<String> finalProductList = productList;
        productItems.forEach(productItem -> productItem.setShowPriceSelected(finalProductList.contains(String.valueOf(productItem.getProduct().getId()))));
        return productItems;
    }


    @Override
    protected JasperReportTypeEnum getJasperReportType() {
        DataLevelUIWrapper parent = totalHotel;
        List<DataLevelUIWrapper> selectedChildren = new ArrayList<>();
        if (null != filterDTO.getSelectedDataCriterion() && !filterDTO.getSelectedDataCriterion().isEmpty()) {
            Map.Entry<DataLevelUIWrapper, List<DataLevelUIWrapper>> firstElement = filterDTO.getSelectedDataCriterion().entrySet().iterator().next();
            parent = firstElement.getKey();
            selectedChildren = firstElement.getValue();
        }
        return findReportType(filterDTO.getDataSelectionType(), parent, selectedChildren, isAggregationSelected());
    }

    private DataLevelUIWrapper getSelectedDataParent() {
        DataLevelUIWrapper selectedData = this.totalHotel;
        if (null != filterDTO.getSelectedDataCriterion() && !filterDTO.getSelectedDataCriterion().isEmpty()) {
            Optional<DataLevelUIWrapper> selectedDateInFilter = filterDTO.getSelectedDataCriterion().entrySet().stream().map(entry -> entry.getKey()).findFirst();
            if (selectedDateInFilter.isPresent()) {
                selectedData = selectedDateInFilter.get();
            }
        }
        return selectedData;
    }

    private List<DataLevelUIWrapper> getSelectedDataChildren() {
        List<DataLevelUIWrapper> selectedData = new ArrayList<>();
        if (null != filterDTO.getSelectedDataCriterion() && !filterDTO.getSelectedDataCriterion().isEmpty()) {
            Optional<List<DataLevelUIWrapper>> selectedChildren = filterDTO.getSelectedDataCriterion().entrySet().stream().map(entry -> entry.getValue()).findFirst();
            if (selectedChildren.isPresent()) {
                selectedData = selectedChildren.get();
            }
        }
        return selectedData;
    }


    private JasperReportTypeEnum findReportType(String selectionType, DataLevelUIWrapper parent, List<DataLevelUIWrapper> selectedChildren, Boolean aggregationSelected) {
        Boolean changeReport = isChangeReport();
        Boolean pickUpReport = isPickUpReport();
        Boolean cpEnable = isCPEnable();
        Boolean barSelected = isBARSelected();
        JasperReportTypeEnum reportType = JasperReportTypeEnum.PICKUP_REPORT_PROPERTY;
        switch (selectionType) {
            case PARAM_ISHOTELCHECKED:
                reportType = getJasperReportTypeForPropertyLevel(changeReport, cpEnable, barSelected);
                break;
            case PARAM_BUSINESS_TYPES:
                reportType = getJasperReportTypeForBusinessTypes(pickUpReport, selectedChildren, parent.isSelected());
                break;
            case PARAM_ROOM_CLASSES:
                reportType = getJasperReportTypeForRoomClass(changeReport, aggregationSelected);
                break;
            case PARAM_MARKET_SEGMENTS:
                reportType = getJasperReportTypeForMarketSegments(pickUpReport, aggregationSelected);
                break;
            case PARAM_FORECAST_GROUPS:
                reportType = getJasperReportTypeForForecastGroup(pickUpReport, aggregationSelected);
                break;
            case PARAM_BUSINESS_VIEWS:
                reportType = getJasperReportTypeForBusinessViews(pickUpReport, aggregationSelected);
                break;
            case PARAM_ROOM_TYPES:
                reportType = getJasperReportTypeForRoomType(pickUpReport, cpEnable, barSelected, aggregationSelected);
                break;
            default:
                reportType = JasperReportTypeEnum.PICKUP_REPORT_PROPERTY;
        }
        return reportType;
    }

    private JasperReportTypeEnum getJasperReportTypeForRoomClass(Boolean changeReport, Boolean aggregationSelected) {
        JasperReportTypeEnum reportType;
        boolean isDynamicSelectionCriteriaForPickupChangeReport = isDynamicSelectionOfCriteriaEnabledForPickupChangeReport();
        JasperReportTypeEnum isMultiRoomClassReport = isDynamicSelectionCriteriaForPickupChangeReport ? JasperReportTypeEnum.DIFFERENTIAL_REPORT_ROOM_CLASS_SIDE_BY_SIDE : JasperReportTypeEnum.DIFFERENTIAL_REPORT_ROOM_CLASS;
        JasperReportTypeEnum isAggregatedReport = aggregationSelected ? JasperReportTypeEnum.PICKUP_REPORT_ROOM_CLASS : JasperReportTypeEnum.PICKUP_REPORT_ROOM_CLASS_SIDE_BY_SIDE;
        reportType = Boolean.TRUE.equals(changeReport) ? isMultiRoomClassReport : isAggregatedReport;
        return reportType;
    }

    private JasperReportTypeEnum getJasperReportTypeForMarketSegments(Boolean pickUpReport, Boolean aggregationSelected) {
        JasperReportTypeEnum reportType;
        if (pickUpReport) {
            reportType = aggregationSelected ? JasperReportTypeEnum.PICKUP_REPORT_MARKET_SEGMENTS : getPickupReportMarketSegmentsSideBySide();
        } else {
            reportType = aggregationSelected ? JasperReportTypeEnum.DIFFERENTIAL_REPORT_MARKET_SEGMENTS : JasperReportTypeEnum.DIFFERENTIAL_REPORT_MARKET_SEGMENTS_SIDE_BY_SIDE;
        }
        return reportType;
    }

    protected JasperReportTypeEnum getPickupReportMarketSegmentsSideBySide() {
        return JasperReportTypeEnum.PICKUP_REPORT_MARKET_SEGMENTS_SIDE_BY_SIDE;
    }

    private boolean isPickUpReportIncreaseMSLimitEnabled() {
        return configParamsService.getBooleanParameterValue(PreProductionConfigParamName.PICK_UP_MS_REPORT_INCREASE_MS_LIMIT);
    }

    private JasperReportTypeEnum getJasperReportTypeForForecastGroup(Boolean pickUpReport, Boolean aggregationSelected) {
        JasperReportTypeEnum reportType;
        if (pickUpReport) {
            reportType = aggregationSelected ? JasperReportTypeEnum.PICKUP_REPORT_FORECAST_GROUPS : JasperReportTypeEnum.PICKUP_REPORT_FORECAST_GROUPS_SIDE_BY_SIDE;
        } else {
            reportType = aggregationSelected ? JasperReportTypeEnum.DIFFERENTIAL_REPORT_FORECAST_GROUPS : JasperReportTypeEnum.DIFFERENTIAL_REPORT_FORECAST_GROUPS_SIDE_BY_SIDE;
        }
        return reportType;
    }

    private JasperReportTypeEnum getJasperReportTypeForBusinessViews(Boolean pickUpReport, Boolean aggregationSelected) {
        JasperReportTypeEnum reportType;
        if (pickUpReport) {
            reportType = aggregationSelected ? JasperReportTypeEnum.PICKUP_REPORT_BUSINESS_VIEWS : JasperReportTypeEnum.PICKUP_REPORT_BUSINESS_VIEWS_SIDE_BY_SIDE;
        } else {
            reportType = aggregationSelected ? JasperReportTypeEnum.DIFFERENTIAL_REPORT_BUSINESS_VIEWS : JasperReportTypeEnum.DIFFERENTIAL_REPORT_BUSINESS_VIEWS_SIDE_BY_SIDE;
        }
        return reportType;
    }

    private JasperReportTypeEnum getJasperReportTypeForRoomType(Boolean pickUpReport, boolean cpEnable, Boolean barSelected, Boolean aggregationSelected) {
        JasperReportTypeEnum reportType;
        if (pickUpReport) {
            reportType = aggregationSelected ? JasperReportTypeEnum.PICKUP_REPORT_ROOM_TYPE : JasperReportTypeEnum.PICKUP_REPORT_ROOM_TYPE_SIDE_BY_SIDE;
        } else {
            reportType = aggregationSelected ? JasperReportTypeEnum.DIFFERENTIAL_REPORT_ROOM_TYPE : JasperReportTypeEnum.DIFFERENTIAL_REPORT_ROOM_TYPE_SIDE_BY_SIDE;
            if (cpEnable && barSelected && !aggregationSelected) {
                reportType = JasperReportTypeEnum.DIFFERENTIAL_REPORT_ROOM_TYPE_CP;
            }
        }
        return reportType;
    }

    protected JasperReportTypeEnum getJasperReportTypeForBusinessTypes(Boolean pickUpReport, List<DataLevelUIWrapper> selectedID, boolean parentSelected) {
        if (parentSelected) {
            return pickUpReport ? JasperReportTypeEnum.PICKUP_REPORT_BUSINESS_TYPE_SIDE_BY_SIDE : JasperReportTypeEnum.DIFFERENTIAL_REPORT_BUSINESS_TYPE_SIDE_BY_SIDE;
        } else if (!selectedID.isEmpty()) {

            Integer id = (Integer) selectedID.get(0).getId();
            if (Objects.equals(id, TRANSIENT_ID)) {
                return pickUpReport ? JasperReportTypeEnum.PICKUP_REPORT_BUSINESS_TYPE_TRANSIENT : JasperReportTypeEnum.DIFFERENTIAL_REPORT_BUSINESS_TYPE_TRANSIENT;
            } else if (Objects.equals(id, GROUP_ID)) {
                return pickUpReport ? JasperReportTypeEnum.PICKUP_REPORT_BUSINESS_TYPE_GROUP : JasperReportTypeEnum.DIFFERENTIAL_REPORT_BUSINESS_TYPE_GROUP;
            }
        }
        return pickUpReport ? JasperReportTypeEnum.PICKUP_REPORT_BUSINESS_TYPE_SIDE_BY_SIDE : JasperReportTypeEnum.DIFFERENTIAL_REPORT_BUSINESS_TYPE_SIDE_BY_SIDE;
    }

    private JasperReportTypeEnum getJasperReportTypeForPropertyLevel(Boolean changeReport, boolean cpEnable, Boolean barSelected) {
        JasperReportTypeEnum reportType;
        if (changeReport) {
            if (cpEnable && barSelected) {
                reportType = JasperReportTypeEnum.DIFFERENTIAL_REPORT_PROPERTY_CP;
            } else {
                reportType = JasperReportTypeEnum.DIFFERENTIAL_REPORT_PROPERTY;
            }
        } else {
            reportType = JasperReportTypeEnum.PICKUP_REPORT_PROPERTY;
        }
        return reportType;
    }


    @Override
    protected boolean sharedSessionStateDataExist() {
        return null != sharedSessionState.getStoredPickUpAndChangeFilterDTO() && uiContext.getPropertyId().equals(sharedSessionState.getStoredPickUpAndChangeFilterDTO().getPropertyID());
    }

    private List<DataLevelUIWrapper> generateDataSelections(boolean includeDiscontinuedRTs, boolean includeDisconMS) {
        List<DataLevelUIWrapper> dataLevelUIWrappers = new ArrayList<>();
        totalHotel = new DataLevelUIWrapper(new DataLevel(PARENT_SELECTION_ID, getTextFromResourceBundle(COMMON_TOTAL_HOTEL), PARAM_ISHOTELCHECKED));

        dataLevelUIWrappers.add(totalHotel);
        addDataSelectionForBusinessTypes(dataLevelUIWrappers);
        addDataSelectionForRoomClasses(dataLevelUIWrappers);
        addDataSelectionForRoomTypes(dataLevelUIWrappers, includeDiscontinuedRTs);
        addDataSelectionForForecastGroup(dataLevelUIWrappers);
        addDataSelectionForBusinessViews(dataLevelUIWrappers);
        addDataSelectionForMarketSegments(dataLevelUIWrappers, includeDisconMS);

        return dataLevelUIWrappers;
    }

    protected String getTextFromResourceBundle(String key) {
        return UiUtils.getText(key);
    }

    private void addDataSelectionForForecastGroup(List<DataLevelUIWrapper> dataLevelUIWrappers) {
        DataLevelUIWrapper forecastGroup = new DataLevelUIWrapper(new DataLevel(PARENT_SELECTION_ID, getTextFromResourceBundle(FORECASTGROUPS), PARAM_FORECAST_GROUPS));
        dataLevelUIWrappers.add(forecastGroup);
        addChildElementsForForecastGroup(dataLevelUIWrappers, forecastGroup);
    }

    private void addChildElementsForForecastGroup(List<DataLevelUIWrapper> dataLevelUIWrappers, DataLevelUIWrapper forecastGroup) {
        List<DataLevelUIWrapper> childrenOfFG = getForecastGroupListByProperty(forecastGroup, forecastGroupFinalService.getAllForecastGroupSummaries());
        dataLevelUIWrappers.addAll(childrenOfFG);
    }

    private void addDataSelectionForBusinessTypes(List<DataLevelUIWrapper> dataLevelUIWrappers) {
        DataLevelUIWrapper businessTypes = new DataLevelUIWrapper(new DataLevel(PARENT_SELECTION_ID, getTextFromResourceBundle(BUSINESS_TYPES), PARAM_BUSINESS_TYPES));
        dataLevelUIWrappers.add(businessTypes);
        addChildElementsForBusinessTypes(dataLevelUIWrappers, businessTypes);
    }

    private void addChildElementsForBusinessTypes(List<DataLevelUIWrapper> dataLevelUIWrappers, DataLevelUIWrapper businessTypes) {
        List<DataLevelUIWrapper> childrenOfBT = getBusinessTypesByProperty(businessTypes);
        dataLevelUIWrappers.addAll(childrenOfBT);
    }

    private void addDataSelectionForRoomClasses(List<DataLevelUIWrapper> dataLevelUIWrappers) {
        DataLevelUIWrapper roomClasses = new DataLevelUIWrapper(new DataLevel(PARENT_SELECTION_ID, getTextFromResourceBundle(ROOM_CLASS), PARAM_ROOM_CLASSES));
        dataLevelUIWrappers.add(roomClasses);
        addChildElementsForRoomClasses(dataLevelUIWrappers, roomClasses);
    }

    private void addChildElementsForRoomClasses(List<DataLevelUIWrapper> dataLevelUIWrappers, DataLevelUIWrapper roomclasses) {
        List<DataLevelUIWrapper> childrenOfRC = getRoomClassesListByProperty(roomclasses, getAccomClassByProperty());
        dataLevelUIWrappers.addAll(childrenOfRC);
    }

    private void addDataSelectionForRoomTypes(List<DataLevelUIWrapper> dataLevelUIWrappers, boolean includeDiscontinuedRTs) {
        DataLevelUIWrapper roomTypes = new DataLevelUIWrapper(new DataLevel(PARENT_SELECTION_ID, getTextFromResourceBundle(ROOM_TYPE), PARAM_ROOM_TYPES));
        dataLevelUIWrappers.add(roomTypes);
        addChildElementsForRoomTypes(dataLevelUIWrappers, roomTypes, includeDiscontinuedRTs);
    }

    private void addChildElementsForRoomTypes(List<DataLevelUIWrapper> dataLevelUIWrappers, DataLevelUIWrapper roomTypes, boolean includeDiscontinuedRTs) {
        List<DataLevelUIWrapper> childrenOfRT = getRoomTypesListByProperty(roomTypes, getAccomClassByProperty(), includeDiscontinuedRTs);
        dataLevelUIWrappers.addAll(childrenOfRT);
    }

    private void addDataSelectionForMarketSegments(List<DataLevelUIWrapper> dataLevelUIWrappers, Boolean includeDiscontinuedMS) {
        DataLevelUIWrapper marketSegments = new DataLevelUIWrapper(new DataLevel(PARENT_SELECTION_ID, getTextFromResourceBundle(MARKET_SEGMENTS), PARAM_MARKET_SEGMENTS));
        dataLevelUIWrappers.add(marketSegments);
        addChildElementsForMarketSegments(dataLevelUIWrappers, marketSegments, includeDiscontinuedMS);
    }

    private void addChildElementsForMarketSegments(List<DataLevelUIWrapper> dataLevelUIWrappers, DataLevelUIWrapper marketSegments, Boolean includeDiscontinuedMS) {
        List<DataLevelUIWrapper> childrenOfMS = getMarketSegmentListByProperty(marketSegments, service.getMktSegByPropertyId(), includeDiscontinuedMS);
        dataLevelUIWrappers.addAll(childrenOfMS);
    }

    private void addDataSelectionForBusinessViews(List<DataLevelUIWrapper> dataLevelUIWrappers) {
        DataLevelUIWrapper businessViews = new DataLevelUIWrapper(new DataLevel(PARENT_SELECTION_ID, getTextFromResourceBundle(BUSINESS_VIEWS), PARAM_BUSINESS_VIEWS));
        dataLevelUIWrappers.add(businessViews);
        addChildElementsForBusinessViews(dataLevelUIWrappers, businessViews);
    }

    private void addChildElementsForBusinessViews(List<DataLevelUIWrapper> dataLevelUIWrappers, DataLevelUIWrapper businessViews) {
        List<DataLevelUIWrapper> childrenOfBV = getBusinessViewsByProperty(businessViews, businessGroupService.getAllBusinessGroupDetails());
        dataLevelUIWrappers.addAll(childrenOfBV);
    }

    private List<DataLevelUIWrapper> getBusinessViewsByProperty(DataLevelUIWrapper parent, List<BusinessGroup> businessGroups) {
        List<DataLevelUIWrapper> children = new ArrayList<>();
        for (BusinessGroup businessGroup : businessGroups) {
            DataLevelUIWrapper child = new DataLevelUIWrapper(new DataLevel(businessGroup.getId(), businessGroup.getName(), BV_PREFIX));
            child.setParent(parent);
            children.add(child);
        }
        parent.setChildren(children);
        return children;
    }

    @VisibleForTesting
    List<DataLevelUIWrapper> getMarketSegmentListByProperty(DataLevelUIWrapper parent, List<MktSeg> mktSegByPropertyId, Boolean includeDiscontinuedMS) {
        List<DataLevelUIWrapper> children = new ArrayList<>();
        for (MktSeg marketSegment : mktSegByPropertyId) {
            if (isDiscontinuedMSEnabled() && !includeDiscontinuedMS) {
                if (marketSegment.getStatusId().equals(1)) {
                    DataLevelUIWrapper child = new DataLevelUIWrapper(new DataLevel(marketSegment.getId(), marketSegment.getName(), MS_PREFIX));
                    child.setParent(parent);
                    children.add(child);
                }
            } else {
                DataLevelUIWrapper child = new DataLevelUIWrapper(new DataLevel(marketSegment.getId(), marketSegment.getName(), MS_PREFIX));
                child.setParent(parent);
                children.add(child);
            }
        }
        parent.setChildren(children);
        return children;
    }

    private List<DataLevelUIWrapper> getRoomTypesListByProperty(DataLevelUIWrapper parent, List<AccomClassSummary> accomClassByProperty, boolean includeDiscontinuedRTs) {
        List<Integer> accomClassList = accomClassByProperty.stream().map(AccomClassSummary::getId).collect(Collectors.toList());
        Map<Integer, List<AccomType>> accomTypesByAccomClass = getAccomTypesGroupedByAccomClasses(accomClassList, includeDiscontinuedRTs);
        List<DataLevelUIWrapper> children = new ArrayList<>();
        for (Map.Entry<Integer, List<AccomType>> entry : accomTypesByAccomClass.entrySet()) {
            for (AccomType accomType : entry.getValue()) {
                DataLevelUIWrapper child = new DataLevelUIWrapper(new DataLevel(accomType.getId(), accomType.getName(), RT_PREFIX));
                child.setParent(parent);
                children.add(child);
            }
        }
        parent.setChildren(children);
        return children;
    }

    private Map<Integer, List<AccomType>> getAccomTypesGroupedByAccomClasses(List<Integer> accomClassList, boolean includeDiscontinuedRTs) {
        if (!includeDiscontinuedRTs && isRoomTypeRecodingUIEnabled()) {
            return accommodationServiceLocal.getDisplayableAccomTypesByAccomClass(accomClassList, Constants.ACTIVE_DISPLAY_STATUS_ID);
        } else {
            return accommodationServiceLocal.getAccomTypesByAccomClass(accomClassList);
        }
    }

    private List<DataLevelUIWrapper> getBusinessTypesByProperty(DataLevelUIWrapper parent) {
        List<DataLevelUIWrapper> children = new ArrayList<>();
        DataLevelUIWrapper totalTransient = new DataLevelUIWrapper(new DataLevel(2, getTextFromResourceBundle(TOTAL_TRANSIENT), BT_PREFIX));
        children.add(totalTransient);
        totalTransient.setParent(parent);
        DataLevelUIWrapper group = new DataLevelUIWrapper(new DataLevel(1, getTextFromResourceBundle(TOTAL_GROUP), BT_PREFIX));
        children.add(group);
        group.setParent(parent);
        parent.setChildren(children);
        return children;
    }

    private List<DataLevelUIWrapper> getForecastGroupListByProperty(DataLevelUIWrapper parent, List<ForecastGroupSummary> childrenLists) {
        List<DataLevelUIWrapper> children = new ArrayList<>();
        for (ForecastGroupSummary forecastGroupSummary : childrenLists) {
            DataLevelUIWrapper child = new DataLevelUIWrapper(new DataLevel(forecastGroupSummary.getId(), forecastGroupSummary.getName(), FG_PREFIX));
            child.setParent(parent);
            children.add(child);
        }
        parent.setChildren(children);
        return children;
    }

    private List<DataLevelUIWrapper> getRoomClassesListByProperty(DataLevelUIWrapper parent, List<AccomClassSummary> childrenLists) {
        List<DataLevelUIWrapper> childrends = new ArrayList<>();
        for (AccomClassSummary accomClassSummary : childrenLists) {
            if (accomClassSummary.isAccomTypesAssigned()) {
                DataLevelUIWrapper child = new DataLevelUIWrapper(new DataLevel(accomClassSummary.getId(), accomClassSummary.getName(), RC_PREFIX));
                child.setParent(parent);
                childrends.add(child);
            }
        }
        parent.setChildren(childrends);
        return childrends;
    }


    public Boolean isPickUpReport() {
        return PICK_UP_REPORT_ID.equals(filterDTO.getReportType().getId());
    }

    public Boolean isChangeReport() {
        return CHANGE_REPORT_ID.equals(filterDTO.getReportType().getId());
    }

    public void notifyDateSelectorChange(PickUpAndChangeFilterDTO filterDTO, JavaDateSelectorBean selectedBean) {
        if (selectedBean.isGivenDateSelected(getTextFromResourceBundle(ACTIVITY_START_DATE))) {
            setAnalysisDatesOnChangeOfActivityDate(filterDTO, selectedBean);
        } else {
            setActivityDatesOnChangeOfAnalysisDate(filterDTO, selectedBean);
        }
        view.dateSelectorReset(filterDTO);
    }

    protected void setActivityDatesOnChangeOfAnalysisDate(PickUpAndChangeFilterDTO filterDTO, JavaDateSelectorBean analysisDateBean) {
        JavaDateSelectorBean activityDate = filterDTO.getActivityDateSelectorBean();
        if (analysisDateBean.isRollingDate()) {
            changeActivityDatesWhenAnalysisDateIsRolling(filterDTO, analysisDateBean, activityDate);
        } else if (!analysisDateBean.isRollingDate() && activityDate.isRollingDate()) {
            changeActivityDatesWhenAnalysisDateIsNotRolling(analysisDateBean, activityDate);
        } else if (!analysisDateBean.isRollingDate() && !activityDate.isRollingDate()) {
            changeActivityDatesWhenBothDatesAreNotRolling(analysisDateBean, activityDate);
        }
    }

    private void changeActivityDatesWhenBothDatesAreNotRolling(JavaDateSelectorBean analysisDateBean, JavaDateSelectorBean activityDate) {
        if (uiContext.getSystemCaughtUpDateAsJavaLocalDate().isAfter(analysisDateBean.getSpecificStartDate())) {
            activityDate.setStartDate(formatDateFor(analysisDateBean.getSpecificStartDate().minusDays(7)));
            activityDate.setEndDate(formatDateFor(analysisDateBean.getSpecificStartDate().minusDays(1)));

            LocalDate startDateUpperLimit = getBusinessDate();
            LocalDate endDateUpperLimit = getBusinessDate();
            if (uiContext.getSystemCaughtUpDateAsJavaLocalDate().isAfter(analysisDateBean.getSpecificEndDate())) {
                startDateUpperLimit = analysisDateBean.getSpecificEndDate();
                endDateUpperLimit = analysisDateBean.getSpecificEndDate();
            }

            activityDate.setStartStaticDateRange(new JavaDateRange(null, startDateUpperLimit));
            activityDate.setEndStaticDateRange(new JavaDateRange(activityDate.getSpecificStartDate(), endDateUpperLimit));
        } else {
            setDefaultActivityDates(activityDate);
        }
    }

    private LocalDate getBusinessDate() {
        return uiContext.getSystemCaughtUpDateAsJavaLocalDate().minusDays(1);
    }

    private void changeActivityDatesWhenAnalysisDateIsNotRolling(JavaDateSelectorBean analysisBean, JavaDateSelectorBean activityBean) {
        setDefaultActivityDates(activityBean);
        activityBean.setEndStaticDateRange(new JavaDateRange(activityBean.getSpecificStartDate(), getBusinessDate()));
        activityBean.setRollingDate(analysisBean.isRollingDate());
    }

    private void changeActivityDatesWhenAnalysisDateIsRolling(PickUpAndChangeFilterDTO filterDTO, JavaDateSelectorBean analysisBean, JavaDateSelectorBean activityBean) {
        if (!activityBean.isRollingDate()) {
            int offset = 0;
            int startOffset = analysisBean.extractOffSetDaysFromRollingDate(analysisBean.getRollingStartDate());
            if (analysisBean.getRollingStartDate().indexOf(MINUS) > 0 && analysisBean.getStartDate().contains(getTextFromResourceBundle(SYSTEM_DATE))) {
                offset = startOffset + 1;
            }
            if (offset > 0) {
                activityBean.setRollingDateString(filterDTO.getActivityDateSelectorBean().getRollingDateOptionMap().entrySet().iterator().next().getValue().getCaption() + offset);
            } else {
                activityBean.setRollingDateString(getTextFromResourceBundle(LAST_UPDATED_DATE));
            }
        }
    }

    protected void setAnalysisDatesOnChangeOfActivityDate(PickUpAndChangeFilterDTO filterDTO, JavaDateSelectorBean activityBean) {
        JavaDateSelectorBean analysisBean = filterDTO.getAnalysisDateSelectorBean();
        if (activityBean.isRollingDate()) {
            if (!analysisBean.isRollingDate()) {
                analysisBean.setRollingDateString(getTextFromResourceBundle(SYSTEM_DATE));
            } else {
                if (!isRollingDateInFuture(analysisBean.getRollingStartDate())) {
                    int offset = 1;
                    int originalOffset = analysisBean.extractOffSetDaysFromRollingDate(analysisBean.getRollingStartDate());
                    int activityOffset = activityBean.extractOffSetDaysFromRollingDate(activityBean.getRollingStartDate());
                    offset = activityOffset < originalOffset ? activityOffset : originalOffset;
                    if (originalOffset > activityOffset && offset != 0) {
                        analysisBean.setRollingDateString(analysisBean.getStartRollingDateOptions().iterator().next().getCaption() + offset);
                    }
                }
            }
        } else if (analysisBean.isRollingDate()) {
            setDefaultAnalysisDates(analysisBean);
            analysisBean.setRollingDate(false);
        }
    }

    protected boolean isRollingDateInFuture(String startDateOfSourceField) {
        return !startDateOfSourceField.contains(MINUS);
    }

    public void setDefaultValues() {
        if (!sharedSessionStateDataExist() && pageLoadSuccessful) {
            JavaDateSelectorBean changeActivityDates = new JavaDateSelectorBean(uiContext.getSystemCaughtUpDateAsJavaLocalDate(), new RollingDatesWithLastUpdatedDate());
            filterDTO.setActivityDateSelectorBean(getActivityDateSelectorBean(changeActivityDates));

            if (isChangeReport()) {
                RollingDateOptionsChoice rollingDateOptions = new RollingDatesWithLastOptimization();
                changeActivityDates = new JavaDateSelectorBean(uiContext.getSystemCaughtUpDateAsJavaLocalDate(), rollingDateOptions);
                filterDTO.setActivityDateSelectorBean(getActivityDateSelectorBean(changeActivityDates));
            }
            setDefaultAnalysisDates(filterDTO.getAnalysisDateSelectorBean());
            resetWebRateCompetitors();
            view.dateSelectorReset(filterDTO);
        }
        pageLoadSuccessful = true;
        sharedSessionState.setStoredPickUpAndChangeFilterDTO(null);
    }

    protected void resetWebRateCompetitors() {
        filterDTO.setWebrateCompetitors(new HashSet<>());
        view.setWebRateCompetitorsData(getAllCompetitorsByAccomClass());
    }

    protected void resetWebRateCompetitorsForRoomClasses(List<Integer> roomClassesSelected) {
        filterDTO.setWebrateCompetitors(new HashSet<>());
        view.setWebRateCompetitorsData(getCompetitorsByAccomClass(roomClassesSelected));
    }

    private List<Integer> getSelectedCompetitors() {
        List<Integer> competitorIdList = new ArrayList<>();
        if (null != filterDTO.getWebrateCompetitors()) {
            for (WebrateCompetitors webrateCompetitors : filterDTO.getWebrateCompetitors()) {
                competitorIdList.add(webrateCompetitors.getId());
            }
        }

        for (int i = competitorIdList.size(); i < 15; i++) {
            competitorIdList.add(-1);
        }
        return competitorIdList;
    }

    public PickUpAndChangeFilterDTO getFilterDTO() {
        return filterDTO;
    }

    @Override
    public boolean inputsValidForScheduling() {
        if (super.inputsValidForScheduling()) {
            return validateActivityAndAnalysisDates() && isValid();
        }
        return false;
    }

    @Override
    public boolean inputsValidForSaving() {
        if (super.inputsValidForSaving()) {
            return validateActivityAndAnalysisDates() && isValid();
        }
        return false;
    }

    boolean validateActivityAndAnalysisDates() {
        LocalDate analysisStartDate = new RollingJavaDates(filterDTO.getAnalysisDateSelectorBean().getStartDate(), uiContext.getSystemCaughtUpDateAsJavaLocalDate()).convertToDate();
        LocalDate analysisEndDate = new RollingJavaDates(filterDTO.getAnalysisDateSelectorBean().getEndDate(), uiContext.getSystemCaughtUpDateAsJavaLocalDate()).convertToDate();
        if (null != analysisStartDate && null != analysisEndDate) {
            int daysBetween = DateUtil.getDaysBetween(analysisStartDate, analysisEndDate, true);
            if (daysBetween > DAYS_DIFFERENCE_FOR_SCHEDULING_LIGHT_LOAD_DATA) {
                view.showAlert(getText("analysisDateValidationErrorTitle", ""), getText("rollingDateRangeValidationMsg", "", DAYS_DIFFERENCE_FOR_SCHEDULING_LIGHT_LOAD_DATA, DAYS_DIFFERENCE_FOR_SCHEDULING_LIGHT_LOAD_DATA));
                return false;
            }
        }
        return true;
    }

    protected boolean validateActivityDateBeforeAnalysisEndDate() {
        LocalDate analysisEndDate = new RollingJavaDates(filterDTO.getAnalysisDateSelectorBean().getEndDate(), uiContext.getSystemCaughtUpDateAsJavaLocalDate()).convertToDate();
        JavaDateSelectorBean activityDates = filterDTO.getActivityDateSelectorBean();
        LocalDate activityStartDate = new RollingJavaDates(activityDates.getStartDate(), uiContext.getSystemCaughtUpDateAsJavaLocalDate()).convertToDate();
        LocalDate activityEndDate = new RollingJavaDates(activityDates.getEndDate(), uiContext.getSystemCaughtUpDateAsJavaLocalDate()).convertToDate();

        if (null != activityStartDate && null != activityEndDate && !validateActivityRollingDates(isPickUpReport(), analysisEndDate, activityStartDate, activityEndDate)) {
            view.showAlert(getText("analysisDateValidationErrorTitle", ""), getTextFromResourceBundle("activityEndDateShouldBeLessThanAnalysisEndDate"));
            return false;
        }
        return true;
    }

    protected boolean validateActivityRollingDates(Boolean isPickUp, LocalDate analysisEndDate, LocalDate activityStartDate, LocalDate activityEndDate) {
        if (isPickUp) {
            return activityEndDate.isBefore(analysisEndDate) || activityEndDate.equals(analysisEndDate);
        }
        return activityStartDate.isBefore(analysisEndDate) || activityStartDate.equals(analysisEndDate);
    }

    @Override
    protected boolean isRollingDateInFuture() {
        return isRollingDateInFuture(filterDTO.getAnalysisDateSelectorBean().getRollingEndDate());
    }

    @Override
    protected boolean isRollingDate() {
        return filterDTO.getAnalysisDateSelectorBean().isRollingDate();
    }

    @Override
    protected String generateErrorMessageWhenRollingEndDateIsInFuture() {
        return getText(ROLLING_DATE_IN_FUTURE_VALIDATION_MSG, getTextFromResourceBundle(ANALYSIS_END_DATE));
    }

    public Set<String> collectSelectedCompetitorIDsFrom(Map<String, String> paramMap) {
        return paramMap.entrySet().stream()
                .filter(entry -> null != entry.getKey() && entry.getKey().startsWith(COMP))
                .filter(entry -> !"-1".equals(entry.getValue()))
                .map(entry -> entry.getValue())
                .collect(Collectors.toSet());
    }

    @Override
    protected Map<String, String> includeAdditionalParameters(Map<String, String> reportParameters) {
        String[] stringArray = new String[]{"-1", "-1", "-1", "-1", "-1", "-1", "-1", "-1", "-1", "-1", "-1", "-1", "-1", "-1", "-1"};
        int i = 0;
        for (Integer id : getSelectedCompetitors()) {
            stringArray[i] = String.valueOf(id);
            i++;
        }
        reportParameters.put(COMP_LIST, "[" + String.join(",", stringArray) + "]");
        reportParameters.put(PARAM_SHOW_AGGREGATED, String.valueOf(isAggregationSelected()));
        DataLevelUIWrapper selectedDataUIWrapper = getSelectedDataParent();
        reportParameters.put(PARAM_DATA_SELECTION_TYPE, selectedDataUIWrapper.getName());
        if (PARAM_ROOM_CLASSES.equals(selectedDataUIWrapper.getType())) {
            reportParameters.put(PARAM_ROOM_CLASS, reportParameters.get(PARAM_ROOM_CLASSES));
        }
        for (int counter = 1; counter <= 15; counter++) {
            reportParameters.remove(COMP + counter);
        }

        return reportParameters;
    }

    public void enabledParametersFor(String dataSelectionType) {
        filterDTO.getParameters().enableRequiredParametersFor(dataSelectionType, isPickUpReport());
        view.enableParameters(dataSelectionType, filterDTO, isPickUpReport());
    }

    @Override
    public boolean isValid() {
        if (super.isValid()) {
            return validateDataSelection() && validateActivityDateBeforeAnalysisEndDate();
        }
        return false;
    }

    public boolean validateDataSelection() {
        Map<DataLevelUIWrapper, List<DataLevelUIWrapper>> selectedDataCriterion = filterDTO.getSelectedDataCriterion();
        if (null == selectedDataCriterion || selectedDataCriterion.size() == 0) {
            view.showAlert(getTextFromResourceBundle(ALERT), getTextFromResourceBundle(MUST_SELECT_AT_LEAST_ONE_FILTER));
            return false;
        }
        if (isChangeReport()) {
            if (PARAM_ROOM_CLASSES.equals(filterDTO.getDataSelectionType()) && getSelectedDataChildren().size() > MAXIMUM_ROOM_CLASS_SELECTION_ALLOWED_FOR_CHANGE_REPROT && !isDynamicSelectionOfCriteriaEnabledForPickupChangeReport()) {
                view.showAlert(getTextFromResourceBundle(ALERT), getText(MAXIMUM_ROOM_CLASSES_ALLOWED_FOR_CHANGE_REPORT, MAXIMUM_ROOM_CLASS_SELECTION_ALLOWED_FOR_CHANGE_REPROT));
                return false;
            }
            if (isAggregationSelected() && isBARSelected()) {
                view.showAlert(getTextFromResourceBundle(ALERT), getTextFromResourceBundle(BAR_CANNOT_BE_SELECTED_FOR_AGGREGATED_DATA_MESSAGE));
                return false;
            }
            if (isBARSelected() && (PARAM_ROOM_TYPES.equals(filterDTO.getDataSelectionType()) || PARAM_ISHOTELCHECKED.equals(filterDTO.getDataSelectionType())
                    || PARAM_ROOM_CLASSES.equals(filterDTO.getDataSelectionType())) && validateIsOnlyOneProductIsSelected()) {
                view.showAlert(getTextFromResourceBundle(ALERT), getTextFromResourceBundle(ONLY_ONE_PRODUCT_CHECKBOX_SHOULD_BE_SELECTED_ON_SELECTING_PARAMETER_PRODUCT_MESSAGE));
                return false;
            }
        }

        if (!isAggregationSelected()) {
            if (isDynamicSelectionOfCriteriaEnabledForPickupChangeReport()) {
                return true;
            }
            for (Map.Entry<DataLevelUIWrapper, List<DataLevelUIWrapper>> entry : selectedDataCriterion.entrySet()) {
                DataLevelUIWrapper parent = entry.getKey();
                List<DataLevelUIWrapper> children = entry.getValue();
                long selectedCount = children.stream().filter(item -> item.isSelected()).count();
                if (parent.isSelected()) {
                    selectedCount = children.size();
                }
                return validateForMaxItemSelection(selectedCount);
            }
        }
        return true;
    }

    private Boolean validateIsOnlyOneProductIsSelected() {
        return view.getSelectedProducts().size() != 1;
    }

    protected boolean validateForMaxItemSelection(long selectedCount) {
        HashMap<Integer, String> maxItemsNumberWithMessage = getMaxItemsForSideBySideView();
        Integer maxLimitNumber = maxItemsNumberWithMessage.keySet().iterator().next();
        if (selectedCount > maxLimitNumber) {
            view.showAlert(getTextFromResourceBundle(ALERT), maxItemsNumberWithMessage.get(maxLimitNumber));
            return false;
        }
        return true;
    }

    private HashMap<Integer, String> getMaxItemsForSideBySideView() {
        HashMap<Integer, String> maxItemNumberWithMessage = new HashMap<>();
        Boolean pickUpReport = isPickUpReport();
        Boolean aggregationSelected = isAggregationSelected();
        if (filterDTO.getDataSelectionType().equals(PARAM_MARKET_SEGMENTS)
                && pickUpReport
                && !aggregationSelected
                && isPickUpReportIncreaseMSLimitEnabled()) {
            maxItemNumberWithMessage.put(
                    MAX_ITEMS_FOR_SIDE_BY_SIDE_VIEW_FOR_INCREASE_MS_LIMIT_ENABLED,
                    getTextFromResourceBundle(MORE_THAN_44_ELEMENT_CANT_BE_SELECTED_UNDER_DATA_SELECTION_CRITERIA)
            );
            return maxItemNumberWithMessage;
        }
        maxItemNumberWithMessage.put(
                MAX_ITEMS_FOR_SIDE_BY_SIDE_VIEW,
                getTextFromResourceBundle(MORE_THAN_8_ELEMENT_CANT_BE_SELECTED_UNDER_DATA_SELECTION_CRITERIA)
        );
        return maxItemNumberWithMessage;
    }

    protected void setPageLoadSuccessful(boolean pageLoadSuccessful) {
        this.pageLoadSuccessful = pageLoadSuccessful;
    }


    protected String getDataSelectionTypeForEditFlow(Map<String, String> reportParamMap) {
        List<String> parameters = new ArrayList<>(reportParamMap.keySet());
        List<String> parametersMapped = new ArrayList<>(DataSelectionParameters.getParametersMap().keySet());
        parametersMapped.retainAll(parameters);
        if (parametersMapped.isEmpty()) {
            return PARAM_ISHOTELCHECKED;
        }
        return parametersMapped.get(0);
    }

    private void setSelectedUIWrappersInEditFlow(Map<String, String> scheduleParams) {
        String dataSelectionTypeForEditFlow = getDataSelectionTypeForEditFlow(scheduleParams);
        Map<DataLevelUIWrapper, List<DataLevelUIWrapper>> selectedCriterion = getSelectedDataLevelUIWrappers(dataSelectionTypeForEditFlow, scheduleParams);
        filterDTO.setSelectedDataCriterion(selectedCriterion);
        filterDTO.setDataSelectionType(dataSelectionTypeForEditFlow);
    }

    protected Map<DataLevelUIWrapper, List<DataLevelUIWrapper>> getSelectedDataLevelUIWrappers(String dataSelectionTypeForEditFlow, Map<String, String> scheduleParams) {
        Map<DataLevelUIWrapper, List<DataLevelUIWrapper>> selectedCriterion = new HashMap<>();
        Optional<DataLevelUIWrapper> matchedParent = filterDTO.getDataLevelUIWrappers().stream().filter(item -> dataSelectionTypeForEditFlow.equals(item.getType())).findFirst();
        if (matchedParent.isPresent()) {
            if (matchedParent.get().getChildren().isEmpty()) {
                matchedParent.get().setSelected(true);
                selectedCriterion.put(matchedParent.get(), new ArrayList<>());
            } else {
                String[] selectedIdArr = scheduleParams.get(dataSelectionTypeForEditFlow).split(",");
                List<String> selectedIds = Arrays.asList(selectedIdArr);
                List<DataLevelUIWrapper> subCategories = matchedParent.get().getChildren()
                        .stream()
                        .filter(item -> selectedIds.contains(String.valueOf(item.getId())))
                        .collect(Collectors.toList());

                subCategories.forEach(item -> item.setSelected(true));
                selectedCriterion.put(matchedParent.get(), subCategories);
            }
        }
        return selectedCriterion;
    }

    protected DataLevelUIWrapper setDataSelectionAsPropertyLevelOnReset() {
        DataLevelUIWrapper hotelLevel = filterDTO.getDataLevelUIWrappers().get(0);
        LinkedHashMap<DataLevelUIWrapper, List<DataLevelUIWrapper>> dataSelections = new LinkedHashMap<>();
        hotelLevel.setSelected(true);
        dataSelections.put(hotelLevel, hotelLevel.getChildren());
        filterDTO.setSelectedDataCriterion(dataSelections);
        filterDTO.setDataSelectionType(PARAM_ISHOTELCHECKED);
        return hotelLevel;
    }

    boolean showDiscontinuedRTsCheckbox() {
        return isRoomTypeRecodingUIEnabled() && !accommodationServiceLocal.getAllDiscontinuedRoomTypes().isEmpty();
    }

    private boolean isRoomTypeRecodingUIEnabled() {
        return configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.ROOM_TYPE_RECODING_UI_ENABLED.value());
    }

    boolean showDiscontinuedMSCheckbox() {
        return isDiscontinuedMSEnabled() && !service.getDiscontinuedMktSegs().isEmpty();
    }

    private boolean isDiscontinuedMSEnabled() {
        return configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.DISCONTINUED_MARKET_SEGMENTS_ENABLED);
    }

    public boolean isDynamicSelectionOfCriteriaEnabledForPickupChangeReportAndNoAggregationSelected() {
        return isDynamicSelectionOfCriteriaEnabledForPickupChangeReport() && !isAggregationSelected();
    }

    public boolean isDynamicSelectionOfCriteriaEnabledForPickupChangeReport() {
        return configParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_DYNAMIC_SELECTION_CRITERIA_FOR_PICKUP_CHANGE_REPORT);
    }

    private Boolean isAggregationSelected() {
        return filterDTO.getParameters().isAggregationSelected();
    }

    private Boolean isBARSelected() {
        return filterDTO.getParameters().isBARSelected();
    }

    public boolean dataSelectionParentDeselected(DataLevelUIWrapper parent) {
        Optional<DataLevelUIWrapper> equivalentParent = filterDTO.getDataLevelUIWrappers().stream().filter(w -> w.equals(parent)).findFirst();
        if (equivalentParent.isPresent()) {
            DataLevelUIWrapper eqParent = equivalentParent.get();
            if (eqParent.getChildren().isEmpty()) {
                eqParent.setSelected(false);
                return true;
            }
        }
        return false;
    }

    @Override
    public boolean isReportEligibleForAsyncJob() {
        int allowedMaxDaysDiff = getAllowedMaxDaysDiff();
        int differenceOfDays;
        final LocalDate systemCaughtUpDate = filterDTO.getAnalysisDateSelectorBean().getSystemCaughtUpDate();
        if (filterDTO.getAnalysisDateSelectorBean().isRollingDate()) {
            LocalDate startDate = new RollingJavaDates(filterDTO.getAnalysisDateSelectorBean().getStartDate(), systemCaughtUpDate).convertToDate();
            LocalDate endDate = new RollingJavaDates(filterDTO.getAnalysisDateSelectorBean().getEndDate(), filterDTO.getAnalysisDateSelectorBean().getSystemCaughtUpDate()).convertToDate();
            differenceOfDays = Math.abs(Long.valueOf(ChronoUnit.DAYS.between(startDate, endDate)).intValue());
        } else {
            differenceOfDays = filterDTO.getAnalysisDateSelectorBean().getDaysDifferenceBetStartDateEndDate().intValue();
        }
        return allowedMaxDaysDiff < differenceOfDays;
    }

    public List<com.ideas.tetris.pacman.services.product.Product> getApplicableProducts(boolean isChangeReportAndRoomTypeChecked) {
        List<Product> applicableProducts = agileRatesConfigurationService.getApplicableProductsForPickUpAndChangeReport();
        return isChangeReportAndRoomTypeChecked ? applicableProducts :
                applicableProducts.stream().filter(p -> p.getId() == 1).collect(Collectors.toList());
    }
}



