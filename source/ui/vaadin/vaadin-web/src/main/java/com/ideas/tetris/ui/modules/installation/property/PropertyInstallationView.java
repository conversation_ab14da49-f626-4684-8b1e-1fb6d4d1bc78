package com.ideas.tetris.ui.modules.installation.property;

import com.ideas.infra.tetris.security.domain.TetrisPermissionKey;
import com.ideas.tetris.pacman.common.constants.QuestionnaireStatus;
import com.ideas.tetris.platform.services.Stage;
import com.ideas.tetris.platform.services.daoandentities.entity.Client;
import com.ideas.tetris.platform.services.daoandentities.entity.Property;
import com.ideas.tetris.ui.common.TetrisUi;
import com.ideas.tetris.ui.common.VaadinConstants;
import com.ideas.tetris.ui.common.cdi.TetrisView;
import com.ideas.tetris.ui.common.component.InputParameterProvider;
import com.ideas.tetris.ui.common.component.TetrisBeanItemContainer;
import com.ideas.tetris.ui.common.component.TetrisLabel;
import com.ideas.tetris.ui.common.component.TetrisNotification;
import com.ideas.tetris.ui.common.component.TetrisViewStack;
import com.ideas.tetris.ui.common.component.button.TetrisActionButton;
import com.ideas.tetris.ui.common.component.button.TetrisButton;
import com.ideas.tetris.ui.common.component.button.TetrisImageButton;
import com.ideas.tetris.ui.common.component.button.TetrisLinkButton;
import com.ideas.tetris.ui.common.component.fontawesome.TetrisFontAwesome;
import com.ideas.tetris.ui.common.component.label.TetrisSpacer;
import com.ideas.tetris.ui.common.component.panel.PanelBar;
import com.ideas.tetris.ui.common.component.select.ItemCaptionGenerator;
import com.ideas.tetris.ui.common.component.select.TetrisComboBox;
import com.ideas.tetris.ui.common.component.select.TetrisEnumListSelect;
import com.ideas.tetris.ui.common.component.select.TetrisQuickFilterComboBox;
import com.ideas.tetris.ui.common.component.table.TetrisTable;
import com.ideas.tetris.ui.common.component.tabsheet.TetrisTabSheet;
import com.ideas.tetris.ui.common.component.textfield.TetrisTextArea;
import com.ideas.tetris.ui.common.component.window.TetrisWindow;
import com.ideas.tetris.ui.common.data.fieldgroup.TetrisBeanFieldGroup;
import com.ideas.tetris.ui.common.util.SimpleBeanQuery;
import com.ideas.tetris.ui.common.util.TetrisLazyQueryContainer;
import com.ideas.tetris.ui.common.util.UiUtils;
import com.ideas.tetris.ui.modules.installation.property.actions.*;
import com.ideas.tetris.ui.modules.installation.property.actions.view.GroupMasterBookingPopulationView;
import com.ideas.tetris.ui.modules.installation.property.configuration.EditPropertyDataView;
import com.ideas.tetris.ui.modules.installation.property.configuration.PropertyEditView;
import com.ideas.tetris.ui.modules.monitor.jobmonitorbeta.JobMapUtil;
import com.ideas.tetris.ui.modules.parameters.PropertyParametersView;
import com.vaadin.annotations.Title;
import com.vaadin.server.Page;
import com.vaadin.shared.ui.ContentMode;
import com.vaadin.ui.*;
import com.vaadin.v7.shared.ui.combobox.FilteringMode;
import com.vaadin.v7.ui.AbstractSelect;
import com.vaadin.v7.ui.HorizontalLayout;
import com.vaadin.v7.ui.Table;
import com.vaadin.v7.ui.Table.ColumnGenerator;
import com.vaadin.v7.ui.VerticalLayout;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.vaadin.hene.popupbutton.PopupButton;

import javax.inject.Inject;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@SuppressWarnings("serial")
@Title("Property")
public class PropertyInstallationView extends TetrisView<PropertyInstallationPresenter, Void> {
    private static final Logger LOGGER = Logger.getLogger(PropertyInstallationView.class.getName());
    private static final String RESOURCE_ACTION = "actions";
    private static final String RESOURCE_CLIENT_NAME = "common.clientName";
    private static final String RESOURCE_CLIENT_CODE = "common.clientCode";
    private static final String RESOURCE_PROP_CODE = "property.code";
    private static final String RESOURCE_PROP_NAME = "common.propertyName";
    private static final String RESOURCE_PROP_ID = "common.property.id";
    private static final String RESOURCE_PROP_ADD = "installation.property.add";
    private static final String RESOURCE_STAGE = "common.stage";
    private static final String RESOURCE_PROP_CODES = "installation.property.codes";
    private static final String RESOURCE_PROP_NAMES = "installation.property.names";
    private static final String RESOURCE_EXTERNAL_SYSTEM = "common.externalSystem";
    private static final String ROW_ACTION_COLUMN_ID = "ROW_ACTION_COLUMN_ID";
    private static final String EXTERNAL_SYSTEM_COLUMN_ID = "EXTERNAL_SYSTEM_COLUMN_ID";
    private static final String CLIENT_NAME_ID = "CLIENT_NAME_ID";
    private static final String PROPERTY_SFDC_ACCT_NO = "sfdcAccountNo";
    private static final String RESOURCE_SFDC_ACCT_NUMBER = "salesforce.accountNumber";
    private static final String DATAFEED_SFTP_TEST_SUCCESS_MESSAGE = "File successfully uploaded to remote host";
    private static final Integer SHOW_PROPERTIES_VIEW_INDEX = 0;
    private static final Integer SHOW_EDIT_PROPERTY_VIEW_INDEX = 1;
    private static final String RESOURCE_QUESTIONNAIRE_STATUS = "questionnaire.status";
    private static final String RESOURCE_LAST_UPDATED_ON = "common.lastUpdatedOn";
    private static final String RESOURCE_LAST_UPDATED_BY = "common.lastUpdatedBy";
    private static final String LAST_UPDATED_BY_USER = "LAST_UPDATED_BY_USER";
    private static final String LAST_UPDATED_ON = "LAST_UPDATED_ON";


    @Inject
    TetrisViewStack viewStack;
    @Inject
    AddPropertyView addPropertyView;
    @Inject
    CatchupExtractView catchupExtractView;
    @Inject
    RateSyncReplayView rateSyncReplayView;
    @Inject
    RollbackView rollbackView;
    @Inject
    EditPropertyDataView propertyStageConfigurationView;
    @Inject
    TetrisTabSheet editPropertyTabSheet;
    @Inject
    PropertyDeleteView propertyDeleteView;
    @Inject
    ReCreateTransDataView reCreateTransDataView;
    @Inject
    PropertyExportView propertyExportView;
    @Inject
    HistoricalDataUploadView historicalDataUploadView;
    @Inject
    HistoricalDataValidationView historicalDataValidationView;
    @Inject
    DeletePseudoRoomTypePopupView deletePseudoRoomTypePopupView;
    @Inject
    GroupMasterBookingPopulationView groupMasterBookingPopulationView;
    @Inject
    ImportOxiPackageConfigurationPopupView importOxiPackageConfigurationPopupView;
    @Inject
    CPRecommendedFloorCeilingUploadView cpRecommendedFloorCeilingUploadView;

    private TetrisBeanItemContainer<Property> propertyCodeBeanContainer;
    private TetrisBeanItemContainer<Property> propertyNameBeanContainer;

    private TetrisBeanFieldGroup<PropertyFilterDto> fieldGroup;
    private TetrisTable propertyTable;
    private TetrisLazyQueryContainer<Property> propertyContainer;
    private TetrisEnumListSelect stageEnumListSelect;
    private TetrisTextArea propertyCodesTextArea;
    private TetrisTextArea propertyNamesTextArea;
    private TetrisTextArea sfdcAccountNumbersTextArea;
    private TetrisTextArea externalSystemTextArea;
    private TetrisQuickFilterComboBox propertyNameComboBox;
    private TetrisQuickFilterComboBox propertyCodeComboBox;
    private TetrisButton exportButton;

    private boolean clientCodeChanged = false;
    private boolean clientNameChanged = false;
    private Property selectedProperty;

    @Override
    protected void initView() {
    }

    @Override
    public void onPresenterInit() {
        setSizeFull();
        viewStack.setSizeFull();
        viewStack.addComponent(getPropertyInstallationComponent());
        viewStack.addView(PropertyEditView.class);
        viewStack.setInputParameterProvider(new InputParameterProvider() {
            @Override
            public Object getInputParameter() {
                return selectedProperty;
            }
        });
        viewStack.setSelectedIndex(SHOW_PROPERTIES_VIEW_INDEX);
        setCompositionRoot(viewStack);
    }

    private VerticalLayout getPropertyInstallationComponent() {
        VerticalLayout rootLayout = new VerticalLayout();
        rootLayout.setSizeFull();
        rootLayout.setSpacing(true);
        rootLayout.setMargin(true);

        addSearchPanel(rootLayout);
        createPropertyTable();
        addButtons(rootLayout);
        rootLayout.addComponent(propertyTable);
        rootLayout.setExpandRatio(propertyTable, 1.0F);
        return rootLayout;
    }

    private void addSearchPanel(VerticalLayout rootLayout) {
        PanelBar searchPanelBar = new PanelBar(PanelBar.PanelBarPosition.HEADER);
        searchPanelBar.setContent(buildSearchFilter());
        searchPanelBar.setTitle("Search Properties");
        searchPanelBar.setOpen(true);
        rootLayout.addComponent(searchPanelBar);
    }

    private Component buildSearchFilter() {
        VerticalLayout verticalLayout = new VerticalLayout();
        verticalLayout.setMargin(true);

        fieldGroup = new TetrisBeanFieldGroup<>(PropertyFilterDto.class);

        HorizontalLayout criteriaLayoutFirstRow = createPropertySearchComponentFirstRow();
        HorizontalLayout criteriaLayoutSecondRow = createPropertySearchComponentSecondRow();
        verticalLayout.addComponent(criteriaLayoutFirstRow);
        verticalLayout.addComponent(criteriaLayoutSecondRow);
        HorizontalLayout buttonLayout = new HorizontalLayout();
        buttonLayout.setWidth(100, Unit.PERCENTAGE);
        buttonLayout.setSpacing(true);

        TetrisSpacer spacer = new TetrisSpacer();
        spacer.setWidth(100, Unit.PERCENTAGE);
        spacer.setHeight(5, Unit.PIXELS);
        buttonLayout.addComponent(spacer);
        buttonLayout.setExpandRatio(spacer, 1.0F);

        TetrisButton resetButton = new TetrisButton(getText("common.reset"), new Button.ClickListener() {
            @Override
            public void buttonClick(Button.ClickEvent clickEvent) {

                fieldGroup.setItemDataSource(presenter.resetPropertyFilterDto());
            }
        });
        buttonLayout.addComponent(resetButton);
        TetrisButton searchButton = new TetrisButton(getText("search"), new Button.ClickListener() {
            @Override
            public void buttonClick(Button.ClickEvent clickEvent) {
                presenter.search();
            }
        });
        searchButton.setIsPrimary(true);
        buttonLayout.addComponent(searchButton);
        verticalLayout.addComponent(buttonLayout);
        fieldGroup.setItemDataSource(presenter.getPropertyFilterDto());

        return verticalLayout;
    }

    private HorizontalLayout createPropertySearchComponentSecondRow() {
        HorizontalLayout criteriaLayoutSecondRow = new HorizontalLayout();
        criteriaLayoutSecondRow.setSpacing(true);
        criteriaLayoutSecondRow.setMargin(true);
        addStageList(criteriaLayoutSecondRow);
        addPropertyCodesTextArea(criteriaLayoutSecondRow);
        addSFDCAccountNumbersTextArea(criteriaLayoutSecondRow);
        addPropertyNamesTextArea(criteriaLayoutSecondRow);
        addExternalSystemTextArea(criteriaLayoutSecondRow);
        return criteriaLayoutSecondRow;
    }

    private HorizontalLayout createPropertySearchComponentFirstRow() {
        HorizontalLayout criteriaLayoutFirstRow = new HorizontalLayout();
        criteriaLayoutFirstRow.setSpacing(true);
        criteriaLayoutFirstRow.setMargin(true);
        addClientComboBoxes(criteriaLayoutFirstRow);
        addPropertyComboBoxes(criteriaLayoutFirstRow);
        addQuestionnaireStatusCombobox(criteriaLayoutFirstRow);
        return criteriaLayoutFirstRow;
    }

    private void addQuestionnaireStatusCombobox(HorizontalLayout horizontalLayout) {
        TetrisComboBox questionnaireStatusComboBox = new TetrisComboBox(getText(RESOURCE_QUESTIONNAIRE_STATUS), presenter.getQuestionnaireStatusOptions());
        questionnaireStatusComboBox.setItemCaptionGenerator((source1, itemId) -> {
            final String status = String.valueOf(itemId);
            return status.equals(UiUtils.ALL_TEXT) ? UiUtils.ALL_TEXT
                    : getText(QuestionnaireStatus.valueOf(status).getLocalizationKey());

        });
        questionnaireStatusComboBox.setRequired(false);
        questionnaireStatusComboBox.setNullSelectionAllowed(false);
        fieldGroup.bind(questionnaireStatusComboBox, "questionnaireStatus");
        horizontalLayout.addComponent(questionnaireStatusComboBox);
    }

    private void addClientComboBoxes(HorizontalLayout horizontalLayout) {

        TetrisBeanItemContainer<Client> clientNameContainer = new TetrisBeanItemContainer<>(Client.class);
        TetrisBeanItemContainer<Client> clientCodeContainer = new TetrisBeanItemContainer<>(Client.class);

        List<Client> clients = presenter.getClients();

        clientNameContainer.addAll(clients);
        clientCodeContainer.addAll(clients);

        final TetrisComboBox clientNameField = new TetrisComboBox(getText(RESOURCE_CLIENT_NAME));
        final TetrisComboBox clientCodeField = new TetrisComboBox(getText(RESOURCE_CLIENT_CODE));

        clientNameField.setItemCaptionPropertyId("name");
        clientNameField.setContainerDataSource(clientNameContainer);
        clientNameField.setRequired(false);
        clientNameField.setNullSelectionAllowed(false);
        clientNameField.setFilteringMode(FilteringMode.CONTAINS);
        clientNameField.addValueChangeListener(new com.vaadin.v7.data.Property.ValueChangeListener() {
            @Override
            public void valueChange(com.vaadin.v7.data.Property.ValueChangeEvent event) {

                if (!clientCodeChanged) {
                    clientNameChanged = true;
                    Client value = (Client) clientNameField.getValue();
                    clientCodeField.select(value);
                    changePropertyComboBoxes(value);
                    clientNameChanged = false;
                }
            }
        });

        clientCodeField.setItemCaptionPropertyId("code");
        clientCodeField.setContainerDataSource(clientCodeContainer);
        clientCodeField.setRequired(false);
        clientCodeField.setNullSelectionAllowed(false);
        clientCodeField.addValueChangeListener(new com.vaadin.v7.data.Property.ValueChangeListener() {
            @Override
            public void valueChange(com.vaadin.v7.data.Property.ValueChangeEvent event) {
                if (!clientNameChanged) {
                    clientCodeChanged = true;
                    Client value = (Client) clientCodeField.getValue();
                    clientNameField.select(value);
                    changePropertyComboBoxes(value);
                    clientCodeChanged = false;
                }

            }
        });

        fieldGroup.bind(clientNameField, "clientByName");
        fieldGroup.bind(clientCodeField, "clientByCode");

        horizontalLayout.addComponent(clientNameField);
        horizontalLayout.addComponent(clientCodeField);
    }

    private void addPropertyComboBoxes(HorizontalLayout horizontalLayout) {
        propertyCodeBeanContainer = new TetrisBeanItemContainer<>(Property.class);
        propertyNameBeanContainer = new TetrisBeanItemContainer<>(Property.class);

        propertyCodeComboBox = new TetrisQuickFilterComboBox(getText(RESOURCE_PROP_CODE), propertyCodeBeanContainer);
        propertyNameComboBox = new TetrisQuickFilterComboBox(getText(RESOURCE_PROP_NAME), propertyNameBeanContainer);

        propertyCodeComboBox.setRequired(false);
        propertyCodeComboBox.setNullSelectionAllowed(false);
        propertyCodeComboBox.setItemCaptionPropertyId("code");
        propertyCodeComboBox.addValueChangeListener(new com.vaadin.v7.data.Property.ValueChangeListener() {
            @Override
            public void valueChange(com.vaadin.v7.data.Property.ValueChangeEvent valueChangeEvent) {
                Property selectedProperty = (Property) valueChangeEvent.getProperty().getValue();
                propertyCodesTextArea.setEnabled(PropertyInstallationPresenter.ALL_PROPERTIES.equals(selectedProperty));
                propertyNameComboBox.select(valueChangeEvent.getProperty().getValue());
                enableDisableStageSelect();
            }
        });
        fieldGroup.bind(propertyCodeComboBox, "propertyByCode");
        horizontalLayout.addComponent(propertyCodeComboBox);

        propertyNameComboBox.setRequired(false);
        propertyNameComboBox.setNullSelectionAllowed(false);
        propertyNameComboBox.setItemCaptionPropertyId("name");
        propertyNameComboBox.setWidth(300, Unit.PIXELS);
        propertyNameComboBox.setFilteringMode(FilteringMode.CONTAINS);
        propertyNameComboBox.addValueChangeListener(new com.vaadin.v7.data.Property.ValueChangeListener() {
            @Override
            public void valueChange(com.vaadin.v7.data.Property.ValueChangeEvent valueChangeEvent) {
                Property selectedProperty = (Property) valueChangeEvent.getProperty().getValue();
                propertyCodesTextArea.setEnabled(PropertyInstallationPresenter.ALL_PROPERTIES.equals(selectedProperty));
                propertyCodeComboBox.select(selectedProperty);
                enableDisableStageSelect();
            }
        });
        fieldGroup.bind(propertyNameComboBox, "propertyByName");
        horizontalLayout.addComponent(propertyNameComboBox);
    }

    private void addPropertyCodesTextArea(HorizontalLayout horizontalLayout) {
        propertyCodesTextArea = new TetrisTextArea(getText(RESOURCE_PROP_CODES));
        propertyCodesTextArea.setRows(6);
        propertyCodesTextArea.setWidth("150");
        propertyCodesTextArea.setRequired(false);
        propertyCodesTextArea.addValueChangeListener(new com.vaadin.v7.data.Property.ValueChangeListener() {
            @Override
            public void valueChange(com.vaadin.v7.data.Property.ValueChangeEvent valueChangeEvent) {
                String propertyCodes = (String) valueChangeEvent.getProperty().getValue();
                propertyNameComboBox.setEnabled(StringUtils.isBlank(propertyCodes));
                propertyCodeComboBox.setEnabled(StringUtils.isBlank(propertyCodes));
                enableDisableStageSelect();
            }
        });
        fieldGroup.bind(propertyCodesTextArea, "propertyCodes");
        horizontalLayout.addComponent(propertyCodesTextArea);

    }

    private void addSFDCAccountNumbersTextArea(HorizontalLayout horizontalLayout) {
        sfdcAccountNumbersTextArea = new TetrisTextArea(getText(RESOURCE_SFDC_ACCT_NUMBER));
        sfdcAccountNumbersTextArea.setRows(6);
        sfdcAccountNumbersTextArea.setWidth("150");
        sfdcAccountNumbersTextArea.setRequired(false);
        sfdcAccountNumbersTextArea.addValueChangeListener(new com.vaadin.v7.data.Property.ValueChangeListener() {
            @Override
            public void valueChange(com.vaadin.v7.data.Property.ValueChangeEvent valueChangeEvent) {
                String sfdcAccountNumbers = (String) valueChangeEvent.getProperty().getValue();
                propertyNameComboBox.setEnabled(StringUtils.isBlank(sfdcAccountNumbers));
                propertyCodeComboBox.setEnabled(StringUtils.isBlank(sfdcAccountNumbers));
                enableDisableStageSelect();
            }
        });
        fieldGroup.bind(sfdcAccountNumbersTextArea, "sfdcAccountNumbers");
        horizontalLayout.addComponent(sfdcAccountNumbersTextArea);

    }

    private void addPropertyNamesTextArea(HorizontalLayout horizontalLayout) {
        propertyNamesTextArea = new TetrisTextArea(getText(RESOURCE_PROP_NAMES));
        propertyNamesTextArea.setRows(6);
        propertyNamesTextArea.setWidth("150");
        propertyNamesTextArea.setRequired(false);
        propertyNamesTextArea.addValueChangeListener(new com.vaadin.v7.data.Property.ValueChangeListener() {
            @Override
            public void valueChange(com.vaadin.v7.data.Property.ValueChangeEvent valueChangeEvent) {
                String propertyNames = (String) valueChangeEvent.getProperty().getValue();
                propertyNameComboBox.setEnabled(StringUtils.isBlank(propertyNames));
                propertyCodeComboBox.setEnabled(StringUtils.isBlank(propertyNames));
                enableDisableStageSelect();
            }
        });
        fieldGroup.bind(propertyNamesTextArea, "propertyNames");
        horizontalLayout.addComponent(propertyNamesTextArea);
    }

    private void addExternalSystemTextArea(HorizontalLayout horizontalLayout) {
        externalSystemTextArea = new TetrisTextArea(getText(RESOURCE_EXTERNAL_SYSTEM));
        externalSystemTextArea.setRows(6);
        externalSystemTextArea.setWidth("150");
        externalSystemTextArea.setRequired(false);
        externalSystemTextArea.addValueChangeListener(new com.vaadin.v7.data.Property.ValueChangeListener() {
            @Override
            public void valueChange(com.vaadin.v7.data.Property.ValueChangeEvent valueChangeEvent) {
                String externalSystem = (String) valueChangeEvent.getProperty().getValue();
                propertyNameComboBox.setEnabled(StringUtils.isBlank(externalSystem));
                propertyCodeComboBox.setEnabled(StringUtils.isBlank(externalSystem));
                enableDisableStageSelect();
            }
        });
        fieldGroup.bind(externalSystemTextArea, "externalSystem");
        horizontalLayout.addComponent(externalSystemTextArea);
    }

    /**
     * The search critera for Property Codes, Property Names and SFDC Account No's are mutually exclusive.  If one has
     * a value, disable others.
     */
    private void enableDisableStageSelect() {
        String propertyCodes = propertyCodesTextArea.getValue();
        String sfdcAccountNumbers = sfdcAccountNumbersTextArea.getValue();
        String propertyNames = propertyNamesTextArea.getValue();
        String externalSystem = externalSystemTextArea.getValue();
        sfdcAccountNumbersTextArea.setEnabled(true);
        externalSystemTextArea.setEnabled(true);
        propertyCodesTextArea.setEnabled(true);
        propertyNamesTextArea.setEnabled(true);
        Property property = (Property) propertyNameComboBox.getValue();
        if (propertyCodes != null && propertyCodes.trim().length() > 0) {
            stageEnumListSelect.setEnabled(false);
            presenter.getPropertyFilterDto().setStages(null);
            disablePropertyNamesTextArea();
            disableSfdcAccountNumbersTextArea();
            disableExternalSystemTextArea();
        } else if (propertyNames != null && propertyNames.trim().length() > 0) {
            stageEnumListSelect.setEnabled(false);
            presenter.getPropertyFilterDto().setStages(null);
            disablePropertyCodesTextArea();
            disableSfdcAccountNumbersTextArea();
            disableExternalSystemTextArea();
        } else if (property != null && !property.equals(PropertyInstallationPresenter.ALL_PROPERTIES)) {
            stageEnumListSelect.setEnabled(false);
            presenter.getPropertyFilterDto().setStages(null);
        } else if (sfdcAccountNumbers != null && sfdcAccountNumbers.trim().length() > 0) {
            disablePropertyCodesTextArea();
            disablePropertyNamesTextArea();
            stageEnumListSelect.setEnabled(false);
            presenter.getPropertyFilterDto().setStages(null);
            disableExternalSystemTextArea();
        } else if (StringUtils.isNotBlank(externalSystem)) {
            disablePropertyCodesTextArea();
            disablePropertyNamesTextArea();
            stageEnumListSelect.setEnabled(false);
            presenter.getPropertyFilterDto().setStages(null);
            disableSfdcAccountNumbersTextArea();
        } else {
            stageEnumListSelect.setEnabled(true);
        }

    }

    private void disablePropertyCodesTextArea() {
        propertyCodesTextArea.setEnabled(false);
        propertyCodesTextArea.setValue(null);
    }

    private void disablePropertyNamesTextArea() {
        propertyNamesTextArea.setEnabled(false);
        propertyNamesTextArea.setValue(null);
    }

    private void disableExternalSystemTextArea() {
        externalSystemTextArea.setEnabled(false);
        externalSystemTextArea.setValue(null);
    }

    private void disableSfdcAccountNumbersTextArea() {
        sfdcAccountNumbersTextArea.setEnabled(false);
        sfdcAccountNumbersTextArea.setValue(null);
    }

    private void addStageList(HorizontalLayout horizontalLayout) {
        stageEnumListSelect = new TetrisEnumListSelect(getText(RESOURCE_STAGE), Stage.class, "order");
        stageEnumListSelect.setWidth(200, Unit.PIXELS);
        stageEnumListSelect.setRows(6);
        stageEnumListSelect.setMultiSelect(true);
        stageEnumListSelect.setRequired(false);
        stageEnumListSelect.setNullSelectionAllowed(true);
        stageEnumListSelect.setItemCaptionGenerator(new ItemCaptionGenerator() {
            @Override
            public String getItemCaption(AbstractSelect source, Object itemId) {
                return JobMapUtil.getPropertyStageLabel((Stage) itemId);
            }
        });
        fieldGroup.bind(stageEnumListSelect, "stages");
        horizontalLayout.addComponent(stageEnumListSelect);
    }

    private void addButtons(VerticalLayout rootLayout) {
        HorizontalLayout horizontalLayout = new HorizontalLayout();
        horizontalLayout.setWidth(100, Unit.PERCENTAGE);
        horizontalLayout.setSpacing(true);

        TetrisButton addPropetyButton = new TetrisButton(getText(RESOURCE_PROP_ADD), new Button.ClickListener() {
            @Override
            public void buttonClick(Button.ClickEvent clickEvent) {
                openAddPropertyWindow();
            }
        });
        addPropetyButton.setIsPrimary(true);

        horizontalLayout.addComponent(addPropetyButton);

        TetrisSpacer spacer = new TetrisSpacer();
        spacer.setWidth(100, Unit.PERCENTAGE);
        horizontalLayout.addComponent(spacer);
        horizontalLayout.setExpandRatio(spacer, 1.0F);

        exportButton = new TetrisImageButton(TetrisFontAwesome.EXCEL_ICON, "Export to Excel");

        exportButton.setEnabled(propertyContainer.sizeExcludingNoDataRow() > 0);
        exportButton.addClickListener(new Button.ClickListener() {
            @Override
            public void buttonClick(Button.ClickEvent clickEvent) {
                openExportWindow();
            }
        });
        horizontalLayout.addComponent(exportButton);
        rootLayout.addComponent(horizontalLayout);
    }

    private void openExportWindow() {
        TetrisWindow exportWindow = new TetrisWindow("Export Property Data");
        exportWindow.setContent(propertyExportView.openView());
        propertyExportView.setPropertyContainer(propertyContainer);
        UI.getCurrent().getUI().addWindow(exportWindow);
        exportWindow.center();
        exportWindow.focus();
        exportWindow.setModal(true);
        exportWindow.setResizable(false);
    }

    private void createPropertyTable() {
        propertyContainer = new TetrisLazyQueryContainer<>(Property.class, new SimpleBeanQuery<Property>() {
            @Override
            public int getBeansSize() {
                return presenter.getPropertyCount();
            }

            @Override
            public List<Property> loadBeans(int startIndex, int count, ArrayList<String> sortPropertyIds, boolean[] sortPropertyAscendingStates,
                                            ArrayList<String> sortablePropertyIds) {
                return presenter.getProperties(startIndex, count, sortPropertyIds, sortPropertyAscendingStates, sortablePropertyIds);
            }
        });

        propertyContainer.sort(new Object[]{"client", "code"}, new boolean[]{true,
                true});

        propertyTable = new TetrisTable("", propertyContainer) {
            @SuppressWarnings("rawtypes")
            @Override
            protected String formatPropertyValue(Object rowId, Object colId, com.vaadin.v7.data.Property property) {
                if ("stage".equals(colId)) {
                    Stage value = (Stage) property.getValue();
                    if (value != null) {
                        return JobMapUtil.getPropertyStageLabel(value);
                    }
                } else if ("client".equals(colId)) {
                    return ((Client) property.getValue()).getCode();
                } else if ("questionnaireStatus".equals(colId)) {
                    return getText(((QuestionnaireStatus) property.getValue()).getLocalizationKey());
                }
                return super.formatPropertyValue(rowId, colId, property);
            }
        };
        propertyTable.setDisplayRowCount(true);
        propertyTable.setImmediate(true);
        propertyTable.setColumnCollapsingAllowed(true);
        propertyTable.setSizeFull();

        propertyTable.addGeneratedColumn(ROW_ACTION_COLUMN_ID, new Table.ColumnGenerator() {
            @Override
            public Object generateCell(Table sourceTable, final Object itemId, Object columnId) {
                return getActionButton(propertyContainer.getBean(itemId));
            }
        });

        propertyTable.addGeneratedColumn(CLIENT_NAME_ID, new Table.ColumnGenerator() {
            @Override
            public Object generateCell(Table source, Object itemId, Object columnId) {
                final Property property = propertyContainer.getBean(itemId);
                TetrisLabel label = new TetrisLabel(property.getClient().getName());
                return label;
            }
        });

        propertyTable.addGeneratedColumn("code", new ColumnGenerator() {
            @Override
            public Object generateCell(Table source, Object itemId, Object columnId) {
                final Property property = propertyContainer.getBean(itemId);
                TetrisLinkButton editButton = new TetrisLinkButton(property.getCode(), new Button.ClickListener() {
                    @Override
                    public void buttonClick(Button.ClickEvent event) {
                        selectedProperty = property;
                        viewStack.setSelectedIndex(SHOW_EDIT_PROPERTY_VIEW_INDEX);
                    }
                });

                HorizontalLayout iconLayout = new HorizontalLayout();
                iconLayout.setSpacing(true);
                iconLayout.setDefaultComponentAlignment(Alignment.MIDDLE_CENTER);
                if (presenter.canShowSpecialCareFlag(property)) {
                    Label specialCareIcon = new Label();
                    specialCareIcon.setContentMode(ContentMode.HTML);
                    specialCareIcon.setValue(TetrisFontAwesome.PROPERTY_SPECIAL_HANDLING_FLAG.getHtml());
                    specialCareIcon.setDescription(getText("specialHandling.flag.tooltip"));
                    iconLayout.addComponent(specialCareIcon);
                }

                if (presenter.canShowMigrationFlag(property)) {
                    Label migrationIcon = new Label();
                    migrationIcon.setContentMode(ContentMode.HTML);
                    migrationIcon.setValue(TetrisFontAwesome.PROPERTY_MIGRATION_FLAG.getHtml());
                    migrationIcon.setDescription(getText("migration.flag.tooltip"));
                    iconLayout.addComponent(migrationIcon);
                }

                editButton.setCaption(property.getCode());
                editButton.setCaptionAsHtml(false);

                HorizontalLayout layout = new HorizontalLayout();
                layout.setSpacing(false);
                layout.setDefaultComponentAlignment(Alignment.MIDDLE_CENTER);
                layout.addComponents(editButton, iconLayout);

                return layout;

            }
        });

        propertyTable.addGeneratedColumn(EXTERNAL_SYSTEM_COLUMN_ID, new Table.ColumnGenerator() {
            @Override
            public Object generateCell(Table sourceTable, final Object itemId, Object columnId) {
                final Property property = propertyContainer.getBean(itemId);
                String externalSystem = presenter.getExternalSystemAsString(property.getClient().getCode(), property.getCode());
                return new TetrisLabel(externalSystem);
            }
        });

        propertyTable.addGeneratedColumn(LAST_UPDATED_BY_USER, new Table.ColumnGenerator() {
            @Override
            public Object generateCell(Table source, Object itemId, Object columnId) {
                final Property property = propertyContainer.getBean(itemId);
                String lastUpdatedByUser = presenter.getUserNameFor(property.getLastUpdatedByUserId());
                TetrisLabel label = new TetrisLabel(lastUpdatedByUser);
                return label;
            }
        });

        propertyTable.addGeneratedColumn(LAST_UPDATED_ON, (ColumnGenerator) (source, itemId, columnId) ->
                new TetrisLabel(presenter.getLastUpdatedDateFor(propertyContainer.getBean(itemId))));

        propertyTable.setVisibleColumns(getColumnPropertyNames().toArray());
        List<String> columnHeaders = getColumnHeaders();
        propertyTable.setColumnHeaders(getColumnHeaders().toArray(new String[columnHeaders.size()]));
        propertyTable.setColumnCollapsed("questionnaireStatus", Boolean.TRUE);
        propertyTable.setColumnCollapsed(LAST_UPDATED_BY_USER, Boolean.TRUE);
        propertyTable.setColumnCollapsed(LAST_UPDATED_ON, Boolean.TRUE);
    }

    private void openAddPropertyWindow() {
        TetrisWindow addPropertyWindow = new TetrisWindow();
        addPropertyWindow.setCaption(getText(RESOURCE_PROP_ADD));
        addPropertyWindow.setContent(addPropertyView.openView());

        UI.getCurrent().getUI().addWindow(addPropertyWindow);
        addPropertyWindow.focus();
        addPropertyWindow.center();
        addPropertyWindow.setModal(true);
        addPropertyWindow.setResizable(false);
    }

    public void showProperties() {
        viewStack.setSelectedIndex(SHOW_PROPERTIES_VIEW_INDEX);
    }

    protected List<String> getColumnHeaders() {
        return new ArrayList<>(Arrays.asList(getText(RESOURCE_ACTION), getText(RESOURCE_SFDC_ACCT_NUMBER), getText(RESOURCE_CLIENT_NAME),
                getText(RESOURCE_CLIENT_CODE), getText(RESOURCE_PROP_ID), getText(RESOURCE_PROP_CODE), getText(RESOURCE_PROP_NAME),
                getText(RESOURCE_STAGE), getText(RESOURCE_EXTERNAL_SYSTEM), getText(RESOURCE_QUESTIONNAIRE_STATUS),
                getText(RESOURCE_LAST_UPDATED_BY), getText(RESOURCE_LAST_UPDATED_ON)));
    }

    //do I need to add generated columns here?
    protected List<String> getColumnPropertyNames() {
        return new ArrayList<>(Arrays.asList(ROW_ACTION_COLUMN_ID, PROPERTY_SFDC_ACCT_NO, CLIENT_NAME_ID, "client", "id",
                "code", "name", "stage", EXTERNAL_SYSTEM_COLUMN_ID,
                "questionnaireStatus", LAST_UPDATED_BY_USER,
                LAST_UPDATED_ON));
    }

    public void refresh() {
        fieldGroup.setItemDataSource(presenter.getPropertyFilterDto());
        propertyContainer.refresh();
        exportButton.setEnabled(propertyContainer.sizeExcludingNoDataRow() > 0);
    }

    private TetrisActionButton getActionButton(final Property property) {
        ArrayList<TetrisActionButton.Action> actions = new ArrayList<>();
        actions.add(new TetrisActionButton.Action(getText(PropertyAction.CONFIGURE.getLocalizationString()), PropertyAction.CONFIGURE));
        actions.add(new TetrisActionButton.Action(getText(PropertyAction.CATCHUP_EXTRACT.getLocalizationString()), PropertyAction.CATCHUP_EXTRACT));
        TetrisActionButton.Action rollbackAction = new TetrisActionButton.Action(getText(PropertyAction.ROLLBACK.getLocalizationString()), PropertyAction.ROLLBACK);
        actions.add(rollbackAction);
        rollbackAction.setEnabledRequirements(true, TetrisPermissionKey.INSTALLATION_ROLLBACK);
        TetrisActionButton.Action deleteAction = new TetrisActionButton.Action(getText(PropertyAction.DELETE.getLocalizationString()), PropertyAction.DELETE);
        actions.add(deleteAction);
        deleteAction.setEnabledRequirements(true, TetrisPermissionKey.INSTALLATION_DELETE);
        actions.add(new TetrisActionButton.Action(getText(PropertyAction.RECREATETRANSDATA.getLocalizationString()), PropertyAction.RECREATETRANSDATA));
        actions.add(new TetrisActionButton.Action(getText(PropertyAction.OXI_HISTORICAL_INVENTORY_UPLOAD.getLocalizationString()), PropertyAction.OXI_HISTORICAL_INVENTORY_UPLOAD));
        actions.add(new TetrisActionButton.Action(getText(PropertyAction.DATAFEED_SFTPCONFIG_CHECK.getLocalizationString()), PropertyAction.DATAFEED_SFTPCONFIG_CHECK));
        actions.add(new TetrisActionButton.Action(getText(PropertyAction.OXI_HISTORICAL_DATA_VALIDATION.getLocalizationString()), PropertyAction.OXI_HISTORICAL_DATA_VALIDATION));
        actions.add(new TetrisActionButton.Action(getText(PropertyAction.DELETE_PSEUDO_ROOM_TYPES.getLocalizationString()), PropertyAction.DELETE_PSEUDO_ROOM_TYPES));
        actions.add(new TetrisActionButton.Action(getText(PropertyAction.GROUP_MASTER_BOOKING_DATE_POPULATION.getLocalizationString()), PropertyAction.GROUP_MASTER_BOOKING_DATE_POPULATION));
        actions.add(new TetrisActionButton.Action(getText(PropertyAction.IMPORT_OXI_PACKAGE_CONFIGURATION.getLocalizationString()), PropertyAction.IMPORT_OXI_PACKAGE_CONFIGURATION));
        actions.add(new TetrisActionButton.Action(getText(PropertyAction.CP_RECOMMENDED_FLOOR_CEILING_UPLOAD.getLocalizationString()), PropertyAction.CP_RECOMMENDED_FLOOR_CEILING_UPLOAD));
        actions.add(new TetrisActionButton.Action(getText(PropertyAction.GENERATE_CP_RECOMMENDED_FLOOR_CEILING_VALUES.getLocalizationString()), PropertyAction.GENERATE_CP_RECOMMENDED_FLOOR_CEILING_VALUES));
        actions.add(new TetrisActionButton.Action(getText(PropertyAction.HILTON_RATESYNC_REPLAY.getLocalizationString()), PropertyAction.HILTON_RATESYNC_REPLAY));

        TetrisActionButton actionButton = new TetrisActionButton(getText(RESOURCE_ACTION), actions) {
            @Override
            protected void popupVisibilityChange(PopupButton.PopupVisibilityEvent popupVisibilityEvent) {
                if (popupVisibilityEvent.isPopupVisible()) {
                    // Make context sensitive if necessary
                    setActionEnabled(PropertyAction.CATCHUP_EXTRACT, presenter.isEligibleForCatchup(property));
                    setActionEnabled(PropertyAction.ROLLBACK, presenter.isEligibleForRollback(property));
                    setActionEnabled(PropertyAction.DELETE, presenter.isEligibleForDelete(property));
                    setActionEnabled(PropertyAction.RECREATETRANSDATA, presenter.isEligibleForReCreateTransData(property.getId()));
                    setActionEnabled(PropertyAction.OXI_HISTORICAL_INVENTORY_UPLOAD, presenter.isEligibleForHistoricalDataUpload(property));
                    setActionEnabled(PropertyAction.DATAFEED_SFTPCONFIG_CHECK, presenter.isEligibleForDatafeed(property));
                    setActionEnabled(PropertyAction.OXI_HISTORICAL_DATA_VALIDATION, presenter.isEligibleForHistoricalDataValidation(property));
                    setActionEnabled(PropertyAction.DELETE_PSEUDO_ROOM_TYPES, presenter.isEligibleForDeletingPseudoRoomTypes(property));
                    setActionEnabled(PropertyAction.GROUP_MASTER_BOOKING_DATE_POPULATION, presenter.isEligibleForGroupMasterBookingPopulation(property));
                    setActionEnabled(PropertyAction.IMPORT_OXI_PACKAGE_CONFIGURATION, presenter.isEligibleForImportOxiPackageConfiguration(property));
                    setActionEnabled(PropertyAction.CP_RECOMMENDED_FLOOR_CEILING_UPLOAD, presenter.isEligibleForCPFloorCeiling(property));
                    setActionEnabled(PropertyAction.GENERATE_CP_RECOMMENDED_FLOOR_CEILING_VALUES, presenter.isEligibleForCPFloorCeiling(property));
                    setActionEnabled(PropertyAction.HILTON_RATESYNC_REPLAY, presenter.isEligibleForHiltonRateSyncAndReplay(property));
                }
            }
        };

        PropertyActionListener actionListener = new PropertyActionListener();
        actionListener.setProperty(property);
        actionButton.addClickListener(actionListener);

        return actionButton;
    }

    private void changePropertyComboBoxes(Client value) {
        propertyCodeBeanContainer.removeAllItems();
        propertyNameBeanContainer.removeAllItems();
        if (value != null) {
            List<Property> properties = presenter.getProperties(value);
            propertyCodeBeanContainer.addAll(properties);
            propertyNameBeanContainer.addAll(properties);
            PropertyFilterDto propertyFilterDto = fieldGroup.getBean();
            if (propertyFilterDto != null) {
                propertyFilterDto.setPropertyByCode(PropertyInstallationPresenter.ALL_PROPERTIES);
                propertyFilterDto.setPropertyByName(PropertyInstallationPresenter.ALL_PROPERTIES);
                propertyCodesTextArea.setEnabled(true);
                enableDisableStageSelect();

            }

        }
    }

    class PropertyActionListener implements TetrisActionButton.ClickListener {
        private Property property;

        public void setProperty(Property property) {
            this.property = property;
        }

        @Override
        public void onClicked(final TetrisActionButton.ClickedEvent event) {
            PropertyAction action = (PropertyAction) event.getAction().getData();
            // Notification show
            TetrisWindow actionWindow = new TetrisWindow();
            actionWindow.setCaption(getText(action.getLocalizationString()) + " " + property.getName() + " (" + property.getCode() + " - " + property.getId()
                    + ")");
            boolean isPopup = false;
            switch (action) {
                case CONFIGURE:
                    executeConfigureAction();
                    break;
                case CATCHUP_EXTRACT:
                    isPopup = true;
                    launchCatchupPopup(actionWindow);
                    break;
                case CATCHUP_WEBRATE:
                    break;
                case ROLLBACK:
                    List<String> results = presenter.enforceRollbackPrerequisites(property);
                    if (results.size() > 0) {
                        displayUnmetPrerequisites(results);
                    } else {
                        showWarningPrompt("Rollback Property Warning ", getText("installation.property.rollback", property.getId(), property.getName()), (Button.ClickListener) clickEvent1 -> {
                            launchRollbackPopup(actionWindow);
                            createActionWindow(actionWindow);
                        });
                    }
                    break;
                case DELETE:
                    showWarningPrompt("Delete Property Warning ", getText("installation.property.delete", property.getId(), property.getName()), (Button.ClickListener) clickEvent1 -> {
                        launchDeletePopup(actionWindow);
                        createActionWindow(actionWindow);
                    });
                    break;
                case RECREATETRANSDATA:
                    isPopup = true;
                    launchRecreateTransDataPopup(actionWindow);
                    break;
                case OXI_HISTORICAL_INVENTORY_UPLOAD:
                    isPopup = true;
                    launchHistoricalDataUploadPopup(actionWindow);
                    break;
                case DATAFEED_SFTPCONFIG_CHECK:
                    performSftpConfigCheckForDatafeed();
                    break;
                case OXI_HISTORICAL_DATA_VALIDATION:
                    isPopup = true;
                    launchHistoricalDataValidationPopup(actionWindow);
                    break;
                case DELETE_PSEUDO_ROOM_TYPES:
                    isPopup = true;
                    launchDeletePseudoRoomTypePopup(actionWindow);
                    break;
                case GROUP_MASTER_BOOKING_DATE_POPULATION:
                    isPopup = true;
                    launchGroupMasterBookingPopulationPopup(actionWindow);
                    break;
                case IMPORT_OXI_PACKAGE_CONFIGURATION:
                    isPopup = true;
                    launchImportOxiPackageConfigurationPopup(actionWindow);
                    break;
                case CP_RECOMMENDED_FLOOR_CEILING_UPLOAD:
                    isPopup = true;
                    launchCPRecommendedFloorCeilingUploadPopup(actionWindow);
                    break;
                case GENERATE_CP_RECOMMENDED_FLOOR_CEILING_VALUES:
                    showWarningPrompt("Generate Recommended Floor/Ceiling values", getText("installation.generate.floor.ceiling.values", property.getName()), (Button.ClickListener) clickEvent1 -> {
                        presenter.triggerRecommendedFloorCeilingJob(property.getId());
                    });
                    break;
                case HILTON_RATESYNC_REPLAY:
                    isPopup = true;
                    launchRateSyncReplayPopup(actionWindow);
                    break;
                default:
                    LOGGER.warn("Unknown action: " + action.toString());
                    break;
            }

            if (isPopup) {
                UI.getCurrent().getUI().addWindow(actionWindow);
                actionWindow.focus();
                actionWindow.center();
                actionWindow.setModal(true);
                actionWindow.setResizable(false);
            }
        }

        protected void displayUnmetPrerequisites(List<String> unmetPrerequisites) {
            StringBuilder buffer = new StringBuilder();
            for (String line : unmetPrerequisites) {
                buffer.append(line).append("\n");
            }
            buffer.append("\n");
            showError(buffer.toString());
        }

        private void executeConfigureAction() {
            // Need to maintain current property else 'not authorized' will
            // welcome
            String propertyContext = "";
            String[] fragments = TetrisUi.getCurrent().getPage().getUriFragment().split("/");
            if (fragments.length > 1 && fragments[0].isEmpty()) {
                propertyContext = fragments[1] + "/";
            } else {
                LOGGER.info("Unexpected url fragment: " + TetrisUi.getCurrent().getPage().getUriFragment());
            }
            String targetTab = PropertyParametersView.class.getSimpleName();
            targetTab = targetTab.substring(0, 1).toLowerCase() + targetTab.substring(1);
            // Read that hot mess - i dare you
            Page.getCurrent().setLocation(
                    MessageFormat.format("/{0}/{1}#/{2}{3}{4}/{5}{6}/!!{7}", UiUtils.getTetrisUi().getSolutionsContextPath(), TetrisPermissionKey.APP_PARAMETERS,
                            propertyContext, VaadinConstants.HASH_CLIENT, property.getClient().getCode(), VaadinConstants.HASH_PROP, property
                                    .getCode(), targetTab));
        }

        private void launchCatchupPopup(TetrisWindow actionWindow) {
            actionWindow.setContent(catchupExtractView.openView());
            catchupExtractView.getPresenter().setProperty(property);
            actionWindow.setWidth("500");
            actionWindow.setHeight("320");
        }

        private void launchRateSyncReplayPopup(TetrisWindow actionWindow) {
            actionWindow.setContent(rateSyncReplayView.openView());
            rateSyncReplayView.getPresenter().setProperty(property);
            actionWindow.setWidth("700");
            actionWindow.setHeight("150");
        }

        private void launchRollbackPopup(TetrisWindow actionWindow) {
            actionWindow.setContent(rollbackView.openView());
            rollbackView.getPresenter().setProperty(property);
            actionWindow.setWidth("400");
            actionWindow.setHeight("150");
        }

        private void launchDeletePopup(TetrisWindow actionWindow) {
            actionWindow.setContent(propertyDeleteView.openView());
            propertyDeleteView.getPresenter().setProperty(property);
            actionWindow.setWidth("500");
            actionWindow.setHeight("200");
        }

        private void launchRecreateTransDataPopup(TetrisWindow actionWindow) {
            actionWindow.setContent(reCreateTransDataView.openView());
            reCreateTransDataView.getPresenter().setProperty(property);
            actionWindow.setWidth("500");
            actionWindow.setHeight("200");
        }

        private void launchHistoricalDataUploadPopup(TetrisWindow actionWindow) {
            actionWindow.setContent(historicalDataUploadView.openView());
            historicalDataUploadView.getPresenter().setProperty(property);
            actionWindow.setWidth("500");
            actionWindow.setHeight("100");
        }

        private void launchHistoricalDataValidationPopup(TetrisWindow actionWindow) {
            actionWindow.setContent(historicalDataValidationView.openView());
            historicalDataValidationView.getPresenter().setProperty(property);
            actionWindow.setWidth("500");
            actionWindow.setHeight("100");
        }

        private void launchDeletePseudoRoomTypePopup(TetrisWindow actionWindow) {
            actionWindow.setContent(deletePseudoRoomTypePopupView.openView());
            deletePseudoRoomTypePopupView.getPresenter().setProperty(property);
            actionWindow.setWidth("530");
            actionWindow.setHeight("520");
        }

        private void launchImportOxiPackageConfigurationPopup(TetrisWindow actionWindow) {
            importOxiPackageConfigurationPopupView.setId("importOxiPackagePopup");
            actionWindow.setContent(importOxiPackageConfigurationPopupView.openView(property));
            importOxiPackageConfigurationPopupView.getPresenter().clearFileNameField();
            actionWindow.setWidth("700");
            actionWindow.setHeight("700");
        }

        private void performSftpConfigCheckForDatafeed() {
            String result = presenter.testSftpConnectionForDatafeed(property.getId());
            if (DATAFEED_SFTP_TEST_SUCCESS_MESSAGE.equalsIgnoreCase(result)) {
                TetrisNotification.showSuccessMessage(result);
            } else {
                TetrisNotification.showError(result);
            }
        }

        private void launchGroupMasterBookingPopulationPopup(TetrisWindow actionWindow) {
            groupMasterBookingPopulationView.setId("groupMasterBookingPopup");
            actionWindow.setWidth(40, Unit.PERCENTAGE);
            actionWindow.setHeight(30, Unit.PERCENTAGE);
            actionWindow.setContent(groupMasterBookingPopulationView.openView(property));
            actionWindow.addCloseListener(groupMasterBookingPopulationView.getCloseListener());
        }

        private void launchCPRecommendedFloorCeilingUploadPopup(TetrisWindow actionWindow) {

            cpRecommendedFloorCeilingUploadView.setId("cpRecommendedFloorCeilingUploadView");
            actionWindow.setWidth(40, Unit.PERCENTAGE);
            actionWindow.setHeight(30, Unit.PERCENTAGE);
            actionWindow.setContent(cpRecommendedFloorCeilingUploadView.openView(property));
            actionWindow.addCloseListener(cpRecommendedFloorCeilingUploadView.getCloseListener());
        }
    }


    private void createActionWindow(TetrisWindow actionWindow) {
        UI.getCurrent().getUI().addWindow(actionWindow);
        actionWindow.focus();
        actionWindow.center();
        actionWindow.setModal(true);
        actionWindow.setResizable(false);
    }
}