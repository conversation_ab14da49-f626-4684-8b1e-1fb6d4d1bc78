package com.ideas.tetris.ui.modules.functionspace.performancetrend;


import com.ideas.infra.tetris.security.domain.TetrisPermissionKey;
import com.ideas.tetris.pacman.services.functionspace.configuration.entity.FunctionSpaceDayPart;
import com.ideas.tetris.ui.common.cdi.TetrisNavigatorView;
import com.ideas.tetris.ui.common.component.TetrisBeanItemContainer;
import com.ideas.tetris.ui.common.component.TetrisSaveCancelButtonBar;
import com.ideas.tetris.ui.common.component.panel.PanelBar;
import com.ideas.tetris.ui.common.component.select.TetrisComboBox;
import com.ideas.tetris.ui.common.component.textfield.TetrisLocalDateField;
import com.vaadin.cdi.CDIView;
import com.vaadin.ui.FormLayout;
import com.vaadin.ui.TabSheet;
import com.vaadin.ui.UI;
import com.vaadin.v7.ui.HorizontalLayout;
import com.vaadin.v7.ui.VerticalLayout;
import com.vaadin.v7.ui.themes.Reindeer;

import java.util.List;

@CDIView(uis = UI.class, value = TetrisPermissionKey.FUNCTION_SPACE_PERFORMANCE_TRENDS)
public class FunctionSpacePerformanceTrendView extends TetrisNavigatorView<FunctionSpacePerformanceTrendPresenter, Void> {
    TabSheet tabSheet;
    private TetrisBeanItemContainer<FunctionSpaceDayPart> dayPartContainer;
    private TetrisComboBox dayPartCombobox;
    private PanelBar panelBar;

    @Override
    protected void initView() {
        setSizeFull();
        VerticalLayout verticalLayout = new VerticalLayout();
        verticalLayout.setMargin(true);
        verticalLayout.setSpacing(true);
        verticalLayout.setSizeFull();

        panelBar = new PanelBar(PanelBar.PanelBarPosition.HEADER);
        panelBar.setTitle(getText("filter"));
        verticalLayout.addComponent(panelBar);

        tabSheet = new TabSheet();
        tabSheet.setSizeFull();
        tabSheet.addStyleName(Reindeer.TABSHEET_MINIMAL);
        tabSheet.addTab(new FunctionSpaceUtilizationTab(presenter), getText("groupEvaluation.functionSpaceUtilization"));
        tabSheet.addTab(new RevenueAndProfitTab(presenter), getText("groupEvaluation.revenueProfit"));
        tabSheet.addTab(new FunctionSpaceDetailTab(presenter), getText("groupEvaluation.functionSpaceDetail"));
        verticalLayout.addComponent(tabSheet);
        verticalLayout.setExpandRatio(tabSheet, 1.0f);
        setCompositionRoot(verticalLayout);
    }

    private VerticalLayout getFilterComponent() {
        TetrisLocalDateField startDateField = new TetrisLocalDateField(getText("startDate"));
        presenter.getFieldGroup().bind(startDateField, "startDate");

        TetrisLocalDateField endDateField = new TetrisLocalDateField(getText("endDate"));
        presenter.getFieldGroup().bind(endDateField, "endDate");
        startDateField.setDependentEndDateField(endDateField);
        endDateField.setDependentStartDateField(startDateField);
        dayPartContainer = new TetrisBeanItemContainer<FunctionSpaceDayPart>(FunctionSpaceDayPart.class);
        dayPartCombobox = new TetrisComboBox(getText("common.dayPart"), dayPartContainer);
        dayPartCombobox.setRequired(false);
        dayPartCombobox.setNullSelectionAllowed(true);
        dayPartCombobox.setItemCaptionPropertyId("name");
        presenter.getFieldGroup().bind(dayPartCombobox, "dayPart");

        TetrisSaveCancelButtonBar saveCancelButtonBar = new TetrisSaveCancelButtonBar();
        saveCancelButtonBar.addValidSaveListener(new TetrisSaveCancelButtonBar.ValidSaveListener() {
            @Override
            public void onValidSave(TetrisSaveCancelButtonBar.ValidSaveEvent event) {
                FunctionSpacePerformanceTrend selectedTab = (FunctionSpacePerformanceTrend) tabSheet.getSelectedTab();
                boolean isUtilizationTab = selectedTab instanceof FunctionSpaceUtilizationTab;
                selectedTab.setData(presenter.getData(isUtilizationTab));
            }
        });
        saveCancelButtonBar.getSaveButton().setCaption(getText("apply"));
        presenter.getFieldGroup().setSaveCancelButtonBar(saveCancelButtonBar);

        HorizontalLayout components = new HorizontalLayout(new FormLayout(startDateField), new FormLayout(endDateField), new FormLayout(dayPartCombobox));
        components.setSpacing(true);
        VerticalLayout verticalLayout = new VerticalLayout(components, saveCancelButtonBar);
        verticalLayout.setMargin(true);
        return verticalLayout;
    }

    @Override
    public void onPresenterInit() {
        panelBar.setContent(getFilterComponent());
        panelBar.setOpen(true);
        //presenter is not null when onPresenterInit is called.
        tabSheet.addSelectedTabChangeListener(new TabSheet.SelectedTabChangeListener() {
            @Override
            public void selectedTabChange(TabSheet.SelectedTabChangeEvent selectedTabChangeEvent) {
                loadDataOntoOpenTab();
            }
        });
        loadDataOntoOpenTab();
    }

    private void loadDataOntoOpenTab() {
        FunctionSpacePerformanceTrend selectedTab = (FunctionSpacePerformanceTrend) tabSheet.getSelectedTab();
        boolean isUtilizationTab = tabSheet.getSelectedTab() instanceof FunctionSpaceUtilizationTab;
        setDayPartVisibility(isUtilizationTab);
        selectedTab.setData(presenter.getData(isUtilizationTab));
    }

    public void setDayPartVisibility(boolean value) {
        dayPartCombobox.setVisible(value);
        dayPartCombobox.setEnabled(value);
    }

    public void setContainerDataSource(List<FunctionSpaceDayPart> dayParts) {
        dayPartContainer.removeAllItems();
        dayPartContainer.addAll(dayParts);
    }

    public void reLodData() {
        loadDataOntoOpenTab();
    }
}
