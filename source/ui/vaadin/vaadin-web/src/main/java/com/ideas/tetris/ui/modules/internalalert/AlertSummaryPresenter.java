package com.ideas.tetris.ui.modules.internalalert;

import com.ideas.tetris.pacman.services.internalalert.InternalAlertService;
import com.ideas.tetris.ui.common.cdi.TetrisPresenter;
import org.springframework.beans.factory.annotation.Autowired;

public class AlertSummaryPresenter extends TetrisPresenter<AlertSummaryView, Void> {

    @Autowired
    InternalAlertService internalAlertService;

    @Override
    public void onViewOpened(Void aVoid) {
        view.initViewInternal();
        updateData();
    }

    protected void updateData() {
        view.updateData(internalAlertService.getAlertSummary());
    }

}
