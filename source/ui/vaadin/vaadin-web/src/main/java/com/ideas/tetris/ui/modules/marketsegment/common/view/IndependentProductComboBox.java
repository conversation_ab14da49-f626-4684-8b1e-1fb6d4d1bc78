package com.ideas.tetris.ui.modules.marketsegment.common.view;

import com.ideas.tetris.pacman.services.product.Product;
import com.ideas.tetris.ui.common.component.select.TetrisComboBoxV8;
import com.ideas.tetris.ui.common.util.UiUtils;

public class IndependentProductComboBox extends TetrisComboBoxV8<Product> {

    public IndependentProductComboBox() {
        setId("ProductComboBox");
        setCaption(UiUtils.getText("independent.products.market.segments.base.product.header"));
        setItemCaptionGenerator(Product::getName);
        setEmptySelectionAllowed(true);
    }

    public boolean isValid() {
        return !isRequiredIndicatorVisible() || !isEmpty();
    }

    public void setRequired(boolean required) {
        setRequiredIndicatorVisible(required);
        setEnabled(required);
        if (!required) {
            setValue(null);
        }
    }
}
