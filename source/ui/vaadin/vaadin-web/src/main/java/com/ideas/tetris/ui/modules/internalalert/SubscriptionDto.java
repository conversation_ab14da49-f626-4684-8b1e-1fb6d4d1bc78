package com.ideas.tetris.ui.modules.internalalert;

import com.ideas.tetris.pacman.services.internalalert.InternalAlertType;
import com.ideas.tetris.ui.common.component.table.TetrisChangeAwareColumnComponentProvider;

public class SubscriptionDto extends TetrisChangeAwareColumnComponentProvider {
    private InternalAlertType alertType;
    private boolean isSubscribed;

    public SubscriptionDto() {

    }

    public SubscriptionDto(InternalAlertType alertType, boolean isSubscribed) {
        this.alertType = alertType;
        this.isSubscribed = isSubscribed;
    }

    public InternalAlertType getAlertType() {
        return alertType;
    }

    public void setAlertType(InternalAlertType alertType) {
        this.alertType = alertType;
    }

    public boolean isSubscribed() {
        return isSubscribed;
    }

    public void setSubscribed(boolean subscribed) {
        isSubscribed = subscribed;
    }

}
