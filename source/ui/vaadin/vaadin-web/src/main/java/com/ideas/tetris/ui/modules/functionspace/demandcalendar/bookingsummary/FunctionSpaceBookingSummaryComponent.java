package com.ideas.tetris.ui.modules.functionspace.demandcalendar.bookingsummary;

import com.ideas.tetris.ui.common.component.TetrisBeanItemContainer;
import com.ideas.tetris.ui.common.component.TetrisLabel;
import com.ideas.tetris.ui.common.component.table.TetrisTable;
import com.ideas.tetris.ui.common.util.UiUtils;
import com.ideas.tetris.ui.modules.functionspace.demandcalendar.FunctionSpaceDemandCalendarPresenter;
import com.vaadin.shared.ui.MarginInfo;
import com.vaadin.v7.shared.ui.label.ContentMode;
import com.vaadin.v7.ui.HorizontalLayout;
import com.vaadin.v7.ui.Table;
import com.vaadin.v7.ui.VerticalLayout;
import org.joda.time.LocalDate;

public class FunctionSpaceBookingSummaryComponent extends VerticalLayout {

    private final FunctionSpaceDemandCalendarPresenter presenter;
    private final TetrisBeanItemContainer<RowData> dayPartContainer;
    private final TetrisBeanItemContainer<RowData> monthlySummaryContainer;
    private final TetrisBeanItemContainer<RowData> monthToDateAverageContainer;
    private TetrisTable monthSummaryTable;
    private TetrisTable summaryMonthAverageTable;
    private TetrisLabel summaryMonthAverageLabel;

    public FunctionSpaceBookingSummaryComponent(FunctionSpaceDemandCalendarPresenter presenter) {
        this.addStyleName("functionspace-summary");
        this.presenter = presenter;

        HorizontalLayout title = new HorizontalLayout(new TetrisLabel(UiUtils.getText("groupPricing.demandCalendar.businessSummary")));
        title.addStyleName("title");
        title.setSizeFull();
        addComponent(title);

        VerticalLayout bookingSummaryContent = new VerticalLayout();
        bookingSummaryContent.setMargin(new MarginInfo(false, true, true, true));
        bookingSummaryContent.setSpacing(true);
        addComponent(bookingSummaryContent);

        dayPartContainer = new TetrisBeanItemContainer<RowData>(RowData.class);
        addDayPartConfigurationTable(bookingSummaryContent);
        monthlySummaryContainer = new TetrisBeanItemContainer<RowData>(RowData.class);
        addMonthSummaryConfigurationTable(bookingSummaryContent);
        monthToDateAverageContainer = new TetrisBeanItemContainer<RowData>(RowData.class);
        addMonthToDateSummaryAverageTable(bookingSummaryContent);

        this.setExpandRatio(bookingSummaryContent, 1);
    }

    private void addMonthToDateSummaryAverageTable(VerticalLayout bookingSummaryContent) {

        VerticalLayout layout = new VerticalLayout();
        layout.setSpacing(false);

        summaryMonthAverageLabel = new TetrisLabel(UiUtils.getText("groupPricing.demandCalendar.monthDateSumAvg"), ContentMode.HTML);
        summaryMonthAverageLabel.setStyleName("v-captiontext");
        layout.addComponent(summaryMonthAverageLabel);

        summaryMonthAverageTable = new TetrisTable(monthToDateAverageContainer);
        applyFormatToTable(summaryMonthAverageTable);
        layout.addComponent(summaryMonthAverageTable);

        bookingSummaryContent.addComponent(layout);

    }

    private void addMonthSummaryConfigurationTable(VerticalLayout bookingSummaryContent) {
        monthSummaryTable = new TetrisTable(UiUtils.getText("groupPricing.demandCalendar.monthlySummary"), monthlySummaryContainer);
        applyFormatToTable(monthSummaryTable);
        bookingSummaryContent.addComponent(monthSummaryTable);
    }

    private void addDayPartConfigurationTable(VerticalLayout bookingSummaryContent) {
        TetrisTable dayPartConfigurationTable = new TetrisTable(UiUtils.getText("groupPricing.demandCalendar.dayPartConfiguration"), dayPartContainer);
        applyFormatToTable(dayPartConfigurationTable);
        dayPartConfigurationTable.setPageLength(4);
        bookingSummaryContent.addComponent(dayPartConfigurationTable);
    }

    private void applyFormatToTable(TetrisTable table) {
        table.setPageLength(5);
        table.setWidth(100, Unit.PERCENTAGE);
        table.setColumnHeaderMode(Table.ColumnHeaderMode.HIDDEN);

        String value = "value";
        String label = "label";
        table.setVisibleColumns(new String[]{label, value});
        table.setColumnAlignment(label, Table.Align.RIGHT);
        table.setColumnWidth(value, 75);
    }

    public void updateSummary(BookingSummaryUiWrapper summaryUiWrapper) {
        dayPartContainer.removeAllItems();
        dayPartContainer.addAll(summaryUiWrapper.getDayParts());

        monthlySummaryContainer.removeAllItems();
        monthlySummaryContainer.addAll(summaryUiWrapper.getMonthSummary());

        monthToDateAverageContainer.removeAllItems();
        monthToDateAverageContainer.addAll(summaryUiWrapper.getMonthToDateAverage());

        LocalDate selectedMonth = presenter.getSelectedMonth().dayOfMonth().withMinimumValue();
        LocalDate systemDate = presenter.getSystemDateAsLocalDate().dayOfMonth().withMinimumValue();

        monthSummaryTable.setVisible(selectedMonth.compareTo(systemDate) >= 0);
        summaryMonthAverageLabel.setVisible(selectedMonth.compareTo(systemDate) <= 0);
        summaryMonthAverageTable.setVisible(selectedMonth.compareTo(systemDate) <= 0);

    }
}
