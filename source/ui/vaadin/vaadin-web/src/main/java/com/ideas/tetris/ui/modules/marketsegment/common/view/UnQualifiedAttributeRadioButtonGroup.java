package com.ideas.tetris.ui.modules.marketsegment.common.view;

import com.ideas.tetris.pacman.services.marketsegment.service.AnalyticalMarketSegmentAttribute;

import java.util.Arrays;
import java.util.Collection;

import static com.ideas.tetris.pacman.services.marketsegment.service.AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR;
import static com.ideas.tetris.pacman.services.marketsegment.service.AnalyticalMarketSegmentAttribute.FENCED;
import static com.ideas.tetris.pacman.services.marketsegment.service.AnalyticalMarketSegmentAttribute.FENCED_AND_PACKAGED;
import static com.ideas.tetris.pacman.services.marketsegment.service.AnalyticalMarketSegmentAttribute.PACKAGED;
import static com.ideas.tetris.pacman.services.marketsegment.service.AnalyticalMarketSegmentAttribute.UNFENCED_NON_PACKAGED;
import static com.ideas.tetris.ui.common.util.UiUtils.getText;

public class UnQualifiedAttributeRadioButtonGroup extends MarketSegmentAttributionRadioButtonGroup<AnalyticalMarketSegmentAttribute> {

    public UnQualifiedAttributeRadioButtonGroup(AttributeAssignmentConfig config) {
        super("UnQualifiedAttributeRadioButtonGroup", getText("unqualified.attribute"), config);
    }

    public String getDescription() {
        return DescriptionBuilder.buildForUnQualifiedAttributes(isIndependentProductsEnabled());
    }

    @Override
    protected Collection<AnalyticalMarketSegmentAttribute> getItems() {
        return Arrays.asList(EQUAL_TO_BAR, FENCED, PACKAGED, FENCED_AND_PACKAGED, UNFENCED_NON_PACKAGED);
    }

    @Override
    protected String getCaption(AnalyticalMarketSegmentAttribute item) {
        switch (item) {
            case EQUAL_TO_BAR:
                return getText(isIndependentProductsEnabled() ? "equalToBaseProduct" : "equalToBar");
            case FENCED:
                return getText("fenced.caption");
            case PACKAGED:
                return getText("packaged");
            case FENCED_AND_PACKAGED:
                return getText("fenced.and.packaged.caption");
            case UNFENCED_NON_PACKAGED:
                return getText("unfenced.and.nonpackaged");
        }
        return null;
    }

}
