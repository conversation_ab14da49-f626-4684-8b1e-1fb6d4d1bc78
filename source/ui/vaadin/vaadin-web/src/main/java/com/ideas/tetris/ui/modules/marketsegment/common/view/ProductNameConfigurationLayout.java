package com.ideas.tetris.ui.modules.marketsegment.common.view;

import com.ideas.tetris.pacman.services.product.Product;
import com.ideas.tetris.pacman.util.Executor;
import com.ideas.tetris.pacman.util.Runner;
import com.ideas.tetris.ui.common.TetrisComponentFactory;
import com.ideas.tetris.ui.common.component.TetrisLabel;
import com.ideas.tetris.ui.common.component.button.TetrisButton;
import com.ideas.tetris.ui.common.component.textfield.TetrisTextFieldV8;
import com.ideas.tetris.ui.common.component.window.TetrisWindow;
import com.vaadin.data.Binder;
import com.vaadin.data.ValidationResult;
import com.vaadin.data.ValueContext;
import com.vaadin.ui.Alignment;
import com.vaadin.ui.HorizontalLayout;
import com.vaadin.ui.VerticalLayout;
import org.apache.commons.lang.StringUtils;

import java.util.LinkedList;
import java.util.List;
import java.util.Optional;

import static com.ideas.tetris.pacman.util.Executor.executeIfTrue;
import static com.ideas.tetris.ui.common.util.UiUtils.getText;
import static com.vaadin.data.ValidationResult.error;
import static com.vaadin.data.ValidationResult.ok;

public class ProductNameConfigurationLayout extends VerticalLayout {

    private Product product;
    private TetrisTextFieldV8 nameField;
    private Binder<Product> binder;
    private List<Product> currentProducts;
    private Executor<String> onOk;

    public ProductNameConfigurationLayout(List<Product> currentProducts, Executor<String> onOk) {
        init(currentProducts, onOk).addProductNameField().addNote().addButtonBar();
    }

    private ProductNameConfigurationLayout addNote() {
        TetrisLabel note = new TetrisLabel();
        note.setCaption(getText("independent.products.name.dialog.note"));
        addComponent(note);
        return this;
    }

    private ProductNameConfigurationLayout addButtonBar() {
        HorizontalLayout bar = TetrisComponentFactory.createHorizontalLayout();
        bar.setWidth("100%");
        TetrisButton okay = TetrisComponentFactory.createButton("OK", getText("ok"), true);
        bar.addComponent(okay);
        okay.addClickListener(clickEvent -> executeIfTrue(binder.validate().isOk() && onOk != null, onOk, product.getName()));
        bar.setComponentAlignment(okay, Alignment.MIDDLE_CENTER);
        addComponent(bar);
        return this;
    }

    private ProductNameConfigurationLayout addProductNameField() {
        this.nameField = new TetrisTextFieldV8();
        nameField.setId("ProductNameField");
        nameField.setCaption(getText("agile.rates.product.name"));
        nameField.setRequiredIndicatorVisible(true);
        nameField.setPlaceholder(getText("independent.product"));
        binder.forField(nameField)
                .withValidator((s, valueContext) -> StringUtils.isEmpty(s) ? error(getText("common.validation.empty")) : ok())
                .withValidator(this::validateForDuplicateName)
                .bind(Product::getName, Product::setName);
        binder.setBean(product);
        addComponent(nameField);
        return this;
    }

    private ValidationResult validateForDuplicateName(String name, ValueContext valueContext) {
        Optional<Product> first = this.currentProducts.stream().filter(p -> p.getName().equalsIgnoreCase(name)).findFirst();
        return first.isPresent() ? error(getText("agile.rates.product.name.warning")) : ok();
    }

    private ProductNameConfigurationLayout init(List<Product> currentProducts, Executor<String> onOk) {
        this.product = new Product();
        this.currentProducts = currentProducts == null ? new LinkedList<>() : currentProducts;
        this.onOk = onOk;
        binder = new Binder<>();
        return this;
    }

    public static void show(Product product, List<Product> currentProducts, Runner onOkay) {
        TetrisWindow tetrisWindow = new TetrisWindow();
        tetrisWindow.setCaption(getText("independent.products.name.dialog.title"));
        ProductNameConfigurationLayout layout = new ProductNameConfigurationLayout(currentProducts, (productName) -> {
            product.setName(productName);
            tetrisWindow.close();
            onOkay.run();
        });
        tetrisWindow.setContent(layout);
        tetrisWindow.show();
    }

}
