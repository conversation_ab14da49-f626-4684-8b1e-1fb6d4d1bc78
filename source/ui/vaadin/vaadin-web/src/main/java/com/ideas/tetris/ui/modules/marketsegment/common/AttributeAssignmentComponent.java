package com.ideas.tetris.ui.modules.marketsegment.common;

import com.google.common.annotations.VisibleForTesting;
import com.ideas.tetris.pacman.services.marketsegment.entity.ForecastActivityType;
import com.ideas.tetris.pacman.services.marketsegment.service.AnalyticalMarketSegmentAttribute;
import com.ideas.tetris.pacman.services.product.Product;
import com.ideas.tetris.ui.common.component.TetrisHRule;
import com.ideas.tetris.ui.common.component.TetrisLabel;
import com.ideas.tetris.ui.common.component.TetrisNotification;
import com.ideas.tetris.ui.common.component.button.TetrisButton;
import com.ideas.tetris.ui.common.component.checkbox.TetrisCheckBoxV8;
import com.ideas.tetris.ui.common.component.optiongroup.TetrisOptionGroup;
import com.ideas.tetris.ui.common.util.UiUtils;
import com.vaadin.shared.ui.ContentMode;
import com.vaadin.ui.Alignment;
import com.vaadin.ui.Button;
import com.vaadin.ui.Component;
import com.vaadin.ui.CssLayout;
import com.vaadin.ui.CustomComponent;
import com.vaadin.util.ReflectTools;
import com.vaadin.v7.data.Property;
import com.vaadin.v7.ui.OptionGroup;
import com.vaadin.v7.ui.VerticalLayout;
import de.steinwedel.messagebox.ButtonId;
import de.steinwedel.messagebox.MessageBox;
import de.steinwedel.messagebox.MessageBoxListener;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import static com.ideas.tetris.pacman.common.constants.Constants.AGILE_PRODUCT_CODE;
import static com.ideas.tetris.pacman.common.constants.Constants.RATE_PROTECT_PRODUCT_CODE;

public class AttributeAssignmentComponent extends CustomComponent {

    private static final String UNQUALIFIED = "Unqualified";
    static final String QUALIFIED = "Qualified";
    private static final String TRANSIENT_BLOCK = "Transient Block";
    static final String LINKED = "Linked to BAR";
    static final String NON_LINKED = "Non-linked";
    private static final String TOOLTIP_CLASS_WITH_BOLD_TEXT = "<p class='attributeTooltip'><b>";
    private static final String CLOSING_BOLD_TAG = ":</b> ";
    private static final int BOOKING_BLOCK_PCT_IS_BLOCK = 100;

    private List<String> marketSegmentRateCodes = Collections.emptyList();
    private CssLayout businessTypeOptionGroupLayout;

    private CssLayout attributesLayout;

    private OptionGroup unqualifiedOptionGroup;
    private OptionGroup businessTypeOptionGroup;
    private OptionGroup linkedOptionGroup;
    private OptionGroup yieldableOptionGroup;
    private TetrisCheckBoxV8 complimentaryCheckbox = new TetrisCheckBoxV8();
    private TetrisButton assignBtn;
    private VerticalLayout rootLayout;
    private List<ForecastActivityType> forecastActivityTypes;
    private UnQualifiedOptionGroup unQualifiedOptionGroupWithIndependentProducts;
    private LinkedOptionGroup linkedOptionGroupWithIndependentProducts;
    private AmsView amsView;

    AttributeAssignmentComponentAware presenter;
    private TetrisOptionGroup forecastTypeOption;
    String mktSegCode;
    String originalAttributeDescription;
    private boolean independentProductsEnabled;

    public AttributeAssignmentComponent(final AmsView amsViewIn,
                                        AttributeAssignmentComponentAware presenter) {
        init(amsViewIn, presenter);
    }

    public AttributeAssignmentComponent(final AmsView amsViewIn,
                                        AttributeAssignmentComponentAware presenter,
                                        String mktSegCode) {
        this.mktSegCode = mktSegCode;
        init(amsViewIn, presenter);
    }

    public AttributeAssignmentComponent(AmsView amsViewIn,
                                        AttributeAssignmentComponentAware presenter,
                                        String mktSegCode,
                                        List<String> marketSegmentRateCodes) {
        setMarketSegmentRateCodes(marketSegmentRateCodes);
        this.mktSegCode = mktSegCode;
        init(amsViewIn, presenter);
    }

    public AttributeAssignmentComponent(AmsView amsViewIn,
                                        AttributeAssignmentComponentAware presenter,
                                        String marketCode,
                                        List<String> marketSegmentRateCodes,
                                        String attributeDescription) {
        setMarketSegmentRateCodes(marketSegmentRateCodes);
        this.mktSegCode = marketCode;
        originalAttributeDescription = attributeDescription;
        init(amsViewIn, presenter);
    }

    private void setMarketSegmentRateCodes(List<String> marketSegmentRateCodes) {
        if (marketSegmentRateCodes == null) {
            this.marketSegmentRateCodes = Collections.emptyList();
        } else {
            this.marketSegmentRateCodes = trimBlankMarketRateCodes(marketSegmentRateCodes);
        }
    }

    private void init(AmsView amsViewIn, AttributeAssignmentComponentAware presenter) {
        this.amsView = amsViewIn;
        this.presenter = presenter;
        forecastActivityTypes = presenter.getForecastActivityTypes();

        addStyleName("ams-assignComponent");

        VerticalLayout contentLayout = new VerticalLayout();
        contentLayout.setSpacing(true);

        rootLayout = new VerticalLayout();

        setSizeUndefined();

        TetrisLabel attributesLabel = new TetrisLabel(UiUtils.getText("attributes.header"));
        contentLayout.addComponent(attributesLabel);
        contentLayout.setComponentAlignment(attributesLabel, Alignment.TOP_LEFT);

        attributesLayout = new CssLayout();

        businessTypeOptionGroup = new OptionGroup();
        businessTypeOptionGroup.setId("businessTypeOptionGroup");
        businessTypeOptionGroup.setCaption(UiUtils.getText("business.type"));
        businessTypeOptionGroup.setRequired(true);
        businessTypeOptionGroup.setRequiredError(UiUtils.getText("attribute.assignment.warning.selectAttribute"));

        businessTypeOptionGroupLayout = new CssLayout();
        boolean groupOptionEnabled = presenter.isGroupOptionEnabled();
        StringBuilder descriptionString = new StringBuilder();

        if (groupOptionEnabled) {
            businessTypeOptionGroup.addItem(AnalyticalMarketSegmentAttribute.GROUP);
            businessTypeOptionGroup.setItemCaption(AnalyticalMarketSegmentAttribute.GROUP, UiUtils.getText("group"));
            descriptionString.append(TOOLTIP_CLASS_WITH_BOLD_TEXT).append(UiUtils.getText("group")).append(CLOSING_BOLD_TAG).append(UiUtils.getText("attribute.group.business.description")).append("</p>");
        }
        businessTypeOptionGroupLayout.addComponent(businessTypeOptionGroup);
        if (amsView == AmsView.AMS_VIEW_GROUP) {
            businessTypeOptionGroupLayout.addComponent(new TetrisHRule());
        }
        attributesLayout.addComponent(businessTypeOptionGroupLayout);
        if (amsView != AmsView.AMS_VIEW_GROUP) {

            businessTypeOptionGroup.addItem(UNQUALIFIED);
            businessTypeOptionGroup.setItemCaption(UNQUALIFIED, UiUtils.getText("unqualified"));
            descriptionString.append(TOOLTIP_CLASS_WITH_BOLD_TEXT).append(UiUtils.getText("unqualified")).append(CLOSING_BOLD_TAG).append(UiUtils.getText("attribute.unqualified.description")).append("</p>");

            businessTypeOptionGroup.addItem(QUALIFIED);
            businessTypeOptionGroup.setItemCaption(QUALIFIED, UiUtils.getText("qualified.nonblock.caption"));
            descriptionString.append(TOOLTIP_CLASS_WITH_BOLD_TEXT).append(UiUtils.getText("qualified")).append(CLOSING_BOLD_TAG).append(UiUtils.getText("attribute.qualified.description")).append("</p>");

            if (groupOptionEnabled) {
                businessTypeOptionGroup.addItem(TRANSIENT_BLOCK);
                businessTypeOptionGroup.setItemCaption(TRANSIENT_BLOCK, UiUtils.getText("common.transient.block"));
                descriptionString.append(TOOLTIP_CLASS_WITH_BOLD_TEXT).append(UiUtils.getText("common.transient.block")).append(CLOSING_BOLD_TAG).append(UiUtils.getText("attribute.transient.description"));
            }

            businessTypeOptionGroup.setDescription(descriptionString.toString(), ContentMode.HTML);

            attributesLayout.addComponent(new TetrisHRule());

            addUnqualifiedOptionGroup();
            addUnqualifiedOptionGroupWithIndependentProducts();
            attributesLayout.addComponent(new TetrisHRule());
            addLinkedOptionGroup();
            addLinkedOptionGroupWithIndependentProducts();
            attributesLayout.addComponent(new TetrisHRule());
            addYieldableOptionGroup();
            addComplimentaryCheckBox(presenter.isComplimentaryAttributeEnabled());

            linkedOptionGroup.addValueChangeListener((Property.ValueChangeListener) event -> {
                Object value = event.getProperty().getValue();
                if (!independentProductsEnabled && value != null) {
                    if (value.equals(LINKED) && yieldableOptionGroup.getValue() != null && yieldableOptionGroup.getValue().equals(AnalyticalMarketSegmentAttribute.MarketSegmentYieldType.NONYIELDABLE)) {
                        yieldableOptionGroup.setValue(null);
                    }
                }
            });

            linkedOptionGroupWithIndependentProducts.onValueChange(value -> {
                if (independentProductsEnabled && value != null) {
                    if (linkedOptionGroupWithIndependentProducts.isLinked() && yieldableOptionGroup.getValue() != null && yieldableOptionGroup.getValue().equals(AnalyticalMarketSegmentAttribute.MarketSegmentYieldType.NONYIELDABLE)) {
                        yieldableOptionGroup.setValue(null);
                    }
                }
            });
        }
        if (this.amsView == AmsView.AMS_VIEW_GROUP) {
            businessTypeOptionGroup.setValue(AnalyticalMarketSegmentAttribute.GROUP);
        }

        rootLayout.addComponent(attributesLayout);

        addForecastTypeOption();
        addFooter(presenter);

        rootLayout.setExpandRatio(attributesLayout, 1);

        contentLayout.addComponent(rootLayout);

        setCompositionRoot(contentLayout);

        businessTypeOptionGroup.addValueChangeListener((Property.ValueChangeListener) event -> {
            Object value = event.getProperty().getValue();
            unQualifiedOptionGroupWithIndependentProducts.setEnabled(value != null && value.equals(UNQUALIFIED));
            unQualifiedOptionGroupWithIndependentProducts.setRequired(value != null && value.equals(UNQUALIFIED));
            linkedOptionGroupWithIndependentProducts.setEnabled(value != null && value.equals(QUALIFIED));
            linkedOptionGroupWithIndependentProducts.setRequired(value != null && value.equals(QUALIFIED));
            yieldableOptionGroup.setEnabled(value != null && value.equals(QUALIFIED));
            yieldableOptionGroup.setRequired(value != null && value.equals(QUALIFIED));
            if (value != null) {
                forecastTypeOption.setItemEnabled(getForecastActivityTypeOptionValueById(ForecastActivityType.NONE), !value.equals(UNQUALIFIED));
                forecastTypeOption.setItemEnabled(getForecastActivityTypeOptionValueById(ForecastActivityType.WASH), !value.equals(UNQUALIFIED));
                unqualifiedOptionGroup.setEnabled(value.equals(UNQUALIFIED));
                unqualifiedOptionGroup.setRequired(value.equals(UNQUALIFIED));
                linkedOptionGroup.setEnabled(value.equals(QUALIFIED));
                linkedOptionGroup.setRequired(value.equals(QUALIFIED));
                complimentaryCheckbox.setValue(false);
                complimentaryCheckbox.setEnabled(!AnalyticalMarketSegmentAttribute.GROUP.equals(value));
                if (!value.equals(QUALIFIED)) {
                    linkedOptionGroup.setValue(null);
                    linkedOptionGroupWithIndependentProducts.reset();
                    yieldableOptionGroup.setValue(null);
                }

                if (!value.equals(UNQUALIFIED)) {
                    unqualifiedOptionGroup.setValue(null);
                    unQualifiedOptionGroupWithIndependentProducts.setValue(null, null);
                } else {
                    forecastTypeOption.setValue(getForecastActivityTypeOptionValueById(ForecastActivityType.DEMAND_AND_WASH));
                }
            }
        });
    }

    private void addLinkedOptionGroupWithIndependentProducts() {
        linkedOptionGroupWithIndependentProducts = new LinkedOptionGroup(presenter.getExistingIndependentProducts());
        linkedOptionGroupWithIndependentProducts.setEnabled(false);
        linkedOptionGroupWithIndependentProducts.setRequired(false);
        attributesLayout.addComponent(linkedOptionGroupWithIndependentProducts);
    }

    private void addUnqualifiedOptionGroupWithIndependentProducts() {
        unQualifiedOptionGroupWithIndependentProducts = new UnQualifiedOptionGroup(presenter.getExistingIndependentProducts(), presenter.getMaxIndependentProducts());
        unQualifiedOptionGroupWithIndependentProducts.setEnabled(false);
        unQualifiedOptionGroupWithIndependentProducts.setRequired(false);
        attributesLayout.addComponent(unQualifiedOptionGroupWithIndependentProducts);
    }


    private void addUnqualifiedOptionGroup() {
        CssLayout unqualifiedOptionGroupLayout = new CssLayout();
        unqualifiedOptionGroup = new OptionGroup();
        unqualifiedOptionGroup.setCaption(UiUtils.getText("unqualified.attribute"));
        unqualifiedOptionGroup.setRequiredError(UiUtils.getText("attribute.assignment.warning.selectAttribute"));

        unqualifiedOptionGroup.setDescription(
                TOOLTIP_CLASS_WITH_BOLD_TEXT + UiUtils.getText("equalToBar") + CLOSING_BOLD_TAG + UiUtils.getText("attribute.equal.to.bar.description") + "</p>" +
                        TOOLTIP_CLASS_WITH_BOLD_TEXT + UiUtils.getText("fenced.caption") + CLOSING_BOLD_TAG + UiUtils.getText("attribute.fenced.description") + "</p>" +
                        TOOLTIP_CLASS_WITH_BOLD_TEXT + UiUtils.getText("packaged") + CLOSING_BOLD_TAG + UiUtils.getText("attribute.packaged.description") + "</p>",
                ContentMode.HTML);
        unqualifiedOptionGroup.setEnabled(false);
        unqualifiedOptionGroup.setRequired(false);
        unqualifiedOptionGroup.addItem(AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR);
        unqualifiedOptionGroup.setItemCaption(AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR, UiUtils.getText("equalToBar"));
        unqualifiedOptionGroup.addItem(AnalyticalMarketSegmentAttribute.FENCED);
        unqualifiedOptionGroup.setItemCaption(AnalyticalMarketSegmentAttribute.FENCED, UiUtils.getText("fenced.caption"));
        unqualifiedOptionGroup.addItem(AnalyticalMarketSegmentAttribute.PACKAGED);
        unqualifiedOptionGroup.setItemCaption(AnalyticalMarketSegmentAttribute.PACKAGED, UiUtils.getText("packaged"));
        unqualifiedOptionGroup.addItem(AnalyticalMarketSegmentAttribute.FENCED_AND_PACKAGED);
        unqualifiedOptionGroup.setItemCaption(AnalyticalMarketSegmentAttribute.FENCED_AND_PACKAGED, UiUtils.getText("fenced.and.packaged.caption"));
        unqualifiedOptionGroup.addItem(AnalyticalMarketSegmentAttribute.UNFENCED_NON_PACKAGED);
        unqualifiedOptionGroup.setItemCaption(AnalyticalMarketSegmentAttribute.UNFENCED_NON_PACKAGED, UiUtils.getText("unfenced.and.nonpackaged"));
        unqualifiedOptionGroupLayout.addComponent(unqualifiedOptionGroup);

        attributesLayout.addComponent(unqualifiedOptionGroupLayout);
    }

    private void addLinkedOptionGroup() {
        CssLayout linkedOptionGroupLayout = new CssLayout();
        linkedOptionGroupLayout.addStyleName("block-option-group");
        linkedOptionGroup = new OptionGroup();
        linkedOptionGroup.setId("linkedOptionGroup");
        linkedOptionGroup.setCaption(UiUtils.getText("linked"));
        linkedOptionGroup.setRequiredError(UiUtils.getText("attribute.assignment.warning.selectAttribute"));
        linkedOptionGroup.setRequired(false);
        linkedOptionGroup.setEnabled(false);

        setLinkedOptionGroupItems();

        linkedOptionGroupLayout.addComponent(linkedOptionGroup);

        attributesLayout.addComponent(linkedOptionGroupLayout);
    }

    public void setLinkedOptionGroupItems() {
        linkedOptionGroup.addItem(LINKED);
        linkedOptionGroup.setItemCaption(LINKED, UiUtils.getText("linked.to.BAR.caption"));

        linkedOptionGroup.addItem(NON_LINKED);
        linkedOptionGroup.setItemCaption(NON_LINKED, UiUtils.getText("nonlinked.caption"));

        linkedOptionGroup.setDescription(
                TOOLTIP_CLASS_WITH_BOLD_TEXT + UiUtils.getText("linked.to.BAR.caption") + CLOSING_BOLD_TAG + UiUtils.getText("attribute.linked.to.bar.description") + "</p>" +
                        TOOLTIP_CLASS_WITH_BOLD_TEXT + UiUtils.getText("nonlinked.caption") + CLOSING_BOLD_TAG + UiUtils.getText("attribute.non.linked.description")
                , ContentMode.HTML);
    }

    private void addYieldableOptionGroup() {
        CssLayout yieldableOptionGroupLayout = new CssLayout();
        yieldableOptionGroupLayout.addStyleName("block-option-group");
        yieldableOptionGroup = new OptionGroup();
        yieldableOptionGroup.setId("yieldableOptionGroup");
        yieldableOptionGroup.setCaption(UiUtils.getText("yieldable"));
        yieldableOptionGroup.setEnabled(false);
        yieldableOptionGroup.setRequiredError(UiUtils.getText("attribute.assignment.warning.selectAttribute"));
        yieldableOptionGroup.addItem(AnalyticalMarketSegmentAttribute.MarketSegmentYieldType.YIELDABLE);
        yieldableOptionGroup.setItemCaption(AnalyticalMarketSegmentAttribute.MarketSegmentYieldType.YIELDABLE, UiUtils.getText("yieldable"));
        yieldableOptionGroup.addItem(AnalyticalMarketSegmentAttribute.MarketSegmentYieldType.SEMIYIELDABLE);
        yieldableOptionGroup.setItemCaption(AnalyticalMarketSegmentAttribute.MarketSegmentYieldType.SEMIYIELDABLE, UiUtils.getText("semi.yieldable.caption"));
        yieldableOptionGroup.addItem(AnalyticalMarketSegmentAttribute.MarketSegmentYieldType.NONYIELDABLE);
        yieldableOptionGroup.setItemCaption(AnalyticalMarketSegmentAttribute.MarketSegmentYieldType.NONYIELDABLE, UiUtils.getText("nonyieldable.caption"));
        yieldableOptionGroup.setDescription(
                TOOLTIP_CLASS_WITH_BOLD_TEXT + UiUtils.getText("yieldable") + CLOSING_BOLD_TAG + UiUtils.getText("attribute.yieldable.description") + "</p>" +
                        TOOLTIP_CLASS_WITH_BOLD_TEXT + UiUtils.getText("semi.yieldable.caption") + CLOSING_BOLD_TAG + UiUtils.getText("attribute.semi.yieldable.description") + "</p>" +
                        TOOLTIP_CLASS_WITH_BOLD_TEXT + UiUtils.getText("nonyieldable.caption") + CLOSING_BOLD_TAG + UiUtils.getText("attribute.non.yieldable.description")
                , ContentMode.HTML);

        yieldableOptionGroup.setInvalidAllowed(false);
        yieldableOptionGroupLayout.addComponent(yieldableOptionGroup);

        attributesLayout.addComponent(yieldableOptionGroupLayout);
        attributesLayout.addComponent(new TetrisHRule());
    }

    private void addForecastTypeOption() {
        forecastTypeOption = new TetrisOptionGroup(UiUtils.getText("forecastType"), forecastActivityTypes);
        forecastTypeOption.setRequiredError(UiUtils.getText("attribute.assignment.warning.selectAttribute"));
        forecastTypeOption.setRequired(true);
        forecastTypeOption.setItemCaptionGenerator((source, itemId) -> ((ForecastActivityType) itemId).getName());
        rootLayout.addComponent(forecastTypeOption);
    }

    private void addFooter(AttributeAssignmentComponentAware presenter) {
        CssLayout footer = new CssLayout();
        assignBtn = new TetrisButton(UiUtils.getText("common.assign"));
        assignBtn.setIsPrimary(true);
        assignBtn.setId("MarketSegmentAssignButton");
        assignBtn.addStyleName("assignBtn");
        assignBtn.setWidth(100, Unit.PERCENTAGE);
        assignBtn.addClickListener((Button.ClickListener) event -> onAssignButtonClicked(presenter));
        // Vaadin doesn't currently support putting the icon on the right of the label, so it's added through css.
        //assignBtn.setIcon(FontAwesome.ARROW_RIGHT);
        footer.addComponent(assignBtn);
        rootLayout.addComponent(footer);
    }

    private void onAssignButtonClicked(AttributeAssignmentComponentAware presenter) {
        if (hasUncommittedForecastGroups()) {
            TetrisNotification.showWarningMessage(UiUtils.getText("marketSegment.blockAttribution"));
        } else if (isValid()) {
            List<String> warningMessages = getWarningMessages(presenter);

            if (!warningMessages.isEmpty()) {
                handleWarningMessage(presenter, warningMessages);
            } else {
                fireEvent(new AssignedAttributeEvent(AttributeAssignmentComponent.this, getValue(), BOOKING_BLOCK_PCT_IS_BLOCK));
            }

        } else {
            TetrisNotification.showWarningMessage(UiUtils.getText("attribute.assignment.warning"));
        }
    }

    @VisibleForTesting
    List<String> getWarningMessages(AttributeAssignmentComponentAware presenter) {
        List<String> warningMessages = new ArrayList<>();
        if (isEqualToBar()) {
            warningMessages.add(UiUtils.getText("attribute.assignment.warning.equalToBarSelected"));
        }

        if (aboutToDeleteWashData(presenter)) {
            warningMessages.add(UiUtils.getText("wash.forecast.delete.warning"));
        }

        if (hasLowVolumeFlag(presenter)) {
            warningMessages.add(presenter.getLowVolumeWarningMessage());
        }

        if (agileProductRateCodesAffected(AGILE_PRODUCT_CODE)) {
            warningMessages.add(UiUtils.getText("attribute.assignment.warning.agileProductRateCodesAffected", getMarketSegmentRateCodesThatAreInUse()));
        }

        if (rateProtectProductRateCodesAffected(RATE_PROTECT_PRODUCT_CODE)) {
            warningMessages.add(UiUtils.getText("attribute.assignment.warning.rateProtectProductRateCodesAffected", getMarketSegmentRateCodesThatAreInUseForRateProtect()));
        }


        if (isMarketSegmentTransitioningFromUnqualifiedOrQualifiedLinked()) {
            warningMessages.add(UiUtils.getText("attribute.assignment.warning.straightMarketSegment"));
        }

        if (!presenter.isGroupOptionEnabled() && isGroupAttribution(presenter)) {
            warningMessages.add(UiUtils.getText("attribute.assignment.warning.splitMarketSegmentWithGroup"));
        }

        if (presenter.isComplimentaryAttributeEnabled() && !presenter.isSeparableIdenticalMsAttributesEnabled()) {
            String warningDetails = presenter.getWarningMessageWhenComplimentaryValueDifferentThanPersistedAMS(isComplimentary());
            if (StringUtils.isNotEmpty(warningDetails)) {
                warningMessages.add(warningDetails);
            }
        }

        return warningMessages;
    }

    private boolean isEqualToBar() {
        if (amsView == AmsView.AMS_VIEW_GROUP) {
            return false; // Group Level does not have Equal To BAR option
        }
        AnalyticalMarketSegmentAttribute selectedAttribute = independentProductsEnabled ?
                unQualifiedOptionGroupWithIndependentProducts.getValue() : (AnalyticalMarketSegmentAttribute) unqualifiedOptionGroup.getValue();
        return isUnqualified() && AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR.equals(selectedAttribute);
    }

    private boolean isMarketSegmentTransitioningFromUnqualifiedOrQualifiedLinked() {
        return isStraightMarketSegment() && !newSelectionIsQualifiedLinkedOrUnqualified() && isOriginalSelectionQualifiedLinkedOrUnqualified();
    }

    private boolean isOriginalSelectionQualifiedLinkedOrUnqualified() {
        return isOriginalSelectionQualifiedLinked() || isOriginalSelectionUnqualified();
    }

    private boolean isOriginalSelectionQualifiedOrUnqualified() {
        return isOriginalSelectionUnqualified() || isOriginalSelectionQualified();
    }

    private boolean isOriginalSelectionUnqualified() {
        return originalAttributeDescription.contains(UNQUALIFIED);
    }

    private boolean isOriginalSelectionQualifiedLinked() {
        return originalAttributeDescription.contains(LINKED);
    }

    private boolean isOriginalSelectionQualified() {
        return originalAttributeDescription.contains(QUALIFIED);
    }

    boolean isStraightMarketSegment() {
        if (presenter.isAgileRatesEnabled()) {
            return marketSegmentRateCodes.isEmpty();
        } else {
            return false;
        }
    }

    private Collection getMarketSegmentRateCodesThatAreInUse() {
        return CollectionUtils.intersection(marketSegmentRateCodes, getAllAssignedProductRateCodes());
    }

    private Collection getMarketSegmentRateCodesThatAreInUseForRateProtect() {
        return CollectionUtils.intersection(marketSegmentRateCodes, getAllAssignedProductRateCodesForRateProtect());
    }

    private List<String> getAllAssignedProductRateCodes() {
        List<String> assignedProductRateCodes = presenter.getAllAssignedProductRateCodes();
        if (assignedProductRateCodes == null) {
            return Collections.emptyList();
        } else {
            return assignedProductRateCodes;
        }
    }

    private List<String> getAllAssignedProductRateCodesForRateProtect() {
        List<String> assignedProductRateCodesForRateProtect = presenter.getAllAssignedProductRateCodesForRateProtect();
        if (assignedProductRateCodesForRateProtect == null) {
            return Collections.emptyList();
        } else {
            return assignedProductRateCodesForRateProtect;
        }
    }

    private List<String> trimBlankMarketRateCodes(List<String> marketRateCodes) {
        //For some reason, a blank entry is found for the rate types, so need to trim.
        return marketRateCodes.stream()
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());
    }

    private void handleWarningMessage(AttributeAssignmentComponentAware presenter, List<String> warningMessages) {
        StringBuilder sb = new StringBuilder();
        for (String warningMessage : warningMessages) {
            sb.append(warningMessage).append("\r\n\n");
        }
        sb.append(UiUtils.getText("attribute.assignment.warning.continue"));
        MessageBox confirmationTitle = TetrisNotification.showAlert(UiUtils.getText("CONFIRMATION_TITLE"), sb.toString(), true, new MessageBoxListener() {
            @Override
            public void buttonClicked(ButtonId buttonId) {
                if (ButtonId.OK.equals(buttonId)) {
                    if (agileProductRateCodesAffected(AGILE_PRODUCT_CODE) && noActiveForecastGroupExists()) {
                        presenter.invalidateAgileProducts(new ArrayList<>(getMarketSegmentRateCodesThatAreNotInUse()));
                    }
                    if (rateProtectProductRateCodesAffected(RATE_PROTECT_PRODUCT_CODE) && noActiveForecastGroupExists()) {
                        presenter.invalidateRateProtectProducts(new ArrayList<>(getMarketSegmentRateCodesThatAreNotInUseForRateProtect()));
                    }
                    fireEvent(new AssignedAttributeEvent(AttributeAssignmentComponent.this, getValue(), BOOKING_BLOCK_PCT_IS_BLOCK, aboutToDeleteWashData(presenter)));
                }
            }
        }).setWidth(50, Unit.PERCENTAGE);
        confirmationTitle.getWindow().setId("ConfirmationPopUpToAssignAttributes");
    }

    private boolean noActiveForecastGroupExists() {
        return !presenter.forecastGroupsExists();
    }

    boolean agileProductRateCodesAffected(String productCode) {
        if (presenter.isAgileRatesEnabled()) {
            return hasAssociatedRateCodes(AGILE_PRODUCT_CODE) && !newSelectionIsQualifiedLinkedOrUnqualified() && isOriginalSelectionQualifiedLinkedOrUnqualified();
        } else {
            return false;
        }
    }

    boolean rateProtectProductRateCodesAffected(String productCode) {
        if (presenter.isRateProtectEnabled()) {
            return hasAssociatedRateCodes(RATE_PROTECT_PRODUCT_CODE) && !newSelectionisQualifiedorUnqualified() && isOriginalSelectionQualifiedOrUnqualified();
        } else {
            return false;
        }
    }

    private boolean hasAssociatedRateCodes(String productCode) {
        if (productCode.equals(AGILE_PRODUCT_CODE)) {
            Collection marketSegmentRateCodesThatAreInUse = getMarketSegmentRateCodesThatAreInUse();
            return CollectionUtils.isNotEmpty(marketSegmentRateCodesThatAreInUse);
        } else {
            Collection marketSegmentRateCodesThatAreInUse = getMarketSegmentRateCodesThatAreInUseForRateProtect();
            return CollectionUtils.isNotEmpty(marketSegmentRateCodesThatAreInUse);
        }
    }

    private Collection getMarketSegmentRateCodesThatAreNotInUse() {
        return CollectionUtils.subtract(marketSegmentRateCodes, getAllAssignedProductRateCodes());
    }

    private Collection getMarketSegmentRateCodesThatAreNotInUseForRateProtect() {
        return CollectionUtils.subtract(marketSegmentRateCodes, getAllAssignedProductRateCodesForRateProtect());
    }

    private boolean newSelectionIsQualifiedLinkedOrUnqualified() {
        return isUnqualified() || isQualifiedLinked();
    }

    private boolean newSelectionisQualifiedorUnqualified() {
        return isUnqualified() || isQualified();
    }

    private boolean isUnqualified() {
        return UNQUALIFIED.equals(getBusinessTypeOptionGroupValue());
    }

    private boolean isQualifiedLinked() {
        return QUALIFIED.equals(getBusinessTypeOptionGroupValue()) && LINKED.equals(getLinkedOptionGroupValue());
    }

    private boolean isQualified() {
        return QUALIFIED.equals(getBusinessTypeOptionGroupValue());
    }

    private String getBusinessTypeOptionGroupValue() {
        Object value = businessTypeOptionGroup.getValue();
        //For some reason, the group option from the option group comes in as an AnalyticalMarketSegmentAttribute
        //instead of a string. This check will prevent a class cast exception for the group option.
        if (value instanceof AnalyticalMarketSegmentAttribute) {
            return ((AnalyticalMarketSegmentAttribute) value).getBusinessType().getName();
        } else {
            return (String) value;
        }
    }

    private String getLinkedOptionGroupValue() {
        return !independentProductsEnabled ? (String) linkedOptionGroup.getValue() : linkedOptionGroupWithIndependentProducts.getValue().getName();
    }

    private boolean hasLowVolumeFlag(AttributeAssignmentComponentAware presenter) {
        return presenter.selectedMarketSegmentsContainLowVolume();
    }

    private boolean isGroupAttribution(AttributeAssignmentComponentAware presenter) {
        return presenter.isGroupAttribution(getValue());
    }

    private boolean aboutToDeleteWashData(AttributeAssignmentComponentAware presenter) {
        return StringUtils.equalsIgnoreCase(getForecastTypeOptionValue().getName(), "None") && presenter.isWashDataPresentFor(mktSegCode);
    }

    private boolean hasUncommittedForecastGroups() {
        return presenter.hasUncommittedForecastGroup();
    }

    public void setValue(AnalyticalMarketSegmentAttribute attribute, boolean isEditable) {
        setValue(attribute);
        businessTypeOptionGroupLayout.setEnabled(isEditable);
        attributesLayout.setEnabled(isEditable);
    }

    public void setValue(AnalyticalMarketSegmentAttribute attribute) {
        if (AnalyticalMarketSegmentAttribute.GROUP.equals(attribute)) {
            businessTypeOptionGroup.setValue(AnalyticalMarketSegmentAttribute.GROUP);
        } else if (!attribute.getQualified()) {
            businessTypeOptionGroup.setValue(UNQUALIFIED);
            unqualifiedOptionGroup.setValue(attribute);
            unQualifiedOptionGroupWithIndependentProducts.setValue(attribute, null);
        } else if (AnalyticalMarketSegmentAttribute.TRANSIENT_BLOCK_LINKED.equals(attribute)
                || AnalyticalMarketSegmentAttribute.TRANSIENT_BLOCK_NON_LINKED.equals(attribute)) {
            businessTypeOptionGroup.setValue(TRANSIENT_BLOCK);
        } else if (attribute.getQualified()) {
            linkedOptionGroup.setValue(attribute.getLinkType().getName());
            linkedOptionGroupWithIndependentProducts.setValue(attribute.getLinkType());
            businessTypeOptionGroup.setValue(QUALIFIED);
            yieldableOptionGroup.setValue(attribute.getYieldType());
        }
    }

    public AnalyticalMarketSegmentAttribute getValue() {
        if (businessTypeOptionGroup.getValue().equals(AnalyticalMarketSegmentAttribute.GROUP)) {
            return AnalyticalMarketSegmentAttribute.GROUP;
        }
        if (businessTypeOptionGroup.getValue().equals(UNQUALIFIED)) {
            return independentProductsEnabled ? unQualifiedOptionGroupWithIndependentProducts.getValue() : (AnalyticalMarketSegmentAttribute) unqualifiedOptionGroup.getValue();
        }

        AnalyticalMarketSegmentAttribute value = null;
        if (businessTypeOptionGroup.getValue().equals(QUALIFIED)) {
            if (independentProductsEnabled ? linkedOptionGroupWithIndependentProducts.isLinked() : linkedOptionGroup.getValue().equals(LINKED)) {
                if (yieldableOptionGroup.getValue().equals(AnalyticalMarketSegmentAttribute.MarketSegmentYieldType.YIELDABLE)) {
                    value = AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE;
                } else if (yieldableOptionGroup.getValue().equals(AnalyticalMarketSegmentAttribute.MarketSegmentYieldType.SEMIYIELDABLE)) {
                    value = AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_SEMIYIELDABLE;
                } else {
                    value = AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_NONYIELDABLE;
                }
            } else if (independentProductsEnabled ? !linkedOptionGroupWithIndependentProducts.isLinked() : linkedOptionGroup.getValue().equals(NON_LINKED)) {
                if (yieldableOptionGroup.getValue().equals(AnalyticalMarketSegmentAttribute.MarketSegmentYieldType.YIELDABLE)) {
                    value = AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE;
                } else if (yieldableOptionGroup.getValue().equals(AnalyticalMarketSegmentAttribute.MarketSegmentYieldType.SEMIYIELDABLE)) {
                    value = AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_NOTLINKED_SEMIYIELDABLE;
                } else {
                    value = AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_NOTLINKED_NONYIELDABLE;
                }
            }
        } else if (businessTypeOptionGroup.getValue().equals(TRANSIENT_BLOCK)) {
            value = AnalyticalMarketSegmentAttribute.TRANSIENT_BLOCK_NON_LINKED;
        }
        return value;
    }

    public boolean isComplimentary() {
        return complimentaryCheckbox.getValue();
    }

    public void setComplimentary(boolean complimentary) {
        complimentaryCheckbox.setValue(complimentary);
    }


    private boolean isValid() {
        return businessTypeOptionGroup.isValid() &&
                (independentProductsEnabled || unqualifiedOptionGroup == null || unqualifiedOptionGroup.isValid()) &&
                (!independentProductsEnabled || unQualifiedOptionGroupWithIndependentProducts == null || unQualifiedOptionGroupWithIndependentProducts.isValid()) &&
                (independentProductsEnabled || linkedOptionGroup == null || linkedOptionGroup.isValid()) &&
                (!independentProductsEnabled || linkedOptionGroupWithIndependentProducts == null || linkedOptionGroupWithIndependentProducts.isValid()) &&
                (yieldableOptionGroup == null || yieldableOptionGroup.isValid()) &&
                forecastTypeOption.isValid() &&
                !presenter.isSelectionEmpty();
    }

    public void reset() {
        //only reset business type in non group mode
        if (amsView != AmsView.AMS_VIEW_GROUP) {
            businessTypeOptionGroup.setValue(null);
        }
        unqualifiedOptionGroup.setValue(null);
        unQualifiedOptionGroupWithIndependentProducts.setValue(null, null);
        List<Product> existingIndependentProducts = presenter.getExistingIndependentProducts();
        unQualifiedOptionGroupWithIndependentProducts
                .setMaxProducts(presenter.getMaxIndependentProducts())
                .setProducts(existingIndependentProducts);
        linkedOptionGroupWithIndependentProducts.setProducts(existingIndependentProducts);
        yieldableOptionGroup.setValue(null);
        linkedOptionGroup.setValue(null);
        linkedOptionGroupWithIndependentProducts.setValue(null);
        forecastTypeOption.setValue(null);
        complimentaryCheckbox.setValue(false);
    }

    public void addAdvancedMappingClickListener(AdvancedMappingClickListener advancedMappingClickListener) {
        addListener(AdvancedMappingClickEvent.class, advancedMappingClickListener, AdvancedMappingClickListener.CLICK_METHOD);
    }

    public void updatePermission(String permission) {
        assignBtn.setEnabledRequirements(false, permission);
    }

    public Product getProduct() {
        if (linkedOptionGroupWithIndependentProducts.isEnabled()) {
            return linkedOptionGroupWithIndependentProducts.linkedTo();
        }
        return unQualifiedOptionGroupWithIndependentProducts.getSelectedProduct();
    }

    public boolean shouldCreateNewProduct() {
        return unQualifiedOptionGroupWithIndependentProducts.shouldCreateNewProduct();
    }

    public interface AdvancedMappingClickListener {
        public static final Method CLICK_METHOD = ReflectTools.findMethod(AdvancedMappingClickListener.class, "onAdvancedMappingClick", AdvancedMappingClickEvent.class);

        public void onAdvancedMappingClick(AdvancedMappingClickEvent event);
    }

    public static class AdvancedMappingClickEvent extends Event {
        public AdvancedMappingClickEvent(Component source) {
            super(source);
        }
    }

    public void addAssignedAttributeListener(AssignedAttributeListener assignedAttributeListener) {
        addListener(AssignedAttributeEvent.class, assignedAttributeListener, AssignedAttributeListener.ATTRIBUTE_CHANGED_METHOD);
    }

    public interface AssignedAttributeListener {
        public static final Method ATTRIBUTE_CHANGED_METHOD = ReflectTools.findMethod(AssignedAttributeListener.class, "onAttributeAssigned", AssignedAttributeEvent.class);

        public void onAttributeAssigned(AssignedAttributeEvent event);
    }

    public class AssignedAttributeEvent extends Event {
        private int blockPercent;
        private AnalyticalMarketSegmentAttribute attribute;
        private boolean wantToDeleteWashData;

        public AssignedAttributeEvent(Component source, AnalyticalMarketSegmentAttribute attribute, int blockPercent, boolean wantToDeleteWashData) {
            super(source);
            this.attribute = attribute;
            this.blockPercent = blockPercent;
            this.wantToDeleteWashData = wantToDeleteWashData;
        }

        public AssignedAttributeEvent(Component source, AnalyticalMarketSegmentAttribute attribute, int blockPercent) {
            super(source);
            this.attribute = attribute;
            this.blockPercent = blockPercent;
            this.wantToDeleteWashData = false;
        }

        public AnalyticalMarketSegmentAttribute getAttribute() {
            return attribute;
        }

        public int getBlockPercent() {
            return blockPercent;
        }

        public boolean wantsToDeleteWashData() {
            return wantToDeleteWashData;
        }
    }

    public void showAdvanceMappingPopup() {
        fireEvent(new AdvancedMappingClickEvent(this));
    }

    public void setAssignButtonCaption(String caption) {
        assignBtn.setCaption(caption);
    }

    public ForecastActivityType getForecastTypeOptionValue() {
        return (ForecastActivityType) forecastTypeOption.getValue();
    }

    public void setForecastTypeOptionValue(ForecastActivityType forecastTypeOptionValue) {
        this.forecastTypeOption.setValue(forecastTypeOptionValue);
    }

    public void setIndependentProductsEnabled(boolean independentProductsEnabled) {
        this.independentProductsEnabled = independentProductsEnabled;
        unqualifiedOptionGroup.setVisible(!independentProductsEnabled);
        unQualifiedOptionGroupWithIndependentProducts.setVisible(independentProductsEnabled);
        linkedOptionGroup.setVisible(!independentProductsEnabled);
        linkedOptionGroupWithIndependentProducts.setVisible(independentProductsEnabled);
    }

    private ForecastActivityType getForecastActivityTypeOptionValueById(Integer id) {
        for (ForecastActivityType forecastActivityType : forecastActivityTypes) {
            if (id.equals(forecastActivityType.getId())) {
                return forecastActivityType;
            }
        }
        return null;
    }

    OptionGroup getBusinessTypeOptionGroup() {
        return businessTypeOptionGroup;
    }

    OptionGroup getLinkedOptionGroup() {
        return linkedOptionGroup;
    }

    private void addComplimentaryCheckBox(boolean complementaryAttributeAssignmentEnabled) {
        complimentaryCheckbox.setCaption(UiUtils.getText("complimentary"));
        complimentaryCheckbox.setId("ComplimentaryCheckbox");
        complimentaryCheckbox.setVisible(complementaryAttributeAssignmentEnabled);
        if (complementaryAttributeAssignmentEnabled) {
            attributesLayout.addComponent(complimentaryCheckbox);
            attributesLayout.addComponent(new TetrisHRule());
        }
    }
}
