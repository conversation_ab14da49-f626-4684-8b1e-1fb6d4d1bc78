package com.ideas.tetris.ui.modules.marketsegment.common.view;

import com.ideas.tetris.pacman.services.marketsegment.service.AnalyticalMarketSegmentAttribute.MarketSegmentYieldType;
import com.ideas.tetris.ui.common.util.UiUtils;

import java.util.Arrays;
import java.util.Collection;

import static com.ideas.tetris.pacman.services.marketsegment.service.AnalyticalMarketSegmentAttribute.MarketSegmentYieldType.NONYIELDABLE;
import static com.ideas.tetris.pacman.services.marketsegment.service.AnalyticalMarketSegmentAttribute.MarketSegmentYieldType.SEMIYIELDABLE;
import static com.ideas.tetris.pacman.services.marketsegment.service.AnalyticalMarketSegmentAttribute.MarketSegmentYieldType.YIELDABLE;

public class YieldableRadioButtonGroup extends MarketSegmentAttributionRadioButtonGroup<MarketSegmentYieldType> {

    public YieldableRadioButtonGroup(AttributeAssignmentConfig config) {
        super("YieldableRadioButtonGroup", UiUtils.getText("yieldable"), config);
    }

    @Override
    protected Collection<MarketSegmentYieldType> getItems() {
        return Arrays.asList(YIELDABLE, SEMIYIELDABLE, NONYIELDABLE);
    }

    @Override
    public String getDescription() {
        return DescriptionBuilder.buildForYieldableAttributes();
    }

    protected String getCaption(MarketSegmentYieldType marketSegmentYieldType) {
        switch (marketSegmentYieldType) {
            case YIELDABLE:
                return UiUtils.getText("yieldable");
            case SEMIYIELDABLE:
                return UiUtils.getText("semi.yieldable.caption");
            case NONYIELDABLE:
                return UiUtils.getText("nonyieldable.caption");
        }
        return null;
    }
}
