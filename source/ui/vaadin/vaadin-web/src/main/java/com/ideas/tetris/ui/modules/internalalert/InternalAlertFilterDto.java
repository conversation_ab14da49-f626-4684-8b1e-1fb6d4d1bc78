package com.ideas.tetris.ui.modules.internalalert;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.ideas.tetris.platform.services.daoandentities.entity.Client;
import com.ideas.tetris.platform.services.daoandentities.entity.Property;
import org.joda.time.LocalDateTime;

import java.util.Set;

public class InternalAlertFilterDto implements Cloneable {

    private LocalDateTime startDate;
    private LocalDateTime endDate;
    private Set<Integer> internalAlertIds;
    private Set<String> alertTypes;
    private Set<String> alertStatuses;
    private Client client;
    private Property propertyByCode;
    private Property propertyByName;

    public InternalAlertFilterDto() {
    }

    @JsonIgnore
    public LocalDateTime getStartDate() {
        return startDate;
    }

    public void setStartDate(LocalDateTime startDate) {
        this.startDate = startDate;
    }

    @JsonIgnore
    public LocalDateTime getEndDate() {
        return endDate;
    }

    public void setEndDate(LocalDateTime endDate) {
        this.endDate = endDate;
    }

    public Set<Integer> getInternalAlertIds() {
        return internalAlertIds;
    }

    public void setInternalAlertIds(Set<Integer> internalAlertIds) {
        this.internalAlertIds = internalAlertIds;
    }

    public Set<String> getAlertTypes() {
        return alertTypes;
    }

    public void setAlertTypes(Set<String> alertTypes) {
        this.alertTypes = alertTypes;
    }

    public Client getClient() {
        return client;
    }

    public void setClient(Client client) {
        this.client = client;
    }

    public Property getPropertyByCode() {
        return propertyByCode;
    }

    public void setPropertyByCode(Property propertyByCode) {
        this.propertyByCode = propertyByCode;
    }

    public Property getPropertyByName() {
        return propertyByName;
    }

    public void setPropertyByName(Property propertyByName) {
        this.propertyByName = propertyByName;
    }

    public Set<String> getAlertStatuses() {
        return alertStatuses;
    }

    public void setAlertStatuses(Set<String> alertStatuses) {
        this.alertStatuses = alertStatuses;
    }

    @Override
    protected Object clone() throws CloneNotSupportedException {
        return super.clone();
    }

    @JsonIgnore
    public boolean isEmpty() {
        return startDate == null && endDate == null &&
                (internalAlertIds == null || internalAlertIds.isEmpty()) &&
                (alertTypes == null || alertTypes.isEmpty()) && (alertStatuses == null || alertStatuses.isEmpty()) && client == null
                && propertyByCode == null && propertyByName == null;
    }

}
