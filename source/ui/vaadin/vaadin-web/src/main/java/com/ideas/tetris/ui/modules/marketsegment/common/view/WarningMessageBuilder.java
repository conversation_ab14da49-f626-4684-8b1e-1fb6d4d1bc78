package com.ideas.tetris.ui.modules.marketsegment.common.view;

import com.ideas.tetris.ui.modules.marketsegment.common.AttributeAssignmentComponentAware;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Collection;
import java.util.Collections;
import java.util.List;

import static com.ideas.tetris.pacman.common.constants.Constants.AGILE_PRODUCT_CODE;
import static com.ideas.tetris.pacman.common.constants.Constants.RATE_PROTECT_PRODUCT_CODE;
import static com.ideas.tetris.pacman.util.Runner.getOrDefault;
import static com.ideas.tetris.ui.common.util.UiUtils.getText;
import static org.apache.commons.collections.CollectionUtils.isEmpty;

public class WarningMessageBuilder {

    private AttributeAssignmentConfig config;
    private AttributeAssignmentComponentAware helper;

    public WarningMessageBuilder(AttributeAssignmentConfig config, AttributeAssignmentComponentAware helper) {
        this.config = config;
        this.helper = helper;
    }

    public String build(AttributeBean attribute) {
        StringBuilder message = new StringBuilder();
        if (attribute.isEqualTo()) {
            message.append(config.isIndependentProductsEnabled() ? getText("attribute.assignment.warning.independent.products.equalToBarSelected")
                            : getText("attribute.assignment.warning.equalToBarSelected"))
                    .append(lineBreak());
        }
        if (isItOkayToCleanWashData(attribute)) {
            message.append(getText("wash.forecast.delete.warning")).append(lineBreak());
        }

        if (helper.selectedMarketSegmentsContainLowVolume()) {
            message.append(helper.getLowVolumeWarningMessage()).append(lineBreak());
        }

        if (agileProductRateCodesAffected(attribute, AGILE_PRODUCT_CODE)) {
            message.append(getText("attribute.assignment.warning.agileProductRateCodesAffected", config.getOriginallySelectedRateCodes())).append(lineBreak());
        }

        if (rateProtectProductRateCodesAffected(attribute, RATE_PROTECT_PRODUCT_CODE)) {
            message.append(getText("attribute.assignment.warning.rateProtectProductRateCodesAffected", config.getOriginallySelectedRateCodes())).append(lineBreak());
        }

        if (isMarketSegmentTransitioningFromUnqualifiedOrQualified(attribute)) {
            message.append(getText("attribute.assignment.warning.straightMarketSegment")).append(lineBreak());
        }

        if (!helper.isGroupOptionEnabled() && helper.isGroupAttribution(attribute.getAttribute())) {
            message.append(getText("attribute.assignment.warning.splitMarketSegmentWithGroup"));
        }

        if (helper.isComplimentaryAttributeEnabled() && !helper.isSeparableIdenticalMsAttributesEnabled()) {
            String warningDetails = helper.getWarningMessageWhenComplimentaryValueDifferentThanPersistedAMS(attribute.isComplimentary());
            if (StringUtils.isNotEmpty(warningDetails)) {
                message.append(warningDetails);
            }
        }

        if (attribute.getProduct() != null && config.getOriginallySelectedBaseProduct() != null && !config.getOriginallySelectedBaseProduct().isBlank()
                && !config.getOriginallySelectedBaseProduct().equals(attribute.getProduct().getName())) {
            message.append(getText("attribute.assignment.warning.baseProductChange",
                    helper.getImpactedProductsName(config.getOriginallySelectedBaseProduct()))).append(lineBreak());
        }

        return message.length() > 0 ? message.toString() : StringUtils.EMPTY;
    }

    private boolean isMarketSegmentTransitioningFromUnqualifiedOrQualified(AttributeBean attribute) {
        return isStraightMarketSegment() && !attribute.isUnQualifiedOrQualifiedLinked() &&
                config.isOriginallySelectedAttributeUnQualifiedOrQualified();
    }

    boolean isStraightMarketSegment() {
        return helper.isAgileRatesEnabled() && isEmpty(config.getOriginallySelectedRateCodes());
    }

    boolean agileProductRateCodesAffected(AttributeBean attribute, String productCode) {
        return helper.isAgileRatesEnabled() && hasAssociatedRateCodes(productCode) &&
                !attribute.isUnQualifiedOrQualifiedLinked() && config.isOriginallySelectedAttributeUnQualifiedOrQualifiedLinked();
    }

    boolean rateProtectProductRateCodesAffected(AttributeBean attribute, String productCode) {
        return helper.isRateProtectEnabled() && hasAssociatedRateCodes(productCode) &&
                !attribute.isUnQualifiedOrQualified() && config.isOriginallySelectedAttributeUnQualifiedOrQualified();
    }

    private boolean hasAssociatedRateCodes(String productCode) {
        if (productCode.equals(AGILE_PRODUCT_CODE)) {
            Collection marketSegmentRateCodesInUse = getRateCodesThatAreInUse();
            if (!marketSegmentRateCodesInUse.isEmpty()) {
                return true;
            } else {
                return false;
            }
        } else {
            Collection marketSegmentRateCodesInUse = getRateCodesThatAreInUseForRateProtect();
            if (!marketSegmentRateCodesInUse.isEmpty()) {
                return true;
            } else {
                return false;
            }
        }
    }

    private Collection getRateCodesThatAreInUse() {
        return CollectionUtils.intersection(config.getOriginallySelectedRateCodes(), getAllAssignedProductRateCodes());
    }

    private Collection getRateCodesThatAreInUseForRateProtect() {
        return CollectionUtils.intersection(config.getOriginallySelectedRateCodes(), getAllAssignedProductRateCodesForRateProtect());
    }

    private List<String> getAllAssignedProductRateCodes() {
        return getOrDefault(helper::getAllAssignedProductRateCodes, Collections::emptyList);
    }

    private List<String> getAllAssignedProductRateCodesForRateProtect() {
        return getOrDefault(helper::getAllAssignedProductRateCodesForRateProtect, Collections::emptyList);
    }

    private boolean isItOkayToCleanWashData(AttributeBean attribute) {
        return attribute.isNone() && helper.isWashDataPresentFor(config.getMarketSegmentUnderEdit());
    }

    private String lineBreak() {
        return "\r\n\n";
    }
}
