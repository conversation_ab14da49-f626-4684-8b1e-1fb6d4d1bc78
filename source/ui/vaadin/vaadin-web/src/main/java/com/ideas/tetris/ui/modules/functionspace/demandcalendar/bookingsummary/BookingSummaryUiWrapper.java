package com.ideas.tetris.ui.modules.functionspace.demandcalendar.bookingsummary;

import com.ideas.tetris.pacman.services.functionspace.configuration.entity.FunctionSpaceDayPart;
import com.ideas.tetris.pacman.services.functionspace.dto.FunctionSpaceDemandCalendarSummaryDto;
import com.ideas.tetris.ui.common.data.util.converter.ScaleAwareStringToBigDecimalConverter;
import com.ideas.tetris.ui.common.util.TimeFormatUtil;
import com.ideas.tetris.ui.common.util.UiUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

public class BookingSummaryUiWrapper {
    List<RowData> dayParts;
    List<RowData> monthSummary;
    List<RowData> monthToDateAverage;

    public BookingSummaryUiWrapper() {
    }

    public BookingSummaryUiWrapper(FunctionSpaceDemandCalendarSummaryDto dto) {
        dayParts = new ArrayList<RowData>();
        for (FunctionSpaceDayPart functionSpaceDayPart : dto.getFunctionSpaceDayParts()) {
            if (functionSpaceDayPart.isIncluded()) {
                dayParts.add(new RowData(functionSpaceDayPart.getName(), TimeFormatUtil.format(functionSpaceDayPart.getBeginTime())));
            }
        }
        monthToDateAverage = new ArrayList<RowData>();
        monthToDateAverage.add(new RowData(UiUtils.getText("groupPricing.forecastReview.utilization"), getPlanTextString(dto.getAvgUtilization()) + " %"));
        monthToDateAverage.add(new RowData(UiUtils.getText("groupPricing.demandCalendar.revPost"), getPlanTextString(dto.getAvgRevPost())));
        monthToDateAverage.add(new RowData(UiUtils.getText("groupPricing.demandCalendar.revPast"), getPlanTextString(dto.getAvgRevPast())));
        monthToDateAverage.add(new RowData(UiUtils.getText("groupPricing.demandCalendar.proPost"), getPlanTextString(dto.getAvgProPost())));
        monthToDateAverage.add(new RowData(UiUtils.getText("groupPricing.demandCalendar.proPast"), getPlanTextString(dto.getAvgProPast())));


        monthSummary = new ArrayList<RowData>();
        monthSummary.add(new RowData(UiUtils.getText("groupEvaluation.utilization.forecast"), getPlanTextString(dto.getAvgUtilizationForecast()) + " %"));
        monthSummary.add(new RowData(UiUtils.getText("groupPricing.demandCalendar.UtilizationOnbooks"), getPlanTextString(dto.getAvgUtilizationOTBs()) + " %"));
        monthSummary.add(new RowData(UiUtils.getText("OPEN"), dto.getOpen().toString()));
        monthSummary.add(new RowData(UiUtils.getText("CLOSED"), dto.getClosed().toString()));
        monthSummary.add(new RowData(UiUtils.getText("EVALUATE"), dto.getEvaluate().toString()));
    }

    private String getPlanTextString(BigDecimal value) {
        if (value != null) {
            return new ScaleAwareStringToBigDecimalConverter().convertToPresentation(value, String.class, UiUtils.getLocale());
        }
        return "";
    }

    public List<RowData> getDayParts() {
        return dayParts;
    }

    public void setDayParts(List<RowData> dayParts) {
        this.dayParts = dayParts;
    }

    public List<RowData> getMonthSummary() {
        return monthSummary;
    }

    public void setMonthSummary(List<RowData> monthSummary) {
        this.monthSummary = monthSummary;
    }

    public List<RowData> getMonthToDateAverage() {
        return monthToDateAverage;
    }

    public void setMonthToDateAverage(List<RowData> monthToDateAverage) {
        this.monthToDateAverage = monthToDateAverage;
    }
}

