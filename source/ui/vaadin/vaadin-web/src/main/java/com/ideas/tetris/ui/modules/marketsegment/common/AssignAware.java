package com.ideas.tetris.ui.modules.marketsegment.common;

import com.ideas.tetris.pacman.services.marketsegment.entity.ForecastActivityType;
import com.ideas.tetris.pacman.services.marketsegment.service.RateCodeSummary;

import java.util.List;

import org.springframework.aop.SpringProxy;
public interface AssignAware extends SpringProxy {
    void assignMarketSegment(AttributeDetails attributeDetails, int block);

    void resetData();

    void reload();

    boolean hasUncommittedForecastGroup();

    List<RateCodeSummary> getRateCodes(String marketCode);

    public List<ForecastActivityType> getForecastActivityTypes();
}
