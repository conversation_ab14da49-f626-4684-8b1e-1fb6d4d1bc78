package com.ideas.tetris.ui.modules.functionspace.configuration.conferencebanquet;


import com.ideas.infra.tetris.security.domain.TetrisPermissionKey;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.TenantStatusEnum;
import com.ideas.tetris.ui.common.cdi.TetrisView;
import com.ideas.tetris.ui.common.component.TetrisBeanItemContainer;
import com.ideas.tetris.ui.common.component.TetrisSaveCancelButtonBar;
import com.ideas.tetris.ui.common.component.button.TetrisImageButton;
import com.ideas.tetris.ui.common.component.fontawesome.TetrisFontAwesome;
import com.ideas.tetris.ui.common.component.table.TetrisChangeAwareComponentColumnGenerator;
import com.ideas.tetris.ui.common.component.table.TetrisChangeAwareTable;
import com.ideas.tetris.ui.common.component.table.TetrisRadioButtonColumnGenerator;
import com.ideas.tetris.ui.common.component.textfield.TetrisBigDecimalField;
import com.ideas.tetris.ui.common.util.ChangeAware;
import com.ideas.tetris.ui.common.util.HelpId;
import com.ideas.tetris.ui.common.util.UiUtils;
import com.vaadin.annotations.Title;
import com.vaadin.ui.Button;
import com.vaadin.v7.data.validator.RangeValidator;
import com.vaadin.v7.ui.Table;
import com.vaadin.v7.ui.VerticalLayout;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import static java.lang.Math.min;

@Title("Conference and Banquet")
@HelpId("1124")
public class FunctionSpaceConferenceBanquetView extends TetrisView<FunctionSpaceConferenceBanquetPresenter, Boolean> {

    private TetrisChangeAwareTable table;
    private TetrisBeanItemContainer<FunctionSpaceConferenceBanquetUiWrapper> conferenceBanquetContainer;
    private TetrisSaveCancelButtonBar saveCancelButtonBar;

    @Override
    protected void initView() {
        setSizeFull();
    }

    @Override
    public void onPresenterInit() {
        intializeView();
    }

    private void intializeView() {
        VerticalLayout layout = new VerticalLayout();
        layout.setSizeFull();
        layout.setMargin(true);
        layout.setSpacing(true);

        addRevenueTable(layout);
        addSaveCancelButtonBar(layout);

        table.setSaveCancelButtonBar(saveCancelButtonBar);

        setCompositionRoot(layout);
    }

    private void addSaveCancelButtonBar(VerticalLayout layout) {
        saveCancelButtonBar = new TetrisSaveCancelButtonBar();
        saveCancelButtonBar.getSaveButton().setEnabledRequirements(true, TetrisPermissionKey.FUNCTION_SPACE_CONFIGURATION_CONFERENCE_BANQUET);

        saveCancelButtonBar.addPreValidationListener(new TetrisSaveCancelButtonBar.PreValidationListener() {
            @Override
            public void onPreValidation(TetrisSaveCancelButtonBar.PreValidationEvent event) {
                //only
                ArrayList<FunctionSpaceConferenceBanquetUiWrapper> checkedIsRentalList = new ArrayList<FunctionSpaceConferenceBanquetUiWrapper>();
                List<FunctionSpaceConferenceBanquetUiWrapper> items = conferenceBanquetContainer.getItemsExcludingNoDataFoundItem();
                for (FunctionSpaceConferenceBanquetUiWrapper item : items) {
                    if (item.getIsRental()) {
                        checkedIsRentalList.add(item);
                    }
                }

                if (checkedIsRentalList.size() == 0 || checkedIsRentalList.size() > 1) {
                    event.cancel();
                    if (checkedIsRentalList.size() == 0) {
                        showWarning(UiUtils.getText("groupEvaluation.warning.atLeastOneRevenueStreamMarkedAsRental"));
                    } else {
                        showWarning(UiUtils.getText("groupEvaluation.warning.onlyOneRevenueStreamMarkedRental"));
                    }
                }
            }
        });

        saveCancelButtonBar.addValidSaveListener(new TetrisSaveCancelButtonBar.ValidSaveListener() {
            @Override
            public void onValidSave(TetrisSaveCancelButtonBar.ValidSaveEvent event) {
                presenter.save();
            }
        });

        saveCancelButtonBar.addCancelClickListener(new Button.ClickListener() {
            @Override
            public void buttonClick(Button.ClickEvent event) {
                presenter.cancel();
            }
        });

        layout.addComponent(saveCancelButtonBar);
    }

    private void addRevenueTable(VerticalLayout layout) {
        conferenceBanquetContainer = new TetrisBeanItemContainer<>(FunctionSpaceConferenceBanquetUiWrapper.class);
        table = createRevenueTable();
        table.setId("conferenceBanquetTable");
        table.setHeight(100, Unit.PERCENTAGE);

        conferenceBanquetContainer.addNestedContainerBean("conferenceAndBanquetDto");
        table.setContainerDataSource(conferenceBanquetContainer);

        String revenueStreamId = "conferenceAndBanquetDto.revenueStream";
        String profitPercentageId = "profitPercentUI";
        String isRentalId = "isRental";

        table.addGeneratedColumn(profitPercentageId, new TetrisChangeAwareComponentColumnGenerator() {
            @Override
            public ChangeAware generateComponent(Table source, Object itemId, Object columnId) {
                TetrisBigDecimalField field = new TetrisBigDecimalField();
                field.setWidth(120, Unit.PIXELS);
                RangeValidator<BigDecimal> rangeValidator = new RangeValidator<>(getText("groupEvaluation.error.entervalidpercent"), BigDecimal.class, BigDecimal.ZERO, BigDecimal.valueOf(100));
                rangeValidator.setMinValueIncluded(true);
                field.addValidator(rangeValidator);

                return field;
            }
        });

        table.addGeneratedColumn(isRentalId, new TetrisRadioButtonColumnGenerator());

        if (presenter.shouldDisplayRevenueStreamDeleteButton()) {
            table.addDeleteActionColumn(true);
        }

        table.setVisibleColumns(getVisibleColumns(revenueStreamId, profitPercentageId, isRentalId));
        table.setColumnHeaders(getColumnHeaders());
        table.setColumnWidth(revenueStreamId, 310);
        table.setColumnWidth(profitPercentageId, 150);
        table.setColumnAlignment(isRentalId, Table.Align.CENTER);

        layout.addComponent(table);
        layout.setExpandRatio(table, 1);
    }

    private TetrisChangeAwareTable createRevenueTable() {
        if (presenter.shouldDisplayRevenueStreamDeleteButton()) {
            return new TetrisChangeAwareTable() {
                @Override
                protected TetrisImageButton createDeleteColumn(Table table, Object bean, Object columnId) {
                    FunctionSpaceConferenceBanquetUiWrapper wrapper = (FunctionSpaceConferenceBanquetUiWrapper) bean;
                    TetrisImageButton deleteButton = new TetrisImageButton(TetrisFontAwesome.DELETE_BUTTON,
                            getDeleteButtonLabel(wrapper), addDeleteButtonListener(bean));
                    deleteButton.setId("deleteButton");
                    deleteButton.setEnabled(wrapper.getConferenceAndBanquetDto().isUserAddedRevenueStream());
                    return deleteButton;
                }

                private String getDeleteButtonLabel(FunctionSpaceConferenceBanquetUiWrapper wrapper) {
                    return wrapper.getConferenceAndBanquetDto().isUserAddedRevenueStream() ?
                            UiUtils.getText("deleteButtonLabel") :
                            UiUtils.getText("groupPricing.conference.and.banquet.revenue.stream.deletion.warning");
                }

                @Override
                protected String getDeleteConfirmationMessage(Object item) {
                    FunctionSpaceConferenceBanquetUiWrapper wrapper = (FunctionSpaceConferenceBanquetUiWrapper) item;
                    if (presenter.isRevenueStreamUsedInPackageConfiguration(wrapper.getConferenceAndBanquetDto())) {
                        return UiUtils.getText("groupPricing.conference.and.banquet.revenue.stream.predeletion.package.warning");
                    }

                    return UiUtils.getText("common.deleteRow");
                }

            };

        }

        return new TetrisChangeAwareTable();
    }

    private Button.ClickListener addDeleteButtonListener(Object bean) {
        return event -> {
            FunctionSpaceConferenceBanquetUiWrapper wrapper = (FunctionSpaceConferenceBanquetUiWrapper) bean;
            wrapper.getConferenceAndBanquetDto().setStatusId(TenantStatusEnum.DELETED.getId());
            table.deleteRow(bean);
        };
    }

    private String[] getColumnHeaders() {
        if (presenter.shouldDisplayRevenueStreamDeleteButton()) {
            return new String[]{getText("groupPricing.column.header.conference.and.banquet.revenue.stream"), getText("common.profit"), getText("groupPricing.confAndBanquet.configuration.isrental"), ""};
        }

        return new String[]{getText("groupPricing.column.header.conference.and.banquet.revenue.stream"), getText("common.profit"), getText("groupPricing.confAndBanquet.configuration.isrental")};
    }

    private String[] getVisibleColumns(String revenueStreamId, String profitPercentageId, String isRentalId) {
        if (presenter.shouldDisplayRevenueStreamDeleteButton()) {
            return new String[]{
                    revenueStreamId,
                    profitPercentageId,
                    isRentalId,
                    TetrisChangeAwareTable.DELETE_ACTION_COLUMN_ID
            };
        }

        return new String[]{
                revenueStreamId,
                profitPercentageId,
                isRentalId
        };
    }

    public void initRevenueStreamTable(List<FunctionSpaceConferenceBanquetUiWrapper> conferenceAndBanquets) {
        table.addAll(conferenceAndBanquets, true);
        table.setPageLength(min(10, conferenceAndBanquets.size()));
    }

    @Override
    public boolean hasChanges() {
        return table.hasChanges();
    }
}


