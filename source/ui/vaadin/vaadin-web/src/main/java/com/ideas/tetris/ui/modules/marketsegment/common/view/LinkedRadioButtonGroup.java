package com.ideas.tetris.ui.modules.marketsegment.common.view;

import com.ideas.tetris.pacman.services.marketsegment.service.AnalyticalMarketSegmentAttribute.MarketSegmentLinkType;
import com.ideas.tetris.ui.common.util.UiUtils;

import java.util.Arrays;
import java.util.Collection;

import static com.ideas.tetris.pacman.services.marketsegment.service.AnalyticalMarketSegmentAttribute.MarketSegmentLinkType.LINKED;
import static com.ideas.tetris.pacman.services.marketsegment.service.AnalyticalMarketSegmentAttribute.MarketSegmentLinkType.NONLINKED;

public class LinkedRadioButtonGroup extends MarketSegmentAttributionRadioButtonGroup<MarketSegmentLinkType> {

    public LinkedRadioButtonGroup(AttributeAssignmentConfig config) {
        super("LinkedRadioButtonGroup", UiUtils.getText("linked"), config);
    }

    protected Collection<MarketSegmentLinkType> getItems() {
        return Arrays.asList(LINKED, NONLINKED);
    }

    public String getDescription() {
        return DescriptionBuilder.buildForLinkedAttributes(isIndependentProductsEnabled());
    }

    protected String getCaption(MarketSegmentLinkType marketSegmentLinkType) {
        switch (marketSegmentLinkType) {
            case LINKED:
                return UiUtils.getText(isIndependentProductsEnabled() ? "linkedToBaseProduct" : "linkedToBar");
            case NONLINKED:
                return UiUtils.getText("nonlinked.caption");
        }
        return null;
    }

    public boolean isLinked() {
        return !isEmpty() && getValue() == LINKED;
    }
}
