package com.ideas.tetris.ui.modules.marketsegment.common.view;

import com.ideas.tetris.pacman.services.marketsegment.entity.ForecastActivityType;
import com.ideas.tetris.pacman.services.product.Product;
import com.ideas.tetris.pacman.util.Executor;
import com.ideas.tetris.ui.common.TetrisComponentFactory;
import com.ideas.tetris.ui.common.component.TetrisHRule;
import com.ideas.tetris.ui.common.component.TetrisLabel;
import com.ideas.tetris.ui.common.component.TetrisNotification;
import com.ideas.tetris.ui.common.component.button.TetrisButton;
import com.ideas.tetris.ui.common.component.checkbox.TetrisCheckBoxV8;
import com.ideas.tetris.ui.common.util.UiUtils;
import com.ideas.tetris.ui.modules.marketsegment.common.AttributeAssignmentComponentAware;
import com.vaadin.data.Binder;
import com.vaadin.server.Sizeable;
import com.vaadin.shared.ui.ContentMode;
import com.vaadin.ui.CssLayout;
import com.vaadin.ui.VerticalLayout;
import de.steinwedel.messagebox.ButtonId;
import org.apache.commons.lang3.StringUtils;

import java.util.LinkedList;
import java.util.List;

import static com.ideas.tetris.pacman.util.Executor.addIfTrue;
import static com.ideas.tetris.pacman.util.Executor.executeIfTrue;
import static com.ideas.tetris.pacman.util.Runner.runIfTrue;
import static com.ideas.tetris.pacman.util.Streams.toList;
import static com.ideas.tetris.ui.common.util.UiUtils.getText;
import static java.util.Objects.nonNull;

public class AttributeAssignmentLayout extends VerticalLayout {

    private CssLayout root;

    private BusinessTypeRadioButtonGroup businessTypeRadioButtonGroup;
    private ForecastActivityTypeRadioButtonGroup forecastActivityTypeRadioButtonGroup;
    private UnQualifiedAttributeRadioButtonGroup unQualifiedAttributeRadioButtonGroup;
    private TetrisButton assignButton;
    private AttributeAssignmentConfig config;
    private AttributeAssignmentComponentAware helper;
    private Executor<AttributeBean> assignHandler;
    private TetrisCheckBoxV8 complimentaryCheckBox;
    private IndependentProductComboBox productComboBox;
    private LinkedRadioButtonGroup linkedRadioButtonGroup;
    private YieldableRadioButtonGroup yieldableRadioButtonGroup;
    private List<Product> products;

    private AttributeBean attribute;
    private Binder<AttributeBean> binder;
    private WarningMessageBuilder warningMessageBuilder;

    public AttributeAssignmentLayout(AttributeAssignmentConfig config, AttributeAssignmentComponentAware helper) {
        init()
                .setConfig(config)
                .setHelper(helper)
                .setWarningMessageHandler()
                .addTitle()
                .addBusinessTypeGroup()
                .addUnQualifiedGroup()
                .addLinkedGroup()
                .addYieldableRadioButtonGroup()
                .addComplimentaryCheckBox()
                .addForecastActivityTypeGroup()
                .addIndependentProductComboBox()
                .addAssignButton()
                .reset();
    }

    private AttributeAssignmentLayout setWarningMessageHandler() {
        this.warningMessageBuilder = new WarningMessageBuilder(config, helper);
        return this;
    }

    private void refreshBinder() {
        binder.setBean(attribute);
    }

    private AttributeAssignmentLayout init() {
        attribute = new AttributeBean();
        binder = new Binder();
        binder.addValueChangeListener(event -> {
            refreshState();
            if (event.getComponent() == businessTypeRadioButtonGroup) {
                forecastActivityTypeRadioButtonGroup.reset();
            }
        });
        root = new CssLayout();
        addComponent(root);
        setExpandRatio(root, 1);
        return this;
    }

    private Product createNewProduct() {
        Product newProduct = new Product();
        newProduct.setName(UiUtils.getText("independent.product"));
        newProduct.setId(0);
        return newProduct;
    }

    private AttributeAssignmentLayout addYieldableRadioButtonGroup() {
        runIfTrue(!config.isGroupView(), () -> {
            yieldableRadioButtonGroup = new YieldableRadioButtonGroup(config);
            binder.forField(yieldableRadioButtonGroup).bind(AttributeBean::getYieldable, AttributeBean::setYieldable);
            root.addComponent(yieldableRadioButtonGroup);
            addSeparator();
        });
        return this;
    }

    private AttributeAssignmentLayout addLinkedGroup() {
        runIfTrue(!config.isGroupView(), () -> {
            linkedRadioButtonGroup = new LinkedRadioButtonGroup(config);
            binder.forField(linkedRadioButtonGroup).bind(AttributeBean::getLinked, AttributeBean::setLinked);
            root.addComponent(linkedRadioButtonGroup);
            addSeparator();
        });
        return this;
    }

    private AttributeAssignmentLayout addIndependentProductComboBox() {
        productComboBox = new IndependentProductComboBox();
        binder.forField(productComboBox).bind(AttributeBean::getProduct, AttributeBean::setProduct);
        productComboBox.setDescription(DescriptionBuilder.buildForBaseProduct(), ContentMode.HTML);
        root.addComponent(productComboBox);
        return this;
    }

    private AttributeAssignmentLayout addComplimentaryCheckBox() {
        if (config.isComplimentaryFeatureEnabled()) {
            complimentaryCheckBox = new TetrisCheckBoxV8();
            complimentaryCheckBox.setCaption(UiUtils.getText("complimentary"));
            complimentaryCheckBox.setId("ComplimentaryCheckbox");
            binder.forField(complimentaryCheckBox).bind(AttributeBean::isComplimentary, AttributeBean::setComplimentary);
            root.addComponent(complimentaryCheckBox);
            complimentaryCheckBox.setEnabled(false);
            addSeparator();
        }
        return this;
    }

    private AttributeAssignmentLayout addAssignButton() {
        assignButton = TetrisComponentFactory.createButton("MarketSegmentAssignButton", UiUtils.getText("common.assign"), true);
        assignButton.addClickListener(clickEvent -> assign());
        addComponent(assignButton);
        return this;
    }

    private void assign() {
        if (isValid()) {
            String warningMessage = warningMessageBuilder.build(attribute);
            if (StringUtils.isNotBlank(warningMessage)) {
                confirmFromUserAndExecute(warningMessage);
            } else {
                execute();
            }
        } else {
            TetrisNotification.showWarningMessage(UiUtils.getText("attribute.assignment.warning"));
        }
    }

    private void confirmFromUserAndExecute(String message) {
        TetrisNotification.showAlert(getText("CONFIRMATION_TITLE"), message, true, buttonId -> {
            if (ButtonId.OK.equals(buttonId)) {
                if (warningMessageBuilder.agileProductRateCodesAffected(attribute, "AGILE_RATES") && !helper.forecastGroupsExists()) {
                    helper.invalidateAgileProducts(toList(config.getOriginallySelectedRateCodes()));
                }
                if (warningMessageBuilder.rateProtectProductRateCodesAffected(attribute, "FIXED_ABOVE_BAR") && !helper.forecastGroupsExists()) {
                    helper.invalidateRateProtectProducts(toList(config.getOriginallySelectedRateCodes()));
                }
                execute();
            }
        }).setWidth(50, Sizeable.Unit.PERCENTAGE);
    }

    private void execute() {
        executeIfTrue(nonNull(assignHandler), assignHandler, attribute);
    }

    public void setAttribute(AttributeBean attribute) {
        this.attribute = attribute;
        refreshBinder();
        refreshState();
    }

    private boolean isValid() {
        return
                businessTypeRadioButtonGroup.isValid() &&
                        (attribute.isGroup() || (
                                unQualifiedAttributeRadioButtonGroup.isValid() &&
                                        linkedRadioButtonGroup.isValid() &&
                                        yieldableRadioButtonGroup.isValid() &&
                                        (!config.isIndependentProductsEnabled() || productComboBox.isValid())
                        )) &&
                        forecastActivityTypeRadioButtonGroup.isValid() &&
                        !helper.isSelectionEmpty();
    }

    private AttributeAssignmentLayout setConfig(AttributeAssignmentConfig config) {
        this.config = config;
        return this;
    }

    private AttributeAssignmentLayout setHelper(AttributeAssignmentComponentAware helper) {
        this.helper = helper;
        return this;
    }

    private void addSeparator() {
        root.addComponent(new TetrisHRule());
    }

    private AttributeAssignmentLayout addBusinessTypeGroup() {
        businessTypeRadioButtonGroup = new BusinessTypeRadioButtonGroup(config);
        binder.forField(businessTypeRadioButtonGroup).bind(AttributeBean::getBusinessType, AttributeBean::setBusinessType);
        root.addComponent(businessTypeRadioButtonGroup);
        businessTypeRadioButtonGroup.setEnabled(config.isEditable());
        addSeparator();
        return this;
    }

    private void refreshState() {
        setUnqualifiedGroupRequired();
        setComplimentaryCheckBoxEnabled();
        setProductsRequired();
        setLinkedRequired();
        setYieldableRequired();
    }

    private void setComplimentaryCheckBoxEnabled() {
        runIfTrue(config.isComplimentaryFeatureEnabled(), () -> {
            boolean enabled = config.isEditable() && !attribute.isGroup();
            complimentaryCheckBox.setEnabled(enabled);
            if (!enabled) {
                complimentaryCheckBox.setValue(false);
            }
        });
    }

    private void setProductsRequired() {
        setProductsRequired(config.isIndependentProductsEnabled() && shouldEnableProductComboBox());
    }

    private void setProductsRequired(boolean required) {
        productComboBox.setRequired(required);
        runIfTrue(required, this::refreshProducts);
    }

    private void setLinkedRequired() {
        runIfTrue(!config.isGroupView(), () -> linkedRadioButtonGroup.setRequired(shouldEnableLinkedGroup()));
    }

    private void setYieldableRequired() {
        runIfTrue(!config.isGroupView(), () -> yieldableRadioButtonGroup.setRequired(shouldEnableYieldableGroup()));
    }

    private void setUnqualifiedGroupRequired() {
        runIfTrue(!config.isGroupView(), () -> unQualifiedAttributeRadioButtonGroup.setRequired(shouldEnableUnqualifiedGroup()));
    }

    private boolean shouldEnableYieldableGroup() {
        return attribute.isQualified();
    }

    private boolean shouldEnableLinkedGroup() {
        return attribute.isQualified();
    }

    private boolean shouldEnableUnqualifiedGroup() {
        return attribute.isUnQualified();
    }

    private boolean shouldEnableProductComboBox() {
        return attribute.isDemandAndWash() && (attribute.isUnQualified() || (attribute.isQualified() && (attribute.isLinked() || attribute.isSemiOrNonYieldable())));
    }

    private AttributeAssignmentLayout addUnQualifiedGroup() {
        runIfTrue(!config.isGroupView(), () -> {
            unQualifiedAttributeRadioButtonGroup = new UnQualifiedAttributeRadioButtonGroup(config);
            binder.forField(unQualifiedAttributeRadioButtonGroup).bind(AttributeBean::getUnqualifiedAttribute, AttributeBean::setUnqualifiedAttribute);
            root.addComponent(unQualifiedAttributeRadioButtonGroup);
            addSeparator();
        });
        return this;
    }

    private AttributeAssignmentLayout addForecastActivityTypeGroup() {
        forecastActivityTypeRadioButtonGroup = new ForecastActivityTypeRadioButtonGroup(config);
        binder.forField(forecastActivityTypeRadioButtonGroup).bind(AttributeBean::getForecastActivityType, AttributeBean::setForecastActivityType);
        root.addComponent(forecastActivityTypeRadioButtonGroup);
        forecastActivityTypeRadioButtonGroup.setItemEnabledProvider(this::shouldEnableForecastType);
        runIfTrue(config.isIndependentProductsEnabled(), this::addSeparator);
        return this;
    }

    private boolean shouldEnableForecastType(ForecastActivityType forecastActivityType) {
        return forecastActivityType.getId() == ForecastActivityType.DEMAND_AND_WASH || !attribute.isUnQualified();
    }

    private AttributeAssignmentLayout addTitle() {
        root.addComponent(new TetrisLabel(UiUtils.getText("attributes.header")));
        return this;
    }

    private void refreshProducts() {
        LinkedList<Product> finalProducts = new LinkedList<>(products);
        addIfTrue(shouldAllowUserToCreateNewProduct(), finalProducts, createNewProduct());
        productComboBox.setItems(finalProducts);
    }

    private boolean shouldAllowUserToCreateNewProduct() {
        return !config.isDefaultMS() && products.size() < config.getMaxProducts() + 1 && attribute.isEqualTo() && config.isEditable();
    }

    public void onAssign(Executor<AttributeBean> assignHandler) {
        this.assignHandler = assignHandler;
    }

    public void updatePermission(String permission) {
        assignButton.setEnabledRequirements(false, permission);
    }

    public void setProducts(List<Product> products) {
        this.products = products;
        refreshProducts();
    }

    public List<Product> getProducts() {
        return products;
    }

    public AttributeAssignmentLayout reset() {
        refreshToggles();
        resetSelections();
        refreshState();
        refreshBinder();
        return this;
    }

    private void resetSelections() {
        attribute.setBusinessType(config.isGroupView() ? BusinessTypeAttribute.GROUP : null);
        attribute.setUnqualifiedAttribute(null);
        attribute.setLinked(null);
        attribute.setYieldable(null);
        attribute.setComplimentary(false);
        productComboBox.setVisible(!config.isGroupView() && config.isIndependentProductsEnabled());
        productComboBox.setRequired(false);
    }

    private void refreshToggles() {
        runIfTrue(!config.isGroupView(), () -> {
            unQualifiedAttributeRadioButtonGroup.setIndependentProductsEnabled(config.isIndependentProductsEnabled());
            linkedRadioButtonGroup.setIndependentProductsEnabled(config.isIndependentProductsEnabled());
            forecastActivityTypeRadioButtonGroup.setIndependentProductsEnabled(config.isIndependentProductsEnabled());
        });
    }

    public void setIndependentProductsEnabled(boolean independentProductsEnabled) {
        config.setIndependentProductsEnabled(independentProductsEnabled);
        reset();
    }

    public AttributeBean getAttribute() {
        return attribute;
    }
}
