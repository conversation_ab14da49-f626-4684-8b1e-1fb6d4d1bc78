package com.ideas.tetris.ui.modules.functionspace.configuration;

import com.google.common.annotations.VisibleForTesting;
import com.ideas.tetris.pacman.services.functionspace.configuration.entity.FunctionSpaceStatus;
import com.ideas.tetris.ui.common.cdi.TetrisView;
import com.ideas.tetris.ui.common.component.TetrisLabel;
import com.ideas.tetris.ui.common.component.TetrisSaveCancelButtonBar;
import com.ideas.tetris.ui.common.component.checkbox.TetrisCheckBoxV8;
import com.ideas.tetris.ui.common.component.fontawesome.TetrisFontAwesome;
import com.ideas.tetris.ui.common.component.grid.TetrisGridV8;
import com.ideas.tetris.ui.common.component.layouts.TetrisHorizontalDataLayout;
import com.ideas.tetris.ui.common.util.HelpId;
import com.ideas.tetris.ui.common.util.TetrisTheme;
import com.ideas.tetris.ui.common.util.UiUtils;
import com.vaadin.data.Binder;
import com.vaadin.data.HasValue;
import com.vaadin.data.ValueProvider;
import com.vaadin.data.provider.Query;
import com.vaadin.server.Setter;
import com.vaadin.shared.ui.grid.HeightMode;
import com.vaadin.ui.Button;
import com.vaadin.ui.Grid;
import com.vaadin.v7.shared.ui.label.ContentMode;
import com.vaadin.v7.ui.VerticalLayout;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static org.fest.util.Collections.isNullOrEmpty;

@HelpId("1065")
public class FunctionSpaceConfigurationStatusView extends TetrisView<FunctionSpaceConfigurationStatusPresenter, Boolean> {

    private TetrisSaveCancelButtonBar saveCancelButtonBar;
    private TetrisGridV8<FunctionSpaceStatus> grid;
    private List<FunctionSpaceStatus> functionSpaceConfigStatusDTO;

    @Override
    protected void initView() {
        setSizeFull();
        VerticalLayout layout = new VerticalLayout();
        layout.setSizeFull();
        setCompositionRoot(layout);
        layout.setMargin(true);
        layout.setSpacing(true);
        addSaveCancelButtonBar(layout);
        layout.addComponent(createTableLegend());
        createGrid();
        layout.addComponent(grid);
    }

    private void createGrid() {
        grid = new TetrisGridV8<>(FunctionSpaceStatus.class);
        grid.addColumn(FunctionSpaceStatus::getStatusCode)
                .setId("Status")
                .setCaption(getText("functionSpaceStatus.status"));

        Grid.Column<FunctionSpaceStatus, TetrisCheckBoxV8> deductibleStatus = grid.addComponentColumn(dto -> checkbox(dto,
                        FunctionSpaceStatus::isInventoryDeducted, FunctionSpaceStatus::setInventoryDeducted,
                        finalOrInventoryDeductedValueChangeListener(dto), presenter.shouldEnableFinalOrInventoryDeduct(dto)))
                .setCaption(getText("functionSpaceStatus.deductible")).setId("Deductible")
                .setStyleGenerator(item -> "v-align-center");
        grid.getDefaultHeaderRow().getCell(deductibleStatus).setStyleName("centeralign");

        Grid.Column<FunctionSpaceStatus, TetrisCheckBoxV8> finalStatus = grid.addComponentColumn(dto -> checkbox(dto,
                        FunctionSpaceStatus::isFinal, FunctionSpaceStatus::setFinal,
                        finalOrInventoryDeductedValueChangeListener(dto), presenter.shouldEnableFinalOrInventoryDeduct(dto)))
                .setCaption(getText("functionSpaceStatus.final")).setId("Final")
                .setStyleGenerator(item -> "v-align-center");
        grid.getDefaultHeaderRow().getCell(finalStatus).setStyleName("centeralign");

        Grid.Column<FunctionSpaceStatus, TetrisCheckBoxV8> tentativeStatus = grid.addComponentColumn(dto -> checkbox(dto,
                        FunctionSpaceStatus::isTentative, FunctionSpaceStatus::setTentative,
                        prospectOrTentativeValueChangeListener(dto), presenter.shouldEnableProspectOrTentative(dto)))
                .setCaption(getText("functionSpaceStatus.tentative")).setId("Tentative")
                .setStyleGenerator(item -> "v-align-center");
        grid.getDefaultHeaderRow().getCell(tentativeStatus).setStyleName("centeralign");

        Grid.Column<FunctionSpaceStatus, TetrisCheckBoxV8> prospectStatus = grid.addComponentColumn(dto -> checkbox(dto,
                        FunctionSpaceStatus::isProspect, FunctionSpaceStatus::setProspect,
                        prospectOrTentativeValueChangeListener(dto), presenter.shouldEnableProspectOrTentative(dto)))
                .setCaption(getText("functionSpaceStatus.prospect")).setId("Prospect")
                .setStyleGenerator(item -> "v-align-center");
        grid.getDefaultHeaderRow().getCell(prospectStatus).setStyleName("centeralign");
        grid.setWidth(50, Unit.PERCENTAGE);
    }

    private HasValue.ValueChangeListener<?> prospectOrTentativeValueChangeListener(FunctionSpaceStatus functionSpaceConfigStatusDTO) {
        return event -> {
            functionSpaceConfigStatusDTO.setFinal(false);
            functionSpaceConfigStatusDTO.setInventoryDeducted(false);
            grid.getDataProvider().refreshAll();
        };
    }

    private HasValue.ValueChangeListener<?> finalOrInventoryDeductedValueChangeListener(FunctionSpaceStatus functionSpaceConfigStatusDTO) {
        return event -> {
            functionSpaceConfigStatusDTO.setProspect(false);
            functionSpaceConfigStatusDTO.setTentative(false);
            grid.getDataProvider().refreshAll();
        };
    }

    private TetrisCheckBoxV8 checkbox(FunctionSpaceStatus dto,
                                      ValueProvider<FunctionSpaceStatus, Boolean> getter,
                                      Setter<FunctionSpaceStatus, Boolean> setter,
                                      HasValue.ValueChangeListener<?> listener,
                                      boolean isCheckboxEnabled) {
        TetrisCheckBoxV8 checkBox = new TetrisCheckBoxV8();
        Binder<FunctionSpaceStatus> binder = new Binder<>();
        binder.bind(checkBox, getter, setter);
        binder.setBean(dto);
        binder.addValueChangeListener(listener);
        checkBox.setEnabled(isCheckboxEnabled);

        //Setting the disabled checkboxes to light grey color
        if (!isCheckboxEnabled) {
            checkBox.addStyleName(TetrisTheme.BOX_LIGHT_GREY);
        }

        return checkBox;
    }

    private void addSaveCancelButtonBar(VerticalLayout layout) {
        saveCancelButtonBar = new TetrisSaveCancelButtonBar(false);
        saveCancelButtonBar.setWidthUndefined();
        saveCancelButtonBar.addValidSaveListener((TetrisSaveCancelButtonBar.ValidSaveListener) event -> {
            List<FunctionSpaceStatus> statuses = grid.getDataProvider().fetch(new Query<>()).collect(Collectors.toList());
            List<FunctionSpaceStatus> updatedStatuses = presenter.findUpdatedStatuses(statuses);
            if (isNullOrEmpty(updatedStatuses)) {
                presenter.showWarning(getText("common.error.msg.nochangesForSubmit"));
            } else {
                presenter.save(updatedStatuses);
                presenter.sync();
            }
        });
        saveCancelButtonBar.addCancelClickListener((Button.ClickListener) event -> presenter.reset(functionSpaceConfigStatusDTO));
        layout.addComponent(saveCancelButtonBar);
    }

    public void refresh(List<FunctionSpaceStatus> functionSpaceConfigStatusDTO) {
        grid.setItems(functionSpaceConfigStatusDTO);
        resizeTheGrid(functionSpaceConfigStatusDTO.size());
        this.functionSpaceConfigStatusDTO = new ArrayList<>(functionSpaceConfigStatusDTO);
        grid.getDataProvider().refreshAll();
    }

    private void resizeTheGrid(int size) {
        if (size <= 25) {
            grid.setHeightMode(HeightMode.UNDEFINED);
        } else {
            grid.setHeightByRows(25);
        }
    }

    @VisibleForTesting
    TetrisHorizontalDataLayout createTableLegend() {
        TetrisLabel infoIcon = new TetrisLabel(TetrisFontAwesome.GREEN_CHECK_CIRCLE.getHtml() + "&nbsp;", ContentMode.HTML);
        infoIcon.setStyleName("v-align-right");

        TetrisLabel infoMessage = new TetrisLabel(UiUtils.getText("functionSpaceStatus.legend"));
        infoMessage.setStyleName("italic-label");
        infoMessage.setSizeUndefined();

        TetrisHorizontalDataLayout legend = new TetrisHorizontalDataLayout();
        legend.addComponents(infoIcon, infoMessage);
        legend.setWidth(50, Unit.PERCENTAGE);
        legend.setId("statusCodesInfoLegend");
        legend.setExpandRatio(infoIcon, 1);

        return legend;
    }

    public void updatePermission(String permissionKey) {
        saveCancelButtonBar.getSaveButton().setEnabledRequirements(true, permissionKey);
    }
}
