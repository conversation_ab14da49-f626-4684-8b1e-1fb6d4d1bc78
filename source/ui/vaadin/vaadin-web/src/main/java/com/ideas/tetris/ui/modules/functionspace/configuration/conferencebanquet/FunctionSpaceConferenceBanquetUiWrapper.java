package com.ideas.tetris.ui.modules.functionspace.configuration.conferencebanquet;

import com.ideas.tetris.pacman.services.functionspace.configuration.entity.FunctionSpaceResourceType;
import com.ideas.tetris.pacman.services.grouppricing.configuration.dto.ConferenceAndBanquetDto;
import com.ideas.tetris.ui.common.component.optiongroup.TetrisOptionGroup;
import com.ideas.tetris.ui.common.component.table.TetrisChangeAwareColumnComponentProvider;

import java.math.BigDecimal;
import java.util.Optional;

import static com.ideas.tetris.pacman.util.BigDecimalUtil.ONE_HUNDRED;
import static java.math.RoundingMode.HALF_UP;
import static java.util.Objects.isNull;

public class FunctionSpaceConferenceBanquetUiWrapper extends TetrisChangeAwareColumnComponentProvider {
    private static final String IS_RENTAL_ID = "isRental";

    private BigDecimal profitPercentUI;
    private FunctionSpaceResourceType rentalResourceType;
    private FunctionSpaceResourceType otherResourceType;
    private ConferenceAndBanquetDto conferenceAndBanquetDto;

    public void setConferenceAndBanquetDto(ConferenceAndBanquetDto conferenceAndBanquetDto) {
        this.conferenceAndBanquetDto = conferenceAndBanquetDto;
    }

    public ConferenceAndBanquetDto getConferenceAndBanquetDto() {
        return conferenceAndBanquetDto;
    }


    public BigDecimal getProfitPercentUI() {
        if (isNull(profitPercentUI)) {
            profitPercentUI = Optional.ofNullable(conferenceAndBanquetDto.getProfitPercentage())
                    .map(profitpercent -> profitpercent.multiply(ONE_HUNDRED))
                    .orElse(null);
        }
        return profitPercentUI;
    }

    public void setProfitPercentUI(BigDecimal profitPercentUI) {
        this.profitPercentUI = profitPercentUI;
        conferenceAndBanquetDto.setProfitPercentage(
                Optional.ofNullable(profitPercentUI)
                        .map(profit -> profit.divide(ONE_HUNDRED, 4, HALF_UP))
                        .orElse(BigDecimal.ZERO));
    }

    @Override
    public boolean isPersisted() {
        return conferenceAndBanquetDto.getId() != null;
    }

    public Boolean getIsRental() {
        if (isNull(conferenceAndBanquetDto.getIsRental())) {
            return Boolean.FALSE;
        }
        return conferenceAndBanquetDto.getIsRental();
    }

    public void setIsRental(Boolean isRental) {
        conferenceAndBanquetDto.setRental(Optional.ofNullable(isRental).orElse(Boolean.FALSE));
    }

    public FunctionSpaceResourceType getRentalResourceType() {
        return rentalResourceType;
    }

    public void setRentalResourceType(FunctionSpaceResourceType rentalResourceType) {
        this.rentalResourceType = rentalResourceType;
    }

    public FunctionSpaceResourceType getOtherResourceType() {
        return otherResourceType;
    }

    public void setOtherResourceType(FunctionSpaceResourceType otherResourceType) {
        this.otherResourceType = otherResourceType;
    }

    public TetrisOptionGroup getIsRentalField() {
        return (TetrisOptionGroup) getComponent(IS_RENTAL_ID);
    }


}
