package com.ideas.tetris.ui.modules.functionspace.demandcalendar.calendar;

import com.ideas.tetris.pacman.services.functionspace.dto.FunctionSpaceDemandCalendarDateDto;
import com.ideas.tetris.pacman.services.functionspace.dto.FunctionSpaceDemandCalendarDateStatus;
import com.ideas.tetris.pacman.services.specialevent.dto.SpecialEventSummaryDto;
import org.joda.time.LocalDate;

import java.math.BigDecimal;
import java.util.List;

public class DemandCalendarUIWrapper {

    private FunctionSpaceDemandCalendarDateDto dto;
    private FunctionSpaceDemandCalendarDateStatus status;
    private LocalDate date;
    private FunctionSpaceForecastLevel forecastLevel;
    private boolean hasSpecialEvent = false;
    private boolean hasOverride = false;
    private BigDecimal utilizationForecast;
    private BigDecimal onBooksUtilization;
    private BigDecimal actualUtilization;
    private List<SpecialEventSummaryDto> specialEvents;

    public DemandCalendarUIWrapper(LocalDate date, FunctionSpaceForecastLevel forecastLevel) {
        this.date = date;
        this.forecastLevel = forecastLevel;
    }

    public DemandCalendarUIWrapper(FunctionSpaceDemandCalendarDateDto dto) {
        this.dto = dto;
        this.date = dto.getOccupancyDate();
        this.forecastLevel = FunctionSpaceForecastLevel.convert(dto.getFunctionSpaceDemandCalendarForecastLevel());
        this.hasSpecialEvent = dto.hasSpecialEvents();
        this.hasOverride = dto.hasUserForecastOverride();
        this.utilizationForecast = dto.getForecastedUtilization().multiply(new BigDecimal(100)).setScale(2, BigDecimal.ROUND_HALF_UP);
        this.onBooksUtilization = dto.getOnTheBooksUtilization().multiply(new BigDecimal(100)).setScale(2, BigDecimal.ROUND_HALF_UP);
        ;
        this.actualUtilization = dto.getOnTheBooksUtilization().multiply(new BigDecimal(100)).setScale(2, BigDecimal.ROUND_HALF_UP);
        ; // actual is for past; what is on books
        this.status = dto.getFunctionSpaceDemandCalendarDateStatus();
    }

    public LocalDate getDate() {
        return date;
    }

    public FunctionSpaceForecastLevel getForecastLevel() {
        return forecastLevel;
    }

    public void setForecastLevel(FunctionSpaceForecastLevel forecastLevel) {
        this.forecastLevel = forecastLevel;
    }

    public boolean isHasSpecialEvent() {
        return hasSpecialEvent;
    }

    public void setHasSpecialEvent(boolean hasSpecialEvent) {
        this.hasSpecialEvent = hasSpecialEvent;
    }

    public boolean isHasOverride() {
        return hasOverride;
    }

    public void setHasOverride(boolean hasOverride) {
        this.hasOverride = hasOverride;
    }

    public BigDecimal getUtilizationForecast() {
        return utilizationForecast;
    }

    public void setUtilizationForecast(BigDecimal utilizationForecast) {
        this.utilizationForecast = utilizationForecast;
    }

    public BigDecimal getOnBooksUtilization() {
        return onBooksUtilization;
    }

    public void setOnBooksUtilization(BigDecimal onBooksUtilization) {
        this.onBooksUtilization = onBooksUtilization;
    }

    public BigDecimal getActualUtilization() {
        return actualUtilization;
    }

    public void setActualUtilization(BigDecimal actualUtilization) {
        this.actualUtilization = actualUtilization;
    }

    public FunctionSpaceDemandCalendarDateStatus getStatus() {
        return status;
    }

    public void setStatus(FunctionSpaceDemandCalendarDateStatus status) {
        this.status = status;
    }

    public FunctionSpaceDemandCalendarDateDto getDto() {
        return dto;
    }

    public void setSpecialEvents(List<SpecialEventSummaryDto> specialEvents) {
        this.specialEvents = specialEvents;
    }

    public List<SpecialEventSummaryDto> getSpecialEvents() {
        return specialEvents;
    }
}
