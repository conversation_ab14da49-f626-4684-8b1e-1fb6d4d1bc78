package com.ideas.tetris.ui.modules.functionspace.performancetrend;

import com.ideas.tetris.ui.common.component.TetrisBeanItemContainer;
import com.ideas.tetris.ui.common.component.button.TetrisButtonBar;
import com.ideas.tetris.ui.common.component.button.TetrisExportXlsImageButton;
import com.ideas.tetris.ui.common.component.charts.TetrisChart;
import com.ideas.tetris.ui.common.component.table.TetrisTable;
import com.ideas.tetris.ui.common.util.DateFormatUtil;
import com.ideas.tetris.ui.common.util.UiUtils;
import com.vaadin.addon.charts.model.ChartType;
import com.vaadin.addon.charts.model.Configuration;
import com.vaadin.addon.charts.model.HorizontalAlign;
import com.vaadin.addon.charts.model.Labels;
import com.vaadin.addon.charts.model.LayoutDirection;
import com.vaadin.addon.charts.model.Legend;
import com.vaadin.addon.charts.model.ListSeries;
import com.vaadin.addon.charts.model.VerticalAlign;
import com.vaadin.addon.charts.model.XAxis;
import com.vaadin.addon.charts.model.YAxis;
import com.vaadin.ui.Alignment;
import com.vaadin.ui.CustomComponent;
import com.vaadin.v7.ui.HorizontalLayout;
import com.vaadin.v7.ui.Table;
import com.vaadin.v7.ui.VerticalLayout;

import java.util.ArrayList;
import java.util.List;

public class FunctionSpaceUtilizationTab extends CustomComponent implements FunctionSpacePerformanceTrend {
    private final VerticalLayout displayLayout;
    private boolean isTableVisible = true;
    private List<FunctionSpaceDetailsReportDtoUiWrapper> data;
    private TetrisChart tetrisChart;
    private TetrisTable tetrisTable;

    public FunctionSpaceUtilizationTab(FunctionSpacePerformanceTrendPresenter presenter) {
        this.addStyleName("tabView");
        setSizeFull();
        VerticalLayout verticalLayout = new VerticalLayout();
        verticalLayout.setSizeFull();
        TetrisButtonBar buttonBar = new TetrisButtonBar();
        buttonBar.addButton("graph", UiUtils.getText("monitor.summary.graph"));
        buttonBar.addButton("table", UiUtils.getText("monitor.summary.table"));
        buttonBar.setSelected("table");
        buttonBar.addSelectedListener(new TetrisButtonBar.SelectedChangeListener() {
            @Override
            public void onSelected(TetrisButtonBar.SelectedEvent event) {
                String mode = (String) event.getSelected();
                isTableVisible = ("table".equalsIgnoreCase(mode));
                displayData();
            }
        });
        tetrisChart = new TetrisChart();
        tetrisChart.setVisible(!isTableVisible);
        tetrisTable = createTable();
        tetrisTable.setVisible(isTableVisible);

        TetrisExportXlsImageButton exportXlsImageButton = new TetrisExportXlsImageButton(tetrisTable, UiUtils.getText("functionspace.dashboard.utilization.chart.header"));
        HorizontalLayout horizontalLayout = new HorizontalLayout(buttonBar);
        horizontalLayout.setWidth(100, Unit.PERCENTAGE);
        horizontalLayout.setComponentAlignment(buttonBar, Alignment.MIDDLE_CENTER);
        HorizontalLayout topBar = new HorizontalLayout(horizontalLayout, exportXlsImageButton);
        topBar.setWidth(100, Unit.PERCENTAGE);
        topBar.setExpandRatio(horizontalLayout, 1.0f);
        verticalLayout.addComponent(topBar);
        verticalLayout.setComponentAlignment(topBar, Alignment.MIDDLE_CENTER);

        displayLayout = new VerticalLayout();
        displayLayout.addComponent(tetrisChart);
        displayLayout.addComponent(tetrisTable);


        displayLayout.setSizeFull();
        verticalLayout.addComponent(displayLayout);
        verticalLayout.setExpandRatio(displayLayout, 1.0f);
        verticalLayout.setSpacing(true);
        verticalLayout.setMargin(true);
        setCompositionRoot(verticalLayout);
    }

    @Override
    public void setData(List<FunctionSpaceDetailsReportDtoUiWrapper> data) {
        this.data = data;
        displayData();
    }

    private void displayData() {
        populateTable();
        populateChart();
    }

    private void populateChart() {
        Configuration configuration = new Configuration();
        configuration.getChart().setType(ChartType.LINE);
        configuration.getChart().setMarginBottom(100);
        XAxis xAxis = configuration.getxAxis();
        Labels labels = new Labels();
        labels.setRotation(-45);
        xAxis.setLabels(labels);

        configuration.getTitle().setText(UiUtils.getText("groupEvaluation.functionSpaceUtilization"));

        List<String> xaxis = new ArrayList<String>();
        List<Number> actualUtilization = new ArrayList<Number>();
        List<Number> forecastedUtilization = new ArrayList<Number>();
        List<Number> lastYearActualUtilization = new ArrayList<Number>();
        for (FunctionSpaceDetailsReportDtoUiWrapper dto : data) {
            xaxis.add(DateFormatUtil.formatStandard(dto.getDto().getOccupancyDate()));
            actualUtilization.add(dto.getDto().getOnBooksUtilization());
            forecastedUtilization.add(dto.getDto().getForecastUtilization());
            lastYearActualUtilization.add(dto.getDto().getLastYearOnBooksUtilization());
        }
        configuration.getxAxis().setCategories(xaxis.toArray(new String[0]));

        YAxis yAxis = configuration.getyAxis();
        yAxis.setMin(-5d);
        yAxis.setTitle(UiUtils.getText("groupEvaluation.utilization%"));
        yAxis.getTitle().setAlign(VerticalAlign.HIGH);

        Legend legend = configuration.getLegend();
        legend.setLayout(LayoutDirection.HORIZONTAL);
        legend.setAlign(HorizontalAlign.CENTER);
        legend.setVerticalAlign(VerticalAlign.BOTTOM);
        legend.setX(10d);
        legend.setY(15d);
        legend.setBorderWidth(0);

        ListSeries ls = new ListSeries();
        ls.setName(UiUtils.getText("groupEvaluation.actual.utilization"));
        ls.setData(actualUtilization);
        configuration.addSeries(ls);
        ls = new ListSeries();
        ls.setName(UiUtils.getText("groupEvaluation.utilization.forecast"));
        ls.setData(forecastedUtilization);
        configuration.addSeries(ls);
        ls = new ListSeries();
        ls.setName(UiUtils.getText("groupPricing.forecastReview.actualUtilizationLastYear"));
        ls.setData(lastYearActualUtilization);
        configuration.addSeries(ls);

        tetrisChart.setConfiguration(configuration);
        tetrisChart.drawChart(configuration);
        tetrisChart.setVisible(!isTableVisible);
    }

    private void populateTable() {
        tetrisTable.addAll(data, true);
        tetrisTable.setVisible(isTableVisible);
    }

    private TetrisTable createTable() {
        TetrisBeanItemContainer<FunctionSpaceDetailsReportDtoUiWrapper> container = new TetrisBeanItemContainer<FunctionSpaceDetailsReportDtoUiWrapper>(FunctionSpaceDetailsReportDtoUiWrapper.class);
        TetrisTable table = new TetrisTable(container);
        container.addNestedContainerBean("dto");
        String dayOfWeek = "dayOfWeek";
        String occupancyDate = "dto.occupancyDate";
        String actualUtilization = "dto.onBooksUtilization";
        String forecastedUtilization = "dto.forecastUtilization";
        String lastYearOnBooks = "dto.lastYearOnBooksUtilization";

        table.setVisibleColumns(new String[]{dayOfWeek, occupancyDate, actualUtilization, forecastedUtilization, lastYearOnBooks});
        table.setColumnHeader(dayOfWeek, UiUtils.getText("common.dow"));
        table.setColumnHeader(occupancyDate, UiUtils.getText("occupancyDate"));
        table.setColumnHeader(actualUtilization, UiUtils.getText("groupPricing.demandCalendar.utilization%Actual"));
        table.setColumnHeader(forecastedUtilization, UiUtils.getText("groupEvaluation.utilization.forecast") + " %");
        table.setColumnHeader(lastYearOnBooks, UiUtils.getText("groupPricing.forecastReview.actualUtilizationLastYear") + " %");

        table.setColumnAlignment(actualUtilization, Table.Align.RIGHT);
        table.setColumnAlignment(forecastedUtilization, Table.Align.RIGHT);
        table.setColumnAlignment(lastYearOnBooks, Table.Align.RIGHT);
        table.setSizeFull();
        return table;
    }
}
