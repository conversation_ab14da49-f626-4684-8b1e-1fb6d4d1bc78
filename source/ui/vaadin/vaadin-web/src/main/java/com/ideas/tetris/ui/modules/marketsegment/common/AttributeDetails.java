package com.ideas.tetris.ui.modules.marketsegment.common;

import com.ideas.tetris.pacman.services.marketsegment.entity.ForecastActivityType;
import com.ideas.tetris.pacman.services.marketsegment.service.AnalyticalMarketSegmentAttribute;
import com.ideas.tetris.pacman.services.product.Product;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

public class AttributeDetails {
    public static final String DEF = "_DEF";
    public static final String LINKED_TO_BASE_PRODUCT = "Linked to Base Product";
    public static final String EQUAL_TO_BASE_PRODUCT = "Equal to Base Product";
    public static final String LINKED_TO_BAR = "Linked to BAR";
    public static final String EQUAL_TO_BAR = "Equal to BAR";
    private List<AMSSummaryUIWrapper> selectedItems;
    private AnalyticalMarketSegmentAttribute attribute;
    private ForecastActivityType forecastActivityType;
    private boolean complimentary = false;
    private Product product;

    public AttributeDetails(List<AMSSummaryUIWrapper> selectedItems, AnalyticalMarketSegmentAttribute attribute) {
        this.selectedItems = selectedItems;
        this.attribute = attribute;
    }

    public AttributeDetails(List<AMSSummaryUIWrapper> selectedItems, AnalyticalMarketSegmentAttribute attribute, boolean complimentary) {
        this(selectedItems, attribute);
        this.complimentary = complimentary;
    }


    public List<AMSSummaryUIWrapper> getSelectedItems() {
        return selectedItems;
    }

    public AnalyticalMarketSegmentAttribute getAttribute() {
        return attribute;
    }

    public boolean isComplimentary() {
        return complimentary;
    }

    public void setComplimentary(boolean complimentary) {
        this.complimentary = complimentary;
    }

    public void setSelectedItems(List<AMSSummaryUIWrapper> selectedItems) {
        this.selectedItems = selectedItems;
    }

    public void setAttribute(AnalyticalMarketSegmentAttribute attribute) {
        this.attribute = attribute;
    }

    public void setProduct(Product product) {
        this.product = product;
    }

    public Product getProduct() {
        return product;
    }

    public int getProductId() {
        return product == null ? -1 : product.getId();
    }

    public ForecastActivityType getForecastActivityType() {
        return forecastActivityType;
    }

    public void setForecastActivityType(ForecastActivityType forecastActivityType) {
        this.forecastActivityType = forecastActivityType;
    }

    public static String getAttributeValue(String attributeValue, boolean isIndependentProductEnabled) {
        if (attributeValue != null) {
            if (isIndependentProductEnabled) {
                return attributeValue;
            }
            return attributeValue.replace(LINKED_TO_BASE_PRODUCT, LINKED_TO_BAR);
        }
        return StringUtils.EMPTY;
    }
}
