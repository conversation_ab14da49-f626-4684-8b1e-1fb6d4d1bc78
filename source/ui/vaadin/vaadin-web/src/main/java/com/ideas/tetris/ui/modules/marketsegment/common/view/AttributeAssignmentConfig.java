package com.ideas.tetris.ui.modules.marketsegment.common.view;

import com.ideas.tetris.pacman.services.marketsegment.entity.ForecastActivityType;
import com.ideas.tetris.pacman.services.marketsegment.service.AnalyticalMarketSegmentAttribute;
import com.ideas.tetris.ui.modules.marketsegment.common.AmsView;

import java.util.List;
import java.util.Set;

public class AttributeAssignmentConfig {
    private AmsView amsView;
    private boolean groupOptionEnabled;
    private List<ForecastActivityType> forecastActivityTypes;
    private boolean complimentaryFeatureEnabled;
    private boolean independentProductsEnabled;
    private int maxProducts;
    private boolean editable;
    private boolean defaultMS;
    private String marketSegmentUnderEdit;
    private Set<String> originallySelectedRateCodes;
    private Set<String> originallySelectedRateCodesForRateProtect;
    private AnalyticalMarketSegmentAttribute originallySelectedAttribute;
    private String originallySelectedBaseProduct;

    private AttributeAssignmentConfig() {
    }

    public AmsView getAmsView() {
        return amsView;
    }

    public void setAmsView(AmsView amsView) {
        this.amsView = amsView;
    }

    public boolean isGroupOptionEnabled() {
        return groupOptionEnabled;
    }

    public void setGroupOptionEnabled(boolean groupOptionEnabled) {
        this.groupOptionEnabled = groupOptionEnabled;
    }

    public List<ForecastActivityType> getForecastActivityTypes() {
        return forecastActivityTypes;
    }

    public void setForecastActivityTypes(List<ForecastActivityType> forecastActivityTypes) {
        this.forecastActivityTypes = forecastActivityTypes;
    }

    public boolean isComplimentaryFeatureEnabled() {
        return complimentaryFeatureEnabled;
    }

    public void setComplimentaryFeatureEnabled(boolean complimentaryFeatureEnabled) {
        this.complimentaryFeatureEnabled = complimentaryFeatureEnabled;
    }

    public boolean isIndependentProductsEnabled() {
        return independentProductsEnabled;
    }

    public void setIndependentProductsEnabled(boolean independentProductsEnabled) {
        this.independentProductsEnabled = independentProductsEnabled;
    }

    public int getMaxProducts() {
        return maxProducts;
    }

    public void setMaxProducts(int maxProducts) {
        this.maxProducts = maxProducts;
    }

    public static Builder builder() {
        return new Builder();
    }

    public boolean isGroupView() {
        return amsView == AmsView.AMS_VIEW_GROUP;
    }

    public boolean isEditable() {
        return editable;
    }

    public void setEditable(boolean editable) {
        this.editable = editable;
    }

    public String getMarketSegmentUnderEdit() {
        return marketSegmentUnderEdit;
    }

    public void setMarketSegmentUnderEdit(String marketSegmentUnderEdit) {
        this.marketSegmentUnderEdit = marketSegmentUnderEdit;
    }

    public Set<String> getOriginallySelectedRateCodes() {
        return originallySelectedRateCodes;
    }

    public void setOriginallySelectedRateCodes(Set<String> originallySelectedRateCodes) {
        this.originallySelectedRateCodes = originallySelectedRateCodes;
    }

    public AnalyticalMarketSegmentAttribute getOriginallySelectedAttribute() {
        return originallySelectedAttribute;
    }

    public void setOriginallySelectedAttribute(AnalyticalMarketSegmentAttribute originallySelectedAttribute) {
        this.originallySelectedAttribute = originallySelectedAttribute;
    }

    public String getOriginallySelectedBaseProduct() {
        return originallySelectedBaseProduct;
    }

    public void setOriginallySelectedBaseProduct(String originallySelectedBaseProduct) {
        this.originallySelectedBaseProduct = originallySelectedBaseProduct;
    }

    public boolean isOriginallySelectedAttributeUnQualifiedOrQualifiedLinked() {
        return originallySelectedAttribute != null && (isOriginallySelectedAttributeUnQualified() || isOriginallySelectedAttributeQualifiedLinked());
    }

    public boolean isOriginallySelectedAttributeUnQualifiedOrQualified() {
        return originallySelectedAttribute != null && (isOriginallySelectedAttributeUnQualified() || isOriginallySelectedAttributeQualified());
    }

    public boolean isDefaultMS() {
        return defaultMS;
    }

    public void setDefaultMS(boolean defaultMS) {
        this.defaultMS = defaultMS;
    }

    private boolean isOriginallySelectedAttributeQualifiedLinked() {
        switch (originallySelectedAttribute) {
            case QUALIFIED_NONBLOCK_LINKED_NONYIELDABLE:
            case QUALIFIED_NONBLOCK_LINKED_SEMIYIELDABLE:
            case QUALIFIED_NONBLOCK_LINKED_YIELDABLE:
                return true;
        }
        return false;
    }

    private boolean isOriginallySelectedAttributeUnQualified() {
        switch (originallySelectedAttribute) {
            case EQUAL_TO_BAR:
            case FENCED:
            case PACKAGED:
            case FENCED_AND_PACKAGED:
            case UNFENCED_NON_PACKAGED:
                return true;
        }
        return false;
    }

    private boolean isOriginallySelectedAttributeQualified() {
        switch (originallySelectedAttribute) {
            case QUALIFIED_NONBLOCK_LINKED_NONYIELDABLE:
            case QUALIFIED_NONBLOCK_LINKED_SEMIYIELDABLE:
            case QUALIFIED_NONBLOCK_LINKED_YIELDABLE:
            case QUALIFIED_NONBLOCK_NOTLINKED_NONYIELDABLE:
            case QUALIFIED_NONBLOCK_NOTLINKED_SEMIYIELDABLE:
            case QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE:
                return true;
        }
        return false;
    }

    public static class Builder {

        AttributeAssignmentConfig config = new AttributeAssignmentConfig();

        public Builder withAmsView(AmsView amsView) {
            config.setAmsView(amsView);
            return this;
        }

        public Builder withGroupOptionEnabled(boolean groupOptionEnabled) {
            config.setGroupOptionEnabled(groupOptionEnabled);
            return this;
        }

        public Builder withForecastActivityTypes(List<ForecastActivityType> forecastActivityTypes) {
            config.setForecastActivityTypes(forecastActivityTypes);
            return this;
        }

        public Builder withComplimentaryFeatureEnabled(boolean complimentaryFeatureEnabled) {
            config.setComplimentaryFeatureEnabled(complimentaryFeatureEnabled);
            return this;
        }

        public Builder withIndependentProductsEnabled(boolean independentProductsEnabled) {
            config.setIndependentProductsEnabled(independentProductsEnabled);
            return this;
        }

        public Builder withMaxProducts(int maxProducts) {
            config.setMaxProducts(maxProducts);
            return this;
        }

        public Builder withEditable(boolean editable) {
            config.setEditable(editable);
            return this;
        }

        public Builder withMarketSegmentUnderEdit(String marketSegmentUnderEdit) {
            config.setMarketSegmentUnderEdit(marketSegmentUnderEdit);
            return this;
        }

        public Builder withOriginallySelectedRateCodes(Set<String> originallySelectedRateCodes) {
            config.setOriginallySelectedRateCodes(originallySelectedRateCodes);
            return this;
        }

        public Builder withOriginallySelectedAttribute(AnalyticalMarketSegmentAttribute originallySelectedAttribute) {
            this.config.setOriginallySelectedAttribute(originallySelectedAttribute);
            return this;
        }

        public Builder withOriginallySelectedBaseProduct(String originallySelectedBaseProduct) {
            this.config.setOriginallySelectedBaseProduct(originallySelectedBaseProduct);
            return this;
        }

        public Builder withDefaultMS(boolean defaultMS) {
            this.config.setDefaultMS(defaultMS);
            return this;
        }

        public AttributeAssignmentConfig build() {
            return config;
        }
    }
}
