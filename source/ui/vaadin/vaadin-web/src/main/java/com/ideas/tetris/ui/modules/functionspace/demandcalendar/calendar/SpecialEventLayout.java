package com.ideas.tetris.ui.modules.functionspace.demandcalendar.calendar;

import com.ideas.tetris.pacman.services.specialevent.dto.SpecialEventSummaryDto;
import com.ideas.tetris.ui.common.util.UiUtils;
import com.vaadin.v7.shared.ui.label.ContentMode;
import com.vaadin.v7.ui.Label;
import com.vaadin.v7.ui.VerticalLayout;

public class SpecialEventLayout extends VerticalLayout {


    public SpecialEventLayout(SpecialEventSummaryDto specialEvent) {
        Label name = new Label("<b>" + specialEvent.getEventName() + "</b>", ContentMode.HTML);
        this.addComponent(name);

        Label description = new Label(getSpecialEventDescription(specialEvent));
        this.addComponent(description);
    }


    public static String getSpecialEventDescription(SpecialEventSummaryDto specialEvent) {
        if (specialEvent.getImpactsFunctionSpace() && !specialEvent.getInformationUseOnly()) {
            return UiUtils.getText("groupPricing.demandCalendar.guestRoomAndFunctionSpaceEvent");
        } else if (specialEvent.getImpactsFunctionSpace()) {
            return UiUtils.getText("function.space.event");
        } else if (!specialEvent.getInformationUseOnly()) {
            return UiUtils.getText("groupPricing.demandCalendar.guestRoomEvent");
        } else {
            return UiUtils.getText("informational.use.only");
        }
    }
}
