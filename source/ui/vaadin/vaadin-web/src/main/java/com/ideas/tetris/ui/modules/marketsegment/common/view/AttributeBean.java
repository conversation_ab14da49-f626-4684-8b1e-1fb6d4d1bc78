package com.ideas.tetris.ui.modules.marketsegment.common.view;

import com.ideas.tetris.pacman.services.marketsegment.entity.ForecastActivityType;
import com.ideas.tetris.pacman.services.marketsegment.service.AnalyticalMarketSegmentAttribute;
import com.ideas.tetris.pacman.services.product.Product;

import static com.ideas.tetris.pacman.services.marketsegment.service.AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR;
import static com.ideas.tetris.pacman.services.marketsegment.service.AnalyticalMarketSegmentAttribute.GROUP;
import static com.ideas.tetris.pacman.services.marketsegment.service.AnalyticalMarketSegmentAttribute.MarketSegmentLinkType;
import static com.ideas.tetris.pacman.services.marketsegment.service.AnalyticalMarketSegmentAttribute.MarketSegmentLinkType.LINKED;
import static com.ideas.tetris.pacman.services.marketsegment.service.AnalyticalMarketSegmentAttribute.MarketSegmentLinkType.NONLINKED;
import static com.ideas.tetris.pacman.services.marketsegment.service.AnalyticalMarketSegmentAttribute.MarketSegmentYieldType;
import static com.ideas.tetris.pacman.services.marketsegment.service.AnalyticalMarketSegmentAttribute.MarketSegmentYieldType.NONYIELDABLE;
import static com.ideas.tetris.pacman.services.marketsegment.service.AnalyticalMarketSegmentAttribute.MarketSegmentYieldType.SEMIYIELDABLE;
import static com.ideas.tetris.pacman.services.marketsegment.service.AnalyticalMarketSegmentAttribute.MarketSegmentYieldType.YIELDABLE;
import static com.ideas.tetris.pacman.services.marketsegment.service.AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_NONYIELDABLE;
import static com.ideas.tetris.pacman.services.marketsegment.service.AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_SEMIYIELDABLE;
import static com.ideas.tetris.pacman.services.marketsegment.service.AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_LINKED_YIELDABLE;
import static com.ideas.tetris.pacman.services.marketsegment.service.AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_NOTLINKED_NONYIELDABLE;
import static com.ideas.tetris.pacman.services.marketsegment.service.AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_NOTLINKED_SEMIYIELDABLE;
import static com.ideas.tetris.pacman.services.marketsegment.service.AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE;
import static com.ideas.tetris.pacman.services.marketsegment.service.AnalyticalMarketSegmentAttribute.TRANSIENT_BLOCK_NON_LINKED;

public class AttributeBean {

    private BusinessTypeAttribute businessType;
    private AnalyticalMarketSegmentAttribute unqualifiedAttribute;
    private MarketSegmentLinkType linked;
    private MarketSegmentYieldType yieldable;
    private ForecastActivityType forecastActivityType;
    private boolean complimentary;
    private Product product;

    public BusinessTypeAttribute getBusinessType() {
        return businessType;
    }

    public void setBusinessType(BusinessTypeAttribute businessType) {
        this.businessType = businessType;
    }

    public AnalyticalMarketSegmentAttribute getUnqualifiedAttribute() {
        return unqualifiedAttribute;
    }

    public void setUnqualifiedAttribute(AnalyticalMarketSegmentAttribute unqualifiedAttribute) {
        this.unqualifiedAttribute = unqualifiedAttribute;
    }

    public MarketSegmentLinkType getLinked() {
        return linked;
    }

    public void setLinked(MarketSegmentLinkType linked) {
        this.linked = linked;
    }

    public MarketSegmentYieldType getYieldable() {
        return yieldable;
    }

    public void setYieldable(MarketSegmentYieldType yieldable) {
        this.yieldable = yieldable;
    }

    public ForecastActivityType getForecastActivityType() {
        return forecastActivityType;
    }

    public void setForecastActivityType(ForecastActivityType forecastActivityType) {
        this.forecastActivityType = forecastActivityType;
    }

    public boolean isComplimentary() {
        return complimentary;
    }

    public void setComplimentary(boolean complimentary) {
        this.complimentary = complimentary;
    }

    public Product getProduct() {
        return product;
    }

    public void setProduct(Product product) {
        this.product = product;
    }

    public boolean isGroup() {
        return businessType != null && businessType == BusinessTypeAttribute.GROUP;
    }

    public boolean isUnQualified() {
        return businessType != null && businessType == BusinessTypeAttribute.UNQUALIFIED;
    }

    public boolean isQualified() {
        return businessType != null && businessType == BusinessTypeAttribute.QUALIFIED_NON_BLOCK;
    }

    public boolean isTransientBlock() {
        return businessType != null && businessType == BusinessTypeAttribute.TRANSIENT_BLOCK;
    }

    public boolean isLinked() {
        return linked != null && linked == LINKED;
    }

    public boolean isUnQualifiedOrQualifiedLinked() {
        return isUnQualified() || isQualifiedLinked();
    }

    public boolean isUnQualifiedOrQualified() {
        return isUnQualified() || isQualified();
    }

    public boolean isSemiOrNonYieldable() {
        return isSemiYieldable() || isNonYieldable();
    }

    public boolean isNonYieldable() {
        return yieldable != null && yieldable == NONYIELDABLE;
    }

    public boolean isSemiYieldable() {
        return yieldable != null && yieldable == SEMIYIELDABLE;
    }

    public boolean isYieldable() {
        return yieldable != null && yieldable == YIELDABLE;
    }

    public boolean isQualifiedLinked() {
        return isQualified() && isLinked();
    }

    public boolean isQualifiedLinkedYieldable() {
        return isQualifiedLinked() && isYieldable();
    }

    public boolean isQualifiedLinkedSemiYieldable() {
        return isQualifiedLinked() && isSemiYieldable();
    }

    public boolean isQualifiedLinkedNonYieldable() {
        return isQualifiedLinked() && isNonYieldable();
    }

    public boolean isQualifiedNonLinked() {
        return isQualified() && !isLinked();
    }

    public boolean isQualifiedNonLinkedYieldable() {
        return isQualifiedNonLinked() && isYieldable();
    }

    public boolean isQualifiedNonLinkedSemiYieldable() {
        return isQualifiedNonLinked() && isSemiYieldable();
    }

    public boolean isQualifiedNonLinkedNonYieldable() {
        return isQualifiedNonLinked() && isNonYieldable();
    }

    public boolean isEqualTo() {
        return getAttribute() == EQUAL_TO_BAR;
    }

    public boolean isDemandAndWash() {
        return getForecastActivityType() != null && getForecastActivityType().getId() == 1;
    }

    public boolean isNone() {
        return getForecastActivityType() != null && getForecastActivityType().getId() == 3;
    }


    public AnalyticalMarketSegmentAttribute getAttribute() {
        if (isGroup()) {
            return GROUP;
        }

        if (isUnQualified()) {
            return unqualifiedAttribute;
        }

        if (isTransientBlock()) {
            return TRANSIENT_BLOCK_NON_LINKED;
        }

        if (isQualifiedLinkedYieldable()) {
            return QUALIFIED_NONBLOCK_LINKED_YIELDABLE;
        }

        if (isQualifiedLinkedSemiYieldable()) {
            return QUALIFIED_NONBLOCK_LINKED_SEMIYIELDABLE;
        }

        if (isQualifiedLinkedNonYieldable()) {
            return QUALIFIED_NONBLOCK_LINKED_NONYIELDABLE;
        }

        if (isQualifiedNonLinkedYieldable()) {
            return QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE;
        }

        if (isQualifiedNonLinkedSemiYieldable()) {
            return QUALIFIED_NONBLOCK_NOTLINKED_SEMIYIELDABLE;
        }

        if (isQualifiedNonLinkedNonYieldable()) {
            return QUALIFIED_NONBLOCK_NOTLINKED_NONYIELDABLE;
        }

        return null;
    }

    public void setAttribute(AnalyticalMarketSegmentAttribute attribute) {
        if (attribute == null) {
            return;
        }
        if (AnalyticalMarketSegmentAttribute.GROUP.equals(attribute)) {
            businessType = BusinessTypeAttribute.GROUP;
        } else if (!attribute.getQualified()) {
            businessType = BusinessTypeAttribute.UNQUALIFIED;
            unqualifiedAttribute = attribute;
        } else if (AnalyticalMarketSegmentAttribute.TRANSIENT_BLOCK_LINKED.equals(attribute)
                || AnalyticalMarketSegmentAttribute.TRANSIENT_BLOCK_NON_LINKED.equals(attribute)) {
            businessType = BusinessTypeAttribute.TRANSIENT_BLOCK;
        } else if (attribute.getQualified()) {
            businessType = BusinessTypeAttribute.QUALIFIED_NON_BLOCK;
            linked = attribute.getLinkType().getId() == 0 ? NONLINKED : LINKED;
            yieldable = attribute.getYieldType();
        }
    }
}
