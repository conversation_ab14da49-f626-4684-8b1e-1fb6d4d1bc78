package com.ideas.tetris.ui.modules.budget.BudgetEntry;

import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.common.utils.systemconfig.SystemConfig;
import com.ideas.tetris.pacman.services.budget.BudgetDataService;
import com.ideas.tetris.pacman.services.budget.BudgetService;
import com.ideas.tetris.pacman.services.budget.dto.BudgetDataDto;
import com.ideas.tetris.pacman.services.budget.dto.BudgetDataValidationError;
import com.ideas.tetris.pacman.services.budget.dto.BudgetType;
import com.ideas.tetris.pacman.services.budget.dto.PercentagePattern;
import com.ideas.tetris.pacman.services.budget.entity.BudgetData;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.fiscalCalendar.entity.FiscalCalendar;
import com.ideas.tetris.pacman.services.fiscalCalendar.service.FiscalCalendarService;
import com.ideas.tetris.pacman.services.marketsegment.entity.BusinessType;
import com.ideas.tetris.pacman.services.property.PropertyService;
import com.ideas.tetris.platform.common.time.JavaLocalDateInterval;
import com.ideas.tetris.platform.common.time.JavaLocalDateUtils;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import com.ideas.tetris.spring.SpringAutowired;
import com.ideas.tetris.ui.common.cdi.TetrisPresenter;
import com.ideas.tetris.ui.common.util.DateFormatUtil;
import com.ideas.tetris.ui.common.util.UiUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.InputStream;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.text.ParseException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.TextStyle;
import java.util.*;


@SpringAutowired
public abstract class AbstractBudgetUserForecastEntryPresenter<T extends AbstractBudgetUserForecastEntryView> extends TetrisPresenter<T, Void> {


    protected static final double MAX_ROOM_REVENUE_VALUE = 99999999999999.99;
    protected static final int MAX_ROOM_SOLDS = 9999;
    protected static final String BUDGET_WEEK1 = "budget.week1";
    protected static final String BUDGET_WEEKLY_TOTAL = "budget.weekly.total";
    protected static final String BUDGET_MONTHLY_TOTAL = "budget.monthly.total";
    protected static final String BUDGET_TOTAL = "budget.total";
    protected static final String BUDGET_MONTH = "budget.month";
    protected static final String TRANSIENT = "transient";
    protected static final String GROUP = "group";
    protected static final String SPACE = " ";

    @Autowired
    protected BudgetDataService budgetDataService;

    @Autowired
    protected BudgetService budgetService;

    @Autowired
    protected DateService dateService;

    @Autowired
    protected PacmanConfigParamsService pacmanConfigParamsService;

    @Autowired
    protected FiscalCalendarService fiscalCalendarService;

    @Autowired
    protected PropertyService propertyService;


    public JavaLocalDateInterval getDateRangeForMonth(LocalDate date) {
        LocalDate startDate;
        LocalDate endDate;
        if (isFiscalCalendarEnabled()) {
            FiscalCalendar calendar;
            calendar = fiscalCalendarService.getFiscalMonth(date.getYear(), date.getMonthValue());
            startDate = JavaLocalDateUtils.toJavaLocalDate(calendar.getStartDate());
            endDate = JavaLocalDateUtils.toJavaLocalDate(calendar.getEndDate());
        } else {
            startDate = date.withDayOfMonth(1);
            endDate = date.withDayOfMonth(date.lengthOfMonth());
        }
        return new JavaLocalDateInterval(startDate, endDate);
    }

    public boolean isFiscalCalendarEnabled() {
        return UiUtils.isFiscalCalendarEnabled();
    }

    public LocalDate getCurrentMonthSelection(LocalDate currentDate) {
        //This will determine what the current month is based on whether the fiscal calendar is enabled or not.
        LocalDate selectedDate;
        FiscalCalendar calendar = fiscalCalendarService.getCurrentFiscalMonthByDate(JavaLocalDateUtils.toJodaLocalDate(currentDate));
        if (calendar != null) {
            return LocalDate.of(calendar.getFiscalCalendarYear(), calendar.getFiscalCalendarMonth(), 1);
        } else {
            return currentDate;
        }
    }


    protected LocalDate getBusinessDate() {
        return getSystemDateAsJavaLocalDate();
    }

    public List<BudgetDataValidationError> validateBudgetData(InputStream inputStream) {
        List<BudgetDataValidationError> results = new ArrayList<>();
        List<String> columnOrder = getColumnOrder();
        return budgetDataService.validateBudgetData(results, inputStream, columnOrder, getSheetName());
    }

    public abstract List<String> getColumnOrder();

    public abstract String getSheetName();

    protected abstract void reloadBudgetUserForecastDataForCurrentMonth(JavaLocalDateInterval dateRange);

    public List<BudgetDataDto> loadBudgetUserForecastDataFromWorkBook(InputStream inputStream) {
        return budgetDataService.getBudgetUserForecastDataFromWorkbook(inputStream, getSheetName());
    }

    protected abstract String loadBudgetUserForecastDataIntoPacman(List<BudgetDataDto> budgetDataDtoList);

    public String getExcelDateFormat() {
        return dateService.getUserDateFormat();
    }

    protected abstract List<BudgetDataDto> getBudgetUserForecastData(LocalDate startDate, LocalDate endDate);

    protected Map<Integer, String> getBusinessTypeMap() {
        Map<Integer, String> businessTypeMap = new HashMap<>();
        List<BusinessType> businessTypes = budgetDataService.getBusinessType();
        for (BusinessType businessType : businessTypes) {
            businessTypeMap.put(businessType.getId(), businessType.getName());
        }
        return businessTypeMap;
    }

    public void loadBudgetDataForTheCurrentMonth(LocalDate startDate, LocalDate endDate) {
        Set<Integer> weeksForDateRange = DateUtil.getWeeksForDateRange(JavaLocalDateUtils.toDate(startDate), JavaLocalDateUtils.toDate(endDate));
        view.updateContainer(getBudgetDataForWeekAndMonth(weeksForDateRange, JavaLocalDateUtils.toDate(startDate), JavaLocalDateUtils.toDate(endDate)));
    }

    protected Date addDaysToDate(Date date, int days) {
        return DateUtil.addDaysToDate(date, days);
    }

    protected Date getStartDate(LocalDate selectedDay) {
        return JavaLocalDateUtils.toDate(LocalDate.of(selectedDay.getYear(), selectedDay.getMonthValue(), 1));
    }

    private int getNumberOfDaysForAWeek(Date dowDate, Date endDate) {
        Long totalNoOfDays = new Long(DateUtil.getTotalNoOfDays(dowDate, endDate));
        return totalNoOfDays >= 7 ? 7 : totalNoOfDays.intValue();
    }

    protected List<BudgetDataEntryUIWrapper> getBudgetDataForWeekAndMonth(Set<Integer> weeksForDateRange, Date startDate, Date endDate) {
        List<BudgetDataEntryUIWrapper> parentWrapper = new ArrayList<>();
        List<BudgetDataEntryUIWrapper> weeklyWrapper = new ArrayList<>();
        BudgetDataEntryUIWrapper childWrapper = new BudgetDataEntryUIWrapper(getText(BUDGET_MONTH), null, getText(BUDGET_MONTHLY_TOTAL), BigInteger.ZERO,
                BigDecimal.ZERO, Boolean.FALSE, getText(BUDGET_MONTHLY_TOTAL), 0);
        parentWrapper.add(childWrapper);
        Date dowDate = startDate;
        for (int i = 0; i <= weeksForDateRange.size(); i++) {
            if (!childWrapper.hasChildren()) {
                childWrapper.addChildren(new BudgetDataEntryUIWrapper(null, null, getText(TRANSIENT), BigInteger.ZERO,
                        BigDecimal.ZERO, Boolean.TRUE, getText(BUDGET_MONTHLY_TOTAL), 0));
                childWrapper.addChildren(new BudgetDataEntryUIWrapper(null, null, getText(GROUP), BigInteger.ZERO,
                        BigDecimal.ZERO, Boolean.TRUE, getText(BUDGET_MONTHLY_TOTAL), 0));
                parentWrapper.addAll(childWrapper.getChildren());
            } else {
                if (startDate.after(endDate)) {
                    break;
                }
                BudgetDataEntryUIWrapper child = new BudgetDataEntryUIWrapper(getText(BUDGET_WEEK1) + i, null, getText(BUDGET_WEEKLY_TOTAL), BigInteger.ZERO,
                        BigDecimal.ZERO, Boolean.FALSE, getText(BUDGET_WEEK1) + i, 0);
                weeklyWrapper.add(child);
                int numberOfDaysForAWeek = getNumberOfDaysForAWeek(dowDate, endDate);
                child.addChildren(new BudgetDataEntryUIWrapper(null, null, getText(TRANSIENT), BigInteger.ZERO,
                        BigDecimal.ZERO, Boolean.TRUE, getText(BUDGET_WEEK1) + i, numberOfDaysForAWeek));
                child.addChildren(new BudgetDataEntryUIWrapper(null, null, getText(GROUP), BigInteger.ZERO,
                        BigDecimal.ZERO, Boolean.TRUE, getText(BUDGET_WEEK1) + i, numberOfDaysForAWeek));
                for (int j = 0; j < 7; j++) {
                    dowDate = addDaysToDate(startDate, j);
                    if (dowDate.after(endDate)) {
                        break;
                    }

                    child.addChildren(new BudgetDataEntryUIWrapper(null, String.format("%s, %s", getText(DateUtil.getDayOfWeekShortName(dowDate).toLowerCase()), formatStandard(dowDate)),
                            getText(BUDGET_TOTAL), BigInteger.ZERO, BigDecimal.ZERO, Boolean.FALSE, getText(BUDGET_WEEK1) + i, 0));
                    child.addChildren(new BudgetDataEntryUIWrapper(null, null, getText(TRANSIENT), BigInteger.ZERO,
                            BigDecimal.ZERO, Boolean.FALSE, getText(BUDGET_WEEK1) + i, DateUtil.getDateForDate(dowDate)));
                    child.addChildren(new BudgetDataEntryUIWrapper(null, null, getText(GROUP), BigInteger.ZERO,
                            BigDecimal.ZERO, Boolean.FALSE, getText(BUDGET_WEEK1) + i, DateUtil.getDateForDate(dowDate)));
                }
                startDate = addDaysToDate(dowDate, 1);
                weeklyWrapper.addAll(child.getChildren());
            }
        }
        parentWrapper.addAll(weeklyWrapper);
        return parentWrapper;
    }

    protected String formatStandard(Date dowDate) {
        return DateFormatUtil.formatStandard(dowDate);
    }

    public Date getProposedStartDate() {
        return dateService.getCurrentDate();
    }

    public String getPropertyName() {
        return budgetDataService.getProperty().getName();
    }

    public Date getLastDayOfYear() {
        Calendar cal = Calendar.getInstance();
        cal.setTime(getProposedStartDate());
        cal.set(cal.get(Calendar.YEAR), Calendar.DECEMBER, 31);
        return cal.getTime();
    }

    protected List<BigInteger> distributeBudgetedRooms(JavaLocalDateInterval localDateInterval, BigInteger budgetedRoomsNewValue, List<BudgetDataEntryUIWrapper> budgetDataEntryUIWrapper,
                                                       String businessType, int weekDays, String distributionLevel, BigInteger budgetedRoomsOldValue) {
        boolean isDistributionLevelMonthly = StringUtils.equalsIgnoreCase(getText(BUDGET_MONTHLY_TOTAL), distributionLevel);
        int totalNumberOfDays = new Long(getTotalNumberOfDays(localDateInterval, weekDays, isDistributionLevelMonthly)).intValue();
        List<BigInteger> distributeBudgetedRooms = distributeBudgetedRooms(budgetedRoomsNewValue.intValue(), totalNumberOfDays);
        if (isDistributionLevelMonthly) {
            view.updateContainer(getBudgetRoomsDataForMonth(distributeBudgetedRooms,
                    budgetedRoomsNewValue, businessType, budgetDataEntryUIWrapper));
        } else {
            view.updateContainer(getBudgetRoomsDataForWeek(distributeBudgetedRooms, budgetedRoomsNewValue, businessType,
                    budgetDataEntryUIWrapper, budgetedRoomsOldValue, distributionLevel));
        }
        return distributeBudgetedRooms;
    }

    private List<BudgetDataEntryUIWrapper> getBudgetRoomsDataForWeek(List<BigInteger> distributeBudgetedRooms, BigInteger budgetedRoomsNewValue, String businessType,
                                                                     List<BudgetDataEntryUIWrapper> budgetDataEntryUIWrapper, BigInteger budgetedRoomsOldValue, String distributionLevel) {
        List<BudgetDataEntryUIWrapper> dataEntryUIWrappers = new ArrayList<>(budgetDataEntryUIWrapper);
        boolean isTransient = isaType(getText(TRANSIENT), businessType);

        //Weekly Total for Transient and Group
        BigInteger weeklyOverrideDifference = getRoomsWeeklyOverrideDifference(budgetedRoomsNewValue, budgetedRoomsOldValue);
        dataEntryUIWrappers.get(0).isUpdated(true);
        if (isTransient) {
            BudgetDataEntryUIWrapper entryUIWrapper = dataEntryUIWrappers.get(1);
            entryUIWrapper.setBudgetedRooms(entryUIWrapper.getBudgetedRooms().add(weeklyOverrideDifference));
            dataEntryUIWrappers.get(0).setBudgetedRooms(entryUIWrapper.getBudgetedRooms().add(dataEntryUIWrappers.get(2).getBudgetedRooms()));

        } else {
            BudgetDataEntryUIWrapper entryUIWrapper = dataEntryUIWrappers.get(2);
            entryUIWrapper.setBudgetedRooms(entryUIWrapper.getBudgetedRooms().add(weeklyOverrideDifference));
            dataEntryUIWrappers.get(0).setBudgetedRooms(entryUIWrapper.getBudgetedRooms().add(dataEntryUIWrappers.get(1).getBudgetedRooms()));
        }

        int distributedRoomIndex = 0;
        for (int i = 3; i < dataEntryUIWrappers.size(); i++) {
            BudgetDataEntryUIWrapper entryUIWrapper = dataEntryUIWrappers.get(i);
            String weeklyBusinessType = entryUIWrapper.getBusinessType();
            if (isaType(entryUIWrapper.getDistributionLevel(), distributionLevel) && isaType(getText(BUDGET_TOTAL), weeklyBusinessType) && isTransient) {
                BudgetDataEntryUIWrapper dowTransient = dataEntryUIWrappers.get(i + 1);
                dowTransient.setBudgetedRooms(distributeBudgetedRooms.get(distributedRoomIndex));
                entryUIWrapper.setBudgetedRooms(dowTransient.getBudgetedRooms().add(dataEntryUIWrappers.get(i + 2).getBudgetedRooms()));
                distributedRoomIndex++;
            } else if (isaType(entryUIWrapper.getDistributionLevel(), distributionLevel) && isaType(getText(BUDGET_TOTAL), weeklyBusinessType) && !isTransient) {
                BudgetDataEntryUIWrapper dowGroup = dataEntryUIWrappers.get(i + 2);
                dowGroup.setBudgetedRooms(distributeBudgetedRooms.get(distributedRoomIndex));
                entryUIWrapper.setBudgetedRooms(dowGroup.getBudgetedRooms().add(dataEntryUIWrappers.get(i + 1).getBudgetedRooms()));
                distributedRoomIndex++;
            } else if (isaType(entryUIWrapper.getDistributionLevel(), distributionLevel) && isaType(getText(BUDGET_WEEKLY_TOTAL), weeklyBusinessType) && isTransient) {
                BudgetDataEntryUIWrapper weeklyLevel = dataEntryUIWrappers.get(i + 1);
                weeklyLevel.setBudgetedRooms(budgetedRoomsNewValue);
                entryUIWrapper.setBudgetedRooms(weeklyLevel.getBudgetedRooms().add(dataEntryUIWrappers.get(i + 2).getBudgetedRooms()));
            } else if (isaType(entryUIWrapper.getDistributionLevel(), distributionLevel) && isaType(getText(BUDGET_WEEKLY_TOTAL), weeklyBusinessType) && !isTransient) {
                BudgetDataEntryUIWrapper weeklyLevel = dataEntryUIWrappers.get(i + 2);
                weeklyLevel.setBudgetedRooms(budgetedRoomsNewValue);
                entryUIWrapper.setBudgetedRooms(dataEntryUIWrappers.get(i + 1).getBudgetedRooms().add(weeklyLevel.getBudgetedRooms()));
            }
        }
        return dataEntryUIWrappers;
    }

    private BigInteger getRoomsWeeklyOverrideDifference(BigInteger budgetedRooms, BigInteger budgetedRoomsOldValue) {
        return budgetedRooms.subtract(budgetedRoomsOldValue);
    }

    private BigDecimal getRevenueWeeklyOverrideDifference(BigDecimal budgetedRooms, BigDecimal budgetedRoomsOldValue) {
        return budgetedRooms.subtract(budgetedRoomsOldValue);
    }

    protected List<BigDecimal> distributeBudgetedRevenue(JavaLocalDateInterval localDateInterval, List<BudgetDataEntryUIWrapper> budgetDataEntryUIWrapper, BigDecimal budgetedRevenueNewValue,
                                                         String businessType, int weekDays, String distributionLevel, BigDecimal budgetedRevenueOldValue) {
        boolean isDistributionLevelMonthly = StringUtils.equalsIgnoreCase(getText(BUDGET_MONTHLY_TOTAL), distributionLevel);
        int totalNumberOfDays = new Long(getTotalNumberOfDays(localDateInterval, weekDays, isDistributionLevelMonthly)).intValue();
        List<BigDecimal> distributeBudgetedRevenue = distributeBudgetedRevenue(budgetedRevenueNewValue, totalNumberOfDays);
        if (isDistributionLevelMonthly) {
            view.updateContainer(getBudgetRevenueDataForMonth(distributeBudgetedRevenue,
                    budgetedRevenueNewValue, businessType, budgetDataEntryUIWrapper));
        } else {
            view.updateContainer(getBudgetRevenueDataForWeek(distributeBudgetedRevenue, budgetedRevenueNewValue, businessType, budgetDataEntryUIWrapper, budgetedRevenueOldValue, distributionLevel));
        }
        return distributeBudgetedRevenue;
    }

    private List<BudgetDataEntryUIWrapper> getBudgetRevenueDataForWeek(List<BigDecimal> distributeBudgetedRevenue, BigDecimal budgetedRevenueNewValue, String businessType,
                                                                       List<BudgetDataEntryUIWrapper> budgetDataEntryUIWrapper, BigDecimal budgetedRevenueOldValue, String distributionLevel) {
        List<BudgetDataEntryUIWrapper> dataEntryUIWrappers = new ArrayList<>(budgetDataEntryUIWrapper);
        boolean isTransient = isaType(getText(TRANSIENT), businessType);

        //Weekly Total for Transient and Group
        BigDecimal weeklyOverrideDifference = getRevenueWeeklyOverrideDifference(budgetedRevenueNewValue, budgetedRevenueOldValue);
        dataEntryUIWrappers.get(0).isUpdated(true);
        if (isTransient) {
            BudgetDataEntryUIWrapper entryUIWrapper = dataEntryUIWrappers.get(1);
            entryUIWrapper.setBudgetedRevenue(entryUIWrapper.getBudgetedRevenue().add(weeklyOverrideDifference));
            dataEntryUIWrappers.get(0).setBudgetedRevenue(entryUIWrapper.getBudgetedRevenue().add(dataEntryUIWrappers.get(2).getBudgetedRevenue()));
        } else {
            BudgetDataEntryUIWrapper entryUIWrapper = dataEntryUIWrappers.get(2);
            entryUIWrapper.setBudgetedRevenue(entryUIWrapper.getBudgetedRevenue().add(weeklyOverrideDifference));
            dataEntryUIWrappers.get(0).setBudgetedRevenue(entryUIWrapper.getBudgetedRevenue().add(dataEntryUIWrappers.get(1).getBudgetedRevenue()));
        }

        int distributedRoomIndex = 0;
        for (int i = 3; i < dataEntryUIWrappers.size(); i++) {
            BudgetDataEntryUIWrapper entryUIWrapper = dataEntryUIWrappers.get(i);
            String weeklyBusinessType = entryUIWrapper.getBusinessType();
            if (isaType(entryUIWrapper.getDistributionLevel(), distributionLevel) && isaType(getText(BUDGET_TOTAL), weeklyBusinessType) && isTransient) {
                BudgetDataEntryUIWrapper dowTransient = dataEntryUIWrappers.get(i + 1);
                dowTransient.setBudgetedRevenue(distributeBudgetedRevenue.get(distributedRoomIndex));
                entryUIWrapper.setBudgetedRevenue(dowTransient.getBudgetedRevenue().add(dataEntryUIWrappers.get(i + 2).getBudgetedRevenue()));
                distributedRoomIndex++;
            } else if (isaType(entryUIWrapper.getDistributionLevel(), distributionLevel) && isaType(getText(BUDGET_TOTAL), weeklyBusinessType) && !isTransient) {
                BudgetDataEntryUIWrapper dowGroup = dataEntryUIWrappers.get(i + 2);
                dowGroup.setBudgetedRevenue(distributeBudgetedRevenue.get(distributedRoomIndex));
                entryUIWrapper.setBudgetedRevenue(dowGroup.getBudgetedRevenue().add(dataEntryUIWrappers.get(i + 1).getBudgetedRevenue()));
                distributedRoomIndex++;
            } else if (isaType(entryUIWrapper.getDistributionLevel(), distributionLevel) && isaType(getText(BUDGET_WEEKLY_TOTAL), weeklyBusinessType) && isTransient) {
                BudgetDataEntryUIWrapper weeklyLevel = dataEntryUIWrappers.get(i + 1);
                weeklyLevel.setBudgetedRevenue(budgetedRevenueNewValue);
                entryUIWrapper.setBudgetedRevenue(weeklyLevel.getBudgetedRevenue().add(dataEntryUIWrappers.get(i + 2).getBudgetedRevenue()));
            } else if (isaType(entryUIWrapper.getDistributionLevel(), distributionLevel) && isaType(getText(BUDGET_WEEKLY_TOTAL), weeklyBusinessType) && !isTransient) {
                BudgetDataEntryUIWrapper weeklyLevel = dataEntryUIWrappers.get(i + 2);
                weeklyLevel.setBudgetedRevenue(budgetedRevenueNewValue);
                entryUIWrapper.setBudgetedRevenue(dataEntryUIWrappers.get(i + 1).getBudgetedRevenue().add(weeklyLevel.getBudgetedRevenue()));
            }
        }
        return dataEntryUIWrappers;
    }

    private List<BudgetDataEntryUIWrapper> getBudgetRevenueDataForMonth(List<BigDecimal> distributeBudgetedRevenue,
                                                                        BigDecimal budgetedRevenue, String businessType, List<BudgetDataEntryUIWrapper> budgetDataEntryUIWrapper) {
        List<BudgetDataEntryUIWrapper> dataEntryUIWrappers = new ArrayList<>(budgetDataEntryUIWrapper);
        boolean isTransient = isaType(getText(TRANSIENT), businessType);
        Map<String, BigDecimal> weeklyTotal = computeBudgetedRevenueWeeklyTotal(distributeBudgetedRevenue);

        //Monthly Total for Transient and Group
        dataEntryUIWrappers.get(0).isUpdated(true);
        if (isTransient) {
            dataEntryUIWrappers.get(0).setBudgetedRevenue(budgetedRevenue.add(budgetDataEntryUIWrapper.get(2).getBudgetedRevenue()));
            dataEntryUIWrappers.get(1).setBudgetedRevenue(budgetedRevenue);
        } else {
            dataEntryUIWrappers.get(0).setBudgetedRevenue(budgetedRevenue.add(budgetDataEntryUIWrapper.get(1).getBudgetedRevenue()));
            dataEntryUIWrappers.get(2).setBudgetedRevenue(budgetedRevenue);
        }

        int distributedRoomIndex = 0;
        for (int i = 3; i < dataEntryUIWrappers.size(); i++) {
            BudgetDataEntryUIWrapper entryUIWrapper = dataEntryUIWrappers.get(i);
            String weeklyBusinessType = entryUIWrapper.getBusinessType();
            if (isaType(getText(BUDGET_TOTAL), weeklyBusinessType) && isTransient) {
                BudgetDataEntryUIWrapper dowTransient = dataEntryUIWrappers.get(i + 1);
                dowTransient.setBudgetedRevenue(distributeBudgetedRevenue.get(distributedRoomIndex));
                entryUIWrapper.setBudgetedRevenue(dowTransient.getBudgetedRevenue().add(dataEntryUIWrappers.get(i + 2).getBudgetedRevenue()));
                distributedRoomIndex++;
            } else if (isaType(getText(BUDGET_TOTAL), weeklyBusinessType) && !isTransient) {
                BudgetDataEntryUIWrapper dowGroup = dataEntryUIWrappers.get(i + 2);
                dowGroup.setBudgetedRevenue(distributeBudgetedRevenue.get(distributedRoomIndex));
                entryUIWrapper.setBudgetedRevenue(dowGroup.getBudgetedRevenue().add(dataEntryUIWrappers.get(i + 1).getBudgetedRevenue()));
                distributedRoomIndex++;
            } else if (isaType(getText(BUDGET_WEEKLY_TOTAL), weeklyBusinessType) && isTransient) {
                BudgetDataEntryUIWrapper weeklyLevel = dataEntryUIWrappers.get(i + 1);
                weeklyLevel.setBudgetedRevenue(weeklyTotal.get(entryUIWrapper.getWeek()));
                entryUIWrapper.setBudgetedRevenue(weeklyLevel.getBudgetedRevenue().add(dataEntryUIWrappers.get(i + 2).getBudgetedRevenue()));
            } else if (isaType(getText(BUDGET_WEEKLY_TOTAL), weeklyBusinessType) && !isTransient) {
                BudgetDataEntryUIWrapper weeklyLevel = dataEntryUIWrappers.get(i + 2);
                weeklyLevel.setBudgetedRevenue(weeklyTotal.get(entryUIWrapper.getWeek()));
                entryUIWrapper.setBudgetedRevenue(dataEntryUIWrappers.get(i + 1).getBudgetedRevenue().add(weeklyLevel.getBudgetedRevenue()));
            }
        }
        return dataEntryUIWrappers;
    }

    private List<BudgetDataEntryUIWrapper> getBudgetRoomsDataForMonth(List<BigInteger> distributeBudgetedRooms, BigInteger budgetedRooms,
                                                                      String businessType, List<BudgetDataEntryUIWrapper> budgetDataEntryUIWrapper) {
        List<BudgetDataEntryUIWrapper> dataEntryUIWrappers = new ArrayList<>(budgetDataEntryUIWrapper);
        boolean isTransient = isaType(getText(TRANSIENT), businessType);
        Map<String, BigInteger> weeklyTotal = computeBudgetedRoomsWeeklyTotal(distributeBudgetedRooms);
        dataEntryUIWrappers.get(0).isUpdated(true);
        //Monthly Total for Transient and Group
        if (isTransient) {
            dataEntryUIWrappers.get(0).setBudgetedRooms(budgetedRooms.add(budgetDataEntryUIWrapper.get(2).getBudgetedRooms()));
            dataEntryUIWrappers.get(1).setBudgetedRooms(budgetedRooms);
        } else {
            dataEntryUIWrappers.get(0).setBudgetedRooms(budgetedRooms.add(budgetDataEntryUIWrapper.get(1).getBudgetedRooms()));
            dataEntryUIWrappers.get(2).setBudgetedRooms(budgetedRooms);
        }

        int distributedRoomIndex = 0;
        for (int i = 3; i < dataEntryUIWrappers.size(); i++) {
            BudgetDataEntryUIWrapper entryUIWrapper = dataEntryUIWrappers.get(i);
            String weeklyBusinessType = entryUIWrapper.getBusinessType();
            if (isaType(getText(BUDGET_TOTAL), weeklyBusinessType) && isTransient) {
                BudgetDataEntryUIWrapper dowTransient = dataEntryUIWrappers.get(i + 1);
                dowTransient.setBudgetedRooms(distributeBudgetedRooms.get(distributedRoomIndex));
                entryUIWrapper.setBudgetedRooms(dowTransient.getBudgetedRooms().add(dataEntryUIWrappers.get(i + 2).getBudgetedRooms()));
                distributedRoomIndex++;
            } else if (isaType(getText(BUDGET_TOTAL), weeklyBusinessType) && !isTransient) {
                BudgetDataEntryUIWrapper dowGroup = dataEntryUIWrappers.get(i + 2);
                dowGroup.setBudgetedRooms(distributeBudgetedRooms.get(distributedRoomIndex));
                entryUIWrapper.setBudgetedRooms(dowGroup.getBudgetedRooms().add(dataEntryUIWrappers.get(i + 1).getBudgetedRooms()));
                distributedRoomIndex++;
            } else if (isaType(getText(BUDGET_WEEKLY_TOTAL), weeklyBusinessType) && isTransient) {
                BudgetDataEntryUIWrapper weeklyLevel = dataEntryUIWrappers.get(i + 1);
                weeklyLevel.setBudgetedRooms(weeklyTotal.get(entryUIWrapper.getWeek()));
                entryUIWrapper.setBudgetedRooms(weeklyLevel.getBudgetedRooms().add(dataEntryUIWrappers.get(i + 2).getBudgetedRooms()));
            } else if (isaType(getText(BUDGET_WEEKLY_TOTAL), weeklyBusinessType) && !isTransient) {
                BudgetDataEntryUIWrapper weeklyLevel = dataEntryUIWrappers.get(i + 2);
                weeklyLevel.setBudgetedRooms(weeklyTotal.get(entryUIWrapper.getWeek()));
                entryUIWrapper.setBudgetedRooms(dataEntryUIWrappers.get(i + 1).getBudgetedRooms().add(weeklyLevel.getBudgetedRooms()));
            }
        }
        return dataEntryUIWrappers;
    }

    private boolean isaType(String expected, String actual) {
        return StringUtils.equalsIgnoreCase(expected, actual);
    }

    private Map<String, BigDecimal> computeBudgetedRevenueWeeklyTotal(List<BigDecimal> distributeBudgetedRevenue) {
        double maxWeeks = getMaxWeeks(distributeBudgetedRevenue.size());
        Map<String, BigDecimal> weeklyTotal = new HashMap<>();
        int i = 0, j = 7;
        for (int weekno = 1; weekno <= maxWeeks; weekno++) {
            if (j > distributeBudgetedRevenue.size()) {
                j = distributeBudgetedRevenue.size();
            }

            List<BigDecimal> bigDecimalList = distributeBudgetedRevenue.subList(i, j);
            weeklyTotal.put(String.format("%s%d", getText(BUDGET_WEEK1), weekno), bigDecimalList.stream().reduce(BigDecimal.ZERO, BigDecimal::add));
            i += 7;
            j += 7;
        }
        return weeklyTotal;
    }

    double getMaxWeeks(int size) {
        return Math.ceil(size / 7.0);
    }

    private Map<String, BigInteger> computeBudgetedRoomsWeeklyTotal(List<BigInteger> distributeBudgetedRooms) {
        double maxWeeks = getMaxWeeks(distributeBudgetedRooms.size());
        Map<String, BigInteger> weeklyTotal = new HashMap<>();
        int i = 0, j = 7;
        for (int weekno = 1; weekno <= maxWeeks; weekno++) {
            if (j > distributeBudgetedRooms.size()) {
                j = distributeBudgetedRooms.size();
            }
            List<BigInteger> bigIntegerList = distributeBudgetedRooms.subList(i, j);
            weeklyTotal.put(String.format("%s%d", getText(BUDGET_WEEK1), weekno), bigIntegerList.stream().reduce(BigInteger.ZERO, BigInteger::add));
            i += 7;
            j += 7;
        }
        return weeklyTotal;
    }

    protected List<BigDecimal> distributeBudgetedRevenue(BigDecimal budgetedRevenue, int totalNumberOfDays) {
        List<BigDecimal> budgetedRoomsPerOccupancyDate = new ArrayList<>();
        BigDecimal remainingDays = new BigDecimal(totalNumberOfDays);
        BigDecimal remainingBudgetedRevenue = budgetedRevenue;
        while (totalNumberOfDays > 0) {
            BigDecimal revenueForCurrentDay = remainingBudgetedRevenue.divide(remainingDays, 2, 1);
            budgetedRoomsPerOccupancyDate.add(revenueForCurrentDay);
            remainingBudgetedRevenue = remainingBudgetedRevenue.subtract(revenueForCurrentDay);
            remainingDays = remainingDays.subtract(BigDecimal.ONE);
            totalNumberOfDays--;
        }
        Collections.reverse(budgetedRoomsPerOccupancyDate);
        return budgetedRoomsPerOccupancyDate;
    }

    protected List<BigInteger> distributeBudgetedRooms(int budgetedRooms, int totalNumberOfDays) {
        List<BigInteger> budgetedRoomsPerOccupancyDate = new ArrayList<>();
        int remainingDays = totalNumberOfDays;
        int remainingSolds = budgetedRooms;
        while (totalNumberOfDays > 0) {
            int soldForCurrentDay = remainingSolds / remainingDays;
            budgetedRoomsPerOccupancyDate.add(BigInteger.valueOf(soldForCurrentDay));
            remainingSolds = remainingSolds - soldForCurrentDay;
            remainingDays--;
            totalNumberOfDays--;
        }
        Collections.reverse(budgetedRoomsPerOccupancyDate);
        return budgetedRoomsPerOccupancyDate;
    }

    protected long getTotalNumberOfDays(JavaLocalDateInterval localDateInterval, int weekDays, Boolean isMonthlyTotalDistribution) {
        if (!isMonthlyTotalDistribution) {
            return weekDays;
        }
        return getTotalNoOfDaysInAMonth(localDateInterval);
    }

    private long getTotalNoOfDaysInAMonth(JavaLocalDateInterval localDateInterval) {
        Date startDate = JavaLocalDateUtils.toDate(localDateInterval.getStartDate());
        Date endDate = JavaLocalDateUtils.toDate(localDateInterval.getEndDate());
        return DateUtil.getTotalNoOfDays(startDate, endDate);
    }

    public Integer getTotalHotelCapacityForMonth(JavaLocalDateInterval localDateInterval) {
        return Math.toIntExact(getTotalHotelCapacity() * getTotalNoOfDaysInAMonth(localDateInterval));
    }

    public Integer getTotalHotelCapacity() {
        return budgetDataService.getTotalHotelCapacity();
    }

    protected abstract void cancel();

    public String saveBudgetedRoomsAndRevenueData(List<BudgetDataEntryUIWrapper> dataEntryUIWrappers) throws ParseException {
        return loadBudgetUserForecastDataIntoPacman(mapDataEntryUIWrappersToBudgetDataDto(dataEntryUIWrappers));
    }

    private List<BudgetDataDto> mapDataEntryUIWrappersToBudgetDataDto(List<BudgetDataEntryUIWrapper> dataEntryUIWrappers) throws ParseException {
        List<BudgetDataDto> budgetDataDtos = new ArrayList<>();
        for (int i = 6; i < dataEntryUIWrappers.size(); i += 3) {
            String dowDate = dataEntryUIWrappers.get(i).getDowDate();
            if (null != dowDate) {
                LocalDate localDate = getLocalDateFromStringDate(dowDate);
                budgetDataDtos.add(constructBudgetDataDto(dataEntryUIWrappers.get(i + 1), localDate, Constants.BUSINESS_TYPE.TRANSIENT.getCode()));
                budgetDataDtos.add(constructBudgetDataDto(dataEntryUIWrappers.get(i + 2), localDate, Constants.BUSINESS_TYPE.GROUP.getCode()));
            }
        }
        return budgetDataDtos;
    }

    protected BudgetDataDto mapDataEntryUIWrappersToBudgetDataDto(BudgetDataEntryUIWrapper dataEntryUIWrapper) throws ParseException {
        String dowDate = dataEntryUIWrapper.getDowDate();
        if (null != dowDate) {
            LocalDate localDate = getLocalDateFromStringDate(dowDate);
            return constructBudgetDataDto(dataEntryUIWrapper, localDate, dataEntryUIWrapper.getBusinessType());
        }
        return null;
    }

    private LocalDate getLocalDateFromStringDate(String dow) throws ParseException {
        String dateString = dow.split(" ")[1];
        return DateUtil.convertJavaUtilDateToLocalDate(DateUtil.parseDate(dateString, dateService.getUserDateFormat(), UiUtils.getLocale()), true);
    }

    private BudgetDataDto constructBudgetDataDto(BudgetDataEntryUIWrapper entryUIWrapper, LocalDate localDate, String code) throws ParseException {
        BudgetDataDto budgetDataDto = new BudgetDataDto();
        budgetDataDto.setOccupancyDate(localDate);
        budgetDataDto.setRoomRevenue(entryUIWrapper.getBudgetedRevenue());
        budgetDataDto.setRoomsSold(entryUIWrapper.getBudgetedRooms().intValue());
        budgetDataDto.setSegment(code);
        return budgetDataDto;
    }

    public boolean validateBudgetData(List<BudgetDataEntryUIWrapper> dataEntryUIWrappers) throws ParseException {
        List<BudgetDataDto> budgetDataDtoList = mapDataEntryUIWrappersToBudgetDataDto(dataEntryUIWrappers);
        for (BudgetDataDto budgetDataDto : budgetDataDtoList) {
            if (budgetDataDto.getRoomsSold() < 0 || budgetDataDto.getRoomsSold() > MAX_ROOM_SOLDS) {
                return false;
            }
            if (budgetDataDto.getRoomRevenue().compareTo(new BigDecimal(SystemConfig.getMinRoomRevenueValue())) < 0 ||
                    budgetDataDto.getRoomRevenue().compareTo(new BigDecimal(MAX_ROOM_REVENUE_VALUE)) > 0) {
                return false;
            }
        }

        return true;
    }

    public boolean validateCapacityConstraint(List<BudgetDataEntryUIWrapper> dataEntryUIWrappers) throws ParseException {
        List<BudgetDataDto> budgetDataDtoList = mapDataEntryUIWrappersToBudgetDataDto(dataEntryUIWrappers);
        LocalDate startDate = null;
        LocalDate endDate = null;
        Map<LocalDate, List<BudgetDataDto>> dateMap = new HashMap<>();
        for (BudgetDataDto budgetDataDto : budgetDataDtoList) {
            LocalDate occupancyDate = budgetDataDto.getOccupancyDate();

            List<BudgetDataDto> budgetDataDtoForDate = dateMap.get(occupancyDate);
            if (budgetDataDtoForDate == null) {
                budgetDataDtoForDate = new ArrayList<>();
                dateMap.put(occupancyDate, budgetDataDtoForDate);
            }
            budgetDataDtoForDate.add(budgetDataDto);
            if (startDate == null || occupancyDate.isBefore(startDate)) {
                startDate = occupancyDate;
            }
            if (endDate == null || occupancyDate.isAfter(endDate)) {
                endDate = occupancyDate;
            }
        }

        return checkDates(startDate, endDate, dateMap);
    }

    private boolean checkDates(LocalDate startDate, LocalDate endDate, Map<LocalDate, List<BudgetDataDto>> dateMap) {
        LocalDate occupancyDate = startDate;
        int capacity = getTotalHotelCapacity();
        while (!occupancyDate.isAfter(endDate)) {
            List<BudgetDataDto> budgetDataDtos = dateMap.get(occupancyDate);
            if (budgetDataDtos != null) {
                int roomsSold = budgetDataDtos.stream().mapToInt(budgetDataDto -> budgetDataDto.getRoomsSold()).sum();
                if (roomsSold > capacity) {
                    return false;
                }
            }
            occupancyDate = occupancyDate.plusDays(1);
        }
        return true;
    }

    public BudgetData monthlyTotalBy(Integer businessType, List<BudgetData> budgetData) {
        BudgetData data = new BudgetData();
        int totalRoomSolds = budgetData.stream().filter(budget -> budget.getSegmentID().equals(businessType)).mapToInt(BudgetData::getRoomsSold).sum();
        BigDecimal totalRevenue = budgetData.stream().filter(budget -> budget.getSegmentID().equals(businessType)).map(BudgetData::getRoomRevenue).reduce(BigDecimal.ZERO, BigDecimal::add);

        data.setSegmentID(businessType);
        data.setRoomsSold(totalRoomSolds);
        data.setRoomRevenue(totalRevenue);
        return data;
    }


    protected Map<Integer, List<Date>> getWeekWithDates(LocalDate startDate, LocalDate endDate) {
        Map<Integer, List<Date>> weekWithDates = new HashMap<>();
        Calendar calendar = Calendar.getInstance();
        int days = 1;
        int weekOfMonth = 1;
        while (startDate.compareTo(endDate) <= 0) {
            calendar.setTime(JavaLocalDateUtils.toDate(startDate));

            if (days > 7) {
                weekOfMonth++;
                days = 1;
            }

            if (weekWithDates.containsKey(weekOfMonth)) {
                List<Date> dates = weekWithDates.get(weekOfMonth);
                dates.add(calendar.getTime());
            } else {
                List<Date> datesInWeek = new ArrayList<>();
                datesInWeek.add(calendar.getTime());
                weekWithDates.put(weekOfMonth, datesInWeek);
            }
            startDate = startDate.plusDays(1);
            days++;
        }
        return weekWithDates;
    }

    public void handleDayLevelRoomsOverrides(BudgetDataEntryUIWrapper budgetDataEntryUIWrapper, BigInteger overRiddenValue,
                                             List<BudgetDataEntryUIWrapper> budgetDataEntryUIWrappers) {
        boolean isOverriddenValueSet = Boolean.FALSE;
        BigInteger actualOverRiddenValue = overRiddenValue.subtract(budgetDataEntryUIWrapper.getBudgetedRooms());

        List<BudgetDataEntryUIWrapper> dataEntryUIWrappers = new ArrayList<>(budgetDataEntryUIWrappers);
        boolean isTransient = isaType(getText(TRANSIENT), budgetDataEntryUIWrapper.getBusinessType());

        BudgetDataEntryUIWrapper entryUIWrapper = dataEntryUIWrappers.get(0);
        entryUIWrapper.setBudgetedRooms(entryUIWrapper.getBudgetedRooms().add(actualOverRiddenValue));
        entryUIWrapper.isUpdated(Boolean.TRUE);

        if (isTransient) {
            BudgetDataEntryUIWrapper uiWrapper = dataEntryUIWrappers.get(1);
            uiWrapper.setBudgetedRooms(uiWrapper.getBudgetedRooms().add(actualOverRiddenValue));
        } else {
            BudgetDataEntryUIWrapper uiWrapper = dataEntryUIWrappers.get(2);
            uiWrapper.setBudgetedRooms(uiWrapper.getBudgetedRooms().add(actualOverRiddenValue));
        }

        for (int i = 3; i < dataEntryUIWrappers.size(); i++) {
            BudgetDataEntryUIWrapper dataEntryUIWrapper = dataEntryUIWrappers.get(i);
            if (isaType(budgetDataEntryUIWrapper.getParent().getWeek(), dataEntryUIWrapper.getWeek())) {
                dataEntryUIWrapper.setBudgetedRooms(dataEntryUIWrapper.getBudgetedRooms().add(actualOverRiddenValue));
                BudgetDataEntryUIWrapper entryUIWrapperDistributeLevel = isTransient ? dataEntryUIWrappers.get(i + 1) : dataEntryUIWrappers.get(i + 2);
                entryUIWrapperDistributeLevel.setBudgetedRooms(entryUIWrapperDistributeLevel.getBudgetedRooms().add(actualOverRiddenValue));
            }

            if ((budgetDataEntryUIWrapper.getWeekDays() == dataEntryUIWrapper.getWeekDays()) && isaType(budgetDataEntryUIWrapper.getBusinessType(), dataEntryUIWrapper.getBusinessType())
                    && !isOverriddenValueSet && !dataEntryUIWrapper.getAction()) {
                isOverriddenValueSet = Boolean.TRUE;
                dataEntryUIWrapper.setBudgetedRooms(overRiddenValue);
                if (isaType(getText(TRANSIENT), dataEntryUIWrapper.getBusinessType())) {
                    BudgetDataEntryUIWrapper uiWrapper = dataEntryUIWrappers.get(i - 1);
                    uiWrapper.setBudgetedRooms(uiWrapper.getBudgetedRooms().add(actualOverRiddenValue));
                } else {
                    BudgetDataEntryUIWrapper uiWrapper = dataEntryUIWrappers.get(i - 2);
                    uiWrapper.setBudgetedRooms(uiWrapper.getBudgetedRooms().add(actualOverRiddenValue));
                }
            }
        }
        view.updateContainer(dataEntryUIWrappers);
    }

    public void handleDayLevelRevenueOverrides(BudgetDataEntryUIWrapper budgetDataEntryUIWrapper, BigDecimal overRiddenValue, List<BudgetDataEntryUIWrapper> budgetDataEntryUIWrappers) {
        boolean isOverriddenValueSet = Boolean.FALSE;
        BigDecimal actualOverRiddenValue = overRiddenValue.subtract(budgetDataEntryUIWrapper.getBudgetedRevenue());

        List<BudgetDataEntryUIWrapper> dataEntryUIWrappers = new ArrayList<>(budgetDataEntryUIWrappers);
        boolean isTransient = isaType(getText(TRANSIENT), budgetDataEntryUIWrapper.getBusinessType());

        BudgetDataEntryUIWrapper entryUIWrapper = dataEntryUIWrappers.get(0);
        entryUIWrapper.setBudgetedRevenue(entryUIWrapper.getBudgetedRevenue().add(actualOverRiddenValue));
        entryUIWrapper.isUpdated(Boolean.TRUE);

        if (isTransient) {
            BudgetDataEntryUIWrapper uiWrapper = dataEntryUIWrappers.get(1);
            uiWrapper.setBudgetedRevenue(uiWrapper.getBudgetedRevenue().add(actualOverRiddenValue));
        } else {
            BudgetDataEntryUIWrapper uiWrapper = dataEntryUIWrappers.get(2);
            uiWrapper.setBudgetedRevenue(uiWrapper.getBudgetedRevenue().add(actualOverRiddenValue));
        }

        for (int i = 3; i < dataEntryUIWrappers.size(); i++) {
            BudgetDataEntryUIWrapper dataEntryUIWrapper = dataEntryUIWrappers.get(i);
            if (isaType(budgetDataEntryUIWrapper.getParent().getWeek(), dataEntryUIWrapper.getWeek())) {
                dataEntryUIWrapper.setBudgetedRevenue(dataEntryUIWrapper.getBudgetedRevenue().add(actualOverRiddenValue));
                BudgetDataEntryUIWrapper entryUIWrapperDistributeLevel = isTransient ? dataEntryUIWrappers.get(i + 1) : dataEntryUIWrappers.get(i + 2);
                entryUIWrapperDistributeLevel.setBudgetedRevenue(entryUIWrapperDistributeLevel.getBudgetedRevenue().add(actualOverRiddenValue));
            }

            if ((budgetDataEntryUIWrapper.getWeekDays() == dataEntryUIWrapper.getWeekDays()) && isaType(budgetDataEntryUIWrapper.getBusinessType(), dataEntryUIWrapper.getBusinessType())
                    && !isOverriddenValueSet && !dataEntryUIWrapper.getAction()) {
                isOverriddenValueSet = Boolean.TRUE;
                dataEntryUIWrapper.setBudgetedRevenue(overRiddenValue);
                if (isaType(getText(TRANSIENT), dataEntryUIWrapper.getBusinessType())) {
                    BudgetDataEntryUIWrapper uiWrapper = dataEntryUIWrappers.get(i - 1);
                    uiWrapper.setBudgetedRevenue(uiWrapper.getBudgetedRevenue().add(actualOverRiddenValue));
                } else {
                    BudgetDataEntryUIWrapper uiWrapper = dataEntryUIWrappers.get(i - 2);
                    uiWrapper.setBudgetedRevenue(uiWrapper.getBudgetedRevenue().add(actualOverRiddenValue));
                }
            }
        }
        view.updateContainer(dataEntryUIWrappers);
    }

    protected abstract Integer getMonthlySold(JavaLocalDateInterval localDateInterval);


    protected abstract BigDecimal getMonthlyRevenue(JavaLocalDateInterval localDateInterval);

    public String getText(String key) {
        if (isNotFound(key)) {
            return key;
        }
        return super.getText(key);
    }

    private boolean isNotFound(String key) {
        if (key.isEmpty() || ("!!" + key).equals(super.getText(key))) {
            return true;
        }
        return false;
    }

    public String[] getText(String[] stringArray) {
        String[] localizedStringArray = new String[stringArray.length];
        for (int i = 0; i < stringArray.length; i++) {
            localizedStringArray[i] = getText(stringArray[i]);
        }
        return localizedStringArray;
    }

    public DowPattern getDowPattern(String businessType, JavaLocalDateInterval selectedLocalDateInterval, boolean isRoomsSolds) {
        int businessTypeId = businessType.equalsIgnoreCase(getText(TRANSIENT)) ? 2 : 1;
        PercentagePattern percentagePatternForRoomsSolds = null;
        if (isRoomsSolds) {
            percentagePatternForRoomsSolds = budgetService.getPercentagePatternForRoomsSolds(businessTypeId, selectedLocalDateInterval, BudgetType.BUSINESS_TYPE);
        } else {
            percentagePatternForRoomsSolds = budgetService.getPercentagePatternForRevenue(businessTypeId, selectedLocalDateInterval, BudgetType.BUSINESS_TYPE);
        }
        DowPattern pattern = convertIntoUiWrapperPattern(percentagePatternForRoomsSolds);
        return pattern;
    }

    private DowPattern convertIntoUiWrapperPattern(PercentagePattern percentagePatternForRoomsSolds) {
        DowPattern pattern = new DowPattern();
        pattern.setMonday(percentagePatternForRoomsSolds.getMonday());
        pattern.setTuesday(percentagePatternForRoomsSolds.getTuesday());
        pattern.setWednesday(percentagePatternForRoomsSolds.getWednesday());
        pattern.setThursday(percentagePatternForRoomsSolds.getThursday());
        pattern.setFriday(percentagePatternForRoomsSolds.getFriday());
        pattern.setSaturday(percentagePatternForRoomsSolds.getSaturday());
        pattern.setSunday(percentagePatternForRoomsSolds.getSunday());
        pattern.setTotal(percentagePatternForRoomsSolds.total());
        return pattern;
    }

    public DowPattern getDowDefaultValues() {
        DowPattern pattern = new DowPattern();
        pattern.setSunday(0);
        pattern.setMonday(0);
        pattern.setTuesday(0);
        pattern.setWednesday(0);
        pattern.setThursday(0);
        pattern.setFriday(0);
        pattern.setSaturday(0);
        return pattern;
    }

    public String getPropertyDisplayName() {
        return propertyService.getDisplayNameForProperty().replaceAll("\\s+-\\s+|\\s+", "_");
    }

    public Map<LocalDate, List<BudgetDataEntryUIWrapper>> getDailyBudgetDataEntryUIWrapper(List<BudgetDataEntryUIWrapper> budgetDataEntryUIWrapper) {
        LocalDate currentDate = null;
        Map<LocalDate, List<BudgetDataEntryUIWrapper>> dailyBudgetEntriesMap = new HashMap<>();
        for (BudgetDataEntryUIWrapper uiWrapper : budgetDataEntryUIWrapper) {
            if (isWeekStarted(uiWrapper)) {
                currentDate = null;
            }
            if (null != uiWrapper.getDowDate()) {
                currentDate = LocalDate.parse(uiWrapper.getDowDate().split(",")[1].trim(), DateTimeFormatter.ofPattern(getDateFormatString(), UiUtils.getLocale()));
            } else if (null != currentDate) {
                if (!dailyBudgetEntriesMap.containsKey(currentDate)) {
                    dailyBudgetEntriesMap.put(currentDate, new ArrayList<>());
                }
                dailyBudgetEntriesMap.get(currentDate).add(uiWrapper);
            }
        }
        return dailyBudgetEntriesMap;
    }

    protected String getDateFormatString() {
        return UiUtils.getDateFormatString();
    }

    private boolean isWeekStarted(BudgetDataEntryUIWrapper uiWrapper) {
        return null != uiWrapper.getWeek() && !uiWrapper.getWeek().equalsIgnoreCase(UiUtils.getText("month"));
    }

    public Map<String, Integer> getDailyRemainingHotelCapacity(Map<LocalDate, List<BudgetDataEntryUIWrapper>> dailyBudgetDataEntryUIWrapper, String businessType) {
        Map<String, Integer> dailyRemainingCapacity = new HashMap<>();
        Map<String, Integer> maxCapacity = new HashMap<>();
        int totalHotelCapacity = getTotalHotelCapacity();
        for (DayOfWeek day : DayOfWeek.values()) {
            maxCapacity.put(day.getDay(), 0);
        }

        populateMaxCapacityMap(dailyBudgetDataEntryUIWrapper, maxCapacity, businessType);
        for (DayOfWeek day : DayOfWeek.values()) {
            dailyRemainingCapacity.put(day.getDay(), totalHotelCapacity - maxCapacity.get(day.getDay()));
        }
        return dailyRemainingCapacity;
    }

    private void populateMaxCapacityMap(Map<LocalDate, List<BudgetDataEntryUIWrapper>> dailyBudgetDataEntryUIWrapper, Map<String, Integer> maxCapacity, String businessType) {
        for (Map.Entry<LocalDate, List<BudgetDataEntryUIWrapper>> entry : dailyBudgetDataEntryUIWrapper.entrySet()) {
            String day = entry.getKey().getDayOfWeek().getDisplayName(TextStyle.SHORT, Locale.getDefault()).toLowerCase();
            int roomsSold = entry.getValue().stream().filter(uiWrapper -> !businessType.equalsIgnoreCase(uiWrapper.getBusinessType()))
                    .findFirst().get().getBudgetedRooms().intValue();
            if (maxCapacity.get(day).compareTo(roomsSold) < 0) {
                maxCapacity.put(day, roomsSold);
            }
        }
    }

}
