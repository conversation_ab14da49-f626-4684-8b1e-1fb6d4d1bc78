package com.ideas.tetris.ui.modules.marketsegment.common;

import com.ideas.tetris.pacman.services.marketsegment.service.AnalyticalMarketSegmentSummary;
import com.ideas.tetris.ui.common.data.hierarchical.HierarchicalWithParent;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class AMSRebuildNeededUIWrapper implements HierarchicalWithParent<AMSRebuildNeededUIWrapper> {

    private String marketSegment;
    private String rateCode;
    private String rateCodeType;
    private AMSRebuildNeededUIWrapper parent;
    private List<AMSRebuildNeededUIWrapper> children = new ArrayList<AMSRebuildNeededUIWrapper>();

    /**
     * Should be called from a root node
     **/
    private AMSRebuildNeededUIWrapper(AMSSummaryUIWrapper amsSummaryUIWrapper) {
        marketSegment = amsSummaryUIWrapper.getMarketSegment();
        rateCode = amsSummaryUIWrapper.getRateCode();
        parent = null;
        for (AMSSummaryUIWrapper child : amsSummaryUIWrapper.getChildren()) {
            children.add(new AMSRebuildNeededUIWrapper(child, this));
        }
    }

    private AMSRebuildNeededUIWrapper(AMSSummaryUIWrapper amsSummaryUIWrapper, AMSRebuildNeededUIWrapper parent) {
        marketSegment = amsSummaryUIWrapper.getMarketSegment();
        rateCode = amsSummaryUIWrapper.getRateCode();
        this.parent = parent;
        for (AMSSummaryUIWrapper child : amsSummaryUIWrapper.getChildren()) {
            children.add(new AMSRebuildNeededUIWrapper(child, this));
        }
    }

    /**
     * Should be called from a root node
     **/
    private AMSRebuildNeededUIWrapper(AMSAssignedSummaryUIWrapper amsAssignedSummaryUIWrapper) {
        marketSegment = amsAssignedSummaryUIWrapper.getMarketSegment();
        rateCode = amsAssignedSummaryUIWrapper.getRateCode();
        parent = null;
        for (AMSAssignedSummaryUIWrapper child : amsAssignedSummaryUIWrapper.getChildren()) {
            children.add(new AMSRebuildNeededUIWrapper(child, this));
        }
    }

    private AMSRebuildNeededUIWrapper(AMSAssignedSummaryUIWrapper amsAssignedSummaryUIWrapper, List<AMSAssignedSummaryUIWrapper> childrenToAdd) {
        marketSegment = amsAssignedSummaryUIWrapper.getMarketSegment();
        rateCode = amsAssignedSummaryUIWrapper.getRateCode();
        parent = null;
        for (AMSAssignedSummaryUIWrapper child : amsAssignedSummaryUIWrapper.getChildren()) {
            if (childrenToAdd.contains(child)) {
                children.add(new AMSRebuildNeededUIWrapper(child, this));
            }
        }
    }

    private AMSRebuildNeededUIWrapper(AMSAssignedSummaryUIWrapper amsAssignedSummaryUIWrapper, AMSRebuildNeededUIWrapper parent) {
        marketSegment = amsAssignedSummaryUIWrapper.getMarketSegment();
        rateCode = amsAssignedSummaryUIWrapper.getRateCode();
        this.parent = parent;
        for (AMSAssignedSummaryUIWrapper child : amsAssignedSummaryUIWrapper.getChildren()) {
            children.add(new AMSRebuildNeededUIWrapper(child, this));
        }
    }

    private AMSRebuildNeededUIWrapper(AnalyticalMarketSegmentSummary analyticalMarketSegmentSummary) {
        marketSegment = analyticalMarketSegmentSummary.getMarketCode();
        rateCode = analyticalMarketSegmentSummary.getRateCode();
        rateCodeType = analyticalMarketSegmentSummary.getRateCodeType();
    }

    public String getMarketSegment() {
        return marketSegment;
    }

    public void setMarketSegment(String marketSegment) {
        this.marketSegment = marketSegment;
    }

    public String getRateCode() {
        return rateCode;
    }

    public void setRateCode(String rateCode) {
        this.rateCode = rateCode;
    }

    public String getRateCodeType() {
        return rateCodeType;
    }

    public void setRateCodeType(String rateCodeType) {
        this.rateCodeType = rateCodeType;
    }

    @Override
    public AMSRebuildNeededUIWrapper getParent() {
        return parent;
    }

    @Override
    public List<AMSRebuildNeededUIWrapper> getChildren() {
        return children;
    }

    @Override
    public boolean hasChildren() {
        return parent == null || children != null && !children.isEmpty();
    }

    public List<AMSRebuildNeededUIWrapper> flattenChildren(AMSRebuildNeededUIWrapper rebuildNeededUIWrapper) {
        List<AMSRebuildNeededUIWrapper> wrappers = new ArrayList<AMSRebuildNeededUIWrapper>();
        for (AMSRebuildNeededUIWrapper wrapper : rebuildNeededUIWrapper.getChildren()) {
            wrappers.add(wrapper);
            if (wrapper.getChildren().size() > 0) {
                wrappers.addAll(flattenChildren(wrapper));
            }
        }
        wrappers.add(rebuildNeededUIWrapper);
        return wrappers;
    }

    public static List<AMSRebuildNeededUIWrapper> buildRebuildUIWrappersFromSummaryUI(List<AMSSummaryUIWrapper> summaryUIWrappers) {
        List<AMSRebuildNeededUIWrapper> rebuildNeededUIWrappers = new ArrayList<AMSRebuildNeededUIWrapper>(summaryUIWrappers.size());
        for (AMSSummaryUIWrapper wrapper : summaryUIWrappers) {
            if (wrapper.getParent() == null) {
                AMSRebuildNeededUIWrapper rebuildNeededUIWrapper = new AMSRebuildNeededUIWrapper(wrapper);
                rebuildNeededUIWrappers.addAll(rebuildNeededUIWrapper.flattenChildren(rebuildNeededUIWrapper));
            }
        }
        return rebuildNeededUIWrappers;
    }

    public static List<AMSRebuildNeededUIWrapper> buildRebuildUIWrappersFromAssignedSummaryUI(List<AMSAssignedSummaryUIWrapper> summaryUIWrappers) {
        List<AMSRebuildNeededUIWrapper> rebuildNeededUIWrappers = new ArrayList<AMSRebuildNeededUIWrapper>(summaryUIWrappers.size());
        for (AMSAssignedSummaryUIWrapper wrapper : summaryUIWrappers) {
            if (wrapper.getParent() == null) {
                AMSRebuildNeededUIWrapper rebuildNeededUIWrapper = new AMSRebuildNeededUIWrapper(wrapper);
                rebuildNeededUIWrappers.addAll(rebuildNeededUIWrapper.flattenChildren(rebuildNeededUIWrapper));
            }
        }
        return rebuildNeededUIWrappers;
    }

    public static List<AMSRebuildNeededUIWrapper> buildRebuildUIWrappersFromAssignedSummaryUIFindParents(List<AMSAssignedSummaryUIWrapper> summaryUIWrappers) {
        List<AMSRebuildNeededUIWrapper> rebuildNeededUIWrappers = new ArrayList<AMSRebuildNeededUIWrapper>(summaryUIWrappers.size());
        Map<AMSAssignedSummaryUIWrapper, AMSRebuildNeededUIWrapper> rebuildParents = new HashMap<AMSAssignedSummaryUIWrapper, AMSRebuildNeededUIWrapper>();
        for (AMSAssignedSummaryUIWrapper wrapper : summaryUIWrappers) {
            //Walk to top of tree and then add the parent which will add all it's children in one structure.
            AMSAssignedSummaryUIWrapper parent = wrapper.getParent();
            while (parent.getParent() != null) {
                parent = parent.getParent();
            }

            //If the root parent already exists, skip it
            if (!rebuildParents.containsKey(parent)) {
                AMSRebuildNeededUIWrapper rebuildNeededUIWrapper = new AMSRebuildNeededUIWrapper(parent, summaryUIWrappers);
                rebuildNeededUIWrappers.addAll(rebuildNeededUIWrapper.flattenChildren(rebuildNeededUIWrapper));
                rebuildParents.put(parent, rebuildNeededUIWrapper);
            }
        }
        return rebuildNeededUIWrappers;
    }

    public static List<AMSRebuildNeededUIWrapper> buildRebuildUIWrappersFromSegmentSummary(AnalyticalMarketSegmentSummary analyticalMarketSegmentSummary) {
        List<AMSRebuildNeededUIWrapper> rebuildNeededUIWrappers = new ArrayList<AMSRebuildNeededUIWrapper>(1);
        rebuildNeededUIWrappers.add(new AMSRebuildNeededUIWrapper(analyticalMarketSegmentSummary));
        return rebuildNeededUIWrappers;
    }
}
