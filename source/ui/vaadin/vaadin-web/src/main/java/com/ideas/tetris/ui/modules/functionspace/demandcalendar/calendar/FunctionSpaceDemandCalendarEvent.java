package com.ideas.tetris.ui.modules.functionspace.demandcalendar.calendar;

import com.ideas.tetris.ui.common.component.calendar.TetrisCalendarEvent;

import java.util.Date;

public class FunctionSpaceDemandCalendarEvent extends TetrisCalendarEvent {

    private boolean isEvaluate = false;

    public FunctionSpaceDemandCalendarEvent(String caption, String description, Date date) {
        super(caption, description, date);
    }

    public boolean isEvaluate() {
        return isEvaluate;
    }

    public void setEvaluate(boolean isEvaluate) {
        this.isEvaluate = isEvaluate;
    }
}
