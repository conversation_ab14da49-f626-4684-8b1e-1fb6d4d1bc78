package com.ideas.tetris.ui.modules.functionspace.demandcalendar.calendar;

import com.ideas.tetris.pacman.services.dashboard.util.DateCalculator;
import com.ideas.tetris.pacman.services.specialevent.dto.SpecialEventSummaryDto;
import com.ideas.tetris.ui.common.component.TetrisBeanItemContainer;
import com.ideas.tetris.ui.common.component.TetrisLabel;
import com.ideas.tetris.ui.common.component.button.TetrisLinkButton;
import com.ideas.tetris.ui.common.component.panel.TetrisScrollablePanel;
import com.ideas.tetris.ui.common.component.table.TetrisTable;
import com.ideas.tetris.ui.common.component.window.TetrisWindow;
import com.ideas.tetris.ui.common.util.DateFormatUtil;
import com.ideas.tetris.ui.common.util.UiUtils;
import com.ideas.tetris.ui.modules.functionspace.demandcalendar.FunctionSpaceDemandCalendarPresenter;
import com.ideas.tetris.ui.modules.functionspace.demandcalendar.bookingsummary.RowData;
import com.vaadin.server.Sizeable;
import com.vaadin.ui.Button;
import com.vaadin.ui.JavaScript;
import com.vaadin.ui.JavaScriptFunction;
import com.vaadin.ui.UI;
import com.vaadin.ui.Window;
import com.vaadin.v7.ui.HorizontalLayout;
import com.vaadin.v7.ui.Label;
import com.vaadin.v7.ui.Table;
import com.vaadin.v7.ui.VerticalLayout;
import elemental.json.JsonArray;
import elemental.json.JsonObject;
import org.joda.time.Days;
import org.joda.time.LocalDate;

import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

public class FunctionSpaceCalendarSummaryComponent {
    public static void show(Date date, FunctionSpaceDemandCalendarPresenter presenter, DemandCalendarUIWrapper demandCalendarUIWrapper) {

        if (!UI.getCurrent().getUI().getWindows().isEmpty()) {
            ArrayList<Window> windows = new ArrayList<Window>(UI.getCurrent().getWindows());
            for (Window window : windows) {
                UI.getCurrent().removeWindow(window);
            }
        }
        presenter.getSystemDateAsLocalDate();
        String title = DateFormatUtil.formatStandard(date);
        if (presenter.getSystemDateAsLocalDate().compareTo(new LocalDate(date)) <= 0) {
            title += " ({0} {1} " + UiUtils.getText("common.toArrival") + ")";
            Days days = Days.daysBetween(presenter.getSystemDateAsLocalDate(), new LocalDate(date));
            title = MessageFormat.format(title, days.getDays(), (days.getDays() == 1 ? UiUtils.getText("common.day") : UiUtils.getText("days")));
        }

        TetrisWindow previewPopup = new TetrisWindow(title, null, false);
        previewPopup.center();
        previewPopup.setId("detailsPopup");

        SummaryPopup previewComponent = new SummaryPopup(date, presenter, previewPopup, demandCalendarUIWrapper);
        previewPopup.setContent(previewComponent);
        previewPopup.setWidth(350, Sizeable.Unit.PIXELS);
        previewPopup.show();
    }

    private static class SummaryPopup extends HorizontalLayout {
        private final TetrisWindow popupWindow;
        private final DemandCalendarUIWrapper wrapper;
        private Date date;
        private FunctionSpaceDemandCalendarPresenter presenter;
        private TetrisWindow leftPopupWindow;
        private String query;

        public SummaryPopup(Date date, FunctionSpaceDemandCalendarPresenter presenter, TetrisWindow previewPopup, DemandCalendarUIWrapper demandCalendarUIWrapper) {
            this.addStyleName("functionspace-details");
            this.presenter = presenter;
            this.date = date;
            this.popupWindow = previewPopup;
            this.wrapper = demandCalendarUIWrapper;
            query = "jQuery";

            setSpacing(true);
            setWidth(100, Unit.PERCENTAGE);
            addContent();
        }

        private void addContent() {
            final FunctionSpaceDetailsUiWrapper detailsFor = presenter.getDetails(new LocalDate(date), wrapper);
            VerticalLayout center = new VerticalLayout();
            center.setWidth(100, Unit.PERCENTAGE);
            center.setSpacing(true);
            center.addStyleName("tetris-padding-15-20");
            addFunctionSpaceSummaryHeaderLabel(center);

            addIndivisibleTable(center, detailsFor);

            addUtilizationForecastTable(center, detailsFor, isAfterSystemDate(new LocalDate(date)) ? UiUtils.getText("groupEvaluation.utilization.forecast") +
                    " %" : UiUtils.getText("groupPricing.demandCalendar.utilization%Actual"), false, isAfterSystemDate(new LocalDate(date)), date);

            addRoomClassSummaryHeaderLabel(center);

            addActualOccupanyTable(center, detailsFor, UiUtils.getText("groupPricing.demandCalendar.actualOccupancy"));
            if (isAfterSystemDate(new LocalDate(date))) {
                addForecastTable(center, detailsFor, UiUtils.getText("forecast"));
            }
            if (!detailsFor.specialEvents.isEmpty()) {
                addSpecialEvents(center, wrapper.getSpecialEvents());
            }

            TetrisLinkButton lastYearLink = new TetrisLinkButton(UiUtils.getText("lastyear"), new Button.ClickListener() {
                @Override
                public void buttonClick(Button.ClickEvent event) {
                    if (leftPopupWindow == null) {
                        openLastYearDetailPopupWindow(detailsFor);
                    } else {
                        leftPopupWindow.setVisible(!leftPopupWindow.isVisible());
                    }
                }
            });

            center.addComponent(lastYearLink);
            addComponent(center);
        }

        private boolean isAfterSystemDate(LocalDate date) {
            return presenter.getSystemDateAsLocalDate().compareTo(new LocalDate(date)) <= 0;
        }

        private void openLastYearDetailPopupWindow(FunctionSpaceDetailsUiWrapper detailsFor) {
            Date lastYearDate = DateCalculator.calculateDateForLastYear(date, true);
            String title = DateFormatUtil.formatStandard(lastYearDate) + " (" + UiUtils.getText("lastyear") + ")";
            leftPopupWindow = new TetrisWindow(title, null, false) {
                @Override
                public void close() {
                    setVisible(false);
                }

                @Override
                public void setVisible(boolean visible) {
                    super.setVisible(visible);
                    if (visible) {
                        positionLeftPopup();
                    }
                }

                @Override
                public void close(boolean changeOccurred) {
                    super.close();
                }
            };
            leftPopupWindow.setId("leftDetailsPopup");
            leftPopupWindow.setWidth(350, Sizeable.Unit.PIXELS);

            JavaScript.getCurrent().addFunction("positionLeftPopup", new JavaScriptFunction() {
                @Override
                public void call(JsonArray arguments) {
                    JsonObject positionObject = arguments.getObject(0);
                    int left = Integer.valueOf((int) positionObject.getNumber("left"));
                    int top = Integer.valueOf((int) positionObject.getNumber("top"));

                    int width = Integer.valueOf((int) arguments.getNumber(1));
                    JavaScript.getCurrent().execute(query + "('#leftDetailsPopup').offset({top:" + top + ", left:" + left + "});");

                    int posX = left - width;
                    JavaScript.getCurrent().execute(query + "('#leftDetailsPopup').animate({left:" + posX + ", top: " + top + ", opacity:1});");
                }
            });

            leftPopupWindow.center();

            LeftPopup previewComponent = new LeftPopup(date, lastYearDate, presenter, detailsFor);
            leftPopupWindow.setContent(previewComponent);
            UI.getCurrent().getUI().addWindow(leftPopupWindow);

            popupWindow.addCloseListener(new Window.CloseListener() {
                @Override
                public void windowClose(Window.CloseEvent e) {
                    leftPopupWindow.close(true);
                }
            });
            positionLeftPopup();
        }

        private void positionLeftPopup() {
            //get Bound did not work for IE  in Vaadin
            JavaScript.getCurrent().execute("positionLeftPopup(" + query + "('#" + popupWindow.getId() + "').position(), " + query + "('#" + popupWindow.getId() + "').width())");
        }
    }

    private static void addRoomClassSummaryHeaderLabel(VerticalLayout center) {
        TetrisLabel guestRoomSummary = new TetrisLabel(UiUtils.getText("groupPricing.demandCalendar.guestRoomSummary"));
        guestRoomSummary.addStyleName("tetris-h1");
        center.addComponent(guestRoomSummary);
    }

    private static void addFunctionSpaceSummaryHeaderLabel(VerticalLayout layout) {
        TetrisLabel functionSpaceSummary = new TetrisLabel(UiUtils.getText("groupPricing.demandCalendar.functionSpaceSummary"));
        functionSpaceSummary.addStyleName("tetris-h1");
        layout.addComponent(functionSpaceSummary);
    }

    private static void addForecastTable(VerticalLayout center, FunctionSpaceDetailsUiWrapper detailsFor, String title) {
        TetrisBeanItemContainer<RowData> container = new TetrisBeanItemContainer<RowData>(RowData.class);
        container.addAll(detailsFor.getForecast());
        TetrisTable table = new TetrisTable(title, container);
        table.addStyleName("blockColorRowCaption");
        applyTableFormatting(table);
        center.addComponent(table);

        String valueCol = "value";
        table.setColumnAlignment(valueCol, Table.Align.RIGHT);
    }

    private static void addActualOccupanyTable(VerticalLayout center, FunctionSpaceDetailsUiWrapper detailsFor, String title) {
        TetrisBeanItemContainer<RowData> container = new TetrisBeanItemContainer<RowData>(RowData.class);
        container.addAll(detailsFor.getActualOccupancy());
        TetrisTable table = new TetrisTable(title, container);
        table.addStyleName("blockColorRowCaption");
        applyTableFormatting(table);
        center.addComponent(table);

        String valueCol = "value";
        table.setColumnAlignment(valueCol, Table.Align.RIGHT);
    }

    private static void addUtilizationForecastTable(VerticalLayout center, FunctionSpaceDetailsUiWrapper detailsFor, String title, final boolean isLastYear, final boolean isAfterSystemDate, final Date date) {
        TetrisBeanItemContainer<RowData> container = new TetrisBeanItemContainer<RowData>(RowData.class);
        container.addAll(detailsFor.getUtilizationForecast());
        TetrisTable table = new TetrisTable(title, container);
        table.addStyleName("blockColorRowCaption");

        table.addGeneratedColumn("label", new Table.ColumnGenerator() {
            @Override
            public Object generateCell(Table source, Object itemId, Object columnId) {
                RowData row = (RowData) itemId;
                if (row.isHasOverride()) {
                    HorizontalLayout horizontalLayout = new HorizontalLayout();
                    TetrisLabel label = new TetrisLabel(row.getLabel());
                    horizontalLayout.addComponent(label);

                    Label icon = new Label();
                    icon.addStyleName("forecastOverride");
                    horizontalLayout.addComponent(icon);
                    return horizontalLayout;
                }
                return new Label(row.getLabel());
            }
        });

        table.addGeneratedColumn("status", new Table.ColumnGenerator() {
            @Override
            public Object generateCell(Table table, Object o, Object o1) {
                RowData status = (RowData) o;
                if (!isAfterSystemDate) {
                    return status.getStatus();
                } else {
                    if (isLastYear) {
                        return new TetrisLabel(status.getStatus());
                    } else {
                        return new TetrisLinkButton(status.getStatus(), new Button.ClickListener() {
                            @Override
                            public void buttonClick(Button.ClickEvent clickEvent) {
                                FunctionSpaceEvaluateLinkUtil.goToEvaluation(date);
                            }
                        });
                    }
                }
            }
        });

        applyTableFormatting(table);
        table.setPageLength(5);
        ArrayList<String> visibleColumns = new ArrayList<String>(Arrays.asList("label",
                "value",
                "status"));

        table.setColumnAlignment("value", Table.Align.RIGHT);
        table.setVisibleColumns(visibleColumns.toArray(new String[visibleColumns.size()]));
        center.addComponent(table);
    }

    private static void addSpecialEvents(VerticalLayout center, List<SpecialEventSummaryDto> specialEvents) {
        TetrisLabel specialEventTitle = new TetrisLabel(UiUtils.getText("SPECIAL_EVENTS"));
        specialEventTitle.addStyleName("tetris-h1");
        center.addComponent(specialEventTitle);

        TetrisScrollablePanel panel = new TetrisScrollablePanel();
        panel.setWidth(100, Sizeable.Unit.PERCENTAGE);
        panel.setHeight(75, Sizeable.Unit.PIXELS);
        center.addComponent(panel);
        VerticalLayout panelContent = new VerticalLayout();

        for (SpecialEventSummaryDto specialEvent : specialEvents) {
            SpecialEventLayout specialEventLayout = new SpecialEventLayout(specialEvent);
            panelContent.addComponent(specialEventLayout);
        }
        panel.setContent(panelContent);
    }

    private static void applyTableFormatting(TetrisTable table) {
        table.setPageLength(3);
        table.setAutoRowHeightEnabled(true);
        table.setWidth(100, Sizeable.Unit.PERCENTAGE);
        table.setColumnHeaderMode(Table.ColumnHeaderMode.HIDDEN);
        String value = "value";
        String label = "label";
        table.setVisibleColumns(new String[]{label, value});
    }

    private static void addIndivisibleTable(VerticalLayout layout, final FunctionSpaceDetailsUiWrapper detailsFor) {
        String indivisibleRoomsBooked = UiUtils.getText("groupPricing.demandCalendar.indivisibleRoomsBooked");
        if (detailsFor.getIndivisibleRoomsCount() == 0) {
            layout.addComponent(new TetrisLabel(indivisibleRoomsBooked + ": 0"));
        } else {
            TetrisBeanItemContainer<RowData> container = new TetrisBeanItemContainer<RowData>(RowData.class);
            container.addAll(detailsFor.getIndivisibleRooms());
            final TetrisTable indivisibleRoomsTable = new TetrisTable();
            indivisibleRoomsTable.setVisible(false);
            indivisibleRoomsTable.setContainerDataSource(container);
            applyTableFormatting(indivisibleRoomsTable);
            TetrisLinkButton indivisibleLink = new TetrisLinkButton(indivisibleRoomsBooked + ": " + detailsFor.getIndivisibleRoomsCount(), new Button.ClickListener() {
                @Override
                public void buttonClick(Button.ClickEvent clickEvent) {
                    indivisibleRoomsTable.setVisible(!detailsFor.getIndivisibleRooms().isEmpty() && !indivisibleRoomsTable.isVisible());
                }
            });
            layout.addComponent(indivisibleLink);
            layout.addComponent(indivisibleRoomsTable);
        }
    }

    private static class LeftPopup extends VerticalLayout {
        private final Date currentDate;
        private final FunctionSpaceDetailsUiWrapper details;
        private FunctionSpaceDemandCalendarPresenter presenter;
        private Date lastYearDate;
        private VerticalLayout leftPanel;

        public LeftPopup(Date date, Date lastYearDate, FunctionSpaceDemandCalendarPresenter presenter, FunctionSpaceDetailsUiWrapper detailsFor) {
            this.addStyleName("functionspace-details");
            this.lastYearDate = lastYearDate;
            this.currentDate = date;
            this.presenter = presenter;
            this.details = detailsFor;
            addLeftPanel();
        }

        private void addLeftPanel() {
            FunctionSpaceDetailsUiWrapper lastYearData = presenter.getLastYearDetails(new LocalDate(lastYearDate), details);
            leftPanel = new VerticalLayout();
            leftPanel.setSpacing(true);
            leftPanel.addStyleName("tetris-padding-15-20");

            addFunctionSpaceSummaryHeaderLabel(leftPanel);

            addIndivisibleTable(leftPanel, lastYearData);
            boolean isAfterSystemDate = presenter.getSystemDateAsLocalDate().compareTo(new LocalDate(currentDate)) <= 0;

            String actualUtilization = UiUtils.getText("groupPricing.demandCalendar.utilization%Actual");
            addUtilizationForecastTable(leftPanel, lastYearData, actualUtilization, true, isAfterSystemDate, lastYearDate);
            addRoomClassSummaryHeaderLabel(leftPanel);

            if (isAfterSystemDate) {
                addForecastTable(leftPanel, lastYearData, UiUtils.getText("groupPricing.demandCalendar.actualOccupancySameLastYear"));
            }

            addActualOccupanyTable(leftPanel, lastYearData, UiUtils.getText("groupPricing.demandCalendar.actualOccupancy"));

            if (!lastYearData.specialEvents.isEmpty()) {
                List<SpecialEventSummaryDto> specialEvents = presenter.getSpecialEvents(new LocalDate(lastYearDate));
                addSpecialEvents(leftPanel, specialEvents);
            }
            addComponent(leftPanel);
        }
    }
}
