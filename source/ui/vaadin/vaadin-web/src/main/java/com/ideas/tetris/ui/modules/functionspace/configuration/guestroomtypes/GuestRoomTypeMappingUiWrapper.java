package com.ideas.tetris.ui.modules.functionspace.configuration.guestroomtypes;

import com.ideas.tetris.pacman.services.accommodation.entity.AccomType;
import com.ideas.tetris.pacman.services.functionspace.configuration.entity.FunctionSpaceGuestRoomCategory;
import com.ideas.tetris.ui.common.component.table.TetrisChangeAwareColumnComponentProvider;

public class GuestRoomTypeMappingUiWrapper extends TetrisChangeAwareColumnComponentProvider {
    private FunctionSpaceGuestRoomCategory guestRoomCategory;
    public AccomType selectedAccomType;

    public GuestRoomTypeMappingUiWrapper() {
    }

    public GuestRoomTypeMappingUiWrapper(FunctionSpaceGuestRoomCategory guestRoomCategory) {
        this.guestRoomCategory = guestRoomCategory;
    }

    public FunctionSpaceGuestRoomCategory getGuestRoomCategory() {
        return guestRoomCategory;
    }

    public void setGuestRoomCategory(FunctionSpaceGuestRoomCategory guestRoomCategory) {
        this.guestRoomCategory = guestRoomCategory;
    }

    public AccomType getSelectedAccomType() {
        return selectedAccomType;
    }

    public void setSelectedAccomType(AccomType selectedAccomType) {
        this.selectedAccomType = selectedAccomType;
    }
}
