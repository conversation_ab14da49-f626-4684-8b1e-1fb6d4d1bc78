package com.ideas.tetris.ui.modules.marketsegment.common;

import com.ideas.tetris.ui.common.component.table.TetrisTreeTable;

public class AttributeAssignmentTreeTable extends TetrisTreeTable {

    //override the addNoDataFoundRowIfNeeded method so the no data found row doesn't show up
    //the no data row was causing client side javascript errors for some reason in TreeTable
    @Override
    public void addNoDataFoundRowIfNeeded() {
        //super.addNoDataFoundRowIfNeeded();
    }
}
