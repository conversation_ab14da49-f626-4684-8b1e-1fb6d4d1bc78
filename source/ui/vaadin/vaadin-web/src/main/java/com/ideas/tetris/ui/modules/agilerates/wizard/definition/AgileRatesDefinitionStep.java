package com.ideas.tetris.ui.modules.agilerates.wizard.definition;

import com.ideas.tetris.pacman.services.accommodation.entity.AccomType;
import com.ideas.tetris.pacman.services.agilerates.configuration.dto.AgileRatesProductConfigurationDTO;
import com.ideas.tetris.pacman.services.agilerates.configuration.dto.AgileRatesProductTypeEnum;
import com.ideas.tetris.pacman.services.agilerates.configuration.entity.AgileRatesDecisionsSentBy;
import com.ideas.tetris.pacman.services.agilerates.configuration.entity.AgileRatesOffsetMethod;
import com.ideas.tetris.pacman.services.agilerates.configuration.entity.AgileRatesPackage;
import com.ideas.tetris.pacman.services.agilerates.configuration.entity.ChildPricingType;
import com.ideas.tetris.pacman.services.product.FloorType;
import com.ideas.tetris.pacman.services.product.OverridableProductEnum;
import com.ideas.tetris.pacman.services.product.Product;
import com.ideas.tetris.pacman.services.product.ProductRateShoppingLOSEnum;
import com.ideas.tetris.pacman.services.product.RoundingRule;
import com.ideas.tetris.rdl.RDLConstants;
import com.ideas.tetris.ui.common.component.TetrisHRule;
import com.ideas.tetris.ui.common.component.TetrisNotification;
import com.ideas.tetris.ui.common.component.button.TetrisButton;
import com.ideas.tetris.ui.common.component.button.TetrisImageButton;
import com.ideas.tetris.ui.common.component.checkbox.TetrisCheckBoxV8;
import com.ideas.tetris.ui.common.component.checkbox.TetrisRadioButtonGroupV8;
import com.ideas.tetris.ui.common.component.filterabletwincolselect.TetrisFilterableTwinColSelectV8;
import com.ideas.tetris.ui.common.component.fontawesome.TetrisFontAwesome;
import com.ideas.tetris.ui.common.component.label.TetrisSpacer;
import com.ideas.tetris.ui.common.component.messagebox.TetrisMessageBox;
import com.ideas.tetris.ui.common.component.panel.TetrisScrollablePanel;
import com.ideas.tetris.ui.common.component.select.TetrisComboBoxV8;
import com.ideas.tetris.ui.common.component.select.TetrisEnumComboBoxV8;
import com.ideas.tetris.ui.common.component.textfield.TetrisTextAreaV8;
import com.ideas.tetris.ui.common.component.textfield.TetrisTextFieldV8;
import com.ideas.tetris.ui.common.component.window.TetrisConfirmationWindow;
import com.ideas.tetris.ui.common.component.window.TetrisWindow;
import com.ideas.tetris.ui.common.component.wizard.WizardStepDirection;
import com.ideas.tetris.ui.common.util.LocalizationDimensionSpecifier;
import com.ideas.tetris.ui.common.util.TetrisComponent;
import com.ideas.tetris.ui.common.util.TetrisValoTheme;
import com.ideas.tetris.ui.common.util.UiUtils;
import com.ideas.tetris.ui.modules.agilerates.packages.AgileRatesUiUtils;
import com.ideas.tetris.ui.modules.agilerates.wizard.AgileRatesDependentWizardStep;
import com.ideas.tetris.ui.modules.agilerates.wizard.AgileRatesWizardPresenter;
import com.vaadin.data.Binder;
import com.vaadin.data.HasValue;
import com.vaadin.data.ValueProvider;
import com.vaadin.data.converter.StringToBigDecimalConverter;
import com.vaadin.data.converter.StringToIntegerConverter;
import com.vaadin.data.validator.BigDecimalRangeValidator;
import com.vaadin.server.SerializablePredicate;
import com.vaadin.server.Setter;
import com.vaadin.shared.data.sort.SortDirection;
import com.vaadin.shared.ui.ContentMode;
import com.vaadin.shared.ui.MarginInfo;
import com.vaadin.ui.AbstractComponentContainer;
import com.vaadin.ui.Alignment;
import com.vaadin.ui.Component;
import com.vaadin.ui.CssLayout;
import com.vaadin.ui.FormLayout;
import com.vaadin.ui.HorizontalLayout;
import com.vaadin.ui.ItemCaptionGenerator;
import com.vaadin.ui.Label;
import com.vaadin.ui.VerticalLayout;
import org.apache.commons.collections.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.EnumSet;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.BooleanSupplier;
import java.util.stream.Collectors;

import static com.ideas.tetris.ui.common.util.UiUtils.getText;
import static com.ideas.tetris.ui.modules.agilerates.packages.AgileRatesUiUtils.createConfirmationDialogue;
import static com.ideas.tetris.ui.modules.agilerates.packages.AgileRatesUiUtils.getTextForFormLabel;
import static com.ideas.tetris.ui.modules.agilerates.packages.AgileRatesUiUtils.isUniqueProductName;
import static com.ideas.tetris.ui.modules.agilerates.packages.AgileRatesUiUtils.save;
import static com.ideas.tetris.ui.modules.agilerates.wizard.offsets.OffsetsUtil.floorPercentageOffsetFieldValidator;
import static com.ideas.tetris.ui.modules.agilerates.wizard.offsets.OffsetsUtil.valueMustBeGreaterThanZero;
import static com.ideas.tetris.ui.modules.agilerates.wizard.offsets.OffsetsUtil.valueMustNotBeNull;

public class AgileRatesDefinitionStep extends VerticalLayout implements AgileRatesDependentWizardStep {
    protected static final String FORM_LAYOUT_CAPTION_VERTICAL_ALIGN_TOP_KEY = "form-layout-caption-vertical-align-top";
    private static final String FIELD_REQUIRED_KEY = "common.fieldrequired";

    private static final String CHILD_PRICING_TYPE_SEND = "child.pricing.type.send";
    private static final String AGILE_RATES_FLOOR_FIELD_ID = "agile-rates-floor-field";
    private static final String AGILE_RATES_MINIMUM_PRICE_CHANGE_ID = "agile-rates-minimum-price-change-field";
    private static final String OPTIMIZED_CHECKBOX_ID = "optimized-checkbox";
    private static final String AGILE_RATES_PUBLIC_CHECKBOX_ID = "agile-rates-public-check-box";
    private static final String AGILE_RATES_FREE_NIGHT_CHECKBOX_ID = "agile-rates-free-night-check-box";
    private static final String AGILE_RATES_FREE_UPGRADE_CHECKBOX_ID = "agile-rates-free-upgrade-check-box";
    private static final String AGILE_RATES_ROUNDING_RULE_COMBOBOX_ID = "agile-rates-rounding-rule-combobox";
    private static final String AGILE_RATES_ADJUSTMENT_BY_TYPE_ID = "agileRatesAdjustmentByRadioButton";
    private static final String AGILE_RATES_ADJUSTMENT_BY_TYPE_STYLE = "agile-rates-adjustment-by-radio-button";
    private static final String AGILE_RATES_SEND_ADJUSTMENT_ID = "agileRatesSendAdjustmentRadioButton";
    private static final String AGILE_RATES_RATE_SHOPPING_LOS_STYLE = "agile-rates-rate-shopping-los-radio-button";

    private static final String CHILD_PRICING_TYPE_BUTTONS_ID = "childPricingTypeRadioButton";
    private static final String AGILE_RATES_SEND_ADJUSTMENT_STYLE = "agile-rates-send-adjustment-radio-button";
    private static final String SEASONAL_PRODUCT_ONLY_CHECK_BOX_ID = "seasonalProductOnlyCheckBox";
    private static final String IS_OVERRIDABLE_CHECKBOX_ID = "is-overridable-checkbox";
    private static final String OVERRIDES_RADIO_BUTTONS_ID = "overridesRadioButtons";
    private static final String FLOOR_TYPE_RADIO_BUTTONS_ID = "floorTypeRadioButtons";
    private static final String FLOOR_PERCENTAGE_OFF_FIELD = "floorPercentageOffField";
    private static final String RATE_TYPE_LOS_RADIO_BUTTONS_ID = "rateTypeLOSRadioButtons";

    private static final String PRODUCT_NAME_ELEMENT_ID = "productName";
    private static final String PRODUCT_DESCRIPTION_ELEMENT_ID = "productDescription";

    private static final String DEFAULT_FLOOR_PERCENTAGE_OFF_FREE_NIGHT_PRODUCT = "";

    private static final int FORM_TEXT_FIELD_WIDTH = 250;
    private static final int REDUCED_FORM_TEXT_FIELD_WIDTH = 70;

    private AgileRatesWizardPresenter presenter;
    protected Binder<AgileRatesProductConfigurationDTO> binder;
    private AbstractComponentContainer rateCodeAndRoomTypeContainer;
    private FencingFields fencingContainer;
    private AbstractComponentContainer packageElementsContainer;
    private TetrisComboBoxV8<AgileRatesProductTypeEnum> typeField;
    private final List<TetrisComponent<?>> hasChangesList = new ArrayList<>();
    private TetrisFilterableTwinColSelectV8<AccomType> roomTypeSelect;
    private TetrisFilterableTwinColSelectV8<AgileRatesPackage> packages;
    private TetrisCheckBoxV8 seasonalProductOnlyCheckBox;
    private TetrisConfirmationWindow confirmationIfOffsetsChangedWindow;
    private TetrisComboBoxV8<Product> baseField;
    private TetrisTextFieldV8 minimumPriceChangeField;
    private TetrisFilterableTwinColSelectV8<String> rateCodeSelect;
    private TetrisFilterableTwinColSelectV8<AccomType> roomTypeTwinColSelect;
    private TetrisFilterableTwinColSelectV8<AgileRatesPackage> packagesTwinColSelect;
    private TetrisCheckBoxV8 optimizedCheckBox;
    private TetrisTextFieldV8 nameField;
    private TetrisEnumComboBoxV8<RoundingRule> roundingRuleDropDown;
    private TetrisTextFieldV8 floorDecimalField;
    private HorizontalLayout checkBoxesLayout;
    private HorizontalLayout freeNightAndFreeUpgradeLayout;
    private TetrisCheckBoxV8 publicCheckBox;
    private TetrisCheckBoxV8 freeNightCheckBox;
    private TetrisCheckBoxV8 freeUpgradeCheckBox;
    private TetrisRadioButtonGroupV8<AgileRatesOffsetMethod> adjustmentByRadioButtons;
    private TetrisRadioButtonGroupV8<AgileRatesDecisionsSentBy> decisionsSentByRadioButtons;

    private TetrisRadioButtonGroupV8<ChildPricingType> childPricingTypeRadioButtons;
    private Label optimizedProductFixedBaseProduct;
    private TetrisRadioButtonGroupV8<OverridableProductEnum> overrideTypeRadioButtons;
    private FormLayout productDefinitionForm;
    private TetrisRadioButtonGroupV8<FloorType> floorTypeRadioButtons;
    private TetrisTextFieldV8 floorPercentageOffField;
    private HorizontalLayout floorTypeLayout;
    private TetrisComboBoxV8<String> rateTypeField;
    private TetrisRadioButtonGroupV8<ProductRateShoppingLOSEnum> rateTypeLOSRadioButtons;
    private TetrisTextFieldV8 rateTypeLOSMinField;
    private TetrisTextFieldV8 rateTypeLOSMaxField;
    private boolean ignoreLOSRangeValidation = false;

    public AgileRatesDefinitionStep(AgileRatesWizardPresenter presenter) {
        this.presenter = presenter;
        init();
    }

    private void init() {
        addStyleName("agile.rates");
        setSizeFull();
        setMargin(new MarginInfo(false, true));
        setCaption(getText("agile.rates.definition"));
        confirmationIfOffsetsChangedWindow =
                createConfirmationDialogue(getText("agile.rates.defaults.offsets.confirmation.title"),
                        getText("OK"),
                        getText("cancel"));
        initStep();
    }

    public boolean isReadOnly() {
        if (presenter.getProductConfiguration().getProduct().isCentrallyManaged()) {
            return !presenter.hasCentrallyManagedPermission();
        } else {
            return !presenter.hasDefinitionStepPermission();
        }
    }

    @Override
    public void initStep() {
        removeAllComponents();
        initBinders();
        hasChangesList.clear();

        seasonalProductOnlyCheckBox = createSeasonalProductOnlyCheckBox();
        roomTypeSelect = createRoomTypeField(getTextForFormLabel(FIELD_REQUIRED_KEY));
        packages = createPackages(getTextForFormLabel(FIELD_REQUIRED_KEY));
        addPanel(seasonalProductOnlyCheckBox, roomTypeSelect, packages);
        updateVisibility();

        binder.setReadOnly(isReadOnly());

        AgileRatesDecisionsSentBy originalSendAdjustment = presenter.getProductConfiguration().isSendAdjustment();

        binder.setBean(presenter.getProductConfiguration());

        if (presenter.isHiltonOptionToSendAdjustmentForAgileEnabled() && presenter.getProductConfiguration().isSendAdjustment() != null) {
            decisionsSentByRadioButtons.setSelectedItem(presenter.getProductConfiguration().isSendAdjustment());
        } else {
            decisionsSentByRadioButtons.setSelectedItem(AgileRatesDecisionsSentBy.PRICE);
            if (presenter.getProductConfiguration().isSendAdjustment() != null) {
                decisionsSentByRadioButtons.setOriginalValue(originalSendAdjustment);
            }
            presenter.getProductConfiguration().setSendAdjustment(AgileRatesDecisionsSentBy.PRICE);
        }

        if (presenter.isSavedBaseProductInvalidOnLoad(presenter.getProductConfiguration())) {
            baseField.clear();
            presenter.getProductConfiguration().setOptimizedChangedStatus(true);
        }
        setFieldsEnabledInSequence();
    }

    private void setFieldsEnabledInSequence() {
        enableFieldsByPreviousFieldsNotNull();
        nameField.addValueChangeListener(event -> enableFieldsByPreviousFieldsNotNull());
        baseField.addValueChangeListener(event -> enableFieldsByPreviousFieldsNotNull());
        typeField.addValueChangeListener(event -> enableFieldsByPreviousFieldsNotNull());
    }

    private void addPanel(
            TetrisCheckBoxV8 seasonalProductOnlyCheckBox,
            TetrisFilterableTwinColSelectV8<AccomType> roomTypeSelect,
            TetrisFilterableTwinColSelectV8<AgileRatesPackage> packagesComponent) {
        TetrisScrollablePanel panel = new TetrisScrollablePanel();
        panel.addStyleName("internal-panel");
        panel.setSizeFull();

        VerticalLayout panelContent = new VerticalLayout();
        panelContent.setMargin(false);
        panelContent.setSpacing(false);

        panelContent.addComponent(new TetrisHRule());
        addFormTo(panelContent, seasonalProductOnlyCheckBox);
        panelContent.addComponent(new TetrisHRule());
        addRateCodeAndRoomTypeTo(panelContent, roomTypeSelect);
        addFencingTo(panelContent);
        addPackagesTo(panelContent, packagesComponent);

        panel.setContent(panelContent);
        addComponentsAndExpand(panel);
    }

    private void enableFieldsByPreviousFieldsNotNull() {
        if (nameField.getValue() == null || "".equals(nameField.getValue()) || nameField.getComponentError() != null) {
            //Set all other fields to read only
            enableProductFields(false);
            baseField.setEnabled(false);
            typeField.setEnabled(false);
        } else if (presenter.isFreeNightProduct()) {
            enableFieldsByPreviousFieldsNotNullForFreeNightProduct();
        } else if (baseField.getValue() == null || typeField.getValue() == null) {
            //Set all other fields to read only excluding Base Product and Product Type
            enableProductFields(false);
            baseField.setEnabled(true);
            typeField.setEnabled(true);
        } else {
            //Set all other fields to enabled
            enableProductFields(true);
            baseField.setEnabled(true);
            typeField.setEnabled(true);
        }
    }

    private void enableProductFields(boolean enableFields) {
        minimumPriceChangeField.setEnabled(enableFields);
        publicCheckBox.setEnabled(enableFields);
        floorTypeLayout.setEnabled(enableFields);
        floorPercentageOffField.setEnabled(presenter.getProductConfiguration().getFloorType().equals(FloorType.RELATIVE_TO_PRIMARY_FLOOR) && enableFields);
        floorDecimalField.setEnabled(presenter.getProductConfiguration().getFloorType().equals(FloorType.FIXED_RATE) && enableFields);
        if (baseField.getValue() != null) {
            if (presenter.isBaseFieldOrParentsFixed(baseField.getValue()) || presenter.isFreeNightProduct()) {
                optimizedCheckBox.setEnabled(false);
                optimizedProductFixedBaseProduct.setEnabled(false);
            } else {
                optimizedCheckBox.setEnabled(enableFields);
                optimizedProductFixedBaseProduct.setEnabled(enableFields);
            }
        } else {
            optimizedCheckBox.setEnabled(enableFields);
            optimizedProductFixedBaseProduct.setEnabled(enableFields);
        }

        if (null != optimizedCheckBox && enableFields) {
            adjustmentByRadioButtons.setEnabled(!optimizedCheckBox.getValue());
        } else {
            adjustmentByRadioButtons.setEnabled(enableFields);
        }

        decisionsSentByRadioButtons.setEnabled(presenter.isHiltonOptionToSendAdjustmentForAgileEnabled() ? enableFields && !presenter.isPackagesSelected() : false);
        if (presenter.isPackagesSelected()) {
            decisionsSentByRadioButtons.setValue(AgileRatesDecisionsSentBy.PRICE);
        }

        childPricingTypeRadioButtons.setEnabled(!CollectionUtils.isEmpty(presenter.getOccupantBuckets()) && enableFields);

        if (presenter.isFreeNightProduct()) {
            //Set default values for these fields and disable them
            seasonalProductOnlyCheckBox.setEnabled(false);

            freeUpgradeCheckBox.setEnabled(enableFields);

            roundingRuleDropDown.setValue(RoundingRule.PRICE_ROUNDING);
            roundingRuleDropDown.setEnabled(false);

            overrideTypeRadioButtons.setValue(OverridableProductEnum.NO_OVERRIDES);
            overrideTypeRadioButtons.setEnabled(false);

            adjustmentByRadioButtons.setValue(AgileRatesOffsetMethod.PERCENTAGE);
            adjustmentByRadioButtons.setEnabled(false);

            if (presenter.isHiltonOptionToSendAdjustmentForAgileEnabled()) {
                decisionsSentByRadioButtons.setValue(AgileRatesDecisionsSentBy.PRICE);
                decisionsSentByRadioButtons.setEnabled(false);
            }

            if (!CollectionUtils.isEmpty(presenter.getOccupantBuckets())) {
                childPricingTypeRadioButtons.setValue(ChildPricingType.USE_SYSTEM_DEFAULT);
                childPricingTypeRadioButtons.setEnabled(false);
            }
        } else {
            seasonalProductOnlyCheckBox.setEnabled(enableFields);
            roundingRuleDropDown.setEnabled(enableFields);
            floorTypeRadioButtons.setEnabled(enableFields);
            floorPercentageOffField.setEnabled(presenter.getProductConfiguration().getFloorType().equals(FloorType.RELATIVE_TO_PRIMARY_FLOOR) && enableFields);
            overrideTypeRadioButtons.setEnabled(enableFields);
        }

        checkBoxesLayout.setEnabled(enableFields);
        rateCodeSelect.setEnabled(enableFields);
        roomTypeSelect.setEnabled(enableFields);
        fencingContainer.setEnabled(enableFields);
        packages.setEnabled(enableFields);

        if (presenter.isRDLEnabled()) {
            rateTypeField.setEnabled(enableFields);
            rateTypeLOSRadioButtons.setEnabled(enableFields);
        }
    }

    private void enableFieldsByPreviousFieldsNotNullForFreeNightProduct() {
        baseField.setValue(presenter.determineAllowedBaseProducts().stream()
                .filter(Product::isSystemDefault)
                .findFirst()
                .orElse(null));
        baseField.setEnabled(false);

        typeField.setValue(AgileRatesProductTypeEnum.FENCED_AND_NO_PACKAGE);
        typeField.setEnabled(false);

        enableProductFields(true);

        floorTypeRadioButtons.setValue(FloorType.FIXED_RATE);
        floorTypeLayout.setEnabled(false);
        floorDecimalField.setValue(String.valueOf(0));
        floorDecimalField.setEnabled(false);

        floorPercentageOffField.setValue(DEFAULT_FLOOR_PERCENTAGE_OFF_FREE_NIGHT_PRODUCT);
        floorPercentageOffField.setEnabled(false);
        roomTypeSelect.setItems(presenter.getBaseProductRoomTypes());

        roomTypeTwinColSelect.getAddAllButton().click();
        roomTypeTwinColSelect.setEnabled(false);
    }

    private void addFormTo(VerticalLayout layout, TetrisCheckBoxV8 seasonalProductOnlyCheckBox) {
        productDefinitionForm = new FormLayout();
        String fieldRequiredText = getText(FIELD_REQUIRED_KEY);

        // Product Name
        nameField = new TetrisTextFieldV8();
        nameField.setId(PRODUCT_NAME_ELEMENT_ID);
        nameField.setCaption(getTextForFormLabel("agile.rates.product.name"));
        nameField.setMaxLength(100);
        nameField.setWidth(FORM_TEXT_FIELD_WIDTH, Unit.PIXELS);
        productDefinitionForm.addComponent(nameField);
        binder.forField(nameField)
                .asRequired(fieldRequiredText)
                .withConverter(name -> name == null ? null : AgileRatesUiUtils.removeInvisibleCharactersFromProductName(name), name -> name == null ? "" : name)
                .withValidator(name -> isUniqueProductName(name, binder.getBean().getProduct(), presenter.getAllProducts()), getText("agile.rates.product.name.warning"))
                .bind(AgileRatesProductConfigurationDTO::getProductName, AgileRatesProductConfigurationDTO::setProductName);
        hasChangesList.add(nameField);

        // Product Description
        TetrisTextFieldV8 descriptionField = new TetrisTextFieldV8();
        descriptionField.setCaption(getTextForFormLabel("description"));
        descriptionField.setId(PRODUCT_DESCRIPTION_ELEMENT_ID);
        descriptionField.setMaxLength(FORM_TEXT_FIELD_WIDTH);
        descriptionField.setWidth(320, Unit.PIXELS);
        productDefinitionForm.addComponent(descriptionField);
        binder.bind(descriptionField, AgileRatesProductConfigurationDTO::getProductDescription, AgileRatesProductConfigurationDTO::setProductDescription);
        hasChangesList.add(descriptionField);

        // Product Type
        EnumSet<AgileRatesProductTypeEnum> productTypes = EnumSet.allOf(AgileRatesProductTypeEnum.class);
        productTypes.remove(AgileRatesProductTypeEnum.LOYALTY);
        productTypes.remove(AgileRatesProductTypeEnum.FIXED_ABOVE_BAR);
        productTypes.remove(AgileRatesProductTypeEnum.DAILY);
        productTypes.remove(AgileRatesProductTypeEnum.INDEPENDENTLY);
        productTypes.remove(AgileRatesProductTypeEnum.SMALL_GROUP);

        // Base Product
        baseField = new TetrisComboBoxV8<>();
        baseField.setCaption(getTextForFormLabel("agile.rates.base.product"));
        baseField.setEmptySelectionAllowed(false);
        baseField.setTextInputAllowed(false);
        baseField.setWidth(FORM_TEXT_FIELD_WIDTH, Unit.PIXELS);
        baseField.setItemCaptionGenerator(Product::getName);
        baseField.setItems(presenter.determineAllowedBaseProducts());
        baseField.setStyleGenerator(product -> {
                baseField.setStyleName(AgileRatesUiUtils.getStyleNameForProduct(baseField.getValue() == null ? product : baseField.getValue()));
                return product.isSystemDefault() ? null : AgileRatesUiUtils.getProductIcon(product).getHtml();
        });
        baseField.setItemIconGenerator(product -> {
                return AgileRatesUiUtils.getProductIcon(product);
        });
        baseField.addValueChangeListener((HasValue.ValueChangeListener<Product>) event -> {
            Set<AccomType> values = new HashSet<>(roomTypeSelect.getValue());
            Set<AccomType> baseProductRoomTypes = new HashSet<>(presenter.getBaseProductRoomTypes(event.getValue()));
            roomTypeSelect.setItems(baseProductRoomTypes);
            values.retainAll(baseProductRoomTypes);
            roomTypeSelect.setValue(values);
            if (event.getValue() != null) {
                if (presenter.isBaseFieldOrParentsFixed(event.getValue())) {
                    optimizedCheckBox.setValue(false);
                    optimizedProductFixedBaseProduct.setVisible(true);
                } else {
                    optimizedProductFixedBaseProduct.setVisible(false);
                }
                //set rate codes if loading an existing product or reset rate codes if primary product has changed
                if ((event.getValue() != null && event.getOldValue() == null) || presenter.hasPrimaryProductChanged(event.getValue(), event.getOldValue())) {
                    rateCodeSelect.setItems(presenter.getAllRateCodesFromPrimaryProduct(event.getValue()));
                }

                childPricingTypeRadioButtons.setItemCaptionGenerator(item -> {
                    if (ChildPricingType.USE_SYSTEM_DEFAULT != item) {
                        return getText(item.getCaptionKey());
                    }
                    return getText(CHILD_PRICING_TYPE_SEND) + presenter.getPrimaryProduct(baseField.getValue()).getName();
                });
            } else {
                optimizedCheckBox.setValue(false);
                optimizedProductFixedBaseProduct.setVisible(true);
            }
        });
        productDefinitionForm.addComponent(baseField);
        binder.forField(baseField)
                .asRequired(fieldRequiredText)
                .withValidator(selectedBaseProduct ->
                                presenter.selectedBaseProductIsFencingCompatibleWithProduct(selectedBaseProduct, typeField.getValue()),
                        getText("agile.rates.selected.base.type.fencing.compatible"))
                .bind(AgileRatesProductConfigurationDTO::getBaseProduct, AgileRatesProductConfigurationDTO::setBaseProduct);
        hasChangesList.add(baseField);

        //Product Type
        typeField = new TetrisComboBoxV8<>();
        typeField.setItems(productTypes);
        typeField.setCaption(getTextForFormLabel("agile.rates.product.type"));
        typeField.setEmptySelectionAllowed(false);
        typeField.setTextInputAllowed(false);
        typeField.setWidth(FORM_TEXT_FIELD_WIDTH, Unit.PIXELS);
        typeField.setItemCaptionGenerator(productType -> getText(productType.getCaptionKey()));
        typeField.sort(productType -> getText(((AgileRatesProductTypeEnum) productType).getCaptionKey()), SortDirection.ASCENDING);
        productDefinitionForm.addComponent(typeField);
        binder.forField(typeField)
                .asRequired(fieldRequiredText)
                .withValidator(typeEnum ->
                                presenter.selectedBaseProductIsFencingCompatibleWithProduct(baseField.getValue(), typeEnum),
                        getText("agile.rates.selected.base.type.fencing.compatible"))
                .bind(AgileRatesProductConfigurationDTO::getProductType, AgileRatesProductConfigurationDTO::setProductType);
        hasChangesList.add(typeField);

        //Optimized Checkbox
        optimizedProductFixedBaseProduct = new Label("(" + getText("agile.rates.optimized.with.fixed.base.product.error") + ")");
        optimizedProductFixedBaseProduct.setVisible(false);
        optimizedProductFixedBaseProduct.addStyleName("optimized-product-fixed-base-product");

        Label optimizationLevelMessage = createOptimizationLevelMessage();
        optimizedCheckBox = createOptimizedCheckBox(optimizationLevelMessage);
        binder.bind(optimizedCheckBox, AgileRatesProductConfigurationDTO::isOptimized, AgileRatesProductConfigurationDTO::setOptimized);
        hasChangesList.add(optimizedCheckBox);

        HorizontalLayout OptimizationLevelMessageContainer = createOptimizedCheckboxMessageContainer(optimizedCheckBox, optimizationLevelMessage, optimizedProductFixedBaseProduct);
        productDefinitionForm.addComponent(OptimizationLevelMessageContainer);

        //Seasonal Product Only
        productDefinitionForm.addComponent(seasonalProductOnlyCheckBox);

        // Free Night - Free Upgrade
        if (presenter.isConsortiaFreeNightEnabled() && presenter.isFreeNightProduct()) {
            freeNightAndFreeUpgradeLayout = new HorizontalLayout();
            freeNightAndFreeUpgradeLayout.setMargin(false);

            freeNightCheckBox = createFreeNightCheckBox();
            freeUpgradeCheckBox = createFreeUpgradeCheckBox();
            hasChangesList.add(freeUpgradeCheckBox);

            freeNightAndFreeUpgradeLayout.addComponents(
                    freeNightCheckBox,
                    new TetrisSpacer(),
                    freeUpgradeCheckBox);

            productDefinitionForm.addComponent(freeNightAndFreeUpgradeLayout);
        }

        //Rounding Rule
        roundingRuleDropDown = createRoundingRuleDropDown(binder);

        hasChangesList.add(roundingRuleDropDown);
        productDefinitionForm.addComponent(roundingRuleDropDown);

        //Floor Type
        productDefinitionForm.addComponent(createFloorTypeLayout());

        //Floor
        productDefinitionForm.addComponents(createFloorLayout());

        //Minimum Pricing Change
        minimumPriceChangeField = createMinimumPriceChangeField(binder);
        hasChangesList.add(minimumPriceChangeField);
        productDefinitionForm.addComponent(minimumPriceChangeField);
        optimizedCheckBox.addValueChangeListener(showHideMinimumPriceChangeFieldOnValueChange());
        optimizedCheckBox.addValueChangeListener(showHideAdjustmentByChangeFieldOnValueChange());

        //Override Type
        overrideTypeRadioButtons = createOverrideTypeRadioGroupButtons(presenter.getProductConfiguration().getProduct().isOptimized());
        binder.forField(overrideTypeRadioButtons).asRequired(getText("common.fieldrequired"))
                .bind(AgileRatesProductConfigurationDTO::getOverrideType, AgileRatesProductConfigurationDTO::setOverrideType);
        hasChangesList.add(overrideTypeRadioButtons);
        productDefinitionForm.addComponent(overrideTypeRadioButtons);

        //Public Checkbox
        HorizontalLayout checkboxHorizontalLayout = new HorizontalLayout();
        publicCheckBox = createPublicCheckBox();
        binder.forField(publicCheckBox)
                .bind(AgileRatesProductConfigurationDTO::isPubliclyAvailable, AgileRatesProductConfigurationDTO::setPubliclyAvailable);
        hasChangesList.add(publicCheckBox);

        checkboxHorizontalLayout.addComponent(publicCheckBox);

        productDefinitionForm.addComponent(checkboxHorizontalLayout);

        checkBoxesLayout = new HorizontalLayout();
        checkBoxesLayout.setCaption(getText("agile.rates.discount.applies.to"));
        checkBoxesLayout.addComponents(
                createAdjustmentAppliesCheckBox(getText("agile.rates.extra.adult"),
                        AgileRatesProductConfigurationDTO::isOffsetForExtraAdult, AgileRatesProductConfigurationDTO::setOffsetForExtraAdult),
                new TetrisSpacer(),
                createAdjustmentAppliesCheckBox(getText(presenter.getExtraChildCheckboxName()),
                        AgileRatesProductConfigurationDTO::isOffsetForExtraChild, AgileRatesProductConfigurationDTO::setOffsetForExtraChild));
        productDefinitionForm.addComponent(checkBoxesLayout);

        //Adjustment Applies to
        adjustmentByRadioButtons = createAdjustmentTypeRadioButtons(getText("agile.rates.adjustment.by"), AgileRatesProductConfigurationDTO::getOffsetMethod, AgileRatesProductConfigurationDTO::setOffsetMethod);
        productDefinitionForm.addComponent(adjustmentByRadioButtons);

        //Configure Adjustment by
        if (presenter.getProductConfiguration().getProduct() != null) {
            if (presenter.getProductConfiguration().getOffsetMethod() != null) {
                adjustmentByRadioButtons.setSelectedItem(presenter.getProductConfiguration().getOffsetMethod());
            } else {
                presenter.getProductConfiguration().setOffsetMethod(AgileRatesOffsetMethod.PERCENTAGE);
                adjustmentByRadioButtons.setSelectedItem(AgileRatesOffsetMethod.PERCENTAGE);
            }
        } else {
            adjustmentByRadioButtons.setSelectedItem(AgileRatesOffsetMethod.PERCENTAGE);
        }

        adjustmentByRadioButtons.setEnabled(!optimizedCheckBox.getValue());

        adjustmentByRadioButtons.setVisible(true);

        //Decisions Sent By
        decisionsSentByRadioButtons = createDecisionsSentByRadioButtons(getText("agile.rates.decisions.sent.by"), AgileRatesProductConfigurationDTO::isSendAdjustment, AgileRatesProductConfigurationDTO::setSendAdjustment);
        if (presenter.getProductConfiguration().getProduct() == null) {
            decisionsSentByRadioButtons.setSelectedItem(AgileRatesDecisionsSentBy.PRICE);
        }
        productDefinitionForm.addComponent(decisionsSentByRadioButtons);

        decisionsSentByRadioButtons.setVisible(presenter.isHiltonOptionToSendAdjustmentForAgileEnabled());

        childPricingTypeRadioButtons = createChildPricingTypeButtons(
                getText("child.pricing.type"), AgileRatesProductConfigurationDTO::getChildPricingType, AgileRatesProductConfigurationDTO::setChildPricingType);
        childPricingTypeRadioButtons.setVisible(presenter.isHiltonAgeBasedPricingEnabled());
        productDefinitionForm.addComponent(childPricingTypeRadioButtons);

        //Rate Shopping Rate Type
        if (presenter.isRDLEnabled()) {
            addRateTypeTo(productDefinitionForm);
            addRateTypeLOSTo(productDefinitionForm);
            setRateTypeLOSRadioButton();
        }

        layout.addComponent(productDefinitionForm);
    }

    private HasValue.ValueChangeListener<Boolean> showHideAdjustmentByChangeFieldOnValueChange() {
        return (HasValue.ValueChangeListener<Boolean>) valueChangeEvent -> {
            if (valueChangeEvent.getValue()) {
                adjustmentByRadioButtons.setSelectedItem(AgileRatesOffsetMethod.PERCENTAGE);
            }
            adjustmentByRadioButtons.setEnabled(!valueChangeEvent.getValue());
        };
    }

    private TetrisRadioButtonGroupV8<AgileRatesOffsetMethod> createAdjustmentTypeRadioButtons(
            String caption,
            ValueProvider<AgileRatesProductConfigurationDTO, AgileRatesOffsetMethod> getter,
            Setter<AgileRatesProductConfigurationDTO, AgileRatesOffsetMethod> setter) {
        final TetrisRadioButtonGroupV8<AgileRatesOffsetMethod> radioButtonGroup = new TetrisRadioButtonGroupV8();
        radioButtonGroup.setId(AGILE_RATES_ADJUSTMENT_BY_TYPE_ID);
        radioButtonGroup.setCaption(caption);
        radioButtonGroup.setItems(AgileRatesOffsetMethod.PERCENTAGE, AgileRatesOffsetMethod.FIXED);
        radioButtonGroup.setItemCaptionGenerator(item -> getText(item.getCaptionKey()));
        radioButtonGroup.addStyleName(AGILE_RATES_ADJUSTMENT_BY_TYPE_STYLE);
        radioButtonGroup.addValueChangeListener(value -> presenter.setIsAdjustmentTypeChanged(radioButtonGroup.hasChanges()));
        binder.forField(radioButtonGroup).asRequired(getText("common.fieldrequired")).bind(getter, setter);
        hasChangesList.add(radioButtonGroup);
        return radioButtonGroup;
    }

    private TetrisRadioButtonGroupV8<AgileRatesDecisionsSentBy> createDecisionsSentByRadioButtons(
            String caption,
            ValueProvider<AgileRatesProductConfigurationDTO, AgileRatesDecisionsSentBy> getter,
            Setter<AgileRatesProductConfigurationDTO, AgileRatesDecisionsSentBy> setter) {
        final TetrisRadioButtonGroupV8<AgileRatesDecisionsSentBy> radioButtonGroup = new TetrisRadioButtonGroupV8();
        radioButtonGroup.setId(AGILE_RATES_SEND_ADJUSTMENT_ID);
        radioButtonGroup.setCaption(caption);
        radioButtonGroup.setItems(AgileRatesDecisionsSentBy.PRICE, AgileRatesDecisionsSentBy.ADJUSTMENT);
        radioButtonGroup.setItemCaptionGenerator(item -> getText(item.getCaptionKey()));
        radioButtonGroup.addStyleName(AGILE_RATES_SEND_ADJUSTMENT_STYLE);
        radioButtonGroup.addValueChangeListener(value -> presenter.setSendAdjustmentByChanged(radioButtonGroup.hasChanges()));
        binder.forField(radioButtonGroup).asRequired(getText("common.fieldrequired")).bind(getter, setter);
        hasChangesList.add(radioButtonGroup);
        return radioButtonGroup;
    }

    private TetrisRadioButtonGroupV8<ChildPricingType> createChildPricingTypeButtons(
            String caption,
            ValueProvider<AgileRatesProductConfigurationDTO, ChildPricingType> getter,
            Setter<AgileRatesProductConfigurationDTO, ChildPricingType> setter) {
        final TetrisRadioButtonGroupV8<ChildPricingType> radioButtonGroup = new TetrisRadioButtonGroupV8();
        radioButtonGroup.setId(CHILD_PRICING_TYPE_BUTTONS_ID);
        radioButtonGroup.setCaption(caption);
        radioButtonGroup.setItems(
                ChildPricingType.DO_NOT_UPLOAD_AGE_BASED_PRICING, ChildPricingType.USE_SYSTEM_DEFAULT, ChildPricingType.CUSTOM_FOR_PRODUCT);
        radioButtonGroup.setItemCaptionGenerator(item -> getText(item.getCaptionKey()));
        radioButtonGroup.addStyleName(AGILE_RATES_SEND_ADJUSTMENT_STYLE);
        radioButtonGroup.addValueChangeListener(value -> presenter.setSendAdjustmentByChanged(radioButtonGroup.hasChanges()));
        if (CollectionUtils.isEmpty(presenter.getOccupantBuckets())) {
            radioButtonGroup.setDescription(getText("child.pricing.type.configure"), ContentMode.PREFORMATTED);
        }
        binder.forField(radioButtonGroup).asRequired(getText("common.fieldrequired")).bind(getter, setter);
        hasChangesList.add(radioButtonGroup);
        return radioButtonGroup;
    }

    private Label createOptimizationLevelMessage() {
        Label message = new Label();
        message.addStyleName("optimization-level-message");
        message.setValue(presenter.getOptimizationLevelMessage());
        message.setVisible(presenter.getProductConfiguration().isOptimized());
        return message;
    }

    private Label createBaseRoomTypeFloorMessage() {
        Label message = new Label();
        message.addStyleName("optimization-level-message");
        message.setValue(presenter.createFloorMessageString());
        message.setVisible(true);
        return message;
    }

    private HorizontalLayout createOptimizedCheckboxMessageContainer(TetrisCheckBoxV8 optimizedCheckBox, Label optimizationLevelMessage, Label optimizedProductFixedBaseProduct) {
        HorizontalLayout container = new HorizontalLayout();
        container.setMargin(false);
        container.addComponents(
                optimizedCheckBox,
                optimizationLevelMessage,
                optimizedProductFixedBaseProduct
        );

        return container;
    }

    private HorizontalLayout createFloorLayout() {
        Label floorMessage = createBaseRoomTypeFloorMessage();
        floorDecimalField = createFloorDecimalField(binder);
        HorizontalLayout container = new HorizontalLayout();
        container.setMargin(false);
        container.addComponents(
                floorDecimalField,
                floorMessage
        );
        container.setCaption(getText("agile.rates.floor") + " " + "<span style= color:red; line-height:-1;>*</span>");
        container.setCaptionAsHtml(true);
        return container;
    }

    private TetrisEnumComboBoxV8<RoundingRule> createRoundingRuleDropDown(Binder<AgileRatesProductConfigurationDTO> binder) {
        TetrisEnumComboBoxV8<RoundingRule> enumComboBox = new TetrisEnumComboBoxV8<>(RoundingRule.class);
        enumComboBox.setId(AGILE_RATES_ROUNDING_RULE_COMBOBOX_ID);
        enumComboBox.setCaption(getTextForFormLabel("agile.rates.rounding.rule"));
        enumComboBox.setWidth(FORM_TEXT_FIELD_WIDTH, Unit.PIXELS);
        enumComboBox.setEmptySelectionAllowed(false);
        enumComboBox.setTextInputAllowed(false);
        enumComboBox.setItemCaptionGenerator(item -> getText(item.getKey()));
        enumComboBox.addValueChangeListener(valueChangeEvent -> {
            RoundingRule newRoundingRule = valueChangeEvent.getValue();
            if (presenter.getProductConfiguration().getFloorPercentage() != null && baseField.getValue() != null) {
                floorDecimalField.setValue(
                        presenter.calculateFloorRelativeToPrimaryProductWhenRoundingRuleChanges(presenter.getProductConfiguration().getFloorPercentage().toString(),
                                baseField.getValue(), newRoundingRule)
                );
            }
        });
        binder.forField(enumComboBox)
                .bind(AgileRatesProductConfigurationDTO::getRoundingRule, AgileRatesProductConfigurationDTO::setRoundingRule);
        return enumComboBox;
    }

    private TetrisTextFieldV8 createFloorDecimalField(Binder<AgileRatesProductConfigurationDTO> binder) {
        TetrisTextFieldV8 field = new TetrisTextFieldV8();
        field.setId(AGILE_RATES_FLOOR_FIELD_ID);
        field.setWidth(FORM_TEXT_FIELD_WIDTH, Unit.PIXELS);
        field.addStyleName(TetrisValoTheme.TEXT_RIGHT_ALIGNED);
        field.setRequiredIndicatorVisible(false);
        checkFloorDecimalField(binder, field);
        hasChangesList.add(field);
        return field;
    }

    public void checkFloorDecimalField(Binder<AgileRatesProductConfigurationDTO> binder, TetrisTextFieldV8 field) {
        if (presenter.isFreeNightProduct()) {
            binder.forField(field)
                    .withNullRepresentation("")
                    .withConverter(new StringToBigDecimalConverter(getText("common.number.validation")))
                    .bind(AgileRatesProductConfigurationDTO::getFloor, AgileRatesProductConfigurationDTO::setFloor);
        } else {
            binder.forField(field)
                    .withNullRepresentation("")
                    .withConverter(new StringToBigDecimalConverter(getText("common.number.validation")))
                    .withValidator(
                            valueMustBeGreaterThanZero(),
                            getText("theNumberShouldBeGreaterThan", BigDecimal.ZERO)
                    )
                    .bind(AgileRatesProductConfigurationDTO::getFloor, AgileRatesProductConfigurationDTO::setFloor);
        }
    }

    private TetrisTextFieldV8 createMinimumPriceChangeField(Binder<AgileRatesProductConfigurationDTO> binder) {
        TetrisTextFieldV8 field = new TetrisTextFieldV8();
        field.setId(AGILE_RATES_MINIMUM_PRICE_CHANGE_ID);
        field.setCaption(getText("agile.rates.minimum.price.change"));
        field.setStyleName(TetrisValoTheme.TEXT_RIGHT_ALIGNED);
        field.setWidth(FORM_TEXT_FIELD_WIDTH, Unit.PIXELS);
        setInitialVisibilityToNonOptimized(field);
        binder.forField(field)
                .withConverter(new StringToBigDecimalConverter(getText("common.number.validation")))
                .withNullRepresentation(BigDecimal.ZERO)
                .withValidator(
                        createIsNotNegativeNumberPredicate(),
                        getText("greater.than.or.equal.to.number", BigDecimal.ZERO)
                )
                .withValidator(createIntegerRangeValidator())
                .bind(AgileRatesProductConfigurationDTO::getMinimumPriceChange, AgileRatesProductConfigurationDTO::setMinimumPriceChange);
        return field;
    }

    private BigDecimalRangeValidator createIntegerRangeValidator() {
        return new BigDecimalRangeValidator(getText("common.number.validation",
                getText("common.number.validation", 0, Integer.MAX_VALUE)),
                BigDecimal.ZERO,
                BigDecimal.valueOf(Integer.MAX_VALUE));
    }

    private void setInitialVisibilityToNonOptimized(TetrisTextFieldV8 field) {
        field.setVisible(false);
    }

    private SerializablePredicate<BigDecimal> createIsNotNegativeNumberPredicate() {
        return number -> number == null || number.compareTo(BigDecimal.ZERO) >= 0;
    }

    private HorizontalLayout createFloorTypeLayout() {
        floorTypeLayout = new HorizontalLayout();
        floorTypeRadioButtons = createFloorTypeRadioGroupButtons();

        floorPercentageOffField = createPercentageOffFloorField(presenter.getProductConfiguration().getFloorType().equals(FloorType.RELATIVE_TO_PRIMARY_FLOOR));
        hasChangesList.add(floorPercentageOffField);
        Label percentageLabelForFloorDecimalField = new Label("%");

        floorTypeLayout.setCaption(getText("linked.product.floor.type") + " " + "<span style= color:red; line-height:-1;>*</span>");
        floorTypeLayout.setCaptionAsHtml(true);
        floorTypeLayout.addComponents(floorTypeRadioButtons, floorPercentageOffField, percentageLabelForFloorDecimalField);

        return floorTypeLayout;
    }

    private TetrisTextFieldV8 createPercentageOffFloorField(boolean isRelativeToPrimaryProductFloorType) {
        floorPercentageOffField = new TetrisTextFieldV8();
        floorPercentageOffField.setId(FLOOR_PERCENTAGE_OFF_FIELD);
        floorPercentageOffField.setWidth(REDUCED_FORM_TEXT_FIELD_WIDTH, Unit.PIXELS);
        floorPercentageOffField.setEnabled(isRelativeToPrimaryProductFloorType && floorTypeRadioButtons.isEnabled());
        floorPercentageOffField.addValueChangeListener(valueChangeEvent -> {
            if (!floorPercentageOffField.isEnabled()) {
                floorPercentageOffField.reset();
            }
        });

        if (isRelativeToPrimaryProductFloorType) {
            bindPercentageOffFloorFieldWhenRelativeToPrimaryProduct();
        } else {
            bindPercentageOffFloorFieldWhenFixedRate();
        }
        floorPercentageOffField.setRequiredIndicatorVisible(false);
        return floorPercentageOffField;
    }

    private void bindPercentageOffFloorFieldWhenFixedRate() {
        binder.forField(floorPercentageOffField)
                .withNullRepresentation("")
                .withConverter(new StringToBigDecimalConverter(getText("common.number.validation")))
                .bind(AgileRatesProductConfigurationDTO::getFloorPercentage, AgileRatesProductConfigurationDTO::setFloorPercentage);
    }

    private void bindPercentageOffFloorFieldWhenRelativeToPrimaryProduct() {
        binder.forField(floorPercentageOffField)
                .withNullRepresentation("")
                .asRequired(getText(FIELD_REQUIRED_KEY))
                .withConverter(new StringToBigDecimalConverter(getText("common.number.validation")))
                .withValidator(valueMustNotBeNull(), getText("common.validation.empty"))
                .withValidator(floorPercentageOffsetFieldValidator())
                .bind(AgileRatesProductConfigurationDTO::getFloorPercentage, AgileRatesProductConfigurationDTO::setFloorPercentage);
    }

    private TetrisRadioButtonGroupV8<FloorType> createFloorTypeRadioGroupButtons() {
        TetrisRadioButtonGroupV8<FloorType> radioButtons = new TetrisRadioButtonGroupV8<>();
        radioButtons.setId(FLOOR_TYPE_RADIO_BUTTONS_ID);
        radioButtons.setItems(FloorType.FIXED_RATE, FloorType.RELATIVE_TO_PRIMARY_FLOOR);
        radioButtons.setItemCaptionGenerator(item -> getText(item.getCaptionKey()));
        radioButtons.addStyleName(AGILE_RATES_ADJUSTMENT_BY_TYPE_STYLE);
        radioButtons.addValueChangeListener(value -> {
            presenter.setIsFloorTypeChanged(radioButtons.hasChanges());
            if (radioButtons.hasChanges()) {
                floorPercentageOffField.reset();
            } else if (value.getValue().equals(FloorType.FIXED_RATE) && "".equals(floorPercentageOffField.getValue()) && floorPercentageOffField.getComponentError() != null) {
                floorPercentageOffField.setValue("0");
            }
        });
        binder.forField(radioButtons).bind(AgileRatesProductConfigurationDTO::getFloorType, AgileRatesProductConfigurationDTO::setFloorType);
        hasChangesList.add(radioButtons);
        return radioButtons;
    }

    private TetrisRadioButtonGroupV8<OverridableProductEnum> createOverrideTypeRadioGroupButtons(boolean isOptimized) {
        TetrisRadioButtonGroupV8<OverridableProductEnum> radioButtons = new TetrisRadioButtonGroupV8<>();
        radioButtons.setId(OVERRIDES_RADIO_BUTTONS_ID);
        radioButtons.setCaption(getText("linked.products.override.types"));
        radioButtons.setItems(OverridableProductEnum.NO_OVERRIDES, OverridableProductEnum.ALLOW_OVERRIDES, OverridableProductEnum.RESTRICT_OVERRIDES);
        radioButtons.setItemCaptionGenerator(item -> getText(item.getCaptionKey()));
        radioButtons.addStyleName(AGILE_RATES_ADJUSTMENT_BY_TYPE_STYLE);
        radioButtons.addValueChangeListener(value -> presenter.setIsOverrideTypeChanged(radioButtons.hasChanges()));

        if (!isOptimized) {
            radioButtons.setItemEnabledProvider(item -> !OverridableProductEnum.RESTRICT_OVERRIDES.equals(item));
        }
        return radioButtons;
    }

    private TetrisCheckBoxV8 createIsOverridableCheckbox() {
        TetrisCheckBoxV8 checkBox = new TetrisCheckBoxV8();
        checkBox.setId(IS_OVERRIDABLE_CHECKBOX_ID);
        checkBox.setCaption(getText("agile.rates.is.overridable"));
        return checkBox;
    }

    private TetrisCheckBoxV8 createPublicCheckBox() {
        TetrisCheckBoxV8 checkBox = new TetrisCheckBoxV8();
        checkBox.setId(AGILE_RATES_PUBLIC_CHECKBOX_ID);
        checkBox.setCaption(getText("agile.rates.public"));
        return checkBox;
    }

    private TetrisCheckBoxV8 createOptimizedCheckBox(Label optimizationLevelMessage) {
        TetrisCheckBoxV8 checkBox = new TetrisCheckBoxV8();
        checkBox.setId(OPTIMIZED_CHECKBOX_ID);
        checkBox.setCaption(getText("agile.rates.optimized"));
        checkBox.addValueChangeListener(event -> {
            optimizationLevelMessage.setVisible(event.getValue());
            presenter.setIsOptimizedChanged(checkBox.hasChanges());
        });
        return checkBox;
    }

    private TetrisCheckBoxV8 createFreeNightCheckBox() {
        TetrisCheckBoxV8 checkBox = new TetrisCheckBoxV8();
        checkBox.setId(AGILE_RATES_FREE_NIGHT_CHECKBOX_ID);
        checkBox.setCaption(getText("agile.rates.free.night"));
        checkBox.setEnabled(false);

        binder.forField(checkBox).bind(AgileRatesProductConfigurationDTO::isFreeNightEnabled, AgileRatesProductConfigurationDTO::setFreeNightEnabled);

        return checkBox;
    }

    private TetrisCheckBoxV8 createFreeUpgradeCheckBox() {
        TetrisCheckBoxV8 checkBox = new TetrisCheckBoxV8();
        checkBox.setId(AGILE_RATES_FREE_UPGRADE_CHECKBOX_ID);
        checkBox.setCaption(getText("agile.rates.free.upgrade"));

        binder.forField(checkBox).bind(AgileRatesProductConfigurationDTO::isFreeUpgradeEnabled, AgileRatesProductConfigurationDTO::setFreeUpgradeEnabled);

        return checkBox;
    }

    private HasValue.ValueChangeListener<Boolean> showHideMinimumPriceChangeFieldOnValueChange() {
        return valueChangeEvent ->
                minimumPriceChangeField.setVisible(valueChangeEvent.getValue());
    }

    private TetrisCheckBoxV8 createSeasonalProductOnlyCheckBox() {
        TetrisCheckBoxV8 seasonalProductOnly = new TetrisCheckBoxV8();
        seasonalProductOnly.setCaption(getText("agile.rates.default.inactive"));
        seasonalProductOnly.setId(SEASONAL_PRODUCT_ONLY_CHECK_BOX_ID);
        boolean productHasNoChildren = presenter.productHasNoChildren();
        seasonalProductOnly.setEnabled(productHasNoChildren);
        if (!productHasNoChildren) {
            seasonalProductOnly.setDescription(getText("agile.rates.parents.cannot.be.seasonal.only"));
        }
        binder.forField(seasonalProductOnly).bind(AgileRatesProductConfigurationDTO::isDefaultInactive, AgileRatesProductConfigurationDTO::setDefaultInactive);
        hasChangesList.add(seasonalProductOnly);
        return seasonalProductOnly;
    }

    private TetrisCheckBoxV8 createAdjustmentAppliesCheckBox(
            String caption,
            ValueProvider<AgileRatesProductConfigurationDTO, Boolean> getter,
            Setter<AgileRatesProductConfigurationDTO, Boolean> setter) {
        final TetrisCheckBoxV8 checkBox = new TetrisCheckBoxV8();
        checkBox.setCaption(caption);
        binder.forField(checkBox).bind(getter, setter);
        hasChangesList.add(checkBox);
        return checkBox;
    }

    private void addRateCodeAndRoomTypeTo(VerticalLayout layout, TetrisFilterableTwinColSelectV8<AccomType> roomTypeField) {
        rateCodeSelect = new TetrisFilterableTwinColSelectV8<>();
        rateCodeSelect.setCaption(getTextForFormLabel("common.rate.codes"));
        rateCodeSelect.setStyleName(FORM_LAYOUT_CAPTION_VERTICAL_ALIGN_TOP_KEY);
        binder.forField(rateCodeSelect)
                .withValidator(value -> !presenter.shouldShowRateCodesAndRoomTypes()
                        || !Objects.equals(value, Collections.emptySet()), getTextForFormLabel(FIELD_REQUIRED_KEY))
                .bind(AgileRatesProductConfigurationDTO::getRateCodes, AgileRatesProductConfigurationDTO::setRateCodes);
        hasChangesList.add(rateCodeSelect);
        rateCodeSelect.setRequiredIndicatorVisible(true);

        FormLayout rateCodeFormLayout = new FormLayout(rateCodeSelect);
        rateCodeFormLayout.setWidth(480, Unit.PIXELS);
        rateCodeFormLayout.setHeight(200, Unit.PIXELS);
        FormLayout roomTypeFormLayout = new FormLayout(roomTypeField);
        roomTypeFormLayout.setWidth(560, Unit.PIXELS);
        roomTypeFormLayout.setHeight(200, Unit.PIXELS);

        TetrisImageButton addButton = new TetrisImageButton(TetrisFontAwesome.ADD_BUTTON_BLUE, event -> {
            TetrisWindow window = new TetrisWindow();
            window.setId("addRateCode");
            window.setCaption(UiUtils.getText("agile.rates.add.rate.code"));

            VerticalLayout addRateCodeLayout = new VerticalLayout();
            String message = "<ul>" +
                    "<li>" + UiUtils.getText("agile.rates.add.rate.code.message.one") + "</li>" +
                    "<li>" + UiUtils.getText("agile.rates.add.rate.code.message.two") + "</li>" +
                    "<li>" + UiUtils.getText("agile.rates.add.rate.code.message.three") + "</li>" +
                    "<li>" + UiUtils.getText("agile.rates.add.rate.code.message.four") + "</li></ul>";
            Label label = new Label(message);
            label.setCaptionAsHtml(true);
            label.setContentMode(ContentMode.HTML);
            addRateCodeLayout.addComponent(label);
            Label rateCodeName = new Label(UiUtils.getText("agile.rates.rate.code.name") + ":");
            TetrisTextAreaV8 enterField = new TetrisTextAreaV8();
            enterField.setWidth(600, Unit.PIXELS);
            enterField.setId("rateCodeTextAreaId");

            HorizontalLayout horizontalLayout = new HorizontalLayout(rateCodeName, enterField);
            addRateCodeLayout.addComponent(horizontalLayout);

            TetrisButton apply = new TetrisButton(UiUtils.getText("add"));
            apply.addClickListener(clickEvent -> {
                if (!enterField.isEmpty()) {
                    String enteredRateCodes = enterField.getValue();
                    List<String> rateCodeList = AgileRatesUiUtils.createRateCodeList(enteredRateCodes, rateCodeSelect.getValue(), presenter.getAllUnassociatedRateCodes());
                    String cleanedRateCode = enteredRateCodes.replace("\n", "").trim();
                    String[] splitRateCodes = cleanedRateCode.split(",");
                    boolean isBlankRateCodePresent = enteredRateCodes.isBlank();
                    for (String rateCode : splitRateCodes) {
                        isBlankRateCodePresent = isBlankRateCodePresent || rateCode.isBlank();
                    }
                    if (isBlankRateCodePresent) {
                        presenter.showError(getText("agile.rates.add.rate.code.errorMessage.blank"));
                        return;
                    }
                    if (presenter.hasDuplicateRateCodesApplied(rateCodeList)) {
                        List<String> nonDuplicateRateCodeOffsets = presenter.createNonDuplicatedRateCodeList(rateCodeList);
                        rateCodeList.removeAll(nonDuplicateRateCodeOffsets);
                        String notificationMessage = getText("agile.rates.add.rate.code.notification") + "<br/>" + AgileRatesUiUtils.createProductBulletPoints(rateCodeList);
                        TetrisMessageBox warningDialogue = presenter.createWarningDialogue(UiUtils.getText("notification"), notificationMessage);
                        warningDialogue.setYesLabel(UiUtils.getText("common.continue"));
                        warningDialogue.setYesClickListener(ignored -> {
                            rateCodeSelect.addManualInputEventAction(nonDuplicateRateCodeOffsets);
                            window.close();
                        });
                        warningDialogue.addCloseListener(ignored -> {
                            rateCodeSelect.addManualInputEventAction(nonDuplicateRateCodeOffsets);
                            window.close();
                        });
                        warningDialogue.setNoButtonVisible(false);
                        warningDialogue.show();
                    } else {
                        rateCodeSelect.addManualInputEventAction(rateCodeList);
                        window.close();
                    }
                } else {
                    window.close();
                }
            });
            apply.addStyleName("primary");
            addRateCodeLayout.addComponent(apply);
            addRateCodeLayout.setComponentAlignment(apply, Alignment.MIDDLE_CENTER);
            window.setContent(addRateCodeLayout);
            window.show();
        });
        addButton.setEnabled(!isReadOnly());
        addButton.setId("rateCodeAddButton");
        addButton.setWidth(LocalizationDimensionSpecifier.forCurrentLocale().getPricingConfigAddButtonWidth(), Unit.PIXELS);
        FormLayout addButtonFormLayout = new FormLayout(addButton);
        addButtonFormLayout.setWidth(80, Unit.PIXELS);

        rateCodeAndRoomTypeContainer = new CssLayout(rateCodeFormLayout, addButtonFormLayout, roomTypeFormLayout);
        rateCodeAndRoomTypeContainer.addStyleName("rate-code-and-room-type-container");
        layout.addComponent(rateCodeAndRoomTypeContainer);
    }

    private TetrisFilterableTwinColSelectV8<AccomType> createRoomTypeField(String fieldRequiredText) {
        roomTypeTwinColSelect = new TetrisFilterableTwinColSelectV8<>();
        roomTypeTwinColSelect.setCaption(getTextForFormLabel("roomTypes"));
        roomTypeTwinColSelect.setStyleName(FORM_LAYOUT_CAPTION_VERTICAL_ALIGN_TOP_KEY);
        roomTypeTwinColSelect.addValueChangeListener(value -> {
            if (roomTypeTwinColSelect.hasChanges()) {
                presenter.setRoomTypesAndRoomClassesRemovedList(roomTypeTwinColSelect.getOriginalSelectedItems(), roomTypeTwinColSelect.getSelectedItems());
            }
        });
        binder.forField(roomTypeTwinColSelect)
                .withValidator(value -> !presenter.shouldShowRateCodesAndRoomTypes() || !Objects.equals(value, Collections.emptySet()), fieldRequiredText)
                .bind(AgileRatesProductConfigurationDTO::getRoomTypes, AgileRatesProductConfigurationDTO::setRoomTypes);
        hasChangesList.add(roomTypeTwinColSelect);
        roomTypeTwinColSelect.setRequiredIndicatorVisible(true);
        return roomTypeTwinColSelect;
    }

    private void addFencingTo(VerticalLayout container) {
        fencingContainer = new FencingFields(binder, presenter);
        hasChangesList.addAll(fencingContainer.getFencingFields());
        container.addComponent(fencingContainer);
    }

    private boolean fencingHasChanges() {
        return fencingContainer.getFencingFields().stream().anyMatch(TetrisComponent::hasChanges);
    }

    private void addPackagesTo(VerticalLayout layout, TetrisFilterableTwinColSelectV8<AgileRatesPackage> packagesComponent) {
        hasChangesList.add(packagesComponent);

        packageElementsContainer = new FormLayout(packagesComponent);
        packageElementsContainer.setWidth(650, Unit.PIXELS);
        packageElementsContainer.setHeight(200, Unit.PIXELS);
        layout.addComponent(packageElementsContainer);
    }

    private TetrisFilterableTwinColSelectV8<AgileRatesPackage> createPackages(String fieldRequiredText) {
        packagesTwinColSelect = new TetrisFilterableTwinColSelectV8<>();
        packagesTwinColSelect.setCaption(getTextForFormLabel("agile.rates.package.elements"));
        packagesTwinColSelect.setItemCaptionGenerator((ItemCaptionGenerator<AgileRatesPackage>) AgileRatesPackage::getName);
        packagesTwinColSelect.setItems(presenter.getAllPackages());
        packagesTwinColSelect.setStyleName(FORM_LAYOUT_CAPTION_VERTICAL_ALIGN_TOP_KEY);
        binder.forField(packagesTwinColSelect)
                .withValidator(value -> !presenter.isPackagesSelected() || !Objects.equals(value, Collections.emptySet()), fieldRequiredText)
                .bind(AgileRatesProductConfigurationDTO::getPackageElements, AgileRatesProductConfigurationDTO::setPackageElements);
        packagesTwinColSelect.setRequiredIndicatorVisible(true);
        return packagesTwinColSelect;
    }

    private void updateVisibility() {
        rateCodeAndRoomTypeContainer.setVisible(presenter.shouldShowRateCodesAndRoomTypes());
        fencingContainer.setVisible(presenter.isFencingSelected());
        packageElementsContainer.setVisible(presenter.isPackagesSelected());
        if (!presenter.isFencingSelected()) {
            fencingContainer.getFencingFields().forEach(TetrisComponent::reset);
        }
        if (!presenter.isPackagesSelected()) {
            packages.reset();
        }
    }

    private void updateVisibilityOnOptimizedCheckboxChange(boolean isOptimized) {
        productDefinitionForm.removeComponent(overrideTypeRadioButtons);
        overrideTypeRadioButtons = createOverrideTypeRadioGroupButtons(isOptimized);

        binder.removeBinding(overrideTypeRadioButtons);
        hasChangesList.remove(overrideTypeRadioButtons);

        if (presenter.getProductConfiguration().getOverrideType().equals(OverridableProductEnum.RESTRICT_OVERRIDES) && !isOptimized) {
            presenter.getProductConfiguration().setOverrideType(OverridableProductEnum.ALLOW_OVERRIDES);
        }

        binder.forField(overrideTypeRadioButtons).asRequired(getText("common.fieldrequired"))
                .bind(AgileRatesProductConfigurationDTO::getOverrideType, AgileRatesProductConfigurationDTO::setOverrideType);
        hasChangesList.add(overrideTypeRadioButtons);

        productDefinitionForm.addComponent(overrideTypeRadioButtons, 10);
    }

    private void updateFloorTypeFieldVisibility() {
        floorTypeLayout.removeComponent(floorPercentageOffField);
        hasChangesList.remove(floorPercentageOffField);
        binder.removeBinding(floorPercentageOffField);

        if (floorTypeRadioButtons.getValue().equals(FloorType.RELATIVE_TO_PRIMARY_FLOOR)) {
            floorPercentageOffField = createPercentageOffFloorField(true);
            floorDecimalField.setEnabled(false);
        } else {
            floorPercentageOffField = createPercentageOffFloorField(false);
            floorPercentageOffField.setValue("");
            floorDecimalField.setEnabled(true);
        }
        hasChangesList.add(floorPercentageOffField);
        floorTypeLayout.addComponent(floorPercentageOffField, 1);
    }

    private void updateFloorDecimalField() {
        if (floorTypeRadioButtons.getValue().equals(FloorType.RELATIVE_TO_PRIMARY_FLOOR) && floorPercentageOffField.getValue() != null) {
            BigDecimal floorPercentage = AgileRatesUiUtils.convertStringToBigDecimalOrElseNull(floorPercentageOffField.getValue());
            if (floorPercentage != null && baseField.getValue() != null) {
                floorDecimalField.setValue(presenter.calculateFloorRelativeToPrimaryProductWhenRoundingRuleChanges(floorPercentageOffField.getValue(),
                        baseField.getValue(), roundingRuleDropDown.getValue()));
            }
        }
    }

    private void updateFloorDecimalFieldAndFloorPercentageFieldOnBaseFieldChange() {
        if (baseField.getValue().getFloorType().equals(FloorType.RELATIVE_TO_PRIMARY_FLOOR)) {
            floorTypeRadioButtons.setValue(FloorType.RELATIVE_TO_PRIMARY_FLOOR);
            floorPercentageOffField.setValue(String.valueOf(baseField.getValue().getFloorPercentage()));
        } else if (baseField.getValue().isSystemDefaultOrIndependentProduct() && floorTypeRadioButtons.getValue().equals(FloorType.RELATIVE_TO_PRIMARY_FLOOR) &&
                presenter.getProductConfiguration().getFloorPercentage() != null && baseField.getValue() != null) {
            presenter.calculateFloorRelativeToPrimaryProductWhenRoundingRuleChanges(presenter.getProductConfiguration().getFloorPercentage().toString(),
                    baseField.getValue(), roundingRuleDropDown.getValue());
        }
    }

    @Override
    public boolean hasChanges() {
        return hasChangesList.stream().anyMatch(TetrisComponent::hasChanges);
    }

    @Override
    public String getPageHelpId() {
        return "1252";
    }

    @Override
    public boolean isValid() {
        if (binder.validate().hasErrors()) {
            TetrisNotification.showWarningMessage(getText("common.reviewselection"));
            return false;
        }
        return true;
    }

    @Override
    public Component getContent() {
        return this;
    }

    @Override
    public boolean onAdvance() {
        if (presenter.isRDLEnabled()) {
            Map<String, List<String>> warningMessagesForRateType = presenter.generateWarningMessagesIfRateTypeLOSRangeOverlapped();
            if (!warningMessagesForRateType.isEmpty()) {
                StringBuilder formattedWarningMessage = new StringBuilder();
                formattedWarningMessage.append(getText("rateType.overlap.min.max.los.warning"));
                for (Map.Entry<String, List<String>> entry : warningMessagesForRateType.entrySet()) {
                    String productName = entry.getKey();
                    String min = entry.getValue().get(0);
                    String max = entry.getValue().get(1);
                    formattedWarningMessage.append("<li>").append(productName).append(" (Minimum: ").append(min)
                            .append(". Maximum: ").append(max).append(")").append("</li>").append("<br/>");
                }
                TetrisMessageBox warningDialogue = presenter.createWarningDialogue(getText("warning"), formattedWarningMessage.toString());
                warningDialogue.setYesClickListener(clickEvent -> {
                    warningDialogue.close();
                });
                warningDialogue.setYesLabel(getText("reconfigure"));
                warningDialogue.setNoButtonVisible(false);
                warningDialogue.show();
                return false;
            }
        }

        if (presenter.getAlertList().contains(getText("agile.rates.system.floor.null.values"))) {
            TetrisNotification.showWarningMessage(getText("agile.rates.system.floor.null.values"));
            return false;
        }

        if (presenter.getAlertList().contains(getText("agile.rates.product.in.product.group.may.be.removed"))) {
            presenter.removeProductFromProductGroup();
        }
        if (presenter.getAlertList().contains(getText("agile.rates.definition.step.invalid.reason.hierarchy.warning"))) {
            presenter.removeInvalidHierarchies();
        }
        if (presenter.getAlertList().contains(getText("removing.of.custom.child.age.offsets"))) {
            presenter.removeOffsetsForProduct();
        }

        if (presenter.getAlertList().contains(getText("product.rename.delete.vendor.mapping"))) {
            presenter.deleteVendorMappingForProductIfPresent(presenter.getProductConfiguration().getProduct().getId());
        }

        return save(this, presenter);
    }

    @Override
    public boolean onBack() {
        return false;
    }

    @Override
    public TetrisConfirmationWindow getNavigationConfirmationDialogue(WizardStepDirection direction) {
        return confirmationIfOffsetsChangedWindow;
    }

    @Override
    public void updateStateOnNext() {
        ViewChangeState changeState = createChangeState();

        presenter.updateAlertList(changeState);

        if (changeState.viewChanged() && !confirmationNeeded()) {
            presenter.updateOffsets();
        }
    }

    private ViewChangeState createChangeState() {
        ViewChangeState changeState = new ViewChangeState();
        changeState.setRateCodeSelectChanged(rateCodeSelect.hasChanges());
        changeState.setRoomTypeSelectChanged(roomTypeTwinColSelect.hasChanges());
        changeState.setPackagesSelectChanged(packagesTwinColSelect.hasChanges());
        changeState.setFencingChanged(fencingHasChanges());
        changeState.setSeasonOnlyProductChangedToTrue(seasonOnlyProductChangedToTrue());
        changeState.setSeasonOnlyProductChangedToFalse(seasonOnlyProductChangedToFalse());
        changeState.setOptimizedCheckBoxChanged(optimizedCheckBox.hasChanges());
        changeState.setIsOverridableChanged(overrideTypeRadioButtons.hasChanges());
        changeState.setFloorFieldChanged(floorDecimalField.hasChanges() || floorPercentageOffField.hasChanges());
        changeState.setSendAdjustmentByChanged(decisionsSentByRadioButtons.hasChanges());
        changeState.setAdjustmentTypeChangedToFixed(adjustmentByRadioButtons.hasChanges() && adjustmentByRadioButtons.getValue().equals(AgileRatesOffsetMethod.FIXED));
        changeState.setAdjustmentTypeChangedToPercentage(adjustmentByRadioButtons.hasChanges() && adjustmentByRadioButtons.getValue().equals(AgileRatesOffsetMethod.PERCENTAGE));
        changeState.setFloorTypeChanged(floorTypeRadioButtons.hasChanges());
        changeState.setFloorPercentageChanged(floorPercentageOffField.hasChanges());
        changeState.setProductNameChanged(nameField.hasChanges());
        if (presenter.isRDLEnabled()) {
            changeState.setRateShoppingRateTypeChanged(rateTypeField.hasChanges() || rateTypeLOSRadioButtons.hasChanges() ||
                    rateTypeLOSMinField.hasChanges() || rateTypeLOSMaxField.hasChanges());
        }
        changeState.setViewChanged(hasChanges());
        return changeState;
    }

    private boolean seasonOnlyProductChangedToFalse() {
        return seasonalProductOnlyCheckBox.hasChanges() && !seasonalProductOnlyCheckBox.getValue();
    }

    private boolean seasonOnlyProductChangedToTrue() {
        return seasonalProductOnlyCheckBox.hasChanges() && seasonalProductOnlyCheckBox.getValue();
    }

    @Override
    public Component getInnerContent(WizardStepDirection direction) {
        return new AgileRatesConfigurationWarning(presenter.getAlertList(), true, false);
    }

    @Override
    public List<Runnable> getConfirmationRunnables(WizardStepDirection direction) {
        return Collections.singletonList(() -> presenter.updateOffsets());
    }

    @Override
    public List<BooleanSupplier> getNavButtonExecutionConditions(WizardStepDirection direction) {
        return new ArrayList<>(
                Arrays.asList(
                        this::hasChanges,
                        this::isValid
                )
        );
    }

    @Override
    public boolean isNavigationConfirmationDialogueNeeded(WizardStepDirection direction) {
        return confirmationNeeded();
    }

    private boolean confirmationNeeded() {
        return !presenter.getAlertList().isEmpty() && !presenter.getAlertList().contains(getText("agile.rates.system.floor.null.values"));
    }

    private void initBinders() {
        binder = new Binder<>();
        binder.addValueChangeListener((HasValue.ValueChangeListener<Object>) this::updateProductTypeComponentVisibility);
    }

    private void updateProductTypeComponentVisibility(HasValue.ValueChangeEvent<Object> valueChangeEvent) {
        Component component = valueChangeEvent.getComponent();
        if ((component.equals(typeField) && baseField.getValue() != null) || (component.equals(baseField) && typeField.getValue() != null)) {
            updateVisibility();
        }
        if ((component.equals(optimizedCheckBox))) {
            updateVisibilityOnOptimizedCheckboxChange(optimizedCheckBox.getValue());
        }
        if (component.equals(floorTypeRadioButtons)) {
            updateFloorTypeFieldVisibility();
        }
        if (component.equals(floorPercentageOffField)) {
            updateFloorDecimalField();
        }
        if (component.equals(baseField)) {
            updateFloorDecimalFieldAndFloorPercentageFieldOnBaseFieldChange();
        }
    }

    private void addRateTypeTo(FormLayout layout) {
        rateTypeField = new TetrisComboBoxV8<>();
        rateTypeField.setId("rateAvailabilityField");
        rateTypeField.setCaption(getTextForFormLabel("rateType"));
        rateTypeField.setEmptySelectionAllowed(false);
        rateTypeField.setTextInputAllowed(false);
        rateTypeField.setWidth(FORM_TEXT_FIELD_WIDTH, Unit.PIXELS);

        //filter options only for UI and present the options in the below order
        List<String> desiredRateTypes;
        desiredRateTypes = Arrays.asList(
                RDLConstants.ADVANCE_PURCHASE_TYPE_CODE,
                RDLConstants.ADVANCE_PURCHASE_LOYALTY_TYPE_CODE,
                RDLConstants.SEMI_FLEXIBLE_TYPE_CODE,
                RDLConstants.SEMI_FLEXIBLE_LOYALTY_TYPE_CODE,
                RDLConstants.BEST_FLEXIBLE_TYPE_CODE,
                RDLConstants.BEST_FLEXIBLE_LOYALTY_TYPE_CODE,
                AgileRatesWizardPresenter.RATE_TYPE_NA_CODE);

        List<String> webrateTypeList = presenter.getWebrateTypesForAgileRates();
        //Besides values from Webrate_Type table, add N/A option for UI
        webrateTypeList.add(AgileRatesWizardPresenter.RATE_TYPE_NA_CODE);

        List<String> filteredRateTypeList = desiredRateTypes.stream()
                .filter(webrateTypeList::contains)
                .collect(Collectors.toList());
        rateTypeField.setItems(filteredRateTypeList);
        rateTypeField.setItemCaptionGenerator(rateType -> getText(rateType));
        layout.addComponent(rateTypeField);

        binder.forField(rateTypeField)
                .asRequired(getText(FIELD_REQUIRED_KEY))
                .bind(AgileRatesProductConfigurationDTO::getRateType, AgileRatesProductConfigurationDTO::setRateType);
        hasChangesList.add(rateTypeField);

        rateTypeField.addValueChangeListener(value -> {
            showHideRateTypeLOS(rateTypeField.getValue());
        });
    }

    private void addRateTypeLOSTo(FormLayout layout) {
        rateTypeLOSRadioButtons = new TetrisRadioButtonGroupV8<>();
        rateTypeLOSRadioButtons.setId(RATE_TYPE_LOS_RADIO_BUTTONS_ID);
        rateTypeLOSRadioButtons.setCaption(getText("rateType.LOS") + ":");
        rateTypeLOSRadioButtons.setItems(ProductRateShoppingLOSEnum.ALL_LOS, ProductRateShoppingLOSEnum.CUSTOM_LOS_RANGE);
        rateTypeLOSRadioButtons.setItemCaptionGenerator(item -> item.getKey());
        rateTypeLOSRadioButtons.addStyleName(AGILE_RATES_RATE_SHOPPING_LOS_STYLE);
        rateTypeLOSRadioButtons.addValueChangeListener(value -> {
            showHideCustomRateTypeLOSFields(rateTypeLOSRadioButtons.getValue());
        });
        hasChangesList.add(rateTypeLOSRadioButtons);

        rateTypeLOSMinField = new TetrisTextFieldV8();
        rateTypeLOSMinField.setId("rateTypeLOSMinTextField");
        rateTypeLOSMinField.setCaption(getTextForFormLabel("agile.rates.rate.shopping.los.min"));
        rateTypeLOSMinField.setMaxLength(3);
        rateTypeLOSMinField.setWidth(80, Unit.PIXELS);
        binder.forField(rateTypeLOSMinField)
                .withNullRepresentation("")
                .asRequired(getText(FIELD_REQUIRED_KEY))
                .withConverter(new StringToIntegerConverter(getText("common.number.validation")))
                .withValidator(value -> AgileRatesUiUtils.isValidRateTypeLOSValue(value, rateTypeLOSRadioButtons.getValue()),
                        getText("independent.product.theNumberShouldBeGreaterThanAndLessThanOrEqual"))
                .bind(AgileRatesProductConfigurationDTO::getRateTypeLOSMin, AgileRatesProductConfigurationDTO::setRateTypeLOSMin);
        hasChangesList.add(rateTypeLOSMinField);

        rateTypeLOSMinField.addValueChangeListener(valueChangeEvent -> {
            if (!ignoreLOSRangeValidation && rateTypeLOSRadioButtons.getValue().equals(ProductRateShoppingLOSEnum.CUSTOM_LOS_RANGE) &&
                    !AgileRatesUiUtils.areLOSFieldsValid(rateTypeLOSMinField.getValue(), rateTypeLOSMaxField.getValue())) {
                rateTypeLOSMinField.setValueChangeEventDisabled(true);
                rateTypeLOSMinField.clear();
                rateTypeLOSMinField.setValueChangeEventDisabled(false);
            }
        });

        rateTypeLOSMaxField = new TetrisTextFieldV8();
        rateTypeLOSMaxField.setId("rateTypeLOSMaxTextField");
        rateTypeLOSMaxField.setCaption(getTextForFormLabel("agile.rates.rate.shopping.los.max"));
        rateTypeLOSMaxField.setMaxLength(3);
        rateTypeLOSMaxField.setWidth(80, Unit.PIXELS);
        binder.forField(rateTypeLOSMaxField)
                .withNullRepresentation("")
                .asRequired(getText(FIELD_REQUIRED_KEY))
                .withConverter(new StringToIntegerConverter(getText("common.number.validation")))
                .withValidator(value -> AgileRatesUiUtils.isValidRateTypeLOSValue(value, rateTypeLOSRadioButtons.getValue()),
                        getText("independent.product.theNumberShouldBeGreaterThanAndLessThanOrEqual"))
                .bind(AgileRatesProductConfigurationDTO::getRateTypeLOSMax, AgileRatesProductConfigurationDTO::setRateTypeLOSMax);
        hasChangesList.add(rateTypeLOSMaxField);

        rateTypeLOSMaxField.addValueChangeListener(valueChangeEvent -> {
            if (!ignoreLOSRangeValidation && rateTypeLOSRadioButtons.getValue().equals(ProductRateShoppingLOSEnum.CUSTOM_LOS_RANGE) &&
                    !AgileRatesUiUtils.areLOSFieldsValid(rateTypeLOSMinField.getValue(), rateTypeLOSMaxField.getValue())) {
                rateTypeLOSMaxField.setValueChangeEventDisabled(true);
                rateTypeLOSMaxField.clear();
                rateTypeLOSMaxField.setValueChangeEventDisabled(false);
            }
        });

        layout.addComponent(rateTypeLOSRadioButtons);
        HorizontalLayout rateTypeLOSCustomRangeLayout = new HorizontalLayout(new TetrisSpacer(100, Unit.PIXELS), rateTypeLOSMinField, rateTypeLOSMaxField);
        layout.addComponent(rateTypeLOSCustomRangeLayout);
    }

    private void showHideCustomRateTypeLOSFields(ProductRateShoppingLOSEnum value) {
        ignoreLOSRangeValidation = true;
        if (ProductRateShoppingLOSEnum.ALL_LOS.equals(value)) {
            showHideRateTypeLOSCustomRangeFields(false);
            rateTypeLOSMinField.setValue("-1");
            rateTypeLOSMaxField.setValue("-1");
        } else {
            showHideRateTypeLOSCustomRangeFields(true);
            if (presenter.getProductConfiguration().getProduct().isSystemDefault()) {
                rateTypeLOSMinField.setValue("1");
                rateTypeLOSMinField.setEnabled(false);
            } else {
                rateTypeLOSMinField.setValue("");
            }
            rateTypeLOSMaxField.setValue("");
        }
        ignoreLOSRangeValidation = false;
    }

    public void showHideRateTypeLOSCustomRangeFields(boolean visible) {
        rateTypeLOSMinField.setVisible(visible);
        rateTypeLOSMaxField.setVisible(visible);
    }

    protected void setRateTypeLOSRadioButtons(ProductRateShoppingLOSEnum value) {
        rateTypeLOSRadioButtons.setValueChangeEventDisabled(true);
        rateTypeLOSRadioButtons.setValue(value);
        rateTypeLOSRadioButtons.setValueChangeEventDisabled(false);
    }

    public void enableRateTypeLOSMinField(boolean enable) {
        rateTypeLOSMinField.setEnabled(enable);
    }

    public void hideRateTypeLOSFields() {
        rateTypeLOSRadioButtons.setVisible(false);
        rateTypeLOSMinField.setVisible(false);
        rateTypeLOSMaxField.setVisible(false);
    }

    private void showHideRateTypeLOS(String value) {
        ignoreLOSRangeValidation = true;
        if (AgileRatesWizardPresenter.RATE_TYPE_NA_CODE.equals(value)) {
            showHideRateTypeLOS(false);

        } else {
            showHideRateTypeLOS(true);
        }
        ignoreLOSRangeValidation = false;
    }

    public void showHideRateTypeLOS(boolean visible) {
        rateTypeLOSRadioButtons.setVisible(visible);
        if (!rateTypeLOSRadioButtons.getValue().equals(ProductRateShoppingLOSEnum.ALL_LOS)) {
            rateTypeLOSMinField.setVisible(visible);
            rateTypeLOSMaxField.setVisible(visible);
        }
    }

    public void setRateTypeLOSRadioButton() {
        Integer rateTypeLOSMin = presenter.getProductConfiguration().getRateTypeLOSMin();
        String rateType = presenter.getProductConfiguration().getRateType();
        setRateTypeLOSRadioButtons(rateTypeLOSMin != null && rateTypeLOSMin.compareTo(0) > 0 ? ProductRateShoppingLOSEnum.CUSTOM_LOS_RANGE : ProductRateShoppingLOSEnum.ALL_LOS);
        if (presenter.isRDLEnabled() && presenter.isIndependentProductsEnabled() && rateType != null && !rateType.equals(AgileRatesWizardPresenter.RATE_TYPE_NA_CODE)) {
            showHideRateTypeLOSCustomRangeFields(rateTypeLOSMin != null && rateTypeLOSMin.compareTo(Integer.valueOf(0)) > 0);
            enableRateTypeLOSMinField(!presenter.getProductConfiguration().getProduct().isSystemDefault());
        } else {
            hideRateTypeLOSFields();
        }
    }
}
