package com.ideas.tetris.ui.modules.functionspace.demandcalendar.calendar;

import com.ideas.tetris.pacman.services.functionspace.dto.FunctionSpaceDemandCalendarForecastLevel;

public enum FunctionSpaceForecastLevel {

    LOW("low"),
    MEDIUM("medium"),
    HIGH("high");

    private String styleName;

    FunctionSpaceForecastLevel(String styleName) {
        this.styleName = styleName;
    }

    public String getStyleName() {
        return styleName;
    }

    public static FunctionSpaceForecastLevel convert(FunctionSpaceDemandCalendarForecastLevel calendarForecastLevel) {
        if (calendarForecastLevel.equals(FunctionSpaceDemandCalendarForecastLevel.HIGH)) {
            return HIGH;
        } else if (calendarForecastLevel.equals(FunctionSpaceDemandCalendarForecastLevel.MEDIUM)) {
            return MEDIUM;
        } else {
            return LOW;
        }
    }
}
