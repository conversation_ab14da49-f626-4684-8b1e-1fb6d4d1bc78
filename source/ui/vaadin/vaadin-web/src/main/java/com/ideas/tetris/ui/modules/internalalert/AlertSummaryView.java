package com.ideas.tetris.ui.modules.internalalert;

import com.ideas.tetris.pacman.services.internalalert.InternalAlertStatus;
import com.ideas.tetris.pacman.services.internalalert.InternalAlertType;
import com.ideas.tetris.pacman.services.internalalert.dto.AlertSummaryDto;
import com.ideas.tetris.ui.common.cdi.TetrisNavigatorView;
import com.ideas.tetris.ui.common.component.TetrisBeanItemContainer;
import com.ideas.tetris.ui.common.component.table.TetrisTable;
import com.ideas.tetris.ui.common.component.tabsheet.TetrisTabSheet;
import com.ideas.tetris.ui.common.util.RefreshAware;
import com.ideas.tetris.ui.modules.commons.forms.customfield.LinkButtonCustomField;
import com.ideas.tetris.ui.modules.monitor.common.JobFilterUtil;
import com.vaadin.annotations.Title;
import com.vaadin.ui.Component;
import com.vaadin.v7.ui.Table;
import com.vaadin.v7.ui.VerticalLayout;

import java.util.Arrays;
import java.util.HashSet;
import java.util.List;

@Title("Alert Summary")
public class AlertSummaryView extends TetrisNavigatorView<AlertSummaryPresenter, Void> implements RefreshAware {
    TetrisTable summaryTable;
    private TetrisBeanItemContainer<AlertSummaryDto> tetrisBeanItemContainer;

    @Override
    protected void initView() {
        // letting presenter control view
    }

    protected void initViewInternal() {
        setSizeFull();
        VerticalLayout mainLayout = new VerticalLayout();
        mainLayout.setSizeFull();
        mainLayout.setMargin(true);
        mainLayout.setSpacing(true);

        addTable(mainLayout);
        mainLayout.setExpandRatio(summaryTable, 1.0f);
        setCompositionRoot(mainLayout);
    }

    protected void addTable(VerticalLayout layout) {
        summaryTable = new TetrisTable();
        summaryTable.setDisplayRowCount(false);

        tetrisBeanItemContainer = new TetrisBeanItemContainer<AlertSummaryDto>(AlertSummaryDto.class);

        summaryTable.setContainerDataSource(tetrisBeanItemContainer);

        String alertTypeCol = "alertType";
        String newCountCol = "newCount";
        String notifiedCountCol = "notifiedCount";
        String resolvedCountCol = "resolvedCount";
        summaryTable.addGeneratedColumn(newCountCol, new Table.ColumnGenerator() {
            @Override
            public Object generateCell(Table source, Object itemId, Object columnId) {
                AlertSummaryDto dto = (AlertSummaryDto) itemId;
                return createDetailsLink(dto.getAlertType(), InternalAlertStatus.NEW, dto.getNewCount());
            }
        });
        summaryTable.addGeneratedColumn(notifiedCountCol, new Table.ColumnGenerator() {
            @Override
            public Object generateCell(Table source, Object itemId, Object columnId) {
                AlertSummaryDto dto = (AlertSummaryDto) itemId;
                return createDetailsLink(dto.getAlertType(), InternalAlertStatus.NOTIFIED, dto.getNotifiedCount());
            }
        });
        summaryTable.addGeneratedColumn(resolvedCountCol, new Table.ColumnGenerator() {
            @Override
            public Object generateCell(Table source, Object itemId, Object columnId) {
                AlertSummaryDto dto = (AlertSummaryDto) itemId;
                return createDetailsLink(dto.getAlertType(), InternalAlertStatus.RESOLVED, dto.getResolvedCount());
            }
        });

        summaryTable.setVisibleColumns(new Object[]{alertTypeCol, newCountCol, notifiedCountCol, resolvedCountCol});
        summaryTable.setColumnHeaders("Alert Type", "New", "Notified", "Resolved");
        summaryTable.setSizeFull();
        summaryTable.setColumnCollapsingAllowed(false);

        layout.addComponent(summaryTable);
    }

    private Component createDetailsLink(InternalAlertType alertType, InternalAlertStatus alertStatus, int count) {
        LinkButtonCustomField linkField = new LinkButtonCustomField();
        linkField.setValue(count);
        linkField.addClickListener(new LinkButtonCustomField.ClickListener() {
            @Override
            public void onClick(LinkButtonCustomField.ClickEvent event) {
                goToDetailsTab(alertType, alertStatus);
            }
        });
        return linkField;
    }

    private void goToDetailsTab(InternalAlertType alertType, InternalAlertStatus alertStatus) {
        TetrisTabSheet tabSheet = findAncestor(TetrisTabSheet.class);
        if (tabSheet != null) {
            InternalAlertFilterDto filterDto = new InternalAlertFilterDto();
            filterDto.setAlertStatuses(new HashSet<String>(Arrays.asList(alertStatus.toString())));
            filterDto.setAlertTypes(new HashSet<String>(Arrays.asList(alertType.toString())));
            tabSheet.setSelectedTab(AlertDetailsView.class, JobFilterUtil.toJSON(filterDto));
        }
    }

    @Override
    public void refresh() {
        presenter.updateData();
    }

    public void updateData(List<AlertSummaryDto> results) {
        tetrisBeanItemContainer.removeAllItems();
        tetrisBeanItemContainer.addAll(results);
    }

}
