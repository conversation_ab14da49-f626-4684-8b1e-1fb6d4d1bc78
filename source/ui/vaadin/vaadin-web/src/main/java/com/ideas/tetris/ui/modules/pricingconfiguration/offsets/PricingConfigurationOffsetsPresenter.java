package com.ideas.tetris.ui.modules.pricingconfiguration.offsets;


import com.google.common.annotations.VisibleForTesting;
import com.ideas.infra.tetris.security.domain.TetrisPermissionKey;
import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.configparams.GUIConfigParamName;
import com.ideas.tetris.pacman.common.configparams.IPConfigParamName;
import com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName;
import com.ideas.tetris.pacman.common.utils.systemconfig.SystemConfig;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomClassPriceRank;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomType;
import com.ideas.tetris.pacman.services.accommodation.service.AccomClassPriceRankService;
import com.ideas.tetris.pacman.services.accommodation.service.AccommodationService;
import com.ideas.tetris.pacman.services.agilerates.configuration.dto.ProductHierarchyDto;
import com.ideas.tetris.pacman.services.agilerates.configuration.entity.ProductAccomType;
import com.ideas.tetris.pacman.services.agilerates.configuration.entity.ProductHierarchy;
import com.ideas.tetris.pacman.services.agilerates.configuration.service.AgileRatesConfigurationService;
import com.ideas.tetris.pacman.services.agilerates.configuration.service.ProductHierarchyValidationService;
import com.ideas.tetris.pacman.services.bestavailablerate.AccomTypeSupplementService;
import com.ideas.tetris.pacman.services.bestavailablerate.PricingConfigurationValidationService;
import com.ideas.tetris.pacman.services.bestavailablerate.dto.CPOffsetConfigDTO;
import com.ideas.tetris.pacman.services.bestavailablerate.dto.CPOffsetConfigPeriodDTO;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.AccomTypeSupplement;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.CPConfigOffsetAccomType;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.OccupancyType;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.OffsetMethod;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.perpersonpricing.MaximumOccupantsEntity;
import com.ideas.tetris.pacman.services.perpersonpricing.OccupantBucketEntity;
import com.ideas.tetris.pacman.services.perpersonpricing.PerPersonPricingService;
import com.ideas.tetris.pacman.services.pricingconfiguration.dto.OffsetUploadDto;
import com.ideas.tetris.pacman.services.pricingconfiguration.dto.PricingConfigurationDTO;
import com.ideas.tetris.pacman.services.pricingconfiguration.entity.PricingAccomClass;
import com.ideas.tetris.pacman.services.pricingconfiguration.entity.PricingBaseAccomType;
import com.ideas.tetris.pacman.services.pricingconfiguration.service.PricingConfigurationLTBDEService;
import com.ideas.tetris.pacman.services.pricingconfiguration.service.PricingConfigurationOffsetService;
import com.ideas.tetris.pacman.services.pricingconfiguration.service.PricingConfigurationService;
import com.ideas.tetris.pacman.services.pricingconfiguration.service.PricingConfigurationUploadService;
import com.ideas.tetris.pacman.services.pricingconfiguration.service.SeasonNameTooLargeException;
import com.ideas.tetris.pacman.services.product.Product;
import com.ideas.tetris.pacman.services.property.PropertyService;
import com.ideas.tetris.pacman.services.restfortesting.ForTesting;
import com.ideas.tetris.pacman.services.roomtyperecoding.services.RoomTypeRecodingService;
import com.ideas.tetris.pacman.services.tax.entity.Tax;
import com.ideas.tetris.pacman.services.tax.service.TaxService;
import com.ideas.tetris.pacman.util.SeasonService;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.common.xml.schema.workcontext.v1.WorkContextType;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import com.ideas.tetris.spring.SpringAutowired;
import com.ideas.tetris.ui.common.component.SeasonsFilterBar;
import com.ideas.tetris.ui.common.component.messagebox.TetrisMessageBox;
import com.ideas.tetris.ui.common.component.window.TetrisConfirmationWindow;
import com.ideas.tetris.ui.common.util.DateFormatUtil;
import com.ideas.tetris.ui.common.util.JavaDateUtil;
import com.ideas.tetris.ui.common.util.UiUtils;
import com.ideas.tetris.ui.modules.agilerates.AgileRatesPresenter;
import com.ideas.tetris.ui.modules.pricingconfiguration.ceilingfloor.nongroup.PricingConfigurationOffsetMainPresenter;
import com.ideas.tetris.ui.modules.pricingconfiguration.layout.InvalidSeasonDatesLayout;
import com.ideas.tetris.ui.modules.reports.usermodified.Status;
import com.vaadin.ui.Button;
import com.vaadin.ui.Label;
import com.vaadin.ui.VerticalLayout;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.log4j.Logger;
import org.apache.poi.ss.usermodel.Workbook;
import org.joda.time.LocalDate;
import org.springframework.beans.factory.annotation.Autowired;

import javax.inject.Inject;
import java.math.BigDecimal;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Predicate;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.ideas.tetris.pacman.services.bestavailablerate.entity.OffsetMethod.FIXED_OFFSET;
import static org.apache.commons.collections.CollectionUtils.isEmpty;

@SpringAutowired
public class PricingConfigurationOffsetsPresenter extends OffsetPresenter<PricingConfigurationOffsetsView> {
    protected static final String OFFSETS_GROUP_HELP_ID = "1154";
    protected static final String OFFSETS_FUNCTION_SPACE_HELP_ID = "1183";
    protected static final Pattern pattern = Pattern.compile("([A-Z\\s-]+)(<BR/>)?");

    private static final String BREAKS_ROOM_TYPE_OFFSET_LOWER_THAN_FLOOR = "breaksRoomTypeOffsetLowerThanFloor";
    private static final String BREAKS_ROOM_TYPE_OFFSET_LOWER_THAN_SEASON_FLOOR = "breaksRoomTypeOffsetLowerThanSeasonFloor";
    private static final String BREAKS_ROOM_CLASS_RANK = "breaksRoomClassRank";
    private static final Integer MAX_ADULTS_DISPLAYED = 5;
    private static final Integer MIN_ADULTS_DISPLAYED = 2;
    private static final Integer MAX_CHILDREN_DISPLAYED = 5;
    private static final Integer MIN_CHILDREN_DISPLAYED = 0;
    private static final String WINDOW_CAPTION_REVIEW_CONFIGURATION = "reviewConfiguration";
    private static final String WINDOW_YES_LABEL_OK = "ok";
    private static final String WINDOW_NO_LABEL = "common.cancel";
    private static final String BREAKS_CHILD_NEGATIVE_OFFSET = "breaksChildNegativeOffset";
    private static final String BREAKS_ONE_ADULT_OFFSET = "breaksOneAdultOffset";
    private static final String BREAKS_ADULT_OFFSET_HIERARCHY = "breaksAdultOffsetHierarchy";
    private static final String BREAKS_CHILD_OFFSET_HIERARCHY = "breaksChildOffsetHierarchy";
    private static final String BREAKS_CHILD_AGE_OFFSET_HIERARCHY = "breaksChildAgeOffsetHierarchy";
    private static final Integer DAILY_BAR_PRICING_RULE_TYPE_TWO = 2;
    private static final Integer DAILY_BAR_PRICING_RULE_TYPE_THREE = 3;

    @Inject
    private AgileRatesPresenter agileRatesPresenter;

    @Autowired
    PricingConfigurationOffsetService offsetService;

    @Autowired
    PricingConfigurationValidationService pricingConfigurationValidationService;

    @Autowired
    PacmanConfigParamsService pacmanConfigParamsService;

    @Autowired
    PricingConfigurationService pricingConfigurationService;

    @Autowired
    PerPersonPricingService perPersonPricingService;

    @Autowired
    private SeasonService seasonService;
    @Autowired
    private RoomTypeRecodingService roomTypeRecodingService;

    @Autowired
    private DateService dateService;

    @Autowired
    private AccomClassPriceRankService accomClassPriceRankService;

    @Autowired
    private TaxService taxService;

    @Autowired
    AccommodationService roomClassService;

    @Autowired
    AccomTypeSupplementService accomTypeSupplementService;

    @Autowired
    AgileRatesConfigurationService agileRatesConfigurationService;

    @Autowired
    PricingConfigurationUploadService pricingConfigurationUploadService;

    @Autowired
    private ProductHierarchyValidationService productHierarchyValidationService;

    @Autowired
    private PricingConfigurationLTBDEService pricingConfigurationLTBDEService;
    @Autowired
    private PropertyService propertyService;

    private boolean isContinuousPricingEnabled;
    private boolean isCPBaseRoomTypeOnlyEnabled;
    private CPOffsetConfigDTO cpOffsetConfigDTO;
    private Boolean isAdvancedPriceRankingEnabled;
    private Boolean isChildAgeBucketsEnabled;
    private Boolean isGroupPricing;
    private Boolean isIndependentProductsEnabled;
    private Boolean isPerPersonPricingEnabled;
    private Boolean isRoomTypeRecodingUIEnabled;
    private Boolean isSupplementsEnabled;
    private List<AccomClassPriceRank> accomClassPriceRank;
    private List<AccomType> accomTypes;
    private List<AccomTypeSupplement> fixedValueAccomTypeSupplements;
    private List<CPConfigOffsetAccomType> cpConfigOffsetAccomTypes;
    private List<MaximumOccupantsEntity> maximumOccupantsEntities = null;
    private List<OccupantBucketEntity> occupantBucketEntities = null;
    private List<PricingAccomClass> excludedPricingAccomClasses;
    private List<PricingAccomClass> pricingAccomClasses;
    private List<PricingConfigurationOffsetSeasonWrapper> seasons;
    private List<String> baseRoomTypes = new ArrayList<>();
    private LocalDate caughtUpLocalDate;
    private OccupancyType baseOccupancyType;
    private PricingConfigurationDTO pricingConfigurationDTO;
    private Set<CPConfigOffsetAccomType> nonBaseRoomTypeOffsets = new HashSet<>();
    protected LocalDate systemDateAsLocalDate;
    private boolean isMissingRoomTypeAlertOpen;

    @Override
    public void onViewInit() {
        setupParameters();
    }

    @VisibleForTesting
    void setupParameters() {
        isContinuousPricingEnabled = pacmanConfigParamsService.getBooleanParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED);
        isChildAgeBucketsEnabled = pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.CP_CHILD_AGE_BUCKETS_ENABLED);
        baseOccupancyType = pricingConfigurationService.getBaseOccupancyType();
        isPerPersonPricingEnabled = pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.PER_PERSON_PRICING_ENABLED);
        isRoomTypeRecodingUIEnabled = pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.ROOM_TYPE_RECODING_UI_ENABLED);
        isCPBaseRoomTypeOnlyEnabled = pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.CPBASE_ROOM_TYPE_ONLY_ENABLED);
        isAdvancedPriceRankingEnabled = pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.IS_ADVANCED_PRICE_RANKING_ENABLED);
        isIndependentProductsEnabled = pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.INDEPENDENT_PRODUCTS_ENABLED);
        isMissingRoomTypeAlertOpen = roomTypeRecodingService.isExistingMissingRoomTypeAlertOpen();
        caughtUpLocalDate = dateService.getCaughtUpLocalDate();
        systemDateAsLocalDate = getSystemDateAsLocalDate();
    }

    @VisibleForTesting
    void loadData() {
        excludedPricingAccomClasses = new ArrayList<>();
        if (isIndependentProductsEnabled && pricingConfigurationDTO.getIndependentProductDTO() != null && pricingConfigurationDTO.getProduct().isIndependentProduct()) {
            pricingAccomClasses = pricingConfigurationService.getPricingAccomClassesForProduct(pricingConfigurationDTO.getProduct());
        } else {
            pricingAccomClasses = pricingConfigurationService.getPricingAccomClasses();
        }
        baseRoomTypes = pricingAccomClasses.stream().map(PricingAccomClass::getAccomType).map(AccomType::getName).collect(Collectors.toList());
        if (isOccupantValueGroupingAvailable()) {
            occupantBucketEntities = perPersonPricingService.getOccupantBuckets();
            maximumOccupantsEntities = perPersonPricingService.getMaximumOccupants();
        }
        cpConfigOffsetAccomTypes = offsetService.retrieveOffsetConfigNoPriceExcluded(uiContext.getPropertyId(), true, getPricingConfigurationDTOProductID());
        seasons = getSeasons();
    }

    @Override
    public void onViewOpened(PricingConfigurationDTO pricingConfigurationDTO) {
        this.pricingConfigurationDTO = pricingConfigurationDTO;
        this.isGroupPricing = pricingConfigurationDTO.isGroupPricing();

        loadData();

        init(true);
        view.onSeasonsChange(value -> view.populateSeasons(getSeasonsOnChangeEvent(value.getType(), value.isSelected())));
    }

    public String getPermissionForPage() {
        // Pricing Configuration module uses this view but doesn't pass a parameter
        if (this.isGroupPricing == null) {
            return (isPricingConfigurationPermissionsEnabled()) ? TetrisPermissionKey.PRICING_CONFIGURATION_OFFSETS : TetrisPermissionKey.PRICING_CONFIGURATION_TRANSIENT_CEIL_AND_FLOOR;
        }
        // This is the Group Pricing Configuration module
        if (this.isGroupPricing) {
            //point to group pricing permission
            return TetrisPermissionKey.FUNCTION_GROUP_PRICING_CONFIG_RATE;
        } else {
            // This is the Function Space module
            //point to function space permission
            return TetrisPermissionKey.FUNCTION_SPACE_CONFIGURATION_RATE;
        }
    }

    @ForTesting
    public void setMissingRoomTypeAlertOpen(boolean missingRoomTypeAlertOpen) {
        isMissingRoomTypeAlertOpen = missingRoomTypeAlertOpen;
    }

    public boolean isMissingRoomTypeAlertOpen() {
        return isMissingRoomTypeAlertOpen;
    }

    public boolean isPricingConfigurationPermissionsEnabled() {
        return Boolean.parseBoolean(pacmanConfigParamsService.getParameterValueByClientLevel(FeatureTogglesConfigParamName.PRICING_CONFIGURATION_PERMISSIONS_ENABLED.value()));
    }

    public boolean isFunctionSpace() {
        return isGroupPricing != null && !isGroupPricing;
    }

    @Override
    public void onWorkContextChange(WorkContextType workContextType) {
        agileRatesPresenter.navigateToProductListing();
    }


    @VisibleForTesting
    void init(boolean reloadSeasons) {
        view.closeSeasonWindow();
        if (reloadSeasons) {
            seasons = getSeasons();
        }
        view.afterPresenterInit();
        view.setSeasonTableHasChanges(false);
        cpOffsetConfigDTO = offsetService.retrieveOffsetConfigDTO(pricingAccomClasses, baseOccupancyType, cpConfigOffsetAccomTypes, getRoomTypesMappedToProduct());
        view.populateDefaultOffsets(getDefaultOffsets(true));
        updateSeasonsTable();
        isPerPersonPricingEnabled = pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.PER_PERSON_PRICING_ENABLED.value(),
                PacmanWorkContextHelper.getClientCode(), PacmanWorkContextHelper.getPropertyCode());
        view.intiPricingConfigurationOffsetDataHolder();
    }

    private List<PricingConfigurationOffsetWrapper> getDefaultOffsets(boolean overlayDataInDatabase) {
        CPOffsetConfigPeriodDTO defaultOffsets = cpOffsetConfigDTO.getDefaultOffsets();
        LinkedHashMap<PricingAccomClass, LinkedHashMap<AccomType, LinkedList<CPConfigOffsetAccomType>>> offsets = defaultOffsets.getOffsets();
        filterOffsetsAgainstDiscontinuedAccomTypes(offsets);

        List<PricingConfigurationOffsetWrapper> defaultOffsetWrappers = getTreeStructure(overlayDataInDatabase, offsets);

        Set<CPConfigOffsetAccomType> uiOffsets = getDefaultOffsetsDisplayedOnScreen(defaultOffsetWrappers);
        Set<CPConfigOffsetAccomType> allDefaultOffsetsFromDB = getAllOffsets(offsets);
        nonBaseRoomTypeOffsets.clear();
        nonBaseRoomTypeOffsets.addAll(allDefaultOffsetsFromDB.stream().filter(offsetFromDB -> !uiOffsets.contains(offsetFromDB)).collect(Collectors.toSet()));

        return defaultOffsetWrappers;
    }

    private Set<CPConfigOffsetAccomType> getAllOffsets(LinkedHashMap<PricingAccomClass, LinkedHashMap<AccomType, LinkedList<CPConfigOffsetAccomType>>> allOffsetsGroupedByRoomClass) {
        Set<CPConfigOffsetAccomType> allOffsets = new HashSet<>();
        for (LinkedHashMap<AccomType, LinkedList<CPConfigOffsetAccomType>> typeBasedList : allOffsetsGroupedByRoomClass.values()) {
            if (typeBasedList != null) {
                for (LinkedList<CPConfigOffsetAccomType> listOfTypeSpecificOffsets : typeBasedList.values()) {
                    if (listOfTypeSpecificOffsets != null) {
                        allOffsets.addAll(listOfTypeSpecificOffsets);
                    }
                }
            }
        }
        return allOffsets;
    }

    private Set<CPConfigOffsetAccomType> getDefaultOffsetsDisplayedOnScreen(List<PricingConfigurationOffsetWrapper> wrappers) {
        Set<CPConfigOffsetAccomType> uiOffsets = new HashSet<>();
        wrappers.forEach(wrapper -> {
            if (!wrapper.isRoomClass() && wrapper.getCpConfigOffsetAccomType().getId() != null) {
                uiOffsets.add(wrapper.getCpConfigOffsetAccomType());
            }
            uiOffsets.addAll(getDefaultOffsetsDisplayedOnScreen(wrapper.getChildren()));
        });
        return uiOffsets;
    }

    protected List<PricingConfigurationOffsetSeasonWrapper> getSeasons() {
        List<PricingConfigurationOffsetSeasonWrapper> tempSeasons = new ArrayList<>();

        Map<LocalDate, CPOffsetConfigPeriodDTO> seasonOffsets = offsetService.retrieveOffsetConfigDTO(pricingAccomClasses, baseOccupancyType, cpConfigOffsetAccomTypes, getRoomTypesMappedToProduct()).getSeasonOffsets();

        for (Map.Entry<LocalDate, CPOffsetConfigPeriodDTO> entry : seasonOffsets.entrySet()) {
            CPOffsetConfigPeriodDTO periodDTO = entry.getValue();
            PricingConfigurationOffsetSeasonWrapper seasonWrapper = new PricingConfigurationOffsetSeasonWrapper(periodDTO);

            LinkedHashMap<PricingAccomClass, LinkedHashMap<AccomType, LinkedList<CPConfigOffsetAccomType>>> offsets = periodDTO.getOffsets();
            filterOffsetsAgainstDiscontinuedAccomTypes(offsets);
            List<PricingConfigurationOffsetWrapper> wrappers = getTreeStructure(true, offsets);
            //since we blow away all seasons when saved, null out the id for the incoming existing seasons
            for (PricingConfigurationOffsetWrapper wrapper : wrappers) {
                nullOffsetIdsAndFindSeasonName(wrapper, seasonWrapper);
            }
            seasonWrapper.setOffsets(wrappers);
            seasonWrapper.retrieveNonBaseRoomTypeOffset(offsets);
            tempSeasons.add(seasonWrapper);
        }

        return tempSeasons;
    }

    private void nullOffsetIdsAndFindSeasonName(PricingConfigurationOffsetWrapper
                                                        wrapper, PricingConfigurationOffsetSeasonWrapper seasonWrapper) {
        if (wrapper.getCpConfigOffsetAccomType() != null && seasonWrapper.getName() == null) {
            seasonWrapper.setName(wrapper.getCpConfigOffsetAccomType().getName());
        }

        if (wrapper.hasChildren()) {
            for (PricingConfigurationOffsetWrapper pricingConfigurationOffsetWrapper : wrapper.getChildren()) {
                nullOffsetIdsAndFindSeasonName(pricingConfigurationOffsetWrapper, seasonWrapper);
            }
        }
    }

    List<PricingConfigurationOffsetWrapper> getTreeStructure(boolean overlayDataInDatabase,
                                                             LinkedHashMap<PricingAccomClass,
                                                                     LinkedHashMap<AccomType, LinkedList<CPConfigOffsetAccomType>>> offsets) {
        //Convert the values coming from the backend to our objects that the UI understands
        ArrayList<PricingConfigurationOffsetWrapper> pricingConfigurationOffsetWrappers = new ArrayList<>();

        //Per Person Pricing data
        OccupancyType currentAdultBucket = null;
        if (isPerPersonPricingEnabled) {
            currentAdultBucket = perPersonPricingService.getAdultBucketType(occupantBucketEntities);
        }

        List<AccomType> accomTypesList = new ArrayList<>();
        offsets.values().forEach(hashMap -> accomTypesList.addAll(hashMap.keySet()));
        List<Integer> accomTypeIds = accomTypesList.stream().map(AccomType::getId).distinct().collect(Collectors.toList());
        List<AccomTypeSupplement> accomTypeSupplements;
        if (isIndependentProductsEnabled && pricingConfigurationDTO.getIndependentProductDTO() != null && pricingConfigurationDTO.isIndependentProduct()) {
            accomTypeSupplements = accomTypeSupplementService.findSupplementsByAccomTypesAndProduct(accomTypeIds, pricingConfigurationDTO.getProduct());
        } else {
            accomTypeSupplements = accomTypeSupplementService.findSupplementsByAccomTypesForBAR(accomTypeIds);
        }

        fixedValueAccomTypeSupplements = accomTypeSupplements.stream().
                filter(this::isFixedValueSupplement)
                .collect(Collectors.toList());

        for (Map.Entry<PricingAccomClass, LinkedHashMap<AccomType, LinkedList<CPConfigOffsetAccomType>>> accomClassLinkedHashMapEntry : offsets.entrySet()) {
            //Room Class
            PricingAccomClass pricingAccomClass = accomClassLinkedHashMapEntry.getKey();

            //store the pricing accom classes as we need to pass them back to the service when saving
            if (pricingAccomClass.isPriceExcluded()) {
                excludedPricingAccomClasses.add(pricingAccomClass);
            }

            List<AccomTypeSupplement> roomClassSupplements = fixedValueAccomTypeSupplements
                    .stream()
                    .filter(accomTypeSupplement -> accomTypeSupplement.getStartDate() == null && accomTypeSupplement.getEndDate() == null)
                    .filter(accomTypeSupplement -> accomTypeSupplement.getAccomType().getAccomClass().equals(pricingAccomClass.getAccomClass()))
                    .collect(Collectors.toList());

            PricingConfigurationOffsetWrapper roomClassWrapper = new PricingConfigurationOffsetWrapper(pricingAccomClass, null, PricingConfigurationNodeLevel.ROOM_CLASS, roomClassSupplements);
            pricingConfigurationOffsetWrappers.add(roomClassWrapper);

            //Room Type
            LinkedHashMap<AccomType, LinkedList<CPConfigOffsetAccomType>> roomTypeMap = accomClassLinkedHashMapEntry.getValue();
            for (Map.Entry<AccomType, LinkedList<CPConfigOffsetAccomType>> accomTypeLinkedListEntry : roomTypeMap.entrySet()) {
                AccomType accomType = accomTypeLinkedListEntry.getKey();
                List<AccomTypeSupplement> roomTypeSupplements = fixedValueAccomTypeSupplements
                        .stream()
                        .filter(accomTypeSupplement -> accomTypeSupplement.getStartDate() == null && accomTypeSupplement.getEndDate() == null)
                        .filter(accomTypeSupplement -> accomTypeSupplement.getAccomType().equals(accomType))
                        .collect(Collectors.toList());
                LinkedList<CPConfigOffsetAccomType> offsetTypes = null;

                //if we need to included the existing offsets, load them up
                if (overlayDataInDatabase) {
                    offsetTypes = accomTypeLinkedListEntry.getValue();
                }

                PricingConfigurationOffsetWrapper roomTypeWrapper = new PricingConfigurationOffsetWrapper(pricingAccomClass, accomType, PricingConfigurationNodeLevel.ROOM_TYPE, roomTypeSupplements);

                //if isCPBaseRoomTypeOnlyEnabled = True; only add the Base Room Types
                //if isCPBaseRoomTypeOnlyEnabled = False; Add all
                if (addRoomType(pricingAccomClass, accomType)) {
                    if (isPerPersonPricingEnabled) {
                        roomClassWrapper.addChild(roomTypeWrapper);
                        getPerPersonPricingTreeStructure(accomType, pricingAccomClass, currentAdultBucket, offsetTypes, roomTypeWrapper, roomTypeSupplements);
                    } else if (isContinuousPricingEnabled) {
                        roomTypeWrapper.setCpConfigOffsetAccomType(getCorrespondingOffsetEntity(pricingAccomClass, accomType, offsetTypes, OccupancyType.SINGLE));
                        roomClassWrapper.addChild(roomTypeWrapper);

                        PricingConfigurationOffsetWrapper doubleOffset = new PricingConfigurationOffsetWrapper(pricingAccomClass, accomType, PricingConfigurationNodeLevel.OFFSET, roomTypeSupplements);
                        doubleOffset.setCpConfigOffsetAccomType(getCorrespondingOffsetEntity(pricingAccomClass, accomType, offsetTypes, OccupancyType.DOUBLE));
                        roomTypeWrapper.addChild(doubleOffset);

                        PricingConfigurationOffsetWrapper extraAdult = new PricingConfigurationOffsetWrapper(pricingAccomClass, accomType, PricingConfigurationNodeLevel.OFFSET, roomTypeSupplements);
                        extraAdult.setCpConfigOffsetAccomType(getCorrespondingOffsetEntity(pricingAccomClass, accomType, offsetTypes, OccupancyType.EXTRA_ADULT));
                        roomTypeWrapper.addChild(extraAdult);

                        // Add Child Age Buckets
                        if (isChildAgeBucketsEnabled) {
                            List<OccupantBucketEntity> childAgeBuckets = occupantBucketEntities
                                    .stream()
                                    .filter(bucket -> OccupancyType.isChildBucket(bucket.getOccupancyType()))
                                    .sorted(Comparator.comparing(OccupantBucketEntity::getMinAge))
                                    .collect(Collectors.toList());

                            for (OccupantBucketEntity entity : childAgeBuckets) {
                                PricingConfigurationOffsetWrapper childBucketOffset = new PricingConfigurationOffsetWrapper(pricingAccomClass, accomType, PricingConfigurationNodeLevel.OFFSET, roomTypeSupplements);
                                childBucketOffset.setCpConfigOffsetAccomType(getCorrespondingOffsetEntity(pricingAccomClass, accomType, offsetTypes, entity.getOccupancyType()));
                                roomTypeWrapper.addChild(childBucketOffset);
                            }
                        }

                        PricingConfigurationOffsetWrapper extraChild = new PricingConfigurationOffsetWrapper(pricingAccomClass, accomType, PricingConfigurationNodeLevel.OFFSET, roomTypeSupplements);
                        extraChild.setCpConfigOffsetAccomType(getCorrespondingOffsetEntity(pricingAccomClass, accomType, offsetTypes, OccupancyType.EXTRA_CHILD));
                        roomTypeWrapper.addChild(extraChild);
                    } else {
                        roomTypeWrapper.setCpConfigOffsetAccomType(getCorrespondingOffsetEntity(pricingAccomClass, accomType, offsetTypes, OccupancyType.SINGLE));
                        roomClassWrapper.addChild(roomTypeWrapper);
                    }
                }
            }
        }

        return pricingConfigurationOffsetWrappers;
    }

    private boolean isFixedValueSupplement(AccomTypeSupplement supplement) {
        return FIXED_OFFSET.equals(supplement.getOffsetMethod());
    }

    protected void getPerPersonPricingTreeStructure(AccomType accomType,
                                                    PricingAccomClass pricingAccomClass,
                                                    OccupancyType currentAdultBucket,
                                                    List<CPConfigOffsetAccomType> offsetTypes,
                                                    PricingConfigurationOffsetWrapper roomTypeWrapper,
                                                    List<AccomTypeSupplement> accomTypeSupplementList) {

        MaximumOccupantsEntity currentMaxOccupants = getMaximumOccupantsEntity(accomType);


        OccupancyType maxAdultOccupancyType;
        Map<Integer, OccupancyType> numAdultsToOccupancyTypeMap = new HashMap<>();
        numAdultsToOccupancyTypeMap.put(0, OccupancyType.SINGLE);
        numAdultsToOccupancyTypeMap.put(1, OccupancyType.SINGLE);
        numAdultsToOccupancyTypeMap.put(2, OccupancyType.DOUBLE);
        numAdultsToOccupancyTypeMap.put(3, OccupancyType.THREE_ADULTS);
        numAdultsToOccupancyTypeMap.put(4, OccupancyType.FOUR_ADULTS);
        numAdultsToOccupancyTypeMap.put(5, OccupancyType.FIVE_ADULTS);

        if (currentAdultBucket.getId() <= numAdultsToOccupancyTypeMap.get(currentMaxOccupants.getAdults() > 5 ? 5 : currentMaxOccupants.getAdults()).getId()) {
            maxAdultOccupancyType = currentAdultBucket;
        } else if (currentMaxOccupants.getAdults() >= MIN_ADULTS_DISPLAYED) {
            maxAdultOccupancyType = currentMaxOccupants.getAdults() > MAX_ADULTS_DISPLAYED ?
                    OccupancyType.FIVE_ADULTS : numAdultsToOccupancyTypeMap.get(currentMaxOccupants.getAdults());
        } else {
            maxAdultOccupancyType = OccupancyType.DOUBLE;
        }

        //add adult buckets
        for (int index = 1; index <= maxAdultOccupancyType.getId(); index++) {
            if (index == 1) {
                roomTypeWrapper.setCpConfigOffsetAccomType(getCorrespondingOffsetEntity(pricingAccomClass, accomType, offsetTypes, OccupancyType.SINGLE));
            } else if (index != OccupancyType.EXTRA_ADULT.getId() && index != OccupancyType.EXTRA_CHILD.getId()) {
                PricingConfigurationOffsetWrapper adultBucket = new PricingConfigurationOffsetWrapper(pricingAccomClass, accomType, PricingConfigurationNodeLevel.OFFSET, accomTypeSupplementList);
                adultBucket.setCpConfigOffsetAccomType(getCorrespondingOffsetEntity(pricingAccomClass, accomType, offsetTypes, OccupancyType.getById(index)));
                roomTypeWrapper.addChild(adultBucket);
            }
        }

        PricingConfigurationOffsetWrapper extraAdult = new PricingConfigurationOffsetWrapper(pricingAccomClass, accomType, PricingConfigurationNodeLevel.OFFSET, accomTypeSupplementList);
        extraAdult.setCpConfigOffsetAccomType(getCorrespondingOffsetEntity(pricingAccomClass, accomType, offsetTypes, OccupancyType.EXTRA_ADULT));
        roomTypeWrapper.addChild(extraAdult);


        Map<Integer, OccupancyType> numChildrenToOccupancyTypeMap = new HashMap<>();
        numChildrenToOccupancyTypeMap.put(1, OccupancyType.ONE_CHILD);
        numChildrenToOccupancyTypeMap.put(2, OccupancyType.TWO_CHILDREN);
        numChildrenToOccupancyTypeMap.put(3, OccupancyType.THREE_CHILDREN);
        numChildrenToOccupancyTypeMap.put(4, OccupancyType.FOUR_CHILDREN);
        numChildrenToOccupancyTypeMap.put(5, OccupancyType.FIVE_CHILDREN);


        //add child buckets
        for (OccupantBucketEntity entity : occupantBucketEntities) {
            if (OccupancyType.isChildBucket(entity.getOccupancyType())) {
                PricingConfigurationOffsetWrapper childBucketOffset = new PricingConfigurationOffsetWrapper(pricingAccomClass, accomType, PricingConfigurationNodeLevel.OFFSET, accomTypeSupplementList);
                childBucketOffset.setCpConfigOffsetAccomType(getCorrespondingOffsetEntity(pricingAccomClass, accomType, offsetTypes, entity.getOccupancyType()));
                roomTypeWrapper.addChild(childBucketOffset);
            } else if (OccupancyType.isChildQuantityBucket(entity.getOccupancyType())) {
                //ID for 1 Child is 10, so to go from ID to # of children it is - 9
                OccupancyType maxChildOccupancyType;
                if (currentMaxOccupants.getChildren() <= MIN_CHILDREN_DISPLAYED) {
                    break;
                }
                if (entity.getOccupancyType().getId() <= numChildrenToOccupancyTypeMap.get(currentMaxOccupants.getChildren() > 5 ? 5 : currentMaxOccupants.getChildren()).getId()) {
                    maxChildOccupancyType = entity.getOccupancyType();
                } else if (currentMaxOccupants.getChildren() > MIN_CHILDREN_DISPLAYED) {
                    maxChildOccupancyType = currentMaxOccupants.getChildren() >= MAX_CHILDREN_DISPLAYED ?
                            OccupancyType.FIVE_CHILDREN : numChildrenToOccupancyTypeMap.get(currentMaxOccupants.getChildren());
                } else {
                    break;
                }
                for (int index = OccupancyType.ONE_CHILD.getId(); index <= maxChildOccupancyType.getId(); index++) {
                    PricingConfigurationOffsetWrapper childBucketOffset = new PricingConfigurationOffsetWrapper(pricingAccomClass, accomType, PricingConfigurationNodeLevel.OFFSET, accomTypeSupplementList);
                    childBucketOffset.setCpConfigOffsetAccomType(getCorrespondingOffsetEntity(pricingAccomClass, accomType, offsetTypes, OccupancyType.getById(index)));
                    roomTypeWrapper.addChild(childBucketOffset);
                }
            }
        }

        PricingConfigurationOffsetWrapper extraChild = new PricingConfigurationOffsetWrapper(pricingAccomClass, accomType, PricingConfigurationNodeLevel.OFFSET, accomTypeSupplementList);
        extraChild.setCpConfigOffsetAccomType(getCorrespondingOffsetEntity(pricingAccomClass, accomType, offsetTypes, OccupancyType.EXTRA_CHILD));
        roomTypeWrapper.addChild(extraChild);
    }

    private MaximumOccupantsEntity getMaximumOccupantsEntity(AccomType accomType) {
        MaximumOccupantsEntity currentMaxOccupants = maximumOccupantsEntities.stream()
                .filter(maximumOccupantsEntity -> maximumOccupantsEntity.getAccomTypeId().equals(accomType.getId()))
                .findFirst()
                .orElse(null);

        if (currentMaxOccupants == null) {
            //set default to 5 adults so display will be third adult, fourth adult, fifth adult
            currentMaxOccupants = new MaximumOccupantsEntity();
            currentMaxOccupants.setAdults(MAX_ADULTS_DISPLAYED);
            currentMaxOccupants.setChildren(MAX_CHILDREN_DISPLAYED);
        }
        return currentMaxOccupants;
    }

    protected boolean addRoomType(PricingAccomClass pricingAccomClass, AccomType accomType) {
        return !isCPBaseRoomTypeOnlyEnabled() || pricingAccomClass.getAccomType().equals(accomType);
    }

    protected CPConfigOffsetAccomType getCorrespondingOffsetEntity(PricingAccomClass pricingAccomClass,
                                                                   AccomType accomType,
                                                                   List<CPConfigOffsetAccomType> offsetTypes,
                                                                   OccupancyType occupancyType) {
        //first search for the one that already exists in the database and if it is found, use it
        if (offsetTypes != null) {
            for (CPConfigOffsetAccomType offsetType : offsetTypes) {
                if (offsetType.getOccupancyType().equals(occupancyType)) {
                    return offsetType;
                }
            }
        }

        //no existing CPConfigOffsetAccomType exists in the database so create a new one with some defaults set
        CPConfigOffsetAccomType cpConfigOffsetAccomType = new CPConfigOffsetAccomType();
        cpConfigOffsetAccomType.setOccupancyType(occupancyType);
        cpConfigOffsetAccomType.setAccomType(accomType);
        cpConfigOffsetAccomType.setPropertyId(accomType.getPropertyId());
        cpConfigOffsetAccomType.setProductID(getPricingConfigurationDTOProductID());

        //if the price for the accom class is excluded, the offset method should be defaulted to fixed price/set
        if (pricingAccomClass.isPriceExcluded() && occupancyType.equals(baseOccupancyType)) {
            cpConfigOffsetAccomType.setOffsetMethod(OffsetMethod.FIXED_PRICE);
        } else {
            cpConfigOffsetAccomType.setOffsetMethod(FIXED_OFFSET);
        }

        return cpConfigOffsetAccomType;
    }

    public boolean isCPBaseRoomTypeOnlyEnabled() {
        return isCPBaseRoomTypeOnlyEnabled;
    }

    public void setCPBaseRoomTypeOnlyEnabled(boolean cpBaseRoomTypeOnlyEnabled) {
        isCPBaseRoomTypeOnlyEnabled = cpBaseRoomTypeOnlyEnabled;
    }

    public void cancel() {
        cpConfigOffsetAccomTypes = offsetService.retrieveOffsetConfigNoPriceExcluded(uiContext.getPropertyId(), true, getPricingConfigurationDTOProductID());
        init(false);
    }

    private Set<AccomType> findAccomTypesForCurrentProduct(Product product) {
        if (product.isSystemDefault()) {
            return offsetService.getAllAccomTypes().stream().filter(ac -> !ac.isDiscontinued() && ac.getAccomClass().getId() != 1).collect(Collectors.toSet());
        }

        List<ProductAccomType> accomTypeListByProduct = agileRatesConfigurationService.findProductRoomTypesByProduct(product);
        return accomTypeListByProduct.stream().map(ProductAccomType::getAccomType).filter(ac -> !ac.isDiscontinued()).collect(Collectors.toSet());
    }
    private void createAdultAndChildMap(Map<Integer, Set<OccupancyType>> accomTypeOccupancyTypeAdultsMap,
                                        Map<Integer, Set<OccupancyType>> accomTypeOccupancyTypeChildrenMap,
                                        Map<Integer, Set<OccupancyType>> accomTypeOccupancyTypeSeasonMap,
                                        Set<OccupancyType> occupancyTypeSet) {
        if (isPerPersonPricingEnabled) {
            populateAdultAndChildMapForPPP(accomTypeOccupancyTypeAdultsMap, accomTypeOccupancyTypeChildrenMap, occupancyTypeSet);
            accomTypeOccupancyTypeSeasonMap.putAll(accomTypeOccupancyTypeAdultsMap);

            for (Map.Entry<Integer, Set<OccupancyType>> entry : accomTypeOccupancyTypeChildrenMap.entrySet()) {
                if (accomTypeOccupancyTypeSeasonMap.containsKey(entry.getKey())) {
                    accomTypeOccupancyTypeSeasonMap.get(entry.getKey()).addAll(entry.getValue());
                }
            }
        } else if (isChildAgeBucketsEnabled) {

            occupancyTypeSet.clear();
            accomTypeOccupancyTypeAdultsMap.clear();
            occupancyTypeSet.add(OccupancyType.SINGLE);
            occupancyTypeSet.add(OccupancyType.DOUBLE);
            occupancyTypeSet.add(OccupancyType.EXTRA_ADULT);
            occupancyTypeSet.add(OccupancyType.EXTRA_CHILD);

            for (AccomType accomType : offsetService.getAllAccomTypes()) {
                accomTypeOccupancyTypeAdultsMap.put(accomType.getId(), occupancyTypeSet);
            }

            populateChildMapForChildAgeBucketsToggle(accomTypeOccupancyTypeChildrenMap);

            accomTypeOccupancyTypeSeasonMap.putAll(accomTypeOccupancyTypeAdultsMap);
            for (Map.Entry<Integer, Set<OccupancyType>> entry : accomTypeOccupancyTypeChildrenMap.entrySet()) {
                if (accomTypeOccupancyTypeSeasonMap.containsKey(entry.getKey())) {
                    accomTypeOccupancyTypeSeasonMap.get(entry.getKey()).addAll(entry.getValue());
                }
            }
        } else {
            Set<OccupancyType> occupancyTypesSeasons = new HashSet<>();
            List<AccomType> accomTypeList = offsetService.getAllAccomTypes();
            occupancyTypesSeasons.add(OccupancyType.SINGLE);
            occupancyTypesSeasons.add(OccupancyType.DOUBLE);
            occupancyTypesSeasons.add(OccupancyType.EXTRA_ADULT);
            occupancyTypesSeasons.add(OccupancyType.EXTRA_CHILD);

            for (AccomType accomType : accomTypeList) {
                accomTypeOccupancyTypeSeasonMap.put(accomType.getId(), occupancyTypesSeasons);
                accomTypeOccupancyTypeAdultsMap.put(accomType.getId(), occupancyTypesSeasons);
            }
        }
    }

    public void offsetExcelUpload(Workbook workbook) {

        LinkedHashMap<PricingAccomClass, LinkedHashMap<AccomType, LinkedList<CPConfigOffsetAccomType>>> excelDefaultOffsets =
                new LinkedHashMap<>();
        LinkedHashMap<String, LinkedHashMap<PricingAccomClass, LinkedHashMap<AccomType,
                LinkedList<CPConfigOffsetAccomType>>>> excelSeasonOffsets = new LinkedHashMap<>();
        Product product = pricingConfigurationDTO.getProduct();
        Map<Integer, Set<OccupancyType>> accomTypeOccupancyTypeAdultsMap = new LinkedHashMap<>();
        Map<Integer, Set<OccupancyType>> accomTypeOccupancyTypeChildrenMap = new LinkedHashMap<>();
        Set<OccupancyType> occupancyTypeSet = new HashSet<>();
        Set<AccomType> accomTypeSet = findAccomTypesForCurrentProduct(product);
        if (isCPBaseRoomTypeOnlyEnabled()) {
            accomTypeSet.clear();
            for (PricingAccomClass pricingAccomClass : pricingAccomClasses) {
                accomTypeSet.add(pricingAccomClass.getAccomType());
            }
        }
        Map<Integer, Set<OccupancyType>> accomTypeOccupancyTypeSeasonMap = new LinkedHashMap<>();

        createAdultAndChildMap(accomTypeOccupancyTypeAdultsMap, accomTypeOccupancyTypeChildrenMap, accomTypeOccupancyTypeSeasonMap,
                occupancyTypeSet);

        List<CPConfigOffsetAccomType> seasonOffsetsList = pricingConfigurationService.findAllOffsets().stream().filter(
                p -> p.isSeason() && Objects.equals(p.getProductID(), product.getId())).collect(Collectors.toList());

        List<AccomTypeSupplement> baseSupplements = fixedValueAccomTypeSupplements.stream().filter(p -> OccupancyType.SINGLE.
                equals(p.getOccupancyType()) && Objects.equals(p.getProductID(), product.getId())).collect(Collectors.toList());

        Map<String, LocalDate> nameStartDateMap = new LinkedHashMap<>();
        Map<String, LocalDate> nameEndDateMap = new LinkedHashMap<>();
        ExcelOffsetUploadValidator uploadValidator = new ExcelOffsetUploadValidator(excelDefaultOffsets, offsetService, product.getId(),
                isPerPersonPricingEnabled, occupantBucketEntities, accomTypeOccupancyTypeAdultsMap,
                accomTypeOccupancyTypeChildrenMap, isChildAgeBucketsEnabled, accomTypeSet, baseSupplements);

        boolean isSupplementPercentageEnabled = pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_PERCENTAGE_FOR_SUPPLEMENT);

        List<OffsetUploadValidationError> offsetDefaultUploadValidationErrorList =
                uploadValidator.offsetDefaultExcelUploadValidation(workbook, baseOccupancyType, baseRoomTypes, isSupplementPercentageEnabled);

        LocalDate localCaughtUpDate = dateService.getCaughtUpLocalDate();

        List<OffsetUploadValidationError> offsetSeasonUploadValidationErrorList =
                uploadValidator.offsetSeasonUploadValidation(workbook, baseOccupancyType, baseRoomTypes, excelSeasonOffsets,
                        nameStartDateMap, nameEndDateMap, accomTypeOccupancyTypeSeasonMap, localCaughtUpDate, seasonOffsetsList, isSupplementPercentageEnabled);

        List<PricingConfigurationOffsetWrapper> offsetWrappersDefault = getTreeStructure(true, excelDefaultOffsets);

        List<PricingConfigurationOffsetSeasonWrapper> seasonsList = new ArrayList<>();

        for (Map.Entry<String, LinkedHashMap<PricingAccomClass, LinkedHashMap<AccomType,
                LinkedList<CPConfigOffsetAccomType>>>> entry : excelSeasonOffsets.entrySet()) {

            LinkedHashMap<PricingAccomClass, LinkedHashMap<AccomType, LinkedList<CPConfigOffsetAccomType>>>
                    innerSeasonsMap = entry.getValue();
            LocalDate startDate = nameStartDateMap.get(entry.getKey());
            LocalDate endDate = nameEndDateMap.get(entry.getKey());
            List<PricingConfigurationOffsetWrapper> seasonOffsetWrappers = getTreeStructure(true, innerSeasonsMap);
            PricingConfigurationOffsetSeasonWrapper seasonWrapper = new PricingConfigurationOffsetSeasonWrapper();
            seasonWrapper.setOffsets(seasonOffsetWrappers);
            seasonWrapper.retrieveNonBaseRoomTypeOffset(innerSeasonsMap);
            seasonWrapper.setName(entry.getKey());
            seasonWrapper.setStartDate(startDate);
            seasonWrapper.setEndDate(endDate);
            seasonsList.add(seasonWrapper);
        }

        checkOverlappingSeasons(seasonsList, offsetSeasonUploadValidationErrorList);

        if (!offsetDefaultUploadValidationErrorList.isEmpty()) {
            view.showOffsetExcelUploadErrors(offsetDefaultUploadValidationErrorList);
            return;
        } else if (!offsetSeasonUploadValidationErrorList.isEmpty()) {
            view.showOffsetExcelUploadErrors(offsetSeasonUploadValidationErrorList);
            return;
        }

        if (isSupplementPercentageEnabled) {
            List<OffsetUploadValidationError> errors = uploadValidator.checkFloorValueGreaterThanSupplement(isPerPersonPricingEnabled, isFunctionSpace());
            if (!errors.isEmpty()) {
                view.showOffsetExcelUploadErrors(errors);
                return;
            }
        }

        Integer productId = product.getId();

        List<CPConfigOffsetAccomType> defaultOffsetsToSave = findOffsetsToSave(offsetWrappersDefault).stream().filter(offsetAccomType -> !isBaseOccupancyType(offsetAccomType)).collect(Collectors.toList());
        List<OffsetUploadValidationError> offsetUploadValidationErrors = validateDefaultOffsets(defaultOffsetsToSave, false, offsetDefaultUploadValidationErrorList);

        if (!offsetUploadValidationErrors.isEmpty()) {
            view.showOffsetExcelUploadErrors(offsetDefaultUploadValidationErrorList);
            return;
        }

        List<List<CPConfigOffsetAccomType>> seasonsSaveList = new ArrayList<>();

        List<PricingBaseAccomType> ceilingAndFloorConfig = getPricingBaseAccomTypes();
        List<PricingBaseAccomType> ceilingAndFloorConfigSeasons = ceilingAndFloorConfig.stream().filter(pricingBaseAccomType -> pricingBaseAccomType.getStartDate() != null && pricingBaseAccomType.getEndDate() != null).collect(Collectors.toList());

        if (accomClassPriceRank == null) {
            accomClassPriceRank = accomClassPriceRankService.getAccomClassPriceRank();
        }

        Tax defaultTax = taxService.findTax();
        List<CPConfigOffsetAccomType> existingOffsets = offsetService.retrieveOffsetConfigNoPriceExcluded(uiContext.getPropertyId(), true, getPricingConfigurationDTOProductID());

        for (PricingConfigurationOffsetSeasonWrapper season : seasonsList) {

            List<CPConfigOffsetAccomType> seasonOffsetsToSave = saveOffsetExcelSeasons(season, true,
                    offsetSeasonUploadValidationErrorList, season.getName(), season.getStartDate(), season.getEndDate(),
                    ceilingAndFloorConfigSeasons, defaultTax, existingOffsets);

            if (CollectionUtils.isNotEmpty(seasonOffsetsToSave)) {
                seasonsSaveList.add(seasonOffsetsToSave);
            }
        }

        if (!offsetSeasonUploadValidationErrorList.isEmpty()) {
            view.showOffsetExcelUploadErrors(offsetSeasonUploadValidationErrorList);
            return;
        }
        triggerUploadJob(productId, defaultOffsetsToSave, seasonsSaveList);
    }

    private List<PricingBaseAccomType> getPricingBaseAccomTypes() {
        return isIndependentProductsEnabled ? pricingConfigurationValidationService.getAllPricingBaseAccomTypesByProductId(getPricingConfigurationDTOProductID())
                : pricingConfigurationValidationService.getAllPricingBaseAccomTypes();
    }

    private void triggerUploadJob(Integer productId, List<CPConfigOffsetAccomType> defaultOffsets,
                                  List<List<CPConfigOffsetAccomType>> seasonOffsets) {
        OffsetUploadDto offsetUploadDto = new OffsetUploadDto();
        offsetUploadDto.setProductId(productId);
        offsetUploadDto.setNonBaseRoomTypeOffsets(nonBaseRoomTypeOffsets);
        offsetUploadDto.setSeasonOffsets(seasonOffsets);
        offsetUploadDto.setDefaultOffsets(defaultOffsets);
        offsetUploadDto.setIndependentProductsEnabled(isIndependentProductsEnabled);
        offsetUploadDto.setPricingConfigurationDTO(pricingConfigurationDTO);
        offsetUploadDto.setRoomTypeRecodingUIEnabled(isRoomTypeRecodingUIEnabled);
        offsetUploadDto.setExcludedPricingAccomClasses(excludedPricingAccomClasses);
        offsetUploadDto.setSystemDateAsLocalDate(systemDateAsLocalDate);
        offsetUploadDto.setCPBaseRoomTypeOnlyEnabled(isCPBaseRoomTypeOnlyEnabled);

        try {
            pricingConfigurationUploadService.triggerOffsetsUploadJob(offsetUploadDto);
            init(true);
            view.showSaveSuccessMessage();
        } catch (TetrisException e) {
            logger.error("OffsetsUploadJob failed/not completed within time", e);
            view.showError(getText("upload.failed"));
        }
    }

    public List<CPConfigOffsetAccomType> saveOffsetExcelSeasons(PricingConfigurationOffsetSeasonWrapper season, boolean ignoreRCRankValidation,
                                                                List<OffsetUploadValidationError> offsetSeasonUploadValidationErrorList, String seasonName,
                                                                LocalDate startDate, LocalDate endDate,
                                                                List<PricingBaseAccomType> ceilingAndFloorConfigSeasons,
                                                                Tax defaultTax, List<CPConfigOffsetAccomType> existingOffsets) {

        List<CPConfigOffsetAccomType> basePricingAccomTypesToUpdate = getSeasonOffsetsList(List.of(season));

        if (CollectionUtils.isEmpty(basePricingAccomTypesToUpdate)) {
            offsetSeasonUploadValidationErrorList.add(new OffsetUploadValidationError(
                    getText("offset.upload.incomplete.configuration"),
                    null, null).withSeason(seasonName));
            return basePricingAccomTypesToUpdate;
        }

        validateSeasonsOffsets(basePricingAccomTypesToUpdate, ignoreRCRankValidation, offsetSeasonUploadValidationErrorList,
                seasonName, startDate, endDate, ceilingAndFloorConfigSeasons, defaultTax, existingOffsets);

        return basePricingAccomTypesToUpdate;
    }

    public List<OffsetUploadValidationError> validateDefaultOffsets(List<CPConfigOffsetAccomType> offsetsToSave,
                                                                    boolean ignoreRCRankValidation, List<OffsetUploadValidationError> offsetUploadValidationErrorList) {

        offsetService.updateForTax(offsetsToSave, isSupplementsEnabled());

        String warningMessage = null;
        String transientOffsetWarningMessage = null;
        String negativePercentageOffsetWarningMessage = null;
        String pppOneAdultWarningMessage = null;
        String pppAdultHierarchyWarningMessage = null;
        String pppChildHierarchyWarningMessage = null;
        String pppChildAgeHierarchyWarningMessage = null;
        String pppChildNegativeWarningMessage = null;

        if (isContinuousPricingEnabled) {
            List<PricingBaseAccomType> ceilingAndFloorConfig;
            if (isIndependentProductsEnabled) {
                ceilingAndFloorConfig = pricingConfigurationValidationService.getAllPricingBaseAccomTypesByProductId(getPricingConfigurationDTOProductID());
            } else {
                ceilingAndFloorConfig = pricingConfigurationValidationService.getAllPricingBaseAccomTypes();
            }
            List<PricingBaseAccomType> ceilingAndFloorConfigDefaults = ceilingAndFloorConfig.stream().filter(pricingBaseAccomType -> pricingBaseAccomType.getStartDate() == null && pricingBaseAccomType.getEndDate() == null).collect(Collectors.toList());
            if (!isAdvancedPriceRankingEnabled) {
                if (accomClassPriceRank == null) {
                    accomClassPriceRank = accomClassPriceRankService.getAccomClassPriceRank();
                }
                Set<String> roomClassesInViolationPricingRuleType = null;

                Integer dailyBarPricingRuleType = getDailyBarPricingRuleType();
                if (dailyBarPricingRuleType.equals(2) || dailyBarPricingRuleType.equals(3)) {

                    roomClassesInViolationPricingRuleType = pricingConfigurationValidationService.validateSaveDefaultOffsetsByPricingRuleType(offsetsToSave, accomClassPriceRank, pricingAccomClasses, ceilingAndFloorConfig, baseOccupancyType);

                }
                if (CollectionUtils.isNotEmpty(roomClassesInViolationPricingRuleType)) {
                    warningMessage = String.join("\n", roomClassesInViolationPricingRuleType);
                    Pattern pattern = Pattern.compile("([A-Z\\s-]+)(<BR/>)?");
                    Matcher matcher = pattern.matcher(warningMessage);
                    String roomClasses = matcher.results()
                            .map(m -> m.group(1))
                            .filter(rc -> !rc.isEmpty())
                            .map(String::trim)
                            .filter(rc -> !rc.matches("^(T|P|D|M|S)$"))
                            .reduce("", (rc1, rc2) -> rc1 + "  " + rc2);

                    offsetUploadValidationErrorList.add(new OffsetUploadValidationError("Room class ranking violation " +
                            roomClasses + " Review your Offsets, Minimum Price Differential, and Floors and Ceilings.", null, null));
                }
            }

            Set<String> roomClassesWithTransientOffsetViolation = pricingConfigurationValidationService.validateSaveOffsetLowerThanTransientFloor(offsetsToSave, ceilingAndFloorConfigDefaults);
            if (CollectionUtils.isNotEmpty(roomClassesWithTransientOffsetViolation)) {
                transientOffsetWarningMessage = String.join("\n", roomClassesWithTransientOffsetViolation);
                offsetUploadValidationErrorList.add(new OffsetUploadValidationError("The Offsets in " + transientOffsetWarningMessage + " cause the value to be less than floor of the Room Class", null, null));
            }

            Set<String> roomClassesWithNegativeOffsetViolation = offsetNegativePercentageValidation(offsetsToSave);
            if (CollectionUtils.isNotEmpty(roomClassesWithNegativeOffsetViolation)) {
                negativePercentageOffsetWarningMessage = String.join("\n", roomClassesWithNegativeOffsetViolation);
                offsetUploadValidationErrorList.add(new OffsetUploadValidationError("Offsets in " + negativePercentageOffsetWarningMessage + " cause the value to be less than floor of Room Class", null, null));
            }

            if (isPerPersonPricingEnabled) {
                List<CPConfigOffsetAccomType> newOffsetsToSave = findNewOffsetsToSave(offsetsToSave);

                Set<String> roomClassesWithOneAdultPositiveViolation = validateOneAdultOffsets(newOffsetsToSave);
                if (CollectionUtils.isNotEmpty(roomClassesWithOneAdultPositiveViolation)) {
                    pppOneAdultWarningMessage = String.join(", ", roomClassesWithOneAdultPositiveViolation);
                    offsetUploadValidationErrorList.add(new OffsetUploadValidationError("The one Adult occupancy in " + pppOneAdultWarningMessage + " is set higher than higher occupancies", null, null));
                }

                Set<String> roomClassesWithAdultHierarchyViolation = validateAdultHierarchyOffsets(offsetsToSave);
                if (CollectionUtils.isNotEmpty(roomClassesWithAdultHierarchyViolation)) {
                    pppAdultHierarchyWarningMessage = String.join(", ", roomClassesWithAdultHierarchyViolation);
                    offsetUploadValidationErrorList.add(new OffsetUploadValidationError("The lower Adult occupancies in " + pppAdultHierarchyWarningMessage + " are set higher than higher occupancies", null, null));
                }

                Set<String> roomClassesWithChildHierarchyViolation = validateChildHierarchyOffsets(offsetsToSave);
                if (CollectionUtils.isNotEmpty(roomClassesWithChildHierarchyViolation)) {
                    pppChildHierarchyWarningMessage = String.join(", ", roomClassesWithChildHierarchyViolation);
                    offsetUploadValidationErrorList.add(new OffsetUploadValidationError("The lower child occupancies in " + pppChildHierarchyWarningMessage + " are set higher than higher occupancies", null, null));
                }

                Set<String> roomClassesWithChildAgeHierarchyViolation = validateChildAgeHierarchyOffsets(offsetsToSave);
                if (CollectionUtils.isNotEmpty(roomClassesWithChildAgeHierarchyViolation)) {
                    pppChildAgeHierarchyWarningMessage = String.join(", ", roomClassesWithChildAgeHierarchyViolation);
                    offsetUploadValidationErrorList.add(new OffsetUploadValidationError("The lower child Age bucket occupancies in " + pppChildAgeHierarchyWarningMessage + " are set higher than higher occupancies", null, null));
                }

                Set<String> roomClassesWithChildNegativeViolation = validateChildNegativeOffsets(offsetsToSave);
                if (CollectionUtils.isNotEmpty(roomClassesWithChildNegativeViolation)) {
                    pppChildNegativeWarningMessage = String.join(", ", roomClassesWithChildNegativeViolation);
                    offsetUploadValidationErrorList.add(new OffsetUploadValidationError("Child Offset cannot be negative in " + pppChildNegativeWarningMessage, null, null));
                }
            } else if (isChildAgeBucketsEnabled) {
                Set<String> roomClassesWithChildAgeHierarchyViolation = validateChildAgeHierarchyOffsets(offsetsToSave);
                if (CollectionUtils.isNotEmpty(roomClassesWithChildAgeHierarchyViolation)) {
                    pppChildAgeHierarchyWarningMessage = String.join(", ", roomClassesWithChildAgeHierarchyViolation);
                    offsetUploadValidationErrorList.add(new OffsetUploadValidationError("The lower child Age bucket occupancies in " + pppChildAgeHierarchyWarningMessage + " are set higher than higher occupancies", null, null));
                }
            }
        }
        return offsetUploadValidationErrorList;
    }

    public List<OffsetUploadValidationError> validateSeasonsOffsets(List<CPConfigOffsetAccomType> offsetsToSave,
                                                                    boolean ignoreRCRankValidation,
                                                                    List<OffsetUploadValidationError> offsetUploadValidationErrorList,
                                                                    String seasonName, LocalDate startDate, LocalDate endDate,
                                                                    List<PricingBaseAccomType> ceilingAndFloorConfigSeasons,
                                                                    Tax defaultTax, List<CPConfigOffsetAccomType> existingOffsets) {
        offsetService.updateForTax(offsetsToSave, isSupplementsEnabled(), defaultTax);

        String warningMessage = null;
        String transientOffsetWarningMessage = null;
        String negativePercentageOffsetWarningMessage = null;
        String pppOneAdultWarningMessage = null;
        String pppAdultHierarchyWarningMessage = null;
        String pppChildHierarchyWarningMessage = null;
        String pppChildAgeHierarchyWarningMessage = null;
        String pppChildNegativeWarningMessage = null;

        if (isContinuousPricingEnabled) {
            if (!isAdvancedPriceRankingEnabled) {

                Set<String> roomClassesInViolationPricingRuleType = null;
                Integer dailyBarPricingRuleType = getDailyBarPricingRuleType();
                if (dailyBarPricingRuleType.equals(2) || dailyBarPricingRuleType.equals(3)) {

                    roomClassesInViolationPricingRuleType = pricingConfigurationValidationService.validateSaveSeasonOffsetsByDailyPricingBarRuleType(offsetsToSave, startDate, endDate, accomClassPriceRank, pricingAccomClasses, baseOccupancyType);

                }

                if (CollectionUtils.isNotEmpty(roomClassesInViolationPricingRuleType)) {
                    warningMessage = String.join("\n", roomClassesInViolationPricingRuleType);
                    Matcher matcher = pattern.matcher(warningMessage);
                    String roomClasses = matcher.results()
                            .map(m -> m.group(1))
                            .filter(rc -> !rc.isEmpty())
                            .map(String::trim)
                            .filter(rc -> !rc.matches("^(T|P|D|M|S)$"))
                            .reduce("", (rc1, rc2) -> rc1 + "  " + rc2);

                    offsetUploadValidationErrorList.add(new OffsetUploadValidationError("Room class ranking violation " +
                            roomClasses + " Review your Offsets, Minimum Price Differential, and Floors and Ceilings.", null, null).withSeason(seasonName));
                }
            }

            Set<String> roomClassesWithTransientOffsetViolation = pricingConfigurationValidationService.validateSaveOffsetLowerThanTransientSeasonFloor(offsetsToSave, ceilingAndFloorConfigSeasons, caughtUpLocalDate);
            if (CollectionUtils.isNotEmpty(roomClassesWithTransientOffsetViolation)) {
                transientOffsetWarningMessage = String.join("\n", roomClassesWithTransientOffsetViolation);
                offsetUploadValidationErrorList.add(new OffsetUploadValidationError("The Offsets in " + transientOffsetWarningMessage + " cause the value to be less than floor of the Room Class", null, null).withSeason(seasonName));
            }

            Set<String> roomClassesWithNegativeOffsetViolation = offsetNegativePercentageValidation(offsetsToSave);
            if (CollectionUtils.isNotEmpty(roomClassesWithNegativeOffsetViolation)) {
                negativePercentageOffsetWarningMessage = String.join("\n", roomClassesWithNegativeOffsetViolation);
                offsetUploadValidationErrorList.add(new OffsetUploadValidationError("Offsets in " + negativePercentageOffsetWarningMessage + " cause the value to be less than floor of Room Class", null, null).withSeason(seasonName));
            }

            if (isPerPersonPricingEnabled) {
                List<CPConfigOffsetAccomType> newOffsetsToSave = findNewOffsetsToSave(offsetsToSave, existingOffsets);

                Set<String> roomClassesWithOneAdultPositiveViolation = validateOneAdultOffsets(newOffsetsToSave);
                if (CollectionUtils.isNotEmpty(roomClassesWithOneAdultPositiveViolation)) {
                    pppOneAdultWarningMessage = String.join(", ", roomClassesWithOneAdultPositiveViolation);
                    offsetUploadValidationErrorList.add(new OffsetUploadValidationError("The one Adult occupancy in " + pppOneAdultWarningMessage + " is set higher than higher occupancies", null, null).withSeason(seasonName));
                }

                Set<String> roomClassesWithAdultHierarchyViolation = validateAdultHierarchyOffsets(offsetsToSave);
                if (CollectionUtils.isNotEmpty(roomClassesWithAdultHierarchyViolation)) {
                    pppAdultHierarchyWarningMessage = String.join(", ", roomClassesWithAdultHierarchyViolation);
                    offsetUploadValidationErrorList.add(new OffsetUploadValidationError("The lower Adult occupancies in " + pppAdultHierarchyWarningMessage + " are set higher than higher occupancies", null, null).withSeason(seasonName));
                }

                Set<String> roomClassesWithChildHierarchyViolation = validateChildHierarchyOffsets(offsetsToSave);
                if (CollectionUtils.isNotEmpty(roomClassesWithChildHierarchyViolation)) {
                    pppChildHierarchyWarningMessage = String.join(", ", roomClassesWithChildHierarchyViolation);
                    offsetUploadValidationErrorList.add(new OffsetUploadValidationError("The lower child occupancies in " + pppChildHierarchyWarningMessage + " are set higher than higher occupancies", null, null).withSeason(seasonName));
                }

                Set<String> roomClassesWithChildAgeHierarchyViolation = validateChildAgeHierarchyOffsets(offsetsToSave);
                if (CollectionUtils.isNotEmpty(roomClassesWithChildAgeHierarchyViolation)) {
                    pppChildAgeHierarchyWarningMessage = String.join(", ", roomClassesWithChildAgeHierarchyViolation);
                    offsetUploadValidationErrorList.add(new OffsetUploadValidationError("The lower child Age bucket occupancies in " + pppChildAgeHierarchyWarningMessage + " are set higher than higher occupancies", null, null).withSeason(seasonName));
                }

                Set<String> roomClassesWithChildNegativeViolation = validateChildNegativeOffsets(offsetsToSave);
                if (CollectionUtils.isNotEmpty(roomClassesWithChildNegativeViolation)) {
                    pppChildNegativeWarningMessage = String.join(", ", roomClassesWithChildNegativeViolation);
                    offsetUploadValidationErrorList.add(new OffsetUploadValidationError("Child Offset cannot be negative in " + pppChildNegativeWarningMessage, null, null).withSeason(seasonName));
                }
            } else if (isChildAgeBucketsEnabled) {
                Set<String> roomClassesWithChildAgeHierarchyViolation = validateChildAgeHierarchyOffsets(offsetsToSave);
                if (CollectionUtils.isNotEmpty(roomClassesWithChildAgeHierarchyViolation)) {
                    pppChildAgeHierarchyWarningMessage = String.join(", ", roomClassesWithChildAgeHierarchyViolation);
                    offsetUploadValidationErrorList.add(new OffsetUploadValidationError("The lower child Age bucket occupancies in " + pppChildAgeHierarchyWarningMessage + " are set higher than higher occupancies", null, null).withSeason(seasonName));
                }
            }
        }

        return offsetUploadValidationErrorList;
    }

    private void checkOverlappingSeasons(List<PricingConfigurationOffsetSeasonWrapper> seasons,
                                         List<OffsetUploadValidationError> errors) {
        int size = isEmpty(seasons) ? 0 : seasons.size();
        for (int i = 0; i < size; i++) {
            for (int j = i + 1; j < size; j++) {
                if (seasons.get(i).isOverlappingWith(seasons.get(j))) {
                    errors.add(new OffsetUploadValidationError(getText("offset.upload.overlapping.season"),
                            null, null).withSeason(seasons.get(i).getName()));
                }
            }
        }
    }

    private void populateAdultAndChildMapForPPP(Map<Integer, Set<OccupancyType>> accomTypeOccupancyTypeAdultsMap, Map<Integer, Set<OccupancyType>> accomTypeOccupancyTypeChildrenMap, Set<OccupancyType> occupancyTypeSet) {

        occupancyTypeSet.add(OccupancyType.SINGLE);
        occupancyTypeSet.add(OccupancyType.DOUBLE);
        occupancyTypeSet.add(OccupancyType.THREE_ADULTS);
        occupancyTypeSet.add(OccupancyType.FOUR_ADULTS);
        occupancyTypeSet.add(OccupancyType.FIVE_ADULTS);
        occupancyTypeSet.add(OccupancyType.ONE_CHILD);
        occupancyTypeSet.add(OccupancyType.TWO_CHILDREN);
        occupancyTypeSet.add(OccupancyType.THREE_CHILDREN);
        occupancyTypeSet.add(OccupancyType.FOUR_CHILDREN);
        occupancyTypeSet.add(OccupancyType.FIVE_CHILDREN);
        occupancyTypeSet.add(OccupancyType.EXTRA_ADULT);
        occupancyTypeSet.add(OccupancyType.EXTRA_CHILD);
        occupancyTypeSet.add(OccupancyType.CHILD_BUCKET_1);
        occupancyTypeSet.add(OccupancyType.CHILD_BUCKET_2);
        occupancyTypeSet.add(OccupancyType.CHILD_BUCKET_3);

        Set<OccupancyType> allowedAdultSet;
        Set<OccupancyType> allowedChildrenSet;

        Integer allowedOccupantQuantityAdult;
        Integer allowedOccupantQuantityChildren;
        Integer allowedOccupantIdChildren;

        if (!occupantBucketEntities.isEmpty()) {
            List<OccupantBucketEntity> allowedOccupancyAdult = occupantBucketEntities.stream()
                    .filter(entities -> entities.getOccupancyType().getId() <= OccupancyType.FIVE_ADULTS.getId())
                    .collect(Collectors.toList());

            List<OccupantBucketEntity> allowedOccupancyChildren = occupantBucketEntities.stream()
                    .filter(entities -> entities.getOccupancyType().getId() >= OccupancyType.ONE_CHILD.getId()
                            && entities.getOccupancyType().getId() <= OccupancyType.CHILD_BUCKET_3.getId())
                    .collect(Collectors.toList());

            if (!allowedOccupancyAdult.isEmpty()) {
                allowedOccupantQuantityAdult = allowedOccupancyAdult.get(0).getOccupancyType().getOccupantQuantity();

            } else {
                allowedOccupantQuantityAdult = OccupancyType.DOUBLE.getOccupantQuantity();
            }

            if (!allowedOccupancyChildren.isEmpty()) {
                allowedOccupantQuantityChildren = allowedOccupancyChildren.get(0).getOccupancyType().getOccupantQuantity();
                allowedOccupantIdChildren = allowedOccupancyChildren.get(allowedOccupancyChildren.size() - 1).getOccupancyType().getId();

            } else {
                allowedOccupantQuantityChildren = 0;
                allowedOccupantIdChildren = 0;
            }

        } else {
            allowedOccupantQuantityAdult = OccupancyType.DOUBLE.getOccupantQuantity();
            allowedOccupantQuantityChildren = 0;
            allowedOccupantIdChildren = 0;
        }

        if (!maximumOccupantsEntities.isEmpty()) {
            for (MaximumOccupantsEntity maximumOccupants : maximumOccupantsEntities) {
                accomTypeOccupancyTypeAdultsMap.put(maximumOccupants.getAccomTypeId(), new HashSet<>());
                accomTypeOccupancyTypeChildrenMap.put(maximumOccupants.getAccomTypeId(), new HashSet<>());

                allowedAdultSet = occupancyTypeSet.stream().filter(occupancyType -> occupancyType.getId() <= OccupancyType.FIVE_ADULTS.getId()
                                && occupancyType.getOccupantQuantity() <= allowedOccupantQuantityAdult && occupancyType.getOccupantQuantity()
                                <= maximumOccupants.getAdults())
                        .collect(Collectors.toSet());

                allowedChildrenSet = occupancyTypeSet.stream().filter(occupancyType -> occupancyType.getId() > OccupancyType.FIVE_ADULTS.getId()
                                && occupancyType.getId() <= OccupancyType.CHILD_BUCKET_3.getId() && occupancyType.getOccupantQuantity()
                                <= allowedOccupantQuantityChildren && occupancyType.getId() <= allowedOccupantIdChildren
                                && occupancyType.getOccupantQuantity() <= maximumOccupants.getChildren())
                        .collect(Collectors.toSet());

                accomTypeOccupancyTypeAdultsMap.get(maximumOccupants.getAccomTypeId()).addAll(allowedAdultSet);
                accomTypeOccupancyTypeChildrenMap.get(maximumOccupants.getAccomTypeId()).addAll(allowedChildrenSet);
            }
        } else {
            Set<OccupancyType> allowedAdults = occupancyTypeSet.stream().filter(occupancyType -> occupancyType.getId()
                            <= OccupancyType.FIVE_ADULTS.getId() && occupancyType.getOccupantQuantity() <= allowedOccupantQuantityAdult)
                    .collect(Collectors.toSet());

            Set<OccupancyType> allowedChildren = occupancyTypeSet.stream()
                    .filter(occupancyType -> occupancyType.getId() > OccupancyType.FIVE_ADULTS.getId() && occupancyType.getId()
                            <= OccupancyType.CHILD_BUCKET_3.getId() && occupancyType.getOccupantQuantity() <= allowedOccupantQuantityChildren
                            && occupancyType.getId() <= allowedOccupantIdChildren)
                    .collect(Collectors.toSet());

            for (AccomType accomType : findAccomTypesForCurrentProduct(pricingConfigurationDTO.getProduct())) {
                accomTypeOccupancyTypeAdultsMap.put(accomType.getId(), allowedAdults);
                accomTypeOccupancyTypeChildrenMap.put(accomType.getId(), allowedChildren);
            }
        }
    }

    private void populateChildMapForChildAgeBucketsToggle(Map<Integer, Set<OccupancyType>> accomTypeOccupancyTypeChildrenMap) {

        Set<OccupancyType> occupancyTypeSet = new HashSet<>();
        occupancyTypeSet.add(OccupancyType.CHILD_BUCKET_1);
        occupancyTypeSet.add(OccupancyType.CHILD_BUCKET_2);
        occupancyTypeSet.add(OccupancyType.CHILD_BUCKET_3);

        Set<OccupancyType> allowedChildrenSet = new HashSet<>();

        if (!occupantBucketEntities.isEmpty()) {

            List<OccupantBucketEntity> allowedOccupancyChildren = occupantBucketEntities.stream().filter
                    (entities -> entities.getOccupancyType().getId() >= OccupancyType.FIVE_CHILDREN.getId()
                            && entities.getOccupancyType().getId()
                            <= OccupancyType.CHILD_BUCKET_3.getId()).collect(Collectors.toList());

            for (OccupantBucketEntity occupantBucketEntity : allowedOccupancyChildren) {
                allowedChildrenSet.add(occupantBucketEntity.getOccupancyType());
            }
        }

        for (AccomType accomType : offsetService.getAllAccomTypes()) {
            accomTypeOccupancyTypeChildrenMap.put(accomType.getId(), allowedChildrenSet);
        }
    }

    public void save(List<PricingConfigurationOffsetWrapper> defaultOffsets) {

        List<CPConfigOffsetAccomType> offsetsToSave = findOffsetsToSave(defaultOffsets).stream().filter(offsetAccomType -> !isBaseOccupancyType(offsetAccomType)).collect(Collectors.toList());
        //loop through and pull out all the entities and send them to the service to be saved
        //default offsets

        pricingConfigurationLTBDEService.enabledLTBDEIfApplicableForOffsetChange(offsetsToSave, dateService.getCaughtUpJavaLocalDate());

        validateAndManageOffsets(offsetsToSave, Collections.emptyList(), nonBaseRoomTypeOffsets, Pair.of(null, null), false, false);
    }

    private List<CPConfigOffsetAccomType> findOffsetsWhichAreEditing(List<CPConfigOffsetAccomType> offsetsToSave,
                                                                     Pair<LocalDate, LocalDate> editingSeason) {
        return filterOffsets(offsetsToSave, offset -> Objects.equals(offset.getOccupancyType(), baseOccupancyType) &&
                Objects.equals(offset.getStartDate(), editingSeason.getLeft())
                && Objects.equals(offset.getEndDate(), editingSeason.getRight()));
    }

    private List<CPConfigOffsetAccomType> findOffsetsWhichAreNotEditing(List<CPConfigOffsetAccomType> offsetsToSave,
                                                                        List<CPConfigOffsetAccomType> offsetsToDelete,
                                                                        List<CPConfigOffsetAccomType> offsetsWhichAreEditing,
                                                                        Pair<LocalDate, LocalDate> editingSeason) {
        Integer productId = getPricingConfigurationDTOProductID();
        List<CPConfigOffsetAccomType> productOffsets = filterOffsets(getAllOffsets(), offset -> Objects.equals(offset.getOccupancyType(), baseOccupancyType) &&
                Objects.equals(offset.getProductID(), productId) && !offsetsWhichAreEditing.contains(offset));
        productOffsets.removeAll(offsetsToDelete);
        productOffsets.addAll(filterOffsets(offsetsToSave, offset -> Objects.equals(offset.getOccupancyType(), baseOccupancyType) &&
                !Objects.equals(offset.getStartDate(), editingSeason.getLeft())
                && !Objects.equals(offset.getEndDate(), editingSeason.getRight())));
        return productOffsets;
    }

    private List<ProductHierarchy> getInvalidHierarchiesWhileEditingOffsets(List<CPConfigOffsetAccomType> offsetsToSave,
                                                                            List<CPConfigOffsetAccomType> offsetsToDelete,
                                                                            Pair<LocalDate, LocalDate> editingSeason) {
        if (!SystemConfig.isSeasonHierarchyValidationEnabled()) {
            return Collections.emptyList();
        }
        List<CPConfigOffsetAccomType> offsetsWhichAreEditing = findOffsetsWhichAreEditing(offsetsToSave, editingSeason);
        List<CPConfigOffsetAccomType> offsetsWhichAreNotEditing = findOffsetsWhichAreNotEditing(offsetsToSave, offsetsToDelete, offsetsWhichAreEditing, editingSeason);
        return pricingConfigurationValidationService.getInvalidHierarchiesEditingOffsets(getPricingConfigurationDTO().getProduct(), offsetsWhichAreEditing, offsetsWhichAreNotEditing);
    }

    public void validateAndManageOffsets(List<CPConfigOffsetAccomType> offsetsToSave, List<CPConfigOffsetAccomType> offsetsToDelete, Set<CPConfigOffsetAccomType> nonBaseOffsetsToUpdate, Pair<LocalDate, LocalDate> editingSeason, boolean reloadSeasonsAfterSaving, boolean ignoreRCRankValidation) {
        offsetService.updateForTax(offsetsToSave, isSupplementsEnabled());

        String warningMessage = null;
        String transientOffsetWarningMessage = null;
        String groupOffsetWarningMessage = null;
        String negativePercentageOffsetWarningMessage = null;
        String transientSeasonOffsetWarningMessage = null;
        String groupSeasonOffsetWarningMessage = null;
        String pppOneAdultWarningMessage = null;
        String pppAdultHierarchyWarningMessage = null;
        String pppChildHierarchyWarningMessage = null;
        String pppChildAgeHierarchyWarningMessage = null;
        String pppChildNegativeWarningMessage = null;

        if (isContinuousPricingEnabled) {
            List<PricingBaseAccomType> ceilingAndFloorConfig;
            if (isIndependentProductsEnabled) {
                ceilingAndFloorConfig = pricingConfigurationValidationService.getAllPricingBaseAccomTypesByProductId(getPricingConfigurationDTOProductID());
            } else {
                ceilingAndFloorConfig = pricingConfigurationValidationService.getAllPricingBaseAccomTypes();
            }
            List<PricingBaseAccomType> ceilingAndFloorConfigDefaults = ceilingAndFloorConfig.stream().filter(pricingBaseAccomType -> pricingBaseAccomType.getStartDate() == null && pricingBaseAccomType.getEndDate() == null).collect(Collectors.toList());
            List<PricingBaseAccomType> ceilingAndFloorConfigSeasons = ceilingAndFloorConfig.stream().filter(pricingBaseAccomType -> pricingBaseAccomType.getStartDate() != null && pricingBaseAccomType.getEndDate() != null).collect(Collectors.toList());
            if (!isAdvancedPriceRankingEnabled) {
                if (accomClassPriceRank == null) {
                    accomClassPriceRank = accomClassPriceRankService.getAccomClassPriceRank();
                }
                Set<String> roomClassesInViolation = null;
                // toggle to enable soft warning by pricing rule type.
                if (isCPFloorAndCeilingSoftWarningByPricingRuleTypeEnable()) {
                    Integer dailyBarPricingRuleType = getDailyBarPricingRuleType();
                    // For dailyBarPricingRuleType 0 or 1 ignore validation
                    if (dailyBarPricingRuleType.equals(2) || dailyBarPricingRuleType.equals(3)) {
                        if (!ignoreRCRankValidation) {
                            roomClassesInViolation = pricingConfigurationValidationService.validateSaveDefaultOffsetsByPricingRuleType(offsetsToSave, accomClassPriceRank, pricingAccomClasses, ceilingAndFloorConfig, baseOccupancyType);
                        }
                    }
                } else {
                    roomClassesInViolation = pricingConfigurationValidationService.validateSaveDefaultOffsets(offsetsToSave, accomClassPriceRank, pricingAccomClasses, ceilingAndFloorConfigDefaults, baseOccupancyType);
                }
                if (CollectionUtils.isNotEmpty(roomClassesInViolation)) {
                    warningMessage = String.join("\n", roomClassesInViolation);
                }
            }

            Set<String> roomClassesWithTransientOffsetViolation = pricingConfigurationValidationService.validateSaveOffsetLowerThanTransientFloor(offsetsToSave, ceilingAndFloorConfigDefaults);
            if (CollectionUtils.isNotEmpty(roomClassesWithTransientOffsetViolation)) {
                transientOffsetWarningMessage = String.join("\n", roomClassesWithTransientOffsetViolation);
            }

            Set<String> roomClassesWithTransientSeasonOffsetViolation = pricingConfigurationValidationService.validateSaveOffsetLowerThanTransientSeasonFloor(offsetsToSave, ceilingAndFloorConfigSeasons, caughtUpLocalDate);
            if (CollectionUtils.isNotEmpty(roomClassesWithTransientSeasonOffsetViolation)) {
                transientSeasonOffsetWarningMessage = String.join("\n", roomClassesWithTransientSeasonOffsetViolation);
            }

            Set<String> roomClassesWithNegativeOffsetViolation = offsetNegativePercentageValidation(offsetsToSave);
            if (CollectionUtils.isNotEmpty(roomClassesWithNegativeOffsetViolation)) {
                negativePercentageOffsetWarningMessage = String.join("\n", roomClassesWithNegativeOffsetViolation);
            }

            if (isPerPersonPricingEnabled) {
                List<CPConfigOffsetAccomType> newOffsetsToSave = findNewOffsetsToSave(offsetsToSave);

                Set<String> roomClassesWithOneAdultPositiveViolation = validateOneAdultOffsets(newOffsetsToSave);
                if (CollectionUtils.isNotEmpty(roomClassesWithOneAdultPositiveViolation)) {
                    pppOneAdultWarningMessage = String.join(", ", roomClassesWithOneAdultPositiveViolation);
                }

                Set<String> roomClassesWithAdultHierarchyViolation = validateAdultHierarchyOffsets(offsetsToSave);
                if (CollectionUtils.isNotEmpty(roomClassesWithAdultHierarchyViolation)) {
                    pppAdultHierarchyWarningMessage = String.join(", ", roomClassesWithAdultHierarchyViolation);
                }

                Set<String> roomClassesWithChildHierarchyViolation = validateChildHierarchyOffsets(offsetsToSave);
                if (CollectionUtils.isNotEmpty(roomClassesWithChildHierarchyViolation)) {
                    pppChildHierarchyWarningMessage = String.join(", ", roomClassesWithChildHierarchyViolation);
                }

                Set<String> roomClassesWithChildAgeHierarchyViolation = validateChildAgeHierarchyOffsets(offsetsToSave);
                if (CollectionUtils.isNotEmpty(roomClassesWithChildAgeHierarchyViolation)) {
                    pppChildAgeHierarchyWarningMessage = String.join(", ", roomClassesWithChildAgeHierarchyViolation);
                }

                Set<String> roomClassesWithChildNegativeViolation = validateChildNegativeOffsets(offsetsToSave);
                if (CollectionUtils.isNotEmpty(roomClassesWithChildNegativeViolation)) {
                    pppChildNegativeWarningMessage = String.join(", ", roomClassesWithChildNegativeViolation);
                }
            }
        }
        final List<ProductHierarchy> invalidHierarchiesIncludingOffsets = new ArrayList<>();
        if (isContinuousPricingEnabled && SystemConfig.isComprehensiveProductHierarchyValidationEnabled()) {
            List<ProductHierarchy> allHierarchiesForProduct = agileRatesConfigurationService.findImpactedProductHierarchies(pricingConfigurationDTO.getProduct().getId());
            allHierarchiesForProduct.forEach(productHierarchy -> {
                Product selectedProduct, relatedProduct;
                if (productHierarchy.getFromProduct().equals(pricingConfigurationDTO.getProduct())) {
                    selectedProduct = pricingConfigurationDTO.getProduct();
                    relatedProduct = productHierarchy.getToProduct();
                } else {
                    selectedProduct = productHierarchy.getFromProduct();
                    relatedProduct = pricingConfigurationDTO.getProduct();
                }
                List<CPConfigOffsetAccomType> offsets = getOffsets(offsetsToSave, offsetsToDelete, editingSeason);
                ProductHierarchyDto productHierarchyDto = this.getProductHierarchyDto(offsets, productHierarchy, selectedProduct, relatedProduct);
                List<String> warnings = new ArrayList<>();
                boolean isValid = productHierarchyValidationService.validateHierarchyWhenEditingOffsets(productHierarchyDto, warnings);
                if (!isValid) {
                    invalidHierarchiesIncludingOffsets.add(productHierarchy);
                }
            });
        } else if (isContinuousPricingEnabled) {
            invalidHierarchiesIncludingOffsets.addAll(getInvalidHierarchiesWhileEditingOffsets(offsetsToSave, offsetsToDelete, editingSeason));
        }

        if (isContinuousPricingEnabled && !isPerPersonPricingEnabled && isChildAgeBucketsEnabled && CollectionUtils.isNotEmpty(occupantBucketEntities)) {
            Set<String> roomClassesWithChildAgeHierarchyViolation = validateChildAgeHierarchyOffsets(offsetsToSave);
            if (CollectionUtils.isNotEmpty(roomClassesWithChildAgeHierarchyViolation)) {
                pppChildAgeHierarchyWarningMessage = String.join(", ", roomClassesWithChildAgeHierarchyViolation);
            }
        }

        if (isCPFloorAndCeilingSoftWarningByPricingRuleTypeEnable()) {
            if (negativePercentageOffsetWarningMessage != null) {
                showWarning(getText(BREAKS_ROOM_TYPE_OFFSET_LOWER_THAN_FLOOR, negativePercentageOffsetWarningMessage));
            } else if (transientOffsetWarningMessage != null) {
                showWarning(getText(BREAKS_ROOM_TYPE_OFFSET_LOWER_THAN_FLOOR, transientOffsetWarningMessage));
            } else if (transientSeasonOffsetWarningMessage != null) {
                showWarning(getText(BREAKS_ROOM_TYPE_OFFSET_LOWER_THAN_SEASON_FLOOR, transientSeasonOffsetWarningMessage));
            } else if (groupOffsetWarningMessage != null) {
                showWarning(getText(BREAKS_ROOM_TYPE_OFFSET_LOWER_THAN_FLOOR, groupOffsetWarningMessage));
            } else if (groupSeasonOffsetWarningMessage != null) {
                showWarning(getText(BREAKS_ROOM_TYPE_OFFSET_LOWER_THAN_SEASON_FLOOR, groupSeasonOffsetWarningMessage));
            } else if (isPerPersonPricingEnabled) {
                TetrisConfirmationWindow window = new TetrisConfirmationWindow();

                VerticalLayout layout = new VerticalLayout();
                window.setInnerContent(layout);

                window.setCaption(getText(WINDOW_CAPTION_REVIEW_CONFIGURATION));
                window.setYesLabel(getText(WINDOW_YES_LABEL_OK));
                window.setNoLabel(getText(WINDOW_NO_LABEL));

                String finalWarningMessage = warningMessage;
                window.setYesClickListener(clickEvent -> {
                    proceedForSoftWarningAndSave(finalWarningMessage, offsetsToSave, offsetsToDelete, nonBaseOffsetsToUpdate, reloadSeasonsAfterSaving);
                    agileRatesConfigurationService.removeHierarchies(invalidHierarchiesIncludingOffsets);
                    view.closeSeasonWindow();
                });
                window.setNoClickListener(clickEvent -> view.closeSeasonWindow());

                if (pppChildNegativeWarningMessage != null) {
                    showWarning(getText(BREAKS_CHILD_NEGATIVE_OFFSET, pppChildNegativeWarningMessage));
                    return;
                }

                if (pppOneAdultWarningMessage != null) {
                    layout.addComponent(new Label(getText(BREAKS_ONE_ADULT_OFFSET, pppOneAdultWarningMessage)));
                }

                if (pppAdultHierarchyWarningMessage != null) {
                    layout.addComponent(new Label(getText(BREAKS_ADULT_OFFSET_HIERARCHY, pppAdultHierarchyWarningMessage)));
                }

                if (pppChildHierarchyWarningMessage != null) {
                    layout.addComponent(new Label(getText(BREAKS_CHILD_OFFSET_HIERARCHY, pppChildHierarchyWarningMessage)));
                }

                if (pppChildAgeHierarchyWarningMessage != null) {
                    layout.addComponent(new Label(getText(BREAKS_CHILD_AGE_OFFSET_HIERARCHY, pppChildAgeHierarchyWarningMessage)));
                }

                if (CollectionUtils.isNotEmpty(invalidHierarchiesIncludingOffsets)) {
                    layout.addComponent(new Label(getText("hierarchy.was.broken.because.of.offsets.warning")));
                }

                if (layout.getComponentCount() > 0) {
                    window.show();
                } else {
                    proceedForSoftWarningAndSave(warningMessage, offsetsToSave, offsetsToDelete, nonBaseOffsetsToUpdate, reloadSeasonsAfterSaving);
                }
            } else if (isContinuousPricingEnabled && !isPerPersonPricingEnabled && isChildAgeBucketsEnabled && CollectionUtils.isNotEmpty(occupantBucketEntities)) {
                TetrisConfirmationWindow window = new TetrisConfirmationWindow();

                VerticalLayout layout = new VerticalLayout();
                window.setInnerContent(layout);

                window.setCaption(getText(WINDOW_CAPTION_REVIEW_CONFIGURATION));
                window.setYesLabel(getText(WINDOW_YES_LABEL_OK));
                window.setNoLabel(getText(WINDOW_NO_LABEL));

                String finalWarningMessage = warningMessage;
                window.setYesClickListener(clickEvent -> {
                    proceedForSoftWarningAndSave(finalWarningMessage, offsetsToSave, offsetsToDelete, nonBaseOffsetsToUpdate, reloadSeasonsAfterSaving);
                    agileRatesConfigurationService.removeHierarchies(invalidHierarchiesIncludingOffsets);
                    view.closeSeasonWindow();
                });
                window.setNoClickListener(clickEvent -> {
                    window.close();
                    init(true);
                });

                if (pppChildAgeHierarchyWarningMessage != null) {
                    layout.addComponent(new Label(getText(BREAKS_CHILD_AGE_OFFSET_HIERARCHY, pppChildAgeHierarchyWarningMessage)));
                }

                if (CollectionUtils.isNotEmpty(invalidHierarchiesIncludingOffsets)) {
                    layout.addComponent(new Label(getText("hierarchy.was.broken.because.of.offsets.warning")));
                }

                if (layout.getComponentCount() > 0) {
                    window.show();
                } else {
                    proceedForSoftWarningAndSave(warningMessage, offsetsToSave, offsetsToDelete, nonBaseOffsetsToUpdate, reloadSeasonsAfterSaving);
                }
            } else if (CollectionUtils.isNotEmpty(invalidHierarchiesIncludingOffsets)) {
                TetrisConfirmationWindow window = new TetrisConfirmationWindow();

                VerticalLayout layout = new VerticalLayout();
                window.setInnerContent(layout);

                window.setCaption(getText(WINDOW_CAPTION_REVIEW_CONFIGURATION));
                window.setYesLabel(getText(WINDOW_YES_LABEL_OK));
                window.setNoLabel(getText(WINDOW_NO_LABEL));

                String finalWarningMessage = warningMessage;
                window.setYesClickListener(clickEvent -> {
                    proceedForSoftWarningAndSave(finalWarningMessage, offsetsToSave, offsetsToDelete, nonBaseOffsetsToUpdate, reloadSeasonsAfterSaving);
                    agileRatesConfigurationService.removeHierarchies(invalidHierarchiesIncludingOffsets);
                    view.closeSeasonWindow();
                });
                window.setNoClickListener(clickEvent -> window.close());
                layout.addComponent(new Label(getText("hierarchy.was.broken.because.of.offsets.warning")));
                window.show();
            } else if (warningMessage != null) {
                proceedForSoftWarningAndSave(warningMessage, offsetsToSave, offsetsToDelete, nonBaseOffsetsToUpdate, reloadSeasonsAfterSaving);
            } else {
                handleOffsetsAndRefresh(offsetsToSave, offsetsToDelete, nonBaseOffsetsToUpdate, reloadSeasonsAfterSaving);
            }

        } else if (isCPOffsetSoftWarningEnabled()) {
            if (negativePercentageOffsetWarningMessage != null) {
                showWarning(getText(BREAKS_ROOM_TYPE_OFFSET_LOWER_THAN_FLOOR, negativePercentageOffsetWarningMessage));
            } else if (transientOffsetWarningMessage != null) {
                showWarning(getText(BREAKS_ROOM_TYPE_OFFSET_LOWER_THAN_FLOOR, transientOffsetWarningMessage));
            } else if (transientSeasonOffsetWarningMessage != null) {
                showWarning(getText(BREAKS_ROOM_TYPE_OFFSET_LOWER_THAN_SEASON_FLOOR, transientSeasonOffsetWarningMessage));
            } else if (groupOffsetWarningMessage != null) {
                showWarning(getText(BREAKS_ROOM_TYPE_OFFSET_LOWER_THAN_FLOOR, groupOffsetWarningMessage));
            } else if (groupSeasonOffsetWarningMessage != null) {
                showWarning(getText(BREAKS_ROOM_TYPE_OFFSET_LOWER_THAN_SEASON_FLOOR, groupSeasonOffsetWarningMessage));
            } else if (isPerPersonPricingEnabled) {
                TetrisConfirmationWindow window = new TetrisConfirmationWindow();

                VerticalLayout layout = new VerticalLayout();
                window.setInnerContent(layout);

                window.setCaption(getText(WINDOW_CAPTION_REVIEW_CONFIGURATION));
                window.setYesLabel(getText(WINDOW_YES_LABEL_OK));
                window.setNoLabel(getText(WINDOW_NO_LABEL));

                String finalWarningMessage = warningMessage;
                window.setYesClickListener(clickEvent -> {
                    proceedForSoftWarningAndSave(finalWarningMessage, offsetsToSave, offsetsToDelete, nonBaseOffsetsToUpdate, reloadSeasonsAfterSaving);
                    agileRatesConfigurationService.removeHierarchies(invalidHierarchiesIncludingOffsets);
                    view.closeSeasonWindow();
                });
                window.setNoClickListener(clickEvent -> window.close());

                if (pppChildNegativeWarningMessage != null) {
                    showWarning(getText(BREAKS_CHILD_NEGATIVE_OFFSET, pppChildNegativeWarningMessage));
                    return;
                }

                if (pppOneAdultWarningMessage != null) {
                    layout.addComponent(new Label(getText(BREAKS_ONE_ADULT_OFFSET, pppOneAdultWarningMessage)));
                }

                if (pppAdultHierarchyWarningMessage != null) {
                    layout.addComponent(new Label(getText(BREAKS_ADULT_OFFSET_HIERARCHY, pppAdultHierarchyWarningMessage)));
                }

                if (pppChildHierarchyWarningMessage != null) {
                    layout.addComponent(new Label(getText(BREAKS_CHILD_OFFSET_HIERARCHY, pppChildHierarchyWarningMessage)));
                }

                if (pppChildAgeHierarchyWarningMessage != null) {
                    layout.addComponent(new Label(getText(BREAKS_CHILD_AGE_OFFSET_HIERARCHY, pppChildAgeHierarchyWarningMessage)));
                }

                if (CollectionUtils.isNotEmpty(invalidHierarchiesIncludingOffsets)) {
                    layout.addComponent(new Label(getText("hierarchy.was.broken.because.of.offsets.warning")));
                }

                if (layout.getComponentCount() > 0) {
                    window.show();
                } else {
                    proceedForSoftWarningAndSave(warningMessage, offsetsToSave, offsetsToDelete, nonBaseOffsetsToUpdate, reloadSeasonsAfterSaving);
                }
            } else if (isContinuousPricingEnabled && !isPerPersonPricingEnabled && isChildAgeBucketsEnabled && CollectionUtils.isNotEmpty(occupantBucketEntities)) {
                TetrisConfirmationWindow window = new TetrisConfirmationWindow();

                VerticalLayout layout = new VerticalLayout();
                window.setInnerContent(layout);

                window.setCaption(getText(WINDOW_CAPTION_REVIEW_CONFIGURATION));
                window.setYesLabel(getText(WINDOW_YES_LABEL_OK));
                window.setNoLabel(getText(WINDOW_NO_LABEL));

                String finalWarningMessage = warningMessage;
                window.setYesClickListener(clickEvent -> {
                    proceedForSoftWarningAndSave(finalWarningMessage, offsetsToSave, offsetsToDelete, nonBaseOffsetsToUpdate, reloadSeasonsAfterSaving);
                    agileRatesConfigurationService.removeHierarchies(invalidHierarchiesIncludingOffsets);
                    view.closeSeasonWindow();
                });
                window.setNoClickListener(clickEvent -> window.close());

                if (pppChildAgeHierarchyWarningMessage != null) {
                    layout.addComponent(new Label(getText(BREAKS_CHILD_AGE_OFFSET_HIERARCHY, pppChildAgeHierarchyWarningMessage)));
                }

                if (CollectionUtils.isNotEmpty(invalidHierarchiesIncludingOffsets)) {
                    layout.addComponent(new Label(getText("hierarchy.was.broken.because.of.offsets.warning")));
                }

                if (layout.getComponentCount() > 0) {
                    window.show();
                } else {
                    proceedForSoftWarningAndSave(warningMessage, offsetsToSave, offsetsToDelete, nonBaseOffsetsToUpdate, reloadSeasonsAfterSaving);
                }
            } else if (CollectionUtils.isNotEmpty(invalidHierarchiesIncludingOffsets)) {
                TetrisConfirmationWindow window = new TetrisConfirmationWindow();

                VerticalLayout layout = new VerticalLayout();
                window.setInnerContent(layout);

                window.setCaption(getText(WINDOW_CAPTION_REVIEW_CONFIGURATION));
                window.setYesLabel(getText(WINDOW_YES_LABEL_OK));
                window.setNoLabel(getText(WINDOW_NO_LABEL));

                String finalWarningMessage = warningMessage;
                window.setYesClickListener(clickEvent -> {
                    proceedForSoftWarningAndSave(finalWarningMessage, offsetsToSave, offsetsToDelete, nonBaseOffsetsToUpdate, reloadSeasonsAfterSaving);
                    agileRatesConfigurationService.removeHierarchies(invalidHierarchiesIncludingOffsets);
                    view.closeSeasonWindow();
                });
                window.setNoClickListener(clickEvent -> window.close());
                layout.addComponent(new Label(getText("hierarchy.was.broken.because.of.offsets.warning")));
                window.show();
            } else if (warningMessage != null) {
                proceedForSoftWarningAndSave(warningMessage, offsetsToSave, offsetsToDelete, nonBaseOffsetsToUpdate, reloadSeasonsAfterSaving);
            } else {
                handleOffsetsAndRefresh(offsetsToSave, offsetsToDelete, nonBaseOffsetsToUpdate, reloadSeasonsAfterSaving);
            }

        } else {
            if (warningMessage != null) {
                showWarning(getText(BREAKS_ROOM_CLASS_RANK) + "\n" + warningMessage);
            } else if (negativePercentageOffsetWarningMessage != null) {
                showWarning(getText(BREAKS_ROOM_TYPE_OFFSET_LOWER_THAN_FLOOR, negativePercentageOffsetWarningMessage));
            } else if (transientOffsetWarningMessage != null) {
                showWarning(getText(BREAKS_ROOM_TYPE_OFFSET_LOWER_THAN_FLOOR, transientOffsetWarningMessage));
            } else if (transientSeasonOffsetWarningMessage != null) {
                showWarning(getText(BREAKS_ROOM_TYPE_OFFSET_LOWER_THAN_SEASON_FLOOR, transientSeasonOffsetWarningMessage));
            } else if (groupOffsetWarningMessage != null) {
                showWarning(getText(BREAKS_ROOM_TYPE_OFFSET_LOWER_THAN_FLOOR, groupOffsetWarningMessage));
            } else if (groupSeasonOffsetWarningMessage != null) {
                showWarning(getText(BREAKS_ROOM_TYPE_OFFSET_LOWER_THAN_SEASON_FLOOR, groupSeasonOffsetWarningMessage));
            } else if (isPerPersonPricingEnabled) {
                TetrisConfirmationWindow window = new TetrisConfirmationWindow();

                VerticalLayout layout = new VerticalLayout();
                window.setInnerContent(layout);

                window.setCaption(getText(WINDOW_CAPTION_REVIEW_CONFIGURATION));
                window.setYesLabel(getText(WINDOW_YES_LABEL_OK));
                window.setNoLabel(getText(WINDOW_NO_LABEL));

                window.setYesClickListener(clickEvent -> {
                    handleOffsetsAndRefresh(offsetsToSave, offsetsToDelete, nonBaseOffsetsToUpdate, reloadSeasonsAfterSaving);
                    agileRatesConfigurationService.removeHierarchies(invalidHierarchiesIncludingOffsets);
                    view.closeSeasonWindow();
                });
                window.setNoClickListener(clickEvent -> window.close());

                if (pppChildNegativeWarningMessage != null) {
                    showWarning(getText(BREAKS_CHILD_NEGATIVE_OFFSET, pppChildNegativeWarningMessage));
                    return;
                }

                if (pppOneAdultWarningMessage != null) {
                    layout.addComponent(new Label(getText(BREAKS_ONE_ADULT_OFFSET, pppOneAdultWarningMessage)));
                }

                if (pppAdultHierarchyWarningMessage != null) {
                    layout.addComponent(new Label(getText(BREAKS_ADULT_OFFSET_HIERARCHY, pppAdultHierarchyWarningMessage)));
                }

                if (pppChildHierarchyWarningMessage != null) {
                    layout.addComponent(new Label(getText(BREAKS_CHILD_OFFSET_HIERARCHY, pppChildHierarchyWarningMessage)));
                }

                if (pppChildAgeHierarchyWarningMessage != null) {
                    layout.addComponent(new Label(getText(BREAKS_CHILD_AGE_OFFSET_HIERARCHY, pppChildAgeHierarchyWarningMessage)));
                }

                if (CollectionUtils.isNotEmpty(invalidHierarchiesIncludingOffsets)) {
                    layout.addComponent(new Label(getText("hierarchy.was.broken.because.of.offsets.warning")));
                }

                if (layout.getComponentCount() > 0) {
                    window.show();
                } else {
                    handleOffsetsAndRefresh(offsetsToSave, offsetsToDelete, nonBaseOffsetsToUpdate, reloadSeasonsAfterSaving);
                }
            } else if (CollectionUtils.isNotEmpty(invalidHierarchiesIncludingOffsets)) {
                TetrisConfirmationWindow window = new TetrisConfirmationWindow();

                VerticalLayout layout = new VerticalLayout();
                window.setInnerContent(layout);

                window.setCaption(getText(WINDOW_CAPTION_REVIEW_CONFIGURATION));
                window.setYesLabel(getText(WINDOW_YES_LABEL_OK));
                window.setNoLabel(getText(WINDOW_NO_LABEL));

                String finalWarningMessage = warningMessage;
                window.setYesClickListener(clickEvent -> {
                    proceedForSoftWarningAndSave(finalWarningMessage, offsetsToSave, offsetsToDelete, nonBaseOffsetsToUpdate, reloadSeasonsAfterSaving);
                    agileRatesConfigurationService.removeHierarchies(invalidHierarchiesIncludingOffsets);
                    view.closeSeasonWindow();
                });
                window.setNoClickListener(clickEvent -> window.close());
                layout.addComponent(new Label(getText("hierarchy.was.broken.because.of.offsets.warning")));
                window.show();
            } else if (isContinuousPricingEnabled && !isPerPersonPricingEnabled && isChildAgeBucketsEnabled && CollectionUtils.isNotEmpty(occupantBucketEntities)) {
                TetrisConfirmationWindow window = new TetrisConfirmationWindow();

                VerticalLayout layout = new VerticalLayout();
                window.setInnerContent(layout);

                window.setCaption(getText(WINDOW_CAPTION_REVIEW_CONFIGURATION));
                window.setYesLabel(getText(WINDOW_YES_LABEL_OK));
                window.setNoLabel(getText(WINDOW_NO_LABEL));

                String finalWarningMessage = warningMessage;
                window.setYesClickListener(clickEvent -> {
                    proceedForSoftWarningAndSave(finalWarningMessage, offsetsToSave, offsetsToDelete, nonBaseOffsetsToUpdate, reloadSeasonsAfterSaving);
                    agileRatesConfigurationService.removeHierarchies(invalidHierarchiesIncludingOffsets);
                    view.closeSeasonWindow();
                });
                window.setNoClickListener(clickEvent -> window.close());

                if (pppChildAgeHierarchyWarningMessage != null) {
                    layout.addComponent(new Label(getText(BREAKS_CHILD_AGE_OFFSET_HIERARCHY, pppChildAgeHierarchyWarningMessage)));
                }

                if (CollectionUtils.isNotEmpty(invalidHierarchiesIncludingOffsets)) {
                    layout.addComponent(new Label(getText("hierarchy.was.broken.because.of.offsets.warning")));
                }

                if (layout.getComponentCount() > 0) {
                    window.show();
                } else {
                    proceedForSoftWarningAndSave(warningMessage, offsetsToSave, offsetsToDelete, nonBaseOffsetsToUpdate, reloadSeasonsAfterSaving);
                }
            } else {
                handleOffsetsAndRefresh(offsetsToSave, offsetsToDelete, nonBaseOffsetsToUpdate, reloadSeasonsAfterSaving);
            }
        }
    }

    private List<CPConfigOffsetAccomType> getOffsets(List<CPConfigOffsetAccomType> offsetsToSave, List<CPConfigOffsetAccomType> offsetsToDelete, Pair<LocalDate, LocalDate> editingSeason) {
        List<CPConfigOffsetAccomType> offsetsWhichAreEditing = findOffsetsWhichAreEditing(offsetsToSave, editingSeason);
        List<CPConfigOffsetAccomType> offsetsWhichAreNotEditing = findOffsetsWhichAreNotEditing(offsetsToSave, offsetsToDelete, offsetsWhichAreEditing, editingSeason);
        List<CPConfigOffsetAccomType> offsets = new ArrayList<>();
        offsets.addAll(offsetsWhichAreEditing);
        offsets.addAll(offsetsWhichAreNotEditing);
        return offsets;
    }

    private ProductHierarchyDto getProductHierarchyDto(List<CPConfigOffsetAccomType> editedOffsets, ProductHierarchy productHierarchy,
                                                       Product selectedProduct, Product relatedProduct) {
        ProductHierarchyDto productHierarchyDto = new ProductHierarchyDto();
        productHierarchyDto.setSelectedProduct(selectedProduct);
        productHierarchyDto.setRelatedProduct(relatedProduct);
        productHierarchyDto.setEditedOffsets(editedOffsets);
        productHierarchyDto.setMinimumDifference(productHierarchy.getMinimumDifference());
        return productHierarchyDto;
    }

    private List<CPConfigOffsetAccomType> filterOffsets(List<CPConfigOffsetAccomType> offsets,
                                                        Predicate<CPConfigOffsetAccomType> filter) {
        return offsets.stream()
                .filter(filter)
                .collect(Collectors.toList());
    }

    @Override
    protected void proceedForSoftWarningAndSave(String warningMessage, List<CPConfigOffsetAccomType> offsetsToSave,
                                              List<CPConfigOffsetAccomType> offsetsToDelete,
                                              Set<CPConfigOffsetAccomType> nonBaseOffsetsToUpdate,
                                              boolean reloadSeasonsAfterSaving) {
        if (warningMessage != null) {
            String formattedWarningMessage = null;
            if (isCPFloorAndCeilingSoftWarningByPricingRuleTypeEnable()) {
                formattedWarningMessage = getWarningMessageByDailyBarPricingRuleType(warningMessage, getDailyBarPricingRuleType());
            } else {
                formattedWarningMessage = getText(BREAKS_ROOM_CLASS_RANK) + "<br/>" + warningMessage.replace("\n", "<br/>");
            }
            showSoftWarningDialogue(formattedWarningMessage, yesClick -> {
                handleOffsetsAndRefresh(offsetsToSave, offsetsToDelete, nonBaseOffsetsToUpdate, reloadSeasonsAfterSaving);
            });
        } else {
            handleOffsetsAndRefresh(offsetsToSave, offsetsToDelete, nonBaseOffsetsToUpdate, reloadSeasonsAfterSaving);
        }
    }

    public void showSoftWarningDialogue(String warningMessage, Button.ClickListener yesClickListener) {
        TetrisMessageBox warningDialogue = createWarningDialogue(UiUtils.getText("warning"), warningMessage);
        warningDialogue.setCustomMessageLabelWidth("600px");
        warningDialogue.setYesClickListener(yesClickListener);
        warningDialogue.setNoClickListener(noClick -> {
            warningDialogue.close();
        });
        warningDialogue.setYesLabel(UiUtils.getText("common.continue"));
        warningDialogue.setNoLabel(UiUtils.getText("cancel"));
        warningDialogue.show();
    }

    public void handleOffsetsAndRefresh(List<CPConfigOffsetAccomType> offsetsToSave, List<CPConfigOffsetAccomType> offsetsToDelete, Set<CPConfigOffsetAccomType> nonBaseOffsetsToUpdate, boolean reloadSeasonsAfterSaving) {
        if (accomTypes == null) {
            if (isIndependentProductsEnabled && pricingConfigurationDTO.getIndependentProductDTO() != null && pricingConfigurationDTO.isIndependentProduct()) {
                accomTypes = pricingConfigurationService.getRoomTypesForProduct(pricingConfigurationDTO.getProduct());
            } else {
                accomTypes = roomClassService.getAllActiveAccomTypes();
            }
            if (isRoomTypeRecodingUIEnabled) {
                accomTypes = accomTypes.stream().filter(accomType -> accomType.getDisplayStatusId().equals(Status.ACTIVE.getId())).collect(Collectors.toList());
            }
        }

        try {
            offsetService.updatePricingOffsets(offsetsToSave, offsetsToDelete, excludedPricingAccomClasses, accomTypes, nonBaseOffsetsToUpdate, systemDateAsLocalDate, isCPBaseRoomTypeOnlyEnabled);
        } catch (SeasonNameTooLargeException e) {
            logger.error(e.getMessage());
            showError(UiUtils.getText("pricingConfiguration.offsetSeasonNameTooLarge", e.getInvalidSeasonName()));
            init(reloadSeasonsAfterSaving);
            return;
        }

        showSaveSuccessMessage();
        //This is needed due to all the magic that happens in the service for doNullCheckForSAS
        cpConfigOffsetAccomTypes = offsetService.retrieveOffsetConfigNoPriceExcluded(uiContext.getPropertyId(), true, getPricingConfigurationDTOProductID());
        init(reloadSeasonsAfterSaving);
    }

    public Set<String> offsetNegativePercentageValidation(List<CPConfigOffsetAccomType> offsetsToSave) {

        Set<String> roomClassesWithOffsetViolation = new HashSet<>();

        offsetsToSave.forEach(config -> {
            if (config.getOffsetMethod().toString().equals(OffsetMethod.PERCENTAGE.toString()) &&
                    (offsetValidation(config.getSundayOffsetValue()) ||
                            offsetValidation(config.getMondayOffsetValue()) ||
                            offsetValidation(config.getTuesdayOffsetValue()) ||
                            offsetValidation(config.getWednesdayOffsetValue()) ||
                            offsetValidation(config.getThursdayOffsetValue()) ||
                            offsetValidation(config.getFridayOffsetValue()) ||
                            offsetValidation(config.getSaturdayOffsetValue()))) {
                roomClassesWithOffsetViolation.add(config.getAccomType().getName());
            }
        });
        return roomClassesWithOffsetViolation;
    }

    public boolean offsetValidation(BigDecimal offsetRate) {
        BigDecimal minNegativePercentage = new BigDecimal(-99);
        return (offsetRate != null && offsetRate.compareTo(minNegativePercentage) < 0);
    }

    List<CPConfigOffsetAccomType> findOffsetsToSave(List<PricingConfigurationOffsetWrapper> wrappers) {
        List<CPConfigOffsetAccomType> listToSave = new ArrayList<>();

        wrappers.forEach(wrapper -> {
            //only pull out entities at the room type or offset level
            if (!wrapper.isRoomClass()) {
                listToSave.add(wrapper.getCpConfigOffsetAccomType());
            }

            listToSave.addAll(findOffsetsToSave(wrapper.getChildren()));
        });

        return listToSave;
    }

    private List<CPConfigOffsetAccomType> findNewOffsetsToSave(List<CPConfigOffsetAccomType> offsets) {
        List<CPConfigOffsetAccomType> existingOffsets = offsetService.retrieveOffsetConfigNoPriceExcluded(uiContext.getPropertyId(), true, getPricingConfigurationDTOProductID());
        return findNewOffsetsToSave(offsets, existingOffsets);
    }

    private List<CPConfigOffsetAccomType> findNewOffsetsToSave(List<CPConfigOffsetAccomType> offsets,
                                                               List<CPConfigOffsetAccomType> existingOffsets) {
        List<CPConfigOffsetAccomType> listToSave = new ArrayList<>();

        for (CPConfigOffsetAccomType newOffset : offsets) {
            boolean found = false;
            for (CPConfigOffsetAccomType existingOffset : existingOffsets) {
                if (compareToSaveOffsets(existingOffset, newOffset)) {
                    found = true;
                    break;
                }
            }

            if (!found) {
                listToSave.add(newOffset);
            }
        }

        return listToSave;
    }

    private boolean compareToSaveOffsets(CPConfigOffsetAccomType existingOffset, CPConfigOffsetAccomType newOffset) {
        return !newOffset.containsNullDayOfWeekValues() &&
                !existingOffset.containsNullDayOfWeekValues() &&
                existingOffset.getId().equals(newOffset.getId()) &&
                (existingOffset.getSundayOffsetValue().equals(newOffset.getSundayOffsetValue()) &&
                        existingOffset.getMondayOffsetValue().equals(newOffset.getMondayOffsetValue()) &&
                        existingOffset.getTuesdayOffsetValue().equals(newOffset.getTuesdayOffsetValue()) &&
                        existingOffset.getWednesdayOffsetValue().equals(newOffset.getWednesdayOffsetValue()) &&
                        existingOffset.getThursdayOffsetValue().equals(newOffset.getThursdayOffsetValue()) &&
                        existingOffset.getFridayOffsetValue().equals(newOffset.getFridayOffsetValue()) &&
                        existingOffset.getSaturdayOffsetValue().equals(newOffset.getSaturdayOffsetValue())
                );
    }

    private boolean shouldBeSaved(CPConfigOffsetAccomType offset) {
        //if the entity has been persisted already, always send it to the backend
        //only save offset records that have an existing id or they have at least one populated offset
        return (offset.getId() != null || offset.hasAtLeastOneOffsetValuePopulated());
    }

    public boolean isBaseOccupancyType(CPConfigOffsetAccomType offset) {
        return baseOccupancyType.equals(offset.getOccupancyType()) && baseRoomTypes.contains(offset.getAccomType().getName()) && !excludedPricingAccomClasses.stream().anyMatch(pricingAccomClass -> pricingAccomClass.getAccomType().equals(offset.getAccomType()));
    }

    public PricingConfigurationOffsetSeasonWrapper newSeason() {
        PricingConfigurationOffsetSeasonWrapper seasonWrapper = new PricingConfigurationOffsetSeasonWrapper();
        List<PricingConfigurationOffsetWrapper> defaultOffsets = getDefaultOffsets(false);
        seasonWrapper.setOffsets(defaultOffsets);

        return seasonWrapper;
    }

    public boolean willSplitOccur(PricingConfigurationOffsetSeasonWrapper season) {
        return seasonService.willSplitOccur(seasons, season);
    }

    public boolean isSeasonEmpty(List<PricingConfigurationOffsetWrapper> offsetWrappers) {
        for (PricingConfigurationOffsetWrapper offsetWrapper : offsetWrappers) {
            if (!isOffsetWrapperEmpty(offsetWrapper)) {
                return false;
            }
        }
        return true;
    }

    public Boolean isOffsetWrapperEmpty(PricingConfigurationOffsetWrapper offsetWrapper) {
        for (PricingConfigurationOffsetWrapper child : offsetWrapper.getChildren()) {
            if (child.getCpConfigOffsetAccomType().hasAtLeastOneOffsetValuePopulated()) {
                return false;
            }
            if (!child.getChildren().isEmpty() && !isOffsetWrapperEmpty(child)) {
                return false;
            }
        }
        return true;
    }

    public void saveSeason(PricingConfigurationOffsetSeasonWrapper season, boolean ignoreRCRankValidation) {
        boolean startDateIsBeforeToday = season.getStartDate().isBefore(systemDateAsLocalDate);
        PricingConfigurationOffsetSeasonWrapper pastSeason = updatePastSeasonEndDate(season, startDateIsBeforeToday);
        List<PricingConfigurationOffsetSeasonWrapper> seasonsToBeDeleted = getOverlappingSeasons(season, pastSeason);

        List<PricingConfigurationOffsetSeasonWrapper> updatedSeasons = seasonService.applySplit(seasons, season, systemDateAsLocalDate);

        if (startDateIsBeforeToday) {
            updatePreviousSeasonIn(seasonsToBeDeleted, updatedSeasons);
        }

        List<CPConfigOffsetAccomType> basePricingAccomTypesToUpdate = getSeasonOffsetsList(updatedSeasons);
        List<CPConfigOffsetAccomType> basePricingAccomTypesToDelete = getSeasonOffsetsList(seasonsToBeDeleted);

        Set<CPConfigOffsetAccomType> nonBaseOffsetsToUpdate = season.getNonBaseRoomTypeOffsets();

        pricingConfigurationLTBDEService.enabledLTBDEIfApplicableForOffsetChange(basePricingAccomTypesToUpdate, dateService.getCaughtUpJavaLocalDate());

        validateAndManageOffsets(basePricingAccomTypesToUpdate, basePricingAccomTypesToDelete, nonBaseOffsetsToUpdate,
                Pair.of(season.getStartDate(), season.getEndDate()), true, ignoreRCRankValidation);

    }

    public boolean isValueMissingWhileEditingSeason(LinkedHashMap<PricingAccomClass, LinkedHashMap<AccomType, LinkedList<CPConfigOffsetAccomType>>> offsetsList, PricingConfigurationOffsetSeasonWrapper season, boolean isValueMissing) {
        java.time.LocalDate startDate = JavaDateUtil.convertJodaLocalDateToJavaLocalDate(season.getStartDate());
        java.time.LocalDate endDate = JavaDateUtil.convertJodaLocalDateToJavaLocalDate(season.getEndDate());

        isValueMissing = offsetsList.values().stream()
                .flatMap(innerMap -> innerMap.values().stream())
                .filter(Objects::nonNull)
                .flatMap(Collection::stream)
                .anyMatch(config -> validateOffsetDaysMatch(startDate, endDate, config));

        return isValueMissing;
    }

    public boolean isValueMissingWhileFormingSeason(CPConfigOffsetAccomType configList, PricingConfigurationOffsetSeasonWrapper season, boolean isValueMissing) {
        java.time.LocalDate startDate = JavaDateUtil.convertJodaLocalDateToJavaLocalDate(season.getStartDate());
        java.time.LocalDate endDate = JavaDateUtil.convertJodaLocalDateToJavaLocalDate(season.getEndDate());

            // Check if the number of days in the range is equal to twice the non-null BigDecimal variables count
            if (validateOffsetDaysMatch(startDate, endDate, configList)) {
                isValueMissing = true;
            }
        return isValueMissing;
    }

    private boolean validateOffsetDaysMatch(java.time.LocalDate startDate, java.time.LocalDate endDate, CPConfigOffsetAccomType config) {
        // Calculate the number of days between startDate and endDate
        long daysBetween = ChronoUnit.DAYS.between(startDate, endDate) + 1;

        // Get the non-null BigDecimal variables and count them
        long nonNullOffsetsCount = Stream.of(
                config.getSundayOffsetValueWithTax(), config.getMondayOffsetValueWithTax(), config.getTuesdayOffsetValueWithTax(),
                config.getWednesdayOffsetValueWithTax(), config.getThursdayOffsetValueWithTax(), config.getFridayOffsetValueWithTax(),
                config.getSaturdayOffsetValueWithTax()
        ).filter(Objects::nonNull).count();

        if (nonNullOffsetsCount == 0) {
            return false;
        }
        return nonNullOffsetsCount < 7 && daysBetween != nonNullOffsetsCount;
    }


    private List<CPConfigOffsetAccomType> getSeasonOffsetsList(List<PricingConfigurationOffsetSeasonWrapper> offsetSeasonWrapper) {
        List<CPConfigOffsetAccomType> seasonList = new ArrayList<>();

        for (PricingConfigurationOffsetSeasonWrapper seasonUiWrapper : offsetSeasonWrapper) {
            List<CPConfigOffsetAccomType> seasonOffsetsToSave = findOffsetsToSave(seasonUiWrapper.getOffsets()).stream()
                    .filter(offsetAccomType -> !isBaseOccupancyType(offsetAccomType))
                    .filter(this::shouldBeSaved).collect(Collectors.toList());

            for (CPConfigOffsetAccomType cpConfigOffsetAccomType : seasonOffsetsToSave) {
                cpConfigOffsetAccomType.setStartDate(seasonUiWrapper.getStartDate());
                cpConfigOffsetAccomType.setEndDate(seasonUiWrapper.getEndDate());
                cpConfigOffsetAccomType.setName(seasonUiWrapper.getName());
            }
            seasonList.addAll(seasonOffsetsToSave);
        }

        return seasonList;
    }

    private PricingConfigurationOffsetSeasonWrapper getSeasonWrapperForSeasonToReplace() {
        PricingConfigurationOffsetSeasonWrapper replace = null;
        for (PricingConfigurationOffsetSeasonWrapper offsetSeason : seasons) {
            if (offsetSeason.getStartDate().isBefore(systemDateAsLocalDate)) {
                replace = offsetSeason;
            }
        }
        return replace;
    }

    private PricingConfigurationOffsetSeasonWrapper getSeasonWrapperForPreviousSeason() {
        List<PricingConfigurationOffsetSeasonWrapper> previousSeasons = getSeasons();
        PricingConfigurationOffsetSeasonWrapper previous = null;
        for (PricingConfigurationOffsetSeasonWrapper offsetSeason : seasons) {
            if (offsetSeason.getStartDate().isBefore(systemDateAsLocalDate)) {
                for (PricingConfigurationOffsetSeasonWrapper previousSeason : previousSeasons) {
                    previous = getPrevious(previous, offsetSeason, previousSeason);
                }
            }
        }
        return previous;
    }

    private void updatePreviousSeasonIn(List<PricingConfigurationOffsetSeasonWrapper> seasonsToBeDeleted, List<PricingConfigurationOffsetSeasonWrapper> updatedSeasons) {
        PricingConfigurationOffsetSeasonWrapper replace = getSeasonWrapperForSeasonToReplace();
        PricingConfigurationOffsetSeasonWrapper previous = getSeasonWrapperForPreviousSeason();
        seasonsToBeDeleted.add(replace);
        seasons.remove(replace);
        seasons.add(previous);
        updatedSeasons.add(previous);
    }


    private List<PricingConfigurationOffsetSeasonWrapper> getOverlappingSeasons(PricingConfigurationOffsetSeasonWrapper newSeason, PricingConfigurationOffsetSeasonWrapper pastSeason) {
        List<PricingConfigurationOffsetSeasonWrapper> overlappingSeasons = seasonService.getOverlappingSeasons(seasons, newSeason);
        if (pastSeason != null) {
            return overlappingSeasons.stream().filter(season -> !season.getStartDate().equals(pastSeason.getStartDate())).collect(Collectors.toList());
        }
        return overlappingSeasons;
    }

    private PricingConfigurationOffsetSeasonWrapper updatePastSeasonEndDate(PricingConfigurationOffsetSeasonWrapper season, boolean startDateIsBeforeToday) {
        PricingConfigurationOffsetSeasonWrapper pastSeason = null;
        if (startDateIsBeforeToday) {
            pastSeason = (PricingConfigurationOffsetSeasonWrapper) season.clone();
            pastSeason.setEndDate(systemDateAsLocalDate.minusDays(1));
            seasons.add(pastSeason);

            season.setName(season.getName() + "-" + getText("common.copy") + "-" + DateFormatUtil.formatStandard(systemDateAsLocalDate));
        }
        return pastSeason;
    }

    private PricingConfigurationOffsetSeasonWrapper getPrevious(PricingConfigurationOffsetSeasonWrapper
                                                                        previous, PricingConfigurationOffsetSeasonWrapper offsetSeason, PricingConfigurationOffsetSeasonWrapper
                                                                        previousSeason) {
        if (previousSeason.getStartDate().equals(offsetSeason.getStartDate())) {
            previous = previousSeason;
            previous.setEndDate(offsetSeason.getEndDate());
        }
        return previous;
    }

    public void deleteSeason(PricingConfigurationOffsetSeasonWrapper season) {
        seasonService.removeAndJoin(seasons, season, systemDateAsLocalDate);
        List<CPConfigOffsetAccomType> pricingOffsetAccomTypes = getListOfAffectedOffsets(season);
        pricingConfigurationLTBDEService.enabledLTBDEForOffsetDeleteIfApplicable(pricingOffsetAccomTypes,
                JavaDateUtil.convertJodaLocalDateToJavaLocalDate(season.getEndDate()), dateService.getCaughtUpJavaLocalDate());

        if (seasons.contains(season)) {
            offsetService.updateOffsetsForSeason(pricingOffsetAccomTypes, excludedPricingAccomClasses);
        } else {
            offsetService.deleteSingleSeasonOffsets(pricingOffsetAccomTypes, excludedPricingAccomClasses);
        }

        init(false);
    }

    private List<CPConfigOffsetAccomType> getListOfAffectedOffsets(PricingConfigurationOffsetSeasonWrapper offsetSeasonWrapper) {

        List<CPConfigOffsetAccomType> offsetAccomTypeList = new ArrayList<>();

        List<CPConfigOffsetAccomType> seasonOffsets = findOffsetsToSave(offsetSeasonWrapper.getOffsets()).stream().filter(offsetAccomType -> !isBaseOccupancyType(offsetAccomType)).collect(Collectors.toList());
        seasonOffsets = seasonOffsets.stream().filter(this::shouldBeSaved).collect(Collectors.toList());

        for (CPConfigOffsetAccomType cpConfigOffsetAccomType : seasonOffsets) {
            cpConfigOffsetAccomType.setStartDate(offsetSeasonWrapper.getStartDate());
            cpConfigOffsetAccomType.setEndDate(offsetSeasonWrapper.getEndDate());
            cpConfigOffsetAccomType.setName(offsetSeasonWrapper.getName());
        }

        offsetAccomTypeList.addAll(seasonOffsets);

        return offsetAccomTypeList;
    }

    private void updateSeasonsTable() {
        view.resetFilters();
        view.populateSeasons(getSeasonsOnChangeEvent(SeasonsFilterBar.SeasonsFilterBarType.PAST, false));
    }

    public List<PricingConfigurationOffsetSeasonWrapper> getExistingSeasons() {
        return seasons;
    }

    public boolean validateOffsetSeasons(PricingConfigurationOffsetSeasonWrapper season) {
        boolean isValid = true;
        List<CPConfigOffsetAccomType> offsets = new ArrayList<>();
        season.getOffsets()
                .stream()
                .forEach(classOffset -> classOffset
                        .getChildren()
                        .stream()
                        .forEach(typeOffset -> {
                            offsets.add(typeOffset.getCpConfigOffsetAccomType());
                            offsets.addAll(typeOffset
                                    .getChildren()
                                    .stream()
                                    .map(PricingConfigurationOffsetWrapper::getCpConfigOffsetAccomType)
                                    .collect(Collectors.toList()));
                        }));

        String warningMessage = null;
        String pppOneAdultWarningMessage = null;
        String pppAdultHierarchyWarningMessage = null;
        String pppChildHierarchyWarningMessage = null;
        String pppChildAgeHierarchyWarningMessage = null;
        String pppChildNegativeWarningMessage = null;

        TetrisConfirmationWindow window = new TetrisConfirmationWindow();

        VerticalLayout layout = new VerticalLayout();
        window.setInnerContent(layout);

        window.setCaption(getText(WINDOW_CAPTION_REVIEW_CONFIGURATION));
        window.setYesLabel(getText(WINDOW_YES_LABEL_OK));
        window.setYesClickListener(clickEvent -> window.close());
        window.setNoButtonVisible(false);

        if (isContinuousPricingEnabled && !isAdvancedPriceRankingEnabled) {
            if (accomClassPriceRank != null) {
                accomClassPriceRank = accomClassPriceRankService.getAccomClassPriceRank();
            }
            Set<String> roomClassesInViolation = pricingConfigurationValidationService.validateSaveSeasonOffsets(offsets, season.getStartDate(), season.getEndDate(), accomClassPriceRank, pricingAccomClasses, baseOccupancyType);
            if (CollectionUtils.isNotEmpty(roomClassesInViolation)) {
                warningMessage = String.join("\n", roomClassesInViolation);
            }
        }

        if (isContinuousPricingEnabled && isPerPersonPricingEnabled) {
            Set<String> roomClassesWithOneAdultPositiveViolation = validateOneAdultOffsets(offsets);
            if (CollectionUtils.isNotEmpty(roomClassesWithOneAdultPositiveViolation)) {
                pppOneAdultWarningMessage = String.join(", ", roomClassesWithOneAdultPositiveViolation);
            }

            Set<String> roomClassesWithAdultHierarchyViolation = validateAdultHierarchyOffsets(offsets);
            if (CollectionUtils.isNotEmpty(roomClassesWithAdultHierarchyViolation)) {
                pppAdultHierarchyWarningMessage = String.join(", ", roomClassesWithAdultHierarchyViolation);
            }

            Set<String> roomClassesWithChildHierarchyViolation = validateChildHierarchyOffsets(offsets);
            if (CollectionUtils.isNotEmpty(roomClassesWithChildHierarchyViolation)) {
                pppChildHierarchyWarningMessage = String.join(", ", roomClassesWithChildHierarchyViolation);
            }

            Set<String> roomClassesWithChildAgeHierarchyViolation = validateChildAgeHierarchyOffsets(offsets);
            if (CollectionUtils.isNotEmpty(roomClassesWithChildAgeHierarchyViolation)) {
                pppChildAgeHierarchyWarningMessage = String.join(", ", roomClassesWithChildAgeHierarchyViolation);
            }

            Set<String> roomClassesWithChildNegativeViolation = validateChildNegativeOffsets(offsets);
            if (CollectionUtils.isNotEmpty(roomClassesWithChildNegativeViolation)) {
                pppChildNegativeWarningMessage = String.join(", ", roomClassesWithChildNegativeViolation);
            }
        }

        if (warningMessage != null) {
            showWarning(getText(BREAKS_ROOM_CLASS_RANK) + "\n" + warningMessage);
            isValid = false;
        } else if (pppChildNegativeWarningMessage != null) {
            showWarning(getText(BREAKS_CHILD_NEGATIVE_OFFSET, pppChildNegativeWarningMessage));
            isValid = false;
        } else if (pppOneAdultWarningMessage != null) {
            layout.addComponent(new Label(getText(BREAKS_ONE_ADULT_OFFSET, pppOneAdultWarningMessage)));
            window.show();
        } else if (pppAdultHierarchyWarningMessage != null) {
            layout.addComponent(new Label(getText(BREAKS_ADULT_OFFSET_HIERARCHY, pppAdultHierarchyWarningMessage)));
            window.show();
        } else if (pppChildHierarchyWarningMessage != null) {
            layout.addComponent(new Label(getText(BREAKS_CHILD_OFFSET_HIERARCHY, pppChildHierarchyWarningMessage)));
            window.show();
        } else if (pppChildAgeHierarchyWarningMessage != null) {
            layout.addComponent(new Label(getText(BREAKS_CHILD_AGE_OFFSET_HIERARCHY, pppChildAgeHierarchyWarningMessage)));
            window.show();
        }

        return isValid;
    }

    public Optional<String> validateOffsetSeasonsHavingSoftWarning(PricingConfigurationOffsetSeasonWrapper season) {
        Optional<String> isValidMessage = Optional.empty();
        List<CPConfigOffsetAccomType> offsets = new ArrayList<>();
        season.getOffsets()
                .stream()
                .forEach(classOffset -> classOffset
                        .getChildren()
                        .stream()
                        .forEach(typeOffset -> {
                            offsets.add(typeOffset.getCpConfigOffsetAccomType());
                            offsets.addAll(typeOffset
                                    .getChildren()
                                    .stream()
                                    .map(PricingConfigurationOffsetWrapper::getCpConfigOffsetAccomType)
                                    .collect(Collectors.toList()));
                        }));

        String warningMessage = null;
        String pppOneAdultWarningMessage = null;
        String pppAdultHierarchyWarningMessage = null;
        String pppChildHierarchyWarningMessage = null;
        String pppChildAgeHierarchyWarningMessage = null;
        String pppChildNegativeWarningMessage = null;

        TetrisConfirmationWindow window = new TetrisConfirmationWindow();

        VerticalLayout layout = new VerticalLayout();
        window.setInnerContent(layout);

        window.setCaption(getText(WINDOW_CAPTION_REVIEW_CONFIGURATION));
        window.setYesLabel(getText(WINDOW_YES_LABEL_OK));
        window.setYesClickListener(clickEvent -> window.close());
        window.setNoButtonVisible(false);

        if (isContinuousPricingEnabled && !isAdvancedPriceRankingEnabled) {

            if (accomClassPriceRank != null) {
                accomClassPriceRank = accomClassPriceRankService.getAccomClassPriceRank();
            }
            Set<String> roomClassesInViolation = null;
            if (isCPFloorAndCeilingSoftWarningByPricingRuleTypeEnable()) {
                // if dailyBarPricingRuleType is 0 or 1 then skip the validation and if it is 2 or 3 do the corresponding validation.
                if (getDailyBarPricingRuleType().equals(DAILY_BAR_PRICING_RULE_TYPE_TWO) || getDailyBarPricingRuleType().equals(DAILY_BAR_PRICING_RULE_TYPE_THREE)) {
                    roomClassesInViolation = pricingConfigurationValidationService.validateSaveSeasonOffsetsByDailyPricingBarRuleType(offsets, season.getStartDate(), season.getEndDate(), accomClassPriceRank, pricingAccomClasses, baseOccupancyType);
                }
            } else {
                roomClassesInViolation = pricingConfigurationValidationService.validateSaveSeasonOffsets(offsets, season.getStartDate(), season.getEndDate(), accomClassPriceRank, pricingAccomClasses, baseOccupancyType);
            }
            if (CollectionUtils.isNotEmpty(roomClassesInViolation)) {
                warningMessage = String.join("\n", roomClassesInViolation);
            }
        }

        if (isContinuousPricingEnabled && isPerPersonPricingEnabled) {
            Set<String> roomClassesWithOneAdultPositiveViolation = validateOneAdultOffsets(offsets);
            if (CollectionUtils.isNotEmpty(roomClassesWithOneAdultPositiveViolation)) {
                pppOneAdultWarningMessage = String.join(", ", roomClassesWithOneAdultPositiveViolation);
            }

            Set<String> roomClassesWithAdultHierarchyViolation = validateAdultHierarchyOffsets(offsets);
            if (CollectionUtils.isNotEmpty(roomClassesWithAdultHierarchyViolation)) {
                pppAdultHierarchyWarningMessage = String.join(", ", roomClassesWithAdultHierarchyViolation);
            }

            Set<String> roomClassesWithChildHierarchyViolation = validateChildHierarchyOffsets(offsets);
            if (CollectionUtils.isNotEmpty(roomClassesWithChildHierarchyViolation)) {
                pppChildHierarchyWarningMessage = String.join(", ", roomClassesWithChildHierarchyViolation);
            }

            Set<String> roomClassesWithChildAgeHierarchyViolation = validateChildAgeHierarchyOffsets(offsets);
            if (CollectionUtils.isNotEmpty(roomClassesWithChildAgeHierarchyViolation)) {
                pppChildAgeHierarchyWarningMessage = String.join(", ", roomClassesWithChildAgeHierarchyViolation);
            }

            Set<String> roomClassesWithChildNegativeViolation = validateChildNegativeOffsets(offsets);
            if (CollectionUtils.isNotEmpty(roomClassesWithChildNegativeViolation)) {
                pppChildNegativeWarningMessage = String.join(", ", roomClassesWithChildNegativeViolation);
            }
        }

        if (pppChildNegativeWarningMessage != null) {
            showWarning(getText(BREAKS_CHILD_NEGATIVE_OFFSET, pppChildNegativeWarningMessage));
            isValidMessage = Optional.of(getText(BREAKS_CHILD_NEGATIVE_OFFSET, pppChildNegativeWarningMessage));
        } else if (pppOneAdultWarningMessage != null) {
            layout.addComponent(new Label(getText(BREAKS_ONE_ADULT_OFFSET, pppOneAdultWarningMessage)));
            window.show();
        } else if (pppAdultHierarchyWarningMessage != null) {
            layout.addComponent(new Label(getText(BREAKS_ADULT_OFFSET_HIERARCHY, pppAdultHierarchyWarningMessage)));
            window.show();
        } else if (pppChildHierarchyWarningMessage != null) {
            layout.addComponent(new Label(getText(BREAKS_CHILD_OFFSET_HIERARCHY, pppChildHierarchyWarningMessage)));
            window.show();
        } else if (pppChildAgeHierarchyWarningMessage != null) {
            layout.addComponent(new Label(getText(BREAKS_CHILD_AGE_OFFSET_HIERARCHY, pppChildAgeHierarchyWarningMessage)));
            window.show();
        } else if (warningMessage != null) {
            // isValidMessage=Optional.of(getText(BREAKS_ROOM_CLASS_RANK) + "\n" + warningMessage);
            if (isCPFloorAndCeilingSoftWarningByPricingRuleTypeEnable()) {
                isValidMessage = Optional.of(getWarningMessageByDailyBarPricingRuleType(warningMessage, getDailyBarPricingRuleType()));
            } else {
                isValidMessage = Optional.of(getText("breaksRoomClassRank") + "\n" + warningMessage);
            }
        }
        return isValidMessage;
    }

    protected Set<String> validateOneAdultOffsets(List<CPConfigOffsetAccomType> offsets) {
        Set<String> roomClassesWithOffsetViolation = new HashSet<>();

        for (CPConfigOffsetAccomType offset : offsets) {
            //Check 1 Adult offset if value > 0 since 2 Adult is the base occupancy
            if (!offset.containsNullDayOfWeekValues() &&
                    OccupancyType.SINGLE.equals(offset.getOccupancyType()) &&
                    (offset.getMondayOffsetValue().compareTo(BigDecimal.ZERO) > 0 ||
                            offset.getTuesdayOffsetValue().compareTo(BigDecimal.ZERO) > 0 ||
                            offset.getWednesdayOffsetValue().compareTo(BigDecimal.ZERO) > 0 ||
                            offset.getThursdayOffsetValue().compareTo(BigDecimal.ZERO) > 0 ||
                            offset.getFridayOffsetValue().compareTo(BigDecimal.ZERO) > 0 ||
                            offset.getSaturdayOffsetValue().compareTo(BigDecimal.ZERO) > 0 ||
                            offset.getSundayOffsetValue().compareTo(BigDecimal.ZERO) > 0)) {
                roomClassesWithOffsetViolation.add(offset.getAccomType().getName());
            }
        }

        return roomClassesWithOffsetViolation;
    }

    protected Set<String> validateAdultHierarchyOffsets(List<CPConfigOffsetAccomType> offsets) {
        Set<String> roomClassesWithOffsetViolation = new HashSet<>();

        for (CPConfigOffsetAccomType offset : offsets) {
            if (!offset.containsNullDayOfWeekValues() && OccupancyType.isAdultBucket(offset.getOccupancyType())) {
                for (CPConfigOffsetAccomType comparisonOffset : offsets) {
                    if (!comparisonOffset.containsNullDayOfWeekValues() &&
                            offset.getAccomType().equals(comparisonOffset.getAccomType()) &&
                            OccupancyType.isAdultBucket(comparisonOffset.getOccupancyType()) &&
                            comparisonOffset.getOccupancyType().getId() > offset.getOccupancyType().getId() &&
                            !roomClassesWithOffsetViolation.contains(offset.getAccomType().getName()) &&
                            !perPersonPricingOffsetCheck(offset, comparisonOffset)) {
                        roomClassesWithOffsetViolation.add(offset.getAccomType().getName());
                    }
                }
            }
        }

        return roomClassesWithOffsetViolation;
    }

    protected Set<String> validateChildHierarchyOffsets(List<CPConfigOffsetAccomType> offsets) {
        Set<String> roomClassesWithOffsetViolation = new HashSet<>();

        for (CPConfigOffsetAccomType offset : offsets) {
            if (!offset.containsNullDayOfWeekValues() && OccupancyType.isChildQuantityBucket(offset.getOccupancyType())) {
                for (CPConfigOffsetAccomType comparisonOffset : offsets) {
                    if (!comparisonOffset.containsNullDayOfWeekValues() &&
                            offset.getAccomType().equals(comparisonOffset.getAccomType()) &&
                            OccupancyType.isChildQuantityBucket(comparisonOffset.getOccupancyType()) &&
                            comparisonOffset.getOccupancyType().getId() > offset.getOccupancyType().getId() &&
                            !roomClassesWithOffsetViolation.contains(offset.getAccomType().getName()) &&
                            !perPersonPricingOffsetCheck(offset, comparisonOffset)) {
                        roomClassesWithOffsetViolation.add(offset.getAccomType().getName());
                    }
                }
            }
        }

        return roomClassesWithOffsetViolation;
    }

    protected Set<String> validateChildAgeHierarchyOffsets(List<CPConfigOffsetAccomType> offsets) {
        Set<String> roomClassesWithOffsetViolation = new HashSet<>();

        for (CPConfigOffsetAccomType offset : offsets) {
            if (!offset.containsNullDayOfWeekValues() && OccupancyType.isChildBucket(offset.getOccupancyType())) {
                for (CPConfigOffsetAccomType comparisonOffset : offsets) {
                    if (!comparisonOffset.containsNullDayOfWeekValues() &&
                            offset.getAccomType().equals(comparisonOffset.getAccomType()) &&
                            OccupancyType.isChildBucket(comparisonOffset.getOccupancyType()) &&
                            comparisonOffset.getOccupancyType().getId() > offset.getOccupancyType().getId() &&
                            !roomClassesWithOffsetViolation.contains(offset.getAccomType().getName()) &&
                            !perPersonPricingOffsetCheck(offset, comparisonOffset)) {
                        roomClassesWithOffsetViolation.add(offset.getAccomType().getName());
                    }
                }
            }
        }

        return roomClassesWithOffsetViolation;
    }

    protected Set<String> validateChildNegativeOffsets(List<CPConfigOffsetAccomType> offsets) {
        Set<String> roomClassesWithOffsetViolation = new HashSet<>();

        for (CPConfigOffsetAccomType offset : offsets) {
            if (!offset.containsNullDayOfWeekValues() &&
                    (OccupancyType.isChildBucket(offset.getOccupancyType()) || OccupancyType.isChildQuantityBucket(offset.getOccupancyType())) &&
                    (offset.getMondayOffsetValue().compareTo(BigDecimal.ZERO) < 0 ||
                            offset.getTuesdayOffsetValue().compareTo(BigDecimal.ZERO) < 0 ||
                            offset.getWednesdayOffsetValue().compareTo(BigDecimal.ZERO) < 0 ||
                            offset.getThursdayOffsetValue().compareTo(BigDecimal.ZERO) < 0 ||
                            offset.getFridayOffsetValue().compareTo(BigDecimal.ZERO) < 0 ||
                            offset.getSaturdayOffsetValue().compareTo(BigDecimal.ZERO) < 0 ||
                            offset.getSundayOffsetValue().compareTo(BigDecimal.ZERO) < 0)) {
                roomClassesWithOffsetViolation.add(offset.getAccomType().getName());
            }
        }

        return roomClassesWithOffsetViolation;
    }

    protected boolean perPersonPricingOffsetCheck(CPConfigOffsetAccomType offset, CPConfigOffsetAccomType
            higherOffset) {
        return (offset.getMondayOffsetValue().compareTo(higherOffset.getMondayOffsetValue()) <= 0 &&
                offset.getTuesdayOffsetValue().compareTo(higherOffset.getTuesdayOffsetValue()) <= 0 &&
                offset.getWednesdayOffsetValue().compareTo(higherOffset.getWednesdayOffsetValue()) <= 0 &&
                offset.getThursdayOffsetValue().compareTo(higherOffset.getThursdayOffsetValue()) <= 0 &&
                offset.getFridayOffsetValue().compareTo(higherOffset.getFridayOffsetValue()) <= 0 &&
                offset.getSaturdayOffsetValue().compareTo(higherOffset.getSaturdayOffsetValue()) <= 0 &&
                offset.getSundayOffsetValue().compareTo(higherOffset.getSundayOffsetValue()) <= 0);
    }

    @ForTesting
    public void setGroupPricing(Boolean groupPricing) {
        isGroupPricing = groupPricing;
    }

    public Boolean getIsGroupPricing() {
        return isGroupPricing;
    }

    public String getHelpId() {
        if (isFunctionSpace()) {
            return OFFSETS_FUNCTION_SPACE_HELP_ID;
        } else {
            return OFFSETS_GROUP_HELP_ID;
        }
    }

    public List<CPConfigOffsetAccomType> getAllOffsets() {
        return pricingConfigurationService.findAllOffsets();
    }

    public boolean isPerPersonPricingEnabled() {
        return isPerPersonPricingEnabled;
    }

    public String makeChildBucketString(OccupancyType type) {
        Optional<OccupantBucketEntity> currentEntity = occupantBucketEntities.stream()
                .filter(occupantBucketEntity -> occupantBucketEntity.getOccupancyType().equals(type))
                .findFirst();
        return currentEntity.isPresent() ? currentEntity.get().makeChildBucketString() : "";
    }

    public boolean isRowNotEditable(PricingConfigurationOffsetWrapper wrapper) {
        return wrapper.isBaseRoomType() && baseOccupancyType.equals(wrapper.getCpConfigOffsetAccomType().getOccupancyType());
    }

    public boolean isMaximumOccupancyConfigured() {
        return CollectionUtils.isNotEmpty(maximumOccupantsEntities);
    }

    public boolean isOccupantValueGroupingAvailable() {
        return isPerPersonPricingEnabled || isChildAgeBucketsEnabled;
    }

    @ForTesting
    public void setSeasons(List<PricingConfigurationOffsetSeasonWrapper> seasons) {
        this.seasons = seasons;
    }

    @VisibleForTesting
    void filterOffsetsAgainstDiscontinuedAccomTypes(LinkedHashMap<PricingAccomClass, LinkedHashMap<AccomType, LinkedList<CPConfigOffsetAccomType>>> allOffsets) {
        if (isRoomTypeRecodingUIEnabled) {
            allOffsets.replaceAll((pricingAccomClass, accomTypeLinkedListLinkedHashMap) -> accomTypeLinkedListLinkedHashMap.entrySet()
                    .stream()
                    .filter(accomTypeLinkedListEntry -> accomTypeLinkedListEntry.getKey().getDisplayStatusId().equals(Status.ACTIVE.getId()))
                    .collect(LinkedHashMap::new, (map, e) -> map.put(e.getKey(), e.getValue()), LinkedHashMap::putAll)
            );
        }
    }

    public List<PricingConfigurationOffsetSeasonWrapper> getSeasonsOnChangeEvent(SeasonsFilterBar.SeasonsFilterBarType type, boolean value) {
        return seasons.stream().peek(season -> updateVisibility(season, type, value)).filter(PricingConfigurationOffsetSeasonWrapper::isVisible).collect(Collectors.toList());
    }

    private void updateVisibility(PricingConfigurationOffsetSeasonWrapper season, SeasonsFilterBar.SeasonsFilterBarType type, boolean value) {
        if (isRowOfType(season, type)) {
            season.setVisible(value);
        }
    }

    private boolean isRowOfType(PricingConfigurationOffsetSeasonWrapper season, SeasonsFilterBar.SeasonsFilterBarType type) {
        switch (type) {
            case PAST:
                return season.getEndDate().compareTo(systemDateAsLocalDate) < 0;
            case FUTURE:
                return season.getStartDate().compareTo(systemDateAsLocalDate) > 0;
            case PRESENT:
                return season.getStartDate().compareTo(systemDateAsLocalDate) <= 0
                        && season.getEndDate().compareTo(systemDateAsLocalDate) >= 0;
        }
        return false;
    }

    @ForTesting
    public void setBaseOccupancyType(OccupancyType baseOccupancyType) {
        this.baseOccupancyType = baseOccupancyType;
    }

    @ForTesting
    public void setAdvancedPriceRankingEnabled(Boolean advancedPriceRankingEnabled) {
        isAdvancedPriceRankingEnabled = advancedPriceRankingEnabled;
    }

    @ForTesting
    public void setChildAgeBucketsEnabled(Boolean childAgeBucketsEnabled) {
        isChildAgeBucketsEnabled = childAgeBucketsEnabled;
    }

    @ForTesting
    public void setRoomTypeRecodingUIEnabled(Boolean roomTypeRecodingUIEnabled) {
        isRoomTypeRecodingUIEnabled = roomTypeRecodingUIEnabled;
    }

    @ForTesting
    public void setPerPersonPricingEnabled(Boolean perPersonPricingEnabled) {
        isPerPersonPricingEnabled = perPersonPricingEnabled;
    }

    @ForTesting
    public void setPricingAccomClasses(List<PricingAccomClass> pricingAccomClasses) {
        this.pricingAccomClasses = pricingAccomClasses;
    }

    @ForTesting
    public void setCpConfigOffsetAccomTypes(List<CPConfigOffsetAccomType> cpConfigOffsetAccomTypes) {
        this.cpConfigOffsetAccomTypes = cpConfigOffsetAccomTypes;
    }

    public List<PricingAccomClass> getExcludedPricingAccomClasses() {
        return excludedPricingAccomClasses;
    }

    public List<String> getBaseRoomTypes() {
        return baseRoomTypes;
    }

    public List<OccupantBucketEntity> getOccupantBucketEntities() {
        return occupantBucketEntities;
    }

    public OccupancyType getBaseOccupancyType() {
        return baseOccupancyType;
    }

    public boolean isSeasonValidComparingSupplementsForPriceExcludedValues(PricingConfigurationOffsetSeasonWrapper season) {
        final LocalDate seasonStartDate = season.getStartDate();
        final LocalDate seasonEndDate = season.getEndDate();
        final List<PricingConfigurationOffsetWrapper> priceExcludedRoomClasses = season.getOffsets().stream().filter(offset -> offset.isPriceExcluded()).collect(Collectors.toList());
        return priceExcludedRoomClasses.stream().allMatch(priceExcludedRoomClass -> childrenWrappersValidForSupplements(seasonStartDate, seasonEndDate, priceExcludedRoomClass.getChildren()));
    }

    private boolean childrenWrappersValidForSupplements(LocalDate seasonStartDate, LocalDate seasonEndDate, List<PricingConfigurationOffsetWrapper> children) {
        return children.stream().allMatch(wrapper -> isWrapperValidForSupplementRestriction(seasonStartDate, seasonEndDate, wrapper));
    }

    @VisibleForTesting
    public boolean isWrapperValidForSupplementRestriction(LocalDate seasonStartDate, LocalDate seasonEndDate, PricingConfigurationOffsetWrapper wrapper) {
        final List<AccomTypeSupplement> accomTypeSupplementSeasons = fixedValueAccomTypeSupplements.stream()
                .filter(supplement -> supplement.getAccomType().equals(wrapper.getAccomType()))
                .filter(supplement -> supplement.getStartDate() != null && supplement.getEndDate() != null)
                .filter(supplement -> isDateBetweenRange(seasonStartDate, seasonEndDate, supplement.getStartDate()) ||
                        isDateBetweenRange(seasonStartDate, seasonEndDate, supplement.getEndDate()))
                .collect(Collectors.toList());

        final AccomTypeSupplement accomTypeSupplementDefault = fixedValueAccomTypeSupplements
                .stream()
                .filter(supplement -> supplement.getAccomType().equals(wrapper.getAccomType()))
                .filter(supplement -> supplement.getStartDate() == null)
                .findAny()
                .orElse(getAccomTypeSupplementSetToZero());
        if (CollectionUtils.isNotEmpty(accomTypeSupplementSeasons)) {
            return accomTypeSupplementSeasons.stream().allMatch(accomTypeSupplement -> isPriceExcludedSeasonValid(wrapper, accomTypeSupplement, accomTypeSupplementDefault));
        } else {
            return isPriceExcludedSeasonValid(wrapper, accomTypeSupplementDefault, accomTypeSupplementDefault);
        }
    }

    //This is used as a default because we don't save records for Supplements that are all Zeros
    private AccomTypeSupplement getAccomTypeSupplementSetToZero() {
        AccomTypeSupplement accomTypeSupplement = new AccomTypeSupplement();
        accomTypeSupplement.setSundaySupplementValue(BigDecimal.ZERO);
        accomTypeSupplement.setMondaySupplementValue(BigDecimal.ZERO);
        accomTypeSupplement.setTuesdaySupplementValue(BigDecimal.ZERO);
        accomTypeSupplement.setWednesdaySupplementValue(BigDecimal.ZERO);
        accomTypeSupplement.setThursdaySupplementValue(BigDecimal.ZERO);
        accomTypeSupplement.setFridaySupplementValue(BigDecimal.ZERO);
        accomTypeSupplement.setSaturdaySupplementValue(BigDecimal.ZERO);
        return accomTypeSupplement;
    }

    /**
     * Offset season should not span for multiple taxes.
     *
     * @return true when season is valid means it completely overlaps with a season or it uses default tax
     * false when season overlap partially with one or more seasons
     */
    public boolean validateSeasonForMultipleTaxes(PricingConfigurationOffsetSeasonWrapper seasonToValidate) {
        List<Tax> overlappingTaxSeasons = taxService.getPartialOverlappingTaxSeasons(seasonToValidate.getStartDate(), seasonToValidate.getEndDate());
        if (CollectionUtils.isNotEmpty(overlappingTaxSeasons)) {
            InvalidSeasonDatesLayout layout = new InvalidSeasonDatesLayout(overlappingTaxSeasons, getText("common.offset"));
            layout.showWarningAboutSeasonConsumingMultipleTaxes();
        }
        return CollectionUtils.isEmpty(overlappingTaxSeasons);
    }


    @VisibleForTesting
    public boolean isDateBetweenRange(LocalDate startDate, LocalDate endDate, LocalDate comparedDate) {
        return comparedDate.isEqual(startDate) ||
                comparedDate.isEqual(endDate) ||
                (comparedDate.isAfter(startDate) && comparedDate.isBefore(endDate));
    }

    @VisibleForTesting
    public boolean isPriceExcludedSeasonValid(PricingConfigurationOffsetWrapper wrapper, AccomTypeSupplement accomTypeSupplementSeason, AccomTypeSupplement accomTypeSupplementDefault) {
        return isSeasonPriceExcludedRateGreaterThanSupplement(wrapper.getCpConfigOffsetAccomType().getSundayOffsetValueWithTax(), accomTypeSupplementSeason.getSundaySupplementValue(), accomTypeSupplementDefault.getSundaySupplementValue()) &&
                isSeasonPriceExcludedRateGreaterThanSupplement(wrapper.getCpConfigOffsetAccomType().getMondayOffsetValueWithTax(), accomTypeSupplementSeason.getMondaySupplementValue(), accomTypeSupplementDefault.getMondaySupplementValue()) &&
                isSeasonPriceExcludedRateGreaterThanSupplement(wrapper.getCpConfigOffsetAccomType().getTuesdayOffsetValueWithTax(), accomTypeSupplementSeason.getTuesdaySupplementValue(), accomTypeSupplementDefault.getTuesdaySupplementValue()) &&
                isSeasonPriceExcludedRateGreaterThanSupplement(wrapper.getCpConfigOffsetAccomType().getWednesdayOffsetValueWithTax(), accomTypeSupplementSeason.getWednesdaySupplementValue(), accomTypeSupplementDefault.getWednesdaySupplementValue()) &&
                isSeasonPriceExcludedRateGreaterThanSupplement(wrapper.getCpConfigOffsetAccomType().getThursdayOffsetValueWithTax(), accomTypeSupplementSeason.getThursdaySupplementValue(), accomTypeSupplementDefault.getThursdaySupplementValue()) &&
                isSeasonPriceExcludedRateGreaterThanSupplement(wrapper.getCpConfigOffsetAccomType().getFridayOffsetValueWithTax(), accomTypeSupplementSeason.getFridaySupplementValue(), accomTypeSupplementDefault.getFridaySupplementValue()) &&
                isSeasonPriceExcludedRateGreaterThanSupplement(wrapper.getCpConfigOffsetAccomType().getSaturdayOffsetValueWithTax(), accomTypeSupplementSeason.getSaturdaySupplementValue(), accomTypeSupplementDefault.getSaturdaySupplementValue());
    }

    @VisibleForTesting
    public boolean isSeasonPriceExcludedRateGreaterThanSupplement(BigDecimal floorRate, BigDecimal supplement, BigDecimal defaultSupplementRate) {
        if (floorRate == null) {
            return true;
        } else if (supplement == null && defaultSupplementRate != null) {
            return floorRate.compareTo(defaultSupplementRate) > 0;
        } else if (supplement != null) {
            return floorRate.compareTo(supplement) > 0;
        } else {
            return true;
        }
    }

    @ForTesting
    public void setFixedValueAccomTypeSupplements(List<AccomTypeSupplement> fixedValueAccomTypeSupplements) {
        this.fixedValueAccomTypeSupplements = fixedValueAccomTypeSupplements;
    }

    public boolean isSupplementsEnabled() {
        if (null == isSupplementsEnabled) {
            isSupplementsEnabled = pricingConfigurationService.isSupplementEnabled();
        }
        return isSupplementsEnabled;
    }

    public boolean isDateBeforeSystemDate(LocalDate date) {
        return date != null ? date.isBefore(systemDateAsLocalDate) : false;
    }

    public Integer getDailyBarPricingRuleType() {
        return pacmanConfigParamsService.getIntegerParameterValue(IPConfigParamName.DAILY_BAR_PRICING_RULE_TYPE.value());
    }

    private String getWarningMessageByDailyBarPricingRuleType(String warningMessage, Integer dailyBarPricingRuleType) {
        StringBuilder messageBuilder = new StringBuilder();
        messageBuilder.append(getText("breaksRoomClassRankBydailyBarPricingRuleTypePreMsg") + "<BR/>");
        messageBuilder.append(warningMessage.replace("\n", "<BR/>"));
        messageBuilder.append("<BR/>");
        if (dailyBarPricingRuleType.equals(DAILY_BAR_PRICING_RULE_TYPE_TWO)) {
            messageBuilder.append(getText("breaksRoomClassRankBydailyBarPricingRuleType2Msg"));
        } else if (dailyBarPricingRuleType.equals(DAILY_BAR_PRICING_RULE_TYPE_THREE)) {
            messageBuilder.append(getText("breaksRoomClassRankBydailyBarPricingRuleType3Msg"));
        }
        messageBuilder.append("<BR/>" + getText("breaksRoomClassRankBydailyBarPricingRuleTypePostMsg"));
        return messageBuilder.toString();
    }

    public PricingConfigurationOffsetSeasonWrapper getDefaultSeasonPopulationWrapper() {
        PricingConfigurationOffsetSeasonWrapper wrapper = new PricingConfigurationOffsetSeasonWrapper();
        wrapper.setName(getText("DEFAULT"));
        wrapper.setOffsets(getDefaultOffsets(true));
        return wrapper;
    }

    public PricingConfigurationDTO getPricingConfigurationDTO() {
        return pricingConfigurationDTO;
    }

    public int getPricingConfigurationDTOProductID() {
        return pricingConfigurationDTO.getIndependentProductDTO() != null ? pricingConfigurationDTO.getProduct().getId() : 1;
    }

    @ForTesting
    public void setPricingConfigurationDTO(PricingConfigurationDTO dto) {
        this.pricingConfigurationDTO = dto;
    }

    public List<AccomType> getRoomTypesMappedToProduct() {
        if (isIndependentProductsEnabled && pricingConfigurationDTO.getIndependentProductDTO() != null && pricingConfigurationDTO.isIndependentProduct()) {
            return pricingConfigurationService.getRoomTypesForProduct(pricingConfigurationDTO.getProduct());
        }
        return null;
    }

    @ForTesting
    public void setIndependentProductsEnabled(boolean isIndependentProductsEnabled) {
        this.isIndependentProductsEnabled = isIndependentProductsEnabled;
    }

    public Boolean isIndependentProductsEnabled() {
        return isIndependentProductsEnabled;
    }

    public boolean isEffectiveReadOnlyAndMissingRoomTypeAlertNotOpen() {
        return !isMissingRoomTypeAlertOpen && isEffectiveReadOnly();
    }

    public boolean isContinuousPricingEnabled() {
        return isContinuousPricingEnabled;
    }

    public boolean isOffsetsUploadEnabled() {
        return pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.OFFSETS_UPLOAD_ENABLED);
    }

    public void setLogger(Logger logger) {
        this.logger = logger;
    }

    public boolean hasOneYearHistoricalData() {
       return pricingConfigurationService.hasOneYearHistoricalData();
    }
    public boolean isPPPOffsetSuggestEnabled() {
        boolean pppOffsetToggleValue = pricingConfigurationService.isPPPOffsetSuggestEnabled();
        return pppOffsetToggleValue;
    }

    public void onSuggestClick() {
        view.showWarning(getText("offset.suggest.incremntal.change.popup"));
    }
}
