package com.ideas.tetris.ui.modules.extendedstayrateconfiguration;

import com.ideas.infra.tetris.security.domain.TetrisPermissionKey;
import com.ideas.tetris.pacman.services.extendedstayrateshopping.entity.ExtendedStayCompetitorMapping;
import com.ideas.tetris.ui.common.TetrisUi;
import com.ideas.tetris.ui.common.cdi.TetrisNavigatorView;
import com.ideas.tetris.ui.common.component.TetrisBeanItemContainer;
import com.ideas.tetris.ui.common.component.TetrisLabel;
import com.ideas.tetris.ui.common.component.TetrisNotification;
import com.ideas.tetris.ui.common.component.TetrisSaveCancelButtonBar;
import com.ideas.tetris.ui.common.component.checkbox.TetrisCheckBox;
import com.ideas.tetris.ui.common.component.label.TetrisSpacer;
import com.ideas.tetris.ui.common.component.table.TetrisChangeAwareComponentColumnGenerator;
import com.ideas.tetris.ui.common.component.table.TetrisChangeAwareTable;
import com.ideas.tetris.ui.common.component.textfield.TetrisBigDecimalField;
import com.ideas.tetris.ui.common.data.util.converter.StringToBigDecimalConverter;
import com.ideas.tetris.ui.common.data.validator.BigDecimalRangeValidator;
import com.ideas.tetris.ui.common.util.ChangeAware;
import com.ideas.tetris.ui.common.util.HelpId;
import com.ideas.tetris.ui.common.util.TetrisTheme;
import com.ideas.tetris.ui.common.util.TitleLocalized;
import com.ideas.tetris.ui.common.util.UiUtils;
import com.vaadin.ui.AbstractOrderedLayout;
import com.vaadin.ui.Alignment;
import com.vaadin.ui.Button;
import com.vaadin.ui.JavaScript;
import com.vaadin.ui.JavaScriptFunction;
import com.vaadin.v7.data.Property;
import com.vaadin.v7.data.validator.RangeValidator;
import com.vaadin.v7.shared.ui.label.ContentMode;
import com.vaadin.v7.ui.HorizontalLayout;
import com.vaadin.v7.ui.Label;
import com.vaadin.v7.ui.Table;
import com.vaadin.v7.ui.VerticalLayout;
import elemental.json.JsonArray;
import org.apache.commons.lang.RandomStringUtils;
import org.apache.commons.lang.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;


@TitleLocalized(key = "esa-competitor-Configuration")
@HelpId("1143")
public class RateShoppingDataView extends TetrisNavigatorView<RateShoppingDataPresenter, Void> {
    private TetrisBeanItemContainer<ExtendedStayCompetitorWrapper> container;
    private TetrisChangeAwareTable table;

    private TetrisSaveCancelButtonBar tetrisSaveCancelButtonBar;
    private TetrisBigDecimalField totalBigDecimalField;

    private Label totalLabel;

    private final static String randomPrefix = RandomStringUtils.randomAlphabetic(10);
    private String influenceId = null;
    private boolean selectCompetitorInfluence = false;
    private boolean selectExtendedStayUSeId = false;
    private String extendedStayUseId = null;
    private String webRateAlias;


    @Override
    protected void initView() {


    }

    @Override
    public void onPresenterInit() {
        setSizeFull();
        VerticalLayout root = new VerticalLayout();
        root.setSizeFull();
        root.setMargin(true);
        root.setSpacing(true);
        webRateAlias = getPresenter().getWebRateAlias();
        addTable(root);
        addTextField(root);
        addButtonSection(root);

        setCompositionRoot(root);
        root.setExpandRatio(table, 0.9f);
    }

    private void addTextField(VerticalLayout root) {
        HorizontalLayout horizontalLayout = new HorizontalLayout();

        totalBigDecimalField = new TetrisBigDecimalField();
        RangeValidator<BigDecimal> bigDecimalRangeValidator = new RangeValidator<BigDecimal>(getText("common.total.equal.to.100"),
                BigDecimal.class,
                BigDecimal.valueOf(100), BigDecimal.valueOf(100));
        bigDecimalRangeValidator.setMinValueIncluded(true);
        bigDecimalRangeValidator.setMaxValueIncluded(true);
        totalBigDecimalField.addValidator(bigDecimalRangeValidator);
        totalBigDecimalField.setStyleName(TetrisTheme.H1);

        totalBigDecimalField.setEnabled(false);

        horizontalLayout.addComponent(totalBigDecimalField);

        totalLabel = new Label();
        totalLabel.setCaption(UiUtils.getText("rate.shopping.total.less.than.hundred"));


        horizontalLayout.setSpacing(true);
        horizontalLayout.addComponent(totalLabel);

        horizontalLayout.setExpandRatio(totalLabel, .30f);
        root.addComponent(horizontalLayout);
        root.setComponentAlignment(horizontalLayout, Alignment.BOTTOM_RIGHT);
    }

    private void addTable(VerticalLayout root) {
        container = new TetrisBeanItemContainer<ExtendedStayCompetitorWrapper>(ExtendedStayCompetitorWrapper.class);
        container.addNestedContainerBean("extendedStayCompetitorMapping");
        container.addNestedContainerBean("extendedStayCompetitorMapping.competitorInfluence");
        container.addNestedContainerBean("extendedStayCompetitorMapping.useExtendedStay");
        container.addNestedContainerBean("extendedStayCompetitorMapping.useUserDefinedCompPercent");


        JavaScript javaScript = JavaScript.getCurrent();
        javaScript.addFunction(getSelectAllJsFunctionName("SelectAll_Func_Competitor_Influence"), new JavaScriptFunction() {
            @Override
            public void call(JsonArray jsonArray) {
                updateCompetitorInfluneceTextInput(jsonArray.getBoolean(0));
            }
        });

        javaScript.addFunction(getSelectAllJsFunctionName("SelectAll_Func_Extended_Use"), new JavaScriptFunction() {
            @Override
            public void call(JsonArray jsonArray) {
                updateExtendedStayUseFields(jsonArray.getBoolean(0));
            }
        });


        table = new TetrisChangeAwareTable();

        table.setStyleName("two-header-row");
        table.setId("esrateShoppingData");
        table.setContainerDataSource(container);
        table.setSizeFull();
        table.setSortEnabled(false);

        final String competitorName = "extendedStayCompetitorMapping.competitorName";
        extendedStayUseId = "extendedStayCompetitorMapping.useExtendedStay";
        influenceId = "extendedStayCompetitorMapping.competitorInfluence";
        table.addGeneratedColumn(competitorName, new Table.ColumnGenerator() {
            @Override
            public Object generateCell(Table table, Object itemId, Object propertyId) {
                ExtendedStayCompetitorWrapper wrapper = (ExtendedStayCompetitorWrapper) itemId;
                HorizontalLayout layout = new HorizontalLayout();
                layout.addComponent(new TetrisLabel(wrapper.getWebRateCompetitorName()));
                if (wrapper.getStatus().equals(3)) {
                    HorizontalLayout horizontalLayout = new HorizontalLayout();
                    TetrisLabel label = new TetrisLabel(" New ", ContentMode.HTML);
                    label.setStyleName(TetrisTheme.ITALICS);
                    label.setStyleName(TetrisTheme.FONT_WHITE);
                    label.setSizeUndefined();
                    horizontalLayout.setSizeUndefined();
                    horizontalLayout.setStyleName(TetrisTheme.BOX_BLUE);
                    horizontalLayout.addComponent(label);
                    horizontalLayout.setComponentAlignment(label, Alignment.TOP_RIGHT);
                    layout.addComponent(new TetrisSpacer("10"));
                    layout.addComponent(horizontalLayout);
                    layout.setComponentAlignment(horizontalLayout, Alignment.TOP_RIGHT);
                }

                return layout;
            }
        });
        table.setColumnHeader(influenceId, getHeader(UiUtils.getText("rate.shopping.competitor.influence.percent"), "SelectAll_Func_Competitor_Influence", "selectAll_competitor_influence", selectCompetitorInfluence));
        table.setColumnHeader(extendedStayUseId, getHeader(UiUtils.getText("rate.shopping.extended.stay.use"), "SelectAll_Func_Extended_Use", "selectAll_Extended_use_", selectExtendedStayUSeId));
        table.setColumnHeader(competitorName, UiUtils.getText("rate.shopping.competitor.property"));


        table.addGeneratedColumn(extendedStayUseId, new TetrisChangeAwareComponentColumnGenerator() {
            @Override
            public ChangeAware generateComponent(Table source, Object itemId, Object columnId) {
                return newExtendedStayUse((ExtendedStayCompetitorWrapper) itemId);
            }
        });

        table.addGeneratedColumn(influenceId, new TetrisChangeAwareComponentColumnGenerator() {
            @Override
            public ChangeAware generateComponent(Table source, Object itemId, Object columnId) {
                return newInfluenceField((ExtendedStayCompetitorWrapper) itemId);
            }
        });

        table.setCellStyleGenerator(new Table.CellStyleGenerator() {
            @Override
            public String getStyle(Table table, Object itemId, Object propertyId) {
                ExtendedStayCompetitorWrapper extendedStayCompetitorWrapper = container.getItem(itemId).getBean();
                if (webRateAlias != null) {
                    if (webRateAlias.equals(extendedStayCompetitorWrapper.getHotelId())) {
                        return TetrisTheme.DISABLED + " " + TetrisTheme.TEXT_RIGHT_ALIGNED_IN_READ_ONLY;
                    }

                }
                return null;
            }
        });
        table.addListener(new TetrisChangeAwareTable.TetrisRowGenerationCompleteListener() {
            @Override
            public void onRowGenerationComplete(TetrisChangeAwareTable.TetrisRowGenerationCompleteEvent event) {

                ExtendedStayCompetitorWrapper bean = (ExtendedStayCompetitorWrapper) event.getBean();
                selectCompetitorInfluence = bean.getExtendedStayCompetitorMapping().getUseUserDefinedCompPercent();
                final TetrisBigDecimalField tetrisBigDecimalField = bean.getCompetitorInfluenceTextField();
                tetrisBigDecimalField.setEnabled(selectCompetitorInfluence);
                final TetrisCheckBox tetrisCheckBox = bean.getExtendedStayUseId();
                tetrisCheckBox.setEnabled(!webRateAlias.equals(bean.getHotelId()));
                if (selectCompetitorInfluence) {
                    ExtendedStayCompetitorMapping extendedStayCompetitorMapping = bean.getExtendedStayCompetitorMapping();
                    tetrisBigDecimalField.setEnabled(extendedStayCompetitorMapping.getUseExtendedStay());
                    tetrisBigDecimalField.setRequired(extendedStayCompetitorMapping.getUseExtendedStay());

                    tetrisCheckBox.addValueChangeListener(new Property.ValueChangeListener() {
                        @Override
                        public void valueChange(Property.ValueChangeEvent event) {
                            Boolean value = tetrisCheckBox.getValue();
                            tetrisBigDecimalField.setEnabled(value);
                            tetrisBigDecimalField.setRequired(value);

                            if (!value) {
                                tetrisBigDecimalField.setValue(" ");
                            }
                        }
                    });
                    tetrisBigDecimalField.setRequiredError(null);
                    tetrisBigDecimalField.setComponentError(null);
                } else {
                    tetrisBigDecimalField.setRequired(false);
                    tetrisBigDecimalField.setRequiredError(null);
                    tetrisBigDecimalField.setComponentError(null);
                }


            }
        });

        table.setVisibleColumns(competitorName, extendedStayUseId, influenceId);
        table.setColumnAlignment(extendedStayUseId, Table.Align.CENTER);
        table.setColumnAlignment(influenceId, Table.Align.CENTER);

        root.addComponent(table);
    }

    private void updateExtendedStayUseFields(boolean isChecked) {
        selectExtendedStayUSeId = isChecked;
        List<ExtendedStayCompetitorWrapper> items = container.getItemsExcludingNoDataFoundItem();
        for (ExtendedStayCompetitorWrapper item : items) {
            TetrisCheckBox extendedStayUseId = item.getExtendedStayUseId();
            if (webRateAlias.equals(item.getHotelId())) {
                extendedStayUseId.setEnabled(false);
            } else {
                extendedStayUseId.setEnabled(true);
                extendedStayUseId.setValue(isChecked);
                if (selectCompetitorInfluence) {
                    updateCompetitorInfluenceField(item);
                }
            }
        }
    }

    private void updateCompetitorInfluenceField(ExtendedStayCompetitorWrapper item) {
        TetrisBigDecimalField competitorInfluenceTextField = item.getCompetitorInfluenceTextField();
        if (!webRateAlias.equals(item.getHotelId())) {

            if (competitorInfluenceTextField != null) {
                Boolean useExtendedStay = false;
                String newValue = " ";
                if (selectCompetitorInfluence) {

                    ExtendedStayCompetitorMapping extendedStayCompetitorMapping = item.getExtendedStayCompetitorMapping();
                    useExtendedStay = selectCompetitorInfluence && extendedStayCompetitorMapping == null ? useExtendedStay : extendedStayCompetitorMapping.getUseExtendedStay();
                    if (useExtendedStay) {
                        newValue = extendedStayCompetitorMapping == null || extendedStayCompetitorMapping.getCompetitorInfluence() == null ? null : extendedStayCompetitorMapping.getCompetitorInfluence().toString();
                    } else
                        newValue = " ";
                    competitorInfluenceTextField.setEnabled(useExtendedStay);
                    competitorInfluenceTextField.setRequired(useExtendedStay);

                } else {
                    competitorInfluenceTextField.setEnabled(false);
                    competitorInfluenceTextField.setRequired(false);
                    newValue = StringUtils.EMPTY;
                }
                competitorInfluenceTextField.setValue(newValue);
                competitorInfluenceTextField.setComponentError(null);
                competitorInfluenceTextField.setConversionError(null);

            }
        } else {
            competitorInfluenceTextField.setEnabled(false);
            competitorInfluenceTextField.setRequired(false);
        }

    }

    private void updateCompetitorInfluneceTextInput(boolean isChecked) {

        selectCompetitorInfluence = isChecked;
        List<ExtendedStayCompetitorWrapper> items = container.getItemsExcludingNoDataFoundItem();
        for (ExtendedStayCompetitorWrapper item : items) {
            TetrisBigDecimalField competitorInfluenceTextField = item.getCompetitorInfluenceTextField();

            TetrisCheckBox extendedStayUseID = item.getExtendedStayUseId();
            extendedStayUseID.setEnabled(!webRateAlias.equals(item.getHotelId()));
            if (isChecked) {
                competitorInfluenceTextField.setEnabled(extendedStayUseID.getValue());
                competitorInfluenceTextField.setValue(" ");
                competitorInfluenceTextField.setRequired(true);

            } else {
                competitorInfluenceTextField.setEnabled(isChecked);
                competitorInfluenceTextField.setValue(" ");
                competitorInfluenceTextField.setRequired(false);

            }


            competitorInfluenceTextField.setComponentError(null);
            competitorInfluenceTextField.setConversionError(null);
            item.getExtendedStayCompetitorMapping().setUseUserDefinedCompPercent(isChecked);

        }
        updateTotalField(BigDecimal.ZERO);

    }

    public String getHeader(final String headerName, String selectAlFunctionName, String selectAllString, boolean selectField) {
        String checkedAttribute;
        if (selectField) {
            checkedAttribute = "checked";
        } else {
            checkedAttribute = " ";
        }

        return "<div><input " +
                " id='" + getSelectAllDivId(selectAllString) + "' type='checkbox' " +
                checkedAttribute +
                " onclick='" + getSelectAllJsFunctionName(selectAlFunctionName) + "(this.checked)' " +
                " style='margin-top: 0px;' >" + headerName + "</div>";
    }

    private String getSelectAllJsFunctionName(String selectAllFunctionName) {
        return selectAllFunctionName + randomPrefix;
    }

    private String getSelectAllDivId(String selectAllString) {
        return selectAllString + randomPrefix;
    }

    private void addButtonSection(AbstractOrderedLayout parentLayout) {
        tetrisSaveCancelButtonBar = new TetrisSaveCancelButtonBar();

        tetrisSaveCancelButtonBar.addPreValidationListener(new TetrisSaveCancelButtonBar.PreValidationListener() {
            @Override
            public void onPreValidation(TetrisSaveCancelButtonBar.PreValidationEvent event) {
                if (!table.hasChanges()) {
                    TetrisNotification.showWarningMessage(getText("common.error.msg.nochangesForSubmit"));
                    event.cancel();
                }
                if (selectCompetitorInfluence && !totalBigDecimalField.getConvertedValue().equals(new BigDecimal("100.00"))) {
                    TetrisNotification.showWarningMessage(getText("common.total.equal.to.100"));
                    event.cancel();
                } else if (!validateInfluencePercentField()) {
                    TetrisNotification.showWarningMessage(getText("pleaseEnterValidInputs"));
                    event.cancel();
                }

            }
        });
        tetrisSaveCancelButtonBar.addValidSaveListener(new TetrisSaveCancelButtonBar.ValidSaveListener() {
            @Override
            public void onValidSave(TetrisSaveCancelButtonBar.ValidSaveEvent event) {
                if (!selectCompetitorInfluence) {
                    distributeEqualPercentageAmongCompetitors();
                }
                presenter.save(container.getItems());
            }
        });
        tetrisSaveCancelButtonBar.getSaveButton().setEnabledRequirements(true, TetrisPermissionKey.EXTENDED_STAY_RATE_CONFIGURATION_COMPETITOR);


        tetrisSaveCancelButtonBar.getCancelButton().addClickListener(new Button.ClickListener() {
            @Override
            public void buttonClick(Button.ClickEvent event) {
                reset();
            }
        });

        parentLayout.addComponent(tetrisSaveCancelButtonBar);

        // table.setSaveCancelButtonBar(tetrisSaveCancelButtonBar);
    }

    private boolean validateInfluencePercentField() {
        List<ExtendedStayCompetitorWrapper> items = container.getItemsExcludingNoDataFoundItem();
        BigDecimal total = BigDecimal.ZERO;
        for (ExtendedStayCompetitorWrapper item : items) {
            TetrisBigDecimalField competitorInfluenceTextField = item.getCompetitorInfluenceTextField();
            competitorInfluenceTextField.setIntegerFormatEnabled(true);
            TetrisCheckBox extendedStayUseField = item.getExtendedStayUseId();
            if ((selectCompetitorInfluence && extendedStayUseField.getValue()) && competitorInfluenceTextField != null && !competitorInfluenceTextField.isValid()) {
                return false;
            }
            String competitorvalue = competitorInfluenceTextField == null ? "0" : competitorInfluenceTextField.getValue();


            total = total.add(new BigDecimal(competitorvalue == null ? "0" : competitorvalue));

        }
        if (selectCompetitorInfluence && total.compareTo((new BigDecimal(100))) != 0) {
            return false;
        }
        return true;
    }

    private void distributeEqualPercentageAmongCompetitors() {
        List<ExtendedStayCompetitorWrapper> items = container.getItemsExcludingNoDataFoundItem();
        int size = 0;
        for (ExtendedStayCompetitorWrapper item : items) {
            if (!webRateAlias.equals(item.getHotelId())) {
                if (item.getExtendedStayCompetitorMapping().getUseExtendedStay()) {
                    size++;
                }
            }
        }

        for (ExtendedStayCompetitorWrapper item : items) {
            if (!webRateAlias.equals(item.getHotelId())) {
                if (item.getExtendedStayCompetitorMapping().getUseExtendedStay()) {
                    item.getExtendedStayCompetitorMapping().setCompetitorInfluence(BigDecimal.valueOf(100).divide(BigDecimal.valueOf(size), 2, RoundingMode.HALF_UP));
                }
            }
        }
    }


    private TetrisBigDecimalField newInfluenceField(ExtendedStayCompetitorWrapper itemId) {
        final TetrisBigDecimalField influencePercentField = new TetrisBigDecimalField();

        RangeValidator<BigDecimal> bigDecimalRangeValidator = new BigDecimalRangeValidator(getText("common.warning.msg.percentage.validInput"), BigDecimal.ONE, BigDecimal.valueOf(100));
        bigDecimalRangeValidator.setMinValueIncluded(true);
        bigDecimalRangeValidator.setMaxValueIncluded(true);


        influencePercentField.addValidator(bigDecimalRangeValidator);
        influencePercentField.setRequired(false);
        influencePercentField.setNullSettingAllowed(true);

        influencePercentField.addValueChangeListener(new Property.ValueChangeListener() {
            @Override
            public void valueChange(Property.ValueChangeEvent valueChangeEvent) {
                if (!influencePercentField.isValid()) {
                    influencePercentField.setRequiredError(getText("invalid.input"));

                    return;
                }

                updateFields();
            }
        });
        return influencePercentField;
    }

    private void updateFields() {
        BigDecimal total = BigDecimal.ZERO;

        for (ExtendedStayCompetitorWrapper extendedStayCompetitorWrapper : container.getItemsExcludingNoDataFoundItem()) {
            if (!webRateAlias.equals(extendedStayCompetitorWrapper.getHotelId())) {
                ExtendedStayCompetitorMapping extendedStayCompetitorMapping = extendedStayCompetitorWrapper.getExtendedStayCompetitorMapping();
                if (extendedStayCompetitorMapping.getUseExtendedStay() && extendedStayCompetitorMapping.getCompetitorInfluence() != null) {
                    total = total.add(extendedStayCompetitorMapping.getCompetitorInfluence());
                }

            }
        }
        updateTotalField(total);
    }

    private void updateTotalField(BigDecimal total) {

        if (selectCompetitorInfluence) {
            totalBigDecimalField.setValue(new StringToBigDecimalConverter().convertToPresentation(total.setScale(2), String.class, TetrisUi.getCurrent().getLocale()));
            totalBigDecimalField.setComponentError(null);
        } else {
            totalBigDecimalField.setValue(null);
            totalBigDecimalField.setComponentError(null);
        }
    }

    private TetrisCheckBox newExtendedStayUse(final ExtendedStayCompetitorWrapper itemId) {
        final TetrisCheckBox extendedStayUseField = new TetrisCheckBox();


        extendedStayUseField.addValueChangeListener(new Property.ValueChangeListener() {
            @Override
            public void valueChange(Property.ValueChangeEvent valueChangeEvent) {
                updateSelectAllExtendUse();
                updateCompetitorInfluenceField(itemId);
                updateFields();

            }
        });
        return extendedStayUseField;
    }


    public void reset() {
        presenter.cancel();
    }

    public void setRecords(List<ExtendedStayCompetitorWrapper> webrateCompetitorsWrappers) {
        table.removeAllItems();
        container.removeAllItems();
        container.addAll(webrateCompetitorsWrappers);
        updateSelectCompetitorFlags();
        updateSelectAllExtendUse();
        updateFields();
        table.setColumnHeader(extendedStayUseId, getHeader(UiUtils.getText("rate.shopping.extended.stay.use"), "SelectAll_Func_Extended_Use", "selectAll_Extended_use_", selectExtendedStayUSeId));
        table.setColumnHeader(influenceId, getHeader(UiUtils.getText("rate.shopping.competitor.influence.percent"), "SelectAll_Func_Competitor_Influence", "selectAll_competitor_influence", selectCompetitorInfluence));

    }

    private void updateSelectAllExtendUse() {
        for (ExtendedStayCompetitorWrapper extendedStayCompetitorWrapper : container.getItemsExcludingNoDataFoundItem()) {
            ExtendedStayCompetitorMapping extendedStayCompetitorMapping = extendedStayCompetitorWrapper.getExtendedStayCompetitorMapping();
            if (!extendedStayCompetitorMapping.getUseExtendedStay()) {
                selectExtendedStayUSeId = false;
                break;

            }
        }
    }

    private void updateSelectCompetitorFlags() {

        for (ExtendedStayCompetitorWrapper extendedStayCompetitorWrapper : container.getItemsExcludingNoDataFoundItem()) {
            if (!webRateAlias.equals(extendedStayCompetitorWrapper.getHotelId())) {

                ExtendedStayCompetitorMapping extendedStayCompetitorMapping = extendedStayCompetitorWrapper.getExtendedStayCompetitorMapping();
                if (extendedStayCompetitorMapping.getUseExtendedStay() && extendedStayCompetitorMapping.getCompetitorInfluence() != null) {
                    selectCompetitorInfluence = extendedStayCompetitorMapping.getUseUserDefinedCompPercent();

                }
            }


        }
    }

    @Override
    public boolean hasChanges() {
        return table.hasChanges();
    }

    public void setPermissions(boolean isEnable) {
        tetrisSaveCancelButtonBar.setEnabled(isEnable);
        table.setEnabled(isEnable);
    }
}

