package com.ideas.tetris.ui.modules.functionspace.configuration.guestroomtypes;

import com.ideas.tetris.pacman.services.accommodation.entity.AccomType;
import com.ideas.tetris.ui.common.cdi.TetrisView;
import com.ideas.tetris.ui.common.component.TetrisBeanItemContainer;
import com.ideas.tetris.ui.common.component.TetrisSaveCancelButtonBar;
import com.ideas.tetris.ui.common.component.select.TetrisComboBox;
import com.ideas.tetris.ui.common.component.table.TetrisChangeAwareComponentColumnGenerator;
import com.ideas.tetris.ui.common.component.table.TetrisChangeAwareTable;
import com.ideas.tetris.ui.common.util.ChangeAware;
import com.vaadin.annotations.Title;
import com.vaadin.ui.Button;
import com.vaadin.v7.ui.Table;
import com.vaadin.v7.ui.VerticalLayout;

import java.util.ArrayList;

@SuppressWarnings("serial")
@Title("Guest Room Type")
public class GuestRoomTypeView extends TetrisView<GuestRoomTypePresenter, Boolean> {
    private TetrisBeanItemContainer<GuestRoomTypeMappingUiWrapper> container;
    private TetrisChangeAwareTable table;
    private TetrisSaveCancelButtonBar saveCancelButtonBar;

    @Override
    protected void initView() {
        container = new TetrisBeanItemContainer<>(GuestRoomTypeMappingUiWrapper.class);

        table = new TetrisChangeAwareTable();
        table.setContainerDataSource(container);

        container.addNestedContainerBean("guestRoomCategory");

        String fsGuestRoomCategory = "guestRoomCategory.roomCategory";
        String rmsAccomType = "selectedAccomType";

        table.addGeneratedColumn(rmsAccomType, new TetrisChangeAwareComponentColumnGenerator() {
            @Override
            public ChangeAware generateComponent(Table source, Object itemId, Object columnId) {
                GuestRoomTypeMappingUiWrapper wrapper = (GuestRoomTypeMappingUiWrapper) itemId;

                TetrisBeanItemContainer<AccomType> accomTypeContainer = new TetrisBeanItemContainer<AccomType>(
                        AccomType.class);

                accomTypeContainer.addAll(presenter.getRmsAccomTypes());

                if (wrapper.getGuestRoomCategory().getAccomTypeId() != null) {
                    wrapper.setSelectedAccomType((presenter.getAccomTypeById(wrapper.getGuestRoomCategory()
                            .getAccomTypeId())));
                }

                TetrisComboBox tetrisComboBox = new TetrisComboBox("", accomTypeContainer);
                tetrisComboBox.setItemCaptionPropertyId("name");
                tetrisComboBox.setRequired(false);
                tetrisComboBox.setNullSelectionAllowed(true);

                return tetrisComboBox;
            }
        });
        table.setVisibleColumns(fsGuestRoomCategory, rmsAccomType);
        table.setColumnHeaders(new String[]{getText("groupPricing.guestRoomType.salesAndCateringRoomTypes"), getText("groupPricing.guestRoomTypes.mapToRmsRoomType")});

        saveCancelButtonBar = new TetrisSaveCancelButtonBar();
        saveCancelButtonBar.addValidSaveListener(new TetrisSaveCancelButtonBar.ValidSaveListener() {
            @Override
            public void onValidSave(TetrisSaveCancelButtonBar.ValidSaveEvent event) {
                presenter.save(container.getItemsExcludingNoDataFoundItem());
            }
        });

        saveCancelButtonBar.addCancelClickListener(new Button.ClickListener() {
            @Override
            public void buttonClick(Button.ClickEvent event) {
                presenter.refresh();
            }
        });

        table.setSaveCancelButtonBar(saveCancelButtonBar);

        VerticalLayout layout = new VerticalLayout(table, saveCancelButtonBar);
        layout.setMargin(true);
        layout.setSpacing(true);
        layout.setSizeUndefined();

        setCompositionRoot(layout);
    }

    public void update(ArrayList<GuestRoomTypeMappingUiWrapper> wrappers) {
        table.addAll(wrappers, true);
    }

    @Override
    public boolean hasChanges() {
        return table.hasChanges();
    }

    public void updatePermission(boolean isEffectiveReadOnly, String permissionKey) {
        saveCancelButtonBar.getSaveButton().setEnabledRequirements(!isEffectiveReadOnly, permissionKey);
    }

    @Override
    public String getHelpId() {
        if (presenter.isGroupPricing()) {
            return "1070";
        } else {
            return "1138";
        }
    }
}
