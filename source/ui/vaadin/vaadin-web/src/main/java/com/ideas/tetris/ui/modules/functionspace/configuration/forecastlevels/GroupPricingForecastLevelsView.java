package com.ideas.tetris.ui.modules.functionspace.configuration.forecastlevels;

import com.ideas.infra.tetris.security.domain.TetrisPermissionKey;
import com.ideas.tetris.ui.common.cdi.TetrisView;
import com.ideas.tetris.ui.common.component.TetrisSaveCancelButtonBar;
import com.ideas.tetris.ui.common.component.label.TetrisSpacer;
import com.ideas.tetris.ui.common.data.fieldgroup.TetrisBeanFieldGroup;
import com.ideas.tetris.ui.common.util.HelpId;
import com.vaadin.annotations.Title;
import com.vaadin.ui.Alignment;
import com.vaadin.ui.Button;
import com.vaadin.ui.Component;
import com.vaadin.ui.FormLayout;
import com.vaadin.ui.HorizontalLayout;
import com.vaadin.ui.Label;
import com.vaadin.ui.VerticalLayout;

@Title("Forecast Levels")
@HelpId("1133")
public class GroupPricingForecastLevelsView extends TetrisView<GroupPricingForecastLevelsPresenter, Boolean> {

    private TetrisBeanFieldGroup<ForecastLevelBean> fieldGroup;

    @Override
    protected void initView() {
        setSizeFull();
        addStyleName("groupPricingForecastLevels");
        VerticalLayout root = new VerticalLayout();
        root.setMargin(true);
        root.setSpacing(true);

        createLegend(root);
        createSlider(root);

        setCompositionRoot(root);
    }

    private void createSlider(VerticalLayout root) {
        fieldGroup = new TetrisBeanFieldGroup<ForecastLevelBean>(ForecastLevelBean.class);

        FormLayout formLayout = new FormLayout();
        formLayout.addStyleName("formLayout");
        formLayout.setWidth(500, Unit.PIXELS);

        ForecastLevelSliderField sliderField = new ForecastLevelSliderField();
        fieldGroup.bind(sliderField, "forecastLevelMinMaxBean");
        formLayout.addComponent(sliderField);

        TetrisSaveCancelButtonBar saveCancelButtonBar = new TetrisSaveCancelButtonBar();
        saveCancelButtonBar.addValidSaveListener(new TetrisSaveCancelButtonBar.ValidSaveListener() {
            @Override
            public void onValidSave(TetrisSaveCancelButtonBar.ValidSaveEvent event) {
                presenter.save();
            }
        });
        saveCancelButtonBar.addCancelClickListener(new Button.ClickListener() {
            @Override
            public void buttonClick(Button.ClickEvent event) {
                fieldGroup.reset();
            }
        });

        saveCancelButtonBar.getSaveButton().setEnabledRequirements(true, TetrisPermissionKey.FUNCTION_SPACE_CONFIGURATION_FORECAST_LEVELS);

        fieldGroup.setSaveCancelButtonBar(saveCancelButtonBar);
        formLayout.addComponent(new TetrisSpacer(1, Unit.PIXELS));
        formLayout.addComponent(saveCancelButtonBar);

        root.addComponent(formLayout);
    }

    private void createLegend(VerticalLayout root) {
        HorizontalLayout legend = new HorizontalLayout();
        legend.addStyleName("legend");

        legend.addComponent(createLegendItem(getText("common.low"), "low"));
        legend.addComponent(new TetrisSpacer(20, Unit.PIXELS));
        legend.addComponent(createLegendItem(getText("common.medium"), "medium"));
        legend.addComponent(new TetrisSpacer(20, Unit.PIXELS));
        legend.addComponent(createLegendItem(getText("common.high"), "high"));

        root.addComponent(legend);
    }

    private Component createLegendItem(String title, String cssClassName) {
        HorizontalLayout layout = new HorizontalLayout();
        layout.setSpacing(true);
        HorizontalLayout box = new HorizontalLayout();
        box.addStyleName(cssClassName);
        box.setWidth(20, Unit.PIXELS);
        box.setHeight(15, Unit.PIXELS);

        Label label = new Label(title);

        layout.addComponent(box);
        layout.addComponent(label);
        layout.setComponentAlignment(label, Alignment.MIDDLE_RIGHT);

        return layout;
    }

    public void populateSliderValue(ForecastLevelBean bean) {
        fieldGroup.setItemDataSource(bean);
    }

    @Override
    public boolean hasChanges() {
        return fieldGroup.hasChanges();
    }
}
