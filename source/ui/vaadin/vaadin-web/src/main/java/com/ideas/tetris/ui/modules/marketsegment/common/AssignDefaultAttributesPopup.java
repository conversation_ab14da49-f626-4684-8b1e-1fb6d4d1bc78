package com.ideas.tetris.ui.modules.marketsegment.common;

import com.ideas.tetris.pacman.services.marketsegment.service.AnalyticalMarketSegmentAttribute;
import com.ideas.tetris.pacman.services.product.Product;
import com.ideas.tetris.ui.common.component.TetrisHRule;
import com.ideas.tetris.ui.common.component.TetrisLabel;
import com.ideas.tetris.ui.common.component.TetrisNotification;
import com.ideas.tetris.ui.common.component.fontawesome.TetrisFontAwesome;
import com.ideas.tetris.ui.common.component.window.TetrisWindow;
import com.ideas.tetris.ui.common.util.UiUtils;
import com.ideas.tetris.ui.modules.marketsegment.common.view.AttributeAssignmentConfig;
import com.ideas.tetris.ui.modules.marketsegment.common.view.AttributeAssignmentLayout;
import com.ideas.tetris.ui.modules.marketsegment.common.view.AttributeBean;
import com.ideas.tetris.ui.modules.marketsegment.common.view.ProductNameConfigurationLayout;
import com.ideas.tetris.ui.modules.marketsegment.popupchain.DelayedPopup;
import com.ideas.tetris.ui.modules.marketsegment.ratecode.RateCodePresenter;
import com.vaadin.server.ClientConnector;
import com.vaadin.ui.Alignment;
import com.vaadin.v7.shared.ui.label.ContentMode;
import com.vaadin.v7.ui.HorizontalLayout;
import com.vaadin.v7.ui.Label;
import com.vaadin.v7.ui.VerticalLayout;

import java.util.List;

import static java.util.Objects.nonNull;

public class AssignDefaultAttributesPopup extends VerticalLayout implements DelayedPopup {
    private static TetrisWindow confirmationPopup;
    RateCodePresenter presenter;

    private TetrisFontAwesome awesomeIcon = TetrisFontAwesome.WARNING;
    private boolean assignCalled = false;
    private AttributeAssignmentLayout assignmentLayout;

    public AssignDefaultAttributesPopup(RateCodePresenter presenter, AMSSummaryUIWrapper summaryUIWrapper) {
        presenter.setAssignDefaultAttributesPopup(this);
        setMargin(true);

        this.presenter = presenter;
        HorizontalLayout contentLayout = addIconAndMarketSegmentText(summaryUIWrapper);
        addComponent(contentLayout);
        addComponent(new TetrisHRule());
        addAttributeAssignmentLayout(summaryUIWrapper);
        AssignDefaultAttributesPopup that = this;
        this.addDetachListener(new ClientConnector.DetachListener() {
            @Override
            public void detach(ClientConnector.DetachEvent detachEvent) {
                that.setVisible(false);
            }
        });
    }

    public static AssignDefaultAttributesPopup showConfirmation(RateCodePresenter presenter, AMSSummaryUIWrapper summaryUIWrapper) {
        AssignDefaultAttributesPopup popup = new AssignDefaultAttributesPopup(presenter, summaryUIWrapper);
        TetrisWindow tetrisWindow = new TetrisWindow(UiUtils.getText("confirmation.window.title"));
        popup.showPopup(tetrisWindow);
        return popup;
    }

    private HorizontalLayout addIconAndMarketSegmentText(AMSSummaryUIWrapper summaryUIWrapper) {
        HorizontalLayout contentLayout = new HorizontalLayout();

        //addIcon to left side at the top
        Label iconLabel = new Label("<span style='font-size: 48px; line-height: 48px;'>" + this.awesomeIcon.getHtml() + "</span>", ContentMode.HTML);
        iconLabel.addStyleName("tetris-messagebox-icon");
        contentLayout.addComponent(iconLabel);

        VerticalLayout rightSide = new VerticalLayout();
        rightSide.setWidth(360, Unit.PIXELS);
        rightSide.addComponent(new TetrisLabel(UiUtils.getText("assign.default.market.segment")));
        TetrisLabel segmentLabel = new TetrisLabel("<span style='font-size: 1.25em; font-weight: bold;'>" + summaryUIWrapper.getMarketSegment() + "_DEF</span>", ContentMode.HTML);
        segmentLabel.addStyleName("tetris-padding-5-top-and-bottom");
        rightSide.addComponent(segmentLabel);
        rightSide.addComponent(new TetrisLabel(UiUtils.getText("assign.default.market.segment.attribute")));
        contentLayout.addComponent(rightSide);
        contentLayout.setSpacing(true);
        contentLayout.setComponentAlignment(iconLabel, Alignment.MIDDLE_LEFT);

        return contentLayout;
    }

    private void addAttributeAssignmentLayout(AMSSummaryUIWrapper summaryUIWrapper) {
        AttributeAssignmentConfig config = AttributeAssignmentConfig.builder()
                .withAmsView(AmsView.AMS_VIEW_RATE_CODE)
                .withComplimentaryFeatureEnabled(presenter.isComplimentaryAttributeEnabled())
                .withForecastActivityTypes(presenter.getForecastActivityTypes())
                .withIndependentProductsEnabled(presenter.isIndependentProductsEnabled())
                .withMaxProducts(presenter.getMaxIndependentProducts())
                .withGroupOptionEnabled(presenter.isGroupOptionEnabled())
                .withEditable(true)
                .withDefaultMS(true)
                .build();
        assignmentLayout = new AttributeAssignmentLayout(config, presenter);
        assignmentLayout.setId("AttributeAssignmentLayoutOnDefMSPopUp");
        if (presenter.isIndependentProductsEnabled()) {
            addNewIndependentProductToExistingListOfDefaults();
        }
        assignmentLayout.onAssign(bean -> {
            if (bean.getForecastActivityType() == null) {
                TetrisNotification.showWarningMessage(UiUtils.getText("assign.market.segment.forecastActivityType.warning"));
            } else {
                assignCalled = true;
                if (nonNull(bean.getProduct()) && !bean.getProduct().isPersisted() && !bean.getProduct().equals(presenter.getNewProduct())) {
                    ProductNameConfigurationLayout.show(bean.getProduct(), assignmentLayout.getProducts(), () -> assign(summaryUIWrapper, bean));
                } else {
                    assign(summaryUIWrapper, bean);
                }
                confirmationPopup.close();
            }
        });
        addComponent(assignmentLayout);
        setComponentAlignment(assignmentLayout, Alignment.MIDDLE_CENTER);
    }

    private void addNewIndependentProductToExistingListOfDefaults() {
        Product newProduct = presenter.getNewProduct();
        List<Product> existingIndependentProducts = presenter.getExistingIndependentProducts();
        //we are creating IP on the fly ,so here we don't have IP created it's just Product p = new Product() , p.setSetName("nameWhichUserHasGiven") at this stage
        if (null != newProduct && (null == newProduct.getId() || (null != newProduct.getId() && newProduct.getId() == 0))) {
            existingIndependentProducts.add(newProduct);
        }
        assignmentLayout.setProducts(existingIndependentProducts);
    }

    private void assign(AMSSummaryUIWrapper summaryUIWrapper, AttributeBean bean) {
        AnalyticalMarketSegmentAttribute attribute = bean.getAttribute();
        presenter.assignDefaultSegment(summaryUIWrapper, attribute);
    }

    @Override
    public void showPopup(TetrisWindow tetrisWindow) {
        confirmationPopup = tetrisWindow;
        confirmationPopup.setHelpId("1161");
        confirmationPopup.setHelpVisibility(true);
        confirmationPopup.setWidth(450, Unit.PIXELS);
        confirmationPopup.setHeight(700, Unit.PIXELS);
        confirmationPopup.center();
        confirmationPopup.setContent(this);
        confirmationPopup.show();
    }

    @Override
    public String getWindowCaption() {
        return UiUtils.getText("confirmation.window.title");
    }

    @Override
    public boolean continueOnClose() {
        return assignCalled;
    }

    public AttributeBean getAttribute() {
        return assignmentLayout.getAttribute();
    }
}
