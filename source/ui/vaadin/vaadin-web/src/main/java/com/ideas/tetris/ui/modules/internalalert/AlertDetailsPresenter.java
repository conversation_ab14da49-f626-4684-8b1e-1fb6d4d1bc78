package com.ideas.tetris.ui.modules.internalalert;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ideas.tetris.pacman.services.commondaoandenities.global.entity.ClientCriteria;
import com.ideas.tetris.pacman.services.commondaoandenities.global.entity.PropertyCriteria;
import com.ideas.tetris.pacman.services.internalalert.InternalAlertService;
import com.ideas.tetris.pacman.services.internalalert.InternalAlertStatus;
import com.ideas.tetris.pacman.services.internalalert.InternalAlertType;
import com.ideas.tetris.pacman.services.internalalert.entity.InternalAlert;
import com.ideas.tetris.pacman.services.internalalert.entity.InternalAlertCriteria;
import com.ideas.tetris.pacman.services.property.PropertyService;
import com.ideas.tetris.platform.services.daoandentities.entity.Client;
import com.ideas.tetris.platform.services.daoandentities.entity.Property;
import com.ideas.tetris.ui.common.cdi.TetrisPresenter;
import com.ideas.tetris.ui.common.util.UiUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;

import javax.inject.Inject;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashSet;
import java.util.List;
import java.util.stream.Collectors;
import com.ideas.tetris.spring.SpringAutowired;

@SpringAutowired
public class AlertDetailsPresenter extends TetrisPresenter<AlertDetailsView, Void> {
    private static final Logger LOGGER = Logger.getLogger(AlertDetailsPresenter.class);
    protected static final String RESOLVE_ACTION = "Resolve";
    private static final Integer ALL_PROPERTIES_ITEM_ID = -1234;
    private static final Integer ALL_CLIENTS_ITEMS_ID = -12345;
    public static final Client ALL_CLIENTS = new Client();
    public static final Property ALL_PROPERTIES = new Property();

    static {
        ALL_CLIENTS.setId(ALL_CLIENTS_ITEMS_ID);
        ALL_CLIENTS.setName(UiUtils.ALL_TEXT);
        ALL_PROPERTIES.setId(ALL_PROPERTIES_ITEM_ID);
        ALL_PROPERTIES.setCode(UiUtils.ALL_TEXT);
        ALL_PROPERTIES.setName(UiUtils.ALL_TEXT);
    }

    protected InternalAlertFilterDto filterDto = new InternalAlertFilterDto();

    @Autowired
    InternalAlertService internalAlertService;

    @Autowired
    PropertyService propertyService;

    @Override
    public void onViewOpened(Void aVoid) {
        view.initViewInternal();
    }

    @Override
    public void onInitParamChanged(String initParam) {
        if (initParam != null && !initParam.equals("alertDetailsView")) {
            try {
                InternalAlertFilterDto filter = new ObjectMapper().readValue(initParam, InternalAlertFilterDto.class);
                setFilterDto(filter);
            } catch (IOException e) {
                LOGGER.warn("Unable to change parameter: " + initParam, e);
            }
        } else {
            setFilterDto(null);
        }
    }

    public List<Client> getClients() {
        List<Client> clients = propertyService.getClients(new ClientCriteria());
        Collections.sort(clients, Comparator.comparing(Client::getName));
        List<Client> sortedClients = new ArrayList<>(clients);
        sortedClients.add(0, ALL_CLIENTS);
        return sortedClients;
    }

    public List<Property> getProperties(Client client) {
        PropertyCriteria propertyCriteria = new PropertyCriteria();
        propertyCriteria.setClient(client);
        List<Property> properties = propertyService.getProperties(propertyCriteria);
        Collections.sort(properties, Comparator.comparing(Property::getName));
        List<Property> sortedProperties = new ArrayList<>(properties);
        sortedProperties.add(0, ALL_PROPERTIES);
        return sortedProperties;
    }

    public InternalAlertFilterDto generateDefaultFilter() {
        InternalAlertFilterDto filterDto = new InternalAlertFilterDto();
        filterDto.setClient(ALL_CLIENTS);
        filterDto.setPropertyByCode(ALL_PROPERTIES);
        filterDto.setPropertyByName(ALL_PROPERTIES);
        filterDto.setAlertStatuses(new HashSet(Arrays.asList(InternalAlertStatus.NEW.toString())));
        return filterDto;
    }

    private List<InternalAlertDisplayDto> getInternalAlerts() {

        InternalAlertCriteria criteria = buildInternalAlertCriteria();
        List<InternalAlert> entities = internalAlertService.getAlerts(criteria);
        return entities.stream()
                .map(InternalAlertDisplayDto::new)
                .collect(Collectors.toList());
    }

    public void performBulkAction(String action, List<InternalAlertDisplayDto> alerts) {
        switch (action) {
            case RESOLVE_ACTION:
                internalAlertService.resolveAlerts(alerts.stream().map(InternalAlertDisplayDto::getInternalAlertId).collect(Collectors.toList()));
                break;
            default:
                break;
        }
        view.refresh();
    }

    public InternalAlertFilterDto getFilterDto() {
        if (filterDto == null) {
            filterDto = new InternalAlertFilterDto();
        }
        return filterDto;
    }

    public void setFilterDto(InternalAlertFilterDto filterDto) {
        if (filterDto != null) {
            this.filterDto = filterDto;
        }
        view.updateData(getInternalAlerts());
    }

    protected InternalAlertCriteria buildInternalAlertCriteria() {
        InternalAlertCriteria criteria = new InternalAlertCriteria();
        if (filterDto.getAlertTypes() != null && filterDto.getAlertTypes().size() > 0) {
            criteria.setAlertTypes(filterDto.getAlertTypes().stream().map(type -> InternalAlertType.valueOf(type)).collect(Collectors.toSet()));
        }
        if (filterDto.getStartDate() != null) {
            criteria.setDateRangeStart(filterDto.getStartDate());
        }
        if (filterDto.getEndDate() != null) {
            criteria.setDateRangeEnd(filterDto.getEndDate());
        }
        if (filterDto.getClient() != null && !filterDto.getClient().getId().equals(ALL_CLIENTS_ITEMS_ID)) {
            criteria.setClientId(filterDto.getClient().getId());
        }
        if (filterDto.getPropertyByCode() != null
                && !filterDto.getPropertyByCode().getId().equals(ALL_PROPERTIES_ITEM_ID)) {
            criteria.setPropertyId(filterDto.getPropertyByCode().getId());
        }
        if (filterDto.getAlertStatuses() != null && !filterDto.getAlertStatuses().isEmpty()) {
            criteria.setStatuses(filterDto.getAlertStatuses().stream().map(status -> InternalAlertStatus.valueOf(status)).collect(Collectors.toSet()));
        }
        return criteria;
    }

    public void updateData() {
        view.updateData(getInternalAlerts());
    }

}
