package com.ideas.tetris.ui.modules.functionspace.forecastreview;

import com.ideas.tetris.pacman.services.functionspace.configuration.entity.FunctionSpaceForecastLevel;
import com.ideas.tetris.pacman.services.functionspace.dto.FunctionSpaceDemandCalendarDateDto;
import com.ideas.tetris.pacman.services.functionspace.dto.FunctionSpaceDemandCalendarDatePartDto;
import com.ideas.tetris.pacman.services.functionspace.dto.FunctionSpaceDemandCalendarDateStatus;
import com.ideas.tetris.pacman.services.functionspace.dto.FunctionSpaceDemandCalendarForecastLevel;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.ui.common.component.button.TetrisLinkButton;
import com.ideas.tetris.ui.common.component.select.TetrisEnumCombobox;
import com.ideas.tetris.ui.common.component.table.TetrisChangeAwareColumnComponentProvider;
import com.ideas.tetris.ui.common.component.textfield.TetrisBigDecimalField;
import com.ideas.tetris.ui.common.data.hierarchical.HierarchicalWithParent;
import com.ideas.tetris.ui.common.data.util.converter.StringToBigDecimalConverter;
import com.ideas.tetris.ui.common.util.ChangeAware;
import com.ideas.tetris.ui.common.util.UiUtils;
import com.vaadin.ui.AbstractOrderedLayout;
import com.vaadin.v7.ui.AbstractField;
import com.vaadin.v7.ui.HorizontalLayout;
import com.vaadin.v7.ui.Label;
import org.apache.commons.collections.CollectionUtils;
import org.joda.time.LocalDate;
import org.joda.time.ReadablePartial;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

public class ForecastReviewUiWrapper extends TetrisChangeAwareColumnComponentProvider implements HierarchicalWithParent<ForecastReviewUiWrapper> {

    private static final String USER_ADJUSTED_UTILIZATION_ID = "userAdjustedUtilization";
    private static final String USER_ADJUSTED_LEVEL_ID = "userAdjustedLevel";
    private static final String STATUS_ID = "status";
    private BigDecimal getOriginalUserUtilization; //used only for day parts level to see if there is a change
    private boolean hasUserForecastOverride;
    private boolean hasSpecialEvents;
    private BigDecimal totalGuestRoomUnconstraintedDemand;
    private BigDecimal groupGuestRoomForecast;
    private BigDecimal groupGuestRoomOnBooks;
    private BigDecimal groupGuestRoomRemainingDemand;
    private BigDecimal transientGuestRoomForecast;
    private BigDecimal transientGuestRoomOnBooks;
    private BigDecimal transientGuestRoomRemainingDemand;
    private BigDecimal groupForecastAchievedPercentage;
    private BigDecimal totalGuestRoomOnBooks;
    private BigDecimal totalGuestRoomForecast;
    private BigDecimal onTheBooksUtilizationWithProspects;
    private int numberOfIndivisibleRoomsOnBooks;

    private AbstractOrderedLayout actionLayout;

    private FunctionSpaceDemandCalendarDateDto dto;
    private FunctionSpaceDemandCalendarDatePartDto datePartDto;
    List<ForecastReviewUiWrapper> childrenInUI = new ArrayList<ForecastReviewUiWrapper>();
    List<ForecastReviewUiWrapper> childrenNotInUI = new ArrayList<ForecastReviewUiWrapper>();
    ForecastReviewUiWrapper parent = null;

    private LocalDate occupancyDate;
    private String functionSpaceDayPartName;
    private BigDecimal forecastedUtilization;
    private BigDecimal otbUtilization;
    private BigDecimal forecastVariance;
    private FunctionSpaceForecastLevel configuredForecastedLevel;
    private FunctionSpaceDemandCalendarDateStatus status;
    private FunctionSpaceDemandCalendarForecastLevel forecastedLevel;
    private BigDecimal userAdjustedUtilization;
    private FunctionSpaceDemandCalendarForecastLevel userAdjustedLevel;
    private String dow;
    private ReadablePartial beginTime;
    private ReadablePartial endTime;
    private boolean deleted;
    private Label overrideIcon;
    private TetrisLinkButton removeLinkButton;
    private boolean userAdjustedUtilizationChangeFlag = false;
    private boolean statusChangeFlag = false;


    public void setGetOriginalUserUtilization(BigDecimal getOriginalUserUtilization) {
        this.getOriginalUserUtilization = getOriginalUserUtilization;
    }

    public ForecastReviewUiWrapper() {
        //no args constructor that we need for the tetris table
    }

    public ForecastReviewUiWrapper(FunctionSpaceDemandCalendarDateDto parent, List<FunctionSpaceDemandCalendarDatePartDto> passingChildren, List<FunctionSpaceDemandCalendarDatePartDto> failingChildren, String dayOfWeek) {
        this.dto = parent;
        this.occupancyDate = dto.getOccupancyDate();
        this.forecastedUtilization = percentValue(dto.getForecastedUtilization());
        this.otbUtilization = percentValue(dto.getOnTheBooksUtilization());
        this.forecastVariance = dto.getForecastVariance(); //already a precent
        this.configuredForecastedLevel = dto.getFunctionSpaceForecastLevel();
        this.status = dto.getFunctionSpaceDemandCalendarDateStatus();
        this.forecastedLevel = dto.getFunctionSpaceDemandCalendarForecastLevel();
        this.dow = dayOfWeek;

        //columns that are not shown by default in the table
        this.numberOfIndivisibleRoomsOnBooks = dto.getNumberOfIndivisibleRoomsOnBooks();
        this.onTheBooksUtilizationWithProspects = percentValue(dto.getOnTheBooksUtilizationWithProspects());
        this.totalGuestRoomForecast = dto.getTotalGuestRoomForecast();
        this.totalGuestRoomOnBooks = dto.getTotalGuestRoomOnBooks();
        this.totalGuestRoomUnconstraintedDemand = dto.getTotalGuestRoomUnconstraintedDemand();
        this.groupGuestRoomForecast = dto.getGroupGuestRoomForecast();
        this.groupGuestRoomOnBooks = dto.getGroupGuestRoomOnBooks();
        this.groupGuestRoomRemainingDemand = dto.getGroupGuestRoomRemainingDemand();
        this.transientGuestRoomForecast = dto.getTransientGuestRoomForecast();
        this.transientGuestRoomOnBooks = dto.getTransientGuestRoomOnBooks();
        this.transientGuestRoomRemainingDemand = dto.getTransientGuestRoomRemainingDemand();
        this.groupForecastAchievedPercentage = dto.getGroupForecastAchievedPercentage();

        addChildren(passingChildren, failingChildren);

        this.userAdjustedUtilization = computeWeightedAverage(this);
        this.userAdjustedLevel = getDemandCalendarForecastLevel(this.configuredForecastedLevel, this.userAdjustedUtilization);
        this.hasSpecialEvents = dto.hasSpecialEvents();
        this.hasUserForecastOverride = dto.hasUserForecastOverride();

        setReadOnly(true);
    }

    public ForecastReviewUiWrapper(FunctionSpaceDemandCalendarDatePartDto dto, FunctionSpaceForecastLevel configuredForecastedLevel, String dow) {
        this.datePartDto = dto;
        this.occupancyDate = dto.getOccupancyDate();
        this.forecastedUtilization = dto.getForecastUtilizationAsPercent();
        this.otbUtilization = percentValue(dto.getOnBooksUtilization());
        this.forecastVariance = dto.getForecastVarianceAsPercent();
        this.status = dto.getFunctionSpaceDemandCalendarDateStatus();
        this.functionSpaceDayPartName = dto.getDayPartName();

        this.hasUserForecastOverride = dto.hasOverride();
        this.userAdjustedUtilization = determineUtilizationForecast(dto.getUserUtilization(), forecastedUtilization);
        this.getOriginalUserUtilization = determineUtilizationForecast(dto.getUserUtilization(), forecastedUtilization);
        this.configuredForecastedLevel = configuredForecastedLevel;
        this.userAdjustedLevel = getDemandCalendarForecastLevel(this.configuredForecastedLevel, this.userAdjustedUtilization);
        this.forecastedLevel = getDemandCalendarForecastLevel(this.configuredForecastedLevel, this.forecastedUtilization);
        this.beginTime = dto.getBeginTime();
        this.endTime = dto.getEndTime();
        this.dow = dow;

        setReadOnly(true);
    }

    public String getForecastLevelForReports() {
        return getStringValueOfForecastLevel(userAdjustedLevel);
    }

    public String getStatusForReports() {
        return getStringValueOfDemanadCalenderStatus(getStatus());
    }

    public FunctionSpaceDemandCalendarDatePartDto getDatePartDto() {
        return datePartDto;
    }

    private BigDecimal determineUtilizationForecast(BigDecimal userValue, BigDecimal forecastedValue) {
        if (userValue == null) {
            return forecastedValue;
        }

        return percentValue(userValue);
    }

    private BigDecimal percentValue(BigDecimal value) {
        if (value == null) {
            return BigDecimal.ZERO;
        }
        return value.multiply(new BigDecimal(100)).setScale(2, BigDecimal.ROUND_HALF_UP);
    }

    //Port over logic from Services - Remove this and share with services, but keep in mind they are not dealing with a percent and we are
    public static FunctionSpaceDemandCalendarForecastLevel getDemandCalendarForecastLevel(FunctionSpaceForecastLevel forecastLevel, BigDecimal value) {
        // Get the min/max values - default to 0
        double min = forecastLevel.getMin() != null ? forecastLevel.getMin().doubleValue() : 0;
        double max = forecastLevel.getMax() != null ? forecastLevel.getMax().doubleValue() : 0;

        // Get the forecasted utilization
        double forecastedUtilization = (value == null) ? 0 : value.doubleValue();
        if (forecastedUtilization <= min) {
            return FunctionSpaceDemandCalendarForecastLevel.LOW;
        } else if (forecastedUtilization < max) {
            return FunctionSpaceDemandCalendarForecastLevel.MEDIUM;
        }
        return FunctionSpaceDemandCalendarForecastLevel.HIGH;
    }

    private void addChildren(List<FunctionSpaceDemandCalendarDatePartDto> passingChildren, List<FunctionSpaceDemandCalendarDatePartDto> failingChildren) {
        BigDecimal total = BigDecimal.ZERO;

        BigDecimal forecastedUtilizationValue;

        //first process children that need to be displayed in the table
        for (FunctionSpaceDemandCalendarDatePartDto passingChild : passingChildren) {
            forecastedUtilizationValue = determineForecastedUtilizationValue(passingChild);
            addChild(passingChild, true);
            total = total.add(forecastedUtilizationValue);
        }

        //next process the children that are NOT going to be displayed in the table
        for (FunctionSpaceDemandCalendarDatePartDto failingChild : failingChildren) {
            forecastedUtilizationValue = determineForecastedUtilizationValue(failingChild);
            addChild(failingChild, false);
            total = total.add(forecastedUtilizationValue);
        }

        this.userAdjustedUtilization = total.divide(new BigDecimal(passingChildren.size() + failingChildren.size()), BigDecimal.ROUND_HALF_UP).setScale(2, BigDecimal.ROUND_HALF_UP);
        this.userAdjustedLevel = getDemandCalendarForecastLevel(this.configuredForecastedLevel, this.userAdjustedUtilization);
    }

    private BigDecimal determineForecastedUtilizationValue(FunctionSpaceDemandCalendarDatePartDto datePart) {
        BigDecimal value;
        if (datePart.hasOverride()) {
            hasUserForecastOverride = true;
            value = datePart.getUserUtilization();
        } else {
            value = datePart.getForecastUtilizationAsPercent();
        }

        return value;
    }

    private void addChild(FunctionSpaceDemandCalendarDatePartDto daypartDto, boolean includeInUi) {
        ForecastReviewUiWrapper child = new ForecastReviewUiWrapper(daypartDto, this.configuredForecastedLevel, this.dow);
        child.setParent(this);
        if (includeInUi) {
            childrenInUI.add(child);
        } else {
            childrenNotInUI.add(child);
        }
    }

    @Override
    public ForecastReviewUiWrapper getParent() {
        return parent;
    }

    @Override
    public List<ForecastReviewUiWrapper> getChildren() {
        return childrenInUI;
    }

    @Override
    public boolean hasChildren() {
        return !childrenInUI.isEmpty();
    }

    public void setParent(ForecastReviewUiWrapper parent) {
        this.parent = parent;
    }

    public LocalDate getOccupancyDate() {
        return occupancyDate;
    }

    public void setOccupancyDate(LocalDate occupancyDate) {
        this.occupancyDate = occupancyDate;
    }

    public String getFunctionSpaceDayPartName() {
        return functionSpaceDayPartName;
    }

    public void setFunctionSpaceDayPartName(String functionSpaceDayPartName) {
        this.functionSpaceDayPartName = functionSpaceDayPartName;
    }

    public BigDecimal getForecastedUtilization() {
        return forecastedUtilization;
    }

    public void setForecastedUtilization(BigDecimal forecastedUtilization) {
        this.forecastedUtilization = forecastedUtilization;
    }

    public BigDecimal getOtbUtilization() {
        return otbUtilization;
    }

    public void setOtbUtilization(BigDecimal otbUtilization) {
        this.otbUtilization = otbUtilization;
    }

    public BigDecimal getForecastVariance() {
        return forecastVariance;
    }

    public void setForecastVariance(BigDecimal forecastVariance) {
        this.forecastVariance = forecastVariance;
    }

    public FunctionSpaceDemandCalendarDateStatus getStatus() {
        return status;
    }

    public void setStatus(FunctionSpaceDemandCalendarDateStatus status) {
        statusChangeFlag = status != this.status;
        this.status = status;
    }

    public FunctionSpaceDemandCalendarDateDto getDto() {
        return dto;
    }

    public BigDecimal getUserAdjustedUtilization() {
        return userAdjustedUtilization;
    }

    public void setUserAdjustedUtilization(BigDecimal userAdjustedUtilization) {
        userAdjustedUtilizationChangeFlag = userAdjustedUtilization != this.userAdjustedUtilization;
        this.userAdjustedUtilization = userAdjustedUtilization;
    }

    public FunctionSpaceDemandCalendarForecastLevel getForecastedLevel() {
        return forecastedLevel;
    }

    public void setForecastedLevel(FunctionSpaceDemandCalendarForecastLevel forecastedLevel) {
        this.forecastedLevel = forecastedLevel;
    }

    public FunctionSpaceDemandCalendarForecastLevel getUserAdjustedLevel() {
        return userAdjustedLevel;
    }

    public void setUserAdjustedLevel(FunctionSpaceDemandCalendarForecastLevel userAdjustedLevel) {
        this.userAdjustedLevel = userAdjustedLevel;
    }

    public String getDow() {
        return this.dow;
    }

    public boolean isParent() {
        return parent == null;
    }

    public ReadablePartial getBeginTime() {
        return beginTime;
    }

    public ReadablePartial getEndTime() {
        return endTime;
    }

    public TetrisBigDecimalField getUserAdjustedUtilizationField() {
        return (TetrisBigDecimalField) getComponent(USER_ADJUSTED_UTILIZATION_ID);
    }

    public TetrisEnumCombobox getUserAdjustedLevelField() {
        return (TetrisEnumCombobox) getComponent(USER_ADJUSTED_LEVEL_ID);
    }

    public TetrisEnumCombobox getStatusField() {
        return (TetrisEnumCombobox) getComponent(STATUS_ID);
    }

    public void removeUserOverride() {
        TetrisBigDecimalField userAdjustedUtilizationField = getUserAdjustedUtilizationField();
        if (userAdjustedUtilizationField != null) {
            userAdjustedUtilizationField.setValue(this.getForecastedUtilization().toPlainString());
        } else {
            //this is a non ui row, so we don't have a textfield to set.  Instead just set the ForecastedUtilization directly
            setUserAdjustedUtilization(this.getForecastedUtilization());
        }

        this.hasUserForecastOverride = false;
        this.deleted = true;

        if (getOverrideIcon() != null) {
            getOverrideIcon().setVisible(false);
            getRemoveLinkButton().setVisible(false);
        }
    }

    public void updateForecastedValueByParent() {
        //Get visible children
        List<ForecastReviewUiWrapper> children1 = getChildren();

        BigDecimal parentValue = getUserAdjustedUtilization();
        //Add logic here to figure out what the day part values should be based on parent utilization value

        //loop over visible children and set its value by using the visible textfield
        for (ForecastReviewUiWrapper child : children1) {
            TetrisBigDecimalField tetrisTextField = child.getUserAdjustedUtilizationField();
            tetrisTextField.setValue(new StringToBigDecimalConverter().convertToPresentation(parentValue, String.class, UiUtils.getLocale()));
        }

        //update any children that are not in the UI
        for (ForecastReviewUiWrapper childNotInUi : childrenNotInUI) {
            childNotInUi.setUserAdjustedUtilization(parentValue);
        }

        //set the forecast level value
        FunctionSpaceDemandCalendarForecastLevel level = getDemandCalendarForecastLevel(this.configuredForecastedLevel, parentValue);
        TetrisEnumCombobox levelField = getUserAdjustedLevelField();
        levelField.setValueChangeEventDisabled(true);
        levelField.setValue(level);
        levelField.setValueChangeEventDisabled(false);
    }

    public void updateForecastedValueByChild() {
        ForecastReviewUiWrapper parent1 = getParent();
        TetrisBigDecimalField parentTextField = parent1.getUserAdjustedUtilizationField();

        BigDecimal childValue = getUserAdjustedUtilization();
        //add logic to populate parent/overall ForecastedUtilization value

        //disable the parentTextField value change listener as we don't want its value propagating back down to its children
        parentTextField.setValueChangeEventDisabled(true);
        parentTextField.setValue(new StringToBigDecimalConverter().convertToPresentation(computeWeightedAverage(parent1), String.class, UiUtils.getLocale()));
        parentTextField.setValueChangeEventDisabled(false);

        TetrisEnumCombobox parentForecastedLevel = parent1.getUserAdjustedLevelField();
        parentForecastedLevel.setValueChangeEventDisabled(true);
        parentForecastedLevel.setValue(getDemandCalendarForecastLevel(this.configuredForecastedLevel, UiUtils.toBigDecimal(parentTextField.getValue())));
        parentForecastedLevel.setValueChangeEventDisabled(false);

        //adjust the forecast level based on child utilization level

        FunctionSpaceDemandCalendarForecastLevel level = getDemandCalendarForecastLevel(this.configuredForecastedLevel, childValue);

        TetrisEnumCombobox userAdjustedLevelField = getUserAdjustedLevelField();
        userAdjustedLevelField.setValueChangeEventDisabled(true);
        userAdjustedLevelField.setValue(level);
        userAdjustedLevelField.setValueChangeEventDisabled(false);
    }

    public void updateForecastLevel() {
        FunctionSpaceDemandCalendarForecastLevel currentLevel = getUserAdjustedLevel();
        TetrisBigDecimalField userAdjustField = getUserAdjustedUtilizationField();

        FunctionSpaceDemandCalendarForecastLevel originalLevel = getDemandCalendarForecastLevel(this.configuredForecastedLevel, this.userAdjustedUtilization);

        BigDecimal value = getUtilizationBasedOnLevels(currentLevel, originalLevel);
        userAdjustField.setValue(new StringToBigDecimalConverter().convertToPresentation(value, String.class, UiUtils.getLocale()));
    }

    private BigDecimal getUtilizationBasedOnLevels(FunctionSpaceDemandCalendarForecastLevel currentLevel, FunctionSpaceDemandCalendarForecastLevel originalLevel) {
        int value = 0;
        if (!currentLevel.equals(FunctionSpaceDemandCalendarForecastLevel.MEDIUM)) {
            value = getValueForlevel(currentLevel);
        } else if (originalLevel.equals(FunctionSpaceDemandCalendarForecastLevel.LOW)) {
            value = getValueForlevel(FunctionSpaceDemandCalendarForecastLevel.LOW) + 1;
        } else {
            value = getValueForlevel(FunctionSpaceDemandCalendarForecastLevel.HIGH) - 1;
        }
        return new BigDecimal(value);
    }

    private int getValueForlevel(FunctionSpaceDemandCalendarForecastLevel level) {
        if (FunctionSpaceDemandCalendarForecastLevel.LOW.equals(level)) {
            return this.configuredForecastedLevel.getMin();
        }
        return this.configuredForecastedLevel.getMax();
    }

    public void rebind() {
        for (ChangeAware changeAware : components.values()) {
            if (changeAware instanceof AbstractField) {
                AbstractField field = (AbstractField) changeAware;
                field.setPropertyDataSource(field.getPropertyDataSource());
            }
        }

        for (ForecastReviewUiWrapper child : getChildren()) {
            child.rebind();
        }
    }

    public AbstractOrderedLayout getActionLayout() {
        if (actionLayout == null) {
            setActionLayout(getHorizontalLayout());
        }
        return actionLayout;
    }

    private HorizontalLayout getHorizontalLayout() {
        HorizontalLayout layout = new HorizontalLayout();
        layout.setSpacing(true);
        return layout;
    }

    public void setActionLayout(AbstractOrderedLayout actionLayout) {
        this.actionLayout = actionLayout;
    }

    @Override
    public boolean hasChanges() {
        boolean hasChange = super.hasChanges(true);
        if (!hasChange) {
            for (ForecastReviewUiWrapper child : getChildren()) {
                if (child.hasChanges(true)) {
                    return true;
                }
            }
        }
        return hasChange || userAdjustedUtilizationChangeFlag || statusChangeFlag;
    }

    @Override
    public boolean isValid() {
        boolean isValid = super.isValid();

        if (isValid) {
            //loop through children
            for (ForecastReviewUiWrapper child : getChildren()) {
                if (!child.isValid()) {
                    return false;
                }
            }
        }
        return isValid;
    }

    @Override
    public void setReadOnly(boolean readOnly) {
        super.setReadOnly(readOnly);
        for (ForecastReviewUiWrapper child : getChildren()) {
            child.setReadOnly(readOnly);
        }
    }

    @Override
    public void resetFields() {
        TetrisEnumCombobox userAdjustedLevelField = getUserAdjustedLevelField();
        TetrisBigDecimalField userAdjustedUtilizationField = getUserAdjustedUtilizationField();
        TetrisEnumCombobox statusField = getStatusField();

        if (userAdjustedLevelField != null) {
            userAdjustedLevelField.setValueChangeEventDisabled(true);
            userAdjustedUtilizationField.setValueChangeEventDisabled(true);
            statusField.setValueChangeEventDisabled(true);
        }

        super.resetFields();
        for (ForecastReviewUiWrapper child : getChildren()) {
            child.resetFields();
        }

        if (userAdjustedLevelField != null) {
            userAdjustedLevelField.setValueChangeEventDisabled(false);
            userAdjustedUtilizationField.setValueChangeEventDisabled(false);
            statusField.setValueChangeEventDisabled(false);
        }

        //hasUserForecast override is not change aware so manual change of flag
        resetOverrideIcons();

        statusChangeFlag = false;
        userAdjustedUtilizationChangeFlag = false;
    }

    private BigDecimal computeWeightedAverage(ForecastReviewUiWrapper parent1) {
        BigDecimal total = BigDecimal.ZERO;
        double hours = 0.0;

        for (ForecastReviewUiWrapper wrapper : parent1.getAllChildren()) {
            if (wrapper.getBeginTime() != null && wrapper.getEndTime() != null) {
                double hour = DateUtil.getHoursBetween(wrapper.getBeginTime(), wrapper.getEndTime());
                total = total.add(wrapper.getUserAdjustedUtilization().multiply(new BigDecimal(hour)));
                hours += hour;
            }
        }
        return total.divide(new BigDecimal(hours), BigDecimal.ROUND_HALF_UP).setScale(4, BigDecimal.ROUND_HALF_UP);
    }

    public List<ForecastReviewUiWrapper> getAllChildren() {
        List<ForecastReviewUiWrapper> allChildren = new ArrayList<ForecastReviewUiWrapper>();
        allChildren.addAll(childrenInUI);
        allChildren.addAll(childrenNotInUI);

        return allChildren;
    }

    //additional columns
    public BigDecimal getTotalGuestRoomUnconstraintedDemand() {
        return totalGuestRoomUnconstraintedDemand;
    }

    public BigDecimal getGroupGuestRoomForecast() {
        return groupGuestRoomForecast;
    }

    public BigDecimal getGroupGuestRoomOnBooks() {
        return groupGuestRoomOnBooks;
    }

    public BigDecimal getGroupGuestRoomRemainingDemand() {
        return groupGuestRoomRemainingDemand;
    }

    public BigDecimal getTransientGuestRoomForecast() {
        return transientGuestRoomForecast;
    }

    public BigDecimal getTransientGuestRoomOnBooks() {
        return transientGuestRoomOnBooks;
    }

    public BigDecimal getTransientGuestRoomRemainingDemand() {
        return transientGuestRoomRemainingDemand;
    }

    public BigDecimal getGroupForecastAchievedPercentage() {
        return groupForecastAchievedPercentage;
    }

    public BigDecimal getTotalGuestRoomOnBooks() {
        return totalGuestRoomOnBooks;
    }

    public BigDecimal getTotalGuestRoomForecast() {
        return totalGuestRoomForecast;
    }

    public BigDecimal getOnTheBooksUtilizationWithProspects() {
        return onTheBooksUtilizationWithProspects;
    }

    public int getNumberOfIndivisibleRoomsOnBooks() {
        return numberOfIndivisibleRoomsOnBooks;
    }

    public boolean isHasUserForecastOverride() {
        return hasUserForecastOverride;
    }

    public void setHasUserForecastOverride(boolean hasUserForecastOverride) {
        this.hasUserForecastOverride = hasUserForecastOverride;
    }

    public boolean isHasSpecialEvents() {
        return hasSpecialEvents;
    }

    public FunctionSpaceForecastLevel getConfiguredForecastedLevels() {
        return configuredForecastedLevel;
    }

    public boolean isDeleted() {
        return deleted;
    }

    public void removeParentOverrideIcons() {
        this.hasUserForecastOverride = false;
        this.deleted = true;
        if (getOverrideIcon() != null) {
            getOverrideIcon().setVisible(false);
            getRemoveLinkButton().setVisible(false);
        }
    }

    public void setOverrideIcon(Label overrideIcon) {
        this.overrideIcon = overrideIcon;
    }

    public Label getOverrideIcon() {
        return overrideIcon;
    }

    public void setRemoveLinkButton(TetrisLinkButton removeLinkButton) {
        this.removeLinkButton = removeLinkButton;
    }

    public TetrisLinkButton getRemoveLinkButton() {
        return removeLinkButton;
    }

    private void resetOverrideIcons() {
        if (deleted) {
            hasUserForecastOverride = true;
            deleted = false;
        }

        if (getOverrideIcon() != null) {
            getOverrideIcon().setVisible(isHasUserForecastOverride());
            getRemoveLinkButton().setVisible(isHasUserForecastOverride());
        }
    }

    public void updateStatus() {
        if (isParent()) {
            for (ForecastReviewUiWrapper child : getAllChildren()) {
                child.updateStatus(this.status);
            }
        } else {
            getParent().updateStatus(computeParentStatus(getParent()));
        }
    }

    private void updateStatus(FunctionSpaceDemandCalendarDateStatus status) {
        TetrisEnumCombobox statusField = getStatusField();

        if (statusField != null) {
            statusField.setValueChangeEventDisabled(true);
            statusField.setValue(status);
            statusField.setValueChangeEventDisabled(false);
        } else {
            setStatus(status);
        }

    }

    //ported from services FunctionSpaceDemandCalendarDatePartDto
    public FunctionSpaceDemandCalendarDateStatus computeParentStatus(ForecastReviewUiWrapper parent) {
        if (CollectionUtils.isNotEmpty(parent.getAllChildren())) {
            int openCount = 0;
            int closedCount = 0;
            for (ForecastReviewUiWrapper datePartDto : parent.getAllChildren()) {
                if (FunctionSpaceDemandCalendarDateStatus.CLOSED.equals(datePartDto.getStatus())) {
                    closedCount++;
                } else if (FunctionSpaceDemandCalendarDateStatus.OPEN.equals(datePartDto.getStatus())) {
                    openCount++;
                }
            }

            if (closedCount == parent.getAllChildren().size()) {
                // If all the day parts are CLOSED, then the status should be closed
                return FunctionSpaceDemandCalendarDateStatus.CLOSED;
            } else if (openCount == parent.getAllChildren().size()) {
                // If all the day parts are OPEN, then the status should be open
                return FunctionSpaceDemandCalendarDateStatus.OPEN;
            }

            // Any other combination means that we should evaluate
            return FunctionSpaceDemandCalendarDateStatus.EVALUATE;
        }

        // If there are no day parts for a date, that means there is nothing to sell
        return FunctionSpaceDemandCalendarDateStatus.CLOSED;
    }

    public BigDecimal getGetOriginalUserUtilization() {
        return getOriginalUserUtilization;
    }

    public void refreshComputedWeightAvg() {
        TetrisBigDecimalField userAdjustedTextField = getUserAdjustedUtilizationField();

        //disable the parentTextField value change listener as we don't want its value propagating back down to its children
        userAdjustedTextField.setValueChangeEventDisabled(true);
        userAdjustedTextField.setValue(computeWeightedAverage(this).toPlainString());
        userAdjustedTextField.setValueChangeEventDisabled(false);
    }

    public String getStringValueOfForecastLevel(FunctionSpaceDemandCalendarForecastLevel forecastLevel) {
        if (FunctionSpaceDemandCalendarForecastLevel.HIGH.equals(forecastLevel)) {
            return UiUtils.getText("common.high");
        } else if (FunctionSpaceDemandCalendarForecastLevel.MEDIUM.equals(forecastLevel)) {
            return UiUtils.getText("common.medium");
        } else if (FunctionSpaceDemandCalendarForecastLevel.LOW.equals(forecastLevel)) {
            return UiUtils.getText("common.low");
        }
        return null;
    }

    public String getStringValueOfDemanadCalenderStatus(FunctionSpaceDemandCalendarDateStatus caption) {
        if (FunctionSpaceDemandCalendarDateStatus.CLOSED.equals(caption)) {
            return UiUtils.getText("CLOSED");
        } else if (FunctionSpaceDemandCalendarDateStatus.EVALUATE.equals(caption)) {
            return UiUtils.getText("EVALUATE");
        } else if (FunctionSpaceDemandCalendarDateStatus.OPEN.equals(caption)) {
            return UiUtils.getText("OPEN");
        } else {
            return null;
        }
    }
}