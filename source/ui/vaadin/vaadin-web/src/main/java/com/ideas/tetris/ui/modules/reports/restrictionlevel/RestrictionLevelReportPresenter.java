package com.ideas.tetris.ui.modules.reports.restrictionlevel;

import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.configparams.IntegrationConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.services.accommodation.dto.AccomClassSummary;
import com.ideas.tetris.pacman.services.accommodation.dto.AccomTypeSummary;
import com.ideas.tetris.pacman.services.accommodation.service.AccommodationService;
import com.ideas.tetris.pacman.services.reports.dto.ScheduleReportDTO;
import com.ideas.tetris.pacman.services.reports.restrictionlevel.RestrictionLevelReportService;
import com.ideas.tetris.pacman.services.reports.restrictionlevel.dto.RestrictionLevelReportDTO;
import com.ideas.tetris.pacman.services.scheduledreport.entity.OutputType;
import com.ideas.tetris.pacman.services.scheduledreport.entity.ReportType;
import com.ideas.tetris.platform.common.xml.schema.workcontext.v1.WorkContextType;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import com.ideas.tetris.ui.common.component.date.customdateselector.JavaDateSelectorBean;
import com.ideas.tetris.ui.common.component.date.customdateselector.RollingDateOptionBean;
import com.ideas.tetris.ui.common.component.date.customdateselector.RollingDateOptions;
import com.ideas.tetris.ui.common.component.date.customdateselector.RollingDateWithMonthOption;
import com.ideas.tetris.ui.common.security.UiContext;
import com.ideas.tetris.ui.common.util.DateFormatUtil;
import com.ideas.tetris.ui.common.util.SharedSessionState;
import com.ideas.tetris.ui.common.util.UiUtils;
import com.ideas.tetris.ui.modules.reports.AbstractReportPresenter;
import com.ideas.tetris.ui.modules.reports.reportscheduler.JasperReportTypeEnum;
import com.ideas.tetris.ui.modules.reports.reportscheduler.dto.ScheduledReportDisplayDTO;
import com.ideas.tetris.ui.modules.reports.util.JavaDateRange;
import com.ideas.tetris.ui.modules.reports.util.ReportCriteria;
import com.ideas.tetris.ui.modules.reports.util.RollingDateRange;

import javax.inject.Inject;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static com.ideas.tetris.ui.common.component.date.customdateselector.LocalizationKeyConstants.LAST_UPDATED_DATE;
import static com.ideas.tetris.ui.common.component.date.customdateselector.LocalizationKeyConstants.LAST_UPDATED_DATE_MINUS;
import static com.ideas.tetris.ui.common.component.date.customdateselector.StaticRollingDateComponent.MINUS;
import static com.ideas.tetris.ui.common.component.date.customdateselector.StaticRollingDateComponent.PLUS;
import static com.ideas.tetris.ui.modules.reports.reportscheduler.JasperReportConstants.DEFAULT_DATE_FOR_REPORTS;
import static com.ideas.tetris.ui.modules.reports.reportscheduler.JasperReportConstants.IS_IGNORE_PAGINATION;
import static com.ideas.tetris.ui.modules.reports.reportscheduler.JasperReportConstants.OUTPUT;
import static com.ideas.tetris.ui.modules.reports.reportscheduler.JasperReportConstants.PARAM_ENDDATE;
import static com.ideas.tetris.ui.modules.reports.reportscheduler.JasperReportConstants.PARAM_ISROLLING_DATE;
import static com.ideas.tetris.ui.modules.reports.reportscheduler.JasperReportConstants.PARAM_ROLLING_END_DATE;
import static com.ideas.tetris.ui.modules.reports.reportscheduler.JasperReportConstants.PARAM_ROLLING_START_DATE;
import static com.ideas.tetris.ui.modules.reports.reportscheduler.JasperReportConstants.PARAM_STARTDATE;
import static com.ideas.tetris.ui.modules.reports.reportscheduler.JasperReportConstants.TRUE_VALUE;
import static java.lang.Boolean.FALSE;
import static java.lang.Boolean.TRUE;
import org.springframework.beans.factory.annotation.Autowired;
import com.ideas.tetris.spring.SpringAutowired;

@SpringAutowired
public class RestrictionLevelReportPresenter extends AbstractReportPresenter<RestrictionLevelReportView, Void> {

    public static final String EMPTY_STRING = "";
    public static final String NONE = "none";
    public static final String PARAM_REPORT_TYPE = "param_reportType";
    public static final String PARAM_REPORT_STYLE = "param_reportStyle";
    public static final String PARAM_ROOM_TYPES = "param_roomTypes";
    private static final String PARAM_IS_SRP_FPLOS_AT_TOTAL_LEVEL_ENABLED = "param_isSRPFPLOSAtTotalLevelEnabled";
    private static final String PARAM_QUALIFIED_FPLOS_MAX_LOS = "param_qualifiedFplosMaxLos";
    private static final String PARAM_MINMAX_LOS_DECISION_ENABLED = "param_minMaxLOSDecisionEnabled";
    private static final String PARAM_IS_DECISION_AT_HOTEL_LEVEL = "param_isDecisionAtHotelLevel";
    private static final String PARAM_BUSINESS_DATE = "param_BusinessDt";
    private static final String PARAM_IS_FULL = "param_isFull";
    public static final String PARAM_ROLLING_BUSINESS_DATE = "param_RollingBusinessDate";
    private RestrictionLevelFilterDTO reportFilterDTO;
    private Map<AccomClassSummary, List<AccomTypeSummary>> accomClassSummaries;
    private boolean isSrpFplosAtTotalLevelEnabled;

    @Autowired
	private AccommodationService accommodationServiceLocal;

    @Inject
    private SharedSessionState sharedSessionState;

    @Autowired
	private RestrictionLevelReportService service;

    @Autowired
    PacmanConfigParamsService pacmanConfigParamsServiceLocal;

    public RestrictionLevelReportPresenter() {
    }

    RestrictionLevelReportPresenter(RestrictionLevelReportService service, AccommodationService accommodationServiceLocal, RestrictionLevelFilterDTO reportFilterDTO, SharedSessionState sharedSessionState, UiContext uiContext, PacmanConfigParamsService pacmanConfigParamsServiceLocal) {
        this.accommodationServiceLocal = accommodationServiceLocal;
        this.service = service;
        this.reportFilterDTO = reportFilterDTO;
        this.sharedSessionState = sharedSessionState;
        this.uiContext = uiContext;
        this.pacmanConfigParamsServiceLocal = pacmanConfigParamsServiceLocal;
    }

    @Override
    public void onWorkContextChange(WorkContextType workContextType) {
        super.onWorkContextChange(workContextType);
        sharedSessionState.setRestrictionLevelReportFilterData(null);
        view.removeTable();
        resetFilterData();
    }

    @Override
    protected void resetFilterData() {
        reportFilterDTO = new RestrictionLevelFilterDTO();
        loadInitialData();
        List<AccomClassSummaryUIWrapper> summaries = wrapAccomClassSummaries(new ArrayList<>(accomClassSummaries.keySet()));
        resetFilterDataValue();
        view.setFilterForm(reportFilterDTO);
        view.updateTable(summaries);
        view.setSelectedRoomTypes(reportFilterDTO.getSelectedRoomTypes());
        view.customizeFilterComponents(isMinMaxLosDecisionFeatureEnabled(), isSRPFPLOSAtTotalLevelEnabled());
    }

    private List<AccomClassSummaryUIWrapper> wrapAccomClassSummaries(List<AccomClassSummary> summaries) {
        ArrayList<AccomClassSummaryUIWrapper> amsSummaryUIWrappers = new ArrayList<>();

        for (AccomClassSummary summary : summaries) {
            AccomClassSummaryUIWrapper wrappedSummary = new AccomClassSummaryUIWrapper(summary);
            populateChildren(wrappedSummary);
            amsSummaryUIWrappers.add(wrappedSummary);
        }

        return amsSummaryUIWrappers;
    }

    private void populateChildren(AccomClassSummaryUIWrapper parent) {
        List<AccomTypeSummary> accomTypes = accomClassSummaries.get(parent.getAccomClassSummary());

        ArrayList<AccomClassSummaryUIWrapper> children = new ArrayList<>();
        for (AccomTypeSummary rateCodeSummary : accomTypes) {
            AccomClassSummaryUIWrapper amsSummaryUIWrapper1 = new AccomClassSummaryUIWrapper(rateCodeSummary);
            amsSummaryUIWrapper1.setParent(parent);
            children.add(amsSummaryUIWrapper1);
        }
        parent.setChildren(children);
    }

    private void restoreSessionData(List<AccomClassSummaryUIWrapper> summaries) {
        RestrictionLevelFilterDTO sessionDTO = sharedSessionState.getRestrictionLevelReportFilterData().clone();
        final JavaDateSelectorBean dateSelectorBean = sessionDTO.getDateSelectorBean();
        final JavaDateSelectorBean changeSinceDateSelectorBean = sessionDTO.getChangeSinceDateSelectorBean();

        DateFormatUtil.formatDateInBean(dateSelectorBean);
        DateFormatUtil.formatDateInBean(changeSinceDateSelectorBean);

        reportFilterDTO.setDateSelectorBean(dateSelectorBean);
        reportFilterDTO.setChangeSinceDateSelectorBean(changeSinceDateSelectorBean);

        dateSelectorBean.setDateFormat(DateFormatUtil.getDateFormatString());
        changeSinceDateSelectorBean.setDateFormat(DateFormatUtil.getDateFormatString());

        reportFilterDTO.setReportStyle(sessionDTO.getReportStyle());
        updateRoomClassTypeSelection(summaries, sessionDTO.getSelectedRoomTypes());
        reportFilterDTO.setSelectedRoomTypes(new LinkedHashMap<>(sessionDTO.getSelectedRoomTypes()));
        reportFilterDTO.setReportType(getReportTypeToSet(sessionDTO));
    }

    private String getReportTypeToSet(RestrictionLevelFilterDTO sessionDTO) {
        String reportType = sessionDTO.getReportType();
        if (getText("FPLOS").equals(reportType)) return reportType;
        return isMinMaxLosDecisionFeatureEnabled() ? getText("minmaxlos") : getText("common.min.los");
    }

    private void updateRoomClassTypeSelection(List<AccomClassSummaryUIWrapper> summaries, LinkedHashMap<AccomClassSummaryUIWrapper, List<AccomClassSummaryUIWrapper>> selectedRoomTypes) {
        List<AccomClassSummaryUIWrapper> selectedKeys = new ArrayList<>(selectedRoomTypes.keySet());
        Collection<List<AccomClassSummaryUIWrapper>> selectedValuesList = selectedRoomTypes.values();
        if (!selectedKeys.isEmpty()) {
            List<AccomClassSummaryUIWrapper> selectedRoomTypeList = new ArrayList<>();
            for (List<AccomClassSummaryUIWrapper> roomTypeList : selectedValuesList) {
                selectedRoomTypeList.addAll(roomTypeList);
            }
            for (AccomClassSummaryUIWrapper summary : summaries) {
                if (null == summary.getParent()) {
                    int roomClassIndex = selectedKeys.indexOf(summary);
                    if (roomClassIndex > -1) {
                        summary.setSelected(selectedKeys.get(roomClassIndex).isSelected());
                    }
                }
                for (AccomClassSummaryUIWrapper roomType : summary.getChildren()) {
                    int roomTypeIndex = selectedRoomTypeList.indexOf(roomType);
                    if (roomTypeIndex > -1) {
                        roomType.setSelected(selectedRoomTypeList.get(roomTypeIndex).isSelected());
                    }
                }
            }
        }
    }

    @Override
    protected com.ideas.tetris.pacman.services.scheduledreport.entity.criteria.ReportCriteria getReportCriteria(ScheduledReportDisplayDTO scheduledReportDisplayDTO) {
        return null;
    }

    @Override
    protected ReportType getReportType() {
        return ReportType.RESTRICTION_REPORT;
    }

    @Override
    protected Map<String, String> getReportParameters() {
        Map<String, String> parameterMap = new HashMap<>();
        parameterMap.put(PARAM_ISROLLING_DATE, reportFilterDTO.getDateSelectorBean().isRollingDate() ? ONE : ZERO);
        parameterMap.put(PARAM_STARTDATE, DEFAULT_DATE_FOR_REPORTS);
        parameterMap.put(PARAM_ENDDATE, DEFAULT_DATE_FOR_REPORTS);
        parameterMap.put(PARAM_ROLLING_START_DATE, reportFilterDTO.getDateSelectorBean().getRollingStartDate());
        parameterMap.put(PARAM_ROLLING_END_DATE, reportFilterDTO.getDateSelectorBean().getRollingEndDate());
        parameterMap.put(OUTPUT, OutputType.XLSX.getType());
        parameterMap.put(IS_IGNORE_PAGINATION, TRUE_VALUE);
        parameterMap.put(PARAM_REPORT_TYPE, reportFilterDTO.getReportType());
        parameterMap.put(PARAM_REPORT_STYLE, reportFilterDTO.getReportStyle());
        parameterMap.put(PARAM_IS_SRP_FPLOS_AT_TOTAL_LEVEL_ENABLED, Boolean.toString(isSrpFplosAtTotalLevelEnabled));
        String roomTypes = getSelecetedAccomTypeIds().stream().map(Object::toString).collect(Collectors.joining(","));
        parameterMap.put(PARAM_ROOM_TYPES, roomTypes);
        parameterMap.put(PARAM_QUALIFIED_FPLOS_MAX_LOS, getQualifiedFplosMaxLos().toString());
        parameterMap.put(PARAM_MINMAX_LOS_DECISION_ENABLED, Boolean.toString(isMinMaxLosDecisionFeatureEnabled()));
        parameterMap.put(PARAM_IS_FULL, Boolean.toString(true));
        parameterMap.put(PARAM_ROLLING_BUSINESS_DATE, String.valueOf(reportFilterDTO.getChangeSinceDateSelectorBean().getRollingStartDate()));
        if (!isFull()) {
            parameterMap.put(PARAM_IS_FULL, Boolean.toString(false));
            parameterMap.put(PARAM_BUSINESS_DATE, DateFormatUtil.formatDate(reportFilterDTO.getChangeSinceDateSelectorBean().getSpecificStartDate(), "yyyyMMdd"));
        }
        parameterMap.put(PARAM_IS_DECISION_AT_HOTEL_LEVEL, String.valueOf(isSrpFplosAtTotalLevelEnabled ? 1 : 0));
        return parameterMap;
    }

    @Override
    protected JasperReportTypeEnum getJasperReportType() {
        return isMinMaxLOS() ? JasperReportTypeEnum.RESTRICTION_MIN_MAX_LOS_REPORT :
                JasperReportTypeEnum.RESTRICTION_FP_LOS_REPORT;
    }

    protected void resetFilterDataValue() {
        LocalDate systemCaughtUpDate = uiContext.getSystemCaughtUpDateAsJavaLocalDate();
        JavaDateSelectorBean dateSelectorBean = setStaticStartEndDates();

        dateSelectorBean.setStartRollingDateRange(new RollingDateRange(null, uiContext.getForecastWindowOffsetBDE()));
        dateSelectorBean.setEndRollingDateRange(new RollingDateRange(null, uiContext.getForecastWindowOffsetBDE()));
        dateSelectorBean.setRollingDateOptionMap(new RollingDateOptions().getRollingDateOptionsnMapForSystemDate());
        dateSelectorBean.setSystemCaughtUpDate(systemCaughtUpDate);
        reportFilterDTO.setDateSelectorBean(dateSelectorBean);
        reportFilterDTO.setReportStyle(getText("common.full"));
        reportFilterDTO.setReportType(isMinMaxLosDecisionFeatureEnabled() ? getText("minmaxlos") : getText("common.min.los"));

        LocalDate businessDate = getBusinessDate();
        JavaDateSelectorBean changesSince = new JavaDateSelectorBean(uiContext.getSystemCaughtUpDateAsJavaLocalDate().minusDays(1));
        changesSince.setStartDateCaption(getText("common.changes.since"));
        changesSince.setStartDate(formatDateFor(businessDate));
        changesSince.setEndDate(dateSelectorBean.getStartDate());
        changesSince.getStartStaticDateRange().setEndDate(businessDate);

        changesSince.setStartRollingDateOptions(new RollingDateOptions().getRollingDateOptionsForLastUpdatedDate());
        changesSince.setRollingDateOptionMap(new RollingDateOptions().getRollingDateOptionsnMapForLastUpdatedDate());

        reportFilterDTO.setChangeSinceDateSelectorBean(changesSince);
        reportFilterDTO.setSelectedRoomTypes(new LinkedHashMap<>());
        view.initForm(reportFilterDTO, uiContext.getSystemCaughtUpDateAsJavaLocalDate(), false, new ScheduleReportDTO());
    }

    @Override
    protected boolean isRollingDate() {
        return reportFilterDTO.getDateSelectorBean().isRollingDate();
    }

    @Override
    protected boolean isRollingDateInFuture() {
        String rollingEndDate = reportFilterDTO.getDateSelectorBean().getRollingEndDate();
        return rollingEndDate.indexOf("-") < 0;
    }

    private void addSelectedRoomTypes(Map<String, String> parameters) {
        loadInitialData();
        List<AccomClassSummaryUIWrapper> summaries = wrapAccomClassSummaries(new ArrayList<>(accomClassSummaries.keySet()));
        view.updateTable(summaries);
        Set<String> roomTypeSet = Arrays.stream(parameters.get(PARAM_ROOM_TYPES).split(",")).collect(Collectors.toSet());
        LinkedHashMap<AccomClassSummaryUIWrapper, List<AccomClassSummaryUIWrapper>> selectedRoomTypes = new LinkedHashMap<>();
        for (AccomClassSummaryUIWrapper parent : summaries) {
            if (!parent.hasChildren()) {
                continue;
            }
            selectedRoomTypes.put(parent, new ArrayList<>());
            List<AccomClassSummaryUIWrapper> children = parent.getChildren();
            for (AccomClassSummaryUIWrapper roomType : children) {
                if (roomTypeSet.contains(String.valueOf(roomType.getAccomTypeSummary().getId()))) {
                    roomType.setSelected(true);
                    selectedRoomTypes.get(parent).add(roomType);
                }
            }
            if (selectedRoomTypes.get(parent).size() == parent.getChildren().size()) {
                parent.setSelected(true);
            }
            if (selectedRoomTypes.get(parent).isEmpty()) {
                selectedRoomTypes.remove(parent);
            }
        }
        reportFilterDTO.setSelectedRoomTypes(selectedRoomTypes);
        view.setSelectedRoomTypes(selectedRoomTypes);
    }

    @Override
    protected void editScheduleData(Map<String, String> parameters, Boolean isEdit, ScheduleReportDTO scheduleReportDTO) {
        reportFilterDTO = new RestrictionLevelFilterDTO();
        resetFilterDataValue();
        view.setFilterForm(reportFilterDTO);
        addSelectedRoomTypes(parameters);
        view.customizeFilterComponents(isMinMaxLosDecisionFeatureEnabled(), isSRPFPLOSAtTotalLevelEnabled());
        addDateSelectorBean(parameters);
        if (parameters.get(PARAM_ROLLING_BUSINESS_DATE) != null) {
            addChangesSinceDateSelectorBean(parameters);
        }
        reportFilterDTO.setReportStyle(parameters.get(PARAM_REPORT_STYLE));
        reportFilterDTO.setReportType(parameters.get(PARAM_REPORT_TYPE));
        view.initForm(reportFilterDTO, uiContext.getSystemCaughtUpDateAsJavaLocalDate(), isEdit, scheduleReportDTO);
    }

    private void addDateSelectorBean(Map<String, String> parameters) {
        JavaDateSelectorBean dateSelectorBean = new JavaDateSelectorBean(new RollingDateWithMonthOption());
        dateSelectorBean.setRollingDate(true);
        dateSelectorBean.setStartDate(dateSelectorBean.convertSelectedRollingDateToRollingDateCaption(parameters.get(PARAM_ROLLING_START_DATE), dateSelectorBean.getStartRollingDateOptions()));
        dateSelectorBean.setEndDate(dateSelectorBean.convertSelectedRollingDateToRollingDateCaption(parameters.get(PARAM_ROLLING_END_DATE), dateSelectorBean.getEndRollingDateOptions()));
        dateSelectorBean.setDateFormat(DateFormatUtil.getDateFormatString());
        reportFilterDTO.setDateSelectorBean(dateSelectorBean);
    }

    private void addChangesSinceDateSelectorBean(Map<String, String> parameters) {
        RollingDateWithMonthOption rollingDateWithMonthOption = new RollingDateWithMonthOption();
        rollingDateWithMonthOption.getRollingDateOptionsMap().clear();
        rollingDateWithMonthOption.getRollingDateOptionsMap().put(LAST_UPDATED_DATE, RollingDateOptionBean.ZERO_OFFSET_FOR_LAST_UPDATED);
        rollingDateWithMonthOption.getRollingDateOptionsMap().put(LAST_UPDATED_DATE_MINUS, RollingDateOptionBean.NEGATIVE_OFFSET_FOR_LAST_UPDATED);

        rollingDateWithMonthOption.getStartDateOptions().clear();
        Set<RollingDateOptionBean> rollingDateOptionBeans = new HashSet<>();
        rollingDateOptionBeans.add(RollingDateOptionBean.ZERO_OFFSET_FOR_LAST_UPDATED);
        rollingDateOptionBeans.add(RollingDateOptionBean.NEGATIVE_OFFSET_FOR_LAST_UPDATED);
        rollingDateWithMonthOption.getStartDateOptions().addAll(rollingDateOptionBeans);

        JavaDateSelectorBean changesSinceBean = new JavaDateSelectorBean(rollingDateWithMonthOption);
        changesSinceBean.setStartDateCaption(getText("common.changes.since"));
        changesSinceBean.setRollingDate(true);
        changesSinceBean.setRollingDateString("Last Updated");
        changesSinceBean.setStartDate(changesSinceBean.
                convertSelectedRollingDateToRollingDateCaption(parameters.get(PARAM_ROLLING_BUSINESS_DATE), rollingDateOptionBeans));
        changesSinceBean.setDateFormat(DateFormatUtil.getDateFormatString());
        reportFilterDTO.setChangeSinceDateSelectorBean(changesSinceBean);
    }

    @Override
    protected void loadSharedSessionStateData() {
    }

    @Override
    protected boolean sharedSessionStateDataExist() {
        return false;
    }

    @Override
    public boolean validateSchedule() {
        if (super.validateSchedule()) {
            return validateFilterSelection();
        }
        return false;
    }


    private JavaDateSelectorBean setStaticStartEndDates() {
        LocalDate systemCaughtUpDateAsLocalDate = uiContext.getSystemCaughtUpDateAsJavaLocalDate();
        JavaDateSelectorBean dateSelectorBean = new JavaDateSelectorBean(systemCaughtUpDateAsLocalDate);

        dateSelectorBean.setStartDate(formatDateFor(systemCaughtUpDateAsLocalDate));
        dateSelectorBean.setEndDate(formatDateFor(systemCaughtUpDateAsLocalDate.plusDays(30)));

        dateSelectorBean.setStartStaticDateRange(new JavaDateRange(null, systemCaughtUpDateAsLocalDate.plusDays(uiContext.getForecastWindowOffsetBDE())));
        dateSelectorBean.setEndStaticDateRange(new JavaDateRange(systemCaughtUpDateAsLocalDate, systemCaughtUpDateAsLocalDate.plusDays(uiContext.getForecastWindowOffsetBDE())));

        return dateSelectorBean;
    }

    void loadInitialData() {
        accomClassSummaries = getAccomClassByProperty();
        view.setAccomClassData(accomClassSummaries.keySet());
    }

    protected Map<AccomClassSummary, List<AccomTypeSummary>> getAccomClassByProperty() {
        Map<AccomClassSummary, List<AccomTypeSummary>> allAccomClasses = accommodationServiceLocal.getAllAccomClassWithTypeSummaries();
        Map<AccomClassSummary, List<AccomTypeSummary>> result = new LinkedHashMap<>();

        if (allAccomClasses.isEmpty()) {
            result.put(new AccomClassSummary(-2, getText("common.nodatafound"), true), Collections.<AccomTypeSummary>emptyList());
        } else {
            result.putAll(allAccomClasses);
        }
        return result;
    }

    public List<?> getTableBeans() {
        LinkedList<RestrictionLevelUIDTO> restrictionLevelUIDTOs = convertToUiDTO(service.filterBy(UiUtils.getKeyFor(reportFilterDTO.getReportStyle()),
                UiUtils.getKeyFor(reportFilterDTO.getReportType()),
                reportFilterDTO.getDateSelectorBean().getSpecificStartDate(),
                reportFilterDTO.getDateSelectorBean().getSpecificEndDate(),
                reportFilterDTO.getChangeSinceDateSelectorBean().getSpecificStartDate(),
                getSelecetedAccomTypeIds(), (reportFilterDTO.getDateSelectorBean().isRollingDate() ? 1 : 0),
                reportFilterDTO.getDateSelectorBean().getRollingStartDate(),
                reportFilterDTO.getDateSelectorBean().getRollingEndDate(),
                reportFilterDTO.getChangeSinceDateSelectorBean().getRollingStartDate(), isSrpFplosAtTotalLevelEnabled));
        return restrictionLevelUIDTOs;
    }

    private LinkedList<RestrictionLevelUIDTO> convertToUiDTO(List<RestrictionLevelReportDTO> reportDataOutputs) {
        if (null == reportDataOutputs) {
            reportDataOutputs = new ArrayList<>();
        }
        Map<UtilityDTO, List<RestrictionLevelUIDTO>> outputMap = new LinkedHashMap<>();
        List<RestrictionLevelUIDTO> outputList = new ArrayList<>();
        UtilityDTO utilityDTO;
        LinkedList<RestrictionLevelUIDTO> finalList = new LinkedList<>();

        if (!reportDataOutputs.isEmpty()) {
            RestrictionLevelReportDTO firstElement = reportDataOutputs.get(0);
            firstElement.setDayOfWeek(getText(firstElement.getDayOfWeek().toLowerCase()));
            firstElement.setLrv(view.formatValue(firstElement.getLrv()));
            firstElement.setOccupancyForecast(view.formatValue(firstElement.getOccupancyForecast()));
            firstElement.setRateValue(attachRateType(view.formatValue(firstElement.getRateValue()), firstElement.getRateQualifiedType()));
            utilityDTO = new UtilityDTO(firstElement);
            outputList.add(new RestrictionLevelUIDTO(firstElement));
            outputMap.put(utilityDTO, outputList);

            reportDataOutputs.remove(firstElement);
        }

        for (RestrictionLevelReportDTO output : reportDataOutputs) {
            output.setDayOfWeek(getText(output.getDayOfWeek().toLowerCase()));
            output.setLrv(view.formatValue(output.getLrv()));
            output.setOccupancyForecast(view.formatValue(output.getOccupancyForecast()));
            output.setRateValue(attachRateType(view.formatValue(output.getRateValue()), output.getRateQualifiedType()));
            utilityDTO = new UtilityDTO(output);
            List<RestrictionLevelUIDTO> list = outputMap.get(utilityDTO);
            if (null == list || list.isEmpty()) {
                list = new ArrayList<>();
            }
            RestrictionLevelUIDTO e = new RestrictionLevelUIDTO(output);
            list.add(e);
            outputMap.put(utilityDTO, list);
        }

        for (UtilityDTO dto : outputMap.keySet()) {
            RestrictionLevelUIDTO header = new RestrictionLevelUIDTO(dto.clone());
            header.setColorMe(true);
            List<RestrictionLevelUIDTO> subDetails = outputMap.get(dto);
            Integer maxLosSize = subDetails.get(0).getMaxLosSize();
            if (null != maxLosSize) {
                header.setLosWithValue(createLosWithEmptyValuesFor(maxLosSize));
            }
            finalList.add(header);
            finalList.addAll(subDetails);
        }
        return finalList;
    }

    private LinkedHashMap<String, String> createLosWithEmptyValuesFor(Integer maxLosSize) {
        LinkedHashMap<String, String> losWithEmptyValues = new LinkedHashMap<>();
        int i = 0;
        while (i < maxLosSize) {
            i++;
            losWithEmptyValues.put(EMPTY_STRING + i, EMPTY_STRING);
        }
        return losWithEmptyValues;
    }

    private List<Integer> getSelecetedAccomTypeIds() {
        List<Integer> selectedItemIds = new ArrayList<>();
        if (null == view.getSelectedRoomTypes() || view.getSelectedRoomTypes().isEmpty()) {
            return selectedItemIds;
        }
        for (List<AccomClassSummaryUIWrapper> selectedItems : view.getSelectedRoomTypes().values()) {
            for (AccomClassSummaryUIWrapper selectedItem : selectedItems) {
                selectedItemIds.add(selectedItem.getId());
            }
        }
        return selectedItemIds;
    }

    private RestrictionLevelFilterDTO populateSessionDTO() {
        RestrictionLevelFilterDTO sessionDto = new RestrictionLevelFilterDTO();
        reportFilterDTO.setSelectedRoomTypes(new LinkedHashMap<>(view.getSelectedRoomTypes()));
        RestrictionLevelFilterDTO clonedDto = reportFilterDTO.clone();
        sessionDto.setDateSelectorBean(clonedDto.getDateSelectorBean());
        sessionDto.setChangeSinceDateSelectorBean(clonedDto.getChangeSinceDateSelectorBean());
        sessionDto.setReportStyle(clonedDto.getReportStyle());
        sessionDto.setSelectedRoomTypes(clonedDto.getSelectedRoomTypes());
        sessionDto.setReportType(clonedDto.getReportType());
        return sessionDto;
    }

    public boolean validateFilterSelection() {
        view.removeTable();
        if (!isSrpFplosAtTotalLevelEnabled && (null == view.getSelectedRoomTypes() || view.getSelectedRoomTypes().isEmpty())) {
            view.showWarning(getText("report.filter.warning.selectFilter"));
            return false;
        }
        if (view.getSelectedRoomTypes().size() == 1) {
            Set<AccomClassSummaryUIWrapper> keys = view.getSelectedRoomTypes().keySet();
            Iterator i = keys.iterator();
            AccomClassSummaryUIWrapper first = (AccomClassSummaryUIWrapper) i.next();
            if (first.getId() == -2 || (null == first.getChildren() || first.getChildren().isEmpty())) {
                view.showWarning(getText("report.filter.warning.noRoomType"));
                return false;
            }
            return true;
        }

        return true;
    }

    public boolean isMinMaxLosDecisionFeatureEnabled() {
        return TRUE.toString().equals(pacmanConfigParamsServiceLocal.getParameterValue(FeatureTogglesConfigParamName.MIN_MAX_LOS_DECISION_ENABLED.value()));
    }

    private boolean isUploadTypeOfMinMaxLosByRateCodeIsNotNone() {
        return !NONE.equalsIgnoreCase(pacmanConfigParamsServiceLocal.getParameterValue(IntegrationConfigParamName.MIN_MAX_LOS_BY_RATE_CODE_UPLOADTYPE.value(Constants.REZVIEW)));
    }

    public ReportCriteria getReportCriteria() {
        RestrictionLevelReportCriteria reportCriteria = new RestrictionLevelReportCriteria();
        reportCriteria.setCommonValues(uiContext);
        reportCriteria.setStartDate(reportFilterDTO.getDateSelectorBean().getStartDateForReportCriteria());
        reportCriteria.setEndDate(reportFilterDTO.getDateSelectorBean().getEndDateForReportCriteria());

        if (reportFilterDTO.getReportStyle().equals(getText("common.differential"))) {
            reportCriteria.setChangeSinceDate(reportFilterDTO.getChangeSinceDateSelectorBean().getStartDateForReportCriteria());
        }

        return reportCriteria;
    }

    public void notifyDateSelectorChange(RestrictionLevelFilterDTO restrictionLevelFilterDTO, JavaDateSelectorBean dateSelectorBean) {
        if (dateSelectorBean.isGivenDateSelected(getText("common.changes.since"))) {
            setStartEndDateOnChangeOfChangeSinceDate(restrictionLevelFilterDTO, dateSelectorBean);
        } else {
            setChangeSinceDateOnChangeOfStartDate(restrictionLevelFilterDTO, dateSelectorBean);
        }
        view.dateSelectorReset(restrictionLevelFilterDTO);
    }

    private void setChangeSinceDateOnChangeOfStartDate(RestrictionLevelFilterDTO restrictionLevelFilterDTO, JavaDateSelectorBean dateSelectorBean) {
        JavaDateSelectorBean changeSinceDateSelectorBean = restrictionLevelFilterDTO.getChangeSinceDateSelectorBean();
        if (dateSelectorBean.isRollingDate()) {
            if (!isStartDateInFuture(changeSinceDateSelectorBean.getStartDate(), dateSelectorBean.getStartDate()) || !changeSinceDateSelectorBean.isRollingDate()) {//change since date is dependant on start date and it has to be always less than start date
                changeSinceDateSelectorBean.setRollingDateWith(dateSelectorBean.getStartDate(), dateSelectorBean.getEndDate(), dateSelectorBean.isRollingDate());
            }
        } else {
            if (dateSelectorBean.getSpecificStartDate().isBefore(getBusinessDate()) || dateSelectorBean.getSpecificStartDate().isEqual(getBusinessDate())) {
                LocalDate changesSinceDate = changeSinceDateSelectorBean.getSpecificStartDate();
                changeSinceDateSelectorBean.getStartStaticDateRange().setEndDate(dateSelectorBean.getSpecificStartDate().minusDays(1));
                if (dateSelectorBean.getSpecificStartDate().isBefore(changesSinceDate) || dateSelectorBean.getSpecificStartDate().isEqual(changesSinceDate)) {
                    changeSinceDateSelectorBean.setStartDate(formatDateFor(dateSelectorBean.getSpecificStartDate().minusDays(1)));
                    changeSinceDateSelectorBean.setEndDate(formatDateFor(dateSelectorBean.getSpecificStartDate().minusDays(1)));
                    changeSinceDateSelectorBean.getStartStaticDateRange().setEndDate(dateSelectorBean.getSpecificStartDate().minusDays(1));
                }
            } else {
                if (!changeSinceDateSelectorBean.getSpecificStartDate().isBefore(getBusinessDate())) {
                    changeSinceDateSelectorBean.setStartDate(formatDateFor(getBusinessDate()));
                    changeSinceDateSelectorBean.setEndDate(formatDateFor(getBusinessDate()));
                }
                changeSinceDateSelectorBean.getStartStaticDateRange().setEndDate(getBusinessDate());
            }
            changeSinceDateSelectorBean.setRollingDate(dateSelectorBean.isRollingDate());
        }
    }

    protected String formatDateFor(LocalDate localDate) {
        return DateFormatUtil.formatDateFor(localDate, UiUtils.getLocale());
    }

    private boolean isStartDateInFuture(String startDateOfDependantField, String startDateOfSourceField) {
        String rollingDateStart = startDateOfSourceField;
        String rollingDateEnd = startDateOfDependantField;

        int startDateOffset = 0;
        int endDateOffset = 0;

        boolean startDateContainsPlusOperator = FALSE;
        boolean endDateContainsPlusOperator = FALSE;
        boolean startDateContainsNoOperator = FALSE;
        boolean endDateContainsNoOperator = FALSE;

        if (rollingDateStart.contains(PLUS)) {
            startDateOffset = Integer.parseInt(rollingDateStart.substring(rollingDateStart.indexOf(PLUS.trim()) + 1, rollingDateStart.length()).trim());
            startDateContainsPlusOperator = TRUE;
        } else if (rollingDateStart.contains(MINUS)) {
            startDateOffset = Integer.parseInt(rollingDateStart.substring(rollingDateStart.indexOf(MINUS.trim()) + 1, rollingDateStart.length()).trim());
            startDateContainsPlusOperator = FALSE;
        } else {
            startDateContainsNoOperator = TRUE;
        }

        if (rollingDateEnd.contains(PLUS)) {
            endDateOffset = Integer.parseInt(rollingDateEnd.substring(rollingDateEnd.indexOf(PLUS.trim()) + 1, rollingDateEnd.length()).trim());
            endDateContainsPlusOperator = TRUE;
        } else if (rollingDateEnd.contains(MINUS)) {
            endDateOffset = Integer.parseInt(rollingDateEnd.substring(rollingDateEnd.indexOf(MINUS.trim()) + 1, rollingDateEnd.length()).trim());
            endDateContainsPlusOperator = FALSE;
        } else {
            endDateContainsNoOperator = TRUE;
        }

        if (startDateContainsPlusOperator && endDateContainsNoOperator) {
            return true;
        } else if (startDateContainsNoOperator && !endDateContainsPlusOperator) {
            return true;
        }

        if (startDateContainsPlusOperator && endDateContainsPlusOperator) {
            return endDateOffset < startDateOffset;
        } else if (!startDateContainsPlusOperator && !endDateContainsPlusOperator) {
            return endDateOffset > startDateOffset;
        } else if (!startDateContainsPlusOperator && endDateContainsPlusOperator) {
            return false;
        } else if (startDateContainsPlusOperator && !endDateContainsPlusOperator) {
            return true;
        } else {
            return false;
        }
    }

    private void setStartEndDateOnChangeOfChangeSinceDate(RestrictionLevelFilterDTO restrictionLevelFilterDTO, JavaDateSelectorBean changeSinceBean) {
        JavaDateSelectorBean startEndDateSelectorBean = restrictionLevelFilterDTO.getDateSelectorBean();
        if (changeSinceBean.isRollingDate()) {
            if (!startEndDateSelectorBean.isRollingDate()) {
                startEndDateSelectorBean.setRollingDateWith(changeSinceBean.getStartDate(), changeSinceBean.getEndDate(), changeSinceBean.isRollingDate());
            } else if (!isStartDateInFuture(changeSinceBean.getStartDate(), startEndDateSelectorBean.getStartDate())) {
                startEndDateSelectorBean.setRollingDateWith(changeSinceBean.getStartDate(), changeSinceBean.getEndDate(), changeSinceBean.isRollingDate());
            }
        } else if (startEndDateSelectorBean.isRollingDate()) {
            startEndDateSelectorBean.setSpecificDateWith(new Long(30), uiContext.getSystemCaughtUpDateAsJavaLocalDate());
        }
    }

    protected LocalDate getBusinessDate() {
        return uiContext.getSystemCaughtUpDateAsJavaLocalDate().minusDays(1);
    }


    public boolean isMinMaxLOS() {
        String reportType = reportFilterDTO.getReportType();
        return reportType.equals(getText("common.min.los")) || reportType.equals(getText("minmaxlos"));
    }

    public boolean isFull() {
        return reportFilterDTO.getReportStyle().equalsIgnoreCase(UiUtils.getText("common.full"));
    }

    public boolean isAllowedToAddMaxLosColumn() {
        return isMinMaxLosDecisionFeatureEnabled() && isUploadTypeOfMinMaxLosByRateCodeIsNotNone();
    }

    public String attachRateType(String rateValue, String rateType) {
        if (!Constants.REPORT_HYPHEN.equals(rateValue) &&
                pacmanConfigParamsServiceLocal.getBooleanParameterValue(FeatureTogglesConfigParamName.IS_DERIVED_QUALIFIED_RATE_PLAN_ENABLED.value())) {
            return new StringBuilder(rateValue).append(" ")
                    .append(Constants.RATE_QUALIFIED_TYPE_SYMBOL.getCaptionOf(rateType)).toString();
        }
        return rateValue;
    }

    public boolean isSRPFPLOSAtTotalLevelEnabled() {
        isSrpFplosAtTotalLevelEnabled = pacmanConfigParamsServiceLocal.getBooleanParameterValue(IntegrationConfigParamName.SRP_FPLOS_AT_TOTAL_LEVEL.value());
        return isSrpFplosAtTotalLevelEnabled;
    }

    private Integer getQualifiedFplosMaxLos() {
        return pacmanConfigParamsServiceLocal.getIntegerParameterValue(IntegrationConfigParamName.CORE_PROPERTY_QUALIFIED_FPLOSMAX_LOS.value());
    }
}
