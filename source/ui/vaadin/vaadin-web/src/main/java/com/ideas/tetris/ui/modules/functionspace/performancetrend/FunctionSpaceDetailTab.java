package com.ideas.tetris.ui.modules.functionspace.performancetrend;

import com.ideas.tetris.ui.common.component.TetrisBeanItemContainer;
import com.ideas.tetris.ui.common.component.button.TetrisExportXlsImageButton;
import com.ideas.tetris.ui.common.component.table.TetrisTable;
import com.ideas.tetris.ui.common.util.UiUtils;
import com.vaadin.ui.Alignment;
import com.vaadin.ui.CustomComponent;
import com.vaadin.v7.ui.Table;
import com.vaadin.v7.ui.VerticalLayout;

import java.util.List;

public class FunctionSpaceDetailTab extends CustomComponent implements FunctionSpacePerformanceTrend {
    private final TetrisBeanItemContainer<FunctionSpaceDetailsReportDtoUiWrapper> functionSpaceDetailsReportDtoTetrisBeanItemContainer;

    public FunctionSpaceDetailTab(FunctionSpacePerformanceTrendPresenter presenter) {
        setSizeFull();
        this.addStyleName("tabView");
        functionSpaceDetailsReportDtoTetrisBeanItemContainer = new TetrisBeanItemContainer<FunctionSpaceDetailsReportDtoUiWrapper>(FunctionSpaceDetailsReportDtoUiWrapper.class);
        functionSpaceDetailsReportDtoTetrisBeanItemContainer.addNestedContainerBean("dto");
        String dayOfWeek = "dayOfWeek";
        String occupancyDate = "dto.occupancyDate";
        String actualUtilization = "dto.onBooksUtilization";
        String utilizationForecast = "dto.forecastUtilization";
        String utilizationWithProspects = "dto.onBooksUtilizationWithProspects";
        String actualUtilizationLastYear = "dto.lastYearOnBooksUtilization";
        String revPast = "dto.revPast";
        String revPost = "dto.revPost";
        String proPast = "dto.proPast";
        String proPost = "dto.proPost";

        TetrisTable table = new TetrisTable("", functionSpaceDetailsReportDtoTetrisBeanItemContainer);
        table.setVisibleColumns(new String[]{dayOfWeek, occupancyDate, actualUtilization, utilizationForecast, utilizationWithProspects, actualUtilizationLastYear, revPast, revPost, proPast, proPost});
        table.setColumnHeader(dayOfWeek, UiUtils.getText("common.dow"));
        table.setColumnHeader(occupancyDate, UiUtils.getText("occupancyDate"));
        table.setColumnHeader(actualUtilization, UiUtils.getText("groupPricing.demandCalendar.utilization%Actual"));
        table.setColumnHeader(utilizationForecast, UiUtils.getText("groupEvaluation.utilization.forecast") + " %");
        table.setColumnHeader(utilizationWithProspects, UiUtils.getText("groupPricing.forecastReview.utilizationWithProspects") + " %");
        table.setColumnHeader(actualUtilizationLastYear, UiUtils.getText("groupPricing.forecastReview.actualUtilizationLastYear") + " %");
        table.setColumnHeader(revPast, UiUtils.getText("groupPricing.demandCalendar.revPast"));
        table.setColumnHeader(revPost, UiUtils.getText("groupPricing.demandCalendar.revPost"));
        table.setColumnHeader(proPast, UiUtils.getText("groupPricing.demandCalendar.proPast"));
        table.setColumnHeader(proPost, UiUtils.getText("groupPricing.demandCalendar.proPost"));

        table.setColumnAlignment(actualUtilization, Table.Align.RIGHT);
        table.setColumnAlignment(utilizationForecast, Table.Align.RIGHT);
        table.setColumnAlignment(utilizationWithProspects, Table.Align.RIGHT);
        table.setColumnAlignment(actualUtilizationLastYear, Table.Align.RIGHT);
        table.setColumnAlignment(revPast, Table.Align.RIGHT);
        table.setColumnAlignment(revPost, Table.Align.RIGHT);
        table.setColumnAlignment(proPast, Table.Align.RIGHT);
        table.setColumnAlignment(proPost, Table.Align.RIGHT);

        table.setSizeFull();
        TetrisExportXlsImageButton exportXlsImageButton = new TetrisExportXlsImageButton(table, UiUtils.getText("groupEvaluation.functionSpaceDetail"));
        VerticalLayout verticalLayout = new VerticalLayout(exportXlsImageButton, table);
        verticalLayout.setMargin(true);
        verticalLayout.setExpandRatio(table, 1.0f);
        verticalLayout.setComponentAlignment(exportXlsImageButton, Alignment.MIDDLE_RIGHT);
        verticalLayout.setSizeFull();
        setCompositionRoot(verticalLayout);
    }

    @Override
    public void setData(List<FunctionSpaceDetailsReportDtoUiWrapper> data) {
        functionSpaceDetailsReportDtoTetrisBeanItemContainer.removeAllItems();
        functionSpaceDetailsReportDtoTetrisBeanItemContainer.addAll(data);
    }
}
