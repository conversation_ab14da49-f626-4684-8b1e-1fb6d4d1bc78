package com.ideas.tetris.ui.modules.internalalert;

import com.ideas.tetris.ui.common.component.TetrisBeanItemContainer;
import com.ideas.tetris.ui.common.component.table.TetrisTable;
import com.ideas.tetris.ui.modules.reports.BaseTableExporter;
import com.vaadin.v7.ui.Table;
import net.sf.dynamicreports.jasper.builder.JasperReportBuilder;
import net.sf.dynamicreports.report.base.datatype.AbstractDataType;
import net.sf.dynamicreports.report.base.expression.AbstractValueFormatter;
import net.sf.dynamicreports.report.builder.DynamicReports;
import net.sf.dynamicreports.report.builder.column.TextColumnBuilder;
import net.sf.dynamicreports.report.definition.ReportParameters;
import net.sf.jasperreports.engine.JRException;
import net.sf.jasperreports.engine.JRField;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.LocalDateTime;

import java.text.SimpleDateFormat;
import java.util.Locale;

public class InternalAlertExporter extends BaseTableExporter {
    private static final String DATE_FORMAT_PROPERTY_TIME = "dd-MMM-yyyy HH:mm";

    @SuppressWarnings({"unchecked", "rawtypes"})
    @Override
    public JasperReportBuilder createReportBuilder(Table table2, String reportTitle) {
        TetrisTable table = (TetrisTable) table2;
        TetrisBeanItemContainer<InternalAlertDisplayDto> container =
                (TetrisBeanItemContainer<InternalAlertDisplayDto>) table.getContainerDataSource();
        JRBeanCollectionDataSource dataSource = new JRBeanCollectionDataSource(container.getItems()) {
            @Override
            protected Object getFieldValue(Object bean, JRField field) throws JRException {
                String stage = "stage";
                if (field != null && StringUtils.equals(stage, field.getName())) {
                    String stageCode = (String) super.getFieldValue(bean, field);
                    if (StringUtils.isNotBlank(stageCode)) {
                        return getText(stageCode.toLowerCase());
                    }
                }
                return super.getFieldValue(bean, field);
            }
        };
        JasperReportBuilder reportBuilder = tetrisReport(reportTitle);

        LocalDateTimeDataType localDateTimeDataType = new LocalDateTimeDataType();
        LocalDateTimeDataFormatter localDateTimeDataFormatter = new LocalDateTimeDataFormatter();

        String internalAlertId = "internalAlertId";
        if (!table.isColumnCollapsed(internalAlertId)) {
            reportBuilder.addColumn(DynamicReports.col.column(table.getColumnHeaderNonHtml(internalAlertId), internalAlertId,
                    DynamicReports.type.integerType()));
        }

        String alertType = "alertType";
        if (!table.isColumnCollapsed(alertType)) {
            reportBuilder.addColumn(DynamicReports.col.column(table.getColumnHeaderNonHtml(alertType), alertType,
                    DynamicReports.type.stringType()));
        }

        String details = "details";
        if (!table.isColumnCollapsed(details)) {
            reportBuilder.addColumn(DynamicReports.col.column(table.getColumnHeaderNonHtml(details), details,
                    DynamicReports.type.stringType()));
        }

        String alertStatus = "alertStatus";
        if (!table.isColumnCollapsed(alertStatus)) {
            reportBuilder.addColumn(DynamicReports.col.column(table.getColumnHeaderNonHtml(alertStatus), alertStatus,
                    DynamicReports.type.stringType()));
        }

        String createdDate = "createdDate";
        if (!table.isColumnCollapsed(createdDate)) {
            TextColumnBuilder createdDateColumn = DynamicReports.col.column(
                    table.getColumnHeaderNonHtml(createdDate), createdDate, localDateTimeDataType);
            createdDateColumn.setValueFormatter(localDateTimeDataFormatter);
            reportBuilder.addColumn(createdDateColumn);
        }

        String clientName = "clientName";
        if (!table.isColumnCollapsed(clientName)) {
            reportBuilder.addColumn(DynamicReports.col.column(table.getColumnHeaderNonHtml(clientName), clientName,
                    DynamicReports.type.stringType()));
        }

        String clientCode = "clientCode";
        if (!table.isColumnCollapsed(clientCode)) {
            reportBuilder.addColumn(DynamicReports.col.column(table.getColumnHeaderNonHtml(clientCode), clientCode,
                    DynamicReports.type.stringType()));
        }

        String propertyName = "propertyName";
        if (!table.isColumnCollapsed(propertyName)) {
            reportBuilder.addColumn(DynamicReports.col.column(table.getColumnHeaderNonHtml(propertyName), propertyName,
                    DynamicReports.type.stringType()));
        }

        String propertyCode = "propertyCode";
        if (!table.isColumnCollapsed(propertyCode)) {
            reportBuilder.addColumn(DynamicReports.col.column(table.getColumnHeaderNonHtml(propertyCode), propertyCode,
                    DynamicReports.type.stringType()));
        }

        String stage = "stage";
        if (!table.isColumnCollapsed(stage)) {
            //          String systemMode = stage == null ? "" : getText(stage.toLowerCase());
            reportBuilder.addColumn(DynamicReports.col.column(table.getColumnHeaderNonHtml(stage), stage,
                    DynamicReports.type.stringType()));
        }

        String notifiedDate = "notifiedDate";
        if (!table.isColumnCollapsed(notifiedDate)) {
            TextColumnBuilder notifiedDateColumn = DynamicReports.col.column(
                    table.getColumnHeaderNonHtml(notifiedDate), notifiedDate, localDateTimeDataType);
            notifiedDateColumn.setValueFormatter(localDateTimeDataFormatter);
            reportBuilder.addColumn(notifiedDateColumn);
        }

        String resolvedDate = "resolvedDate";
        if (!table.isColumnCollapsed(resolvedDate)) {
            TextColumnBuilder resolvedDateColumn = DynamicReports.col.column(
                    table.getColumnHeaderNonHtml(resolvedDate), resolvedDate, localDateTimeDataType);
            resolvedDateColumn.setValueFormatter(localDateTimeDataFormatter);
            reportBuilder.addColumn(resolvedDateColumn);
        }

        reportBuilder.setDataSource(dataSource);
        return reportBuilder;
    }

    @SuppressWarnings("serial")
    class LocalDateTimeDataType extends AbstractDataType<LocalDateTime, LocalDateTime> {

        @Override
        public String valueToString(LocalDateTime dateTime, Locale locale) {
            return new SimpleDateFormat(DATE_FORMAT_PROPERTY_TIME).format(dateTime.toDate());
        }
    }

    @SuppressWarnings("serial")
    class LocalDateTimeDataFormatter extends AbstractValueFormatter<String, LocalDateTime> {

        @Override
        public String format(LocalDateTime dateTime, ReportParameters reportParameters) {
            return new SimpleDateFormat(DATE_FORMAT_PROPERTY_TIME).format(dateTime.toDate());
        }
    }

}
