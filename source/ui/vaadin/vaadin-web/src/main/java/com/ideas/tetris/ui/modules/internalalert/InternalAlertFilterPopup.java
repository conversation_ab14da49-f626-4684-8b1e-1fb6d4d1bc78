package com.ideas.tetris.ui.modules.internalalert;

import com.ideas.tetris.pacman.services.internalalert.InternalAlertStatus;
import com.ideas.tetris.pacman.services.internalalert.InternalAlertType;
import com.ideas.tetris.platform.services.daoandentities.entity.Client;
import com.ideas.tetris.platform.services.daoandentities.entity.Property;
import com.ideas.tetris.ui.common.component.TetrisBeanItemContainer;
import com.ideas.tetris.ui.common.component.filter.FilterComponent;
import com.ideas.tetris.ui.common.component.filter.ResetToDefault;
import com.ideas.tetris.ui.common.component.select.ItemCaptionGenerator;
import com.ideas.tetris.ui.common.component.select.TetrisComboBox;
import com.ideas.tetris.ui.common.component.select.TetrisListSelect;
import com.ideas.tetris.ui.common.component.select.TetrisQuickFilterComboBox;
import com.ideas.tetris.ui.common.component.textfield.TetrisLocalDateTimeField;
import com.ideas.tetris.ui.common.data.fieldgroup.TetrisBeanFieldGroup;
import com.ideas.tetris.ui.modules.installation.property.PropertyInstallationPresenter;
import com.vaadin.v7.ui.AbstractSelect;
import com.vaadin.v7.ui.HorizontalLayout;
import com.vaadin.v7.ui.VerticalLayout;
import org.apache.log4j.Logger;

import java.util.Arrays;
import java.util.List;

@SuppressWarnings("serial")
public class InternalAlertFilterPopup extends VerticalLayout implements FilterComponent, ResetToDefault {
    private static final Logger LOGGER = Logger.getLogger(InternalAlertFilterPopup.class);
    private TetrisBeanItemContainer<Property> propertyCodeBeanContainer;
    private TetrisBeanItemContainer<Property> propertyNameBeanContainer;
    private TetrisQuickFilterComboBox propertyNameComboBox;
    private TetrisQuickFilterComboBox propertyCodeComboBox;
    private final TetrisBeanFieldGroup<InternalAlertFilterDto> fieldGroup;
    TetrisLocalDateTimeField startDate;
    TetrisLocalDateTimeField endDate;
    private AlertDetailsPresenter presenter;


    public InternalAlertFilterPopup(AlertDetailsPresenter presenter) {
        this.presenter = presenter;

        fieldGroup = new TetrisBeanFieldGroup<InternalAlertFilterDto>(InternalAlertFilterDto.class);

        setSizeFull();
        setMargin(true);
        HorizontalLayout firstRowLayout = new HorizontalLayout();
        firstRowLayout.setSpacing(true);
        addFirstRowComponents(firstRowLayout);
        addComponent(firstRowLayout);
        HorizontalLayout secondRowLayout = new HorizontalLayout();
        secondRowLayout.setSpacing(true);
        addSecondRowComponents(secondRowLayout);
        addComponent(secondRowLayout);
        HorizontalLayout thirdRowLayout = new HorizontalLayout();
        thirdRowLayout.setSpacing(true);
        addComponent(thirdRowLayout);

    }

    private void addFirstRowComponents(HorizontalLayout firstRowLayout) {
        addClientComboBox(firstRowLayout);
        addPropertyComboBoxes(firstRowLayout);
    }

    private void addSecondRowComponents(HorizontalLayout secondRowLayout) {
        VerticalLayout dateLayout = new VerticalLayout();
        addStartDateAndEndDateSelectors(dateLayout);
        secondRowLayout.addComponent(dateLayout);

        TetrisBeanItemContainer<String> alertTypes = new TetrisBeanItemContainer<String>(String.class);
        Arrays.asList(InternalAlertType.values()).stream()
                .map(Enum::toString)
                .sorted()
                .forEach(alertTypes::addItem);
        TetrisListSelect typeListSelect = new TetrisListSelect("Type", alertTypes);
        typeListSelect.setItemCaptionGenerator(new ItemCaptionGenerator() {
            @Override
            public String getItemCaption(AbstractSelect source, Object itemId) {
                return (String) itemId;
            }
        });
        typeListSelect.setRequired(false);
        typeListSelect.setMultiSelect(true);
        typeListSelect.setNullSelectionAllowed(true);
        typeListSelect.setRows(alertTypes.size());

        secondRowLayout.addComponent(typeListSelect);
        fieldGroup.bind(typeListSelect, "alertTypes");

        TetrisBeanItemContainer<String> statuses = new TetrisBeanItemContainer<String>(String.class);
        for (InternalAlertStatus status : InternalAlertStatus.values()) {
            statuses.addItem(status.toString());
        }
        TetrisListSelect statusListSelect = new TetrisListSelect("Status", statuses);
        statusListSelect.setItemCaptionGenerator(new ItemCaptionGenerator() {
            @Override
            public String getItemCaption(AbstractSelect source, Object itemId) {
                return itemId.toString();
            }
        });
        statusListSelect.setRequired(false);
        statusListSelect.setMultiSelect(true);
        statusListSelect.setNullSelectionAllowed(true);
        statusListSelect.setRows(InternalAlertStatus.values().length);

        secondRowLayout.addComponent(statusListSelect);
        fieldGroup.bind(statusListSelect, "alertStatuses");

    }

    private void addStartDateAndEndDateSelectors(VerticalLayout verticalLayout) {
        HorizontalLayout layout = new HorizontalLayout();
        layout.setSpacing(true);
        startDate = new TetrisLocalDateTimeField("Created Start Date");
        startDate.setDateFormat("MM/dd/yyyy HH:mm");
        startDate.setWidth(170, Unit.PIXELS);
        startDate.setRequired(false);
        fieldGroup.bind(startDate, "startDate");
        endDate = new TetrisLocalDateTimeField("Created End Date");
        endDate.setDateFormat("MM/dd/yyyy HH:mm");
        endDate.setWidth(170, Unit.PIXELS);
        endDate.setRequired(false);
        fieldGroup.bind(endDate, "endDate");
        layout.addComponent(startDate);
        layout.addComponent(endDate);
        verticalLayout.addComponent(layout);
    }

    private void addClientComboBox(HorizontalLayout horizontalLayout) {
        TetrisBeanItemContainer<Client> clientBeanContainer = new TetrisBeanItemContainer<Client>(Client.class);
        clientBeanContainer.addAll(presenter.getClients());

        final TetrisComboBox clientField = new TetrisComboBox("Client Code");
        clientField.setItemCaptionPropertyId("name");
        clientField.setContainerDataSource(clientBeanContainer);
        clientField.setRequired(false);
        clientField.setNullSelectionAllowed(false);
        clientField.addValueChangeListener(new com.vaadin.v7.data.Property.ValueChangeListener() {
            @Override
            public void valueChange(com.vaadin.v7.data.Property.ValueChangeEvent event) {
                Client value = (Client) clientField.getValue();
                propertyCodeBeanContainer.removeAllItems();
                propertyNameBeanContainer.removeAllItems();
                if (value != null) {
                    List<Property> properties = presenter.getProperties(value);
                    propertyCodeBeanContainer.addAll(properties);
                    propertyNameBeanContainer.addAll(properties);
                    InternalAlertFilterDto filterDto = fieldGroup.getBean();
                    filterDto.setPropertyByCode(PropertyInstallationPresenter.ALL_PROPERTIES);
                    filterDto.setPropertyByName(PropertyInstallationPresenter.ALL_PROPERTIES);
                }
            }
        });

        fieldGroup.bind(clientField, "client");

        horizontalLayout.addComponent(clientField);
    }

    private void addPropertyComboBoxes(HorizontalLayout horizontalLayout) {
        propertyCodeBeanContainer = new TetrisBeanItemContainer<Property>(Property.class);
        propertyNameBeanContainer = new TetrisBeanItemContainer<Property>(Property.class);

        propertyCodeComboBox = new TetrisQuickFilterComboBox("Property Code", propertyCodeBeanContainer);
        propertyNameComboBox = new TetrisQuickFilterComboBox("Property Name", propertyNameBeanContainer);

        propertyCodeComboBox.setRequired(false);
        propertyCodeComboBox.setNullSelectionAllowed(false);
        propertyCodeComboBox.setItemCaptionPropertyId("code");
        propertyCodeComboBox.addValueChangeListener(new com.vaadin.v7.data.Property.ValueChangeListener() {
            @Override
            public void valueChange(com.vaadin.v7.data.Property.ValueChangeEvent valueChangeEvent) {
                propertyNameComboBox.select(valueChangeEvent.getProperty().getValue());
            }
        });
        fieldGroup.bind(propertyCodeComboBox, "propertyByCode");
        horizontalLayout.addComponent(propertyCodeComboBox);

        propertyNameComboBox.setRequired(false);
        propertyNameComboBox.setNullSelectionAllowed(false);
        propertyNameComboBox.setItemCaptionPropertyId("name");
        propertyNameComboBox.setWidth(400, Unit.PIXELS);
        propertyNameComboBox.addValueChangeListener(new com.vaadin.v7.data.Property.ValueChangeListener() {
            @Override
            public void valueChange(com.vaadin.v7.data.Property.ValueChangeEvent valueChangeEvent) {
                Property selectedProperty = (Property) valueChangeEvent.getProperty().getValue();
                propertyCodeComboBox.select(selectedProperty);
            }
        });
        fieldGroup.bind(propertyNameComboBox, "propertyByName");
        horizontalLayout.addComponent(propertyNameComboBox);
    }

    @Override
    public Object getPropertyId() {
        return null;
    }

    @Override
    public Object getFilterResult() {
        return fieldGroup.getBean();
    }

    public void updateFilters(InternalAlertFilterDto filterDto) {
        InternalAlertFilterDto clone = null;
        try {
            clone = (InternalAlertFilterDto) filterDto.clone();
            clone.setInternalAlertIds(null);
            fieldGroup.setItemDataSource(clone);
        } catch (CloneNotSupportedException e) {
            LOGGER.debug("Cloning is not supported on given object", e);
        }
    }

    @Override
    public void resetToDefault() {
        updateFilters(presenter.generateDefaultFilter());
    }

}
