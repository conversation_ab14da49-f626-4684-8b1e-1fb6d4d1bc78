package com.ideas.tetris.ui.modules.functionspace.forecastreview;

import com.ideas.tetris.pacman.services.functionspace.configuration.entity.FunctionSpaceDayPart;
import com.ideas.tetris.pacman.services.functionspace.configuration.service.FunctionSpaceConfigurationService;
import com.ideas.tetris.pacman.services.functionspace.dto.FunctionSpaceDemandCalendarDateDto;
import com.ideas.tetris.pacman.services.functionspace.dto.FunctionSpaceDemandCalendarDatePartDto;
import com.ideas.tetris.pacman.services.functionspace.forecastreview.service.FunctionSpaceForecastReviewService;
import com.ideas.tetris.pacman.services.notes.NotesService;
import com.ideas.tetris.pacman.services.notes.entity.DateNote;
import com.ideas.tetris.platform.common.xml.schema.workcontext.v1.WorkContextType;
import com.ideas.tetris.ui.common.cdi.TetrisPresenter;
import com.ideas.tetris.ui.common.component.date.customdateselector.DateSelectorBean;
import com.ideas.tetris.ui.common.util.DateFormatUtil;
import com.ideas.tetris.ui.common.util.DateUtil;
import com.ideas.tetris.ui.common.util.UiUtils;
import com.ideas.tetris.ui.modules.functionspace.demandcalendar.calendar.FunctionSpaceDemandCalendarMockData;
import com.ideas.tetris.ui.modules.functionspace.forecastreview.filter.FilterDto;
import com.ideas.tetris.ui.modules.functionspace.forecastreview.filter.ForecastReviewFilterUtil;
import org.joda.time.LocalDate;

import javax.inject.Inject;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import com.ideas.tetris.spring.SpringAutowired;

@SpringAutowired
public class ForecastReviewPresenter extends TetrisPresenter<ForecastReviewView, Void> {
    @Autowired
    FunctionSpaceForecastReviewService functionSpaceForecastReviewService;
    @Autowired
    FunctionSpaceConfigurationService functionSpaceConfigurationService;
    @Autowired
	private NotesService notesService;

    private FilterDto filterDto;
    private List<DateNote> notes;

    @Override
    public void onViewOpened(Void aVoid) {
        init();
    }

    @Override
    public void onWorkContextChange(WorkContextType workContextType) {
        init();
    }

    private void init() {
        view.resetFilter();
        loadData(getDefaultFilter());
    }

    public void loadData(FilterDto bean) {
        this.filterDto = bean;
        updateNotes();
        Map<LocalDate, FunctionSpaceDemandCalendarDateDto> forecastsByCriteria = functionSpaceForecastReviewService.getForecastsByCriteria(bean.getCriteria());
        view.updateContainer(createForecastReviewUiWrapper(forecastsByCriteria));
    }

    private List<ForecastReviewUiWrapper> createForecastReviewUiWrapper(Map<LocalDate, FunctionSpaceDemandCalendarDateDto> forecasts) {
        ArrayList<ForecastReviewUiWrapper> forecastReviewUiWrappers = new ArrayList<ForecastReviewUiWrapper>();

        //Note that we are doing client side filtering here.  This is because we need all dayparts for a given date to do our calculations at the daylevel.
        //So we might be only showing a few day parts in the UI, but in order to do some calculations we need all dayparts.
        forecastReviewUiWrappers.addAll(ForecastReviewFilterUtil.filterForecasts(forecasts, filterDto));

        return forecastReviewUiWrappers;
    }


    public FilterDto getDefaultFilter() {
        if (filterDto == null) {
            filterDto = new FilterDto();
            DateSelectorBean dateSelectorBean = new DateSelectorBean(getSystemDateAsLocalDate());
            dateSelectorBean.setStartDate(DateFormatUtil.formatDateFor(getSystemDateAsLocalDate(), UiUtils.getLocale()));
            dateSelectorBean.setEndDate(DateFormatUtil.formatDateFor(getSystemDateAsLocalDate().plusDays(30), UiUtils.getLocale()));
            filterDto.setDateSelector(dateSelectorBean);
        }
        return filterDto;
    }

    public List<FunctionSpaceDayPart> getDayParts() {
        List<FunctionSpaceDayPart> dayParts = new ArrayList<FunctionSpaceDayPart>();
        if (mockModeEnabled) {
            dayParts = FunctionSpaceDemandCalendarMockData.getDayParts();
        } else {
            for (FunctionSpaceDayPart dayPart : functionSpaceConfigurationService.getDayParts()) {
                if (dayPart.isIncluded()) {
                    dayParts.add(dayPart);
                }
            }
        }
        return dayParts;
    }

    public void onSave(ForecastReviewUiWrapper wrapper) {
        FunctionSpaceDemandCalendarDateDto dto = new FunctionSpaceDemandCalendarDateDto(wrapper.getOccupancyDate(), wrapper.getConfiguredForecastedLevels());

        for (ForecastReviewUiWrapper childWrapper : wrapper.getAllChildren()) {
            if (childWrapper.hasChanges() || childWrapper.isDeleted()) {
                FunctionSpaceDemandCalendarDatePartDto datePartDto = childWrapper.getDatePartDto();

                if (childWrapper.isDeleted() && childWrapper.getUserAdjustedUtilization().equals(childWrapper.getForecastedUtilization())) {
                    datePartDto.setDelete(childWrapper.isDeleted());
                } else {
                    datePartDto.setUpdateUtilization(!childWrapper.getGetOriginalUserUtilization().equals(childWrapper.getUserAdjustedUtilization()));
                    datePartDto.setUserUtilization(childWrapper.getUserAdjustedUtilization().divide(new BigDecimal(100)));
                }
                datePartDto.setUpdateFunctionBusinessStatus(!childWrapper.getStatus().equals(datePartDto.getFunctionSpaceDemandCalendarDateStatus()));
                datePartDto.setFunctionSpaceDemandCalendarDateStatus(childWrapper.getStatus());

                dto.addDatePartDto(datePartDto);
            }
        }

        if (!dto.getFunctionSpaceDemandCalendarDatePartDtos().isEmpty()) {
            functionSpaceForecastReviewService.save(dto);
            view.showSaveSuccessMessage();
            init();//refresh ui
        } else {
            view.showWarning(UiUtils.getText("common.warning.youHaveNotMadeAnyChanges"));
        }
    }

    public boolean isFutureDate(LocalDate occupancyDate) {
        return !DateUtil.isDateInPast(getSystemDateAsLocalDate(), occupancyDate);
    }

    public FilterDto resetFilter() {
        filterDto = null;
        return getDefaultFilter();
    }

    public boolean hasNotes(LocalDate localDate) {
        return notes.stream().anyMatch(dateNote -> dateNote.getArrivalDate().equals(localDate.toDate()));
    }

    public void updateNotes() {
        notes = notesService.getNotes(filterDto.getDateSelector().getSpecificStartDate().toDate(), filterDto.getDateSelector().getSpecificEndDate().toDate());
    }

    @Override
    public boolean hasChanges() {
        return view.hasChanges();
    }

    public void setNotes(List<DateNote> notes) {
        this.notes = notes;
    }

    public void setFilterDto(FilterDto filterDto) {
        this.filterDto = filterDto;
    }
}
