package com.ideas.tetris.ui.modules.functionspace.forecastreview.filter;

import com.ideas.tetris.pacman.services.functionspace.configuration.entity.FunctionSpaceDayPart;
import com.ideas.tetris.pacman.services.functionspace.dto.FunctionSpaceDemandCalendarDateStatus;
import com.ideas.tetris.pacman.services.functionspace.dto.FunctionSpaceDemandCalendarForecastLevel;
import com.ideas.tetris.pacman.services.functionspace.forecastreview.service.ForecastReviewCriteria;
import com.ideas.tetris.ui.common.component.date.customdateselector.DateSelectorBean;
import org.joda.time.LocalDate;

import java.math.BigDecimal;
import java.util.Date;

public class FilterDto {
    private FunctionSpaceDayPart selectedDayPart;
    private FunctionSpaceDemandCalendarForecastLevel selectedForecastLevel;
    private BigDecimal forecastVariance;
    private FunctionSpaceDemandCalendarDateStatus status;
    private boolean overrideOnly;
    private DateSelectorBean dateSelector;

    public FilterDto() {
    }

    public FunctionSpaceDayPart getSelectedDayPart() {
        return selectedDayPart;
    }

    public void setSelectedDayPart(FunctionSpaceDayPart selectedDayPart) {
        this.selectedDayPart = selectedDayPart;
    }

    public FunctionSpaceDemandCalendarForecastLevel getSelectedForecastLevel() {
        return selectedForecastLevel;
    }

    public void setSelectedForecastLevel(FunctionSpaceDemandCalendarForecastLevel selectedForecastLevel) {
        this.selectedForecastLevel = selectedForecastLevel;
    }

    public ForecastReviewCriteria getCriteria() {
        ForecastReviewCriteria forecastReviewCriteria = new ForecastReviewCriteria();
        forecastReviewCriteria.setDayPart(this.selectedDayPart);
        forecastReviewCriteria.setForecastLevel(this.selectedForecastLevel);
        Date startDate = null, endDate = null;
        if (dateSelector.isRollingDate()) {
            startDate = dateSelector.getStartDateForReportCriteria().toDate();
            endDate = dateSelector.getEndDateForReportCriteria().toDate();
        } else {
            startDate = dateSelector.getSpecificStartDate().toDate();
            endDate = dateSelector.getSpecificEndDate().toDate();
        }


        forecastReviewCriteria.setStartDate(new LocalDate(startDate));
        forecastReviewCriteria.setEndDate(new LocalDate(endDate));

        forecastReviewCriteria.setOverrideOnly(this.isOverrideOnly());
        forecastReviewCriteria.setFunctionSpaceDemandCalendarDateStatus(this.status);
        forecastReviewCriteria.setForecastVariance(this.forecastVariance);
        return forecastReviewCriteria;
    }

    public boolean isAnyCriteriaSet() {
        //note that we always assume start date and end date are set, that is why we don't consider them
        //in this method
        ForecastReviewCriteria criteria = getCriteria();

        if (criteria.getForecastLevel() != null || criteria.getDayPart() != null
                || criteria.getForecastVariance() != null || criteria.getFunctionSpaceDemandCalendarDateStatus() != null
                || criteria.isOverrideOnly()) {
            return true;
        }

        return false;
    }

    public BigDecimal getForecastVariance() {
        return forecastVariance;
    }

    public void setForecastVariance(BigDecimal forecastVariance) {
        this.forecastVariance = forecastVariance;
    }

    public FunctionSpaceDemandCalendarDateStatus getStatus() {
        return status;
    }

    public void setStatus(FunctionSpaceDemandCalendarDateStatus status) {
        this.status = status;
    }

    public boolean isOverrideOnly() {
        return overrideOnly;
    }

    public void setOverrideOnly(boolean overrideOnly) {
        this.overrideOnly = overrideOnly;
    }

    public DateSelectorBean getDateSelector() {
        return dateSelector;
    }

    public void setDateSelector(DateSelectorBean dateSelector) {
        this.dateSelector = dateSelector;
    }
}
