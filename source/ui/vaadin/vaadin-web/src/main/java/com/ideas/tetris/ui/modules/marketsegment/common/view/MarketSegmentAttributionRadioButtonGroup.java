package com.ideas.tetris.ui.modules.marketsegment.common.view;

import com.ideas.tetris.ui.common.component.checkbox.TetrisRadioButtonGroupV8;
import com.vaadin.shared.ui.ContentMode;

import java.util.Collection;

public abstract class MarketSegmentAttributionRadioButtonGroup<T> extends TetrisRadioButtonGroupV8<T> {

    private final AttributeAssignmentConfig config;
    private boolean independentProductsEnabled;

    public MarketSegmentAttributionRadioButtonGroup(String id, String caption, AttributeAssignmentConfig config) {
        setId(id);
        setCaption(caption);
        this.config = config;
        reset();
        setItemCaptionGenerator(this::getCaption);
        setEnabled(false);
    }

    protected abstract Collection<T> getItems();

    protected abstract String getCaption(T item);

    @Override
    public void reset() {
        super.setItems(getItems());
        setDescription(getDescription(), ContentMode.HTML);
    }

    public void setIndependentProductsEnabled(boolean independentProductsEnabled) {
        this.independentProductsEnabled = independentProductsEnabled;
        reset();
    }

    protected boolean isIndependentProductsEnabled() {
        return independentProductsEnabled;
    }

    protected AttributeAssignmentConfig getConfig() {
        return config;
    }

    public boolean isValid() {
        return !isRequiredIndicatorVisible() || !isEmpty();
    }

    public void setRequired(boolean required) {
        setRequiredIndicatorVisible(required);
        setEnabled(config.isEditable() && required);
        if (!required) {
            setValue(null);
        }
    }
}
