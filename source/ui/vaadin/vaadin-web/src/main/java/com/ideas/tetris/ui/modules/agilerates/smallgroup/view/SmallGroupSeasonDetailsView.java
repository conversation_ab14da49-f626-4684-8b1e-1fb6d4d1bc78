package com.ideas.tetris.ui.modules.agilerates.smallgroup.view;

import com.ideas.tetris.pacman.services.agilerates.configuration.dto.AgileRatesSeason;
import com.ideas.tetris.ui.common.component.TetrisNotification;
import com.ideas.tetris.ui.common.component.button.TetrisImageButton;
import com.ideas.tetris.ui.common.component.checkbox.TetrisCheckBoxV8;
import com.ideas.tetris.ui.common.component.fontawesome.TetrisFontAwesome;
import com.ideas.tetris.ui.common.component.grid.TetrisGridV8;
import com.ideas.tetris.ui.common.component.textfield.TetrisDateFieldV8;
import com.ideas.tetris.ui.common.component.textfield.TetrisTextFieldV8;
import com.ideas.tetris.ui.common.util.ChangeAware;
import com.ideas.tetris.ui.common.util.DateFormatUtil;
import com.ideas.tetris.ui.common.util.JavaDateUtil;
import com.ideas.tetris.ui.common.util.TetrisComponent;
import com.ideas.tetris.ui.common.util.UiUtils;
import com.ideas.tetris.ui.modules.agilerates.smallgroup.SmallGroupWizardPresenter;
import com.ideas.tetris.ui.modules.agilerates.smallgroup.offsets.season.SmallGroupSeasonOffsetsLayout;
import com.vaadin.data.Binder;
import com.vaadin.ui.Button;
import com.vaadin.ui.Grid;
import com.vaadin.ui.HorizontalLayout;
import com.vaadin.ui.VerticalLayout;
import de.steinwedel.messagebox.ButtonId;
import org.apache.commons.collections.CollectionUtils;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;

import static com.ideas.tetris.pacman.common.constants.Constants.MAX_AMOUNT_OF_DAYS_FOR_SEASON;
import static com.ideas.tetris.ui.common.util.UiUtils.getText;
import static com.ideas.tetris.ui.modules.agilerates.packages.AgileRatesUiUtils.anyOffsetWhereAllDowOffsetsNull;
import static com.ideas.tetris.ui.modules.agilerates.packages.AgileRatesUiUtils.atLeastOneNullDowOffsetsForSeasonRateOffsets;

public class SmallGroupSeasonDetailsView extends VerticalLayout implements ChangeAware {
    private static final String START_DATE_ID = "startDateId";
    private static final String END_DATE_ID = "endDateId";
    private SmallGroupSeasonsStep smallGroupSeasonsStep;
    private SmallGroupWizardPresenter presenter;
    private List<TetrisComponent> hasChangesList = new ArrayList<>();
    private TetrisDateFieldV8 startDateField;
    private TetrisDateFieldV8 endDateField;
    private SmallGroupSeasonOffsetsLayout seasonOffsetsLayout;
    private List<TetrisCheckBoxV8> seasonCheckBoxList = new ArrayList<>();
    private Binder<AgileRatesSeason> binder;
    private static final String ADJUST_ACTION_BUTTONS = "adjustActionButtons";

    SmallGroupSeasonDetailsView(AgileRatesSeason season, SmallGroupWizardPresenter presenter, SmallGroupSeasonsStep smallGroupSeasonsStep) {
        this.presenter = presenter;
        this.smallGroupSeasonsStep = smallGroupSeasonsStep;
        init(season);
    }

    private void init(AgileRatesSeason season) {
        setSizeFull();

        seasonOffsetsLayout = new SmallGroupSeasonOffsetsLayout(season, presenter);
        binder = seasonOffsetsLayout.getBinder();

        addComponent(createSeasonGrid(season, binder));
        addComponent(seasonOffsetsLayout);

        setupStartEndDates(season, binder);

        disableFieldsIfReadOnlyOrPastDates(season, binder);

        setExpandRatio(seasonOffsetsLayout, 1);
    }

    private boolean isSeasonStepReadOnly() {
        if (presenter.getProductConfiguration().getProduct().isCentrallyManaged()) {
            return !presenter.hasCentrallyManagedPermission();
        } else {
            return !presenter.hasSeasonStepPermission();
        }
    }

    private void disableFieldsIfReadOnlyOrPastDates(AgileRatesSeason season, Binder<AgileRatesSeason> seasonBinder) {
        if (isSeasonStepReadOnly()) {
            //read only permissions
            seasonBinder.setReadOnly(true);
        } else if (presenter.isPastSeason(season)) {
            //Past Season
            for (TetrisCheckBoxV8 tetrisCheckBoxV8 : seasonCheckBoxList) {
                tetrisCheckBoxV8.setReadOnly(true);
            }
            startDateField.setReadOnly(true);
            endDateField.setReadOnly(true);
        } else if (season.getStartDate() != null && season.getStartDate().isBefore(presenter.getSystemDateAsLocalDate())) {
            //present season with a date before system date
            startDateField.setReadOnly(true);
        }
    }

    private TetrisGridV8<AgileRatesSeason> createSeasonGrid(AgileRatesSeason season, Binder<AgileRatesSeason> seasonBinder) {
        final String CENTER_ALIGN = "centeralign";
        TetrisGridV8<AgileRatesSeason> grid = new TetrisGridV8<>(AgileRatesSeason.class);
        grid.setWidth(100, Unit.PERCENTAGE);
        grid.setRowHeight(35);
        grid.setHeightByRows(1);

        grid.addComponentColumn(s -> createSeasonNameField(s, seasonBinder)).setId("seasonNameId").setCaption(getText("common.season.name"));
        grid.getDefaultHeaderRow().getCell("seasonNameId").setStyleName(CENTER_ALIGN);

        Grid.Column<AgileRatesSeason, TetrisDateFieldV8> startDateColumn = grid.addComponentColumn(s -> getStartDateField());
        grid.getDefaultHeaderRow().getCell(startDateColumn).setHtml(createRequiredHeaderCaption(getText("startDate")));
        grid.getDefaultHeaderRow().getCell(startDateColumn).setStyleName(CENTER_ALIGN);

        Grid.Column<AgileRatesSeason, TetrisDateFieldV8> endDateColumn = grid.addComponentColumn(s -> getEndDateField());
        grid.getDefaultHeaderRow().getCell(endDateColumn).setHtml(createRequiredHeaderCaption(getText("endDate")));
        grid.getDefaultHeaderRow().getCell(endDateColumn).setStyleName(CENTER_ALIGN);

        grid.addComponentColumn(this::getActionLayout).setMaximumWidth(138);

        grid.setItems(Collections.singletonList(season));
        return grid;
    }

    private String createRequiredHeaderCaption(String caption) {
        return caption + " " + "<span style= color:red; line-height:-1;>*</span>";
    }

    private HorizontalLayout getActionLayout(AgileRatesSeason season) {
        HorizontalLayout layout = new HorizontalLayout();
        layout.setMargin(false);
        layout.addComponents(
                createSeasonSaveButton(season),
                createSeasonCopyButton(season),
                createSeasonDeleteButton(season),
                createSeasonCancelButton()
        );

        return layout;
    }

    private Button createSeasonCancelButton() {
        TetrisImageButton cancelButton =
                new TetrisImageButton(TetrisFontAwesome.REMOVE_CIRCLE_MEDIUM, (Button.ClickListener) event -> cancelEdit());
        cancelButton.addStyleName(ADJUST_ACTION_BUTTONS);
        return cancelButton;
    }

    void cancelEdit() {
        switchToSeasonLayoutView();
    }

    private Button createSeasonCopyButton(AgileRatesSeason season) {
        boolean isReadOnly = isSeasonStepReadOnly();
        TetrisImageButton copyButton = new TetrisImageButton(TetrisFontAwesome.COPY);
        copyButton.addStyleName(ADJUST_ACTION_BUTTONS);
        copyButton.setDescription(getText("common.copy"));
        copyButton.setEnabled(!isReadOnly && !season.isNewSeason());
        copyButton.addClickListener(clickEvent -> {
            if (hasChanges()) {
                TetrisNotification.showAlert(getText("unsaved.changes"), getText("please.save.cancel.changes.before.proceeding"));
            } else {
                smallGroupSeasonsStep.copyEditSeason(season);
            }
        });
        return copyButton;
    }

    private TetrisImageButton createSeasonSaveButton(AgileRatesSeason season) {
        boolean isReadOnly = isSeasonStepReadOnly();
        TetrisImageButton saveButton = new TetrisImageButton(TetrisFontAwesome.SAVE);
        saveButton.addStyleName(ADJUST_ACTION_BUTTONS);
        saveButton.setDescription(getText("common.save"));
        saveButton.setEnabled(!isReadOnly && !presenter.isPastSeason(season));
        saveButton.addClickListener(clickEvent -> saveSeason(season));
        return saveButton;
    }

    private TetrisImageButton createSeasonDeleteButton(AgileRatesSeason season) {
        boolean isReadOnly = isSeasonStepReadOnly();
        TetrisImageButton deleteButton = new TetrisImageButton(TetrisFontAwesome.DELETE_BUTTON);
        deleteButton.addStyleName(ADJUST_ACTION_BUTTONS);
        deleteButton.setDescription(getText("deleteButtonLabel"));
        deleteButton.setEnabled(!isReadOnly && !season.isNewSeason() && !presenter.isPastSeason(season));
        deleteButton.addClickListener((Button.ClickListener) ignored -> createSeasonDeleteButtonClickListener(season));
        return deleteButton;
    }

    private void createSeasonDeleteButtonClickListener(AgileRatesSeason season) {
        TetrisNotification.showAlert(
                UiUtils.getText("validation.Message.delete.title"),
                getText("ratePlan.season.deleteSeason"),
                true,
                buttonId -> {
                    if (ButtonId.OK.equals(buttonId)) {
                        deleteSeason(season);
                    }
                });
    }

    private void saveWillSplitOccurConfirmationDialog(AgileRatesSeason season) {
        //Present season (start date in the past) need to be split to retain historical settings for the past days
        if (season.getStartDate().isBefore(presenter.getSystemDateAsLocalDate())) {
            //Get original season with original settings and set the end date to system date - 1
            AgileRatesSeason pastSeason = presenter.createPastSeason(season);

            //Create new season (start date is set to system date)
            AgileRatesSeason newSeason = presenter.createFutureSeason(season);
            overlappingSeasonsValidation(newSeason, pastSeason);
        } else {
            overlappingSeasonsValidation(season, null);
        }
    }

    private void overlappingSeasonsValidation(AgileRatesSeason newSeason, AgileRatesSeason pastSeason) {
        Set<AgileRatesSeason> overlappingSeasons = presenter.getOverlappingSeasons(newSeason, pastSeason);
        if (CollectionUtils.isNotEmpty(overlappingSeasons)) {
            //Season is in the future and has overlapping seasons that need to be split
            StringBuilder overlappingSeasonsMessage = new StringBuilder();
            for (AgileRatesSeason overlappingSeason : overlappingSeasons) {
                buildOverlappingMessage(overlappingSeasonsMessage, overlappingSeason);
            }
            String message = UiUtils.getText("common.season.mergeConfirmation.followingSeasons", overlappingSeasonsMessage);
            TetrisNotification.showAlert(UiUtils.getText("CONFIRMATION_TITLE"), message, true, buttonId -> {
                if (ButtonId.OK.equals(buttonId)) {
                    if (pastSeason != null) {
                        presenter.saveSeason(pastSeason);
                    }
                    presenter.saveSplitSeasons(newSeason, overlappingSeasons);
                    TetrisNotification.showSuccessMessage();
                    switchToSeasonLayoutView();
                }
            });
        } else {
            if (pastSeason != null) {
                presenter.saveSeason(pastSeason);
            }
            //season is in the future and has no overlapping seasons
            presenter.saveSeason(newSeason);
            TetrisNotification.showSuccessMessage();
            switchToSeasonLayoutView();
        }
        smallGroupSeasonsStep.setSeasonDetailsChangeFlag(true);
    }

    private void buildOverlappingMessage(StringBuilder overlappingSeasonsMessage, AgileRatesSeason overlappingSeason) {
        if (overlappingSeason.getName() != null) {
            overlappingSeasonsMessage
                    .append(getText("common.season.name"))
                    .append(": ")
                    .append(overlappingSeason.getName())
                    .append(" ");
        }
        overlappingSeasonsMessage
                .append(getText("startDate"))
                .append(": ")
                .append(DateFormatUtil.formatStandard(overlappingSeason.getStartDate()))
                .append(" ")
                .append(getText("endDate"))
                .append(": ")
                .append(DateFormatUtil.formatStandard(overlappingSeason.getEndDate()))
                .append("\n");
    }

    private TetrisTextFieldV8 createSeasonNameField(AgileRatesSeason season, Binder<AgileRatesSeason> seasonBinder) {
        TetrisTextFieldV8 textfield = new TetrisTextFieldV8();
        textfield.setReadOnly(isSeasonStepReadOnly() || presenter.isPastSeason(season));
        textfield.setSizeFull();
        hasChangesList.add(textfield);
        seasonBinder.forField(textfield)
                .withValidator(name -> name.length() < 256,
                        getText("pricingConfiguration.seasonNameTooLarge"))
                .bind(AgileRatesSeason::getName, AgileRatesSeason::setName);
        return textfield;
    }

    private TetrisDateFieldV8 getStartDateField() {
        return startDateField;
    }

    private TetrisDateFieldV8 getEndDateField() {
        return endDateField;
    }

    private void setupStartEndDates(AgileRatesSeason season, Binder<AgileRatesSeason> seasonBinder) {
        startDateField = new TetrisDateFieldV8();
        startDateField.setId(START_DATE_ID);
        startDateField.addValueChangeListener(valueChangeEvent -> seasonOffsetsLayout.applySeasonStartDateToOffset(valueChangeEvent.getValue()));
        seasonBinder.forField(startDateField)
                .asRequired(getText("promptToSelectStartDate"))
                .bind(AgileRatesSeason::getJavaStartDate, AgileRatesSeason::setJavaStartDate);
        hasChangesList.add(startDateField);

        endDateField = new TetrisDateFieldV8();
        endDateField.setId(END_DATE_ID);
        endDateField.addValueChangeListener(valueChangeEvent -> seasonOffsetsLayout.applySeasonEndDateToOffset(valueChangeEvent.getValue()));
        seasonBinder.forField(endDateField)
                .asRequired(getText("promptToSelectEndDate"))
                .bind(AgileRatesSeason::getJavaEndDate, AgileRatesSeason::setJavaEndDate);
        hasChangesList.add(endDateField);

        boolean isPastStartDate = season.getStartDate() != null && season.getStartDate().isBefore(presenter.getSystemDateAsLocalDate());
        if (season.getStartDate() == null || !isPastStartDate) {
            startDateField.setRangeStart(LocalDate.parse(presenter.getSystemDateAsLocalDate().toString()));
            //set date fields to be dependent on each other if start date is in the future
            endDateField.setDependentStartDateField(startDateField, LocalDate.parse(presenter.getSystemDateAsLocalDate().toString()));
            startDateField.setDependentEndDateField(endDateField);
        } else if (endDateField.getValue() == null || endDateField.getValue().isBefore(LocalDate.parse(presenter.getSystemDateAsLocalDate().toString()))) {
            //set no dependency if end date is in the past
            endDateField.setRangeStart(null);
        } else {
            //set end date range start to system date if start date is in the past.
            endDateField.setRangeStart(LocalDate.parse(presenter.getSystemDateAsLocalDate().toString()));
        }
    }

    private void saveSeason(AgileRatesSeason season) {
        if (isValid()) {
            if (startDateField.getValue() != null && endDateField.getValue() != null
                    && JavaDateUtil.getDaysBetween(startDateField.getValue(), endDateField.getValue(), true) > MAX_AMOUNT_OF_DAYS_FOR_SEASON) {
                TetrisNotification.showWarningMessage(UiUtils.getText("pricingConfiguration.season.invalid.range", MAX_AMOUNT_OF_DAYS_FOR_SEASON));
            } else if (noDowOffsetAllNull(season)) {
                if (hasChanges()) {
                    if (!presenter.validateSeasonForMultipleTaxes(season)) {
                        return;
                    }
                    saveWithEmptyValueConfirmationDialog(season);
                } else {
                    TetrisNotification.showWarningMessage(getText("common.error.msg.nochangesForSubmit"));
                    switchToSeasonLayoutView();
                }
            } else {
                TetrisNotification.showWarningMessage(getText("agile.rates.empty.offset.warning"));
            }

        } else {
            TetrisNotification.showWarningMessage(getText("agile.rates.check.values"));
        }
    }

    private boolean noDowOffsetAllNull(AgileRatesSeason season) {
        return !anyOffsetWhereAllDowOffsetsNull(season.getSeasonRateOffsets());
    }

    private void switchToSeasonLayoutView() {
        smallGroupSeasonsStep.setGridAndFilterLayoutVisible(true);
    }

    private void saveWithEmptyValueConfirmationDialog(AgileRatesSeason season) {
        if (atLeastOneNullDowOffsetsForSeasonRateOffsets(season, season.getSeasonRateOffsets(), presenter.getSystemDateAsLocalDate())) {
            TetrisNotification.showAlert(
                    getText("common.seasons"), getText("agile.rates.defaults.nulls.present.warning.message"),
                    true, buttonId -> {
                        if (ButtonId.OK.equals(buttonId)) {
                            saveWillSplitOccurConfirmationDialog(season);
                        }
                    });
        } else {
            saveWillSplitOccurConfirmationDialog(season);
        }
    }

    public void deleteSeason(AgileRatesSeason season) {
        presenter.deleteSeasonFromGrid(season);
        TetrisNotification.showSuccessMessage();
        smallGroupSeasonsStep.setSeasonDetailsChangeFlag(true);
        switchToSeasonLayoutView();
    }

    @Override
    public boolean hasChanges() {
        return hasChangesList.stream().anyMatch(TetrisComponent::hasChanges) || seasonOffsetsLayout.hasChanges();
    }

    @Override
    public boolean isValid() {
        return binder.validate().isOk() && seasonOffsetsLayout.isValid();
    }
}