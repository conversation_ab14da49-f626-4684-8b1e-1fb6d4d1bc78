package com.ideas.tetris.ui.modules.functionspace.demandcalendar.bookingsummary;

public class RowData {
    private String label;
    private String value;
    private String status;
    private boolean hasOverride;

    public RowData() {

    }

    public RowData(String label, String value) {
        this.label = label;
        this.value = value;
    }

    public RowData(String label, String value, String status, boolean hasOverride) {
        this.label = label;
        this.value = value;
        this.status = status;
        this.hasOverride = hasOverride;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public boolean isHasOverride() {
        return hasOverride;
    }

    public void setHasOverride(boolean hasOverride) {
        this.hasOverride = hasOverride;
    }
}