package com.ideas.tetris.ui.modules.marketsegment.common;

import com.ideas.tetris.pacman.services.marketsegment.entity.ForecastActivityType;
import com.ideas.tetris.pacman.services.marketsegment.service.AnalyticalMarketSegmentSummary;
import com.ideas.tetris.pacman.services.marketsegment.service.RateCodeSummary;
import com.ideas.tetris.pacman.services.marketsegment.service.RateCodeTypeEnum;
import com.ideas.tetris.ui.common.data.hierarchical.HierarchicalWithParent;
import com.ideas.tetris.ui.common.util.UiUtils;
import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.List;

public class AMSSummaryUIWrapper implements HierarchicalWithParent<AMSSummaryUIWrapper> {

    private List<String> marketSegments;
    private Integer marketSegmentCount;
    private AnalyticalMarketSegmentSummary summary;
    private RateCodeSummary rateCodeSummary;
    private List<AMSSummaryUIWrapper> children = new ArrayList<>();
    private AMSSummaryUIWrapper parent;
    private RateCodeTypeEnum rateCodeType;
    private ForecastActivityType forecastActivityType;

    private String marketSegment;
    private String rateCode;
    private Integer roomsSold;
    private Double roomRevenue;
    private Double adr;
    private Double percent;

    private String assignedAttributeDescriptionKey;

    private boolean sharedRateCodesExist = false;

    public AMSSummaryUIWrapper() {
    }

    public AMSSummaryUIWrapper(AnalyticalMarketSegmentSummary summary, boolean sharedRateCodesExist) {
        this.summary = summary;
        marketSegment = summary.getMarketCode();
        rateCode = summary.getRateCode();
        roomsSold = summary.getRoomsSold();
        roomRevenue = summary.getRoomRevenue();
        adr = summary.getAverageDailyRate();
        percent = summary.getHotelPercent();
        rateCodeType = RateCodeTypeEnum.find(summary.getRateCodeType());
        this.sharedRateCodesExist = sharedRateCodesExist;
    }

    public AMSSummaryUIWrapper(RateCodeSummary rateCodeSummary) {
        this.rateCodeSummary = rateCodeSummary;
        rateCode = rateCodeSummary.getRateCode();
        roomsSold = rateCodeSummary.getRoomsSold();
        roomRevenue = rateCodeSummary.getRoomRevenue();
        adr = rateCodeSummary.getAverageDailyRate();
        percent = rateCodeSummary.getPercent();
        rateCodeType = RateCodeTypeEnum.find(rateCodeSummary.getRateCodeType());

        //only available for rate code
        marketSegmentCount = rateCodeSummary.getMarketSegmentCount();
        marketSegments = rateCodeSummary.getMarketSegments();
    }

    public List<String> getMarketSegments() {
        return marketSegments;
    }

    public Integer getMarketSegmentCount() {
        return marketSegmentCount;
    }

    public String getMarketSegment() {
        return marketSegment;
    }

    public String getRateCode() {
        return rateCode;
    }

    public Integer getRoomsSold() {
        return roomsSold;
    }

    public Double getRoomRevenue() {
        return roomRevenue;
    }

    public Double getAdr() {
        return adr;
    }

    public Double getPercent() {
        if (percent != null) {
            return percent * 100;
        }
        return percent;
    }

    public void setPercent(Double percent) {
        this.percent = percent;
    }

    public AnalyticalMarketSegmentSummary getSummary() {
        return summary;
    }

    public RateCodeSummary getRateCodeSummary() {
        return rateCodeSummary;
    }

    public void setChildren(List<AMSSummaryUIWrapper> children) {
        this.children = children;
    }

    public void setParent(AMSSummaryUIWrapper parent) {
        this.parent = parent;
    }

    public boolean isSharedRateCodesExist() {
        return sharedRateCodesExist;
    }

    public void setSharedRateCodesExist(boolean sharedRateCodesExist) {
        this.sharedRateCodesExist = sharedRateCodesExist;
    }

    public RateCodeTypeEnum getRateCodeType() {
        return rateCodeType;
    }

    @Override
    public AMSSummaryUIWrapper getParent() {
        return parent;
    }

    @Override
    public List<AMSSummaryUIWrapper> getChildren() {
        return children;
    }

    @Override
    public boolean hasChildren() {
        //if children was specifically set to null, we want to return false to fix the arrows on the AssignAttributeConfirmationPopup
        if (children != null && parent == null) {
            return true;
        }
        return children != null && !children.isEmpty();
    }

    public AMSSummaryUIWrapper clone() {
        if (this.summary != null) {
            return new AMSSummaryUIWrapper(this.summary, this.sharedRateCodesExist);
        } else {
            return new AMSSummaryUIWrapper(this.rateCodeSummary);
        }
    }

    public String getAssignedAttributeDescriptionKey() {
        return assignedAttributeDescriptionKey;
    }

    public void setAssignedAttributeDescriptionKey(String assignedAttribute) {
        this.assignedAttributeDescriptionKey = assignedAttribute;
    }

    public String getAssignedAttributeText() {
        if (assignedAttributeDescriptionKey != null) {
            return UiUtils.getText(assignedAttributeDescriptionKey, StringUtils.EMPTY);
        }
        return StringUtils.EMPTY;
    }

    public ForecastActivityType getForecastActivityType() {
        return forecastActivityType;
    }

    public void setForecastActivityType(ForecastActivityType forecastActivityType) {
        this.forecastActivityType = forecastActivityType;
    }

    public String getAssignedAttributeText(Boolean complimentary) {
        if (assignedAttributeDescriptionKey != null) {
            return UiUtils.getText(assignedAttributeDescriptionKey, new ComplimentaryAttribute().getComplimentaryDescription(complimentary));
        }
        return StringUtils.EMPTY;
    }

    public String getMarketSegmentName() {
        if (getRateCodeType() != null && RateCodeTypeEnum.DEFAULT.equals(getRateCodeType())) {
            return getMarketSegment() + AttributeDetails.DEF;
        }
        return getMarketSegment();
    }
}
