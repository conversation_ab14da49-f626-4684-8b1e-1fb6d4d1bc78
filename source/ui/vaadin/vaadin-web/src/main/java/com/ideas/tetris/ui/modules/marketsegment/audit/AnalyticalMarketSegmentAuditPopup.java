package com.ideas.tetris.ui.modules.marketsegment.audit;

import com.ideas.tetris.pacman.services.marketsegment.service.AnalyticalMarketSegmentAuditMapping;
import com.ideas.tetris.ui.common.component.TetrisBeanItemContainer;
import com.ideas.tetris.ui.common.component.button.TetrisExportXlsImageButton;
import com.ideas.tetris.ui.common.component.table.TetrisTable;
import com.ideas.tetris.ui.common.component.window.TetrisWindow;
import com.ideas.tetris.ui.common.util.UiUtils;
import com.vaadin.ui.Alignment;
import com.vaadin.v7.ui.Table;
import com.vaadin.v7.ui.VerticalLayout;

import java.util.List;

public class AnalyticalMarketSegmentAuditPopup extends VerticalLayout {

    private TetrisBeanItemContainer<AnalyticalMarketSegmentAuditMapping> container;
    private static TetrisWindow tetrisWindow;

    private AnalyticalMarketSegmentAuditPopup(final TetrisWindow window, boolean independentProductsEnabled) {
        setSizeFull();
        setMargin(true);
        setSpacing(true);

        TetrisTable table = new TetrisTable();
        table.setStyleName("two-header-row");
        container = new TetrisBeanItemContainer<>(AnalyticalMarketSegmentAuditMapping.class);
        table.setContainerDataSource(container);

        String marketCode = "marketCode";
        String rateCode = "rateCode";
        String mappedMarketCode = "mappedMarketCode";
        String attribute = "attributeDescription";
        String lastUpdatedUser = "lastUpdatedUser";
        String lastUpdatedDateTime = "dateTimeString";
        String productName = "productName";

        if (independentProductsEnabled) {
            table.setVisibleColumns(mappedMarketCode,
                    rateCode,
                    marketCode,
                    attribute,
                    productName,
                    lastUpdatedUser,
                    lastUpdatedDateTime);
        } else {
            table.setVisibleColumns(mappedMarketCode,
                    rateCode,
                    marketCode,
                    attribute,
                    lastUpdatedUser,
                    lastUpdatedDateTime);
        }

        if (independentProductsEnabled) {
            table.setColumnHeaders(UiUtils.getText("market.segment.header"),
                    UiUtils.getText("rate.code.newline.header"),
                    UiUtils.getText("original.newline.market.segment.header"),
                    UiUtils.getText("attributes.header"),
                    UiUtils.getText("independent.products.market.segments.base.product.header"),
                    UiUtils.getText("common.updatedBy"),
                    UiUtils.getText("updatedOn"));
        } else {
            table.setColumnHeaders(UiUtils.getText("market.segment.header"),
                    UiUtils.getText("rate.code.newline.header"),
                    UiUtils.getText("original.newline.market.segment.header"),
                    UiUtils.getText("attributes.header"),
                    UiUtils.getText("common.updatedBy"),
                    UiUtils.getText("updatedOn"));
        }


        table.setColumnWidth(mappedMarketCode, 100);
        table.setColumnAlignment(mappedMarketCode, Table.Align.CENTER);
        table.setColumnWidth(rateCode, 75);
        table.setColumnAlignment(rateCode, Table.Align.CENTER);
        table.setColumnWidth(marketCode, 100);
        table.setColumnAlignment(marketCode, Table.Align.CENTER);
        table.setColumnWidth(attribute, 325);
        table.setColumnAlignment(attribute, Table.Align.CENTER);
        table.setColumnWidth(lastUpdatedUser, 100);
        table.setColumnAlignment(lastUpdatedUser, Table.Align.CENTER);
        table.setColumnWidth(lastUpdatedDateTime, 150);
        table.setColumnAlignment(lastUpdatedDateTime, Table.Align.CENTER);
        if (independentProductsEnabled) {
            table.setColumnWidth(productName, 100);
            table.setColumnAlignment(productName, Table.Align.CENTER);
        }
        String title = UiUtils.getText("analytical.market.segment.audit.title");

        TetrisExportXlsImageButton exportButton = new TetrisExportXlsImageButton(table, title, title,
                UiUtils.getText("audit.button.title"));
        exportButton.setId("auditExcelExportButton");

        table.setSizeFull();

        addComponent(exportButton);
        addComponent(table);
        setExpandRatio(table, 1.0f);
        setComponentAlignment(exportButton, Alignment.TOP_RIGHT);
    }

    public static AnalyticalMarketSegmentAuditPopup showAuditPopup(boolean independentProductsEnabled) {
        tetrisWindow = new TetrisWindow(UiUtils.getText("analytical.market.segment.audit.title"));
        tetrisWindow.setHeight(550, Unit.PIXELS);
        tetrisWindow.setWidth(900, Unit.PIXELS);
        tetrisWindow.center();

        AnalyticalMarketSegmentAuditPopup auditPopup = new AnalyticalMarketSegmentAuditPopup(tetrisWindow, independentProductsEnabled);
        tetrisWindow.setContent(auditPopup);
        tetrisWindow.show();
        return auditPopup;
    }

    public void setPreviewBeans(List<AnalyticalMarketSegmentAuditMapping> dtoList) {
        container.removeAllItems();
        container.addAll(dtoList);
    }

    List<AnalyticalMarketSegmentAuditMapping> getPreviewBeans() {
        return container.getItemsExcludingNoDataFoundItem();
    }

    String getWindowTitle() {
        return tetrisWindow != null ? tetrisWindow.getCaption() : "";
    }
}