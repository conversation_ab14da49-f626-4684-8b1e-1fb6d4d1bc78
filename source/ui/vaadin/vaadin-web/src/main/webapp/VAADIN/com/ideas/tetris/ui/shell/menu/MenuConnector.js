com_ideas_tetris_ui_shell_menu_Menu = function () {
    var menuwrapper = this.getElement();
    var vaadin = this;

    /***************************************************************************
     * Class names
     **************************************************************************/
    var MENU_ITEM_CLASS = "menu-item";
    var MENU_ITEM_ICON_CLASS = "menu-item-icon";
    var SELECTED_MENU_ITEM_CLASS = "selected-menu-item";
    var OPENED_MENU_ITEM_CLASS = "opened-menu-item";
    var FLYOUT_CLASS = "flyout";
    var MENU_ITEM_SEPARATOR="menu-item-separator";

    var SUBMENU_CLASS = "submenu";
    var SUBMENU_TITLE_CLASS = "title";
    var SUBMENU_TAG_CLASS = "tag";
    var SUBMENU_CONTENT_CLASS = "content";
    var SUBMENU_CATEGORY_CLASS = "category";
    var SUBMENU_ENTRY_CLASS = "entry";
    var SUBMENU_GRID_LAYOUT = "grid";
    var SUBMENU_CATEGORY_LAYOUT = "categorized";
    var SUBMENU_COLUMN_LAYOUT = "column";

    /***************************************************************************
     * Ids
     **************************************************************************/
    var ID_SHOW_ME = "walkme-menu";

    /***************************************************************************
     * Setting up globals and the DOM
     **************************************************************************/
    // Offset the menu contents so that they live below the header
    menuwrapper.style.paddingTop = "54px";

    // If we are clicking outside of a menu-item, then close any opened menu items
    document.addEventListener("click", hideOpenedSubMenuListener);

    // If we are clicking outside of show-me when it is opened, then close it
    document.addEventListener("click", closeShowMe);

    window.addEventListener("orientationchange", function (event) {
        // This is specific to the iPad where categorized submenus are opened
        // and the user rotates the iPad -- we need to resize the width
        // accordingly now that the dimensions have changed

        var openedMenuItem = getOpenedMenuItem();
        if (openedMenuItem) {
            // Close and reset the width
            openedMenuItem.classList.remove(OPENED_MENU_ITEM_CLASS);
            fixCategorizedLayout();

            // Open and fix the width
            openedMenuItem.classList.add(OPENED_MENU_ITEM_CLASS);
            fixCategorizedLayout();
        }
    });

    window.addEventListener("resize", function (event) {
        var openedMenuItem = getOpenedMenuItem();
        if (openedMenuItem) {
            fixCategorizedLayout();
        }
    });

    function hideOpenedSubMenuListener(event) {
        var clickedOutsideOpenedMenuItem = event.target.closest("." + OPENED_MENU_ITEM_CLASS) == null;
        if (clickedOutsideOpenedMenuItem) {
            closeOpenMenuItem();
        }
    }

    function closeOpenMenuItem() {
        var openedMenuItem = getOpenedMenuItem();
        if (openedMenuItem != null) {
            openedMenuItem.classList.remove(OPENED_MENU_ITEM_CLASS);
        }
    }

    function closeShowMe(event) {
        var clickedOutsideShowMe = event.target.closest("#" + ID_SHOW_ME) == null;
        if (isWalkMeEnabled() && clickedOutsideShowMe && WalkMePlayerAPI.isMenuOpen()) {
            WalkMePlayerAPI.toggleMenu();
        }
    }

    /***************************************************************************
     * Interface functions
     **************************************************************************/
    renderMenu = function (pages) {
        // Clean out the menu every time we need to render it
        while (menuwrapper.firstChild) {
            menuwrapper.removeChild(menuwrapper.firstChild);
        }

        for (var i = 0; i < pages.length; i++) {
            var pageObj = JSON.parse(pages[i]);
            menuwrapper.appendChild(makeMenuItem(pageObj));
        }

        if (get_browser().name === "IE") {
            // IE11's grid and flex specs are outdates, fallback on the
            // column layout (column-count) until we drop IE11
            replaceGridsWithColumns();
            replaceCategorizedLayoutWithColumns();
        }
    };

    addShowMe = function (showme) {
        var menuItem = document.createElement("div");
        menuItem.classList.add(MENU_ITEM_CLASS);
        menuItem.id = "showme";

        var icon = makeMenuIcon(showme.iconHTML, "showmeicon");

        menuItem.appendChild(icon);
        menuItem.title = "Menu of Show Mes";
        menuItem.addEventListener("click", function (event) {
            closeOpenMenuItem();
            if (isWalkMeEnabled()) {
                WalkMePlayerAPI.toggleMenu();
            }
        });

        menuItem.addEventListener("mouseenter", function (event) {
            closeOpenMenuItem();
        });

        menuwrapper.appendChild(menuItem);
    };

    addIdeaShare = function (ideaShare) {
        var menuItem = document.createElement("div");
        menuItem.classList.add(MENU_ITEM_CLASS);
        menuItem.id = "IDeaShare";

        var icon = makeMenuIcon(ideaShare.iconHTML, "ideashareicon");

        menuItem.appendChild(icon);
        menuItem.title = "IDeaShare";
        menuItem.addEventListener("click", function (event) {
            closeOpenMenuItem();
            vaadin.onClick("ideashare");
        });

        menuwrapper.appendChild(menuItem);
    };

    addStopWatch = function (stopwatch) {
        var menuItem = document.createElement("div");
        menuItem.className = MENU_ITEM_CLASS;
        menuItem.id = "stopwatch";

        var icon = makeMenuIcon(stopwatch.iconHTML, stopwatch.description + "Icon");

        menuItem.appendChild(icon);
        menuItem.title = stopwatch.description;
        menuItem.addEventListener("click", function (event) {
            closeOpenMenuItem();
            vaadin.onStopWatchClick();
        });

        menuwrapper.appendChild(menuItem);
    };

    addOptix = function (optix) {
        var menuItem = document.createElement("div");
        menuItem.classList.add(MENU_ITEM_CLASS);
        menuItem.id = "Optix";

        var icon = makeMenuIcon(optix.iconHTML, "optixicon");

        menuItem.appendChild(icon);
        menuItem.title = optix.description;
        menuItem.addEventListener("click", function (event) {
            closeOpenMenuItem();
            vaadin.onOptixClick("optix");
        });

        menuwrapper.appendChild(menuItem);
    };

    addMenuItemSeparator = function(){
        var menuItem = document.createElement("div");
        menuItem.innerHTML = "<hr class = 'menu-item-separator'>";
        menuwrapper.appendChild(menuItem);
    };

    updateAlertCount = function (alertCount) {
        var INFORMATION_MANAGER_ID = "information-manager";
        var INFORMATION_MANAGER_ALERT_COUNT_CLASS = "information-manager-alert-count";

        var infoManager = document.getElementById(INFORMATION_MANAGER_ID);

        function createIcon(alertCountString) {
            var icon = document.createElement('span');
            icon.setAttribute('class', "v-icon  FontAwesome");
            icon.innerHTML = "&#xf075;";
            icon.appendChild(createNumber(alertCountString));
            return icon;
        }

        function getThousandsDigit(count) {
            count = Math.floor(count / 1000);
            return count;
        }

        function createNumber(alertCount) {
            var alertCountElement = document.createElement('span');
            alertCountElement.setAttribute('class', "alert-count-icon");
            alertCountElement.setAttribute('style', "left: 3px; top: 9px;");
            alertCountElement.appendChild(document.createTextNode(alertCount));
            return alertCountElement;
        }

        if (alertCount && alertCount > 0) {
            var alertCountString = alertCount.toString();
            if (alertCount > 999) {
                var thousandsDigit = getThousandsDigit(alertCount);
                if (thousandsDigit > 9) {
                    alertCountString = "9K+";
                } else {
                    alertCountString = thousandsDigit.toString() + "K+";
                }
            }
            var alertIconWrapper = document.createElement('span');
            alertIconWrapper.setAttribute('class', INFORMATION_MANAGER_ALERT_COUNT_CLASS);
            alertIconWrapper.appendChild(addSpace());
            alertIconWrapper.appendChild(createIcon(alertCountString));
            alertIconWrapper.id = "alertIcon";

            var alertIcon = infoManager.querySelector("#alertIcon");
            if (alertIcon !== null) {
                infoManager.removeChild(alertIcon);
            }
            infoManager.appendChild(alertIconWrapper);
        }
    };

    highlightSelectedMenuCategory = function (selectedCategory) {
        unselectMenuItem();
        var selectedMenuItem = document.getElementById(formatId(selectedCategory));
        selectedMenuItem.classList.add(SELECTED_MENU_ITEM_CLASS);
    };

    highlightIdeaShareIcon = function (){
        unselectMenuItem();
        var selectedMenuItem = document.getElementById("IDeaShare");
        selectedMenuItem.classList.add(SELECTED_MENU_ITEM_CLASS);
    };

    /***************************************************************************
     * Menu generation functions
     **************************************************************************/
    function makeMenuItem(page) {
        function onClickMenuItemListener(event) {
            var clickedMenuItem = event.target.closest("." + MENU_ITEM_CLASS);
            var openedMenuItem = getOpenedMenuItem();

            closeOpenMenuItem();
            if (openedMenuItem == null || openedMenuItem.id !== clickedMenuItem.id) {
                clickedMenuItem.classList.add(OPENED_MENU_ITEM_CLASS);
            }
        }

        function onHoverMenuItemListener(event) {
            var hoveredMenuItem = event.target.closest("." + MENU_ITEM_CLASS);
            var openedMenuItem = getOpenedMenuItem();

            if (openedMenuItem == null || openedMenuItem.id !== hoveredMenuItem.id) {
                closeOpenMenuItem();
                hoveredMenuItem.classList.add(OPENED_MENU_ITEM_CLASS);
            }
        }

        function onLeaveMenuItemListener(event) {
            closeOpenMenuItem();
        }

        var menuItem = document.createElement("div");
        menuItem.classList.add(MENU_ITEM_CLASS);
        menuItem.id = formatId(page.pageId);
        menuItem.title = page.pageTitle;

        var icon = makeMenuIcon(page.iconHTML, page.pageId + "Icon");
        menuItem.appendChild(icon);

        if (hasSubmenu(page)) {
            var flyout = makeFlyout();
            var submenu = makeSubmenu(page);
            flyout.appendChild(submenu);
            menuItem.appendChild(flyout);
            menuItem.addEventListener("click", onClickMenuItemListener);
            menuItem.addEventListener("mouseenter", onHoverMenuItemListener);
            menuItem.addEventListener("mouseleave", onLeaveMenuItemListener);

            if (isCategorized(page) || (isConfigureMenuPage(page))) {
                menuItem.addEventListener("click", fixCategorizedLayout);
                menuItem.addEventListener("mouseenter", fixCategorizedLayout);
            }

        }
        return menuItem;
    }

    function makeMenuIcon(html, id) {
        var icon = document.createElement("div");
        icon.classList.add(MENU_ITEM_ICON_CLASS);
        icon.innerHTML = html;
        icon.id = formatId(id);
        return icon;
    }

    function makeFlyout() {
        var flyout = document.createElement("div");
        flyout.classList.add(FLYOUT_CLASS);
        return flyout;
    }

    /***************************************************************************
     * Submenu generation functions
     **************************************************************************/
    function makeSubmenu(page) {
        var submenu = document.createElement("div");
        submenu.classList.add(SUBMENU_CLASS);

        var title = document.createElement("div");
        title.classList.add(SUBMENU_TITLE_CLASS);
        title.appendChild(document.createTextNode(page.pageTitle));

        submenu.appendChild(title);
        submenu.appendChild(makeSubmenuContents(page));
        return submenu;
    }

    function makeSubmenuContents(page) {
        var content = document.createElement("ul");
        content.classList.add(SUBMENU_CONTENT_CLASS);

        var subpages = page.pages;
        subpages.sort(pageTitleSorter);
        if (isCategorized(page)) {
            content.classList.add(SUBMENU_CATEGORY_LAYOUT);
        } else {
            content.classList.add(SUBMENU_GRID_LAYOUT);

            // Place non-categorized items first
            var nonCategorizedSubpages = subpages.filter(function (page) {
                return !hasSubmenu(page)
            });

            var categorizedSubpages = subpages.filter(function (page) {
                return hasSubmenu(page);
            });
            nonCategorizedSubpages.sort(pageTitleSorter);
            categorizedSubpages.sort(pageTitleSorter);
            subpages = nonCategorizedSubpages.concat(categorizedSubpages);
        }

        //Creating category for Configuration only
        if(isConfigureMenuPage(page)) {
            var category = document.createElement("li");
            category.classList.add(SUBMENU_CATEGORY_LAYOUT);
            category.id = formatId("configureCategorisedItem");
        }



        for (var i = 0; i < subpages.length; i++) {
            var subpage = subpages[i];
            if (hasSubmenu(subpage)) {
                if(isConfigureMenuPage(page)){
                    category.appendChild(makeSubmenuCategory(subpage));
                } else {
                    content.appendChild(makeSubmenuCategory(subpage));
                }

            } else {
                content.appendChild(makeSubmenuEntry(subpage));
            }
        }
        if(isConfigureMenuPage(page)) {
            content.appendChild(category);
        }
        return content;
    }

    function makeSubmenuEntry(subpage) {
        function onClickLinkListener(event) {
            //prevent the browser from navigating to the link
            event.preventDefault();

            //reset the page load time
            tetrisPagePerformance.reset();

            if (event.ctrlKey) {
                //we need a new tab, shhh don't tell vaadin
                window.open(link.href, "_blank");
            } else {
                //tell vaadin about the click
                vaadin.onClick(subpage.code);
            }

            // Close the showme if it's open
            if (isWalkMeEnabled() && WalkMePlayerAPI.isMenuOpen()) {
                WalkMePlayerAPI.toggleMenu();
            }

            // Don't allow the event to propagate/bubble-up, otherwise it will trigger
            // the menu item's click event which will cause the flyout to open again
            event.stopPropagation();
            closeOpenMenuItem();
        }

        function makeBetaTag() {
            var betaTag = document.createElement('span');
            betaTag.className = "beta-tag";
            betaTag.appendChild(document.createTextNode("Beta"));
            return betaTag;
        }

        var entry = document.createElement("li");
        entry.classList.add(SUBMENU_ENTRY_CLASS);
        entry.id = formatId(subpage.pageId);

        var link = document.createElement("a");
        link.appendChild(document.createTextNode(subpage.pageTitle));
        link.title = subpage.pageTitle;
        link.href = subpage.code;
        link.id = entry.id + "-link";
        link.addEventListener("click", onClickLinkListener);

        var hash = window.location.hash;
        if (hash) {
            //strip anything after the client or property/property group identifier
            //for example if the hash is #/p5/!!optimizationSettingsView
            //we only want the #/p5 part
            var positionOfLastSlash = hash.lastIndexOf("/");
            if (positionOfLastSlash !== -1 && hash.charAt(positionOfLastSlash - 1) !== "#") {
                hash = hash.substring(0, positionOfLastSlash);
            }

            link.href = link.href + hash;
        }

        entry.appendChild(link);

        if (subpage.isBeta) {
            link.appendChild(addSpace());
            link.appendChild(makeBetaTag());
        }

        return entry;
    }

    function makeSubmenuCategory(categoryPage) {
        var category = document.createElement("li");
        category.classList.add(SUBMENU_CATEGORY_CLASS);
        category.id = formatId(categoryPage.pageId);

        var categoryTag = document.createElement("div");
        categoryTag.classList.add(SUBMENU_TAG_CLASS);
        categoryTag.appendChild(document.createTextNode(categoryPage.pageTitle));

        var content = document.createElement("ul");
        content.classList.add(SUBMENU_GRID_LAYOUT);

        var subpages = categoryPage.pages;
        subpages.sort(pageTitleSorter);
        for (var i = 0; i < subpages.length; i++) {
            content.appendChild(makeSubmenuEntry(subpages[i]));
        }

        category.appendChild(categoryTag);
        category.appendChild(content);
        return category;
    }

    /***************************************************************************
     * Utility functions
     **************************************************************************/
    function isWalkMeEnabled() {
        return typeof (WalkMePlayerAPI) != "undefined";
    }

    function formatId(id) {
        // Replace all of the spaces and underscores with dashes
        return id.replace(/\s+/g, '-').replace(/_/g, '-').toLowerCase();
    }

    function getOpenedMenuItem() {
        return document.getElementsByClassName(OPENED_MENU_ITEM_CLASS)[0];
    }

    function getSelectedMenuItem() {
        return document.getElementsByClassName(SELECTED_MENU_ITEM_CLASS)[0];
    }

    function unselectMenuItem() {
        var toUnselect = getSelectedMenuItem();
        if (toUnselect) {
            toUnselect.classList.remove(SELECTED_MENU_ITEM_CLASS);
        }
    }

    function pageTitleSorter(a, b) {
        return a.pageTitle.localeCompare(b.pageTitle);
    }

    function hasSubmenu(page) {
        return page.pages.length > 0;
    }

    function isConfigureMenuPage(page) {
        return page.pageId === "configure";
    }

    function isCategorized(page) {
        var subpages = page.pages;
        var categorized = true;
        for (var i = 0; i < subpages.length; i++) {
            var subpage = subpages[i];
            if (subpage.pages.length === 0) {
                return false;
            }
        }
        return categorized;
    }

    function addSpace() {
        return document.createTextNode(" ");
    }

    function fixCategorizedLayout() {
        // flex has a bug where if a flex container's parent is absolutely
        // positioned, or made to be a shrink-to-fit container, then when it
        // wraps column-wise, the flex container won't grow its width:
        // https://stackoverflow.com/questions/33891709/when-flexbox-items-wrap-in-column-mode-container-does-not-grow-its-width
        //
        // This is attempts to fix it by manually adjusting the width of the parent
        // container based on the widths of the flex children

        function isElementAtTopOfParent(element) {
            var childOffset = element.offsetTop;
            var parentOffset = element.parentElement.offsetTop;
            return (childOffset - parentOffset) === 0;
        }

        var flexElements = document.getElementsByClassName(SUBMENU_CATEGORY_LAYOUT);
        for (var i = 0; i < flexElements.length; i++) {
            var flexElement = flexElements[i];
            flexElement.style.width = 0;
            var flexChildren = flexElement.getElementsByClassName(SUBMENU_CATEGORY_CLASS);

            var actualWidth = 0;
            var minHeight = -1;
            for (var j = 0; j < flexChildren.length; j++) {
                var flexChild = flexChildren[j];
                if (isElementAtTopOfParent(flexChild)) {
                    actualWidth += flexChild.offsetWidth;
                }
                // Set the minimum height to be the height of the largest category
                minHeight = Math.max(flexChild.offsetHeight, minHeight);
            }

            // Add some additional pixels for padding/whitespace
            flexElement.style.minHeight = minHeight + 10 + "px";
            flexElement.style.width = actualWidth + 15 + "px";
        }
    }

    /***************************************************************************
     * IE11 Specifics
     **************************************************************************/
    // https://stackoverflow.com/questions/5916900/how-can-you-detect-the-version-of-a-browser
    function get_browser() {
        var ua = navigator.userAgent, tem,
            M = ua.match(/(opera|chrome|safari|firefox|msie|trident(?=\/))\/?\s*(\d+)/i) || [];
        if (/trident/i.test(M[1])) {
            tem = /\brv[ :]+(\d+)/g.exec(ua) || [];
            return {name: 'IE', version: (tem[1] || '')};
        }
        if (M[1] === 'Chrome') {
            tem = ua.match(/\bOPR|Edge\/(\d+)/)
            if (tem != null) {
                return {name: 'Opera', version: tem[1]};
            }
        }
        M = M[2] ? [M[1], M[2]] : [navigator.appName, navigator.appVersion, '-?'];
        if ((tem = ua.match(/version\/(\d+)/i)) != null) {
            M.splice(1, 1, tem[1]);
        }
        return {
            name: M[0],
            version: M[1]
        };
    }

    function replaceGridsWithColumns() {
        var grids = document.getElementsByClassName(SUBMENU_GRID_LAYOUT);
        for (var i = 0; i < grids.length; i++) {
            var grid = grids[i];
            // Max it out at 10 rows per column
            grid.style.columnCount = Math.ceil(grid.children.length / 10);
        }
    }

    function replaceCategorizedLayoutWithColumns() {
        var categorizedSubmenus = document.getElementsByClassName(SUBMENU_CATEGORY_LAYOUT);
        for (var i = 0; i < categorizedSubmenus.length; i++) {
            var submenu = categorizedSubmenus[i];
            submenu.classList.remove(SUBMENU_CATEGORY_LAYOUT);
            submenu.classList.add(SUBMENU_COLUMN_LAYOUT);
            submenu.style.columnCount = 3;

            var categories = submenu.getElementsByClassName(SUBMENU_CATEGORY_CLASS);
            for (var j = 0; j < categories.length; j++) {
                var grid = categories[j].getElementsByClassName(SUBMENU_GRID_LAYOUT)[0];
                grid.style.columnCount = 1;
            }
        }
    }

    // IE11 doesn't support modern Element.matches. Instead, it has its own
    // matches, so replace the proto with its version
    if (!Element.prototype.matches) {
        Element.prototype.matches = Element.prototype.msMatchesSelector ||
            Element.prototype.webkitMatchesSelector;
    }

    // IE11 doesn't have Element.closest, so implement it:
    // https://developer.mozilla.org/en-US/docs/Web/API/Element/closest#Polyfill
    if (!Element.prototype.closest) {
        Element.prototype.closest = function (s) {
            var el = this;
            do {
                if (el.matches(s)) return el;
                el = el.parentElement || el.parentNode;
            } while (el !== null && el.nodeType === 1);
            return null;
        };
    }
};