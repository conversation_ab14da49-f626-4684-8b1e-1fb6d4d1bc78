All.Rights.Reserved.Label=Tutti i diritti riservati
Application.CalendarWidget.prompt=Visualizza Calendario
Application.Controls.Continue.prompt=Continua
Application.Controls.Print.prompt=Stampa
Application.Controls.Printable.prompt=Versione Stampabile
Application.Controls.Upload.prompt=Carica
Application.Controls.View.prompt=Lista
Application.Database.runtime.error=Il sistema ha rilevato un possibile problema del database. Si prega di contattare l'amministratore del sistema per ulteriore assistenza.
Application.Filter.EndMonth.prompt=Fine Mese
Application.Filter.StartMonth.prompt=Inizio Mese
Application.Filter.prompt=Filtro da:
Application.Help.error=File Guida non sono accessibili. Inserisci una lettera nel Cliente IDeaS portale o contattare il servizio Assistenza clienti IDeaS.
Application.Help.prompt=Aiuto
Application.Message.ConfirmCancel.prompt=Le modifiche apportate saranno perse. Continuare (S/N)?
Application.Message.ConfirmDisable.prompt=Disabilitare le voci selezionate?
Application.Navigator.BackOrForwardButton.error=Il tasto Avanti/Indietro del browser è stato utilizzato.
Application.NoUserSelection.error=Effettuare una selezione prima di eseguire questa operazione
Application.SessionExpired.prompt=Accedi
Application.TitleBar.Property.Capacity=Capienza:
Application.alertMessage.CTRL=Questo funzionamento non è permesso usando questa applicazione.
Chart.LINE.prompt=Linea
Chart.NoData.prompt=Nessuo dei dati per il grafico
Chart.PIE.prompt=Pie
ChartTable.NoData.prompt=Nessuna Data per la tabella
DefaultModule.Change.Message=Si prega di modificare il modulo predefinito Preferiti Utenti
Error.ErrorCodes.message=Il Sistema V5i ha verificato un errore. Ci scusiamo per il disagio che ciò ha provocato. Si prega di provare nuovamente il login. Se il problema persiste, si prega di creare un ticket nel Centro di supporto comunitario IDeaS in modo che possa essere rapidamente riparato.
Error.ErrorCodes.message1=your patience during this maintenance process.
Error.ErrorCodes.message2=L'IDeaS V5i System è temporaneamente non disponibile a causa di manutenzione programmata o distribuzione, o per il backup giornaliero del sistema. Ci scusiamo per gli eventuali disagi causati. Il sistema sarà disponibile al più presto quando questa attività prevista è stata completata.
Error.ErrorCodes.message3=L'IDeaS V5i System è temporaneamente non disponibile a causa del backup giornaliero del sistema. Ci scusiamo per il disagio causato. Si prega di provare a rifare il login entro 40 minuti.
Error.PageNotFoundError.title=La pagina richiesta non è stata trovata
FiscalCalendar.Database.load.error=errore caricamento dati per il calendario fiscale
FiscalCalendar.Definition.EndEndDate.range.error=Periodo Data di scadenza non può essere prima di qualsiasi precedente periodo Data di scadenza
FiscalCalendar.Definition.Error=Più periodi fiscali sono stati definiti per un singolo periodo Calendario.
FiscalCalendar.Definition.Period.range.error=Alcuni periodi sono più lunghi rispetto al limite consentito. Il limite consentito è indicato tra parentesi.
FiscalCalendar.Definition.StartEndDate.range.error=Periodo Data di scadenza non può essere precedente alla data di inizio
FiscalCalendar.Definition.format.error=Formato della data errata per il periodo fiscale Calendario. Formato corretto è {0}.
FiscalCalendar.FlexibleEndDate.Header=Data di Scadenza flessibile
FiscalCalendar.FlexibleStartDate.Header=Data di inizio flessibili:
FiscalCalendar.Month.Header=Periodo Fiscale
FiscalCalendar.NumDaysInPeriod.Header=Numero dei giorni
FiscalCalendar.Year.Title=Configurazione Calendario fiscale flessibile.
LRVbyRoomType.LRVoffset.Database.save.error=Salvare errore Ultima Camera Rapporto database Offset.
LRVbyRoomType.LRVoffset.LRVoffsetRange.error=Inserisci Ultima Camera valore di offset tra 0 e 100
LRVbyRoomType.LRVoffset.prompt=Camera Ultimo Valore Offset
LRVbyRoomType.LRVoffsetPercent.prompt=Ultima Camera Valore Offset %
LRVbyRoomType.menu.title=Ultimo Valore della Camera da Camera Tipo
Login.ContactInfo.Address.prompt=La Communità IDeS
Login.Error.Prompt=Errore(i):
Login.Failure.AlreadyLoggedIn.error=Questo utente ha già effettuato l''accesso all''applicazione
Login.Failure.IpSecurityRestriction.Error=Si sta tentando di accedere al sistema IDeaS V5i da una rete non autorizzata.
Login.Failure.UserInActive.Error=Il tentativo di login è fallito. L''ID utente è inattivo. Si prega di contattare l''amministratore della vostra azienda per l''assistenza.
Login.Failure.UserInActive.MaximumInactivityPeriodConsumed.Error=tentativo di login è fallito. Il periodo massimo inattivo è scaduto e il tuo account è ora contrassegnato come inattivo. Si prega di contattare l''amministratore della vostra azienda per l''assistenza.
Login.Failure.UserInActive.NumberOfUnsuccessfulAttemptConsumed.Error=Il tentativo di login è fallito. Si sono verificati eccessivi errori di login e il vostro account è ora contrassegnato come inattivo. Si prega di contattare l''amministratore della vostra azienda per l''assistenza.
Login.Failure.UserInActive.PasswordExpiryPeriodConsumed.Error=Il tentativo di login è fallito. La password è scaduta e non è più valida. Si prega di contattare l''amministratore della vostra azienda per l''assistenza.
Login.Failure.error=Il tentativo di login è fallito. L''ID utente o password non può essere corretto. Si prega di contattare l''amministratore della vostra azienda per l''assistenza.
Login.Failure.error.License.MissingProperties=Si prega di installare prima una licenza.
Login.Fatal.Preferences.error=Errore fatale. Incapace di caricare le preferenze dell'' utente. Contattare il gestore del sistema.
Login.Fatal.error=Errore Fatale. Contattare l''Amministratore del Sistema
Login.IDeaS.A.SAS.COMPANY.Label=IDeaS - A SAS COMPANY
Login.Login.Go.Prompt=Vai
Login.Logout.Back.Prompt=Vai Indietro
Login.Logout.prompt=Esci
Login.PasswordExpiry.error=Password è scaduta!!!
Login.ProductInfo.Address.prompt=IDeaS.com
Login.ProductInfo.prompt=Informazione Prodotto
Login.RememberMe.prompt=Ricordare le mie informazioni
Login.Security.ChangePassword.Alert.Message=Si prega di cambiare la password prima di uscire o l''ID utente diventerà inattivo.
Login.Security.IPSecurity.ALL.Label=Tutti gli indirizzi IP (senza restrizioni)
Login.Security.IPSecurity.AddRows.Label=Inserisci Riga
Login.Security.IPSecurity.AllowAccessFrom.Label=Permettere l''accesso da
Login.Security.IPSecurity.IPSecurityMaster.Deletion.Confirm=Uno o più indirizzi IP saranno cancellati. Volete continuare?
Login.Security.IPSecurity.IPSecurityMaster.Duplicate.Error=Indirizzo IP duplicato
Login.Security.IPSecurity.IPSecurityMaster.IPSecurityAddress.Error=Si prega di inserire un indirizzo IP valido
Login.Security.IPSecurity.IPSecurityMaster.IPSecurityDescription.Error=Descrizione in bianco per uno o più indirizzi IP.
Login.Security.IPSecurity.IPSecurityMaster.NoIPsDefined.Error=Definire almeno un indirizzo IP
Login.Security.IPSecurity.IPSecurityMaster.Retrieve.Error=Errore durante il recupero dell'' IP  sicurezza informazione Principale
Login.Security.IPSecurity.IPSecurityMaster.Update.Error=Errore durante l''aggiornamento di protezione IP
Login.Security.IPSecurity.IPSecurityPolicy.Retrieve.Error=Errore nel recupero di protezione IP informazione polizza
Login.Security.IPSecurity.IPSecurityPolicy.Update.Error=Errore aggiornando informazioni di politica di sicurezza del IP
Login.Security.IPSecurity.Inherited.Label=IP address come per limitazione livello della catena
Login.Security.IPSecurity.IpAddressDescription.Label=Descrizione Indirizzo IP
Login.Security.IPSecurity.IpAdress.Label=Indirizzo IP
Login.Security.IPSecurity.NoIpsDefined.Message=Nessuno degli indirizzi IP sono stati definiti
Login.Security.IPSecurity.Restricted.Label=Selezione di indirizzi IP
Login.Security.PasswordExpiry.Alert.Message=La password scadrà in {0} giorni, {1} ore, {2} minuti. Si prega di modificare la password o contattare l''amministratore della vostra azienda per l''assistenza.
Login.Signup.message=Inserisci il tuo ID Utente e Password
Login.Signup.prompt=Accedi
NoAccess.MultiPropertyView.Message.prompt=Questo modulo non è attualmente disponibile nella proprietà Multi Visualizzazione. Selezionare una proprietà per visualizzare questo modulo.
NoAccess.NoAccess.Message.prompt=L'accesso "{0}" al modulo è negato. {1}
NoAccess.SystemMaintenance.MultiProperty.Message.prompt=Questa visione non è disponibile, mentre una o più proprietà ha un nuovo software installato.
NoAccess.SystemMaintenance.SingleProperty.Message.prompt=Questa proprietà non è disponibile mentre il nuovo software è stato installato.
Notes.Bar.Heading.Label=Bar Decisione Sovrascrittura Note
Notes.Bar.Save.Label=Le note sono salavate soltanto dal tasto ESEGUITO sullo schermo principale.
Notes.Cancel.Alert=Questo rimuoverà le note che avete applicato. Fare clic su OK se si desidera procedere con la cancellazione.
Notes.Data.Unavailable.Message=Nessuna cronologia disponibile.
Notes.GroupwashOverride.Container1.Heading.Label=Attesa gruppo Sovrascrive Note:
Notes.GroupwashOverride.Heading.Label=Gruppo in Attesa Sovrascrittura Note
Notes.GroupwashOverride.Save.Alert=Note sono salvate soltanto con il tasto ESEGUITO/ANNULLA sullo schermo principale
Notes.Notes.help=Data notepad specifici.
Notes.Notes.title=
Notes.Save.Alert=Le note sono salvate solo dal pulsante APPLICA nella schermata principale
Notes.SystemOverride.Container1.Heading.Label=Nota Sovrascrittura del sistema
Notes.SystemOverride.Heading.Label=Sistema Sovrascrittura Note
Notes.SystemOverride.Save.Label=Le note sono salvate solo dai tasti ESEGUITO/APPLICA nella schermata principale
Notes.WalkMgmt.Heading.Label=Note Trasferimento Gestione
RoomClass.Database.Delete.Error=Errore durante l'eliminazione dei dati Classe Camera.
RoomClass.Database.Load.Error=Errore nel caricamento dei dati della categoria Camera.
RoomClass.Database.Update.Duplicate.Error=Il codice categoria camera con lo stesso nome già esiste. Inserire un nome differente del codice categoria camera.
RoomClass.Database.Update.Error=Errore nell' Aggiornamento dati Categoria Camera.
RoomClass.Entry.NotOfSameType.Message=Il tipo di camera ed il codice categoria camera selezionato devono avere lo stesso tipo.
RoomClass.NoRomTypesAvailable.Error.Message=Nessuno dei tipi camera non mappati disponibile per la mappatura. Almeno un locale tipo deve essere associato con la Categotia Camera.
RoomClass.NoRoomClassesConfigured.Message=Nessuna categoria camere è stata configurata.
RoomClass.RoomClass.3.Label=PMS
RoomClass.RoomClass.4.Label=CRS
RoomClass.RoomClass.5.Label=Codice categoria della Camera PMS
RoomClass.RoomClass.6.Label=CRS Categoria Camera
RoomClass.RoomClassDescription.Label=Descrizione Categoria Camera
RoomClass.RoomClassName.Error.Message=Si prega di inserire il Nome della Categoria Camera, Descrizione Categoria Camera ed allegare uno o più tipi di camera della stessa categoria.
RoomClass.RoomClassName.Label=Categoria Nome Camera
RoomClass.RoomClassType.Label=Categoria Camera Tipo
RoomClass.RoomClasses.Label=Classe Camere
RoomClass.RoomTypeAllocation.Label=Assegnazione Tipo Camera
RoomClass.RoomTypeNotSelected.Error.Message=Seleziona una o più tipologie di camere. La Camera Tipo di tutte le stanze selezionate devono essere le stesse della Classe Tipologia Camera.
RoomClass.RoomTypes.Unavailable.Label=Tutti i tipi di camere sono assegnate.
RoomClass.UnMappedRoomClass.Label=Alcune tipologie di camere potrebbero essere state cancellate. Si prega di associare le categorie delle camere con almeno una o più tipologie di camere.
RoomClass.UnassignedRoomTypes.Label=Camere Tipo Non assegnate
Services.BDE.prompt=(Fine Giornata Lavorativa)
Services.Failure.Message.1.prompt=Alcuni servizi necessari per V5 idee non sono stati eseguiti a causa di problemi.
Services.Failure.Message.2.prompt=Si prega di contattare l\\'amministratore di sistema. Se si sceglie di continuare il sistema sar\u00E0 in modalit\u00E0 SOLO-LETTURA.
Services.Schedule.Run.Code.10=Vecchio Estratto Caricato
Services.Schedule.Run.Code.100=Errore del database si è verificato nel controllo di integrità.
Services.Schedule.Run.Code.1000=Guasto generale
Services.Schedule.Run.Code.1001=PreBDE-Rezume:: Errore fatale
Services.Schedule.Run.Code.1002=PreBDE-Rezume:: Errore di configurazione.
Services.Schedule.Run.Code.1003=PreBDE-Rezume:: Errore non valido di invocazione
Services.Schedule.Run.Code.1004=PreBDE-Rezume\:\: Estratto gi\u00E0 popolato. Ripetere l\\'errore dell\\'estratto.
Services.Schedule.Run.Code.101=Settori di mercato Non assegnati sono stati scoperti. Si prega di assegnare tutti i Settori di mercato, dal sistema di gestione - Mercato schermo a Settori di mercato adeguati a gruppi di Settori. Gruppi di Settori di mercato devono essere creati prima di assegnare Settori di mercato.
Services.Schedule.Run.Code.102=Nessuno dei Gruppi Segmento di mercato sono stati configurati. Gruppi Segmento di mercato devono essere creati e dovrebbero essere assegnati ad appropriati Gruppi Segmento di mercato adeguati dal Sistema di Gestione - Schermo Segmenti Mercato
Services.Schedule.Run.Code.1092=Impossibile decomprimere estratto.
Services.Schedule.Run.Code.1093=File valido non trovato nell\\'estratto.
Services.Schedule.Run.Code.1095=PreBDE-Rezume:: Errore sconosciuto
Services.Schedule.Run.Code.1096=Proprietà / mappatura PMS errato.
Services.Schedule.Run.Code.1097=La foto sembra essere danneggiata o ha un formato compresso non valido.
Services.Schedule.Run.Code.1098=Parametri per Pre-BDE non sono configurati nel sistema
Services.Schedule.Run.Code.1099=Pre-BDE non è configurato nel sistema
Services.Schedule.Run.Code.110=Chiedete al vostro amministratore del sistema per configurare il costo di default del trasferimento.
Services.Schedule.Run.Code.1100=Errore di database in cluster cedimento si è verificato.
Services.Schedule.Run.Code.120=Chiedete al vostro amministratore del sistema di selezionare il livello di restrizione del tariffario rack.
Services.Schedule.Run.Code.1400=Guasto totale:: Il servizio BAR non ha potuto calcolare le decisioni per la proprietà data.
Services.Schedule.Run.Code.1401=Successo parziale:: Le decisioni BAR non potevano essere calcolate per tutti i giorni per una proprietà data.
Services.Schedule.Run.Code.1402=Guasto totale\: Le decisioni BAR non potevano essere calcolate a causa dell\\'errore DataBase
Services.Schedule.Run.Code.1403=Successo parziale:: Servizio Bar non poteva calcolare le decisioni per tutti i giorni a causa del piano tariffario mancante.
Services.Schedule.Run.Code.1404=Guasto totale\: Le decisioni BAR non potevano essere salvate a causa dell\\'errore Database
Services.Schedule.Run.Code.200=Populazione mancante.
Services.Schedule.Run.Code.201=Popolazione mancante:: PMSExtract.zip file non trovato.
Services.Schedule.Run.Code.202=Popolazione mancante:: Impossibile decomprimere file PMSExtract.zip
Services.Schedule.Run.Code.203=Popolazione mancante:Nome Proprietà invalida in CSV.
Services.Schedule.Run.Code.204=Data di sistema nel database e data di sistema in file CSV sono gli stessi.
Services.Schedule.Run.Code.205=Popolazione mancante:: Posizione impropria CSV.
Services.Schedule.Run.Code.206=Popolazione mancante:: nome della proprietà impropria nel file di immagine. Si prega di verificare file immagine caricato.
Services.Schedule.Run.Code.207=Popolazione mancante:: Data impropria per le date nel file di immagine. Si prega di verificare file immagine caricato
Services.Schedule.Run.Code.208=Popolazione mancante:: Errore durante la conversione in XML CSV
Services.Schedule.Run.Code.211=Popolazione Servizio:: NS_CREATE_TEMP_TABLES_FAILED:: Impossibile creare le tabelle temporanee mentre modificando NoShows .
Services.Schedule.Run.Code.212=Popolazione Servizio:: NS_UPDATE_MARKET_TABLES_FAILED:: Impossibile aggiornare le tabelle delle POM del mercato, mentre modificando NoShows.
Services.Schedule.Run.Code.213=Popolazione Service:: NS_UPDATE_TOTAL_TABLES_FAILED:: Impossibile aggiornare le tabelle PMS totale, mentre la regolazione NoShows.
Services.Schedule.Run.Code.214=Popolazione Servizio:: NS_UPDATE_NOSHOW_ROOMS_TABLE_FAILED:: Impossibile aggiornare lo stato di sincronizzazione nelle modifiche dello stato No Show
Services.Schedule.Run.Code.221=Popolazione Servizio:: OOO_CREATE_TEMP_TABLES_FAILED:: Impossibile creare le tabelle temporanee durante la regolazione OOO.
Services.Schedule.Run.Code.222=Popolazione Servizio:: OOO_UPDATE_TOTAL_TABLES_FAILED:: Impossibile aggiornare le informazioni Hotel di livello durante la regolazione OOO.
Services.Schedule.Run.Code.223=Popolazione Servizio:: NS_UPDATE_NOSHOW_ROOMS_TABLE_FAILED:: Impossibile aggiornare lo stato di sincronizzazione in OOO regolazione.
Services.Schedule.Run.Code.2300=Errore:: Application Server è stato arrestato prima che potesse completare CDP.
Services.Schedule.Run.Code.231=Errore nell\\'elaborazione del XML.
Services.Schedule.Run.Code.232=Errore convalidando file.
Services.Schedule.Run.Code.233=Errore durante l\\'esecuzione di convalida
Services.Schedule.Run.Code.234=Errore convalidando proprietà.
Services.Schedule.Run.Code.235=Proprietà invalida
Services.Schedule.Run.Code.236=Errore convalidando la data.
Services.Schedule.Run.Code.237=Vecchi dati shopping tariffario.
Services.Schedule.Run.Code.238=Errore fdi lettura dati tariffari.
Services.Schedule.Run.Code.239=Errore di inizializzazione lettore XML.
Services.Schedule.Run.Code.24=Guasto: Pre-BDE non riuscito. Salta BDE e Post-BDE
Services.Schedule.Run.Code.240=Errore di analisi XML.
Services.Schedule.Run.Code.241=Errore intermedio della creazione della tabella database
Services.Schedule.Run.Code.242=Errore intermedio della creazione della tabella database
Services.Schedule.Run.Code.243=Creazione errore batch file
Services.Schedule.Run.Code.244=Errore di esecuzione Batch file.
Services.Schedule.Run.Code.245=Errore importando dati.
Services.Schedule.Run.Code.246=Errore aggiornamento database
Services.Schedule.Run.Code.247=Errore nell\\'azzeramento auto.
Services.Schedule.Run.Code.248=Errore mancante dei dati di Active ms dall\\'immagine
Services.Schedule.Run.Code.249=Errore attivo dati ms sopra l\\' ultimo giorno della finestra
Services.Schedule.Run.Code.25=Guasto:: Pre-BDE riuscito.  BDE non riuscito. Salta Post-BDE
Services.Schedule.Run.Code.250=Errore bloccando Tabelle Tariffarie Shopping.
Services.Schedule.Run.Code.251=Popolazione non riuscita\:\: Errore durante l\\'analisi delle informazioni di intestazione csv. Assicurarsi che i file csv hanno un intestazione di informazione valida.
Services.Schedule.Run.Code.252=Popolazione non riuscita:: Informazioni di intestazione contradditorie. Intestazioni CSV devono disporre di informazioni coerenti attraverso i file.
Services.Schedule.Run.Code.254=GROUP_EXTRACT_DATE parametro, necessario per la popolazione Gruppo estratto è mancante in propertysystemparameters.
Services.Schedule.Run.Code.255=Gruppo licenza Attesa \u00E8 presente, ma i file del gruppo Estratto sono mancanti dall\\'estrazione.
Services.Schedule.Run.Code.256=Gruppo estratto contiene un Segmento di mercato che non è presente nella tabella propertyMarkets
Services.Schedule.Run.Code.257=Parser SAX non richiesto per la popolazione del gruppo di estrazione
Services.Schedule.Run.Code.258=Errore generale durante l\\'elaborazione del gruppo di estrazione file XML. Per i dettagli
Services.Schedule.Run.Code.259=Verificare l\\'Installazione
Services.Schedule.Run.Code.26=Guasto:: Pre-BDE e BDE riusciti. Post-BDE Non riuscito
Services.Schedule.Run.Code.260=Gruppo estratto errore di convalida. Gruppo di estrazione non è conforme alla GroupExtract.dtd come indicato nel V5i Specifiche Integration (3.1.3).
Services.Schedule.Run.Code.261=Gruppo di popolazione di estrazione richiede un parser di convalida. Nessuna convalida parser XML trovata.
Services.Schedule.Run.Code.262=l\\'estratto del gruppo \u00E8 gi\u00E0 popolato.
Services.Schedule.Run.Code.263=Errore generale mentre archivia l\\'estratto CSV del gruppo di elaborazione. Per i dettagli
Services.Schedule.Run.Code.264=Impossibile aggiornare GROUP_EXTRACT_DATE in propertysystemparameters.
Services.Schedule.Run.Code.265=CSV Gruppo estratto non è conforme alle specifiche V5i Integration (3.1.3).
Services.Schedule.Run.Code.266=Gruppo estratto file csv _gm.csv e _gd.csv contengono diversi captureTime OR captureDate .
Services.Schedule.Run.Code.267=Vecchi dati di campagna.
Services.Schedule.Run.Code.268=Dati di campagna della lettura di errore.
Services.Schedule.Run.Code.269=Campagna estratto popolazione è invocata anche quando la campagna Licenza non è attiva.
Services.Schedule.Run.Code.271=L\\'parametro LAST_TRANSACTION_EXTRACT_DATE, richiesto per la popolazione dell\\'estratto di transazione manca nei propertysystemparameters.
Services.Schedule.Run.Code.272=L\\'estratto non contiene gli schedari di dati della transazione.
Services.Schedule.Run.Code.273=L\\'estratto di transazione contiene un settore di mercato che non \u00E8 assente nella tabella dei propertyMarkets
Services.Schedule.Run.Code.274=Errore di convalida dell\\'estratto di transazione
Services.Schedule.Run.Code.275=L\\'estratto di transazione gi\u00E0 \u00E8 popolato.
Services.Schedule.Run.Code.276=Transazione estratto CSV errore processando
Services.Schedule.Run.Code.277=Non ha potuto aggiornare LAST_TRANSACTION_EXTRACT_DATE in propertysystemparameters.
Services.Schedule.Run.Code.278=L\\'estratto Transazione CSV non si conforma alle specifiche di integrazione di V5i (3.1.4). Intestazione non valida
Services.Schedule.Run.Code.279=L\\'estratto Transazione CSV non si conforma alla codifica di specifiche di integrazione di V5i (3.1.4).
Services.Schedule.Run.Code.3=Errore:: Esecuzione simultanea di servizio.
Services.Schedule.Run.Code.300=Previsione Non riuscita .
Services.Schedule.Run.Code.3200=Totale  successo: Generazione rapporto è andato a buon fine
Services.Schedule.Run.Code.3201=Guasto totale\:\: Programmato.Rapporti salvati. Percorso non \u00E8 configurato nell\\'Ambiente.file di propriet\u00E0
Services.Schedule.Run.Code.3202=Guasto totale\:\: Programmato.Rapporti.Installazione.Percorso non \u00E8 configurato nell\\'Ambiente.file di propriet\u00E0
Services.Schedule.Run.Code.3203=ATTENZIONE:: Programmato.Rapporti.Allegati .Dimensione non è configurata nel file Ambiente.proprietà [opzionale] Predefinito
Services.Schedule.Run.Code.3204=AVVISO:: La lista di ingresso processata è vuota
Services.Schedule.Run.Code.3205=Successo parziale:: Non ci sono rapporti previsti per questa proprietà
Services.Schedule.Run.Code.3206=Guasto totale:: Errore nella Generazione del Rapporto
Services.Schedule.Run.Code.3207=Successo parziale:: Errore consegna di posta
Services.Schedule.Run.Code.3400=Servizio SAT: Guasto generale.
Services.Schedule.Run.Code.3401=I Campi lavoro sono mancanti dal database.
Services.Schedule.Run.Code.3402=Servizio SAT: Errore Database.
Services.Schedule.Run.Code.3403=Il servizio Sat non è stato eseguito perchè propertylist era vuoto.
Services.Schedule.Run.Code.3800=ERRORE:::GENERALE
Services.Schedule.Run.Code.3801=ERRORE:::Servizio
Services.Schedule.Run.Code.3802=Errore::: Nessun aggiornamento, come richiesto / cambiare esistenza soluzione downgrade
Services.Schedule.Run.Code.3803=ERRORE::: Nessuna richiesta downgrade di aggiornamento è in corso.
Services.Schedule.Run.Code.3804=ERRORE:::Più di un processo di downgrade di aggiornamento è in corso.
Services.Schedule.Run.Code.3805=ERRORE::: Richiesta per modificare la soluzione è arrivata, ma la stessa soluzione è già installata.
Services.Schedule.Run.Code.3806=ERRORE::: Sistema Oggi non trovato.
Services.Schedule.Run.Code.3807=ERRORE::: Nome Proprietà non trovata
Services.Schedule.Run.Code.4=Errore:: Impossibile scrivere le tabelle di contabilità.
Services.Schedule.Run.Code.400=Ottimizzazione Fallito.
Services.Schedule.Run.Code.5=Errore::: Lavoro-Secondario saltato a causa del guasto del predecessore.
Services.Schedule.Run.Code.500=Il monitoraggio ha fallito.
Services.Schedule.Run.Code.600=La raccomandazione non è riuscita.
Services.Schedule.Run.Code.622=Impossibile generare dati relativi alla campagna in uscita
Services.Schedule.Run.Code.623=Manuale di campagna upload è invocato anche quando la campagna di Licenza non è attiva.
Services.Schedule.Run.Code.624=Configurazione errata per l\\'amministrazione della campagna
Services.Schedule.Run.Code.7=Il Servizio Fine Giornata è attualmente in esecuzione.
Services.Schedule.Run.Code.800=Servizio traduttore ha fallito.
Services.Schedule.Run.Code.9=Rilasciato con Licenza
Services.Schedule.Run.Code.900=Servizio di caricamento non ha esito.
Services.Schedule.Run.Code.CDPRunning=Elaborazione Giorno Corrente (EGC) il servizio è attualmente in esecuzione.
Services.Schedule.Run.Code.NoBARLicense=Licenza BAR non disponibile. Impossibile eseguire il servizio BAR.
Services.Schedule.Run.Code.NoMSGUnQualified=Almeno un MSG deve essere contrassegnato come Qualificato. Impossibile eseguire il servizio BAR.
Services.Schedule.Run.Code.Running=Il Business Day End (BDE) servizio è attualmente in esecuzione.
Services.Schedule.ScheduledForDay.Daily.prompt=Ogni giorno
Services.Schedule.ScheduledForDay.Weekly.prompt=Sempre
Services.Schedule.ScheduledForDay.prompt=Previsto per
Services.Schedule.ScheduledForTime.Hours.prompt=Ore
Services.Schedule.ScheduledForTime.prompt=Ora
Services.Schedule.help=Programma di servizi di sistema
Services.Scheduler.Run.EveryWeek=Ogni Settimana in
Services.Scheduler.Schedule.Add.AvailableServices.Error=Non sono stati configurati servizi nel sistema
Services.Scheduler.Schedule.Add.Error=Il servizio specificato non può essere aggiunto al programma
Services.Scheduler.Schedule.RunOption.1.prompt=Esegui immediatamente
Services.Scheduler.Schedule.RunOption.2.prompt=Esguire questo operazione su
Services.Scheduler.Schedule.RunOption.2.prompt.Hint=Lavori con date precedenti, verranno eseguite immediatamente.
Services.Scheduler.Schedule.RunOption.3.prompt=Eseguire periodicamente
Services.Scheduler.Schedule.Validation.DayOfMonth=Il Giorno-De-Mese non può avere valori alfanumerici
Services.Scheduler.Schedule.Validation.DaysInMonth.Range=Numero errato dei giorni nel mese
Services.Scheduler.Schedule.Validation.DiscreteRunDate.Empty=Run-On-Data non può essere lasciato vuoto
Services.Scheduler.Schedule.Validation.DiscreteRunDate.Error=L'esecuzione-Su-Data ha un formato data non corretto
Services.Scheduler.Schedule.Validation.DiscreteRunDateDay=Il giorno di esecuzione non può avere valori alfanumerici
Services.Scheduler.Schedule.Validation.DiscreteRunDateMonth=Il mese non può avere valori alfanumerici
Services.Scheduler.Schedule.Validation.DiscreteRunDateYear=L'anno non può avere valori alfanumerici
Services.Scheduler.Schedule.Validation.DiscreteRunDateYear.Range=L'anno non può essere precedente
Services.Scheduler.Schedule.Validation.Hours.Range=Ore dovrebbero essere tra il campo  0-23
Services.Scheduler.Schedule.Validation.Minutes.Range=Minuti devono essere compresi tra il campo 0-59
Services.Scheduler.Schedule.Validation.Month.Range=Il mese deve essere fra l'intervallo 1-12
Services.Scheduler.Schedule.Validation.TimeOfRunHoursDiscrete=Tempo di percorrenza (ore) non possono avere valori alfanumerici o hanno spazi
Services.Scheduler.Schedule.Validation.TimeOfRunMinutesDiscrete=Tempo di percorrenza (minuti) non può avere valori alfanumerici o spazi
Services.Scheduler.Schedule.prompt=Programma del funzionamento
Services.Scheduler.Schedule.prompt.allProperties=Programma per tutte le proprietà
Services.Scheduler.Service.prompt=Servizio da programmare
Services.Status.CompletedAt.prompt=Completata a
Services.Status.ScheduledAt.prompt=Programmato a
Services.Status.StatusOfRun.prompt=Stato di Esecuzione
Services.Status.help=log attività di servizi del sistema.
Services.Warning.Message.1.prompt=Alcuni servizi di IDeaS hanno generato avvertenze.
SysManagement.Property.Parameter.PropertyUnderMaintenance.Update.Error=Errore durante l'elaborazione della richiesta di aggiornamento dei parametri IS_PROPERTY_UNDER_MAINTENANCE.
System.Bookkeeping.Request.Failed=La richiesta di contabilità non è riuscita.
System.BudgetDefinition.Menu.Name=Definizione del preventivo
System.CSVUpload.Bookeeping.Insert.Message=Estratto ricevuto - Utilizzo CSVUpload Utilità
System.GlobalSystemParameters.UPGRADE_DOWNGRADE_MAILING_LIST=Mailing list al quale dovrebbe andare upgrade / downgrade  successo / insuccesso e-mail.
System.MarketSegment.Controls.Groups.prompt=Gruppi
System.MarketSegment.Controls.MoveGroups.prompt=Assegnare i settori di mercato a
System.MarketSegment.Database.load.error=Errore caricamento databse segmento di mercato
System.MarketSegment.Database.update.error=Errore dell'aggiornamento database del segmento di mercato
System.MarketSegment.DisplayOverride.prompt=Sovrascrivere Display
System.MarketSegment.DisplayOverrideMessage.prompt=Questo nasconderà dalla visualizzazione qualsiasi sovrascrittura (# e *) fatta contro questo Gruppo Settore di Mercato in tutto V5i, compresi i rapporti.
System.MarketSegment.Message.ConfirmAction.prompt=Modifica dei segmenti di mercato e Gruppi Segmento di Mercato influiscono le previsioni di occupazione, restrizioni e overbooking. Per un periodo di 30 a 90 giorni, il Sistema IDeaS deve riqualificarsi.
System.MarketSegment.Message.NoMarketSegment.prompt=Nessuno dei segmenti di mercato unmapped trovati
System.MarketSegment.NoData.prompt=Nessuno dei settori di mercato
System.MarketSegment.Qualified.prompt=Qualificato
System.MarketSegment.Remapped.warning=Apportare modifiche alla configurazione del Gruppo Segmento di Mercato possono avere un impatto: le previsioni di occupazione, le restrizioni, Overbooking valori, la sovrascrittura di sistema e dei dati per il Segmento di mercato a livello di Gruppo visualizzate nell'interfaccia utente e report.
System.MarketSegment.Remapped.warning.alert=Sono state apportate modifiche alla configurazione del Segmento di mercato del Gruppo, che avr\u00E0 un impatto sul sistema IDeas. A seconda della portata delle modifiche, si pu\u00F2 notare\: - \n - Variazioni alle previsioni di occupazione, restrizioni e Overbooking valori \n - Esegue l'override di sistema \u00E8 entrato per date future e saranno rimosse dal sistema \n - Segmento di mercato dati a livello di Gruppo saranno disponibili all'interno dell'interfaccia utente o sui report per le date antecedenti al cambiamento. \n \n Se desiderate maggiori informazioni prima di procedere, selezionare ANNULLA e digitare un tasto nel portale Cliente IDeaS o contattare il servizio Assistenza clienti IDeaS. Se si desidera procedere con questa configurazione, selezionare OK.
System.MarketSegment.Ungrouped.error=Settori di mercato non assegnati sono stati rilevati. Si prega di assegnarli a gruppi di Settori di mercato adeguato.
System.MarketSegment.database.delete.error=Impossibile eliminare i dati.
System.MarketSegment.database.load.error=Errore durante il caricamento del sistema note Override.
System.MarketSegment.database.unavailable.error=Database non disponibile, verificare se il servizio è in esecuzione.
System.MarketSegment.database.wash.delete.error=Impossibile eliminare i dati futuri di attesa.
System.MarketSegment.help=Assegnazione gruppo segmento di mercato
System.MarketSegmentGroup.Database.delete.error=Errore di cancellazione della base dati dei gruppi del segmento di mercato
System.MarketSegmentGroup.Database.load.error=Il segmento di mercato raggruppa l'errore della base dati
System.MarketSegmentGroup.Database.update.duplicaterow.error=Il gruppo del segmento di mercato con lo stesso nome già esiste
System.MarketSegmentGroup.Database.update.error=Il segmento di mercato raggruppa l'errore dell'aggiornamento della base dati
System.MarketSegmentGroup.Description.prompt=Descrizione Gruppo
System.MarketSegmentGroup.ForecastType.Impure.error=Questo Gruppo Segmento di Mercato è un Segmento di mercato impostato su Previsione = Sui libri, quindi tutti i segmenti di mercato in questo Gruppo Segmento di Mercato devono essere configurati come previsione = sui libri.
System.MarketSegmentGroup.Message.ConfirmAction.prompt=Cambiare un Gruppo Segmento di Mercato da un tipo di business a un altro colpisce previsioni di occupazione, restrizioni, overbooking e informazioni storiche hotel in IDeaS. Per un periodo di 30 a 90 giorni, il Sistema IDeaS deve riqualificarsi. Cronologia Informazioni Hotel in IDeas non può essere aggiornato per riflettere il cambiamento dei Gruppi Segmento di Mercato.
System.MarketSegmentGroup.Name.Required.error=Il nome di gruppo del settore di mercato è richiesto
System.MarketSegmentGroup.NoData.prompt=Nessuno dei gruppi segmento di mercato
System.MarketSegmentGroup.NoMarketSegGroup.message=Nessuno dei Gruppi Segmento di Mercato sono stati configurati.
System.MarketSegmentGroup.SystemForecastWashType.Impure.error=Questo Gruppo Segmento di Mercato è un Segmento di Mercato che ha selezionato "Previsioni del sistema di attesa", quindi tutti i Segmenti di mercato in questo Gruppo Settore di Mercato devono selezionare lavaggio Previsioni del sistema.
System.MarketSegmentGroup.UsageType.Impure.error=Questo Gruppo Segmento di Mercato contiene un Segmento di mercato che si configura come non-qualificato, nel qual caso tutti i segmenti di mercato in questo Gruppo Segmento di Mercato devono essere configurati come non-qualificati.
System.MarketSegmentGroup.combination.bifTranient.error=Il commercio transitorio, il metodo di prenotazione dai blocchi = sì, previsione = sui libri non è permesso.
System.MarketSegmentGroup.combination.error=Per le imprese transitorie, prenotazione metodo da blocchi = Sì, Previsioni = Su Prenotazione e su  Previsioni Sistema di Attesa = No, non è permesso.
System.MarketSegmentGroup.help=Installazione gruppo segmento di mercato.
System.Property.Address.State.prompt=Stato/provincia
System.Property.Address.Zip.prompt=Codice Postale/ CAP
System.Property.Controls.Preferences.prompt=Modifica Preferiti
System.Property.PMSLocation.prompt=Posizione PMS
System.Property.PMSName.prompt=Nome PMS
System.Property.Phone.prompt=Telefono
System.Property.SystemAdministrator.prompt=Amministratore Sistema
System.Property.SystemAdministratorEmail.prompt=Amministratore sistema e-mail
System.Property.help=Configurare proprietà informazione
System.PropertyConfig.BARDecisionType.1.prompt=Web Impact BAR
System.PropertyConfig.BARDecisionType.prompt=Per BAR Decisione Carica, usare come predefinito
System.PropertyConfig.BarChanged.warning.alert=Il tipo di decisione BAR definito è stato cambiato. Si prega di rivedere le decisioni BAR dopo la corsa di lavorazione successiva.
System.PropertyConfig.BarRateAvailableChanged.warning.alert=Le tariffe disponibili per la BAR di arrivo per tutte le date future verranno reimpostate su Sì.
System.PropertyConfig.BarRateMinAndMaxChanged.warning.alert=Le tariffe BAR Min e Max LOS per tutte le date future verranno ripristinati a 1 e 8.
System.PropertyConfig.BudgetDefinition.level=Definire il preventivo a
System.PropertyConfig.BudgetDefinition.level.change.message=Facendo clic sul pulsante Applica dopo aver modificato il tipo di Preventivo verranno eliminati tutti i dati del Preventivo definiti in precedenza, se presenti. Conferma.
System.PropertyConfig.CostOfWalk.prompt=Costo Predefinito del Trasporto Ospite
System.PropertyConfig.CostOfWalk.required.warning=Costo predefinito della trasferta non è impostata. Costo della licenza modulo trasferta non saranno disponibili.
System.PropertyConfig.CurrencyCode.required.warning=Il codice di valuta non è fissato.
System.PropertyConfig.Database.Update.error=Impossibile aggiornare i dati. Riprova. Se l'errore persiste, si prega di contattare il rappresentante dell'assistenza.
System.PropertyConfig.EarlisetDate.prompt=La prima data per i quali ci sono dati nel sistema
System.PropertyConfig.FiscalCalendar.preview=Anteprima
System.PropertyConfig.ForecastWindow.prompt=Numero di giorni nel sistema futuro dovrebbe generare previsioni
System.PropertyConfig.GroupPricingConfig.prompt=Prezzo Gruppo (inclusione/esclusione)
System.PropertyConfig.HighOccupancy.String.error=La percentuale di occupazione in cui è considerato un hotel di alto occupazione dovrebbe essere un valore numerico.
System.PropertyConfig.HighOccupancy.prompt=Percentuale di occupazione a cui l'hotel è considerato Alta occupazione
System.PropertyConfig.HighOccupancy.range.error=L'intervallo di valori per pecentuale Occupazione in cui è considerato un hotel Alta occupazione è compresa tra 0 e 100.
System.PropertyConfig.HighOccupancy.required.error=Inserire percentuale di occupazione al quale l'hotel è considerato Alta Occupazione.
System.PropertyConfig.IncContractGroupsForAltDates.prompt=Include i gruppi con contratto di condizione di materializzazione rilasciato per le date di arrivo alternate?
System.PropertyConfig.IncScenarioGroupsForAltDates.prompt=Include gruppi con lo scenario di materializzazione di Alternate Date di arrivo?
System.PropertyConfig.IncTentativeGroupsForAltDates.prompt=Includere Gruppi con lo stato di Materializzazione Provvisorio per Alternate Data di arrivo?
System.PropertyConfig.LowOccupancy.Greater.error=La percentuale di occupazione a cui l'hotel è considerato Bassa Occupazione non può essere maggiore della percentuale di occupazione a cui l'hotel è considerato Alta occupazione
System.PropertyConfig.LowOccupancy.String.error=La percentuale di occupazione a cui l'hotel è considerato occupazione bassa dovrebbe essere un valore numerico.
System.PropertyConfig.LowOccupancy.prompt=Occupazione percentuale al quale è considerato l'hotel è a  bassa Occupazione
System.PropertyConfig.LowOccupancy.range.error=Il campo di valore per la percentuale di occupazione in cui l'hotel è considerato Occupazione Bassa è fra 0 e 100.
System.PropertyConfig.LowOccupancy.required.error=Percentuale di occupazione inserita al quale è considerato l'hotel è di bassa occupazione.
System.PropertyConfig.MaxLOS.RequiredAsNumber.error=Lunghezza massima del soggiorno per le restrizioni deve essere un numero intero da 1 a 14.
System.PropertyConfig.NoRateIdentified.warning=Nessuna Tariffa nome in codice identificata.
System.PropertyConfig.NoRateSet.warning=Nessuna restrizione di livello. Nessun nome tariffario identificato in codice
System.PropertyConfig.RackRateCodeName.prompt=Nome Codice Tariffario
System.PropertyConfig.RoomServicingCost.required.error=Per Manutenzione dei Costi Camera deve essere maggiore o uguale a zero. Si prega di inserire i numeri senza virgole.
System.PropertyConfig.UseRankingForBAR.prompt=Applicare Classifica Tariffario Concorrente Web al BAR decisioni
System.PropertyConfiguration.CSAFrequency.prompt=Frequenza alla quale competitivo set di dati del foglio di lavoro sono iscritti
System.PropertyConfiguration.CSARate.prompt=Vota le informazioni disponibili per l'accesso concorrenziale Set foglio dati
System.PropertyConfiguration.Database.Duplicate.error=Proprietà con lo stesso nome o descrizione già esiste
System.PropertyConfiguration.Database.load.error=Errore caricando le informazioni della proprietà
System.PropertyConfiguration.Database.update.error=Errore aggiornamento informazioni proprietà
System.PropertyConfiguration.PropertyAccountId.prompt=ID Account Proprietà
System.PropertyConfiguration.PropertyAddress.required.error=Inserire un indirizzo valido
System.PropertyConfiguration.PropertyAdminEmail.prompt=E-Mail
System.PropertyConfiguration.PropertyAdminEmail.required.error=Si prega di inserire un ID e-mail valido
System.PropertyConfiguration.PropertyAdminName.required.error=Si prega di inserire un valido Nome Amministratore Sistema
System.PropertyConfiguration.PropertyCity.required.error=Inserire un nome valido Città
System.PropertyConfiguration.PropertyCountry.required.error=Il Nome Paese non è impostato
System.PropertyConfiguration.PropertyDescription.prompt=Descrizione Proprietà
System.PropertyConfiguration.PropertyDescription.required.error=Si prega di inserire una descrizione
System.PropertyConfiguration.PropertyFaxNumber.prompt=Numero Fax
System.PropertyConfiguration.PropertyFaxNumber.required.error=Si prega di inserire un numero fax valido
System.PropertyConfiguration.PropertyName.required.error=SI prega di inserire il nome
System.PropertyConfiguration.PropertyState.required.error=Si prega di inserire un Nome Stato valido
System.PropertyConfiguration.PropertyTeleNumber.prompt=Telefono
System.PropertyConfiguration.PropertyTeleNumber.required.error=Si prega di inserire un numero di telefono valido
System.PropertyConfiguration.PropertyZip.prompt=Codice Postale
System.PropertyConfiguration.PropertyZipCode.required.error=Impostare un codice postale valido
System.PropertyConfiguration.help=Configurare proprietà informazione
System.PropertyConfiguration.prompt=Gestisci le informazioni della proprietà
System.PropertyConfigurationEdit.help=Configurazione di informazioni della proprietà
System.PropertyConfigurationEdit.title=Informazioni Proprietà
System.PropertySystemParameter.BarMinandMax.Remark=Abilità LOS Min e Max in Installazione BAR Piano Tariffario
System.PropertySystemParameter.BarRateAvailable.Remark=Abilita tariffa disponibile per la configurazione Arrivo nel programma di installazione BAR Piano tariffario
System.PropertySystemParameter.Database.DuplicateCSVLocation=Questo percorso è utilizzato da altre proprietà; Immettere un percorso diverso.
System.PropertySystemParameter.Database.load.error=I parametri di sistema non potevano essere caricati
System.PropertySystemParameter.Database.loadForEdit.error=Dettagli del parametro selezionato non potrebbe essere caricati
System.PropertySystemParameter.ParameterList.NoData.prompt=Non ci sono parametri disponibili
System.PropertySystemParameter.Remark.2_WAY=Comunicazione bidirezionale
System.PropertySystemParameter.Remark.AUTH_CAPACITY=Autorizzare la capienza
System.PropertySystemParameter.Remark.AutoLumping=L'indicatore indica se la funzione Auto Unire caratteristica dovrebbe essere eseguito durante l'esecuzione successiva della BDE
System.PropertySystemParameter.Remark.AutoTimeOut=Il tempo massimo di inattività in minuti
System.PropertySystemParameter.Remark.BAR_DEFAULT_DECISION=Il valore predefinito per BAR Decisione Caricamento
System.PropertySystemParameter.Remark.BokingHours=Prenotazione Ore per la proprietà utilizzata da CDP a ridimensionare la richiesta per il giorno d'arrivo
System.PropertySystemParameter.Remark.BuzyPercentage=Definire percentuale occupata da usare liberamente
System.PropertySystemParameter.Remark.CBMDISPLAY=Parametro per abilitare / disabilitare il beneficio di misura
System.PropertySystemParameter.Remark.CDP_FCST_WINDOW=Finestra di previsioni per DayOpt
System.PropertySystemParameter.Remark.CSVLocation=Percorso della cartella sul server IDeaS dove PMS estratti verranno caricati.
System.PropertySystemParameter.Remark.CostOfWalk=Costo predefinito della trasferta, che sarà il fattore decisivo per overbooking.
System.PropertySystemParameter.Remark.EarliestDate=Prima data per i quali ci sono dati cronologia nel database.
System.PropertySystemParameter.Remark.ForceWeeklyDemandRevision=Se l'evento speciale creato per il sistema precedente dovrebbe forzare la revisione settimanale richiesta
System.PropertySystemParameter.Remark.ForecastWindow=Numero di giorni nel futuro nelle previsioni del sistema.
System.PropertySystemParameter.Remark.FromList=Mittente della posta stato BDE
System.PropertySystemParameter.Remark.GENERATE_XML_FPLOS=Generare FPLOS
System.PropertySystemParameter.Remark.GENERATE_XML_HTL_OVERBOOKING=Genera Overbooking Hotel
System.PropertySystemParameter.Remark.GENERATE_XML_MINLOS=Genera MINLOS
System.PropertySystemParameter.Remark.GENERATE_XML_OPPCOST=Generare l'ultimo valore della camera
System.PropertySystemParameter.Remark.GENERATE_XML_RMT_OVERBOOKING=Generare Camera - Tipo Overbooking
System.PropertySystemParameter.Remark.GenerateFAR=Parametro per Abilitare / Disabilitare Rapporto Precisione Previsioni.
System.PropertySystemParameter.Remark.GenerateFPLOS=Se il servizio di raccomandazioni generano le limitazioni di FPLOS.
System.PropertySystemParameter.Remark.GroupBookingCurveReviseNumDays=Frequenza della Revisione per la Curva Prenotazione Gruppo
System.PropertySystemParameter.Remark.GroupCutoffDaysToArrival=Sconto gruppi per gli arrivi
System.PropertySystemParameter.Remark.GroupDayofWeekReviseNumDays=Frequenza della revisione per il gruppo Giorno della settimana
System.PropertySystemParameter.Remark.GroupPeakDemandReviseNumDays=Frequenza della revisione per la domanda di punta del gruppo
System.PropertySystemParameter.Remark.GroupRateReviseNumDays=Frequenza della revisione per la tariffa di Gruppo
System.PropertySystemParameter.Remark.GroupSacleFactorReviseNumDays=Frequenza della revisione per il fattore di scala del gruppo
System.PropertySystemParameter.Remark.GroupWash=Gruppo Predefinito Attesa Percentuale Volume
System.PropertySystemParameter.Remark.GroupWashReviseNumDays=Frequenza della revisione per il gruppo in attesa.
System.PropertySystemParameter.Remark.MAX_LOS=Lunghezza massima del soggiorno per le restrizioni
System.PropertySystemParameter.Remark.Overbooking=Numero tipico delle camere dell'albergo prenotate in numero superiore ai posti disponibili per ogni giorno
System.PropertySystemParameter.Remark.PMSCommunication=LAN, WAN, Internet ecc
System.PropertySystemParameter.Remark.PMSDTDName=Nome del file DTD
System.PropertySystemParameter.Remark.PMSExtractFile=Nome del PMS File Estratto
System.PropertySystemParameter.Remark.PropertyLanguage=Selezionare la lingua predefinita per il sistema IDeaS
System.PropertySystemParameter.Remark.ROH=ROH
System.PropertySystemParameter.Remark.RoomServicingCost=Costi di manutenzione Camere utilizzato per il calcolo del valore display Gruppo Prezzo
System.PropertySystemParameter.Remark.RunAutoLumping=Abbiamo bisogno di rompere ulteriormente il Segmento di mercato del Gruppo Utente
System.PropertySystemParameter.Remark.SMTPupload_to=Risoluzione caricamento indirizzo
System.PropertySystemParameter.Remark.Scalingwindow=Finestra in scala per previsioni
System.PropertySystemParameter.Remark.Smtp=Il server SMTP
System.PropertySystemParameter.Remark.SystemInstallationDate=Data in cui il sistema è andato in diretta
System.PropertySystemParameter.Remark.TZ=Installazione Fuso Orario per Proprietà
System.PropertySystemParameter.Remark.ToList=Destinatari della posta stato BDE
System.PropertySystemParameter.Remark.TransientBookingCurveReviseNumDays=Frequenza della revisione per la curva transitoria di prenotazione
System.PropertySystemParameter.Remark.TransientDayofWeekReviseNumDays=Periodicità della revisione del giorno della settimana
System.PropertySystemParameter.Remark.TransientLOSReviseNumDays=Frequenza della revisione per LOS transitorio
System.PropertySystemParameter.Remark.TransientNoShow=Valore Percentuale Predefinito Transitorio No Show
System.PropertySystemParameter.Remark.TransientNoShowReviseNumDays=Frequenza della revisione per il No Show Transitorio
System.PropertySystemParameter.Remark.TransientPeakDemandReviseNumDays=Frequenza della revisione per la domanda di picco transitoria
System.PropertySystemParameter.Remark.TransientRateReviseNumDays=Frequenza della Revisione per Tariffa Transitoria
System.PropertySystemParameter.Remark.TransientSacleFactorReviseNumDays=Periodicità della revisione per il fattore di scala Transitorio
System.PropertySystemParameter.Remark.UnconstrainAlgorithm=Algoritmo non vincolato da utilizzare su richiesta settimanale
System.PropertySystemParameter.Remark.UnderMaintenance=Parametro per indicare se questa proprietà è nell'ambito di manutenzione.
System.PropertySystemParameter.Remark.Upload_key=Il nome della proprietà per il caricamento
System.PropertySystemParameter.Remark.Walks=Il numero tipico degli ospiti dell'hotel che sono dispostI a trasferimento per settimana
System.PropertySystemParameter.Remark.Weekenddays=Abbreviazione di tre lettere per i giorni della fine settimana
System.PropertySystemParameter.Remark.ZeroOvrBkCowRackRatio=Zero Overbooking COW Rack rapporto.
System.PropertySystemParameter.Remark.fractionOfExpectedWalks=Rapporto di Servizio
System.PropertySystemParameter.Remark.ftpip_address=Indirizzo I/P per caricamento
System.PropertySystemParameter.Remark.ftpremote_path=Percorso a distanza per il caricamento
System.PropertySystemParameter.Remark.ftpuser_name=Nome-utente FTP
System.PropertySystemParameter.Remark.ftpuser_password=Password FTP
System.PropertySystemParameter.Remark.upload_type=Il tipo di caricamento
System.PropertySystemParameter.Validation.UpdatedValue.CreateError=Il diretcory specificato non può essere creata.
System.PropertySystemParameter.Validation.UpdatedValue.DateFormat=La data non è valida
System.PropertySystemParameter.Validation.UpdatedValue.ForecastWindow.LowerBound=Il valore del parametro deve essere maggiore di 0
System.PropertySystemParameter.Validation.UpdatedValue.ForecastWindow.Range=Il valore di parametro non può superare 365.
System.PropertySystemParameter.Validation.UpdatedValue.MissingSeparators=Il percorso specificato non esiste
System.PropertySystemParameter.Validation.UpdatedValue.Negative=Il campo non può essere negativo
System.PropertySystemParameter.Validation.UpdatedValue.NumberFormat=Valore non corretto per un campo numerico
System.PropertySystemParameter.Validation.UpdatedValue.PathFormat=Il percorso specificato non esiste
System.PropertySystemParameter.Validation.UpdatedValue.Required=Il campo non può essere lasciato vuoto
System.PropertySystemParameter.help=Un insieme di parametri vengono utilizzati dal software per modificarne il comportamento secondo i requisiti dell' hotel.
System.PropertySystemParameter.title=Parametri della proprietà
System.RateCode.Controls.Filter.Current.prompt=Corrente: Scadenza >
System.RateCode.Controls.Filter.Past.prompt=Passato: <data di scadenza o
System.RateCode.DOW.Required.error=DOW selezione è richiesto
System.RateCode.Database.update.duplicaterow.error=Codice tariffario con lo stesso nome esiste già.
System.RateCode.Date.Range.error=Sovrapposizione dati con (uno o più) voci esistenti per questo
System.RateCode.InventoryType.NotExist.error=Errore Fatale. Nessuno inventario tipo
System.RateCode.Message.ConfirmDelete.prompt=Eliminare le voci selezionate? Questa azione cancellerà tutti i dati dei codici di velocità selezionata.
System.RateCode.Message.ConfirmDelete2.prompt=Eliminare le voci selezionate? Questa azione cancellerà tutti i dettagli dei livelli di restrizione selezionati.
System.RateCode.Message.NoGroupRateCode.prompt=Nessuno dei codici tariffi di gruppo sono stati configurati
System.RateCode.Message.NoRateCode.message=Nessun livelli di restrizione sono stati configurati
System.RateCode.RateType.NotExist.error=Errore Fatale. Nessun Tariffario Tipo.
System.RateCode.help=Indice Codice di Gestione
System.RateCodeDetails.Update.title=Dettagli Livello di Restrizione Aggiornato
System.RestrictionRemoval.Changed.error=Ci sono modifiche non salvate nella sezione rimozione restrizioni di configurazione. Fare clic su OK se si desidera salvare e poi procedere.
System.RestrictionRemoval.Database.update.error=Errore durante l'aggiornamento limitazioni di rimozione di configurazione
System.RestrictionRemoval.Enable.prompt=Attiva Rimozione Restrizioni di configurazione
System.RestrictionRemoval.RemovalTime.1.prompt=Cancella tutte le restrizioni per i livelli selezionati di restrizione
System.RestrictionRemoval.RestrictionLevels.NoData.prompt=Nessun livelli di limitazione definiti.
System.RestrictionRemoval.RestrictionLevels.Required.Alert=Si prega di modificare uno o più valori prima di fare clic su questo tasto.
System.RestrictionRemoval.RestrictionLevels.Required.error=Uni o più livelli di limitazione devono essere selezionati.
System.RestrictionRemoval.Save.Confirm.Message=Il giorno della configurazione di rimozione delle limitazioni di arrivo è stato conservato.
System.RestrictionRemoval.Time.Required.error=L'ora in cui le limitazioni dovrebbero essere eliminate è richiesto.
System.RestrictionRemoval.title=Giorno di arrivo Restrizioni
System.Role.Database.update.error=Errore dell'aggiornamento del database di ruoli
System.Role.NoData.prompt=Nessun ruolo
System.Role.Privileges.Component.prompt=Componente
System.Role.Privileges.Privilege.prompt=Privilegio
System.Role.Privileges.Write.prompt=Scrive
System.Role.Privileges.prompt=Privilegi componenti
System.Role.help=Configurazione dei ruoli utenti e privilegi.
System.Roles.CSA-Only.desc=Voce ruolo CSA
System.Roles.CSA-Only.name=CSA_Soltanto
System.Roles.GR_PRICING.desc=Gruppo Prezzo utente
System.Roles.Manager.desc=Ruolo del responsabile
System.Roles.Manager.name=Responsabile
System.Roles.Normal-RO.desc=Funzionamento normale solo-lettura
System.Roles.Normal-RO.name=Normale-RO
System.Roles.Normal-WGP.desc=Ruolo speciale per gli utenti normali con prezzi di Gruppo
System.Roles.Normal-WGP.name=Normale-Con Prezzo del gruppo
System.Roles.Normal.desc=Normale ruolo utente
System.Roles.Normal.name=Normale
System.Roles.RateCheckOnly.desc=Controllo Tariffario
System.Roles.RateCheckOnly.name=Soltanto Controllo Tariffa
System.Roles.Support.desc=Ruolo speciale per gruppo di supporto
System.Roles.SysAdmin.desc=Ruolo dell amministratore del sistema
System.Roles.SysAdmin.name=SysAdmin
System.Roles.System-RO.desc=Sistema ruolo di sola lettura
System.Roles.System-RO.name=Sistema-RO
System.RolesAndPrivileges.ui.CanBeCopiedRoleList.NotExist.error=Errore. Nessuno dei ruoli può essere copiato
System.RolesAndPrivileges.ui.Database.delete.error=Eliminare errore ruoli database
System.RolesAndPrivileges.ui.DuplicateRoleName.error=Il ruolo con lo stesso nome già esiste
System.RolesAndPrivileges.ui.MenuMaster.NotExist.error=Errore fatale. Nessun Menù data.
System.RolesAndPrivileges.ui.Property.load.error=Errore caricamento proprietà database
System.RolesAndPrivileges.ui.RoleUser.error=I seguenti ruoli non possono essere eliminati in quanto hanno uno o più utenti ad essi connessi.
System.RolesAndPrivileges.ui.changedrow.error=Questo ruolo è cambiato. Controllare la lista dei ruoli.
System.UploadCSV.Bookeeping.Insert.Message=Estratto ricevuto - Utilizzando caricamento diretto
System.UploadCSV.Overwrite.prompt=Sovrascrittura
System.UploadCSV.PostUpload.BDE.Schedule.Error=Il servizio di fine giornata lavorativa non poteva essere programmato
System.UploadCSV.Run.BDENow.prompt=Esegui Giorno Fine Lavoro Adesso
System.UploadCSV.Success.prompt=Caricamento con successo
System.UploadCSV.UploadDirectory.Missing.error=Posizione caricamento CSV non specificato / non esiste. Contattare amministratore di sistema.
System.UploadCSV.UploadFile.Data.error=Dati errati forniti.
System.UploadCSV.UploadFile.Exists.error=Il file specificato già esiste
System.UploadCSV.UploadFile.Login.error=ID utente non corretto/Combinazione password fornita
System.UploadCSV.UploadFile.Read.error=Il file specificato è mancate o è danneggiato
System.UploadCSV.UploadFile.Required.error=Nessun file specificato
System.UploadCSV.UploadFile.Type.error=Il nome dell'immagine deve essere PMSExtract.zip
System.UploadCSV.UploadFile.WrongType.error=Il tipo di immagine dovrebbe essere soltanto .zip, .tar, .tar. Z, .tar.gz. Z, o .gz
System.UploadCSV.UploadFile.dB.Schedule.Error=La fotografia istantanea non può essere copiata all'assistente.
System.UploadCSV.header.title=Carica File.
System.UploadCSV.prompt=Selezionare un file da caricare
System.User.AssignablePrivilege.prompt=Privilegi assegnabili
System.User.AssignableRole.prompt=Ruoli assegnabili
System.User.AssignedPrivilege.prompt=Privilegi assegnati
System.User.AssignedProperty.prompt=Combinazione assegnata di proprietà-ruolo
System.User.AssignedRole.prompt=Ruoli assegnati
System.User.Authentication.prompt=Modalità di autenticazione
System.User.ConfirmPassword.Mismatch.error=La password non è stata correttamente confermata
System.User.ConfirmPassword.prompt=Confermare password
System.User.ConfirmPassword.required.error=La conferma della password è richiesta
System.User.Database.Duplicate.error=Utente con questo ID esiste già
System.User.Database.load.error=Errore di caricamento datebase di utenti
System.User.Database.update.error=Errore nell'aggiornamento del database utenti
System.User.Description.prompt=Descrizione Utente
System.User.Email.prompt=ID Email
System.User.LastLoginDate.prompt=Ultima data di accesso
System.User.LoginID.prompt=ID Login
System.User.NoData.prompt=Nessun utente
System.User.NoUser.message=Nessun utente è stato configurato
System.User.NonLDAP.MaxInactivityPeriod.Label=Periodo inattivo massimo
System.User.NonLDAP.NeverExpires.Label=Nessuna Scadenza
System.User.NonLDAP.PasswordValidity.Label=Validità password
System.User.NonLDAP.PasswordValidityAlert.Label=Avviso scadenza della prima password
System.User.NonLDAP.PasswordValidityAlertDays.Update.Error=Impossibile aggiornare `Il periodo in giorni dell'avviso per la scadenza della Password?.
System.User.NonLDAPMaxInactivePeriod.Error=Il periodo inattivo massimo (giorni) dovrebbe essere numerico e più maggior di 0.
System.User.NonLDAPPasswordExpiryAlertPeriodInDays.Error=Password validità avviso (giorni) deve essere numerico e maggiore di 0.
System.User.NonLDAPPasswordExpiryPeriodInDays.Error=Validità Password (giorni) deve essere numerica e maggiore di 0.
System.User.Password.ValidCharacters.error=La parola d'accesso deve essere almeno 6 caratteri.
System.User.PasswordExpires.prompt=La password ha scadenza?
System.User.PasswordExpiry.prompt=Quando? (in giorni)
System.User.Property.CannotBeSelected.error=La proprietà non può essere selezionata
System.User.Property.NotAssigned.error=Nessuna proprietà sono stati assegnati a questo utente
System.User.Property.filter.prompt=Proprietà
System.User.Property.prompt=Proprietà-Ruolo
System.User.RateCheckOnly.condition1.error=Utente con accesso a più proprietà deve avere un ruolo diverso da quello di solo controllo in almeno una proprietà. Si prega di assegnare i ruoli appropriati per questo utente.
System.User.Retrieved.prompt=Conto Utente Ricuperato
System.User.Role.NotSelected.error=Selezionare un ruolo
System.User.Roles.required.error=Nessun ruolo è stato assegnato a questo utente
System.User.SecretAnswer.prompt=Risposta Segreta
System.User.SecretQuestion.prompt=Selezionare la Domanda Segreta
System.User.Type.External.prompt=Esterno
System.User.Type.Internal.prompt=Interno
System.User.Type.prompt=Tipo di Utente
System.User.Update.title=Aggiornamento Utente
System.User.UserDescription.required.error=E' richesta la descrizione utente
System.User.UserId.ValidCharacters.error=L'ID utente deve iniziare con un carattere alfanumerico e non deve contenere spazi.
System.User.help=Istruzioni per l'installazione e configurazione.
SystemTools.ScrollBars.ScrollDown.Label=Scorrere Giù
SystemTools.ScrollBars.ScrollUp.Label=Scorrere Su
User.ForgotPassword.Answer.Incorrect=La tua risposta è errata
User.ForgotPassword.Confirm.Email.Required=Si prega di inserire un indirizzo email
User.ForgotPassword.Confirm.Email.validation=Indirizzo email confermato non è valido
User.ForgotPassword.Email.Confirm.Email.Mismatch=Confermare l'indirizzo email non è lo stesso indirizzo email.
User.ForgotPassword.Email.Email.body=Utente {0} ,la tua password è {1}.
User.ForgotPassword.Email.Failed=Errore durante l'invio della password email modificata. Si prega di verificare host SMTP o contattare l'amministratore del sistema.
User.ForgotPassword.Email.Not.Set=Non hai impostato il tuo indirizzo e-mail.
User.ForgotPassword.Email.Required=Si prega di inserire l'indirizzo email.
User.ForgotPassword.Email.Subject=La parola d'accesso è stata cambiata con successo
User.ForgotPassword.Email.validation=Indirizzo e-mail non è valido
User.ForgotPassword.Email.validation.1=Gli indirizzi E-mail non iniziano con punti o segni @
User.ForgotPassword.Email.validation.2=Esso conteneva caratteri non corretti, come ad esempio spazi o virgole.
User.ForgotPassword.EmailSuccess.prompt=Una email è stata inviata all'indirizzo configurato nel sistema di V5i.
User.ForgotPassword.Error.fetching.Data=L'errore si è presentato mentre i dati utenti sono stati inviati dal database.
User.ForgotPassword.Hint.Question=Domanda di Suggerimento
User.ForgotPassword.Internal.User.Cannot.Use.Forgot.Password=Utente interno non può utilizzare la funzionalità password dimenticata.
User.ForgotPassword.LoginLink.prompt=Torna alla schermata Login
User.ForgotPassword.Page.Header=Password Dimenticata
User.ForgotPassword.Preferences.Confirm.email=Conferma l'indirizzo email
User.ForgotPassword.Preferences.Secret.Question=Domanda Segreta
User.ForgotPassword.Preferences.Secret.Question.1=Qual è il tuo numero principale frequent flyer?
User.ForgotPassword.Preferences.Secret.Question.2=Qual è il vostro numero di carta di biblioteca?
User.ForgotPassword.Preferences.Secret.Question.3=Qual è stato il primo numero di telefono?
User.ForgotPassword.Preferences.Secret.Question.4=Qual era il nome del tuo primo insegnante?
User.ForgotPassword.Preferences.Secret.Question.5=Quale è il secondo nome di tuo padre
User.ForgotPassword.Preferences.Secret.Question.Select=Selezionare la domanda segreta.
User.ForgotPassword.Preferences.Securiy=Sicurezza
User.ForgotPassword.Preferences.emailAddress=Inserire l'indirizzo e-mail
User.ForgotPassword.Prompt.Enter.User.ID=Inserire ID Utente
User.ForgotPassword.Prompt.Login=Hai dimenticato la password
User.ForgotPassword.Secret.Question.Answer=Garantire la sicurezza del tuo account, dando risposta alla domanda segreta più di 3 caratteri.
User.ForgotPassword.Secret.Question.Not.Set=Non hai impostato la domanda segreta.
User.ForgotPassword.Secret.Question.Required=Scegliere la domanda segreta.
User.ForgotPassword.SecretQuestion.Answer.Required=Inserire una risposta per la domanda segreta
User.ForgotPassword.Smtp.Host.Name.Description=Nome host SMTP per la funzionalità della password dimenticata.
User.ForgotPassword.Smtp.Not.Configured=Host SMTP non è configurato. Si prega di contattare l'amministratore del sistema.
User.ForgotPassword.Submit.Button=Invia
User.ForgotPassword.User.Not.Present=Si prega di specificare nome utente corretto
errors.footer=<hr></font>
module.AtAGlance.View=Abilitare la visualizzazione  @ Un'Occhiata con il  Calendario Fiscale Flessibile.
module.AtAGlance.View.Mode=Utilizzando il calendario fiscale flessibile
module.BusinessAnalysis.Results.title=Risultati
module.Channel.title=Gestione Canale
module.GroupPricing.GroupPricingResults.prompt=Risultati Prezzo Gruppo
module.Services.title=Servizi
module.SpecialEvents.RetrievedSpecialEvents.title=Recuperato Eventi Speciali
module.System.Service.title=Servizio Sistema
module.System.title=Gestione Sistema
module.footer.lastupdated=Ultimo aggiornamento
module.footer.privacy=Privato
module.footer.title=Data Politica di segretezza
module.form.UpdatePassword.Head=Aggiorna Password
module.menu.PerformanceTrends=Tendenze di prestazione
module.menu.businessforecast=Previsione Business
module.menu.chainleveliprestriction=Criteri di restrizione IP (Livello Catena)
module.menu.contextBar.defview=Definire le viste
module.menu.contextBar.rmcap=Capienza Camera
module.menu.contextBar.selectnewview=Selezionare una nuova visulizzazione o proprietà
module.menu.definevalidips=Definire l'IPS valido (livello Catena)
module.menu.glance=@ Un'occhiata
module.menu.managecontrol=Gestire Controlli
module.menu.monitorperformance=Controllare la prestazione
module.menu.mylinks=Miei Link
module.menu.mylinks.Education.Message=Visita la struttura educativa per una preziosa introduzione online di Amministrazione del reddito.
module.menu.mylinks.Education.Message.Head=IDeaS Hotel Educativo
module.menu.mylinks.ProductInfo.Message.Head=Chiarezza.  Fiducia.  Controllo
module.menu.useraccounts=Conto Utente
module.menu.userdetails=Dettagli Utente
module.menu.welcome=Benvenuto
module.myProductX.New.title=Miei IDeaS
module.myProductX.title=myIDeaS
module.popup.bodytext=Questa dichiarazione riguarda le informazioni personali che è soggetto a dei dati dell'Unione Europea Protezione Direttiva. <p> Come parte del software di ottimizzazione fiscale per i nostri clienti, le decisioni e sistemi integrati, Inc. (IDeS) ricevono dati, che possono includere informazioni personali. Queste informazioni vengono elaborate e riportate per ogni cliente in forma di riepilogo. </p> Noi rispettiamo la privacy degli individui e dei dati dei nostri clienti. E 'contro la nostra politica di vendere, scambiare o altrimenti divulgare dati personali o client. </p> IDeS è un membro del
module.popup.link=Safe Harbour
module.pview.collapse.tooltip=Crollo
module.pview.editview=Modifica una visualizzazione salvata
module.pview.expand.tooltip=Espander
module.pview.loading=Caricamento.
module.pview.newview=NUova Visualizzazione
module.pview.newview.alreadyexists.error=Una visualizzazione con questo nome esiste già. Si prega di selezionare un nome diverso.
module.pview.newview.noCurrency.error=Devi selezionare una valuta da salvare.
module.pview.newview.noname.error=È necessario dare a una nuova visione un nome per salvarlo.
module.pview.newview.nopropertyselected.error=Si prega di selezionare una Proprietà
module.pview.pvinfo=Aggiungere, modificare o eliminare le visualizzazioni di proprietà per la visualizzazione in A colpo d'occhio, Business Analysis, e relazioni consolidate. È necessario disporre delle autorizzazioni per le proprietà.
module.pview.saveview=Salvare questa immagine
module.pview.selall.tooltip=Selezionare tutte le proprietà
module.pview.selnone.tooltip=Deselezionare tutte le proprietà
module.pview.step1.desc=Selezionare fino a 3 livelli di raggruppamento usando le opzioni qui sotto:
module.pview.step1.title=Passo 1: Selezionare tipi di visualizzazione
module.pview.step2.desc=Selezionare le proprietà desiderate di seguito:
module.pview.step2.title=Passo 2: Definire i tipi di immagine
module.pview.title=VIsualizzazione Proprietà Avanzata
myProductX.MSView.AssignableMarket.prompt=Settori di mercato assegnabili
myProductX.MSView.AssignedMarket.prompt=Settori di mercato assegnati
myProductX.MSView.Configure.NoMSView.prompt=Nessuna vista del segmento di mercato è stata configurata
myProductX.MSView.Database.Duplicate.error=La vista del segmento di mercato con lo stesso nome esiste
myProductX.MSView.Database.delete.error=Errore durante l'aggiornamento della visualizzazione del  segmento di mercato
myProductX.MSView.Database.load.error=Errore durante il caricamento della visualizzazione del segmento di mercato
myProductX.MSView.Database.update.changedrow.error=Questo Segmento immagine di mercato è cambiato. Controllare l'elenco dei Settori immagine di mercato.
myProductX.MSView.Database.update.error=Errore durante l'aggiornamento della visualizzazione del segmento di mercato
myProductX.MSView.MSConsolidated.NotAssigned.error=Almeno un segmento di mercato deve aggiungersi
myProductX.MSView.MSConsolidated.prompt=Settori di mercato consolidati
myProductX.MSView.MarketSegments.AlreadyConsolidated.error=I seguenti segmenti di mercato - {0} già sono stati consolidati nell'ambito di un'altra visualizzazione.
myProductX.MSView.MarketSegments.NotExist.error=I dati di settori di mercato non sono popolati
myProductX.MSView.MarketSegments.NotSelected.error=Selezionare almeno un segmento di mercato
myProductX.MSView.MarketSegments.Unconsolidated.NotExist.error=Tutti i Settori di mercato sono stati consolidati.
myProductX.MSView.Name.prompt=Vista del segmento di mercato
myProductX.MSView.Name.required.error=Il nome di visualizzazione è richiesto
myProductX.MSView.New.title=Vista del mercato dell'utente
myProductX.MSView.NoData.prompt=Nessuna delle visualizzazioni segmento di mercato definite
myProductX.Password.Change.Success.prompt=La password è stata cambiata con successo
myProductX.Password.ConfirmPassword.prompt=Confermare Nuova Password
myProductX.Password.NewPassword.prompt=Nuova Password
myProductX.Password.OldPassword.prompt=Vecchia Password
myProductX.Password.Update.Success.prompt=Password è stata aggiornata con successo
myProductX.Password.help=Cambia Password
myProductX.Preferences.Database.update.error=Impossible aggiornare I preferiti
myProductX.Preferences.DateFormat.prompt=Formato Data
myProductX.Preferences.StartPage.prompt=Inizio Pagina
myProductX.Preferences.Update.Success.prompt=Preferenze aggiornate con successo.
myProductX.Preferences.help=Preferenze utente
myProductX.Preferences.section.title=Aggiornamento preferenze utente
myProductX.Preferences.title=Preferiti
myProductX.Property.Controls.SetDefault.prompt=Imposta come predefinito
myProductX.Property.Controls.Switch.prompt=Cambia
myProductX.Property.SingleProperty.error=Avete accesso soltanto ad una proprietà
myProductX.Property.help=Cambia Proprietà
myProductX.Property.title=Cambia proprietà
myProductX.UpdatePassword.Database.update.error=Impossibile aggiornare la password utente.
myProductX.UpdatePassword.ReadOnly=Il sistema è nella modalità di sola lettura
myProductX.UpdatePassword.Validation.error.confirmNewPassword.required=Confermare pure la nuova password
myProductX.UpdatePassword.Validation.error.newPassword.required=La nuova password deve essere specificata
myProductX.UpdatePassword.Validation.error.newPasswordMismatch=Conferma nuova password correttamente
myProductX.UpdatePassword.Validation.error.oldNewSamePassword=La nuova password deve essere diversa dalla vecchia password
myProductX.UpdatePassword.Validation.error.oldPassword.required=La vecchia password deve essere specificata
myProductX.UpdatePassword.Validation.error.passwordMismatch=La vecchia password non è corretta
myProductX.UserPrefLinks.Add.Header=Aggiungere Link
myProductX.UserPrefLinks.Database.delete.error=Il mio database Link  eliminare errore.
myProductX.UserPrefLinks.Database.load.error=Errore caricando I Mie Link informazioni.
myProductX.UserPrefLinks.Database.update.duplicaterow.error=Nome Display con lo stesso nome esiste già.
myProductX.UserPrefLinks.Database.update.error=Incapace di aggiornare i miei collegamenti
myProductX.UserPrefLinks.DisplayName.message=Questo è il nome che apparirà nel menu tendina My Links.
myProductX.UserPrefLinks.DisplayName.prompt=Nome Display
myProductX.UserPrefLinks.DisplayName.required.error=Si prega di inserire un valido Nome Display
myProductX.UserPrefLinks.Edit.Header=Modifica Link
myProductX.UserPrefLinks.MaxChars.message=(caratteri massimo 40)
myProductX.UserPrefLinks.Nolinks.message=Attualmente, non hai links. Aggiungi i tuoi link preferiti a questa zona di facile accesso. Fare clic su "Gestione Links" qui sotto.
myProductX.UserPrefLinks.NolinksSetup.message=Nessuno dei Miei Links sono stati configurati.
myProductX.UserPrefLinks.URL.message=Utilizzare l'URL completo del sito web (a partire da http://www.). Questo URL non apparirà nel menu a discesa I miei Link..
myProductX.UserPrefLinks.URL.prompt=URL
myProductX.UserPrefLinks.URL.required.error=Si prega di inserire un URL valido.
warnings.SaveConfirm.prompt=Ci sono avertimenti nel modulo. Fare clic su OK per salvare. Fare clic su Annulla per tornare al modulo.
warnings.header=Avvisi
RMS.Commons.Key.RoomsSold=Camere Vendute
RMS.Commons.Key.UpdatedOn=Aggiornato il
RMS.Commons.Key.UpdatedBy=Caricato da
RMS.Commons.Key.StartDate=Inizio Data
RMS.Commons.Key.EndDate=Data di Scadenza
RMS.Commons.Key.Rooms=Camere
RMS.Commons.Key.AverageDailyRate=ADR
RMS.Commons.Key.CreatedOn=Creato il
RMS.Commons.Key.CreatedBy=Creato da
RMS.Commons.Key.RevPAR=RevPAR
RMS.Commons.Key.MarketSegment=Segmento di mercato
RMS.Commons.Key.Notes=Note
RMS.Commons.Key.OccupancyDate=Data di occupazione
RMS.Commons.Key.Overbooking=Sovraprenotazione
RMS.Commons.Key.OccupancyForecast=Occupazione Prevista
RMS.Commons.Key.MarketSegmentGroup=Gruppo Segmento di Mercato
RMS.Commons.Key.PropertyName=Nome Proprietà
RMS.Commons.Key.Total=Totale
RMS.Commons.Key.Status=Stato
RMS.Commons.Key.DayOfWeek=DOW
RMS.Commons.Key.BusinessType=Tipo d'affari
RMS.Commons.Key.LastRoomValue=Valore Ultima Camera
RMS.Commons.Key.RoomRevenue=Reddito Camera
RMS.Commons.Key.Yes=Si
RMS.Commons.Key.Cancel=Annulla
RMS.Commons.Key.No=No
RMS.Commons.Key.HotelCapacity=Capienza Hotel
RMS.Commons.Key.HotelName=Nome Albergo
RMS.Commons.Key.Value=Valore
RMS.Commons.Key.NotAvailable=NA
RMS.Commons.Key.Group=Gruppo
RMS.Commons.Key.Transient=Transitorio
RMS.Commons.Key.Date=Data
RMS.Commons.Key.Forecast= Previsione
RMS.Commons.Key.All=Tutti
RMS.Commons.Key.Active=Attivo
RMS.Commons.Key.Percentage=%
RMS.Commons.Key.SpecialEvents=Eventi Speciale
RMS.Commons.Key.Property=Proprietà
RMS.Commons.Key.Hotel=Hotel
RMS.Commons.Key.Description=Descrizione
RMS.Commons.Key.Done=Eseguito
RMS.Commons.Key.System=Sistema
RMS.Commons.Key.Day=Giorno
RMS.Commons.Key.User=Utente
RMS.Commons.Key.OccupancyForecastPercentage=Occupazione Prevista%
RMS.Commons.Key.Week=Settimana
RMS.Commons.Key.RoomsSoldLastYear=Camere Vendute lo Scorso Anno
RMS.Commons.Key.LastYear=Scorso Anno
RMS.Commons.Key.RoomsSoldPercentage=Camere Vendute %
RMS.Commons.Key.Delete=Annulla
RMS.Commons.Key.Tuesday=Mar
RMS.Commons.Key.None=Nulla
RMS.Commons.Key.SpecialEvent=Evento Speciale
RMS.Commons.Key.ArrivalDate=Data di Arrivo
RMS.Commons.Key.Next=Avanti
RMS.Commons.Key.ADRLastYear=ADR Scorso Anno
RMS.Commons.Key.MSGroupName=Nome Gruppo MS
RMS.Commons.Key.SelectDataElementsToInclude=Selezionare gli elementi da includere:
RMS.Commons.Key.NotesWithColon=Note:
RMS.Commons.Key.Friday=Ven
RMS.Commons.Key.To=a
RMS.Commons.Key.Wednesday=Mer
RMS.Commons.Key.Thursday=Gio
RMS.Commons.Key.Monday=Lun
RMS.Commons.Key.OfEveryMonth=di ogni mese
RMS.Commons.Key.Inactive=Inattivo
RMS.Commons.Key.Sunday=Dom
RMS.Commons.Key.GroupName=Nome Gruppo
RMS.Commons.Key.Capacity=Capienza
RMS.Commons.Key.Saturday=Sab
RMS.Commons.Key.Details=Dettagli
RMS.Commons.Key.Add=Aggiungi
RMS.Commons.Key.AlertType=Tipo Avviso
RMS.Commons.Key.AlertCondition=Condizione Avviso
RMS.Commons.Key.Summary=Sintesi
RMS.Commons.Key.ChangeOneOrMoreValues=Si prega di modificare uno o più valori prima di fare clic su questo pulsante.
RMS.Commons.Key.BARByDay=BAR per Giorno
RMS.Commons.Key.Previous=Precedente
RMS.Commons.Key.OOO=OOO
RMS.Commons.Key.RoomRevenueLastYear=Reddito Camera Scorso Anno
RMS.Commons.Key.PercentageRoomsSold=% Camere Vendute
RMS.Commons.Key.Current=Corrente
RMS.Commons.Key.DataSelectionColon=Selezione Dati:
RMS.Commons.Key.BestAvailableRate=BAR
RMS.Commons.Key.Name=Nome
RMS.Commons.Key.RestrictionLevel=Livello di Restrizione
RMS.Commons.Key.Monthly=Mensile
RMS.Commons.Key.Daily=Giornaliero
RMS.Commons.Key.Reset=Riavvia
RMS.Commons.Key.MarketSegments=Settori di mercato
RMS.Commons.Key.OutOfOrderRooms=Camere Fuori Servizio
RMS.Commons.Key.UnableToRetrieveData=Impossibile recuperare i dati. Riprova. Se l'errore persiste, si prega di contattare il rappresentante dell'assistenza.
RMS.Commons.Key.BARRatePlan=Progetti Tariffe BAR
RMS.Commons.Key.BARRates=Tariffe BAR
RMS.Commons.Key.Rate=Tariffa
RMS.Commons.Key.Category=Categoria
RMS.Commons.Key.DataNotAvailableBefore0=Dati non disponibili prima {0}.
RMS.Commons.Key.Month=Mese
RMS.Commons.Key.Change=Modifica
RMS.Commons.Key.EndDateWithColon=Fine Data:
RMS.Commons.Key.OnBooks=Su Prenotazione
RMS.Commons.Key.StartDateWithColon=Data Inizio:
RMS.Commons.Key.ValuesBeingEdited=Uno o più valori sono in corso di modifica. Fare clic su OK se si desidera salvare e poi procedere.
RMS.Commons.Key.BudgetedADR=Preventivo ADR
RMS.Commons.Key.ThisYear=Quest'anno
RMS.Commons.Key.BudgetedRoomsSold=Preventivo Camere Vendute
RMS.Commons.Key.DataNotAvailableBeyond0=Dati non disponibili dopo {0}.
RMS.Commons.Key.RequestProcessingErrorOccurred=Errore durante l'elaborazione di questa richiesta. Prova di nuovo. Se il problema persiste contattare IDeaS
RMS.Commons.Key.RestrictionsReport=Rapporto di limitazioni
RMS.Commons.Key.WebBARForAllLOS=Web BAR per tutti LOS
RMS.Commons.Key.WebBARByDay=Giorno da Web BAR
RMS.Commons.Key.EsitmatedRoomRevenue=Reddito valutato della camera
RMS.Commons.Key.RoomsSoldAndOccupancyForecastByBusinessTypeReport=Camere Vendute ed occupazione Prevista dal Rapporto Tipo di Affari
RMS.Commons.Key.SystemBARByDay=BAR sistema da Giorno
RMS.Commons.Key.Revenue=Reddito
RMS.Commons.Key.UserOverrideBARForAllLOS=BAR Sovrascrittura Utente per tutti LOS
RMS.Commons.Key.DayOfArrival=Data di Arrivo
RMS.Commons.Key.UserBARByDay=Utente BAR da Data
RMS.Commons.Key.SystemBARForAllLOS=Sistema BAR per tutti LOS
RMS.Commons.Key.MSGroups=Gruppi del MS
RMS.Commons.Key.FunctionAllowsDataExtraction=Questa funzione permette che i dati siano estratti dal database per uso nei fogli elettronici, nel database locale, ecc.
RMS.Commons.Key.End=Fine
RMS.Commons.Key.PerformanceComparisonReport=Rapporto Confronto Prestazione
RMS.Commons.Key.UserOverrideBARByDay=BAR Sovrascrittura Utente dal Giorno
RMS.Commons.Key.AvailableCapacity=Capienza disponibile
RMS.Commons.Key.Start=Inizia
RMS.Commons.Key.UserBARForAllLOS=BAR Utente per tutti i LOS
RMS.Commons.Key.Alerts=Avvisi
RMS.Commons.Key.Type=Tipo
RMS.Commons.Key.Close=Chiudere
RMS.Commons.Key.Apply=Applicare
RMS.Commons.Key.Error=Errore
RMS.Commons.Key.AtLeastOneEntryMust=Almeno una voce deve essere selezionata.
RMS.Commons.Key.HistoryOfNotes=Cronologia degli Appunti
RMS.Commons.Key.UserID=ID Utente
RMS.Commons.Key.UserName=Nome Utente
RMS.Commons.Key.PositiveRateValue=Valore della frequenza deve essere un numero positivo (non-zero). Si prega di inserire solo numeri senza virgole.
RMS.Commons.Key.Weekly=Settimanale
RMS.Commons.Key.Days=giorni
RMS.Commons.Key.MarketSegmentGroups=Gruppi Segmento di Mercato
RMS.Commons.Key.WrongEndDateFormat=Formato della data di scadenza è sbagliato
RMS.Commons.Key.Setup=Installazione
RMS.Commons.Key.From=Da
RMS.Commons.Key.DateRange=Campo Dati
RMS.Commons.Key.Ok=Ok
RMS.Commons.Key.WrongStartDateFormat=Formato Giorno d'inizio è sbagliato
RMS.Commons.Key.SystemBAR=BAR Sistema
RMS.Commons.Key.RoomType=Tipologia Camera
RMS.Commons.Key.EndDateRequired=La data di scadenza è richiesta
RMS.Commons.Key.Last=ultimo
RMS.Commons.Key.Fourth=quarto
RMS.Commons.Key.Third=terzo
RMS.Commons.Key.Second=secondo
RMS.Commons.Key.First=primo
RMS.Commons.Key.RestrictionLevels=Livelli di limitazione
RMS.Commons.Key.UnsavedInformationWarning=Ci sono informazioni non salvate. Volete salvare le modifiche? Fare clic su OK per salvare.
RMS.Commons.Key.Revert=Reverso
RMS.Commons.Key.WebRateData=Tariffe Dati Web
RMS.Commons.Key.UserOverride=Sovrascrivere Utente
RMS.Commons.Key.Rank=Classifica
RMS.Commons.Key.BARForAllLOS=BAR per tutti LOS
RMS.Commons.Key.Web=Web
RMS.Commons.Key.BudgetedRoomRevenue=Reddito Preventivo Camera
RMS.Commons.Key.Save=Salvato
RMS.Commons.Key.ForecastOverride=Sovrascrivere Previsione
RMS.Commons.Key.SystemOverride=Sovrascrittura Sistema
RMS.Commons.Key.Actual=Corrente
RMS.Commons.Key.DisplacedRevenue=Guadagni Mostrati
RMS.Commons.Key.TotalRoomNights=Notti Camere Totali
RMS.Commons.Key.Reverted=Indietro
RMS.Commons.Key.Other=Altro
RMS.Commons.Key.StartDateBeforeEarliestForecastedDateDefaultingTo1=Data di inizio è prima della Data prevista, {0}. Data di inizio predefinita a {1}.
RMS.Commons.Key.MinimumLOSReport=Rapporto minimo di LOS
RMS.Commons.Key.RoomsSoldThisYear=Le Camere Vendute Quest' Anno
RMS.Commons.Key.OverbookingReport=Rapporto Overbooking
RMS.Commons.Key.ConsolidatedAlertHighlighterReport=Rapporto Avviso Consolidato Evidenziato
RMS.Commons.Key.ReportType=Tipo Rapporto:
RMS.Commons.Key.UserBAR=Utente BAR
RMS.Commons.Key.CostOfWalk=Costo Trasferta
RMS.Commons.Key.FPLOSReport=Rapporto di FPLOS
RMS.Commons.Key.HotelPerformanceByLevelOfRoomsSoldReport=La prestazione dell'hotel dal rapporto di livello di camaere vendute.
RMS.Commons.Key.StartDateBeforeEarlisetForecastedDateDefaultingTo0=La data di inizio è priva della data prevista, {0}. Predefinire la data di inizio a {0}.
RMS.Commons.Key.PercentageRoomsSoldByDateWithRevPARAndADRReport=Percentuale camere vendute entro Date con RevPAR ed il rapporto ADR
RMS.Commons.Key.BookingPaceReport=Rapporto Andamento Prenotazione
RMS.Commons.Key.RoomsSoldADRAndRevPARToDateReport=Camere vendute, ADR e RevPAR a Rapporto Data
RMS.Commons.Key.RoomsSoldADRAndRevenueByDateAndSegmentationReport=Camere Vendute, ADR e Reddito da Date e dal rapporto di Segmentazione
RMS.Commons.Key.DistributionOfRoomsSoldByDayOfWeekReport=Distribuzione delle Camere Vendute Dal Rapporto entro il Giorno-della-Settimana
RMS.Commons.Key.RevPARLastYear=RevPAR Scorso Anno
RMS.Commons.Key.LastRoomValueReport=Ultimo Rapporto Valore Camera
RMS.Commons.Key.DifferentialLastRoomValueReport=Differenziale Ultimo Rapporto Valore Camera
RMS.Commons.Key.SelectDateWithFirstDayOfMonth=Selezionare una data che comincia con il primo giorno del mese.
RMS.Commons.Key.AtLeastOneSelectionMust=Almeno un elemento deve essere selezionato.
RMS.Commons.Key.DifferentialOverbookingReport=Rapporto Overbooking differenziale
RMS.Commons.Key.LastYearSoldsToDate=Date Vendute Lo Scorso Anno
RMS.Commons.Key.DifferentialRestrictionsReport=Rapporto Restrizioni Differenziali
RMS.Commons.Key.ChannelRestrictionsReport=Rapporto Restrizioni Canale
RMS.Commons.Key.DaysToArrival=Giorni all' Arrivo
RMS.Commons.Key.PercentageForecastVariance=% Previsone Varianza
RMS.Commons.Key.ConsolidatedOccupancyHighlighterReport=Rapporto Occupazione Evidenziato Consolidato
RMS.Commons.Key.ClusterYieldingReport=Rapporto Gruppo Guadagno
RMS.Commons.Key.CompleteMonthMustAsDateRange=L'intervallo data dovrebbe essere un mese completo
RMS.Commons.Key.Country=Paese
RMS.Commons.Key.EveryMonthOnDay=Ogni mese nel giorno
RMS.Commons.Key.RatePlans=Piano Tariffario
RMS.Commons.Key.GeneralInformation=Informazione Generale
RMS.Commons.Key.ServiceFailureNotice=Servizio Avviso Guasto
RMS.Commons.Key.IDeaSLearningSystem=IDeaS Learning System
RMS.Commons.Key.PropertyConfiguration=Configurazione Proprietà
RMS.Commons.Key.Roles=Ruoli
RMS.Commons.Key.AtLeastOnePropertySelectionMust=Selezionare almeno una proprietà.
RMS.Commons.Key.StartDateGreaterThanEqualToSystemDate=La data di inizio deve essere superiore o uguale alla data del sistema.
RMS.Commons.Key.Schedule=Programmato
RMS.Commons.Key.Retrieve=Recuperare
RMS.Commons.Key.GroupWashByGroup=Gruppo In Attesa Da Gruppo
RMS.Commons.Key.ConfirmDeletionOfSelectedEntries=Cancellare voci selezionate?
RMS.Commons.Key.RoomTypeCode=Tipo Codice Camera
RMS.Commons.Key.Support=Supporto
RMS.Commons.Key.NumberOfRooms=Numero di Camere
RMS.Commons.Key.GroupPricing=Prezzo Gruppo
RMS.Commons.Key.StartDateRequired=E' richiesta la data d'inizio
RMS.Commons.Key.Compute=Calcolo
RMS.Commons.Key.CompitiveSet=Competitivo Set
RMS.Commons.Key.LastUpdatedBy=Ultimo Aggiornamento Da
RMS.Commons.Key.HotelForecastOverride=Sovrascrittura Previsone Hotel
RMS.Commons.Key.EndDateAfterStartDateMust=Data di scadenza deve essere successiva alla data di inizio
RMS.Commons.Key.ForecastActivity=Previsione Attività
RMS.Commons.Key.City=Città
RMS.Commons.Key.Remarks=Commenti
RMS.Commons.Key.GroupRates=Tariffe Gruppo
RMS.Commons.Key.Select=Seleziona
RMS.Commons.Key.BusinessAnalysis=Business Analysis
RMS.Commons.Key.Year=Anno
RMS.Commons.Key.Password=Password
RMS.Commons.Key.Edit=Modifica
RMS.Commons.Key.SystemTools=Sistema Strumenti
RMS.Commons.Key.Assign=Assegna
RMS.Commons.Key.Errors=Errori
RMS.Commons.Key.RateCodesDatabaseError=Errore database Codici Tariffari
RMS.Commons.Key.NumberOfOccurences=Numero degli avvenimenti
RMS.Commons.Key.ThursdayFullText=Giovedì
RMS.Commons.Key.FridayFullText=Venerdì
RMS.Commons.Key.WednesdayFullText=Mercoledì
RMS.Commons.Key.ActualCondition=Condizioni Effettive
RMS.Commons.Key.AlertCriteria=Criteri di Avvisi
RMS.Commons.Key.MondayFullText=Lunedì
RMS.Commons.Key.SaturdayFullText=Sabato
RMS.Commons.Key.New=Nuovo
RMS.Commons.Key.AlertSubLevel=Avviso Sottolivello
RMS.Commons.Key.AlertLevel=Avviso Livello
RMS.Commons.Key.SundayFullText=Domenica
RMS.Commons.Key.Suspended=Sospeso
RMS.Commons.Key.TusedayFullText=Martedì
RMS.Commons.Key.OccupancyForecastLessThan=Occupazione: previsione meno di
RMS.Commons.Key.Available=Disponibile
RMS.Commons.Key.OccupancyForecastGreaterThan=L'occupazione è maggiore di
RMS.Commons.Key.Channel=Canale
RMS.Commons.Key.OutOfOrder=Fuori Uso
RMS.Commons.Key.BudgetedRooms=Reddito Preventivo Camera
RMS.Commons.Key.SubTotal=Totale parziale
RMS.Commons.Key.HotelPerformanceAtAGlance=Prestazione dell'hotel ad occhio
RMS.Commons.Key.Occupancy=Occupazione
RMS.Commons.Key.CopyToRest=Copia a Rest
RMS.Commons.Key.BudgetedRevPAR=Preventivo RevPAR
RMS.Commons.Key.Expected=Previsiti
RMS.Commons.Key.EstimatedRevPAR=RevPAr Stimato
RMS.Commons.Key.EstimatedADR=ADR Stimato
RMS.Commons.Key.GroupRevenueTotal=Totale Reddito Gruppo
RMS.Commons.Key.ConferenceAndBanquetRevenueStreamInput=Immissione Flusso Entrate Reddito Conferenze e Banchetti
RMS.Commons.Key.TotalNetRevenueForGroup=Totale Reddito Netto per Gruppo
RMS.Commons.Key.ProfitPerRoomNight=Reddito per Camera a Notte
RMS.Commons.Key.ProfitMarginPercentage=Margine Profitto%
RMS.Commons.Key.Average=Media
RMS.Commons.Key.RevenuePerRoomNight=Reddito per Notte Camera
RMS.Commons.Key.EvaluatedOn=Valutato il
RMS.Commons.Key.RoomNights=Notti Camere
RMS.Commons.Key.TotalDisplacementCost=Costo Totale Trasferimento
RMS.Commons.Key.RateName=Nome Tariffa
RMS.Commons.Key.EvaluationResults=Risultati Valutazione
RMS.Commons.Key.DisplacedRooms=Trasferimento Camere
RMS.Commons.Key.SalesPerson=Addetto alle Vendite
RMS.Commons.Key.DiscountedRoomNights=Notti Camere Scontate
RMS.Commons.Key.TotalHotel=Hotel Totale
RMS.Commons.Key.Override=Sovrascrittura
RMS.Commons.Key.OverbookingType=Tipo Overbooking
RMS.Commons.Key.OverbookingLimit=Limite Overbooking
RMS.Commons.Key.HotelTotal=Totale Hotel
RMS.Commons.Key.ComparisonPeriod=Periodo di confronto
RMS.Commons.Key.Reports=Rapporti
RMS.Commons.Key.EndDateAfterSystemTodayDefaultingTo0=La Data di Scadenza dopo il sistema oggi, {0}.  Data di Scadenza da predefinire {0}.
RMS.Commons.Key.DenotionOfOverridesMessage=(*) denota la presenza di una previsione di occupazione sovrascritta
RMS.Commons.Key.ProduceTableWithMinLOSRestriction=Produrre una tabella con le restrizioni di MinLOS.
RMS.Commons.Key.ProduceTableOfMinLOSRestrictionsForDistributionChannels=Produrre una tabella delle limitazioni di MinLOS per canale di distribuzione.
RMS.Commons.Key.GroupEvaluationReport=Rapporto di valutazione del gruppo
RMS.Commons.Key.PercentageRevenueVariance=% Variazione del reddito
RMS.Commons.Key.PercentageVariance=% Variazione
RMS.Commons.Key.ScheduledReports=Rapporti Programmati
RMS.Commons.Key.ReviewPreDefinedReportsForHealthOfBusiness=Potete approfondire nella condizione del commercio all'hotel esaminando questi rapporti predefiniti.
RMS.Commons.Key.MultiDayBARPaceReport=Migliore rapporto di andamento delle tariffe disponibili Multi-Giorno
RMS.Commons.Key.LengthOfStayWithColon=LOS:
RMS.Commons.Key.LastUsed=Usato per ultimo
RMS.Commons.Key.UserBAROverrideLOS1=Sovrascrittura BAR Utente - LOS1
RMS.Commons.Key.UserBAROverrideLOS2=Sovrascrittura BAR Utente - LOS2
RMS.Commons.Key.EndDateAfterlastForecastedDateDefaultingTo1=La Data di Scadenza è dopo la data prevista, {0}.  Predefinire la Data di Scadenza a {1}.
RMS.Commons.Key.ProduceTableForMultiPropertyAlerts=Produrre una tabella degli avvisi per le proprietà multiple.
RMS.Commons.Key.BestAvailableRateReport=Miglior rapporto tariffa disponibile
RMS.Commons.Key.ProduceTableOfLOSRestrictionsByRestrictionLevels=Produrre una tabella del livello di limitazione Lunghezza-del-Soggiorno (con informazioni in materia di gestione supplementare del reddito).
RMS.Commons.Key.HotelSummarized=Hotel ricapitolato
RMS.Commons.Key.ProduceTableGraphOfDistributionOfRoomsSoldOverOneMonth=Produrre una tabella e un grafico illustrando la distribuzione delle camere vendute oltre i blocchi da un mese.
RMS.Commons.Key.ProduceTableOfChangedLRVs=Produrre una tabella degli ultimi valori variabili della camere.
RMS.Commons.Key.ProduceTableOfChangedLOSRestrictionsByRestrictionLevels=Produrre una tabella di modifica lunghezza-del soggiorno illimitato per restrizione livelli (con informazioni aggiuntive gestione dei ricavi).
RMS.Commons.Key.ProduceTableOfOccupanciesMultiProperty=Produrre una tabella occupazioni per le proprietà multiple.
RMS.Commons.Key.ProduceTableOfClusteredHotelsWithOrder=Produrre una tabella degli hotel raggruppati e dell'ordine in cui trasmettere le loro prenotazioni.
RMS.Commons.Key.ProduceTableWithFPLOSRestrictions=Produrre una tabella con il Modello Restrizione Completo della Lunghezza-del-Soggiorno (FPLOS).
RMS.Commons.Key.ProduceTableOfFullOrDifferentialLRV=Produrre una tabella completa o gli ultimi valori differenziali della camera.
RMS.Commons.Key.ProduceTableWithChangeInNumberOfOverbookedRooms=Produrre una tabella con i cambiamenti in numero delle camere sovraprenotate.
RMS.Commons.Key.ProduceTableOfFullOrDifferentialOverbookedRooms=Produrre una tabella di completo o differenziale numero di camere sovrapprenotate
RMS.Commons.Key.SpecialEventName=Nome Evento Speciale
RMS.Commons.Key.ShowRateDetailWithColon=Mostrare Dettagli Tariffa:
RMS.Commons.Key.ADROnBooksThisYear=Prenotazioni ADR Quest' Anno
RMS.Commons.Key.ProduceTableGraphOfDistributionsByMSorMSG=Produrre una tabella e un grafico illustrando la distribuzione di camere vendute, ADR o Reddito Camere dal segmento di mercato o Gruppo Segmento di Mercato.
RMS.Commons.Key.ProduceTableGraphOfRoomSoldADRAndRevPAR=Produrre una tabella e rappresentare graficamente l'illustrazione delle camere vendute,  ADR e RevPAR.
RMS.Commons.Key.BusinessDetailsReport=Rapporto Dettagli Affari
RMS.Commons.Key.CategoryWithColon=Categoria:
RMS.Commons.Key.DataExtractionProgram=Programma Estrazione Dati
RMS.Commons.Key.EndDateAfterLastForecastedDateDefaultingTo0=La data di scadenza ha luogo dopo l'ultima data Prevista, {0}. Predefinire Data di Scadenza a {0}.
RMS.Commons.Key.DataSelection=Selezione Data
RMS.Commons.Key.MaxDaysToArrivalWithColon=Giorni Massimi all'Arrivo:
RMS.Commons.Key.WebBAR=Web BAR
RMS.Commons.Key.TotalRevenue=Reddito totale
RMS.Commons.Key.RoomsNotAvailableOOO=Camera N/A - Fuori Uso
RMS.Commons.Key.SpecialEventInEffect=L'evento speciale in effetti è per questa data.
RMS.Commons.Key.Detail=Dettaglio
RMS.Commons.Key.AtLeastOneDataElementSelectionMust=Almeno un elemento dati deve essere selezionato
RMS.Commons.Key.ProduceTableGraphOfBookingPaceOfRoomSoldForADate=Produrre una tabella e un grafico illustrando l'andamento di prenotazione delle camere vendute per una data.
RMS.Commons.Key.ProcessStatusReport=Rapporto Stato Processo
RMS.Commons.Key.ProduceTableGraphPercentRoomsSoldRevPARAndADR=Produrre una tabella e un grafico illustrando le percentuali delle camere vendute entro la data con RevPAR ed il ADR.
RMS.Commons.Key.ProduceTableGraphHotelPerformanceByLevelOfRoomsSold=Produrre una tabella e un grafico illustrando la prestazione dell'hotel dal Livello delle Camere Vendute.
RMS.Commons.Key.TableGraphOfDistributionOfRoomSoldAndOccForecastByBusinessType=Produrre una tabella e un grafico illustrando la distribuzione delle camere vendute e la previsione di occupazione del tipo di affari.
RMS.Commons.Key.PaceDays=Andamento Giorni
RMS.Commons.Key.AnalysisPeriod=Periodo di analisi
RMS.Commons.Key.SavedReports=Rapporti Salvati
RMS.Commons.Key.Wash=Attesa
RMS.Commons.Key.ForecastPerformance=Prestazione Previsione
RMS.Commons.Key.DelphiGroupPricing=Delphi Group Pricing
RMS.Commons.Key.ParameterName=Parametro Nome
RMS.Commons.Key.Everyday=giornaliero
RMS.Commons.Key.RetrievedAlerts=Avvisi Estratti
RMS.Commons.Key.VisitIDeaS=Vai alla IDeaS.com per saperne di più sui nostri clienti, le nostre soluzioni e la nostra azienda
RMS.Commons.Key.MarketSegmentViews=Panoramica dei Settori di Mercato
RMS.Commons.Key.UploadSnapshotFile=Carica File Immagine
RMS.Commons.Key.UserMarketSegmentViews=Panoramica Utente Segmento del Mercato
RMS.Commons.Key.AllProperties=Tutte le Proprietà
RMS.Commons.Key.AtLeastOneIPRestrictedMode=L''accesso da almeno un IP deve essere somministrato in modalità limitata.
RMS.Commons.Key.NoShowMaintenance=Manutenzione No Show
RMS.Commons.Key.Competitors=Concorrenti
RMS.Commons.Key.DeplhiGroupPricingResults=Risultati Prezzo Gruppo Delphi
RMS.Commons.Key.PreviousEntryDataGapInfo=Info: Data vuota con voce precedente
RMS.Commons.Key.RatePlansForRateCodeWithColon=Piano Tariffario per Codice Tariffario:
RMS.Commons.Key.Read=Leggere
RMS.Commons.Key.Failure=Guasto
RMS.Commons.Key.ThreeLetterAbbreviationForWeekdays=Abbreviazione della tre-lettera per i giorni della settimana
RMS.Commons.Key.TypeOfRoom=Tipo di Camera
RMS.Commons.Key.DtdLocationNotFound=Posizione DTD non trovata.
RMS.Commons.Key.BusinessOverview=Descrizione Affari
RMS.Commons.Key.ErrorLoadingMarketSegments=Errore caricamento segmento mercato
RMS.Commons.Key.AccessForbidden=Accesso Negato
RMS.Commons.Key.ReadOnly=Sola-lettura
RMS.Commons.Key.Processing=Elaborazione
RMS.Commons.Key.DayMustInRangeOneAndThirtyOne=Il giorno deve essere fra l'intervallo 1-31
RMS.Commons.Key.Answer=Risposta
RMS.Commons.Key.State=Stato
RMS.Commons.Key.Service=Servizio
RMS.Commons.Key.NoScheduledService=Nessun servizio è stato programmato
RMS.Commons.Key.NoDataAvailable=Nessuna data disponibile
RMS.Commons.Key.Success=Successo
RMS.Commons.Key.FormatPreferences=Formato Preferito
RMS.Commons.Key.Copy=Copia
RMS.Commons.Key.SessionExpired=La sessione è scaduta.
RMS.Commons.Key.RoomsPrecentageToQualifyBusyNight=Percentuale di camere necessarie a qualificarsi come una notte occupata
RMS.Commons.Key.ParameterValue=Valore di parametro
RMS.Commons.Key.InsertOnBookkeepingFailed=Operazione di inserimento sulla tabella di contabilità non riuscita durante l'aggiornamento Estrai file di stato ricevuto.
RMS.Commons.Key.QualifiedOrUnqualified=Qualificato/incompetente
RMS.Commons.Key.ManageMyLinks=Gestisce I miei Link
RMS.Commons.Key.VisitIDeaSResourcesSupport=Visita il  Portale IDeaS Community per il supporto del prodotto, le risorse o per contattare un rappresentante di supporto.
RMS.Commons.Key.DataAsOfWithColon=Data come il :
RMS.Commons.Key.Login=Login
RMS.Commons.Key.Update=Aggiorna
RMS.Commons.Key.GoToOnlineTrainingProgram=Andare al programma di formazione online a sviluppare e affinare le vostre abilità V5i.
RMS.Commons.Key.NoRateCodesDefined=Nessuno dei codici tariffari definiti
RMS.Commons.Key.UserMaintenance=Manutenzione Utente
RMS.Commons.Key.RateCodeHasChangedCheckTheList=Questo codice tariffario è cambiato. Controllare la lista dei codici tariffari
RMS.Commons.Key.Page=Pagina
RMS.Commons.Key.PasswordRequired=La password è richiesta
RMS.Commons.Key.NoneWithDashes=- Nessuno -
RMS.Commons.Key.DateWithColon=Data:
RMS.Commons.Key.CostOfWalkRequiredGTEZero=Costo del trasferimento è richiesto. Deve essere maggiore o uguale a zero. Si prega di inserire i numeri senza virgole.
RMS.Commons.Key.UserIDRequired=ID Utente è richiesto
RMS.Commons.Key.FiscalCalendar=Calendario Fiscale
RMS.Commons.Key.Unqualified=Non qualificato
RMS.Commons.Key.RateValueNotGreaterThan9999999=Valore tariffa non può essere maggiore di 9999999
RMS.Commons.Key.AppServerShutDownBeforeBDEFailure=Errore:: Il server applicativo era arresto prima che i BDE potrebbero concludere.
RMS.Commons.Key.DateGapWithNextEntry=Info: Data spazio con voce successiva
RMS.Commons.Key.RolesDatabaseLoadError=Ruoli database errore di caricamento
RMS.Commons.Key.Of=di
RMS.Commons.Key.AssignDateRangeToNote=Assegna intervallo di data alla nota
RMS.Commons.Key.DemandAndWash=Chiedi e Attesa
RMS.Commons.Key.PerRoomServicingCost=Per Costi di Manutenzione Camera
RMS.Commons.Key.RateDetailsRequired=Dettagli Tariffario sono richieste
RMS.Commons.Key.NoPropertiesAvailable=Nessun proprietà disponibili
RMS.Commons.Key.BudgetingAndPlanning=Preventivo e Progettare
RMS.Commons.Key.Users=Utente
RMS.Commons.Key.RateCodeDetails=Codice Dettaglio Tariffario
RMS.Commons.Key.Address=Indirizzo
RMS.Commons.Key.Disable=Disattiva
RMS.Commons.Key.DateGapWithPreviousAndNext=Info: Data spazio con le voci precedenti e successive
RMS.Commons.Key.ChangePassword=Modifica Password
RMS.Commons.Key.BookingMethodByBlocks=Metodo di prenotazione da blocchi
RMS.Commons.Key.WashOnly=Attesa Soltanto
RMS.Commons.Key.RateCodesDatabaseUpdateError=Errore di aggiornamento del database dei codici tariffario
RMS.Commons.Key.RateCodesDatabaseDeleteError=Errore di cancellazione della database di codici tariffario
RMS.Commons.Key.CreateNewWithDoubleDashes=-- Creato nuovo --
RMS.Commons.Key.ForecastSystemWash=Sistema Previsone Attesa
RMS.Commons.Key.FirstStartDate=Prima data di inizio
RMS.Commons.Key.LastEndDate=Ultima data di scadenza
RMS.Commons.Key.AvailabilityCheck=Controllo disponibilità tariffaria
RMS.Commons.Key.CurrencyCode=Codice Valuta
RMS.Commons.Key.NoRateCodeDetailsDefined=Nessun codice dettagli tariffari definito
RMS.Commons.Key.AssignableProperties=Proprietà assegnabili
RMS.Commons.Key.GroupPricingDetails=Dettagli Prezzo Gruppo
RMS.Commons.Key.UnmappedMarketSegments=Unmapped Settori di mercato
RMS.Commons.Key.RoleName=Nome Ruolo
RMS.Commons.Key.SpecialEventCategories=Categorie Evento Speciale
RMS.Commons.Key.Role=Ruolo
RMS.Commons.Key.WashOverride=Sovrascrittura Attesa
RMS.Commons.Key.RateCodeIsRequired=E' richiesto Codice Tariffario
RMS.Commons.Key.Analyze=Analizzare
RMS.Commons.Key.OccupancyLastYearActualVsForecast=Occupazione - L'anno scorso reale vs  Previsione
RMS.Commons.Key.Weeks=Settimana(e)
RMS.Commons.Key.DaysBracket=Giorno(i)
RMS.Commons.Key.Years=Anno(i)
RMS.Commons.Key.Back=Indietro
RMS.Commons.Key.OccupancyChangeInForecast=Occupazione-Modifica in Previsone
RMS.Commons.Key.CreationDate=Data Creazione
RMS.Commons.Key.ThisYearOccForecastDiffersFromLastYearActualOccupancy=Se quest'anno di Occupazione Previsione si differenzia da questi ultimi anni effettivi di occupazione da
RMS.Commons.Key.AlertStatus=Avviso Stato
RMS.Commons.Key.LastDayOfTheMonth=Ultimo giorno del mese
RMS.Commons.Key.Summation=Sommatoria.
RMS.Commons.Key.TheOccForecastChangesBy=Occupazione per i cambiamenti previsti
RMS.Commons.Key.AgeInDays=Età nel Giorno
RMS.Commons.Key.DayOfMonthBetweenOneTwentyEight=Giorno del mese dovrebbe essere tra 1-28
RMS.Commons.Key.NoAlerts=Nessun Avviso
RMS.Commons.Key.Review=Revisione
RMS.Commons.Key.NoAlertRulesConfigured=Nessuna delle regole di avviso sono state configurate.
RMS.Commons.Key.SystemDefined=Sistema Definito
RMS.Commons.Key.Months=Mese(i)
RMS.Commons.Key.MonitoringWindow=Finestra del monitoraggio
RMS.Commons.Key.UserDefinedAlertsSetup=Definito Avvisi Utenti -Installazione
RMS.Commons.Key.SystemAlerts=Avvisi Sistema
RMS.Commons.Key.ReAssessEvery=Rivalutare ogni
RMS.Commons.Key.The=Il
RMS.Commons.Key.NextCheckDate=Prossima Data Arrivo
RMS.Commons.Key.Action=Azione
RMS.Commons.Key.SelectValidMonthlyGenerationFrequency=Selezionare una valida generazione frequenza mensile
RMS.Commons.Key.UserDefined=Utente definito
RMS.Commons.Key.MonitorWindow=Monitoraggio Finestra
RMS.Commons.Key.AlertDetails=Dettagli Avviso
RMS.Commons.Key.NotesLimit255Characters=Le note non possono superare 255 caratteri
RMS.Commons.Key.LOS2=LOS2
RMS.Commons.Key.HistoryOfMarketSegmentAssignments=Cronologia delle assegnazioni del segmento di mercato
RMS.Commons.Key.Rates=Tariffe
RMS.Commons.Key.LengthOfStay=LOS
RMS.Commons.Key.LOS6=LOS6
RMS.Commons.Key.AvailableUptoOccupancyForecastPercentage=Disponibile fino ad Occupazione Prevista (%)
RMS.Commons.Key.HistoryOfBARDecisionOverridesForWithColon=Cronologia Decisione BAR sovrascritto per:
RMS.Commons.Key.BARDecisionsReviewAndOverride=BAR_Decisioni Revisionato e Sovrascritto
RMS.Commons.Key.LOS3=LOS3
RMS.Commons.Key.BARDecisionsDoNotExistForSelectedDate=Le decisioni della BAR non esistono per la data selezionata
RMS.Commons.Key.BarRateMaintenance=Manutenzione Tariffa Bar
RMS.Commons.Key.BAROverrides=BAR Sovrascrittura
RMS.Commons.Key.GroupWashOverrideInEffectForThisDate=Una cancellazione di un Gruppo prenotato è in esecuzione per questa data.
RMS.Commons.Key.BARRateDetails=Dettagli Tariffa BAR
RMS.Commons.Key.MaximumLOS=Massimo LOS
RMS.Commons.Key.HighlightDifferentialDates=Evidenziare Soltanto Dati Differenziali
RMS.Commons.Key.LOS7=LOS7
RMS.Commons.Key.LOS4=LOS4
RMS.Commons.Key.UnconstrainedDemand=La domanda non vincolata
RMS.Commons.Key.RanksMustBeUniqueAndPositive=Le classifiche devono avere numeri interi e positivi.Le classifiche possono variare da 1 a {0}.
RMS.Commons.Key.LOS1=LOS1
RMS.Commons.Key.Full=Pieno
RMS.Commons.Key.StartDateMustBeAfterSystemDate=Data di partenza deve essere successiva alla data del sistema
RMS.Commons.Key.ForecastOverrideInEffectForThisDate=Una previsione sovrascritta è in esecuzione per questa data.
RMS.Commons.Key.LinkedProductName=Nome Prodotto Collegato
RMS.Commons.Key.LOS5=LOS5
RMS.Commons.Key.MinimumLOSGreaterThanMaximumLOS=Valore minimo LOS è maggiore del valore massimo LOS per i seguenti - {0}.
RMS.Commons.Key.MinimumLOS=Minimo LOS
RMS.Commons.Key.MonthDashYear=Mese -Anno
RMS.Commons.Key.TransientRevenue=Reddito transitorio
RMS.Commons.Key.QuietNights=Notti calme
RMS.Commons.Key.EndMonthMustBeEarlierThanCurrentSystemMonth=Il mese di conclusione dovrebbe essere in anticipo del mese corrente del sistema
RMS.Commons.Key.TransientRoomNights=Notti Camera Transitoria
RMS.Commons.Key.BusyNights=Notti occupate
RMS.Commons.Key.ShoulderNights=Notte a basso costo
RMS.Commons.Key.Budget=Preventivo
RMS.Commons.Key.NoFileImported=Nessun file importato
RMS.Commons.Key.ForecastForTheDay=Previsione per il giorno
RMS.Commons.Key.Hide=Nascosto
RMS.Commons.Key.NextYear=Prossimo Anno
RMS.Commons.Key.PreviousMonth=Mese precedente
RMS.Commons.Key.LowOccupancy=Occupazione Bassa
RMS.Commons.Key.PreviousYear=Anno precedente
RMS.Commons.Key.TotalExpectedOccupancyForTheMonth=Totale Occupazione Prevista per il mese
RMS.Commons.Key.HighOccupancy=Alta occupazione
RMS.Commons.Key.NextMonth=Prossimo Mese
RMS.Commons.Key.NoDataForSelectedDateRange=Nessun dato disponibile per l'intervallo di date selezionate.
RMS.Commons.Key.PositiveNonZeroRoomCapacityRequired=Capienza della camera è richiesta. Deve essere maggiore di zero. Si prega di inserire i numeri senza virgole.
RMS.Commons.Key.CapacityOfConfiguredRoomTypesExceedsHotelCapacity=Capacità di tutte le tipologie di camere configurate supera l'hotel.
RMS.Commons.Key.RoomTypeNameRequired=Nome Tipo Camera è richiesto
RMS.Commons.Key.ReferenceChannelIndicator=Indicatore Canale Riferimento
RMS.Commons.Key.NoRoomTypesConfigured=Nessun tipo camera è stato configurato.
RMS.Commons.Key.RevPARAnalysis=Analisi RevPAR
RMS.Commons.Key.RoomsSoldAnalysis=Analisi Camere Vendute
RMS.Commons.Key.ADRAnalysis=Analisi ADR
RMS.Commons.Key.Hotels=Hotels
RMS.Commons.Key.NoCompetitorInformation=Nessuna informazione concorrente
RMS.Commons.Key.WrongDateRangeMessageUsingStartOfCurrentMonthToSystemToday=Intervallo date errate. Data di partenza superiore a fine data. Utilizzando intervallo predefinito ad.es. Inizio del mese corrente a data Sistema.
RMS.Commons.Key.WorksheetEntryFor=Voce foglio di lavoro per
RMS.Commons.Key.Analysis=Analisi
RMS.Commons.Key.ExchangeRate=Cambio Valuta
RMS.Commons.Key.BaseCurrency=Valuta Base
RMS.Commons.Key.RestrictionReports=Restrizione Rapporto
RMS.Commons.Key.BudgetDetails=Dettagli Preventivo
RMS.Commons.Key.BudgetedRevenue=Guadagno Preventivo
RMS.Commons.Key.TotalDemand=Totale Richiesto
RMS.Commons.Key.RoomsSoldMTDPlusForecastForRestOfMonth=Camere vendute MTD + Previsioni per il resto del mese
RMS.Commons.Key.OccupancyForecastWithGroup=Previsione Occupazione con gruppo
RMS.Commons.Key.Commission=Commissione
RMS.Commons.Key.CurrentDefiniteGroups=Gruppi Correnti Definiti
RMS.Commons.Key.Dates=Dati
RMS.Commons.Key.YellowShadedInputsDeleted=Le immissioni (i) ombreggiate gialle sono state cancellate dalla lista di immissione nel Sistema Strumenti.
RMS.Commons.Key.ProfitPerGroup=Reddito per Gruppo
RMS.Commons.Key.RoomDisplacementCost=Costo Spostamento Camera
RMS.Commons.Key.AncillaryRevenueStreamInput=Immissione Flusso Entrate Ausiliarie
RMS.Commons.Key.PreStay=Pre-nottamento
RMS.Commons.Key.Definite=Definito
RMS.Commons.Key.Size=Dimensione
RMS.Commons.Key.TentativeGroups=Tentativo Gruppi
RMS.Commons.Key.MeetingRoomRentals=Affitti Sala Riunione
RMS.Commons.Key.NetIncrementalRevenue=Netto Ricavo Incrementale
RMS.Commons.Key.TotalTransient=Totale Transitorio
RMS.Commons.Key.PerRoomDiscount=Camere scontate per
RMS.Commons.Key.TotalPerRoomNight=Totale Notte Camera per
RMS.Commons.Key.TotalIncrementalRevenue=Reddito incrementale totale
RMS.Commons.Key.RevenueItem=Elemento Reddito
RMS.Commons.Key.DADividedByVAAsPercentage=DA/VA come %
RMS.Commons.Key.OccupancyForecastWithoutGroup=Occupazione prevista senza gruppo
RMS.Commons.Key.DisplacementAssessment=Valutazione di spostamento
RMS.Commons.Key.GrossIncrementalProfitPercentage=Profitto incrementale lordo %
RMS.Commons.Key.NetRoomRevenue=Reddito netto della Camera
RMS.Commons.Key.OtherRevenue=Altre Entrate
RMS.Commons.Key.RateCode=Codice Tariffa
RMS.Commons.Key.AncillaryRevenue=Entrate Ausiliarie
RMS.Commons.Key.ContractIssuedGroups=Il contratto pubblica Gruppo
RMS.Commons.Key.PostStay=Post-soggiorno
RMS.Commons.Key.SelectedProperties=Proprietà selezionate
RMS.Commons.Key.PreferredArrivalDate=Data di arrivo preferita
RMS.Commons.Key.GrossIncrementalProfit=Profitto incrementale lordo
RMS.Commons.Key.TotalNetRevenue=Reddito netto totale
RMS.Commons.Key.FullRateRoomNights=Tariffa piena Notti Camera
RMS.Commons.Key.ChooseMarketSegmentInGeneralInfoSection=Scegliere un segmento di mercato nella sezione Informazioni generali.
RMS.Commons.Key.AncillaryRevenueStreamInputs=Flusso Immissione Entrate Ausiliarie
RMS.Commons.Key.Deleted=Eliminato
RMS.Commons.Key.ScenarioGroups=Gruppi Scenari
RMS.Commons.Key.TypeOfEvaluation=Tipo di Valutazione
RMS.Commons.Key.MaterializationStatus=Stato Materializzazione
RMS.Commons.Key.DeletedBy=Eliminato da
RMS.Commons.Key.Tentative=Tentativo
RMS.Commons.Key.GrossRevenue=Reddito lordo
RMS.Commons.Key.ComplimentaryRoomNights=Notti Camere Gratuite
RMS.Commons.Key.DayOfStay=Data del Soggiorno
RMS.Commons.Key.NetRevenue=Reddito netto
RMS.Commons.Key.ValueAssessment=Valutazione valore
RMS.Commons.Key.RevenuePerGroup=Reddito per gruppo
RMS.Commons.Key.EvaluatedBy=Valutato da
RMS.Commons.Key.IncrementalRooms=Camere Incrementali
RMS.Commons.Key.VADashDA=VA_DA
RMS.Commons.Key.NetIncerementalRoomRevenue=Reddito incrementale netto della camera
RMS.Commons.Key.SuggestedRate=Tariffa suggerita
RMS.Commons.Key.FoodAndBeverage=Alimenti e Bevande
RMS.Commons.Key.LengthOfStayFullText=Durata Soggiorno
RMS.Commons.Key.WashOverrideNotPermitted=Cancellazione dell'attesa non è consentita in quanto previsione attività è impostata su Nulla o transitorio Gruppo Segmento di mercato contiene uno o più non-Block segmenti di mercato.
RMS.Commons.Key.GroupDetails=Dettagli Gruppo
RMS.Commons.Key.WrongDateFormatUsingDefaultFormatAndRangeSysDatePlus7=Data formato Data Sistema +7.
RMS.Commons.Key.WrongDateRangeMessageUsingSystemDateToSystemDatePlus7=Intervallo date errate. Data di partenza superiore a data di fine. Utilizzando il formato predefinito e l'intervallo ad.es  Data Sistema Data Sistema +7.
RMS.Commons.Key.NoForecastExistsForGivenRangeUsingDefaultSystemDateToSystemDatePlus7=Nessuna previsione esistente per l'intervallo data. Utilizzando il formato predefinito e l'intervallo ad.es Data Sistema a Data sistema +7.
RMS.Commons.Key.RateValueNotGreaterThan9999999FullStop=Tariffa valore non può essere maggiore di 9999999
RMS.Commons.Key.MassBARConfiguration=Mass BAR configurazione
RMS.Commons.Key.Modify=Modifica
RMS.Commons.Key.StaleDataExists=Dati non aggiornati esistenti su questa pagina. Fare clic sul pulsante Annulla e modificare nuovamente il record.
RMS.Commons.Key.MoreThanThreeDecimalsNotAllowed=Più di tre decimali non sono permessi.
RMS.Commons.Key.EndDateMustBeAfterStartDate=Data di scadenza deve essere successiva alla data di inizio.
RMS.Commons.Key.ShowAllDecisions=Mostrare tutte le decisioni
RMS.Commons.Key.MaxDotLOS=Max. LOS
RMS.Commons.Key.Import=Importa
RMS.Commons.Key.AllLOS=Tutti LOS
RMS.Commons.Key.UpToPercentage=Fino a %
RMS.Commons.Key.Export=Esporta
RMS.Commons.Key.DecisionsFor=Decisione Per
RMS.Commons.Key.EachLOS=Ogni LOS
RMS.Commons.Key.SystemUse=Sistema Usato
RMS.Commons.Key.MinDotLOS=Min. LOS
RMS.Commons.Key.FilterBy=Filtro Da
RMS.Commons.Key.Unlimited=Non limitati
RMS.Commons.Key.RoomsAllocated=Camere assegnate
RMS.Commons.Key.InvetoryType=Tipo di Inventario
RMS.Commons.Key.Proportional=Proporzionale
RMS.Commons.Key.NoLimit=Nessun Limite
RMS.Commons.Key.LicenseUpgradeStatus=Stato Aggiornamento Licenza
RMS.Commons.Key.NoActiveOverridesExistsNoRevert=Nessuna Sovrascrittura Attiva esiste. L'opzione per andare indietro non è possibile.
RMS.Commons.Key.SystemNotProcessedOverrideDenotion=** - Indica che il sistema non ha sovrascritture elaborate
RMS.Commons.Key.ActualRoomsSold=Camere effettive Venduti
RMS.Commons.Key.RetrievedForecastOverrides=Sovrascrittura Previsione Recuperata
RMS.Commons.Key.OverrideDate=Sovrascrittura dati
RMS.Commons.Key.NoShowRooms=Camere No Show
RMS.Commons.Key.NoDataAvailableForTheDateRange=Nessun Dato è disponibile per l'intervallo dati
RMS.Commons.Key.EnterCriteria=Inserire Criteri
RMS.Commons.Key.Duration=Durata
RMS.Commons.Key.ResetDefaults=Reimposta Predefiniti
RMS.Commons.Key.ProduceSummaryOrDetailedGroupEvaluations=Produrre una tabella sommaria o dettagliata delle valutazioni del gruppo.
RMS.Commons.Key.IndividualProperty=Proprietà specifica
RMS.Commons.Key.ArrivalDateNewLine=Arrivo \nData
RMS.Commons.Key.IDeaSForecast=Previsione IDeaS
RMS.Commons.Key.InvalidAnalysisEndDate=L'Analisi della Data di Scadenza inserita non è valida. Si prega di riprovare.
RMS.Commons.Key.UserOverrideBARRateValueLOS7=Sovrascrittura Utente Valore Tariffa BAR - LOS7
RMS.Commons.Key.UserOverrideBARRateValueLOS8Plus=Valore Tariffa BAR Sovrascrittura Utente- LOS8+
RMS.Commons.Key.UserOverrideBARLOS7=Sovrascrittura Utente BAR - LOS7
RMS.Commons.Key.UserOverrideBARLOS6=Sovrascrittura Utente BAR - LOS6
RMS.Commons.Key.UserOverrideBARLOS5=Sovrascrittura Utente BAR - LOS5
RMS.Commons.Key.UserOverrideBARLOS4=Sovrascrittura Utente BAR - LOS4
RMS.Commons.Key.UserOverrideBARLOS3=Sovrascrittura Utente BAR - LOS3
RMS.Commons.Key.UserOverrideBARLOS2=Sovrascrittura Utente BAR - LOS2
RMS.Commons.Key.UserOverrideBARLOS1=Sovrascrittura Utente BAR - LOS1
RMS.Commons.Key.AdjustStartDateWithColon=Registrare la data di inizio:
RMS.Commons.Key.WebBARRateValueLOS2=Valore Tariffa Web BAR - LOS2
RMS.Commons.Key.UserOverrideBARRateValueLOS3=Utente Sovrascrittura BAR Tariffa Valore - LOS3
RMS.Commons.Key.UserBAROverrideBARByDay=Sovrascrittura BAR Utente BAR da Giorno
RMS.Commons.Key.MarketSegmentsSummarized=Segmenti di mercato ricapitolati
RMS.Commons.Key.InvalidAsOfDateEntered=Il decorrere della data inserita non è valida. Riprovare.
RMS.Commons.Key.SelectStartDateStartsWithSelectedDayOfWeek=Si prega di selezionare la data di inizio che comincia con il giorno selezionato della settimana.
RMS.Commons.Key.ProduceTableOfAllActiveSystemOverrides=Produrre una tabella di tutte le sovrascritture del sistema attivo
RMS.Commons.Key.SystemBARRateValueLOS7=Valore Tariffa BAR Sistema - LOS7
RMS.Commons.Key.ProduceTableGraphOfOccupancyBookingCurves=Produrre una tabella e grafico illustrando come le curve di occupazione si formano quando si avvicinano al giorno di arrivo.
RMS.Commons.Key.SelectUsersWithColon=Selezionare Utenti:
RMS.Commons.Key.ForecastOverrideReport=Rapporto Previsione Sovrascrittura
RMS.Commons.Key.UserBARRateValueLOS4=Valore Tariffa BAR UTENTE -LOS4
RMS.Commons.Key.Sold=Venduto
RMS.Commons.Key.NumberOfContacts=Numero dei contatti
RMS.Commons.Key.SystemActivityReport=Rapporto d'attività del sistema
RMS.Commons.Key.LastUpdatedOn=Ultimo Aggiornamento il
RMS.Commons.Key.SystemBARRateValueLOS5=Valore Tariffa BAR Sistema - LOS5
RMS.Commons.Key.WebBARRateValueLOS1=Valore Tariffa Web BAR- LOS1
RMS.Commons.Key.WebBARLOS8Plus=Web BAR - LOS8+
RMS.Commons.Key.WebBARLOS7=Web BAR - LOS7
RMS.Commons.Key.SystemBARLOS8Plus=Sistema BAR - LOS8+
RMS.Commons.Key.WebBARLOS6=Web BAR - LOS6
RMS.Commons.Key.SystemBARLOS7=Sistema BAR - LOS7
RMS.Commons.Key.WebBARLOS5=Web BAR - LOS5
RMS.Commons.Key.SystemBARLOS6=Sistema BAR - LOS6
RMS.Commons.Key.WebBARLOS4=Web BAR - LOS4
RMS.Commons.Key.SystemBARLOS5=Sistema BAR - LOS5
RMS.Commons.Key.WebBARLOS3=Web BAR - LOS3
RMS.Commons.Key.SystemBARLOS4=Sistema BAR - LOS4
RMS.Commons.Key.WebBARLOS2=Web BAR - LOS2
RMS.Commons.Key.SystemBARLOS3=Sistema BAR - LOS3
RMS.Commons.Key.WebBARLOS1=Web BAR - LOS1
RMS.Commons.Key.SystemBARLOS2=Sistema BAR - LOS2
RMS.Commons.Key.SystemBARLOS1=Sistema BAR - LOS1
RMS.Commons.Key.ProduceTableWithLRVAndMinLOSRestrictions=Produrre una tabella con le ultime limitazioni di valori e di MinLOS della Camera
RMS.Commons.Key.SelectDateWithSelectedDayOfWeek=Si prega di selezionare una data che comincia con il giorno selezionato della settimana.
RMS.Commons.Key.EducationHotelSummaryOrDetailedUserCompletionStatus=Produrre una tabella sommaria o dettagliata delle condizioni di completamento dell'utente per l'hotel educativo.
RMS.Commons.Key.TableOfPickUpOrChangeAlongV5Controls=Produrre una tabella che illustra Pick-up o Modifiche accanto ai comandi di IDeaS v5 e come sono stati cambiati durante lo stesso periodo.
RMS.Commons.Key.IncludeWithColon=Includere:
RMS.Commons.Key.TableGraphToCompareRestrictionsAgainsteSelectedRate=Produrre una tabella e un  per confrontare le limitazioni ad una tariffa selezionata.
RMS.Commons.Key.NumberOfVisits=Numero di Visite
RMS.Commons.Key.SystemPaceReport=Rapporto Andamento Sistema
RMS.Commons.Key.TableOfPickUpOrChangeOfSelectedDataElements=Produrre una tabella che illustra il Pick-Up o il cambiamento degli elementi selezionati e come sono cambiati in un periodo dato.
RMS.Commons.Key.PickUpChangeReport=Rapporto Pick- Up/Modifica
RMS.Commons.Key.AnalysisEndDateWithColon=Analisi Data di Scadenza:
RMS.Commons.Key.TableGraphComparingOccAndRevForecastAgainstHotelRoomsSoldAndRoomRevenue=Produrre una tabella e un  per il confronto delle previsioni del reddito e l'occupazione contro le camere hotel vendute ed il reddito camera.
RMS.Commons.Key.ForecastComparisonReport=Previsione Rapporto di Confronto
RMS.Commons.Key.NightCategory=Categoria Notte
RMS.Commons.Key.UserBARRateValueLOS5=Valore Tariffa BAR dell'utente - LOS5
RMS.Commons.Key.DifferentialLRVByRoomTypeReport=Ultimo valore differenziale del Rapporto Tipo Camera.
RMS.Commons.Key.SelectYearWithColon=Selezionare Anno
RMS.Commons.Key.TableWithLinkedProductBAR=Produrre una tabella con la migliore tariffa disponibile collegata al prodotto.
RMS.Commons.Key.TableOfBARRateReview=Produrre una tabella delle migliori tariffe disponibili per la revisione
RMS.Commons.Key.UserOverrideBARRateValueLOS1=Sovrascrittura Utente Valore Tariffa BAR - LOS!
RMS.Commons.Key.ForecastStrategyBoardDEP=Previsione da tavolo di strategia - Estrazione Programma Dati
RMS.Commons.Key.TableOfFullOrDifferentailLRVByRoomType=Produrre una tabella di completa o differenziali dal Tipo Camera.
RMS.Commons.Key.SelectEndDateSuchAsRangeIsFullWeek=Si prega di selezionare Data di scadenza in modo che l'intervallo date è una settimana completa.
RMS.Commons.Key.PickUpChangeAndDifferentialControlsReport=Pick Up / Cambio e Rapporto Controlli Differenziali
RMS.Commons.Key.UserBARRateValueLOS6=Valore Tariffa BAR Utente - LOS6
RMS.Commons.Key.TableWithSystemUpdateStatus=Produrre una tabella con lo stato di aggiornamento del sistema di proprietà selezionato.
RMS.Commons.Key.UserBAROverrideLOS6=Sovrascrittura BAR Utente - LOS&
RMS.Commons.Key.DateRangeCompleteWeek=L'intervallo data dovrebbe essere una settimana completa.
RMS.Commons.Key.OverbookingByRoomTypeReport=Overbooking da Rapporto Tipo Camera
RMS.Commons.Key.SavedReportName=Nome Rapporto Salvato
RMS.Commons.Key.TableShowingPaceOfBAR=Produrre una tabella che mostra l'andamento della migliore tariffa disponibile.
RMS.Commons.Key.WebBARRateValueLOS3=Valore Tariffa Web BAR - LOS£
RMS.Commons.Key.InvalidAnalysisStartDateEntered=L'analisi Data di Inizio inserita non è valida. Si prega di riprovare.
RMS.Commons.Key.ProduceTableOfUserDetails=Produrre una tabella con i particolari dell'utente.
RMS.Commons.Key.TableGraphOfRoomsSoldOccFcstRoomRevOrAdRComparison=Produrre una tabella e rappresentare graficamente l'illustrazione delle camere vendute, la previsione di occupazione, il reddito camera o il confronto ADR a partire da un intervallo data selezionato.
RMS.Commons.Key.ProduceTableOfBAR=Produrre una tabella delle migliori tariffe disponibili.
RMS.Commons.Key.ProduceTableOfKeyBusinessPerformanceIndicators=Produrre una tabella di indicatori chiave di prestazione aziendali.
RMS.Commons.Key.Demand=Richiesta
RMS.Commons.Key.BestAvailableratePaceReport=Miglior Rapporto dell' Andamento della Tariffa disponibile
RMS.Commons.Key.SpecialEventThisYear=Evento Speciale Quest' Anno
RMS.Commons.Key.PropertyViewWithColon=Visualizza proprietà:
RMS.Commons.Key.UserBAROverrideLOS4=Sovrascrittura BAR utente -LOS4
RMS.Commons.Key.OccupancyBookingCurveReport=Rapporto della curva di prenotazione di occupazione
RMS.Commons.Key.SpecialEventLastYear=Evento Speciale dello Scorso Anno
RMS.Commons.Key.ExchangeRates=Tariffe di scambio
RMS.Commons.Key.UserBARRateValueLOS7=Valore Tariffa BAR Utente -LOS7
RMS.Commons.Key.OccupancyForecastGroup=Gruppo Previsione Occupazione
RMS.Commons.Key.OccupancyForecastTransient=Transitoria Previsione Occupazione
RMS.Commons.Key.RoomsSoldGroup=Gruppo Camere Vendute
RMS.Commons.Key.RoomsSoldTransient=Transitorie Camere Vendute
RMS.Commons.Key.RoomsSoldTotal=Totale Camere Vendute
RMS.Commons.Key.SelectStartDateStartsWithFirstDayOfMonth=Selezionare la data di inizio che comincia con il primo giorno del mese.
RMS.Commons.Key.CRSRestrictionReport=Rapporto di Restrizione CRS
RMS.Commons.Key.SelectEndDateAsRangeIsCompleteMonth=Selezionare la data discadenza tale che l'intervallo data è un mese completo.
RMS.Commons.Key.InvalidStartDate=La data di inizio inserita non è valida. Riprovare
RMS.Commons.Key.EducationalHotelCompletionReport=Rapporto Completamento Educativo dell'Hotel
RMS.Commons.Key.TableGraphOfSystemForecastsAndDecisions=Produrre una tabella e un grafico illustrando le previsioni e le decisioni del sistema.
RMS.Commons.Key.UserBARRateValueLOS8Plus=Valore Tariffa BAR Utente - LOS8+
RMS.Commons.Key.HistoricalRestrictionsReport=Rapporto storico di Restrizioni
RMS.Commons.Key.ForecastAccuracyReading=Lettura Accurata Previsione (Calcolata)
RMS.Commons.Key.OverBookingWithDashNewLine=Sovra-\nprenotazione
RMS.Commons.Key.ShowSpecialEventsWithColon=Mostrare gli Eventi Speciali:
RMS.Commons.Key.WebBARRateValueLOS5=Valore Tariffa BAR wEB - LOS5
RMS.Commons.Key.Overrides=Sovrascritture
RMS.Commons.Key.TableWithRestrictionLevelsAndRateDetails=Produrre una tabella con i livelli di restrizione ed i particolari delle tariffe.
RMS.Commons.Key.TableOfChangedLRVsByRoomType=Produrre una tabella degli ultimi Valori variabili della Tipo Camera dal Tipo Camera.
RMS.Commons.Key.UserOverrideBARRateValueLOS2=Valore Tariffa BAR Sovbrascrittura Utente - LOS2
RMS.Commons.Key.SystemBARRateValueLOS2=Valore Tariffa BAR Sistema - LOS2
RMS.Commons.Key.UserBAROverrideLOS7=Sovrascrittura Utente BAR -LOS7
RMS.Commons.Key.BARReviewReport=Rapporto Revisione Migliore Tariffa Disponibile
RMS.Commons.Key.LRVByRoomTypeReport=Rapporto Camera Tipo da Valore Ultima Camera
RMS.Commons.Key.SelectStartDayOfWeekWithColon=Selezionare Inizio giorno-della-settimana:
RMS.Commons.Key.MSGroupsSummarized=Gruppi MS Ricapitolati
RMS.Commons.Key.ForecastValidationReport=Rapporto Convalida Prevista
RMS.Commons.Key.EndDateAfterLastForecastedDateSystemDefaultingToLastForecastedDate=La data di Scadenza ha luogo dopo l'ultima data prevista, {0}. Il sistema sta predefinendo la data di scadenza all'ultima data prevista.
RMS.Commons.Key.StartDateAfterLastForecastedDateStartDateDefaultingTo0=La data di inizio ha luogo dopo l'ultima data prevista, {0}. Predefinire Data di inizio a {0}.
RMS.Commons.Key.EndMonthWithColon=Fine Mese:
RMS.Commons.Key.UserDetailsAdminReport=Rapporto Admin Dettagli Utente
RMS.Commons.Key.UserOverrideBARRateValueLOS5=Valore Tariffa BAR Sovrascrittura Utente - LOS5
RMS.Commons.Key.TableGraphOfRoomsSoldAndOccFcstAndForecastAccuracy=Produrre una tabella e un grafico illustrando le camere vendute e le occupazioni previste così come previste accurate letture.
RMS.Commons.Key.LinkedProductBARReport=Migliore rapporto tariffea disponibile collegata al prodotto
RMS.Commons.Key.ImpactsForecastQuestion=Effetti Previsione
RMS.Commons.Key.TableOfSpecialEventsGroupRatesOrRestrictionLevelsMultiProperty=Produrre una tabella degli eventi speciali, tariffe gruppo o dei livelli di restrizione per le proprietà multiple.
RMS.Commons.Key.AuditReport=Rapporto contabile
RMS.Commons.Key.WebBARRateValueLOS4=Valore Tariffa Web BAR - LOS4
RMS.Commons.Key.TableOfCampaignInfoEntered=Produrre una tabella di una campagna di informazioni inserita tramite il Modulo Amministrativo di Campagna.
RMS.Commons.Key.WebBARRateValueLOS7=Valore Tariffa Web BAR - LOS7
RMS.Commons.Key.StartDateBeforeEarliestForecastedDateDefaultingTo0=Data di Inizio è prima della Prima Data Prevista, {0}. Inizio Data predefinita a {0}.
RMS.Commons.Key.ActivityUntilEndDateWithColon=Attività fino alla data di scadenza:
RMS.Commons.Key.ActivityFromStartDateWithColon=Attività a partire dalla data di inizio
RMS.Commons.Key.FPLOS=FPLOS
RMS.Commons.Key.TableToShowUserActivityInSystem=Produrre una tabella per mostrare l'attività dell'utente nel sistema.
RMS.Commons.Key.SystemToday=Sistema oggi
RMS.Commons.Key.UserOverrideBARRateValueLOS4=Valore Tariffa BAR Sovrascrittura Utente - LOS4
RMS.Commons.Key.CampaignName=Nome di campagna
RMS.Commons.Key.SelectedCount=Conteggio selezionato
RMS.Commons.Key.SystemBARRateValueLOS4=Valore Tariffa BAR Sistema - LOS4
RMS.Commons.Key.LinkedProductFPLOSReport=Rapporto FPLOS Prodotto Collegato
RMS.Commons.Key.ExportReportToXLAcrobatXML=Esportare questo rapporto in Excel, Acrobata o XML.
RMS.Commons.Key.UserBARRateValueLOS1=Valore Tariffa BAR Utente - LOS1
RMS.Commons.Key.Campaign=Campagna
RMS.Commons.Key.ActualToDate=Effettivo( a data)
RMS.Commons.Key.ShowAllDataColon=Visualizza Tutti i Dati.
RMS.Commons.Key.SummationIntervalColon=Intervallo di sommatoria.:
RMS.Commons.Key.RatesAdminReport=Rapporto Amministrazione Tariffe
RMS.Commons.Key.CampaignManagementReport=Rapporto Gestione Compagnia
RMS.Commons.Key.AsOfDateColon=In Data:
RMS.Commons.Key.TableGraphOfPropertyPerformanceMonthByMonthToLastYear=Produrre una tabella e un grafico illustrando come una proprietà e/o tenuta sta eseguendo mese per mese rispetto allo stesso mese lo scorso anno, come ad una determinata data.
RMS.Commons.Key.TableOfSpecialEvents=Produrre una tabella degli Eventi Speciali.
RMS.Commons.Key.WebBARRateValueLOS6=Valore Tariffa BAR Web -LOS&
RMS.Commons.Key.UserBAROverrideLOS5=Sovrascrittura BAR Utente - LOS5
RMS.Commons.Key.StartMonthColon=Inizio Mese:
RMS.Commons.Key.ConsolidatedForecastAndBudgetReport=Rapporto Preventivo e Previsione Consolidata
RMS.Commons.Key.SpecialEventsListReport=Rapporto Lista di Eventi Speciali
RMS.Commons.Key.AnalysisStartDateColon=Analisi Data di Inizio:
RMS.Commons.Key.ConfigurationPanel=Pannello di configurazione
RMS.Commons.Key.DataElement=Elementi Dati
RMS.Commons.Key.UserOverrideBARRateValueLOS6=Valore Tariffa BAR Sovrascrittura Utente - LOS6
RMS.Commons.Key.TableGraphOfPaceOfKeySystemValuesForADate=Produrre una tabella e un grafico illustrando l'andamento dei valori del sistema chiave per una data.
RMS.Commons.Key.UserBARRateValueLOS2=Valore Tariffa BAR Utente - LOS2
RMS.Commons.Key.UserBARRateValueLOS3=Valore Tariffa BAR Utente - LOS3
RMS.Commons.Key.AverageForPeriodColon=Media per il periodo:
RMS.Commons.Key.NoDataAvailableForTheseDates=Non ci sono dati disponibili per queste date.
RMS.Commons.Key.SystemBARRateValueLOS6=Valore Tariffa BAR Sistema -LOS6
RMS.Commons.Key.YieldCapacity=Capienza Guadagno
RMS.Commons.Key.SelectMonthColon=Selezionare il mese:
RMS.Commons.Key.ForecastAccuracyReport=Rapporto Accurato Previsto
RMS.Commons.Key.InvalidEndDate=La data di Scadenza inserita non è valida. Riprovare.
RMS.Commons.Key.UserBAROverrideLOS3=Sovrascritura BAR Utente - LOS3
RMS.Commons.Key.TableOfTransientBenefitMeasurement=Produrre una tabella che mostra la misura transitoria del beneficio.
RMS.Commons.Key.Email=Email
RMS.Commons.Key.UserBAROverrideForAllLOS=Sovrascrittura BAR Utente per tutti LOS
RMS.Commons.Key.BudgetComparisonReport=Rapporto Confronto Preventivato
RMS.Commons.Key.MinLOS=MinLOS
RMS.Commons.Key.UserUnconstrainedDemand=Richiesta Utente Non Vincolata
RMS.Commons.Key.UserBAROverrideLOS8Plus=Sovrascrittura BAR Utente -LOS8+
RMS.Commons.Key.WebBARRateValueLOS8Plus=Valore Tariffa Web BAR - LOS8+
RMS.Commons.Key.AnalysisPeriodInBracket=(Periodo di analisi)
RMS.Commons.Key.SystemBARRateValueLOS8Plus=Valore Tariffa BAR Sistema - LOS8+
RMS.Commons.Key.HighlightChangesSinceWithColon=Modifiche Evidenziate Dal:
RMS.Commons.Key.TableForBudgetedOccupancyAndRevenueComparedWithActuals=Produrre una tabella per il confronto dell'Occupazione Preventivata e contro gli effettivi numeri dei redditi e ultime camere vendute previste e numeri dei redditi.
RMS.Commons.Key.PickUp=Pick Up
RMS.Commons.Key.ReportsAToZ=Rapporto dalla A alla Z
RMS.Commons.Key.StartDateBeforeEarliestSystemDateDefaultingTo0=La data di inizio è prima della data del sistema, {0}. Predefinire la data di inizio a {0}.
RMS.Commons.Key.TotalOnBooks=Totale Su Prenotazione
RMS.Commons.Key.FlagChangesSince=Indicatore Modifica Da:
RMS.Commons.Key.Past=Passato
RMS.Commons.Key.TableWithLinkedProductFPLOS=Produrre una tabella con il Collegamento Prodotto Modello Completo della Lunghezza-del-Soggiorno (FPLOS).
RMS.Commons.Key.DemandOverride=Richiesta Sovrascrittura
RMS.Commons.Key.View=Visualizza
RMS.Commons.Key.CommunicationMethod=Metodo di comunicazione
RMS.Commons.Key.SonestaForecastComparisonReport=Rapporto di confronto di previsione di Sonesta
RMS.Commons.Key.TableWithWalksByDateWithOverbooking=Produrre una tabella con le trasferte entro la data con Sovraprenotazione e costo della trasferta.
RMS.Commons.Key.HistoryOfWalksReport=Storia del Rapporto di Trasferimento
RMS.Commons.Key.TotalForecast=Previsione di totale
RMS.Commons.Key.VirtualCapacity=Capienza Virtuale
RMS.Commons.Key.CollapseThisSection=Restringi questa sezione
RMS.Commons.Key.UserBARLOS8Plus=Utente BAR -LOS8+
RMS.Commons.Key.UserBARLOS7=Utente BAR -LOS8+
RMS.Commons.Key.UserBARLOS6=Utente BAR -LOS6
RMS.Commons.Key.UserBARLOS5=Utente BAR -LOS5
RMS.Commons.Key.UserBARLOS4=Utente BAR -LOS4
RMS.Commons.Key.UserBARLOS3=Utente BAR -LOS3
RMS.Commons.Key.UserBARLOS2=Utente BAR -LOS2
RMS.Commons.Key.UserBARLOS1=Utente BAR -LOS1
RMS.Commons.Key.PrintDateColon=Data Stampa:
RMS.Commons.Key.OfferType=Tipo di Offerta
RMS.Commons.Key.TableWithOnTheBooksAndFcstedRoomNights=Produrre una tabella con Su le prenotazioni e le Notti Camere Previste, Reddito ed il ADR.
RMS.Commons.Key.DeliveryMethod=Metodo consegna
RMS.Commons.Key.TableOfAllActiveForecastOverrides=Produrre una tabella di tutte le previsione sovrascritte.
RMS.Commons.Key.SelectDataElementToBeIncludedColon=Selezionare Dati Elemento per essere incluso:
RMS.Commons.Key.SystemBARRateValueLOS1=Sistema BAR Tariffa Valore - LOS1
RMS.Commons.Key.SystemUnconstrainedDemand=Richiesta Sistema Non Vincolante
RMS.Commons.Key.Future=Futuro
RMS.Commons.Key.TableWithOverbookingByRoomType=Produrre una tabella con Sovraprenotazione da Camera Tipo.
RMS.Commons.Key.SystemOverrideReport=Rapporto Sovrascrittura Sistema
RMS.Commons.Key.ImpactForecast=Previsione di effetto
RMS.Commons.Key.ImpactFunctionSpaceForecast=
RMS.Commons.Key.TableOfMultipleDaysOfBARPaceData=Produrre una tabella che illustra i giorni multipli dei tariffe migliori disponibile.
RMS.Commons.Key.MultiPropertySummation=Somma Multi-Proprietà
RMS.Commons.Key.SystemBARRateValueLOS3=Sistema BAR Tariffa Valore _LOS3
RMS.Commons.Key.Responses=Risposte
RMS.Commons.Key.ReportStyleColon=Stile Rapporto:
RMS.Commons.Key.TransientBenefitsReport=Rapporto transitorio dei benefici
RMS.Commons.Key.TargetSegment=Segmento di Obiettivo
RMS.Commons.Key.OnBooksDash=Su- Prenotazione
RMS.Commons.Key.Offer=Offer
RMS.Commons.Key.MarketSegmentsBracket=Segmento Mercato
RMS.Commons.Key.DefaultModule=Modulo Predefinito
RMS.Commons.Key.RoomTypesAndOverBookingDistribution=Tipi Camera e Distribuzione Sovraprenotazione
RMS.Commons.Key.Categories=Categorie
RMS.Commons.Key.GroupTotal=Tatale Gruppo
RMS.Commons.Key.TransientTotal=Totale Transitorio
RMS.Commons.Key.Estimate=Stima
RMS.Commons.Key.DateIsPriorToSystemAccessDate=Data inserita è precedente alla Data d'Ingresso del Sistema {0}. Sono presenti Sovrascritture del Sistema precedenti alla Data d'Ingresso del Sistema. Utilizzando il formato predefinito e l'intervallo ad.es Data Sistema a Data Sistema +7.
RMS.Commons.Key.WalkManagement=Gestione Andamento
RMS.Commons.Key.HistoryOfWalks=Storia degli Andamenti
RMS.Commons.Key.BlackoutEndDateInvalid=Data scadenza interruzione non è valida
RMS.Commons.Key.CampaignManagement=Gestione Campagna
RMS.Commons.Key.OccupancyDates=Date di occupazione
RMS.Commons.Key.BadStartMonth=Il mese di inizio inserito non è valido. Riprovare.
RMS.Commons.Key.BadEndMonth=Il mese di conclusione inserito non è valido. Riprovare.
RMS.Commons.Key.OccupancyMonth=Occupazione Mensile
RMS.Commons.Key.StartDateBeforeEarliestSystemDateDefaultingTo1=La data di inizio ha luogo prima della data del sistema, {0}. Predefinire la Data di inizio a {1}.