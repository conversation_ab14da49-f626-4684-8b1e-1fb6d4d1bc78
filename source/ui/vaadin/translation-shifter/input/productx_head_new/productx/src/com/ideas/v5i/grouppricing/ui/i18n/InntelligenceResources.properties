Delphi.Arrival.Dates.Required.error=Arrival dates required
Delphi.Booking.required.error=Delphi Booking ID required
Delphi.data.export.failed.check.web.service=Error occurred while sending evaluation results data for booking Id {0}.
Delphi.data.reveive.failed.check.web.service=Error occured while receiving data from web service,Please contact system administrator
Delphi.GP.License.Expired=License Has Expired
Delphi.GP.License.Not.Available=Delphi Group Pricing license not available for {0} property
Delphi.GroupPricing.License.Not.Available=GroupPricing License Is Not Available for {0} Property for Delphi.
Delphi.invalid.propertyAccountId=Invalid property information / Configure the property information.
Delphi.message.on.session.timeout=Close the browser for the security reasons
Delphi.NoAccess.Message.prompt=Access to Group Pricing module is denied.
Delphi.PropertyAccount.ID.Required.error=Property information is not received
Delphi.Roomsize.For.Each.Day.Of.Stay.Required.error=Room size for each day of stay is required
Delphi.session.timeout=Your session has timed out
Delphi.Status.cancelled.error=The booking for {0} currently has a status of cancelled and cannot be evaluated at this time.
Delphi.Unknown.BookingID.error=Delphi Booking ID {0} does not currently exist.Please ensure that the booking contains guestrooms , the booking status is P, T or D and that no gaps exist in the room pattern. If the booking meets this criteria, please try again later.
Delphi.User.Doesnothave.Permission.Property=User {0} does not have permissions to property {1}.
Delphi.Userid.Password.Combi.Not.Match.Contact.SysAdmin=Userid - password combination does not match. Please contact system administrator
Delphi.VO.Not.WellSet.inconsistent=Data received from Delphi is inconsistent
Delphi.web.service.must.configure=Configure the delphi web service
DelphiGroupPricing.Evaluation.Details.New.prompt=Delphi Group Pricing Details
GroupPricing.AncillaryRevenue.Ancillary.prompt=Ancillary
GroupPricing.AncillaryRevenue.AncillaryRevenueAssignments.Menu.Name=Ancillary Revenue Assignments
GroupPricing.AncillaryRevenue.AncillaryRevenueStream.CnbName.prompt=Conference and Banquet Revenue Stream Inputs
GroupPricing.AncillaryRevenue.AncillaryRevenueStream.CnbName.Title=Conference and Banquet Revenue Stream
GroupPricing.AncillaryRevenue.AncillaryRevenueStream.Database.delete.error=Ancillary Revenue Stream Input database delete error
GroupPricing.AncillaryRevenue.AncillaryRevenueStream.Title=Ancillary Revenue Stream
GroupPricing.AncillaryRevenue.AncillaryRevenueStreamEdit.Database.Load.Error=Error loading ancillary revenue stream input information
GroupPricing.AncillaryRevenue.AncillaryRevenueStreamEdit.Input.Prompt=Input
GroupPricing.AncillaryRevenue.AncillaryRevenueStreamEdit.Name.Required.error=Ancillary Revenue Stream Input Name is required
GroupPricing.AncillaryRevenue.AncillaryRevenueStreamEdit.ProfitMargin.error=Invalid Profit Margin.
GroupPricing.AncillaryRevenue.AncillaryRevenueStreamEdit.ProfitMargin.Prompt=Profit Margin
GroupPricing.AncillaryRevenue.AncillaryRevenueStreamEdit.ProfitMargin.Range.error=Invalid Range. Please enter a Profit Margin between 0 to 100.
GroupPricing.AncillaryRevenue.AncillaryRevenueStreamEdit.ProfitMargin.Required.error=Profit Margin for Ancillary Revenue Stream Input is required.
GroupPricing.AncillaryRevenue.AncillaryStreamInput.Nodata.Lable=No Ancillary Revenue Stream Inputs have been configured.
GroupPricing.AncillaryRevenue.AR.DataEntry.error=Ancillary Revenue Tab: Revenue per Room Night value should be a positive decimal number.
GroupPricing.AncillaryRevenue.AStream.Database.update.duplicaterow.error=Ancillary Revenue Stream with same Input Name already exists
GroupPricing.AncillaryRevenue.AStream.Database.update.error=Error updating Ancillary Revenue Stream information
GroupPricing.AncillaryRevenue.CNB.Database.Load.Error=Conference and Banquet database load error
GroupPricing.AncillaryRevenue.CnB.DataEntry1.error=Conference and Banquet Revenue Tab: Revenue per Group value should be a number and cannot be negative.
GroupPricing.AncillaryRevenue.CnB.DataEntry2.error=Conference and Banquet Revenue Tab: Profit Margin % value should be a number and should be between 0 and 100.
GroupPricing.AncillaryRevenue.CNB.Nodata.Lable=No Conference and Banquet Revenue Stream Inputs have been configured.
GroupPricing.AncillaryRevenue.CnB.prompt=Conference and Banquet
GroupPricing.AncillaryRevenue.CnB.YellowShade.prompt=The Conference and Banquet Revenue Stream Input(s) shaded yellow have been deleted from the Input list in System Tools.
GroupPricing.AncillaryRevenue.CnBCommission.Format.error=Conference and Banquet Revenue Tab: Conference and Banquet Commission value should be a decimal number.
GroupPricing.AncillaryRevenue.CnBCommission.prompt=Conference and Banquet Commission
GroupPricing.AncillaryRevenue.CnBCommission.Value.error=Conference and Banquet Revenue Tab: Conference and Banquet Commission value should not be less than 0 and it should not exceed 100.
GroupPricing.AncillaryRevenue.CnBRevenue.prompt=Conference and Banquet Revenue
GroupPricing.AncillaryRevenue.CnbStream.Database.update.duplicaterow.error=Conference and Banquet Stream with same name already exists
GroupPricing.AncillaryRevenue.CnbStream.Database.update.error=Error updating Conference and Banquet Stream information
GroupPricing.AncillaryRevenue.CompDiscSum.error={0}{1}The sum total of Complimentary, Discounted Room Nights, Other Concessions and Rebates must be equal to or less than Total Room Nights.
GroupPricing.AncillaryRevenue.ComplimentaryRoomNights.Format.error=Costs Tab: Complimentary Room Nights field(s) must be a positive (non-zero) number. Please enter numbers only without commas.
GroupPricing.AncillaryRevenue.ComplimentaryRoomNights.minusValue.error=Costs Tab: Complimentary Room Nights field(s) should not be negative.
GroupPricing.AncillaryRevenue.ComplimentaryRoomNights.Value.error=Costs Tab: Complimentary Room Nights value of the field prior to "in" clause should be less than or equal to the one after.
GroupPricing.AncillaryRevenue.Costs.prompt=Costs
GroupPricing.AncillaryRevenue.currencyPerRoomDiscount.Format.error=Costs Tab: Per Room Discount currency value should be a decimal number.
GroupPricing.AncillaryRevenue.currencyPerRoomDiscount.Value.error=Costs Tab: Per Room Discount currency value should not less than 0.
GroupPricing.AncillaryRevenue.DefaultValuesMSG.prompt=Default values for assignments for Market Segment Group
GroupPricing.AncillaryRevenue.DiscountedRoomNights.Format.error=Costs Tab: Discounted Room Nights field(s) must be positive (non-zero) number(s). Please enter numbers only without commas.
GroupPricing.AncillaryRevenue.DiscountedRoomNights.minusValue.error=Costs Tab: Discounted Room Nights field(s) should not be negative.
GroupPricing.AncillaryRevenue.DiscountedRoomNights.Value.error=Costs Tab: Discounted Room Nights value of the field prior to "in" clause should be less than or equal to the one after.
GroupPricing.AncillaryRevenue.entireStayARev.prompt=Total for entire stay - Ancillary Revenue
GroupPricing.AncillaryRevenue.entireStayARevProf.prompt=Total for entire stay - Ancillary Revenue Profit
GroupPricing.AncillaryRevenue.entireStayNetCnBRev.prompt=Total Conference and Banquet Revenue after Commission
GroupPricing.AncillaryRevenue.GroupRevenue.prompt=Group Revenue
GroupPricing.AncillaryRevenue.in.prompt=in
GroupPricing.AncillaryRevenue.percentPerRoomDiscount.Format.error=Costs Tab: Per Room Discount percent value should be a decimal number.
GroupPricing.AncillaryRevenue.percentPerRoomDiscount.Value.error=Costs Tab: Per Room Discount percent value should not be less than 0 and it should not exceed 100.
GroupPricing.AncillaryRevenue.Profit.Lable=Profit
GroupPricing.AncillaryRevenue.roomCommission.Format.error=Costs Tab: Room Commission value should be a decimal number.
GroupPricing.AncillaryRevenue.RoomCommission.prompt=Room Commission
GroupPricing.AncillaryRevenue.roomCommission.Value.error=Costs Tab: Room Commission value should not be less than 0 and it should not exceed 100.
GroupPricing.AncillaryRevenue.RoomsRequirements.prompt=Rooms Requirements
GroupPricing.AncillaryRevenue.ShowDefaultValues.prompt=Show Default Values
GroupPricing.AncillaryRevenue.YellowShade.prompt=The Ancillary Revenue Stream Input(s) shaded yellow have been deleted from the Input list in System Tools.
GroupPricing.AncillaryRevenueAssignment.Database.load.error=Ancillary Revenue database error
GroupPricing.AncillaryRevenueAssignment.Database.save.error=Ancillary Revenue database save error
GroupPricing.AncillaryRevenueAssignments.Alert.Value=Revenue per Room Night value should be a positive number
GroupPricing.AncillaryRevenueAssignments.NoData.Label=No data available.
GroupPricing.AncillaryRevenueAssignments.Table.Label=Assignments for Market Segment Group -
GroupPricing.AncillaryRevenueAssignments.TPM.Label=Total Profit Margin %
GroupPricing.AncillaryRevenueAssignments.TPPRN.Label=Total Profit per Room Night
GroupPricing.AncillaryRevenueAssignments.TRPRN.Label=Total Revenue per Room Night
GroupPricing.AncillaryRevenueAssignments.Ungrouped.error=Unassigned Market Segments have been detected. Please assign them to appropriate Market Segment Groups before configuring Ancillary Revenue Assignments.
GroupPricing.Evaluation.Add.MinimumTwoLicense.error=Group Pricing license for minimum of two properties required.
GroupPricing.Evaluation.Add.MinimumTwoProperties.error=Please select two or more properties.
GroupPricing.Evaluation.Alternate.Arrival.error=Please choose alternate arrival dates.
GroupPricing.Evaluation.AlternatePreferred.Arrival.error=Please choose an alternate result.
GroupPricing.Evaluation.AncillaryRevenue.Title=Ancillary Revenue Details
GroupPricing.Evaluation.AncillarySpend.AncillaryRevperRoomNight.prompt=Ancillary Revenue per Room Night
GroupPricing.Evaluation.AncillarySpend.CommissionsRebates.error=Please enter a valid number for Ancillary Spend - Commissions / Rebates.
GroupPricing.Evaluation.AncillarySpend.CommissionsRebates.prompt=Commissions / Rebates
GroupPricing.Evaluation.AncillarySpend.FoodnBeverage.error=Please enter a valid number for Ancillary Spend - Food & Beverage.
GroupPricing.Evaluation.AncillarySpend.MeetingRoomRentals.error=Please enter a valid number for Ancillary Spend - Meeting Room Rentals.
GroupPricing.Evaluation.AncillarySpend.NetAncillarySpend.prompt=Net Ancillary Spend
GroupPricing.Evaluation.AncillarySpend.OtherRevenue.error=Please enter a valid number for Ancillary Spend - Other Revenue.
GroupPricing.Evaluation.AncillarySpend.prompt=Ancillary Spend
GroupPricing.Evaluation.AncillarySpend.TotalAncillarySpend.prompt=Total Ancillary Spend
GroupPricing.Evaluation.AncillarySpendDetails.prompt=Ancillary Spend Details
GroupPricing.Evaluation.Arrival.Preferred.prompt=Preferred
GroupPricing.Evaluation.Arrival.prompt=Arrival
GroupPricing.Evaluation.ArrivalDate.Value.error={0}{1}Arrival Date must be between {2} and {3}.
GroupPricing.Evaluation.ArrivalDates.Assigned.prompt=Arrival Date selected
GroupPricing.Evaluation.ArrivalDates.NotAssigned.error=At least one arrival date is required.
GroupPricing.Evaluation.ArrivalDates.Preference.error=Preference for arrival date is required.
GroupPricing.Evaluation.AverageGroupSize.Range.error=Group size cannot be greater than the capacity.
GroupPricing.Evaluation.AverageGroupSize.Required.error=Rooms must be a positive (non-zero) number not less than {0}. Please enter numbers only without commas.
GroupPricing.Evaluation.AverageRoomNights.prompt=Average Room Nights
GroupPricing.Evaluation.Controls.Evaluate.prompt=Evaluate
GroupPricing.Evaluation.Controls.NewEvaluation.prompt=New Evaluation
GroupPricing.Evaluation.Controls.Reevaluate.prompt=Re-evaluate
GroupPricing.Evaluation.Database.delete.error=Error deleting Group Evaluation.
GroupPricing.Evaluation.Database.load.error=Error loading group pricing information
GroupPricing.Evaluation.Database.update.duplicaterow.error=This group evaluation already exists.
GroupPricing.Evaluation.DataCorrupt.Fatal.error=Error in fetching evaluation results. Please try again.
GroupPricing.Evaluation.Departure.Date.prompt=Departure Date
GroupPricing.Evaluation.Departure.prompt=Departure
GroupPricing.Evaluation.DepartureDate.Value.error={0}{1}Group Pricing evaluations are possible for stay periods not exceeding {2}.
GroupPricing.Evaluation.Details.Graph.Prompt=Graph
GroupPricing.Evaluation.Details.Graph.Title=Details for
GroupPricing.Evaluation.Details.NotAvailable.error=The evaluation results have already been saved or discarded. Please look at the list of evaluations. This window will close in 3 seconds.
GroupPricing.Evaluation.Details.Post-stayRooms.Displaced=Post-stay: Rooms Displaced
GroupPricing.Evaluation.Details.Pre-stayRooms.Displaced=Pre-stay: Rooms Displaced
GroupPricing.Evaluation.Details.Rooms.Books=On Books - Total
GroupPricing.Evaluation.Details.Rooms.Displaced=Rooms Displaced
GroupPricing.Evaluation.Details.Rooms.Requested=Rooms Requested by Group
GroupPricing.Evaluation.Details.Rooms.Stay.Totals=Stay Totals
GroupPricing.Evaluation.extraCnBstreamName.error=Conference and Banquet Revenue Tab: Provide name for the extra Conference and Banquet Revenue Stream Input.
GroupPricing.Evaluation.FollowUpDate.prompt=Follow Up Date
GroupPricing.Evaluation.FollowupDate.Range.error={0}{1}Follow up date must be greater than system date {2}.
GroupPricing.Evaluation.FollowupDate.Value.error=Wrong date format for follow up date.
GroupPricing.Evaluation.GroupName.size.warning=The Group Name field exceeds the maximum of 40 characters allowed. The first 40 characters of the Group Name will be accepted.
GroupPricing.Evaluation.GroupSizeOverLOS.NotAssigned.error=Group Size for each day of stay is required
GroupPricing.Evaluation.GroupSizeOverLOS.Value.AtleastOneGreaterThanMinimumSize.error=Group size for at least one day must be greater than or equal to {0}.
GroupPricing.Evaluation.GroupSizeOverLOS.Value.error={0}{1}Group Size for each day of stay must be a positive number between {2} and {3}. Please enter numbers only without commas.
GroupPricing.Evaluation.help=Price a group stay request
GroupPricing.Evaluation.IncludeExclude.CurrentGrpDetails.prompt=Current Group Details
GroupPricing.Evaluation.IncludeExclude.CurrentRooms.prompt=Rooms(A)
GroupPricing.Evaluation.IncludeExclude.DefiniteGrp.prompt=Definite Groups
GroupPricing.Evaluation.IncludeExclude.ExcludeFrmOnBooks.prompt=Exclude from on Books:(C)
GroupPricing.Evaluation.IncludeExclude.ExcludFromOnBooks.error=Exclude from On Books field value should be a whole number greater than or equal to zero.Also, it should be less than Hotel On Books for respective day of stay.
GroupPricing.Evaluation.IncludeExclude.HotelOnBooks.prompt=Hotel on Books
GroupPricing.Evaluation.IncludeExclude.IETab.prompt=Include/Exclude Groups
GroupPricing.Evaluation.IncludeExclude.LoadingMessage.prompt=Loading data for evaluating include/exclude scenarios
GroupPricing.Evaluation.IncludeExclude.MappedLOS.prompt=Please enter Group Size on Rooms Requirements Tab
GroupPricing.Evaluation.IncludeExclude.MaxGrpSize.prompt=Maximum Group Size
GroupPricing.Evaluation.IncludeExclude.NoIEData.message=No Include/Exclude data is available for this Group.
GroupPricing.Evaluation.IncludeExclude.PreExtGrpDetials.prompt=Pre-Existing Group Details
GroupPricing.Evaluation.IncludeExclude.TotalGrp.prompt=Total Groups
GroupPricing.Evaluation.IncludeExclude.TotalGrpIncluded.prompt=Group Evaluation Rooms Included (B)
GroupPricing.Evaluation.IncludeExclude.TotalIE.prompt=Total: (A+B-C)
GroupPricing.Evaluation.LOS.Required.error=Length of Stay must be a positive (non-zero) number. Please enter numbers only without commas.
GroupPricing.Evaluation.MarketSegments.load.error=Group Market Segment data not available.
GroupPricing.Evaluation.Message.ConfirmDelete.prompt=This action will delete all the details of the selected evaluations.
GroupPricing.Evaluation.Message.Evaluating.Wait.prompt=Evaluating..... Please wait.
GroupPricing.Evaluation.Message.NoEvaluationDetails.prompt=No evaluation details.
GroupPricing.Evaluation.Message.NoEvaluationResults.prompt=No evaluation results
GroupPricing.Evaluation.MP_AverageGroupSize.Range.error={0}{1}Group size cannot be greater than the capacity of {2}.
GroupPricing.Evaluation.Name.Required.error=Group Name is required
GroupPricing.Evaluation.NoData.prompt=No evaluations have been done
GroupPricing.Evaluation.NoGroupRvaluation.message=No Group Pricing evaluations have been performed.
GroupPricing.Evaluation.RateCode.Details.prompt=Group Rate Code Details
GroupPricing.Evaluation.RateCodeName.prompt=Rate Code Name
GroupPricing.Evaluation.RecommendedProperty.prompt=Recommended Property
GroupPricing.Evaluation.Results.ADRTooLow.error=Caution-This is not a suggested rate as the ADR achieved is too low as compared to the group rates defined.
GroupPricing.Evaluation.Results.InvalidInput.error=Invalid input
GroupPricing.Evaluation.Results.NoCapacity.error=No capacity to accommodate this group
GroupPricing.Evaluation.Results.NoRates.error=Group Rates must be set up before performing Group Evaluations. Please set up Groups Rates under System Tools or ask a System Administrator to do so.
GroupPricing.Evaluation.Results.NotSufficientRates.error=At least two rates need to be configured for a group size and date.
GroupPricing.Evaluation.Results.TooLowRates.error=Caution - This is not a suggested rate as the Displacement Assessment is greater than the Value Assessment.
GroupPricing.Evaluation.SelectProperty.prompt=Select Property
GroupPricing.Evaluation.Status.1.prompt=Contract Issued
GroupPricing.Evaluation.Status.3.prompt=Scenario
GroupPricing.Evaluation.title=Evaluation
GroupPricing.RateCode.Configure.NoGroupRateCode.message=No Group Rate code details have been configured.
GroupPricing.RateCode.Configure.NoGroupRateCodeDetails.message=No Restriction level details have been configured.
GroupPricing.RateCode.Database.update.duplicaterow.error=Rate Code with same name already exists
GroupPricing.RateCode.Date.Range.error=Date range overlaps with that of (one or more) existing entries for this rate code.
GroupPricing.RateCode.help=Tell us about the different group rates you have configured.
GroupPricing.RateCode.MaximumSize.prompt=Maximum Size
GroupPricing.RateCode.Message.ConfirmDelete.prompt=This action will delete all the details of the selected rate codes.
GroupPricing.RateCode.MinimumSize.prompt=Minimum Size
GroupPricing.RateCode.Size.GapNext.prompt=Info: Size gap with next entry
GroupPricing.RateCode.Size.GapPrev.prompt=Info: Size gap with previous entry
GroupPricing.RateCode.Size.GapPrevAndNext.prompt=Info: Size gap with previous and next entries
GroupPricing.RateCode.Size.Max.Range.error=The upper bound of the size range must be greater than the lower bound.
GroupPricing.RateCode.Size.Range.error=Size must be a positive (non-zero) number between {0} and {1}. Please enter numbers only without commas.
GroupPricing.RateCode.Size.Range.Overlap.error=Size range overlaps with that of (one or more) existing entries for this rate code.
GroupPricing.RetrievedGroups.title=Retrieved Group Evaluations
GroupPricing.ViewEvaluation.constituentProperties.prompt=Properties Evaluated
GroupPricing.ViewEvaluation.Details.prompt=Group Evaluation Details
GroupPricing.ViewIncludeExclude.Details.prompt=Include/Exclude Details
GroupPricing.Evaluation.Delphi.Single.Property.Eval.Present.warning=A Single-Property Group Evaluation is already present for {0}.
GroupPricing.Evaluation.Delphi.Multi.Property.Eval.Present.warning=A Multi-Property Group Evaluation is already present for {0}.
GroupPricing.Evaluation.BonusCnBProfitMarginRevenue.NoValue.Error={0}{1}Conference and Banquet Revenue Tab: Revenue per Group and Profit Margin % can not be blank.
GroupPricing.Evaluation.SalesPerson.Size.Warning=The Sales Person field exceeds the maximum of 40 characters allowed. The first 40 characters of the Sales Person will be accepted.
GroupPricing.GroupRateCode.Rate.Range.error=Rate value must be a positive (non-zero) number up to 2 decimal. Please enter numbers only without commas.
GroupPricing.Evaluation.Notes.Size.Warning=The Notes field exceeds the maximum of 255 characters allowed. The first 255 characters of the Notes will be accepted.
Delphi.GroupPricing.Errors.Header=Errors:
Delphi.GroupPricing.System.Error=System Error
Delphi.GroupPricing.Access.Denied=Access to Delphi Group Pricing module is denied.
GroupPricing.Evaluation.MinimumTwoLicense.error=New evaluation operation requires at least two properties in the Multi-Property view, {0}.
GroupPricing.QuotedRate.EvaluationType2.Prompt=Quoted Rate Evaluation:
GroupPricing.QuotedRate.EvaluationType1.Prompt=IDeaS Evaluation:
GroupPricing.QuotedRate.QuotedRateName.Prompt=Quoted Rate
GroupPricing.QuotedRate.Range.Error=Quoted Rate value must be a positive (non-zero) number up to 2 decimal. Please enter numbers only without commas.
GroupPricing.QuotedRate.Range.UpperBound.Error=Quoted Rate value cannot be greater than 9999999
GroupPricing.QuotedRate.TypeOfEvaluation.Error=Please select either Quoted or IDeaS Type Of Evaluation or both.
GroupPricing.QuotedRate.TypeOfEvaluation.Error1=Please select Quoted Rate Evaluation.
GroupPricing.Evaluation.Filterto.prompt=To:
GroupPricing.Evaluation.ArrivalDateFrom.prompt=Arrival Date From:
GroupPricing.Evaluation.EvaluatedFrom.prompt=Evaluated From:
GroupPricing.Evaluation.FollowUpDateFrom.prompt=Follow Up Date From:
GroupPricing.Evaluation.SaveDefaultFilter.prompt=Save Default Filter
GroupPricing.Evaluation.NoEvaluationsMeetMessage.prompt=No Group Pricing evaluations meet the filter criteria.
GroupPricing.Evaluation.UnSavedEvaluation.Warning=You have unsaved changes with the group evaluation. If you leave this page, those changes will be lost.
GroupPricing.AncillaryRevenue.OtherConcessions.Format.error=Costs Tab: Other Concessions field(s) must be positive (non-zero) number(s). Please enter numbers only without commas.
GroupPricing.AncillaryRevenue.OtherConcessions.minusValue.error=Costs Tab: Other Concessions field(s) should not be negative.
GroupPricing.AncillaryRevenue.OtherConcessions.Value.error=Costs Tab: Other Concessions value of the field prior to "in" clause should be less than or equal to the one after.
GroupPricing.AncillaryRevenue.Rebates.Format.error=Costs Tab: Rebates field(s) must be positive (non-zero) number(s). Please enter numbers only without commas.
GroupPricing.AncillaryRevenue.Rebates.minusValue.error=Costs Tab: Rebates field(s) should not be negative.
GroupPricing.AncillaryRevenue.Rebates.Value.error=Costs Tab: Rebates value of the field prior to "in" clause should be less than or equal to the one after.
GroupPricing.AncillaryRevenue.OtherConcessions.defaultEntry=enter text...
GroupPricing.AncillaryRevenue.OtherConcessions.description.sizeLimitExceeded=The maximum number of characters allowed is 100.
GroupPricing.AncillaryRevenue.OtherConcessions.description.sizeLimit=Max 100 characters
GroupPricing.AncillaryRevenue.OtherConcessions.description.blank=Costs Tab: Other Concessions Description cannot be left blank OR same as the default entry.
GroupPricing.AncillaryRevenue.OtherConcessions.description.count=You have already entered the maximum number of Other Concessions allowed (5).
GroupPricing.AncillaryRevenue.AncillaryRevenueStream.FunctionRoomRental=Function Room Rental