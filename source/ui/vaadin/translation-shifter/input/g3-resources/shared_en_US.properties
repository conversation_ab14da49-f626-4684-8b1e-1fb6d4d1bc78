????#Common
common.dummyKey=Hello IDeaS
common.help=Help
common.copy=Copy
common.none=None
common.at=at
common.ok=Ok
common.save=Save
common.apply=Apply
common.cancel=Cancel
common.delete=Delete
common.add=Add
common.yes=Yes
common.no=No
common.not.available=NA
common.value=Value
common.information=Info
common.report=Report
common.reports=Reports
common.category=Category
common.min=Min
common.max=Max
common.problem=Problem
common.average=Average
common.changesSaved=Changes have been saved.
common.changes.cancel=Changes have been cancelled.
common.changes.none=There were no changes.
common.changes.delete=Deletion successful
common.invalidDataLink=Invalid or required field exists: Click to View
common.block=Block
common.pickup=Pickup
common.header.availableBlock=Available\nBlock
common.header.occupancyDate=Occupancy\nDate
common.header.expirationDate=Expiration\nDate
common.header.systemWash=System\nWash
common.header.systemWashPercent=System\nWash %
common.minLos=Min LOS
common.maxLos=Max LOS
common.avgLos=Avg LOS
common.los=LOS
common.dow=DOW
common.fieldIsRequired=This field is required
common.numberIsRequired=Number is required
common.date.validation.parseError=Please provide a valid date format ({0}).
common.date.validation.rangeErrorMessage=The selected date, {0}, must be between {1} and {2}.
common.date.validation.rangeErrorMessageStartDateOnly=The start date must be before the end date.
common.date.validation.rangeErrorMessageEndDateOnly=The end date must be after the start date.
common.date.validation.endBeforeStart=End date cannot be before start date.
common.date.validation.startAfterEnd=Start date cannot be after end date.
common.date.start=Start Date
common.date.end=End Date
common.date=Date
common.date.range={0} to {1}
common.from=From
common.to=To
common.specialEventsForDate=Special events for {0}
common.stayDates=Stay Dates
common.cutoffDate=Cutoff Date
common.status=Status
common.salesManager=Sales Manager
common.occupancyDate=Occupancy Date
common.groupName=Group Name
common.roleName=Role Name
common.evaluated.on=Evaluated On
common.marketSegment=Market Segment
common.processGroup=Process Group
common.forecastTask=Forecast Task
common.forecastGroup=Forecast Group
common.roomClass=Room Class
common.specialEvent=Special Event
common.systemDate=System Date
common.seasonGroup=Season Group
common.seasonGroupNumber=Season Group Number
common.dowGroupNumber=DOW Group Number
common.losGroupNumber=LOS Group Number
common.horizonGroupNumber=Horizon Group Number
common.toDTA=To DTA
common.fromDTA=From DTA
common.propertyForecastParameter=Property FCST Parameter
common.dowGroup=DOW Group
common.losGroup=LOS Group
common.horizonGroup=Horizon Group
common.readingHorizonGroup=Reading Horizon Group
common.daysToArrival=Days to Arrival
common.daysToArrivalNotFound=No Days to Arrival ranges found
common.email.validation=Please enter valid email address
common.propertyName=Property Name
common.property.id=Property ID
common.propertyCode=Property Code
common.timeZone=Time Zone
common.propertyCode.header=Property \nCode
common.clientCode.header=Client \nCode
common.client.id=Client ID
common.clientCode=Client Code
common.clientName=Client Name
common.updatedBy=Updated By
common.updatedOn=Updated On
common.update=Update
common.module=Module
common.notes=Notes
common.open=Open
common.closed=Closed
common.details=Details
common.edit=Edit
common.clear=Clear
common.close=Close
common.jobName=Job Name
common.jobStep=Job Step
common.job.log=Job Log
common.stepName=Step Name
common.client=Client
common.property=Property
common.parameter=Parameter
common.action=Actions
common.closedOn=Closed On
common.closedBy=Closed By
common.errorCode=Error Code
common.all=All
common.current=Current
common.unclaimed=Unclaimed
common.claimed=Claimed
common.stage=Stage
common.duration=Duration
common.resume=Resume
common.resumeall=Resume all
common.start=Start
common.pause=Pause
common.stop=Stop
common.stopall=Stop all
common.abandon=Abandon
common.continue=Continue
common.assign=Assign
common.propertyStage.header=Property \n Stage
common.description=Description
common.creationDate=Creation Date
common.downloadAttachment=Download attachment
common.confirmation=Confirmation
common.deleteRow=Delete the selected row?
common.refresh=Refresh
common.overdue=Overdue
common.timeout=Timeout
common.arrivalDate=Arrival Date
common.ms=MS
common.sales.person=Sales Person
common.materialization=Materialization
common.plural.sales.persons=Sales Person(s):
common.enter.name=Enter name
common.numberOfDaysOfPace=Days of Pace
common.competitors=Competitors
common.type=Type
common.roomsSold=Rooms Sold
common.count=Count
common.percent=Percent
common.generate=Generate
common.startDate=Start Date
common.endDate=End Date
common.lastUpdatedBy=Last Updated By
common.lastUpdatedOn=Last Updated On
common.total=Total
common.roomType=Room Type
common.group=Group
common.transient=Transient
common.userName=User Name
common.for=for
common.level=Level
common.select=Select
common.nameValueFormat=<b>{0} </b>: {1}
common.lastName=Last Name
common.firstName=First Name
common.userStatus=User Status
common.email=Email
common.lastLogin=Last Login
common.properties=Properties
common.authorizationGroups=Authorization Groups
common.userRoles=User Roles
common.userNames=User Names
common.find=Find...
common.colon=:
common.lastAccessed=Last Accessed
common.role=Role
common.authorizationGroup=Authorization Group
common.individualProperties=Individual Properties
common.filterByPropertyCode=Filter by property code...
common.search=Search
common.reset=Reset
common.run=Run
common.deprecated.marker=(Dead)
common.validation.integer=Must be integer value
common.validation.integer.range=Must be between {0} and {1}
common.validation.url=Must be valid url value
common.sasServerName=SAS Server Name
common.checked=Checked
common.signOut=Sign Out
common.price=Price
common.analysis=Analysis
common.comparison=Comparison
common.rooms=Rooms
common.catchup.mode=Catchup Mode
common.button.next=Next >
common.button.previous=< Previous
common.filter=Filter
common.warning.noPropertyInFilter=No properties available with this filter
common.warning.nothingToResetInFilter=Nothing to reset in the filter.
common.warning.wantToResetFilter=Do you want to reset the filter?
common.error.msg.duplicateNotAllowed=Duplicate {0} not allowed.
common.filter.properties.available=Properties Available
common.filter.properties.selected=Properties Selected
common.resetFilter=Reset Filter
common.unsavedchanges=Unsaved Changes
common.unsaved.changes.desc=You have made changes to the {0}, do you want to discard your changes?
common.current.group=current group
common.current.role=current role
common.nodatafound=No data found.
common.isrequired=is required
common.fieldrequired=Field is required
common.makethisfilterasrule=Make this filter a rule
common.filterProperty.message=Add valid filter criteria to filter properties.
common.profit=Profit %
common.reviewselection=Please review and correct your selections.
common.maxcharlimit.message=Max 140 characters
common.revenuePerRoomNight=Revenue per Room Night
common.ancillary=Ancillary
common.remove=Remove
common.expand=Expand
common.collapse=Collapse
common.filter.results=Filter results
common.time.machine=Time Machine
common.export.to.excel=Export to Excel
common.performance.metrics=Performance Metrics
common.addFilterCriteriaError.message=Adding filter criteria is disabled, until you select all possible values.
common.yearRound=Year-round
common.dta=DTA
common.occupancyForecast=Occupancy Forecast
common.date.range.text=Date Range
common.between=Between
common.and=and
common.groups=Groups
common.daily.status=Daily Status
common.blocks=Blocks
common.avail.block=Avail Block
common.occ.fcst=Occ FCST
common.tran.block=Tran Block
common.transient.block=Transient Block
common.non.block=Non-Block
common.total.hotel=Total Hotel
common.on.books.percentage=On Books %
common.on.books.percent=On Books Percent
common.room.ooo=Room OOO
common.room.out.of.order=Room Out of Order
common.ovbk=OVBK
common.occupancy.forecast.value=Occupancy Forecast Value
common.occ.fcst.percentage=Occ FCST %
common.occupancy.forecast.percent=Occupancy Forecast Percent
common.by.occupancy.date=By Occupancy Date
common.null.representation=N/A
common.occupancy.forecast=Occupancy\nForecast
common.reporttype=Report Type
common.reportstyle=Report Style
common.full=Full
common.differential=Differential
common.min.los=MinLOS
common.fp.los=FPLOS
common.changes.since=Changes Since
common.lrv=LRV
common.restriction.code=Restriction Code
common.rate.value=Rate Value
common.percentage=%
common.changed=Changed
common.permission.button.disabled.msg=DISABLED
common.permission.button.propertyreadonly.msg=Cannot be used while BDE, IDP or sync is running.
common.permission.button.propertysync.msg=Cannot be used while sync is pending.
common.permission.button.unauthorized.msg=You do not have the necessary permission.
common.readWrite=Read/Write
common.readOnly=Read Only
common.noAccess=No Access
common.xAxis=X Axis
common.values=Values
common.nationality=Nationality
common.source.booking=Source Booking
common.channel=Channel
common.room.type.code=Room Type Code
common.channels=Channels
common.nationalities=Nationalities
common.source.bookings=Source Bookings
common.rate.codes=Rate Codes
common.charts=Charts
common.line.chart=Line Chart
common.column.chart=Column Chart
common.bar.chart=Bar Chart
common.days.to.arrival=Days To Arrival
common.error.message.integer.range.0.to.365=Please enter a value between 0 and 365
common.unknown.failure=Internal server error has occurred.
common.reservation.id=Reservation ID
common.individual.status=Individual Status
common.departure.date=Departure Date
common.booking.date=Booking Date
common.cancellation.date=Cancellation Date
common.booked.accom.type.code=Booked Accom Type Code
common.market.segment.code=Market Segment Code
common.room.number=Room Number
common.booking.type=Booking Type
common.number.adults=Number Adults
common.number.children=Number Children
common.create.date=Create Date
common.confirmation.number=Confirmation No.
common.trans=Trans
common.overbooking=Overbooking
common.costofwalk=Cost of Walk
common.comparisonDateLastYear=Comparison Date Last Year
common.thisYear=This Year
common.lastYearActual=Last Year Actual
common.arrivals=Arrivals
common.departures=Departures
common.hotel=Hotel
common.capacity=Capacity
common.systemWashPercentage=System Wash %
common.userWashPercentage=User Wash %
common.businessView=Business View
common.allPermission=All Permission


Demand.Occupancy.Date=Demand - Occupancy Date
Demand.Arrival.By.LOS=Demand - Arrival By LOS
common.wash=Wash

report.NoDataAvailableForFilterCriteria=No data is available for the selected filter criteria.
report.noLimit=No Limit
report.outputoverride_ovbk=Output (OVBK) Override Report
report.GeneratedOn=Generated On
report.ReportCriteria=Report Criteria
report.decision=Decision
report.old=Old
report.new=New
report.NoBAR=NoBAR
report.BAROverride=BAR Override

#These are dynamically constructed
#suppress inspection "UnusedProperty"
common.pickup_type_code.reservation_control=Reservation control
#suppress inspection "UnusedProperty"
common.pickup_type_code.conventional_block=Conventional block
#suppress inspection "UnusedProperty"
common.pickup_type_code.rooming_list=Rooming list
#suppress inspection "UnusedProperty"
common.pickup_type_code.callin_list=Call-in list
common.error.msg.duplicate=The {0} {1} already exists. Please select another {2}.
common.error.msg.validInput=Please enter valid {0}
common.error.msg.nochangesForSubmit=You have not made any changes, so nothing was submitted.
common.warning.msg.percentage.validInput=Please enter a percent value larger than 0 and less than or equal to 100, not {0}.
common.warning.msg.duplicate.name=The name {0} already exists. Please select another name.
common.warning.msg.unsaved.changes.before.delete=Please save or cancel the unsaved row before deleting an item.
common.warning.msg.unsaved.changes.before.edit=Please save or cancel the unsaved row before editing an item.
common.warning.msg.unsaved.changes.before.add=Please save or cancel the unsaved row before adding a new item.
common.warning.delete.message=Do you want to delete this group?
common.group.wash.restricted=Group wash override restricted as this group belongs to non-block business
common.group.wash.override.applied=A Wash Override has been applied at the Forecast Group and/or Individual Group level on this date.
common.wash.override.forecast.group=A Wash Override has been applied at the Forecast Group level on this date.
common.wash.override.individual.group.date=A Wash Override has been applied at the Individual Group level on this date.
common.wash.override.individual.group=A Wash Override has been applied at the Individual Group level.
common.special.event.exist.date=One or more special events exist on this date.

#Common Season Specific
common.season.addSeason=Add Season
common.season.applySeason=Apply Season Changes
common.season.deleteSeason=Delete season ({0})?
common.season.mergeConfirmation=This date range will split or merge an existing season. Are you sure you want to continue?
common.season.editModeSaveWarning=Save is disabled, until you apply or cancel the season entry you are editing.
common.season.editModeEditWarning=Editing this season is disabled, until you apply or cancel the other entry you are editing.
common.season.editModeCollapseWarning=Collapse is disabled, until you apply or cancel this entry.
common.season.editModeAddWarning=Adding a new season is disabled, until you apply or cancel the other entry you are editing.
common.season.deleteSeason.title=Delete season
common.season.deleteRate=Are you sure you want to delete this Rate Plan?
common.days=Days
common.LowerCase.to=to
common.wholenumber.validation=Please enter a valid whole number.
common.confirmNavigation=Confirm Navigation
common.unsavedChanges=There are unsaved changes. Are you sure you want to leave this page?
common.leavePage=Leave this Page
common.stayOnPage=Stay on this Page
common.number.validation=Please enter a valid number.
common.role.management=Role Management
common.total.revenue=Total Revenue
common.arrivals.last.year=Arrivals Last Year
common.cancellations.last.year=Cancellations Last Year
common.departures.last.year=Departures Last Year
common.room.revenue.last.year=Room Revenue Last Year
common.room.sold.last.year=Rooms Sold Last Year
common.total.revenue.last.year=Total Revenue Last Year
common.room.revenue.avg=Room Revenue Avg
common.room.revenue.last.year.avg=Room Revenue Last Year Avg
common.total.revenue.avg=Total Revenue Avg
common.total.revenue.last.year.avg=Total Revenue Last Year Avg
common.transactions.for=Transactions for

#These are dynamically constructed
#suppress inspection "UnusedProperty"
competitionDisplay.medianPrice=Median Price
#suppress inspection "UnusedProperty"
competitionDisplay.highestPrice=Highest Price
#suppress inspection "UnusedProperty"
competitionDisplay.specificCompetitor=Specific Competitor
#suppress inspection "UnusedProperty"
competitionDisplay.lowestPrice=Lowest Price

#Modules
roa.overrides=Overrides
roa.overrides.invalid.min.max.date=Invalid Min/Max date
roa.overrides.selectdow=Select Days of the Week
roa.report.success=You will receive an email with your generated reports shortly.
roa.report.in.progress=A report is already in progress of being generated for the selected property. Try again later.
roa.reports.include.pdf.output=Include PDF Output
roa.overrides.runtimeForecastParams.validation=Value must be a number.
roa.overrides.runtimeForecastParams.validation.FCST_HISTORY_LENGTH_VALUE=Value must be an integer equal to or greater than 1
roa.overrides.runtimeForecastParams.success=Your override has been saved successfully.
roa.overrides.specialEvents.excludedFlag=Exclude Flag
roa.overrides.rateforecasting.addoverride=Add Override
roa.overrides.rateforecasting.overrideRateValue=Override Rate Value
roa.overrides.rateforecasting.existingOverrides= Existing Rate Forecast Overrides
roa.overrides.rateforecasting.rate=Rate
roa.overrides.rateforecasting.dowrequiredmessage=At least one day should be selected
roa.overrides.rateforecasting.ratevalidation=Rate cannot be less than 0
roa.overrides.reference.price=Override Reference Price
roa.overrides.reference.price.validation=Reference Price cannot be less than or equal to 0
roa.overrides.referenceprice.existing.override.label=Existing Reference Price Overrides
roa.overrides.referenceprice.add=Add Override
roa.overrides.referenceprice.price=Reference Price
roa.overrides.referenceprice.horizon.group.number=Horizon Group Number
roa.overrides.seasonGroupDetails=Season Group Details
roa.overrides.dowGroupDetails=DOW Group Details
roa.overrides.losGroupDetails=LOS Group Details
roa.overrides.horizonGroupDetails=Horizon Group Details
roa.overrides.processGroups.success=Process Group Configuration was saved successfully.
roa.processgroup.current.override=Current Override
roa.processgroup.default.configuration=Default Configuration
roa.processgroup.override=Process Group Override
roa.overrides.property.attributes.default.value=Default value is {0}
roa.overrides.noshow.add.no.show.override=Add Override
roa.overrides.noshow.overrideValue=Override No show Value
roa.overrides.noshow.existing.overrides=Existing Overrides
roa.overrides.noshow.value.validation.message=Value must be between {0} and {1}
roa.overrides.noshow.no.shows=No Shows
roa.overrides.attribute.to.override=Attribute to Override
roa.overrides.attribute.value=Value
roa.overrides.seasongroups.validationMessage=Season groups must be continuous, non overlapping and defined for at least a year.
roa.overrides.client.disclaimer=Update multiple properties for client (overrides page selection)
roa.overrides.prop.codes.disclaimer=If no property codes specified, all properties for client affected.
roa.overrides.read.only.props.skipped=The following properties were skipped since they are in read only state: {0}

#Group Pricing
groupPricing.revenueStream=Revenue Stream
groupPricing.ancillary.assignment.by.season=Ancillary Assignments by Season
groupPricing.ancillary.revenueStreams=Ancillary Revenue Streams
groupPricing.ancillary.add.revenueStream=Add Revenue Stream
groupPricing.ancillary.revenueStreams.delete.message=Stream in use and unable to delete. Please remove any assignments which use this stream.
groupPricing.ancillary.deleteSeason.message=<b>You are deleting a season</b><p>Deleting this season will also remove its revenue assignments. Are you sure you want to continue?</p>
groupPricing.ancillary.mergeConfirmation=<b>Season dates will be adjusted.</b><p>The dates you&#39;ve selected overlap an existing season. The existing season will be split or merged. Are you sure you want to continue?</p>
groupPricing.generalConfig.perRoomServicingCost=Per Room Servicing Cost
groupPricing.generalConfig.error.validRange=Please enter a value between {0} and {1}.
groupPricing.rateConfig.defaultRateConfigTitle=Default Rate Configuration
common.upperLimit=Upper Limit
groupPricing.defaultMAR=Default MAR
groupPricing.MAR_override=MAR Override
groupPricing.validation.DefaultMAR=The {0} Default MAR value {1} has to be greater or equal to 0 and less than or equal to the {0} Upper Limit value
groupPricing.validation.upperLimit=The {0} Default MAR value {1} has to be less than or equal to all current MAR season values for that day. MAR season with range ({2}) has a lower value."
groupPricing.seasonCollapseView.errorMessage=The MAR season {0} value must be equal to or greater than the Default MAR {0} value ({1}) and less than or equal to the Upper Limit {0} value ({2})
groupPricing.upperLimit.defaultMAR.validation=The {0} Upper Limit value {1} has to be greater than or equal to the {0} Default MAR value
groupPricing.upperLimit.MAR.range.error=The {0} Upper Limit value {1} has to be greater than or equal to all current MAR season values for that day. MAR season with range ({2}) has a higher value.
groupPricing.add.revenue.stream=Add Revenue Stream
groupPricing.column.header.conference.and.banquet.revenue.stream=Conference and Banquet Revenue Stream
groupPricing.column.header.profit.percentage=Profit %
groupPricing.generalConfiguration=General Configuration
groupPricing.rateConfiguration=Rate Configuration
groupPricing.confAndBanquet=Conference & Banquet
groupPricing.confAndBanquet.range.validator=Please enter a value greater than 0 and less than 100000000, not {0}
groupPricing.roomDisplacementsAndOccupancyForecastDetails.title=Room Displacements and Occupancy Forecast Details

#Group Evaluation
groupEvaluation.total.rooms=Total \nRooms
groupEvaluation.number.of.nights=# of \nNights
groupEvaluation.net.incremental.rooms=Net Incremental \nRooms
groupEvaluation.recommended.rate=Recommended \nRate
groupEvaluation.break.even.rate=Break Even \nRate
groupEvaluation.average.mar.rate=Avg MAR \nRate
groupEvaluation.net.profit=Net \nProfit
groupEvaluation.profit.percentage=Profit \n%
groupEvaluation.show.details=Show Details
groupEvaluation.edit.group.info=Edit Group Info
groupEvaluation.group.info=Group Info
groupEvaluation.re.evaluate=Re-Evaluate
groupEvaluation.singular.action=Action
groupEvaluation.stay.date.start=Stay Date Start
groupEvaluation.stay.date.end=Stay Date End
groupEvaluation.evaluation.date.start=Evaluation Date Start
groupEvaluation.evaluation.date.end=Evaluation Date End
groupEvaluation.notes=Notes
groupEvaluation.generalparameters=General Parameters
groupEvaluation.costs=Costs
groupEvaluation.ancillary=Ancillary
groupEvaluation.conferenceandbanquet=Conference and Banquet
groupEvaluation.nights=Nights
groupEvaluation.followupdate=Follow-up Date
groupEvaluation.complimentaryrooms=Complimentary Rooms
groupEvaluation.commission=Commission
groupEvaluation.recommendedrate=Recommended Rate
groupEvaluation.preferreddate=Preferred Date
groupEvaluation.breakeven=Break Even
groupEvaluation.averagemar=Average MAR
groupEvaluation.netprofit=Net Profit
groupEvaluation.profitper=Profit %
groupEvaluation.netincrementalrooms=Net Incremental Rooms
groupEvaluation.preferred.arrival.date.in.past.warning=The preferred arrival date was in the past and has been removed. Please review the preferred arrival date selection.
Scenario=Scenario
Definite=Definite
Tentative=Tentative
groupEvaluation.search.Evaluation=Search Evaluation
groupEvaluation.evaluation.details=Evaluation Details
groupEvaluation.evaluation.results=Evaluation Results
groupEvaluation.new.evaluation=New Evaluation
groupEvaluation.grossrevenue=Gross Revenue
groupEvaluation.concessions=Concessions
groupEvaluation.netrevenue=Net Revenue
groupEvaluation.grossprofit=Gross Profit
groupEvaluation.displacedrevenue=Displaced Revenue
groupEvaluation.displacedprofit=Displaced Profit
groupEvaluation.character.length.group.name=Please enter 100 characters or less for Group name.
groupEvaluation.rate.contracted=Rate Contracted
groupEvaluation.successful.save.with.group.name=Evaluation group details have been saved successfully for group: {0}
groupEvaluation.click.to.remove=Click to remove
groupEvaluation.revenueperroomnight=Revenue per Room Night
groupEvaluation.totalprofitperroomnight=Total Profit Per Room Night
groupEvaluation.revenuepergroup=Revenue per Group
groupEvaluation.commissionpercent=Commission %
groupEvaluation.totalprofit=Total Profit
groupEvaluation.discountedroomsat=Discounted Rooms at
groupEvaluation.discountedrooms=Discounted Rooms:
groupEvaluation.complimentaryroomsper=Complimentary Rooms per
groupEvaluation.error.notincludedinresponse=Not Included In Response
groupEvaluation.success.acceptablerate=Acceptable Rate
groupEvaluation.error.notprofitable=Not Profitable
groupEvaluation.error.notacceptable=Not Acceptable
groupEvaluation.warning.recommendedratecappedbyMAR=Recommended Rate is capped by Average MAR
groupEvaluation.warning.recommendedratecappedbyUpperLimit=Recommended Rate is capped by Upper Limit
groupEvaluation.error.unabletoperformevaluation=Unable to Perform Evaluation
groupEvaluation.error.nocapacityavailable=No Capacity Available
groupEvaluation.dateofstay=Date of Stay
groupEvaluation.availableCapacity=Available \nCapacity
groupEvaluation.revenuecosts=Revenue/Costs
groupEvaluation.discountedroomsper=Discounted Rooms per
groupEvaluation.generateEvaluation=Generate Evaluation
groupEvaluation.saveEvaluation=Save Evaluation
groupEvaluation.preferred=Preferred
groupEvaluation.warning.ancillarystreamreset=Your Ancillary Revenue Streams have been reset to those that are associated with your new Market Segment selection.
Please review the adjusted revenue streams.
groupEvaluation.success.evaluationresultssaved=Your evaluation results have been saved successfully.
groupEvaluation.maximumreached=Maximum reached
groupEvaluation.selectupto=Select up to
groupEvaluation.error.entervalidpercent=Please enter a percent value between 0 and 100, not {0}.
groupEvaluation.error.entervalidvalue=Please enter a value between 1 and {0}, not {1}.
groupEvaluation.error.complimentaryroomstotalmustbelessthansum=The complimentary rooms total ({0}) must be less than the sum of the requested rooms ({1}).
groupEvaluation.error.totalcomplimentaryanddiscountedrooms=The total of complimentary rooms and discounted rooms ({0}) must be less than the sum of the requested rooms ({1}).
groupEvaluation.error.setupgroupratesreadaccess=You must set group rates in Group Pricing Configuration before you can run a Group Evaluation. Please contact your Manager/Administrator.
groupEvaluation.error.setupgrouprateswriteaccess=You must set group rates in
groupEvaluation.error.beforegroupevaluation=before you can run a Group Evaluation.
groupEvaluation.requestedarrivaldates=The requested arrival dates,
groupEvaluation.numberofnightsfallsoutside=and the number of nights you have given falls outside the systems forecasted window date of {0}.
groupEvaluation.numberofroomsgreaterthanavlcap=Number of rooms must be greater than or equal to {0} and less than {1}, the available capacity.
groupEvaluation.error.enteratleastonenight=You must enter at least one night.
groupEvaluation.error.enternomorethannights=You must enter no more than {0} nights.
groupEvaluation.warning.parameterschanged=Evaluation parameters have changed. Please evaluate again before saving.
groupEvaluation.warning.discountedroomslessthanrequestedrooms=The discounted rooms total {0} must be less than the sum of the requested rooms {1}.
groupEvaluation.DayOfStay=Day of {0} Stay
groupEvaluation.displacedRooms=Displaced {0} Rooms
groupEvaluation.incrementalRooms=Incremental Rooms
groupEvaluation.withoutGroup=Without Group
groupEvaluation.withGroup=With Group
groupEvaluation.preStay=Pre-Stay
groupEvaluation.postStay=Post-Stay
groupEvaluation.detailsFor=Details for
groupEvaluation.orMore=or more
groupEvaluation.clkPieChartToFilterFG=Click pie chart to filter by Forecast Group
groupEvaluation.onBooks=On Books
groupEvaluation.noDisplacedRoomsFound=No displaced rooms found

#Group Wash
groupWash.pickupType=Pickup Type
groupWash.overrideStatus=Override
groupWash.overrideDate=Override\nDate
groupWash.overrideUser=Override\nUser
groupWash.userWash=User\nWash
groupWash.userWashPercent=User\nWash %
groupWash.dateRange=<b>Date Range</b><br/>{0} to {1}
groupWash.cutoffDate=<b>Cutoff Date</b><br/>{0}
groupWash.marketSegment=<b>Market Segment</b><br/>{0}
groupWash.forecastGroup=<b>Forecast Group</b><br/>{0}
groupWash.salesManager=<b>Sales Manager</b><br/>{0}
groupWash.collapseInvalidField=Collapse is disabled when invalid fields exist.
groupWash.group.name.search=Group Name Search
groupWash.group.range.validator.error.message=Please enter a value between 0 and {0}, not 
groupWash.specify.expiration.date=Please enter a wash override when specifying an expiration date.
groupWash.individual.groups=Individual Groups
groupWash.filter.invalid.error.message=Filter is disabled when fields are invalid.
groupWash.filter.contains=Filter\: Group Name contains {0}
groupWash.sys.rem.demand=Sys Rem Demand
groupWash.system.remaining.demand=System Remaining Demand
groupWash.on.books.value=On Books Value
groupWash.excelTitle=Individual Group Wash Report
groupWash.washOverrides=Wash Overrides
groupWash.onbooks=On\nBooks
groupWash.RemainingDemand=Remaining\nDemand
groupWash.userwash.remainingdemand=User Wash %\n(Remaining Demand)
groupWash.expiration.date=Expiration\nDate
groupWash.rate.code=Rate Code
groupWash.search.rate.code=Search Rate Code
groupWash.data.changed.refresh=Data has changed. Please refresh the page.
groupWash.group.wash=Group Wash


#test
bogus.key.for.test=Even more bogus value for test
dataExtractionUtility.analyticsDataSets=Analytics Data Sets
dataExtractionUtility.request=Request Data Sets
dataExtractionUtility.email=Email
dataExtractionUtility.note=Note\: Data requested will be from the time of the request.
dataExtractionUtility.rss=RSS
dataExtractionUtility.requestExtracts=Request Extracts
dataExtractionUtility.showUserMessage=Your request has been submitted. You will be notified of the extract location via email when available.
dataExtractionUtility.tenant=Tenant
dataExtractionUtility.database=Request Database
dataExtractionUtility.invalidMessage=Please select at least one request
dataExtractionUtility.requestExtractNote=Note\: Date range selection is only applicable for request extracts.

#Property Specific Configuration
propertySpecificConfig.competitor.display.settings=Competitor Display Settings
propertySpecificConfig.competitorDisplaySettings.description=Select the way competitors will be displayed for comparison within Pricing Management
propertySpecificConfig.competitorLabel=Show competitor by
propertySpecificConfig.specificCompetitorLabel=Select a specific competitor
propertySpecificConfig.error.selectSpecificCompetitor=A specific competitor is required.
propertySpecificConfig.propertyInformation.description=Edit the Property Name
propertySpecificConfig.propertyInformation.propertyLabel=Property Name
propertySpecificConfig.propertyInformation.validation.requiredValueShouldNotBeEmpty=Property Name should not be empty.
propertySpecificConfig.property.information=Manage Property Information
propertySpecificConfig.propertyInformation.update.failed.error=Property name can not be saved. Please try again later. If this problem continues, please contact Administrator.

#Optimization Settings
propertySpecificConfig.optimizationSettings.description=Edit the Optimization Setting
propertySpecificConfig.optimizationSettings.label=Optimization Method 2 (Dynamic Pricing)\:
propertySpecificConfig.optimizationSettings.tableTitle=Optimization Settings History
propertySpecificConfig.optimization.settings=Optimization Settings
propertySpecificConfig.optimizationSettings.enable=Enable
propertySpecificConfig.optimizationSettings.disable=Disable
Enable=Enable
Disable=Disable



#Monitoring Dashboard
monitor.nameValueFormat=<b>{0} </b>: {1}

monitor.problem.myClaimedProblems=My Problems
monitor.problem.header.score=Score
monitor.problem.header.propertyStage=Property Stage
monitor.problem.header.jobName=Job Name
monitor.problem.header.owner=Owner
monitor.problem.claim=Claim
monitor.problem.release=Release
monitor.problem.problemDetails=Problem Detail
monitor.problem.problemDescription=Problem Description
monitor.problem.type=Type
monitor.problem.claimType=Claim Type
monitor.problem.problemState=Problem State
monitor.problem.owner=Owner \:
monitor.problem.userName=Me({0},{1})
monitor.problem.problemOpenLongerThan=Problem was open longer than
monitor.problem.and=and
monitor.problem.between=between
monitor.problem.problemWas=Problem was
monitor.problem.already.claimed.title=Problem Already Claimed
monitor.problem.already.claimed.message=One or more of your selected problems has already been claimed by another user.\n Please claim the open problems you wish to action.
monitor.problem.header.jobNameFilter=Filter Jobs

monitor.solution.addProposedSolution=Add Solution
monitor.solution.updateSolution=Update Solution

monitor.lockingProperties.blockedJobs=Blocked Jobs
monitor.lockingProperties.lockedBy=Locked By
monitor.lockingProperties.lockedSince=Locked Since

monitor.intervention.totalFailedJobs=Total Failed Jobs
monitor.intervention.totalProblem=Total Problem
monitor.intervention.activeProblem=Active Problem
monitor.intervention.averageUnclaimedTime=Average Unclaimed Time
monitor.intervention.averageActiveTime=Average Active Time

monitor.pdp.title=Property Daily Processing
monitor.pdp.title.short=PDP

monitor.mbe.Exception=Exception Condition

note.typeNotesHere=Type notes here
note.addNote=Add Note
note.addNoteCharactersMaximum=Add note ({0} characters maximum)
monitor.job.title=Jobs
monitor.job.summary.title=Job Summary
monitor.job.agent=Agent
monitor.job.details=Job Details
monitor.job.exitMessage=Exit Message
monitor.job.fileCount=File Count
monitor.job.header.jobNameFilter=Filter Jobs
monitor.job.node=Node
monitor.job.loop=Loop
monitor.job.loop.info={0} of {1} complete
monitor.job.step.name=Step
monitor.job.step.name.current=Current Step
monitor.job.step.exclude=Exclude if current step
monitor.job.problem.summary.title=Problem Summary
monitor.job.problemCount=Problems : {0}
monitor.problem.closedDate=Closed Date
monitor.jobActionConfirmation=Are you sure you want to {0} selected
monitor.jobActionConfirmation.jobs={0} jobs?
monitor.jobActionConfirmation.job={0} job?
monitor.job.action.step.after={0} after current step
monitor.job.action.step.now={0} immediately
monitor.job.loop.start=Loop start
monitor.job.loop.end=Loop end
monitor.job.loop.number=Loop number
monitor.addNewSolution=Add New SolutionR
monitor.availableSolutions=Available Solutions
monitor.newSolution= New Solution
jobStatus.running=Running
jobStatus.stopped=Stopped
jobStatus.stopping=Stopping
jobStatus.failed=Failed
jobStatus.abandoned=Abandoned
jobStatus.completed=Completed
jobStatus.skipped=Skipped
jobStatus.blocked=Blocked
jobStatus.overdue=Overdue

monitor.timeMachine.go=Go!
monitor.timeMachine.now=Now

monitor.timeMachine.summary.activeJobCount=Active Jobs
monitor.timeMachine.summary.activeProblemCount=Active Problems
monitor.timeMachine.summary.activeUserCount=Active User Sessions

monitor.timeMachine.jobs.jobName=Job Name
monitor.timeMachine.jobs.propertyCode=Property Code
monitor.timeMachine.jobs.stepName=Step Name
monitor.timeMachine.jobs.startDate=Start Date
monitor.timeMachine.jobs.endDate=End Date
monitor.timeMachine.jobs.stepStartDate=Step Start Date
monitor.timeMachine.jobs.stepEndDate=Step End Date

monitor.timeMachine.problems.jobName=Job Name
monitor.timeMachine.problems.propertyCode=Property Code
monitor.timeMachine.problems.stepName=Step Name
monitor.timeMachine.problems.creationDate=Creation Date
monitor.timeMachine.problems.errorCode=Error Code
monitor.timeMachine.problems.closedDate=Closed Date

monitor.timeMachine.users.userName=User Name
monitor.timeMachine.users.loginDate=Login Date
monitor.timeMachine.users.pageName=Page Name
monitor.timeMachine.users.pageStartDate=Page Start Date
monitor.timeMachine.users.pageEndDate=Page End Date
monitor.timeMachine.users.logoutDate=Logout Date

timeMachine.step.class.regulator=Acquire Regulator Lock
timeMachine.step.class.ratchet=Ratchet
timeMachine.step.class.population=Population
timeMachine.step.class.forecasting=Forecasting
timeMachine.step.class.optimization=Optimization
timeMachine.step.class.calibration=Calibration
timeMachine.step.class.noncritical=Non Critical

proposed.solutions=Proposed Solutions
monitor.jobStatistics.totalJobsCount=Total Jobs Count
monitor.jobStatistics.failedJobsCount=Failed Jobs Count
monitor.jobStatistics.failureRate=Failure Rate
monitor.jobStatistics.averageDuration=Average Duration

monitor.statistics.bdeReport.title=BDE Report
monitor.statistics.bdeReport.bdeInitiatedDate=BDE Initiated Date
monitor.statistics.bdeReport.decisionsCreatedDate=Decisions Created Date
monitor.statistics.bdeReport.inputIdentifier=Input Identifier
monitor.statistics.bdeReport.decisionsIdentifier=Decisions Identifier
monitor.statistics.bdeReport.duration=Duration

monitor.statistics.cdpReport.title=CDP Report
monitor.statistics.cdpReport.countExpected=Number of Expected
monitor.statistics.cdpReport.countCompleted=Number of Completed
monitor.statistics.cdpReport.discrepancy=Discrepancy
monitor.statistics.cdpReport.discrepanciesOnly=Show Discrepancies Only

monitor.statistics.daily.title=Daily Job Statistics
monitor.statistics.daily.graph.title=Average {0} Duration by Date
monitor.statistics.daily.graph.ave.week=Week Ave
monitor.statistics.daily.graph.ave.fortnight=Fortnight Ave
monitor.statistics.daily.graph.ave.month=Month Ave
monitor.statistics.daily.graph.ave.filter=Filtered Ave

monitor.propertyDailyProcessing.title=Property Daily Processing
monitor.propertyDailyProcessing.currentPropertyTime=Current Property Time
monitor.propertyDailyProcessing.bdeStatus=BDE Status
monitor.propertyDailyProcessing.cdpStatus=CDP Status
monitor.propertyDailyProcessing.rssStatus=RSS Status
monitor.propertyDailyProcessing.notReceived=Not Received
monitor.propertyDailyProcessing.inProgress=In Progress
monitor.propertyDailyProcessing.completed=Completed
monitor.propertyDailyProcessing.inputProcessingWindow.caption=Processing Details
monitor.propertyDailyProcessing.inputProcessingWindow.inputId=Input Identifier
monitor.propertyDailyProcessing.inputProcessingWindow.receivedTime=Received Time
monitor.propertyDailyProcessing.inputProcessingWindow.completedTime=Completed Time
monitor.propertyDailyProcessing.inputProcessingWindow.decisionId=Decision ID
monitor.propertyDailyProcessing.inputProcessingWindow.destinationId=Destination
monitor.propertyDailyProcessing.inputProcessingWindow.status=Status
monitor.propertyDailyProcessing.inputProcessingWindow.uploadedTime=Uploaded Time
monitor.propertyDailyProcessing.inputProcessingWindow.duration=Duration
monitor.propertyDailyProcessing.inputProcessingWindow.problemCount=Problem Count
monitor.inputProcessingTable.title=Input Processing
monitor.inputProcessingTable.input.type=Input Type
monitor.inputProcessingTable.sla.violation=SLA Violation
monitor.inputProcessingTable.overdue.date=Overdue Time
monitor.inputProcessingTable.filter.sla.violations.only=SLA Violations Only
monitor.dailyProcessingSummary.count=Count
monitor.dailyProcessingSummary.percent=Percent
monitor.inputProcessingTable.status=Status
monitor.inputProcessingTable.start.date=Received Start Date
monitor.inputProcessingTable.end.date=Received End Date

monitor.schedule.name=Schedule Name
monitor.schedule.status=Status
monitor.schedule.last.execution=Last Execution
monitor.schedule.next.execution=Next Execution
monitor.schedule.interval=Interval (minutes)
monitor.schedule.cron.expression=Cron Expression
monitor.schedule.cron.presets=Presets
monitor.schedule.start=Start Schedule
monitor.schedule.update=Update Schedule
monitor.schedule.stop=Stop Schedule
monitor.schedule.stop.confirm=Are you sure you want to stop schedule {0}
monitor.schedule.stopall=Stop All Schedules
monitor.schedule.stopall.confirm=Are you sure you want to stop all schedules
monitor.schedule.cancel=Cancel Schedule
monitor.schedule.cancel.confirm=Are you sure you want to cancel schedule {0}
monitor.schedule.resume=Resume Schedule
monitor.schedule.resume.confirm=Are you sure you want to resume schedule {0}
monitor.schedule.resumneall=Resume All Schedules
monitor.schedule.resumeall.confirm=Are you sure you want to resume all schedules
monitor.schedule.interval.or.expression.required=An Interval or Cron Expression is Required to start schedule {0}
monitor.schedule.invalid.expression=Cron Expression is not valid, cannot start schedule {0}
monitor.schedule.frequency=Frequency

monitor.summary.table=Table
monitor.summary.graph=Graph
monitor.bulletin.contact.person=Contact Person
monitor.bulletin.estimated.resolution.time=Estimated Resolution Time

#Installation
installation.client.add=Add Client
installation.client.add.success=Add Client has been initiated for {0}
installation.client.total.properties=Total Properties
installation.property.add=Add Property
installation.property.remote.agent=Remote Agent
installation.property.remote.agent.id=Remote Agent ID
installation.property.create.new.agent=Create New Remote Agent
installation.property.add.success=An Add Property job has been started for property {0}
installation.property.already.exists=Unable to add property: property already exists
installation.property.id=Property ID
installation.property.codes=Property Codes
installation.property.job.already.active=An Add Property job is already active for this property
installation.property.timezone=Property Timezone
installation.property.crsTimezone=CRS Timezone
installation.property.webrate.alias=Web Rate Alias
installation.property.yield.currency=Yield Currency
installation.property.propertycode.invalid.character=Only alphanumeric characters are allowed for Property Code
installation.property.clientcode.invalid.character=Only alphanumeric characters are allowed for Client Code
installation.property.add.excluded.dates=Add Dates
installation.property.excluded.dates=Excluded Dates
installation.property.modify.excluded.dates=Modify Excluded Dates
isntallation.property.exclude.dates.overlap=Dates overlap existing exclude date range
installation.property.modify.excluded.dates.update.notes=Please enter an updated description in the Notes field
installation.property.modify.excluded.dates.required.fields=Please enter a valid value for all required fields
installation.property.summary.agent=Agent
installation.property.summary.title=Property Summary
installation.property.summary.unprocessed.activity.count=Unprocessed Activity Count
installation.property.summary.processed.activity.count.bde=Processed BDE Activity Count
installation.property.summary.processed.activity.count.cdp=Processed CDP Activity Count
installation.property.summary.unprocessed.rss.count=Unprocessed Web Rate Extracts
installation.property.summary.processed.rss.count=Processed Web Rate Extracts
installation.property.summary.cdp.schedules=Scheduled CDP's
installation.property.summary.most.recent.input=Most Recent Input Received
installation.property.delete.salesforce.case.number=Salesforce Case Number
isntallation.property.delete.salesforce.case.number.required=Salesforce Case Number is Required
installation.property.delete.confirmation=Once the property is deleted, you will not be able to recover the data.  Are you sure you want to delete this property?
installation.property.delete.success=A Delete property job has been started for property {0}

#Stage Enums
DATA_CAPTURE=Data Capture
CATCHUP=Catch Up
POPULATION=Population
ONE_WAY=1-Way
TWO_WAY=2-Way

#Property actions
action.property.configure=Configure
action.property.catchup.extract=Catchup
action.property.catchup.webrate=Web Rate Catchup
action.property.rollback=Rollback
action.property.message.catchup.success=Catchup job created
action.property.message.rollback.success=Rollback job created
action.property.message.rollback.warning=This will delete all activity data for this property.  Continue?
#Reports
#Common
report.createdBy=Created By
report.createdOn=Created On
report.propertyName=Property Name
report.currency=Currency
report.currencyValue=USD
report.clientName=Client
report.panelBarTitle=Setup
report.formatLabel=Format
report.analysisStart=Analysis Start
report.analysisEnd=Analysis End
report.totalRecords=Total Records
report.barByDay=Bar by Day
report.barByLOS= Bar LOS
report.paceDays.validation = Enter a number for the days of pace
report.viewBy=View By
report.dow=Day of Week
report.los=Length of Stay
report.overrideCategory=Override Category
report.costOfWalkValue=Cost of Walk \nValue
report.overbookingValue=Overbooking \nValue
report.overbookingLimit=Overbooking \nLimit
report.overrideLastModifiedOn= Override Last \nModified On
report.overrideLastModifiedBy= Override Last \nModified By
report.barOverrideType=BAR Override Type
report.userSelection=User Selection
report.systemSelection=System Selection
report.competitorRateFor=Competitor Rate For
report.barOption=BAR
report.label.noCompetitor= No Competitor
report.label.noRoomClasses= No RoomClasses
report.label.noForecastGroups= No Forecast Groups
report.label.noMarketSegments= No Market Segments
report.label.noBusinessViews= No Business Views
report.column.decisionGenerationDate=Decision Generation
report.column.daysOfPace1=Days of
report.column.daysOfPace2=Pace
report.column.captureDate=Capture Date
report.column.roomsSold=Rooms Sold
report.column.adr=ADR
report.column.lastRoomValue=Last Room Value
report.column.systemUnconstrainedTotalDemand=System Unconstrained Total Demand - Total
report.column.roomsNAOutOfOrder=Rooms N/A - Out of Order
report.column.roomsNAOther=Rooms N/A - Other
report.column.occupancyForecastTotal=Occupancy Forecast - Total
report.column.budgetedRoomsSold=Budgeted Rooms Sold
report.column.systemUnconstrainedDemand=System Unconstrained Demand
report.column.systemGroupWashPer=System Group Wash %
report.column.userGroupWashPer=User Group Wash %
report.column.value=Value
report.column.outOfOrder=Out of Order
report.column.lastRoom=Last Room
report.column.barRate=Bar Rate
report.column.competitorPrice=Competitor Price
report.arrivalDateLabel=Arrival Date
report.filter.viewBy.totalProperty=Total Property
report.filter.viewBy.totalTransient=Total Transient
report.filter.viewBy.totalGroup=Total Group
report.filter.viewBy.roomClasses=Room Classes
report.filter.viewBy.forecastGroups=Forecast Groups
report.filter.viewBy.marketSegments=Market Segments
report.filter.caption.noSelectionAvailable = Selection not available
report.filter.warning.selectFilter=Select filter criteria before generating report.
report.filter.warning.noRoomType=Select room type before generating report.
report.format.on.screen=On Screen
report.format.excel=Excel
report.report=Report
report.criteria=Criteria
report.column.hotelCapacity=Hotel Capacity
report.column.roomsSoldGroup=Rooms Sold - Group
report.column.roomsSoldTransient=Rooms Sold - Transient
report.column.cancelled=Cancelled
report.column.noShow=No Show
report.column.bookedRoomRevenue=Booked Room Revenue
report.column.forecastedRoomRevenue=Forecasted Room Revenue
report.column.occupancyForecastGroup=Occupancy Forecast - Group
report.column.occupancyForecastTransient=Occupancy Forecast - Transient
report.column.systemUnconstrainedTotalDemandGroup=System Unconstrained Total Demand - Group
report.column.systemUnconstrainedTotalDemandTransient=System Unconstrained Total Demand - Transient
report.column.userTotalDemandTotal=User Total Demand - Total
report.column.userConstrainedTotalDemandGroup=User Constrained Total Demand - Group
report.column.userUnconstrainedTotalDemandTransient=User Unconstrained Total Demand - Transient
report.column.bookedRevPar=Booked RevPAR
report.column.forecastedRevPar=Forecasted RevPAR
report.column.bookedAdr=Booked ADR
report.column.forecastedAdr=Forecasted ADR
report.column.barByDayForRC=BAR by Day for RC
report.column.barLos1ForRC=BAR LOS1 for RC
report.column.barLos2ForRC=BAR LOS2 for RC
report.column.barLos3ForRC=BAR LOS3 for RC
report.column.barLos4ForRC=BAR LOS4 for RC
report.column.barLos5ForRC=BAR LOS5 for RC
report.column.barLos6ForRC=BAR LOS6 for RC
report.column.barLos7ForRC=BAR LOS7 for RC
report.column.barLos8ForRC=BAR LOS8 for RC
report.column.forRC=for RC
report.column.forForecastGroup=for Forecast Group
report.column.userDemand=User Demand
report.column.barLos1=BAR LOS1
report.column.barLos2=BAR LOS2
report.column.barLos3=BAR LOS3
report.column.barLos4=BAR LOS4
report.column.barLos5=BAR LOS5
report.column.barLos6=BAR LOS6
report.column.barLos7=BAR LOS7
report.column.barLos8=BAR LOS8
report.column.hotelOutOfOrder=TTL Hotel Out of Order

#Booking Pace Report
bookingPaceReport.title=Booking Pace Report
bookingPaceReport.paceDays.validation=Please enter a valid number that is less then or equal to {0}
bookingPaceReport.viewByOption.totalGroupNTrasient = Testing Testing Total Property, Group & Transient
bookingPaceReport.hotelCapacity= Hotel Capacity
bookingPaceReport.availableCapacity= Available Capacity
bookingPaceReport.forecast= Forecast

#MCAT Pace Report
mCATReport.title=MCAT Plus Report
mCATReport.column.srp.heading=SRP
mCATReport.column.marketSegment.heading=Market Segment
mCATReport.column.mcat.heading=MCAT

#Pricing Pace Report
pricingPaceReport.name=Pricing Pace Report
pricingPaceReport.validation.maximumCompetitor=More than 15 competitors cannot be selected

#Rate Plan Report
ratePlanReport.title=Production Report - All Rate Plans
ratePlanReport.ratePlanName=Rate Plan Name
ratePlanReport.ratePlans=Rate Plans

#Input Override Report
inputoverrideReport.title=Input Override Report
inputOverrideReport.category= Override \nCategory
inputOverrideReport.includeNotesLabel= Include Notes
inputOverrideReport.column.userDemandOverride= User Demand \nOverride
inputOverrideReport.column.userWashOverridePercent= User Wash \nOverride (%)
inputOverrideReport.column.userWashExpirationDate= User Wash \nExpiration Date

#Forecast Validation Report
forecastValidationReport.title=Forecast Validation Report
forecastValidationReport.forecastGroupLevel.title=Data Extract Report for Forecast Validation at Forecast Group Level
forecastValidationReport.hotelLevel.title=Data Extract Report for Forecast Validation at Total Hotel Level
forecastValidationReport.msLevel.title=Data Extract Report for Forecast Validation at Market Segment Level

#Data Extraction Report
dataExtractionReport.hotelLevel.title=Data Extraction Report at Hotel level
dataExtractionReport.AtRoomClass.title=Data Extraction Report at Room Class level
dataExtractionReport.atRoomType.title=Data Extraction Report at Room Type Level
dataExtractionReport.ForecastGroup.title=Data Extraction Report at Forecast Group level
dataExtractionReport.MarketSegment.title=Data Extraction Report at Market Segment level
dataExtractionReport.BusinessView.title=Data Extraction Report at Business View level


#Property Attribute Assignment Report
propertyAttributeAssignmentReport.name=Property Attributes Report

#Output Override Report
overrideReport.title=Output Override Report
overrideReport.bar.bayLos.title=Output (BAR by LOS) Override Report
overrideReport.bar.bayDay.title=Output (BAR by Day) Override Report
overrideReport.overbooking.title=Output (OVBK) Override Report
overrideReport.barOption.fileName=OutputOverride_PriceManagement_report
overrideReport.overbookingOption.fileName=OutputOverride_OVBKReport
overrideReport.overbookingOption=Overbooking & Cost of Walk

#Pricing Report
pricingReport.name=Pricing Report
pricingReport.barByDay.title=Pricing (BAR By Day) Report
pricingReport.barByLos.title=Pricing (BAR By LOS) Report
pricingReport.barByDay.fileName=Bar_Rate
pricingReport.barByLos.fileName=BarRateReport_LOS

#Pricing Override History Report
pricingOverrideHistoryReport.title=Pricing Override History Report
pricingOverrideHistoryReport.fileName=BarOverrideHistory_LOS
pricingOverrideHistoryReport.newDecision=New Decision
pricingOverrideHistoryReport.oldDecision=Old Decision
pricingOverrideHistoryReport.overrideLastModifiedOn=Override Last Modified On

#Performance Comparison Report
performanceComparisonReport.title=Performance Comparison
performanceComparisonReport.filter.label.analysisStartDate = Analysis Start Date
performanceComparisonReport.filter.label.analysisEndDate = Analysis End Date
performanceComparisonReport.filter.label.comparisonStartDate = Comparison Start Date
performanceComparisonReport.filter.label.comparisonEndDate = Comparison End Date
performanceComparisonReport.filter.label.classes = Classes
performanceComparisonReport.filter.label.groups = Groups
performanceComparisonReport.filter.label.segments = Segments
performanceComparisonReport.filter.label.view = View
performanceComparisonReport.output.column.occupancyOnBooks = Occupancy On Books
paceDays.validationDays = # of Days of Pace can not be more than {0} Days.

#Pick Up and Change Differentital Report
pickUpReport.hotelLevel=Pick Up Report at Total Hotel Level
pickUpReport.roomClassLevel=Pick Up Report at Room Class level
pickUpReport.marketSegmentLevel=Pick Up Report at Market Segment Level
pickUpReport.businessLevel=Pick Up Report at Business View Level
pickUpReport.groupLevel=Pick Up Report at Group Level
changeReport.marketSegmentLevel=Change and Differential Control Report - Market Segment
changeReport.businessViewLevel=Change and Differential Control Report - Business View


# Property configuration / global parameters
prop.config.title.app.param = Configuration
prop.config.title.global = Global
prop.config.title.client = Client
prop.config.title.property = Property
prop.config.title.history = Change History
prop.config.param.column.name = Parameter Name
prop.config.param.column.value = Value
prop.config.param.column.modified.date = Since
prop.config.param.column.multi.property.edit = Multi-Property Edit
prop.config.param.column.global = Global
prop.config.param.column.client = Client
prop.config.param.column.property = Property
prop.config.param.column.overridden = Overridden
prop.config.param.column.overridden.property = Property Overriding
prop.config.param.column.override.level = Inherit Level
prop.config.param.search.not.found=The parameter {0} could not be found for the current context.
prop.config.value.delete.confirm=Are you sure you want to remove the parameter value?
prop.config.delete.toggle=Allow deleting
prop.config.client.property.values=Property parameter values
prop.config.history.parameter.name = Parameter Name
prop.config.history.action = Action
prop.config.history.context = Context
prop.config.history.value = Value
prop.config.history.modified.date = Modified Date
prop.config.history.modified.by = Modified By

# Rate Plan Configuration Module
ratePlan.headers.title=Rate Headers
ratePlan.configuration.title=Rate Plan Configuration
ratePlan.column.heading.name = Name
ratePlan.column.heading.description = Description
ratePlan.button.addRatePlan.label = Add Rate Plan
ratePlan.checkBox.dateAccelerator.label = Apply first Rate Header Start and End Date
ratePlan.message.title=Rate Plan Configuration
ratePlan.message.validation.duplicate.name = The rate plan name already exists.
ratePlan.message.validation.validCharacterSet.name = The rate plan name contains some invalid characters.
ratePlan.message.validation.invalidRateHeaderDates.title = Edit Rate Header Dates
ratePlan.message.validation.invalidRateHeaderDates=Rate header startdate can not be after season earliest startdate or rate header endDate cannot be before season latest endDate.
ratePlan.message.validation.invalidRateHeaderValues=The data is invalid, please review.
ratePlan.configuration.nonyieldable.column=Non-Yieldable
rateDetails.message.validation.negativeRateValues=Enter rates greater than 0.
ratePlan.message.noRateHeadersPresent = You must add Rate Plans on the Rate Headers tab before you can work with Rate Details.
rateDetails.message.validation.existingSeason.title=Add Seasonal Dates
rateDetails.message.validation.existingSeason=Selected dates are used in an existing season.
rateDetails.message.validation.existingSeason1= The new season's date range will split or merge with an existing season. Do you want to continue?
rateDetails.ratePlanLevel.editModeCollapseWarning=Collapse is disabled, until you save or cancel this entry.
rateDetails.negative.rates.copied=Rates for each DOW should be greater than zero. There are invalid season rates for
rateDetails.order=Order:
rateDetails.room.class=Room Class:
rateDetails.warning.rates.for.one.roomtype.entered=Rates for at least one Room Type should be entered & rates for all days should be greater than zero.
rateDetails.title=Rate Details
rateDetails.warning.enter.valid.number=Please enter a valid number.

ratePlan.checkBox.accelerator.label = Offset from
ratePlan.season.deleteSeason=Are you sure you want to delete this season?
ratePlan.season.splitSeason.deleteSeason=Past dates for the season will not be deleted. Are you sure you want to continue?
common.label.of = of
common.label.show = Show
common.label.legend.past = Past
common.label.legend.present = Present
common.label.legend.future = Future
common.label.day.sunday = Sunday
common.label.day.monday = Monday
common.label.day.tuesday = Tuesday
common.label.day.wednesday = Wednesday
common.label.day.thursday = Thursday
common.label.day.friday = Friday
common.label.day.saturday = Saturday
ratePlan.past=Rate is in past, you cannot delete this.

# User Report
userReport.Name=User Report
userReport.allCodes=All Properties
userReport.allGroups=All Groups
userReport.allRoles=All Roles
userReport.allNames=All Names
userReport.authGroupName=Authorization Group Name
userReport.authGroupRole=Authorization Group Role
userReport.individualProperty=Individual Properties Property
userReport.individualPropertyRole=Individual Properties Role
userReport.userAssociationLabel=User Association:
userReport.userDetails=User Details

# MetricType2
ROOMS_SOLD=Rooms Sold
TOTAL_CAPACITY=Total Capacity
AVAILABLE_CAPACITY=Available Capacity
CANCELLATIONS=Cancellations
ARRIVALS=Arrivals
NO_SHOWS=No Shows
ROOMS_LEFT_TO_SELL=Rooms Left to Sell
OUT_OF_ORDER=Out of Order
DEPARTURES=Departures
MONTHLY_BOOKING_PACE=Monthly Booking Pace
OCCUPANCY_FORECAST_VALUE=Occupancy
OCCUPANCY_FORECAST_PERCENT=Occupancy %
AVERAGE_DAILY_RATE=ADR
REVENUE_PER_AVAIL_ROOM=RevPAR
REVENUE=Revenue
LAST_ROOM_VALUE=Last Room Value
BEST_AVAILABLE_RATE=Best Available Rate
OVERBOOKING=Overbooking
SPECIAL_EVENTS=Special Events
COMPETITOR_PRICE=Competitor Price

rateAdjustment.deleteConfiguration.title=Delete Rate Adjustment for Competitor
rateAdjustment.deleteConfiguration.message=<b>You are deleting rate adjustment configuration for {0}</b><p>Deleting this configuration will also remove its revenue assignments. Are you sure you want to continue?</p>
rateAdjustment.editRateAdjustmentConfiguration.message=Editing this Rate Shopping Adjustment is disabled, until you apply or cancel the other entry you are editing.
rateAdjustment.warning.review.your.changes=Please review your changes.
rateAdjustment.warning.no.changes.found.to.submit=No changes found to submit.
rateAdjustment.success.changes.saved.successfully=Changes saved successfully.
rateAdjustment.error.only.positive.values=Only positive values allowed.
rateAdjustment.error.percentage.between.limits=Percentage should be between 0.01 - 99.99.
rateAdjustment.tax=Tax
rateAdjustment.other=Other
rateAdjustment.example=Example
rateAdjustment.adjust.rate=Adjust Rate
rateAdjustment.warning.actual.rate.is.required=Actual Rate is required
rateAdjustment.actual.rate=Actual Rate
rateAdjustment.warning.work.is.in.progress=Still the work is in progress!
rateAdjustment.rate.Adjustment.for.competitor.title=Rate Adjustment for Competitor
Deduct=Deduct
Percentage=Percentage
Value=Value
userReport.individualPropertiesFor.0=Individual Properties For \: {0}

common.totalRecords=Total Records \:
total.records=Total Records

#Property Group
propertyGroup.listofpropertygroups=List of Property Groups:
propertyGroup.groupname=Group Name
propertyGroup.groupdescription=Group Description
propertyGroup.defaultproperty=Set as Default Property Group
propertyGroup.warning.delete.title=Delete Property Group
propertyGroup.error.msg.duplicatedefaultPropertygroup=Already Default property group has been assigned. Please remove and proceed further.
propertyGroup.error.msg.maxpropertiesinpropertygroup=You can select no more than {0} properties for a Property Group. Select fewer properties to continue.
propertyGroup.addProperties=Add Properties

# Authorization Group
authgroup.listofauthgroups=List of Authorization Groups:
authgroup.requiredinformation=Required Information
authgroup.groupname=Group Name
authgroup.groupdescription=Group Description
authgroup.max140chars=Max 140 characters
authgroup.filterproperties=Filter Properties
authgroup.showallproperties=Show All Properties
authgroup.propertiesavailable=Properties Available
authgroup.propertiesselected=Properties Selected
authGroup.addFilterCriteriaError.message=Adding filter criteria is disabled, until you select all possible values.
authGroup.warning.inUse.title=Authorization Group In Use
authGroup.warning.inUse.message=This might result in some user who had few roles assigned for the deleted authorization group to loose access to properties within the deleted authorization group.\nDo you want to delete this group?
authGroup.warning.inUse.message.change=Changes in authorization group might impact authorization significantly. Do you want to proceed?
authGroup.warning.delete.title=Delete Authorization Group


login.username=Username
login.password=Password
login.rememberMe=Remember Me
login.action=Log In
login.invalidLogin=The username or password you entered is incorrect.

# User Management
usermanagement.editinternalusers=Edit internal users
usermanagement.create=Create
usermanagement.deactivate=Deactivate
usermanagement.reactivate=Reactivate
usermanagement.listofusers=List of Users
usermanagement.listofusertablecaption=Name / E-mail (Login ID)
usermanagement.emailLoginID=E-mail (Login ID)
usermanagement.verifyEmail=Verify E-mail
usermanagement.ideasclientportalaccess=IDeaS Client Portal Access
usermanagement.propertiesrolesandpermissions=Properties, Roles and Permissions
usermanagement.authorizationgroup=Authorization Group
usermanagement.role=Role
usermanagement.individualproperty=Individual Property
usermanagement.nopermissiontoeditinternalusers=You do not have permissions to edit internal users.
usermanagement.reactivatinguserwarning=Reactivating a user will enable all previous associations to individual properties, authorization groups and roles.\n\nAre you sure you want to reactivate user(s)?
usermanagement.deactivatinguserwarning=Deactivating a user will disable all associations to individual properties, authorization groups and roles. \n\nThe user's email will also be removed from associated scheduled reports. \nAre you sure you want to deactivate user(s)?
usermanagement.unsavedchanges=You have made changes to the current user, do you want to discard your changes?
usermanagement.nouserselected=No user(s) selected to 
usermanagement.duplicateproperties=The list of Individual Properties cannot contain the same property more than once.
usermanagement.musthaverole=must have role.
usermanagement.musthaveindividualproperty=must have Individual Property selected
usermanagement.shouldnotexceed=should not exceed {0} characters.
usermanagement.authgrppropertywithoutrole=An Authorization Group cannot have a property set without a role
usermanagement.authgrprolewithoutpropoerty=An Authorization Group cannot have a role set without a property
usermanagement.aphabetvalidation=Cannot contain numbers.
usermanagement.lengthshouldnotexceed=Length should not exceed {0} characters.
usermanagement.entervalidemail=Enter a valid email.
usermanagement.discardchagesforcurrentuser=You have made changes to the current user, do you want to discard your changes?
usermanagement.updatevalidation=You cannot update this user as there are no permissions to access Individual Property(s) or Authorization Group.
usermanagement.emailsmustmatch=Emails must match.
usermanagement.editinternaluserwarning=Note: You are currently editing internal users. These users are internal to IDeaS and have access to all work contexts.

restrictionLevelReport.title=Restriction Level Report

# Decision configuration
decision.configuration=Decision Configuration
decision.configuration.destination=Destination
decision.configuration.delivery.type=Delivery Type
decision.configuration.upload.type=Upload Type

opera.data.load.process.title=Process Data Feed
opera.data.load.process.feed=Data Feed
opera.data.load.process.start=Process Data Feed

opera.data.load.summary.title=Data Loads
opera.data.load.summary.correlation.id=Correlation ID
opera.data.load.summary.first.activity.date=First Activity
opera.data.load.summary.last.activity.date=Last Activity
opera.data.load.summary.completion.status=Completion Status
opera.data.load.summary.past.days=Past Days
opera.data.load.summary.future.days=Future Days
opera.data.load.summary.business.date=Business Date
opera.data.load.summary.prepared.date=Prepared Date
opera.data.load.details.creation.date=Received Date
opera.data.load.details.file.type=Input Type

# Enumerations
enum.decision.destination.pcrs=PCRS System
enum.decision.destination.hilstar=Hilstar System
enum.decision.destination.opera=Opera System
enum.decision.destination.reserve=RESERVE System
enum.decision.destination.rds=RDS System
enum.decision.destination.ors=ORS System
enum.decision.destination.rezview=Rezview System
enum.decision.destination.hbsi=HBsi System
enum.decision.destination.synsis=Synxis System
enum.decision.destination.traveltripper=Travel Tripper System
enum.decision.type.barDaily=Daily BAR
enum.decision.type.dbar=DBAR
enum.decision.type.barByLosByRoomClass=BAR by LOS by room class
enum.decision.type.barByLosByRoomType=BAR by LOS by room type
enum.decision.type.barByFplos=BAR by FPLOS
enum.decision.type.fplosByRank=FPLOS by rank
enum.decision.type.fplosByHierarchy=FPLOS by hierarchy
enum.decision.type.fplos=FPLOS
enum.decision.type.losMinByRateCode=Min LOS by rate code
enum.decision.type.overbookingHotel=Hotel overbooking
enum.decision.type.overbookingRoomType=Room type overbooking
enum.decision.type.lrvByRoomClass=LRV by room class
enum.decision.type.lrvByRoomType=LRV by room type
enum.decision.type.profitAdjustmentType=Profit Adjustment type
enum.decision.type.lraControlFplos=LRA control FPLOS
enum.decision.type.lraControlMinLos=LRA control min LOS
enum.decision.upload.type.none=None
enum.decision.upload.type.differential=Differential
enum.decision.upload.type.full=Full

# Role Management
rolemangement.edit.internal.user.roles=Edit internal user roles
rolemanagement.edit.internal.user.warning=You are currently editing roles for internal users. These roles can only be assigned to internal IDeaS users.
rolemanagement.list.of.roles=List of Roles
rolemanagement.no.permission.to.edit.internal.roles=You do not have permissions to edit internal roles.
rolemanagement.warning.delete.role.title=Delete Role
rolemanagement.warning.role.in.use=This role cannot be deleted until the associated users are removed from it using the <a href='/solutions/user-management'>User Management</a> module.
rolemanagement.warning.delete.role=Are you sure you want to delete this role?
rolemanagement.warning.selectarole=Select a role.
rolemanagement.rolename=Role Name
rolemanagement.roledescription=Role Description
common.isCorporate=Is Corporate
rolemanagement.permissiontable.title=Permission Configuration
rolemanagement.copyRole.addedText=1
rolemanagement.roleIsInUse=This role is in use by one or more users. Changing the permissions for this role will modify the permissions for those users. Are you sure you want to modify this role?
rolemanagement.warning.inUse.title=Role In Use
rolemanagement.specialCharactersErrorMessage=Role title cannot contain any of the following characters: ?%&$#+\\

inventoryhistory.name=Inventory History Report
decision.type=Decision Type
rate.level=Rate Level
srp=SRP
decision.date.time=Decision Date Time
acknowledgement.date.time=Acknowledgement Date Time
acknowledgement.status=Acknowledgement Status
not.received=not received
success=success
failure=failure

pricing.pace.report.byday.title=Pricing (BAR by Day) Pace Report
decision.generation.date=Decision Generation Date
property.rooms.sold=Property Rooms Sold
property.occupancy.forecast=Property Occupancy Forecast
property.occupancy.forecast.percent=Property Occupancy Forecast (%)
room.class.occupancy.forecast=Room Class Occupancy Forecast
room.class.occupancy.forecast.percent=Room Class Occupancy Forecast %
bar.by.day=BAR by Day
fplos=FPLOS
overbooking=Overbooking
hotel=Hotel
house=House

comparative.booking.pace.report=Comparative Booking Pace Report
no.data.for.filter.criteria=No data is available for the selected filter criteria.
occupancyforecast%=Occupancy Forecast %

pricing.bar.by.los.pace.report=Pricing (BAR by LOS) Pace Report

bar.los1=BAR LOS 1
bar.los2=BAR LOS 2
bar.los3=BAR LOS 3
bar.los4=BAR LOS 4
bar.los5=BAR LOS 5
bar.los6=BAR LOS 6
bar.los7=BAR LOS 7
bar.los8=BAR LOS 8

name=Name
common.u=U
common.f=F
bar.pace.rateoftheday.tab.name=Bar Single Day Pace Report
inventoryhistoryrpt.tab.name=InventoryHistoryRpt
pricing.pace.bar.by.los.report=Pricing Pace Bar by LOS Report

active=Active
inactive=InActive
any=Any
sunday=Sunday
monday=Monday
tuesday=Tuesday
wednesday=Wednesday
thursday=Thursday
friday=Friday
saturday=Saturday

room.class.out.of.order=Room Class Out of Order
booking.pace.report.total.hotel.group.transient=Booking Pace Report: Total Hotel, Group & Transient
rooms.sold.total=Rooms Sold - Total
occupancy.forecast.total.percent=Occupancy Forecast - Total %
occupancy.forecast.transient.percent=Occupancy Forecast - Transient %
occupancy.forecast.group.percent=Occupancy Forecast - Group %

performance.comparision.chart=Performance Comparison Chart
total.hotel.level=Total Hotel Level
analysis.start.date=Analysis Start Date
analysis.end.date=Analysis End Date
comparision.start.date=Comparision Start Date
comparision.end.date=Comparision End Date
performance.comparision.report=Performance Comparison Report
occupancy.on.books=Occupancy On Books
analysis.period=Analysis Period
comparision.period=Comparison Period
competitor.rate=Competitor Rate
total.group.level=Total Group Level
total.transient.level=Total Transient Level

booking.pace.report.forecast.groups=Booking Pace Report: Forecast Groups
forecast=Forecast
booking.pace.report.market.segments=Booking Pace Report: Market Segments
booking.pace.report.business.views=Booking Pace Report: Business Views

user.activity.log.report=User Activity Log Report
property.group=Property Group
last.used=Last Used
information.manager.module=Information Manager Module
business.analysis.dashboard.module=Business Analysis Dashboard Module
a.glance.dashboard.module=@ A Glance Dashboard Module
demand.and.wash.module=Demand and Wash Module
special.events.module=Special Events Module
overbooking.module=Overbooking Module
pricing.module=Pricing Module
price.strategy.module=Price Strategy Module
property.attributes.module=Property Attributes Module
property.attributes.assigments.module=Property Attributes Assigments Module
property.groups.module=Property Groups Module
corporate.business.views.module=Corporate Business Views Module
room.class.configuration=Room Class Configuration
rate.shopping.configuration.module=Rate Shopping Configuration Module
clients.module=Clients Module
properties.module=Properties Module
market.segmetns.module=Market Segmetns Module
overbooking.configuration.module=Overbooking Configuration Module
property.authorization=Property Authorization
no.of.visits=No Of visits
system.usage.min=System Usage (Min)
average.duration.min=Average Duration (Min)
revenue.forecast=Revenue Forecast
adr.forecast=ADR Forecast
revpar.forecast=RevPAR Forecast

common.chart=Chart

performance.comparison.report.for=Performance Comparison Report for

booking.situation.report.at.hotel.level=Booking Situation Report at Hotel level
analysis.business.date=Analysis Business Date
comparision.business.date=Comparision Business Date
day.of.arrival=Day of Arrival
as.of=as of
variance.occupancy.on.books=Variance Occupancy On Books
revenue.on.books=Revenue On Books
variance.revenue.on.books=Variance Revenue On Books
adr.on.books=ADR On Books
variance.adr.on.books=Variance ADR On Books
revpar.on.books=RevPAR On Books
variance.revpar.on.books=Variance RevPAR On Books
total=TOTAL

booking.situation.report.at.transient.level=Booking Situation Report at Transient level
booking.situation.report.at.group.level=Booking Situation Report at Group level
booking.situation.report.at.forecast.group.level=Booking Situation Report at Forecast Group level
booking.situation.report.at.business.view.level=Booking Situation Report at Business View level
pick.up.report.at.special.rate.plan.level=Pick Up Report at Special Rate Plan Level
side.by.side.view=[Side by Side View]
activity.start.date=Activity Start Date
activity.end.date=Activity End Date
activity.range.pick.up=Activity Range Pick Up
change.report.at.special.rate.plan.level=Change Report at Special Rate Plan Level
change=Change
pick.up.report.at.business.type.level=Pick Up Report at Business Type level
available.in.block=Available in Block
picked.up.from.block=Picked Up from Block

group.arrivals=Group Arrivals
group.departures=Group Departures
seven.day.pickup.variance=7 Day Pickup Variance
group.srp.code=Group SRP Code
rate=Rate

pick.up.report.at.transient.level=Pick Up Report at Transient level
pick.up.report.at.room.type.level=Pick Up Report at Room Type level
pick.up.report.at.forecast.group.level=Pick Up Report at Forecast Group Level
change.and.differential.control.report.room.type=Change and Differential Control Report - Room Type
room.type.out.of.order=RoomType Out of Order
change.and.differential.control.report.forecast.group=Change and Differential Control Report - Forecast Group
change.and.differential.control.report.hotel=Change and Differential Control Report - Hotel
last.room.value.for.RC=Last Room Value For RC
highest.bar.restricted.for.rc=Highest BAR Restricted for RC
change.and.differential.control.report.at.business.type.level=Change and Differential Control Report at Business Type level
change.and.differential.control.report.transient=Change and Differential Control Report - Transient
change.and.differential.control.report.group=Change and Differential Control Report - Group
change.and.differential.control.report.room.class=Change and Differential Control Report - Room Class
read.only.notification.title=Read Only Warning.
read.only.notification.message=The screen is about to go into Read Only. You may want to save your changes.