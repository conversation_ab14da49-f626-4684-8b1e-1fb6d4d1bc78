%macro tetris_seed(property_id=,
                   tetris_lib=,
                   max_los=8,
                   fix_elasticity=1,
                   horizon_grp_cutoff=6);


 data DOW_GRP;
  retain property_id;
  property_id = &property_Id;  
  dow_GRP_id =1;dow_GRP_num=1;dow=1;output;
  dow_GRP_id =1;dow_GRP_num=2;dow=2;output;
  dow_GRP_id =1;dow_GRP_num=3;dow=3;output;
  dow_GRP_id =1;dow_GRP_num=4;dow=4;output;
  dow_GRP_id =1;dow_GRP_num=5;dow=5;output;
  dow_GRP_id =1;dow_GRP_num=6;dow=6;output;
  dow_GRP_id =1;dow_GRP_num=7;dow=7;output;
  
  
  dow_GRP_id =2;dow_GRP_num=1;dow=2;output;
  dow_GRP_id =2;dow_GRP_num=1;dow=3;output;
  dow_GRP_id =2;dow_GRP_num=1;dow=4;output;
  dow_GRP_id =2;dow_GRP_num=1;dow=5;output;
  dow_GRP_id =2;dow_GRP_num=2;dow=6;output;
  dow_GRP_id =2;dow_GRP_num=2;dow=7;output;
  dow_GRP_id =2;dow_GRP_num=2;dow=1;output;
  
  dow_GRP_id =3;dow_GRP_num=1;dow=2;output;
  dow_GRP_id =3;dow_GRP_num=1;dow=3;output;
  dow_GRP_id =3;dow_GRP_num=1;dow=4;output;
  dow_GRP_id =3;dow_GRP_num=1;dow=5;output;
  dow_GRP_id =3;dow_GRP_num=2;dow=6;output;
  dow_GRP_id =3;dow_GRP_num=2;dow=7;output;
  dow_GRP_id =3;dow_GRP_num=1;dow=1;output;
  
 run;
%rm_util_truncate_table(&tetris_lib..dow_GRP);
%rm_util_truncate_table(&tetris_lib..los_GRP);
%rm_util_truncate_table(&tetris_lib..horizon_GRP);
 proc append base=&tetris_lib..dow_GRP data=dow_GRP force;run;
 
 data LOS_GRP;
  retain property_id open_end_flg;
  property_id = &property_Id; 
  open_end_flg =0;
  los_GRP_id =1;los_GRP_num=1;los=1;output;
  los_GRP_id =1;los_GRP_num=1;los=2;output;
  los_GRP_id =1;los_GRP_num=1;los=3;output;
  los_GRP_id =1;los_GRP_num=1;los=4;output;
  los_GRP_id =1;los_GRP_num=1;los=5;output;
  los_GRP_id =1;los_GRP_num=1;los=6;output;
  los_GRP_id =1;los_GRP_num=1;los=7;output;
  
  los_grp_id=2;
  do i = 1 to &max_los;
     los_grp_num=i;
     los=i;
     output;
  end;
  
  los_GRP_id =2;los_GRP_num=1;los=1;output;
  los_GRP_id =2;los_GRP_num=2;los=2;output;
  los_GRP_id =2;los_GRP_num=3;los=3;output;
  los_GRP_id =2;los_GRP_num=4;los=4;output;
  los_GRP_id =2;los_GRP_num=5;los=5;output;
  los_GRP_id =2;los_GRP_num=6;los=6;output;
  los_GRP_id =2;los_GRP_num=7;los=7;output;
  
  
  los_GRP_id =3;los_GRP_num=1;los=1;output;
  los_GRP_id =3;los_GRP_num=1;los=2;output;
  los_GRP_id =3;los_GRP_num=1;los=3;output;
  los_GRP_id =3;los_GRP_num=2;los=4;output;
  los_GRP_id =3;los_GRP_num=2;los=5;output;
  los_GRP_id =3;los_GRP_num=3;los=6;output;
  los_GRP_id =3;los_GRP_num=3;los=7;output;
  
  drop i;
 run;
 proc append base=&tetris_lib..los_GRP data=los_GRP force;run;
 
 
 
 data SEASON_GRP;
  property_id=&property_id;season_GRP_id =1;season_GRP_num=1;start_dt="01JAN2005"D;end_dt="31DEC2005"d;output;
  property_id=&property_id;season_GRP_id =1;season_GRP_num=1;start_dt="01JAN2006"D;end_dt="31DEC2006"d;output;
  property_id=&property_id;season_GRP_id =1;season_GRP_num=1;start_dt="01JAN2007"D;end_dt="31DEC2007"d;output;
  property_id=&property_id;season_GRP_id =1;season_GRP_num=1;start_dt="01JAN2008"D;end_dt="31DEC2008"d;output;
  property_id=&property_id;season_GRP_id =1;season_GRP_num=1;start_dt="01JAN2009"D;end_dt="31DEC2009"d;output;
  property_id=&property_id;season_GRP_id =1;season_GRP_num=1;start_dt="01JAN2010"D;end_dt="31DEC2010"d;output;
  property_id=&property_id;season_GRP_id =1;season_GRP_num=1;start_dt="01JAN2011"D;end_dt="31DEC2011"d;output;
  property_id=&property_id;season_GRP_id =1;season_GRP_num=1;start_dt="01JAN2012"D;end_dt="31DEC2012"d;output;
  property_id=&property_id;season_GRP_id =1;season_GRP_num=1;start_dt="01JAN2013"D;end_dt="31DEC2013"d;output;
  property_id=&property_id;season_GRP_id =1;season_GRP_num=1;start_dt="01JAN2014"D;end_dt="31DEC2014"d;output;

  property_id=&property_id;season_GRP_id =2;season_GRP_num=1;start_dt="01JAN2005"D;end_dt="31JAN2005"d;output;
  property_id=&property_id;season_GRP_id =2;season_GRP_num=1;start_dt="01JAN2006"D;end_dt="31JAN2006"d;output;
  property_id=&property_id;season_GRP_id =2;season_GRP_num=1;start_dt="01JAN2007"D;end_dt="31JAN2007"d;output;
  property_id=&property_id;season_GRP_id =2;season_GRP_num=1;start_dt="01JAN2008"D;end_dt="31JAN2008"d;output;
  property_id=&property_id;season_GRP_id =2;season_GRP_num=1;start_dt="01JAN2009"D;end_dt="31JAN2009"d;output;
  property_id=&property_id;season_GRP_id =2;season_GRP_num=1;start_dt="01JAN2010"D;end_dt="31JAN2010"d;output;
  property_id=&property_id;season_GRP_id =2;season_GRP_num=1;start_dt="01JAN2011"D;end_dt="31JAN2011"d;output;
  property_id=&property_id;season_GRP_id =2;season_GRP_num=1;start_dt="01JAN2012"D;end_dt="31JAN2012"d;output;
  property_id=&property_id;season_GRP_id =2;season_GRP_num=1;start_dt="01JAN2013"D;end_dt="31JAN2013"d;output;
  property_id=&property_id;season_GRP_id =2;season_GRP_num=1;start_dt="01JAN2014"D;end_dt="31JAN2014"d;output;

  property_id=&property_id;season_GRP_id =2;season_GRP_num=2;start_dt="01FEB2005"D;end_dt="28FEB2005"d;output;
  property_id=&property_id;season_GRP_id =2;season_GRP_num=2;start_dt="01FEB2006"D;end_dt="28FEB2006"d;output;
  property_id=&property_id;season_GRP_id =2;season_GRP_num=2;start_dt="01FEB2007"D;end_dt="28FEB2007"d;output;
  property_id=&property_id;season_GRP_id =2;season_GRP_num=2;start_dt="01FEB2008"D;end_dt="29FEB2008"d;output;
  property_id=&property_id;season_GRP_id =2;season_GRP_num=2;start_dt="01FEB2009"D;end_dt="28FEB2009"d;output;
  property_id=&property_id;season_GRP_id =2;season_GRP_num=2;start_dt="01FEB2010"D;end_dt="28FEB2010"d;output;
  property_id=&property_id;season_GRP_id =2;season_GRP_num=2;start_dt="01FEB2011"D;end_dt="28FEB2011"d;output;
  property_id=&property_id;season_GRP_id =2;season_GRP_num=2;start_dt="01FEB2012"D;end_dt="29FEB2012"d;output;
  property_id=&property_id;season_GRP_id =2;season_GRP_num=2;start_dt="01FEB2013"D;end_dt="28FEB2013"d;output;
  property_id=&property_id;season_GRP_id =2;season_GRP_num=2;start_dt="01FEB2014"D;end_dt="28FEB2014"d;output;

  property_id=&property_id;season_GRP_id =2;season_GRP_num=3;start_dt="01MAR2005"D;end_dt="31MAR2005"d;output;
  property_id=&property_id;season_GRP_id =2;season_GRP_num=3;start_dt="01MAR2006"D;end_dt="31MAR2006"d;output;
  property_id=&property_id;season_GRP_id =2;season_GRP_num=3;start_dt="01MAR2007"D;end_dt="31MAR2007"d;output;
  property_id=&property_id;season_GRP_id =2;season_GRP_num=3;start_dt="01MAR2008"D;end_dt="31MAR2008"d;output;
  property_id=&property_id;season_GRP_id =2;season_GRP_num=3;start_dt="01MAR2009"D;end_dt="31MAR2009"d;output;
  property_id=&property_id;season_GRP_id =2;season_GRP_num=3;start_dt="01MAR2010"D;end_dt="31MAR2010"d;output;
  property_id=&property_id;season_GRP_id =2;season_GRP_num=3;start_dt="01MAR2011"D;end_dt="31MAR2011"d;output;
  property_id=&property_id;season_GRP_id =2;season_GRP_num=3;start_dt="01MAR2012"D;end_dt="31MAR2012"d;output;
  property_id=&property_id;season_GRP_id =2;season_GRP_num=3;start_dt="01MAR2013"D;end_dt="31MAR2013"d;output;
  property_id=&property_id;season_GRP_id =2;season_GRP_num=3;start_dt="01MAR2014"D;end_dt="31MAR2014"d;output;

  property_id=&property_id;season_GRP_id =2;season_GRP_num=4;start_dt="01APR2005"D;end_dt="30APR2005"d;output;
  property_id=&property_id;season_GRP_id =2;season_GRP_num=4;start_dt="01APR2006"D;end_dt="30APR2006"d;output;
  property_id=&property_id;season_GRP_id =2;season_GRP_num=4;start_dt="01APR2007"D;end_dt="30APR2007"d;output;
  property_id=&property_id;season_GRP_id =2;season_GRP_num=4;start_dt="01APR2008"D;end_dt="30APR2008"d;output;
  property_id=&property_id;season_GRP_id =2;season_GRP_num=4;start_dt="01APR2009"D;end_dt="30APR2009"d;output;
  property_id=&property_id;season_GRP_id =2;season_GRP_num=4;start_dt="01APR2010"D;end_dt="30APR2010"d;output;
  property_id=&property_id;season_GRP_id =2;season_GRP_num=4;start_dt="01APR2011"D;end_dt="30APR2011"d;output;
  property_id=&property_id;season_GRP_id =2;season_GRP_num=4;start_dt="01APR2012"D;end_dt="30APR2012"d;output;
  property_id=&property_id;season_GRP_id =2;season_GRP_num=4;start_dt="01APR2013"D;end_dt="30APR2013"d;output;
  property_id=&property_id;season_GRP_id =2;season_GRP_num=4;start_dt="01APR2014"D;end_dt="30APR2014"d;output;

  property_id=&property_id;season_GRP_id =2;season_GRP_num=5;start_dt="01MAY2005"D;end_dt="31MAY2005"d;output;
  property_id=&property_id;season_GRP_id =2;season_GRP_num=5;start_dt="01MAY2006"D;end_dt="31MAY2006"d;output;
  property_id=&property_id;season_GRP_id =2;season_GRP_num=5;start_dt="01MAY2007"D;end_dt="31MAY2007"d;output;
  property_id=&property_id;season_GRP_id =2;season_GRP_num=5;start_dt="01MAY2008"D;end_dt="31MAY2008"d;output;
  property_id=&property_id;season_GRP_id =2;season_GRP_num=5;start_dt="01MAY2009"D;end_dt="31MAY2009"d;output;
  property_id=&property_id;season_GRP_id =2;season_GRP_num=5;start_dt="01MAY2010"D;end_dt="31MAY2010"d;output;
  property_id=&property_id;season_GRP_id =2;season_GRP_num=5;start_dt="01MAY2011"D;end_dt="31MAY2011"d;output;
  property_id=&property_id;season_GRP_id =2;season_GRP_num=5;start_dt="01MAY2012"D;end_dt="31MAY2012"d;output;
  property_id=&property_id;season_GRP_id =2;season_GRP_num=5;start_dt="01MAY2013"D;end_dt="31MAY2013"d;output;
  property_id=&property_id;season_GRP_id =2;season_GRP_num=5;start_dt="01MAY2014"D;end_dt="31MAY2014"d;output;

  property_id=&property_id;season_GRP_id =2;season_GRP_num=6;start_dt="01JUN2005"D;end_dt="30JUN2005"d;output;
  property_id=&property_id;season_GRP_id =2;season_GRP_num=6;start_dt="01JUN2006"D;end_dt="30JUN2006"d;output;
  property_id=&property_id;season_GRP_id =2;season_GRP_num=6;start_dt="01JUN2007"D;end_dt="30JUN2007"d;output;
  property_id=&property_id;season_GRP_id =2;season_GRP_num=6;start_dt="01JUN2008"D;end_dt="30JUN2008"d;output;
  property_id=&property_id;season_GRP_id =2;season_GRP_num=6;start_dt="01JUN2009"D;end_dt="30JUN2009"d;output;
  property_id=&property_id;season_GRP_id =2;season_GRP_num=6;start_dt="01JUN2010"D;end_dt="30JUN2010"d;output;
  property_id=&property_id;season_GRP_id =2;season_GRP_num=6;start_dt="01JUN2011"D;end_dt="30JUN2011"d;output;
  property_id=&property_id;season_GRP_id =2;season_GRP_num=6;start_dt="01JUN2012"D;end_dt="30JUN2012"d;output;
  property_id=&property_id;season_GRP_id =2;season_GRP_num=6;start_dt="01JUN2013"D;end_dt="30JUN2013"d;output;
  property_id=&property_id;season_GRP_id =2;season_GRP_num=6;start_dt="01JUN2014"D;end_dt="30JUN2014"d;output;

  property_id=&property_id;season_GRP_id =2;season_GRP_num=7;start_dt="01JUL2005"D;end_dt="31JUL2005"d;output;
  property_id=&property_id;season_GRP_id =2;season_GRP_num=7;start_dt="01JUL2006"D;end_dt="31JUL2006"d;output;
  property_id=&property_id;season_GRP_id =2;season_GRP_num=7;start_dt="01JUL2007"D;end_dt="31JUL2007"d;output;
  property_id=&property_id;season_GRP_id =2;season_GRP_num=7;start_dt="01JUL2008"D;end_dt="31JUL2008"d;output;
  property_id=&property_id;season_GRP_id =2;season_GRP_num=7;start_dt="01JUL2009"D;end_dt="31JUL2009"d;output;
  property_id=&property_id;season_GRP_id =2;season_GRP_num=7;start_dt="01JUL2010"D;end_dt="31JUL2010"d;output;
  property_id=&property_id;season_GRP_id =2;season_GRP_num=7;start_dt="01JUL2011"D;end_dt="31JUL2011"d;output;
  property_id=&property_id;season_GRP_id =2;season_GRP_num=7;start_dt="01JUL2012"D;end_dt="31JUL2012"d;output;
  property_id=&property_id;season_GRP_id =2;season_GRP_num=7;start_dt="01JUL2013"D;end_dt="31JUL2013"d;output;
  property_id=&property_id;season_GRP_id =2;season_GRP_num=7;start_dt="01JUL2014"D;end_dt="31JUL2014"d;output;

  property_id=&property_id;season_GRP_id =2;season_GRP_num=8;start_dt="01AUG2005"D;end_dt="31AUG2005"d;output;
  property_id=&property_id;season_GRP_id =2;season_GRP_num=8;start_dt="01AUG2006"D;end_dt="31AUG2006"d;output;
  property_id=&property_id;season_GRP_id =2;season_GRP_num=8;start_dt="01AUG2007"D;end_dt="31AUG2007"d;output;
  property_id=&property_id;season_GRP_id =2;season_GRP_num=8;start_dt="01AUG2008"D;end_dt="31AUG2008"d;output;
  property_id=&property_id;season_GRP_id =2;season_GRP_num=8;start_dt="01AUG2009"D;end_dt="31AUG2009"d;output;
  property_id=&property_id;season_GRP_id =2;season_GRP_num=8;start_dt="01AUG2010"D;end_dt="31AUG2010"d;output;
  property_id=&property_id;season_GRP_id =2;season_GRP_num=8;start_dt="01AUG2011"D;end_dt="31AUG2011"d;output;
  property_id=&property_id;season_GRP_id =2;season_GRP_num=8;start_dt="01AUG2012"D;end_dt="31AUG2012"d;output;
  property_id=&property_id;season_GRP_id =2;season_GRP_num=8;start_dt="01AUG2013"D;end_dt="31AUG2013"d;output;
  property_id=&property_id;season_GRP_id =2;season_GRP_num=8;start_dt="01AUG2014"D;end_dt="31AUG2014"d;output;

  property_id=&property_id;season_GRP_id =2;season_GRP_num=9;start_dt="01SEP2005"D;end_dt="30SEP2005"d;output;
  property_id=&property_id;season_GRP_id =2;season_GRP_num=9;start_dt="01SEP2006"D;end_dt="30SEP2006"d;output;
  property_id=&property_id;season_GRP_id =2;season_GRP_num=9;start_dt="01SEP2007"D;end_dt="30SEP2007"d;output;
  property_id=&property_id;season_GRP_id =2;season_GRP_num=9;start_dt="01SEP2008"D;end_dt="30SEP2008"d;output;
  property_id=&property_id;season_GRP_id =2;season_GRP_num=9;start_dt="01SEP2009"D;end_dt="30SEP2009"d;output;
  property_id=&property_id;season_GRP_id =2;season_GRP_num=9;start_dt="01SEP2010"D;end_dt="30SEP2010"d;output;
  property_id=&property_id;season_GRP_id =2;season_GRP_num=9;start_dt="01SEP2011"D;end_dt="30SEP2011"d;output;
  property_id=&property_id;season_GRP_id =2;season_GRP_num=9;start_dt="01SEP2012"D;end_dt="30SEP2012"d;output;
  property_id=&property_id;season_GRP_id =2;season_GRP_num=9;start_dt="01SEP2013"D;end_dt="30SEP2013"d;output;
  property_id=&property_id;season_GRP_id =2;season_GRP_num=9;start_dt="01SEP2014"D;end_dt="30SEP2014"d;output;

  property_id=&property_id;season_GRP_id =2;season_GRP_num=10;start_dt="01OCT2005"D;end_dt="31OCT2005"d;output;
  property_id=&property_id;season_GRP_id =2;season_GRP_num=10;start_dt="01OCT2006"D;end_dt="31OCT2006"d;output;
  property_id=&property_id;season_GRP_id =2;season_GRP_num=10;start_dt="01OCT2007"D;end_dt="31OCT2007"d;output;
  property_id=&property_id;season_GRP_id =2;season_GRP_num=10;start_dt="01OCT2008"D;end_dt="31OCT2008"d;output;
  property_id=&property_id;season_GRP_id =2;season_GRP_num=10;start_dt="01OCT2009"D;end_dt="31OCT2009"d;output;
  property_id=&property_id;season_GRP_id =2;season_GRP_num=10;start_dt="01OCT2010"D;end_dt="31OCT2010"d;output;
  property_id=&property_id;season_GRP_id =2;season_GRP_num=10;start_dt="01OCT2011"D;end_dt="31OCT2011"d;output;
  property_id=&property_id;season_GRP_id =2;season_GRP_num=10;start_dt="01OCT2012"D;end_dt="31OCT2012"d;output;
  property_id=&property_id;season_GRP_id =2;season_GRP_num=10;start_dt="01OCT2013"D;end_dt="31OCT2013"d;output;
  property_id=&property_id;season_GRP_id =2;season_GRP_num=10;start_dt="01OCT2014"D;end_dt="31OCT2014"d;output;

  property_id=&property_id;season_GRP_id =2;season_GRP_num=11;start_dt="01NOV2005"D;end_dt="30NOV2005"d;output;
  property_id=&property_id;season_GRP_id =2;season_GRP_num=11;start_dt="01NOV2006"D;end_dt="30NOV2006"d;output;
  property_id=&property_id;season_GRP_id =2;season_GRP_num=11;start_dt="01NOV2007"D;end_dt="30NOV2007"d;output;
  property_id=&property_id;season_GRP_id =2;season_GRP_num=11;start_dt="01NOV2008"D;end_dt="30NOV2008"d;output;
  property_id=&property_id;season_GRP_id =2;season_GRP_num=11;start_dt="01NOV2009"D;end_dt="30NOV2009"d;output;
  property_id=&property_id;season_GRP_id =2;season_GRP_num=11;start_dt="01NOV2010"D;end_dt="30NOV2010"d;output;
  property_id=&property_id;season_GRP_id =2;season_GRP_num=11;start_dt="01NOV2011"D;end_dt="30NOV2011"d;output;
  property_id=&property_id;season_GRP_id =2;season_GRP_num=11;start_dt="01NOV2012"D;end_dt="30NOV2012"d;output;
  property_id=&property_id;season_GRP_id =2;season_GRP_num=11;start_dt="01NOV2013"D;end_dt="30NOV2013"d;output;
  property_id=&property_id;season_GRP_id =2;season_GRP_num=11;start_dt="01NOV2014"D;end_dt="30NOV2014"d;output;

  property_id=&property_id;season_GRP_id =2;season_GRP_num=12;start_dt="01DEC2005"D;end_dt="31DEC2005"d;output;
  property_id=&property_id;season_GRP_id =2;season_GRP_num=12;start_dt="01DEC2006"D;end_dt="31DEC2006"d;output;
  property_id=&property_id;season_GRP_id =2;season_GRP_num=12;start_dt="01DEC2007"D;end_dt="31DEC2007"d;output;
  property_id=&property_id;season_GRP_id =2;season_GRP_num=12;start_dt="01DEC2008"D;end_dt="31DEC2008"d;output;
  property_id=&property_id;season_GRP_id =2;season_GRP_num=12;start_dt="01DEC2009"D;end_dt="31DEC2009"d;output;
  property_id=&property_id;season_GRP_id =2;season_GRP_num=12;start_dt="01DEC2010"D;end_dt="31DEC2010"d;output;
  property_id=&property_id;season_GRP_id =2;season_GRP_num=12;start_dt="01DEC2011"D;end_dt="31DEC2011"d;output;
  property_id=&property_id;season_GRP_id =2;season_GRP_num=12;start_dt="01DEC2012"D;end_dt="31DEC2012"d;output;
  property_id=&property_id;season_GRP_id =2;season_GRP_num=12;start_dt="01DEC2013"D;end_dt="31DEC2013"d;output;
  property_id=&property_id;season_GRP_id =2;season_GRP_num=12;start_dt="01DEC2014"D;end_dt="31DEC2014"d;output;

  property_id=&property_id;season_GRP_id =3;season_GRP_num=1;start_dt="01JAN2005"D;end_dt="31MAR2005"d;output;
  property_id=&property_id;season_GRP_id =3;season_GRP_num=1;start_dt="01JAN2006"D;end_dt="31MAR2006"d;output;
  property_id=&property_id;season_GRP_id =3;season_GRP_num=1;start_dt="01JAN2007"D;end_dt="31MAR2007"d;output;
  property_id=&property_id;season_GRP_id =3;season_GRP_num=1;start_dt="01JAN2008"D;end_dt="31MAR2008"d;output;
  property_id=&property_id;season_GRP_id =3;season_GRP_num=1;start_dt="01JAN2009"D;end_dt="31MAR2009"d;output;
  property_id=&property_id;season_GRP_id =3;season_GRP_num=1;start_dt="01JAN2010"D;end_dt="31MAR2010"d;output;
  property_id=&property_id;season_GRP_id =3;season_GRP_num=1;start_dt="01JAN2011"D;end_dt="31MAR2011"d;output;
  property_id=&property_id;season_GRP_id =3;season_GRP_num=1;start_dt="01JAN2012"D;end_dt="31MAR2012"d;output;
  property_id=&property_id;season_GRP_id =3;season_GRP_num=1;start_dt="01JAN2013"D;end_dt="31MAR2013"d;output;
  property_id=&property_id;season_GRP_id =3;season_GRP_num=1;start_dt="01JAN2014"D;end_dt="31MAR2014"d;output;

  property_id=&property_id;season_GRP_id =3;season_GRP_num=2;start_dt="01APR2005"D;end_dt="30JUN2005"d;output;
  property_id=&property_id;season_GRP_id =3;season_GRP_num=2;start_dt="01APR2006"D;end_dt="30JUN2006"d;output;
  property_id=&property_id;season_GRP_id =3;season_GRP_num=2;start_dt="01APR2007"D;end_dt="30JUN2007"d;output;
  property_id=&property_id;season_GRP_id =3;season_GRP_num=2;start_dt="01APR2008"D;end_dt="30JUN2008"d;output;
  property_id=&property_id;season_GRP_id =3;season_GRP_num=2;start_dt="01APR2009"D;end_dt="30JUN2009"d;output;
  property_id=&property_id;season_GRP_id =3;season_GRP_num=2;start_dt="01APR2010"D;end_dt="30JUN2010"d;output;
  property_id=&property_id;season_GRP_id =3;season_GRP_num=2;start_dt="01APR2011"D;end_dt="30JUN2011"d;output;
  property_id=&property_id;season_GRP_id =3;season_GRP_num=2;start_dt="01APR2012"D;end_dt="30JUN2012"d;output;
  property_id=&property_id;season_GRP_id =3;season_GRP_num=2;start_dt="01APR2013"D;end_dt="30JUN2013"d;output;
  property_id=&property_id;season_GRP_id =3;season_GRP_num=2;start_dt="01APR2014"D;end_dt="30JUN2014"d;output;

  property_id=&property_id;season_GRP_id =3;season_GRP_num=3;start_dt="01JUL2005"D;end_dt="30SEP2005"d;output;
  property_id=&property_id;season_GRP_id =3;season_GRP_num=3;start_dt="01JUL2006"D;end_dt="30SEP2006"d;output;
  property_id=&property_id;season_GRP_id =3;season_GRP_num=3;start_dt="01JUL2007"D;end_dt="30SEP2007"d;output;
  property_id=&property_id;season_GRP_id =3;season_GRP_num=3;start_dt="01JUL2008"D;end_dt="30SEP2008"d;output;
  property_id=&property_id;season_GRP_id =3;season_GRP_num=3;start_dt="01JUL2009"D;end_dt="30SEP2009"d;output;
  property_id=&property_id;season_GRP_id =3;season_GRP_num=3;start_dt="01JUL2010"D;end_dt="30SEP2010"d;output;
  property_id=&property_id;season_GRP_id =3;season_GRP_num=3;start_dt="01JUL2011"D;end_dt="30SEP2011"d;output;
  property_id=&property_id;season_GRP_id =3;season_GRP_num=3;start_dt="01JUL2012"D;end_dt="30SEP2012"d;output;
  property_id=&property_id;season_GRP_id =3;season_GRP_num=3;start_dt="01JUL2013"D;end_dt="30SEP2013"d;output;
  property_id=&property_id;season_GRP_id =3;season_GRP_num=3;start_dt="01JUL2014"D;end_dt="30SEP2014"d;output;

  property_id=&property_id;season_GRP_id =3;season_GRP_num=4;start_dt="01OCT2005"D;end_dt="31DEC2005"d;output;
  property_id=&property_id;season_GRP_id =3;season_GRP_num=4;start_dt="01OCT2006"D;end_dt="31DEC2006"d;output;
  property_id=&property_id;season_GRP_id =3;season_GRP_num=4;start_dt="01OCT2007"D;end_dt="31DEC2007"d;output;
  property_id=&property_id;season_GRP_id =3;season_GRP_num=4;start_dt="01OCT2008"D;end_dt="31DEC2008"d;output;
  property_id=&property_id;season_GRP_id =3;season_GRP_num=4;start_dt="01OCT2009"D;end_dt="31DEC2009"d;output;
  property_id=&property_id;season_GRP_id =3;season_GRP_num=4;start_dt="01OCT2010"D;end_dt="31DEC2010"d;output;
  property_id=&property_id;season_GRP_id =3;season_GRP_num=4;start_dt="01OCT2011"D;end_dt="31DEC2011"d;output;
  property_id=&property_id;season_GRP_id =3;season_GRP_num=4;start_dt="01OCT2012"D;end_dt="31DEC2012"d;output;
  property_id=&property_id;season_GRP_id =3;season_GRP_num=4;start_dt="01OCT2013"D;end_dt="31DEC2013"d;output;
  property_id=&property_id;season_GRP_id =3;season_GRP_num=4;start_dt="01OCT2014"D;end_dt="31DEC2014"d;output;

 run;

 
  %rm_util_truncate_table(&tetris_lib..season_GRP);
 proc append base=&tetris_lib..season_GRP data=season_GRP force;run;
 
   /*
    * Seeding of horizon_grp data 
    */
   data horizon_grp;
      property_id=&property_id;
      horizon_grp_id=1;
      do i = 0 to 90;
         horizon_grp_num=i;
         from_dta=i;
         to_dta=i;
         output;
      end;
      
      horizon_grp_num = 91;
      from_dta=91;
      to_dta=100;
      output;
      
      horizon_grp_num = 92;
      from_dta=101;
      to_dta=120;
       output;
       
      horizon_grp_num = 93;
      from_dta=121;
      to_dta=140;
       output;
       
      horizon_grp_num = 94;
      from_dta=141;
      to_dta=180;
       output;
       
      horizon_grp_num = 95;
      from_dta=181;
      to_dta=364;
       output;
       
       horizon_grp_num = 96;
      from_dta=365;
      to_dta=365;
       output;
   run;
   proc append base=&tetris_lib..horizon_grp data=horizon_grp force;run;
   
   data horizon_grp;
      property_id=&property_id;
      horizon_grp_id=12;
      
         horizon_grp_num=1;
         from_dta=0;
         to_dta=0;
         output;
         horizon_grp_num=2;
         from_dta=1;
         to_dta=1;
         output;
         horizon_grp_num=3;
         from_dta=2;
         to_dta=3;
         output;
         horizon_grp_num=4;
         from_dta=4;
         to_dta=6;
         output;
         horizon_grp_num=5;
         from_dta=7;
         to_dta=9;
         output;
         horizon_grp_num=6;
         from_dta=10;
         to_dta=13;
         output;
         horizon_grp_num=7;
         from_dta=14;
         to_dta=20;
         output;
         horizon_grp_num=8;
         from_dta=21;
         to_dta=27;
         output;
         horizon_grp_num=9;
         from_dta=28;
         to_dta=34;
         output;
         horizon_grp_num=10;
         from_dta=35;
         to_dta=44;
         output;
         horizon_grp_num=11;
         from_dta=45;
         to_dta=59;
         output;
         horizon_grp_num=12;
         from_dta=60;
         to_dta=104;
         output;
         horizon_grp_num=13;
         from_dta=105;
         to_dta=164;
         output;
         horizon_grp_num=14;
         from_dta=165;
         to_dta=224;
         output;
         horizon_grp_num=15;
         from_dta=225;
         to_dta=314;
         output;
         horizon_grp_num=16;
         from_dta=315;
         to_dta=404;
         output;
         horizon_grp_num=17;
         from_dta=405;
         to_dta=584;
         output;
         horizon_grp_num=18;
         from_dta=585;
         to_dta=764;
         output;
         horizon_grp_num=19;
         from_dta=765;
         to_dta=944;
         output;
         horizon_grp_num=20;
         from_dta=945;
         to_dta=1124;
         output;
         horizon_grp_num=21;
         from_dta=1125;
         to_dta=1130;
         output;

   run;
   proc append base=&tetris_lib..horizon_grp data=horizon_grp force;run;
   
   
   data horizon_grp;
   property_id=&property_id;
   horizon_grp_id=2;
   horizon_grp_num=1;
   from_dta=0;
   to_dta=3;
   output;
   horizon_grp_num=2;
   from_dta=4;
   to_dta=15;
   output;
   horizon_grp_num=3;
   from_dta=16;
   to_dta=45;
   output;
   horizon_grp_num=4;
   from_dta=46;
   to_dta=90;
   output;
   run;
   proc append base=&tetris_lib..horizon_grp data=horizon_grp force;run;
   
   data horizon_grp;
   property_id=&property_id;
   horizon_grp_id=3;
   do i = 1 to 9;
      horizon_grp_num=i;
      if i = 1 then do;
         from_dta =0;
         to_dta = 10;
      end;
      else do;
        from_dta = (i-1) * 10 +1;
         to_Dta =  from_dta + 9;
      end;
      output;
   end;
   drop i;
   run;
   proc append base=&tetris_lib..horizon_grp data=horizon_grp force;run;
   
   data horizon_grp;
   property_id=&property_id;
   horizon_grp_id=10;
   horizon_grp_num=1;
   from_dta=0;
   to_dta=3;
   output;
   horizon_grp_num=2;
   from_dta=4;
   to_dta=90;
   output;
   run;
    proc append base=&tetris_lib..horizon_grp data=horizon_grp force;run;
   data horizon_grp;
   property_id=&property_id;
   horizon_grp_id=100;
   horizon_grp_num=1;
   from_dta=0;
   to_dta=90;
   output;
   run;
   proc append base=&tetris_lib..horizon_grp data=horizon_grp force;run;
   
   data horizon_grp;
      property_id=&property_id;
      horizon_grp_id=99;
      horizon_grp_num=1;
      from_dta=0;
      to_dta=&horizon_grp_cutoff;
      output;
      property_id=&property_id;
      horizon_grp_id=99;
      horizon_grp_num=2;
      from_dta=to_dta+1;
      to_dta=365;
      output;
   
   run;
   proc append base=&tetris_lib..horizon_grp data=horizon_grp force;run;
   
   
   data model_config;
       length name $50;
       length value$50;
       property_id = &property_id;
       config_id =1;
       
       name='SEASON_GRP_ID';
       value='1';
       output; 
   
       name='DOW_GRP_ID';
       value='1';
       output;
       
       name='LOS_GRP_ID';
       value='2';
       output;
       
       name='HORIZON_GRP_ID';
       value='99';
       output;
       
       name='READING_DAY_GRP_ID';
       value='1';
       output;
       
       config_id =2;
       
       name='SEASON_GRP_ID';
       value='1';
       output; 
   
       name='DOW_GRP_ID';
       value='1';
       output;
       
       name='LOS_GRP_ID';
       value='2';
       output;
       
       name='HORIZON_GRP_ID';
       value='99';
       output;
       
       name='READING_DAY_GRP_ID';
       value='1';
       output;
       
       
       config_id =3;
       
       name='SEASON_GRP_ID';
       value='2';
       output; 
   
       name='DOW_GRP_ID';
       value='3';
       output;
       
       name='LOS_GRP_ID';
       value='2';
       output;
       
       name='HORIZON_GRP_ID';
       value='99';
       output;
       
       name='READING_DAY_GRP_ID';
       value='12';
       output;
       
   run;
  %rm_util_truncate_table(&tetris_lib..model_config);
   proc append base=&tetris_lib..model_config data=model_config force;run;
  
   data model_type_list;
      model_type=1;
      name='UNQUALIFIED';
      output;
      model_type=2;
      name='QUALIFIED';
      output;
      model_type=3;
      name='GROUP';
      output;
      model_type=4;
      name='TRANSIENT_BLOCK';
      output;
      model_type=5;
      name='NOFCST';
      output;
      model_type=6;
      name='NOFCST_WASH';
      output;
   run;
%rm_util_truncate_table(&tetris_lib..model_type_list);
   proc append base=&tetris_lib..model_type_list data=model_type_list force;run;
   
   data task_name_list;
      length task_name $30;
      task_name='RATE_FCST';
      name=task_name;
      output;
      task_name='UNCON';
      name=task_name;
      output;
      task_name='LOS_CALIB';
      name=task_name;
      output;
      task_name='BOOKING_CURVE';
      name=task_name;
      output;
      task_name='CANCEL';
      name=task_name;
      output;
      task_name='NO_SHOW';
      name=task_name;
      output;
      task_name='UNQ_ELASTICITY';
      name=task_name;
      output;
      task_name='UNQ_BASE_DMD';
      name=task_name;
      output;
      task_name='UNQ_FCST';
      name=task_name;
      output;
      task_name='GRP_BASE_DMD';
      name=task_name;
      output;
      task_name='QUAL_BASE_DMD';
      name=task_name;
      output;
      task_name='EVENT_CALIB';
      name=task_name;
      output;
   run;
   %rm_util_truncate_table(&tetris_lib..task_name_list);
   proc append base=&tetris_lib..task_name_list data=task_name_list force;run;
   
   %if "&fix_elasticity" ne "1" %then %do;   
   
 
       data work.elsd_scoring_param_default;
       attrib ELSD_MODEL_TYPE                     length=8                                            ;
       attrib MODEL_PARAM_NM                      length=$30                                           ;
       attrib MODEL_PARAM_EST                     length=8                                          ;
       attrib MODEL_PARAM_EST_U                   length=8                                            ;
       attrib MODEL_PARAM_EST_L                   length=8                                            ;
       ELSD_MODEL_TYPE = 2; MODEL_PARAM_NM = 'PRICE_PHB_RATIO'; MODEL_PARAM_EST = -1; MODEL_PARAM_EST_U = -1; MODEL_PARAM_EST_L = -3; output;
       ELSD_MODEL_TYPE = 2; MODEL_PARAM_NM = 'PRICE_PHB_RATIO'; MODEL_PARAM_EST = -1; MODEL_PARAM_EST_U = -1; MODEL_PARAM_EST_L = -3; output;
       ELSD_MODEL_TYPE = 2; MODEL_PARAM_NM = 'PRICE_PHB_RATIO'; MODEL_PARAM_EST = -1; MODEL_PARAM_EST_U = -1; MODEL_PARAM_EST_L = -3; output;
       ELSD_MODEL_TYPE = 5; MODEL_PARAM_NM = 'PRICE_PHB_RATIO'; MODEL_PARAM_EST = -1; MODEL_PARAM_EST_U = -1; MODEL_PARAM_EST_L = -3; output;
       run;
  
   
   %end;
   %else %do;
   
      data work.elsd_scoring_param_default;
      attrib ELSD_MODEL_TYPE                     length=8                                            ;
      attrib MODEL_PARAM_NM                      length=$30                                           ;
      attrib MODEL_PARAM_EST                     length=8                                          ;
      attrib MODEL_PARAM_EST_U                   length=8                                            ;
      attrib MODEL_PARAM_EST_L                   length=8                                            ;
      ELSD_MODEL_TYPE = 2; MODEL_PARAM_NM = 'PRICE_PHB_RATIO'; MODEL_PARAM_EST = -1.16; MODEL_PARAM_EST_U = -1.17; MODEL_PARAM_EST_L = -1.18; output;
      ELSD_MODEL_TYPE = 2; MODEL_PARAM_NM = 'PRICE_PHB_RATIO'; MODEL_PARAM_EST = -1.16; MODEL_PARAM_EST_U = -1.17; MODEL_PARAM_EST_L = -1.18; output;
      ELSD_MODEL_TYPE = 2; MODEL_PARAM_NM = 'PRICE_PHB_RATIO'; MODEL_PARAM_EST = -1.16; MODEL_PARAM_EST_U = -1.17; MODEL_PARAM_EST_L = -1.18; output;
      ELSD_MODEL_TYPE = 5; MODEL_PARAM_NM = 'PRICE_PHB_RATIO'; MODEL_PARAM_EST = -1.16; MODEL_PARAM_EST_U = -1.17; MODEL_PARAM_EST_L = -1.18; output;
      run;
   
   %end;
   %rm_util_truncate_table(&tetris_lib..elsd_scoring_param_default);
   proc append base=&tetris_lib..elsd_scoring_param_default
               data=work.elsd_scoring_param_default force;
   run;
   
%mend;   