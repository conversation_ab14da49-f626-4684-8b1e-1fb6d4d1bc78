%macro alter_10(LIBREF=,part_lib=);

     %if %sysfunc(exist(&LIBREF..bookdates)) %then %do;
        proc sql;
           alter table &LIBREF..bookdates drop CONSTRAINT prim_key_bookdates;
           alter table &LIBREF..bookdates modify resid char(14);
           alter table &LIBREF..bookdates add CONSTRAINT prim_key_bookdates PRIMARY KEY(hotelid, resid);
        run;
     %end;

     %if %sysfunc(exist(&LIBREF..occdates)) %then %do;
         proc sql;
           /*occdates*/
           alter table &LIBREF..occdates drop CONSTRAINT prim_key_occdates;
           alter table &LIBREF..occdates modify resid char(14);
           alter table &LIBREF..occdates add CONSTRAINT prim_key_occdates PRIMARY KEY(hotelid,resid,occdate);
         run;
     %end;

    %if %sysfunc(exist(&LIBREF..books)) %then %do;
        proc sql;
		alter table &LIBREF..books drop CONSTRAINT prim_key_books;
		alter table &LIBREF..books modify resid char(14);
		alter table &LIBREF..books modify roomtype char(21);
		alter table &LIBREF..books modify srp char(21);
		alter table &LIBREF..books modify oldroomtype char(21);
        alter table &LIBREF..books modify ratelevel char(14);
		alter table &LIBREF..books add CONSTRAINT prim_key_books PRIMARY KEY(hotelid, resid);
        run;
    %end;

    %if %sysfunc(exist(&LIBREF..cattotal)) %then %do;
        proc sql;
            /*cattotal*/
            alter table &LIBREF..cattotal drop CONSTRAINT prim_key_cattotal;
            alter table &LIBREF..cattotal modify roomtype char(21);
            alter table &LIBREF..cattotal add CONSTRAINT prim_key_cattotal PRIMARY KEY(hotelid, repdate, roomtype);
        run;
    %end;

    %if %sysfunc(exist(&LIBREF..csegment)) %then %do;
        proc sql;
            /*csegment*/
            alter table &LIBREF..csegment drop CONSTRAINT prim_key_csegment;
            alter table &LIBREF..csegment modify roomtype char(21);
            alter table &LIBREF..csegment add CONSTRAINT prim_key_csegment PRIMARY KEY(hotelid, repdate, segment, roomtype);
        run;
    %end;

    %if %sysfunc(exist(&LIBREF..fplos)) %then %do;
        proc sql;
    		/*fplos*/
            alter table &LIBREF..fplos drop CONSTRAINT prim_key_fplos;
            alter table &LIBREF..fplos modify srpid char(21);
            alter table &LIBREF..fplos modify roomtype char(21);
            alter table &LIBREF..fplos add CONSTRAINT prim_key_fplos PRIMARY KEY(hotelid, Idate, srpid, roomtype);
        run;
    %end;

    %if %sysfunc(exist(&LIBREF..grpbooking)) %then %do;
        proc sql;
            /*grpbooking*/
            alter table &LIBREF..grpbooking drop CONSTRAINT prim_key_grpbooking;
            alter table &LIBREF..grpbooking modify srpid char(21);
            alter table &LIBREF..grpbooking add CONSTRAINT prim_key_grpbooking PRIMARY KEY(hotelid, srpID);
        run;
    %end;

    %if %sysfunc(exist(&LIBREF..grpcvt)) %then %do;
        proc sql;
            /*grpcvt*/
            alter table &LIBREF..grpcvt drop CONSTRAINT prim_key_grpcvt;
            alter table &LIBREF..grpcvt modify srpid char(21);
            alter table &LIBREF..grpcvt add CONSTRAINT prim_key_grpcvt PRIMARY KEY(hotelid, Idate, SRPID);
        run;
    %end;

    %if %sysfunc(exist(&LIBREF..multiyield)) %then %do;
        proc sql;
		/*multiyield*/
            alter table &LIBREF..multiyield drop CONSTRAINT prim_key_multiyield;
            alter table &LIBREF..multiyield modify resid char(14);
            alter table &LIBREF..multiyield modify oresid char(14);
            alter table &LIBREF..multiyield modify new_level char(14);
            alter table &LIBREF..multiyield modify old_level char(14);
            alter table &LIBREF..multiyield modify new_srp char(21);
            alter table &LIBREF..multiyield modify old_srp char(21);
            alter table &LIBREF..multiyield modify new_type char(21);
            alter table &LIBREF..multiyield modify old_type char(21);
            alter table &LIBREF..multiyield add CONSTRAINT prim_key_multiyield PRIMARY KEY(hotelid, resid);
        run;
    %end;

    %if %sysfunc(exist(&LIBREF..rates)) %then %do;
        proc sql;
            /*rates*/
            alter table &LIBREF..rates drop CONSTRAINT prim_key_rates;
            alter table &LIBREF..rates modify srpid char(21);
            alter table &LIBREF..rates modify roomtype char(21);
            alter table &LIBREF..rates add CONSTRAINT prim_key_rates PRIMARY KEY(HotelID, srpId, roomType, startDate, endDate);
        run;
    %end;

    %if %sysfunc(exist(&LIBREF..rmsrates)) %then %do;
        proc sql;
            /*rmsrates*/
            alter table &LIBREF..rmsrates drop CONSTRAINT prim_key_rmsrates;
            alter table &LIBREF..rmsrates modify srpid char(21);
            alter table &LIBREF..rmsrates add CONSTRAINT prim_key_rmsrates PRIMARY KEY(HotelID, srpId, startDate, endDate);
        run;
    %end;

    %if %sysfunc(exist(&LIBREF..rmtype)) %then %do;
        proc sql;
            /*rmtype*/
            alter table &LIBREF..rmtype drop CONSTRAINT prim_key_rmtype;
            alter table &LIBREF..rmtype modify rmtype char(21);
            alter table &LIBREF..rmtype add CONSTRAINT prim_key_rmtype PRIMARY KEY(HotelID, RmType, Idate);
        run;
    %end;

    %if %sysfunc(exist(&LIBREF..srpcnt)) %then %do;
        proc sql;
            /*srpcnt*/
            alter table &LIBREF..srpcnt drop CONSTRAINT prim_key_srpcnt;
            alter table &LIBREF..srpcnt modify srpid char(21);
            alter table &LIBREF..srpcnt add CONSTRAINT prim_key_srpcnt PRIMARY KEY(hotelid, SRPID, Idate);
        run;
    %end;

    %if %sysfunc(exist(&LIBREF..srprm)) %then %do;
        proc sql;
            /*srprm*/
            alter table &LIBREF..srprm drop CONSTRAINT prim_key_srprm;
            alter table &LIBREF..srprm modify srpid char(21);
            alter table &LIBREF..srprm modify roomtype char(21);
            alter table &LIBREF..srprm add CONSTRAINT prim_key_srprm PRIMARY KEY(HotelID, SRPID, RoomType, Idate);
        run;
    %end;

    %if %sysfunc(exist(&LIBREF..srprmpd)) %then %do;
        proc sql;
            /*srprmpd*/
            alter table &LIBREF..srprmpd drop CONSTRAINT prim_key_srprmpd;
            alter table &LIBREF..srprmpd modify srpid char(21);
            alter table &LIBREF..srprmpd modify roomtype char(21);
            alter table &LIBREF..srprmpd add CONSTRAINT prim_key_srprmpd PRIMARY KEY(HotelID, SRPID, RoomType, Idate);
        run;
    %end;

    %if %sysfunc(exist(&LIBREF..yield)) %then %do;
        proc sql;
            /*yield*/
            alter table &LIBREF..yield modify resid char(14);
            alter table &LIBREF..yield modify oresid char(14);
            alter table &LIBREF..yield modify new_level char(14);
            alter table &LIBREF..yield modify old_level char(14);
            alter table &LIBREF..yield modify new_srp char(21);
            alter table &LIBREF..yield modify old_srp char(21);
            alter table &LIBREF..yield modify new_type char(21);
            alter table &LIBREF..yield modify old_type char(21);
        run;
    %end;
%mend;
