<?xml version="1.0" encoding="UTF-8"?>

<!-- ############################################################ -->
<!-- 2011-05-16T16:27:41 -->
<!-- copied and modified from other map -->
<!-- Generated by XML Mapper, 902000.3.6.20090116170000_v920 -->
<!-- ############################################################ -->
<!-- ###  Validation report                                   ### -->
<!-- ############################################################ -->
<!-- XMLMap validation completed successfully. -->
<!-- ############################################################ -->
<SXLEMAP name="AUTO_GEN" version="1.9">

    <!-- ############################################################ -->
    <TABLE description="SASRequest" name="SASRequest">
        <TABLE-PATH syntax="XPath">/SASRequest</TABLE-PATH>

        <COLUMN class="ORDINAL" name="SASRequest_ORDINAL">
            <DESCRIPTION>SASRequest_ORDINAL</DESCRIPTION>
            <INCREMENT-PATH beginend="BEGIN" syntax="XPath">/SASRequest</INCREMENT-PATH>
            <TYPE>numeric</TYPE>
            <DATATYPE>integer</DATATYPE>
        </COLUMN>

        <COLUMN name="SASRequest">
            <PATH syntax="XPath">/SASRequest</PATH>
            <DESCRIPTION>SASRequest</DESCRIPTION>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>32</LENGTH>
        </COLUMN>

    </TABLE>

    <!-- ############################################################ -->
    <TABLE description="RequestHeader" name="RequestHeader">
        <TABLE-PATH syntax="XPath">/SASRequest/RequestHeader</TABLE-PATH>

        <COLUMN class="ORDINAL" name="SASRequest_ORDINAL">
            <DESCRIPTION>SASRequest_ORDINAL</DESCRIPTION>
            <INCREMENT-PATH beginend="BEGIN" syntax="XPath">/SASRequest</INCREMENT-PATH>
            <TYPE>numeric</TYPE>
            <DATATYPE>integer</DATATYPE>
        </COLUMN>

        <COLUMN class="ORDINAL" name="RequestHeader_ORDINAL">
            <DESCRIPTION>RequestHeader_ORDINAL</DESCRIPTION>
            <INCREMENT-PATH beginend="BEGIN" syntax="XPath">/SASRequest/RequestHeader</INCREMENT-PATH>
            <TYPE>numeric</TYPE>
            <DATATYPE>integer</DATATYPE>
        </COLUMN>

        <COLUMN name="PropertyId">
            <PATH syntax="XPath">/SASRequest/RequestHeader/PropertyId</PATH>
            <DESCRIPTION>PropertyId</DESCRIPTION>
            <TYPE>numeric</TYPE>
            <DATATYPE>integer</DATATYPE>
        </COLUMN>

        <COLUMN name="operationName">
            <PATH syntax="XPath">/SASRequest/RequestHeader/operationName</PATH>
            <DESCRIPTION>operationName</DESCRIPTION>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>32</LENGTH>
        </COLUMN>

        <COLUMN name="resume">
            <PATH syntax="XPath">/SASRequest/RequestHeader/resume</PATH>
            <DESCRIPTION>resume</DESCRIPTION>
            <TYPE>numeric</TYPE>
            <DATATYPE>integer</DATATYPE>
        </COLUMN>

        <COLUMN name="requestId">
            <PATH syntax="XPath">/SASRequest/RequestHeader/requestId</PATH>
            <DESCRIPTION>requestId</DESCRIPTION>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>50</LENGTH>
        </COLUMN>

        <COLUMN name="tenant_server">
            <PATH syntax="XPath">/SASRequest/RequestHeader/tenant_server</PATH>
            <DESCRIPTION>tenant_server</DESCRIPTION>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>256</LENGTH>
        </COLUMN>

        <COLUMN name="tenant_server_instance">
            <PATH syntax="XPath">/SASRequest/RequestHeader/tenant_server_instance</PATH>
            <DESCRIPTION>tenant_server_instance</DESCRIPTION>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>32</LENGTH>
        </COLUMN>

        <COLUMN name="tenant_password">
            <PATH syntax="XPath">/SASRequest/RequestHeader/tenant_password</PATH>
            <DESCRIPTION>tenant_password</DESCRIPTION>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>32</LENGTH>
        </COLUMN>

        <COLUMN name="tenant_user">
            <PATH syntax="XPath">/SASRequest/RequestHeader/tenant_user</PATH>
            <DESCRIPTION>tenant_user</DESCRIPTION>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>32</LENGTH>
        </COLUMN>

        <COLUMN name="tenant_database">
            <PATH syntax="XPath">/SASRequest/RequestHeader/tenant_database</PATH>
            <DESCRIPTION>tenant_database</DESCRIPTION>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>32</LENGTH>
        </COLUMN>

        <COLUMN name="tenant_db_port">
            <PATH syntax="XPath">/SASRequest/RequestHeader/tenant_db_port</PATH>
            <DESCRIPTION>tenant_db_port</DESCRIPTION>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>32</LENGTH>
        </COLUMN>

        <COLUMN name="dataset_path">
            <PATH syntax="XPath">/SASRequest/RequestHeader/dataset_path</PATH>
            <DESCRIPTION>dataset_path</DESCRIPTION>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>255</LENGTH>
        </COLUMN>

        <COLUMN name="componentRoom">
            <PATH syntax="XPath">/SASRequest/RequestHeader/componentRoom</PATH>
            <DESCRIPTION>componentRoom</DESCRIPTION>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>8</LENGTH>
        </COLUMN>
		
       <COLUMN name="sas_debug">
            <PATH syntax="XPath">/SASRequest/RequestHeader/sas_debug</PATH>
            <DESCRIPTION>sas_debug</DESCRIPTION>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>8</LENGTH>
        </COLUMN>

        <COLUMN name="rra">
            <PATH syntax="XPath">/SASRequest/RequestHeader/rra</PATH>
            <DESCRIPTION>rra</DESCRIPTION>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>8</LENGTH>
        </COLUMN>

        <COLUMN name="RequestHeader">
            <PATH syntax="XPath">/SASRequest/RequestHeader</PATH>
            <DESCRIPTION>RequestHeader</DESCRIPTION>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>32</LENGTH>
        </COLUMN>

    </TABLE>

    <!-- ############################################################ -->
    <TABLE description="UpdateDDLRequest" name="UpdateDDLRequest">
        <TABLE-PATH syntax="XPath">/SASRequest/UpdateDDLRequest</TABLE-PATH>

        <COLUMN class="ORDINAL" name="SASRequest_ORDINAL">
            <DESCRIPTION>SASRequest_ORDINAL</DESCRIPTION>
            <INCREMENT-PATH beginend="BEGIN" syntax="XPath">/SASRequest</INCREMENT-PATH>
            <TYPE>numeric</TYPE>
            <DATATYPE>integer</DATATYPE>
        </COLUMN>

        <COLUMN class="ORDINAL" name="UpdateDDLRequest_ORDINAL">
            <DESCRIPTION>UpdateDDLRequest_ORDINAL</DESCRIPTION>
            <INCREMENT-PATH beginend="BEGIN" syntax="XPath">/SASRequest/UpdateDDLRequest</INCREMENT-PATH>
            <TYPE>numeric</TYPE>
            <DATATYPE>integer</DATATYPE>
        </COLUMN>

        <COLUMN name="scriptPath">
            <PATH syntax="XPath">/SASRequest/UpdateDDLRequest/scriptPath</PATH>
            <DESCRIPTION>scriptPath</DESCRIPTION>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>150</LENGTH>
        </COLUMN>

       <COLUMN name="rmRefresh">
            <PATH syntax="XPath">/SASRequest/UpdateDDLRequest/rmRefresh</PATH>
            <DESCRIPTION>rmRefresh</DESCRIPTION>
            <TYPE>numeric</TYPE>
            <DATATYPE>integer</DATATYPE>
        </COLUMN>
        
        <COLUMN name="UpdateDDLRequest">
            <PATH syntax="XPath">/SASRequest/UpdateDDLRequest</PATH>
            <DESCRIPTION>UpdateDDLRequest</DESCRIPTION>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>32</LENGTH>
        </COLUMN>

    </TABLE>

</SXLEMAP>
