<?xml version="1.0" encoding="windows-1252"?>

<!-- ############################################################ -->
<!-- 2010-12-13T15:07:43 -->
<!-- SAS XML Libname Engine Map -->
<!-- Generated by XML Mapper, 902000.3.6.20090116170000_v920 -->
<!-- ############################################################ -->
<!-- ###  Validation report                                   ### -->
<!-- ############################################################ -->
<!-- XMLMap validation completed successfully. -->
<!-- ############################################################ -->
<SXLEMAP name="AUTO_GEN" version="1.9">

    <!-- ############################################################ -->
    <TABLE description="SASResponse" name="SASResponse">
        <TABLE-PATH syntax="XPath">/SASResponse</TABLE-PATH>

        <COLUMN class="ORDINAL" name="SASResponse_ORDINAL">
            <DESCRIPTION>SASResponse_ORDINAL</DESCRIPTION>
            <INCREMENT-PATH beginend="BEGIN" syntax="XPath">/SASResponse</INCREMENT-PATH>
            <TYPE>numeric</TYPE>
            <DATATYPE>integer</DATATYPE>
        </COLUMN>

        <COLUMN name="SASResponse">
            <PATH syntax="XPath">/SASResponse</PATH>
            <DESCRIPTION>SASResponse</DESCRIPTION>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>32</LENGTH>
        </COLUMN>

    </TABLE>

    <!-- ############################################################ -->
    <TABLE description="ResponseHeader" name="ResponseHeader">
        <TABLE-PATH syntax="XPath">/SASResponse/ResponseHeader</TABLE-PATH>

        <COLUMN class="ORDINAL" name="SASResponse_ORDINAL">
            <DESCRIPTION>SASResponse_ORDINAL</DESCRIPTION>
            <INCREMENT-PATH beginend="BEGIN" syntax="XPath">/SASResponse</INCREMENT-PATH>
            <TYPE>numeric</TYPE>
            <DATATYPE>integer</DATATYPE>
        </COLUMN>

        <COLUMN class="ORDINAL" name="ResponseHeader_ORDINAL">
            <DESCRIPTION>ResponseHeader_ORDINAL</DESCRIPTION>
            <INCREMENT-PATH beginend="BEGIN" syntax="XPath">/SASResponse/ResponseHeader</INCREMENT-PATH>
            <TYPE>numeric</TYPE>
            <DATATYPE>integer</DATATYPE>
        </COLUMN>

        <COLUMN name="PropertyId">
            <PATH syntax="XPath">/SASResponse/ResponseHeader/PropertyId</PATH>
            <DESCRIPTION>PropertyId</DESCRIPTION>
            <TYPE>numeric</TYPE>
            <DATATYPE>integer</DATATYPE>
        </COLUMN>

        <COLUMN name="operationName">
            <PATH syntax="XPath">/SASResponse/ResponseHeader/operationName</PATH>
            <DESCRIPTION>operationName</DESCRIPTION>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>32</LENGTH>
        </COLUMN>

        <COLUMN name="requestId">
            <PATH syntax="XPath">/SASResponse/ResponseHeader/requestId</PATH>
            <DESCRIPTION>requestId</DESCRIPTION>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>32</LENGTH>
        </COLUMN>

        <COLUMN name="ResponseHeader">
            <PATH syntax="XPath">/SASResponse/ResponseHeader</PATH>
            <DESCRIPTION>ResponseHeader</DESCRIPTION>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>32</LENGTH>
        </COLUMN>

    </TABLE>

    <!-- ############################################################ -->
    <TABLE description="Errors" name="Errors">
        <TABLE-PATH syntax="XPath">/SASResponse/ResponseHeader/Errors</TABLE-PATH>

        <COLUMN class="ORDINAL" name="ResponseHeader_ORDINAL">
            <DESCRIPTION>ResponseHeader_ORDINAL</DESCRIPTION>
            <INCREMENT-PATH beginend="BEGIN" syntax="XPath">/SASResponse/ResponseHeader</INCREMENT-PATH>
            <TYPE>numeric</TYPE>
            <DATATYPE>integer</DATATYPE>
        </COLUMN>

        <COLUMN class="ORDINAL" name="Errors_ORDINAL">
            <DESCRIPTION>Errors_ORDINAL</DESCRIPTION>
            <INCREMENT-PATH beginend="BEGIN" syntax="XPath">/SASResponse/ResponseHeader/Errors</INCREMENT-PATH>
            <TYPE>numeric</TYPE>
            <DATATYPE>integer</DATATYPE>
        </COLUMN>

        <COLUMN name="Errors">
            <PATH syntax="XPath">/SASResponse/ResponseHeader/Errors</PATH>
            <DESCRIPTION>Errors</DESCRIPTION>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>32</LENGTH>
        </COLUMN>

    </TABLE>

    <!-- ############################################################ -->
    <TABLE description="Error" name="Error">
        <TABLE-PATH syntax="XPath">/SASResponse/ResponseHeader/Errors/Errors</TABLE-PATH>

        <COLUMN class="ORDINAL" name="Errors_ORDINAL">
            <DESCRIPTION>Errors_ORDINAL</DESCRIPTION>
            <INCREMENT-PATH beginend="BEGIN" syntax="XPath">/SASResponse/ResponseHeader/Errors</INCREMENT-PATH>
            <TYPE>numeric</TYPE>
            <DATATYPE>integer</DATATYPE>
        </COLUMN>

        <COLUMN class="ORDINAL" name="Error_ORDINAL">
            <DESCRIPTION>Error_ORDINAL</DESCRIPTION>
            <INCREMENT-PATH beginend="BEGIN" syntax="XPath">/SASResponse/ResponseHeader/Errors/Errors</INCREMENT-PATH>
            <TYPE>numeric</TYPE>
            <DATATYPE>integer</DATATYPE>
        </COLUMN>

        <COLUMN name="id">
            <PATH syntax="XPath">/SASResponse/ResponseHeader/Errors/Errors/@id</PATH>
            <DESCRIPTION>id</DESCRIPTION>
            <TYPE>numeric</TYPE>
            <DATATYPE>integer</DATATYPE>
        </COLUMN>

        <COLUMN name="ErrorMessage">
            <PATH syntax="XPath">/SASResponse/ResponseHeader/Errors/Errors/ErrorMessage</PATH>
            <DESCRIPTION>ErrorMessage</DESCRIPTION>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>32</LENGTH>
        </COLUMN>

        <COLUMN name="ErrorData">
            <PATH syntax="XPath">/SASResponse/ResponseHeader/Errors/Errors/ErrorData</PATH>
            <DESCRIPTION>ErrorData</DESCRIPTION>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>32</LENGTH>
        </COLUMN>

        <COLUMN name="Error">
            <PATH syntax="XPath">/SASResponse/ResponseHeader/Errors/Errors</PATH>
            <DESCRIPTION>Error</DESCRIPTION>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>32</LENGTH>
        </COLUMN>

    </TABLE>

</SXLEMAP>
