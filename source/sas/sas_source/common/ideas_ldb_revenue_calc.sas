%macro ideas_ldb_revenue_calc()/store;
	%let macroname=&SYSMACRONAME;
    %ideas_macro_log (start_end=START, macroname=&macroname.);

	proc sql;
	  create table &work_lib..unqualified_rates as
		select avgrate.Accom_Type_ID, MAX(rate.Sunday) as median_rate from(
		(select Accom_Type_ID,avg(Sunday) as sunday_avg
		from tenant.Rate_Unqualified_Details
		where End_Date_DT >= %sysfunc(today()) and Sunday > 0
		group by Accom_Type_ID) as avgrate
		join tenant.Rate_Unqualified_Details as rate on
		avgrate.Accom_Type_ID = rate.Accom_Type_ID) where rate.Sunday <= sunday_avg group by avgrate.Accom_Type_ID;
	quit;

	proc sql;
	  create table &work_lib..accom_class_unqualified_rates as
	   select a.Accom_Type_Capacity,a.Accom_Class_ID,b.*,(a.Accom_Type_Capacity * b.median_rate) as accom_capacity_rate
		from tenant.Accom_Type as a inner join &work_lib..unqualified_rates as b
	   on a.Accom_Type_ID = b.Accom_Type_ID where Status_ID=1 order by a.Accom_Class_ID,b.Accom_Type_ID;
	quit;

	proc sql;
	  create table &work_lib..accom_class_apportioned_adr as select Accom_Class_ID, SUM(Accom_Type_Capacity) as accom_class_capacity,
		(SUM(accom_capacity_rate) / SUM(Accom_Type_Capacity)) as apportioned_adr from &work_lib..accom_class_unqualified_rates group by Accom_Class_ID;
	quit;

	%let macroname=&SYSMACRONAME;
    %ideas_macro_log (start_end=END, macroname=&macroname.);
%mend ideas_ldb_revenue_calc;
