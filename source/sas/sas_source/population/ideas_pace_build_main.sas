%macro ideas_pace_build_main(lib=in_xml)/store;

options sortsize=3g;
    %Global process_id first_snapshot_dt First_Snapshot_dttm pid ;
    %Global property_desc property_desc_short status_desc app_start_time app_end_time load_id Backfill_future_window_size
        SasDatasetsOnly preserveHotelAccomPace useReservationNight isRunningAsPartOfPMSRevamp RequestedRoomTypeOption RequestedNumberPaceDays RequestedNumberoccDays RequestedMarketSegments selectedmarketsegments GROUPMKTSEGLIST groupupdatecount groupmslist rmpartlist
        mscommacount MSSORTCOMMALIST mssortlist updatemslist accomsortlist accomsortcommalist RequestPaceBuildLastDay TotalRateEnabled UsePacmanData IncludeDepartureAdjustment skipGroupPercentValidation overridePacePastBuildDays useBookingDtAsCutoffDt useGroupsAsTrans
        Stayed_minimum_occupancy_dt Booked_minimum_occupancy_dt booked_start_dt ProcessedNumberPaceDays minimum_occupancy_dt minimum_snapshot_dt maximum_occupancy_dt curSnapshotDtMinus365 non_pace_dataset_list stopPopulatingPartitionsTable;
    %Global sqlrc status etl_file_status startupruntime mktsegruntime accomtyperuntime Group_Booked_Difference_Percent Booked_Stayed_Difference_Percent Booked_Stayed_Accom_Differences BookedPaceTablesUpdated StayedPaceTablesUpdated SasStayedPaceProcessingTime SasBookedPaceProcessingTime market_segment_prefix market_segment_los_prefix;
    %Global work_lib work_dir return_code;
    %global error_codes error_bit etl_status err_str loadtype err_ct;
    %global connect_Str;
    %let error_codes = 0;
    %let return_code=0;
    %let etl_status = 0;
    %let err_str = .;
    %let etl_file_status = 0;
    %let app_start_time=%sysfunc(datetime());
    %let startup_start_time=%sysfunc(datetime());
    %let runTime=0;
    %let syscc = 0;
    %let BookedPaceTablesUpdated=No;
    %let StayedPaceTablesUpdated=No;
    %let Booked_Stayed_Accom_Differences=No;
    %let Booked_Stayed_Difference_Percent=0;
    %let SasStayedPaceProcessingTime=0;
    %let mktsegruntime=0;
    %let accomtyperuntime=0;
    %let set_autoDetectBookedRTPace=FALSE;
    %let TWO_YEARS_DAYS = 731;
    %let RequestedRoomTypeOption=ALL;
    %let useReservationNight=True;
    %Put *********************************************************************;
    %Put ********************** Starting Pace Build **************************;
    %Put *********************************************************************;
    %Put;

    data _null_;
        set &lib..requestHeader;
        call symputx('Operation',OperationName,'g');
        call symputx('property_Id',propertyId,'g');
        call symputx('requestId',requestId,'g');
        call symputx('tenant_server',tenant_server,'g');
        call symputx('server_instance',tenant_server_instance,'g');
        call symputx('tenant_pwd',tenant_password,'g');
        call symputx('tenant_user',tenant_user,'g');
        call symputx('tenant_db',tenant_database,'g');
        call symputx('port',tenant_db_port,'g');
        call symputx('saspath',dataset_path,'g');
        stop;
    run;
    %if %varexist(&lib..PaceBuildRequest, NumberPaceDays) eq 1 and
        %varexist(&lib..PaceBuildRequest, NumberOccupancyDays) eq 1 and
        %varexist(&lib..PaceBuildRequest, MarketSegments) eq 1 %then
        %do;

            data _null_;
                set &lib..PaceBuildRequest;
                 call symputx('RequestedNumberPaceDays',NumberPaceDays,'G');
                 call symputx('RequestedNumberOccDays',NumberOccupancyDays,'G');
                 call symputx('RequestedMarketSegments',MarketSegments,'G');
                 call symputx('usePaceGroupMaster',usePaceGroupMaster,'G');
                 call symputx('ignoreCanceledReservations',ignoreCanceledReservations,'G');
                 call symputx('TotalRateEnabled', totalRateEnabled, 'G');
                 call symputx('stopPopulatingPartitionsTable',stopPopulatingPartitionsTable,'G');
                 call symputx('skipGroupPercentValidation', skipGroupPercentValidation, 'G');
                 call symputx('useBookingDtAsCutoffDt', useBookingDtAsCutoffDt, 'G');
                 call symputx('useGroupsAsTrans', useGroupsAsTrans, 'G');
                 call symputx('IncludeDepartureAdjustment', includeDepartureAdjustment, 'G');
                stop;
            run;

            %if %upcase(&operation.)=PACEBUILDSASONLY %then
            %do;
                %let SasDatasetsOnly=False;
                %let isRunningAsPartOfPMSRevamp=False;
                %let useReservationNight=False;
                %let syncTenantMS=False;
            %end;
            %let preserveHotelAccomPace=False;
        %end;
    %else
        %do;

            data _null_;
                set &lib..PaceBuildRequest;
                call symputx('SasDatasetsOnly',SasDatasetsOnly,'G');
                call symputx('RequestedMarketSegments',marketSegments,'G');
                call symputx('preserveHotelAccomPace',preserveHotelAccomPace,'G');
                call symputx('isRunningAsPartOfPMSRevamp',isRunningAsPartOfPMSRevamp,'G');
                call symputx('usePaceGroupMaster',usePaceGroupMaster,'G');
                call symputx('syncTenantMS',syncTenantMS,'G');
                call symputx('ignoreCanceledReservations',ignoreCanceledReservations,'G');
                call symputx('paceBuildPastDays',paceBuildPastDays,'G');
                call symputx('TotalRateEnabled', totalRateEnabled, 'G');
                call symputx('stopPopulatingPartitionsTable',stopPopulatingPartitionsTable,'G');
                call symputx('skipGroupPercentValidation', skipGroupPercentValidation, 'G');
                call symputx('useBookingDtAsCutoffDt', useBookingDtAsCutoffDt, 'G');
                call symputx('useGroupsAsTrans', useGroupsAsTrans, 'G');
                call symputx('IncludeDepartureAdjustment', includePostDeparture, 'G');
                stop;
            run;
            %put &=paceBuildPastDays;
            %let RequestedNumberPaceDays=365;
            %let RequestedNumberOccDays=&paceBuildPastDays.;
        %end;
        %put &=requestedMarketSegments;
        %if "%upcase(%trim(&RequestedMarketSegments.))" eq "OVERFLOW" %then %do;
            %put resolving the overflowing marketSegmentIds;
            proc sql;
                select marketSegmentId into: RequestedMarketSegments separated by '|'
                    from &lib..marketSegmentIdList;
            run;
        %end;
        %put &=requestedMarketSegments;
        %put &=skipGroupPercentValidation;

    %if %varexist(&lib..PaceBuildRequest, PaceBuildLastDays) eq 1 %then %do;
        data _null_;
            set &lib..PaceBuildRequest;
            call symputx('RequestPaceBuildLastDay',paceBuildLastDays,'G');
            call symputx('UsePacmanData', usePacmanData,'G');
            stop;
        run;
    %end;

    %if %varexist(&lib..PaceBuildRequest, overridePacePastBuildDays) eq 1 %then %do;
        data _null_;
            set &lib..PaceBuildRequest;
            call symputx('overridePacePastBuildDays',overridePacePastBuildDays,'G');
            stop;
        run;
        %if &overridePacePastBuildDays. > 0 %then %do;
            %let RequestedNumberOccDays=&overridePacePastBuildDays.;
        %end;
    %end;

    %if %upcase(&RequestedMarketSegments.) ne ALL %then %do;
        %let preserveHotelAccomPace=TRUE;
        %if &overridePacePastBuildDays. eq 0 %then %do;
            %let RequestedNumberOccDays=&TWO_YEARS_DAYS.;
        %end;
    %end;

    %if %upcase(&isRunningAsPartOfPMSRevamp.)=TRUE AND &overridePacePastBuildDays. eq 0 %then %do;
        %let RequestedNumberOccDays=&TWO_YEARS_DAYS.;
    %end;

    %if %symexist(RequestPaceBuildLastDay) and %upcase(&RequestPaceBuildLastDay.) ne LASTDATE %then %do;
        %let last_date_sas_format = %sysfunc(inputn(&RequestPaceBuildLastDay.,yymmdd10.));
    %end;
    %if &syscc > 4 %then
        %do;
            %ideas_util_report_errors(910, 'Failed to read request header', '', &request_Id., 1);
            %let etl_status = 1;
            %goto EXIT;
        %end;

    %let request_id = %str(&requestid.);
    %let sas_path = %str(&saspath.);

    %if &work_library.=work %then
        %do;
            %let work_lib=work;
            %let work_path=%sysfunc(pathname(work));
        %end;
    %else
        %do;
            %let work_lib=&work_library.;
            %let work_path_drive=%substr (%sysfunc(pathname(work)),1,2);
            %let work_path_root=&work_path_drive./sas;

            %ideas_util_create_dir(dir=temp,base_path=&work_path_root.);
            %let work_path_root=&work_path_drive./sas/temp;

            %ideas_util_create_dir(dir=&property_id.,base_path=&work_path_root.);
            %let work_path_root=&work_path_drive./sas/temp/&property_id.;

            %ideas_util_create_dir(dir=work,base_path=&work_path_root.);
            %let work_path=&work_path_drive./sas/temp/&property_id./work;
            libname &work_lib "&work_path.";

            proc Datasets library=&work_lib. kill memtype = data noprint nolist;
            run;

        %end;

    data &work_lib..errors;
        length err_ct error_cd 8 request_id error_params error_message $ 200;
        stop;
    run;

    %let mylog_path = %str(&saspath.);
    %let y=%sysfunc(compress(&request_id.,' -'));
    %let load_id = %sysfunc(substr(&y.,1,5));

    %if %sysfunc(libref(tenant)) ne 0 %then
        %do;
            %ideas_connect_tenant (&tenant_db., &tenant_server., &port., &server_instance., &tenant_user., &tenant_pwd.);

            %if &etl_status = 1 %then
                %goto EXIT;
        %end;

    %let connect_str = driver={SQL Server Native Client 10.0}%str(;)server=&tenant_server\&server_instance%str(;)uid=&tenant_user%str(;)pwd=&tenant_pwd%str(;)database=&tenant_db%str(;)port=&port;
    %let connect_str=complete="&connect_str";
    %let rm_data_perm_path=&sas_path.;
    %let rm_part_perm_path=&sas_path.\partitions;

    %ideas_util_create_dir(dir=pacehistorydata,base_path=&rm_data_perm_path.);
    %ideas_util_create_dir(dir=pacehistorypart,base_path=&rm_part_perm_path.);
    %ideas_util_create_dir(dir=group,base_path=&rm_part_perm_path.\pacehistorypart);
    libname rm_data "&sas_path.\pacehistorydata";
    libname rm_datap "&sas_path.";
    libname rm_part "&sas_path.\partitions\pacehistorypart";
    libname group "&sas_path.\partitions\pacehistorypart\group";
    libname rm_partp "&sas_path.\partitions";

    proc Sql noprint;
        select property_id into : pid from tenant.property where property_id = &property_id. and status_id=1;
        select property_code into: pcode from tenant.property where property_id = &property_id. and status_id=1;
        select property_name into :property_name from tenant.property where property_Id=&property_id. and status_id=1;
    quit;

    %if not %symexist(pid) %then
        %do;
            %let property_desc=%str(Property: %trim(&property_id.) does not exist in the %trim(&tenant_db.) database);
            %let property_desc_short=%str(Property: %trim(&property_id.) does not exist in the %trim(&tenant_db.) database);

            %ideas_util_inc_errors;
            %ideas_util_report_errors(913, "&property_desc.", '', &request_Id., &err_ct.);
            %let etl_status = 1;
            %goto EXIT;
        %end;
    %else
        %do;
            %let property_desc=%str(Property: %trim(&property_id.) - %trim(&pcode.));
            %let property_desc_short=%str(Property %trim(&property_id.) - %trim(&pcode.));
        %end;

    proc Sql noprint;
        select record_type_id into: TransRecType from tenant.record_type where upcase(record_type_name) = 'INDTRANS' and status_id=1;
        select record_type_id into: SummaryRecType from tenant.record_type where upcase(record_type_name) = 'T2SNAP' and status_id=1;
    quit;


    proc Sql;
        %if %upcase(&isRunningAsPartOfPMSRevamp.)=TRUE %then
            %do;
                select intnx('day', 1, max(snapshot_dt)) into :currentSnapshotDt from tenant.file_metadata
                                        where property_id = &property_id and isbde=1 and record_type_id = &SummaryRecType.;
                create table &work_lib..snapshot_date as
                    select distinct intnx('day', 1, snapshot_dt) as snapshot_dt format=date9. , snapshot_tm as snapshot_tm format=time8.
                        from tenant.file_metadata
                            where property_id = &property_id and record_type_id = &SummaryRecType. and
                                (select max(snapshot_dt) from tenant.file_metadata
                                    where property_id = &property_id and isbde=1 and record_type_id = &SummaryRecType.) = snapshot_dt;
            %end;
        %else
            %do;
                select max(snapshot_dt) into :currentSnapshotDt from tenant.file_metadata
                                        where property_id = &property_id and isbde=1 and File_Name ne "PaceHistoryBuild" and record_type_id = &SummaryRecType.;
                %put SAS FORMAT RPBLD DATE = %sysfunc(inputn(&RequestPaceBuildLastDay.,yymmdd10.));
                %put SAS FORMAT CSD DATE = %sysfunc(inputn(&currentSnapshotDt,date9.));
                %if %symexist(RequestPaceBuildLastDay) and %upcase(&RequestPaceBuildLastDay.) ne LASTDATE and %sysfunc(inputn(&currentSnapshotDt,date9.)) gt %sysfunc(inputn(&RequestPaceBuildLastDay.,yymmdd10.)) %then
                    %do;
                    create table &work_lib..snapshot_date as
                        select distinct snapshot_dt as snapshot_dt format=date9. , snapshot_tm as snapshot_tm format=time8.
                            from tenant.file_metadata
                                where property_id = &property_id and record_type_id = &SummaryRecType. and
                                    (select min(snapshot_dt) from tenant.file_metadata
                                        where property_id = &property_id and isbde=1 and snapshot_dt>&last_date_sas_format. and record_type_id = &SummaryRecType.) = snapshot_dt;
                    %end;
                %else
                    %do;
                    proc Sql noprint;
                        select min(snapshot_dt), max(snapshot_dt), INTCK('DAY',min(snapshot_dt),max(snapshot_dt))  into: candidateSnapDt, :latestSnapDt, :datediffCnt
                            from tenant.file_metadata
                                where property_id = &property_id and isbde=1 and upcase(File_Name) not in ('PACEHISTORYBUILD','POPULATEMISSINGSNAPSHOTPACEPOINT') and record_type_id = &SummaryRecType.;
                    quit;

                    %put &=datediffCnt.;

                    %let backfillSnapDt = &candidateSnapDt.;
                    %if %symexist(datediffCnt) and &datediffCnt. > 5 %then %do;
                        %let backfillSnapDt = &latestSnapDt.;
                    %end;
                    %put &=backfillSnapDt.;

                    proc sql;
                        create table &work_lib..snapshot_date as
                            select distinct snapshot_dt as snapshot_dt format=date9. , snapshot_tm as snapshot_tm format=time8.
                                from tenant.file_metadata
                                    where property_id = &property_id and record_type_id = &SummaryRecType. and
                                        &backfillSnapDt.=snapshot_dt;
                    quit;
                    %end;
            %end;

    quit;
    /*
        Note: The SQL server flow assigns 'currentSnapshotDT' with DDMMMYYYY string output and
        test dataset flow assigns numeric value. AS the ensuing code assumes DDMMMYYYY value in 'currentSnapshotDT',
         adding the logic to convert numeric values to DDMMMYYYY format
    */
    %if %sysfunc(notdigit(&currentSnapshotDT.)) eq 0 %then %do;
        %let currentSnapshotDT = %sysfunc(putn(&currentSnapshotDT., date9.));
    %end;

    /* Adjust Request days if 2 years data is not available in REservation_night */
    %if %upcase(&RequestedMarketSegments.) ne ALL %then %do;
        %if &overridePacePastBuildDays. eq 0 %then %do;
            proc sql;
                select min(&TWO_YEARS_DAYS.,(%sysfunc(inputn(&currentSnapshotDt,date9.)) - min(occupancy_DT))) into :RequestedNumberOccDays
                 from tenant.reservation_night;
            run;
        %end;
    %end;
    %let curSnapshotDtMinus365=%sysevalf(%sysfunc(inputn(&currentSnapshotDt,date9.))-365);

    %if %sysfunc(exist(&work_lib..snapshot_date)) and %ideas_util_nobs(&work_lib..snapshot_date) = 0 %then
        %goto EXIT;

    proc Sql;
        create table &work_lib..backfill_number_snapshot_dates as
            select distinct snapshot_dt as snapshot_dt format=date9. , snapshot_tm as snapshot_tm format=time8.
                from tenant.file_metadata
                    where property_id = &property_id  and isbde=1 and record_type_id = &SummaryRecType. and  upcase(File_Name) eq "PACEHISTORYBUILD";
    quit;

    %if %ideas_util_nobs(&work_lib..backfill_number_snapshot_dates) =0 %then
        %do;

            proc Datasets library=group kill memtype = data noprint nolist;
            run;

            quit;

        %end;

    %let offset = 2;
    %if %upcase(&isRunningAsPartOfPMSRevamp.)=TRUE %then
        %let offset = 1;

    data _null_;
        attrib Pace_Start_dt format=date9.;
        attrib Pace_End_dt format=date9.;
        attrib snapshot_dttm format=datetime20.;
        set &work_lib..snapshot_date;
        Minimum_occupancy_dt=snapshot_dt-&requestednumberoccdays.;
        Snapshot_Dttm=dhms(snapshot_dt,0,0,snapshot_tm);
        Pace_End_dt=(intnx('day', snapshot_dt , -&offset.));
        Pace_Start_dt=intnx('day', Pace_End_dt , &RequestedNumberPaceDays.*-2);
        booked_start_dt=intnx('day', snapshot_dt , &RequestedNumberPaceDays.*-1);
        call symputx('Pace_Start_dt',Pace_Start_dt);
        call symputx('Pace_End_dt',Pace_End_dt);
        call symputx('First_Snapshot_dt',Snapshot_Dt);
        call symputx('First_Snapshot_dttm',Snapshot_Dttm);
        call symputx('Minimum_occupancy_dt',Minimum_occupancy_dt);
        call symputx('Stayed_minimum_occupancy_dt',Minimum_occupancy_dt);
        call symputx('Booked_minimum_occupancy_dt',Minimum_occupancy_dt);
        call symputx('booked_start_dt',booked_start_dt);

    run;

    %let minimum_snapshot_dt=&first_snapshot_dt.-&ProcessedNumberpaceDays.;

    proc Sql noprint;
        select count(*) into :Backfill_Correction_count
            from tenant.file_metadata
                where property_id = &property_id  and isbde=1 and record_type_id = &SummaryRecType. and  upcase(File_Name) eq "PACEHISTORYBUILD"
                    and upcase(file_location) not in ('TYPETWOALLPACE','TYPETHREEALLPACE');
    quit;

    %put &=Backfill_Correction_count.;


    %if &Backfill_Correction_count. =0 %then
        %do;

            proc Sql noprint;
                select max(occupancy_dt) into :maximum_occupancy_dt
                    from rm_datap.accom_inventory_pace
                        where datepart(&first_snapshot_dttm.)=datepart(capture_dttm) and rooms_sold >0;
            quit;

        %end;
    %else
        %do;

            proc Sql noprint;
                select max(occupancy_dt) into :maximum_occupancy_dt
                    from rm_datap.accom_inventory_pace
                        where rooms_sold >0;
            quit;

        %end;

    %if not %symexist(maximum_occupancy_dt) or &maximum_occupancy_dt. = . %then
        %do;
            %if %upcase(&isRunningAsPartOfPMSRevamp.)=TRUE %then %do;
                proc sql noprint;
                    select max(future_window_size) into :Backfill_future_window_size
                        from tenant.file_metadata
                            where isbde=1 and record_type_id = &SummaryRecType.
                            and snapshot_dt = (select max(snapshot_dt) from tenant.file_metadata where property_id = &property_id and isbde=1 and record_type_id = &SummaryRecType.);
                quit;

                %put BACKFILL_WINDOW_SIZE &Backfill_future_window_size.;
            %end;
            %else %do;
                proc sql noprint;
                    select max(future_window_size) into :Backfill_future_window_size
                        from tenant.file_metadata
                            where snapshot_dt <=&first_snapshot_dt. and isbde=1 and upcase(File_Name) ne "PACEHISTORYBUILD" and record_type_id = &SummaryRecType.;
                quit;
            %end;

            %let maximum_occupancy_dt=%eval(%sysfunc(sum(&first_snapshot_dt.+&Backfill_future_window_size.)));
        %end;
   %if %symexist(RequestPaceBuildLastDay) and %upcase(&RequestPaceBuildLastDay.) ne LASTDATE %then %do;
        %let maximum_occupancy_dt=%sysfunc(inputn(&RequestPaceBuildLastDay.,yymmdd10.));
   %end;

    proc Datasets library = rm_data kill memtype=data nolist noprint;
    run;

    quit;

    proc Datasets library = rm_part kill memtype=data nolist noprint;
    run;

    quit;

    %if %upcase(&isRunningAsPartOfPMSRevamp.)=TRUE %then
        %do;
            proc sql;
                select memname into: non_pace_dataset_list separated by ' '
                    from dictionary.members
                        where libname = upcase("rm_datap")
                        and upcase(memname) in ('ACCOM_INVENTORY','MKT_ACCOM_INVENTORY',
                                                   'TOTAL_INVENTORY','BDE_ACCOM_INVENTORY',
                                                   'BDE_MKT_ACCOM_INVENTORY','ORG_MKT_ACCOM_INVENTORY',
                                                   'MKT_ACCOM_LOS_INVENTORY','ORG_MKT_ACCOM_LOS_INVENTORY',
                                                   'BDE_MKT_ACCOM_LOS_INVENTORY');
            quit;

            %move_nonpace_2_temp_lib();
        %end;

    %let startup_end_time=%sysfunc(datetime());
    %let startuprunTime=%sysfunc(round(&startup_end_time-&startup_start_time, .05));

    %if &return_code > 0 %then
        %goto EXIT;

    %if %upcase(&RequestedRoomTypeOption.)=STAYED or %upcase(&RequestedRoomTypeOption.)=ALL %then
        %do;
            %Ideas_Process_Accom_Type_Pace(Accom_Type_Option=STAYED);

            %if &return_code > 0 %then
                %goto EXIT;
        %end;
    %else
        %do;
            %let SasStayedPaceProcessingTime=0;
            %let mktsegruntime=0;
            %let accomtyperunTime=0;
        %end;

    %if %upcase(&RequestedRoomTypeOption.)=BOOKED or %upcase(&RequestedRoomTypeOption.)=ALL and %sysfunc(exist(rm_datap.org_mkt_accom_inventory)) and (%ideas_util_nobs(rm_datap.org_mkt_accom_inventory) > 0 or %upcase(&isRunningAsPartOfPMSRevamp.)=TRUE) %then
        %do;
            %Ideas_Process_Accom_Type_Pace(Accom_Type_Option=BOOKED);

            %if &return_code > 0 %then
                %goto EXIT;

            %if %upcase(&RequestedRoomTypeOption.)=ALL and %upcase(&RequestedMarketSegments.)=ALL and %upcase(&Booked_Stayed_Accom_Differences.)=YES and &Booked_Stayed_Difference_Percent. GE 0.02 %then
                %do;
                    %let set_autoDetectBookedRTPace=true;
                    DATA _NULL_;
                        CALL symput("sdate",cats("'",putn(&curSnapshotDtMinus365.,'date9.'),"'"));
                    RUN;
                    %let booked_start_dt=&curSnapshotDtMinus365.;
                 %end;
             %else
                %do;
                    DATA _NULL_;
                        CALL symput("sdate",cats("'","&currentSnapshotDt.","'"));
                    RUN;
                    %let booked_start_dt=%sysfunc(inputn(&&currentSnapshotDt,date9.));
                    %let booked_minimum_occupancy_dt=%sysfunc(inputn(&&currentSnapshotDt,date9.));

                %end;

            proc sql noprint;
                select value into :bkStartDt from tenant.IP_Cfg_Property_Attribute
                where Attribute_Name = 'PROP_BK_HISTORY_START_DT';
            quit;

            %if %upcase(&operation.) ne PACEBUILDSASONLY and %upcase(&SasDatasetsOnly.)=FALSE
                    and ( %symexist(bkStartDt)=0 or %length(&bkStartDt.)=0 or &booked_start_dt lt %sysfunc(inputn(&bkStartDt,date9.)) ) %then %do;
                    %put update prop bk history start date;
                    proc sql;
                        CONNECT TO ODBC (&connect_str autocommit=no);
                        EXECUTE (
                            UPDATE IP_CFG_PROPERTY_ATTRIBUTE
                                SET Value = &sdate.
                                    WHERE Attribute_Name='PROP_BK_HISTORY_START_DT';
                        COMMIT;
                        ) BY ODBC;
                    quit;
            %end;
            %else %if %symexist(bkStartDt)=1 and %length(&bkStartDt.)>0 %then %do;
                %put Previous Prop Bk history start date retained;
                %let booked_start_dt=%sysfunc(inputn(&bkStartDt,date9.));
            %end;
        %end;
    %else
        %do;
            %let SasBookedPaceProcessingTime=0;

        %end;


    %if &return_code.=0 and &syscc<= 4 and "&GL_ABORT_FLAG" eq "0" %then
        %do;
            %Put Committing Pace Updates To Sas Market Segment/LOS Sas Datasets;

            %if %upcase(&RequestedRoomTypeOption.)=STAYED or %upcase(&RequestedRoomTypeOption.)=ALL %then
                %do;

                    proc Datasets library=rm_datap noprint nolist;
                        delete accom_inventory_pace;
                        copy in=rm_data out=rm_datap move
                            CLONE
                            CONSTRAINT=YES
                            INDEX=YES;
                        select accom_inventory_pace;
                    run;

                    quit;

                    %if %upcase(&isRunningAsPartOfPMSRevamp.)=TRUE %then
                        %do;
                            %move_nonpace_2_perm_lib;
                        %end;

                %end;

            proc Sql noprint;
                select memname into: updatepartlist separated by ' '
                    from dictionary.members
                        where libname = upcase("RM_PART");
            quit;

            %if %upcase(&stopPopulatingPartitionsTable) eq FALSE %then
                %do;
                    proc Datasets library=rm_partp noprint nolist;
                        Delete &updatepartlist.;
                        copy in=rm_part out=rm_partp move
                            CLONE
                            CONSTRAINT=YES
                            INDEX=YES;
                        select &updatepartlist.;
                    run;

                    quit;
                %end;
            %else
                %do;
                   proc Datasets library=rm_part kill memtype = data noprint nolist;
                        run;
                   quit;
               %end;

        %end;

%EXIT:

    proc Sql noprint;
        select count(*) into: errcount from &work_lib..errors;
    quit;

    %if &errcount. > 0 %then
        %do;

            proc Sql noprint;
                select distinct error_message into: err_str from &work_lib..errors;
            quit;

        %end;

    data _null_;
        FILE resXml;
        put '<?xml version="1.0" encoding="UTF-8"?>';
        put '<SASResponse xmlns="http://xml.common.pacman.tetris.ideas.com/schema/pacebuild/response/v1" ';
        put 'xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" ';
        put 'xsi:schemaLocation="http://xml.common.pacman.tetris.ideas.com/schema/pacebuild/response/v1/pace_build_response.xsd ">';
        put "<ResponseHeader> <PropertyId>&property_Id</PropertyId>" @;
        put "<operationName>paceBuild</operationName>" @;
        put "<setAutoDetectSufficientBookedRTPace>&set_autoDetectBookedRTPace</setAutoDetectSufficientBookedRTPace>" @;
        put "<requestId>&requestId</requestId>";
        stop;
    run;

    %if "&GL_ABORT_FLAG" eq "1" or &syscc > 4 %then
        %do;
            %let Status_desc=Failed;
            %let GL_ABORT_FLAG =1;

            proc Sort data=&work_lib..errors;
                by request_id error_cd;
            run;

            data _NULL_;
                FILE resXml MOD;
                set &work_lib..errors;

                if _N_ > 0 then
                    put "<ErrorMessage>&err_str.</ErrorMessage>";
            run;

        %end;

    data _NULL_;
        FILE resXml MOD;

        put "</ResponseHeader>";
        put "<PaceBuildResponse>";
        put "<operationName>paceBuild</operationName>";
        put "<setAutoDetectSufficientBookedRTPace>&set_autoDetectBookedRTPace.</setAutoDetectSufficientBookedRTPace>" @;
        put "</PaceBuildResponse>";
        put "</SASResponse>";
    run;

    %let app_end_time=%sysfunc(datetime());
    %let runTime=%sysfunc(round(&app_end_time-&app_start_time, .05));
    %let createDt=%sysfunc(date());

    %if &Backfill_Correction_count. =0 %then
        %let Backfill_type=Historical Backfill;
    %else %let Backfill_type=Correction Backfill;

    %if %upcase(&isRunningAsPartOfPMSRevamp.)=TRUE %then
        %let Backfill_type=PMS Revamp Backfill;

    %if %symexist(RequestPaceBuildLastDay) and %upcase(&RequestPaceBuildLastDay.) ne LASTDATE and &overridePacePastBuildDays. eq 0 %then %do;
        %let Backfill_type=Selective Backfill;
        %let createDt=%sysfunc(datepart(&app_start_time.));
    %end;

    %if "&GL_ABORT_FLAG" eq "0" and &syscc <=4 %then
        %do;
            %if %sysfunc(exist(&work_lib..snapshot_date)) and %ideas_util_nobs(&work_lib..snapshot_date) = 0 %then
                %let Status_desc=Successful - No Pace Processed - Pacman Data Not Loaded Yet;
            %else %if (%symexist(updatemslist)=0) or %length(&updatemslist.) = 0 %then
                %let Status_desc=Failed - Invalid MS Input;
            %else %let Status_desc=Successful;

            %if %upcase(&Sas_Debug.)=FALSE %then
                %do;

                    proc printto log="&logfile" new;
                    run;

                %end;
        %end;
        %let snapshot_dt_temp=%sysfunc(putn(&First_snapshot_dt,date9.));
        %let Pace_History_Last_Snapshot_Dt=%sysfunc(putn(&Pace_End_dt.+1,date9.));

    %populate_backfill_log;



    Options nomacrogen NoSymbolgen nomlogic nomprint nomfile;
    %Put;
    %Put ---------------------------------------------------------------------;
    %Put ------------------------- Pace Build Summary ------------------------;
    %Put ---------------------------------------------------------------------;
    %Put;
    %Put -                      Property Info: &property_desc.;
    %Put -                Pace History Status: &status_desc.;
    %Put -                          Operation: &Operation.;
    %Put -                         Request ID: &requestId.;
    %Put;
    %Put -                      Sas Data Path: &saspath.;
    %Put -                     Partition Path: &rm_part_perm_path.;
    %Put -                          Pace Path: &rm_data_perm_path.;
    %Put -                  Work Library Path: &work_path.;
    %Put -                  Work Library Name: &work_lib;
    %Put;
    %Put -                    Database Server: &tenant_server.;
    %Put -                    Server Instance: &server_instance.;
    %Put -                      Database Name: &tenant_db.;
    %Put -                      Database User: &tenant_user.;
    %Put -                      Database Port: &port.;

    %if (%sysfunc(exist(&work_lib..snapshot_date)) and %ideas_util_nobs(&work_lib..snapshot_date) > 0) %then
        %do;
            %let snapshot_dt_temp=%sysfunc(putn(&First_snapshot_dt,date9.));
            %let Pace_History_Last_Snapshot_Dt=%sysfunc(putn(&Pace_End_dt.+1,date9.));
            %Put;
            %put -             G3 First Snapshot Date: &snapshot_dt_temp.;
            %put -             Current Snapshot Date : &currentSnapshotDt.;
            %put -             Current Snapshot Date -365: &curSnapshotDtMinus365.;
            %Put -            Pace Last Snapshot Date: &Pace_History_Last_Snapshot_Dt.;
            %put -        Max Backfill Occupancy Date: %sysfunc(putn(&maximum_occupancy_dt..+0,date9.));
            %Put -         Backfill Sas Datasets Only: &SasDatasetsOnly.;
            %Put -          preserve Hotel Accom Pace: &preserveHotelAccomPace.;
            %Put -          Use Pace Group Master    : &usePaceGroupMaster.;
            %Put -   Is Running as part of PMS Revamp: &isRunningAsPartOfPMSRevamp.;
            %put -                      Backfill Type: &Backfill_type.;
            %Put -        Requested Accom Type Option: &RequestedRoomTypeOption.;
            %Put -          Requested Market Segments: &RequestedMarketSegments.;
            %Put;
            %Put -         Requested Number Pace Days: &RequestedNumberPaceDays.;
            %Put -        Processed  Number Pace Days: &ProcessedNumberPaceDays.;
            %Put -    Requested Number Occupancy Days: &RequestedNumberOccDays.;
            %Put -            Total Pace Process Time: &runTime. seconds;
            %Put -               Startup Process Time: &startuprunTime. seconds;
            %Put;
            %put - Group Invalid Booking Date Percent: %sysfunc(putn(&Group_Booked_Difference_Percent.+0,percent7.3));
            %Put -       Sas Stayed Pace Process Time: &SasStayedPaceProcessingTime. seconds;
            %Put -        Mkt Segment Process/DB Time: &mktsegruntime. seconds;
            %Put -         Accom Type Process/DB Time: &accomtyperuntime. seconds;
            %Put -         Stayed Pace Tables Updated: &StayedPaceTablesUpdated.;
            %put -       Stayed Occupancy Date Cutoff: %sysfunc(putn(&stayed_minimum_occupancy_dt.+0,date9.));
            %Put;
            %Put -       Sas Booked Pace Process Time: &SasBookedPaceProcessingTime. seconds;
            %Put -         Booked Pace Tables Updated: &BookedPaceTablesUpdated.;
            %put -   History Booked Start DT         : %sysfunc(putn(&booked_start_dt.,date9.));
            %put -       Booked Occupancy Date Cutoff: %sysfunc(putn(&booked_minimum_occupancy_dt.+0,date9.));
            %put -          Booked Difference Percent: %sysfunc(putn(&Booked_Stayed_Difference_Percent.+0,percent7.3));
            %put - Booked_Stayed_Accom_Differences   : &Booked_Stayed_Accom_Differences;
            %put -          setAutoDetectBookedRTPace: &set_autoDetectBookedRTPace;
            %put -          useGroupsAsTrans         : &useGroupsAsTrans;
            %Put;
            %Put -Processed Transient Market Segments: %str(&updatemslist.);
            %put;
            %Put -    Processed Group Market Segments: %str(&groupmktseglist.);
            %put;
            %Put -            Updated Market Segments: %str(&updatemslist.);
            %put;
            %Put -              Processed Accom Types: %str(&accomsortcommalist.);
            %put;
            %Put -                     Sync Tenant MS: &syncTenantMS.;
            %Put -                     Ignore Canceled Reservations: &ignoreCanceledReservations.;
            %put;
        %end;

    %Put *********************************************************************;
    %Put *********************** Ending Pace Build ***************************;
    %Put *********************************************************************;
    %Put;
    %if &work_lib. ne work %then
        %do;

            proc Datasets library = &work_lib. kill memtype=data nolist noprint;
            run;

            quit;

        %end;

%mend ideas_pace_build_main;

%macro varexist(ds,var)/store;
    %local dsid rc;
    %let dsid = %sysfunc(open(&ds));

    %if (&dsid) %then
        %do;
            %if %sysfunc(varnum(&dsid,&var)) %then
                1;
            %else 0;
            %let rc = %sysfunc(close(&dsid));
        %end;
    %else 0;
%mend varexist;

%macro Ideas_Process_Accom_Type_Pace(Accom_Type_Option=)/store;
    %Put ---------------------------------------------------------------------;
    %Put ------------------ Starting &Accom_Type_Option. Pace Processing ------------------;
    %Put ---------------------------------------------------------------------;
    %Put;
    %let sas_pace_processing_start_time=%sysfunc(datetime());
    %Put Copying &Accom_Type_Option. Sas Datasets To Scratchpad Library;

    %if %upcase(&Accom_Type_Option.)=BOOKED %then
        %do;
            %let market_segment_prefix=ma_org;
            %let market_segment_los_prefix=ma_los_org;

            proc Sql noprint;
                select memname into: rmpartlist separated by ' '
                    from dictionary.members
                        where libname = upcase("RM_PARTP") and
                            ( trim(lowcase(memname)) like "ma_org_%" or trim(lowcase(memname)) like ("ma_los_org_%" ));
            quit;

        %end;
    %else
        %do;
            %let market_segment_prefix=ma;
            %let market_segment_los_prefix=ma_los;

            proc Sql noprint;
                select memname into: rmpartlist separated by ' '
                    from dictionary.members
                        where libname = upcase("RM_PARTP") and
                            ( trim(lowcase(memname)) like "ma_%" or trim(lowcase(memname)) like ("ma_los_%" ) ) and
                            ( trim(lowcase(memname)) not like "ma_org_%" and trim(lowcase(memname)) not like ("ma_los_org_%" ));
            quit;

            proc Datasets library=rm_datap nolist noprint;
                copy in=rm_datap out=rm_data
                    CLONE
                    CONSTRAINT=YES
                    INDEX=YES;
                select accom_inventory_pace;
            run;

            quit;

        %end;

    proc Datasets library=rm_datap nolist noprint;
        copy in=rm_partp out=rm_part
            CLONE
            CONSTRAINT=YES
            INDEX=YES;
        select &rmpartlist.;
    run;

    quit;

    %if %upcase(&UsePacmanData.)=FALSE %then
        %do;
        %goto STPC;
    %end;

    %Put Preparing &Accom_Type_Option. Transaction Pace Information;

    %ideas_pace_transactions(rc=&return_code.);
    %Put Calculating &Accom_Type_Option. RETURN CODE ----> &return_code.;
    %if &return_code > 0 %then
        %goto EXIT1;

    %if %upcase(&stopPopulatingPartitionsTable) eq FALSE %then
        %do;
            %if %upcase(&isRunningAsPartOfPMSRevamp.)=TRUE or %upcase(&syncTenantMS.)=TRUE %then
                %do;
                    %sync_tenant_ms_part(tenant_ms=&mssortlist., ms_parts=&rmpartlist.,
                                                ms_prefix=&market_segment_prefix., ms_los_prefix=&market_segment_los_prefix.,
                                                Accom_Type_Option=&Accom_Type_Option.);
                %end;
        %end;
    %else
        %do;
            %if %symexist(mssortlist) and "&mssortlist." ne "."  %then
               %do;
                   %sync_tenant_ms_part(tenant_ms=&mssortlist., ms_parts=&rmpartlist.,
                       ms_prefix=&market_segment_prefix., ms_los_prefix=&market_segment_los_prefix.,
                       Accom_Type_Option=&Accom_Type_Option.);
               %end;
           %else
               %do;
                    %put SKIPPING SYNC_TENANT FOR TRANSIENT MS;
               %end;
           %if %symexist(groupmslist) and "&groupmslist." ne "." %then
               %do;
                   %sync_tenant_ms_part(tenant_ms=&groupmslist., ms_parts=&rmpartlist.,
                       ms_prefix=&market_segment_prefix., ms_los_prefix=&market_segment_los_prefix.,
                       Accom_Type_Option=&Accom_Type_Option.);
               %end;
           %else
              %do;
                   %put SKIPPING SYNC_TENANT FOR GROUP MS;
              %end;
        %end;

    %Put Calculating &Accom_Type_Option. Market Segment/Accom Type Pace Points;

    %ideas_pace_mkt_accom(rc=&return_code.);

    %if &return_code > 0 %then
        %goto EXIT1;

    %Put Calculating &Accom_Type_Option. Los Market Segment/Accom Type Pace Points;

    %ideas_pace_mkt_accom_los(rc=&return_code.);

    %if &return_code > 0 %then
        %goto EXIT1;

    %Put Updating &Accom_Type_Option. Sas Market Segment/LOS Sas Datasets;

    %ideas_pace_update_sas(rc=&return_code.);

    %if &return_code > 0 %then
        %goto EXIT1;

    %let sas_pace_processing_end_time=%sysfunc(datetime());

    %if %upcase(&Accom_Type_Option.)=BOOKED %then
        %do;
            %let SasBookedPaceProcessingTime=%sysfunc(round(&sas_pace_processing_end_time-&sas_pace_processing_start_time, .05));
            %let BookedPaceTablesUpdated=Yes;
        %end;
    %else
        %do;
            %let SasStayedPaceProcessingTime=%sysfunc(round(&sas_pace_processing_end_time-&sas_pace_processing_start_time, .05));
            %let StayedPaceTablesUpdated=Yes;
        %end;

    %if %upcase(&Accom_Type_Option.)=STAYED and %upcase(&operation.) ne PACEBUILDSASONLY %then
        %do;
            %Ideas_Update_File_Metadata(rc=&return_code.);

            %if &return_code > 0 %then
                %goto EXIT1;
        %end;

    %if %upcase(&isRunningAsPartOfPMSRevamp.)=TRUE %then
        %do;
            %create_mkt_accom_los_inv(Accom_Type_Option=&Accom_Type_Option.);

            %if %upcase(&Accom_Type_Option.)=BOOKED %then %do;
                    %create_mkt_accom_booked_inv();
            %end;
        %end;


    %if %upcase(&Accom_Type_Option.)=STAYED and %upcase(&operation.) ne PACEBUILDSASONLY and (&Group_Booked_Difference_Percent. LE 0.20 or %upcase(&skipGroupPercentValidation.)=TRUE) and %upcase(&SasDatasetsOnly.)=FALSE %then
        %do;
            %Put Updating &Accom_Type_Option. Pacman Pace Tables;

            %ideas_pace_update_db(tenant_db=&tenant_db., tenant_server=&tenant_server.,port=&port., server_instance=&server_instance., tenant_user=&tenant_user., tenant_pwd=&tenant_pwd.,rc=&return_code.);

            %if &return_code > 0 %then
                %goto EXIT1;
        %end;
    %else
        %do;
            %let mktsegruntime=0;
            %let accomtyperuntime=0;

            %if %upcase(&Accom_Type_Option.) eq STAYED and %upcase(&operation.) ne PACEBUILDSASONLY and (%upcase(&SasDatasetsOnly.)=TRUE or &Group_Booked_Difference_Percent. GT 0.20)
             and %upcase(&preserveHotelAccomPace.)=FALSE
             %then
                %do;
                    %put Removing Backfilled Pace from Pacman Pace Activity Database Tables;
                    %let First_Snapshot_dt_YYmmdd=%sysfunc(putn(&First_Snapshot_dt,yymmdd10.));
                    %let first_snapshot_dt_sql=%NRBQUOTE('&First_Snapshot_dt_YYmmdd.');
                    %let mktsegi=1;

                    %do %while (%scan(&mssortlist., &mktsegi., |) ne);

                        data _null_;
                            call symputx ('mkt_seg',%scan(&mssortlist., &mktsegi., | ),'l');
                        run;

                        proc Sql;
                            connect to odbc (&connect_str autocommit=yes);
                            execute ( delete from pace_mkt_activity where &mkt_seg.=mkt_seg_id and DATEADD(dd, 0, DATEDIFF(dd, 0, snapshot_dttm)) < &first_snapshot_dt_sql.

                                ) by odbc;

                            %if NOT (&sysdbrc eq 0 or "&sysdbrc" eq "") %then
                                %do;
                                    %let error_codes = 921;
                                    %goto EXIT1;
                                %end;

                            disconnect from odbc;
                        quit;

                        %let mktsegi = %sysevalf(&mktsegi. + 1);
                    %end;

                    proc Sql noprint;
                        select distinct accom_type_id into: accomsortlist SEPARATED by '|' from &work_lib..accom_types
                            order by accom_type_id;
                    quit;

                    %let accomi= 1;

                    %do %while (%scan(&accomsortlist., &accomi., |) ne);

                        data _null_;
                            call symputx ('accom_id',%scan(&accomsortlist., &accomi., |),'l');
                        run;

                        proc Sql;
                            connect to odbc (&connect_str autocommit=yes bulkload=yes);
                            execute ( delete from PACE_Accom_Activity where &accom_id. = accom_type_id and DATEADD(dd, 0, DATEDIFF(dd, 0, snapshot_dttm)) < &first_snapshot_dt_sql.
                                ) by odbc;

                            %if NOT (&sysdbrc eq 0 or "&sysdbrc" eq "") %then
                                %do;
                                    %let error_codes = 921;
                                    %goto EXIT1;
                                %end;

                            disconnect from odbc;
                        quit;

                        %let accomi = %sysevalf(&accomi. + 1);
                    %end;

                    proc Sql;
                        connect to odbc (&connect_str autocommit=yes bulkload=yes);
                        execute ( delete from pace_total_activity where DATEADD(dd, 0, DATEDIFF(dd, 0, snapshot_dttm)) < &first_snapshot_dt_sql.
                            ) by odbc;

                        %if NOT (&sysdbrc eq 0 or "&sysdbrc" eq "") %then
                            %do;
                                %let error_codes = 921;
                                %goto EXIT1;
                            %end;

                        disconnect from odbc;
                    quit;

                %end;
        %end;

    /* When in TEST mode, the group data need not be deleted */
    %if &groupupdatecount. >0 and %upcase(&operation.) ne PACEBUILDSASONLY and &Backfill_Correction_count.=0 %then
        %do;
            %let msi= 1;

            %do %while (%scan(&groupmslist., &msi., |) ne);

                data _null_;
                    call symputx ('ms',%scan(&groupmslist., &msi, | ),'l');
                run;

                %put Cleaning up data of group market segment &ms.;

                proc sql;
                    delete from rm_part.&market_segment_prefix._&ms.
                        where datepart(capture_dttm) lt &First_Snapshot_dt.;
                    delete from rm_part.&market_segment_los_prefix._&ms.
                        where datepart(capture_dttm) lt &First_Snapshot_dt.;
                quit;

                %let msi = %sysevalf(&msi. + 1);
            %end;
        %end;

%STPC:
    %if %upcase(&UsePacmanData.)=FALSE and %upcase(&Accom_Type_Option.)=STAYED %then
    %do;
       %ideas_pace_update_db(tenant_db=&tenant_db., tenant_server=&tenant_server.,port=&port., server_instance=&server_instance., tenant_user=&tenant_user., tenant_pwd=&tenant_pwd.,rc=&return_code.);
       %LET Group_Booked_Difference_Percent=0.0031416;
       %put &=Group_Booked_Difference_Percent;
    %end;
%EXIT1:

    %if &error_codes =921 %then
        %do;
            %ideas_util_inc_errors;
            %ideas_util_report_errors(919, 'RDBMS delete failed', '', &request_Id., &err_ct.)
            %let etl_status = 1;
            %let GL_ABORT_FLAG=1;
        %end;

    %if &GL_ABORT_FLAG eq 1 or &syscc > 4 %then
        %do;
            %let etl_status = 1;
            %let return_code=200;
        %end;
    %else %let return_code=0;
%mend Ideas_Process_Accom_Type_Pace;

%macro Ideas_Update_File_Metadata(rc=)/store;

    proc Sql noprint;
        select min(datepart(capture_dttm)) into :min_capture_dt
            from &work_lib..mkt_accom_inventory;
    quit;

    %let max_capture_dt=%sysevalf(&first_snapshot_dt. - 1);

    data &work_lib..metadata_transactions;
        attrib File_Name format = $150.;
            attrib File_Location format = $512.;
            attrib Snapshot_DT Prepared_DT Scope_start_dt Scope_end_dt format  = date9.;
            attrib createdate format = datetime22.;
            attrib mode format = $50.;
            format Snapshot_TM Prepared_TM time16.7;
            File_Name = "PaceHistoryBuild";
            Property_ID = &Property_ID.;
            Past_Window_Size = 365;
            Future_Window_Size = 365;
            Scope_start_dt = &min_capture_dt.;
            Scope_end_dt = &max_capture_dt.;
            mode = "None";

            do Snapshot_DT=&min_capture_dt. to &max_capture_dt.;
                Snapshot_TM = '03:00:00't;
                Prepared_DT = Snapshot_DT;
                Prepared_TM = '03:00:00't;
                createdate = datetime();
                Process_Status_ID = 13;
                IsBDE = 1;
                Record_Type_Id = &SummaryRecType.;
                File_Location = "TypeTwoAllPace";
                output &work_lib..metadata_transactions;
            end;
    run;

    %local metad;

    %ideas_trans_upload_tmp(upload_table=&work_lib..metadata_transactions,
        like_table=tenant.file_metadata (drop = File_Metadata_ID),
        _name_in_db=metad,
        tmp_trans_lib=ten_tmp,cnt=1&load_id.);
    %let metad = ##&metad;

    proc Sql;
        connect to odbc (&connect_str autocommit=no);
        execute (
            with etldata as (select file_name, file_location, snapshot_dt, snapshot_tm,
            prepared_dt, prepared_tm, record_type_id, property_id,
            past_window_size, future_window_size, Process_Status_ID, isbde
        from &metad.)

            merge file_metadata as t

        using etldata as s

            on t.property_id = s.property_id and
            t.snapshot_dt=s.snapshot_dt and t.record_type_id= s.record_type_id
			and t.isBde = 1

            when matched then

        update set
            t.file_name=s.file_name

            when not matched by target then
        insert (file_name, file_location, snapshot_dt, snapshot_tm,
            prepared_dt, prepared_tm, record_type_id, property_id, past_window_size,
            future_window_size, process_status_id, isbde)
        values (file_name, file_location, snapshot_dt, snapshot_tm,
            prepared_dt, prepared_tm, record_type_id, property_id,
            past_window_size, future_window_size, Process_Status_ID, isbde);
        ) by odbc;
        %if NOT (&sysdbrc eq 0 or "&sysdbrc" eq "") %then
            %do;
                execute(rollback) by odbc;
                %let error_codes = 921;
                %goto EXIT3;
            %end;

        execute (
            commit
            )by odbc;
    quit;

%EXIT3:

    %if &error_codes =921 %then
        %do;
            %ideas_util_inc_errors;
            %ideas_util_report_errors(919, 'File Metadata Update failed', '', &request_Id., &err_ct.)
            %let etl_status = 1;
            %let GL_ABORT_FLAG=1;
        %end;

    %if &GL_ABORT_FLAG eq 1 or &syscc > 4 %then
        %do;
            %let etl_status = 1;
            %let return_code=200;
        %end;
    %else %let return_code=0;
%mend Ideas_Update_File_Metadata;


%macro populate_backfill_log(rc=)/store;
    options compress=yes;
    data &work_lib..backfill_log;
            propertyInfo="&property_desc.";
            operationName="&Operation." ;
            paceHistoryStatus="&Status_desc.";
            setAutoDetectRTPace="&set_autoDetectBookedRTPace.";
            firstSnapshotDt=&First_snapshot_dt.;
            paceLastSnapshotDT=%SYSEVALF(&Pace_End_dt.+1);
            maxBackfillOccupancyDT=&maximum_occupancy_dt.;
            sasDatasetsOnly="&SasDatasetsOnly.";
            preserveHotelAccomPace="&preserveHotelAccomPace.";
            usePaceGroupMaster="&usePaceGroupMaster.";
            backfillType="&Backfill_type.";
            RequestedMarketSegments="&RequestedMarketSegments.";
            totalProcessDuration=&runTime.;
            startUpDuration=&startuprunTime.;
            groupInvalidPercent=&Group_Booked_Difference_Percent.;
            stayedPaceDuration=&SasStayedPaceProcessingTime.;
            mktSegmentDuration=&mktsegruntime.;
            accomTypeDuration=&accomtyperuntime.;
            stayedPaceUpdated="&StayedPaceTablesUpdated.";
            stayedOccupancyDtCutOff=&stayed_minimum_occupancy_dt.;
            bookedPaceDuration="&SasBookedPaceProcessingTime.";
            bookedPaceUpdated="&BookedPaceTablesUpdated.";
            bookedOccupancyDtCutoff=&booked_minimum_occupancy_dt.;
            bookedDifferencePercent=&Booked_Stayed_Difference_Percent.;
            ProcessedTransMktSeg="&updatemslist.";
            ProcessedGroupMktSeg="%str(&groupmktseglist.)";
            ProcessedAccomTypes="%str(&accomsortcommalist.)";
            createDt=&createDt.;
            createTime=%sysfunc(time());
            ignoreCanceledReservations="&ignoreCanceledReservations.";
    run;
    proc append base=rm_datap.backfill_log data=&work_lib..backfill_log force;
    run;
    options compress=no;
%mend populate_backfill_log;