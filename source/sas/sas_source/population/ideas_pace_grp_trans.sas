%macro ideas_pace_grp_trans(rc=)/store;
    proc sql;
        create table &work_lib..Group_first_bde_dates as
                select b.group_id, min(business_day_end_dt) as first_bde_dt
                    from tenant.pace_group_block as b
                        inner join tenant.Group_master as c
                            on b.group_id=c.group_id
                    group by b.group_id;
    quit;

    proc sql;
        %if %upcase(&usePaceGroupMaster.)=FALSE %then
            %do;
            create table &work_lib..Group_Transactions1 as
                select "Group" as Transaction_Type format=$15., &pid. as property_id length=4, b.occupancy_dt, c.group_id,
                    "Grp"||put(c.group_id,9.)||put(b.accom_type_id,5.)||put(b.occupancy_dt,yymmdd10.)||put(b.business_day_end_dt,yymmdd10.) as reservation_identifier format=$50.,
                    "SS" as individual_status format=$2.,
                    b.business_day_end_dt as booking_dt format=date9.,
                case
                    when c.booking_dt ne . and c.booking_dt < c.start_dt then c.booking_dt
                    when c.booking_dt ne . and c.cut_off_date ne . then min(c.booking_dt,c.cut_off_date-60, c.start_dt-60)
                    when c.booking_dt ne . and c.cut_off_days ne . then min(c.booking_dt,(b.occupancy_dt - (c.cut_off_days+60)) , c.start_dt-60)
                    when c.booking_dt ne . then min(c.booking_dt,c.start_dt-60)
                    else min(c.start_dt-60, d.first_bde_dt)
                end
            as original_booking_dt format=date9., b.original_blocks, max(b.pickup,b.blocks) as blocks length=8,b.pickup as pickup length=8,
                b.occupancy_dt as Arrival_DT format=date9., b.occupancy_dt+1 as departure_dt format=date9., 1 as los length=3 format=5., . as cancellation_dt,
                b.accom_type_id as accom_type_id length=6, b.accom_type_id as stayed_accom_type_id length=6, c.mkt_seg_id as mkt_seg_id length=3, b.accom_type_id as booked_accom_type_id length=6,
                b.rate as room_revenue format=11.2, 0 as food_revenue format=11.2,
                0 as Beverage_revenue format=11.2, 0 as telecom_revenue format=11.2, 0 as other_revenue format=11.2, b.rate as total_revenue format=11.2 ,b.rate as rate_value,
            case
                when c.cut_off_date ne . then max(c.cut_off_date, c.booking_dt+1)
                when c.cut_off_days ne . and c.cut_off_days ne 0 then (b.occupancy_dt - c.cut_off_days)
                when cut_off_date eq . and (&useBookingDtAsCutoffDt. eq 1 and c.start_dt lte &first_snapshot_dt.) then booking_dt
                else c.start_dt
            end
            as block_cutoff_use_pickup_dt format=date9.
                from tenant.pace_group_block as b inner join tenant.Group_master as c on b.group_id=c.group_id
                    inner join &work_lib..Group_first_bde_dates d
                        on b.group_id=d.group_id
                    where upcase(c.group_status_code)="DEFINITE" and
                        b.business_day_end_dt<=&first_snapshot_dt. and
                        c.mkt_seg_id in (select mkt_seg_id from &work_lib..group_market_segments) and
                        b.occupancy_dt between &minimum_occupancy_dt. and &maximum_occupancy_dt. and b.occupancy_dt between c.start_dt and c.end_dt
                    order by b.occupancy_dt, booking_dt, c.group_id, accom_type_id;
            %end;
        %else %do;
            %let sql_first_snapshot_dt = %sysfunc(putn(&first_snapshot_dt,yymmddd10.));
            %let sql_minimum_occupancy_dt = %sysfunc(putn(&minimum_occupancy_dt,yymmddd10.));
            %let sql_maximum_occupancy_dt = %sysfunc(putn(&maximum_occupancy_dt,yymmddd10.));
            connect to odbc (&connect_str autocommit=no);
            create table &work_lib..Group_Transactions1 as
            select "Group" as Transaction_Type format=$15., &pid. as property_id length=4, occupancy_dt, group_id,
                            "Grp"||put(group_id,9.)||put(accom_type_id,5.)||put(occupancy_dt,yymmdd10.)||put(business_day_end_dt,yymmdd10.) as reservation_identifier format=$50.,
                        case when group_status_code='CANCELLED' then "XX"
                            else "SS"
                        end as individual_status format=$2.,
                            business_day_end_dt as booking_dt format=date9.,
                        case
                            when booking_dt ne . and booking_dt < start_dt then booking_dt
                            when booking_dt ne . and cut_off_date ne . then min(booking_dt,cut_off_date-60, start_dt-60)
                            when booking_dt ne . and cut_off_days ne . then min(booking_dt,(occupancy_dt - (cut_off_days+60)) , start_dt-60)
                            when booking_dt ne . then min(booking_dt,start_dt-60)
                            else min(start_dt-60, first_bde_dt)
                        end
                    as original_booking_dt format=date9.,
                         original_blocks,
                         case
                             when cancel_dt ne . and business_day_end_dt ge cancel_dt then 0
                             when cancel_dt ne . and sum_block eq 0 and sum_orig gt 0 then original_blocks
                             else blocks
                         end
                         as blocks length=8,
                         case
                             when cancel_dt ne . and business_day_end_dt ge cancel_dt then 0
                             else pickup
                         end
                         as pickup length=8,
                        occupancy_dt as Arrival_DT format=date9., occupancy_dt+1 as departure_dt format=date9., 1 as los length=3 format=5., cancel_dt as cancellation_dt,
                        accom_type_id as accom_type_id length=6, accom_type_id as stayed_accom_type_id length=6, mkt_seg_id as mkt_seg_id length=3, accom_type_id as booked_accom_type_id length=6,
                        rate as room_revenue format=11.2, 0 as food_revenue format=11.2,
                        0 as Beverage_revenue format=11.2, 0 as telecom_revenue format=11.2, 0 as other_revenue format=11.2,
                        rate as total_revenue format=11.2 ,
                        rate as rate_value,
                    case
                        when cut_off_date ne . then max(cut_off_date, booking_dt+1)
                        when cut_off_days ne . and cut_off_days ne 0 then (occupancy_dt - cut_off_days)
                        else start_dt
                    end
                    as block_cutoff_use_pickup_dt format=date9.
                from connection to odbc (
                    exec get_group_transaction_from_pgm "&sql_first_snapshot_dt","&sql_minimum_occupancy_dt","&sql_maximum_occupancy_dt"
                );
            disconnect from odbc;

                        /* If the blocks is 0 for all cancelled blocks and original_blocks is not 0, then update blocks = original_blocks */
            %end;
    quit;

/* for cancelled groups extra info is : i. Changed status ii. cancel date iii. Blocks logic (If the block value is 0 for all dates beyond cancel date -1 , then further logic would have no effect)*/

    proc sort data=&work_lib..Group_Transactions1;
        by occupancy_dt group_id accom_type_id booking_dt;
    run;

    data &work_lib..Group_Transactions2_temp (drop=pace_blocks save_first_booking_dt save_occupancy_dt);
        retain original_applied;
        retain save_first_booking_dt save_occupancy_dt;
        set &work_lib..Group_Transactions1 end=last;
        by occupancy_dt group_id accom_type_id;
        pace_blocks=blocks;

        if _n_=1 or (first.occupancy_dt or first.group_id or first.accom_type_id) then
            do;
                save_occupancy_dt = occupancy_dt;
                original_applied="N";
                save_first_booking_dt=booking_dt;
            end;

        if save_first_booking_dt=booking_dt and original_applied ="N" and original_booking_dt ne booking_dt then
            do;
                if (cancellation_dt eq . OR ((original_booking_dt) lt cancellation_dt) ) then
                do;
                    blocks=original_blocks;
                    booking_dt=original_booking_dt;
                    output &work_lib..Group_Transactions2_temp;
                end;
                /* Check for cancellation date before updating booking_dt+1 */
                if (cancellation_dt eq . OR ((original_booking_dt+1) lt cancellation_dt) ) then
                do;
                    blocks=pace_blocks;
                    booking_dt=original_booking_dt+1;
                    output &work_lib..Group_Transactions2_temp;
                end;

            end;
        else
            do;
                original_applied="Y";
                output &work_lib..Group_Transactions2_temp;
            end;

        /* Check for pickup entry to be inserted on cutoff date*/
        if ( last.accom_type_id=1 and (cancellation_dt eq . OR (block_cutoff_use_pickup_dt lt cancellation_dt)) and (block_cutoff_use_pickup_dt gt (original_booking_dt+1)) ) then
        do;
            booking_dt=block_cutoff_use_pickup_dt;
            output &work_lib..Group_Transactions2_temp;
        end;
    run;

    PROC SQL;
     DROP TABLE &work_lib..Group_Transactions1;
    QUIT;

    proc sort data=&work_lib..Group_Transactions2_temp EQUALS NODUPKEY;
        by occupancy_dt group_id accom_type_id booking_dt;
    run;

    /*
     * Update inserted pickup entry on cutoff date to reflect correct revenue values and pickup/block info
     */
    data &work_lib..Group_Transactions2 (drop= prev_pickup prev_room_revenue prev_food_revenue prev_bev_revenue
                                            prev_tel_revenue prev_other_revenue prev_total_revenue prev_rate_value);
        retain prev_pickup prev_room_revenue prev_food_revenue prev_bev_revenue prev_tel_revenue prev_other_revenue prev_total_revenue prev_rate_value;
        set &work_lib..Group_Transactions2_temp end=last;
        by occupancy_dt group_id accom_type_id;

        if booking_dt lt block_cutoff_use_pickup_dt or first.accom_type_id=1 then
            do;
                prev_pickup=pickup;
                prev_room_revenue=room_revenue;
                prev_food_revenue=food_revenue;
                prev_bev_revenue=beverage_revenue;
                prev_tel_revenue=telecom_revenue;
                prev_other_revenue=other_revenue;
                prev_total_revenue=total_revenue;
                prev_rate_value=rate_value;
            end;
        if booking_dt eq block_cutoff_use_pickup_dt then
            do;
                blocks=prev_pickup;
                pickup=prev_pickup;
                room_revenue=prev_room_revenue;
                food_revenue=prev_food_revenue;
                beverage_revenue=prev_bev_revenue;
                telecom_revenue=prev_tel_revenue;
                other_revenue=prev_other_revenue;
                total_revenue=prev_total_revenue;
                rate_value=prev_rate_value;
            end;
        if booking_dt gt block_cutoff_use_pickup_dt then
             do;
                 blocks=pickup;
             end;
    run;

    PROC SQL;
        DROP TABLE &work_lib..Group_Transactions2_temp;
    QUIT;

    proc sort data=&work_lib..Group_Transactions2;
        by occupancy_dt group_id accom_type_id booking_dt;
    run;

    data &work_lib..Group_Transactions3(drop=save_accom_type_id);
        retain previous_blocks;
        retain pace_blocks save_occupancy_dt save_accom_type_id save_group_id;
        set &work_lib..Group_Transactions2 end=last;
        by occupancy_dt group_id accom_type_id booking_dt;
        pace_blocks=blocks;
        pace_pickup=pickup;
        pace_booking_dt=booking_dt;

        if _N_=1 or save_occupancy_dt ne occupancy_dt or save_group_id ne group_id or save_accom_type_id ne accom_type_id then
            do;
                previous_blocks=0;
                save_group_id=group_id;
                save_occupancy_dt=occupancy_dt;
                save_accom_type_id=accom_type_id;
            end;

        if (block_cutoff_use_pickup_dt ne . and block_cutoff_use_pickup_dt < booking_dt ) then
            do;
                booking_dt=block_cutoff_use_pickup_dt;
                blocks=pace_pickup-previous_blocks;
                previous_blocks=pace_pickup;
                output &work_lib..Group_Transactions3;
            end;
        else
            do;
                blocks=pace_blocks-previous_blocks;
                previous_blocks=pace_blocks;
                output &work_lib..Group_Transactions3;
            end;
    run;

    PROC SQL;
        DROP TABLE &work_lib..Group_Transactions2;
    QUIT;

    data &work_lib..Group_Transactions_exploded (drop=group_id blocks pickup save_reservation_identifier block_cutoff_use_pickup_dt
        previous_blocks original_booking_dt original_blocks pace_blocks pace_pickup group_solds isolds);
        format save_reservation_identifier $50.;
        format solds 3.;
        set &work_lib..Group_Transactions3;
        where booking_dt<&first_snapshot_dt.;
        save_reservation_identifier=compress(reservation_identifier);
        Group_solds=blocks;

        if Group_solds >= 1 then
            do;
                do isolds = 1 to Group_solds;
                    solds=1;
                    reservation_identifier=trim(save_reservation_identifier)||put(isolds,4.);
                    output &work_lib..Group_Transactions_exploded;
                end;
            end;
        else if Group_solds < 0 then
            do;
                do isolds = -1 to Group_solds by -1;
                    solds=-1;
                    reservation_identifier=trim(save_reservation_identifier)||put(isolds,4.);
                    output &work_lib..Group_Transactions_exploded;
                end;
            end;
    run;

    PROC SQL;
        DROP TABLE &work_lib..Group_Transactions3;
    QUIT;

    %if %upcase(&useReservationNight.) eq TRUE %then %do;
        data &work_lib..Group_Transactions_exploded;
            set &work_lib..Group_Transactions_exploded;
            Number_Nights=1;
            occupancy_DT=arrival_DT;
        run;
    %end;
%mend ideas_pace_grp_trans;