/*
 * Creates org_mkt_accom_inventory using ma_org_* partitions(i.e. BOOKED data).
 */
%macro create_mkt_accom_booked_inv()/store;

    %let mktsegi=1;

    %do %while (%scan(&updatemslist., &mktsegi., |) ne);

        data _null_;
            call symputx ('mkt_seg',%scan(&updatemslist., &mktsegi., | ),'l');
        run;

        proc Sql;
            create table &work_lib..mkt_accom_final_temp as
                select &mkt_seg. as mkt_seg_id format=5., accom_type_id , occupancy_dt, capture_dttm,
                    arrivals, departures, no_shows, cancellations, Room_revenue, rooms_sold, Food_Revenue, Total_Revenue
                from rm_part.&market_segment_prefix._&mkt_seg.
                    where datepart(capture_dttm)<=&first_snapshot_dt. and occupancy_dt between &minimum_occupancy_dt. and &maximum_occupancy_dt.
                        order by accom_type_id, occupancy_dt, capture_dttm;
        quit;

        %put Updating Market Segment &mkt_seg. into org_mkt_accom_inventory;

        data &work_lib..mkt_accom_pace_all_exp (keep=accom_type_id occupancy_dt capture_dttm arrivals
            departures no_shows cancellations Room_revenue rooms_sold Food_Revenue Total_Revenue);
            format idate date9.;
            format itime time8.;
            format save_capture_dttm datetime20.;
            retain save_accom_type_id save_occupancy_dt save_capture_dttm
                save_arrivals save_departures save_no_shows save_cancellations save_Room_Revenue
                save_rooms_sold save_Food_Revenue save_Total_Revenue
                original_capture_dttm original_accom_type_id original_occupancy_dt original_arrivals original_departures original_no_shows original_cancellations original_Room_Revenue
                original_rooms_sold original_Food_Revenue original_Total_Revenue;
            set &work_lib..mkt_accom_final_temp end=last;
            by accom_type_id occupancy_dt capture_dttm;

            if _n_=1 or first.accom_type_id=1 or first.occupancy_dt=1 then
                do;
                    save_accom_type_id=accom_type_id;
                    save_occupancy_dt=occupancy_dt;
                    save_capture_dttm=capture_dttm;
                    save_arrivals=arrivals;
                    save_departures=departures;
                    save_no_shows=no_shows;
                    save_cancellations=cancellations;
                    save_Room_Revenue=Room_revenue;
                    save_rooms_sold=rooms_sold;
                    save_Food_Revenue=Food_Revenue;
                    save_Total_Revenue=Total_Revenue;
                end;

            output &work_lib..mkt_accom_pace_all_exp;
            original_accom_type_id=accom_type_id;
            original_occupancy_dt=occupancy_dt;
            original_capture_dttm=capture_dttm;
            original_arrivals=arrivals;
            original_departures=departures;
            original_no_shows=no_shows;
            original_cancellations=cancellations;
            original_Room_Revenue=Room_revenue;
            original_rooms_sold=rooms_sold;
            original_Food_Revenue=Food_Revenue;
            original_Total_Revenue=Total_Revenue;

            if _n_> 1 and datepart(capture_dttm) ne datepart(save_capture_dttm)+1 then
                do;
                    do idate=datepart(save_capture_dttm)+1 to datepart(capture_dttm)-1;
                        itime=timepart(save_capture_dttm);
                        capture_dttm=dhms(idate,0,0,itime);
                        business_day_end_dt=idate;
                        accom_type_id=save_accom_type_id;
                        occupancy_dt=save_occupancy_dt;
                        arrivals=save_arrivals;
                        departures=save_departures;
                        no_shows=save_no_shows;
                        cancellations=save_cancellations;
                        Room_Revenue=save_Room_revenue;
                        rooms_sold=save_rooms_sold;
                        Food_Revenue=save_Food_Revenue;
                        Total_Revenue=save_Total_Revenue;
                        output &work_lib..mkt_accom_pace_all_exp;
                    end;
                end;

            if _n_ > 1 and (last=1 or last.accom_type_id=1 or last.occupancy_dt=1) and datepart(original_capture_dttm) LT min(&First_snapshot_dt.-1,original_occupancy_dt+1) then
                do;
                    do idate=datepart(original_capture_dttm)+1 to  min(&First_snapshot_dt.-1,original_occupancy_dt+1);
                        itime=timepart(original_capture_dttm);
                        capture_dttm=dhms(idate,0,0,itime);
                        business_day_end_dt=idate;
                        accom_type_id=Original_accom_type_id;
                        occupancy_dt=Original_occupancy_dt;
                        arrivals=Original_arrivals;
                        departures=Original_departures;
                        no_shows=Original_no_shows;
                        cancellations=Original_cancellations;
                        Room_Revenue=Original_Room_revenue;
                        rooms_sold=Original_rooms_sold;
                        Food_Revenue=Original_Food_Revenue;
                        Total_Revenue=Original_Total_Revenue;
                        output &work_lib..mkt_accom_pace_all_exp;
                    end;
                end;

            save_accom_type_id=original_accom_type_id;
            save_occupancy_dt=original_occupancy_dt;
            save_capture_dttm=original_capture_dttm;
            save_arrivals=original_arrivals;
            save_departures=original_departures;
            save_no_shows=original_no_shows;
            save_cancellations=original_cancellations;
            save_Room_Revenue=original_Room_revenue;
            save_rooms_sold=original_rooms_sold;
            save_Food_Revenue=original_Food_Revenue;
            save_Total_Revenue=original_Total_Revenue;
        run;

        proc sql;
            create table &work_lib..mkt_accom_inventory_db as
                select &Property_ID. as property_id, a.occupancy_dt, a.capture_dttm as Snapshot_DTTM ,
                        datepart(a.capture_dttm)-1 as Business_day_end_dt,
                        &mkt_seg. as mkt_seg_id length=8,
                        a.accom_type_id,
                        a.rooms_sold,
                        a.arrivals,
                        a.departures,
                        a.no_shows,
                        a.cancellations,
                        a.Room_revenue,
                        a.Food_Revenue,
                        a.Total_Revenue,
                        b.file_metadata_id,
                        month(a.occupancy_dt) as Month_ID,
                        year(a.occupancy_dt) - 2000 as Year_ID,
                        datetime() as Last_Updated_Dttm
                    from &work_lib..mkt_accom_pace_all_exp as a
                        inner join &work_lib..file_metadata as b
                            on datepart(a.capture_dttm)=b.snapshot_dt;
        quit;


        proc sort data =&work_lib..mkt_accom_inventory_db;
                by accom_type_id occupancy_dt descending Snapshot_DTTM;
        run;

        data &work_lib..mkt_accom_nonpace;
            set &work_lib..mkt_accom_inventory_db;
            by accom_type_id occupancy_dt;
            if first.occupancy_dt then
                do;
                    length mkt_seg_id 6;
                    output &work_lib..mkt_accom_nonpace;
                end;
        run;

        proc append base=rm_data.org_mkt_accom_inventory data=&work_lib..mkt_accom_nonpace(keep=mkt_seg_id
                accom_type_id occupancy_dt rooms_sold arrivals departures no_shows cancellations room_revenue rooms_sold );
        run;

        %let mktsegi = %sysevalf(&mktsegi. + 1);
    %end;

%mend create_mkt_accom_booked_inv;




/*
 * when Accom_Type_Option=STAYED, creates mkt_accom_los_inventory and bde_mkt_accom_los_inventory using data from ma_los_* partitions(i.e. STAYED data).
 * when Accom_Type_Option=BOOKED, creates org_mkt_accom_los_inventory using data from ma_los_org_* partitions(i.e. BOOKED data).
 */
%macro create_mkt_accom_los_inv(Accom_Type_Option=)/store;

    %if %upcase(&Accom_Type_Option.)=STAYED %then
        %do;
            %stayed_mkt_accom_los_inv();
        %end;
    %else %do;
            %booked_mkt_accom_los_inv();
        %end;

%mend create_mkt_accom_los_inv;



%macro stayed_mkt_accom_los_inv()/store;

    %let mkt_accom_inv_ds=mkt_accom_los_inventory;

    %let mktsegi=1;

    %do %while (%scan(&updatemslist., &mktsegi., |) ne);

        data _null_;
            call symputx ('mkt_seg',%scan(&updatemslist., &mktsegi., | ),'l');
        run;

        proc Sql;
            create table &work_lib..mkt_accom_final_temp as
                select &mkt_seg. as mkt_seg_id format=5., accom_type_id , arrival_dt, capture_dttm, los,
                    arrivals, no_shows, cancellations, Room_revenue,Total_Revenue
                from rm_part.&market_segment_los_prefix._&mkt_seg.
                    where datepart(capture_dttm)<=&first_snapshot_dt. and arrival_dt between &minimum_occupancy_dt. and &maximum_occupancy_dt.
                        order by accom_type_id, arrival_dt, los, capture_dttm;
        quit;

        %put Updating Market Segment &mkt_seg. into &mkt_accom_inv_ds.;

        data &work_lib..mkt_accom_los_pace_all_exp (keep=accom_type_id arrival_dt capture_dttm arrivals los
            no_shows cancellations Room_revenue Total_Revenue);
            format idate date9.;
            format itime time8.;
            format save_capture_dttm datetime20.;
            retain save_accom_type_id save_arrival_dt save_capture_dttm save_los
                save_arrivals save_no_shows save_cancellations save_Room_Revenue save_Total_Revenue
                original_capture_dttm original_accom_type_id original_arrival_dt original_los original_arrivals original_no_shows original_cancellations original_Room_Revenue original_Total_Revenue;
            set &work_lib..mkt_accom_final_temp end=last;
            by accom_type_id arrival_dt los capture_dttm;

            if _n_=1 or first.accom_type_id=1 or first.arrival_dt=1 or first.los=1 then
                do;
                    save_accom_type_id=accom_type_id;
                    save_arrival_dt=arrival_dt;
                    save_capture_dttm=capture_dttm;
                    save_arrivals=arrivals;
                    save_no_shows=no_shows;
                    save_cancellations=cancellations;
                    save_Room_Revenue=Room_revenue;
                    save_los=los;
                    save_Total_Revenue=Total_Revenue;
                end;

            output &work_lib..mkt_accom_los_pace_all_exp;
            original_accom_type_id=accom_type_id;
            original_arrival_dt=arrival_dt;
            original_capture_dttm=capture_dttm;
            original_arrivals=arrivals;
            original_no_shows=no_shows;
            original_cancellations=cancellations;
            original_Room_Revenue=Room_revenue;
            original_los=los;
            original_Total_Revenue=Total_Revenue;

            if _n_> 1 and datepart(capture_dttm) ne datepart(save_capture_dttm)+1 then
                do;
                    do idate=datepart(save_capture_dttm)+1 to datepart(capture_dttm)-1;
                        itime=timepart(save_capture_dttm);
                        capture_dttm=dhms(idate,0,0,itime);
                        business_day_end_dt=idate;
                        accom_type_id=save_accom_type_id;
                        arrival_dt=save_arrival_dt;
                        arrivals=save_arrivals;
                        no_shows=save_no_shows;
                        cancellations=save_cancellations;
                        Room_Revenue=save_Room_revenue;
                        Total_Revenue=save_Total_Revenue;
                        los=save_los;
                        output &work_lib..mkt_accom_los_pace_all_exp;
                    end;
                end;

            if _n_ > 1 and (last=1 or last.accom_type_id=1 or last.arrival_dt=1 or last.los=1) and datepart(original_capture_dttm) LT min(&First_snapshot_dt.-1,original_arrival_dt+1) then
                do;
                    do idate=datepart(original_capture_dttm)+1 to  min(&First_snapshot_dt.-1,original_arrival_dt+1);
                        itime=timepart(original_capture_dttm);
                        capture_dttm=dhms(idate,0,0,itime);
                        business_day_end_dt=idate;
                        accom_type_id=Original_accom_type_id;
                        arrival_dt=Original_arrival_dt;
                        arrivals=Original_arrivals;
                        no_shows=Original_no_shows;
                        cancellations=Original_cancellations;
                        Room_Revenue=Original_Room_revenue;
                        los=Original_los;
                        Total_Revenue=Original_Total_Revenue;
                        output &work_lib..mkt_accom_los_pace_all_exp;
                    end;
                end;

            save_accom_type_id=original_accom_type_id;
            save_arrival_dt=original_arrival_dt;
            save_capture_dttm=original_capture_dttm;
            save_arrivals=original_arrivals;
            save_no_shows=original_no_shows;
            save_cancellations=original_cancellations;
            save_Room_Revenue=original_Room_revenue;
            save_los=original_los;
            save_Total_Revenue=original_Total_Revenue;
        run;

        proc sql;
            create table &work_lib..mkt_accom_los_inventory_db as
                select &Property_ID. as property_id, a.arrival_dt, a.capture_dttm as Snapshot_DTTM ,
                        datepart(a.capture_dttm)-1 as Business_day_end_dt,
                        &mkt_seg. as mkt_seg_id length=8,
                        a.accom_type_id,
                        a.los,
                        a.arrivals,
                        a.no_shows,
                        a.cancellations,
                        a.Room_revenue,
                        a.Total_Revenue,
                        month(a.arrival_dt) as Month_ID,
                        year(a.arrival_dt) - 2000 as Year_ID,
                        datetime() as Last_Updated_Dttm
                    from &work_lib..mkt_accom_los_pace_all_exp as a;
        quit;


        proc sort data =&work_lib..mkt_accom_los_inventory_db;
                by accom_type_id arrival_dt los descending Snapshot_DTTM;
        run;

        data &work_lib..mkt_accom_los_nonpace;
            set &work_lib..mkt_accom_los_inventory_db;
            by accom_type_id arrival_dt los;
            if first.los then
                do;
                    length property_id mkt_seg_id 6;
                    output &work_lib..mkt_accom_los_nonpace;
                end;
        run;

        proc append base=rm_data.&mkt_accom_inv_ds. data=&work_lib..mkt_accom_los_nonpace(keep=property_id mkt_seg_id arrival_dt
                accom_type_id los arrivals no_shows cancellations room_revenue total_revenue);
        run;

        %let mktsegi = %sysevalf(&mktsegi. + 1);
    %end;

    proc append base=rm_data.bde_mkt_accom_los_inventory data=rm_data.&mkt_accom_inv_ds. force;
    run;

%mend stayed_mkt_accom_los_inv;




%macro booked_mkt_accom_los_inv()/store;

    %let mkt_accom_inv_ds=org_mkt_accom_los_inventory;

    %let mktsegi=1;

    %do %while (%scan(&updatemslist., &mktsegi., |) ne);

        data _null_;
            call symputx ('mkt_seg',%scan(&updatemslist., &mktsegi., | ),'l');
        run;

        proc Sql;
            create table &work_lib..mkt_accom_final_temp as
                select &mkt_seg. as mkt_seg_id format=5., accom_type_id , arrival_dt, capture_dttm, los,
                    arrivals, no_shows, cancellations, Room_revenue
                from rm_part.&market_segment_los_prefix._&mkt_seg.
                    where datepart(capture_dttm)<=&first_snapshot_dt. and arrival_dt between &minimum_occupancy_dt. and &maximum_occupancy_dt.
                        order by accom_type_id, arrival_dt, los, capture_dttm;
        quit;

        %put Updating Market Segment &mkt_seg. into &mkt_accom_inv_ds.;

        data &work_lib..mkt_accom_los_pace_all_exp (keep=accom_type_id arrival_dt capture_dttm arrivals los
            no_shows cancellations Room_revenue);
            format idate date9.;
            format itime time8.;
            format save_capture_dttm datetime20.;
            retain save_accom_type_id save_arrival_dt save_capture_dttm save_los
                save_arrivals save_no_shows save_cancellations save_Room_Revenue
                original_capture_dttm original_accom_type_id original_arrival_dt original_los original_arrivals original_no_shows original_cancellations original_Room_Revenue;
            set &work_lib..mkt_accom_final_temp end=last;
            by accom_type_id arrival_dt los capture_dttm;

            if _n_=1 or first.accom_type_id=1 or first.arrival_dt=1 or first.los=1 then
                do;
                    save_accom_type_id=accom_type_id;
                    save_arrival_dt=arrival_dt;
                    save_capture_dttm=capture_dttm;
                    save_arrivals=arrivals;
                    save_no_shows=no_shows;
                    save_cancellations=cancellations;
                    save_Room_Revenue=Room_revenue;
                    save_los=los;
                end;

            output &work_lib..mkt_accom_los_pace_all_exp;
            original_accom_type_id=accom_type_id;
            original_arrival_dt=arrival_dt;
            original_capture_dttm=capture_dttm;
            original_arrivals=arrivals;
            original_no_shows=no_shows;
            original_cancellations=cancellations;
            original_Room_Revenue=Room_revenue;
            original_los=los;

            if _n_> 1 and datepart(capture_dttm) ne datepart(save_capture_dttm)+1 then
                do;
                    do idate=datepart(save_capture_dttm)+1 to datepart(capture_dttm)-1;
                        itime=timepart(save_capture_dttm);
                        capture_dttm=dhms(idate,0,0,itime);
                        business_day_end_dt=idate;
                        accom_type_id=save_accom_type_id;
                        arrival_dt=save_arrival_dt;
                        arrivals=save_arrivals;
                        no_shows=save_no_shows;
                        cancellations=save_cancellations;
                        Room_Revenue=save_Room_revenue;
                        los=save_los;
                        output &work_lib..mkt_accom_los_pace_all_exp;
                    end;
                end;

            if _n_ > 1 and (last=1 or last.accom_type_id=1 or last.arrival_dt=1 or last.los=1) and datepart(original_capture_dttm) LT min(&First_snapshot_dt.-1,original_arrival_dt+1) then
                do;
                    do idate=datepart(original_capture_dttm)+1 to  min(&First_snapshot_dt.-1,original_arrival_dt+1);
                        itime=timepart(original_capture_dttm);
                        capture_dttm=dhms(idate,0,0,itime);
                        business_day_end_dt=idate;
                        accom_type_id=Original_accom_type_id;
                        arrival_dt=Original_arrival_dt;
                        arrivals=Original_arrivals;
                        no_shows=Original_no_shows;
                        cancellations=Original_cancellations;
                        Room_Revenue=Original_Room_revenue;
                        los=Original_los;
                        output &work_lib..mkt_accom_los_pace_all_exp;
                    end;
                end;

            save_accom_type_id=original_accom_type_id;
            save_arrival_dt=original_arrival_dt;
            save_capture_dttm=original_capture_dttm;
            save_arrivals=original_arrivals;
            save_no_shows=original_no_shows;
            save_cancellations=original_cancellations;
            save_Room_Revenue=original_Room_revenue;
            save_los=original_los;
        run;

        proc sql;
            create table &work_lib..mkt_accom_los_inventory_db as
                select &Property_ID. as property_id, a.arrival_dt, a.capture_dttm as Snapshot_DTTM ,
                        datepart(a.capture_dttm)-1 as Business_day_end_dt,
                        &mkt_seg. as mkt_seg_id length=8,
                        a.accom_type_id,
                        a.los,
                        a.arrivals,
                        a.no_shows,
                        a.cancellations,
                        a.Room_revenue,
                        month(a.arrival_dt) as Month_ID,
                        year(a.arrival_dt) - 2000 as Year_ID,
                        datetime() as Last_Updated_Dttm
                    from &work_lib..mkt_accom_los_pace_all_exp as a;
        quit;


        proc sort data =&work_lib..mkt_accom_los_inventory_db;
                by accom_type_id arrival_dt los descending Snapshot_DTTM;
        run;

        data &work_lib..mkt_accom_los_nonpace;
            set &work_lib..mkt_accom_los_inventory_db;
            by accom_type_id arrival_dt los;
            if first.los then
                do;
                    length mkt_seg_id 6;
                    output &work_lib..mkt_accom_los_nonpace;
                end;
        run;

        proc append base=rm_data.&mkt_accom_inv_ds. data=&work_lib..mkt_accom_los_nonpace(keep=mkt_seg_id arrival_dt
                accom_type_id los arrivals no_shows cancellations room_revenue);
        run;

        %let mktsegi = %sysevalf(&mktsegi. + 1);
    %end;

%mend booked_mkt_accom_los_inv;