%macro ideas_type3_cleanup(lib=in_xml)/store;
    /* options mprint; */
    /* Use these options for debugging*/

    /*
    Options nomacrogen NoSymbolgen nomlogic nomprint nomfile;
    */

    /* Variables */
	%global work_lib work_dir;
    %local  error_bit rm_data;
    %local c_var m_var error_codes;
    %global etl_status err_str err_ct;
    %let etl_status = 0;
    %let error_codes = .;
    %PUT ---------------------------------------------------------------------;
    %PUT ------------------ Starting Type 3 Clean Up -----------------------;
    %PUT ---------------------------------------------------------------------;
    %put;
    %put Work Library Path: %sysfunc(pathname(work));

    proc Datasets library = work memtype=data;
    quit;

    /*%ideas_util_tmp_lib(_lib=work_lib,_dir=work_dir); */
    data _null_;
        set  &lib..requestHeader;
        call symputx('property_Id',propertyId,'l');
        call symputx('requestId',requestId,'l');
        call symputx('tenant_server',tenant_server,'l');
        call symputx('server_instance',tenant_server_instance,'l');
        call symputx('tenant_pwd',tenant_password,'l');
        call symputx('tenant_user',tenant_user,'l');
        call symputx('tenant_db',tenant_database,'l');
        call symputx('port',tenant_db_port,'l');
        call symputx('saspath',dataset_path,'l');
        stop;
    run;

    %if &syscc > 4 %then %do;
        %ideas_util_report_errors(910, 'Failed to read request header', '', &request_Id., 1)
        %let etl_status = 1;
        %goto EXIT;
    %end;

    data _null_;
        set  &lib..populationrequest;
        call symputx('etl_file_name',etlfilename,'g');
        call symputx('c_var',clientDataStorageValue,'g');
 		call symputx('booked_org_enabled',BookedVsOriginalEnabled,'g');
		call symputx('extendedLOSDataEnabled',ExtendedLOSDataEnabled,'g');
        if operationtype ne "BDE" and operationtype ne "CDP" then
            call symputx('OperationType','BDE','g');
        else call symputx('OperationType',OperationType,'g');
        stop;
    run;

	%put BookedVsOriginalEnabled &booked_org_enabled;
	%put ExtendedLOSDataEnabled &extendedLOSDataEnabled;

    %if &syscc > 4 %then %do;
        %ideas_util_report_errors(909, 'Failed to read Type Three Population request', '', &request_Id., 1)
        %let etl_status = 1;
        %goto EXIT;
    %end;

    %let request_id = %str(&requestid.);
    %let sas_path = %str(&saspath.);
    %let mylog_path = %str(&saspath.);

    %if &work_library.=work %then %do;
        %let work_lib=work;
        %let work_path=%sysfunc(pathname(work));
    %end;
    %else %do;
        %let work_lib=&work_library.;
        %let work_path_drive=%substr (%sysfunc(pathname(work)),1,2);
        %let work_path_root=&work_path_drive./sas;

        %ideas_util_create_dir(dir=temp,base_path=&work_path_root.);
        %let work_path_root=&work_path_drive./sas/temp;

        %ideas_util_create_dir(dir=&property_id.,base_path=&work_path_root.);
        %let work_path_root=&work_path_drive./sas/temp/&property_id.;

        %ideas_util_create_dir(dir=work,base_path=&work_path_root.);
        %let work_path=&work_path_drive./sas/temp/&property_id./work;

        %if &memlib_option. = memlib %then %do;
            libname &work_lib "&work_path." memlib;
        %end;
        %else %do;
            libname &work_lib "&work_path.";
        %end;
    %end;

    /* Create errors table to store error logging */
    data &work_lib..errors;
        length err_ct error_cd 8 request_id error_params error_message $ 200;
        stop;
    run;


        %let rm_data_perm_path=&sas_path.;

        libname rm_data "&sas_path.";

		%IDEAS_UTIL_TRUNCATE_TABLE(rm_data.mkt_accom_los_inventory);

		%if &booked_org_enabled eq true %then %do;
			%IDEAS_UTIL_TRUNCATE_TABLE(rm_data.ORG_mkt_accom_inventory);
			%IDEAS_UTIL_TRUNCATE_TABLE(rm_data.ORG_mkt_accom_los_inventory);
			%IDEAS_UTIL_TRUNCATE_TABLE(rm_data.ORIGINAL_INFO);

		%end;

		%if &extendedLOSDataEnabled eq true %then %do;
			%IDEAS_UTIL_TRUNCATE_TABLE(rm_data.inhouse_los_chg_summary);
			%IDEAS_UTIL_TRUNCATE_TABLE(rm_data.los_extension_info);
		%end;


	%if &syscc > 4 %then %do;
        %ideas_util_report_errors(402, 'Failed to truncate table mkt_accom_los_inventory', '', &request_Id., 1);
        %let etl_status = 1;
        %goto EXIT;
    %end;

		%IDEAS_UTIL_TRUNCATE_TABLE(rm_data.bde_mkt_accom_los_inventory);

	%if &syscc > 4 %then %do;
        %ideas_util_report_errors(403, 'Failed to truncate table bde_mkt_accom_los_inventory', '', &request_Id., 1);
        %let etl_status = 1;
        %goto EXIT;
    %end;


%EXIT:

    proc Sql noprint;
        select count(*) into: errcount from &work_lib..errors where error_cd ne .;
    quit;

    %if &errcount. > 0 %then %do;

        proc Sql noprint;
            select distinct error_message into: err_str from &work_lib..errors;
        quit;

    %end;

    data _NULL_;
        FILE resXml;
        put '<?xml version="1.0" encoding="UTF-8"?>';
        put '<SASResponse xmlns="http://xml.common.pacman.tetris.ideas.com/schema/typethreepopulation/response/v1">';
        put "<ResponseHeader> <PropertyId>&property_Id</PropertyId>" @;
        put "<operationName>type3cleanup</operationName>" @;
        put "<requestId>&request_Id</requestId>";
    run;

    %if &syscc > 4 or &etl_status = 1 %then %do;
        %let syscc = 5;
        %let etl_status = 1;
        %put &error_codes;

        %if &error_codes ne  415 %then %do;
            %let  xptFileName=%substr(&etl_file_name., 1, %length(&etl_file_name.)-4).xpt;

            proc Cport lib=&work_lib.
                file="&xptFileName";
            run;

        %end;
    %end;

    data _NULL_;
        FILE resXml MOD;
        set &work_lib..errors;

        if _N_ > 0 then
            put "<ErrorMessage>&err_str.</ErrorMessage>";
    run;

    data _NULL_;
        FILE resXml MOD;
        put "</ResponseHeader></SASResponse>";
    run;

	%PUT ---------------------------------------------------------------------;
    %PUT ------------------ Finished Type 3 Clean Up -----------------------;
    %PUT ---------------------------------------------------------------------;
    %put;

%mend ideas_type3_cleanup;
