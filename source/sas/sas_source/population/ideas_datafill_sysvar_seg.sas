%macro ideas_datafill_sysvar_seg (property_id, m_var, work_lib)/store;
%let macroname=&SYSMACRONAME; 
	
%ideas_macro_log (start_end=START, macroname=&macroname.);

proc sql;
	create table &work_lib..myaccoms_seg as select distinct Accom_type_cd, market_segment as mkt_seg_code from &work_lib..mkt_accom order by Accom_type_cd, mkt_seg_code;
	create table &work_lib..sysaccoms as select distinct accom_type_code as accom_type_cd from tenant.accom_type where property_id = &property_id. and status_id=1 order by accom_type_cd;
	create table &work_lib..sysseg as select distinct mkt_seg_code from tenant.mkt_seg where property_id = &property_id. and status_id=1 order by mkt_seg_code;
	select accom_type_cd into: all_accom_list separated by '/' from &work_lib..sysaccoms;
quit;

/*build up the table of all possible accom mkt combinations*/

data &work_lib..accom_seg;
	attrib mkt_seg_code accom_type_cd format = $50.;
	delete; 
run;

%if %symexist(all_accom_list) %then %do;
	%let i = 1;
	%do %while (%scan(&all_accom_list, &i, '/') ne);
		%let prod = %scan(&all_accom_list, &i, '/');

		data &work_lib..accom_seg_&i; set &work_lib..sysseg;
			accom_type_cd = "&prod.";
		run;

		proc append base=&work_lib..accom_seg data=&work_lib..accom_seg_&i force;  quit;

	%let i = %sysevalf(&i + 1);
	%end;

proc sort data=&work_lib..accom_seg; 
by Accom_type_cd mkt_seg_code;

	data &work_lib..missaccoms_seg;
		merge &work_lib..accom_seg (in=a) &work_lib..myaccoms_seg (in=b);
		by Accom_type_cd mkt_seg_code;
        retain id 0 ; 
        if a and not b then do; 
		id=id+1; 
        output &work_lib..missaccoms_seg;
		end; 
	run;
/*	data &work_lib..missaccoms_seg; set &work_lib..missaccoms_seg;
		id = _n_;
	run; */ 

	proc sql;
		select id into: id_list separated by '/' from &work_lib..missaccoms_seg;
		create table &work_lib..myoccdates as select distinct occupancy_dt from &work_lib..OccDates;
	quit;

	%if %symexist(id_list) %then %do;

		data &work_lib..occdates_accom_seg;
			attrib occupancy_dt format = date9.;
			attrib mkt_seg_code accom_type_cd format = $50.;
		run;

		%let i = 1;
		%do %while (%scan(&id_list, &i, '/') ne);
			%let id = %scan(&id_list, &i, '/');

		/*	data &work_lib..sdata_&i; set &work_lib..myoccdates;
				id = &id.;
			run; */ 

			proc sql;
				create table &work_lib..sdata_&i as 
				select a.occupancy_dt, b.Accom_type_cd, b.mkt_seg_code 
				from &work_lib..myoccdates as a, &work_lib..missaccoms_seg as b 
				where &id. = b.id;
			quit;

			proc append base=&work_lib..occdates_accom_seg data=&work_lib..sdata_&i force;  quit;

			%let i = %sysevalf(&i + 1);
		%end;

		data &work_lib..occdates_accom_seg; set &work_lib..occdates_accom_seg;
			where mkt_seg_code ne '';
					rooms_not_avail_maint = &m_var.;
					rooms_not_avail_other = &m_var.;
					rooms_sold = &m_var.;
					arrivals= &m_var.;
					departures = &m_var.;
					room_revenue = &m_var.;
					food_revenue = &m_var.;
					total_revenue = &m_var.;
					cancellations = &m_var.;
					no_shows = &m_var.;
					record_type_id = 5;
					rename mkt_seg_code = market_segment;
		run;

		proc append base=&work_lib..mkt_accom data=&work_lib..occdates_accom_seg force; quit;
	%end;
%end;

%let macroname=&SYSMACRONAME; 
	
%ideas_macro_log (start_end=END, macroname=&macroname.);

%mend ideas_datafill_sysvar_seg;





