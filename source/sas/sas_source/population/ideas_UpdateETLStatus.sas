%macro ideas_UpdateETLStatus(status,tenant_db, tenant_server, port, server_instance, tenant_user, tenant_pwd, meta_id)/store;
%let macroname=&SYSMACRONAME; 
	
%ideas_macro_log (start_end=START, macroname=&macroname.);
    
	%local connect_Str;
	%let connect_str = driver={SQL Server Native Client 10.0}%str(;)server=&tenant_server\&server_instance%str(;)uid=&tenant_user%str(;)pwd=&tenant_pwd%str(;)database=&tenant_db%str(;)port=&port;
	%let connect_str=complete="&connect_str";

	proc sql;
		connect to odbc (&connect_str autocommit=no);
		execute(
			update file_metadata set process_status_id = &status. where file_metadata_id = &meta_id;

		%if &sqlrc ne 0 %then %do;
			rollback;
			%let error_codes = 909;
			%let err_str = 'ERROR updating process_status_id in file metadata';
			%goto EXIT;
		%end;

		commit;
		) by odbc;
	quit;

	%EXIT:

	%if &error_codes =909 %then %do;
		%ideas_util_report_errors(909, 'ERROR updating process_status_id in file metadata', '', &request_Id.,1)
		%let etl_status = 1;
	%end;

proc sql noprint;
	select count(*) into: error_ct from &work_lib..errors;
quit;

%if "&GL_ABORT_FLAG" eq "1" or &syscc > 4 or &error_ct. > 0 %then %do;
	%let GL_ABORT_FLAG =1;
	%let etl_status = 1;
%end;

%let macroname=&SYSMACRONAME; 
	
%ideas_macro_log (start_end=END, macroname=&macroname.);
%mend;
