%macro ideas_sync_accom_inv_pace(lib=in_xml)/store;

    options sortsize=3g;
        %Global pid ;
        %let runTime=0;
        %let syscc = 0;
        %let propertyClause=;
    %global error_codes error_bit etl_status err_str loadtype err_ct;
    options fullstimer mprint notes;
    %let etl_status = 0;

    %Put *********************************************************************;
    %Put ********************** Starting sync_accom_inv_pace *****************;
    %Put *********************************************************************;
    %Put;

    data _null_;
        set &lib..requestHeader;
        call symputx('Operation',OperationName,'g');
        call symputx('property_Id',propertyId,'g');
        call symputx('requestId',requestId,'g');
        call symputx('tenant_server',tenant_server,'g');
        call symputx('server_instance',tenant_server_instance,'g');
        call symputx('tenant_pwd',tenant_password,'g');
        call symputx('tenant_user',tenant_user,'g');
        call symputx('tenant_db',tenant_database,'g');
        call symputx('port',tenant_db_port,'g');
        call symputx('saspath',dataset_path,'g');
        stop;
    run;

    data _null_;
        set &lib..SyncAccomInvPaceRequestType;
        call symputx('firstSnapshotDt', firstSnapshotDt,'G');
        call symputx('startDt', startDt,'G');
        call symputx('endDt', endDt,'G');
    run;

    %if &work_library.=work %then
        %do;
            %let work_lib=work;
            %let work_path=%sysfunc(pathname(work));
        %end;
    %else
        %do;
            %let work_lib=&work_library.;
            %let work_path_drive=%substr (%sysfunc(pathname(work)),1,2);
            %let work_path_root=&work_path_drive./sas;

            %ideas_util_create_dir(dir=temp,base_path=&work_path_root.);
            %let work_path_root=&work_path_drive./sas/temp;

            %ideas_util_create_dir(dir=&property_id.,base_path=&work_path_root.);
            %let work_path_root=&work_path_drive./sas/temp/&property_id.;

            %ideas_util_create_dir(dir=work,base_path=&work_path_root.);
            %let work_path=&work_path_drive./sas/temp/&property_id./work;

            libname &work_lib "&work_path.";
        %end;

    data &work_lib..errors;
            length err_ct error_cd 8 request_id error_params error_message $ 200;
            stop;
        run;

        %if %sysfunc(libref(tenant)) ne 0 %then
            %do;
                %ideas_connect_tenant (&tenant_db., &tenant_server., &port., &server_instance., &tenant_user., &tenant_pwd.);

                %if &etl_status = 1 %then
                    %goto EXIT;
            %end;

        options fullstimer mprint notes;

        %let sas_path = %str(&saspath.);
        %let rm_data_perm_path=&sas_path.;
        %ideas_util_create_dir(dir=pacehistorydata,base_path=&rm_data_perm_path.);
        libname rm_data "&sas_path./pacehistorydata";
        libname rm_datap "&sas_path.";

        proc Datasets library = rm_data kill memtype=data nolist noprint;
        run;

        proc Datasets library=rm_datap nolist noprint;
            copy in=rm_datap out=rm_data
                CLONE
                CONSTRAINT=YES
                INDEX=YES;
            select accom_inventory_pace;
        run;

        %put &=firstSnapshotDt.;
        %put &=endDt.;
        %put &=startDt.;

        %let first_snap_date_sas_format = %sysfunc(inputn(&firstSnapshotDt.,yymmdd10.));
        %let end_date_sas_format = %sysfunc(inputn(&endDt.,yymmdd10.));
        %let start_date_sas_format = %sysfunc(inputn(&startDt.,yymmdd10.));

        %put &=firstSnapshotDt.;
        %put &=start_date_sas_format.;
        %put &=end_date_sas_format.;

        proc sql;
            delete from rm_data.accom_inventory_pace
                where datepart(capture_dttm) lt &first_snap_date_sas_format.;
        quit;

        %let iStartDate = &start_date_sas_format.;
        %let iEndDate = %sysfunc(intnx(day, &iStartDate., 100));

        %put &=iStartDate;
        %put &=iEndDate;

        %do %while( (&iStartDate <= &end_date_sas_format) );
        	proc sql;
                DROP TABLE &work_lib..ACCOM_INVENTORY_PACE;
                CREATE TABLE &work_lib..ACCOM_INVENTORY_PACE AS
                SELECT Property_ID length=6, Occupancy_DT format=best12. length=8, Accom_Type_ID length=6, Snapshot_DTTM AS CAPTURE_DTTM format=best12. length=8, Accom_Capacity length=4, Rooms_Not_Avail_Maint length=4, Rooms_Not_Avail_Other length=4, Rooms_Sold
                FROM TENANT.PACE_ACCOM_ACTIVITY
                WHERE Occupancy_DT BETWEEN &iStartDate. AND &iEndDate.;
        	quit;

            Proc append base=rm_data.accom_inventory_pace data=&work_lib..ACCOM_INVENTORY_PACE force;

        	%let iStartDate = %sysfunc(intnx(day, &iEndDate., 1));
        	%let iEndDate = %sysfunc(intnx(day, &iStartDate., 100));
        %end;

        proc sort data=rm_data.accom_inventory_pace;
            by property_id accom_type_id occupancy_dt capture_dttm;
        run;

        proc Datasets library=rm_datap noprint nolist;
            copy in=rm_data out=rm_datap move
                CLONE
                CONSTRAINT=YES
                INDEX=YES;
            select accom_inventory_pace;
        run;

        proc Datasets library = rm_data kill memtype=data nolist noprint;
         run;

    %EXIT:

        proc Sql noprint;
            select count(*) into: errcount from &work_lib..errors;
        quit;

        %if &errcount. > 0 %then
            %do;

                proc Sql noprint;
                    select distinct error_message into: err_str from &work_lib..errors;
                quit;

            %end;

        data _null_;
            FILE resXml;
            put '<?xml version="1.0" encoding="UTF-8"?>';
            put '<SyncAccomInvPaceSASResponse xmlns="http://xml.common.pacman.tetris.ideas.com/schema/syncaccominvpace/response/v1" ';
            put 'xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" ';
            put 'xsi:schemaLocation="http://xml.common.pacman.tetris.ideas.com/schema/syncaccominvpace/response/v1/sync_accom_inv_pace_response.xsd ">';
            put "<ResponseHeader> <PropertyId>&property_Id</PropertyId>" @;
            put "<operationName>syncaccominvpace</operationName>" @;
            put "<requestId>&requestId</requestId>";
            stop;
        run;

        %let responseMessage = SUCCESS;

        %if "&GL_ABORT_FLAG" eq "1" or &syscc > 4 %then
            %do;
                %let Status_desc=Failed;
                %let GL_ABORT_FLAG =1;

                proc Sort data=&work_lib..errors;
                    by request_id error_cd;
                run;

                %let responseMessage = &err_str.;

                data _NULL_;
                    FILE resXml MOD;
                    set &work_lib..errors;

                    if _N_ > 0 then
                        put "<ErrorMessage>&err_str.</ErrorMessage>";
                run;

            %end;

        data _NULL_;
            FILE resXml MOD;

            put "</ResponseHeader>";
            put "<SyncAccomInvPaceResponseType>";
            put "<responseMessage>&responseMessage.</responseMessage>" @;
            put "</SyncAccomInvPaceResponseType>";
            put "</SyncAccomInvPaceSASResponse>";
        run;




        %Put *********************************************************************;
        %Put *********************** Ending sync_accom_inv_pace ******************;
        %Put *********************************************************************;
        %Put;

    %mend ideas_sync_accom_inv_pace;