%macro ideas_pace_update_db(tenant_db=,tenant_server=,port=,server_instance=,tenant_user=,tenant_pwd=,rc=)/store mindelimiter=',';

    options minoperator;
    %local metad pace_acc_activity pace_mkt_activity pace_total_activity;
    %let mktexp_start_time=%sysfunc(datetime());
    %let market_segment_pace_updated=NO;
    %let skipZeroFillingWhere=and (rooms_sold >0 OR arrivals >0 OR departures >0 OR cancellations >0
                              OR no_shows >0 OR room_revenue !=0.0 OR food_revenue !=0.0 OR total_revenue !=0.0);

    proc Sql noprint;
        select count(*) into :pace_back_fill_count 
            from tenant.Pace_total_activity 
                where datepart(snapshot_dttm) <&first_snapshot_dt.;
    quit;

    %let First_Snapshot_dt_YYmmdd=%sysfunc(putn(&First_Snapshot_dt,yymmdd10.));
    %let first_snapshot_dt_sql=%NRBQUOTE('&First_Snapshot_dt_YYmmdd.');
    %let RequestPaceBuildLastDay_SQL=%NRBQUOTE('&RequestPaceBuildLastDay.');

    /* Pace points should be overwritten when the below toggle has value more than 0 */
    %if &overridePacePastBuildDays. > 0 %then %do;
        %let RequestPaceBuildLastDay_SQL=%NRBQUOTE('LASTDATE');
    %end;

    proc Sql;
        create table &work_lib..file_metadata as 
            select distinct a.property_id, a.file_metadata_ID, a.snapshot_dt
                from tenant.file_metadata as a 
                    where a.record_type_id=3 and a.isbde=1 and a.snapshot_dt < &First_snapshot_dt. 
                        order by a.snapshot_dt;
    quit;

    proc sort data=&work_lib..file_metadata nodupkey; 
    by snapshot_dt ; 
    run ; 

    %let mktsegi=1;
    %if %upcase(&UsePacmanData.)=FALSE %then %do;
    %let groupmktseglist = 999999999999;
        proc sql;
            select distinct mkt_seg_id
                into: updatemslist SEPARATED by '|' from tenant.mkt_seg
                 where mkt_seg_id ne 1
            order by mkt_seg_id;
            select *  into: groupmktseglist SEPARATED by '|'
            from (
            select a.mkt_seg_id
            from tenant.Mkt_Seg_Details_Proposed as a
            where a.business_Type_ID=1 or a.booking_block_pc>0
            union
            select b.mkt_seg_id
            from tenant.Mkt_Seg_Details as b
            where b.business_Type_ID=1 or b.booking_block_pc>0
            ) as grp_mkts;
        quit;
        proc Sql;
            create table &work_lib..accom_type_temp as
                select accom_type_id, accom_type_code
                    from tenant.accom_type
                        where accom_type_capacity >0
                            order by accom_type_code;
            create table &work_lib..transaction_accom_types as
                select distinct a.accom_type_id, b.accom_type_code
                    from tenant.Individual_Trans as a
                        inner join tenant.accom_type as b
                            on a.accom_type_id=b.accom_type_id
                        order by b.accom_type_code;
        quit;

        proc sort data=&work_lib..accom_type_temp;
        by accom_type_code;
        quit;
        proc sort data=&work_lib..transaction_accom_types;
        by accom_type_code;
        quit;

        data &work_lib..accom_types(keep=accom_type_code accom_type_id);
            merge &work_lib..transaction_accom_types (in=tranin) &work_lib..accom_type_temp (in=accomin);
            by accom_type_code;

            if tranin =1 then
                output &work_lib..accom_types;
        run;

        data &work_lib..occDates;
           do occDate=&minimum_occupancy_dt to &maximum_occupancy_dt;
                output;
            end;
        run;

        proc Sql;
            create table &work_lib..accom_capacity as
                select t1.Occupancy_DT format=date9., t1.accom_type_id length=6, t1.Accom_Capacity length=4 ,
                        COALESCE(t2.Rooms_Not_Avail_Maint, 0) as Rooms_Not_Avail_Maint length=4,
                        COALESCE(t2.Rooms_Not_Avail_Other, 0) as Rooms_Not_Avail_Other length=4
                from (select *
                	from (select occDate AS occupancy_dt from &work_lib..occDates)
                	cross join
                	(select accom_type_id, Accom_Type_Capacity as Accom_Capacity from tenant.Accom_Type)) as t1
                	left join rm_datap.accom_inventory t2 on t1.occupancy_dt=t2.occupancy_dt and t1.accom_type_id=t2.accom_type_id
                	order by occupancy_dt, accom_type_id;
        quit;

        proc Sql;
            create table &work_lib..total_capacity as
                select t1.Occupancy_DT format=date9.,
                    t1.Total_Accom_Capacity length=8 , COALESCE(t2.Rooms_Not_Avail_Maint, 0) as Rooms_Not_Avail_Maint length=8,
                   COALESCE(t2.Rooms_Not_Avail_Other, 0) as Rooms_Not_Avail_Other length=8
                from (select *
                    from (select occDate AS occupancy_dt from &work_lib..occDates)
                    cross join
                    (select sum(Accom_Type_Capacity) as Total_Accom_Capacity from tenant.Accom_Type)) as t1
                    left join rm_datap.total_inventory t2 on t1.occupancy_dt=t2.occupancy_dt
                    order by occupancy_dt;
        quit;
    %end;

    %do %while (%scan(&updatemslist., &mktsegi., |) ne);

        data _null_;
            call symputx ('mkt_seg',%scan(&updatemslist., &mktsegi., | ),'l');
        run;

        proc Sql;
            create table &work_lib..mkt_accom_final_temp as 
                select &mkt_seg. as mkt_seg_id format=5., accom_type_id , occupancy_dt, capture_dttm,
                    arrivals, departures, no_shows, cancellations, Room_revenue, rooms_sold, Food_Revenue, Total_Revenue 
                from rm_part.&market_segment_prefix._&mkt_seg. 
                    where ((occupancy_DT < &first_snapshot_dt. AND datepart(capture_dttm) between (occupancy_dt - &RequestedNumberPaceDays.) AND &first_snapshot_dt.)
                            OR (occupancy_DT >= &first_snapshot_dt. AND datepart(capture_dttm) <= &first_snapshot_dt.))
                        AND occupancy_dt between &minimum_occupancy_dt. and &maximum_occupancy_dt.
                    order by accom_type_id, occupancy_dt, capture_dttm;
        quit;

        %if &mkt_seg. in &groupmktseglist. and %SYSFUNC(EXIST(group.&market_segment_prefix._&mkt_seg.)) = 1 %THEN
            %DO;

                proc Sql;
                    create table &work_lib..compare_base_ms as 
                        select * 
                            from group.&market_segment_prefix._&mkt_seg. 
                                where datepart(capture_dttm)<=&first_snapshot_dt. and occupancy_dt between &minimum_occupancy_dt. and &maximum_occupancy_dt.
                                    order by accom_type_id, occupancy_dt, capture_dttm;
                quit;

            %end;
        %else
            %do;
                %if %upcase(&stopPopulatingPartitionsTable) eq FALSE %then
                    %do;
                        proc sql;
                            create table &work_lib..compare_base_ms as
                                select *
                                    from rm_partp.&market_segment_prefix._&mkt_seg.
                                        where datepart(capture_dttm)<=&first_snapshot_dt. and occupancy_dt between &minimum_occupancy_dt. and &maximum_occupancy_dt.
                                            order by accom_type_id, occupancy_dt, capture_dttm;
                        quit;
                    %end;
            %end;

        %if &mkt_seg. in &groupmktseglist. %then
            %put Processing For Group Market Segment &mkt_seg.;
        %else %put Processing For Transient Market Segment &mkt_seg.;


        %if %upcase(&stopPopulatingPartitionsTable) eq FALSE %then
            %do;
                proc compare base=&work_lib..compare_base_ms
                    compare=&work_lib..mkt_accom_final_temp
                    criterion = 0.01
                    noprint;
                    id accom_type_id occupancy_dt capture_dttm;
                    var accom_type_id occupancy_dt capture_dttm arrivals departures no_shows cancellations Room_revenue rooms_sold Food_Revenue Total_Revenue;
                run;
            %end;

        %let offset = 0;
        %if %upcase(&isRunningAsPartOfPMSRevamp.)=TRUE %then
            %let offset = 1;

        %let ms_data_changed = %eval(&sysinfo.>=64 or &pace_back_fill_count.=0 or %upcase(&stopPopulatingPartitionsTable) eq TRUE);

        %if &ms_data_changed.=1 or %upcase(&isRunningAsPartOfPMSRevamp.)=TRUE or %upcase(&RequestPaceBuildLastDay.) ne LASTDATE %then
            %do;
                %put Updating Market Segment &mkt_seg. into Pace Activity Database Table;
                %let market_segment_pace_updated=YES;

                data &work_lib..mkt_accom_pace_all_exp (keep=accom_type_id occupancy_dt capture_dttm arrivals
                    departures no_shows cancellations Room_revenue rooms_sold Food_Revenue Total_Revenue);
                    format idate date9.;
                    format itime time8.;
                    format save_capture_dttm datetime20.;
                    retain save_accom_type_id save_occupancy_dt save_capture_dttm 
                        save_arrivals save_departures save_no_shows save_cancellations save_Room_Revenue
                        save_rooms_sold save_Food_Revenue save_Total_Revenue
                        original_capture_dttm original_accom_type_id original_occupancy_dt original_arrivals original_departures original_no_shows original_cancellations original_Room_Revenue
                        original_rooms_sold original_Food_Revenue original_Total_Revenue;
                    set &work_lib..mkt_accom_final_temp end=last;
                    by accom_type_id occupancy_dt capture_dttm;

                    if _n_=1 or first.accom_type_id=1 or first.occupancy_dt=1 then
                        do;
                            save_accom_type_id=accom_type_id;
                            save_occupancy_dt=occupancy_dt;
                            save_capture_dttm=capture_dttm;
                            save_arrivals=arrivals;
                            save_departures=departures;
                            save_no_shows=no_shows;
                            save_cancellations=cancellations;
                            save_Room_Revenue=Room_revenue;
                            save_rooms_sold=rooms_sold;
                            save_Food_Revenue=Food_Revenue;
                            save_Total_Revenue=Total_Revenue;
                        end;

                    output &work_lib..mkt_accom_pace_all_exp;
                    original_accom_type_id=accom_type_id;
                    original_occupancy_dt=occupancy_dt;
                    original_capture_dttm=capture_dttm;
                    original_arrivals=arrivals;
                    original_departures=departures;
                    original_no_shows=no_shows;
                    original_cancellations=cancellations;
                    original_Room_Revenue=Room_revenue;
                    original_rooms_sold=rooms_sold;
                    original_Food_Revenue=Food_Revenue;
                    original_Total_Revenue=Total_Revenue;

                    if _n_> 1 and datepart(capture_dttm) ne datepart(save_capture_dttm)+1 AND (save_arrivals GT 0 OR save_departures GT 0 OR save_no_shows GT 0 OR save_cancellations GT 0 OR save_Room_Revenue NE 0.0 OR save_rooms_sold GT 0 OR save_Food_Revenue NE 0.0 OR save_Total_Revenue NE 0.0) then
                        do;
                            do idate=datepart(save_capture_dttm)+1 to datepart(capture_dttm)-1;
                                itime=timepart(save_capture_dttm);
                                capture_dttm=dhms(idate,0,0,itime);
                                business_day_end_dt=idate;
                                accom_type_id=save_accom_type_id;
                                occupancy_dt=save_occupancy_dt;
                                arrivals=save_arrivals;
                                departures=save_departures;
                                no_shows=save_no_shows;
                                cancellations=save_cancellations;
                                Room_Revenue=save_Room_revenue;
                                rooms_sold=save_rooms_sold;
                                Food_Revenue=save_Food_Revenue;
                                Total_Revenue=save_Total_Revenue;
                                output &work_lib..mkt_accom_pace_all_exp;
                            end;
                        end;

                    if _n_ > 1 and (last=1 or last.accom_type_id=1 or last.occupancy_dt=1) and datepart(original_capture_dttm) LT min(&First_snapshot_dt.-1,original_occupancy_dt+&offset.) AND (original_arrivals GT 0 OR original_departures GT 0 OR original_no_shows GT 0 OR original_cancellations GT 0 OR original_Room_Revenue NE 0.0 OR original_rooms_sold GT 0 OR original_Food_Revenue NE 0.0 OR original_Total_Revenue NE 0.0) then
                        do;
                            do idate=datepart(original_capture_dttm)+1 to  min(&First_snapshot_dt.-1,original_occupancy_dt+&offset.);
                                itime=timepart(original_capture_dttm);
                                capture_dttm=dhms(idate,0,0,itime);
                                business_day_end_dt=idate;
                                accom_type_id=Original_accom_type_id;
                                occupancy_dt=Original_occupancy_dt;
                                arrivals=Original_arrivals;
                                departures=Original_departures;
                                no_shows=Original_no_shows;
                                cancellations=Original_cancellations;
                                Room_Revenue=Original_Room_revenue;
                                rooms_sold=Original_rooms_sold;
                                Food_Revenue=Original_Food_Revenue;
                                Total_Revenue=Original_Total_Revenue;
                                output &work_lib..mkt_accom_pace_all_exp;
                            end;
                        end;

                    save_accom_type_id=original_accom_type_id;
                    save_occupancy_dt=original_occupancy_dt;
                    save_capture_dttm=original_capture_dttm;
                    save_arrivals=original_arrivals;
                    save_departures=original_departures;
                    save_no_shows=original_no_shows;
                    save_cancellations=original_cancellations;
                    save_Room_Revenue=original_Room_revenue;
                    save_rooms_sold=original_rooms_sold;
                    save_Food_Revenue=original_Food_Revenue;
                    save_Total_Revenue=original_Total_Revenue;
                run;

            %if %upcase(&UsePacmanData.)=FALSE %then %do;
                data &work_lib..mkt_accom_pace_all_exp(rename=(cap_date=capture_dttm) drop=capture_dttm);
                	set &work_lib..mkt_accom_pace_all_exp;
                	cap_date=dhms(datepart(capture_dttm),0,0,0);
                run;
            %end;

                proc Sql;
                    create table &work_lib..Mkt_Pace_temp as 
                        select distinct occupancy_dt, capture_dttm, 
                            sum(arrivals) as arrivals, 
                            sum(departures) as departures, 
                            sum(no_shows) as no_shows, 
                            sum(cancellations) as cancellations, 
                            sum(Room_Revenue) as Room_revenue,
                            sum(rooms_sold) as rooms_sold , 
                            sum(Food_Revenue) as Food_Revenue,
                            sum(Total_Revenue) as Total_Revenue 
                        from &work_lib..mkt_accom_pace_all_exp
                            group by occupancy_dt, capture_dttm
                                order by occupancy_dt, capture_dttm;
                quit;

                %if %upcase(&preserveHotelAccomPace.)=FALSE %then
                    %do;
                        proc Sql;
                            create table &work_lib..accom_pace_final_temp as
                                select distinct accom_type_id, occupancy_dt, capture_dttm,
                                    sum(arrivals) as arrivals,
                                    sum(departures) as departures,
                                    sum(no_shows) as no_shows,
                                    sum(cancellations) as cancellations,
                                    sum(Room_Revenue) as Room_revenue,
                                    sum(rooms_sold) as rooms_sold ,
                                    sum(Food_Revenue) as Food_Revenue,
                                    sum(Total_Revenue) as Total_Revenue
                                from &work_lib..mkt_accom_pace_all_exp
                                    group by accom_type_id, occupancy_dt, capture_dttm
                                        order by accom_type_id, occupancy_dt, capture_dttm;
                        quit;

                        proc append base=&work_lib..all_accom_pace_final data=&work_lib..accom_pace_final_temp force;
                        run;
                    %end;

                proc Sql;
                    create table &work_lib..mkt_inventory_db as
                        select &Property_ID. as property_id, a.occupancy_dt, a.capture_dttm as Snapshot_DTTM ,
                            datepart(a.capture_dttm)-1 as Business_day_end_dt, 
                            &mkt_seg. as mkt_seg_id length=8, 
                            a.rooms_sold, 
                            a.arrivals, 
                            a.departures, 
                            a.no_shows, 
                            a.cancellations, 
                            a.Room_revenue,
                            a.Food_Revenue,
                            a.Total_Revenue, 
                            b.file_metadata_id, 
                            month(a.occupancy_dt) as Month_ID, 
                            year(a.occupancy_dt) - 2000 as Year_ID, 
                            datetime() as Last_Updated_Dttm
                        from &work_lib..Mkt_Pace_temp as a 
                            inner join &work_lib..file_metadata as b 
                                on datepart(a.capture_dttm)=b.snapshot_dt
                            where datepart(a.capture_dttm) <= a.occupancy_dt + &offset. and a.occupancy_dt <=&maximum_occupancy_dt.
                                order by mkt_seg_id , a.occupancy_dt, a.capture_dttm;
                quit;
                
                %if &ms_data_changed.=1 or %upcase(&RequestPaceBuildLastDay.) ne LASTDATE %then
                    %do;
                        %ideas_trans_upload_tmp(upload_table=&work_lib..mkt_inventory_db,
                            _name_in_db=pace_mkt_activity,
                            like_table=tenant.pace_mkt_activity(drop=PACE_Mkt_Activity_ID),
                            tmp_trans_lib=ten_tmp,cnt=2&load_id.2);
                        %let pace_mkt_activity = ##&pace_mkt_activity;
        
                        %if &syscc > 4 %then
                            %do;
                                %ideas_util_report_errors(920, 'Failed to insert temp tables into RDBMS', '', &request_Id.)
                                %goto EXIT;
                            %end;

                        proc Sql;
                            connect to odbc (&connect_str autocommit=yes);
                            execute ( delete from pace_mkt_activity where &mkt_seg.=mkt_seg_id and DATEADD(dd, 0, DATEDIFF(dd, 0, snapshot_dttm)) < &first_snapshot_dt_sql. and &RequestPaceBuildLastDay_SQL. = 'LASTDATE'
                                ) by odbc;
        
                            %if NOT (&sysdbrc eq 0 or "&sysdbrc" eq "") %then
                                %do;
                                    %let error_codes = 921;
                                    %goto EXIT;
                                %end;
        
                            execute ( insert into pace_mkt_activity (property_id, occupancy_dt, snapshot_dttm, business_day_end_dt, Mkt_Seg_ID, rooms_sold, arrivals, departures,
                                cancellations, no_shows, room_revenue, food_revenue, total_revenue, file_metadata_id, month_id, year_id) 
                            select property_id, occupancy_dt, snapshot_dttm, business_day_end_dt, Mkt_Seg_ID, rooms_sold, arrivals, departures,
                                cancellations, no_shows, room_revenue, food_revenue, total_revenue, file_metadata_id, month_id, year_id from &pace_mkt_activity. as tmp_maa
                                where not exists (select 1 from pace_mkt_activity maa where maa.property_id=tmp_maa.property_id and maa.Occupancy_DT=tmp_maa.occupancy_dt
                                 and maa.business_day_end_dt = tmp_maa.business_day_end_dt and maa.Mkt_Seg_ID=tmp_maa.Mkt_Seg_ID)
                                &skipZeroFillingWhere.
                                ) by odbc;
        
                            %if NOT (&sysdbrc eq 0 or "&sysdbrc" eq "") %then
                                %do;
                                    %let error_codes = 921;
                                    %goto EXIT;
                                %end;
        
                            disconnect from odbc;
                        quit;
                    %end;

                %if %upcase(&isRunningAsPartOfPMSRevamp.)=TRUE %then
                    %do;
                        %create_mkt_accom_nonpace(mkt_seg=&mkt_seg.);
                    %end;

            %end;

        %if &mkt_seg. in &groupmktseglist. %then
            %do;

                proc sql;
                    create table group.&market_segment_prefix._&mkt_seg. as 
                        select * from rm_part.&market_segment_prefix._&mkt_seg.;
                quit;

            %end;

        %let mktsegi = %sysevalf(&mktsegi. + 1);
    %end;

    %let mktexp_end_time=%sysfunc(datetime());
    %let mktsegruntime=%sysfunc(round(&mktexp_end_time-&mktexp_start_time, .05));
    %let accom_type_start_time=%sysfunc(datetime());

    %if %upcase(&market_segment_pace_updated.)=YES and %upcase(&preserveHotelAccomPace.)=FALSE %then
        %do;
            *options mprint notes;
            proc Sql noprint;
                select distinct accom_type_id into: accomsortlist SEPARATED by '|' from &work_lib..accom_types 
                    order by accom_type_id;
            quit;

            %let accomi= 1;

            %do %while (%scan(&accomsortlist., &accomi., |) ne);

                data _null_;
                    call symputx ('accom_id',%scan(&accomsortlist., &accomi., |),'l');
                run;

                %put Updating Accom Type &accom_id into Pace Activity Database Table;

                proc Sql;
                    create table &work_lib..Accom_Pace_temp as
                        select distinct &accom_id. as accom_type_id, occupancy_dt, capture_dttm,
                            sum(arrivals) as arrivals,
                            sum(departures) as departures,
                            sum(no_shows) as no_shows,
                            sum(cancellations) as cancellations,
                            sum(Room_Revenue) as Room_revenue,
                            sum(rooms_sold) as rooms_sold, 
                            sum(Food_Revenue) as Food_Revenue,
                            sum(Total_Revenue) as Total_Revenue
                        from &work_lib..all_accom_pace_final
                            where accom_type_id=&accom_id. and occupancy_dt between &minimum_occupancy_dt. and &maximum_occupancy_dt.
                            group by occupancy_dt, capture_dttm
                                order by occupancy_dt, capture_dttm;
                quit;

                proc append base=&work_lib..total_pace_final_temp data=&work_lib..Accom_Pace_temp(drop= accom_type_id) force;
                run;

                proc Sql;
                    create table &work_lib..accom_inventory_db as 
                        select &Property_ID. as property_id, a.occupancy_dt, a.capture_dttm as Snapshot_DTTM format=datetime22., 
                            datepart(a.capture_dttm)-1 as Business_day_end_dt, 
                            a.accom_type_id as accom_type_id length=8, 
                            b.accom_capacity as accom_capacity length=8,
                            a.rooms_sold as rooms_sold length=8, 
                            b.rooms_not_avail_maint as rooms_not_avail_maint length=8, 
                            b.rooms_not_avail_other as rooms_not_avail_other length=8, 
                            a.arrivals, 
                            a.departures, 
                            a.no_shows, 
                            a.cancellations, 
                            a.Room_revenue,
                            a.Food_Revenue,
                            a.Total_Revenue, 
                            c.file_metadata_id, 
                            month(a.occupancy_dt) as Month_ID, 
                            year(a.occupancy_dt) - 2000 as Year_ID, 
                            datetime() as Last_Updated_Dttm
                        from &work_lib..Accom_Pace_temp as a 
                            inner join &work_lib..accom_capacity as b
                                on a.occupancy_dt=b.occupancy_dt and a.accom_type_id=b.accom_type_id
                            inner join &work_lib..file_metadata as c 
                                on datepart(a.capture_dttm)=c.snapshot_dt
                            where &accom_id=a.accom_type_id and datepart(a.capture_dttm) <= a.occupancy_dt + &offset. and a.occupancy_dt <=&maximum_occupancy_dt.
                                order by a.accom_type_id, a.occupancy_dt, a.capture_dttm;
                quit;

                %ideas_trans_upload_tmp(upload_table=&work_lib..accom_inventory_db,
                    _name_in_db=pace_acc_activity,
                    like_table=tenant.pace_accom_activity(drop=PACE_Accom_Activity_ID),
                    tmp_trans_lib=ten_tmp,cnt=3&load_id.);
                %let pace_acc_activity = ##&pace_acc_activity;

                %if &syscc > 4 %then
                    %do;
                        %ideas_util_report_errors(920, 'Failed to insert temp tables into RDBMS', '', &request_Id.)
                        %goto EXIT;
                    %end;

                proc Sql;
                    connect to odbc (&connect_str autocommit=yes bulkload=yes);
                    execute ( delete from PACE_Accom_Activity where &accom_id. = accom_type_id and DATEADD(dd, 0, DATEDIFF(dd, 0, snapshot_dttm)) < &first_snapshot_dt_sql. and &RequestPaceBuildLastDay_SQL. = 'LASTDATE'
                        ) by odbc;

                    %if NOT (&sysdbrc eq 0 or "&sysdbrc" eq "") %then
                        %do;
                            %let error_codes = 921;
                            %goto EXIT;
                        %end;

                    execute (insert into PACE_Accom_Activity (property_id, occupancy_dt, snapshot_dttm, business_day_end_dt, Accom_Type_ID, accom_capacity, rooms_sold, rooms_not_avail_maint,
                        Rooms_Not_Avail_Other, arrivals, departures, cancellations, no_shows, room_revenue, food_revenue, total_revenue, file_metadata_id, month_id, year_id) 
                    select property_id, occupancy_dt, snapshot_dttm, business_day_end_dt, Accom_Type_ID, accom_capacity, rooms_sold, rooms_not_avail_maint,
                        Rooms_Not_Avail_Other, arrivals, departures, cancellations, no_shows, room_revenue, food_revenue, total_revenue, file_metadata_id, month_id, year_id from &pace_acc_activity. as tmp_aa
                        where not exists (select 1 from PACE_Accom_Activity aa where aa.property_id=tmp_aa.property_id and aa.Occupancy_DT=tmp_aa.occupancy_dt
                         and aa.business_day_end_dt = tmp_aa.business_day_end_dt and aa.Accom_Type_ID=tmp_aa.Accom_Type_ID)
                        &skipZeroFillingWhere.
                        ) by odbc;

                    %if NOT (&sysdbrc eq 0 or "&sysdbrc" eq "") %then
                        %do;
                            %let error_codes = 921;
                            %goto EXIT;
                        %end;

                    disconnect from odbc;
                quit;

                %if %upcase(&isRunningAsPartOfPMSRevamp.)=TRUE %then
                    %do;
                        %create_accom_nonpace();
                    %end;
                %let accomi = %sysevalf(&accomi. + 1);
            %end;

            %put Updating Total Pace into Pace Activity Database Table;

            proc Sql;
                create table &work_lib..Total_Pace_temp as 
                    select distinct occupancy_dt, capture_dttm, 
                        sum(arrivals) as arrivals, 
                        sum(departures) as departures, 
                        sum(no_shows) as no_shows, 
                        sum(cancellations) as cancellations, 
                        sum(Room_Revenue) as Room_revenue,
                        sum(rooms_sold) as rooms_sold , 
                        sum(Food_Revenue) as Food_Revenue,
                        sum(Total_Revenue) as Total_Revenue
                    from &work_lib..Total_pace_final_temp
                        where occupancy_dt between &minimum_occupancy_dt. and &maximum_occupancy_dt.
                        group by occupancy_dt, capture_dttm
                            order by occupancy_dt, capture_dttm;
            quit;

            proc Sql;
                create table &work_lib..Total_inventory_db as 
                    select &Property_ID. as property_id, a.occupancy_dt, a.capture_dttm as Snapshot_DTTM format=datetime22., 
                        datepart(a.capture_dttm)-1 as Business_day_end_dt, 
                        b.total_accom_capacity,
                        b.rooms_not_avail_maint, 
                        b.rooms_not_avail_other,
                        a.rooms_sold , 
                        a.arrivals, 
                        a.departures, 
                        a.no_shows, 
                        a.cancellations, 
                        a.Room_revenue,
                        a.Food_Revenue,
                        a.Total_Revenue, 
                        c.file_metadata_id, 
                        month(a.occupancy_dt) as Month_Id, 
                        year(a.occupancy_dt) - 2000 as Year_ID, 
                        datetime() as Last_Updated_Dttm
                    from &work_lib..Total_pace_temp as a 
                        inner join &work_lib..total_capacity as b
                            on a.occupancy_dt=b.occupancy_dt  
                        inner join &work_lib..file_metadata as c 
                            on datepart(a.capture_dttm)=c.snapshot_dt
                        where datepart(a.capture_dttm) <= a.occupancy_dt + &offset. and a.occupancy_dt <=&maximum_occupancy_dt.
                            order by a.occupancy_dt, a.capture_dttm;
            quit;

            %ideas_trans_upload_tmp(upload_table=&work_lib..total_inventory_db,
                _name_in_db=pace_total_activity,
                like_table=tenant.pace_total_activity(drop=PACE_Total_Activity_ID),
                tmp_trans_lib=ten_tmp,cnt=4&load_id.);
            %let pace_total_activity = ##&pace_total_activity;

            %if &syscc > 4 %then
                %do;
                    %ideas_util_report_errors(920, 'Failed to insert temp tables into RDBMS', '', &request_Id.)
                    %goto EXIT;
                %end;

            proc Sql;
                connect to odbc (&connect_str autocommit=yes bulkload=yes);
                execute ( delete from pace_total_activity where DATEADD(dd, 0, DATEDIFF(dd, 0, snapshot_dttm)) < &first_snapshot_dt_sql. and &RequestPaceBuildLastDay_SQL. = 'LASTDATE'
                    ) by odbc;

                %if NOT (&sysdbrc eq 0 or "&sysdbrc" eq "") %then
                    %do;
                        %let error_codes = 921;
                        %goto EXIT;
                    %end;

                execute ( insert into pace_total_activity (property_id, occupancy_dt, snapshot_dttm, business_day_end_dt, total_accom_capacity, rooms_sold, rooms_not_avail_maint,
                    Rooms_Not_Avail_Other, arrivals, departures, cancellations, no_shows, room_revenue, food_revenue, total_revenue, file_metadata_id, month_id, year_id) 
                select property_id, occupancy_dt, snapshot_dttm, business_day_end_dt, total_accom_capacity, rooms_sold, rooms_not_avail_maint,
                    Rooms_Not_Avail_Other, arrivals, departures, cancellations, no_shows, room_revenue, food_revenue, total_revenue, file_metadata_id, month_id, year_id from &pace_total_activity. as tmp_ta
                    where not exists (select 1 from pace_total_activity ta where ta.property_id=tmp_ta.property_id and ta.Occupancy_DT=tmp_ta.occupancy_dt
                     and ta.business_day_end_dt = tmp_ta.business_day_end_dt)
                    &skipZeroFillingWhere.
                    ) by odbc;

                %if NOT (&sysdbrc eq 0 or "&sysdbrc" eq "") %then
                    %do;
                        %let error_codes = 921;
                        %goto EXIT;
                    %end;

                disconnect from odbc;
            quit;

            %if %upcase(&isRunningAsPartOfPMSRevamp.)=TRUE %then
                %do;
                    %create_total_nonpace();
                    %create_bde_inventories();
                    %upload_nonpace_2_tenant();
                %end;
        %end;

    %let accom_type_end_time=%sysfunc(datetime());
    %let accomtyperunTime=%sysfunc(round(&accom_type_end_time-&accom_type_start_time, .05));
    options nomprint nonotes;

%EXIT:

    %if &error_codes =921 %then
        %do;
            %ideas_util_inc_errors;
            %ideas_util_report_errors(919, 'RDBMS update failed', '', &request_Id., &err_ct.)
            %let etl_status = 1;
            %let GL_ABORT_FLAG=1;
        %end;

    %if &GL_ABORT_FLAG eq 1 or &syscc > 4 %then
        %do;
            %let etl_status = 1;
            %let return_code=200;
        %end;
    %else %let return_code=0;
%mend ideas_pace_update_db;
