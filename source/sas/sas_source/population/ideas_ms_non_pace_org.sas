%macro ideas_ms_non_pace_org/store;

%let macroname=&SYSMACRONAME; 
	
%ideas_macro_log (start_end=START, macroname=&macroname.);

	proc sql;
		select distinct mkt_seg_id into: list SEPARATED by '|' from tenant.mkt_seg 
		 where mkt_seg_code in (select distinct mkt_seg_code from &work_lib..inbound_mktseg_cd)
			AND Property_ID EQ &property_id.;
	quit;

	%let ms_list = &list.;
	%let i = 1;
    
	%do %while (%scan(&ms_list., &i., |) ne);

		data _null_;
			call symputx ('ms',%scan(&ms_list, &i, |),'l');
		run;
        
		%let dsn = Ma_org_&ms.;
		%let los_dsn = ma_los_org_&ms.;
		%let libdsn = rm_part.ma_org_&ms.;
		%let liblosdsn = rm_part.ma_los_org_&ms.;

		/*Check if mktseg org table exist if not create an empty one*/
			%if %sysfunc(exist(&libdsn)) eq 0 %then %do;
				proc sql;
					create table rm_part.&dsn. like rm_part.ma_inv_pace_template;
				quit;
			%end;

		/*Check if mktseg los org table exist if not create an empty one*/
			%if %sysfunc(exist(&liblosdsn)) eq 0 %then %do;
				proc sql;
					create table rm_part.&los_dsn. like rm_part.ma_los_inv_pace_template;
				quit;
			%end;

		%let i = %sysevalf(&i. + 1);
	%end;
%let macroname=&SYSMACRONAME; 
	
%ideas_macro_log (start_end=END, macroname=&macroname.);
%mend ideas_ms_non_pace_org;
