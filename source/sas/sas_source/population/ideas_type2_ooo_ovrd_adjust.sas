%macro ideas_type2_ooo_ovrd_adjust/store;
    %let macroname=&SYSMACRONAME;
    proc sql;
        create table &work_lib..ooo_overrides as
            select * from tenant.Out_Of_Order_Override;

        %if %sysfunc(exist(&work_lib..ooo_overrides)) %then %do;
            UPDATE &work_lib..accom2 aa
            SET Rooms_Not_Avail_Maint = ( select ooo.Override_Value from &work_lib..ooo_overrides ooo where ooo.Override_Date = aa.occupancy_DT
                    and ooo.accom_type_id = aa.accom_type_id),
                Rooms_Not_Avail_Other = 0
            Where exists
                ( select 1 from &work_lib..ooo_overrides ooo where ooo.Override_Date = aa.occupancy_DT and ooo.accom_type_id = aa.accom_type_id);

            create table &work_lib..updated_totals as
                SELECT Occupancy_DT,
                  SUM(Rooms_Not_Avail_Maint) AS Rooms_Not_Avail_Maint,
                  SUM(Rooms_Not_Avail_Other) AS Rooms_Not_Avail_Other
                FROM &work_lib..accom2
                where occupancy_DT IN
                      (SELECT DISTINCT ooo.Override_Date FROM &work_lib..ooo_overrides ooo)
                group by occupancy_DT;
        %end;

        %if %sysfunc(exist(&work_lib..updated_totals)) %then %do;
            update &work_lib..total ta
                set Rooms_Not_Avail_Maint = ( select rooms_not_avail_maint from &work_lib..updated_totals ut where ut.occupancy_DT = ta.occupancy_DT),
                    rooms_not_avail_other = ( select rooms_not_avail_other from &work_lib..updated_totals ut where ut.occupancy_DT = ta.occupancy_DT)
                where exists (select 1 from &work_lib..updated_totals ut where ut.occupancy_DT = ta.occupancy_DT);
        %end;

    quit;
%mend ideas_type2_ooo_ovrd_adjust;
