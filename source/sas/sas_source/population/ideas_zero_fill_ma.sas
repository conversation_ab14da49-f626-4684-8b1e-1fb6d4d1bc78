%macro ideas_zero_fill_ma(indsn)/store;
%let macroname=&SYSMACRONAME;

%ideas_macro_log (start_end=START, macroname=&macroname.);

	data &indsn. (drop=occupancyDt accomID);
		set &work_lib..zero_fill_occdate_accom;
		OCCUPANCY_DT = occupancyDt;
		CAPTURE_DTTM = DATETIME();
		Accom_Type_ID = accomID;
		ARRIVALS=0;
		DEPARTURES=0;
		NO_SHOWS=0;
		CANCELLATIONS=0;
		ROOM_REVENUE=0;
		ROOMS_SOLD=0;
		TOTAL_REVENUE=0;
		food_revenue=0;
		output;
	run;

%ideas_macro_log (start_end=END, macroname=&macroname.);
%mend ideas_zero_fill_ma;
