%macro ideas_pace_checker_extract (rc=)/store mindelimiter=',';

    options minoperator;

    proc Sql;
        create table &work_lib..snapshot_date as
            select distinct snapshot_dt as snapshot_dt format=date9. 
                from tenant.file_metadata
                    where property_id = &property_id and record_type_id = &SummaryRecType. and 
                        (select min(snapshot_dt) from tenant.file_metadata 
                            where property_id = &property_id and File_Name ne "PaceHistoryBuild" and record_type_id = &SummaryRecType.) = snapshot_dt;
    quit;

    data _null_;
        set &work_lib..snapshot_date;
        call symputx('First_Snapshot_dt',Snapshot_Dt);
    run;

    proc Sql;
        create table &work_lib..file_metadata as 
            select a.property_id, a.file_metadata_ID, a.snapshot_dt, a.snapshot_tm
                from tenant.file_metadata as a 
                    where a.record_type_id=3 
                        order by a.snapshot_dt;
    quit;

    proc Sql;
        create table &work_lib..history_snapshot_dates as
            select snapshot_dt as snapshot_dt format=date9. 
                from tenant.file_metadata
                    where property_id = &property_id and record_type_id = &SummaryRecType. and 
                        File_Name eq "PaceHistoryBuild";
    quit;

    proc Sql noprint;
        select min(occupancy_dt) format=date9. into :min_occupancy_dt 
            from rm_data.Mkt_accom_inventory;
    quit;

    proc Sql noprint;
        select max(occupancy_dt) format=date9. into :max_occupancy_dt 
            from tenant.PACE_Mkt_Activity;
    quit;

    proc Sql noprint;
        select min(snapshot_dt), max(snapshot_dt) into: min_capture_dt ,:max_capture_dt
            from &work_lib..file_metadata;
    quit;

    %let minimum_occupancy_dt=%sysfunc(inputn(&min_occupancy_dt.,date9.));
    %let maximum_occupancy_dt=%sysfunc(inputn(&max_occupancy_dt.,date9.));

    proc Sql noprint;
        select distinct accom_type_id into: accomtypelist SEPARATED by '|' from rm_data.accom_inventory_pace 
            order by accom_type_id;
        select distinct mkt_seg_id into: mssortlist SEPARATED by '|' from rm_data.mkt_accom_inventory 
            order by mkt_seg_id;
    quit;

    %put &accomtypelist;
    %put &mssortlist;
    %let mktsegi = 1;

    %do %while (%scan(&mssortlist., &mktsegi., |) ne);

        data _null_;
            call symputx ('mkt_seg',%scan(&mssortlist., &mktsegi., |),'l');
        run;

        %if &mkt_seg. in &groupmktsegcommalist. and %SYSFUNC(EXIST(group.ma_&mkt_seg.)) = 1 %THEN
            %do;
                %put Processing Group Market Segment: &mkt_seg.;

                proc Sql;
                    create table &work_lib..mkt_accom_sas_temp as 
                        select &Property_ID. as property_id length=4, &mkt_seg. as mkt_seg_id length=6, accom_type_id , occupancy_dt format=date9., capture_dttm format=datetime20. , 
                            rooms_sold as sas_rooms_sold length=6 format=7., arrivals as sas_arrivals length=6 format=7., departures as sas_departures length=6 format=7., no_shows as sas_no_shows length=6 format=7., 
                            cancellations as sas_cancellations length=6 format=7., round(Room_Revenue,.01) as sas_Room_Revenue format=11.2, round(Food_Revenue,.01) as sas_Food_Revenue format=11.2, round(Total_Revenue,.01) as sas_Total_Revenue format=11.2
                        from group.ma_&mkt_seg. 
                            where occupancy_dt <=&maximum_occupancy_dt.;
                quit;

            %end;
        %else
            %do;
                %put Processing Transient Market Segment: &mkt_seg.;

                proc Sql;
                    create table &work_lib..mkt_accom_sas_temp as 
                        select &Property_ID. as property_id length=4, &mkt_seg. as mkt_seg_id length=6, accom_type_id , occupancy_dt format=date9., capture_dttm format=datetime20. , 
                            rooms_sold as sas_rooms_sold length=6 format=7., arrivals as sas_arrivals length=6 format=7., departures as sas_departures length=6 format=7., no_shows as sas_no_shows length=6 format=7., 
                            cancellations as sas_cancellations length=6 format=7., round(Room_Revenue,.01) as sas_Room_Revenue format=11.2, round(Food_Revenue,.01) as sas_Food_Revenue format=11.2, round(Total_Revenue,.01) as sas_Total_Revenue format=11.2
                        from rm_part.ma_&mkt_seg. 
                            where occupancy_dt <=&maximum_occupancy_dt.;
                quit;

            %end;

        proc Append base=&work_lib..mkt_inventory_pace1 data=&work_lib..mkt_accom_sas_temp force;
        run;

        %let mktsegi = %sysevalf(&mktsegi. + 1);
    %end;

    proc Sort data=&work_lib..mkt_inventory_pace1;
        by mkt_seg_id accom_type_id occupancy_dt capture_dttm;
    run;

    data &work_lib..pace_all_exp (keep=property_id mkt_seg_id accom_type_id occupancy_dt capture_dttm sas_arrivals
        sas_departures sas_no_shows sas_cancellations sas_Room_revenue sas_rooms_sold sas_Food_Revenue sas_Total_Revenue);
        format idate date9.;
        format itime time8.;
        format save_capture_dttm datetime20.;
        retain save_property_id save_mkt_seg_id save_accom_type_id save_occupancy_dt save_capture_dttm 
            save_arrivals save_departures save_no_shows save_cancellations save_Room_Revenue
            save_rooms_sold save_Food_Revenue save_Total_Revenue
            original_property_id original_capture_dttm original_mkt_seg_id original_accom_type_id original_occupancy_dt original_arrivals original_departures original_no_shows original_cancellations original_Room_Revenue
            original_rooms_sold original_Food_Revenue original_Total_Revenue;
        set &work_lib..mkt_inventory_pace1;
        by mkt_seg_id accom_type_id occupancy_dt capture_dttm;

        if _n_=1 or first.mkt_seg_id=1 or first.accom_type_id=1 or first.occupancy_dt=1 then
            do;
                save_property_id=property_id;
                save_mkt_seg_id=mkt_seg_id;
                save_accom_type_id=accom_type_id;
                save_occupancy_dt=occupancy_dt;
                save_capture_dttm=capture_dttm;
                save_arrivals=sas_arrivals;
                save_departures=sas_departures;
                save_no_shows=sas_no_shows;
                save_cancellations=sas_cancellations;
                save_Room_Revenue=sas_Room_revenue;
                save_rooms_sold=sas_rooms_sold;
                save_Food_Revenue=sas_Food_Revenue;
                save_Total_Revenue=sas_Total_Revenue;
            end;

        output &work_lib..pace_all_exp;
        original_property_id=property_id;
        original_accom_type_id=accom_type_id;
        original_mkt_seg_id=mkt_seg_id;
        original_occupancy_dt=occupancy_dt;
        original_capture_dttm=capture_dttm;
        original_arrivals=sas_arrivals;
        original_departures=sas_departures;
        original_no_shows=sas_no_shows;
        original_cancellations=sas_cancellations;
        original_Room_Revenue=sas_Room_revenue;
        original_rooms_sold=sas_rooms_sold;
        original_Food_Revenue=sas_Food_Revenue;
        original_Total_Revenue=sas_Total_Revenue;

        if first.capture_dttm=1 and datepart(capture_dttm) ne datepart(save_capture_dttm)+1 then
            do;
                do idate=datepart(save_capture_dttm)+1 to min(datepart(capture_dttm)-1, &First_snapshot_dt.-1,occupancy_dt);
                    itime=timepart(save_capture_dttm);
                    capture_dttm=dhms(idate,0,0,itime);
                    property_id=save_property_id;
                    mkt_seg_id=save_mkt_seg_id;
                    accom_type_id=save_accom_type_id;
                    occupancy_dt=save_occupancy_dt;
                    sas_arrivals=save_arrivals;
                    sas_departures=save_departures;
                    sas_no_shows=save_no_shows;
                    sas_cancellations=save_cancellations;
                    sas_Room_Revenue=save_Room_revenue;
                    sas_rooms_sold=save_rooms_sold;
                    sas_Food_Revenue=save_Food_Revenue;
                    sas_Total_Revenue=save_Total_Revenue;
                    output &work_lib..pace_all_exp;
                end;
            end;

        save_property_id=original_property_id;
        save_mkt_seg_id=original_mkt_seg_id;
        save_accom_type_id=original_accom_type_id;
        save_occupancy_dt=original_occupancy_dt;
        save_capture_dttm=original_capture_dttm;
        save_arrivals=original_arrivals;
        save_departures=original_departures;
        save_no_shows=original_no_shows;
        save_cancellations=original_cancellations;
        save_Room_Revenue=original_Room_revenue;
        save_rooms_sold=original_rooms_sold;
        save_Food_Revenue=original_Food_Revenue;
        save_Total_Revenue=original_Total_Revenue;
    run;

    proc Sql;
        create table &work_lib..mkt_inventory_pace_temp as 
            select distinct property_id, occupancy_dt, capture_dttm, mkt_seg_id, 
                sum(sas_arrivals) as sas_arrivals length=6 format=7.,
                sum(sas_departures) as sas_departures length=6 format=7., 
                sum(sas_no_shows) as sas_no_shows length=6 format=7., 
                sum (sas_cancellations) as sas_cancellations length=6 format=7., 
                round(sum(sas_Room_Revenue),.01) as sas_Room_revenue length=8 format=11.2, 
                sum(sas_rooms_sold) as sas_rooms_sold length=6 format=7., 
                round(sum(sas_Food_Revenue),.01) as sas_Food_revenue length=8 format=11.2, 
                round(sum(sas_Total_Revenue),.01) as sas_Total_revenue length=8 format=11.2 
            from &work_lib..pace_all_exp
                group by property_id, mkt_seg_id, occupancy_dt, capture_dttm 
                    order by property_id, mkt_seg_id, occupancy_dt, capture_dttm;
    quit;

    data &work_lib..mkt_inventory_pace_last (keep=property_id Occupancy_dt capture_dttm mkt_seg_id sas_rooms_sold 
        sas_Arrivals sas_departures sas_Cancellations sas_no_shows sas_Room_Revenue sas_Food_Revenue sas_Total_Revenue );
        retain property_id Occupancy_dt capture_dttm mkt_seg_id sas_rooms_sold 
            sas_Arrivals sas_departures sas_Cancellations sas_no_shows sas_Room_Revenue sas_Food_Revenue sas_Total_Revenue;
        retain previous_rooms_sold 0;
        retain previous_room_revenue 0;
        retain previous_food_revenue 0;
        retain previous_total_revenue 0;
        retain previous_cancellations 0;
        retain previous_no_shows 0;
        retain previous_arrivals 0;
        retain previous_departures 0;
        set &work_lib..mkt_inventory_pace_temp end=last;
        by property_id mkt_seg_id occupancy_dt capture_dttm;

        if _n_=1 or first.property_id=1 or first.mkt_seg_id=1 or first.occupancy_dt=1 then
            do;
                previous_rooms_sold=-10;
                previous_room_revenue=-10;
                previous_food_revenue=-10;
                previous_total_revenue=-10;
                previous_cancellations=-10;
                previous_no_shows=-10;
                previous_arrivals=-10;
                previous_departures=-10;
            end;

        if (previous_arrivals ne sas_arrivals or 
            previous_rooms_sold ne sas_rooms_sold or 
            previous_room_revenue ne sas_room_revenue or 
            previous_food_revenue ne sas_food_revenue or 
            previous_total_revenue ne sas_total_revenue or 
            previous_no_shows ne sas_no_shows or 
            previous_cancellations ne sas_cancellations or 
            previous_departures ne sas_departures or last.mkt_seg_id=1 or last.occupancy_dt=1) then
            output &work_lib..mkt_inventory_pace_last;
        previous_rooms_sold=sas_rooms_sold;
        previous_room_revenue=sas_room_revenue;
        previous_food_revenue=sas_food_revenue;
        previous_total_revenue=sas_total_revenue;
        previous_no_shows=sas_no_shows;
        previous_cancellations=sas_cancellations;
        previous_arrivals=sas_arrivals;
        previous_departures=sas_departures;
    run;

    proc Sql;
        create table &work_lib..mkt_inventory_pace as 
            select distinct property_id, occupancy_dt, capture_dttm, mkt_seg_id, 
                sas_arrivals,sas_departures,sas_no_shows, sas_cancellations,sas_Room_revenue, sas_rooms_sold,sas_Food_revenue, sas_Total_revenue
            from &work_lib..mkt_inventory_pace_last
                order by property_id, mkt_seg_id, occupancy_dt, capture_dttm;
    quit;

    options nolabel;

    proc Sql;
        create table &work_lib..accom_capacity1 as 
            select property_id, Occupancy_DT format=date9., datepart(SnapShot_DTTM) as snapshot_dt format=date9., 
                accom_type_id, Accom_Capacity length=6 format=7. ,Rooms_Not_Avail_Maint length=6 format=7.,Rooms_Not_Avail_Other length=6 format=7.
            from tenant.Accom_Activity 
                order by property_id, accom_type_id , occupancy_dt;
    quit;

    Data &work_lib..Accom_capacity2 (keep= property_id accom_type_id occupancy_dt capture_dttm accom_capacity rooms_not_avail_maint rooms_not_avail_other );
        attrib Accom_capacity rooms_not_avail_maint rooms_not_avail_other format=7. length=6;
        attrib occupancy_dt format=date9.;
        format capture_dttm datetime20.;
        set &work_lib..accom_capacity1;

        do idate=&min_capture_dt. to &max_capture_dt.;
            itime='03:00:00't;
            capture_dttm=dhms(idate,0,0,itime);
            output &work_lib..Accom_capacity2;
        end;
    run;

    proc Sql;
        create table &work_lib..Accom_capacity3 as 
            select property_id, occupancy_dt format=date9., capture_dttm format=datetime20., accom_type_id, 
                accom_capacity length=6 format=7., rooms_not_avail_maint length=6 format=7., rooms_not_avail_other length=6 format=7.
            from rm_data.accom_inventory_pace
                where datepart(capture_dttm) not in(select snapshot_dt from &work_lib..history_snapshot_dates)
                    order by property_id, accom_type_id, occupancy_dt, capture_dttm;
    quit;

    Data &work_lib..Accom_capacity4;
        set &work_lib..Accom_capacity2 &work_lib..Accom_capacity3;
    run;

    proc Sql;
        create table &work_lib..Accom_capacity as 
            select distinct property_id , occupancy_dt, accom_type_id , capture_dttm, 
                sum(accom_capacity) as accom_capacity length=6 format=7.,
                sum(rooms_not_avail_maint) as rooms_not_avail_maint length=6 format=7., 
                sum(rooms_not_avail_other) as rooms_not_avail_other length=6 format=7.
            from &work_lib..Accom_capacity4 
                group by property_id, accom_type_id , occupancy_dt, capture_dttm
                    order by property_id, accom_type_id , occupancy_dt, capture_dttm;
    quit;

    proc Sql;
        create table &work_lib..accom_inventory_pace_temp as 
            select distinct property_id, occupancy_dt, capture_dttm, accom_type_id, 
                sum(sas_arrivals) as sas_arrivals length=6 format=7.,
                sum(sas_departures) as sas_departures length=6 format=7., 
                sum(sas_no_shows) as sas_no_shows length=6 format=7., 
                sum (sas_cancellations) as sas_cancellations length=6 format=7., 
                round(sum(sas_Room_Revenue),.01) as sas_Room_revenue length=8 format=11.2, 
                sum(sas_rooms_sold) as sas_rooms_sold length=6 format=7., 
                round(sum(sas_Food_Revenue),.01) as sas_Food_revenue length=8 format=11.2, 
                round(sum(sas_Total_Revenue),.01) as sas_Total_revenue length=8 format=11.2 
            from &&work_lib..pace_all_exp
                group by property_id, accom_type_id, occupancy_dt, capture_dttm 
                    order by property_id, accom_type_id, occupancy_dt, capture_dttm;
    quit;

    data &work_lib..accom_inventory_pace_last (keep=property_id Occupancy_dt capture_dttm Accom_type_id sas_rooms_sold 
        sas_Arrivals sas_departures sas_Cancellations sas_no_shows sas_Room_Revenue sas_Food_Revenue sas_Total_Revenue);
        retain property_id Occupancy_dt capture_dttm Accom_type_id sas_rooms_sold 
            sas_Arrivals sas_departures sas_Cancellations sas_no_shows sas_Room_Revenue sas_Food_Revenue sas_Total_Revenue;
        retain previous_rooms_sold 0;
        retain previous_room_revenue 0;
        retain previous_food_revenue 0;
        retain previous_total_revenue 0;
        retain previous_cancellations 0;
        retain previous_no_shows 0;
        retain previous_arrivals 0;
        retain previous_departures 0;
        set &work_lib..accom_inventory_pace_temp end=last;
        by property_id accom_type_id occupancy_dt capture_dttm;

        if _n_=1 or first.property_id=1 or first.accom_type_id=1 or first.occupancy_dt=1 then
            do;
                previous_rooms_sold=-10;
                previous_room_revenue=-10;
                previous_food_revenue=-10;
                previous_total_revenue=-10;
                previous_cancellations=-10;
                previous_no_shows=-10;
                previous_arrivals=-10;
                previous_departures=-10;
            end;

        if (previous_arrivals ne sas_arrivals or 
            previous_rooms_sold ne sas_rooms_sold or 
            previous_room_revenue ne sas_room_revenue or 
            previous_food_revenue ne sas_food_revenue or 
            previous_total_revenue ne sas_total_revenue or 
            previous_no_shows ne sas_no_shows or 
            previous_cancellations ne sas_cancellations or 
            previous_departures ne sas_departures or last.accom_type_id=1 or last.occupancy_dt=1) then
            output &work_lib..accom_inventory_pace_last;
        previous_rooms_sold=sas_rooms_sold;
        previous_room_revenue=sas_room_revenue;
        previous_food_revenue=sas_food_revenue;
        previous_total_revenue=sas_total_revenue;
        previous_no_shows=sas_no_shows;
        previous_cancellations=sas_cancellations;
        previous_arrivals=sas_arrivals;
        previous_departures=sas_departures;
    run;

    proc Sql;
        create table &work_lib..accom_inventory_pace as 
            select a.property_id, a.accom_type_id, a.occupancy_dt, a.capture_dttm, 
                b.accom_capacity as sas_accom_capacity length=6 format=7.,
                b.rooms_not_avail_maint as sas_rooms_not_avail_maint length=6 format=7., 
                b.rooms_not_avail_other as sas_rooms_not_avail_other length=6 format=7., 
                a.sas_arrivals, a.sas_departures, a.sas_no_shows , a.sas_cancellations,
                a.sas_Room_Revenue, a.sas_rooms_sold , a.sas_food_Revenue , a.sas_Total_Revenue
            from &work_lib..accom_inventory_pace_last as a 
                inner join &work_lib..Accom_capacity as b 
                    on a.accom_type_id=b.accom_type_id and a.occupancy_dt=b.occupancy_dt and a.capture_dttm=b.capture_dttm 
                order by a.property_id, a.accom_type_id, a.occupancy_dt, a.capture_dttm;
    quit;

    proc Sql;
        create table &work_lib..total_capacity as 
            select distinct property_id, occupancy_dt, capture_dttm, 
                sum(accom_capacity) as sas_total_accom_capacity length=6 format=7.,
                sum(rooms_not_avail_maint) as sas_rooms_not_avail_maint length=6 format=7., 
                sum(rooms_not_avail_other) as sas_rooms_not_avail_other length=6 format=7. 
            from &work_lib..accom_capacity
                group by property_id, occupancy_dt, capture_dttm
                    order by property_id, occupancy_dt, capture_dttm;
    quit;

    proc Sql;
        create table &work_lib..Total_Pace_all_temp as 
            select distinct property_id, occupancy_dt, capture_dttm, 
                sum(sas_arrivals) as sas_arrivals length=6 format=7.,
                sum(sas_departures) as sas_departures length=6 format=7., 
                sum(sas_no_shows) as sas_no_shows length=6 format=7., 
                sum (sas_cancellations) as sas_cancellations length=6 format=7., 
                round(sum(sas_Room_Revenue),.01) as sas_Room_revenue length=8 format=11.2, 
                sum(sas_rooms_sold) as sas_rooms_sold length=6 format=7., 
                round(sum(sas_Food_Revenue),.01) as sas_Food_revenue length=8 format=11.2, 
                round(sum(sas_Total_Revenue),.01) as sas_Total_revenue length=8 format=11.2 
            from &work_lib..pace_all_exp
                group by property_id, occupancy_dt, capture_dttm
                    order by property_id, occupancy_dt, capture_dttm;
    quit;

    data &work_lib..Total_Pace_all_last (keep=property_id Occupancy_dt capture_dttm sas_rooms_sold 
        sas_Arrivals sas_departures sas_Cancellations sas_no_shows sas_Room_Revenue sas_Food_Revenue sas_Total_Revenue );
        retain property_id Occupancy_dt capture_dttm sas_rooms_sold 
            sas_Arrivals sas_departures sas_Cancellations sas_no_shows sas_Room_Revenue sas_Food_Revenue sas_Total_Revenue;
        retain previous_rooms_sold 0;
        retain previous_room_revenue 0;
        retain previous_food_revenue 0;
        retain previous_total_revenue 0;
        retain previous_cancellations 0;
        retain previous_no_shows 0;
        retain previous_arrivals 0;
        retain previous_departures 0;
        set &work_lib..Total_Pace_all_temp end=last;
        by property_id occupancy_dt capture_dttm;

        if _n_=1 or first.property_id=1 or first.occupancy_dt=1 then
            do;
                previous_rooms_sold=-10;
                previous_room_revenue=-10;
                previous_food_revenue=-10;
                previous_total_revenue=-10;
                previous_cancellations=-10;
                previous_no_shows=-10;
                previous_arrivals=-10;
                previous_departures=-10;
            end;

        if (previous_arrivals ne sas_arrivals or 
            previous_rooms_sold ne sas_rooms_sold or 
            previous_room_revenue ne sas_room_revenue or 
            previous_food_revenue ne sas_food_revenue or 
            previous_total_revenue ne sas_total_revenue or 
            previous_no_shows ne sas_no_shows or 
            previous_cancellations ne sas_cancellations or 
            previous_departures ne sas_departures or last.occupancy_dt=1) then
            output &work_lib..Total_Pace_all_last;
        previous_rooms_sold=sas_rooms_sold;
        previous_room_revenue=sas_room_revenue;
        previous_food_revenue=sas_food_revenue;
        previous_total_revenue=sas_total_revenue;
        previous_no_shows=sas_no_shows;
        previous_cancellations=sas_cancellations;
        previous_arrivals=sas_arrivals;
        previous_departures=sas_departures;
    run;

    proc Sql;
        create table &work_lib..total_inventory_pace as 
            select a.*, b.sas_total_accom_capacity, b.sas_rooms_not_avail_maint , b.sas_rooms_not_avail_other
                from &work_lib..Total_Pace_all_last as a 
                    inner join &work_lib..total_capacity as b 
                        on a.occupancy_dt=b.occupancy_dt and a.capture_dttm=b.capture_dttm 
                    order by a.property_id, a.occupancy_dt, a.capture_dttm;
    quit;

    proc Sql;
        create table &work_lib..db_PACE_mkt_Activity1 as 
            select a.property_id , a.Occupancy_dt , dhms(datepart(a.snapshot_dttm),0,0,b.snapshot_tm) as capture_dttm format=datetime20., a.business_day_end_dt, a.Mkt_seg_id, 
                a.rooms_sold, a.Arrivals, a.departures, a.Cancellations, a.no_shows,
                round(a.Room_Revenue,.01) as Room_Revenue format=11.2, round(a.Food_Revenue,.01) as Food_Revenue format=11.2, round(a.Total_Revenue,.01) as Total_Revenue format=11.2, a.File_metadata_id 
            from tenant.PACE_Mkt_Activity as a 
                inner join &work_lib..file_metadata as b 
                    on a.File_metadata_id = b.File_metadata_id 
                order by property_id, a.mkt_seg_id, a.occupancy_dt, capture_dttm;
        create table &work_lib..db_PACE_Accom_Activity1 as 
            select property_id, Occupancy_dt , snapshot_dttm as capture_dttm format=datetime20., business_day_end_dt, Accom_type_id, accom_capacity, 
                rooms_sold, rooms_not_avail_maint, rooms_not_avail_other, Arrivals, departures, Cancellations, no_shows, 
                round(Room_Revenue,.01) as Room_Revenue format=11.2, round(Food_Revenue,.01) as Food_Revenue format=11.2, round(Total_Revenue,.01) as Total_Revenue format=11.2, File_metadata_id 
            from tenant.PACE_Accom_Activity
                order by property_id, accom_type_id, occupancy_dt, capture_dttm;
        create table &work_lib..db_PACE_Total_Activity1 as 
            select property_id, Occupancy_dt , snapshot_dttm as capture_dttm format=datetime20., business_day_end_dt, Total_accom_capacity, 
                rooms_sold format=7. length=6, rooms_not_avail_maint, rooms_not_avail_other, Arrivals, departures, Cancellations, no_shows,
                round(Room_Revenue,.01) as Room_Revenue format=11.2, round(Food_Revenue,.01) as Food_Revenue format=11.2, round(Total_Revenue,.01) as Total_Revenue format=11.2, File_metadata_id 
            from tenant.PACE_Total_Activity 
                order by property_id, occupancy_dt, capture_dttm;
    quit;

    data &work_lib..db_PACE_mkt_Activity (keep=property_id Occupancy_dt capture_dttm business_day_end_dt mkt_seg_id rooms_sold 
        Arrivals departures Cancellations no_shows Room_Revenue Food_Revenue Total_Revenue File_metadata_id );
        retain property_id Occupancy_dt capture_dttm business_day_end_dt mkt_seg_id rooms_sold 
            Arrivals departures Cancellations no_shows Room_Revenue Food_Revenue Total_Revenue File_metadata_id;
        retain previous_rooms_sold 0;
        retain previous_room_revenue 0;
        retain previous_food_revenue 0;
        retain previous_total_revenue 0;
        retain previous_cancellations 0;
        retain previous_no_shows 0;
        retain previous_arrivals 0;
        retain previous_departures 0;
        set &work_lib..db_PACE_mkt_Activity1 end=last;
        by property_id mkt_seg_id occupancy_dt capture_dttm;

        if _n_=1 or first.Mkt_seg_id=1 or first.occupancy_dt=1 then
            do;
                previous_rooms_sold=-10;
                previous_room_revenue=-10;
                previous_food_revenue=-10;
                previous_total_revenue=-10;
                previous_cancellations=-10;
                previous_no_shows=-10;
                previous_arrivals=-10;
                previous_departures=-10;
            end;

        if (previous_arrivals ne arrivals or 
            previous_rooms_sold ne rooms_sold or 
            previous_room_revenue ne room_revenue or 
            previous_food_revenue ne food_revenue or 
            previous_total_revenue ne total_revenue or 
            previous_no_shows ne no_shows or 
            previous_cancellations ne cancellations or 
            previous_departures ne departures or last.mkt_seg_id=1 or last.occupancy_dt=1 ) then
            output &work_lib..db_PACE_mkt_Activity;
        previous_rooms_sold=rooms_sold;
        previous_room_revenue=room_revenue;
        previous_food_revenue=food_revenue;
        previous_total_revenue=total_revenue;
        previous_no_shows=no_shows;
        previous_cancellations=cancellations;
        previous_arrivals=arrivals;
        previous_departures=departures;
    run;

    data &work_lib..db_PACE_Accom_Activity (keep=property_id Occupancy_dt capture_dttm business_day_end_dt Accom_type_id accom_capacity rooms_sold rooms_not_avail_maint rooms_not_avail_other 
        Arrivals departures Cancellations no_shows Room_Revenue Food_Revenue Total_Revenue File_metadata_id );
        retain property_id Occupancy_dt capture_dttm business_day_end_dt Accom_type_id accom_capacity rooms_sold rooms_not_avail_maint rooms_not_avail_other 
            Arrivals departures Cancellations no_shows Room_Revenue Food_Revenue Total_Revenue File_metadata_id;
        retain previous_rooms_sold 0;
        retain previous_room_revenue 0;
        retain previous_food_revenue 0;
        retain previous_total_revenue 0;
        retain previous_cancellations 0;
        retain previous_no_shows 0;
        retain previous_arrivals 0;
        retain previous_departures 0;
        retain previous_accom_capacity 0;
        retain previous_rooms_not_avail_maint 0;
        retain previous_rooms_not_avail_other 0;
        set &work_lib..db_PACE_Accom_Activity1 end=last;
        by property_id accom_type_id occupancy_dt capture_dttm;

        if _n_=1 or first.accom_type_id=1 or first.occupancy_dt=1 then
            do;
                previous_rooms_sold=-10;
                previous_room_revenue=-10;
                previous_food_revenue=-10;
                previous_total_revenue=-10;
                previous_cancellations=-10;
                previous_no_shows=-10;
                previous_arrivals=-10;
                previous_departures=-10;
                previous_accom_capacity=-10;
                previous_rooms_not_avail_maint=-10;
                previous_rooms_not_avail_other=-10;
            end;

        if (previous_arrivals ne arrivals or 
            previous_rooms_sold ne rooms_sold or 
            previous_room_revenue ne room_revenue or 
            previous_food_revenue ne food_revenue or 
            previous_total_revenue ne total_revenue or 
            previous_no_shows ne no_shows or 
            previous_cancellations ne cancellations or 
            previous_departures ne departures or 
            previous_accom_capacity ne accom_capacity or 
            previous_rooms_not_avail_maint ne rooms_not_avail_maint or 
            previous_rooms_not_avail_other ne rooms_not_avail_other or 
            last.accom_type_id=1 or last.occupancy_dt=1) then
            output &work_lib..db_PACE_Accom_Activity;
        previous_rooms_sold=rooms_sold;
        previous_room_revenue=room_revenue;
        previous_food_revenue=food_revenue;
        previous_total_revenue=total_revenue;
        previous_no_shows=no_shows;
        previous_cancellations=cancellations;
        previous_arrivals=arrivals;
        previous_departures=departures;
        previous_accom_capacity=accom_capacity;
        previous_rooms_not_avail_maint=rooms_not_avail_maint;
        previous_rooms_not_avail_other=rooms_not_avail_other;
    run;

    data &work_lib..db_PACE_Total_Activity (keep=property_id Occupancy_dt capture_dttm business_day_end_dt Total_accom_capacity rooms_sold rooms_not_avail_maint rooms_not_avail_other 
        Arrivals departures Cancellations no_shows Room_Revenue Food_Revenue Total_Revenue File_metadata_id );
        retain property_id Occupancy_dt capture_dttm business_day_end_dt Total_accom_capacity rooms_sold rooms_not_avail_maint rooms_not_avail_other 
            Arrivals departures Cancellations no_shows Room_Revenue Food_Revenue Total_Revenue File_metadata_id;
        retain previous_rooms_sold 0;
        retain previous_room_revenue 0;
        retain previous_food_revenue 0;
        retain previous_total_revenue 0;
        retain previous_cancellations 0;
        retain previous_no_shows 0;
        retain previous_arrivals 0;
        retain previous_departures 0;
        retain previous_Total_accom_capacity 0;
        retain previous_rooms_not_avail_maint 0;
        retain previous_rooms_not_avail_other 0;
        set &work_lib..db_PACE_Total_Activity1 end=last;
        by property_id occupancy_dt capture_dttm;

        if _n_=1 or first.occupancy_dt=1 then
            do;
                previous_rooms_sold=-10;
                previous_room_revenue=-10;
                previous_food_revenue=-10;
                previous_total_revenue=-10;
                previous_cancellations=-10;
                previous_no_shows=-10;
                previous_arrivals=-10;
                previous_departures=-10;
                previous_Total_accom_capacity=-10;
                previous_rooms_not_avail_maint=-10;
                previous_rooms_not_avail_other=-10;
            end;

        if (previous_arrivals ne arrivals or 
            previous_rooms_sold ne rooms_sold or 
            previous_room_revenue ne room_revenue or 
            previous_food_revenue ne food_revenue or 
            previous_total_revenue ne total_revenue or 
            previous_no_shows ne no_shows or 
            previous_cancellations ne cancellations or 
            previous_departures ne departures or 
            previous_total_accom_capacity ne total_accom_capacity or 
            previous_rooms_not_avail_maint ne rooms_not_avail_maint or 
            previous_rooms_not_avail_other ne rooms_not_avail_other or 
            last.occupancy_dt=1) then
            output &work_lib..db_PACE_Total_Activity;
        previous_rooms_sold=rooms_sold;
        previous_room_revenue=room_revenue;
        previous_food_revenue=food_revenue;
        previous_total_revenue=total_revenue;
        previous_no_shows=no_shows;
        previous_cancellations=cancellations;
        previous_arrivals=arrivals;
        previous_departures=departures;
        previous_Total_accom_capacity=Total_accom_capacity;
        previous_rooms_not_avail_maint=rooms_not_avail_maint;
        previous_rooms_not_avail_other=rooms_not_avail_other;
    run;

    options label;

    %if &syscc >4 %then
        %do;
            %let etl_status = 1;
            %let return_code=200;
        %end;
    %else
        %do;
            %if %sysfunc(exist(&work_lib..db_PACE_mkt_Activity)) and %ideas_util_nobs(&work_lib..db_PACE_mkt_Activity) > 0 and 
                %sysfunc(exist(&work_lib..mkt_inventory_pace)) and %ideas_util_nobs(&work_lib..mkt_inventory_pace) > 0 %then
                %let return_code=0;
            %else %let return_code=100;
        %end;
%mend;