%macro ideas_msldb/store;

%let macroname=&SYSMACRONAME;

%ideas_macro_log (start_end=START, macroname=&macroname.);

	proc sql noprint;
		select distinct mkt_seg_id into: list SEPARATED by '|' from tenant.mkt_seg where Property_ID EQ &property_id.;
	quit;

	
	%let ms_list = &list;
	%let i = 1;
	

	%do %while (%scan(&ms_list., &i., |) ne);

		data _null_;
			call symputx ('ms', %scan(&ms_list, &i, |), 'l');
		run;
		%let dsn = Ma_&ms;
		%let los_dsn = ma_los_&ms.;
		%let libdsn = rm_part.ma_&ms.;
		%let liblosdsn = rm_part.ma_los_&ms.;

		%if %sysfunc(exist(&libdsn)) eq 0 %then %do;	

			proc sql;
				create table rm_part.&dsn. like rm_part.ma_inv_pace_template;
			quit;

			%ideas_zero_fill_ma(&libdsn);
		%end;

		%if %sysfunc(exist(&liblosdsn)) eq 0 %then %do;	

			proc sql;
				create table rm_part.&los_dsn. like rm_part.Ma_los_inv_pace_template;
			quit;

		%end;
		
		%let i = %sysevalf(&i. + 1);
	%end;

%let macroname=&SYSMACRONAME;

%ideas_macro_log (start_end=END, macroname=&macroname.);
%mend ideas_msldb;

