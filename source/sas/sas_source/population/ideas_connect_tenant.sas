%macro ideas_connect_tenant(tenant_db, tenant_server, port, server_instance, tenant_user, tenant_pwd)/store;
	
%let macroname=&SYSMACRONAME; 
	
%ideas_macro_log (start_end=START, macroname=&macroname.);

	%ideas_initialize_trans(lib_name=tenant,server=&tenant_server, uid=&tenant_user, server_inst=&server_instance,
		pwd=&tenant_pwd, database=&tenant_db, port=&port, is_temp=0);
	%ideas_initialize_trans(lib_name=ten_tmp,server=&tenant_server, uid=&tenant_user, server_inst=&server_instance,
		pwd=&tenant_pwd, database=&tenant_db, port=&port, is_temp=1);

	%if &syscc > 4 or "&GL_ABORT_FLAG" eq "1" %then %do;
		%unquote(%nrstr(%%)ideas_util_log_msg(msg=Exiting out unable to initialize tenant database,lvl=6,macro=&sysmacroname);
			data failure;
			length error_cd 8 request_id error_params error_message $50;
			request_id = "&requestId";
			error_cd = 902;
			error_params = "";
			error_message = "Failed to initialize tenant database";
			run;

			proc append base=errors data=failure;  run;
			%goto EXIT;
	%end;

	%EXIT:

	%if "&GL_ABORT_FLAG" eq "1" or &syscc > 4 %then %do;
		%let GL_ABORT_FLAG =1;
		%let syscc = 5;
		%let etl_status = 1;
	%end;
	
%let macroname=&SYSMACRONAME; 
	
%ideas_macro_log (start_end=END, macroname=&macroname.);
%mend;
