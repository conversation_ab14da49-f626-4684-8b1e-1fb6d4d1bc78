%macro sync_tenant_ms_part(tenant_ms=, ms_parts=, ms_prefix=, ms_los_prefix=, Accom_Type_Option=)/store;

    libname partlibp "&sas_path.\partitions";
    libname partlibt "&sas_path.\partitions\pacehistorypart";

    %put PMS Revamp: Syncing tenant.Mkt_Seg and mkt_partitions ;

    %let mktsegi=1;
    %do %while (%scan(&tenant_ms., &mktsegi., |) ne);

        data _null_;
            call symputx ('t_ms', %scan(&tenant_ms., &mktsegi., | ),'l');
        run;

        %let found=FALSE;
        %let mktsegj=1;

        %do %while (%scan(&ms_parts., &mktsegj., ' ') ne);
            data _null_;
                call symputx ('ms_part', "%scan(&ms_parts., &mktsegj., ' ')",'l');
            run;

            %if %upcase(&ms_prefix._&t_ms.) = %upcase(&ms_part.) %then
                %do;
                    %let found=TRUE;
                %end;

            %let mktsegj = %sysevalf(&mktsegj. + 1);
        %end;

        %if &found.=FALSE %then
            %do;
                %put Partitions do not exist for Mkt_Seg- &t_ms. . Creating empty ones.;

            %if %upcase(&stopPopulatingPartitionsTable) eq FALSE %then
                %do;
                    data partlibp.&ms_prefix._&t_ms.;
                        set partlibp.ma_inv_pace_template(obs=0);
                    run;
                %end;

                data partlibt.&ms_prefix._&t_ms.;
                    set partlibp.ma_inv_pace_template(obs=0);
                run;

                %if %upcase(&Accom_Type_Option.)=STAYED %then
                    %do;
                        %if %upcase(&stopPopulatingPartitionsTable) eq FALSE %then
                            %do;
                                data partlibp.&ms_los_prefix._&t_ms.;
                                    set partlibp.ma_los_inv_pace_template(obs=0);
                                run;
                            %end;

                        data partlibt.&ms_los_prefix._&t_ms.;
                            set partlibp.ma_los_inv_pace_template(obs=0);
                        run;
                    %end;
                %else
                    %do;
                    %if %upcase(&stopPopulatingPartitionsTable) eq FALSE %then
                        %do;
                            data partlibp.&ms_los_prefix._&t_ms.(drop=total_revenue);
                               set partlibp.ma_los_inv_pace_template(obs=0);
                            run;
                        %end;

                        data partlibt.&ms_los_prefix._&t_ms.(drop=total_revenue);
                           set partlibp.ma_los_inv_pace_template(obs=0);
                        run;
                    %end;
            %end;

        %let mktsegi = %sysevalf(&mktsegi. + 1);
    %end;


    %let mktsegi=1;
    %do %while (%scan(&ms_parts., &mktsegi., ' ') ne);

        data _null_;
            call symputx ('ms_part', "%scan(&ms_parts., &mktsegi., ' ')",'l');
        run;

        %let found=FALSE;
        %let mktsegj=1;
        %do %while (%scan(&tenant_ms., &mktsegj., |) ne);
            data _null_;
                call symputx ('t_ms', %scan(&tenant_ms., &mktsegj., | ),'l');
            run;

            %if %upcase(&ms_prefix._&t_ms.) = %upcase(&ms_part.) or %upcase(&ms_los_prefix._&t_ms.) = %upcase(&ms_part.) %then
                %do;
                    %let found=TRUE;
                %end;

            %let mktsegj = %sysevalf(&mktsegj. + 1);
        %end;

        %if &found.=FALSE  and %index(%upcase(&ms_part), INV_PACE_TEMPLATE)=0 %then
            %do;
                %if %upcase(&stopPopulatingPartitionsTable) eq FALSE %then
                    %do;
                        %put Mkt_Seg counterpart of  &ms_part. does not exist. Deleting it.;

                        proc datasets library=partlibp noprint;
                           delete &ms_part.;
                        run;

                        proc datasets library=partlibt noprint;
                           delete &ms_part.;
                        run;
                    %end;
            %end;

        %let mktsegi = %sysevalf(&mktsegi. + 1);
    %end;

%mend sync_tenant_ms_part;