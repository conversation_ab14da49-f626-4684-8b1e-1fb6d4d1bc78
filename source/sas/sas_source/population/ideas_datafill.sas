%macro ideas_datafill(sumlevel,indsn,m_var, RecType)/store;
%let macroname=&SYSMACRONAME; 
	
%ideas_macro_log (start_end=START, macroname=&macroname.);

	%if &sumlevel = 1 %then %do;
		%let classvar=%str(Accom_type_cd market_segment);
		%let sqlvar=%str(a.Accom_type_cd, a.market_segment,);
		%let joinsql=%str(and a.Accom_type_cd=b.Accom_type_cd and a.market_segment = b.market_segment );
		%let firstdot=market_segment;
	%end;

	%if &sumlevel = 2 %then %do;
		%let classvar=%str(accom_type_cd);
		%let sqlvar=%str(a.accom_type_cd,);
		%let joinsql=%str(and a.accom_type_cd=b.accom_type_cd);
		%let firstdot=accom_type_cd;
	%end;

	%if &sumlevel = 3 %then %do;
		%let classvar=;
		%let sqlvar=;
		%let joinsql=;
		%let firstdot=;
	%end;

	/*** Step 2. Loop time series from Min to Max by Category to get all dates within time range by category. ***/
	data &work_lib..OccDates;
		set &indsn._stats;

		%if %eval(&sumlevel < 3) %then %do;
			by &classvar;

			if first.&firstdot then
				occupancy_dt=MinOcc;
		%end;
		%else %do;
			occupancy_dt=MinOcc;
		%end;

		format occupancy_dt date9.;

		do i = 0 to(MaxOcc-MinOcc);
			occupancy_dt = MinOcc + i;
			output &work_lib..OccDates;
		end;

		drop i MinOcc MaxOcc;
	run;

	/*** Step 3: Merge with time series template with actual data. CASE Logic Assigns sold to m_var if missing. ***/
	proc sort data=&indsn.;
		by occupancy_dt &classvar.;
	run;

	proc sort data=&work_lib..OccDates ;
		by occupancy_dt &classvar.;
	run;
    
	%if &sumlevel = 1 %then %do;

		data &indsn.;
			merge &work_lib..occdates (in=a) &indsn. (in=b);
			by occupancy_dt &classvar.;

			if a and not b then do;
				rooms_sold = &m_var.;
				arrivals= &m_var.;
				departures = &m_var.;
				room_revenue = &m_var.;
				food_revenue = &m_var.;
				total_revenue = &m_var.;
				cancellations = &m_var.;
				no_shows = &m_var.;
				record_type_id = &RecType.;
			end;
		run;

	%end;

	%if &sumlevel = 2 %then %do;

		data &indsn.;
			merge &work_lib..occdates (in=a) &indsn. (in=b);
			by occupancy_dt &classvar.;

			if a and not b then do;
				rooms_not_avail_maint = &m_var.;
				rooms_not_avail_other = &m_var.;
				rooms_sold = &m_var.;
				arrivals= &m_var.;
				departures = &m_var.;
				room_revenue = &m_var.;
				food_revenue = &m_var.;
				total_revenue = &m_var.;
				cancellations = &m_var.;
				no_shows = &m_var.;
				record_type_id = &RecType.;
				accom_capacity = .;
				*accom_capacity = &m_var.;
			end;
		run;
  
         %ideas_fillcapacity (&indsn., &classvar., accom_capacity);
	%end;

	%if &sumlevel = 3 %then %do;

		data &indsn.;
			merge &work_lib..occdates (in=a) &indsn. (in=b);
			by occupancy_dt &classvar.;

			if a and not b then do;
				rooms_not_avail_maint = &m_var.;
				rooms_not_avail_other = &m_var.;
				rooms_sold = &m_var.;
				arrivals= &m_var.;
				departures = &m_var.;
				room_revenue = &m_var.;
				food_revenue = &m_var.;
				total_revenue = &m_var.;
				cancellations = &m_var.;
				no_shows = &m_var.;
				record_type_id = &RecType.;
				total_accom_capacity = .;
				*total_accom_capacity = &m_var.; 
			end;
		run;

		%ideas_fillcapacity_total (&indsn., total_accom_capacity);
	%end;
%let macroname=&SYSMACRONAME; 
	
%ideas_macro_log (start_end=END, macroname=&macroname.);

%mend ideas_datafill;
