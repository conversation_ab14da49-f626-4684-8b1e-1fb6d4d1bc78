%macro ideas_check_ma_tables(lib=in_xml)/store;

options sortsize=3g;
    %Global pid ;
    %let runTime=0;
    %let syscc = 0;
    %let etl_status = 0;
    %let propertyClause=;

    %Put *********************************************************************;
    %Put ********************** Starting Check on ma_ **************************;
    %Put *********************************************************************;
    %Put;

    data _null_;
        set &lib..requestHeader;
        call symputx('Operation',OperationName,'g');
        call symputx('property_Id',propertyId,'g');
        call symputx('requestId',requestId,'g');
        call symputx('tenant_server',tenant_server,'g');
        call symputx('server_instance',tenant_server_instance,'g');
        call symputx('tenant_pwd',tenant_password,'g');
        call symputx('tenant_user',tenant_user,'g');
        call symputx('tenant_db',tenant_database,'g');
        call symputx('port',tenant_db_port,'g');
        call symputx('global_server',global_server,'g');
        call symputx('global_server_instance',global_server_instance,'g');
        call symputx('global_pwd',global_password,'g');
        call symputx('global_user',global_user,'g');
        call symputx('global_db',global_database,'g');
        call symputx('global_port',global_db_port,'g');
        call symputx('saspath',dataset_path,'g');
        stop;
    run;

    data _null_;
        set &lib..CheckMaTableRequestType;
        call symputx('client_id', clientId,'G');
        call symputx('property_id2', propertyId,'G');
        call symputx('check_date', checkDate,'G');
        call symputx('is_delete', isForDelete,'G');
    run;

    %let global_work_lib=glolib;
    %let work_path_drive=%substr (%sysfunc(pathname(work)),1,2);
    %let work_path_root=&work_path_drive./sas;

    %ideas_util_create_dir(dir=temp,base_path=&work_path_root.);
    %let work_path_root=&work_path_drive./sas/temp;

    %ideas_util_create_dir(dir=results,base_path=&work_path_root.);
    %let work_path_root=&work_path_drive./sas/temp/results;

    %let glolib_path=&work_path_root.;

    libname &global_work_lib "&work_path_root.";

    proc Datasets library=&global_work_lib. kill memtype = data noprint nolist;
    run;

    data &global_work_lib..errors;
        length err_ct error_cd 8 request_id error_params error_message $ 200;
        stop;
    run;

    %if %symexist(is_delete) and &is_delete. eq 1 %then %do;
        %goto EXIT;
    %end;

    %let global_connect_str = driver={SQL Server Native Client 10.0}%str(;)server=&global_server\&global_server_instance%str(;)uid=&global_user%str(;)pwd=&global_pwd%str(;)database=&global_db%str(;)port=&global_port;
    %let global_connect_str="&global_connect_str";
    libname gblib odbc noprompt=&global_connect_str.;

    proc sql;
        create table &global_work_lib..propDetails (
          property_id char(10),
          property_code char(10),
          property_name char(50),
          client_code char(10),
          onboard_date char(10),
          cutOff_date char(10),
          invalid_ms_list char(8000),
          total_ms num,
          invalid_ms_count num,
          min_mkt_pace_date char(10),
          min_accom_pace_date char(10),
          min_total_pace_date char(10),
          min_reservation_night char(10),
          min_mkt_activity_date char(10),
          min_accom_activity_date char(10),
          min_total_activity_date char(10),
          is_pms_migrated char(5),
          ams_rebuild_dt char(10)
        ) ;
    quit;

    %if %symexist(property_id2) and &property_id2. ne 0 %then %do;
        %let propertyClause= and prp.property_id=&property_id;
    %end;

    proc sql;
        create table &global_work_lib..propdb as
        select prp.property_id as property_id, dl.server_name as tenant_server, dl.server_inst as server_instance,
            "&tenant_user." as tenant_user, "&tenant_pwd." as tenant_pwd, dbname as tenant_db, port_number as port,
            SAS_Server_Name as sas_server, SAS_File_Location_Path as data_path
         from gblib.property prp
        left join gblib.sas_file_loc slc on prp.property_id=slc.property_id
        left join gblib.dbloc dl on prp.dbloc_id=dl.dbloc_id
        where prp.client_id=&client_id. &propertyClause.;
    quit;

    %let request_id = %str(&requestid.);

    data _null_;
        set &global_work_lib..propdb nobs=nobs;
        if _N_=1 then call symput("N_Libs",nobs);
        prop_id=cats("pid", _N_);
        sas_server_name=cats("sas_server",_N_);
        db_server=cats("tenant_server",_N_);
        db_inst=cats("server_instance",_N_);
        db_user=cats("tenant_user",_N_);
        db_pwd=cats("tenant_pwd",_N_);
        db_port=cats("port",_N_);
        db_name=cats("tenant_db",_N_);
        sas_file_path=cats("data_path",_N_);
        call symput(prop_id, property_id);
        call symput(sas_server_name, sas_server);
        call symput(db_server, tenant_server);
        call symput(db_inst, server_instance);
        call symput(db_user, tenant_user);
        call symput(db_pwd, tenant_pwd);
        call symput(db_port, port);
        call symput(db_name, tenant_db);
        call symput(sas_file_path, data_path);
    run;

    %let check_date_sas_format = %sysfunc(inputn(&check_date.,yymmdd10.));

    %let i=1;
    %do %while (&i <= &N_Libs);

        %let sas_path=%sysfunc(cats(\\, &&sas_server&i.,\,&&data_path&i.));
        %let property_id=&&pid&i.;
        %let tenant_db=&&tenant_db&i.;
        %let tenant_server=&&tenant_server&i.;
        %let port=&&port&i.;
        %let server_instance=&&server_instance&i.;
        %let tenant_user=&&tenant_user&i.;
        %let tenant_pwd=&&tenant_pwd&i.;


        %let mylog_path = %str(&saspath.);
        %let y=%sysfunc(compress(&request_id.,' -'));
        %let load_id = %sysfunc(substr(&y.,1,5));

        %let connect_str = driver={SQL Server Native Client 10.0}%str(;)server=&tenant_server\&server_instance%str(;)uid=&tenant_user%str(;)pwd=&tenant_pwd%str(;)database=&tenant_db%str(;)port=&port;
        %let connect_str="&connect_str";
        %let rm_data_perm_path=&sas_path.;
        %let rm_part_perm_path=&sas_path.\partitions;

        %ideas_util_create_dir(dir=pacehistorydata,base_path=&rm_data_perm_path.);
        %ideas_util_create_dir(dir=pacehistorypart,base_path=&rm_part_perm_path.);
        %ideas_util_create_dir(dir=group,base_path=&rm_part_perm_path.\pacehistorypart);
        libname rm_data "&sas_path.\pacehistorydata";
        libname rm_datap "&sas_path.";
        libname rm_part "&sas_path.\partitions\pacehistorypart";
        libname group "&sas_path.\partitions\pacehistorypart\group";
        libname rm_partp "&sas_path.\partitions";

        libname dblib odbc noprompt=&connect_str.;

        %if %sysfunc(libref(dblib)) ne 0 or %sysfunc(libref(rm_datap)) ne 0 %then
            %do;
                %goto continue;
            %end;

        proc Sql noprint;
             select property_id, property_code, property_name into :pid, :pcode, :property_name from dblib.property where property_id = &property_id. and status_id=1;
        quit;

        %if not %symexist(pid) %then
            %do;
                %let property_desc=%str(Property: %trim(&property_id.) does not exist in the %trim(&tenant_db.) database);
                %let property_desc_short=%str(Property: %trim(&property_id.) does not exist in the %trim(&tenant_db.) database);

                %ideas_util_inc_errors;
                %ideas_util_report_errors(913, "&property_desc.", '', &request_Id., &err_ct.);
                %let etl_status = 1;
                %goto EXIT;
            %end;
        %else
            %do;
                %let property_desc=%str(Property: %trim(&property_id.) - %trim(&pcode.));
                %let property_desc_short=%str(Property %trim(&property_id.) - %trim(&pcode.));
            %end;

        %let paceCount=0;

        %if %sysfunc(exist(rm_datap.backfill_log)) %then
        %do;
            proc sql noprint;
                select count(*) into :paceCount from rm_datap.backfill_log;
            quit;
        %end;

        proc sql noprint;
            select min(snapshot_dt) into: minRes from dblib.File_Metadata
                where isBDE=1 and Record_Type_ID=3 and File_Name <> %str('PaceHistoryBuild');
        quit;
        %let pid=%trim(&pid.);

        proc sql;
            create table &global_work_lib..ma_dates_&property_id. (
              ms_id num,
              minOccupancyDate char(10),
              mktSegCreate_dt char(30)
            ) ;
        quit;

        %let inCompleteMs=;
        %let count=0;
        %let datenum=%sysfunc(inputn(&minRes.,date9.));

        proc sql noprint;
            select max(stayedOccupancyDtCutOff) into :minDate from rm_datap.backfill_log;
        quit;

        proc sql;
            select min(Occupancy_dt) into: min_mkt_pace_date from dblib.PACE_Mkt_Activity;
        quit;

        proc sql;
            select min(Occupancy_dt) into: min_accom_pace_date from dblib.PACE_Accom_Activity;
        quit;

        proc sql;
            select min(Occupancy_dt) into: min_total_pace_date from dblib.PACE_Total_Activity;
        quit;

        proc sql;
            select min(Departure_Dt) into: min_res_night_date from dblib.Reservation_Night;
        quit;

        proc sql;
            create table &global_work_lib..mkt_seg as
            select mkt_seg_id, mkt_seg_code, status_id, Created_DTTM from dblib.mkt_seg;
        quit;

        proc sql;
            select min(Occupancy_dt) into: min_mkt_activity_date from dblib.Mkt_Accom_Activity;
        quit;

        proc sql;
            select min(Occupancy_dt) into: min_accom_activity_date from dblib.Accom_Activity;
        quit;

        proc sql;
            select min(Occupancy_dt) into: min_total_activity_date from dblib.Total_Activity;
        quit;

        proc sql;
            select count(*) into: is_pms_migrated from gblib.PMS_Migration_Config where property_id= %superq(property_id) and Migration_State <> %str('PMS_MIGRATION_NOT_STARTED');
        quit;

        proc sql;
            select max(snapshot_dt) into: ams_rebuild_dt from dblib.File_Metadata where file_name=%str('OperaNgiLoaded');
        quit;

        %if &is_pms_migrated > 0 %then
        %do;
            %let is_pms_migrated=YES;
        %end;
        %else
            %do;
            %let is_pms_migrated=NO;
        %end;

        proc sql noprint;
            select distinct mkt_seg_id, count(distinct(mkt_seg_id))
            into :mslist SEPARATED by '|', :totalms
            from &global_work_lib..mkt_seg
            where mkt_seg_code <> %str('-1') and status_id=1
            order by mkt_seg_id;
        quit;

        %let msid=1;
        %do %while (%scan(&mslist., &msid., |) ne);
            data _null_;
                call symputx('ms', %scan(&mslist., &msid., |), 'l');
            run;
            %let minMSDate=0;
            %if %sysfunc(exist(rm_partp.ma_&ms.)) %then %do;
                proc sql noprint;
                    select min(occupancy_dt) into :minMSDate
                    from (select occupancy_dt as occupancy_dt
                            from rm_partp.ma_&ms.
                            where datepart(capture_dttm)<=occupancy_dt
                            group by occupancy_dt
                            having count(distinct datepart(capture_dttm))>1);
                quit;
            %end;

            proc sql;
                select Created_DTTM into: create_date from &global_work_lib..mkt_seg where mkt_seg_id= &ms.;
            quit;

            %if %sysfunc(exist(rm_partp.ma_&ms.)) and (&minMSDate. > &check_date_sas_format. or &minMSDate. eq .) %then
            %do;
                %let inCompleteMs=&inCompleteMs.| &ms.;
                %let count=%sysevalf(&count. + 1);
            %end;
            %let minMSDate=%sysfunc(putn(&minMSDate, date9.));

            proc sql noprint;
                insert into &global_work_lib..ma_dates_&property_id.(ms_id, minOccupancyDate, mktSegCreate_dt)
                    values(&ms., "%trim(&minMSDate.)", "%trim(&create_date.)");
            quit;
            %let msid = %sysevalf(&msid. + 1);
        %end;

        %let percent = %sysevalf(&count./&totalms.);
        %let invalidMs = %unquote( %substr(%superq(inCompleteMs), 2));

        proc sql noprint;
            select Property_Code, Property_Name, Client_Code into :propCode, :propName, :clientCode from dblib.Property where Property_ID=&pid.;
        quit;
        %let propName=%sysfunc(compress("&propName.", '"'''));
        %let propName=%sysfunc(tranwrd(%quote(&propName.), %str(,), %str(|)));
        %let minDate=%sysfunc(putn(&minDate, date9.));
        %let propName=%sysfunc(tranwrd(%quote(&propName.), %str(,), %str(|)));
        proc sql;
            insert into &global_work_lib..propDetails(property_id, property_code, property_name, client_code, onboard_date, cutOff_date, invalid_ms_list, total_ms,
                invalid_ms_count, min_mkt_pace_date, min_accom_pace_date, min_total_pace_date, min_reservation_night, min_mkt_activity_date, min_accom_activity_date, min_total_activity_date, is_pms_migrated, ams_rebuild_dt)
                values("&pid.", "%trim(&propCode.)","%trim(&propName.)", "%trim(&clientCode.)", "&minRes.", "&minDate.","%trim(&invalidMs.)", &totalms. , &count.,
                    "&min_mkt_pace_date.", "&min_accom_pace_date.", "&min_total_pace_date.", "&min_res_night_date.", "&min_mkt_activity_date.", "&min_accom_activity_date.", "&min_total_activity_date.", "&is_pms_migrated.",
                    "&ams_rebuild_dt.");
        quit;
        %continue:
            %let i= %eval(&i+1);
    %end;

%EXIT:

    proc Sql noprint;
        select count(*) into: errcount from &global_work_lib..errors;
    quit;

    %if &errcount. > 0 %then
        %do;

            proc Sql noprint;
                select distinct error_message into: err_str from &global_work_lib..errors;
            quit;

        %end;

    %if %symexist(is_delete) and &is_delete. eq 1 %then %do;
        proc Datasets library=&global_work_lib. kill memtype = data noprint nolist;
        run;
    %end;

    data _null_;
        FILE resXml;
        put '<?xml version="1.0" encoding="UTF-8"?>';
        put '<CheckMaTableSASResponse xmlns="http://xml.common.pacman.tetris.ideas.com/schema/checkmatable/response/v1" ';
        put 'xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" ';
        put 'xsi:schemaLocation="http://xml.common.pacman.tetris.ideas.com/schema/checkmatable/response/v1/check_ma_table_response.xsd ">';
        put "<ResponseHeader> <PropertyId>&property_Id</PropertyId>" @;
        put "<operationName>checkmatable</operationName>" @;
        put "<requestId>&requestId</requestId> @";
        put "</ResponseHeader> @";
        put "<CheckMaTableResponseType><tempLocation>&glolib_path.</tempLocation> @";
        put "</CheckMaTableResponseType> @";
        put "</CheckMaTableSASResponse>";
        stop;
    run;




    %Put *********************************************************************;
    %Put *********************** Ending check on ma_ ***************************;
    %Put *********************************************************************;
    %Put;

%mend ideas_check_ma_tables;