%macro ideas_pace_mkt_accom_los(rc=)/store;

   data &work_lib..mkt_accom_Los_pace (keep=Source property_id reservation_identifier mkt_seg_id accom_type_id los arrival_dt capture_dttm solds room_revenue total_revenue arrivals cancellations no_shows );
		retain Sold_output Source property_id reservation_identifier mkt_seg_id accom_type_id los arrival_dt capture_dttm solds room_revenue total_revenue arrivals cancellations no_shows;
		format capture_dttm datetime20.;
		Format Source $20.;
		Format Sold_output $1.;
		set &work_lib..Transactions_Combined_for_los;
		arrivals=0;
		cancellations=0;
		no_shows=0;
		Sold_output="N";

		if arrival_dt+(los) >=&minimum_occupancy_dt. then
			do;
				if (individual_status in ("SS", "CO" ,"CI") ) or 
					( individual_status in ("XX","CX")  and booking_dt<cancellation_dt  ) or
					(individual_status in ("NS")  and booking_dt<arrival_dt ) then
					do;
						if arrival_dt lt &first_snapshot_dt. then
							do;
								if booking_dt+1>=&minimum_snapshot_dt. then
									capture_dttm=dhms(booking_dt+1,0,0,'03:00:00't);
								else capture_dttm=dhms(max(booking_dt+1,arrival_dt-(&ProcessedNumberpaceDays.-1)),0,0,'03:00:00't);
							end;
						else capture_dttm=dhms(max(booking_dt+1,&first_snapshot_dt.-&ProcessedNumberpaceDays.),0,0,'03:00:00't);
						Source="SOLD";
						arrivals=solds;
						room_revenue=(room_revenue*solds);
						total_revenue=(total_revenue*solds);
						output &work_lib..mkt_accom_Los_pace;
						Sold_output="Y";
					end;

				if (individual_status in ("XX","CX" ) and cancellation_dt+1 <&first_snapshot_dt. ) then
					do;
						if cancellation_dt le arrival_dt then
							cancellations=1;

						if sold_output="Y" then
							arrivals=-1;
						else arrivals=0;

						if sold_output="Y" then
							do;
								rooms_sold=-1;
								room_revenue=(room_revenue*-1);
								total_revenue=(total_revenue*-1);
							end;
						else
							do;
								rooms_sold=0;
								room_revenue=0;
								total_revenue=0;
							end;

						capture_dttm=dhms(max(cancellation_dt+1,arrival_dt-(&ProcessedNumberpaceDays.-1)),0,0,'03:00:00't);
						Source="SOLD-CANCEL";
						output &work_lib..mkt_accom_Los_pace;
					end;

				if individual_status = 'NS' and arrival_dt+1 < &first_snapshot_dt. then
					do;
						no_shows=1;

						if sold_output="Y" then
							arrivals=-1;
						else arrivals=0;

						if sold_output="Y" then
							do;
								rooms_sold=-1;
								room_revenue=(room_revenue*-1);
								total_revenue=(total_revenue*-1);
							end;
						else
							do;
								rooms_sold=0;
								room_revenue=0;
								total_revenue=0;
							end;

						capture_dttm=dhms(max(arrival_dt+1,arrival_dt-(&ProcessedNumberpaceDays.-1)),0,0,'03:00:00't);
						Source="SOLD-NOSHOW";
						output &work_lib..mkt_accom_Los_pace;
					end;
			end;
	run;

	proc sort data =&work_lib..mkt_accom_Los_pace;
		by property_id mkt_seg_id Accom_Type_ID arrival_dt los capture_dttm;
	run;

	data &work_lib..mkt_accom_los_inventory_temp1;
		merge &work_lib..mkt_accom_los_Zeros (in=zeroin) &work_lib..mkt_accom_los_pace (in = losin);
		by property_id mkt_seg_id Accom_Type_ID arrival_dt los capture_dttm;
		retain property_id capture_dttm arrival_dt accom_type_id mkt_seg_id los arrivals no_shows cancellations Room_Revenue total_revenue;
		arrivals=sum(arrivals,0);
		Room_Revenue=sum(Room_Revenue,0);
		total_revenue=sum(total_revenue,0);
		cancellations=sum(cancellations,0);
		no_shows=sum(no_shows,0);

		if arrival_dt+los>=&minimum_occupancy_dt. then
			output &work_lib..mkt_accom_los_inventory_temp1;

	proc sql;
		create table &work_lib..mkt_accom_los_inventory_temp2 as 
			select distinct property_id, mkt_seg_id, accom_type_id, arrival_dt, los, capture_dttm , 
				sum(arrivals) as arrivals length=8 format=7., sum(cancellations) as cancellations length=8 format=7., sum(no_shows) as no_shows length=8 format=7., 
				round (sum(Room_Revenue),.01) as Room_Revenue length=8 format=11.2, round (sum(total_revenue),.01) as total_revenue length=8 format=11.2
			from &work_lib..mkt_accom_los_inventory_temp1
				group property_id, mkt_seg_id, accom_type_id, arrival_dt, los, capture_dttm 
					order by mkt_seg_id, accom_type_id, arrival_dt, los, capture_dttm;
	quit;

	data &work_lib..mkt_accom_los_inventory (keep=property_id capture_dttm arrival_dt los mkt_seg_id accom_type_id 
		arrivals cancellations no_shows Room_Revenue total_revenue);
		retain property_id mkt_seg_id accom_type_id arrival_dt los capture_dttm arrivals room_revenue total_revenue cancellations no_shows;
		retain save_room_revenue 0;
		retain save_total_revenue 0;
		retain save_cancellations 0;
		retain save_no_shows 0;
		retain save_arrivals 0;
		retain previous_rooms_sold 0;
		retain previous_room_revenue 0;
		retain previous_total_revenue 0;
		retain previous_cancellations 0;
		retain previous_no_shows 0;
		retain previous_arrivals 0;
		format capture_dttm datetime20.;
		set &work_lib..mkt_accom_los_inventory_temp2 end=last;
		by mkt_seg_id accom_type_id arrival_dt los capture_dttm;

		if first.mkt_seg_id=1 or first.accom_type_id=1 or first.arrival_dt=1 or first.los=1 then
			do;
				save_room_revenue=0;
				save_total_revenue=0;
				save_cancellations=0;
				save_no_shows=0;
				save_arrivals=0;
				previous_rooms_sold=.;
				previous_room_revenue=.;
				previous_total_revenue=.;
				previous_cancellations=.;
				previous_no_shows=.;
				previous_arrivals=.;
			end;

		save_room_revenue=save_room_revenue+room_revenue;
		save_total_revenue=save_total_revenue+total_revenue;
		save_arrivals=save_arrivals+arrivals;
		save_cancellations=save_cancellations+cancellations;
		save_no_shows=save_no_shows+no_shows;
		arrivals=save_arrivals;
		room_revenue=save_room_revenue;
		total_revenue=save_total_revenue;
		no_shows=save_no_shows;
		cancellations=save_cancellations;
		arrivals=save_arrivals;

		if (previous_arrivals ne save_arrivals or 
			previous_rooms_sold ne save_rooms_sold or 
			previous_room_revenue ne save_room_revenue or 
			previous_total_revenue ne save_total_revenue or 
			previous_no_shows ne save_no_shows or 
			previous_cancellations ne save_cancellations or 
			previous_arrivals ne save_arrivals) then
			output &work_lib..mkt_accom_los_inventory;
		previous_arrivals=save_arrivals;
		previous_rooms_sold=save_rooms_sold;
		previous_room_revenue=save_room_revenue;
		previous_total_revenue=save_total_revenue;
		previous_no_shows=save_no_shows;
		previous_cancellations=save_cancellations;
	run;

	proc sort data =&work_lib..mkt_accom_los_inventory;
		by mkt_seg_id accom_type_id arrival_dt los capture_dttm;
	run;

	%if &syscc >4 %then
		%let return_code=200;
	%else %if %sysfunc(exist(&work_lib..mkt_accom_los_inventory)) %ideas_util_nobs(&work_lib..mkt_accom_los_inventory) > 0 %then
		%let return_code=0;
	%else %let return_code=100;
%mend ideas_pace_mkt_accom_los;
