%macro ideas_type2_deltas_resumable/store;
    %let macroname=&SYSMACRONAME;

    %ideas_macro_log (start_end=START, macroname=&macroname.);
    %let rm_data = rm_data;

    proc Sql;
        select distinct accom_class_id into: accom_class_id 
            from tenant.accom_class where property_id = &property_id. and upcase(accom_class_code) ='UNASSIGNED';
    quit;

    %if not %symexist(accom_class_id) %then %do;
        %ideas_util_report_errors(917, 'No accom_class_id listed for the property', '', &request_Id.,1);
        %let etl_status = 1;
        %goto EXIT;
    %end;

    /*get user id from database to assign to overbooking values*/
    proc Sql noprint;
        select distinct user_id into: user_id from tenant.users where User_Name = 'SSO User';
    quit;

    %if not (%symexist(user_id)) %then %do;
        %ideas_util_report_errors(911, "%str(SSO User is not defined in the %trim(&tenant_db.) database )", " ", &request_Id., 1);
        %let etl_status = 1;
    %end;

    /*Identify the new accom and mkt seg codes in inbound data and load into rdbms to get new ids;*/
    proc Sql;
        create table &work_lib..inbound_accom_cd as
            select distinct accom_type_cd as accom_type_code , accom_type_cd as accom_type_name, &property_id. as property_id,
                1 as status_id, 1 as Accom_class_Id,0 as system_default,
                datetime() as Last_Updated_DTTM, datetime() as createdate 
            from &work_lib..accom where accom_type_code not in (select accom_type_code from tenant.accom_type
                where &property_id. = property_id and status_id in (1,2)) order by accom_type_code;
        create table &work_lib..inbound_mktseg_cd as
            select distinct market_segment as mkt_seg_code , market_segment as mkt_seg_name, &property_id as property_id,1 as status_id, 
                1 as Is_Editable, datetime() as Created_DTTM, datetime() as Last_updated_dttm, 1 as Template_Default,1 as Created_By_User_ID,1 as Last_Updated_By_User_ID
            from &work_lib..mkt_accom where mkt_seg_code not in (select mkt_seg_code from tenant.mkt_seg
                where &property_id. = property_id and status_id in(1,3)) order by mkt_seg_code;
    quit;

    proc Sql noprint;
        SELECT LEFT(PUT(COUNT(DISTINCT mkt_seg_code),3.)) 
            INTO:MKTCODECNT FROM tenant.mkt_seg_master;
    quit;

    proc Sql noprint;
        SELECT DISTINCT mkt_seg_code 
            INTO:MKT1-:MKT&MKTCODECNT FROM tenant.mkt_seg_master;
    quit;

    %do I=1 %TO &MKTCODECNT;
        %let mktcodes="&&mkt&I";

        proc Sql noprint;
            select is_Editable into:isEd from tenant.mkt_seg_master where mkt_seg_code=&mktcodes;
        quit;

        proc Sql;
            update &work_lib..inbound_mktseg_cd set is_Editable=&isEd where mkt_seg_code=&mktcodes;
        quit;

    %end;
    quit;

    /*Identify max capacity for new accom_codes based on inbound future available capacity*/
    proc Sql;
        create table &work_lib..inbound_accom_cd_ac as
            select distinct a.*, max(b.accom_capacity) as accom_type_capacity
                from &work_lib..inbound_accom_cd as a left join &work_lib..accom as b
                    on a.accom_type_code = b.accom_type_cd where b.record_type_id = 4
                group by a.accom_type_code;
    quit;

    %if %ideas_util_nobs(&work_lib..inbound_accom_cd) > 0 %then %do;

        data ten_tmp.iaccomtype&load_id.;
            set &work_lib..inbound_accom_cd_ac;
        run;

        /*set up connection string for sql passthrough query to insert into rdbms*/
        %local connect_Str;
        %let connect_str = driver={SQL Server Native Client 10.0}%str(;)server=&tenant_server\&server_instance%str(;)uid=&tenant_user%str(;)pwd=&tenant_pwd%str(;)database=&tenant_db%str(;)port=&port;
        %let connect_str=complete="&connect_str";

        proc Sql;
            connect to odbc (&connect_str autocommit=no);
            execute(
                insert into accom_type (accom_type_code, accom_type_name, property_id, status_id, accom_class_id, accom_type_capacity)
                    select accom_type_code, accom_type_name, property_id, status_id, accom_class_id, accom_type_capacity from ##iaccomtype&load_id.
                        ) by odbc;

            %if NOT (&sysdbrc eq 0 or "&sysdbrc" eq "") %then %do;
                execute(rollback) by odbc;
                %let error_codes = 918;
                %goto EXIT;
            %end;

            execute (
                commit
                )by odbc;
        quit;

    %end;

    %if %ideas_util_nobs(&work_lib..inbound_mktseg_cd) > 0 %then %do;
        %ideas_trans_append(data_table=&work_lib..inbound_mktseg_cd, trans_table=mkt_seg, trans_lib=tenant);
    %end;

    %if &syscc > 4 %then %do;
        %ideas_util_report_errors(918, 'Error while inserting accomodation types or market_segment codes', '', &request_Id.,1);
        %let etl_status = 1;
        %goto EXIT;
    %end;

    data &work_lib..Overbooking_Accom;
        set &work_lib..inbound_accom_cd;
        Sunday_Overbooking_Type_ID = 3;
        Sunday_Ceiling = -1;
        Monday_Overbooking_Type_ID = 3;
        Monday_Ceiling = -1;
        Tuesday_Overbooking_Type_ID = 3;
        Tuesday_Ceiling = -1;
        Wednesday_Overbooking_Type_ID = 3;
        Wednesday_Ceiling = -1;
        Thursday_Overbooking_Type_ID = 3;
        Thursday_Ceiling = -1;
        Friday_Overbooking_Type_ID = 3;
        Friday_Ceiling = -1;
        Saturday_Overbooking_Type_ID = 3;
        Saturday_Ceiling = -1;
        Created_By_User_ID = &user_id;
        Created_DTTM = createdate;
        Last_Updated_By_User_ID = &user_id;
        Last_Updated_DTTM = Last_Updated_DTTM;
        drop status_id accom_class_id createdate;
    run;

    proc Sql;
        create table &work_lib..Overbooking_Accom2 (drop= accom_type_code accom_type_name system_default) as
            select a.*, b.accom_type_id from &work_lib..Overbooking_Accom as a, tenant.accom_type as b 
                where a.accom_type_code = b.accom_type_code and b.status_id =1 and b.property_id = &property_id.;
    quit;

    %if %ideas_util_nobs(&work_lib..Overbooking_Accom2) > 0 %then %do;
        %ideas_trans_append(data_table=&work_lib..Overbooking_Accom2, trans_table=Overbooking_Accom, trans_lib=tenant);
    %end;

    %if &syscc > 4 %then %do;
        %ideas_util_report_errors(918, 'Error while inserting accomodation types or market_segment codes', '', &request_Id.,1);
        %let etl_status = 1;
        %goto EXIT;
    %end;

    /*Assign the unique accom and mkt seg ids to the inbound data;*/
    proc Sql;
        create table &work_lib..accom2 as
            select A.*,B.accom_type_id from
                &work_lib..accom A, tenant.accom_type B 
            where A.accom_type_cd = B.accom_type_code and b.status_id =1 and b.property_id = &property_id.;

        create table &work_lib..mkt_accom2 as
            select A.*,B.accom_type_id,C.mkt_Seg_id from
                &work_lib..mkt_accom A, tenant.accom_type B, tenant.mkt_seg C
            where A.accom_type_cd = B.accom_type_code and
                A.market_segment = C.mkt_seg_code and c.status_id =1 and c.property_id = &property_id. and 
                b.status_id =1 and b.property_id = &property_id.;
        drop table &work_lib..mkt_accom;
        drop table &work_lib..accom;
    quit;

    /*Adjust OOO overrides */
    %if %upcase(&oooOverrideEnabled.)=TRUE %then %do;
        %ideas_type2_ooo_ovrd_adjust;
    %end;

    /*Identify max capacity all accom_codes based on future available capacity*/
    proc Sql;
        create table &work_lib..accom_cd_ac as
            select distinct b.accom_type_id, b.property_id, b.accom_type_name, b.accom_type_code, b.roh_type, b.system_default, b.accom_type_description, 
                max(a.accom_capacity) as accom_type_capacity, b.accom_class_id, b.status_id, b.last_updated_dttm, b.Created_DTTM
            from &work_lib..accom2 as a, tenant.accom_type as b
                where a.record_type_id = 4 and a.accom_type_cd = b.accom_type_code and b.property_id = &property_id and b.status_id=1
                    group by b.accom_type_id;
    quit;

    %local connect_Str;
    %let connect_str = driver={SQL Server Native Client 10.0}%str(;)server=&tenant_server\&server_instance%str(;)uid=&tenant_user%str(;)pwd=&tenant_pwd%str(;)database=&tenant_db%str(;)port=&port;
    %let connect_str=complete="&connect_str";
    %local acc_cap;

    %ideas_trans_upload_tmp(upload_table=&work_lib..accom_cd_ac, _name_in_db=acc_cap,
        like_table=tenant.accom_type,
        tmp_trans_lib=ten_tmp,cnt=8&load_id.);
    %let acc_cap = ##&acc_cap;

    proc Sql;
        connect to odbc (&connect_str autocommit=no);
        execute(
            with etldata as (select * from &acc_cap.)
            merge accom_type as t
        using etldata as s
            on t.accom_type_id = s.accom_type_id and t.property_id = s.property_id
            when matched and (t.accom_type_capacity <> s.accom_type_Capacity) then
        update set t.accom_type_capacity = s.accom_type_capacity;

        %if &sqlrc ne 0 %then %do;
            rollback;
            %let error_codes = 919;
            %goto EXIT;
        %end;

        commit;
        ) by odbc;
    quit;

    /*assign market segments to the mkt_seg_details_proposed table */
    /*If the market segment is in the MKT_SEG_MASTER(new) table then populate the mkt_seg_details_proposed table */
    /*identify market segments that are in the mkt_seg_master table */
    proc Sql noprint;
        create table &work_lib..mkt_seg_prop as
            select distinct c.mkt_seg_id, a.business_type_id, a.yield_type_id, a.forecast_activity_type_id, 
                a.qualified, a.booking_block_pc, a.fenced, a.link, a.package, a.priced_by_bar /*, c.is_editable*/
        from tenant.mkt_seg_master as a, &work_lib..inbound_mktseg_cd as b,
            (select d.mkt_seg_id ,b.mkt_seg_code /*, d.is_editable*/ from &work_lib..inbound_mktseg_cd as b, 
                tenant.mkt_seg as d
            where b.mkt_seg_code = d.mkt_seg_code and b.property_id = d.property_id) as c 
            where a.mkt_seg_code = b.mkt_seg_code and c.mkt_seg_code = b.mkt_seg_code and a.business_type_id is not null
        ;
    quit;

    data &work_lib..mkt_seg_prop1;
        set &work_lib..mkt_seg_prop;
        Template_Default = 0;
        template_id=.;
        offset_type_id=.;
        offset_value=.;
        Process_Status_ID = 10;
        Status_ID = 1;
        Last_Updated_DTTM = datetime();
        Created_By_User_ID = 1;
        Last_Updated_By_User_ID = 1;
        Created_DTTM = datetime();
    run;

    %if %ideas_util_nobs(&work_lib..mkt_seg_prop1) > 0 %then %do;
        %ideas_trans_append(data_table=&work_lib..mkt_seg_prop1, trans_table=mkt_seg_details_proposed (drop=Mkt_Seg_Details_Proposed_ID), trans_lib=tenant);
    %end;

    /*Now make a backup copy (_fin) of the sas datasets so when I create the deltas I can automatically insert them
    into the _fin table. After RDBMS is updated I will then rename this _fin table to the original dataset name*/
    proc Copy in=rm_data out=&work_lib. memtype=data;
        select accom_inventory BDE_accom_inventory accom_inventory_pace total_inventory BDE_total_inventory total_inventory_pace mkt_accom_inventory BDE_mkt_accom_inventory;
    run;

    proc Datasets library=&work_lib.;
        change accom_inventory=accom_inventory_fin accom_inventory_pace=accom_inventory_pace_fin 
            total_inventory=total_inventory_fin total_inventory_pace=total_inventory_pace_fin
            mkt_accom_inventory=mkt_accom_inventory_fin;
    run;

    /*prep tables for sas insert;*/
    data &work_lib..accom_inventory;
        set &work_lib..accom2;

        if missing(cancellations) then
            cancellations =0;

        if missing(no_shows) then
            no_shows =0;
        property_id = &property_id;
        drop accom_type_cd record_type_id;
    run;

    proc Sort data=&work_lib..accom_inventory;
        by property_id accom_type_id occupancy_dt;

    data &work_lib..mkt_accom_inventory;
        set &work_lib..mkt_accom2;

        if missing(cancellations) then
            cancellations =0;

        if missing(no_shows) then
            no_shows =0;
        property_id = &property_id;
        drop market_segment accom_type_cd record_type_id;
    run;

    proc Sort data=&work_lib..mkt_accom_inventory;
        by property_id accom_type_id mkt_seg_Id occupancy_dt;

    data &work_lib..total_inventory;
        set &work_lib..total;

        if missing(cancellations) then
            cancellations =0;

        if missing(no_shows) then
            no_shows =0;
        property_id = &property_id;
        drop record_type_id;
    run;

    proc Sort data=&work_lib..total_inventory;
        by property_id occupancy_dt;

        *create temp table to store deltas;
    data &work_lib..delta_accom_inventory;
        attrib file_metadata_id length=8;
        attrib property_id length=8;
        attrib occupancy_dt length=8;
        attrib Accom_Type_ID length=8;
        attrib accom_capacity length=8;
        attrib rooms_not_avail_maint length=8;
        attrib rooms_not_avail_other length=8;
        attrib rooms_sold length=8;
        attrib arrivals length=8;
        attrib departures length=8;
        attrib no_shows length=8;
        attrib cancellations length=8;
        attrib room_revenue length=8;
        attrib rooms_sold length=8;
        attrib total_revenue length=8;
        attrib food_revenue length=8;
        attrib change_flag length=8;
        stop;
    run;

    data &work_lib..delta_mkt_accom_inventory;
        attrib file_metadata_id length=8;
        attrib property_id length=8;
        attrib occupancy_dt length=8;
        attrib Accom_Type_ID length=8;
        attrib rooms_not_avail_maint length=8;
        attrib rooms_not_avail_other length=8;
        attrib rooms_sold length=8;
        attrib arrivals length=8;
        attrib departures length=8;
        attrib no_shows length=8;
        attrib cancellations length=8;
        attrib room_revenue length=8;
        attrib rooms_sold length=8;
        attrib total_revenue length=8;
        attrib food_revenue length=8;
        attrib Mkt_Seg_ID length=8;
        attrib change_flag length=8;
        stop;
    run;

    data &work_lib..delta_total_inventory;
        attrib file_metadata_id length=8;
        attrib property_id length=8;
        attrib occupancy_dt length=8;
        attrib total_accom_capacity length=8;
        attrib rooms_not_avail_maint length=8;
        attrib rooms_not_avail_other length=8;
        attrib rooms_sold length=8;
        attrib arrivals length=8;
        attrib departures length=8;
        attrib no_shows length=8;
        attrib cancellations length=8;
        attrib room_revenue length=8;
        attrib rooms_sold length=8;
        attrib total_revenue length=8;
        attrib food_revenue length=8;
        attrib change_flag length=8;
        stop;
    run;

    /* PACE DATA */
    /*create the delta tables for the sas pace insert. Since there are fewer variables we need to create a smaller subset table*/
    data &work_lib..BDE_accom_inventory
        &work_lib..accom_inventory_pace (keep = property_id occupancy_dt Accom_Type_ID capture_dttm accom_capacity rooms_sold rooms_not_avail_maint rooms_not_avail_other);
        set &work_lib..accom_inventory (rename=(rooms_sold = i_rooms_sold accom_capacity=i_accom_capacity rooms_not_avail_maint=i_rooms_not_avail_maint rooms_not_avail_other=i_rooms_not_avail_other));
        modify &work_lib..BDE_accom_inventory key = acc_idx;

        if _iorc_=0 then do;
            if rooms_sold ne i_rooms_sold or accom_capacity ne i_accom_capacity 
                or rooms_not_avail_maint ne i_rooms_not_avail_maint or rooms_not_avail_other ne i_rooms_not_avail_other 
                then do;
                rooms_sold = i_rooms_sold;
                accom_capacity=i_accom_capacity;
                rooms_not_avail_other=i_rooms_not_avail_other;
                rooms_not_avail_maint=i_rooms_not_avail_maint;
                capture_dttm = &snap_dttm;
                output &work_lib..accom_inventory_pace;
            end;
        end;
        else do;
            rooms_sold = i_rooms_sold;
            accom_capacity=i_accom_capacity;
            rooms_not_avail_other=i_rooms_not_avail_other;
            rooms_not_avail_maint=i_rooms_not_avail_maint;
            capture_dttm = &snap_dttm;
            output &work_lib..accom_inventory_pace;
        end;

        _iorc_=0;
        _error_=0;
    run;

    proc Append base=&work_lib..accom_inventory_pace_fin
        data=&work_lib..accom_inventory_pace force;
    run;

    data &work_lib..accom_inventory_fin 
        &work_lib..delta_accom_inventory (keep = Accom_Type_ID snapshot_dttm accom_capacity rooms_sold rooms_not_avail_maint rooms_not_avail_other 
        occupancy_dt property_id file_metadata_id arrivals departures cancellations no_shows
        room_revenue food_revenue total_revenue change_flag);
        set &work_lib..accom_inventory (rename=(
            rooms_sold = i_rooms_sold 
            accom_capacity=i_accom_capacity
            rooms_not_avail_maint=i_rooms_not_avail_maint
            rooms_not_avail_other = i_rooms_not_avail_other 
            arrivals = i_arrivals
            departures = i_departures
            cancellations = i_cancellations
            no_shows = i_no_shows 
            room_revenue = i_room_revenue
            food_revenue = i_food_revenue
            total_revenue = i_total_revenue ));
        modify &work_lib..accom_inventory_fin key = acc_idx;

        *key is by property occupancy_dt room_type_id;
        if _iorc_=0 then do;
            if rooms_sold ne i_rooms_sold 
                or accom_capacity ne i_accom_capacity 
                or rooms_not_avail_maint ne i_rooms_not_avail_maint 
                or rooms_not_avail_other ne i_rooms_not_avail_other 
                or arrivals ne i_arrivals 
                or departures ne i_departures
                or cancellations ne i_cancellations 
                or no_shows ne i_no_shows 
                or room_revenue ne i_room_revenue
                or food_revenue ne i_food_revenue 
                or total_revenue ne i_total_revenue 
                then do;
                rooms_sold = i_rooms_sold;
                accom_capacity=i_accom_capacity;
                rooms_not_avail_maint=i_rooms_not_avail_maint;
                rooms_not_avail_other = i_rooms_not_avail_other;
                arrivals = i_arrivals;
                departures = i_departures;
                cancellations = i_cancellations;
                no_shows = i_no_shows;
                room_revenue = i_room_revenue;
                food_revenue = i_food_revenue;
                total_revenue = i_total_revenue;
                snapshot_dttm = &snap_dttm;
                file_metadata_id = &meta_id;
                change_flag = 1;
                replace &work_lib..accom_inventory_fin;
                output &work_lib..delta_accom_inventory;
            end;
            else do;
                rooms_sold = rooms_sold;
                accom_capacity=accom_capacity;
                rooms_not_avail_maint=rooms_not_avail_maint;
                rooms_not_avail_other = rooms_not_avail_other;
                arrivals = arrivals;
                departures = departures;
                cancellations = cancellations;
                no_shows = no_shows;
                room_revenue = room_revenue;
                food_revenue = food_revenue;
                total_revenue = total_revenue;
                snapshot_dttm = &snap_dttm;
                file_metadata_id = &meta_id;
                change_flag = 0;
                output &work_lib..delta_accom_inventory;
            end;
        end;
        else do;
            rooms_sold = i_rooms_sold;
            accom_capacity=i_accom_capacity;
            rooms_not_avail_maint=i_rooms_not_avail_maint;
            rooms_not_avail_other = i_rooms_not_avail_other;
            arrivals = i_arrivals;
            departures = i_departures;
            cancellations = i_cancellations;
            no_shows = i_no_shows;
            room_revenue = i_room_revenue;
            food_revenue = i_food_revenue;
            total_revenue = i_total_revenue;
            snapshot_dttm = &snap_dttm;
            file_metadata_id = &meta_id;
            change_flag = 1;
            output &work_lib..accom_inventory_fin;
            output &work_lib..delta_accom_inventory;
        end;

        _iorc_=0;
        _error_=0;
    run;

    

    /* market segment SAS pace data */
    data &work_lib..BDE_mkt_accom_inventory
        &work_lib..mkt_accom_inventory_pace (keep = Mkt_Seg_ID Accom_Type_ID snapshot_dttm rooms_sold 
        occupancy_dt property_id arrivals departures cancellations no_shows room_revenue food_revenue total_revenue );
        set &work_lib..mkt_accom_inventory (rename=(
            rooms_sold = i_rooms_sold 
            arrivals = i_arrivals
            departures = i_departures
            cancellations = i_cancellations
            no_shows = i_no_shows 
            room_revenue = i_room_revenue
            food_revenue = i_food_revenue
            total_revenue = i_total_revenue ));
        modify &work_lib..BDE_mkt_accom_inventory key = mkt_idx;

        *key is by property occupancy_dt room_type_id;
        if _iorc_=0 then do;
            if rooms_sold ne i_rooms_sold 
                or arrivals ne i_arrivals 
                or departures ne i_departures
                or cancellations ne i_cancellations 
                or no_shows ne i_no_shows 
                or room_revenue ne i_room_revenue
                or food_revenue ne i_food_revenue 
                or total_revenue ne i_total_revenue 
                then do;
                rooms_sold = i_rooms_sold;
                arrivals = i_arrivals;
                departures = i_departures;
                cancellations = i_cancellations;
                no_shows = i_no_shows;
                room_revenue = i_room_revenue;
                food_revenue = i_food_revenue;
                total_revenue = i_total_revenue;
                snapshot_dttm = &snap_dttm;
                output &work_lib..mkt_accom_inventory_pace;
            end;
        end;
        else do;
            rooms_sold = i_rooms_sold;
            arrivals = i_arrivals;
            departures = i_departures;
            cancellations = i_cancellations;
            no_shows = i_no_shows;
            room_revenue = i_room_revenue;
            food_revenue = i_food_revenue;
            total_revenue = i_total_revenue;
            snapshot_dttm = &snap_dttm;
            file_metadata_id = &meta_id;
            output &work_lib..mkt_accom_inventory_pace;
        end;

        _iorc_=0;
        _error_=0;
    run;

    data &work_lib..mkt_accom_inventory_fin
        &work_lib..delta_mkt_accom_inventory (keep = Mkt_Seg_ID Accom_Type_ID snapshot_dttm rooms_sold 
        occupancy_dt property_id file_metadata_id arrivals departures cancellations no_shows
        room_revenue food_revenue total_revenue change_flag);
        set &work_lib..mkt_accom_inventory (rename=(
            rooms_sold = i_rooms_sold 
            arrivals = i_arrivals
            departures = i_departures
            cancellations = i_cancellations
            no_shows = i_no_shows 
            room_revenue = i_room_revenue
            food_revenue = i_food_revenue
            total_revenue = i_total_revenue ));
        modify &work_lib..mkt_accom_inventory_fin key = mkt_idx;

        *key is by property occupancy_dt room_type_id;
        if _iorc_=0 then do;
            if rooms_sold ne i_rooms_sold 
                or arrivals ne i_arrivals 
                or departures ne i_departures
                or cancellations ne i_cancellations 
                or no_shows ne i_no_shows 
                or room_revenue ne i_room_revenue
                or food_revenue ne i_food_revenue 
                or total_revenue ne i_total_revenue 
                then do;
                rooms_sold = i_rooms_sold;
                arrivals = i_arrivals;
                departures = i_departures;
                cancellations = i_cancellations;
                no_shows = i_no_shows;
                room_revenue = i_room_revenue;
                food_revenue = i_food_revenue;
                total_revenue = i_total_revenue;
                snapshot_dttm = &snap_dttm;
                file_metadata_id = &meta_id;
                change_flag = 1;
                replace &work_lib..mkt_accom_inventory_fin;
                output &work_lib..delta_mkt_accom_inventory;
            end;
            else do;
                rooms_sold = rooms_sold;
                arrivals = arrivals;
                departures = departures;
                cancellations = cancellations;
                no_shows = no_shows;
                room_revenue = room_revenue;
                food_revenue = food_revenue;
                total_revenue = total_revenue;
                snapshot_dttm = &snap_dttm;
                file_metadata_id = &meta_id;
                change_flag = 0;
                output &work_lib..delta_mkt_accom_inventory;
            end;
        end;
        else do;
            rooms_sold = i_rooms_sold;
            arrivals = i_arrivals;
            departures = i_departures;
            cancellations = i_cancellations;
            no_shows = i_no_shows;
            room_revenue = i_room_revenue;
            food_revenue = i_food_revenue;
            total_revenue = i_total_revenue;
            snapshot_dttm = &snap_dttm;
            file_metadata_id = &meta_id;
            change_flag = 1;
            output &work_lib..mkt_accom_inventory_fin;
            output &work_lib..delta_mkt_accom_inventory;
        end;

        _iorc_=0;
        _error_=0;
    run;

    proc Sort data = &work_lib..mkt_accom_inventory_pace;
        by mkt_seg_id accom_type_id occupancy_dt;

    proc Sql;
        select count(*) into: mspace_ct from &work_lib..mkt_accom_inventory_pace;
    quit;

    %if &mspace_ct. > 0 and &loadtype. = BDE %then %do;
        %ideas_mspace;
    %end;

	%IF &loadtype EQ CDP AND %ideas_util_nobs(&work_lib..inbound_mktseg_cd) > 0 %THEN %DO;
		%ideas_ms_non_pace;
		
		/*Create Org table only if booked vs original feature is turned on*/
		%IF %upcase(&booked_org_enabled) EQ TRUE %THEN %DO;
			%ideas_ms_non_pace_org;
		%END;
	%END;

    

    data &work_lib..BDE_total_inventory
        &work_lib..total_inventory_pace (keep = property_id occupancy_dt snapshot_dttm total_accom_capacity rooms_sold rooms_sold rooms_not_avail_maint rooms_not_avail_other);
        set &work_lib..total_inventory (rename=(rooms_sold = i_rooms_sold total_accom_capacity=i_total_accom_capacity rooms_not_avail_maint=i_rooms_not_avail_maint rooms_not_avail_other=i_rooms_not_avail_other));
        modify &work_lib..BDE_total_inventory key = ttl_idx;

        *key is by property occupancy_dt room_type_id;
        if _iorc_=0 then do;
            if rooms_sold ne i_rooms_sold or total_accom_capacity ne i_total_accom_capacity or 
                rooms_not_avail_maint ne i_rooms_not_avail_maint or rooms_not_avail_other ne i_rooms_not_avail_other
                then do;
                rooms_sold = i_rooms_sold;
                total_accom_capacity=i_total_accom_capacity;
                rooms_not_avail_other=i_rooms_not_avail_other;
                rooms_not_avail_maint=i_rooms_not_avail_maint;
                snapshot_dttm = &snap_dttm;
                output &work_lib..total_inventory_pace;
            end;
        end;
        else do;
            rooms_sold = i_rooms_sold;
            total_accom_capacity=i_total_accom_capacity;
            rooms_not_avail_other=i_rooms_not_avail_other;
            rooms_not_avail_maint=i_rooms_not_avail_maint;
            snapshot_dttm = &snap_dttm;
            output &work_lib..total_inventory_pace;
        end;

        _iorc_=0;
        _error_=0;
    run;

    proc Append base=&work_lib..total_inventory_pace_fin
        data=&work_lib..total_inventory_pace force;
    run;

    data &work_lib..total_inventory_fin
        &work_lib..delta_total_inventory (keep = snapshot_dttm total_accom_capacity rooms_sold rooms_not_avail_maint rooms_not_avail_other 
        occupancy_dt property_id file_metadata_id arrivals departures cancellations no_shows
        room_revenue food_revenue total_revenue change_flag);
        set &work_lib..total_inventory (rename=(
            rooms_sold = i_rooms_sold 
            total_accom_capacity=i_total_accom_capacity
            rooms_not_avail_maint=i_rooms_not_avail_maint
            rooms_not_avail_other = i_rooms_not_avail_other 
            arrivals = i_arrivals
            departures = i_departures
            cancellations = i_cancellations
            no_shows = i_no_shows 
            room_revenue = i_room_revenue
            food_revenue = i_food_revenue
            total_revenue = i_total_revenue ));
        modify &work_lib..total_inventory_fin key = ttl_idx;

        *key is by property occupancy_dt room_type_id;
        if _iorc_=0 then do;
            if rooms_sold ne i_rooms_sold 
                or total_accom_capacity ne i_total_accom_capacity 
                or rooms_not_avail_maint ne i_rooms_not_avail_maint 
                or rooms_not_avail_other ne i_rooms_not_avail_other 
                or arrivals ne i_arrivals 
                or departures ne i_departures
                or cancellations ne i_cancellations 
                or no_shows ne i_no_shows 
                or room_revenue ne i_room_revenue
                or food_revenue ne i_food_revenue 
                or total_revenue ne i_total_revenue 
                then do;
                rooms_sold = i_rooms_sold;
                total_accom_capacity=i_total_accom_capacity;
                rooms_not_avail_maint=i_rooms_not_avail_maint;
                rooms_not_avail_other = i_rooms_not_avail_other;
                arrivals = i_arrivals;
                departures = i_departures;
                cancellations = i_cancellations;
                no_shows = i_no_shows;
                room_revenue = i_room_revenue;
                food_revenue = i_food_revenue;
                total_revenue = i_total_revenue;
                snapshot_dttm = &snap_dttm;
                file_metadata_id = &meta_id;
                change_flag = 1;
                replace &work_lib..total_inventory_fin;
                output &work_lib..delta_total_inventory;
            end;
            else do;
                rooms_sold = rooms_sold;
                total_accom_capacity=total_accom_capacity;
                rooms_not_avail_maint=rooms_not_avail_maint;
                rooms_not_avail_other = rooms_not_avail_other;
                arrivals = arrivals;
                departures = departures;
                cancellations = cancellations;
                no_shows = no_shows;
                room_revenue = room_revenue;
                food_revenue = food_revenue;
                total_revenue = total_revenue;
                snapshot_dttm = &snap_dttm;
                file_metadata_id = &meta_id;
                change_flag = 0;
                output &work_lib..delta_total_inventory;
            end;
        end;
        else do;
            rooms_sold = i_rooms_sold;
            total_accom_capacity=i_total_accom_capacity;
            rooms_not_avail_maint=i_rooms_not_avail_maint;
            rooms_not_avail_other = i_rooms_not_avail_other;
            arrivals = i_arrivals;
            departures = i_departures;
            cancellations = i_cancellations;
            no_shows = i_no_shows;
            room_revenue = i_room_revenue;
            food_revenue = i_food_revenue;
            total_revenue = i_total_revenue;
            snapshot_dttm = &snap_dttm;
            file_metadata_id = &meta_id;
            change_flag = 1;
            output &work_lib..total_inventory_fin;
            output &work_lib..delta_total_inventory;
        end;

        _iorc_=0;
        _error_=0;
    run;

    /*create list of unassigned market segments and accom types to be reported back to the bpms process*/
    proc Sql noprint;
        create table &work_lib..accom_type_unassigned as
            select Accom_Type_Code from tenant.Accom_Type a, tenant.Property c 
                where Accom_Class_ID =
                    (select Accom_Class_ID from tenant.Accom_Class ac where ac.Property_ID = a.Property_ID and
                        ac.Accom_Class_Code = 'Unassigned') and c.Property_ID = a.Property_ID = &property_id.;
    quit;

    /* DE1477 : PROD:New MS Alert - New MS alert is not generated */
    proc Sql noprint;
        create table &work_lib..mkt_seg_unassigned as
            select distinct ms.Mkt_Seg_Code 
                from tenant.Mkt_Seg ms 
                    LEFT join
                        tenant.Forecast_Group_Mkt_Seg_Proposed fgmsp
                        on ms.Mkt_Seg_ID = fgmsp.Mkt_Seg_ID 
                    LEFT JOIN 
                        tenant.Mkt_Seg_Details msd 
                        ON ms.Mkt_Seg_ID = msd.Mkt_Seg_ID 
                    where (fgmsp.Mkt_Seg_ID IS NULL and msd.Business_Type_ID IS NULL)
                        and ms.property_Id = &property_id.;
    quit;

        proc Sql noprint;
                    /* If group master is not present, use the tenant group master as the master record*/
                    create table &work_lib..all_master_groups as
                    select master_group_code, max(coalesce(master_group_id,1)) as max_offset , min(occupancy_DT) as min_occ_DT, max(occupancy_DT) as max_occ_DT
                        from tenant.group_master gm
                        inner join tenant.group_block gb on gm.group_id = gb.group_id
                        group by master_group_code;

                    create table &work_lib..groups_without_master as
                        select distinct group_code from &work_lib..gb where group_code not in (select group_code from &work_lib..gm);

                    insert into &work_lib..gm (group_code, group_name, group_description, group_status_code, start_dt, end_Date ,
                    group_type_code, booking_dt, pickup_type_code, cancel_dt, booking_type, sales_person, cut_off_date, cut_off_days, record_type_id, market_segment, prop_id)
                        select a.master_group_code, a.group_name, a.group_description, a.group_status_code, a.start_dt, a.end_DT ,
                        a.group_type_code, a.booking_dt, a.pickup_type_code, a.cancel_dt, a.booking_type, a.sales_person,
                        a.cut_off_date, a.cut_off_days, 13 ,ms.mkt_seg_code , a.property_id
                            from tenant.group_master a join &work_lib..all_master_groups b on a.master_group_code = b.master_group_code
                            and a.master_group_id = b.max_offset
                            join tenant.mkt_seg ms on ms.mkt_seg_id = a.mkt_seg_id
                    where a.master_group_code in (select group_code from &work_lib..groups_without_master);

            create table &work_lib..group_master as
                select b.property_id, a.group_code, a.group_name, a.group_description, group_code AS master_group_code, 1 AS MASTER_GROUP_ID,
                    a.group_status_code, a.start_dt, a.end_date as end_dt, a.group_type_code, a.booking_dt, a.pickup_type_code,
                    a.cancel_dt, a.booking_type, a.sales_person, a.cut_off_date, a.cut_off_days, b.mkt_seg_id
                from &work_lib..gm as a, tenant.mkt_seg as b
                    where a.market_segment = b.Mkt_Seg_Code and b.property_id=&property_id. order by a.group_code, a.booking_dt desc;

            create table &work_lib..group_master_system as
                select * from tenant.group_master as a
                    where a.property_id=&property_id.
                        order by group_code;
        quit;
        %if %upcase(&enableReusableGroupSrps.) eq TRUE %then %do;
            %ideas_type2_updated_gm;
        %end;

        proc Sort data=&work_lib..group_master_system nodupkey;
            by group_code;

        proc Sort data=&work_lib..group_master nodupkey;
            by group_code;
        run;

        data &work_lib..group_master;
            format cancel_dt cut_off_date start_Dt end_dt booking_dt date9.;
            merge &work_lib..group_master_system (in=gmsystemin) &work_lib..group_master (in=gmextractin);
            by group_code;
            group_new="N";

            if ( gmextractin=1 and gmsystemin=0) then
                group_new="Y";

            if ( gmextractin=1 ) then do;
                if master_group_id eq . then
                    master_group_id = 1;
                if master_group_code = '' then
                    master_group_code = group_code;

                if group_name = '' then
                    group_name = group_code;

                if group_description = '' then
                    group_description = group_code;

                if group_type_code = '' then
                    group_type_code = 'GROUP';
                /* Update the mkt_seg and cutoff date */
                output &work_lib..group_master;
            end;
            else do;
                if gmsystemin=1 then
                    output &work_lib..group_master;
            end;
        run;


    %if &use_group_past_data = FALSE %THEN %DO;
        /*Start of code to be toggled off for Past Group Data processing*/
        /*identify deltas for gm*/
        proc Sql;
            create table &work_lib..group_block as
                select a.rate, a.pickup, a.original_blocks, a.occupancy_dt, b.group_code, a.blocks, a.accom_type_id
                    from tenant.group_block as a, tenant.group_master as b
                        where a.group_id = b.group_id and b.property_id=&property_id. and a.occupancy_dt>&snap_date
                            order by b.group_code, a.accom_type_id, a.occupancy_dt;
        quit;

        proc Sql noprint;
            create index gb_idx on &work_lib..group_block(group_code, accom_type_id, occupancy_dt );
        quit;

        %if %sysfunc(exist(rm_data.BDE_group_block))=0  %then %do;

            proc Sql;
                create table rm_data.BDE_group_block as
                    select a.rate, a.pickup, a.original_blocks, a.occupancy_dt, b.group_code, a.blocks, a.accom_type_id
                        from tenant.group_block as a, tenant.group_master as b
                            where a.group_id = b.group_id and b.property_id=&property_id. and a.occupancy_dt>=&snap_date
                                order by b.group_code, a.accom_type_id, a.occupancy_dt;
            quit;

            proc Sql noprint;
                create index gb_idx on rm_data.BDE_group_block(group_code, accom_type_id, occupancy_dt );
            quit;

        %end;

        proc Sql;
            create table &work_lib..BDE_group_block as
                select *
                    from rm_data.BDE_group_block as a
         order by a.group_code, a.accom_type_id, a.occupancy_dt;
        quit;

        proc Sql noprint;
            create index gb_idx on &work_lib..BDE_group_block(group_code, accom_type_id, occupancy_dt );
        quit;

        proc Sql;
            create table &work_lib..gb1 as
                select a.rate, a.pickup, a.original_blocks, a.occupancy_dt, a.group_code, a.blocks, b.accom_type_id
                    from &work_lib..gb as a, tenant.accom_type as b
                        where a.accom_type_cd = b.accom_type_code and b.property_id=&property_id.
                            order by a.group_code, b.accom_type_id, a.occupancy_dt;
        quit;

        proc Sql;
            create table &work_lib..group_unique_extract_temp as
                select distinct a.group_code, b.accom_type_id, c.start_dt, c.end_date as end_dt from &work_lib..gb as a,
                    tenant.accom_type as b, &work_lib..gm as c
                where a.accom_type_cd = b.accom_type_code and b.property_id=&property_id. and a.group_code=c.group_code
                    order by a.group_code, b.accom_type_id;
        quit;

        data &work_lib..group_unique_extract (keep=group_code accom_type_id start_dt end_dt group_new );
            merge &work_lib..group_unique_extract_temp (in=groupblockin) &work_lib..group_master (in=groupmasterin);
            by group_code;

            if groupblockin=1 then
                output &work_lib..group_unique_extract;

        data &work_lib..group_exploded (keep=rate pickup original_blocks occupancy_dt blocks group_code accom_type_id group_new);
            retain group_code accom_type_id rate occupancy_dt blocks pickup original_blocks group_new;
            format occupancy_dt date9.;
            set &work_lib..group_unique_extract;
            explode_start_dt= max(start_dt,"&snap_date"-"&past_window_size");
            explode_end_dt= min(end_dt,"&snap_date"+"&future_window_size"+1);

            do date = explode_start_dt to explode_end_dt;
                rate=0;
                pickup=0;
                original_blocks=0;
                occupancy_dt=date;
                blocks=0;

                if date>&snap_date or group_new="Y" then
                    output &work_lib..group_exploded;
            end;
        run;

        proc Sort data= &work_lib..group_exploded;
            by group_code accom_type_id occupancy_dt;


        proc Sql;
            create table &work_lib..group_block_future_temp as
                select a.rate, a.pickup, a.original_blocks, a.occupancy_dt, a.group_id, b.group_code, a.blocks, a.accom_type_id, b.property_id,
                    b.start_dt ,b.end_dt, b.group_status_code as system_group_status_code, a.group_block_id
                from tenant.group_block as a, tenant.group_master as b
                    where a.group_id = b.group_id and b.property_id=&property_id. and
                        a.occupancy_dt > &snap_date and a.occupancy_dt >= b.start_dt
                    order by b.group_code, a.accom_type_id, a.occupancy_dt;
        quit;

        proc Sql;
            create table &work_lib..group_block_future as
                select a.*,b.group_status_code as extract_group_status_code
                    from &work_lib..group_block_future_temp as a, &work_lib..group_master as b
                        where a.group_code = b.group_code and b.property_id=a.property_id
                            order by b.group_code, a.accom_type_id, a.occupancy_dt;
        quit;

        data &work_lib..group_exploded1 (keep=rate pickup original_blocks occupancy_dt group_code blocks accom_type_id system_group_status_code extract_group_status_code );
            merge &work_lib..group_exploded (in=tranin) &work_lib..group_block_future (in=futin);
            by group_code accom_type_id occupancy_dt;

            if ( tranin=1 or futin=1) then do;
                output &work_lib..group_exploded1;
            end;

        data &work_lib..gb_extract_transactions (keep=rate pickup original_blocks occupancy_dt group_code blocks accom_type_id);
            merge &work_lib..group_exploded1 (in=tranin) &work_lib..gb1 (in=extin);
            by group_code accom_type_id occupancy_dt;

            if ( tranin=1 or extin=1) then do;
                if tranin=1 and occupancy_dt > &snap_date and
                    upcase(system_group_status_code) ne "CANCELLED" and upcase(extract_group_status_code) eq "CANCELLED" then do;
                    blocks=Pickup;
                end;
                else if tranin=1 and extin=0 and occupancy_dt > &snap_date then do;
                    blocks=0;
                    pickup=0;
                end;

                output &work_lib..gb_extract_transactions;
            end;

        proc Sql;
            create table &work_lib..gb_master_deletes as
                select c.rate, 0 as pickup, c.original_blocks, c.occupancy_dt, 0 as blocks, c.accom_type_id, b.group_id, b.group_code, c.group_block_id
                    from &work_lib..gm as a inner join tenant.group_master as b
                        on a.group_code = b.group_code , tenant.group_block as c
                    where ((b.group_id =c.group_id and b.property_id=&property_id. and c.occupancy_dt >= &snap_date ) and
                        (c.occupancy_dt < a.start_dt or c.occupancy_dt > a.end_date) )
                    order by b.group_code, c.accom_type_id, c.occupancy_dt;
        quit;

        data &work_lib..gb_extract_transactions (keep=rate pickup original_blocks occupancy_dt group_code blocks accom_type_id);
            merge &work_lib..gb_extract_transactions (in=tranin) &work_lib..gb_master_deletes (in=extin);
            by group_code accom_type_id occupancy_dt;

            if ( tranin=1 or extin=1) then do;
                output &work_lib..gb_extract_transactions;
            end;

        data &work_lib..gb_transactions (keep=rate pickup original_blocks occupancy_dt group_code blocks accom_type_id );
            set &work_lib..gb_extract_transactions;
        run;

        data &work_lib..group_block;
            set &work_lib..gb_transactions (rename=(
                rate = i_rate
                pickup=i_pickup
                original_blocks=i_original_blocks
                blocks = i_blocks  ));
            modify &work_lib..group_block key = gb_idx;

            if _iorc_=0 then do;
                if rate ne i_rate
                    or pickup ne i_pickup
                    or original_blocks ne i_original_blocks
                    or blocks ne i_blocks
                    then do;
                    rate = i_rate;
                    pickup=i_pickup;
                    original_blocks=i_original_blocks;
                    blocks = i_blocks;
                    replace &work_lib..group_block;
                end;
            end;
            else do;
                rate = i_rate;
                pickup=i_pickup;
                original_blocks=i_original_blocks;
                blocks = i_blocks;
                output &work_lib..group_block;
            end;

            _iorc_=0;
            _error_=0;
        run;

        /* Group Block Pace  */
        data &work_lib..BDE_group_block
            &work_lib..delta_group_block (keep = rate pickup original_blocks occupancy_dt group_code blocks
            accom_type_id snapshot_dttm file_metadata_id change_flag);
            set &work_lib..gb_transactions (rename=(
                rate = i_rate
                pickup=i_pickup
                original_blocks=i_original_blocks
                blocks = i_blocks  ));
            modify &work_lib..BDE_group_block key = gb_idx;

            if _iorc_=0 then do;
                if rate ne i_rate
                    or pickup ne i_pickup
                    or original_blocks ne i_original_blocks
                    or blocks ne i_blocks
                    then do;
                    rate = i_rate;
                    pickup=i_pickup;
                    original_blocks=i_original_blocks;
                    blocks = i_blocks;
                    snapshot_dttm = &snap_dttm;
                    file_metadata_id = &meta_id;
                    change_flag = 1;
                    output &work_lib..delta_group_block;
                end;
                else do;
                    rate = rate;
                    pickup=pickup;
                    original_blocks=original_blocks;
                    blocks = blocks;
                    snapshot_dttm = &snap_dttm;
                    file_metadata_id = &meta_id;
                    change_flag = 0;
                end;
            end;
            else do;
                rate = i_rate;
                pickup=i_pickup;
                original_blocks=i_original_blocks;
                blocks = i_blocks;
                snapshot_dttm = &snap_dttm;
                file_metadata_id = &meta_id;
                change_flag = 1;
                output &work_lib..delta_group_block;
            end;

            _iorc_=0;
            _error_=0;
        run;

        data &work_lib..pace_group_block;
            set &work_lib..delta_group_block;
            format Business_Day_End_Dt date9.;
            snapshot_dttm = &snap_dttm;

            if occupancy_dt le datepart(snapshot_dttm) - 1 then do;
                Business_Day_End_Dt = occupancy_dt;
            end;
            else do;
                Business_Day_End_Dt = datepart(snapshot_dttm) - 1;
            end;

            drop change_flag snapshot_dttm;
        run;

    %END;
    %ELSE %DO;
        /*New Macro Call*/
        %ideas_past_gb_deltas;
    %END;

%EXIT:

    %if &error_codes =918 %then %do;
        %ideas_util_report_errors(918, 'Error while inserting accomodation types or market_segment codes', '', &request_Id.,1);
        %let etl_status = 1;
    %end;

    %if &error_codes =919 %then %do;
        %ideas_util_report_errors(919, 'Failed to assign capacity to rdbms', '', &request_Id.,1);
        %let etl_status = 1;
    %end;

    proc Sql noprint;
        select count(*) into: error_ct from &work_lib..errors;
    quit;

    %if &error_ct. = 1 and &error_codes. =415 %then %do;
        %let error_ct = 0; /*this is because we write out the 415 error code to the log but we shouldn't fail because of it*/
    %end;

    %if "&GL_ABORT_FLAG" eq "1" or &syscc > 4 or &error_ct. > 0 %then %do;
        %let GL_ABORT_FLAG =1;
        %let etl_status = 1;
    %end;

    %let macroname=&SYSMACRONAME;

    %ideas_macro_log (start_end=END, macroname=&macroname.);
%mend ideas_type2_deltas_resumable;
%macro ideas_type2_updated_gm /store;
        /* we have last 2 days and a few future days */
        /* collect current offsets and maxOccDt */
        proc sql;
        create table &work_lib..master_group_entities as
        select gm.master_group_code, max_group_id as max_offset, min(occupancy_DT) as min_occupancy_DT from
        tenant.group_master gm join tenant.group_block gb on gm.group_id = gb.group_id
        join (
            select master_group_code,max(master_group_id) as max_group_id
            from tenant.group_master gm
            inner join tenant.group_block gb on gm.group_id = gb.group_id
            group by master_group_code
        ) A on gm.master_group_code=a.master_group_code and gm.master_group_id = a.max_group_id
        group by gm.master_group_code, max_group_id ;

        create table &work_lib..master_groups_nonzero as
        select master_group_code, max(master_group_id) as max_offset ,
            min(occupancy_DT) as min_occ_DT, max(occupancy_DT) as max_occ_DT
            from tenant.group_master gm
            inner join tenant.group_block gb on gm.group_id = gb.group_id
            where (blocks > 0 or pickup > 0)
            group by master_group_code;

        create table &work_lib..master_groups as
        select  a.master_group_code,coalesce(b.max_offset,a.max_offset) as max_offset, coalesce(b.min_occ_DT, a.min_occupancy_DT) as min_occ_DT,
            coalesce(b.max_occ_DT, a.min_occupancy_DT) as max_occ_DT
        from &work_lib..master_group_entities a left join &work_lib..master_groups_nonzero b on a.master_group_code = b.master_group_code;


        /* find future groups with fully departed references */

        create table &work_lib..future_groups_all as
        select gm.group_code,min(start_DT) as start_DT, min(end_Date) as end_DT, min(occupancy_DT) as min_occ_dt from
                     &work_lib..gm gm
                     inner join &work_lib..gb gb on gm.group_code = gb.group_code
                     group by gm.group_code ;

        create table &work_lib..future_groups_nonzero as
        select gm.group_code,min(start_DT) as start_DT, min(end_Date) as end_DT, min(occupancy_DT) as min_occ_dt from
                     &work_lib..gm gm
                     inner join &work_lib..gb gb on gm.group_code = gb.group_code
                     where (blocks > 0 or pickup > 0)
                     group by gm.group_code ;

        create table &work_lib..future_groups as
        select a.group_code, coalesce(b.start_DT,a.start_DT) as start_DT, coalesce(b.end_DT,a.end_DT) as end_DT,
                coalesce(b.min_occ_DT,a.min_occ_DT) as min_occ_DT
        from &work_lib..future_groups_all a
            left join &work_lib..future_groups_nonzero b on a.group_code = b.group_code;

        /* find if the tenant group_block has already departed */
        create table &work_lib..new_split_grps as
        select gm.master_group_code, max(gm.master_group_id)+1 as max_offset,
            min(fg.min_occ_DT) as start_DT
            from tenant.group_master gm
             inner join &work_lib..master_groups mg on gm.master_group_code = mg.master_group_code
                 and gm.master_group_id = mg.max_offset
             inner join &work_lib..future_groups fg on fg.group_code = gm.master_group_code
             where (mg.max_occ_DT+10) <= &snap_date and (mg.max_occ_DT+10) <= fg.min_occ_dt
             group by gm.master_group_code;
        quit;

        /*Update end date for the past occurrence of the new split group */
        proc sql;
            update &work_lib..group_master_system as gm set end_DT = (select max_occ_dt
                from &work_lib..master_groups as mg where mg.master_group_code = gm.master_group_code)
                where exists (select 1 from &work_lib..new_split_grps as nsg where nsg.master_group_code = gm.master_group_code
                    and nsg.max_offset-1 = gm.master_group_id);
        run;
        proc sql;
            /* Update group code for existing split groups */
            update &work_lib..group_master as gm1 set master_group_id = (select max(master_group_id)
                from tenant.group_master gm where gm.master_group_code = gm1.master_group_code and gm.master_group_id > 1),
                group_code = (select catx('_',max(master_group_code),put(max(master_group_id),best12.))
                     from tenant.group_master gm where gm.master_group_code = gm1.master_group_code and gm.master_group_id > 1),
                start_DT = (select max(gm.start_DT)
                     from tenant.group_master gm where gm.master_group_code = gm1.master_group_code and gm.master_group_id > 1),
                booking_DT = (select min(pacegm.business_day_end_DT)
                     from tenant.pace_group_master pacegm where pacegm.master_group_code = gm1.master_group_code and pacegm.master_group_id > 1)
                where exists (select 1 from tenant.group_master gm where
                     gm.master_group_id > 1 and gm1.master_group_code = gm.master_group_code);

            /* update start_DT for the future groups whose start_DT has changed */
            UPDATE &work_lib..group_master AS gm1
            SET start_DT =
              (SELECT min_occ_dt
               FROM &work_lib..future_groups fg
               WHERE fg.group_code = gm1.master_group_code)
            WHERE EXISTS
                (SELECT 1
                 FROM tenant.group_master gm
                 JOIN &work_lib..future_groups fg ON gm.master_group_code = fg.group_code
                 AND fg.min_occ_dt < gm.start_DT
                 WHERE gm.master_group_id > 1
                   AND gm1.master_group_code = gm.master_group_code);

            /* Update group code for newly split groups*/
            update &work_lib..group_master set master_group_id = (select max_offset
                from &work_lib..new_split_grps gm where gm.master_group_code = group_master.master_group_code),
                group_code = (select catx('_',master_group_code,put(max_offset,best12.))
                     from &work_lib..new_split_grps gm where gm.master_group_code = group_master.master_group_code),
                start_DT = (select max(gm.start_DT)
                     from &work_lib..new_split_grps gm where gm.master_group_code = group_master.master_group_code),
                booking_DT = &snap_date
                where exists (select 1 from &work_lib..new_split_grps gm where group_master.master_group_code = gm.master_group_code);

            /* Update split group code in extracts */
            update &work_lib..gm as gm set group_code = (select catx('_',master_group_code,put(master_group_id,best12.))
                from &work_lib..group_master where group_master.master_group_code = gm.group_code and group_master.master_group_id > 1)
                where exists (select 1 from &work_lib..group_master where group_master.master_group_code = gm.group_code and group_master.master_group_id > 1);
            update &work_lib..gb as gb set group_code = (select catx('_',master_group_code,put(master_group_id,best12.))
                from &work_lib..group_master where group_master.master_group_code = gb.group_code and group_master.master_group_id > 1)
                where exists (select 1 from &work_lib..group_master where group_master.master_group_code = gb.group_code and group_master.master_group_id > 1);

        quit;

%mend;