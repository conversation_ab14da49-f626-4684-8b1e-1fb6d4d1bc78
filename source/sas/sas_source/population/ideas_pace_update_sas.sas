%macro ideas_pace_update_sas(rc=)/store;
	%if %upcase(&Accom_Type_Option.)=STAYED AND %upcase(&preserveHotelAccomPace.)=FALSE %then
		%do;
			/* remove pace points based on first snapshot dt */
			proc sql;
				delete from rm_data.accom_inventory_pace 
					where datepart(capture_dttm) lt &first_snapshot_dt.;
			quit;

			Proc append base=rm_data.accom_inventory_pace data=&work_lib..accom_inventory force;

			proc sort data=rm_data.accom_inventory_pace;
				by property_id accom_type_id occupancy_dt capture_dttm;
			run;

		%end;

	%let ms_list=&updatemslist.;
	%let msi= 1;

	%do %while (%scan(&ms_list., &msi., |) ne);

		data _null_;
			call symputx ('ms',%scan(&ms_list., &msi, | ),'l');
		run;

		/* remove pace points based on first snapshot dt */
		proc sql;
			delete from rm_part.&market_segment_prefix._&ms.  
				where datepart(capture_dttm) lt &first_snapshot_dt.;
		quit;

		data &work_lib..ma_temp (drop=property_id mkt_seg_id);
			set &work_lib..mkt_accom_inventory (where=(mkt_seg_id = &ms.));
		run;

		proc append base=rm_part.&market_segment_prefix._&ms. data=&work_lib..ma_temp force;

		proc Sort data=rm_part.&market_segment_prefix._&ms.;
			by occupancy_dt accom_type_id capture_dttm;
		run;

		%let msi = %sysevalf(&msi. + 1);
	%end;

	%let ms_Los_list=&updatemslist.;
	%let mslosi = 1;

	%do %while (%scan(&ms_Los_list., &mslosi., | ) ne);

		data _null_;
			call symputx ('ms',%scan(&ms_los_list, &mslosi, | ),'l');
		run;

		/* remove pace points based on first snapshot dt */
		proc sql;
			delete from rm_part.&market_segment_los_prefix._&ms.   
				where datepart(capture_dttm) lt &first_snapshot_dt.;
		quit;

		data &work_lib..ma_los_temp (drop=property_id mkt_seg_id);
			set &work_lib..mkt_accom_los_inventory (where=(mkt_seg_id = &ms.));
		run;

		proc append base=rm_part.&market_segment_los_prefix._&ms. data=&work_lib..ma_los_temp force;
		run;

		proc Sort data=rm_part.&market_segment_los_prefix._&ms.;
			by arrival_dt accom_type_id los capture_dttm;
		run;

		%let mslosi = %sysevalf(&mslosi. + 1);
	%end;

	%if &syscc >4 %then
		%let return_code=200;
	%else 
		%if %sysfunc(exist(&work_lib..mkt_accom_inventory)) and %ideas_util_nobs(&work_lib..mkt_accom_inventory) > 0 and 
			%sysfunc(exist(&work_lib..mkt_accom_los_inventory)) and %ideas_util_nobs(&work_lib..mkt_accom_los_inventory) > 0 %then
		%let return_code=0;
	%else %let return_code=100;
%mend ideas_pace_update_sas;