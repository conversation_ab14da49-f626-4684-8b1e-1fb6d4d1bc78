%macro ideas_mspace_remove_fin_los/store;
%let macroname=&SYSMACRONAME; 
	
%ideas_macro_log (start_end=START, macroname=&macroname.);
    
	proc sql;
		select distinct mkt_seg_id into: list SEPARATED by '|' from &work_lib..delta_mkt_accom_los_inventory where change_flag = 1;
	quit;

	%if (%symexist(list)) %then %do;
		%let ms_list = &list.;
		%let i = 1;

		%do %while (%scan(&ms_list., &i., |) ne);

			data _null_;
				call symputx ('ms',%scan(&ms_list, &i, |),'l');
			run;

			%let dsn = Ma_los_&ms.;
			%let libdsn = rm_part.ma_los_&ms.;
            /*
                When both _ms_los and _ms_los_fin table exist, the _ms_los_fin table replaces the _ms_los tables.
                When stayed data population is discontinued, _fin table would not exist, but we wouldn't want to
                delete the old data
            */
			%if %sysfunc(exist(&libdsn)) eq 1 and %sysfunc(exist(&libdsn._fin)) eq 1 %then %do;

				proc datasets library=rm_part;
					delete &dsn.;
					change &dsn._fin = &dsn.;
				run;
				
			%end;

			%let i = %sysevalf(&i. + 1);
		%end;
	%end;
%let macroname=&SYSMACRONAME; 
	
%ideas_macro_log (start_end=END, macroname=&macroname.);
%mend ideas_mspace_remove_fin_los;
