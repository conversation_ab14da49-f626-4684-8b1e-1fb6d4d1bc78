%macro ideas_pace_checker_validate (rc=)/store;

	proc Sql;
		create table &work_lib..history_snapshot_dates as
			select snapshot_dt as snapshot_dt format=date9.                    
				from tenant.file_metadata
					where property_id = &property_id and record_type_id = &SummaryRecType. and  
						File_Name eq "PaceHistoryBuild";
	quit;

	proc Sql noprint;
		select min(snapshot_dt), max(snapshot_dt) into: min_capture_dt ,:max_capture_dt
			from &work_lib..history_snapshot_dates;
	quit;

	data &work_lib..Mkt_Pace_point_SAS_Only &work_lib..Mkt_Pace_point_Missing_DB &work_lib..Mkt_Pace_matches &work_lib..Mkt_Pace_differences;
		retain property_id occupancy_dt capture_dttm arrivals sas_arrivals departures sas_departures no_shows sas_no_shows cancellations sas_cancellations 
			Room_Revenue sas_Room_revenue rooms_sold sas_rooms_sold food_Revenue sas_Food_Revenue Total_Revenue sas_Total_Revenue Pace_Source;
		format Pace_Source $8.;
		merge &work_lib..mkt_inventory_pace (in=sasin) &work_lib..db_PACE_Mkt_Activity (in=g3in);
		by property_id Mkt_seg_id occupancy_dt capture_dttm;

		if datepart(capture_dttm) >= &min_capture_dt. and datepart(capture_dttm) <= &max_capture_dt. then
			Pace_Source="History";
		else Pace_Source="Daily";

		if sasin=1 and g3in=1 then
			do;
				if arrivals ne sas_arrivals or 
					departures ne sas_departures or 
					no_shows ne sas_no_shows or 
					cancellations ne sas_cancellations or
					rooms_sold ne sas_rooms_sold or  
					abs(round(Room_Revenue,.01) - round(sas_Room_revenue,.01)) >=1.00 or 
					abs(round(total_Revenue,.01) - round(sas_total_revenue,.01)) >=1.00 or 
					abs(round(food_Revenue,.01) - round(sas_food_revenue,.01)) >=1.00 then
					output &work_lib..Mkt_Pace_differences;
				else output &work_lib..Mkt_Pace_matches;
			end;
		else if sasin=1 and g3in=0 then
			do;
				if  
					datepart(capture_dttm) >= &min_capture_dt. and datepart(capture_dttm) <= &max_capture_dt. and datepart(capture_dttm) <=occupancy_dt then
					output &work_lib..Mkt_Pace_point_Missing_DB;
				else output &work_lib..Mkt_Pace_point_SAS_Only;
			end;
		else delete;
	run;

	%if %upcase(&marketSegmentValidationOnly.)=FALSE %then
		%do;

			data &work_lib..Total_Pace_point_SAS_Only &work_lib..Total_Pace_point_Missing_DB &work_lib..Total_Pace_matches &work_lib..Total_Pace_differences;
				retain property_id occupancy_dt capture_dttm arrivals sas_arrivals departures sas_departures no_shows sas_no_shows cancellations sas_cancellations 
					Room_Revenue sas_Room_revenue rooms_sold sas_rooms_sold food_Revenue sas_Food_Revenue Total_Revenue sas_Total_Revenue 
					total_accom_capacity sas_total_accom_capacity rooms_not_avail_maint sas_rooms_not_avail_maint 
					rooms_not_avail_other sas_rooms_not_avail_other Pace_Source;
				format Pace_Source $8.;
				merge &work_lib..total_inventory_pace (in=sasin) &work_lib..db_PACE_Total_Activity (in=g3in);
				by property_id occupancy_dt capture_dttm;

				if datepart(capture_dttm) >= &min_capture_dt. and datepart(capture_dttm) <= &max_capture_dt. then
					Pace_Source="History";
				else Pace_Source="Daily";

				if sasin=1 and g3in=1 then
					do;
						if arrivals ne sas_arrivals or 
							departures ne sas_departures or 
							no_shows ne sas_no_shows or 
							cancellations ne sas_cancellations or 
							rooms_sold ne sas_rooms_sold or 
							abs(round(Room_Revenue,.01) - round(sas_Room_revenue,.01)) >=1.00 or 
							abs(round(total_Revenue,.01) - round(sas_total_revenue,.01)) >=1.00 or 
							abs(round(food_Revenue,.01) - round(sas_food_revenue,.01)) >=1.00 or 
							total_accom_capacity ne sas_total_accom_capacity or 
							rooms_not_avail_maint ne sas_rooms_not_avail_maint or 
							rooms_not_avail_other ne sas_rooms_not_avail_other then
							do;
								output &work_lib..Total_Pace_differences;
							end;
						else output &work_lib..Total_Pace_matches;
					end;
					else if sasin=1 and g3in=0 then
			do;
				if  
					datepart(capture_dttm) >= &min_capture_dt. and datepart(capture_dttm) <= &max_capture_dt. and datepart(capture_dttm) <=occupancy_dt then
					output &work_lib..Total_Pace_point_Missing_DB;
				else output &work_lib..Total_Pace_point_SAS_Only;
			end;
					else delete;
			run;

			data &work_lib..Accom_Pace_point_SAS_Only &work_lib..Accom_Pace_point_Missing_DB &work_lib..Accom_Pace_matches &work_lib..Accom_Pace_differences;
				retain property_id occupancy_dt capture_dttm accom_type_id arrivals sas_arrivals departures sas_departures no_shows sas_no_shows cancellations sas_cancellations 
					Room_Revenue sas_Room_revenue rooms_sold sas_rooms_sold food_Revenue sas_Food_Revenue Total_Revenue sas_Total_Revenue 
					accom_capacity sas_accom_capacity rooms_not_avail_maint sas_rooms_not_avail_maint 
					rooms_not_avail_other sas_rooms_not_avail_other Pace_Source;
				format Pace_Source $8.;
				merge &work_lib..accom_inventory_pace (in=sasin) &work_lib..db_PACE_Accom_Activity (in=g3in);
				by property_id accom_type_id occupancy_dt capture_dttm;

				if datepart(capture_dttm) >= &min_capture_dt. and datepart(capture_dttm) <= &max_capture_dt. then
					Pace_Source="History";
				else Pace_Source="Daily";

				if sasin=1 and g3in=1 then
					do;
						if arrivals ne sas_arrivals or 
							departures ne sas_departures or 
							no_shows ne sas_no_shows or 
							cancellations ne sas_cancellations or 
							rooms_sold ne sas_rooms_sold or 
							abs(round(Room_Revenue,.01) - round(sas_Room_revenue,.01)) >=1.00 or 
							abs(round(total_Revenue,.01) - round(sas_total_revenue,.01)) >=1.00 or 
							abs(round(food_Revenue,.01) - round(sas_food_revenue,.01)) >=1.00 or 
							accom_capacity ne sas_accom_capacity or 
							rooms_not_avail_maint ne sas_rooms_not_avail_maint or 
							rooms_not_avail_other ne sas_rooms_not_avail_other then
							do;
								output &work_lib..Accom_Pace_differences;
							end;
						else output &work_lib..Accom_Pace_matches;
					end;

	else if sasin=1 and g3in=0 then
			do;
				if  
					datepart(capture_dttm) >= &min_capture_dt. and datepart(capture_dttm) <= &max_capture_dt. and datepart(capture_dttm) <=occupancy_dt then
					output &work_lib..Accom_Pace_point_Missing_DB;
				else output &work_lib..Accom_Pace_point_SAS_Only;
			end;
		else delete;
	run;

		%end;
%mend;