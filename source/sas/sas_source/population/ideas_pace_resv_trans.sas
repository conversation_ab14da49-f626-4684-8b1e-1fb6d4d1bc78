%macro ideas_pace_resv_trans(totalRateEnableForResNight, includePostDepForResNight) /store;

       %let offset = 1;
               %let inv_block_where_clause=;
               %if %upcase(&isRunningAsPartOfPMSRevamp.)=TRUE %then
                   %do;
                       %let offset = 2;
                       %let inv_block_where_clause=and missing(inv_block_code);
                   %end;
          proc sql;
            create table &work_lib..Transient_exploded as
                            select "Transient" as Transaction_Type format=$15.,a.occupancy_DT format date9., a.property_id as property_id length=4, a.reservation_identifier as reservation_identifier format=$50., a.individual_status,
                                min(a.booking_dt,a.Arrival_DT) as booking_dt format=date9., a.Arrival_DT, a.departure_dt, a.Departure_Dt-a.Arrival_DT as Number_Nights length=3 format=5.,1 as los, 1 as solds ,
                            case
                                when individual_status in ("XX","CX","CANCELLED") then max( min(a.booking_dt,a.Arrival_DT,&first_snapshot_dt.-&offset.), min(a.cancellation_dt,a.Arrival_DT,&first_snapshot_dt.-&offset.))
                                else .
                            end
                        as cancellation_dt format=date9.,
                            case
                                when individual_status in ("XX","CX","NS","NO_SHOW","NO SHOW","CANCELLED") OR &totalRateEnableForResNight. eq 1 then (a.rate_value)
                                else a.room_revenue
                            end
                        as Room_revenue length=8,
                            case
                                when individual_status in ("XX","CX","NS","NO_SHOW","NO SHOW","CANCELLED") then (a.rate_value)
                                else a.total_revenue
                            end
                        as total_revenue length=8,
                            a.accom_type_id as accom_type_id length=6, a.accom_type_id as stayed_accom_type_id length=6, a.mkt_seg_id as mkt_seg_id length=3, b.accom_type_id as booked_accom_type_id length=6,
                            a.Food_revenue, a.Beverage_revenue, a.Telecom_revenue, a.other_revenue, a.rate_value
                        from (select occupancy_dt, property_id, reservation_identifier, individual_status, booking_dt, arrival_dt, departure_dt,
                                 cancellation_dt, room_revenue, total_revenue, accom_type_id, mkt_seg_id, Food_revenue, beverage_revenue,
                              	telecom_revenue, other_revenue, rate_value, booked_accom_type_code, Rate_Code, rn.Inv_Block_Code
                              from tenant.Reservation_Night rn
                              union
                              select pdr.occupancy_dt, property_id, pdr.reservation_identifier, individual_status, booking_dt, arrival_dt, departure_dt,
                              	cancellation_dt, pdr.room_revenue, pdr.total_revenue, pdr.accom_type_id, pdr.mkt_seg_id, pdr.Food_revenue, beverage_revenue,
                              	telecom_revenue, pdr.other_revenue, pdr.rate_value, rn.booked_accom_type_code, pdr.Rate_Code, rn.Inv_Block_Code
                             from tenant.post_departure_revenue pdr
                              left join (select distinct Reservation_Identifier, property_id, individual_status, Booking_DT, Arrival_DT, Departure_DT,
                                         Cancellation_DT, Telecom_Revenue, Beverage_Revenue, booked_accom_type_code, Rate_Code, Inv_Block_Code
                              		     from tenant.Reservation_Night) rn
                              on pdr.reservation_identifier=rn.Reservation_Identifier
                             where &includePostDepForResNight.=1) as a
                        inner join tenant.accom_type as ta
                            on a.accom_type_id = ta.accom_type_id
                        left join &work_lib..accom_types as b
                                on a.booked_accom_type_code=b.accom_type_code
                            where ta.status_id <> 6 and a.booking_dt<&first_snapshot_dt.-1 and   ((Arrival_DT between &minimum_occupancy_dt. and &maximum_occupancy_dt. ) or
                                                                           (departure_Dt between &minimum_occupancy_dt. and &maximum_occupancy_dt. ))
                                                                            &mktSegWhereClause.
                                                                            &inv_block_where_clause.
                                                                            &canceledReservationsWhere.
                                order by a.reservation_identifier, a.Arrival_DT;
           run;


        /* Update Individual status code in case of v3 reservations*/
        data &work_lib..Transient_exploded(drop=old_status);
            set &work_lib..Transient_exploded(rename=(individual_status=old_status));
                if (old_status = 'CANCELLED') then individual_status='XX';
                else if (old_status = 'CHECKED OUT') then individual_status='CO';
                else if (old_status = 'CHECKED_OUT') then individual_status='CO';
                else if (old_status = 'NO_SHOW') then individual_status='NS';
                else if (old_status = 'NO SHOW') then individual_status='NS';
                else if (old_status = 'IN_HOUSE') then individual_status='CI';
                else if (old_status = 'CHECKED IN') then individual_status='CI';
                else if (old_status = 'RESERVED') then individual_status='SS';
                else if (old_status = 'WALKED GUEST') then individual_status='WG';
                else if (length(old_status) = 2) then individual_status=old_status;
                else individual_status = 'SS';
        run;

        data &work_lib..Transactions_exploded;
            set &work_lib..Transient_exploded &group_trans_ds.;
        run;

        PROC SQL;
            DROP TABLE &work_lib..Group_Transactions_exploded;
        QUIT;

        %if %upcase(&RequestedRoomTypeOption.)=ALL and %upcase(&Accom_Type_Option.)=BOOKED %then
        %do;

            Data &work_lib..Transactions_exploded;
                set &work_lib..Transactions_exploded;

                if booked_accom_type_Id ne . and booked_accom_type_Id ne stayed_accom_type_id then
                    do;
                        accom_type_id=booked_accom_type_id;
                    end;
            run;

        %end;
%mend ideas_pace_resv_trans;
