%macro ideas_type3_validation(lib=in_xml)/store;
	%local sqlrc  rm_data;
	%global work_lib work_dir;
	%global etl_status err_str err_ct file_references property_desc property_desc_short;
	%let etl_status = 0;
	%let maxerrors = .;
	%let app_start_time=%sysfunc(datetime());
	%let runTime=0;
	%PUT ---------------------------------------------------------------------;
	%PUT --------------------- Starting Type 3 Validation --------------------;
	%PUT ---------------------------------------------------------------------;
	%PUT;

	data _null_;
		set  &lib..requestHeader;
		call symputx('property_Id',propertyId,'l');
		call symputx('requestId',requestId,'l');
		call symputx('tenant_server',tenant_server,'l');
		call symputx('server_instance',tenant_server_instance,'l');
		call symputx('tenant_pwd',tenant_password,'l');
		call symputx('tenant_user',tenant_user,'l');
		call symputx('tenant_db',tenant_database,'l');
		call symputx('port',tenant_db_port,'l');
		call symputx('saspath',dataset_path,'l');
		stop;
	run;

	%if &syscc > 4 %then
		%do;
			%ideas_util_report_errors(910, 'Failed to read request header', "&property_desc.", &request_Id.,1);
			%let etl_status = 1;
			%goto EXIT;
		%end;

	%if &work_library.=work %then
		%do;
			%let work_lib=work;
			%let work_path=%sysfunc(pathname(work));
		%end;
	%else
		%do;
			%let work_lib=&work_library.;
			%let work_path_drive=%substr (%sysfunc(pathname(work)),1,2);
			%let work_path_root=&work_path_drive./sas;

			%ideas_util_create_dir(dir=temp,base_path=&work_path_root.);
			%let work_path_root=&work_path_drive./sas/temp;

			%ideas_util_create_dir(dir=&property_id.,base_path=&work_path_root.);
			%let work_path_root=&work_path_drive./sas/temp/&property_id.;

			%ideas_util_create_dir(dir=work,base_path=&work_path_root.);
			%let work_path=&work_path_drive./sas/temp/&property_id./work;

			%if &memlib_option. = memlib %then
				%do;
					libname &work_lib "&work_path." memlib;
				%end;
			%else
				%do;
					libname &work_lib "&work_path.";
				%end;
		%end;

	%let sas_path = %str(&saspath.);
	%let request_id = %str(&requestid.);

	data &work_lib..errors;
		length err_ct error_cd 8 request_id error_params error_message $ 200;
		stop;
	run;

	data _null_;
		set  &lib..populationrequest;
		call symputx('etl_file_name',etlfilename,'l');
		call symputx('maxErrors',maxErrors,'l');
		call symputx('logpath',logfilepath,'l');
		call symputx('percentnumber',percentnumber,'l');
		stop;
	run;

	%put &percentnumber. &maxErrors.;

	%if &syscc > 4 %then
		%do;
			%ideas_util_report_errors(909, 'Failed to read Type Three Validation request', "&property_desc.", &request_Id.,1);
			%let etl_status = 1;
			%goto EXIT;
		%end;

	%let mylog_path = %str(&file_references_root.);

	/* Code added to retry for 15 times as on prod we are getting file not found error */
	%let i = 1;

	%do i = 1 %to 15;
		%if %sysfunc(fileexist("&etl_file_name.")) %then
			%do;
				%put The etl file "&etl_file_name" exists.;
				%goto TYPE3_FILE_EXISTS;
			%end;
		%else
			%do;
				%put The etl file "&etl_file_name" does not exist. Will retry.;

				data _null_;
					slept= sleep(1);
				run;

				%put The Type Three validation program paused for "&i" seconds;

				%if &i = 15 %then
					%do;
						%put "15 retry attemps failed.. The type three file does not exist";

						%ideas_util_inc_errors;
						%ideas_util_report_errors(940, 'ERROR:Type Three file does not exist hence aborting', '',&req_Id,'');
						%let error_str = %str(ERROR:Type Three file does not exist hence aborting);
						%let etl_status = 1;
						%let syscc=6;
						%goto EXIT;
					%end;
			%end;
	%end;

%TYPE3_FILE_EXISTS:

	/* Code added to retry for 15 times as on prod we are getting file not found error */
	/* Read in datafile to get the size of the file to determine the number of errors that will be reported */
	data &work_lib..count (keep=counter percent maxerrors);
		retain counter 0;
		retain nobs 0;
		infile "&etl_file_name." delimiter='|' missover dsd end=last;
		input @1 record_type $7. @;
		nobs+1;

		if record_type = '_TRANS_' then
			counter+1;

		if last then
			do;
				percent =&percentnumber./100;
				maxerrors = max(1,round((counter * percent),1));
				call symput('maxerrors',maxerrors);
				call symput ('counter', counter);
				output &work_lib..count;
			end;
	run;

	%put &counter. &maxerrors.;

	%if &maxerrors. = . %then
		%do;
			%ideas_util_report_errors(901, 'Failed to read inbound ETL file', '', &request_Id., 1);

			%if &etl_status = 1 %then
				%goto EXIT;
		%end;

	%ideas_connect_tenant (&tenant_db., &tenant_server., &port., &server_instance., &tenant_user., &tenant_pwd.);

	%if &etl_status = 1 %then %goto EXIT; /*Connection failed - Abort*/

		proc sql noprint;
			select property_id into : pid from tenant.property where property_id = &property_id. and status_id=1;
			select property_code into: pcode from tenant.property where property_id = &property_id. and status_id=1;
			select property_name into :property_name from tenant.property where property_Id=&property_id. and status_id=1;
		quit;

		%if not %symexist(pid) %then
			%do;
				%let property_desc=%str(Property: %trim(&property_id.) does not exist in the %trim(&tenant_db.) database);
				%let property_desc_short=%str(Property: %trim(&property_id.) does not exist in the %trim(&tenant_db.) database);

				%ideas_util_inc_errors;
				%ideas_util_report_errors(913, "&property_desc.", '', &request_Id., &err_ct.);
				%let etl_status = 1;
			%end;
		%else
			%do;
				%let property_desc=%str(Property: %trim(&property_id.)  -  %trim(&pcode.));
				%let property_desc_short=%str(Property %trim(&property_id.)  -  %trim(&pcode.));
			%end;

		proc sql noprint;
			select record_type_id into: RecType from tenant.record_type where upcase(record_type_name) = 'INDTRANS';
		quit;

		%if not %symexist(RecType) %then
			%do;
				%ideas_util_inc_errors;
				%ideas_util_report_errors(912, "%str(Record type INDTRANS does not exist in the %trim(&tenant_db.) database)",  "&property_desc.", &request_Id., &err_ct.);
				%let etl_status = 1;
			%end;

		/* If the property does not exist in the file metadata set the default dtttm to 0 so the file will be processed
		f_file indicates if it is the first file processed = 0 = false 1 = true
		Do the property lookup again, this time in the file_metadata table to see if an extract has ever been processed;*/
		proc sql noprint;
			select property_id into:  tpid from tenant.file_metadata
				where property_id = &property_id. and record_type_id = &RecType.;
		quit;

		%local f_file;
		%let f_file = 0;

		%if not %symexist(tpid) %then
			%do;

				data &work_lib..ssdt;
					attrib ssnap_dt format = date9.;
					attrib sssnap_tm format = time10.;
					ssnap_dt = 0;
					ssnap_tm = 0;
					sssnap_tm = 0;
					isbde = 1;
					process_status_id = .;
				run;

				data &work_lib..spdt;
					attrib sprep_dt format = date9.;
					attrib ssprep_tm format = time10.;
					sprep_dt = 0;
					sprep_tm = 0;
					ssprep_tm = 0;
					process_status_id = .;
				run;

				%let f_file = 1;
			%end;
		%else
			%do;
				/* Get the max system/prepared dates from the global file to be compared against the input file for validation;*/
				proc sql;
					create table &work_lib..ssdt as
						select distinct snapshot_dt as ssnap_dt, max(snapshot_tm) as ssnap_tm, put(max(snapshot_tm),time10.) as sssnap_tm, isbde, process_status_id
							from tenant.file_metadata
								where property_id = &property_id and record_type_id = &rectype
									and (select max(snapshot_dt) from tenant.file_metadata where property_id = &property_id and record_type_id = &rectype) = snapshot_dt;
				quit;

				proc sql;
					create table &work_lib..spdt as
						select distinct prepared_dt as sprep_dt, max(prepared_tm) as sprep_tm, put(max(prepared_tm),time10.) as ssprep_tm
							from tenant.file_metadata
								where property_id = &property_id and record_type_id = &rectype
									and (select max(prepared_dt) from tenant.file_metadata where property_id = &property_id and record_type_id = &rectype) = prepared_dt;
				quit;

			%end;

		data &work_lib..dates;
			set &work_lib..ssdt;
			set &work_lib..spdt;
		run;

		data  &work_lib..dates;
			set  &work_lib..dates;
			ssnap_dttm=input(put(ssnap_dt,date9.)||':'||trim(left(ssnap_tm)),datetime20.);
			sprep_dttm=input(put(sprep_dt,date9.)||':'||trim(left(sprep_tm)),datetime20.);
		run;

		/* Create temp tables to store the hash output */
		data &work_lib..res_val;
			attrib  reservation_identifier format=$50.;
		run;

		data &work_lib..file_metadata (keep=record_type client_code property_id past_window_size future_window_size
			snapshot_date snapshot_time prepared_date prepared_time)
			&work_lib..trans (keep=record_type reservation_identifier individual_status arrival_dt departure_dt booking_dt cancellation_dt
			booked_accom_type_code accom_type_cd market_segment room_revenue food_revenue beverage_revenue telecom_revenue
			other_revenue total_revenue source_booking nationality rate_code rate_value room_number booking_type
			number_children number_adults)
			&work_lib..badrecs (keep=record_type)
			&work_lib..failure (keep = error_cd error_message error_params request_id err_ct);
			retain snapshot_date prepared_date_temp snapshot_date_temp past_window_size future_window_size;
			length error_cd 8 error_params error_message request_id $200;
			attrib record_type format=$20.;
			format snapshot_date date9.;
			attrib accom_type_cd format=$50.;
			attrib booked_accom_type_code format=$50.;
			attrib record_type format=$20.;
			attrib reservation_identifier format=$50.;
			attrib arrival_dt format=date9.;
			attrib departure_dt format=date9.;
			attrib booking_dt format=date9.;
			attrib cancellation_dt format=date9.;

			if _n_ = 1 then
				do;
					retain err_ct &err_ct.;
				end;

			infile "&etl_file_name" delimiter='|' missover dsd;

			if _n_=2 then
				do;
					/* Below hashes to validate uniqueness of reservation id */
					declare hash res_val( dataset: "&work_lib..res_val");
					res_val.defineKey('reservation_identifier');
					res_val.defineDone();
					input record_type $ client_code $ property_id $ past_window_size 
					future_window_size snapshot_date : yymmdd10. snapshot_time $ prepared_date : yymmdd10. prepared_time $;

					if sum( missing(record_type),missing(client_code),missing(property_id),
						missing(past_window_size),missing(future_window_size),
						missing(snapshot_date),missing(snapshot_time),
						missing(prepared_date),missing(prepared_time) ) > 0 then
						do;
							error_cd = 421;
							error_message = 'Missing universal field on metadata record';
							error_params = _n_;
							request_id = "&request_id";
							err_ct + 1;
							output &work_lib..failure;
							call symputx('etl_status',1,'l');
						end;
					else
						do;
							tmp_dt=put(snapshot_date,date9.)||':'||trim(left(snapshot_time));
							snap_dttm=input(tmp_dt,datetime20.);
							tmp_dt=put(prepared_date,date9.)||':'||trim(left(prepared_time));
							prepared_dttm=input(tmp_dt,datetime20.);
							snapshot_date_temp=put(snapshot_date,worddate12.);
							prepared_date_temp=put(prepared_date,worddate12.);
							call symput('snapshot_date',snap_dttm);
							call symput('prepared_date',prepared_dttm);
							call symput('snap_date', compress(snapshot_date_temp));
							call symput('snap_time', compress(snapshot_time));
							call symput('prep_date', compress(prepared_date_temp));
							call symput('prep_time', compress(prepared_time));
							call symput('snap_dttm', compress(snap_dttm));
							call symput('prepared_dttm', compress(prepared_dttm));
							call symput ('past_window_size', compress (past_window_size));
							call symput ('future_window_size', compress (future_window_size));
							call symput ('property_cd', property_id);
							call symput('prep_dttm',prepared_dttm);
							output &Work_lib..file_metadata;
						end;
				end;
			else
				do;
					input record_type $ @;

					if record_type='_TRANS_' then
						do;
							INPUT reservation_identifier $ individual_status: $2. arrival_dt : YYMMDD10. departure_dt : YYMMDD10.
								booking_dt :YYMMDD10. cancellation_dt : YYMMDD10. booked_accom_type_code : $50. accom_type_cd : $50.
								market_segment: $50. room_revenue food_revenue beverage_revenue telecom_revenue  other_revenue
								total_revenue  source_booking: $50. nationality: $2. rate_code: $50. rate_value  room_number : $50. 
								booking_type: $50. number_children number_adults;
							booking_dt_temp=put(booking_dt,worddate12.);
							departure_dt_temp=put(departure_dt,worddate12.);
							arrival_dt_temp=put(arrival_dt,worddate12.);

							if sum(missing(record_type),missing(reservation_identifier), missing(individual_status), missing(arrival_dt), missing(departure_dt),
								missing(booking_dt), missing(accom_type_cd), missing(market_segment), missing(room_revenue), missing(total_revenue)) > 0 then
								do;
									if record_type=. then
										columnName='record_type';
									else if reservation_identifier=.          then
										columnName='reservation_identifier';
									else if individual_status=.   then
										columnName='individual_status';
									else if arrival_dt=.  then
										columnName='arrival_dt';
									else if departure_dt=. then
										columnName='departure_dt';
									else if booking_dt=.            then
										columnName='booking_dt';
									else if accom_type_cd=.              then
										columnName='accom_type_cd';
									else if market_segment=.            then
										columnName='market_segment';
									else if room_revenue=.          then
										columnName='room_revenue';
									else if total_revenue=.         then
										columnName='total_revenue';
									error_message = 'TRANS data has missing required values for field: '||columnName||'   Extract Row: '||left(_n_);
									error_cd = 422;
									error_params =  "&property_desc.";
									request_id = "&request_id";
									err_ct + 1;
									output &work_lib..failure;
								end;
							else
								do;
									if arrival_dt < snapshot_date - past_window_size then
										do;
											error_cd = 446;
											error_params = "&property_desc.";
											error_message = 'TRANS data arrival date: ' ||arrival_dt_temp||' is less than snapshot date: '||snapshot_date_temp||' minus past window size: '||left(past_window_size)||'   Extract Row: '||left(_n_);
											request_id = "&request_id";
											err_ct + 1;
											output &work_lib..failure;
											call symputx('etl_status',1,'l');
										end;

									if arrival_dt > snapshot_date + future_window_size then
										do;
											error_cd = 447;
											error_params =  "&property_desc.";
											error_message = 'TRANS data arrival date: ' ||arrival_dt_temp||' is greater than snapshot date: '||snapshot_date_temp||' plus future window size: '||left(future_window_size) ||'   Extract Row: '||left(_n_);
											request_id = "&request_id";
											err_ct + 1;
											output &work_lib..failure;
											call symputx('etl_status',1,'l');
										end;

									if departure_dt < arrival_dt then
										do;
											error_cd = 442;
											error_message = 'TRANS data departure date: ' ||departure_dt_temp||' is less than arrival date: '||arrival_dt_temp||'   Extract Row: '||left(_n_);
											error_params = "&property_desc.";
											request_id = "&request_id";
											err_ct + 1;
											output &work_lib..failure;
											call symputx('etl_status',1,'l');
										end;

									if booking_dt > arrival_dt then
										do;
											error_cd = 443;
											error_message = 'TRANS data booking date: ' ||booking_dt_temp||' is greater than arrival date: '||arrival_dt_temp||'   Extract Row: '||left(_n_);
											error_params =  "&property_desc.";
											request_id = "&request_id";
											err_ct + 1;
											output &work_lib..failure;
											call symputx('etl_status',1,'l');
										end;

									if upcase(individual_status) eq 'XX' or upcase(individual_status) eq 'CX' then
										do;
											if cancellation_dt eq . then
												do;
													error_cd = 444;
													error_message = 'TRANS data missing required cancellation date.  Extract Row: '||left(_n_);
													error_params =  "&property_desc.";
													request_id = "&request_id";
													err_ct + 1;
													output &work_lib..failure;
													call symputx('etl_status',1,'l');
												end;
										end;
									else
										do;
											output &work_lib..trans;

											if res_val.find() ne 0 then
												do;
													res_val.add();
												end;
											else
												do;
													error_cd = 448;
													error_message = 'TRANS data duplicate reservation id: ' ||trim(reservation_identifier)||'  Extract Row: '||left(_n_);
													error_params = "&property_desc.";
													request_id = "&request_id";
													err_ct + 1;
													output &work_lib..failure;
													call symputx('etl_status',1,'l');
												end;
										end;
								end;
						end;  /*** end TRANS checks ***/
					else if record_type='_FOOTER_' then
						do;
						end;
					else if record_type not in ('_HEADER_', '_FOOTER_', '_TRANS_', '') then
						do;
							error_cd = 440;
							error_message = 'Bad record types found in ETL file.  Extract Row: '||left(_n_);
							error_params =  "&property_desc.";
							request_id = "&request_id";
							err_ct = err_ct + 1;
							output &work_lib..failure;
							output &work_lib..badrecs;
						end;
				end;

			if err_ct ge &maxerrors. then
				do;
					call symputx('etl_status',1,'l');
					stop;
				end;
		run;

		%if &syscc > 4 %then
			%do;
				%ideas_util_inc_errors;
				%ideas_util_report_errors(903, 'Error importing ETL file',  "&property_desc.", &request_Id., &err_ct.);
				%let etl_status = 1;
			%end;

		%put max errors &maxerrors.;

		%if &maxerrors. ne -1 %then
			%do;
				%ideas_util_inc_errors;

				%if &err_ct. -1 >= &maxerrors. %then
					%do;
						%ideas_util_report_errors(501, "Max errors of &maxerrors. has been reached", "&property_desc.", &request_Id., &err_ct.);
						%let etl_status = 1;
					%end;
			%end;

		%if &maxErrors. ne -1 %then
			%do;

				proc sql inobs = &maxErrors.;
					create table &work_lib..failure as select * from &work_lib..failure;
				quit;

			%end;

		proc append base= &work_lib..errors data= &work_lib..failure;
		run;

		%if not %symexist(tpid) %then
			%do;

				data &work_lib..ssdt;
					attrib ssnap_dt format = date9.;
					attrib sssnap_tm format = time10.;
					ssnap_dt = 0;
					ssnap_tm = 0;
					sssnap_tm = 0;
					isbde = 1;
					process_status_id = .;
				run;

				data &work_lib..spdt;
					attrib sprep_dt format = date9.;
					attrib ssprep_tm format = time10.;
					sprep_dt = 0;
					sprep_tm = 0;
					ssprep_tm = 0;
					process_status_id = .;
				run;

				%let f_file = 1;
			%end;
		%else
			%do;
				/* Get the max system/prepared dates from global file and compare against the input file for validation */
				proc sql;
					create table &work_lib..ssdt as
						select distinct snapshot_dt as ssnap_dt, max(snapshot_tm) as ssnap_tm, put(max(snapshot_tm),time10.) as sssnap_tm, isbde, process_status_id
							from tenant.file_metadata
								where property_id = &property_id and record_type_id = &rectype
									and (select max(snapshot_dt) from tenant.file_metadata where property_id = &property_id and record_type_id = &rectype) = snapshot_dt;
				quit;

				proc sql;
					create table &work_lib..spdt as
						select distinct prepared_dt as sprep_dt, max(prepared_tm) as sprep_tm, put(max(prepared_tm),time10.) as ssprep_tm
							from tenant.file_metadata
								where property_id = &property_id and record_type_id = &rectype
									and (select max(prepared_dt) from tenant.file_metadata where property_id = &property_id and record_type_id = &rectype) = prepared_dt;
				quit;

			%end;

		data &work_lib..fdates;
			set &Work_lib..file_metadata 
				(keep=snapshot_date snapshot_time prepared_date prepared_time);
			snap_tm = input(snapshot_time,time10.);
			prep_tm = input(prepared_time,time10.);
			snap_dttm = input(put(snapshot_date,date9.)||':'||trim(left(snapshot_time)),datetime20.);
			prep_dttm = input(put(prepared_date,date9.)||':'||trim(left(prepared_time)),datetime20.);
		run;

		data  &work_lib..dates;
			set &work_lib..dates;
			set &work_lib..fdates;
		run;

		data &work_lib..failure (keep = error_cd error_message error_params request_id err_ct);
			length error_cd 8 error_params error_message request_id $200;
			set &work_lib..dates;
			format snapshot_date date9.;
			extract_snap_dttm=put(snap_dttm,DATETIME18.);
			extract_prep_dttm=put(prep_dttm,DATETIME18.);
			system_ssnap_dttm=put(ssnap_dttm,DATETIME18.);
			system_sprep_dttm=put(sprep_dttm,DATETIME18.);

			if snap_dttm <  ssnap_dttm then
				do;
					error_cd = 412;
					error_message = 'Extract Snapshot date/time: '||extract_snap_dttm||'  is less than system Snapshot date/time: '||system_ssnap_dttm;
					request_id = "&request_id";
					error_params =  "&property_desc.";
					err_ct + 1;
					output &work_lib..failure;
					call symputx('etl_status',1,'l');
				end;
		run;

		proc append base= &work_lib..errors data= &work_lib..failure;
		run;

		%if %ideas_util_nobs(&work_lib..file_metadata) > 0 %then
			%do;
				%if &pcode. ne &property_cd. %then
					%do;
						%ideas_util_inc_errors;
						%ideas_util_report_errors(409, "%str(Extract file Property Code: %trim(&pcode.) does not match %trim(&tenant_db.) property code: %trim(&property_cd.))",  "&property_desc.", &request_Id., &err_ct.);
						%let etl_status = 1;
					%end;

				%if &past_window_size. < 0  %then
					%do;
						%ideas_util_inc_errors;
						%ideas_util_report_errors(410, "%str(Past window size: %left(&past_window_size.) required to be greater or equal 0 )", "&property_desc.", &request_Id., &err_ct.);
						%let etl_status = 1;
					%end;

				%if &future_window_size. < 0 or &future_window_size. > 9999 %then
					%do;
						%ideas_util_inc_errors;
						%ideas_util_report_errors(411, "%str(Future window size: %left(&future_window_size.) required to be greater or equal 0 and less than 10000)", "&property_desc.", &request_Id., &err_ct.);
						%let etl_status = 1;
					%end;
			%end;

%EXIT:
		%let outname = .;

		%if "&GL_ABORT_FLAG" eq "1" or &syscc > 4 or &etl_status = 1 %then
			%do;
				%let GL_ABORT_FLAG =1;
				%let syscc = 5;
				%let etl_status = 1;
			%end;

		%let  prepared_dttm_temp=%sysfunc(putn(&prep_dttm,datetime20.));
		%let  snapshot_dttm_temp=%sysfunc(putn(&snap_dttm,datetime20.));

		data &work_lib..errors;
			set &work_lib..errors;
			where error_cd ne 0;
		run;

		%if &maxerrors. ne -1 %then
			%do;
				%ideas_util_inc_errors;

				%if &err_ct. -1 = &maxerrors. %then
					%do;
						%ideas_util_report_errors(501, "Maximum number errors of &maxerrors. has been reached", "&property_desc.", &request_Id., &err_ct.);
						%let etl_status = 1;
					%end;
			%end;

		proc sql noprint;
			select count(*) into: errcount from &work_lib..errors;
		quit;

		%if &errcount. > 0 %then
			%do;

				data _null_;
					call scan("&etl_file_name",-1,fn_pos,fn_length,'\');
					fname = substrn("&etl_file_name",fn_pos,fn_length-4);
					call symputx('fname',fname,'l');
				run;

				%let outname = %str(&mylog_path./validation_errorlog_&fname..txt);
				%let file_references = &file_references.|&outname;

				data _null_;
					set &work_lib..errors;
					where error_cd ne 0;
					FILE  "&outname.";

					If _N_ = 1 then
						do;
							Put '-------------------------------------- Type Three Validation Summary ---------------------------------------';
							put ' ';
							Put "      Property Info: &property_desc.";
							put "Snap Shot Date/Time: &snapshot_dttm_temp.";
							put " Prepared Date/Time: &prepared_dttm_temp.";
							put "   Past Window Size: &past_window_size.";
							put " Future Window Size: &future_window_size.";
							put "Validation ETL File: &etl_file_name.";
							put "         Request ID: &requestId.";
							put ' ';
							Put '-------------------------------------- Type Three Validation Errors -----------------------------------------';
							put ' ';
							Put 'Error Code --------------------------- Error Description --------------------------------------------------------------';
							put ' ';
						end;

					PUT  @4  error_cd @ 12 error_message;
				run;

			%end;

        %if "&GL_ABORT_FLAG" eq "1" or &syscc > 4 or &etl_status = 1 %then
        %do;
            data _NULL_;
                FILE resXml;
                put '<?xml version="1.0" encoding="UTF-8"?>';
                put '<SASResponse xmlns="http://xml.common.pacman.tetris.ideas.com/schema/population/response/v1">';
                put "<ResponseHeader> <PropertyId>&property_Id</PropertyId>" @;
                put "<operationName>TypeThreeValidation</operationName>" @;
                put "<requestId>&request_Id</requestId>";
                stop;
            run;

            data _NULL_;
                FILE resXml MOD;
                set &work_lib..errors;

                if _N_ = 1 then
                    put "<ErrorMessage>" "&outname." "</ErrorMessage>";
            run;

            data _NULL_;
                FILE resXml MOD;
                put "</ResponseHeader></SASResponse>";
            run;
        %end;

		%let app_end_time=%sysfunc(datetime());
		%let runTime=%sysfunc(round(&app_end_time-&app_start_time, .05));

		%if "&GL_ABORT_FLAG" eq "1" or &syscc > 4 or &etl_status = 1 %then
			%let Status_desc=Failed;
		%else
			%do;
				%let Status_desc=Successful;

				%if %upcase(&Sas_Debug.)=FALSE %then
					%do;

						proc printto log="&logfile" new;
						run;

					%end;
			%end;

		Options nomacrogen NoSymbolgen nomlogic nomprint nomfile;
		%put ---------------------------------------------------------------------;
		%put -----------------      Type 3 Validation Summary      ---------------;
		%put ---------------------------------------------------------------------;
		%put;
		%Put -         Property Info: &property_desc.;
		%put -     Validation Status: &status_desc.;
		%Put -   Validation Run time: &runTime. seconds;
		%put -   Snap Shot Date/Time: &snapshot_dttm_temp.;
		%put -    Prepared Date/Time: &prepared_dttm_temp.;
		%put -      Past Window Size: &past_window_size.;
		%put -    Future Window Size: &future_window_size.;
		%put;
		%put -   Validation ETL File: &etl_file_name.;
		%put -         Sas Data Path: &saspath.;
		%put -            Request ID: &requestId.;
		%put -     Work Library Path: &work_path.;
		%put -     Work Library Name: &work_lib;
		%put -         Memlib Option: &memlib_option;
		%put;
		%put -       Database Server: &tenant_server.;
		%put -       Server Instance: &server_instance.;
		%put -         Database Name: &tenant_db.;
		%put -         Database User: &tenant_user.;
		%put -         Database Port: &port.;
		%put;

		data _null_;
			set &work_lib..performance;

			if _n_=1 then
				do;
					put '*********************************************************************';
					put '*************** Type 3 Validation Macro Summary *********************';
					put '*********************************************************************';
					put ' ';
					put  @1 '-    Macro Name                        Status            Run Time(seconds)';
				end;

			put @ 1'-' @ 5  macro_name    @ 40  macro_status  @ 60    macro_run_time_seconds;
		run;

		%PUT ---------------------------------------------------------------------;
		%PUT ----------------------- Ending Type 3 Validation --------------------;
		%PUT ---------------------------------------------------------------------;
		%put;

		%if &work_lib. ne work %then
			%do;

				proc Datasets library = &work_lib. kill memtype=data nolist noprint;
				quit;

			%end;
%mend ideas_type3_validation;