%macro ideas_type3_load(lib=in_xml)/store;
	%local req_id snap_date snap_time prep_date prep_time snap_dttm load_id;
	%local property_desc property_desc_short status_desc start_end app_start_time app_end_time runtime;
	%local meta_id process_id;
	%local sqlrc status etl_file_status etl_process_status;
	%global work_lib work_dir isbde loadtype;
	%local  error_bit rm_data;
	%local c_var m_var error_codes;
	%global etl_status err_str err_ct;
	%let etl_status = 0;
	%let maxerrors = -1;
	%let error_codes = .;
	%let process_id = 0;
	%let app_start_time=%sysfunc(datetime());
	%let runTime=0;
	%PUT ---------------------------------------------------------------------;
	%PUT ------------------ Starting Type 3 Population -----------------------;
	%PUT ---------------------------------------------------------------------;
	%put;

	data _null_;
		set  &lib..requestHeader;
		call symputx('property_Id',propertyId,'l');
		call symputx('requestId',requestId,'l');
		call symputx('tenant_server',tenant_server,'l');
		call symputx('server_instance',tenant_server_instance,'l');
		call symputx('tenant_pwd',tenant_password,'l');
		call symputx('tenant_user',tenant_user,'l');
		call symputx('tenant_db',tenant_database,'l');
		call symputx('port',tenant_db_port,'l');
		call symputx('saspath',dataset_path,'l');
		stop;
	run;

	%if &syscc > 4 %then
		%do;
			%ideas_util_report_errors(910, 'Failed to read request header', '', &request_Id., 1)
			%let etl_status = 1;
			%goto EXIT;
		%end;

	data _null_;
		set  &lib..populationrequest;
		call symputx('etl_file_name',etlfilename,'g');
		call symputx('c_var',clientDataStorageValue,'g');
		call symputx('booked_org_enabled',BookedVsOriginalEnabled,'g');
		call symputx('extendedLOSDataEnabled',ExtendedLOSDataEnabled,'g');
		call symputx('MAX_LOS_HISTORY_OFFSET',HistDataDays,'g');
		call symputx('multiUnitGnrEnabled',multiUnitGnrEnabled,'g');
		call symputx('skipStayedRTPopulation',skipStayedRTPopulation,'g');
		call symputx('DirectReservationNightPopulation',DirectReservationNightPopulation,'g');
		call symputx('useMemlib', useMemlib,'g');

		if operationtype ne "BDE" and operationtype ne "CDP" then
			call symputx('OperationType','BDE','g');
		else call symputx('OperationType',OperationType,'g');
		stop;
	run;

	%put BookedVsOriginalEnabled &booked_org_enabled;
	%put "multiUnitGnrEnabled " &multiUnitGnrEnabled;
	%put &=DirectReservationNightPopulation;
	%put &=skipStayedRTPopulation;

	%if &syscc > 4 %then
		%do;
			%ideas_util_report_errors(909, 'Failed to read Type Three Population request', '', &request_Id., 1)
			%let etl_status = 1;
			%goto EXIT;
		%end;

	%let request_id = %str(&requestid.);
	%let sas_path = %str(&saspath.);
	%let mylog_path = %str(&saspath.);

	%if &work_library.=work %then
		%do;
			%let work_lib=work;
			%let work_path=%sysfunc(pathname(work));
		%end;
	%else
		%do;
			%let work_lib=&work_library.;
			%let work_path_drive=%substr (%sysfunc(pathname(work)),1,2);
			%let work_path_root=&work_path_drive./sas;

			%ideas_util_create_dir(dir=temp,base_path=&work_path_root.);
			%let work_path_root=&work_path_drive./sas/temp;

			%ideas_util_create_dir(dir=&property_id.,base_path=&work_path_root.);
			%let work_path_root=&work_path_drive./sas/temp/&property_id.;

			%ideas_util_create_dir(dir=work,base_path=&work_path_root.);
			%let work_path=&work_path_drive./sas/temp/&property_id./work;

			%if &memlib_option. = memlib  AND %upcase(&useMemlib.) eq TRUE %then
				%do;
					libname &work_lib "&work_path." memlib;
				%end;
			%else
				%do;
					libname &work_lib "&work_path.";
				%end;
		%end;

	/* Create errors table to store error logging */
	data &work_lib..errors;
		length err_ct error_cd 8 request_id error_params error_message $ 200;
		stop;
	run;

	/*Generate a unique load id for the creation of the temp tables;*/
	%let y=%sysfunc(compress(&request_id.,' -'));
	%let load_id = %sysfunc(substr(&y.,1,5));

	%ideas_connect_tenant (&tenant_db., &tenant_server., &port., &server_instance., &tenant_user., &tenant_pwd.);

	%if &etl_status = 1 %then
		%goto EXIT;

	%if &population_use_local_stg=YES %then
		%do;
			%let work_path_drive=%substr (%sysfunc(pathname(work)),1,2);
			%let work_path_root=&work_path_drive./sas;

			%ideas_util_create_dir(dir=temp,base_path=&work_path_root.);
			%let work_path_root=&work_path_drive./sas/temp;

			%ideas_util_create_dir(dir=&property_id.,base_path=&work_path_root.);
			%let work_path_root=&work_path_drive./sas/temp/&property_id.;

			%ideas_util_create_dir(dir=rm_data,base_path=&work_path_root.);
			%let work_path_root=&work_path_drive./sas/temp/&property_id.;

			%ideas_util_create_dir(dir=partitions,base_path=&work_path_root.);
			%let rm_data_perm_path=&sas_path.;
			%let rm_data_work_path=&work_path_drive./sas/temp/&property_id./rm_data;
			%let rm_part_perm_path=&sas_path./partitions;
			%let rm_part_work_path=&work_path_drive./sas/temp/&property_id./partitions;

			%if &memlib_option. = memlib %then
				%do;
					libname rm_data "&rm_data_work_path." memlib;
					libname rm_part "&rm_part_work_path." memlib;
				%end;
			%else
				%do;
					libname rm_data "&rm_data_work_path.";
					libname rm_part "&rm_part_work_path.";
				%end;

			libname rm_datap "&rm_data_perm_path.";
			libname rm_partp "&rm_part_perm_path.";

			/*Creates the copy of the rm_datap.bde_mkt_accom_los_inventory if not exists*/
			%if %sysfunc(exist(rm_datap.bde_mkt_accom_los_inventory))=0 %then
				%do;

					proc Copy in=rm_datap out=rm_data
						CLONE
						CONSTRAINT=YES
						INDEX=YES;
						select mkt_accom_los_inventory;
					run;

					proc Datasets library=rm_data;
						change mkt_accom_los_inventory=BDE_mkt_accom_los_inventory;
						copy in=rm_data out=rm_datap
							CLONE
							CONSTRAINT=YES
							INDEX=YES
							MOVE;
						select BDE_mkt_accom_los_inventory / memtype=data;
					run;

				%end;

			proc Copy in=rm_datap out=rm_data
				CLONE
				CONSTRAINT=YES
				INDEX=YES;
				select mkt_accom_los_inventory BDE_mkt_accom_los_inventory;
			run;

			proc Sql;
				select memname into: type3list separated by ' '
					from dictionary.members
						where libname = upcase("RM_PARTP") and substr(upcase(memname),1,6) eq "MA_LOS";
			quit;

			proc Copy in=rm_partp out=rm_part
				CLONE
				CONSTRAINT=YES
				INDEX=YES;
				select &type3list.;
			run;

		%end;
	%else
		%do;
			%let rm_data_perm_path=&sas_path.;
			%let rm_part_perm_path=&sas_path./partitions;
			libname rm_data "&sas_path.";
			libname rm_part "&sas_path.\partitions";

			%if %sysfunc(exist(rm_data.bde_mkt_accom_los_inventory))=0 %then
				%do;

					proc Copy in=rm_data out=&work_lib.
						CLONE
						CONSTRAINT=YES
						INDEX=YES;
						select mkt_accom_los_inventory;
					run;

					proc Datasets library=&work_lib.;
						change mkt_accom_los_inventory=BDE_mkt_accom_los_inventory;
						copy in=&work_lib. out=rm_data
							CLONE
							CONSTRAINT=YES
							INDEX=YES
							MOVE;
						select BDE_mkt_accom_los_inventory / memtype=data;
					run;

				%end;
		%end;

	proc Sql noprint;
		select record_type_id into: RecType from tenant.record_type where upcase(record_type_name) = 'INDTRANS';
	quit;

	%if not %symexist(RecType) %then
		%do;
			%ideas_util_inc_errors;
			%ideas_util_report_errors(912, "%str(Record type INDTRANS does not exist in the %trim(&tenant_db.) database)",  "&property_desc.", &request_Id., &err_ct.);
			%let etl_status = 1;
		%end;

	proc Sql noprint;
		select property_id into : pid from tenant.property where property_id = &property_id. and status_id=1;
		select property_code into: pcode from tenant.property where property_id = &property_id. and status_id=1;
		select property_name into :property_name from tenant.property where property_Id=&property_id. and status_id=1;
	quit;

	%if not %symexist(pid) %then
		%do;
			%str(&requestid.);
			%let property_desc=%str(Property: %trim(&property_id.) does not exist in the %trim(&tenant_db.) database);
			%let property_desc_short=%str(Property: %trim(&property_id.) does not exist in the %trim(&tenant_db.) database);

			%ideas_util_inc_errors;
			%ideas_util_report_errors(913, "&property_desc.", '', &request_Id., &err_ct.);
			%let etl_status = 1;
			%goto EXIT;
		%end;
	%else
		%do;
			%let property_desc=%str(Property: %trim(&property_id.)  -  %trim(&pcode.));
			%let property_desc_short=%str(Property %trim(&property_id.)  -  %trim(&pcode.));
		%end;

	%ideas_trans_exe(sql=%str(select distinct accom_type_code,accom_type_id from tenant.accom_type where status_id in (1,2) and property_id = &property_id.),
		_output_table=&work_lib..accom_type);
	%ideas_trans_exe(sql=%str(select distinct mkt_seg_code,mkt_seg_id from tenant.mkt_seg where status_id =1 and property_id = &property_id.),
		_output_table=&work_lib..mkt_seg);

	/* If the property does not exist in the file metadata set the default dtttm to 0 so the file will be processed;*/
	/* f_file indicates if it is the first file processed = 0 = false 1 = true;*/
	*  Do the property lookup again, this time in the file_metadata table to see if an extract has ever been processed;
	proc Sql noprint;
		select property_id into:  tpid from tenant.file_metadata where property_id = &property_id. and record_type_id = &RecType.;
	quit;

	%if not %symexist(tpid) %then
		%do;

			data &work_lib..ssdt;
				attrib ssnap_dt format = date9.;
				attrib sssnap_tm format = time10.;
				ssnap_dt = 0;
				ssnap_tm = 0;
				sssnap_tm = 0;
				process_status_id = .;
			run;

			data &work_lib..spdt;
				attrib sprep_dt format = date9.;
				attrib ssprep_tm format = time10.;
				sprep_dt = 0;
				sprep_tm = 0;
				ssprep_tm = 0;
				process_status_id = .;
			run;

		%end;
	%else
		%do;
			/* Get the max system/prepared dates from the global file to be compared against the input file for validation */
			proc Sql;
				create table &work_lib..ssdt1 as
					select distinct snapshot_dt as ssnap_dt, max(snapshot_tm) as ssnap_tm, max(snapshot_tm) as sssnap_tm, isbde
						from tenant.file_metadata
							where property_id = &property_id and record_type_id = &rectype
								and (select max(snapshot_dt) from tenant.file_metadata
							where property_id = &property_id and record_type_id = &rectype) = snapshot_dt;
				create table &work_lib..spdt1 as
					select distinct prepared_dt as sprep_dt, max(prepared_tm) as sprep_tm, max(prepared_tm) as ssprep_tm
						from tenant.file_metadata
							where property_id = &property_id and record_type_id = &rectype
								and (select max(prepared_dt) from tenant.file_metadata
							where property_id = &property_id and record_type_id = &rectype) = prepared_dt;
			quit;

			data &work_lib..ssdt;
				set &work_lib..ssdt1;
				attrib ssnap_dt format = date9.;
				attrib sssnap_tm format = time10.;
			run;

			data &work_lib..spdt;
				set &work_lib..spdt1;
				attrib sprep_dt format = date9.;
				attrib ssprep_tm format = time10.;
			run;

		%end;

	data &work_lib..dates;
		set &work_lib..ssdt;
		set &work_lib..spdt;
	run;

	data &work_lib..dates;
		set &work_lib..dates;
		format ssnap_dttm sprep_dttm datetime20.;
		ssnap_dttm=dhms(ssnap_dt,0,0,ssnap_tm);
		sprep_dttm=dhms(sprep_dt,0,0,sprep_tm);
	run;

	/* Input the inbound ETL file */
	/* Create temp tables to store the hash output */
	data &work_lib..res_val;
		attrib reservation_identifier format=$50.;
	run;

	data &work_lib..file_metadata (keep=record_type client_code property_id past_window_size future_window_size
		snapshot_date snapshot_time prepared_date prepared_time)
		&work_lib..trans (keep=record_type reservation_identifier individual_status arrival_dt departure_dt booking_dt cancellation_dt
		booked_accom_type_code accom_type_cd market_segment room_revenue food_revenue beverage_revenue telecom_revenue
		other_revenue total_revenue source_booking nationality rate_code rate_value room_number booking_type
		number_children number_adults Booking_TM)
		&work_lib..accom_type_codes (keep = accom_type_code)
		&work_lib..mkt_seg_codes (keep = mkt_seg_code)
		&work_lib..badrecs (keep=record_type)
		&work_lib..errors (keep = error_cd error_message error_params request_id err_ct);
		retain snapshot_date past_window_size future_window_size;
		length error_cd err_ct 8 error_params error_message request_id $200;
		length accom_type_id 4;
		length mkt_seg_id 4;
		length occupancy_dt 8;
		length market_segment $50;
		attrib record_type format=$20.;
		attrib accom_type_cd format=$50.;
		attrib booked_accom_type_code format=$50.;
		attrib record_type format=$20.;
		attrib reservation_identifier format=$50.;
		attrib arrival_dt format=date9.;
		attrib departure_dt format=date9.;
		attrib booking_dt format=date9.;
		attrib cancellation_dt format=date9.;

		if _n_ = 1 then
			do;
				retain err_ct  &err_ct.;
			end;

		infile "&etl_file_name" delimiter='|' missover dsd;

		if _n_=2 then
			do;
				/* below hash to validate uniqueness of reservation id */
				declare hash res_val( dataset: "&work_lib..res_val");
				res_val.defineKey('reservation_identifier');
				res_val.defineDone();

				/* create hash table to identify new accom and market segment codes */
				declare hash accom(dataset: "&work_lib..accom_type");
				accom.defineKey('accom_type_code');
				accom.defineData('accom_type_id');
				accom.defineDone();
				declare hash mkt(dataset: "&work_lib..mkt_seg");
				mkt.defineKey('mkt_seg_code');
				mkt.defineData('mkt_seg_id');
				mkt.defineDone();
				input record_type $ client_code $ property_id $ past_window_size
					future_window_size snapshot_date : yymmdd10. snapshot_time $ prepared_date : yymmdd10. prepared_time $;

				if sum( missing(record_type),missing(client_code),missing(property_id),
					missing(past_window_size),missing(future_window_size),
					missing(snapshot_date),missing(snapshot_time),
					missing(prepared_date),missing(prepared_time) ) > 0 then
					do;
						error_cd = 421;
						error_message = 'Missing universal field on metadata record';
						error_params = _n_;
						request_id = "&request_id";
						err_ct + 1;
						output &work_lib..errors;
						call symputx('etl_status',1,'l');
					end;
				else
					do;
						tmp_dt=put(snapshot_date,date9.)||':'||trim(left(snapshot_time));
						snap_dttm=input(tmp_dt,datetime20.);
						tmp_dt=put(prepared_date,date9.)||':'||trim(left(prepared_time));
						prepared_dttm=input(tmp_dt,datetime20.);
						call symput('snapshot_date',snap_dttm);
						call symput('prepared_date',prepared_dttm);
						call symput('snap_date', compress(snapshot_date));
						call symput('snap_time', compress(snapshot_time));
						call symput('prep_date', compress(prepared_date));
						call symput('prep_time', compress(prepared_time));
						call symput('snap_dttm', compress(snap_dttm));
						call symput('prepared_dttm', compress(prepared_dttm));
						call symput ('past_window_size', compress (past_window_size));
						call symput ('future_window_size', compress (future_window_size));
						call symput ('property_cd', property_id);
						call symput('prep_dttm',prepared_dttm);
						output &Work_lib..file_metadata;
					end;
			end;
		else
			do;
				input record_type $ @;

				if record_type='_TRANS_' then
					do;
						INPUT reservation_identifier $ individual_status: $2. arrival_dt : YYMMDD10. departure_dt : YYMMDD10.
							booking_dt :YYMMDD10. cancellation_dt : YYMMDD10. booked_accom_type_code : $50. accom_type_cd : $50.
							market_segment: $50. room_revenue food_revenue beverage_revenue telecom_revenue  other_revenue
							total_revenue  source_booking: $50. nationality: $2. rate_code: $50. rate_value  room_number : $50.
							booking_type: $50. number_children number_adults Booking_TM: time7.;
						mkt_seg_code = market_segment;
						accom_type_code = accom_type_cd;

						if sum(missing(record_type),missing(reservation_identifier), missing(individual_status), missing(arrival_dt), missing(departure_dt),
							missing(booking_dt), missing(accom_type_cd), missing(market_segment), missing(room_revenue), missing(total_revenue)) > 0 then
							do;
								return;
							end;

						if res_val.find() ne 0 then
							do;
								res_val.add();
							end;
						else
							do;
								return;
							end;

						if departure_dt < arrival_dt or booking_dt > arrival_dt or
							upcase(individual_status) eq 'XX' and cancellation_dt eq .
							or upcase(individual_status) eq 'CX' and cancellation_dt eq .
							or arrival_dt < snapshot_date - past_window_size
							or arrival_dt > snapshot_date + future_window_size then
							do;
								return;
							end;

						output &work_lib..trans;

						if accom.find() ne 0 then
							do;
								output &work_lib..accom_type_codes;
							end;

						if mkt.find() ne 0 then
							do;
								output &work_lib..mkt_seg_codes;
							end;
					end;
			end;
	run;

	%if &maxErrors. ne -1 %then
		%do;

			proc Sql inobs = &maxErrors.;
				create table &work_lib..errors as
					select * from &work_lib..errors;
			quit;

		%end;

	%if &syscc > 4 %then
		%do;
			%ideas_util_report_errors(903, 'Error importing ETL file', '', &request_Id., 1)
			%let etl_status = 1;
			%goto EXIT;
		%end;

	%if &past_window_size. EQ 0 AND &future_window_size. EQ 0 %then
		%do;
			%goto EXIT;
		%end;

	proc Sql;
		select count(*) into: transcount from &work_lib..trans;
	quit;

	%if &transcount eq 0 %then
		%do;
			%let error_codes = 933;
			%let etl_status = 1;
			%goto EXIT;

			%let etl_status = 1;
			%goto EXIT;
		%end;

	%if %upcase(&OperationType.) eq BDE %then
		%do;
			%let isbde=1;
			%let LoadType=%upcase(&OperationType.);
		%end;
	%else
		%do;
			%let isbde=0;
			%let LoadType=%upcase(&OperationType.);
		%end;

	data &work_lib..fdates;
		set &Work_lib..file_metadata 
			(keep=snapshot_date snapshot_time prepared_date prepared_time);
		snap_tm = input(snapshot_time,time10.);
		prep_tm = input(prepared_time,time10.);
		snap_dttm = input(put(snapshot_date,date9.)||':'||trim(left(snapshot_time)),datetime20.);
		prep_dttm = input(put(prepared_date,date9.)||':'||trim(left(prepared_time)),datetime20.);
	run;

	data  &work_lib..dates;
		set &work_lib..dates;
		set &work_lib..fdates;
	run;

	/*determine type of file*/
	data &work_lib..comp;
		set &work_lib..dates;
		format snapshot_date date9.;
		format snap_dttm prep_dttm datetime20.;

		if missing(ssnap_dttm) then
			do;
				file_type = 'EOB';
			end;
		else
			do;
				if snapshot_date > ssnap_dt then
					do;
						file_type = 'EOB';
					end;

				if snap_dttm eq ssnap_dttm and prep_dttm > sprep_dttm then
					do;
						file_type = 'COR';
					end;

				if snapshot_date eq ssnap_dt and snap_tm > sssnap_tm then
					do;
						file_type = 'INT';
					end;

				if snap_dttm eq ssnap_dttm and prep_dttm eq sprep_dttm then
					do;
						file_type = 'RP';
					end;
			end;

		call symputx('file_type', file_type,'g');
	run;

	/* Check to see if the type three ETL file has been previously processed  */
	data &work_lib..failure (keep = error_cd error_message error_params request_id err_ct);
		length error_cd err_ct 8 error_params error_message request_id $200;
		set &work_lib..dates;
		format snapshot_date date9.;

		if prep_dttm eq sprep_dttm  and snap_dttm eq ssnap_dttm then
			do;
				call symputx('error_codes',415,'l');
				put 'ETL file has been previously processed';
			end;
	run;

	/* Prepare for metadata insert */
	data &work_lib..fn;
		call scan("&etl_file_name",-1,fn_pos,fn_length,'\');
		fname = substrn("&etl_file_name",fn_pos,fn_length);
	run;

	proc Sql noprint;
		select fname into: file_name from &work_lib..fn;
		select fn_length -4 into: fn_length from &work_lib..fn;
	quit;

	data &work_lib..fl;
		floc = substrn("&etl_file_name",1,length(trim("&etl_file_name"))-&fn_length-4);
	run;

	proc Sql noprint;
		select floc into: file_location from &work_lib..fl;
	quit;

	data &work_lib..metatemp;
		attrib File_Location format = $512.;
		attrib File_Name format = $150.;
		attrib mode format = $50.;
		attrib Snapshot_DT Prepared_DT Scope_Start_DT Scope_End_DT format = date9.;
		attrib createdate format = datetime22.;
		Record_Type_Id = &RecType;
		File_Name = "&file_name";
		File_Location = "&file_location";
		Property_ID = &Property_ID;
		Past_Window_Size = &Past_Window_Size;
		Future_Window_Size = &Future_Window_Size;
		Scope_Start_DT=.;
		Scope_End_DT=.;
		Snapshot_DT = &Snap_Date;
		Snapshot_TM = "&Snap_Time";
		Prepared_DT = &Prep_Date;
		Prepared_TM = "&Prep_Time";
		mode=.;
		createdate = datetime();
		Process_Status_ID = 3;
		IsBDE = &isbde.;
	run;

	/*change the format of the metatemp to be consistent with the tenant database*/
	data &work_lib..metatemp1;
		set &work_lib..metatemp;
		format snaptm preptm time16.7;
		snaptm = input(Snapshot_TM, time16.7);
		preptm = input(Prepared_TM, time16.7);
		drop Snapshot_TM Prepared_TM;
	run;

	data &work_lib..metatemp2;
		set &work_lib..metatemp1;
		rename snaptm = Snapshot_TM;
		rename preptm = Prepared_TM;
	run;

	%if &error_codes ne 415 %then
		%do;
			%let connect_str = driver={SQL Server Native Client 10.0}%str(;)server=&tenant_server\&server_instance%str(;)uid=&tenant_user%str(;)pwd=&tenant_pwd%str(;)database=&tenant_db%str(;)port=&port;
			%let connect_str=complete="&connect_str";
			%local metad;

			%ideas_trans_upload_tmp(upload_table=&work_lib..metatemp2,
				like_table=tenant.file_metadata (drop = File_Metadata_ID),
				_name_in_db=metad,
				tmp_trans_lib=ten_tmp,cnt=1&load_id.);
			%let metad = ##&metad;

			proc Sql;
				connect to odbc (&connect_str autocommit=no);
				execute(
					insert into file_metadata (file_name, file_location, snapshot_dt, snapshot_tm, prepared_dt, prepared_tm, record_type_id, property_id, past_window_size, future_window_size, process_status_id, isbde)
						select file_name, file_location, snapshot_dt, snapshot_tm, prepared_dt, prepared_tm, record_type_id, property_id, past_window_size, future_window_size, Process_Status_ID, isbde
							from &metad;

				%if &sqlrc ne 0 %then
					%do;
						rollback;
						%let error_codes = 914;
						%goto EXIT;
					%end;

				commit;
				) by odbc;
			quit;

		%end;

	/* generate metadata_id & process_status_id for inbound data */
	proc Sql noprint;
		select a.file_metadata_id into: meta_id 
			from tenant.file_metadata as a, &work_lib..metatemp2 as b
				where a.Property_ID = b.Property_ID
					and a.SnapShot_DT = b.SnapShot_DT
					and a.Snapshot_TM = b.Snapshot_TM    
					and a.Prepared_DT = b.Prepared_DT  
					and a.Prepared_TM = b.Prepared_TM 
					and a.record_type_id = b.record_type_id
					and a.file_name = b.file_name;
	quit;

	%if not %symexist(meta_id) %then
		%do;
			%ideas_util_report_errors(915, 'Failed to assign meta_id', '', &request_Id., 1)
			%let etl_status = 1;
		%end;

	proc Sql noprint;
		select process_status_id into: process_id from tenant.file_metadata 
			where file_metadata_id = &meta_id;
	quit;

	%if not %symexist(process_id) %then
		%do;
			%ideas_util_report_errors(916, 'Failed to assign process_id', '', &request_Id., 1)
			%let etl_status = 1;
		%end;

	data &work_lib..metastatus;
		meta_id = &meta_id.;
		process_id = &process_id.;
	run;

	%if &process_id. = 13 %then %goto EXIT; /* previously processed file completed successfully */

		/* determine accom class and available capacity for new inbound accom codes */
		/* set default accom_class_id. in case it is not in the database. At the end of the call if still . then fail */
		proc Sql;
			select distinct accom_class_id into:  accom_class_id 
				from tenant.accom_class where property_id = &property_id. and upcase(accom_class_code) ='UNASSIGNED';
		quit;

		%if not %symexist(accom_class_id) %then
			%do;
				%ideas_util_report_errors(917, 'No accom_class_id listed for the property', '', &request_Id., 1)
				%let etl_status = 1;
				%goto EXIT;
			%end;

		/* Identify the new accom and mkt seg codes in inbound data and load into rdbms to get new ids */
		proc Sql;
			create table &work_lib..inbound_accom_cd as
				select distinct accom_type_code , accom_type_code as accom_type_name, &property_id. as property_id,
					1 as status_id,&accom_class_id. as Accom_class_Id,0 as system_default,
					datetime() as Last_Updated_DTTM, datetime() as createdate 
				from &work_lib..accom_type_codes;
			create table &work_lib..inbound_mktseg_cd as
				select distinct mkt_seg_code, 1 as Is_Editable, mkt_seg_code as Mkt_Seg_Name, &property_id as property_id,1 as status_id, 
					datetime() as createdate, datetime() as Last_updated_dttm
				from &work_lib..mkt_seg_codes;
		quit;

		%if %ideas_util_nobs(&work_lib..inbound_accom_cd) > 0 %then
			%do;

				data ten_tmp.iaccomtype&load_id.;
					set &work_lib..inbound_accom_cd;
				run;

				%local connect_Str;
				%let connect_str = driver={SQL Server Native Client 10.0}%str(;)server=&tenant_server\&server_instance%str(;)uid=&tenant_user%str(;)pwd=&tenant_pwd%str(;)database=&tenant_db%str(;)port=&port;
				%let connect_str=complete="&connect_str";
				%put Connect Str= &connect_str.;

				proc Sql;
					connect to odbc (&connect_str autocommit=no);
					execute(
						insert into accom_type (accom_type_code, accom_type_name, property_id, status_id, accom_class_id)
							select accom_type_code, accom_type_name, property_id, status_id, accom_class_id from ##iaccomtype&load_id.
								) by odbc;
					execute (
						commit
						)by odbc;
				quit;

				/* to get the new sequence keys generated for the ids we need to query again */
				%ideas_trans_exe(sql=%str(select distinct accom_type_code,accom_type_id from tenant.accom_type where status_id =1 and property_id = &property_id.),
					_output_table=&work_lib..accom_type);
			%end;

		%if %ideas_util_nobs(&work_lib..inbound_mktseg_cd) > 0 %then
			%do;
				%ideas_trans_append(data_table=&work_lib..inbound_mktseg_cd, trans_table=mkt_seg, trans_lib=tenant);

				/* to get the new sequence keys generated for the ids we need to query again */
				%ideas_trans_exe(sql=%str(select distinct mkt_seg_code,mkt_seg_id from tenant.mkt_seg where status_id =1 and property_id = &property_id.),
					_output_table=&work_lib..mkt_seg);
			%end;

		%if &syscc > 4 %then
			%do;
				%ideas_util_report_errors(918, 'Error while inserting accomodation types or market_segment codes', '', &request_Id., 1)
				%let etl_status = 1;
				%goto EXIT;
			%end;

		/* Assign the unique accom and mkt seg ids to the inbound data */
		proc Sql;
			create table &work_lib..trans2 as
				select a.*, b.accom_type_id, c.mkt_seg_id 
					from &work_lib..trans A, &work_lib..accom_type B, &work_lib..mkt_seg C
						where A.accom_type_cd = B.accom_type_code
							and A.market_segment = C.mkt_seg_code;
		quit;

		proc Sql;
			select min(arrival_dt) format=YYMMDD10. into: startdate from &work_lib..trans;
			select max(arrival_dt) format=YYMMDD10. into: enddate from &work_lib..trans;
			select min(arrival_dt) into: sdate from &work_lib..trans;
			select snapshot_date into: edate from &work_lib..file_metadata;
		quit;

		data _null_;
			startDate="'"||symget('startdate')||"'";
			call symput('startDate', startdate);
			endDate="'"||symget('enddate')||"'";
			call symput('endDate', enddate);
		run;

		data &work_lib..trans3;
			set &work_lib..trans2;
			file_metadata_id = &meta_id.;
			CreateDate_DTTM = datetime();
			property_id = &property_id.;
			drop market_segment accom_type_cd record_type;
		run;

		%if &error_codes ne 415 or &process_id. < 4 %then
			%do;
				%local tran_t;
				%local connect_Str;
				%let connect_str = driver={SQL Server Native Client 10.0}%str(;)server=&tenant_server\&server_instance%str(;)uid=&tenant_user%str(;)pwd=&tenant_pwd%str(;)database=&tenant_db%str(;)port=&port;
				%let connect_str=complete="&connect_str";
				%put Connect Str= &connect_str.;

				%if &DirectReservationNightPopulation eq true %then %do;
				 		data &work_lib..trans4(drop=Room_Revenue Food_Revenue Beverage_Revenue Telecom_Revenue Other_Revenue Total_Revenue
                 		        rename=(Room_Revenue_t=Room_Revenue  Food_Revenue_t=Food_Revenue  Beverage_Revenue_t=Beverage_Revenue
                 		            Telecom_Revenue_t=Telecom_Revenue  Other_Revenue_t=Other_Revenue  Total_Revenue_t=Total_Revenue));
                    			set &work_lib..trans3;
                       			occupancy_DT=Arrival_DT;
                       			format occupancy_DT YYMMDD10.;
                                 do while (occupancy_DT <= Departure_DT -1);
                                     Room_Revenue_t=Room_Revenue/(Departure_DT-Arrival_DT);
                                     Food_Revenue_t=Food_Revenue/(Departure_DT-Arrival_DT);
                                     Beverage_Revenue_t=Beverage_Revenue/(Departure_DT-Arrival_DT);
                                     Telecom_Revenue_t=Telecom_Revenue/(Departure_DT-Arrival_DT);
                                     Other_Revenue_t=Other_Revenue/(Departure_DT-Arrival_DT);
                                     Total_Revenue_t=Total_Revenue/(Departure_DT-Arrival_DT);
                                     format Room_Revenue_t Food_Revenue_t Beverage_Revenue_t Telecom_Revenue_t Other_Revenue_t Total_Revenue_t 19.5;
                                     persistent_key = cats(strip(Reservation_Identifier),',',put(occupancy_DT,YYMMDD10.));
                                     output;
                                     occupancy_DT = occupancy_DT +1;
                                 end;
                        run;

                    %ideas_trans_upload_tmp(upload_table=&work_lib..trans4,
                        like_table=tenant.Reservation_Night(drop = Individual_Trans_ID),
                        _name_in_db=tran_t,
                        tmp_trans_lib=ten_tmp,cnt=2&load_id.);
                    %let tran_t = ##&tran_t;

                    proc Sql noprint;
                        connect to odbc (&connect_str autocommit=no);
                        execute(
                            delete from Reservation_night where property_id = &property_id. and  arrival_dt between &startdate. and &enddate.
                                ) by odbc;
                        execute(
                            insert into Reservation_night
                                (File_Metadata_ID, Property_ID, Reservation_Identifier, Individual_Status, Arrival_DT, Departure_DT,
                                Booking_DT, Cancellation_DT, Booked_Accom_Type_Code, Accom_Type_ID, Mkt_Seg_ID, Room_Revenue,
                                Food_Revenue, Beverage_Revenue, Telecom_Revenue, Other_Revenue, Total_Revenue, Source_Booking,
                                Nationality, Rate_Code, Rate_Value, Room_Number, Booking_type, Number_Children, Number_Adults,Booking_TM, CreateDate_DTTM,
                                Occupancy_DT,Persistent_key)

                            select File_Metadata_ID, Property_ID, Reservation_Identifier, Individual_Status, Arrival_DT, Departure_DT,
                                Booking_DT, Cancellation_DT, Booked_Accom_Type_Code, Accom_Type_ID, Mkt_Seg_ID, Room_Revenue,
                                Food_Revenue, Beverage_Revenue, Telecom_Revenue, Other_Revenue, Total_Revenue, Source_Booking,
                                Nationality, Rate_Code, Rate_Value, Room_Number, Booking_type, Number_Children, Number_Adults, Booking_TM, CreateDate_DTTM,
                                Occupancy_DT, Persistent_key
                            from &tran_t.) by odbc;
  					%end;
                %else %do;
                    %ideas_trans_upload_tmp(upload_table=&work_lib..trans3,
                        like_table=tenant.Individual_Trans(drop = Individual_Trans_ID),
                        _name_in_db=tran_t,
                        tmp_trans_lib=ten_tmp,cnt=2&load_id.);
                    %let tran_t = ##&tran_t;

                    proc Sql noprint;
                        connect to odbc (&connect_str autocommit=no);
                        execute(
                            delete from individual_trans where property_id = &property_id. and  arrival_dt between &startdate. and &enddate.
                                ) by odbc;
                        execute(
                            insert into Individual_Trans
                                (File_Metadata_ID, Property_ID, Reservation_Identifier, Individual_Status, Arrival_DT, Departure_DT,
                                Booking_DT, Cancellation_DT, Booked_Accom_Type_Code, Accom_Type_ID, Mkt_Seg_ID, Room_Revenue,
                                Food_Revenue, Beverage_Revenue, Telecom_Revenue, Other_Revenue, Total_Revenue, Source_Booking,
                                Nationality, Rate_Code, Rate_Value, Room_Number, Booking_type, Number_Children, Number_Adults,Booking_TM, CreateDate_DTTM)

                            select File_Metadata_ID, Property_ID, Reservation_Identifier, Individual_Status, Arrival_DT, Departure_DT,
                                Booking_DT, Cancellation_DT, Booked_Accom_Type_Code, Accom_Type_ID, Mkt_Seg_ID, Room_Revenue,
                                Food_Revenue, Beverage_Revenue, Telecom_Revenue, Other_Revenue, Total_Revenue, Source_Booking,
                                Nationality, Rate_Code, Rate_Value, Room_Number, Booking_type, Number_Children, Number_Adults, Booking_TM, CreateDate_DTTM
                            from &tran_t.) by odbc;
                %end;


					%if &sysdbrc eq 0 or "&sysdbrc" eq "" %then
						%do;
							execute(update file_metadata set process_status_id = 4 where file_metadata_id = &meta_id) by odbc;

							%if &sysdbrc eq 0 or "&sysdbrc" eq "" %then
								%do;
									execute (commit ) by odbc;
								%end;
							%else
								%do;
									execute(rollback) by odbc;
									%let error_codes = 932;
									%goto EXIT;
								%end;
						%end;
					%else
						%do;
							execute(rollback) by odbc;
							%let error_codes = 932;
							%goto EXIT;
						%end;
				quit;

			%end;

		/* Make a backup copy (_fin) of the sas datasets so when the deltas are created, they can be automatically insert them into the _fin table. */
		proc Copy in=rm_data out=&work_lib. memtype=data;
			select mkt_accom_los_inventory bde_mkt_accom_los_inventory;
		run;

		proc Datasets library=&work_lib.;
			change mkt_accom_los_inventory=mkt_accom_los_inventory_fin;
		run;

		%let boolean=3;
		%let vshort=3;
		%let short=4;
		%let long=8;

		data &work_lib..delta_mkt_accom_los_inventory;
			attrib PROPERTY_ID                         length=&short;
			attrib MKT_SEG_ID                          length=&vshort;
			attrib ARRIVAL_DT                          length=&long;
			attrib ACCOM_TYPE_ID                       length=&vshort;
			attrib LOS                                 length=&vshort;
			attrib ARRIVALS                            length=&long;
			attrib NO_SHOWS                            length=&long;
			attrib CANCELLATIONS                       length=&long;
			attrib ROOM_REVENUE                        length=&long;
			attrib TOTAL_REVENUE                       length=&long;
			attrib CAPTURE_DTTM                        length=&long;
			attrib CHANGE_FLAG                           length=&boolean;
			stop;
		run;

		/*prepare data for aggregated load into sas datasets*/
		/*100311 setting cancellations and no_shows to 0 is a temporary fix until ratchet is ready*/
		data &work_lib..lostrans;
			set &work_lib..trans3;
			los = departure_dt - arrival_dt;

			if individual_status = 'XX' or individual_status = 'CX' then
				cancellations = 1;
			else cancellations = 0;

			if individual_status = 'NS' then
				no_shows = 1;
			else no_shows = 0;

			if cancellations or no_shows = 1 then
				do;
					arrivals = 0;
					room_revenue = 0;
					total_revenue = 0;
				end;
			else
				do;
					arrivals = 1;
				end;

			keep  arrival_dt property_id accom_type_id mkt_seg_id los arrivals no_shows cancellations room_revenue total_revenue;
		run;

		proc Sql;
			create table &work_lib..mkt_accom_los_inventory as
				select arrival_dt, property_id, accom_type_id, mkt_seg_id, los, sum(arrivals) as arrivals, sum(no_shows) as no_shows, sum(cancellations) as cancellations,
					sum(room_revenue) as room_revenue, sum(total_revenue) as total_revenue
				from &work_lib..lostrans
					group by arrival_dt, property_id, accom_type_id, mkt_seg_id, los
						order by property_id, Accom_Type_ID, mkt_seg_id, arrival_dt, los;
		quit;

		/* identify key combinations that are not in the inbound dataset */
		data _null_;
			set &work_lib..file_metadata;
			earrival = snapshot_date - past_window_size;
			call symput('earrival', earrival);
		run;

		%if %upcase(&loadtype.)=BDE %then
			%do;

				data &work_lib..BDE_mkt_accom_los_inventory_tmp;
					set &work_lib..BDE_mkt_accom_los_inventory;
					format arrival_dt date9.;
					where arrival_dt >= &earrival.;
				run;

				proc Sort data = &work_lib..BDE_mkt_accom_los_inventory_tmp;
					by property_id Accom_Type_ID mkt_seg_id arrival_dt los;
				run;

				data &work_lib..BDE_deletes;
					/*merge &work_lib..BDE_mkt_accom_los_inventory_tmp (in=a) &work_lib..BDE_mkt_accom_los_inventory (in=b);*/
					merge &work_lib..BDE_mkt_accom_los_inventory_tmp (in=a) &work_lib..mkt_accom_los_inventory (in=b);
					by property_id Accom_Type_ID mkt_seg_id arrival_dt los;

					if a and not b then
						do;
							arrivals = 0;
							no_shows = 0;
							cancellations = 0;
							room_revenue = 0;
							total_revenue = 0;
							output &work_lib..BDE_deletes;
						end;
				run;

				proc Append base=&work_lib..mkt_accom_los_inventory data=&work_lib..BDE_deletes force;
				run;

				/* identify the deltas to be inserted into the pace table */
				data &work_lib..BDE_mkt_accom_los_inventory
					&work_lib..delta_mkt_accom_los_inventory (keep = Mkt_Seg_ID Accom_Type_ID CAPTURE_DTTM  
					ARRIVAL_DT property_id arrivals cancellations no_shows room_revenue total_revenue change_flag los);
					set &work_lib..mkt_accom_los_inventory (rename=(
						arrivals = i_arrivals
						cancellations = i_cancellations
						no_shows = i_no_shows  
						room_revenue = i_room_revenue
						total_revenue = i_total_revenue));
					modify &work_lib..BDE_mkt_accom_los_inventory key = book_idx;

					if _iorc_=0 then
						do;
							if  arrivals ne i_arrivals 
								or cancellations ne i_cancellations 
								or no_shows ne i_no_shows 
								or room_revenue ne i_room_revenue
								or total_revenue ne i_total_revenue then
								do;
									arrivals = i_arrivals;
									cancellations = i_cancellations;
									no_shows = i_no_shows;
									room_revenue = i_room_revenue;
									total_revenue = i_total_revenue;
									CAPTURE_DTTM = &snap_dttm;
									change_flag = 1;
									replace &work_lib..BDE_mkt_accom_los_inventory;
									output &work_lib..delta_mkt_accom_los_inventory;
								end;
							else
								do;
									arrivals = arrivals;
									cancellations = cancellations;
									no_shows = no_shows;
									room_revenue = room_revenue;
									total_revenue = total_revenue;
									CAPTURE_DTTM = &snap_dttm;
									change_flag = 0;
									output &work_lib..delta_mkt_accom_los_inventory;
								end;
						end;
					else
						do;
							arrivals = i_arrivals;
							cancellations = i_cancellations;
							no_shows = i_no_shows;
							room_revenue = i_room_revenue;
							total_revenue = i_total_revenue;
							CAPTURE_DTTM = &snap_dttm;
							change_flag = 1;
							output &work_lib..BDE_mkt_accom_los_inventory;
							output &work_lib..delta_mkt_accom_los_inventory;
						end;

					_iorc_=0;
					_error_=0;
				run;

				data &work_lib..mkt_accom_los_inventory_pace;
					set &work_lib..delta_mkt_accom_los_inventory;
					where change_flag = 1;
					drop change_flag;
				run;

				proc Sql noprint;
					select count(*) into: mspace_ct  from &work_lib..mkt_accom_los_inventory_pace;
				quit;

				%if &mspace_ct > 0 %then
					%ideas_mspace_los;
			%end;

		data &work_lib..mkt_accom_los_inventory_fin_tmp;
			set &work_lib..mkt_accom_los_inventory_fin;
			format arrival_dt date9.;
			where arrival_dt >= &earrival.;
		run;

		proc Sort data = &work_lib..mkt_accom_los_inventory_fin_tmp;
			by property_id Accom_Type_ID mkt_seg_id arrival_dt los;
		run;

		proc Sort data = &work_lib..mkt_accom_los_inventory;
			by property_id Accom_Type_ID mkt_seg_id arrival_dt los;
		run;

		data &work_lib..deletes;
			merge &work_lib..mkt_accom_los_inventory_fin_tmp (in=a) &work_lib..mkt_accom_los_inventory (in=b);
			by property_id Accom_Type_ID mkt_seg_id arrival_dt los;

			if a and not b then
				do;
					arrivals = 0;
					no_shows = 0;
					cancellations = 0;
					room_revenue = 0;
					total_revenue = 0;
					output &work_lib..deletes;
				end;
		run;

		proc Append base=&work_lib..mkt_accom_los_inventory data=&work_lib..deletes force;
		run;

		/* identify the deltas to be inserted into the pace table */
		data &work_lib..mkt_accom_los_inventory_fin;
			set &work_lib..mkt_accom_los_inventory (rename=(
				arrivals = i_arrivals
				cancellations = i_cancellations
				no_shows = i_no_shows  
				room_revenue = i_room_revenue
				total_revenue = i_total_revenue));
			modify  &work_lib..mkt_accom_los_inventory_fin key = book_idx;

			if _iorc_=0 then
				do;
					if  arrivals ne i_arrivals 
						or cancellations ne i_cancellations 
						or no_shows ne i_no_shows 
						or room_revenue ne i_room_revenue
						or total_revenue ne i_total_revenue then
						do;
							arrivals = i_arrivals;
							cancellations = i_cancellations;
							no_shows = i_no_shows;
							room_revenue = i_room_revenue;
							total_revenue = i_total_revenue;
							CAPTURE_DTTM = &snap_dttm;
							replace &work_lib..mkt_accom_los_inventory_fin;
						end;
					else
						do;
						end;
				end;
			else
				do;
					arrivals = i_arrivals;
					cancellations = i_cancellations;
					no_shows = i_no_shows;
					room_revenue = i_room_revenue;
					total_revenue = i_total_revenue;
					CAPTURE_DTTM = &snap_dttm;
					output &work_lib..mkt_accom_los_inventory_fin;
				end;

			_iorc_=0;
			_error_=0;
		run;

		%if &loadtype. = BDE %then
			%do;

				proc Datasets library=rm_data nolist;
					Copy in=&work_lib.  out = rm_data 
						CLONE 
						CONSTRAINT=YES 
						INDEX=YES;
					select mkt_accom_los_inventory_fin / memtype=data;
					Delete mkt_accom_los_inventory BDE_mkt_accom_los_inventory;
					Change mkt_accom_los_inventory_fin=mkt_accom_los_inventory;
					Copy in=rm_data out = &work_lib.
						CLONE 
						CONSTRAINT=YES 
						INDEX=YES;
					select mkt_accom_los_inventory / memtype=data;
				run;

				proc Datasets library=&work_lib.;
					Delete BDE_mkt_accom_los_inventory;
					change mkt_accom_los_inventory=BDE_mkt_accom_los_inventory;
					Copy in=&work_lib.  out = rm_data 
						CLONE 
						CONSTRAINT=YES 
						INDEX=YES;
					select BDE_mkt_accom_los_inventory / memtype=data;
				run;

			%end;
		%else
			%do;

				proc Datasets library=rm_data nolist;
					Copy in=&work_lib.  out = rm_data 
						CLONE 
						CONSTRAINT=YES 
						INDEX=YES;
					select mkt_accom_los_inventory_fin / memtype=data;
					Delete mkt_accom_los_inventory;
					change mkt_accom_los_inventory_fin=mkt_accom_los_inventory;
				run;

			%end;

		%if %upcase(&loadtype.)=BDE %then
			%do;
				%IF (&booked_org_enabled eq true OR &extendedLOSDataEnabled eq true) %THEN
					%DO;
						%put Booked versus original is set to &booked_org_enabled hence preserving original information;
						%let snapshot_dt=%sysfunc(putn(&snap_dttm.,datetime20.));
						%put snapshot date is &snapshot_dt;

						data &work_lib..snapshot;
							snap_dttm=input("&snapshot_dt",date9.);
							put snap_dttm;
						run;

						proc sql;
							select snap_dttm into :snapdt from &work_lib..snapshot;
						quit;

						/* Adding an libname reference*/
						libname rm_datap "&sas_path.";

						%ideas_individual_trans_temp(&snapdt.);
					%END;

				%if &multiUnitGnrEnabled eq true %then
					%do;
						%ideas_reset_org_info_resid(&property_id.,los_extension_info,'resetlosextinfoforesids',&request_Id.);
						%ideas_reset_org_info_resid(&property_id.,Original_info,'resetoriginalinforesids',&request_Id.);
					%end;

				%if &extendedLOSDataEnabled eq true %then
					%do;
						%ideas_los_extension_information(&snapdt.);
					%end;

				%if &booked_org_enabled eq true %then
					%do;
						%ideas_booked_versus_original(&snap_dttm.,&saspath.);
					%end;
				%else
					%do;
						%put Booked versus original is set to &booked_org_enabled hence not preserving original information;
					%end;

				/***** Check code that can be turned on locally
				%ideas_chk_InhS_BKDSTYD_Info(&property_id., &snapdt.);
				*********/
			%end;

		%if &syscc > 4 %then
			%do;
				%ideas_util_report_errors(931, 'Lock on SAS Datasets', '', &request_Id., 1);
				%let etl_status = 1;
				%goto EXIT;
			%end;

		%if %upcase(&loadtype.)=BDE %then
			%do;
				%ideas_mspace_remove_fin_los;
			%end;

		%if &syscc > 4 %then
			%do;
				%ideas_util_report_errors(931, 'Lock on SAS dataset', '', &request_Id., 1)
				%let etl_status = 1;
				%goto EXIT;
			%end;

		proc Sql noprint;
			select count(*), memname into: removecount, :removelist separated by ' '
				from dictionary.members
					where libname = upcase("RM_PART") and  
						( trim(lowcase(memname)) like 'ma_los%fin');
		quit;

		%if &removecount. >0 %then
			%do;

				proc Datasets library=rm_part noprint nolist;
					Delete &removelist.;
				run;

				quit;

			%end;
		%ideas_UpdateETLStatus(13,&tenant_db., &tenant_server., &port., &server_instance., &tenant_user., &tenant_pwd., &meta_id.);
%EXIT:

		%if &error_codes eq 918 %then
			%let err_str = 'Error while inserting accomodation types or market_segment codes';

		%if &error_codes eq 415 %then
			%let err_str = 'ETL File has been previously processed';

		%if &error_codes eq 932 %then
			%let err_str = 'Failed to write transaction data into RDBMS';

		%if &error_codes eq 933 %then
			%let err_str = 'No records were imported into the transaction table';

		proc Sql noprint;
			select count(*) into: errcount from &work_lib..errors where error_cd ne .;
		quit;

		%if &errcount. > 0 %then
			%do;

				proc Sql noprint;
					select distinct error_message into: err_str from &work_lib..errors;
				quit;

			%end;

		data _NULL_;
			FILE resXml;
			put '<?xml version="1.0" encoding="UTF-8"?>';
			put '<SASResponse xmlns="http://xml.common.pacman.tetris.ideas.com/schema/typethreepopulation/response/v1">';
			put "<ResponseHeader> <PropertyId>&property_Id</PropertyId>" @;
			put "<operationName>typethreepopulation</operationName>" @;
			put "<requestId>&request_Id</requestId>";
		run;

		%if "&GL_ABORT_FLAG" eq "1" or &syscc > 4 or &etl_status = 1 %then
			%do;
				%let GL_ABORT_FLAG =1;
				%let syscc = 5;
				%let etl_status = 1;
				%put &error_codes;

				%if &error_codes ne  415 %then
					%do;
						%let  xptFileName=%substr(&etl_file_name., 1, %length(&etl_file_name.)-4).xpt;

						proc Cport lib=&work_lib.                                                                                                                                                                                                                                     
							file="&xptFileName";
						run;

					%end;
			%end;

		data _NULL_;
			FILE resXml MOD;
			set &work_lib..errors;

			if _N_ > 0 then
				put "<ErrorMessage>&err_str.</ErrorMessage>";
		run;

		data _NULL_;
			FILE resXml MOD;
			put "</ResponseHeader></SASResponse>";
		run;

		%let app_end_time=%sysfunc(datetime());
		%let runTime=%sysfunc(round(&app_end_time-&app_start_time, .05));
		%let  prepared_dttm_temp=%sysfunc(putn(&prep_dttm,datetime20.));
		%let  snapshot_dttm_temp=%sysfunc(putn(&snap_dttm,datetime20.));

		%if "&GL_ABORT_FLAG" eq "1" or &syscc > 4 or &etl_status = 1 %then
			%let Status_desc=Failed;
		%else
			%do;
				%let Status_desc=Successful;

				%if %upcase(&Sas_Debug.)=FALSE %then
					%do;

						proc printto log="&logfile" new;
						run;

					%end;
			%end;

		%if &error_codes eq 415 %then
			%let Status_desc=SUCCESSFUL - ETL File Previously Processed;
		Options nomacrogen NoSymbolgen nomlogic nomprint nomfile;
		%PUT ---------------------------------------------------------------------;
		%put --------------------   Type 3 population Summary  -------------------;
		%put ---------------------------------------------------------------------;
		%put;
		%Put -         Property Info: &property_desc.;
		%put -     Population Status: &status_desc.;
		%Put -   Population Run time: &runTime. seconds;
		%Put -             Load Type: &loadtype.;
		%Put -             File Type: &file_type.;
		%put -   Snap Shot Date/Time: &snapshot_dttm_temp.;
		%put -    Prepared Date/Time: &prepared_dttm_temp.;
		%put -      Past Window Size: &past_window_size.;
		%put -    Future Window Size: &future_window_size.;
		%put -            Request ID: &requestId.;
		%put;
		%put -   Population ETL File: &etl_file_name.;
		%put -         Sas Data Path: &saspath.;
		%put -    Mkt Partition Path: &rm_part_perm_path.;
		%put -       Population Path: &rm_data_perm_path.;
		%put -     Work Library Path: &work_path.;
		%put -     Work Library Name: &work_lib;
		%put -         Memlib Option: &memlib_option;
		%put -     Use Local Storage: &population_use_local_stg;
		%put;
		%put -       Database Server: &tenant_server.;
		%put -       Server Instance: &server_instance.;
		%put -         Database Name: &tenant_db.;
		%put -         Database User: &tenant_user.;
		%put -         Database Port: &port.;
		%put -  DirectReservationNightPopulation: &DirectReservationNightPopulation.;
		%put -      UseMemlib: &useMemlib.;
		%put;

		data _null_;
			set &work_lib..performance;

			if _n_=1 then
				do;
					put '*********************************************************************';
					put '***************  Type 3 Population Macro Summary *******************';
					put '*********************************************************************';
					put ' ';
					put  @1 '-    Macro Name                        Status            Run Time(seconds)';
				end;

			put @ 1'-' @ 5  macro_name    @ 40  macro_status  @ 60    macro_run_time_seconds;
		run;

		%if &etl_status. = 1 and %ideas_util_nobs(&work_lib..errors) > 0 %then
			%do;

				data _null_;
					set &work_lib..errors;

					if _n_=1 then
						do;
							put '*********************************************************************';
							put '***************** Type 3 Population Error Summary *******************';
							put '*********************************************************************';
							put ' ';
							put  @ 1 '- Error message';
							PUT @ 1  '---------------------------------------------------------------------';
						end;

					put @ 3  error_message;
				run;

			%end;

		%PUT ---------------------------------------------------------------------;
		%PUT ------------------- Ending Type 3 Population ------------------------;
		%PUT ---------------------------------------------------------------------;

		%if &population_use_local_stg. eq YES and  &GL_ABORT_FLAG. eq 0 and &syscc <= 4 %then
			%do;

				proc Copy in=rm_data out=rm_datap 
					CLONE 
					CONSTRAINT=YES 
					INDEX=YES;
					select mkt_accom_los_inventory;
				run;

				proc Copy in=rm_part out=rm_partp 
					CLONE 
					CONSTRAINT=YES 
					INDEX=YES;
				run;

				proc Datasets library = rm_data kill memtype=data nolist noprint;
				quit;

				proc Datasets library = rm_part kill memtype=data nolist noprint;
				quit;

			%end;

		%if &work_lib. ne work %then
			%do;

				proc Datasets library = &work_lib. kill memtype=data nolist noprint;
				quit;

			%end;
%mend ideas_type3_load;