%macro ideas_pace_transactions(rc=)/store;
    options nolabel;
    %let ONLY_TRANS_MS_CLAUSE=and mkt_seg_id not in (select mkt_seg_id from &work_lib..group_market_segments);

    %let WHERE_POST_ARRIVAL_CANCELLATION=and (cancellation_dt <= a.Arrival_DT or cancellation_dt=.);
    %let canceledReservationsWhere=;
    %if %upcase(&ignoreCanceledReservations.)=TRUE %then
            %let canceledReservationsWhere=&WHERE_POST_ARRIVAL_CANCELLATION.;

    proc Sql;
        create table &work_lib..accom_type_temp as 
            select accom_type_id, accom_type_code 
                from tenant.accom_type 
                    where accom_type_capacity >0 
                        order by accom_type_code;
        create table &work_lib..transaction_accom_types as 
            select distinct a.accom_type_id, b.accom_type_code 
                from tenant.Individual_Trans as a 
                    inner join tenant.accom_type as b 
                        on a.accom_type_id=b.accom_type_id 
                    order by b.accom_type_code;
    quit;

    proc sort data=&work_lib..accom_type_temp;
    by accom_type_code;
    quit;
    proc sort data=&work_lib..transaction_accom_types;
    by accom_type_code;
    quit;

    data &work_lib..accom_types(keep=accom_type_code accom_type_id);
        merge &work_lib..transaction_accom_types (in=tranin) &work_lib..accom_type_temp (in=accomin);
        by accom_type_code;

        if tranin =1 then
            output &work_lib..accom_types;
    run;

    proc sql;
            create table &work_lib..group_market_segments as
                select a.mkt_seg_id
                    from tenant.Mkt_Seg_Details_Proposed as a
                        where a.business_Type_ID=1 or a.booking_block_pc>0
                            union
                        select b.mkt_seg_id
                            from tenant.Mkt_Seg_Details as b
                                where b.business_Type_ID=1 or b.booking_block_pc>0;
    run;


    %put calculating the group invalid percent;
    proc sql;
        select count(*), sum(case when c.booking_dt= . or c.booking_dt>c.start_dt then 1 else 0 end) into :Total_Groups, :Group_Invalid_count
            from tenant.group_block as b
                inner join tenant.Group_master as c
                    on b.group_id=c.group_id
                where upcase(c.group_status_code)="DEFINITE" and
                    c.start_dt<&first_snapshot_dt. and
                    c.mkt_seg_id in (select mkt_seg_id from &work_lib..group_market_segments) and
                    b.occupancy_dt between &minimum_occupancy_dt. and &maximum_occupancy_dt. and b.occupancy_dt between c.start_dt and c.end_dt
                order by c.group_id, b.occupancy_dt, c.start_dt;

        /* Get the count of group MS reservations with non-null inv_block_code */
        select count(DISTINCT Reservation_Identifier) into: valid_group_trans from tenant.reservation_night as a
            where a.booking_dt<&first_snapshot_dt. and a.mkt_seg_id in (select mkt_seg_id from &work_lib..group_market_segments);
    quit;

    %if (&Total_Groups. * 100) lt (&valid_group_trans.) %then
        %let useGroupsAsTrans=TRUE;

    %if %upcase(&Accom_Type_Option.)=STAYED %then
        %do;
            %if &Total_Groups. eq 0  or %upcase(&RequestedMarketSegments.) ne ALL %then
                    %do;
                    %put if Group Transactions are absent;
                        proc sql;
                            select count(*) into: group_reservation_count from tenant.reservation_night as a
                                    where a.booking_dt<&first_snapshot_dt. and a.mkt_seg_id in (select mkt_seg_id from &work_lib..group_market_segments);
                            select count(*) into: reservation_count from tenant.reservation_night as a
                                                            where a.booking_dt<&first_snapshot_dt.;
                        quit;
                        %put &=group_reservation_count;
                        %put &=reservation_count;
                        /*
                            Assuming a 1% tolerance, i.e., if <1% of total reservations are group reservations
                            Then we assume that the property is a transient only property
                            Hence, the absence of pace_group_block would be ignored
                        */
                        %if %upcase(&useGroupsAsTrans.)=TRUE OR ((&group_reservation_count. * 100) le (&reservation_count.)) or %upcase(&RequestedMarketSegments.) ne ALL %then %do;
                            %put Assuming Transient ONLY property and set group_difference to 0;
                            %LET Group_Booked_Difference_Percent=0.0031416;
                        %end;
                        %else %do;
                            %LET Group_Booked_Difference_Percent=100;
                        %end;
                    %end;
            %else %do;
                %LET Group_Booked_Difference_Percent=%sysfunc(round(%SYSEVALF(&Group_Invalid_count./&Total_Groups.),.01));
            %end;
        %end;

    %put &=Group_Booked_Difference_Percent;

    /* KEEP ONLY THOSE MKT SEGS WHICH ARE TO BE UPDATED */
    %if %upcase(&RequestedMarketSegments.) ne ALL %then
        %do;
            %paceConvertPsvToDataset(&work_lib..requiredMsIds,&requestedmarketsegments.);
            %let onlyRequiredMktSegs = and mkt_seg_id in (select msid from &work_lib..requiredMsIds) and "&useGroupsAsTrans."="FALSE";
            proc sql;
                drop table &work_lib..group_market_segments;
                create table &work_lib..group_market_segments as
                select a.mkt_seg_id
                    from tenant.Mkt_Seg_Details_Proposed as a
                        where (a.business_Type_ID=1 or a.booking_block_pc>0) &onlyRequiredMktSegs.
                                union
                select b.mkt_seg_id
                    from tenant.Mkt_Seg_Details as b
                        where (b.business_Type_ID=1 or b.booking_block_pc>0) &onlyRequiredMktSegs.;
                create table &work_lib..trans_market_segments as
                select msid as mkt_seg_id from &work_lib..requiredMsIds
                    except
                select mkt_seg_id from &work_lib..group_market_segments;
            run;
        %end;

    %let mktSegWhereClause=;
    %if %upcase(&useGroupsAsTrans.)=FALSE %then
        %let mktSegWhereClause=&ONLY_TRANS_MS_CLAUSE.;
    %if %upcase(&RequestedMarketSegments.) ne ALL %then
            %let mktSegWhereClause=and mkt_seg_id in (select mkt_seg_id from &work_lib..trans_market_segments);
    %let offset = 1;
    %if %upcase(&isRunningAsPartOfPMSRevamp.)=TRUE %then
        %let offset = 2;
    proc sql;
        create table &work_lib..Transactions_Transient_all as
            select "Transient" as Transaction_Type format=$15., a.property_id as property_id length=4, a.reservation_identifier as reservation_identifier format=$50., a.individual_status, 
                min(a.booking_dt,a.Arrival_DT) as booking_dt format=date9., a.Arrival_DT, a.departure_dt, a.Departure_Dt-a.Arrival_DT as los length=3 format=5., 1 as solds , 
            case
                when individual_status in ("XX","CX") then max( min(a.booking_dt,a.Arrival_DT,&first_snapshot_dt.-&offset.), min(a.cancellation_dt,a.Arrival_DT,&first_snapshot_dt.-&offset.))
                else .
            end
        as cancellation_dt format=date9.,
            case
                when individual_status in ("XX","CX","NS") then (a.rate_value*(a.Departure_Dt-a.Arrival_DT))
                when &TotalRateEnabled. eq 1 and &IncludeDepartureAdjustment. eq 0 then a.total_rate_value
                when &TotalRateEnabled. eq 0 and &IncludeDepartureAdjustment. eq 1 then a.room_revenue+pdr.room_revenue
                when &TotalRateEnabled. eq 1 and &IncludeDepartureAdjustment. eq 1 then a.total_rate_value+pdr.rate_value
                else a.room_revenue
            end
        as Room_revenue length=8,
            case
                when individual_status in ("XX","CX","NS") then (a.rate_value*(a.Departure_Dt-a.Arrival_DT))
                else a.total_revenue 
            end
        as total_revenue length=8,
            a.accom_type_id as accom_type_id length=6, a.accom_type_id as stayed_accom_type_id length=6, a.mkt_seg_id as mkt_seg_id length=3, b.accom_type_id as booked_accom_type_id length=6, 
            a.Food_revenue, a.Beverage_revenue, a.Telecom_revenue, a.other_revenue, a.rate_value
        from tenant.Individual_Trans as a
            left join (select reservation_identifier, room_revenue, rate_value
                from tenant.post_departure_revenue) as pdr
            on a.reservation_identifier=pdr.reservation_identifier
            left join &work_lib..accom_types as b 
                on a.booked_accom_type_code=b.accom_type_code
            where a.booking_dt<&first_snapshot_dt.-1 &mktSegWhereClause.
            &canceledReservationsWhere.
                order by a.reservation_identifier, a.Arrival_DT;
    quit;

    /* PROCESS PGB/GM IF GROUPS AREN'T TRANS*/
    %if %upcase(&useGroupsAsTrans.)=FALSE %then
        %ideas_pace_grp_trans(rc=&rc.);

    %if %upcase(&RequestedRoomTypeOption.)=ALL and %upcase(&Accom_Type_Option.)=STAYED %then
        %do;

            proc sql;
                create table &work_lib..Transactions_Transient_temp as 
                    select a.* 
                        from &work_lib..Transactions_Transient_all a 
                            where ((a.Arrival_DT+(a.Departure_Dt-a.Arrival_DT) between &minimum_occupancy_dt. and &maximum_occupancy_dt. ) or 
                                (a.Arrival_DT between &minimum_occupancy_dt. and &maximum_occupancy_dt. ) or 
                                (a.departure_Dt between &minimum_occupancy_dt. and &maximum_occupancy_dt. ) or
                                ( a.Arrival_DT < &minimum_occupancy_dt. and a.departure_dt > &maximum_occupancy_dt.) ) 
                            order by a.reservation_identifier, a.Arrival_DT;
            quit;

            Data &work_lib..Transactions_Transient_booked;
                set &work_lib..Transactions_Transient_temp;

                if booked_accom_type_Id ne . and booked_accom_type_Id ne stayed_accom_type_id then
                    do;
                        accom_type_id=booked_accom_type_id;
                        book_difference=1;
                    end;
                else book_difference=0;
            run;

            proc Sql noprint;
                select count(*) 
                    into: Booking_vs_occupied_Differences from &work_lib..Transactions_Transient_temp where 
                    booked_accom_type_Id ne stayed_accom_type_id &mktSegWhereClause.;
                select count(*) 
                    into: Total_reservations from &work_lib..Transactions_Transient_temp where 1=1 &mktSegWhereClause.;
            quit;

            %LET Booked_Stayed_Difference_Percent=%sysfunc(round(%SYSEVALF(&Booking_vs_occupied_Differences./&Total_reservations.),.01));

            proc sql;
                create table &work_lib..Transaction_Arrival_Stats as 
                    select Arrival_DT, count(*) as book_diff format=7., min(booking_dt) as min_booking_dt format=date9.
                        from &work_lib..Transactions_Transient_booked
                            where book_difference>0
                                group by Arrival_DT 
                                    order by Arrival_DT;
            quit;

            Data _null_;
                Retain Save_Arrival_DT 0;
                Retain Booked_minimum_occupancy_dt;
                Retain Consecutive_Count;
                Format Save_Arrival_DT Booked_minimum_occupancy_dt Date9.;
                Format Booked_Stayed_Accom_Differences $3.;
                Set &work_lib..Transaction_Arrival_Stats;

                IF _N_=1 THEN
                    DO;
                        Save_Arrival_DT=Arrival_DT-1;
                        Booked_minimum_occupancy_dt=Arrival_Dt;
                        Consecutive_Count=0;
                    END;

                IF Save_Arrival_DT+1=Arrival_DT THEN
                    Consecutive_Count=Consecutive_Count+1;
                ELSE
                    DO;
                        Booked_minimum_occupancy_dt=Arrival_DT;
                        Consecutive_Count=1;
                    END;

                /* set the consecutive count to #consecutive days that there are reservations with booked room types different than stayed room type */
                /* if the threshold is met set min accoupancy date to arrival date of the reservation that meets the threshold */
                if Consecutive_Count=1 then
                    do;
                        Booked_Stayed_Accom_Differences="YES";
                        call symputx('Booked_minimum_occupancy_dt',Booked_minimum_occupancy_dt);
                        call symputx('Booked_Stayed_Accom_Differences',Booked_Stayed_Accom_Differences);
                        stop;
                    end;

                Save_Arrival_DT=Arrival_DT;
            run;

        %end;
    %else %if %upcase(&RequestedRoomTypeOption.)=ALL and %upcase(&Accom_Type_Option.)=BOOKED %then
        %do;

            Data &work_lib..Transactions_Transient_all;
                set &work_lib..Transactions_Transient_all;

                if booked_accom_type_Id ne . and booked_accom_type_Id ne stayed_accom_type_id then
                    do;
                        accom_type_id=booked_accom_type_id;
                    end;
            run;

        %end;

    %let group_trans_ds=&work_lib..Group_Transactions_exploded;
    %if %upcase(&useGroupsAsTrans.)=TRUE %then
        %let group_trans_ds=;

    data &work_lib..Transactions_All;
        set &work_lib..Transactions_Transient_all &group_trans_ds.;
    run;

    proc sort data=&work_lib..Transactions_All;
        by reservation_identifier Arrival_DT;
    run;

    data &work_lib..Transactions_Combined_for_los1 
        (keep=property_id Transaction_Type reservation_identifier individual_status booking_dt Arrival_DT departure_dt los solds 
        cancellation_dt accom_type_id mkt_seg_id Room_revenue Food_revenue Beverage_revenue Telecom_revenue other_revenue Total_Revenue );
        Retain save_property_id save_reservation_identifier save_individual_status save_booking_dt Save_Arrival_DT save_departure_dt save_los save_solds 
            save_cancellation_dt save_accom_type_id save_mkt_seg_id save_Room_revenue save_Food_revenue save_Beverage_revenue save_Telecom_revenue save_other_revenue save_Total_Revenue;
        attrib save_property_id length=8;
        attrib save_los length=8;
        attrib save_solds length=8;
        attrib save_booking_dt format=date9.;
        attrib save_accom_type_id length=8;
        attrib save_mkt_seg_id length=8;
        format save_reservation_identifier $50.;
        set &work_lib..Transactions_all;
        by reservation_identifier Arrival_DT;

        if _n_=1 or first.reservation_identifier then
            do;
                save_property_id=property_id;
                save_reservation_identifier=reservation_identifier;
                save_individual_status=individual_status;
                save_booking_dt=booking_dt;
                Save_Arrival_DT=Arrival_DT;
                save_departure_dt=departure_dt;
                save_cancellation_dt=cancellation_dt;
                save_mkt_seg_id=mkt_seg_id;
                save_accom_type_id=accom_type_id;
                save_solds=solds;
                save_los=0;
                save_Room_Revenue=0;
                save_Beverage_revenue=0;
                save_Telecom_revenue=0;
                save_Food_Revenue=0;
                save_other_Revenue=0;
                save_Total_Revenue=0;
            end;

        save_departure_dt=departure_dt;
        save_Room_Revenue=save_Room_Revenue+Room_revenue;
        save_Beverage_revenue=save_Beverage_revenue+Beverage_revenue;
        save_Telecom_revenue=save_Telecom_revenue+Telecom_revenue;
        save_Food_Revenue=save_Food_Revenue+Food_Revenue;
        save_other_Revenue=save_other_Revenue+other_revenue;
        save_Total_Revenue=save_Total_Revenue+Total_Revenue;
        save_los=save_los+los;

        if last.reservation_identifier then
            do;
                property_id=save_property_id;
                reservation_identifier=save_reservation_identifier;
                individual_status=save_individual_status;
                booking_dt=save_booking_dt;
                Arrival_DT=Save_Arrival_DT;
                departure_dt=save_departure_dt;
                cancellation_dt=save_cancellation_dt;
                mkt_seg_id=save_mkt_seg_id;
                accom_type_id=save_accom_type_id;
                los=save_los;
                solds=save_solds;
                Room_Revenue=save_Room_revenue;
                Beverage_revenue=save_Beverage_revenue;
                Telecom_revenue=save_Telecom_revenue;
                Food_Revenue=save_Food_Revenue;
                other_Revenue=save_other_revenue;
                Total_Revenue=save_Total_Revenue;
                output &work_lib..Transactions_Combined_for_los1;
            end;
    run;

    proc Sql;
        create table &work_lib..Transactions_Combined_for_los as 
            select a.* from &work_lib..Transactions_Combined_for_los1 as a 
                where 
                    ((a.Arrival_DT+a.los between &minimum_occupancy_dt. and &maximum_occupancy_dt. ) or 
                    (a.Arrival_DT between &minimum_occupancy_dt. and &maximum_occupancy_dt. ) or 
                    (a.departure_Dt between &minimum_occupancy_dt. and &maximum_occupancy_dt. ) or
                    ( a.Arrival_DT < &minimum_occupancy_dt. and a.departure_dt > &maximum_occupancy_dt.) ) 
                order by a.property_id, a.mkt_seg_id, a.Accom_Type_ID, a.Arrival_DT, a.los;
    quit;

    %if %upcase(&useReservationNight.) eq TRUE %then %do;
        %ideas_pace_resv_trans(totalRateEnableForResNight=&TotalRateEnabled., includePostDepForResNight=&IncludeDepartureAdjustment.);
    %end;
    %else %do;
        data &work_lib..Transactions_exploded;
            format occupancy_dt date9.;
            format number_nights 5.;
            set &work_lib..Transactions_all (where=(((Arrival_DT+los between &minimum_occupancy_dt. and &maximum_occupancy_dt. ) or
                (Arrival_DT between &minimum_occupancy_dt. and &maximum_occupancy_dt. ) or
                (departure_Dt between &minimum_occupancy_dt. and &maximum_occupancy_dt. ) or
                (Arrival_DT < &minimum_occupancy_dt. and departure_dt > &maximum_occupancy_dt.))));

            if Arrival_DT < Departure_Dt then
                do;
                    Number_Nights=Departure_Dt-Arrival_DT;
                    Room_Revenue=room_revenue/Number_Nights;
                    Food_Revenue=Food_revenue/Number_Nights;
                    Other_Revenue=Other_revenue/Number_Nights;
                    beverage_Revenue=0;
                    telecom_Revenue=0;
                    total_revenue=total_revenue/number_nights;
                    los = 1;

                    do occupancy_dt = Arrival_DT to min(Departure_Dt -1, &maximum_occupancy_dt.);
                        output &work_lib..Transactions_exploded;
                    end;
                end;
            else
                do;
                    occupancy_dt=Arrival_DT;
                    los = 0;
                    number_nights=0;
                    output &work_lib..Transactions_exploded;
                end;
        run;

    %end;


    proc sort data =&work_lib..Transactions_exploded;
        by mkt_seg_id accom_type_id booking_dt occupancy_dt;
    run;

    %let ProcessedNumberPaceDays=&RequestedNumberPaceDays.;
    %let minimum_snapshot_dt=&first_snapshot_dt.-&ProcessedNumberpaceDays.;

    proc Sql;
        create table &work_lib..accom_zeros as 
            select property_id length=4, Occupancy_DT format=date9., 
                case
                    when occupancy_dt lt &first_snapshot_dt. then dhms(occupancy_dt-(&ProcessedNumberpaceDays.-1),0,0,'03:00:00't) 
                    else dhms(&first_snapshot_dt.-&ProcessedNumberpaceDays.,0,0,'03:00:00't) 
                end
            as capture_dttm format=datetime20., accom_type_id length=6, 0 as rooms_sold length=8 
                from tenant.Accom_Activity 
                    where occupancy_dt between &minimum_occupancy_dt. and &maximum_occupancy_dt. 
                        order by property_id, accom_type_id, occupancy_dt, capture_dttm;
    quit;

    proc Sql;
        create table &work_lib..accom_capacity as 
            select property_id length=4, Occupancy_DT format=date9., 
                accom_type_id length=6, Accom_Capacity length=4 ,Rooms_Not_Avail_Maint length=4,Rooms_Not_Avail_Other length=4 
            from tenant.Accom_Activity 
                where occupancy_dt between &minimum_occupancy_dt. and &maximum_occupancy_dt. 
                    order by accom_type_id, occupancy_dt;
    quit;

    proc Sql;
        create table &work_lib..total_capacity as 
            select property_id length=4, Occupancy_DT format=date9., 
                Total_Accom_Capacity length=8 ,Rooms_Not_Avail_Maint length=8,Rooms_Not_Avail_Other length=8
            from tenant.Total_Activity
                where occupancy_dt between &minimum_occupancy_dt. and &maximum_occupancy_dt. 
                    order by occupancy_dt;
    quit;

    %if &Backfill_Correction_count. =0 %then
        %do;

            proc Sql noprint;
                select distinct mkt_seg_id, count(distinct(mkt_seg_id)) 
                    into: mssortcommalist SEPARATED by ',',:mscommacount from &work_lib..Transactions_All 
                order by mkt_seg_id;
                select distinct mkt_seg_id, count(distinct(mkt_seg_id)) 
                    into: mssortlist SEPARATED by '|',:mscount from &work_lib..Transactions_All 
                order by mkt_seg_id;
                select distinct accom_type_id, count(distinct(accom_type_id)) 
                    into: accomsortcommalist SEPARATED by '|',:accomcount from &work_lib..accom_types
                order by accom_type_id;
                select distinct mkt_seg_id into: groupmktseglist SEPARATED by '|' from &work_lib..group_market_segments
                    order by mkt_seg_id;
                select distinct mkt_seg_id , count(*) into:groupmslist   SEPARATED by '|', :groupupdatecount from &work_lib..group_market_segments
                    order by mkt_seg_id;
        %end;
    %else
        %do;

            proc Sql noprint;
                select distinct mkt_seg_id, count(distinct(mkt_seg_id)) 
                    into: mssortcommalist SEPARATED by ',',:mscommacount from tenant.mkt_seg  
                     where mkt_seg_id ne 1
                order by mkt_seg_id;
                select distinct mkt_seg_id, count(distinct(mkt_seg_id)) 
                    into: mssortlist SEPARATED by '|',:mscount from tenant.mkt_seg  
                     where mkt_seg_id ne 1     
                order by mkt_seg_id;
                select distinct accom_type_id, count(distinct(accom_type_id)) 
                    into: accomsortcommalist SEPARATED by '|',:accomcount from &work_lib..accom_types
                order by accom_type_id;
                select distinct mkt_seg_id into: groupmktseglist SEPARATED by '|' from &work_lib..group_market_segments
                    order by mkt_seg_id;
                select distinct mkt_seg_id , count(*) into:groupmslist   SEPARATED by '|', :groupupdatecount from &work_lib..group_market_segments 
                    order by mkt_seg_id;
            quit;

        %end;

    %if %symexist(groupmktseglist)=0 or %length(&groupmktseglist.)=0  %then
        %let groupmktseglist = 999999999999;

    %ideas_pace_commonIds(&mssortlist.,&requestedmarketsegments.);

    Data &work_lib..mkt_accom_Zeros (keep=property_id mkt_seg_id accom_type_id occupancy_dt capture_dttm rooms_sold arrivals departures no_shows cancellations room_revenue food_revenue total_revenue);
        Retain property_id mkt_seg_id accom_type_id occupancy_dt capture_dttm rooms_sold arrivals departures no_shows cancellations room_revenue food_revenue total_revenue;
        attrib rooms_sold arrivals departures no_shows cancellations room_revenue food_revenue total_revenue length=8;
        attrib mkt_seg_id length=3;
        attrib accom_type_id length=6;
        attrib property_id length=4 format=11.;
        attrib occupancy_dt length=8 format=date9.;
        attrib capture_dttm length=8 format=datetime20.;
        rooms_sold=0;
        arrivals=0;
        departures=0;
        no_shows=0;
        cancellations=0;
        room_revenue=0;
        food_revenue=0;
        total_revenue=0;

        do accomi=1 to &accomcount.;
            accom_type_num=scan("&accomsortcommalist.", accomi);

            do mktsegi=1 to &mscommacount.;
                mkt_seg_num=scan("&mssortcommalist.", mktsegi);

                do occupancy_date=&minimum_occupancy_dt. to &maximum_occupancy_dt.;
                    property_id=&property_id.;
                    mkt_seg_id=mkt_seg_num;
                    accom_type_id=accom_type_num;
                    occupancy_dt=occupancy_date;

                    if occupancy_dt lt &first_snapshot_dt. then
                        capture_dttm=dhms(occupancy_dt-(&ProcessedNumberpaceDays.-1),0,0,'03:00:00't);
                    else capture_dttm=dhms(&first_snapshot_dt.-(&ProcessedNumberpaceDays.),0,0,'03:00:00't);
                    output &work_lib..mkt_accom_Zeros;
                end;
            end;
        end;
    run;

    proc sort data =&work_lib..mkt_accom_zeros;
        by property_id mkt_seg_id accom_type_id occupancy_dt capture_dttm;
    run;

    data &work_lib..Transaction_for_los_zeros 
        (keep=property_id Transaction_Type reservation_identifier individual_status booking_dt Arrival_DT departure_dt los 
        cancellation_dt accom_type_id mkt_seg_id Room_revenue Food_revenue Beverage_revenue Telecom_revenue other_revenue Total_Revenue );
        Retain save_property_id save_reservation_identifier save_individual_status save_booking_dt Save_Arrival_DT save_departure_dt save_los 
            save_cancellation_dt save_accom_type_id save_mkt_seg_id save_Room_revenue save_Food_revenue save_Beverage_revenue save_Telecom_revenue save_other_revenue save_Total_Revenue;
        attrib save_property_id length=4;
        attrib save_los length=3;
        attrib save_booking_dt format=date9.;
        attrib save_accom_type_id length=6;
        attrib save_mkt_seg_id length=3;
        format save_reservation_identifier $50.;
        set &work_lib..Transactions_All;
        by reservation_identifier Arrival_DT;

        if _n_=1 or first.reservation_identifier then
            do;
                save_property_id=property_id;
                save_reservation_identifier=reservation_identifier;
                save_individual_status=individual_status;
                save_booking_dt=booking_dt;
                Save_Arrival_DT=Arrival_DT;
                save_departure_dt=departure_dt;
                save_cancellation_dt=cancellation_dt;
                save_mkt_seg_id=mkt_seg_id;
                save_accom_type_id=accom_type_id;
                save_los=0;
                save_Room_Revenue=0;
                save_Beverage_revenue=0;
                save_Telecom_revenue=0;
                save_Food_Revenue=0;
                save_other_Revenue=0;
                save_Total_Revenue=0;
            end;

        save_departure_dt=departure_dt;
        save_Room_Revenue=save_Room_Revenue+Room_revenue;
        save_Beverage_revenue=save_Beverage_revenue+Beverage_revenue;
        save_Telecom_revenue=save_Telecom_revenue+Telecom_revenue;
        save_Food_Revenue=save_Food_Revenue+Food_Revenue;
        save_other_Revenue=save_other_Revenue+other_revenue;
        save_Total_Revenue=save_Total_Revenue+Total_Revenue;
        save_los=save_los+los;

        if last.reservation_identifier then
            do;
                property_id=save_property_id;
                reservation_identifier=save_reservation_identifier;
                individual_status=save_individual_status;
                booking_dt=save_booking_dt;
                Arrival_DT=Save_Arrival_DT;
                departure_dt=save_departure_dt;
                cancellation_dt=save_cancellation_dt;
                mkt_seg_id=save_mkt_seg_id;
                accom_type_id=save_accom_type_id;
                los=save_los;
                Room_Revenue=save_Room_revenue;
                Beverage_revenue=save_Beverage_revenue;
                Telecom_revenue=save_Telecom_revenue;
                Food_Revenue=save_Food_Revenue;
                other_Revenue=save_other_revenue;
                Total_Revenue=save_Total_Revenue;
                output &work_lib..Transaction_for_los_zeros;
            end;
    run;

    proc Sql;
        create table &work_lib..mkt_accom_los_Zeros as 
            select distinct property_id as property_id length=4, Arrival_DT format=date9., mkt_seg_id as mkt_seg_id length=3, 
                accom_type_id as accom_type_id length=6, los as los length=3, 
            case
                when (Arrival_DT<(&first_snapshot_dt.-&ProcessedNumberpaceDays.)) then dhms(&first_snapshot_dt.-&ProcessedNumberpaceDays.,0,0,'03:00:00't)
                when (Arrival_DT<&first_snapshot_dt.) then dhms(min(Arrival_DT-(&ProcessedNumberpaceDays.-1),&first_snapshot_dt.-&ProcessedNumberpaceDays.),0,0,'03:00:00't)
                when (Arrival_DT>=&first_snapshot_dt.) then dhms(min(Arrival_DT-(&ProcessedNumberpaceDays.-1), &first_snapshot_dt.-&ProcessedNumberpaceDays.),0,0,'03:00:00't)
                else dhms(&first_snapshot_dt.-&ProcessedNumberpaceDays.,0,0,'03:00:00't)
            end 
        as capture_dttm format=datetime20., 
            0 as arrivals length=8 , 0 as no_shows length=8 , 0 as cancellations length=8 , 0 as room_revenue length=8 , 0 as total_revenue length=8 
        from &work_lib..Transaction_for_los_zeros where Arrival_DT+los>=&minimum_occupancy_dt.
            order by property_id, mkt_seg_id, Accom_Type_ID, Arrival_DT, los, capture_dttm;
    quit;

    %if &syscc >4 %then
        %let return_code=200;
    %else 
        %if %sysfunc(exist(&work_lib..accom_capacity)) and %ideas_util_nobs(&work_lib..accom_capacity) > 0 and 
            %sysfunc(exist(&work_lib..accom_types)) and %ideas_util_nobs(&work_lib..accom_types) > 0 and 
        %sysfunc(exist(&work_lib..total_capacity)) and %ideas_util_nobs(&work_lib..total_capacity ) > 0 and
        %sysfunc(exist(&work_lib..mkt_accom_Zeros)) and %ideas_util_nobs(&work_lib..mkt_accom_Zeros ) > 0 and
        %sysfunc(exist(&work_lib..accom_zeros)) and %ideas_util_nobs(&work_lib..accom_zeros) > 0 and
        %sysfunc(exist(&work_lib..mkt_accom_los_Zeros)) and %ideas_util_nobs(&work_lib..mkt_accom_los_Zeros) > 0 and
        %sysfunc(exist(&work_lib..Transactions_exploded)) and %ideas_util_nobs(&work_lib..Transactions_exploded) > 0 and 
        %sysfunc(exist(&work_lib..Transactions_Combined_for_los)) and %ideas_util_nobs(&work_lib..Transactions_Combined_for_los) > 0 and 
        %sysfunc(exist(&work_lib..Transactions_all)) and %ideas_util_nobs(&work_lib..Transactions_all) > 0 and
        (%symexist(updatemslist)=1) and %length(&updatemslist.) > 0
        %then
        %let return_code=0;
    %else %do;
        %let return_code=100;
    %end;
    options label;
%mend ideas_pace_transactions;
%macro paceConvertPsvToDataset(libname,input) /store    ;
    data &libname.;
        stop;
        attrib msId length=8;
        msId=10;
    run;

    %let mktsegi=1;

    %do %while (%scan(&input.,&mktsegi.,|) ne );
        data _null_;
            call symputx ('mkt_seg',%scan(&input.,&mktsegi.,|),'l');
        run;
        proc sql noprint;
            insert into &libname. values (&mkt_seg);
        run;

    %let mktsegi = %sysevalf(&mktsegi. + 1);
    %end;
    proc sort data=&libname.;
        by msId;
    run;
%mend;
%macro ideas_pace_commonIds(allMsIds, requiredIds) /store;
    %put &=requiredIds;
    %if %upcase(&requiredIds.) eq ALL %then %do;
            %let updatemslist=&mssortlist.;
        %end;
    %paceConvertPsvToDataset(&work_lib..allMsids,&allMsIds.);
    %paceConvertPsvToDataset(&work_lib..requiredIds,&requiredIds.);
    proc sql noprint;
        select distinct a.msid into :updatemslist separated by '|' from &work_lib..allmsids a join
            &work_lib..requiredIds b on a.msid = b.msid;
    run;

    %put &=updatemslist;
%mend;