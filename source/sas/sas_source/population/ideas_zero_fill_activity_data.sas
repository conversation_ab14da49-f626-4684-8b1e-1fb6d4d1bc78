%macro ideas_zero_fill_activity_data/store;
%let macroname=&SYSMACRONAME;

%ideas_macro_log (start_end=START, macroname=&macroname.);

	proc sql noprint;
	  create table &work_lib..tmp_mkt_seg as select distinct Mkt_Seg_ID from tenant.Mkt_seg where Property_ID EQ &property_id. and 
			 Status_ID EQ 1;
	quit;

	proc sql noprint;
	 create table &work_lib..zero_fill_occdate_accom_mkt as 
		select a.occupancyDt,a.accomID,b.Mkt_Seg_ID as mktSegId
		from &work_lib..tmp_mkt_seg as b, &work_lib..zero_fill_occdate_accom as a;
	quit;

	%if %sysfunc(exist(rm_data.Mkt_accom_inventory)) EQ 0 OR %ideas_util_nobs(rm_data.Mkt_accom_inventory) EQ 0 %then %do;

		proc sql noprint;
			select sum(Accom_Type_Capacity) into: TotalCapacity from tenant.Accom_Type where Property_ID EQ &property_id.
			and Status_ID EQ 1;
		quit;

		data rm_data.accom_inventory (index=(acc_idx=(Property_ID Accom_Type_ID Occupancy_DT)) drop=occupancyDt accomID Accom_Type_Capacity);
			set &work_lib..zero_fill_occdate_accom;
			attrib Property_ID length=6;
			attrib Occupancy_DT length=8;
			attrib Accom_Type_ID length=6;
			attrib Accom_Capacity Rooms_Not_Avail_Maint Rooms_Not_Avail_Other length=4;
			attrib Rooms_Sold Arrivals Departures No_Shows Cancellations Room_Revenue Total_Revenue Food_Revenue length=8;

			Property_ID = &property_id.;
			Occupancy_DT = occupancyDt;
			Accom_Type_ID = accomID;
			Accom_Capacity=Accom_Type_Capacity;
			Rooms_Not_Avail_Maint=0;
			Rooms_Not_Avail_Other=0;
			Rooms_Sold=0;
			Arrivals=0;
			Departures=0;
			No_Shows=0;
			Cancellations=0;
			Room_Revenue=0;
			Total_Revenue=0;
			Food_Revenue=0;
			output;
		run;

		data rm_data.Accom_inventory_pace (drop=occupancyDt accomID Accom_Type_Capacity);
			set &work_lib..zero_fill_occdate_accom;
			attrib Property_ID length=6;
			attrib Occupancy_DT length=8;
			attrib Accom_Type_ID length=6;
			attrib CAPTURE_DTTM length=8;
			attrib Accom_Capacity Rooms_Not_Avail_Maint Rooms_Not_Avail_Other length=4;
			attrib Rooms_Sold length=8;

			Property_ID = &property_id.;
			Occupancy_DT = occupancyDt;
			Accom_Type_ID = accomID;
			CAPTURE_DTTM = DATETIME();
			Accom_Capacity=Accom_Type_Capacity;
			Rooms_Not_Avail_Maint=0;
			Rooms_Not_Avail_Other=0;
			Rooms_Sold=0;
			output;
		run;

		data rm_data.Bde_accom_inventory (index=(acc_idx=(Property_ID Accom_Type_ID Occupancy_DT)) drop=occupancyDt accomID Accom_Type_Capacity);
			set &work_lib..zero_fill_occdate_accom;
			attrib Property_ID length=6;
			attrib Occupancy_DT length=8;
			attrib Accom_Type_ID length=6;
			attrib Accom_Capacity Rooms_Not_Avail_Maint Rooms_Not_Avail_Other length=4;
			attrib Rooms_Sold Arrivals Departures No_Shows Cancellations Room_Revenue Total_Revenue Food_Revenue length=8;

			Property_ID = &property_id.;
			Occupancy_DT = occupancyDt;
			Accom_Type_ID = accomID;
			Accom_Capacity=Accom_Type_Capacity;
			Rooms_Not_Avail_Maint=0;
			Rooms_Not_Avail_Other=0;
			Rooms_Sold=0;
			Arrivals=0;
			Departures=0;
			No_Shows=0;
			Cancellations=0;
			Room_Revenue=0;
			Total_Revenue=0;
			Food_Revenue=0;
			output;
		run;

		data rm_data.Total_inventory (index=(ttl_idx=(Property_ID Occupancy_DT)) drop=occupancyDt);
			set &work_lib..zero_fill_occdate;
			attrib Property_ID length=6;
			attrib Occupancy_DT length=8;
			attrib Total_Accom_Capacity Rooms_Not_Avail_Maint Rooms_Not_Avail_Other length=4;
			attrib Rooms_Sold Arrivals Departures No_Shows Cancellations Room_Revenue Total_Revenue Food_Revenue length=8;

			Property_ID = &property_id.;
			Occupancy_DT = occupancyDt;
			Total_Accom_Capacity=&TotalCapacity;
			Rooms_Not_Avail_Maint=0;
			Rooms_Not_Avail_Other=0;
			Rooms_Sold=0;
			Arrivals=0;
			Departures=0;
			No_Shows=0;
			Cancellations=0;
			Room_Revenue=0;
			Total_Revenue=0;
			Food_Revenue=0;
			output;
		run;

		data rm_data.Total_inventory_pace (drop=occupancyDt);
			set &work_lib..zero_fill_occdate;
			attrib Property_ID length=6;
			attrib Occupancy_DT Snapshot_dttm length=8;
			attrib Total_Accom_Capacity length=4;
			attrib Rooms_Sold length=8;

			Property_ID = &property_id.;
			Occupancy_DT = occupancyDt;
			Snapshot_dttm =  DATETIME();
			Total_Accom_Capacity=&TotalCapacity;
			Rooms_Sold=0;
			output;
		run;

		data rm_data.Bde_total_inventory (index=(ttl_idx=(Property_ID Occupancy_DT)) drop=occupancyDt);
			set &work_lib..zero_fill_occdate;
			attrib Property_ID length=6;
			attrib Occupancy_DT  length=8;
			attrib Total_Accom_Capacity Rooms_Not_Avail_Maint Rooms_Not_Avail_Other length=4;
			attrib Rooms_Sold Arrivals Departures No_Shows Cancellations Room_Revenue Total_Revenue Food_Revenue length=8;

			Property_ID = &property_id.;
			Occupancy_DT = occupancyDt;
			Total_Accom_Capacity=&TotalCapacity;
			Rooms_Not_Avail_Maint=0;
			Rooms_Not_Avail_Other=0;
			Rooms_Sold=0;
			Arrivals=0;
			Departures=0;
			No_Shows=0;
			Cancellations=0;
			Room_Revenue=0;
			Total_Revenue=0;
			Food_Revenue=0;
			output;
		run;

		data rm_data.Bde_mkt_accom_inventory (index=(mkt_idx=(Property_ID Accom_Type_ID MKT_SEG_ID Occupancy_DT)) drop=occupancyDt accomID mktSegId);
			set &work_lib..zero_fill_occdate_accom_mkt;
			attrib Property_ID MKT_SEG_ID Accom_Type_ID length=6;
			attrib Occupancy_DT Arrivals Departures No_Shows Cancellations Room_Revenue Rooms_Sold Total_Revenue Food_Revenue length=8;

			Property_ID = &property_id.;
			MKT_SEG_ID = mktSegId;
			Accom_Type_ID = accomID;
			Occupancy_DT = occupancyDt;		
			Arrivals=0;
			Departures=0;
			No_Shows=0;
			Cancellations=0;
			Room_Revenue=0;
			Rooms_Sold=0;
			Total_Revenue=0;
			Food_Revenue=0;
			output;
		run;

		data rm_data.Bde_mkt_accom_los_inventory (index=(book_idx=(Property_ID Accom_Type_ID MKT_SEG_ID ARRIVAL_DT LOS)) drop=occupancyDt accomID mktSegId);
			set &work_lib..zero_fill_occdate_accom_mkt;
			attrib Property_ID MKT_SEG_ID length=6;
			attrib ARRIVAL_DT length=8;
			attrib Accom_Type_ID length=6;
			attrib LOS length=3;
			attrib Arrivals No_Shows Cancellations Room_Revenue Total_Revenue length=8;

			Property_ID = &property_id.;
			MKT_SEG_ID = mktSegId;
			ARRIVAL_DT = occupancyDt;
			Accom_Type_ID = accomID;
			LOS=1;
			Arrivals=0;
			No_Shows=0;
			Cancellations=0;
			Room_Revenue=0;
			Total_Revenue=0;
			output;
		run;

		data rm_data.Mkt_accom_inventory (index=(mkt_idx=(Property_ID Accom_Type_ID MKT_SEG_ID Occupancy_DT)) drop=occupancyDt accomID mktSegId);
			set &work_lib..zero_fill_occdate_accom_mkt;
			attrib Property_ID MKT_SEG_ID Accom_Type_ID length=6;
			attrib Occupancy_DT Arrivals Departures No_Shows Cancellations Room_Revenue Rooms_Sold Total_Revenue Food_Revenue length=8;

			Property_ID = &property_id.;
			MKT_SEG_ID = mktSegId;
			Accom_Type_ID = accomID;
			Occupancy_DT = occupancyDt;		
			Arrivals=0;
			Departures=0;
			No_Shows=0;
			Cancellations=0;
			Room_Revenue=0;
			Rooms_Sold=0;
			Total_Revenue=0;
			Food_Revenue=0;
			output;
		run;

		data rm_data.Mkt_accom_los_inventory (index=(book_idx=(Property_ID Accom_Type_ID MKT_SEG_ID ARRIVAL_DT LOS)) drop=occupancyDt accomID mktSegId);
			set &work_lib..zero_fill_occdate_accom_mkt;
			attrib Property_ID MKT_SEG_ID length=6;
			attrib ARRIVAL_DT length=8;
			attrib Accom_Type_ID length=6;
			attrib LOS length=3;
			attrib Arrivals No_Shows Cancellations Room_Revenue Total_Revenue length=8;

			Property_ID = &property_id.;
			MKT_SEG_ID = mktSegId;
			ARRIVAL_DT = occupancyDt;
			Accom_Type_ID = accomID;
			LOS=1;
			Arrivals=0;
			No_Shows=0;
			Cancellations=0;
			Room_Revenue=0;
			Total_Revenue=0;
			output;
		run;

	%end;

	%else %do;

		proc sql noprint;
			 create table &work_lib..zero_fill_new_mkt as select * 
				from &work_lib..zero_fill_occdate_accom_mkt where mktSegId not in
				(select distinct(MKT_SEG_ID) as Mkt_Seg_ID from rm_data.Mkt_accom_inventory );
		quit;		

		data &work_lib..Bde_mkt_accom_inventory_current (index=(mkt_idx=(Property_ID Accom_Type_ID MKT_SEG_ID Occupancy_DT)) drop=occupancyDt accomID mktSegId);
			set &work_lib..zero_fill_new_mkt;
			attrib Property_ID MKT_SEG_ID Accom_Type_ID length=6;
			attrib Occupancy_DT Arrivals Departures No_Shows Cancellations Room_Revenue Rooms_Sold Total_Revenue Food_Revenue length=8;

			Property_ID = &property_id.;
			MKT_SEG_ID = mktSegId;
			Accom_Type_ID = accomID;
			Occupancy_DT = occupancyDt;		
			Arrivals=0;
			Departures=0;
			No_Shows=0;
			Cancellations=0;
			Room_Revenue=0;
			Rooms_Sold=0;
			Total_Revenue=0;
			Food_Revenue=0;
			output;
		run;
		
		data &work_lib..Bde_mkt_accom_los_inventory_c (index=(book_idx=(Property_ID Accom_Type_ID MKT_SEG_ID ARRIVAL_DT LOS)) drop=occupancyDt accomID mktSegId);
			set &work_lib..zero_fill_new_mkt;
			attrib Property_ID MKT_SEG_ID length=6;
			attrib ARRIVAL_DT length=8;
			attrib Accom_Type_ID length=6;
			attrib LOS length=3;
			attrib Arrivals No_Shows Cancellations Room_Revenue Total_Revenue length=8;

			Property_ID = &property_id.;
			MKT_SEG_ID = mktSegId;
			ARRIVAL_DT = occupancyDt;
			Accom_Type_ID = accomID;
			LOS=1;
			Arrivals=0;
			No_Shows=0;
			Cancellations=0;
			Room_Revenue=0;
			Total_Revenue=0;
			output;
		run;

		data &work_lib..Mkt_accom_inventory_current (index=(mkt_idx=(Property_ID Accom_Type_ID MKT_SEG_ID Occupancy_DT)) drop=occupancyDt accomID mktSegId);
			set &work_lib..zero_fill_new_mkt;
			attrib Property_ID MKT_SEG_ID Accom_Type_ID length=6;
			attrib Occupancy_DT Arrivals Departures No_Shows Cancellations Room_Revenue Rooms_Sold Total_Revenue Food_Revenue length=8;

			Property_ID = &property_id.;
			MKT_SEG_ID = mktSegId;
			Accom_Type_ID = accomID;
			Occupancy_DT = occupancyDt;		
			Arrivals=0;
			Departures=0;
			No_Shows=0;
			Cancellations=0;
			Room_Revenue=0;
			Rooms_Sold=0;
			Total_Revenue=0;
			Food_Revenue=0;
			output;
		run;

		data &work_lib..Mkt_accom_los_inventory_current (index=(book_idx=(Property_ID Accom_Type_ID MKT_SEG_ID ARRIVAL_DT LOS)) drop=occupancyDt accomID mktSegId);
			set &work_lib..zero_fill_new_mkt;
			attrib Property_ID MKT_SEG_ID length=6;
			attrib ARRIVAL_DT length=8;
			attrib Accom_Type_ID length=6;
			attrib LOS length=3;
			attrib Arrivals No_Shows Cancellations Room_Revenue Total_Revenue length=8;

			Property_ID = &property_id.;
			MKT_SEG_ID = mktSegId;
			ARRIVAL_DT = occupancyDt;
			Accom_Type_ID = accomID;
			LOS=1;
			Arrivals=0;
			No_Shows=0;
			Cancellations=0;
			Room_Revenue=0;
			Total_Revenue=0;
			output;
		run;

		proc append 
			base=rm_data.Bde_mkt_accom_inventory 
			data=&work_lib..Bde_mkt_accom_inventory_current force;
        run;

		proc append 
			base=rm_data.Bde_mkt_accom_los_inventory 
			data=&work_lib..Bde_mkt_accom_los_inventory_c force;
        run;

		proc append 
			base=rm_data.Mkt_accom_inventory 
			data=&work_lib..Mkt_accom_inventory_current force;
        run;

		proc append 
			base=rm_data.Mkt_accom_los_inventory 
			data=&work_lib..Mkt_accom_los_inventory_current force;
        run;
	%end;

%ideas_macro_log (start_end=END, macroname=&macroname.);
%mend ideas_zero_fill_activity_data;
