%macro ideas_pace_checker_report (rc=)/store;

    %let Number_Total_pace_differences=%ideas_util_nobs(&work_lib..Total_Pace_differences);
    %let Number_Total_pace_matches=%ideas_util_nobs(&work_lib..Total_Pace_matches);
    %let Number_Total_pace_missing_db=%ideas_util_nobs(&work_lib..Total_Pace_point_Missing_DB);
    %let Number_Total_pace_Sas_only=%ideas_util_nobs(&work_lib..Total_Pace_point_SAS_Only);
    %let Number_Total_pace_pts_checked=%ideas_util_nobs(&work_lib..total_inventory_pace);
    %let Number_Mkt_pace_differences=%ideas_util_nobs(&work_lib..Mkt_Pace_differences);
    %let Number_mkt_pace_matches=%ideas_util_nobs(&work_lib..mkt_Pace_matches);
    %let Number_mkt_pace_missing_db=%ideas_util_nobs(&work_lib..mkt_Pace_point_Missing_DB);
    %let Number_mkt_pace_Sas_only=%ideas_util_nobs(&work_lib..Mkt_Pace_point_SAS_Only);
    %let Number_Mkt_pace_pts_checked=%ideas_util_nobs(&work_lib..Mkt_inventory_pace);
    %let Number_Accom_pace_differences=%ideas_util_nobs(&work_lib..Accom_Pace_differences);
    %let Number_accom_pace_matches=%ideas_util_nobs(&work_lib..accom_Pace_matches);
    %let Number_accom_pace_missing_db=%ideas_util_nobs(&work_lib..accom_Pace_point_Missing_DB);
    %let Number_Accom_pace_Sas_only=%ideas_util_nobs(&work_lib..Accom_Pace_point_SAS_Only);
    %let Number_Accom_pace_pts_checked=%ideas_util_nobs(&work_lib..Accom_inventory_pace);

    %if &Number_Total_pace_differences. >0 or &Number_Accom_pace_differences. >0 or &Number_mkt_pace_differences. >0 
        &Number_Total_pace_missing_db. >0 or &Number_Accom_pace_missing_db. >0 or &Number_mkt_pace_missing_db. >0 %then
        %let pace_status_description=%str(&validation_desc. - Differences Found);
    %else %let pace_status_description=%str(&validation_desc. - 100% Pace Match);

    data &work_lib..pace_summary_counts;
        format Description $50.;
        format Count 9.;
        Description="Total Pace Points Checked";
        count=&Number_Total_pace_pts_checked.;
        output &work_lib..pace_summary_counts;
        Description="Total Pace Point Matched";
        count=&Number_Total_pace_matches.;
        output &work_lib..pace_summary_counts;
        Description="Total Pace Point Differences";
        count=&Number_Total_pace_differences.;
        output &work_lib..pace_summary_counts;
        Description="Total Pace Point Missing";
        count=&Number_Total_pace_missing_db.;
        output &work_lib..pace_summary_counts;
        Description="Total Sas Only Pace Points";
        count=&Number_Total_pace_Sas_only.;
        output &work_lib..pace_summary_counts;
        Description="Accom Pace Points Checked";
        count=&Number_Accom_pace_pts_checked.;
        output &work_lib..pace_summary_counts;
        Description="Accom Pace Point Matched";
        count=&Number_Accom_pace_matches.;
        output &work_lib..pace_summary_counts;
        Description="Accom Pace Point Differences";
        count=&Number_Accom_pace_differences.;
        output &work_lib..pace_summary_counts;
        Description="Accom Pace Point Missing";
        count=&Number_Accom_pace_missing_db.;
        output &work_lib..pace_summary_counts;
        Description="Accom Sas Only Pace Points";
        count=&Number_Accom_pace_Sas_only.;
        output &work_lib..pace_summary_counts;
        Description="Mkt Pace Points Checked";
        count=&Number_Mkt_pace_pts_checked.;
        output &work_lib..pace_summary_counts;
        Description="Mkt Pace Point Matched";
        count=&Number_Mkt_pace_matches.;
        output &work_lib..pace_summary_counts;
        Description="Mkt Pace Point Differences";
        count=&Number_Mkt_pace_differences.;
        output &work_lib..pace_summary_counts;
        Description="Mkt Pace Point Missing";
        count=&Number_Mkt_pace_missing_db.;
        output &work_lib..pace_summary_counts;
        Description="Mkt Sas Only Pace Points";
        count=&Number_Mkt_pace_Sas_only.;
        output &work_lib..pace_summary_counts;
    run;

    ods excel close;
    ods listing;
    ods excel file="&file_references_root./&property_id._Pace_Checker_Summary.xlsx" 
        options( embedded_titles='Yes' embedded_footnotes='yes' suppress_bylines='no');
    ods excel options(sheet_interval='none' sheet_name="Pace Checker Summary");

    %IF %SYSFUNC(EXIST(&work_lib..pace_summary_counts)) = 1 and %ideas_util_nobs(&work_lib..pace_summary_counts) >0 %THEN
        %DO;

            proc Print data= &work_lib..pace_summary_counts label noobs width = minimum;
                title1 bold h=13pt f='Courier New' color=black 'Pace Checker Summary';
                title2 bold h=13pt f='Courier New' color=black "&property_desc";
                title3 ' ';
                title4 bold h=13pt f='Courier New' color=black "&pace_status_description.";
                var count description;
                label count="Count";
                label description="Pace Metric Description";
            run;

            title;
        %end;

    %IF %SYSFUNC(EXIST(&work_lib..Total_Pace_point_Missing_DB)) = 1 and %ideas_util_nobs(&work_lib..Total_Pace_point_Missing_DB) >0 %THEN
        %DO;
            ods excel options(sheet_interval='proc' sheet_name="Total Pace Missing DB");

            proc Print data= &work_lib..Total_Pace_point_Missing_DB label noobs width = minimum;
                var property_id occupancy_dt capture_dttm pace_source sas_rooms_sold sas_arrivals sas_departures sas_no_shows sas_cancellations 
                    sas_Room_revenue sas_Food_Revenue sas_Total_Revenue sas_total_accom_capacity sas_rooms_not_avail_maint sas_rooms_not_avail_other;
                label property_id="Property";
                label Occupancy_dt="Occupancy Dt";
                label capture_dttm="Capture DTTM";
                Label pace_source="Pace Source";
                label sas_arrivals="Arrivals";
                label sas_departures="Departures";
                label sas_no_shows="No Shows";
                label sas_cancellations="Cancels";
                label sas_room_revenue="Room Rev";
                label sas_rooms_sold="Rooms Sold";
                label sas_food_revenue="Food Rev";
                label sas_total_revenue="Total Rev";
                label sas_total_accom_capacity="Capacity";
                label sas_rooms_not_avail_maint="OOO-Maint";
                label sas_rooms_not_avail_other="OOO-Other";
            run;

        %end;

    %IF %SYSFUNC(EXIST(&work_lib..Total_Pace_point_SAS_Only)) = 1 and %ideas_util_nobs(&work_lib..Total_Pace_point_SAS_Only) >0 %THEN
        %DO;
            ods excel options(sheet_interval='proc' sheet_name="Total Sas Only Pace");

            proc Print data= &work_lib..Total_Pace_point_SAS_Only label noobs width = minimum;
                var property_id occupancy_dt capture_dttm pace_source sas_rooms_sold sas_arrivals sas_departures sas_no_shows sas_cancellations 
                    sas_Room_revenue sas_Food_Revenue sas_Total_Revenue sas_total_accom_capacity sas_rooms_not_avail_maint sas_rooms_not_avail_other;
                label property_id="Property";
                label Occupancy_dt="Occupancy Dt";
                label capture_dttm="Capture DTTM";
                Label pace_source="Pace Source";
                label sas_arrivals="Arrivals";
                label sas_departures="Departures";
                label sas_no_shows="No Shows";
                label sas_cancellations="Cancels";
                label sas_room_revenue="Room Rev";
                label sas_rooms_sold="Rooms Sold";
                label sas_food_revenue="Food Rev";
                label sas_total_revenue="Total Rev";
                label sas_total_accom_capacity="Capacity";
                label sas_rooms_not_avail_maint="OOO-Maint";
                label sas_rooms_not_avail_other="OOO-Other";
            run;

        %end;

    %IF %SYSFUNC(EXIST(&work_lib..Total_Pace_differences)) = 1 and %ideas_util_nobs(&work_lib..Total_Pace_differences) >0 %THEN
        %DO;
            ods excel options(sheet_interval='proc' sheet_name="Total Pace Differences");

            proc Report data=&work_lib..Total_Pace_differences nowd nofs;
                columns property_id occupancy_dt capture_dttm pace_source rooms_sold sas_rooms_sold arrivals sas_arrivals departures sas_departures no_shows sas_no_shows cancellations sas_cancellations 
                    Room_Revenue sas_Room_revenue food_Revenue sas_Food_Revenue Total_Revenue sas_Total_Revenue 
                    total_accom_capacity sas_total_accom_capacity rooms_not_avail_maint sas_rooms_not_avail_maint 
                    rooms_not_avail_other sas_rooms_not_avail_other;
                define property_id /display width=10 "Property";
                define occupancy_dt /display width=9 "Occupancy Dt";
                define capture_dttm /display width=17 "Capture DTTM";
                define pace_source /display width=8 "Pace Source";
                define arrivals /display width=8 "Arrivals";
                define sas_arrivals /display width=11 "Sas Arrivals";
                define departures /display width=10 "Departures";
                define sas_departures /display width=13 "Sas Departures";
                define no_shows /display width=8 "NoShows";
                define sas_no_shows /display width=10 "Sas NoShows";
                define Room_Revenue /display width=10 format=9.2 "Room Rev";
                define sas_Room_revenue /display width=11 format=9.2 "Sas Room Rev";
                define rooms_sold /display width=10 "Rooms Sold";
                define sas_rooms_sold /display width=13 "Sas Rooms Sold";
                define food_Revenue /display width=10 format=9.2 "Food Rev";
                define sas_Food_Revenue /display width=12 format=9.2 "Sas Food Rev";
                define Total_Revenue /display width=10 format=9.2 "Total Rev";
                define sas_Total_Revenue /display width=12 format=9.2 "Sas Total Rev";
                define cancellations /display width=8 "Cancels";
                define sas_cancellations /display width=11 "Sas Cancels";
                define total_accom_capacity /display width=8 "Capacity";
                define sas_total_accom_capacity /display width=12 "Sas Capacity";
                define rooms_not_avail_maint /display width=8 "OOOMaint";
                define sas_rooms_not_avail_maint/display width=12 "Sas OOOMaint";
                define rooms_not_avail_other /display width=8 "OOOOther";
                define sas_rooms_not_avail_other/display width=12 "Sas OOOOther";

                compute sas_rooms_sold;

                    if rooms_sold ne sas_rooms_sold then
                        call define(_col_,"style","style={background=red}");
                endcomp;

                compute sas_arrivals;

                    if arrivals ne sas_arrivals then
                        call define(_col_,"style","style={background=red}");
                endcomp;

                compute sas_departures;

                    if departures ne sas_departures then
                        call define(_col_,"style","style={background=red}");
                endcomp;

                compute sas_no_shows;

                    if no_shows ne sas_no_shows then
                        call define(_col_,"style","style={background=red}");
                endcomp;

                compute sas_cancellations;

                    if cancellations ne sas_cancellations then
                        call define(_col_,"style","style={background=red}");
                endcomp;

                compute sas_Room_Revenue;

                    if Room_Revenue ne sas_Room_Revenue then
                        call define(_col_,"style","style={background=red}");
                endcomp;

                compute sas_food_Revenue;

                    if food_Revenue ne sas_food_Revenue then
                        call define(_col_,"style","style={background=red}");
                endcomp;

                compute sas_total_Revenue;

                    if total_Revenue ne sas_total_Revenue then
                        call define(_col_,"style","style={background=red}");
                endcomp;

                compute sas_total_accom_capacity;

                    if total_accom_capacity ne sas_total_accom_capacity then
                        call define(_col_,"style","style={background=red}");
                endcomp;

                compute sas_rooms_not_avail_maint;

                    if rooms_not_avail_maint ne sas_rooms_not_avail_maint then
                        call define(_col_,"style","style={background=red}");
                endcomp;

                compute sas_rooms_not_avail_other;

                    if rooms_not_avail_other ne sas_rooms_not_avail_other then
                        call define(_col_,"style","style={background=red}");
                endcomp;
            run;

        %end;

    %IF %SYSFUNC(EXIST(&work_lib..Accom_Pace_point_Missing_DB)) = 1 and %ideas_util_nobs(&work_lib..Accom_Pace_point_Missing_DB) >0 %THEN
        %DO;
            ods excel options(sheet_interval='proc' sheet_name="Accom Pace Missing DB");

            proc Print data= &work_lib..Accom_Pace_point_Missing_DB label noobs width = minimum;
                var property_id occupancy_dt capture_dttm pace_source accom_type_id sas_rooms_sold sas_arrivals sas_departures sas_no_shows sas_cancellations 
                    sas_Room_revenue sas_Food_Revenue sas_Total_Revenue sas_accom_capacity sas_rooms_not_avail_maint sas_rooms_not_avail_other;
                label property_id="Property";
                label Occupancy_dt="Occupancy Dt";
                label capture_dttm="Capture DTTM";
                Label pace_source="Pace Source";
                label accom_type_id="Accom Type";
                label sas_arrivals="Arrivals";
                label sas_departures="Departures";
                label sas_no_shows="No Shows";
                label sas_cancellations="Cancels";
                label sas_room_revenue="Room Rev";
                label sas_rooms_sold="Rooms Sold";
                label sas_food_revenue="Food Rev";
                label sas_total_revenue="Total Rev";
                label sas_accom_capacity="Capacity";
                label sas_rooms_not_avail_maint="OOO-Maint";
                label sas_rooms_not_avail_other="OOO-Other";
            run;

        %end;

    %IF %SYSFUNC(EXIST(&work_lib..Accom_Pace_point_SAS_Only)) = 1 and %ideas_util_nobs(&work_lib..Accom_Pace_point_SAS_Only) >0 %THEN
        %DO;
            ods excel options(sheet_interval='proc' sheet_name="Accom Sas Only Pace");

            proc Print data= &work_lib..Accom_Pace_point_SAS_Only label noobs width = minimum;
                var property_id occupancy_dt capture_dttm pace_source accom_type_id sas_rooms_sold sas_arrivals sas_departures sas_no_shows sas_cancellations 
                    sas_Room_revenue sas_Food_Revenue sas_Total_Revenue sas_accom_capacity sas_rooms_not_avail_maint sas_rooms_not_avail_other;
                label property_id="Property";
                label Occupancy_dt="Occupancy Dt";
                label capture_dttm="Capture DTTM";
                Label pace_source="Pace Source";
                label accom_type_id="Accom Type";
                label sas_arrivals="Arrivals";
                label sas_departures="Departures";
                label sas_no_shows="No Shows";
                label sas_cancellations="Cancels";
                label sas_room_revenue="Room Rev";
                label sas_rooms_sold="Rooms Sold";
                label sas_food_revenue="Food Rev";
                label sas_total_revenue="Total Rev";
                label sas_accom_capacity="Capacity";
                label sas_rooms_not_avail_maint="OOO-Maint";
                label sas_rooms_not_avail_other="OOO-Other";
            run;

        %end;

    %IF %SYSFUNC(EXIST(&work_lib..Accom_Pace_differences)) = 1 and %ideas_util_nobs(&work_lib..Accom_Pace_differences) >0 %THEN
        %DO;
            ods excel options(sheet_interval='proc' sheet_name="Accom Pace Differences");

            proc Report data=&work_lib..Accom_Pace_differences nowd nofs;
                columns property_id occupancy_dt capture_dttm pace_source accom_type_id rooms_sold sas_rooms_sold arrivals sas_arrivals departures sas_departures no_shows sas_no_shows cancellations sas_cancellations 
                    Room_Revenue sas_Room_revenue food_Revenue sas_Food_Revenue Total_Revenue sas_Total_Revenue 
                    accom_capacity sas_accom_capacity rooms_not_avail_maint sas_rooms_not_avail_maint rooms_not_avail_other sas_rooms_not_avail_other;
                define property_id /display width=10 "Property";
                define occupancy_dt /display width=9 "Occupancy Dt";
                define capture_dttm /display width=17 "Capture DTTM";
                define pace_source /display width=8 "Pace Source";
                define accom_type_id /display width=10 "Accom Type";
                define arrivals /display width=8 "Arrivals";
                define sas_arrivals /display width=11 "Sas Arrivals";
                define departures /display width=10 "Departures";
                define sas_departures /display width=13 "Sas Departures";
                define no_shows /display width=8 "NoShows";
                define sas_no_shows /display width=10 "Sas NoShows";
                define Room_Revenue /display width=10 format=9.2 "Room Rev";
                define sas_Room_revenue /display width=11 format=9.2 "Sas Room Rev";
                define rooms_sold /display width=10 "Rooms Sold";
                define sas_rooms_sold /display width=13 "Sas Rooms Sold";
                define food_Revenue /display width=10 format=9.2 "Food Rev";
                define sas_Food_Revenue /display width=12 format=9.2 "Sas Food Rev";
                define Total_Revenue /display width=10 format=9.2 "Total Rev";
                define sas_Total_Revenue /display width=12 format=9.2 "Sas Total Rev";
                define cancellations /display width=8 "Cancels";
                define sas_cancellations /display width=11 "Sas Cancels";
                define accom_capacity /display width=8 "Capacity";
                define sas_accom_capacity /display width=12 "Sas Capacity";
                define rooms_not_avail_maint /display width=8 "OOOMaint";
                define sas_rooms_not_avail_maint/display width=12 "Sas OOOMaint";
                define rooms_not_avail_other /display width=8 "OOOOther";
                define sas_rooms_not_avail_other/display width=12 "Sas OOOOther";

                compute sas_rooms_sold;

                    if rooms_sold ne sas_rooms_sold then
                        call define(_col_,"style","style={background=red}");
                endcomp;

                compute sas_arrivals;

                    if arrivals ne sas_arrivals then
                        call define(_col_,"style","style={background=red}");
                endcomp;

                compute sas_departures;

                    if departures ne sas_departures then
                        call define(_col_,"style","style={background=red}");
                endcomp;

                compute sas_no_shows;

                    if no_shows ne sas_no_shows then
                        call define(_col_,"style","style={background=red}");
                endcomp;

                compute sas_cancellations;

                    if cancellations ne sas_cancellations then
                        call define(_col_,"style","style={background=red}");
                endcomp;

                compute sas_Room_Revenue;

                    if Room_Revenue ne sas_Room_Revenue then
                        call define(_col_,"style","style={background=red}");
                endcomp;

                compute sas_food_Revenue;

                    if food_Revenue ne sas_food_Revenue then
                        call define(_col_,"style","style={background=red}");
                endcomp;

                compute sas_total_Revenue;

                    if total_Revenue ne sas_total_Revenue then
                        call define(_col_,"style","style={background=red}");
                endcomp;

                compute sas_accom_capacity;

                    if accom_capacity ne sas_accom_capacity then
                        call define(_col_,"style","style={background=red}");
                endcomp;

                compute sas_rooms_not_avail_maint;

                    if rooms_not_avail_maint ne sas_rooms_not_avail_maint then
                        call define(_col_,"style","style={background=red}");
                endcomp;

                compute sas_rooms_not_avail_other;

                    if rooms_not_avail_other ne sas_rooms_not_avail_other then
                        call define(_col_,"style","style={background=red}");
                endcomp;
            run;

        %end;

    %IF %SYSFUNC(EXIST(&work_lib..Mkt_Pace_point_Missing_DB)) = 1 and %ideas_util_nobs(&work_lib..Mkt_Pace_point_Missing_DB) >0 %THEN
        %DO;
            ods excel options(sheet_interval='proc' sheet_name="Mkt Pace Missing DB");

            proc Print data= &work_lib..Mkt_Pace_point_Missing_DB label noobs width = minimum;
                var property_id occupancy_dt capture_dttm pace_source mkt_seg_id sas_rooms_sold sas_arrivals sas_departures sas_no_shows sas_cancellations 
                    sas_Room_revenue sas_Food_Revenue sas_Total_Revenue;
                label property_id="Property";
                label Occupancy_dt="Occupancy Dt";
                label capture_dttm="Capture DTTM";
                Label pace_source="Pace Source";
                label mkt_seg_id="Mkt Seg";
                label sas_arrivals="Arrivals";
                label sas_departures="Departures";
                label sas_no_shows="No Shows";
                label sas_cancellations="Cancels";
                label sas_room_revenue="Room Rev";
                label sas_rooms_sold="Rooms Sold";
                label sas_food_revenue="Food Rev";
                label sas_total_revenue="Total Rev";
            run;

        %end;

    %IF %SYSFUNC(EXIST(&work_lib..Mkt_Pace_point_SAS_Only)) = 1 and %ideas_util_nobs(&work_lib..Mkt_Pace_point_SAS_Only) >0 %THEN
        %DO;
            ods excel options(sheet_interval='proc' sheet_name="Mkt Sas Only Pace");

            proc Print data= &work_lib..Mkt_Pace_point_SAS_Only label noobs width = minimum;
                var property_id occupancy_dt capture_dttm pace_source mkt_seg_id sas_rooms_sold sas_arrivals sas_departures sas_no_shows sas_cancellations 
                    sas_Room_revenue sas_Food_Revenue sas_Total_Revenue;
                label property_id="Property";
                label Occupancy_dt="Occupancy Dt";
                label capture_dttm="Capture DTTM";
                Label pace_source="Pace Source";
                label mkt_seg_id="Mkt Seg";
                label sas_arrivals="Arrivals";
                label sas_departures="Departures";
                label sas_no_shows="No Shows";
                label sas_cancellations="Cancels";
                label sas_room_revenue="Room Rev";
                label sas_rooms_sold="Rooms Sold";
                label sas_food_revenue="Food Rev";
                label sas_total_revenue="Total Rev";
            run;

        %end;

    %IF %SYSFUNC(EXIST(&work_lib..mkt_Pace_differences)) = 1 and %ideas_util_nobs(&work_lib..mkt_Pace_differences) >0 %THEN
        %DO;
            ods excel options(sheet_interval='proc' sheet_name="Mkt Pace Differences");

            proc Report data=&work_lib..Mkt_Pace_differences nowd nofs;
                columns property_id occupancy_dt capture_dttm Pace_source Mkt_seg_id rooms_sold sas_rooms_sold arrivals sas_arrivals departures sas_departures no_shows sas_no_shows cancellations sas_cancellations 
                    Room_Revenue sas_Room_revenue food_Revenue sas_Food_Revenue Total_Revenue sas_Total_Revenue;
                define property_id /display width=10 "Property";
                define occupancy_dt /display width=9 "Occupancy Dt";
                define capture_dttm /display width=17 "Capture DTTM";
                define pace_source /display width=8 "Pace Source";
                define mkt_seg_id /display width=10 "Mkt Seg";
                define arrivals /display width=8 "Arrivals";
                define sas_arrivals /display width=11 "Sas Arrivals";
                define departures /display width=10 "Departures";
                define sas_departures /display width=13 "Sas Departures";
                define no_shows /display width=8 "NoShows";
                define sas_no_shows /display width=10 "Sas NoShows";
                define cancellations /display width=8 "Cancels";
                define sas_cancellations /display width=11 "Sas Cancels";
                define Room_Revenue /display width=10 format=9.2 "Room Rev";
                define sas_Room_revenue /display width=11 format=9.2 "Sas Room Rev";
                define rooms_sold /display width=10 "Rooms Sold";
                define sas_rooms_sold /display width=13 "Sas Rooms Sold";
                define food_Revenue /display width=10 format=9.2 "Food Rev";
                define sas_Food_Revenue /display width=12 format=9.2 "Sas Food Rev";
                define Total_Revenue /display width=10 format=9.2 "Total Rev";
                define sas_Total_Revenue /display width=12 format=9.2 "Sas Total Rev";

                compute sas_rooms_sold;

                    if rooms_sold ne sas_rooms_sold then
                        call define(_col_,"style","style={background=red}");
                endcomp;

                compute sas_arrivals;

                    if arrivals ne sas_arrivals then
                        call define(_col_,"style","style={background=red}");
                endcomp;

                compute sas_departures;

                    if departures ne sas_departures then
                        call define(_col_,"style","style={background=red}");
                endcomp;

                compute sas_no_shows;

                    if no_shows ne sas_no_shows then
                        call define(_col_,"style","style={background=red}");
                endcomp;

                compute sas_cancellations;

                    if cancellations ne sas_cancellations then
                        call define(_col_,"style","style={background=red}");
                endcomp;

                compute sas_Room_Revenue;

                    if Room_Revenue ne sas_Room_Revenue then
                        call define(_col_,"style","style={background=red}");
                endcomp;

                compute sas_food_Revenue;

                    if food_Revenue ne sas_food_Revenue then
                        call define(_col_,"style","style={background=red}");
                endcomp;

                compute sas_total_Revenue;

                    if total_Revenue ne sas_total_Revenue then
                        call define(_col_,"style","style={background=red}");
                endcomp;
            run;

        %end;

    %IF %SYSFUNC(EXIST(&work_lib..Mkt_Pace_point_Missing_DB)) = 1 and %ideas_util_nobs(&work_lib..Mkt_Pace_point_Missing_DB) >0 %THEN
        %DO;
            ods excel options(sheet_interval='proc' sheet_name="Mkt Pace Missing DB");

            proc Print data= &work_lib..Mkt_Pace_point_Missing_DB label noobs width = minimum;
                var property_id occupancy_dt capture_dttm pace_source mkt_seg_id sas_rooms_sold sas_arrivals sas_departures sas_no_shows sas_cancellations 
                    sas_Room_revenue sas_Food_Revenue sas_Total_Revenue;
                label property_id="Property";
                label Occupancy_dt="Occupancy Dt";
                label capture_dttm="Capture DTTM";
                Label pace_source="Pace Source";
                label mkt_seg_id="Mkt Seg";
                label sas_arrivals="Arrivals";
                label sas_departures="Departures";
                label sas_no_shows="No Shows";
                label sas_cancellations="Cancels";
                label sas_room_revenue="Room Rev";
                label sas_rooms_sold="Rooms Sold";
                label sas_food_revenue="Food Rev";
                label sas_total_revenue="Total Rev";
            run;

        %end;

    ods excel close;
    ods _all_ close;
%mend;