%macro ideas_type2_extract(c_var, m_var, tenant_db, tenant_server, port, server_instance, tenant_user, tenant_pwd)/store;
    %let macroname=&SYSMACRONAME;

    %ideas_macro_log (start_end=START, macroname=&macroname.);
    %ideas_trans_exe(sql=%str(select * from tenant.record_type),
        _output_table=&work_lib..record_type );

    proc Sql noprint;
        select record_type_id into :CTAT from &work_lib..record_type where upcase(record_type_name)='CTAT' and status_id=1;
        select record_type_id into :CSAT from &work_lib..record_type where upcase(record_type_name)='CSAT' and status_id=1;
        select record_type_id into :PTAT from &work_lib..record_type where upcase(record_type_name)='PTAT' and status_id=1;
        select record_type_id into :PSAT from &work_lib..record_type where upcase(record_type_name)='PSAT' and status_id=1;
        select record_type_id into :PT from &work_lib..record_type where upcase(record_type_name)='PT' and status_id=1;
        select record_type_id into :CT from &work_lib..record_type where upcase(record_type_name)='CT' and status_id=1;
        select record_type_id into :GM from &work_lib..record_type where upcase(record_type_name)='GROUPMASTER' and status_id=1;
        select record_type_id into :GB from &work_lib..record_type where upcase(record_type_name)='GROUPBLOCK' and status_id=1;
    quit;

    %if not (%symexist(CTAT) and %symexist(GM) and %symexist(GB) and %symexist(CSAT) and %symexist(PTAT) and %symexist(PSAT) and %symexist(PT) and %symexist(CT)) %then %do;
        %ideas_util_report_errors(911, 'Required record types are not in the database', '', &request_Id., 1);
        %let etl_status = 1;
        %goto EXIT;
    %end;

    proc Sql noprint;
        select record_type_id into: RecType from tenant.record_type where upcase(record_type_name) = 'T2SNAP' and status_id=1;
    quit;

    %if not %symexist(RecType) %then %do;
        %ideas_util_report_errors(912, 'Record type T2SNAP does not exist in the tenant database', '', &request_Id., 1);
        %let etl_status = 1;
        %goto EXIT;
    %end;

    *Do the property lookup again, this time in the file_metadata table to see if an extract has ever been processed;
    proc Sql noprint;
        select property_id into:  tpid from tenant.file_metadata
            where property_id = &property_id. and record_type_id = &RecType.;
    quit;

    %global f_file;

    /*f_file indicated if it is the first file processed = 0 = false 1 = true;*/
    %let f_file = 0;

    %if not %symexist(tpid) %then %do;

        data &work_lib..ssdt;
            attrib ssnap_dt format = date9.;
            attrib sssnap_tm format = time10.;
            ssnap_dt = 0;
            ssnap_tm = 0;
            sssnap_tm = 0;
        run;

        data &work_lib..spdt;
            attrib sprep_dt format = date9.;
            attrib ssprep_tm format = time10.;
            sprep_dt = 0;
            sprep_tm = 0;
            ssprep_tm = 0;
        run;

        %let f_file = 1;
    %end;
    %else %do;

        /*Get the max system/prepared dates from the global file to be compared against the input file for validation;*/
        proc Sql;
            create table &work_lib..ssdt1 as
                select distinct snapshot_dt as ssnap_dt, max(snapshot_tm) as ssnap_tm, max(snapshot_tm) as sssnap_tm, isbde
                    from tenant.file_metadata
                        where property_id = &property_id and record_type_id = &rectype
                            and (select max(snapshot_dt) from tenant.file_metadata 
                        where property_id = &property_id and record_type_id = &rectype) = snapshot_dt;
            create table &work_lib..spdt1 as
                select distinct prepared_dt as sprep_dt, max(prepared_tm) as sprep_tm, max(prepared_tm) as ssprep_tm
                    from tenant.file_metadata
                        where property_id = &property_id and record_type_id = &rectype
                            and (select max(prepared_dt) from tenant.file_metadata 
                        where property_id = &property_id and record_type_id = &rectype) = prepared_dt;
        quit;

        data &work_lib..ssdt;
            set &work_lib..ssdt1;
            attrib ssnap_dt format = date9.;
            attrib sssnap_tm format = time10.;
        run;

        data &work_lib..spdt;
            set &work_lib..spdt1;
            attrib sprep_dt format = date9.;
            attrib ssprep_tm format = time10.;
        run;

    %end;

    data &work_lib..dates;
        set &work_lib..ssdt;
        set &work_lib..spdt;
    run;

    data &work_lib..dates;
        set &work_lib..dates;
        format ssnap_dttm sprep_dttm datetime20.;
        ssnap_dttm=dhms(ssnap_dt,0,0,ssnap_tm);
        sprep_dttm=dhms(sprep_dt,0,0,sprep_tm);
    run;

    /*
     *Input the inbound ETL file;
     *Read in the text input file.  It is pipe delimited, and contains total, accom, mkt_accom records;
     *As read in, separate the data into five files; metadata (one input file record), accommodation data;
     *market data, total data, and bad records (identified by unknown record types.)  If there are any bad records;
     *we will error out.  Otherwise, the total, accom and mkt_accom data will be loaded into the warehouse tables;
     */
    data &work_lib..file_metadata (keep=record_type client_code property_id past_window_size future_window_size
        snapshot_date snapshot_time prepared_date prepared_time)
        &work_lib..accom  (keep=record_type_id occupancy_dt accom_type_cd  accom_capacity rooms_not_avail_maint 
        rooms_not_avail_other rooms_sold arrivals departures room_revenue food_revenue 
        total_revenue cancellations no_shows)
        &work_lib..mkt_accom (keep=record_type_id occupancy_dt accom_type_cd market_segment rooms_sold 
        arrivals departures room_revenue food_revenue total_revenue cancellations no_shows)
        &work_lib..total (keep=record_type_id occupancy_dt total_accom_capacity rooms_not_avail_maint 
        rooms_not_avail_other rooms_sold arrivals departures room_revenue food_revenue 
        total_revenue cancellations no_shows)
        &work_lib..gm (keep=record_type_id prop_id Group_Code Group_Name Group_Description Master_Group_Code Group_Status_Code market_segment
        start_dt end_date group_type_code booking_dt pickup_type_code cancel_dt booking_type 
        sales_person cut_off_date cut_off_days)
        &work_lib..gb (keep=record_type_id Group_Code occupancy_dt accom_type_cd blocks pickup original_blocks rate)
        &work_lib..badrecs (keep=record_type);
        length accom_type_id 4;
        length mkt_seg_id 4;
        length occupancy_dt 8;
        length market_segment $50;
        attrib record_type format=$20.;
        attrib accom_type_cd format=$50.;
        retain snapshot_date prepared_date_temp snapshot_date_temp past_window_size future_window_size;
        format prepared_date date9.;
        format snapshot_date date9.;
        format occupancy_dt date9.;
        format start_dt date9.;
        format end_dt date9.;
        format booking_dt date9.;
        format cancel_dt date9.;
        infile "&etl_file_name" delimiter='|' dsd missover;

        /* The metadata is always on the second record */
        if _n_=2 then do;
            input record_type $ 
                client_code $ 
                property_id $ 
                past_window_size 
                future_window_size 
                snapshot_date: yymmdd10.
                snapshot_time $ 
                prepared_date: yymmdd10. 
                prepared_time $;

            *Metadata checks;
            tmp_dt=put(snapshot_date,date9.)||':'||trim(left(snapshot_time));
            snap_dttm=input(tmp_dt,datetime20.);
            tmp_dt=put(prepared_date,date9.)||':'||trim(left(prepared_time));
            prepared_dttm=input(tmp_dt,datetime20.);

            *read the snapshot/prepared_dttm into variables;
            call symput('snapshot_date',snap_dttm);
            call symput('prepared_date',prepared_dttm);
            call symput('snap_date', compress(snapshot_date));
            call symput('snap_time', compress(snapshot_time));
            call symput('prep_date', compress(prepared_date));
            call symput('prep_time', compress(prepared_time));
            call symput('snap_dttm', compress(snap_dttm));
            call symput('prepared_dttm', compress(prepared_dttm));
            call symput ('past_window_size', compress (past_window_size));
            call symput ('future_window_size', compress (future_window_size));
            output &Work_lib..file_metadata;
        end;
        else do;

            /* This is not a metadata record */
            /* Read in the "universal" fields first */
            input record_type $ @;

            /* The record type determines the remaining field names.  Read accordingly.*/
            if record_type='_CT_' then do;
                record_type_id=&ct;
                input  occupancy_dt: yymmdd10. total_accom_capacity rooms_not_avail_maint rooms_not_avail_other rooms_sold arrivals departures 
                    room_revenue food_revenue total_revenue;

                if food_revenue=. then
                    food_revenue=0;

                /* US13009 */
                if  total_accom_capacity < 0  then do;
                    total_accom_capacity = 0;
                    rooms_not_avail_maint = 0;
                    rooms_not_avail_other = 0;
                    rooms_sold = 0;
                    arrivals = 0;
                    departures = 0;
                    room_revenue = 0;
                    food_revenue = 0;
                    total_revenue = 0;
                end;

                output &work_lib..total;
            end;  /*** end CT checks ***/
            else if record_type='_PT_' then do;
                record_type_id=&pt;
                input  occupancy_dt: yymmdd10. total_accom_capacity rooms_not_avail_maint rooms_not_avail_other rooms_sold arrivals departures 
                    room_revenue food_revenue total_revenue cancellations no_shows;

                if food_revenue=. then
                    food_revenue=0;

                /* US13009 */
                if  total_accom_capacity < 0  then do;
                    total_accom_capacity = 0;
                    rooms_not_avail_maint = 0;
                    rooms_not_avail_other = 0;
                    rooms_sold = 0;
                    arrivals = 0;
                    departures = 0;
                    room_revenue = 0;
                    food_revenue = 0;
                    total_revenue = 0;
                    cancellations = 0;
                    no_shows = 0;
                end;

                output &work_lib..total;
            end;  /*** end PT checks ***/
            else if record_type='_CTAT_' then do;
                record_type_id=&ctat;
                input occupancy_dt: yymmdd10. accom_type_cd $ accom_capacity rooms_not_avail_maint rooms_not_avail_other rooms_sold arrivals departures 
                    room_revenue food_revenue total_revenue;

                if food_revenue=. then
                    food_revenue=0;

                /* US13009 */
                if  accom_capacity < 0  then do;
                    accom_capacity = 0;
                    rooms_not_avail_maint = 0;
                    rooms_not_avail_other = 0;
                    rooms_sold = 0;
                    arrivals = 0;
                    departures = 0;
                    room_revenue = 0;
                    food_revenue = 0;
                    total_revenue = 0;
                end;

                output &work_lib..accom;
            end;  /*** end CTAT checks ***/
            else if record_type='_CSAT_' then do;
                record_type_id=&csat;
                input occupancy_dt: yymmdd10. accom_type_cd $ market_segment $ rooms_sold arrivals departures room_revenue food_revenue total_revenue;

                if food_revenue=. then
                    food_revenue=0;
                output &work_lib..mkt_accom;
            end; /*** end CSAT checks ***/
            else if record_type='_PTAT_' then do;
                record_type_id=&ptat;
                input occupancy_dt: yymmdd10. accom_type_cd $ accom_capacity rooms_not_avail_maint rooms_not_avail_other rooms_sold arrivals departures 
                    room_revenue food_revenue total_revenue cancellations no_shows;

                if food_revenue=. then
                    food_revenue=0;

                /* US13009 */
                if  accom_capacity < 0  then do;
                    accom_capacity = 0;
                    rooms_not_avail_maint = 0;
                    rooms_not_avail_other = 0;
                    rooms_sold = 0;
                    arrivals = 0;
                    departures = 0;
                    room_revenue = 0;
                    food_revenue = 0;
                    total_revenue = 0;
                    cancellations = 0;
                    no_shows = 0;
                end;

                output &work_lib..accom;
            end; /*** end PTAT checks ***/
            else if record_type='_PSAT_' then do;
                record_type_id=&psat;
                input occupancy_dt: yymmdd10. accom_type_cd $ market_segment $ rooms_sold arrivals departures room_revenue food_revenue total_revenue cancellations no_shows;

                if food_revenue=. then
                    food_revenue=0;
                output &work_lib..mkt_accom;
            end;  /*** end PSAT checks ***/
            else if record_type='_GM_' then do;
                record_type_id=&gm;
                attrib prop_id format = 8.;
                input Group_Code: ~$50. Group_Name: ~$50. Group_Description: ~$100. Master_Group_Code: ~$50. Group_Status_Code: ~$9. market_segment $
                    start_dt: yymmdd10. end_date: yymmdd10. group_type_code: ~$5. booking_dt: yymmdd10. pickup_type_code : ~$18.
                    cancel_dt: yymmdd10. booking_type: ~$18. sales_person: ~$30. cut_off_date: yymmdd10. cut_off_days;
                Prop_ID = &Property_ID;
                output &work_lib..gm;
            end;  /*** end GM checks ***/
            else if record_type='_GB_' then do;
                record_type_id=&gb;
                input Group_Code $ occupancy_dt: yymmdd10. accom_type_cd $ blocks pickup original_blocks rate;
                output &work_lib..gb;
            end;  /*** end GB checks ***/

            /* Record types can be header or footer.  Ignore those, and anything else not processed is a bad record */
            else if record_type not in ('_HEADER_','_FOOTER_') then output &work_lib..badrecs;
        end;
    run;

    %if &syscc > 4 %then %do;
        %ideas_util_report_errors(903, 'Error importing ETL file', '', &request_Id., 1);
        %let etl_status = 1;
        %goto EXIT;
    %end;

    * Store the metadata fields in macro variables, so that we can call;
    data _null_;
        attrib snap_dttm prep_dttm prepared_dttm prepared_date snapshot_date format=datetime20.;
        set &Work_lib..file_metadata;
        tmp_dt=put(snapshot_date,date9.)||':'||trim(left(snapshot_time));
        snap_dttm=input(tmp_dt,datetime20.);
        tmp_dt=put(prepared_date,date9.)||':'||trim(left(prepared_time));
        prepared_dttm=input(tmp_dt,datetime20.);
        call symput('record_type',compress(record_type));
        call symput('client_code',compress(client_code));
        call symput('past_window_size',compress(past_window_size));
        call symput('future_window_size',compress(future_window_size));
        call symput('snapshot_date',snap_dttm);
        call symput('prepared_date',prepared_dttm);
        call symput('snap_date', compress(snapshot_date));
        call symput('snap_time', compress(snapshot_time));
        call symput('prep_date', compress(prepared_date));
        call symput('prep_time', compress(prepared_time));
        call symput('snap_dttm', compress(snap_dttm));
        call symput('prep_dttm',prepared_dttm);
    run;

    %put SNAP DATE IS &snap_date;

    data &work_lib..fdates;
        set &Work_lib..file_metadata 
            (keep=snapshot_date snapshot_time prepared_date prepared_time);
        snap_tm = input(snapshot_time,time10.);
        prep_tm = input(prepared_time,time10.);
        snap_dttm = input(put(snapshot_date,date9.)||':'||trim(left(snapshot_time)),datetime20.);
        prep_dttm = input(put(prepared_date,date9.)||':'||trim(left(prepared_time)),datetime20.);
    run;

    data  &work_lib..dates;
        set &work_lib..dates;
        set &work_lib..fdates;
    run;

    /*determine type of file*/
    data &work_lib..comp;
        set &work_lib..dates;
        format snapshot_date date9.;
        format snap_dttm prep_dttm datetime20.;

        if missing(ssnap_dttm) then do;
            file_type = 'EOB';
        end;
        else do;
            if snapshot_date > ssnap_dt then do;
                file_type = 'EOB';
            end;

            if snap_dttm eq ssnap_dttm and prep_dttm > sprep_dttm then do;
                file_type = 'COR';
            end;

            if snapshot_date eq ssnap_dt and snap_tm > sssnap_tm then do;
                file_type = 'INT';
            end;

            if snap_dttm eq ssnap_dttm and prep_dttm eq sprep_dttm then do;
                file_type = 'RP';
                call symputx('error_codes', 415);
            end;
        end;

        call symputx('file_type', file_type,'g');
    run;

    data rm_data.filespecs;
        loadtype = "&loadtype.";
        file_type = "&file_type.";
    run;

    %if &syscc > 4 %then %do;
        %ideas_util_report_errors(904, 'Error parsing metadata record', '', &request_Id., 1);
        %let etl_status = 1;
        %goto EXIT;
    %end;

    /*Prep tables for update/insert into rdbms;*/
    /*Metadata table;*/
    data &work_lib..fn;
        call scan("&etl_file_name",-1,fn_pos,fn_length,'\');
        fname = substrn("&etl_file_name",fn_pos,fn_length);
    run;

    proc Sql noprint;
        select fname into: file_name from &work_lib..fn;
        select fn_length -4 into: fn_length from &work_lib..fn;
    quit;

    data &work_lib..fl;
        floc = substrn("&etl_file_name",1,length(trim("&etl_file_name"))-&fn_length-4);
    run;

    proc Sql noprint;
        select floc into: file_location from &work_lib..fl;
    quit;

    data &work_lib..metatemp;
        attrib File_Name format = $150.;
        attrib File_Location format = $512.;
        attrib Snapshot_DT Prepared_DT Scope_start_dt Scope_end_dt format  = date9.;
        attrib createdate format = datetime22.;
        attrib mode format = $50.;
        Record_Type_Id = &RecType;
        File_Name = "&file_name";
        File_Location = "&file_location";
        Property_ID = &Property_ID;
        Past_Window_Size = &Past_Window_Size;
        Future_Window_Size = &Future_Window_Size;
        Scope_start_dt = &Snap_Date;
        Scope_end_dt = &Snap_Date;
        mode = "None";
        Snapshot_DT = &Snap_Date;
        Snapshot_TM = "&Snap_Time";
        Prepared_DT = &Prep_Date;
        Prepared_TM = "&Prep_Time";
        createdate = datetime();
        Process_Status_ID = 3;
        IsBDE = &isbde.;
    run;

    /*change the format of the metatemp to be consistent with the tenant database*/
    data &work_lib..metatemp1;
        set &work_lib..metatemp;
        format snaptm preptm time16.7;
        snaptm = input(Snapshot_TM, time16.7);
        preptm = input(Prepared_TM, time16.7);
        drop Snapshot_TM Prepared_TM;
    run;

    data &work_lib..metatemp2;
        set &work_lib..metatemp1;
        rename snaptm = Snapshot_TM;
        rename preptm = Prepared_TM;
    run;

    %if &error_codes. ne 415 %then %do;

        /*if the file has been previously processed do not reinsert record*/
        /*Build past/future occupancy dates to be used in data fill processing;*/
        data _null_;
            set &work_lib..file_metadata;
            format minOcc date9.;
            format maxOcc date9.;
            pastminOcc = snapshot_date - past_window_size;
            minOcc = snapshot_date;
            maxOcc = snapshot_date + (future_window_size-1);
            extract_time_period = maxocc-pastminOcc+1;
            call symput('minOcc', compress(minOcc));
            call symput('maxOcc', compress(maxOcc));
            call symput('extract_time_period', compress(extract_time_period));
        run;

        proc Sql noprint;
            create table &work_lib..accom_stats_temp1 as select distinct accom_type_cd from &work_lib..accom;
            create table &work_lib..mkt_accom_stats_temp1 as select distinct accom_type_cd, market_segment from &work_lib..mkt_accom;
            create table &work_lib..accom_stats as select  a.accom_type_cd as accom_type_cd, &minOcc as minOcc, &maxOcc as maxOcc 
                from &work_lib..accom_stats_temp1 as a  
                    union select b.accom_type_code as accom_type_cd, &minOcc as minOcc, &maxOcc as maxOcc from  tenant.accom_type as b  
                where b.property_id=&property_id. and b.status_id=1  order by accom_type_cd;
            create table &work_lib..mkt_accom_stats_temp2 as select distinct a.market_segment, &minOcc as minOcc, &maxOcc as maxOcc
                from &work_lib..mkt_accom_stats_temp1 as a 
                    union select b.mkt_seg_code as market_segment,&minOcc as minOcc, &maxOcc as maxOcc from  tenant.mkt_seg as b 
                where b.property_id=&property_id. and b.status_id=1;
            create table &work_lib..mkt_accom_stats as select a.*, b.market_segment 
                from &work_lib..accom_stats as a,  &work_lib..mkt_accom_stats_temp2 as b  
                    order by accom_type_cd, market_segment;
        quit;

        data &work_lib..total_stats;
            format minOcc date9.;
            format maxOcc date9.;
            minOcc = &minOcc;
            maxOcc = &maxOcc;
        run;

        %if (%eval (%ideas_util_nobs(&work_lib..mkt_accom_stats)*&extract_time_period) ne %ideas_util_nobs(&work_lib..mkt_accom)) or
            (%eval(%ideas_util_nobs(&work_lib..accom_stats)*&extract_time_period) ne %ideas_util_nobs(&work_lib..accom)) or 
            (%eval(%ideas_util_nobs(&work_lib..total_stats)*&extract_time_period) ne %ideas_util_nobs(&work_lib..total))  %then %do;
            %put Mkt Accom Window: %eval (%ideas_util_nobs(&work_lib..mkt_accom_stats)*&extract_time_period) Mkt Accom OBS: %ideas_util_nobs(&work_lib..mkt_accom);
            %put Accom Window: %eval(%ideas_util_nobs(&work_lib..accom_stats)*&extract_time_period) Accom OBS: %ideas_util_nobs(&work_lib..accom);
            %put Total Window: %eval(%ideas_util_nobs(&work_lib..total_stats)*&extract_time_period) Total OBS: %ideas_util_nobs(&work_lib..total);

            /*Fill in missing data based on client paramater;
                  If this is the first file processed for the property the default parameter is 0;

                  If the client parameter is 2 then we use the last know value and no data is inserted;*/
            %if &f_file. eq 1 %then %do;
                %ideas_datafill(1,&work_lib..mkt_accom,0,5);
                %ideas_datafill_sysvar_seg (&property_id., 0, &work_lib.);  /*needs to follow seg for occ dates*/
                %ideas_datafill(2,&work_lib..accom,0,4);
                %ideas_datafill_sysvar_accom(&property_id., &m_var.,&work_lib.);  /*needs to follow accom for occ dates*/
                %ideas_datafill(3,&work_lib..total,0,1);
            %end;
            %else %do;
                %if &c_var. ne 2 %then %do;
                    %ideas_datafill(1,&work_lib..mkt_accom,&m_var.,5);
                    %ideas_datafill_sysvar_seg (&property_id., &m_var., &work_lib.);  /*needs to follow seg for occ dates*/
                    %ideas_datafill(2,&work_lib..accom,&m_var.,4);
                    %ideas_datafill_sysvar_accom(&property_id., &m_var.,&work_lib.);   /*needs to follow accom for occ dates*/
                    %ideas_datafill(3,&work_lib..total,&m_var.,1);
                %end;
            %end;
        %end;

        /*Generate a unique process id for the creation of the temp tables;*/
        %let y=%sysfunc(compress(&request_id.,' -'));
        %let load_id = %sysfunc(substr(&y.,1,5));

        /*Set connection details to run the pass through query to update/insert the data into rdbms;*/
        %local connect_Str;
        %let connect_str = driver={SQL Server Native Client 10.0}%str(;)server=&tenant_server\&server_instance%str(;)uid=&tenant_user%str(;)pwd=&tenant_pwd%str(;)database=&tenant_db%str(;)port=&port;
        %let connect_str=complete="&connect_str";
        %local metad;

        %ideas_trans_upload_tmp(upload_table=&work_lib..metatemp2,
            like_table=tenant.file_metadata (drop = File_Metadata_ID),
            _name_in_db=metad,
            tmp_trans_lib=ten_tmp,cnt=1&load_id.);
        %let metad = ##&metad;

        proc Sql;
            connect to odbc (&connect_str autocommit=no);
            execute(
                insert into file_metadata (file_name, file_location, snapshot_dt, snapshot_tm, 
                    prepared_dt, prepared_tm, record_type_id, property_id, past_window_size,
                    future_window_size, process_status_id, isbde)
                select file_name, file_location, snapshot_dt, snapshot_tm,
                    prepared_dt, prepared_tm, record_type_id, property_id, 
                    past_window_size, future_window_size, Process_Status_ID, isbde
                from &metad;

            %if &sqlrc ne 0 %then %do;
                rollback;
                %let error_codes = 914;
                %goto EXIT;
            %end;

            commit;
            ) by odbc;
        quit;

    %end;

%EXIT:

    /*put this below the exit in the event that we aborted out of the transaction this will report the error*/
    %if &error_codes =914  %then %do;
        %ideas_util_report_errors(914, 'RDBMS transaction failed on metadata insert', '', &request_Id., 1);
        %let etl_status = 1;
    %end;

    /*generate metadata_id & process_status_id for inbound data;*/
    proc Sql noprint;
        select a.file_metadata_id into: meta_id 
            from tenant.file_metadata as a, &work_lib..metatemp2 as b
                where a.Property_ID = b.Property_ID
                    and a.SnapShot_DT = b.SnapShot_DT
                    and a.Snapshot_TM = b.Snapshot_TM    
                    and a.Prepared_DT = b.Prepared_DT  
                    and a.Prepared_TM = b.Prepared_TM 
                    and a.record_type_id = b.record_type_id
                    and a.file_name = b.file_name;
    quit;

    %if not %symexist(meta_id) %then %do;
        %ideas_util_report_errors(915, 'Failed to assign meta_id', '', &request_Id., 1);
        %let etl_status = 1;
    %end;

    proc Sql noprint;
        select process_status_id into: process_id from tenant.file_metadata 
            where file_metadata_id = &meta_id;
    quit;

    %if not %symexist(process_id) %then %do;
        %ideas_util_report_errors(916, 'Failed to assign process_id', '', &request_Id., 1);
        %let etl_status = 1;
    %end;

    data &work_lib..metastatus;
        meta_id = &meta_id.;
        process_id = &process_id.;
    run;

    proc Sql noprint;
        select count(*) into: error_ct from &work_lib..errors;
    quit;

    %if &error_codes ne 415 %then %do;
        %if "&GL_ABORT_FLAG" eq "1" or &syscc > 4 or &error_ct. > 0 %then %do;
            %let GL_ABORT_FLAG =1;
            %let etl_status = 1;
        %end;
    %end;
    %else %do;
        %let etl_status = 0;
        %let GL_ABORT_FLAG =0;
    %end;

    %let macroname=&SYSMACRONAME;

    %ideas_macro_log (start_end=END, macroname=&macroname.);
%mend ideas_type2_extract;