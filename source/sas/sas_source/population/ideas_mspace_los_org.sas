%macro ideas_mspace_los_org/store;
%let macroname=&SYSMACRONAME; 
	
%ideas_macro_log (start_end=START, macroname=&macroname.);
    

	proc sql noprint;
		select distinct mkt_seg_id into: list SEPARATED by '|' from tenant.mkt_seg where Property_ID EQ &property_id.;
	quit;

	%let ms_list = &list.;
	%let i = 1;

	%do %while (%scan(&ms_list., &i., |) ne);

		data _null_;
			call symputx ('ms',%scan(&ms_list, &i, |),'l');
		run;
        %let wklibdsn = &work_lib..Ma_los_org_&ms.;
		%let dsn = Ma_los_org_&ms.;
		%let libdsn = rm_part.ma_los_org_&ms.;

		%if %sysfunc(exist(&libdsn)) eq 1 %then %do;

			
            proc sql;
				create table &work_lib..org_ms_pace (drop= mkt_seg_id) as select * from &work_lib..org_mkt_accom_los_inventory_pace 
                where mkt_seg_id = &ms.;
			quit;


			proc copy in=rm_part out=&work_lib. memtype=data;
				select &dsn.;
			run;

			proc datasets library = &work_lib. nolist;
				change &dsn. = &dsn._fin;
			quit;

			proc append base=&wklibdsn._fin data=&work_lib..org_ms_pace force;
			run;

		%end;
		%else %do;

			proc sql;
				create table rm_part.&dsn. like rm_part.Ma_los_inv_pace_template;

			proc sql;
				create table &wklibdsn._fin(drop= total_revenue) like rm_part.Ma_los_inv_pace_template;

			proc sql;
				create table &work_lib..org_ms_pace (drop= mkt_seg_id) as select * from &work_lib..org_mkt_accom_los_inventory_pace 
                where mkt_seg_id = &ms.;
			quit;

			proc append base=&wklibdsn._fin data=&work_lib..org_ms_pace force;
			run;

		%end;

		proc copy in=&work_lib. out=rm_part memtype=data;
			select &dsn._fin;
		run;

		%let i = %sysevalf(&i. + 1);
	%end;

	* Added code to take care of missing mkt segs in org_mkt_accom_los_inventory_pace;
	proc sql;
		select distinct mkt_seg_id into: mkt_seg_list SEPARATED by '|' from rm_datap.mkt_accom_los_inventory where  
			mkt_seg_id not in(select distinct mkt_seg_id from &work_lib..org_mkt_accom_los_inventory_pace);
	quit;

	%if (%symexist(mkt_seg_list)) %then %do;
		%let mkt_list = &mkt_seg_list.;
		%let i = 1;
    
		%do %while (%scan(&mkt_list., &i., |) ne);
			data _null_;
				call symputx ('ms',%scan(&mkt_list, &i, |),'l');
			run;
        %let dms = Ma_los_org_&ms.;
		%let libdms = rm_part.ma_los_org_&ms.;
		
		%if %sysfunc(exist(&libdms)) eq 0 %then %do;
			proc sql;
				create table rm_part.&dms. like rm_part.Ma_los_inv_pace_template;
			quit;
		%end;
		
		%let i = %sysevalf(&i. + 1);
	  %end;
    %end;
* Added code to take care of missing mkt segs in org_mkt_accom_los_inventory_pace;

%let macroname=&SYSMACRONAME; 
	
%ideas_macro_log (start_end=END, macroname=&macroname.);
%mend;
