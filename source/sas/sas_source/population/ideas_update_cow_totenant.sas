%macro ideas_update_cow_totenant (work_lib, property_id, server, db, server_instance, user, pwd, port) / store;
    %let macroname=&SYSMACRONAME;

    %ideas_macro_log (start_end=START, macroname=&macroname.);
     
    /*put on temp folder on database*/
  %if %sysfunc(exist(&work_lib..cow4))  %then %do;
    data ten_tmp.cow1&load_id.;
        set &work_lib..cow4;
    run;

    /*set up connection string for sql passthrough query to insert into rdbms*/
    %local connect_Str;
    %let connect_str = driver={SQL Server Native Client 10.0}%str(;)server=&server\&server_instance%str(;)uid=&user%str(;)pwd=&pwd%str(;)database=&db%str(;)port=&port;
    %let connect_str=complete="&connect_str";
    %put Connect Str= &connect_str.;

    proc Sql;
        connect to odbc (&connect_str autocommit=no);
        execute(
            insert into costofwalk_default (property_id, accom_type_id, sunday_value, monday_value, tuesday_value, wednesday_value,
                thursday_value, friday_value, saturday_value, Created_By_User_ID)
            select property_id, accom_type_id, sunday_value, monday_value, tuesday_value, wednesday_value,
                thursday_value, friday_value, saturday_value, user_id from ##cow1&load_id.
                ) by odbc;

        %if NOT (&sysdbrc eq 0 or "&sysdbrc" eq "") %then %do;
            execute(rollback) by odbc;
            %let etl_status = 1;
            %let message = 'RDBMS Transaction Failed';
            %goto EXIT;
        %end;

        execute (
            commit
            )by odbc;
    quit;
  %end;

%EXIT:

    %if &etl_status eq 1 or &syscc > 4 %then %do;
        %let GL_ABORT_FLAG =1;
        %let etl_status = 1;
        %put 'Process Stopped';
    %end;

    %put &message;
    %let macroname=&SYSMACRONAME;

    %ideas_macro_log (start_end=END, macroname=&macroname.);
%mend ideas_update_cow_totenant;
