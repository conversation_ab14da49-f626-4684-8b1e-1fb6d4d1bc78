%macro ideas_resumable_type2_load(lib=in_xml)/store;
	%local snap_date snap_time prep_date prep_time snap_dttm snapshot_date prepared_date prep_dttm past_window_size future_window_size;
	%local meta_id process_id;
	%local property_desc property_desc_short status_desc start_end app_start_time app_end_time runtime;
	%local sqlrc status etl_file_status etl_process_status;
	%local error_codes error_bit rm_data;
	%global work_lib work_dir;
	%local c_var m_var;
	%global etl_status err_str loadtype err_ct maxerrors FILE_TYPE isbde use_group_past_data capturedate;
	%let error_codes = 0;
	%let etl_status = 0;
	%let err_str = .;
	%let etl_file_status = 0;
	%let etl_process_status = 0;
	%let maxerrors = -1;
	%let app_start_time=%sysfunc(datetime());
	%let runTime=0;
	%let syscc = 0;
	%PUT ---------------------------------------------------------------------;
	%PUT ------------------ Starting resumable Type 2 Population -----------------------;
	%PUT ---------------------------------------------------------------------;
	%put;

	/*STAGE1;*/
	data _null_;
		set  &lib..requestHeader;
		call symputx('property_Id',propertyId,'l');
		call symputx('requestId',requestId,'l');
		call symputx('tenant_server',tenant_server,'l');
		call symputx('server_instance',tenant_server_instance,'l');
		call symputx('tenant_pwd',tenant_password,'l');
		call symputx('tenant_user',tenant_user,'l');
		call symputx('tenant_db',tenant_database,'l');
		call symputx('port',tenant_db_port,'l');
		call symputx('saspath',dataset_path,'l');
		stop;
	run;

	%if &syscc > 4 %then
		%do;
			%ideas_util_report_errors(910, 'Failed to read request header', '', &request_Id., 1);
			%let etl_status = 1;
			%goto EXIT;
		%end;

	%let request_id = %str(&requestid.);
	%let sas_path = %str(&saspath.);

	data _null_;
		set  &lib..populationrequest;
		call symputx('etl_file_name',etlfilename,'g');
		call symputx('c_var',clientDataStorageValue,'g');

		if operationtype ne "BDE" and operationtype ne "CDP" then
			call symputx('OperationType','BDE','g');
		else call symputx('OperationType',OperationType,'g');
		call symputx('booked_org_enabled',BookedVsOriginalEnabled,'g');
		call symputx('skipStayedRTPopulation',skipStayedRTPopulation,'g');
		call symputx('useGroupPastData', useGroupPastData, 'g');
		call symputx('oooOverrideEnabled', oooOverrideEnabled, 'g');
		call symputx('enableReusableGroupSrps', enableReusableGroupSrps,'g');
		call symputx('useMemlib', useMemlib,'g');

		stop;
	run;

	%if &work_library.=work %then
		%do;
			%let work_lib=work;
			%let work_path=%sysfunc(pathname(work));
		%end;
	%else
		%do;
			%let work_lib=&work_library.;
			%let work_path_drive=%substr (%sysfunc(pathname(work)),1,2);
			%let work_path_root=&work_path_drive./sas;

			%ideas_util_create_dir(dir=temp,base_path=&work_path_root.);
			%let work_path_root=&work_path_drive./sas/temp;

			%ideas_util_create_dir(dir=&property_id.,base_path=&work_path_root.);
			%let work_path_root=&work_path_drive./sas/temp/&property_id.;

			%ideas_util_create_dir(dir=work,base_path=&work_path_root.);
			%let work_path=&work_path_drive./sas/temp/&property_id./work;

			%if &memlib_option. = memlib AND %upcase(&useMemlib.) eq TRUE %then
				%do;
					libname &work_lib "&work_path." memlib;
				%end;
			%else
				%do;
					libname &work_lib "&work_path.";
				%end;
		%end;

	/*Create errors table to store error logging*/
	data &work_lib..errors;
		length err_ct error_cd 8 request_id error_params error_message $ 200;
		stop;
	run;

    %put "useGroupPastData value is " &useGroupPastData;
	%let use_group_past_data = %upcase(&useGroupPastData);
	%put &=oooOverrideEnabled;
	%put &=enableReusableGroupSrps;
	%put &=useMemlib;

	%if &syscc > 4 %then
		%do;
			%ideas_util_report_errors(909, 'Failed to read population request', '', &request_Id., 1);
			%let etl_status = 1;
			%goto EXIT;
		%end;

	%if %upcase(&OperationType.) eq BDE %then
		%do;
			%let isbde=1;
			%let LoadType=%upcase(&OperationType.);
		%end;
	%else
		%do;
			%let isbde=0;
			%let LoadType=%upcase(&OperationType.);
		%end;

	%let mylog_path = %str(&saspath.);
	%let y=%sysfunc(compress(&request_id.,' -'));
	%let load_id = %sysfunc(substr(&y.,1,5));

	/*STAGE2: Connect to tenant database  */
	%ideas_connect_tenant (&tenant_db., &tenant_server., &port., &server_instance., &tenant_user., &tenant_pwd.);

	%if &etl_status = 1 %then
		%goto EXIT;

	%if &population_use_local_stg=YES %then
		%do;
			%let work_path_drive=%substr (%sysfunc(pathname(work)),1,2);
			%let work_path_root=&work_path_drive./sas;

			%ideas_util_create_dir(dir=temp,base_path=&work_path_root.);
			%let work_path_root=&work_path_drive./sas/temp;

			%ideas_util_create_dir(dir=&property_id.,base_path=&work_path_root.);
			%let work_path_root=&work_path_drive./sas/temp/&property_id.;

			%ideas_util_create_dir(dir=rm_data,base_path=&work_path_root.);
			%let work_path_root=&work_path_drive./sas/temp/&property_id.;

			%ideas_util_create_dir(dir=partitions,base_path=&work_path_root.);
			%let rm_data_perm_path=&sas_path.;
			%let rm_data_work_path=&work_path_drive./sas/temp/&property_id./rm_data;
			%let rm_part_perm_path=&sas_path./partitions;
			%let rm_part_work_path=&work_path_drive./sas/temp/&property_id./partitions;

			%if &memlib_option. = memlib %then
				%do;
					libname rm_data "&rm_data_work_path." memlib;
					libname rm_part "&rm_part_work_path." memlib;
				%end;
			%else
				%do;
					libname rm_data "&rm_data_work_path.";
					libname rm_part "&rm_part_work_path.";
				%end;

			libname rm_datap "&rm_data_perm_path.";
			libname rm_partp "&rm_part_perm_path.";

			%if %sysfunc(exist(rm_datap.BDE_accom_inventory))=0 %then
				%do;

					proc Copy in=rm_datap out=&work_lib. 
						CLONE 
						CONSTRAINT=YES 
						INDEX=YES;
						select accom_inventory total_inventory mkt_accom_inventory;
					run;

					proc Datasets library=&work_lib.;
						change accom_inventory=BDE_accom_inventory total_inventory=BDE_total_inventory mkt_accom_inventory=BDE_mkt_accom_inventory;
						copy in=&work_lib. out=rm_datap
							CLONE 
							CONSTRAINT=YES 
							INDEX=YES
							MOVE;
						select BDE_accom_inventory BDE_total_inventory BDE_mkt_accom_inventory / memtype=data;
					run;

				%end;

			proc Sql;
				select memname into: rmdatalist separated by ' '
					from dictionary.members
						where libname = upcase("RM_DATAP") and ( lowcase(memname) like '%_fin'  or 
							trim(lowcase(memname)) in ("accom_inventory" "accom_inventory_pace" "total_inventory" "total_inventory_pace" "mkt_accom_inventory"
							"delta_mkt_accom_inventory" "delta_accom_inventory" "delta_total_inventory" "filespecs"  "BDE_mkt_accom_inventory" "BDE_accom_inventory" "BDE_total_inventory" ));
			quit;

			proc Copy in=rm_datap out=rm_data 
				CLONE 
				CONSTRAINT=YES 
				INDEX=YES;
				select &rmdatalist.;
			run;

			proc Sql;
				select memname into: rmpartlist separated by ' '
					from dictionary.members
						where libname = upcase("RM_PARTP") and substr(upcase(memname),1,6) ne "MA_LOS";
			quit;

			proc Copy in=rm_partp out=rm_part 
				CLONE 
				CONSTRAINT=YES 
				INDEX=YES;
				select &rmpartlist. MA_LOS_INV_PACE_TEMPLATE;
			run;

		%end;
	%else
		%do;
			%let rm_data_perm_path=&sas_path.;
			%let rm_part_perm_path=&sas_path./partitions;
			libname rm_data "&sas_path.";
			libname rm_part "&sas_path.\partitions";

			%if %sysfunc(exist(rm_data.BDE_accom_inventory))=0 %then
				%do;

					proc Copy in=rm_data out=&work_lib. 
						CLONE 
						CONSTRAINT=YES 
						INDEX=YES;
						select accom_inventory total_inventory mkt_accom_inventory;
					run;

					proc Datasets library=&work_lib.;
						change accom_inventory=BDE_accom_inventory total_inventory=BDE_total_inventory mkt_accom_inventory=BDE_mkt_accom_inventory;
						copy in=&work_lib. out=rm_data
							CLONE 
							CONSTRAINT=YES 
							INDEX=YES
							MOVE;
						select BDE_accom_inventory BDE_total_inventory BDE_mkt_accom_inventory / memtype=data;
					run;

				%end;
		%end;

	/*Missing variable specification  1 = Zero fill (default) 2 = Last Known Good, 3 = Missing (-1 for sql . for sas)*/
	/*this will be passed in by bpms in the future*/
	/*Option 2 only gets filled with a value (0) when it is the first file being processed has missing records*/
	/*    %let c_var = 1;*/
	%if &c_var. eq 1 or 2 %then
		%do;
			%let m_var = 0;
		%end;

	%if &c_var. = 3 %then
		%do;
			%let m_var = .;
		%end;

	proc Sql noprint;
		select property_id into : pid from tenant.property where property_id = &property_id. and status_id=1;
		select property_code into: pcode from tenant.property where property_id = &property_id. and status_id=1;
		select property_name into :property_name from tenant.property where property_Id=&property_id. and status_id=1;
	quit;

	%if not %symexist(pid) %then
		%do;
			%str(&requestid.);
			%let property_desc=%str(Property: %trim(&property_id.) does not exist in the %trim(&tenant_db.) database);
			%let property_desc_short=%str(Property: %trim(&property_id.) does not exist in the %trim(&tenant_db.) database);

			%ideas_util_inc_errors;
			%ideas_util_report_errors(913, "&property_desc.", '', &request_Id., &err_ct.);
			%let etl_status = 1;
			%goto EXIT;
		%end;
	%else
		%do;
			%let property_desc=%str(Property: %trim(&property_id.)  -  %trim(&pcode.));
			%let property_desc_short=%str(Property %trim(&property_id.)  -  %trim(&pcode.));
		%end;

	/*STAGE3;*/
	%ideas_type2_extract (&c_var.,&m_var., &tenant_db., &tenant_server., &port., &server_instance., &tenant_user., &tenant_pwd.);

	%if &etl_status = 1 %then %goto EXIT; /*Inound data is bad and/or processed failed - Abort*/

		data _null_;
			set &work_lib..metastatus;
			call symputx('meta_id',meta_id,'l');
			call symputx('process_id',process_id,'l');
		run;

		/* Check the status of the inbound file */
		%if &process_id. = 3 %then
			%goto Stage4;

		%if &process_id. = 13 %then
			%goto Exit;

%Stage4:

		/* ETL Status = 4 Identify deltas, create temp tables, update sas tables - update metadata processid */
		%if &etl_status = 1 %then
			%goto EXIT;

		%ideas_type2_deltas_resumable;

		%if &etl_status = 1 %then
			%goto EXIT;
			
%Stage5:

		/* ETL Status = 5 Update rdbms with delta tables prom previous run;
		RDBMS update for the file metadata is within the same commit as the data upload; */
		%if &etl_status = 1 %then
			%goto EXIT;

		%ideas_type2_RDBMS_load_resumable(&tenant_db., &tenant_server., &port., &server_instance., &tenant_user., &tenant_pwd.);

		%if &etl_status = 1 %then
			%goto EXIT;

		%ideas_cow (work_lib=&work_lib., factor=1.5, property_id=&property_id.);
		%ideas_update_cow_totenant (work_lib=&work_lib., property_id=&property_id., server=&tenant_server., db=&tenant_db., server_instance=&server_instance., 
			user=&tenant_user., pwd=&tenant_pwd., port=&port.);

		%if &etl_status= 1 %then
			%do;
				%let etl_status = 1;
				%put 'Cost of walk processing failed';
				%goto EXIT;
			%end;

%Stage6:

		/*Rename sas _fin datasets to original name*/
		%if &etl_status = 1 %then
			%goto EXIT;


		%put Stage6 copying fin tables to permanent lib;
		
		data &rm_data..delta_accom_inventory;
			set &work_lib..delta_accom_inventory (drop = snapshot_dttm);
		run;
		
		data &rm_data..delta_mkt_accom_inventory;
			set &work_lib..delta_mkt_accom_inventory (drop = snapshot_dttm);
		run;

		data &rm_data..delta_total_inventory;
			set &work_lib..delta_total_inventory (drop = snapshot_dttm);
		run;

		proc Copy in=&work_lib. out=rm_data memtype=data;
        	select accom_inventory_fin accom_inventory_pace_fin total_inventory_fin total_inventory_pace_fin mkt_accom_inventory_fin;
    	run;

		%if &loadtype. = BDE %then
			%do;

				proc Datasets library=rm_data nolist;
					Delete accom_inventory accom_inventory_pace total_inventory total_inventory_pace mkt_accom_inventory
						BDE_accom_inventory BDE_total_inventory BDE_mkt_accom_inventory BDE_Group_block;
					Change accom_inventory_fin=accom_inventory accom_inventory_pace_fin=accom_inventory_pace total_inventory_fin=total_inventory 
						total_inventory_pace_fin=total_inventory_pace mkt_accom_inventory_fin=mkt_accom_inventory;
					Copy in=rm_data out = &work_lib.
						CLONE 
						CONSTRAINT=YES 
						INDEX=YES;
					select accom_inventory total_inventory mkt_accom_inventory / memtype=data;
				run;

				proc Datasets library=&work_lib.;
					Delete BDE_accom_inventory BDE_total_inventory BDE_mkt_accom_inventory BDE_Group_Block;
					change accom_inventory=BDE_accom_inventory total_inventory=BDE_total_inventory 
						mkt_accom_inventory=BDE_mkt_accom_inventory group_block=BDE_group_block;
					Delete accom_inventory accom_inventory_pace total_inventory total_inventory_pace mkt_accom_inventory;
					Copy in=&work_lib.  out = rm_data 
						CLONE 
						CONSTRAINT=YES 
						INDEX=YES;
					select BDE_group_block BDE_accom_inventory BDE_total_inventory BDE_mkt_accom_inventory / memtype=data;
				run;

			%end;
		%else
			%do;

				proc Datasets library=rm_data nolist;
					delete accom_inventory  accom_inventory_pace_fin total_inventory total_inventory_pace_fin mkt_accom_inventory;
					change accom_inventory_fin=accom_inventory total_inventory_fin=total_inventory 
						mkt_accom_inventory_fin=mkt_accom_inventory;
				run;

			%end;

		%if &syscc > 4 %then
			%do;
				%ideas_util_report_errors(931, 'Lock on SAS Datasets', '', &request_Id., 1);
				%let etl_status = 1;
				%goto EXIT;
			%end;

		%if &loadtype. = BDE %then
			%do;
				%ideas_mspace_remove_fin;
			%end;

		/* idnpak - DE1474 */
		%if &syscc > 4 %then
			%do;
				%ideas_util_report_errors(931, 'Lock on SAS dataset', '', &request_Id., 1)
				%let etl_status = 1;
				%goto EXIT;
			%end;
		
%Stage7:

		/* ETL Status = 7 Truncate Deltas and update metadata processid    */
		%if &etl_status = 1 %then
			%goto EXIT;

		%ideas_UTIL_TRUNCATE_TABLE(rm_data.delta_mkt_accom_inventory);

		%if &etl_status = 1 %then
			%goto EXIT;

		%ideas_UTIL_TRUNCATE_TABLE(rm_data.delta_accom_inventory);

		%if &etl_status = 1 %then
			%goto EXIT;

		%ideas_UTIL_TRUNCATE_TABLE(rm_data.delta_total_inventory);

		%if &etl_status = 1 %then
			%goto EXIT;

		%ideas_UTIL_TRUNCATE_TABLE(rm_data.filespecs);

		%if &etl_status = 1 %then
			%goto EXIT;

		proc Sql noprint;
			select count(*), memname into: removecount, :removelist separated by ' '
				from dictionary.members
					where libname = upcase("RM_PART") and  
						( trim(lowcase(memname)) like 'ma%fin') and  
						( trim(lowcase(memname)) not like 'ma_los%fin');
		quit;

		%if &removecount. >0 %then
			%do;

				proc Datasets library=rm_part noprint nolist;
					Delete &removelist.;
				run;

				quit;

			%end;

		%ideas_UpdateETLStatus(13,&tenant_db., &tenant_server., &port., &server_instance., &tenant_user., &tenant_pwd.,&meta_id.);
%EXIT:

		/* Output to the error log if the etl file has been previously processed */
		%if &error_codes. = 415 %then
			%do;
				%ideas_util_report_errors(415, 'ETL file has been previously processed', '', &request_Id., 1);
				%let syscc = 4;
				%put 'ETL file has been previously processed';
			%end;

		proc Sql noprint;
			select count(*) into: errcount from &work_lib..errors;
		quit;

		%if &errcount. > 0 %then
			%do;

				proc Sql noprint;
					select distinct error_message into: err_str from &work_lib..errors;
				quit;

			%end;

		data _NULL_;
			FILE resXml;
			put '<?xml version="1.0" encoding="UTF-8"?>';
			put '<SASResponse xmlns="http://xml.common.pacman.tetris.ideas.com/schema/population/response/v1">';
			put "<ResponseHeader> <PropertyId>&property_Id</PropertyId>" @;
			put "<operationName>population</operationName>" @;
			put "<requestId>&request_Id</requestId>";
			stop;
		run;

		%if "&GL_ABORT_FLAG" eq "1" or &syscc > 4 or &etl_status = 1 %then
			%do;
				%let GL_ABORT_FLAG =1;
				%let syscc = 5;
				%let etl_status = 1;

				%if &error_codes ne 415 %then
					%do;
						%let  xptFileName=%substr(&etl_file_name., 1, %length(&etl_file_name.)-4).xpt;

						proc Cport lib=&work_lib.                                                                                                                                                                                                                                     
							file="&xptFileName";
						run;

					%end;
			%end;

		data _NULL_;
			FILE resXml MOD;
			set &work_lib..errors;

			if _N_ > 0 then
				put "<ErrorMessage>&err_str.</ErrorMessage>";
		run;

		data _NULL_;
			FILE resXml MOD;
			put "</ResponseHeader><PopulationResponse><operationType>&loadtype</operationType>";
		run;

		%if %sysfunc(exist(&work_lib..mkt_seg_unassigned)) %then
			%do;

				data _NULL_;
					FILE resXml MOD;
					set &work_lib..mkt_seg_unassigned;

					if _N_ > 0 then
						put "<unassignedMarketSegments>";
					stop;
				run;

				data _NULL_;
					FILE resXml MOD;
					set &work_lib..mkt_seg_unassigned;

					if _N_ > 0 then
						put "<marketSegmentCode>" mkt_seg_code "</marketSegmentCode>";
				run;

				data _NULL_;
					FILE resXml MOD;
					set &work_lib..mkt_seg_unassigned;

					if _N_ > 0 then
						put "</unassignedMarketSegments>";
					stop;
				run;

			%end;

		%if %sysfunc(exist(&work_lib..Accom_type_unassigned)) %then
			%do;

				data _NULL_;
					FILE resXml MOD;
					set &work_lib..Accom_type_unassigned;

					if _N_ > 0 then
						put "<unassignedRoomTypes>";
					stop;
				run;

				data _NULL_;
					FILE resXml MOD;
					set &work_lib..Accom_type_unassigned;

					if _N_ > 0 then
						put "<roomTypeCode>" accom_type_code "</roomTypeCode>";
				run;

				data _NULL_;
					FILE resXml MOD;
					set &work_lib..Accom_type_unassigned;

					if _N_ > 0 then
						put "</unassignedRoomTypes>";
					stop;
				run;

			%end;

		/*Cow alert for defaulted dow's by accommodation type id*/
		%if %sysfunc(exist(&work_lib..defaultcow)) %then
			%do;

				data _NULL_;
					FILE resXml MOD;
					set &work_lib..defaultcow;

					if _N_ > 0 then
						put "<defaultedCowRoomTypes>";
					stop;
				run;

				data _NULL_;
					FILE resXml MOD;
					%let sep="|";
					%let msg= Accom_Type_Code  dow1&sep dow2&sep dow3&sep dow4&sep dow5&sep dow6&sep dow7;
					set &work_lib..defaultcow;

					if _N_ > 0 then
						put "<roomTypeCode>" &msg "</roomTypeCode>";
				run;

				data _NULL_;
					FILE resXml MOD;
					set &work_lib..defaultcow;

					if _N_ > 0 then
						put "</defaultedCowRoomTypes>";
					stop;
				run;

			%end;

		data _NULL_;
			FILE resXml MOD;
			put "</PopulationResponse></SASResponse>";
		run;

		data _null_;
			set &work_lib..file_metadata;
			call symputx('past_window_size_report',past_window_size,'l');
			call symputx('future_window_size_report',future_window_size,'l');
		run;

		%let app_end_time=%sysfunc(datetime());
		%let runTime=%sysfunc(round(&app_end_time-&app_start_time, .05));
		%let  prepared_dttm_temp=%sysfunc(putn(&prep_dttm,datetime20.));
		%let  snapshot_dttm_temp=%sysfunc(putn(&snap_dttm,datetime20.));

		%if "&GL_ABORT_FLAG" eq "1" or &syscc > 4 or &etl_status = 1 %then
			%let Status_desc=Failed;
		%else
			%do;
				%let Status_desc=Successful;

				%if %upcase(&Sas_Debug.)=FALSE %then
					%do;

						proc printto log="&logfile" new;
						run;

					%end;
			%end;

		%if &error_codes eq 415 %then
			%let Status_desc=Successful - ETL File Previously Processed;
		Options nomacrogen NoSymbolgen nomlogic nomprint nomfile;
		%put ---------------------------------------------------------------------;
		%put --------------------   Resumable Type 2 population Summary  -------------------;
		%put ---------------------------------------------------------------------;
		%put;
		%Put -         Property Info: &property_desc.;
		%put -     Population Status: &status_desc.;
		%Put -   Population Run time: &runTime. seconds;
		%Put -             Load Type: &loadtype.;
		%Put -             File Type: &file_type.;
		%put -   Snap Shot Date/Time: &snapshot_dttm_temp.;
		%put -    Prepared Date/Time: &prepared_dttm_temp.;
		%put -      Past Window Size: &past_window_size_report.;
		%put -    Future Window Size: &future_window_size_report.;
		%put -            Request ID: &requestId.;
		%put;
		%put -   Population ETL File: &etl_file_name.;
		%put -         Sas Data Path: &saspath.;
		%put -    Mkt Partition Path: &rm_part_perm_path.;
		%put -       Population Path: &rm_data_perm_path.;
		%put;
		%put -     Work Library Path: &work_path.;
		%put -     Work Library Name: &work_lib;
		%put -         Memlib Option: &memlib_option;
		%put -     Use Local Storage: &population_use_local_stg;
		%put;
		%put -       Database Server: &tenant_server.;
		%put -       Server Instance: &server_instance.;
		%put -         Database Name: &tenant_db.;
		%put -         Database User: &tenant_user.;
		%put -         Database Port: &port.;
		%put -    oooOverrideEnabled: &oooOverrideEnabled.;
		%put -enableReusableGroupSrps: &enableReusableGroupSrps.;
		%put -             useMemlib: &useMemlib.;
		%put;

		data _null_;
			set &work_lib..performance;

			if _n_=1 then
				do;
					put '*********************************************************************';
					put '***************  Resumable Type 2 Population Macro Summary  *******************';
					put '*********************************************************************';
					put ' ';
					put  @1 '-    Macro Name                        Status            Run Time(seconds)';
				end;

			put @ 1'-' @ 5  macro_name    @ 40  macro_status  @ 60    macro_run_time_seconds;
		run;

		%if &etl_status. = 1 and %ideas_util_nobs(&work_lib..errors) > 0 %then
			%do;

				data _null_;
					set &work_lib..errors;

					if _n_=1 then
						do;
							put '*********************************************************************';
							put '***************** Resumable Type 2 Population Error Summary *******************';
							put '*********************************************************************';
							put ' ';
							put  @ 1 '- Error message';
							PUT @ 1  '---------------------------------------------------------------------';
						end;

					put @ 3  error_message;
				run;

			%end;

		%PUT ---------------------------------------------------------------------;
		%PUT -------------------- Ending  Resumable Type 2 Population ----------------------;
		%PUT ---------------------------------------------------------------------;
		%PUT;

		%if &population_use_local_stg. eq YES %then
			%do;

				proc Copy in=rm_data out=rm_datap 
					CLONE 
					CONSTRAINT=YES 
					INDEX=YES;
				run;

				proc Copy in=rm_part out=rm_partp 
					CLONE 
					CONSTRAINT=YES 
					INDEX=YES;
				run;

				proc Datasets library = rm_data kill memtype=data nolist noprint;
				quit;

				proc Datasets library = rm_part kill memtype=data nolist noprint;
				quit;

			%end;

		%if &work_lib. ne work %then
			%do;

				proc Datasets library = &work_lib. kill memtype=data nolist noprint;
				quit;

			%end;

%mend ideas_resumable_type2_load;
