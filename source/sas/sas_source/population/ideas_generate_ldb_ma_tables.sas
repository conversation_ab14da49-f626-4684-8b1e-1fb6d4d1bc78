%macro ideas_generate_ldb_ma_tables(lib=in_xml) /store;
 
	 %PUT ---------------------------------------------------------------------;
	 %PUT ------------------ Starting LDB MA Tables generation -----------------------;
	 %PUT ---------------------------------------------------------------------;
	 %put;
	 
	 %put Work Library Path: %sysfunc(pathname(work));

	  proc Datasets library = work memtype=data;
	   quit;
	   
	 data _null_;
		set &lib..RequestHeader;
		call symputx('property_id',propertyId,'l');	
		call symputx('tetris',dataset_path,'l');		
		call symputx('DB_SERVER',tenant_server,'l');
		call symputx('DB_SERVER_INST',tenant_server_instance,'l');
		call symputx('DB_USER',tenant_user,'l');
		call symputx('DB_PASSWORD',tenant_password,'l');
		call symputx('DB_NAME',tenant_database,'l');
		call symputx('DB_PORT',tenant_db_port,'l');
		call symputx('saspath',dataset_path,'l');
		stop;
	 run;

	 data _null_;
		set &lib..LimitedDataBuildRequestType;
		call symputx('location_path',ldbInputFolder,'l');
		call symputx('zip_file',zipFile,'l');
		call symputx('param_file',paramFile,'l');
		call symputx('startDate', startDate, 'g');
		call symputx('endDate', endDate, 'g');
		call symputx('stopPopulatingPartitionsTable',stopPopulatingPartitionsTable,'G');
		stop;
	 run;

	%put ProperytID=&property_id; 
	%put libnameTetris=&tetris;
	%put locationPath=&location_path;
	%put zipFile=&zip_file;
	%put paramFile=&param_file;
	%put dbServer=&DB_SERVER;
	%put dbServerInstance=&DB_SERVER_INST;
	%put dbUser=&DB_USER;
	%put dbPassword=&DB_PASSWORD;
	%put dbName=&DB_NAME;
	%put dbPort=&DB_PORT;
	%put startDate=&startDate;
	%put endDate =&endDate;
	
	%let request_id = %str(&requestid.);
    %let sas_path = %str(&saspath.);
    %let mylog_path = %str(&saspath.);

	%if &work_library.=work %then %do;
        %let work_lib=work;
        %let work_path=%sysfunc(pathname(work));
    %end;
    %else %do;
        %let work_lib=&work_library.;
        %let work_path_drive=%substr (%sysfunc(pathname(work)),1,2);
        %let work_path_root=&work_path_drive./sas;

        %ideas_util_create_dir(dir=temp,base_path=&work_path_root.);
        %let work_path_root=&work_path_drive./sas/temp;

        %ideas_util_create_dir(dir=&property_id.,base_path=&work_path_root.);
        %let work_path_root=&work_path_drive./sas/temp/&property_id.;

        %ideas_util_create_dir(dir=work,base_path=&work_path_root.);
        %let work_path=&work_path_drive./sas/temp/&property_id./work;

        %if &memlib_option. = memlib %then %do;
            libname &work_lib "&work_path." memlib;
        %end;
        %else %do;
            libname &work_lib "&work_path.";
        %end;
    %end;

    /* Create errors table to store error logging */
    data &work_lib..errors;
        length err_ct error_cd 8 request_id error_params error_message $ 200;
        stop;
    run;
	
	%if &population_use_local_stg=YES %then %do;
        %let work_path_drive=%substr (%sysfunc(pathname(work)),1,2);
        %let work_path_root=&work_path_drive./sas;

        %ideas_util_create_dir(dir=temp,base_path=&work_path_root.);
        %let work_path_root=&work_path_drive./sas/temp;

        %ideas_util_create_dir(dir=&property_id.,base_path=&work_path_root.);
        %let work_path_root=&work_path_drive./sas/temp/&property_id.;

        %ideas_util_create_dir(dir=rm_data,base_path=&work_path_root.);
        %let work_path_root=&work_path_drive./sas/temp/&property_id.;

        %ideas_util_create_dir(dir=partitions,base_path=&work_path_root.);
        %let rm_data_perm_path=&sas_path.;
        %let rm_data_work_path=&work_path_drive./sas/temp/&property_id./rm_data;
        %let rm_part_perm_path=&sas_path./partitions;
        %let rm_part_work_path=&work_path_drive./sas/temp/&property_id./partitions;

        %if &memlib_option. = memlib %then %do;
            libname rm_data "&rm_data_work_path." memlib;
            libname rm_part "&rm_part_work_path." memlib;
        %end;
        %else %do;
            libname rm_data "&rm_data_work_path.";
            libname rm_part "&rm_part_work_path.";
        %end;

        libname rm_datap "&rm_data_perm_path.";
        libname rm_partp "&rm_part_perm_path.";
     
    %end;
    %else %do;
        %let rm_data_perm_path=&sas_path.;
        %let rm_part_perm_path=&sas_path./partitions;
        libname rm_data "&sas_path.";
        libname rm_part "&sas_path.\partitions";
    %end;

	%ideas_connect_tenant (&DB_NAME., &DB_SERVER., &DB_PORT., &DB_SERVER_INST., &DB_USER., &DB_PASSWORD.);

	%if &syscc > 4 %then %do;
        %ideas_util_inc_errors;
        %ideas_util_report_errors(1218, 'ERROR: LDB Connection to SQL DB failed', '', &req_id, 1);
        %goto EXIT;
    %end;

	%ideas_zero_fill_occdates;

	%if &syscc > 4 %then %do;
        %ideas_util_inc_errors;
        %ideas_util_report_errors(1216, 'ERROR: LDB zero fill occupancy dates failed', '', &req_id, &err_ct.);
        %goto EXIT;
    %end;

    %if %upcase(&stopPopulatingPartitionsTable) eq FALSE %then %do;
        %ideas_msldb;
    %end;

	%if &syscc > 4 %then %do;
        %ideas_util_inc_errors;
        %ideas_util_report_errors(1217, 'ERROR: LDB create and Zero fill Market Segment tables in partitions failed', '', &req_id, &err_ct.);
        %goto EXIT;
    %end;

	%ideas_zero_fill_activity_data;

	%if &syscc > 4 %then %do;
        %ideas_util_inc_errors;
        %ideas_util_report_errors(1219, 'ERROR: LDB Zero fill Activity Data failed', '', &req_id, &err_ct.);
        %goto EXIT;
    %end;

	%EXIT:
	data _null_;
	    set &work_lib..errors;
	    where error_cd ne 0;
	    FILE  "&outname." DLM=',';
	    PUT  error_cd error_message request_id;
	run;
%mend;

