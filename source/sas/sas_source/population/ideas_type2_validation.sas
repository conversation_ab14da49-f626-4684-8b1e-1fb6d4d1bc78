%macro ideas_type2_validation(lib=in_xml) / store;
	/*Variables;*/
	%local req_id snap_date snap_time prep_date prep_time snap_dttm prep_dttm;
	%local meta_id process_id;
	%local sqlrc status etl_file_status etl_process_status;
	%local error_bit rm_data;
	%global work_lib work_dir;
	%local CTAT CSAT PTAT PSAT PT CT GB GM;
	%local c_var m_var String;
	%global etl_status err_str err_ct file_references property_desc property_desc_short;
	%let etl_status = 0;
	%let error_codes = .;
	%let app_start_time=%sysfunc(datetime());
	%let runTime=0;
	%PUT ---------------------------------------------------------------------;
	%PUT ---------------- Starting Type 2 Validation -------------------------;
	%PUT ---------------------------------------------------------------------;
	%put;

	data _null_;
		set  &lib..requestHeader;
		call symputx('property_Id',propertyId,'l');
		call symputx('requestId',requestId,'l');
		call symputx('tenant_server',tenant_server,'l');
		call symputx('server_instance',tenant_server_instance,'l');
		call symputx('tenant_pwd',tenant_password,'l');
		call symputx('tenant_user',tenant_user,'l');
		call symputx('tenant_db',tenant_database,'l');
		call symputx('port',tenant_db_port,'l');
		call symputx('saspath',dataset_path,'l');
		stop;
	run;

	%if &syscc > 4 %then
		%do;
			%ideas_util_report_errors(910, 'Failed to read Type Two request header',  '', &requestId.,1);
			%let etl_status = 1;
			%goto EXIT;
		%end;

	%if &work_library.=work %then
		%do;
			%let work_lib=work;
			%let work_path=%sysfunc(pathname(work));
		%end;
	%else
		%do;
			%let work_lib=&work_library.;
			%let work_path_drive=%substr (%sysfunc(pathname(work)),1,2);
			%let work_path_root=&work_path_drive./sas;

			%ideas_util_create_dir(dir=temp,base_path=&work_path_root.);
			%let work_path_root=&work_path_drive./sas/temp;

			%ideas_util_create_dir(dir=&property_id.,base_path=&work_path_root.);
			%let work_path_root=&work_path_drive./sas/temp/&property_id.;

			%ideas_util_create_dir(dir=work,base_path=&work_path_root.);
			%let work_path=&work_path_drive./sas/temp/&property_id./work;

			%if &memlib_option. = memlib %then
				%do;
					libname &work_lib "&work_path." memlib;
				%end;
			%else
				%do;
					libname &work_lib "&work_path.";
				%end;
		%end;

	%let sas_path = %str(&saspath.);
	%let request_id = %str(&requestid.);

	data &work_lib..errors;
		length err_ct error_cd 8 request_id error_params error_message $ 200;
		stop;
	run;

	data _null_;
		set  &lib..populationrequest;
		call symputx('etl_file_name',etlfilename,'l');
		call symputx('maxErrors',maxErrors,'l');
		call symputx('logpath',logfilepath,'l');
		stop;
	run;
	%if &syscc > 4 %then
		%do;
			%ideas_util_report_errors(909, 'Failed to read Type Two Validation request',  '', &request_Id.,1);
			%let etl_status = 1;
			%goto EXIT;
		%end;

	%let mylog_path = %str(&file_references_root.);

	/*Check to make sure ETL file exists, if not sleep for up to 15 seconds, then if not abort*/
	%ideas_connect_tenant (&tenant_db., &tenant_server., &port., &server_instance., &tenant_user., &tenant_pwd.);

	%if &etl_status = 1 %then %goto EXIT; /*Connection failed - Abort*/

		proc sql noprint;
			select property_id into : pid from tenant.property where property_id = &property_id. and status_id=1;
			select property_code into: pcode from tenant.property where property_id = &property_id. and status_id=1;
			select property_name into :property_name from tenant.property where property_Id=&property_id. and status_id=1;
		quit;

		%if not %symexist(pid) %then
			%do;
				%let property_desc=%str(Property: %trim(&property_id.) does not exist in the %trim(&tenant_db.) database);
				%let property_desc_short=%str(Property: %trim(&property_id.) does not exist in the %trim(&tenant_db.) database);

				%ideas_util_inc_errors;
				%ideas_util_report_errors(913, "&property_desc.", '', &request_Id., &err_ct.);
				%let etl_status = 1;
			%end;
		%else
			%do;
				%let property_desc=%str(Property: %trim(&property_id.)  -  %trim(&pcode.));
				%let property_desc_short=%str(Property %trim(&property_id.)  -  %trim(&pcode.));
			%end;

		%ideas_trans_exe(sql=%str(select * from tenant.record_type),
		_output_table=&work_lib..record_type );

		proc sql noprint;
			select record_type_id into :CTAT from &work_lib..record_type where upcase(record_type_name)='CTAT' and status_id=1;
			select record_type_id into :CSAT from &work_lib..record_type where upcase(record_type_name)='CSAT' and status_id=1;
			select record_type_id into :PTAT from &work_lib..record_type where upcase(record_type_name)='PTAT' and status_id=1;
			select record_type_id into :PSAT from &work_lib..record_type where upcase(record_type_name)='PSAT' and status_id=1;
			select record_type_id into :PT from &work_lib..record_type where upcase(record_type_name)='PT' and status_id=1;
			select record_type_id into :CT from &work_lib..record_type where upcase(record_type_name)='CT' and status_id=1;
			select record_type_id into :GM from &work_lib..record_type where upcase(record_type_name)='GROUPMASTER' and status_id=1;
			select record_type_id into :GB from &work_lib..record_type where upcase(record_type_name)='GROUPBLOCK' and status_id=1;
			select record_type_id into: RecType from &work_lib..record_type where upcase(record_type_name) = 'T2SNAP' and status_id=1;;
		quit;


		/************************************************************************/
		/*                                                                      */
		/* Validate Required SSO User in database for population processing     */
		/*                                                                      */
		/************************************************************************/
		proc sql noprint;
			select distinct user_id into: user_id from tenant.users where User_Name = 'SSO User';
		quit;

		%if not (%symexist(user_id)) %then
			%do;
				%ideas_util_inc_errors;
				%ideas_util_report_errors(911, "%str(SSO User is not defined in the  %trim(&tenant_db.) database )",  "&property_desc.", &request_Id., &err_ct.);
				%let etl_status = 1;
			%end;

		%ideas_trans_exe(sql=%str(select distinct accom_type_code,accom_type_id, accom_class_id, roh_type from tenant.accom_type where status_id =1 and property_id = &property_id.),
			_output_table=&work_lib..accom_type);
		%ideas_trans_exe(sql=%str(select distinct mkt_seg_code,mkt_seg_id from tenant.mkt_seg where status_id =1 and property_id = &property_id.),
			_output_table=&work_lib..mkt_seg);
		%ideas_trans_exe(sql=%str(select distinct accom_class_id, accom_class_code, master_class from tenant.Accom_Class where status_id =1 and property_id = &property_id.),
			_output_table=&work_lib..accom_class);

		/*If the property does not exist in the file metadata set the default dtttm to 0 so the file will be processed;*/
		/**f_file indicates if it is the first file processed = 0 = false 1 = true;*/
		*Do the property lookup again, this time in the file_metadata table to see if an extract has ever been processed;
		proc sql noprint;
			select property_id into:  tpid from tenant.file_metadata
				where property_id = &property_id. and record_type_id = &RecType.;
		quit;

		%local f_file;
		%let f_file = 0;

		%if not %symexist(tpid) %then
			%do;

				data &work_lib..ssdt;
					attrib ssnap_dt format = date9.;
					attrib sssnap_tm format = time10.;
					ssnap_dt = 0;
					ssnap_tm = 0;
					sssnap_tm = 0;
					isbde = 1;
					process_status_id = .;
				run;

				data &work_lib..spdt;
					attrib sprep_dt format = date9.;
					attrib ssprep_tm format = time10.;
					sprep_dt = 0;
					sprep_tm = 0;
					ssprep_tm = 0;
					process_status_id = .;
				run;

				%let f_file = 1;
			%end;
		%else
			%do;
				/*Get the max system/prepared dates from the global file to be compared against the input file for validation;*/
				proc sql;
					create table &work_lib..ssdt1 as
						select distinct snapshot_dt as ssnap_dt, max(snapshot_tm) as ssnap_tm, max(snapshot_tm) as sssnap_tm, isbde, process_status_id
							from tenant.file_metadata
								where property_id = &property_id and record_type_id = &rectype
									and (select max(snapshot_dt) from tenant.file_metadata 
								where property_id = &property_id and record_type_id = &rectype) = snapshot_dt;
					create table &work_lib..spdt1 as
						select distinct prepared_dt as sprep_dt, max(prepared_tm) as sprep_tm, max(prepared_tm) as ssprep_tm
							from tenant.file_metadata
								where property_id = &property_id and record_type_id = &rectype
									and (select max(prepared_dt) from tenant.file_metadata 
								where property_id = &property_id and record_type_id = &rectype) = prepared_dt;
				quit;

				data &work_lib..ssdt;
					set &work_lib..ssdt1;
					attrib ssnap_dt format = date9.;
					attrib sssnap_tm format = time10.;
				run;

				data &work_lib..spdt;
					set &work_lib..spdt1;
					attrib sprep_dt format = date9.;
					attrib ssprep_tm format = time10.;
				run;

			%end;

		data &work_lib..dates;
			set &work_lib..ssdt;
			set &work_lib..spdt;
		run;

		data &work_lib..dates;
			set &work_lib..dates;
			ssnap_dttm=input(put(ssnap_dt,date9.)||':'||put(ssnap_tm,time8.),datetime18.);
			sprep_dttm=input(put(sprep_dt,date9.)||':'||put(sprep_tm,time8.),datetime18.);
			format ssnap_dttm sprep_dttm datetime18.;
		run;

		/****************************************************************************************************************/
		/*                                                                                                              */
		/* Input the inbound ETL file. Read in the pip-delimited text input file. File contains total, accom,           */
		/* mkt_accom records. As it is read in, separate the data into five files; metadata (one input file record)     */
		/* accommodation data, market data, total data, and bad records (identified by unknown record types.)           */
		/* If there are any bad records, error out the entire load.                                                     */
		/* Error codes our written out to a table based on the input verification;                                      */
		/*                                                                                                              */
		/****************************************************************************************************************/
		/*Create temp tables to store the hash output;*/
		data &work_lib..gc_val;
			attrib occupancy_dt length=8;
			attrib group_code length=$50;
		run;

		data &work_lib..gb_val;
			attrib occupancy_dt length=8;
			attrib group_code length=$50;
			attrib accom_type_cd length=$50;
		run;

		data &work_lib..ct_val;
			attrib occupancy_dt length=8;
			attrib accom_type_cd length=$50;
		run;

		data &work_lib..cst_val;
			attrib occupancy_dt length=8;
			attrib accom_type_cd length=$50;
			attrib market_segment length=$50;
		run;

		data &work_lib..ct_date;
			attrib occupancy_dt length=8;
		run;

		data &work_lib..pt_date;
			attrib occupancy_dt length=8;
		run;

		data &work_lib..file_metadata (keep=record_type client_code property_id past_window_size future_window_size
			snapshot_date snapshot_time prepared_date prepared_time)
			&work_lib..accom  (keep=record_type_id occupancy_dt accom_type_cd  accom_capacity rooms_not_avail_maint 
			rooms_not_avail_other rooms_sold arrivals departures room_revenue food_revenue 
			total_revenue cancellations no_shows)
			&work_lib..mkt_accom (keep=record_type_id occupancy_dt accom_type_cd market_segment rooms_sold 
			arrivals departures room_revenue food_revenue total_revenue cancellations no_shows)
			&work_lib..total (keep=record_type_id occupancy_dt total_accom_capacity rooms_not_avail_maint 
			rooms_not_avail_other rooms_sold arrivals departures room_revenue food_revenue 
			total_revenue cancellations no_shows)
			&work_lib..gm (keep=record_type_id property_id Group_Code Group_Name Group_Description Master_Group_Code Group_Status_Code market_segment
			start_dt end_date group_type_code booking_dt pickup_type_code cancel_dt booking_type 
			sales_person cut_off_date cut_off_days)
			&work_lib..gb (keep=record_type_id Group_Code occupancy_dt accom_type_cd blocks pickup original_blocks rate)
			&work_lib..badrecs (keep=record_type)
			&work_lib..accom_type_codes (keep = accom_type_code)
			&work_lib..mkt_seg_codes (keep = mkt_seg_code)
			&work_lib..failure (keep = error_cd error_message error_params request_id err_ct);
			retain snapshot_date prepared_date_temp snapshot_date_temp past_window_size future_window_size;
			length error_cd 8 error_params error_message request_id $200;
			length columnName $20;
			length accom_type_id 4;
			length mkt_seg_id 4;
			length occupancy_dt 8;
			length market_segment $50;
			attrib record_type format=$20.;
			attrib accom_type_cd format=$50.;
			retain snapshot_date prepared_date_temp snapshot_date_temp past_window_size future_window_size;
			format prepared_date date9.;
			format snapshot_date date9.;
			format occupancy_dt date9.;
			format start_dt date9.;
			format end_dt date9.;
			format booking_dt date9.;
			format cancel_dt date9.;
			length Group_Code $21;
			infile "&etl_file_name" delimiter='|' dsd missover end=last;

			if _n_ = 1 then
				do;
					retain err_ct  &err_ct.;
				end;

			/* The metadata is always on the second record */
			if _n_=2 then
				do;
					declare hash accom(dataset: "&work_lib..accom_type");
					accom.defineKey('accom_type_code');
					accom.defineData('accom_type_id');
					accom.defineDone();
					declare hash mkt(dataset: "&work_lib..mkt_seg");
					mkt.defineKey('mkt_seg_code');
					mkt.defineData('mkt_seg_id');
					mkt.defineDone();

					/* below hashes to validate uniqueness of group_code for group master table  */
					declare hash gc_val( dataset: "&work_lib..gc_val");
					gc_val.defineKey('group_code');
					gc_val.defineDone();

					/* below hashes to validate uniqueness of group_block table  */
					declare hash gb_val( dataset: "&work_lib..gb_val");
					gb_val.defineKey('occupancy_dt','accom_type_cd','group_code');
					gb_val.defineDone();

					/* below hashes to validate uniqueness of accom_type_code and occupancy_dt for ct and pt records  */
					declare hash ct_val( dataset: "&work_lib..ct_val");
					ct_val.defineKey('occupancy_dt','accom_type_cd');
					ct_val.defineDone();

					/* below hashes to validate uniqueness of accom_type_code and occupancy_dt for cst and pst records */
					declare hash cst_val( dataset: "&work_lib..cst_val");
					cst_val.defineKey('occupancy_dt','accom_type_cd','market_segment');
					cst_val.defineDone();
					declare hash ctdate( dataset: "&work_lib..ct_date");
					ctdate.defineKey('occupancy_dt');
					ctdate.defineData('occupancy_dt');
					ctdate.defineDone();
					declare hash ptdate( dataset: "&work_lib..pt_date");
					ptdate.defineKey('occupancy_dt');
					ptdate.defineData('occupancy_dt');
					ptdate.defineDone();
					input record_type $ 
						client_code $
						property_id $
						past_window_size 
						future_window_size 
						snapshot_date: yymmdd10. 
						snapshot_time $ 
						prepared_date: yymmdd10. 
						prepared_time $;

					if sum(missing(record_type),missing(client_code),missing(property_id),
						missing(past_window_size),missing(future_window_size),
						missing(snapshot_date),missing(snapshot_time),
						missing(prepared_date),missing(prepared_time) ) > 0 then
						do;
							error_cd = 421;
							error_message = 'Missing universal fields on metadata record'||'   Extract Row: '|| left(_n_);
							error_params =  "&property_desc.";
							request_id = "&request_id";
							err_ct + 1;
							output &work_lib..failure;
							call symputx('etl_status',1,'l');
						end;
					else
						do;
							tmp_dt=put(snapshot_date,date9.)||':'||trim(left(snapshot_time));
							snap_dttm=input(tmp_dt,datetime20.);
							tmp_dt=put(prepared_date,date9.)||':'||trim(left(prepared_time));
							prepared_dttm=input(tmp_dt,datetime20.);

							/*read the snapshot/prepared_dttm into variables;*/
							call symput('snapshot_date',snap_dttm);
							call symput('prepared_date',prepared_dttm);
							call symput('snap_date', compress(snapshot_date));
							call symput('snap_time', compress(snapshot_time));
							call symput('prep_date', compress(prepared_date));
							call symput('prep_time', compress(prepared_time));
							call symput('snap_dttm', compress(snap_dttm));
							call symput('prepared_dttm', compress(prepared_dttm));
							call symput ('past_window_size', compress (past_window_size));
							call symput ('future_window_size', compress (future_window_size));
							call symput ('property_cd', property_id);
							call symput('prep_dttm',prepared_dttm);
							output &Work_lib..file_metadata;
						end;
				end;
			else
				do;
					/* This is not a metadata record */
					/* Read in the "universal" fields first */
					input record_type $ @;

					/* Verify that multiple medatadat records are not in the file */
					if record_type='_T2SNAP-META_' then
						do;
							error_cd = 929;
							error_message = 'Multiple metadata (_T2SNAP-META_) records in file'||'   Extract Row: '|| left(_n_);
							error_params = "&property_desc.";
							request_id = "&request_id";
							err_ct + 1;
							output &work_lib..failure;
							call symputx('etl_status',1,'l');
						end;

					/* The record type determines the remaining field names.  Read accordingly.*/
					if record_type='_CT_' then
						do;
							record_type_id=&ct;
							input occupancy_dt : yymmdd10. total_accom_capacity rooms_not_avail_maint rooms_not_avail_other rooms_sold arrivals departures 
								room_revenue food_revenue total_revenue;
							occupancy_dt_temp=put(occupancy_dt,worddate12.);

							if sum( missing(record_type),missing(occupancy_dt), missing(total_accom_capacity),missing(rooms_not_avail_maint),
								missing(rooms_not_avail_other),
								missing(rooms_sold),missing(arrivals),missing(departures),missing(room_revenue),
								missing(total_revenue) ) > 0 then
								do;
									if record_type=. then
										columnName='record_type';
									else if occupancy_dt=.          then
										columnName='occupancy_dt';
									else if total_accom_capacity=.   then
										columnName='total_accom_capacity';
									else if rooms_not_avail_maint=.  then
										columnName='rooms_not_avail_maint';
									else if rooms_not_avail_other=. then
										columnName='rooms_not_avail_other';
									else if rooms_sold=.            then
										columnName='rooms_sold';
									else if arrivals=.              then
										columnName='arrivals';
									else if departures=.            then
										columnName='departures';
									else if room_revenue=.          then
										columnName='room_revenue';
									else if total_revenue=.         then
										columnName='total_revenue';
									error_message = 'CT data has missing required values for field: '||columnName||'   Extract Row: '|| left(_n_);
									error_cd = 422;
									error_params =  "&property_desc.";
									request_id = "&request_id";
									err_ct + 1;
									output &work_lib..failure;
									call symputx('etl_status',1,'l');
								end;
							else
								do;
									if (rooms_not_avail_maint + rooms_not_avail_other) > total_accom_capacity
										or rooms_not_avail_maint > total_accom_capacity 
										or rooms_not_avail_other > total_accom_capacity then
										do;
											Put " ";
										end;
								end;

							output &work_lib..total;

							if ctdate.find() ne 0 then
								do;
									ctdate.add();
								end;
							else
								do;
									error_cd = 432;
									error_message = 'CT data has duplicate entries for Occupancy Date: ' || occupancy_dt_temp||'   Extract Row: '||left(_n_);
									error_params =  "&property_desc.";
									request_id = "&request_id";
									err_ct + 1;
									output &work_lib..failure;
									call symputx('etl_status',1,'l');
								end;
						end;  /*** end CT checks ***/
					else if record_type='_PT_' then
						do;
							record_type_id=&pt;
							input occupancy_dt : yymmdd10. total_accom_capacity rooms_not_avail_maint rooms_not_avail_other rooms_sold arrivals departures 
								room_revenue food_revenue total_revenue cancellations no_shows;
							occupancy_dt_temp=put(occupancy_dt,worddate12.);

							if sum( missing(record_type),missing(occupancy_dt), missing(total_accom_capacity),missing(rooms_not_avail_maint),
								missing(rooms_not_avail_other),
								missing(rooms_sold),missing(arrivals),missing(departures),missing(room_revenue),
								missing(total_revenue),missing(cancellations),missing(no_shows)) > 0 then
								do;
									if record_type=. then
										columnName='record_type';
									else if occupancy_dt=.          then
										columnName='occupancy_dt';
									else if total_accom_capacity=.   then
										columnName='total_accom_capacity';
									else if rooms_not_avail_maint=.  then
										columnName='rooms_not_avail_maint';
									else if rooms_not_avail_other=. then
										columnName='rooms_not_avail_other';
									else if rooms_sold=.            then
										columnName='rooms_sold';
									else if arrivals=.              then
										columnName='arrivals';
									else if departures=.            then
										columnName='departures';
									else if room_revenue=.          then
										columnName='room_revenue';
									else if total_revenue=.         then
										columnName='total_revenue';
									else if cancellations=.         then
										columnName='cancellations';
									else if no_shows=.              then
										columnName='no_shows';
									error_message = 'PT data has missing required values for field: '||columnName||'   Extract Row: '|| _n_;
									error_cd = 422;
									error_params =  "&property_desc.";
									request_id = "&request_id";
									err_ct + 1;
									output &work_lib..failure;
									call symputx('etl_status',1,'l');
								end;
							else
								do;
									if (rooms_not_avail_maint + rooms_not_avail_other) > total_accom_capacity
										or rooms_not_avail_maint > total_accom_capacity 
										or rooms_not_avail_other > total_accom_capacity then
										do;
											put " ";
										end;

									output &work_lib..total;

									if ptdate.find() ne 0 then
										do;
											ptdate.add();
										end;
									else
										do;
											error_cd = 432;
											error_message = 'PT data has duplicate entries for Occupancy Date: ' || occupancy_dt_temp||'   Extract Row: '|| left(_n_);
											error_params =  "&property_desc.";
											request_id = "&request_id";
											err_ct + 1;
											output &work_lib..failure;
											call symputx('etl_status',1,'l');
										end;
								end;
						end;  /*** end PT checks ***/
					else if record_type='_CTAT_' then
						do;
						    length accom_type_cd$21;
							record_type_id=&ctat;
							input occupancy_dt : yymmdd10. accom_type_cd $ accom_capacity rooms_not_avail_maint rooms_not_avail_other rooms_sold arrivals departures 
								room_revenue food_revenue total_revenue;
							occupancy_dt_temp=put(occupancy_dt,worddate12.);

							if sum(missing(record_type),missing(occupancy_dt),missing(accom_type_cd), 
								missing(accom_capacity),
								missing(rooms_not_avail_maint),missing(rooms_not_avail_other),
								missing(rooms_sold),missing(arrivals),missing(departures),missing(room_revenue),
								missing(total_revenue)) > 0 then
								do;
									if rec_type=. then
										columnName='rec_type';
									else if occupancy_dt=.          then
										columnName='occupancy_dt';
									else if accom_type_cd=.         then
										columnName='accom_type_cd';
									else if accom_capacity=.        then
										columnName='accom_capacity';
									else if rooms_not_avail_maint=. then
										columnName='rooms_not_avail_maint';
									else if rooms_not_avail_other=. then
										columnName='rooms_not_avail_other';
									else if rooms_sold=.            then
										columnName='rooms_sold';
									else if arrivals=.              then
										columnName='arrivals';
									else if departures=.            then
										columnName='departures';
									else if room_revenue=.          then
										columnName='room_revenue';
									else if total_revenue=.         then
										columnName='total_revenue';
									error_cd = 422;
									error_message = 'CTAT data has missing required values for field: '||columnName;
									error_params =  "&property_desc.";
									request_id = "&request_id";
									err_ct + 1;
									output &work_lib..failure;
									call symputx('etl_status',1,'l');
								end;
							else
								do;
									if (rooms_not_avail_maint + rooms_not_avail_other) > accom_capacity 
										or rooms_not_avail_maint > accom_capacity 
										or rooms_not_avail_other > accom_capacity then
										do;
											Put " ";
										end;

									accom_type_code = accom_type_cd;
									output &work_lib..accom;

									if ct_val.find() ne 0 then
										do;
											ct_val.add();
										end;
									else
										do;
											error_cd = 432;
											error_message = 'CTAT has duplicate entries for Occupancy Date: ' || occupancy_dt_temp || ' Accom Type: '|| trim(accom_type_cd)||'   Extract Row: '||left(_n_);
											error_params =  "&property_desc.";
											request_id = "&request_id";
											err_ct + 1;
											output &work_lib..failure;
											call symputx('etl_status',1,'l');
										end;

									if accom.find() ne 0 then
										do;
											output &work_lib..accom_type_codes;
										end;
								end;
						end;  /*** end CTAT checks ***/
					else if record_type='_CSAT_' then
						do;
						    length accom_type_cd$21;
							record_type_id=&csat;
							input occupancy_dt : yymmdd10. accom_type_cd $ market_segment $ rooms_sold arrivals departures room_revenue food_revenue total_revenue;
							occupancy_dt_temp=put(occupancy_dt,worddate12.);

							if sum( missing(record_type),missing(occupancy_dt),missing(accom_type_cd), missing(market_segment),
								missing(rooms_sold),missing(arrivals),missing(departures),
								missing(room_revenue),missing(total_revenue)) > 0 then
								do;
									if rec_type=. then
										columnName='rec_type';
									else if occupancy_dt=.          then
										columnName='occupancy_dt';
									else if accom_type_cd=.         then
										columnName='accom_type_cd';
									else if market_segment=.        then
										columnName='market_segment';
									else if rooms_sold=.            then
										columnName='rooms_sold';
									else if arrivals=.              then
										columnName='arrivals';
									else if departures=.            then
										columnName='departures';
									else if room_revenue=.          then
										columnName='room_revenue';
									else if total_revenue=.         then
										columnName='total_revenue';
									error_cd = 422;
									error_message = 'CSAT data has missing required values for field: '||columnName||'   Extract Row: '||left(_n_);
									error_params =  "&property_desc.";
									request_id = "&request_id";
									err_ct + 1;
									output &work_lib..failure;
									call symputx('etl_status',1,'l');
								end;
							else
								do;
									mkt_seg_code = market_segment;
									accom_type_code = accom_type_cd;
									output &work_lib..mkt_accom;

									if accom.find() ne 0 then
										do;
											output &work_lib..accom_type_codes;
										end;

									if cst_val.find() ne 0 then
										do;
											cst_val.add();
										end;
									else
										do;
											error_cd = 434;
											error_message = 'CSAT data has duplicate entries for Occupancy Date: ' ||occupancy_dt_temp || ' Accom Type: '|| trim(accom_type_cd)|| ' Market Segment: '|| trim(market_segment)||'   Extract Row: '||left(_n_);
											error_params =  "&property_desc.";
											request_id = "&request_id";
											err_ct + 1;
											output &work_lib..failure;
											call symputx('etl_status',1,'l');
										end;

									if mkt.find() ne 0 then
										do;
											output &work_lib..mkt_seg_codes;
										end;
								end;
						end; /*** end CSAT checks ***/
					else if record_type='_PTAT_' then
						do;
						    length accom_type_cd$21;
							record_type_id=&ptat;
							input occupancy_dt : yymmdd10. accom_type_cd $ accom_capacity rooms_not_avail_maint rooms_not_avail_other rooms_sold arrivals departures
								room_revenue food_revenue total_revenue cancellations no_shows;
							occupancy_dt_temp=put(occupancy_dt,worddate12.);

							if sum( missing(record_type),missing(occupancy_dt),missing(accom_type_cd), missing(accom_capacity),
								missing(rooms_not_avail_maint),missing(rooms_not_avail_other),
								missing(rooms_sold),missing(arrivals),missing(departures),missing(room_revenue),
								missing(total_revenue),missing(cancellations),missing(no_shows)) > 0 then
								do;
									if rec_type=. then
										columnName='rec_type';
									else if occupancy_dt=.          then
										columnName='occupancy_dt';
									else if accom_type_cd=.         then
										columnName='accom_type_cd';
									else if accom_capacity=.        then
										columnName='accom_capacity';
									else if rooms_not_avail_maint=. then
										columnName='rooms_not_avail_maint';
									else if rooms_not_avail_other=. then
										columnName='rooms_not_avail_other';
									else if rooms_sold=.            then
										columnName='rooms_sold';
									else if arrivals=.              then
										columnName='arrivals';
									else if departures=.            then
										columnName='departures';
									else if room_revenue=.          then
										columnName='room_revenue';
									else if total_revenue=.         then
										columnName='total_revenue';
									else if cancellations=.         then
										columnName='cancellations';
									else if no_shows=.              then
										columnName='no_shows';
									error_cd = 422;
									error_message = 'PTAT data has missing required values for field: '||columnName||'   Extract Row: '||left(_n_);
									error_params =  "&property_desc.";
									request_id = "&request_id";
									err_ct + 1;
									output &work_lib..failure;
									call symputx('etl_status',1,'l');
								end;
							else
								do;
									if (rooms_not_avail_maint + rooms_not_avail_other) > accom_capacity 
										or rooms_not_avail_maint > accom_capacity 
										or rooms_not_avail_other > accom_capacity then
										do;
											put " ";
										end;

									accom_type_code = accom_type_cd;
									output &work_lib..accom;

									if ct_val.find() ne 0 then
										do;
											ct_val.add();
										end;
									else
										do;
											error_cd = 432;
											error_message = 'PTAT data has duplicate entries for Occupancy Date: ' || occupancy_dt_temp || ' Accom Type: '|| trim(accom_type_cd)||'   Extract Row: '||left(_n_);
											error_params =  "&property_desc.";
											request_id = "&request_id";
											err_ct + 1;
											output &work_lib..failure;
											call symputx('etl_status',1,'l');
										end;

									if accom.find() ne 0 then
										do;
											output &work_lib..accom_type_codes;
										end;
								end;
						end; /*** end PTAT checks ***/
					else if record_type='_PSAT_' then
						do;
						    length accom_type_cd$21;
							record_type_id=&psat;
							input occupancy_dt : yymmdd10. accom_type_cd $ market_segment $ rooms_sold arrivals departures room_revenue food_revenue total_revenue cancellations no_shows;
							occupancy_dt_temp=put(occupancy_dt,worddate12.);

							if sum( missing(record_type),missing(occupancy_dt),missing(accom_type_cd), missing(market_segment),
								missing(rooms_sold),missing(arrivals),missing(departures),missing(room_revenue),
								missing(total_revenue),missing(cancellations),missing(no_shows)) > 0 then
								do;
									if rec_type=. then
										columnName='rec_type';
									else if occupancy_dt=.          then
										columnName='occupancy_dt';
									else if accom_type_cd=.         then
										columnName='accom_type_cd';
									else if market_segment=.        then
										columnName='market_segment';
									else if rooms_sold=.            then
										columnName='rooms_sold';
									else if arrivals=.              then
										columnName='arrivals';
									else if departures=.            then
										columnName='departures';
									else if room_revenue=.          then
										columnName='room_revenue';
									else if total_revenue=.         then
										columnName='total_revenue';
									else if cancellations=.         then
										columnName='cancellations';
									else if no_shows=.              then
										columnName='no_shows';
									error_cd = 422;
									error_message = 'PSAT data has missing required values for field: '||columnName||'   Extract Row: '|| left(_n_);
									error_params =  "&property_desc.";
									request_id = "&request_id";
									err_ct + 1;
									output &work_lib..failure;
									call symputx('etl_status',1,'l');
								end;
							else
								do;
									accom_type_code = accom_type_cd;
									mkt_seg_code = market_segment;
									output &work_lib..mkt_accom;

									if accom.find() ne 0 then
										do;
											output &work_lib..accom_type_codes;
										end;

									if cst_val.find() ne 0 then
										do;
											cst_val.add();
										end;
									else
										do;
											error_cd = 434;
											error_message = 'PSAT data has duplicate entries for Occupancy Date: ' || occupancy_dt_temp || ' Accom Type: '|| trim(accom_type_cd)|| ' Market Segment: '|| trim(market_segment) ||'   Extract Row: '|| left(_n_);
											error_params =  "&property_desc.";
											request_id = "&request_id";
											err_ct + 1;
											output &work_lib..failure;
											call symputx('etl_status',1,'l');
										end;

									if mkt.find() ne 0 then
										do;
											output &work_lib..mkt_seg_codes;
										end;
								end;
						end;  /*** end PSAT checks ***/
					else if record_type='_GM_' then
						do;
						    length Group_Code$21;
							record_type_id=&gm;
							input Group_Code $ Group_Name $ Group_Description $ Master_Group_Code $ Group_Status_Code $ market_segment $
								start_dt: yymmdd10. end_date: yymmdd10. group_type_code $ booking_dt: yymmdd10. pickup_type_code $
								cancel_dt: yymmdd10. booking_type $ sales_person $ cut_off_date: yymmdd10. cut_off_days;
							mkt_seg_code = market_segment;
							Property_ID = &Property_ID;
							output &work_lib..gm;

							if gc_val.find() ne 0 then
								do;
									gc_val.add();
								end;
							else
								do;
									error_cd = 449;
									error_message = 'Group Master data has duplicate for Group Master Code: '||trim(master_group_code) ||'   Extract Row: '|| left(_n_);
									error_params =  "&property_desc.";
									request_id = "&request_id";
									err_ct + 1;
									output &work_lib..failure;
									call symputx('etl_status',1,'l');
								end;

							if mkt.find() ne 0 then
								do;
									output &work_lib..mkt_seg_codes;
								end;
						end;  /*** end GM checks ***/
					else if record_type='_GB_' then
						do;
						    length Group_Code$21 accom_type_cd$21;
							record_type_id=&gb;
							input Group_Code $ occupancy_dt: yymmdd10. accom_type_cd $ blocks pickup original_blocks rate;
							accom_type_code = accom_type_cd;
							output &work_lib..gb;
							occupancy_dt_temp=put(occupancy_dt,worddate12.);

							if gb_val.find() ne 0 then
								do;
									gb_val.add();
								end;
							else
								do;
									error_cd = 450;
									error_message = 'Group Block data has duplicate for occupancy date: '||occupancy_dt_temp|| ' accom type: '|| trim(accom_type_cd)||' Group Code: '||trim(group_code)||'   Extract Row: '||left(_n_);
									error_params =  "&property_desc.";
									request_id = "&request_id";
									err_ct + 1;
									output &work_lib..failure;
									call symputx('etl_status',1,'l');
								end;

							if accom.find() ne 0 then
								do;
									output &work_lib..accom_type_codes;
								end;
						end;  /*** end GB checks ***/
					else if record_type='_FOOTER_' then
						do;
							rc = ctdate.output(dataset: "&work_lib..ct_date");
							rc = ptdate.output(dataset: "&work_lib..pt_date");
						end;

					/* Record types can be header or footer.  Ignore those, and anything else not processed is a bad record */
					else if record_type not in ('_HEADER_') then
						output &work_lib..badrecs;
				end;

			if err_ct ge &maxerrors. and &maxerrors. NE -1 then
				do;
					stop;
				end;
		run;

		proc append base= &work_lib..errors data= &work_lib..failure;
		run;

		%if &syscc > 4 %then
			%do;
				%ideas_util_inc_errors;
				%ideas_util_report_errors(903, 'Error importing ETL file',  "&property_desc.", &request_Id., &err_ct.);
				%let etl_status = 1;
			%end;

		/************************************************************************/
		/*                                                                      */
		/* Summary Validation Section for type two population                   */
		/*                                                                      */
		/************************************************************************/
		/* ROH/capacity check  */
		proc sql;
			create table &work_lib..accom_roh_errors as
				select a.*, b.roh_type  from  &work_lib..accom  as a inner join &work_lib..accom_type  as b 
					on a.accom_type_cd=b.accom_type_code
				where a.accom_capacity=0 and b.roh_type=1 and a.occupancy_dt >= &snap_date 
					order by a.accom_type_cd, a.occupancy_dt;
		quit;

		%if %ideas_util_nobs(&work_lib..accom_roh_errors) > 0 %then
			%do;

				data &work_lib..failure (keep = error_cd error_message error_params request_id err_ct);
					length error_cd 8 error_params error_message request_id $200;
					length occ_dt $12.;
					retain err_ct  0;
					set &work_lib..accom_roh_errors;

					if _n_=1 then
						err_ct = SYMGET("err_ct");
					occ_dt=put(occupancy_dt,worddate12.);
					error_cd = 936;
					error_message = 'Zero Capacity for ROH Accom Type: '||trim(accom_type_cd)||' for Occupancy Date: '||occ_dt;
					error_params =  "&property_desc.";
					request_id = "&request_id";
					err_ct + 1;
					output &work_lib..failure;
					error_string='VALIDATION ERROR: Zero Capacity for ROH Accom Type: '||trim(accom_type_cd)||' for Occupancy Date: '||occ_dt;
					put error_string;

				proc append base= &work_lib..errors data= &work_lib..failure;
				run;

				%ideas_util_inc_errors;
				%let etl_status = 1;
			%end;

		/* master class/accomidation type capacity validation  */
		proc sql;
			create table &work_lib..accom_master_class_future as
				select a.*, b.accom_type_id, c.accom_class_id, c.accom_class_code, c.master_class from &work_lib..accom  as a 
					inner join &work_lib..accom_type as b 
						on a.accom_type_cd=b.accom_type_code
					inner join &work_lib..accom_class as c
						on b.accom_class_id=c.accom_class_id
					where c.master_class=1 and a.occupancy_dt >= &snap_date  
						order by c.accom_class_id, a.occupancy_dt, accom_type_cd;
		quit;

		data  &work_lib..master_class_capacity_errors (keep=save_capacity accom_class_id accom_class_code accom_type_id accom_type_cd occupancy_dt );
			retain save_capacity;
			format occupancy_dt date9.;
			set &work_lib..accom_master_class_future;
			by accom_class_id occupancy_dt;

			if first.accom_class_id or first.occupancy_dt then
				do;
					save_capacity=0;
				end;

			save_capacity=save_capacity+accom_capacity;

			if (last.accom_class_id or last.occupancy_dt)  and save_capacity=0 then
				do;
					output &work_lib..master_class_capacity_errors;
				end;
		run;

		%if %ideas_util_nobs(&work_lib..master_class_capacity_errors) > 0 %then
			%do;

				data &work_lib..failure (keep = error_cd error_message error_params request_id err_ct);
					length error_cd 8 error_params error_message request_id $200;
					length occ_dt $12.;
					retain err_ct  0;
					set &work_lib..master_class_capacity_errors;

					if _n_=1 then
						err_ct = SYMGET("err_ct");
					occ_dt=put(occupancy_dt,worddate12.);
					error_cd = 937;
					error_message = 'Zero Capacity on all Accom Types in Master Class: '||trim(accom_class_code)||' for Occupancy Date: '|| occ_dt;
					error_params =  "&property_desc.";
					request_id = "&request_id";
					output &work_lib..failure;
					err_ct + 1;
					error_string='Zero Capacity on all Accom Types in Master Class: '||trim(accom_class_code)||' for Occupancy Date: '|| occ_dt;
					put error_string;

				proc append base= &work_lib..errors data= &work_lib..failure;
				run;

				%let etl_status = 1;

				%ideas_util_inc_errors;
			%end;

		data &work_lib..fdates;
			set &Work_lib..file_metadata 
				(keep=snapshot_date snapshot_time prepared_date prepared_time);
			snap_tm = input(snapshot_time,time10.);
			prep_tm = input(prepared_time,time10.);
			snap_dttm = input(put(snapshot_date,date9.)||':'||trim(left(snapshot_time)),datetime20.);
			prep_dttm = input(put(prepared_date,date9.)||':'||trim(left(prepared_time)),datetime20.);
		run;

		data  &work_lib..dates;
			set &work_lib..dates;
			set &work_lib..fdates;
		run;

		data &work_lib..failure (keep = error_cd error_message error_params request_id err_ct);
            length error_cd 8 error_params error_message request_id $200;
            set &work_lib..dates;
            format snapshot_date date9.;
            extract_snap_dttm=put(snap_dttm,DATETIME18.);
            extract_prep_dttm=put(prep_dttm,DATETIME18.);
            system_ssnap_dttm=put(ssnap_dttm,DATETIME18.);
            system_sprep_dttm=put(sprep_dttm,DATETIME18.);

            if snap_dttm <  ssnap_dttm then
                do;
                    error_cd = 412;
                    error_message = 'Extract Snapshot date/time: '||extract_snap_dttm||'  is less than system Snapshot date/time: '||system_ssnap_dttm;
                    request_id = "&request_id";
                    error_params =  "&property_desc.";
                    err_ct + 1;
                    output &work_lib..failure;
                    call symputx('etl_status',1,'l');
                end;
        run;

        proc append base= &work_lib..errors data= &work_lib..failure;
        run;

		%if %ideas_util_nobs(&work_lib..file_metadata) > 0 %then
			%do;
				%if &pcode. ne &property_cd. %then
					%do;
						%ideas_util_inc_errors;
						%ideas_util_report_errors(409, "%str(Extract file Property Code: %trim(&pcode.) does not match %trim(&tenant_db.) property code: %trim(&property_cd.))",  "&property_desc.", &request_Id., &err_ct.);
						%let etl_status = 1;
					%end;

				%if &past_window_size. < 0  %then
					%do;
						%ideas_util_inc_errors;
						%ideas_util_report_errors(410, "%str(Past window size: %trim(&past_window_size.) required to be greater or equal 0 )", "&property_desc.", &request_Id., &err_ct.);
						%let etl_status = 1;
					%end;

				%if &future_window_size. < 0 or &future_window_size. > 9999 %then
					%do;
						%ideas_util_inc_errors;
						%ideas_util_report_errors(411, "%str(Future window size: %trim(&future_window_size.) required to be greater or equal 0 and less than 10000)", "&property_desc.", &request_Id., &err_ct.);
						%let etl_status = 1;
					%end;
			%end;

		proc sql;
			select count(*) into: ct_ct from &work_lib..total where record_type_id = 1;
		quit;

		%if &ct_ct. ne 0 %then
			%do;
				%occ_snap_dt_check (total, CT, 1);
			%end;

		proc sql;
			select count(*) into: ct_ctat from &work_lib..accom where record_type_id = 4;
		quit;

		%if &ct_ctat. ne 0 %then
			%do;
				%occ_snap_dt_check_bound (accom, CTAT, 4);
			%end;

		proc sql;
			select count(*) into: ct_csat from &work_lib..mkt_accom where record_type_id = 5;
		quit;

		%if &ct_csat. ne 0 %then
			%do;
				%occ_snap_dt_check_bound (mkt_accom, CSAT, 5);
			%end;

		proc sql;
			select count(*) into: ct_pt from &work_lib..total where record_type_id = 8;
		quit;

		%if &ct_pt. ne 0 %then
			%do;
				%occ_snap_dt_check_past (total, PT, 8);
			%end;

		proc sql;
			select count(*) into: ct_ptat from &work_lib..accom where record_type_id = 6;
		quit;

		%if &ct_ptat. ne 0 %then
			%do;
				%occ_snap_dt_check_past (accom, PTAT, 6);
			%end;

		proc sql;
			select count(*) into: ct_psat from &work_lib..mkt_accom where record_type_id = 7;
		quit;

		%if &ct_psat. ne 0 %then
			%do;
				%occ_snap_dt_check_past (mkt_accom, PSAT, 7);
			%end;

%EXIT:
		%let outname = .;

		%if "&GL_ABORT_FLAG" eq "1" or &syscc > 4 or &etl_status = 1 %then
			%do;
				%let GL_ABORT_FLAG =1;
				%let syscc = 5;
				%let etl_status = 1;
			%end;


		%let  prepared_dttm_temp=%sysfunc(putn(&prep_dttm,datetime20.));
		%let  snapshot_dttm_temp=%sysfunc(putn(&snap_dttm,datetime20.));

		%if &etl_status. = 1 %then
			%do;

				data &work_lib..errors;
					set &work_lib..errors;
					where error_cd ne 0;
				run;

				%if &maxerrors. ne -1 %then
					%do;
						%ideas_util_inc_errors;

						%if &err_ct. -1 = &maxerrors. %then
							%do;
								%ideas_util_report_errors(1000, "Max errors of &maxerrors. has been reached",  "&property_desc.", &request_Id., &err_ct.);
							%end;
					%end;

				data _null_;
					call scan("&etl_file_name",-1,fn_pos,fn_length,'\');
					fname = substrn("&etl_file_name",fn_pos,fn_length-4);
					call symputx('fname',fname,'l');
				run;

				%let outname = %str(&mylog_path.\validation_errorlog_&fname..txt);
				%let file_references = &file_references.|&outname;
				%put File References: &file_references;

				data _null_;
					set &work_lib..errors;
					where error_cd ne 0;
					FILE  "&outname.";

					If _N_ = 1 then
						do;
							Put '-------------------------------------------- Type Two Validation Summary -----------------------------------------------';
							put ' ';
							Put "      Property Info: &property_desc.";
							put "Snap Shot Date/Time: &snapshot_dttm_temp.";
							put " Prepared Date/Time: &prepared_dttm_temp.";
							put "   Past Window Size: &past_window_size.";
							put " Future Window Size: &future_window_size.";
							put "Validation ETL File: &etl_file_name.";
							put "         Request ID: &requestId.";
							put ' ';
							Put '------------------------------------------- Type Two Validation Errors ------------------------------------------------';
							put ' ';
							Put 'Error Code -------------------------------- Error Description ---------------------------------------------------------';
							put ' ';
						end;

					PUT  @4  error_cd @ 12 error_message;
				run;

			%end;
		/* if any error then build xml response else population will generate required response */
        %if "&GL_ABORT_FLAG" eq "1" or &syscc > 4 or &etl_status = 1 %then
        %do;
            data _NULL_;
                FILE resXml;
                put '<?xml version="1.0" encoding="UTF-8"?>';
                put '<SASResponse xmlns="http://xml.common.pacman.tetris.ideas.com/schema/population/response/v1">';
                put "<ResponseHeader> <PropertyId>&property_Id</PropertyId>" @;
                put "<operationName>TypeTwoValidation-&operationName.</operationName>" @;
                put "<requestId>&request_Id</requestId>";
                stop;
            run;

            data _NULL_;
                FILE resXml MOD;
                set &work_lib..errors;

                if _N_ = 1 then
                    put "<ErrorMessage>" "&outname." "</ErrorMessage>";
            run;

            data _NULL_;
                FILE resXml MOD;
                put "</ResponseHeader></SASResponse>";
            run;
        %end;

		%let app_end_time=%sysfunc(datetime());
		%let runTime=%sysfunc(round(&app_end_time-&app_start_time, .05));

		%if "&GL_ABORT_FLAG" eq "1" or &syscc > 4 or &etl_status = 1 %then
			%let Status_desc=Failed;
		%else
			%do;
				%let Status_desc=Successful;

				%if %upcase(&Sas_Debug.)=FALSE %then
					%do;

						proc printto log="&logfile" new;
						run;

					%end;
			%end;

		Options nomacrogen NoSymbolgen nomlogic nomprint nomfile;
		%PUT ---------------------------------------------------------------------;
		%put -----------------      Type 2 Validation Summary      ---------------;
		%put ---------------------------------------------------------------------;
		%put;
		%Put -         Property Info: &property_desc.;
		%put -     Validation Status: &status_desc.;
		%Put -   Validation Run time: &runTime. seconds;
		%put -   Snap Shot Date/Time: &snapshot_dttm_temp.;
		%put -    Prepared Date/Time: &prepared_dttm_temp.;
		%put -      Past Window Size: &past_window_size.;
		%put -    Future Window Size: &future_window_size.;
		%put;
		%put -   Validation ETL File: &etl_file_name.;
		%put -         Sas Data Path: &saspath.;
		%put -            Request ID: &requestId.;
		%put -      ork Library Path: &work_path.;
		%put -     Work Library Name: &work_lib;
		%put -         Memlib Option: &memlib_option;
		%put;
		%put -       Database Server: &tenant_server.;
		%put -       Server Instance: &server_instance.;
		%put -         Database Name: &tenant_db.;
		%put -         Database User: &tenant_user.;
		%put -         Database Port: &port.;
		%put;

		data _null_;
			set &work_lib..performance;

			if _n_=1 then
				do;
					put '*********************************************************************';
					put '*************** Type 2 Validation Macro Summary *********************';
					put '*********************************************************************';
					put ' ';
					put  @1 '    Macro Name                        Status            Run Time(seconds)';
				end;

			put  @ 5  macro_name    @ 40  macro_status  @ 60    macro_run_time_seconds;
		run;

		%PUT ---------------------------------------------------------------------;
		%PUT ------------------ Ending Type 2 Validation -------------------------;
		%PUT ---------------------------------------------------------------------;
		%put;

		%if &work_lib. ne work %then
			%do;

				proc Datasets library = &work_lib. kill memtype=data;
				quit;

			%end;
%mend ideas_type2_validation;

%macro occ_snap_dt_check (ds, type, rt)/store;
	%let macroname=&SYSMACRONAME;

	%ideas_macro_log (start_end=START, macroname=&macroname.);

	%if %ideas_util_nobs(&work_lib..&ds.) > 0 %then
		%do;
			%if %ideas_util_nobs(&work_lib..file_metadata) > 0 %then
				%do;

					proc sql;
						select snapshot_date format = worddate12. into: sdt  from &Work_lib..file_metadata;
					quit;

					data &work_lib..mdt;
						set &work_lib..file_metadata;
						maxdt = snapshot_date +future_window_size -1;
					run;

					proc sql;
						select maxdt format = worddate12. into: msdt  from &work_lib..mdt;
					quit;

				%end;

			proc sql;
				select min(occupancy_dt) format=worddate12. into: ctMinDt from  &work_lib..&ds. where record_type_id = &rt.;
				select max(occupancy_dt) format=worddate12. into: ctMaxDt from  &work_lib..&ds. where record_type_id = &rt.;
			quit;

			%if %symexist(sdt) and %symexist(ctMinDt) and %symexist(ctMaxDt) %then
				%do;
					%if &sdt. ne &ctMinDt. %then
						%do;
							%ideas_util_inc_errors;
							%ideas_util_report_errors(437, "%str(Earliest &type. occupancy date: &ctMinDt. is not equal extract snapshot date: &sdt)", "&property_desc.", &request_Id., &err_ct.);
							%let etl_status = 1;
						%end;

					%if &msdt. ne &ctMaxDt. %then
						%do;
							%ideas_util_inc_errors;
							%ideas_util_report_errors(437, "%str(Latest &type. occupancy date: &ctMaxDt. is not equal extract snapshot plus future window size date: &msdt)", "&property_desc.", &request_Id., &err_ct.);
							%let etl_status = 1;
						%end;
				%end;
			%else
				%do;
					%ideas_util_inc_errors;
					%ideas_util_report_errors(436, 'Missing required variables to complete occupancy date checks',  "&property_desc.", &request_Id., &err_ct.);
					%let etl_status = 1;
				%end;
		%end;

	%let macroname=&SYSMACRONAME;

	%ideas_macro_log (start_end=END, macroname=&macroname.);
%mend occ_snap_dt_check;

%macro occ_snap_dt_check_past (ds, type, rt) / store;
	%let macroname=&SYSMACRONAME;

	%ideas_macro_log (start_end=START, macroname=&macroname.);

	%if %ideas_util_nobs(&work_lib..&ds.) > 0 %then
		%do;
			%if %ideas_util_nobs(&work_lib..file_metadata) > 0 %then
				%do;

					proc sql;
						select snapshot_date format = worddate12. into: sdt  from &Work_lib..file_metadata;
					quit;

					data &work_lib..mdt;
						set &work_lib..file_metadata;
						mindt = snapshot_date - 1;
						maxdt = snapshot_date - past_window_size;
					run;

					proc sql;
						select mindt format = worddate12. into: minsdt  from &work_lib..mdt;
						select maxdt format = worddate12. into: maxsdt  from &work_lib..mdt;
					quit;

				%end;

			proc sql;
				select max(occupancy_dt) format=worddate12. into: ctMinDt from  &work_lib..&ds. where record_type_id = &rt.;
				select min(occupancy_dt) format=worddate12. into: ctMaxDt from  &work_lib..&ds. where record_type_id = &rt.;
			quit;

			%if %symexist(sdt) and %symexist(ctMinDt) and %symexist(ctMaxDt) %then
				%do;
					%if &minsdt. ne &ctMinDt. %then
						%do;
							%ideas_util_inc_errors;
							%ideas_util_report_errors(437, "%str(Latest &type. occupancy date: &ctMinDt. is not equal extract snapshot date - 1 day : &minsdt)", "&property_desc.", &request_Id., &err_ct.);
							%let etl_status = 1;
						%end;

					%if &maxsdt. ne &ctMaxDt. %then
						%do;
							%ideas_util_inc_errors;
							%ideas_util_report_errors(437, "%str(Earliest &type. occupancy date: &ctMaxDt. is not equal extract snapshot date - past window size: &maxsdt)", "&property_desc.", &request_Id., &err_ct.);
							%let etl_status = 1;
						%end;
				%end;
			%else
				%do;
					%ideas_util_inc_errors;
					%ideas_util_report_errors(436, 'Missing required variables to complete occupancy date checks',  "&property_desc.", &request_Id., &err_ct.);
					%let etl_status = 1;
				%end;
		%end;

	%let macroname=&SYSMACRONAME;

	%ideas_macro_log (start_end=END, macroname=&macroname.);
%mend occ_snap_dt_check_past;

%macro occ_snap_dt_check_bound (ds, type, rt)/store;
	%let macroname=&SYSMACRONAME;

	%ideas_macro_log (start_end=START, macroname=&macroname.);

	%if %ideas_util_nobs(&work_lib..&ds.) > 0 %then
		%do;
			%if %ideas_util_nobs(&work_lib..file_metadata) > 0 %then
				%do;

					proc sql;
						select snapshot_date format = worddate12. into: sdt  from &Work_lib..file_metadata;
					quit;

					data &work_lib..mdt;
						set &work_lib..file_metadata;
						maxdt = snapshot_date +future_window_size -1;
					run;

					proc sql;
						select maxdt format = worddate12. into: msdt  from &work_lib..mdt;
					quit;

				%end;

			proc sql;
				select min(occupancy_dt) format=worddate12. into: ctMinDt from  &work_lib..&ds. where record_type_id = &rt.;
				select max(occupancy_dt) format=worddate12. into: ctMaxDt from  &work_lib..&ds. where record_type_id = &rt.;
			quit;

			%if %symexist(sdt) and %symexist(ctMinDt) and %symexist(ctMaxDt) %then
				%do;
					%if &ctMinDt. < &sdt. %then
						%do;
							%ideas_util_inc_errors;
							%ideas_util_report_errors(437, "%str(Earliest &type. occupancy date: &ctMinDt. is less than snapshot date: &sdt)", "&property_desc.", &request_Id., &err_ct.);
							%let etl_status = 1;
						%end;

					%if &ctMaxDt. > &msdt. %then
						%do;
							%ideas_util_inc_errors;
							%ideas_util_report_errors(437, "%str(Latest &type. occupancy date: &ctMaxDt. is greater than snapshot date + future window size: &msdt)", "&property_desc.", &request_Id., &err_ct.);
							%let etl_status = 1;
						%end;
				%end;
			%else
				%do;
					%ideas_util_inc_errors;
					%ideas_util_report_errors(436, 'Missing required variables to complete occupancy date checks',  "&property_desc.", &request_Id., &err_ct.);
					%let etl_status = 1;
				%end;
		%end;

	%let macroname=&SYSMACRONAME;

	%ideas_macro_log (start_end=END, macroname=&macroname.);
%mend occ_snap_dt_check_bound;