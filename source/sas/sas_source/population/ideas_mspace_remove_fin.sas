%macro ideas_mspace_remove_fin/store;
	
%let macroname=&SYSMACRONAME; 
	
%ideas_macro_log (start_end=START, macroname=&macroname.);

	proc sql noprint;
		select distinct mkt_seg_id into: list SEPARATED by '|' from rm_data.delta_mkt_accom_inventory where change_flag = 1;
	quit;

	%if (%symexist(list)) %then %do;
		%let ms_list = &list.;
		%let i = 1;

		%do %while (%scan(&ms_list., &i., |) ne);

			data _null_;
				call symputx ('ms',%scan(&ms_list, &i, |),'l');
			run;
           	%let dsn = Ma_&ms.;
			%let libdsn = rm_part.ma_&ms.;
            /*
                When both _ms and _ms_fin table exist, the _fin table replaces the _ms tables.
                When stayed data population is discontinued, _fin table would not exist, but we wouldn't want to
                delete the old data
            */
			%if %sysfunc(exist(&libdsn)) eq 1 and %sysfunc(exist(&libdsn._fin)) eq 1 %then %do;

				proc datasets library=rm_part nolist;
					delete &dsn.;
					change &dsn._fin = &dsn.;
                run; 

			%end;

			%let i = %sysevalf(&i. + 1);
		%end;
	%end;

%let macroname=&SYSMACRONAME; 
	
%ideas_macro_log (start_end=END, macroname=&macroname.);
%mend ideas_mspace_remove_fin;
