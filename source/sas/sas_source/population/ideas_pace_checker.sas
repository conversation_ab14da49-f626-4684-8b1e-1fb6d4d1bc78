%macro ideas_pace_checker(lib=in_xml)/store mindelimiter=',';

    options minoperator;
    %Global property_id property_desc property_desc_short status_desc app_start_time app_end_time runtime;
    %Global minimum_snapshot_dt maximum_snapshot_dt validation_desc;
    %Global rm_data rp_part work_lib work_dir return_code;
    %global sqlrc status etl_file_status error_codes error_bit etl_status err_str loadtype err_ct;
    %let error_codes = 0;
    %let return_code=0;
    %let etl_status = 0;
    %let err_str = .;
    %let etl_file_status = 0;
    %let app_start_time=%sysfunc(datetime());
    %let runTime=0;
    %let syscc = 0;
    %PUT ---------------------------------------------------------------------;
    %PUT ------------------ Starting Pace History Checker --------------------;
    %PUT ---------------------------------------------------------------------;
    %put;

    data _null_;
        set &lib..requestHeader;
        call symputx('Operation',Operation_Name,'l');
        call symputx('property_Id',propertyId,'l');
        call symputx('requestId',requestId,'l');
        call symputx('tenant_server',tenant_server,'l');
        call symputx('server_instance',tenant_server_instance,'l');
        call symputx('tenant_pwd',tenant_password,'l');
        call symputx('tenant_user',tenant_user,'l');
        call symputx('tenant_db',tenant_database,'l');
        call symputx('port',tenant_db_port,'l');
        call symputx('saspath',dataset_path,'l');
        stop;
    run;

    data _null_;
        set &lib..PaceCheckerRequest;
        call symputx('MarketSegmentValidationOnly',MarketSegmentValidationOnly,'l');
        stop;
    run;

    %if &syscc > 4 %then
        %do;
            %ideas_util_report_errors(910, 'Failed to read request header', '', &request_Id., 1);
            %let etl_status = 1;
            %goto EXIT;
        %end;

    %let request_id = %str(&requestid.);
    %let sas_path = %str(&saspath.);

    %if &work_library.=work %then
        %do;
            %let work_lib=work;
            %let work_path=%sysfunc(pathname(work));
        %end;
    %else
        %do;
            %let work_lib=&work_library.;
            %let work_path_drive=%substr (%sysfunc(pathname(work)),1,2);
            %let work_path_root=&work_path_drive./sas;

            %ideas_util_create_dir(dir=temp,base_path=&work_path_root.);
            %let work_path_root=&work_path_drive./sas/temp;

            %ideas_util_create_dir(dir=&property_id.,base_path=&work_path_root.);
            %let work_path_root=&work_path_drive./sas/temp/&property_id.;

            %ideas_util_create_dir(dir=work,base_path=&work_path_root.);
            %let work_path=&work_path_drive./sas/temp/&property_id./work;
            libname &work_lib "&work_path.";

            proc Datasets library=&work_lib. kill memtype = data noprint nolist;
            run;

        %end;

    /*Create errors table to store error logging*/
    data &work_lib..errors;
        length err_ct error_cd 8 request_id error_params error_message $ 200;
        stop;
    run;

    %let mylog_path = %str(&saspath.);
    %let y=%sysfunc(compress(&request_id.,' -'));
    %let load_id = %sysfunc(substr(&y.,1,5));

    %ideas_connect_tenant (&tenant_db., &tenant_server., &port., &server_instance., &tenant_user., &tenant_pwd.);

    %if &etl_status = 1 %then
        %goto EXIT;

    %let rm_data_perm_path=&sas_path.;
    %let rm_part_perm_path=&sas_path.\partitions;

    %ideas_util_create_dir(dir=pacehistorydata,base_path=&rm_data_perm_path.);
    %ideas_util_create_dir(dir=pacehistorypart,base_path=&rm_part_perm_path.);
    %ideas_util_create_dir(dir=group,base_path=&rm_part_perm_path.\pacehistorypart);
    libname group "&rm_part_perm_path.\pacehistorypart\group";
    libname rm_data "&sas_path.";
    libname rm_part "&sas_path.\partitions";

    proc Sql noprint;
        select property_id into : pid from tenant.property where property_id = &property_id. and status_id=1;
        select property_code into: pcode from tenant.property where property_id = &property_id. and status_id=1;
        select property_name into :property_name from tenant.property where property_Id=&property_id. and status_id=1;
    quit;

    %if not %symexist(pid) %then
        %do;
            %str(&requestid.);
            %let property_desc=%str(Property: %trim(&property_id.) does not exist in the %trim(&tenant_db.) database);
            %let property_desc_short=%str(Property: %trim(&property_id.) does not exist in the %trim(&tenant_db.) database);

            %ideas_util_inc_errors;
            %ideas_util_report_errors(913, "&property_desc.", '', &request_Id., &err_ct.);
            %let etl_status = 1;
            %goto EXIT;
        %end;
    %else
        %do;
            %let property_desc=%str(Property: %trim(&property_id.) - %trim(&pcode.));
            %let property_desc_short=%str(Property %trim(&property_id.) - %trim(&pcode.));
        %end;

    proc Sql noprint;
        select record_type_id into: TransRecType from tenant.record_type where upcase(record_type_name) = 'INDTRANS' and status_id=1;
        select record_type_id into: SummaryRecType from tenant.record_type where upcase(record_type_name) = 'T2SNAP' and status_id=1;
    quit;

    Proc Sql noprint;
        select count(*) into: market_segment_only_count  
            from tenant.file_metadata
                where property_id = &property_id and record_type_id = &SummaryRecType. and
                    File_Name eq "PaceHistoryBuild" and File_Location = "TypeTwoMarketSegmentPaceOnly";
    quit;

    proc sql;
        create table &work_lib..group_market_segments as 
            select a.mkt_seg_id 
                from tenant.Mkt_Seg_Details_Proposed as a 
                    where a.business_Type_ID=1 or a.booking_block_pc=1 
                        union 
                    select b.mkt_seg_id
                        from tenant.Mkt_Seg_Details as b 
                            where b.business_Type_ID=1 or b.booking_block_pc=1;
    quit;

    %if %sysfunc(exist(&work_lib..group_market_segments)) and %ideas_util_nobs(&work_lib..group_market_segments) > 0 %then
        %do;

            proc Sql noprint;
                select distinct mkt_seg_id into: groupmktsegcommalist SEPARATED by ',' from &work_lib..group_market_segments
                    order by mkt_seg_id;
                select distinct mkt_seg_id into: groupmktseglist SEPARATED by '|' from &work_lib..group_market_segments
                    order by mkt_seg_id;
            quit;

            %let mktsegi = 1;
            %let groupmarketdatasetspresent=YES;

            %do %while (%scan(&groupmktseglist., &mktsegi., |) ne);

                data _null_;
                    call symputx ('mkt_seg',%scan(&groupmktseglist., &mktsegi., | ),'l');
                run;

                %if &mkt_seg. in &groupmktsegcommalist. and %SYSFUNC(EXIST(group.ma_&mkt_seg.)) = 0 %THEN
                    %Do;
                        %let groupmarketdatasetspresent=NO;
                    %end;

                %let mktsegi = %sysevalf(&mktsegi. + 1);
            %end;
        %end;
    %else %let groupmarketdatasetspresent=NO;

    %if %upcase(&groupmarketdatasetspresent.)=NO or &market_segment_only_count.>0 %then
        %let marketSegmentValidationOnly=TRUE;

    %if %upcase(&marketSegmentValidationOnly.)=TRUE %then
        %let validation_desc=Market Segment Pace Validated;
    %else %let validation_desc=All Pace Validated;
    %ideas_pace_checker_extract(rc=&return_code.);

    %if &return_code > 0 %then
        %goto EXIT;

    %ideas_pace_checker_validate (rc=&return_code.);

    %if &return_code > 0 %then
        %goto EXIT;

    %ideas_pace_checker_report (rc=&return_code.);

    %if &return_code > 0 %then
        %goto EXIT;

%EXIT:

    %if "&GL_ABORT_FLAG" eq "1" or &syscc > 4 %then
        %do;
            %let Status_desc=FAILED;
            %let GL_ABORT_FLAG =1;
        %end;
    %else %let Status_desc=SUCCESSFUL;

    data _null_;
        FILE resXml;
        put '<?xml version="1.0" encoding="UTF-8"?>';
        put '<SASResponse xmlns="http://xml.common.pacman.tetris.ideas.com/schema/PaceCheckerrequest/response/v1" ';
        put 'xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" ';
        put 'xsi:schemaLocation="http://xml.common.pacman.tetris.ideas.com/schema/PaceCheckerrequest/response/v1/pace_checker_build_response_data.xsd ">';
        put "<ResponseHeader> <PropertyId>property_Id</PropertyId>" @;
        put "<operationName>PaceChecker</operationName>" @;
        put "<requestId>&requestId</requestId>";
        stop;
    run;

    data _NULL_;
        FILE resXml MOD;
        put "</ResponseHeader>";
        put "<PaceCheckerResponse>";
        put "<MarketSegmentValidation>&MarketSegmentValidationOnly.</MarketSegmentValidation>";
        put "<operationType>paceChecker</operationType>";
        put "</PaceCheckerResponse>";
        put "</SASResponse>";
    run;

    %let app_end_time=%sysfunc(datetime());
    %let runTime=%sysfunc(round(&app_end_time-&app_start_time, .05));
    Options nomacrogen NoSymbolgen nomlogic nomprint nomfile;
    %PUT ---------------------------------------------------------------------;
    %put --------------------- Pace History Checker Summary --------------------;
    %put ---------------------------------------------------------------------;
    %put;
    %Put -                   Property Info: &property_desc.;
    %put -             Pace Checker Status: &status_desc.;
    %Put -           Pace Checker Run Time: &runTime. seconds;
    %put -          Pace Validation Option: &validation_desc.;
    %put -  Group Mkt Seg Datasets Present: &groupmarketdatasetspresent.;
    %put;
    %put -                       Operation: &Operation.;
    %put -                      Request ID: &requestId.;
    %put;
    %put -                   Sas Data Path: &saspath.;
    %put -              Sas Partition Path: &rm_part_perm_path.;
    %put -                       Pace Path: &rm_data_perm_path.;
    %put;
    %put -                 Database Server: &tenant_server.;
    %put -                 Server Instance: &server_instance.;
    %put -                   Database Name: &tenant_db.;
    %put -                   Database User: &tenant_user.;
    %put -                    Database Pwd: &tenant_pwd.;
    %put -                   Database Port: &port.;
    %put;
    %put -               Work Library Path: &work_path.;
    %put -               Work Library Name: &work_lib;
    %PUT ---------------------------------------------------------------------;
    %PUT ------------------- Ending Pace History Checker ---------------------;
    %PUT ---------------------------------------------------------------------;
    %PUT;

    %if &work_lib. ne work %then
        %do;

            proc Datasets library = &work_lib. kill memtype=data nolist noprint;
            run;

            quit;

        %end;
%mend ideas_pace_checker;